package com.mega.eloan.lms.fms.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.pages.LMS8000M01Page;
import com.mega.eloan.lms.fms.report.LMS8000R01RptService;
import com.mega.eloan.lms.fms.service.LMS8000Service;
import com.mega.eloan.lms.model.L260M01A;
import com.mega.eloan.lms.model.L260M01B;
import com.mega.eloan.lms.model.L260M01C;
import com.mega.eloan.lms.model.L260M01D;
import com.mega.eloan.lms.model.L260S01A;
import com.mega.eloan.lms.model.L260S01D;
import com.mega.eloan.lms.model.L260S01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.jcs.common.report.SubReportParam;

@Service("lms8000r01rptservice")
public class LMS8000R01RptServiceImpl implements LMS8000R01RptService,
		FileDownloadService {
	//
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS8000R01RptServiceImpl.class);

	@Resource
	BranchService branch;

	@Resource
	LMSService lmsService;

	@Resource
	LMS8000Service lms8000Service;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	DocFileService docFileService;

	@Resource
	UserInfoService userInfoService;

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

	public OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		LOGGER.info("into setReportData");
		OutputStream outputStream = null;
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS8000M01Page.class);
		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		String type = Util.trim(params.getString("type"));

		if (Util.equals(type, "R01")) {
			outputStream = this.genLMS8000R01(params, prop, locale);
		} else if (Util.equals(type, "R02")) {
			outputStream = this.genLMS8000R02(params, prop, locale);
		} else if (Util.equals(type, "R03")) {
			outputStream = this.genLMS8000R03(params, prop, locale);
		} else if (Util.equals(type, "R04")) {
			outputStream = this.genLMS8000R04(params, prop, locale);
		} else if (Util.equals(type, "R05")) {
			outputStream = this.genLMS8000R05(params, prop, locale);
		} else if (Util.equals(type, "R06")) {
			// J-112-0307
			// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。 
			outputStream = this.genLMS8000R06(params, prop, locale);
		} else {

		}

		return outputStream;
	}

	@SuppressWarnings("unchecked")
	public OutputStream genLMS8000R01(PageParameters params, Properties prop,
			Locale locale) throws FileNotFoundException, IOException, Exception {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branch.getBranch(
				brNo).getBrNoFlag());

		ReportGenerator generator = new ReportGenerator(
				"report/fms/LMS8000R01_" + locale.toString() + ".rpt");
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		OutputStream outputStream = null;

		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L260M01A l260m01a = null;
		List<L260M01B> l260m01blist = null;

		String branchName = null;
		try {
			l260m01a = lms8000Service.findModelByOid(L260M01A.class, mainOid);
			if (l260m01a == null) {
				l260m01a = new L260M01A();
			}

			String apprid = "";
			String recheckid = "";
			String bossid = "";
			String managerid = "";
			l260m01blist = (List<L260M01B>) lms8000Service.findListByMainId(
					L260M01B.class, mainId);
			if (!Util.isEmpty(l260m01blist)
					&& Util.notEquals(l260m01a.getDocStatus(),
							CreditDocStatusEnum.海外_編製中)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L260M01B l260m01b : l260m01blist) {
					// 要加上人員代碼
					String type = Util.trim(l260m01b.getStaffJob());
					String userId = Util.trim(l260m01b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) { // L1. 分行經辦
						apprid = userId + " " + value;
					} else if ("L3".equals(type)) { // L3. 分行授信主管
						bossId.append(bossId.length() > 0 ? "\r\n" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) { // L4. 分行覆核主管
						recheckid = userId + " " + value;
					} else if ("L5".equals(type)) { // L5. 經副襄理
						managerid = userId + " " + value;
					}
				}
				bossid = bossId.toString();
			}
			String updater = Util.trim(l260m01a.getUpdater()) + " "
					+ Util.trim(lmsService.getUserName(l260m01a.getUpdater()));

			Map<String, String> wordingMap = codetypeservice.findByCodeType(
					"postLoan_wording", locale.toString());
			// StringBuffer wordingSB = new StringBuffer();
			ArrayList<String> wording = new ArrayList<String>();
			for (String key : wordingMap.keySet()) {
				// wordingSB.append(key).append(".　").append(wordingMap.get(key));
				wording.add(key + ".　" + wordingMap.get(key));
			}
			rptVariableMap.put("WORDING", StringUtils.join(wording, "<br/>"));
			// rptVariableMap.put("WORDING", prop.getProperty("WORDING01") +
			// "<br/>" + prop.getProperty("WORDING02") +
			// "<br/>" + prop.getProperty("WORDING03") + "<br/>" +
			// prop.getProperty("WORDING04") +
			// "<br/>" + prop.getProperty("WORDING05"));
			// + "<br/>" + prop.getProperty("WORDING06") +
			// "<br/>" + prop.getProperty("WORDING07"));
			rptVariableMap.put("ISOVERSEA", isOverSea ? "Y" : "N");
			rptVariableMap.put("L260M01A.CUSTID",
					Util.nullToSpace(l260m01a.getCustId()));
			rptVariableMap.put("L260M01A.DUPNO",
					Util.nullToSpace(l260m01a.getDupNo()));
			rptVariableMap.put("L260M01A.CUSTNAME",
					Util.nullToSpace(l260m01a.getCustName()));
			rptVariableMap.put("L260M01A.CNTRNO",
					Util.nullToSpace(l260m01a.getCntrNo()));
			rptVariableMap.put("L260M01A.HAS_LOANNO",
					Util.nullToSpace(l260m01a.getLoanNo()));
			rptVariableMap.put(
					"L260M01A.LOANNO",
					(isOverSea ? prop.getProperty("loanNoOvs") : prop
							.getProperty("loanNo"))
							+ "："
							+ Util.nullToSpace(l260m01a.getLoanNo()));
			rptVariableMap.put("L260M01A.MAINID",
					Util.nullToSpace(l260m01a.getMainId()));
			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(l260m01a.getOwnBrId())));
			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("L260M01A.UPDATER", updater);
			rptVariableMap.put("L260M01B.APPRID", apprid);
			rptVariableMap.put("L260M01B.RECHECKID", recheckid);
			rptVariableMap.put("L260M01B.BOSSID", bossid);
			rptVariableMap.put("L260M01B.MANAGERID", managerid);

			Map<String, CapAjaxFormResult> codeTypes = codetypeservice
					.findByCodeType(new String[] { "postLoan_followKind",
							"postLoan_followWay", "postLoan_handlingStatus",
							"postLoan_staff", "YNX", "postLoan_proType",
							"postLoan_tranType" });

			List<Map<String, String>> m01cRowData = new ArrayList<Map<String, String>>();
			List<L260M01C> l260m01clist = (List<L260M01C>) lms8000Service
					.findListByMainIdNotDel(L260M01C.class, mainId, true);
			List<L260M01C> copyM01cList = new ArrayList<L260M01C>();
			if (!l260m01clist.isEmpty()) {
				copyM01cList.addAll(l260m01clist);

				for (int i = copyM01cList.size() - 1; i >= 0; i--) {
					L260M01C l260m01c = copyM01cList.get(i);
					if (Util.equals(l260m01c.getStatus(), "C")
							|| Util.isEmpty(Util.nullToSpace(l260m01c
									.getCheckYN()))) {
						copyM01cList.remove(l260m01c);
					}
				}
			}
			if (copyM01cList != null && copyM01cList.size() > 0) {
				rptVariableMap.put("L260M01C.HASDATA", "Y");
				for (L260M01C l260m01c : copyM01cList) {
					Map<String, String> m01cMap = new LinkedHashMap<String, String>();
					m01cMap.put("ReportBean.column01", this.getStrByCode(
							Util.trim(l260m01c.getFollowKind()), codeTypes,
							"Kind"));
					m01cMap.put("ReportBean.column02",
							Util.trim(l260m01c.getFollowContent()));
					m01cMap.put("ReportBean.column03", this.getStrByCode(
							Util.trim(l260m01c.getFollowWay()), codeTypes,
							"Way"));
					m01cMap.put("ReportBean.column04", CapDate.formatDate(
							l260m01c.getNextFollowDate(), "yyyy-MM-dd"));
					if (Util.equals(l260m01c.getFollowWay(), "2")) {
						m01cMap.put("ReportBean.column100",
								l260m01c.getFollowWay());
						m01cMap.put("ReportBean.column05",
								Util.trim(l260m01c.getFollowCycle()));
						m01cMap.put("ReportBean.column06", CapDate.formatDate(
								l260m01c.getFollowBgnDate(), "yyyy-MM-dd"));
						m01cMap.put("ReportBean.column07", CapDate.formatDate(
								l260m01c.getFollowEndDate(), "yyyy-MM-dd"));
					} else {
						m01cMap.put("ReportBean.column100",
								l260m01c.getFollowWay());
						m01cMap.put("ReportBean.column05", "");
						m01cMap.put("ReportBean.column06", "");
						m01cMap.put("ReportBean.column07", "");
					}
					m01cMap.put("ReportBean.column08", this.getStrByCode(
							Util.trim(l260m01c.getStaff()), codeTypes, "Staff"));
					m01cMap.put("ReportBean.column09", Util.trim(lmsService
							.getUserName(l260m01c.getFo_staffNo())));
					m01cMap.put("ReportBean.column10", Util.trim(lmsService
							.getUserName(l260m01c.getAo_staffNo())));
					String byId = "";
					if (Util.isEmpty(Util.trim(l260m01c.getCntrNo()))
							&& Util.isEmpty(Util.trim(l260m01c.getLoanNo()))) {
						byId = "Y";
					}
					m01cMap.put("ReportBean.column11",
							this.getStrByCode(byId, codeTypes, "YN"));
					m01cRowData.add(m01cMap);
				}
			} else {
				rptVariableMap.put("L260M01C.HASDATA", "N");
				Map<String, String> rptrowMap = new LinkedHashMap<String, String>();
				rptrowMap.put("ReportBean.column01", "");
				m01cRowData.add(rptrowMap);
			}

			List<Map<String, String>> m01dRowData = new ArrayList<Map<String, String>>();
			List<L260M01D> l260m01dlist = (List<L260M01D>) lms8000Service
					.findListByMainIdNotDel(L260M01D.class, mainId, true);
			List<L260M01D> copyM01dList = new ArrayList<L260M01D>();
			if (!l260m01dlist.isEmpty()) {
				copyM01dList.addAll(l260m01dlist);

				for (int i = l260m01dlist.size() - 1; i >= 0; i--) {
					L260M01D l260m01d = l260m01dlist.get(i);
					if (Util.equals(l260m01d.getHandlingStatus(), "4")
							|| Util.isEmpty(Util.nullToSpace(l260m01d
									.getCheckYN()))) {
						copyM01dList.remove(l260m01d);
					}
				}
			}
			if (copyM01dList != null && copyM01dList.size() > 0) {
				rptVariableMap.put("L260M01D.HASDATA", "Y");
				for (L260M01D l260m01d : copyM01dList) {
					Map<String, String> m01dMap = new LinkedHashMap<String, String>();
					m01dMap.put("ReportBean.column01", this.getStrByCode(
							Util.trim(l260m01d.getFollowKind()), codeTypes,
							"Kind"));
					m01dMap.put("ReportBean.column02",
							Util.trim(l260m01d.getFollowContent()));
					m01dMap.put("ReportBean.column03", CapDate.formatDate(
							l260m01d.getFollowDate(), "yyyy-MM-dd"));
					m01dMap.put("ReportBean.column04", this.getStrByCode(
							Util.trim(l260m01d.getHandlingStatus()), codeTypes,
							"Handle"));
					m01dMap.put("ReportBean.column05", CapDate.formatDate(
							l260m01d.getChkDate(), "yyyy-MM-dd"));
					m01dMap.put("ReportBean.column06",
							this.getStrByCode(
									Util.trim(l260m01d.getConformFg()),
									codeTypes, "YN"));
					m01dMap.put("ReportBean.column07",
							Util.trim(l260m01d.getFollowMemo()));
					m01dMap.put("ReportBean.column08",
							Util.trim(l260m01d.getFileDesc()));
					m01dMap.put("ReportBean.column12", this.getStrByCode(
							Util.trim(l260m01d.getRepayUnusualFg()), codeTypes,
							"YN"));
					m01dMap.put("ReportBean.column100",
							l260m01d.getRepayUnusualFg());
					if (Util.equals(l260m01d.getRepayUnusualFg(), "Y")) {
						m01dMap.put("ReportBean.column09",
								Util.trim(l260m01d.getUnusualDesc()));
						String isNotional = Util.trim(l260m01d.getIsNotional());
						m01dMap.put(
								"ReportBean.column10",
								(Util.isEmpty(isNotional) ? ""
										: (Util.equals("Y", isNotional) ? prop
												.getProperty("L260M01D.isNotional_Y")
												: prop.getProperty("L260M01D.isNotional_N"))));
						String isAML = Util.trim(l260m01d.getIsAML());
						m01dMap.put(
								"ReportBean.column11",
								(Util.isEmpty(isAML) ? "" : (Util.equals("Y",
										isAML) ? prop
										.getProperty("L260M01D.isAML_Y") : prop
										.getProperty("L260M01D.isAML_N"))));
					} else {
						m01dMap.put("ReportBean.column09", "");
						m01dMap.put("ReportBean.column10", "");
						m01dMap.put("ReportBean.column11", "");
					}
					m01dMap.put("ReportBean.column13",
							this.getStrByCode(
									Util.trim(l260m01d.getFinProdFg()),
									codeTypes, "YN"));
					m01dMap.put("ReportBean.column101", l260m01d.getFinProdFg());
					if (Util.equals(l260m01d.getFinProdFg(), "Y")) {
						m01dMap.put("ReportBean.column14", this.getFinProdStr(
								l260m01d, prop, codeTypes, "R01"));
					} else {
						m01dMap.put("ReportBean.column14", "");
					}
					String byId = "";
					if (Util.isEmpty(Util.trim(l260m01d.getCntrNo()))
							&& Util.isEmpty(Util.trim(l260m01d.getLoanNo()))) {
						byId = "Y";
					}
					m01dMap.put("ReportBean.column15",
							this.getStrByCode(byId, codeTypes, "YN"));

					m01dRowData.add(m01dMap);
				}
			} else {
				rptVariableMap.put("L260M01D.HASDATA", "N");
				Map<String, String> rptrowMap = new LinkedHashMap<String, String>();
				rptrowMap.put("ReportBean.column01", "");
				m01dRowData.add(rptrowMap);
			}

			// 子報表設定
			SubReportParam subReportParam = new SubReportParam();
			Map<String, String> map = new HashMap<String, String>();
			subReportParam.setData(0, map, m01cRowData);
			subReportParam.setData(1, map, m01dRowData);
			generator.setSubReportParam(subReportParam);

			generator.setVariableData(rptVariableMap);
			generator.setLang(locale);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}

	public OutputStream genLMS8000R02(PageParameters params, Properties prop,
			Locale locale) throws FileNotFoundException, IOException, Exception {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branch.getBranch(
				brNo).getBrNoFlag());

		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		List<InputStream> list = new LinkedList<InputStream>();
		OutputStream outputStream = null;
		try {
			Properties propEloanPage = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);
			int subLine = 9;
			// content
			String rptOid = Util.nullToSpace(params.getString("rptOid"));
			// datas.cntrNo + "^" + datas.loanNo + "|";
			String[] dataSplit = rptOid.split("\\|");
			for (String temp : dataSplit) {
				outputStream = null;
				String cntrNo = null;
				String loanNo = null;
				String[] tempSplits = temp.split("\\^");
				if (tempSplits.length < 1) {
					cntrNo = "";
				} else {
					cntrNo = tempSplits[0];
				}
				if (tempSplits.length < 2) {
					loanNo = "";
				} else {
					loanNo = tempSplits[1];
				}

				outputStream = this.genLMS8000R02Detail(params, cntrNo, loanNo,
						locale, prop, isOverSea);
				pdfNameMap.put(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()),
						subLine);
			}

			if (pdfNameMap != null && pdfNameMap.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine);
				list.add(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()));
			}

			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);
		} finally {
			if (list != null) {
				list.clear();
			}
			if (pdfNameMap != null) {
				pdfNameMap.clear();
			}
		}
		return outputStream;
	}

	@SuppressWarnings("unchecked")
	public OutputStream genLMS8000R02Detail(PageParameters params,
			String cntrNo, String loanNo, Locale locale, Properties prop,
			boolean isOverSea) throws FileNotFoundException, IOException,
			Exception {
		ReportGenerator generator = new ReportGenerator(
				"report/fms/LMS8000R02_" + locale.toString() + ".rpt");
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		OutputStream outputStream = null;

		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		L260M01A l260m01a = null;
		String branchName = null;
		try {
			l260m01a = lms8000Service.findModelByOid(L260M01A.class, mainOid);
			if (l260m01a == null) {
				l260m01a = new L260M01A();
			}

			rptVariableMap.put("ISOVERSEA", isOverSea ? "Y" : "N");
			rptVariableMap.put("L260M01A.CUSTID",
					Util.nullToSpace(l260m01a.getCustId()));
			rptVariableMap.put("L260M01A.DUPNO",
					Util.nullToSpace(l260m01a.getDupNo()));
			rptVariableMap.put("L260M01A.CUSTNAME",
					Util.nullToSpace(l260m01a.getCustName()));
			rptVariableMap.put("L260M01A.CNTRNO", cntrNo);
			rptVariableMap.put("L260M01A.HAS_LOANNO", loanNo);
			rptVariableMap.put(
					"L260M01A.LOANNO",
					(isOverSea ? prop.getProperty("loanNoOvs") : prop
							.getProperty("loanNo")) + "：" + loanNo);
			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(l260m01a.getOwnBrId())));
			rptVariableMap.put("BRANCHNAME", branchName);

			Map<String, CapAjaxFormResult> codeTypes = codetypeservice
					.findByCodeType(new String[] { "postLoan_followKind",
							"postLoan_statusForShow", "postLoan_proType" });

			rptVariableMap.put("L260M01C.ITEM", "");
			rptVariableMap.put("L260M01C.ITEMDSC", "");
			StringBuffer sb = new StringBuffer();
			Set<String> temp = new HashSet<String>();
			List<Map<String, String>> m01cRowData = new ArrayList<Map<String, String>>();
			// 檢核表的時候 deletedTime 有值 => 不能設定 deletedTime is null
			// incEmptyLoanNo = true => loanNo 如果是空的也要當search條件
			List<L260M01C> l260m01clist = (List<L260M01C>) lms8000Service
					.findByMainIdAndNos(L260M01C.class, mainId, cntrNo, loanNo,
							false, true);
			boolean hasL260M01C = false;
			if (l260m01clist != null && l260m01clist.size() > 0) {
				for (L260M01C l260m01c : l260m01clist) {
					hasL260M01C = true;
					Map<String, String> m01cMap = new LinkedHashMap<String, String>();
					m01cMap.put("ReportBean.column01", Util.trim(l260m01c
							.getFollowKind().replace("|", "、")));
					m01cMap.put("ReportBean.column02",
							l260m01c.getFollowContent());
					if (Util.equals(l260m01c.getFollowWay(), "2")) {
						m01cMap.put("ReportBean.column03",
								Util.trim(l260m01c.getFollowCycle()));
					} else {
						m01cMap.put("ReportBean.column03", "");
					}
					m01cMap.put("ReportBean.column04", CapDate.formatDate(
							l260m01c.getNextFollowDate(), "yyyy-MM-dd"));
					m01cMap.put(
							"ReportBean.column05",
							Util.nullToSpace(codeTypes.get(
									"postLoan_statusForShow").get(
									l260m01c.getStatusForShow())));
					m01cRowData.add(m01cMap);

					String[] kind = StringUtils.split(
							Util.trim(l260m01c.getFollowKind()), "|");
					for (String s : kind) {
						if (!temp.contains(s)) {
							temp.add(s);
							sb.append(sb.length() > 0 ? "|" : "");
							sb.append(s);
						}
					}
				}
				rptVariableMap.put("L260M01C.HASDATA",
						(hasL260M01C ? "Y" : "N"));
				rptVariableMap.put("L260M01C.ITEM",
						this.getStrByCode(sb.toString(), codeTypes, "Kind"));
			} else {
				rptVariableMap.put("L260M01C.HASDATA", "N");
				Map<String, String> rptrowMap = new LinkedHashMap<String, String>();
				rptrowMap.put("ReportBean.column01", "");
				m01cRowData.add(rptrowMap);
			}

			List<CodeType> typeList = codetypeservice
					.findByCodeTypeList("postLoan_followKind");
			StringBuffer sbDsc = new StringBuffer();
			for (CodeType type : typeList) {
				sbDsc.append(sbDsc.length() > 0 ? "、" : "");
				sbDsc.append(type.getCodeValue());
				sbDsc.append("：");
				sbDsc.append(type.getCodeDesc());
			}
			rptVariableMap.put("L260M01C.ITEMDSC", "類別說明：" + sbDsc.toString());

			List<Map<String, String>> listData = new ArrayList<Map<String, String>>();
			List<Map<String, String>> amlListData = new ArrayList<Map<String, String>>();
			Boolean hasAML = false;
			boolean hasL260M01D = false;
			List<L260M01D> l260m01dlist = (List<L260M01D>) lms8000Service
					.findByMainIdAndNos(L260M01D.class, mainId, cntrNo, loanNo,
							false, true);
			if (l260m01dlist != null && l260m01dlist.size() > 0) {
				for (L260M01D l260m01d : l260m01dlist) {
					hasL260M01D = true;
					Map<String, String> map = new LinkedHashMap<String, String>();
					map.put("ReportBean.column01", CapDate.formatDate(
							l260m01d.getChkDate(), "yyyy-MM-dd"));
					map.put("ReportBean.column02", this.getStrByCode(
							Util.trim(l260m01d.getFollowKind()), codeTypes,
							"Kind"));
					String finProdStr = this.getFinProdStr(l260m01d, prop,
							codeTypes, "R02");
					map.put("ReportBean.column03",
							Util.nullToSpace(l260m01d.getFollowMemo())
									+ (Util.isEmpty(finProdStr) ? ""
											: ("\r\n撥款資金購買理財商品：（"
													+ prop.getProperty("L260S01A.proType")
													+ "、"
													+ prop.getProperty("L260S01A.bankPro")
													+ "、"
													+ prop.getProperty("L260S01A.accNo")
													+ "）" + "\r\n" + finProdStr)));
					map.put("ReportBean.column04", l260m01d.getFileDesc());
					if (Util.equals(l260m01d.getConformFg(), "Y")) {
						map.put("ReportBean.column05", "V");
						map.put("ReportBean.column06", "");
					} else if (Util.equals(l260m01d.getConformFg(), "N")) {
						map.put("ReportBean.column05", "");
						map.put("ReportBean.column06", "V");
					} else {
						map.put("ReportBean.column05", "");
						map.put("ReportBean.column06", "");
					}
					if (Util.equals(l260m01d.getRepayUnusualFg(), "Y")) {
						hasAML = true;
						this.addAmlData(amlListData, l260m01d);
					}
					map.put("ReportBean.column07", Util.trim(lmsService
							.getUserName(Util.trim(l260m01d.getCreator()))));
					map.put("ReportBean.column08", Util.trim(lmsService
							.getUserName(Util.trim(l260m01d.getUpdater()))));
					listData.add(map);
				}
				rptVariableMap.put("L260M01D.HASDATA",
						(hasL260M01D ? "Y" : "N"));
				rptVariableMap.put("WORDING",
						prop.getProperty("L260M01Drpt.WORDING01") + "<br/>"
								+ prop.getProperty("L260M01Drpt.WORDING02"));
			} else {
				rptVariableMap.put("L260M01D.HASDATA", "N");
				Map<String, String> rptrowMap = new LinkedHashMap<String, String>();
				rptrowMap.put("ReportBean.column01", "");
				listData.add(rptrowMap);
			}
			rptVariableMap.put("hasAML", hasAML ? "Y" : "N");

			// 子報表設定
			SubReportParam subReportParam = new SubReportParam();
			Map<String, String> map = new HashMap<String, String>();
			subReportParam.setData(0, map, m01cRowData);
			subReportParam.setData(1, map, listData);
			subReportParam.setData(2, map, amlListData);
			generator.setSubReportParam(subReportParam);

			generator.setVariableData(rptVariableMap);
			generator.setLang(locale);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}

	private String getStrByCode(String input,
			Map<String, CapAjaxFormResult> codeTypes, String type) {
		StringBuffer output = new StringBuffer("");
		if (Util.equals("Kind", type)) {
			String kindArr[] = StringUtils.split(input, "|");
			for (int i = 0; i < kindArr.length; i++) {
				String k = kindArr[i];
				output.append(output.length() > 0 ? "、" : "");
				output.append(Util.nullToSpace(codeTypes.get(
						"postLoan_followKind").get(k)));
			}
		} else if (Util.equals("Way", type)) {
			output.append(Util.nullToSpace(codeTypes.get("postLoan_followWay")
					.get(input)));
		} else if (Util.equals("Handle", type)) {
			output.append(Util.nullToSpace(codeTypes.get(
					"postLoan_handlingStatus").get(input)));
		} else if (Util.equals("YN", type)) {
			output.append(Util.nullToSpace(codeTypes.get("YNX").get(input)));
		} else if (Util.equals("Staff", type)) {
			output.append(Util.nullToSpace(codeTypes.get("postLoan_staff").get(
					input)));
		}
		return output.toString();
	}

	public void addAmlData(List<Map<String, String>> list, L260M01D l260m01d) {
		Map<String, String> map = new LinkedHashMap<String, String>();
		map.put("ReportBean.column01",
				CapDate.formatDate(l260m01d.getChkDate(), "yyyy-MM-dd"));
		map.put("ReportBean.column02", l260m01d.getFollowContent());
		map.put("ReportBean.column03", l260m01d.getUnusualDesc());
		if (Util.equals(l260m01d.getIsNotional(), "Y")) {
			map.put("ReportBean.column04", "V");
			map.put("ReportBean.column05", "");
		} else if (Util.equals(l260m01d.getIsNotional(), "N")) {
			map.put("ReportBean.column04", "");
			map.put("ReportBean.column05", "V");
		} else {
			map.put("ReportBean.column04", "");
			map.put("ReportBean.column05", "");
		}
		if (Util.equals(l260m01d.getIsAML(), "Y")) {
			map.put("ReportBean.column06", "V");
			map.put("ReportBean.column07", "");
		} else if (Util.equals(l260m01d.getIsAML(), "N")) {
			map.put("ReportBean.column06", "");
			map.put("ReportBean.column07", "V");
		} else {
			map.put("ReportBean.column06", "");
			map.put("ReportBean.column07", "");
		}
		map.put("ReportBean.column08", Util.trim(lmsService.getUserName(Util
				.trim(l260m01d.getCreator()))));
		map.put("ReportBean.column09", Util.trim(lmsService.getUserName(Util
				.trim(l260m01d.getUpdater()))));
		list.add(map);
	}

	@SuppressWarnings("unchecked")
	public String getFinProdStr(L260M01D l260m01d, Properties prop,
			Map<String, CapAjaxFormResult> codeTypes, String type) {
		StringBuilder sb = new StringBuilder("");
		if (l260m01d != null) {
			List<L260S01A> l260s01aList = (List<L260S01A>) lms8000Service
					.findListByMainIdNotDel(L260S01A.class, l260m01d.getOid(),
							true);
			for (L260S01A l260s01a : l260s01aList) {
				if (Util.equals(type, "R01")) {
					sb.append("<table border='1' style='width: 100%'><tbody>");
					sb.append("<tr><td colspan='2' align='right'>");
					sb.append(prop.getProperty("L260S01A.dataDt") + "：");
					sb.append(Util.nullToSpace(TWNDate.toAD(l260s01a
							.getDataDt())));
					// CapDate.formatDate(l260s01a.getDataDt(),
					// UtilConstants.DateFormat.YYYY_MM_DD));
					sb.append("</td></tr><tr><td style='width: 50%'>");
					sb.append(prop.getProperty("L260S01A.cust") + "：");
					sb.append(Util.nullToSpace(l260s01a.getCustId()) + "　"
							+ Util.nullToSpace(l260s01a.getDupNo()) + "　"
							+ Util.nullToSpace(l260s01a.getCustName()));
					sb.append("</td><td>");
					sb.append(prop.getProperty("L260S01A.proType") + "：");
					sb.append(Util.nullToSpace(codeTypes
							.get("postLoan_proType").get(l260s01a.getProType())));
					sb.append("</td></tr><tr><td>");
					sb.append(prop.getProperty("L260S01A.tranType") + "：");
					sb.append(Util.nullToSpace(codeTypes.get(
							"postLoan_tranType").get(l260s01a.getTranType())));
					sb.append("</td><td>");
					sb.append(prop.getProperty("L260S01A.accNo") + "：");
					sb.append(Util.nullToSpace(l260s01a.getAccNo()));
					sb.append("</td></tr><tr><td colspan='2'>");
					sb.append(prop.getProperty("L260S01A.bankPro") + "：");
					sb.append(Util.nullToSpace(l260s01a.getBankProCode()) + "　"
							+ Util.nullToSpace(l260s01a.getBankProName()));
					sb.append("</td></tr><tr><td colspan='2'>");
					sb.append(prop.getProperty("L260S01A.lstBuy") + "：");
					sb.append(Util.nullToSpace(l260s01a.getLstBuyBrCd())
							+ branch.getBranchName(l260s01a.getLstBuyBrCd())
							+ "　"
							+ (CrsUtil.isNull_or_ZeroDate(l260s01a
									.getLstBuyDt()) ? "" : Util
									.nullToSpace(TWNDate.toAD(l260s01a
											.getLstBuyDt()))) + "　"
							+ Util.nullToSpace(l260s01a.getLstBuyCurCd()) + " "
							+ NumConverter.addComma(l260s01a.getLstBuyAmt()));
					sb.append("</td></tr>");
					// 隱藏欄位 需與js同步 hideSell hideInvAmt
					if (!CrsUtil.isNull_or_ZeroDate(l260s01a.getLstSellDt())) {
						sb.append("<tr><td colspan='2'>");
						sb.append(prop.getProperty("L260S01A.lstSell") + "：");
						sb.append(Util.nullToSpace(l260s01a.getLstSellBrCd())
								+ branch.getBranchName(l260s01a
										.getLstSellBrCd())
								+ "　"
								+ (CrsUtil.isNull_or_ZeroDate(l260s01a
										.getLstSellDt()) ? "" : Util
										.nullToSpace(TWNDate.toAD(l260s01a
												.getLstSellDt())))
								+ "　"
								+ Util.nullToSpace(l260s01a.getLstSellCurCd())
								+ " "
								+ NumConverter.addComma(l260s01a
										.getLstSellAmt()));
						sb.append("</td></tr>");
					}
					if (Util.equals(Util.nullToSpace(l260s01a.getProType()),
							"FUND")
							|| Util.equals(
									Util.nullToSpace(l260s01a.getProType()),
									"BD")
							|| Util.equals(
									Util.nullToSpace(l260s01a.getProType()),
									"ETF")) {
						sb.append("<tr><td colspan='2'>");
						sb.append(prop.getProperty("L260S01A.invAmt") + "：");
						sb.append(NumConverter.addComma(l260s01a.getInvAmt()));
						sb.append("</td></tr>");
					}
					sb.append("</tbody></table>");
				} else if (Util.equals(type, "R02")) {
					sb.append(sb.length() > 0 ? "\r\n" : "");
					sb.append(
							Util.nullToSpace(codeTypes.get("postLoan_proType")
									.get(l260s01a.getProType())))
							.append("、")
							.append(Util.nullToSpace(l260s01a.getBankProName()))
							.append("、")
							.append(Util.nullToSpace(l260s01a.getAccNo()));
				}
			}
		}
		return sb.toString();
	}

	public OutputStream genLMS8000R03(PageParameters params, Properties prop,
			Locale locale) throws Exception {
		List<InputStream> list = new LinkedList<InputStream>();
		OutputStream outputStream = null;

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		try {
			String rowObj = Util.trim(params.getString("rowObj"));
			String[] dataSplit = rowObj.split("\\^");
			if (dataSplit != null && dataSplit.length > 0) {

				for (String temp : dataSplit) {
					outputStream = this.genLMS8000R03Detail(params, prop,
							locale, temp);
					list.add(new ByteArrayInputStream(
							((ByteArrayOutputStream) outputStream)
									.toByteArray()));
				}

				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(list, outputStream);

				// 產生附件
				String actType = Util.trim(params.getString("actType")); // 1:
																			// 僅列印
																			// 2:
																			// 列印+附件
				if (Util.equals(actType, "2")) {
					String mainId = Util.trim(params
							.getString(EloanConstants.MAIN_ID)); // L260M01D 的
																	// oid
					String bgnDate = Util.trim(params.getString("bgnDate"));
					String endDate = Util.trim(params.getString("endDate"));
					String bgnDateStr = sdf.format(CapDate.getDate(bgnDate,
							"yyyy-MM-dd"));
					String endDateStr = sdf.format(CapDate.getDate(endDate,
							"yyyy-MM-dd"));
					String dateStr = (Util.equals(bgnDateStr, endDateStr) ? bgnDateStr
							: (bgnDateStr + "_" + endDateStr));

					String[] rowObjArray = dataSplit[0].split(",");
					String fakeActNo = null;
					String curr = null;
					String currCode = null;
					String realActNo = null;
					fakeActNo = ((rowObjArray.length < 1) ? "" : rowObjArray[0]);
					curr = ((rowObjArray.length < 2) ? "" : rowObjArray[1]);
					currCode = ((rowObjArray.length < 3) ? "" : rowObjArray[2]);
					realActNo = ((rowObjArray.length < 4) ? "" : rowObjArray[3]);

					Boolean muti = params.getAsBoolean("muti", false);
					String fileTitle = "0060_";
					String fileSubTitle = "";
					if (muti) {
						fileSubTitle = dateStr;
					} else {
						fileSubTitle = fakeActNo + "_" + curr + "_" + dateStr;
					}
					ByteArrayOutputStream baos = null;
					baos = (ByteArrayOutputStream) outputStream;

					DocFile file = new DocFile();
					// J-111-0025_05097_B1001
					// 為增進eloan擔保品及貸後管理查詢時價登入作業效率,增加相關作業需求
					// file.setMainId(mainId); // L260M01D 的 oid
					L260M01D l260m01d = lms8000Service.findModelByOid(
							L260M01D.class, mainId);

					file.setMainId(l260m01d.getMainId()); // L260M01D 的 mainId =
															// L260M01A 的 MAINID
					file.setPid(mainId); // L260M01D 的 oid
					file.setData(baos != null ? baos.toByteArray() : null);
					file.setFileDesc(fileTitle + fileSubTitle);
					// J-111-0025_05097_B1001
					// 為增進eloan擔保品及貸後管理查詢時價登入作業效率,增加相關作業需求
					file.setCrYear(CapDate.getCurrentDate("yyyy-MM"));
					file.setFieldId("postLoanCertified");
					file.setSrcFileName(fileSubTitle + ".pdf");
					file.setUploadTime(CapDate.getCurrentTimestamp());
					file.setBranchId(user.getUnitNo());
					file.setContentType("application/pdf");
					file.setSysId("LMS");
					docFileService.save(file);
				}
			}
		} finally {
			if (list != null) {
				list.clear();
			}
		}

		return outputStream;
	}

	@SuppressWarnings("unchecked")
	public OutputStream genLMS8000R03Detail(PageParameters params,
			Properties prop, Locale locale, String rowObj) throws Exception {
		ReportGenerator generator = new ReportGenerator(
				"report/fms/LMS8000R03_" + locale.toString() + ".rpt");
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		OutputStream outputStream = null;

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		try {
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID)); // L260M01D
																					// 的
																					// oid
			String bgnDate = Util.trim(params.getString("bgnDate"));
			String endDate = Util.trim(params.getString("endDate"));

			String[] rowObjArray = rowObj.split(",");
			String fakeActNo = null;
			String curr = null;
			String currCode = null;
			String realActNo = null;
			String q0060Flag = null;
			fakeActNo = ((rowObjArray.length < 1) ? "" : rowObjArray[0]);
			curr = ((rowObjArray.length < 2) ? "" : rowObjArray[1]);
			currCode = ((rowObjArray.length < 3) ? "" : rowObjArray[2]);
			realActNo = ((rowObjArray.length < 4) ? "" : rowObjArray[3]);
			q0060Flag = ((rowObjArray.length < 5) ? "" : rowObjArray[4]);

			String branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(user.getUnitNo())));
			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("qUserId", Util.nullToSpace(user.getUserId()));
			rptVariableMap.put("fakeActNo", Util.nullToSpace(fakeActNo));
			rptVariableMap.put("CURR", Util.nullToSpace(curr));
			rptVariableMap.put("CUST", "");
			L260M01D l260m01d = lms8000Service.findModelByOid(L260M01D.class,
					mainId);
			if (l260m01d != null) {
				L260M01A l260m01a = lms8000Service.findModelByMainId(
						L260M01A.class, l260m01d.getMainId());
				if (l260m01a != null) {
					rptVariableMap.put(
							"CUST",
							Util.nullToSpace(l260m01a.getCustId())
									+ Util.nullToSpace(l260m01a.getDupNo())
									+ "　"
									+ Util.nullToSpace(l260m01a.getCustName()));
				}
			}

			// 股票檔因位於SIT環境撈取要耗時 JdbcTemplate spend 5363 ms 且 目前此類型案件不多
			// 改遇到時再針對該筆資料查詢
			Map<String, String> tblMap = null;// this.getListToMap("TBL"); //
												// 股票代號中文名稱對照檔
			Map<String, String> tbnMap = this.getListToMap("TBN"); // 存摺摘要代號中英文名稱對照檔

			String bgnDateStr = sdf.format(CapDate.getDate(bgnDate,
					"yyyy-MM-dd"));
			String endDateStr = sdf.format(CapDate.getDate(endDate,
					"yyyy-MM-dd"));
			rptVariableMap.put("qBgnDate", bgnDateStr);
			rptVariableMap.put("qEndDate", endDateStr);

			List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();

			if (Util.notEquals(q0060Flag, "N")) {

				Date today = CapDate.getDate(CapDate
						.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD),
						UtilConstants.DateFormat.YYYY_MM_DD);
				Date bgn = CapDate.getDate(bgnDate, "yyyy-MM-dd");
				Date end = CapDate.getDate(endDate, "yyyy-MM-dd");
				if (bgn.compareTo(today) == 0) { // 查詢起日是今天 ==> 查 TXN & TXNN
					// 迄日一定要是今天
					// 因為 1. 迄日 晚於 起日 2. 起迄日皆不晚於今日
					List<Map<String, Object>> odsList = lms8000Service
							.findODS_0060_TXN(realActNo);

					for (Map<String, Object> row : odsList) {
						this.parseTxnData(row, titleRows, tblMap, tbnMap);
					}
				} else if (bgn.compareTo(today) < 0) {
					if (end.compareTo(today) == 0) { // 查詢迄日到今天 ==> 查 TXN & TXNN
														// + HIST
						// 起日為過去日 過去日~今日
						List<Map<String, Object>> histList = lms8000Service
								.findODS_0060_HIST(realActNo, currCode,
										bgnDateStr, endDateStr);
						for (Map<String, Object> row : histList) {
							this.parseHistData(row, titleRows);
						}

						List<Map<String, Object>> odsList = lms8000Service
								.findODS_0060_TXN(realActNo);
						for (Map<String, Object> row : odsList) {
							this.parseTxnData(row, titleRows, tblMap, tbnMap);
						}
					} else { // 起迄日皆為過去日 ==> 查 HIST
						// 過去日~過去日
						List<Map<String, Object>> histList = lms8000Service
								.findODS_0060_HIST(realActNo, currCode,
										bgnDateStr, endDateStr);

						for (Map<String, Object> row : histList) {
							this.parseHistData(row, titleRows);
						}
					}
				} else {
					Map<String, String> map = Util.setColumnMap();
					map.put("ReportBean.column01", "查詢區間有誤");
					titleRows.add(map);
				}
			} else {
				Map<String, String> map = Util.setColumnMap();
				map.put("ReportBean.column01", "此帳號不可查詢0060");
				titleRows.add(map);
			}

			if (titleRows == null || titleRows.size() == 0) {
				Map<String, String> map = Util.setColumnMap();
				map.put("ReportBean.column01", "查無資料");
				titleRows.add(map);
			}

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}

	public List<Map<String, String>> parseHistData(Map<String, Object> row,
			List<Map<String, String>> titleRows) {
		DecimalFormat dfMoney = new DecimalFormat("###,###,###,###,###,##0.00");

		String txnDate = Util.trim(row.get("BTT_0060_TXN_DATE")); // 帳務日
		String brNo = Util.trim(row.get("BTT_0060_ACT_BR_NO")); // 發動分行
		String staffNo = Util.trim(row.get("BTT_0060_TELLER_ID")); // 交易櫃員
		String memo = Util.trim(row.get("BTT_0060_TXN_MEMO")); // 交易摘要
		String dbCr = Util.trim(row.get("BTT_0060_DR_CR")); // 借貸方
		String amt = Util.trim(row.get("BTT_0060_TXN_AMT")); // 交易金額
		amt = dfMoney.format(Util.parseBigDecimal(amt));
		String bal = Util.trim(row.get("BTT_0060_TXN_AMT_BAL")); // 日終餘額
		bal = dfMoney.format(Util.parseBigDecimal(bal));
		String todayBal = Util.trim(row.get("TODAY_BAL")); // 日終餘額
		todayBal = (Util.isEmpty(todayBal) ? "" : dfMoney.format(Util
				.parseBigDecimal(todayBal)));

		String transId = Util.trim(row.get("BTT_0060_TRANS_ID")); // 交易來源代號
		String misc = Util.trim(row.get("BTT_0060_MISC")); // 備註欄
		String miscStr = (misc.length() >= 20 ? misc.substring(0, 20) : misc);
		String mmDate = Util.trim(row.get("SHIST_PB_MM"));
		String ddDate = Util.trim(row.get("SHIST_PB_DD"));
		String hhTime = Util.trim(row.get("SHIST_PB_TIME_HH"));
		String mmTime = Util.trim(row.get("SHIST_PB_TIME_MM"));
		String timeStr = mmDate + "/" + ddDate + "　" + hhTime + ":" + mmTime;
		boolean miscEmpty = Util.isEmpty(miscStr);

		Map<String, String> map = Util.setColumnMap();
		map.put("ReportBean.column01", txnDate); // 帳務日
		map.put("ReportBean.column02", brNo + "-" + staffNo); // 櫃號
		map.put("ReportBean.column03", memo); // 摘要
		if (Util.equals(dbCr, "D") || Util.equals(dbCr, "H")) {
			map.put("ReportBean.column04", amt); // 提出金額
			map.put("ReportBean.column05", (miscEmpty ? ""
					: ("(" + timeStr + ")"))); // 存入金額
		} else if (Util.equals(dbCr, "C")) {
			map.put("ReportBean.column04", (miscEmpty ? ""
					: ("(" + timeStr + ")"))); // 提出金額
			map.put("ReportBean.column05", amt); // 存入金額
		} else {
			map.put("ReportBean.column04", ""); // 提出金額
			map.put("ReportBean.column05", ""); // 存入金額
		}
		map.put("ReportBean.column06", todayBal); // 日終餘額
		map.put("ReportBean.column07", transId); // 代號
		map.put("ReportBean.column08", (miscEmpty ? timeStr : miscStr)); // 備註
		titleRows.add(map);

		return titleRows;
	}

	public List<Map<String, String>> parseTxnData(Map<String, Object> row,
			List<Map<String, String>> titleRows, Map<String, String> tblMap,
			Map<String, String> tbnMap) {
		DecimalFormat dfMoney = new DecimalFormat("###,###,###,###,###,##0.00");

		String textId = Util.trim(row.get("TCSTXN_TEXTID"));
		String textId_1 = (textId.length() >= 1 ? textId.substring(0, 1)
				: textId);
		String pbRepeatNo = Util.trim(row.get("PBAIOE_REPEAT_NO"));
		Integer pbRepeat = NumberUtils.toInt(pbRepeatNo);
		String ckRepeatNo = Util.trim(row.get("CKAIOE_REPEAT_NO"));
		Integer ckRepeat = NumberUtils.toInt(ckRepeatNo);

		String today = Util.trim(row.get("TODAY_BAL")); // 是否呈現 當日餘額
		boolean lastFlag = Util.equals(today, "Y");

		if (Util.equals(textId_1, "1") && pbRepeat > 0) {
			this.parseRepeat(row, titleRows, pbRepeat, "PB", tblMap, tbnMap,
					lastFlag);
		} else if (Util.equals(textId_1, "2") && ckRepeat > 0) {
			this.parseRepeat(row, titleRows, ckRepeat, "CK", tblMap, tbnMap,
					lastFlag);
		} else {
			String txnDate = Util.trim(row.get("TCSTXN_DATE")); // 帳務日
			String brNo = Util.trim(row.get("TCSTXN_CLS_BRN")); // 發動分行
			String staffNo = Util.trim(row.get("TCIA_EMPLE_NO")); // 交易櫃員
			String memo = Util.trim(row.get("AIOE_MEMO")); // 交易摘要
			String dbCr = Util.trim(row.get("AIOE_DBCR")); // 借貸方
			String amt = Util.trim(row.get("AIOE_AMT")); // 交易金額
			amt = dfMoney.format(Util.parseBigDecimal(amt));
			String bal = (lastFlag ? dfMoney.format(this.getTodayBal(row)) : ""); // 日終餘額
			String transId = Util.trim(row.get("TCSTXN_TRANSID")); // 交易來源代號
			String misc = Util.trim(row.get("AIOE_MISC")); // 備註欄
			String miscStr = (misc.length() >= 20 ? misc.substring(0, 20)
					: misc);
			String mmDate = Util.trim(row.get("TCIA_ATM_ORG_DATE_MM"));
			String ddDate = Util.trim(row.get("TCIA_ATM_ORG_DATE_DD"));
			String hhTime = Util.trim(row.get("TCIA_TIME_HH"));
			String mmTime = Util.trim(row.get("TCIA_TIME_MM"));
			String timeStr = mmDate + "/" + ddDate + "　" + hhTime + ":"
					+ mmTime;
			boolean miscEmpty = Util.isEmpty(miscStr);

			Map<String, String> map = Util.setColumnMap();
			map.put("ReportBean.column01", txnDate); // 帳務日
			map.put("ReportBean.column02", brNo + "-" + staffNo); // 櫃號
			map.put("ReportBean.column03", this.getMemo(tblMap, tbnMap, memo)); // 摘要
			if (Util.equals(dbCr, "D") || Util.equals(dbCr, "H")) {
				map.put("ReportBean.column04", amt); // 提出金額
				map.put("ReportBean.column05", (miscEmpty ? ""
						: ("(" + timeStr + ")"))); // 存入金額
			} else if (Util.equals(dbCr, "C")) {
				map.put("ReportBean.column04", (miscEmpty ? ""
						: ("(" + timeStr + ")"))); // 提出金額
				map.put("ReportBean.column05", amt); // 存入金額
			} else {
				map.put("ReportBean.column04", ""); // 提出金額
				map.put("ReportBean.column05", ""); // 存入金額
			}
			map.put("ReportBean.column06", (lastFlag ? bal : "")); // 日終餘額
			map.put("ReportBean.column07", transId); // 代號
			map.put("ReportBean.column08", (miscEmpty ? timeStr : miscStr)); // 備註
			titleRows.add(map);
		}

		return titleRows;
	}

	public List<Map<String, String>> parseRepeat(Map<String, Object> row,
			List<Map<String, String>> titleRows, Integer repeat, String type,
			Map<String, String> tblMap, Map<String, String> tbnMap,
			boolean lastFlag) {
		DecimalFormat dfMoney = new DecimalFormat("###,###,###,###,###,##0.00");
		BigDecimal amtTotal = BigDecimal.ZERO;
		for (int i = 1; i <= repeat; i++) {
			boolean last = ((i == repeat) && lastFlag);
			String thisRound = Integer.toString(i);
			String txnDate = Util.trim(row.get("TCSTXN_DATE")); // 帳務日
			String brNo = Util.trim(row.get("TCSTXN_CLS_BRN")); // 發動分行
			String staffNo = Util.trim(row.get("TCIA_EMPLE_NO")); // 交易櫃員

			String memo = ""; // 交易摘要
			if (Util.equals(type, "PB")) {
				memo = Util.trim(row.get("PBAIOE_1_MEMO_" + thisRound));
			} else if (Util.equals(type, "CK")) {
				memo = Util.trim(row.get("CKAIOE_1_MEMO_" + thisRound));
			}

			String dbCr = Util.trim(row.get("AIOE_DBCR")); // 借貸方
			String amtStr = "";
			BigDecimal amt = BigDecimal.ZERO; // 交易金額
			if (Util.equals(type, "PB")) {
				amtStr = dfMoney.format(Util.parseBigDecimal(Util.trim(row
						.get("PBAIOE_1_AMT_" + thisRound))));
				amt = Util.parseBigDecimal(Util.trim(row.get("PBAIOE_1_AMT_"
						+ thisRound)));
			} else if (Util.equals(type, "CK")) {
				amtStr = dfMoney.format(Util.parseBigDecimal(Util.trim(row
						.get("CKAIOE_1_AMT_" + thisRound))));
				amt = Util.parseBigDecimal(Util.trim(row.get("CKAIOE_1_AMT_"
						+ thisRound)));
			}
			amtTotal = amtTotal.add(amt);
			String aioeAmtStr = dfMoney.format(Util.parseBigDecimal(Util
					.trim(row.get("AIOE_AMT"))));
			BigDecimal aioeAmt = Util.parseBigDecimal(Util.trim(row
					.get("AIOE_AMT")));
			String aioeBalStr = dfMoney.format(Util.parseBigDecimal(Util
					.trim(row.get("AIOE_BAL"))));
			BigDecimal aioeBal = Util.parseBigDecimal(Util.trim(row
					.get("AIOE_BAL")));// Util.parseBigDecimal(aioeBalStr);
										// 因為有千分位會轉換失敗

			String bal = (last ? dfMoney.format(this.getTodayBal(row)) : ""); // 日終餘額

			String transId = Util.trim(row.get("TCSTXN_TRANSID")); // 交易來源代號

			String misc = ""; // 備註欄
			if (Util.equals(type, "PB")) {
				misc = Util.trim(row.get("PBAIOE_1_MISC_" + thisRound));
			} else if (Util.equals(type, "CK")) {
				misc = Util.trim(row.get("CKAIOE_1_CHECK_NO_" + thisRound));
				Integer miscInt = NumberUtils.toInt(misc);
				if (miscInt == 0) {
					String aioeMisc = Util.trim(row.get("AIOE_MISC"));
					misc = (Util.isNotEmpty(aioeMisc) ? aioeMisc : "");
				}
			}
			String miscStr = (misc.length() >= 20 ? misc.substring(0, 20)
					: misc);
			String mmDate = Util.trim(row.get("TCIA_ATM_ORG_DATE_MM"));
			String ddDate = Util.trim(row.get("TCIA_ATM_ORG_DATE_DD"));
			String hhTime = Util.trim(row.get("TCIA_TIME_HH"));
			String mmTime = Util.trim(row.get("TCIA_TIME_MM"));
			String timeStr = mmDate + "/" + ddDate + "　" + hhTime + ":"
					+ mmTime;
			boolean miscEmpty = Util.isEmpty(miscStr);

			Map<String, String> map = Util.setColumnMap();
			map.put("ReportBean.column01", txnDate); // 帳務日
			map.put("ReportBean.column02", brNo + "-" + staffNo); // 櫃號
			map.put("ReportBean.column03", this.getMemo(tblMap, tbnMap, memo)); // 摘要
			if (Util.equals(dbCr, "D") || Util.equals(dbCr, "H")) {
				map.put("ReportBean.column04", amtStr); // 提出金額
				map.put("ReportBean.column05", (miscEmpty ? ""
						: ("(" + timeStr + ")"))); // 存入金額
				map.put("ReportBean.column06", bal); // 日終餘額
			} else if (Util.equals(dbCr, "C")) {
				map.put("ReportBean.column04", (miscEmpty ? ""
						: ("(" + timeStr + ")"))); // 提出金額
				map.put("ReportBean.column05", amtStr); // 存入金額
				map.put("ReportBean.column06", bal); // 日終餘額
			} else {
				map.put("ReportBean.column04", ""); // 提出金額
				map.put("ReportBean.column05", ""); // 存入金額
				map.put("ReportBean.column06", ""); // 日終餘額
			}

			map.put("ReportBean.column07", transId); // 代號
			map.put("ReportBean.column08", (miscEmpty ? timeStr : miscStr)); // 備註
			titleRows.add(map);
		}
		return titleRows;
	}

	public Map<String, String> getListToMap(String type) {
		Map<String, String> result = new HashMap<String, String>();

		List<Map<String, Object>> rows = null; // new ArrayList<Map<String,
												// Object>>();
		if (Util.equals(type, "TBL")) {
			rows = lms8000Service.findODS_CMSTKTBL();

			for (Map<String, Object> row : rows) {
				result.put(Util.trim(row.get("CMSTK_CODE")),
						Util.trim(row.get("CMSTK_NAME")));
			}
		} else if (Util.equals(type, "TBN")) {
			rows = lms8000Service.findODS_CMMEMTBN();

			for (Map<String, Object> row : rows) {
				result.put(Util.trim(row.get("CMMEMN_CODE")),
						Util.trim(row.get("CMMEMN_NAME1")));
			}
		}

		return result;
	}

	public String getMemo(Map<String, String> tblMap,
			Map<String, String> tbnMap, String memo) {
		String finalMemo = "";

		/*
		 * IF AIOE_MEMO(1:1) NOT  = SPACES AND '*' CMMEMN_CODE X(06) 股票代號
		 * AIOE_MEMO(1:4) 從第一位取四碼 ELSE CMMEMN_CODE X(06) 股票代號 AIOE_MEMO(2:4)
		 * 從第二位取四碼
		 */

		String memo_1 = (memo.length() >= 1 ? memo.substring(0, 1) : memo);
		boolean isStar = false;
		if (Util.isNumeric(memo_1)) { // 讀 tblMap
			if (tblMap == null) {
				tblMap = lms8000Service.findODS_CMSTKTBL_SINGLE(memo);
			}
			if (tblMap != null) {
				finalMemo = Util.trim(tblMap.get(memo));
			}
		} else if (Util.equals(memo_1, "*") || Util.isEmpty(memo_1)) {
			isStar = true;
			// 先取從第二位開始
			String memo_2 = (memo.length() >= 2 ? memo.substring(1,
					memo.length()) : memo);
			// 再取四碼
			String memo_5 = (memo_2.length() >= 4 ? memo_2.substring(0, 4)
					: memo_2);
			if (tbnMap != null) {
				finalMemo = Util.trim(tbnMap.get(memo_5));
			}
		} else {
			// 直接取四碼
			String memo_4 = (memo.length() >= 4 ? memo.substring(0, 4) : memo);
			if (tbnMap != null) {
				finalMemo = Util.trim(tbnMap.get(memo_4));
			}
		}
		finalMemo = (Util.isEmpty(finalMemo) ? memo : finalMemo);

		finalMemo = (isStar ? (memo_1 + finalMemo) : finalMemo);

		return finalMemo;
	}

	public BigDecimal getTodayBal(Map<String, Object> row) {
		BigDecimal todayBal = BigDecimal.ZERO;

		// 資料來源 TCSTXN_VIEW -> TXNA TCSTXNN_VIEW -> TXNNA
		String src = Util.trim(row.get("SRC"));
		BigDecimal actBal = Util.parseBigDecimal(Util.trim(row
				.get("H24BL_ACT_BAL")));
		BigDecimal dbAmt = Util.parseBigDecimal(Util.trim(row
				.get("H24BL_NDTX_DB_TXN_AMT")));
		BigDecimal crAmt = Util.parseBigDecimal(Util.trim(row
				.get("H24BL_NDTX_CR_TXN_AMT")));

		if (Util.equals(src, "TCSTXN_VIEW")) { // TXNA
			todayBal = actBal.add(dbAmt).subtract(crAmt);
		} else if (Util.equals(src, "TCSTXNN_VIEW")) { // TXNNA
			todayBal = actBal;
		}

		return todayBal;
	}

	public OutputStream genLMS8000R04(PageParameters params, Properties prop,
			Locale locale) throws Exception {
		ReportGenerator generator = new ReportGenerator(
				"report/fms/LMS8000R04_" + locale.toString() + ".rpt");
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();

		List<InputStream> list = new LinkedList<InputStream>();
		OutputStream outputStream = null;

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		DecimalFormat dfMoney = new DecimalFormat("###,###,###,###,###,##0.00");

		try {
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID)); // L260M01D
																					// 的
																					// oid
			String bgnDate = Util.trim(params.getString("bgnDate"));
			String endDate = Util.trim(params.getString("endDate"));
			String custId = Util.trim(params.getString("custId", ""));
			String dupNo = Util.trim(params.getString("dupNo", "0"));
			String ioFlag = Util.trim(params.getString("ioFlag"));
			String remitType = Util.trim(params.getString("remitType"));
			String begAmt = Util.trim(params.getString("begAmt"));
			String endAmt = Util.trim(params.getString("endAmt"));
			String bankId = Util.trim(params.getString("bankId"));
			String ractNo = Util.trim(params.getString("ractNo"));

			String branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(user.getUnitNo())));
			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("qUserId", Util.nullToSpace(user.getUserId()));
			String bgnDateStr = sdf.format(CapDate.getDate(bgnDate,
					"yyyy-MM-dd"));
			String endDateStr = sdf.format(CapDate.getDate(endDate,
					"yyyy-MM-dd"));
			rptVariableMap.put("qBgnDate", bgnDateStr);
			rptVariableMap.put("qEndDate", endDateStr);
			rptVariableMap.put("CUST", "");
			L260M01D l260m01d = lms8000Service.findModelByOid(L260M01D.class,
					mainId);
			if (l260m01d != null) {
				L260M01A l260m01a = lms8000Service.findModelByMainId(
						L260M01A.class, l260m01d.getMainId());
				if (l260m01a != null) {
					rptVariableMap.put(
							"CUST",
							Util.nullToSpace(l260m01a.getCustId())
									+ Util.nullToSpace(l260m01a.getDupNo())
									+ "　"
									+ Util.nullToSpace(l260m01a.getCustName()));
				}
			}
			rptVariableMap.put("qFactor", "");
			StringBuffer qFactor = new StringBuffer("");
			if (Util.isNotEmpty(ioFlag)) {
				qFactor.append(prop.getProperty("btt.ioFlag")).append("：")
						.append(prop.getProperty("radio.io" + ioFlag, ioFlag));
			}
			if (Util.isNotEmpty(remitType)) {
				Map<String, String> codeMap = codetypeservice.findByCodeType(
						"postLoan_remitType", locale.toString());
				qFactor.append((qFactor.length() > 0 ? "\r\n" : ""));
				qFactor.append(prop.getProperty("btt.remitType")).append("：")
						.append(codeMap.get(remitType));
			}
			if (Util.isNotEmpty(begAmt) || Util.isNotEmpty(endAmt)) {
				qFactor.append((qFactor.length() > 0 ? "\r\n" : ""));
				boolean hasBeg = false;
				if (Util.isNotEmpty(begAmt)) {
					hasBeg = true;
					qFactor.append(prop.getProperty("btt.begAmt"))
							.append("：")
							.append(dfMoney.format(Util.parseBigDecimal(begAmt)));
				}
				if (Util.isNotEmpty(endAmt)) {
					qFactor.append((hasBeg ? "　" : ""));
					qFactor.append(prop.getProperty("btt.endAmt"))
							.append("：")
							.append(dfMoney.format(Util.parseBigDecimal(endAmt)));
				}
			}
			if (Util.isNotEmpty(bankId)) {
				qFactor.append((qFactor.length() > 0 ? "\r\n" : ""));
				qFactor.append(prop.getProperty("btt.bankId")).append("：")
						.append(bankId);
			}
			if (Util.isNotEmpty(ractNo)) {
				qFactor.append((qFactor.length() > 0 ? "\r\n" : ""));
				qFactor.append(prop.getProperty("btt.ractNo")).append("：")
						.append(ractNo);
			}
			rptVariableMap.put("qFactor", qFactor.toString());

			List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();

			List<Map<String, Object>> odsList = lms8000Service.findODS_8250(
					user.getUnitNo(), ioFlag, "2", remitType, bgnDate, endDate,
					custId, dupNo, begAmt, endAmt, bankId, ractNo);

			for (Map<String, Object> row : odsList) {
				String AP85_DATE = Util.trim(row.get("AP85_DATE")); // 交易日期
				String AP85_TIME = Util.trim(row.get("AP85_TIME")); // 交易時間
				String AP85_RMNO = Util.trim(row.get("AP85_RMNO")); // 匯款編號
				String AP85_STATUS = Util.trim(row.get("AP85_STATUS")); // 交易處理狀態
				String AP85_SNAME = Util.trim(row.get("AP85_SNAME")); // 匯款人姓名
				String AP85_TXNNO = Util.trim(row.get("AP85_TXNNO")); // 交易序號
				String AP85_ACTNO = Util.trim(row.get("AP85_ACTNO")); // 匯款帳號
				String AP85_AMOUNT = Util.trim(row.get("AP85_AMOUNT")); // 匯款金額
																		// 9(11)V99
				AP85_AMOUNT = (Util.isEmpty(AP85_AMOUNT) ? "" : dfMoney
						.format(Util.parseBigDecimal(AP85_AMOUNT)));
				String AP85_RNAME = Util.trim(row.get("AP85_RNAME")); // 收款人姓名
				String AP85_BANKID = Util.trim(row.get("AP85_BANKID")); // 對方行代號
				String AP85_BANK_NAME = Util.trim(row.get("AP85_BANK_NAME")); // 對方行名稱
				String AP85_NOTE = Util.trim(row.get("AP85_NOTE")); // 附言

				Map<String, String> map = Util.setColumnMap();
				map.put("ReportBean.column01", AP85_DATE); // 交易日期
				map.put("ReportBean.column02", AP85_TIME); // 交易時間
				map.put("ReportBean.column03", AP85_RMNO); // 匯款編號
				map.put("ReportBean.column04", AP85_STATUS); // 交易處理狀態
				map.put("ReportBean.column05", AP85_SNAME); // 匯款人姓名
				map.put("ReportBean.column06", AP85_TXNNO); // 交易序號
				map.put("ReportBean.column07", AP85_ACTNO); // 匯款帳號
				map.put("ReportBean.column08", AP85_AMOUNT); // 匯款金額
				map.put("ReportBean.column09", AP85_RNAME); // 收款人姓名
				map.put("ReportBean.column10", AP85_BANKID); // 對方行代號
				map.put("ReportBean.column11", AP85_BANK_NAME); // 對方行名稱
				map.put("ReportBean.column12", AP85_NOTE); // 附言

				titleRows.add(map);
			}

			if (titleRows == null || titleRows.size() == 0) {
				Map<String, String> map = Util.setColumnMap();
				map.put("ReportBean.column01", "查無資料");
				titleRows.add(map);
			}

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);

			outputStream = generator.generateReport();

			// 產生附件
			String actType = Util.trim(params.getString("actType")); // 1: 僅列印
																		// 2:
																		// 列印+附件
			if (Util.equals(actType, "2")) {
				String dateStr = (Util.equals(bgnDateStr, endDateStr) ? bgnDateStr
						: (bgnDateStr + "_" + endDateStr));
				String fileTitle = "8250_" + dateStr;

				ByteArrayOutputStream baos = null;
				baos = (ByteArrayOutputStream) outputStream;

				DocFile file = new DocFile();

				// J-111-0025_05097_B1001
				// 為增進eloan擔保品及貸後管理查詢時價登入作業效率,增加相關作業需求
				// file.setMainId(mainId); // L260M01D 的 oid
				file.setMainId(l260m01d.getMainId()); // L260M01D 的 mainId =
														// L260M01A 的 MAINID
				file.setPid(mainId); // L260M01D 的 oid
				file.setData(baos != null ? baos.toByteArray() : null);
				file.setFileDesc(fileTitle);
				// J-111-0025_05097_B1001 為增進eloan擔保品及貸後管理查詢時價登入作業效率,增加相關作業需求
				file.setCrYear(CapDate.getCurrentDate("yyyy-MM"));
				file.setFieldId("postLoanCertified");
				file.setSrcFileName(fileTitle + ".pdf");
				file.setUploadTime(CapDate.getCurrentTimestamp());
				file.setBranchId(user.getUnitNo());
				file.setContentType("application/pdf");
				file.setSysId("LMS");
				docFileService.save(file);
			}
		} finally {
			if (list != null) {
				list.clear();
			}
		}

		return outputStream;
	}

	public OutputStream genLMS8000R05(PageParameters params, Properties prop,
			Locale locale) throws Exception {
		ReportGenerator generator = new ReportGenerator(
				"report/fms/LMS8000R05_" + locale.toString() + ".rpt");
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();

		List<InputStream> list = new LinkedList<InputStream>();
		OutputStream outputStream = null;

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		DecimalFormat dfMoney = new DecimalFormat("###,###,###,###,###,##0.00");
		DecimalFormat dfMoney2 = new DecimalFormat("###,###,###,###,###,##0.##");

		try {
			// L260M01D 的 oid
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			L260M01D l260m01d = lms8000Service.findModelByOid(L260M01D.class,
					mainId);
			String loanNo = "";
			if (l260m01d != null) {
				loanNo = Util.trim(l260m01d.getLoanNo());
			}
			rptVariableMap.put("qLoanNo", loanNo);
			String branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(user.getUnitNo())));
			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("qUserId", Util.nullToSpace(user.getUserId()));

			if (l260m01d != null) {
				L260M01A l260m01a = lms8000Service.findModelByMainId(
						L260M01A.class, l260m01d.getMainId());
				if (l260m01a != null) {
					rptVariableMap.put(
							"CUST",
							Util.nullToSpace(l260m01a.getCustId())
									+ Util.nullToSpace(l260m01a.getDupNo())
									+ "　"
									+ Util.nullToSpace(l260m01a.getCustName()));
				}
			}
			String qDateStr = CapDate
					.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD);
			rptVariableMap.put("qDate", qDateStr);

			List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();

			if (Util.isNotEmpty(loanNo)) {
				List<Map<String, Object>> odsList = lms8000Service
						.findODS_8410_ByAccNo(loanNo, user.getUnitNo());

				for (Map<String, Object> row : odsList) {
					String RIMTXNNO = Util.trim(row.get("SDR4560_RIMTXNNO")); // 交易序號
					String RIMPDATE = Util.trim(row.get("SDR4560_RIMPDATE")); // 交易日期
					String RIMTIME = Util.trim(row.get("SDR4560_RIMTIME")); // 交易時間
					String RIMKEY = Util.trim(row.get("SDR4560_RIMKEY")); // 匯款編號(匯入)
					String RIMSBNK = Util.trim(row.get("SDR4560_RIMSBNK")); // 匯款行代號
					String RIMSBNK_NAM = Util.trim(row
							.get("SDR4560_RIMSBNK_NAM")); // 匯款行名稱
					String ROMKEY = Util.trim(row.get("SDR4560_ROMKEY")); // 匯款帳號(匯出)
					String RIMSNAM = Util.trim(row.get("SDR4560_RIMSNAM")); // 匯款人姓名
					String RIMAMNT = Util.trim(row.get("SDR4560_RIMAMNT")); // 匯款金額
					// 9(11)V99
					RIMAMNT = (Util.isEmpty(RIMAMNT) ? "" : dfMoney.format(Util
							.parseBigDecimal(RIMAMNT)));
					/*
					 * // 大寫金額 String RIMAMNT_C =
					 * Util.trim(row.get("SDR4560_RIMAMNT")); // 匯款金額 RIMAMNT_C
					 * = (Util.isEmpty(RIMAMNT_C) ? "" : NumConverter
					 * .toChineseNumberFull
					 * (dfMoney2.format(Util.parseBigDecimal(RIMAMNT_C))));
					 */
					String RIMREMRK = Util.trim(row.get("SDR4560_RIMREMRK")); // 附言
					String RIMRNAM = Util.trim(row.get("SDR4560_RIMRNAM")); // 收款人姓名
					String RIMRTEL = Util.trim(row.get("SDR4560_RIMRTEL")); // 收款人電話
					String RIMRACNO = Util.trim(row.get("SDR4560_RIMRACNO")); // 收款人帳號

					Map<String, String> map = Util.setColumnMap();
					map.put("ReportBean.column01", RIMSBNK + RIMSBNK_NAM
							+ "<br/>" + RIMSNAM);
					map.put("ReportBean.column02", RIMAMNT);
					map.put("ReportBean.column03", RIMRNAM + "<br/>" + RIMRTEL);
					map.put("ReportBean.column04", RIMKEY);
					map.put("ReportBean.column05", RIMRACNO);
					map.put("ReportBean.column06", RIMTXNNO + "<br/>"
							+ RIMPDATE + "<br/>" + RIMTIME);
					map.put("ReportBean.column07", RIMREMRK);
					/*
					 * map.put("ReportBean.column01", RIMTXNNO);
					 * map.put("ReportBean.column02", RIMPDATE);
					 * map.put("ReportBean.column03", RIMTIME);
					 * map.put("ReportBean.column04", RIMKEY);
					 * map.put("ReportBean.column05", RIMSBNK + RIMSBNK_NAM);
					 * map.put("ReportBean.column06", RIMSNAM);
					 * map.put("ReportBean.column07", Util.isNotEmpty(RIMAMNT_C)
					 * ? (RIMAMNT_C + "元整") : "");
					 * map.put("ReportBean.column08", RIMAMNT);
					 * map.put("ReportBean.column09", RIMREMRK);
					 * map.put("ReportBean.column10", RIMRNAM);
					 * map.put("ReportBean.column11", RIMRTEL);
					 * map.put("ReportBean.column12", RIMRACNO);
					 */

					titleRows.add(map);
				}
			}

			if (titleRows == null || titleRows.size() == 0) {
				Map<String, String> map = Util.setColumnMap();
				map.put("ReportBean.column01", "查無資料");
				titleRows.add(map);
			}

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);

			outputStream = generator.generateReport();

			// 1: 僅列印 2: 列印+附件
			String actType = Util.trim(params.getString("actType"));
			// 產生附件
			if (Util.equals(actType, "2")) {
				String fileTitle = "當日匯入匯款_" + qDateStr;

				ByteArrayOutputStream baos = null;
				baos = (ByteArrayOutputStream) outputStream;

				DocFile file = new DocFile();

				// J-111-0025_05097_B1001 為增進eloan擔保品及貸後管理查詢時價登入作業效率,增加相關作業需求
				// L260M01D 的 mainId = L260M01A 的 MAINID
				file.setMainId(l260m01d.getMainId());
				file.setPid(mainId); // L260M01D 的 oid
				file.setData(baos != null ? baos.toByteArray() : null);
				file.setFileDesc(fileTitle);
				// J-111-0025_05097_B1001 為增進eloan擔保品及貸後管理查詢時價登入作業效率,增加相關作業需求
				file.setCrYear(CapDate.getCurrentDate("yyyy-MM"));
				file.setFieldId("postLoanCertified");
				file.setSrcFileName(fileTitle + ".pdf");
				file.setUploadTime(CapDate.getCurrentTimestamp());
				file.setBranchId(user.getUnitNo());
				file.setContentType("application/pdf");
				file.setSysId("LMS");
				docFileService.save(file);
			}
		} finally {
			if (list != null) {
				list.clear();
			}
		}

		return outputStream;
	}

	// J-112-0307
	// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	public OutputStream genLMS8000R06(PageParameters params, Properties prop,
			Locale locale) throws Exception {
		ReportGenerator generator = new ReportGenerator(
				"report/fms/LMS8000R06_" + locale.toString() + ".rpt");

		final String oidL260M01D = Util.trim(params.getString("oidL260M01D"));
		final String randomCode_S01D = Util.trim(params
				.getString("randomCode_S01D"));
		final String spaceStr = "&nbsp;";

		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<InputStream> list = new LinkedList<InputStream>();
		OutputStream outputStream = null;

		try {

			List<L260S01D> l260s01dList = (List<L260S01D>) lms8000Service
					.findListByMainIdNotDel(L260S01D.class, oidL260M01D, true);
			L260S01D l260s01d = new L260S01D();
			StringBuffer tmpContent = new StringBuffer();
			if (!Util.isEmpty(l260s01dList) && l260s01dList.size() > 0) {
				l260s01d = l260s01dList.get(0);

				rptVariableMap
						.put("VISITCOMPNAME", l260s01d.getVisitCompName());

				tmpContent.setLength(0);
				tmpContent.append("<table>");
				tmpContent.append("<tr>");
				tmpContent.append("<td>");
				String visitWay = l260s01d.getVisitWay();
				if (Util.equals(visitWay, "1")) {
					tmpContent.append("■");
				} else {
					tmpContent.append("□");
				}
				tmpContent.append(prop.getProperty("L260S01D.visitWayOption1"));
				tmpContent.append("</td>");
				tmpContent.append("</tr>");

				tmpContent.append("<tr>");
				tmpContent.append("<td>");
				if (Util.equals(visitWay, "2")) {
					tmpContent.append("■");
				} else {
					tmpContent.append("□");
				}
				tmpContent.append(prop.getProperty("L260S01D.visitWayOption2"));
				tmpContent.append("</td>");
				tmpContent.append("</tr>");

				tmpContent.append("<tr>");
				tmpContent.append("<td>");
				tmpContent.append(prop.getProperty("L260S01D.visitWayMsg"));
				tmpContent.append("</td>");
				tmpContent.append("</tr>");

				tmpContent.append("</table>");

				rptVariableMap.put("VISITWAY", tmpContent.toString());

				tmpContent.setLength(0);
				String visitDt = CapDate.formatDate(l260s01d.getVisitDt(),
						"yyyy-MM-dd");
				String[] visitDtAry = visitDt.split("-");
				if (Util.isNotEmpty(visitDtAry) && visitDtAry.length >= 3) {
					tmpContent
							.append(String.valueOf(Integer
									.parseInt(visitDtAry[0]) - 1911)).append(
									prop.getProperty("L260S01D.YYY"));
					tmpContent.append(visitDtAry[1]).append(
							prop.getProperty("L260S01D.MM"));
					tmpContent.append(visitDtAry[2]).append(
							prop.getProperty("L260S01D.DD"));
				}
				rptVariableMap.put("VISITDT", tmpContent.toString());

				rptVariableMap.put("VISITPLACE", l260s01d.getVisitPlace());
				rptVariableMap.put("VISITORJOBTITLE",
						l260s01d.getVisitorJobTitle());
				rptVariableMap.put("VISITORNAME", l260s01d.getVisitorName());
				rptVariableMap.put("VISITORPHONE", l260s01d.getVisitorPhone());

				rptVariableMap
						.put("UNITMGR_S01D", userInfoService
								.getUserName(l260s01d.getUnitMgr_S01D()));
				rptVariableMap.put("ACCOUNTMGR_S01D", userInfoService
						.getUserName(l260s01d.getAccountMgr_S01D()));
				rptVariableMap.put("L260M01A.RANDOMCODE", randomCode_S01D);

				Map<String, String> titleMap = new HashMap<String, String>();
				Map<String, String> checkYNKMap = new HashMap<String, String>();
				Map<String, String> checkYMap = new HashMap<String, String>();
				Map<String, String> sameGroupMap = new HashMap<String, String>();

				if (LrsUtil.compareRptVersion(l260s01d.getRptId_S01D(), "<=",
						UtilConstants.Lms8000m01_visitComVer.V_O_202308)) {

					titleMap.put("A001", "");
					titleMap.put("A015", "");

					checkYNKMap.put("A002", "A002");
					checkYNKMap.put("A003", "A003");
					checkYNKMap.put("A004", "A004");
					checkYNKMap.put("A005", "A005");
					checkYNKMap.put("A006", "A006");
					checkYNKMap.put("A007", "A007");
					checkYNKMap.put("A008", "A008");
					checkYNKMap.put("A009", "A009");
					checkYNKMap.put("A010", "A010");
					checkYNKMap.put("A011", "A011");
					checkYNKMap.put("A012", "A012");
					checkYNKMap.put("A013", "A013");
					checkYNKMap.put("A014", "A014");

					checkYMap.put("A016", "A016");
					checkYMap.put("A017", "A017");
					checkYMap.put("A018", "A018");
					checkYMap.put("A021", "A021");
					checkYMap.put("A022", "A022");

					sameGroupMap.put("A019", "A019");
					sameGroupMap.put("A020", "A020");

				}

				List<L260S01E> l260s01eList = (List<L260S01E>) lms8000Service
						.findListByMainIdNotDel(L260S01E.class,
								l260s01d.getOid(), true);
				tmpContent.setLength(0);
				tmpContent
						.append("<table border=\"0px\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border: 0px #000000 solid; \">");
				if (Util.isNotEmpty(l260s01eList)) {
					for (L260S01E l260s01e : l260s01eList) {

						String itemNo = Util.trim(l260s01e.getItemNo());
						String itemSeqShow = Util.trim(l260s01e
								.getItemSeqShow());
						String itemContent = Util.trim(l260s01e
								.getItemContent());
						String chkText = Util.trim(l260s01e.getChkText());
						String chkResult = Util.trim(l260s01e.getChkResult());

						if (titleMap.containsKey(itemNo)) {

							tmpContent.append("<tr>");

							tmpContent.append("<td colspan='2'>");
							tmpContent.append(itemContent);
							tmpContent.append("</td>");

							tmpContent.append("</tr>");

							if (Util.equals(itemNo, "A001")) {
								tmpContent.append("<tr>");

								tmpContent.append("<td colspan='2'>");
								tmpContent.append(
										prop.getProperty("L260S01E.Y")).append(
										" ");
								tmpContent.append(
										prop.getProperty("L260S01E.N")).append(
										" ");
								tmpContent.append(
										prop.getProperty("L260S01E.KDESC"))
										.append(" ");
								tmpContent.append("</td>");

								tmpContent.append("</tr>");
							}

						} else if (checkYNKMap.containsKey(itemNo)) {

							tmpContent.append("<tr>");

							tmpContent.append("<td width='15%'>");
							tmpContent.append(toBox("Y|N|K", chkResult, 1));
							tmpContent.append("</td>");

							tmpContent.append("<td nowrap width='85%'>");
							tmpContent.append(itemContent);
							if (Util.notEquals(chkText, "")) {
								tmpContent.append("<br/>");
								tmpContent.append("(");
								tmpContent.append(
										prop.getProperty("L260S01E.title5"))
										.append("：");
								tmpContent.append(chkText);
								tmpContent.append(")");

							}
							tmpContent.append("</td>");

							tmpContent.append("</tr>");

						} else if (checkYMap.containsKey(itemNo)) {

							tmpContent.append("<tr>");

							tmpContent.append("<td colspan='2' >");
							tmpContent.append(itemSeqShow);
							tmpContent.append(toBox("Y", chkResult, 1));
							tmpContent.append(itemContent);
							if (Util.notEquals(chkText, "")) {
								tmpContent.append("<br/>");
								tmpContent.append(spaceStr).append(spaceStr)
										.append(spaceStr).append(chkText);
							}
							tmpContent.append("</td>");

							tmpContent.append("</tr>");

						} else if (sameGroupMap.containsKey(itemNo)) {

							tmpContent.append("<tr>");

							tmpContent.append("<td colspan='2'>");

							String[] chkResultAry = chkResult.split("@");
							Map<String, String> m = new HashMap<String, String>();
							for (String s : chkResultAry) {

								if (Util.equals(s, "Y")) {
									m.put("0", s);
								} else {
									m.put(s, s);
								}

							}

							String[] itemContentAry = itemContent.split("@");
							for (int i = 0; i < itemContentAry.length; i++) {
								if (m.containsKey(String.valueOf(i))) {
									tmpContent.append("■");
								} else {
									tmpContent.append("□");
								}
								if (i == 0) {
									tmpContent.append(" ");
								}
								tmpContent.append(itemContentAry[i]);
							}

							tmpContent.append("</td>");

							tmpContent.append("</tr>");

						}
					}
				}
			}
			tmpContent.append("</table>");

			rptVariableMap.put("STR_ITEMCONTENT", tmpContent.toString());

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (list != null) {
				list.clear();
			}
		}

		return outputStream;
	}

	private String toBox(String result_fmt, String s, int spaceLen) {
		String addStr = Util.addSpaceWithValue("", spaceLen);

		String[] valSplit = Util.trim(result_fmt).split("\\|");
		if (valSplit == null || valSplit.length == 0) {
			return s;
		} else {
			List<String> r = new ArrayList<String>();
			for (String val : valSplit) {
				String showStr = (Util.equals(s, Util.getLeftStr(val, 1)) ? "■"
						: "□") + " ";
				r.add(showStr);
			}
			return StringUtils.join(r, addStr);
		}
	}

}
