package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.jcs.common.TWNDate;


/** 消金額度新做時徵信情形 **/

public class ELF459 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Column(name="ELF459_CNTRNO", length=12, columnDefinition="CHAR(12)", nullable=false,unique = true)
	private String elf459_cntrno;

	/** 新做時簽報書ID **/
	@Column(name="ELF459_RPTID", length=32, columnDefinition="CHAR(32)")
	private String elf459_rptId;

	/** 新做時額度序號ID **/
	@Column(name="ELF459_TABMAINID", length=32, columnDefinition="CHAR(32)")
	private String elf459_tabMainId;
	
	/** 簽案日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF459_CASEDATE", columnDefinition="DATE")
	private Date elf459_caseDate;

	/** 申貸時年齡 **/
	@Column(name="ELF459_APPLYAGE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal elf459_applyAge;
	
	/** 授信期間(年) **/
	@Column(name="ELF459_LNAGE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal elf459_lnAge;
	
	/** 負面資訊第6項(Y/N)(chkItem的1=Y, 2=N, 3=NA) **/
	@Column(name="ELF459_NEGATIVE_6", length=1, columnDefinition="CHAR(1)")
	private String elf459_negative_6;

	/** 負面資訊第7項(Y/N)(chkItem的1=Y, 2=N, 3=NA) **/
	@Column(name="ELF459_NEGATIVE_7", length=1, columnDefinition="CHAR(1)")
	private String elf459_negative_7;
	
	/** 負面資訊第8項(Y/N)(chkItem的1=Y, 2=N, 3=NA) **/
	@Column(name="ELF459_NEGATIVE_8", length=1, columnDefinition="CHAR(1)")
	private String elf459_negative_8;

	/** 個人年所得(萬)元 **/
	@Column(name="ELF459_INCOME", columnDefinition="DECIMAL(8,0)")
	private BigDecimal elf459_income;	

	/** 個人負債比 **/
	@Column(name="ELF459_DRATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal elf459_dRate;
	
	/** 房貸模型評等 **/
	//也可用 ELF459 去串 ELF675 取得評等
	@Column(name="ELF459_GRADE", length=2, columnDefinition="CHAR(2)")
	private String elf459_grade;
	
	/** 出表註記1 **/
	@Column(name="ELF459_FLAG1", length=1, columnDefinition="CHAR(1)")
	private String elf459_flag1;
	
	/** 出表註記2 **/
	@Column(name="ELF459_FLAG2", length=1, columnDefinition="CHAR(1)")
	private String elf459_flag2;
	
	/** 出表註記3 **/
	@Column(name="ELF459_FLAG3", length=1, columnDefinition="CHAR(1)")
	private String elf459_flag3;

	/** 來源註記{'':臨櫃, '1':線上貸款}
	 * J-109-0232 目前在 CLS1220M0xFormHandler 裡的 flowAction(...) 去呼叫 misdbBASEService.update_elf459_srcflag(srcflag, cntrNo)
	 */
	@Column(name="ELF459_SRCFLAG", length=1, columnDefinition="CHAR(1)")
	private String elf459_srcflag;
	
	/** 線上貸款案件編號 */
	@Column(name="ELF459_PLOAN_NO", length=30, columnDefinition="CHAR(30)")
	private String elf459_ploan_no;
	
	/** 歡喜信貸客群方案 */
	//已在 CLS1151M01FormHandler::chk_mix_prodKind(...) 去檢核，產品種類71不可與其它產品混合簽報
	@Column(name="ELF459_TERM_GROUP", length=10, columnDefinition="CHAR(10)")
	private String elf459_term_group;
	
	/** 專案種類 */
	@Column(name="ELF459_PROJ_CLASS", length=2, columnDefinition="CHAR(2)")
	private String elf459_proj_class;
	
	public String getElf459_cntrno() {
		return elf459_cntrno;
	}

	public void setElf459_cntrno(String elf459_cntrno) {
		this.elf459_cntrno = elf459_cntrno;
	}

	public String getElf459_rptId() {
		return elf459_rptId;
	}

	public void setElf459_rptId(String elf459_rptId) {
		this.elf459_rptId = elf459_rptId;
	}

	public String getElf459_tabMainId() {
		return elf459_tabMainId;
	}

	public void setElf459_tabMainId(String elf459_tabMainId) {
		this.elf459_tabMainId = elf459_tabMainId;
	}

	public Date getElf459_caseDate() {
		return elf459_caseDate;
	}

	public void setElf459_caseDate(Date elf459_caseDate) {
		this.elf459_caseDate = elf459_caseDate;
	}

	public BigDecimal getElf459_applyAge() {
		return elf459_applyAge;
	}

	public void setElf459_applyAge(BigDecimal elf459_applyAge) {
		this.elf459_applyAge = elf459_applyAge;
	}

	public BigDecimal getElf459_lnAge() {
		return elf459_lnAge;
	}

	public void setElf459_lnAge(BigDecimal elf459_lnAge) {
		this.elf459_lnAge = elf459_lnAge;
	}

	public String getElf459_negative_6() {
		return elf459_negative_6;
	}

	public void setElf459_negative_6(String elf459_negative_6) {
		this.elf459_negative_6 = elf459_negative_6;
	}

	public String getElf459_negative_7() {
		return elf459_negative_7;
	}

	public void setElf459_negative_7(String elf459_negative_7) {
		this.elf459_negative_7 = elf459_negative_7;
	}

	public String getElf459_negative_8() {
		return elf459_negative_8;
	}

	public void setElf459_negative_8(String elf459_negative_8) {
		this.elf459_negative_8 = elf459_negative_8;
	}

	public BigDecimal getElf459_income() {
		return elf459_income;
	}

	public void setElf459_income(BigDecimal elf459_income) {
		this.elf459_income = elf459_income;
	}

	public BigDecimal getElf459_dRate() {
		return elf459_dRate;
	}

	public void setElf459_dRate(BigDecimal elf459_dRate) {
		this.elf459_dRate = elf459_dRate;
	}

	public String getElf459_grade() {
		return elf459_grade;
	}

	public void setElf459_grade(String elf459_grade) {
		this.elf459_grade = elf459_grade;
	}

	public String getElf459_flag1() {
		return elf459_flag1;
	}

	public void setElf459_flag1(String elf459_flag1) {
		this.elf459_flag1 = elf459_flag1;
	}

	public String getElf459_flag2() {
		return elf459_flag2;
	}

	public void setElf459_flag2(String elf459_flag2) {
		this.elf459_flag2 = elf459_flag2;
	}

	public String getElf459_flag3() {
		return elf459_flag3;
	}

	public void setElf459_flag3(String elf459_flag3) {
		this.elf459_flag3 = elf459_flag3;
	}

	public String getElf459_srcflag() {
		return elf459_srcflag;
	}

	public void setElf459_srcflag(String elf459_srcflag) {
		this.elf459_srcflag = elf459_srcflag;
	}

	public String getElf459_ploan_no() {
		return elf459_ploan_no;
	}
	public void setElf459_ploan_no(String elf459_ploan_no) {
		this.elf459_ploan_no = elf459_ploan_no;
	}

	public String getElf459_term_group() {
		return elf459_term_group;
	}
	public void setElf459_term_group(String elf459_term_group) {
		this.elf459_term_group = elf459_term_group;
	}

	public String getElf459_proj_class() {
		return elf459_proj_class;
	}
	public void setElf459_proj_class(String elf459_proj_class) {
		this.elf459_proj_class = elf459_proj_class;
	}

	public String toString(){
		return "[elf459_cntrno="+elf459_cntrno+"]"
				+"[elf459_rptId="+elf459_rptId+"]"
				+"[elf459_tabMainId="+elf459_tabMainId+"]"
				+"[elf459_caseDate="+TWNDate.toAD(elf459_caseDate)+"]"
				+"[elf459_applyAge="+elf459_applyAge+"]"
				+"[elf459_lnAge="+elf459_lnAge+"]"
				+"[elf459_negative_6="+elf459_negative_6+"]"
				+"[elf459_negative_7="+elf459_negative_7+"]"
				+"[elf459_negative_8="+elf459_negative_8+"]"
				+"[elf459_income="+elf459_income+"]"
				+"[elf459_dRate="+elf459_dRate+"]"
				+"[elf459_grade="+elf459_grade+"]"
				+"[elf459_flag1="+elf459_flag1+"]"
				+"[elf459_flag2="+elf459_flag2+"]"
				+"[elf459_flag3="+elf459_flag3+"]"
				+"[elf459_srcflag="+elf459_srcflag+"]"
				+"[elf459_ploan_no="+elf459_ploan_no+"]"
				+"[elf459_term_group="+elf459_term_group+"]"
				+"[elf459_proj_class="+elf459_proj_class+"]"
				;
	}
}
