/*將原先V01中只用於V02的code搬移至對應的js檔*/
$(function(){
    var Temp_UserID = "";
    var Temp_DBU_UserID = "";
    var Temp_OBU_UserID = "";
    var L230M01AGrid = $("#gridview").iGrid({
        handler: 'cls2301gridhandler',
        height: 350,
        sortname: 'createTime',
        sortorder: 'desc',
        postData: {
            formAction: "queryL230M01A",
            docStatus: viewstatus//viewstatus,
        },
        rowNum: 15,
        multiselect: true, //選項前多checkbox
        colModel: [{
            colHeader: i18n.cls2301v01['custId'],//統一編號
            align: "left",
            width: 100,
            sortable: true,
            name: 'custId',
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.cls2301v01['custName'],//客戶名稱
            align: "left",
            width: 100,
            sortable: true,
            name: 'custName'
        }, {
            colHeader: i18n.cls2301v01['caseNo'], //案件號碼
            align: "left",
            width: 150,
            sortable: true,
            name: 'caseNo'
        }, {
            colHeader: i18n.cls2301v01['reasion'], //未簽約、動用原因
            align: "left",
            width: 100,
            sortable: true,
            name: 'reasion'
        }, {
            colHeader: i18n.cls2301v01['appraiser'], //經辦姓名
            align: "left",
            width: 100,
            sortable: true,
            name: 'apprId'
        }, {
            name: 'oid',
            hidden: true //是否隱藏
        }, {
            name: 'mainId',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L230M01AGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        $.thickbox.close();
        $.form.submit({
            url: '../cls/cls2301m01/01',
            formHandler: "cls2301m01formhandler",
            data: {
                formAction: "queryL230m01a",
                mainOid: rowObject.oid,
                mainId: rowObject.mainId,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    };
});
