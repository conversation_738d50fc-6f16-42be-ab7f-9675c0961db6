package com.mega.eloan.lms.fms.report.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.AbstractReportService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.jcs.common.report.SubReportParam;

/**
 * <pre>
 * 產生以房養老搭配額度型試算PDF
 * </pre>
 * 
 * @since 2020/09/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/09/18, Benrison
 *          </ul>
 */
@Service("lms9091r04rptservice")
public class LMS9091R04RptServiceImpl extends AbstractReportService {

	@Override
	public String getReportTemplateFileName() {
		return "report/fms/LMS9091R04_" + LMSUtil.getLocale().toString()+ ".rpt";
	}
	
	private String check(int totalPeriods, BigDecimal traditionalLoanAmount, BigDecimal quotaLoanAmount){
		
		String msg = "";
		if(totalPeriods == 0){
			msg += "期數  需>0";
		}

		boolean isRemainder = traditionalLoanAmount.remainder(new BigDecimal(totalPeriods)).compareTo(BigDecimal.ZERO) != 0;
		if(isRemainder){
			msg += "傳統型貸款金額 需為 期數 的倍數(每月撥款金額 需為整數)\n";
		}
		
		isRemainder = (quotaLoanAmount.remainder(new BigDecimal(totalPeriods)).compareTo(BigDecimal.ZERO) != 0);
		if(isRemainder){
			msg += "累積型貸款金額 需為 期數 的倍數(每月撥款金額 需為整數)\n";
		}
		
		return msg;
	}

	@SuppressWarnings("unchecked")
	@Override
	public void setReportData(ReportGenerator generator, PageParameters params) throws CapException, ParseException {
		
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();

		try {
			BigDecimal traditionalLoanAmount = CrsUtil.parseBigDecimal(params.getAsDouble("traditionalLoanAmount"));
			BigDecimal quotaLoanAmount = CrsUtil.parseBigDecimal(params.getAsDouble("quotaLoanAmount"));
			BigDecimal totalLoanAmount = traditionalLoanAmount.add(quotaLoanAmount);
			Integer year = Util.parseInt(params.getAsInteger("year"));
			Integer month = Util.parseInt(params.getAsInteger("month"));
			Integer usingYear = Util.parseInt(params.getAsInteger("usingYear"));
			BigDecimal rate = CrsUtil.parseBigDecimal(params.getAsDouble("rate"));
			BigDecimal total_collateralLoanValue = CrsUtil.parseBigDecimal(params.getAsDouble("collateralLoanValue"));
			
			Integer totalPeriods = year * 12 + month;
			BigDecimal tra_monthlySendAmount = LMSUtil.prod_rmRctAmt_upperLimit(traditionalLoanAmount, totalPeriods);
			BigDecimal qua_monthlySendAmount = LMSUtil.prod_rmRctAmt_upperLimit(quotaLoanAmount, totalPeriods);
			
			BigDecimal tra_InterestMax = LMSUtil.prod_rmIntMax(tra_monthlySendAmount);
			BigDecimal qua_InterestMax = LMSUtil.prod_rmIntMax(qua_monthlySendAmount);
			
			BigDecimal tra_collateralLoanValue = total_collateralLoanValue.multiply(traditionalLoanAmount).divide(totalLoanAmount, 0, RoundingMode.DOWN);
			BigDecimal quo_collateralLoanValue = total_collateralLoanValue.multiply(quotaLoanAmount).divide(totalLoanAmount, 0, RoundingMode.DOWN);
			
			String errMsg = this.check(totalPeriods, traditionalLoanAmount, quotaLoanAmount);
			
			rptVariableMap.put("tra_loanAmount", ui_num(traditionalLoanAmount));
			rptVariableMap.put("quo_loanAmount", ui_num(quotaLoanAmount));
			rptVariableMap.put("totalLoanAmt", ui_num(totalLoanAmount));
			rptVariableMap.put("year", Util.trim(year));
			rptVariableMap.put("month", Util.trim(month));
			rptVariableMap.put("rate", ui_num(rate));
			rptVariableMap.put("tra_collateralLoanValue", ui_num(tra_collateralLoanValue));
			rptVariableMap.put("quo_collateralLoanValue", ui_num(quo_collateralLoanValue));
			rptVariableMap.put("totalPeriods", Util.trim(totalPeriods));
			rptVariableMap.put("tra_monthlySendAmount", ui_num(totalPeriods != 0 ? Arithmetic.div(traditionalLoanAmount, new BigDecimal(totalPeriods), 2) : tra_monthlySendAmount));
			rptVariableMap.put("qua_monthlySendAmount", ui_num(totalPeriods != 0 ? Arithmetic.div(quotaLoanAmount, new BigDecimal(totalPeriods), 2) : qua_monthlySendAmount));
			rptVariableMap.put("tra_InterestMax", ui_num(tra_InterestMax));
			rptVariableMap.put("qua_InterestMax", ui_num(qua_InterestMax));
			rptVariableMap.put("usingYear", String.valueOf(usingYear));
			rptVariableMap.put("errMsg", errMsg);
			
			if(Util.isEmpty(errMsg)){
				rate = rate.divide(new BigDecimal(100), 6, RoundingMode.HALF_UP);
				Map<String, Object> dataMap = this.getEveryPeriodData(rate, totalPeriods, tra_monthlySendAmount, qua_monthlySendAmount, 
																							tra_InterestMax, qua_InterestMax, tra_collateralLoanValue, 
																							quo_collateralLoanValue, usingYear);
				SubReportParam subReportParam = new SubReportParam();
				rptVariableMap.put("tra_totalRealGetAmount", ui_num((BigDecimal)dataMap.get("tra_totalRealGetAmount")));
				rptVariableMap.put("quo_totalRealGetAmount", ui_num((BigDecimal)dataMap.get("quo_totalRealGetAmount")));
				subReportParam.setData(0, rptVariableMap, (List<Map<String, String>>)dataMap.get("TRA_LIST"));			
				subReportParam.setData(1, rptVariableMap, (List<Map<String, String>>)dataMap.get("QUO_LIST"));
				generator.setSubReportParam(subReportParam);
				generator.setRowsData(new LinkedList<Map<String, String>>());
			}
			
			Locale locale = LMSUtil.getLocale();
			generator.setLang(locale);
			
			generator.setVariableData(rptVariableMap);

		} finally {

		}
	}

	private String ui_num(BigDecimal v) {
		return NumConverter.addComma(LMSUtil.pretty_numStr(v));
	}

	private Map<String, Object> getEveryPeriodData(BigDecimal rate, Integer totalPeriods, 
														BigDecimal traditionalSendAmount, BigDecimal quotaSendAmount,
														BigDecimal tra_interestMax, BigDecimal quo_interestMax, 
														BigDecimal tra_collateralLoanValue, BigDecimal quo_collateralLoanValue,
														Integer usingYear) {
		Integer usingTerm = usingYear * 12;
		BigDecimal month_12 = new BigDecimal(12);
		Map<String, Object> returnMap = new HashMap<String, Object>();
		List<Map<String, String>> traditionalList = new LinkedList<Map<String, String>>();
		List<Map<String, String>> quoList = new LinkedList<Map<String, String>>();

		BigDecimal tra_termSendAmount = traditionalSendAmount;
		BigDecimal tra_lastAccSendAmount = BigDecimal.ZERO;
		BigDecimal tra_accHoldingInterest = BigDecimal.ZERO;
		BigDecimal tra_lastAccHoldingInterest = BigDecimal.ZERO;
		BigDecimal tra_accSendAmount = BigDecimal.ZERO;
		BigDecimal tra_totalRealGetAmount = BigDecimal.ZERO;
		
		BigDecimal quo_termPaidQuota = quotaSendAmount;
		BigDecimal quo_accPaidQuota = BigDecimal.ZERO;
		BigDecimal quo_termPaidAmount = BigDecimal.ZERO;
		BigDecimal quo_accPaidAmount = BigDecimal.ZERO;
		BigDecimal quo_lastAccPaidAmount = BigDecimal.ZERO;
		BigDecimal quo_availableAmount = BigDecimal.ZERO;
		BigDecimal quo_accHoldingInterest = BigDecimal.ZERO;

		for (int period=1; period <= totalPeriods; period++) {
			
			BigDecimal tra_interest = Arithmetic.div(tra_lastAccSendAmount.multiply(rate), month_12, 0);
			BigDecimal quo_interest = Arithmetic.div(quo_lastAccPaidAmount.multiply(rate), month_12, 0);
			BigDecimal tra_termHoldingInterest = BigDecimal.ZERO;
			BigDecimal quo_termHoldingInterest = BigDecimal.ZERO;
			
			if (tra_interest.compareTo(tra_interestMax) > 0) {
				tra_termHoldingInterest = tra_interest.subtract(tra_interestMax);
				tra_interest = tra_interestMax;
			}
			
			if (quo_interest.compareTo(quo_interestMax) > 0) {
				quo_termHoldingInterest = quo_interest.subtract(quo_interestMax);
				quo_interest = quo_interestMax;
			}
			
			//check 是否大於擔保品價值
			if(tra_termSendAmount.add(tra_lastAccSendAmount).add(tra_lastAccHoldingInterest).compareTo(tra_collateralLoanValue)>0){
				
				tra_termSendAmount = tra_collateralLoanValue.subtract(tra_lastAccSendAmount).subtract(tra_lastAccHoldingInterest);
				//完全掛帳
				if(tra_termSendAmount.compareTo(BigDecimal.ZERO)<0){
					tra_termSendAmount = BigDecimal.ZERO;
					tra_interest = BigDecimal.ZERO;
					tra_termHoldingInterest = tra_interest;
				}
			}
			
			quo_accPaidQuota = quo_accPaidQuota.add(quo_termPaidQuota);
			quo_termPaidAmount = period == usingTerm ? quo_accPaidQuota : BigDecimal.ZERO;
			quo_accPaidAmount = quo_accPaidAmount.add(quo_termPaidAmount);
			quo_accHoldingInterest = quo_accHoldingInterest.add(quo_termHoldingInterest);
			quo_availableAmount = quo_accPaidQuota.subtract(quo_accPaidAmount).subtract(quo_accHoldingInterest);
			
			if( quo_availableAmount.compareTo(BigDecimal.ZERO) < 0){
				quo_availableAmount = BigDecimal.ZERO;
				quo_termPaidQuota = BigDecimal.ZERO;
				quo_interest = BigDecimal.ZERO;
				quo_termHoldingInterest = quo_interest;
			}
			
			BigDecimal tra_realGetAmount = tra_termSendAmount.subtract(tra_interest);
			tra_accSendAmount = tra_accSendAmount.add(tra_termSendAmount);
			tra_accHoldingInterest = tra_accHoldingInterest.add(tra_termHoldingInterest);
			tra_lastAccSendAmount  = tra_accSendAmount;
			tra_lastAccHoldingInterest = tra_accHoldingInterest;
			quo_lastAccPaidAmount = quo_accPaidAmount;
			tra_totalRealGetAmount = tra_totalRealGetAmount.add(tra_realGetAmount);
			
			Map<String, String> tMap = Util.setColumnMap(15);
			tMap.put("CommonBean1.field01", String.valueOf(period));
			tMap.put("CommonBean1.field02", ui_num(tra_termSendAmount));
			tMap.put("CommonBean1.field03", ui_num(tra_accSendAmount));
			tMap.put("CommonBean1.field04", ui_num(tra_interest));
			tMap.put("CommonBean1.field05", ui_num(tra_termHoldingInterest));
			tMap.put("CommonBean1.field06", ui_num(tra_accHoldingInterest));			
			tMap.put("CommonBean1.field07", ui_num(tra_realGetAmount));
			traditionalList.add(tMap);
			
			Map<String, String> qMap = Util.setColumnMap(15);
			qMap.put("CommonBean1.field01", String.valueOf(period));
			qMap.put("CommonBean1.field02", ui_num(quo_termPaidQuota));
			qMap.put("CommonBean1.field03", ui_num(quo_accPaidQuota));
			qMap.put("CommonBean1.field04", ui_num(quo_interest));
			qMap.put("CommonBean1.field05", ui_num(quo_termHoldingInterest));
			qMap.put("CommonBean1.field06", ui_num(quo_accHoldingInterest));			
			qMap.put("CommonBean1.field07", ui_num(quo_termPaidAmount));
			qMap.put("CommonBean1.field08", ui_num(quo_availableAmount));
			quoList.add(qMap);
		}
		
		returnMap.put("TRA_LIST", traditionalList);
		returnMap.put("QUO_LIST", quoList);
		returnMap.put("tra_totalRealGetAmount", tra_totalRealGetAmount);
		returnMap.put("quo_totalRealGetAmount", quo_accPaidAmount);
		return returnMap;
	}

}
