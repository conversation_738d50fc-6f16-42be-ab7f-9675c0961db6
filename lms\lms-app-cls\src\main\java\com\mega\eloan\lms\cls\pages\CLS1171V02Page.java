/* 
 * CLS1171V02Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * [國內企金]聯行額度明細表
 * </pre>
 * 
 * @since 2012/11/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/29,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1171v02")
public class CLS1171V02Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_待覆核);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.View);
		renderJsI18N(CLS1171V01Page.class);
		renderJsI18N(CLS1171M01Page.class);

		// UPGRADE: 待確認畫面是否正常
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1171V01Page');");
	}

}
