/* 
 * L210M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L210M01A;

/** 異動聯貸案參貸比率檔 **/
public interface L210M01ADao extends IGenericDao<L210M01A> {

	L210M01A findByOid(String oid);

	/**
	 * 
	 * @param oids
	 *            文件編號陣列
	 * @return List<L210M01A>
	 */
	List<L210M01A> findByOids(String[] oids);

	L210M01A findByMainId(String mainId);

	List<L210M01A> findByMainIds(String mainId);

	List<L210M01A> findByDocStatus(String docStatus);

	List<L210M01A> findByCntrNo(String CntrNo);
}