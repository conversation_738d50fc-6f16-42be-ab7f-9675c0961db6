/* 
 * LMSS08GPanel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 相關文件(個金) - 小放會會議
 * </pre>
 * 
 * @since 2013/4/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/4/9,REX,new
 *          </ul>
 */
public class LMSS08IPanel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMSS08IPanel(String id) {
		super(id);
	}
	
}
