/* 
 * L700M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L700M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L700M01A;

/** 案件分案對照表檔 **/
@Repository
public class L700M01ADaoImpl extends LMSJpaDao<L700M01A, String> implements
		L700M01ADao {

	@Override
	public L700M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L700M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L700M01A> list = createQuery(L700M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L700M01A findByUniqueKey(String branchId, String subject) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		search.addSearchModeParameters(SearchMode.EQUALS, "subject", subject);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L700M01A> findByIndex01(String branchId, String subject) {
		ISearch search = createSearchTemplete();
		List<L700M01A> list = null;
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId",
					branchId);
		if (subject != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "subject",
					subject);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L700M01A.class, search).getResultList();
		}
		return list;
	}

	@Override
	public L700M01A findByBranchId(String ownBrId, String branchId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		return findUniqueOrNone(search);
	}
}