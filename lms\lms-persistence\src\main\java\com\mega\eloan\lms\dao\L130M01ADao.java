/* 
 * L130M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L130M01A;

/** 異常通報表主檔 **/
public interface L130M01ADao extends IGenericDao<L130M01A> {

	L130M01A findByOid(String oid);
	
	List<L130M01A> findByMainId(String mainId);
	
	L130M01A findByUniqueKey(String mainId);

	List<L130M01A> findByIndex01(String mainId);
}