/* 
 * RPS4035M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.lms.lms.service.RPS4035Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 婉卻紀錄查詢
 * </pre>
 * 
 * @since 2011/5/4
 * <AUTHOR> @version <ul>
 *          <li>2011/5/4,new
 *          </ul>
 */
@Scope("request")
@Controller("rps4035m01formhandler")
public class RPS4035M01FormHandler extends AbstractFormHandler {

	@Resource
	RPS4035Service rps4035Service;

	@Resource
	ICustomerService iCustomerService;

	/**
	 * 查詢婉卻記錄
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */

	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId", "")).toUpperCase();
		String dupNo = Util.trim(params.getString("dupNo", "")).toUpperCase();
		if (!CapString.isEmpty(custId)) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			result.set("date", new Date());
			result.set("sEQuential", user.getUserCName());
			// 先查詢客戶資料檔
			List<Map<String, Object>> custList = iCustomerService
					.findById(custId);
			if (custList != null && !custList.isEmpty()) {
				Map<String, String> map = new TreeMap<String, String>();
				for (Map<String, Object> cust : custList) {
					map.put((String) cust.get("DUPNO"),
							(String) cust.get("CNAME"));
					if (custList.size() == 1) {
						result.set("dupNo", (String) cust.get("DUPNO"));
						result.set("custNm", (String) cust.get("CNAME"));
					}
				}
				result.set("custList", new CapAjaxFormResult(map));
				result.set("size", custList.size());
			} else {
				// 當資料客戶建檔沒有就抓婉卻記錄檔
				List<Map<String, Object>> lnunids = rps4035Service
						.getLnunIdByCustIdCount(custId);
				for (Map<String, Object> lnunid : lnunids) {
					result.set("dupNo", Util.trim((String) lnunid.get("DUPNO")));
					result.set("custNm",
							Util.trim((String) lnunid.get("CUSTNM")));
					break;
				}
			}
		}
		if (Util.isEmpty(dupNo)) {
			dupNo = "0";
		}
		result.set("custId", custId);
		result.set("dupNo", dupNo);
		return result;
	}
}
