package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 消金掃描對象編號檔 **/
@Entity
@Table(name="C900S02B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900S02B extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;
	
	/** 分類 **/
	@Size(max = 23)
	@Column(name = "REF_CAT", length = 23, columnDefinition = "CHAR(23)")
	private String ref_cat;
	
	/** 流水號 **/
	@Digits(integer=5, fraction=0)
	@Column(name="SEQ_NO", columnDefinition="DEC(5,0)")
	private BigDecimal seq_no;
	
	/** 表格 **/
	@Size(max=8)
	@Column(name="MAP_CLASS", length=8, columnDefinition="CHAR(8)")
	private String map_class;

	/** 表格oid **/
	@Size(max=32)
	@Column(name="MAP_OID", length=32, columnDefinition="CHAR(32)")
	private String map_oid;
	
	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;


	public String getOid() {
		return this.oid;
	}
	
	public void setOid(String value) {
		this.oid = value;
	}
	
	public String getRef_cat() {
		return ref_cat;
	}

	public void setRef_cat(String ref_cat) {
		this.ref_cat = ref_cat;
	}

	public BigDecimal getSeq_no() {
		return seq_no;
	}

	public void setSeq_no(BigDecimal seq_no) {
		this.seq_no = seq_no;
	}

	public String getMap_class() {
		return map_class;
	}

	public void setMap_class(String map_class) {
		this.map_class = map_class;
	}

	public String getMap_oid() {
		return map_oid;
	}

	public void setMap_oid(String map_oid) {
		this.map_oid = map_oid;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
}
