/* 
 * C900M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.C900M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M01A;

/** 個金產品名稱檔 **/
@Repository
public class C900M01ADaoImpl extends LMSJpaDao<C900M01A, String> implements
		C900M01ADao {

	@Override
	public C900M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C900M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C900M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public C900M01A findByUniqueKey(String prodKind) {
		ISearch search = createSearchTemplete();
		if (prodKind != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "prodKind",
					prodKind);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C900M01A> findByIndex01(String prodKind) {
		ISearch search = createSearchTemplete();
		List<C900M01A> list = null;
		if (prodKind != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "prodKind",
					prodKind);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C900M01A> getAll(boolean isForShow) {
		ISearch search = createSearchTemplete();
		search.setFirstResult(0).setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("prodKind");
		// 當為取的新作下拉選單，只撈取 c900m01b.isCancel = N 的資料
		if (!isForShow) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"c900m01b.isCancel", "N");
			search.setDistinct(true);
		}
		return find(search);
	}

	/**
	 * J-113-0036 新增新產品資訊時，將產品種類下拉選單選項做細部分類
	 * 以貸款類型取得產品種類
	 */
	@Override
	public List<C900M01A> getAllByLoanType(String loanType) {
		ISearch search = createSearchTemplete();
		search.setFirstResult(0).setMaxResults(Integer.MAX_VALUE);
		if (Util.equals("1", loanType)) {// 房貸
			search.addSearchModeParameters(SearchMode.GREATER_THAN,
					"houseLoanOrder", 0);
			search.addOrderBy("houseLoanOrder");
		} else if (Util.equals("2", loanType)) {// 信貸
			search.addSearchModeParameters(SearchMode.GREATER_THAN,
					"creditLoanOrder", 0);
			search.addOrderBy("creditLoanOrder");
		} else if (Util.equals("3", loanType)) {// 其他
			search.addSearchModeParameters(SearchMode.GREATER_THAN,
					"otherLoanOrder", 0);
			search.addOrderBy("otherLoanOrder");
			search.addOrderBy("prodKind");
		}
		return find(search);
	}

	@Override
	public Map<String, String> getProdMap() {
		Map<String, String> result = new HashMap<String, String>();
		List<C900M01A> data = find(createSearchTemplete());
		if (data != null) {
			for (int i = 0; i < data.size(); i++) {
				C900M01A pivot = data.get(i);
				String prodFullName = (Util.isEmpty(pivot.getProdNm2())) ? pivot
						.getProdNm1() : pivot.getProdNm1() + "-"
						+ pivot.getProdNm2();
				result.put(pivot.getProdKind(), prodFullName);
			}
		}
		return result;
	}
}