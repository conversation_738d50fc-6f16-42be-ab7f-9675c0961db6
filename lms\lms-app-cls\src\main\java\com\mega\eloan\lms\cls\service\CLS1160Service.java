/* 
 * CLS1160Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service;

import java.math.BigDecimal;
import java.util.List;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.base.common.MISRows;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C900M01G;
import com.mega.eloan.lms.model.L140M01R;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;

/**
 * <pre>
 * 動用審核表Service
 * </pre>
 * 
 * @since 2012/12/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/21,Fantasy,new
 *          </ul>
 */
public interface CLS1160Service extends AbstractService {

	/**
	 * createSearchTemplete
	 * 
	 * @return
	 */
	ISearch createSearchTemplete();

	/**
	 * findList
	 * 
	 * @param clazz
	 * @param serach
	 * @return
	 */
	List<? extends GenericBean> findList(Class<?> clazz, ISearch search);

	/**
	 * 依oid取得model
	 * 
	 * @param <T>
	 * @param clazz
	 * @param oid
	 * @param create
	 * @return
	 */
	<T extends GenericBean> T findModelByOid(Class<?> clazz, String oid,
			boolean create);

	/**
	 * 依mainId取得model
	 * 
	 * @param <T>
	 * @param clazz
	 * @param mainId
	 * @return
	 */
	<T extends GenericBean> T findModelByMainId(Class<?> clazz, String mainId);

	/**
	 * 儲存
	 * 
	 * @param list
	 */
	void save(List<? extends GenericBean> list);

	/**
	 * 儲存
	 * 
	 * @param list
	 */
	void save(PageParameters params, List<? extends GenericBean> list);

	/**
	 * 刪除
	 * 
	 * @param list
	 */
	void delete(List<? extends GenericBean> list);

	/**
	 * 動審表上傳MIS
	 * 
	 * @param c160m01a
	 * @throws CapMessageException
	 */
	public void uploadMIS_DW(C160M01A c160m01a) throws CapMessageException;

	public void uploadMIS(C160M01A c160m01a, C160S01D c160s01d)
			throws CapMessageException;

	public <T> void updateMisToServer(MISRows<T> misRows, String TableType);

	public <T> void upMisToServer(MISRows<T> misRows, String TalbeType);

	/**
	 * 動審表上傳MIS.PTEAMAPP(團貸年度總額度檔)
	 * 
	 * @param c160m01a
	 *            動用審核表主檔
	 * @param c160s01gsum
	 *            總額
	 */
	public void uploadMISPTEAMAPP(C160M01A c160m01a, BigDecimal c160s01gsum);

	List<? extends GenericBean> findModelByMainIdAndRefMainId(Class<?> clazz,
			String mainId, String refMainId, Integer seqNo);

	/**
	 * 重新引進簽報書中的各項費用
	 * 
	 * @param oldList
	 * @param newList
	 */
	public void reNewL140M01R(List<L140M01R> oldList, List<L140M01R> newList);

	/**
	 * 取得各項費用資料BY mainId
	 * 
	 * @param mainId
	 * @return
	 */
	List<L140M01R> getL140M01RbyMainId(String mainId);

	/**
	 * 重新引進簽報書中的各項費用
	 * 
	 * @param mainIds
	 * @param mainId
	 * @throws CapException
	 */
	public void reNewL140M01R(String[] mainIds, String mainId)
			throws CapException;

	/**
	 * 呈案前設定各項費用資料之SEQ
	 * 
	 * @param mainId
	 */
	public void setL140M01RSeq(String mainId);

	/**
	 * 查詢MIS DB是否原上傳資料已變異動過
	 * 
	 * @param l140m01r
	 * @return
	 */
	public String checkMisToUpdateL140M01R(L140M01R l140m01r);

	/**
	 * 重新引進個人資料清冊
	 * 
	 * @param mainIds
	 * @param mainId
	 * @throws CapException
	 */
	public void reNewC801M01A(String[] cntrNos, String mainId)
			throws CapException;

	/**
	 * 依mainId取得model list
	 * 
	 * @param clazz
	 *            Class
	 * @param mainId
	 *            String
	 * @param refMainId
	 *            String
	 * @return findListByMainId List<? extends GenericBean>
	 */
	// @SuppressWarnings("rawtypes")
	// List<? extends GenericBean> findListByMainId(Class clazz, String
	// mainId,String refMainId);

	public void upC900M01G_when_caseType2(C160M01A c160m01a, List<C900M01G> data,
			String reCheckId);

	public C120S01Q findC120S01QByUniqueKey(String mainId, String ownBrId,
			String custId, String dupNo);

	public void uploadDW(C160M01A c160m01a, C160S01D c160s01d);

	public <T> void upDwToServer(MISRows<T> misRows, String TableType);

	/**
	 * 只適用在 ELF504 剛上線時，補舊資料
	 * @param c160m01a
	 * @throws CapMessageException
	 */
	public void complement_ELF504(C160M01A c160m01a) throws CapMessageException;
}
