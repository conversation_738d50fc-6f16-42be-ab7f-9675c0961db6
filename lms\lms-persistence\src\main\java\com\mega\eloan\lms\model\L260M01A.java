/* 
 * L260M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 貸後管理主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L260M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L260M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

//
//	/**
//	 * 刪除註記<p/>
//	 * 文件刪除時使用(非立即性刪除)
//	 */
//	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
//	private Timestamp deletedTime;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 放款帳號 **/
	@Size(max=20)
	@Column(name="LOANNO", length=20, columnDefinition="VARCHAR(20)")
	private String loanNo;
	
	/** 退回修改原因 **/
	@Size(max=300)
	@Column(name="RTNMODIFYREASON", length=300, columnDefinition="VARCHAR(300)")
	private String rtnModifyReason;
	
	/** 曾執行退回修改 **/
	@Size(max=1)
	@Column(name="HASRTNMODIFY", length=1, columnDefinition="VARCHAR(1)")
	private String hasRtnModify;
	
//	/**
//	 * 取得刪除註記<p/>
//	 * 文件刪除時使用(非立即性刪除)
//	 */
//	public Timestamp getDeletedTime() {
//		return this.deletedTime;
//	}
//	/**
//	 *  設定刪除註記<p/>
//	 *  文件刪除時使用(非立即性刪除)
//	 **/
//	public void setDeletedTime(Timestamp value) {
//		this.deletedTime = value;
//	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得放款帳號 **/
	public String getLoanNo() {
		return this.loanNo;
	}
	/** 設定放款帳號 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}
	
	/**取得退回修改原因**/
	public String getRtnModifyReason() {
		return rtnModifyReason;
	}
	/**設定退回修改原因**/
	public void setRtnModifyReason(String rtnModifyReason) {
		this.rtnModifyReason = rtnModifyReason;
	}
	
	/**取得曾執行退回修改**/
	public String getHasRtnModify() {
		return hasRtnModify;
	}
	/**設定曾執行退回修改**/
	public void setHasRtnModify(String hasRtnModify) {
		this.hasRtnModify = hasRtnModify;
	}
	
}
