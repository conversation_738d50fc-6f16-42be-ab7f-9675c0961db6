/* 
 *MisPTEAMAPPServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;

/**
 * <pre>
 * PTEAMAPP(ELF409)團貸年度總額度檔
 * </pre>
 * 
 * @since 2012/12/24
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/24,REX,new
 *          </ul>
 */
@Service
public class MisPTEAMAPPServiceImpl extends AbstractMFAloanJdbc implements
		MisPTEAMAPPService {

	@Override
	public List<Map<String, Object>> getPTEAMAPPData(String custId, String dupNo) {
		return this.getJdbc().queryForList("MIS.PTEAMAPP.selByCustId",
				new Object[] { custId, dupNo });
	}

	@Override
	public Map<String, Object> getPTEAMAPPData(String custId, String dupNo,
			String grpcntrNo) {
		return this.getJdbc().queryForMap("MIS.PTEAMAPP.selByIdAndGrpcntrno",
				new Object[] { custId, dupNo, grpcntrNo });
	}

	@Override
	public String getMaxAmtappno(String custId, String dupNo, String branch) {
		int count = 1;
		Map<String, Object> map = this.getJdbc().queryForMap(
				"MIS.PTEAMAPP.getAMTAPPNO",
				new Object[] { custId, dupNo, branch });
		Util.addZeroWithValue(4, 4);
		if (map != null)
			count = Util.parseInt(map.get("counts")) + 1;

		return Util.addZeroWithValue(count, 4);
	}

	@Override
	public String getMaxAmtappno2(String LoanMasterNo) {
		int count = 1;
		Map<String, Object> map = this.getJdbc().queryForMap(
				"MIS.PTEAMDTL.getMAXLOTNOByGRPCNTRNO",
				new Object[] { LoanMasterNo });
		Util.addZeroWithValue(4, 4);
		if (map.get("counts") != null) {
			count = Util.parseInt(map.get("counts")) + 1;
		} else {
			map = this.getJdbc().queryForMap(
					"MIS.PTEAMAPP.getCUSTIDDUPNOYEARAMTAPPNO",
					new Object[] { LoanMasterNo });
			if (map.get("counts") != null) {
				map = this.getJdbc().queryForMap(
						"MIS.PTEAMDTL.getMAXLOTNOByGRPCNTRNO2",
						new Object[] { map.get("CUSTID"), map.get("DUPNO"),
								map.get("YEAR"), map.get("AMTAPPNO") });
				if (map.get("counts") != null) {
					count = Util.parseInt(map.get("counts")) + 1;
				}
			}
		}
		return Util.addZeroWithValue(count, 4);
	}
	
	@Override
	public List<Map<String, Object>> getPTEAMAPPDataByEFFEND(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("MIS.PTEAMAPP.selByCustIdAndEFFEND",
				new Object[] { custId, dupNo });
	}

	@Override
	public Map<String, Object> findByIdNoYear(String custId, String dupNo,
			String year) {
		return this.getJdbc().queryForMap("MIS.PTEAMAPP.findByIdNoYear",
				new Object[] { custId, dupNo, year });
	}

	@Override
	public List<Map<String, Object>> findListByIdNo(String custId, String dupNo) {
		return this.getJdbc().queryForList("MIS.PTEAMAPP.findByIdNo",
				new Object[] { custId, dupNo });
	}

	@Override
	public Map<String, Object> findByMgrnoSubid(String MGRPCNTRNO,
			String SUBCOMPID) {
		return this.getJdbc().queryForMap("MIS.PTEAMAPP.findByMgrnoSubid",
				new Object[] { MGRPCNTRNO, SUBCOMPID });
	}

	@Override
	public void insertByGrpno(String GRPCNTRNO, String SUBCOMPID,
			String SUBCOMPNM, String MGRPCNTRNO) {
		this.getJdbc().updateByCustParam("MIS.PTEAMAPP.insertByGrpno",
				new Object[] { GRPCNTRNO, SUBCOMPID, SUBCOMPNM },
				new Object[] { MGRPCNTRNO });
	}

	@Override
	public List<Map<String, Object>> getDataByCustId(String custId) {
		return getJdbc().queryForList("MIS.PTEAMAPP.FindByCustId",
				new Object[] { custId });
	}

	@Override
	public Map<String, Object> getDataByGrpCntrNo(String grpCntrNo) {
		return getJdbc().queryForMap("MIS.PTEAMAPP.FindByGrpCntrNo",
				new String[] { grpCntrNo });
	}

	@Override
	public List<PTEAMAPP> getDataBykey(String custId, String dupNo,
			String effEnd) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.PTEAMAPP.getDataBykeyAndEffEnd",
				new Object[] { custId, dupNo, effEnd });

		List<PTEAMAPP> list = new ArrayList<PTEAMAPP>();
		for (Map<String, Object> row : rowData) {
			PTEAMAPP model = new PTEAMAPP();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public PTEAMAPP getDataBykey(String custId, String dupNo, String cntrNo,
			String effEnd) {
		Map<String, Object> rowData = this.getJdbc().queryForMap(
				"MIS.PTEAMAPP.getDataBykeyAndCntrNoEffEnd",
				new Object[] { custId, dupNo, cntrNo, effEnd });

		PTEAMAPP model = new PTEAMAPP();
		if (rowData == null) {
			return null;
		} else {
			DataParse.map2Bean(rowData, model);
		}
		return model;
	}

	@Override
	public List<PTEAMAPP> getPTEAMAPPDataByBean(String custId, String dupNo) {

		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.PTEAMAPP.selByCustId", new Object[] { custId, dupNo });

		List<PTEAMAPP> list = new ArrayList<PTEAMAPP>();
		for (Map<String, Object> row : rowData) {
			PTEAMAPP model = new PTEAMAPP();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<Map<String, Object>> selCrsGroupData(String brNo, Date effend) {
		return this.getJdbc().queryForListWithMax(
				"MIS.PTEAMAPP.selCrsGroupData", new String[] { brNo, TWNDate.toAD(effend) });
	}

	@Override
	public List<Map<String, Object>> selCrsGroupDetail_orderByLoanBalDesc(
			String brNo, String grpCntrNo) {
		return this.getJdbc().queryForListWithMax(
				"MIS.PTEAMAPP.selCrsGroupDetail_orderByLoanBalDesc",
				new String[] { brNo, grpCntrNo });
	}

	@Override
	public HashMap<String, HashSet<String>> findDISTINCTCntrNoByCustId(
			HashSet<String> custIdSet) {
		String custIdParams = Util.genSqlParam(custIdSet.toArray(new String[0]));
		List<Object> params = new ArrayList<Object>();
		params.addAll(custIdSet);
//		StringBuffer custString = new StringBuffer();
//		for (String key : custIdSet) {
//			custString.append(custString.length() > 0 ? "," : "");
//			custString.append("'");
//			custString.append(key);
//			custString.append("'");
//		}

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForAllListByCustParam(
						"MIS.PTEAMAPP.getDataBykeyAndEffEndAllCustId",
						new Object[] { custIdParams, }, params.toArray(new Object[0]));

		HashMap<String, HashSet<String>> custIdAndCntrNo = new HashMap<String, HashSet<String>>();
		for (Map<String, Object> row : rowData) {
			String custId = Util.trim(row.get("custId"));
			String cntrNo = Util.trim(row.get("cntrNo"));
			if (!custIdAndCntrNo.containsKey(custId)) {
				custIdAndCntrNo.put(custId, new HashSet<String>());
			}
			custIdAndCntrNo.get(custId).add(cntrNo);
		}
		return custIdAndCntrNo;
	}

	@Override
	public List<PTEAMAPP> getDataByGrpCntrNo(HashSet<String> grpCntrNos) {
		String grpCntrNoParams = Util.genSqlParam(grpCntrNos.toArray(new String[0]));
//		StringBuffer keys = new StringBuffer();
//		for (String key : grpCntrNos) {
//			keys.append(keys.length() > 0 ? "," : "");
//			keys.append("'");
//			keys.append(key);
//			keys.append("'");
//		}
		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForAllListByCustParam("MIS.PTEAMAPP.getDataByCntrNos",
						new Object[] { grpCntrNoParams }, grpCntrNos.toArray(new String[0]));

		List<PTEAMAPP> list = new ArrayList<PTEAMAPP>();
		for (Map<String, Object> row : rowData) {
			PTEAMAPP model = new PTEAMAPP();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;

	}

	@Override
	public Map<String, Object> getMaxAmtappno(String custId, String dupNo,
			String year, String brno) {
		return this.getJdbc().queryForMap("MIS.PTEAMAPP.getMaxAmtappno",
				new Object[] { custId, dupNo, year });
	}

	@Override
	public List<PTEAMAPP> getGrpDataBykey(String custId, String dupNo,
			String effEnd) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.PTEAMAPP.getGrpDataBykey",
				new Object[] { custId, dupNo, effEnd, effEnd });

		List<PTEAMAPP> list = new ArrayList<PTEAMAPP>();
		for (Map<String, Object> row : rowData) {
			PTEAMAPP model = new PTEAMAPP();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
	
	@Override
	public int delDataByGrpCntrNo(Set<String> grpCntrNos) {

		String grpCntrnoParams = Util.genSqlParam(grpCntrNos.toArray(new String[0]));
//		StringBuffer cntrnoStr = new StringBuffer();
//		for (String cntrNo : grpCntrNos) {
//			if (Util.isNotEmpty(Util.trim(cntrNo))) {
//				cntrnoStr.append(cntrnoStr.length() > 0 ? "," : "");
//				cntrnoStr.append("'");
//				cntrnoStr.append(cntrNo);
//				cntrnoStr.append("'");
//			}
//		}
//		String cntrnoStrParam = cntrnoStr.toString();
		//---
		return this.getJdbc().updateByCustParam("MIS.PTEAMAPP.delDataByCntrNos",
				new Object[] { grpCntrnoParams }, grpCntrNos.toArray(new String[0]));

	}
	
	@Override
	public PTEAMAPP getDataByLnf020GrpCntrNo(String cntrNo) {
		Map<String, Object> rowData = this.getJdbc().queryForMap(
				"MIS.PTEAMAPP.getDataByCntrNo",
				new Object[] { cntrNo });

		PTEAMAPP model = new PTEAMAPP();
		if (rowData == null) {
			return null;
		} else {
			DataParse.map2Bean(rowData, model);
		}
		return model;
	}
	
	public PTEAMAPP getGroupLoanBuildCaseMasterAccount(String groupLoanMasterCntrNo){
		
		Map<String, Object> rowData = this.getJdbc().queryForMap("PTEAMAPP.findGroupLoanBuildCaseMasterAccount", new Object[] { groupLoanMasterCntrNo });
		
		if (rowData == null) {
			return null;
		}

		PTEAMAPP model = DataParse.map2Bean(rowData, new PTEAMAPP());
		
		return model;
	}
}
