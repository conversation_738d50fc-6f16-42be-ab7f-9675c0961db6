/* 
 * LMS9990M06Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.ui.ModelMap;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.ctr.panels.LMS9990S02APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S16Panel;
import com.mega.eloan.lms.model.C999M01A;

/**
 * <pre>
 * // * 個金約據書-主頁
 * </pre>
 * 
 * @since 2012/02/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/02/21,ICE,new
 *          </ul>
 */

@Controller
@RequestMapping("/ctr/lms9990m06/{page}")
public class LMS9990M06Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS9990M06Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		// 依權限設定button

		renderJsI18N(LMS9990M06Page.class);
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page);
		model.addAttribute("tabID", tabID);
		panel.processPanelData(model, params);
	}// ;

	// 頁籤
	public Panel getPanel(int index) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS9990S16Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new LMS9990S02APanel(TAB_CTX, true);
			break;
		default:
			panel = new LMS9990S16Panel(TAB_CTX, true);
			break;
		}
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C999M01A.class;

	}

}
