/* 
 *  LMS1411M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.form;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.lns.service.LMS1411Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.eloan.lms.model.L141M01D;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * 聯行額度明細表
 * 
 * @since 2011/12/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/2,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1411m01formhandler")
@DomainClass(L141M01A.class)
public class LMS1411M01FormHandler extends AbstractFormHandler {

	@Resource
	LMS1401Service lms1401Service;

	@Resource
	LMS1411Service lms1411Service;

	@Resource
	LMS1201Service lms1201Service;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	UserInfoService userInfoService;
	@Resource
	LMSService lmsService;

	/**
	 * 查詢聯行額度明細表
	 * 
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL141m01a(PageParameters params)	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L141M01A l141m01a = lms1411Service.findModelByOid(L141M01A.class, oid);
		if (l141m01a != null) {
			int page = Util.parseInt(params.getString(EloanConstants.PAGE));
			result = DataParse.toResult(l141m01a);
			result = formatResultShow(result, l141m01a, page);
		}
		return result;
	}

	/**
	 * 格式化顯示訊息
	 * 
	 * @param mainId
	 *            文件編號
	 * 
	 * @return
	 * @throws CapException
	 */
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L141M01A l141m01a, Integer page) throws CapException {
		switch (page) {
		case 1:
			result.set("ownBrName", l141m01a.getCaseBrId() + " "
					+ branchService.getBranchName(l141m01a.getCaseBrId()));

			result.set("docStatus",
					getMessage("docStatus." + l141m01a.getDocStatus()));

			result.set("docStatusVal", l141m01a.getDocStatus());
			result.set("typCd", getMessage("typCd." + l141m01a.getTypCd()));
			result.set("creator", lmsService.getUserName(l141m01a.getCreator()));
			result.set("updater", lmsService.getUserName(l141m01a.getUpdater()));

			StringBuffer tempL3 = new StringBuffer(0);
			StringBuffer tempL4 = new StringBuffer(0);
			StringBuffer mtempL3 = new StringBuffer(0);
			StringBuffer mtempL4 = new StringBuffer(0);
			List<L141M01D> l141m01ds = l141m01a.getL141M01D();
			for (L141M01D l141m01d : l141m01ds) {
				// 1是原本案件簽報書
				if ("1".equals(l141m01d.getBranchType())) {
					/**
					 * <pre>
					 * L1. 分行經辦(授管處/營運中心) <br/>
					 * L2. 帳戶管理員 
					 * L3. 分行授信/覆核主管 (母行) 
					 * L4.分行覆核主管(母行/授管處/營運中心) 
					 * L5. 單位/授權主管 (母行) 
					 * L6. 分行單位主管
					 * (母行/授管處/營運中心)
					 * </pre>
					 */
					if ("L1".equals(l141m01d.getStaffJob())) {
						result.set("coAppraiser", l141m01d.getStaffNo() + " "
								+ lmsService.getUserName(l141m01d.getStaffNo()));
					} else if ("L3".equals(l141m01d.getStaffJob())) {// coReCheck
						// 聯行覆核
						mtempL3.append(mtempL3.length() > 0 ? "<br/>" : "");
						mtempL3.append(l141m01d.getStaffNo());
						mtempL3.append(" ");
						mtempL3.append(lmsService.getUserName(l141m01d
								.getStaffNo()));
					} else if ("L4".equals(l141m01d.getStaffJob())) {// coReCheck
						mtempL4.append(mtempL4.length() > 0 ? "<br/>" : "");
						mtempL4.append(l141m01d.getStaffNo());
						mtempL4.append(" ");
						mtempL4.append(lmsService.getUserName(l141m01d
								.getStaffNo()));
					} else if ("L5".equals(l141m01d.getStaffJob())) {// coManager
						result.set("coManager", l141m01d.getStaffNo() + " "
								+ lmsService.getUserName(l141m01d.getStaffNo()));
					}
				} else {
					// 經辦appraiser" L1
					// 帳戶管理員"accounting" L2
					// 授信/覆核主管"mainBossShow" L3
					// 單位/授權主管manager L5
					if (UtilConstants.STAFFJOB.經辦L1.equals(l141m01d
							.getStaffJob())) {
						result.set("showAppraiser", l141m01d.getStaffNo() + " "
								+ lmsService.getUserName(l141m01d.getStaffNo()));
					} else if (UtilConstants.STAFFJOB.帳戶管理員L2.equals(l141m01d
							.getStaffJob())) {
						result.set(
								"showAccounting",
								l141m01d.getStaffNo()
										+ " "
										+ lmsService.getUserName(l141m01d
												.getStaffNo()));
					} else if ("L3".equals(l141m01d.getStaffJob())) {
						tempL3.append(tempL3.length() > 0 ? "<br/>" : "");
						tempL3.append(l141m01d.getStaffNo());
						tempL3.append(" ");
						tempL3.append(lmsService.getUserName(l141m01d
								.getStaffNo()));
					} else if ("L4".equals(l141m01d.getStaffJob())) {
						tempL4.append(tempL4.length() > 0 ? "<br/>" : "");
						tempL4.append(l141m01d.getStaffNo());
						tempL4.append(" ");
						tempL4.append(lmsService.getUserName(l141m01d
								.getStaffNo()));
					} else if ("L5".equals(l141m01d.getStaffJob())) {
						result.set("showManager", l141m01d.getStaffNo() + " "
								+ lmsService.getUserName(l141m01d.getStaffNo()));
					}

				}

			}
			String mainBossShow = tempL3.toString();
			if (tempL4.length() > 0) {
				mainBossShow += "<br/>／<br/>" + tempL4.toString();
			}
			result.set("mainBossShow", mainBossShow);

			String coReCheck = mtempL3.toString();
			if (mtempL4.length() > 0) {
				coReCheck += "<br/>／<br/>" + mtempL4.toString();
			}
			result.set("coReCheck", coReCheck);
			result.set(
					"approver",
					l141m01a.getApprover() + " "
							+ lmsService.getUserName(l141m01a.getApprover()));
			result.set("coAppraiser", l141m01a.getCoAppraiser() + " "
					+ lmsService.getUserName(l141m01a.getCoAppraiser()));
			break;
		case 2:
			break;
		case 3:
			break;
		}
		result.set("showTypCd", getMessage("typCd." + l141m01a.getTypCd()));
		result.set("showCustId",
				l141m01a.getCustId() + " " + l141m01a.getDupNo() + " "
						+ l141m01a.getCustName());
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l141m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l141m01a.getMainId()));
		return result;

	}

	/**
	 * 查詢分行下拉式選單
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBrankList(PageParameters params) throws CapException {

		Map<String, String> m = new TreeMap<String, String>();
		List<IBranch> bank = branchService.getAllBranch();
		for (IBranch b : bank) {
			String brName = Util.trim(b.getBrName());
			String brCode = b.getBrNo();
			m.put(brCode, brName);
		}
		return new CapAjaxFormResult(m);
	}

	/**
	 * 額度明細表傳送聯行
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult copyCase(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String selectBrank = Util.trim(params.getString("brank"));
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L120M01A l120m01a = lms1201Service.findL120m01aByOid(oid);
		List<L120M01C> l120m01cs = lms1401Service
				.findL120m01cListByMainId(l120m01a.getMainId());
		lms1411Service.copyL120M01A(l120m01a, selectBrank, l120m01cs);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * 查詢主管人員清單
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(AuthType.Modify)
	public IResult queryBoss(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;
	}

	/**
	 * 刪除L141M01A 聯行額度明細表主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL141m01a(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L141M01A l141m01a = lms1411Service.findModelByOid(L141M01A.class, oid);
		if (l141m01a != null) {
			lms1411Service.delete(l141m01a);
			//result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					//.getMainMessage(this.getComponent(),
							//UtilConstants.AJAX_RSP_MSG.刪除成功));
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));

		}

		return result;

	}

	/**
	 * 呈主管覆核
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(AuthType.Query)
	public IResult flowAction(PageParameters params) throws CapException {

		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L141M01A l141m01a = (L141M01A) lms1411Service.findModelByOid(
				L141M01A.class, oid);
		String[] formSelectBoss = params.getStringArray("selectBoss");

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L141M01D> models = lms1411Service
				.findL141m01dsByMainIdAndBranchType(l141m01a.getMainId(), "2");

		// 取得呈案經辦
		L141M01D appraiserModel = lms1411Service.findL141m01dsByUniqueKey(
				l141m01a.getMainId(), "2", UtilConstants.STAFFJOB.經辦L1);
		if (!Util.isEmpty(formSelectBoss)) {

			String manager = Util.trim(params.getString("manager"));
			String accounting = Util.trim(params.getString("accounting"));

			if (!models.isEmpty()) {
				lms1411Service.deleteL141m01ds(models);
			}
			List<L141M01D> l141m01ds = new ArrayList<L141M01D>();
			int seq = 0;
			for (String people : formSelectBoss) {
				L141M01D l141m01d = new L141M01D();
				l141m01d.setCreator(user.getUserId());
				l141m01d.setCreateTime(CapDate.getCurrentTimestamp());
				l141m01d.setL141m01a(l141m01a);
				l141m01d.setMainId(l141m01a.getMainId());
				l141m01d.setBranchType("2");
				l141m01d.setBranchId(user.getUnitNo());

				/*
				 * L1. 分行經辦(授管處/營運中心) L2. 帳戶管理員 L3. 分行授信/覆核主管 (母行) L4.
				 * 分行覆核主管(母行/授管處/營運中心) L5. 單位/授權主管 (母行) L6. 分行單位主管 (母行/授管處/營運中心)
				 */
				l141m01d.setStaffJob("L3");
				l141m01d.setStaffNo(people);
				l141m01d.setStaffName(lmsService.getUserName(people));
				l141m01d.setSeq(BigDecimal.valueOf(++seq));
				l141m01ds.add(l141m01d);
			}
			L141M01D managerL141m01d = new L141M01D();
			managerL141m01d.setCreator(user.getUserId());
			managerL141m01d.setCreateTime(CapDate.getCurrentTimestamp());
			managerL141m01d.setL141m01a(l141m01a);
			managerL141m01d.setMainId(l141m01a.getMainId());
			managerL141m01d.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			managerL141m01d.setStaffNo(manager);
			managerL141m01d.setBranchType("2");
			managerL141m01d.setStaffName(lmsService.getUserName(user
					.getUserId()));
			managerL141m01d.setBranchId(user.getUnitNo());
			l141m01ds.add(managerL141m01d);

			L141M01D apprL141m01d = new L141M01D();
			apprL141m01d.setCreator(user.getUserId());
			apprL141m01d.setCreateTime(CapDate.getCurrentTimestamp());
			apprL141m01d.setL141m01a(l141m01a);
			apprL141m01d.setMainId(l141m01a.getMainId());
			apprL141m01d.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprL141m01d.setStaffNo(user.getUserId());
			apprL141m01d.setBranchId(user.getUnitNo());
			apprL141m01d.setBranchType("2");
			apprL141m01d.setStaffName(lmsService.getUserName(user.getUserId()));
			l141m01ds.add(apprL141m01d);
			if (!Util.isEmpty(Util.trim(accounting))) {
				L141M01D accountL141m01d = new L141M01D();
				accountL141m01d.setCreator(user.getUserId());
				accountL141m01d.setCreateTime(CapDate.getCurrentTimestamp());
				accountL141m01d.setL141m01a(l141m01a);
				accountL141m01d.setMainId(l141m01a.getMainId());
				accountL141m01d.setStaffJob(UtilConstants.STAFFJOB.帳戶管理員L2);
				accountL141m01d.setStaffNo(accounting);
				accountL141m01d.setBranchType("2");
				accountL141m01d.setBranchId(user.getUnitNo());
				accountL141m01d
						.setStaffName(lmsService.getUserName(accounting));
				l141m01ds.add(accountL141m01d);
			}

			lms1411Service.saveL141m01ds(l141m01ds);
		}

		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {

			L141M01D l141m01d = lms1411Service.findL141m01dsByUniqueKey(
					l141m01a.getMainId(), "2", "L4");
			l141m01a.setApproveTime(CapDate.convertStringToTimestamp(params
					.getString("checkDate") + " 00:00:00"));
			if (l141m01d == null) {
				l141m01d = new L141M01D();
				l141m01d.setCreator(user.getUserId());
				l141m01d.setCreateTime(CapDate.getCurrentTimestamp());
				l141m01d.setL141m01a(l141m01a);
				l141m01d.setMainId(l141m01a.getMainId());
				l141m01d.setStaffJob("L4");
				l141m01d.setStaffNo(user.getUserId());
				l141m01d.setBranchType("2");
				l141m01d.setStaffName(user.getUsername());
			}
			// 塞入覆核主管
			l141m01d.setBranchId(user.getUnitNo());
			l141m01d.setUpdater(user.getUnitNo());
			l141m01d.setUpdateTime(CapDate.getCurrentTimestamp());
			lms1411Service.save(l141m01d);
		}

		if (l141m01a != null) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件創造者是否相同
				if (params.containsKey("flowAction") && appraiserModel != null) {

					if (!"back".equals(params.getString("flowAction"))) {
						if (user.getUserId()
								.equals(appraiserModel.getStaffNo())) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						}
					}

				}
				lms1411Service.flowAction(oid, l141m01a,
						params.containsKey("flowAction"),
						params.getString("flowAction", ""));
			} catch (Throwable t1) {
				throw new CapMessageException(getMessage(t1.getMessage()),
						getClass());
			}
		}
		return new CapAjaxFormResult();
	}

	/**
	 * 收件動作
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	public IResult getCase(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		List<L120M01A> l120m01as = lms1201Service.findL120m01asByOids(oids);
		if (!l120m01as.isEmpty()) {
			for (L120M01A l120m01a : l120m01as) {
				l120m01a.setHqReceiveDate(CapDate.getCurrentTimestamp());
			}
			lms1201Service.saveL120m01as(l120m01as);
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}

		return result;
	}

}
