/* 
 * L140M01N.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 額度利率結構利率化明細檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M01N", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "rateSeq", "rateType" }))
public class L140M01N extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 序號
	 * <p/>
	 * L140M01F_rateSeq
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "RATESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer rateSeq;

	/**
	 * 類別
	 * <p/>
	 * 100/11/24調整<br/>
	 * 1新台幣、2美金、3日幣、4歐元、5人民幣、6澳幣、7港幣、Z雜幣
	 */
	@Size(max = 1)
	@Column(name = "RATETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String rateType;

	/**
	 * 段數
	 * <p/>
	 * 0.全段<br/>
	 * 1、2、3..........
	 */
	@Size(max = 2)
	@Column(name = "SECNO", length = 2, columnDefinition = "VARCHAR (2)")
	private String secNo;

	/**
	 * 適用期間選項
	 * <p/>
	 * 全案 | 1<br/>
	 * 自動用日起迄月| 2<br/>
	 * YYYY-MM-DD~YYYY-MM-DD| 3"<br/>
	 * 自簽約日起迄月| 4<br/>
	 * CodeType=lms1401s0204_secNoOp
	 */
	@Size(max = 1)
	@Column(name = "SECNOOP", length = 1, columnDefinition = "CHAR(1)")
	private String secNoOp;

	/**
	 * 適用期間起月
	 * <p/>
	 * 適用期間為2|4
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SECBEGMON", columnDefinition = "DECIMAL(3,0)")
	private Integer secBegMon;

	/**
	 * 適用期間迄月
	 * <p/>
	 * 適用期間為2|4
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SECENDMON", columnDefinition = "DECIMAL(3,0)")
	private Integer secEndMon;

	/**
	 * 適用期間起日
	 * <p/>
	 * 適用期間為3
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "SECBEGDATE", columnDefinition = "DATE")
	private Date secBegDate;

	/**
	 * 適用期間迄日
	 * <p/>
	 * 適用期間為3
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "SECENDDATE", columnDefinition = "DATE")
	private Date secEndDate;

	/**
	 * 利率基礎
	 * <p/>
	 * (可複選)<br/>
	 * XX|XX|XX<br/>
	 * 60/3 = 20
	 */
	@Size(max = 60)
	@Column(name = "RATEBASE", length = 60, columnDefinition = "VARCHAR(60)")
	private String rateBase;

	/**
	 * 利率基礎-是否以借款同天期顯示文字
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 */
	@Size(max = 1)
	@Column(name = "RATESETALL", length = 1, columnDefinition = "CHAR(1)")
	private String rateSetAll;

	/** 利率基礎-貨幣市場利率群組 **/
	@Size(max = 120)
	@Column(name = "GROUPNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String groupName;

	/** 利率基礎-牌告利率-最小值 **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "TRATEMIN", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal tRateMin;

	/** 利率基礎-牌告利率-最大值 **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "TRATEMAX", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal tRateMax;

	/** 利率基礎-牌告利率-最小值(修改值) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "CTRATEMIN", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal ctRateMin;

	/** 利率基礎-牌告利率-最大值(修改值) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "CTRATEMAX", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal ctRateMax;

	/**
	 * 利率基礎-自訂利率參考指標-指標名稱<br/>
	 * SELECT * FROM LN.LNF070 WHERE LNF070_CODE='01PR'
	 * 
	 **/
	@Size(max = 3)
	@Column(name = "PRRATE", length = 3, columnDefinition = "VARCHAR(3)")
	private String prRate;

	/**
	 * 利率基礎-自訂利率天期
	 * <p/>
	 * 複選 30 * 4 + 30 = 150 XXXX|XXXX|XXXX<br/>
	 * 無 | B000<br/>
	 * 借款同天期|B001<br/>
	 * 一週 | W101<br/>
	 * 二週 | W102<br/>
	 * 一個月 | M101<br/>
	 * 二個月 | M102<br/>
	 * 三個月 | M103<br/>
	 * 四個月 | M104<br/>
	 * 五個月 | M105<br/>
	 * 六個月 | M106<br/>
	 * 七個月 | M107<br/>
	 * 八個月 | M108<br/>
	 * 九個月 | M109<br/>
	 * 十個月 | M110<br/>
	 * 十一個月 | M111<br/>
	 * 十二個月 | M112<br/>
	 * 二年期 | Y102<br/>
	 * 三年期 | Y103<br/>
	 * 四年期 | Y104<br/>
	 * 五年期 | Y105<br/>
	 * 一個月平均 | M301<br/>
	 * 二個月平均 | M302<br/>
	 * 三個月平均 | M303<br/>
	 * 四個月平均 | M304<br/>
	 * 五個月平均 | M305<br/>
	 * 六個月平均 | M306<br/>
	 * 七個月平均 | M307<br/>
	 * 八個月平均 | M308<br/>
	 * 九個月平均 | M309<br/>
	 * 十個月平均 | M310<br/>
	 * 十一個月平均 | M311<br/>
	 * 十二個月平均 | M312<br/>
	 * 二年期平均 | Y302<br/>
	 * 三年期平均 | Y303<br/>
	 * 四年期平均 | Y304<br/>
	 * 五年期平均 | Y305<br/>
	 * Codetype=lms1401s0204_ratePeriod
	 */
	@Size(max = 300)
	@Column(name = "RATEPERIOD", length = 300, columnDefinition = "VARCHAR(300)")
	private String ratePeriod;

	/**
	 * 利率基礎-加減年利率選項
	 * <p/>
	 * 加|1<br/>
	 * 減|2<br/>
	 * Codetype=lms1405s0204_count
	 */
	@Size(max = 1)
	@Column(name = "DISYEAROP", length = 1, columnDefinition = "CHAR(1)")
	private String disYearOp;

	/** 利率基礎-加減年利率(%) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "DISYEARRATE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal disYearRate;

	/** 利率基礎-敘做利率最小值(%) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "RERATEMIN", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal reRateMin;

	/** 利率基礎-敘做利率最大值(%) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "RERATEMAX", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal reRateMax;

	/**
	 * 利率基礎-敘做利率(%) 組合文字時顯示此項目
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 */
	@Size(max = 1)
	@Column(name = "RERATESELALL", length = 1, columnDefinition = "CHAR(1)")
	private String reRateSelAll;

	/** 利率基礎-固定利率(%) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "ATTRATE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal attRate;

	/** 利率基礎-其他(自行輸入) **/
	@Size(max = 3072)
	@Column(name = "OTHERRATEDRC", length = 3072, columnDefinition = "VARCHAR(3072)")
	private String otherRateDrc;

	/**
	 * 自訂利率參考指標U01、U02專用-與借款人敘做之市場利率代碼
	 * <p/>
	 * SIBOR | 1<br/>
	 * LIBOR | 2
	 */
	@Size(max = 1)
	@Column(name = "USDMARKET", length = 1, columnDefinition = "CHAR(1)")
	private String usdMarket;

	/**
	 * 自訂利率參考指標U01、U02專用-是否以借款同天期顯示
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 */
	@Size(max = 1)
	@Column(name = "USDSETALL", length = 1, columnDefinition = "CHAR(1)")
	private String usdSetAll;

	/**
	 * 自訂利率參考指標U01、U02專用-(A)與借款人敘作之市場利率
	 * <p/>
	 * SELECT * FROM ( SELECT LNF070_NAME ,RIGHT(LNF070_CODE,2) AS LNF070_CODE
	 * FROM LN.LNF070 WHERE LEFT(LNF070_CODE,2)='99' AND LNF070_TYPE =
	 * 'LNF030-INT-CODE' ) A <br/>
	 * 複選 SJ|SH|..|..<br/>
	 * 3*10 =30<br/>
	 * SJ:XXXXXXXX, <br/>
	 * SJ:XXXXXXXX, ...........
	 * **/
	@Size(max = 30)
	@Column(name = "USDMARKETRATE", length = 30, columnDefinition = "VARCHAR(30)")
	private String usdMarketRate;

	/**
	 * 自訂利率參考指標U01、U02專用-(B)稅賦負擔
	 * <p/>
	 * 本行|0<br/>
	 * 借款人 | 1<br/>
	 * 無 | 2<br/>
	 * Codetype=lms1401s0204_rateTax
	 */
	@Size(max = 1)
	@Column(name = "USDRATETAX", length = 1, columnDefinition = "CHAR(1)")
	private String usdRateTax;

	/** 自訂利率參考指標U01、U02專用-(C)內含 **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "USDINSIDERATE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal usdInsideRate;

	/** 自訂利率參考指標U01、U02專用-(D)S/LBOR與TAIFX差額逾 **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "USDDESRATE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal usdDesRate;

	/**
	 * 利率補充說明
	 * <p/>
	 * 100/11/22配合國內分段利率新增
	 */
	@Size(max = 3072)
	@Column(name = "RATEMEMO", length = 3072, columnDefinition = "VARCHAR(3072)")
	private String rateMemo;

	/**
	 * 利率方式
	 * <p/>
	 * 1固定利率<br/>
	 * 2機動利率<br/>
	 * 3定期浮動<br/>
	 * ※若選擇「機動利率」或「定期浮動」則不組入文字串中<br/>
	 * Codetype=lms1405s0204_rateKind
	 */
	@Size(max = 1)
	@Column(name = "RATEKIND", length = 1, columnDefinition = "CHAR(1)")
	private String rateKind;

	/**
	 * 收息方式
	 * <p/>
	 * 按月收息 | 1<br/>
	 * 每三個月收息乙次 | 2<br/>
	 * 每半年收息乙次 | 3<br/>
	 * 按年收息 | 4<br/>
	 * 本息併付 | 5<br/>
	 * 期付金 | 6<br/>
	 * 不計息(含保證、承兌) | 7<br/>
	 * 預扣利息 | 8<br/>
	 * 每三或六個月收息乙次|9<br/>
	 * CodeType=lms1401s0204_rateGetInt
	 */
	@Size(max = 1)
	@Column(name = "RATEGETINT", length = 1, columnDefinition = "CHAR(1)")
	private String rateGetInt;

	/**
	 * 利率變動方式
	 * <p/>
	 * 月 | M<br/>
	 * 年 | Y<br/>
	 * codeType=lms1401s0204_rateChgKind
	 */
	@Size(max = 1)
	@Column(name = "RATECHGKIND", length = 1, columnDefinition = "CHAR(1)")
	private String rateChgKind;

	/**
	 * 變動週期
	 * <p/>
	 * 1,2,3....
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "RATECHG1", columnDefinition = "DECIMAL(2,0)")
	private Integer rateChg1;

	/**
	 * 指定下次變動日期
	 * <p/>
	 * 100/11/22配合國內分段利率新增
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RATECHGDATE", columnDefinition = "DATE")
	private Date rateChgDate;

	/** 聯貸案專用說明 **/
	@Size(max = 4096)
	@Column(name = "UIONMEMO", length = 4096, columnDefinition = "VARCHAR(4096)")
	private String uionMemo;

	/**
	 * 稅負洽收
	 * <p/>
	 * 本行|0<br/>
	 * 借款人 | 1<br/>
	 * 無 | 2<br/>
	 * Codetype=lms1401s0204_rateTax
	 */
	@Size(max = 1)
	@Column(name = "RATETAX", length = 1, columnDefinition = "CHAR(1)")
	private String rateTax;

	/**
	 * 稅負洽收-扣稅負擔
	 * <p/>
	 * ※稅負由「借款人」負擔時輸入
	 */
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "RATETAXCODE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rateTaxCode;

	/**
	 * 限制條件/說明
	 * <p/>
	 * 無 | 0<br/>
	 * 惟不得低於「XX」利率加「XX」％除以「XX」| 13<br/>
	 * Codetype=lms1401s0204_rateLimitType
	 */
	@Size(max = 2)
	@Column(name = "RATELIMITTYPE", length = 2, columnDefinition = "CHAR(2)")
	private String rateLimitType;

	/**
	 * 限制條件利率
	 * <p/>
	 * 100/11/22配合國內分段利率新增<br/>
	 * 新台幣：惟不得低於「」％<br/>
	 * 美　金：S/LBOR與TAIFX差額逾「」％部分由借戶負擔 (值域 0.3％-0.5％，小數最多5位)
	 */
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "RATELIMITRATE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rateLimitRate;

	/**
	 * 下限利率
	 * <p/>
	 * 惟不得低於 | 1<br/>
	 * 孰高計收 | 2<br/>
	 * Codetype=lms1401s0204_rateLimit
	 */
	@Size(max = 1)
	@Column(name = "RATELIMIT", length = 1, columnDefinition = "CHAR(1)")
	private String rateLimit;

	/** 下限利率-利率代碼 **/
	@Size(max = 2)
	@Column(name = "RATELIMITCODE", length = 2, columnDefinition = "CHAR(2)")
	private String rateLimitCode;

	/**
	 * 下限利率-是否以借款同天期顯示文字
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 */
	@Size(max = 1)
	@Column(name = "RATELIMITSETALL", length = 1, columnDefinition = "CHAR(1)")
	private String rateLimitSetAll;

	/** 下限利率-貨幣市場利率群組 **/
	@Size(max = 120)
	@Column(name = "RATELIMITMARKET", length = 120, columnDefinition = "VARCHAR(120)")
	private String rateLimitMarket;

	/**
	 * 下限利率-加減年利率選項
	 * <p/>
	 * 加|1<br/>
	 * 減|2<br/>
	 * Codetype=lms1405s0204_count
	 */
	@Size(max = 1)
	@Column(name = "RATELIMITCOUNTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String rateLimitCountType;

	/** 下限利率-加減年利率(%) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "RATELIMITCOUNTRATE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rateLimitCountRate;

	/** 下限利率-自訂利率 **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "RATELIMITCOUNTPR", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rateLimitCountPr;

	/**
	 * 下限利率-稅負負擔選項
	 * <p/>
	 * 本行|0<br/>
	 * 借款人 | 1<br/>
	 * 無 | 2<br/>
	 * Codetype=lms1401s0204_rateTax
	 */
	@Size(max = 1)
	@Column(name = "RATELIMITTAX", length = 1, columnDefinition = "CHAR(1)")
	private String rateLimitTax;

	/** 下限利率-稅負負擔年率(%) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "RATELIMITTAXRATE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rateLimitTaxRate;

	/** 組成說明字串 **/
	@Size(max = 4096)
	@Column(name = "RATEDSCR", length = 4096, columnDefinition = "VARCHAR(4096)")
	private String rateDscr;

	/** 上傳用文字 **/
	@Size(max = 4096)
	@Column(name = "UPRATEDSCR", length = 4096, columnDefinition = "VARCHAR(4096)")
	private String upRateDscr;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 試算利率1最小值(ALL IN用) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "ALLINRATEMINBF1", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal allInRateMinBf1;

	/** 試算利率1最大值(ALL IN用) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "ALLINRATEMAXBF1", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal allInRateMaxBf1;

	/** 試算利率2最小值(ALL IN用) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "ALLINRATEMINBF2", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal allInRateMinBf2;

	/** 試算利率2最大值(ALL IN用) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "ALLINRATEMAXBF2", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal allInRateMaxBf2;

	/** 試算利率3最小值(ALL IN用) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "ALLINRATEMINBF3", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal allInRateMinBf3;

	/** 試算利率3最大值(ALL IN用) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "ALLINRATEMAXBF3", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal allInRateMaxBf3;

	/** 計算後利率最小值(ALL IN用) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "ALLINRATEMINAF", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal allInRateMinAf;

	/** 計算後利率最大值(ALL IN用) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "ALLINRATEMAXAF", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal allInRateMaxAf;

	/**
	 * 組合文字時顯示此項目(ALL IN用)
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 */
	@Size(max = 1)
	@Column(name = "ALLINRATESELALL", length = 1, columnDefinition = "CHAR(1)")
	private String allInRateSelAll;

	/**
	 * 帳務系統是否需建置下限利率
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 */
	@Size(max = 1)
	@Column(name = "RATELIMITNEEDBUILD", length = 1, columnDefinition = "VARCHAR(1)")
	private String rateLimitNeedBuild;

	/**
	 * 俟帳務系統起帳時再依客戶繳息方式建入
	 * <p/>
	 * 是|Y<br/>
	 */
	@Size(max = 1)
	@Column(name = "RATETAXCODEDECIDEFUTURE", length = 1, columnDefinition = "VARCHAR(1)")
	private String rateTaxCodeDecideFuture;

	/**
	 * 限制條件/說明
	 * <p/>
	 * 無 | 0<br/>
	 * 惟不得低於「XX」利率加「XX」％除以「XX」| 13<br/>
	 * Codetype=lms1401s0204_rateLimitType
	 */
	@Size(max = 2)
	@Column(name = "RATELIMITTYPE2", length = 2, columnDefinition = "CHAR(2)")
	private String rateLimitType2;

	/**
	 * 限制條件利率
	 * <p/>
	 * 100/11/22配合國內分段利率新增<br/>
	 * 新台幣：惟不得低於「」％<br/>
	 * 美　金：S/LBOR與TAIFX差額逾「」％部分由借戶負擔 (值域 0.3％-0.5％，小數最多5位)
	 */
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "RATELIMITRATE2", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rateLimitRate2;

	/**
	 * 下限利率
	 * <p/>
	 * 惟不得低於 | 1<br/>
	 * 孰高計收 | 2<br/>
	 * Codetype=lms1401s0204_rateLimit
	 */
	@Size(max = 1)
	@Column(name = "RATELIMIT2", length = 1, columnDefinition = "CHAR(1)")
	private String rateLimit2;

	/** 下限利率-利率代碼 **/
	@Size(max = 2)
	@Column(name = "RATELIMITCODE2", length = 2, columnDefinition = "CHAR(2)")
	private String rateLimitCode2;

	/**
	 * 下限利率-是否以借款同天期顯示文字
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 */
	@Size(max = 1)
	@Column(name = "RATELIMITSETALL2", length = 1, columnDefinition = "CHAR(1)")
	private String rateLimitSetAll2;

	/** 下限利率-貨幣市場利率群組 **/
	@Size(max = 120)
	@Column(name = "RATELIMITMARKET2", length = 120, columnDefinition = "VARCHAR(120)")
	private String rateLimitMarket2;

	/**
	 * 下限利率-加減年利率選項
	 * <p/>
	 * 加|1<br/>
	 * 減|2<br/>
	 * Codetype=lms1405s0204_count
	 */
	@Size(max = 1)
	@Column(name = "RATELIMITCOUNTTYPE2", length = 1, columnDefinition = "CHAR(1)")
	private String rateLimitCountType2;

	/** 下限利率-加減年利率(%) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "RATELIMITCOUNTRATE2", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rateLimitCountRate2;

	/** 下限利率-自訂利率 **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "RATELIMITCOUNTPR2", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rateLimitCountPr2;

	/**
	 * 下限利率-稅負負擔選項
	 * <p/>
	 * 本行|0<br/>
	 * 借款人 | 1<br/>
	 * 無 | 2<br/>
	 * Codetype=lms1401s0204_rateTax
	 */
	@Size(max = 1)
	@Column(name = "RATELIMITTAX2", length = 1, columnDefinition = "CHAR(1)")
	private String rateLimitTax2;

	/** 下限利率-稅負負擔年率(%) **/
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "RATELIMITTAXRATE2", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rateLimitTaxRate2;

    /** 不得適用本行各項優惠利率 **/
    @Size(max = 1)
    @Column(name = "INAPPLICABILITY", length = 1, columnDefinition = "CHAR(1)")
    private String inapplicability;

    /** 適用優惠利率 **/
    @Size(max = 1)
    @Column(name = "PRIMERATEPLAN", length = 1, columnDefinition = "CHAR(1)")
    private String primeRatePlan;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得序號
	 * <p/>
	 * L140M01F_rateSeq
	 */
	public Integer getRateSeq() {
		return this.rateSeq;
	}

	/**
	 * 設定序號
	 * <p/>
	 * L140M01F_rateSeq
	 **/
	public void setRateSeq(Integer value) {
		this.rateSeq = value;
	}

	/**
	 * 取得類別
	 * <p/>
	 * 100/11/24調整<br/>
	 * 1新台幣、2美金、3日幣、4歐元、5人民幣、6澳幣、7港幣、Z雜幣
	 */
	public String getRateType() {
		return this.rateType;
	}

	/**
	 * 設定類別
	 * <p/>
	 * 100/11/24調整<br/>
	 * 1新台幣、2美金、3日幣、4歐元、5人民幣、6澳幣、7港幣、Z雜幣
	 **/
	public void setRateType(String value) {
		this.rateType = value;
	}

	/**
	 * 取得段數
	 * <p/>
	 * 0.全段<br/>
	 * 1、2、3..........
	 */
	public String getSecNo() {
		return this.secNo;
	}

	/**
	 * 設定段數
	 * <p/>
	 * 0.全段<br/>
	 * 1、2、3..........
	 **/
	public void setSecNo(String value) {
		this.secNo = value;
	}

	/**
	 * 取得適用期間選項
	 * <p/>
	 * 全案 | 1<br/>
	 * 自動用日起迄月| 2<br/>
	 * YYYY-MM-DD~YYYY-MM-DD| 3"<br/>
	 * 自簽約日起迄月| 4<br/>
	 * CodeType=lms1401s0204_secNoOp
	 */
	public String getSecNoOp() {
		return this.secNoOp;
	}

	/**
	 * 設定適用期間選項
	 * <p/>
	 * 全案 | 1<br/>
	 * 自動用日起迄月| 2<br/>
	 * YYYY-MM-DD~YYYY-MM-DD| 3"<br/>
	 * 自簽約日起迄月| 4<br/>
	 * CodeType=lms1401s0204_secNoOp
	 **/
	public void setSecNoOp(String value) {
		this.secNoOp = value;
	}

	/**
	 * 取得適用期間起月
	 * <p/>
	 * 適用期間為2|4
	 */
	public Integer getSecBegMon() {
		return this.secBegMon;
	}

	/**
	 * 設定適用期間起月
	 * <p/>
	 * 適用期間為2|4
	 **/
	public void setSecBegMon(Integer value) {
		this.secBegMon = value;
	}

	/**
	 * 取得適用期間迄月
	 * <p/>
	 * 適用期間為2|4
	 */
	public Integer getSecEndMon() {
		return this.secEndMon;
	}

	/**
	 * 設定適用期間迄月
	 * <p/>
	 * 適用期間為2|4
	 **/
	public void setSecEndMon(Integer value) {
		this.secEndMon = value;
	}

	/**
	 * 取得適用期間起日
	 * <p/>
	 * 適用期間為3
	 */
	public Date getSecBegDate() {
		return this.secBegDate;
	}

	/**
	 * 設定適用期間起日
	 * <p/>
	 * 適用期間為3
	 **/
	public void setSecBegDate(Date value) {
		this.secBegDate = value;
	}

	/**
	 * 取得適用期間迄日
	 * <p/>
	 * 適用期間為3
	 */
	public Date getSecEndDate() {
		return this.secEndDate;
	}

	/**
	 * 設定適用期間迄日
	 * <p/>
	 * 適用期間為3
	 **/
	public void setSecEndDate(Date value) {
		this.secEndDate = value;
	}

	/**
	 * 取得利率基礎
	 * <p/>
	 * (可複選)<br/>
	 * XX|XX|XX<br/>
	 * 60/3 = 20
	 */
	public String getRateBase() {
		return this.rateBase;
	}

	/**
	 * 設定利率基礎
	 * <p/>
	 * (可複選)<br/>
	 * XX|XX|XX<br/>
	 * 60/3 = 20
	 **/
	public void setRateBase(String value) {
		this.rateBase = value;
	}

	/**
	 * 取得利率基礎-是否以借款同天期顯示文字
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 */
	public String getRateSetAll() {
		return this.rateSetAll;
	}

	/**
	 * 設定利率基礎-是否以借款同天期顯示文字
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 **/
	public void setRateSetAll(String value) {
		this.rateSetAll = value;
	}

	/** 取得利率基礎-貨幣市場利率群組 **/
	public String getGroupName() {
		return this.groupName;
	}

	/** 設定利率基礎-貨幣市場利率群組 **/
	public void setGroupName(String value) {
		this.groupName = value;
	}

	/** 取得利率基礎-牌告利率-最小值 **/
	public BigDecimal getTRateMin() {
		return this.tRateMin;
	}

	/** 設定利率基礎-牌告利率-最小值 **/
	public void setTRateMin(BigDecimal value) {
		this.tRateMin = value;
	}

	/** 取得利率基礎-牌告利率-最大值 **/
	public BigDecimal getTRateMax() {
		return this.tRateMax;
	}

	/** 設定利率基礎-牌告利率-最大值 **/
	public void setTRateMax(BigDecimal value) {
		this.tRateMax = value;
	}

	/** 取得利率基礎-牌告利率-最小值(修改值) **/
	public BigDecimal getCtRateMin() {
		return this.ctRateMin;
	}

	/** 設定利率基礎-牌告利率-最小值(修改值) **/
	public void setCtRateMin(BigDecimal value) {
		this.ctRateMin = value;
	}

	/** 取得利率基礎-牌告利率-最大值(修改值) **/
	public BigDecimal getCtRateMax() {
		return this.ctRateMax;
	}

	/** 設定利率基礎-牌告利率-最大值(修改值) **/
	public void setCtRateMax(BigDecimal value) {
		this.ctRateMax = value;
	}

	/** 取得利率基礎-自訂利率參考指標-指標名稱 **/
	public String getPrRate() {
		return this.prRate;
	}

	/** 設定利率基礎-自訂利率參考指標-指標名稱 **/
	public void setPrRate(String value) {
		this.prRate = value;
	}

	/**
	 * 取得利率基礎-自訂利率天期
	 * <p/>
	 * 無 | B000<br/>
	 * 借款同天期|B001<br/>
	 * 一週 | W101<br/>
	 * 二週 | W102<br/>
	 * 一個月 | M101<br/>
	 * 二個月 | M102<br/>
	 * 三個月 | M103<br/>
	 * 四個月 | M104<br/>
	 * 五個月 | M105<br/>
	 * 六個月 | M106<br/>
	 * 七個月 | M107<br/>
	 * 八個月 | M108<br/>
	 * 九個月 | M109<br/>
	 * 十個月 | M110<br/>
	 * 十一個月 | M111<br/>
	 * 十二個月 | M112<br/>
	 * 二年期 | Y102<br/>
	 * 三年期 | Y103<br/>
	 * 四年期 | Y104<br/>
	 * 五年期 | Y105<br/>
	 * 一個月平均 | M301<br/>
	 * 二個月平均 | M302<br/>
	 * 三個月平均 | M303<br/>
	 * 四個月平均 | M304<br/>
	 * 五個月平均 | M305<br/>
	 * 六個月平均 | M306<br/>
	 * 七個月平均 | M307<br/>
	 * 八個月平均 | M308<br/>
	 * 九個月平均 | M309<br/>
	 * 十個月平均 | M310<br/>
	 * 十一個月平均 | M311<br/>
	 * 十二個月平均 | M312<br/>
	 * 二年期平均 | Y302<br/>
	 * 三年期平均 | Y303<br/>
	 * 四年期平均 | Y304<br/>
	 * 五年期平均 | Y305<br/>
	 * Codetype=lms1401s0204_ratePeriod
	 */
	public String getRatePeriod() {
		return this.ratePeriod;
	}

	/**
	 * 設定利率基礎-自訂利率天期
	 * <p/>
	 * 無 | B000<br/>
	 * 借款同天期|B001<br/>
	 * 一週 | W101<br/>
	 * 二週 | W102<br/>
	 * 一個月 | M101<br/>
	 * 二個月 | M102<br/>
	 * 三個月 | M103<br/>
	 * 四個月 | M104<br/>
	 * 五個月 | M105<br/>
	 * 六個月 | M106<br/>
	 * 七個月 | M107<br/>
	 * 八個月 | M108<br/>
	 * 九個月 | M109<br/>
	 * 十個月 | M110<br/>
	 * 十一個月 | M111<br/>
	 * 十二個月 | M112<br/>
	 * 二年期 | Y102<br/>
	 * 三年期 | Y103<br/>
	 * 四年期 | Y104<br/>
	 * 五年期 | Y105<br/>
	 * 一個月平均 | M301<br/>
	 * 二個月平均 | M302<br/>
	 * 三個月平均 | M303<br/>
	 * 四個月平均 | M304<br/>
	 * 五個月平均 | M305<br/>
	 * 六個月平均 | M306<br/>
	 * 七個月平均 | M307<br/>
	 * 八個月平均 | M308<br/>
	 * 九個月平均 | M309<br/>
	 * 十個月平均 | M310<br/>
	 * 十一個月平均 | M311<br/>
	 * 十二個月平均 | M312<br/>
	 * 二年期平均 | Y302<br/>
	 * 三年期平均 | Y303<br/>
	 * 四年期平均 | Y304<br/>
	 * 五年期平均 | Y305<br/>
	 * Codetype=lms1401s0204_ratePeriod
	 **/
	public void setRatePeriod(String value) {
		this.ratePeriod = value;
	}

	/**
	 * 取得利率基礎-加減年利率選項
	 * <p/>
	 * 加|1<br/>
	 * 減|2<br/>
	 * Codetype=lms1405s0204_count
	 */
	public String getDisYearOp() {
		return this.disYearOp;
	}

	/**
	 * 設定利率基礎-加減年利率選項
	 * <p/>
	 * 加|1<br/>
	 * 減|2<br/>
	 * Codetype=lms1405s0204_count
	 **/
	public void setDisYearOp(String value) {
		this.disYearOp = value;
	}

	/** 取得利率基礎-加減年利率(%) **/
	public BigDecimal getDisYearRate() {
		return this.disYearRate;
	}

	/** 設定利率基礎-加減年利率(%) **/
	public void setDisYearRate(BigDecimal value) {
		this.disYearRate = value;
	}

	/** 取得利率基礎-敘做利率最小值(%) **/
	public BigDecimal getReRateMin() {
		return this.reRateMin;
	}

	/** 設定利率基礎-敘做利率最小值(%) **/
	public void setReRateMin(BigDecimal value) {
		this.reRateMin = value;
	}

	/** 取得利率基礎-敘做利率最大值(%) **/
	public BigDecimal getReRateMax() {
		return this.reRateMax;
	}

	/** 設定利率基礎-敘做利率最大值(%) **/
	public void setReRateMax(BigDecimal value) {
		this.reRateMax = value;
	}

	/**
	 * 取得利率基礎-敘做利率(%) 組合文字時顯示此項目
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 */
	public String getReRateSelAll() {
		return this.reRateSelAll;
	}

	/**
	 * 設定利率基礎-敘做利率(%) 組合文字時顯示此項目
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 **/
	public void setReRateSelAll(String value) {
		this.reRateSelAll = value;
	}

	/** 取得利率基礎-固定利率(%) **/
	public BigDecimal getAttRate() {
		return this.attRate;
	}

	/** 設定利率基礎-固定利率(%) **/
	public void setAttRate(BigDecimal value) {
		this.attRate = value;
	}

	/** 取得利率基礎-其他(自行輸入) **/
	public String getOtherRateDrc() {
		return this.otherRateDrc;
	}

	/** 設定利率基礎-其他(自行輸入) **/
	public void setOtherRateDrc(String value) {
		this.otherRateDrc = value;
	}

	/**
	 * 取得自訂利率參考指標U01、U02專用-與借款人敘做之市場利率代碼
	 * <p/>
	 * SIBOR | 1<br/>
	 * LIBOR | 2
	 */
	public String getUsdMarket() {
		return this.usdMarket;
	}

	/**
	 * 設定自訂利率參考指標U01、U02專用-與借款人敘做之市場利率代碼
	 * <p/>
	 * SIBOR | 1<br/>
	 * LIBOR | 2
	 **/
	public void setUsdMarket(String value) {
		this.usdMarket = value;
	}

	/**
	 * 取得自訂利率參考指標U01、U02專用-是否以借款同天期顯示
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 */
	public String getUsdSetAll() {
		return this.usdSetAll;
	}

	/**
	 * 設定自訂利率參考指標U01、U02專用-是否以借款同天期顯示
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 **/
	public void setUsdSetAll(String value) {
		this.usdSetAll = value;
	}

	/**
	 * 取得自訂利率參考指標U01、U02專用-(A)與借款人敘作之市場利率 *
	 * <p/>
	 * SELECT * FROM ( SELECT LNF070_NAME ,RIGHT(LNF070_CODE,2) AS LNF070_CODE
	 * FROM LN.LNF070 WHERE LEFT(LNF070_CODE,2)='99' AND LNF070_TYPE =
	 * 'LNF030-INT-CODE' ) A <br/>
	 * 複選 SJ|SH|..|..<br/>
	 * 3*10 =30<br/>
	 * SJ:XXXXXXXX, <br/>
	 * SJ:XXXXXXXX, ...........
	 * **/
	public String getUsdMarketRate() {
		return this.usdMarketRate;
	}

	/**
	 * 設定自訂利率參考指標U01、U02專用-(A)與借款人敘作之市場利率 *
	 * <p/>
	 * SELECT * FROM ( SELECT LNF070_NAME ,RIGHT(LNF070_CODE,2) AS LNF070_CODE
	 * FROM LN.LNF070 WHERE LEFT(LNF070_CODE,2)='99' AND LNF070_TYPE =
	 * 'LNF030-INT-CODE' ) A <br/>
	 * 複選 SJ|SH|..|..<br/>
	 * 3*10 =30<br/>
	 * SJ:XXXXXXXX, <br/>
	 * SJ:XXXXXXXX, ...........
	 * **/
	public void setUsdMarketRate(String value) {
		this.usdMarketRate = value;
	}

	/**
	 * 取得自訂利率參考指標U01、U02專用-(B)稅賦負擔
	 * <p/>
	 * 本行|0<br/>
	 * 借款人 | 1<br/>
	 * 無 | 2<br/>
	 * Codetype=lms1401s0204_rateTax
	 */
	public String getUsdRateTax() {
		return this.usdRateTax;
	}

	/**
	 * 設定自訂利率參考指標U01、U02專用-(B)稅賦負擔
	 * <p/>
	 * 本行|0<br/>
	 * 借款人 | 1<br/>
	 * 無 | 2<br/>
	 * Codetype=lms1401s0204_rateTax
	 **/
	public void setUsdRateTax(String value) {
		this.usdRateTax = value;
	}

	/** 取得自訂利率參考指標U01、U02專用-(C)內含 **/
	public BigDecimal getUsdInsideRate() {
		return this.usdInsideRate;
	}

	/** 設定自訂利率參考指標U01、U02專用-(C)內含 **/
	public void setUsdInsideRate(BigDecimal value) {
		this.usdInsideRate = value;
	}

	/** 取得自訂利率參考指標U01、U02專用-(D)S/LBOR與TAIFX差額逾 **/
	public BigDecimal getUsdDesRate() {
		return this.usdDesRate;
	}

	/** 設定自訂利率參考指標U01、U02專用-(D)S/LBOR與TAIFX差額逾 **/
	public void setUsdDesRate(BigDecimal value) {
		this.usdDesRate = value;
	}

	/**
	 * 取得利率補充說明
	 * <p/>
	 * 100/11/22配合國內分段利率新增
	 */
	public String getRateMemo() {
		return this.rateMemo;
	}

	/**
	 * 設定利率補充說明
	 * <p/>
	 * 100/11/22配合國內分段利率新增
	 **/
	public void setRateMemo(String value) {
		this.rateMemo = value;
	}

	/**
	 * 取得利率方式
	 * <p/>
	 * 1固定利率<br/>
	 * 2機動利率<br/>
	 * 3定期浮動<br/>
	 * ※若選擇「機動利率」或「定期浮動」則不組入文字串中<br/>
	 * Codetype=lms1405s0204_rateKind
	 */
	public String getRateKind() {
		return this.rateKind;
	}

	/**
	 * 設定利率方式
	 * <p/>
	 * 1固定利率<br/>
	 * 2機動利率<br/>
	 * 3定期浮動<br/>
	 * ※若選擇「機動利率」或「定期浮動」則不組入文字串中<br/>
	 * Codetype=lms1405s0204_rateKind
	 **/
	public void setRateKind(String value) {
		this.rateKind = value;
	}

	/**
	 * 取得收息方式
	 * <p/>
	 * 按月收息 | 1<br/>
	 * 每三個月收息乙次 | 2<br/>
	 * 每半年收息乙次 | 3<br/>
	 * 按年收息 | 4<br/>
	 * 本息併付 | 5<br/>
	 * 期付金 | 6<br/>
	 * 不計息(含保證、承兌) | 7<br/>
	 * 預扣利息 | 8<br/>
	 * 每三或六個月收息乙次|9<br/>
	 * CodeType=lms1401s0204_rateGetInt
	 */
	public String getRateGetInt() {
		return this.rateGetInt;
	}

	/**
	 * 設定收息方式
	 * <p/>
	 * 按月收息 | 1<br/>
	 * 每三個月收息乙次 | 2<br/>
	 * 每半年收息乙次 | 3<br/>
	 * 按年收息 | 4<br/>
	 * 本息併付 | 5<br/>
	 * 期付金 | 6<br/>
	 * 不計息(含保證、承兌) | 7<br/>
	 * 預扣利息 | 8<br/>
	 * 每三或六個月收息乙次|9<br/>
	 * CodeType=lms1401s0204_rateGetInt
	 **/
	public void setRateGetInt(String value) {
		this.rateGetInt = value;
	}

	/**
	 * 取得利率變動方式
	 * <p/>
	 * 月 | M<br/>
	 * 年 | Y<br/>
	 * codeType=lms1401s0204_rateChgKind
	 */
	public String getRateChgKind() {
		return this.rateChgKind;
	}

	/**
	 * 設定利率變動方式
	 * <p/>
	 * 月 | M<br/>
	 * 年 | Y<br/>
	 * codeType=lms1401s0204_rateChgKind
	 **/
	public void setRateChgKind(String value) {
		this.rateChgKind = value;
	}

	/**
	 * 取得變動週期
	 * <p/>
	 * 1,2,3....
	 */
	public Integer getRateChg1() {
		return this.rateChg1;
	}

	/**
	 * 設定變動週期
	 * <p/>
	 * 1,2,3....
	 **/
	public void setRateChg1(Integer value) {
		this.rateChg1 = value;
	}

	/**
	 * 取得指定下次變動日期
	 * <p/>
	 * 100/11/22配合國內分段利率新增
	 */
	public Date getRateChgDate() {
		return this.rateChgDate;
	}

	/**
	 * 設定指定下次變動日期
	 * <p/>
	 * 100/11/22配合國內分段利率新增
	 **/
	public void setRateChgDate(Date value) {
		this.rateChgDate = value;
	}

	/** 取得聯貸案專用說明 **/
	public String getUionMemo() {
		return this.uionMemo;
	}

	/** 設定聯貸案專用說明 **/
	public void setUionMemo(String value) {
		this.uionMemo = value;
	}

	/**
	 * 取得稅負洽收
	 * <p/>
	 * 本行|0<br/>
	 * 借款人 | 1<br/>
	 * 無 | 2<br/>
	 * Codetype=lms1401s0204_rateTax
	 */
	public String getRateTax() {
		return this.rateTax;
	}

	/**
	 * 設定稅負洽收
	 * <p/>
	 * 本行|0<br/>
	 * 借款人 | 1<br/>
	 * 無 | 2<br/>
	 * Codetype=lms1401s0204_rateTax
	 **/
	public void setRateTax(String value) {
		this.rateTax = value;
	}

	/**
	 * 取得稅負洽收-扣稅負擔
	 * <p/>
	 * ※稅負由「借款人」負擔時輸入
	 */
	public BigDecimal getRateTaxCode() {
		return this.rateTaxCode;
	}

	/**
	 * 設定稅負洽收-扣稅負擔
	 * <p/>
	 * ※稅負由「借款人」負擔時輸入
	 **/
	public void setRateTaxCode(BigDecimal value) {
		this.rateTaxCode = value;
	}

	/**
	 * 取得限制條件/說明
	 * <p/>
	 * 無 | 0<br/>
	 * 惟不得低於「XX」利率加「XX」％除以「XX」| 13<br/>
	 * Codetype=lms1401s0204_rateLimitType
	 */
	public String getRateLimitType() {
		return this.rateLimitType;
	}

	/**
	 * 設定限制條件/說明
	 * <p/>
	 * 無 | 0<br/>
	 * 惟不得低於「XX」利率加「XX」％除以「XX」| 13<br/>
	 * Codetype=lms1401s0204_rateLimitType
	 **/
	public void setRateLimitType(String value) {
		this.rateLimitType = value;
	}

	/**
	 * 取得限制條件利率
	 * <p/>
	 * 100/11/22配合國內分段利率新增<br/>
	 * 新台幣：惟不得低於「」％<br/>
	 * 美　金：S/LBOR與TAIFX差額逾「」％部分由借戶負擔 (值域 0.3％-0.5％，小數最多5位)
	 */
	public BigDecimal getRateLimitRate() {
		return this.rateLimitRate;
	}

	/**
	 * 設定限制條件利率
	 * <p/>
	 * 100/11/22配合國內分段利率新增<br/>
	 * 新台幣：惟不得低於「」％<br/>
	 * 美　金：S/LBOR與TAIFX差額逾「」％部分由借戶負擔 (值域 0.3％-0.5％，小數最多5位)
	 **/
	public void setRateLimitRate(BigDecimal value) {
		this.rateLimitRate = value;
	}

	/**
	 * 取得下限利率
	 * <p/>
	 * 惟不得低於 | 1<br/>
	 * 孰高計收 | 2<br/>
	 * Codetype=lms1401s0204_rateLimit
	 */
	public String getRateLimit() {
		return this.rateLimit;
	}

	/**
	 * 設定下限利率
	 * <p/>
	 * 惟不得低於 | 1<br/>
	 * 孰高計收 | 2<br/>
	 * Codetype=lms1401s0204_rateLimit
	 **/
	public void setRateLimit(String value) {
		this.rateLimit = value;
	}

	/** 取得下限利率-利率代碼 **/
	public String getRateLimitCode() {
		return this.rateLimitCode;
	}

	/** 設定下限利率-利率代碼 **/
	public void setRateLimitCode(String value) {
		this.rateLimitCode = value;
	}

	/**
	 * 取得下限利率-是否以借款同天期顯示文字
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 */
	public String getRateLimitSetAll() {
		return this.rateLimitSetAll;
	}

	/**
	 * 設定下限利率-是否以借款同天期顯示文字
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 **/
	public void setRateLimitSetAll(String value) {
		this.rateLimitSetAll = value;
	}

	/** 取得下限利率-貨幣市場利率群組 **/
	public String getRateLimitMarket() {
		return this.rateLimitMarket;
	}

	/** 設定下限利率-貨幣市場利率群組 **/
	public void setRateLimitMarket(String value) {
		this.rateLimitMarket = value;
	}

	/**
	 * 取得下限利率-加減年利率選項
	 * <p/>
	 * 加|1<br/>
	 * 減|2<br/>
	 * Codetype=lms1405s0204_count
	 */
	public String getRateLimitCountType() {
		return this.rateLimitCountType;
	}

	/**
	 * 設定下限利率-加減年利率選項
	 * <p/>
	 * 加|1<br/>
	 * 減|2<br/>
	 * Codetype=lms1405s0204_count
	 **/
	public void setRateLimitCountType(String value) {
		this.rateLimitCountType = value;
	}

	/** 取得下限利率-加減年利率(%) **/
	public BigDecimal getRateLimitCountRate() {
		return this.rateLimitCountRate;
	}

	/** 設定下限利率-加減年利率(%) **/
	public void setRateLimitCountRate(BigDecimal value) {
		this.rateLimitCountRate = value;
	}

	/** 取得下限利率-自訂利率 **/
	public BigDecimal getRateLimitCountPr() {
		return this.rateLimitCountPr;
	}

	/** 設定下限利率-自訂利率 **/
	public void setRateLimitCountPr(BigDecimal value) {
		this.rateLimitCountPr = value;
	}

	/**
	 * 取得下限利率-稅負負擔選項
	 * <p/>
	 * 本行|0<br/>
	 * 借款人 | 1<br/>
	 * 無 | 2<br/>
	 * Codetype=lms1401s0204_rateTax
	 */
	public String getRateLimitTax() {
		return this.rateLimitTax;
	}

	/**
	 * 設定下限利率-稅負負擔選項
	 * <p/>
	 * 本行|0<br/>
	 * 借款人 | 1<br/>
	 * 無 | 2<br/>
	 * Codetype=lms1401s0204_rateTax
	 **/
	public void setRateLimitTax(String value) {
		this.rateLimitTax = value;
	}

	/** 取得下限利率-稅負負擔年率(%) **/
	public BigDecimal getRateLimitTaxRate() {
		return this.rateLimitTaxRate;
	}

	/** 設定下限利率-稅負負擔年率(%) **/
	public void setRateLimitTaxRate(BigDecimal value) {
		this.rateLimitTaxRate = value;
	}

	/** 取得組成說明字串 **/
	public String getRateDscr() {
		return this.rateDscr;
	}

	/** 設定組成說明字串 **/
	public void setRateDscr(String value) {
		this.rateDscr = value;
	}

	/** 取得上傳用文字 **/
	public String getUpRateDscr() {
		return this.upRateDscr;
	}

	/** 設定上傳用文字 **/
	public void setUpRateDscr(String value) {
		this.upRateDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 設定試算利率1最小值(ALL IN用) **/
	public void setAllInRateMinBf1(BigDecimal allInRateMinBf1) {
		this.allInRateMinBf1 = allInRateMinBf1;
	}

	/** 取得試算利率1最小值(ALL IN用) **/
	public BigDecimal getAllInRateMinBf1() {
		return allInRateMinBf1;
	}

	/** 設定試算利率1最大值(ALL IN用) **/
	public void setAllInRateMaxBf1(BigDecimal allInRateMaxBf1) {
		this.allInRateMaxBf1 = allInRateMaxBf1;
	}

	/** 取得試算利率1最大值(ALL IN用) **/
	public BigDecimal getAllInRateMaxBf1() {
		return allInRateMaxBf1;
	}

	/** 設定試算利率2最小值(ALL IN用) **/
	public void setAllInRateMinBf2(BigDecimal allInRateMinBf2) {
		this.allInRateMinBf2 = allInRateMinBf2;
	}

	/** 取得試算利率2最小值(ALL IN用) **/
	public BigDecimal getAllInRateMinBf2() {
		return allInRateMinBf2;
	}

	/** 設定試算利率2最大值(ALL IN用) **/
	public void setAllInRateMaxBf2(BigDecimal allInRateMaxBf2) {
		this.allInRateMaxBf2 = allInRateMaxBf2;
	}

	/** 取得試算利率1最大值(ALL IN用) **/
	public BigDecimal getAllInRateMaxBf2() {
		return allInRateMaxBf2;
	}

	/** 設定試算利率3最小值(ALL IN用) **/
	public void setAllInRateMinBf3(BigDecimal allInRateMinBf3) {
		this.allInRateMinBf3 = allInRateMinBf3;
	}

	/** 取得試算利率3最小值(ALL IN用) **/
	public BigDecimal getAllInRateMinBf3() {
		return allInRateMinBf3;
	}

	/** 設定試算利率2最大值(ALL IN用) **/
	public void setAllInRateMaxBf3(BigDecimal allInRateMaxBf3) {
		this.allInRateMaxBf3 = allInRateMaxBf3;
	}

	/** 取得試算利率1最大值(ALL IN用) **/
	public BigDecimal getAllInRateMaxBf3() {
		return allInRateMaxBf3;
	}

	/** 設定計算後利率最小值(ALL IN用) **/
	public void setAllInRateMinAf(BigDecimal allInRateMinAf) {
		this.allInRateMinAf = allInRateMinAf;
	}

	/** 取得計算後利率最小值(ALL IN用) **/
	public BigDecimal getAllInRateMinAf() {
		return allInRateMinAf;
	}

	/** 設定計算後利率最大值(ALL IN用) **/
	public void setAllInRateMaxAf(BigDecimal allInRateMaxAf) {
		this.allInRateMaxAf = allInRateMaxAf;
	}

	/** 取得計算後利率最大值(ALL IN用) **/
	public BigDecimal getAllInRateMaxAf() {
		return allInRateMaxAf;
	}

	/** 設定組合文字時顯示此項目(ALL IN用) **/
	public void setAllInRateSelAll(String allInRateSelAll) {
		this.allInRateSelAll = allInRateSelAll;
	}

	/** 取得組合文字時顯示此項目(ALL IN用) **/
	public String getAllInRateSelAll() {
		return allInRateSelAll;
	}

	/** 設定帳務系統是否需建置下限利率 **/
	public void setRateLimitNeedBuild(String rateLimitNeedBuild) {
		this.rateLimitNeedBuild = rateLimitNeedBuild;
	}

	/** 取得帳務系統是否需建置下限利率 **/
	public String getRateLimitNeedBuild() {
		return rateLimitNeedBuild;
	}

	/** 設定俟帳務系統起帳時再依客戶繳息方式建入 **/
	public void setRateTaxCodeDecideFuture(String rateTaxCodeDecideFuture) {
		this.rateTaxCodeDecideFuture = rateTaxCodeDecideFuture;
	}

	/** 取得俟帳務系統起帳時再依客戶繳息方式建入 **/
	public String getRateTaxCodeDecideFuture() {
		return rateTaxCodeDecideFuture;
	}

	/**
	 * 取得限制條件/說明
	 * <p/>
	 * 無 | 0<br/>
	 * 惟不得低於「XX」利率加「XX」％除以「XX」| 13<br/>
	 * Codetype=lms1401s0204_rateLimitType
	 */
	public String getRateLimitType2() {
		return this.rateLimitType2;
	}

	/**
	 * 設定限制條件/說明
	 * <p/>
	 * 無 | 0<br/>
	 * 惟不得低於「XX」利率加「XX」％除以「XX」| 13<br/>
	 * Codetype=lms1401s0204_rateLimitType
	 **/
	public void setRateLimitType2(String value) {
		this.rateLimitType2 = value;
	}

	/**
	 * 取得限制條件利率
	 * <p/>
	 * 100/11/22配合國內分段利率新增<br/>
	 * 新台幣：惟不得低於「」％<br/>
	 * 美　金：S/LBOR與TAIFX差額逾「」％部分由借戶負擔 (值域 0.3％-0.5％，小數最多5位)
	 */
	public BigDecimal getRateLimitRate2() {
		return this.rateLimitRate2;
	}

	/**
	 * 設定限制條件利率
	 * <p/>
	 * 100/11/22配合國內分段利率新增<br/>
	 * 新台幣：惟不得低於「」％<br/>
	 * 美　金：S/LBOR與TAIFX差額逾「」％部分由借戶負擔 (值域 0.3％-0.5％，小數最多5位)
	 **/
	public void setRateLimitRate2(BigDecimal value) {
		this.rateLimitRate2 = value;
	}

	/**
	 * 取得下限利率
	 * <p/>
	 * 惟不得低於 | 1<br/>
	 * 孰高計收 | 2<br/>
	 * Codetype=lms1401s0204_rateLimit
	 */
	public String getRateLimit2() {
		return this.rateLimit2;
	}

	/**
	 * 設定下限利率
	 * <p/>
	 * 惟不得低於 | 1<br/>
	 * 孰高計收 | 2<br/>
	 * Codetype=lms1401s0204_rateLimit
	 **/
	public void setRateLimit2(String value) {
		this.rateLimit2 = value;
	}

	/** 取得下限利率-利率代碼 **/
	public String getRateLimitCode2() {
		return this.rateLimitCode2;
	}

	/** 設定下限利率-利率代碼 **/
	public void setRateLimitCode2(String value) {
		this.rateLimitCode2 = value;
	}

	/**
	 * 取得下限利率-是否以借款同天期顯示文字
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 */
	public String getRateLimitSetAll2() {
		return this.rateLimitSetAll2;
	}

	/**
	 * 設定下限利率-是否以借款同天期顯示文字
	 * <p/>
	 * 是|Y<br/>
	 * 否|N<br/>
	 * Codetype =Common_YesNo
	 **/
	public void setRateLimitSetAll2(String value) {
		this.rateLimitSetAll2 = value;
	}

	/** 取得下限利率-貨幣市場利率群組 **/
	public String getRateLimitMarket2() {
		return this.rateLimitMarket2;
	}

	/** 設定下限利率-貨幣市場利率群組 **/
	public void setRateLimitMarket2(String value) {
		this.rateLimitMarket2 = value;
	}

	/**
	 * 取得下限利率-加減年利率選項
	 * <p/>
	 * 加|1<br/>
	 * 減|2<br/>
	 * Codetype=lms1405s0204_count
	 */
	public String getRateLimitCountType2() {
		return this.rateLimitCountType2;
	}

	/**
	 * 設定下限利率-加減年利率選項
	 * <p/>
	 * 加|1<br/>
	 * 減|2<br/>
	 * Codetype=lms1405s0204_count
	 **/
	public void setRateLimitCountType2(String value) {
		this.rateLimitCountType2 = value;
	}

	/** 取得下限利率-加減年利率(%) **/
	public BigDecimal getRateLimitCountRate2() {
		return this.rateLimitCountRate2;
	}

	/** 設定下限利率-加減年利率(%) **/
	public void setRateLimitCountRate2(BigDecimal value) {
		this.rateLimitCountRate2 = value;
	}

	/** 取得下限利率-自訂利率 **/
	public BigDecimal getRateLimitCountPr2() {
		return this.rateLimitCountPr2;
	}

	/** 設定下限利率-自訂利率 **/
	public void setRateLimitCountPr2(BigDecimal value) {
		this.rateLimitCountPr2 = value;
	}

	/**
	 * 取得下限利率-稅負負擔選項
	 * <p/>
	 * 本行|0<br/>
	 * 借款人 | 1<br/>
	 * 無 | 2<br/>
	 * Codetype=lms1401s0204_rateTax
	 */
	public String getRateLimitTax2() {
		return this.rateLimitTax2;
	}

	/**
	 * 設定下限利率-稅負負擔選項
	 * <p/>
	 * 本行|0<br/>
	 * 借款人 | 1<br/>
	 * 無 | 2<br/>
	 * Codetype=lms1401s0204_rateTax
	 **/
	public void setRateLimitTax2(String value) {
		this.rateLimitTax2 = value;
	}

	/** 取得下限利率-稅負負擔年率(%) **/
	public BigDecimal getRateLimitTaxRate2() {
		return this.rateLimitTaxRate2;
	}

	/** 設定下限利率-稅負負擔年率(%) **/
	public void setRateLimitTaxRate2(BigDecimal value) {
		this.rateLimitTaxRate2 = value;
	}

	/** 取得不得適用本行各項優惠利率 **/
	public String getInapplicability() {
		return this.inapplicability;
	}

	/** 設定不得適用本行各項優惠利率 **/
	public void setInapplicability(String value) {
		this.inapplicability = value;
	}

	/** 取得適用優惠利率 **/
	public String getPrimeRatePlan() {
		return this.primeRatePlan;
	}

	/** 設定適用優惠利率 **/
	public void setPrimeRatePlan(String value) {
		this.primeRatePlan = value;
	}
}
