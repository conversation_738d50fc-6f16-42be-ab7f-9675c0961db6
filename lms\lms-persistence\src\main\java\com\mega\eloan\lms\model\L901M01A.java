/* 
 * L901M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 動用審核表稽核項目 **/
@Entity
@Table(name = "L901M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L901M01A extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 分行別代碼 **/
	@Column(name = "BRANCHID", length = 3, columnDefinition = "CHAR(3)")
	private String branchId;

	/**
	 * 查核項目類別
	 * <p/>
	 * 1.全行共同項目(總行維護)<br/>
	 * ※918授管處<br/>
	 * 2.當地特殊規定項目(海外各分行自行維護) <br/>
	 * 101/09/07新增 <br/>
	 * 3.國內企金(總行維護) <br/>
	 * 4.國內個金(總行維護)
	 */
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String itemType;

	/**
	 * 語言別
	 * <p/>
	 * 101/02/02新增<br/>
	 * zh_TW: 繁體中文<br/>
	 * zh_CN: 簡體中文 <br/>
	 * en_US: 英文
	 */
	@Column(name = "LOCALE", length = 5, columnDefinition = "CHAR(5)")
	private String locale;

	/**
	 * 查核項目序號
	 * <p/>
	 * 使用者可自行指定顯示順序
	 */
	@Column(name = "ITEMSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer itemSeq;

	/**
	 * 查核項目內容<p/>
	 * 300個全型字
	 */
	@Column(name="ITEMCONTENT", length=900, columnDefinition="VARCHAR(1800)")
	private String itemContent;

	/**
	 * 備註
	 * <p/>
	 * 64個全型字
	 */
	@Column(name = "ITEMMEMO", length = 192, columnDefinition = "VARCHAR(192)")
	private String itemMemo;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得分行別代碼 **/
	public String getBranchId() {
		return this.branchId;
	}

	/** 設定分行別代碼 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/**
	 * 取得查核項目類別
	 * <p/>
	 * 1.全行共同項目(總行維護)<br/>
	 * ※918授管處<br/>
	 * 2.當地特殊規定項目(海外各分行自行維護) <br/>
	 * 101/09/07新增 <br/>
	 * 3.國內企金(總行維護) <br/>
	 * 4.國內個金(總行維護)
	 */
	public String getItemType() {
		return this.itemType;
	}

	/**
	 * 設定查核項目類別
	 * <p/>
	 * 1.全行共同項目(總行維護)<br/>
	 * ※918授管處<br/>
	 * 2.當地特殊規定項目(海外各分行自行維護) <br/>
	 * 101/09/07新增 <br/>
	 * 3.國內企金(總行維護) <br/>
	 * 4.國內個金(總行維護)
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/**
	 * 取得語言別
	 * <p/>
	 * 101/02/02新增<br/>
	 * zh_TW: 繁體中文<br/>
	 * zh_CN: 簡體中文 <br/>
	 * en_US: 英文
	 */
	public String getLocale() {
		return this.locale;
	}

	/**
	 * 設定語言別
	 * <p/>
	 * 101/02/02新增<br/>
	 * zh_TW: 繁體中文<br/>
	 * zh_CN: 簡體中文 <br/>
	 * en_US: 英文
	 **/
	public void setLocale(String value) {
		this.locale = value;
	}

	/**
	 * 取得查核項目序號
	 * <p/>
	 * 使用者可自行指定顯示順序
	 */
	public Integer getItemSeq() {
		return this.itemSeq;
	}

	/**
	 * 設定查核項目序號
	 * <p/>
	 * 使用者可自行指定顯示順序
	 **/
	public void setItemSeq(Integer value) {
		this.itemSeq = value;
	}

	/**
	 * 取得查核項目內容<p/>
	 * 300個全型字
	 */
	public String getItemContent() {
		return this.itemContent;
	}
	/**
	 *  設定查核項目內容<p/>
	 *  300個全型字
	 **/
	public void setItemContent(String value) {
		this.itemContent = value;
	}

	/**
	 * 取得備註
	 * <p/>
	 * 64個全型字
	 */
	public String getItemMemo() {
		return this.itemMemo;
	}

	/**
	 * 設定備註
	 * <p/>
	 * 64個全型字
	 **/
	public void setItemMemo(String value) {
		this.itemMemo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
