
var initDfd = $.Deferred(), inits = {
    fhandle: "lms8500m01formhandler",
    ghandle: "lms8500gridhandler"
};

// 該交易自有EVENT*************************************************************************
function initEvent(){
	
	// 上傳EXCEL功能
	$("#importBisSelfCapitalExl").click(function(){
		// J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
		$("#uploadBisFile").val('');
		var limitFileSize = 3145728*500;   // 3M * 100 = 300M
		var s = $.extend({
			handler: 'lms8500m01fileuploadhandler',
			fieldId: "uploadBisFile",
			title: i18n && i18n.def.insertfile || "請選擇附加檔案",
			fileCheck: ['xls'],
			limitSize: limitFileSize,
			successMsg: false,
			success: function(){
			},
			data: {
				fileSize: limitFileSize,
				mainId : "",
				deleteDup : true,
				changeUploadName: "uploadBisFile.xlsx"
			}
		}, s);
		
		$("#importByExl_bisFile").thickbox({ // 使用選取的內容進行彈窗
			title : "引進BIS自有資本與風險性資產",
			width : 500,
			height : 200,
			modal : true,
			i18n:i18n.def,
			buttons: (function(){
				var b = {};
				b['引進'] = function(){
					
					$.capFileUpload({
						handler: s.handler,
						fileCheck: s.fileCheck,
						fileElementId: s.fieldId,
						successMsg: s.successMsg,
						limitSize: limitFileSize,
						data: $.extend({
							fieldId: "uploadBisFile",
							
						}, s.data || {}),
						success: function(json){
							$.thickbox.close();
							if(json.errorMsg != ""){
								CommonAPI.showErrorMessage("引進失敗:"+json.errorMsg); 
							}else{
								$("#mainPanel").find("#lmsBisSelfCapital").val(json.lmsBisSelfCapital);
								$("#mainPanel").find("#lmsBisMegaRwa").val(json.lmsBisMegaRwa);
								API.showPopMessage("引進成功"); 
							}    
						}
					});
				};
				b[i18n && i18n.def.cancel || "取消"] = function(){
					$.thickbox.close();
				};
				return b;
			})()
		});	 
	})
	
}

// 共用功能EVENT*************************************************************************
function initCommonEvent(){
	
	// 設定按鈕事件
	var btn = $("#buttonPanel");
	btn.find("#btnSave").click(function(showMsg){
		// 純儲存才show message
		saveData(true);
	}).end().find("#btnSend").click(function(){
		// 傳送並儲存不show message
		saveData(false, sendBoss);	
	}).end().find("#btnCheck").click(function(){
		openCheck();
	});
	
}

// 共用功能function*************************************************************************
function queryForm(){
	$.ajax({
		handler: inits.fhandle,
		data: {// 把資料轉成json
			formAction: "queryL850m01a",
			oid: responseJSON.oid
		},
		success: function(json){
			var $form = $("#mainPanel");
			$form.injectData(json);
		}
	});
}

// 驗證readOnly狀態
function checkReadonly(){
	var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
	if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
		return true;
	}
	return false;
}

// 儲存的動作
function saveData(showMsg, tofn){
	var $form = $("#mainPanel");
	
	if ($form.valid()) {
		$.ajax({
			handler: inits.fhandle,
			data: {// 把資料轉成json
				formAction: "saveL850m01a",
				oid: responseJSON.oid,
				page: responseJSON.page,
				txCode: responseJSON.txCode,
				showMsg: showMsg
			},
			success: function(obj){
				// 資料回寫回畫面上
				queryForm();
				
				// 儲存有成功後，有後續要執行的method，就執行
				if(tofn){
					tofn();
				}
			}
		});
		
	}
}

// 檢查陣列內容是否重複
function checkArrayRepeat(arrVal){
	var newArray = [];
	for (var i = arrVal.length; i--;) {
		var val = arrVal[i];
		if ($.inArray(val, newArray) == -1) {
			newArray.push(val);
		}
		else {
			return true;
		}
	}
	return false;
}

// 流程EVENT*************************************************************************
function initFlowEvent(){
	
	// 呈主管覆核 選授信主管人數
	$("#numPerson").change(function(){
		$('#bossItem').empty();
		var value = $(this).val();
		if (value) {
			var html = '';
			for (var i = 1; i <= value; i++) {
				var name = 'boss' + i;
				html += i + '. '
				// || '授信主管'
				html += '<select id="' + name + '" name="boss"' +
				'" class="required" CommonManager="kind:2;type:2" />';
				html += '<br/>';
			}
			$('#bossItem').append(html).find('select').each(function(){
				$(this).setItems({
					item: item,
					format: "{value} {key}"
				});
			});
		}     
	});
	
}

// 流程function*************************************************************************
var item;
// 呈主管 - 編製中
function sendBoss(){
	$.ajax({
		handler: inits.fhandle,
		action: "checkData",
		data: {},
		success: function(json){
			$('#managerItem').empty();
			item = json.bossList;
			// 不需要授信主管
			/*
			$('#bossItem').empty();
			var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"/>';
			$('#bossItem').append(bhtml).find('select').each(function(){
				$(this).setItems({
					item: item,
					format: "{value} {key}"
				});
			});
			*/
			// kind 1.企金 2.個金 3.通用
			// type 1.帳戶管理員 2.授信主管 3.授權主管 4.單位副主管 5.單位主管
			var html = '<select id="manager" name="manager" class="required" CommonManager="kind:1;type:3" />';
			$('#managerItem').append(html).find('select').each(function(){
				$(this).setItems({
					item: item,
					format: "{value} {key}"
				});
			});
			
			// 是否呈主管覆核？
			CommonAPI.confirmMessage(i18n.lms8500m01v00101["L850M01A.message01"], function(b){
				if (b) {
					$("#selectBossBox").thickbox({
						// 覆核
						title: i18n.lms8500m01v00101['approve'],
						width: 500,
						height: 300,
						modal: true,
						readOnly: false,
						valign: "bottom",
						align: "center",
						i18n: i18n.def,
						buttons: {
							"sure": function(){
								// 不需要授信主管
								/*
								var selectBoss = $("select[name^=boss]").map(function(){
									return $(this).val();
								}).toArray();
								
								for (var i in selectBoss) {
									if (selectBoss[i] == "") {
										// 請選擇授信主管
										return CommonAPI.showErrorMessage(i18n.lms8500m01v00101['checkSelect'] +
												i18n.lms8500m01v00101['L850M01A.bossId']);
									}
								}
								*/
								if ($("#manager").val() == "") {
									// 請選擇經副襄理
									return CommonAPI.showErrorMessage(i18n.lms8500m01v00101['checkSelect'] +
											i18n.lms8500m01v00101['L850M01A.managerId']);
								}
								// 驗證是否有重複的主管
								// if (checkArrayRepeat(selectBoss)) {
									// 主管人員名單重複請重新選擇
									// return CommonAPI.showErrorMessage(i18n.lms8500m01v00101['L850M01A.message02']);
								// }
								
								flowAction({
									page: responseJSON.page,
									saveData: true,
									manager: $("#manager").val()
								});
								$.thickbox.close();
								
							},
							
							"cancel": function(){
								$.thickbox.close();
							}
						}
					});
				}
			});
		}
	});
}

// 待覆核 - 覆核
function openCheck(){
	$("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
		title: i18n.lms8500m01v00101['approve'],
		width: 100,
		height: 100,
		modal: true,
		readOnly: false,
		valign: "bottom",
		align: "center",
		i18n: i18n.def,
		buttons: {
			"sure": function(){
				var val = $("[name=checkRadio]:checked").val();
				if (!val) {
					return CommonAPI.showMessage(i18n.lms8500m01v00101['checkSelect']);
				}
				$.thickbox.close();
				switch (val) {
				case "1":
					// 一般退回到編製中01O
					// 該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
					CommonAPI.confirmMessage(i18n.lms8500m01v00101['L850M01A.message03'], function(b){
						if (b) {
							flowAction({
								flowAction: false
							});
						}
					}); 
					break;
				case "3":
					// 該案件是否確定執行核定作業
					CommonAPI.confirmMessage(i18n.lms8500m01v00101['L850M01A.message04'], function(b){
						if (b) {
							flowAction({
								flowAction: true,
								checkDate: CommonAPI.getToday()// forCheckDate
							});
						}
					});
					break;
				}
			},
			"cancel": function(){
				$.thickbox.close();
			}
		}
	});
}

function flowAction(sendData){
	$.ajax({
		handler: inits.fhandle,
		data: $.extend({
			formAction: "flowAction",
			oid: responseJSON.oid,
			mainId: responseJSON.mainId
		}, (sendData || {})),
		success: function(){
			CommonAPI.triggerOpener("gridview", "reloadGrid");
			API.showPopMessage(i18n.def["runSuccess"], window.close);
		}
	});
}


$(document).ready(function(){
	
	// function的設定直接寫再外層即可
	// 跑event定義
	initFlowEvent();
	initCommonEvent();
	initEvent();

	// 撈畫面資料
	queryForm();
	
	$("#check1").show();

	// 控制畫面唯讀
    if (checkReadonly()) {
        $(".readOnlyhide").hide();
        $("form").lockDoc();
		_openerLockDoc="1";
    }

	
});