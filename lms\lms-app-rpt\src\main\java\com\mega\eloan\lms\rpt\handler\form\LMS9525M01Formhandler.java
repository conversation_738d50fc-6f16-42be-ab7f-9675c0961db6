/* 
 * LMS9515M01Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.form;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.rpt.report.LMS9511R01RptService;
import com.mega.eloan.lms.rpt.service.LMS9515Service;
import com.mega.sso.service.BranchService;

/**
 * <pre>
 * 管理報表
 * </pre>
 * 
 * @since 2011/11/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/25,jessica,new
 *          </ul>
 */

@Scope("request")
@Controller("lms9525m01formhandler")
public class LMS9525M01Formhandler extends AbstractFormHandler {

	@Resource
	LMS9515Service service9515;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	BranchService branchService;

	@Resource
	LMS9511R01RptService lms9515r01rptService;

	static final String SPACE = "";

	boolean st = false;
}
