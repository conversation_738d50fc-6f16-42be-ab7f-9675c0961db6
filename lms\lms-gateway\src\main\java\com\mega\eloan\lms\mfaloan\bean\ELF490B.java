package com.mega.eloan.lms.mfaloan.bean;

import javax.persistence.Column;
import javax.validation.constraints.Digits;

import tw.com.iisi.cap.model.GenericBean;

/** 防杜代辦消金覆審資料檔  **/
public class ELF490B extends GenericBean{

	private static final long serialVersionUID = 1L;
	/**
	 * 資料日期
	 */
	@Column(name = "ELF490B_DATA_YM", length = 7, columnDefinition = "CHAR(7)", nullable=false,unique = true)
	private String elf490b_data_ym;
	
	/**
	 * 覆審執行單位
	 */
	@Column(name = "ELF490B_BRNO", length = 3, columnDefinition = "CHAR(3)", nullable=false,unique = true)
	private String elf490b_brno;
	
	/**
	 * 命中註記 {當flag='1' 以 營運中心 去統計 命中條件 最多次的行員編號, 
	 *          當flag='2' 該 empNo 出現在哪些分行,
	 *          當flag='3' 該 empNo 在哪些分行, 符合哪些 ruleNo }
	 */
	@Column(name = "ELF490B_FLAG", length = 1, columnDefinition = "CHAR(1)", nullable=false,unique = true)
	private String elf490b_flag;
	
	/**
	 * 命中條件
	 */
	@Column(name = "ELF490B_RULE_NO", length = 2, columnDefinition = "CHAR(2)", nullable=false,unique = true)
	private String elf490b_rule_no;

	/**
	 * 行員編號
	 */
	@Column(name = "ELF490B_EMP_NO", length = 6, columnDefinition = "CHAR(6)", nullable=false,unique = true)
	private String elf490b_emp_no;
	
	/**
	 * 命中筆數(每一個營運中心, R1、R2、R3的前三大「承辦行員」案件優先覆審  => ELF490B_COUNT 借用 999 來區分 )
	 */
	@Digits(integer = 5, fraction = 0)
	@Column(name = "ELF490B_COUNT", columnDefinition = "DECIMAL(5,0)")
	private Integer elf490b_count;

	public String getElf490b_data_ym() {
		return elf490b_data_ym;
	}

	public void setElf490b_data_ym(String elf490b_data_ym) {
		this.elf490b_data_ym = elf490b_data_ym;
	}

	public String getElf490b_brno() {
		return elf490b_brno;
	}

	public void setElf490b_brno(String elf490b_brno) {
		this.elf490b_brno = elf490b_brno;
	}

	public String getElf490b_flag() {
		return elf490b_flag;
	}

	public void setElf490b_flag(String elf490b_flag) {
		this.elf490b_flag = elf490b_flag;
	}

	public String getElf490b_rule_no() {
		return elf490b_rule_no;
	}

	public void setElf490b_rule_no(String elf490b_rule_no) {
		this.elf490b_rule_no = elf490b_rule_no;
	}

	public String getElf490b_emp_no() {
		return elf490b_emp_no;
	}

	public void setElf490b_emp_no(String elf490b_emp_no) {
		this.elf490b_emp_no = elf490b_emp_no;
	}

	public Integer getElf490b_count() {
		return elf490b_count;
	}

	public void setElf490b_count(Integer elf490b_count) {
		this.elf490b_count = elf490b_count;
	}
		
}
