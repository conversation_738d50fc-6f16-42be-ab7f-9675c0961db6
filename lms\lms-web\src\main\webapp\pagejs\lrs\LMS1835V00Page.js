$(function(){
	var regYYYYMM = /^\d{4}\-(0?[1-9]|1[0-2])$/;
    // 最新歷史資料查詢初始化
    $("#show1").show();
    
    $("[name=search]").click(function(){
    
        if ($(this).val() == "new") {
            $("#show1").show();
            $("#show2").hide();
        } else {
            $("#show2").show();
            $("#show1").hide();
            
        }
    })
    
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        multiselect: true,
        handler: 'lms1835gridhandler',
        height: 350, // 設定高度
        width: 785,
        autowidth: false,
        postData: {
            formAction: "query",
            docStatus: viewstatus
        },
        colModel: [{
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'excelFile',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId',
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms1835v00["L1835M01a.dataDate"],
            align: "center",
            width: 60, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: download,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            },
            name: 'dataDate' // col.id
        }, {
            colHeader: i18n.lms1835v00["L1835M01a.branch"],
            align: "left",
            width: 140, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'branchId' // col.id
        }, {
            colHeader: i18n.lms1835v00["L1835M01a.reportDate"],
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            download(null, null, data);
        }
    
    });
    
    // ===============================================================================================================
    $("#buttonPanel").find("#btnDelete").click(function(){
        var $gridview = $("#gridview");
        var id = $gridview.getGridParam('selarrrow');
        var content = [];
        for (var i = 0; i < id.length; i++) {
            if (id[i] != "") {
                var datas = $gridview.getRowData(id[i]);
                content.push(datas.mainId);
            }
        }
        if (content.length == 0) {
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        } else {
            // confirmDelete=是否確定刪除?
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    $.ajax({
                        type: "POST",
                        handler: "lms1835m01formhandler",
                        data: {
                            formAction: "delete",
                            mainId: content
                        }
                        }).done(function(obj){
                            // 更新Grid內容
                            $("#gridview").trigger("reloadGrid");
                    });
                }
            });
        }
        
        
    }).end().find("#btnInsertExcelData").click(function(){
        // /**產生企金戶新增/增額名單*/
        thickBoxOpenADD();
        
    }).end().find("#btnFilter").click(function(){
        // 最新歷史資料查詢
        thickBoxOpenShow();
        
    });
    // =======================================================================================================
    // 產生企金戶新增/增額名單
	function thickBoxOpenADD(cellvalue, options, rowObject, showMsg){
	    
	        $.ajax({
	            type: "POST",
	            handler: "lms1705m01formhandler",
	            data: {
	                formAction: "queryBranch"
	            }
	        }).done(function(obj){
	                $.each(obj.itemOrder, function(idx, brNo) {
	            		var currobj = {};
	            		var brName = obj.item[brNo];
	            		currobj[brNo] = brName;
	            		//依 itemOrder, 一個一個append, 把 clear 指為 false
	            		
	            		//select
						$("#order").setItems({ item: currobj, format: "{value} {key}", clear:false, space: false });
					});
	                
	                //============================================
	                // '產生企金戶新增/增額名單',
	                $("#lms1835new").thickbox({
	                    title: i18n.lms1835v00['L1835M01a.title1'],
	                    width: 600,
	                    height: 200,
	                    align: 'center',
	                    valign: 'bottom',
	                    modal: false,
	                    i18n: i18n.def,
	                    buttons: {
	                        "sure": function(cellvalue, showMsg, oid, id){
	                        
	                            //YYYY-MM 格式驗證 (可不輸入，預設值為主機資料庫中最新月份之資料)
	                            var dataDate = $("#dataDate").val();
	                            
	                            if (dataDate != '') {
	                                if (!dataDate.match(regYYYYMM)) {
	                                    //val.date2=日期格式錯誤(YYYY-MM) 
	                                    return CommonAPI.showMessage(i18n.def["val.date2"]);
	                                }
	                            }
	                            
	                            var getVal = $("#order").val();
	                            $.ajax({
	                                handler: "lms1835m01formhandler",
	                                data: {
	                                    formAction: "transportExcel",
	                                    dataDate: dataDate,
	                                    // brNos : data
	                                    brNo: getVal
	                                }
	                                }).done(function(obj){
	                                    // 更新Grid內容
	                                    CommonAPI.triggerOpener("gridview", "reloadGrid");
	                                    $("#gridview").trigger("reloadGrid");
	                            });
	                            $.thickbox.close();
	                            
	                        },
	                        "cancel": function(){
	                        
	                            $.thickbox.close();
	                        }
	                    }
	                });
	        });
	        
	    };

// =======================================================================================================
// 產生企金戶新增/增額名單 [查詢]
function thickBoxOpenShow(cellvalue, options, rowObject, showMsg){

    // 抓(當日)日期年月去減少一個月
    var today = new Date();
    var year = today.getFullYear();
    var month = today.getMonth();
    // month = month + 1;
    // month = month - 1;
    if (month == 0) {
        month = 12;
        year = year - 1;
    }
    if (month < 10) {
        $("#searchDate").val(year + "-0" + month);
    } else {
        $("#searchDate").val(year + "-" + month);
    }
    
    // thickbox內容的預設值
    $("[name=search][value=new]").prop("checked", true);
    $("#show1").show();
    $("#show2").hide();
    
    $("#lms1835new2").thickbox({
        // '產生企金戶新增/增額名單[查詢]',
        title: i18n.lms1835v00['L1835M01a.title2'],
        width: 500,
        height: 160,
        align: 'center',
        valign: 'bottom',
        modal: false,
        i18n: i18n.def,
        buttons: {
            "sure": function(cellvalue, showMsg, oid, id){
                var showId = $("[name=search]:checked").val();
                //alert(showId);
                //資料年月驗證
                if (showId == "old") {
                
                    //YYYY-MM 格式驗證
                    //起始日期
                    var starDate = $("#starDate").val();
                    //迄至日期
                    var endDate = $("#endDate").val();
                    var str = "";
                    
                    if (starDate == "") {
                        //L1835v00.startDate=起始日期
                        str = i18n.lms1835v00["L1835v00.startDate"]
                        //val.ineldate=請輸入年月
                        return CommonAPI.showMessage(str + "" + i18n.def["val.ineldate"]);
                    }
                    if (endDate == "") {
                        //L1835v00.endDate=迄至日期
                        str = i18n.lms1835v00["L1835v00.endDate"]
                        //val.ineldate=請輸入年月
                        return CommonAPI.showMessage(str + i18n.def["val.ineldate"]);
                    }
                    if (!starDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                        //val.date2=日期格式錯誤(YYYY-MM)
                        return CommonAPI.showMessage(i18n.def["val.date2"]);
                    }
                    if (!endDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                        //val.date2=日期格式錯誤(YYYY-MM)
                        return CommonAPI.showMessage(i18n.def["val.date2"]);
                    }
                    
                    //判斷起始日期是否大於迄至日期
                    var starMon = starDate.replace("-", "");
                    var endMon = endDate.replace("-", "");
                    var a1 = parseInt(starMon);
                    var a2 = parseInt(endMon);
                    if (a1 > a2) {
                        //L1835v00.error=起始日期不可大於迄至日期
                        return CommonAPI.showMessage(i18n.lms1835v00["L1835v00.error"]);
                    }
                    
                }
                
                // 找L184M01A的資料
                updateGrid(showId);
                //type1(showId);
                $.thickbox.close();
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
    
}

//=================================================================================================
// 查詢
function updateGrid(showId){
    $("#gridview").jqGrid("setGridParam", {
        postData: {
            formAction: "search",
            rowNum: 5,
            searchDate: $("#searchDate").val(),
            starDate: $("#starDate").val(),
            endDate: $("#endDate").val(),
            kind: showId
        },
		page : 1,
		//gridPage : 1,
        search: true
    }).trigger("reloadGrid");
}

// ======================================================================================================
//下載EXCEL
function download(cellvalue, options, data){
    $.capFileDownload({
        //  handler: "simplefiledwnhandler",
        data: {
            fileOid: data.excelFile
        
        }
    });
    
}

});



