package com.mega.eloan.lms.lms.bean;

import java.math.BigDecimal;

import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.model.L140M01A;

/**
 * <pre>
 * 額度明細表加總
 * By CustId + DUPNO 去計算
 * </pre>
 * 
 * @since 2012/5/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/5/9,Rex,new
 *          </ul>
 */
public class CountTOT {

	private static final BigDecimal 一百 = new BigDecimal(100);

	/** 幣別 */
	private String curr;
	/** 授信額度合計 */
	private BigDecimal LoanTotAmt = BigDecimal.ZERO;
	/** 擔保授信合計 */
	private BigDecimal GtAmt = BigDecimal.ZERO;
	/** 衍生性商品原始合計 */
	private BigDecimal LoanTotZAmt = BigDecimal.ZERO;
	/** 衍生性商品相當合計 */
	private BigDecimal LoanTotLAmt = BigDecimal.ZERO;
	/** 前准額度批覆合計 */
	private BigDecimal LVTOTAMT = BigDecimal.ZERO;
	/** 前准額度批覆擔保合計 */
	private BigDecimal LVASSTOTAMT = BigDecimal.ZERO;
	/** 減額額度合計 */
	private BigDecimal MinTOTAMT = BigDecimal.ZERO;
	/** 減額擔保額度合計 */
	private BigDecimal MinAssTOTAMT = BigDecimal.ZERO;

	/** 授信額度合計多幣別說明 */
	private String MultiAmt = "";
	/** 擔保授信合計多幣別說明 */
	private String MultiAssureAmt = "";
	/** 客戶統編 */
	private String custId;
	/** 重覆序號 */
	private String dupNo;
	/** 額度序號 */
	private String cntrNo;
	/** 共用額度序號 */
	private String commSno;

	/** 取得加總的key值現請額度幣別 + 客戶統編 + 重覆序號 */
	public String getKey() {
		return curr + custId + dupNo;
	}

	/** 授信額度合計多幣別說明 */
	public String getMultiAmt() {
		return MultiAmt;
	}

	/** 授信額度合計多幣別說明 */
	public void addMultiAmt(String multiAmt) {
		MultiAmt = MultiAmt + "\n\r" + multiAmt;
	}

	/** 擔保授信合計多幣別說明 */
	public String getMultiAssureAmt() {
		return MultiAssureAmt;
	}

	/** 擔保授信合計多幣別說明 */
	public void addMultiAssureAmt(String multiAssureAmt) {
		MultiAssureAmt = MultiAssureAmt + "\n\r" + multiAssureAmt;
	}

	/** 重覆序號 */
	public String getCntrNo() {
		return cntrNo;
	}

	/** 重覆序號 */
	public void setCntrNo(String cntrNo) {
		this.cntrNo = cntrNo;
	}

	/** 共用額度序號 */
	public String getCommSno() {
		return commSno;
	}

	/** 共用額度序號 */
	public void setCommSno(String commSno) {
		this.commSno = commSno;
	}

	/** 幣別 **/
	public void setCurr(String curr) {
		this.curr = curr;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	public BigDecimal getMinTOTAMT() {
		return MinTOTAMT;
	}

	public void addMinTOTAMT(BigDecimal minTOTAMT) {
		if (minTOTAMT == null) {
			minTOTAMT = BigDecimal.ZERO;
		}
		MinTOTAMT = MinTOTAMT.add(minTOTAMT);
	}

	public BigDecimal getMinAssTOTAMT() {
		return MinAssTOTAMT;
	}

	public void addMinAssTOTAMT(BigDecimal minAssTOTAMT) {
		if (minAssTOTAMT == null) {
			minAssTOTAMT = BigDecimal.ZERO;
		}
		MinAssTOTAMT = MinAssTOTAMT.add(minAssTOTAMT);
	}

	public String getCustId() {
		return custId;
	}

	public String getDupNo() {
		return dupNo;
	}

	/** 幣別 **/
	public String getCurr() {
		return curr;
	}

	/** 授信額度合計 */
	public BigDecimal getLoanTotAmt() {
		return LoanTotAmt;
	}

	/** 授信額度合計 */
	public void addLoanTotAmt(BigDecimal loanTotAmt) {
		if (loanTotAmt == null) {
			loanTotAmt = BigDecimal.ZERO;
		}
		LoanTotAmt = LoanTotAmt.add(loanTotAmt);
	}

	/** 擔保授信合計 */
	public BigDecimal getGtAmt() {
		return GtAmt;
	}

	/** 擔保授信合計 */
	public void addGtAmt(BigDecimal gtAmt) {
		if (gtAmt == null) {
			gtAmt = BigDecimal.ZERO;
		}
		GtAmt = GtAmt.add(gtAmt);
	}

	/** 衍生性商品原始合計 */
	public BigDecimal getLoanTotZAmt() {
		return LoanTotZAmt;
	}

	/** 衍生性商品原始合計 */
	public void addLoanTotZAmt(BigDecimal loanTotZAmt) {
		if (loanTotZAmt == null) {
			loanTotZAmt = BigDecimal.ZERO;
		}
		LoanTotZAmt = LoanTotZAmt.add(loanTotZAmt);
	}

	/** 衍生性商品相當合計 */
	public BigDecimal getLoanTotLAmt() {
		return LoanTotLAmt;
	}

	/** 衍生性商品相當合計 */
	public void addLoanTotLAmt(BigDecimal loanTotLAmt) {
		if (loanTotLAmt == null) {
			loanTotLAmt = BigDecimal.ZERO;
		}
		LoanTotLAmt = LoanTotLAmt.add(loanTotLAmt);
	}

	/** 前准額度批覆合計 **/
	public BigDecimal getLVTOTAMT() {
		return LVTOTAMT;
	}

	/** 前准額度批覆合計 **/
	public void addLVTOTAMT(BigDecimal lVTOTAMT) {
		if (lVTOTAMT == null) {
			lVTOTAMT = BigDecimal.ZERO;
		}
		LVTOTAMT = LVTOTAMT.add(lVTOTAMT);
	}

	/** 前准額度批覆擔保合計 */
	public BigDecimal getLVASSTOTAMT() {
		return LVASSTOTAMT;
	}

	/** 前准額度批覆擔保合計 */
	public void addLVASSTOTAMT(BigDecimal lVASSTOTAMT) {
		if (lVASSTOTAMT == null) {
			lVASSTOTAMT = BigDecimal.ZERO;
		}
		LVASSTOTAMT = LVASSTOTAMT.add(lVASSTOTAMT);
	}

	/**
	 * 額度明細表主檔
	 */
	private L140M01A l140m01a;

	public L140M01A getL140m01a() {
		return l140m01a;
	}

	public void setL140m01a(L140M01A l140m01a) {
		this.l140m01a = l140m01a;
	}

	public CountTOT(String curr, String custId, String dupNo) {
		this.curr = curr;
		this.custId = custId;
		this.dupNo = dupNo;

	}

	public CountTOT(L140M01A l140m01a, BranchRate branchRate) {
		this.l140m01a = l140m01a;
		this.custId = l140m01a.getCustId();
		this.dupNo = l140m01a.getDupNo();
		this.cntrNo = Util.trim(l140m01a.getCntrNo());
		this.commSno = Util.trim(l140m01a.getCommSno());
		this.LoanTotAmt = l140m01a.getCurrentApplyAmt();
		this.curr = l140m01a.getCurrentApplyCurr();
		// 當有衍生性金融商品 要做計算(要多判斷hasDerivatives為Y時才是真正的衍生性金融商品科目(非保證、進出口科目))
		if ("Y".equals(l140m01a.getIsDerivatives()) && "Y".equals(l140m01a.getHasDerivatives())) {
			LoanTotZAmt = LoanTotAmt;
			BigDecimal DerivativesNum = l140m01a.getDerivativesNum();
			// 總和+(現請額度*風險係數(%))
			if (BigDecimal.ZERO.compareTo(DerivativesNum) != 0) {
				LoanTotLAmt = LoanTotAmt.multiply(DerivativesNum).divide(一百);
			}
		}
		// 檢查有無送保 並且 保證成數不等於空值
		if (("Y".equals(l140m01a.getHeadItem1()) && (Util.isNotEmpty(l140m01a
				.getGutPercent())))) {
			// 目前這份額度明細表 擔保合計等於現請額度 * 保證成數 - 擔保授信額度調整
			GtAmt = Arithmetic.div_floor(
					LoanTotAmt.multiply(l140m01a.getGutPercent()), 一百, 0);
			if (Util.isNotEmpty(l140m01a.getAssureTotEAmt())) {
				GtAmt.subtract(l140m01a.getAssureTotEAmt());
			}
		} else {
			// 非信保案件計算其中擔保 判斷授信性質為擔保或無擔保　
			// 額度性質為擔保(S)時 才去做加總
			if (UtilConstants.Cntrdoc.sbjProperty.擔保.equals(l140m01a
					.getSbjProperty())) {
				if (Util.isNotEmpty(l140m01a.getAssureTotEAmt())) {
					GtAmt = LoanTotAmt.subtract(l140m01a.getAssureTotEAmt());
				}
			}

		}

		// 前准批覆授信額度 當有幣別才做計算
		if (Util.isNotEmpty(Util.trim(l140m01a.getLV2Curr()))) {
			BigDecimal nowLv = l140m01a.getLV2Amt();
			if (nowLv == null) {
				nowLv = BigDecimal.ZERO;
			}
			// 將幣別轉為現請額度幣別做加總
			nowLv = branchRate.toOtherAmt(l140m01a.getLV2Curr(), curr, nowLv);
			this.LVTOTAMT = nowLv;
			branchRate.toLocalAmt(l140m01a.getLV2Curr(), nowLv);
			// 減額計算
			if (nowLv.compareTo(LoanTotAmt) == 1) {
				MinTOTAMT = nowLv.subtract(LoanTotAmt);
			}

		}
		// 前准額度批覆擔保合計 當有幣別才做計算
		if (Util.isNotEmpty(Util.trim(l140m01a.getLVAssureCurr()))) {
			BigDecimal nowLvass = l140m01a.getLVAssureAmt();
			if (nowLvass == null) {
				nowLvass = BigDecimal.ZERO;
			}
			// 將幣別轉為現請額度幣別做加總
			nowLvass = branchRate.toOtherAmt(l140m01a.getLVAssureCurr(), curr,
					nowLvass);
			this.LVASSTOTAMT = nowLvass;
			// 減額擔保計算
			if (nowLvass.compareTo(GtAmt) == 1) {
				MinAssTOTAMT = nowLvass.subtract(GtAmt);
			}
		}
		this.MultiAmt = curr + " " + NumConverter.addComma(LoanTotLAmt);
		this.MultiAssureAmt = curr + " " + NumConverter.addComma(GtAmt);

	}

}
