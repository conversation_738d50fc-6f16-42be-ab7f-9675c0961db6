/* 
 * LMS9515M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.pages;



import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.rpt.panels.LMS9515S01Panel;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 管理報表[常董會及申報案件明細檔]
 * </pre>
 * 
 * @since 2011/12/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/30,jessica,new
 *          </ul>
 */

@Controller
@RequestMapping(path = "/rpt/LMS9515M01Page/{page}")
public class LMS9515M01Page extends AbstractEloanForm {

    @Autowired
	FlowService flowService;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS9515M01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		
		
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
		AuthType.Modify));

		// 要加GRID才能載入欄位名稱
		renderJsI18N(LMS9515M01Page.class);
		

		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
		System.out
				.println("mainid=" + params.getString(EloanConstants.MAIN_ID));

	}// ;

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS9515S01Panel(TAB_CTX, true);
			renderJsI18N(LMS9515S01Panel.class);
			break;

		default:
			panel = new LMS9515S01Panel(TAB_CTX, true);
			break;
		}
		if (panel == null)
			panel = new Panel(TAB_CTX);
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L170M01A.class;
	}

}//
