/* 
 * C101S01N.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 個金相關查詢授信歸戶檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01N", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "loanNo" }))
public class C101S01N extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 分行別或銀行名稱
	 * <p/>
	 * 依來源：<br/>
	 * (MIS.ELLNF)固定為017<br/>
	 * (MIS.BAM002.BANKCODE)前3碼
	 */
	@Size(max = 3)
	@Column(name = "BANKNO", length = 3, columnDefinition = "CHAR(3)")
	private String bankNo;

	/**
	 * 放款帳號
	 * <p/>
	 * (MIS.ELLNF.LOANNO)<br/>
	 * (MIS.BAM002)
	 */
	@Size(max = 15)
	@Column(name = "LOANNO", length = 15, columnDefinition = "VARCHAR(15)")
	private String loanNo;

	/**
	 * (會計)科目
	 * <p/>
	 * (MIS.ELLNF.LOANTP)
	 */
	@Size(max = 8)
	@Column(name = "LOANTP", length = 8, columnDefinition = "VARCHAR(8)")
	private String loanTP;

	/**
	 * 額度
	 * <p/>
	 * 單位：新台幣元<br/>
	 * (MIS.ELLNF.QUOTAPRV)<br/>
	 * (MIS.BAM002.TOT_CONT)
	 */
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "QUOTAPRV", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal quotaPrv;

	/**
	 * 額度重複註記
	 * <p/>
	 * 若為*則顯示＊<br/>
	 * (MIS.ELLNF.QUOTADUP)<br/>
	 * $主要額度(多筆共用時之主要額度)<br/>
	 * *共用額度<br/>
	 * 0流用額度<br/>
	 * 空白表示單一額度<br/>
	 * 若要加總同一ID之授信額度只要選擇空白及$
	 */
	@Size(max = 1)
	@Column(name = "QUOTADUP", length = 1, columnDefinition = "VARCHAR(1)")
	private String quotaDup;

	/**
	 * 餘額(有擔保)
	 * <p/>
	 * 單位：新台幣元
	 */
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "BALS", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal balS;

	/**
	 * 餘額(無擔保)
	 * <p/>
	 * 單位：新台幣元
	 */
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "BALN", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal balN;

	/**
	 * 餘額(合計)
	 * <p/>
	 * 單位：新台幣元<br/>
	 * (MIS.ELLNF.LOANBAL)<br/>
	 * (MIS.BAM002.TOT_LOAN)
	 */
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "LOANBAL", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal loanBal;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 循環註記 **/
	@Size(max = 1)
	@Column(name = "CYCLE_FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String cycle_flag;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得分行別或銀行名稱
	 * <p/>
	 * 依來源：<br/>
	 * (MIS.ELLNF)固定為017<br/>
	 * (MIS.BAM002.BANKCODE)前3碼
	 */
	public String getBankNo() {
		return this.bankNo;
	}

	/**
	 * 設定分行別或銀行名稱
	 * <p/>
	 * 依來源：<br/>
	 * (MIS.ELLNF)固定為017<br/>
	 * (MIS.BAM002.BANKCODE)前3碼
	 **/
	public void setBankNo(String value) {
		this.bankNo = value;
	}

	/**
	 * 取得放款帳號
	 * <p/>
	 * (MIS.ELLNF.LOANNO)<br/>
	 * (MIS.BAM002)
	 */
	public String getLoanNo() {
		return this.loanNo;
	}

	/**
	 * 設定放款帳號
	 * <p/>
	 * (MIS.ELLNF.LOANNO)<br/>
	 * (MIS.BAM002)
	 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}

	/**
	 * 取得(會計)科目
	 * <p/>
	 * (MIS.ELLNF.LOANTP)
	 */
	public String getLoanTP() {
		return this.loanTP;
	}

	/**
	 * 設定(會計)科目
	 * <p/>
	 * (MIS.ELLNF.LOANTP)
	 **/
	public void setLoanTP(String value) {
		this.loanTP = value;
	}

	/**
	 * 取得額度
	 * <p/>
	 * 單位：新台幣元<br/>
	 * (MIS.ELLNF.QUOTAPRV)<br/>
	 * (MIS.BAM002.TOT_CONT)
	 */
	public BigDecimal getQuotaPrv() {
		return this.quotaPrv;
	}

	/**
	 * 設定額度
	 * <p/>
	 * 單位：新台幣元<br/>
	 * (MIS.ELLNF.QUOTAPRV)<br/>
	 * (MIS.BAM002.TOT_CONT)
	 **/
	public void setQuotaPrv(BigDecimal value) {
		this.quotaPrv = value;
	}

	/**
	 * 取得額度重複註記
	 * <p/>
	 * 若為*則顯示＊<br/>
	 * (MIS.ELLNF.QUOTADUP)<br/>
	 * $主要額度(多筆共用時之主要額度)<br/>
	 * *共用額度<br/>
	 * 0流用額度<br/>
	 * 空白表示單一額度<br/>
	 * 若要加總同一ID之授信額度只要選擇空白及$
	 */
	public String getQuotaDup() {
		return this.quotaDup;
	}

	/**
	 * 設定額度重複註記
	 * <p/>
	 * 若為*則顯示＊<br/>
	 * (MIS.ELLNF.QUOTADUP)<br/>
	 * $主要額度(多筆共用時之主要額度)<br/>
	 * *共用額度<br/>
	 * 0流用額度<br/>
	 * 空白表示單一額度<br/>
	 * 若要加總同一ID之授信額度只要選擇空白及$
	 **/
	public void setQuotaDup(String value) {
		this.quotaDup = value;
	}

	/**
	 * 取得餘額(有擔保)
	 * <p/>
	 * 單位：新台幣元
	 */
	public BigDecimal getBalS() {
		return this.balS;
	}

	/**
	 * 設定餘額(有擔保)
	 * <p/>
	 * 單位：新台幣元
	 **/
	public void setBalS(BigDecimal value) {
		this.balS = value;
	}

	/**
	 * 取得餘額(無擔保)
	 * <p/>
	 * 單位：新台幣元
	 */
	public BigDecimal getBalN() {
		return this.balN;
	}

	/**
	 * 設定餘額(無擔保)
	 * <p/>
	 * 單位：新台幣元
	 **/
	public void setBalN(BigDecimal value) {
		this.balN = value;
	}

	/**
	 * 取得餘額(合計)
	 * <p/>
	 * 單位：新台幣元<br/>
	 * (MIS.ELLNF.LOANBAL)<br/>
	 * (MIS.BAM002.TOT_LOAN)
	 */
	public BigDecimal getLoanBal() {
		return this.loanBal;
	}

	/**
	 * 設定餘額(合計)
	 * <p/>
	 * 單位：新台幣元<br/>
	 * (MIS.ELLNF.LOANBAL)<br/>
	 * (MIS.BAM002.TOT_LOAN)
	 **/
	public void setLoanBal(BigDecimal value) {
		this.loanBal = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得循環註記 **/
	public String getCycle_flag() {
		return cycle_flag;
	}
	/** 設定循環註記 **/
	public void setCycle_flag(String cycle_flag) {
		this.cycle_flag = cycle_flag;
	}
}
