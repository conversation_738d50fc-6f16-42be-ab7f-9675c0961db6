/**
 * Operation.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.operation;

import java.util.Map;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.handler.FormHandler;
import tw.com.iisi.cap.response.IResult;

/**
 * <p>
 * Operation.
 * </p>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/22,iristu,new
 *          </ul>
 */
public interface Operation {

    /**
     * 取得Operation name
     * 
     * @return
     */
    String getName();

    /**
     * 設置Operation name
     * 
     * @param name
     */
    void setName(String name);

    /**
     * 取得所有步驟
     * 
     * @return
     */
    Map<String, OperationStep> getRuleMap();

    /**
     * 設置所有步驟
     * 
     * @param ruleMap
     *            所有Operation步驟
     */
    void setRuleMap(Map<String, OperationStep> ruleMap);

    /**
     * 執行
     * 
     * @param params
     *            Request參數
     * @param handler
     *            對應執行的handler
     * @return
     * @throws CapException
     */
    IResult execute(PageParameters params, FormHandler handler) throws CapException;

}
