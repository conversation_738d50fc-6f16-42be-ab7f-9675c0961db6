---------------------------------------------------------
-- LMS.L140M01O 擔保品資料明細檔
---------------------------------------------------------


---------------------------------------------------------
-- TABLE
---------------------------------------------------------
DROP TABLE LMS.L140M01O;
CREATE TABLE LMS.L140M01O (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	<PERSON>QNO         DECIMAL(5,0)  not null,
	<PERSON><PERSON><PERSON>        CHAR(3)      ,
	<PERSON>STBRN        CHAR(3)      ,
	ESTDATE       DATE         ,
	<PERSON><PERSON><PERSON><PERSON>        VARCHAR(10)  ,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>      VARCHAR(120) ,
	<PERSON><PERSON>NO        VARCHAR(09)  ,
	COLLTYP1      CHAR(2)      ,
	COLLTYP2      CHAR(2)      ,
	DOCSTATUS     VARCHAR(03)  ,
	COLLNAME      VARCHAR(150) ,
	LOANTWD       DECIMAL(13,0),
	TTLNUM        DECIMAL(5,0) ,
	TTLBAL        DECIMAL(13,0),
	SET1          CHAR(1)      ,
	SETODR        DECIMAL(2,0) ,
	SETAMT        DECIMAL(13,0),
	PRIAMT        DECIMAL(13,0),
	TOTALLOANAMT  DECIMAL(13,0),
	INS1          CHAR(1)      ,
	INSAMT1       DECIMAL(13,0),
	INSAMT2       DECIMAL(13,0),
	INSID         VARCHAR(10)  ,
	FIREINS       VARCHAR(05)  ,
	INAPPMONEY    DECIMAL(13,0),
	ASSTOT        DECIMAL(13,0),
	REBUILDCOST   DECIMAL(13,0),
	TERM1         CHAR(1)      ,
	TERM2         CHAR(1)      ,
	ADDRAREANUM   DECIMAL(13,0),
	BUILD         VARCHAR(4096),
	AREADETAIL    VARCHAR(2048),
	LNNO          VARCHAR(1024),
	TAXNO         VARCHAR(12)  ,
	TAXADDR       VARCHAR(150) ,
	PAYPERCENT    DECIMAL(5,2) ,
	CMSDESC       CLOB         ,
	INAMT         DECIMAL(13,0),
	HOUSEYEAR     DECIMAL(3,0) ,
	CMSOID        CHAR(32)     ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,
	SETORDSTR	  VARCHAR(120) ,
	UNITCHG		  CHAR(1) ,
	TOTLNAMT	  DECIMAL(13,0),
	REBUILDAMT	  DECIMAL(13,0),

	constraint P_L140M01O PRIMARY KEY(OID)
) IN EL_DATA_16KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------

CREATE UNIQUE INDEX LMS.XL140M01O01 ON LMS.L140M01O   (MAINID, SEQNO);
CREATE INDEX LMS.XL140M01O02 ON LMS.L140M01O   (MAINID, COLLNO, CUSTID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140M01O IS '擔保品資料明細檔';
COMMENT ON LMS.L140M01O (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQNO         IS '序號', 
	BRANCH        IS '分行代碼', 
	ESTBRN        IS '鑑價分行', 
	ESTDATE       IS '鑑估日期', 
	CUSTID        IS '統一編號', 
	DUPNO         IS '重覆序號', 
	CUSTNAME      IS '客戶名稱', 
	COLLNO        IS '擔保品編號', 
	COLLTYP1      IS '擔保品大類', 
	COLLTYP2      IS '擔保品小類', 
	DOCSTATUS     IS '擔保品文件狀態', 
	COLLNAME      IS '擔保品名稱', 
	LOANTWD       IS '放款值（TWD）', 
	TTLNUM        IS '已敘做放款總戶數', 
	TTLBAL        IS '已敘做放款總餘額', 
	SET1          IS '土地及建物擬/已設定', 
	SETODR        IS '順位', 
	SETAMT        IS '抵押金額', 
	PRIAMT        IS '前順位抵押金額', 
	TOTALLOANAMT  IS '押值', 
	INS1          IS '建物擬/已投保', 
	INSAMT1       IS '火險金額', 
	INSAMT2       IS '地震險金額', 
	INSID         IS '保險公司', 
	FIREINS       IS '火險金額計算方式', 
	INAPPMONEY    IS '借款金額', 
	ASSTOT        IS '土地放款值', 
	REBUILDCOST   IS '重置成本', 
	TERM1         IS '擔保品係', 
	TERM2         IS '有無加建未辦保存登記之建物', 
	ADDRAREANUM   IS '面積', 
	BUILD         IS '建物地址', 
	AREADETAIL    IS '土地地段', 
	LNNO          IS '地號', 
	TAXNO         IS '稅籍編號', 
	TAXADDR       IS '稅籍地址', 
	PAYPERCENT    IS '核貸成數', 
	CMSDESC       IS '擔保品內容', 
	INAMT         IS '購價或時價', 
	HOUSEYEAR     IS '屋齡', 
	CMSOID        IS '擔保品Oid', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期',
	SETORDSTR	  IS '設定順位文字',
	UNITCHG		  IS '金額單位異動',
	TOTLNAMT	  IS '押值(折算後)',
	REBUILDAMT	  IS '重置成本(折算後)'
);
