/* 
 * LPDFD01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 授信 PDF 舊案刪除記錄檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="LPDFD01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class LPDFD01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 記錄主文件UniversalID
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 報表文件ID<p/>
	 * 記錄報表文件UniversalID
	 */
	@Size(max=32)
	@Column(name="RPTUNID", length=32, columnDefinition="CHAR(32)")
	private String rptUNID;

	/** 顯示順序 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="RPTSEQ", columnDefinition="DECIMAL(3,0)")
	private Integer rptSeq;

	/** 
	 * 顯示註記<p/>
	 * 資料採2進制方式儲存(2的次方)<br/>
	 *  1.授管處<br/>
	 *  2.營運中心<br/>
	 *  4.分行端<br/>
	 *  如包含該值則表示要顯示<br/>
	 *  例如：<br/>
	 *  rptDisp = 7 (1+2+4全部顯示)
	 */
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="RPTDISP", columnDefinition="DECIMAL(3,0)")
	private Integer rptDisp;

	/** 
	 * 報表編號<p/>
	 * FLMS110R01(案件簽報書)<br/>
	 *  FLMS120R01(案件簽報書)<br/>
	 *  FLMS130R01(案件簽報書)<br/>
	 *  FLMS130R03(異常通報表)<br/>
	 *  FLMS130R03A(異常通報表-批覆)<br/>
	 *  FLMS320R01(區中心說明及意見)<br/>
	 *  FLMS740R01(額度批覆表)<br/>
	 *  FLMS720R01(補充說明及授信審查意見)<br/>
	 *  FLMS720R04(會簽意見)<br/>
	 *  FLMS720R05(區域中心說明及授信審查意見)<br/>
	 *  FLMS730R01(授信報案考核表)<br/>
	 *  <br/>
	 *  FLMS150R01(小放會會議記錄)<br/>
	 *  FLMS160R01(動用審核表)<br/>
	 *  FLMS170R01(覆審報告表)<br/>
	 *  FLMS180R01(已敘做授信案件清單)<br/>
	 *  FLMS180R05(授信契約已逾期控制表)<br/>
	 *  FLMS180R10(信保案件未動用屆期清單)<br/>
	 *  FLMS780R02<br/>
	 *  已敘做授信案件清單(總行批示意見)<br/>
	 *  FLMS781R01<br/>
	 *  每月常董會報告事項彙總及申報案件數統計表<br/>
	 *  FLMS781R02<br/>
	 *  各級授權範圍內承做授信案件統計表
	 */
	@Size(max=20)
	@Column(name="RPTTYPE", length=20, columnDefinition="VARCHAR(20)")
	private String rptType;

	/** 報表名稱(描述) **/
	@Size(max=120)
	@Column(name="RPTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String rptName;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="VARCHAR(12)")
	private String cntrNo;

	/** 報表檔檔案位置 **/
	@Size(max=128)
	@Column(name="RPTFILE", length=128, columnDefinition="VARCHAR(128)")
	private String rptFile;

	/** 報表亂碼 **/
	@Size(max=32)
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 刪除時間 **/
	@Column(name="DELETETIME", columnDefinition="TIMESTAMP")
	private Timestamp deleteTime;

	/** 
	 * 建立人員號碼<p/>
	 * 系統轉換：NOTES<br/>
	 *  自行附加：員工編號
	 */
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 
	 * 建立日期<p/>
	 * current timestamp
	 */
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 
	 * 異動人員號碼<p/>
	 * 系統轉換：NOTES<br/>
	 *  自行附加：員工編號
	 */
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 
	 * 異動日期<p/>
	 * current timestamp
	 */
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 記錄主文件UniversalID
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  記錄主文件UniversalID
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得報表文件ID<p/>
	 * 記錄報表文件UniversalID
	 */
	public String getRptUNID() {
		return this.rptUNID;
	}
	/**
	 *  設定報表文件ID<p/>
	 *  記錄報表文件UniversalID
	 **/
	public void setRptUNID(String value) {
		this.rptUNID = value;
	}

	/** 取得顯示順序 **/
	public Integer getRptSeq() {
		return this.rptSeq;
	}
	/** 設定顯示順序 **/
	public void setRptSeq(Integer value) {
		this.rptSeq = value;
	}

	/** 
	 * 取得顯示註記<p/>
	 * 資料採2進制方式儲存(2的次方)<br/>
	 *  1.授管處<br/>
	 *  2.營運中心<br/>
	 *  4.分行端<br/>
	 *  如包含該值則表示要顯示<br/>
	 *  例如：<br/>
	 *  rptDisp = 7 (1+2+4全部顯示)
	 */
	public Integer getRptDisp() {
		return this.rptDisp;
	}
	/**
	 *  設定顯示註記<p/>
	 *  資料採2進制方式儲存(2的次方)<br/>
	 *  1.授管處<br/>
	 *  2.營運中心<br/>
	 *  4.分行端<br/>
	 *  如包含該值則表示要顯示<br/>
	 *  例如：<br/>
	 *  rptDisp = 7 (1+2+4全部顯示)
	 **/
	public void setRptDisp(Integer value) {
		this.rptDisp = value;
	}

	/** 
	 * 取得報表編號<p/>
	 * FLMS110R01(案件簽報書)<br/>
	 *  FLMS120R01(案件簽報書)<br/>
	 *  FLMS130R01(案件簽報書)<br/>
	 *  FLMS130R03(異常通報表)<br/>
	 *  FLMS130R03A(異常通報表-批覆)<br/>
	 *  FLMS320R01(區中心說明及意見)<br/>
	 *  FLMS740R01(額度批覆表)<br/>
	 *  FLMS720R01(補充說明及授信審查意見)<br/>
	 *  FLMS720R04(會簽意見)<br/>
	 *  FLMS720R05(區域中心說明及授信審查意見)<br/>
	 *  FLMS730R01(授信報案考核表)<br/>
	 *  <br/>
	 *  FLMS150R01(小放會會議記錄)<br/>
	 *  FLMS160R01(動用審核表)<br/>
	 *  FLMS170R01(覆審報告表)<br/>
	 *  FLMS180R01(已敘做授信案件清單)<br/>
	 *  FLMS180R05(授信契約已逾期控制表)<br/>
	 *  FLMS180R10(信保案件未動用屆期清單)<br/>
	 *  FLMS780R02<br/>
	 *  已敘做授信案件清單(總行批示意見)<br/>
	 *  FLMS781R01<br/>
	 *  每月常董會報告事項彙總及申報案件數統計表<br/>
	 *  FLMS781R02<br/>
	 *  各級授權範圍內承做授信案件統計表
	 */
	public String getRptType() {
		return this.rptType;
	}
	/**
	 *  設定報表編號<p/>
	 *  FLMS110R01(案件簽報書)<br/>
	 *  FLMS120R01(案件簽報書)<br/>
	 *  FLMS130R01(案件簽報書)<br/>
	 *  FLMS130R03(異常通報表)<br/>
	 *  FLMS130R03A(異常通報表-批覆)<br/>
	 *  FLMS320R01(區中心說明及意見)<br/>
	 *  FLMS740R01(額度批覆表)<br/>
	 *  FLMS720R01(補充說明及授信審查意見)<br/>
	 *  FLMS720R04(會簽意見)<br/>
	 *  FLMS720R05(區域中心說明及授信審查意見)<br/>
	 *  FLMS730R01(授信報案考核表)<br/>
	 *  <br/>
	 *  FLMS150R01(小放會會議記錄)<br/>
	 *  FLMS160R01(動用審核表)<br/>
	 *  FLMS170R01(覆審報告表)<br/>
	 *  FLMS180R01(已敘做授信案件清單)<br/>
	 *  FLMS180R05(授信契約已逾期控制表)<br/>
	 *  FLMS180R10(信保案件未動用屆期清單)<br/>
	 *  FLMS780R02<br/>
	 *  已敘做授信案件清單(總行批示意見)<br/>
	 *  FLMS781R01<br/>
	 *  每月常董會報告事項彙總及申報案件數統計表<br/>
	 *  FLMS781R02<br/>
	 *  各級授權範圍內承做授信案件統計表
	 **/
	public void setRptType(String value) {
		this.rptType = value;
	}

	/** 取得報表名稱(描述) **/
	public String getRptName() {
		return this.rptName;
	}
	/** 設定報表名稱(描述) **/
	public void setRptName(String value) {
		this.rptName = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得報表檔檔案位置 **/
	public String getRptFile() {
		return this.rptFile;
	}
	/** 設定報表檔檔案位置 **/
	public void setRptFile(String value) {
		this.rptFile = value;
	}

	/** 取得報表亂碼 **/
	public String getRandomCode() {
		return this.randomCode;
	}
	/** 設定報表亂碼 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得刪除時間 **/
	public Timestamp getDeleteTime() {
		return this.deleteTime;
	}
	/** 設定刪除時間 **/
	public void setDeleteTime(Timestamp value) {
		this.deleteTime = value;
	}

	/** 
	 * 取得建立人員號碼<p/>
	 * 系統轉換：NOTES<br/>
	 *  自行附加：員工編號
	 */
	public String getCreator() {
		return this.creator;
	}
	/**
	 *  設定建立人員號碼<p/>
	 *  系統轉換：NOTES<br/>
	 *  自行附加：員工編號
	 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 
	 * 取得建立日期<p/>
	 * current timestamp
	 */
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/**
	 *  設定建立日期<p/>
	 *  current timestamp
	 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 
	 * 取得異動人員號碼<p/>
	 * 系統轉換：NOTES<br/>
	 *  自行附加：員工編號
	 */
	public String getUpdater() {
		return this.updater;
	}
	/**
	 *  設定異動人員號碼<p/>
	 *  系統轉換：NOTES<br/>
	 *  自行附加：員工編號
	 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 
	 * 取得異動日期<p/>
	 * current timestamp
	 */
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/**
	 *  設定異動日期<p/>
	 *  current timestamp
	 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
