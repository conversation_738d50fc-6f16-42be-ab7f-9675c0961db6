package com.mega.eloan.lms.crs.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;

import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.dao.C241M01ADao;
import com.mega.eloan.lms.dao.C241M01BDao;
import com.mega.eloan.lms.dao.C241M01CDao;
import com.mega.eloan.lms.dao.C241M01EDao;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.sso.service.BranchService;

/**
 * 產生簽報書PDF
 * 
 * <AUTHOR>
 * 
 */
@Service("lms2405r01rptservice")
public class LMS2405R01RptServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS2405R01RptServiceImpl.class);

	@Resource
	C241M01ADao C241m01aDao;

	@Resource
	C241M01BDao C241m01bDao;

	@Resource
	C241M01CDao C241m01cDao;

	@Resource
	C241M01EDao C241m01eDao;

	@Resource
	LMS2415R01RptServiceImpl lms2415R01RptServiceImpl;

	@Resource
	CodeTypeService codetypeservice;
	
	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branch;

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception 
	 * @throws IOException 
	 */
	public OutputStream generateReport(PageParameters params) throws IOException, Exception {

		String rptOid = Util.nullToSpace(params.getString("rptOid"));
		Map<InputStream,Integer> pdfNameMap = new LinkedHashMap<InputStream,Integer>();
		List<InputStream> list = new LinkedList<InputStream>();
		String[] dataSplit = rptOid.split("\\|");
		OutputStream outputStream = null;
		Locale locale = null;
		Properties propEloanPage = null;
		Properties rptProperties = null;
		int subLine = 7;
		try {
			locale = LMSUtil.getLocale();
			rptProperties = MessageBundleScriptCreator
			.getComponentResource(LMS2415R01RptServiceImpl.class);
			propEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
			for(String dataTemp : dataSplit){
				outputStream = null;
				String[] data = dataTemp.split("\\^");
				String rptMainId = data[2];
//				String type = data[0];
				List<C241M01B> C241m01bList = null;
				C241m01bList = C241m01bDao.findByMainId(rptMainId);
				
				outputStream = lms2415R01RptServiceImpl.genLMS2415R01(rptMainId,
						locale,rptProperties);
				if(outputStream != null){
					pdfNameMap.put(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()),subLine);
				}else{
					pdfNameMap.put(null,subLine);
				}
				if(C241m01bList.size() > 3){
					outputStream = null;
					outputStream = lms2415R01RptServiceImpl.genLMS2415R02(rptMainId,
							locale,rptProperties);
					if(outputStream != null){
						pdfNameMap.put(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()),subLine);
					}else{
						pdfNameMap.put(null,subLine);
					}
				}
			}
			
			if(pdfNameMap != null && pdfNameMap.size() > 0){
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,propEloanPage.getProperty("PaginationText"), true,locale,subLine);
				list.add(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()));
			}
			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);
		}finally{
			
		}
		return outputStream;
	}

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params) throws IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}

		}
	}

}
