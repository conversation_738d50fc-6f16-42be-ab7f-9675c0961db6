<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<div id="C160S01CTab" class="tabs">
            <ul>
               <li><a href="#C160S01CTab01" ><b><th:block th:text="#{'C160S01C.tab01'}">基本資訊</th:block></b></a></li>
               <li><a href="#C160S01CTab02" ><b><th:block th:text="#{'C160S01C.tab02'}">代償/轉貸/借新還舊</th:block></b></a></li>
            </ul>
            <div class="tabCtx-warp">
                <div id="C160S01CTab01" >
                	<span class="color-red"><th:block th:text="#{'C160S01C.warn1'}">金額及期限不可優於原核准之額度明細表或批覆書。</th:block></span>
					<form id="C160S01CForm" name="C160S01CForm">
						<table class="tb2" width="100%">
							<tr>
								<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M01B.custName'}">借款人名稱</th:block>&nbsp;&nbsp;</td>								
								<td width="35%">
									<span id="custId" class="field" ></span>&nbsp;
									<span id="dupNo" class="field" ></span>&nbsp;
									<span id="custName" class="field" ></span>&nbsp;
								</td>
								
								<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160S01C.loanAmt'}">動撥金額</th:block>&nbsp;&nbsp;</td>
								<td width="35%">
									<input id="approvedAmt" name="approvedAmt" class="required max numeric" maxlength="15" />&nbsp;
									<br/>
									<span class="color-red"><b><th:block th:text="#{'C160S01C.mome01'}"><!--來源>產品資訊[分項金額]--></th:block></b></span>
									<!-- <input type="hidden" id="loanAmt" name="loanAmt" class="number" maxlength="15" /> -->
								</td>
							</tr>
							<tr>
								<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160S01C.cntrNo'}">額度序號</th:block>&nbsp;&nbsp;</td>
								<td width="35%"><span id="cntrNo" class="field" ></span>&nbsp;</td>
								<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160S01C.prodKind'}">產品種類</th:block>&nbsp;&nbsp;</td>
								<td width="35%">
									<span class="field" id="prodKind" name="prodKind" ></span>&nbsp;
									<span id="prodKindNm" class="field" ></span>&nbsp;
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.rateDesc'}">利率</th:block>&nbsp;&nbsp;</td>
								<td ><span id="rateDesc" class="field" ></span>&nbsp;</td>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.Deadline'}">期限</th:block>&nbsp;&nbsp;</td>
								<td >
									<!-- <input type="text" id="year" name="year" class="max number" maxlength="4" size="3" /> -->
									<span id="year" class="field" ></span>
									<th:block th:text="#{'C160S01C.year'}">年</th:block>
									<!--<select id="Month" name="Month" codeType="month" ></select>-->
									<span id="Month" class="field" ></span><th:block th:text="#{'C160S01C.Month'}">月</th:block>
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.lnSelect'}">授信期間</th:block>&nbsp;&nbsp;</td>
								<td>
									<input type="text" id="lnStartDate" name="lnStartDate" class="date " />
									~<input type="text" id="lnEndDate" name="lnEndDate" class="date " />
									<!--
									<select id="lnSelect" name="lnSelect" codeType="L140S02A_lnSelect" ></select>
									<span id="lnSelectSpan1" class="lnSelect" >
										<input type="text" id="lnStartDate" name="lnStartDate" class="date"  />~<input type="text" id="lnEndDate" name="lnEndDate" class="date"  />
									</span>
									<span id="lnSelectSpan2" class="lnSelect" >
										<input type="text" id="lnYear" name="lnYear" class="max number" maxlength="2" size="1" /><th:block th:text="#{'C160S01C.year'}">年</th:block>
										<input type="text" id="lnMonth" name="lnMonth" class="max number" maxlength="2" size="1" /><th:block th:text="#{'C160S01C.Month'}">月</th:block>
									</span>
									<input type="text" id="lnOther" name="lnOther" class="max lnSelect" maxlength="60" />
									-->
								</td>
								<td class="hd2" align="right" flag="nowFrom"><th:block th:text="#{'C160S01C.nowFrom'}">寬限期</th:block>&nbsp;&nbsp;</td>
								<td flag="nowFrom">
									<th:block th:text="#{'C160S01C.nowFromStart'}">起期</th:block>
									<input type="text" id="nowFrom" name="nowFrom" class="max number" maxlength="3" size="4" />
									<th:block th:text="#{'C160S01C.nowFromEnd'}">迄期</th:block>
									<input type="text" id="nowEnd" name="nowEnd" class="max number" maxlength="3" size="4"/>
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.useLine'}">動用期間</th:block>&nbsp;&nbsp;</td>
								<td>
									<input type="text" id="useStartDate" name="useStartDate" class="date required" />
									~<input type="text" id="useEndDate" name="useEndDate" class="date required" />
									<!--
									<select id="useLine" name="useLine" codeType="lms1405s02_useDeadline" ></select>
									<span id="useLineSpan1" class="useLine" >
										<input type="text" id="useStartDate" name="useStartDate" class="date"  />~<input type="text" id="useEndDate" name="useEndDate" class="date"  />
									</span>
									<span id="useLineSpan2" class="useLine" >
										<input type="text" id="useYear" name="useYear" class="max number" maxlength="2" size="1" /><th:block th:text="#{'C160S01C.year'}">年</th:block>
										<input type="text" id="useMonth" name="useMonth" class="max number" maxlength="2" size="1" /><th:block th:text="#{'C160S01C.Month'}">月</th:block>
									</span>
									<input type="text" id="useOther" name="useOther" class="max useLine" maxlength="60" />
									-->
								</td>
								<td class="hd2" align="right" flag="nowFrom"><th:block th:text="#{'C160S01C.overdueReason'}">逾期原因</th:block>&nbsp;&nbsp;</td>
								<td flag="nowFrom">
									<textarea id="overdueReason" name="overdueReason" cols="40" maxlength="200" maxlengthc="200" style="width: 260px; height: 30px;"></textarea>
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.pmt_1st_rt_dt'}">首次還款日</th:block>&nbsp;&nbsp;</td>
								<td colspan="3">
									<input type="text" id="pmt_1st_rt_dt" name="pmt_1st_rt_dt" class="date " /> &nbsp;&nbsp;&nbsp;&nbsp;
									<!--※<th:block th:text="#{'C160S01C.pmt_1st_rt_dt.memo_msg'}">目前僅「產品種類71:歡喜信貸」適用</th:block>&nbsp;-->
								</td>
							</tr>
							<tr class="payType" >
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.payType'}">扣帳方式</th:block>&nbsp;&nbsp;</td>
								<td ><select id="payType" name="payType" codeType="cls1161m01_payType"  class="required"></select></td>
								<td class="hd2" align="right">&nbsp;&nbsp;</td>
								<td ><span class="field" ></span>&nbsp;</td>
							</tr>
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.autoPay'}">自動扣帳</th:block>&nbsp;&nbsp;</td>
								<td ><input type="radio" id="autoPay" name="autoPay" codeType="Common_YesNo" itemStyle="sort:desc" /></td>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.atpayNo'}">扣款帳號</th:block>&nbsp;&nbsp;</td>
								<td>
									<table width="100%">
										<tr>
											<td>
												<b class="text-black">
													<th:block th:text="#{'C160S01C.account017'}">本行帳號</th:block>：<br/>
												</b>
												<b class="text-red">
													<th:block th:text="#{'C160S01C.accountMemo'}">※存款帳號如果當日才開戶者因時間落差無法提供引進請自行輸入即可。</th:block><br/>
												</b>
												<input type="text" id="atpayNo" name="atpayNo" class="max" maxlength="16" />&nbsp;
												<button type="button" id="atpayNoPullin" >
													<span class="text-only"><th:block th:text="#{'button.pullin'}">引進</th:block></span>
												</button>
											</td>
										</tr>
										
										<tr>
											<td>
												<b class="text-black">
													<th:block th:text="#{'C160S01C.otherBankAccount'}">他行帳號</th:block>：
												</b>
												<input type="hidden" id="ploanIsNeedACH" name="ploanIsNeedACH" />
												<input type="hidden" id="achBankNo" name="achBankNo" />
												<input type="hidden" id="achBankNm" name="achBankNm" />
												<button type="button" id="achBankNoPullin" >
													<span class="text-only"><th:block th:text="#{'button.pullin'}">引進</th:block></span>
												</button>
												<br/>
												<input type="text" id="achBranchNo" name="achBranchNo" class="max" size="7" maxlength="7" />&nbsp;
												<input type="text" id="achBranchNm" name="achBranchNm" class="max" size="20" maxlength="20" />
												<br/>
												<input type="text" id="achAccount" name="achAccount" class="max" maxlength="16" />
												<input type="hidden" id="achServId" name="achServId" />
											</td>
										</tr>
									</table>
								</td>
							</tr>
							
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.autoRct'}">自動進帳</th:block>&nbsp;&nbsp;</td>
								<td ><input type="radio" id="autoRct" name="autoRct" codeType="Common_YesNo" itemStyle="sort:desc" /></td>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.accNo'}">存款帳號/進帳帳號</th:block>&nbsp;&nbsp;</td>
								<td >
									<table width="100%">
										<tr>
											<td>
												<b class="text-black">
													<th:block th:text="#{'C160S01C.account017'}">本行帳號</th:block>：<br/>
												</b>
												<input type="text" id="accNo" name="accNo" class="max" maxlength="16" />&nbsp;
												<button type="button" id="accNoPullin" >
													<span class="text-only"><th:block th:text="#{'button.pullin'}">引進</th:block></span>
												</button>
											</td>
										</tr>
										
										<tr>
											<td>
												<b class="text-black">
													<th:block th:text="#{'C160S01C.otherBankAccount'}">他行帳號</th:block>：
												</b>
												<input type="hidden" id="appOtherBankNo"  name="appOtherBankNo" />
												<input type="hidden" id="appOtherBankNm"  name="appOtherBankNm" />
												<button type="button" id="appOtherBankNoPullin" >
													<span class="text-only"><th:block th:text="#{'button.pullin'}">引進</th:block></span>
												</button>
												<br/>
												<input type="text" id="appOtherBranchNo" name="appOtherBranchNo" class="max" size="7" maxlength="7" />&nbsp;
												<input type="text" id="appOtherBranchNm" name="appOtherBranchNm" size="20" />
												<br/>
												<input type="text" id="appOtherAccount" name="appOtherAccount" size="16" maxlength="16" />
											</td>
										</tr>
									</table>
								</td>
							</tr>
							<tr id="autoRctTr" >
								<td class="hd2" align="right"><span id="span_c160s01c_rctAmt"><!--進帳金額--></span>&nbsp;&nbsp;
								</td>
								<td ><input type="text" id="rctAMT" name="rctAMT" class="max numeric" maxlength="15" /> &nbsp;&nbsp;&nbsp;
									<button type="button" id="rmCalc" class="for_prod_ReverseMortgage" style='display:none;'>計算</button>
									
									<div class="for_prod_ReverseMortgage" style='display:none;'>
										<span class="color-red"><b><span id="span_memo_rctAmt"><!--簽報書>產品資訊[以房養老每期**]--></span>&nbsp;<span id="l140s02a_rmRctAmt" value="" ></span></b></span>
									</div>
								</td>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.rctDate'}">進帳日期</th:block>&nbsp;&nbsp;</td>
								<td ><input type="text" id="rctDate" name="rctDate" class="date"  /></td>
							</tr>
							<tr class="for_prod_ReverseMortgage"  style='display:none;'>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.rmIntMax'}">以房養老每期利息上限金額</th:block>&nbsp;&nbsp;</td>
								<td ><input type="text" id="rmIntMax" name="rmIntMax" class="numeric" readonly  /></td>
								<td class="hd2" align="right"><th:block th:text="#{'label.lnf030_dp_sday'}">以房養老進帳基準日</th:block>&nbsp;&nbsp;</td>
								<td ><span class="text-red">＊將以 進帳日期 末2碼(日)，作為 a-loan 每期撥款的進帳基準日</span>
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right">
									<span class="text-red">＊</span>
									<th:block th:text="#{'C160S01C.ctrType'}">契約書種類</th:block>&nbsp;&nbsp;
								</td>
								<td colspan="3" >
									<div>
										<label><input type='radio' id='ctrType' name='ctrType' value='1' >個人購屋貸款契約書</label> &nbsp;&nbsp;&nbsp;&nbsp;
										<label><input type='radio' id='ctrType' name='ctrType' value='2' >借款契約書（信用貸款）</label> &nbsp;&nbsp;&nbsp;&nbsp;
										<label><input type='radio' id='ctrType' name='ctrType' value='3' >借款契約書（其他貸款）</label>
									</div>
									<div class="for_prod_ReverseMortgage"  style='display:none;'>
										<label><input type='radio' id='ctrType' name='ctrType' value='4' >歡喜樂活貸款契約書(以房養老)</label> 
									</div>
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.dTitle1'}">計收延遲利息/違約金計算條件</th:block>&nbsp;&nbsp;</td>
								<td colspan="3" >
									<span class="color-red"><th:block th:text="#{'C160S01C.warn2'}">以下欄位請一定要輸入，若案件轉入催收系統時可依此計算違約金</th:block></span>									
									<table class="tb2" width="100%">
										<!-- ****   -->
										<th:block th:if="${show_dMonth_dRate_V20190920_visible}">
										<tr>
											<td width="25%" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160S01C.dRateAdd'}">計收延遲利息加碼</th:block>：</td>
											<td>計收延遲利息加碼<input type="text" id="dRateAdd" name="dRateAdd" readonly  class="max number" maxlength="5" size="4"/>%</td>
										</tr>
										<tr>
											<td align="right"><span class="text-red">＊</span><th:block th:text="#{'label.DelayedInterestCondTitle'}">延遲利息計算條件</th:block>：</td>
											<td><th:block th:text="#{'label.DelayedInterestCond'}">借戶如延遲還本，本金自到期日起按貸款(借款)利率計收延遲利息。</th:block>
											</td>
										</tr>
										<tr>
											<td  align="right"><span class="text-red">＊</span><th:block th:text="#{'C160S01C.dTitle2'}">違約金計算條件</th:block>： </td>
											<td>
												<th:block th:text="#{'label.dMonthdRate.part1'}">借戶如延遲還本或付息(即寬限期內分期償還之中長期案件、按月付息到期一次還本之貸款)時，本金自到期日起，利息自繳息日起，	</th:block>											
												<th:block th:text="#{'label.dMonthdRate.part2A'}">逾期在</th:block><input type="text" id="dMonth1" name="dMonth1" readonly class="max number" maxlength="1" size="1"/>
												<th:block th:text="#{'label.dMonthdRate.part2B'}">個月以內部份，按貸款(借款)利率</th:block><input type="text" id="dRate1" name="dRate1" readonly class="max number" maxlength="2" size="2"/>
												<th:block th:text="#{'label.dMonthdRate.part2C'}">，逾期超過</th:block><input type="text" id="dMonth2" name="dMonth2" readonly class="max number" maxlength="1" size="1"/>
												<th:block th:text="#{'label.dMonthdRate.part2D'}">個月部份，按貸款(借款)利率</th:block><input type="text" id="dRate2" name="dRate2" readonly class="max number" maxlength="2" size="2"/>
												<th:block th:text="#{'label.dMonthdRate.part2E'}">計付違約金。</th:block> 
												<div class='section_penaltyMaxContTm'>
													<th:block th:text="#{'label.PenaltyMaxContTm.part1'}">每次違約狀態最高連續收取期數為</th:block><input type="text" id="penaltyMaxContTm" name="penaltyMaxContTm" readonly class="max number" maxlength="1" size="1"/><th:block th:text="#{'label.PenaltyMaxContTm.part2'}">期。 </th:block>
												</div>
											</td>
										</tr>
										</th:block>
										<!-- ****   -->
										<th:block th:if="${show_dMonth_dRate_UNKNOWN_visible}">
										<tr>
											<td width="25%" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160S01C.dRateAdd'}">計收延遲利息加碼</th:block>：</td>
											<td>計收延遲利息加碼<input type="text" id="dRateAdd" name="dRateAdd" class="max number" maxlength="5" size="4"/>%</td>
										</tr>
										<tr>
											<td  align="right"><span class="text-red">＊</span><th:block th:text="#{'C160S01C.dTitle2'}">違約金計算條件</th:block>： </td>
											<td>
												借戶如延遲還本或付息時，本金自到期日起，利息自繳息日起，逾期在<input type="text" id="dMonth1" name="dMonth1" class="max number" maxlength="2" size="2"/>個月以內部份， 
												按約定利率百分之<input type="text" id="dRate1" name="dRate1" class="max number" maxlength="2" size="2"/>，
												逾期超過<input type="text" id="dMonth2" name="dMonth2" class="max number" maxlength="2" size="2"/>個月部份，
												按約定利率百分之<input type="text" id="dRate2" name="dRate2" class="max number" maxlength="2" size="2"/>計付違約金。 
												<div class='section_penaltyMaxContTm'>
													每次違約狀態最高連續收取期數為<input type="text" id="penaltyMaxContTm" name="penaltyMaxContTm" class="max number" maxlength="2" size="2"/>期。 
												</div>
											</td>
										</tr>
										</th:block>
										<!-- ****   -->
									</table>
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.getDate'}">所有權取得日</th:block>&nbsp;&nbsp;</td>
								<td ><input type="text" id="getDate" name="getDate" class="date"  /></td>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.efctBH'}">行銷分行</th:block>&nbsp;&nbsp;</td>
								<td >
									<span id="efctBH" class="field" ></span>&nbsp;<span id="efctBHName" class="field" ></span>&nbsp;
									<button type="button" id="efctBHPullin" >
										<span class="text-only"><th:block th:text="#{'button.pullin'}">引進</th:block></span>
									</button>
								</td>
							</tr>

							<!-- ACH扣帳方式 -->
							<!--
							<tr class="ach" >
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.achBankNm'}">銀行名稱</th:block>&nbsp;&nbsp;</td>
								<td >
									<span id="achBankNo" class="field" ></span>&nbsp;<span id="achBankNm" class="field" ></span>&nbsp;
									<button type="button" id="achBankNoPullin" >
										<span class="text-only"><th:block th:text="#{'button.pullin'}">引進</th:block></span>
									</button>
								</td>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.achBranchNm'}">分行名稱</th:block>&nbsp;&nbsp;</td>
								<td ><span id="achBranchNo" class="field" ></span>&nbsp;<span id="achBranchNm" class="field" ></span></td>
							</tr>
							<tr class="ach" >
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.achAccount'}">帳號</th:block>&nbsp;&nbsp;</td>
								<td ><input type="text" id="achAccount" name="achAccount" class="max required" maxlength="14" /></td>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.achServId'}">服務單位統編</th:block>&nbsp;&nbsp;</td>
								<td ><input type="text" id="achServId" name="achServId" class="max required" maxlength="10" /></td>
							</tr>
							 -->
							<!-- J-103-0001各項費用調整為多筆方式處理
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'C160S01C.agencyAMT'}">開辦費</th:block>&nbsp;&nbsp;</td>
								<td ><input type="text" id="agencyAMT" name="agencyAMT" class="max number" maxlength="15" /></td>
								
							</tr>
							-->
							<tr></tr>
						</table>
					</form>
				</div>
				<div id="C160S01CTab02" >
					<form id="C160S01EForm" name="C160S01EForm">
						<table class="tb2" width="100%">
						<!--
							Rex_edit 2013_04_03
							<tr>
								<td width="50%" class="hd2" align="right"><th:block th:text="#{'C160S01E.chgCase'}">是否辦理「代償/轉貸/借新還舊」</th:block>&nbsp;&nbsp;</td>
								<td width="50%"><input type="radio" id="chgCase" name="chgCase" codeType="Common_YesNo" itemStyle="sort:desc" /></td>
							</tr>
						-->
							<tr >
								<td width="50%" class="hd2" align="right"><th:block th:text="#{'C160S01E.chgOther'}">本筆借款是否用來償還其他筆貸款</th:block>&nbsp;&nbsp;</td>
								<td width="50%"><input type="radio" id="chgOther" name="chgOther" codeType="Common_YesNo" itemStyle="sort:desc" class="required" /></td>
							</tr>
							<tr class="chgCase" >
								<td class="hd2" align="right"><th:block th:text="#{'C160S01E.chgBorrower'}">該其他筆貸款之主借款人是否和本案主借款人相同</th:block>&nbsp;&nbsp;</td>
								<td ><input type="radio" id="chgBorrower" name="chgBorrower" codeType="Common_YesNo" itemStyle="sort:desc" class="required" /></td>
							</tr>
							<tr style="display:none" >
								<td class="hd2" align="right"><th:block th:text="#{'C160S01E.chgALoan'}">是否為本行貸款</th:block>&nbsp;&nbsp;</td>
								<td ><input type="radio" id="chgALoan" name="chgALoan" codeType="Common_YesNo" itemStyle="sort:desc" class="required" disabled='disabled' /></td>
							</tr>
							<tr class="chgCase" >
								<td colspan="2">
									<th:block th:text="#{'C160S01E.onlentDate'}">轉貸日期</th:block>
									<input type="text" id="onlentDate" name="onlentDate" class="date" /><br/>
									<span class="text-red">(<th:block th:text="#{'C160S01E.warn1'}">此欄位即本行代償他行貸款之撥款日！若選擇自動進帳則進帳日期要與轉貸日期相同</th:block>。)</span><br/>
									<span class="text-red">＊</span><th:block th:text="#{'C160S01E.chargeFlag'}">是否收取手續費</th:block>
									<input type="radio" id="chargeFlag" name="chargeFlag" codeType="Common_YesNo" itemStyle="sort:desc" /><br/>
									<span id="spanChargeAmt" >
										NT$<input type="text" id="chargeAmt" name="chargeAmt" class="max number required" maxlength="13" /><th:block th:text="#{'C160S01E.msg1'}">元，應由借款人出具「轉貸約定書」</th:block>。
									</span> 
								</td>
							</tr>
						</table>
					</form>
					<div  class="chgCase">
						<button type="button" id="C160S01F_add" >
							<span class="text-only"><th:block th:text="#{'button.add'}">新增</th:block></span>
						</button>
						<button type="button" id="C160S01F_delete" >
							<span class="text-only"><th:block th:text="#{'button.delete'}">刪除</th:block></span>
						</button>
						<div id="C160S01FGrid" ></div>
					</div>
				</div>
			</div>
		</div>
	</th:block>
</body>
</html>
