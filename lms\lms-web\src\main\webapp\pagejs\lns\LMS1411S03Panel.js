initDfd.done(function(json){

	
	
	// 共同借款人
	var gridComPeople=$("#gridComPeople").iGrid({
		handler : "lms1411gridhandler",
		height : "230px",
		width : "100%",
		sortname : 'custId',
		postData : {
			formAction : "queryL141m01c",
				mainId:$("#mainId").val()
		},
		autowidth : true,
		colModel : [ {
			colHeader : i18n.lms1411m01['L141M01C.custId'],//統編
			name : 'custId',
			width : 100,
			sortable : true,
			align : "left"
		}, {
			colHeader : i18n.lms1411m01['L141M01C.custName'],//姓名
			name : 'custName',
			width : 90,
			sortable : true,
			align : "center"
		}, {
			colHeader : i18n.lms1411m01['L141M01C.ntCode'],// 國別
			name : 'ntCode',
			width : 90,
			sortable : true,
			align : "center"
		}, {
			colHeader : i18n.lms1411m01['L141M01C.custRlt'],//與主要借款人關係
			name : 'custRlt',
			width : 60,
			sortable : true,
			align : "center"
		}, {
			name : 'oid',
			hidden : true
		}, {
			name : 'mainId',
			hidden : true
		} ]
	});
});