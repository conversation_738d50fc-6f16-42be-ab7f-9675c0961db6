/* 
 * C122S01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C122S01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C122S01B;

/** 線上增貸核貸批號明細 **/
@Repository
public class C122S01BDaoImpl extends LMSJpaDao<C122S01B, String>
	implements C122S01BDao {

	@Override
	public C122S01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C122S01B findByMainId(String  mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId",  mainId);
		return findUniqueOrNone(search);
	}
	@Override
	public List<C122S01B> findByMainIdBatchNo(String mainId, Integer batchNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "batchNo", batchNo);
		List<C122S01B> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C122S01B findByUniqueKey(String mainId, Integer batchNo, String cntrNoMainId, Integer seq){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "batchNo", batchNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNoMainId", cntrNoMainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}
}