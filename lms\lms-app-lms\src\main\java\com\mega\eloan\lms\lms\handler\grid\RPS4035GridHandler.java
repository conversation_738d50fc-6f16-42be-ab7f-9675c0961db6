package com.mega.eloan.lms.lms.handler.grid;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;


import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.lms.pages.RPS4035V00Page;
import com.mega.eloan.lms.lms.service.RPS4035Service;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 婉卻記錄查詢
 * </pre>
 * 
 * @since 2012/2/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/14,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("rps4035gridhandler")
public class RPS4035GridHandler extends AbstractGridHandler {

	@Resource
	RPS4035Service rps4035Service;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	/**
	 * 查詢LocalGrid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 * @throws ParseException
	 */
	public CapMapGridResult queryLocalGrid(ISearch pageSetting,
			PageParameters params) throws CapException,
			ParseException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(RPS4035V00Page.class);
		String custId = params.getString("custId", "").toUpperCase();
		String dupNo = params.getString("dupNo", "").toUpperCase();
		String[] codeType = { "RejtCode" };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);

		Page<Map<String, Object>> lnunids = rps4035Service.getLnunIdByCustId(
				custId, dupNo, pageSetting);

		// 當無婉卻記錄則要顯示出訊息到Grid上
		if (lnunids.getContent().isEmpty()) {
			Map<String, Object> tempModel = new HashMap<String, Object>();
			tempModel.put("regdt", pop.getProperty("lnunid.noCase"));
			lnunids.getContent().add(tempModel);
			return new CapMapGridResult(lnunids.getContent(),
					lnunids.getTotalRow());
		}
		String def = "";
		for (Map<String, Object> lnunid : lnunids.getContent()) {
			lnunid.put("regdt", CapDate.formatDate((Date) lnunid.get("regdt"),
					UtilConstants.DateFormat.YYYY_MM_DD));
			lnunid.put(
					"clscase",
					pop.getProperty(StrUtils.concat("lnunid.clscase",
							lnunid.get("clscase"))));
			def = Util.trim(lnunid.get("refusecd"));
			lnunid.put("refuseds", StrUtils.concat(codeMap.get("RejtCode")
					.containsKey(def) ? codeMap.get("RejtCode").get(def) : "",
					"<br/>", lnunid.get("refuseds")));
			
			lnunid.put("statuscd", lnunid.get("statuscd"));
			

			lnunid.put("regbr",
					branchService.getBranchName((String) lnunid.get("regbr")));
		}

		return new CapMapGridResult(lnunids.getContent(), lnunids.getTotalRow());
	}

	/**
	 * 查詢HoldingGrid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryHoldingGrid(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(RPS4035V00Page.class);
		String cUSTID = params.getString("custId", "").toUpperCase();
		String dupNo = params.getString("dupNo", "").toUpperCase();
		Page<Map<String, Object>> list = rps4035Service.getLnunId02ByCustId(
				cUSTID, dupNo, pageSetting);
		// 當無婉卻記錄則要顯示出訊息到Grid上
		if (list.getContent().isEmpty()) {
			Map<String, Object> tempModel = new HashMap<String, Object>();
			tempModel.put("regdt", pop.getProperty("lnunid.noCase"));
			list.getContent().add(tempModel);
			return new CapMapGridResult(list.getContent(), list.getTotalRow());
		}

		for (Map<String, Object> lnunid : list.getContent()) {
			lnunid.put("regdt", CapDate.formatDate((Date) lnunid.get("regdt"),
					UtilConstants.DateFormat.YYYY_MM_DD));

		}

		return new CapMapGridResult(list.getContent(), list.getTotalRow());
	}
}
