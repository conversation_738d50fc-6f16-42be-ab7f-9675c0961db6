/* 
 * L120S03A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 資本適足率影響數資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name="L120S03A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","cntrMainId","cntrNo"}))
public class L120S03A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號(簽報書) **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 文件編號(額度明細表) **/
	@Column(name="CNTRMAINID", length=32, columnDefinition="CHAR(32)")
	private String cntrMainId;

	/** 額度序號 **/
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 
	 * 信保/非信保<p/>
	 * 信保<br/>
	 *  非信保
	 */
	@Column(name="CRDFLAG", length=1, columnDefinition="CHAR(01)")
	private String crdFlag;

	/** 
	 * 原始申請額度<p/>
	 * H.
	 */
	@Column(name="APPLYAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal applyAmt;

	/** 
	 * 授信額度<p/>
	 * A.<br/>
	 *  (若為進口押匯額度需乘以20%。<br/>
	 *  若為衍生性金融商品需乘以適當風險轉換係數)
	 */
	@Column(name="FCLTAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal fcltAmt;

	/** 
	 * 合格擔保品抵減額<p/>
	 * (非信保)B.
	 */
	@Column(name="COLLAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal collAmt;

	/** 
	 * 風險權數（保證人請註記）<p/>
	 * (非信保)C.
	 */
	@Column(name="RSKRATIO", columnDefinition="DECIMAL(5,2)")
	private Double rskRatio;

	/** 
	 * 風險抵減後曝險額<p/>
	 * (非信保)D.(A-B）×C
	 */
	@Column(name="RSKAMT1", columnDefinition="DECIMAL(15,2)")
	private BigDecimal rskAmt1;

	/** 
	 * 抵減後風險權數<p/>
	 * (非信保)D-1
	 */
	@Column(name="RSKR1", columnDefinition="DECIMAL(5,2)")
	private Double rskr1;

	/** 
	 * 資本使用額（免填）<p/>
	 * (非信保)E
	 */
	@Column(name="CAMT1", columnDefinition="DECIMAL(15,2)")
	private BigDecimal camt1;

	/** 
	 * 占資本適足率<p/>
	 * (非信保)F
	 */
	@Column(name="BISR1", columnDefinition="DECIMAL(8,5)")
	private Double bisr1;

	/** 
	 * 資金成本率<p/>
	 * (非信保)G
	 */
	@Column(name="COSTR1", columnDefinition="DECIMAL(8,5)")
	private Double costr1;

	/** 
	 * 信保保證成數(%)<p/>
	 * (信保)B.
	 */
	@Column(name="CRDRATIO", columnDefinition="DECIMAL(5,2)")
	private Double crdRatio;

	/** 
	 * 本行自貸曝險風險權數<p/>
	 * (信保)B1.本行自貸曝險A*（1-B）<br/>
	 *  風險權數100%
	 */
	@Column(name="RSKMEGA", columnDefinition="DECIMAL(15,2)")
	private Double rskMega;

	/** 
	 * 信保曝險風險權數<p/>
	 * (信保)B3.(A-B1) X 20%<br/>
	 *  信保曝險(風險權數20%)
	 */
	@Column(name="RSKCRD", columnDefinition="DECIMAL(15,2)")
	private BigDecimal rskCrd;

	/** 
	 * 抵減後全部曝險額<p/>
	 * (信保)C
	 */
	@Column(name="RSKAMT2", columnDefinition="DECIMAL(15,2)")
	private BigDecimal rskAmt2;

	/** 
	 * 抵減後風險權數<p/>
	 * (信保)D
	 */
	@Column(name="RSKR2", columnDefinition="DECIMAL(15,2)")
	private Double rskr2;

	/** 
	 * 資本使用額（免填）<p/>
	 * (信保)E
	 */
	@Column(name="CAMT2", columnDefinition="DECIMAL(15,2)")
	private BigDecimal camt2;

	/** 
	 * 占資本適足率<p/>
	 * (信保)F
	 */
	@Column(name="BISR2", columnDefinition="DECIMAL(8,5)")
	private Double bisr2;

	/** 
	 * 資金成本率<p/>
	 * (信保)G
	 */
	@Column(name="COSTR2", columnDefinition="DECIMAL(8,5)")
	private Double costr2;

	/** 
	 * 輸入資料檢誤完成(Y/N)<p/>
	 * 100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name="CHKYN", length=1, columnDefinition="CHAR(1)")
	private String chkYN;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 信保風險權數(%)<p/>
	 * (B').
	 */
	@Column(name="CRDRSKRATIO", columnDefinition="DECIMAL(5,2)")
	private Double crdRskRatio;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號(簽報書) **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號(簽報書) **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得文件編號(額度明細表) **/
	public String getCntrMainId() {
		return this.cntrMainId;
	}
	/** 設定文件編號(額度明細表) **/
	public void setCntrMainId(String value) {
		this.cntrMainId = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 
	 * 取得信保/非信保<p/>
	 * 信保<br/>
	 *  非信保
	 */
	public String getCrdFlag() {
		return this.crdFlag;
	}
	/**
	 *  設定信保/非信保<p/>
	 *  信保<br/>
	 *  非信保
	 **/
	public void setCrdFlag(String value) {
		this.crdFlag = value;
	}

	/** 
	 * 取得原始申請額度<p/>
	 * H.
	 */
	public BigDecimal getApplyAmt() {
		return this.applyAmt;
	}
	/**
	 *  設定原始申請額度<p/>
	 *  H.
	 **/
	public void setApplyAmt(BigDecimal value) {
		this.applyAmt = value;
	}

	/** 
	 * 取得授信額度<p/>
	 * A.<br/>
	 *  (若為進口押匯額度需乘以20%。<br/>
	 *  若為衍生性金融商品需乘以適當風險轉換係數)
	 */
	public BigDecimal getFcltAmt() {
		return this.fcltAmt;
	}
	/**
	 *  設定授信額度<p/>
	 *  A.<br/>
	 *  (若為進口押匯額度需乘以20%。<br/>
	 *  若為衍生性金融商品需乘以適當風險轉換係數)
	 **/
	public void setFcltAmt(BigDecimal value) {
		this.fcltAmt = value;
	}

	/** 
	 * 取得合格擔保品抵減額<p/>
	 * (非信保)B.
	 */
	public BigDecimal getCollAmt() {
		return this.collAmt;
	}
	/**
	 *  設定合格擔保品抵減額<p/>
	 *  (非信保)B.
	 **/
	public void setCollAmt(BigDecimal value) {
		this.collAmt = value;
	}

	/** 
	 * 取得風險權數（保證人請註記）<p/>
	 * (非信保)C.
	 */
	public Double getRskRatio() {
		return this.rskRatio;
	}
	/**
	 *  設定風險權數（保證人請註記）<p/>
	 *  (非信保)C.
	 **/
	public void setRskRatio(Double value) {
		this.rskRatio = value;
	}

	/** 
	 * 取得風險抵減後曝險額<p/>
	 * (非信保)D.(A-B）×C
	 */
	public BigDecimal getRskAmt1() {
		return this.rskAmt1;
	}
	/**
	 *  設定風險抵減後曝險額<p/>
	 *  (非信保)D.(A-B）×C
	 **/
	public void setRskAmt1(BigDecimal value) {
		this.rskAmt1 = value;
	}

	/** 
	 * 取得抵減後風險權數<p/>
	 * (非信保)D-1
	 */
	public Double getRskr1() {
		return this.rskr1;
	}
	/**
	 *  設定抵減後風險權數<p/>
	 *  (非信保)D-1
	 **/
	public void setRskr1(Double value) {
		this.rskr1 = value;
	}

	/** 
	 * 取得資本使用額（免填）<p/>
	 * (非信保)E
	 */
	public BigDecimal getCamt1() {
		return this.camt1;
	}
	/**
	 *  設定資本使用額（免填）<p/>
	 *  (非信保)E
	 **/
	public void setCamt1(BigDecimal value) {
		this.camt1 = value;
	}

	/** 
	 * 取得占資本適足率<p/>
	 * (非信保)F
	 */
	public Double getBisr1() {
		return this.bisr1;
	}
	/**
	 *  設定占資本適足率<p/>
	 *  (非信保)F
	 **/
	public void setBisr1(Double value) {
		this.bisr1 = value;
	}

	/** 
	 * 取得資金成本率<p/>
	 * (非信保)G
	 */
	public Double getCostr1() {
		return this.costr1;
	}
	/**
	 *  設定資金成本率<p/>
	 *  (非信保)G
	 **/
	public void setCostr1(Double value) {
		this.costr1 = value;
	}

	/** 
	 * 取得信保保證成數(%)<p/>
	 * (信保)B.
	 */
	public Double getCrdRatio() {
		return this.crdRatio;
	}
	/**
	 *  設定信保保證成數(%)<p/>
	 *  (信保)B.
	 **/
	public void setCrdRatio(Double value) {
		this.crdRatio = value;
	}

	/** 
	 * 取得本行自貸曝險風險權數<p/>
	 * (信保)B1.本行自貸曝險A*（1-B）<br/>
	 *  風險權數100%
	 */
	public Double getRskMega() {
		return this.rskMega;
	}
	/**
	 *  設定本行自貸曝險風險權數<p/>
	 *  (信保)B1.本行自貸曝險A*（1-B）<br/>
	 *  風險權數100%
	 **/
	public void setRskMega(Double value) {
		this.rskMega = value;
	}

	/** 
	 * 取得信保曝險風險權數<p/>
	 * (信保)B3.(A-B1) X 20%<br/>
	 *  信保曝險(風險權數20%)
	 */
	public BigDecimal getRskCrd() {
		return this.rskCrd;
	}
	/**
	 *  設定信保曝險風險權數<p/>
	 *  (信保)B3.(A-B1) X 20%<br/>
	 *  信保曝險(風險權數20%)
	 **/
	public void setRskCrd(BigDecimal value) {
		this.rskCrd = value;
	}

	/** 
	 * 取得抵減後全部曝險額<p/>
	 * (信保)C
	 */
	public BigDecimal getRskAmt2() {
		return this.rskAmt2;
	}
	/**
	 *  設定抵減後全部曝險額<p/>
	 *  (信保)C
	 **/
	public void setRskAmt2(BigDecimal value) {
		this.rskAmt2 = value;
	}

	/** 
	 * 取得抵減後風險權數<p/>
	 * (信保)D
	 */
	public Double getRskr2() {
		return this.rskr2;
	}
	/**
	 *  設定抵減後風險權數<p/>
	 *  (信保)D
	 **/
	public void setRskr2(Double value) {
		this.rskr2 = value;
	}

	/** 
	 * 取得資本使用額（免填）<p/>
	 * (信保)E
	 */
	public BigDecimal getCamt2() {
		return this.camt2;
	}
	/**
	 *  設定資本使用額（免填）<p/>
	 *  (信保)E
	 **/
	public void setCamt2(BigDecimal value) {
		this.camt2 = value;
	}

	/** 
	 * 取得占資本適足率<p/>
	 * (信保)F
	 */
	public Double getBisr2() {
		return this.bisr2;
	}
	/**
	 *  設定占資本適足率<p/>
	 *  (信保)F
	 **/
	public void setBisr2(Double value) {
		this.bisr2 = value;
	}

	/** 
	 * 取得資金成本率<p/>
	 * (信保)G
	 */
	public Double getCostr2() {
		return this.costr2;
	}
	/**
	 *  設定資金成本率<p/>
	 *  (信保)G
	 **/
	public void setCostr2(Double value) {
		this.costr2 = value;
	}

	/** 
	 * 取得輸入資料檢誤完成(Y/N)<p/>
	 * 100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}
	/**
	 *  設定輸入資料檢誤完成(Y/N)<p/>
	 *  100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	
	/** 設定信保風險權數 **/
	public void setCrdRskRatio(Double crdRskRatio) {
		this.crdRskRatio = crdRskRatio;
	}
	/** 取得信保風險權數 **/
	public Double getCrdRskRatio() {
		return crdRskRatio;
	}
}
