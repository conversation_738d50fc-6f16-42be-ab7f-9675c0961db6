/* 
 *  MicroEntFormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.handler.form;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.panels.LMSS23APanel;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.MicroEntService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S10A;
import com.mega.eloan.lms.model.L120S10B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L164S01A;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("microentformhandler")
public class MicroEntFormHandler extends AbstractFormHandler {

	Properties pop = MessageBundleScriptCreator
			.getComponentResource(LMSS23APanel.class);

	@Resource
	MicroEntService microEntService;

	@Resource
	EjcicService ejcicService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	LMSService lmsService;

	/**
	 * 開起時查詢
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s10a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L120S10A l120s10a = microEntService.findL120s10aByOid(oid);
		if (l120s10a == null) { // 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		CapAjaxFormResult myForm2Result = DataParse.toResult(l120s10a);

		result.set("S23Form", myForm2Result);
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		List<L120S10A> l120s10as = microEntService.findL120s10aByMainId(mainId);
		if (l120s10as != null && !l120s10as.isEmpty()) {
			microEntService.deleteListL120s10a(l120s10as);
		}

		// 開始引進名單開始
		L120M01A l120m01a = microEntService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a != null) {
			this.importForL120m01a(mainId);
		} else {
			// 動審表
			L160M01A l160m01a = microEntService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				this.importForL160m01a(mainId);
			}
		}

		// 取得J10
		List<L120S10A> newl120s10as = microEntService
				.findL120s10aByMainId(mainId);
		if (newl120s10as != null && !newl120s10as.isEmpty()) {
			TreeMap<String, String> J10_BREACH_MAP = this
					.getJ10DefaultRateByType("1");
			TreeMap<String, String> J10_PERCENTILE_MAP = this
					.getJ10DefaultRateByType("2");

			// 怕無資料時，暫時針對小規模簽報書才去抓資信簡表
			// 先準備好資信簡表資料，免得每一筆明細都要重新查詢

			List<Map<String, Object>> cesJ10List = null;

			if (Util.equals(l120m01a.getMiniFlag(), "Y")
					&& (Util.equals(l120m01a.getCaseType(),
							UtilConstants.Casedoc.caseType.小規模營業人)
							|| Util.equals(l120m01a.getCaseType(),
									UtilConstants.Casedoc.caseType.疫後振興F02) || Util
							.equals(l120m01a.getCaseType(),
									UtilConstants.Casedoc.caseType.疫後振興F02_無利息補貼))) {

				List<String> cesMainIds = new ArrayList<String>();
				Map<String, String> cntrAllCustIdMap = new HashMap<String, String>();

				List<L140M01A> l140m01as = microEntService
						.findL140m01aListByL120m01cMainId(mainId,
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				for (L140M01A l140m01a : l140m01as) {
					// 額度明細表借款人
					String bId = Util.trim(l140m01a.getCustId());
					String bNo = Util.trim(l140m01a.getDupNo());
					String bName = Util.trim(l140m01a.getCustName());
					if (!cntrAllCustIdMap.containsKey(bId + bNo)) {
						cntrAllCustIdMap.put(bId + bNo, bName);
					}
				}

				List<L120M01E> oldList = lmsService
						.findL120m01eByMainId(l120m01a.getMainId());

				if (!oldList.isEmpty()) {

					for (L120M01E model : oldList) {
						if (UtilConstants.Casedoc.L120m01eDocType.資信簡表
								.equals(model.getDocType())) {

							// 從借款人引進資信簡表而來
							if (cntrAllCustIdMap.containsKey(Util.trim(model
									.getDocCustId()
									+ Util.trim(model.getDocDupNo())))) {
								String tCesMainId = Util
										.trim(model.getDocOid());
								// cesMainIds.append(cesMainIds.length() > 0 ?
								// ","
								// : "");
								// cesMainIds.append("'");
								// cesMainIds.append(tCesMainId);
								// cesMainIds.append("'");
								cesMainIds.add(tCesMainId);
							}
						}
					}
				}

				if (CollectionUtils.isNotEmpty(cesMainIds)) {
					// 有資信簡表

					// select * from CES.C120JSON where
					// MAINID='06805c45e8d349a7b57a3be24f13e7a7' and TAB='02'
					// and SUBTAB like 'J%'

					cesJ10List = eloandbBASEService.findCesJ10(cesMainIds
							.toArray(new String[0]));
				}
			}

			for (L120S10A l120s10a : newl120s10as) {
				l120s10a = microEntService.importJ10(l120s10a, J10_BREACH_MAP,
						J10_PERCENTILE_MAP, cesJ10List);
			}
		}

		// J-109-0459_05097_B1001 Web e-Loan簡化微型企業簽報書資僅為動用新台幣案件時得免執行制裁/管制名單掃描。
		result.set("isLmsCaseReportCanPassAml",
				lmsService.isLmsCaseReportCanPassAml(l120m01a) ? "Y" : "N");

		return result;
	}

	public void importForL120m01a(String mainId) {
		// 有ID*9***********************************
		// 借款人+共借人(要掃其負責人與實質受益人)
		Map<String, String> cntrAllCustIdMap = new HashMap<String, String>();
		List<L140M01A> l140m01as = microEntService
				.findL140m01aListByL120m01cMainId(mainId,
						UtilConstants.Cntrdoc.ItemType.額度明細表);
		for (L140M01A l140m01a : l140m01as) {
			// 額度明細表借款人
			String bId = l140m01a.getCustId();
			String bNo = l140m01a.getDupNo();
			String bName = Util.toSemiCharString(Util.trim(l140m01a
					.getCustName()));

			if (!cntrAllCustIdMap.containsKey(bId + bNo)) {
				cntrAllCustIdMap.put(bId + bNo, bName);
			}

			// 額度明細表共同借款人
			Set<L140M01J> l140m01js = l140m01a.getL140m01j();
			for (L140M01J l140m01j : l140m01js) {
				String cId = l140m01j.getCustId();
				String cNo = l140m01j.getDupNo();
				String cName = Util.toSemiCharString(Util.trim(l140m01j
						.getCustName()));

				if (!cntrAllCustIdMap.containsKey(cId + cNo)) {
					cntrAllCustIdMap.put(cId + cNo, cName);
				}
			}

			// 連帶保證人
			Set<L140M01I> l140m01is = l140m01a.getL140m01i();
			for (L140M01I l140m01i : l140m01is) {
				String gId = l140m01i.getRId();
				String gNo = l140m01i.getRDupNo();
				String gName = Util.toSemiCharString(Util.trim(l140m01i
						.getRName()));

				if (Util.equals(UtilConstants.lngeFlag.連帶保證人,
						l140m01i.getRType())) {
					if (Util.notEquals(Util.trim(l140m01a.getGuarantorType()),
							"2")) {
						microEntService
								.reSetL120S10A(
										mainId,
										gId,
										gNo,
										gName,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.連保人);
					}
				}
			}
		}

		// 可能沒有ID*****************************************************************
		// 負責人+實質受益人
		List<L120S01B> l120s01bs = (List<L120S01B>) microEntService
				.findListByMainId(L120S01B.class, mainId);
		if (l120s01bs != null && !l120s01bs.isEmpty()) {
			for (L120S01B l120s01b : l120s01bs) {
				String s01bCustId = Util.trim(l120s01b.getCustId());
				String s01bDupNo = Util.trim(l120s01b.getDupNo());
				// 借款人或共借人才要
				if (cntrAllCustIdMap.containsKey(s01bCustId + s01bDupNo)) {
					// 負責人
					String chairmanId = Util.trim(l120s01b.getChairmanId());
					String chairmanDupNo = Util.trim(l120s01b
							.getChairmanDupNo());
					String chairman = Util.toSemiCharString(Util.trim(l120s01b
							.getChairman()));

					if (Util.notEquals(chairmanId, "")
							|| Util.notEquals(chairman, "")) {
						microEntService
								.reSetL120S10A(
										mainId,
										chairmanId,
										chairmanDupNo,
										chairman,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人);
					}
				}
			}
		}
	}

	public void importForL160m01a(String mainId) {

		// 動審表主要資料來源為MIS TABLE
		L160M01A l160m01a = microEntService.findModelByMainId(L160M01A.class,
				mainId);

		// 有ID*9***********************************
		Map<String, String> cntrCustIdMap = new HashMap<String, String>();
		List<L162S01A> l162s01as = (List<L162S01A>) microEntService
				.findListByMainId(L162S01A.class, l160m01a.getMainId());

		if (l162s01as != null && !l162s01as.isEmpty()) {
			for (L162S01A l162s01a : l162s01as) {
				if (Util.equals(l162s01a.getRType(),
						UtilConstants.lngeFlag.共同借款人)) {
					// 額度明細表借款人
					String bId = l162s01a.getRId();
					String bNo = l162s01a.getRDupNo();
					String bName = Util.toSemiCharString(Util.trim(l162s01a
							.getRName()));

					if (!cntrCustIdMap.containsKey(bId + bNo)) {
						cntrCustIdMap.put(bId + bNo, bName);

						// 相同ID的借戶/共同借款人，負責人、實質受益人、關係企業應該都相同
						// 負責人
						L164S01A l164s01a = microEntService
								.findL164s01aByUniqueKey(mainId, bId, bNo);
						if (l164s01a != null) {
							String chairmanId = Util.trim(l164s01a
									.getChairmanId());
							String chairmanDupNo = Util.trim(l164s01a
									.getChairmanDupNo());
							String chairman = Util.toSemiCharString(Util
									.trim(l164s01a.getChairman()));

							if (Util.notEquals(chairmanId, "")
									|| Util.notEquals(chairman, "")) {
								microEntService
										.reSetL120S10A(
												mainId,
												chairmanId,
												chairmanDupNo,
												chairman,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人);
							}
						}
					}
				}

				if (Util.equals(l162s01a.getRType(),
						UtilConstants.lngeFlag.連帶保證人)) {
					String gId = l162s01a.getRId();
					String gNo = l162s01a.getRDupNo();
					String gName = Util.toSemiCharString(Util.trim(l162s01a
							.getRName()));

					microEntService
							.reSetL120S10A(
									mainId,
									gId,
									gNo,
									gName,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.連保人);
				}
			}
		}
	}

	public TreeMap<String, String> getJ10DefaultRateByType(String type) {
		List<Map<String, Object>> list = eloandbBASEService
				.getJ10DefaultRateByType(type);

		TreeMap<String, String> map = new TreeMap<String, String>();
		BigDecimal hundred = new BigDecimal(100);

		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> jMap = list.get(i);
			BigDecimal score = LMSUtil.toBigDecimal(Util.trim(MapUtils
					.getString(jMap, "SCORE", "0")));
			BigDecimal defaultRate = LMSUtil.toBigDecimal(Util.trim(MapUtils
					.getString(jMap, "DEFAULT_RATE", "0")));
			String rate = hundred.multiply(defaultRate)
					.setScale(2, BigDecimal.ROUND_HALF_UP).toString()
					+ "%";

			if (Util.equals("2", type)) {

				if (i == 0) {
					rate = "0%~" + rate;
				} else {
					Map<String, Object> preMap = list.get(i - 1);
					BigDecimal preDefaultRate = LMSUtil.toBigDecimal(Util
							.trim(MapUtils.getString(preMap, "DEFAULT_RATE",
									"0")));

					rate = hundred.multiply(
							preDefaultRate
									.setScale(2, BigDecimal.ROUND_HALF_UP))
							.toString()
							+ "%" + "~" + rate;
				}
			}

			map.put(score.toPlainString(), rate);
		}
		return map;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = Util.trim(params.getString(EloanConstants.OID));

		L120S10A l120s10a = microEntService.findL120s10aByOid(oid);
		String mainId = l120s10a.getMainId();
		L120M01A l120m01a = microEntService.findModelByMainId(L120M01A.class,
				mainId);

		// 取得J10
		if (l120s10a != null && Util.isNotEmpty(l120s10a)) {
			TreeMap<String, String> J10_BREACH_MAP = this
					.getJ10DefaultRateByType("1");
			TreeMap<String, String> J10_PERCENTILE_MAP = this
					.getJ10DefaultRateByType("2");

			// 怕無資料時，暫時針對小規模簽報書才去抓資信簡表
			// 先準備好資信簡表資料，免得每一筆明細都要重新查詢

			List<Map<String, Object>> cesJ10List = null;

			if (Util.equals(l120m01a.getMiniFlag(), "Y")
					&& (Util.equals(l120m01a.getCaseType(),
							UtilConstants.Casedoc.caseType.小規模營業人)
							|| Util.equals(l120m01a.getCaseType(),
									UtilConstants.Casedoc.caseType.疫後振興F02) || Util
							.equals(l120m01a.getCaseType(),
									UtilConstants.Casedoc.caseType.疫後振興F02_無利息補貼))) {

				List<String> cesMainIds = new ArrayList<String>();
				Map<String, String> cntrAllCustIdMap = new HashMap<String, String>();

				List<L140M01A> l140m01as = microEntService
						.findL140m01aListByL120m01cMainId(mainId,
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				for (L140M01A l140m01a : l140m01as) {
					// 額度明細表借款人
					String bId = Util.trim(l140m01a.getCustId());
					String bNo = Util.trim(l140m01a.getDupNo());
					String bName = Util.trim(l140m01a.getCustName());
					if (!cntrAllCustIdMap.containsKey(bId + bNo)) {
						cntrAllCustIdMap.put(bId + bNo, bName);
					}
				}

				List<L120M01E> oldList = lmsService
						.findL120m01eByMainId(l120m01a.getMainId());

				if (!oldList.isEmpty()) {

					for (L120M01E model : oldList) {
						if (UtilConstants.Casedoc.L120m01eDocType.資信簡表
								.equals(model.getDocType())) {

							// 從借款人引進資信簡表而來
							if (cntrAllCustIdMap.containsKey(Util.trim(model
									.getDocCustId()
									+ Util.trim(model.getDocDupNo())))) {
								String tCesMainId = Util
										.trim(model.getDocOid());
								// cesMainIds.append(cesMainIds.length() > 0 ?
								// ","
								// : "");
								// cesMainIds.append("'");
								// cesMainIds.append(tCesMainId);
								// cesMainIds.append("'");
								cesMainIds.add(tCesMainId);
							}
						}
					}
				}

				if (CollectionUtils.isNotEmpty(cesMainIds)) {
					// 有資信簡表

					// select * from CES.C120JSON where
					// MAINID='06805c45e8d349a7b57a3be24f13e7a7' and TAB='02'
					// and SUBTAB like 'J%'

					cesJ10List = eloandbBASEService.findCesJ10(cesMainIds
							.toArray(new String[0]));
				}

			}

			l120s10a = microEntService.importJ10(l120s10a, J10_BREACH_MAP,
					J10_PERCENTILE_MAP, cesJ10List);

			CapAjaxFormResult myForm2Result = DataParse.toResult(l120s10a);

			result.set("S23Form", myForm2Result);

			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL120s10a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		microEntService.deleteL120s10as(mainId);
		microEntService.deleteL120s10b(mainId);

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		CapAjaxFormResult form = new CapAjaxFormResult();
		L120S10B l120s10b = microEntService.findModelByMainId(L120S10B.class,
				mainId);
		if (l120s10b != null) {
			// 將Model欄位傳到前端Form上
			form = DataParse.toResult(l120s10b, DataParse.Delete, "oid",
					"mainId");
		}
		result.set("LMSS23Form", form);
		return result;
	}
}
