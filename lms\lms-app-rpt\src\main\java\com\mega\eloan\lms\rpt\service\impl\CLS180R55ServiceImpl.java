package com.mega.eloan.lms.rpt.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.rpt.service.CLS180R55Service;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

/**
 * <pre>
 * 待售房屋(餘屋)去化落後追蹤表
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class CLS180R55ServiceImpl extends AbstractCapService implements CLS180R55Service {

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	MisPTEAMAPPService misPTEAMAPPService;
	
	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;

	@Override
	public Map<String, Integer> getTitleMap(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R55.unsoldHouseTrackReport", 10);//待售房屋(餘屋)去化落後追蹤表
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getHeaderMapFor918(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R55.title.01", 15);//分行別
		titleMap.put("CLS180R55.title.02", 10);//分行代號
		titleMap.put("CLS180R55.title.03", 20);//戶名
		titleMap.put("CLS180R55.title.04", 15);//統編
		titleMap.put("CLS180R55.title.05", 20);//案名
		titleMap.put("CLS180R55.title.06", 40);//去化時程落後於銷售計劃之原因
		titleMap.put("CLS180R55.title.07", 20);//餘屋貸款銷售率
		titleMap.put("CLS180R55.title.08", 15);//餘屋座落區
		titleMap.put("CLS180R55.title.09", 20);//案號
		titleMap.put("CLS180R55.title.10", 15);//額度序號
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getHeaderMapForSecondList(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R55.title.01", 15);//分行別
		titleMap.put("CLS180R55.title.02", 10);//分行代號
		titleMap.put("CLS180R55.title.03", 20);//戶名
		titleMap.put("CLS180R55.title.04", 15);//統編
		titleMap.put("CLS180R55.title.05", 20);//案名
		titleMap.put("CLS180R55.title.06", 40);//去化時程落後於銷售計劃之原因
		titleMap.put("CLS180R55.title.07", 20);//餘屋貸款銷售率
		titleMap.put("CLS180R55.title.11", 25);//自首撥日起超過三年且銷售率未達四成
		titleMap.put("CLS180R55.title.08", 15);//餘屋座落區
		titleMap.put("CLS180R55.title.09", 20);//案號
		titleMap.put("CLS180R55.title.10", 15);//額度序號
		titleMap.put("CLS180R55.title.12", 15);//最近核定層級
		return titleMap;
	}
	
	@Override
	public void setBodyContent(WritableSheet sheet, List<Map<String, Object>> data, int colIndex, int rowIndex, boolean isSecondList) throws RowsExceededException, WriteException{
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(Map<String, Object> map : data){
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BRNAME")), cellFormat));//分行別
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF602_BR_NO")), cellFormat));//分行代號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CNAME")), cellFormat));//戶名
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF602_CUSTID")), cellFormat));//統編
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517_BUILD_NAME")), cellFormat));//案名
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517_BEHIND_DESC")), cellFormat));//去化時程落後於銷售計劃之原因
			sheet.addCell(new Label(colIndex++, rowIndex, map.get("SALE_RATE") + "% " + map.get("SALE_RATE_FORMULA"), cellFormat));//餘屋貸款銷售率
			if(isSecondList){
				sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("IS_LOWER_40")), cellFormat));//自首撥日起超過三年且銷售率未達四成
			}
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CITY_DISTRICT_NAME")), cellFormat));//餘屋座落區
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CASENO")), cellFormat));//案號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517_CONTRACT")), cellFormat));//額度序號
			if(isSecondList){
				sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("APPROVAL_LEVEL")), cellFormat));//最近核定層級
			}
			rowIndex++;
			colIndex = 0;
		}
	}
	
	@Override
	public void setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, String date) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 20);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(String title : titleMap.keySet()){
			this.mergeFieldWithAutoSize(sheet, fromColIndex, toColIndex, fromRowIndex, toRowIndex, prop.getProperty(title) + " (" + date + ")", cellFormat);
		}
	}
	
	@Override
	public void setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop, int colIndex, int rowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(String header : headerMap.keySet()){
			this.mergeFieldAndSetWidth(sheet, colIndex, colIndex++, rowIndex, rowIndex+1, prop.getProperty(header), cellFormat, headerMap.get(header));
		}
	}
	
//	private void setCellsFormat(WritableSheet sheet, int width, String content, int colIndex, int rowIndex, WritableCellFormat cellFormat) throws RowsExceededException, WriteException{
//		sheet.setColumnView(colIndex, width);//設定欄寬
//		sheet.addCell(new Label(colIndex, rowIndex, content, cellFormat));
//	}
	
	private void mergeFieldAndSetWidth(WritableSheet sheet, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, 
										String content, WritableCellFormat cellFormat, int width) throws RowsExceededException, WriteException{
		sheet.setColumnView(fromColIndex, width);
		sheet.mergeCells(fromColIndex, fromRowIndex, toColIndex, toRowIndex);
		sheet.addCell(new Label(fromColIndex, fromRowIndex, content, cellFormat));
	}
	
	private void mergeFieldWithAutoSize(WritableSheet sheet, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, 
										String content, WritableCellFormat cellFormat) throws RowsExceededException, WriteException{
		sheet.mergeCells(fromColIndex, fromRowIndex, toColIndex, toRowIndex);
		sheet.addCell(new Label(fromColIndex, fromRowIndex, content, cellFormat));
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public void processData(List<Map<String, Object>> elf517List, Map<String, Map<String, Object>> eloanData){
		
		Map<String, String> branchMap = new HashMap<String, String>();
		List<IBranch> branchList = this.branchService.getAllBranch();
		for(IBranch branch : branchList){
			branchMap.put(branch.getBrNo(), branch.getBrName());
		}
		
		Map<String, Map<String, Object>> locationMap = new HashMap<String, Map<String, Object>>();
		Date currentDate = CapDate.parseDate(CapDate.getCurrentDate("yyyy-MM-dd"));
		
		Iterator<Map<String, Object>> iterator = elf517List.iterator();
		
		while(iterator.hasNext()){
			
			Map<String, Object> elf517Data = (Map<String, Object>)iterator.next();

			BigDecimal totalLoanBal = LMSUtil.nullToZeroBigDecimal(elf517Data.get("TOTAL_LOAN_BAL"));

			if(elf517Data.get("LNF020_DURATION_ED") != null && totalLoanBal.compareTo(BigDecimal.ZERO) == 0){
				Date lnf020DurationEd = CapDate.parseDate(String.valueOf(elf517Data.get("LNF020_DURATION_ED")));
				if(currentDate.compareTo(lnf020DurationEd) >= 0){
					iterator.remove();
					continue;
				}
			}
			
			if(elf517Data.get("LNF020_END_DATE") != null && totalLoanBal.compareTo(BigDecimal.ZERO) == 0){
				Date lnf020EndDate = CapDate.parseDate(String.valueOf(elf517Data.get("LNF020_END_DATE")));
				if(currentDate.compareTo(lnf020EndDate) >= 0){
					iterator.remove();
					continue;
				}
			}
			
			String cntrno = String.valueOf(elf517Data.get("ELF517_CONTRACT"));
			Map<String, Object> eLoanTmpData = eloanData.get(cntrno) == null ? new HashMap<String, Object>() : eloanData.get(cntrno);
			elf517Data.put("CASENO", eLoanTmpData.get("CASENO"));
			elf517Data.put("BRNAME", branchMap.get(elf517Data.get("ELF602_BR_NO")));
			
			BigDecimal soldNumber = LMSUtil.nullToZeroBigDecimal(elf517Data.get("ELF517_SOLD_NUMBER"));
			BigDecimal begNumber = LMSUtil.nullToZeroBigDecimal(elf517Data.get("ELF517_BEG_FORSELL"));
			BigDecimal saleRate = begNumber.compareTo(BigDecimal.ZERO) == 0 
									? BigDecimal.ZERO
									: soldNumber.divide(begNumber, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2);
			
			String saleRateStr = saleRate.toPlainString();
			
			Date loanDateAdd3Years = CapDate.addYears(CapDate.parseDate(String.valueOf(elf517Data.get("LNF030_LOAN_DATE"))), 3);
			boolean isOver3Years = loanDateAdd3Years.compareTo(new Date()) < 0;
			elf517Data.put("SALE_RATE", saleRateStr);
			elf517Data.put("IS_LOWER_40", saleRate.compareTo(new BigDecimal(40)) < 0 && isOver3Years ? "V" : "");
			elf517Data.put("SALE_RATE_FORMULA", "(" + soldNumber.toPlainString() + "/" + begNumber.toPlainString() + ")");
			
			String cityCode = CapString.trimNull(elf517Data.get("ELF517_SITE1NO"));//擔保品座落縣市別
			String districtCode = CapString.trimNull(elf517Data.get("ELF517_SITE2NO"));//擔保品座落鄉鎮市區別
			String sectionCode = CapString.trimNull(elf517Data.get("ELF517_SITE3NO"));//擔保品座落段小段
			String villageCode = CapString.trimNull(elf517Data.get("ELF517_SITE4NO"));//擔保品座落村里
			districtCode = StringUtils.leftPad(districtCode, 2, '0');
			sectionCode = StringUtils.leftPad(sectionCode, 4, '0');
				
		    String key = cityCode + districtCode + sectionCode + villageCode;
		    Map<String, Object> areaMap = locationMap.get(key);
		    
		    if(null == areaMap){
		    	locationMap.putAll(this.eloandbcmsBASEService.getCollateralLocationByDistrict(cityCode + districtCode));
		    	areaMap = locationMap.get(key);
		    }
		    
		    if(areaMap != null){
		    	elf517Data.put("CITY_DISTRICT_NAME", CapString.trimNull(areaMap.get("CITY_COUNTY_NAME")) + CapString.trimNull(areaMap.get("DISTRICT_NAME")));
		    }
		    
		    elf517Data.put("APPROVAL_LEVEL", eLoanTmpData.get("APPROVAL_LEVEL"));
		}
	}

}
