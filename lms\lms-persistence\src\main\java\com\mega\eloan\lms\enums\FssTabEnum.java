/* 
 * FssTabEnum.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.enums;

/**
 * <pre>
 * 財務三表的報表別
 * 1 資產負債表
 * 2 損益表
 * 3 現金流量表
 * </pre>
 * 
 * @since 2011/8/9
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/8/9,Sunkist Wang,new
 *          </ul>
 */
public enum FssTabEnum {

    /** 1 資產負債表 **/
    BS("1"),
    /** 2 損益表 **/
    IS("2"),
    /** 3 現金流量表 **/
    C("3");

    private String code;

    FssTabEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public boolean isEquals(Object other) {
        if (other instanceof String) {
            return code.equals(other);
        } else {
            return super.equals(other);
        }
    }

    public static FssTabEnum getEnum(String code) {
        for (FssTabEnum enums : FssTabEnum.values()) {
            if (enums.isEquals(code)) {
                return enums;
            }
        }
        return null;
    }
}
