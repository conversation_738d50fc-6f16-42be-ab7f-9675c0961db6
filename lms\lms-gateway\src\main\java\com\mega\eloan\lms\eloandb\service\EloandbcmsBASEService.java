/* 
 * DWCommonService.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2011 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.eloandb.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 取擔保品資料使用
 * EloandbCMSBASEService
 * </pre>
 * 
 * @since 2012/11/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/6,ICE,new
 *          <li>2013/02/05,GaryCahng,
 *          </ul>
 */

public interface EloandbcmsBASEService {

	/**
	 * 取得擔保品(不動產)資訊
	 * 
	 * @param cmsMainId
	 *            擔保品文件編號
	 * @return
	 */
	public Map<String, Object> getShowDataCollType1For140(String cmsMainId);

	/**
	 * 取得擔保品(土地及建物)資訊
	 * 
	 * @param cmsMainId
	 *            擔保品文件編號
	 * @return
	 */
	public List<Map<String, Object>> getlandBuildDataCollType1For140(
			String cmsMainId);

	/**
	 * 取得擔保品(不動產以外)資訊
	 * 
	 * @param cmsMainId
	 *            擔保品文件編號
	 * @return 查詢結果
	 */
	public Map<String, Object> getShowDataFor140(String cmsMainId);

	/**
	 * 取得建物資料
	 * 
	 * @param cmsMainId
	 *            擔保品文件編號
	 * @return 查詢結果
	 */
	public List<Map<String, Object>> getC101M04ByMainId(String cmsMainId);

	/**
	 * 取得土地資料
	 * 
	 * @param cmsMainId
	 *            擔保品文件編號
	 * @return 查詢結果
	 */
	public List<Map<String, Object>> getC101M03ByMainId(String cmsMainId);

	/**
	 * 取得段小段資料
	 * 
	 * @param cmsMainId
	 *            擔保品文件編號
	 * @return 查詢結果
	 */
	public Map<String, Object> getC101M09ByCityAreaIr48(String cityId,
			String areaId, String Ir48);

	/**
	 * 查詢擔保品組字相關資料
	 * 
	 * @param Oid
	 * @return
	 */
	List<Map<String, Object>> getCmsSetDataByOid(String Oid);

	Map<String, Object> getC100M01ByMainId(String mainId);
	/**
	 * 查詢擔保品資料檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @return
	 */
	Map<String, Object> getC100M03ByMainId(String mainId);

	List<Map<String, Object>> getOwnerByMainId(String mainId);

	List<Map<String, Object>> getBuildingByMainId(String mainId);

	List<Map<String, Object>> getLandByMainId(String mainId);

	Map<String, Object> getC100M01ByOid(String oid);

	Map<String, Object> getC101M06ByMainId(String mainId);

	/**
	 * 抓不動產前准抵押權第一順位金額
	 * 
	 * @param mainId
	 *            文件編號
	 * @return
	 */
	Map<String, Object> getC101S05AByMainId(String mainId);

	List<Map<String, Object>> getC101S05ByCustIdDupNo(String custId, String dupNo);
	
	List<Map<String, Object>> getC101M05ByMainId(String mainId);

	List<Map<String, Object>> getC101M08ByMainId(String mainId);

	/**
	 * 取得權利質權 各個類別的名稱
	 * 
	 * @param mainId
	 *            文件編號
	 * @return
	 */
	List<Map<String, Object>> getTy03ShowNameByMainId(String mainId);

	/**
	 * 取得擔保品已設定未塗消資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	List<Map<String, Object>> getSetDataByCustIdAndBranch(String custId,
			String dupNo, String branch);

	/**
	 * 動產明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC102S01AByMainId(String mainId);
	
	/**
	 * 權利質權銀行定存單明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC103S01AByMainId(String mainId);

	/**
	 * 權利質權國庫券明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC103S01BByMainId(String mainId);

	/**
	 * 權利質權公債明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC103S01CByMainId(String mainId);

	/**
	 * 權利質權金融債券明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC103S01DByMainId(String mainId);

	/**
	 * 權利質權央行儲蓄券明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC103S01EByMainId(String mainId);

	/**
	 * 權利質權公司債明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC103S01FByMainId(String mainId);

	/**
	 * 權利質權股票明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC103S01GByMainId(String mainId);

	/**
	 * 權利質權受益憑證明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC103S01HByMainId(String mainId);

	/**
	 * 權利質權票券明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC103S01IByMainId(String mainId);
	
	/**
	 * 動產質權倉單明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC104S01ByMainId(String mainId);

	/**
	 * 銀行保證明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC105S01AByMainId(String mainId);

	/**
	 * 公庫主管機關保證明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC105S01BByMainId(String mainId);

	/**
	 * 信用保證機構保證明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC105S01CByMainId(String mainId);

	/**
	 * 母公司保證明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC105S01DByMainId(String mainId);

	/**
	 * 額度本票分期償還票據明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC106S01AByMainId(String mainId);

	/**
	 * 貼現票據明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC107S01AByMainId(String mainId);

	/**
	 * 信託占有明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC108S01AByMainId(String mainId);

	/**
	 * 參貸他行明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC109S01AByMainId(String mainId);

	/**
	 * 其他明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC110S01AByMainId(String mainId);

	/**
	 * 反面承諾明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC110S01BByMainId(String mainId);

	/**
	 * 浮動擔保明細資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC110S01CByMainId(String mainId);

	/**
	 * 不動產建物基地座落地號檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC101S03AByMainId(String mainId);
	
	/**
	 * 不動產所有權人明細
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC101S03BByMainId(String mainId);

	/**
	 * 動產設定保險明細檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> getC102S01DByMainId(String mainId);

	/**
	 * 查詢村里
	 * 
	 * @param LOCATE1DESC
	 *            縣市名
	 * @param LOCATE2DESC
	 *            鄉鎮名
	 * @return
	 */
	List<Map<String, Object>> getCMSC101M15ByLOCATE1DESC(String LOCATE1DESC,
			String LOCATE2DESC);

	/**
	 * 取得大段
	 * 
	 * @param cityName
	 *            縣市名稱
	 * @param areaName
	 *            鄉鎮名稱
	 * @return
	 */
	public List<Map<String, Object>> getCMSC101M09FindSECTION1(String cityName,
			String areaName);

	/**
	 * 取得小段
	 * 
	 * @param cityName
	 *            縣市名稱
	 * @param areaName
	 *            鄉鎮名稱
	 * @param site3
	 *            大段
	 * @return
	 */
	public List<Map<String, Object>> getCMSC101M09FindSECTION2(String cityName,
			String areaName, String site3);

	public List<Map<String, Object>> getC101M15ByLOCATE3CODE(String LOCATE3CODE);
		
	public List<Map<String, Object>> getCrsCollInfoByCntrNo(String cntrNo);
	public List<Map<String, Object>> getCrsCollCntrNoByCustIdDupNo(String custId, String dupNo);
	
	/**
	 * 從額度明細表MAINID取得引進的不動產擔保品的購價/鑑價/估價/增值稅/預提折舊/房屋稅與地價稅金額
	 * @param mainId
	 * @return
	 */
	public Map<String, Object> findC101M06ByL140M01A(String mainId); 
	
	public Map<String, Map<String, Object>> getCollateralLocationByDistrict(String district);

	public List<Map<String, Object>> getCollateralBuildStatus(String cmsOid);

	public List<Map<String, Object>> getSpecificMoneyTrustCollateralRightsPledgeData(String cmsOid);

	public List<Map<String, Object>> getCntrnoByComparisonCollateralOwnerId(String l140m01a_mainId, List<String> idList);

	public List<Map<String, Object>> getCntrnoOfSameAsCollateralOwnerIdInfo(List<String> idList);

	public Map<String, String> getAllCollateralAreaName();

	public Map<String, Object> getTSumAmtAdjInRealEstateTypeByC100m01Oid(String oid);

	public List<Map<String, Object>> getSpaceDataOfCollateralByCntrNo(String cntrno);

	public List<String> getCntrNoBeenSetByC100m01Oid(String oid);
}
