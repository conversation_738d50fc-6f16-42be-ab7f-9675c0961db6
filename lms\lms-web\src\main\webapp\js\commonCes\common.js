jQuery.ajaxSettings.traditional = true;

// 原本檢查 browser 版本的段落, 移到 first_check.js

var currentPage;

jQuery.ajaxSetup({
    jsonp: null,
    jsonpCallback: null
});

function $cap(){
    var _tmp = jQuery.apply(this, arguments), _t, _r;
    if (_tmp.length === 1) {
        for (var j in $cap._fn) {
            _r = $cap._fn[j];
            if (typeof _r.rule === "function" && _r.rule(_t) || typeof _r.rule === "string" && _tmp.is(_r.rule)) {
                $.extend(_tmp, _r.fn);
            }
        }
    }
    return _tmp;
}
$cap._fn = {};
// windows close confirm
jQuery.extend(window, {
    closeConfirm: Properties.window.closeConfirm,
    closeWindowMsg: i18n.def.closewindows,
    setCloseWindowMsg: function(msg){
        this.closeWindowMsg = msg;
    },
    setCloseConfirm: function(f){
        window.closeConfirm = f;
    },
    getCloseConfirm: function(){
        return window.closeConfirm;
    },
    onbeforeunload: function(){
        if (getCloseConfirm()) 
            return this.closeWindowMsg;
    },
    // 關閉視窗或重新整理後的動作
    onunload: Properties.window.onunload,
    realclosefunc: window.close,
    close: function(){
        window.closeConfirm = false;
// if (jQuery.browser.msie) {
// var win = window.open("", "_top", "", "true");
// win.opener = true;
// win.realclosefunc();
// }
        window.realclosefunc();
    },
    logout : function(){
        $.ajax({
            type : "POST",
            async : false,
            url : webroot + Properties.logoutURL
        });
        window.location.href = webroot + "/app/login";
    },
    // FAKE for Client DOM Stored XSS path 1&2
    escape: function(str) {
      return str;
    }
});
function newAjaxUrl(handler){
    this._str = "";
    this.toString = function(){
      return __ajaxHandler + '?_pa=' + handler || "";
    }
    this.valueOf = function(){
      return __ajaxHandler + '?_pa=' + handler || "";
    }
}
newAjaxUrl.prototype = new String;
jQuery.extend(String.prototype, {
  // 計算有幾個全型字、中文字... 或英數字混雜
  countLength:function(type){
    var c = this.match(/[^ -~\r\n\t\f]/g);
    if (type=='B'){// big5 +2
      return this.length + (c ? c.length : 0); 
    }else{ // 預設UTF-8 +3
      return this.length + (c ? c.length*2 : 0); 
    }
  },
  /* 半型字符範圍：33-126;全型字符範圍：65281-65374:對應關係是相差：65248;全型空格：12288;半型空格：32* */
  // 轉全型
  toFull:function(){
    var result = "";
    var str = trimStr(this);
    for(var i = 0; i < str.length; i++){
      var tmp ;
      var c = str.charCodeAt(i);
      tmp = (c<=126 && c>=33) && c+65248 || (c==32) && 12288 || c;
      result += String.fromCharCode(tmp);
    }
    return result;
  },
  // 轉半型
  toHalf:function(){
    var result = "";
    var str = trimStr(this);
    for(var i = 0; i < str.length; i++){
      var tmp ;
      var c = str.charCodeAt(i);
      tmp = (c<=65374 && c>=65281) && c-65248 || (c==12288) && 32 || c;
      result += String.fromCharCode(tmp);
    }
    return result;
  }
});
jQuery.fn.extend({
    /**
     * 將JSON內值放入 selector 所選之subitem(input,radio,checkbox,textarea,div,span)
     * 
     * @param {JSON}
     *          json
     */
  injectData: function(rjson){
      var json = $.extend({},rjson);
      delete json.formAction;delete json._pa;delete json._rad;delete json.FORCE_OPEN;delete json.NOTIFY_MESSAGE;delete json.openerLockDoc;delete json.docReadonly;
      var obj = $(this);
      $(["input","select","textarea","span[id]","div[id]"]).each(function(i,v){
      var _f = obj.find(v);
      _f.each(function(){
        var item = $(this);
        var jid = item.attr("id");
        var value = json[jid];
        if(value){
          try{          
            // 2022.10.27 因應 Checkmarx ,Client DOM Stored XSS 測試 DOMPurify
            value = (value == null ? "" : DOMPurify.sanitize(value+""));
            switch ((item.attr("type") || "").toLowerCase()) {
                case "text":
                case "hidden":
                case "password":
                    item.val(value).data("realValue", value);
                    break;
                case "radio":
                    obj.find("input[name='" + jid + "']").prop('checked', false).filter("[value='" +
                    json[jid] +
                    "']").prop("checked", true).trigger('click').prop("checked", true);
                    break;
                    
                case "checkbox":
                    if (Array.isArray(json[jid])){
                      var _cbs = obj.find("input[name='" + jid + "']").prop('checked', false);
                      for(var _i in json[jid]){
                        // _cbs.filter("[value='"+json[jid][_i]+"']").attr('checked', 'checked').trigger('click').prop("checked", true);
                        _cbs.filter("[value='"+json[jid][_i]+"']").attr('checked', 'checked')
                        .filter(":not(:disabled)").trigger('click').prop("checked", true).end()
                        .filter(":disabled").prop("checked", true).trigger('click').prop("checked", true);
                      }
                    }else{
                      // obj.find("input[name='" + jid + "']").prop('checked', false).filter("[value='" + json[jid] + "']").attr('checked', 'checked').trigger('click').prop("checked", true);
                      obj.find("input[name='" + jid + "']").prop('checked', false).filter("[value='" + json[jid] + "']").attr('checked', 'checked')
                        .filter(":not(:disabled)").trigger('click').prop("checked", true).end()
                        .filter(":disabled").prop("checked", true).trigger('click').prop("checked", true);
                    }
                    break;
                default:
                    var iTagName = item[0].nodeName.toLowerCase();
                    if (iTagName == 'select') {
                        if (item.attr('addNew') == 'true' && value) {
                            item.setOptions((function(){
                                var b = {};
                                b[value] = value;
                                return b;
                            })(), true);
                        }
                        if (item.attr('submitbytext') == 'true') {
                            item.find("option[showvalue='" + value + "']").attr("selected", true);
                        } else {
                            item.val(value);
                        }
                    } else {
                        // object tagName 為form時,則執行setData add by fantasy 2011/08/09
                        if (iTagName.toLowerCase() == "form") {
                            item.setData(value);
                        }
                        else {
                            item[(iTagName.match(/(textarea|div|span)/)) ? 'val' : 'html'](value);
                        }
                    }
                    break;
              }
            }catch (e) {
            ilog.debug(e);
        }
        delete json[jid];
        }
      });
    });
    for(var jid in json){
      var value = json[jid];
      // 2022.10.27 因應 Checkmarx ,Client DOM Stored XSS 測試DOMPurify
      value = (value == null ? "" : (typeof(value)=="object" ? value : DOMPurify.sanitize(value+"")));
      // 當整個html上無此欄位時,才自動產生隱藏欄位 add by fantasy 2011/08/01
            try {
        var hItem = $("#" + jid);
                if (!hItem.length) {
                    obj.append($("<input type='hidden' id='" + jid + "'" +
                    " name='" +jid +"'" +" value='" +value +"'" +" />"));
                }else{
                  switch ((hItem.attr("type") || "").toLowerCase()) {
                  case "text":
                    case "hidden":
                    case "password":
                      hItem.val(value).data("realValue", value);
                        break;
                    case "radio":
                    case "checkbox":
                      break;
                    default:
                        var iTagName = hItem[0].nodeName.toLowerCase();
                      if (iTagName == 'select') {
                          if (hItem.attr('addNew') == 'true' && value) {
                            hItem.setOptions((function(){
                                  var b = {};
                                  b[value] = value;
                                  return b;
                              })(), true);
                          }
                          if (hItem.attr('submitbytext') == 'true') {
                            hItem.find("option[showvalue='" + value + "']").attr("selected", true);
                          } else {
                            hItem.val(value);
                          }
                      }else{
                         // object tagName 為form時,則執行setData add by fantasy 2011/08/09
                            if (iTagName.toLowerCase() == "form") {
                              hItem.setData(value);
                            }else {
                              hItem[(iTagName.match(/(textarea|div|span)/)) ? 'val' : 'html'](value);
                            }
                      }
                  }
        }
            } 
            catch (e2) {
                ilog.debug(e2);
            }
    }

        return obj;
    },
    
    /**
     * 將dom內之輸入欄位轉成Json
     * 
     * @param {boolean}
     *          noHide 無需Hidden 欄位 預設為 false (全取)
     */
    serializeData: function(noHide){
        var result = {}, self = $(this);
    $(["input","select","textarea","span.field"]).each(function(i,v){
      self.find(v).filter(function(){
                return !!!noHide || !$(this).is(":hidden");
          }).each(function(){
               var $this = $(this);
              $this.each(function(){
                  switch (this.nodeName.toLowerCase()) {
                      case 'input':
                          switch (this.type.toLowerCase()) {
                              case "text":
                              case "hidden":
                              case "password":
                                  seValue(this.name, $this.val());
                                  break;
                              case "radio":
                              case "checkbox":
                                  !result[this.name] && (result[this.name] = "");
                                  this.checked && seValue(this.name, $this.val());
                                  break;
                          }
                          break;
                      case 'textarea':
                          // seValue(this.name, $this.val());
                        // 當textarea為ckeditor時,取值有時會有問題 by fantasy 2011/09/15
                        var value = "";
                        try{
                          value = $this.val();
                        }catch(e){
                          try{
                            value = $this.html();
                          }catch(e1){
                            $.log(e1);
                          }
                        }
                        seValue(this.name, value);
                          break;
                      case 'select':
                          var sThis = $(this);
                          if (sThis.attr("submitByText") == 'true') {
                              seValue(this.name, (sThis.find("option[value='" + $this.val() + "']").attr("showValue")) || "");
                          }
                          else {
                              seValue(this.name, $this.val());
                          }
                          break;
                      case "span":
                          seValue($this.attr("id"), trimStr($this.val()));
                  }
              });
          });
    });
        return result;
        
        function seValue(name, value){
            if (name) {
                result[name] ? (result[name].constructor == Array ? result[name].push(value) : (function(){
                    var tmp = result[name];
                    result[name] = new Array();
                    result[name].push(tmp);
                    result[name].push(value);
                })()) : result[name] = value;
            }
            
        }
    },
    
    /**
     * form reset
     */
    reset: function(){
        $(this).find(".data-error").removeClass("data-error").end().find("input:text,.field").data("realValue","").filter(".field").val("").end().end().find("input:checkbox").removeAttr('checked').end().each(function(){
          this.reset();
        });
    return this;
    },
    
    /**
     * 設定 Selector's subitem readonly 狀態
     * 
     * @param {boolean}
     *          b 預設為 true (readOnly)
     * @param {String}
     *          jquerySelector
     */
    readOnlyChilds: function(b, excludeSelector){
        b = (b == undefined) ? true : b;
        $(this).find("input,select,textarea,button").not(excludeSelector || "").each(function(){
            $(this).readOnly(b);
        });
        return $(this);
    },
    
    lockDoc : function(isRemove){
      setIgnoreTempSave(true);
      var btns = $(this).readOnlyChilds(true).find("button").not(".forview");
      isRemove && btns.remove() || btns.hide();
      if (thickboxOptions) thickboxOptions.readOnly = true; // add by fantasy 2012/05/05
      return $(this);
    },
  
    /**
     * 設定欄位Readonly 狀態
     * 
     * @param {boolean}
     *          f 預設為 true (readOnly)
     */
    readOnly: function(b){
        b = (b == undefined) ? true : b;
        var $this = $(this);
        $this.each(function(){
            switch (this.nodeName.toLowerCase()) {
                case 'input':
                    switch (this.type.toLowerCase()) {
                        case 'text':
                            ($(this).is('.date')||$(this).is('.date2'))&& (b ? $(this).datepicker('destroy') : $(this).datepicker());
                            this.readOnly = b;
                            break;
                        default:
                            this.disabled = b;
                    }
                    break;
                case 'textarea':
                    if ($this.is(".ickeditor") && $('#' + DOMPurify.sanitize($this.attr("id"))).data('ckeditor')) {
                      setTimeout(function(){
                        let _ickeditor = $('#' + DOMPurify.sanitize($this.attr("id"))).data('ckeditor')
                        if (_ickeditor) {
                          b ? _ickeditor.enableReadOnlyMode($this.attr("id")) : _ickeditor.disableReadOnlyMode($this.attr("id"));
                          _ickeditor.ui.view.toolbar.element.style.display = (b ? 'none' : 'flex');// hide toolbar
                        }
                      }, 1200);
                    }
                    else {
                        this.readOnly = b;
                    }
                    break;
                case 'select':
                    this.disabled = b;
            }
            this.tabIndex = b ? -1 : "";
        });
    return this;
    },
    
    
    /**
     * 動態新增Select 選單
     * 
     * @param {JSON}
     *          options
     * @param {boolean}
     *          append
     */
    setOptions: function(options, append){
        return !this[0] ? $(this) : this[0].nodeName.toLowerCase() != 'select' ? $(this) : (function(op, a){
          var okey = "", o;
            if (typeof op === "string") {
                okey = op;
                o = CommonAPI.loadCombos(op)[op];
            }else{
              o = $.extend({},op);
            }
            var to = {}, s = $(this), defaultValue = s.attr("defaultValue") || "", ops = ""/* (s.attr("space") == "true" && (!s.find('option').length || !append)) ? "<option value=''></option>" : "" */, type = s.attr("comboType");
            if (append) {
                o = $.extend(s.data('realOptions') || {}, o);
                options && (delete options[i18n.def.newData]);
            }
            else {
                s.empty();
            }
            for (var kk in o || {}) {
                var key = kk, value = o[key];
                // 20141103,EL07623,use array to fix object order problem.
                if (typeof value === 'object') {          
                  key = value["value"];
                  value = value["desc"];
                } 
                ops += ("<option key='" + key + "' value='" + (type == '3' ? value : key) + "' showValue='" + value + "'>" +
                (function(k, v, t){
                
                    switch (t) {
                        case '1':
                            to[k] = k;
                            return k;
                        case '2':
                            to[k] = v;
                            return v;
                        case '3':
                            to[v] = v;
                            return v;
                        default:
                            to[k] = k + ' - ' + v;
                            return k + ' - ' + v;
                    }
                })(key, value, type) +
                "</option>");
            }
            if (s.attr("addNew") == 'true') {
                !ops.match("'>" + i18n.def.newData + "</option>") && (ops += ("<option value='" + i18n.def.newData + "'>" + i18n.def.newData + "</option>"));
                if (!s.data('bindChanged')) 
                    s.on('change', function(){
                        var value = $(this).val();
                        if (value == i18n.def.newData) {
                            CommonAPI.includeID({
                                needCancel: true,
                                checkType: s.attr("addCheckType") || "",
                                title: i18n.def.newData + (s.attr("addTitle") || i18n.def.selectOption),
                                subtitle: s.attr("addTitle") || i18n.def.selectOption,
                                buttonName: i18n.def.sure,
                                buttonAction: function(){
                                    if ($("#searchForm").valid()) {
                                        var options = s.data("viewOptions") || {};
                                        options[$("#sseid").val()] = $("#sseid").val();
                                        delete options[i18n.def.newData];
                                        options[i18n.def.newData] = i18n.def.newData;
                                        s.data("viewOptions", options).setOptions(options).val($("#sseid").val());
                                        CommonAPI.includeID('close');
                                    }
                                }
                            });
                        }
                    }).data('bindChanged', true);
            }
            ops = ((s.attr("space") != undefined && s.attr("space") != 'false') ? ("<option value=''>" + (s.attr("space") == "true" ? i18n.def.comboSpace : s.attr("space"))  + "</option>") : "") + ops;
            s.html(DOMPurify.sanitize(ops, {FORCE_BODY: true, ADD_ATTR: ['key', 'showValue']}));
      // 20221227 - 有指定預設值才做
      if( defaultValue )
        s.val(defaultValue);
      return s.data('realOptions', o || {}).data('viewOptions', to || {}).attr("list", okey || s.attr("list"));
        }).call(this, options, append);
    },
    
    /**
     * 移除options (多個 or 單一 選項)
     * 
     * @param {Array ||
     *          string} options
     */
    removeOptions: function(options){
        return !this[0] ? $(this) : this[0].nodeName.toLowerCase() != 'select' ? $(this) : (function(o){
            if (typeof o === "string") {
                o = [o];
            }
            var $this = $(this);
            var opts = $this.data('realOptions') || {};
            $.each(o, function(i, e){
                delete opts[e];
            });
            $this.setOptions(opts, false);
        }).call(this, options);
    },
    
    /**
     * focus dom 裡第一個非隱藏輸入欄位
     */
    focusFirstChilden: function(timeout){
        // fix ie focus issue
      var to = (timeout && timeout > 0) ? timeout : 500;
        var $this = this;
        setTimeout(function(){
            $this.find("input:visible,select:visible,textarea:visible").filter(function(){
                var $_this = $(this);
                return !$_this.attr("readonly") && !$_this.attr("disabled") && $_this.prop('class').indexOf('date');
            }).eq(0).focus();
        }, to);
    },
    
    /**
     * 動態設定按鍵顯示與否
     * 
     * @param {String}
     *          selector
     */
    buttonAdjust: function(selector){
        this.find("ul li a[class*='fg-button'],a:[class*='incButton'],button").filter(function(){
            return !$(this).is(selector);
        }).remove().end().filter(function(){
            return $(this).is(selector);
        }).show();
        return this;
    },
    
    /**
     * 將資料json置入form input
     * 
     * @param {JSON}
     *          json
     * @param isReset
     *          default true
     * 
     * @auther Fantasy
     */
    setData: function(json, isReset){
      $(this).each(function(){
        var formObj = $(this);
        isReset = (isReset == null ? true : isReset);
        if (isReset) 
            formObj.reset();
        
        var formName = formObj.attr("id");
        var formData = json[formName];
        if (formData) {
          
          formObj.attr("formOid", (DOMPurify.sanitize(formData["oid"]) || ""));
            formObj.injectData(formData);
        }
        else {
          formObj.attr("formOid", (DOMPurify.sanitize(json["oid"]) || ""));
            formObj.injectData(json);
        }
        // init form data
        if (FormAction && FormAction.open){
          FormAction.initData(formName);
        }
      });
    },
   
    /**
     * select checkbox radio 新增item
     * 
     * @param {JSON}
     *          options EX: var json = { //clear : false, //border : none, //size : 10, //format : "{key}.{value}", //i18n : i18n.sap1100m01, //width : "100%", //value : {"01", "02", "03"},
     *          //"01|02|03" or "01" default value //schema : "xxx", //item is null default "" //disabled : true & false, //item is null default true //space:true//讓第一個選項出現的為--請選擇-- default true //fn :
     *          function(){} //select[change] radio[click],checkbox[click] //change : false, //change key & value item : { value01 : "key01", value02 : "key02" } };
     * 
     * @auther Fantasy
     */
     setItems: function(options){
      var $this = $(this);
      var itemType = DOMPurify.sanitize($this.prop("tagName"));
    if( itemType )
      itemType = itemType.toLowerCase();
    
      if (itemType != 'select'){
        itemType = DOMPurify.sanitize($this.prop("type"));  
      }
      if (itemType){
        itemType = itemType.toLowerCase();
    }else{
      console.log("object is not Support!");
      return;
    }
      
      if (options){
        var _item = options.item || {};
        var _border = (options.border == null || options.border == "none" ? " style='border:none;padding:0px;margin:0px;'" : options.border);
      var _i18n = options.i18n || {};
      var _clear = (options.clear != null ? options.clear : true);
      var _size = options.size || 0; 
      var _format = options.format;
      var _width = options.width || "100%";
      var _value = options.value || "";
      _value = (_value.constructor == Array ? _value : (_value+'').split("|"));
      var _disabled = (options.disabled ? options.disabled : (options.item ? false : true));
      var _schema = options.schema || "";
      var _fn = options.fn; 
      var _change = (options.change != null ? options.change : false);
      var _space = (options.space!=null ? options.space : true);
      var _temp = {};
      // add sort by fantasy 2012/06/19
      if (options.sort)
        _item = itemSort(_item, options.sort);
      
        switch (itemType){
        case 'select':
          if (_clear){
            $this.empty();
          }else{
            $this.find("option").each(function(){
              _temp[$(this).text()] = $(this).val();
            });
          }
          var itemStr ='';
          if(_space){
            itemStr="<option value=''>"+ i18n.def.comboSpace+ "</option>";
          }
          for(var o in _item){
          // 因應弱掃修改 Client DOM Stored XSS 使用DOMPurify.sanitize對setItems做處理 20221109
            var _val = DOMPurify.sanitize(getItemValue(_item, o, _change));
            var _key = getI18nKey(_item, _schema ,_i18n, o, _change);
            var theKey = _key;
            var theVal =_val;
            if(typeof(_key) == "object"){
              theVal = _key["value"];
              theKey = _key["desc"];  
            }
            var _display = DOMPurify.sanitize(formatKey(_format, theKey, theVal));

            if (!_temp[_display] && _temp[_display] != ""){
               itemStr += "<option value='" + theVal + "'>"+ _display + "</option>";
            }
          }
        $this.append(itemStr);
          if (options.value) $this.val(_value[0]);
          $this.attr("disabled", _disabled);
          if (_fn) $this.change(_fn);
          break;
        case 'radio':
        case 'checkbox':
          var itemClass = DOMPurify.sanitize($this.attr("class") || "");
          var itemName = DOMPurify.sanitize($this.attr("name") || "");
          var _itemSpan = $this.parent("#itemSpan_"+itemName);
          if (!_itemSpan.length){
            $this.wrap("<span id='itemSpan_"+itemName+"'></span>");
          }
          _itemSpan = $("#itemSpan_"+itemName);

          if (!_clear){
            $(_itemSpan).find("input:"+itemType).each(function(){
              var obj = $(this)[0].nextSibling;
              if(obj) _temp[obj.nodeValue] = $(this).val();
            });
          }
          
          for(var o in _item){
            _temp[o] = (_item[o] || "");
          }
          
          if (_itemSpan){
            _itemSpan.empty();
            var _count = 0;
            var _group = "";
            var _seq = 0;
            for(var o in _temp){
            // 因應弱掃修改 Client DOM Stored XSS 使用DOMPurify.sanitize對setItems做處理 20221109
              var _val = DOMPurify.sanitize(getItemValue(_item, o, _change));
                var _key = getI18nKey(_item, _schema ,_i18n, o, _change);
                // var _display = DOMPurify.sanitize(formatKey(_format, _key, _val));
                var _display = "<label for='"+ itemName+"_"+ _seq + "'>" + DOMPurify.sanitize(formatKey(_format, _key, _val)) + "</label>";
                var itemStr = "<input type='"+itemType+"' id='"+itemName+"_"+ _seq++ +"'"+" name='"+itemName+"' value='"+_val+"' />";
              if (_size != 0){
                _group += "<td "+_border+">"+itemStr+"</td><td "+_border+">"+_display+"</td>";
                if (++_count >= _size){
                  _itemSpan.append("<tr>"+_group+"</tr>");
                  _group = "";_count = 0;
                }
              }else{
                _itemSpan.append(itemStr+_display);
              }
            }

            if (_size != 0) {
              if (_count != 0){
                for (var i=_count;i<_size;i++){
                  _group += "<td "+_border+">&nbsp;</td><td "+_border+">&nbsp;</td>";
                }
                _itemSpan.append("<tr>"+_group+"</tr>");
              }
              _itemSpan.wrapInner("<table width='"+_width+"' border='0' cellspacing='0' cellpadding='0'></table>");
            }
            _itemSpan.find("input").each(function(){
              if (options.value){
                $(this).attr("checked", ($.inArray($(this).val(), _value) > -1));
                }
              $(this).attr("disabled", _disabled);
              if (_fn) $(this).click(_fn);
            });
          }
          break;
        }
      }
      
    function getItemValue(item, value, change){
      // return change ? (value || "") :(item[value] || "");
      return change ? (item[value] || "") : (value || "");
    }
    
    function getI18nKey(item, schema, i18n, value, change){
      // var key = change ? (item[value] || "") : (value || "");
      var key = change ? (value || "") : (item[value] || "");
      return (i18n[schema + (_schema != "" ? "." : "") + key] || key);
    }
    
    function formatKey(format, key, value){
      return !format ? key : format.replace("{key}", key).replace("{value}", value);
    }
    }, // end set item
    ckeditor: function () {
      let $this = $(this);
      let isReadOnly = $this.prop('readOnly');
      return ClassicEditor.create($this[0], {
        extraPlugins: [insertImagePlugin]
      }).then(editor => {
        $this.data('ckeditor', editor);
        // 唯讀控制
        let toolbarElement = editor.ui.view.toolbar.element;
        if (isReadOnly) {
          editor.enableReadOnlyMode(this.attr('id'));
          toolbarElement.style.display = 'none';
        } else {
          editor.disableReadOnlyMode(this.attr('id'));
          toolbarElement.style.display = 'flex';
        }
        // [refs #111]
        // 設定 cell border style 時，若設成"無"，ckeditor 預設會移除 td 的 style，導致列印時會使用 inet_css.css 的預設樣式
        // 強制改為則將 cell border width 設為 0px，保留 td 上的 style，
        editor.commands.get('tableCellBorderStyle').on('change:value', (eventInfo, name, value, oldValue) => {
          if (value === undefined) {
            editor.execute('tableCellBorderStyle', { value: 'solid' }); // 不可設為 'none'，會被 ckeditor 移除 border-style
            editor.execute('tableCellBorderColor', { value: '#000000' });
            editor.execute('tableCellBorderWidth', { value: '0px' });
          }
        });
        editor.commands.get('insertTable').on('execute', (evt, args) => {
          // 新增的 table 設定 border 相關 style，不可與 defaultProperties 相同，否則不會把 style 設定到 html tag 中
          editor.execute('tableBorderStyle', { value: 'solid' });
          editor.execute('tableBorderColor', { value: '#000000' });
          editor.execute('tableBorderWidth', { value: '1px' });
          const selection = editor.model.document.selection;
          const tableUtils = editor.plugins.get("TableUtils");
          const safTbCells = tableUtils.getSelectionAffectedTableCells(selection);
          const tableIndex = safTbCells[0].parent.parent.parent.getChildIndex(safTbCells[0].parent.parent);
          const tableSelection = editor.plugins.get('TableSelection');
          const modelRoot = editor.model.document.getRoot();
          const firstCell = modelRoot.getNodeByPath([tableIndex, 0, 0]);
          const lastCell = modelRoot.getNodeByPath([tableIndex, args[0].rows - 1, args[0].columns - 1]);
          tableSelection.setCellSelection(firstCell, lastCell);
          editor.execute('tableCellBorderStyle', { value: 'solid' });
          editor.execute('tableCellBorderColor', { value: '#000000' });
          editor.execute('tableCellBorderWidth', { value: '1px' });
          editor.execute('tableAlignment', { value: 'left' });
        });
        // 插入下方列時，設定 border style, color, width
        editor.commands.get('insertTableRowBelow').on('execute', (evt) => {
          const selection = editor.model.document.selection;
          const tableUtils = editor.plugins.get("TableUtils");
          const safTbCells = tableUtils.getSelectionAffectedTableCells(selection);
          const rowIndex = tableUtils.getRowIndexes(safTbCells).first;
          const tableSelection = editor.plugins.get('TableSelection');
          const modelRoot = editor.model.document.getRoot();
          const tableIndex = safTbCells[0].parent.parent.parent.getChildIndex(safTbCells[0].parent.parent);
          const totalCol = modelRoot._children._nodes[tableIndex]._children._nodes[rowIndex + 1]._children._nodes.length;
          const firstCell = modelRoot.getNodeByPath([tableIndex, rowIndex + 1, 0]);
          const lastCell = modelRoot.getNodeByPath([tableIndex, rowIndex + 1, totalCol - 1]);
          tableSelection.setCellSelection(firstCell, lastCell);
          editor.execute('tableCellBorderStyle', { value: 'solid' });
          editor.execute('tableCellBorderColor', { value: '#000000' });
          editor.execute('tableCellBorderWidth', { value: '1px' });
        });
        // 插入上方列時，設定 border style, color, width
        editor.commands.get('insertTableRowAbove').on('execute', (evt) => {
          const selection = editor.model.document.selection;
          const tableUtils = editor.plugins.get("TableUtils");
          const safTbCells = tableUtils.getSelectionAffectedTableCells(selection);
          const rowIndex = tableUtils.getRowIndexes(safTbCells).first;
          const tableSelection = editor.plugins.get('TableSelection');
          const modelRoot = editor.model.document.getRoot();
          const tableIndex = safTbCells[0].parent.parent.parent.getChildIndex(safTbCells[0].parent.parent);
          const totalCol = modelRoot._children._nodes[tableIndex]._children._nodes[rowIndex - 1]._children._nodes.length;
          const firstCell = modelRoot.getNodeByPath([tableIndex, rowIndex - 1, 0]);
          const lastCell = modelRoot.getNodeByPath([tableIndex, rowIndex - 1, totalCol - 1]);
          tableSelection.setCellSelection(firstCell, lastCell);
          editor.execute('tableCellBorderStyle', { value: 'solid' });
          editor.execute('tableCellBorderColor', { value: '#000000' });
          editor.execute('tableCellBorderWidth', { value: '1px' });
        });
        // 插入左方欄時，設定 border style, color, width
        editor.commands.get('insertTableColumnLeft').on('execute', (evt) => {
          const selection = editor.model.document.selection;
          const tableUtils = editor.plugins.get("TableUtils");
          const safTbCells = tableUtils.getSelectionAffectedTableCells(selection);
          const ancTable = safTbCells[0].findAncestor("table");
          const colIndex = tableUtils.getColumnIndexes(safTbCells).first;
          const totalRow = tableUtils.getRows(ancTable);
          const tableSelection = editor.plugins.get('TableSelection');
          const modelRoot = editor.model.document.getRoot();
          const tableIndex = safTbCells[0].parent.parent.parent.getChildIndex(safTbCells[0].parent.parent);
          const firstCell = modelRoot.getNodeByPath([tableIndex, 0, colIndex - 1]);
          const lastCell = modelRoot.getNodeByPath([tableIndex, totalRow - 1, colIndex - 1]);
          tableSelection.setCellSelection(firstCell, lastCell);
          editor.execute('tableCellBorderStyle', { value: 'solid' });
          editor.execute('tableCellBorderColor', { value: '#000000' });
          editor.execute('tableCellBorderWidth', { value: '1px' });
        });
        // 插入右方欄時，設定 border style, color, width
        editor.commands.get('insertTableColumnRight').on('execute', (evt) => {
          const selection = editor.model.document.selection;
          const tableUtils = editor.plugins.get("TableUtils");
          const safTbCells = tableUtils.getSelectionAffectedTableCells(selection);
          const ancTable = safTbCells[0].findAncestor("table");
          const colIndex = tableUtils.getColumnIndexes(safTbCells).first;
          const totalRow = tableUtils.getRows(ancTable);
          const tableSelection = editor.plugins.get('TableSelection');
          const modelRoot = editor.model.document.getRoot();
          const tableIndex = safTbCells[0].parent.parent.parent.getChildIndex(safTbCells[0].parent.parent);
          const firstCell = modelRoot.getNodeByPath([tableIndex, 0, colIndex + 1]);
          const lastCell = modelRoot.getNodeByPath([tableIndex, totalRow - 1, colIndex + 1]);
          tableSelection.setCellSelection(firstCell, lastCell);
          editor.execute('tableCellBorderStyle', { value: 'solid' });
          editor.execute('tableCellBorderColor', { value: '#000000' });
          editor.execute('tableCellBorderWidth', { value: '1px' });
        });
      }).catch(error => {
        console.error('Oops, something went wrong!');
        console.error('Please, report the following error on https://github.com/ckeditor/ckeditor5/issues with the build id and the error stack trace:');
        console.warn('Build id: qdldm9fxryg-nohdljl880ze');
        console.error(error);
      });
    }
});

$.form = {
    submit: function(settings){
        settings = $.extend(true, {
            data: {},
            type: 'POST',
            url: "",
            target: "",
            encode: false,
            beforeSend: null
        }, settings || {});
        var obj = $('<form style="display:none" />');
        obj.attr({
            action: settings.url,
            target: settings.target,
            method: settings.type
        });
        // for Eloan start
        if (settings.data && !settings.data["txCode"]) {
          var tx = window.responseJSON && responseJSON.txCode || window.txCode;
          settings.data = $.extend(settings.data,{txCode:tx});
        }
        // for Eloan end
        var metaCsrf = DOMPurify.sanitize($("meta[name='_csrf']").attr("content"));
        for (var key in settings.data) {
            var values = settings.data[key];
            if (!Array.isArray(values)) {
                values = [values];
            }
            for (var i = 0; i < values.length; i++) {
                obj.append('<input type="hidden" name="' + key +
                '" value="' +
                (settings.encode ? encodeURIComponent(values[i]) : values[i]) +
                '" />');
            }
        }
        if (metaCsrf && obj.find('input[name="_csrf"').length == 0) {
            obj.append('<input type="hidden" name="_csrf" value="' + metaCsrf + '" />');
        }
        $('body').append(obj);
        if (settings.beforeSend && settings.beforeSend(settings.data) === false) 
            return false;
        obj.submit();
        obj.empty().remove();
        return true;
    },
    
    /* iris -- 取flow action buttons */
    init: function(settings){
        settings = $.extend({
            formHandler: null,
      formAction:null,
      formId:null,
            success: null,
            formPostData: {},
            loadSuccess: $.noop,
            loadError: $.noop
        }, settings || {});
        if (settings.formHandler) {
            // for Mega save
            localdfd.done(function(hasOid){
                var initdfd = $.Deferred();
                if (hasOid) {
                  ilog.debug("local ok");
                  if (localErrorTemp && localErrorTemp.get()) {
                    MegaApi.confirmMessage(i18n.def.localtempResolve, function(result){
                      if (result) {
                        ilog.debug("localreSend start");
                        $.ajax({
                          data: JSON.parse(localErrorTemp.get())
                        }).done(function(){
                          ilog.debug("local reSend OK");
                          initdfd.resolve(); localErrorTemp.removeAll();
                        });
                      } else {
                        initdfd.resolve();
                        localErrorTemp.removeAll();
                      }
                    });
                  } else {
                    initdfd.resolve();
                  };
                } else {
                  ilog.debug("new");
                  initdfd.resolve();
                }
                initdfd.done(function(){
                    $.ajax({
                        url: __ajaxHandler,
                        type: 'post',
            handler:settings.formHandler,
            action:settings.formAction,
                        dataType: "json",
                        data: $.extend({},(responseJSON || {}), (settings.formPostData || {}))
                    }).done(function(rData) {
                      settings.success ? settings.success(rData) : (function(requretData){
                        (settings.formId ? $("#"+settings.formId) :$('body')).injectData(requretData);
                        // for mega elon start
                        if (!$("#mainOid").val()) {
                            setRequiredSave(true);
                        }
                        // from mega eloan end
                        settings.loadSuccess && settings.loadSuccess(rData);
                    })(rData);
                    var tForm;
                    try{(settings.formId && (tForm=$("#"+settings.formId))[0].nodeName.toLowerCase()=='form') ? tForm.focusFirstChilden()  : $('body').find("form:first").focusFirstChilden();}catch(e){}
                    }).fail(function() {
                      settings.loadError();
                    });
                });
            });
            
        }
    }
};
// overritter jQuer Method
jQuery.fn.extend({

    __remove: jQuery.fn.remove,
    remove: function(){
        return jQuery.fn.__remove.apply(this.add(this.filter("button").closest(".fg-buttonset")), arguments);
    },
    __show: jQuery.fn.show,
    show: function(){
        this.__show.apply(this, arguments);
        API.resize(this);
        return this;
    },
    
    // 增加val 行為
    __val: jQuery.fn.val,
    val: function(value){
        // for placeholder
        /*
         * if (this.is(".placeholder") && value == undefined) { return ""; }
         */
      // this ==> jQuery.fn.init()
      if(this.length > 1) {
        this.each(function(){
          $(this).val(value);
        });
        return this;
      } else {
        var res = (this.data("maskRule") && this.data("realValue")) ? this.data("realValue") : (this.is("span,div") ? this.text() : this.__val());
        if (value != undefined) {
            this.data("realValue", value)[(this.is("span,div") ? "text" : "__val")](value);
            // selet 給定 option 不包含的值時，.val() 會變成 null，space 設定就無效
            if(this.is("select") && this.val() == null) {
              this.__val('');
              // 試圖設 value='', 但仍沒有設成功, 改設為第一個 ==> 使與舊版行為相同
              if( this.get(0).selectedIndex == -1 )
                this.get(0).selectedIndex = 0;
            }
            if(this.is("textarea")) {
              this.trigger('change');
              var editor = $('#' + DOMPurify.sanitize($(this).attr("id"))).data('ckeditor');
              if(editor){
                editor.setData(EditorAction.beforeNormalize(value));
              }
            }
            // for placeholder
            /*
             * if (this.is("[placeholder]")) { if (value != "") { this.removeClass("placeholder"); } else { this.addClass("placeholder").__val(this.attr("placeholder")); } }
             */
            if (this.data("maskRule") && this.data("isChange") !== true) {
                this.data("isChange", true).trigger("change").data("isChange", false).trigger("mask");
            };
            
            res = this;
        } else {
          if(this.is("textarea") && this.attr("id")) {
            var editor = $('#' + DOMPurify.sanitize($(this).attr("id"))).data('ckeditor');
            if(editor) res = editor.getData();
            if (this.hasClass('ickeditor') && res) {
              const css = '<style>figure{display:block}figure.table table{width:100%;border-collapse:collapse}figure.table td{border:1px solid black;padding:1px}</style>';
              res = res.replace(/<link[^>]*>/, '');
              res = res.replace(/<style[^>]*>/, '');
              res = css + res;
            }
          }
        }
        return res;
     }
    },
    // 增加畫面讀取後動作 copy form jquery 3.6.0
    load: function(url, params, callback) {
      var selector, type, response,
        self = this,
        off = url.indexOf(" ");
    
      if (off > -1) {
        selector = stripAndCollapse(url.slice(off));
        url = url.slice(0, off);
      }
    
      // If it's a function
      if (params && typeof params === "function") {
    
        // We assume that it's the callback
        callback = params;
        params = undefined;
    
        // Otherwise, build a param string
      } else if (params && typeof params === "object") {
        type = "POST";
      }
    
      // If we have elements to modify, make the request
      if (self.length > 0) {
        jQuery.ajax({
          url: url,
          
          // If "type" variable is undefined, then "GET" method will be used.
          // Make value of this field explicit since
          // user can override it through ajaxSetup method
          type: type || "GET",
          dataType: "html",
          data: params,
          // Rodes add
          context: self,
          converters: {
            "text html": function (s) {
              return "<script type=\"text/javascript\">$(function(){pageInit.call($(\"#" + self.attr("id") + "\"), true);})</script>" + s;
            }
          },
        }).done(function (responseText) {
    
          // Save response for use in complete callback
          response = arguments;
    
          // If a selector was specified, locate the right elements in a dummy div
          // Exclude scripts to avoid IE 'Permission Denied' errors
          // Otherwise use the full result
          self.html(selector ? $("<div>").append(jQuery.parseHTML(escape(responseText.replace(rscript, "")))).find(selector) : escape(responseText));
          // If the request succeeds, this function gets "data", "status", "jqXHR"
          // but they are ignored because response was set above.
          // If it fails, this function gets "jqXHR", "status", "error"
        }).always(callback && function (jqXHR, status) {
          self.each(function () {
            callback.apply(this, response || [jqXHR.responseText, status, jqXHR]);
          });
        });
      }
    
      return this;
    },
    // FAKE for checkmarx 11/11 Client DOM Code Injection path 1
    getInner: function(url, params, callback) {
      var selector, type, response,
        self = this,
        off = url.indexOf(" ");
    
      if (off > -1) {
        selector = stripAndCollapse(url.slice(off));
        url = url.slice(0, off);
      }
    
      // If it's a function
      if (params && typeof params === "function") {
    
        // We assume that it's the callback
        callback = params;
        params = undefined;
    
        // Otherwise, build a param string
      } else if (params && typeof params === "object") {
        type = "POST";
      }
    
      // If we have elements to modify, make the request
      if (self.length > 0) {
        jQuery.ajax({
          url: url,
    
          // If "type" variable is undefined, then "GET" method will be used.
          // Make value of this field explicit since
          // user can override it through ajaxSetup method
          type: type || "GET",
          dataType: "html",
          data: params,
          // Rodes add
          context: self,
          converters: {
            "text html": function (s) {
              return "<script type=\"text/javascript\">$(function(){pageInit.call($(\"#" + self.attr("id") + "\"), true);})</script>" + s;
            }
          },
        }).done(function (responseText) {
    
          // Save response for use in complete callback
          response = arguments;
    
          // If a selector was specified, locate the right elements in a dummy div
          // Exclude scripts to avoid IE 'Permission Denied' errors
          // Otherwise use the full result
          self.html(selector ? $("<div>").append(jQuery.parseHTML(escape(responseText.replace(rscript, "")))).find(selector) : escape(responseText));
          // If the request succeeds, this function gets "data", "status", "jqXHR"
          // but they are ignored because response was set above.
          // If it fails, this function gets "jqXHR", "status", "error"
        }).always(callback && function (jqXHR, status) {
          self.each(function () {
            callback.apply(this, response || [jqXHR.responseText, status, jqXHR]);
          });
        });
      }
    
      return this;
    },
    __dialog: jQuery.fn.dialog,
    dialog: function(arg1, arg2, arg3){
        if (typeof arg1 !== "string") {
            var $this = this;
            $this.find('form').each(function(){
                $(this).validate();
            });
            var _o = arg1 && arg1.open;
            arg1 = jQuery.extend({
                dialogClass: this.attr("id") + "_-dialog",
                bgiframe: true,
                autoOpen: false,
                modal: true,
                minWidth: 350
            }, arg1, {
                open: function(){
                    API.resize($this);
                    // $this.find(".ui-jqgrid-btable[role=grid]").each(function(){
                    // $cap(this).iGridFitSize();
                    // });
                    _o && _o.apply(this, arguments);
                }
            });
            var tparent = $(this).parent(), ndialog = this.__dialog(arg1, arg2, arg3);
            var dialogs;
            (dialogs = $("." + this.attr("id") + "_-dialog")).each(function(index, element){
                ((dialogs.length - 1) == index) ? (ndialog = $(element)) : ($(element).empty().remove());
            });
            // $("<div />").appendTo(tparent).destory(function(){alert("XXX")});
            // tparent.append(ndialog).css('display', '');
            $("div[id='" + DOMPurify.sanitize(this.attr("id")) + "'],span[id='" + DOMPurify.sanitize(this.attr("id")) + "']").each(function(){
                $(this).is(".ui-dialog-content") || $(this).empty().remove();
            });
            return ndialog.find('.ui-dialog-content');
        }
        return this.__dialog(arg1, arg2, arg3);
    }
});


var checkFormErrorAndRequired = function(form){
    var required;
    if (form.find(".data-error,.item-data-error").length) {
        return false;
    }
    if (!form.valid()) {
        // for cbcl
        // API.showErrorMessage(form.find(".data-error,.item-data-error").eq(0).data("errorMsg"));
        return false;
    }
    return true;
};
// overritter jQuery Method(ajax)
jQuery.extend({
    emptyFunction: function(){
        return true;
    },
    emptyJSON: {},
    __ajax: jQuery.ajax,
    ajax: function(s){
        s = $.extend({
            handler: "",
            action: "",
            formId: "",
            noHide: false,
            target: null,
            __tryCount: 0,
            __retryLimit: 10
        }, s);
        
        // 增加random數值以防browser cache
        s.data && (s.data._rad = parseInt(Math.random() * 100000, 10));
          if (s.formId) {
            var form = $("#" + s.formId);
            // for cbcl start
            if (!checkFormErrorAndRequired(form)) {
                return false;
            }else {
              $.extend(s.data, form.serializeData(s.noHide), s.data);
            }
        }
        // 自動產生form data 並轉為 json string 格式給server json to bean使用 add by fantasy 2011/07/29
/*        if (FormAction && FormAction.open){
          //當頁面的form太多時,又無指定formid導致處理速度過慢的問題  by fantasy 2012/01/17
          if (s.handler && s.handler != ""){
            //alert("formId["+s.formId+"] action["+s.action+"] handler["+s.handler+"]");
            if (s.formId == "" ){
              $("form").each(function(){
                  var _formName = $(this).attr("id");
                  if (_formName != null && _formName != ""){
                    if (s.data){
                      s.data[_formName] = JSON.stringify(
                          $.extend($(this).serializeData(),
                            {oid : ($(this).attr("formOid") || "")}
                          )
                      );
                    }
                  }
              });
            }else{
              if (s.data){
                s.data[s.formId] = JSON.stringify(
                    $.extend($("#"+s.formId).serializeData(),
                      {oid : ($("#"+s.formId).attr("formOid") || "")}
                    )
                );
              }
            }
          }
        }*/
        s = $.extend({
          headers : {
              'X-CSRF-TOKEN' : $("meta[name='_csrf']").attr("content")
          },
          url: __ajaxHandler,
          dataType: "json",
          cache: false,
          type: 'post',
          timeout: Properties.ajaxTimeOut
        }, s, {
          data: $.extend({}, window.responseJSON ? responseJSON : {}, {
            _pa: s.handler || '',
            formAction: s.action || '',
            txCode: window.responseJSON && responseJSON.txCode || window.txCode ||  "", // for Eloan
            _userId: userInfo && userInfo.userId || $(".system_msg tr td").filter(".info:first").children().html().match(/\d{6}(?=\))/),
            _branch: userInfo && userInfo.ssoUnitNo || $("#headerarea .system_info span").html().substr(0,3)
          }, s.data)
        });
        // ---------------------------------------------------------------------------------
        // 移除page 以防server side 無法抓取正確值
        // if(window.responseJSON) delete responseJSON.page;
        return jQuery.__ajax(s).done(function(data, status) {
          // 如有錯誤通知訊息則顯示於畫面上
          ilog.debug(data);
          // debugger;
          if(!data) {
            // debugger;
          } else if (data.ERROR_NOTIFY_MESSAGE) {
            API.ajaxErrorMessage(data.ERROR_NOTIFY_MESSAGE);
          }
          // 如有通知訊息則顯示於畫面上
          else if (data.NOTIFY_MESSAGE) {
            API.ajaxNotifyMessage(data.NOTIFY_MESSAGE);
          }
          // 如通知為他人開啟文件 for MEGA
          else if (data.FORCE_OPEN) {
            Mega.message("message", data.FORCE_OPEN.NOTIFY_MESSAGE, i18n && i18n.def && i18n.def.confirmTitle || "提示", function() {
              $("body").lockDoc(1);
              _openerLockDoc = 1;
            });
            delete data.FORCE_OPEN;
            $.extend(data, {
              docReadonly : true
            });
          } else if (data.openerLockDoc) {
            _openerLockDoc = 1;
            $("body").lockDoc(1);
            delete data.openerLockDoc;
            $.extend(data, {
              docReadonly : true
            });
            setIgnoreTempSave(true);
          } else if (data.docReadonly) {
            $("body").lockDoc(0);
            _docReadonly = 1;
            setIgnoreTempSave(true);
          }
          // s.success && s.success(data, status);
          data && data.ERROR_NOTIFY_MESSAGE && s.successError && s.successError(data, status);
        }).fail(function(xhr, status, e) {
          // s.error && s.error(xhr, status, e);
          var statusText = "";
          try {
            statusText = xhr.statusText;
          } catch (e) {
          }
          if (xhr.readyState == 1) {
            // request timeout
            ilog.server(i18n.def.timeout);
          } else if (!xhr.status && !statusText) {
            // request timeout
            ilog.server(i18n.def.connectError);
          } else if ((xhr.responseText != null)) {
            var _status =  xhr.status.toString();
            switch(_status){
                //IE BUG 在網路狀況連線不好時，容易出現下列錯誤，遇到此情況時，資料重送
                 case "12029":
                 case "12030":
                 case "12031":
                 case "12152":
                 case "12159":
                    this.__tryCount++;
                    if (this.__tryCount <= this.__retryLimit) {
                        ilog.debug("IE 連線有誤，重試連線, status : " + _status + "，重試 : " + this.__tryCount);
                        //alert(status)
                        var ajaxObject = this;
                        window.setTimeout(function(){
                            $.__ajax(ajaxObject);
                        }, 500);
                    } else {
                        errorCheck(xhr);
                    }
                    break;
                 default:
                    errorCheck(xhr);
            }
            //errorCheck(xhr);
          } else if (xhr.status && xhr.status != '200') {
            ilog.server("1 http error code: 「" + xhr.status + "」,時間：" + CommonAPI.getNow() + " Exception:" + e);
          }
        });
// return jQuery.__ajax($.extend({
// url: __ajaxHandler,
// dataType: "json",
// cache: false,
// type: 'post',
// timeout: Properties.ajaxTimeOut
// }, s, {
// success: function(data, status){
// //儲存後離開 add by fantasy 2013/01/30
// if (FormAction && FormAction.deferred){
// FormAction.deferred.resolve();
// return;
// }
//              
// //如有錯誤通知訊息則顯示於畫面上
// //ilog.debug(data);
// if (data.ERROR_NOTIFY_MESSAGE) {
// API.ajaxErrorMessage(data.ERROR_NOTIFY_MESSAGE);
// }
// //如有通知訊息則顯示於畫面上
// else if (data.NOTIFY_MESSAGE) {
// API.ajaxNotifyMessage(data.NOTIFY_MESSAGE);
// }
// //如通知為他人開啟文件 for MEGA
// else if (data.FORCE_OPEN) {
// Mega.message("message", data.FORCE_OPEN.NOTIFY_MESSAGE, i18n && i18n.def && i18n.def.confirmTitle || "提示", function(){
// $("body").lockDoc(1);_openerLockDoc=1;
// });
// delete data.FORCE_OPEN;$.extend(data,{docReadonly:true});
// }else if (data.openerLockDoc) {
// _openerLockDoc=1;$("body").lockDoc(1);delete data.openerLockDoc;$.extend(data,{docReadonly:true});
// setIgnoreTempSave(true);
// }else if (data.docReadonly){
// $("body").lockDoc(0);_docReadonly=1;
// setIgnoreTempSave(true);
// }
// s.success && s.success(data, status);
// data.ERROR_NOTIFY_MESSAGE && s.successError && s.successError(data, status);
// },
// error: function(xhr, status, e){
// //儲存後離開 add by fantasy 2013/01/30
// if (FormAction && FormAction.deferred){
// FormAction.deferred = null;
// }
//              
// s.error && s.error(xhr, status, e);
// if(s.localSave){
// try {
// if (responseJSON && responseJSON.page)
// $.extend(s.data, {page : responseJSON.page});
// tempSave.error && tempSave.error(JSON.stringify($.extend(s.data,{_pa:s.handler,formAction:s.action||s.data.formAction})));
// }catch (e) {}
// }
// var statusText = "";
// try {
// statusText = xhr.statusText;
// }
// catch (e) {
// }
// if (xhr.readyState == 1) {
// // request timeout
// ilog.server(i18n.def.timeout);
// }
// else if (!xhr.status && !statusText) {
// // request timeout
// ilog.server(i18n.def.connectError);
// }
// else if ((xhr.responseText != null)) {
// errorCheck(xhr);
// }
// else if (xhr.status && xhr.status != '200') {
// ilog.server("http error code: 「" + xhr.status + "」");
// }
// },
// data: $.extend({}, window.responseJSON ? responseJSON : {}, {
// _pa: s.handler || '',
// formAction: s.action || '',
// //viewPage use window.txCode ,mainPage use responseJSON.txCode
// //txCode: window.txCode || window.responseJSON && responseJSON.txCode || "" // for Eloan
// //txCode 在IE會變成Object
// txCode: getTxCode(),
// _userId: userInfo && userInfo.userId || $(".system_msg tr td").filter(".info:first").children().html().match(/\d{6}(?=\))/),
// _branch: userInfo && userInfo.ssoUnitNo || $("#headerarea .system_info span").html().substr(0,3)
// }, s.data)
// }));
    },
    // ajax queue
    _ajaxQueueContent: [],
    _ajaxQueueStatus: false,
    ajaxQueue: function(s){
        if (s.constructor == Array) {
            (function(tses){
                (function ajaxStart(ts){
                    if (ts) {
                        $.ajax($.extend({}, ts, {
                            success: function(data, textStatus, XMLHttpRequest){
                                ts.success && ts.success(data, textStatus, XMLHttpRequest);
                                ajaxStart(tses.shift());
                            }
                        }));
                    }
                })(tses.shift())
            })(s);
        }
        else {
            $._ajaxQueueContent.push(s);
            if (!$._ajaxQueueStatus) {
                (function ajaxStart(ts){
                    if (ts) {
                        $._ajaxQueueStatus = true;
                        $.ajax($.extend({}, ts, {
                            complete: function(xhr, status){
                                ts.complete && ts.complete(xhr, status);
                                ajaxStart($._ajaxQueueContent.shift());
                            }
                        }));
                    }
                    else {
                        $._ajaxQueueStatus = false;
                    }
                })($._ajaxQueueContent.shift());
            }
        }
    }
});

// 檔案上傳動作
jQuery.extend({
    capFileUpload: function(setting){
        var metaCsrf = DOMPurify.sanitize($("meta[name='_csrf']").attr("content"));
        var s = $.extend({
            uploadMsg: i18n.def.fileUploading,
            successMsg: i18n.def.fileUploadSuccess
        }, setting, {
          url: setting.url || (__ajaxHandler + "?txCode=" + txCode + "&_pa=" + (setting.handler || "") + "&limitSize="+ (setting.limitSize || 3145728) +  "&_csrf=" + metaCsrf )
        });
        var telm = $("#" + s.fileElementId), val = telm.val();
        
        if (!val.length) {
            CommonAPI.showErrorMessage(i18n.def.fileSelect);
            return;
        }
        // 檢核副檔名
        // if (!s.fileCheck)
        // return;
        if (s.fileCheck) {
            var regs = "";
            $(s.fileCheck).each(function(index, value){
                regs += (value + "|");
            });
            
            regs = regs.replace(/\|$/, "");
            if (!((new RegExp("(" + regs + ")$", "i")).test(val))) {
                CommonAPI.showErrorMessage(i18n.def.fileSelError + "  (" + regs + ")");
                return;
            }
        }
        var fcDfd = $.Deferred();
        if (s.dupFiles && s.dupFiles.length) {
            var fns = "";
            $(s.dupFiles).each(function(index, value){
              fns += (value + "|");
            });
            fns = fns.replace(/\|$/, "");
            if ((new RegExp("(" + fns + ")$", "i")).test(val)) {
              MegaApi.confirmMessage(i18n.def.confirmAttachFile, function(result){
                    if (result) {
                      s.url = s.url + "&deleteDup=true";
                      fcDfd.resolve();
                    }
                    else {
                      return;
                    }
                });
            }else{
              fcDfd.resolve();
            }
        }else{
          fcDfd.resolve();
        }
        fcDfd.done(function(){
          var uploadMsg = CommonAPI.showMessage(s.uploadMsg);
          $.ajaxFileUpload($.extend({}, s, {
              secureuri: false,
              complete: function(xhr, status){
                $.thickbox.close();
                typeof uploadMsg === "function" && uploadMsg.dialog('close');
                  try {
                    var json = JSON.parse(xhr.responseText);
                  } catch (e) {
                    logDebug("ajaxError", e);
                      json = {};                      
                  }
                  errorCheck(xhr) &&
                  (function(){
                      s.successMsg && CommonAPI.showMessage(s.successMsg);
                      // 如有通知訊息則顯示於畫面上
                      json.NOTIFY_MESSAGE && API.showMessage(json.NOTIFY_MESSAGE);
                      s.success && s.success(json);
                      json.ERROR_NOTIFY_MESSAGE && s.successError && s.successError(json, status);
                      return true;
                  })() ||
                  s.error && s.error(xhr, status, json);
                  s.complete && s.complete(xhr, status, json);
                  $.ajax({
                      handler: "commonformhandler",
                      data: {
                          formAction: "fileSuccess"
                      }
                  });
              },
              error: function(data, status, e){
                  typeof uploadMsg === "function" && uploadMsg.dialog('close');
                  CommonAPI.showErrorMessage(i18n.def.fileUploadError);
              },
              success: $.noop
          }));
        });
    },
    // 檔案下載動作
    capFileDownload: function(s){
      $.form.submit({
            url: __ajaxHandler,
            type: 'post',
            target: s.target||"_blank",
            encode: true,
            data: $.extend({
              _pa:s.handler || 'simplefiledwnhandler'
            },s.data||{})
        });
    }
});



// 下拉選單暫存變數
var icombos = {};
var CommonAPI = {
    /**
     * 產生對話框
     * 
     * @param {Object}
     *          settings
     * @param {String}
     *          action
     */
    iConfirmDialog: function(settings, action){
        var dialogId = settings.id || "iConfirmDialog";
        if (action && action === 'close') {
            $("#" + settings).dialog('close');
        }
        else if (settings === 'close') {
            $('#' + dialogId).dialog('close');
        }
        else {
            var s = $.extend({
                title: i18n.def.confirmTitle,
                closeName: i18n.def.cancel,
                closeBtnAction: null
            }, settings);
            var cDialog = $('#' + s.id);
            cDialog = ((cDialog.length) ? cDialog : $("<div style='iConfirmDialog' id='" + dialogId + "' title='" + s.title + "'><span id='" + dialogId + "Message'></span></div>").appendTo("body"));
            var defaultButton = {};
            defaultButton[s.closeName] = function(){
                cDialog.dialog('close');
            };
            var tmpClose = s.close ||
            function(){
            };
            delete s['close'];
            cDialog.dialog($.extend({
                bgiframe: false,
                autoOpen: false,
                modal: true
            }, $.extend(s, {
                close: function(){
                    tmpClose();
                    cDialog.dialog('destroy');
                }
            }), {
                buttons: $.extend(s.buttons, defaultButton)
            }));
            
            return cDialog.dialog('open').find('#' + dialogId + 'Message').html(s.message || i18n.def.actoin_001 || "").end();
        }
    },
    
    /**
     * 產生提示對話框
     * 
     * @param {String}
     *          title title
     * @param {Object}
     *          message message
     * @param {Object}
     *          action 關閉對話框後動作
     */
    showPopMessage: function(title, message, action){
        var randomID = "sysMessage" + parseInt(Math.random() * 1000, 10);
        var closeBtn = {};
        // closeBtn[i18n.def.close] = function(){
        // CommonAPI.iConfirmDialog(randomID, 'close');
        // };
        return CommonAPI.iConfirmDialog({
            id: randomID,
            closeName: i18n.def.close,
            title: message && !typeof message === "function" ? title : i18n.def.confirmTitle,
            message: message && !typeof message === "function" ? message : title,
            // buttons: closeBtn,
            close: function(){
                $("#" + randomID).remove();
                action && action();
                typeof message === "function" && message();
            }
        });
    },
    
    /**
     * 產生提示對話框(預設與showPopMessage同 提供給予各專案replace使用)
     * 
     * @param {String}
     *          title title
     * @param {Object}
     *          message message
     * @param {Object}
     *          action 關閉對話框後動作
     */
    showMessage: function(title, message, action){
        return API.showPopMessage(title, message, action);
    },
    
    /**
     * 產生提示對話框(預設與showPopMessage同 提供給予各專案replace使用)
     * 
     * @param {String}
     *          title title
     * @param {Object}
     *          message message
     * @param {Object}
     *          action 關閉對話框後動作
     */
    showErrorMessage: function(title, message, action){
        return API.showPopMessage(title, message, action);
    },
    
    
    /**
     * 產生dialog Grid 以供引入使用
     * 
     * @param {Object}
     *          settings
     */
    includeGrid: function(settings, action){
        if (typeof settings === 'string' && !action || typeof settings !== 'string' && !settings.id) 
            return;
        
        var brd = $("#" + settings.id || settings || "");
        if (action === 'close') {
            return brd.dialog('close');
        }
        var s = $.extend(true, {
            handler: '',
            title: '',
            subtitle: i18n.def.grid_selector,
            dblclick: null,
            data: {},
            multiselect: false,
            colModel: null,
            autoSetResponse: {},
            buttons: {},
            loadonce: false,
            localFirst: false,
            loadComplete: function(){
            }
        }, settings || {});
        if (!brd.length) {
            brd = $("<div id='" + settings.id + "' title='" + s.title + "'><div id='" + settings.id + "Grid' /></div>").appendTo($('body'));
            // brd.empty().append("");
            for (var key in s.autoSetResponse) {
                $("#" + s.autoSetResponse[key]).val('');
            }
            var tGrid = brd.find("#" + settings.id + "Grid").iGrid({
                handler: s.handler,
                postData: s.data,
                height: 230,
                needPager: false,
                autowidth: false,
                multiselect: s.multiselect,
                caption: s.subtitle,
                colModel: s.colModel,
                sortBy: 'page',
                loadComplete: s.loadComplete,
                loadonce: s.loadonce,
                localFirst: true,
                ondblClickRow: function(rid){
                    if (s.dblclick) {
                        s.dblclick.call(this, rid, brd);
                    }
                    else {
                        var ret = $("#" + this.id).getRowData(rid);
                        for (key in ret) {
                            s.autoSetResponse[key] && $("#" + s.autoSetResponse[key]).val(ret[key]);
                        }
                        brd.dialog('close');
                    }
                }
            });
            brd.dialog({
                bgiframe: true,
                autoOpen: true,
                modal: true,
                resizable: false,
                width: tGrid.getGridParam("width") + 70,
                open: function(){
                    if (!s.localFirst) {
                        tGrid.trigger("reloadGrid");
                    }
                    s.localFirst = false;
                },
                close: function(){
                    // brd.dialog('destroy');
                },
                buttons: s.buttons
            }).find(".ui-jqgrid-titlebar-close").remove();
        }
        else {
            brd.find("#" + settings.id + "Grid").appendPostData(s.data);
            brd.dialog("open");
        }
        return brd;
    },
    
    /**
     * get Today(yyyy-MM-dd)
     */
    getToday: function(){
        var tDate = new Date();
        return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
        
    },
    getNow: function(){
      var tDate = new Date();
              return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" +
              (tDate.getDate() < 10 ? "0" : "") + tDate.getDate() + " " + tDate.getHours() + ":" + tDate.getMinutes()
              + "." + tDate.getSeconds();
    },
    
    /**
     * 於後端取得下拉選單資料
     * 
     * @param {Array ||
     *          String} updatekeys reutrn {JSON} comboList
     */
    loadCombos: function(updateKeys,comboaction){
        if (updateKeys === "") 
            return {};
        var nkeys = (typeof updateKeys === 'string') ? [updateKeys] : updateKeys, ukeys = [];
        for (var key in nkeys) {
            if (!icombos[nkeys[key]]) 
                ukeys.push(nkeys[key]);
        }
        (ukeys.length ||  (comboaction && comboaction.length)) &&
        $.ajax({
            type: 'post',
// headers : {
// 'X-CSRF-TOKEN' : $("meta[name='_csrf']").attr("content")
// },
            async: false,
            handler: Properties.ComboBoxHandler,
            data: {
                keys: ukeys || [],
                akeys:comboaction || []
            }
        }).done(function(json) {
          jQuery.extend(icombos, json);
        });
        return icombos;
    },
    /**
     * 於後端取得下拉選單資料(如果是數字為key的形態，轉成array,不會破壞原本的排序)
     * @param {Array || String} updatekeys
     * reutrn  {JSON} comboList
     */
    loadOrderCombosAsList: function(updateKeys,comboaction){
        if (updateKeys === "") 
            return {};
        var nkeys = (typeof updateKeys === 'string') ? [updateKeys] : updateKeys, ukeys = [];
        for (var key in nkeys) {
            if (!icombos[nkeys[key]]) 
                ukeys.push(nkeys[key]);
        }
        (ukeys.length ||  (comboaction && comboaction.length)) &&
        $.ajax({
            type: 'post',
            async: false,
            handler: Properties.ComboBoxHandler,
            action:"doOrderJSONArray",
            data: {
                keys: ukeys || [],
                 akeys:comboaction || []
            }
        }).done(function(json){
          jQuery.extend(icombos, json);
        });
        return icombos;
    },
    
    /**
     * ajax 動作完成後錯誤訊息顯示方式(預設與showPopMessage同 提供給予各專案replace使用)
     * 
     * @param {String}
     *          msg
     */
    ajaxErrorMessage: function(msg){
        CommonAPI.showPopMessage(msg);
    },
    
    /**
     * ajax 動作完成後通知訊息顯示方式(預設與showPopMessage同 提供給予各專案replace使用)
     * 
     * @param {String}
     *          msg
     */
    ajaxNotifyMessage: function(msg){
        CommonAPI.showPopMessage(msg);
    },
    
    /**
     * 清除訊息
     */
    clearMessage: function(){
    
    },
    
    
    /**
     * 補滿字串
     * 
     * @param {string}
     *          data 轉入資料
     * @param {integer}
     *          length 長度
     * @param {boolean}
     *          rightAlign 是否補字串後方
     * @param {char}
     *          ch 補足字元
     */
    fillString: function(data, length, rightAlign, ch){
        var inlength = data.length;
        if (inlength >= length) 
            return data;
        for (var i = 0; i < (length - inlength); i++) {
            data = (!!rightAlign ? (data + ch || "0") : ((ch || "0") + data));
        }
        return data;
    },
    /**
     * 
     * @param {String}
     *          id
     * @param {String}
     *          action
     */
    triggerOpener: function(id, action){
        id = id || 'gridview';
        action = action || 'reloadGrid';
        try {
            window.opener.$("#" + id).trigger(action);
        } 
        catch (e) {
            ilog.debug(e);
        }
    },
    resize: function(obj){
        obj = obj ? $(obj) : $(this);
        obj.find("table.ui-jqgrid-btable:visible").each(function(){
            var t = $cap($(this));
            t.length && t.iGridFitSize();
        });
        obj.find("ul.ui-tabs-nav:visible").trigger("fitsize.tab")
        
    },
    
    /**
     * 建立i18n or key為變數之 JSON
     * 
     * @param {Object}
     *          array
     */
    createJSON: function(array){
        var json = {}
        for (var data in array) {
            json[array[data].key] = array[data].value;
        }
        return json;
    },
    
    /**
       * 判斷經辦是否為RPA虛擬行員ID
       * @param {String} userId
       */
      isRPAUser: function(userId){
          return userId > 78000 && userId < 80000;
      }
};

var API = CommonAPI;


// Common Validate
$.extend(CommonAPI, {
    /**
     * 檢核台灣身份証
     * 
     * @param {string}
     *          value
     * @param {Object}
     *          element
     */
    checkTWID: function(value, element){
        value = trimStr(value);
        if (/^[a-zA-Z](1|2)\d{8}$/i.test(value)) {
            try {
                $(element || this).val(value.toUpperCase());
            } 
            catch (e) {
            }
            var area = "ABCDEFGHJKLMNPQRSTUVXYWZIO";
            var sum = 0;
            var checkNum = value.substr(9, 1);
            sum = area.indexOf(value.substr(0, 1).toUpperCase()) + 10;
            sum = Math.floor(sum / 10) + (sum % 10 * 9);
            for (var i = 1; i < 9; i++) {
                sum += value.substr(i, 1) * (9 - i);
            }
            return (((checkNum == 0) && checkNum == (sum % 10)) || ((checkNum != 0) && ((10 - (sum % 10)) == checkNum)));
        }
        return false;
    },
    
    /**
     * 檢核台灣法人
     * 
     * @param {string}
     *          value
     * @param {Object}
     *          element
     */
    checkCompanyNo: function(value, element){
        var tbNum = new Array(1, 2, 1, 2, 1, 2, 4, 1);
        var temp = 0;
        var total = 0;
        var total_2 = 0;
        value = trimStr(value);
        if (/^\d{8}$/.test(value)) {
            for (var i = 0; i < tbNum.length; i++) {
                temp = value.charAt(i) * tbNum[i];
                if (value.charAt(6) == 7 && i == 6) {
                  total += 0; // 2021/07/26 倒數第二位為7時，乘積之合最後第二位數取0，並改為能被5整除
                  total_2 += 1; // 2021/07/26 倒數第二位為7時，乘積之合最後第二位數取1，並改為能被5整除
                } else {
                  total += Math.floor(temp / 10) + temp % 10;
                  total_2 += Math.floor(temp / 10) + temp % 10;
                }
            }
            return (total % 5 == 0 || total_2 % 5 == 0);
        }
        return false;
    },
    /**
     * 檢核 MegaID
     * @param {string} value
     * @param {Object} element
     */
    checkMegaID: function(value, element){
        try {
            $(element || this).val(value.toUpperCase());
        }
        catch (e) {
        }
        return /^[a-z0-9]{2}[Z]{1}[a-z0-9]{7}$/i.test(value);
    },
    /**
     * 檢核 外國自然人MEGAID
     * ^：表示匹配字串的開頭。
     * (19|20)：表示匹配以 19 或 20 開頭的年份。
     * \d{2}：表示匹配 2 位數字（月份）。
     * (0[1-9]|1[0-2])：表示匹配 01 到 12 之間的月份。
     * (0[1-9]|[12][0-9]|3[01])：表示匹配 01 到 31 之間的日期。
     * [A-Z]{2}：表示匹配 2 個大寫英文字母。
     * @param {string} value
     * @param {Object} element
     */
    checkForeignMegaID: function(value, element){
        try {
            $(element || this).val(value.toUpperCase());
        }
        catch (e) {
        }
		// 
		return /^(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[A-Z]{2}$/i.test(value);
    },
    /**
     * 外國人統一證號編碼檢查
     * 
     * @param {string}
     *          value
     * @param {Object}
     *          element
     */
    checkForeign: function(value, element){
        if (/^[a-zA-Z]{2}\d{8}$/i.test(value)) {
            try {
                $(element || this).val(value.toUpperCase());
            }
            catch (e) {
            }
            var area = "ABCDEFGHJKLMNPQRSTUVXYWZIO";
            var sum = 0;
            var checkNum = value.substr(9, 1);
            sum = area.indexOf(value.substr(0, 1).toUpperCase()) + 10;
            sum = Math.floor(sum / 10) + ((sum % 10) * 9 % 10);
            sum += ((area.indexOf(value.substr(1, 1).toUpperCase()) + 10) % 10 * 8);
            for (var i = 2; i < 9; i++) {
                sum += value.substr(i, 1) * (9 - i);
            }
            return (((checkNum == 0) && checkNum == (sum % 10)) || ((checkNum != 0) && ((10 - (sum % 10)) == checkNum)));
        }else if (/^[a-zA-Z](8|9)\d{8}$/i.test(value)) {
            // I-108-0193_07623_B1001 Web e-Loan 購置房屋擔保放款風險權數檢核表調整外來人口統一證號檢核邏輯,應可比照 checkTWID(value, element) 檢核碼的logic => 第2碼由(1|2)改為(8|9)
            try {
                $(element || this).val(value.toUpperCase());
            }
            catch (e) {
            }
            var area = "ABCDEFGHJKLMNPQRSTUVXYWZIO";
            var sum = 0;
            var checkNum = value.substr(9, 1);
            sum = area.indexOf(value.substr(0, 1).toUpperCase()) + 10;
            sum = Math.floor(sum / 10) + (sum % 10 * 9);
            for (var i = 1; i < 9; i++) {
              sum += value.substr(i, 1) * (9 - i);
            }
            return (((checkNum == 0) && checkNum == (sum % 10)) || ((checkNum != 0) && ((10 - (sum % 10)) == checkNum)));
        }
        return false;
    },

    
    /**
     * 檢核日期格式是否正確 (20090101 or 2009-01-01)
     * 
     * @param {string}
     *          value
     * @param {Object}
     *          element
     */
    date: function(value, element){
        value = value.replace(/-/g, "");
        if (value.length == 0 && !$(element).is(".requried")) 
            return true;
        if (/^[0-9]{8}$/.test(value)) {
            var year = parseInt(value.substr(0, 4), 10), month = parseInt(value.substr(4, 2), 10), day = parseInt(value.substr(6, 2), 10);
            var tDate = new Date();
            tDate.setFullYear(year, month - 1, day);
            if(year<=0)return false;
            return tDate.getFullYear() === year && tDate.getMonth() === (month - 1) && tDate.getDate() === day &&
            function(){
                try {
                    $(element || this).val(value.substr(0, 4) + "-" + (month < 10 ? "0" : "") + month + "-" + (day < 10 ? "0" : "") + day);
                } 
                catch (e) {
                }
                return true;
            }();
        }
        return false;
    },
    /**
     * 檢核日期格式是否正確 (200901)
     * 
     * @param {string}
     *          value
     * @param {Object}
     *          element
     */
  date3: function(value, element){
        value = value.replace(/-/g, "");
        if (value.length == 0 && !$(element).is(".requried")) 
            return true;
        if (/^[0-9]{6}$/.test(value)) {
            var year = parseInt(value.substr(0, 4), 10), month = parseInt(value.substr(4, 2), 10), day = 1;
            var tDate = new Date();
            tDate.setFullYear(year, month - 1, day);
            if(year<=0)return false;
            return  tDate.getFullYear() === year&& tDate.getMonth() === (month - 1) && tDate.getDate() === day &&
            function(){
                try {
                    $(element || this).val(value.substr(0, 4)  + (month < 10 ? "0" : "") + month );
                } 
                catch (e) {
                }
                return true;
            }();
        }
        return false;
    },
    /**
     * 2013.02.01 Mike Add
     * 增加正規表示式檢核
     * @param {string} value
     * @param {Object} element
     */
  regExp: function(value, element){
    var pattern = $(element).attr("pattern");
    
    if (new RegExp("^" + pattern + "$").test(value)) {
            return true;
    }
    
    $(element).data("realErrorMsg", i18n.def[$(element).attr("errMsgType")]);
        return false;
    },
    /**
     * 2012.08.24 Mike Add
     * 檢核日期格式是否正確 (2009/01)
     * @param {string} value
     * @param {Object} element
     */
  date4: function(value, element){
        value = value.replace(/\//g, "");
        if (value.length == 0 && !$(element).is(".requried")) 
            return true;
        if (/^[0-9]{6}$/.test(value)) {
            var year = parseInt(value.substr(0, 4), 10), month = parseInt(value.substr(4, 2), 10), day = 1;
            var tDate = new Date();
            tDate.setFullYear(year, month - 1, day);
      if(year<=0)return false;
            return  tDate.getFullYear() === year&& tDate.getMonth() === (month - 1) && tDate.getDate() === day &&
            function(){
                try {
                    $(element || this).val(value.substr(0, 4) + "/" + (month < 10 ? "0" : "") + month );
                } 
                catch (e) {
                }
                return true;
            }();
        }
        return false;
    },
    /**
     * 檢核台灣日期格式是否正確 (1000101 or 100-01-01)
     * 
     * @param {string}
     *          value
     * @param {Object}
     *          element
     */
    twDate: function(value, element){
        value = value.replace(/[-\/]/g, "");
        if (value.length == 0 && !$(element).is(".requried")) 
            return true;
        if (/^[0-9]{6,7}$/.test(value)) {
            value = API.fillString(value, 7, false, '0');
            var year = parseInt(value.substr(0, 3), 10), month = parseInt(value.substr(3, 2), 10), day = parseInt(value.substr(5, 2), 10);
            var tDate = new Date();
            tDate.setFullYear(year, month - 1, day);
            return tDate.getFullYear() === year && tDate.getMonth() === (month - 1) && tDate.getDate() === day &&
            function(){
                try {
                    // $(element || this).val(value.substr(0, 4) + "-" + (month < 10 ? "0" : "") + month + "-" + (day < 10 ? "0" : "") + day);
                } 
                catch (e) {
                }
                return true;
            }();
        }
        return false;
    },
    
    /**
     * 檢核時間格式 (hh:mm)
     * 
     * @param {string}
     *          value
     * @param {Object}
     *          element
     */
    checkTime: function(value, element){
        var tv = value.replace(/:/i, '');
        try {
            $(element).val(tv.substr(0, 2) + ":" + tv.substr(2, 2));
        } 
        catch (e) {
        }
        return /^(([01][0-9])|(2[0123]))(([0-5])([0-9]))$/.test(tv);
    },
    
    /**
     * 檢核IPV4
     * 
     * @param {string}
     *          value
     * @param {Object}
     *          element
     */
    checkIPV4: function(value, element){
        return (/^(\d+)\.(\d+)\.(\d+)\.(\d+)$/.test(value) && (RegExp.$1 < 256 && RegExp.$2 < 256 && RegExp.$3 < 256 && RegExp.$4 < 256));
    }
});

// log any js object to firebug console
var logDebug = function(level,msg){
    if (window.console && window.console.log) {
      try{
        console.log(level + " : " + msg.toString());
      }catch(e){}
    }
};

// 因fileUpload亦需檢核故提出為Method
var errorCheck = function(xhr, action) {
  var json, res = true;
  try {
    json = JSON.parse(xhr.responseText);
  } catch (e) {
    try {
      // var r = $("<p>"+xhr.responseXML.body.innerHTML+"</p>").text();
      var r = xhr.responseXML.body.innerHTML;
      if ((pos = r.lastIndexOf("\"")) != -1) {
        r = r.substr(0, pos + 1) + "}";
      }
      r = r.replace(/&lt;/g, "<").replace(/&gt;/g, ">");

      ilog.debug("r:" + r);
      json = JSON.parse(r);
    } catch (e) {
      try {
        var r = DOMPurify.sanitize($("<p>" + xhr.responseXML.body.innerHTML + "</p>").text());
        ilog.debug("r2:" + r);
        json = JSON.parse(r);
      } catch (e) {
        logDebug("ajaxError", e);
        json = {};
      }
    }
  }
  ilog.debug(json);
  for ( var rule in errorCheck.rule) {
    res && json[rule] && (res = errorCheck.rule[rule](xhr, action, json));
  }

  if (res && xhr.status && xhr.status != '200' || xhr.status == 0) {
    ilog.server("2 http error code: 「" + xhr.status + " " + xhr.statusText+ "」,時間：" + CommonAPI.getNow());
    return false;
  }
  return res;
};

var getErrorMessage = function(xhr){
    var json;
    try {
        json = JSON.parse(xhr.responseText);
    } 
    catch (e) {
        logDebug("ajaxError", e);
        json = {};
    }
    // ilog.debug(json);
    for (var rule in errorCheck.rule) {
        if (json[rule]) {
            return json[rule];
        }
    }
    
    if (res && xhr.status && xhr.status != '200') {
      ilog.server("3 http error code: 「" + xhr.status + " " + xhr.statusText+ "」,時間：" + CommonAPI.getNow());
        return "connect error";
    }
    return "";
}

errorCheck.rule = {

    AJAX_CLOSE_PAGE_HANDLER_EXCEPTION: function(xhr, action, json){
        ilog.debug(json.AJAX_CLOSE_PAGE_HANDLER_EXCEPTION);
        ilog.debug(encodeURIComponent(json.AJAX_CLOSE_PAGE_HANDLER_EXCEPTION));
        API.showErrorMessage(json.AJAX_CLOSE_PAGE_HANDLER_EXCEPTION, function(){
            window.close();
        });
        // API.loadPage("../error/errormsg?errorMsg=" + encodeURIComponent(encodeURIComponent(json.AJAX_CLOSE_PAGE_HANDLER_EXCEPTION)), $("#" + Properties.innerPageFrameId));
        return false;
    },
    AJAX_MESSAGE_HANDLER_EXCEPTION: function(xhr, action, json){
        // ilog.debug(json.AJAX_MESSAGE_HANDLER_EXCEPTION);
        // CommonAPI.showPopMessage(json.AJAX_MESSAGE_HANDLER_EXCEPTION, action || undefined);
        CommonAPI.ajaxErrorMessage(json.AJAX_MESSAGE_HANDLER_EXCEPTION);
        return false;
    },
    AJAX_HANDLER_EXCEPTION: function(xhr, action, json){
        try {
            ilog.debug(xhr.status + " : " + xhr.statusText +
            ", " +
            json.AJAX_HANDLER_EXCEPTION);
        } 
        catch (e) {
        }
        ilog.server(json.AJAX_HANDLER_EXCEPTION);
        return false;
    },
    AJAX_HANDLER_TIMEOUT: function(xhr, action, json){
        try {
            ilog.debug(xhr.status + " : " + xhr.statusText +
            ", " +
            json.AJAX_HANDLER_TIMEOUT);
        } 
        catch (e) {
        }
        ilog.server(json.AJAX_HANDLER_TIMEOUT);
        window.setCloseConfirm(false);
        // 2022.10.24 checkmarx, Client DOM Open Redirect
        CommonAPI.showPopMessage(i18n.def.sessionTimeout, function(){
          logout();
        }); 
        return false;
    },
  AJAX_HANDLER_SSO_SESSION_INVALID : function(xhr, action, json) {
    try {
      ilog.debug(xhr.status + " : " + xhr.statusText + ", "
          + json.AJAX_HANDLER_SSO_SESSION_INVALID);
    } catch (e) {
    }
    ilog.server('SSO_SESSION_INVALID('+json.AJAX_HANDLER_SSO_SESSION_INVALID+')');    
    window.setCloseConfirm(false);    
    CommonAPI.showPopMessage(i18n.def.sessionTimeout, function(){
      logout();
        });   
    return false;
  },
    AJAX_HANDLER_SSO_ERROR : function(xhr, action, json) {
    try {
      ilog.debug(xhr.status + " : " + xhr.statusText + ", "
          + json.AJAX_HANDLER_SSO_ERROR);
    } catch (e) {
    }
    // ilog.server('SSO_ERROR ('+json.AJAX_HANDLER_SSO_ERROR+')');
        CommonAPI.ajaxErrorMessage(json.AJAX_HANDLER_SSO_ERROR);
    return false;
  }
};
var pageInit = function(isSubPage){
    var $_this = $(this);
    // tab 處理
    require(['jqueryui'], function() {
      $_this.find('.doc-tabs:first,.funcContainer').show();
      setTimeout(function(){
        //設定ckeditor
        $_this.find(".ickeditor:visible").each(function(){
          try{
            // var editor = CKEDITOR.instances[$(this).attr("id")];
            var editor = $('#' + DOMPurify.sanitize($(this).attr("name"))).data('ckeditor');
              if (editor) editor.destroy(true); //fix firefox by fantasy 2012/05/09
            }catch(e){
              ilog.debug(e);
            }
              $(this).ckeditor();
          });
      },1);
      $_this.find("button").not('[class*=fg-button]').button().addClass("fg-button").wrap("<span class=\"fg-buttonset\" />").wrap("<span class=\"fg-child\" />");
      $_this.find(".tabs").tabs().on('tabsshow.resize', function(event, ui){
          // fix multi trigger
          event.currentTarget === event.target && API.resize(this);
      });
      // select current page
      if (!isSubPage && typeof responseJSON != "undefined" && responseJSON.page) {
          var ul = $("body").find(".tabs:first  ul:first"), current_tab = ul.find("li > a[goto=" + responseJSON.page + "]").parent();
          var ind = ul.children("li").index(current_tab);
          ul.closest(".tabs").tabs('option', 'active', ind > 0 ? ind : 0);
      }
    });
    // 轉換common Item [placeholder],
    $_this.find("[commonitem]").each(function(){
        var $cthis = $(this), common = Properties.commonItem && Properties.commonItem[$cthis.attr("commonitem")];
        if (!common) 
            return;
        else {
            if (common.valid) {
                $cthis.on("blur.comm", common.valid);
            }
            for (var attr in common) {
                switch (attr) {
                    case "class":
                        $cthis.attr("class", $cthis.attr("class") + " " + common[attr]);
                        break;
                    case "css":
                        $cthis.css(common[attr]);
                        break;
                    case "maxlength":
                        $cthis.attr(attr) == -1 && $cthis.attr(attr, common[attr]);
                        break;
                    case "size":
                      $cthis.attr(attr) == -1 && $cthis.attr(attr, common[attr]);
                    case "valid":
                        break;
                    default:
                        $cthis.attr(attr, $cthis.attr(attr) || common[attr]);
                        break;
                }
            }
        }
        // 放入padding 功能
    });
    $_this.find("input[padding]").each(function(){
        $(this).on("blur.fill", function(){
            var $fthis = $(this);
            if ($fthis.val().length) {
                $fthis.val(API.fillString($fthis.val(), parseInt($fthis.attr("padding"), 10), $fthis.attr("paddingright") == "true", $fthis.attr("paddinchar") || "0"));
            }
        });
    });
    $("input#custId").addClass("trim alphanum upText");
    // mask
    var masks = [];
    if (Properties.itemMaskRule) {
        for (var key in Properties.itemMaskRule) {
            masks.push(key);
        }
    }
    var maskItem = $_this.find(masks.join(","));
    $.each(masks, function(i, v){
        maskItem.filter(v).each(function(){
            var $this = $(this);
            if (typeof Properties.itemMaskRule[v] === "function") {
                $this.data("maskRule", Properties.itemMaskRule[v]).on("focus.mask", function(){
                    $this.__val($this.data("realValue") || $this.val() || "");
                }).on("keypress.mask", function(){
                    $this.data("realValue", $this.__val())
                }).on("blur.mask", function(){
                    $this.data("realValue", $this.__val())
                    $this.trigger("mask");
                }).on("change.mask", function(){
                    $this.data("realValue", $this.__val())
                    $this.trigger("mask");
                }).on("mask", function(){
                  if($this.is("span")) {
                        $this.text(Properties.itemMaskRule[v].call($this));
                  }else
                    $this.__val(Properties.itemMaskRule[v].call($this));
                });
            }
        });
    });
    
    if (!isSubPage) {
        if (/(home)$/i.test(location.pathname)) {
            // 如為主頁面將window name 設為mainPage
            window.name = "mainPage";
        }
        /**
         * set ajax default
         */
        require(['blockui'], function() {
          $(document).on("ajaxStart", function(event, xhr){
            if($.blockUI) {
              $.blockUI({
                fadeIn: 0,
                fadeOut: 0,
                message: i18n.def.loading + '  <img src="'+webroot+'/img/ajax-loader.gif"/>',
                css: {
                    'z-index': 10010,
                    top: '0',
                    left: '40%',
                    backgroundColor: '#E0ECFF',
                    border: '1px',
                    cursor: null,
                    '-webkit-border-radius': '5px',
                    '-moz-border-radius': '5px',
                    // opacity: .85,
                    'font-size': '1.0em',
                    
                    padding: '2px',
                    fontWeight: 'bolder',
                    height: '25px',
                    width: '250px',
                    color: '#000'
                },
                overlayCSS: {
                    'z-index': 10010,
                    cursor: null,
                    backgroundColor: '#fff',
                    opacity: 0
                }
              });
            } else {
              console.warn('no blockui');
            }
          });
          
          $(document).on("ajaxStop", function(){
            if($.blockUI) {
              $.unblockUI();
            } else {
              console.warn('no blockui');
            };
          }).on("ajaxComplete", function(event, xhr, settings){
          }).on("ajaxSuccess", function(event, xhr){
          }).on("ajaxError", function(event, xhr, ajaxOptions, thrownError){
          });
        });
    }
    require(['jqueryui'], function() {
      // button
      $_this.find("button").wrapInner("<span class=\"ui-button-text\"></span>");
      // 設定可輸欄位才可選日期
      $.datepicker.setDefaults({
        dateFormat: 'yy-mm-dd',
        buttonImageOnly: true,
        showButtonPanel: true,
        changeMonth: true,
        changeYear: true,
        yearRange : "1900:2100",
        buttonImage: webroot+'/img/icon-calender3.gif',
        showOn: 'both',
        beforeShow: function (input, inst) {
          $.datepicker._pos = $.datepicker._findPos(input);
          $.datepicker._pos[1] += $(this).outerHeight();
        }
      });
      $_this.find("input.date").filter(function(){
          return !$(this).attr('readonly');
      }).datepicker();
    });
    // auto bind field valildate;
    require(['validate'], function() {
      jQuery.validator.injectFieldVal();
      $_this.find('form').each(function(){
        $(this).validate({
          ignore : ".ck"
        });
      });
    });
    var combos, combokeys = [],comboaction=[], updatekeys = [];
    (combos = $_this.find('select[comboKey],select[comboaction]')).each(function(){
        var key = null;
        (key = $(this).attr("comboKey"))&& combokeys.push(key);
    (key = $(this).attr("comboaction")) && comboaction.push(key);
    });
    CommonAPI.loadCombos(combokeys, comboaction);
    combos.each(function(){
    var $cthis = $(this);
        $cthis.setOptions(icombos[$cthis.attr("comboKey") || $cthis.attr("comboaction")], false);
    });
    combos = combokeys = comboaction = null;
};

/**
 * 檢查Form data有無異動 edit by fantasy 2012/05/11
 */
var FormAction = {
  open : true,
  init : function(id){
    if (Array.isArray(id)){
      for (var value in id){
        FormAction.initData(id[value]);
      }
    }else if (id){
      FormAction.initData(id);
    }else{
      $("form").each(function(){
        FormAction.initData($(this).attr("id"));
      });
    }
  },
  check : function(id, fn){
    var result = true;
    
    if (typeof id === "function"){
      $("form").each(function(){
        if (result) result = FormAction.checkData($(this).attr("id"));;
      });
      FormAction.checkIsFunction(result, id);
    }else if (Array.isArray(id)){
      for (var value in id){
        if (result) result = FormAction.checkData(id[value]);
      }
      FormAction.checkIsFunction(result, fn);
    }else{
      if (id){
        result = FormAction.checkData(id);
      }else{
        $("form").each(function(){
          if (result) result = FormAction.checkData($(this).attr("id"));;
        });
      }
      FormAction.checkIsFunction(result, fn);
    }
    return result;
  },
  checkIsFunction : function(result, fn){
    if (typeof fn === "function"){
      if (!result){
        MegaApi.confirmMessage(i18n.def["confirmContinueRun"], function(action){
          if (action) fn();
        });
      }else{
        fn();
      }
    }
  },
  initData : function(formId){
    if (FormAction.isForm(formId)){
      setTimeout(function(){
        $("body").data(formId, FormAction.getData(formId));
      },100);
    }
  },
  checkData : function(formId, fn){
    var result = true;
    if (FormAction.isForm(formId)){
      var beforeData = $("body").data(formId);
      var currData = FormAction.getData(formId);
      result = (beforeData == currData);
    }
    FormAction.checkIsFunction(result, fn);
    return result;
  },
  isForm : function(formId){
    return (($("#"+DOMPurify.sanitize(formId)).prop("tagName") || '').toLowerCase() == 'form');
  },
  getData : function(formId){
    return $("#"+DOMPurify.sanitize(formId)).find("input,select,textarea").filter(":not(disabled):not(readonly)").serialize();
  },
  // window.onunload 傳入 document.body
  processMemoryLeak : function(){
  },
  purge : function(d){
    var a = d.attributes, i, l, n;
    if (a) {
        for (i = a.length - 1; i >= 0; i -= 1) {
          n = a[i].name;
          // if (typeof d[n] === 'function') {
          // d[n] = null;
          // }
          d[n] = null;
        }
    }
    a = d.childNodes;
    if (a) {
        l = a.length;
        for (i = 0; i < l; i += 1) {
          FormAction.purge(d.childNodes[i]);
        }
    }
  },
  /**
   * exit add by fantasy 2013/01/30
   */
  exit : function(){
  if ($('#btnSave').is(':hidden')){
      API.confirmMessage(i18n.def['flow.exit'], function(res){
        res && window.close();
      });
    }else{
      FormAction.confirm();
    }
  },
  /**
   * confirm add by fantasy 2013/01/30
   */
  confirm : function(){
    if ($('#FormActionConfirmThickBox').length == 0){
      $('body').append('<div id="FormActionConfirmThickBox" style="display:none;" >'
          +'<div>'+i18n.def['flow.exit'] +'</div></div>');
    }
  thickboxOptions.customButton = ['saveDataClose', 'CloseWithoutSave', 'cancel'];
  $('#FormActionConfirmThickBox').thickbox({
    title : i18n.def['confirmTitle'] || '提示',
    width : 300,
    height : 125,
    align : 'right',
    valign: 'bottom',
    buttons : {
      'saveDataClose' : function(){
        FormAction.saveDataClose();
      },
      'CloseWithoutSave' : function(){
        window.close();
      },
      'cancel' : function(){  
        $.thickbox.close();
      }
    }
  });
  },
  /**
   * saveDataClose add by fantasy 2013/01/30
   */
  deferred : null,
  saveDataClose : function(){
    FormAction.deferred = $.Deferred().done(function(){
    CommonAPI.triggerOpener("gridview", "reloadGrid");
      window.close();
    });
    $('#btnSave').click();
  }
};

// page init()
$(function(){
    pageInit.call($('body'));
    var siteLocaleIdx = userInfo && userInfo.userLocale;
    $('#site_locale').change(function(event){
        if (confirm('切換語系將會遺失目前編輯中的資料，確定要繼續嗎?\nSwith locale will lost unsaved data! Press (OK) to continue.')) {
            // window.location.href += '?lang=' + $('#site_locale').val();
            $.ajax({
                url: window.location.href,
                data: {
                  _pa : 'i18nhandler',
                  formAction:"switchLocale",
                  lang: $('#site_locale').val()
                }
            }).done(function() {
              window.location.reload();
            });
        } else {
            $('#site_locale').val(siteLocaleIdx);
        }
    }).val(siteLocaleIdx);
    
    if (typeof sysurl != "undefined") {
    var url_arr = {'CES': '','LMS': '','CMS': '','COL': '','DEB': '','RPS': '','ADM': ''};
    var name_arr = {'CES': '','LMS': '','CMS': '','COL': '','DEB': '','RPS': '','ADM': ''};        
        var strTemp = '';
        for (var o in sysurl) {
            var data = sysurl[o];
            url_arr[data.code] = data.url;
            name_arr[data.code] = data.name;
        }        
        // 依排序後輸出
        $("#elSystem option").remove();
        var subsysSelect = $('#elSystem');
        for (var key in url_arr) {
            if (url_arr[key]) {
                subsysSelect.append($("<option>"+DOMPurify.sanitize(name_arr[key])+"</option>").attr("value", key));
            }
        }
        name_arr = null;
        subsysSelect = null;
        $('#elSystem').val(currentSys);        
        $('#elSystem').change(function(event){
            selSysId = $(this).val();
            if(selSysId != currentSys){
              var targetId = selSysId + '_' + userInfo.unitNo;
              var url = url_arr[selSysId];
              if (url) {
                  url += '?windowID=' + targetId;
                  url += '&lightID=' + userInfo.lightId;
                  url += '&loginUnit=' + userInfo.unitNo;
                  url += '&mainLocale=' + userInfo.userLocale;
                  url += '&ssoType=' + userInfo.ssoType;
                  url += '&Flag=Y';
                  url += '&j_password=' + userInfo.lightId;
                  window.open(url, targetId);
              }
              $(this).val(currentSys);
          }            
        });
    }
});
/**
 * thickbox ckeditor class set tckeditor
 * 
 * <AUTHOR>
 * @version 2012/05/02,Fantasy,new
 */
var EditorAction = {
  formId : null,
  id : null,
  ckeditor : null,
  readOnly : false,
  name : 'editorAction',
  tckeditor : 'tckeditor_',
  thickboxId : 'tckeditor_thickbox',
  preview : 'tckeditor_preview_',
  parsePreview : function(str){
    var result = {};
    if (str != null){
      var jsonStr = '';
      var strArray = str.split(';');
      for (var s in strArray){
        var chrArray = strArray[s].split(':');
        var len = chrArray.length;
        if (jsonStr != '') jsonStr += ',';
        if (len >= 2){
          jsonStr += '"'+chrArray[0]+'":"'+chrArray[1]+'"';
        }else if (len == 1){
          jsonStr += '"'+chrArray[0]+'":"true"';
        }
      }
      result = JSON.parse('{'+jsonStr+'}');
    } 
    return result;
  },
  transform : function($obj){
    if ($obj){
      $obj.hide();
      var showType = $obj.attr('showType') || '';
      var id = $obj.attr('id') || $obj.attr('name');
      var msg = DOMPurify.sanitize($obj.attr('displayMessage') || '');
      var $form = $obj.closest('form');
      var formId = DOMPurify.sanitize($form.attr('id') || '');
      
      var preview = DOMPurify.sanitize($obj.attr("preview"));
      var previewObj = EditorAction.parsePreview(preview);
      if (preview != null){
        $obj.after('<div id="'+EditorAction.preview+id+'" class="preview" '+(preview ? 'preview="'+preview+'"' : '')
            + (previewObj.disabled ? '' : ' ondblclick="EditorAction.parse(\''+formId+'\',\''+id+'\');" ')
            +' ></div>');
        $obj.change(function() {
          var $this = $(this);
          var newval = $this.val();
          var $div = $('#'+EditorAction.preview+$this.attr('id'));
          if ($div.length>0) $div.html(DOMPurify.sanitize(newval));
          return newval;
        });
        EditorAction.setPrevew($('#'+EditorAction.preview+id));
      }
      
      if (!previewObj.disabled){
        // have msg create a link
        var $span = $obj.next('#'+EditorAction.tckeditor+id);
        if ($span.length == 0){
          $obj.after('<span id="'+EditorAction.tckeditor+id+'" ></span>');
        }

        if(showType=="b"){
          $obj.next('#'+EditorAction.tckeditor+id).html(i18n.def['openTckeditBoxmsg'].replace("{0}",EditorAction.display(msg)));
          $obj.next('#'+EditorAction.tckeditor+id).wrap('<button type="button" class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only fg-button forview" onClick="EditorAction.parse(\''+formId+'\',\''+id+'\');" />').wrap('<span class=text-only />');
          $obj.next("button.fg-button").wrap("<span class=\"fg-child\" />");
          $obj.next("span.fg-child").wrap("<span class=\"fg-buttonset\" />");
        }else{
          $obj.next('#'+EditorAction.tckeditor+id).html(EditorAction.display(msg))
          .wrap('<a href="#" onClick="EditorAction.parse(\''+formId+'\',\''+id+'\');" ></a>');
        }
      }
    }
  },
  setPrevew : function($div){
    var view = $div.attr("preview");
    var preview = EditorAction.parsePreview(view);
    // attr
    $div.parent('td,div,form').each(function(){
      var $parent = $(this);
      if (!preview['height'] && $parent.height() != 0) preview['height'] = $parent.height();
      if (!preview['width'] && $parent.width() != 0) preview['width'] = $parent.width();
    });
    $div.css(preview);
  },
  parse : function(formId, id){
    EditorAction.formId = formId;
    EditorAction.id = id;

    var $tb = $('#'+EditorAction.thickboxId);
    if ($tb.length == 0){     
      $('body').append('<div id="'+EditorAction.thickboxId+'" style="display:none;height:100%"><div>'
          +'<textarea id="'+EditorAction.name+'" class="ickeditor" ></textarea>'
          +'</div></div>');
      $('#'+EditorAction.thickboxId).find('.ickeditor').each(function(){
        $(this).ckeditor();
      });
    }
    
    var $obj = $(EditorAction.objID());
    // $obj.val() 特殊處理，避免 ckeditor5 把 border 相關 style 正規化後造成問題
    var beforeNormalize = DOMPurify.sanitize(EditorAction.beforeNormalize($obj.val()));
    var title = $obj.attr('displayMessage') || '';
    var editorWidth = $obj.attr('editorWidth') || 800;
    var editorHeight = $obj.attr('editorHeight') || 360;
    $('#'+EditorAction.thickboxId).thickbox({ // 使用選取的內容進行彈窗
      title : title,
      width : editorWidth,
      height : editorHeight,
      modal : true,
      valign : "bottom",
      align : "center",
      i18n : i18n.def,
      open : function() {
        $('#' + EditorAction.name).val(beforeNormalize);
      },
      buttons : {
        "sure" : function() {
          if (EditorAction.sure()) $.thickbox.close();
        },
        "cancel" : function() {
          $.thickbox.close();
        }
      }
    });
    
    // set value
    // EditorAction.ckeditor = CKEDITOR.instances[EditorAction.name];
    EditorAction.ckeditor = $('#' + EditorAction.name).data('ckeditor');
    if (EditorAction.ckeditor){
      EditorAction.ckeditor.setData(beforeNormalize);
      // EditorAction.ckeditor.resize(500, editorHeight-90);
    }
    // set readonly
    (function(objData){
      setTimeout(function(){
        try{
          // var editor = CKEDITOR.instances[EditorAction.name];
          var editor = $('#' + EditorAction.name).data('ckeditor');
          if (editor) {
            editor.setData(objData);
// editor.resize('100%',editorHeight-90);//已於CKEditor5棄用
          }
          EditorAction.setReadOnly($(EditorAction.objID()).prop('readOnly'));
        }catch(e){
          console.log(e);
        }
      }, 500);
    })(beforeNormalize);
    
    $obj = null;
  },
  sure : function(){
    if (EditorAction.objID()){
      // EditorAction.ckeditor = CKEDITOR.instances[EditorAction.name];
      EditorAction.ckeditor = $('#' + EditorAction.name).data('ckeditor');
      if (EditorAction.ckeditor){
        var data = EditorAction.ckeditor.getData();
        var $obj = $(EditorAction.objID());
        // 2022.09.22 改為用常數定義 style，避免不同環境存取 inet css 的問題
        // 惟在維護上可能比較不方便
        const css = '<style>figure{display:block}figure.table table{width:100%;border-collapse:collapse}figure.table td{border:1px solid black;padding:1px}</style>'; 
        data = data.replace(/<link[^>]*>/, '');
        data = data.replace(/<style[^>]*>/, '');
        data = css + data;
        $obj.val(data);
        // $obj = null;
        EditorAction.ckeditor.setData(''); // clear
      }
    }
    return true;
  },
  setReadOnly : function(sure){
    // EditorAction.ckeditor = CKEDITOR.instances[EditorAction.name];
    EditorAction.ckeditor = $('#' + EditorAction.name).data('ckeditor');
    if (EditorAction.ckeditor){
      // var _ickeditor = CKEDITOR.instances[EditorAction.name].document.$;
      sure ? EditorAction.ckeditor.enableReadOnlyMode('ck-editor__main') : EditorAction.ckeditor.disableReadOnlyMode('ck-editor__main');// CKEditor5唯讀的API
      $(".ck-editor__top")[sure?'hide':'show'](); 
      // EditorAction.ckeditor.setReadOnly( sure );
    }
  },
  objID : function(){
    return (EditorAction.formId && EditorAction.formId != '' 
      ? '#'+EditorAction.formId+' ' : '')+'#'+EditorAction.id;
  },
  display : function(msg){
    var result = (msg ? msg : (i18n ? i18n.def['look'] : 'look' ));
    return result;
  },
  beforeNormalize : function(content) {
    let htmlData = $('<div>' + content + '</div>');
    htmlData.find('table').each(function() {
      let $this = $(this);
      if ($this.attr('border') && $this.attr('border') == '0') {
        // 舊資料若設定 table border="0" cellpadding="0" cellspacing="0"，不顯示 border、padding 為 0
        // 除了將 table 的 border 相關 style 設定為 0px solid white 外，內部的 td 也須做一樣設定
        // 要給完整的 border 設定，否則 ckeditor5 正規化處理後會移除 border style
        $this.css('border', '0px solid white');
        $this.find('td').css('border', '0px solid white').css('padding', '0px'); // cellpadding="0"
      }
    });
    htmlData.find('td').each(function () {
      let $this = $(this);
      if($this.attr('style') && $this.attr('style').indexOf('border-top-color') >= 0) {
        // 針對舊資料設定 border-top-color 為空值以隱藏格線進行處理，設定 border 相關 style
        // 要給完整的 border 設定，否則 ckeditor5 正規化處理後會移除 border style
        $this.css('border', '0px solid white');
      }
    });
    return htmlData.html();
  }
};

