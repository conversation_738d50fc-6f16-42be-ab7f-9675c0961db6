package tw.com.jcs.flow.core;

import static tw.com.jcs.flow.FlowConstant.CACHE_SIZE;
import static tw.com.jcs.flow.FlowConstant.DEFINITION_LOCATION;
import static tw.com.jcs.flow.FlowConstant.TABLE_INSTANCE;
import static tw.com.jcs.flow.FlowConstant.TABLE_INSTANCE_HISTORY;
import static tw.com.jcs.flow.FlowConstant.TABLE_SEQUENCE;
import static tw.com.jcs.flow.FlowConstant.TABLE_SEQUENCE_HISTORY;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileFilter;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.ref.WeakReference;
import java.net.URL;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

import javax.sql.DataSource;

import org.apache.commons.collections4.map.LRUMap;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.ObjectMapper;

import tw.com.jcs.flow.FlowDefinitionParser;
import tw.com.jcs.flow.FlowEngine;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.provider.IdProvider;
import tw.com.jcs.flow.provider.ObjectFactory;
import tw.com.jcs.flow.service.FlowService;
import tw.com.jcs.flow.service.impl.FlowServiceImpl;

/**
 * <pre>
 * <h1>流程引擎</h1> 控管流程引擎執行時所需的所有資源
 * </pre>
 * 
 * @since 2022年12月22日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月22日
 *          <li>2024年10月21日 修正CHECKMARX
 *          </ul>
 */
public class FlowEngineImpl implements FlowEngine {

    private static final Logger log = LoggerFactory.getLogger(FlowEngineImpl.class);

    DataSource dataSource;
    FlowServiceImpl service;
    FlowPersistence persistence;
    IdProvider idProvider;
    Map<String, FlowDefinitionImpl> definitions;
    ObjectFactory objectFactory;

    /**
     * 流程實體的快取Pool，確保同AP取得同一參考
     */
    Map<Object, WeakReference<FlowInstanceImpl>> instancePool;

    /**
     * 用於將流程資料做JSON轉換的工具類
     */
    final ObjectMapper jsonMapper = new ObjectMapper();

    /**
     * constructor
     */
    public FlowEngineImpl(DataSource dataSource, Map<String, String> cfg, ObjectFactory objectFactory, IdProvider idProvider) {
        this.dataSource = dataSource;
        this.objectFactory = objectFactory;
        this.idProvider = idProvider;
        init(cfg);
    }

    /**
     * 初始化資料
     * 
     * @param cfg
     */
    void init(Map<String, String> cfg) {
        persistence = new FlowPersistence(this);
        persistence.setInstanceTable(cfg.get(TABLE_INSTANCE));
        persistence.setSequenceTable(cfg.get(TABLE_SEQUENCE));
        persistence.setInstanceHistoryTable(cfg.get(TABLE_INSTANCE_HISTORY));
        persistence.setSequenceHistoryTable(cfg.get(TABLE_SEQUENCE_HISTORY));

        service = new FlowServiceImpl(this);

        int cacheSize = Integer.parseInt(cfg.get(CACHE_SIZE));
        instancePool = Collections.synchronizedMap(new LRUMap<>(cacheSize));

        definitions = new HashMap<String, FlowDefinitionImpl>();
        loadDefinition(cfg.get(DEFINITION_LOCATION));
    }

    public FlowService getService() {
        return service;
    }

    /**
     * 取得流程的DB處理器
     * 
     * @return
     */
    public FlowPersistence getPersistence() {
        return persistence;
    }

    public IdProvider getIdProvider() {
        return idProvider;
    }

    /**
     * 取得Pool中的流程實體，如找不到則回傳null
     * 
     * @param id
     * @return
     */
    public FlowInstanceImpl getPooledInstance(Object id) {
        WeakReference<FlowInstanceImpl> ref = instancePool.get(id);
        if (ref != null) {
            return ref.get();
        }
        return null;
    }

    /**
     * 將流程實體新增到Pool中
     * 
     * @param instance
     */
    public void addInstanceToPool(FlowInstanceImpl instance) {
        instancePool.put(instance.getId(), new WeakReference<FlowInstanceImpl>(instance));
    }

    /**
     * 從Pool中移除流程實體
     * 
     * @param id
     */
    public void removeInstanceFromPool(Object id) {
        synchronized (instancePool) {
            instancePool.remove(id);
        }
    }

    /**
     * 清除快取的流程實體
     * 
     */
    public void clearInstancePool() {
        synchronized (instancePool) {
            instancePool.clear();
        }
    }

    /**
     * 取得下一個流程的自動編號
     * 
     * @return
     */
    public synchronized Object getNextId() {
        return idProvider.getNextId();
    }

    /**
     * 取得下一個流程的自動編號
     * 
     * @param parameter
     * @return
     */
    public synchronized Object getNextId(Object[] parameter) {
        return idProvider.getNextId(parameter);
    }

    /**
     * 取得下一個流程的自動編號
     * 
     * @param instance
     * @return
     */
    public synchronized Object getNextId(FlowInstance instance) {
        return idProvider.getNextId(instance);
    }

    /**
     * 取得流程定義檔
     * 
     * @param defName
     *            流程義定名稱
     */
    public FlowDefinitionImpl getDefinition(String defName) {
        return definitions.get(defName);
    }

    /**
     * 載入流程定義檔
     * 
     * @param path
     *            路徑
     */
    void loadDefinition(String path) {
        // 從ClassPath取得流程定義的目錄
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        ClassLoader classLoader = objectFactory.getClassLoader();
        URL url = classLoader.getResource(path);
        if (url == null) {
            throw new FlowException("Can't find resource[" + path + "]");
        }

        int count = 0;
        FlowDefinitionParser parser = new FlowDefinitionParserImpl(objectFactory);

        // 過濾出流程定義檔案
        List<File> files = null;
        if ("file".equals(url.getProtocol())) {
        	// 修正Input_Path_Not_Canonicalized
        	// 用FilenameUtils.normalize
            files = getDefFiles(new File(FilenameUtils.normalize(url.getPath())));
            for (File file : files) {
                try {
                    InputStream is = new BufferedInputStream(new FileInputStream(file));
                    // 解析流程定義檔
                    FlowDefinitionImpl def = (FlowDefinitionImpl) parser.parse(is);
                    definitions.put(def.getName(), def);
                    is.close();
                    ++count;
                } catch (Exception e) {
                    log.error("Occur some errors on paring {}.", file.getAbsolutePath());
                    log.error("[loadDefinition]file ERROR!!", e);
                }
            }
        } else if ("jar".equals(url.getProtocol())) {
            try {
                JarFile jar = new JarFile(extractJarName(url));
                Enumeration<JarEntry> entries = jar.entries();
                while (entries.hasMoreElements()) {
                    JarEntry entry = entries.nextElement();
                    if (!entry.isDirectory() && entry.getName().startsWith(path) && entry.getName().endsWith(".jpdl.xml")) {
                        FlowDefinitionImpl def = (FlowDefinitionImpl) parser.parse(jar.getInputStream(entry));
                        definitions.put(def.getName(), def);
                        ++count;
                    }
                }
                jar.close();
            } catch (Exception e) {
                log.error("Occur some errors on paring {}.", url);
                log.error("[loadDefinition]jar ERROR!!", e);
            }
        }

        parser = null;
        log.info("Parse {} definition files done.", count);
    }

    /**
     * 擷取一段路徑
     * 
     * @param url
     * @return
     */
    private String extractJarName(URL url) {
        String path = url.getPath();
        int begin = path.startsWith("file:/") ? 6 : 0;
        int end = path.indexOf('!');
        return path.substring(begin, end > 6 ? end : path.length());
    }

    /**
     * 過濾並取得所有的流程定義檔(包含子目錄)
     * 
     * @param dir
     * @return
     */
    List<File> getDefFiles(File dir) {
        final List<File> files = new LinkedList<File>();
        dir.listFiles(new FileFilter() {
            public boolean accept(File file) {
                if (file.isDirectory()) {
                    files.addAll(getDefFiles(file));
                } else if (file.getName().endsWith(".jpdl.xml")) {
                    files.add(file);
                }
                return false;
            }
        });
        return files;
    }

    public ObjectFactory getObjectFactory() {
        return objectFactory;
    }
}
