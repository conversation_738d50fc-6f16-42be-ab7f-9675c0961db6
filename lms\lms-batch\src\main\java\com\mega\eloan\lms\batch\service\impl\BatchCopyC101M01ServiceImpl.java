package com.mega.eloan.lms.batch.service.impl;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.Score;
import com.mega.eloan.lms.base.constants.ScoreCardLoan;
import com.mega.eloan.lms.base.constants.ScoreNotHouseLoan;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.dao.C101M01ADao;
import com.mega.eloan.lms.dao.C101S01ADao;
import com.mega.eloan.lms.dao.C101S01BDao;
import com.mega.eloan.lms.dao.C101S01CDao;
import com.mega.eloan.lms.dao.C101S01DDao;
import com.mega.eloan.lms.dao.C101S01EDao;
import com.mega.eloan.lms.dao.C101S01FDao;
import com.mega.eloan.lms.dao.C101S01GDao;
import com.mega.eloan.lms.dao.C101S01G_NDao;
import com.mega.eloan.lms.dao.C101S01HDao;
import com.mega.eloan.lms.dao.C101S01IDao;
import com.mega.eloan.lms.dao.C101S01JDao;
import com.mega.eloan.lms.dao.C101S01KDao;
import com.mega.eloan.lms.dao.C101S01LDao;
import com.mega.eloan.lms.dao.C101S01MDao;
import com.mega.eloan.lms.dao.C101S01NDao;
import com.mega.eloan.lms.dao.C101S01PDao;
import com.mega.eloan.lms.dao.C101S01QDao;
import com.mega.eloan.lms.dao.C101S01Q_NDao;
import com.mega.eloan.lms.dao.C101S01RDao;
import com.mega.eloan.lms.dao.C101S01R_NDao;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.C120S01BDao;
import com.mega.eloan.lms.dao.C120S01CDao;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.dao.C120S01GDao;
import com.mega.eloan.lms.dao.C120S01QDao;
import com.mega.eloan.lms.dao.C120S01RDao;
import com.mega.eloan.lms.dw.service.DWRKCNTRNOService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.etch.service.EtchService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01C;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01G_N;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01I;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01Q_N;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C101S01R_N;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01G;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01R;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

@Service("batchCopyC101M01ServiceImpl")
/**
http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'batchCopyC101M01ServiceImpl','step':'1'}
http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'batchCopyC101M01ServiceImpl','step':'2_3'}
------------------------
DELETE FROM DWADM.DW_RKSCORE WHERE  mowtype='N' and mowVer1=3 and mowVer2=0 and br_cd='229' and data_src_dt>='2019-10-01' ;
DELETE FROM DWADM.DW_RKCREDIT WHERE mowtype='N' and mowVer1=3 and mowVer2=0 and br_cd='229' and data_src_dt>='2019-10-01' ;
DELETE FROM DWADM.DW_RKAPPLICANT WHERE mowtype='N' and mowVer1=3 and mowVer2=0 and br_cd='229' and data_src_dt>='2019-10-01' ;
------------------------
若在「簽報書」引入「借款人」時，L120S01M 有誤，執行以下SQL，撈出異常的 L120S01M.oid

-- 在 C101 的情境中，同一 mainId 底下只會有一個 custId,dupNo
-- 若有 N 個，表示 copy 異常
select oid from (select a.oid,a.custId, b.dupno from (select * from  lms.l120s01m where mainId in 
(
    select mainId  from  lms.l120s01m where mainId in (select mainId from lms.c101m01a) 
    group by mainId having count(*)>1
)) a left outer join lms.c101m01a b
on a.mainId=b.mainId and a.custId=b.custId and a.dupno=b.dupno
) t where dupno is null
------------------------
select oid from
(   select a.oid,a.mainid,b.dupno  from  lms.l120s01n a left outer join lms.l120s01m b
on a.mainId=b.mainId and a.custId=b.custId and a.dupno=b.dupno
) t where dupno is null
------------------------
select oid from
(   select a.oid,a.mainid,b.dupno  from  lms.l120s01o a left outer join lms.l120s01m b
on a.mainId=b.mainId and a.custId=b.custId and a.dupno=b.dupno
) t where dupno is null
 */
public class BatchCopyC101M01ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(BatchCopyC101M01ServiceImpl.class);

//	String proc_model = "G";
	String proc_model = "Q"; //判斷上傳DW時使用
//	String proc_model = "R";
	//~~~~~~~
	String srcBrId = "078";
	String srcCustId = "K292692523";
	String srcDupNo = "0";
	//~~~~~~~
	String targetBrId = "078";//測試分行
	//~~~~~~~
	int readTargetId_src_type = 9; //{1:由程式指定的ID_LIST, 9:由XLS讀取}
	String path = "cls/EJCIC_ID_TEST.xls";
//	String path = "cls/EJCIC_ID.xls";
	
	@Resource
	C101M01ADao c101m01aDao;

	@Resource
	C101S01ADao c101s01aDao;

	@Resource
	C101S01BDao c101s01bDao;

	@Resource
	C101S01CDao c101s01cDao;

	@Resource
	C101S01DDao c101s01dDao;

	@Resource
	C101S01EDao c101s01eDao;

	@Resource
	C101S01FDao c101s01fDao;

	@Resource
	C101S01GDao c101s01gDao;

	@Resource
	C101S01HDao c101s01hDao;

	@Resource
	C101S01IDao c101s01iDao;

	@Resource
	C101S01JDao c101s01jDao;

	@Resource
	C101S01KDao c101s01kDao;

	@Resource
	C101S01LDao c101s01lDao;

	@Resource
	C101S01MDao c101s01mDao;

	@Resource
	C101S01NDao c101s01nDao;

	@Resource
	C101S01PDao c101s01pDao;
	
	@Resource
	C101S01QDao c101s01qDao;
	
	@Resource
	C101S01RDao c101s01rDao;
	
	@Resource
	C120S01GDao c120s01gDao;
	
	@Resource
	C120S01QDao c120s01qDao;
	
	@Resource
	C120S01RDao c120s01rDao;
	
	@Resource
	C120M01ADao c120m01aDao;	
	@Resource
	C120S01ADao c120s01aDao;
	@Resource
	C120S01BDao c120s01bDao;
	@Resource
	C120S01CDao c120s01cDao;
	
	@Resource
	C120S01EDao c120s01eDao;
	
	@Resource
	C101S01G_NDao c101s01g_nDao;
	
	@Resource
	C101S01Q_NDao c101s01q_nDao;
	
	@Resource
	C101S01R_NDao c101s01r_nDao;
	
	@Resource
	CodeTypeService codeTypeService;

	@Resource
	EloandbBASEService eloandbbaseservice;

	@Resource
	MisdbBASEService misBaseService;

	@Resource
	EjcicService ejcicService;

	@Resource
	EtchService etchService;

	@Resource
	ScoreService scoreService;

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;
	
	@Resource
	CLS1131Service cls1131Service;
	
	@Resource
	DWRKCNTRNOService dwRkcntrnoService;

	Properties prop;
	
	@Override
	public JSONObject execute(JSONObject json) {
		
		JSONObject result = new JSONObject();
		
		String step = Util.trim(json.get("step"));		
		LOGGER.info("passed_param["+step+"]");
		try {
			if (Util.equals("1", step)){
				result.putAll(step1());
			}else if (Util.equals("2", step)){
				result.putAll(step2());
			}else if (Util.equals("3", step)){
				result.putAll(step3());
			}else if (Util.equals("2_3", step)){
				step2();
				step3();
			}else if (Util.equals("SingleL", step)){
				singleL();	
			}
		} catch (Exception ex) {
			result.put("success", false);
			LOGGER.error("[execute] Exception!!", ex);
		} finally {
			result.put("success", true);
		}

		LOGGER.info("end");
		return result;
	}

	/**
	 * 由{來源ID} copy 到{指定的ID_list}
	 * @return
	 * @throws CapException
	 */
	private JSONObject step1()throws CapException{
		
		JSONObject result = new JSONObject();
		//---
		
		C101M01A src_c101m01a = cls1131Service.findC101M01A(srcBrId, srcCustId, srcDupNo);
		
		if(src_c101m01a==null){
			result.put("msg", "find no src_c101m01a");	
		}else{
			ArrayList<String> custIdExistList = new ArrayList<String>();
			ArrayList<String> notNaturalList = new ArrayList<String>();
//			List<String> idList = readTargetId();
			
			HashMap<String, List<String>> ExcelMap = readTargetId_v2();
			
			List<String> mainidList = ExcelMap.get("mainid");
			List<String> idList = ExcelMap.get("id");
			List<String> dupnoList = ExcelMap.get("dupno");
			
//			String dupNo = "0";
			//---
			Map<Integer, List<String>> catMap = new HashMap<Integer, List<String>>();
			int idList_size = idList.size();
			for(int i_idx=0; i_idx<idList_size; i_idx++){
				String mainId = mainidList.get(i_idx);
				String custId = idList.get(i_idx);
//				String dupNo = dupnoList.get(i_idx);
				String dupNo ="0";
				
				
				if(!isNaturalMan(custId)){
					notNaturalList.add(custId);
					continue;
				}
				C101M01A c101m01a = cls1131Service.findC101M01A(targetBrId, custId, dupNo);
				if(c101m01a!=null){
					custIdExistList.add(custId);
				}else{
					//以下程式, copy CLS1131FormHandler:: sendCust
					List<GenericBean> list = new ArrayList<GenericBean>();
					String newMainId = IDGenerator.getUUID();
					
					for (Class<?> clazz : LMSUtil.C101Class) {
						List<? extends GenericBean> beans = cls1131Service
								.findListByRelationKey(clazz, src_c101m01a.getMainId(), src_c101m01a.getCustId(), src_c101m01a.getDupNo());
						for (GenericBean bean : beans) {
							GenericBean model = cls1131Service.findModelByOid(clazz, null,
									true);
							DataParse.copy(bean, model);
							// 個金徵信借款人主檔,個金信用評等表
							if (clazz == C101M01A.class || clazz == C101S01G.class ||  clazz == C101S01Q.class){
								model.set("ownBrId", targetBrId);
							}
							if (clazz == C101S01G.class){ //房貸
								model.set(Score.column.報表亂碼, IDGenerator.getUUID());
							}
							if (clazz == C101S01Q.class){ //非房貸
								model.set(Score.column.報表亂碼, IDGenerator.getUUID());								
							}	
							
							if (clazz == C101S01A.class){
								C101S01A c101s01a = (C101S01A)model;
//								chg_factor_c101s01a(custId, c101s01a);
								
								//消金房貸3.0 >> 直接把資料整筆複製!!
								C101S01A c101a = c101s01aDao.findByUniqueKey(mainId,custId,"0");
								if(c101a != null){
									DataParse.copy(c101a,c101s01a);
								}
								
							}
							if (clazz == C101S01B.class){
								C101S01B c101s01b = (C101S01B)model;
//								int cat = chg_factor(custId, c101s01b);
//								if(!catMap.containsKey(cat)){
//									catMap.put(cat, new ArrayList<String>());
//								}
//								catMap.get(cat).add(c101s01b.getCustId());
								
								//消金房貸3.0 >> 直接把資料整筆複製!!
								C101S01B c101b = c101s01bDao.findByUniqueKey(mainId,custId,"0");
								if(c101b != null){
									DataParse.copy(c101b,c101s01b);
								}
							}
							if (clazz == C101S01C.class){
								C101S01C c101s01c = (C101S01C)model;
//								chg_factor_c101s01c(custId, c101s01c);
								
								//消金房貸3.0 >> 直接把資料整筆複製!!
								C101S01C c101c = c101s01cDao.findByUniqueKey(mainId,custId,"0");
								if(c101c != null){
									DataParse.copy(c101c,c101s01c);
								}
								//補值!! 本行有擔餘額、本行無擔餘額
								Map<String, Object> map_S = misBaseService.getLNF022_loanBalSByid(custId+dupNo);
								
								String loanBal_S = MapUtils.getString(map_S, "LOANBAL_S", "");
								c101s01c.setLoanBalSByid(loanBal_S.isEmpty() ? null : new BigDecimal(loanBal_S));
//								"Y".equals(YNFlag) ? "■" : "□"
								Map<String, Object> map_N = misBaseService.getLNF022_loanBalNByid(custId+dupNo);
								String loanBal_N = MapUtils.getString(map_N, "LOANBAL_N", "");
								c101s01c.setLoanBalSByid(loanBal_N.isEmpty() ? null : new BigDecimal(loanBal_N));
								
								
							}
							if (clazz == C101S01E.class){
								C101S01E c101s01e = (C101S01E)model;
//								chg_factor_c101s01c(custId, c101s01c);
								C101S01E c101e = c101s01eDao.findByUniqueKey(mainId,custId,"0");
								if(c101e != null){
									DataParse.copy(c101e,c101s01e);
								}
							}
							
							model.set(EloanConstants.OID, null);
							model.set(EloanConstants.MAIN_ID, newMainId);
							model.set("custId", custId);
							model.set("dupNo", dupNo);
							model.set("custName", "批次"+custId);
							list.add(model);
						}
					}
					// 儲存
					cls1131Service.save(list);
				}
			}
			//---		
			result.put("notNaturalList", notNaturalList);
			result.put("total_idListSize", idList.size());
			result.put("custIdExistList", custIdExistList);			
		}		
		
		return result;
	} 
	
	private boolean isNaturalMan(String custId){
		//可能ID會是 **********
		return LMSUtil.check2(custId);
	}
	
	private int chg_factor(String custId, C101S01B c101s01b){ //XXX 變更
		
//		int ascii_area = custId.charAt(0);
//		String gender = StringUtils.substring(custId, 1, 2);
		//------------------
		/*
		A~Z: 65~90
		a~z: 97~122
		0:48
		1:49
		9:57
		*/
//		int cat = 0;
//		int v_ascii = ascii_area%2;
//		if(65<=ascii_area && ascii_area<=67){
//			cat = 4;
//		}else{			
//			if(Util.equals(gender, "1")){//男
//				cat = ( v_ascii==0?3:1);
//			}else{
//				cat = ( v_ascii==0?2:0);
//			}
//		}
		//消金房貸3.0
//		String custId2 = StringUtils.substring(custId, 2, 8);
//		String custId3 = StringUtils.substring(custId, 8, 10);
//		System.out.println("custId2 = " + custId2);
//		int ascii_area = Integer.parseInt(custId2)*33 + Integer.parseInt(custId3);
//		int cat = ascii_area%9;
//		
//		if(cat==0){
//			c101s01b.setJobTitle("g");
//		}else if(cat==1){
//			c101s01b.setJobTitle("d");
//		}else if(cat==2){
//			c101s01b.setJobTitle("h");
//		}else if(cat==3){
//			c101s01b.setJobTitle("i");
//		}else if(cat==4){
//			c101s01b.setJobTitle("b");
//		}else if(cat==5){
//			c101s01b.setJobTitle("f");
//		}else if(cat==6){
//			c101s01b.setJobTitle("c");
//		}else if(cat==7){
//			c101s01b.setJobTitle(null);
//		}else if(cat==8){
//			c101s01b.setJobTitle("a");
//		}
		
		//消金房貸2.1 (年資SENIORITY、年薪PAYAMT、其他收入OTHAMT、職業別JOBTITLE)
		List<C101S01B> c101bList = c101s01bDao.findByCustIdDupId(custId,"0");
		if(c101bList != null && c101bList.size()>0){
			//不管  抓第一筆就好
			C101S01B c101b = c101bList.get(0);
			c101s01b.setSeniority(c101b.getSeniority());
			c101s01b.setPayAmt(c101b.getPayAmt());
			c101s01b.setOthAmt(c101b.getOthAmt());
			c101s01b.setJobTitle(c101b.getJobTitle());
		}
		
		return 0;
	}
	
	private void chg_factor_c101s01c(String custId, C101S01C c101s01c) throws CapException{
		
//		int ascii_area = custId.charAt(8);
//		String gender = StringUtils.substring(custId, 1, 2);
//		int v_ascii = ascii_area%9;
		//------------------
		/*
		A~Z: 65~90
		a~z: 97~122
		0:48
		1:49
		9:57
		*/
//		int cat = 0;
//		if(Util.equals(gender, "1")){//男
//			cat = ( v_ascii==0?1:2);
//		}else{
//			cat = ( v_ascii==0?3:4);
//		}
		
		//消金房貸2.1(夫妻年收入YFAMAMT)
		List<C101S01C> c101cList = c101s01cDao.findByCustIdDupId(custId,"0");
		if(c101cList != null && c101cList.size()>0){
			C101S01C c101c = c101cList.get(0);
			c101s01c.setYFamAmt(c101c.getYFamAmt());
			c101s01c.setDRate(c101c.getDRate());
			c101s01c.setYRate(c101c.getYRate());
			c101s01c.setFRate(c101c.getFRate());
		}
		
		
	}
	
private void chg_factor_c101s01a(String custId, C101S01A c101s01a){
		
//		int ascii_area = custId.charAt(9);
		String gender = StringUtils.substring(custId, 2, 10);
		int ascii_area = Integer.parseInt(gender);
		System.out.println("ascii_area = " + ascii_area%8);
//		int v_ascii = ascii_area%2;
		//------------------
		/*
		A~Z: 65~90
		a~z: 97~122
		0:48
		1:49
		9:57
		*/
//		if(Util.equals(gender, "1")){//男
//			cat = ( v_ascii==0?1:2);
//		}else{
//			cat = ( v_ascii==0?3:4);
//		}
		//消金房貸2.1
		
		
	}

	/**
	 * 【appraise】
	 * @return
	 * @throws Exception
	 */
	private JSONObject step2() throws Exception{
		String c101s01G_varVer = scoreService.get_Version_HouseLoan();
		String c101s01q_varVer = scoreService.get_Version_NotHouseLoan();
		String c101s01R_varVer = scoreService.get_Version_CardLoan();
		//==========
		JSONObject result = new JSONObject();
//		List<String> idList = readTargetId();
		
		HashMap<String, List<String>> ExcelMap = readTargetId_v2();
		
		List<String> mainidList = ExcelMap.get("mainid");
		List<String> idList = ExcelMap.get("id");
		List<String> dupnoList = ExcelMap.get("dupno");
		
		
//		String dupNo = "0";
		int idList_size = idList.size();
		
		List<String> fail_list = new ArrayList<String>();
		for(int i_idx=0; i_idx<idList_size; i_idx++){
//		for(String custId : idList){
			String custId = idList.get(i_idx);
//			String dupNo = dupnoList.get(i_idx);
			String dupNo = "0";
			C101M01A c101m01a = cls1131Service.findC101M01A(targetBrId, custId,  dupNo);
			if(c101m01a==null){
				continue;
			}			
			List<GenericBean> list = my_appraise( c101m01a, c101s01G_varVer, c101s01q_varVer, c101s01R_varVer);
			
			
			
			
			try{
				cls1131Service.save(c101m01a.getMainId(), custId, dupNo, list, C101S01H.class,
					C101S01I.class);
			}catch(Exception e){
				fail_list.add("c101m01a.mainId="+c101m01a.getMainId()+" "+custId+"-"+dupNo);
				LOGGER.error(StrUtils.getStackTrace(e));
			}
		}
		result.put("success", true);
		result.put("fail_list", fail_list);
		return result;
	}
	
	/**
	 * 【upload DW】
	 * @return
	 * @throws CapException
	 */
	private JSONObject step3()throws CapException{
		JSONObject result = new JSONObject();
		ISearch search = clsService.getMetaSearch();
		if(true){
//			search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "ownBrId", targetBrId);
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", targetBrId);
//			search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "grdCDate", "2023-07-04");			
//			search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "custId", "K297777898");
			search.setFirstResult(0).setMaxResults(Integer.MAX_VALUE);
			//==========	
			if(Util.equals("G", proc_model)){
				search.addSearchModeParameters(SearchMode.EQUALS, "varVer", scoreService.get_Version_HouseLoan());

				for (C101S01G model : c101s01gDao.find(search)){
					if(isNaturalMan(model.getCustId())){
						_upDW_C101(model, null, null, null, null, null);			
					}
				}
			}else if(Util.equals("Q", proc_model)){
				search.addSearchModeParameters(SearchMode.EQUALS, "varVer", scoreService.get_Version_NotHouseLoan());
				
				for (C101S01Q model : c101s01qDao.find(search)){
					if(isNaturalMan(model.getCustId())){
						_upDW_C101(null, model, null, null, null, null);			
					}
				}
			}else if(Util.equals("R", proc_model)){
				search.addSearchModeParameters(SearchMode.EQUALS, "varVer", scoreService.get_Version_CardLoan());
				
				for (C101S01R model : c101s01rDao.find(search)){
					if(isNaturalMan(model.getCustId())){
						_upDW_C101(null, null, model, null, null, null);			
					}
				}
			}
			
			//是否執行雙軌
			boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
			if(scoreDoubleTrack){
				ISearch search_n = clsService.getMetaSearch();
				search_n.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", targetBrId);
//				search_n.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
				search_n.setFirstResult(0).setMaxResults(Integer.MAX_VALUE);
				
				if(Util.equals("G", proc_model)){
					search_n.addSearchModeParameters(SearchMode.EQUALS, "varVer", ClsScoreUtil.V3_0_HOUSE_LOAN);
					for (C101S01G_N model : c101s01g_nDao.find(search_n)){
						if(LMSUtil.check2(model.getCustId())){
							_upDW_C101(null, null, null, model, null, null);			
						}
					}
				}else if(Util.equals("Q", proc_model)){
					search_n.addSearchModeParameters(SearchMode.EQUALS, "varVer", ClsScoreUtil.V4_0_NOT_HOUSE_LOAN);
					
					for (C101S01Q_N model : c101s01q_nDao.find(search_n)){
						if(LMSUtil.check2(model.getCustId())){
							_upDW_C101(null, null, null, null, model, null);			
						}
					}
				}else if(Util.equals("R", proc_model)){
					search_n.addSearchModeParameters(SearchMode.EQUALS, "varVer", ClsScoreUtil.V4_0_CARD_LOAN);
					
					for (C101S01R_N model : c101s01r_nDao.find(search_n)){
						if(LMSUtil.check2(model.getCustId())){
							_upDW_C101(null, null, null, null, null, model);			
						}
					}
				}
			}
			
		}
		result.put("success", true);
		return result;
	}
	
	private void singleL()throws CapException{
		if(Util.equals("G", proc_model)){
			String c101s01G_oid = "08E3D0E0485711E4B2DD50640A70FE60";
			String c120s01G_oid = "";
			if(Util.isNotEmpty(c101s01G_oid)){
				C101S01G model_G = c101s01gDao.findByOid(c101s01G_oid);
//				_upDW_C101(model_G, null, null);
			}
			
			if(Util.isNotEmpty(c120s01G_oid)){
				C120S01G model_G = c120s01gDao.findByOid(c120s01G_oid);
				_upDW_C120(model_G, null, null);
			}		
		}else if(Util.equals("Q", proc_model)){
			String c101s01q_oid = "2D918F97B62B11E380E477D20A70FE61";
			String c120s01q_oid = "";//"A765E84399D04D6DA2E2A6FCA713625F";
			if(Util.isNotEmpty(c101s01q_oid)){
				C101S01Q model_q = c101s01qDao.findByOid(c101s01q_oid);
//				_upDW_C101(null, model_q, null);	
			}
			
			if(Util.isNotEmpty(c120s01q_oid)){
				C120S01Q model_q = c120s01qDao.findByOid(c120s01q_oid);
				_upDW_C120(null, model_q, null);
			}
		}else if(Util.equals("R", proc_model)){
			String c101s01r_oid = "2A64A2017ACD11E4A418C51DC0A83B85";
			String c120s01r_oid = "";//"A765E84399D04D6DA2E2A6FCA713625F";
			if(Util.isNotEmpty(c101s01r_oid)){
				C101S01R model_r = c101s01rDao.findByOid(c101s01r_oid);
//				_upDW_C101(null, null, model_r);	
			}
			
			if(Util.isNotEmpty(c120s01r_oid)){
				C120S01R model_r = c120s01rDao.findByOid(c120s01r_oid);
				_upDW_C120(null, null, model_r);
			}
		}		
	}
	
	private void _upDW_C101(C101S01G model_G, C101S01Q model_Q, C101S01R model_R, C101S01G_N model_NG, C101S01Q_N model_NQ, C101S01R_N model_NR) throws CapException{
		C120M01A c120m01a = null;
		C120S01A c120s01a = null;
		C120S01B c120s01b = null;
		C120S01C c120s01c = null;
		C120S01E c120s01e = null;
		C120S01G c120s01G = null;		
		C120S01Q c120s01Q = null;
		C120S01R c120s01R = null;	
		if(true){				
			String mainId = "";
			String custId = "";
			String dupNo = "";
			if(true){
				if(model_G!=null){
					mainId = model_G.getMainId();
					custId = model_G.getCustId();
					dupNo = model_G.getDupNo();
					c120s01G = LMSUtil.copy_to_C120S01G(model_G);
				}else if(model_Q!=null){
					mainId = model_Q.getMainId();
					custId = model_Q.getCustId();
					dupNo = model_Q.getDupNo();
					c120s01Q = LMSUtil.copy_to_C120S01Q(model_Q);
				}else if(model_R!=null){
					mainId = model_R.getMainId();
					custId = model_R.getCustId();
					dupNo = model_R.getDupNo();
					c120s01R = LMSUtil.copy_to_C120S01R(model_R);
				}else if(model_NG!=null){
					mainId = model_NG.getMainId();
					custId = model_NG.getCustId();
					dupNo = model_NG.getDupNo();
				}else if(model_NQ!=null){
					mainId = model_NQ.getMainId();
					custId = model_NQ.getCustId();
					dupNo = model_NQ.getDupNo();
				}else if(model_NR!=null){
					mainId = model_NR.getMainId();
					custId = model_NR.getCustId();
					dupNo = model_NR.getDupNo();
				}else{
					return;
				}
			}
			if(true){
				C101M01A c101m01a = c101m01aDao.findByUniqueKey(mainId, null, custId, dupNo);
				if(true){	
					c120m01a = new C120M01A();
					c120m01a.setCustId(c101m01a.getCustId());
					c120m01a.setDupNo(c101m01a.getDupNo());
					c120m01a.setModelTyp("");// 房貸模型上線前舊案(若為 Y 時，表示只有 房貸模型，非房貸模型還不能用)
					//=============
					c120m01a.setPrimary_card(c101m01a.getPrimary_card()); 
					c120m01a.setAdditional_card(c101m01a.getAdditional_card()); 
					c120m01a.setBusiness_or_p_card(c101m01a.getBusiness_or_p_card());
				}
			}
			c120s01a = LMSUtil.copy_to_C120S01A(c101s01aDao.findByUniqueKey(mainId, custId, dupNo));
			c120s01b = LMSUtil.copy_to_C120S01B(c101s01bDao.findByUniqueKey(mainId, custId, dupNo));
			c120s01c = LMSUtil.copy_to_C120S01C(c101s01cDao.findByUniqueKey(mainId, custId, dupNo));
			c120s01e = LMSUtil.copy_to_C120S01E(c101s01eDao.findByUniqueKey(mainId, custId, dupNo));
			//copy from c101 to c120
			if(model_G!=null){
				clsService.verify_GradeModel_upDW(c120m01a, c120s01a, c120s01b, c120s01c, c120s01G, null, null, c120s01e);
			}
			if(model_Q!=null){
				clsService.verify_GradeModel_upDW(c120m01a, c120s01a, c120s01b, c120s01c, null, c120s01Q, null, c120s01e);
			}			
			if(model_R!=null){
				clsService.verify_GradeModel_upDW(c120m01a, c120s01a, c120s01b, c120s01c, null, null, c120s01R, c120s01e);
			}
			
			if(model_NG!=null){
				clsService.verify_GradeModel_U_upDW(c120m01a, c120s01a, c120s01b, c120s01c, model_NG, null, null, c120s01e);
			}
			if(model_NQ!=null){
				clsService.verify_GradeModel_U_upDW(c120m01a, c120s01a, c120s01b, c120s01c, null, model_NQ, null, c120s01e);
			}
			if(model_NR!=null){
				clsService.verify_GradeModel_U_upDW(c120m01a, c120s01a, c120s01b, c120s01c, null, null, model_NR, c120s01e);
			}
		}		
	}
	
	private void _upDW_C120(C120S01G model_G, C120S01Q model_Q, C120S01R model_R) throws CapException{		
		if(model_G!=null){
			C120M01A c120m01a = c120m01aDao.findByUniqueKey(model_G.getMainId(), model_G.getCustId(), model_G.getDupNo());
			C120S01A c120s01a = c120s01aDao.findByUniqueKey(model_G.getMainId(), model_G.getCustId(), model_G.getDupNo());
			C120S01B c120s01b = c120s01bDao.findByUniqueKey(model_G.getMainId(), model_G.getCustId(), model_G.getDupNo()); 
			C120S01C c120s01c = c120s01cDao.findByUniqueKey(model_G.getMainId(), model_G.getCustId(), model_G.getDupNo());
			C120S01E c120s01e = c120s01eDao.findByUniqueKey(model_G.getMainId(), model_G.getCustId(), model_G.getDupNo());
			//~~~
			clsService.verify_GradeModel_upDW(c120m01a, c120s01a, c120s01b, c120s01c, model_G, null, null, c120s01e);
		}else if(model_Q!=null){
			C120M01A c120m01a = c120m01aDao.findByUniqueKey(model_Q.getMainId(), model_Q.getCustId(), model_Q.getDupNo());
			C120S01A c120s01a = c120s01aDao.findByUniqueKey(model_Q.getMainId(), model_Q.getCustId(), model_Q.getDupNo());
			C120S01B c120s01b = c120s01bDao.findByUniqueKey(model_Q.getMainId(), model_Q.getCustId(), model_Q.getDupNo()); 
			C120S01C c120s01c = c120s01cDao.findByUniqueKey(model_Q.getMainId(), model_Q.getCustId(), model_Q.getDupNo());
			C120S01E c120s01e = c120s01eDao.findByUniqueKey(model_Q.getMainId(), model_Q.getCustId(), model_Q.getDupNo());
			//~~~
			clsService.verify_GradeModel_upDW(c120m01a, c120s01a, c120s01b, c120s01c, null, model_Q, null, c120s01e);
		}else if(model_R!=null){
			C120M01A c120m01a = c120m01aDao.findByUniqueKey(model_R.getMainId(), model_R.getCustId(), model_R.getDupNo());
			C120S01A c120s01a = c120s01aDao.findByUniqueKey(model_R.getMainId(), model_R.getCustId(), model_R.getDupNo());
			C120S01B c120s01b = c120s01bDao.findByUniqueKey(model_R.getMainId(), model_R.getCustId(), model_R.getDupNo()); 
			C120S01C c120s01c = c120s01cDao.findByUniqueKey(model_R.getMainId(), model_R.getCustId(), model_R.getDupNo());
			C120S01E c120s01e = c120s01eDao.findByUniqueKey(model_R.getMainId(), model_R.getCustId(), model_R.getDupNo());
			//~~~
			clsService.verify_GradeModel_upDW(c120m01a, c120s01a, c120s01b, c120s01c, null, null, model_R, c120s01e);
		}
	}
	
	private List<GenericBean> my_appraise(C101M01A c101m01a
			, String c101s01G_varVer, String c101s01q_varVer, String c101s01r_varVer) throws Exception{
		List<GenericBean> r = new ArrayList<GenericBean>();
		//---
			JSONObject fetch_score_src = addBasicData(c101m01a);
			fetch_score_src = this.init_fetch_score_src(fetch_score_src, c101s01G_varVer, c101s01q_varVer, c101s01r_varVer);
			
			// 取得聯徵和票信資料
			fetch_score_src.putAll(scoreService.getData(c101m01a.getCustId(), c101m01a.getDupNo(), c101s01G_varVer, c101s01q_varVer, c101s01r_varVer, fetch_score_src));
			//===
			boolean del_GradeModel = Util.isEmpty(fetch_score_src.get(Score.column.聯徵資料日期))
				|| !LMSUtil.check2(c101m01a.getCustId());
			boolean delR = false;
			
			if (Util.isNotEmpty(fetch_score_src.get(Score.column.聯徵資料日期))) {
				String prodId = Util.trim(fetch_score_src.get(ClsScoreUtil.PRODID_KEY));
				//===
				JSONObject score_houseLoan = new JSONObject();
				if(true){//房貸
					score_houseLoan.putAll(fetch_score_src);
					score_houseLoan.putAll(scoreService.score(Score.type.基本, fetch_score_src, c101s01G_varVer));
				}
				JSONObject score_NotHouseLoan = new JSONObject();
				if(true){//非房貸
					score_NotHouseLoan.putAll(fetch_score_src);
					score_NotHouseLoan.putAll(scoreService.scoreNotHouseLoan(ScoreNotHouseLoan.type.非房貸基本, fetch_score_src, c101s01q_varVer));
				}
				JSONObject score_cardLoan = new JSONObject();
				if(true){//卡友貸
					score_cardLoan.putAll(fetch_score_src);
					score_cardLoan.putAll(scoreService.scoreCardLoan(ScoreCardLoan.type.卡友貸基本, fetch_score_src, c101s01r_varVer));
				}
				
				c101m01a.set("importFlag", UtilConstants.DEFAULT.是); // 註記已評等可引入
				//在 loop 的順序,先處理 C101M01A, 再處理 C101S01G,C101S01Q
				//score_houseLoan.put(Score.column.引用_房貸, ClsUtil.fetchQuoteValue(ClsUtil.isQuote(c101m01a, UtilConstants.L140S02AModelKind.房貸)));
				//score_NotHouseLoan.put(ScoreNotHouseLoan.column.引用_非房貸, ClsUtil.fetchQuoteValue(ClsUtil.isQuote(c101m01a, UtilConstants.L140S02AModelKind.非房貸)));
				if(true){
					//Batch 特定
					boolean use_g = false;
					boolean use_q = false;
					boolean use_r = false;
					if(Util.isNotEmpty(score_houseLoan.get(Score.column.最終評等))){
						score_houseLoan.put(Score.column.引用_房貸, "Y");
						use_g = true;
					}
					if(Util.isNotEmpty(score_NotHouseLoan.get(ScoreNotHouseLoan.column.最終評等))){
						score_NotHouseLoan.put(ScoreNotHouseLoan.column.引用_非房貸, "Y");
						use_q = true;
					}
					if(Util.isNotEmpty(score_cardLoan.get(ScoreCardLoan.column.最終評等))){
						score_cardLoan.put(ScoreCardLoan.column.引用_卡友貸, "Y");
						use_r = true;
					}
					if(true){
						List<String> list = new ArrayList<String>();
						if(use_g){
							list.add("1");
						}
						if(use_q){
							list.add("2");
						}
						if(use_r){
							list.add("3");
						}
						
						String markModel = "0";
						if(list.size()>0){
							markModel = StringUtils.join(list, "|");
						}
						c101m01a.setMarkModel(markModel);
					}
				}
				
				//---
				C101S01E c101s01e = cls1131Service.findModelByKey(C101S01E.class, c101m01a.getMainId(),
						c101m01a.getCustId(), c101m01a.getDupNo(), true);
				// 聯徵票信 資料&查詢日期
				JSONObject set = new JSONObject();
				set.put(ClsConstants.C101S01E.查詢組合, prodId);
				set.put(ClsConstants.C101S01E.聯徵資料日期,
						Util.trim(score_houseLoan.get(Score.column.聯徵資料日期)));
				set.put(ClsConstants.C101S01E.聯徵查詢日期,
						Util.trim(score_houseLoan.get(Score.column.聯徵查詢日期)));
				set.put(ClsConstants.C101S01E.票信資料截止日,
						Util.trim(score_houseLoan.get(Score.column.票信資料日期)));
				set.put(ClsConstants.C101S01E.票信查詢日期,
						Util.trim(score_houseLoan.get(Score.column.票信查詢日期)));
				DataParse.toBean(set, c101s01e);
				//---
				C101S01G c101s01g = cls1131Service.findModelByKey(C101S01G.class, c101m01a.getMainId(),
						c101m01a.getCustId(), c101m01a.getDupNo(), true);
				if(true){
					DataParse.toBean(score_houseLoan, c101s01g);
					scoreService.clear_unUsedColumn(c101s01g);
				}
				//---
				C101S01Q c101s01q = cls1131Service.findModelByKey(C101S01Q.class, c101m01a.getMainId(),
						c101m01a.getCustId(), c101m01a.getDupNo(), true);
				if(true){
					DataParse.toBean(score_NotHouseLoan, c101s01q);
					scoreService.clear_unUsedColumn(c101s01q);
				}					
				//---
				C101S01R c101s01r = null;
				if(true){
					if(Util.isNotEmpty(score_cardLoan.get(ScoreCardLoan.column.最終評等))){
						c101s01r = cls1131Service.findModelByKey(C101S01R.class, c101m01a.getMainId(),
								c101m01a.getCustId(), c101m01a.getDupNo(), true);
						if(true){
							DataParse.toBean(score_cardLoan, c101s01r);
							scoreService.clear_unUsedColumn(c101s01r);
						}		
					}else{
						delR = true;
					}
				}
									
				//---
				r.add(c101m01a);
				r.add(c101s01e);
				r.add(c101s01g);
				r.add(c101s01q);
				if(!delR){
					r.add(c101s01r);	
				}
				
				//雙軌處裡
				boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
				if(scoreDoubleTrack){
					JSONObject fetch_score_src_N = addBasicData(c101m01a);
					fetch_score_src_N = this.init_fetch_score_src(fetch_score_src_N, ClsScoreUtil.V3_0_HOUSE_LOAN, ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, ClsScoreUtil.V4_0_CARD_LOAN);
					// 取得聯徵和票信資料
					fetch_score_src_N.putAll(scoreService.getData(c101m01a.getCustId(), c101m01a.getDupNo(), 
							ClsScoreUtil.V3_0_HOUSE_LOAN, ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, ClsScoreUtil.V4_0_CARD_LOAN, fetch_score_src_N));
					
					JSONObject score_houseLoan_3_0 = new JSONObject();
					if (true) {// 房貸3.0
						score_houseLoan_3_0.putAll(fetch_score_src_N);
						score_houseLoan_3_0.putAll(scoreService.score(Score.type.基本,
								fetch_score_src_N, ClsScoreUtil.V3_0_HOUSE_LOAN));
					}
					JSONObject score_NotHouseLoan_4_0 = new JSONObject();
					if (true) {// 非房貸4.0
						score_NotHouseLoan_4_0.putAll(fetch_score_src_N);
						// 計算 score、評等、DR
						score_NotHouseLoan_4_0.putAll(scoreService.scoreNotHouseLoan(
								ScoreNotHouseLoan.type.非房貸基本, fetch_score_src_N,
								ClsScoreUtil.V4_0_NOT_HOUSE_LOAN));
					}
					JSONObject score_cardLoan_4_0 = new JSONObject();
					if (true) {// 卡友貸4.0
						score_cardLoan_4_0.putAll(fetch_score_src_N);
						// 計算 score、評等、DR
						score_cardLoan_4_0.putAll(scoreService.scoreCardLoan(
								ScoreCardLoan.type.卡友貸基本, fetch_score_src_N,
								ClsScoreUtil.V4_0_CARD_LOAN));
					}
					
					
					C101S01G_N c101s01g_n = cls1131Service.findModelByKey(C101S01G_N.class, c101m01a.getMainId(),
							c101m01a.getCustId(), c101m01a.getDupNo(), true);
					if(true){
						DataParse.toBean(score_houseLoan_3_0, c101s01g_n);
//						scoreService.clear_unUsedColumn(c101s01g_n);
					}
					//---
					C101S01Q_N c101s01q_n = cls1131Service.findModelByKey(C101S01Q_N.class, c101m01a.getMainId(),
							c101m01a.getCustId(), c101m01a.getDupNo(), true);
					if(true){
						DataParse.toBean(score_NotHouseLoan_4_0, c101s01q_n);
//						scoreService.clear_unUsedColumn(c101s01q);
					}		
					
					boolean delR_n = false;
					C101S01R_N c101s01r_n = null;
					if(true){
						if(Util.isNotEmpty(score_cardLoan_4_0.get(ScoreCardLoan.column.最終評等))){
							c101s01r_n = cls1131Service.findModelByKey(C101S01R_N.class, c101m01a.getMainId(),
									c101m01a.getCustId(), c101m01a.getDupNo(), true);
							if(true){
								DataParse.toBean(score_cardLoan_4_0, c101s01r_n);
//								scoreService.clear_unUsedColumn(c101s01r);
							}		
						}else{
							delR_n = true;
						}
					}
					r.add(c101s01g_n);
					r.add(c101s01q_n);
					if(!delR){
						r.add(c101s01r_n);	
					}
				}
				
				// 取得聯徵和票信信查詢結果(HTML)
				r.addAll(cls1131Service.getHtml(c101m01a.getMainId(), c101m01a.getCustId(), c101m01a.getDupNo(), prodId));
			}
			
			if (del_GradeModel) {
				// 在 JCIC 查無資料, 把C101S01G, C101S01Q, C101S01R清掉
				List<GenericBean> delList = new ArrayList<GenericBean>();
				if(true){
					C101S01G c101s01g = cls1131Service.findModelByKey(C101S01G.class,
							c101m01a.getMainId(), c101m01a.getCustId() , c101m01a.getDupNo());
					if (c101s01g != null) {
						delList.add(c101s01g);
					}
				}
				if(true){
					C101S01Q c101s01q = cls1131Service.findModelByKey(C101S01Q.class,
							c101m01a.getMainId(), c101m01a.getCustId() , c101m01a.getDupNo());
					if (c101s01q != null) {
						delList.add(c101s01q);
					}
				}
				if(true){
					C101S01R c101s01r = cls1131Service.findModelByKey(C101S01R.class,
							c101m01a.getMainId(), c101m01a.getCustId() , c101m01a.getDupNo());
					if (c101s01r != null) {
						delList.add(c101s01r);
					}
				}
				cls1131Service.delete(delList);
			}
			if(delR && !del_GradeModel){ //若之前已進到 del_GradeModel, 就把 C101S01R 刪掉了
				List<GenericBean> delList = new ArrayList<GenericBean>();
				if(true){
					C101S01R c101s01r = cls1131Service.findModelByKey(C101S01R.class,
							c101m01a.getMainId(), c101m01a.getCustId() , c101m01a.getDupNo());
					if (c101s01r != null) {
						delList.add(c101s01r);
					}
				}
				cls1131Service.delete(delList);
			}
		//---		
		return r;
	}
	
	private JSONObject init_fetch_score_src(JSONObject fetch_score_src, String c101s01G_varVer, String c101s01q_varVer, String c101s01r_varVer){
		if (Util.equals(ClsScoreUtil.V1_3_HOUSE_LOAN, c101s01G_varVer)) { //房貸
			
		} else if (Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN, c101s01G_varVer) || Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN, c101s01G_varVer)) {
			fetch_score_src.put(Score.column.個人年所得, ClsUtil.get_pIncome_from_uiC101S01BForm(fetch_score_src));
		}
		
		if(Util.equals(ClsScoreUtil.V1_0_NOT_HOUSE_LOAN, c101s01q_varVer)){//非房貸
			Map<String, String> m = new HashMap<String, String>();
			
			m.putAll(ClsUtil.map_c101s01q_c101s01a_V1_0());

			m.putAll(ClsUtil.map_c101s01q_c101s01b_V1_0());

			for(String q_column :m.keySet()){
				String column_s01 = m.get(q_column);
				fetch_score_src.put(q_column, fetch_score_src.get(column_s01));	
			}
		}else if(Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN, c101s01q_varVer)){
			ClsUtil.set_c101s01q_factor_V2_0(fetch_score_src, fetch_score_src);
		}else if(Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN, c101s01q_varVer)){
			ClsUtil.set_c101s01q_factor_V2_1(fetch_score_src, fetch_score_src);
		}else if(Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN, c101s01q_varVer) 
				|| Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN, c101s01q_varVer)){
			ClsScoreUtil.set_c101s01q_factor_V3_0_c101s01b(fetch_score_src, fetch_score_src);
			ClsScoreUtil.set_c101s01q_factor_V3_0_c101s01c(fetch_score_src, fetch_score_src);
		}else if(Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, c101s01q_varVer)){
			ClsScoreUtil.set_c101s01q_factor_V4_0_c101s01a(fetch_score_src, fetch_score_src);
			ClsScoreUtil.set_c101s01q_factor_V4_0_c101s01c(fetch_score_src, fetch_score_src);
		}
		
		if(Util.equals(ClsScoreUtil.V2_1_CARD_LOAN, c101s01r_varVer)){
			ClsUtil.set_c101s01r_factor_V2_1(fetch_score_src, fetch_score_src);
		}else if(Util.equals(ClsScoreUtil.V3_0_CARD_LOAN, c101s01r_varVer) || Util.equals(ClsScoreUtil.V3_1_CARD_LOAN, c101s01r_varVer)){
			ClsScoreUtil.set_c101s01r_factor_V3_0_c101s01b(fetch_score_src, fetch_score_src);
			ClsScoreUtil.set_c101s01r_factor_V3_0_c101s01c(fetch_score_src, fetch_score_src);
		}else if(Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, c101s01q_varVer)){
			ClsScoreUtil.set_c101s01r_factor_V4_0_c101s01a(fetch_score_src, fetch_score_src);
			ClsScoreUtil.set_c101s01r_factor_V4_0_c101s01c(fetch_score_src, fetch_score_src);
		}
		return fetch_score_src;
	}
	
	
	
	private JSONObject addBasicData(C101M01A c101m01a) throws Exception{		
		C101S01A c101s01a = c101s01aDao.findByUniqueKey(c101m01a.getMainId(), c101m01a.getCustId(), c101m01a.getDupNo());
		C101S01B c101s01b = c101s01bDao.findByUniqueKey(c101m01a.getMainId(), c101m01a.getCustId(), c101m01a.getDupNo());
		C101S01C c101s01c = c101s01cDao.findByUniqueKey(c101m01a.getMainId(), c101m01a.getCustId(), c101m01a.getDupNo());
		C101S01E c101s01e = c101s01eDao.findByUniqueKey(c101m01a.getMainId(), c101m01a.getCustId(), c101m01a.getDupNo());
		
		JSONObject fetch_score_src = new JSONObject();
		fetch_score_src.putAll(DataParse.toJSON(c101m01a));
		if(c101s01a!=null){
			fetch_score_src.putAll(DataParse.toJSON(c101s01a));	
		}
		if(c101s01b!=null){
			fetch_score_src.putAll(DataParse.toJSON(c101s01b));			
		}
		if(c101s01c!=null){
			fetch_score_src.putAll(DataParse.toJSON(c101s01c));
		}
		if(c101s01e!=null){
			fetch_score_src.putAll(DataParse.toJSON(c101s01e));
		}
		
		return fetch_score_src;
	}
	
	private List<String> readTargetId() {
		if(readTargetId_src_type==1){
			return readTargetId_with_define_data();
		}else if(readTargetId_src_type==9){
			return readTargetIdFromExcel(path); 
//			D:/Work/e-Loan/J-111-0271_消金房貸評等3.0/EJCIC_ID_TEST.xls
		}
		return new ArrayList<String>();
	}
	
	private HashMap<String, List<String>> readTargetId_v2() {
		if(readTargetId_src_type==1){
//			return readTargetId_with_define_data();
		}else if(readTargetId_src_type==9){
			return readTargetIdFromExcel_v2(path); 
//			D:/Work/e-Loan/J-111-0271_消金房貸評等3.0/EJCIC_ID_TEST.xls
		}
		return new HashMap<String, List<String>>();
	}
	
	
	private List<String> readTargetId_with_define_data() {
		ArrayList<String> result = new ArrayList<String>();
		//~~~~~~~~~~~~~
//		result.add("A113009385");
		result.add("X101010107");
//		result.add("X202020207");
//		result.add("Y101010108");
//		result.add("Y202020208");
		//~~~~~~~~~~~~~
		return result;
	}
	private List<String> readTargetIdFromExcel(String path) {
		ArrayList<String> result = new ArrayList<String>();
		int max_id_cnt = 5000;
		//XXX 調整 max_id_cnt = 2;
		InputStream is = null;
		Workbook rwb = null;
		Sheet sheet = null;
		try {

			if (Util.isEmpty(path)){
				path = "cls/EJCIC_ID.xls";
//				path = "cls/EJCIC_ID_TEST.xls";
			}
			is = Thread.currentThread().getContextClassLoader()
					.getResourceAsStream(path);

			if (is != null) {
				rwb = Workbook.getWorkbook(is);
				sheet = rwb.getSheet(0); // 取得資料表

				int rsRows = sheet.getRows() - 1; // 獲取Sheet表中所包含的總行數
				int fetchMax = Math.min(rsRows, max_id_cnt);
				for (int y = 1; y <= fetchMax; y++) {
					Cell cell = sheet.getCell(0, y); 
					String id = Util.trim(cell.getContents());
					if (Util.isNotEmpty(id) && !result.contains(id)) {
						result.add(id);
					}
					
				}
			} else {
				LOGGER.info("excel is not found!");
			}

		} catch (FileNotFoundException e) {
			LOGGER.error("FileNotFoundException:" + e.toString());
			LOGGER.error(e.getMessage());
		} catch (BiffException e) {
			LOGGER.error("BiffException:" + e.toString());
			LOGGER.error(e.getMessage());
		} catch (IOException e) {
			LOGGER.error("IOException:" + e.toString());
			LOGGER.error(e.getMessage());
		} finally {
			sheet = null;
			if (rwb != null) {
				rwb.close();
				rwb = null;
			}
			try {
				if (is != null) {
					is.close();
					is = null;
				}
			} catch (IOException e) {
				LOGGER.error("IOException:" + e.toString());
				LOGGER.error(e.getMessage());
			}
		}

		return result;
	}
	
	private HashMap<String, List<String>> readTargetIdFromExcel_v2(String path) {
		ArrayList<String> result_mainid = new ArrayList<String>();
		ArrayList<String> result_id = new ArrayList<String>();
		ArrayList<String> result_dupno = new ArrayList<String>();
		
		HashMap<String, List<String>> result = new HashMap<String, List<String>>();
		int max_id_cnt = 5000;
		//XXX 調整 max_id_cnt = 2;
		InputStream is = null;
		Workbook rwb = null;
		Sheet sheet = null;
		try {

			if (Util.isEmpty(path)){
				path = "cls/EJCIC_ID.xls";
			}
			is = Thread.currentThread().getContextClassLoader()
					.getResourceAsStream(path);

			if (is != null) {
				rwb = Workbook.getWorkbook(is);
				sheet = rwb.getSheet(0); // 取得資料表

				int rsRows = sheet.getRows() - 1; // 獲取Sheet表中所包含的總行數
				int fetchMax = Math.min(rsRows, max_id_cnt);
				for (int y = 1; y <= fetchMax; y++) {
					Cell cell = sheet.getCell(0, y); 
					Cell cell2 = sheet.getCell(1, y); 
					Cell cell3 = sheet.getCell(2, y); 
					String mainid = Util.trim(cell.getContents());
					String id = Util.trim(cell2.getContents());
					String dupno = Util.trim(cell3.getContents());
					if (Util.isNotEmpty(mainid) && !result_mainid.contains(mainid)) {
						result_mainid.add(mainid);
					}
					if (Util.isNotEmpty(id) && !result_id.contains(id)) {
						result_id.add(id);
					}
					if (Util.isNotEmpty(dupno) && !result_dupno.contains(dupno)) {
						result_dupno.add(dupno);
					}
					
				}
				
				result.put("mainid", result_mainid);
				result.put("id", result_id);
				result.put("dupno", result_dupno);
				
			} else {
				LOGGER.info("excel is not found!");
			}

		} catch (FileNotFoundException e) {
			LOGGER.error("FileNotFoundException:" + e.toString());
			LOGGER.error(e.getMessage());
		} catch (BiffException e) {
			LOGGER.error("BiffException:" + e.toString());
			LOGGER.error(e.getMessage());
		} catch (IOException e) {
			LOGGER.error("IOException:" + e.toString());
			LOGGER.error(e.getMessage());
		} finally {
			sheet = null;
			if (rwb != null) {
				rwb.close();
				rwb = null;
			}
			try {
				if (is != null) {
					is.close();
					is = null;
				}
			} catch (IOException e) {
				LOGGER.error("IOException:" + e.toString());
				LOGGER.error(e.getMessage());
			}
		}

		return result;
	}
	
	
}
