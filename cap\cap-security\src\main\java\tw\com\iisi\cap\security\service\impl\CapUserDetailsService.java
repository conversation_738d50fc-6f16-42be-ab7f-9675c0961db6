/*
 * CapUserDetailsService.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.security.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import tw.com.iisi.cap.security.dao.IUserDao;
import tw.com.iisi.cap.security.model.CapUserDetails;
import tw.com.iisi.cap.security.model.IRole;
import tw.com.iisi.cap.security.model.IUser;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * CapUserDetailsService implements UserDetailsService.
 * 讀取使用者資訊
 * </pre>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/9,iristu,new
 *          </ul>
 */
@SuppressWarnings("rawtypes")
public class CapUserDetailsService implements UserDetailsService {

    private static final Log logger = LogFactory.getLog(CapUserDetailsService.class);

    /*
     * 以使用者資料讀取詳細資訊
     * 
     * @see org.springframework.security.core.userdetails.UserDetailsService#loadUserByUsername(java.lang.String)
     */
    @Override
    public UserDetails loadUserByUsername(String username) {
        if (CapString.isEmpty(username)) {
            throw new UsernameNotFoundException("Empty login");
        }

        if (logger.isDebugEnabled()) {
            logger.debug("Security verification for user '" + username + "'");
        }

        IUser user = obtainAccount(username);

        if (user == null) {
            if (logger.isInfoEnabled()) {
                logger.info("Account " + username + " could not be found");
            }
            throw new UsernameNotFoundException("account " + username + " could not be found");
        }

        Map<String, String> roles = obtainRole(user);
        return obtainUserDetails(user, roles);

    }

    /**
     * 獲取使用者資訊
     * 
     * @param user
     * @param roles
     * @return new {@linkplain tw.com.iisi.cap.security.model.CapUserDetails#CapUserDetails(IUser, Map) CapUserDetails}(user, roles)
     */
    public UserDetails obtainUserDetails(IUser user, Map<String, String> roles) {
        return new CapUserDetails(user, roles);
    }

    /**
     * 使用者資料Dao
     */
    private IUserDao userDao;

    /**
     * 設置使用者資料Dao
     * 
     * @param userDao
     */
    public void setUserDao(IUserDao userDao) {
        this.userDao = userDao;
    }

    /**
     * 取得使用者資料Dao
     * 
     * @return
     */
    public IUserDao getUserDao() {
        return userDao;
    }

    /**
     * Return the user depending on the login provided by spring security.
     * 
     * @return the user if found
     */
    protected IUser obtainAccount(String login) {
        return userDao.getUserByLoginId(login, null);
    }

    /**
     * 獲得角色
     * 
     * @param user
     * @return
     */
    @SuppressWarnings("unchecked")
    protected Map<String, String> obtainRole(IUser user) {
        Map<String, String> mRoles = new HashMap<String, String>();
        List<IRole> roles = userDao.getRoleByUser(user);
        if (roles != null) {
            for (int i = 0; i < roles.size(); i++) {
                IRole role = roles.get(i);
                mRoles.put(role.getOid(), role.getRoleName());
            }
        }
        return mRoles;
    }

    /**
     * Returns null. Subclass may override it to provide their own password.
     */
    protected String obtainPassword(String username) {
        return "";
    }

}
