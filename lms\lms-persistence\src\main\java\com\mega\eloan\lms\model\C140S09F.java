package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.RelativeMeta;


/**
 * <pre>
 * C140S09F model.
 * </pre>
 * 
 * @since 2011/10/27
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/27,<PERSON>,new</li>
 *          <li>2013/02/20,EL07623,將年度長度改為30
 *          </ul>
 */
@NamedEntityGraph(name = "C140S09F-entity-graph", attributeNodes = { @NamedAttributeNode("c140m01a") })
@Entity
@Table(name="C140S09F", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C140S09F extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

	//J-105-0080-001 Web e-Loan授信管理系統集團轄下公司名稱欄位放大為38個全形字
	@Column(length=120)
	private String linv11;

	@Column(length=30)
	private String linv110;

	@Column(length=3)
	private String linv111;

	@Column(precision=12)
	private BigDecimal linv112;

	@Column(length=150)
	private String linv12;

	@Column(precision=11, scale=2)
	private BigDecimal linv13;

	@Column(length=3)
	private String linv14;

	@Column(precision=12)
	private BigDecimal linv15;

	@Column(length=3)
	private String linv16;

	@Column(precision=12)
	private BigDecimal linv17;

	@Column(length=3)
	private String linv18;

	@Column(precision=12)
	private BigDecimal linv19;

	//bi-directional many-to-one association to C140M01A
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
        @JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false) })
	private C140M01A c140m01a;

	public String getLinv11() {
		return this.linv11;
	}

	public void setLinv11(String linv11) {
		this.linv11 = linv11;
	}

	public String getLinv110() {
		return this.linv110;
	}

	public void setLinv110(String linv110) {
		this.linv110 = linv110;
	}

	public String getLinv111() {
		return this.linv111;
	}

	public void setLinv111(String linv111) {
		this.linv111 = linv111;
	}

	public BigDecimal getLinv112() {
		return this.linv112;
	}

	public void setLinv112(BigDecimal linv112) {
		this.linv112 = linv112;
	}

	public String getLinv12() {
		return this.linv12;
	}

	public void setLinv12(String linv12) {
		this.linv12 = linv12;
	}

	public BigDecimal getLinv13() {
		return this.linv13;
	}

	public void setLinv13(BigDecimal linv13) {
		this.linv13 = linv13;
	}

	public String getLinv14() {
		return this.linv14;
	}

	public void setLinv14(String linv14) {
		this.linv14 = linv14;
	}

	public BigDecimal getLinv15() {
		return this.linv15;
	}

	public void setLinv15(BigDecimal linv15) {
		this.linv15 = linv15;
	}

	public String getLinv16() {
		return this.linv16;
	}

	public void setLinv16(String linv16) {
		this.linv16 = linv16;
	}

	public BigDecimal getLinv17() {
		return this.linv17;
	}

	public void setLinv17(BigDecimal linv17) {
		this.linv17 = linv17;
	}

	public String getLinv18() {
		return this.linv18;
	}

	public void setLinv18(String linv18) {
		this.linv18 = linv18;
	}

	public BigDecimal getLinv19() {
		return this.linv19;
	}

	public void setLinv19(BigDecimal linv19) {
		this.linv19 = linv19;
	}

	public C140M01A getC140m01a() {
		return this.c140m01a;
	}

	public void setC140m01a(C140M01A c140m01a) {
		this.c140m01a = c140m01a;
	}
	
}