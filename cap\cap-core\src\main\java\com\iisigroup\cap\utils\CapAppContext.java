package com.iisigroup.cap.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.io.Resource;

import tw.com.iisi.cap.context.SeparateReloadableResourceBundleMessageSource;

/**
 * <pre>
 * 取得 Spring Application Context
 * </pre>
 * 
 * @since 2011/11/4
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/11/4,rodeschen,new
 *          <li>2012/12/19,rodeschen,catch NoSuchMessageException
 *          </ul>
 */
public class CapAppContext implements ApplicationContextAware {
    protected final static Logger LOGGER = LoggerFactory.getLogger(CapAppContext.class);

    // [refs#206] cache string by server startup timestamp 會在 main.js 的 require.config 中使用 jsCacheString 作為 urlArgs
    private static String jsCacheString;

    public CapAppContext() {
        // [refs#206] 在 server 啟動時設定此參數
        jsCacheString = "cache=" + System.currentTimeMillis();
    }

    /*
     * (non-Javadoc)
     * 
     * @see org.springframework.context.ApplicationContextAware#setApplicationContext(org.springframework.context.ApplicationContext)
     */
    @Override
    public void setApplicationContext(ApplicationContext ctx) {
        applicationContext = ctx;
    }

    /** Spring Application Context */
    private static ApplicationContext applicationContext;

    /**
     * Get Spring Application Context
     * 
     * @return {@linkplain #applicationContext applicationContext}
     */
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 以指定的 Bean name 取得對應 Bean
     * 
     * @param <T>
     * @param beanName
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T getBean(String beanName) {
        return (T) (applicationContext.containsBean(beanName) ? applicationContext.getBean(beanName) : null);
    }

    /**
     * 從指定路徑取得資源
     * 
     * @param path
     *            路徑
     * @return
     */
    public static Resource getResource(String path) {
        Resource resource = applicationContext.getResource(path);
        return resource;
    }

    /**
     * 從指定的Bean name和Class取得的對應Bean
     * 
     * @param <T>
     * @param beanName
     * @param c
     * @return
     */
    public static <T> T getBean(String beanName, Class<T> c) {
        return (T) applicationContext.getBean(beanName, c);
    }

    /**
     * 以MessageKey當前地區取得訊息
     * 
     * @param key
     *            MessageKey
     * @return
     */
    public static String getMessage(String key) {
        Locale locale = (Locale) LocaleContextHolder.getLocale();
        return getMessage(key, new Object[] {}, locale == null ? Locale.getDefault() : locale);
    }

    /**
     * 以MessageKey、設置的額外資訊和當前地區取得訊息
     * 
     * @param key
     *            MessageKey
     * @param args
     *            額外資訊
     * @return
     */
    public static String getMessage(String key, Object[] args) {
        Locale locale = (Locale) LocaleContextHolder.getLocale();
        return getMessage(key, args, locale == null ? Locale.getDefault() : locale);
    }

    /**
     * 以MessageKey和指定地區取得訊息
     * 
     * @param key
     *            MessageKey
     * @param locale
     *            地區
     * @return
     */
    public static String getMessage(String key, Locale locale) {
        return getMessage(key, new Object[] {}, locale);
    }

    /**
     * 以MessageKey、自定義資訊和地區來取得訊息
     * 
     * @param key
     *            MessageKey
     * @param args
     *            額外資訊
     * @param locale
     *            地區
     * @return
     */
    public static String getMessage(String key, Object[] args, Locale locale) {
        try {
            return applicationContext.getMessage(key, args, locale);
        } catch (NoSuchMessageException e) {
            // 因應弱掃改動
            // LOGGER.warn("can't find message key:" + key);
            return key;
        }

    }

    /**
     * 以MessageKey、自定義的額外資訊和地區來取得訊息
     * 
     * @param key
     *            MessageKey
     * @param extraInfo
     *            額外資訊
     * @param locale
     *            地區
     * @return
     */
    public static String getMessage(String key, Map<String, String> extraInfo, Locale locale) {
        try {
            List<String> args = new ArrayList<>();
            if (extraInfo != null) {
                for (Entry<String, String> e : extraInfo.entrySet()) {
                    args.add(e.getValue());
                }
            }
            return applicationContext.getMessage(key, args.toArray(), locale);
        } catch (NoSuchMessageException e) {
            // 因應弱掃改動
            // LOGGER.warn("can't find message key:" + key);
            return key;
        }

    }

    /**
     * 以綴詞和地區來取得訊息
     * 
     * @param prefix
     *            前綴
     * @param locale
     *            地區
     * @return {@linkplain tw.com.iisi.cap.context.SeparateReloadableResourceBundleMessageSource#resolvePrefix(String, Locale) resolvePrefix}({@code prefix}, {@code locale})
     */
    public static Map<String, String> getMessages(String prefix, Locale locale) {
        SeparateReloadableResourceBundleMessageSource ss = applicationContext.getBean(SeparateReloadableResourceBundleMessageSource.class);
        return ss.resolvePrefix(prefix, locale);
    }

    /**
     * @return the jsCacheString
     */
    public static String getJsCacheString() {
    	//LOCAL開發模式，才不用reload js 都要重啟
    	//return "cache=" + System.currentTimeMillis();
    	
    	//PROD
        return jsCacheString;
    }
}
