package tw.com.iisi.cap.dao.utils;

/**
 * <pre>
 * SearchModeParameter Util
 * 查詢資料串接
 * </pre>
 *
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2020/7/4,007625,new
 *          </ul>
 * @since 2020/7/4
 */
public class SearchParameterUtil {

    /**
     * 將所有參數用and串起
     *
     * @param parameters
     *            查詢資料
     * @return
     */
    public static SearchModeParameter getMultiAndParameters(SearchModeParameter... parameters) {
        if (parameters != null) {
            if (parameters.length == 1) {
                return parameters[0];
            } else {
                SearchModeParameter s1 = new SearchModeParameter(SearchMode.AND, parameters[0], parameters[1]);
                for (int i = 2; i < parameters.length; i++) {
                    s1 = new SearchModeParameter(SearchMode.AND, s1, parameters[i]);
                }
                return s1;
            }
        }
        return null;
    }

    /**
     * 將所有參數用or串起
     *
     * @param parameters
     *            查詢資料
     * @return
     */
    public static SearchModeParameter getMultiOrParameters(SearchModeParameter... parameters) {
        if (parameters != null) {
            if (parameters.length == 1) {
                return parameters[0];
            } else {
                SearchModeParameter s1 = new SearchModeParameter(SearchMode.OR, parameters[0], parameters[1]);
                for (int i = 2; i < parameters.length; i++) {
                    s1 = new SearchModeParameter(SearchMode.OR, s1, parameters[i]);
                }
                return s1;
            }
        }
        return null;
    }

}
