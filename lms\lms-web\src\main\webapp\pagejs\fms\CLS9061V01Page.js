$(function(){	
	var grid = $("#gridview").iGrid({
        handler: "cls9061gridhandler",
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        postData: {
            formAction: "queryMain",
            docStatus: viewstatus
        },
        colModel: [{
            colHeader: "",name: 'oid', hidden: true
        }, {
        	colHeader: "",name: 'mainId', hidden: true
        }, {
            colHeader: i18n.cls9061v01["C900M01E.custId"],//統一編號
            align: "left", width: 90, sortable: true, name: 'custId',
            formatter: 'click', onclick: openDoc
        }, {
            colHeader: i18n.def["compName"], //姓名
            align: "left", width: 100, sortable: true, name: 'custName'
        }, {
            colHeader: i18n.cls9061v01["C900M01E.comName"], //任職公司名稱
            align: "left", width: 120, sortable: true, name: 'comName'
        }, {
            colHeader: i18n.cls9061v01["C900M01E.comTarget"], //服務單位地址
            align: "left", width: 120, sortable: true, name: 'comTarget'
        }, {
            colHeader: i18n.cls9061v01["C900M01E.sourceNo"], //來源文號
            align: "left", width: 120, sortable: true, name: 'sourceNo'
        }, {
            colHeader: i18n.cls9061v01["C900M01E.isClosed"], //是否解除
            align: "left", width: 60, sortable: true, name: 'isClosed'
        }, {
            colHeader: i18n.cls9061v01["C900M01E.updater"], //異動人員
            align: "left", width: 80, sortable: true, name: 'updater'
        }, {
            colHeader: i18n.cls9061v01["C900M01E.updateTime"], //異動日期
            align: "left", width: 120, sortable: true, name: 'updateTime'
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
    	var postData = {
    			'mainOid': rowObject.oid, 
    			'mainId': rowObject.mainId,
    			'mainDocStatus': viewstatus
    		}
    		$.form.submit({ url:'../fms/cls9061m01/01', data:postData, target:rowObject.oid});
    }
    
    $("#buttonPanel").find("#btnFilter").click(function(){
    	FilterAction.openBox();
    }).end().find("#btnAdd").click(function(){
    	AddCustAction.open({
    		handler: 'cls9061formhandler',
			action : 'newC900M01E',
            doNewUser: true,
			data : {                
            },
			callback : function(responseData){
				//更新 grid
				grid.trigger("reloadGrid");
            	//開啟m01頁面
				var rowObject = {};
				rowObject['oid'] = responseData.mainOid;
				rowObject['mainId'] = responseData.mainId;
				openDoc(null, null, rowObject);
				
            	//關掉 AddCustAction 的 thickbox
            	$.thickbox.close();
			}
		});
    }).end().find("#btnDelete").click(function(){
    	var rows = grid.getGridParam('selrow');
        var list = "";
        if (rows != 'undefined' && rows != null && rows != 0) {
            var data = grid.getRowData(rows);
            list = data.oid;
        }
        if (list == "") {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "cls9061formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "deleteMark",
                        list: list
                    },
                    success: function(obj){
                    	if(obj.saveOkFlag){
                    		API.showMessage(i18n.def.runSuccess);//執行成功
                    	}
                    	grid.trigger("reloadGrid");
                    }
                });
            }
        });	
    });
});

