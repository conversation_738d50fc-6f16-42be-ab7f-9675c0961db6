/* 
 * CLS1161S02Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.panels.CLS1161S02APanel;
import com.mega.eloan.lms.cls.panels.CLS1161S02BPanel;
import com.mega.eloan.lms.cls.panels.CLS1161S02CPanel;
import com.mega.eloan.lms.cls.panels.CLS1161S02FPanel;
import com.mega.eloan.lms.model.C160M01A;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 動審表 - 額度明細表
 * </pre>
 * 
 * @since 2012/12/27
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/27,Fantasy,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1161s02")
public class CLS1161S02APage extends AbstractOutputPage {

	@Autowired
	CLSService clsService;

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		C160M01A c160m01a = null;
		
		String mainId = Util.trim(params.getString("mainId"));
		if(Util.isNotEmpty(mainId)){
			c160m01a = clsService.findC160M01A_mainId(mainId);
		}
		//logger.debug("CLS1161S02APage[mainId="+mainId+"]["+(c160m01a==null?"null_c160m01a":("c160m01a.rptId="+c160m01a.getRptId()) )+"]");
		
		
		model.addAttribute("hasHtml", true);
		model.addAttribute("loadScript", "loadScript('pagejs/cls/CLS1161S02APage');");
		
		renderJsI18N(CLS1161S02APage.class); // render i18n

		new CLS1161S02APanel("CLS1161S02A").processPanelData(model, params); // add panel

		new CLS1161S02BPanel("CLS1161S02B").processPanelData(model, params); // add panel

		new CLS1161S02CPanel("CLS1161S02C", c160m01a).processPanelData(model, params); // add panel

		new CLS1161S02FPanel("CLS1161S02F").processPanelData(model, params); // add panel

		return "&nbsp;";
	}

	@Override
	protected String getViewName() {
		return getEloanPagePathByClass(getClass()); // 返回對應的 HTML 模板路徑
	}
}
