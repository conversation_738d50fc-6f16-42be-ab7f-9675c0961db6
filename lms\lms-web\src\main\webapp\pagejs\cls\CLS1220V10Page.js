pageJsInit(function() {
	$(function() {

		//初始化作業
		//設定經辦下拉選單(派案-指定簽案行員)
		$.ajax({
			type: "POST",
			handler: "cls1220m10formhandler",
			action: "getMegaEmpInfo"
		}).done(function(responseData) {
			if (responseData.Success) {
				var signMegaEmp = $("#SignMegaEmp");
				signMegaEmp.setItems({
					item: responseData.bossListAO,
					space: true,
					format: "{value} {key}"
				});
			}
		});


		var grid = $("#gridview").iGrid({
			handler: 'cls1222gridhandler',
			height: 350,
			width: 800,
			autowidth: false,
			action: "queryView_E",
			postData: {
				docStatus: viewstatus
			},
			rowNum: 15,
			sortname: "createTime",
			sortorder: "desc",
			multiselect: false,
			colModel: [
				{ name: 'oid', hidden: true }
				, { name: 'mainId', hidden: true }
				, { name: 'docStatus', hidden: true }
				, { name: 'ownBrId', hidden: true }
				, { name: 'applyKind', hidden: true }
				, { name: 'incomType', hidden: true }
				, {
					//身分證統編
					colHeader: i18n.cls1220v10['C122M01A.custId'],
					width: 90,
					sortable: true,
					name: 'custId',
					formatter: 'click',
					onclick: openDoc
				}, {
					//借款人姓名
					colHeader: i18n.cls1220v10['C122M01A.custName'],
					align: "left",
					width: 90,
					sortable: true,
					name: 'custName'
				}, {
					//線上進件時間 >> 進件時間
					colHeader: i18n.cls1220v10['C122M01A.applyTS'],
					align: "left",
					width: 100,
					name: 'applyTS'
				}, {
					//進件類型
					colHeader: i18n.cls1220v10['C122M01A.incomType'],
					align: "left",
					width: 60,
					sortable: false,
					name: 'incomTypeDesc'
				}, {
					//案件類型
					colHeader: i18n.cls1220v10['C122M01A.applyKind'],
					align: "left",
					width: 60,
					sortable: false,
					name: 'applyKindDesc'
				}, {
					//申貸案件狀態
					colHeader: i18n.cls1220v10['C122M01A.docStatus'],
					align: "left",
					width: 60,
					sortable: false,
					name: 'docStatusDesc'
				}, {
					//備註
					colHeader: i18n.cls1220v10['C122M01A.notifyMemo'],
					align: "left",
					width: 140,
					name: 'notifyMemo'
				}, {
					//案件編號
					colHeader: i18n.cls1220v10['C122M01A.ploanCaseNo'],
					align: "left",
					width: 100,
					name: 'ploanCaseId'
				}, {
					//行銷方案
					colHeader: i18n.cls1220v10['csc_ploanPlan'],
					align: "left",
					width: 60,
					name: 'ploanPlan'
				}, {
					//J-112-0006 取消顯示[最後異動者]，新增[地政士]
					//最後異動者
					//	colHeader : i18n.cls1220v10['C122M01A.updater'],
					//	align : "left",
					//	width : 60,
					//	name : 'updater'
					//地政士
					colHeader: i18n.cls1220v10['C122M01A.laaName'],
					align: "left",
					width: 60,
					name: 'laaName'
				}, {
					//簽案人員
					colHeader: i18n.cls1220v10['C122M01A.signMegaEmpName'],
					align: "left",
					width: 60,
					name: 'c122m01f.signMegaEmpName'
				}]
		});

		var c900m01nChooseEmpGrid = $("#c900m01nChooseEmpGrid").iGrid({
			handler: 'cls1222gridhandler',
			height: 400,
			width: 600,
			autowidth: false,
			action: "queryEmpCanAssignCase",
			needPager: false,
			// rowNum: 15,
			sortname: "assignEmpNo",
			sortorder: "asc",
			multiselect: true,
			colModel: [
				{ name: 'oid', hidden: true }
				, { name: 'brNo', hidden: true }
				, {
					// 行員代號
					colHeader: i18n.cls1220v10['C900M01N.assignEmpNo'],
					align: "right",
					width: 25,
					sortable: true,
					name: 'assignEmpNo'
				}, {
					//行員姓名
					colHeader: i18n.cls1220v10['C900M01N.assignEmpName'],
					align: "left",
					width: 25,
					sortable: true,
					name: 'assignEmpName'
				}, {
					// 派案順序
					colHeader: i18n.cls1220v10['C900M01N.assignOrder'],
					align: "left",
					width: 10,
					sortable: true,
					name: 'assignOrder'
				}]
		});

		var c900m01nBrUserGrid = $("#c900m01nBrUserGrid").iGrid({
			handler: 'cls1222gridhandler',
			height: 500,
			width: 500,
			autowidth: false,
			action: "queryThisBrNoEmp",
			needPager: false,
			// rowNum: 15,
			cellEdit: true,
			cellsubmit: 'clientArray',
			sortname: "userId",
			sortorder: "asc",
			multiselect: true,
			colModel: [
				{ name: 'oid', hidden: true }
				, { name: 'brNo', hidden: true }
				, { name: 'exist', hidden: true }// 已經存在的資料要幫他預設勾起來
				, {
					// 行員代號
					colHeader: i18n.cls1220v10['C900M01N.assignEmpNo'],
					align: "right",
					width: 25,
					sortable: false,
					name: 'userId'
				}, {
					// 行員姓名
					colHeader: i18n.cls1220v10['C900M01N.assignEmpName'],
					align: "left",
					width: 25,
					sortable: false,
					name: 'userName'
				}, {
					// 派案順序
					colHeader: i18n.cls1220v10['C900M01N.assignOrder'],
					align: "left",
					width: 10,
					name: 'assignOrder',
					editable: true,
					sortable: true,
					editrules: {
						number: true
					}
				}],
			loadComplete: function() {
				// 已經存在的資料要幫他預設勾起來
				var length = c900m01nBrUserGrid.getGridParam("records");
				// c900m01nBrUserGrid(1); 資料從第一列開始
				for (var i = 1; i <= length; i++) {
					var assignEmp = c900m01nBrUserGrid.getRowData(i);
					if (assignEmp.exist == 'Y') {
						c900m01nBrUserGrid.setSelection(i, true);
					}
				}
			}
		});

		var chooseEmpRows = [];
		var lastC122m01aOid = '';
		var c900m01nAssignCaseGrid = $("#c900m01nAssignCaseGrid").iGrid({
			handler: 'cls1222gridhandler',
			height: 450,
			width: 600,
			autowidth: false,
			localFirst: true,// lazy load
			action: "queryAssignCase",
			needPager: false,
			cellEdit: true,
			cellsubmit: 'clientArray',
			sortname: "lastAssignTime|assignOrder",
			sortorder: "asc|asc",
			multiselect: false,
			// 有設定這個看起來每次才可以"重傳參數"給後端
			postData: {
				'assignEmpNos': chooseEmpRows,
				'lastC122m01aOid': lastC122m01aOid
			},
			colModel: [
				{
					// 行員代號
					colHeader: i18n.cls1220v10['C900M01N.assignEmpNo'],
					align: "right",
					width: 25,
					sortable: false,
					name: 'assignEmpNo'
				}, {
					// 行員姓名
					colHeader: i18n.cls1220v10['C900M01N.assignEmpName'],
					align: "left",
					width: 25,
					sortable: false,
					name: 'assignEmpName'
				}, {
					// 案件數
					colHeader: i18n.cls1220v10['C900M01N.caseCount'],
					align: "left",
					width: 10,
					sortable: false,
					name: 'shouldCount',
					editable: true,
					editrules: {
						number: true
					}
				}, {
					// 可被分配的最大案件數
					colHeader: i18n.cls1220v10['C900M01N.maxCount'],
					align: "left",
					width: 20,
					sortable: false,
					name: 'maxCount',
					hidden: true
				}, {
					// 此次分案的最後一筆C122M01A.oid
					name: 'lastC122m01aOid', hidden: true
				}, {
					// 此次分案的總案件數totalCount
					name: 'totalCount', hidden: true
				}],
			afterSaveCell: function(rowid, cellname, value, iRow, iCol) {
				debugger
				var rowData = c900m01nAssignCaseGrid.getRowData();
				var shouldTotalCount = 0;
				for (var i = 0; i < rowData.length; i++) {
					shouldTotalCount += parseInt(rowData[i].shouldCount);
				}
				var c900m01nShouldTotalCount = $("#c900m01nShouldTotalCount");
				c900m01nShouldTotalCount.val(shouldTotalCount);
			},
			loadComplete: function() {
				if (c900m01nAssignCaseGrid) {
					// 把此次分案總筆數列在grid上
					var length = c900m01nAssignCaseGrid.getGridParam("records");
					var totalCount = 0;
					// 一筆資料都沒有就是預設的0
					if (length > 0) {
						var assignEmp = c900m01nAssignCaseGrid.getRowData(1);
						if (assignEmp.totalCount) {
							totalCount = assignEmp.totalCount;
						}
					}

					// 總件數
					var c900m01nTotalCaseCount = $("#c900m01nTotalCaseCount");
					c900m01nTotalCaseCount.val(totalCount);
					// 已分配總件數		
					var c900m01nShouldTotalCount = $("#c900m01nShouldTotalCount");
					c900m01nShouldTotalCount.val(totalCount);
				}
			}
		});

		function openDoc(cellvalue, options, rowObject) {
			var applyKind = rowObject.applyKind;
			var murl = '';
			switch (applyKind) {
				case 'H':
					murl = '../lms/cls1220m01/01';
					break;
				case 'C':
					murl = '../lms/cls1220m02/01';
					break;
				case 'B':
					murl = '../lms/cls1220m03/01';
					break;
				case 'E':
					murl = '../lms/cls1220m05/01';
					break;
				case 'F':
					murl = '../lms/cls1220m05/01';
					break;
				case 'P':
					murl = '../lms/cls1220m04/01';
					break;
				case 'Q':
					murl = '../lms/cls1220m04/01';
					break;
				case 'I':
				case 'J':
					murl = '../lms/cls1220m04/01';
					break;
				case 'O':
					murl = '../lms/cls1220m05/01';
					break;
			}

			$.form.submit({
				url: murl,
				data: {
					mainOid: rowObject.oid,
					mainId: rowObject.mainId,
					mainDocStatus: rowObject.docStatus,
					mainApplyKind: rowObject.applyKind,
					mainIncomType: rowObject.incomType

				},
				target: rowObject.oid
			});
		};

		var changeSignAction = function(data) {
			$("#changeSignEmpDiv").thickbox({
				title: i18n.cls1220v10['button.CaseToChangeSignEmp'], width: 350, height: 200, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
				buttons: {
					"sure": function() {
						$.ajax({
							type: "POST",
							handler: "cls1220m10formhandler",
							data: {
								formAction: "changeSignEmp",
								mainOid: data.oid,
								mainId: data.mainId,
								chgSignMegaEmp: $("select#chgSignMegaEmp").val(),
								chgEvaMegaEmpNo: $("select#chgEvaMegaEmp").val(),
								chgEstUnitName: $('#chgEstUnitName').val()
							}
						}).done(function(responseData) {
							if (responseData.Success) {
								$.thickbox.close();
								grid.trigger("reloadGrid");
							} else {
								CommonAPI.showErrorMessage(responseData.Message);
							}
						});
					},
					"cancel": function() {
						$.thickbox.close();
					}
				}
			});
		}

		var setEstData = function() {
			//設定經辦下拉選單(有無擔保品-指定收件行員)
			return $.ajax({
				type: "POST",
				handler: "cls1220m10formhandler",
				action: "getMegaEmpInfo"
			}).done(function(responseData) {
				if (responseData.Success) { //成功
					var chgEvaMegaEmp = $("#chgEvaMegaEmp");
					chgEvaMegaEmp.setItems({
						item: responseData.bossListAO,
						space: true,
						format: "{value} {key}"
					});
				}
			});
		}

		$("#buttonPanel").find('#btnView').click(function() {
			var selrow = grid.getGridParam('selrow');
			if (selrow) {
				openDoc('', '', grid.getRowData(selrow));
			}
			else {
				CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
			}
		}).end().find("#btnModify").click(function() {//查詢客戶申貸記錄
			var _id = "div_applyKindE_History";
			var _form = "div_applyKindE_History_form";
			var grid_id = "grid_applyKindE_History";

			//clear data
			$("#" + _form).reset();
			var my_post_data = get_param_grid_history('N');

			if ($("#" + grid_id + ".ui-jqgrid-btable").length > 0) {
				$("#" + grid_id).jqGrid("setGridParam", {
					postData: my_post_data,
					search: true
				}).trigger("reloadGrid");
			} else {
				$("#" + grid_id).iGrid({
					handler: 'cls1222gridhandler',
					height: 160,
					divWidth: 0,
					postData: my_post_data,
					colModel: [{
						colHeader: i18n.cls1220v10['C122M01A.custName'],
						align: "left", width: 120, sortable: false, name: 'custName'
					}, {
						colHeader: i18n.cls1220v10['C122M01A.applyTS'],
						align: "left", width: 70, sortable: false, name: 'applyTS'
					}, {
						colHeader: i18n.cls1220v10['C122M01A.ownBrId'],
						align: "left", width: 100, sortable: false, name: 'ownBrId'
					}, {
						colHeader: i18n.cls1220v10['C122M01A.incomType'],
						align: "left", width: 100, sortable: false, name: 'incomType'
					}, {
						colHeader: i18n.cls1220v10['C122M01A.applyKind'],
						align: "left", width: 100, sortable: false, name: 'applyKind'
					}, {
						colHeader: i18n.cls1220v10['C122M01A.docStatus'],
						align: "left", width: 100, sortable: false, name: 'docStatus'
					}, {
						colHeader: i18n.cls1220v10['C122M01A.signMegaEmpName'],
						align: "left", width: 100, sortable: false, name: 'c122m01f.signMegaEmpName'
					}, {
						colHeader: i18n.cls1220v10['C122M01A.orgBrId'],
						align: "left", width: 100, sortable: false, name: 'orgBrId'
					}, {
						colHeader: i18n.cls1220v10['C122M01A.marketingStaff'],
						align: "left", width: 100, sortable: false, name: 'marketingStaff'
					}]
				});
			}
			if (true) {
				$("#" + _id + " #div_applyKindE_History_label_custId").val(i18n.cls1220v10['C122M01A.custId']);
			}

			$("#" + _id).thickbox({
				title: '',
				width: 650, height: 400, align: 'center', valign: 'bottom', modal: false,
				buttons: {
					"close": function() {
						$.thickbox.close();
					}
				}
			});
		}).end().find("#btnPrint").click(function() {//引介案件進度查詢
			var _id = "div_case_progress";
			var _form = "filer_case_progress_form";
			var _grid_id = "grid_case_progress";

			$("#" + _form).find("[id=tx_applyTS_start]").datepicker('setDate', 'today');
			$("#" + _form).find("[id=tx_applyTS_end]").datepicker('setDate', 'today');

			//初始化引介分行下拉選單，營業單位只能選自己
			$.ajax({
				handler: "cls1220m10formhandler",
				action: "queryBrList",
				data: {}
			}).done(function(obj) {
				var select_brIdFilter = $("#brIdFilter");
				select_brIdFilter.setItems({
					item: obj.brList,
					clear: false,
					space: true,
					format: "{value} {key}"
				});
				if (obj.userBrid != null) {
					$("#" + _form).find("#brIdFilter").val(encodeURI(obj.userBrid));

				}
			});
			//初始化引介行員下拉選單，營業單位只能選自己
			$.ajax({
				type: "POST",
				handler: "cls1220m10formhandler",
				action: "getMegaEmpInfo",
				data: { "need943flag": "1" }
			}).done(function(responseData) {
				if (responseData.Success) {
					var introduceMegaEmp = $("#" + _form).find("#introduceMegaEmp");
					introduceMegaEmp.setItems({
						item: responseData.bossListAO,
						space: true,
						format: "{value} {key}"
					});
				}
			});

			$("#" + _id).thickbox({
				title: '引介案件進度查詢',
				width: 800, height: 500, align: 'center', valign: 'bottom', modal: false,
				buttons: {
					"查詢": function() {//依據條件查詢
						if ($("#" + _form).find("[id=tx_applyTS_start]").val() == "" || $("#" + _form).find("[id=tx_applyTS_end]").val() == "") {//
							CommonAPI.showErrorMessage(i18n.cls1220v10['case_progress.C122M01F.megaEmpNo.error1']);
							return;
						}
						var _post_data = {
							'formAction': "query_case_progess",
							'applyTS_beg': $("#" + _form).find("[id=tx_applyTS_start]").val(),
							'applyTS_end': $("#" + _form).find("[id=tx_applyTS_end]").val(),
							'custId': $("#" + _form).find("[id=custId]").val(),
							'brIdFilter': $("#" + _form).find("[id=brIdFilter]").val(),
							'introduceMegaEmp': $("#" + _form).find("[id=introduceMegaEmp]").val(),
							'ploanCaseId': $("#" + _form).find("[id=ploanCaseId]").val()
						};

						if ($("#" + _grid_id + ".ui-jqgrid-btable").length > 0) {
							$("#" + _grid_id).jqGrid("setGridParam", {
								postData: _post_data,
								search: true
							}).trigger("reloadGrid");
						} else {
							$("#" + _grid_id).iGrid({
								handler: 'cls1222gridhandler',
								height: 160,
								postData: _post_data,
								colModel: [{
									colHeader: i18n.cls1220v10['case_progress.C122M01A.custName'],
									align: "left", width: 120, sortable: false, name: 'custName'
								}, {
									colHeader: i18n.cls1220v10['case_progress.C122M01A.applyTS'],
									align: "left", width: 90, sortable: false, name: 'applyTS'
								}, {
									colHeader: i18n.cls1220v10['case_progress.C122M01A.ownBrId'],
									align: "left", width: 110, sortable: false, name: 'ownBrId'
								}, {
									colHeader: i18n.cls1220v10['case_progress.C122M01A.incomType'],
									align: "left", width: 100, sortable: false, name: 'incomType'
								}, {
									colHeader: i18n.cls1220v10['case_progress.C122M01A.docStatus'],
									align: "left", width: 100, sortable: false, name: 'docStatus'
								}, {
									colHeader: i18n.cls1220v10['case_progress.C122M01F.signMegaEmpNo'],
									align: "left", width: 90, sortable: false, name: 'signMegaEmpNo'
								}, {
									colHeader: i18n.cls1220v10['case_progress.C122M01A.ploanIntroduceBr1st'],
									align: "left", width: 110, sortable: false, name: 'ploanIntroduceBr1st'
								}, {
									colHeader: i18n.cls1220v10['case_progress.signMegaEmpName'],
									align: "left", width: 90, sortable: false, name: 'marketingStaff'
								}]
							});
						}
					}, "匯出查詢結果": function() {//開始產生EXCEL報表            	
						$.ajax({
							type: "POST",
							handler: "cls1220m10formhandler",
							action: "make_case_progress_excelfilename",
							data: {
								'formAction': "make_case_progress_excelfilename",
								'applyTS_beg': $("#" + _form).find("[id=tx_applyTS_start]").val(),
								'applyTS_end': $("#" + _form).find("[id=tx_applyTS_end]").val(),
								'custId': $("#" + _form).find("[id=custId]").val(),
								'brIdFilter': $("#" + _form).find("[id=brIdFilter]").val()
							}
						}).done(function(responseData) {
							if (responseData.Success) {
								$.form.submit({
									url: __ajaxHandler,
									target: "_blank",
									data: {
										_pa: 'lmsdownloadformhandler',
										'applyTS_beg': $("#" + _form).find("[id=tx_applyTS_start]").val(),
										'applyTS_end': $("#" + _form).find("[id=tx_applyTS_end]").val(),
										'custId': $("#" + _form).find("[id=custId]").val(),
										'brIdFilter': $("#" + _form).find("[id=brIdFilter]").val(),
										'introduceMegaEmp': $("#" + _form).find("[id=introduceMegaEmp]").val(),
										'ploanCaseId': $("#" + _form).find("[id=ploanCaseId]").val(),
										'fileDownloadName': responseData.fileNmae,
										'serviceName': "cls1220r13rptservice"
									}
								});
								//$.thickbox.close();
							}
						});
					}, "close": function() {//關閉
						$.thickbox.close();
					}
				}
			});
		}).end().find("#btnCaseToChange").click(function() {
			//只有待派案的案件可以改派分行
			var grid_chose_data = grid.getSingleData();
			if (grid_chose_data) {
				$.ajax({
					type: "POST",
					handler: "cls1220m10formhandler",
					data: {
						formAction: "C122M01A_checkDocStatus",
						mainOid: grid_chose_data.oid
					}
				}).done(function(responseData) {
					if (responseData.Success) { //待派案可改派分行
						CommonAPI.showAllBranch({
							btnAction: function(a, rtn) {
								MegaApi.confirmMessage("是否將" + grid_chose_data.custId + " " + grid_chose_data.custName
									+ " 由 " + grid_chose_data.ownBrId + " 改分派至 " + rtn.brNo + " ？", function(action) {
										if (action) {
											$.thickbox.close();
											changeOwnBrId_memo().done(function(json_changeOwnBrId_memo) {
												$.ajax({
													handler: 'cls1220m10formhandler',
													action: 'changeOwnBrId_then_mail',
													data: $.extend(json_changeOwnBrId_memo, { 'mainOid': grid_chose_data.oid, 'newBrNo': rtn.brNo })
												}).done(function(json) {
													grid.trigger("reloadGrid");
												});
											});
										}
									});

							}
						});
						grid.trigger("reloadGrid");
					} else { //審核中之案件不可改派其他分行
						CommonAPI.showErrorMessage(responseData.Message);
						return false;
					}
				});

			}

			//    	var grid_chose_data = grid.getSingleData();
			//		if (grid_chose_data){
			//			
			//        }else{
			////        	CommonAPI.showMessage(i18n.def['grid.selrow']);
			//        }
		}).end().find("#btnFilter").click(function() {
			$("#filterBoxForm").reset();
			$("#incomTypeTr").hide();
			$("#filterBoxDiv").thickbox({ // 使用選取的內容進行彈窗
				title: i18n.def.query,
				width: 590,
				height: 350,
				align: "center",
				valign: "bottom",
				modal: false,
				i18n: i18n.def,
				buttons: {
					"sure": function() {
						$.thickbox.close();
						//~~~~
						grid.jqGrid("setGridParam", {
							postData: $.extend({ docStatus: viewstatus }, $("#filterBoxForm").serializeData()),
							search: true
						}).trigger("reloadGrid");
					},
					"cancel": function() {
						$.thickbox.close();
					}
				}
			});
		}).end().find("#btnAdd").click(function() {
			//先清空視窗欄位
			$('#custId').val("");
			$('#custName').val("");
			$('#Year').val("");
			$('#Month').val("");
			$('#extYear').val("");
			$('#applyAmt').val("");

			//跳一個視窗，填寫一些基本資料[CustId、申請金額、借款年限...]
			$("#C122M01A_CreateDiv").thickbox({
				title: '本次申貸資料', width: 600, height: 400, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
				buttons: {
					"sure": function() {
						//檢核欄位
						$.ajax({
							type: "POST",
							handler: "cls1220m10formhandler",
							data: {
								formAction: "checkC122M01AByManualData",
								custId: $('#custId').val(),
								dupNo: $('#dupNo').val(),
								applyAmt: $('#applyAmt').val(),
								year: $('#Year').val(),
								estFlag: $('#estFlag').val(),
								ByType: $("input[name='ByType']:radio:checked").val(),
								extYear: $('#extYear').val(),
								nowExtend: $("input[name='nowExtend']:radio:checked").val()
							}
						}).done(function(responseData) {
							if (responseData.msg) {
								CommonAPI.showErrorMessage(responseData.msg + " 未填妥");
								return;
							} else {
								//建檔開始
								$.ajax({
									type: "POST",
									handler: "cls1220m10formhandler",
									data: {
										formAction: "makeC122M01AByManual",
										custId: $('#custId').val(),
										dupNo: $('#dupNo').val(),
										custName: $('#custName').val(),
										applyAmt: $('#applyAmt').val(),
										maturity: $('#Year').val(),
										maturityM: $('#Month').val(),
										nowExtend: $("input[name='nowExtend']:radio:checked").val(),
										extYear: $('#extYear').val(),
										applyKind: ($("input[name='ByType']:radio:checked").val() == "IJ" ? $("input[name='youngLoanType']:radio:checked").val() : $("input[name='ByType']:radio:checked").val()),
										estFlag: $('#estFlag').val(),
										purposeType: $("input[name='purposeType']:radio:checked").val(),
										purchaseHouse: $("input[name='purchaseHouse']:radio:checked").val(),
										laaFlag: $("input[name='laaFlag']:radio:checked").val()
									}
								}).done(function(responseData) {
									if (responseData.Success) {
										$.thickbox.close();
										$("#gridview").trigger("reloadGrid");

										openDoc('', '', responseData);
										//			         				        
									}
								});//建檔END
							}
						});
					},
					"cancel": function() {
						$.thickbox.close();
					}
				}
			});

		}).end().find("#btnCaseToChangeSignEmp").click(function() { //改派簽案人員
			var selrow = grid.getGridParam('selrow');
			if (selrow) {
				var data = grid.getRowData(selrow);
				//待派案之案件不得改派，請於確認案件內容後於執行派案
				if (data.docStatus == 'A00') {
					CommonAPI.showErrorMessage("待派案之案件不得改派，請於確認案件內容後於執行派案");
				} else if (data.docStatus == 'A99') {
					CommonAPI.showErrorMessage("待初審之案件不得改派，請於確認案件內容後於執行派案");
				} else {
					//先根據estFlag判斷要不要隱藏欄位，藏完再顯示dialog
					$.ajax({
						type: "POST",
						handler: "cls1220m10formhandler",
						action: "getEstFlag",
						data: {
							mainOid: data.oid,
							mainId: data.mainId
						}
					}).done(function(responseData) {
						if (responseData.checkAccess) {
							CommonAPI.showErrorMessage(responseData.checkAccess);
							return;
						} else {
							var estFlag = responseData.estFlag;
							$("#chgSignMegaEmp").val(responseData.chgSignMegaEmp);
							$("#chgEvaMegaEmp").val("");
							$('#chgEstUnitName').val("");
							if (estFlag == "1") {
								$("#MegaEmpTr").show();
								$("#estUnitTr").hide();
								setEstData().done(function() {
									$("#chgEvaMegaEmp").val(responseData.chgEvaMegaEmp);
									changeSignAction(data);
								});
							} else if (estFlag == "3") {
								$("#MegaEmpTr").hide();
								$("#estUnitTr").show();
								$('#chgEstUnitName').val(responseData.chgEstUnitName);
								changeSignAction(data);
							} else {
								$("#MegaEmpTr").hide();
								$("#estUnitTr").hide();
								changeSignAction(data);
							}
						}
					});
				}
			} else {
				CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
			}
		}).end().find("#btnAutoAssignMaintenance").click(function() {
			$.ajax({// Init被分派分行下拉選單
				type: "POST",
				handler: "cls1220m10formhandler",
				action: "getDomesticBrches"
			}).done(function(json) {
				if (json.Success) {
					var _addSpace = json.br_addSpace == "Y" ? true : false;
					$.each(json.br_itemOrder, function(idx, brNo) {
						var currobj = {};
						var brName = json.br_item[brNo];
						currobj[brNo] = brName;
						var assigneeBrchIdOptions = $('#assigneeBrchIdOptions');
						assigneeBrchIdOptions.setItems({ item: currobj, format: "{value} - {key}", clear: false, space: (_addSpace ? (idx == 0) : false) });
					});
				}
			});

			var lastSel;// 最後一次被inline編輯的資料列id
			function assignSeqformatter(cellvalue, otions, rowObject) {
				var seq = 0;
				if (!cellvalue) {
					cellvalue = ++seq;
				}
				seq = 0;
				return cellvalue;
			}
			if ($('#autoAssignGrid' + '.ui-jqgrid-btable').length > 0) {// 若先前已生成過Grid，不可重複生成
				$("#autoAssignGrid").trigger('reloadGrid');
			} else {
				var $autoAssignGrid = $("#autoAssignGrid").iGrid({
					handler: 'cls1222gridhandler',
					action: "queryAutoAssignGrid",
					height: 250,
					cellsubmit: 'clientArray',
					autowidth: true,
					rownumbers: true,
					sortname: 'assignOrder|assigneeBrchId|oid',
					sortorder: 'asc|asc|asc',
					colModel: [
						{
							colHeader: 'OID'
							, name: 'oid', width: 90, sortable: true, hidden: true
						}, {
							colHeader: '被分派分行'
							, name: 'assigneeBrchId', width: 90, sortable: true, hidden: false
						}, {
							colHeader: '分案順序'
							, name: 'assignOrder', width: 60, align: "center", sortable: true, editable: true
							, editrules: {
								number: true
							}
							, formatter: assignSeqformatter
						}],
					onSelectRow: function(id) {
						if (id && id != lastSel) {
							$("#autoAssignGrid").saveRow(lastSel, false, 'clientArray');
							$('#autoAssignGrid').restoreRow(lastSel);
							lastSel = id;
						}
						$('#autoAssignGrid').editRow(id, false);
					}
				});
			}

			$("#autoAssignDiv").thickbox({
				title: '派案分行設定視窗',
				width: 500,
				height: 500,
				modal: false,
				align: 'center',
				valign: 'bottom',
				buttons: API.createJSON([{
					key: '儲存',
					value: function() {
						var $autoAssignGrid = $('#autoAssignGrid');
						var selGridRowId = $autoAssignGrid.jqGrid('getGridParam', 'selrow');
						$autoAssignGrid.jqGrid('saveRow', selGridRowId, false, 'clientArray');// 透過儲存來解除inline edit狀態，以取被選取欄位值
						var gridRowDataArray = $autoAssignGrid.jqGrid('getRowData');
						var assignOrderArray = $autoAssignGrid.getCol("assignOrder");
						if (gridRowDataArray.length === 0) {
							return API.showMessage('請至少新增一間分行');
						}
						if (checkArrayElementRepeat(assignOrderArray)) {
							return API.showMessage('分案順序不可重複');
						}
						if (checkArrayElementIsInteger(assignOrderArray)) {
							return API.showMessage('分案順序必須為整數');
						}
						CommonAPI.confirmMessage('您確定要變更被分派分行與順序嗎', function(b) {
							if (b) {
								$.ajax({
									handler: 'cls1220m10formhandler',
									action: 'updateAssigneeBrchList',
									formId: "empty",
									data: {
										gridRowDataJSONArray: JSON.stringify(gridRowDataArray)
									}
								}).done(function(obj) {
									API.showMessage('變更成功');
									$autoAssignGrid.trigger('reloadGrid');
								});
							}
						});
					}
				}, {
					key: i18n.def['close'],
					value: function() {
						$.thickbox.close();
					}
				}])
			});
			$("#deleteDesigneeBrch").click(function() {// 派案分行設定視窗-[刪除]被指派分行
				$("#autoAssignGrid").delRowData(lastSel);// lastSel被宣告於jqGrid中的onSelectRow事件裡
			});
		}).end().find("#btnOneButtonAssignCase").click(function() { //一鍵分案
			// step1.先去查
			c900m01nChooseEmpGrid.trigger("reloadGrid");
			$("#c900m01nChooseEmpDiv").thickbox({
				title: '一鍵分案設定(信調人員)', width: 620, height: 540, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
				buttons: {
					// 人員清單維護
					"人員清單維護": function() {
						c900m01nBrUserGrid.trigger("reloadGrid");

						$("#c900m01nBrUserDiv").thickbox({
							title: '人員清單維護', width: 520, height: 640, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
							buttons: {
								"sure": function() {
									// 在要送ajax給後端前!!呼叫下面這一段，將會保存所有的編輯，讓json取得的時候是值，而不是怪怪的input tag
									c900m01nBrUserGrid.jqGrid("editCell", 0, 0, false);
									// .getSelectData("userId")  不能用寫好的，他裡面不接受一筆都沒選
									// 但以這個功能的case，是可以一筆都不選的
									var selUserIds = [];
									var assignRows = [];
									var assignRowsNum = c900m01nBrUserGrid.getGridParam('selarrrow');// 被選取的行數
									for (var o in assignRowsNum) {
										assignRows.push(c900m01nBrUserGrid.getRowData(assignRowsNum[o]));// 被選取的資料
									}

									$.ajax({
										type: "POST",
										handler: "cls1220m10formhandler",
										data: {
											formAction: "saveAssignCaseEmps",
											assignRows: JSON.stringify(assignRows)
										}
									}).done(function(responseData) {
										$.thickbox.close();
										// 更新第一層的清單
										c900m01nChooseEmpGrid.trigger("reloadGrid");
									});
								},
								"cancel": function() {
									$.thickbox.close();
								}
							}
						});
					},
					"派案": function() {
						var chooseEmpRowsNum = c900m01nChooseEmpGrid.getGridParam('selarrrow');

						if (chooseEmpRowsNum == "") {// 請先選擇被分案的人員
							return CommonAPI.showMessage(i18n.cls1220v10['C900M01N.selectAssignRow']);
						}

						chooseEmpRows = [];// 已在grid初始話那邊設定過
						for (var o in chooseEmpRowsNum) {
							chooseEmpRows.push(c900m01nChooseEmpGrid.getRowData(chooseEmpRowsNum[o]).assignEmpNo);// 被選取的資料的assignEmpNo
						}

						// 先去後端檢查是否有待分案案件，沒有的話根本不跳下一個視窗
						$.ajax({
							type: "POST",
							handler: "cls1220m10formhandler",
							data: {
								formAction: "checkHaveNeedAssignCase",
								assignEmpNos: chooseEmpRows
							}
						}).done(function(responseData) {
							// 能走到sucess就是基本檢查都有過
							// 開啟分派案件數量的thickbox
							openAssignCaseThickbox(chooseEmpRows, responseData.lastC122m01aOid);
						});
					},
					"cancel": function() {
						$.thickbox.close();
					}
				}
			});
		});

		$('#addDesigneeBrch').click(function() {// 派案分行設定視窗-[新增]被指派分行
			var $autoAssignGrid = $('#autoAssignGrid');
			var $assigneeBrchIdOptions = $('#assigneeBrchIdOptions');
			var selGridRowId = $autoAssignGrid.jqGrid('getGridParam', 'selrow');
			$autoAssignGrid.jqGrid('saveRow', selGridRowId, false, 'clientArray');// 透過儲存來解除inline edit狀態，以取被選取欄位值
			var assigneeBrchIdOptionVal = $assigneeBrchIdOptions.val();
			var assigneeBrchIdName = $assigneeBrchIdOptions.find('option:selected').html();
			var maxAssignOrder = null;
			var assignOrderArray = $autoAssignGrid.jqGrid('getCol', 'assignOrder');
			if (assignOrderArray.length === 0) {
				maxAssignOrder = 1;
			} else {
				maxAssignOrder = Math.max.apply(null, assignOrderArray) + 1;// 新增序號為現有序號最大值+1
			}
			if (assigneeBrchIdOptionVal != "") {
				// 檢核新增分行時，是否重複新增
				var gridRowIds = $autoAssignGrid.jqGrid('getDataIDs');
				var isDuplicate = false;
				for (var i = 0; i < gridRowIds.length; i++) {
					var gridRowId = gridRowIds[i];
					var gridRowData = $autoAssignGrid.jqGrid('getRowData', gridRowId);
					if (assigneeBrchIdOptionVal == gridRowData.assigneeBrchId.substring(0, 3)) {
						isDuplicate = true;
						break;
					}
				}
				if (isDuplicate) {
					API.showMessage('分行重複，請指派不同分行');
				} else {
					var gridData = {
						assigneeBrchId: assigneeBrchIdName,
						assignOrder: maxAssignOrder
					};
					var gridRowId = $autoAssignGrid.getGridParam("reccount") + 1;
					$autoAssignGrid.jqGrid('addRowData', gridRowId, gridData, 'last');
				}
			} else {
				API.showMessage('請選擇一間分行');
			}
		});
		$('#unloadGird').click(function() {// 派案分行設定視窗-清空清單
			$('#autoAssignGrid').jqGrid('clearGridData');
		});

		checkArrayElementRepeat = function(arrVal) {
			var newArray = [];
			for (var i = arrVal.length; i--;) {
				var val = arrVal[i];
				if ($.inArray(val, newArray) == -1) {
					newArray.push(val);
				} else {
					return true;
				}
			}
			return false;
		};
		checkArrayElementIsInteger = function(arrVal) {
			for (var i = arrVal.length; i--;) {
				var val = arrVal[i];
				if (!Number.isInteger(parseFloat(val))) {
					return true;
				}
			}
			return false;
		};

		// 開啟分派案件數量的thickbox
		function openAssignCaseThickbox(chooseEmpRows, lastC122m01aOid) {
			// postData 要用function return的方式才會持續更新
			c900m01nAssignCaseGrid.jqGrid("setGridParam", {
				postData: {
					'assignEmpNos': function() {
						return chooseEmpRows;
					},
					'lastC122m01aOid': function() {
						return lastC122m01aOid;
					}
				},
				search: true
			}).trigger("reloadGrid");

			$("#c900m01nAssignCaseDiv").thickbox({
				title: '確認', width: 620, height: 640, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
				buttons: {
					"sure": function() {
						// 在要送ajax給後端前!!呼叫下面這一段，將會保存所有的編輯，讓json取得的時候是值，而不是怪怪的input tag
						c900m01nAssignCaseGrid.jqGrid("editCell", 0, 0, false);

						$.ajax({
							type: "POST",
							handler: "cls1220m10formhandler",
							data: {
								formAction: "oneButtonAssignCase",
								// 抓整個grid資料到後端
								assignCaseRows: c900m01nAssignCaseGrid.iGridSerialize(true)
							}
						}).done(function(responseData) {
							$.thickbox.close();// 關真的分案的視窗
							$.thickbox.close();// 關選經辦的視窗
							grid.trigger("reloadGrid");// 分完案要重整主畫面的grid，很多待派案的會變成已派案

							// 上面的關一關後告訴user成功幾筆
							// havePassCase是否有案件因行銷人員而pass
							// message分案的訊息
							if (responseData.havePassCase) {
								// 要呈現紅色
								CommonAPI.showErrorMessage(responseData.message);
							} else {
								CommonAPI.showMessage(responseData.message);
							}
						});
					},
					"cancel": function() {
						$.thickbox.close();
					}
				}
			});
		}

		//引進客戶
		$("#getCustName").click(function() {
			API.includeIdCes({
				defaultValue: $("#C122M01A_CreateForm").find("#custId").val()
			});
		});


		$("#filter_historyBtn").click(function() {

			var grid_id = "grid_applyKindE_History";
			$("#" + grid_id).jqGrid("setGridParam", {
				postData: get_param_grid_history('Y'),
				search: true
			}).trigger("reloadGrid");

		});

		function get_param_grid_history(flag) {
			var _form = "div_applyKindE_History_form";
			return {
				'formAction': "query_applyKindE_History",
				'custId': $("#" + _form).find("[name=search_custId]").val(),
				'flag': flag
			};
		}

		function changeOwnBrId_memo() {
			var my_dfd = $.Deferred();
			$("#div_changeOwnBrId_memo").thickbox({ // 使用選取的內容進行彈窗
				width: 650,
				height: 280,
				align: "center",
				valign: "bottom",
				modal: false,
				i18n: i18n.def,
				buttons: {
					"sure": function() {
						$frm = $("#div_changeOwnBrId_memo_form");
						if ($frm.valid()) {
							$.thickbox.close();
							my_dfd.resolve($frm.serializeData());
						}
					}
				}
			});
			return my_dfd.promise();
		}

		$("input[name=ByType]").change(function() {
			var ByType = $("input[name='ByType']:radio:checked").val();
			$("input[name='laaFlag'][value='N']").prop('checked', true);//[地政士引介]固定都改回預設N  	
			if (ByType == 'E' || ByType == 'O') {//案件類型新增[其他]，欄位比照房貸
				$("#purposeTypeTr").show();
				//觸發一下purposeType的checked
				$("#purposeType").trigger("change");
				$("#estFlagTr").show();
				$("#laaFlagTr").show();
			} else {
				$("#purposeTypeTr").hide();
				$("#estFlagTr").hide();
				$("input[name='purchaseHouse'][value='N']").prop('checked', true);
				$("#purchaseHouseTr").hide();
				$("#laaFlagTr").hide();
			}
			if (ByType == 'IJ') {
				$("#youngLoanTypeTr").show();
			} else {
				$("#youngLoanTypeTr").hide();
			}
		});

		$("input[name=nowExtend]").change(function() {
			var ByType = $("input[name='nowExtend']:radio:checked").val();
			if (ByType == 'Y') {
				$("#extYear").prop('disabled', false);
			} else {
				$("#extYear").prop('disabled', true);
			}

		});

		$("#applyKind").change(function() {
			var applyKind = $("#applyKind").val();
			if (applyKind == 'E' || applyKind == 'P' || applyKind == 'I' || applyKind == 'J') {
				$("#incomTypeTr").show();
			} else {
				$("#incomType").val('');
				$("#incomTypeTr").hide();
			}
		});
		$("input[name=purposeType]").change(function() {
			var purposeType = $("input[name='purposeType']:radio:checked").val();
			var ByType = $("input[name='ByType']:radio:checked").val();
			if (ByType == 'O') {//其他案件不隱藏[是否為購屋]，並預設[否]
				$("input[name='purchaseHouse'][value='N']").prop('checked', true);
				$("#purchaseHouseTr").show();
			} else {
				if (purposeType == '1') {
					$("input[name='purchaseHouse'][value='Y']").prop('checked', true);
					$("#purchaseHouseTr").show();
				} else {
					//預設為否
					$("input[name='purchaseHouse'][value='N']").prop('checked', true);
					$("#purchaseHouseTr").hide();
				}
			}
		});
		$("#estFlag").change(function() {
			var estFlag = $("#estFlag").val();
			$("input[name='laaFlag'][value='N']").prop('checked', true);//地政士引介固定都先改回預設值[N]
			if (estFlag == 'N') {
				$("#laaFlagTr").hide();
			} else {
				$("#laaFlagTr").show();
			}
		});


		//初始化設定
		$("#estFlag").trigger("change");
		//設定經辦下拉選單(派案-指定簽案行員)
		$.ajax({
			type: "POST",
			handler: "cls1220m10formhandler",
			action: "getMegaEmpInfo"
		}).done(function(responseData) {
			if (responseData.Success) {
				var chgSignMegaEmp = $("#chgSignMegaEmp");
				chgSignMegaEmp.setItems({
					item: responseData.bossListAO,
					space: true,
					format: "{value} {key}"
				});
			}
		});

	});
});
