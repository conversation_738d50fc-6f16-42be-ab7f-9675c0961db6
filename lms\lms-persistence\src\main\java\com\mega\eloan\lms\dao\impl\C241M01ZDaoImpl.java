/* 
 * C241M01ZDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C241M01ZDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C241M01Z;

/** 覆審異動歷程檔 **/
@Repository
public class C241M01ZDaoImpl extends LMSJpaDao<C241M01Z, String>
	implements C241M01ZDao {

	@Override
	public C241M01Z findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C241M01Z> findByIndex1(String branch, String custId, String dupNo){
		ISearch search = createSearchTemplete();
		List<C241M01Z> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C241M01Z> findProc(String reasonFlag, int batch_size){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "reason", reasonFlag);
		search.setMaxResults(batch_size);
		return find(search);
	}
}