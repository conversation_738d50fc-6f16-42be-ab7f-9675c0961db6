/* 
 * LMS140MServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.gwclient.OBSMqGwClient;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.MISRows;
import com.mega.eloan.lms.base.panels.LMSL140M01MPanel;
import com.mega.eloan.lms.base.service.CentralBankControlService;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.base.service.LMS140MService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L140AM1ADao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01MDao;
import com.mega.eloan.lms.dao.L140MM1ADao;
import com.mega.eloan.lms.dao.L140MM1BDao;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.mfaloan.bean.ELF500;
import com.mega.eloan.lms.mfaloan.bean.ELF600;
import com.mega.eloan.lms.mfaloan.service.MisELF500Service;
import com.mega.eloan.lms.mfaloan.service.MisELF517Service;
import com.mega.eloan.lms.mfaloan.service.MisELF600Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.mfaloan.service.impl.MisStoredProcServiceImpl;
import com.mega.eloan.lms.model.L140AM1A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140MM1A;
import com.mega.eloan.lms.model.L140MM1B;
import com.mega.eloan.lms.model.L140S05A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 央行註記異動作業
 * </pre>
 * 
 * @since 2014/08/28
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class LMS140MServiceImpl extends AbstractCapService implements
		LMS140MService {
	private static final Logger logger = LoggerFactory
			.getLogger(LMS140MServiceImpl.class);

	@Resource
	FlowNameService flowNameService;
	@Resource
	TempDataService tempDataService;
	@Resource
	OBSMqGwClient obsMqGwClient;
	@Resource
	FlowService flowService;
	@Resource
	MisELF500Service misELF500Service;
	@Resource
	L140M01MDao l140m01mDao;
	@Resource
	L140AM1ADao l140am1aDao;
	@Resource
	L140MM1ADao l140mm1aDao;
	@Resource
	L140MM1BDao l140mm1bDao;
	@Resource
	DocFileDao docFileDao;
	@Resource
	DocLogService docLogService;
	@Resource
	BranchService branchService;
	@Resource
	UserInfoService userInfoService;
	@Resource
	LMSService lmsService;
	@Resource
	DocFileService docFileService;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	MisdbBASEService misdbBaseService;
	@Resource
	MisdbBASEService misDbService;
	@Resource
	MisELF517Service misElf517Service;
	@Resource
	LMSService lmsservice;
	@Resource
	MisELF600Service misELF600Service;
	
	@Resource
	CentralBankControlService centralBankControlService;
	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;
	@Resource
	MisStoredProcServiceImpl misStoredProcServiceImpl;
	@Resource
	L140M01ADao l140m01aDao;

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L140MM1A.class) {
			return l140mm1aDao.findByMainId(mainId);
		} else if (clazz == L140MM1B.class) {
			return l140mm1bDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L140MM1A) {
					if (Util.isEmpty(((L140MM1A) model).getOid())) {
						((L140MM1A) model).setCreator(user.getUserId());
						((L140MM1A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
						l140mm1aDao.save((L140MM1A) model);

						flowService.start("LMS140MFlow",
								((L140MM1A) model).getOid(), user.getUserId(),
								user.getUnitNo());
						// 新增授權檔
						L140AM1A l140am1a = new L140AM1A();
						l140am1a.setAuthTime(CapDate.getCurrentTimestamp());
						l140am1a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
						l140am1a.setAuthUnit(user.getUnitNo());
						l140am1a.setMainId(((L140MM1A) model).getMainId());
						l140am1a.setOwner(user.getUserId());
						l140am1a.setOwnUnit(user.getUnitNo());
						l140am1aDao.save(l140am1a);

					} else {
						// 當文件狀態為編製中時文件亂碼才變更

						((L140MM1A) model).setUpdater(user.getUserId());
						((L140MM1A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						l140mm1aDao.save((L140MM1A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((L140MM1A) model)
									.getMainId());
							docLogService.record(((L140MM1A) model).getOid(),
									DocLogEnum.SAVE);
						}
					}
				} else if (model instanceof L140MM1B) {
					((L140MM1B) model).setUpdater(user.getUserId());
					((L140MM1B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140mm1bDao.save((L140MM1B) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L140MM1A.class) {
			return l140mm1aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L140MM1A.class) {
			return (T) l140mm1aDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public boolean deleteL140mm1as(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L140MM1A> l140mm1as = new ArrayList<L140MM1A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			L140MM1A l140mm1a = (L140MM1A) findModelByOid(L140MM1A.class,
					oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			l140mm1a.setDeletedTime(CapDate.getCurrentTimestamp());
			l140mm1a.setUpdater(user.getUserId());
			l140mm1as.add(l140mm1a);
			docLogService.record(l140mm1a.getOid(), DocLogEnum.DELETE);
		}
		if (!l140mm1as.isEmpty()) {
			l140mm1aDao.save(l140mm1as);
			flag = true;
		}
		return flag;
	}

	@Override
	public void saveL140mm1bList(List<L140MM1B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L140MM1B l140mm1b : list) {
			l140mm1b.setUpdater(user.getUserId());
			l140mm1b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l140mm1bDao.save(list);
	}

	@Override
	public void flowAction(String mainOid, L140MM1A model, boolean setResult,
			boolean resultType, boolean upMis) throws Throwable {
		// TODO 要補的
		// Properties pop =
		// MessageBundleScriptCreator.getComponentResource(LMS140MM01Page.class);

		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS140MFlow",
						((L140MM1A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// inst.setNextDept("200");
				// inst.setNextUser("nextTest")
				// resultType 控制前進還是後退
				// 當有先行動用的狀態 是到03O 非先行動用表示已完成 到05O
				inst.setAttribute("result", resultType ? "核准" : "退回");
				if (resultType) {
					save((L140MM1A) model);
					if (upMis) {
						L140MM1A l140mm1a = (L140MM1A) findModelByOid(
								L140MM1A.class, mainOid);
						L140M01M l140m01m = lmsService
								.findModelByMainId(L140M01M.class,
										Util.trim(l140mm1a.getMainId()));
						
						String editType = l140mm1a.getEditType();
						
						//新增全新央行註記資料
						if("C".equals(editType)){
							this.insertLNF087(l140mm1a, l140m01m);
						}
						
						//修改央行註記資料
						if("U".equals(editType)){
							
							// 動審表簽章欄檔取得人員職稱
							List<L140MM1B> l140mm1blist = l140mm1bDao
									.findByMainId(l140mm1a.getMainId());
							String apprId = "";
							String reCheckId = "";

							for (L140MM1B l140mm1b : l140mm1blist) {
								String StaffJob = Util.trim(l140mm1b.getStaffJob());// 取得人員職稱
								String StaffNo = Util.trim(l140mm1b.getStaffNo());// 取得行員代碼
								if (Util.equals(StaffJob, "L1")) {// 分行經辦
									apprId = StaffNo;
								} else if (Util.equals(StaffJob, "L4")) {// 分行覆核主管
									reCheckId = StaffNo;
								}
							}
							// 若人員職稱為空值改取c160m01a上的人員資料
							if (Util.isEmpty(apprId)) {
								apprId = l140mm1a.getUpdater();
							}
							if (Util.isEmpty(reCheckId)) {
								reCheckId = l140mm1a.getApprover();
							}

							// 處理企金和個金差別
							if (l140mm1a.getSDate() == null) {
								MISRows<ELF500> misRows500 = new MISRows<ELF500>(
										ELF500.class);
								List<ELF500> ELF500List = new ArrayList<ELF500>();
								ELF500List = upELF500(ELF500List, l140mm1a,
										l140m01m, apprId, reCheckId);

								misRows500.setValues(ELF500List);
								// 上傳DB用
								up_DelThenInsert(misRows500, "MIS");
							} else {
								this.upELF383(l140mm1a, l140m01m, reCheckId);
							}

							// 上傳至 ELF517
							this.uploadELF517(l140mm1a.getCntrNo(),
									l140mm1a.getMainId());
							// 上傳至 ELF600
							ELF600 elf600 = misELF600Service.findByContract(l140mm1a.getCntrNo());
							if(elf600 == null){
								List<ELF600> elf600List = new ArrayList<ELF600>();
								this.lmsService.genELF600ObjectWhenNoRecord(elf600List, l140m01m, l140mm1a.getCntrNo(), l140mm1a.getCustId(), l140mm1a.getDupNo());
								for(ELF600 entity : elf600List){
									logger.info("{}=======>{}", "Start", "misELF600Service.insert|");
									this.misELF600Service.insert(entity);
								}
							}
							else{
								this.uploadELF600(l140m01m.getActStartDate(), l140mm1a.getCntrNo(), user.getUserId(), l140m01m.getMainId(), l140m01m.getFstDate(), l140m01m.getLstDate());
							}
						}
					}
				}
			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	private <T> void up_DelThenInsert(MISRows<T> misRows, String schemaName) {
		if (Util.isNotEmpty(misRows.getKeyValues())) {
			int DelCount = misdbBaseService.delete(
					misRows.getKeyMsgFmtParam(schemaName),
					misRows.getKeyValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Delete:"
					+ DelCount);
			misdbBaseService.insert(misRows.getMsgFmtParam(schemaName),
					misRows.getTypes(), misRows.getValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Insert");
		} else {
			logger.error("{}=======>{}", misRows.getTableNm(), "EMPTY");
		}
	}

	@Override
	public L140MM1B findL140mm1b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		return l140mm1bDao.findByUniqueKey(mainId, branchType, branchId,
				staffNo, staffJob);
	}

	@Override
	public void deleteL140mm1bs(List<L140MM1B> l140mm1bs, boolean isAll) {
		if (isAll) {
			l140mm1bDao.delete(l140mm1bs);
		} else {
			List<L140MM1B> L140MM1BsOld = new ArrayList<L140MM1B>();
			for (L140MM1B l140mm1b : l140mm1bs) {
				String staffJob = l140mm1b.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					L140MM1BsOld.add(l140mm1b);
				}
			}
			l140mm1bDao.delete(L140MM1BsOld);
		}

	}

	@Override
	public void delete(GenericBean... entity) {
		// Auto-generated method stub

	}

	@Override
	public void addL140M01M(L140MM1A l140mm1a, String selectVersion) throws CapException {
		String custId = Util.trim(l140mm1a.getCustId());
		String dupNo = Util.trim(l140mm1a.getDupNo());
		String cntrNo = Util.trim(l140mm1a.getCntrNo());

		L140M01M l140m01m = new L140M01M();
		l140m01m.setMainId(l140mm1a.getMainId());
		l140m01m.setCbcCase("");
		l140m01m.setVersion(selectVersion);//J-111-0297 央行對辦理不動產抵押貸款業務規定版本, 改為可選擇原版或最新版本
		
		ELF600 elf600 = misELF600Service.findByContract(l140mm1a.getCntrNo());
		String isClearLand = this.lmsservice.isClearLandEffective(elf600);
		l140m01m.setIsClearLand(isClearLand);
		// 因應有上解除控管日後, 就不會認定為空地貸款, 但分行還是須維護實際動工日, 所以目前無論本案是否為撥貸未動工興建之空地貸款控管對象是或否皆開放修改
		if (elf600 != null) {
			
			l140m01m.setCtlType(Util.trim(elf600.getElf600_ctltype()));
			l140m01m.setFstDate(elf600.getElf600_fstdate() == null ? null
					: Util.parseDate(Util.trim(elf600.getElf600_fstdate())));
			l140m01m.setLstDate(elf600.getElf600_lstdate() == null ? null
					: Util.parseDate(Util.trim(elf600.getElf600_lstdate())));
			l140m01m.setIsChgStDate("N");
			l140m01m.setIsChgRate("N");
			l140m01m.setActStartDate(elf600.getElf600_act_st_date());
			
			//是否變更預計動工日 = 'Y', 引進相關欄位
			if(elf600.getElf600_cstdate() != null){
				l140m01m.setIsChgStDate("Y");
				l140m01m.setCstDate(elf600.getElf600_cstdate());
				l140m01m.setCstReason(elf600.getElf600_cstreason());
				l140m01m.setAdoptFg(elf600.getElf600_adoptfg());
			}
			
			//是否調整利率 = 'Y', 引進相關欄位
			if(elf600.getElf600_rateadd() != null && elf600.getElf600_rateadd().compareTo(BigDecimal.ZERO) > 0){
				l140m01m.setIsChgRate("Y");
				l140m01m.setRateAdd(elf600.getElf600_rateadd());
				l140m01m.setCustRoa(elf600.getElf600_custroa());
				l140m01m.setRelRoa(elf600.getElf600_relroa());
				l140m01m.setIsLegal(elf600.getElf600_islegal());
			}
		} 
		
		String property = "";
		String quantLoan = "";
		L140M01A l140m01a = this.lmsService.getL140m01aByCntrNoForNew(l140mm1a.getCntrNo());
		if(l140m01a != null){
			property = l140m01a.getProPerty();
			L140S05A l140s05a = this.lmsService.findL140s05aByMainId(l140m01a.getMainId());
			quantLoan = l140s05a != null ? l140s05a.getQuant_loan() : "";
		}
		
		BigDecimal totalTimeVal = this.centralBankControlService.getCMStSumAmtAdj(l140m01a.getMainId());
		if(l140m01m.getTimeVal() == null){
			l140m01m.setTimeVal(totalTimeVal);
		}
		
		L140M01M oldL140m01m = this.l140m01mDao.findByMainId(l140m01a.getMainId());
		String oldProdClass = oldL140m01m == null ? null : oldL140m01m.getProdClass();
		if(oldProdClass != null && StringUtils.isNotBlank(oldProdClass)){
			l140m01m.setProdClass(oldProdClass);
			l140m01m.setLandBuildYN("Y");
		}
		
		if (l140mm1a.getSDate() == null) {
			ELF500 elf500Data = misELF500Service.findByCustIdAndCntrno1(custId,
					dupNo, cntrNo);
			this.creatCLS_L140M01M(elf500Data, l140m01m, cntrNo);
		} else {
			Map<String, Object> srcData = misDbService.selL140M01MForQUOTAPPR1(
					custId, cntrNo,
					Util.trim(TWNDate.toAD(l140mm1a.getSDate())));
			this.creatLMS_L140M01M(srcData, l140m01m, cntrNo, property, quantLoan);
		}
		l140m01mDao.save(l140m01m);
		save(l140mm1a);
	}
	
	@Override
	public void addL140M01MForBrandNewCntrNo(L140MM1A l140mm1a, String selectVersion) throws CapException {

		L140M01M l140m01m = new L140M01M();
		l140m01m.setMainId(l140mm1a.getMainId());
		l140m01m.setCbcCase("");
		l140m01m.setVersion(selectVersion);//J-111-0297 央行對辦理不動產抵押貸款業務規定版本, 改為可選擇原版或最新版本
		l140m01m.setIsClearLand("N");
		l140m01mDao.save(l140m01m);
		save(l140mm1a);
	}

	/**
	 * 異動個金L140M01M
	 * 
	 * @param elf500Data
	 * @param l140m01m
	 * @throws CapException
	 */
	private void creatCLS_L140M01M(ELF500 elf500Data, L140M01M l140m01m,
			String cntrNo) throws CapException {
		if (elf500Data != null) {
			String cbccase = Util.trim(elf500Data.getElf500_control_cd());
			l140m01m.setCbcCase(cbccase); // 央行購住狀態
			if ("4".equals(cbccase)) {
				l140m01m.setPlusReason(elf500Data.getElf500_plus_reason()); // 非央行控管種類
				l140m01m.setPlusReasonMeMo(elf500Data.getElf500_plus_memo()); // 非央行控管種類原因
			}
			l140m01m.setPayPercent(elf500Data.getElf500_ltv_rate()); // 貸款成數
			String areaId = Util.trim(elf500Data.getElf500_location_cd());
			String cityId = "";
			if (Util.isNotEmpty(areaId)) {
				cityId = areaId.substring(0, 1);
			}
			l140m01m.setCityId(cityId); // 縣市代碼
			l140m01m.setAreaId(areaId); // 鄉鎮市區代碼
			l140m01m.setCustYN(elf500Data.getElf500_jcic_mark()); // 借款人聯徵名下有無 -
																	// 用途別為[1]之其他房貸資料
			l140m01m.setAppAmt(Arithmetic.floor(
					Util.parseBigDecimal(elf500Data.getElf500_app_amt()), 0)); // 擔保品購價(台幣元)
			l140m01m.setCommonYN(elf500Data.getElf500_cocoll_fg()); // 有無與其他帳號共用同一擔保品
			l140m01m.setShareCollAmt(Arithmetic.floor(
					Util.parseBigDecimal(elf500Data.getElf500_sum_factamt()), 0)); // 授權外核准得分批撥款「擔保品總貸款額度」
			l140m01m.setShareCollYN(elf500Data.getElf500_part_fund()); // 有無授權外核准得分批撥款「擔保品總貸款額度」
			l140m01m.setBuildYN(elf500Data.getElf500_reg_purpose()); // 建物謄本登記用途是否屬「住」類，或「住商」類等無營業登記
			l140m01m.setNowAMT(Arithmetic.floor(
					Util.parseBigDecimal(elf500Data.getElf500_est_amt()), 0)); // 擔保品鑑價(台幣元)
			l140m01m.setValueAMT(Arithmetic.floor(
					Util.parseBigDecimal(elf500Data.getElf500_lawval()), 0)); // 擔保品估值(台幣元)
			l140m01m.setSit3No(Util.parseBigDecimal(elf500Data
					.getElf500_site3no())); // 擔保品座落-段
			l140m01m.setSit4No(elf500Data.getElf500_site4no()); // 擔保品座落-村
			l140m01m.setHouseOther(elf500Data.getElf500_coll_char_m());
			l140m01m.setHouseYN(Util.trim(elf500Data.getElf500_plan_area())); // 是否屬都市計畫劃定之區域(Y/N)
			l140m01m.setHouseType(Util.trim(elf500Data.getElf500_p_usetype())); // 都市計畫劃定之使用分區
			l140m01m.setPurposeType(Util.trim(elf500Data.getElf500_p_loanuse())); // 借款用途
			l140m01m.setCmsType(Util.trim(elf500Data.getElf500_coll_char())); // 擔保品性質別
			l140m01m.setKeepYN(Util.trim(elf500Data.getElf500_keep_lawval())); // 是否保留一成估值
			l140m01m.setCmsOther(Util.trim(elf500Data.getElf500_coll_char_m())); // 擔保品性質別3其他之說明
			l140m01m.setIsLimitCust(Util.trim(elf500Data.getElf500_restrict())); // 是否為央行受限戶
			l140m01m.setIsHighHouse(Util.trim(elf500Data.getElf500_hp_house())); // 是否為高價住宅(Y/N)
			l140m01m.setHouse_age(elf500Data.getElf500_house_age());
			l140m01m.setLoanAmt(elf500Data.getElf500_loanAmt());
			
			l140m01m.setIsRenew(elf500Data.getElf500_isRenew());
			l140m01m.setIsPayOldQuota(elf500Data.getElf500_isPay_old());
			l140m01m.setOldQuota(elf500Data.getElf500_old_quota());
			l140m01m.setPayOldAmt(elf500Data.getElf500_pay_old_amt());
			l140m01m.setPayOldAmtItem(elf500Data.getElf500_pay_old_item());
			l140m01m.setIsMatchUnsoldHouseItem(elf500Data.getElf500_isMatch_item());
			l140m01m.setIsSaleCase(elf500Data.getElf500_isSale_case());
			l140m01m.setTimeVal(elf500Data.getElf500_timeval());
			
			this.lmsService.setL140m01mValueByELF500(l140m01m, l140m01m.getVersion(), elf500Data.getElf500_hLoanLimit(), elf500Data.getElf500_hLoanLimit_2());

//			List<String> errList = new ArrayList<String>();
//			List<String> showList = new ArrayList<String>();

//			this.lmsService.gotoSetType(Util.trim(l140m01m.getVersion()),
//					l140m01m, Util.trim(elf500Data.getElf500_custid()),
//					errList, showList, false, cntrNo);
		}
	}

	/**
	 * 異動企金L140M01M
	 * 
	 * @param srcData
	 * @param l140m01m
	 * @throws CapException
	 */
	private void creatLMS_L140M01M(Map<String, Object> srcData,
			L140M01M l140m01m, String cntrNo, String property, String quantLoan) throws CapException {
		if (srcData != null) {
			String cbccase = Util.trim(srcData.get("CONTROLCD")); // 央行購住狀態
			l140m01m.setCbcCase(cbccase);
			if ("4".equals(cbccase)) {
				l140m01m.setPlusReason(Util.trim(srcData.get("PLUSREASON"))); // 非央行控管種類
				l140m01m.setPlusReasonMeMo(Util.trim(srcData.get("PLUS_MEMO"))); // 非央行控管種類原因
			}
			l140m01m.setPayPercent(Util.parseBigDecimal(srcData.get("LTVRATE"))); // 貸款成數

			String areaId = Util.trim(Util.trim(srcData.get("LOCATIONCD")));
			String cityId = "";
			if (Util.isNotEmpty(areaId)) {
				cityId = areaId.substring(0, 1);
			}
			l140m01m.setCityId(cityId); // 縣市代碼
			l140m01m.setAreaId(areaId); // 鄉鎮市區代碼
			l140m01m.setCustYN(Util.trim(srcData.get("JCICMARK"))); // 借款人聯徵名下有無
																	// -
																	// 用途別為[1]之其他房貸資料
			l140m01m.setAppAmt(Arithmetic.floor(
					Util.parseBigDecimal(srcData.get("APPAMT")), 0)); // 擔保品購價(台幣元)
			l140m01m.setCommonYN(Util.trim(srcData.get("COCOLL_FG"))); // 有無與其他帳號共用同一擔保品
			l140m01m.setShareCollAmt(Arithmetic.floor(
					Util.parseBigDecimal(srcData.get("SUM_FACTAMT")), 0)); // 授權外核准得分批撥款「擔保品總貸款額度」
			l140m01m.setShareCollYN(Util.trim(srcData.get("PART_FUND"))); // 有無授權外核准得分批撥款「擔保品總貸款額度」
			l140m01m.setBuildYN(Util.trim(srcData.get("REG_PURPOSE"))); // 建物謄本登記用途是否屬「住」類，或「住商」類等無營業登記
			l140m01m.setNowAMT(Arithmetic.floor(
					Util.parseBigDecimal(srcData.get("EST_AMT")), 0)); // 擔保品鑑價(台幣元)
			l140m01m.setValueAMT(Arithmetic.floor(
					Util.parseBigDecimal(srcData.get("LAWVAL")), 0)); // 擔保品估值(台幣元)
			l140m01m.setSit3No(Util.parseBigDecimal(srcData.get("SITE3NO"))); // 擔保品座落-段
			l140m01m.setSit4No(Util.trim(srcData.get("SITE4NO"))); // 擔保品座落-村
			l140m01m.setHouseOther(Util.trim(srcData.get("COLL_CHAR_M")));

			l140m01m.setHouseYN(Util.trim(srcData.get("PLAN_AREA"))); // 是否屬都市計畫劃定之區域(Y/N)
			l140m01m.setHouseType(Util.trim(srcData.get("P_USETYPE"))); // 都市計畫劃定之使用分區
			l140m01m.setPurposeType(Util.trim(srcData.get("P_LOANUSE"))); // 借款用途
			l140m01m.setCmsType(Util.trim(srcData.get("COLL_CHAR"))); // 擔保品性質別
			l140m01m.setKeepYN(Util.trim(srcData.get("KEEP_LAWVAL"))); // 是否保留一成估值
			l140m01m.setCmsOther(Util.trim(srcData.get("COLL_CHAR_M"))); // 是否擔保品性質別-其他
			l140m01m.setIsLimitCust(Util.trim(srcData.get("RESTRICT"))); // 是否為央行受限戶
			l140m01m.setIsHighHouse(Util.trim(srcData.get("HP_HOUSE"))); // 是否為高價住宅(Y/N)
			l140m01m.setRealEstateLoanLimitReason(Util.trim(srcData
					.get("HLOANLIMIT")));// 2020-12-08 央行對辦理不動產抵押貸款業務限制理由
			
			l140m01m.setIsRenew(Util.trim(srcData.get("ISRENEW")));
			l140m01m.setIsPayOldQuota(Util.trim(srcData.get("ISPAY_OLD")));
			l140m01m.setOldQuota(Util.parseBigDecimal(srcData.get("OLD_QUOTA")));
			l140m01m.setPayOldAmt(Util.parseBigDecimal(srcData.get("PAY_OLD_AMT")));
			l140m01m.setPayOldAmtItem(Util.trim(srcData.get("PAY_OLD_ITEM")));
			l140m01m.setIsMatchUnsoldHouseItem(Util.trim(srcData.get("ISMATCH_ITEM")));
			l140m01m.setIsSaleCase(Util.trim(srcData.get("ISSALE_CASE")));
			l140m01m.setTimeVal(Util.parseBigDecimal(srcData.get("TIMEVAL")));
			
			List<String> errList = new ArrayList<String>();
			List<String> showList = new ArrayList<String>();

			this.lmsService.gotoSetType(Util.trim(l140m01m.getVersion()),
					l140m01m, Util.trim(srcData.get("CUSTID")), errList,
					showList, false, cntrNo, property, quantLoan);
		}
	}

	/**
	 * 更新ELF383值
	 */
	private void upELF383(L140MM1A l140mm1a, L140M01M l140m01m, String reCheckId) {

		String custId = Util.trim(l140mm1a.getCustId());
		String cntrNo = Util.trim(l140mm1a.getCntrNo());
		String srcDate = Util.trim(TWNDate.toAD(l140mm1a.getSDate()));

		String SUPVNO = Util.getRightStr(reCheckId, 5);// 主管代號

		String CONTROLCD = Util.trim(l140m01m.getCbcCase());
		BigDecimal LTVRATE = (Util.isEmpty(l140m01m.getPayPercent()) ? new BigDecimal(
				0) : l140m01m.getPayPercent()); // 貸放成數
		String LOCATIONCD = LMSUtil.getUploadLocationCd(Util.trim(l140m01m
				.getAreaId())); // 擔保品坐落區
		String JCICMARK = Util.trim(l140m01m.getCustYN()); // JCIC 已有購屋融資註記 Y/N
		BigDecimal APPAMT = (Util.isEmpty(l140m01m.getAppAmt()) ? new BigDecimal(
				0) : l140m01m.getAppAmt()); // 擔保品購價或時價
		String PLUSREASON = Util.trim(l140m01m.getPlusReason()); // 央行控管種類 4 的理由
		String REG_PURPOSE = Util.trim(l140m01m.getBuildYN()); // 個人:「住」或「住商」無營業註記
																// >> 法人:
																// 建物謄本登記用途是否屬「住」類
																// (Y/N)
		BigDecimal EST_AMT = l140m01m.getNowAMT() == null ? BigDecimal.ZERO : l140m01m.getNowAMT(); // 擔保品鑑價
		BigDecimal LAWVAL = l140m01m.getValueAMT(); // 擔保品估價
		String COCOLL_FG = Util.trim(l140m01m.getCommonYN()); // 有無與其他額度共用擔保品
		String PART_FUND = Util.trim(l140m01m.getShareCollYN()); // 有無授權外核准得分批動用
		BigDecimal SUM_FACTAMT = new BigDecimal(0); // 共用同一擔保品總額度金額
		if ("N".equals(Util.trim(l140m01m.getCommonYN()))
				&& "N".equals(Util.trim(l140m01m.getShareCollYN()))) {
			SUM_FACTAMT = BigDecimal.ZERO;
		} else {
			SUM_FACTAMT = Util.parseBigDecimal(l140m01m.getShareCollAmt());
		}
		String RESTRICT = Util.trim(l140m01m.getIsLimitCust()); // 是否為受限戶(Y/N)
		String HP_HOUSE = Util.trim(l140m01m.getIsHighHouse()); // 是否為受限戶(Y/N)

		String PLAN_AREA = Util.trim(l140m01m.getHouseYN()); // 是否屬都市計畫劃定之區域(Y/N)
		String P_USETYPE = Util.trim(l140m01m.getHouseType()); // 都市計畫劃定之使用分區
		String P_LOANUSE = Util.trim(l140m01m.getPurposeType()); // 借款用途
		String COLL_CHAR = Util.trim(l140m01m.getCmsType()); // 擔保品性質別
		String KEEP_LAWVAL = Util.trim(l140m01m.getKeepYN()); // 保留一成估值(Y/N)
		String PLUS_MEMO = Util.trimSizeInOS390(
				Util.toFullCharString(Util.trim(l140m01m.getPlusReasonMeMo())),
				62); // 非屬央行填報對象理由
		if (!"5".equals(PLUSREASON)) {
			// 非屬央行控管對象者需要選擇一個理由, 當理由不是其它時，要將取得非央行控管種類原因 備註清除
			PLUS_MEMO = "";
		}
		// 將值清除，避免畫面上殘留的值塞到DB
		if ("2".equals(CONTROLCD)) {
			//APPAMT = BigDecimal.ZERO;
			EST_AMT = BigDecimal.ZERO;
		} else if ("4".equals(CONTROLCD)) {
			APPAMT = BigDecimal.ZERO;
			EST_AMT = BigDecimal.ZERO;
			LAWVAL = BigDecimal.ZERO;
		}
		String SITE3NO = l140m01m.getSit3No() == null ? "" : Util
				.addZeroWithValue(l140m01m.getSit3No().intValue(), 4); // 擔保品座落-段
		String SITE4NO = Util.trim(l140m01m.getSit4No()); // 擔保品座落-村
		String COLL_CHAR_M = Util.trimSizeInOS390(
				Util.trim(l140m01m.getCmsOther()), 20); // 擔保品性質別3其他之說明

		String VERSION = l140m01m.getVersion() == null ? "" : l140m01m
				.getVersion();
		String HLOANLIMIT = l140m01m.getRealEstateLoanLimitReason() == null ? ""
				: l140m01m.getRealEstateLoanLimitReason();
		
		String HLOANLIMIT_2 = StringUtils.isNotBlank(l140m01m.getIs3rdHignHouse()) ? l140m01m.getIs3rdHignHouse() : "" ;
		
		Date ENDDATE = this.lmsservice.getApprovalDateByLnf447nForIs3rdHighHouseRule(l140mm1a.getCntrNo());
		
		String ISRENEW = l140m01m.getIsRenew() == null ? "" : l140m01m.getIsRenew();
		String ISPAYOLDQUOTA = l140m01m.getIsPayOldQuota() == null ? "" : l140m01m.getIsPayOldQuota();
		BigDecimal OLDQUOTA = null;// J-111-0534：隨此欄位於畫面上被棄用，已不需放值於DB
		BigDecimal PAYOLDAMT = l140m01m.getPayOldAmt() == null ? BigDecimal.ZERO : l140m01m.getPayOldAmt();
		String PAYOLDAMTITEM = l140m01m.getPayOldAmtItem() == null ? "" : l140m01m.getPayOldAmtItem();
		String ISMATCHUNSOLDHOUSEITEM = l140m01m.getIsMatchUnsoldHouseItem() == null ? "" : l140m01m.getIsMatchUnsoldHouseItem();
		String ISSALECASE = l140m01m.getIsSaleCase() == null ? "" : l140m01m.getIsSaleCase();
		Date lstDate = l140m01m.getCbControlLstDate();
		BigDecimal timeVal = l140m01m.getTimeVal() == null ? BigDecimal.ZERO : l140m01m.getTimeVal();

		misdbBaseService.updateL140M01MForQUOTAPPR(CONTROLCD, LTVRATE,
				LOCATIONCD, JCICMARK, APPAMT, PLUSREASON, REG_PURPOSE, EST_AMT,
				LAWVAL, COCOLL_FG, PART_FUND, SUM_FACTAMT, RESTRICT, HP_HOUSE,
				PLAN_AREA, P_USETYPE, P_LOANUSE, COLL_CHAR, KEEP_LAWVAL,
				PLUS_MEMO, SITE3NO, SITE4NO, COLL_CHAR_M, SUPVNO, VERSION,
				HLOANLIMIT, HLOANLIMIT_2, ENDDATE, ISRENEW, ISPAYOLDQUOTA, 
				OLDQUOTA, PAYOLDAMT, PAYOLDAMTITEM, ISMATCHUNSOLDHOUSEITEM, ISSALECASE, lstDate, timeVal,
				custId, cntrNo, srcDate);
	}

	/**
	 * 組上傳ELF500的值
	 * 
	 * @param data
	 * @param l140mm1a
	 * @param l140m01m
	 * @param apprId
	 * @param reCheckId
	 * @param sDate
	 * @return
	 */
	private List<ELF500> upELF500(List<ELF500> data, L140MM1A l140mm1a,
			L140M01M l140m01m, String apprId, String reCheckId) {

		String custId = Util.trim(l140mm1a.getCustId());
		String dupNo = Util.trim(l140mm1a.getDupNo());
		String cntrNo = Util.trim(l140mm1a.getCntrNo());

		ELF500 elf500Data = misELF500Service.findByCustIdAndCntrno1(custId,
				dupNo, cntrNo);

		// 2013-09-06團貸案上傳MIS.ELF500的ELF500_GRANTNO錯誤問....
		if (!Util.isEmpty(l140m01m)) {

			String ELF500_CUSTID = Util.trim(elf500Data.getElf500_custid());// 客戶統編
			String ELF500_DUPNO = Util.trim(elf500Data.getElf500_dupno());// 重覆序號
			String ELF500_STAFFNO = "";// 職工編號
			String ELF500_CNTRNO = Util.trim(elf500Data.getElf500_cntrno());// 額度序號
			String ELF500_GRANTNO = Util.trim(elf500Data.getElf500_grantno());// 案件審層級
			String ELF500_LNCICL = (Util.trim(elf500Data.getElf500_lncicl()));// 是否循環使用
			String ELF500_SWFT = Util.trim(elf500Data.getElf500_swft());// 現請額度-幣別
			BigDecimal ELF500_FACTAMT = (Util.isEmpty(elf500Data
					.getElf500_factamt()) ? BigDecimal.ZERO : elf500Data
					.getElf500_factamt());// 現請額度-金額
			String ELF500_DOCUMENT_NO = Util.trim(elf500Data
					.getElf500_document_no());
			String ELF500_GRP_CNTRNO = Util.trim(elf500Data
					.getElf500_grp_cntrno());
			String ELF500_NOTICE_TYPE = Util.trim(elf500Data
					.getElf500_notice_type());
			String ELF500_COMPANY_ID = Util.trim(elf500Data
					.getElf500_company_id());
			String ELF500_COMPANY_NM = Util.trim(elf500Data
					.getElf500_company_nm());
			Date ELF500_REGISTER_DT = elf500Data.getElf500_register_dt();
			Date ELF500_APPLY_DATE = elf500Data.getElf500_apply_date();
			String ELF500_ASSIST_FLAG = elf500Data.getElf500_assist_flag();
			String ELF500_COMPANY_AREA = elf500Data.getElf500_company_area();
			String ELF500_POS = elf500Data.getElf500_pos();
			BigDecimal ELF500_CAPITAL_AMT = elf500Data.getElf500_capital_amt();
			int ELF500_YPAY = elf500Data.getElf500_ypay();
			int ELF500_HINCOME = elf500Data.getElf500_hincome();
			int ELF500_OMONEY = elf500Data.getElf500_omoney();
			String ELF500_LLNNO = elf500Data.getElf500_llnno();
			Date ELF500_CNTFROM = elf500Data.getElf500_cntfrom();
			Date ELF500_CNTEND = elf500Data.getElf500_cntend();
			Integer ELF500_LLNMON = elf500Data.getElf500_llnmon();
			String ELF500_LNUSENO = elf500Data.getElf500_lnuseno();
			Date ELF500_USEFMDT = elf500Data.getElf500_usefmdt();
			Date ELF500_USEENDT = elf500Data.getElf500_useendt();
			Integer ELF500_USEFTMN = elf500Data.getElf500_useftmn();
			String ELF500_STATUS = elf500Data.getElf500_status();
			Date ELF500_ALOANTIMES = elf500Data.getElf500_aloantimes();
			String ELF500_EDU = elf500Data.getElf500_edu();
			String ELF500_MARRY = elf500Data.getElf500_marry();
			Integer ELF500_CHILD = elf500Data.getElf500_child();
			//Integer ELF500_SENIORITY = elf500Data.getElf500_seniority();
			BigDecimal ELF500_DRATE = elf500Data.getElf500_drate();
			BigDecimal ELF500_YRATE = elf500Data.getElf500_yrate();
			String ELF500_CREDIT = elf500Data.getElf500_credit();
			String ELF500_ISPERIODFUND = elf500Data.getElf500_isperiodfund();
			String ELF500_BUSI = elf500Data.getElf500_busi();
			String ELF500_INVMBALCURR = elf500Data.getElf500_invmbalcurr();
			BigDecimal ELF500_INVMBALAMT = elf500Data.getElf500_invmbalamt();
			String ELF500_INVOBALCURR = elf500Data.getElf500_invobalcurr();
			BigDecimal ELF500_INVOBALAMT = elf500Data.getElf500_invobalamt();
			String ELF500_BRANCURR = elf500Data.getElf500_brancurr();
			BigDecimal ELF500_BRANAMT = elf500Data.getElf500_branamt();
			String ELF500_CMSSTATUS = elf500Data.getElf500_cmsstatus();
			Date ELF500_EJCICQDATE = elf500Data.getElf500_ejcicqdate();
			BigDecimal ELF500_CINSPENT = elf500Data.getElf500_cinspent();
			String ELF500_COBORFLG = elf500Data.getElf500_coborflg();// 共同借款人註記
			BigDecimal ELF500_DBR22_FACT = elf500Data.getElf500_dbr22_fact(); // DBR22倍規範額度總和
			String ELF500_FACTTYPE = elf500Data.getElf500_facttype(); //
			String ELF500_RELATE = elf500Data.getElf500_relate(); //
			String ELF500_CMPID = elf500Data.getElf500_cmpid();
			String ELF500_LOTNO = elf500Data.getElf500_lotno();
			String Elf500_RISK_AREA = elf500Data.getElf500_risk_area();
			String ELF500_IDENTITY_NO = elf500Data.getElf500_identity_no();
			String ELF500_EDITNO = elf500Data.getElf500_editno();
			String ELF500_REPAYFUND = elf500Data.getElf500_repayfund();
			String ELF500_L56A_FUNC = elf500Data.getElf500_l56a_func();
			String ELF500_EXCEPT = elf500Data.getElf500_except();
			String ELF500_UNSECUREFLA = elf500Data.getElf500_unsecurefla();
			String ELF500_NOTVALID = elf500Data.getElf500_notvalid();
			String ELF500_PROJ_CLASS = elf500Data.getElf500_proj_class();
			String ELF500_ITW_CODE = elf500Data.getElf500_itw_code();
			String ELF500_SIXCOR_CODE = elf500Data.getElf500_sixcor_code();
			String elf500_mega_empno = elf500Data.getElf500_mega_empno();
			String elf500_agnt_no = elf500Data.getElf500_agnt_no();
			String elf500_agnt_chain = elf500Data.getElf500_agnt_chain();
			String elf500_mega_code = elf500Data.getElf500_mega_code();
			String elf500_sub_unitno = elf500Data.getElf500_sub_unitno();
			String elf500_sub_empno = elf500Data.getElf500_sub_empno();
			String elf500_sub_empnm = elf500Data.getElf500_sub_empnm();
			int ELF500_FINCOME = elf500Data.getElf500_fincome();
			String ELF500_SYND_IPFD = elf500Data.getElf500_synd_ipfd();
			// ---------------------------有異動的部份-----------------------------------------
			String ELF500_CONTROL_CD = Util.trim(l140m01m.getCbcCase()); // 央行控管類別
			String ELF500_PLUS_REASON = Util.trim(l140m01m.getPlusReason()); // 央行控管種類
																				// 4
																				// 的理由
			String ELF500_LOCATION_CD = LMSUtil.getUploadLocationCd(Util
					.trim(l140m01m.getAreaId())); // 擔保品坐落區
			BigDecimal ELF500_APP_AMT = (Util.isEmpty(l140m01m.getAppAmt()) ? new BigDecimal(
					0) : l140m01m.getAppAmt()); // 擔保品購價或時價
			String ELF500_JCIC_MARK = Util.trim(l140m01m.getCustYN()); // JCIC
																		// 已有購屋融資註記
																		// Y/N
			String ELF500_COCOLL_FG = Util.trim(l140m01m.getCommonYN()); // 有無與其他額度共用擔保品

			BigDecimal ELF500_SUM_FACTAMT = new BigDecimal(0); // 共用同一擔保品總額度金額
			if ("N".equals(Util.trim(l140m01m.getCommonYN()))
					&& "N".equals(Util.trim(l140m01m.getShareCollYN()))) {
				ELF500_SUM_FACTAMT = BigDecimal.ZERO;
			} else {
				ELF500_SUM_FACTAMT = l140m01m.getShareCollAmt();
			}

			String ELF500_PART_FUND = Util.trim(l140m01m.getShareCollYN()); // 有無授權外核准得分批動用
			String ELF500_PLUS_MEMO = Util.trimSizeInOS390(Util
					.toFullCharString(Util.trim(l140m01m.getPlusReasonMeMo())),
					62); // 非屬央行填報對象理由
			String ELF500_REG_PURPOSE = Util.trim(l140m01m.getBuildYN()); // 個人:「住」或「住商」無營業註記
																			// >>
																			// 法人:
																			// 建物謄本登記用途是否屬「住」類
																			// (Y/N)

			String ELF500_PLAN_AREA = Util.trim(l140m01m.getHouseYN()); // 是否屬都市計畫劃定之區域(Y/N)
			String ELF500_P_USETYPE = Util.trim(l140m01m.getHouseType()); // 都市計畫劃定之使用分區
			String ELF500_P_LOANUSE = Util.trim(l140m01m.getPurposeType()); // 借款用途
			String ELF500_COLL_CHAR = Util.trim(l140m01m.getCmsType()); // 擔保品性質別
			String ELF500_KEEP_LAWVAL = Util.trim(l140m01m.getKeepYN()); // 保留一成估值(Y/N)

			BigDecimal ELF500_EST_AMT = l140m01m.getNowAMT() == null ? BigDecimal.ZERO : l140m01m.getNowAMT(); // 擔保品鑑價
			BigDecimal ELF500_LAWVAL = l140m01m.getValueAMT(); // 擔保品估價
			String ELF500_RESTRICT = Util.trim(l140m01m.getIsLimitCust()); // 是否為受限戶(Y/N)
			String ELF500_HP_HOUSE = Util.trim(l140m01m.getIsHighHouse()); // 是否為高價住宅(Y/N)
			String ELF500_COLL_CHAR_M = Util.trimSizeInOS390(
					Util.trim(l140m01m.getCmsOther()), 20); // 擔保品性質別3其他之說明
			BigDecimal ELF500_SITE3NO = l140m01m.getSit3No(); // 擔保品座落-段
			String ELF500_SITE4NO = Util.trim(l140m01m.getSit4No()); // 擔保品座落-村
			BigDecimal ELF500_LTV_RATE = (Util
					.isEmpty(l140m01m.getPayPercent()) ? new BigDecimal(0)
					: l140m01m.getPayPercent()); // 貸放成數

			if (!"5".equals(ELF500_PLUS_REASON)) {
				// 非屬央行控管對象者需要選擇一個理由, 當理由不是其它時，要將取得非央行控管種類原因 備註清除
				ELF500_PLUS_MEMO = "";
			}

			// 將值清除，避免畫面上殘留的值塞到DB
			if ("2".equals(ELF500_CONTROL_CD)) {
				//ELF500_APP_AMT = BigDecimal.ZERO;
				ELF500_EST_AMT = BigDecimal.ZERO;
			} else if ("4".equals(ELF500_CONTROL_CD)) {
				if (LMSUtil.is_cls_prod_67(l140m01m)) {

				} else {
					ELF500_APP_AMT = BigDecimal.ZERO;
				}
				ELF500_EST_AMT = BigDecimal.ZERO;
				ELF500_LAWVAL = BigDecimal.ZERO;
			}

			Date ELF500_TMESTAMP = CapDate.getCurrentTimestamp();

			String ELF500_TELLER = Util.getRightStr(apprId, 5);// 櫃員代號
			String ELF500_SUPVNO = Util.getRightStr(reCheckId, 5);// 主管代號

			BigDecimal elf500_house_age = BigDecimal.ZERO;
			BigDecimal elf500_loanAmt = BigDecimal.ZERO;
			if (LMSUtil.is_cls_prod_67(l140m01m)) {
				elf500_house_age = (Util.isEmpty(l140m01m.getHouse_age()) ? BigDecimal.ZERO
						: l140m01m.getHouse_age());
				elf500_loanAmt = (Util.isEmpty(l140m01m.getLoanAmt()) ? BigDecimal.ZERO
						: l140m01m.getLoanAmt());
			}

			// 央行109-12-08對辦理不動產抵押貸款業務規定
			String ELF500_VERSION = l140m01m.getVersion() == null ? ""
					: l140m01m.getVersion();// 20201208_ver1
			String ELF500_HLOANLIMIT = l140m01m.getRealEstateLoanLimitReason() == null ? ""
					: l140m01m.getRealEstateLoanLimitReason();
			String ELF500_HLOANLIMIT_2 = l140m01m.getIs3rdHignHouse() == null ? "" : l140m01m.getIs3rdHignHouse();
			Date ELF500_ENDDATE = this.lmsservice.getApprovalDateByLnf447nForIs3rdHighHouseRule(l140mm1a.getCntrNo());
			ELF500_HLOANLIMIT_2 = this.lmsservice.proccessHloanLimitValueForIs3rdHighHouseRule(ELF500_VERSION, ELF500_HLOANLIMIT, ELF500_HLOANLIMIT_2);
			
			String ELF500_ISRENEW = l140m01m.getIsRenew() == null ? "" : l140m01m.getIsRenew();
			String ELF500_ISPAY_OLD = l140m01m.getIsPayOldQuota() == null ? "" : l140m01m.getIsPayOldQuota();
			BigDecimal ELF500_OLD_QUOTA = null;// J-111-0534：隨此欄位於畫面上被棄用，已不需放值於DB
			BigDecimal ELF500_PAY_OLD_AMT = l140m01m.getPayOldAmt() == null ? BigDecimal.ZERO : l140m01m.getPayOldAmt();
			String ELF500_PAY_OLD_ITEM = l140m01m.getPayOldAmtItem() == null ? "" : l140m01m.getPayOldAmtItem();
			String ELF500_ISMATCH_ITEM = l140m01m.getIsMatchUnsoldHouseItem() == null ? "" : l140m01m.getIsMatchUnsoldHouseItem();
			String ELF500_ISSALE_CASE = l140m01m.getIsSaleCase() == null ? "" : l140m01m.getIsSaleCase();
			Date ELF500_LSTDATE = l140m01m.getCbControlLstDate();
			BigDecimal ELF500_TIMEVAL = l140m01m.getTimeVal() == null ? BigDecimal.ZERO : l140m01m.getTimeVal();
			// -------------------------------------------------------------------------

			// 將變數數塞bean
			ELF500 elf500 = new ELF500();
			elf500.setElf500_custid(ELF500_CUSTID);
			elf500.setElf500_dupno(ELF500_DUPNO);
			elf500.setElf500_cntrno(ELF500_CNTRNO);
			elf500.setElf500_grantno(ELF500_GRANTNO);
			elf500.setElf500_lncicl(ELF500_LNCICL);
			elf500.setElf500_swft(ELF500_SWFT);
			elf500.setElf500_factamt(ELF500_FACTAMT);
			elf500.setElf500_facttype(ELF500_FACTTYPE);
			elf500.setElf500_cinspent(ELF500_CINSPENT);
			elf500.setElf500_coborflg(ELF500_COBORFLG);
			elf500.setElf500_llnno(ELF500_LLNNO);
			elf500.setElf500_cntfrom(ELF500_CNTFROM);
			elf500.setElf500_cntend(ELF500_CNTEND);
			elf500.setElf500_llnmon(ELF500_LLNMON);
			elf500.setElf500_lnuseno(ELF500_LNUSENO);
			elf500.setElf500_usefmdt(ELF500_USEFMDT);
			elf500.setElf500_useendt(ELF500_USEENDT);
			elf500.setElf500_useftmn(ELF500_USEFTMN);
			elf500.setElf500_relate(ELF500_RELATE);
			elf500.setElf500_document_no(ELF500_DOCUMENT_NO);
			elf500.setElf500_grp_cntrno(ELF500_GRP_CNTRNO);
			elf500.setElf500_cmpid(ELF500_CMPID);
			elf500.setElf500_lotno(ELF500_LOTNO);
			elf500.setElf500_staffno(ELF500_STAFFNO);
			elf500.setElf500_dbr22_fact(ELF500_DBR22_FACT);
			elf500.setElf500_notice_type(ELF500_NOTICE_TYPE);
			elf500.setElf500_company_id(ELF500_COMPANY_ID);
			elf500.setElf500_company_nm(ELF500_COMPANY_NM);
			elf500.setElf500_company_area(ELF500_COMPANY_AREA);
			elf500.setElf500_register_dt(ELF500_REGISTER_DT);
			elf500.setElf500_apply_date(ELF500_APPLY_DATE);
			elf500.setElf500_capital_amt(ELF500_CAPITAL_AMT);
			elf500.setElf500_assist_flag(ELF500_ASSIST_FLAG);
			elf500.setElf500_control_cd(ELF500_CONTROL_CD);
			elf500.setElf500_plus_reason(ELF500_PLUS_REASON);
			elf500.setElf500_ltv_rate(ELF500_LTV_RATE);
			elf500.setElf500_location_cd(ELF500_LOCATION_CD);
			elf500.setElf500_jcic_mark(ELF500_JCIC_MARK);
			elf500.setElf500_app_amt(ELF500_APP_AMT);
			elf500.setElf500_cocoll_fg(ELF500_COCOLL_FG);
			elf500.setElf500_sum_factamt(ELF500_SUM_FACTAMT);
			elf500.setElf500_part_fund(ELF500_PART_FUND);
			elf500.setElf500_plus_memo(ELF500_PLUS_MEMO);
			elf500.setElf500_reg_purpose(ELF500_REG_PURPOSE);
			elf500.setElf500_eloantimes(CapDate.getCurrentTimestamp());
			elf500.setElf500_aloantimes(ELF500_ALOANTIMES);
			elf500.setElf500_teller(ELF500_TELLER);
			elf500.setElf500_supvno(ELF500_SUPVNO);
			elf500.setElf500_risk_area(Elf500_RISK_AREA);
			elf500.setElf500_identity_no(ELF500_IDENTITY_NO);
			elf500.setElf500_pos(ELF500_POS);
			elf500.setElf500_edu(ELF500_EDU);
			elf500.setElf500_marry(ELF500_MARRY);
			elf500.setElf500_child(ELF500_CHILD);
			elf500.setElf500_seniority(elf500Data.getElf500_seniority());
			elf500.setElf500_ypay(Util.parseInt(ELF500_YPAY));
			elf500.setElf500_hincome(Util.parseInt(ELF500_HINCOME));
			elf500.setElf500_omoney(Util.parseInt(ELF500_OMONEY));
			elf500.setElf500_plan_area(ELF500_PLAN_AREA);
			elf500.setElf500_p_usetype(ELF500_P_USETYPE);
			elf500.setElf500_p_loanuse(ELF500_P_LOANUSE);
			elf500.setElf500_coll_char(ELF500_COLL_CHAR);
			elf500.setElf500_keep_lawval(ELF500_KEEP_LAWVAL);
			elf500.setElf500_est_amt(ELF500_EST_AMT);
			elf500.setElf500_lawval(ELF500_LAWVAL);
			elf500.setElf500_restrict(ELF500_RESTRICT);
			elf500.setElf500_hp_house(ELF500_HP_HOUSE);
			elf500.setElf500_coll_char_m(ELF500_COLL_CHAR_M);
			elf500.setElf500_site3no(Util.parseInt(ELF500_SITE3NO));
			elf500.setElf500_site4no(ELF500_SITE4NO);
			elf500.setElf500_tmestamp(ELF500_TMESTAMP);
			elf500.setElf500_status(ELF500_STATUS);
			elf500.setElf500_drate(ELF500_DRATE);
			elf500.setElf500_yrate(ELF500_YRATE);
			elf500.setElf500_credit(ELF500_CREDIT);
			elf500.setElf500_isperiodfund(ELF500_ISPERIODFUND);
			elf500.setElf500_busi(ELF500_BUSI);
			elf500.setElf500_invmbalcurr(ELF500_INVMBALCURR);
			elf500.setElf500_invmbalamt(ELF500_INVMBALAMT);
			elf500.setElf500_invobalcurr(ELF500_INVOBALCURR);
			elf500.setElf500_invobalamt(ELF500_INVOBALAMT);
			elf500.setElf500_brancurr(ELF500_BRANCURR);
			elf500.setElf500_branamt(ELF500_BRANAMT);
			elf500.setElf500_cmsstatus(ELF500_CMSSTATUS);
			elf500.setElf500_ejcicqdate(ELF500_EJCICQDATE);
			elf500.setElf500_editno(ELF500_EDITNO);
			elf500.setElf500_repayfund(ELF500_REPAYFUND);
			elf500.setElf500_l56a_func(ELF500_L56A_FUNC);
			elf500.setElf500_except(ELF500_EXCEPT);
			elf500.setElf500_unsecurefla(ELF500_UNSECUREFLA);
			elf500.setElf500_notvalid(ELF500_NOTVALID);
			elf500.setElf500_proj_class(ELF500_PROJ_CLASS);
			elf500.setElf500_itw_code(ELF500_ITW_CODE);
			elf500.setElf500_sixcor_code(ELF500_SIXCOR_CODE);
			elf500.setElf500_house_age(elf500_house_age);
			elf500.setElf500_loanAmt(elf500_loanAmt);
			elf500.setElf500_mega_empno(elf500_mega_empno);
			elf500.setElf500_agnt_no(elf500_agnt_no);
			elf500.setElf500_agnt_chain(elf500_agnt_chain);
			elf500.setElf500_mega_code(elf500_mega_code);
			elf500.setElf500_sub_unitno(elf500_sub_unitno);
			elf500.setElf500_sub_empno(elf500_sub_empno);
			elf500.setElf500_sub_empnm(elf500_sub_empnm);
			elf500.setElf500_version(ELF500_VERSION);
			elf500.setElf500_hLoanLimit(ELF500_HLOANLIMIT);
			elf500.setElf500_hLoanLimit_2(ELF500_HLOANLIMIT_2);
			elf500.setElf500_endDate(ELF500_ENDDATE);
			elf500.setElf500_isRenew(ELF500_ISRENEW);
			elf500.setElf500_isPay_old(ELF500_ISPAY_OLD);
			elf500.setElf500_old_quota(ELF500_OLD_QUOTA);
			elf500.setElf500_pay_old_amt(ELF500_PAY_OLD_AMT);
			elf500.setElf500_pay_old_item(ELF500_PAY_OLD_ITEM);
			elf500.setElf500_isMatch_item(ELF500_ISMATCH_ITEM);
			elf500.setElf500_isSale_case(ELF500_ISSALE_CASE);
			elf500.setElf500_lstDate(ELF500_LSTDATE);
			elf500.setElf500_timeval(ELF500_TIMEVAL);
			elf500.setElf500_fincome(ELF500_FINCOME);
			elf500.setElf500_synd_ipfd(ELF500_SYND_IPFD);
			data.add(elf500);
		}
		return data;
	}

	@Override
	public String[] l140m01m_location(String str_city, String str_area,
			BigDecimal str_site3, String str_site4) {
		String str1 = "";
		String str2 = "";
		String str3 = "";

		if (!Util.isEmpty(Util.trim(str_city))
				&& !Util.isEmpty(Util.trim(str_area))) {

			CodeType counties = codeTypeService.findByTypeAndDesc3("counties",
					str_city);

			if (counties != null) {
				str1 = counties.getCodeDesc();

				CodeType counties2 = codeTypeService.findByTypeAndDesc2(
						"counties" + counties.getCodeValue(), str_area);

				if (counties2 != null) {
					str2 = counties2.getCodeDesc();
				}

				str3 = lmsService.l140m01msite3(
						Util.trim(counties.getCodeDesc2()),
						Util.trim(str_area), Util.trim(str_site3),
						Util.trim(str_site4));
			}
		}

		String[] r = new String[3];
		r[0] = str1;
		r[1] = str2;
		r[2] = str3;
		return r;
	}

	@Override
	public void setUnsoldHouseFinancingDataForCentralBankMarkMaintenance(
			Map<String, String> elf517Map, L140M01M l140m01m) {

		String bremainLoanYN = elf517Map.get("bremainLoanYN");
		String bremainLoanClass = elf517Map.get("bremainLoanClass");
		String bremainLoanLocationCity = elf517Map
				.get("bremainLoanLocationCity");
		String bremainLoanLocationCd = elf517Map.get("bremainLoanLocationCd");
		BigDecimal bremainLoanSite3No = LMSUtil.toBigDecimal(elf517Map
				.get("bremainLoanSite3No"));
		String bremainLoanSite4No = elf517Map.get("bremainLoanSite4No");

		// J-110-0195_05097_B1001 Web e-Loan 企金授信額度明細表新增以整批匯入方式填列
		l140m01m.setRemainLoanYN((Util.equals(bremainLoanYN, "X") || Util
				.equals(bremainLoanYN, "")) ? "N" : bremainLoanYN);
		l140m01m.setRemainLoanClass(bremainLoanClass);
		l140m01m.setRemainLoanLocationCity(bremainLoanLocationCity);
		l140m01m.setRemainLoanLocationCd(bremainLoanLocationCd);
		l140m01m.setRemainLoanSite3No(bremainLoanSite3No);
		l140m01m.setRemainLoanSite4No(bremainLoanSite4No);
		l140m01m.setBremainLoanYN(bremainLoanYN);
		l140m01m.setBremainLoanClass(bremainLoanClass);
		l140m01m.setBremainLoanLocationCity(bremainLoanLocationCity);
		l140m01m.setBremainLoanLocationCd(bremainLoanLocationCd);
		l140m01m.setBremainLoanSite3No(bremainLoanSite3No);
		l140m01m.setBremainLoanSite4No(bremainLoanSite4No);

		if (this.lmsservice.isOpenUnsoldHouseLoanInfoFunction()) {

			BigDecimal preFirstLoanUnsoldHouseQuantity = LMSUtil
					.toBigDecimal(elf517Map
							.get("preFirstLoanUnsoldHouseQuantity"));

			if (null != preFirstLoanUnsoldHouseQuantity
					&& preFirstLoanUnsoldHouseQuantity
							.compareTo(BigDecimal.ZERO) > 0) {

				BigDecimal preCurrentUnsoldHouseQuantity = LMSUtil
						.toBigDecimal(elf517Map
								.get("preCurrentUnsoldHouseQuantity"));
				BigDecimal preMonthOfCreditPeriod = LMSUtil
						.toBigDecimal(elf517Map.get("preMonthOfCreditPeriod"));
				Date preFinalCreditPeriodEndDate = elf517Map
						.get("preFinalCreditPeriodEndDate") != null ? CapDate
						.parseDate(elf517Map.get("preFinalCreditPeriodEndDate"))
						: null;

				l140m01m.setFirstLoanUnsoldHouseQuantity(preFirstLoanUnsoldHouseQuantity);
				l140m01m.setCurrentUnsoldHouseQuantity(preCurrentUnsoldHouseQuantity);
				l140m01m.setMonthOfCreditPeriod(preMonthOfCreditPeriod);
				l140m01m.setFinalCreditPeriodEndDate(preFinalCreditPeriodEndDate);
				l140m01m.setPreFirstLoanUnsoldHouseQty(preFirstLoanUnsoldHouseQuantity);
				l140m01m.setPreCurrentUnsoldHouseQuantity(preCurrentUnsoldHouseQuantity);
				l140m01m.setPreMonthOfCreditPeriod(preMonthOfCreditPeriod);
				l140m01m.setPreFinalCreditPeriodEndDate(preFinalCreditPeriodEndDate);
			}
		}
	}

	@Override
	public String checkUnsoldHouseFinancingDataForCentralBankMarkMaintenance(
			L140M01M l140m01m) {

		if (!this.lmsService.isOpenUnsoldHouseLoanInfoFunction()) {
			return "";
		}

		List<String> msgList = new ArrayList<String>();

		BigDecimal preFirstLoanUnsoldHouseQty = l140m01m
				.getPreFirstLoanUnsoldHouseQty();
		boolean isNewCase = preFirstLoanUnsoldHouseQty == null
				|| preFirstLoanUnsoldHouseQty.compareTo(BigDecimal.ZERO) == 0;

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMSL140M01MPanel.class);

		int currentUnsoldHouseQuantity = l140m01m
				.getCurrentUnsoldHouseQuantity() == null ? 0 : l140m01m
				.getCurrentUnsoldHouseQuantity().intValue();
		int firstLoanUnsoldHouseQuantity = l140m01m
				.getFirstLoanUnsoldHouseQuantity() == null ? 0 : l140m01m
				.getFirstLoanUnsoldHouseQuantity().intValue();

		// 新案時，[目前餘屋戶數]應等於[初貸餘屋戶數] J-111-0607 移除此檢核
//		if (isNewCase
//				&& currentUnsoldHouseQuantity != firstLoanUnsoldHouseQuantity) {
//			msgList.add(prop.getProperty("L140M01M.error.message01"));
//		}
		// 舊案時，[目前餘屋戶數]應大於0，並小於等於[初貸餘屋戶數]
		if (!isNewCase
				&& (currentUnsoldHouseQuantity <= 0 || currentUnsoldHouseQuantity > firstLoanUnsoldHouseQuantity)) {
			msgList.add(prop.getProperty("L140M01M.error.message02"));
		}

		int monthOfCreditPeriod = l140m01m.getMonthOfCreditPeriod() == null ? 0
				: l140m01m.getMonthOfCreditPeriod().intValue();

		// 新案時，[本案授信期間年限]應大於等於1年
		if (isNewCase && monthOfCreditPeriod <= 0) {
			msgList.add(prop.getProperty("L140M01M.error.message03"));
		}
		// 舊案時，[本案授信期間年限]不需填寫
		// if(!isNewCase && null != l140m01m.getMonthOfCreditPeriod()){
		// msgList.add(prop.getProperty("L140M01M.error.message04"));
		// }

		Date finalCreditPeriodEndDate = l140m01m.getFinalCreditPeriodEndDate();

		// 舊案時，[本案最終授信期限止日]為必填
		if (!isNewCase && null == finalCreditPeriodEndDate) {
			msgList.add(prop.getProperty("L140M01M.error.message05"));
		}

		// 餘屋貸款授信期限最長不得超過 3 年
		if (isNewCase && monthOfCreditPeriod > 36) {
			msgList.add(prop.getProperty("L140M01M.error.message06"));
		}
 
		if (msgList.isEmpty()) {
			return "";
		}

		msgList.add(0, "<b>[" + prop.getProperty("L140M01M.remainLoanFg")
				+ "]</b>");

		return StringUtils.join(msgList, "<br/>");
	}

	private void uploadELF517(String cntrNo, String l140mm1a_mainId)
			throws CapException {

		L140M01M l140m01m = lmsservice.findModelByMainId(L140M01M.class,
				l140mm1a_mainId);

		if (l140m01m == null) {
			return;
		}

		Map<String, Object> elf517Data = misElf517Service.getByCntrNo(cntrNo);

		boolean hasElf517 = elf517Data != null ? true : false;

		String documentNo = LMSUtil
				.getUploadCaseNoForCentralBankMarkMaintenance();

		this.lmsservice.insertOrUpdateELF517ForUnsoldHouseFinanceingData(
				documentNo, l140m01m, hasElf517, cntrNo);
	}
	
	private void uploadELF600(Date actStartDate, String cntrNo, String updater, String mainId, Date fstDate, Date lstDate) {

		ELF600 elf600 = misELF600Service.findByContract(cntrNo);
		
//		if(elf600 == null){
//			List<ELF600> elf600List = new ArrayList<ELF600>();
//			this.lmsService.genELF600ObjectWhenNoRecord(elf600List, l140m01a);
//			for(ELF600 elf600 : elf600List){
//				logger.info("{}=======>{}", "Start", "misELF600Service.insert|");
//				this.misELF600Service.insert(elf600);
//			}
//		}
//		else{
			
			// J-112-0415 開放維護實際動工日，若擔保品系統該額度序號下所有土地擔保品已無空地時，由實際動工日填入解除控管日
			if(this.centralBankControlService.isRemarkRrmovedt(actStartDate, cntrNo)){
				elf600.setElf600_rrmovedt(actStartDate);
			}
			
			actStartDate = actStartDate != null ? CapDate.parseSQLDate(actStartDate) : null;
			elf600.setElf600_act_st_date(actStartDate);
			elf600.setElf600_contract(cntrNo);
			elf600.setElf600_updfrom("CMM");//建檔維護
			elf600.setElf600_updater(updater);
			elf600.setElf600_universal_id(mainId);
			
			// 金爺說如果ELF600沒有初次核定預計動工日或最新核定(動審)預計動工日，要讓分行維護上傳(僅限有變更預計動工日或調整利率才上傳)
			if (CrsUtil.isNull_or_ZeroDate(elf600.getElf600_fstdate()) && CrsUtil.isNOT_null_and_NOTZeroDate(fstDate)) {
				elf600.setElf600_fstdate(fstDate);
			}

			// 金爺說如果ELF600沒有初次核定預計動工日或最新核定(動審)預計動工日，要讓分行維護上傳(僅限有變更預計動工日或調整利率才上傳)
			if (CrsUtil.isNull_or_ZeroDate(elf600.getElf600_lstdate()) && CrsUtil.isNOT_null_and_NOTZeroDate(lstDate)) {
				elf600.setElf600_lstdate(lstDate);
			}
			
			misELF600Service.updateByContract(elf600);
//		}
	}
	
	private void insertLNF087(L140MM1A l140mm1a, L140M01M l140m01m){
		BigDecimal ltvRate = l140m01m.getPayPercent() == null ? BigDecimal.ZERO : l140m01m.getPayPercent();
		String contract = l140mm1a.getCntrNo();
		String controlCd = l140m01m.getCbcCase();
		String locationCd = LMSUtil.getUploadLocationCd(Util.trim(l140m01m.getAreaId()));
		BigDecimal appAmt = l140m01m.getAppAmt() == null ? BigDecimal.ZERO : l140m01m.getAppAmt();
		String plusReason = Util.trim(l140m01m.getPlusReason());
		String plusMemo = Util.trimSizeInOS390(Util.toFullCharString(Util.trim(l140m01m.getPlusReasonMeMo())), 62);
		
		if (!"5".equals(plusReason)) {
			// 非屬央行控管對象者需要選擇一個理由, 當理由不是其它時，要將取得非央行控管種類原因 備註清除
			plusMemo = "";
		}
		
		String jcicMark = Util.trim(l140m01m.getCustYN());
		String cocollFg = Util.trim(l140m01m.getCommonYN());
		BigDecimal sumFactamt = l140m01m.getShareCollAmt() == null ? BigDecimal.ZERO : l140m01m.getShareCollAmt() ;
		String partFund = Util.trim(l140m01m.getShareCollYN());
		if ("N".equals(cocollFg) && "N".equals(partFund)) {
			sumFactamt = BigDecimal.ZERO;
		}
		
		String regPurpose = Util.trim(l140m01m.getBuildYN());
		BigDecimal estAmt = l140m01m.getNowAMT() == null ? BigDecimal.ZERO : l140m01m.getNowAMT();
		BigDecimal lawval = l140m01m.getValueAMT() == null ? BigDecimal.ZERO : l140m01m.getValueAMT() ; // 本行鑑價(購價與時價取孰低-土增稅-寬限期折舊)【B】
		String restrict = Util.trim(l140m01m.getIsLimitCust());
		String hpHouse = Util.trim(l140m01m.getIsHighHouse());
		String planArea = Util.trim(l140m01m.getHouseYN());
		String pUsetype = Util.trim(l140m01m.getHouseType());
		String pLoanuse = Util.trim(l140m01m.getPurposeType());
		String collChar = Util.trim(l140m01m.getCmsType());
		String keepLawval = Util.trim(l140m01m.getKeepYN());
		BigDecimal site3no = l140m01m.getSit3No() == null ? BigDecimal.ZERO : l140m01m.getSit3No();
		String site4no = Util.trim(l140m01m.getSit4No());
		String collCharM = Util.trimSizeInOS390(Util.trim(l140m01m.getCmsOther()), 20);
		BigDecimal houseAge = BigDecimal.ZERO;
		BigDecimal loanamt = BigDecimal.ZERO;
		String version = Util.trim(l140m01m.getVersion());
		String hloanlimit = Util.trim(l140m01m.getRealEstateLoanLimitReason());
		String hloanlimit2 = this.lmsservice.proccessHloanLimitValueForIs3rdHighHouseRule(version, hloanlimit, Util.trim(l140m01m.getIs3rdHignHouse()));
		Date endDate = CapDate.parseSQLDate(new Date());
		Date lstdate = l140m01m.getCbControlLstDate() == null ? CapDate.parseDate("9999-01-01") : l140m01m.getCbControlLstDate();
		BigDecimal timeval = l140m01m.getTimeVal() == null ? new BigDecimal(-1) : l140m01m.getTimeVal();
		String custId = l140mm1a.getCustId();
		String dupNo = l140mm1a.getDupNo();
		
		// 將值清除，避免畫面上殘留的值塞到DB
		if ("2".equals(controlCd)) {
			//ELF500_APP_AMT = BigDecimal.ZERO;
			estAmt = BigDecimal.ZERO;
		} 
		
		if("4".equals(controlCd)){
			
			if(LMSUtil.is_cls_prod_67(l140m01m)){
				
			}else{
				appAmt = BigDecimal.ZERO;	
			}					
			estAmt = BigDecimal.ZERO;
			lawval = BigDecimal.ZERO;
		}
		
		if(LMSUtil.is_cls_prod_67(l140m01m)){
			houseAge = l140m01m.getHouse_age() == null ? BigDecimal.ZERO : l140m01m.getHouse_age();
			loanamt = l140m01m.getLoanAmt() == null ? BigDecimal.ZERO : l140m01m.getLoanAmt();
		}

		this.misStoredProcServiceImpl.insertLNF087(contract, controlCd, ltvRate, locationCd, appAmt, plusReason, 
													jcicMark, cocollFg, sumFactamt, partFund, plusMemo, regPurpose, 
													estAmt, lawval, restrict, hpHouse, planArea, pUsetype, pLoanuse, 
													collChar, keepLawval, site3no, site4no, collCharM, houseAge, 
													loanamt, version, hloanlimit, hloanlimit2, endDate, lstdate, 
													timeval, custId, dupNo);
	}
	
	@Override
	public String checkContractNoIsCorrect(String cntrNo) {
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMSL140M01MPanel.class);
		
		String pattern = "^[A-Z0-9]{12}";
		if(!cntrNo.matches(pattern)){
			// 額度序號須為12碼英數字
			return prop.getProperty("L140M01M.msg.error.contractNoLengthMustEq12");
		}
		
		String brNo = cntrNo.substring(0, 3);
		if(!StringUtils.isNumeric(brNo) || this.branchService.getBranch(brNo) == null){
			// 額度序號前3碼須為國內分行
			return prop.getProperty("L140M01M.msg.error.contractNoFirst3DigitsMustBeBranchNo");
		}
		
		int the4Digit = Integer.parseInt(cntrNo.substring(3, 4));
		if(the4Digit != 1 && the4Digit != 4){
			// 額度序號第4碼須為1或4
			return prop.getProperty("L140M01M.msg.error.contractNoNumber4DigitMustBe1or4");
		}
		
		boolean isCntrNoExisted = this.centralBankControlService.checkContractNoIsExisted(cntrNo);
		
		if(isCntrNoExisted){
			// 額度序號已存在，不得新增
			return prop.getProperty("L140M01M.msg.error.contractNoAlreadyExisted");
		}

		return "";
	}

}
