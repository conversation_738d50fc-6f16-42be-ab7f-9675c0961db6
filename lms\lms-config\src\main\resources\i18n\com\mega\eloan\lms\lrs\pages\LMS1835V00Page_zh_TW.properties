#==================================================
# \u7522\u751f\u4f01\u91d1\u6236\u65b0\u589e/\u589e\u984d\u540d\u55ae-\u5916Grid
#==================================================
L1835M01a.dataDate=\u8cc7\u6599\u65e5\u671f
L1835M01a.branch=\u5206\u884c\u540d\u7a31
L1835M01a.reportDate=\u5831\u8868\u7522\u751f\u65e5\u671f

#==================================================
# \u7522\u751f\u4f01\u91d1\u6236\u65b0\u589e/\u589e\u984d\u540d\u55ae--thickbox
#==================================================
L1835M01a.title1=\u7522\u751f\u4f01\u91d1\u6236\u65b0\u589e/\u589e\u984d\u540d\u55ae
L1835M01a.title2=\u7522\u751f\u4f01\u91d1\u6236\u65b0\u589e/\u589e\u984d\u540d\u55ae\u67e5\u8a62
L1835M01a.date=\u8acb\u8f38\u5165\u9810\u8a08\u8986\u5be9\u4e4b\u5e74\u6708
L1835M01a.brNo=\u8acb\u9078\u64c7\u6b32\u7522\u751f\u540d\u55ae\u4e4b\u5206\u884c\u4ee3\u865f 
L1835M01a.dataDateForFilter=\u8cc7\u6599\u65e5\u671f
L1835M01a.dataDateForFilter2=\u8cc7\u6599\u65e5\u671f\u5340\u9593
L1835M01a.searchNew=\u6700\u65b0\u8cc7\u6599
L1835M01a.searchOld=\u6b77\u53f2\u8cc7\u6599
L1835v00.startDate=\u8d77\u59cb\u65e5\u671f
L1835v00.endDate=\u8fc4\u81f3\u65e5\u671f
#==================================================
# \u7522\u751f\u4f01\u91d1\u6236\u65b0\u589e/\u589e\u984d\u540d\u55ae--ERROR
#==================================================
L1835v00.error=\u8d77\u59cb\u65e5\u671f\u4e0d\u53ef\u5927\u65bc\u8fc4\u81f3\u65e5\u671f