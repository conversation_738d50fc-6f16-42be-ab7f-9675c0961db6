package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C101S01YDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C101S01Y;

/** 個金地政士名單 **/
@Repository
public class C101S01YDaoImpl extends LMSJpaDao<C101S01Y, String>
        implements C101S01YDao {

    @Override
    public C101S01Y findByOid(String oid) {
        ISearch search = createSearchTemplete();
        search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
        return findUniqueOrNone(search);
    }

    @Override
    public List<C101S01Y> findByList(String mainId, String custId, String dupNo) {

        ISearch search = createSearchTemplete();
        if (mainId != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        }
        if (custId != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
        }
        if (dupNo != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
        }
        search.setMaxResults(Integer.MAX_VALUE);
        List<C101S01Y> list = createQuery(search).getResultList();
        return list;
    }

    @Override
    public C101S01Y findLaaByList(String mainId, String custId, String dupNo, String laaYear, String laaWord, String laaNo) {
        ISearch search = createSearchTemplete();
        if (mainId != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
            if (custId != null) {
                search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
            }
            if (dupNo != null) {
                search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
            }
            if (laaYear != null) {
                search.addSearchModeParameters(SearchMode.EQUALS, "laaYear", laaYear);
            }
            if (laaWord != null) {
                search.addSearchModeParameters(SearchMode.EQUALS, "laaWord", laaWord);
            }
            if (laaNo != null) {
                search.addSearchModeParameters(SearchMode.EQUALS, "laaNo", laaNo);
            }
        }
        return findUniqueOrNone(search);
    }
    
    @Override
	public List<C101S01Y> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<C101S01Y> list = createQuery(C101S01Y.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<C101S01Y> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
        if (mainId != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        }
        search.setMaxResults(Integer.MAX_VALUE);
        List<C101S01Y> list = createQuery(search).getResultList();
        return list;
	}

	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C101S01Y.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
}