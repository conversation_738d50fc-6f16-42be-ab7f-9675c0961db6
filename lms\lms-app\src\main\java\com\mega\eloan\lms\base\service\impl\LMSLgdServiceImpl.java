/* 
 * MicroEntServiceImpl.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * ustu
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.security.KeyStore;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.scheme.PlainSocketFactory;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.conn.tsccm.ThreadSafeClientConnManager;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.http.util.EntityUtils;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.gwclient.RPAHttpSSLSocketFactory;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.LgdConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.constants.UtilConstants.Casedoc;
import com.mega.eloan.lms.base.pages.LMSLgdCommomPage;
import com.mega.eloan.lms.base.service.LMSLgdService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01BDao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L120S01CDao;
import com.mega.eloan.lms.dao.L120S20ADao;
import com.mega.eloan.lms.dao.L120S20BDao;
import com.mega.eloan.lms.dao.L120S21ADao;
import com.mega.eloan.lms.dao.L120S21BDao;
import com.mega.eloan.lms.dao.L120S21CDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01ODao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.impl.EloandbBASEServiceImpl;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisLMS338NService;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S20A;
import com.mega.eloan.lms.model.L120S20B;
import com.mega.eloan.lms.model.L120S21A;
import com.mega.eloan.lms.model.L120S21B;
import com.mega.eloan.lms.model.L120S21C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * J-110-0986_05097_B1001 於簽報書新增LGD欄位
 * </pre>
 * 
 * @since 2019/09
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/09,009301,new
 *          </ul>
 */
@Service("LMSLgdService")
public class LMSLgdServiceImpl extends AbstractCapService implements
		LMSLgdService {

	protected final Logger logger = LoggerFactory.getLogger(getClass());

	@Resource
	L120S20ADao l120s20aDao;

	@Resource
	L120S20BDao l120s20bDao;

	@Resource
	L120S21ADao l120s21aDao;

	@Resource
	L120S21BDao l120s21bDao;
	@Resource
	L120S21CDao l120s21cDao;

	@Resource
	LMSService lmsService;

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	BranchService branchService;

	@Resource
	SysParameterService sysParamService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L140M01ODao l140m01oDao;

	@Resource
	C120S01ADao c120s01aDao;

	@Resource
	L120S01BDao l120s01bDao;

	@Resource
	L120S01CDao l120s01cDao;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisMISLN20Service misMisLN20Service;

	@Resource
	MisELLNGTEEService misEllngteeService;

	@Resource
	DwdbBASEService dwdbBaseService;

	@Resource
	L120M01BDao l120m01bDao;

	@Resource
	MisLMS338NService misLMS338NService;

	private static final Logger LOGGER = LoggerFactory
			.getLogger(EloandbBASEServiceImpl.class);

	@Override
	public void save(GenericBean... entity) {
		// 進行無限多筆儲存
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L120S20A) {
					l120s20aDao.save((L120S20A) model);

				} else if (model instanceof L120S20B) {
					l120s20bDao.save((L120S20B) model);

				} else if (model instanceof L120S21A) {
					((L120S21A) model).setUpdater(user.getUserId());
					((L120S21A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120s21aDao.save((L120S21A) model);
				} else if (model instanceof L120S21B) {
					((L120S21B) model).setUpdater(user.getUserId());
					((L120S21B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120s21bDao.save((L120S21B) model);
				} else if (model instanceof L120S21C) {
					((L120S21C) model).setUpdater(user.getUserId());
					((L120S21C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120s21cDao.save((L120S21C) model);
				}

			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L120S21A.class) {
			// J-110-0382 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
			return l120s21aDao.findPage(search);
		} else if (clazz == L120S21B.class) {
			// J-110-0382 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
			return l120s21bDao.findPage(search);
		} else if (clazz == L120S21C.class) {
			// J-110-0382 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
			return l120s21cDao.findPage(search);
		}
		return null;
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L120S20A) {
					l120s20aDao.delete((L120S20A) model);
				} else if (model instanceof L120S20B) {
					l120s20bDao.delete((L120S20B) model);
				} else if (model instanceof L120S21A) {
					l120s21aDao.delete((L120S21A) model);
				} else if (model instanceof L120S21B) {
					l120s21bDao.delete((L120S21B) model);
				} else if (model instanceof L120S21C) {
					l120s21cDao.delete((L120S21C) model);
				}
			}
		}
	}

	@SuppressWarnings({ "rawtypes" })
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L120S20A.class) {
			return l120s20aDao.findByMainId(mainId);
		} else if (clazz == L120S20B.class) {
			return l120s20bDao.findByMainId(mainId);
		} else if (clazz == L120S21A.class) {
			return l120s21aDao.findByMainId(mainId);
		} else if (clazz == L120S21B.class) {
			return l120s21bDao.findByMainId(mainId);
		} else if (clazz == L120S21C.class) {
			return l120s21cDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void deleteListL120s20a(List<L120S20A> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S20A model : list) {
			listOid.add(model.getOid());
		}
		l120s20aDao.delete(list);
	}

	@Override
	public void deleteListL120s20b(List<L120S20B> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S20B model : list) {
			listOid.add(model.getOid());
		}
		l120s20bDao.delete(list);
	}

	@Override
	public List<L120S20A> findL120s20aByMainId(String mainId) {
		return l120s20aDao.findByMainId(mainId);
	}

	@Override
	public List<L120S20A> findL120s20aByMainIdCntrNoCo(String mainId,
			String cntrNoCo) {
		return l120s20aDao.findByIndex02(mainId, cntrNoCo);
	}

	@Override
	public List<L120S20A> findL120s20aByMainIdCntrNoCoAndCntrNo(String mainId,
			String cntrNoCo, String cntrNo) {
		return l120s20aDao.findByIndex03(mainId, cntrNoCo, cntrNo);
	}

	@Override
	public List<L120S20A> findL120s20aByMainIdCntrNo(String mainId,
			String cntrNo) {
		return l120s20aDao.findByCntrNo(mainId, cntrNo);
	}

	@Override
	public List<Object[]> findL120s20aMinAllocate(String mainId) {
		return l120s20aDao.findMinAllocate(mainId);
	}

	@Override
	public List<L120S20B> findL120s20bByMainId(String mainId) {
		return l120s20bDao.findByMainId(mainId);
	}

	@Override
	public L120S20B findL120s20bByMainIdCntrNo(String mainId, String cntrNo) {
		return l120s20bDao.findByIndex02(mainId, cntrNo);
	}

	/**
	 * J-110-0382 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
	 */
	@Override
	public List<L120S21A> findL120s21aByMainIdAndCntrNo(String mainId,
			String cntrNo) {
		return l120s21aDao.findByMainIdAndCntrNo(mainId, cntrNo);
	}

	@Override
	public List<L120S21A> findL120s21aByMainIdAndCntrNoCo(String mainId,
			String cntrNoCo) {
		return l120s21aDao.findByIndex02(mainId, cntrNoCo);
	}

	@Override
	public List<L120S21A> findL120s21aByMainIdAndCntrNoCoAndCntrNo(
			String mainId, String cntrNoCo, String cntrNo) {
		return l120s21aDao.findByIndex03(mainId, cntrNoCo, cntrNo);
	}

	@Override
	public void saveL120s21aList(List<L120S21A> list) {
		if (!list.isEmpty()) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			for (L120S21A model : list) {
				model.setUpdater(user.getUserId());
				model.setUpdateTime(CapDate.getCurrentTimestamp());
			}
			l120s21aDao.save(list);
		}
	}

	@Override
	public L120S21A findL120s21aByOid(String oid) {
		// 透過Oid取得資料
		return l120s21aDao.findByOid(oid);
	}

	@Override
	public List<L120S21A> findL120s21aByMainId(String mainId) {
		return l120s21aDao.findByMainId(mainId);
	}

	@Override
	public List<Object[]> findL120s21aMinAllocate(String mainId) {
		return l120s21aDao.findMinAllocate(mainId);
	}

	@Override
	public void deleteListL120s21a(List<L120S21A> list) {
		l120s21aDao.delete(list);
	}

	@Override
	public void deleteListL120s21aByOid(String[] oidArray) {
		// 刪除多筆資料
		for (String oid : oidArray) {
			L120S21A model = l120s21aDao.findByOid(oid);
			l120s21aDao.delete(model);
		}
	}

	@Override
	public L120S21B findL120s21bByMainIdAndCntrNo(String mainId, String cntrNo) {
		return l120s21bDao.findByIndex02(mainId, cntrNo);
	}

	@Override
	public L120S21B findL120s21bByOid(String oid) {
		// 透過Oid取得資料
		return l120s21bDao.findByOid(oid);
	}

	@Override
	public List<L120S21B> findL120s21bByMainId(String mainId) {
		return l120s21bDao.findByMainId(mainId);
	}

	@Override
	public List<L120S21B> findL120s21bByCustId(String mainId, String custId,
			String dupNo) {
		return l120s21bDao.findByIndex03(mainId, custId, dupNo);
	}

	@Override
	public void deleteListL120s21b(List<L120S21B> list) {
		l120s21bDao.delete(list);
	}

	@Override
	public L120S21C findL120s21cByOid(String oid) {
		// 透過Oid取得資料
		return l120s21cDao.findByOid(oid);
	}

	@Override
	public List<L120S21C> findL120s21cByMainId(String mainId) {
		return l120s21cDao.findByMainId(mainId);
	}

	@Override
	public void deleteListL120s21c(List<L120S21C> list) {
		l120s21cDao.delete(list);
	}

	@Override
	public void deleteListL120s21cByOid(String[] oidArray) {
		// 刪除多筆資料
		for (String oid : oidArray) {
			L120S21C model = l120s21cDao.findByOid(oid);
			l120s21cDao.delete(model);
		}
	}

	@Override
	public List<L120S21C> findL120s21cByMainIdAndCollType(String mainId,
			String cntrNo_s21c, String collType_s21c) {
		return l120s21cDao.findByIndex03(mainId, cntrNo_s21c, collType_s21c);
	}

	@Override
	public List<L120S21C> findL120s21cByMainIdAndCollKind(String mainId,
			String cntrNo_s21c, String colKind_s21c) {
		return l120s21cDao.findByColKind(mainId, cntrNo_s21c, colKind_s21c);
	}

	@Override
	public List<L120S21B> findL120s21bByMainIdCustIdAndBussType(String mainId,
			String custId_s21b, String dupNo_s21b, String bussType_s21b) {
		return l120s21bDao.findByCustIdAndBussType(mainId, custId_s21b,
				dupNo_s21b, bussType_s21b);
	}

	/**
	 * 取得額度明細表LGD業務種類
	 * 
	 * J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	 * 
	 * @param l140m01a
	 * @return
	 */
	@Override
	public String getCntrBussType(L140M01A l140m01a) {
		String bussType = "";

		// 01.授信業務
		// 02.外匯業務
		// 03.一般應收帳款承購業務
		// 04.EDI預約付款應收帳款承購業務
		// 05.衍生性金融商品交易業務
		// 99.其他

		// 外匯業務
		String[] forType2 = new String[] { "941", "944", "942", "950", "715" }; // 941-進口押匯
																				// 、9411-
																				// 應收信用狀款項－進口押匯
																				// 、942-出口押匯,715-保兌,944-應收信用狀款項－即期

		// 應收帳款
		String[] forType3 = new String[] { "947", "949" }; // 無追索權 947 949

		// 供應鏈融資
		String[] forType4_1 = new String[] { "Z03", "Z08" }; // 買方:
		String[] forType4_2 = new String[] { "111", "211" }; // 買方:

		// "Z03",
		// "Z08"
		// 賣方:限周轉"111",
		// "211"

		String item = "";
		Set<L140M01C> l140m01cs = l140m01a.getL140m01c();
		if (l140m01cs != null) {
			for (L140M01C l140m01c : l140m01cs) {
				// 科目的驗證是抓前三碼
				item = Util.truncateString(l140m01c.getLoanTP(), 3);

				List<String> asList2 = Arrays.asList(forType2);
				if (asList2.contains(item)) {
					bussType = "02"; // 02.外匯業務;
					break;
				}

				List<String> asList3 = Arrays.asList(forType3);
				if (asList3.contains(item)) {
					bussType = "03"; // 03.應收帳款
					break;
				}

				List<String> asList4_1 = Arrays.asList(forType4_1);
				if (asList4_1.contains(item)) {
					bussType = "04"; // 04.供應鏈融資;
					break;
				}

				List<String> asList4_2 = Arrays.asList(forType4_2);
				if (asList4_2.contains(item)) {
					if (Util.equals(Util.trim(l140m01a.getIsEfin()), "Y")) {
						// 供應鏈融資賣放限週轉科目 111 211
						bussType = "04"; // 04.供應鏈融資;
						break;
					}
				}

				String dervKind = lmsService.getDerivateSubjectKind(l140m01c
						.getLoanTP());
				if (Util.equals(dervKind, "04") || Util.equals(dervKind, "05")) {
					// elf447nClass =
					// UtilConstants.UploadType.DBELF447N_CLASS.遠匯;
					bussType = "05"; // 05.衍生性金融商品
					break;
				}
				if (Util.equals(dervKind, "01") || Util.equals(dervKind, "02")
						|| Util.equals(dervKind, "03")) {
					// elf447nClass =
					// UtilConstants.UploadType.DBELF447N_CLASS.衍生性金融商品;
					bussType = "05"; // 05.衍生性金融商品
					break;
				}

			}
		}

		if (Util.isEmpty(bussType)) {

			if (l140m01cs != null) {
				for (L140M01C l140m01c : l140m01cs) {
					// 科目的驗證是抓前三碼
					item = Util.truncateString(l140m01c.getLoanTP(), 3);

					if (Util.equals(Util.truncateString(item, 1), "1")
							|| Util.equals(Util.truncateString(item, 1), "2")
							|| Util.equals(Util.truncateString(item, 1), "3")
							|| Util.equals(Util.truncateString(item, 1), "4")
							|| Util.equals(Util.truncateString(item, 1), "5")
							|| Util.equals(Util.truncateString(item, 1), "6")
							|| Util.equals(Util.truncateString(item, 1), "7")
							|| Util.equals(Util.truncateString(item, 1), "8")) {
						bussType = "01"; // 01.授信
						break;
					}
				}
			}

		}

		// J-110-0485_05097_B1005 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		// J-111-0083_05097_B1002 Web
		// e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		if (!Util.isEmpty(l140m01a.getIsStandAloneAuth())
				&& Util.notEquals(Util.trim(l140m01a.getIsStandAloneAuth()),
						"0")) {
			// J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
			// bussType = "06"; // 06.屬本行授信業務授權準則得單獨劃分之業務
		}

		if (Util.isEmpty(bussType)) {
			bussType = "99"; // 99.其他
		}

		return bussType;

	}

	/**
	 * 取得額度明細表LGD業務種類Map型態
	 * 
	 * J-111-0461_05097_B1001 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
	 * 
	 * @param l140m01a
	 * @return
	 */
	@Override
	public Map<String, String> getCntrBussTypeMap(L140M01A l140m01a) {

		Map<String, String> bussTypeMap = new HashMap<String, String>();

		Map<String, String> chkMap = new HashMap<String, String>();

		// [上午 10:39] 黃建霖(資訊處,高級專員)
		// LGD授權計算科目我整理了一下
		// 目前有計算LGD額度授權
		// 1,9,A,B,C,E,H
		// 同時有多個業務別的時候，只要其中有一個屬於要算的，就屬於要納入授權額度合計的業務
		//
		// 3.衍生性金融商品:Z09,Z10,Z11, Z16
		// 4.交換票據抵用:Z15, 1022
		// 7.進口:941, 944
		// 8.出口:942
		// 9.同業交易:950
		// A.保兌:715
		// B.承兌:701, 702, 801, 802
		// C.應收帳款賣方(有追):114, 115
		// D.應收帳款賣方(無追):912, 913, 947, 949
		// 供應鏈融資(額度明細表isEfin為Y)
		// E.供應鏈融資-賣方:111, 211
		// F.供應鏈融資-買方:Z03, Z08
		// G.貼現:201
		// H.預約付款－應收帳款承購（有追索權）:Z04
		// X.遠匯:961,9611,962,963,964,Z12,Z13,Z14
		// Z.其他:Z01, Z02, Z05, Z06, Z07,910,971
		// ********************************************
		// 非以上科目，且科目第一碼介於1~8
		// 1.授信

		// 1. 授信 ( 預設 )
		// 3. 衍生性金融商品
		// 4. 交換票據抵用
		// 5. 整批團貸
		// 7. 進口(941、944)
		// 8. 出口(942)
		// 9. 同業交易(950.同業交易)
		// A. 保兌(715)
		// B. 承兌(701/702/801/802)
		// C. 應收帳款賣方(有追)
		// D. 應收帳款賣方(無追)
		// E. 供應鏈融資-賣方
		// F. 供應鏈融資-買方
		// G. 貼現(201)
		// X. 遠匯換匯
		// Z. 其他

		// lms1405m01_SubItem

		// 應收帳款-賣方
		// 114 12105001 短放-應收帳款國內 61 62 63 Y AJ
		// 115 12105002 短放-應收帳款出口 61 62 63 Y AK
		// 947 14815802 應收款-國內無追索權 61 62 63 N AI
		// 949 14815902 應收款-出口無追索權 61 62 63 N AG
		// String[] forType2 = new String[] { "912", "913", "114", "115", "214",
		// "215", "947", "949" }; // 有追索權 114 115 214 215 無追索權 947 949

		// [上午 10:53] 金至忠(授信審查處,襄理)
		// 授信業務之授權額度計算的業務範圍為1,9,A,B,C,H,E,Z04, ，另外，D.應收帳款賣方(無追):912, 913, 947,
		// 949是同進出口等單獨另外授權的!

		// 交換票據抵用
		String[] forType4 = new String[] { "Z15", "1022" };

		// 供應鏈融資
		String[] forType6 = new String[] { "Z03", "Z08", "111", "211" };

		String[] forType7 = new String[] { "941", "944" }; // 進口(941、944)
		String[] forType8 = new String[] { "942" }; // 出口(942)
		String[] forType9 = new String[] { "950" }; // 同業交易(950)

		String[] forTypeA = new String[] { "715" }; // 保兌
		String[] forTypeB = new String[] { "701", "702", "801", "802" }; // 承兌

		String[] forTypeC = new String[] { "114", "115" }; // 應收帳款賣方(有追)
		String[] forTypeD = new String[] { "912", "913", "947", "949" }; // 應收帳款賣方(無追)
		String[] forTypeE = new String[] { "111", "211" }; // 供應鏈融資-賣方
		String[] forTypeF = new String[] { "Z03", "Z08" }; // 供應鏈融資-買方

		// "Z03",
		// "Z08"
		// 賣方:限周轉"111",
		// "211"

		// 衍生性金融商品
		// String[] forType3 = new String[] { "Z09", "Z10", "Z11" };

		String[] forTypeG = new String[] { "201" }; // 貼現

		// 「EDI預約付款之應收帳款承購業務買、賣方授權額度」之賣方有追索應收帳款承購業務。
		String[] forTypeH = new String[] { "Z04" };

		// 其他
		// 910.應收代收款－應收帳款
		// 971.移轉(非)融資性風險參與
		// 這兩個也沒算(因為風控不用計算LGD)

		String[] forTypeZ = new String[] { "Z01", "Z02", "Z05", "Z06", "Z07",
				"910", "971" };

		// 遠匯、換匯
		// String[] forTypeX = new String[] { "961", "Z12", "Z13", "Z14",
		// "9611",
		// "962", "963", "964", "950", "971" };

		// 額外排除科目
		String LMS_LGD_AUTH_TOTAL_EX_LOANTP = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_AUTH_TOTAL_EX_LOANTP"));
		String[] exSubjectArr = null;
		if (Util.notEquals(LMS_LGD_AUTH_TOTAL_EX_LOANTP, "")) {
			exSubjectArr = LMS_LGD_AUTH_TOTAL_EX_LOANTP.split(",");
		}

		// 額外包含科目
		String LMS_LGD_AUTH_TOTAL_IN_LOANTP = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_AUTH_TOTAL_IN_LOANTP"));
		String[] inSubjectArr = null;
		if (Util.notEquals(LMS_LGD_AUTH_TOTAL_IN_LOANTP, "")) {
			inSubjectArr = LMS_LGD_AUTH_TOTAL_IN_LOANTP.split(",");
		}

		String item = "";

		List<String> asList4 = Arrays.asList(forType4);
		List<String> asList7 = Arrays.asList(forType7);
		List<String> asList8 = Arrays.asList(forType8);
		List<String> asList9 = Arrays.asList(forType9);
		List<String> asListA = Arrays.asList(forTypeA);
		List<String> asListB = Arrays.asList(forTypeB);
		List<String> asListC = Arrays.asList(forTypeC);
		List<String> asListD = Arrays.asList(forTypeD);
		List<String> asListE = Arrays.asList(forTypeE);
		List<String> asListF = Arrays.asList(forTypeF);
		List<String> asListG = Arrays.asList(forTypeG);
		List<String> asListH = Arrays.asList(forTypeH);
		List<String> asListZ = Arrays.asList(forTypeZ);
		List<String> exSubjectList = exSubjectArr == null ? null : Arrays
				.asList(exSubjectArr);
		List<String> inSubjectList = inSubjectArr == null ? null : Arrays
				.asList(inSubjectArr);

		Set<L140M01C> l140m01cs = l140m01a.getL140m01c();
		if (l140m01cs != null) {

			// 整理科目**********************************************************
			HashMap<String, String> itemMap = new HashMap<String, String>();
			for (L140M01C l140m01c : l140m01cs) {
				item = Util.truncateString(l140m01c.getLoanTP(), 3);
				if (exSubjectList != null) {
					if (exSubjectList.contains(item)) {
						// 要排除計算的科目
						continue;
					}
				}
				itemMap.put(l140m01c.getLoanTP(), l140m01c.getLoanTP());
			}

			// 判斷科目*******************************************************
			for (String fitem : itemMap.keySet()) {
				// 科目的驗證是抓前三碼
				fitem = Util.trim(fitem);
				item = Util.truncateString(Util.trim(fitem), 3);

				// 要用四碼科目來判斷
				if (asList4.contains(Util.trim(fitem))) {
					bussTypeMap.put(LgdConstants.bussType.交換票據抵用,
							LgdConstants.bussType.交換票據抵用);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));

				}

				if (asList7.contains(item)) {
					bussTypeMap.put(LgdConstants.bussType.進口,
							LgdConstants.bussType.進口);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));
				}

				if (asList8.contains(item)) {
					bussTypeMap.put(LgdConstants.bussType.出口,
							LgdConstants.bussType.出口);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));

				}

				if (asList9.contains(item)) {
					bussTypeMap.put(LgdConstants.bussType.同業交易,
							LgdConstants.bussType.同業交易);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));

				}

				if (asListA.contains(item)) {
					bussTypeMap.put(LgdConstants.bussType.保兌,
							LgdConstants.bussType.保兌);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));

				}

				if (asListB.contains(item)) {
					bussTypeMap.put(LgdConstants.bussType.承兌,
							LgdConstants.bussType.承兌);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));

				}

				if (asListC.contains(item)) {
					bussTypeMap.put(LgdConstants.bussType.應收帳款賣方_有追,
							LgdConstants.bussType.應收帳款賣方_有追);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));
				}

				if (asListD.contains(item)) {

					bussTypeMap.put(LgdConstants.bussType.應收帳款賣方_無追,
							LgdConstants.bussType.應收帳款賣方_無追);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));
				}

				if (Util.equals(Util.trim(l140m01a.getSnoKind()), "60")
						|| Util.equals(Util.trim(l140m01a.getSnoKind()), "61")
						|| Util.equals(Util.trim(l140m01a.getSnoKind()), "62")) {
					// 應收帳款及供應鏈融資
					if (Util.equals(Util.trim(l140m01a.getIsEfin()), "Y")) {
						// 是否為供應鏈融資-預約付款使用(非客票融資)
						// 供應鏈融資

						if (asListE.contains(item)) {
							bussTypeMap.put(LgdConstants.bussType.供應鏈融資_賣方,
									LgdConstants.bussType.供應鏈融資_賣方);
							chkMap.put(Util.trim(fitem), Util.trim(fitem));
						} else {
							bussTypeMap.put(LgdConstants.bussType.供應鏈融資_買方,
									LgdConstants.bussType.供應鏈融資_買方);
							chkMap.put(Util.trim(fitem), Util.trim(fitem));
						}
					}

				}

				if (asListG.contains(item)) {
					bussTypeMap.put(LgdConstants.bussType.貼現,
							LgdConstants.bussType.貼現);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));
				}

				String dervKind = lmsService.getDerivateSubjectKind(fitem);
				if (Util.equals(dervKind, "04") || Util.equals(dervKind, "05")) {
					bussTypeMap.put(LgdConstants.bussType.遠匯,
							LgdConstants.bussType.遠匯);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));
				}
				if (Util.equals(dervKind, "01") || Util.equals(dervKind, "02")
						|| Util.equals(dervKind, "03")) {
					bussTypeMap.put(LgdConstants.bussType.衍生性金融商品,
							LgdConstants.bussType.衍生性金融商品);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));
				}

				if (asListH.contains(item)) {
					bussTypeMap.put(LgdConstants.bussType.預約付款應收帳款承購_有追索權,
							LgdConstants.bussType.預約付款應收帳款承購_有追索權);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));
				}

				if (asListZ.contains(item)) {
					bussTypeMap.put(LgdConstants.bussType.其他,
							LgdConstants.bussType.其他);
					chkMap.put(Util.trim(fitem), Util.trim(fitem));
				}

				// 額外要算的科目
				if (inSubjectList != null) {
					if (inSubjectList.contains(item)) {
						bussTypeMap.put(LgdConstants.bussType.一般,
								LgdConstants.bussType.一般);
						chkMap.put(Util.trim(fitem), Util.trim(fitem));
					}
				}

			}

			// 最後檢查科目不在前面業務別，但介於1-8
			for (String fitem : itemMap.keySet()) {
				// 科目的驗證是抓前三碼
				fitem = Util.trim(fitem);
				item = Util.truncateString(Util.trim(fitem), 3);

				if (Util.equals(Util.truncateString(item, 1), "1")
						|| Util.equals(Util.truncateString(item, 1), "2")
						|| Util.equals(Util.truncateString(item, 1), "3")
						|| Util.equals(Util.truncateString(item, 1), "4")
						|| Util.equals(Util.truncateString(item, 1), "5")
						|| Util.equals(Util.truncateString(item, 1), "6")
						|| Util.equals(Util.truncateString(item, 1), "7")
						|| Util.equals(Util.truncateString(item, 1), "8")) {
					if (!chkMap.containsKey(fitem)) {
						bussTypeMap.put(LgdConstants.bussType.一般,
								LgdConstants.bussType.一般);
						chkMap.put(Util.trim(fitem), Util.trim(fitem));
						break;
					}
				}
			}

		}

		if (bussTypeMap.isEmpty()) {
			bussTypeMap.put(LgdConstants.bussType.其他, LgdConstants.bussType.其他);
		}

		return bussTypeMap;

	}

	/**
	 * 判斷要計算LGD之額度明細表
	 * 
	 * FOR 企金簽報書用
	 * 
	 * J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	 * 
	 * @param l140m01a
	 * @param chkType
	 *            1:授權額度合計判斷用 (授審處金至忠) - 非單獨另計授權額度(納入授權額度限額計算註記=1)
	 *            2:引入計算LGD用(風控處邱煥翔) 　　　　　　　　　　　　　　　　　　　　　　　　　　　
	 * 
	 *            3:授權額度合計判斷用 (授審處金至忠) - 單獨另計授權額度(納入授權額度限額計算註記=2-8)
	 * @return
	 */
	@Override
	public boolean isL140m01aNeedLgd(L140M01A l140m01a, String chkType) {
		boolean isNeed = true;

		// **************FOR 企金簽報書用**************
		// **************FOR 企金簽報書用**************
		// **************FOR 企金簽報書用**************

		if (l140m01a == null) {
			isNeed = false;
			return isNeed;
		}

		L120M01A l120m01a = null;
		L120M01C l120m01c = l140m01a.getL120m01c();
		if (l120m01c != null) {
			l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());

		}
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		// 消金簽報書、企金簽報書之消金額度檢查一率不用*************************************************************************************
		// 消金簽報書不要、團貸不要
		if (LMSUtil.isParentCase(l120m01a) || LMSUtil.isClsCase(l120m01a)
				|| LMSUtil.isOverSea_CLS(l120m01a)) {
			isNeed = false;
			return isNeed;
		}

		if (lmsService.isEuroyenTiborExitCase(l120m01a)) {
			isNeed = false;
			return isNeed;
		}

		// 行業對象別
		String busCode = "";
		if (LMSUtil.isClsCase(l120m01a) || LMSUtil.isOverSea_CLS(l120m01a)) {
			C120S01A c120s01a = c120s01aDao.findByUniqueKey(
					l120m01a.getMainId(), l140m01a.getCustId(),
					l140m01a.getDupNo());
			if (c120s01a != null) {
				busCode = Util.trim(c120s01a.getBusCode());
			}

		} else {
			L120S01B l120s01b = l120s01bDao.findByUniqueKey(
					l120m01a.getMainId(), l140m01a.getCustId(),
					l140m01a.getDupNo());
			if (l120s01b != null) {
				busCode = Util.trim(l120s01b.getBusCode());
			}

		}
		if (LMSUtil.isBusCode_060000_130300(busCode)) {
			// 個人戶額度不用檢查LGD頁簽
			isNeed = false;
			return isNeed;
		}

		// J-111-0461_05097_B1001 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		Map<String, String> bussTypeMap = this.getCntrBussTypeMap(l140m01a);

		// [上午 11:35] 邱煥翔(風險控管處,科長)
		// 10 皆不用算LGD
		// 1,2,3,9僅含議題三之授信科目 不用計算LGD
		// 其他1,2,3,9皆要算LGD 且列為CUSTid LGD

		// [下午 06:11] 黃建霖(資訊處,高級專員)
		// 所以10 我改成A 可以嗎
		// [下午 06:11] 黃建霖(資訊處,高級專員)
		// A.不納入，因屬於借新還舊之舊有「不變」額度(本次以新貸額度清償後取消)

		// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則

		// 1.排除LGD霸王條款*******************************************************************************************

		// 額度明細表-納入授權額度限額計算註記，如果是10，優先不算
		// A.不納入，因屬於借新還舊之舊有「不變」額度(本次以新貸額度清償後取消)
		if (Util.equals(Util.trim(l140m01a.getIsStandAloneAuth()), "A")) {
			isNeed = false;
			return isNeed;
		}

		// 2.優先判斷排除科目**************************************************************************
		Set<L140M01C> l140m01cs = l140m01a.getL140m01c();

		// 要排除計算的科目(額度明細表皆為排除科目時才排除)
		if (isNeed) {
			String LMS_LGD_EXCLUDE_SUBJECT = Util.trim(lmsService
					.getSysParamDataValue("LMS_LGD_EXCLUDE_SUBJECT"));
			boolean hasExSubject = false;
			boolean hasExSubject_Not = false;
			String[] exSubjectArr = LMS_LGD_EXCLUDE_SUBJECT.split(",");
			List<String> exSubjectList = Arrays.asList(exSubjectArr);

			if (l140m01cs != null) {
				for (L140M01C l140m01c : l140m01cs) {
					// 科目的驗證是抓前三碼
					String item = Util.truncateString(l140m01c.getLoanTP(), 3);
					if (exSubjectList.contains(item)) {
						hasExSubject = true;
					} else {
						hasExSubject_Not = true;
					}
				}
			}

			if (hasExSubject && !hasExSubject_Not) {
				// 有排除科目，且所有科目都包含再排除科目
				isNeed = false;
			}
		}

		// 要特別加上要計算的科目(額度明細表皆為加計科目時才算)
		if (!isNeed) {

			String LMS_LGD_INCLUDE_SUBJECT = Util.trim(lmsService
					.getSysParamDataValue("LMS_LGD_INCLUDE_SUBJECT"));
			boolean hasInSubject = false;
			boolean hasInSubject_Not = false;
			String[] inSubjectArr = LMS_LGD_INCLUDE_SUBJECT.split(",");
			List<String> inSubjectList = Arrays.asList(inSubjectArr);

			if (l140m01cs != null) {
				for (L140M01C l140m01c : l140m01cs) {
					// 科目的驗證是抓前三碼
					String item = Util.truncateString(l140m01c.getLoanTP(), 3);
					if (inSubjectList.contains(item)) {
						hasInSubject = true;
					} else {
						hasInSubject_Not = true;
					}
				}
			}

			if (hasInSubject && !hasInSubject_Not) {
				// 有加計包含科目，且所有科目都包含再加計包含科目
				isNeed = true;
			}
		}

		if (!isNeed) {
			// 全都是排除科目，則不算LGD
			return isNeed;
		}

		// 3.判斷納入授權額度限額計算註記與chkType

		// 要計算的業務類別
		// 01,02,03
		// =>1,7,8,9,A,D,
		// J-111-0461_05097_B1001 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		if (Util.equals(chkType, "1") || Util.equals(chkType, "3")) {
			// chkType=1:授權額度合計判斷用 (授審處金至忠)

			// 'LMS_LGD_INCLUDE_BUSSTYPE','01,02,03','需計算LGD授權額度之業務類別'
			String LMS_LGD_INCLUDE_BUSSTYPE = Util.trim(lmsService
					.getSysParamDataValue("LMS_LGD_INCLUDE_BUSSTYPE"));

			if (Util.notEquals(LMS_LGD_INCLUDE_BUSSTYPE, "")) {
				String[] item_LMS_LGD_INCLUDE_BUSSTYPE = LMS_LGD_INCLUDE_BUSSTYPE
						.split(",");
				List<String> asList_LMS_LGD_INCLUDE_BUSSTYPE = Arrays
						.asList(item_LMS_LGD_INCLUDE_BUSSTYPE);

				if (bussTypeMap != null && !bussTypeMap.isEmpty()) {
					boolean needBussType = false;
					for (String bussType : bussTypeMap.keySet()) {
						if (asList_LMS_LGD_INCLUDE_BUSSTYPE.contains(bussType)) {
							needBussType = true;
							break;
						}
					}
					if (!needBussType) {
						isNeed = false;
						return isNeed;
					}
				}

			}

			// 判斷是否要納入授權
			// LMS_LGD_INCLUDE_CHKTYPE_1','1','需計算LGD之納入授權額度限額計算註記(授審處金至忠)
			String isStandAloneAuth = Util.isEmpty(Util.trim(l140m01a
					.getIsStandAloneAuth())) ? "1" : Util.trim(l140m01a
					.getIsStandAloneAuth());

			String LMS_LGD_INCLUDE_CHKTYPE_1 = "";
			if (Util.equals(chkType, "1")) {
				// 1:授權額度合計判斷用 (授審處金至忠) - 非單獨另計授權額度(納入授權額度限額計算註記=1)
				LMS_LGD_INCLUDE_CHKTYPE_1 = Util.trim(lmsService
						.getSysParamDataValue("LMS_LGD_INCLUDE_CHKTYPE_1"));
			} else {
				// 3:授權額度合計判斷用 (授審處金至忠) - 單獨另計授權額度(納入授權額度限額計算註記=2-8)
				LMS_LGD_INCLUDE_CHKTYPE_1 = Util.trim(lmsService
						.getSysParamDataValue("LMS_LGD_INCLUDE_CHKTYPE_3"));
			}

			if (Util.notEquals(LMS_LGD_INCLUDE_CHKTYPE_1, "")) {
				String[] item_LMS_LGD_INCLUDE_CHKTYPE_1 = LMS_LGD_INCLUDE_CHKTYPE_1
						.split(",");
				List<String> asList_LMS_LGD_INCLUDE_CHKTYPE_1 = Arrays
						.asList(item_LMS_LGD_INCLUDE_CHKTYPE_1);
				if (!asList_LMS_LGD_INCLUDE_CHKTYPE_1
						.contains(isStandAloneAuth)) {
					isNeed = false;
					return isNeed;
				}
			}

		} else {

			// chkType=2:判斷額度須要計算LGD (風控處邱煥翔)

			// 判斷是否要納入授權
			// LMS_LGD_INCLUDE_CHKTYPE_2','1,2,3,4,9','需計算LGD之納入授權額度限額計算註記(風控處邱煥翔)
			String isStandAloneAuth = Util.isEmpty(Util.trim(l140m01a
					.getIsStandAloneAuth())) ? "1" : Util.trim(l140m01a
					.getIsStandAloneAuth());

			String LMS_LGD_INCLUDE_CHKTYPE_2 = Util.trim(lmsService
					.getSysParamDataValue("LMS_LGD_INCLUDE_CHKTYPE_2"));

			if (Util.notEquals(LMS_LGD_INCLUDE_CHKTYPE_2, "")) {
				String[] item_LMS_LGD_INCLUDE_CHKTYPE_2 = LMS_LGD_INCLUDE_CHKTYPE_2
						.split(",");
				List<String> asList_LMS_LGD_INCLUDE_CHKTYPE_2 = Arrays
						.asList(item_LMS_LGD_INCLUDE_CHKTYPE_2);
				if (!asList_LMS_LGD_INCLUDE_CHKTYPE_2
						.contains(isStandAloneAuth)) {
					isNeed = false;
					return isNeed;
				}
			}

		}

		return isNeed;
	}

	/**
	 * 該額度是否需要計算授信額度合計
	 * 
	 * @param l140m01a
	 * @param chkType
	 *            1:授權額度合計判斷用 (授審處金至忠) - 非單獨另計授權額度(納入授權額度限額計算註記=1) 3:授權額度合計判斷用
	 *            (授審處金至忠) - 單獨另計授權額度(納入授權額度限額計算註記=2-8)
	 * @return
	 */
	@Override
	public boolean isL140m01aNeedCountLgdTotal(L140M01A l140m01a, String chkType) {
		boolean needCount = false;
		L120M01A l120m01a = null;
		L120M01C l120m01c = l140m01a.getL120m01c();
		if (l120m01c != null) {
			l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());

		}
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		// 1.先BY人判斷，如雙軌時，個人戶不要顯示*********************************************

		boolean custNeed = this.isCustNeedCountLgdTotal(l140m01a);
		if (!custNeed) {
			needCount = false;
			return needCount;
		}

		// String busCode = "";

		// 2.進階判斷***********************************************************************

		// 消金簽報書判斷
		if (LMSUtil.isClsCase(l120m01a) || LMSUtil.isOverSea_CLS(l120m01a)) {
			// 團貸不要
			needCount = LMSUtil.isParentCase(l120m01a) ? false : true;
		} else {
			// 企金簽報書
			// 要進一步判斷個人戶額度或企業戶額度
			// 1.個人戶都要合計
			// 2.企業戶要進一步判斷

			String busCode = this.getCustBusCodeByL140m01a(l140m01a);
			if (LMSUtil.isBusCode_060000_130300(busCode)) {
				needCount = true;
				// 個金欄位同企金，所以應該也可以一樣判斷
				// needCount = this.isL140m01aNeedLgd(l140m01a, "1");

			} else {
				needCount = this.isL140m01aNeedLgd(l140m01a, chkType);
			}

		}
		return needCount;
	}

	/**
	 * 依借款人行業對象別判斷是否需要計算授信額度合計 FOR 雙軌判斷
	 * 
	 * @param l140m01a
	 * @return
	 */
	@Override
	public boolean isCustNeedCountLgdTotal(L140M01A l140m01a) {
		boolean needCount = false;

		L120M01A l120m01a = null;
		L120M01C l120m01c = l140m01a.getL120m01c();
		if (l120m01c != null) {
			l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());

		}
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		String countryType = branchService.getBranch(caseBrId).getCountryType();

		// 雙軌，個人戶不計算LGD
		String busCode = this.getCustBusCodeByL140m01a(l140m01a);
		if (Util.equals(busCode, "")) {
			// lmsL120M01A.error026=於借款人基本資料中無該借款人行業對象別資訊。
			// showMsg = pop.getProperty("lmsL120M01A.error026");
			return needCount;
		}

		if (LMSUtil.isBusCode_060000_130300(busCode)) {
			boolean canCls = false;
			if (Util.notEquals(countryType, "TW")) {
				countryType = "OV";
			}

			// 雙軌，第一次只有先上TW
			// LMS_LGD_CLS_COUNT_LGDTOTAMT 有兩個地方會呼叫
			// LMSServiceImpl.java \showLgdTotAmt
			// LMSLgdServiceImpl.java \isCustNeedCountLgdTotal
			String LMS_LGD_CLS_COUNT_LGDTOTAMT = Util.trim(lmsService
					.getSysParamDataValue("LMS_LGD_CLS_COUNT_LGDTOTAMT"));

			if (Util.notEquals(LMS_LGD_CLS_COUNT_LGDTOTAMT, "")) {
				for (String xx : LMS_LGD_CLS_COUNT_LGDTOTAMT.split(",")) {
					if (Util.equals(xx, caseBrId)
							|| Util.equals(countryType, xx)) {
						canCls = true;
						break;
					}
				}

			}
			if (!canCls) {
				needCount = false;
				return needCount;
			} else {
				needCount = true;
			}

		} else {
			needCount = true;
		}

		return needCount;
	}

	/**
	 * 取得借款人行業對象別BY額度明細表
	 * 
	 * @param l140m01a
	 * @return
	 */
	@Override
	public String getCustBusCodeByL140m01a(L140M01A l140m01a) {
		L120M01A l120m01a = null;
		L120M01C l120m01c = l140m01a.getL120m01c();
		if (l120m01c != null) {
			l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());

		}
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		String busCode = "";

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		String countryType = branchService.getBranch(caseBrId).getCountryType();

		// 行業對象別
		if (LMSUtil.isClsCase(l120m01a) || LMSUtil.isOverSea_CLS(l120m01a)) {
			C120S01A c120s01a = c120s01aDao.findByUniqueKey(
					l120m01a.getMainId(), l140m01a.getCustId(),
					l140m01a.getDupNo());
			if (c120s01a != null) {
				busCode = Util.trim(c120s01a.getBusCode());
			}

		} else {
			L120S01B l120s01b = l120s01bDao.findByUniqueKey(
					l120m01a.getMainId(), l140m01a.getCustId(),
					l140m01a.getDupNo());
			if (l120s01b != null) {
				busCode = Util.trim(l120s01b.getBusCode());
			}

		}
		return busCode;
	}

	/**
	 * 依借款人行業對象別判斷是否需要計算授信額度合計 FOR 雙軌判斷
	 * 
	 * 雙軌期間LGD不要印在額度明細表
	 * 
	 * @param l140m01a
	 * @return
	 */
	@Override
	public boolean isCountLgdTotalOnTestPeriod(L120M01A l120m01a) {
		boolean isTest = false;

		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		String countryType = branchService.getBranch(caseBrId).getCountryType();

		boolean canCls = false;
		if (Util.notEquals(countryType, "TW")) {
			countryType = "OV";
		}

		// 雙軌，第一次只有先上TW
		String LMS_LGD_LGDTOTAMT_FOR_TEST = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_LGDTOTAMT_FOR_TEST"));

		if (Util.notEquals(LMS_LGD_LGDTOTAMT_FOR_TEST, "")) {
			for (String xx : LMS_LGD_LGDTOTAMT_FOR_TEST.split(",")) {
				if (Util.equals(xx, caseBrId) || Util.equals(countryType, xx)) {
					isTest = true;
					break;
				}
			}

		}

		return isTest;
	}

	/**
	 * 依借款人行業對象別判斷是否需要計算授信額度合計 FOR 雙軌判斷
	 * 
	 * 雙軌期間LGD不要印在額度明細表
	 * 
	 * @param l140m01a
	 * @return
	 */
	@Override
	public boolean isCountLgdTotalOnTestPeriod_2(L120M01A l120m01a) {
		boolean isTest = false;

		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		String countryType = branchService.getBranch(caseBrId).getCountryType();

		boolean canCls = false;
		if (Util.notEquals(countryType, "TW")) {
			countryType = "OV";
		}

		// 雙軌，第一次只有先上TW
		// step1.國別有列出的不印
		String LMS_LGD_LGDTOTAMT_FOR_TEST = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_LGDTOTAMT_FOR_TEST_2"));

		if (Util.notEquals(LMS_LGD_LGDTOTAMT_FOR_TEST, "")) {
			for (String xx : LMS_LGD_LGDTOTAMT_FOR_TEST.split(",")) {
				if (Util.equals(xx, caseBrId) || Util.equals(countryType, xx)) {
					isTest = true;
					break;
				}
			}

		}

		// step2. caseDate簽案日期要大於某日
		// 有通過1的才會進來
		if (!isTest) {
			String chkDateStr = Util.trim(lmsService
					.getSysParamDataValue("LMS_LGD_LGDTOTAMT_FOR_TEST_3"));
			if (Util.notEquals(chkDateStr, "")) {

				Date chkDate = CapDate.getDate(
						(Util.isEmpty(chkDateStr) ? CapDate
								.getCurrentDate("yyyy-MM-dd") : chkDateStr),
						"yyyy-MM-dd");
				Date caseDate = (l120m01a.getCaseDate() == null ? Util
						.parseDate(CapDate.getCurrentDate("yyyy-MM-dd"))
						: l120m01a.getCaseDate());

				// ex:2025-01-01
				// 簽案日期比設定的日期小，不給印
				if (caseDate.compareTo(chkDate) < 0) {
					isTest = true;
				}
			}
		}

		// step3.特別開放的簽案行才可以印
		if (!isTest) {
			String LMS_LGD_LGDTOTAMT_FOR_TEST_4 = Util.trim(lmsService
					.getSysParamDataValue("LMS_LGD_LGDTOTAMT_FOR_TEST_4"));
			if (Util.notEquals(LMS_LGD_LGDTOTAMT_FOR_TEST_4, "")) {

				List<String> chkBrNoList = Arrays
						.asList(LMS_LGD_LGDTOTAMT_FOR_TEST_4.split(","));

				// ex:025,007,201
				// 不在開放的簽案行名單裡，不給印
				if (!chkBrNoList.contains(caseBrId)) {
					isTest = true;
				}
			}
		}

		// step4. 登入者為特定分行才可以印
		if (!isTest) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			String ssoUnitNo = user.getSsoUnitNo();
			String LMS_LGD_LGDTOTAMT_FOR_TEST_5 = Util.trim(lmsService
					.getSysParamDataValue("LMS_LGD_LGDTOTAMT_FOR_TEST_5"));
			if (Util.notEquals(LMS_LGD_LGDTOTAMT_FOR_TEST_5, "")) {

				List<String> chkSsoUnitNoList = Arrays
						.asList(LMS_LGD_LGDTOTAMT_FOR_TEST_5.split(","));

				// ex:918,900
				// 不在開放的登入者分行名單裡，不給印
				if (!chkSsoUnitNoList.contains(ssoUnitNo)) {
					isTest = true;
				}
			}
		}

		return isTest;
	}

	/**
	 * J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
	 * 
	 * 擔保品建擋隱藏順位或前順位
	 * 
	 * @param collKind
	 * @return
	 */
	@Override
	@NonTransactional
	public boolean hideRgstInfoForL120s21c(String collKind) {
		boolean hide = true;

		if (Util.equals(Util.getLeftStr(collKind, 2), "01")) {
			hide = false;
		} else if (Util.equals(Util.getLeftStr(collKind, 2), "02")) {
			hide = false;
		}

		return hide;
	}

	/**
	 * J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
	 * 
	 * 取得LGD參數(擔保品回收率只有zh_TW，所以英文版會抓不到)
	 * 
	 * @param l120m01a
	 * @param cntrNo
	 * @param codeTypePrefix
	 * @param othCollKind
	 *            "LGD_Rate_"、"LGD_" + othCollKind
	 * @return
	 */
	@Override
	public Map<String, String> findLgdCollKindCodeTypeMap(L120M01A l120m01a,
			String cntrNo, String codeTypeStr) {

		// J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		// 風控處邱煥翔8/12：簽案行的國別->改成額度序號前三碼
		String cntrBranch = Util.getLeftStr(Util.trim(cntrNo), 3);
		if (Util.isEmpty(cntrBranch)) {
			cntrBranch = l120m01a.getCaseBrId();
		}

		IBranch ibranch = branchService.getBranch(cntrBranch);
		String countryType = Util.trim(ibranch.getCountryType());
		// 025算海外 --->9/14 風控處邱煥翔要求025變回國內
		// [下午 06:16] 邱煥翔(風險控管處,科長)
		// DBU及OBU的額度序號 清償機率請用96%

		// if (Util.equals(cntrBranch, "025")) {
		// countryType = "OTH";
		// }

		// codeTypeStr:
		// "LGD_Rate_"
		// "LGD_" + othCollKind
		// (擔保品回收率只有zh_TW，所以英文版會抓不到)
		Map<String, String> codeCollRateMap = codeTypeService.findByCodeType(
				codeTypeStr + "_" + countryType, "zh_TW");

		if (codeCollRateMap == null || codeCollRateMap.isEmpty()) {
			codeCollRateMap = codeTypeService.findByCodeType(codeTypeStr
					+ "_OTH", "zh_TW");
		}

		return codeCollRateMap;

	}

	/**
	 * J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
	 * 
	 * 計算LGD前清除前次LGD計算相關資料
	 * 
	 * @param l120s21b
	 */
	public void clearL120s21bLgdData(L120S21B l120s21b) {
		l120s21b.setExpectLgd(null);
		l120s21b.setCustLgd(null);
		l120s21b.setBussLgd(null);
		l120s21b.setCollateralRecoveryOthSme(null); // 含信保
		l120s21b.setCollateralRecoveryCmsSme(null); // 含信保
	}

	/**
	 * J-111-0572_05097_B1001 Web e-Loan企金授信配合e-Loan LGD擔保品分配規則修改
	 * 
	 * @param l120s21b
	 * @return
	 */
	@Override
	public String getCollateralRecovery(L120S21B l120s21b) {

		String mainId = Util.trim(l120s21b.getMainId());
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);
		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());

		String CMS_LGD_NEW = Util.trim(lmsService
				.getSysParamDataValue("CMS_LGD_NEW"));

		// 簽報書下所有額度明細表
		List<L140M01A> listL140m01a = l140m01aDao
				.findL140m01aListByL120m01cMainId(mainId,
						UtilConstants.Cntrdoc.ItemType.額度明細表, null);

		// 優先用額度EAD金額
		List<L120S21B> listL120s21b = this.findL120s21bByMainId(mainId);
		Map<String, String> cntrEadMap = new HashMap<String, String>();
		for (L120S21B tl120s21b : listL120s21b) {
			String tCntrNo = Util.nullToSpace(tl120s21b.getCntrNo_s21b());
			cntrEadMap.put(tCntrNo, tl120s21b.getCntrEad_s21b() == null ? "0"
					: tl120s21b.getCntrEad_s21b().toPlainString());
		}

		// 補有計算共管的分配後額度
		List<L120S21A> listL120s21a = this.findL120s21aByMainId(mainId);
		for (L120S21A tl120s21a : listL120s21a) {
			String tCntrNo = Util.nullToSpace(tl120s21a.getCntrNo_s21a());
			if (Util.notEquals(tCntrNo, "")) {
				if (!cntrEadMap.containsKey(tCntrNo)) {
					cntrEadMap.put(tCntrNo,
							tl120s21a.getAllocateF() == null ? "0" : tl120s21a
									.getAllocateF().toPlainString());
				}
			}
		}

		// 再補沒有在額度EAD的額度明細表現請額度
		for (L140M01A tl140m01a : listL140m01a) {
			String tCntrNo = Util.nullToSpace(tl140m01a.getCntrNo());
			if (Util.notEquals(tCntrNo, "")) {
				if (!cntrEadMap.containsKey(tCntrNo)) {
					String currentApplyCurr = Util.isEmpty(Util.trim(tl140m01a
							.getCurrentApplyCurr())) ? "TWD" : Util
							.trim(tl140m01a.getCurrentApplyCurr());
					BigDecimal currentApplyAmt = tl140m01a.getCurrentApplyAmt();
					cntrEadMap.put(
							tCntrNo,
							currentApplyAmt == null ? "0" : (Util.equals(
									currentApplyCurr, "TWD") ? currentApplyAmt
									: branchRate.toOtherAmt(currentApplyCurr,
											"TWD", currentApplyAmt))
									.toPlainString());
				}
			}
		}

		if (Util.equals(CMS_LGD_NEW, "Y")) {
			return getCollateralRecovery_2(l120s21b, cntrEadMap);
		} else {
			return getCollateralRecovery_1(l120s21b, cntrEadMap);
		}
	}

	public String getCollateralRecovery_1(L120S21B l120s21b,
			Map<String, String> cntrEadMap) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "";
		String cntrNo = Util.nullToSpace(l120s21b.getCntrNo_s21b());

		L140M01A l140m01a = lmsService.findL140M01AByL120m01cMainIdAndcntrNo(
				l120s21b.getMainId(), cntrNo,
				UtilConstants.Cntrdoc.ItemType.額度明細表);

		if (l140m01a == null) {
			l140m01a = new L140M01A();
		}

		// 先刪除所有已存在擔保品已建檔資料
		List<L120S21C> listL120s21c = this.findL120s21cByMainIdAndCollType(
				l120s21b.getMainId(), cntrNo, "1"); // 1.已建檔
		if (listL120s21c != null && !listL120s21c.isEmpty()) {
			this.deleteListL120s21c(listL120s21c);
		}

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		List<L120S21C> listL120s21c3 = this.findL120s21cByMainIdAndCollType(
				l120s21b.getMainId(), cntrNo, "9"); // 9.其他
		if (listL120s21c3 != null && !listL120s21c3.isEmpty()) {
			this.deleteListL120s21c(listL120s21c3);
		}

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		// 先引進其他擔保品
		if (Util.equals(Util.trim(l140m01a.getSyndIsCmsSpecial_1()), "Y")) {
			// L140M01a.syndIsCmsSpecial_1=本案是否為First priority security interest
			// in substantially all tangible and intangible assets…或類似授信條件者？

			L120S21C l120s21c = new L120S21C();
			l120s21c.setMainId(l120s21b.getMainId());
			l120s21c.setCollType_s21c("9");// 9.其他
			l120s21c.setCntrNo_s21c(l120s21b.getCntrNo_s21b());
			l120s21c.setCreator(user.getUserId());
			l120s21c.setCreateTime(CapDate.getCurrentTimestamp());

			String othCollKind = "999901";
			l120s21c.setColKind_s21c(othCollKind);

			// J-110-0485_05097_B1009_B Web
			// e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
			if (this.hideRgstInfoForL120s21c(othCollKind)) {
				l120s21c.setColPreRgstAmt_s21c(null);
				l120s21c.setColRgstAmt_s21c(null);
			}

			// 改以擔保品回傳資料為主，免得兩邊計算不一致
			// errorMsg = this.calcL120s21c(l120s21c);
			//
			// if (Util.notEquals(errorMsg, "")) {
			// return errorMsg;
			// }

			this.save(l120s21c);

		}

		BigDecimal cntrCollAmt = BigDecimal.ZERO;
		BigDecimal cntrCollAmt_0503 = BigDecimal.ZERO;
		try {
			int sec = 30;

			KeyStore trustStore = KeyStore.getInstance(KeyStore
					.getDefaultType());
			trustStore.load(null, null);

			SSLSocketFactory sf = new RPAHttpSSLSocketFactory(trustStore);
			final HttpParams params = new BasicHttpParams();
			HttpConnectionParams.setStaleCheckingEnabled(params, false);
			HttpConnectionParams.setConnectionTimeout(params, sec * 1000);
			HttpConnectionParams.setSoTimeout(params, sec * 1000);
			HttpConnectionParams.setSocketBufferSize(params, 8192 * 5);
			SchemeRegistry registry = new SchemeRegistry();
			registry.register(new Scheme("http", PlainSocketFactory
					.getSocketFactory(), 80));
			registry.register(new Scheme("https", sf, 443));
			ClientConnectionManager ccm = new ThreadSafeClientConnManager(
					params, registry);
			HttpClient httpclient = new DefaultHttpClient(ccm, params);

			HttpPost httpPost = new HttpPost(
					sysParamService
							.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL));
			httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
			// 設定Header Authorization=token

			JSONObject reqJSONObj = new JSONObject();
			JSONObject request = new JSONObject();

			// if (Util.equals("004110300326", cntrNo)) {
			// String xxx = "004110300326";
			// }

			request.put("mainId", l120s21b.getMainId());
			request.put("cntrNo", cntrNo);

			JSONObject cntrEad = new JSONObject();
			cntrEad.putAll(cntrEadMap);

			request.put("cntrEad", cntrEad.toString());

			JSONObject collKey = new JSONObject();
			// 沒有額度明細表以擔保品建檔為主
			List<String> cntrNos = new ArrayList<String>();
			cntrNos.add(cntrNo);

			// 有額度明細表要以額度明細表擔保品連結為主，沒有額度明細表以擔保品建檔為主
			if (Util.equals(Util.trim(l120s21b.getHasCntrDoc_s21b()), "Y")) {
				// 有額度明細表要以額度明細表擔保品連結為主
				List<L140M01O> l140m01os = this
						.findL140m01oByMainIdOrderForLgd(l140m01a.getMainId());
				if (l140m01os != null && !l140m01os.isEmpty()) {
					for (L140M01O l140m01o : l140m01os) {

						String cmsOid = Util.trim(l140m01o.getCmsOid());
						String cmsCollKey = Util.trim(l140m01o.getCmsCollKey());

						if (Util.notEquals(cmsOid, "")
								&& Util.equals(cmsCollKey, "")) {
							List<Map<String, Object>> cmsList = eloandbBASEService
									.findCms_selByOid(cmsOid);
							if (cmsList != null && !cmsList.isEmpty()) {
								for (Map<String, Object> cmsMap : cmsList) {

									cmsCollKey = Util.trim(MapUtils.getString(
											cmsMap, "COLLKEY"));

									if (Util.notEquals(cmsCollKey, "")) {
										l140m01o.setCmsCollKey(cmsCollKey);
										l140m01oDao.save(l140m01o);
										break;
									}
								}
							}
						}
						collKey.put(cmsOid, cmsCollKey);
					}
				}
			}

			// J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
			// 都沒有擔保品，則抓擔保品管理系統
			if (collKey.isEmpty()) {
				if (applyCmsCollDataWhenNoL140m01o()) {
					List<Map<String, Object>> cmsList = eloandbBASEService
							.findCmsC100m01ByCntrNoForLgd(cntrNos);

					for (Map<String, Object> cmsMap : cmsList) {
						collKey.put(MapUtils.getString(cmsMap, "OID"),
								MapUtils.getString(cmsMap, "COLLKEY"));
					}
				}
			}

			if (Util.equals(Util.trim(l120s21b.getUnionFlag()), "Y")) {
				request.put("unionFlag", Util.trim(l120s21b.getUnionFlag()));
				request.put("syndAmt",
						l120s21b.getSyndAmt() == null ? BigDecimal.ZERO
								: l120s21b.getSyndAmt()); // 本行參貸額度
				request.put("unionAmt",
						l120s21b.getUnionAmt() == null ? BigDecimal.ZERO
								: l120s21b.getUnionAmt()); // 聯合授信案總金額
			} else {
				request.put("unionFlag", "N");
				request.put("syndAmt", BigDecimal.ZERO); // 本行參貸額度
				request.put("unionAmt", BigDecimal.ZERO); // 聯合授信案總金額
			}

			request.put("brNo", user.getUnitNo());

			boolean isTestEmail = "true".equals(PropUtil
					.getProperty("isTestEmail")) ? true : false; // 是否為測試信件
			isTestEmail = false;

			// TODO
			// if (isTestEmail) {
			// errorMsg = "";
			// cntrCollAmt = BigDecimal.ZERO;
			// l120s21b.setCollateralRecoveryCms(cntrCollAmt);
			// this.save(l120s21b);
			// return "";
			// }

			// 沒有擔保品就不用去呼擔保品API
			if (collKey.isEmpty()) {
				cntrCollAmt = BigDecimal.ZERO;
				// l120s21b.setCollateralRecovery(cntrCollAmt);
				l120s21b.setCollateralRecoveryCms(cntrCollAmt);
				this.save(l120s21b);
				return "";
			} else {
				request.put("collKey", collKey.toString());
			}

			reqJSONObj.put("serviceId", "cmscollEDA_LGDService");
			reqJSONObj.put("vaildIP", "N");
			reqJSONObj.put("request", request.toString());

			LOGGER.info("LGD CALL CMS Begin*******************************************************************");
			LOGGER.info(reqJSONObj.toString());

			StringEntity stringEntity = new StringEntity(reqJSONObj.toString(),
					"UTF-8");
			stringEntity.setContentEncoding("UTF-8");
			httpPost.setEntity(stringEntity);

			HttpResponse response = null;
			response = httpclient.execute(httpPost);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 回傳內容
				String content = EntityUtils.toString(response.getEntity(),
						"UTF-8");// UTF-8 big5
				LOGGER.info(content);
				// {"rc":0,"rcmsg":"SUCCESS","message":"執行成功","CollRecycle":"729063574"}

				// {"rc":0,"rcmsg":"SUCCESS","message":"執行成功","CollRecycle":"45662637","CollRecycleLABOR":0,"rateLABOR":0}
				//
				// CollRecycleLABOR 信保回收
				//
				// rateLABOR 信保成數

				JSONObject responseJson = JSONObject.fromObject(content);
				String rc = responseJson.optString("rc", "1");

				// TODO
				// if (isTestEmail) {
				// rc = "0";
				// isTestEmail = false; // 控制jsonStr測試資料
				// }

				if (Util.equals(rc, "0")) {
					// SUCCESS
					LOGGER.info("Response SUCCESS");

					cntrCollAmt = Util.parseBigDecimal(responseJson.optString(
							"CollRecycle", "0"));

					// CollRecycleLABOR 信保回收
					// rateLABOR 信保成數

					// TODO
					// 產生L120S21C擔保品明細--已建檔
					// {"rc":0,"rcmsg":"SUCCESS","message":"執行成功","CollRecycle":"45662637","CollRecycleLABOR":0,"rateLABOR":0,"collData":""}
					JSONArray collData = null;
					if (isTestEmail) {
						// String jsonStr =
						// "{collData:[{'cmsBranch_s21c':'005','cmsTypeCd_s21c':'1','cmsCustId_s21c':'11111111','cmsDupNo_s21c':'0','cmsCollNo_s21c':'01-01-001','cmsOid_s21c':'11111111111111111111111111111111','cmsCollKey_s21c':'22222222222222222222222222222222','colKind_s21c':'020100','colCurr_s21c':'TWD','colTimeValue_s21c':'9999999','colPreRgstAmt_s21c':'1111111','colRgstAmt_s21c':'7777777','colCoUseFlag_s21c':'Y','colShareRate_s21c':'40.5'},{'cmsBranch_s21c':'005','cmsTypeCd_s21c':'1','cmsCustId_s21c':'22222222','cmsDupNo_s21c':'0','cmsCollNo_s21c':'01-01-002','cmsOid_s21c':'11111111111111111111111111111111','cmsCollKey_s21c':'22222222222222222222222222222222','colKind_s21c':'020100','colCurr_s21c':'TWD','colTimeValue_s21c':'8888888','colPreRgstAmt_s21c':'2222222','colRgstAmt_s21c':'6666666','colCoUseFlag_s21c':'Y','colShareRate_s21c':'80.0'},{'cmsBranch_s21c':'005','cmsTypeCd_s21c':'1','cmsCustId_s21c':'33333333','cmsDupNo_s21c':'0','cmsCollNo_s21c':'01-01-003','cmsOid_s21c':'11111111111111111111111111111111','cmsCollKey_s21c':'22222222222222222222222222222222','colKind_s21c':'020100','colCurr_s21c':'TWD','colTimeValue_s21c':'7777777','colPreRgstAmt_s21c':'0','colRgstAmt_s21c':'5555555','colCoUseFlag_s21c':'N','colShareRate_s21c':'0'}]}";
						String jsonStr = "collData':[{'cmsBranch_s21c':'0C3','cmsCustId_s21c':'','cmsOid_s21c':'8DD4E830F85211EC9A97FF21C0A83B1E','colCurr_s21c':'HKD','colTimeValue_s21c':'0','colPreRgstAmt_s21c':0,'colCoUseFlag_s21c':'N','colShareRate_s21c':'100.000000000000000000000000','majorType':'1','colKind_s21c':'050301','cmsGrtrt_s21c':'90','colEstRecovery_s21c':'95000.0000','colRgstRecovery_s21c':'95000.00','colRgstAmt_s21c':'0','twdRate':3.7002,'estDate':'2022-06-30','colRate_s21c':'0.95','colRecovery_s21c':'95000.00','colRecoveryTwd_s21c':'351519.00'}";
						JSONObject jobjectD = JSONObject.fromObject(jsonStr);
						collData = jobjectD.getJSONArray("collData");
					} else {
						collData = responseJson.optJSONArray("collData");
					}

					if (collData != null) {

						for (int j = 1; j < collData.size() + 1; j++) {
							// JSONObject json =
							// JSONObject.fromObject(collData.getString(j - 1));

							L120S21C l120s21c = new L120S21C();
							l120s21c.setMainId(l120s21b.getMainId());
							l120s21c.setCollType_s21c("1");// 已建檔
							l120s21c.setCntrNo_s21c(l120s21b.getCntrNo_s21b());
							l120s21c.setCreator(user.getUserId());
							l120s21c.setCreateTime(CapDate
									.getCurrentTimestamp());

							DataParse.toBean(collData.getString(j - 1),
									l120s21c);

							// J-111-0083_05097_B1002 Web
							// e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
							// 擔保品回傳回收率0.75，要乘上100
							if (l120s21c.getColRate_s21c() != null) {
								l120s21c.setColRate_s21c(l120s21c
										.getColRate_s21c().multiply(
												new BigDecimal(100)));
							}

							// J-110-0485_05097_B1008 Web
							// e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
							String othCollKind = Util.trim(l120s21c
									.getColKind_s21c()); // L120S21C.colKind_s21c=擔保品種類

							// J-111-0400_05097_B1002 Web
							// e-Loan企金授信增修LGD及額度暴險估算規則
							if (Util.equals(
									Util.trim(l120s21c.getColCoUseFlag_s21c()),
									"N")) {
								// 是否與其他額度共用為否，分配比率預設100
								l120s21c.setColShareRate_s21c(new BigDecimal(
										100));
							} else if (Util.equals(
									Util.getLeftStr(othCollKind, 6), "050300")) {
								// 中小信保因為雨青不分配會帶0，所以風控處邱煥祥要求改為100
								// [星期一 下午 01:35] 邱煥翔(風險控管處,襄理)
								// [星期一 下午 01:37] 邱煥翔(風險控管處,襄理)
								// 為何分配比率為0
								l120s21c.setColShareRate_s21c(new BigDecimal(
										100));
							}

							if (Util.equals(Util.getLeftStr(othCollKind, 4),
									"0503")) {
								cntrCollAmt_0503 = cntrCollAmt_0503
										.add(l120s21c.getColRecoveryTwd_s21c());
							}

							// J-110-0485_05097_B1009_B Web
							// e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
							if (this.hideRgstInfoForL120s21c(othCollKind)) {
								l120s21c.setColPreRgstAmt_s21c(null);
								l120s21c.setColRgstAmt_s21c(null);
							}

							// 改以擔保品回傳資料為主，免得兩邊計算不一致
							// errorMsg = this.calcL120s21c(l120s21c);
							//
							// if (Util.notEquals(errorMsg, "")) {
							// return errorMsg;
							// }

							this.save(l120s21c);
						}
					}

					// J-110-0485_05097_B1008 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
					l120s21b.setCollateralRecoveryCms((cntrCollAmt
							.subtract(cntrCollAmt_0503)).max(BigDecimal.ZERO));

					this.save(l120s21b);
				} else {
					// FAIL
					errorMsg = "額度序號「" + l120s21b.getCntrNo_s21b()
							+ "」 Response FAIL:"
							+ responseJson.optString("message", "FAIL");

					LOGGER.error(errorMsg);
				}

			} else {
				errorMsg = "HTTP ERROR:"
						+ response.getStatusLine().getStatusCode();
			}
		} catch (IOException ioe) {
			errorMsg = StrUtils.getStackTrace(ioe);
			LOGGER.error(errorMsg);
		} catch (Exception e) {
			errorMsg = StrUtils.getStackTrace(e);
			LOGGER.error(errorMsg);
		} finally {

		}

		return errorMsg;
	}

	public String getCollateralRecovery_2(L120S21B l120s21b,
			Map<String, String> cntrEadMap) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "";
		String cntrNo = Util.nullToSpace(l120s21b.getCntrNo_s21b());
		String mainId = Util.trim(l120s21b.getMainId());

		L140M01A l140m01a = lmsService.findL140M01AByL120m01cMainIdAndcntrNo(
				l120s21b.getMainId(), cntrNo,
				UtilConstants.Cntrdoc.ItemType.額度明細表);

		if (l140m01a == null) {
			l140m01a = new L140M01A();
		}

		// 先刪除所有已存在擔保品已建檔資料
		List<L120S21C> listL120s21c = this.findL120s21cByMainIdAndCollType(
				l120s21b.getMainId(), cntrNo, "1"); // 1.已建檔
		if (listL120s21c != null && !listL120s21c.isEmpty()) {
			this.deleteListL120s21c(listL120s21c);
		}

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		List<L120S21C> listL120s21c3 = this.findL120s21cByMainIdAndCollType(
				l120s21b.getMainId(), cntrNo, "9"); // 9.其他
		if (listL120s21c3 != null && !listL120s21c3.isEmpty()) {
			this.deleteListL120s21c(listL120s21c3);
		}

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		// 先引進其他擔保品
		if (Util.equals(Util.trim(l140m01a.getSyndIsCmsSpecial_1()), "Y")) {
			// L140M01a.syndIsCmsSpecial_1=本案是否為First priority security interest
			// in substantially all tangible and intangible assets…或類似授信條件者？

			L120S21C l120s21c = new L120S21C();
			l120s21c.setMainId(l120s21b.getMainId());
			l120s21c.setCollType_s21c("9");// 9.其他
			l120s21c.setCntrNo_s21c(l120s21b.getCntrNo_s21b());
			l120s21c.setCreator(user.getUserId());
			l120s21c.setCreateTime(CapDate.getCurrentTimestamp());

			String othCollKind = "999901";
			l120s21c.setColKind_s21c(othCollKind);

			// J-110-0485_05097_B1009_B Web
			// e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
			if (this.hideRgstInfoForL120s21c(othCollKind)) {
				l120s21c.setColPreRgstAmt_s21c(null);
				l120s21c.setColRgstAmt_s21c(null);
			}

			// 改以擔保品回傳資料為主，免得兩邊計算不一致
			// errorMsg = this.calcL120s21c(l120s21c);
			//
			// if (Util.notEquals(errorMsg, "")) {
			// return errorMsg;
			// }

			this.save(l120s21c);

		}

		// 準備呼叫擔保品系統取得分配後擔保品**********************************************
		BigDecimal cntrCollAmt = BigDecimal.ZERO;
		BigDecimal cntrCollAmt_0503 = BigDecimal.ZERO;

		// 額度明細表沒有擔保品就不用呼叫擔保品系統算LGD分配
		List<L140M01O> l140m01os = this
				.findL140m01oByMainIdOrderForLgd(l140m01a.getMainId());
		if (l140m01os == null || l140m01os.isEmpty()) {
			// 沒有擔保品就不用去呼擔保品API
			cntrCollAmt = BigDecimal.ZERO;
			// l120s21b.setCollateralRecovery(cntrCollAmt);
			l120s21b.setCollateralRecoveryCms(cntrCollAmt);
			this.save(l120s21b);
			return "";
		}

		// 簽報書下所有額度明細表
		List<L140M01A> listL140m01a = l140m01aDao
				.findL140m01aListByL120m01cMainId(mainId,
						UtilConstants.Cntrdoc.ItemType.額度明細表, null);

		try {
			int sec = 30;

			KeyStore trustStore = KeyStore.getInstance(KeyStore
					.getDefaultType());
			trustStore.load(null, null);

			SSLSocketFactory sf = new RPAHttpSSLSocketFactory(trustStore);
			final HttpParams params = new BasicHttpParams();
			HttpConnectionParams.setStaleCheckingEnabled(params, false);
			HttpConnectionParams.setConnectionTimeout(params, sec * 1000);
			HttpConnectionParams.setSoTimeout(params, sec * 1000);
			HttpConnectionParams.setSocketBufferSize(params, 8192 * 5);
			SchemeRegistry registry = new SchemeRegistry();
			registry.register(new Scheme("http", PlainSocketFactory
					.getSocketFactory(), 80));
			registry.register(new Scheme("https", sf, 443));
			ClientConnectionManager ccm = new ThreadSafeClientConnManager(
					params, registry);
			HttpClient httpclient = new DefaultHttpClient(ccm, params);

			HttpPost httpPost = new HttpPost(
					sysParamService
							.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL));
			httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
			// 設定Header Authorization=token

			JSONObject reqJSONObj = new JSONObject();
			JSONObject request = new JSONObject();

			reqJSONObj.put("serviceId", "cmscollEDA_LGDService");
			reqJSONObj.put("vaildIP", "N");

			// J-111-0572_05097_B1001 Web e-Loan企金授信配合e-Loan LGD擔保品分配規則修改

			request.put("mainId", l120s21b.getMainId());
			request.put("cntrNo", cntrNo);

			JSONObject cntrEad = new JSONObject();
			cntrEad.putAll(cntrEadMap);

			request.put("cntrEad", cntrEad.toString());

			JSONArray ja = this.generateLgdCallCmsCntrData(l120s21b);
			if (ja.isEmpty()) {
				// 沒有擔保品就不用去呼擔保品API
				cntrCollAmt = BigDecimal.ZERO;
				// l120s21b.setCollateralRecovery(cntrCollAmt);
				l120s21b.setCollateralRecoveryCms(cntrCollAmt);
				this.save(l120s21b);
				return "";
			}
			request.put("cntrData", ja);

			reqJSONObj.put("request", request.toString());

			LOGGER.info("LGD CALL CMS Begin*******************************************************************");
			LOGGER.info(reqJSONObj.toString());

			StringEntity stringEntity = new StringEntity(reqJSONObj.toString(),
					"UTF-8");
			stringEntity.setContentEncoding("UTF-8");
			httpPost.setEntity(stringEntity);

			HttpResponse response = null;
			response = httpclient.execute(httpPost);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 回傳內容
				String content = EntityUtils.toString(response.getEntity(),
						"UTF-8");// UTF-8 big5
				LOGGER.info(content);
				// {"rc":0,"rcmsg":"SUCCESS","message":"執行成功","CollRecycle":"729063574"}

				// {"rc":0,"rcmsg":"SUCCESS","message":"執行成功","CollRecycle":"45662637","CollRecycleLABOR":0,"rateLABOR":0}
				//
				// CollRecycleLABOR 信保回收
				//
				// rateLABOR 信保成數

				JSONObject responseJson = JSONObject.fromObject(content);
				String rc = responseJson.optString("rc", "1");

				boolean isTestEmail = "true".equals(PropUtil
						.getProperty("isTestEmail")) ? true : false; // 是否為測試信件
				isTestEmail = false;

				// TODO
				// if (isTestEmail) {
				// rc = "0";
				// isTestEmail = false; // 控制jsonStr測試資料
				// }

				if (Util.equals(rc, "0")) {
					// SUCCESS
					LOGGER.info("Response SUCCESS");

					cntrCollAmt = Util.parseBigDecimal(responseJson.optString(
							"CollRecycle", "0"));

					// CollRecycleLABOR 信保回收
					// rateLABOR 信保成數

					// TODO
					// 產生L120S21C擔保品明細--已建檔
					// {"rc":0,"rcmsg":"SUCCESS","message":"執行成功","CollRecycle":"45662637","CollRecycleLABOR":0,"rateLABOR":0,"collData":""}
					JSONArray collData = null;
					if (isTestEmail) {
						// String jsonStr =
						// "{collData:[{'cmsBranch_s21c':'005','cmsTypeCd_s21c':'1','cmsCustId_s21c':'11111111','cmsDupNo_s21c':'0','cmsCollNo_s21c':'01-01-001','cmsOid_s21c':'11111111111111111111111111111111','cmsCollKey_s21c':'22222222222222222222222222222222','colKind_s21c':'020100','colCurr_s21c':'TWD','colTimeValue_s21c':'9999999','colPreRgstAmt_s21c':'1111111','colRgstAmt_s21c':'7777777','colCoUseFlag_s21c':'Y','colShareRate_s21c':'40.5'},{'cmsBranch_s21c':'005','cmsTypeCd_s21c':'1','cmsCustId_s21c':'22222222','cmsDupNo_s21c':'0','cmsCollNo_s21c':'01-01-002','cmsOid_s21c':'11111111111111111111111111111111','cmsCollKey_s21c':'22222222222222222222222222222222','colKind_s21c':'020100','colCurr_s21c':'TWD','colTimeValue_s21c':'8888888','colPreRgstAmt_s21c':'2222222','colRgstAmt_s21c':'6666666','colCoUseFlag_s21c':'Y','colShareRate_s21c':'80.0'},{'cmsBranch_s21c':'005','cmsTypeCd_s21c':'1','cmsCustId_s21c':'33333333','cmsDupNo_s21c':'0','cmsCollNo_s21c':'01-01-003','cmsOid_s21c':'11111111111111111111111111111111','cmsCollKey_s21c':'22222222222222222222222222222222','colKind_s21c':'020100','colCurr_s21c':'TWD','colTimeValue_s21c':'7777777','colPreRgstAmt_s21c':'0','colRgstAmt_s21c':'5555555','colCoUseFlag_s21c':'N','colShareRate_s21c':'0'}]}";
						String jsonStr = "collData':[{'cmsBranch_s21c':'0C3','cmsCustId_s21c':'','cmsOid_s21c':'8DD4E830F85211EC9A97FF21C0A83B1E','colCurr_s21c':'HKD','colTimeValue_s21c':'0','colPreRgstAmt_s21c':0,'colCoUseFlag_s21c':'N','colShareRate_s21c':'100.000000000000000000000000','majorType':'1','colKind_s21c':'050301','cmsGrtrt_s21c':'90','colEstRecovery_s21c':'95000.0000','colRgstRecovery_s21c':'95000.00','colRgstAmt_s21c':'0','twdRate':3.7002,'estDate':'2022-06-30','colRate_s21c':'0.95','colRecovery_s21c':'95000.00','colRecoveryTwd_s21c':'351519.00'}";
						JSONObject jobjectD = JSONObject.fromObject(jsonStr);
						collData = jobjectD.getJSONArray("collData");
					} else {
						collData = responseJson.optJSONArray("collData");
					}

					if (collData != null) {

						for (int j = 1; j < collData.size() + 1; j++) {
							// JSONObject json =
							// JSONObject.fromObject(collData.getString(j - 1));

							L120S21C l120s21c = new L120S21C();
							l120s21c.setMainId(l120s21b.getMainId());
							l120s21c.setCollType_s21c("1");// 已建檔
							l120s21c.setCntrNo_s21c(l120s21b.getCntrNo_s21b());
							l120s21c.setCreator(user.getUserId());
							l120s21c.setCreateTime(CapDate
									.getCurrentTimestamp());

							DataParse.toBean(collData.getString(j - 1),
									l120s21c);

							// J-111-0083_05097_B1002 Web
							// e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
							// 擔保品回傳回收率0.75，要乘上100
							if (l120s21c.getColRate_s21c() != null) {
								l120s21c.setColRate_s21c(l120s21c
										.getColRate_s21c().multiply(
												new BigDecimal(100)));
							}

							// J-110-0485_05097_B1008 Web
							// e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
							String othCollKind = Util.trim(l120s21c
									.getColKind_s21c()); // L120S21C.colKind_s21c=擔保品種類

							// J-111-0400_05097_B1002 Web
							// e-Loan企金授信增修LGD及額度暴險估算規則
							if (Util.equals(
									Util.trim(l120s21c.getColCoUseFlag_s21c()),
									"N")) {
								// 是否與其他額度共用為否，分配比率預設100
								l120s21c.setColShareRate_s21c(new BigDecimal(
										100));
							} else if (Util.equals(
									Util.getLeftStr(othCollKind, 6), "050300")) {
								// 中小信保因為雨青不分配會帶0，所以風控處邱煥祥要求改為100
								// [星期一 下午 01:35] 邱煥翔(風險控管處,襄理)
								// [星期一 下午 01:37] 邱煥翔(風險控管處,襄理)
								// 為何分配比率為0
								l120s21c.setColShareRate_s21c(new BigDecimal(
										100));
							}

							if (Util.equals(Util.getLeftStr(othCollKind, 4),
									"0503")) {
								cntrCollAmt_0503 = cntrCollAmt_0503
										.add(l120s21c.getColRecoveryTwd_s21c());
							}

							// J-110-0485_05097_B1009_B Web
							// e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
							if (this.hideRgstInfoForL120s21c(othCollKind)) {
								l120s21c.setColPreRgstAmt_s21c(null);
								l120s21c.setColRgstAmt_s21c(null);
							}

							// J-111-0572_05097_B1001 Web e-Loan企金授信配合e-Loan
							// LGD擔保品分配規則修改
							// 分配比率由小數2位變4位
							JSONObject json = JSONObject.fromObject(collData
									.getString(j - 1));
							if (Util.isNotEmpty(json.optString(
									"colShareRate_s21c", ""))) {
								BigDecimal colShareRate_s21c = new BigDecimal(
										json.optString("colShareRate_s21c"))
										.setScale(4, BigDecimal.ROUND_HALF_UP);
								if (colShareRate_s21c.compareTo(new BigDecimal(
										100)) > 0) {
									l120s21c.setColShareRate_s21c(new BigDecimal(
											100));
								} else {
									l120s21c.setColShareRate_s21c(colShareRate_s21c);
								}

							}

							// 改以擔保品回傳資料為主，免得兩邊計算不一致
							// errorMsg = this.calcL120s21c(l120s21c);
							//
							// if (Util.notEquals(errorMsg, "")) {
							// return errorMsg;
							// }

							this.save(l120s21c);
						}
					}

					// J-110-0485_05097_B1008 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
					l120s21b.setCollateralRecoveryCms((cntrCollAmt
							.subtract(cntrCollAmt_0503)).max(BigDecimal.ZERO));

					this.save(l120s21b);
				} else {
					// FAIL
					errorMsg = "額度序號「" + l120s21b.getCntrNo_s21b()
							+ "」 Response FAIL:"
							+ responseJson.optString("message", "FAIL");

					LOGGER.error(errorMsg);
				}

			} else {
				errorMsg = "HTTP ERROR:"
						+ response.getStatusLine().getStatusCode();
			}
		} catch (IOException ioe) {
			errorMsg = StrUtils.getStackTrace(ioe);
			LOGGER.error(errorMsg);
		} catch (Exception e) {
			errorMsg = StrUtils.getStackTrace(e);
			LOGGER.error(errorMsg);
		} finally {

		}

		return errorMsg;
	}

	/**
	 * J-111-0572_05097_B1001 Web e-Loan企金授信配合e-Loan LGD擔保品分配規則修改
	 * 
	 * @param l120s21b
	 * @return
	 */
	private JSONArray generateLgdCallCmsCntrData(L120S21B l120s21b) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(l120s21b.getMainId());

		// 簽報書下所有額度明細表
		List<L140M01A> listL140m01a = l140m01aDao
				.findL140m01aListByL120m01cMainId(mainId,
						UtilConstants.Cntrdoc.ItemType.額度明細表, null);

		JSONArray ja = new JSONArray();

		for (L140M01A l140m01a : listL140m01a) {

			JSONObject request = new JSONObject();

			JSONObject collKey = new JSONObject();

			String cntrNo = Util.nullToSpace(l140m01a.getCntrNo());
			request.put("cntrNo", cntrNo);

			// 沒有額度明細表以擔保品建檔為主
			List<String> cntrNos = new ArrayList<String>();
			cntrNos.add(cntrNo);

			// 有額度明細表要以額度明細表擔保品連結為主，沒有額度明細表以擔保品建檔為主
			// if (Util.equals(Util.trim(l120s21b.getHasCntrDoc_s21b()), "Y")) {
			// 有額度明細表要以額度明細表擔保品連結為主
			List<L140M01O> l140m01os = this
					.findL140m01oByMainIdOrderForLgd(l140m01a.getMainId());
			if (l140m01os != null && !l140m01os.isEmpty()) {
				for (L140M01O l140m01o : l140m01os) {
					// J-112-0210_05097_B1001 Web
					// e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
					// [下午 03:56] 邱煥翔(風險控管處,襄理)
					// 經與授審處討論  機器設備請不要顯示在LGD頁面及違約損失率明細表之 擔保品項目
					if (Util.equals(Util.getLeftStr(l140m01o.getCollNo(), 5),
							"02-01")) {
						continue;
					}

					String cmsOid = Util.trim(l140m01o.getCmsOid());
					String cmsCollKey = Util.trim(l140m01o.getCmsCollKey());

					if (Util.notEquals(cmsOid, "")
							&& Util.equals(cmsCollKey, "")) {
						List<Map<String, Object>> cmsList = eloandbBASEService
								.findCms_selByOid(cmsOid);
						if (cmsList != null && !cmsList.isEmpty()) {
							for (Map<String, Object> cmsMap : cmsList) {

								cmsCollKey = Util.trim(MapUtils.getString(
										cmsMap, "COLLKEY"));

								if (Util.notEquals(cmsCollKey, "")) {
									l140m01o.setCmsCollKey(cmsCollKey);
									l140m01oDao.save(l140m01o);
									break;
								}
							}
						}
					}
					collKey.put(cmsOid, cmsCollKey);
				}
			}
			// }

			// J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
			// 都沒有擔保品，則抓擔保品管理系統
			if (collKey.isEmpty()) {
				if (applyCmsCollDataWhenNoL140m01o()) {
					List<Map<String, Object>> cmsList = eloandbBASEService
							.findCmsC100m01ByCntrNoForLgd(cntrNos);
					for (Map<String, Object> cmsMap : cmsList) {

						// J-112-0210_05097_B1001 Web
						// e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
						// [下午 03:56] 邱煥翔(風險控管處,襄理)
						// 經與授審處討論  機器設備請不要顯示在LGD頁面及違約損失率明細表之 擔保品項目
						String COLLNO = Util.trim(MapUtils.getString(cmsMap,
								"COLLNO"));
						if (Util.equals(Util.getLeftStr(COLLNO, 5), "02-01")) {
							continue;
						}
						collKey.put(MapUtils.getString(cmsMap, "OID"),
								MapUtils.getString(cmsMap, "COLLKEY"));
					}
				}
			}
			request.put("collKey", collKey.toString());

			if (Util.equals(Util.trim(l140m01a.getUnitCase2()), "Y")) {
				request.put("unionFlag", Util.trim(l140m01a.getUnitCase2()));
				request.put("syndLoanCurr",
						Util.trim(l140m01a.getSyndLoanCurr())); // 聯貸幣別
				request.put("syndAmt",
						l140m01a.getSyndLoanPart() == null ? BigDecimal.ZERO
								: l140m01a.getSyndLoanPart()); // 本行參貸額度
				request.put("unionAmt",
						l140m01a.getSyndLoanTotal() == null ? BigDecimal.ZERO
								: l140m01a.getSyndLoanTotal()); // 聯合授信案總金額
			} else {
				request.put("unionFlag", "N");
				request.put("syndLoanCurr", ""); // 聯貸幣別
				request.put("syndAmt", BigDecimal.ZERO); // 本行參貸額度
				request.put("unionAmt", BigDecimal.ZERO); // 聯合授信案總金額
			}

			request.put("brNo", user.getUnitNo());

			ja.add(request);
		}

		return ja;
	}

	/**
	 * J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	 * 
	 * @return
	 */
	public boolean applyCmsCollDataWhenNoL140m01o() {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String ssoUnitNo = user.getSsoUnitNo();
		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean canApply = false;

		// LMS_LGD_APPLY_CMS_WHEN_NODATA 900 額度明細表沒有擔保品時，預設引進已設定擔保品系統資料

		String LMS_LGD_APPLY_CMS_WHEN_NODATA = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_APPLY_CMS_WHEN_NODATA"));

		if (Util.notEquals(LMS_LGD_APPLY_CMS_WHEN_NODATA, "")) {
			for (String xx : LMS_LGD_APPLY_CMS_WHEN_NODATA.split(",")) {
				if (Util.equals(xx, ssoUnitNo)) {
					canApply = true;
					break;
				}
			}
		}

		return canApply;
	}

	/**
	 * J-111-0572_05097_B1001 Web e-Loan企金授信配合e-Loan LGD擔保品分配規則修改
	 */
	@Override
	public List<L140M01O> findL140m01oByMainIdOrderForLgd(String mainId) {
		// 透過MainId取得多筆資料
		return l140m01oDao.findByMainIdOrderForLgd(mainId);
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21b
	 * @return
	 * @throws Exception
	 */
	@Override
	public String chkIsGuarantorEffect_s21b(L120S21B l120s21b) {
		String isEffect = "";
		String errMsg = "";
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSLgdCommomPage.class);
		Map<String, String> errMap = new HashMap<String, String>();

		if (l120s21b == null) {
			return isEffect;
		}

		String custId = l120s21b.getCustId_s21b();
		String dupNo = l120s21b.getDupNo_s21b();
		String cntrNo = l120s21b.getCntrNo_s21b();
		String mainId = l120s21b.getMainId();

		if (Util.equals(Util.trim(l120s21b.getHasGuarantor_s21b()), "Y")) {

			// J-114-XXX1 LGD合格保證人納入境外保證人
			String isGuarantorEffect_s21b = Util.trim(l120s21b
					.getIsGuarantorEffect_s21b());

			if (Util.equals(isGuarantorEffect_s21b, "1")
					|| Util.equals(isGuarantorEffect_s21b, "2")) {
				L120M01A l120m01a = l120m01aDao.findByMainId(l120s21b
						.getMainId());

				BranchRate branchRate = lmsService.getBranchRate(l120m01a
						.getCaseBrId());

				if (l120m01a == null) {
					return isEffect;
				}

				List<L120S01C> l120s01cs = l120s01cDao.findByCustId(mainId,
						custId, dupNo);
				if (l120s01cs == null || l120s01cs.isEmpty()) {
					// L120S21B.guarantor.msg01=借款人無內部評等資料
					return pop.getProperty("L120S21B.guarantor.msg01");
				}
				L120S01C l120s01c_M = null;
				for (L120S01C l120s01c : l120s01cs) {
					String custCrdType = Util.trim(l120s01c.getCrdType());
					if (Util.equals(Util.getLeftStr(Util.trim(custCrdType), 1),
							"M")) {
						l120s01c_M = l120s01c;
						break;
					}
				}
				if (l120s01c_M == null) {
					// L120S21B.guarantor.msg01=借款人無內部評等資料
					return pop.getProperty("L120S21B.guarantor.msg01");
				}

				// PD GRADE TO SCORE
				List<Map<String, Object>> lnf25cListType4 = misdbBASEService
						.getLnf25cAllMowScoreMappingByType("4");
				Map<String, String> pdGradeMap = new HashMap<String, String>();
				if (lnf25cListType4 != null && !lnf25cListType4.isEmpty()) {
					for (Map<String, Object> lnf25cMap4 : lnf25cListType4) {
						String LNF25C_MAP_VALUE1 = Util.trim(MapUtils
								.getString(lnf25cMap4, "LNF25C_MAP_VALUE1"));
						String LNF25C_MAP_VALUE2 = Util.trim(MapUtils
								.getString(lnf25cMap4, "LNF25C_MAP_VALUE2"));
						pdGradeMap.put(LNF25C_MAP_VALUE1, LNF25C_MAP_VALUE2);
					}
				}

				// 借款人基本資料-借款人內部評等
				String custGrade = Util.trim(l120s01c_M.getGrade());
				String custCrdType = Util.trim(l120s01c_M.getCrdType());
				custGrade = this.convertMowGrade(custCrdType, custGrade); // 轉換後
				String[] codeType = { "CRDType" };
				String custGradeToScore = MapUtils.getString(pdGradeMap,
						custGrade, "999");
				Map<String, CapAjaxFormResult> codeMap = codeTypeService
						.findByCodeType(codeType);

				String custGradeMsg = "N.A.";
				custGradeMsg = Util.equals(custCrdType, "")
						|| Util.equals(custCrdType,
								UtilConstants.Casedoc.CrdType.未評等) ? "N.A."
						: (Util.trim(codeMap.get("CRDType").get(
								Util.trim(custCrdType)))
								+ "-" + custGrade);

				// 若保證人符合以下 所有 條件時，回收率維持 44%，否則為 21%( 視作無保證人)。
				// a.為國內上市上櫃公司且一般評等7等(特殊融資2級)以上，且非外國企業來台上市(KY公司者)。
				// b.且企業規模(實收資本額)大於等於借款人。
				// c.評等優於等於借款人。

				// [下午 02:41] 楊竺軒(風險控管處,高級辦事員)
				// 建霖好，不好意思依授審處的意思，國內海外連保公司判斷條件要相同，海外請加回股票上市上櫃情形，一樣要是台灣上市櫃公司才可以符合條件，謝謝~

				// if (!UtilConstants.Casedoc.typCd.海外.equals(Util.trim(l120m01a
				// .getTypCd()))) {

				// **************************************************************
				// 共同檢核
				// **************************************************************

				// 且企業規模(實收資本額)大於等於借款人
				// 實收資本額（幣別） cptlCurr
				// 實收資本額（金額） cptlAmt
				// 實收資本額（單位） cptlUnit

				L120S01B l120s01b = l120s01bDao.findByUniqueKey(mainId, custId,
						dupNo);
				boolean cptlOk = false;
				String cptlCurr = "";
				BigDecimal cptlAmt = null;
				BigDecimal cptlUnit = null;
				BigDecimal cptl = null;
				BigDecimal guaCptl = null;
				if (l120s01b != null) {

					if (Util.equals(isGuarantorEffect_s21b, "2")) {
						// 淨值
						cptlCurr = Util.trim(l120s01b.getNetSwft());
						cptlAmt = Util.parseBigDecimal(l120s01b.getNetAmt());
						cptlUnit = Util.parseBigDecimal(l120s01b
								.getNetAmtUnit());
					} else {
						// 實收資本額
						cptlCurr = Util.trim(l120s01b.getCptlCurr());
						cptlAmt = Util.parseBigDecimal(l120s01b.getCptlAmt());
						cptlUnit = Util.parseBigDecimal(l120s01b.getCptlUnit());
					}

					if (Util.isNotEmpty(cptlCurr) && cptlAmt != null
							&& cptlUnit != null) {
						// L120S21B.guarantorCptl=實收資本額
						// L120S21B.guarantorCptlCurr_s21b=實收資本額-幣別
						// L120S21B.guarantorCptlAmt_s21b=實收資本額-金額

						String guarantorCptlCurr_s21b = Util.trim(l120s21b
								.getGuarantorCptlCurr_s21b());
						BigDecimal guarantorCptlAmt_s21b = Util
								.parseBigDecimal(l120s21b
										.getGuarantorCptlAmt_s21b());
						BigDecimal guarantorCptlUnit_s21b = Util
								.parseBigDecimal(l120s21b
										.getGuarantorCptlUnit_s21b());
						if (Util.isNotEmpty(guarantorCptlCurr_s21b)
								&& guarantorCptlAmt_s21b != null
								&& guarantorCptlUnit_s21b != null) {

							if (Util.notEquals(cptlCurr, guarantorCptlCurr_s21b)) {
								// 不同幣別要轉換
								guarantorCptlAmt_s21b = branchRate.toOtherAmt(
										guarantorCptlCurr_s21b, cptlCurr,
										guarantorCptlAmt_s21b);
							}

							cptl = cptlAmt.multiply(cptlUnit);
							guaCptl = guarantorCptlAmt_s21b
									.multiply(guarantorCptlUnit_s21b);

							if (guaCptl.compareTo(cptl) >= 0) {
								cptlOk = true;
							}
						}
					}
				}

				if (!cptlOk) {
					String cptlName = pop.getProperty("L120S21B.guarantorCptl"); // 實收資本額
					if (Util.equals(isGuarantorEffect_s21b, "2")) {
						// 淨值
						cptlName = pop
								.getProperty("L120S21B.guarantorNetWorth");
					}

					// L120S21B.guarantorCptl=實收資本額
					if (l120s01b != null) {
						if (Util.isNotEmpty(cptlCurr) && cptlAmt != null
								&& cptlUnit != null) {
							// L120S21B.custBaseData=借款人基本資料
							// L120S21B.guarantorCptl=實收資本額
							errMap.put(
									cptlName
											+ "("
											+ pop.getProperty("L120S21B.custBaseData")
											+ ":"
											+ cptlCurr
											+ " "
											+ NumConverter.addComma(Util
													.nullToSpace(cptl)) + ")",
									"");
						} else {
							errMap.put(cptlName, "");
						}

					} else {
						errMap.put(cptlName, "");
					}

				}

				// **************************************************************
				// (1)
				// a.為國內上市上櫃公司且一般評等7等(特殊融資2級)以上，且非外國企業來台上市(KY公司)者。
				// b.且企業規模(實收資本額)大於等於借款人。
				// c.評等優於等於借款人。
				// **************************************************************

				if (Util.equals(isGuarantorEffect_s21b, "1")) {

					// 非外國企業來台上市(KY公司者)
					String guarantorNtCode_s21b = Util.trim(l120s21b
							.getGuarantorNtCode_s21b());
					if (Util.equals(guarantorNtCode_s21b, "KY")
							|| Util.equals(guarantorNtCode_s21b, "")) {
						// L120S21B.guarantorNtCode_s21b=註冊地國別
						errMap.put(pop
								.getProperty("L120S21B.guarantorNtCode_s21b"),
								"");
					}

					// 為國內上市上櫃公司
					String guarantorStockStatus_s21b = Util.trim(l120s21b
							.getGuarantorStockStatus_s21b());
					if (Util.notEquals(guarantorStockStatus_s21b, "1")
							&& Util.notEquals(guarantorStockStatus_s21b, "2")) {
						// 上市上櫃
						// L120S21B.guarantorStockStatus_s21b=股票上市上櫃情形
						errMap.put(
								pop.getProperty("L120S21B.guarantorStockStatus_s21b"),
								"");
					}
					// }

					// 一般評等7等(特殊融資2級)以上
					// 評等優於等於借款人
					String guarantorGradeNew_s21b = Util.trim(l120s21b
							.getGuarantorGradeNew_s21b());

					String gradeToScore = MapUtils.getString(pdGradeMap,
							guarantorGradeNew_s21b, "999");

					if (Util.parseBigDecimal(gradeToScore).compareTo(
							Util.parseBigDecimal(MapUtils.getString(pdGradeMap,
									"7", "41"))) > 0) {
						// L120S21B.guarantorGradeOrg_s21b=評等等級
						errMap.put(
								pop.getProperty("L120S21B.guarantorGradeOrg_s21b"),
								"");
					} else {
						// 評等優於等於借款人
						// 跟借款人比

						boolean crdOk = false;

						if (Util.notEquals(custGrade, "")
								&& Util.notEquals(guarantorGradeNew_s21b, "")) {
							if (Util.parseInt(custGrade) >= Util
									.parseInt(guarantorGradeNew_s21b)) {
								crdOk = true;
							}
						}

						if (!crdOk) {
							// L120S21B.custBaseData=借款人基本資料
							// L120S21B.guarantor.msg02=優於等於借款人
							errMap.put(
									pop.getProperty("L120S21B.guarantorGradeOrg_s21b")
											+ pop.getProperty("L120S21B.guarantor.msg02")
											+ (Util.notEquals(custGradeMsg, "") ? ("("
													+ pop.getProperty("L120S21B.custBaseData")
													+ "：" + custGradeMsg + ")")
													: ""), "");
						}

					}

				}

				// **************************************************************
				// (2)
				// a.
				// 1.境外公司有外部信評(S&P、Moody's或Fitch）且達本行內評相當7等以上，
				// 2.或是沒有外部信評但本行內部評等達7等,
				// 並在本行所認可之海外交易所掛牌者。
				// b.且企業規模(淨值)大於等於借款人。
				// c.評等優於等於借款人。
				// **************************************************************
				if (Util.equals(isGuarantorEffect_s21b, "2")) {

					// a.
					// 1.境外公司有外部信評(S&P、Moody's或Fitch）且達本行內評相當7等以上，
					// 2.或是沒有外部信評但本行內部評等達7等,

					// 境外公司
					if (Util.equals(Util.trim(l120s21b.getGuarantorId_s21b())
							.substring(2, 3), "Z")) {
						// 有外部信評(S&P、Moody's或Fitch）且達本行內評相當7等以上
						int ovsGradeOk = 999;
						String guarantorCrdType2_s21b = Util.trim(l120s21b
								.getGuarantorCrdType2_s21b());
						if (Util.notEquals(guarantorCrdType2_s21b, "")) {
							String guarantorGradeNew2_s21b = Util.trim(l120s21b
									.getGuarantorGradeNew2_s21b());
							if (Util.notEquals(guarantorGradeNew2_s21b, "")) {
								if (Util.parseBigDecimal(
										guarantorGradeNew2_s21b).compareTo(
										Util.parseBigDecimal(MapUtils
												.getString(pdGradeMap, "7",
														"41"))) <= 0) {
									// 分數越小越好，外部評等 <= 41 : 七等或更好
									ovsGradeOk = Util
											.parseInt(guarantorGradeNew2_s21b);
								}
							}
						}

						// 沒有外部信評，但本行內部評等達7等並在本行所認可之海外交易所掛牌者。
						int ovsGradeOk2 = 999;
						String guarantorStkCatNm_s21b = Util.trim(l120s21b
								.getGuarantorStkCatNm_s21b());
						if (Util.notEquals(guarantorStkCatNm_s21b, "")) {
							// 沒有外部信評但本行內部評等達7等並在本行所認可之海外交易所掛牌者。
							String guarantorGradeNew_s21b = Util.trim(l120s21b
									.getGuarantorGradeNew_s21b());
							String gradeToScore = MapUtils.getString(
									pdGradeMap, guarantorGradeNew_s21b, "999");

							if (Util.parseBigDecimal(gradeToScore).compareTo(
									Util.parseBigDecimal(MapUtils.getString(
											pdGradeMap, "7", "41"))) <= 0) {
								ovsGradeOk2 = Util.parseInt(gradeToScore);
							}
						}

						if (ovsGradeOk == 999 && ovsGradeOk2 == 999) {
							// 沒有合法的評等a
							// L120S21B.guarantorEffectType_s21b_2.a=a.境外公司有外部信評(S&P、Moody's或Fitch）且達本行內評相當7等以上，或是沒有外部信評但本行內部評等達7等,
							// 並在本行所認可之海外交易所掛牌者。
							errMap.put(
									pop.getProperty("L120S21B.guarantorEffectType_s21b_2.a"),
									"");
						} else {
							// c.評等優於等於借款人。
							// 有合格評等，且評等優於等於借款人。
							if (Util.parseBigDecimal(custGradeToScore)
									.compareTo(new BigDecimal(ovsGradeOk)) >= 0
									|| Util.parseBigDecimal(custGradeToScore)
											.compareTo(
													new BigDecimal(ovsGradeOk2)) >= 0) {
								// 檢核通過，有合格信評，且信用評等優於等於借款人
							} else {
								// L120S21B.custBaseData=借款人基本資料
								// L120S21B.guarantorEffectType_s21b_2.c=c.且信用評等優於等於借款人。

								// J-113-0102_05097_B1001 修改e-Loan
								// LGD之公司保證回收率估算規則
								// if (true) 是為了處理思恩過版超車
								if (true) {
									errMap.put(
											pop.getProperty("L120S21B.guarantorEffectType_s21b_2.c")
													+ (Util.notEquals(
															custGradeMsg, "") ? ("("
															+ pop.getProperty("L120S21B.custBaseData")
															+ "："
															+ custGradeMsg + ")")
															: ""), "");
								}
							}
						}
					} else {
						// 非境外公司
						// L120S21B.guarantorEffectType_s21b_2.a=a.境外公司有外部信評(S&P、Moody's或Fitch）且達本行內評相當7等以上，或是沒有外部信評但本行內部評等達7等,
						// 並在本行所認可之海外交易所掛牌者。
						errMap.put(
								pop.getProperty("L120S21B.guarantorEffectType_s21b_2.a"),
								"");
					}

				}

			}
		}

		if (errMap != null && !errMap.isEmpty()) {
			StringBuffer errbuff = new StringBuffer("");
			for (String key : errMap.keySet()) {
				if (Util.equals(errbuff, "")) {
					errbuff.append(key);
				} else {
					errbuff.append("、").append(key);
				}
			}
			isEffect = errbuff.toString();

			// #J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
			if (Util.notEquals(isEffect, "")) {
				// lgd.errMsg18=額度序號「{0}」本案「保證人/共借人」並不符合一定條件(下列a~c款)，係因「{1}」不符合條件。
				// L120S21B.isGuarantorEffect_s21b=本案是否有保證人/共借人符合下列a~c之所有條件
				errMsg = MessageFormat.format(pop.getProperty("lgd.errMsg18"),
						cntrNo, isEffect);
			}

		}

		return errMsg;
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param crdType
	 * @param gradeOrg
	 * @return
	 */
	@Override
	public String convertMowGrade(String crdType, String gradeOrg) {
		String gradeNew = gradeOrg;

		// 特殊融資1~4等分別對應至PD等級之第6、7、9、13等
		if (lmsService.is4GradeMow(crdType)) {
			// 特殊融資模型評等等級1-2級;
			if ("1".equals(gradeOrg)) {
				gradeNew = "6";
			} else if ("2".equals(gradeOrg)) {
				gradeNew = "7";
			} else if ("3".equals(gradeOrg)) {
				gradeNew = "9";
			} else {
				gradeNew = "13";
			}
		}

		return gradeNew;
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21b
	 * @return
	 */
	@Override
	public void chkL120s21b(L120S21B l120s21b) throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSLgdCommomPage.class);

		StringBuffer temp = new StringBuffer();

		L120M01A l120m01a = l120m01aDao.findByMainId(l120s21b.getMainId());

		// 統計未填欄位數
		int countItme = 1;

		if (Util.isEmpty(l120s21b.getHasGuarantor_s21b())) {
			// L120S21B.hasGuarantor_s21b=有無公司保證人
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S21B.hasGuarantor_s21b"));
		}

		if (Util.isEmpty(l120s21b.getHeadItem1_s21b())) {
			// L120S21B.headItem1_s21b=本額度有無送保
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S21B.headItem1_s21b"));
		}

		if (Util.equals(Util.trim(l120s21b.getHeadItem1_s21b()), "Y")) {
			if (Util.isEmpty(l120s21b.getGutPercent_s21b())) {
				// L120S21B.gutPercent_s21b=信保保證成數
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21B.gutPercent_s21b"));
			}
		}

		if (Util.isEmpty(l120s21b.getUnionFlag())) {
			// L120S21B.unionFlag=是否為聯貸案
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S21B.unionFlag"));
		}

		if (Util.equals(Util.trim(l120s21b.getUnionFlag()), "Y")) {
			if (Util.isEmpty(l120s21b.getUnionCurr())) {
				// L120S21B.unionCurr=聯貸幣別
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21B.unionCurr"));
			}

			if (Util.isEmpty(l120s21b.getSyndAmt())) {
				// L120S21B.syndAmt=本行參貸額度(預計)
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21B.syndAmt"));
			}

			if (Util.isEmpty(l120s21b.getUnionAmt())) {
				// L120S21B.unionAmt=聯合授信案總金額(預計)
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21B.unionAmt"));
			}

		}

		// J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
		if (Util.equals(Util.trim(l120s21b.getHasGuarantor_s21b()), "Y")) {
			if (Util.isEmpty(Util.trim(l120s21b.getIsGuarantorEffect_s21b()))) {
				// L120S21B.isGuarantorEffect_s21b=本案是否有保證人/共借人符合下列a~c之所有條件
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21B.isGuarantorEffect_s21b"));
			}

			// J-114-XXX1 LGD合格保證人納入境外保證人
			if (Util.equals(Util.trim(l120s21b.getIsGuarantorEffect_s21b()),
					"1")
					|| Util.equals(
							Util.trim(l120s21b.getIsGuarantorEffect_s21b()),
							"2")) {
				if (Util.isEmpty(Util.trim(l120s21b.getGuarantorId_s21b()))) {
					// L120S21B.guarantorId_s21b=統編
					countItme = this.setHtmlBr(temp, countItme,
							pop.getProperty("L120S21B.guarantorId_s21b"));
				}

				if (Util.isEmpty(Util.trim(l120s21b.getGuarantorDupNo_s21b()))) {
					// L120S21B.guarantorDupNo_s21b=重覆序號
					countItme = this.setHtmlBr(temp, countItme,
							pop.getProperty("L120S21B.guarantorDupNo_s21b"));
				}

				if (Util.isEmpty(Util.trim(l120s21b.getGuarantorRName_s21b()))) {
					// L120S21B.guarantorRName_s21b=戶名
					countItme = this.setHtmlBr(temp, countItme,
							pop.getProperty("L120S21B.guarantorRName_s21b"));
				}

				// J-114-XXX1 LGD合格保證人納入境外保證人
				if (Util.equals(
						Util.trim(l120s21b.getIsGuarantorEffect_s21b()), "1")) {

					if (Util.isEmpty(Util.trim(l120s21b
							.getGuarantorCrdType_s21b()))) {
						// L120S21B.guarantorCrdType_s21b=評等種類
						countItme = this.setHtmlBr(temp, countItme, pop
								.getProperty("L120S21B.guarantorCrdType_s21b"));
					}

					if (l120s21b.getGuarantorCrdTYear_s21b() == null) {
						// L120S21B.guarantorCrdTYear_s21b=評等日期
						countItme = this
								.setHtmlBr(
										temp,
										countItme,
										pop.getProperty("L120S21B.guarantorCrdTYear_s21b"));
					}

					if (Util.isEmpty(Util.trim(l120s21b
							.getGuarantorGradeOrg_s21b()))) {
						// L120S21B.guarantorGradeOrg_s21b=評等等級
						countItme = this
								.setHtmlBr(
										temp,
										countItme,
										pop.getProperty("L120S21B.guarantorGradeOrg_s21b"));
					}
				}

				// J-114-XXX1 LGD合格保證人納入境外保證人
				String cptlName = pop.getProperty("L120S21B.guarantorCptl"); // 實收資本額
				if (Util.equals(
						Util.trim(l120s21b.getIsGuarantorEffect_s21b()), "2")) {
					cptlName = pop.getProperty("L120S21B.guarantorNetWorth"); // 淨值
				}

				if (Util.isEmpty(Util.trim(l120s21b.getGuarantorCptlCurr_s21b()))) {
					// L120S21B.guarantorCptlCurr_s21b=實收資本額-幣別
					countItme = this
							.setHtmlBr(
									temp,
									countItme,
									cptlName
											+ "-"
											+ pop.getProperty("L120S21B.guarantorCptlCurr_s21b"));
				}

				if (l120s21b.getGuarantorCptlAmt_s21b() == null) {
					// L120S21B.guarantorCptlAmt_s21b=實收資本額-金額
					countItme = this
							.setHtmlBr(
									temp,
									countItme,
									cptlName
											+ "-"
											+ pop.getProperty("L120S21B.guarantorCptlAmt_s21b"));
				}

				if (l120s21b.getGuarantorCptlUnit_s21b() == null) {
					// L120S21B.guarantorCptlUnit_s21b=實收資本額-單位
					countItme = this
							.setHtmlBr(
									temp,
									countItme,
									cptlName
											+ "-"
											+ pop.getProperty("L120S21B.guarantorCptlUnit_s21b"));
				}

				String cptlCurr = "";
				BigDecimal cptlAmt = null;
				BigDecimal cptlUnit = null;

				L120S01B l120s01b = l120s01bDao.findByUniqueKey(
						l120s21b.getMainId(),
						Util.trim(l120s21b.getCustId_s21b()),
						Util.trim(l120s21b.getDupNo_s21b()));

				if (l120s01b != null) {

					// J-114-XXX1 LGD合格保證人納入境外保證人
					if (Util.equals(
							Util.trim(l120s21b.getIsGuarantorEffect_s21b()),
							"2")) {
						// 淨值
						cptlCurr = Util.trim(l120s01b.getNetSwft());
						cptlAmt = Util.parseBigDecimal(l120s01b.getNetAmt());
						cptlUnit = Util.parseBigDecimal(l120s01b
								.getNetAmtUnit());
					} else {
						// 實收資本額
						cptlCurr = Util.trim(l120s01b.getCptlCurr());
						cptlAmt = Util.parseBigDecimal(l120s01b.getCptlAmt());
						cptlUnit = Util.parseBigDecimal(l120s01b.getCptlUnit());
					}

				}

				if (Util.isEmpty(cptlCurr) || cptlAmt == null
						|| cptlUnit == null) {
					// L120S21B.custBaseData=借款人基本資料
					// L120S21B.guarantorCptl=實收資本額
					countItme = this.setHtmlBr(temp, countItme, cptlName + "("
							+ pop.getProperty("L120S21B.custBaseData") + ")");
				}

				if (Util.isEmpty(Util.trim(l120s21b.getGuarantorNtCode_s21b()))) {
					// L120S21B.guarantorNtCode_s21b=註冊地國別
					countItme = this.setHtmlBr(temp, countItme,
							pop.getProperty("L120S21B.guarantorNtCode_s21b"));
				}

				// J-114-XXX1 LGD合格保證人納入境外保證人
				if (Util.equals(
						Util.trim(l120s21b.getIsGuarantorEffect_s21b()), "1")) {
					// if
					// (!UtilConstants.Casedoc.typCd.海外.equals(Util.trim(l120m01a
					// .getTypCd()))) {
					// if
					// (!UtilConstants.Casedoc.typCd.海外.equals(Util.trim(l120m01a
					// .getTypCd()))) {
					if (Util.isEmpty(Util.trim(l120s21b
							.getGuarantorStockStatus_s21b()))) {
						// L120S21B.guarantorStockStatus_s21b=股票上市上櫃情形
						countItme = this
								.setHtmlBr(
										temp,
										countItme,
										pop.getProperty("L120S21B.guarantorStockStatus_s21b"));
					}

					if (Util.isEmpty(Util.trim(l120s21b
							.getGuarantorStockNum_s21b()))) {
						// L120S21B.guarantorStockNum_s21b=股票代號
						countItme = this
								.setHtmlBr(
										temp,
										countItme,
										pop.getProperty("L120S21B.guarantorStockNum_s21b"));
					}
					// }
				}

			}

		}

		if (temp.length() > 0) {
			// errMsg13=尚有必填欄位未填
			temp.insert(0, pop.getProperty("errMsg13") + "<br/>");
			throw new CapMessageException(
					RespMsgHelper.getMessage(
							UtilConstants.AJAX_RSP_MSG.執行有誤,
							"「" + l120s21b.getCntrNo_s21b() + "」"
									+ temp.toString()), getClass());
		}

		// 檢核 *****************************************************************
		// J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
		if (Util.equals(Util.trim(l120s21b.getHasGuarantor_s21b()), "Y")) {

			// J-114-XXX1 LGD合格保證人納入境外保證人
			if (Util.equals(Util.trim(l120s21b.getIsGuarantorEffect_s21b()),
					"1")
					|| Util.equals(
							Util.trim(l120s21b.getIsGuarantorEffect_s21b()),
							"2")) {
				String chkMsg = this.chkIsGuarantorEffect_s21b(l120s21b);
				if (Util.notEquals(chkMsg, "")) {
					// lgd.errMsg18=額度序號「{0}」欄位「{1}」為「是」，「{2}」不符合條件。
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, chkMsg),
							getClass());
				}
			}
		}

		return;

	}

	public int setHtmlBr(StringBuffer temp, int countItme, String showMessage) {
		int maxLenth = 5;
		temp.append(temp.length() > 0 ? "、" : "");
		if (countItme > maxLenth) {
			temp.append("<br/>");
			countItme = 1;
		} else {
			countItme++;
		}
		temp.append(showMessage);
		return countItme;
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21b
	 * @return
	 */
	@Override
	public BigDecimal getUnsecuredRecoveryRate(L120S21B l120s21b) {

		BigDecimal unsecuredRecoveryRateY = l120s21b
				.getUnsecuredRecoveryRateY().divide(new BigDecimal(100));
		BigDecimal unsecuredRecoveryRateN = l120s21b
				.getUnsecuredRecoveryRateN().divide(new BigDecimal(100));

		// J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
		BigDecimal unsecuredRecoveryRate = unsecuredRecoveryRateN; // 預設

		if (Util.equals(Util.nullToSpace(l120s21b.getHasGuarantor_s21b()), "Y")) {
			// 有公司保證者 =>(額度EAD(F9)- 預期產品自償回收(I9)-額度預期擔保品回收(K9)) * 無擔保回收 -
			// 有公司保證者 44%(E38)
			// J-114-XXX1 LGD合格保證人納入境外保證人
			if (Util.equals(Util.trim(l120s21b.getIsGuarantorEffect_s21b()),
					"1")
					|| Util.equals(
							Util.trim(l120s21b.getIsGuarantorEffect_s21b()),
							"2")) {
				// 判斷保證人要有效
				unsecuredRecoveryRate = unsecuredRecoveryRateY;
			} else if (Util.equals(
					Util.trim(l120s21b.getIsGuarantorEffect_s21b()), "N")) {
				// 無公司保證者 =>(額度EAD(F9)- 預期產品自償回收(I9)-額度預期擔保品回收(K9))
				// *
				// 無擔保回收 -
				// 無公司保證者 21%(E39)
				unsecuredRecoveryRate = unsecuredRecoveryRateN; // 無擔保回收率
			} else {
				// 考慮舊案沒有值，預設為Y

				// 有公司保證者 =>(額度EAD(F9)- 預期產品自償回收(I9)-額度預期擔保品回收(K9))
				// *
				// 無擔保回收 -
				// 有公司保證者 44%(E38)
				unsecuredRecoveryRate = unsecuredRecoveryRateY; // 無擔保回收率
			}
		}

		return unsecuredRecoveryRate;
	}

	// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21b
	 * @return
	 */
	@Override
	public String caculateLgd(L120S21B l120s21b) throws CapMessageException {
		String errorMsg = "";
		boolean hasSyndIsCmsSpecial_1 = false;
		List<L120S21C> l120s21clist2 = this.findL120s21cByMainIdAndCollType(
				l120s21b.getMainId(), l120s21b.getCntrNo_s21b(), "9"); // 9.其他
		if (l120s21clist2 != null && !l120s21clist2.isEmpty()) {

			for (L120S21C l120s21c : l120s21clist2) {
				if (Util.equals(l120s21c.getColKind_s21c(), "999901")) {
					// 本案為First priority security interest in substantially all
					// tangible and intangible assets…或類似授信條件者
					hasSyndIsCmsSpecial_1 = true;
				}
			}
		}

		if (hasSyndIsCmsSpecial_1) {
			errorMsg = this.caculateLgd_2(l120s21b);
		} else {
			errorMsg = this.caculateLgd_1(l120s21b);
		}
		return errorMsg;
	}

	// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21b
	 * @return
	 */
	@Override
	public String caculateLgd_1(L120S21B l120s21b) throws CapMessageException {
		String errorMsg = "";

		L120M01A l120m01a = l120m01aDao.findByMainId(l120s21b.getMainId());

		IBranch ibranch = branchService.getBranch(l120m01a.getCaseBrId());
		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());

		// BigDecimal gutPercent = BigDecimal.ZERO;

		// if (Util.equals(Util.nullToSpace(l120s21b.getHeadItem1_s21b()), "Y"))
		// {
		// // 額度明細表有信保，優先使用額度明細表信保成數
		// gutPercent = Util.notEquals(
		// Util.nullToSpace(l120s21b.getHeadItem1_s21b()), "Y") ?
		// BigDecimal.ZERO
		// : l120s21b.getGutPercent_s21b().divide(new BigDecimal(100));
		// } else {
		// // 額度明細表非信保，檢查L120S01C下有沒有擔保品種類為050300且有信保保證成數
		// List<L120S21C> l120s21clistAll = lmsLgdService
		// .findL120s21cByMainIdAndCollType(l120s21b.getMainId(),
		// l120s21b.getCntrNo_s21b(), null); // 已建檔+未建檔
		// if (l120s21clistAll != null && !l120s21clistAll.isEmpty()) {
		// for (L120S21C l120s21c : l120s21clistAll) {
		// if (Util.equals(
		// Util.getLeftStr(
		// Util.trim(l120s21c.getColKind_s21c()), 4),
		// "0503")) {
		//
		// BigDecimal tGutPercent = l120s21c.getCmsGrtrt_s21c() == null ?
		// BigDecimal.ZERO
		// : l120s21c.getCmsGrtrt_s21c();
		//
		// if (tGutPercent.compareTo(gutPercent) > 0) {
		// gutPercent = tGutPercent;
		// }
		//
		// }
		// }
		//
		// if (gutPercent.compareTo(gutPercent) > 0) {
		// gutPercent = gutPercent.divide(new BigDecimal(100));
		// }
		//
		// }
		//
		// }

		// if (Util.equals(l120s21b.getCntrNo_s21b(), "005111100120")) {
		// System.out.println("005111100120");
		// }

		BigDecimal cntrEad = l120s21b.getCntrEad_s21b() == null ? BigDecimal.ZERO
				: l120s21b.getCntrEad_s21b();

		// A.額度預期擔保品回收 = 擔保品預期回收合計 + 信用保證回收

		// A.1.擔保品預期回收合計(無共用之順位預估擔保品回收 + 有共用之分配後擔保品回收)
		// 未建檔+已建檔

		BigDecimal collateralRecoveryOth = BigDecimal.ZERO;// 未建檔
		BigDecimal collateralRecoveryCms = BigDecimal.ZERO; // 已建檔

		collateralRecoveryCms = l120s21b.getCollateralRecoveryCms() == null ? BigDecimal.ZERO
				: l120s21b.getCollateralRecoveryCms(); // 已建檔

		// 未建檔重算一次
		List<L120S21C> l120s21clist2 = this.findL120s21cByMainIdAndCollType(
				l120s21b.getMainId(), l120s21b.getCntrNo_s21b(), "2"); // 未建檔
		if (l120s21clist2 != null && !l120s21clist2.isEmpty()) {

			// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
			// 重算未建檔擔保品明細
			for (L120S21C l120s21c : l120s21clist2) {
				String calcMsg = this.calcL120s21c(l120s21c);
				if (Util.notEquals(calcMsg, "")) {
					throw new CapMessageException(calcMsg, getClass());
				}
				this.save(l120s21c);
			}

			for (L120S21C l120s21c : l120s21clist2) {
				// J-110-0485_05097_B1006 Web
				// e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
				if (Util.notEquals(
						Util.getLeftStr(l120s21c.getColKind_s21c(), 4), "0503")) {
					collateralRecoveryOth = collateralRecoveryOth.add(l120s21c
							.getColRecoveryTwd_s21c() == null ? BigDecimal.ZERO
							: l120s21c.getColRecoveryTwd_s21c());
				}
			}
		}

		// 如果聯貸案且兆豐為擔保品管理行=Y，要乘上攤貸比例
		// 擔保品建檔的時候呼叫擔保品API時已經乘上攤貸比例
		// 針對未建擔保品的才要乘攤貸比例

		// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
		// if (Util.equals(l120s21b.getUnionFlag(), "Y")) {
		// if (l120s21b.getUnionAmt() != null
		// && l120s21b.getUnionAmt().compareTo(BigDecimal.ZERO) > 0) {
		// collateralRecoveryOth = collateralRecoveryOth.multiply(l120s21b
		// .getSyndAmt().divide(l120s21b.getUnionAmt(), 16,
		// BigDecimal.ROUND_HALF_UP));
		// }
		// }

		// if (false) {
		// collateralRecoveryOth = l120s21b.getCollateralRecoveryOth() == null ?
		// BigDecimal.ZERO
		// : l120s21b.getCollateralRecoveryOth(); // 未建檔
		//
		// collateralRecoveryCms = l120s21b.getCollateralRecoveryCms() == null ?
		// BigDecimal.ZERO
		// : l120s21b.getCollateralRecoveryCms(); // 已建檔
		// } else {
		// // 未建檔
		// List<L120S21C> l120s21clist2 = lmsLgdService
		// .findL120s21cByMainIdAndCollType(l120s21b.getMainId(),
		// l120s21b.getCntrNo_s21b(), "2"); // 未建檔
		// if (l120s21clist2 != null && !l120s21clist2.isEmpty()) {
		// for (L120S21C l120s21c : l120s21clist2) {
		// collateralRecoveryOth = collateralRecoveryOth.add(l120s21c
		// .getColRecoveryTwd_s21c() == null ? BigDecimal.ZERO
		// : l120s21c.getColRecoveryTwd_s21c());
		// }
		// }
		//
		// // 如果聯貸案且兆豐為擔保品管理行=Y，要乘上攤貸比例
		// // 擔保品建檔的時候呼叫擔保品API時已經乘上攤貸比例
		// // 針對未建擔保品的才要乘攤貸比例
		// if (Util.equals(l120s21b.getUnionFlag(), "Y")) {
		// collateralRecoveryOth = collateralRecoveryOth.multiply(l120s21b
		// .getSyndAmt().divide(l120s21b.getUnionAmt(), 16,
		// BigDecimal.ROUND_HALF_UP));
		// }
		//
		// // 已建檔
		// // 方法1.直接抓擔保品API回傳合計
		//
		// if (true) {
		// collateralRecoveryCms = l120s21b.getCollateralRecoveryCms() == null ?
		// BigDecimal.ZERO
		// : l120s21b.getCollateralRecoveryCms(); // 已建檔
		// } else {
		// // 方法2.自行加總
		// List<L120S21C> l120s21clist1 = lmsLgdService
		// .findL120s21cByMainIdAndCollType(l120s21b.getMainId(),
		// l120s21b.getCntrNo_s21b(), "1"); // 已建檔
		// if (l120s21clist1 != null && !l120s21clist1.isEmpty()) {
		// for (L120S21C l120s21c : l120s21clist1) {
		// collateralRecoveryCms = collateralRecoveryCms
		// .add(l120s21c.getColRecoveryTwd_s21c() == null ? BigDecimal.ZERO
		// : l120s21c.getColRecoveryTwd_s21c());
		// }
		// }
		// }
		// }

		// 擔保品預期回收合計 = 未建檔+已建檔
		BigDecimal collateralRecovery = collateralRecoveryOth
				.add(collateralRecoveryCms);

		// A.2預期產品自償回收=預期 EAD *(如果是應收帳款，要在乘上帳款管理商保證成數)* 產品自償回收90%( Lookups!$E$37
		// )
		BigDecimal prodRecvRate = l120s21b.getProdRecvRate().divide(
				new BigDecimal(100));
		BigDecimal prodRecvTwd = BigDecimal.ZERO;
		if (Util.equals(Util.trim(l120s21b.getIsProdRecv()), "Y")) {

			BigDecimal ArAccPercent_s21b = BigDecimal.ONE;
			if (Util.equals(Util.trim(l120s21b.getProdRecvType()), "02")) {
				// 應收帳款非本行保證
				if (l120s21b.getArAccPercent_s21b() != null) {
					ArAccPercent_s21b = ArAccPercent_s21b.min(new BigDecimal(
							100)); // 超過100以100計
					ArAccPercent_s21b = l120s21b.getArAccPercent_s21b().divide(
							new BigDecimal(100));
				}
			}

			prodRecvTwd = cntrEad.multiply(ArAccPercent_s21b).multiply(
					prodRecvRate);
		}

		// A.3.信用保證回收((額度EAD-擔保品預期回收合計-預期產品自償回收) * 信保保證成數 * 95%(信保回收率 E33))

		// 信保回收率 95%( E33)
		BigDecimal gutRecoveryRate = l120s21b.getGutRecoveryRate().divide(
				new BigDecimal(100));

		// (額度EAD-額度預期擔保品回收合計-預期產品自償回收) * 信保保證成數 * 95%(信保回收率 E33)
		// BigDecimal creditRecovery = (cntrEad.subtract(collateralRecovery)
		// .subtract(prodRecvTwd)).multiply(gutPercent).multiply(
		// gutRecoveryRate);

		// 額度EAD-額度預期擔保品回收合計-預期產品自償回收

		BigDecimal creditOrgAmt = cntrEad.subtract(collateralRecovery)
				.subtract(prodRecvTwd);
		BigDecimal creditInitAmt = cntrEad.subtract(collateralRecovery)
				.subtract(prodRecvTwd);

		creditOrgAmt = creditOrgAmt.max(BigDecimal.ZERO); // 小於0要等於0
		creditInitAmt = creditInitAmt.max(BigDecimal.ZERO); // 小於0要等於0

		BigDecimal creditLastAmt = BigDecimal.ZERO;

		// 如果同一個額度同時有中小信保跟非中信保兩種情形
		// 要先算非中小信保的(保證成數大->小)，剩下再算中小信保(保證成數大->小)
		List<L120S21C> l120s21clist0503 = this.findL120s21cByMainIdAndCollKind(
				l120s21b.getMainId(), l120s21b.getCntrNo_s21b(), "0503"); // 已建檔+未建檔

		// *******************************************************************************************************
		// 計算信保************************************************************************************************
		// *******************************************************************************************************
		// 非中小信保
		// J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		BigDecimal creditNoneSmeAmt = BigDecimal.ZERO;
		if (l120s21clist0503 != null && !l120s21clist0503.isEmpty()) {
			for (L120S21C l120s21c : l120s21clist0503) {

				// J-110-0485_05097_B1008 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
				if (Util.notEquals(Util.trim(l120s21c.getColKind_s21c()),
						"050300")) {

					// J-110-0485_05097_B1006 Web
					// e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類

					// [下午 01:21] 齊孝慈(風險控管處,科長) 建霖~我跟翔哥跟乃誠討論過 想麻煩你將下列四個欄位的值塞進去

					// 於明細時已經計算非中小信保的其他信保之擔保品回收，所以這邊為FALSE
					if (false) {

						// 非中小信保機構保證
						BigDecimal tGutPercent = l120s21c.getCmsGrtrt_s21c() == null ? BigDecimal.ZERO
								: l120s21c.getCmsGrtrt_s21c();

						if (tGutPercent.compareTo(BigDecimal.ZERO) > 0) {
							tGutPercent = tGutPercent
									.divide(new BigDecimal(100));
						}

						BigDecimal colTimeValue_s21c = l120s21c
								.getColTimeValue_s21c() == null ? BigDecimal.ZERO
								: l120s21c.getColTimeValue_s21c();

						BigDecimal colTimeValue_s21c_twd = BigDecimal.ZERO;
						String colCurr_s21c = Util.trim(l120s21c
								.getColCurr_s21c());
						if (Util.equals(colCurr_s21c, "TWD")) {
							colTimeValue_s21c_twd = colTimeValue_s21c;
						} else {
							colTimeValue_s21c_twd = branchRate.toOtherAmt(
									colCurr_s21c, "TWD", colTimeValue_s21c)
									.setScale(0, BigDecimal.ROUND_HALF_UP);
						}
						BigDecimal tCreditLastAmt = colTimeValue_s21c_twd
								.multiply(tGutPercent)
								.multiply(gutRecoveryRate);

						// 是否與其他額度共用
						String colCoUseFlag_s21c = Util.trim(l120s21c
								.getColCoUseFlag_s21c()); // 是否與其他額度共用
						if (Util.equals(colCoUseFlag_s21c, "Y")) {
							BigDecimal colShareRate_s21c = l120s21c
									.getColShareRate_s21c() == null ? BigDecimal.ZERO
									: l120s21c.getColShareRate_s21c(); // 分配比率
							tCreditLastAmt = tCreditLastAmt.multiply(
									colShareRate_s21c).divide(
									new BigDecimal(100));

						}

						creditLastAmt = creditLastAmt.add(tCreditLastAmt);
					}

					// J-110-0485_05097_B1006 Web
					// e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類

					// [下午 01:21] 齊孝慈(風險控管處,科長)
					// 建霖~我跟翔哥跟乃誠討論過 想麻煩你將下列四個欄位的值塞進去
					//
					//
					// 擔保品回收率 colRate_s21c
					// 預估擔保品回收 colEstRecovery_s21c
					// 順位預估擔保品回收 colRgstRecovery_s21c
					// 分配後擔保品回收 colRecovery_s21c
					// 分配後擔保品回收TWD colRecoveryTwd_s21c
					BigDecimal tCreditLastAmt = l120s21c
							.getColRecoveryTwd_s21c() == null ? BigDecimal.ZERO
							: l120s21c.getColRecoveryTwd_s21c();
					tCreditLastAmt = tCreditLastAmt.max(BigDecimal.ZERO); // 小於0要等於0
					creditLastAmt = creditLastAmt.add(tCreditLastAmt);

				}

			}
		}

		creditLastAmt = creditLastAmt.max(BigDecimal.ZERO); // 小於0要等於0
		if (creditLastAmt.compareTo(creditInitAmt) > 0) {
			// 保證金額不能超過 (額度EAD-額度預期擔保品回收合計-預期產品自償回收)
			creditLastAmt = creditInitAmt;
		}

		// 非中小信保小計
		// J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		creditNoneSmeAmt = creditLastAmt;

		// J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		// 未建檔擔保品小計(含信保)
		BigDecimal collateralRecoveryOthSme = collateralRecoveryOth
				.add(creditLastAmt);

		// 剩下還沒有被非信保保證的
		creditInitAmt = creditInitAmt.subtract(creditLastAmt);
		creditInitAmt = creditInitAmt.max(BigDecimal.ZERO); // 小於0要等於0

		// 中小信保
		// J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		BigDecimal creditSmeAmt = BigDecimal.ZERO;
		if (creditInitAmt.compareTo(BigDecimal.ZERO) > 0) {
			// 分配非中小信保後還有剩下未保額度

			if (Util.equals(Util.nullToSpace(l120s21b.getHeadItem1_s21b()), "Y")) {
				BigDecimal tGutPercent = Util.notEquals(
						Util.nullToSpace(l120s21b.getHeadItem1_s21b()), "Y") ? BigDecimal.ZERO
						: l120s21b.getGutPercent_s21b().divide(
								new BigDecimal(100));

				BigDecimal tCreditLastAmt = creditInitAmt.multiply(tGutPercent)
						.multiply(gutRecoveryRate);
				tCreditLastAmt = tCreditLastAmt.max(BigDecimal.ZERO); // 小於0要等於0

				// J-110-0485_05097_B1006 Web
				// e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
				creditSmeAmt = creditSmeAmt.add(tCreditLastAmt);
				creditLastAmt = creditLastAmt.add(tCreditLastAmt);
			}
		}

		if (creditLastAmt.compareTo(creditOrgAmt) > 0) {
			// 保證金額不能超過 (額度EAD-額度預期擔保品回收合計-預期產品自償回收)
			creditLastAmt = creditOrgAmt;
		}

		BigDecimal creditRecovery = creditLastAmt;

		// J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		// 擔保品系統分配後擔保品回收小計(含信保)
		BigDecimal collateralRecoveryCmsSme = collateralRecoveryCms
				.add(creditLastAmt.subtract(creditNoneSmeAmt).compareTo(
						BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : creditLastAmt
						.subtract(creditNoneSmeAmt));

		creditRecovery = creditRecovery.max(BigDecimal.ZERO); // 小於0要等於0

		// if (Util.equals(Util.trim(l120s21b.getCntrNo_s21b()),
		// "Y01500600043")) {
		// System.out.println("Y01500600043");
		// }

		// A.額度預期擔保品回收= 擔保品預期回收合計 + 信用保證回收
		BigDecimal expectSecuredRecovery = BigDecimal.ZERO; // A.額度預期擔保品回收
		expectSecuredRecovery = collateralRecovery.add(creditRecovery);

		// 預期無擔保回收
		// =IFERROR(IF(SUM(I9,K9)>F9,0,IF(F$4="有",(F9-I9-K9)*Lookups!$E$38,IF(F$4="無",(F9-I9-K9)*Lookups!$E$39,0))),0)
		BigDecimal expectUnsecuredRecovery = BigDecimal.ZERO;
		if ((expectSecuredRecovery.add(prodRecvTwd)).compareTo(cntrEad) > 0) {
			// 額度預期擔保品回收 +預期產品自償回收 > 額度EAD 則 = 0
			expectUnsecuredRecovery = BigDecimal.ZERO;
		} else { // 額度預期擔保品回收 +預期產品自償回收 < 額度EAD ，代表有無擔保
			BigDecimal tmp_expectUnsecuredRecovery = cntrEad.subtract(
					expectSecuredRecovery).subtract(prodRecvTwd); // 額度EAD(F9)-
																	// 預期產品自償回收(I9)-額度預期擔保品回收(K9)
			tmp_expectUnsecuredRecovery = tmp_expectUnsecuredRecovery
					.max(BigDecimal.ZERO); // 小於0要等於0

			BigDecimal unsecuredRecoveryRateY = l120s21b
					.getUnsecuredRecoveryRateY().divide(new BigDecimal(100));
			BigDecimal unsecuredRecoveryRateN = l120s21b
					.getUnsecuredRecoveryRateN().divide(new BigDecimal(100));

			// J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
			BigDecimal unsecuredRecoveryRate = unsecuredRecoveryRateN; // 預設
																		// unsecuredRecoveryRateN

			// J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等

			// 有公司保證者 =>(額度EAD(F9)- 預期產品自償回收(I9)-額度預期擔保品回收(K9)) * 無擔保回收 -
			// 有公司保證者 44%(E38)

			// 無公司保證者 =>(額度EAD(F9)- 預期產品自償回收(I9)-額度預期擔保品回收(K9)) * 無擔保回收 -
			// 無公司保證者 21%(E39)

			if (Util.equals(Util.nullToSpace(l120s21b.getHasGuarantor_s21b()),
					"Y")) {
				// 有公司保證者 =>(額度EAD(F9)- 預期產品自償回收(I9)-額度預期擔保品回收(K9)) * 無擔保回收 -
				// 有公司保證者 44%(E38)

				// 先檢核欄位是否OK
				try {
					this.chkL120s21b(l120s21b);
				} catch (Exception e) {
					throw new CapMessageException(e.getMessage(), getClass());
				}

				unsecuredRecoveryRate = this.getUnsecuredRecoveryRate(l120s21b);

			}

			// J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
			expectUnsecuredRecovery = tmp_expectUnsecuredRecovery
					.multiply(unsecuredRecoveryRate);
		}

		// 清償損失率=IFERROR(1-(SUM(額度預期擔保品回收,預期無擔保回收,預期產品自償回收)/額度EAD),"")
		// =IFERROR(1-(SUM(I9,K9,L9)/F9),"")
		BigDecimal payOffLossRate = BigDecimal.ZERO;

		// 判斷分母(額度EAD)為0時直接給0
		if (cntrEad.compareTo(BigDecimal.ZERO) == 0) {
			payOffLossRate = BigDecimal.ZERO;
		} else {

			BigDecimal tmp_payOffLossRate = (expectSecuredRecovery
					.add(expectUnsecuredRecovery).add(prodRecvTwd)).divide(
					cntrEad, 4, BigDecimal.ROUND_HALF_UP);

			payOffLossRate = BigDecimal.ONE.subtract(tmp_payOffLossRate);
		}

		// [下午 02:57] 邱煥翔(風險控管處,科長)
		// 經與顧問討論 LGD清償= 清償損失率+間接成本 其中清償損失率最低為0
		// payOffLossRate = payOffLossRate.min(BigDecimal.ZERO);
		if (payOffLossRate.compareTo(BigDecimal.ZERO) < 0) {
			payOffLossRate = BigDecimal.ZERO;
		}

		// 預期 LGD ＝ 清償路徑比例％ × （清償損失率+間接成本）＋ 協商路徑比例 × （協商損失率＋間接成本）

		BigDecimal expectLgd = BigDecimal.ZERO;

		BigDecimal payOffPath = l120s21b.getPayOffPath().divide(
				new BigDecimal(100)); // 清償路徑比例E43
		BigDecimal negotiatePath = l120s21b.getNegotiatePath().divide(
				new BigDecimal(100)); // 協商路徑比例E44
		BigDecimal turnPositivePath = l120s21b.getTurnPositivePath().divide(
				new BigDecimal(100)); // 轉正路徑比例E45
		BigDecimal negotiateLossRate = l120s21b.getNegotiateLossRate().divide(
				new BigDecimal(100)); // 協商損失率E46
		BigDecimal turnPositiveLossRate = l120s21b.getTurnPositiveLossRate()
				.divide(new BigDecimal(100)); // 轉正損失率E47
		BigDecimal indirectCost = l120s21b.getIndirectCost().divide(
				new BigDecimal(100)); // 間接成本E48

		// (清償損失率+間接成本) * 清償路徑比例
		// 20220614邱煥翔與顧問討論後，決議這邊要先判斷不得小於0，以免這邊負太大吃掉後面轉正損失率及協商損失率
		// [下午 01:40] 邱煥翔(風險控管處,科長)
		// 清償損失率+間接成本 不得低於0
		BigDecimal payOffAddIndirectCost = (payOffLossRate.add(indirectCost))
				.max(BigDecimal.ZERO); // 小於0要等於0

		BigDecimal tmp_expectLgd_1 = payOffPath.multiply(payOffAddIndirectCost);

		// (轉正損失率+間接成本) * 轉正路徑比例
		BigDecimal tmp_expectLgd_2 = turnPositivePath
				.multiply(turnPositiveLossRate.add(indirectCost));

		// (協商損失率+間接成本) * 協商路徑比例
		BigDecimal tmp_expectLgd_3 = negotiatePath.multiply(negotiateLossRate
				.add(indirectCost));

		BigDecimal tmp_expectLgd = tmp_expectLgd_1.add(tmp_expectLgd_2).add(
				tmp_expectLgd_3);
		if (tmp_expectLgd.compareTo(BigDecimal.ONE) > 0) {
			expectLgd = BigDecimal.ONE;
		} else if (tmp_expectLgd.compareTo(BigDecimal.ZERO) < 0) {
			expectLgd = BigDecimal.ZERO;
		} else {
			expectLgd = tmp_expectLgd;
		}

		// J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		// [昨天 下午 03:22] 邱煥翔(風險控管處,科長)
		// 無EAD暴險者 額度LGD為0
		if (cntrEad.compareTo(BigDecimal.ZERO) <= 0) {
			expectLgd = BigDecimal.ZERO;
		}

		l120s21b.setCollateralRecoveryOth(collateralRecoveryOth);
		l120s21b.setCollateralRecoveryCms(collateralRecoveryCms);
		// J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		// l120s21b.setCollateralRecoveryOthSme(collateralRecoveryOthSme); //
		// 含信保
		// l120s21b.setCollateralRecoveryCmsSme(collateralRecoveryCmsSme); //
		// 含信保

		l120s21b.setCollateralRecoveryOthSme(null); // 含信保
		l120s21b.setCollateralRecoveryCmsSme(null); // 含信保

		l120s21b.setProdRecvTwd(prodRecvTwd); // 產品自償回收
		l120s21b.setCollateralRecovery(collateralRecovery); // 擔保品預期回收合計
		l120s21b.setCreditRecovery(creditRecovery); // 信用保證回收
		l120s21b.setExpectSecuredRecovery(expectSecuredRecovery); // A.額度預期擔保品回收=
																	// 擔保品預期回收合計
																	// + 信用保證回收
		l120s21b.setExpectUnsecuredRecovery(expectUnsecuredRecovery); // 額度預期無擔保回收

		// 當擔保品遠大於分配到額度EAD時，清償損失率可能為很大的負值，造成寫入DB時錯誤，所以要先判斷大小
		BigDecimal savePayOffLossRate = payOffLossRate.multiply(new BigDecimal(
				100));
		if (savePayOffLossRate.compareTo(new BigDecimal(999.99)) > 0) {
			savePayOffLossRate = new BigDecimal(999.99);
		} else if (savePayOffLossRate.compareTo(new BigDecimal(-999.99)) < 0) {
			savePayOffLossRate = new BigDecimal(-999.99);
		} else {
			savePayOffLossRate = savePayOffLossRate;
		}
		l120s21b.setPayOffLossRate(savePayOffLossRate); // 清償損失率
		l120s21b.setExpectLgd(expectLgd.multiply(new BigDecimal(100)).setScale(
				2, BigDecimal.ROUND_HALF_UP)); // 預期LGD

		// J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		// LGD版本
		String LMS_LGD_LGD_VERSION_1 = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_LGD_VERSION_1"));
		String LMS_LGD_LGD_VERSION_2 = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_LGD_VERSION_2"));
		String LMS_LGD_EAD_VERSION_1 = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_EAD_VERSION_1"));
		String LMS_LGD_EAD_VERSION_2 = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_EAD_VERSION_2"));

		l120s21b.setLgdVer1(Util.isEmpty(LMS_LGD_LGD_VERSION_1) ? 0 : Util
				.parseInt(LMS_LGD_LGD_VERSION_1));
		l120s21b.setLgdVer2(Util.isEmpty(LMS_LGD_LGD_VERSION_2) ? 0 : Util
				.parseInt(LMS_LGD_LGD_VERSION_2));
		l120s21b.setEadVer1(Util.isEmpty(LMS_LGD_EAD_VERSION_1) ? 0 : Util
				.parseInt(LMS_LGD_EAD_VERSION_1));
		l120s21b.setEadVer2(Util.isEmpty(LMS_LGD_EAD_VERSION_2) ? 0 : Util
				.parseInt(LMS_LGD_EAD_VERSION_2));

		this.save(l120s21b);

		return errorMsg;
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21c
	 * @return
	 */
	@Override
	public String calcL120s21c(L120S21C l120s21c) throws CapMessageException {

		// 1.非保證**********************
		// 預估擔保品回收=估值 x 回收率
		// 順位預估擔保品回收= 取小(預估擔保品回收- 前順位設定金額,擔保品設定金額)
		// 順位預估擔保品回收= 順位預估擔保品回收 x 聯貸比例
		// 分配後擔保品回收 = 順位預估擔保品回收 x 有其他額度共用之分配比率

		// 2.保證(擔保品類別05，但不含050300中小/海外信保)**********************
		// 預估擔保品回收=送保金額 X 保證成數 X 回收率
		// 順位預估擔保品回收= 取小(預估擔保品回收- 前順位設定金額,擔保品設定金額)
		// 順位預估擔保品回收= 順位預估擔保品回收 x 聯貸比例
		// 分配後擔保品回收 = 順位預估擔保品回收 x 有其他額度共用之分配比率

		// 3.保證(050300中小/海外信保)**********************
		// 預估擔保品回收=0

		String errorMsg = "";
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSLgdCommomPage.class);

		L120M01A l120m01a = l120m01aDao.findByMainId(l120s21c.getMainId());

		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());

		// J-112-0210_05097_B1002 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
		String othCollKind = Util.trim(l120s21c.getColKind_s21c()); // L120S21C.colKind_s21c=擔保品種類
		String colCurr_s21c = Util.trim(l120s21c.getColCurr_s21c());// 擔保品幣別
		BigDecimal colTimeValue_s21c = l120s21c.getColTimeValue_s21c() == null ? BigDecimal.ZERO
				: l120s21c.getColTimeValue_s21c(); // 購置時價
		String colCoUseFlag_s21c = Util.trim(l120s21c.getColCoUseFlag_s21c()); // 是否與其他額度共用
		BigDecimal colShareRate_s21c = l120s21c.getColShareRate_s21c(); // 分配比率

		// 擔保品回收率************************************************************
		BigDecimal colRate_s21c = BigDecimal.ZERO;
		l120s21c.setColRate_s21c(null);

		// J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		// if (Util.equals(Util.getLeftStr(othCollKind, 4), "0503")) {
		// colRate_s21c = BigDecimal.ZERO;
		// l120s21c.setColRate_s21c(colRate_s21c);
		// } else {

		// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
		// LGD參數要特別處理，有些只有zh_TW
		Map<String, String> codeCollRateMap = this.findLgdCollKindCodeTypeMap(
				l120m01a, l120s21c.getCntrNo_s21c(), "LGD_" + othCollKind);

		if (codeCollRateMap == null || codeCollRateMap.isEmpty()) {
			colRate_s21c = BigDecimal.ZERO;
			l120s21c.setColRate_s21c(colRate_s21c);
		} else {
			for (String key : codeCollRateMap.keySet()) {
				// L120S21C.colRate_s21c=擔保品回收率
				l120s21c.setColRate_s21c(Util.parseBigDecimal(key));
				if (l120s21c.getColRate_s21c() != null) {
					colRate_s21c = Util.parseBigDecimal(key).divide(
							new BigDecimal(100));
					break;
				}
			}
		}
		// }

		if (colRate_s21c == null) {
			// errMsg08=擔保品種類無對應之回收率，請洽資訊處。
			throw new CapMessageException(pop.getProperty("errMsg08"),
					getClass());
		}

		// 預估擔保品回收************************************************************
		BigDecimal colEstRecovery_s21c = colTimeValue_s21c
				.multiply(colRate_s21c) == null ? BigDecimal.ZERO
				: colTimeValue_s21c.multiply(colRate_s21c); // 預估擔保品回收

		// 保證要乘上保證成數*********************************************************
		if (Util.equals(Util.getLeftStr(othCollKind, 2), "05")) {
			BigDecimal tGutPercent = l120s21c.getCmsGrtrt_s21c() == null ? BigDecimal.ZERO
					: l120s21c.getCmsGrtrt_s21c();

			if (tGutPercent.compareTo(BigDecimal.ZERO) > 0) {
				colEstRecovery_s21c = colEstRecovery_s21c.multiply(tGutPercent)
						.divide(new BigDecimal(100));
			}

		}

		// J-110-0485_05097_B1008 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		if (Util.equals(othCollKind, "050300")) {
			// 信保案件不算
			colEstRecovery_s21c = BigDecimal.ZERO;
		}

		// 預估擔保品回收塞值*******
		l120s21c.setColEstRecovery_s21c(colEstRecovery_s21c);

		// 順位預估擔保品回收************************************************************
		// 順位預估擔保品回收=取小(預估擔保品回收- 前順位設定金額,擔保品設定金額) x 聯貸比例 x 保證成數
		BigDecimal colPreRgstAmt_s21c = l120s21c.getColPreRgstAmt_s21c(); // 前順位設定金額
		BigDecimal colRgstAmt_s21c = l120s21c.getColRgstAmt_s21c(); // 擔保品設定金額
		BigDecimal colRgstRecovery_s21c = null; // 順位預估擔保品回收

		// J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		if (this.hideRgstInfoForL120s21c(othCollKind)) {
			// 保證只看送保金額(估值)，不看前順位跟順位金額
			colPreRgstAmt_s21c = null;
			colRgstAmt_s21c = null;
			l120s21c.setColPreRgstAmt_s21c(colPreRgstAmt_s21c);
			l120s21c.setColRgstAmt_s21c(colRgstAmt_s21c);
			colRgstRecovery_s21c = colEstRecovery_s21c;
		} else {
			if (Util.isEmpty(colPreRgstAmt_s21c)
					&& Util.isEmpty(colRgstAmt_s21c)) {
				colRgstRecovery_s21c = colEstRecovery_s21c;
			} else {
				colPreRgstAmt_s21c = colPreRgstAmt_s21c == null ? BigDecimal.ZERO
						: colPreRgstAmt_s21c;
				colRgstAmt_s21c = colRgstAmt_s21c == null ? BigDecimal.ZERO
						: colRgstAmt_s21c;
				BigDecimal tmpRgstAmt = colEstRecovery_s21c
						.subtract(colPreRgstAmt_s21c);
				if (tmpRgstAmt.compareTo(BigDecimal.ZERO) <= 0) {
					colRgstRecovery_s21c = BigDecimal.ZERO;
				} else {
					colRgstRecovery_s21c = tmpRgstAmt.min(colRgstAmt_s21c);
				}
			}
		}

		// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
		// 要乘上聯貸攤貸比例*********************************************************

		// 順位預估擔保品回收= 順位預估擔保品回收 x
		// 聯貸比例*****************************************************************************
		// 如果聯貸案且兆豐為擔保品管理行=Y，要乘上攤貸比例
		// 擔保品建檔的時候呼叫擔保品API時已經乘上攤貸比例
		// 針對未建擔保品的才要乘攤貸比例
		L120S21B l120s21b = this.findL120s21bByMainIdAndCntrNo(
				l120s21c.getMainId(), l120s21c.getCntrNo_s21c());

		// J-112-0210_05097_B1005 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
		if (Util.equals(l120s21b.getUnionFlag(), "Y")) {
			if (Util.equals(l120s21c.getUnionFlag_s21c(), "Y")) {
				// 擔保品是否為多筆聯貸案額度共用
				colRgstRecovery_s21c = colRgstRecovery_s21c.multiply(l120s21c
						.getSyndAmt_s21c().divide(l120s21c.getUnionAmt_s21c(),
								4, BigDecimal.ROUND_HALF_UP));
			} else {
				colRgstRecovery_s21c = colRgstRecovery_s21c.multiply(l120s21b
						.getSyndAmt().divide(l120s21b.getUnionAmt(), 4,
								BigDecimal.ROUND_HALF_UP));
			}
		}

		// ******順位預估擔保品回收塞值******
		l120s21c.setColRgstRecovery_s21c(colRgstRecovery_s21c);

		// 分配後擔保品回收************************************************************
		// 分配後擔保品回收 = 順位預估擔保品回收 x 有其他額度共用之分配比率
		BigDecimal colRecovery_s21c = BigDecimal.ZERO; // 分配後擔保品回收
		BigDecimal colRecoveryTwd_s21c = BigDecimal.ZERO; // 分配後擔保品回收TWD
		if (Util.equals(colCoUseFlag_s21c, "Y")) {
			// 有與其他額度共用
			colRecovery_s21c = colRgstRecovery_s21c.multiply(colShareRate_s21c)
					.divide(new BigDecimal(100));
		} else {
			colRecovery_s21c = colRgstRecovery_s21c;
		}

		if (colRecovery_s21c.compareTo(BigDecimal.ZERO) == 0
				|| Util.equals(colCurr_s21c, "TWD")) {
			colRecoveryTwd_s21c = colRecovery_s21c;
		} else {
			colRecoveryTwd_s21c = branchRate.toOtherAmt(colCurr_s21c, "TWD",
					colRecovery_s21c).setScale(0, BigDecimal.ROUND_HALF_UP);
		}

		l120s21c.setColRecovery_s21c(colRecovery_s21c);
		l120s21c.setColRecoveryTwd_s21c(colRecoveryTwd_s21c);

		// J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		// [昨天 下午 03:54] 邱煥翔(風險控管處,科長)
		// 共用註記為否 不論未建檔 已建檔 若無前順位時 分配比率皆顯示100%
		if (Util.equals(Util.trim(l120s21c.getColCoUseFlag_s21c()), "N")) {
			if (l120s21c.getColPreRgstAmt_s21c() == null
					|| BigDecimal.ZERO.compareTo(l120s21c
							.getColPreRgstAmt_s21c()) == 0) {
				l120s21c.setColShareRate_s21c(new BigDecimal(100));
			}
		}
		return errorMsg;
	}

	// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21b
	 * @return
	 */
	@Override
	public String caculateLgd_2(L120S21B l120s21b) throws CapMessageException {
		String errorMsg = "";
		L120M01A l120m01a = l120m01aDao.findByMainId(l120s21b.getMainId());

		IBranch ibranch = branchService.getBranch(l120m01a.getCaseBrId());
		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());

		BigDecimal cntrEad = l120s21b.getCntrEad_s21b() == null ? BigDecimal.ZERO
				: l120s21b.getCntrEad_s21b();

		// A.額度預期擔保品回收 = 擔保品預期回收合計 + 信用保證回收

		// A.1.擔保品預期回收合計(無共用之順位預估擔保品回收 + 有共用之分配後擔保品回收)
		// 未建檔+已建檔

		BigDecimal collateralRecoveryOth = BigDecimal.ZERO;// 未建檔
		BigDecimal collateralRecoveryCms = BigDecimal.ZERO; // 已建檔

		collateralRecoveryCms = l120s21b.getCollateralRecoveryCms() == null ? BigDecimal.ZERO
				: l120s21b.getCollateralRecoveryCms(); // 已建檔

		// 未建檔重算一次
		List<L120S21C> l120s21clist2 = this.findL120s21cByMainIdAndCollType(
				l120s21b.getMainId(), l120s21b.getCntrNo_s21b(), "2"); // 未建檔
		if (l120s21clist2 != null && !l120s21clist2.isEmpty()) {

			// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
			// 重算未建檔擔保品明細
			for (L120S21C l120s21c : l120s21clist2) {
				String calcMsg = this.calcL120s21c(l120s21c);
				if (Util.notEquals(calcMsg, "")) {
					throw new CapMessageException(calcMsg, getClass());
				}
				this.save(l120s21c);
			}

		}

		String othCollKind = "999901";
		Map<String, String> codeCollRateMap = this.findLgdCollKindCodeTypeMap(
				l120m01a, l120s21b.getCntrNo_s21b(), "LGD_SPECIFY_"
						+ othCollKind);
		BigDecimal expectLgd = null;

		for (String key : codeCollRateMap.keySet()) {
			if (Util.notEquals(Util.trim(key), "")) {
				expectLgd = Util.parseBigDecimal(key);
				break;
			}
		}

		if (cntrEad.compareTo(BigDecimal.ZERO) <= 0) {
			expectLgd = BigDecimal.ZERO;
		}

		l120s21b.setCollateralRecoveryOth(null);
		l120s21b.setCollateralRecoveryCms(null);
		l120s21b.setCollateralRecoveryOthSme(null); // 含信保
		l120s21b.setCollateralRecoveryCmsSme(null); // 含信保
		l120s21b.setProdRecvTwd(null); // 產品自償回收
		l120s21b.setCollateralRecovery(null); // 擔保品預期回收合計
		l120s21b.setCreditRecovery(null); // 信用保證回收
		l120s21b.setExpectSecuredRecovery(null); // A.額度預期擔保品回收=
													// 擔保品預期回收合計
													// + 信用保證回收
		l120s21b.setExpectUnsecuredRecovery(null); // 額度預期無擔保回收

		// 當擔保品遠大於分配到額度EAD時，清償損失率可能為很大的負值，造成寫入DB時錯誤，所以要先判斷大小

		l120s21b.setPayOffLossRate(null); // 清償損失率
		l120s21b.setExpectLgd(expectLgd); // 預期LGD

		// J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		// LGD版本
		String LMS_LGD_LGD_VERSION_1 = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_LGD_VERSION_1"));
		String LMS_LGD_LGD_VERSION_2 = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_LGD_VERSION_2"));
		String LMS_LGD_EAD_VERSION_1 = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_EAD_VERSION_1"));
		String LMS_LGD_EAD_VERSION_2 = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_EAD_VERSION_2"));

		l120s21b.setLgdVer1(Util.isEmpty(LMS_LGD_LGD_VERSION_1) ? 0 : Util
				.parseInt(LMS_LGD_LGD_VERSION_1));
		l120s21b.setLgdVer2(Util.isEmpty(LMS_LGD_LGD_VERSION_2) ? 0 : Util
				.parseInt(LMS_LGD_LGD_VERSION_2));
		l120s21b.setEadVer1(Util.isEmpty(LMS_LGD_EAD_VERSION_1) ? 0 : Util
				.parseInt(LMS_LGD_EAD_VERSION_1));
		l120s21b.setEadVer2(Util.isEmpty(LMS_LGD_EAD_VERSION_2) ? 0 : Util
				.parseInt(LMS_LGD_EAD_VERSION_2));

		this.save(l120s21b);

		return errorMsg;
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public String calcCustLgd(String mainId) throws CapException {
		String errorMsg = "";
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSLgdCommomPage.class);

		// 借款人LGD
		List<L120S21B> l120s21blist = this.findL120s21bByMainId(mainId);

		if (l120s21blist == null || l120s21blist.isEmpty()) {
			// errMsg10=無借款人額度LGD明細資料！！
			return pop.getProperty("errMsg10");
		}

		Map<String, Object> execCustLgd = new HashMap<String, Object>();
		for (L120S21B l120s21b : l120s21blist) {
			String custId = Util.trim(l120s21b.getCustId_s21b());
			String dupNo = Util.trim(l120s21b.getDupNo_s21b());
			String custKey = custId + "-" + dupNo;
			if (execCustLgd.containsKey(custKey)) {
				// 已經執行過
				continue;
			} else {

				// BY借款人項下所有LGD明細
				List<L120S21B> l120s21bs = this.findL120s21bByCustId(mainId,
						custId, dupNo);
				BigDecimal totalEad = BigDecimal.ZERO;
				BigDecimal totalEadxLgd = BigDecimal.ZERO;
				BigDecimal custLgd = BigDecimal.ZERO;
				if (l120s21bs != null && !l120s21bs.isEmpty()) {
					for (L120S21B tl120s21b : l120s21bs) {
						if (Util.equals(
								Util.trim(tl120s21b.getHasCntrDoc_s21b()), "Y")) {
							// J-112-0278_05097_B1001 Web
							// e-Loan企金授信借款人LGD計算範圍排除進出口額度，並於試算頁簽內加註：借款人違約損失率不含進出口額度。
							String cntrNo = Util.trim(tl120s21b
									.getCntrNo_s21b());

							L140M01A l140m01a = lmsService
									.findL140M01AByL120m01cMainIdAndcntrNo(
											tl120s21b.getMainId(),
											cntrNo,
											UtilConstants.Cntrdoc.ItemType.額度明細表);
							if (l140m01a != null) {
								if (!this.isCustLgdNeed(l140m01a)) {
									continue;
								}
							}

							BigDecimal cntrEad_s21b = tl120s21b
									.getCntrEad_s21b();
							BigDecimal expectLgd = tl120s21b.getExpectLgd();

							if (expectLgd == null) {
								// errMsg11=「{0}」無額度LGD資訊！！
								// L120S21C.colShareRate_s21c=分配比率
								return MessageFormat.format(
										pop.getProperty("errMsg11"), cntrNo);
							}

							totalEad = totalEad.add(cntrEad_s21b);
							totalEadxLgd = totalEadxLgd.add(cntrEad_s21b
									.multiply(expectLgd));

						}
					}

					if (totalEad.compareTo(BigDecimal.ZERO) == 0) {
						custLgd = BigDecimal.ZERO;
					} else {
						custLgd = totalEadxLgd.divide(totalEad, 2,
								BigDecimal.ROUND_HALF_UP);
					}

					for (L120S21B tl120s21b : l120s21bs) {
						tl120s21b.setCustLgd(custLgd.setScale(2,
								BigDecimal.ROUND_HALF_UP));
						this.save(tl120s21b);
					}

				}
				// 已經執行過
				execCustLgd.put(custKey, custLgd);
			}
		}

		return errorMsg;
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等 補齊 L120S21B
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public void furtherL120s21b(String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);

		// 依目前簽案行做計算幣別
		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());
		Map<String, String> cntrNoMap = this.getCntrNoMap(mainId);
		if (cntrNoMap != null && !cntrNoMap.isEmpty()) {
			for (Map.Entry<String, String> entry : cntrNoMap.entrySet()) {
				String cntrNo = entry.getKey();
				String fullCust = entry.getValue();
				String custId = "";
				String dupNo = "";
				if (Util.notEquals(fullCust, "")) {
					custId = Util.trim(Util.getLeftStr(fullCust,
							fullCust.length() - 1));
					dupNo = Util.trim(Util.getRightStr(fullCust, 1));
				}

				L120S21B l120s21b = this.findL120s21bByMainIdAndCntrNo(mainId,
						cntrNo);
				if (l120s21b == null) {
					l120s21b = new L120S21B();
					l120s21b.setMainId(mainId);
					l120s21b.setCustId_s21b(custId);
					l120s21b.setDupNo_s21b(dupNo);
					l120s21b.setCntrNo_s21b(cntrNo);
					l120s21b.setCntrEad_s21b(null);
					l120s21b.setCreator(user.getUserId());
					l120s21b.setCreateTime(CapDate.getCurrentTimestamp());

				}

				// 沒有共用的額度序號更新EAD
				List<L120S21A> noCoCntrNo = this.findL120s21aByMainIdAndCntrNo(
						mainId, cntrNo);
				if (noCoCntrNo == null || noCoCntrNo.isEmpty()) {

					L140M01A l140m01a = lmsService
							.findL140M01AByL120m01cMainIdAndcntrNo(mainId,
									cntrNo,
									UtilConstants.Cntrdoc.ItemType.額度明細表);
					if (l140m01a != null) {
						l120s21b.setCntrEad_s21b(branchRate
								.toTWDAmt(
										(Util.isEmpty(l140m01a
												.getCurrentApplyCurr()) ? UtilConstants.CURR.TWD
												: l140m01a
														.getCurrentApplyCurr()),
										(l140m01a.getCurrentApplyAmt() == null ? BigDecimal.ZERO
												: l140m01a.getCurrentApplyAmt()))
								.setScale(0, BigDecimal.ROUND_HALF_UP));

					} else {
						Map<String, Object> lnf252 = misdbBASEService
								.findLnf252ByCntrNo(cntrNo);
						if (lnf252 != null && !lnf252.isEmpty()) {
							BigDecimal LNF252_FACT_AMT_NT = Util.equals(
									MapUtils.getString(lnf252,
											"LNF252_FACT_AMT_NT"), "") ? BigDecimal.ZERO
									: Util.parseBigDecimal(MapUtils.getString(
											lnf252, "LNF252_FACT_AMT_NT"));
							l120s21b.setCntrEad_s21b(LNF252_FACT_AMT_NT);
						}

					}
				}

				this.save(l120s21b);
			}
		}
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等 清查 S21B -
	 * 只留共用及額度明細表
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public void inventoryL120s21b(String mainId) {

		// 額度明細表額度序號List
		List<String> cntrNoList = this.getCntrNoList(mainId);

		List<L120S21B> delL120s21bList = new ArrayList<L120S21B>();
		List<L120S21B> l120s21bList = this.findL120s21bByMainId(mainId);
		if (l120s21bList != null && !l120s21bList.isEmpty()) {
			for (L120S21B l120s21b : l120s21bList) {
				String cntrNo = Util.nullToSpace(l120s21b.getCntrNo_s21b());
				List<L120S21A> noCoCntrNo = this.findL120s21aByMainIdAndCntrNo(
						mainId, cntrNo);
				if (noCoCntrNo == null || noCoCntrNo.isEmpty()) {
					// 不是共用的額度序號，再檢查額度明細表
					if (cntrNoList != null && !cntrNoList.isEmpty()) {
						if (cntrNoList.contains(cntrNo)) {
							// 是額度明細表的額度序號
							continue;
						} else {
							delL120s21bList.add(l120s21b);
						}
					} else { // 沒有額度明細表
						delL120s21bList.add(l120s21b);
					}
				} else { // 是共用的額度序號
					continue;
				}
			}
		}
		if (delL120s21bList != null && !delL120s21bList.isEmpty()) {
			this.deleteListL120s21b(delL120s21bList);
		}
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public void syncL120s21bInfo(String mainId, boolean reImpl) {
		List<L120S21B> l120s21bList = this.findL120s21bByMainId(mainId);

		// J-110-0485_05097_B1008 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);

		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());

		if (l120s21bList != null && !l120s21bList.isEmpty()) {
			for (L120S21B l120s21b : l120s21bList) {
				String cntrNo = Util.nullToSpace(l120s21b.getCntrNo_s21b());
				boolean has = this.isHasCntrDoc(mainId, cntrNo);
				String orgHas = Util.nullToSpace(l120s21b.getHasCntrDoc_s21b());

				// if (reImpl) { // 引進額度時，更新最新資訊
				// this.getL120s21bInfo(l120s21b, cntrNo, has);
				// } else {
				// if (has && Util.equals(orgHas, "Y")) {
				// // 原HasCntrDoc=Y 且 本次判定也為Y => 不更新以下欄位，怕蓋掉經辦維護的
				// } else {
				// this.getL120s21bInfo(l120s21b, cntrNo, has);
				// }
				// }

				this.getL120s21bInfo(l120s21b, cntrNo, has, branchRate);

				l120s21b.setHasCntrDoc_s21b(has ? "Y" : "N");
				// J-110-0485_05097_B1006 Web
				// e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
				this.clearL120s21bLgdData(l120s21b);
				this.save(l120s21b);
			}
		}
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public void importL120s21bLookupsRate(String mainId) throws CapException {
		List<L120S21B> l120s21bList = this.findL120s21bByMainId(mainId);

		// 風控處邱煥翔8/12：簽案行的國別->改成額度序號前三碼
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);
		// IBranch ibranch = branchService.getBranch(l120m01a.getCaseBrId());

		if (l120s21bList != null && !l120s21bList.isEmpty()) {
			for (L120S21B l120s21b : l120s21bList) {

				// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
				// LGD參數要特別處理，有些只有zh_TW
				Map<String, String> codeMap = this.findLgdCollKindCodeTypeMap(
						l120m01a, l120s21b.getCntrNo_s21b(), "LGD_Rate");

				// 補齊固定欄位
				l120s21b.setUnsecuredRecoveryRateY(Util
						.parseBigDecimal(MapUtils.getString(codeMap,
								"UnsecuredRecoveryRateY", "0"))); // 44
				l120s21b.setUnsecuredRecoveryRateN(Util
						.parseBigDecimal(MapUtils.getString(codeMap,
								"UnsecuredRecoveryRateN", "0"))); // 21
				l120s21b.setGutRecoveryRate(Util.parseBigDecimal(MapUtils
						.getString(codeMap, "GutRecoveryRate", "0"))); // 95
				l120s21b.setPayOffPath(Util.parseBigDecimal(MapUtils.getString(
						codeMap, "PayOffPath", "0"))); // 96
				l120s21b.setNegotiatePath(Util.parseBigDecimal(MapUtils
						.getString(codeMap, "NegotiatePath", "0"))); // 4
				l120s21b.setTurnPositivePath(Util.parseBigDecimal(MapUtils
						.getString(codeMap, "TurnPositivePath", "0"))); // 0
				l120s21b.setNegotiateLossRate(Util.parseBigDecimal(MapUtils
						.getString(codeMap, "NegotiateLossRate", "0"))); // 12
				l120s21b.setTurnPositiveLossRate(Util.parseBigDecimal(MapUtils
						.getString(codeMap, "TurnPositiveLossRate", "0"))); // 0
				l120s21b.setIndirectCost(Util.parseBigDecimal(MapUtils
						.getString(codeMap, "IndirectCost", "0"))); // 0.3

				// J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
				BigDecimal prodRecvRate = Util.parseBigDecimal(MapUtils
						.getString(codeMap, "ProdRecvRate", "90"));// 產品自償回收率
				if (Util.equals(Util.trim(l120s21b.getIsProdRecv()), "Y")) {
					// 01.出口押匯
					// 02.應收帳款
					// 03.開發即期信用狀
					String prodRecvType = Util.trim(l120s21b.getProdRecvType());
					prodRecvRate = Util.parseBigDecimal(MapUtils.getString(
							codeMap, "ProdRecvRate_" + prodRecvType, "90"));// 產品自償回收率
				}
				l120s21b.setProdRecvRate(prodRecvRate);// 90

				// 未建擔保品回收率
				// for (int i = 1; i < 6; i++) {
				// String othCollKind = Util.trim(l120s21b.get("othCollKind"
				// + i));
				// if (Util.notEquals(othCollKind, "")) {
				//
				// Map<String, String> codeCollRateMap = codeTypeService
				// .findByCodeType("LGD_" + othCollKind + "_"
				// + ibranch.getCountryType());
				//
				// if (codeCollRateMap == null
				// || codeCollRateMap.isEmpty()) {
				// codeCollRateMap = codeTypeService
				// .findByCodeType("LGD_" + othCollKind
				// + "_OTH");
				// }
				//
				// for (String key : codeCollRateMap.keySet()) {
				// l120s21b.set("othCollRate" + i,
				// Util.parseBigDecimal(key));
				// }
				//
				// }
				// }

				this.save(l120s21b);
			}
		}
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等 初始化 L120S21B
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public void initialL120s21b(String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L120S21B> l120s21bList = this.findL120s21bByMainId(mainId);
		if (l120s21bList != null && !l120s21bList.isEmpty()) {
			for (L120S21B l120s21b : l120s21bList) {
				l120s21b.setCntrEad_s21b(null);
				l120s21b.setUpdater(user.getUserId());
				l120s21b.setUpdateTime(CapDate.getCurrentTimestamp());
				this.save(l120s21b);
			}
		}
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等 更新 S21B 資訊 -
	 * 信保、信保保證成數；有無公司保證人
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public void getL120s21bInfo(L120S21B l120s21b, String cntrNo,
			boolean hasCntrDoc, BranchRate branchRate) {
		if (l120s21b != null) {
			// 信保、信保保證成數
			l120s21b.setHeadItem1_s21b("N");
			l120s21b.setGutPercent_s21b(null);
			// 有無公司保證人
			l120s21b.setHasGuarantor_s21b("N");
			// 是否為產品自償回收額度
			l120s21b.setIsProdRecv("N");
			// 產品自償回收額度種類
			l120s21b.setProdRecvType("");
			// 帳款管理商類別
			l120s21b.setArAccManagerType_s21b("");
			// 帳款管理商保證成數
			l120s21b.setArAccPercent_s21b(BigDecimal.ZERO);
			// 是否為聯貸案 unionFlag
			l120s21b.setUnionFlag("N");
			// 聯貸幣別 uionCurr
			l120s21b.setUnionCurr("");
			// 本行參貸額度 syndAmt
			l120s21b.setSyndAmt(null);
			// 聯合授信案總金額 unionAmt
			l120s21b.setUnionAmt(null);
			// J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
			// LGD業務種類
			l120s21b.setBussType_s21b("");

			// J-111-0083_05097_B1002 Web
			// e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
			l120s21b.setIsStandAloneAuth_s21b("");

			// J-110-0485_05097_B1008 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
			l120s21b.setCurrentApplyCurr_s21b("");
			l120s21b.setCurrentRate_s21b(null);
			String currentApplyCurr = "";

			if (hasCntrDoc) {
				// 有額度明細表
				String mainId = Util.nullToSpace(l120s21b.getMainId());
				L140M01A l140m01a = lmsService
						.findL140M01AByL120m01cMainIdAndcntrNo(mainId, cntrNo,
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				if (l140m01a != null) {

					// J-110-0485_05097_B1008 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
					currentApplyCurr = Util
							.trim(l140m01a.getCurrentApplyCurr());

					String headItem1 = Util
							.nullToSpace(l140m01a.getHeadItem1());
					l120s21b.setHeadItem1_s21b(Util.isEmpty(headItem1) ? "N"
							: headItem1);
					if (Util.equals(headItem1, "Y")) {
						BigDecimal tGutPercent = null;
						if (Util.equals(Util.trim(l140m01a.getGutType()), "3")) {
							// 批次保證因為沒有保證成數，所以預設為100
							tGutPercent = new BigDecimal(100);
						} else {
							tGutPercent = (l140m01a.getGutPercent() == null) ? null
									: l140m01a.getGutPercent();
						}

						l120s21b.setGutPercent_s21b(tGutPercent);
					}

					Set<L140M01I> l140m01is = l140m01a.getL140m01i();
					for (L140M01I l140m01i : l140m01is) {
						String type = l140m01i.getType();
						String rType = l140m01i.getRType();
						if (Util.equals(type, "2") && (Util.equals(rType, "G"))) {
							l120s21b.setHasGuarantor_s21b("Y");
						}
					}

					// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
					// 額度明細表共同借款人
					Set<L140M01J> l140m01js = l140m01a.getL140m01j();
					for (L140M01J l140m01j : l140m01js) {
						String custIdj = l140m01j.getCustId();
						String dupNoj = l140m01j.getDupNo();
						if (Util.isNotEmpty(custIdj) && Util.isNotEmpty(dupNoj)) {

							L120S01B l120s01b = l120s01bDao.findByUniqueKey(
									l120s21b.getMainId(), Util.trim(custIdj),
									Util.trim(dupNoj));

							if (l120s01b != null) {
								String busCode = Util.trim(l120s01b
										.getBusCode());
								if (Util.isNotEmpty(busCode)) {
									if (Util.notEquals(busCode, "060000")
											&& Util.notEquals(busCode, "130300")) {
										// 企業戶
										l120s21b.setHasGuarantor_s21b("Y");
									}
								}
							}
						}
					}

					// J-112-0210_05097_B1001 Web
					// e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
					if (Util.notEquals(l120s21b.getHasGuarantor_s21b(), "Y")) {
						l120s21b.setIsGuarantorEffect_s21b("");
						l120s21b.setGuarantorId_s21b("");
						l120s21b.setGuarantorDupNo_s21b("");
						l120s21b.setGuarantorRName_s21b("");
						l120s21b.setGuarantorCrdType_s21b("");
						l120s21b.setGuarantorCrdTYear_s21b(null);
						l120s21b.setGuarantorGradeOrg_s21b("");
						l120s21b.setGuarantorGradeNew_s21b("");
						l120s21b.setGuarantorCptlCurr_s21b("");
						l120s21b.setGuarantorCptlAmt_s21b(null);
						l120s21b.setGuarantorCptlUnit_s21b(null);
						l120s21b.setGuarantorNtCode_s21b("");
						l120s21b.setGuarantorStockStatus_s21b("");
						l120s21b.setGuarantorStockNum_s21b("");
					}

					// 是否為產品自償回收額度
					// 額度明細表科目為出口押匯或應收帳款
					String[] sysExperf = StringUtils.split(Util.trim(lmsService
							.getSysParamDataValue("EXPERF")), ",");
					Set<L140M01C> l140m01cs = l140m01a.getL140m01c();
					for (L140M01C l140m01c : l140m01cs) {
						if (Arrays.asList(sysExperf).contains(
								l140m01c.getLoanTP())) {
							l120s21b.setIsProdRecv("Y");
							l120s21b.setProdRecvType("01");// 01.出口押匯
							break;
						}
					}

					boolean isShowAr = this.chkIsNeedARBuyer(
							Util.trim(l140m01a.getSnoKind()),
							Util.trim(l140m01a.getIsEfin()), l140m01a);
					if (isShowAr) {
						if (Util.equals(
								Util.trim(l140m01a.getArAccManagerType()), "2")
								|| Util.equals(Util.trim(l140m01a
										.getArAccManagerType()), "3")) {
							// 洽風控煥翔確認，帳款管理商類別 2.IMPORT
							// FACTOR、3.保險公司才算有產品自償回收，1.本行沒有
							l120s21b.setIsProdRecv("Y");
							l120s21b.setProdRecvType("02");// 02.應收帳款
							l120s21b.setArAccManagerType_s21b(Util
									.trim(l140m01a.getArAccManagerType()));
							// 目前額度明細表沒有帳款管理商保證成數

							if (l140m01a.getArAccPercent() != null) {
								// 不要超過100
								l120s21b.setArAccPercent_s21b(l140m01a
										.getArAccPercent().min(
												new BigDecimal(100)));

							}

						}

					}

					// J-112-0210_05097_B1001 Web
					// e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
					// 開發即期信用狀
					//
					String[] prodRecvType03 = StringUtils
							.split(Util
									.trim(lmsService
											.getSysParamDataValue("LGD_PRODRECVTYPE_03_SUBJECT")),
									",");

					for (L140M01C l140m01c : l140m01cs) {
						if (Arrays.asList(prodRecvType03).contains(
								l140m01c.getLoanTP())) {
							l120s21b.setIsProdRecv("Y");
							l120s21b.setProdRecvType("03");// 03.開發即期信用狀s
							break;
						}
					}

					// 聯貸資訊*********************************************
					L120M01B l120m01b = l120m01bDao.findByUniqueKey(mainId);
					// Util.equals(Util.trim(l120m01b.getUnitCase()), "Y")&&
					if (Util.equals(l140m01a.getUnitCase2(), "Y")) {
						// 是否為聯貸案 unionFlag
						l120s21b.setUnionFlag("Y");

						if (Util.isEmpty(l140m01a.getSyndLoanCurr())) {
							// 自動引進聯貸比率
							String syndLoanCurr = "";
							BigDecimal syndLoanTotal = BigDecimal.ZERO;
							BigDecimal syndLoanPart = BigDecimal.ZERO;

							if (Util.notEquals(cntrNo, "")) {
								List<Map<String, Object>> listLnf273 = dwdbBaseService
										.findDW_LNF273_by_cntrNo(cntrNo);

								if (listLnf273 != null && !listLnf273.isEmpty()) {

									for (Map<String, Object> lnf273Map : listLnf273) {

										if (Util.notEquals(Util.trim(MapUtils
												.getString(lnf273Map,
														"LOAN_PART")), "")
												&& Util.notEquals(
														Util.trim(MapUtils
																.getString(
																		lnf273Map,
																		"LOAN_TOTAL")),
														"")) {
											syndLoanCurr = "TWD";
											syndLoanTotal = Util.equals(Util
													.trim(MapUtils.getString(
															lnf273Map,
															"LOAN_TOTAL")), "") ? BigDecimal.ZERO
													: Util.parseBigDecimal(MapUtils
															.getString(
																	lnf273Map,
																	"LOAN_TOTAL"));
											syndLoanPart = Util.equals(Util
													.trim(MapUtils.getString(
															lnf273Map,
															"LOAN_PART")), "") ? BigDecimal.ZERO
													: Util.parseBigDecimal(MapUtils
															.getString(
																	lnf273Map,
																	"LOAN_PART"));
											break;
										}

									}

								}
							}

							// 聯貸幣別 uionCurr
							l120s21b.setUnionCurr(syndLoanCurr);
							// 本行參貸額度 syndAmt
							l120s21b.setSyndAmt(syndLoanPart);
							// 聯合授信案總金額 unionAmt
							l120s21b.setUnionAmt(syndLoanTotal);
						} else {
							// 聯貸幣別 uionCurr
							l120s21b.setUnionCurr(Util.trim(l140m01a
									.getSyndLoanCurr()));
							// 本行參貸額度 syndAmt
							l120s21b.setSyndAmt(l140m01a.getSyndLoanPart());
							// 聯合授信案總金額 unionAmt
							l120s21b.setUnionAmt(l140m01a.getSyndLoanTotal());
						}

					}

					// J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
					// 業務分類
					l120s21b.setBussType_s21b(Util.trim(this
							.getCntrBussType(l140m01a)));

					// J-111-0083_05097_B1002 Web
					// e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
					l120s21b.setIsStandAloneAuth_s21b(l140m01a
							.getIsStandAloneAuth());

				}
			} else {
				// 信保、信保保證成數
				Map<String, Object> misln20 = misMisLN20Service
						.findByCntrNo(cntrNo);
				if (misln20 != null && !misln20.isEmpty()) {
					String LNF020_FACT_TYPE = Util.trim(MapUtils.getString(
							misln20, "LNF020_FACT_TYPE"));
					BigDecimal LNF020_IPFD_RATE = Util.equals(Util
							.trim(MapUtils.getString(misln20,
									"LNF020_IPFD_RATE")), "") ? BigDecimal.ZERO
							: Util.parseBigDecimal(Util.trim(MapUtils
									.getString(misln20, "LNF020_IPFD_RATE")));
					if (Util.equals(LNF020_FACT_TYPE, "20")
							|| Util.equals(LNF020_FACT_TYPE, "50")) {
						if (LNF020_IPFD_RATE.compareTo(BigDecimal.ZERO) > 0) {
							l120s21b.setHeadItem1_s21b("Y");
							l120s21b.setGutPercent_s21b(LNF020_IPFD_RATE);
						}
					}
				}

				// 有無公司保證人
				List<Map<String, Object>> ellngteeData = misEllngteeService
						.findByCntrNo(cntrNo);
				for (Map<String, Object> ellData : ellngteeData) {
					String lngeid = "";
					String lngeFlag = "";

					lngeid = Util.trim(MapUtils.getString(ellData, "LNGEID"));
					lngeFlag = MapUtils.getString(ellData, "LNGEFLAG");

					if (Util.equals(lngeFlag, "G")
							|| Util.equals(lngeFlag, "N")) {
						// C: 共同借款人
						// D: 共同發票人　
						// E: 票據債務人（指金融交易之擔保背書）
						// G: 連帶保證人，擔保品提供人兼連帶保證人
						// L: 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人
						// S: 擔保品提供人
						// N: ㄧ般保證人

						if (StringUtils.length(lngeid) == 8
								|| Util.equals(lngeid.substring(2, 3), "Z")) {
							l120s21b.setHasGuarantor_s21b("Y");
						}
					}
				}
			}

			// J-111-0278_05097_B1001 Web
			// e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
			if (Util.notEquals(currentApplyCurr, "")) {
				l120s21b.setCurrentApplyCurr_s21b(currentApplyCurr);
				if (Util.equals(currentApplyCurr, UtilConstants.CURR.TWD)) {
					l120s21b.setCurrentRate_s21b(BigDecimal.ONE);
				} else {
					l120s21b.setCurrentRate_s21b(branchRate
							.toTWDRate(currentApplyCurr));
				}
			}

		}
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public void calcTWD(String mainId, BranchRate branchRate) {
		List<L120S21A> listL120s21a = this.findL120s21aByMainId(mainId);
		for (L120S21A l120s21a : listL120s21a) {
			// 共用額度
			String currCo_s21a = Util.nullToSpace(l120s21a.getCurrCo_s21a());
			BigDecimal factAmtCo_s21a = l120s21a.getFactAmtCo_s21a();
			if (Util.notEquals(currCo_s21a, UtilConstants.CURR.TWD)) {
				l120s21a.setFactAmtCoTwd_s21a(branchRate.toTWDAmt(
						(Util.isEmpty(currCo_s21a) ? UtilConstants.CURR.TWD
								: currCo_s21a),
						(factAmtCo_s21a == null ? BigDecimal.ZERO
								: factAmtCo_s21a)).setScale(0,
						BigDecimal.ROUND_HALF_UP));
			} else {
				l120s21a.setFactAmtCoTwd_s21a(factAmtCo_s21a == null ? BigDecimal.ZERO
						: factAmtCo_s21a);
			}

			// 現請額度
			String currentApplyCurr_s21a = Util.nullToSpace(l120s21a
					.getCurrentApplyCurr_s21a());
			BigDecimal currentApplyAmt_s21a = l120s21a
					.getCurrentApplyAmt_s21a();
			if (Util.notEquals(currentApplyCurr_s21a, UtilConstants.CURR.TWD)) {
				l120s21a.setCurrentApplyAmtTwd_s21a(branchRate
						.toTWDAmt(
								(Util.isEmpty(currentApplyCurr_s21a) ? UtilConstants.CURR.TWD
										: currentApplyCurr_s21a),
								(currentApplyAmt_s21a == null ? BigDecimal.ZERO
										: currentApplyAmt_s21a)).setScale(0,
								BigDecimal.ROUND_HALF_UP));
			} else {
				l120s21a.setCurrentApplyAmtTwd_s21a(currentApplyAmt_s21a == null ? BigDecimal.ZERO
						: currentApplyAmt_s21a);
			}

			// 餘額
			String blCurr_s21a = Util.nullToSpace(l120s21a.getBlCurr_s21a());
			BigDecimal blAmt_s21a = l120s21a.getBlAmt_s21a();
			if (Util.notEquals(blCurr_s21a, UtilConstants.CURR.TWD)) {
				l120s21a.setBlAmtTwd_s21a(branchRate.toTWDAmt(
						(Util.isEmpty(blCurr_s21a) ? UtilConstants.CURR.TWD
								: blCurr_s21a),
						(blAmt_s21a == null ? BigDecimal.ZERO : blAmt_s21a))
						.setScale(0, BigDecimal.ROUND_HALF_UP));
			} else {
				l120s21a.setBlAmtTwd_s21a(blAmt_s21a == null ? BigDecimal.ZERO
						: blAmt_s21a);
			}

			// 應收利息
			String rcvCurr_s21a = Util.nullToSpace(l120s21a.getRcvCurr_s21a());
			BigDecimal rcvInt_s21a = l120s21a.getRcvInt_s21a();
			if (Util.notEquals(rcvCurr_s21a, UtilConstants.CURR.TWD)) {
				l120s21a.setRcvIntTwd_s21a(branchRate.toTWDAmt(
						(Util.isEmpty(rcvCurr_s21a) ? UtilConstants.CURR.TWD
								: rcvCurr_s21a),
						(rcvInt_s21a == null ? BigDecimal.ZERO : rcvInt_s21a))
						.setScale(0, BigDecimal.ROUND_HALF_UP));
			} else {
				l120s21a.setRcvIntTwd_s21a(rcvInt_s21a == null ? BigDecimal.ZERO
						: rcvInt_s21a);
			}
		}
	}

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等 更新 S21B 資訊 -
	 * 信保、信保保證成數；有無公司保證人
	 * 
	 * @param mainId
	 * @return
	 */
	@Override
	public String calcLGDInner(String mainId)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		StringBuffer errorMsgBuf = new StringBuffer("");

		String errorMsg = "";

		// J-110-0485_05097_B1008 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);

		// step 1. 補齊 L120S21B - 補齊有額度明細表且無共用
		this.furtherL120s21b(mainId);

		// step 2. 清查 S21B - 只留共用及額度明細表
		this.inventoryL120s21b(mainId);

		// step 3. 補齊 L120S21B 資訊
		this.syncL120s21bInfo(mainId, false);

		// step 4. 引進相關比率(EXCEL Lookups頁籤)
		this.importL120s21bLookupsRate(mainId);

		// step 5. 計算額度LGD
		List<L120S21B> l120s21bList = this.findL120s21bByMainId(mainId);

		// step 5.1 檢核欄位是否有缺
		StringBuffer allErrMsg = new StringBuffer("");
		for (L120S21B l120s21b : l120s21bList) {
			if (Util.equals(Util.nullToSpace(l120s21b.getHasCntrDoc_s21b()),
					"Y")) {
				String tErrMsg = this.chkL120s21bData(l120s21b);
				if (Util.notEquals(tErrMsg, "")) {
					allErrMsg.append((Util.isEmpty(tErrMsg) ? "" : "<BR>"))
							.append(tErrMsg);
				}
			}
		}
		if (Util.notEquals(allErrMsg.toString(), "")) {
			throw new CapMessageException(
					RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, allErrMsg.toString()), getClass());
		}

		// step 5.2 計算額度LGD
		for (L120S21B l120s21b : l120s21bList) {
			if (Util.equals(Util.nullToSpace(l120s21b.getHasCntrDoc_s21b()),
					"Y")) {
				errorMsg = this.caculateLgd(l120s21b);
				if (Util.notEquals(errorMsg, "")) {
					throw new CapMessageException(
							RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, "calcLGD「計算LGD」執行失敗！" + errorMsg),
							getClass());
				}
			}
		}

		// step 6. 計算借款人LGD
		String errorMsg_custLgd = this.calcCustLgd(mainId);
		if (Util.notEquals(errorMsg_custLgd, "")) {
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤,
					"calcCustLgd「計算借款人LGD」執行失敗！" + errorMsg_custLgd), getClass());
		}

		// step 6. 計算業務種類LGD
		String errorMsg_bussLgd = this.calcBussLgd(mainId);
		if (Util.notEquals(errorMsg_bussLgd, "")) {
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤,
					"calcBussLgd「計算業務種類LGD」執行失敗！" + errorMsg_bussLgd), getClass());
		}

		// step 8. 上傳DW
		lmsService.upLoadDwLgd(l120m01a);

		return errorMsgBuf.toString();
	}

	/**
	 * J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 */
	@Override
	public String calcBussLgd(String mainId) throws CapException {
		String errorMsg = "";
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSLgdCommomPage.class);

		// 借款人LGD
		List<L120S21B> l120s21blist = this.findL120s21bByMainId(mainId);

		if (l120s21blist == null || l120s21blist.isEmpty()) {
			// errMsg10=無借款人額度LGD明細資料！！
			return pop.getProperty("errMsg10");
		}

		Map<String, Object> execBussLgd = new HashMap<String, Object>();
		for (L120S21B l120s21b : l120s21blist) {
			String bussType = Util.trim(l120s21b.getBussType_s21b());
			String custId = Util.trim(l120s21b.getCustId_s21b());
			String dupNo = Util.trim(l120s21b.getDupNo_s21b());
			String custKey = custId + "-" + dupNo + "-" + bussType;

			if (execBussLgd.containsKey(custKey)) {
				// 已經執行過
				continue;
			} else {

				// BY借款人項下所有LGD明細
				List<L120S21B> l120s21bs = this
						.findL120s21bByMainIdCustIdAndBussType(mainId, custId,
								dupNo, bussType);
				BigDecimal totalEad = BigDecimal.ZERO;
				BigDecimal totalEadxLgd = BigDecimal.ZERO;
				BigDecimal bussLgd = BigDecimal.ZERO;
				if (l120s21bs != null && !l120s21bs.isEmpty()) {
					for (L120S21B tl120s21b : l120s21bs) {

						if (Util.equals(
								Util.trim(tl120s21b.getHasCntrDoc_s21b()), "Y")) {

							BigDecimal cntrEad_s21b = tl120s21b
									.getCntrEad_s21b();
							BigDecimal expectLgd = tl120s21b.getExpectLgd();
							String cntrNo = Util.trim(tl120s21b
									.getCntrNo_s21b());
							if (expectLgd == null) {
								// errMsg11=「{0}」無額度LGD資訊！！
								// L120S21C.colShareRate_s21c=分配比率
								return MessageFormat.format(
										pop.getProperty("errMsg11"), cntrNo);
							}

							totalEad = totalEad.add(cntrEad_s21b);
							totalEadxLgd = totalEadxLgd.add(cntrEad_s21b
									.multiply(expectLgd));

						}
					}

					if (totalEad.compareTo(BigDecimal.ZERO) == 0) {
						bussLgd = BigDecimal.ZERO;
					} else {
						bussLgd = totalEadxLgd.divide(totalEad, 2,
								BigDecimal.ROUND_HALF_UP);
					}

					for (L120S21B tl120s21b : l120s21bs) {
						tl120s21b.setBussLgd(bussLgd.setScale(2,
								BigDecimal.ROUND_HALF_UP));
						this.save(tl120s21b);
					}

				}
				// 已經執行過
				execBussLgd.put(custKey, custKey);
			}
		}

		return errorMsg;
	}

	/**
	 * 取得此份簽報書之額度明細表序號及ID
	 */
	public Map<String, String> getCntrNoMap(String mainId) {
		HashMap<String, String> cntrNoMap = new HashMap<String, String>();

		List<L140M01A> listL140m01a = l140m01aDao
				.findL140m01aListByL120m01cMainId(mainId,
						UtilConstants.Cntrdoc.ItemType.額度明細表, null);

		if (listL140m01a != null && !listL140m01a.isEmpty()) {
			for (L140M01A l140m01a : listL140m01a) {
				if (Util.isNotEmpty(Util.nullToSpace(l140m01a.getCntrNo()))) {
					if (!cntrNoMap.containsKey(Util.nullToSpace(l140m01a
							.getCntrNo()))) {
						cntrNoMap
								.put(Util.nullToSpace(l140m01a.getCntrNo()),
										Util.addSpaceWithValue(Util
												.nullToSpace(l140m01a
														.getCustId()), 10)
												+ Util.nullToSpace(l140m01a
														.getDupNo()));
					}
				}
			}
		}
		return cntrNoMap;
	}

	/**
	 * 取得此份簽報書之額度明細表序號
	 */
	@Override
	public List<String> getCntrNoList(String mainId) {
		List<String> cntrNoList = new ArrayList<String>();
		List<L140M01A> listL140m01a = l140m01aDao
				.findL140m01aListByL120m01cMainId(mainId,
						UtilConstants.Cntrdoc.ItemType.額度明細表, null);

		if (listL140m01a != null && !listL140m01a.isEmpty()) {
			for (L140M01A l140m01a : listL140m01a) {
				if (Util.isNotEmpty(Util.nullToSpace(l140m01a.getCntrNo()))) {
					if (!cntrNoList.contains(Util.nullToSpace(l140m01a
							.getCntrNo()))) {
						cntrNoList.add(Util.nullToSpace(l140m01a.getCntrNo()));
					}
				}
			}
		}
		return cntrNoList;
	}

	/**
	 * 
	 * @param mainId
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Boolean isHasCntrDoc(String mainId, String cntrNo) {
		boolean has = false;
		List<String> cntrNoList = this.getCntrNoList(mainId);
		if (cntrNoList != null && !cntrNoList.isEmpty()) {
			if (cntrNoList.contains(cntrNo)) {
				has = true;
			}
		}

		// J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		// 排除不計算LGD的額度
		if (has) {
			L140M01A l140m01a = lmsService
					.findL140M01AByL120m01cMainIdAndcntrNo(mainId, cntrNo,
							UtilConstants.Cntrdoc.ItemType.額度明細表);
			if (l140m01a != null) {
				boolean isNeed = this.isL140m01aNeedLgd(l140m01a, "2");
				if (!isNeed) {
					has = false;
				}
			}
		}

		return has;
	}

	public String chkL120s21bData(L120S21B l120s21b) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSLgdCommomPage.class);
		StringBuffer temp = new StringBuffer();

		// 加減核**************************************************************

		// 統計未填欄位數
		int countItme = 1;

		if (Util.isEmpty(l120s21b.getHasGuarantor_s21b())) {
			// L120S21B.hasGuarantor_s21b=有無公司保證人
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S21B.hasGuarantor_s21b"));
		}

		if (Util.isEmpty(l120s21b.getHeadItem1_s21b())) {
			// L120S21B.headItem1_s21b=本額度有無送保
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S21B.headItem1_s21b"));
		}

		if (Util.equals(Util.trim(l120s21b.getHeadItem1_s21b()), "Y")) {
			if (Util.isEmpty(l120s21b.getGutPercent_s21b())) {
				// L120S21B.gutPercent_s21b=信保保證成數
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21B.gutPercent_s21b"));
			}
		}

		if (Util.isEmpty(l120s21b.getUnionFlag())) {
			// L120S21B.unionFlag=是否為聯貸案
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S21B.unionFlag"));
		}

		if (Util.equals(Util.trim(l120s21b.getUnionFlag()), "Y")) {
			if (Util.isEmpty(l120s21b.getUnionCurr())) {
				// L120S21B.unionCurr=聯貸幣別
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21B.unionCurr"));
			}

			if (Util.isEmpty(l120s21b.getSyndAmt())) {
				// L120S21B.syndAmt=本行參貸額度(預計)
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21B.syndAmt"));
			}

			if (Util.isEmpty(l120s21b.getUnionAmt())
					|| l120s21b.getUnionAmt().compareTo(BigDecimal.ZERO) <= 0) {
				// L120S21B.unionAmt=聯合授信案總金額(預計)
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21B.unionAmt"));
			}

		}

		// J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		if (Util.isEmpty(l120s21b.getBussType_s21b())) {
			// L120S21B.bussType_s21b=LGD業務種類
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S21B.bussType_s21b"));
		}

		if (temp.length() > 0) {
			// errMsg13=尚有必填欄位未填
			temp.insert(0,
					l120s21b.getCntrNo_s21b() + pop.getProperty("errMsg13")
							+ "<br/>");
		}

		return temp.toString();
	}

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public boolean chkIsNeedARBuyer(String snoKind, String isEfin,
			L140M01A l140m01a) {
		boolean isNeedChkARBuyer = false;

		// 額度控管種類:61(賣方)
		// isEFIN=N
		// 科目:947(應收款－國內無追索權),949(應收款－出口無追索權)

		if (Util.equals(Util.trim(snoKind), "61")
				|| Util.equals(Util.trim(snoKind), "62")
				|| Util.equals(Util.trim(snoKind), "63")) {
			if (Util.equals(Util.trim(isEfin), "N")) {
				// 檢查無追索科目
				// 用來暫存已登錄授信科目
				HashMap<String, String> itemMap = new HashMap<String, String>();
				Set<L140M01C> l140m01cs = l140m01a.getL140m01c();

				for (L140M01C l140m01c : l140m01cs) {
					itemMap.put(l140m01c.getLoanTP(), "");
				}

				String checkItem4 = "504,947,949";
				String[] item4 = checkItem4.split(",");
				List<String> asList4 = Arrays.asList(item4);

				for (String key : itemMap.keySet()) {
					if (asList4.contains(key)) {
						isNeedChkARBuyer = true;
					}
				}
			}
		}

		return isNeedChkARBuyer;
	}

	/**
	 * 共用資訊重新引進額度明細表現請額度
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 */
	@Override
	public String reNewCntrDocCreditData(String mainId) throws CapException {

		String errorMsg = "";

		// 共用重新引進額度並計算EAD
		List<L120S21A> l120s21aList = this.findL120s21aByMainId(mainId);
		if (l120s21aList != null && !l120s21aList.isEmpty()) {
			// 有共用時.......................................

			// 1.重新引進額度資訊並分配EAD
			List<L120S21A> newL120s21aList = new ArrayList<L120S21A>();
			for (L120S21A l120s21a : l120s21aList) {
				if (this.setLatestLoanDataByCntrNo(mainId,
						Util.nullToSpace(l120s21a.getCntrNo_s21a()), l120s21a,
						true)) {
					newL120s21aList.add(l120s21a);
				}
			}
			if (newL120s21aList.size() > 0) {
				this.saveL120s21aList(newL120s21aList);
			}
		}

		return errorMsg;
	}

	/**
	 * 
	 * @param mainId
	 * @param cntrNo
	 * @param l120s21a
	 * @param setOnlyFromL140m01a
	 * @return
	 */
	@Override
	public boolean setLatestLoanDataByCntrNo(String mainId, String cntrNo,
			L120S21A l120s21a, boolean setOnlyFromL140m01a) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSLgdCommomPage.class);
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);

		// 依目前簽案行做計算幣別
		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());
		String hasData = "N";

		if (l120s21a == null) {
			return false;
		}

		Map<String, Object> data = new HashMap<String, Object>();
		data = this.getLatestLoanDataByCntrNo(branchRate, mainId,
				Util.nullToSpace(l120s21a.getCntrNo_s21a()),
				setOnlyFromL140m01a);

		hasData = (String) MapUtils.getObject(data, "hasData");

		if (Util.equals(hasData, "Y")) {

			l120s21a.setCurrentApplyCurr_s21a((String) MapUtils.getObject(data,
					"currentApplyCurr"));
			l120s21a.setCurrentApplyAmt_s21a((BigDecimal) MapUtils.getObject(
					data, "currentApplyAmt"));
			l120s21a.setCurrentApplyAmtTwd_s21a((BigDecimal) MapUtils
					.getObject(data, "currentApplyAmtTwd"));
			l120s21a.setBlCurr_s21a((String) MapUtils.getObject(data, "blCurr"));
			l120s21a.setBlAmt_s21a((BigDecimal) MapUtils.getObject(data,
					"blAmt"));
			l120s21a.setBlAmtTwd_s21a((BigDecimal) MapUtils.getObject(data,
					"blAmtTwd"));
			l120s21a.setRcvCurr_s21a((String) MapUtils.getObject(data,
					"rcvCurr"));
			l120s21a.setRcvInt_s21a((BigDecimal) MapUtils.getObject(data,
					"rcvInt"));
			l120s21a.setRcvIntTwd_s21a((BigDecimal) MapUtils.getObject(data,
					"rcvIntTwd"));

			l120s21a.setReUse_s21a((String) MapUtils.getObject(data, "reUse"));
		}
		return Util.equals(hasData, "Y") ? true : false;
	}

	public Map<String, Object> getLatestLoanDataByCntrNo(BranchRate branchRate,
			String mainId, String cntrNo, boolean setOnlyFromL140m01a) {
		// 取得資料順序：LNF252 => 同一份簽報書 => 其他簽報書已核准 (不管性質別)
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("currentApplyCurr", ""); // 現請額度幣別
		resultMap.put("currentApplyAmt", BigDecimal.ZERO); // 現請額度原幣金額
		resultMap.put("currentApplyAmtTwd", BigDecimal.ZERO); // 現請額度台幣金額
		resultMap.put("blCurr", ""); // 餘額幣別
		resultMap.put("blAmt", BigDecimal.ZERO); // 餘額金額
		resultMap.put("blAmtTwd", BigDecimal.ZERO); // 餘額金額台幣
		resultMap.put("rcvCurr", ""); // 應收利息幣別
		resultMap.put("rcvInt", BigDecimal.ZERO); // 應收利息金額
		resultMap.put("rcvIntTwd", BigDecimal.ZERO); // 應收利息等值台幣金額
		resultMap.put("reUse", ""); // 循環註記
		resultMap.put("hasData", "N"); // 有無資料

		boolean hasData = false;

		if (true) {
			// 以額度明細表為準
			L140M01A l140m01a = lmsService
					.findL140M01AByL120m01cMainIdAndcntrNo(mainId, cntrNo,
							UtilConstants.Cntrdoc.ItemType.額度明細表);

			if (l140m01a != null) {

				hasData = true;
				String currentApplyCurr = Util.equals(
						Util.trim(l140m01a.getCurrentApplyCurr()), "") ? UtilConstants.CURR.TWD
						: Util.trim(l140m01a.getCurrentApplyCurr());
				BigDecimal currentApplyAmt = l140m01a.getCurrentApplyAmt() == null ? BigDecimal.ZERO
						: l140m01a.getCurrentApplyAmt();
				resultMap.put("currentApplyCurr", currentApplyCurr);
				resultMap.put("currentApplyAmt", currentApplyAmt);
				if (Util.notEquals(currentApplyCurr, UtilConstants.CURR.TWD)) {
					resultMap.put(
							"currentApplyAmtTwd",
							branchRate.toTWDAmt(currentApplyCurr,
									currentApplyAmt).setScale(0,
									BigDecimal.ROUND_HALF_UP));
				} else {
					resultMap.put("currentApplyAmtTwd", currentApplyAmt);
				}

				String blCurr = Util
						.equals(Util.trim(l140m01a.getBLCurr()), "") ? currentApplyCurr
						: Util.trim(l140m01a.getBLCurr());
				BigDecimal blAmt = l140m01a.getBLAmt() == null ? BigDecimal.ZERO
						: l140m01a.getBLAmt();

				resultMap.put("blCurr", blCurr);
				resultMap.put("blAmt", blAmt);
				if (Util.notEquals(blCurr, UtilConstants.CURR.TWD)) {
					resultMap.put(
							"blAmtTwd",
							branchRate.toTWDAmt(blCurr, blAmt).setScale(0,
									BigDecimal.ROUND_HALF_UP));
				} else {
					resultMap.put("blAmtTwd", blAmt);
				}

				// 應收利息
				String rcvCurr = Util.equals(Util.trim(l140m01a.getRcvCurr()),
						"") ? currentApplyCurr : Util.trim(l140m01a
						.getRcvCurr());
				BigDecimal rcvInt = l140m01a.getRcvInt() == null ? BigDecimal.ZERO
						: l140m01a.getRcvInt();
				resultMap.put("rcvCurr", rcvCurr);
				resultMap.put("rcvInt", rcvInt);
				if (Util.notEquals(rcvCurr, UtilConstants.CURR.TWD)) {
					resultMap.put(
							"rcvIntTwd",
							branchRate.toTWDAmt(rcvCurr, rcvInt).setScale(0,
									BigDecimal.ROUND_HALF_UP));
				} else {
					resultMap.put("rcvIntTwd", rcvInt);
				}

				String reUse = Util.nullToSpace(l140m01a.getReUse());
				resultMap.put("reUse", reUse); // 1.不循環使用2.循環使用

			}
		}

		if (setOnlyFromL140m01a) {
			// 只取額度明細表，不抓ALOAN
			resultMap.put("hasData", hasData ? "Y" : "N");
			return resultMap;
		}

		// 無額度明細表再找帳務系統*******************************************************************************

		if (!hasData) {

			boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
					.getBranch(Util.getLeftStr(cntrNo, 3)).getBrNoFlag());

			if (isOverSea) {
				// 海外
				// 額度、餘額
				String ovsCurr = "";
				List<Map<String, Object>> lncntrovsList = dwdbBaseService
						.findDW_LNCNTROVS_By_CntrNo(cntrNo);
				if (lncntrovsList != null && !lncntrovsList.isEmpty()) {
					for (Map<String, Object> lncntrovs : lncntrovsList) {
						hasData = true;
						String LNF022_FACT_SWFT = Util.trim(MapUtils.getString(
								lncntrovs, "LNF022_FACT_SWFT"));
						LNF022_FACT_SWFT = Util.isEmpty(LNF022_FACT_SWFT) ? UtilConstants.CURR.TWD
								: LNF022_FACT_SWFT;
						ovsCurr = LNF022_FACT_SWFT;
						BigDecimal LNF022_ADJ_FAMT = Util.equals(MapUtils
								.getString(lncntrovs, "LNF022_ADJ_FAMT"), "") ? BigDecimal.ZERO
								: Util.parseBigDecimal(MapUtils.getString(
										lncntrovs, "LNF022_ADJ_FAMT"));

						BigDecimal LNF022_ADJ_FAMT_T = Util.equals(MapUtils
								.getString(lncntrovs, "LNF022_ADJ_FAMT_T"), "") ? BigDecimal.ZERO
								: Util.parseBigDecimal(MapUtils.getString(
										lncntrovs, "LNF022_ADJ_FAMT_T"));

						BigDecimal LNF022_LOAN_BAL_S = Util.equals(MapUtils
								.getString(lncntrovs, "LNF022_LOAN_BAL_S"), "") ? BigDecimal.ZERO
								: Util.parseBigDecimal(MapUtils.getString(
										lncntrovs, "LNF022_LOAN_BAL_S"));
						BigDecimal LNF022_LOAN_BAL_N = Util.equals(MapUtils
								.getString(lncntrovs, "LNF022_LOAN_BAL_N"), "") ? BigDecimal.ZERO
								: Util.parseBigDecimal(MapUtils.getString(
										lncntrovs, "LNF022_LOAN_BAL_N"));

						BigDecimal LOAN_BAL_OS = Util.equals(
								MapUtils.getString(lncntrovs, "LOAN_BAL_OS"),
								"") ? BigDecimal.ZERO : Util
								.parseBigDecimal(MapUtils.getString(lncntrovs,
										"LOAN_BAL_OS"));
						BigDecimal LOAN_BAL_ON = Util.equals(
								MapUtils.getString(lncntrovs, "LOAN_BAL_ON"),
								"") ? BigDecimal.ZERO : Util
								.parseBigDecimal(MapUtils.getString(lncntrovs,
										"LOAN_BAL_ON"));

						BigDecimal LNF022_LOAN_BAL = LOAN_BAL_OS
								.add(LOAN_BAL_ON);
						BigDecimal LNF022_LOAN_BAL_TW = LNF022_LOAN_BAL_S
								.add(LNF022_LOAN_BAL_N);

						String REVOVLE = Util.trim(MapUtils.getString(
								lncntrovs, "REVOVLE"));

						resultMap.put("currentApplyCurr", LNF022_FACT_SWFT);
						resultMap.put("currentApplyAmt", LNF022_ADJ_FAMT);
						resultMap.put("currentApplyAmtTwd", LNF022_ADJ_FAMT_T
								.setScale(0, BigDecimal.ROUND_HALF_UP));
						resultMap.put("blCurr", LNF022_FACT_SWFT);
						resultMap.put("blAmt", LNF022_LOAN_BAL);
						resultMap.put("blAmtTwd", LNF022_LOAN_BAL_TW.setScale(
								0, BigDecimal.ROUND_HALF_UP));
						resultMap.put("reUse", Util.equals(REVOVLE, "Y") ? "2"
								: "1"); // 1.不循環使用2.循環使用

						break;
					}
				}

				// 應收利息
				if (!Util.isEmpty(ovsCurr)) {

					List<Map<String, Object>> dwAslndavgovsList = dwdbBaseService
							.findSumDW_ASLNDAVGOVSByCntrNo(cntrNo);
					BigDecimal INT_AMT_T = BigDecimal.ZERO;
					if (dwAslndavgovsList != null
							&& !dwAslndavgovsList.isEmpty()) {
						for (Map<String, Object> dwAslndavgovs : dwAslndavgovsList) {
							String INT_CURR = MapUtils.getString(dwAslndavgovs,
									"INT_CURR", ovsCurr);
							BigDecimal xINT_AMT_T = Util.equals(MapUtils
									.getString(dwAslndavgovs, "INT_AMT_T"), "") ? BigDecimal.ZERO
									: Util.parseBigDecimal(MapUtils.getString(
											dwAslndavgovs, "INT_AMT_T"));

							if (!Util.isEmpty(INT_CURR)
									&& xINT_AMT_T.compareTo(BigDecimal.ZERO) > 0) {
								if (Util.equals(INT_CURR, ovsCurr)) {
									INT_AMT_T = INT_AMT_T.add(xINT_AMT_T);
								} else {
									INT_AMT_T = INT_AMT_T.add(branchRate
											.toOtherAmt(INT_CURR, ovsCurr,
													xINT_AMT_T));
								}

							}

						}

						resultMap.put("rcvCurr", ovsCurr);
						resultMap.put("rcvInt", INT_AMT_T);
						resultMap.put(
								"rcvIntTwd",
								branchRate.toOtherAmt(ovsCurr,
										UtilConstants.CURR.TWD, INT_AMT_T)
										.setScale(0, BigDecimal.ROUND_HALF_UP));

					}
				}

			} else {
				// 國內
				Map<String, Object> lnf252Map = misdbBASEService
						.findLnf252ByCntrNo(cntrNo);
				if (lnf252Map != null) {
					hasData = true;

					String LNF252_FACT_SWFT = Util.equals(
							MapUtils.getString(lnf252Map, "LNF252_FACT_SWFT"),
							"") ? UtilConstants.CURR.TWD : MapUtils.getString(
							lnf252Map, "LNF252_FACT_SWFT");

					String LNF252_REVOLVE = Util
							.equals(MapUtils.getString(lnf252Map,
									"LNF252_REVOLVE"), "") ? UtilConstants.CURR.TWD
							: MapUtils.getString(lnf252Map, "LNF252_REVOLVE");

					BigDecimal LNF252_FACT_AMT_NT = Util
							.equals(MapUtils.getString(lnf252Map,
									"LNF252_FACT_AMT_NT"), "") ? BigDecimal.ZERO
							: Util.parseBigDecimal(MapUtils.getString(
									lnf252Map, "LNF252_FACT_AMT_NT"));
					BigDecimal LNF252_LOAN_BAL_NT = Util
							.equals(MapUtils.getString(lnf252Map,
									"LNF252_LOAN_BAL_NT"), "") ? BigDecimal.ZERO
							: Util.parseBigDecimal(MapUtils.getString(
									lnf252Map, "LNF252_LOAN_BAL_NT"));

					BigDecimal LNF252_EF_FACT = Util
							.equals(MapUtils.getString(lnf252Map,
									"LNF252_EF_FACT"), "") ? BigDecimal.ZERO
							: Util.parseBigDecimal(MapUtils.getString(
									lnf252Map, "LNF252_EF_FACT"));
					BigDecimal LNF252_EF_FACT_NT = Util.equals(
							MapUtils.getString(lnf252Map, "LNF252_EF_FACT_NT"),
							"") ? BigDecimal.ZERO : Util
							.parseBigDecimal(MapUtils.getString(lnf252Map,
									"LNF252_EF_FACT_NT"));

					BigDecimal LNF252_RCV_INT_NT = Util.equals(
							MapUtils.getString(lnf252Map, "LNF252_RCV_INT_NT"),
							"") ? BigDecimal.ZERO : Util
							.parseBigDecimal(MapUtils.getString(lnf252Map,
									"LNF252_RCV_INT_NT"));

					resultMap.put("currentApplyCurr", LNF252_FACT_SWFT);
					resultMap.put("currentApplyAmt", LNF252_EF_FACT);
					resultMap.put("currentApplyAmtTwd", LNF252_EF_FACT_NT
							.setScale(0, BigDecimal.ROUND_HALF_UP));
					resultMap.put("blCurr", UtilConstants.CURR.TWD);
					resultMap.put("blAmt", LNF252_LOAN_BAL_NT);
					resultMap.put("blAmtTwd", LNF252_LOAN_BAL_NT.setScale(0,
							BigDecimal.ROUND_HALF_UP));
					resultMap.put("rcvCurr", UtilConstants.CURR.TWD);
					resultMap.put("rcvInt", LNF252_RCV_INT_NT);
					resultMap.put("rcvIntTwd", LNF252_RCV_INT_NT.setScale(0,
							BigDecimal.ROUND_HALF_UP));
					resultMap.put("reUse",
							Util.equals(LNF252_REVOLVE, "Y") ? "2" : "1"); // 1.不循環使用2.循環使用
				}
			}

		}

		if (!hasData) {
			List<Map<String, Object>> l140m01aList = eloandbBASEService
					.findL140m01aLastByCntrNo(cntrNo);
			if (l140m01aList != null && !l140m01aList.isEmpty()
					&& l140m01aList.size() > 0) {
				hasData = true;
				Map<String, Object> l140m01aMap = l140m01aList.get(0);
				String CURRENTAPPLYCURR = Util.nullToSpace(MapUtils.getString(
						l140m01aMap, "CURRENTAPPLYCURR"));
				CURRENTAPPLYCURR = Util.isEmpty(CURRENTAPPLYCURR) ? UtilConstants.CURR.TWD
						: CURRENTAPPLYCURR;
				resultMap.put("currentApplyCurr", CURRENTAPPLYCURR);
				BigDecimal CURRENTAPPLYAMT = Util.equals(
						MapUtils.getString(l140m01aMap, "CURRENTAPPLYAMT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(l140m01aMap,
								"CURRENTAPPLYAMT"));
				resultMap.put("currentApplyAmt", CURRENTAPPLYAMT);
				if (Util.notEquals(CURRENTAPPLYCURR, UtilConstants.CURR.TWD)) {
					resultMap.put(
							"currentApplyAmtTwd",
							branchRate.toTWDAmt(
									CURRENTAPPLYCURR,
									(CURRENTAPPLYAMT == null ? BigDecimal.ZERO
											: CURRENTAPPLYAMT)).setScale(0,
									BigDecimal.ROUND_HALF_UP));
				} else {
					resultMap.put("currentApplyAmtTwd", CURRENTAPPLYAMT);
				}
				String BLCURR = Util.nullToSpace(MapUtils.getString(
						l140m01aMap, "BLCURR"));
				BLCURR = Util.isEmpty(BLCURR) ? CURRENTAPPLYCURR : BLCURR;
				resultMap.put("blCurr", BLCURR);
				BigDecimal BLAMT = Util.equals(
						MapUtils.getString(l140m01aMap, "BLAMT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(l140m01aMap,
								"BLAMT"));
				resultMap.put("blAmt", BLAMT);
				if (Util.notEquals(BLCURR, UtilConstants.CURR.TWD)) {
					resultMap.put(
							"blAmtTwd",
							branchRate.toTWDAmt(BLCURR, BLAMT).setScale(0,
									BigDecimal.ROUND_HALF_UP));
				} else {
					resultMap.put("blAmtTwd", BLAMT);
				}

				// 應收利息
				String RCVCURR = Util.nullToSpace(MapUtils.getString(
						l140m01aMap, "RCVCURR"));
				RCVCURR = Util.isEmpty(RCVCURR) ? CURRENTAPPLYCURR : RCVCURR;
				resultMap.put("rcvCurr", RCVCURR);
				BigDecimal RCVINT = Util.equals(
						MapUtils.getString(l140m01aMap, "RCVINT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(l140m01aMap,
								"RCVINT"));
				resultMap.put("rcvInt", RCVINT);
				if (Util.notEquals(RCVCURR, UtilConstants.CURR.TWD)) {
					resultMap.put(
							"rcvIntTwd",
							branchRate.toTWDAmt(RCVCURR, RCVINT).setScale(0,
									BigDecimal.ROUND_HALF_UP));
				} else {
					resultMap.put("rcvIntTwd", RCVINT);
				}

				String reUse = Util.nullToSpace(Util.nullToSpace(MapUtils
						.getString(l140m01aMap, "REUSE")));
				resultMap.put("reUse", reUse); // 1.不循環使用2.循環使用

			}
		}

		resultMap.put("hasData", hasData ? "Y" : "N");

		return resultMap;
	}

	/**
	 * 
	 * @param mainId
	 */
	@Override
	public void reAllocate(String mainId) {
		Map<String, String> coCntrNoMap = new TreeMap<String, String>();

		// 初始化重新分配
		this.initialAllocate(mainId);

		List<L120S21A> listL120s21a = this.findL120s21aByMainId(mainId);
		for (L120S21A l120s21a : listL120s21a) {
			String cntrNoCo = Util.trim(l120s21a.getCntrNoCo_s21a());
			if (!coCntrNoMap.containsKey(cntrNoCo)) {
				coCntrNoMap.put(cntrNoCo, cntrNoCo);
			}
		}

		for (String coCntrNoKey : coCntrNoMap.keySet()) {
			List<L120S21A> l120s21aList = this.findL120s21aByMainIdAndCntrNoCo(
					mainId, coCntrNoKey);

			boolean allocateAgain = false;
			do {
				allocateAgain = false;
				BigDecimal factAmtCoTw = null;
				BigDecimal totalAmt = BigDecimal.ZERO;
				Map<String, String> cntrNoNeedAllocateMap = new HashMap<String, String>();

				// 1. 取得待分配共用限額
				for (L120S21A l120s21a : l120s21aList) {
					String cntrNo = Util.nullToSpace(l120s21a.getCntrNo_s21a());
					if (factAmtCoTw == null) {
						factAmtCoTw = l120s21a.getFactAmtCoTwd_s21a() == null ? BigDecimal.ZERO
								: l120s21a.getFactAmtCoTwd_s21a().setScale(0,
										BigDecimal.ROUND_HALF_UP);
					}

					boolean thisCntrNeedAllocate = true;

					// 先看這個額度序號有沒有已經確認分配EAD，若沒有則看該組共用內有沒有最終分配
					L120S21B l120s21b = this.findL120s21bByMainIdAndCntrNo(
							mainId, cntrNo);
					if (l120s21b == null || l120s21b.getCntrEad_s21b() == null) {
						// 沒有EAD
						factAmtCoTw = factAmtCoTw.subtract(l120s21a
								.getAllocate2() == null ? BigDecimal.ZERO
								: l120s21a.getAllocate2());
						if (l120s21a.getAllocate2() != null
								|| l120s21a.getAllocateF() != null) {
							thisCntrNeedAllocate = false;
						}
					} else {
						// L120S21B有EAD
						factAmtCoTw = factAmtCoTw.subtract(l120s21b
								.getCntrEad_s21b());
						l120s21a.setAllocate2(l120s21b.getCntrEad_s21b());
						l120s21a.setAllocateF(l120s21b.getCntrEad_s21b());
						thisCntrNeedAllocate = false;
					}

					// 要分配
					if (thisCntrNeedAllocate) {
						cntrNoNeedAllocateMap.put(cntrNo, cntrNo);
						totalAmt = totalAmt
								.add(l120s21a.getCurrentApplyAmtTwd_s21a() == null ? BigDecimal.ZERO
										: l120s21a
												.getCurrentApplyAmtTwd_s21a()
												.setScale(
														0,
														BigDecimal.ROUND_HALF_UP));
					}
				}

				// 2. 算佔比
				for (L120S21A l120s21a : l120s21aList) {
					String cntrNo = Util.nullToSpace(l120s21a.getCntrNo_s21a());
					if (cntrNoNeedAllocateMap.containsKey(cntrNo)) {
						BigDecimal currentApplyAmtTwd = l120s21a
								.getCurrentApplyAmtTwd_s21a() == null ? BigDecimal.ZERO
								: l120s21a.getCurrentApplyAmtTwd_s21a()
										.setScale(0, BigDecimal.ROUND_HALF_UP);
						BigDecimal ratio = totalAmt != null
								&& totalAmt.compareTo(BigDecimal.ZERO) > 0 ? (currentApplyAmtTwd)
								.multiply(new BigDecimal(100)).divide(totalAmt,
										16, BigDecimal.ROUND_HALF_UP)
								: BigDecimal.ZERO;

						// 用佔比X共用限額
						BigDecimal allocateTmp = factAmtCoTw.multiply(ratio)
								.divide(new BigDecimal(100), 1,
										BigDecimal.ROUND_HALF_UP);
						BigDecimal balTwd = l120s21a.getBlAmtTwd_s21a() == null ? BigDecimal.ZERO
								: l120s21a.getBlAmtTwd_s21a().setScale(0,
										BigDecimal.ROUND_HALF_UP);
						BigDecimal rcvIntTwd = l120s21a.getRcvIntTwd_s21a() == null ? BigDecimal.ZERO
								: l120s21a.getRcvIntTwd_s21a().setScale(0,
										BigDecimal.ROUND_HALF_UP);

						// 分配後額度如果小於餘額(餘額大於0的才要判斷)
						// 2022-01-19 研議e-Loan授信管理系統之LGD及額度暴險估算規則相關議題決議以額度為主
						// if (balTwd.compareTo(BigDecimal.ZERO) > 0
						// && allocateTmp.compareTo(balTwd.add(rcvIntTwd)) < 0)
						// {
						// l120s21a.setAllocate2(balTwd.add(rcvIntTwd));
						// allocateAgain = true;
						// }

						l120s21a.setRatio_s21a(ratio);
						l120s21a.setAllocate1(allocateTmp);
						this.save(l120s21a);
					}
				}

				// 不用再重分配了，代表沒有餘額大於額度的情形，就可以將剩下的 Allocate1 搬到 Allocate2
				if (!allocateAgain) {
					for (L120S21A l120s21a : l120s21aList) {
						if (l120s21a.getAllocate2() == null) {
							l120s21a.setAllocate2(l120s21a.getAllocate1());
							this.save(l120s21a);
						}
					}
				}
			} while (allocateAgain);
		}
	}

	/**
	 * 
	 * @param mainId
	 */
	@Override
	public void initialAllocate(String mainId) {
		// 初始化重新分配要清除資料
		List<L120S21A> listL120s21a = this.findL120s21aByMainId(mainId);
		// 清除共用額度暫存欄位
		for (L120S21A l120s21a : listL120s21a) {
			String cntrNo = Util.nullToSpace(l120s21a.getCntrNo_s21a());

			// 看該額度序號有沒已經已分配的EAD
			L120S21B l120s21b = this.findL120s21bByMainIdAndCntrNo(mainId,
					cntrNo);
			if (l120s21b != null) {
				BigDecimal cntrEad = l120s21b.getCntrEad_s21b();
				if (cntrEad != null) {
					// 已有EAD的，就塞值後就不會再參與分配
					l120s21a.setAllocate2(cntrEad);
					l120s21a.setAllocateF(cntrEad);
				} else {
					// 要重新參加分配
					l120s21a.setRatio_s21a(null);
					l120s21a.setAllocate1(null);
					l120s21a.setAllocate2(null);
					l120s21a.setAllocate3(null);
					l120s21a.setAllocateF(null);
				}
			} else {
				// 要重新參加分配
				l120s21a.setRatio_s21a(null);
				l120s21a.setAllocate1(null);
				l120s21a.setAllocate2(null);
				l120s21a.setAllocate3(null);
				l120s21a.setAllocateF(null);

			}

			this.save(l120s21a);
		}
	}

	@Override
	public String chkL120s21aData(L120S21A l120s21a) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSLgdCommomPage.class);
		StringBuffer temp = new StringBuffer();

		// 加減核**************************************************************

		// 統計未填欄位數
		int countItme = 1;

		if (Util.isEmpty(Util.trim(l120s21a.getReUse_s21a()))) {
			// L120S21A.reUse_s21a=循環註記
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S21A.reUse_s21a"));
		}

		if (temp.length() > 0) {
			// errMsg13=尚有必填欄位未填
			temp.insert(
					0,
					l120s21a.getCntrNoCo_s21a() + "("
							+ l120s21a.getCntrNo_s21a() + ")"
							+ pop.getProperty("errMsg13") + "<br/>");
		}

		return temp.toString();
	}

	@Override
	public String getFullCustIdByCntrNo(String cntrNo, String mainId) {
		String fullCustId = "";

		// 取得資料順序：LNF020 => 同一份簽報書 => 其他簽報書已核准 (不管性質別)
		boolean hasData = false;
		List<Map<String, Object>> lnf020List = misdbBASEService
				.findLnf020(cntrNo);
		if (lnf020List != null && !lnf020List.isEmpty()
				&& lnf020List.size() > 0) {
			hasData = true;
			fullCustId = lnf020List.get(0).get("LNF020_CUST_ID").toString();
		}

		if (!hasData) {
			if (mainId != null) {
				L140M01A l140m01a = lmsService
						.findL140M01AByL120m01cMainIdAndcntrNo(mainId, cntrNo,
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				if (l140m01a != null) {
					hasData = true;
					fullCustId = Util.addSpaceWithValue(
							Util.nullToSpace(l140m01a.getCustId()), 10)
							+ Util.nullToSpace(l140m01a.getDupNo());
				}
			}
		}

		if (!hasData) {
			List<Map<String, Object>> l140m01aList = eloandbBASEService
					.findL140m01aLastByCntrNo(cntrNo);
			if (l140m01aList != null && !l140m01aList.isEmpty()
					&& l140m01aList.size() > 0) {
				hasData = true;
				Map<String, Object> l140m01aMap = l140m01aList.get(0);
				String lCustId = Util.nullToSpace(MapUtils.getString(
						l140m01aMap, "CUSTID"));
				String lDupNo = Util.nullToSpace(MapUtils.getString(
						l140m01aMap, "DUPNO"));
				fullCustId = Util.addSpaceWithValue(Util.nullToSpace(lCustId),
						10) + Util.nullToSpace(lDupNo);
			}
		}

		return fullCustId;
	}

	@Override
	public String calcEADInner(String mainId)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuffer errorMsgBuf = new StringBuffer("");

		// step 1. 將 CntrEad_s21b set null
		this.initialL120s21b(mainId);

		// step 2. 用額度明細表的現請額度先更新額度共用內之額度序號資料
		this.reNewCntrDocCreditData(mainId);

		// step 3. 將TWD欄位補齊 依目前簽案行做計算幣別
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);

		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());
		this.calcTWD(mainId, branchRate);

		Map<String, String> coCntrNoMap = new TreeMap<String, String>(); // 共用序號Map
		Map<String, String> cntrNoIdMap = new HashMap<String, String>(); // 額度序號IdMap
		// step 4. 找出簽報書有設定共用之額度組別
		List<L120S21A> listL120s21a = this.findL120s21aByMainId(mainId);
		for (L120S21A l120s21a : listL120s21a) {
			String cntrNoCo = Util.nullToSpace(l120s21a.getCntrNoCo_s21a());
			if (!coCntrNoMap.containsKey(cntrNoCo)) {
				coCntrNoMap.put(cntrNoCo, cntrNoCo);
			}
			String cntrNo = Util.nullToSpace(l120s21a.getCntrNo_s21a());
			if (Util.isNotEmpty(cntrNo) && !cntrNoIdMap.containsKey(cntrNo)) {
				cntrNoIdMap.put(cntrNo,
						this.getFullCustIdByCntrNo(cntrNo, mainId));
			}
		}

		// J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		String LMS_LGD_EAD_VERSION_1 = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_EAD_VERSION_1"));
		String LMS_LGD_EAD_VERSION_2 = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_EAD_VERSION_2"));

		// 檢核資料是否完整
		StringBuffer allErrMsg = new StringBuffer("");
		for (L120S21A l120s21a : listL120s21a) {

			// J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
			l120s21a.setEadVer1(Util.isEmpty(LMS_LGD_EAD_VERSION_1) ? 0 : Util
					.parseInt(LMS_LGD_EAD_VERSION_1));
			l120s21a.setEadVer2(Util.isEmpty(LMS_LGD_EAD_VERSION_2) ? 0 : Util
					.parseInt(LMS_LGD_EAD_VERSION_2));

			String tErrMsg = this.chkL120s21aData(l120s21a);
			if (Util.notEquals(tErrMsg, "")) {
				allErrMsg.append((Util.isEmpty(tErrMsg) ? "" : "<BR>")).append(
						tErrMsg);
			}
			this.save(l120s21a);
		}
		if (Util.notEquals(allErrMsg.toString(), "")) {
			throw new CapMessageException(
					RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, allErrMsg.toString()), getClass());
		}

		boolean allocateMainAgain = false;
		// step 5. 分配
		do {
			allocateMainAgain = false;

			// step 5-1. 開始分配
			this.reAllocate(mainId);

			// step 5-2. 分配結束，判斷有沒有多組共用
			// 判斷同一額度序號有沒有出現在其他組
			Map<String, BigDecimal> cntrNoHasMultipleCnrNoCo = new LinkedHashMap<String, BigDecimal>();
			List<Object[]> metaList = this.findL120s21aMinAllocate(mainId);
			if (metaList != null && !metaList.isEmpty()) {
				for (Object[] meta : metaList) {
					String cntrNo = Util.trim(meta[0]);
					BigDecimal maxAllocate2 = Util.parseToBigDecimal(Util
							.trim(meta[1]));
					cntrNoHasMultipleCnrNoCo.put(cntrNo, maxAllocate2);
				}
			}

			// step 5-3. 同一額度序號都沒有出現在多組共用，直接用前次分配結果為額度EAD
			for (String coCntrNoKey : coCntrNoMap.keySet()) {
				boolean canFinish = true;
				List<L120S21A> l120s21aList = this
						.findL120s21aByMainIdAndCntrNoCo(mainId, coCntrNoKey);
				for (L120S21A l120s21a : l120s21aList) {
					String cntrNo = Util.nullToSpace(l120s21a.getCntrNo_s21a());
					if (cntrNoHasMultipleCnrNoCo.containsKey(cntrNo)) {
						// 該組共用額度的分配不能馬上結束，留到下一段處理
						canFinish = false;
						break;
					}
				}

				if (canFinish) {
					// 該組共用額度的分配沒有其他額度共用問題，所以可以結束
					for (L120S21A l120s21a : l120s21aList) {
						String cntrNo = Util.nullToSpace(l120s21a
								.getCntrNo_s21a());
						// 沒有EAD的，就可以直接用分配後金額來設定額度EAD
						BigDecimal allocate2 = l120s21a.getAllocate2();
						// 分配後額度若大於原始申請額度，則以原始申請額度為主
						allocate2 = allocate2.compareTo(l120s21a
								.getCurrentApplyAmtTwd_s21a()) > 0 ? l120s21a
								.getCurrentApplyAmtTwd_s21a() : allocate2;
						// 分配後小於0要變成0
						allocate2 = allocate2.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO
								: allocate2;

						l120s21a.setAllocate2(allocate2);

						// 沒有EAD的，就可以直接用分配後金額來設定額度EAD
						L120S21B l120s21b = this.findL120s21bByMainIdAndCntrNo(
								mainId, cntrNo);
						if (l120s21b != null) {
							if (l120s21b.getCntrEad_s21b() == null) {
								l120s21a.setAllocateF(allocate2);
								this.save(l120s21a);
								l120s21b.setCntrEad_s21b(allocate2);
							}
						} else {
							l120s21a.setAllocateF(allocate2);
							this.save(l120s21a);
							l120s21b = new L120S21B();
							l120s21b.setMainId(mainId);
							l120s21b.setCustId_s21b(null);
							l120s21b.setDupNo_s21b(null);
							if (cntrNoIdMap.containsKey(cntrNo)) {
								if (Util.notEquals(cntrNoIdMap.get(cntrNo), "")) {
									l120s21b.setCustId_s21b(Util.trim(Util
											.getLeftStr(
													cntrNoIdMap.get(cntrNo),
													cntrNoIdMap.get(cntrNo)
															.length() - 1)));
									l120s21b.setDupNo_s21b(Util.trim(Util
											.getRightStr(
													cntrNoIdMap.get(cntrNo), 1)));
								}
							}
							l120s21b.setCntrNo_s21b(cntrNo);
							l120s21b.setCntrEad_s21b(allocate2);
							l120s21b.setCreator(user.getUserId());
							l120s21b.setCreateTime(CapDate
									.getCurrentTimestamp());
						}

						this.save(l120s21a);
						this.save(l120s21b);
					}
				}
			}

			// step 5-4. 先檢查多組共用之各額度
			boolean gHasComplex = false; // 有沒有多額度共用且交錯共用之情形
			// 如果有多組但沒有交集的，則該組額度已經可以先確認EAD，不再重新分配
			for (String cntrKey : cntrNoHasMultipleCnrNoCo.keySet()) {
				Map<String, String> chkCnrNoCo = new HashMap<String, String>();
				List<L120S21A> l120s21aList = this
						.findL120s21aByMainIdAndCntrNo(mainId, cntrKey);
				for (L120S21A l120s21a : l120s21aList) {
					// 找出該額度序號有關的共用序號
					chkCnrNoCo.put(l120s21a.getCntrNoCo_s21a(), "");
				}

				boolean hasComplex = false;
				if (!chkCnrNoCo.isEmpty()) {
					// 檢查有關的共用序號中，有沒有其他有多筆共用的額度序號
					for (String chkCnrNoCoKey : chkCnrNoCo.keySet()) {
						List<L120S21A> chkL120s21a = this
								.findL120s21aByMainIdAndCntrNoCo(mainId,
										chkCnrNoCoKey);
						for (L120S21A tl120s21a : chkL120s21a) {
							String tCntrNo = Util.trim(tl120s21a
									.getCntrNo_s21a());
							if (Util.notEquals(tCntrNo, cntrKey)) {
								if (cntrNoHasMultipleCnrNoCo
										.containsKey(tCntrNo)) {
									hasComplex = true;
									gHasComplex = true;
									break;
								}
							}
						}

						if (hasComplex) {
							break;
						}
					}
				}

				// 沒有交錯共用，就可以直接決定EAD了
				if (!hasComplex) {
					// 分配後額度若大於原始申請額度，則以原始申請額度為主
					BigDecimal currentApplyAmt = BigDecimal.ZERO;

					List<L120S21A> xl120s21aList = this
							.findL120s21aByMainIdAndCntrNo(mainId, cntrKey);
					for (L120S21A xl120s21a : xl120s21aList) {
						// 取得其中一筆現請額度
						currentApplyAmt = xl120s21a
								.getCurrentApplyAmtTwd_s21a();
						break;
					}

					BigDecimal allocate2 = cntrNoHasMultipleCnrNoCo
							.get(cntrKey);
					allocate2 = allocate2.compareTo(currentApplyAmt) > 0 ? currentApplyAmt
							: allocate2;
					// 分配後小於0要變成0
					allocate2 = allocate2.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO
							: allocate2;
					// 沒有EAD的，就可以直接用分配後金額來設定額度EAD
					L120S21B l120s21b = this.findL120s21bByMainIdAndCntrNo(
							mainId, cntrKey);
					if (l120s21b != null) {
						if (l120s21b.getCntrEad_s21b() == null) {
							l120s21b.setCntrEad_s21b(allocate2);
						}
					} else {
						l120s21b = new L120S21B();
						l120s21b.setMainId(mainId);
						l120s21b.setCustId_s21b(null);
						l120s21b.setDupNo_s21b(null);
						if (cntrNoIdMap.containsKey(cntrKey)) {
							if (Util.notEquals(cntrNoIdMap.get(cntrKey), "")) {
								l120s21b.setCustId_s21b(Util.trim(Util
										.getLeftStr(cntrNoIdMap.get(cntrKey),
												cntrNoIdMap.get(cntrKey)
														.length() - 1)));
								l120s21b.setDupNo_s21b(Util.trim(Util
										.getRightStr(cntrNoIdMap.get(cntrKey),
												1)));
							}
						}
						l120s21b.setCntrNo_s21b(cntrKey);
						l120s21b.setCntrEad_s21b(allocate2);
						l120s21b.setCreator(user.getUserId());
						l120s21b.setCreateTime(CapDate.getCurrentTimestamp());
					}
					this.save(l120s21b);

					for (L120S21A yl120s21a : l120s21aList) {
						String cntrNo = Util.trim(yl120s21a.getCntrNo_s21a());
						if (Util.equals(cntrNo, cntrKey)) {
							// 沒有EAD的，就可以直接用分配後金額來設定額度EAD
							yl120s21a.setAllocateF(yl120s21a.getAllocate2());
							this.save(yl120s21a);
						}
					}
				}
			}

			// step 5-5. 有額度序號同時出現在多組共用裡面，且該類額度序號有兩筆以上(交錯共用)
			if (gHasComplex) {
				// 由大至小採用
				for (String cntrKey : cntrNoHasMultipleCnrNoCo.keySet()) {
					L120S21B l120s21b = this.findL120s21bByMainIdAndCntrNo(
							mainId, cntrKey);
					// 還沒有最後額度EAD的才從最大處理
					if (l120s21b == null || l120s21b.getCntrEad_s21b() == null) {
						String cntrNo = cntrKey;
						BigDecimal maxAllocate2 = Util
								.parseToBigDecimal(MapUtils.getString(
										cntrNoHasMultipleCnrNoCo, cntrKey));

						// 判斷這個最大的額度如果扣掉出現的各組限額後仍大於非該額度之餘額合計，則可以採用
						// 確保分配後的額度不會是負的
						boolean canUse = true;
						// 這個額度序號出現在那些組
						List<L120S21A> l120s21aList = this
								.findL120s21aByMainIdAndCntrNo(mainId, cntrKey);
						for (L120S21A l120s21a : l120s21aList) {
							String cntrNoCo = l120s21a.getCntrNoCo_s21a();
							BigDecimal factAmtCoTw = l120s21a
									.getFactAmtCoTwd_s21a(); // 限額
							BigDecimal coBalAmtSum = BigDecimal.ZERO;
							BigDecimal coAreadyEadSum = BigDecimal.ZERO;
							// 這些組下非該額度序號的餘額加總
							List<L120S21A> l120s21as = this
									.findL120s21aByMainIdAndCntrNoCo(mainId,
											cntrNoCo);
							for (L120S21A xl120s21a : l120s21as) {
								if (Util.notEquals(xl120s21a.getCntrNo_s21a(),
										cntrNo)) {
									// 是否為待分配
									L120S21B xl120s21b = this
											.findL120s21bByMainIdAndCntrNo(
													mainId,
													xl120s21a.getCntrNo_s21a());
									if (xl120s21b == null
											|| xl120s21b.getCntrEad_s21b() == null) {
										// 是待分配
										coBalAmtSum = coBalAmtSum
												.add(xl120s21a
														.getBlAmtTwd_s21a() == null ? BigDecimal.ZERO
														: xl120s21a
																.getBlAmtTwd_s21a()
																.setScale(
																		0,
																		BigDecimal.ROUND_HALF_UP))
												.add(xl120s21a
														.getRcvIntTwd_s21a() == null ? BigDecimal.ZERO
														: xl120s21a
																.getRcvIntTwd_s21a()
																.setScale(
																		0,
																		BigDecimal.ROUND_HALF_UP));
									} else {
										coAreadyEadSum = coAreadyEadSum
												.add(xl120s21b
														.getCntrEad_s21b());
									}
								}
							}

							// 判斷是否：
							// 共用限額 - 已確定分配EAD - 本次欲使用之最大分配額度 < 該組其他待分配額度之餘額加總
							if (factAmtCoTw.subtract(coAreadyEadSum)
									.subtract(maxAllocate2)
									.compareTo(coBalAmtSum) < 0) {
								// 如果小於，代表可能分配後造成其他額度分配是負的，則繼續使用下一組次大的

								// 2022/12/14 050分行發生一直LOOP的情形
								// 050 34459820 2022永康(兆)授字第00340號

								// 05010980011903 TWD 50,000,000.00
								// 05040980012301 USD 1,500,000.00
								// 05040980012401 USD 1,500,000.00
								//
								// 34459820 0 050109800119 0.00 N N N 0 0 0
								// 1.納入授權額度限額計算
								// 34459820 0 050110700141 0.00 N N N 0 0 0
								// 1.納入授權額度限額計算
								// SCZ00381040 050410700003 0.00 N Y N 0 0 0
								// 1.納入授權額度限額計算
								// SCZ00381040 050410700004 0.00 N Y N 0 0 0
								// 1.納入授權額度限額計算
								// VGZ00824510 050409800123 8,216,117.40 N Y N 0
								// 0 0
								// 9.不納入，限同借戶之額度共管等因素已由其他額度計入授權額度限額者(不同借戶間之額度共管，仍應分別納入各別歸戶授權額度限額計算，不應選擇本選項)
								// VGZ00824510 050410600003 0.00 N Y N 0 0 0
								// 1.納入授權額度限額計算
								// VGZ00824670 050409800124 8,216,117.40 N Y N 0
								// 0 0
								// 9.不納入，限同借戶之額度共管等因素已由其他額度計入授權額度限額者(不同借戶間之額度共管，仍應分別納入各別歸戶授權額度限額計算，不應選擇本選項)
								// VGZ00824670 050410600002 0.00 N Y N 0 0 0 1.

								// 拿掉下列的條件
								// canUse = false;
								// break;
							}
						}

						if (canUse) {
							// 分配後額度若大於原始申請額度，則以原始申請額度為主
							BigDecimal currentApplyAmt = BigDecimal.ZERO;

							/*
							 * List<L120S21A> l120s21aList = lms1201Service
							 * .findL120s21aByMainIdAndCntrNo(mainId, cntrKey);
							 */
							for (L120S21A xl120s21a : l120s21aList) {
								// 取得其中一筆現請額度
								currentApplyAmt = xl120s21a
										.getCurrentApplyAmtTwd_s21a();
								break;
							}

							BigDecimal allocate2 = maxAllocate2;
							allocate2 = allocate2.compareTo(currentApplyAmt) > 0 ? currentApplyAmt
									: allocate2;

							// 分配後小於0要變成0
							allocate2 = allocate2.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO
									: allocate2;

							for (L120S21A l120s21a : l120s21aList) {
								l120s21a.setAllocate2(allocate2);
								l120s21a.setAllocateF(allocate2);
								this.save(l120s21a);
							}

							// 設定額度EAD
							L120S21B yl120s21b = this
									.findL120s21bByMainIdAndCntrNo(mainId,
											cntrKey);
							if (yl120s21b != null) {
								yl120s21b.setCntrEad_s21b(allocate2);
							} else {
								yl120s21b = new L120S21B();
								yl120s21b.setMainId(mainId);
								yl120s21b.setCustId_s21b(null);
								yl120s21b.setDupNo_s21b(null);
								if (cntrNoIdMap.containsKey(cntrKey)) {
									if (Util.notEquals(
											cntrNoIdMap.get(cntrKey), "")) {
										yl120s21b
												.setCustId_s21b(Util.trim(Util.getLeftStr(
														cntrNoIdMap
																.get(cntrKey),
														cntrNoIdMap
																.get(cntrKey)
																.length() - 1)));
										yl120s21b.setDupNo_s21b(Util.trim(Util
												.getRightStr(cntrNoIdMap
														.get(cntrKey), 1)));
									}
								}
								yl120s21b.setCntrNo_s21b(cntrKey);
								yl120s21b.setCntrEad_s21b(allocate2);
								yl120s21b.setCreator(user.getUserId());
								yl120s21b.setCreateTime(CapDate
										.getCurrentTimestamp());
							}
							this.save(yl120s21b);

							break;
						}
					}
				}
			}

			// step 5-6. 初始化重新分配
			this.initialAllocate(mainId);

			// step 5-7. 檢查是不是共用都分配完了
			allocateMainAgain = false;
			List<L120S21A> chkListL120s21a = this.findL120s21aByMainId(mainId);
			// 清除共用額度暫存欄位
			for (L120S21A l120s21a : chkListL120s21a) {
				String cntrNo = Util.trim(l120s21a.getCntrNo_s21a());
				// 看該額度序號有沒已經已分配的EAD
				L120S21B l120s21b = this.findL120s21bByMainIdAndCntrNo(mainId,
						cntrNo);
				if (l120s21b == null || l120s21b.getCntrEad_s21b() == null) {
					allocateMainAgain = true; // 還有其他組，還要重新再分配一次
					break;
				}
			}
		} while (allocateMainAgain);

		// step 6. 補齊 L120S21B - 補齊有額度明細表且無共用
		this.furtherL120s21b(mainId);

		// step 7. 清查 S21B - 只留共用及額度明細表
		this.inventoryL120s21b(mainId);

		// step 8. 補齊 L120S21B 資訊
		this.syncL120s21bInfo(mainId, false);

		// step 9. 引進相關比率(EXCEL Lookups頁籤)
		this.importL120s21bLookupsRate(mainId);

		// step 10. 取得分配擔保品
		List<L120S21B> listL120s21b = this.findL120s21bByMainId(mainId);

		StringBuffer cmsErrorMsgBuf = new StringBuffer("");
		for (L120S21B l120s21b : listL120s21b) {
			if (Util.equals(Util.nullToSpace(l120s21b.getHasCntrDoc_s21b()),
					"Y")) {
				String errorMsg = this.getCollateralRecovery(l120s21b);
				if (Util.notEquals(errorMsg, "")) {
					if (Util.equals(errorMsgBuf.toString(), "")) {
						cmsErrorMsgBuf.append(errorMsg);
					} else {
						cmsErrorMsgBuf.append("<BR>").append(errorMsg);
					}
				}
			}
		}

		if (Util.notEquals(cmsErrorMsgBuf.toString(), "")) {
			errorMsgBuf.append("calcLGD「取得分配後擔保品回收」執行失敗：<BR>"
					+ cmsErrorMsgBuf.toString());
		}

		return errorMsgBuf.toString();
	}

	/**
	 * J-112-0210_05097_B1003 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21c
	 * @return
	 */
	@Override
	public void chkL120s21c(L120S21C l120s21c) throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSLgdCommomPage.class);

		StringBuffer temp = new StringBuffer();

		L120M01A l120m01a = l120m01aDao.findByMainId(l120s21c.getMainId());
		L120S21B l120s21b = this.findL120s21bByMainIdAndCntrNo(
				l120s21c.getMainId(), l120s21c.getCntrNo_s21c());

		// 統計未填欄位數
		int countItme = 1;

		String colKind_s21c = Util.trim(l120s21c.getColKind_s21c()); // L120S21C.colKind_s21c=擔保品種類
		if (Util.isEmpty(colKind_s21c)) {
			// L120S21C.colKind_s21c=擔保品種類
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S21C.colKind_s21c"));
		}

		String colCurr_s21c = Util.trim(l120s21c.getColCurr_s21c());
		if (Util.isEmpty(colCurr_s21c)) {
			// L120S21C.colCurr_s21c=擔保品幣別
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S21C.colCurr_s21c"));
		}

		BigDecimal colTimeValue_s21c = l120s21c.getColTimeValue_s21c();
		if (Util.isEmpty(colTimeValue_s21c)) {
			// L120S21C.colTimeValue_s21c=估值
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S21C.colTimeValue_s21c"));
		}

		if (Util.equals(Util.getLeftStr(colKind_s21c, 2), "05")) {
			BigDecimal cmsGrtrt_s21c = l120s21c.getCmsGrtrt_s21c();
			if (Util.isEmpty(cmsGrtrt_s21c)) {
				// L120S21C.cmsGrtrt_s21c=保證成數
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21C.cmsGrtrt_s21c"));
			}
		}

		String colCoUseFlag_s21c = Util.trim(l120s21c.getColCoUseFlag_s21c());
		if (Util.isEmpty(colCoUseFlag_s21c)) {
			// L120S21C.colCoUseFlag_s21c=是否與其他額度共用
			countItme = this.setHtmlBr(temp, countItme,
					pop.getProperty("L120S21C.colCoUseFlag_s21c"));
		}

		if (Util.equals(colCoUseFlag_s21c, "Y")) {
			BigDecimal colShareRate_s21c = l120s21c.getColShareRate_s21c();
			if (Util.isEmpty(colShareRate_s21c)) {
				// L120S21C.colShareRate_s21c=分配比率
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21C.colShareRate_s21c"));
			} else if (colShareRate_s21c.compareTo(new BigDecimal(100)) > 0) {
				// errMsg09=「{0}」欄位資料內容有誤！！
				// L120S21C.colShareRate_s21c=分配比率
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21C.colShareRate_s21c"));
			}

		}

		if (!this.hideRgstInfoForL120s21c(colKind_s21c)) {
			BigDecimal colRgstAmt_s21c = l120s21c.getColRgstAmt_s21c();
			if (Util.isEmpty(colRgstAmt_s21c)) {
				// L120S21C.colRgstAmt_s21c=擔保品設定金額
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21C.colRgstAmt_s21c"));
			}

			BigDecimal colPreRgstAmt_s21c = l120s21c.getColPreRgstAmt_s21c();
			if (Util.isEmpty(colPreRgstAmt_s21c)) {
				// L120S21C.colPreRgstAmt_s21c=前順位設定金額
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21C.colPreRgstAmt_s21c"));
			}
		}

		// 發行公司是否為借款人之同一關係企業
		String LGD_L120S21C_ISELCRECOM_SHOW = Util.trim(lmsService
				.getSysParamDataValue("LGD_L120S21C_ISELCRECOM_SHOW"));

		boolean needChkIsElcreCom = false;
		if (Util.isNotEmpty(LGD_L120S21C_ISELCRECOM_SHOW)) {
			if (Util.notEquals(LGD_L120S21C_ISELCRECOM_SHOW, "")) {
				String[] forType2 = LGD_L120S21C_ISELCRECOM_SHOW.split(",");
				List<String> asList2 = Arrays.asList(forType2);
				if (asList2.contains(colKind_s21c)) {
					needChkIsElcreCom = true;
				}
			}
		}

		String isElcreCom_s21c = Util.trim(l120s21c.getIsElcreCom_s21c());
		if (needChkIsElcreCom) {
			if (Util.isEmpty(isElcreCom_s21c)) {
				// /L120S21C.isElcreCom_s21c=發行公司是否為借款人之同一關係企業
				countItme = this.setHtmlBr(temp, countItme,
						pop.getProperty("L120S21C.isElcreCom_s21c"));
			}
		}

		// J-112-0210_05097_B1005 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
		if (l120s21b != null) {
			if (Util.equals(Util.trim(l120s21b.getUnionFlag()), "Y")) {
				if (Util.isEmpty(l120s21c.getUnionFlag_s21c())) {
					// L120S21C.unionFlag_s21c=擔保品是否為多筆聯貸案額度共用
					countItme = this.setHtmlBr(temp, countItme,
							pop.getProperty("L120S21C.unionFlag_s21c"));
				}

				if (Util.equals(Util.trim(l120s21c.getUnionFlag_s21c()), "Y")) {
					if (Util.isEmpty(l120s21c.getUnionCurr_s21c())) {
						// L120S21C.unionCurr_s21c=聯貸幣別
						countItme = this.setHtmlBr(temp, countItme,
								pop.getProperty("L120S21C.unionCurr_s21c"));
					}

					if (Util.isEmpty(l120s21c.getSyndAmt_s21c())) {
						// L120S21C.syndAmt_s21c=本行參貸額度合計
						countItme = this.setHtmlBr(temp, countItme,
								pop.getProperty("L120S21C.syndAmt_s21c"));
					}

					if (Util.isEmpty(l120s21c.getUnionAmt_s21c())) {
						// L120S21C.unionAmt_s21c=聯合授信案總金額合計
						countItme = this.setHtmlBr(temp, countItme,
								pop.getProperty("L120S21C.unionAmt_s21c"));
					}
				}
			}
		}

		if (temp.length() > 0) {
			// errMsg13=尚有必填欄位未填
			temp.insert(0, pop.getProperty("errMsg13") + "<br/>");
			throw new CapMessageException(
					RespMsgHelper.getMessage(
							UtilConstants.AJAX_RSP_MSG.執行有誤,
							"「" + l120s21c.getCntrNo_s21c() + "」"
									+ temp.toString()), getClass());
		}

		// 檢核 *****************************************************************
		// J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
		if (needChkIsElcreCom) {
			if (Util.equals(Util.trim(isElcreCom_s21c), "Y")) {
				// lgd.errMsg19=股票、公司債、可轉換公司債擔保品為借款人之「同一關係企業」者，於計算e-Loan
				// LGD時不列入擔保品回收，不需自建擔保品。
				throw new CapMessageException(pop.getProperty("lgd.errMsg19"),
						getClass());
			}
		}

		return;

	}

	/**
	 * J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 * 
	 * 回傳額度明細表顯示建議授權
	 * 
	 * @param l120m01a
	 * @param l140m01a
	 * @param itemType
	 * @return
	 */
	@Override
	public String getLgdSuggestMsg(L120M01A l120m01a, L140M01A l140m01a,
			String itemType) {
		StringBuffer suggestStr = new StringBuffer("");
		// suggestStr.append("建議授權層級:營運中心(授信)、副總經理(出口押匯)");
		// suggestStr.append("<br>建議合併授權層級:總經理(授信)");
		Map<String, String> suggestMap = lmsService
				.findL120m01lCaseLvlByCustId(l120m01a.getMainId(),
						l140m01a.getCustId(), l140m01a.getDupNo());
		if (suggestMap.get("custCaseLvl") != null) {
			suggestStr.append(Util.trim(suggestMap.get("custCaseLvl")));
		}

		return suggestStr.toString();
	}

	/**
	 * 借款人LGD計算範圍請排除
	 * 
	 * J-112-0278_05097_B1001 Web
	 * e-Loan企金授信借款人LGD計算範圍排除進出口額度，並於試算頁簽內加註：借款人違約損失率不含進出口額度。
	 * 
	 * 簽案LGD試算頁籤的「借款人LGD」係作為授信利率合理性分析之用。考量進出口額度相關利費率已另有進出口作業規範，
	 * 故借款人LGD計算範圍請排除進出口額度。並於試算頁簽內加註：借款人違約損失率不含進出口額度。
	 * 
	 * @param l140m01a
	 * @return
	 */
	@Override
	public boolean isCustLgdNeed(L140M01A l140m01a) {

		// [昨天 下午 06:31] 黃建霖(資訊處,高級專員)
		//
		// 941 進口押匯 、 944 應收信用狀款項－即期 、 942 出口押匯會排除
		//
		// [昨天 下午 06:58] 連喬凱(授信審查處,專員)
		//
		// 還有9411跟9441
		//
		// [上午 08:57] 連喬凱(授信審查處,專員)
		//
		// 試算頁籤內加註：借款人違約損失率不含進出口額度及買入光票。
		// [上午 09:07] 連喬凱(授信審查處,專員)
		//
		// 如果有出現超出以上所列範圍的科目
		//
		// [上午 09:07] 連喬凱(授信審查處,專員)
		//
		// 就照算

		boolean isNeed = true;

		if (l140m01a == null) {
			isNeed = false;
			return isNeed;
		}

		L120M01A l120m01a = null;
		L120M01C l120m01c = l140m01a.getL120m01c();
		if (l120m01c != null) {
			l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());

		}
		if (l120m01a == null) {
			isNeed = false;
			return isNeed;
		}

		// 額外排除科目
		String LMS_LGD_CUSTLGD_EX_SUBJECT = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_CUSTLGD_EX_SUBJECT"));
		String[] exSubjectArr = null;
		if (Util.notEquals(LMS_LGD_CUSTLGD_EX_SUBJECT, "")) {
			exSubjectArr = LMS_LGD_CUSTLGD_EX_SUBJECT.split(",");
		}

		// 判斷額度明細表是否需要LGD***********************************************************************
		boolean needLgd = this.isL140m01aNeedLgd(l140m01a, "2");
		if (!needLgd) {
			isNeed = false;
			return isNeed;
		}

		// 判斷LGD頁籤該筆額度是否有顯示LGD************************************************************************
		L120S21B l120s21b = this.findL120s21bByMainIdAndCntrNo(
				l120m01a.getMainId(), l140m01a.getCntrNo());

		if (l120s21b != null) {
			if (Util.notEquals(Util.trim(l120s21b.getHasCntrDoc_s21b()), "Y")) {
				isNeed = false;
				return isNeed;
			}
		}

		// ***********************************************************************

		String item = "";

		List<String> exSubjectList = exSubjectArr == null ? null : Arrays
				.asList(exSubjectArr);

		Set<L140M01C> l140m01cs = l140m01a.getL140m01c();
		if (l140m01cs != null) {

			// 整理科目**********************************************************
			boolean hasNeedSubject = false;
			boolean hasNotNeedSubject = false;
			for (L140M01C l140m01c : l140m01cs) {
				item = Util.truncateString(l140m01c.getLoanTP(), 4);
				if (exSubjectList != null) {
					if (exSubjectList.contains(item)) {
						hasNotNeedSubject = true;
					} else {
						hasNeedSubject = true;
					}
				}

			}

			// 所以科目都沒有包含其他非排除的科目
			if (!hasNeedSubject && hasNotNeedSubject) {
				isNeed = false;
			}

		}

		return isNeed;

	}

	@Override
	public Map<String, String> getCustBestElf338nGrade(String custId,
			String dupNo) {

		HashMap<String, String> returnMap = null;

		List<Map<String, Object>> elf338nList = misLMS338NService
				.findCrdtypeByCustIdForLgdGuarantor(custId, dupNo);
		if (elf338nList != null && !elf338nList.isEmpty()) {

			returnMap = new HashMap<String, String>();

			List<Map<String, String>> depData = new ArrayList<Map<String, String>>();
			Map<String, String> tMap1 = new LinkedHashMap<String, String>();

			for (Map<String, Object> elf338nMap : elf338nList) {
				tMap1 = new LinkedHashMap<String, String>();

				tMap1.put("CRDTYEAR", Util.trim(MapUtils.getString(elf338nMap,
						"ELF338N_CRDTYEAR")));
				tMap1.put("CRDTBR", Util.trim(MapUtils.getString(elf338nMap,
						"ELF338N_CRDTBR")));

				String crdType = Util.trim(MapUtils.getString(elf338nMap,
						"ELF338N_CRDTYPE"));
				String grade = Util.trim(MapUtils.getString(elf338nMap,
						"ELF338N_GRADE"));

				tMap1.put("CRDTYPE", crdType);
				tMap1.put("GRADE", grade);
				tMap1.put("FINYEAR", Util.trim(MapUtils.getString(elf338nMap,
						"ELF338N_FINYEAR")));

				String covertGrade = "";
				Map<String, String> crdTypeScoreMap = null;
				if (Casedoc.CrdType.MOODY.equals(crdType)) {
					crdTypeScoreMap = codeTypeService
							.findByCodeType("ELRPS04D06_mdALn");
				} else if (Casedoc.CrdType.SAndP.equals(crdType)) {
					crdTypeScoreMap = codeTypeService
							.findByCodeType("ELRPS04D06_purALn");
				} else if (Casedoc.CrdType.Fitch.equals(crdType)) {
					crdTypeScoreMap = codeTypeService
							.findByCodeType("ELRPS04D06_fhALn");
				}

				covertGrade = Util.trim(MapUtils.getString(crdTypeScoreMap,
						grade, "999"));

				tMap1.put("NEWGRADE", covertGrade);

				depData.add(tMap1);

			}

			// 排序-分數越小越好
			Collections.sort(depData, new Comparator<Map<String, String>>() {
				public int compare(Map<String, String> o1,
						Map<String, String> o2) {
					return CapMath.compare(
							Util.parseBigDecimal(o1.get("NEWGRADE")),
							Util.parseBigDecimal(o2.get("NEWGRADE")));
				}
			});

			for (Map<String, String> xMap : depData) {
				returnMap.putAll(xMap);
				break;
			}
		}

		return returnMap;
	}

}
