/* 
 * C900M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金產品名稱檔（LN.LNF110） **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C900M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "prodKind" }))
public class C900M01A extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件與產品種類對應表(C900M01B)
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "c900m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<C900M01B> c900m01b;

	public Set<C900M01B> getC900M01B() {
		return c900m01b;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	/**
	 * 產品代號
	 * <p/>
	 * 02
	 */
	@Size(max = 2)
	@Column(name = "PRODKIND", length = 2, columnDefinition = "VARCHAR(2)", nullable = false)
	private String prodKind;

	/**
	 * 產品名稱1
	 * <p/>
	 * 行家理財貸款
	 */
	@Size(max = 60)
	@Column(name = "PRODNM1", length = 60, columnDefinition = "VARCHAR(60)")
	private String prodNm1;

	/**
	 * 產品名稱2
	 * <p/>
	 * A短期
	 */
	@Size(max = 60)
	@Column(name = "PRODNM2", length = 60, columnDefinition = "VARCHAR(60)")
	private String prodNm2;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;
	
	/** 房貸產品排序 **/
	@Column(name="HOUSELOANORDER", columnDefinition="INTEGER" )
	private Integer houseLoanOrder;
	
	/** 信貸產品排序 **/
	@Column(name="CREDITLOANORDER", columnDefinition="INTEGER" )
	private Integer creditLoanOrder;
	
	/** 其他產品排序 **/
	@Column(name="OTHERLOANORDER", columnDefinition="INTEGER" )
	private Integer otherLoanOrder;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得產品代號
	 * <p/>
	 * 02
	 */
	public String getProdKind() {
		return this.prodKind;
	}

	/**
	 * 設定產品代號
	 * <p/>
	 * 02
	 **/
	public void setProdKind(String value) {
		this.prodKind = value;
	}

	/**
	 * 取得產品名稱1
	 * <p/>
	 * 行家理財貸款
	 */
	public String getProdNm1() {
		return this.prodNm1;
	}

	/**
	 * 設定產品名稱1
	 * <p/>
	 * 行家理財貸款
	 **/
	public void setProdNm1(String value) {
		this.prodNm1 = value;
	}

	/**
	 * 取得產品名稱2
	 * <p/>
	 * A短期
	 */
	public String getProdNm2() {
		return this.prodNm2;
	}

	/**
	 * 設定產品名稱2
	 * <p/>
	 * A短期
	 **/
	public void setProdNm2(String value) {
		this.prodNm2 = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得房貸產品排序 **/
	public Integer getHouseLoanOrder() {
		return houseLoanOrder;
	}

	/** 設定房貸產品排序 **/
	public void setHouseLoanOrder(Integer houseLoanOrder) {
		this.houseLoanOrder = houseLoanOrder;
	}

	/** 取得信貸產品排序 **/
	public Integer getCreditLoanOrder() {
		return creditLoanOrder;
	}

	/** 設定信貸產品排序 **/
	public void setCreditLoanOrder(Integer creditLoanOrder) {
		this.creditLoanOrder = creditLoanOrder;
	}

	/** 取得其他產品排序 **/
	public Integer getOtherLoanOrder() {
		return otherLoanOrder;
	}

	/** 設定其他產品排序 **/
	public void setOtherLoanOrder(Integer otherLoanOrder) {
		this.otherLoanOrder = otherLoanOrder;
	}
}
