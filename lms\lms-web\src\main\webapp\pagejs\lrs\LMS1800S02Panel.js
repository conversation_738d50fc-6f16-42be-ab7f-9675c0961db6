initDfd.done(function(json){
	if(json.page=="02"){
		build_attch(json);
	}
	//排序條件參考VLMS18115
	var $gridview = $("#gridview").iGrid({
        handler: 'lms1800gridhandler',
        height: 480,        
        multiselect: true,
        needPager: false,        
        shrinkToFit: false,
		altRows:false,
        postData: {
            formAction: "queryList",
            type: "Y"    //要覆審
        },
		loadComplete: function(data) {
            var allRows = $('#gridview').jqGrid('getDataIDs');
			for (var i = 0; i < allRows.length; i++) {
			   var rowid = allRows[i];
			   var ctlType = $('#gridview').jqGrid('getCell', rowid, 'ctlType');
			   var unitCtlType = $('#gridview').jqGrid('getCell', rowid, 'unitCtlType');
			   
			   if (ctlType == "B" && unitCtlType == "Z") {
				   	   $(this).jqGrid('setRowData', rowid, false,{'background':'#e3aeda'});  
			   }
			   
			   //J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			   //https://www.google.com/search?client=firefox-b&q=%23e3aeda
			   if (ctlType == "C" && unitCtlType == "Z") {
			   	   $(this).jqGrid('setRowData', rowid, false,{'background':'#6cd1c2'});  
		       }
				
//			   $(this).jqGrid('setRowData', rowid, false,{'background':'#e3aeda'});  
//			   
//			   
//			   $(this).jqGrid('setRowData', rowid, false,{'font-color':'red'});  
			   
	           
//					if (ctlType == "B") {
//						 $(this).jqGrid('setRowData', rowid, false,{'background-color':'#e3aeda'});  
//					}
//	              
			}
			
			
			
			//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		    //確認新增覆審名單種類
			$.ajax({
	            type: "POST",
	            handler: "lms1800formhandler",
	            data: {	 'formAction': 'getCtlTypeByBrNo'
	            },
	            success: function(responseData){
					 var ctlType =responseData.ctlType;
					 if(ctlType =="B"){
					 	//只有自辦
					 	$("#gridview").hideCol('realCkFg');
						$("#gridview").hideCol('realDt');
						$("#gridview").hideCol('elfMDFlag');
						$("#gridview").hideCol('elfDBUOBU');
					 }
	            }
	        });  
			
			
			
		},
        colModel: [{
            // "序號",
            colHeader: i18n.lms1800m01['grid.projectSeq'],name: 'projectSeq',width:30,align:"center",sortable: true
        }, {
            // "需覆審",
            colHeader: i18n.lms1800m01['grid.docStatus1'],name: 'showDocStatus1',width:45,align:"center",sortable: false,
            formatter: function(cellvalue, options, rowObject){
                if (cellvalue == "1") {
                    return "<img src='webroot/img/lms/V.png'>";
                }else if (cellvalue == "2") {
                    return "<img src='webroot/img/lms/V2.png'>";
                }else if (cellvalue == "3") {
                	return "<img src='webroot/img/lms/X.png'>";
                }else{
                	return cellvalue;
                }
            } 
        }, {
            // "上次覆審日",
            colHeader: i18n.lms1800m01['grid.newLRDate'],name:'showLRDate',align:"center",width: 70,sortable: false
		}, {
            //"覆審種類"
			//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
            colHeader: i18n.lms1800m01['L180M01B.ctlType'], name: 'ctlType', align: "center", width: 50	
		}, {
            //"實地覆審"
            colHeader: i18n.lms1800m01['L180M01B.realCkFg'], name: 'realCkFg', align: "center", width: 30		
		}, {
            //"最近實地覆審日"
            colHeader: i18n.lms1800m01['L180M01B.realDt'], name: 'realDt', align: "center", width: 70			
        }, {
            // "借款人統編",
            colHeader: i18n.lms1800m01['grid.custId'],name: 'custId',align: "left",width: 85,sortable: true,formatter: 'click',onclick: openDoc
        }, {
            // "借款人姓名",
            colHeader: i18n.lms1800m01['L180M01B.elfCName'],name: 'elfCName',align: "left",width: 130
        }, {
            // "主要戶",
            colHeader: i18n.lms1800m01['grid.elfMainCust'],name: 'elfMainCust',align: "center",width: 40,
            formatter: function(value){
                return value == "Y" ? "<img src='webroot/img/lms/M.png'>" : "";
            }
        }, {
            // "異常戶",
            colHeader: i18n.lms1800m01['grid.elfMDFlag'],name: 'elfMDFlag',align: "center",width: 40,
            formatter: function(value){
            	//可能是 '  '
                return $.trim(value) == "" ? value:"<img src='webroot/img/lms/E.png'>";
            }
        }, {
            // "共用",
            colHeader: i18n.lms1800m01['grid.elfDBUOBU'],name: 'elfDBUOBU',align: "center",width: 35,
            formatter: function(value){
                return value == "Y" ? "<img src='webroot/img/lms/I.png'>" : "";
            }
        }, {
            // "週期",
            colHeader: i18n.lms1800m01['grid.elfRCkdLine'],name: 'elfRCkdLine',align:"center",width:35
        }, {
            // "新作/增額",
            colHeader: i18n.lms1800m01['grid.elfNewAdd'],name: 'elfNewAddDesc', align: "left", width: 40,sortable: false
        }, {
        	//新增年月
            colHeader: i18n.lms1800m01['grid.elfNewDate'],name: 'elfNewDate', align: "left", width: 55
        }, {
            //新作紓困註記
            colHeader: i18n.lms1800m01['grid.elfNewRescue'],name: 'elfNewRescueDesc', align: "left", width: 40,sortable: false
        }, {
            //新作紓困年月
            colHeader: i18n.lms1800m01['grid.elfNewRescueYM'],name: 'elfNewRescueYM', align: "left", width: 55
        }, {
            //純小規模
            colHeader: i18n.lms1800m01['grid.isSmallBuss'],name: 'smallBussDesc', align: "left", width: 40
        }, {
            //小規模評分
            colHeader: i18n.lms1800m01['grid.sbScore'],name: 'sbScore', align: "left", width: 55
        }, {
            // J-110-0272 抽樣覆審
            colHeader: i18n.lms1800m01['grid.elfRandomType'],name: 'elfRandomType', align: "left", width: 40
        }, {
            // "不覆審",
            colHeader: i18n.lms1800m01['grid.newNCkdFlag'],name: 'newNCkdFlag',align: "center", width: 40
        }, {
            //文件狀態
            colHeader: " ", name: 'createBY', align: "left", width: 30,
            formatter: function(value){
                return value == "PEO" ? "新增" : "";
            }
        }, {
            //"分行覆審報告表新增註記"
            colHeader: i18n.lms1800m01['L180M01B.newBy170M01'], name: 'newBy170M01', align: "left", width: 70
        }
		, { colHeader: "unitCtlType",name: 'unitCtlType', hidden: true  }
        , { name: 'oid', hidden: true }
        , { name: 'mainId', hidden: true }
        , { name: 'coMainId', hidden: true }
		
        ]
    });

    // J-110-0396 配合授審處，E-Loan企金授信覆審系統修改每月需覆審名單報表，優化由系統排除免覆審名單等事項。
    var $gridNReview = $("#gridNReview").iGrid({
        handler: 'lms1800gridhandler',
        height: 480,
        multiselect: true,
        needPager: false,
        shrinkToFit: false,
        altRows:false,
        postData: {
            formAction: "queryList",
            type: "N"    //不覆審
        },
        loadComplete: function(data) {
            var allRows = $('#gridview').jqGrid('getDataIDs');
            for (var i = 0; i < allRows.length; i++) {
               var rowid = allRows[i];
               var ctlType = $('#gridview').jqGrid('getCell', rowid, 'ctlType');
               var unitCtlType = $('#gridview').jqGrid('getCell', rowid, 'unitCtlType');

               if (ctlType == "B" && unitCtlType == "Z") {
                       $(this).jqGrid('setRowData', rowid, false,{'background':'#e3aeda'});
               }

               if (ctlType == "C" && unitCtlType == "Z") {
                   $(this).jqGrid('setRowData', rowid, false,{'background':'#6cd1c2'});
               }
            }

            //確認新增覆審名單種類
            $.ajax({
                type: "POST",
                handler: "lms1800formhandler",
                data: {	 'formAction': 'getCtlTypeByBrNo'
                },
                success: function(responseData){
                     var ctlType =responseData.ctlType;
                     if(ctlType =="B"){
                        //只有自辦
                        $("#gridview").hideCol('realCkFg');
                        $("#gridview").hideCol('realDt');
                        $("#gridview").hideCol('elfMDFlag');
                        $("#gridview").hideCol('elfDBUOBU');
                     }
                }
            });
        },
        colModel: [{
            // "序號",
            colHeader: i18n.lms1800m01['grid.projectSeq'],name: 'projectSeq',width:30,align:"center",sortable: true
        }, {
            // "需覆審",
            colHeader: i18n.lms1800m01['grid.docStatus1'],name: 'showDocStatus1',width:45,align:"center",sortable: false,
            formatter: function(cellvalue, options, rowObject){
                if (cellvalue == "1") {
                    return "<img src='webroot/img/lms/V.png'>";
                }else if (cellvalue == "2") {
                    return "<img src='webroot/img/lms/V2.png'>";
                }else if (cellvalue == "3") {
                    return "<img src='webroot/img/lms/X.png'>";
                }else{
                    return cellvalue;
                }
            }
        }, {
            // "上次覆審日",
            colHeader: i18n.lms1800m01['grid.newLRDate'],name:'showLRDate',align:"center",width: 70,sortable: false
        }, {
            //"覆審種類"
            //J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
            colHeader: i18n.lms1800m01['L180M01B.ctlType'], name: 'ctlType', align: "center", width: 50
        }, {
            //"實地覆審"
            colHeader: i18n.lms1800m01['L180M01B.realCkFg'], name: 'realCkFg', align: "center", width: 30
        }, {
            //"最近實地覆審日"
            colHeader: i18n.lms1800m01['L180M01B.realDt'], name: 'realDt', align: "center", width: 70
        }, {
            // "借款人統編",
            colHeader: i18n.lms1800m01['grid.custId'],name: 'custId',align: "left",width: 85,sortable: true,formatter: 'click',onclick: openDoc
        }, {
            // "借款人姓名",
            colHeader: i18n.lms1800m01['L180M01B.elfCName'],name: 'elfCName',align: "left",width: 130
        }, {
            // "主要戶",
            colHeader: i18n.lms1800m01['grid.elfMainCust'],name: 'elfMainCust',align: "center",width: 40,
            formatter: function(value){
                return value == "Y" ? "<img src='webroot/img/lms/M.png'>" : "";
            }
        }, {
            // "異常戶",
            colHeader: i18n.lms1800m01['grid.elfMDFlag'],name: 'elfMDFlag',align: "center",width: 40,
            formatter: function(value){
                //可能是 '  '
                return $.trim(value) == "" ? value:"<img src='webroot/img/lms/E.png'>";
            }
        }, {
            // "共用",
            colHeader: i18n.lms1800m01['grid.elfDBUOBU'],name: 'elfDBUOBU',align: "center",width: 35,
            formatter: function(value){
                return value == "Y" ? "<img src='webroot/img/lms/I.png'>" : "";
            }
        }, {
            // "週期",
            colHeader: i18n.lms1800m01['grid.elfRCkdLine'],name: 'elfRCkdLine',align:"center",width:35
        }, {
            // "新作/增額",
            colHeader: i18n.lms1800m01['grid.elfNewAdd'],name: 'elfNewAddDesc', align: "left", width: 40,sortable: false
        }, {
            //新增年月
            colHeader: i18n.lms1800m01['grid.elfNewDate'],name: 'elfNewDate', align: "left", width: 55
        }, {
            //新作紓困註記
            colHeader: i18n.lms1800m01['grid.elfNewRescue'],name: 'elfNewRescueDesc', align: "left", width: 40,sortable: false
        }, {
            //新作紓困年月
            colHeader: i18n.lms1800m01['grid.elfNewRescueYM'],name: 'elfNewRescueYM', align: "left", width: 55
        }, {
            //純小規模
            colHeader: i18n.lms1800m01['grid.isSmallBuss'],name: 'smallBussDesc', align: "left", width: 40
        }, {
            //小規模評分
            colHeader: i18n.lms1800m01['grid.sbScore'],name: 'sbScore', align: "left", width: 55
        }, {
            // J-110-0272 抽樣覆審
            colHeader: i18n.lms1800m01['grid.elfRandomType'],name: 'elfRandomType', align: "left", width: 40
        }, {
            // "不覆審",
            colHeader: i18n.lms1800m01['grid.newNCkdFlag'],name: 'newNCkdFlag',align: "center", width: 40
        }, {
            //文件狀態
            colHeader: " ", name: 'createBY', align: "left", width: 30,
            formatter: function(value){
                return value == "PEO" ? "新增" : "";
            }
        }, {
            //"分行覆審報告表新增註記"
            colHeader: i18n.lms1800m01['L180M01B.newBy170M01'], name: 'newBy170M01', align: "left", width: 70
        }
        , { colHeader: "unitCtlType",name: 'unitCtlType', hidden: true  }
        , { name: 'oid', hidden: true }
        , { name: 'mainId', hidden: true }
        , { name: 'coMainId', hidden: true }
        ]
    });
    
	var $gridNckdFlagO = $("#gridNckdFlagO").iGrid({
        handler: 'lms1800gridhandler',        
        height: 120,
        postData: {
        	mainOid: $("#mainOid").val(),
            formAction: "queryNckdFlagO"
        },
        sortname: "dataDate",
        sortorder: "desc",
        needPager: false,        
        shrinkToFit: false,       
        colModel: [
          {//分行名稱
        	  colHeader: i18n.abstracteloan['doc.branchName'], name: 'show_branchId', width: 150, sortable: false, align: "left" 
          },{//資料年月
            colHeader: i18n.lms1800m01['L180M01A.dataDate'], name: 'dataDate', width: 120, sortable: true, align: "left"          
          }, {//預計覆審日
		    colHeader: i18n.lms1800m01['L180M01A.defaultCTLDate'], name: 'defaultCTLDate', width: 120, sortable: true, align: "left"
         }
        , { name: 'branchId', hidden: true }  
        , { name: 'oid', hidden: true }
        , { name: 'mainId', hidden: true }
        , { name: 'dataSrc', hidden: true }
        ]
    });

    var $gridSmallBuss = $("#smallBussGridview").iGrid({
        handler: 'lms1800gridhandler',
        height: 150,
        postData: {
            formAction: "queryList",
            type: "S"    //smallBuss
        },
        loadComplete: function() {
            // 修正為整體抽樣率
            $.ajax({
                handler: _handler,
                action: "cauculateSamplingRate",
                data : {
                    mainOid: $("#mainOid").val()
                },
                success: function(obj){
                    $("#samplingRate").val(obj.samplingRate);
                }
            });
        },
        needPager: false,
        multiselect: true,
        colModel: [{
            // "需覆審",
            colHeader: i18n.lms1800m01['grid.docStatus1'],name: 'showDocStatus1',width:45,align:"center",sortable: false,
            formatter: function(cellvalue, options, rowObject){
                if (cellvalue == "1") {
                    return "<img src='webroot/img/lms/V.png'>";
                }else if (cellvalue == "2") {
                    return "<img src='webroot/img/lms/V2.png'>";
                }else if (cellvalue == "3") {
                    return "<img src='webroot/img/lms/X.png'>";
                }else{
                    return cellvalue;
                }
            }
        }, {
            // "借款人統編",
            colHeader: i18n.lms1800m01['grid.custId'],name: 'custId',align: "left",width: 85,sortable: false
        }, {
            // "借款人姓名",
            colHeader: i18n.lms1800m01['L180M01B.elfCName'],name: 'elfCName',align: "left",width: 130,sortable: false
        }, {
            //小規模評分
            colHeader: i18n.lms1800m01['grid.sbScore'],name: 'sbScore', align: "left", width: 55,sortable: false
        }, {
            // "不覆審",
            colHeader: i18n.lms1800m01['grid.newNCkdFlag'],name: 'newNCkdFlag',align: "center", width: 40,sortable: false
        }, {
            //文件狀態
            colHeader: " ", name: 'createBY', align: "left", width: 30,sortable: false,
            formatter: function(value){
                return value == "PEO" ? "新增" : "";
            }
        }, {
            // "異常戶",
            colHeader: i18n.lms1800m01['grid.elfMDFlag'],name: 'elfMDFlag',align: "center",width: 40,
            formatter: function(value){
                //可能是 '  '
                return $.trim(value) == "" ? value:"<img src='webroot/img/lms/E.png'>";
            }
        }
        , { name: 'oid', hidden: true }
        , { name: 'showDocStatus1Val', hidden: true }
        ]
    });

    var $gridRandom = $("#randomGridview").iGrid({
        handler: 'lms1800gridhandler',
        height: 150,
        postData: {
            formAction: "queryList",
            type: "R"    //random
        },
        loadComplete: function() {
            $.ajax({
                handler: _handler,
                action: "cauculateRandomSamplingRate",
                data : {
                    mainOid: $("#mainOid").val()
                },
                success: function(obj){
                    $("#randomSamplingRate").val(obj.randomSamplingRate);
                }
            });
        },
        needPager: false,
        multiselect: true,
        colModel: [{
            // "需覆審",
            colHeader: i18n.lms1800m01['grid.docStatus1'],name: 'showDocStatus1',width:45,align:"center",sortable: false,
            formatter: function(cellvalue, options, rowObject){
                if (cellvalue == "1") {
                    return "<img src='webroot/img/lms/V.png'>";
                }else if (cellvalue == "2") {
                    return "<img src='webroot/img/lms/V2.png'>";
                }else if (cellvalue == "3") {
                    return "<img src='webroot/img/lms/X.png'>";
                }else{
                    return cellvalue;
                }
            }
        }, {
            // "借款人統編",
            colHeader: i18n.lms1800m01['grid.custId'],name: 'custId',align: "left",width: 85,sortable: false
        }, {
            // "借款人姓名",
            colHeader: i18n.lms1800m01['L180M01B.elfCName'],name: 'elfCName',align: "left",width: 130,sortable: false
        }, {
            // "不覆審",
            colHeader: i18n.lms1800m01['grid.newNCkdFlag'],name: 'newNCkdFlag',align: "center", width: 40,sortable: false
        }, {
            //文件狀態
            colHeader: " ", name: 'createBY', align: "left", width: 30,sortable: false,
            formatter: function(value){
                return value == "PEO" ? "新增" : "";
            }
        }
        , { name: 'oid', hidden: true }
        , { name: 'showDocStatus1Val', hidden: true }
        ]
    });
	
	//新增覆審名單
	$("#btn_addL180M01B").click(function(){
		
		//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		//確認新增覆審名單種類
		$.ajax({
            type: "POST",
            handler: _handler,
            data: {	 'formAction': 'getCtlTypeByBrNo'
            },
            success: function(responseData){
				 var ctlType =responseData.ctlType;
				 $("#custInfoForm").find("#_choiceCtlType").show();
				 //J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
				 build_selItem(responseData.selItem, responseData.selItemOrder, ".", true).done(function(){
					 
					 $("#_choiceCtlType").thickbox({
					        title: i18n.lms1800m01['L180M01B.choiceCtlType'], //"請選取覆審名單種類",
					        width: 400,
				            height: 100,
				            align: "center",
				            valign: "bottom",
				            modal: false,
				            i18n: i18n.def,
				            buttons: {
				                "sure": function(){
									//ctlType = $("input[name='_ctlType']:radio:checked").val();
									ctlType = $("#_ctlType").val();
									$.thickbox.close();
									AddCustAction.open({
							    		handler: _handler,
										action : 'produceNew',
										data : {
							                mainOid: $("#mainOid").val(),
											ctlType : ctlType
							            },
										callback : function(responseData){
											var tabForm = $("#tabForm");
											tabForm.injectData(responseData);
							            	
							            	$gridview.trigger("reloadGrid");
							            	$gridNReview.trigger("reloadGrid");
							            	//更新 opener 的 Grid
							            	CommonAPI.triggerOpener("gridview", "reloadGrid");
							            	
							            	//關掉 AddCustAction 的 thickbox
							            	$.thickbox.close();
											
										}
									});  
				                },
				                "cancel": function(){
				                    $.thickbox.close();
				                }
				            }
					    });
				 });
            }
        });   
		
		
	});
	//取消覆審名單
	$("#btn_dcL180M01B").click(function(){
		var oid_arr = gridGridSelectOidArr("gridview");
    	if(oid_arr.length==0){
    		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
    		return;
    	}
    	
		API.confirmMessage("是否確定要取消名單？", function(result){
            if (result) {
            	chose_nCkdFlag().done(function(json_nCkdFlag){
            		chose_newNextNwDt(json_nCkdFlag).done(function(json_nCkdFlag_newNextNwDt){
            			$.ajax({
    	    	            type: "POST",
    	    	            handler: _handler,
    	    	            data: $.extend({	 'formAction': "saveNoCTL"
	    	            		,'mainOid': $("#mainOid").val()
	    	            		,'oids': oid_arr.join("|")
    	    	            	}, json_nCkdFlag_newNextNwDt
    	    	            )
    	    	            , success: function(responseData){
    	    	            	$gridview.trigger("reloadGrid");
    	    	            	$gridNReview.trigger("reloadGrid");
    	    	            }
    	    	        });
                	});
            	});
        	}
    	});
		
	});
	//恢復已取消之覆審名單
	$("#btn_rsL180M01B").click(function(){
		var oid_arr = gridGridSelectOidArr("gridNReview");
    	if(oid_arr.length==0){
    		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
    		return;
    	}
    	
		API.confirmMessage("是否確定要恢復已取消之覆審名單？", function(result){
			if(result){
				$.ajax({
		            type: "POST", handler: _handler,
		            data: {	 'formAction': "saveReCTL1"
		            	,'mainOid': $("#mainOid").val() 
		            	,'oids': oid_arr.join("|")
		            },                
		            success: function(json_NeedLrDate){
		            	if(json_NeedLrDate.needLrDateOidList){
		            		var theQueue = $({});
                    		$.each(json_NeedLrDate.needLrDateOidList, function(idx, oid) {
                    			
                    			var needLrDateDesc = json_NeedLrDate.needLrDateDescList[idx];
                    			var newLRDate = json_NeedLrDate.newLRDateList[idx];
                    			var elfRckdLine = json_NeedLrDate.elfRckdLineList[idx]
                    			
                    			theQueue.queue('myqueue', function(next) {
                    				proc_saveReCTL_2(idx, oid, needLrDateDesc, newLRDate, elfRckdLine).done(function(){            		
                    				
                      					//---
                      					//把 next() 寫在 finish 的 callback 裡
                      					//才會 1個 url 抓到 response 後,再get下1個
                      					//不然會1次跳 N 個出來
                      					next(); 
                            		});
                    			});                                            			 
                    		});
                    		theQueue.dequeue('myqueue');
                    		
		            		
						}else{
							$gridview.trigger("reloadGrid");
							$gridNReview.trigger("reloadGrid");
						}
		            }
		        });
			}
    	});
	});
	
	//依前次已傳送覆審名單改列本次為暫不覆審
	$("#btn_nrL180M01B").click(function(){
		var _id = "_div_btnNckdFlagO";
		
		$gridNckdFlagO.trigger("reloadGrid");
		
			$("#"+_id).thickbox({
		        title: "請選取",
		        width: 500,
	            height: 270,
	            align: "center",
	            valign: "bottom",
	            modal: false,
	            i18n: i18n.def,
	            buttons: {
	                "sure": function(){
	                	 var data = $gridNckdFlagO.getSingleData();
	                     if (data) {
	                    	 $.ajax({
	                             type: "POST",
	                             handler: _handler,
	                             data: {
	                            	 mainOid: $("#mainOid").val(),
	                                 formAction: "saveNoCTL_O",	                                 
	                                 cmpOid: data.oid,
	                                 dataSrc: data.dataSrc
	                             },
	                             success: function(responseData){
	                            	 $("#tabForm").injectData(responseData);
	                            	 
	                            	 //更新  grid
	                            	 $("#gridview").trigger("reloadGrid");
	    	                         
	    	                     	 //更新 opener 的 Grid
	    	                     	 CommonAPI.triggerOpener("gridview", "reloadGrid");
	    	                     	 
	    	                     	$.thickbox.close();
	    	                     	
	    	                     	API.showMessage(i18n.def.runSuccess);
	                             }
	                         });
	                         
	                     }      
	                },
	                "cancel": function(){
	                    $.thickbox.close();
	                }
	            }
		    });
		
	});

	// 編輯小規模營業人覆審名單
	$("#btn_esL180M01B").click(function(){
	    $gridSmallBuss.trigger("reloadGrid");

        $("#editSmallBussBox").thickbox({
            title: "小規模營業人名單",
            width: 800,
            height: 400,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var rowId_arr = $gridSmallBuss.getGridParam('selarrrow');
                    var oid_arr = [];
                    for (var i = 0; i < rowId_arr.length; i++) {
                        var data = $gridSmallBuss.getRowData(rowId_arr[i]);
                        oid_arr.push(data.oid);
                    }

                    if(oid_arr.length==0){
                        API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
                        return;
                    }

                    var ds = $("[name=dsRadio]:checked").val();

                    if(ds == "1"){
                        // 比照 恢復已取消之覆審名單
                        $.ajax({
                            type: "POST", handler: _handler,
                            data: {	 'formAction': "saveReCTL1"
                                ,'mainOid': $("#mainOid").val()
                                ,'oids': oid_arr.join("|")
                            },
                            success: function(json_NeedLrDate){
                                if(json_NeedLrDate.needLrDateOidList){
                                    var theQueue = $({});
                                    $.each(json_NeedLrDate.needLrDateOidList, function(idx, oid) {

                                        var needLrDateDesc = json_NeedLrDate.needLrDateDescList[idx];
                                        var newLRDate = json_NeedLrDate.newLRDateList[idx];
                                        var elfRckdLine = json_NeedLrDate.elfRckdLineList[idx]

                                        theQueue.queue('myqueue', function(next) {
                                            proc_saveReCTL_2(idx, oid, needLrDateDesc, newLRDate, elfRckdLine).done(function(){

                                                //---
                                                //把 next() 寫在 finish 的 callback 裡
                                                //才會 1個 url 抓到 response 後,再get下1個
                                                //不然會1次跳 N 個出來
                                                next();
                                            });
                                        });
                                    });
                                    theQueue.dequeue('myqueue');
                                }else{
                                    $gridSmallBuss.trigger("reloadGrid");
                                }
                            }
                        });
                    } else if(ds == "2"){
                        // 比照 取消覆審名單
                        chose_nCkdFlag().done(function(json_nCkdFlag){
                            chose_newNextNwDt(json_nCkdFlag).done(function(json_nCkdFlag_newNextNwDt){
                                $.ajax({
                                    type: "POST",
                                    handler: _handler,
                                    data: $.extend({	 'formAction': "saveNoCTL"
                                        ,'mainOid': $("#mainOid").val()
                                        ,'oids': oid_arr.join("|")
                                        }, json_nCkdFlag_newNextNwDt
                                    )
                                    , success: function(responseData){
                                        $gridSmallBuss.trigger("reloadGrid");
                                    }
                                });
                            });
                        });
                    } else {
                        return CommonAPI.showErrorMessage("請選擇是否需覆審");
                    }
                },
                "close": function(){
                    $gridview.trigger("reloadGrid");
                    $gridNReview.trigger("reloadGrid");
                    $.thickbox.close();
                }
            }
        });
    });

    // J-110-0272 抽樣覆審 隨機抽樣覆審名單
    $("#btn_random").click(function(){
        $gridRandom.trigger("reloadGrid");
        $("#samplingCnt").val("");
        $("#editRandomBox").thickbox({
            title: "隨機抽樣覆審名單",
            width: 800,
            height: 400,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "close": function(){
                    $gridview.trigger("reloadGrid");
                    $gridNReview.trigger("reloadGrid");
                    $.thickbox.close();
                }
            }
        });
    });

    $("#btnGetRandom").click(function(){
        $.ajax({
            handler : _handler,
            type : "POST",
            data : {
                formAction: "samplingList",
                byCnt: false,
                mainOid: $("#mainOid").val()
            },
            success : function(obj) {
                if(obj.needLrDateOidList){
                    var theQueue = $({});
                    $.each(obj.needLrDateOidList, function(idx, oid) {
                        var needLrDateDesc = obj.needLrDateDescList[idx];
                        var newLRDate = obj.newLRDateList[idx];
                        var elfRckdLine = obj.elfRckdLineList[idx]

                        theQueue.queue('myqueue', function(next) {
                            proc_saveReCTL_2(idx, oid, needLrDateDesc, newLRDate, elfRckdLine).done(function(){
                                next();
                            });
                        });
                    });
                    theQueue.dequeue('myqueue');
                }else{
                    $gridRandom.trigger("reloadGrid");
                }
            }
        });
    });

    $("#btnGetRandomByCnt").click(function(){
        var samplingCnt = $.trim($("#samplingCnt").val());
        if (samplingCnt == "" || (/[^\d]/g.test(samplingCnt))) {
            return CommonAPI.showMessage("請輸入欲抽樣筆數！");
        } else if(samplingCnt <= 0) {
            return CommonAPI.showMessage("抽樣筆數需大於0！");
        }

        $.ajax({
            handler : _handler,
            type : "POST",
            data : {
                formAction: "samplingList",
                byCnt: true,
                samplingCnt: samplingCnt,
                mainOid: $("#mainOid").val()
            },
            success : function(obj) {
                if(obj.needLrDateOidList){
                    var theQueue = $({});
                    $.each(obj.needLrDateOidList, function(idx, oid) {
                        var needLrDateDesc = obj.needLrDateDescList[idx];
                        var newLRDate = obj.newLRDateList[idx];
                        var elfRckdLine = obj.elfRckdLineList[idx]

                        theQueue.queue('myqueue', function(next) {
                            proc_saveReCTL_2(idx, oid, needLrDateDesc, newLRDate, elfRckdLine).done(function(){
                                next();
                            });
                        });
                    });
                    theQueue.dequeue('myqueue');
                }else{
                    $gridRandom.trigger("reloadGrid");
                }
            }
        });
    });

    $("#btnDcRandom").click(function(){
        // 比照 取消覆審名單
        var rowId_arr = $gridRandom.getGridParam('selarrrow');
        var oid_arr = [];
        for (var i = 0; i < rowId_arr.length; i++) {
            var data = $gridRandom.getRowData(rowId_arr[i]);
            oid_arr.push(data.oid);
        }

        if(oid_arr.length==0){
            API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
            return;
        }
        chose_nCkdFlag().done(function(json_nCkdFlag){
            chose_newNextNwDt(json_nCkdFlag).done(function(json_nCkdFlag_newNextNwDt){
                $.ajax({
                    type: "POST",
                    handler: _handler,
                    data: $.extend({	 'formAction': "saveNoCTL"
                        ,'mainOid': $("#mainOid").val()
                        ,'oids': oid_arr.join("|")
                        }, json_nCkdFlag_newNextNwDt
                    )
                    , success: function(responseData){
                        $gridRandom.trigger("reloadGrid");
                    }
                });
            });
        });
    });

    $("#btnRsRandom").click(function(){
        // 比照 恢復已取消之覆審名單
        var rowId_arr = $gridRandom.getGridParam('selarrrow');
        var oid_arr = [];
        var err_arr = [];
        for (var i = 0; i < rowId_arr.length; i++) {
            var data = $gridRandom.getRowData(rowId_arr[i]);
            oid_arr.push(data.oid);
            if(data.newNCkdFlag == "13"){
                err_arr.push(data.oid);
            }
        }

        if(oid_arr.length==0){
            API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
            return;
        }

        if(err_arr.length>0){
            API.showMessage("不覆審代碼13不可更改為要覆審，請使用抽樣功能！");
            return;
        }
        $.ajax({
            type: "POST", handler: _handler,
            data: {	 'formAction': "saveReCTL1"
                ,'mainOid': $("#mainOid").val()
                ,'oids': oid_arr.join("|")
            },
            success: function(json_NeedLrDate){
                if(json_NeedLrDate.needLrDateOidList){
                    var theQueue = $({});
                    $.each(json_NeedLrDate.needLrDateOidList, function(idx, oid) {

                        var needLrDateDesc = json_NeedLrDate.needLrDateDescList[idx];
                        var newLRDate = json_NeedLrDate.newLRDateList[idx];
                        var elfRckdLine = json_NeedLrDate.elfRckdLineList[idx]

                        theQueue.queue('myqueue', function(next) {
                            proc_saveReCTL_2(idx, oid, needLrDateDesc, newLRDate, elfRckdLine).done(function(){

                                //---
                                //把 next() 寫在 finish 的 callback 裡
                                //才會 1個 url 抓到 response 後,再get下1個
                                //不然會1次跳 N 個出來
                                next();
                            });
                        });
                    });
                    theQueue.dequeue('myqueue');
                }else{
                    $gridRandom.trigger("reloadGrid");
                }
            }
        });
    });

    $("#btnDcNReview").click(function(){
        // 比照 取消覆審名單
        var oid_arr = gridGridSelectOidArr("gridNReview");
        if(oid_arr.length==0){
            API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
            return;
        }

        API.confirmMessage("是否確定要取消名單？", function(result){
            if (result) {
                chose_nCkdFlag().done(function(json_nCkdFlag){
                    chose_newNextNwDt(json_nCkdFlag).done(function(json_nCkdFlag_newNextNwDt){
                        $.ajax({
                            type: "POST",
                            handler: _handler,
                            data: $.extend({	 'formAction': "saveNoCTL"
                                ,'mainOid': $("#mainOid").val()
                                ,'oids': oid_arr.join("|")
                                }, json_nCkdFlag_newNextNwDt
                            )
                            , success: function(responseData){
                                $gridNReview.trigger("reloadGrid");
                            }
                        });
                    });
                });
            }
        });
    });
	//===================================================
	
	function openDoc(cellvalue, options, rowObject){
		var postData = {'mainOid':rowObject.oid };
		$.form.submit({ url:'../lms1800m02/01', data:postData, target:rowObject.oid});		
	}
	function chose_nCkdFlag(){
		var my_dfd = $.Deferred();
		
		var _id = "_div_chose_lrs_nCkdFlag";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table>");
			dyna.push("	<tr><td><input type='radio' id='newNCkdFlag' name='newNCkdFlag' /></td></tr>");
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
	 
		     $.ajax({
		    	 type: 'post', handler: _handler, data:{'formAction':'load_nckdFlag'},
		         success: function(json_combos){
		         	$("#newNCkdFlag").setItems({ item: json_combos.nckdFlag,format: "{key}",size: 1});
					
					var dynaB = [];
					dynaB.push("-------以下為董事會(或常董會)權限案件實地覆審專用--------<br>");
					
					//var nckdFlagA = $("#newNCkdFlag").find("[name='newNCkdFlag'][value='A']:radio");
					$("[name='newNCkdFlag'][value='A']:radio").parent().parent().prepend("<br>------------※以下為【覆審種類B.董事會(或常董會)權限案件實地覆審】專用-------------<br>");

                    // J-110-0272 抽樣覆審，不覆審代碼13 僅供系統用，不可經由人工更改
					$("[name='newNCkdFlag'][value='13']:radio").attr('disabled', true);
		         }
		     });
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	       title: "請選擇不覆審原因", width: 550, height: 400, align: "center", valign: "bottom", modal: false,
           i18n: i18n.def, buttons: {
               "sure": function(){
                   var newNCkdFlag = $("#"+_form).find("[name='newNCkdFlag']:checked").val();
                   if(newNCkdFlag){                	   
                   }else{
                	   CommonAPI.showMessage("請選擇不覆審原因");
                       return;
                   }
                                      
                  $.thickbox.close();
                  my_dfd.resolve( {'newNCkdFlag':newNCkdFlag} );	
               },
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
		
		return my_dfd.promise();
	}
	
	function chose_newNextNwDt(src_param){
		var my_dfd = $.Deferred();
		if(src_param.newNCkdFlag=="8"){
			var frm =  $("#div_NEW_NEXTNWDT_form");
			frm.reset();
			
			$("#div_NEW_NEXTNWDT").thickbox({ // 使用選取的內容進行彈窗
		       title: "請輸入", width: 550, height: 150, align: "center", valign: "bottom", modal: false,
	           i18n: i18n.def, buttons: {
	               "sure": function(){		            	   
	            	   
	            	   if(frm.valid()){
	            		   $.thickbox.close();
	            		   var o = {};
	           				o['newNextNwDt'] = frm.find("#newNextNwDt").val();
	           				my_dfd.resolve($.extend(src_param, o));   
	            	   }
	               },
	               "cancel": function(){
	            	   $.thickbox.close();
	               }
	           }
			});
			
		}else{
			my_dfd.resolve(src_param); 
		}
		return my_dfd.promise();
	}
	
	function proc_saveReCTL_2(idx, oid, needLrDateDesc, newLRDate, elfRckdLine){
		//------
		var my_dfd = $.Deferred();   
		var frm = $("#div_newLRDate_form");
		//先指定 lrDate 的值
		frm.find("[name='newLRDate']").val( newLRDate );
		frm.find("#div_newLRDate_desc").val(needLrDateDesc);
		if(elfRckdLine=="C"){
			$("#div_newLRDate_newAddC").show();
			frm.find("[name='newLRDate']").removeClass("required");
		}else{
			$("#div_newLRDate_newAddC").hide();
			frm.find("[name='newLRDate']").addClass("required");
		}
		$("#div_newLRDate").thickbox({ // 使用選取的內容進行彈窗
	       title: "確認輸入上次覆審日", width: 550, height: 230, align: "center", valign: "bottom", modal: false,
           i18n: i18n.def, buttons: {
               "sure": function(){            	   
            	   if(frm.valid()){
            		   var newLRDate = frm.find("[name='newLRDate']").val();
            		   
            		   $.ajax({
    	    	            type: "POST",
    	    	            handler: _handler,
    	    	            data: {	 'formAction': 'saveReCTL2'
    	    	            		,'mainOid': $("#mainOid").val()
    	    	            		,'oid': oid
    	    	            		,'newLRDate':newLRDate
    	    	            },
    	    	            success: function(responseData){
    	    	            	$gridview.trigger("reloadGrid");
    	    	            	$gridNReview.trigger("reloadGrid");
    	    	            	// 因為抽樣也有呼叫到這個function 所以重刷
    	    	            	$gridRandom.trigger("reloadGrid");
    	    	            	$.thickbox.close();
    	    	            	my_dfd.resolve();
    	    	            }
    	    	        });   
            	   }						                   	
               }
           }
		});
		return my_dfd.promise();
	}
});

function build_selItem(json_selItem, json_selItemOrder, sep, doCopy){

    //J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	var my_dfd = $.Deferred();
	$.each(json_selItem, function(itemName, kvMap) {
		var chooseItem = $("#"+itemName);
		var _addSpace = false;
		if(chooseItem.attr("space")=="true"){
			_addSpace = true;
		}
		
		var _fmt = "{key}";
		if(chooseItem.attr("myShowKey")==="Y"){
			_fmt = "{value}"+sep+"{key}";
		}
		$.each(json_selItemOrder[itemName], function(idx, kVal) {
			var currobj = {};
    		currobj[kVal] = kvMap[kVal];
    		
    		chooseItem.setItems({ item: currobj, format: _fmt, clear:false, space: (_addSpace?(idx==0):false) });	    		
		});			
//		$("#"+itemName).setOptions(kvMap, false);
		//---
		//copy html element
		if(doCopy){
			$("#"+itemName+"_1").html( chooseItem.html() );	
		}

	});
	
	//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	my_dfd.resolve();
	return my_dfd.promise();
	
		
}
