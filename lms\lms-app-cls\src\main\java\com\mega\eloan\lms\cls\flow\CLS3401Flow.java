package com.mega.eloan.lms.cls.flow;

import java.text.MessageFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.stereotype.Component;

import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C340M01C;
import com.mega.sso.context.MegaSSOSecurityContext;

import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;

@Component
public class CLS3401Flow extends AbstractFlowHandler {

	@Resource
	CommonMetaDao metaDao;

	@Resource
	CLSService clsService;
	
	@Resource
	ContractDocService contractDocService;
	
	@Resource
	SysParameterService sysParameterService;

	@Transition(node = "決策", value = "to核定")
	public void ok(FlowInstance instance) throws FlowMessageException {
		String instanceId = Util.trim(instance.getId());
		C340M01A meta = (C340M01A) metaDao.findByOid(getDomainClass(), instanceId);
		meta.setApprover(MegaSSOSecurityContext.getUserId());
		meta.setApproveTime(CapDate.getCurrentTimestamp());


		// 線上對保要發電文
		if (ContractDocConstants.C340M01A_CtrType.Type_A.equals(meta.getCtrType())) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(new Date());
			cal.add(Calendar.DATE, 14);
			meta.setPloanCtrStatus("1");
			meta.setPloanCtrExprDate(cal.getTime());
			
			try{
				//發送電文給線上對保平台
				JSONObject rtnJSON = contractDocService.ploan_sendSigningContract(meta);
				if(!Util.equals(rtnJSON.optString("stat"), "ok")){
					throw new FlowMessageException("送「線上貸款平台」失敗，請洽資訊處!");
				}
			}catch(Exception e){
				throw new FlowMessageException(e.getMessage());
			}	
			
		}else if (ContractDocConstants.C340M01A_CtrType.Type_B.equals(meta.getCtrType())) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(new Date());
			cal.add(Calendar.DATE, 14);
			meta.setPloanCtrStatus("1");
			meta.setPloanCtrExprDate(cal.getTime());

			try{
				//發送電文給線上對保平台
				JSONObject rtnJSON = contractDocService.ploan_sendSigningContract(meta);
				if(!Util.equals(rtnJSON.optString("stat"), "ok")){
					throw new FlowMessageException("送「線上貸款平台」失敗，請洽資訊處!");
				}
			}catch(Exception e){
				throw new FlowMessageException(e.getMessage());
			}

		}else if (ContractDocConstants.C340M01A_CtrType.Type_L.equals(meta.getCtrType())) {
			if(clsService.is_function_on_codetype("send_PLOAN014")){
				Calendar cal = Calendar.getInstance();
				cal.setTime(new Date());
				cal.add(Calendar.DATE, 30);
				meta.setPloanCtrStatus("1");
				meta.setPloanCtrExprDate(cal.getTime());
				try{
					//發送電文給勞工紓困平台
					JSONObject rtnJSON = contractDocService.ploan_sendSigningContract(meta);
					if(!Util.equals(rtnJSON.optString("stat"), "ok")){
						throw new FlowMessageException("送「勞工紓困平台」失敗，請洽資訊處!");
					}
					else//發送SMS簡訊
					{
						C340M01C c340m01c = meta.getC340m01cs().get(0);
						String content = "";
						if (c340m01c == null || CapString.isEmpty(c340m01c.getJsonData())) {
							content = "{}";
						} else {
							content = c340m01c.getJsonData();
						}
						JSONObject jsContent = JSONObject.fromObject(content);

						if(Util.isNotEmpty(jsContent.get("borrowerMobileNumber"))){
							Map<String, Object> map = new HashMap<String, Object>();
							
							String ownBrId = meta.getOwnBrId();
							map.put("mainId", meta.getCaseMainId());
							map.put("ownBrId", ownBrId);
							map.put("custId", meta.getCustId());
							map.put("dupNo", meta.getDupNo());
							map.put("custName", meta.getCustName());
							map.put("approver", meta.getApprover());
							map.put("approveTime", meta.getApproveTime());
							map.put("borrowerMobileNumber", jsContent.get("borrowerMobileNumber"));
							map.put("loanAmt", jsContent.get("loanAmt"));
							
							String url = sysParameterService.getParamValue(SysParamConstants.PLOAN_URL_LABOR);
							String Prod69_SMS_Content = "您好，您所申請的兆豐銀行勞工紓困貸款已可簽約對保，請盡速辦理，線上對保請點選{0}，如僅持有本行未臨櫃提升權限之數位存款帳戶，請準備自然人憑證或洽臨櫃辦理。";
							String smsContent=MessageFormat.format(Prod69_SMS_Content,url);
							clsService.createSMS(map, smsContent);
						}
					}
					clsService.changeC122M01AStatFlag(meta.getOwnBrId(), meta.getCustId(),ClsConstants.C122M01A_StatFlag.待對保);
				}catch(Exception e){
					throw new FlowMessageException(e.getMessage());
				}				
			}
		}else if (ContractDocConstants.C340M01A_CtrType.Type_S.equals(meta.getCtrType())) {
			meta.setPloanCtrStatus("1");
			meta.setPloanCtrExprDate(CapDate.shiftDays(CapDate.getCurrentTimestamp(), 14));
		}
	}


	@Transition(node = "決策", value = "to退回")
	public void back(FlowInstance instance) {

	}



	@Override
	public Class<? extends Meta> getDomainClass() {
		return C340M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}