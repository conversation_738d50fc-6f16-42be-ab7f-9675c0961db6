/* 
 * L140S09BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S10B;

/** 其他敘作條件細項資訊檔 **/
public interface L140S10BDao extends IGenericDao<L140S10B> {

	L140S10B findByOid(String oid);
	
	List<L140S10B> findByMainId(String mainId);

	List<L140S10B> findByMainIdAndBizCat(String mainId, String bizCatType);

	L140S10B findByMainIdAndBizCatAndBizItem(String mainId, String bizCat, String bizItem);
	
	List<L140S10B> findByMainIdAndBizCatAndSequence(String mainId, String bizCat, int[] seqArray);

	L140S10B findMaxSeqNumByMainId(String mainId);
	
	L140S10B findMaxBizItemByMainIdAndBizCat(String mainId, String bizCat);
}