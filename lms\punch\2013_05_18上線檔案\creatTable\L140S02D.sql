---------------------------------------------------------
-- LMS.L140S02D 分段利率明細檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140S02D;
CREATE TABLE LMS.L140S02D (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	PHASE         DECIMAL(3,0)  not null,
	ISUSEBOX      CHAR(1)      ,
	BGNNUM        DECIMAL(3,0) ,
	ENDNUM        DECIMAL(3,0) ,
	RATETYPE      CHAR(2)      ,
	RATEUSER      DECIMAL(6,4) ,
	RATEUSERTYPE  CHAR(3)      ,
	BASERATE      DECIMAL(6,4) ,
	PMFLAG        CHAR(1)      ,
	PMRATE        DECIMAL(6,4) ,
	NOWRATE       DECIMAL(6,4) ,
	RATEFLAG      CHAR(1)      ,
	RATECHGWAY    CHAR(1)      ,
	RATECHGWAY2   CHAR(2)      ,
	BASEDESC      VARCHAR(150) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140S02D PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140S02D01;
CREATE UNIQUE INDEX LMS.XL140S02D01 ON LMS.L140S02D   (MAINID, SEQ, PHASE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140S02D IS '分段利率明細檔';
COMMENT ON LMS.L140S02D (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQ           IS '序號', 
	PHASE         IS '段別', 
	ISUSEBOX      IS '是否勾選使用', 
	BGNNUM        IS '期別-起', 
	ENDNUM        IS '期別-迄', 
	RATETYPE      IS '利率基礎', 
	RATEUSER      IS '利率基礎(自訂利率)', 
	RATEUSERTYPE  IS '自訂利率參考指標', 
	BASERATE      IS '指標利率', 
	PMFLAG        IS '加減碼', 
	PMRATE        IS '加減碼利率', 
	NOWRATE       IS '目前利率', 
	RATEFLAG      IS '利率方式', 
	RATECHGWAY    IS '利率變動方式', 
	RATECHGWAY2   IS '利率變動方式(每『月/三個月/半年/九個月/年』調整乙次)', 
	BASEDESC      IS '利率基礎組成文字', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
