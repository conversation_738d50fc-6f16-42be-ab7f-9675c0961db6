/* 
 * L140MM1A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.IDataObject;

/** 央行註記異動作業主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140MM1A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo","cntrNo"}))
public class L140MM1A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L140AM1A．關聯檔 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l140mm1a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)	
	private Set<L140AM1A> l140am1a;

	public Set<L140AM1A> getL140am1a() {
		return l140am1a;
	}

	public void setL140am1a(Set<L140AM1A> l140am1a) {
		this.l140am1a = l140am1a;
	}
	
	/** 
	 * 刪除註記<p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** RptId **/
	@Size(max=32)
	@Column(name="RPTID", length=32, columnDefinition="VARCHAR(32)")
	private String rptId;
	
	/**
	 * 動用審核表確認當日<br/>
	 * for國內企金
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "SDATE", columnDefinition = "DATE")
	private Date sDate;
	
	
	/** 編輯類型	C:新增 **/
	@Size(max=1)
	@Column(name="EDITTYPE", length=12, columnDefinition="CHAR(1)")
	private String editType;
	

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得RptId **/
	public String getRptId() {
		return this.rptId;
	}
	/** 設定RptId **/
	public void setRptId(String value) {
		this.rptId = value;
	}

	/**
	 * 設定動用審核表確認當日<br/>
	 * for國內企金
	 */
	public void setSDate(Date value) {
		this.sDate = value;
	}

	/**
	 * 取得動用審核表確認當日<br/>
	 * for國內企金
	 */
	public Date getSDate() {
		return this.sDate;
	}

	public String getEditType() {
		return editType;
	}

	public void setEditType(String editType) {
		this.editType = editType;
	}
}
