/* 
 * L230A01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L230A01A;

/** 簽約未動用授信案件報送授權檔 **/
public interface L230A01ADao extends IGenericDao<L230A01A> {

	L230A01A findByOid(String oid);

	List<L230A01A> findByMainId(String mainId);

	L230A01A findByUniqueKey(String mainId, String ownUnit, String authType,
			String authUnit);

	List<L230A01A> findByIndex01(String mainId, String ownUnit,
			String authType, String authUnit);

	/**
	 * 找 被授權單位
	 * 
	 * @param mainId
	 *            文件編號
	 * @param authUnit
	 *            被授權單位
	 * @return
	 */
	L230A01A findByUniqueKey(String mainId, String authUnit);

}