package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 模擬動審 - 檢核表
 * </pre>
 * 
 * @since
 * <AUTHOR>
 * @version <ul>
 *          <li>
 *          </ul>
 */
public class CLS1161S42Panel extends Panel {

	public CLS1161S42Panel(String id) {
		super(id);
	}

	public CLS1161S42Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;
}
