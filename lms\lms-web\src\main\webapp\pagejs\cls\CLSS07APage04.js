var oldOid = "";
var _handler = "";
$(function() {
//initDfd.done(function(){
	ilog.debug("CLSS07APage04.js init_grid[beg]");
	
	gridViewShow();
	A_1_10_4();
	A_1_10_3();
	ilog.debug("CLSS07APage04.js init_grid[end]");
	$("#showSel04").change(function(i){
/*
		$("#showGrid").show();
		$("#gridviewAA").jqGrid("setGridParam", {//重新設定grid需要查到的資料
			postData : {
				formAction:"queryL140m01a2",
				custId : $("#showSel04 option:selected").val()
			},
			search: true
		}).trigger("reloadGrid");
*/
		$("#showBrid").show();
		$("#formSearch").find("#textBrid").val(userInfo.unitNo);
	});

	//依10碼ID 去查詢
	$("#buttonSearch04").click(function(){				
		if($("#formSearch").valid()){
	        $.ajax({
	            type: "POST",
	            handler: responseJSON["handler"],
	            data: {
	                formAction: "getCustData",
	                custId: $("#formSearch").find("#searchId04").val()
	            },
	            success: function(responseData04){
	                // alert(JSON.stringify(responseData));
				      var selJson04 = {
					       		item : responseData04.selCus,
					       		format : "{value} - {key}",
					       		space: true
					       	};
				      $("#selCus04").setItems(selJson04);
				      $("#showSel04").show();
					  $("#selCus04").show();				
	            }
	        });						
		}
	});
	
	//依10碼ID、分行 去查詢
	$("#buttonSearch05").click(function(){				
		if($("#formSearch").valid()){
			$("#showGrid").show();
			$("#gridviewAA").jqGrid("setGridParam", {//重新設定grid需要查到的資料
				postData : {
					formAction:"queryL140m01a2",
					custId : $("#showSel04 option:selected").val(),
					textBrid : $("#textBrid").val()
				},
				page : 1,
				search: true
			}).trigger("reloadGrid");						
		}
	});	
	
	$("#buttonSearch05_l120s06b_type2_OrderByRate").click(function(){				
		if($("#formSearch_l120s06b_type2_OrderByRate").valid()){
			$("#showGrid_l120s06b_type2_OrderByRate").show();
			$("#gridviewAA_l120s06b_type2_OrderByRate").jqGrid("setGridParam", {//重新設定grid需要查到的資料
				postData : {
					formAction:"queryList_for_cls_l120s06b_type2_orderByRate",
					prodKind : $("#formSearch_l120s06b_type2_OrderByRate_prodKind option:selected").val(),
					subj : $("#formSearch_l120s06b_type2_OrderByRate_subj option:selected").val(),
					lnPurs : $("#formSearch_l120s06b_type2_OrderByRate_lnPurs option:selected").val()
				},
				page : 1,
				search: true
			}).trigger("reloadGrid");						
		}
	});	
	
});

function gridViewShow(){
	var gridView = $("#gridviewShow").iGrid({
		handler: 'cls1141gridhandler',
		//height: 345, //for 15 筆
		height: "230px", //for 10 筆
		//autoHeight: true,
		width: "100%",
		postData : {
			formAction : "queryL120s06a",
			mainId : responseJSON.mainid,
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		sortname: 'custId',
		multiselect: true,hideMultiselect:false,
		// autofit: false,
		autowidth:true,
		colModel: [{
		  colHeader: i18n.clss07a["L1205S07.gridb"],//"本次授信對像"
		  name: 'custId',
		  align:"center",
		  width: 110,
		  sortable: true,
		  formatter : 'click',
		  onclick : openDoc2
		}, {
		  colHeader: i18n.clss07a["L1205S07.grid3"],//"本次科目"
		  name: 'lnSubject',
		  width: 80,
		  sortable: true
		}, {
		  colHeader: i18n.clss07a["L1205S07.grid4"],//"本次額度序號"
		  width: 90,
		  name: 'cntrNo',
		  sortable: true
		}, {
		  colHeader: i18n.clss07a["L1205S07.grid5"],//"對照授信對象"
		  name: 'custId2',
		  width: 110,
		  sortable: true,
		  align:"center"
		}, {
		  colHeader: i18n.clss07a["L1205S07.grid6"],//"對照科目"
		  name: 'lnSubject2',
		  width: 80,
		  sortable: true,
		  align:"right"
		}, {
		  colHeader: i18n.clss07a["L1205S07.grid7"],//"對照額度序號"
		  name: 'cntrNo2',
		  width: 90,
		  sortable: true,
		  align:"left"
		}, {
		  colHeader: "<span class='text-red'>"+i18n.clss07a['L1205S07.grid8']+"</span>",//列印模式
		  name: 'printMode',
		  width:50,
		  sortable: false,
		  align:"center"
		},{
	      colHeader: "&nbsp",//"檢核欄位",
	      name: 'chkYN',
	      width: 20,
	      sortable: false,
	      align:"center"
	    },{
		  name: 'oid',
		  hidden: true
		}],
			ondblClickRow: function(rowid){
				var data = gridView.getRowData(rowid);
				openDoc2(null, null, data);
		}
	});
}
/**
 * 請選擇本次欲產生對照表之額度明細表
 */
function A_1_10_4(){
	$("#gridviewCC").iGrid({
   	 handler: "cls1141gridhandler",
        height: 200,
        rownumbers:true,
		multiselect : true,
		hideMultiselect : false,
		caption: "&nbsp;",
		hiddengrid : false,
        sortname: 'cntrNo',
        sortorder: 'asc',
        //原本LMS1405GridHandler::queryL140m01a
        postData : {
				formAction:"queryL140m01aLoanItemCompare",
				itemType:"1"
			},
        colModel: [{
            colHeader: i18n.cls1151s01["L140M01A.custName"],//借款人名稱
            width: 140,
            name: 'custName',
            sortable: false			 
        }, {
            colHeader: i18n.cls1151s01["L140M01A.cntrNo"],//"額度序號",
            name: 'cntrNo',
            width: 80,
            sortable: false
        }, {
            colHeader: i18n.cls1151s01["L140M01A.cntrNoCom"],//"共用額度序號",
            name: 'commSno',
            width: 80,
            sortable: false
        }, {
            colHeader: "&nbsp;",
            name: 'currentApplyCurr',
            width: 25,
            sortable: false,
			 align:"center"
        }, {
            colHeader: i18n.cls1151s01["L140M01A.moneyAmt"],//現請額度,
            name: 'currentApplyAmt',
            width: 100,
            sortable: false,
			 align:"right",
			 formatter:'currency', 
			 formatoptions:
			   {
			    thousandsSeparator: ",",
			    decimalPlaces: 0//小數點到第幾位
			    }
        }, {
            colHeader: i18n.cls1151s01["L140M01A.type"],//"性質"
            name: 'proPerty',
            width: 70,
            sortable: false,
            align:"center",
            formatter:proPertyFormatter
        }, {
            colHeader:i18n.cls1151s01["L140M01A.docStatus"], //"文件狀態",
            name: 'docStatus',
            width: 60,
            sortable: false,
			 align:"center"
        }, {
            colHeader: i18n.cls1151s01["L140M01A.branchId"],//"分行別",
            name: 'ownBrId',
            width: 80,
            sortable: false,
			 align:"center"
        },	{
            name: 'oid',
            hidden: true
        },{
            name: 'printSeq',
            hidden: true
        }],
        	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
    });
}

function A_1_10_3(){
var grid = $("#gridviewAA").iGrid({
  handler: 'cls1141gridhandler',
  //height: 345, //for 15 筆
  height: "230px", //for 10 筆
  //autoHeight: true,
  width: 785,
	postData : {
		formAction : "queryL140m01a2",
		custId : $("#formSearch").find("#searchId").val(),
		textBrid : $("#textBrid").val(),
		rowNum:10
	},
  sortname: 'endDate',
//  multiselect: true,hideMultiselect:false,
 // autofit: false,
  shrinkToFit: true,
  autowidth: false,
  caption: "&nbsp;",
  hiddengrid : false,  
  colModel: [{
//      colHeader: i18n.clss07a["L1205S07.grid31"],//"簽案日期"
//      name: 'caseDate',
//		align:"center",
//		width: 80,
//      sortable: false
//  }, {
      colHeader: i18n.clss07a["L1205S07.grid40"],//"核准日期"
      name: 'endDate',
		align:"center",
		width: 80,
      sortable: false
  }, {
      colHeader: i18n.clss07a["L1205S07.grid32"],//"案號",
      name: 'caseNo',
      width: 130,
      sortable: false
  }, {
      colHeader: i18n.clss07a["L1205S07.grid33"],//"額度序號",
      name: 'cntrNo',
      width: 80,
      sortable: false,
		align:"center"
  }, {
      colHeader: i18n.clss07a["L1205S07.grid35"],//"現請額度",
      name: 'currentApplyAmt',
      width: 80,
      sortable: false,
						align:"right"
  }, {
      colHeader: i18n.clss07a["L1205S07.grid36"],//"科目",
      name: 'lnSubject',
      width:200,
      sortable: false,
		align:"center"
  },{
      name: 'mainId',
      hidden: true
  },{
      name: 'caseDate',
      hidden: true
  }],
  	ondblClickRow: function(rowid){  
  }
});
//=======

$("#gridviewAA_l120s06b_type2_OrderByRate").iGrid({
  handler: 'cls1141gridhandler',
  height: "230px",
  width: 900,
	postData : {
		formAction : "queryList_for_cls_l120s06b_type2_orderByRate",
		prodKind : "",
		subj : "",
		lnPurs : ""
	},
  needPager: false,
  shrinkToFit: true,  
  colModel: [{
//      colHeader: i18n.clss07a["L1205S07.grid31"],//"簽案日期"
//      name: 'caseDate',
//		align:"center",
//		width: 80,
//      sortable: false
//  }, {
      colHeader: i18n.clss07a["L1205S07.grid40"],//"核准日期"
      name: 'endDate',
		align:"center",
		width: 80,
      sortable: false
  }, {
      colHeader: i18n.clss07a["L1205S07.grid32"],//"案號",
      name: 'caseNo',
      width: 80,
      sortable: false
  }, {
      colHeader: i18n.clss07a["L120S06A.custId2"],//"對照授信戶統編",
      name: 'custId',
      width: 90,
      sortable: false
  }, {
      colHeader: i18n.clss07a["L120S06A.custName2"],//"對照授信戶",
      name: 'custName',
      width: 90,
      sortable: false
  }, {
      colHeader: i18n.clss07a["L1205S07.grid33"],//"額度序號",
      name: 'cntrNo',
      width: 90,
      sortable: false,
		align:"center"
  }, {
      colHeader: i18n.clss07a["L1205S07.grid35"],//"現請額度",
      name: 'currentApplyAmt',
      width: 70,
      sortable: false,
						align:"right"
  }, {
      colHeader: i18n.clss07a["L1205S07.grid36"],//"科目",
      name: 'lnSubject',
      width:80,
      sortable: false,
		align:"center"
  }, {
      colHeader: i18n.clss07a["label.ltv_from_l140m01o"], // 成數 
      name: 'ltv_from_l140m01o',
      width:35,
      sortable: false,
	  align:"right"
  }, {
      colHeader: i18n.clss07a["label.nowExtendPeriod_from_l140s02e"], // 寬限期期數
      name: 'nowExtendPeriod_from_l140s02e',
      width:35,
      sortable: false,
	  align:"right"
  }, {
      colHeader: i18n.clss07a["L1205S07.index28"],//"利率",
      name: 'itemType2_itemDscr',
      width:300,
      sortable: false
  },{
      name: 'caseDate',
      hidden: true
  },{
      name: 'mainId',
      hidden: true
  }],
  	ondblClickRow: function(rowid){  
  }
});

}