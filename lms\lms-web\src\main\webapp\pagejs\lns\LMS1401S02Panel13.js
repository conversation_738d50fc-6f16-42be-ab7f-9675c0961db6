var dfd131 = new $.Deferred(), dfd132 = new $.Deferred(), dfd133 = new $.Deferred(), dfd137 = new $.Deferred();

initAll.done(function(inits){
    dfd131.done(gridviewitemChildren_13);//科子目限額
    dfd133.done(gridviewitemChildren3_13);
     
    //====================button event ================================
    //新增本案無追索買方額度資訊
    $("#newItemChildren1Bt13_1").click(function(){
        newItemChildren1_13();
    });
    
    $("#importExcel13_1").click(function(){
         importExcel13_1();
    });
    
    $("#countTotal13_1").click(function(){
    	countTotal13_1();
    });

    $("#applyARAppendix13_B").click(function(){
    	applyARAppendix13('B');
    });
    
    //刪除本案無追索買方額度資訊
    $("#removeGridviewitemChildren13_1").click(function(){
        var gridID = $("#gridviewitemChildren_13").getGridParam('selarrrow');
        if (gridID == "") {
            //TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var gridIDList = [];
                for (var i = 0; i < gridID.length; i++) {
                    gridIDList[i] = $("#gridviewitemChildren_13").getRowData(gridID[i]).oid;
                }
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteL140m01s",
                        tabFormMainId: $("#tabFormMainId").val(),
                        Idlist: gridIDList
                    },
                    success: function(obj){
                        $("#gridviewitemChildren_13").trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
     
    
    $("#lms140Tab13").click(function(){
        dfd131.resolve();
        dfd137.resolve();
        
        $("#gridviewitemChildren_13").jqGrid("setGridParam", {//當文件載完再將科子目的grid更新
            sortname: 'itemSeq',
            sortorder: 'asc',
            postData: {
                formAction: "queryL140m01s",
                tabFormMainId: $("#tabFormMainId").val(),
                type: "1",
				noOpenDoc: true
            },
            search: true
        }).trigger("reloadGrid");
        
    });
    $("#tab13_1,#tab13_3").find("a").click(function(){
        var $thisId = $(this).parent("li").attr("id");
        switch ($thisId) {
	        case "tab13_1":
	            dfd131.resolve();
	            $("#gridviewitemChildren_13").jqGrid("setGridParam", {//當文件載完再將科子目的grid更新
	                sortname: 'itemSeq',
	                sortorder: 'asc',
	                postData: {
	                    formAction: "queryL140m01s",
	                    tabFormMainId: $("#tabFormMainId").val(),
	                    type: "1",
						noOpenDoc: true
	                },
	                search: true
	            }).trigger("reloadGrid");
	            break;
            case "tab13_3":
                dfd133.resolve();
                $("#gridviewitemChildren3_13").jqGrid("setGridParam", {//當文件載完再將科子目的grid更新
                    sortname: 'itemSeq',
                    sortorder: 'asc',
                    postData: {
                        formAction: "queryL140m02s",
                        tabFormMainId: $("#tabFormMainId").val(),
                        type: "B",
						noOpenDoc: true
                    },
                    search: true
                }).trigger("reloadGrid");
                break;

        }
    });
    
    
     
    //驗證是不是數字   
    function isNumber(val){
        return /\d/.test(val);
    }
    
     
    
    //====================button event End================================
    
    //====================Grid Code ======================================
    
    /**  本案無追索買方額度資訊grid  */
    function gridviewitemChildren_13(){
        $("#gridviewitemChildren_13").iGrid({
            handler: 'lms1401gridhandler',
            height: 170,
            rownumbers: false,
            multiselect: true,
            hideMultiselect: false,
            sortname: 'itemSeq',
            sortorder: 'asc',
            shrinkToFit : false,
            needPager: false,
            postData: {
                formAction: "queryL140m01s",
                tabFormMainId: $("#tabFormMainId").val(),
                type: "1",
				noOpenDoc: true
            },
            autowidth: true,
            colModel : [{
				colHeader : i18n.lms1401s02["L140M01S.itemSeq"], //序號
				align : "center",
				width : 40, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'itemSeq' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M01S.custId"], //統一編號
				align : "left",
				width : 80, //設定寬度
				sortable : true, //是否允許排序
				formatter : 'click',
				onclick : newItemChildren1_13,
				name : 'custId' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M01S.dupNo"], //重覆序號
				align : "center",
				width : 30, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'dupNo' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M01S.custName"], //客戶名稱
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'custName' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M01S.curr"], //額度－幣別
				align : "center",
				width : 30, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'curr' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M01S.factAmt"], //額度－金額
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'factAmt' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M01S.custId2"], //共用統一編號
				align : "left",
				width : 80, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				name : 'custId2' //col.id		
			},{
				colHeader : i18n.lms1401s02["L140M01S.ratio"], //預支成數（%）
				align : "right",
				width : 50, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'ratio' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M01S.memo1"], //備註1
				align : "left",
				width : 140, //設定寬度
				//sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'memo1' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M01S.memo2"], //備註2
				align : "left",
				width : 140, //設定寬度
				//sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'memo2' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M01S.country"], //國別
				align : "center",
				width : 20, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				name : 'country' //col.id				
			},{
				colHeader :"　" , //i18n.lms1401s02["L140M01S.chkYN"] //輸入資料檢誤完成
				align : "center",
				width : 20, //設定寬度
				//sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'chkYN' //col.id		
			},{
				colHeader : "oid",
				name : 'oid',
				hidden : true //是否隱藏
			},{
				colHeader : i18n.lms1401s02["L140M01S.mainId"], //文件編號
				align : "left",
				width : 100, //設定寬度
				//sortable : true, //是否允許排序
				hidden : true, //是否隱藏
				name : 'mainId' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M01S.type"], //類型
				align : "left",
				width : 100, //設定寬度
				//sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				hidden : true, //是否隱藏
				name : 'type' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M01S.refRate"], //備註-匯率
				align : "left",
				width : 100, //設定寬度
				//sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				hidden : true, //是否隱藏
				name : 'refRate' //col.id		
			}],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewitemChildren_13").getRowData(rowid);
                newItemChildren1_13(null, null, data);
            },
            loadComplete: function(){
            	var numberOfRecords = $(this).jqGrid('getGridParam', 'records') ;
                if(numberOfRecords>0){
                	 $.ajax({
                		handler : "lms1401m01formhandler",
                		type : "POST",
                		dataType : "json",
                		action : "getARAppendixRefRate",
                		data : {
                			tabFormMainId : $("#tabFormMainId").val(),
                			type:"1"   //應收帳款買方額度資訊主檔
                		},
                		success : function(obj) {
                			
                			$("#tabs-c13").find("#refRate13_1").val(obj.refRate);
                			 
                		}
                	});	
                	
                }		        	  	
            }  
        });
    }// close gridviewitemChildren fn(x)

    /** 本案無追索買方於本行額度彙總grid  */
    function gridviewitemChildren3_13(){
    	$("#gridviewitemChildren3_13").iGrid({//
    		handler: 'lms1401gridhandler',
            height: 170,
            needPager: false,
            rownumbers: false,
            multiselect: true,
            hideMultiselect: false,
            sortname: 'itemSeq',
            sortorder: 'asc',
            shrinkToFit : false,
            postData: {
                formAction: "queryL140m02s",
                tabFormMainId: $("#tabFormMainId").val(),
                type: "B",
				noOpenDoc: true
            },
            autowidth: true,
            colModel : [{
				colHeader : i18n.lms1401s02["L140M02S.itemSeq"], //序號
				align : "center",
				width : 50, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'itemSeq' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M02S.custId"], //統一編號
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'custId' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M02S.dupNo"], //重覆序號
				align : "center",
				width : 50, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'dupNo' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M02S.custName"], //客戶名稱
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'custName' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M02S.imfAmtS"], //屬應收帳款承購無追索權買方額度- IMPORT FACTOR承購額度或保險承保額度(1)
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'imfAmtS' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M02S.imfAmtI"], //屬應收帳款承購無追索權買方額度- 經合格保險公司承保額度(2)
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'imfAmtI' //col.id		
			},{
				colHeader : i18n.lms1401s02["L140M02S.imfAmtN"], //屬應收帳款承購無追索權買方額度- 無IMPORT FACTOR承購額度或無保險承保額度(3)
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'imfAmtN' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M02S.imfAmtT"], //承購額度小計=(1)+(2)+(3)
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'imfAmtT' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M02S.imfAdjAmtT"], //調整後之承購額度(A)
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'imfAdjAmtT' //col.id		
			},{
				colHeader : i18n.lms1401s02["L140M02S.factAmtS"], //應收帳款債務人在本行之授信有效額度有-擔保(4)
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'factAmtS' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M02S.factAmtN"], //應收帳款債務人在本行之授信有效額度有-無擔保(5)
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'factAmtN' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M02S.factAmtT"], //應收帳款債務人在本行之授信有效額度有-小計(B)=(4)+(5)
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'factAmtT' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M02S.totalAmt"], //合   計(C)=(A)+(B) 
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'totalAmt' //col.id
			},{
				colHeader : "oid",
				name : 'oid',
				hidden : true //是否隱藏
			},{
				colHeader : i18n.lms1401s02["L140M02S.mainId"], //文件編號
				align : "left",
				width : 100, //設定寬度
//				sortable : true, //是否允許排序
				hidden : true, //是否隱藏
				name : 'mainId' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M02S.type"], //類型
				align : "left",
				width : 10, //設定寬度
				//sortable : true, //是否允許排序
				//formatter : 'click',
				hidden : true, //是否隱藏
				name : 'type' //col.id
			},{
				colHeader : i18n.lms1401s02["L140M02S.dataDate"], //資料日期
				align : "left",
				width : 10, //設定寬度
				//sortable : true, //是否允許排序
				//formatter : 'click',
				hidden : true, //是否隱藏
				name : 'dataDate' //col.id		
			},{
				colHeader : i18n.lms1401s02["L140M02S.refRate"], //備註-匯率
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				hidden : true, //是否隱藏
				name : 'refRate' //col.id		
			}],ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
//            	 var data = $("#gridviewitemChildren3_13").getRowData(rowid);
//                 newItemChildren3_13(null, null, data);
            },
            loadComplete: function(){
            	var numberOfRecords = $(this).jqGrid('getGridParam', 'records') ;
                if(numberOfRecords>0){
                	$("#tabs-c13_3").find("#dataDate13_3").val($(this).jqGrid('getCell', 1, 'dataDate'));
                	
                	$.ajax({
                		handler : "lms1401m01formhandler",
                		type : "POST",
                		dataType : "json",
                		action : "getARAppendixRefRate",
                		data : {
                			tabFormMainId : $("#tabFormMainId").val(),
                			type:"B"  //應收帳款買方額度加計其在本行授信額度之彙總
                		},
                		success : function(obj) {
                			
                			$("#tabs-c13_3").find("#refRate13_3").val(obj.refRate);
                			 
                		}
                	});	
                	
                }		        	  	
            }  
        });
    }//close gridviewitemChildren3 fn(x)
    //====================Grid Code End======================================
    
    //====================thickbox Code======================================
    function openNewItemChildrenBox1_13(data,isTotal){
    	var $L140M01SForm1 = $("#L140M01SForm1");
    	//isTotal = 合計
    	
    	if(isTotal == "Y"){
    		$L140M01SForm1.find("#custId").prop('disabled', true);
    		$L140M01SForm1.find("#dupNo").prop('disabled', true);
    		$L140M01SForm1.find("#custName").prop('disabled', true);
    		$L140M01SForm1.find("#btnApplyCustName13").hide();
    	}else{
    		$L140M01SForm1.find("#custId").prop('disabled', false);
    		$L140M01SForm1.find("#dupNo").prop('disabled', false);
    		$L140M01SForm1.find("#custName").prop('disabled', false);
    		$L140M01SForm1.find("#btnApplyCustName13").show();
    	}
    	
        $("#newItemChildrenBox1_13").thickbox({
            // L140S02Tab.13_01=本案無追索買方額度資訊
            title: i18n.lms1401s02["L140S02Tab.13_01"],
            width: 700,
            height: 600,
            readOnly: _openerLockDoc == "1" || inits.toreadOnly,
            i18n: i18n.def,
            modal: true,
            open: function(){
                //鎖定box
                $("#L140M01SForm1").readOnlyChilds(_openerLockDoc == "1" || inits.toreadOnly);
            },
            buttons: {
                "saveData": function(){
                	
                	
                	
                    if (!$L140M01SForm1.valid()) {
                        return false;
                    }
                    
                    
                    var custId2 = $L140M01SForm1.find("#custId2").val();
                	var dupNo2 = $L140M01SForm1.find("#dupNo2").val();
                	
                	if(custId2 || dupNo2){
                		if(!custId2 || !dupNo2 ){
                			return CommonAPI.showMessage(i18n.lms1401s02["L140M01a.error18"] + i18n.lms1401s02["L140M01S.custId2"]+"/"+i18n.lms1401s02["L140M01S.dupNo"]);
                    		//return;
                		}
                	}
                	
                	//非合計時，預支成數（%）要有值
                	if(isTotal != "Y"){
                		//檢核預支成數（%）ratio要有值
                		var ratio = $L140M01SForm1.find("#ratio").val();
                		if(!ratio){
                			return CommonAPI.showMessage(i18n.lms1401s02["L140M01a.error18"] + i18n.lms1401s02["L140M01S.ratio"]);
                    		//return;
                		}
                	}
                 
                    
                    FormAction.open = true;
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "saveL140m01s",
                            type: "1",
                            tabFormMainId: $("#tabFormMainId").val() ,
                            l140m01sOid: data && data.oid,
                            L140M01SForm1: JSON.stringify($("#L140M01SForm1").serializeData())
                        },
                        success: function(obj){
                        	$("#L140M01SForm1").find("#itemSeq").val(obj.itemSeq);
                            FormAction.open = false;
                            $.thickbox.close();
                            $("#gridviewitemChildren_13").trigger("reloadGrid");
                        }
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    function newItemChildren1_13(cellvalue, type, data){
        $("#L140M01SForm1").reset();
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "queryL140m01s",
                l140m01sOid: data && data.oid,
                tabFormMainId: $("#tabFormMainId").val(),
				noOpenDoc: true
            },
            success: function(obj){
                
                $("#L140M01SForm1").injectData(obj);
                openNewItemChildrenBox1_13(data,obj.isTotal);
            }
        });
        
        
    }

     
    //====================thickbox Code End======================================	

});

/**
 * 引進戶名
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function applyCustName13() {
	var $L140M01SForm1 = $("#L140M01SForm1");
	
	var custId = $L140M01SForm1.find("#custId").val();
	var dupNo = $L140M01SForm1.find("#dupNo").val();
	
	if(!custId ){
		return CommonAPI.showMessage(i18n.lms1401s02["L140M01a.error18"] + i18n.lms1401s02["L140M01S.custId"]);
		//return;
	}
	
	if(!dupNo){
		return CommonAPI.showMessage(i18n.lms1401s02["L140M01a.error18"] + i18n.lms1401s02["L140M01S.dupNo"]);
		//return;
	}
	
	$.ajax({
		handler : "lms1401m01formhandler",
		type : "POST",
		dataType : "json",
		action : "applyCustName13",
		data : {
			tabFormMainId : $("#tabFormMainId").val(),
			custId : custId,
			dupNo : dupNo
		},
		success : function(obj) {
			
			if(obj.newCustName){
				$L140M01SForm1.find("#custName").val(obj.newCustName);
			}
			
			 
		}
	});	
}



/**
 * 匯入EXCEL名單
 */
function importExcel13_1() {
	
	var type = "1";
	
	$("#uploadFileAml").val('');
    var fileSize = 5 * 1024 * 1024;
    var s = $.extend({
        handler: 'lms1401fileuploadhandler',
        fieldId: "uploadFileAml",
        title: i18n && i18n.def.insertfile || "請選擇附加檔案",
        fileCheck: ['xls'],
        successMsg: false,
        success: function(){
        },
        data: {
            fileSize: fileSize,
            tabFormMainId : $("#tabFormMainId").val(),
			deleteDup : true,
            changeUploadName: "Lms1401s02panel13_01.xls"
        }
    }, s);

	$("#loginImportByExl13_1").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lms1401s02["L140M01S.importByExcel"],//匯入EXCEL名單
		width : 600,
		height : 250,
		modal : true,
		i18n:i18n.def,
		buttons: (function(){
            var b = {};
            b[i18n.lms1401s02["L140M01S.upLoad"]] = function(){
            	var count=$("#gridviewitemChildren_13").jqGrid('getGridParam','records');
            	if(count > 0){
    				//"L140M01a.message196=執行引進後會刪除已存在之名單，是否確定執行？
    				CommonAPI.confirmMessage(i18n.lms1401s02["L140M01a.message196"], function(b){
    					if (b) {					
    						//是的function
    		                $.capFileUpload({
    		                    handler: s.handler,
    		                    fileCheck: s.fileCheck,
    		                    fileElementId: s.fieldId,
    		                    successMsg: s.successMsg,
    		                    data: $.extend({
    			                    type: type
    			                }, s.data || {}),
    		                    success: function(json){
    		                        $.thickbox.close();
    		                        API.showPopMessage(i18n.lms1401s02["L140M01S.importByExcel"]+i18n.lms1401s02["L140M01S.success"]);
    		                        $("#gridviewitemChildren_13").trigger("reloadGrid");
    		                    }
    		                });
    					}				
    				});		
    			}else{
    				 
                    $.capFileUpload({
                        handler: s.handler,
                        fileCheck: s.fileCheck,
                        fileElementId: s.fieldId,
                        successMsg: s.successMsg,
                        data: $.extend({
                        	type: type
    	                }, s.data || {}),
                        success: function(json){
                            $.thickbox.close();
                            API.showPopMessage(i18n.lms1401s02["L140M01S.importByExcel"]+i18n.lms1401s02["L140M01S.success"]);
                            $("#gridviewitemChildren_13").trigger("reloadGrid");
                        }
                    });	
    			}
            	
				
            };
            b[i18n && i18n.def.cancel || "取消"] = function(){
                $.thickbox.close();
            };
            return b;
        })()
	});
}



/**
 * 本案無追索買方額度資訊合計
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function countTotal13_1() {
	$.ajax({
		handler : "lms1401m01formhandler",
		type : "POST",
		dataType : "json",
		action : "countTotalL140m01s",
		data : {
			tabFormMainId : $("#tabFormMainId").val(),
			type:"1"
		},
		success : function(obj) {
			
			$("#gridviewitemChildren_13").trigger("reloadGrid");
			 
		}
	});	
}


/**
 * 本案無追索買方額度資訊合計
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function applyARAppendix13(type) {
	$.ajax({
		handler : "lms1401m01formhandler",
		type : "POST",
		dataType : "json",
		action : "applyARAppendix13",
		data : {
			tabFormMainId : $("#tabFormMainId").val(),
			type : type
		},
		success : function(obj) {
			
			$("#gridviewitemChildren3_13").trigger("reloadGrid");
			 
		}
	});	
}

