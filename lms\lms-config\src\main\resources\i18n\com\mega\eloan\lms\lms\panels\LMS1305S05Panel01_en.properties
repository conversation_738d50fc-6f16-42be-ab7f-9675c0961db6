#==================================================
# \u7c3d\u5831\u66f8(\u5176\u4ed6/\u9673\u5fa9\u3001\u8ff0)\u8aaa\u660e\u4e4b\u903e\u50ac\u5446\u5831\u544a\u8868\u8cc7\u6599\u5b50\u9801\u7c64
# Lenged \u540d\u7a31
#==================================================
lms1305s05.legend1=Overdue/Collection/Bad Loan Report
#==================================================
# \u7c3d\u5831\u66f8(\u5176\u4ed6/\u9673\u5fa9\u3001\u8ff0)\u8aaa\u660e\u4e4b\u903e\u50ac\u5446\u5831\u544a\u8868\u8cc7\u6599\u5b50\u9801\u7c64
# \u5176\u4ed6\u7d30\u90e8\u8cc7\u6599\u540d\u7a31
#==================================================
lms1305s05.other1=[Description] For overdue and hardcore collection cases, you may use this function to attach the "Overdue/Collection/Bad Loan Report" and send to the reviewing unit.
#==================================================
# \u7c3d\u5831\u66f8(\u5176\u4ed6/\u9673\u5fa9\u3001\u8ff0)\u8aaa\u660e\u4e4b\u903e\u50ac\u5446\u5831\u544a\u8868\u8cc7\u6599\u5b50\u9801\u7c64
# \u6309\u9215\u540d\u7a31
#==================================================
lms1305s05.bt01=Select Files To Attach
lms1305s05.bt02=Delete


