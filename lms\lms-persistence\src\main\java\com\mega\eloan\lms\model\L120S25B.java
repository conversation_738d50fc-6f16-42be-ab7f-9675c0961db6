/* 
 * L120S25B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 會計科目CCF對照檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S25B")
public class L120S25B extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 會計科目代碼 **/
	@Id
	@Size(max = 8)
	@Column(name = "ACKEY", length = 8, columnDefinition = "CHAR(8)")
	private String acKey;

	/** 會計科目名稱 **/
	@Size(max = 100)
	@Column(name = "ACNM", length = 100, columnDefinition = "VARCHAR(100)")
	private String acNm;

	/** 業務類別 **/
	@Size(max = 1)
	@Column(name = "BITYPE", length = 1, columnDefinition = "CHAR(1)")
	private String biType;

	/** 業務名稱 **/
	@Size(max = 45)
	@Column(name = "BINM", length = 45, columnDefinition = "VARCHAR(45)")
	private String biNm;

	/** 風險權數 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "RW", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal rw;

	/** 風險係數 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "CCF", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal ccf;

	/** RTYPE **/
	@Size(max = 1)
	@Column(name = "RTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String rType;

	/** DW_LST_DATA_SRC **/
	@Size(max = 30)
	@Column(name = "DW_LST_DATA_SRC", length = 30, columnDefinition = "VARCHAR(30)")
	private String dw_Lst_Data_Src;

	/** DW_DATA_SRC_DT **/
	@Size(max = 10)
	@Column(name = "DS_DATA_SRC_DT", length = 10, columnDefinition = "CHAR(10)")
	private String Ds_Data_Src_Dt;

	/** DW_LST_MNT_DT **/
	@Size(max = 10)
	@Column(name = "DW_LST_MNT_DT", length = 10, columnDefinition = "CHAR(10)")
	private String Dw_Lst_Mnt_Dt;

	/** 取得會計科目代碼 **/
	public String getAcKey() {
		return this.acKey;
	}

	/** 設定會計科目代碼 **/
	public void setAcKey(String value) {
		this.acKey = value;
	}

	/** 取得會計科目名稱 **/
	public String getAcNm() {
		return this.acNm;
	}

	/** 設定會計科目名稱 **/
	public void setAcNm(String value) {
		this.acNm = value;
	}

	/** 取得業務類別 **/
	public String getBiType() {
		return this.biType;
	}

	/** 設定業務類別 **/
	public void setBiType(String value) {
		this.biType = value;
	}

	/** 取得業務名稱 **/
	public String getBiNm() {
		return this.biNm;
	}

	/** 設定業務名稱 **/
	public void setBiNm(String value) {
		this.biNm = value;
	}

	/** 取得風險權數 **/
	public BigDecimal getRw() {
		return this.rw;
	}

	/** 設定風險權數 **/
	public void setRw(BigDecimal value) {
		this.rw = value;
	}

	/** 取得風險係數 **/
	public BigDecimal getCcf() {
		return this.ccf;
	}

	/** 設定風險係數 **/
	public void setCcf(BigDecimal value) {
		this.ccf = value;
	}

	/** 取得RTYPE **/
	public String getRType() {
		return this.rType;
	}

	/** 設定RTYPE **/
	public void setRType(String value) {
		this.rType = value;
	}

	/** 取得DW_LST_DATA_SRC **/
	public String getDw_Lst_Data_Src() {
		return this.dw_Lst_Data_Src;
	}

	/** 設定DW_LST_DATA_SRC **/
	public void setDw_Lst_Data_Src(String value) {
		this.dw_Lst_Data_Src = value;
	}

	/** 取得DW_DATA_SRC_DT **/
	public String getDs_Data_Src_Dt() {
		return this.Ds_Data_Src_Dt;
	}

	/** 設定DW_DATA_SRC_DT **/
	public void setDs_Data_Src_Dt(String value) {
		this.Ds_Data_Src_Dt = value;
	}

	/** 取得DW_LST_MNT_DT **/
	public String getDw_Lst_Mnt_Dt() {
		return this.Dw_Lst_Mnt_Dt;
	}

	/** 設定DW_LST_MNT_DT **/
	public void setDw_Lst_Mnt_Dt(String value) {
		this.Dw_Lst_Mnt_Dt = value;
	}
}
