package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.lms.panels.LMSM01Panel;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 綜合評估/往來彙總  - 綜合評估及敘做理由
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lmss07A/{page}")
public class LMSS07Page01 extends AbstractEloanForm {

	@Override
	public void afterExecute(ModelMap model, PageParameters parameters) {
		super.afterExecute(parameters);
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// remove("_headerPanel");
		model.addAttribute("showHeader", false); // 不顯示 _headerPanel

	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		// 共用借款人引進界面
		Panel panel = new LMSM01Panel("lmsm01_panel");
		panel.processPanelData(model, params);

		// UPGRADE: 前端須配合改Thymeleaf的樣式
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if ("0C3".equals(user.getUnitNo())) {
			model.addAttribute("HKMAText_fragmentArea", "HKMAText_fragmentArea_show");
		} else {
			model.addAttribute("HKMAText_fragmentArea", "");
		}
	}

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
}
