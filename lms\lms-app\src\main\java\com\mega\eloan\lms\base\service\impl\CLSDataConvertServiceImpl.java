package com.mega.eloan.lms.base.service.impl;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.MessageFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import tw.com.iisi.cap.context.CapParameter;
import tw.com.iisi.cap.service.ICapService;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.PropUtil;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.EloanJdbcTemplate;
import com.mega.eloan.lms.base.service.CLSDataConvertService;
import com.mega.eloan.lms.dc.action.RowData;
import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.ConfigData;
import com.mega.eloan.lms.dc.conf.MainConfig;
import com.mega.eloan.lms.dc.main.OnlineDCHandler;

/**
 * <pre>
 * 個金單筆資料轉換
 * </pre>
 * 
 * @since 2013/5/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/5/11, 007625 ,new
 *          </ul>
 */

@Service("clsdataconvertservice")
public class CLSDataConvertServiceImpl implements CLSDataConvertService,
		ICapService {

	private EloanJdbcTemplate jdbc;

	@Resource(name = "eLoanSqlStatement")
	CapParameter lmsSQL;

	@Resource(name = "lms-db")
	public void setDataSource(DataSource dataSource) {
		jdbc = new EloanJdbcTemplate(dataSource, GWException.GWTYPE_ELDB);
		jdbc.setSqlProvider(lmsSQL);
		jdbc.setCauseClass(this.getClass());
	}

	/**
	 * get the the jdbc
	 * 
	 * @return the jdbc
	 */
	public EloanJdbcTemplate getJdbc() {
		return jdbc;
	}

	private static final String PROCESS_NAME = "CLSDataConvertService";

	private static Logger logger = LoggerFactory
			.getLogger(CLSDataConvertServiceImpl.class);

	@Override
	public synchronized String doDataConvert(String nsfName,
			List<String> viewList, String mainId, String ip, String schema,
			String dbType, String brNo, String... l120m01aMainIds) {

		long startTime = System.currentTimeMillis();

		String result = "";

		ConfigData config = null;
		File stateFile = new File("/elnfs/LMS/lmsdc/" + mainId + ".clsState");

		try {
			if (doLockFile()) {

				FileUtils.writeStringToFile(stateFile, "R");

				OnlineDCHandler onlineDCHandler = new OnlineDCHandler();
				List<RowData> dcSqls = onlineDCHandler.runAll(nsfName,
						viewList, mainId, ip, schema, dbType, brNo);

				config = MainConfig.getInstance().getConfig();

				parseSQLtoClean(dcSqls, mainId);

				FileUtils.writeLines(
						new File("/elnfs/LMS/lmsdc/clsDataConvertest_" + mainId
								+ ".sql"), "BIG5", dcSqls);

				List<String> delSqls = new ArrayList<String>();

				int allSqlCount = dcSqls != null ? dcSqls.size() : 0;
				int count = 0;
				for (RowData rowData : dcSqls) {
					count++;

					String sql = rowData.getSql();

					String deleteSql = this.genDeleteSql(sql);
					delSqls.add(deleteSql);
					try {

						logger.info("SQL筆數處理進度：" + count + "/" + allSqlCount);
						if (rowData.isClobTb()) {
							String pSql = rowData.getSql();
							this.update(pSql, rowData.getClobString());
						} else {
							this.update(sql);
						}
					} catch (Exception e) {
						String exception = e.toString().toLowerCase()
								.replaceAll("\r", "").replaceAll("\n", "");

						if (exception.indexOf("state=23505") >= 0) {
							// String key = "LMS.";
							// String key2 = " VALUES(";
							//
							// String tableName = sql.substring(
							// sql.indexOf(key), sql.indexOf(key2));
							// if
							// (!"LMS.L120M01F".equalsIgnoreCase(tableName)
							// &&
							// !"LMS.L140M01C".equalsIgnoreCase(tableName))
							// {
							// throw e;
							// }
						} else {
							throw e;
						}
					}
				}

				// 舊批號，新批號
				String newMainId = "T" + mainId.substring(1, mainId.length());

				afterToDoMergeInto(mainId, newMainId);
				deleteDuplicateData(mainId, newMainId);
				doGutpercentUpdate(mainId, newMainId);

				FileUtils.writeLines(new File(
						"/elnfs/LMS/lmsdc/clsDataConvertestDelete_" + mainId
								+ ".sql"), "BIG5", delSqls);

				result = "Y";
			} else {
				result = "N";
			}

			File sourceDir = new File(config.getClsloadDB2DirPath(),
					config.getFilesPath());
			// String sourceDir = "/elnfs/LMS/lmsdc/load_db2/" + mainId
			// + "/LMS/FILES/LMS" ;
			File destDir = new File(PropUtil.getProperty("docFile.dir"));
			// String destDir = "/elnfs/LMS";

			// 附件檔案複製
			if (sourceDir.exists()) {
				FileUtils.copyDirectory(sourceDir, destDir);
			}

			// FileUtils.deleteDirectory(sourceDir);

		} catch (DCException e) {
			logger.error("CLSDataConvertServiceImpl Exception : ", e);
			result = e.toString();
		} catch (Exception ioe) {
			logger.error("CLSDataConvertServiceImpl IO Exception : ", ioe);
			result = ioe.toString();
		} finally {
			try {
				if (stateFile.exists()) {
					FileUtils.writeStringToFile(stateFile, result);
				}
			} catch (IOException e) {
				logger.error("Exception", e);
			}
			doUnLockFile();

		}

		long endTime = System.currentTimeMillis();

		logger.info("---------------------------轉檔作業執行結束，花費時間為："
				+ (endTime - startTime) / 1000 + "秒---------------------------");
		return result;
	}

	private String genDeleteSql(String sql) {

		String text = sql;
		int startPosition = text.indexOf("VALUES('") + "VALUES('".length();

		// System.out.println(text.indexOf("VALUES('")+ "VALUES('".length());

		String oid = text.substring(startPosition, startPosition + 32);
		// System.out.println(text.substring(startPosition , startPosition +
		// 32));

		String key = "LMS.";
		String key2 = " VALUES(";

		String tableName = text
				.substring(text.indexOf(key), text.indexOf(key2));
		// System.out.println(text.substring(text.indexOf(key),
		// text.indexOf(key2)));

		StringBuffer deleteSql = new StringBuffer();

		deleteSql.append("DELETE FROM " + tableName + " WHERE OID = '" + oid
				+ "';");
		return deleteSql.toString();

	}

	@Override
	public boolean checkIsOK() {
		String lockFile = PropUtil.getProperty("docFile.dir") + "/"
				+ PROCESS_NAME + ".lock";
		File file = new File(lockFile);
		return !file.exists();
	}

	@Override
	public synchronized boolean doLockFile() {
		return lockSingletonProgramFile(PROCESS_NAME);
	}

	@Override
	public synchronized boolean doUnLockFile() {
		return unLockSingletonProgramFile(PROCESS_NAME);
	}

	private boolean lockSingletonProgramFile(String programName) {

		final String startFailureMessage = "Error:start " + programName
				+ " application";
		String lockFile = PropUtil.getProperty("docFile.dir") + "/"
				+ programName + ".lock";

		logger.info("start " + programName + " application with [lockFile] : "
				+ lockFile);

		FileWriter writer = null;
		try {
			File file = new File(lockFile);
			if (!file.exists()) {
				String parent = file.getParent();
				File folder = new File(parent);
				if (!folder.exists() || !folder.isDirectory()) {
					if (!folder.mkdirs()) {
						logger.error(startFailureMessage
								+ " failure: create lock file folder failure:"
								+ parent);
					}
				}
				if (!file.createNewFile()) {
					logger.error(startFailureMessage
							+ " failure: create lock file failure:" + lockFile);
				}

				writer = new FileWriter(file);
				writer.write(programName);
				writer.flush();
				writer.close();
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			logger.error(startFailureMessage + " failure: lock file is ["
					+ lockFile + "]:" + e.getMessage(), e);
			try {

				if (null != writer) {
					writer.close();
				}
			} catch (Exception ex) {
				logger.error(
						"Error: close resource failure:" + ex.getMessage(), ex);
			}
			logger.debug("There is a "
					+ programName
					+ " application process in system processes. Now exit starting!");

			return false;

		}
	}

	private boolean unLockSingletonProgramFile(String programName) {

		final String startFailureMessage = "Error:start " + programName
				+ " application";
		String lockFile = PropUtil.getProperty("docFile.dir") + "/"
				+ programName + ".lock";
		logger.debug("start " + programName
				+ " application with [delete lockFile] : " + lockFile);

		try {
			File file = new File(lockFile);

			if (file.exists()) {
				logger.debug(file.toString() + " delete : " + file.delete()
						+ "");
			}

			return true;

		} catch (Exception e) {
			logger.debug(startFailureMessage + " failure: lock file is ["
					+ lockFile + "]:" + e.getMessage(), e);
			try {

				return false;

			} catch (Exception ex) {
				logger.error(
						"Error: close resource failure:" + ex.getMessage(), ex);
			}
			logger.error("There is a "
					+ programName
					+ " application process in system processes. Now exit starting!");

			return false;

		}
	}

	private boolean checkIsL120M01C(String sql) {
		return sql.indexOf("LMS.L120M01C") >= 0;
	}

	private boolean checkIsC101M01A(String sql) {
		return sql.indexOf("LMS.C101M01A") >= 0;
	}

	private boolean checkIsC102M01A(String sql) {
		return sql.indexOf("LMS.C102M01A") >= 0;
	}

	private boolean checkIsFCLS715M01_L120M01C(String sql) {
		return sql.indexOf("LMS.FCLS715M01_L120M01C") >= 0;
	}

	private boolean checkIsFCLS115M01_L120M01C(String sql) {
		return sql.indexOf("LMS.FCLS115M01_L120M01C") >= 0;
	}

	private String[] getCustIdDatas(String sql) {
		// INSERT INTO LMS.C101M01A
		// VALUES('792E6991DE8CE45C48257B89002A3CC9','792E6991DE8CE45C48257B89002A3CC9','999','A121542031','0','莊永敬','1','','','','','',null,'','','','','Y','N','2012-06-20','N05228',timestamp('2013-06-24-09.32.27.274000'),'N05228',timestamp('2013-06-24-09.32.27.274000'))
		String pattern = "INSERT INTO LMS.C101M01A VALUES({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17},{18},{19},{20},timestamp({21}),{22},timestamp({23}))";

		Object[] values2 = null;
		String custId = "";
		String dupNo = "";
		String ownBrId = "";
		try {
			// sql =
			// "INSERT INTO LMS.L120M01C VALUES('6001D28CDA5041A29D6C50E8DECC140B','C546670CDA1FE49948257A0C0011C909','1','C546670CDA1FE4994825019110100081','N05228',timestamp('2013-06-20-15.00.00.400000'),'N05228',timestamp('2013-06-20-15.00.00.401000'))"
			values2 = new MessageFormat(pattern).parse(sql);
		} catch (ParseException e) {
			logger.error("parseException", e);
		}

		ownBrId = CapString.trimNull(values2[2]);
		ownBrId = StringUtils.substring(ownBrId, 1, ownBrId.length() - 1);
		custId = CapString.trimNull(values2[3]);
		custId = StringUtils.substring(custId, 1, custId.length() - 1);
		dupNo = CapString.trimNull(values2[4]);
		dupNo = StringUtils.substring(dupNo, 1, dupNo.length() - 1);

		return new String[] { custId, dupNo, ownBrId };

	}

	private String getC102M01A_MainId(String sql) {

		// INSERT INTO LMS.C102M01A
		// VALUES('47F8959D395D4F60AC37ABA13E5B9CF1','C546670CDA1FE49948257A0C0011C909','C546670CDA1FE49948257A0C0011C909','1','A121542031','0','莊永敬','1','999','030','','','','N05228',timestamp('2013-06-24-09.32.27.843000'),'N05228',timestamp('2013-06-24-09.32.27.843000'),'',null,'',null,'','',null,'','Y','Y','N','Y','N','2','')
		String pattern = "INSERT INTO LMS.C102M01A VALUES({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17},{18},{19},{20},{21},{22},{23},{24},{25},{26},{27},{28},{29},{30},{31})";
		Object[] values2 = null;
		String mainId = "";
		try {
			// sql =
			// "INSERT INTO LMS.L120M01C VALUES('6001D28CDA5041A29D6C50E8DECC140B','C546670CDA1FE49948257A0C0011C909','1','C546670CDA1FE4994825019110100081','N05228',timestamp('2013-06-20-15.00.00.400000'),'N05228',timestamp('2013-06-20-15.00.00.401000'))"
			values2 = new MessageFormat(pattern).parse(sql);
		} catch (ParseException e) {
			logger.error("parseException", e);
		}
		mainId = CapString.trimNull(values2[2]);
		mainId = StringUtils.substring(mainId, 1, mainId.length() - 1);

		return mainId;

	}

	private String[] getMainIdAndRefMainId(String sql) {
		String pattern = "INSERT INTO LMS.L120M01C VALUES({0},{1},{2},{3},{4},timestamp({5}),{6},timestamp({7}))";

		Object[] values2 = null;
		String mainid = "";
		String refmainid = "";
		try {
			// sql =
			// "INSERT INTO LMS.L120M01C VALUES('6001D28CDA5041A29D6C50E8DECC140B','C546670CDA1FE49948257A0C0011C909','1','C546670CDA1FE4994825019110100081','N05228',timestamp('2013-06-20-15.00.00.400000'),'N05228',timestamp('2013-06-20-15.00.00.401000'))"
			values2 = new MessageFormat(pattern).parse(sql);
		} catch (ParseException e) {
			logger.error("parseException", e);
		}

		mainid = CapString.trimNull(values2[1]);
		mainid = StringUtils.substring(mainid, 1, mainid.length() - 1);

		refmainid = CapString.trimNull(values2[3]);
		refmainid = StringUtils.substring(refmainid, 1, refmainid.length() - 1);

		return new String[] { mainid, refmainid };

	}

	private String[] getFCLS115M01_MainIdAndRefMainId(String sql) {
		String pattern = "INSERT INTO LMS.FCLS115M01_L120M01C VALUES({0},{1},{2},{3},{4},timestamp({5}),{6},timestamp({7}))";

		Object[] values2 = null;
		String mainid = "";
		String refmainid = "";
		try {
			// sql =
			// "INSERT INTO LMS.L120M01C VALUES('6001D28CDA5041A29D6C50E8DECC140B','C546670CDA1FE49948257A0C0011C909','1','C546670CDA1FE4994825019110100081','N05228',timestamp('2013-06-20-15.00.00.400000'),'N05228',timestamp('2013-06-20-15.00.00.401000'))"
			values2 = new MessageFormat(pattern).parse(sql);
		} catch (ParseException e) {
			logger.error("parseException", e);
		}

		mainid = CapString.trimNull(values2[1]);
		mainid = StringUtils.substring(mainid, 1, mainid.length() - 1);

		refmainid = CapString.trimNull(values2[3]);
		refmainid = StringUtils.substring(refmainid, 1, refmainid.length() - 1);

		return new String[] { mainid, refmainid };

	}

	private String[] getFCLS715M01_MainIdAndRefMainId(String sql) {
		String pattern = "INSERT INTO LMS.FCLS715M01_L120M01C VALUES({0},{1},{2},{3},{4},timestamp({5}),{6},timestamp({7}))";

		Object[] values2 = null;
		String mainid = "";
		String refmainid = "";
		try {
			// sql =
			// "INSERT INTO LMS.L120M01C VALUES('6001D28CDA5041A29D6C50E8DECC140B','C546670CDA1FE49948257A0C0011C909','1','C546670CDA1FE4994825019110100081','N05228',timestamp('2013-06-20-15.00.00.400000'),'N05228',timestamp('2013-06-20-15.00.00.401000'))"
			values2 = new MessageFormat(pattern).parse(sql);
		} catch (ParseException e) {
			logger.error("parseException", e);
		}

		mainid = CapString.trimNull(values2[1]);
		mainid = StringUtils.substring(mainid, 1, mainid.length() - 1);

		refmainid = CapString.trimNull(values2[3]);
		refmainid = StringUtils.substring(refmainid, 1, refmainid.length() - 1);

		return new String[] { mainid, refmainid };

	}

	private void parseSQLtoClean(List<RowData> sqlDatas, String notesUp) {
		for (RowData sqlData : sqlDatas) {
			String sql = sqlData.getSql();
			if (checkIsL120M01C(sql)) {
				String[] mainRefMaind = getMainIdAndRefMainId(sql);

				String mainId = mainRefMaind[0];
				String refMainId = mainRefMaind[1];

				/*
				 * 將已存在的資料從資料庫中刪除
				 */
				clearData(mainId, refMainId);

			} else if (checkIsC101M01A(sql)) {
				String[] custIdDatas = getCustIdDatas(sql);
				String custId = custIdDatas[0];
				String dupNo = custIdDatas[1];
				String ownBrId = custIdDatas[2];

				clearCustData(ownBrId, custId, dupNo);

			} else if (checkIsC102M01A(sql)) {
				String c102m01a_mainId = getC102M01A_MainId(sql);
				clearC102M01AData(c102m01a_mainId);
			} else if (checkIsFCLS115M01_L120M01C(sql)) {
				String[] mainRefMaind = getFCLS115M01_MainIdAndRefMainId(sql);
				String mainId = mainRefMaind[0];
				String refMainId = mainRefMaind[1];

				/*
				 * 將已存在的資料從資料庫中刪除
				 */
				clearData(mainId, refMainId);

			} else if (checkIsFCLS715M01_L120M01C(sql)) {
				String[] mainRefMaind = getFCLS715M01_MainIdAndRefMainId(sql);
				String mainId = mainRefMaind[0];
				String refMainId = mainRefMaind[1];

				/*
				 * 將已存在的資料從資料庫中刪除
				 */
				clearData(mainId, refMainId);
			}

		}

		cleanTempData();
	}

	private void clearC102M01AData(String mainId) {
		String sql = lmsSQL.getValue("dcSql3");

		this.update(MessageFormat.format(sql, new Object[] { "LMS.C102M01A" }),
				mainId);
	}

	private void clearCustData(String ownBrid, String custId, String dupNo) {

		String sql = "SELECT * FROM LMS.C101M01A WHERE CUSTID = ? AND DUPNO = ? AND OWNBRID = ?";

		Map<String, Object> custData = this.getJdbc().queryForMap(sql,
				new Object[] { custId, dupNo, ownBrid });

		if (!CollectionUtils.isEmpty(custData)) {
			String mainId = (String) custData.get("mainid");
			sql = "SELECT * FROM LMS.C101S01E WHERE CUSTID = ? AND DUPNO = ? AND MAINID = ? ";
			Map<String, Object> c101s01e = this.getJdbc().queryForMap(sql,
					new Object[] { custId, dupNo, mainId });

			if (CollectionUtils.isEmpty(c101s01e)) {

				sql = lmsSQL.getValue("dcSql3");

				this.update(MessageFormat.format(sql,
						new Object[] { "LMS.C101M01A" }), mainId);
				this.update(MessageFormat.format(sql,
						new Object[] { "LMS.C101S01A" }), mainId);
				this.update(MessageFormat.format(sql,
						new Object[] { "LMS.C101S01B" }), mainId);
				this.update(MessageFormat.format(sql,
						new Object[] { "LMS.C101S01C" }), mainId);

			}

		}

	}

	private void cleanTempData() {
		this.getJdbc().update("DELETE FROM LMS.FCLS115M01_L120M01C", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS115M01_L140M01A", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS115M01_L140M03A", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS115M01_L140M01B", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS115M01_L140M01L", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS115M01_L140S02A", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS715M01_L120M01C", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS715M01_L140M01A", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS715M01_L140M03A", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS715M01_L140M01B", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS715M01_L140M01L", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS715M01_L140S02A", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS114M01_L120M01A", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS114M01_L120M01F", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS114M02_L120M01A", null);
		this.getJdbc().update("DELETE FROM LMS.FCLS114M02_L120M01F", null);
	}

	private void clearData(String mainId, String refMainId) {
		// DELETE FROM {0} WHERE MAINID IN (?)

		String sql = lmsSQL.getValue("dcSql3");

		this.update(MessageFormat.format(sql, new Object[] { "LMS.BDOCFILE" }),
				mainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.BDOCFILE" }),
				refMainId);

		/**
		 * C101M01A C101s01a c101s01b c101s01c c101s01d
		 * 
		 * 
		 */
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L120M01A" }),
				mainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L120M01C" }),
				mainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L120M01F" }),
				mainId);

		this.update(MessageFormat.format(sql, new Object[] { "LMS.C120M01A" }),
				mainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.C120S01A" }),
				mainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.C120S01B" }),
				mainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.C120S01C" }),
				mainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.C120S01D" }),
				mainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.C120S01E" }),
				mainId);

		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140M01A" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140M01B" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140M01L" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140M01M" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140M01O" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140M03A" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140S01A" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140S02A" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140S02B" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140S02C" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140S02D" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140S02E" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140S02F" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140S02G" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140S02H" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140S02I" }),
				refMainId);
		this.update(MessageFormat.format(sql, new Object[] { "LMS.L140S02J" }),
				refMainId);

	}

	/**
	 * 單筆轉檔完之後，要執行下列Merge into 語法
	 * 
	 * @param notesUp
	 */
	private void afterToDoMergeInto(String notesUp, String newNotesUp) {

		// String newNotesUp = "T" + notesUp.substring(1, notesUp.length());

		logger.info("---------------------------do meger SQL1-------------------------");
		this.update("dcClsSql1",
				new Object[] { notesUp, newNotesUp, newNotesUp });
		logger.info("---------------------------do meger SQL2-------------------------");
		this.update("dcClsSql2",
				new Object[] { notesUp, newNotesUp, newNotesUp });
		logger.info("---------------------------do meger SQL3-------------------------");
		this.update("dcClsSql3",
				new Object[] { notesUp, newNotesUp, newNotesUp });
		logger.info("---------------------------do meger SQL4-------------------------");
		this.update("dcClsSql4",
				new Object[] { notesUp, newNotesUp, newNotesUp });
		logger.info("---------------------------do meger SQL5-------------------------");
		this.update("dcClsSql5",
				new Object[] { notesUp, newNotesUp, newNotesUp });
		logger.info("---------------------------do meger SQL6-------------------------");
		this.update("dcClsSql6", new Object[] { notesUp, newNotesUp });
		logger.info("---------------------------do meger SQL7-------------------------");
		this.update("dcClsSql7", new Object[] { notesUp, newNotesUp });
		logger.info("---------------------------do meger SQL8-------------------------");
		this.update("dcClsSql8", new Object[] { notesUp, newNotesUp });
		logger.info("---------------------------do meger SQL9-------------------------");
		this.update("dcClsSql9", new Object[] { notesUp, newNotesUp });
		logger.info("---------------------------do meger SQL10-------------------------");
		this.update("dcClsSql10", new Object[] { notesUp, newNotesUp,
				newNotesUp });
		logger.info("---------------------------do meger SQL11-------------------------");
		this.update("dcClsSql11", new Object[] { notesUp, newNotesUp,
				newNotesUp });
		logger.info("---------------------------do meger SQL12-------------------------");
		this.update("dcClsSql12", new Object[] { notesUp, newNotesUp });
		logger.info("---------------------------do meger SQL13-------------------------");
		this.update("dcClsSql13", new Object[] { notesUp, newNotesUp });
		logger.info("---------------------------do meger SQL14-------------------------");
		this.update("dcClsSql14", new Object[] { notesUp, newNotesUp });
		logger.info("---------------------------do meger SQL15-------------------------");
		this.update("dcClsSql15", new Object[] { notesUp, newNotesUp });
		logger.info("---------------------------do meger SQL16-------------------------");
		this.update("dcClsSql16", new Object[] { notesUp, newNotesUp,
				newNotesUp });

	}
	
	
	/**
	 * 更新轉檔資料定義不同問題
	 * @param notesUp
	 * @param newNotesUp
	 */
	private void doGutpercentUpdate(String notesUp, String newNotesUp) {
		logger.info("---------------------------doGutpercentUpdate-------------------------");
		String sql = "update lms.l140m01a "
				+ " set gutpercent=gutpercent*10 "
				+ " where gutpercent>0 and notesup in (?,?) and docurl='CLS' "
				+ " and gutpercent<10 and mainid in (select mainid from lms.l140s02a where prodkind in('58','60')) ";
		this.getJdbc().update(sql, new Object[] { notesUp, newNotesUp });
	}

	private void deleteDuplicateData(String oldNotesUp, String newNotesUp) {

		String deleteDuplicateData = "delete from lms.L120M01A where docType='2' and mainid in (\n"
				+ " select mainid from lms.l120m01a where docType='2'\n"
				+ " and notesup in (?,?) and createtime is null\n"
				+ " group by mainid,ownbrid,caseno,custId,CASEYEAR,CASEBRID,CASESEQ,CREATOR,APPROVETIME,deletedTime having count(*)>1\n"
				+ " ) and oid not in (\n"
				+ " select max(oid) from lms.l120m01a where docType='2'\n"
				+ " and notesup in (?,?) and createtime is null\n"
				+ " group by mainid,ownbrid,caseno,custId,CASEYEAR,CASEBRID,CASESEQ,CREATOR,APPROVETIME,deletedTime having count(*)>1\n"
				+ " )";

		this.update(deleteDuplicateData, new Object[] { oldNotesUp, newNotesUp,
				oldNotesUp, newNotesUp });

	}

	private int update(String sql, Object... objects) {
		return this.getJdbc().update(sql, objects);
	}

}
