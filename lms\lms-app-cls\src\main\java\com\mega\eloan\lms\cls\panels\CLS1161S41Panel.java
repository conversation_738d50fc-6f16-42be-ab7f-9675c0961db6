package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 模擬動審文件資訊
 * </pre>
 * 
 * @since
 * <AUTHOR>
 * @version <ul>
 *          <li>
 *          </ul>
 */
public class CLS1161S41Panel extends Panel {

	public CLS1161S41Panel(String id) {
		super(id);
	}

	public CLS1161S41Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new DocLogPanel("_docLog").processPanelData(model, params);
	}

	/**/
	private static final long serialVersionUID = 1L;
}
