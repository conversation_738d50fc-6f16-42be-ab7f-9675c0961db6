---------------------------------------------------------
-- LMS.L918M01A 授管處停權主檔
---------------------------------------------------------
--DROP TABLE LMS.L918M01A;
CREATE TABLE LMS.L918M01A (
	OID           CHAR(32)      not null,
	UID           CHAR(32)     ,
	MAINID        CHAR(32)     ,
	TYPCD         CHAR(1)      ,
	CUSTID        VARCHAR(10)  ,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(1)      ,
	<PERSON>USTNAME      VARCHAR(120) ,
	<PERSON><PERSON>TYPE      CHAR(1)      ,
	OWNBRID       CHAR(3)      ,
	DOCSTATUS     VARCHAR(3)   ,
	<PERSON><PERSON><PERSON>CO<PERSON>    CHAR(32)     ,
	DOCURL        VARCHAR(40)  ,
	<PERSON>CODE        CHAR(6)      ,
	<PERSON>EA<PERSON><PERSON>       CHAR(6)      ,
	<PERSON>EA<PERSON><PERSON><PERSON>    TIMESTAMP    ,
	<PERSON><PERSON><PERSON><PERSON>       CHAR(6)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>    TIMESTAMP    ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>      CHAR(6)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>   TIMESTAMP    ,
	<PERSON><PERSON><PERSON><PERSON>      CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>ME   TIMESTAMP    ,
	CLASSNO       CHAR(3)      ,
	CASEYEAR      DECIMAL(4,0) ,
	CASEBRID      CHAR(3)      ,
	CASESEQ       DECIMAL(5,0) ,
	CASENO        VARCHAR(62)  ,
	STOPUPDATER   CHAR(6)      ,
	CASEDATE      TIMESTAMP    ,
	STOPAPPROVER  CHAR(6)      ,
	STOPAPPRTIME  TIMESTAMP    ,

	constraint P_L918M01A PRIMARY KEY(OID)
) in EL_DATA_4KTS index in EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L918M01A IS '授管處停權主檔';
COMMENT ON LMS.L918M01A (
	OID           IS 'oid', 
	UID           IS 'uid', 
	MAINID        IS '文件編號', 
	TYPCD         IS '區部別', 
	CUSTID        IS '統一編號', 
	DUPNO         IS '重覆序號', 
	CUSTNAME      IS '客戶名稱', 
	UNITTYPE      IS '辦理單位類別', 
	OWNBRID       IS '編製單位代號', 
	DOCSTATUS     IS '目前文件狀態', 
	RANDOMCODE    IS '文件亂碼', 
	DOCURL        IS '文件URL', 
	TXCODE        IS '交易代碼', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期', 
	APPROVER      IS '核准人員號碼', 
	APPROVETIME   IS '核准日期', 
	ISCLOSED      IS '是否結案', 
	DELETEDTIME   IS '刪除註記', 
	CLASSNO       IS '系統代碼', 
	CASEYEAR      IS '案件號碼-年度', 
	CASEBRID      IS '案件號碼-分行', 
	CASESEQ       IS '案件號碼-流水號', 
	CASENO        IS '案件號碼', 
	STOPUPDATER   IS '停權單位經辦', 
	CASEDATE      IS '停權單位經辦建立時間', 
	STOPAPPROVER  IS '停權單位覆核主管', 
	STOPAPPRTIME  IS '停權單位覆核時間'
);
