package com.mega.eloan.lms.crs.pages;


import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.crs.panels.LMS2430S01Panel;
import com.mega.eloan.lms.model.C243M01A;


@Controller
@RequestMapping("/crs/lms2430m01/{page}")

public class LMS2430M01Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	
	public LMS2430M01Page() {
		super();
	}
	
	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.編製中));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
				AuthType.Accept, RetrialDocStatusEnum.待覆核));
		
		renderJsI18N(LMS2430M01Page.class);

		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2);
		model.addAttribute("tabID", tabID);
		
		Panel panel = getPanel(page, params);
		panel.processPanelData(model, params);
	}
	
	public Panel getPanel(int index, PageParameters params) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS2430S01Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS2430S01Panel(TAB_CTX, true);
			break;
		}
		if (panel == null)
			panel = new Panel(TAB_CTX, true);
		return panel;
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return C243M01A.class;
	}
}
