/* 
 * L140S02BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S02B;

/** 流用(長擔)額度序號檔 **/
public interface L140S02BDao extends IGenericDao<L140S02B> {

	L140S02B findByOid(String oid);

	List<L140S02B> findByOids(String[] oids);

	List<L140S02B> findByMainId(String mainId);

	List<L140S02B> findByMainIdSeq(String mainId, Integer seq);

	L140S02B findByUniqueKey(String mainId, Integer seq, String cntrNo);

	List<L140S02B> findByIndex01(String mainId, Integer seq, String cntrNo);

	List<L140S02B> findByCntrNo(String CntrNo);
}