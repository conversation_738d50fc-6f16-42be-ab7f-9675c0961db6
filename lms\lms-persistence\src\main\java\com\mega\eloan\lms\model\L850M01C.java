/* 
 * L850M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 資料上傳作業主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L850M01C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L850M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** uid **/
	@Size(max=32)
	@Column(name="UID", length=32, columnDefinition="CHAR(32)")
	private String uid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 資料json<p/>
	 * 看各自的appCode申請的表單，希望長得是什麼樣子，將excel上傳的資料或是手敲的資料存在此TABLE
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="JSONDATA", columnDefinition="CLOB")
	private String jsondata;

	/** 資料細項1 **/
	@Size(max=1000)
	@Column(name="DETAIL1", length=1000, columnDefinition="VARCHAR(1000)")
	private String detail1;

	/** 資料細項2 **/
	@Size(max=1000)
	@Column(name="DETAIL2", length=1000, columnDefinition="VARCHAR(1000)")
	private String detail2;

	/** 資料細項3 **/
	@Size(max=1000)
	@Column(name="DETAIL3", length=1000, columnDefinition="VARCHAR(1000)")
	private String detail3;

	/** 資料細項4 **/
	@Size(max=1000)
	@Column(name="DETAIL4", length=1000, columnDefinition="VARCHAR(1000)")
	private String detail4;
	
	/** 資料細項5 **/
	@Size(max=1000)
	@Column(name="DETAIL5", length=1000, columnDefinition="VARCHAR(1000)")
	private String detail5;
	
	/** 資料細項6 **/
	@Size(max=1000)
	@Column(name="DETAIL6", length=1000, columnDefinition="VARCHAR(1000)")
	private String detail6;
	
	/** 資料細項7 **/
	@Size(max=1000)
	@Column(name="DETAIL7", length=1000, columnDefinition="VARCHAR(1000)")
	private String detail7;
	
	/** 資料細項8 **/
	@Size(max=1000)
	@Column(name="DETAIL8", length=1000, columnDefinition="VARCHAR(1000)")
	private String detail8;
	
	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得uid **/
	public String getUid() {
		return this.uid;
	}
	/** 設定uid **/
	public void setUid(String value) {
		this.uid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得資料json<p/>
	 * 看各自的appCode申請的表單，希望長得是什麼樣子，將excel上傳的資料或是手敲的資料存在此TABLE
	 */
	public String getJsondata() {
		return this.jsondata;
	}
	/**
	 *  設定資料json<p/>
	 *  看各自的appCode申請的表單，希望長得是什麼樣子，將excel上傳的資料或是手敲的資料存在此TABLE
	 **/
	public void setJsondata(String value) {
		this.jsondata = value;
	}

	/** 取得資料細項1 **/
	public String getDetail1() {
		return this.detail1;
	}
	/** 設定資料細項1 **/
	public void setDetail1(String value) {
		this.detail1 = value;
	}

	/** 取得資料細項2 **/
	public String getDetail2() {
		return this.detail2;
	}
	/** 設定資料細項2 **/
	public void setDetail2(String value) {
		this.detail2 = value;
	}

	/** 取得資料細項3 **/
	public String getDetail3() {
		return this.detail3;
	}
	/** 設定資料細項3 **/
	public void setDetail3(String value) {
		this.detail3 = value;
	}

	/** 取得資料細項4 **/
	public String getDetail4() {
		return this.detail4;
	}
	/** 設定資料細項4 **/
	public void setDetail4(String value) {
		this.detail4 = value;
	}

	/** 取得資料細項5 **/
	public String getDetail5() {
		return this.detail5;
	}
	/** 設定資料細項5 **/
	public void setDetail5(String value) {
		this.detail5 = value;
	}

	/** 取得資料細項6 **/
	public String getDetail6() {
		return this.detail6;
	}
	/** 設定資料細項6 **/
	public void setDetail6(String value) {
		this.detail6 = value;
	}
	
	/** 取得資料細項7 **/
	public String getDetail7() {
		return this.detail7;
	}
	/** 設定資料細項7 **/
	public void setDetail7(String value) {
		this.detail7 = value;
	}
	
	/** 取得資料細項8 **/
	public String getDetail8() {
		return this.detail8;
	}
	/** 設定資料細項8 **/
	public void setDetail8(String value) {
		this.detail8 = value;
	}
}
