/* 
 * L140M01R.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 各項費用明細檔 **/
/* 上傳 MIS.ELF509 */
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M01R")
public class L140M01R extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;
	
	/** 案件號碼-年度 **/
	@Column(name = "CASEYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer caseYear;
	
	/** 案件號碼-分行 **/
	@Size(max = 3)
	@Column(name = "CASEBRID", length = 3, columnDefinition = "VARCHAR(3)")
	private String caseBrId;
	
	/** 案件號碼-流水號 **/
	@Column(name = "CASESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer caseSeq;
	
	/** 案件號碼 **/
	@Size(max = 32)
	@Column(name = "CASENO", length = 32, columnDefinition = "VARCHAR(32)")
	private String caseNo;
	
	/** 費用代碼    select codevalue, codeDesc from com.bcodetype where codetype='cls1141_feeNo' and locale='zh_TW'     */
	@Size(max = 2)
	@Column(name = "FEENO", length = 2, columnDefinition = "VARCHAR(2)")
	private String feeNo;

	/** 收取範圍 **/
	@Size(max = 2)
	@Column(name = "FEESPHERE", length = 2, columnDefinition = "VARCHAR(2)")
	private String feeSphere;
	
	/** 費用-流水號 **/
	@Column(name = "FEESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer feeSeq;
	
	/** 客戶編號 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;
	
	/** 重覆碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "VARCHAR(1)")
	private String dupNo;
	
	/** 額度序號 **/
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "VARCHAR(12)")
	private String cntrno;
	
	/** 授信帳號 **/
	@Size(max = 14)
	@Column(name = "LOANNO", length = 14, columnDefinition = "VARCHAR(14)")
	private String loanNo;
	
	/** 費用幣別 **/
	@Size(max = 3)
	@Column(name = "FEESWFT", length = 3, columnDefinition = "VARCHAR(3)")
	private String feeSwft;
	
	/** 費用金額 **/
	@Column(name = "FEEAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal feeAmt;
	
	/** 備註 **/
	@Size(max = 350)
	@Column(name = "FEEMEMO", length = 350, columnDefinition = "VARCHAR(350)")
	private String feeMemo;
	
	/** 資料來源 
	 * 0>由簽報書新增  1>動審表引進簽報書內容  2>由動審表新增  3>授權外總處調整各項費用
	 * **/
	@Size(max = 1)
	@Column(name = "FEESRC", length = 1, columnDefinition = "VARCHAR(1)")
	private String feeSrc;	
	
	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}
	
	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}


	/** 設定費用代碼 **/
	public void setFeeNo(String value) {
		this.feeNo = value;
	}

	/** 取得費用代碼 **/
	public String getFeeNo() {
		return this.feeNo;
	}

	/** 設定收取範圍 **/
	public void setFeeSphere(String value) {
		this.feeSphere = value;
	}

	/** 取得收取範圍 **/
	public String getFeeSphere() {
		return this.feeSphere;
	}

	/** 設定費用金額 **/
	public void setFeeAmt(BigDecimal value) {
		this.feeAmt = value;
	}

	/** 取得費用金額 **/
	public BigDecimal getFeeAmt() {
		return this.feeAmt;
	}

	/** 設定備註 **/
	public void setFeeMemo(String value) {
		this.feeMemo = value;
	}

	/** 取得備註 **/
	public String getFeeMemo() {
		return this.feeMemo;
	}

	/** 設定客戶編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得客戶編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定重覆碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得重覆碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定額度序號 **/
	public void setCntrno(String value) {
		this.cntrno = value;
	}

	/** 取得額度序號 **/
	public String getCntrno() {
		return this.cntrno;
	}

	/** 設定授信帳號 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}

	/** 取得授信帳號 **/
	public String getLoanNo() {
		return this.loanNo;
	}

	/** 設定費用幣別 **/
	public void setFeeSwft(String value) {
		this.feeSwft = value;
	}

	/** 取得費用幣別 **/
	public String getFeeSwft() {
		return this.feeSwft;
	}

	/** 設定資料來源 **/
	public void setFeeSrc(String value) {
		this.feeSrc = value;
	}

	/** 取得資料來源 **/
	public String getFeeSrc() {
		return this.feeSrc;
	}

	/** 設定案件號碼-年度 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/** 取得案件號碼-年度 **/
	public Integer getCaseYear() {
		return this.caseYear;
	}

	/** 設定案件號碼-分行 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/** 取得案件號碼-分行 **/
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/** 設定案件號碼-流水號 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/** 取得案件號碼-流水號 **/
	public Integer getCaseSeq() {
		return this.caseSeq;
	}

	/** 設定案件號碼 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 取得案件號碼 **/
	public String getCaseNo() {
		return this.caseNo;
	}

	/** 設定費用-流水號 **/
	public void setFeeSeq(Integer value) {
		this.feeSeq = value;
	}

	/** 取得費用-流水號 **/
	public Integer getFeeSeq() {
		return this.feeSeq;
	}

}
