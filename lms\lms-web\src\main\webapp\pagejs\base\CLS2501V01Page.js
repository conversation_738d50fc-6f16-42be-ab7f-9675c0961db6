$(function(){

    var grid = $("#gridview").iGrid({
        handler: 'cls2501gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryC250M01A",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "custId",
        sortorder: "desc|desc",
        multiselect: true,
        colModel: [{            	
            colHeader: i18n.cls2501v01['C250M01A.custId'],//"借款人統編",
            name: 'custId',
            width: 45,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.cls2501v01['C250M01A.custName'],//"借款人",
            name: 'custName',
            width: 60,
            sortable: true
        }, {
            colHeader: i18n.cls2501v01['C250M01A.cntrNo'],//"額度序號",
            name: 'cntrNo',
            width: 45,
            sortable: true
        }, {
            colHeader: i18n.cls2501v01['C250M01A.yyyymm'],//"資料年月",
            name: 'yyyymm',
            width: 35,
            sortable: true
        }, {
            colHeader: i18n.cls2501v01['C250M01A.status'],//"性質",
            name: 'status',
            width: 20,			
            sortable: true,
			formatter: itemFormatter01
        }, {
            colHeader: i18n.cls2501v01['C250M01A.lnflag'],//"疑似代辦案件訊息",
            name:'lnflag', 
            width: 80,
            sortable: true,
			formatter: itemFormatter02
        }, {
            colHeader: i18n.cls2501v01['C250M01A.creator'],//"分行經辦",
            name: 'creator',
            width: 25,
            sortable: true,
            align: "center"
        }, {
                colHeader: i18n.cls2501v01["C250M01A.createTime"], // 建立日期
                align: "left",
                width: 35, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'createTime',
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d H:i'
                }
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
	
	var CntrnoGrid = $('#CntrnoGrid').iGrid({
            handler: 'cls2501gridhandler', //設定handler
            height: 80, //設定高度
            width: 500,
        	autowidth: true,
            action: 'queryGetCntrno', //執行的Method
            postData: {
	            
	        },
            rowNum: 15,
            rownumbers: true,
            colModel: [{
                colHeader: i18n.cls2501v01["C250M01A.yyyymm"], // 資料年月
                align: "left",
                width: 80, //設定寬度
                sortable: true, //是否允許排序
                name: 'yyyymm'
            }, {
                colHeader: i18n.cls2501v01["C250M01A.cntrNo"], // 額度序號
                align: "left",
                width: 120, //設定寬度
                sortable: true, //是否允許排序
                name: 'cntrNo'
            },{
                colHeader: i18n.cls2501v01["C250M01A.status"], // status
                name: 'statusDes',
                align: "left",
                width: 50, //設定寬度
                sortable: true, //是否允許排序                
                formatter: itemFormatter01
            },{
                colHeader: i18n.cls2501v01["C250M01A.lnflag"], // 疑似代辦案件訊息
                name: 'lnflagDes',
                align: "left",
                width: 200, //設定寬度
                sortable: true, //是否允許排序
                formatter: itemFormatter02
            }, {
            	name: 'loanNo',
            	hidden: true
        	}, {
            	name: 'status',
            	hidden: true
        	}, {
            	name: 'yyyymm',
            	hidden: true
        	}, {
            	name: 'lnflag',
            	hidden: true
        	}, {
            	name: 'othermemo',
            	hidden: true
        	}, {
            	name: 'branchComm',
            	hidden: true
        	}],
            loadComplete: function () {            	
            	if( CntrnoGrid.getGridParam("records")>0){
            		
            	}else{
					var custId = CntrnoGrid.getGridParam("postData")['custId'];
					if(custId && custId.length>1){
						API.showErrorMessage(i18n.cls2501v01["C250M01A.error6"]);	
					}            		
            	}
            }  
     });
	 
	function itemFormatter01(cellvalue, otions, rowObject){
		var val = "";
		if(cellvalue!==null && Boolean(cellvalue)){
			val = $.trim(cellvalue);
		}
		
		if (val == 'N') {
            // C250M01A.statusN=新做  
            return i18n.cls2501v01['C250M01A.statusN'];
        }
		else if (val == 'A') {
            // C250M01A.statusA=增額  
            return i18n.cls2501v01['C250M01A.statusA'];
        }
        else {
            return val;
        }
        
    }
	
	function itemFormatter02(cellvalue, otions, rowObject){
		var val = "";
		if(cellvalue!==null && Boolean(cellvalue)){
			val = $.trim(cellvalue);
		}
        if (val == 'A') {
            // C250M01A.lnflagA=撥款後回查有其他金融機構短期內接續撥款情形 (指3個月內) 
            return i18n.cls2501v01['C250M01A.lnflagA'];
        }
		else if (val == 'B') {
            // C250M01A.lnflagB=貸款後不久即延滯情形 (指3個月內) 
            return i18n.cls2501v01['C250M01A.lnflagB'];
        }
		else if (val == 'C') {
            // C250M01A.lnflagC=跨區承作之情形 (對於借款人居住、工作地與案件來源無地源關係之申貸案件) 
            return i18n.cls2501v01['C250M01A.lnflagC'];
        }
		else if (val == 'D') {
            // C250M01A.lnflagD=其他可疑情形 
            return i18n.cls2501v01['C250M01A.lnflagD'];
        }
        else if (val == 'E') {
            // C250M01A.lnflagE=經查並無仲介代辦情況出現 
            return i18n.cls2501v01['C250M01A.lnflagE'];
        }
		else if (val == 'F') {
            // C250M01A.lnflagF=撥款後回查有其他金融機構短期內接續撥款情形(指3個月內)&貸款後不久即延滯情形(指3個月內)
            return i18n.cls2501v01['C250M01A.lnflagF'];
        }
        else {
            return val;
        }
    }
	
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({			
            url: '..' + rowObject.docURL + '/01',
            data: {
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
	
	
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];
        
        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($("#gridview").getRowData(rows[i]).oid);
                }
                
                $.ajax({
                    handler: "cls2501m01formhandler",
                    data: {
                        formAction: "deleteC250M01A",
                        oids: data
						}
                    }).done(function(obj){
                        $("#gridview").trigger("reloadGrid");
                });
            }
        });
    }).end().find("#btnAdd").click(function(){
		chose_custId().done(function(resultFrom_chose_custId){
   	 		chose_cntrNo(resultFrom_chose_custId).done(function(resultFrom_chose_cntrNo){
				$.ajax({
                    handler: "cls2501m01formhandler",
                    action : 'newC250M01A',
					data : {
						custId:resultFrom_chose_cntrNo.custId,
						dupNo:resultFrom_chose_cntrNo.dupNo,
						custName:resultFrom_chose_cntrNo.custName,
						cntrNo:resultFrom_chose_cntrNo.cntrNo,
						loanNo:resultFrom_chose_cntrNo.loanNo,
						status:resultFrom_chose_cntrNo.status,
						lnflag:resultFrom_chose_cntrNo.lnflag,
						yyyymm:resultFrom_chose_cntrNo.yyyymm,
						othermemo:resultFrom_chose_cntrNo.othermemo,
						branchComm:resultFrom_chose_cntrNo.branchComm						                
					}
   	 				}).done(function(obj){
	        			$.form.submit({
                    		url: '../cls/cls2501m01/01',
                    		data: {
                        		oid: obj.oid,
                        		mainOid: obj.oid,
                        		mainDocStatus: viewstatus,
                        		txCode: txCode
                    		},
                    		target: obj.oid
               	 		});
	    		});
	    	});
	    });
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
			// C250M01A.error1=此功能不能多選
            CommonAPI.showMessage(i18n.cls2501v01["C250M01A.error1"]);
        }
        else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
        openFilterBox();
    }).end().find("#btnUseFirstTable").click(function(){
        openUseFirstTable();
    }).end().find("#btnLogeIN").click(function(){
        openLogeIN();
    }).end().find("#btnDataFix").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        openCntrCaseBox(result.oid);
    });
	
	
	function chose_custId(){	
		var my_dfd = $.Deferred();
		AddCustAction.open({
	    		handler: 'cls2501m01formhandler',
				action : 'echo_custId',
				data : {
	            },
				callback : function(json){					
	            	// 關掉 AddCustAction 的 
	            	$.thickbox.close();					
					my_dfd.resolve( json );					
				}
			});
		return my_dfd.promise();
	}
	function chose_cntrNo(resultFrom_chose_custId){
		var my_dfd = $.Deferred();
		
		CntrnoGrid.jqGrid("setGridParam", {
            postData: {
                'custId': resultFrom_chose_custId.custId
				,'dupNo': resultFrom_chose_custId.dupNo
            },
            search: true
        }).trigger("reloadGrid");

		$("#CntrnoThickBox").thickbox({
	       title: i18n.cls2501v01["C250M01A.title02"], width: 600,height: 250,align: "center",valign: "bottom",
           modal: false, i18n: i18n.def,
		   buttons: {
                "sure": function(){
					 var data = CntrnoGrid.getSingleData();
                     if (data) {
						 $.thickbox.close();
						 //---
                    	 var cntrNo = data.cntrNo;
						 var yyyymm = data.yyyymm;
						 var lnflag = data.lnflag;
						 var status = data.status;
						 var lnflagDes = data.lnflagDes;
						 var statusDes = data.statusDes;
						 var loanNo = data.loanNo;
						 var othermemo = data.othermemo;
						 var branchComm = data.branchComm;
        				 my_dfd.resolve($.extend(resultFrom_chose_custId, {'cntrNo':cntrNo, 'yyyymm':yyyymm,
						  'lnflag':lnflag, 'status':status, 'loanNo':loanNo, 'lnflagDes':lnflagDes, 
						  'statusDes':statusDes, 'othermemo':othermemo, 'branchComm':branchComm} ));
                     }     	
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }	
		});	
		
		return my_dfd.promise();
	}
	
});

