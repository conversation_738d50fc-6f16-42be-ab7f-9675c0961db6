package com.mega.eloan.lms.batch.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.mfaloan.bean.ELF506;
import com.mega.eloan.lms.mfaloan.bean.ELF515;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import com.mega.eloan.lms.mfaloan.service.MisELF515Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF515Service;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

@Service("lmsbatch0003serviceimpl")
public class LmsBatch0003ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(LmsBatch0003ServiceImpl.class);

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	MisELF506Service misELF506Service;

	@Resource
	MisELF515Service misELF515Service;

	@Resource
	ObsdbELF515Service obsdbELF515Service;

	@Resource
	BranchService branchService;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		JSONObject result = new JSONObject();

		try {
			LOGGER.info("-------------------lmsbatch0003serviceimpl start -----------------------------------");

			JSONObject request = json.getJSONObject("request");
			String type = request.getString("type");

			LOGGER.info("type:{} ", type);

			if ("1".equals(type)) {

				misELF515Service.do72_2BatchFrom515();

			} else if ("2".equals(type)) {
				// 取出倉儲所有的海外授信資料

				LOGGER.info("開始抓取DW_LNCNTROVS");
				List<Map<String, Object>> datas = dwdbBASEService
						.findLnCntrovsExitem();

				LOGGER.info("資料筆數：{}", datas.size());

				int count = 0;
				for (Map<String, Object> data : datas) {
					count = count + 1;
					LOGGER.info("開始執行第{}筆", count);

					// 取得72-2欄位相關註記
					String dw_cntrNo = Util.trim(MapUtils.getString(data,
							"LINE_NO"));
					String dw_FLAG_722 = Util.trim(MapUtils.getString(data,
							"FLAG_722"));
					String dw_IS_BUY_722 = Util.trim(MapUtils.getString(data,
							"IS_BUY_722"));
					String dw_EX_ITEM_722 = Util.trim(MapUtils.getString(data,
							"EX_ITEM_722"));
					String dw_CUST_KEY = Util.trim(MapUtils.getString(data,
							"CUST_KEY"));

					// 因為400沒開放維護exitem，所以isBUy = "N", exitem 要清掉
					if ("N".equals(dw_IS_BUY_722)) {
						dw_EX_ITEM_722 = "";
					}

					// 更新中心主機的ELF506 資料
					ELF506 elf506 = misELF506Service.findByCntrNo(dw_cntrNo);
					if (elf506 != null) {
						// 如果506有資料的話，72-2的註記資訊先以倉儲的資料為準
						misELF506Service.update72_2markFromDW(dw_cntrNo,
								dw_FLAG_722, dw_IS_BUY_722, dw_EX_ITEM_722);

					} else {
						// 可能中心主機沒資料的話，那麼就insert一筆72-2的資料，其它資料預設為空值或是N
						misELF506Service.insert(dw_cntrNo, "N", "", "N",
								BigDecimal.ZERO, BigDecimal.ZERO,
								BigDecimal.ZERO, BigDecimal.ZERO,
								BigDecimal.ZERO, BigDecimal.ZERO,
								BigDecimal.ZERO, BigDecimal.ZERO,
								new Timestamp(System.currentTimeMillis()),
								new Timestamp(System.currentTimeMillis()),
								"sys", "sys", "", "", "", "", dw_CUST_KEY,
								dw_FLAG_722, "sys", "",
								new Timestamp(System.currentTimeMillis()),
								CapDate.formatDate(new java.util.Date(),
										"yyyy-MM-dd"), dw_IS_BUY_722,
								dw_EX_ITEM_722, "", "", "", "", "", "", "",
								"N", "", "", "", "", "");
					}

					// 如果exitem 排除事項有值的話，那麼就看elf515 有沒有資料，沒有的話，也insert進去
					if (Util.isNotEmpty(dw_EX_ITEM_722)) {
						ELF515 elf515data = misELF515Service.find(dw_cntrNo,
								dw_EX_ITEM_722);
						if (elf515data == null) {
							ELF515 newData = new ELF515();
							newData.setElf515_cntrno(dw_cntrNo);
							newData.setElf515_type(dw_EX_ITEM_722);
							newData.setElf515_createUnit("sys");
							newData.setElf515_modifyUnit("sys");
							newData.setElf515_empl_no("sys");
							newData.setElf515_supv_no("sys");
							misELF515Service.insert(newData);
						}

					} else {
						misELF515Service.delete(dw_cntrNo);
					}

				}
			} else if ("3".equals(type)) {
				LOGGER.info("更新海外分行ELF515資料");
				List<IBranch> allBranch = branchService.getAllBranch();

				for (IBranch branch : allBranch) {
					String brNo = branch.getBrNo();
					String brekFlag = branch.getBrekFlag();
					if (branchService.isOBSBranch(brNo)
							&& !"Y".equals(brekFlag)) {
						String obsSchema = branch.getObsSchema();
						LOGGER.info(brNo + ":" + obsSchema);
						LOGGER.info("更新分行：{}，dbname：{}", new String[] { brNo,
								obsSchema });
						if (Util.isNotEmpty(obsSchema)
								&& obsSchema.startsWith("EL")) {
							obsdbELF515Service.deleteAll(brNo);
							obsdbELF515Service.do72_2BatchFrom515(brNo);
						}
					}

				}
			}

			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"LmsBatch0003ServiceImpl執行成功！");

		} catch (Exception ex) {
			ex.printStackTrace();
			result = WebBatchCode.RC_ERROR;
			result.element(
					WebBatchCode.P_RESPONSE,
					"LmsBatch0003ServiceImpl執行失敗！==>"
							+ ex.getLocalizedMessage());

		}

		return result;
	}

}
