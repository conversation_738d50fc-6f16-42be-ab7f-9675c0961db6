package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01F;


/** 企金存放款外匯往來檔 **/
public interface L120S01FDao extends IGenericDao<L120S01F> {

	L120S01F findByOid(String oid);
	
	List<L120S01F> findByMainId(String mainId);
	
	L120S01F findByUniqueKey(String mainId,String custId,String dupNo);
	
	List<Object[]> findL120s01f(String mainId);

	int delModel(String mainId);
	List<L120S01F> findByCustIdDupId(String custId,String DupNo);
}