/* 
 * LMS9541GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.grid;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.model.L810M01A;
import com.mega.eloan.lms.rpt.service.LMS9541V01Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 優惠房貸報表 - 明細表
 * </pre>
 * 
 * @since 2012/12/06
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/06,Vector,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9541v01gridhandler")
public class LMS9541V01GridHandler extends AbstractGridHandler {

	@Resource
	LMS9541V01Service service;

	@Resource
	BranchService branch;

	final int BASE = 10000;// 輸出金額單位：萬元

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult query(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "useType", "4");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "brno",
				user.getUnitNo());
		Page page = service.findPage(L810M01A.class, pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 調閱資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryView(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> data = service.findMisData(
				params.getString("type"), false,
				params.getAsBoolean("tooMuch"), params.getString("brno"));
		
		//解決grid資料不會換頁問題....
		int page = Util.parseInt(params.getString("gridPage")) - 1, cols = Util
				.parseInt(params.getString("rows"));
		int begin = page * cols, end = (page + 1) * cols;
		end = end > data.size() ? data.size() : end;
		List<Map<String, Object>> result = data;
		if (begin <= end) {
			result = data.subList(begin, end);
		}

		return new CapMapGridResult(result, data.size());
	}
}
