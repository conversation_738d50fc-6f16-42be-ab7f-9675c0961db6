package com.mega.eloan.lms.rpt.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
/**
 * <pre>
 * 優惠房貸報表-統計表
 * </pre>
 * 
 * @since 2012/12/06
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/06,Vector,new
 *          </ul>
 */
@Controller
@RequestMapping(path = "/rpt/lms9541v02")
public class LMS9541V02Page extends AbstractEloanInnerView {

	public LMS9541V02Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		
		addToButtonPanel(model, LmsButtonEnum.Add,LmsButtonEnum.View,LmsButtonEnum.Print);

		renderJsI18N(LMS9541V02Page.class);
		model.addAttribute("loadScript", "loadScript('pagejs/rpt/LMS9541V02Page');");
	}
}
