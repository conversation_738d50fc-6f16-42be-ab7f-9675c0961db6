/* 
 * LELF412BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.LELF412BDao;
import com.mega.eloan.lms.model.LELF412B;

/** 逾期未覆審名單自辦覆審控制歷史檔 **/
@Repository
public class LELF412BDaoImpl extends LMSJpaDao<LELF412B, String> implements
		LELF412BDao {

	@Override
	public LELF412B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<LELF412B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<LELF412B> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<LELF412B> findByMainIdAndDataDate(String mainId, Date dataDate) {
		ISearch search = createSearchTemplete();
		List<LELF412B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (dataDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
					dataDate);

		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<LELF412B> findByMainIdDataDateAndBranch(String mainId,
			Date dataDate, String branch) {
		ISearch search = createSearchTemplete();
		List<LELF412B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (dataDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
					dataDate);
		if (branch != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);

		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<LELF412B> findByMainIdDataDateBranchAndCustId(String mainId,
			Date dataDate, String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<LELF412B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (dataDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
					dataDate);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
}