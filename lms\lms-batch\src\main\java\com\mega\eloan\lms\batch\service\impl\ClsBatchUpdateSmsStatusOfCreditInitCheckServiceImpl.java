package com.mega.eloan.lms.batch.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.model.SmsContent;
import com.mega.eloan.common.service.SmsService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C122M01A;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 *  歡喜信貸-自動初審被婉卻之顧客-更新其該次申請之簡訊發送狀態-批次
 * </pre>
 * 
 * @since 2023/06/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2023/06/05,new
 *          </ul>
 */
@Service("clsBatchUpdateSmsStatusOfCreditInitCheckServiceImpl")
public class ClsBatchUpdateSmsStatusOfCreditInitCheckServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory.getLogger(ClsBatchUpdateSmsStatusOfCreditInitCheckServiceImpl.class);
	
	private static final String Virtual_Employee_id = "00ZCB2";
	
	protected static final int timeout = 60;
	
	@Resource
	C122M01ADao c122m01aDao;

	@Resource
	CLSService clsService;
	
	@Resource
	private SmsService smsService;
	
	@Resource
	CLS1220Service cls1220Service;

	@Resource
	MisdbBASEService misdbBASEService;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		JSONObject result = null;
		String mainId = null;
		String custId = null;
		String dupNo = null;
		String branchNo = null;
		try {
			List<C122M01A> c122m01aList = c122m01aDao.findSmsSentCaseForOnlineInitialCheckCase();
			for(C122M01A c122m01a : c122m01aList){
				mainId = c122m01a.getMainId();
				custId = c122m01a.getCustId();
				dupNo = c122m01a.getDupNo();
				branchNo = c122m01a.getOwnBrId();
				List<SmsContent> smsContentList = smsService.queryMsgsByMainIdForHPCL(mainId);// SIT環境下得到的response內xml body可能無值；SIT下六組目前不會保存距今超過一個月的簡訊狀態
				if (CollectionUtils.isNotEmpty(smsContentList)) {
					smsService.updateMsgs(smsContentList, ClsBatchUpdateSmsStatusOfCreditInitCheckServiceImpl.Virtual_Employee_id);// 更新SMSLOG檔：測試時要看其中的ARRIVECODE有無被更新至最新
				}
				SmsContent smsContent = smsService.queryLatestMsgsByMainId(mainId);// 理論上，一次申請若被婉卻只會發送一次簡訊，但若例外導致多次發送，取最新的SMSLOG
				if (smsContent != null) {
					if (CapString.isEmpty(smsContent.getArriveCode())) {
						c122m01a.setSmsSendingStatus("U");
						LOGGER.debug("歡喜信貸初審簡訊發送狀態：API在途 ===> C122M01A.mainId: {}, C122M01A.custId: {}, C122M01A.dupNo: {}, C122M01A.branchNo: {}", new String[]{mainId, custId, dupNo, branchNo});
					} else if (Util.trim(smsContent.getArriveCode()).matches("OK|0")) {
						c122m01a.setSmsSendingStatus("S");
						LOGGER.debug("歡喜信貸初審簡訊發送狀態：成功 ===> C122M01A.mainId: {}, C122M01A.custId: {}, C122M01A.dupNo: {}, C122M01A.branchNo: {}", new String[]{mainId, custId, dupNo, branchNo});
					} else if ("FD".equals(Util.trim(smsContent.getArriveCode()))) {
						c122m01a.setSmsSendingStatus("D");
						LOGGER.debug("歡喜信貸初審簡訊發送狀態：已送達顧客之電信業者 ===> C122M01A.mainId: {}, C122M01A.custId: {}, C122M01A.dupNo: {}, C122M01A.branchNo: {}", new String[]{mainId, custId, dupNo, branchNo});
					} else {
						c122m01a.setSmsSendingStatus("F");
						LOGGER.debug("歡喜信貸初審簡訊發送狀態：失敗 ===> C122M01A.mainId: {}, C122M01A.custId: {}, C122M01A.dupNo: {}, C122M01A.branchNo: {}", new String[]{mainId, custId, dupNo, branchNo});
					}
					c122m01a.setUpdater(ClsBatchUpdateSmsStatusOfCreditInitCheckServiceImpl.Virtual_Employee_id);
					c122m01a.setUpdateTime(CapDate.getCurrentTimestamp());
					clsService.daoSave(c122m01a);
				}
			}
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE, "clsBatchUpdateSmsStatusOfCreditInitCheckServiceImpl 執行成功！簡訊發送狀態更新完成");
		} catch (Exception ex) {
			LOGGER.error(StrUtils.getStackTrace(ex));
			result = WebBatchCode.RC_ERROR;
			String msg = ex.getMessage() + "===> C122M01A.mainId:" + mainId + ", C122M01A.custId:" + custId + ", C122M01A.dupNo:" + dupNo + ", C122M01A.branchNo:" + branchNo;
			result.element(
					WebBatchCode.P_RESPONSE, "clsBatchUpdateSmsStatusOfCreditInitCheckServiceImpl 執行失敗！==>" + msg + " - " + ex.getLocalizedMessage());
		}
		return result;
	}
	
}
