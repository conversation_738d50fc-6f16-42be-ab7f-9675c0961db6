/* 
 * L902M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L902M01A;

/** 私募基金代碼主檔 **/
public interface L902M01ADao extends IGenericDao<L902M01A> {

	L902M01A findByOid(String oid);

	L902M01A findByMainId(String mainId);

	List<L902M01A> findByIndex01(String mainId);

	L902M01A findByPeNo(String peNo);

	public List<L902M01A> findByPeNoList(String peNo);

	/**
	 * 取得所有私募基金資料 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 */
	List<L902M01A> findAllPeNo();
	
	/**
	 * 取得所有私募基金最大號
	 * @return
	 */
	public L902M01A findMaxPeNo();
	
}