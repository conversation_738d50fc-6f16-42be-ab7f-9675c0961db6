/* 
 * LMS7840ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.dao.C900M01DDao;
import com.mega.eloan.lms.eloandb.service.LmsCustdataService;
import com.mega.eloan.lms.lms.service.LMS7840Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C900M01D;
import com.mega.sso.service.BranchService;

/**
 * <pre>
 * 簽報紀錄查詢
 * </pre>
 * 
 * @since 2011/12/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/8,REX,new
 *          </ul>
 */
@Service
public class LMS7840ServiceImpl implements LMS7840Service {

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	LmsCustdataService lmsCustdataService;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	C900M01DDao c900m01dDao;

	@Override
	public List<C900M01D> queryC900m01ds() {
		List<C900M01D> c900m01ds = new ArrayList<C900M01D>();
		c900m01ds = c900m01dDao.getAll();
		return c900m01ds;
	}

}
