/* 
 *LMS902Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service;

import java.util.Map;


/**
 * <pre>
 * 優惠房貸額度維護 - 總額度維護作業
 * </pre>
 * 
 * @since 2012/11/01
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/01,Vector Lo,new
 *          <li>2013/07/09,Vector Lo,加入寄信Batch
 *          </ul>
 */
/* Use MIS-RDB */
public interface CLS9021Service {
	
	// 取得 目前已登記之總優惠額度
	public String findFavloan(String kindNo, String orctNo2);

	//  取得 目前已撥款之總優惠額度
	public String findRfavloan(String orctNo2);
	
	public boolean saveElghtapp(String kindNo,String totapp);

	public String findElghtappByKindno(String kindNo);
	
	//以下寄信用
	public boolean sendMail(String kind, String version, Map<String, String> paramMap);
	
	public Map<String, Object> findTot(String kindNo, String type);
}
