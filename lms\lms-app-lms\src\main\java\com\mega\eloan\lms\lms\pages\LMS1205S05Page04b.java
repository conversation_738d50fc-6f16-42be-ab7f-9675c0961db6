package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

/**
 * <pre>
 * 六、中長期營運、財務計劃及償債能力評估，及產業概況  IFRS
 * </pre>
 * 
 * @since 2013/6/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/6/19,007625 new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1205S05Db")
public class LMS1205S05Page04b extends AbstractEloanForm {

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		
		super.execute(model, params);
		renderJsI18N(LMS1205S05Page04b.class);
	}
	
	@Override
    public String getViewName() {
        return "common/pages/None";
    }

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
}
