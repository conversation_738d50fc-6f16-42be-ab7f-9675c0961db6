/* 
 * C160M01FDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160M01F;

/** 個金動審表匯入主檔 **/
public interface C160M01FDao extends IGenericDao<C160M01F> {

	C160M01F findByOid(String oid);
	
	List<C160M01F> findByMainId(String mainId);
	
	C160M01F findByUniqueKey(String mainId);

	List<C160M01F> findByIndex01(String mainId);
}