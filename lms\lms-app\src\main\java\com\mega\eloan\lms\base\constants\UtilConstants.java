/* 
 * UtilConstants.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.constants;

/**
 * <pre>
 * 專案常用 參數
 * </pre>
 * 
 * @since 2011/11/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/29,REX,new
 *          <li>2013/06/25,Rex,修改選項代碼錯誤 自然人 = "1"; 公司法人 = "5";土地抵押貸款 =
 *          "2";非央行自然人 = "4";
 *          </ul>
 */
public interface UtilConstants {

	static final String 兆豐銀行代碼 = "017";

	/** 案件類別 */
	interface CaseSchema {
		static final String 企金 = "LMS";
		static final String 個金 = "CLS";
		static final String 徵信 = "CES";
	}

	/** 傳送類別 */
	interface AuthType {
		/** 送會簽 */
		static final String 送會簽 = "5";
		/** 送審查 */
		static final String 送審查 = "6";
	}

	/** 共同借款人檔新增類別 */
	interface L140S01AType {
		static final String 分行新增 = "1";
		static final String 授管處新增 = "2";
	}

	interface L140S02AModelKind {
		String 免辦 = "0";
		String 房貸 = "1";
		String 非房貸 = "2";
		String 卡友貸 = "3";
	}

	interface L140S02ADisasType {
		String 民105_0206震災受災戶 = "01";
		String 民113_0403花蓮地震震災 = "05";
	}

	/** J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等 */
	interface L140S03AModelKind {
		String 免辦 = "C0";
		String 房貸 = "C1";
		String 非房貸 = "C2";
	}

	/** 簽章欄職稱 */
	interface STAFFJOB {
		/**
		 * 分行經辦 <br/>
		 * 營運中心經辦<br/>
		 * 授管處經辦
		 */
		static final String 經辦L1 = "L1";
		/** 帳戶管理員 */
		static final String 帳戶管理員L2 = "L2";
		/**
		 * 分行授信/覆核主管(Boss) <br/>
		 * 母行覆核 營運中心襄理(Area_Manager) <br/>
		 * 授管處覆核(Head_Manager) (母行/授管處/營運中心)
		 */
		static final String 授信主管L3 = "L3";
		/**
		 * 分行覆核主管(ReCheck) <br/>
		 * 母行覆核主管<br/>
		 * 營運中心覆核主管<br/>
		 * 授管處覆核主管 <br/>
		 * (母行/授管處/營運中心)
		 */
		static final String 執行覆核主管L4 = "L4";
		/**
		 * 位/授權主管(Manager) <br/>
		 * 營運中心副營運長(Area_Sub_Leader) <br/>
		 * 授管處副處長(Head_Sub_Leader)<br/>
		 * (母行/授管處/營運中心)
		 */
		static final String 單位授權主管L5 = "L5";
		/**
		 * 分行單位主管() <br/>
		 * 母行單位主管 營運中心營運長(Area_Leader) <br/>
		 * 授管處協理(Head_Leader) <br/>
		 * (母行/授管處/營運中心)
		 */
		static final String 分行單位主管L6 = "L6";
		/** 提會登錄經辦 */
		static final String 提會登錄經辦L7 = "L7";
		/** 提會放行主管 */
		static final String 提會放行主管L8 = "L8";
		/**
		 * L9.單位主管 分行單位主管(UNIT_MANAGERID)
		 */
		static final String 單位主管L9 = "L9";

		static final String 無紙化簽報最後覆核主管P7 = "P7";
	}

	/** 簽章欄單位類型 */
	interface BRANCHTYPE {
		/** 分行 */
		static final String 分行 = "1";
		/** 母行/海外總行 */
		static final String 母行海外總行 = "2";
		/** 營運中心 */
		static final String 營運中心 = "3";
		/** 授管處 */
		static final String 授管處 = "4";
		/** 徵信承作分行 */
		static final String 徵信承作分行 = "5";
		/** 國金部_營運中心 */
		static final String 國金部_營運中心 = "6";

	}

	/** Ajax回傳訊息 */
	interface AJAX_RSP_MSG {
		static final String[] ERR_MSG = new String[] { AJAX_RSP_MSG.儲存成功,
				AJAX_RSP_MSG.執行成功, AJAX_RSP_MSG.刪除成功, AJAX_RSP_MSG.新增成功,
				AJAX_RSP_MSG.無權限刪除資料, AJAX_RSP_MSG.查無資料, AJAX_RSP_MSG.內部處理發生錯誤,
				AJAX_RSP_MSG.匯率引入發生錯誤, AJAX_RSP_MSG.依聯徵規定,
				AJAX_RSP_MSG.明細資料最多x筆, AJAX_RSP_MSG.持分輸入不完整無法計算持分面積,
				AJAX_RSP_MSG.尚未建立或引進該集團之轄下公司授信往來及名單資料, AJAX_RSP_MSG.主機資料庫中無該x,
				AJAX_RSP_MSG.報表無資料, AJAX_RSP_MSG.資料已存在 };
		static final String 欄位不得為空 = "EFD0005";
		static final String 輸入位數超過 = "EFD0007";
		static final String 注意 = "EFD0015";
		static final String 儲存成功 = "EFD0017";
		static final String 執行成功 = "EFD0018";
		static final String 刪除成功 = "EFD0019";
		static final String 執行有誤 = "EFD0025";
		static final String 新增成功 = "EFD0035";
		static final String 無權限刪除資料 = "EFD0058";
		static final String 查無資料 = "EFD0052";
		static final String 內部處理發生錯誤 = "EFD0066";
		static final String 匯率引入發生錯誤 = "EFD0068";
		static final String 依聯徵規定 = "EFD2056";
		static final String 明細資料最多x筆 = "EFD2039";
		static final String 持分輸入不完整無法計算持分面積 = "EFD2047";
		static final String 尚未建立或引進該集團之轄下公司授信往來及名單資料 = "EFD2057";
		static final String 主機資料庫中無該x = "EFD0038";
		static final String 報表無資料 = "EFD0002";
		static final String 資料已存在 = "EFD0047";
		static final String 尚未建立或引進該集團之轄下公司授信往來及名單資料請先建立後再引進財務資訊 = "EFD2058";
		static final String 引入新資料前將導致目前舊有資料被清除 = "EFD2059";
		static final String 主機資料庫中無該集團轄下公司之財務資訊 = "EFD2060";
	}

	/** 幣別 */
	interface CURR {
		/** 新台幣 */
		static final String TWD = "TWD";
		/** 美金 */
		static final String USD = "USD";
	}

	/** 一般預設共用 */
	interface DEFAULT {
		/** 是 */
		static final String 是 = "Y";
		/** 否 */
		static final String 否 = "N";
		/** － */
		static final String 一 = "O";
	}

	/** 央行房貸註記 */
	interface HOUSEYN {
		/** 一戶 */
		static final String 一戶 = "Y";
		/** 無 */
		static final String 無 = "N";
		/** 二戶(含)以上 */
		static final String 二戶 = "B";
	}

	/** 一般共用欄位 */
	interface Field {

		/** 刪除時間 */
		static final String 刪除時間 = "deletedTime";
		static final String 目前編製行 = "ownBrId";
		static final String CUSTID = "custId";
		static final String DUPNO = "dupNo";
		static final String 兆 = "(兆)";
		static final String 授字第 = "授字第";
		static final String 號 = "號";
	}

	/** 符號 */
	interface Mark {
		static final String SPILT_MARK = "\\|";
		static final String MARK = "|";
		static final String MARKDAN = "、";
		static final String SPACE = "";
		static final String FULLSPACE = "　";
		static final String HTMLSPACE = "&nbsp;";
		static final String HTMLBR = "<br/>";
		static final String ZEROISNODATA = "0";
	}

	/** 日期格式 */
	interface DateFormat {
		static final String YYYY_MM = "yyyy-MM";
		static final String YYYY_MM_DD = "yyyy-MM-dd";
		static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
		static final String YYYY_MM_DD_HH_MM_SS2 = "yyyy-MM-dd HH.mm.ss";
	}

	// J-109-0363_05097_B1001 Web
	// e-Loan授信修改日本地區分行大阪分行授權外案件經由東京分行放行後送呈授信審查處(Country Head 模式)。
	/** 行別代號 */
	interface BankNo {
		static final String 國外部 = "007";
		static final String 財富管理處 = "009";
		static final String 財務部 = "011";
		static final String 金控總部分行 = "201";
		static final String 國金部 = "025";
		static final String 資訊處 = "900";
		static final String 稽核處 = "906";
		static final String 風控處 = "912";
		static final String 企劃處 = "916";
		static final String 授管處 = "918";
		static final String 債管處 = "924";
		static final String 業管處 = "938";
		static final String 海業處 = "942";
		static final String 海管處 = "937";
		static final String 紐約分行 = "0A2";
		// 泰國(TH)
		static final String 泰國曼谷總行 = "Y01";
		static final String 曼谷春武里分行 = "Y02";
		static final String 挽那分行 = "Y03";
		static final String 萬磅分行 = "Y04";
		static final String 羅勇分行 = "Y05";
		// 越南(VN)
		static final String 胡志明市分行 = "0B6";
		
		// 加拿大(CA)
		//要確定大光華分行已停用，溫哥華分行代號有換
		static final String 加拿大光華分行 = "Z01";
//		static final String 加拿大溫哥華 = "Z03";
		static final String 加拿大多倫多 = "0D7";
		static final String 加拿大溫哥華 = "0D8";
		
		// 澳洲(AU)
		static final String 雪梨分行 = "0B9";
		static final String 布里斯本分行 = "0C2";
		static final String 墨爾本分行 = "0C5";
		// 日本(JP)
		static final String 東京分行 = "0A7";
		static final String 大阪分行 = "0A8";
		// 柬埔寨(KH)
		static final String 金邊分行 = "0C7";
		static final String 金邊機場支行 = "0C9";
		static final String 金邊奧林匹克支行 = "0D2";
		static final String 金邊堆谷支行 = "0D4";
		//法國(FR)
		static final String 法國巴黎分行 = "0B0";

		static final String 中部區域授信中心 = "920";
		static final String 南部區域授信中心 = "922";
		static final String 北一區營運中心 = "931";
		static final String 北二區營運中心 = "932";
		static final String 桃竹苗區營運中心 = "933";
		static final String 中區營運中心 = "934";
		static final String 南區營運中心 = "935";
		static final String 蘇州分行 = "0C8";
		static final String 吳江支行 = "0D1";
		static final String 授信行銷處 = "940";
		static final String 消金業務處 = "943";
		static final String 私銀處作業組 = "149";
		static final String 信用卡暨支付處 = "109";
	}

	// J-109-0363_05097_B1001 Web
	// e-Loan授信修改日本地區分行大阪分行授權外案件經由東京分行放行後送呈授信審查處(Country Head 模式)。
	/** 國別 */
	interface Country {
		static final String 泰國 = "TH";
		static final String 澳洲 = "AU";
		static final String 加拿大 = "CA";
		static final String 美國 = "US";
		static final String 中國 = "CN";
		static final String 柬埔寨 = "KH";
		static final String 日本 = "JP";
		static final String 台灣 = "TW";
	}

	/** 相關身分 */
	interface lngeFlag {
		static final String 共同借款人 = "C";
		static final String 共同發票人 = "D";
		static final String 票據債務人 = "E";
		static final String 連帶保證人 = "G";
		static final String 連帶借款人 = "L";
		static final String 擔保品提供人 = "S";
		static final String ㄧ般保證人 = "N";
	}

	/** 常用codeType項目 */
	interface CodeTypeItem {
		static final String 額度明細表性質 = "lms1405s02_proPerty";
		static final String 授信科目 = "lms1405m01_SubItem";
		static final String 企業關係 = "Relation_type1";
		static final String 親屬關係 = "Relation_type2";
		static final String 綜合關係_企業 = "Relation_type31";
		static final String 綜合關係_親屬 = "Relation_type32";
		static final String 簽案文件狀態 = "lms7830m01_ctype";
		static final String 授信科目轉會計科目 = "lms1405m01_SubItemToAccounting";
	}

	/** 分行別 */
	interface BrNoType {
		static final String 國內 = "1";
		static final String 國外 = "2";
		static final String 總處 = "3";
		static final String 子銀行 = "4";
	}

	/** 海外分行分行類型 */
	interface obsBrnType {
		static final String 海外總行 = "1";
		static final String 海外一般分行 = "2";
		static final String 海外簡易分行 = "3";
	}

	/** 分行類型代碼 */
	interface unitType {
		static final String 營運中心 = "A";
		static final String 授管處 = "S";
		static final String 泰國以外海外總行 = "Q";
		static final String 泰國海外總行 = "R";

		static final String 一般分行 = "B";
		static final String 簡易分行 = "J";
		static final String 債管處 = "D";
		static final String 稽核處 = "G";
		static final String 徵信處 = "H";
		static final String 海外分行 = "O";
		static final String 國金部 = "K";
		static final String 海外分行當地有總行 = "P";

	}

	/****************************** 以下為業務相關 *********************************/

	/** 擬/已辦 */
	interface seqKind {
		static final String 已辦 = "1";
		static final String 擬辦 = "2";
		static final String 其他 = "3";
		static final String 空白 = "4";
	}

	/** 單位種類 */
	interface branchKind {
		static final String 分行合併字串 = "1";
		static final String 營運中心合併字串 = "2";
		static final String 授管處合併字串 = "3";
		static final String 授管處批覆意見_給分行看的 = "4";
	}

	/** 單位種類 */
	interface branchKind2 {
		static final String 分行 = "1";
		static final String 營運中心 = "2";
		static final String 授管處 = "3";
	}

	/** 金額/設押種類 */
	interface setKind {
		static final String 金額 = "1";
		static final String 設押金額 = "2";
		static final String 解除曝險金額 = "3";
	}

	/** 案件類型定義 */
	interface CaseDefName {
		static final String 案件簽報書 = "L120M01A";
		static final String 額度明細表 = "L140M01A";
		static final String 聯行額度明細表 = "L141M01A";
		static final String 動用審核表 = "L160M01A";
		static final String 修改資料特殊流程 = "L210M01A";
		static final String 簽約未動用 = "L230M01A";
		static final String 企金覆審報告表 = "L170M01A";
		static final String 個金覆審報告表 = "C241M01A";
		static final String 稽核工作底稿 = "L192M01A";
		static final String 企金戶新增增額逾放轉正名單檔 = "L184M01A";
		static final String 企金覆審管理報表 = "L185M01A";
		static final String 企金授信管理報表檔 = "L784M01A";
		static final String 企金覆審名單 = "L180M01A";
		static final String 個金覆審工作底稿主檔 = "C240M01A";
		static final String 個金動用審核表 = "C160M01A";
		static final String 購置房屋擔保放款風險權數檢核表 = "C102M01A";
		static final String 停權解除維護 = "L918M01A";
		static final String 大陸地區控管維護 = "L712M01A";
		static final String 全行額度明細表查詢 = "L785M01A";
		static final String 中小信保整批申請 = "C124M01A";
		static final String 管理報表 = "LMSRPT";
	}

	/** 案件簽報書 */
	interface Casedoc {

		/**
		 * 評等表類型
		 * <p/>
		 * DB=DBU大型企業<br/>
		 * DL=DBU中小型企業<br/>
		 * OU=0BU<br/>
		 * CA=泰國GroupA<br/>
		 * CB=泰國GroupB<br/>
		 * CK=自訂<br/>
		 * CS=消金評等<br/>
		 * NM=Moody<br/>
		 * NS=S&P<br/>
		 * NF=Fitch<br/>
		 * NC=中華信評
		 * 
		 * */
		interface CrdType {
			static final String DBU大型企業 = "DB";
			static final String DBU中小型企業 = "DL";
			static final String 海外 = "OU";
			static final String 境外 = "OB";
			static final String 泰國GroupA = "CA";
			static final String 泰國GroupB = "CB";
			static final String 納閩大企業及聯貸案 = "CC";
			static final String 納閩自貸案 = "CD";
			static final String 納閩金融機構 = "CE";
			static final String 岷行大企業 = "CF";
			static final String 岷行中小企業 = "CG";
			static final String 自訂 = "CK";
			static final String 消金評等 = "CS";
			static final String MOODY = "NM";
			static final String SAndP = "NS";
			static final String Fitch = "NF";
			static final String 中華信評 = "NC";
			static final String FitchTW = "NT";
			static final String KBRA = "NK";
			static final String 未評等 = "NA";
			static final String 澳洲大型企業 = "A0";
			static final String 澳洲中型企業 = "A1";
			static final String 澳洲中小型企業 = "A2";
			static final String 美國大企業 = "CH";
			static final String 美國中小企業 = "CI";
			static final String 英國企業 = "CJ";
			static final String TCRI評等 = "TC";
		}

		/**
		 * 評等表類型
		 * <p/>
		 * DB=DBU大型企業<br/>
		 * DL=DBU中小型企業<br/>
		 * OU=0BU<br/>
		 * CA=泰國GroupA<br/>
		 * CB=泰國GroupB<br/>
		 * CK=自訂<br/>
		 * CS=消金評等<br/>
		 * NM=Moody<br/>
		 * NS=S&P<br/>
		 * NF=Fitch<br/>
		 * NC=中華信評
		 * 
		 * */
		interface CrdTypeM {
			static final String DBU大型企業 = "B";
			static final String DBU中小型企業 = "L";
			static final String OBU = "U";
			static final String 未評等 = "NA";
		}

		/**
		 * <pre>
		 * Mow信評
		 * 			1:DBU大型企業
		 * 			2:DBU中型企業
		 * 			3:DBU中小型企業
		 * 			4:DBU不動產有建案規劃
		 * 			5:DBU專案融資
		 * 			6:DBU本國證券公司
		 * 			8:DBU投資公司一般情況
		 * 			9:DBU租賃公司
		 * 			A:DBU一案建商
		 * 			B:DBU非一案建商(擔保/土融)
		 * 			C:DBU非一案建商(無擔)
		 * 			D:投資公司情況一
		 * 			E:投資公司情況二
		 * 			F:OBU船舶/航空器模型
		 *          G:OBU境外貿易型/控股型
		 * </pre>
		 **/
		interface CrdType2 {
			static final String DBU大型企業 = "M1";
			static final String DBU中型企業 = "M2";
			static final String DBU中小型企業 = "M3";
			static final String DBU不動產有建案規劃 = "M4";
			static final String DBU專案融資 = "M5";
			static final String DBU本國證券公司 = "M6";
			static final String DBU投資公司一般情況 = "M8";
			static final String DBU租賃公司 = "M9";
			static final String DBU一案建商 = "MA";
			static final String DBU非一案建商_擔保_土融 = "MB";
			static final String DBU非一案建商_無擔 = "MC";
			static final String 投資公司情況一 = "MD";
			static final String 投資公司情況二 = "ME";
			static final String OBU船舶_航空器模型 = "MF";
			static final String OBU境外貿易型_控股型 = "MG";
			static final String 國金部實體營運企業 = "MH";
			static final String 國金部金融租賃業 = "MI";
			static final String 信託基金及其他金融工具 = "MJ";
			static final String 澳洲不動產租售業 = "MK";
			static final String 境外中型中小型 = "ML";
			static final String 日本一般企業 = "MM";
			static final String 日本不動產租售業 = "MN";
			static final String 亞太船舶航空器模型 = "MO";
			static final String 亞太貿易型控股型企業模型 = "MP";
			static final String 亞太大型企業模型 = "MQ";
			static final String 亞太中型中小型企業模型 = "MR";
			static final String 亞太租賃業評等表 = "MS";
			static final String 買入企業應收帳款 = "MT";
			static final String 泰子行大型_中型企業 = "MU";
			static final String 泰子行中小型企業 = "MV";
			static final String 澳洲地區一般企業 = "MW";
			static final String 歐洲地區一般企業 = "MX";
			static final String 美國地區一般企業 = "MY";
			static final String DBU_大型企業 = "Ma";
			static final String DBU_中型企業 = "Mb";
			static final String DBU_中小型企業 = "Mc";
			static final String DBU_微型企業 = "Md";
			static final String 不動產開發商 = "Mh";
			static final String 不動產租售商 = "Mi";
			static final String 收益性不動產 = "Mj";
			static final String 高風險商用不動產 = "Mk";
			static final String 免辦 = "NO";
		}

		/** 本案最後批示結果 */
		interface DocRslt {
			static final String 承做 = "1";
			static final String 婉卻 = "2";
		}

		/** 企/個金案件 */
		interface DocType {
			static final String 企金 = "1";
			static final String 個金 = "2";
			static final String 企金個金 = "3";

		}

		/** 授權別 */
		interface DocKind {
			static final String 授權內 = "1";
			static final String 授權外 = "2";
		}

		/** 授權外 - 是否加送會審單位 */
		interface AreaChk {
			static final String 無 = "1";
			static final String 送會簽 = "2";
			static final String 送審查 = "3";
			static final String 送會簽審查 = "4";
			// (108)第 3230 號
			static final String 送初審 = "5";
			static final String 送初審審查 = "6";
		}

		/** 授權內-授權等級 */
		interface AuthLvl {
			static final String 分行授權內 = "1";
			static final String 總行授權內 = "2";
			static final String 營運中心授權內 = "3";
			// static final String 母行授權內 = "4";
			static final String 分行授權外 = "5";
		}

		/** 案件別 */
		interface DocCode {
			static final String 一般 = "1";
			static final String 其他 = "2";
			static final String 陳復陳述案 = "3";
			static final String 異常通報 = "4";
			static final String 團貸案件 = "5";
		}

		/** 借款人有無赴大陸投資 */
		interface InvMFlag {
			static final String 有 = "1";
			static final String 無 = "2";
			static final String 不適用 = "3";
		}

		/** 授管處提會註記 */
		interface HqMeetFlag {
			static final String 授審會 = "1";
			static final String 逾審會 = "2";
			static final String 常董會 = "3";
			static final String 審計委員會 = "4";
			static final String 特殊分行授審會 = "A";
			static final String 特殊分行逾審會 = "B";
			static final String 特殊分行常董會 = "C";
			static final String 特殊分行審計委員會 = "D";
		}

		/** 頁籤代碼 */
		// 不符合授信政策:J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
		// J-108-0243 微型企業
		// J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		// J-109-0369_05097_B1001 e-Loan企金授權外簽報書引進國家風險限額資料
		interface bookMark {
			static final String 額度批覆表 = "#book1";
			static final String 母行法人提案意見 = "#book2";
			static final String 會簽_會議決議 = "#book3";
			static final String 授管處意見 = "#book4";
			static final String 國金部_營運中心會簽意見 = "#book5";
			static final String 營運中心意見 = "#book6";
			static final String 常董稿附加檔案 = "#book7";
			static final String 主管批示 = "#book8";
			static final String 異常通報表 = "#book99";
			static final String 額度明細表 = "#book98";
			static final String 說明 = "#page05";
			static final String 相關文件 = "#page08";
			static final String 綜合評估 = "#page07";
			static final String 不符合授信政策 = "#book22";
			static final String 微型企業 = "#book23";
			static final String AML_CFT = "#book20";

			static final String 案由 = "#book10";
			static final String 補充說明 = "#book11";
			static final String 還款來源國說明 = "#book12";
			static final String 國家風險限額說明 = "#book24";
			static final String RPA資料查詢 = "#book28";

		}

		/**
		 * 婉卻控管種類 1.維持控管, 2.警示不控管, D.刪除控管
		 **/
		interface rejtCase {
			static final String 維持控管 = "1";
			static final String 警示不控管 = "2";
			static final String 刪除控管 = "D";
		}

		/**
		 * 異常通報狀態Y.已解除, N.未解除 J-105-0179-001 Web
		 * e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
		 **/
		interface abnormalStatus {
			static final String 已解除 = "Y";
			static final String 未解除 = "N";
		}

		/** 分析與評估種類 */
		interface L120s01gType {
			static final String 營運概況分析與評估 = "1";
			static final String 財務狀況分析與評估 = "2";
		}

		/** 企金營收獲利財務狀況種類 */
		interface L120s01eKind {
			static final String 營運概況 = "1";
			static final String 財務狀況 = "2";
		}

		/** 區部別 */
		interface typCd {
			static final String 無 = "0";
			static final String DBU = "1";
			static final String OBU = "4";
			static final String 海外 = "5";
		}

		/**
		 * <pre>
		 * 配偶欄
		 * 			無配偶 |0
		 * 			列於本欄|1
		 * 			同本案借保人|2
		 * </pre>
		 * */
		interface L120s01hDataName {
			static final String 無配偶 = "0";
			static final String 列於本欄 = "1";
			static final String 同本案借保人 = "2";
		}

		/**
		 * 職業別大項
		 * 
		 * <pre>
		 * 			01.政府/公共事業
		 * 			02.教育業
		 * 			03.軍/警/消防
		 * 			04.醫療保健業
		 * 			05.金融保險業
		 * 			06.資訊及通訊業
		 * 			07.貿易/自營
		 * 			08.服務業
		 * 			09.製造業
		 * 			10.營造業
		 * 			11.運輸倉儲業
		 * 			12.自由業
		 * 			13.專業事務所
		 * 			14.農、林、漁、牧、礦業
		 * </pre>
		 **/
		interface L120s01iJobType {
			static final String 政府_公共事業 = "01";
			static final String 教育業 = "02";
			static final String 軍_警_消防 = "03";
			static final String 醫療保健業 = "04";
			static final String 金融保險業 = "05";
			static final String 資訊及通訊業 = "06";
			static final String 貿易_自營 = "07";
			static final String 服務業 = "08";
			static final String 製造業 = "09";
			static final String 營造業 = "10";
			static final String 運輸倉儲業 = "11";
			static final String 自由業 = "12";
			static final String 專業事務所 = "13";
			static final String 農_林_漁_牧_礦業 = "14";
		}

		/**
		 * <pre>
		 * 使用信用卡循環信用或現金卡情形
		 * (複選) A或B或AB
		 * 最近一年有使用兩家(含)以上銀行之信用卡循環信用紀錄| A
		 * 最近一年有使用兩家(含)以上銀行之現金卡紀錄| B
		 * </pre>
		 * */
		interface L120s01jCredit {
			static final String 最近一年有使用兩家_含_以上銀行之信用卡循環信用紀錄 = "A";
			static final String 最近一年有使用兩家_含_以上銀行之現金卡紀錄 = "B";
			static final String 兩者都有 = "AB";
		}

		/**
		 * <pre>
		 * 法人或自然人
		 * 			N：自然人
		 * 			C：法人
		 * 			A：主管機關核准作無擔保授信
		 * 			B：官股代表
		 * </pre>
		 **/
		interface L120s01aRenCd {
			static final String 自然人 = "N";
			static final String 法人 = "C";
			static final String 主管機關核准作無擔保授信 = "A";
			static final String 官股代表 = "B";
		}

		/**
		 * 授權類別 1.編製/移送 2.代編 3.傳簽 4.傳送(授權)
		 * */
		interface L120a01aAuthType {
			static final String 編製_移送 = "1";
			static final String 代編 = "2";
			static final String 傳簽 = "3";
			static final String 傳送_授權 = "4";
		}

		/**
		 * <pre>
		 * 項目類別
		 * 			1.產銷方式
		 * 			2.產業概況表相關作業
		 * 			3.其他
		 * 			4.綜合評估及敘作理由(併於主表)
		 * 			E.綜合評估及敘作理由(獨立印表)
		 * 			5.補充說明
		 * 			6.主管批示
		 * 			7.營運中心說明及意見
		 * 			8.營運中心會議決議
		 * 			9.國金部/營運中心會簽意見
		 * 			A.授管處補充說明
		 * 			B.授管處審查意見
		 * 			C.授管處會簽意見(國內授信用)
		 * 			※(國外部/國金部/金控總部分行)
		 * 			D.中長期財務預估Freeformat
		 * 			F.借戶集團與本行授信往來情形
		 * 			G.借戶所屬行業之本行曝險額
		 * 			H.本案『有/無』偏離本行授信政策/規定或建議
		 * 			I.實地訪廠
		 * 			J.法律搜索
		 * 			K.香港_信貸續約之內部信用評等及分類
		 * 			L.上一次授信審視日期
		 *          O.還款來源國現況說明  J-106-0085
		 *          P.不符合授信政策說明  J-106-0087
		 *          Q.誠信經營評估 J-106-0213-001
		 * </pre>
		 * */
		interface L120m01dItemType {
			static final String 產銷方式 = "1";
			static final String 產業概況表相關作業 = "2";
			static final String 其他 = "3";
			static final String 綜合評估及敘作理由_併於主表 = "4";
			static final String 綜合評估及敘作理由_獨立印表 = "E";
			static final String 補充說明 = "5";
			static final String 主管批示 = "6";
			static final String 營運中心說明及意見 = "7";
			static final String 營運中心會議決議 = "8";
			static final String 國金部_營運中心會簽意見 = "9";
			static final String 授管處補充說明 = "A";
			static final String 授管處審查意見 = "B";
			static final String 授管處會簽意見_國內授信用 = "C";
			static final String 中長期財務預估Freeformat = "D";
			static final String 香港_借戶集團與本行授信往來情形 = "F";
			static final String 香港_借戶所屬行業之本行曝險額 = "G";
			static final String 香港_本案有無偏離本行授信政策規定或建議 = "H";
			static final String 香港_實地訪廠 = "I";
			static final String 香港_法律搜索 = "J";
			static final String 香港_信貸續約之內部信用評等及分類 = "K";
			static final String 香港_上一次授信審視日期 = "L";
			static final String 赤道原則評估 = "M";
			static final String 合理性分析表補充說明 = "N";
			static final String 還款來源國現況說明 = "O";
			static final String 不符合授信政策說明 = "P";
			static final String 誠信經營評估 = "Q";
			static final String 敘做理由 = "R";
			static final String 國家風險限額說明 = "S";
			static final String 綜合評估及敘作理由_補充說明 = "T";
			static final String 綜合評估及敘作理由_本案理由 = "U";
			static final String 人頭戶指標態樣說明 = "V";
			static final String 決策審核表備註說明 = "W";
			static final String 觸及人頭戶態樣警示案件敘做說明 = "X"; // select * from
														// com.bcodetype where
														// codetype='cls1141_headAccountCheckItem'
			static final String 常董會案由 = "Y";
			static final String 常董會中文案由 = "Z";
			static final String 比對與其他借款人相同資訊描述 = "a";
			static final String 比對與其他借款人相同資訊說明 = "b";
			static final String 觸及疑似本行人頭戶或代辦案件黑名單說明 = "c";
		}

		/**
		 * 借款用途
		 * 
		 * <pre>
		 * (多選)  1|2|3...
		 * 			1.購料週轉金
		 * 			2.營運週轉金
		 * 			3.其他
		 * 			A.購置住宅
		 * 			B.修繕房屋
		 * 			C.週轉(含行家理財中長期)
		 * 			D.購置汽車
		 * 			E.購置停車位貸款
		 * 			F.出國留學
		 * 			G.投資理財
		 * 			H.購置自用耐久性消費品
		 * 			I.出國旅遊
		 * 			J.子女教育
		 * 			K.繳稅
		 * 			L.青年創業貸款
		 * </pre>
		 * */
		interface purpose {
			static final String 購料週轉金 = "1";
			static final String 營運週轉金 = "2";
			static final String 其他 = "3";
			static final String 購置住宅 = "A";
			static final String 修繕房屋 = "B";
			static final String 週轉_含行家理財中長期 = "C";
			static final String 購置汽車 = "D";
			static final String 購置停車位貸款 = "E";
			static final String 出國留學 = "F";
			static final String 投資理財 = "G";
			static final String 購置自用耐久性消費品 = "H";
			static final String 出國旅遊 = "I";
			static final String 子女教育 = "J";
			static final String 繳稅 = "K";
			static final String 青年創業貸款 = "L";
			static final String 生活安養所需資金 = "M";
			static final String 個人週轉金 = "N";
			static final String 個人消費性用途 = "O";
			static final String 企業員工認購股票 = "P";
		}

		/**
		 * <pre>
		 * 還款財源
		 * 	(多選)  1|2|3...
		 * 			1.營業收入
		 * 			2.盈餘及折舊
		 * 			3.其他
		 * 			A.薪資收入
		 * 			B.營利收入
		 * 			C.投資收入
		 * 			D.租金收入
		 * 			E.利息收入
		 * </pre>
		 * */
		interface resource {
			static final String 營業收入 = "1";
			static final String 盈餘及折舊 = "2";
			static final String 其他 = "3";
			static final String 薪資收入 = "A";
			static final String 營利收入 = "B";
			static final String 投資收入 = "C";
			static final String 租金收入 = "D";
			static final String 利息收入 = "E";
		}

		/** 授審會/催收會 */
		interface MeetingType {
			static final String 授審會 = "1";
			static final String 逾審會_催收會 = "2";
			static final String 營運中心 = "3";
		}

		/**
		 * <pre>
		 * 與借款人關係 
		 * 	(多選)
		 * 			借戶 | 1
		 * 			借戶負責人 | 2
		 * 			集團企業合計 | 3
		 * 			關係企業合計 | 4
		 * 			集團企業 | 5
		 * 			關係企業 | 6
		 * </pre>
		 * */
		interface L120s04aCustRelation {
			static final String 借戶 = "1";
			static final String 借戶負責人 = "2";
			static final String 集團企業合計 = "3";
			static final String 關係企業合計 = "4";
			static final String 集團企業 = "5";
			static final String 關係企業 = "6";
		}

		/**
		 * <pre>
		 * 相關文件類別 
		 * 			1.徵信報告書
		 * 			2.資信簡表
		 * 			3.擔保品估價報告書
		 * 			4.新增連保人
		 * 			5.最新集團企業授信往來情形
		 * 			6.個金簽報書
		 * 			7.土建融案檢視清單
		 * 			8.同一關係人
		 * 			9.小放會會議記錄
		 * </pre>
		 * */
		interface L120m01eDocType {
			static final String 徵信報告書 = "1";
			static final String 資信簡表 = "2";
			static final String 擔保品估價報告書 = "3";
			static final String 新增連保人 = "4";
			static final String 最新集團企業授信往來情形 = "5";
			static final String 個金簽報書 = "6";
			static final String 土建融案檢視清單 = "7";
			static final String 同一關係人 = "8";
			static final String 小放會會議記錄 = "9";
			static final String 團貸母戶額度明細表 = "A";
		}

		/** 授審會/逾審會 */
		interface L120m01hMeetType {
			static final String 授審會 = "1";
			static final String 逾審會_催收會 = "2";
			static final String 營運中心 = "3";
		}

		/** 資本適足率信保/非信保 */
		interface L120s03aCrdFlag {
			static final String 信保 = "1";
			static final String 非信保 = "2";
		}

		/** 關係戶於本行各項業務往來文件產生方式 */
		interface L120s04aCreateBY {
			static final String 系統產生 = "SYS";
			static final String 人工產生 = "PEO";
		}

		/** 關係戶於本行各項業務往來簽報書列印註記 */
		interface L120s04aPrtFlag {
			static final String 要列印 = "1";
			static final String 不列印 = "2";
		}

		/** 黑名單 J-106-0029-002 洗錢防制-新增洗錢防制頁籤 */
		interface L120s09aBlackListCode {
			static final String 未列於黑名單 = "00";
			static final String 是黑名單 = "02";
			static final String 可能是黑名單 = "04";
		}

		/** 黑名單 J-106-0029-002 洗錢防制-新增洗錢防制頁籤 */
		interface L120s09aBlackListShow {
			static final String 未列於黑名單 = "◎";
			static final String 是黑名單 = "★";
			static final String 可能是黑名單 = "△";
			static final String 拒絕交易 = "╳";
		}

		/** 洗錢防制控管對象 J-106-0029-002 洗錢防制-新增洗錢防制頁籤 */
		// 高階管理人員 = "A":J-107-0070-001 將「高階管理人員」納入應查詢比對黑名單之對象。
		interface L120s09aBlackListCtlTarget {
			static final String 借戶 = "1";
			static final String 共同借款人 = "2";
			static final String 負責人 = "3";
			static final String 連保人 = "4";
			static final String 擔保品提供人 = "5";
			static final String 關係企業 = "6";
			static final String 實質受益人 = "7";
			static final String 一般保證人 = "8";
			static final String 應收帳款買方無追索 = "9";
			static final String 高階管理人員 = "10";
			static final String 具控制權人 = "11";
		}
		
		/** J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊  */
		interface L120s09aCmfwarnpResultCode {
			static final String 有 = "1";
			static final String 無 = "2";
			static final String 不適用 = "3";
		}

		/** 利害關係人列印模式 */
		interface L120s06aPrint {
			static final String 單獨一頁 = "1";
			static final String 合併共頁 = "2";
		}

		/** 利害關係人類別 */
		interface L120s06bType {
			static final String 本案授信戶 = "1";
			static final String 對照授信戶 = "2";
		}

		/** 利害關係人項目類別 */
		interface L120s06bItemType {
			static final String 利_費_率 = "2";
			static final String 擔保品 = "3";
			static final String 期限說明 = "4";
			static final String 其他敘做條件 = "5";
			static final String 現請額度 = "6";
			static final String 動用期限 = "7";
			static final String 聯貸總額度 = "8";
			static final String 動用先決條件 = "9";
			static final String 增額條款 = "A";
			static final String 償還方式 = "B"; // J-111-0182 個金授信條件對照表
											// 新增比對欄位：償還方式、核准日期
			static final String 核准日期 = "C";
			static final String 本息償還方式 = "D"; // J-112-0124 授信條件對照表
		}

		/** 審核層級類別 */
		interface CaseLvl {
			static final String 常董會權限 = "1";
			static final String 常董會權限簽奉總經理核批 = "2";
			static final String 常董會權限簽准由副總經理核批 = "3";
			static final String 利費率變更案件由總處經理核定 = "4";
			static final String 屬常董會授權總經理逕核案件 = "5";
			static final String 總經理權限內 = "6";
			static final String 副總經理權限 = "7";
			static final String 處長權限 = "8";
			static final String 其他 = "9";
			static final String 董事會權限 = "A";
			static final String 營運中心營運長 = "B";
			static final String 利費率變更案件由董事長核定 = "C";
			static final String 個金處經理權限 = "D";
		}

		interface SimplifyFlag {
			static final String 一般消金 = "A"; // J-108-0318
			static final String 卡友信貸 = "B"; // J-108-0296
			static final String 勞工紓困 = "C"; //
			static final String 歡喜信貸 = "D"; //
			static final String 快速審核信貸 = "E"; //
		}

		static final String[] 財務狀況不需加百分比欄位 = new String[] { "27", "28", "29",
				"2a", "2b", "2c", "2d", "2e", "2f", "2i", "2j" };

		static final String 往來彙總用全行 = "999";

		// 適用方案
		// J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
		// J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
		// J-110-0458 企金授權內其他 - 「簡易簽報」選項，適用方案「LIBOR退場變更利率條件簡易簽報」
		// J-112-0148 疫後振興
		interface caseType {
			static final String 無 = "000";
			static final String 小規模營業人 = "001";
			static final String 青年創業及啟動金貸款 = "002";
			static final String 國發基金協助新創事業紓困融資加碼方案600萬 = "003";
			static final String 國發基金協助新創事業紓困融資加碼方案2600萬 = "004";
			static final String 微型企業 = "005";
			static final String 區域營運中心_授信審查處權限內異常通報 = "00A";
			static final String LIBOR退場變更利率條件簡易簽報 = "AAA";
			static final String EUROYEN_TIBOR退場變更利率條件簡易簽報 = "AAB";
			static final String 疫後振興F02 = "F02";
			static final String 疫後振興F02_無利息補貼 = "F07";
		}

		// J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
		interface caseTypeA {
			static final String 無 = "000";
			static final String 小規模營業人 = "A01";
		}

		// J-109-0370_05097_B1002 配合企金一般簽報書格式修訂，新增、調整簽報作業相關內容
		// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
		/** 主要申請敘作內容項目類別 */
		interface L120s16bItemType {
			static final String 利_費_率 = "2";
			static final String 擔保品 = "3";
			static final String 期限說明 = "4";
			static final String 其他敘做條件 = "5";
			static final String 現請額度 = "6";
			static final String 動用期限 = "7";
			static final String 聯貸總額度 = "8";
			static final String 動用先決條件 = "9";
			static final String 增額條款 = "A";
			static final String 連保人參考 = "B";
			static final String 說明 = "C";
		}

		// J-110-0358 海外授權外改版
		/**
		 * 通常使用於setVer，檢核時的條件可能還是寫死版本，所以變更版本時記得用 getVer() 搜尋 L120M01I_Ver 跟
		 * L120S01Q_Ver 一剛開始都是控制 赤道原則版本 所以應該要一致 L120M01I_Ver => 簽報書版本
		 * L120M01I_PrintVer => 因應國內海外列印不同時改版生效而新增的欄位 L120S01Q_Ver => 赤道原則版本
		 **/
		// J-111-0551 在途授信額度
		/**
		 * 配合授權外簽報書改版&「授信信用風險管理」遵循檢核表 LMS1201R30 L120M01I_PrintVer 改為 04
		 * 因之前很多判斷是用Util.equals 所以拆分版本歷程參數
		 **/
		// J-112-0505 社會責任改版
		// J-113-0442 新增赤道原則相關欄位 VER08
		static final String L120M01I_Ver = "03";
		static final String L120M01I_PrintVer = "04";
		static final String L120S01Q_Ver = "08";
        static final String L120S01Q_Ver03 = "03";
		static final String L120S01Q_Ver04 = "04";
		static final String L120S01Q_Ver05 = "05";
		static final String L120S01Q_Ver06 = "06";
		static final String L120S01Q_Ver07 = "07";
		static final String L120S01Q_Ver08 = "08";
		static final String L120M01I_Ver03 = "03";
		static final String L120M01I_PrintVer03 = "03";
		static final String L120M01I_PrintVer04 = "04";
	}

	/** 額度明細表 */
	interface Cntrdoc {
		/** 限額控管相關代碼 */
		interface NeedControl {
			static final String 行業別代碼 = "L";
			static final String 集團代碼 = "M";
			static final String 國別代碼 = "N";
			static final String 大陸地區授信 = "N1";
			static final String 區域代碼 = "O";
			static final String 單一授信戶 = "P";
			static final String 產品種類 = "Q";
			static final String 土地融資 = "Q1";
			static final String 建築融資 = "Q2";

		}

		/** 執行的動作 */
		interface ACTION {
			static final String 呈主管 = "T";
			static final String 覆核 = "M";
		}

		/** 檢核規則 */
		interface CHKYN {
			static final String 已計算 = "Y";
			static final String 通過檢核 = "N";
			static final String 尚未通過檢核 = null;
		}

		/** 科子目總類 */
		interface LMTTYPE {
			static final String 科子目限額 = "1";
			static final String 科子目合併限額 = "2";
		}

		/** 額度明細表種類 */
		interface ItemType {
			static final String 額度明細表 = "1";
			static final String 額度批覆表 = "2";
			static final String 泰國額度批覆表 = "3";
		}

		/** 額度明細表複製來源種類 */
		interface DataSrc {
			static final String 國內個金舊案 = "0";
			static final String 新增額度明細表 = "1";
			static final String 複製額度明細表 = "2";
			// 即聯行額度明細表
			static final String 轉入額度明細表 = "3";
			static final String 條件續約變更產生 = "4";
			// 5.帳務 (由帳務系統資訊轉入之資料LN.LNF)
			static final String 個金帳務 = "5";
			// 6.核准 (由R6轉入之資料)
			static final String 個金核准 = "6";
			// 7.動用 (由mis.Elf500轉入之資料)
			static final String 個金動用 = "7";
			// 8.帳務銷戶
			static final String 帳務銷戶 = "8";
			// 9.核准取銷
			static final String 核准取銷 = "9";
		}

		/** 額度性質 */
		interface sbjProperty {
			static final String 擔保 = "S";
			static final String 無擔保 = "N";
		}

		/** 性質 */
		interface Property {
			static final String 新做 = "1";
			static final String 續約 = "2";
			static final String 變更條件 = "3";
			static final String 流用 = "4";
			static final String 增額 = "5";
			static final String 減額 = "6";
			static final String 不變 = "7";
			static final String 取消 = "8";
			static final String 展期 = "9";
			static final String 紓困 = "10";
			static final String 提前續約者 = "11";
			static final String 協議清償 = "12";
			static final String 報價 = "13";
		}

		/** 額度控管種類 */
		interface snoKind {
			static final String 一般 = "10";
			static final String 信保 = "20";
			static final String 聯貸 = "30";
			static final String 合作母 = "40";
			static final String 合作子 = "41";
			static final String 個人戶一般 = "51";
			static final String 應收帳款買方 = "60";
			static final String 應收帳款賣方一般戶 = "61";
			static final String 應收帳款賣方聯貸母戶 = "62";
			static final String 應收帳款賣方聯貸子戶 = "63";
		}

		/** 聯行攤貸比率計算方式 */
		interface shareType {
			static final String 以金額計算 = "1";
			static final String 以比例計算 = "2";

		}

		/** 現請額度－是否循環使用 */
		interface ReUse {
			static final String 不循環使用 = "1";
			static final String 循環使用 = "2";

		}

		/** 額度敘述說明檔 itemType */
		// J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 --應收帳款買方備註 =
		// "H"
		interface l140m01bItemType {
			static final String 限額條件 = "1";
			static final String 利費率 = "2";
			static final String 擔保品 = "3";
			static final String 其他敘做條件 = "4";
			static final String 敘做條件異動情形 = "5";
			static final String 附表第一頁 = "6";
			static final String 附表第二頁 = "7";
			static final String 附表第三頁 = "8";
			static final String 其他敘做條件_動撥提示用語 = "9";
			static final String 總處核定意見 = "A";
			static final String 董事會意見 = "B";
			static final String 國內個金聯貸說明 = "C";
			static final String 國內個金團貸借保人說明 = "D";
			static final String 關聯戶 = "E";
			static final String 引入評等質化內容 = "F";
			static final String 不符合授信政策 = "G";
			static final String 應收帳款買方備註 = "H";
			static final String 央行購置註記 = "X";
			static final String 大陸地區授信業務控管註記 = "Y";
			static final String[] 海外企金項目 = new String[] { 限額條件, 利費率, 擔保品,
					其他敘做條件, 敘做條件異動情形, 附表第一頁, 附表第二頁, 附表第三頁, 其他敘做條件_動撥提示用語,
					總處核定意見, 董事會意見, 大陸地區授信業務控管註記, 關聯戶 };
			// J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 --應收帳款買方備註
			// =
			// "H"
			static final String[] 企金項目 = new String[] { 限額條件, 利費率, 擔保品, 其他敘做條件,
					敘做條件異動情形, 附表第一頁, 附表第二頁, 附表第三頁, 其他敘做條件_動撥提示用語, 總處核定意見,
					董事會意見, 央行購置註記, 大陸地區授信業務控管註記, 應收帳款買方備註 };
			static final String[] 個金項目 = new String[] { 擔保品, 其他敘做條件, 敘做條件異動情形,
					附表第一頁, 其他敘做條件_動撥提示用語, 國內個金聯貸說明, 國內個金團貸借保人說明, 央行購置註記,
					總處核定意見, 大陸地區授信業務控管註記 };

		}

		/** 擔保維持率種類 */
		interface l140m01aMRateType {
			static final String 標準 = "0";
			static final String 自訂 = "1";
		}

		/**
		 * 擔保維持率種類 J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
		 * */
		interface l140s04aItemType {
			static final String 不符合授信政策 = "1";
		}

		/**
		 * 額度明細表特殊類別J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
		 * */
		// J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
		interface genL140m01aCaseType {
			static final String 小規模營業人額度明細表 = "IS_SMALL_BUSS";
			static final String 新增經濟部B方案營運週轉金搭配央行AB專案明細表 = "IS_RESCUE_A02_CBCREFIN_A_B";
			static final String 青年創業及啟動金貸款 = "IS_LNTYPE_61";
			static final String 國發基金協助新創事業紓困融資加碼方案 = "IS_RESCUE_A07_CBCREFIN_B";
			static final String 疫後振興額度明細表 = "IS_RESCUE_F02";
			static final String 疫後振興額度明細表_無利息補貼 = "IS_RESCUE_F07";
		}

	}

	/** 動審表 */
	interface Usedoc {

		/** 聯貸性質 */
		interface unioType {
			static final String 同業聯貸 = "1";
			static final String 自行聯貸 = "2";
		}

		/** 上傳性質 */
		interface upType {
			static final String 新作 = "N";
			static final String 增額 = "A";
			static final String 減額 = "D";
			static final String 取消 = "C";
		}

		/** 案件性質 */
		interface caseType {
			static final String 同業聯貸主辦 = "1";
			static final String 同業聯貸主辦含自行聯貸 = "2";
			static final String 同業聯貸參貸 = "3";
			static final String 同業聯貸參貸含自行聯貸 = "4";
			static final String 自行聯貸 = "5";
			static final String 一般貸款 = "6";
			static final String 合作業務母戶 = "7";
			static final String 合作業務子戶 = "8";
			static final String 應收帳款 = "9";
			static final String 應收帳款聯貸 = "10";

		}

		/** 動審表種類 */
		interface caseType2 {
			static final String 一般 = "1";
			static final String 團貸 = "2";
			static final String 整批匯入 = "3";
		}

		/** 稽核項目 種類 */
		interface itemType {
			static final String 全行共同項目 = "1";
			static final String 當地特殊規定項目 = "2";
			static final String 自訂項目 = "3";
			static final String 國內企金 = "4";
			static final String 國內個金 = "5";
		}

		/** 稽核項目 */
		interface checkItem {
			static final String 免收 = "0";
			static final String 已收 = "1";
			static final String 未收 = "2";
		}

		/** 引進來源 */
		interface dataDrc {
			static final String 簽報書 = "1";
			static final String 聯行額度明細表 = "2";
		}

		/** 取得授信契約書期別 */
		interface TType {
			static final String 短期 = "1";
			static final String 中長期 = "2";
		}

		/** 動審表稽核項目 */
		// static final Integer[] 動審表必填項目 = new Integer[] { 1, 3, 10, 11, 12,
		// 13,
		// 14, 15, 17, 22 };
		static final Integer[] 動審表必填項目 = new Integer[] { 1, 3, 10, 11, 12, 13,
				14, 16, 21 };

	}

	/** 小放會 */
	interface Meeting {

		/** 建檔來源 */
		interface CreateType {
			static final String 海外 = "1";
			static final String 企金 = "2";
			static final String 消金 = "3";
		}
	}

	/** 修改資料特殊流程 */
	interface editDoc {

		/** 變動前、變動後 */
		interface chanFlag {
			static final String 變動前 = "1";
			static final String 變動後 = "2";
		}

		/** 聯貸種類 */
		interface caseType {
			static final String 同行聯貸 = "1";
			static final String 同業聯貸 = "2";
			static final String 同業聯貸和同行聯貸 = "3";
		}
	}

	/** 工作底稿 */
	interface ShtType {
		static final String 授信業務工作底稿 = "1";
		static final String 房貸業務工作底稿 = "2";
		static final String 團體消貸工作底稿 = "3";
	}

	/** 新作/增額(覆審用) */
	interface NEWADD {
		static final String 新作 = "N";
		static final String 增額 = "C";
		static final String 逾放轉正 = "R";
	}

	/** 上次覆審評等(企金覆審用) */
	interface Type {
		static final String 無資料_C = "XC";
		static final String 無資料_M = "XM";
	}

	/** 信用評等(企金覆審用) */
	interface crdType {
		static final String DBU大型企業 = "DB";
		static final String DBU中小型企業 = "DL";
		static final String 海外 = "OU";
		static final String 境外 = "OB";
		static final String 未評等 = "NA";
	}

	interface crdTypeC {
		static final String 泰國GroupA = "CA";
		static final String 泰國GroupB = "CB";
		static final String 自訂 = "CK";
		static final String 消金評等 = "CS";
	}

	interface crdTypeN {
		static final String Moody = "NM";
		static final String SP = "NS";
		static final String Fitch = "NF";
		static final String 中華信評 = "NC";
		//J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
		static final String FitchTW = "NT";
		static final String KBRA = "NK";
	}

	/**
	 * 信用評等(企金覆審用) select CODEVALUE,CODEDESC from com.bcodetype where
	 * CODETYPE='lms1705s01_crdType2' and LOCALE='zh_TW'
	 */
	interface mowType {
		static final String DBU大型企業 = "1";
		static final String DBU中型企業 = "2";
		static final String DBU中小型企業 = "3";
		static final String DBU不動產有建案規劃 = "4";
		static final String DBU專案融資 = "5";
		static final String DBU本國證券公司 = "6";
		static final String DBU投資公司一般情況 = "8";
		static final String DBU租賃公司 = "9";
		static final String DBU一案建商 = "A";
		static final String DBU非一案建商 = "B";
		static final String DBU非一案建商無擔 = "C";
		static final String 投資公司情況一 = "D";
		static final String 投資公司情況二 = "E";
		static final String OBU境外船舶 = "F";
		static final String 免辦 = "G";
	}

	/** 管理報表(覆審用) */
	interface rRptType {
		static final String 逾期未覆審名單 = "1";
		static final String 企金戶未出現於覆審名單 = "2";
		static final String 撥貸後半年內辦理覆審檢核表 = "3";
		static final String 授信覆審明細檢核表 = "4";
	}

	/** 取號用 */
	interface WORD {
		static final String 覆審 = "覆審";
		static final String 授 = "授";
	}

	/** 覆審報告表 業務及財務 */
	interface ratioNo {
		static final String 負債比率 = "20";
		static final String 流動比率 = "11";
		static final String 速動比率 = "12";
		static final String 固定長期適合率 = "22";
	}

	/** 覆審報告表 業務及財務 */
	interface CREATEBY {
		static final String 每月產生 = "1";
		static final String 指定年月查詢 = "2";
	}

	/** 管理報表 */
	interface RPTREPORT {
		/** 營運中心 */
		interface OPERATIONS {
			static final String 北一區營運中心 = "931";
			static final String 北二區營運中心 = "932";
			static final String 桃竹苗區營運中心 = "933";
			static final String 中區營運中心 = "934";
			static final String 南區營運中心 = "935";
		}

		/**
		 * 授信契約已逾期控制表 已敘做授信案件清單 信保案件未動用屆期清單 授信契約產生主辦聯貸案一覽表 授信案件統計表
		 * 營運中心每日授權外授信案件清單 常董會及申報案件統計表 各級授權範圍內承做授信案件統計表 營業單位授信報案考核彙總表
		 */
		interface REPORTTYPE {
			static final String 授信契約已逾期控制表 = "1";
			static final String 已敘做授信案件清單 = "2";
			static final String 信保案件未動用屆期清單 = "3";
			static final String 授信契約產生主辦聯貸案一覽表 = "4";
			static final String 授信案件統計表 = "5";
			static final String 營運中心每日授權外授信案件清單 = "6";
			static final String 常董會及申報案件統計表 = "7";
			static final String 各級授權範圍內承做授信案件統計表 = "8";
			static final String 營業單位授信報案考核彙總表 = "9";
			static final String 分行授信淨增加額度統計表 = "10";
		}

		/** 國內企金 */
		/**
		 * LMS180R42:J-108-0040_05097_B1001
		 * Webe-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
		 * 
		 * 
		 * LMS180R45:J-108-0107_05097_B1001 國內分行新核准往來企金客戶數統計表(按分行列表)
		 * LMS180R14Q:J-108-0192_05097_B1001 Web e-Loan企金授信新增每季海外營業單位授信報案考核彙總表
		 * LMS180R48:J-107-0342_05097_B1003 Web e-Loan授信系統新增覆審考核相關報表
		 * LMS180R53:J-109-0132_05097_B1001
		 * e-Loan授信系統新增「法令遵循自評檢核表」之抽測筆數所需之各檢核項目授信案件明細報表。
		 * LMS180R60:J-109-0519_05097_B1001 Web
		 * e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單 J-110-0018_05097_B1001 Web
		 * e-Loan簽報書額度明細表中增列「兆豐百億挺你專案」及「協助農地工廠合法化融資貸款」兩項專案，並產生統計報表
		 * J-110-0038_05097_B1001 Web e-Loan企金額度明細表新增「本案是否屬110年行銷名單來源客戶」並產生統計報表
		 * J-110-0049_05097_B1001 Web e-Loan企金授信增加「境內法人於國際金融業務分行辦理外幣授信業務報表」
		 * 
		 * J-110-0402_05097_B1001 Web e-Loan企金新增國內分行承作海外信保承作情況之臨時性報表
		 * LMS180R72:J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
		 * LMS180R73:J-111-0411_05097_B1002 Web e-Loan企金授信新增不動產授信例外管理相關功能
		 */
		interface DOCTYPE1 {
			static final String 已敘做授信案件清單 = "LMS180R01";
			static final String 營運中心已敘做授信案件清單 = "LMS180R02A";
			static final String 信保案件未動用屆期清單 = "LMS180R10";
			static final String 授信契約已逾期控制表 = "LMS180R05";
			static final String 授信契約產生主辦聯貸案一覽表 = "LMS161T02";
			static final String 授信案件統計表 = "LMS180R11";
			static final String 金融機構辦理振興經濟非中小企業專案貸款 = "LMS180R12";
			static final String 企業自行申請展延案件案件統計表 = "LMS180R13";
			static final String 營業單位授信報案考核彙總表 = "LMS180R14";
			static final String 營業單位授信報案考核彙總表_季報 = "LMS180R14Q";
			static final String 營運中心每日授權外授信案件清單 = "LMS180R15";
			static final String 常董會報告事項彙總及申報案件數統計表 = "LMS180R16";
			static final String 本行各營業單位各級授權範圍內承做授信案件統計表 = "LMS180R17";
			static final String 分行授信淨增加額度統計表 = "LMS180R18";
			static final String 企金逾期未覆審名單 = "LMS180R19";
			static final String 企金逾期未覆審名單_董事會授權 = "LMS180R19B";
			static final String 企金戶未出現於覆審名單 = "LMS180R20";
			static final String 撥貸後半年內辦理覆審檢核表 = "LMS180R21";
			static final String 授信覆審明細檢核表 = "LMS180R22";
			static final String 覆審考核表 = "LMS180R23";
			static final String 覆審考核表_董事會授權 = "LMS180R23B";
			static final String 企金授信覆審頻率3次含以上明細表 = "LMS180R24";
			static final String 授信業務異常通報月報 = "LMS180R25";
			static final String 振興經濟非中小企業專案貸款暨信用保證要點執行情形調查表 = "LMS180R26";
			static final String 授信異常通報案件報送統計表 = "LMS180R27";
			static final String 覆審經理記點統計明細表 = "LMS180R28";
			static final String 營業單位辦理對私募基金旗下投資事業敘做授信案件清單 = "LMS180R29";
			static final String 授信簽案已核准未能簽約撥貸原因表 = "LMS180R30";
			static final String 已核准授信額度辦理狀態通報彙總表 = "LMS180R31";
			static final String 營運中心轄下分行往來客戶有全行通報異常情形彙總表 = "LMS180R32";
			static final String 中小企業創新發展專案貸款執行情形統計月報表 = "LMS180R33";
			static final String 金控總部分行逾期尚未辦理覆審名單_一次性 = "LMS180R34";
			static final String 尚未完成常董會企金戶實地覆審名單_一次性 = "LMS180R35";
			static final String 企金聯貸新作案件統計表 = "LMS180R36";
			static final String 企金授信案件敘做情形及比較表 = "LMS180R37";
			static final String 企金已核准授信額度辦理狀態通報彙總表 = "LMS180R38";
			static final String 企金共同行銷報表 = "LMS180R39"; // for投資處
			static final String 簽報階段都更危老業務統計表 = "LMS180R40";
			static final String 協助工業區及產業園區建廠優惠貸款專案執行情形統計月報表 = "LMS180R41";
			static final String 新核准往來客戶及新增放款額度統計表 = "LMS180R42";
			static final String 新核准往來客戶明細表 = "LMS180R42T";
			static final String 國內分行每季新做無擔保中小企業戶授信額度明細表 = "LMS180R43";
			static final String 國內分行每季新作副總權限以上授信額度累計金額 = "LMS180R44";
			static final String 國內分行新核准往來企金客戶數統計表 = "LMS180R45";
			static final String 共同行銷擔保品投保未結案明細表 = "LMS180R46"; // for企金處簽報作業
			static final String 企業社會責任貸放情形統計表 = "LMS180R47";
			static final String 對區域營運中心授信覆審作業之管理績效考核表 = "LMS180R48";
			static final String 國內分行新核准往來企金客戶數統計表含舊戶 = "LMS180R49";
			static final String 投資台灣三大方案專案貸款執行情形統計表 = "LMS180R50";
			static final String 愛企貸專案統計表 = "LMS180R51";
			static final String 因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表 = "LMS180R52";
			static final String 法令遵循自評授信案件明細報表 = "LMS180R53";
			static final String 兆元振興融資方案辦理情形統計表 = "LMS180R54";
			static final String 兆元振興融資方案明細表 = "LMS180R54T";
			static final String 兆元振興融資方案辦理情形總表 = "LMS180R55";
			static final String 兆元振興融資方案分行核准情形總表 = "LMS180R56";
			static final String 建案完成未出售房屋融資統計表 = "LMS180R57";
			static final String 小規模營業人授信異常通報表 = "LMS180R58";
			static final String 青年創業及啟動金貸款辦理情形總表 = "LMS180R59";
			static final String 小規模營業人兌付振興三倍券達888張名單 = "LMS180R60";
			static final String 額度專案種類明細表 = "LMS180R61";
			static final String 案件屬110年行銷名單來源客戶簽案資料 = "LMS180R62";
			static final String 境內法人於國際金融業務分行辦理外幣授信業務報表 = "LMS180R63";
			static final String 授信異常通報案件報送統計表_不含小規 = "LMS180R64";
			static final String 企金授權外案件流程進度控管表 = "LMS180R65";
			static final String 土建融案資料表 = "LMS180R66";
			static final String 國內營業單位海外信保基金基金案件表 = "LMS180R67";
			static final String 企金授信簽報案件經區域營運中心流程進度控管表 = "LMS180R68";
			static final String 企金綠色授信暨ESG簽報案件明細表 = "LMS180R69";
			static final String 免計入銀行法72_2限額控管之廠房貸款案件追蹤表 = "LMS180R70";
			static final String 待售房屋去化落後追蹤表 = "LMS180R71";
			static final String 企金授信核准案件BIS評估表 = "LMS180R72";
			static final String 不動產授信例外管理報表 = "LMS180R73";
			static final String 中小企業千億振興融資方案 = "LMS180R74";
			static final String 企金授信簽報案件明細檔 = "LMS180R75";
			static final String 授信案件曾涉及ESG風險因而有條件通過或未核准之情形表 = "LMS180R76";
			static final String 青創追蹤報表 = "LMS180R77"; //J-113-0237
		}

		/** 國內個金 */
		interface DOCTYPE2 {
			// TODO select * from COM.BCODETYPE where
			// codetype='lms9511v01_docType2' and locale='zh_TW'
			static final String 已敘作消金案件清單 = "CLS180R01";
			static final String 敘做無自用住宅購屋放款明細表 = "CLS180R02";
			static final String 授權內分行敘做房屋貸款月報 = "CLS180R03";
			static final String 授權內分行敘做房屋貸款月報_稽核處 = "CLS180R03B";
			static final String 授權內已敘做個人消費金融業務月報表 = "CLS180R04";
			static final String 授權內已敘做個人消費金融業務月報表_稽核處 = "CLS180R04B";
			static final String 報案考核表被扣分清單 = "CLS180R05";
			static final String 審件統計表 = "CLS180R06";
			static final String 分行對保費明細表 = "CLS180R07";
			static final String 已收件待辦中案件報表 = "CLS180R08";
			static final String 婉拒案件明細表 = "CLS180R09";
			static final String 消金授信已逾期控制表 = "CLS180R10";
			static final String 已核准尚未撥款案件報表 = "CLS180R11";
			static final String 價金履保覆審統計表 = "CLS180R12";
			static final String 實際覆審戶數及件數統計表 = "CLS180R13";
			static final String 未依期限覆審資料 = "CLS180R14";
			static final String 未依期限覆審資料_至上個月 = "CLS180R14A";
			static final String 未依期限覆審資料_期限自訂 = "CLS180R14B";
			static final String 未依期限覆審資料_從查詢月起 = "CLS180R14C";
			static final String 未依期限覆審資料_逾覆審期限 = "CLS180R14D";
			static final String 覆審類別8_1之進度表 = "CLS180R15";
			static final String 覆審類別_不動產十足擔保低於門檻抽樣之進度表 = "CLS180R15B";
			static final String 覆審類別_專案信貸抽樣之進度表 = "CLS180R15C";
			static final String 覆審類別_95_1抽樣之進度表 = "CLS180R15D";
			static final String 覆審類別R14抽樣之進度表 = "CLS180R15E";
			
			// J-112-0465 新增「○○區營運中心覆審類別「額度一千萬元以下十足擔保信保七成循環動用」抽樣之授信戶明細表
			static final String 覆審類別R14抽樣之明細表 = "CLS180R15F";
			
			static final String 個金授信覆審頻率3次含以上明細表 = "CLS180R16";
			static final String 消金處新做案件_當月_當年度累計_統計表 = "CLS180R17"; // J-107-0286
			static final String 消金防杜代辦專案覆審名單 = "CLS180R18"; // J-107-0354
															// 防杜覆審第1階段
			static final String 消金防杜代辦專案覆審件數控管表 = "CLS180R18B"; // J-108-0076
																// 防杜覆審第2階段
			static final String 消金整批房貸消貸敘做明細 = "CLS180R19"; // J-107-0355
			static final String 消金線上貸款餘額表 = "CLS180R20"; // J-108-0046
			static final String 消金借款人留存同一通訊處註記清單 = "CLS180R21"; // J-107-0129
			static final String 消金借款人留存同一通訊處未註記清單 = "CLS180R21A";
			static final String 消金借款人留存同一通訊處已註記清單 = "CLS180R21B";
			static final String 消金借款人留存同一通訊處全部清單 = "CLS180R21C";
			static final String 消金借款人留存同一通訊處地址比對清單 = "CLS180R221";
			static final String 消金借款人留存同一通訊處電話比對清單 = "CLS180R222";
			static final String 消金借款人留存同一通訊處EML比對清單 = "CLS180R223";
			static final String 消金自住型房貸成長方案統計報表 = "CLS180R23"; // J-108-0208
			static final String 消金手續費收入統計表 = "CLS180R24";
			static final String 消金線上信貸申貸清單 = "CLS180R25"; // J-109-0090
			static final String 消金線上房貸申貸清單 = "CLS180R26"; // J-110-0081
			static final String 歡喜信貸案件明細表 = "CLS180R27"; // J-110-0081
			static final String 歡喜信貸ESG明細表 = "CLS180R27B"; // J-111-0578
			static final String 歡喜信貸KYC分案報表 = "CLS180R27C"; // J-112-0099
			static final String 歡喜信貸婉拒案件自動發送簡訊失敗顧客清單 = "CLS180R27D"; // J-112-0171
			static final String 疑似代辦案件訊息明細表 = "CLS250R01";
			static final String 疑似代辦案件訊息未註記明細表 = "CLS250R01A";
			static final String 疑似代辦案件訊息已註記明細表 = "CLS250R01B";
			static final String 疑似代辦案件訊息全部明細表 = "CLS250R01C";
			static final String 台電員工貸款明細表 = "CLS180R30"; // J-108-0238
			static final String 勞工紓困貸款彙總表 = "CLS180R40";//
			static final String 勞工紓困貸款明細表 = "CLS180R41";//
			static final String 特定金錢信託案件量統計報表 = "CLS180R42";// J-110-0142
			static final String 六個月內到期土建融案件月報表 = "CLS180R50";
			static final String 整批房貸統計表 = "CLS180R51";
			static final String 全行地政士黑名單 = "CLS180R52";
			static final String 全行地政士黑名單CSV = "CLS180R52V";
			static final String 整批貸款明細表 = "CLS180R53";
			static final String 消金授信簽報案件經區域營運中心流程進度控管表 = "CLS180R54";

			static final String 中期循環年度檢視表 = "CLS180R57";
			static final String 客戶訪談紀錄表 = "CLS180R58"; // J-111-0178
			static final String 國內消金貸款地政士引介房貸案發生逾放比例統計表 = "CLS180R59";
			static final String 分行承作購置住宅貸款年限逾30年統計表 = "CLS180R60";
			static final String 房貸案件明細表 = "CLS180R61"; //J-113-0009
			static final String 青創追蹤報表 = "CLS180R62"; //J-113-0237
		}

		/** 海外國外企金 */
		interface DOCTYPE3 {
			static final String 董事會權限核定授信案件報表 = "LMS9515R10";
		}
	}

	/** L230M01A 簽約未動用 */
	interface NoUseCase {
		/** 狀態註記 */
		interface NuseMemo {
			static final String 已動用 = "Y";
			static final String 已簽約 = "3";
			static final String 不簽約註銷額度 = "D";
			static final String 未簽約 = "Z";
			static final String 未簽約一不需簽約 = "E";
			static final String 未簽約一聯貸案待確認 = "F";
			static final String 未簽約一辦理中 = "G";
			static final String 未簽約一暫不訂約 = "H";
			static final String 未簽約一溝通中 = "I";

		}
	}

	/** For 上傳用 */
	interface UploadType {
		/** 報案類別 */
		interface DBELF447N_CLASS {
			static final String 一般 = "1";
			static final String 應收帳款 = "2";
			static final String 衍生性金融商品 = "3";
			static final String 交換票據抵用 = "4";
			static final String 整批團貸 = "5";
			static final String 供應鏈融資 = "6";
			static final String 進口 = "7";
			static final String 出口 = "8";
			static final String 同業交易 = "9";
			static final String 保兌 = "A";
			static final String 承兌 = "B";
			static final String 應收帳款賣方_有追 = "C";
			static final String 應收帳款賣方_無追 = "D";
			static final String 供應鏈融資_賣方 = "E";
			static final String 供應鏈融資_買方 = "F";
			static final String 貼現 = "G";
			static final String 遠匯 = "X";
			static final String 其他 = "Z";

		}

		/** 授權等級 */
		interface ELLNSEEK_approLvl {
			static final String 分行授權內 = "1";
			static final String 營運中心授權內 = "2";
			static final String 分行授權外 = "3";
			static final String 營運中心授權外 = "4";
			static final String 子行授權內 = "5";
		}
	}

	/** 外部連結 **/
	interface External {
		String 系統別 = "sysId";
		String 產品別 = "prodId";
		String 查詢ID = "queryId";
		String 查詢類別 = "queryType";
		String 聯徵 = "ejcic";
		String 票信 = "etch";
	}

	interface haveNo {
		String 有 = "1";
		String 無 = "2";
		String NA = "3";
	}

	/**
	 * 擔保品種類
	 */
	interface CollTyp1 {
		String 不動產 = "01";
		String 動產 = "02";
		String 權利質權 = "03";
		String 動產質權 = "04";
		String 保證 = "05";
		String 額度本票分期償還票據 = "06";
		String 融資客票 = "07";
		String 信託占有 = "08";
		String 參貸他行 = "09";
		String 其他 = "10";
		String 反面承諾 = "11";
		String 浮動擔保抵押 = "12";
	}

	/** 權利質權 */
	interface PLEDGEOFRIGHTS {
		String 銀行定存單 = "01";
		String 國庫券 = "02";
		String 公債 = "03";
		String 金融債券 = "04";
		String 央行儲蓄券 = "05";
		String 公司債 = "06";
		String 股票 = "07";
		String 開放型基金 = "08";
		String 票券 = "09";

	}

	/** 建檔維護 **/
	interface Maintain {
		/** 建檔維護 動審表稽核項目 */
		interface L901M01ItemType {
			static final String 全行共同項目 = "1";
			static final String 當地特殊規定項目 = "2";
			static final String 國內企金 = "3";
			static final String 國內個金 = "4";

		}
	}

	/** 額度明細表團貸檔 */
	interface L140M01LItemType {
		String 母戶 = "1";
		String 子戶 = "2";
	}

	/** 額度明細表團貸檔 */
	interface L140M01LloanType {
		String 房貸案 = "1";
		String 團貸案 = "2";
	}

	/** 額度批覆表異動檔對應表 */
	interface L140M02AName {
		static final String 本額度風險歸屬國別 = "caseBox1";
		static final String 本額度有無送保 = "caseBox2";
		static final String 保證成數 = "caseBox3";
		static final String 信保首次動用有效期限 = "caseBox4";
		static final String 借款人是否為中小企業 = "caseBox5";
		static final String 本額度是否為聯貸信保 = "caseBox6";
		static final String 是否符合送中小信保 = "caseBox7";
		static final String 擔保品是否為股票_開放型基金_受益憑證 = "caseBox8";
		static final String 本案是否為同業聯貸案額度 = "caseBox9";
		static final String 性質 = "caseBox2_1";
		static final String 額度性質 = "caseBox2_2";
		static final String 授信科目 = "caseBox2_3";
		static final String 前准額度 = "caseBox2_4";
		static final String 餘額 = "caseBox2_5";
		static final String 購料放款案下已開狀未到單金額 = "caseBox2_6";
		static final String 現請額度 = "caseBox2_7";
		static final String 聯貸說明 = "caseBox2_8";
		static final String 動用期限 = "caseBox2_9";
		static final String 授信額度合計 = "caseBox2_10";
		static final String 本票 = "caseBox2_11";
		static final String 連保人 = "caseBox2_12";
		static final String 本案未送保原因 = "caseBox2_13";
		static final String 擔保授信額度調整 = "caseBox2_14";
		static final String 備註 = "caseBox2_16";
		static final String 前准批覆授信額度 = "caseBox2_17";
		static final String 前准批覆擔保授信額度 = "caseBox2_18";
		static final String 收付彙計數 = "caseBox2_19";
		static final String 清償期限 = "caseBox2_20";
		static final String 共同借款人 = "caseBox2_21";
		static final String 大陸地區授信業務控管註記 = "caseBox2_22";
		static final String 限額條件 = "tab03";
		static final String 利率條件 = "tab04";
		static final String 擔保品內容 = "tab05";
		static final String 其他序做條件 = "tab06";
		static final String 敘做條件異動情形 = "tab07";
		static final String 銀行法72_2條控管對象 = "caseBox16";
		static final String 償還方式 = "cls_01"; // 消金額度明細表
	}

	/** 額度明細表央行購置 */
	// start 2013/06/25,Rex,修改選項代碼錯誤 自然人 = "1"; 公司法人 = "5";土地抵押貸款 = "2";非央行自然人 =
	// "4";
	interface L140M01MCbcCase {
		String 自然人 = "1";
		String 公司法人 = "5";
		String 土地抵押貸款 = "2";
		String 非央行自然人 = "4";
		String 餘屋貸款 = "6";
		String 工業區閒置土地抵押貸款 = "7";
	}

	// end 2013/06/25,Rex,修改選項代碼錯誤 自然人 = "1"; 公司法人 = "5";土地抵押貸款 = "2";非央行自然人 =
	// "4";

	/** 報表圖檔Code Type */
	interface RPTPicType {
		static final String 兆豐LOGO = "rptPath_megaLogo";
	}

	interface L140S02CIntway {
		static final String 按月計息 = "1";
		static final String 期付金 = "2";
		static final String 透支end = "P";
		static final String 透支top = "Q";
	}

	interface C900S01A_CHECKCODE {
		static final String 第165項 = "165";
		static final String 第187項 = "187"; // 本筆貸款之資金用途是否用於購買本行保險理財商品?
	}

	interface C900S01B_ITEMTYPE {
		static final String 共用項目 = "1";
		static final String 依產品自訂 = "2";
		static final String 自行輸入項目 = "3";
	}

	interface C900S01B_ITEMCODE {
		static final String 第24項 = "24"; // 若為利害關係人授信案件
		static final String 第45項 = "45"; // J-108-0351 聲明書(授信融資申購保險商品專用)
		static final String 第46項 = "46"; // J-108-0351 電話查訪機制檢核表(授信融資申購保險商品專用)
		static final String 第56項 = "56"; // J-111-0287 「央行管制」購屋(或購地)案件撥款當日檢核表
	}

	interface C122_ApplyStatus {
		static final String 受理中 = "0A0";
		static final String 審核中 = "0B0";
		static final String 不承做 = "Z01";
		static final String 轉臨櫃 = "Z02";
		static final String 已核准 = "Z03";
		static final String 待補件 = "Z04";
		static final String 動審表已覆核 = "Z05";
		static final String RPA評分未達60分 = "R01";
		static final String RPA送信保中 = "R02";
		static final String RPA簽報書待主管放行 = "R03";
		static final String RPA簽報書需分行介入 = "R04";
		static final String RPA動審表待主管放行 = "R05";
		static final String RPA動審表需分行介入 = "R06";
		static final String RPA執行異常 = "R07";
		static final String RPA契約書待主管放行 = "R08";
		static final String 擔保品待主管放行 = "R09";
		static final String RPA待人工評分 = "R10";
		static final String RPA契約書需分行介入 = "R11";
		static final String RPA洗防高風險客群負面新聞需分行介入 = "R12";
		static final String 擔保品已覆核 = "R13";
	}

	interface C122_ApplyKind {
		static final String H = "H";
		static final String C = "C";
		static final String B = "B";
		static final String D = "D";
		static final String P = "P"; // 線上信貸_PLOAN系統_主借人
		static final String Q = "Q"; // 線上信貸_PLOAN系統_從債務人
		static final String E = "E"; // 線上房貸_PLOAN系統_主借人
		static final String F = "F"; // 線上房貸_PLOAN系統_從債務人
		static final String I = "I"; // 青創100萬以下
		static final String J = "J"; // 青創100萬以上
		static final String O = "O"; // 其他_主借人
		static final String R = "R"; // 其他_從債務人
	}

	interface L250S01B_TYPE2 { // 模擬動審_行家理財
		static final Integer GROUP_30 = 30;
		static final Integer GROUP_31 = 31;
	}

	/**
	 * J-105-0167-001 Web e-Loan 企金授信案件簽報書第八之3增列集團企業(應予注意集團)有關集團評等之「財務警訊項目資訊」。
	 * 集團企業財務警訊項目資訊
	 */
	interface GrpFinAlert {
		static final String 本年評等降級 = "1";
		static final String 本年評分降低 = "2";
		static final String 借款大於營收 = "3";
		static final String 借款大於淨值 = "4";
		static final String 連續２年營收衰退 = "5";
		static final String 連續２年營業利益衰退 = "6";
		static final String 連續２年利息保障倍數小於1 = "7";
		static final String 連續２年現金流量比率為負 = "8";
		static final String 連續２年負債比率高於150趴 = "9";
		static final String 連續２年負債比率在70趴以上且評等年年度比前年度增加者 = "10";

	}

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 SAS NC_RESULT
	 */
	interface SasNcResult {
		static final String 未命中疑似名單 = "000";
		static final String 命中疑似名單 = "001";
		static final String 經調查覆核後可交易 = "002";
		static final String 經調查覆核後不可交易 = "003";
		static final String 台伊清算NonBlock = "007";
		static final String 未掃描 = "008";
		static final String 經調查覆核後取消交易 = "009";
		static final String 案件調查中 = "012";
		static final String 經調查確認命中 = "101";
		static final String 經調查確認誤中 = "102";
		static final String SAS錯誤LEVEL_1 = "A81";
		static final String SAS錯誤LEVEL_2 = "A82";
		static final String 掃描中 = "E00";
	}

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 */
	interface L140m01sType {
		static final String 本案應收帳款買方額度資訊 = "1";
	}

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 */
	interface L140m02sType {
		static final String 附表B_應收帳款買方額度加計其在本行授信額度之彙總 = "B";
	}

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 */
	interface L120m01jType {
		static final String 附表A_本案借款人同時為其他授信戶應收帳款債務人之額度資料 = "A";
	}

	interface L140M01T_estatType {
		/** 另含有子類別　01 一般，02都更，03色老，04 其它都更危老 */
		static final String 都更危老 = "D01";

		static final String 公私立各級學校 = "D02";

		static final String 醫療機構 = "D03";

		static final String 政府廳舍 = "D04";

		static final String 長照服務機構 = "D05";

		static final String 社會住宅 = "D06";

		/**
		 * 「自101.1.1起承作符合資金用途為供擴增生產而購置興建廠房」，或「依工廠管理輔導法申請許可或登記工廠，*
		 * 含其工廠所使用的土地及相關建物設施」，得不計入銀行法72-2限額
		 * 
		 * 按J-111-0313程修改為
		 * 
		 * 依工廠管理輔導法申請許可或登記工廠者，得不計入銀行法72-2限額。
		 */
		static final String A0井 = "A0#";
		/** A03 房屋修繕貸款（其他） */
		static final String A03 = "A03";
		/** A0N 房屋修繕貸款 */
		static final String A0N = "A0N";
		/** 使用郵匯局行政院經建會中長期資金辦理之企業建築放款 */
		static final String A0鼠 = "A0@";

		/** 一般房貸─購屋儲蓄 */
		static final String B11 = "B11";
		/** ９００億購置新屋 */
		static final String B20 = "B20";
		/** ６００億無自用住宅購屋 */
		static final String B21 = "B21";
		/** ９２１－新購屋 */
		static final String B22 = "B22";
		/** ９２１－重建 */
		static final String B23 = "B23";
		/** ９２１－修繕 */
		static final String B24 = "B24";

		/** 科目為保證承兌 */
		static final String C01 = "C01";
		/** 資金來源大類為郵匯局資金，產品種類為輔助人民自購住宅貸款 */
		static final String C02 = "C02";
		/** 催呆戶 */
		static final String C03 = "C03";
		/** C04 購買非住宅及企業建築之土地僅建物 */
		static final String C04 = "C04";

		static final String[] isRentOrSell_CLS_CheckItems = new String[] {
				L140M01T_estatType.公私立各級學校, L140M01T_estatType.醫療機構,
				L140M01T_estatType.政府廳舍, L140M01T_estatType.長照服務機構,
				L140M01T_estatType.社會住宅 };

		static final String[] isRentOrSell_LMS_CheckItems = new String[] {
				L140M01T_estatType.A0井, L140M01T_estatType.公私立各級學校,
				L140M01T_estatType.醫療機構, L140M01T_estatType.政府廳舍,
				L140M01T_estatType.長照服務機構, L140M01T_estatType.社會住宅 };
	}

	/** 子類別　01 一般，02都更，03危老，04 其它都更危老 */
	interface L140M01T_estatSubType {

		static final String 一般 = "01";
		static final String 都更 = "02";
		static final String 危老 = "03";
		static final String 其它都更危老 = "04";

	}

	/**
	 * 一般簽報書可選的特殊註記 select * from com.bcodetype where
	 * codeType='L140M01Y_refType_docCode1' ---> CLS1151S01Page.js ::
	 * injectL140m01yColumn(...)
	 */
	interface L140M01Y_refType_docCode1 {
		static final String ELF459_SRCFLAG_1 = "ELF459_srcflag_1"; // J-109-0232
																	// 線上貸款進件
		static final String L140M01A_PROJCLASS_X1Y1 = "L140M01A_projClass_X1Y1"; // J-109-0243
																					// 菁英方案承做原因{X1:菁英人員房貸,
																					// Y1:菁英人員信貸,
																					// Y2:菁英人員理財型貸款(循環動用),
																					// Y3:次順位房貸}
		static final String L140S01A_custPos_G = "L140S01A_custPos_G"; // J-109-0374
	}

	/**
	 * 團貸戶簽報書可選的特殊註記 select * from com.bcodetype where
	 * codeType='L140M01Y_refType_docCode5' ---> CLS1151S01Page.js ::
	 * injectL140m01yColumn(...)
	 */
	interface L140M01Y_refType_docCode5 {
		// 命名以 GRP_ 開頭，和{一般簽報書}區分
		static final String GRP_L140S02F_RATEPLAN_24 = "GRP_L140S02F_ratePlan_24"; // J-109-0093
																					// 與
																					// J-109-0310
																					// 整批房貸200億元成長方案
	}

	// =====================================================================================
	// select * from com.bcodetype where codetype='cls1141_headAccountCheckItem'
	interface L140MC1A_itemCode { // J-109-0325 , (109)第 2433 號 檢核疑似人頭戶態樣
		static final String A_01_借款人所得與借款金額顯不相稱且無其他財力文件可供說明 = "A"; // othAmt ,
																	// dRate
		static final String B_07_借款人現居地或工作地與案件來源無地緣關係 = "B"; // 戶籍地址 , 通訊地址 ,
																// 服務單位地址 ,
																// l140m01o_data
		static final String C_08_保證人財力明顯高於借款人且二者無親屬或共同生活關係 = "C"; // l140s01a ,
																	// PayAmt
		static final String D_09_擔保品為法拍屋_建商餘屋_長期空屋 = "D"; // l140m01o_data
		static final String E_10_非行員之同一介紹人轉介整批之申請案件 = "E";
		static final String F_18_借款人提供之資料與聯徵中心或與本行內部留存資料不相符 = "F"; // c120m01a.isImportDataMatch
		static final String G_19_年資小於等於一年 = "G"; // Seniority
		static final String H_19_前工作屬性是否與現工作相同 = "H"; // c120s01b.isSameWorkAttributes
		static final String I_22_本案是否已確實直接照會借保人本人無誤 = "I"; // c120m01a.isNoteBorrower
		static final String J_23_已確實核對申貸文件正本資料 = "J"; // c120m01a.isCheckOriginalDocument
		static final String K_02_借款人或保證人提供申請資料及證明文件過於完整 = "K"; // c120m01a.isFullApplyDocument

		static final String L_14_檢查擔保品所有權人_其是否在別的案子也是擔保品提供人 = "L"; // l140m01o_data
		static final String M_14_不同借款人案下保證人_擔保品提供人或聯絡資料相同 = "M"; // l140m01a ,
																	// l140s01a

		// =========================
		// 由聯徵系統取得
		static final String N_04_借款人名下有多筆非自住房貸貸款 = "N"; // BAM095
		static final String O_05_借款人於聯徵中心無授信資料 = "O"; // BAM095
		static final String P_06_借款人於聯徵中心被查詢次數密集 = "P"; // STM022
		static final String Q_07_借款人於聯徵中心無信用卡記錄或持卡小於一年 = "Q"; // KRM046

		// =========================
		static final String R_A01_評等升等 = "R"; // 先用 l140s02a.modelKind
												// 判斷哪一種申評等，再去串［C120S01G 或
												// C120S01Q 或 C120S01R］裡的
												// adjustStatus
		static final String S_A02_有寬限期案件 = "S"; // l140s02e.nowExtend
		static final String T_A03_有辦房貸壽險案 = "T"; // l140s02f.rmbinsFlag
		static final String U_A04_承辦案件的地政士有受警示紀錄 = "U"; // c120s01e 去串 c900m01h

		// J-110-0190 , (110)第 1320 號 , 人頭戶態樣優化
		static final String V_買賣契約書_如有_與借款契約_借款申請書簽名不一致 = "V";
		static final String W_借款人_擔保品所有權人與房屋契約書之買方不同人 = "W";
		static final String X_由非親屬之第三人陪同申辦貸款並回答詢問案件問題 = "X";
		static final String Y_借款人指定撥款日期及時間且無法提出合理解釋 = "Y";
		static final String Z_對自己從事之行業或職業性質與內容及貸款用途不瞭解或毫無概念 = "Z";
		static final String _1_於聯徵具被查詢紀錄_卻無法說明原因 = "1";
		static final String _2_支付銀行收取開辦手續費以外之費用 = "2";
		static final String _3_借款人或保證人之扣繳憑單給付總額或收入入帳金額為整數 = "3";
		static final String _4_借戶所得來自建築業者_代銷_仲介_是否購屋價金來自第三人_且無法佐證其與第三人之關係 = "4"; // c120m01a.isCashFromOthers

	}

	interface L140MC1A {

		static final String[] STRONG_INDEX_ITEM = new String[] {
				UtilConstants.L140MC1A_itemCode.K_02_借款人或保證人提供申請資料及證明文件過於完整,
				UtilConstants.L140MC1A_itemCode.F_18_借款人提供之資料與聯徵中心或與本行內部留存資料不相符,
				UtilConstants.L140MC1A_itemCode.J_23_已確實核對申貸文件正本資料,
				UtilConstants.L140MC1A_itemCode.V_買賣契約書_如有_與借款契約_借款申請書簽名不一致,
				UtilConstants.L140MC1A_itemCode.W_借款人_擔保品所有權人與房屋契約書之買方不同人,
				UtilConstants.L140MC1A_itemCode.I_22_本案是否已確實直接照會借保人本人無誤,
				UtilConstants.L140MC1A_itemCode.X_由非親屬之第三人陪同申辦貸款並回答詢問案件問題,
				UtilConstants.L140MC1A_itemCode.Y_借款人指定撥款日期及時間且無法提出合理解釋,
				UtilConstants.L140MC1A_itemCode.Z_對自己從事之行業或職業性質與內容及貸款用途不瞭解或毫無概念,
				UtilConstants.L140MC1A_itemCode._1_於聯徵具被查詢紀錄_卻無法說明原因,
				UtilConstants.L140MC1A_itemCode._2_支付銀行收取開辦手續費以外之費用,
				UtilConstants.L140MC1A_itemCode.U_A04_承辦案件的地政士有受警示紀錄,
				UtilConstants.L140MC1A_itemCode._4_借戶所得來自建築業者_代銷_仲介_是否購屋價金來自第三人_且無法佐證其與第三人之關係 };

		static final String[] WEAK_INDEX_ITEM = new String[] {
				UtilConstants.L140MC1A_itemCode.O_05_借款人於聯徵中心無授信資料,
				UtilConstants.L140MC1A_itemCode.S_A02_有寬限期案件,
				UtilConstants.L140MC1A_itemCode.A_01_借款人所得與借款金額顯不相稱且無其他財力文件可供說明,
				UtilConstants.L140MC1A_itemCode.D_09_擔保品為法拍屋_建商餘屋_長期空屋,
				UtilConstants.L140MC1A_itemCode.H_19_前工作屬性是否與現工作相同,
				UtilConstants.L140MC1A_itemCode.Q_07_借款人於聯徵中心無信用卡記錄或持卡小於一年,
				UtilConstants.L140MC1A_itemCode.L_14_檢查擔保品所有權人_其是否在別的案子也是擔保品提供人,
				UtilConstants.L140MC1A_itemCode.M_14_不同借款人案下保證人_擔保品提供人或聯絡資料相同,
				UtilConstants.L140MC1A_itemCode.G_19_年資小於等於一年,
				UtilConstants.L140MC1A_itemCode.R_A01_評等升等,
				UtilConstants.L140MC1A_itemCode.P_06_借款人於聯徵中心被查詢次數密集,
				UtilConstants.L140MC1A_itemCode.T_A03_有辦房貸壽險案,
				UtilConstants.L140MC1A_itemCode.C_08_保證人財力明顯高於借款人且二者無親屬或共同生活關係,
				UtilConstants.L140MC1A_itemCode.B_07_借款人現居地或工作地與案件來源無地緣關係,
				UtilConstants.L140MC1A_itemCode.N_04_借款人名下有多筆非自住房貸貸款,
				UtilConstants.L140MC1A_itemCode.E_10_非行員之同一介紹人轉介整批之申請案件,
				UtilConstants.L140MC1A_itemCode._3_借款人或保證人之扣繳憑單給付總額或收入入帳金額為整數 };
	}

	/**
	 * J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。
	 */
	interface L120s08aVersion {
		static final String Ver_20170905 = "2017-09-05";
		static final String Ver_20181001 = "2018-10-01";
		// J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」
		static final String Ver_20210501 = "2021-05-01";
		// J-110-0498 優化eloan企金授信系統之額度明細表利率合理性分析編輯功能，依借戶評等自動帶入各級擬制呆帳損失差異率
		static final String Ver_20220222 = "2022-02-22";
		static final String Ver_20230301 = "2023-03-01";
	}

	/**
	 * J-112-0217 配合修訂「本行授信利率暨保證及承兌手續費計收標準實施要點」，修改e-loan簽報書_利率定價合理性及收益率分析表格頁籤。
	 */
	interface L120s08aCheckMark {
		/**
		 * 加碼合計未達0.65%(or 新戶0.45%)
		 */
		static final String 加碼合計未達 = "A";
		/**
		 * 減碼幅度未符合貢獻度標準
		 * 
		 */
		static final String 減碼幅度未符合貢獻度標準 = "B";
		/**
		 * 敘作利率低於「基礎放款利率+預期損失個案差異率+0.15%」
		 * 
		 */
		static final String 敘作利率低於_基礎放款利率_預期損失個案差異率 = "C";

		/**
		 * 敘作利率低於「基礎放款利率」
		 * 
		 */
		static final String 敘作利率低於_基礎放款利率 = "D";

	}

	/**
	 * J-111-0220 高齡客戶關懷檢核表
	 */
	interface L120s01sVersion {
		static final String Ver_20220706 = "20220706";
	}

	/**
	 * J-111-0454 風控風險權數
	 */
	interface L120s24aVersion {
		static final String Ver_20220812 = "20220812";
		static final String Ver_20250101 = "20250101";
	}

	/**
	 * J-111-0461 建議授權層級 C900M01K_LGD、C900M01P共用的DocType 企/個金案件
	 */
	interface C900m01k_lgdDocType {
		static final String 企金 = "1";
		static final String 個金 = "2";
	}

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 審核層級類別
	 */
	interface C900m01k_lgdCaseLvl {
		static final String 董事會常務董事會權限 = "1";
		static final String 總經理權限 = "6";
		static final String 副總經理權限 = "7";
		static final String 授信審查處處長_區域營運中心營運長權限 = "8B";
		static final String 授信審查處處長權限 = "8";
		static final String 區域營運中心營運長權限 = "B";
		static final String 分行權限 = "X";
		static final String 支行權限 = "X_";
		static final String 上層分行權限 = "PA";
		static final String countryHead權限 = "CH";
	}

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * C900M01P.crdType
	 */
	interface C900m01pCrdType {
		static final String 一般企業模型 = "1";
		static final String 特殊融資模型 = "2";
		static final String 消金評等 = "CS";
		static final String MOODY = "NM";
		static final String SAndP = "NS";
		static final String Fitch = "NF";
		static final String 中華信評 = "NC";
	}

	/**
	 * J-111-0536 顯示逾權計算公式
	 */
	interface L120m01kOverType {
		static final String 授信 = "1";
		static final String 出口押匯 = "2";
		static final String 單獨劃分授信 = "3";
	}

	/**
	 * J-111-0536 顯示逾權計算公式
	 */
	interface OverAuthLoan {
		static final String 總額度 = "1";
		static final String 有擔額度 = "2";
		static final String 無擔額度 = "3";
	}

	/**
	 * J-111-0536 顯示逾權計算公式
	 */
	interface OverAuthExperf {
		static final String 符合出口實績規範 = "1";
		static final String 不符出口實績規範 = "2";
		static final String 瑕疵單據押匯 = "3";
	}

	/**
	 * J-111-0536 顯示授信額度合計提示，判斷額度明細的科目類別
	 */
	interface loanTotAmtType {
		static final String 授信 = "loan";
		static final String 十成存款設質 = "standAlone";
		static final String EDI_預約付款 = "edi";
		static final String 應收帳款承購 = "receivable";
		static final String 進出口押匯 = "enterExit";
		static final String 衍生性金融商品 = "finance";
		static final String 其他 = "other";
	}

	/**
	 * J-111-0461 建議授權層級->業務別
	 */
	interface L120m01lLoanKind {
		static final String 授信 = "1";
		static final String 應收帳款 = "2";
		static final String 供應鏈融資 = "3";
		static final String 出口押匯 = "4";
		static final String 開發即期信用狀 = "5";
		static final String 買入光票 = "6";
		static final String DA_DP = "7";
		static final String 瑕疵押匯_授信 = "8";
		static final String 單獨劃分授權 = "9";
	}

	/**
	 * J-107-0395_05097_B1001 Web e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍
	 */
	interface L120s05a_bondFlag {
		static final String J1050017 = "1";
		static final String J1070395 = "2";
	}

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 */
	interface L120s11aRelType {
		static final int 借款人本人 = 1;
		static final int 關係企業 = 2;
		static final int 負責人 = 11;
		static final int 配偶0024 = 12;
		static final int 配偶lnfe085 = 13;
		static final int 一親等血親 = 14;
		static final int 同地址電話 = 15;
	}

	/**
	 * J-108-0303 連鎖店Chain store J-108-0304 投資台灣三大方案 J-109-0025 愛企貸專案
	 */
	interface projClass {
		static final String 連鎖加盟貸款專案 = "09";
		static final String 歡迎台商回台投資專案貸款 = "10";
		static final String 中小企業加速投資貸款 = "11";
		static final String 根留台灣企業加速投資專案貸款 = "12";
		static final String 愛企貸專案 = "13";
	}

	/**
	 * J-109-0208 LMS企金動審表一鍵查詢and列印 RPA
	 * */
	interface RPA {

		static final String DocFile_FieldId = "rpa_lms";

		/** 不動產 */
		interface TYPE {
			String 公司登記事項卡 = "01";
			String 稅籍登記資料公示查詢 = "02";
			String 身份證換補查詢 = "03";
			String 受監護輔助宣告查詢 = "04";
			String 銀行法及金控法利害關係人查詢 = "05";
			String 地政士 = "06";
			String 信保保證書 = "07";
		}

		/*
		 * A01:查詢中 A02:查詢完成 A03:查詢失敗
		 */
		interface STATUS {
			String 查詢中 = "A01";
			String 查詢完成 = "A02";
			String 查詢失敗 = "A03";
		}

		interface SEARCH_RESULT {
			String 有案件 = "0";
			String 查無資料 = "1";
		}

		interface RESULT {
			String 啟動執行中 = "01";
			String 接收結果中 = "02";
			String 查詢完成 = "03";
			String 引入完成 = "04";
		}

		/** 洗錢防制控管對象 J-106-0029-002 洗錢防制-新增洗錢防制頁籤 */
		// 高階管理人員 = "A":J-107-0070-001 將「高階管理人員」納入應查詢比對黑名單之對象。
		interface custRelationListCtlTarget {
			static final String 借戶 = "1";
			static final String 共同借款人 = "2";
			static final String 負責人 = "3";
			static final String 連保人 = "4";
			static final String 擔保品提供人 = "5";
			static final String 關係企業 = "6";
			static final String 實質受益人 = "7";
			static final String 一般保證人 = "8";
			static final String 應收帳款買方無追索 = "9";
			static final String 高階管理人員 = "10";
			static final String 具控制權人 = "11";
			static final String 董監事 = "99";
		}

		interface functionCode {
			static final String 個金徵信作業查詢Z13 = "CLS1131_00001";
			static final String 企金動用審核表小規模營業人查詢 = "LMS1601_00001";
		}
	}

	/**
	 * 
	 */
	interface L140m01mVersion {
		static final String VERSION_20201208 = "20201208_ver1";
		static final String VERSION_20210319 = "20210319_ver2";
		static final String VERSION_20210924 = "20210924_ver3";
		static final String VERSION_20211217 = "20211217_ver4";
		static final String VERSION_20230616 = "20230616_ver5";
		static final String VERSION_20240614 = "20240614_ver6";
		static final String VERSION_20240919 = "20240919_ver7";
		static final String VERSION_OLD = "oldVersion";
		static final String VERSION_LASTEST = UtilConstants.L140m01mVersion.VERSION_20240919;

	}

	interface City {
		static final String 台北市 = "A";
		static final String 新北市 = "F";
	}

	interface ApplyStatusC {
		static final String RPA處理中 = "R01";
		static final String RPA完成 = "R02";
		static final String RPA一鍵查詢結果有誤 = "R03";
		static final String RPA執行異常 = "R04";
	}

	interface l240m01aApplyStatus {
		static final String 不承做_線上貸款 = "Z01";
		static final String 轉臨櫃_線上貸款 = "Z02";
		static final String 已核貸_線上貸款 = "Z03";
	}

	interface c240m01aStatus4Br {
		static final String RPA簽報書處理中 = "B01";
		static final String RPA簽報書完成 = "B02";
		static final String RPA簽報書執行異常 = "B03";
		static final String RPA動審表處理中 = "B04";
		static final String RPA動審表完成 = "B05";
		static final String RPA動審表執行異常 = "B06";
		static final String RPA信保查詢失敗 = "B07";
		static final String RPA信保查詢完成 = "B08";
		static final String 簽報書已核准 = "B20";
		static final String 簽報書已婉卻 = "B21";
		static final String 動審表已核准 = "B22";
	}

	interface SYS_SHORT_CODE {
		static final String CES = "A";
		static final String LMS = "B";
		static final String CMS = "C";
		static final String COL = "D";
		static final String DEB = "E";
		static final String RPS = "F";
	}

	interface PaperlessActingType {
		static final String QUOTA = "quota";
		static final String TERM = "term";
		static final String RATE = "rate";
		static final String FEE = "fee";

	}

	interface PaperlessActingRole {
		static final String AO = "AO";
		static final String RV = "RV";
	}

	interface C122_IncomType {
		static final String 線下 = "1";
		static final String 線上 = "2";
	}

	interface C122_DocStatus {
		static final String 待派案 = "A00";
		static final String 已派案 = "A01";
		static final String 補件通知 = "A02";
		static final String 待初審 = "A99";
		static final String 系統徵信 = "B00";
		static final String 徵信照會 = "B01";
		static final String 報價中 = "B02";
		static final String 系統建議核准 = "B03";
		static final String 人工審核 = "B04";
		static final String 系統建議婉拒 = "B05";
		static final String 簽案作業 = "C00";
		static final String 簽案待覆核 = "C01";
		static final String 簽案已覆核 = "C02";
		static final String 契約作業 = "D00";
		static final String 契約待覆核 = "D01";
		static final String 契約已覆核 = "D02";
		static final String 契約已簽定 = "D03";
		static final String 定約通知 = "D04";
		static final String 動用作業 = "E00";
		static final String 動用待覆核 = "E01";
		static final String 動用已覆核 = "E02";
		static final String 已作廢 = "F00";
		static final String 不承作 = "G00";
		static final String 估價作業 = "H00";
		static final String 估價待覆核 = "H01";
		static final String 估價已覆核 = "H02";
		static final String 取消 = "I00";
		static final String 系統結案 = "Z99";
	}

	interface C122s01f_otherFlag {
		static final String 變更原始分行 = "Z01";
		static final String 改派簽案行員 = "Z02";
		static final String 轉移分行 = "Z03";
		static final String 狀態回復 = "Z04";

	}

	interface C122s01h_flowId {
		static final String 借保人資料 = "1";
		static final String 案件簽報書 = "2";
		static final String 消金契約書 = "3";
		static final String 動用審核表 = "4";
	}

	interface IsHeadAccountVersion {
		static final String VERSION_2_0 = "2.0";
		static final String VERSION_2_1 = "2.1";
	}

	interface CheckItemRange {
		static final String defult = "1to5";
	}

	interface DATA_TYPE_OF_DW_OTSCUSTINFO {
		static final String ADDRESS = "1";
		static final String CELLPHONE = "2";
		static final String TELPHONE = "3";
		static final String EMAIL = "4";
	}

	interface IsHittingSameInfoVersion {
		static final String VERSION_1_0 = "1.0";
	}

	interface Lms8000m01_esg {
		// J-113-0035 為利ESG案件之貸後管控,
		// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
		static final String ESG = "lms140_esgSustainLoanType";
	}
	
	interface Lms8000m01_fllowKind {
		// J-112-0307
		// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
		static final String 公司訪問紀錄表限ID階層 = "10";

		// J-112-0418 配合企金處依據企金處112年一般業務查核意見，E-LOAN企金授信管理系統修改增加「綠色授信」註記之管控機制等。
		static final String 綠色授信 = "11";

		// J-113-0035 為利ESG案件之貸後管控,
		// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
		static final String 永續績效目標檢視 = "12";
		static final String 其他ESG條件 = "13";
	}

	interface Lms8000m01_visitComVer {
		static final String V_O_202308 = "Ver202308";
	}

	// 公司訪問紀錄表最新版本
	interface Lms8000m01_visitComLastVer {
		static final String V_O_202308 = "Ver202308";
	}

    /*
    *
    * */
    interface usePlan {
        static final String 公教好享貸 = "E147";
    }
	
	interface MortgageRatioCheckVersion {
		static final String VERSION_1_0 = "1.0";
		static final String VERSION_2_0 = "2.0";
		static final String VERSION_2_1 = "2.1";
		static final String VERSION_2_2 = "2.2";
		static final String VERSION_2_3 = "2.3";
	}
}
