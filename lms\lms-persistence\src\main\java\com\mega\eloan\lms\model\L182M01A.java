/* 
 * L182M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.Meta;

import tw.com.iisi.cap.model.IDataObject;

/** 覆審預約單檔 **/
@Entity
@Table(name = "L182M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L182M01A extends Meta implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l182m01a", fetch = FetchType.LAZY)
	private Set<L182A01A> l182a01a;

	public Set<L182A01A> getL182a01a() {
		return l182a01a;
	}

	public void setL182a01a(Set<L182A01A> l182m01a) {
		this.l182a01a = l182m01a;
	}

	/** 指定執行整批產生名單時間 **/
	@Column(name = "GENDATE", columnDefinition = "TIMESTAMP")
	private Date genDate;

	/**
	 * 下次覆審日期年月
	 * <p/>
	 * YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "BASEDATE", columnDefinition = "DATE")
	private Date baseDate;

	/**
	 * 分行名單
	 * <p/>
	 * xxx|xxx|xxx…
	 */
	@Column(name = "BRANCHLIST", length = 768, columnDefinition = "VARCHAR(768)")
	private String branchList;

	/** 實際執行時間 **/
	@Column(name="EXETIME", columnDefinition="TIMESTAMP")
	private Timestamp exeTime;
	
	/** 取得指定執行整批產生名單時間 **/
	public Date getGenDate() {
		return this.genDate;
	}

	/** 設定指定執行整批產生名單時間 **/
	public void setGenDate(Date value) {
		this.genDate = value;
	}

	/**
	 * 取得下次覆審日期年月
	 * <p/>
	 * YYYY-MM-01
	 */
	public Date getBaseDate() {
		return this.baseDate;
	}

	/**
	 * 設定下次覆審日期年月
	 * <p/>
	 * YYYY-MM-01
	 **/
	public void setBaseDate(Date value) {
		this.baseDate = value;
	}

	/**
	 * 取得分行名單
	 * <p/>
	 * xxx|xxx|xxx…
	 */
	public String getBranchList() {
		return this.branchList;
	}

	/**
	 * 設定分行名單
	 * <p/>
	 * xxx|xxx|xxx…
	 **/
	public void setBranchList(String value) {
		this.branchList = value;
	}

	/** 取得實際執行時間 **/
	public Timestamp getExeTime() {
		return this.exeTime;
	}
	/** 設定實際執行時間 **/
	public void setExeTime(Timestamp value) {
		this.exeTime = value;
	}
}
