
var lms140_loanTarget = null;
if (!window.LMS140M01MAction) {
    window.LMS140M01MAction = {
        fhandle: "lmscommonformhandler",
        gridhandler: 'lmscommongridhandler',
        mainId: "",
        formId: "LMS140M01MForm",
        isInit: false,
        isChinaLoanInit: false,
        isCondChgInit: false,       // J-108-0283 變更條件Condition Change
        formData: {},
        // 是否為塞值
        isInject: false,
        ELF442Grid: null, // 帳號資料表
        /**
         * 清空欄位值並隱藏
         *
         * @param {Object}
         *            $obj jquery 物件
         */
        cleanTrHideInput: function($obj){
            $obj.hide();
            $obj.find(["input", "select", "span.field"]).each(function(){
                var $this = $(this);
                $this.each(function(){
                    switch (this.nodeName.toLowerCase()) {
                        case 'input':
                            var item = $(this).attr("type");
                            switch (item.toLowerCase()) {
                                case "text":
                                case "hidden":
                                case "password":
                                    $(this).val("");
                                    break;
                                case "radio":
                                case "checkbox":
                                    $(this).removeAttr("checked");
                                    break;
                                default:
                                    $(this).val("");
                                    break;
                            }
                            break
                        case 'select':
                            $this.val("").trigger("change");
                            break;
                        case "span":
                            $this.html("");
                            break;
                    }
                });
            });
        },
        /**
         *
         * @param {Object}
         *            $formObject 表單物件
         */
        init: function($formObject, l140m01mVersion){
			
            if (!this.isInit) {
            
                // 產生下拉選單
                var $div = $formObject.find("[itemType]");
                var allKey = [];
                $div.each(function(){
                    allKey.push($(this).attr("itemType"));
                });
                var item = API.loadCombos(allKey);
                $div.each(function(){
                    var $obj = $(this);
                    var itemType = $obj.attr("itemType");
                    if (itemType) {
                        var format = $obj.attr("itemFormat") || "{key}";
                        $obj.setItems({
                            space: $obj.attr("space") || true,
                            item: item[itemType],
                            format: format,
                            size: $obj.attr("itemSize")
                        });
                    }
                });
				
				CentralBankMortgageLoanManagement.initInfo(l140m01mVersion);
				
				if (!LMS140M01MAction.ELF442Grid){
	                LMS140M01MAction.ELF442Grid = $("#ELF442Grid").iGrid({
						localFirst : true,
	                    height: 100,
	                    shrinkToFit: false,
	                    handler: "lmscommongridhandler",
	                    action: "ELF442Query",
	                    rownumbers: true,
	                    colModel: [{
	                        colHeader: i18n.lmsl140m01m["ELF442.CNTRNO"], //額度序號
	                        align: "left",
	                        width: 150, //設定寬度
	                        sortable: true, //是否允許排序
	                        name: 'elf442_cntrno'
	                    }, {
	                        colHeader: i18n.lmsl140m01m["ELF442.CURR"], //幣別
	                        align: "left",
	                        width: 40, //設定寬度
	                        sortable: true, //是否允許排序
	                        name: 'elf442_curr'
	                    }, {
	                        colHeader: i18n.lmsl140m01m["ELF442.QUOTA"], //預約額度金額
	                        align: "left",
	                        width: 150, //設定寬度
	                        sortable: true, //是否允許排序
	                        name: 'elf442_quota'
	                    }, {
	                        colHeader: i18n.lmsl140m01m["ELF442.ENDDT"], //預約額度申請到期日
	                        align: "left",
	                        width: 150, //設定寬度
	                        sortable: true, //是否允許排序
	                        name: 'elf442_enddt'
	                    }, {
	                        colHeader: i18n.lmsl140m01m["ELF442.PRODCLASS"], //產品種類
	                        align: "left",
	                        width: 100, //設定寬度
	                        sortable: true, //是否允許排序
	                        name: 'elf442_prod_class'
	                    }, {
	                        name: 'elf442_land_area', //土地面積
	                        hidden: true //是否隱藏
	                    }, {
	                        name: 'elf442_wait_month', //預計撥款至動工期間月數
	                        hidden: true //是否隱藏
	                    }, {
	                        name: 'elf442_build_date', //預計取得建照日期
	                        hidden: true //是否隱藏
	                    }, {
	                        name: 'elf442_location_cd', //擔保品座落
	                        hidden: true //是否隱藏
	                    }, {
	                        name: 'elf442_site3no', //座落區段
	                        hidden: true //是否隱藏
	                    }, {
	                        name: 'elf442_site4no', //座落區村里
	                        hidden: true //是否隱藏
	                    }, {
	                        name: 'elf442_land_type', //土地使用分區
	                        hidden: true //是否隱藏
	                    }]
	                });
				}
                this.initEvent($formObject, l140m01mVersion);
                //this.isInit = true;
            }
            
            $formObject.reset();
            $formObject.find(".L140M01M_cbcCase").hide();
        },
        initChinaLoan: function($formObject){
            if (!this.isChinaLoanInit) {
                this.initChinaLoanEvent($formObject);
				this.isChinaLoanInit=true;
            }
            $formObject.reset();
        },
        // J-108-0283 變更條件Condition Change
        initCondChg: function($formObject, obj, readonly){
            if (!this.isCondChgInit) {
                this.initCondChgEvent($formObject, obj, readonly);
                this.isCondChgInit=true;
            }
//            $formObject.reset();
        },
		initLoanTarget: function(){
			//J-104-0XXX-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			var nitem = API.loadCombos('lms140_loanTarget');
			lms140_loanTarget = nitem.lms140_loanTarget;
			var code = [];
			var codeDesc = []; 
			for(var prop in lms140_loanTarget){			     
		        if(lms140_loanTarget.hasOwnProperty(prop)){
					code.push(prop);
					codeDesc.push(lms140_loanTarget[prop]);
		        }			     
			}

			//var code = ['10000','11000','11100','11200','11210','11220','11230','11240','11241','11242','11243','11250','11300','11310'];	
			//var codeDesc = ['直接往來授信','債務人','大陸地區人民','大陸地區法人','大陸地區金融機構','大陸地區外資企業','大陸地區臺資企業','大陸地區陸資企業','中央企業','國營企業','民營企業','其他','大陸地區人民，法人在第三地區','人民在第三地區設立之企業'];			
			$("#_loanTarget").empty();
			$("#_loanTarget").append("<option value=''>請選擇</option>");
			
		    for (i = 0; i < code.length; i++) {
		      var haveChildren = haveChild(code[i], code[i+1]);
		      var zeroCount = countTailZero(code[i]);
		      var spaceCount = 4 - zeroCount;
		
		      var nbsp = "&nbsp;&nbsp;";
		      for(var tmp = 0 ; tmp<spaceCount;tmp++){
		        nbsp = nbsp + "&nbsp;";
		        nbsp = nbsp + "&nbsp;";
		        nbsp = nbsp + "&nbsp;";
		        nbsp = nbsp + "&nbsp;";
		      }
		      var option = $("<option>"+code[i] + nbsp + codeDesc[i]+ "</option>");
		      if(haveChildren){
		        option.attr("disabled", true);
		      }
		      option.attr("value", code[i]);
		      $("#_loanTarget").append(option);
		
		    }
        },
        /**
         * 傳入之縣市名稱(fCity)AND鄉鎮市區名稱(fZip) 傳回段名稱(SITE3)
         */
        getSITE3: function(fCity, fZip, loanBuild){
            if (!fCity || !fZip) 
                return {};
            var SITE3 = null;
            $.ajax({
                handler: "lmscommonformhandler",
                action: "querySIET3",
                type: 'post',
                async: false,
                data: {
                    fCity: fCity,
                    fZip: fZip
                },
                success: function(obj){
                    if (loanBuild == "Y") {
                        SITE3NO = obj['SITE3NO'];
                    }
                    else {
                        SITE3 = obj['SITE3'];
                    }
                }
            });
            return SITE3;
        },
        /**
         * 傳入之縣市名稱(fCity)AND鄉鎮市區名稱(fZip) 傳回村名稱(SITE4)
         */
        getSITE4: function(fCity, fZip, loanBuild){
            if (!fCity || !fZip) 
                return {};
            var SITE4 = null;
            $.ajax({
                handler: "lmscommonformhandler",
                action: "querySIT4NO",
                type: 'post',
                async: false,
                data: {
                    LOCATE1DESC: fCity,
                    LOCATE2DESC: fZip
                },
                success: function(obj){
                    if (loanBuild == "Y") {
                        SITE4NO = obj['SITE4NO'];
                    }
                    else {
                        SITE4 = obj['SITE4'];
                    }
                }
            });
            return SITE4;
        },
        /**
         *
         */
        getCMSData: function(){
            $.ajax({
                handler: "lmscommonformhandler",
                action: "querySuminAmt",
                data: {},
                success: function(obj){
                    var tappAmt = $("#appAmt").val();
                    if (tappAmt == '' || tappAmt == undefined || tappAmt == null) {
                        $("#appAmt").val(obj.SINAMT);
                        $("#nowAMT").val(obj.nowAMT);
                        $("#valueAMT").val(obj.valueAMT);
                        $("#sLndTax").val(obj.SLNDTAX);
                    }
                }
            });
        },
        initClearLand: function($formObject){
   		    //J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
        	$formObject.find("#showClearLand").find("input:radio:checked" ).attr( "checked" ,false);
        	$formObject.find("#showClearLand").find("input:checkbox").removeAttr("checked" );
        	$formObject.find("#showClearLand").find("input:text").val('');
        	$formObject.find("#showClearLand").find(".field").val('');
        	$formObject.find("#showClearLand").find("select").val('');

        	$formObject.find("#showClearLand").find("input:radio" ).trigger("change");
        	$formObject.find("#showClearLand").find("input:checkbox").trigger("change");
        	
       
        },
        /**
         * 初始化事件
         *
         * @param {Object}
         *            $formObject 表單物件
         */
        initEvent: function($formObject, l140m01mVersion){

        	
        	var $locationDomestic = $formObject.find("#locationDomestic");
        	$locationDomestic.change(function(){
                if ($(this).val() == '1'){
                	$("#spanDomestic").show();
                	$("#spanForiegn").hide();              	
                	$(".domesticTarget").addClass("required");
                	$(".foreignTarget").removeClass("required");
                	
                	$("#locationTarget").val("");
                } else {
                	$("#spanDomestic").hide();
                	$("#spanForiegn").show();
                	$(".domesticTarget").removeClass("required");
                	$(".foreignTarget").addClass("required");
                	
                	$("#locationCity").val("");
                	$("#locationCd").val("");
                	$("#site3No").val("");
                	$("#site4No").val("");
                }
            });
        	
            var $LANDBUILD = $formObject.find("#LANDBUILD");
			$formObject.find("[name=landBuildYN]").click(function(){
            
                $LANDBUILD.hide();
                if ($(this).val() == "Y") {
                    $LANDBUILD.show();
                }

				 CentralBankMortgageLoanManagement.switchLatestStartDate($formObject, l140m01mVersion);
            });
			
			var $remainLoan = $formObject.find("#remainLoan");
			$formObject.find("[name=remainLoanYN]").click(function(){
                $remainLoan.hide();
                if ($(this).val() == "Y") {
                    $remainLoan.show();

                	$locationDomestic.triggerHandler("change");
                }else{
					$remainLoan.hide();
				}
            });
			
			//BGN J-104-0276-001 額度明細檢核附表「建案完工未出售房屋融資註記」欄位，增加引進最近一次「建案完工未出售房屋融資註記」欄位。
			var $bremainLoan = $formObject.find("#bremainLoan");
			$formObject.find("[name=bremainLoanYN]").click(function(){
                $bremainLoan.hide();
                if ($(this).val() == "Y") {
                    $bremainLoan.show();
                }else{
					$bremainLoan.hide();
				}
            });
			
			$formObject.find("#btnApplyELF517").click(function(){
                 applyELF517();
            });
			
			//END J-104-0276-001 額度明細檢核附表「建案完工未出售房屋融資註記」欄位，增加引進最近一次「建案完工未出售房屋融資註記」欄位。
            
            // 建物謄本登記用途是否屬「住」類，或「住商」類等無營業登記
            var $buildYN = $formObject.find("[name=buildYN]");
            /**
             * 非屬央行控管對象者需要選擇一個理由
             */
            var $plusReasonMeMo = $formObject.find("#plusReasonMeMo")
            $formObject.find("[name=plusReason]").click(function(){
                $plusReasonMeMo.hide();
                if ($(this).val() == "5") {
                    $plusReasonMeMo.show();
                }

                if ($(this).val() == "8") {
                	$formObject.find(".L140M01M_cbcCase4_PlusReason8").show();                	
                }else{
                	$formObject.find(".L140M01M_cbcCase4_PlusReason8").hide();
                }
            });
            /**
             * 央行購住/空地/興建房屋統計報表用相關資訊
             */
            var $about_CbcCase = $formObject.find(".L140M01M_cbcCase");
            // 非央行原因
            var $L140M01M_Reason = $formObject.find("#L140M01M_Reason");
            
            // 皆會出現項目
            var $cbcCaseBase = $formObject.find(".L140M01M_cbcCaseBase");
            var $L140M01M_cbcCase1 = $formObject.find(".L140M01M_cbcCase1");
            var $L140M01M_cbcCase2 = $formObject.find(".L140M01M_cbcCase2");
            var $L140M01M_cbcCase3 = $formObject.find(".L140M01M_cbcCase3");
			var $notLatestVerDescTr = $formObject.find(".notLatestVerDescTr");
			var $timeValTr = $formObject.find("#timeValTr");
			
            // 借款人聯徵名下有無 - 用途別為[1.購置不動產]之其他房貸資料
            var $custYNTr = $formObject.find("#custYNTr");
            
            var $RMKClass = $formObject.find(".L140M01MRMK");
            // 非央行原因選項
            var $plusReason = $formObject.find("[name=plusReason]");
			
			$plusReason.click(function(){
				CentralBankMortgageLoanManagement.switchPurchaseAndCurrentAmount($(this).val());
			});
			
			var cbcCase;
            $formObject.find("[name=cbcCase]").click(function(){
				cbcCase = $(this).val();
                $RMKClass.hide();
                if (!LMS140M01MAction.isInject) {
                    // 把form reset
                    //                    $formObject.reset();
                    
                    // 將form原本的值先存入backupObj中
                    var backupObj = $formObject.serializeData();
                    // 再new一個新的obj存放保留的欄位
                    var memoObj = {};
                    $.each(["landBuildYN", "prodClass", "landBuildCntrno", "areaLand", "waitMonth", "buildDate", "landType", "locationCity", "locationCd", "site3No", "site4No","remainLoanYN","remainLoanClass", "remainLoanLocationCity", "remainLoanLocationCd", "remainLoanSite3No", "remainLoanSite4No","bremainLoanYN","bremainLoanClass", "bremainLoanLocationCity", "bremainLoanLocationCd", "bremainLoanSite3No", "bremainLoanSite4No","isClearLand","ctlType","fstDate","lstDate","isChgStDate","cstDate","cstReason","isChgRate","adoptFg","rateAdd","custRoa","relRoa","roaBgnDate","roaEndDate","isLegal"], function(idx, colName){
                        memoObj[colName] = backupObj[colName];
                    });
                    
                    $formObject.reset();
                    LMS140M01MAction.isInject = true;
                    // 再將保留的欄位回存
                    $formObject.injectData(memoObj);
                    LMS140M01MAction.isInject = false;
                    
                    
                    // 因為選項值包含在form內
                    $(this).attr("checked", "checked");
					
					if (CentralBankMortgageLoanManagement.isContainSpecificVersion(l140m01mVersion) || $(this).val() != '1') {
						CentralBankMortgageLoanManagement.hideIs3rdHighHouseOption($formObject);
					}
					
					CentralBankMortgageLoanManagement.switchDuringCbcCaseChange($formObject, value);
                }
                $about_CbcCase.hide();
                var value = $(this).val() + "";
                if (value) {
                    if (value != "4") {
                        // 當不為非央行原因要清除選項值
                        $plusReason.removeAttr("checked");
                        $plusReasonMeMo.val("").hide();
                        $cbcCaseBase.show();
                    }
                    $formObject.find("#rmk" + value).show();
                }
				
				$timeValTr.hide();

                switch (value) {
                    case "1":
                        $L140M01M_cbcCase1.show();
						$timeValTr.show();
                        break;
                    case "5":
                        $L140M01M_cbcCase2.show();
						$timeValTr.show();
                        break;
                    case "2":
                        $L140M01M_cbcCase3.show();
                        break;
                    case "4":
                        // 清除其他欄位值
                        $L140M01M_Reason.show();
						$notLatestVerDescTr.hide();
						$formObject.find("#notLatestVerDesc").val("");
                        break;
					case "7":
                    // 清除其他欄位值
                    	$L140M01M_cbcCase3.show();
                    break;
                }

				CentralBankMortgageLoanManagement.switchFillingItem($formObject, l140m01mVersion, value);
				CentralBankMortgageLoanManagement.switchPurchaseAndCurrentAmount($formObject.find("[name=plusReason]:radio:checked").val());
            });
			
			CentralBankMortgageLoanManagement.initEvent($formObject, l140m01mVersion);
			
            // 建物謄本登記用途是否屬「住」類，或「住商」類等無營業登記
            $buildYN.click(function(){
                if ($(this).val() == "N") {
                    $formObject.find("[name=cbcCase][value=4]").click();
                    $formObject.find("[name=plusReason][value=2]").click();
					CentralBankMortgageLoanManagement.hideIs3rdHighHouseOption($formObject);
                }
            });
            
            // 是否屬都市計畫劃定之區域
            var $houseYN = $formObject.find("[name=houseYN]");
            $houseYN.click(function(){
                if ($(this).val() == "N") {
                    $formObject.find("[name=houseYN]:checked").removeAttr("checked");
                    // L140M01M.MSG001=土地抵押貸款非屬「都市計畫劃定之區域」者，不屬於央行報表填報對象，請改選「註記資訊」為「非屬央行自然人購屋
                    // / 公司法人購屋 / 土地抵押貸款報表填報對象」，理由「其他，並加註非都市計畫劃定之區域」。
                    API.showMessage(i18n.lmsl140m01m['L140M01M.MSG001']);
                }
            });
			
			$formObject.find("[name=peopleMortgageDetail]").change(function(){
				CentralBankMortgageLoanManagement.switchIs3rdHighHouseOption(l140m01mVersion, $(this).val(), $formObject);
			});

            // 借款用途
            var $purposeType = $formObject.find("[name=purposeType]");
            $purposeType.click(function(){
                // 營運週轉 -3
                if ($(this).val() == "3" && cbcCase!= '7') {
                    $formObject.find("[name=purposeType]:checked").removeAttr("checked");
                    // L140M01M.MSG003=土地抵押貸款非屬「都市計畫劃定之區域」者，不屬於央行報表填報對象，請改選「註記資訊」為「非屬央行自然人購屋
                    // / 公司法人購屋 / 土地抵押貸款報表填報對象」，理由「其他，並加註非都市計畫劃定之區域」。
                    API.showMessage(i18n.lmsl140m01m['L140M01M.MSG003']);
                }

				CentralBankMortgageLoanManagement.switchLatestStartDate($formObject, l140m01mVersion);
				
            });
            
            // 有無授權外核准得分批撥款
            $formObject.find("[name=shareCollYN]").click(function(){
                LMS140M01MAction.setShareCollAmt($formObject);
            });
            // 有無與其他帳號共用同一擔保品
            $formObject.find("[name=commonYN]").click(function(){
                LMS140M01MAction.setShareCollAmt($formObject);
            });
            
            var $houseTypeDiv = $formObject.find("#houseTypeDiv");
            // 都市計畫劃定之使用分區
            $formObject.find("[name=houseType]").click(function(){
                var houseType = $(this).val();
                if (houseType == "0") {
                    // $houseTypeDiv.show();
                    $formObject.find("[name=houseType]:checked").removeAttr("checked");
                    // L140M01M.MSG002=土地抵押貸款<span
                    // class="text-red">屬</span>「都市計畫劃定之區域」，但<span
                    // class="text-red">使用分區</span>非屬住宅區或商業區者，<br/>不屬於央行報表填報對象，請改選「註記資訊」為「非屬央行自然人購屋
                    // / 公司法人購屋 / 土地抵押貸款報表填報對象」，理由「其他，並加註使用分區」。
                    API.showMessage(i18n.lmsl140m01m['L140M01M.MSG002']);
                }
                else {
                    $houseTypeDiv.hide();
                    $houseTypeDiv.find("input").val("");
                }
            });
            
            var $cmsOtherDiv = $formObject.find("#cmsOtherDiv");
            // 擔保品性質別
            $formObject.find("[name=cmsType]").click(function(){
                var cmsType = $(this).val();
                if (cmsType == "3") {
                    $cmsOtherDiv.show();
                }
                else {
                    $cmsOtherDiv.hide();
                    $cmsOtherDiv.find("input").val("");
                }
            });
            
            var defaultSelect = "<option value=''>" + i18n.def.comboSpace +
            "</option>";
            var $sit3No = $formObject.find("#sit3No");
            var $sit4No = $formObject.find("#sit4No");
            /**
             * 縣市變更
             */
            var $companyArea = $formObject.find("#areaId");
            $formObject.find("#cityId").setItems({
                item: QueryCityCode.getCode("1", ""),
                sort: "asc",
                value: LMS140M01MAction.formData['cityId'] || "",
                fn: function(k, v){
                
                    changCityValue($formObject.find("#cityId"), $formObject.find("#areaId"), $formObject.find("#sit3No"), $formObject.find("#sit4No"), defaultSelect, ['areaId', 'sit3No', 'sit4No'], (!v));
                }
            });
            
            /**
             * 鄉鎮切換
             */
            $companyArea.change(function(k, v){
            
                changAreaValue($formObject.find("#cityId"), $formObject.find("#areaId"), $formObject.find("#sit3No"), $formObject.find("#sit4No"), defaultSelect, ['areaId', 'sit3No', 'sit4No'], (!v));
                
            });
            
            
            /**
             * 縣市變更
             */
            var $locationCd = $formObject.find("#locationCd");
            $formObject.find("#locationCity").setItems({
                item: QueryCityCode.getCode("1", ""),
                sort: "asc",
                value: LMS140M01MAction.formData['locationCity'] || "",
                fn: function(k, v){
                
                    changCityValue($formObject.find("#locationCity"), $formObject.find("#locationCd"), $formObject.find("#site3No"), $formObject.find("#site4No"), defaultSelect, ['locationCd', 'site3No', 'site4No'], (!v));
                }
            });
            
            /**
             * 鄉鎮切換
             */
            $locationCd.change(function(k, v){
            
                changAreaValue($formObject.find("#locationCity"), $formObject.find("#locationCd"), $formObject.find("#site3No"), $formObject.find("#site4No"), defaultSelect, ['locationCd', 'site3No', 'site4No'], (!v));
                
            });
			
			
			/**
             * 縣市變更
             */
            var remainLoanLocationCd = $formObject.find("#remainLoanLocationCd");
            $formObject.find("#remainLoanLocationCity").setItems({
                item: QueryCityCode.getCode("1", ""),
                sort: "asc",
                value: LMS140M01MAction.formData['remainLoanLocationCity'] || "",
                fn: function(k, v){
                
                    changCityValue($formObject.find("#remainLoanLocationCity"), $formObject.find("#remainLoanLocationCd"), $formObject.find("#remainLoanSite3No"), $formObject.find("#remainLoanSite4No"), defaultSelect, ['remainLoanLocationCd', 'remainLoanSite3No', 'remainLoanSite4No'], (!v));
                }
            });
            
            /**
             * 鄉鎮切換
             */
            remainLoanLocationCd.change(function(k, v){
            
                changAreaValue($formObject.find("#remainLoanLocationCity"), $formObject.find("#remainLoanLocationCd"), $formObject.find("#remainLoanSite3No"), $formObject.find("#remainLoanSite4No"), defaultSelect, ['remainLoanLocationCd', 'remainLoanSite3No', 'remainLoanSite4No'], (!v));
                
            });
			
			//J-104-0276-001 額度明細檢核附表「建案完工未出售房屋融資註記」欄位，增加引進最近一次「建案完工未出售房屋融資註記」欄位。
			/**
             * 縣市變更
             */
            var bremainLoanLocationCd = $formObject.find("#bremainLoanLocationCd");
            $formObject.find("#bremainLoanLocationCity").setItems({
                item: QueryCityCode.getCode("1", ""),
                sort: "asc",
                value: LMS140M01MAction.formData['bremainLoanLocationCity'] || "",
                fn: function(k, v){
                
                    changCityValue($formObject.find("#bremainLoanLocationCity"), $formObject.find("#bremainLoanLocationCd"), $formObject.find("#bremainLoanSite3No"), $formObject.find("#bremainLoanSite4No"), defaultSelect, ['bremainLoanLocationCd', 'bremainLoanSite3No', 'bremainLoanSite4No'], (!v));
                }
            });
            
            /**
             * 鄉鎮切換
             */
            bremainLoanLocationCd.change(function(k, v){
            
                changAreaValue($formObject.find("#bremainLoanLocationCity"), $formObject.find("#bremainLoanLocationCd"), $formObject.find("#bremainLoanSite3No"), $formObject.find("#bremainLoanSite4No"), defaultSelect, ['bremainLoanLocationCd', 'bremainLoanSite3No', 'bremainLoanSite4No'], (!v));
                
            });
			
			
            
            /**
             * 央行購住/空地/興建房屋統計報表用相關資訊
             */
            $formObject.find("#cbcCase").change(function(){
                var value = $(this).val();
                
                //LMS140M01MAction.cleanTrHideInput($("#appAmtTr"));
                //$("#LMS140M01MForm").setData({'cbcCase':value}, true);
                if (!LMS140M01MAction.isInject) {
                    if (value == '1' || value == '2' || value == '7') {
                        LMS140M01MAction.getCMSData();
                    }
                }
				
				if(value == '1' || value == '5'){
					$formObject.find("#timeVal").val(LMS140M01MAction.formData.totalTimeVal);
				}
				
				CentralBankMortgageLoanManagement.switchLatestStartDate($formObject, l140m01mVersion);
            });


            /**
             * 開啟 購價/鑑價/估價登錄說明
             */
            $formObject.find("#getExplainPdf").click(function(){
				// 找出路徑
				var url_arr= document.location.pathname.split('/');
				var open_url = "../simple/FileProcessingService";
				if(url_arr.length==6){
					open_url = "../../simple/FileProcessingService";
				}

                $.form.submit({
                    url: open_url,
                    target: "_blank",
                    data: {
                        fileId: "LMS140M01MExplain.pdf",
                        fileDownloadName: "LMS140M01MExplain.pdf",
                        serviceName: "lmsfiledownloadservice"
                    }
                });
            });
            
            $("#landBuildYN").click(function(){

                if (LMS140M01MAction.isInject) {
                    return;
                }      
                LMS140M01MAction.ELF442Grid.jqGrid("setGridParam", {
                    postData: $.extend({
                        'custId': $("#custId").val(),
                        'dupNo': $("#dupNo").val()
                    }, LMS140M01MAction.ELF442Grid.getGridParam("postData") || {})
                }).trigger("reloadGrid");
                
                $('#ELF442ThickBox').thickbox({
                    title: i18n.lmsl140m01m['ELF442.Title'],//'引進土建融預約額度',
                    width: 750,
                    height: 250,
                    align: 'center',
                    valign: 'bottom',
                    i18n: i18n.def,
                    buttons: {
                        'sure': function(){
                            var data = LMS140M01MAction.ELF442Grid.getSingleData();
                            if (data) {
                                var fmt_elf442_site3no = util.addZeroBefore($.trim(data.elf442_site3no || ""), 4);
                                
                                $("#prodClass").val(data.elf442_prod_class);
                                $("#areaLand").val(data.elf442_land_area);
                                $("#waitMonth").val(data.elf442_wait_month);
                                $("#buildDate").val(data.elf442_build_date);
                                
                                $("#locationCity").val(data.elf442_location_cd.substring(0, 1));
                                $("#locationCity").trigger('change');
                                $("#locationCd").val(data.elf442_location_cd);
                                $("#locationCd").trigger('change');
                                $("#site3No").val(fmt_elf442_site3no);
                                $("#site4No").val(data.elf442_site4no);
                                //$("#landType").val(data.elf442_land_type);								
                                $("input[name='landType']").attr('checked', false).filter("[value='" + data.elf442_land_type + "']").trigger('click').attr("checked", true);
                                
                                $("#landBuildCntrno").val(data.elf442_cntrno);
                                
                                
                                $.thickbox.close();
                            }
                        },
                        'close': function(){
                        
                            //API.showMessage(i18n.lmsl140m01m['L140M01M.MSG004']);
                            // L140M01M.MSG004=本案尚無任何一筆有效的土建融預約額度，請至資料建檔系統中再次確認！
                            API.confirmMessage(i18n.lmsl140m01m['L140M01M.MSG004'], function(result){
                                if (result) {
//                                    $("input[name='landBuildYN'][value='N']").attr("checked", true).trigger("click");
//                                    ;
                                    $.thickbox.close();
                                }
                            });
                            //$.thickbox.close();
                        }
                    }
                });
            });
            
            //J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
			var item = API.loadOrderCombosAsList("lms7600_adoptFg")["lms7600_adoptFg"];
			$("#adoptFg").setItems({
				size: "3",
		        item: item,
				clear : true,
				itemType: 'checkbox' 
		    });
			
			
            //J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
            
			$formObject.find("[name=isClearLand]").change(function(){ 
				var isClearLand = $("input[name='isClearLand']:radio:checked" ).val(); 
	        	if (isClearLand == "Y") {
                    $("#showClearLand").show();
                }else{
                	$("#showClearLand").hide();
                	LMS140M01MAction.initClearLand($formObject); 
                }
            });
			
			$formObject.find("[name=isChgStDate]").change(function(){ 
				var isChgStDate = $("input[name='isChgStDate']:radio:checked" ).val(); 
	        	if (isChgStDate == "Y") {
	        		$(".showChgStDate").show();
                    $(".showIsChgRate").hide();
                    $(".showIsLegal").show();
                    
	        	}else if (isChgStDate == "N") {
	        		$(".showChgStDate").hide();
                    $(".showIsChgRate").show();   
                    
                    if($formObject.find("[name=isChgRate]:checked").val() == "Y"){
                		$(".showIsLegal").show();
                	}else{
                		$(".showIsLegal").hide();
                	}
                    
                }else{
                	$(".showChgStDate").hide();
                	$(".showIsChgRate").hide();
                	 if($formObject.find("[name=isChgRate]:checked").val() == "Y"){
                 		$(".showIsLegal").show();
                 	}else{
                 		$(".showIsLegal").hide();
                 	}
                }
	        	
	        	$formObject.find("#showClearLand").find("input:checkbox").trigger("change");
            });
			
			
			
			var $adoptFg = $formObject.find("input[name='adoptFg']");
            $adoptFg.change(function(){
            	
            	//增加4.無以上措施(再加碼幅度為0%)
                if ($formObject.find("[name=adoptFg][value=4]").attr("checked")) {
                	$formObject.find("[name='adoptFg']").filter("[value !='4']").attr("checked", false).attr("disabled", true);  
                	$formObject.find(".showChgRate").hide();
                	$formObject.find("[name=isChgRate][value=N]").click();
                }
                else {
                	$formObject.find("[name='adoptFg']").attr("disabled", false);
                	$formObject.find("[name='adoptFg']").filter("[value='4']").attr("checked", false);
                }
                
                
                if ($formObject.find("[name=adoptFg][value=3]").attr("checked")) {
                    $formObject.find(".showChgRate").show();
                    $formObject.find("[name=isChgRate][value=Y]").click();   
                }
                else {
                	$formObject.find(".showChgRate").hide();
                	$formObject.find("[name=isChgRate][value=N]").click();
                    
                }

            });
                    
            $formObject.find("[name=isChgRate]").change(function(){ 
            	var isChgRate = $("input[name='isChgRate']:radio:checked" ).val();
	        	if (isChgRate == "Y") {
                    $(".showChgRate").show();
                    $(".showIsLegal").show();
                }else{
                	$(".showChgRate").hide();
                	$formObject.find("#custRoa,#relRoa,#rateAdd,#roaBgnDate,#roaEndDate").val('');
                	
                	if($formObject.find("[name=isChgStDate]:checked").val() == "Y"){
                		$(".showIsLegal").show();
                	}else{
                		$(".showIsLegal").hide();
                	}
                }
            });
            
            $formObject.find("#btnApplyClearLand").click(function(){
            	applyClearLand();
            });
            
            $formObject.find("#btnApplyClearLandRoa").click(function(){
            	applyClearLandRoa();
            });
            
            $formObject.find("#btnApplyIsLegal").click(function(){
            	applyIsLegal();
            });
		
            
        },
        initChinaLoanEvent: function($formObject){
			
			//J-104-00279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			var item = API.loadOrderCombosAsList("L140M01Q_othCrdType")["L140M01Q_othCrdType"];
			$("#othCrdType").setItems({ size: "1", item: item, clear: true, itemType: 'checkbox' });
			$("#bothCrdType").setItems({ size: "1", item: item, clear: true, itemType: 'checkbox' });
								
            var $cnLoanFg = $formObject.find("input[name='cnLoanFg']");
            $cnLoanFg.click(function(){
                var cnLoanFg = $(this).val();
                if (cnLoanFg == "Y") {
                    $formObject.find(".chinaLoan").show();
					//J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
			        $('#directFg').change();
					$('#bdirectFg').change();
                }
                else {
                    $formObject.find(".chinaLoan").hide();
                    $formObject.find("input[name='iGolFlag']").attr("checked", false);
                    $formObject.find("#directFg").val('');
					$formObject.find("#cnBusKind").val('');
                    $formObject.find("input[name='sTradeFg']").attr("checked", false);
                    $formObject.find("input[name='rickTrFg']").attr("checked", false);
                    $formObject.find("#guar1Rate,#guar2Rate,#guar3Rate,#coll1Rate,#coll2Rate,#coll3Rate,#coll4Rate,#coll5Rate").val('0');
					
					//J-105-0074-001 Web e-Loan 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
					$formObject.find("input[name='nCnSblcFg']").attr("checked", false);
					
					//J-112-0462 「對大陸地區授信業務控管註記」新增三提問
					//本案資金流向是否為中國
					var fundsToCnVal = $("input[name='fundsToCn']:radio:checked").val();
					//借款人之股權結構往上追溯至中國籍股東止，其合計中國籍股東持股達50%
					var cnShareholderVal = $("input[name='cnShareholder']:radio:checked").val();
					//本案任一保證人國籍/註冊地是否為中國
					var cnGuarantorVal = $("input[name='cnGuarantor']:radio:checked").val();
					if( fundsToCnVal == "Y" || cnShareholderVal == "Y" || cnGuarantorVal == "Y"){
						API.showMessage(i18n.lmsl140m01m['L140M01Q.MSG002']);
					}
                }
            });
            
            var $sTradeFg = $formObject.find("input[name='sTradeFg']");
            $sTradeFg.click(function(){
                var sTradeFg = $(this).val();
                if (sTradeFg == "N") {
                    $formObject.find(".sTrade").show();
                }
                else {
                    $formObject.find(".sTrade").hide();
                    $formObject.find("input[name='rickTrFg']").attr("checked", false);
                    $formObject.find("#guar1Rate,#guar2Rate,#guar3Rate,#coll1Rate,#coll2Rate,#coll3Rate,#coll4Rate,#coll5Rate").val('0');
                }
            });
            
            var $rickTrFg = $formObject.find("input[name='rickTrFg']");
            $rickTrFg.click(function(){
                var rickTrFg = $(this).val();
                if (rickTrFg == "Y") {
                    $formObject.find(".rickTr").show();
                }
                else {
                    $formObject.find(".rickTr").hide();
                    $formObject.find("#guar1Rate,#guar2Rate,#guar3Rate,#coll1Rate,#coll2Rate,#coll3Rate,#coll4Rate,#coll5Rate").val('0');
                }
            });
			
			//J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
			//授信對象別代碼切換
		    $('#directFg').change(function(){
		        var value = $(this).val();
				 switch (value) {
		            case "12":
		            case "14":
					    $("#showCnBusKind").show();
						//$("#showBcnBusKind").show();
		                break;
		            default:
					    $("#showCnBusKind").hide();
						//$("#showBcnBusKind").hide();
		                break;
		        }
		    });
            $('#bdirectFg').change(function(){
		        var value = $(this).val();
				 switch (value) {
		            case "12":
		            case "14":
					    //$("#showCnBusKind").show();
						$("#showBcnBusKind").show();
		                break;
		            default:
					    //$("#showCnBusKind").hide();
						$("#showBcnBusKind").hide();
		                break;
		        }
		    });
				
			//J-104-0073-001 Web e-Loan授信系統修改大陸地區控管註記授信對象別檢核。			
            $('#cnBusKind').click(function(){
				var value = $("#directFg").val();
				switch (value) {
		            case "12":
					    $("#cnBusKind option[value='A']").attr('disabled', false);
				        $("#cnBusKind option[value='B']").attr('disabled', false);
		                break;
		            case "14":
					    $("#cnBusKind option[value='A']").attr('disabled', true);
						$("#cnBusKind option[value='B']").attr('disabled', true);
		                break;
		            default:   
					    $("#cnBusKind option[value='A']").attr('disabled', false);
				        $("#cnBusKind option[value='B']").attr('disabled', false);
		                break;
		        }
				
				
		    }); 
			//J-104-0073-001 Web e-Loan授信系統修改大陸地區控管註記授信對象別檢核。
			$('#directFg').click(function(){
				var value = $("#directFg").val();
				switch (value) {
		            case "12":
					    $("#cnBusKind option[value='A']").attr('disabled', false);
				        $("#cnBusKind option[value='B']").attr('disabled', false);
		                break;
		            case "14":
					    $("#cnBusKind option[value='A']").attr('disabled', true);
						$("#cnBusKind option[value='B']").attr('disabled', true);
						break;
		            default:   
					    $("#cnBusKind option[value='A']").attr('disabled', false);
				        $("#cnBusKind option[value='B']").attr('disabled', false);
		                break;
		        }
				
		    }); 
			
			
			
			//BGN J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別 center 
			$('#btnChoiceLoanTarget').click(function(){
				//$.thickbox.close();
				if(!lms140_loanTarget ){
					LMS140M01MAction.initLoanTarget();
				} 
				
				$('#choiceLoanTarget').thickbox({
                    title: i18n.lmsl140m01m['L140M01Q.login']+i18n.lmsl140m01m['L140M01Q.loanTarget'],//登錄+授信對象別
                    width: 750,
                    height: 100,
                    align: 'center',
                    valign: 'bottom',
                    i18n: i18n.def,
                    buttons: {
                        'sure': function(){
                            var _loanTarget = $("#_loanTarget").val();
							$("#loanTarget").val(_loanTarget);
							var _loanTargetDscr = buildLoanTargetDscr(_loanTarget);
							$("#loanTargetDscr").val(_loanTargetDscr);
							$("#loanTarget").triggerHandler("change")
                            $.thickbox.close();
                        },
                        'close': function(){
                            $.thickbox.close();
                        }
                    }
                });
				
		    }); 
			
			
			 
			var $iGolFlag = $formObject.find("input[name='iGolFlag']");
			$iGolFlag.click(function(){
				var iGolFlag = $(this).val();
				 switch (iGolFlag) {
		            case "Y":
					    $("#isType").show();
		                break;
		            default:
					    $("#isType").hide();
		                break;
		        }
		    });	
			
			 
			var $loanTarget = $formObject.find("#loanTarget");
			$loanTarget.change(function(){
				var loanTarget = $(this).val();
				if(loanTarget){
					if(loanTarget.substring(0,2) == "12"){
					   $("#grntType").show();
					}else{
					   $("#grntType").hide();
					}
				}else{
					$("#grntType").hide();
				}
				
		    });	
			
			
			var $grntClass = $formObject.find("#grntClass");
			$grntClass.change(function(){
				var grntClass = $(this).val();
				if(grntClass){ 
					if(grntClass == "2"){
					   $("#showOthCrdType").show();
					}else{
					   $("#showOthCrdType").hide();
					}
				}else{
					$("#showOthCrdType").hide();
				}
				
		    });	
			
			var $bgrntClass = $formObject.find("#bgrntClass");
			$bgrntClass.change(function(){
				var bgrntClass = $(this).val();
				if(bgrntClass){ 
					if(bgrntClass == "2"){
					   $("#showBOthCrdType").show();
					}else{
					   $("#showBOthCrdType").hide();
					}
				}else{
					$("#showBOthCrdType").hide();
				}
				
		    });	
			//END J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別 center
			
			// J-112-0462 「對大陸地區授信業務控管註記」新增三提問
			//本案資金流向是否為中國
			var $fundsToCn = $formObject.find("input[name='fundsToCn']");
			$fundsToCn.click(function(){
                var fundsToCn = $(this).val();
                if(fundsToCn == "Y"){
                	$formObject.find("input[name='cnLoanFg'][value='Y']").attr("checked", true);
                }
            });
			//借款人之股權結構往上追溯至中國籍股東止，其合計中國籍股東持股達50%
			var $cnShareholder = $formObject.find("input[name='cnShareholder']");
			$cnShareholder.click(function(){
                var fundsToCn = $(this).val();
                if(fundsToCn == "Y"){
                	$formObject.find("input[name='cnLoanFg'][value='Y']").attr("checked", true);
                }
            });
			//本案任一保證人國籍/註冊地是否為中國
			var $cnGuarantor = $formObject.find("input[name='cnGuarantor']");
			$cnGuarantor.click(function(){
                var fundsToCn = $(this).val();
                if(fundsToCn == "Y"){
                	$formObject.find("input[name='cnLoanFg'][value='Y']").attr("checked", true);
                }
            });
				
        },
        // J-108-0283 變更條件Condition Change
        initCondChgEvent: function($formObject, obj, readonly){
            var item = API.loadCombos(["L140S05A_quantize", "Common_Currcy"]);
            $("[name^='quant_']").setItems({
                item: item.L140S05A_quantize,
                format: "{key}",
                space: false,
				disabled: readonly
            });

            // 本位幣以外的幣別
            $(".foreignCur").setItems({
                item: item.Common_Currcy,
                format: "{value} - {key}"
            });
            $(".foreignCur").find('option[value="'+obj.localCurr+'"]').remove();

            var removeC = ["intRateL", "intRateF", "rateL", "rateF",
                            "grace", "drawdown", "loan"];
            for(var n in removeC){
                var name = removeC[n];
                $("#quant_" + name).find('option[value="C"]').remove();
            }

            $("[name^='quant_']").each(function(v, k){
                $(this).val('X');	// 預設 N/A

                $(this).change(function(){
//					var id = $(this).attr('id');
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    if(name.length >= 1){
                        name = name.split("_").pop();
                    }
                    var pass = ['coll', 'guarantor', 'commitments'];
                    //if (!pass.includes(name)) {
                    if ($.inArray(name, pass) == -1 ) {
                        if(value != 'X'){
                            $("#span_"+name).show();
                        } else {
                            $("#span_"+name).hide();
                            // 本位幣不清空
                            $("#span_"+name).children().not(".localCur").each(function(){
                                $(this).val('');
                            });
                        }
                    }
                });
            });
        },
        /**
         * 查詢還是已修改
         */
        isforQuery: false,
        /**
         * 控制「擔保品總貸款額度」(新臺幣元)顯示
         */
        setShareCollAmt: function($formObject){
            var shareCollYN = $formObject.find("[name=shareCollYN]:checked").val();
            var commonYN = $formObject.find("[name=commonYN]:checked").val();
            var $shareCollAmtTr = $formObject.find("#shareCollAmtTr");
            if (shareCollYN == "Y" || commonYN == "Y") {
                $shareCollAmtTr.show();
            }
            else {
                //如果shareCollYN & commonYN皆等於N時 ，但有金額時要將金額清掉
                $formObject.find("#shareCollAmt").val("");
                
                $shareCollAmtTr.hide();
            }
        },
        /**
         * 開啟央行購住
         *
         * @param {Object}
         *            cntrNoMainId 額度明細表主檔mainId
         * @param {Object}
         *            isforQuery 是否為查詢
         * @param {Object}
         *            showIsClearLand 國內企金開啟的時候(true)才要顯示空地貸款控管機制

         */
        openBox: function(cntrNoMainId, isforQuery,showIsClearLand, formObject){
            var $form = $("#" + LMS140M01MAction.formId);
            ilog.debug("openBox @ LMSL140M01MPanel.js");
            
            //國內企金(消金暫時沒有)才要顯示 (目前企消金皆開放可以修改)
//            if(showIsClearLand){
//            	$('#L140M01M_clear_land_ctrl').show();
//            }else{
//            	$('#L140M01M_clear_land_ctrl').hide();
//            }
			
			var cbRuleVersion = $("#cbLoanVersion").val();
			
			
			$.ajax({
                handler: LMS140M01MAction.fhandle,
                action: "queryL140M01M",
                data: {
                    cntrNoMainId: cntrNoMainId,
                    noOpenDoc: true
                },
                success: function(obj){
                    LMS140M01MAction.formData = obj;
					if(true){
						/*
						 * 在額度明細表開啟時，才有 openBox(...)
						 * 於左邊選單＞央行註記異動作業：開啟頁面時，不會 call openBox(...)
						 */
						var cls_bef_1050325 = false;
						
						if (typeof(_M) != 'undefined') {
							var _dataSrc = "";
							if(_M.AllFormData && _M.AllFormData["04"] && _M.AllFormData["04"].dataSrc){
								_dataSrc = _M.AllFormData["04"].dataSrc;
							}
							
							if( _M.tabMainId 
								&& _dataSrc!="" && _dataSrc!="1" && _dataSrc!="2" 
								&& obj.custYN && obj.custYN!=""){
								cls_bef_1050325 = true;	
							}								
						}
						
						if(cls_bef_1050325){
							$form.find("#custYNTr").addClass("L140M01M_cbcCase1");
						}else{
							//LMS or cls_af_1050325 or Menu-央行註記異動作業
						}												
					}
					
                    LMS140M01MAction.init($form, cbRuleVersion);
					obj.cbRuleVersion = cbRuleVersion;
					LMS140M01MAction.isInject = true;
					$form.injectData(obj);
					$("#cbRuleVersion").val(cbRuleVersion);
					LMS140M01MAction.isInject = false;
					 $form.find("select").trigger("change", "init");
					LMS140M01MAction.setShareCollAmt($form);
					$form.readOnlyChilds(isforQuery, "#prodClass");
                    if (!obj.checkYN || obj.checkYN != "Y") {
                        $form.find("#checkYNtoShow").show();
                    }
                    else {
                        $form.find("#checkYNtoShow").hide();
                    }
					
					CentralBankMortgageLoanManagement.switchIs3rdHighHouseOption(cbRuleVersion, obj.peopleMortgageDetail, $form);
                    
					//J-104-0276-001 額度明細檢核附表「建案完工未出售房屋融資註記」欄位，增加引進最近一次「建案完工未出售房屋融資註記」欄位。
					$form.find("#bremainLoanYN").attr('disabled', true);
					$form.find("#bremainLoanClass").attr('disabled', true);
					$form.find("#bremainLoanLocationCity").attr('disabled', true);
					$form.find("#bremainLoanLocationCd").attr('disabled', true);
                    $form.find("#bremainLoanSite3No").attr('disabled', true);
					$form.find("#bremainLoanSite4No").attr('disabled', true);
					
					//載入 J-109-0226 建案餘屋及貸款資料控制 default value
					LMS140M01MAction.initFinancingDataForUnsoldHouseInFinishedConstruction();
					
					if(true){
						//在帳務轉換時【取消額度、新簽額度】仍有需要輸入之前核貸的狀態
						//$form.find("input[name=cbcCase][value=2]").attr("disabled",true);
					}
					
					//J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
					$form.find("#isClearLand").attr('disabled', true);
					$form.find("#ctlType").attr('disabled', true);
//					$form.find("#fstDate").attr('disabled', true);
//					$form.find("#lstDate").attr('disabled', true);
					$form.find("#isLegal").attr('disabled', true);
					
					if(obj.adoptFg){
						var vals = obj.adoptFg.split("|");
		                for (var i in vals) {
		                    $("[name=adoptFg][value=" + vals[i] + "]").attr("checked", true);
		                }
					}
					
					$form.find("#L140M01M_clear_land_ctrl").find("input:radio" ).trigger("change");  
					$form.find("#L140M01M_clear_land_ctrl").find("input:checkbox").trigger("change");
					 
					ClearLandLoanOfNotConstructed.setInitialValue($form, obj); 
                }
            });
            var btn = {
                "saveData": function(){
                	
                	//國內分行(暫時企金)才要檢核，未來國內消金也要檢核
                	if(showIsClearLand){
                		//J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
                    	var isClearLand = $("input[name='isClearLand']:radio:checked" ).val();
                		var isChgStDate = $("input[name='isChgStDate']:radio:checked" ).val();
                		var isChgRate = $("input[name='isChgRate']:radio:checked" ).val();
                		
                		if(!isClearLand){
                    		return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.isClearLand"]+ i18n.def['val.required']);
                    	}
                    	 
                    	if(isClearLand == "Y"){
                			
                    		if(!isChgStDate){
                    			return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.isChgStDate"]+ i18n.def['val.required']);
                    		}
                    		
                    		if($form.find("#fstDate").val() == ""){
            					return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.fstDate"]+ i18n.def['val.required']);
            				}
                    		
                    		
                    		if($form.find("#lstDate").val() == ""){
            					return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.lstDate"]+ i18n.def['val.required']);
            				}
                    		
                			if(isChgStDate == "Y"){
                				if($form.find("#cstDate").val() == ""){
                					return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.cstDate"]+ i18n.def['val.required']);
                				}
                				
                				if($form.find("#cstReason").val() == ""){
                					return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.cstReason"]+ i18n.def['val.required']);
                				}
                				
                				var hasAdoptFg = "";
                		        $("[name=adoptFg]:checked").each(function(v, k){
                		        	hasAdoptFg ="Y";
                		        }); 
                		        if( hasAdoptFg == ""){
                					return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.adoptFg"]+ i18n.def['val.required']);
                				}   
                			}else{
                				if(!isChgRate){
                        			return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.isChgRate"]+ i18n.def['val.required']);
                        		}
                			}

                        	if(isChgRate == "Y" || (isChgStDate=="Y" && $form.find("[name=adoptFg][value=3]").attr("checked") )  ){
                        		if($form.find("#rateAdd").val() == ""){
                					return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.rateAdd"]+ i18n.def['val.required']);
                				}
                        		
                        		if($form.find("#custRoa").val() == ""){
                					return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.custRoa"]+ i18n.def['val.required']);
                				}
                        		
                        		if($form.find("#relRoa").val() == ""){
                					return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.relRoa"]+ i18n.def['val.required']);
                				}
                        	}
                        	
                        	//重新計算是否符合本行規定
                        	if( isChgStDate== "Y" || isChgRate== "Y"){
                				checkIsLegal();
                			} 
          
                    	} 
                	}
                	
                	
                	
                    if ($form.valid()) {
                    	
                        var orgSno = $("#LMS140M01MForm").find("#landBuildCntrno").val();
						
						//J-104-0276-001 額度明細檢核附表「建案完工未出售房屋融資註記」欄位，增加引進最近一次「建案完工未出售房屋融資註記」欄位。
						if (!$("input[name='bremainLoanYN']:checked").val() ) {
						    //L140M01M.err016=前次是否為建案完工未出售房屋之融資欄位不得空白，請先執行引進功能！
					        return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.err016"]);
						}	
						
						if($("input[name='landBuildYN']:checked").val()=="Y"){
							//驗證舊有額度序號規則
					        if (!orgSno.match(/\w{12}/)) {
					            //L140M01a.message68=額度序號長度應為12碼，編碼原則:XXX(分行代號)+X(1:DBU,4:OBU,5:海外)+YYY(年度)+99999(流水號)
					            return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01a.message68"]);
					        }

								$.ajax({
					            handler: "lmscommonformhandler",
					            async: false,
					            action: "checkCntrno",
					            data: {
									custId: $("#custId").val(),
									dupNo: $("#dupNo").val(),
					                cntrNo: orgSno 
					            },
					            success: function(obj){

									$.ajax({
			                            handler: LMS140M01MAction.fhandle,
			                            formId: LMS140M01MAction.formId,
			                            action: "saveL140M01M",
			                            data: {
			                                cntrNoMainId: cntrNoMainId,
			                                LMS140M01MForm: JSON.stringify($("#" + LMS140M01MAction.formId).serializeData()),
											cbLoanVersion: $("#cbRuleVersion").val()
			                            },
			                            success: function(obj){
			                                var isChangCntrno = (obj.isChangCntrno);

			                                if (isChangCntrno == "Y") {
												
			                                    // L140M01M.MSG005=本案額度明細表中的額度序號和現行所勾選的土建融預約額度序號不一致，是否依土建融預約額度序號覆蓋額度明細表中額度序號？
			                                    API.confirmMessage(i18n.lmsl140m01m['L140M01M.MSG005'], function(result){
			                                        if (result) {
			                                            $.ajax({
			                                                handler: LMS140M01MAction.fhandle,
			                                                formId: LMS140M01MAction.formId,
			                                                action: "compelSaveL140M01M",
			                                                data: {
			                                                    cntrNoMainId: cntrNoMainId,
			                                                    LMS140M01MForm: JSON.stringify($("#" + LMS140M01MAction.formId).serializeData())
			                                                },
			                                                success: function(obj){
			                                                    if (typeof(_M) == 'undefined') {
			
			                                                        $("#L140M01AForm2").find("#cntrNo").val(obj.cntrNo);
																	$("#L140M01AForm1").find("#lnType").val(obj.prodClass);
			                                                    }
			                                                    else {
			                                                        
			                                                        _M._triggerMainGrid();
			                                                        _M.AllFormData["04"]["cntrNo"] = (obj.cntrNo);
			                                                    }
			                                                }
			                                            });
			                                            //$.thickbox.close();
			                                        }
			                                    });
			                                }
			                                else {
			                                    if (typeof(_M) == 'undefined') {
			                                        //$("#L140M01AForm2").find("#showCntrNoName").html("V分行");
			                                        $("#L140M01AForm2").find("#cntrNo").val(obj.cntrNo);
													$("#L140M01AForm1").find("#lnType").val(obj.prodClass);
			                                    }
			                                    else {
			                                        _M._triggerMainGrid();
			                                        _M.AllFormData["04"]["cntrNo"] = (obj.cntrNo);
			                                    }
			                                }
			                            }
			                        });
								}
							});
						}else{
							var cbRuleVersion = $("#cbRuleVersion").val();
							$.ajax({
		                            handler: LMS140M01MAction.fhandle,
		                            formId: LMS140M01MAction.formId,
		                            action: "saveL140M01M",
		                            data: {
										cbLoanVersion: cbRuleVersion,
		                                cntrNoMainId: cntrNoMainId,
		                                LMS140M01MForm: JSON.stringify($("#" + LMS140M01MAction.formId).serializeData())
		                            },
		                            success: function(obj){
		                                var isChangCntrno = (obj.isChangCntrno);
		                                
		                                if (isChangCntrno == "Y") {
		                                    // L140M01M.MSG005=本案額度明細表中的額度序號和現行所勾選的土建融預約額度序號不一致，是否依土建融預約額度序號覆蓋額度明細表中額度序號？
		                                    API.confirmMessage(i18n.lmsl140m01m['L140M01M.MSG005'], function(result){
		                                        if (result) {
		                                            $.ajax({
		                                                handler: LMS140M01MAction.fhandle,
		                                                formId: LMS140M01MAction.formId,
		                                                action: "compelSaveL140M01M",
		                                                data: {
		                                                    cntrNoMainId: cntrNoMainId,
		                                                    LMS140M01MForm: JSON.stringify($("#" + LMS140M01MAction.formId).serializeData())
		                                                },
		                                                success: function(obj){
		                                                    if (typeof(_M) == 'undefined') {
		
		                                                        $("#L140M01AForm2").find("#cntrNo").val(obj.cntrNo);
 
																if(obj.prodClass != "" && obj.prodClass != 'undefined'){
																	$("#L140M01AForm1").find("#lnType").val(obj.prodClass);
																}															
		                                                    }
		                                                    else {
		                                                        
		                                                        _M._triggerMainGrid();
		                                                        _M.AllFormData["04"]["cntrNo"] = (obj.cntrNo);
		                                                    }
		                                                }
		                                            });
		                                            //$.thickbox.close();
		                                        }
		                                    });
		                                }
		                                else {
		                                    if (typeof(_M) == 'undefined') {
		                                        //$("#L140M01AForm2").find("#showCntrNoName").html("V分行");
		                                        $("#L140M01AForm2").find("#cntrNo").val(obj.cntrNo);
								
												if(obj.prodClass != "" && obj.prodClass != 'undefined'){
													$("#L140M01AForm1").find("#lnType").val(obj.prodClass);
												}									
		                                    }
		                                    else {
		                                        _M._triggerMainGrid();
		                                        _M.AllFormData["04"]["cntrNo"] = (obj.cntrNo);
		                                    }
		                                }
		                            }
		                        });
						}
						
						
						 
                        
                    }
                },
                "close": function(){

                    $.thickbox.close();
					//J-108-0097  購置高價住宅貸款檢核表
					formObject.find("#grid1").jqGrid("setGridParam", {
		                postData:{
							l140m01aMainId: cntrNoMainId,
		                    custId: $("#custId").val(),
				 			dupNo:  $("#dupNo").val()
		                }
		            }).trigger("reloadGrid");
                }
            }
            if (isforQuery) {
                delete btn["saveData"];
            }
			
            $("#openBox_L140M01M").thickbox({
                title: i18n.def['common.L140M01M'] + '-' + CentralBankMortgageLoanManagement.getTitleVersion(cbRuleVersion),
                width: 850,
                height: 530,
                modal: true,
                readOnly: _openerLockDoc == "1",
                i18n: i18n.def,
                buttons: btn
            });
        },
        /**
         * 對大陸地區授信業務控管註記
         *
         * @param {Object}
         *            cntrNoMainId
         * @param {Object}
         *            isforQuery
         */
        openChinaLoan: function(cntrNoMainId, isforQuery){
            var $form = $("#LMS140M01QForm");
            $.ajax({
                handler: LMS140M01MAction.fhandle,
                action: "queryL140M01Q",
                data: {
                    cntrNoMainId: cntrNoMainId,
                    noOpenDoc: true
                },
                success: function(obj){
                    LMS140M01MAction.initChinaLoan($form);
					
					//J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
					LMS140M01MAction.initLoanTarget();
	
					$form.injectData(obj);

					//BGN J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
					var loanTarget = $("#loanTarget").val();
					if(loanTarget){
						$("#loanTargetDscr").val(buildLoanTargetDscr(loanTarget));
					}
					$("#loanTarget").triggerHandler("change");
					$("#grntClass").triggerHandler("change");
					
					var bloanTarget = $("#bloanTarget").val();
					if(bloanTarget){
						$("#bloanTargetDscr").val(buildLoanTargetDscr(bloanTarget));
					}
					$("#bloanTarget").triggerHandler("change");
					$("#bgrntClass").triggerHandler("change");
					
					//END J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別

                    if (obj.showBefore) {
                        $form.find(".showBefore").show();
                    }
                    else {
                        $form.find(".showBefore").hide();
                    }
                    
                    if (obj.showDerv) {
                        $form.find(".showDerv").show();
                    }
                    else {
                        $form.find(".showDerv").hide();
                    }
                    
					//J-105-0074-001 Web e-Loan 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
					
					if(obj.showForCNCntrno){
						$form.find(".showForCNCntrno").show();
					}else{
						$form.find(".showForCNCntrno").hide();
					}
					
                    $form.readOnlyChilds(isforQuery, ".noEdit");
                    
                    
                    $("#cnTMUFg option[value='-']").attr('disabled', true);
                    
                    $('#cnTMUFg').each(function(){
                        this.rejectDisabled = function(){
                            if (this.options[this.selectedIndex].disabled) {
                                if (this.lastSelectedIndex) {
                                    this.selectedIndex = this.lastSelectedIndex;
                                }
                                else {
                                    var first_enabled = $(this).children('option:not(:disabled)').get(0);
                                    this.selectedIndex = first_enabled ? first_enabled.index : 0;
                                }
                            }
                            else {
                                this.lastSelectedIndex = this.selectedIndex;
                            }
                        };
                        this.rejectDisabled();
                        this.lastSelectedIndex = this.selectedIndex;
                        $(this).children('option[disabled]').each(function(){
                            $(this).css('color', '#CCC');
                        });
                        $(this).change(function(){
                            this.rejectDisabled();
                        });
                    });
					
					//J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
					
                    // J-112-0462 「對大陸地區授信業務控管註記」新增三提問  
					if(obj.m01aDocUrl == "CLS"){// 消金
						// 不適用「借款人之股權結構往上追溯至中國籍股東止，其合計中國籍股東持股達50%者，請選是」
						// 鎖定欄位並預設「否」
						$form.find("input[name='cnShareholder']").readOnly(true);
						if(obj.cnShareholder == undefined || obj.cnShareholder == "" ){
							$form.find("input[name='cnShareholder'][value='N']").attr("checked", true);
						}
					}
					
					
					//J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
					$('#directFg').change();
                    $('#bdirectFg').change();
                },
                error: function(obj){
                    $.thickbox.close();
                }
            });
            
            var btn = {
                "saveData": function(){
                    var $rickTrFg = $form.find("input[name='rickTrFg']");
                    if ($form.find("input[name='rickTrFg']:checked").val() == "Y") {
                        var guar1Rate = $form.find("#guar1Rate").val();
                        guar1Rate = isNaN(guar1Rate) || guar1Rate == "" ? 0 : parseInt(guar1Rate, 10);
                        var guar2Rate = $form.find("#guar2Rate").val();
                        guar2Rate = isNaN(guar2Rate) || guar2Rate == "" ? 0 : parseInt(guar2Rate, 10);
                        var guar3Rate = $form.find("#guar3Rate").val();
                        guar3Rate = isNaN(guar3Rate) || guar3Rate == "" ? 0 : parseInt(guar3Rate, 10);
                        var coll1Rate = $form.find("#coll1Rate").val();
                        coll1Rate = isNaN(coll1Rate) || coll1Rate == "" ? 0 : parseInt(coll1Rate, 10);
                        var coll2Rate = $form.find("#coll2Rate").val();
                        coll2Rate = isNaN(coll2Rate) || coll2Rate == "" ? 0 : parseInt(coll2Rate, 10);
                        var coll3Rate = $form.find("#coll3Rate").val();
                        coll3Rate = isNaN(coll3Rate) || coll3Rate == "" ? 0 : parseInt(coll3Rate, 10);
                        var coll4Rate = $form.find("#coll4Rate").val();
                        coll4Rate = isNaN(coll4Rate) || coll4Rate == "" ? 0 : parseInt(coll4Rate, 10);
                        var coll5Rate = $form.find("#coll5Rate").val();
                        coll5Rate = isNaN(coll5Rate) || coll5Rate == "" ? 0 : parseInt(coll5Rate, 10);
                        
                        var total = (guar1Rate + guar2Rate + guar3Rate +
                        coll1Rate +
                        coll2Rate +
                        coll3Rate +
                        coll4Rate +
                        coll5Rate);
                        
                        if (!(total == 100)) {
                            API.showMessage(i18n.lmsl140m01m['L140M01Q.MSG001']);
                            return false;
                        }
                    }
                    
					var directFg = $form.find("#directFg").val();
					if(directFg =="12" || directFg == "14"){
						//J-104-0073-001 Web e-Loan授信系統修改大陸地區控管註記授信對象別檢核。	
						if (directFg == "14") {
							var cnBusKindValue = $form.find("#cnBusKind").val();
							switch (cnBusKindValue) {
					            case "A":
								case "B":
								    $("#cnBusKind").val("");
					                break;
					            default:   
					                break;
					        } 
						}
						
						
						
						var cnBusKind = $form.find("#cnBusKind").val();
						if(cnBusKind == "" || cnBusKind == "undefined"){
							API.showMessage("「"+i18n.lmsl140m01m['L140M01Q.CNBUSKIND']+"」"+i18n.def['val.required']);
                            return false;
						}					
					}
					
                    if ($form.valid()) {
                        $.ajax({
                            handler: LMS140M01MAction.fhandle,
                            formId: LMS140M01MAction.formId,
                            action: "saveL140M01Q",
                            data: {
                                cntrNoMainId: cntrNoMainId,
                                LMS140M01QForm: JSON.stringify($("#LMS140M01QForm").serializeData())
                            },
                            success: function(obj){
                            
                            }
                        });
                    }
                },
                "reQuery": function(){
                    $.ajax({
                        handler: LMS140M01MAction.fhandle,
                        action: "requeryHistoryToL140M01Q",
                        data: {
                            cntrNoMainId: cntrNoMainId,
                            LMS140M01QForm: JSON.stringify($("#LMS140M01QForm").serializeData()),
                            noOpenDoc: true
                        },
                        success: function(obj){
							
							//J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
							//J-105-0074-001 Web e-Loan 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
                            $form.find("input[name='bcnLoanFg'],input[name='biGolFlag'],input[name='bsTradeFg'],input[name='brickTrFg'],input[name='bunionArea3'],input[name='bnCnSblcFg']").attr("checked", false);
                            $form.find("#bdirectFg").val('');

							$form.find("#bcnBusKind").val('');
                            $form.find("#bguar1Rate,#bguar2Rate,#bguar3Rate,#bcoll1Rate,#bcoll2Rate,#bcoll3Rate,#bcoll4Rate,#bcoll5Rate").val('');
							
							//BGN J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
							$form.find("#bisType").val('');
							$form.find("#bgrntType").val('');
							$form.find("#bgrntClass").val('');
							$( "[name=bothCrdType]"). removeAttr("checked" );
							$form.find("#bloanTarget").val('');
							$form.find("#bloanTargetDscr").val('');
							//END J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
							 
                            LMS140M01MAction.initChinaLoan($form);
							
							//J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
							LMS140M01MAction.initLoanTarget();
							
		                    $form.injectData(obj);
							
							//BGN J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
							var loanTarget = $("#loanTarget").val();
							if(loanTarget){
								$("#loanTargetDscr").val(buildLoanTargetDscr(loanTarget));
							}
							$("#loanTarget").triggerHandler("change");
							$("#grntClass").triggerHandler("change");
							
							var bloanTarget = $("#bloanTarget").val();
							if(bloanTarget){
								$("#bloanTargetDscr").val(buildLoanTargetDscr(bloanTarget));
							}
							$("#bloanTarget").triggerHandler("change");
							$("#bgrntClass").triggerHandler("change");
							
							//END J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
                            
                            if (obj.showBefore) {
                                $form.find(".showBefore").show();
                            }
                            else {
                                $form.find(".showBefore").hide();
                            }
                            
                            if (obj.showDerv) {
                                $form.find(".showDerv").show();
                            }
                            else {
                                $form.find(".showDerv").hide();
                            }
							
							//J-105-0074-001 Web e-Loan 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
							if(obj.showForCNCntrno){
								
								$form.find(".showForCNCntrno").show();
							}else{
								$form.find(".showForCNCntrno").hide();
							}
					
                            //J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
					        $('#directFg').change();
							$('#bdirectFg').change();
                        },
                        error: function(obj){
                            $.thickbox.close();
                        }
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            };
            if (isforQuery) {
                delete btn["saveData"];
                delete btn["reQuery"];
            }
            $("#openBox_L140M01Q").thickbox({
                title: i18n.def['common.L140M01Q'],
                width: 850,
                height: 530,
                modal: true,
                readOnly: _openerLockDoc == "1",
                i18n: i18n.def,
                buttons: btn
            });
        },
        // J-108-0283 變更條件Condition Change
        openCondChg: function(cntrNoMainId, readonly){
             var $form = $("#LMS140S05AForm");
             $.ajax({
                 handler: LMS140M01MAction.fhandle,
                 action: "queryL140S05A",
                 data: {
                     cntrNoMainId: cntrNoMainId
                 },
                 success: function(obj){
                    LMS140M01MAction.initCondChg($form, obj, readonly);

                    $form.injectData(obj);

                    $("[name^='quant_']").each(function(v, k){
                        var name = $(this).attr('name');
                        var value = $(this).val();
                        if(name.length >= 1){
                            name = name.split("_").pop();
                        }
                        var pass = ['coll', 'guarantor', 'commitments'];
                        //if (!pass.includes(name)) {
                        if ($.inArray(name, pass) == -1 ) {
                            if(value != 'X'){
                                $("#span_"+name).show();
                            } else {
                                $("#span_"+name).hide();
                            }
                        }
                    });
                 },
                 error: function(obj){
                     $.thickbox.close();
                 }
             });

             var btn = {
                 "saveData": function(){
                     if ($form.valid()) {
                         $.ajax({
                             handler: LMS140M01MAction.fhandle,
                             formId: LMS140M01MAction.formId,
                             action: "saveL140S05A",
                             data: {
                                 cntrNoMainId: cntrNoMainId,
                                 LMS140S05AForm: JSON.stringify($("#LMS140S05AForm").serializeData())
                             },
                             success: function(obj){

                             }
                         });
                     }
                 },
                 "close": function(){
                     $.thickbox.close();
                 }
             };
             if (readonly) {
                 delete btn["saveData"];
                 $form.lockDoc(0);
             }

             $("#openBox_L140S05A").thickbox({
                 title: i18n.def['common.L140S05A'],
                 width: 850,
                 height: 530,
                 modal: true,
                 readOnly: _openerLockDoc == "1" || readonly,
                 i18n: i18n.def,
                 buttons: btn
             });

             // 敘作條件異動比較表
//            openChgCf._init(cntrNoMainId, readonly);
         },
		 // J-109-0226 引進最近一次「建案完工未出售房屋融資註記」欄位。
		 initFinancingDataForUnsoldHouseInFinishedConstruction : function(){
		 	
			var isOpenUnsoldHouseLoanInfoFunction;
			
			$.ajax({
	            handler: 'lmscommonformhandler',
	            action: "isOpenUnsoldHouseLoanInfoFunction",
				async:false,
	            data: {
	            },
	            success: function(obj){
					isOpenUnsoldHouseLoanInfoFunction = obj.isOpenUnsoldHouseLoanInfoFunction;
	            }
			});

			//判斷由"央行註記異動作業"功能選單進來並為授審處才可修改餘屋貸款資料
			var txCode = responseJSON.txCode;
			var isShowUnsoldHouseLoanData = $(".isShowUnsoldHouseLoanData");
			
			isShowUnsoldHouseLoanData.show();
			if(!isOpenUnsoldHouseLoanInfoFunction || (userInfo.unitNo != '918' && (txCode == '338094' || txCode == '338095' || txCode == '338096'))){
				//isShowUnsoldHouseLoanData.hide();
				$("#finalCreditPeriodEndDate").attr("disabled", true).datepicker('destroy');;
			}

		 	var preFirstLoanUnsoldHouseQty = LMS140M01MAction.formData['preFirstLoanUnsoldHouseQty'];

			//初貸餘屋戶數
			if(preFirstLoanUnsoldHouseQty && preFirstLoanUnsoldHouseQty != '' && preFirstLoanUnsoldHouseQty != 0){
				$("#firstLoanUnsoldHouseQuantity").val(preFirstLoanUnsoldHouseQty);
				$("#firstLoanUnsoldHouseQuantity").attr("disabled", true);
			}
			
			if(userInfo.unitNo == '918' && (txCode == '338094' || txCode == '338095' || txCode == '338096')){
				$("#firstLoanUnsoldHouseQuantity").attr("disabled", false);
			}

			var isOldCase = preFirstLoanUnsoldHouseQty != undefined && preFirstLoanUnsoldHouseQty != '' && preFirstLoanUnsoldHouseQty != 0;
							//|| ($("#proPerty").val() != undefined && $("#proPerty").val() != '' && $("#proPerty").val().indexOf('1') != 0);
	
			var preMonthOfCreditPeriod = LMS140M01MAction.formData['preMonthOfCreditPeriod'];

			//本案授信期間年限
			if(isOldCase){
				$("#monthOfCreditPeriod").val(preMonthOfCreditPeriod).attr("disabled", true);
			}
			else{
				$("#monthOfCreditPeriod").attr("disabled", false);
			}
		 }
    };
    
    // 敘作條件異動比較表
    var openChgCf = {
        cntrNoMainId: null,
        isforQuery: true,
        _isLoad: false,
        chgCfGrid: null,
        ChgCfFormData: {},
        _initEvent: function(){
            $("#addChgCf").click(function(){
                openChgCf.openEditChgCfBox(null, "add", null);
            });
            $("#deleteChgCf").click(function(){
                openChgCf.deleteChgCf();
            });
            $("#printChgCf").click(function(){
//                openChgCf.printChgCf();
            });
        },
        _initGrid: function(){
            this.chgCfGrid = $("#chgCfGrid").iGrid({
            height: 100,
            handler: LMS140M01MAction.gridhandler,  //inits.ghandle,
            sortname: 'seqNum|createTime',
            sortorder: 'asc|asc',
            action: "queryL140s07aList",
            postData: {
                mainId: cntrNoMainId
            },
            loadComplete: function(){
                $('#chgCfGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.lmsl140m01m["L140S07A.item"],
                width: 80,
                name: 'item',
                formatter: 'click',
                onclick: openChgCf.openEditChgCfBox
            }, {
                colHeader: i18n.lmsl140m01m["L140S07A.befText"],
                name: 'befText'
            }, {
                colHeader: i18n.lmsl140m01m["L140S07A.aftText"],
                name: 'aftText'
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            },{
                colHeader: "seqNum",
                name: 'seqNum',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = openChgCf.chgCfGrid.getRowData(rowid);
                openChgCf.openEditChgCfBox(null, null, data);
            }
            });
        },
        _init: function(mainId, readonly){
            cntrNoMainId = mainId;
            isforQuery = readonly;
            if (!this._isLoad) {
                this._initGrid();
                this._initEvent();
                this._isLoad = true;
            }
            else {
                this._reloadGrid();
            }
        },
        openEditChgCfBox: function(cellvalue, options, data){
            var $form = $("#chgCfFormDetail");
            $form.reset();

            var buttons = {
                "saveData": function(){
                    if ($form.valid()) {
//                        ilog.debug("===========$('#chgCfFormDetail').oid============" + $("#chgCfFormDetail").find("#oid").val());
                        $.ajax({
                            handler: LMS140M01MAction.fhandle,//"lms1401m01formhandler",
                            action: "saveL140S07A",
                            data: $.extend($("#chgCfFormDetail").serializeData(), {
                                oid: $("#chgCfFormDetail").find("#oid").val()
                            }),
                            success: function(obj){
                                openChgCf.chgCfGrid.trigger("reloadGrid");
                                //saveSuccess=儲存成功
                                CommonAPI.confirmMessage(i18n.def["saveSuccess"], function(b){
                                    if (b) {
                                        $.thickbox.close();
                                    }
                                });
                            }
                        });
                    }
                },
                "close": function(){
                    $.thickbox.close();
                    openChgCf.chgCfGrid.trigger("reloadGrid");
                }
            }

            $.ajax({
                handler: LMS140M01MAction.fhandle,
                action: "addL140S07A",
                data: {
                    mainId: cntrNoMainId,
                    options: options
                },
                success: function(addObj){
                    var tOid = null;
                    if(data!=null){
                        tOid = data.oid;
                    } else {
                        tOid = addObj.oid;
                    }

                    $.ajax({
                        handler: LMS140M01MAction.fhandle,
                        action: "queryL140S07Adetail",
                        data: {
                            tOid: tOid,
                            mainId: cntrNoMainId
                        },
                        success: function(obj){
                            openChgCf.ChgCfFormData = obj;
                            $form.injectData(obj);

                            $("#chgCfDetailThickbox").thickbox({
                                title: "",
                                width: 830,
                                height: 340,
                                modal: true,
                                readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                                i18n: i18n.def,
                                buttons: buttons
                            });
                        }
                    });
                }
            });
        },
        deleteChgCf: function(){
            var girdId = this.chgCfGrid.getGridParam('selrow');
            var data = this.chgCfGrid.getRowData(girdId);
            if (!girdId) {
                return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            }
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(result){
                if (result) {
                    $.ajax({
                        handler: LMS140M01MAction.fhandle,
                        action: "deleteL140s07a",
                        data: {
                            tOid: data.oid
                        },
                        success: function(obj){
                            openChgCf.chgCfGrid.trigger("reloadGrid");
                        }
                    })
                }
            });
        },
        printChgCf: function(){
            var count = $("#chgCfGrid").jqGrid('getGridParam', 'records');
            if (count > 0) {
                $.capFileDownload({
                    handler: "lmsdownloadformhandler",
                    data: {
                        fileDownloadName: "LMSDoc07A.doc",
                        serviceName: "lms1201docservice",
                        fileName: "LMSDoc07A.htm",
                        mainId: cntrNoMainId,
                        oids: "",
                        docTempType: "LMSDoc07A",
                        type: "1"
                    }
                });
            } else {
                // L140S07A.error01=至少輸入一項
                CommonAPI.showMessage(i18n.lmsl140m01m["L140S07A.error01"]);
                return;
            }
        },
        _reloadGrid:function(){
            this.chgCfGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料
                postData: {
                    mainId: cntrNoMainId
                }
            }).trigger("reloadGrid");
        }
    }
    
    function changCityValue(tCity, tArea, tSite3, tSite4, defaultSelect, formDataArr, isClear){
        var value = tCity.val();
        
        var obj = QueryCityCode.getCode("2", value);
        
        tArea.setItems({
            item: obj,
            value: LMS140M01MAction.formData[formDataArr[0]] || ""
        });
        
        if (isClear) {
            tSite3.html(defaultSelect);
            tSite4.html(defaultSelect);
            
            for (var i = 0; i < formDataArr.length; i++) {
                LMS140M01MAction.formData[formDataArr[i]] = "";
            }
        }
    }
    
    function changAreaValue(tCity, tArea, tSite3, tSite4, defaultSelect, formDataArr, isClear){
        if (isClear) {
            for (var i = 1; i < formDataArr.length; i++) {
                LMS140M01MAction.formData[formDataArr[i]] = "";
            }
        }
        
        tSite3.setItems({
            item: LMS140M01MAction.getSITE3(tCity.find(":selected").text(), tArea.find(":selected").text(), "N"),
            value: util.addZeroBefore($.trim(LMS140M01MAction.formData[formDataArr[1]]).replace(",", "") ||
            "", 4)
        });
        
        tSite4.setItems({
            item: LMS140M01MAction.getSITE4(tCity.find(":selected").text(), tArea.find(":selected").text(), "N"),
            value: LMS140M01MAction.formData[formDataArr[2]] || ""
        });
        
    }
	
	function countTailZero(str){
	  //J-104-0XXX-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
      //var end = 4;
      var count = 0;
      for(var end=4;end>0;end--){
        if(str.charAt(end) == "0"){
          count = count + 1;
        }
      }
      return count;
    }

    function haveChild(base, compre){
	  //J-104-0XXX-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
      base = base +"";
      var maxLength = 5;
      var zeroCount = countTailZero(base);
      var maxBase = base.substring(0, maxLength-zeroCount) + "999999999".substring(0, zeroCount) ;
      if(maxBase >= compre){
        return true;
      } else {
        return false;
      }
    }
	
	function buildLoanTargetDscr(tloanTarget){
	    //J-104-0XXX-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
		var loanTargetDscr ="";
		
		if(!tloanTarget){
			return loanTargetDscr;
		}
		
		if(lms140_loanTarget == null){
			var nitem = API.loadCombos('lms140_loanTarget');
		    lms140_loanTarget = nitem.lms140_loanTarget;
		}
		
		if(lms140_loanTarget.hasOwnProperty(tloanTarget)){
			loanTargetDscr = $.trim(lms140_loanTarget[tloanTarget]);
	    }		
			
	    
		for(var end=5;end>0;end--){
			 var newLoanTarget =  ((tloanTarget).substring(0,end )+"00000").substring(0,5 ) ;
			 if(newLoanTarget != tloanTarget ){
			 	if(lms140_loanTarget.hasOwnProperty(newLoanTarget)){
					loanTargetDscr = $.trim(lms140_loanTarget[newLoanTarget]) + "／" + loanTargetDscr;
			    }	
			 }
			 		     
		}

		return loanTargetDscr;
	}
	
	function applyELF517(){
		
		var cntrNo =$("#cntrNo").val();
		var $LMS140M01MForm = $("#LMS140M01MForm");
	
		if(cntrNo =="" ){
			if (typeof(_M) != 'undefined') {
			    if(_M && _M.AllFormData && _M.AllFormData["04"]){
					//國內消金簽報書
					cntrNo = _M.AllFormData["04"]["cntrNo"]||'';
				}
			}	
		}
		
		if(cntrNo ==""){
			//L140M01M.err015=額度明細表之額度序號欄位不得空白，請先輸入並儲存後再執行本作業！
			return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.err015"]);
			//return false;
		}
		$.ajax({
            handler: LMS140M01MAction.fhandle,
            formId: LMS140M01MAction.formId,
            action: "applyELF517",
            data: {
                cntrNo: cntrNo 
            },
            success: function(obj){
				
				// 將form原本的值先存入backupObj中
                var backupObj = $LMS140M01MForm.serializeData();
                // 再new一個新的obj存放保留的欄位
                var memoObj = {};
                $.each(["cityId","areaId","sit3No","sit4No","locationCity", "locationCd", "site3No", "site4No","remainLoanLocationCity", "remainLoanLocationCd", "remainLoanSite3No", "remainLoanSite4No","bremainLoanLocationCity", "bremainLoanLocationCd", "bremainLoanSite3No", "bremainLoanSite4No"], function(idx, colName){
                    memoObj[colName] = backupObj[colName];
                });

                memoObj["bremainLoanYN"] = obj.bremainLoanYN;
                memoObj["bremainLoanClass"] = obj.bremainLoanClass  ;
				memoObj["bremainLoanLocationCity"] = obj.bremainLoanLocationCity  ;
				memoObj["bremainLoanLocationCd"] = obj.bremainLoanLocationCd;
				memoObj["bremainLoanSite3No"] = obj.bremainLoanSite3No;
				memoObj["bremainLoanSite4No"] = obj.bremainLoanSite4No;
				memoObj["preFirstLoanUnsoldHouseQty"] = obj.preFirstLoanUnsoldHouseQuantity;//初貸餘屋戶數
				memoObj["preCurrentUnsoldHouseQuantity"] = obj.preCurrentUnsoldHouseQuantity;//目前餘屋戶數
				memoObj["preYearsOfCreditPeriod"] = obj.preYearsOfCreditPeriod;//本案授信期間年限-年
				memoObj["preMonthOfCreditPeriod"] = obj.preMonthOfCreditPeriod;//本案授信期間年限-月
				memoObj["preFinalCreditPeriodEndDate"] = obj.preFinalCreditPeriodEndDate;//本案最終授信期限止日(續約後)

				var $form = $("#" + LMS140M01MAction.formId);	
				LMS140M01MAction.formData = memoObj;
				//LMS140M01MAction.init($form);
				LMS140M01MAction.isInject = true;
                $form.injectData(memoObj);
				LMS140M01MAction.isInject = false;
				$form.find("select").trigger("change", "init");
				
            }
        });
	}
	
	//J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	function applyClearLand(){
		var cntrNo =$("#cntrNo").val();
		var $LMS140M01MForm = $("#LMS140M01MForm");
	
		if(cntrNo =="" ){
			if (typeof(_M) != 'undefined') {
			    if(_M && _M.AllFormData && _M.AllFormData["04"]){
					//國內消金簽報書
					cntrNo = _M.AllFormData["04"]["cntrNo"]||'';
				}
			}	
		}
		
		if(cntrNo ==""){
			//L140M01M.err015=額度明細表之額度序號欄位不得空白，請先輸入並儲存後再執行本作業！
			return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.err015"]);
			//return false;
		}
		
		$.ajax({
            handler: LMS140M01MAction.fhandle,
            formId: LMS140M01MAction.formId,
            action: "applyClearLand",
            data: {
                cntrNo: cntrNo 
            },
            success: function(obj){
				var $form = $("#" + LMS140M01MAction.formId);	
				LMS140M01MAction.initClearLand($form);
				$form.injectData(obj);
				//$form.find("#L140M01M_clear_land_ctrl").find("input").triggerHandler("change");
				//$form.find("input[name='isClearLand'],input[name='isChgStDate'],input[name='isChgRate'],input[name='adoptFg']").triggerHandler('change');
				$form.find("input[name='isClearLand']").trigger('change');
            }
        });
	}
	
	
	//J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	function applyClearLandRoa(){
		var cntrNo =$("#cntrNo").val();
		var $LMS140M01MForm = $("#LMS140M01MForm");
		var mainMainId_CLS = "";
		if(cntrNo =="" ){
			if (typeof(_M) != 'undefined') {
			    if(_M && _M.AllFormData && _M.AllFormData["04"]){
					//國內消金簽報書
					cntrNo = _M.AllFormData["04"]["cntrNo"]||'';
				}
			}	
		}

		if (typeof(_M) != 'undefined') {
			mainMainId_CLS = _M.tabMainId ||'';    
		}
		
		if(cntrNo ==""){
			//L140M01M.err015=額度明細表之額度序號欄位不得空白，請先輸入並儲存後再執行本作業！
			return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.err015"]);
			//return false;
		}
		
		$.ajax({
            handler: LMS140M01MAction.fhandle,
            formId: LMS140M01MAction.formId,
            action: "applyClearLandRoa",
            data: {
                cntrNo: cntrNo,
                mainMainId : responseJSON.mainId,
				mainMainId_CLS : mainMainId_CLS,
                subMainId : $("#tabFormMainId").val(),
                custId: $("#custId").val(),
				dupNo: $("#dupNo").val(),
				custName: $("#custName").val(),
				callFrom : "L140M01A"
            },
            success: function(obj){

                var memoObj = {};
                memoObj["custRoa"] = obj.custRoa;
                memoObj["relRoa"] = obj.relRoa  ;
				memoObj["roaBgnDate"] = obj.roaBgnDate  ;
				memoObj["roaEndDate"] = obj.roaEndDate;

				var $form = $("#" + LMS140M01MAction.formId);	
				LMS140M01MAction.formData = memoObj;
				$form.injectData(memoObj);
				//$form.find("#L140M01M_clear_land_ctrl").find("input").triggerHandler("change");
				//$form.find("input[name='isClearLand'],input[name='isChgStDate'],input[name='isChgRate'],input[name='adoptFg']").triggerHandler('change');
				//$form.find("input[name='isClearLand']").trigger('change');
				
				checkIsLegal();
            }
        });
	}
	
	
	//J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	function applyIsLegal(){
		var cntrNo =$("#cntrNo").val();
		var $LMS140M01MForm = $("#LMS140M01MForm");
	
		if(cntrNo =="" ){
			if (typeof(_M) != 'undefined') {
			    if(_M && _M.AllFormData && _M.AllFormData["04"]){
					//國內消金簽報書
					cntrNo = _M.AllFormData["04"]["cntrNo"]||'';
				}
			}	
		}
		
		if(cntrNo ==""){
			//L140M01M.err015=額度明細表之額度序號欄位不得空白，請先輸入並儲存後再執行本作業！
			return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.err015"]);
			//return false;
		}
		
		checkIsLegal();
	}
	
	
	//J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	function checkIsLegal(){
		
		var isClearLand = $("input[name='isClearLand']:radio:checked" ).val();
		var isChgStDate = $("input[name='isChgStDate']:radio:checked" ).val();
		var isChgRate = $("input[name='isChgRate']:radio:checked" ).val();
		
		if(isClearLand == "Y"){
			if( isChgStDate== "Y" || isChgRate== "Y"){
				 //屬於要控管，且有變更預計動工日/調整利率，要檢核是否符合本行規定
			}else{
				return;
			}   
		}else{
			return;
		}
		
		var data = [];
        $("[name=adoptFg]:checked").each(function(v, k){
            data.push($(k).val());
        });
		
        $.ajax({
	        handler: LMS140M01MAction.fhandle,
	        action: "checkIsLegal",
	        async: false ,
	        data: {
	        	isClearLand :isClearLand,
	        	isChgStDate:isChgStDate,
	        	isChgRate:isChgRate,
	        	ctlType:$("#ctlType").val(),
	        	fstDate:$("#fstDate").val(),
	        	lstDate:$("#lstDate").val(),
	        	cstDate:$("#cstDate").val(),
	        	adoptFg:data.join("|"),
	        	rateAdd:$("#rateAdd").val(),
	        	custRoa:$("#custRoa").val(),
	        	relRoa:$("#relRoa").val()
	        },
	        success: function(obj){
	        	$("[name='isLegal'][value='" + obj.isLegal + "']:radio").attr("checked", "checked");
	        }
		});
	}
	
	//J-108-0097 購置高價住宅貸款檢核表
	var HighPricedHousingLoanCheckList = {
		
		init: function(custId){

			$("#checkListDiv").find('.ynxCheckItem').each(function(){
				$(this).setItems({
		            item: CommonAPI.loadCombos(["YNX"]).YNX,
		            format: '{key}'
		    	});
			});
		},
		
		open: function(formObject, isReadonly, l140m01aMainId){

			var custId = $("#custId").val();
			var dupNo = $("#dupNo").val();
			var custName = $("#custName").val();

			HighPricedHousingLoanCheckList.init(custId);
			
			var checkListData = HighPricedHousingLoanCheckList.query(custId, dupNo, l140m01aMainId, custName);
			$("#checkListForm").setData(checkListData);
			$('#checkListDiv').show();
			HighPricedHousingLoanCheckList.showCheckListVersion(checkListData.version, checkListData.caseDate)

			var btn = {};
			//儲存
			btn[i18n.def['saveData']] = function(){
			
				if ($("#checkListForm").valid()) {
					HighPricedHousingLoanCheckList.save(custId, dupNo, l140m01aMainId);
				}
			};
			//刪除
			btn[i18n.def["del"]] = function(){
			
				MegaApi.confirmMessage(i18n.def["confirmDelete"], function(){
					HighPricedHousingLoanCheckList.deleteCheckList(custId, dupNo, l140m01aMainId, formObject);
				});
			};
			//關閉
			btn[i18n.def["close"]] = function(){
				$('#checkListDiv').hide();
				$.thickbox.close();
				formObject.find("#grid1").trigger("reloadGrid");
			};
			
			$("#checkListDiv").thickbox({
				title: i18n.lmsl140m01m['L140M01X.highPricedHousingLoanCheckList'],
				width: 900,
				height: 650,
				modal: true,
				i18n: i18n.def,
				buttons: btn
			});
			
			HighPricedHousingLoanCheckList.processReadonly(isReadonly, btn);
	    },
		query: function(custId, dupNo, mainId, custName){

			var data;
			$.ajax({
		         handler: LMS140M01MAction.fhandle,
		         formId: LMS140M01MAction.formId,
		         action: "queryCheckListOfHighPricedHousingLoan",
		         data: {
					 custId: custId,
					 dupNo: dupNo,
					 l140m01aMainId: mainId,
					 custName: custName
		         },
				 async:false,
		         success: function(obj){
					data = obj;
		         }
	        });
			return data;
		},
		save: function(custId, dupNo, mainId){

			$.ajax({
	             handler: LMS140M01MAction.fhandle,
	             formId: LMS140M01MAction.formId,
	             action: "saveCheckListForHighPricedHousingLoan",
	             data: {
	                 checkListForm: JSON.stringify($("#checkListForm").serializeData()),
					 custId: custId,
					 dupNo: dupNo,
					 mainId: mainId
	             },
	             success: function(obj){

	             }
	        });
		},
		deleteCheckList: function(custId, dupNo, mainId, formObject){
			
			$.ajax({
	            handler: LMS140M01MAction.fhandle,
	            action: 'deleteCheckListForHighPricedHousingLoan',
				data: {
					 custId: custId,
					 dupNo: dupNo,
					 mainId: mainId
	            },
	            success: function(response){

					MegaApi.showPopMessage(i18n.def["confirmDeleteSuccess"], function(x){
		                if (x) {
							$('#checkListDiv').hide();
		                  	$.thickbox.close();
							formObject.find("#grid1").trigger("reloadGrid");
		                }
	            	});
				}
	       });
		},
		processReadonly: function(isReadonly, btn){

			var inputArray = $("#checkListTable :input");
			if(isReadonly){
				inputArray.each(function(){
					$(this).attr("readonly", true).attr("disabled", true);
				});
			}
			else{
				inputArray.each(function(){
					$(this).attr("readonly", false).attr("disabled", false);
				});
			}
			
			if (isReadonly) {
				delete btn[i18n.def['saveData']];
				delete btn[i18n.def["del"]];
			}
		},
		
		showCheckListVersion: function(version, caseDate){
			
			$('.20211217_ver4_words_20230616_ver5_20240614_ver6_words').hide();
			$('.20210319_ver2_20210924_ver3_words').hide();
			$('.old_version_words').hide();
			$('.20211217_ver4_words_20230616_ver5_20240614_ver6_words_ls_caseDate').hide();
			$('.20211217_ver4_words_20230616_ver5_20240614_ver6_words_eq_mt_caseDate').hide();
			$('.20240919_ver7_words').hide();
			
			if(version == '20240919_ver7'){
				
				$('.20240919_ver7_words').show();
				
			}else if(version == '20211217_ver4' || version == '20230616_ver5' || version == '20240614_ver6'){
				
				if(caseDate >= '2023-08-07'){
					$('.20211217_ver4_words_20230616_ver5_20240614_ver6_words_eq_mt_caseDate').show();
				}
				else{
				    $('.20211217_ver4_words_20230616_ver5_20240614_ver6_words_ls_caseDate').show();
				}
                $('.20211217_ver4_words_20230616_ver5_20240614_ver6_words').show();
			}
			else if(version == '20210319_ver2' || version == '20210924_ver3'){
				$('.20210319_ver2_20210924_ver3_words').show();
			}
			else{
				$('.old_version_words').show();
			}
		}
	}
	
	
	
	
	var CentralBankMortgageLoanManagement = {
		
		specificVersion: ['20211217_ver4', '20230616_ver5', '20240614_ver6', '20240919_ver7'],
		
		initEvent: function($formObject, version){
			
			$formObject.find("#appAmt").blur(function(){
				CentralBankMortgageLoanManagement.calculateCollateralTotalLoanRatio($formObject);
			});
			
			$formObject.find("#valueAMT").blur(function(){
				CentralBankMortgageLoanManagement.calculateCollateralTotalLoanRatio($formObject);
			});
			
			//是否續約/提前續約
			$formObject.find("[name=isRenew]").change(function(){
				CentralBankMortgageLoanManagement.switchIsRenew($(this).val());
			});
			
			//是否以新額度償還舊額度(含轉貸)
			$formObject.find("[name=isPayOldQuota]").change(function(){
				CentralBankMortgageLoanManagement.switchIsPayOldQuota($formObject.find("[name=cbcCase]:checked").val(), $(this).val());
			});
			
			//是否符合餘屋貸款排除控管事項
			$formObject.find("[name=isMatchUnsoldHouseItem]").change(function(){
				if ($(this).is(':checked')) {
					CentralBankMortgageLoanManagement.switchIsMatchUnsoldHouseItem($formObject.find("[name=cbcCase]:checked").val(), $(this).val());
				}
			});
			
			$formObject.find("#prodClass").change(function(){
				CentralBankMortgageLoanManagement.switchLatestStartDate($formObject, version);
			});

			var cbcCaseVal = LMS140M01MAction.formData.cbcCase;

			CentralBankMortgageLoanManagement.switchIsRenew(LMS140M01MAction.formData.isRenew);
			CentralBankMortgageLoanManagement.switchIsPayOldQuota(cbcCaseVal, LMS140M01MAction.formData.isPayOldQuota)
			CentralBankMortgageLoanManagement.switchIsMatchUnsoldHouseItem(cbcCaseVal, LMS140M01MAction.formData.isMatchUnsoldHouseItem);
		},
		
		initInfo: function(version){

			switch(version) {
			
				case '********_ver1':
				    var itema = API.loadCombos("L140M01M_cbcCase_********")["L140M01M_cbcCase_********"];
					$("#cbcCase").setItems({ size: "1", item: itema, clear: true});
					var itema = API.loadCombos("L140M01M_plusReason_********")["L140M01M_plusReason_********"];
					$("#plusReason").setItems({ size: "1", item: itema, clear: true});
					var itema = API.loadCombos("peopleMortgageDetail_********")["peopleMortgageDetail_********"];
					$("#peopleMortgageDetail").setItems({ size: "1", item: itema, clear: true});
					var itema = API.loadCombos("companyMortgageDetail_********")["companyMortgageDetail_********"];
					$("#companyMortgageDetail").setItems({ size: "1", item: itema, clear: true});
					break;
					
				case '20210319_ver2':
			    	var itema = API.loadCombos("L140M01M_cbcCase_20210319")["L140M01M_cbcCase_20210319"];
					$("#cbcCase").setItems({ size: "1", item: itema, clear: true});
					var itema = API.loadCombos("L140M01M_plusReason_20210319")["L140M01M_plusReason_20210319"];
					$("#plusReason").setItems({ size: "1", item: itema, clear: true});
					var itema = API.loadCombos("peopleMortgageDetail_20210319")["peopleMortgageDetail_20210319"];
					$("#peopleMortgageDetail").setItems({ size: "1", item: itema, clear: true});
			    	break;
					
				case '20210924_ver3':
			    	var itema = API.loadCombos("L140M01M_cbcCase_20210924")["L140M01M_cbcCase_20210924"];
					$("#cbcCase").setItems({ size: "1", item: itema, clear: true});
					var itema = API.loadCombos("L140M01M_plusReason_20210924")["L140M01M_plusReason_20210924"];
					$("#plusReason").setItems({ size: "1", item: itema, clear: true});
					var itema = API.loadCombos("peopleMortgageDetail_20210924")["peopleMortgageDetail_20210924"];
					$("#peopleMortgageDetail").setItems({ size: "1", item: itema, clear: true});
			    	break;
			    	
				case '20211217_ver4':
			    	var itema = API.loadCombos("L140M01M_cbcCase_20211217")["L140M01M_cbcCase_20211217"];
					$("#cbcCase").setItems({ size: "1", item: itema, clear: true});
					var itema = API.loadCombos("L140M01M_plusReason_20211217")["L140M01M_plusReason_20211217"];
					$("#plusReason").setItems({ size: "1", item: itema, clear: true});
					var itema = API.loadCombos("peopleMortgageDetail_20211217")["peopleMortgageDetail_20211217"];
					$("#peopleMortgageDetail").setItems({ size: "1", item: itema, clear: true});
			    	break;
					
				case '20230616_ver5':
			    	var itema = API.loadCombos("L140M01M_cbcCase_20230616")["L140M01M_cbcCase_20230616"];
					$("#cbcCase").setItems({ size: "1", item: itema, clear: true});
					var itema = API.loadCombos("L140M01M_plusReason_20230616")["L140M01M_plusReason_20230616"];
					$("#plusReason").setItems({ size: "1", item: itema, clear: true});
					var itema = API.loadCombos("peopleMortgageDetail_20230616")["peopleMortgageDetail_20230616"];
					$("#peopleMortgageDetail").setItems({ size: "1", item: itema, clear: true});
			    	break;
                case '20240614_ver6':
                    var itema = API.loadCombos("L140M01M_cbcCase_20240614")["L140M01M_cbcCase_20240614"];
                    $("#cbcCase").setItems({ size: "1", item: itema, clear: true});
                    var itema = API.loadCombos("L140M01M_plusReason_20240614")["L140M01M_plusReason_20240614"];
                    $("#plusReason").setItems({ size: "1", item: itema, clear: true});
                    var itema = API.loadCombos("peopleMortgageDetail_20240614")["peopleMortgageDetail_20240614"];
                    $("#peopleMortgageDetail").setItems({ size: "1", item: itema, clear: true});
                    break;
				case '20240919_ver7':
                    var itema = API.loadCombos("L140M01M_cbcCase_20240919")["L140M01M_cbcCase_20240919"];
                    $("#cbcCase").setItems({ size: "1", item: itema, clear: true});
                    var itema = API.loadCombos("L140M01M_plusReason_20240919")["L140M01M_plusReason_20240919"];
                    $("#plusReason").setItems({ size: "1", item: itema, clear: true});
                    var itema = API.loadCombos("peopleMortgageDetail_20240919")["peopleMortgageDetail_20240919"];
                    $("#peopleMortgageDetail").setItems({ size: "1", item: itema, clear: true});
                    break;
					
			  	default://old version
					var itema = API.loadCombos("L140M01M_cbcCase")["L140M01M_cbcCase"];
					$("#cbcCase").setItems({ size: "1", item: itema, clear: true, name:"cbcCase"});
					var itema = API.loadCombos("L140M01M_plusReason")["L140M01M_plusReason"];
					$("#plusReason").setItems({ size: "1", item: itema, clear: true});
			}
		},
		
		switchFillingItem: function($formObject, version, cbcCase){
			
			var $centralBankLimitRuleForRealEstateLoan = $formObject.find("#centralBankLimitRuleForRealEstateLoan");
			var $peopleMortgageLoanLimit = $formObject.find("#peopleMortgageLoanLimit");
			var $companyMortgageLoanLimit = $formObject.find("#companyMortgageLoanLimit");
			var $L140M01M_buyLand = $formObject.find(".L140M01M_buyLand");
			var $L140M01M_cbcCase6 = $formObject.find(".L140M01M_cbcCase6");
			var $commonYNTr = $formObject.find("#commonYNTr");
			var $shareCollYNTr = $formObject.find("#shareCollYNTr");
			var $houseTypeTr = $formObject.find("#houseTypeTr");
			var $cmsTypeTr = $formObject.find("#cmsTypeTr");
			var $houseYNTr = $formObject.find("#houseYNTr");
			var $notLatestVerDescTr = $formObject.find(".notLatestVerDescTr");
			var $isRenewTr = $formObject.find("#isRenewTr");
			var $isMatchUnsoldHouseItemTr = $formObject.find("#isMatchUnsoldHouseItemTr");
			var $isSaleCaseTr = $formObject.find("#isSaleCaseTr");
			var $cbControlLstDateTr = $formObject.find("#cbControlLstDateTr");
			var $timeValTr = $formObject.find("#timeValTr");

			$peopleMortgageLoanLimit.hide();
			$companyMortgageLoanLimit.hide();
			$centralBankLimitRuleForRealEstateLoan.hide();
			$isRenewTr.hide();
			$isMatchUnsoldHouseItemTr.hide();
			$isSaleCaseTr.hide();
			$cbControlLstDateTr.hide();
			
			if(version == '********_ver1'){
				
				switch (cbcCase) {
                    case "1":
						$centralBankLimitRuleForRealEstateLoan.show();
						$peopleMortgageLoanLimit.show();
						$isRenewTr.show();
                        break;
                    case "5":
						$centralBankLimitRuleForRealEstateLoan.show();
                        $companyMortgageLoanLimit.show();
						$isRenewTr.show();
                        break;
                    case "2":
                        $L140M01M_buyLand.show();
						$isRenewTr.show();
                        break;
					case "6":
						$L140M01M_cbcCase6.show();
						$commonYNTr.hide();
						$shareCollYNTr.hide();
						$notLatestVerDescTr.show();
						$isMatchUnsoldHouseItemTr.show();
						$isRenewTr.show();
						break;
            	}
			}
			
			if(version == '20210319_ver2'){
				
				switch (cbcCase) {
                    case "1":
						$centralBankLimitRuleForRealEstateLoan.show();
						$peopleMortgageLoanLimit.show();
						$isRenewTr.show();
                        break;
                    case "5":
						$isRenewTr.show();
                        break;
                    case "2":
                        $L140M01M_buyLand.show();
						$isRenewTr.show();
                        break;
					case "6":
						$L140M01M_cbcCase6.show();
						$commonYNTr.hide();
						$shareCollYNTr.hide();
						$notLatestVerDescTr.show();
						$isMatchUnsoldHouseItemTr.show();
						$isRenewTr.show();
						break;
					case "7":
						$L140M01M_buyLand.show();
						$houseTypeTr.hide();
						$cmsTypeTr.hide();
						$houseYNTr.hide();
						$isRenewTr.show();
						$isSaleCaseTr.show();
						break;
            	}
			}
			
			if(version == '20210924_ver3'){
				
				switch (cbcCase) {
                    case "1":
						$centralBankLimitRuleForRealEstateLoan.show();
						$peopleMortgageLoanLimit.show();
						$isRenewTr.show();
                        break;
                    case "5":
						$isRenewTr.show();
                        break;
                    case "2":
                        $L140M01M_buyLand.show();
						$isRenewTr.show();
                        break;
					case "6":
						$L140M01M_cbcCase6.show();
						$commonYNTr.hide();
						$shareCollYNTr.hide();
						$notLatestVerDescTr.show();
						$isMatchUnsoldHouseItemTr.show();
						$isRenewTr.show();
						break;
					case "7":
						$L140M01M_buyLand.show();
						$houseTypeTr.hide();
						$cmsTypeTr.hide();
						$houseYNTr.hide();
						$isRenewTr.show();
						$isSaleCaseTr.show();
						break;
            	}
			}
			
			if(version == '20211217_ver4'){
				
				switch (cbcCase) {
                    case "1":
						$centralBankLimitRuleForRealEstateLoan.show();
						$peopleMortgageLoanLimit.show();
						$isRenewTr.show();
                        break;
                    case "5":
						$isRenewTr.show();
                        break;
                    case "2":
                        $L140M01M_buyLand.show();
						$isRenewTr.show();
						$cbControlLstDateTr.show();
                        break;
					case "6":
						$L140M01M_cbcCase6.show();
						$commonYNTr.hide();
						$shareCollYNTr.hide();
						$notLatestVerDescTr.show();
						$isMatchUnsoldHouseItemTr.show();
						$isRenewTr.show();
						break;
					case "7":
						$L140M01M_buyLand.show();
						$houseTypeTr.hide();
						$cmsTypeTr.hide();
						$houseYNTr.hide();
						$isRenewTr.show();
						$isSaleCaseTr.show();
						break;
            	}
			}
			
			if(version == '20230616_ver5'){

				switch (cbcCase) {
                    case "1":
						$centralBankLimitRuleForRealEstateLoan.show();
						$peopleMortgageLoanLimit.show();
						$isRenewTr.show();
                        break;
                    case "5":
						$isRenewTr.show();
                        break;
                    case "2":
                        $L140M01M_buyLand.show();
						$isRenewTr.show();
						$cbControlLstDateTr.show();
                        break;
					case "6":
						$L140M01M_cbcCase6.show();
						$commonYNTr.hide();
						$shareCollYNTr.hide();
						$notLatestVerDescTr.show();
						$isMatchUnsoldHouseItemTr.show();
						$isRenewTr.show();
						break;
					case "7":
						$L140M01M_buyLand.show();
						$houseTypeTr.hide();
						$cmsTypeTr.hide();
						$houseYNTr.hide();
						$isRenewTr.show();
						$isSaleCaseTr.show();
						break;
            	}
			}
			if(version == '20240614_ver6'){

                switch (cbcCase) {
                    case "1":
                        $centralBankLimitRuleForRealEstateLoan.show();
                        $peopleMortgageLoanLimit.show();
                        $isRenewTr.show();
                        break;
                    case "5":
                        $isRenewTr.show();
                        break;
                    case "2":
                        $L140M01M_buyLand.show();
                        $isRenewTr.show();
                        $cbControlLstDateTr.show();
                        break;
                    case "6":
                        $L140M01M_cbcCase6.show();
                        $commonYNTr.hide();
                        $shareCollYNTr.hide();
                        $notLatestVerDescTr.show();
                        $isMatchUnsoldHouseItemTr.show();
                        $isRenewTr.show();
                        break;
                    case "7":
                        $L140M01M_buyLand.show();
                        $houseTypeTr.hide();
                        $cmsTypeTr.hide();
                        $houseYNTr.hide();
                        $isRenewTr.show();
                        $isSaleCaseTr.show();
                        break;
                }
            }
			
			if (version == '20240919_ver7') {
			
				switch (cbcCase) {
					case "1":
						$centralBankLimitRuleForRealEstateLoan.show();
						$peopleMortgageLoanLimit.show();
						$isRenewTr.show();
						break;
					case "5":
						$isRenewTr.show();
						break;
					case "2":
						$L140M01M_buyLand.show();
						$isRenewTr.show();
						$cbControlLstDateTr.show();
						break;
					case "6":
						$L140M01M_cbcCase6.show();
						$commonYNTr.hide();
						$shareCollYNTr.hide();
						$notLatestVerDescTr.show();
						$isMatchUnsoldHouseItemTr.show();
						$isRenewTr.show();
						break;
					case "7":
						$L140M01M_buyLand.show();
						$houseTypeTr.hide();
						$cmsTypeTr.hide();
						$houseYNTr.hide();
						$isRenewTr.show();
						$isSaleCaseTr.show();
						break;
				}
			}
		},
		
		getTitleVersion: function(version){
			
			var title = '';
			switch (version) {
                case "********_ver1":
					//新版(109-12-08不動產抵押貸款規定)
					title = i18n.lmsl140m01m["L140M01M.********.version1"]
                    break;
                case "20210319_ver2":
					//新版(110-03-19不動產抵押貸款規定)
					title = i18n.lmsl140m01m["L140M01M.20210319.version2"]
                    break;
				case "20210924_ver3":
					//新版(110-09-24不動產抵押貸款規定)
					title = i18n.lmsl140m01m["L140M01M.20210924.version3"]
                    break;
				case "20211217_ver4":
					//新版(110-12-17不動產抵押貸款規定)
					title = i18n.lmsl140m01m["L140M01M.20211217.version4"]
                    break;
				case "20230616_ver5":
					//新版(112-06-16不動產抵押貸款規定)
					title = i18n.lmsl140m01m["L140M01M.20230616.version5"]
                    break;
                case "20240614_ver6":
                    //新版(113-06-14不動產抵押貸款規定)
                    title = i18n.lmsl140m01m["L140M01M.20240614.version6"]
                    break;
				 case "20240919_ver7":
					//新版(113-09-19不動產抵押貸款規定)
					title = i18n.lmsl140m01m["L140M01M.20240919.version7"]
                    break;
                case "oldVersion":
					//舊版
                    title = i18n.lmsl140m01m["L140M01M.old.version"]
                    break;
        	}
			
			return title;
		},
		
		showIs3rdHighHouseOption: function($formObject){
			$formObject.find("#is3rdHighHouseTr").show();
		},
		
		hideIs3rdHighHouseOption: function($formObject){
			$formObject.find("#is3rdHighHouseTr").hide();
			$formObject.find("[name=is3rdHignHouse]").attr("checked", false);
		},
		
		switchIs3rdHighHouseOption: function(version, peopleMortgageDetail, $formObject){
			
			if(CentralBankMortgageLoanManagement.isContainSpecificVersion(version) && peopleMortgageDetail == 'A'){
				CentralBankMortgageLoanManagement.showIs3rdHighHouseOption($formObject);
			}
			else{
				CentralBankMortgageLoanManagement.hideIs3rdHighHouseOption($formObject);
			}
		},
		
		switchIsRenew: function(value){
			
			if(value == 'N'){
				$("#isPayOldQuotaTr").show();
			}
			else{
				$("#isPayOldQuotaTr").hide();
				$("[name=isPayOldQuota]").attr("checked", false);
				$("[name=isPayOldQuota]").trigger("change");
			}
		},
		
		switchIsPayOldQuota: function(cbcCaseVal, value){

			$("#oldQuotaTr").hide();
			$("#oldQuota").val("");
			$("#payOldAmtTr").hide();
			$("#payOldAmt").val("");
			$("#payOldAmtItemTr").hide();
			$("[name=payOldAmtItem]").attr("checked", false);

			if(value == 'Y'){
				//購地貸款
				if(cbcCaseVal == '2'){
					// $("#oldQuotaTr").show();// #J-111-0534：不顯示[原(舊)額度]欄位
					$("#payOldAmtTr").show();
				}
				
				//餘屋貸款
				if(cbcCaseVal == '6'){
					$("#payOldAmtItemTr").show();
				}
			}
		},
		
		switchIsMatchUnsoldHouseItem: function(cbcCaseVal, value){

			if(cbcCaseVal == '6'){
				
				if (value == "Y") {
					$("[name=isMatchUnsoldHouseItem]").attr("checked", false);
					$("#isRenewTr").hide();
					$("[name=isRenew]").attr("checked", false);
					$("#isPayOldQuotaTr").hide();
					$("[name=isPayOldQuota]").attr("checked", false);
	                API.showMessage(i18n.lmsl140m01m['L140M01M.error.message13']);
	            }
				
				if(value == 'N'){
					$("#isRenewTr").show();
				}
			}
		},
		
		switchDuringCbcCaseChange: function($formObject, cbcCaseVal){
			CentralBankMortgageLoanManagement.switchIsRenew($formObject.find("[name=isRenew]:checked").val());
			CentralBankMortgageLoanManagement.switchIsPayOldQuota(cbcCaseVal, $formObject.find("[name=isPayOldQuota]:checked").val())
			CentralBankMortgageLoanManagement.switchIsMatchUnsoldHouseItem(cbcCaseVal, $formObject.find("[name=isMatchUnsoldHouseItem]:checked").val());
		},
		
		calculateCollateralTotalLoanRatio: function($formObject){

			if($formObject.find("[name=cbcCase]:checked").val() != '4'){
				
				var appAmt = $formObject.find("#appAmt").val();  //購價【A】
				var valueAMT = $formObject.find("#valueAMT").val();//本行鑑價(估值)【B】
				var approvedLoanAmt = $formObject.find("#approvedLoanAmt").val();
				
				appAmt = appAmt == null || appAmt == '' ? 0 : RemoveStringComma(appAmt);
				valueAMT = valueAMT == null || valueAMT == '' ? 0 : RemoveStringComma(valueAMT);
				approvedLoanAmt = approvedLoanAmt == null || approvedLoanAmt == '' ? 0 : RemoveStringComma(approvedLoanAmt);
	
				var monther;
				if(appAmt != 0 && valueAMT != 0){
					monther = Math.min(appAmt, valueAMT);
				}
				else{
					monther = appAmt == 0 ? valueAMT : appAmt;
				}
	
				var ratio = monther == 0 || approvedLoanAmt == 0 ? 0 : (approvedLoanAmt/monther).toFixed(4);
				$formObject.find("#payPercent").val((ratio*100).toFixed(2))
			}
		},
		switchLatestStartDate: function($formObject, version){
			
			var landBuildYN = $formObject.find("[name=landBuildYN]:checked").val();//Y
			var prodClass = $formObject.find("#prodClass").val();//33
			var cbcCase = $formObject.find("[name=cbcCase]:checked").val();//2
			var purposeType = $formObject.find("[name=purposeType]:checked").val();//4

			if(cbcCase == '2' && purposeType == '4'){
				$("#cbControlLstDateTr").hide();
				$("#cbControlLstDate").val("");
			}
			else{
				CentralBankMortgageLoanManagement.switchFillingItem($formObject, version, cbcCase);
			}
		},
		
		isContainSpecificVersion: function(version){

			for(var n in CentralBankMortgageLoanManagement.specificVersion){
				
                var v = CentralBankMortgageLoanManagement.specificVersion[n];
                if(v == version){
					return true;
				}
            }
			
			return false;
		},
		
		switchPurchaseAndCurrentAmount: function(plusReason){
			
			$("#appAmtSpan").show();
			$("#timeValAnnuityPayment").hide();
			if(plusReason == '8'){
				$("#appAmtSpan").hide();
				$("#timeValAnnuityPayment").show();
			}
		}
	}

	function isUseRealEstateLoanRuleNewVersion(cbRuleVersion){
		
		var txCode = responseJSON.txCode;
		if (cbRuleVersion != undefined && (cbRuleVersion == 'new')) {
			return true;
		}
		
		return false;
	}
	
	/**
	 * 移除字符串中的逗號
	 */
	function RemoveStringComma(number){
	    if (number == undefined || number == null || number == "") {
	        return;
	    }
	    else {
	        number = number.toString();
	        if (number != undefined) {
	            var pos = number.indexOf(",");
	            while (pos != -1) {
	                number = number.substring(0, pos) + number.substring(pos + 1, number.length);
	                pos = number.indexOf(",");
	            }
	            return number;
	        }
	    }
	}
	
	var ClearLandLoanOfNotConstructed = {
		
		setInitialValue: function($formObject, objValue){
			$("#showClearLand").injectData(objValue);
			$formObject.find("[name=isChgRate]").trigger("change");
		}
	}
}
