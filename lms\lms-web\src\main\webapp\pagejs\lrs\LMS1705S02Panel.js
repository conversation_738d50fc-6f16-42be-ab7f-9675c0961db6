//alert(JSON.stringify(responseJSON)); 
$(function(){
    var L170M01bGrid = $("#L170M01bGrid").iGrid({
        rownumbers: true,
		multiselect: true,
		hideMultiselect:false,
		caption: "&nbsp;",
		hiddengrid : false,
        handler: 'lms1705gridhandler',
        height: 350, // 設定高度
        // sortname : 'oid', //預設排序
        action: "queryL170m01b",
        postData: {
            mainId: responseJSON.mainId,
            custId: responseJSON.custId,
            dupNo: responseJSON.dupNo
        },
        // multiselect : true, //是否開啟多選
        colModel: [{//匯入時間
            name: 'lnDataDate',
            hidden: true
            // 是否隱藏
        },{
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
        }, {
            colHeader: i18n.lms1705s02["L170M01b.cntrNo"],// "額度序號",
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'cntrNo' // col.id
        }, {
            colHeader: i18n.lms1705s02["L170M01b.loanNo"],// "帳號",
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'loanNo' // col.id
        }, {
            colHeader: i18n.lms1705s02["L170M01b.subject"], // 授信科目
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'loanTP' // col.id
        }, {
            colHeader: i18n.lms1705s02["L170M01b.quotaCurr"], //  額度幣別
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'quotaCurr' // col.id
        }, {
            colHeader: i18n.lms1705s02["L170M01b.quotaAmt"], // 額度(金額)
            align: "right",
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 2//小數點到第幾位
            },
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'quotaAmt' // col.id
        }, {
            colHeader: i18n.lms1705s02["L170M01b.balCurr"], // 餘額幣別
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'balCurr' // col.id
        }, {
            colHeader: i18n.lms1705s02["L170M01b.balAmt"], // 前日結欠餘額(金額)
            align: "right",
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                decimalPlaces: 2//小數點到第幾位
            },
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'balAmt' // col.id
        }, {
            colHeader: i18n.lms1705s02["L170M01b.newCase"], // 新貸/舊案
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'newCase' // col.id
        }, {
            colHeader: i18n.lms1705s02["L170M01b.lnDataDateResult"], // 手動新增
            align: "center",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            name: 'creator' // col.id
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    /*------------------------------------------ 新增----------------------------------------*/
    $("#_lms1705s02ADD").click(function(){
    	thickboxLock(false);
        $("#L170M01BForm").reset();
//        $("#guaranteeName").val('');
//        $("#insMemo").val('');
//        $("#majorMemo").val('');
        //新增
        thickboxShow(responseJSON.mainId,'',responseJSON.txCode,"N");
    });
    
    /*----------------------------------------- 刪除----------------------------------------*/
    $("#_lms1705s02Delete").click(function(){
        // confirmDelete=是否確定刪除?
    	var content = "";
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
            	var id = L170M01bGrid.getGridParam('selarrrow');
                for (var i = 0; i < id.length; i++) {
                    if (id[i] != "") {
                        var datas = L170M01bGrid.getRowData(id[i]);
                        if(content.length == 0){
                        	content = datas.oid;
                        }else{
                        	content = content + "^" + datas.oid;
                        }
                        
                    }
                }
                if (content.length == 0) {
                    CommonAPI.showMessage(i18n.def['grid.selrow']);
                } else {
                	$.ajax({
                        handler: "lms1705m01formhandler",
                        type: "POST",
                        action: "deleteChkCredit",
                        data: {
                            mainOid: responseJSON.oid,
                            oids: content,
                            txCode: responseJSON.txCode
                        },
					}).done(function(responseData){
						/*----重新載入grid內容-*/
	                    $("#totQuota").val(responseData.totQuota);
	                    $("#totBal").val(responseData.totBal);
	                    $("#L170M01bGrid").trigger("reloadGrid");//更新Grid內容	
					});
                }
            }
        })
    })
    
    /*------------------------------------------ 產生所有授信資料----------------------------------------*/
    $("#_lms1705s02ButtonADD").click(function(){
        //產生所有授信資料
    	$.ajax({
            handler: "lms1705m01formhandler",
            type: "POST",
            action: "addCredit",
            data: {
                mainOid: responseJSON.oid,
                mainId: responseJSON.mainId,
                dupNo: responseJSON.dupNo,
                txCode: responseJSON.txCode
            },
		}).done(function(responseData){
			/*-- 額度合計 , 前日結欠餘額合計 顯示 --*/
			$("#totQuota").val(responseData.totQuota);
			$("#totBal").val(responseData.totBal);
			$("#lnDataDate").val(responseData.lnDataDate);
			/*---重新載入grid內容-*/
			$("#L170M01bGrid").trigger("reloadGrid");//更新Grid內容	
		});
    });
    /*----------------------------------------- 刪除所有授信資料----------------------------------------*/
    $("#_lms1705s02ButtonDEL").click(function(){
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "lms1705m01formhandler",
                    type: "POST",
                    action: "deleteCredit",
                    data: {
                        mainOid: responseJSON.oid,
                        mainId: responseJSON.mainId,
                        dupNo: responseJSON.dupNo,
                        txCode: responseJSON.txCode
                    },
				}).done(function(responseData){
					/*----重新載入grid內容-*/
					$("#totQuota").val(responseData.totQuota);
					$("#totBal").val(responseData.totBal);
					$("#lnDataDate").val(responseData.lnDataDate);
					$("#L170M01bGrid").trigger("reloadGrid");//更新Grid內容	
				});
            }
        })
    })



    /*===============================================================================================*/
    function openDoc(cellvalue, options, rowObject){
        $("#L170M01BForm").reset();
        $.ajax({
            handler: "lms1705m01formhandler",
            type: "POST",
            action: "queryCredit",
            data: $.extend($("#L170M01BForm").serializeData(), {
                mainOid: rowObject.oid,
                txCode: responseJSON.txCode
            
            }),
		}).done(function(responseData){
            //alert(JSON.stringify(responseData));
			//$("#guaranteeName").val('');
			//$("#insMemo").val('');
			//$("#majorMemo").val('');
            $("#L170M01BForm").setData(responseData);
            if(responseData.lnDataDateResult == 'Y'){
            	thickboxLock(true);
            }else{
            	thickboxLock(false);
            }
            thickboxShow(rowObject.mainId,rowObject.oid,responseJSON.txCode,responseData.lnDataDateResult);
		});
    }
    
    function thickboxShow(obj_mainId,obj_oid,obj_txCode,lnDataDateResult){
    	var test2 = $("#lms1705s02").thickbox({
            title: i18n.lms1705s02['L170M01b.creditData'],
            width: 870,
            height: 550,
            modal: false,
            i18n: i18n.def,
            buttons: {
                "saveData": function(){
                	if($("#L170M01BForm").valid()){
                		var currResult = true;
                		if($("#loanAmt").val() != ''){
                			if($("#loanCurr").val() == ''){
                				CommonAPI.showMessage(i18n.lms1705s02["L170M01b.warmMsg03"]);
                				currResult = false;
                			}
                		}
                		if($("#estAmt").val() != ''){
                			if($("estCurr").val() == ''){
                				CommonAPI.showMessage(i18n.lms1705s02["L170M01b.warmMsg02"]);
                				currResult = false;
                			}
                		}
                		if($("#balAmt").val() != ''){
                			if($("#balCurr").val() == ''){
                				CommonAPI.showMessage(i18n.lms1705s02["L170M01b.warmMsg01"]);
                				currResult = false;
                			}
                		}
                		if($("#fromDate").val().replace(/\-/g,"") > $("#endDate").val().replace(/\-/g,"")){
                			CommonAPI.showMessage(i18n.lms1705s02["L170M01b.errMsg01"]);
                			currResult = false;
                		}
                		if(currResult){
                			$.ajax({
                                handler: "lms1705m01formhandler",
                                type: "POST",
                                action: "saveL170m01b",
                                
                                data: $.extend($("#L170M01BForm").serializeData(), {
                                    mainId: obj_mainId,
                                    mainOid: obj_oid,
                                    txCode: obj_txCode,
                                    lnDataDateResult : lnDataDateResult,
                                }),
							}).done(function(responseData){
								$("#totQuota").val(responseData.totQuota);
								$("#totBal").val(responseData.totBal);
								obj_oid = responseData.temp_oid;
								//更新Grid內容
								$("#L170M01bGrid").trigger("reloadGrid");
							});//ajax
                		}
                	}
                },
                "reQuery": function(){
                	if($("#cntrNo").val() == ''){
                        return CommonAPI.showMessage(i18n.lms1705s02['L170M01b.warmMsg04']);
                	}
                	if($("#L170M01BForm").valid()){
                		$.ajax({
                            handler: "lms1705m01formhandler",
                            type: "POST",
                            action: "reQueryL170m01bCMSData",
                            data: $.extend($("#L170M01BForm").serializeData(), {
                                mainId: obj_mainId,
                                oid: obj_oid,
                                txCode: obj_txCode,
                                cntrNo : $("#cntrNo").val()
                            }),
						}).done(function(responseData){
							$("#loanCurr").val(responseData.loanCurr);
							$("#estCurr").val(responseData.estCurr);
							$("#loanAmt").val(responseData.loanAmt);
							$("#estAmt").val(responseData.estAmt);
							$("#guaranteeName").val(responseData.guaranteeName);
							$("#insMemo").val(responseData.insMemo);
						});//ajax	
                	}
                },
                "close": function(){
                    $.thickbox.close();
                }//關閉
            }//bottons
        });//thickbox
    }
    
    function thickboxLock(result){
    	$("#loanTP").readOnly(result);
    	$("#cntrNo").readOnly(result);
    	$("#loanNo").readOnly(result);
    	$("#quotaCurr").readOnly(result);
    	$("#quotaAmt").readOnly(result);
    	// J-111-0326 海外覆審作業系統改良第一階段：
    	// 5. 動用期限或授信期間自系統引進可能有誤，須開放可人工修正。
    	//$("#fromDate").readOnly(result);
    	//$("#endDate").readOnly(result);
    }
});


