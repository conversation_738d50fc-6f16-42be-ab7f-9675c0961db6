package com.mega.eloan.lms.fms.handler.grid;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.service.LMS7205Service;
import com.mega.eloan.lms.model.L720M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 使用者自定表格範本檔(GridHandler)
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR> @version <ul>
 *          <li>2011/9/29,<PERSON>,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7205gridhandler")
public class LMS7205GridHandler extends AbstractGridHandler {

	@Resource
	LMS7205Service service;
	@Resource
	UserInfoService userSrv;
	@Resource
	LMSService lmsService;

	//
	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL720m01a(ISearch pageSetting,
			PageParameters params) throws CapException {

		// 建立主要Search 條件
		// pageSetting.addOrderBy("patternNM");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service.findPage(L720M01A.class,
				pageSetting);

		List<L720M01A> list = (List<L720M01A>) page.getContent();
		for (L720M01A model : list) {
			StringBuilder fullUp = new StringBuilder();
			fullUp.append(getPerName(model.getUpdater())).append(" (")
					.append(TWNDate.toFullTW(model.getUpdateTime()))
					.append(")");
			model.setUpdater(fullUp.toString());
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	private String getPerName(String id) {
		return (!Util.isEmpty(userSrv.getUserName(id)) ? userSrv
				.getUserName(id) : id);
	}
}
