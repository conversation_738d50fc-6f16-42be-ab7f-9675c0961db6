package com.mega.eloan.lms.lms.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.ScoreAU;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ScoreServiceAU;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.C120S01BDao;
import com.mega.eloan.lms.dao.C120S01CDao;
import com.mega.eloan.lms.dao.C120S01DDao;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.dao.C121M01ADao;
import com.mega.eloan.lms.dao.C121M01CDao;
import com.mega.eloan.lms.dao.C121M01EDao;
import com.mega.eloan.lms.dao.C121M01GDao;
import com.mega.eloan.lms.dao.C121S01ADao;
import com.mega.eloan.lms.dao.L120S01MDao;
import com.mega.eloan.lms.dao.L120S01NDao;
import com.mega.eloan.lms.dao.L120S01ODao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.pages.LMS1025M01Page;
import com.mega.eloan.lms.lms.service.LMS1025Service;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01C;
import com.mega.eloan.lms.model.C121M01E;
import com.mega.eloan.lms.model.C121M01G;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Workbook;
import jxl.WorkbookSettings;
import jxl.format.Alignment;
import jxl.format.Colour;
import jxl.write.Formula;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;


@Service("LMS1025Service")
public class LMS1025ServiceImpl extends AbstractCapService implements
		LMS1025Service {

	private static Logger logger = LoggerFactory
			.getLogger(LMS1025ServiceImpl.class);
	@Resource
	CLSService clsService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocFileService docFileService;

	@Resource
	C120M01ADao c120m01aDao;
	
	@Resource
	C120S01ADao c120s01aDao;
	
	@Resource
	C120S01BDao c120s01bDao;

	@Resource
	C120S01CDao c120s01cDao;

	@Resource
	C120S01DDao c120s01dDao;
	
	@Resource
	C120S01EDao c120s01eDao;
	
	@Resource
	C121M01ADao c121m01aDao;
	
	@Resource
	C121S01ADao c121s01aDao;
	
	@Resource
	C121M01CDao c121m01cDao;
	
	@Resource
	C121M01GDao c121m01gDao;
	
	@Resource
	ScoreServiceAU scoreServiceAU;
	
	@Resource
	L120S01MDao l120s01mDao;
	
	@Resource
	L120S01NDao l120s01nDao;

	@Resource
	L120S01ODao l120s01oDao;

	@Resource
	DwdbBASEService dwdbBASEService;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	C121M01EDao c121m01eDao;
	
	@Override
	public String checkIncompleteMsg(C121M01A meta, List<String> adjReasonCnt, LinkedHashMap<String, String> adjReasonCfmMap){
		C121S01A c121s01a = clsService.findC121S01A(meta);	
		Properties prop_lms1025m01 = MessageBundleScriptCreator.getComponentResource(LMS1025M01Page.class);
		Properties prop_abstractOverSeaCLS = MessageBundleScriptCreator.getComponentResource(AbstractOverSeaCLSPage.class);
		List<C120M01A> c120m01a_list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
		List<C120M01A> c120m01a_shouldRating_list = clsService.filter_shouldRating(c120m01a_list);
				
		if(true){			
			List<String> panel1 = new ArrayList<String>();//文件資訊
			if(true){
				List<String> overRange = new ArrayList<String>();
				if (true) {
					if(meta.getLnYear()!=null && (meta.getLnYear()<0)){
						overRange.add(prop_lms1025m01.getProperty("tab01.lnPeriod")+"-"+prop_lms1025m01.getProperty("tab01.lnYear"));
					}
					if(meta.getLnMonth()!=null && (meta.getLnMonth()<0||meta.getLnMonth()>=12)){
						overRange.add(prop_lms1025m01.getProperty("tab01.lnPeriod")+"-"+prop_lms1025m01.getProperty("tab01.lnMonth"));
					}
					OverSeaUtil.add_empty_to_list(panel1, meta.getLnYear(), prop_lms1025m01.getProperty("tab01.lnPeriod")+"-"+prop_lms1025m01.getProperty("tab01.lnYear"));
					OverSeaUtil.add_empty_to_list(panel1, meta.getLnMonth(), prop_lms1025m01.getProperty("tab01.lnPeriod")+"-"+prop_lms1025m01.getProperty("tab01.lnMonth"));
					
					if(Util.equals("2", meta.getRepaymentSchFmt())){
						OverSeaUtil.add_empty_to_list(panel1, meta.getRepaymentSchDays(), prop_lms1025m01.getProperty("tab01.repaymentSch"));
					}
					if(OverSeaUtil.valid_RepaymentSchDays_LoanTenor(meta)==false){
						panel1.add(prop_lms1025m01.getProperty("tab01.repaymentSchDays.invalid"));
					}
				}
				
				for(String colDesc :overRange){
					HashMap<String, String> msg = new HashMap<String, String>();
					msg.put("colName", colDesc);
					panel1.add(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.輸入位數超過, msg));
				}									
			}
			List<String> panel2 = new ArrayList<String>();//本案關係人基本資料
			if(true){
				int hasKeyMan = 0;
				for(C120M01A c120m01a: c120m01a_list){
					if(Util.equals("Y", c120m01a.getKeyMan())){
						hasKeyMan++;
					}
				}
				if(hasKeyMan==1){
					for(C120M01A c120m01a: c120m01a_list){
						if(Util.notEquals("Y", c120m01a.getO_chkYN())){
							panel2.add(MessageFormat.format(prop_lms1025m01.getProperty("msg.004")
									, c120m01a.getCustId()+"-"+c120m01a.getDupNo()) );
							continue;
						}
						
						if( Util.equals("Y", c120m01a.getKeyMan())){
							
						}else{
							//非主借人，要有 與主要借款人關係
							if(Util.isEmpty(Util.trim(c120m01a.getO_custRlt()))){
								panel2.add(MessageFormat.format(prop_lms1025m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_lms1025m01.getProperty("l120s01a.custrlt")) );
								continue;
							}
							
							if(Util.isEmpty(Util.trim(c120m01a.getCustPos()))){
								panel2.add(MessageFormat.format(prop_lms1025m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_lms1025m01.getProperty("l120s01a.custpos")) );
								continue;
							}
						}
					}	
				}else{
					panel2.add(MessageFormat.format(prop_lms1025m01.getProperty("msg.004"), prop_lms1025m01.getProperty("C121M01A.custId")) );
				}				
			}
			
			List<String> panel3 = new ArrayList<String>();//擔保品資料
			if(true){

				if(c121s01a==null || Util.isEmpty(Util.trim(c121s01a.getCmsType()))){
					OverSeaUtil.add_empty_to_list(panel3, "", prop_lms1025m01, "C121S01A.cmsType");
				}else{
					if(Util.equals("1", c121s01a.getCmsType())){
						
						OverSeaUtil.add_empty_to_list(panel3, c121s01a.getHouseAge(), prop_lms1025m01, "C121S01A.houseAge");
						OverSeaUtil.add_empty_to_list(panel3, c121s01a.getHouseArea(), prop_lms1025m01, "C121S01A.houseArea");						
					}
				}	
			}
			
			List<String> panel5 = new ArrayList<String>();//主觀評等更新
			if(true){
				//3.0模型不給調整，且僅澳洲有Veda Report
				LinkedHashMap<String, String> adjReasonMap = new LinkedHashMap<String, String>();
				LinkedHashMap<String, String> adjReasonErrMap = new LinkedHashMap<String, String>();
								
				for(C120M01A c120m01a: c120m01a_shouldRating_list){
					//非澳洲不檢核以下Veda Report部分
					C121M01C c121m01c = clsService.findC121M01C_byC120M01A(c120m01a);
					if(LMSUtil.get_AU_BRNO_SET().contains(c120m01a.getOwnBrId())){ //分行屬於澳洲地區
						if(c121m01c==null || Util.isEmpty(Util.trim(c121m01c.getNoAdj()))){
							panel5.add(MessageFormat.format(prop_lms1025m01.getProperty("msg.004")
									, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_lms1025m01.getProperty("tab05.desc03")) );
						}
						if(c121m01c==null){
							continue;
						}
						if(Util.equals(c121m01c.getNoAdj(),"2")){
							if(Util.isEmpty(Util.trim(c121m01c.getAdjustStatus()))){
								panel5.add(MessageFormat.format(prop_lms1025m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_abstractOverSeaCLS.getProperty("message.adjustStatus")) );
							}	
							if(Util.equals(c121m01c.getSRating(), c121m01c.getFRating())){
								panel5.add(MessageFormat.format(prop_lms1025m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_abstractOverSeaCLS.getProperty("message.adjustLevel")) );
							}
							if(Util.equals(c121m01c.getAdjustStatus(),"1") && Util.isEmpty(Util.trim(c121m01c.getAdjustFlag()))){
								panel5.add(MessageFormat.format(prop_lms1025m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_abstractOverSeaCLS.getProperty("message.adjustFlag")) );
							}
							if(Util.isEmpty(Util.trim(c121m01c.getAdjustReason()))){
								panel5.add(MessageFormat.format(prop_lms1025m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_abstractOverSeaCLS.getProperty("message.adjustReason")) );
							}
						}
					}
					
					//(AU)切換 tab 頁面，可能使不完整的 data 存進DB
					String adjustReason = Util.trim(c121m01c.getAdjustReason());
					if(Util.isNotEmpty(adjustReason)){
						adjReasonMap.put(c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+c120m01a.getCustName()
								, adjustReason);						
					}
				}
				
				if(true){
					adjReasonCnt.add(String.valueOf(adjReasonMap.size()));
					
					clsService.validate_adjustReason(adjReasonMap, adjReasonErrMap, adjReasonCfmMap);
					
					if(adjReasonErrMap.size()>0){
						for(String idDupName : adjReasonErrMap.keySet()){
							panel5.add(idDupName+" "+adjReasonErrMap.get(idDupName));
						}
					}					
				}
			}			
			
			List<String> panel6 = new ArrayList<String>();//評等等級
			if(true){
				for(C120M01A c120m01a: c120m01a_shouldRating_list){
					C121M01C c121m01c = clsService.findC121M01C_byC120M01A(c120m01a);
					if(c121m01c==null||c121m01c.getFRating()==null||CrsUtil.isNull_or_ZeroDate(c121m01c.getRatingDate())){
						panel6.add(MessageFormat.format(prop_lms1025m01.getProperty("msg.001"), c120m01a.getCustId()+"-"+c120m01a.getDupNo()) );	
					}
				}
			}
			
			List<String> r = new ArrayList<String>();
			add_errMsg(r, prop_lms1025m01.getProperty("tab.01"), panel1);
			add_errMsg(r, prop_lms1025m01.getProperty("tab.02"), panel2);
			add_errMsg(r, prop_lms1025m01.getProperty("tab.03"), panel3);
			add_errMsg(r, prop_lms1025m01.getProperty("tab.05"), panel5);
			add_errMsg(r, prop_lms1025m01.getProperty("tab.06"), panel6);
			
			String msg = StringUtils.join(r, "<br>");
			if(Util.isNotEmpty(msg)){
				logger.trace("checkIncompleteMsg:"+msg);
				return msg;
			}	
		}
		//已在 should_calc_C121_score(...) 裡，有檢核 varVer 不一致的狀況
		return "";
	}
	
	private void add_errMsg(List<String> errMsg, String panelTitle, List<String> panelErrMsg){
		if(panelErrMsg.size()>0){
			errMsg.add("【"+panelTitle+"】");
			for(String m : panelErrMsg){
				errMsg.add("&nbsp;&nbsp;&nbsp;&nbsp;"+m);	
			}
		}
	}
	

	@Override	
	public void delRatingDocCust(C121M01A c121m01a, C120M01A c120m01a){
		C121M01C c121m01c = clsService.findC121M01C_byC120M01A(c120m01a);
		if(c121m01c!=null){
			c121m01cDao.delete(c121m01c);	
		}
		//澳洲模型3.0新增C121M01G，有就一起刪
		C121M01G c121m01g = clsService.findC121M01G_byC120M01A(c120m01a);
		if(c121m01g!=null){
			c121m01gDao.delete(c121m01g);	
		}
		
		//-------------
		clsService.delC120Relate(c120m01a);
	}
	
	@Override
	public void calc_C121_score(C121M01A meta)
	throws CapException{
		List<C120M01A> c120m01a_list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if(CollectionUtils.isEmpty(c120m01a_list)){
			return;
		}
		List<C121M01C> c121m01c_list = new ArrayList<C121M01C>();
		
		if(true){
			BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());
			List<Map<String, Object>> dw_fxrth_list = dwdbBASEService.findDW_FXRTH_LatestRate();
			String varVer = scoreServiceAU.get_Version_AU();
			for(C120M01A c120m01a : c120m01a_list ){
				String custId = c120m01a.getCustId();
				String dupNo = c120m01a.getDupNo();
				
				C120S01A c120s01a = clsService.findC120S01A(c120m01a);
				C120S01B c120s01b = clsService.findC120S01B(c120m01a);
				C120S01C c120s01c = clsService.findC120S01C(c120m01a);
				C120S01E c120s01e = clsService.findC120S01E(c120m01a);
				C121S01A c121s01a = clsService.findC121S01A(meta);
				C121M01C c121m01c = clsService.findC121M01C_byC120M01A(c120m01a);
				if(clsService.custPosHasRating(c120m01a)){
					
					JSONObject fetch_score_src = fetch_score_src(branchRate,dw_fxrth_list,meta, c121s01a
							, c120s01a, c120s01b, c120s01c, c120s01e, varVer);
					
					if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲, meta.getMowType())){
						
						if(c121m01c==null){
							c121m01c = new C121M01C();
							OverSeaUtil.copyC121M01C(c121m01c, meta, custId, dupNo);
						}
						
						//重製數值(C121M01C)
						this.initC121m01c(c121m01c);
						
						JSONObject score_AU = new JSONObject();							
						score_AU.putAll(fetch_score_src);
						score_AU.putAll(scoreServiceAU.scoreAU(ScoreAU.type.澳洲消金模型基本, fetch_score_src, varVer, OverSeaUtil.海外評等_房貸));
						DataParse.toBean(score_AU, c121m01c);
						//------
						c121m01c_list.add(c121m01c);
					}					
				}				
			}
		}
		
		if(c121m01c_list.size()>0){
			//XXX 重算分數時，不應該用 tempSave
			SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
			//~~~~~~
			List<GenericBean> saved_list = new ArrayList<GenericBean>();
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			String creator = user.getUserId();
			for(C121M01C c121m01c: c121m01c_list){
				c121m01c.setCreator(creator);
				c121m01c.setCreateTime(nowTS);
				c121m01c.setUpdater(null);
				c121m01c.setUpdateTime(null);
				//===
				saved_list.add(c121m01c);
			}
			if(true){
				//以 C121M01C 的評等日期、版本為主
				C121M01C c121m01c = c121m01c_list.get(0);
				String varVer = c121m01c.getVarVer();
				
				meta.setRatingDate(null);
				meta.setVarVer(varVer);
				//------
				saved_list.add(meta);
			}
			clsService.daoSave(saved_list);
		}
	}
	
	@Override
	public void calc_C121_score_V3_0(C121M01A meta)
	throws CapException{
		List<C120M01A> c120m01a_list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if(CollectionUtils.isEmpty(c120m01a_list)){
			return;
		}
		
		List<GenericBean> saved_list = new ArrayList<GenericBean>();
		
		if(true){
			BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());
			List<Map<String, Object>> dw_fxrth_list = dwdbBASEService.findDW_FXRTH_LatestRate();
			String varVer = scoreServiceAU.get_Version_AU();
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			String creator = user.getUserId();
			
			for(C120M01A c120m01a : c120m01a_list ){
				String custId = c120m01a.getCustId();
				String dupNo = c120m01a.getDupNo();
				
				C120S01A c120s01a = clsService.findC120S01A(c120m01a);
				C120S01B c120s01b = clsService.findC120S01B(c120m01a);
				C120S01C c120s01c = clsService.findC120S01C(c120m01a);
				C120S01E c120s01e = clsService.findC120S01E(c120m01a);
				C121S01A c121s01a = clsService.findC121S01A(meta);
				//1.0、2.0版本無區分房貸非房貸，3.0開始拆分C121M01C=房貸、C121M01G=非房貸
				C121M01C c121m01c = clsService.findC121M01C_byC120M01A(c120m01a);
				C121M01G c121m01g = clsService.findC121M01G_byC120M01A(c120m01a);
				
				if(clsService.custPosHasRating(c120m01a)){
					
					JSONObject fetch_score_src = fetch_score_src(branchRate,dw_fxrth_list,meta, c121s01a
							, c120s01a, c120s01b, c120s01c, c120s01e, varVer);
					
					if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲, meta.getMowType())){
						//處理房貸部份
						if(c121m01c==null){
							c121m01c = new C121M01C();
							OverSeaUtil.copyC121M01C(c121m01c, meta, custId, dupNo);
						}
						//重製數值(C121M01C)
						this.initC121m01c(c121m01c);
						
						JSONObject score_AU = new JSONObject();							
						score_AU.putAll(fetch_score_src);
						score_AU.putAll(scoreServiceAU.scoreAU(ScoreAU.type.澳洲消金模型基本, fetch_score_src, varVer, OverSeaUtil.海外評等_房貸));
						DataParse.toBean(score_AU, c121m01c);
						c121m01c.setCreator(creator);
						c121m01c.setCreateTime(nowTS);
						c121m01c.setUpdater(creator);
						c121m01c.setUpdateTime(nowTS);
						c121m01c.setRatingDate(nowTS);
						//2.0不可做評等調整，故noAdj[註記不需調整]，直接寫[1]=是
						c121m01c.setNoAdj("1");
						//------
						saved_list.add(c121m01c);
						
						//處理非房貸部份
						if(c121m01g==null){
							c121m01g = new C121M01G();
							OverSeaUtil.copyC121M01G(c121m01g, meta, custId, dupNo);
						}
						//重製數值(C121M01G)					
						JSONObject score_AU_NotHouse = new JSONObject();							
						score_AU_NotHouse.putAll(fetch_score_src);
						score_AU_NotHouse.putAll(scoreServiceAU.scoreAU(ScoreAU.type.澳洲消金模型基本, fetch_score_src, varVer, OverSeaUtil.海外評等_非房貸));
						DataParse.toBean(score_AU_NotHouse, c121m01g);
						c121m01g.setCreator(creator);
						c121m01g.setCreateTime(nowTS);
						c121m01g.setUpdater(creator);
						c121m01g.setUpdateTime(nowTS);
						c121m01g.setRatingDate(nowTS);
						//2.0不可做評等調整，故noAdj[註記不需調整]，直接寫[1]=是
						c121m01g.setNoAdj("1");
						//------
						saved_list.add(c121m01g);
					}					
				}				
			}
			meta.setRatingDate(nowTS);
			meta.setVarVer(varVer);
			//避免評等升版時，缺乏國別資料
			if(Util.isEmpty(Util.trim(meta.getMowTypeCountry()))){
				meta.setMowTypeCountry(OverSeaUtil.getMowTypeCountry(meta.getCaseBrId()));
			}
			//------
			saved_list.add(meta);
		}
		
		clsService.daoSave(saved_list);
		
	}
	
	private JSONObject fetch_score_src(BranchRate branchRate, List<Map<String, Object>> dw_fxrth_list, C121M01A meta, C121S01A c121s01a
			, C120S01A c120s01a, C120S01B c120s01b
			, C120S01C c120s01c, C120S01E c120s01e, String varVer)
	throws CapException{
		JSONObject fetch_score_src = new JSONObject();
		
		JSONObject json_c120s01b = DataParse.toJSON(c120s01b);
		JSONObject json_c120s01e = DataParse.toJSON(c120s01e);
		JSONObject json_c121s01a = DataParse.toJSON(c121s01a);
		
		fetch_score_src.put(ScoreAU.column.VEDA查詢日期, json_c120s01e.get("vedaQDate") );
				
		String chkItemAUG1 = _get_chkItemAUG1(c120s01e);
		String chkItemAUG2 = _get_chkItemAUG2(c120s01e);
		String chkItemAUG3 = _get_chkItemAUG3(c120s01e);
		String chkItemAUS1 = _get_chkItemAUS1(c120s01e);
		String chkItemAUS2 = _get_chkItemAUS2(c120s01e);
		String chkItemAUO1 = _get_chkItemAUO1(c120s01e);
		
		fetch_score_src.put(ScoreAU.column.AU一般警訊1, chkItemAUG1);
		fetch_score_src.put(ScoreAU.column.AU一般警訊2, chkItemAUG2);
		fetch_score_src.put(ScoreAU.column.AU一般警訊3, chkItemAUG3);
		fetch_score_src.put(ScoreAU.column.AU特殊警訊1, chkItemAUS1);
		fetch_score_src.put(ScoreAU.column.AU特殊警訊2, chkItemAUS2);		
		fetch_score_src.put(ScoreAU.column.AU其他資訊1, chkItemAUO1);
		
		String item_m1 = "";
		Date raw_m1 = null;
		String raw_payCurr = Util.trim(c120s01b.getPayCurr());
		BigDecimal exRate_pay = _proc_exRate(raw_payCurr, branchRate, dw_fxrth_list);
		
		String raw_otherCurr = Util.trim(c120s01c.getOMoneyCurr());
		BigDecimal exRate_oth = _proc_exRate(raw_otherCurr, branchRate, dw_fxrth_list);
				
		String raw_hincomeCurr = Util.trim(c120s01c.getYFamCurr());		
		BigDecimal exRate_hincome = _proc_exRate(raw_hincomeCurr, branchRate, dw_fxrth_list);
		BigDecimal raw_hincomeAmt = c120s01c.getYFamAmt();
		String p3_curr = Util.trim(c120s01c.getYFamCurr());
		
		String raw_rincomeCurr = Util.trim(c120s01c.getRealEstateRentIncomeCurr());
		BigDecimal exRate_rincome = _proc_exRate(raw_rincomeCurr, branchRate, dw_fxrth_list);
		
		String raw_invMBalCurr = Util.trim(c120s01c.getInvMBalCurr());
		BigDecimal exRate_invMBal = _proc_exRate(raw_invMBalCurr, branchRate, dw_fxrth_list);
		
		String raw_invOBalCurr = Util.trim(c120s01c.getInvOBalCurr());
		BigDecimal exRate_invOBal = _proc_exRate(raw_invOBalCurr, branchRate, dw_fxrth_list);
		
		String raw_branAmtCurr = Util.trim(c120s01c.getBranCurr());	
		BigDecimal exRate_branAmt = _proc_exRate(raw_branAmtCurr, branchRate, dw_fxrth_list);
		
		BigDecimal p3 = null;
		Integer raw_a5 = null;
		String item_a5 = ""; 
		
		if(true){
			if(true){
				raw_m1 = OverSeaUtil.get_raw_m1(c120s01a);
				item_m1 = Util.trim(OverSeaUtil.getAge(raw_m1));
			}			
			if(exRate_hincome!=null && raw_hincomeAmt!=null){
				p3 = Arithmetic.mul(exRate_hincome, raw_hincomeAmt);
				p3 = Arithmetic.div(p3, BigDecimal.ONE, 0);
			}
			
			if(true){
				raw_a5 = OverSeaUtil.get_raw_a5(meta);
				
				if(raw_a5!=null){					
					int a5_scale = 2;//小數點後2位
					item_a5 = String.valueOf(Arithmetic.div(new BigDecimal(raw_a5), new BigDecimal(12), a5_scale) );	
				}				
			}
		}
				
		if(true){
			fetch_score_src.put(ScoreAU.column.出生日M1, Util.trim(TWNDate.toAD(raw_m1)));
			fetch_score_src.put(ScoreAU.column.因子M1_年齡, item_m1);	
		}
		
		fetch_score_src.put(ScoreAU.column.因子M5_職業, json_c120s01b.get("jobType1"));
		fetch_score_src.put(ScoreAU.column.因子M7_年資, json_c120s01b.get("seniority"));
		
		if(true){			
			fetch_score_src.put(ScoreAU.column.年薪幣別, raw_payCurr);
			fetch_score_src.put(ScoreAU.column.轉換匯率_年薪, Util.trim(exRate_pay));
			
			fetch_score_src.put(ScoreAU.column.其它收入幣別, raw_otherCurr);
			fetch_score_src.put(ScoreAU.column.轉換匯率_其他收入, Util.trim(exRate_oth));
			
			fetch_score_src.put(ScoreAU.column.家庭所得幣別, raw_hincomeCurr);
			fetch_score_src.put(ScoreAU.column.轉換匯率_家庭所得, Util.trim(exRate_hincome));
			fetch_score_src.put(ScoreAU.column.家庭所得金額P3, raw_hincomeAmt);
			
			fetch_score_src.put(ScoreAU.column.本次新做案下不動產租金收入幣別 , raw_rincomeCurr);
			fetch_score_src.put(ScoreAU.column.轉換匯率_本次新做案下不動產租金收入, Util.trim(exRate_rincome));
			
			fetch_score_src.put(ScoreAU.column.財富管理_本行幣別 , raw_invMBalCurr);
			fetch_score_src.put(ScoreAU.column.轉換匯率_財富管理本行, Util.trim(exRate_invMBal));
			
			fetch_score_src.put(ScoreAU.column.財富管理_它行幣別 , raw_invOBalCurr);
			fetch_score_src.put(ScoreAU.column.轉換匯率_財富管理它行, Util.trim(exRate_invOBal));
			
			fetch_score_src.put(ScoreAU.column.金融機構存款往來情形幣別 , raw_branAmtCurr);
			fetch_score_src.put(ScoreAU.column.轉換匯率_金融機構存款往來情形, Util.trim(exRate_branAmt));
							
			fetch_score_src.put(ScoreAU.column.因子P3_夫妻年收入_幣別, Util.trim(p3_curr));
			fetch_score_src.put(ScoreAU.column.因子P3_夫妻年收入, Util.trim(p3));
		}
		
		if(true){
			fetch_score_src.put(ScoreAU.column.月份數A5, Util.trim(raw_a5));
			fetch_score_src.put(ScoreAU.column.因子A5_契約年限, item_a5 );
		}
		fetch_score_src.put(ScoreAU.column.因子Z1, json_c121s01a.get("factor1") );
		fetch_score_src.put(ScoreAU.column.因子Z2, json_c121s01a.get("factor2") );
		
		
		if(Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU)){ //3.0模型，無D1、O1因子，新增EDU、DRATE因子
			if(true){
				fetch_score_src.put(ScoreAU.column.因子edu, c120s01a.getEdu());
				fetch_score_src.put(ScoreAU.column.因子drate, c120s01c.getDRate());
			}
		}else{
			//1.0、2.0模型，檢核D1、O1
			if(true){
				fetch_score_src.put(ScoreAU.column.因子O1_VEDASCORE, Util.trim(c120s01e.getVedaScore()));
			}
			if(true){
				String icr_na = OverSeaUtil.get_item_icr_na(c120s01c);
				BigDecimal icr = null;
				if(Util.equals("0", icr_na)){
					icr = c120s01c.getICR();
				}
				fetch_score_src.put(ScoreAU.column.因子D1_ICR註記, icr_na);
				fetch_score_src.put(ScoreAU.column.因子D1_ICR值, Util.trim(icr));
			}
		}

		return fetch_score_src;
	}
	
	/**
	 * 取得的匯率可能是 3.3783783784
	 * 但在 eloan 只存到小數點後5位
	 * 
	 * 若直接用 branchRate.toLocalAmt(curr, amt)
	 * 可能會和 amt*匯率 的值，有一些差異 
	 */
//	private BigDecimal _proc_exRate(String inputCurr, BranchRate branchRate){
//		if(Util.isEmpty(Util.trim(inputCurr))){
//			return null;
//		}
//		int _scale = 5;//小數點後5位
//		return Arithmetic.div(branchRate.toLocalRate(inputCurr), BigDecimal.ONE, _scale);		
//	}
	
	private BigDecimal _proc_exRate(String inputCurr, BranchRate branchRate, List<Map<String, Object>> dw_fxrth_list){
		int _scale = 5;//小數點後5位
		return clsService.proc_exRate(inputCurr, branchRate.getMCurr(), _scale, dw_fxrth_list);
	}
	
	private String __get_chkItem_default(String val){
		if(Util.equals(UtilConstants.haveNo.有, val)){
			return UtilConstants.haveNo.有;
		}else if(Util.equals(UtilConstants.haveNo.無, val)){
			return UtilConstants.haveNo.無;
		}
		return UtilConstants.haveNo.NA;
	}	
	
	private String _get_chkItem_when_GE_cmpVal(String isQdata, Integer cntQdata, int cmpVal){
		if(Util.equals(UtilConstants.haveNo.有, isQdata)){
			if(cntQdata==null){
				return UtilConstants.haveNo.無;
			}else{
				if(cntQdata>=cmpVal){
					return UtilConstants.haveNo.有;
				}else{
					return UtilConstants.haveNo.無;
				}	
			}			
		}else if(Util.equals(UtilConstants.haveNo.無, isQdata)){
			return UtilConstants.haveNo.無;
		}
		return UtilConstants.haveNo.NA;	
	}
	
	private String _get_chkItemAUG1(C120S01E c120s01e){
		return __get_chkItem_default(c120s01e.getVedaJudgement());
	}
	private String _get_chkItemAUG2(C120S01E c120s01e){
		return _get_chkItem_when_GE_cmpVal(c120s01e.getVedaEnquiriesFlag()
				, c120s01e.getVedaEnquiriesTimes(), 5);
	}
	private String _get_chkItemAUG3(C120S01E c120s01e){
		return __get_chkItem_default(c120s01e.getVedaProvider());
	}
	private String _get_chkItemAUS1(C120S01E c120s01e){		
		return __get_chkItem_default(c120s01e.getVedaAdverseFile());
	}
	private String _get_chkItemAUS2(C120S01E c120s01e){		
		return __get_chkItem_default(c120s01e.getVedaDefaultAmt());
	}
	private String _get_chkItemAUO1(C120S01E c120s01e){		
		boolean match = false;
		if(true){
			String valG1 = _get_chkItemAUG1(c120s01e);
			String valG2 = _get_chkItemAUG2(c120s01e);
			String valG3 = _get_chkItemAUG3(c120s01e);
			String valS1 = _get_chkItemAUS1(c120s01e);
			String valS2 = _get_chkItemAUS2(c120s01e);
			if(Util.equals(UtilConstants.haveNo.有, valG1) ||
					Util.equals(UtilConstants.haveNo.有, valG2) ||
					Util.equals(UtilConstants.haveNo.有, valG3) ||
					Util.equals(UtilConstants.haveNo.有, valS1) ||
					Util.equals(UtilConstants.haveNo.有, valS2) ){
				match = false;
			}else{
				//皆未觸動
				match = (c120s01e.getVedaFileAge()!=null && c120s01e.getVedaFileAge()>=7);	
			}
		}
		return match?UtilConstants.haveNo.有:UtilConstants.haveNo.無;
	}
	
	private boolean isCommonFactorChg(C121M01A meta, Integer c121m01_grade_a5, Integer c121m01_grade_z1, Integer c121m01_grade_z2){
		
		if(Util.notEquals(Util.trim(c121m01_grade_a5), Util.trim(OverSeaUtil.get_raw_a5(meta)))){	
			return true;
		}
		C121S01A c121s01a = clsService.findC121S01A(meta);
		if(c121s01a==null){
			return true;
		}else{			
			if(Util.notEquals(Util.trim(c121m01_grade_z1), Util.trim(c121s01a.getFactor1()))){
				return true;
			}
			if(Util.notEquals(Util.trim(c121m01_grade_z2), Util.trim(c121s01a.getFactor2()))){
				return true;
			}
		}
		
		return false;
	}
	
	private boolean isCustFactorChg(C121M01A meta, C120M01A c120m01a, C121M01C c121m01c){
		try{
			isCustFactorChg_str(meta, c120m01a, c121m01c);
			return false;
		}catch(CapException r){
			debug("isCustFactorChg【"+r.getMessage()+"】");
			return true;
		}		
	}
	
	private void isCustFactorChg_str(C121M01A meta, C120M01A c120m01a, C121M01C c121m01c)
	throws CapException{		
		C120S01A c120s01a = clsService.findC120S01A(c120m01a);
		C120S01B c120s01b = clsService.findC120S01B(c120m01a);
		C120S01C c120s01c = clsService.findC120S01C(c120m01a);
		C120S01E c120s01e = clsService.findC120S01E(c120m01a);		
		//M1_age
		if(true){
			Date exist_raw_m1 = c121m01c.getRaw_m1();
			Date raw_m1 = OverSeaUtil.get_raw_m1(c120s01a);
			
			if(!OverSeaUtil.eqDate(exist_raw_m1, raw_m1)){				
				throw new CapException("raw_m1["+exist_raw_m1+" , "+ raw_m1+"]", getClass());
			}
		}
		//M5_occupation
		if(true){
			String exist_item_m5 = c121m01c.getItem_m5();
			String item_m5 = c120s01b.getJobType1();
			cmp_diff("m5", exist_item_m5, item_m5);
		}
		//M7_seniority
		if(true){
//			Integer exist_item_m7 = c121m01c.getItem_m7();
//			Integer m7 = c120s01b.getSeniority();
//			cmp_diff("m7", Util.trim(exist_item_m7), Util.trim(m7));
			if(!OverSeaUtil.eqBigDecimal(c121m01c.getItem_m7(), c120s01b.getSeniority())){
				throw new CapException("m7["+c121m01c.getItem_m7()+" , "+ c120s01b.getSeniority()+"]", getClass());
			}	
		}
		
		//P3
		if(true){
			cmp_diff("raw_payCurr", c121m01c.getRaw_payCurr(), c120s01b.getPayCurr());
			cmp_diff("raw_otherCurr", c121m01c.getRaw_otherCurr(), c120s01c.getOMoneyCurr());
			cmp_diff("raw_hincomeCurr", c121m01c.getRaw_hincomeCurr(), c120s01c.getYFamCurr());
			cmp_diff("raw_rincomeCurr", c121m01c.getRaw_rincomeCurr(), c120s01c.getRealEstateRentIncomeCurr());
			cmp_diff("raw_invMBalCurr", c121m01c.getRaw_invMBalCurr(), c120s01c.getInvMBalCurr());
			cmp_diff("raw_invOBalCurr", c121m01c.getRaw_invOBalCurr(), c120s01c.getInvOBalCurr());
			cmp_diff("raw_branAmtCurr", c121m01c.getRaw_branAmtCurr(), c120s01c.getBranCurr());
			
			BigDecimal raw_hincomeAmt = c120s01c.getYFamAmt();
				
			if(!OverSeaUtil.eqBigDecimal(c121m01c.getRaw_hincomeAmt(), raw_hincomeAmt)){
				throw new CapException("raw_hincomeAmt["+c121m01c.getRaw_hincomeAmt()+" , "+ raw_hincomeAmt+"]", getClass());
			}
		}
		
		
		
		//模型3.0要再判斷[學歷]、[個人負債比]
		String varVer = Util.trim(meta.getVarVer());
		if(Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU)){ //3.0模型
			//edu
			if(true){
				String exist_item_edu = c121m01c.getItem_edu();
				String item_edu = c120s01a.getEdu();
				cmp_diff("edu", exist_item_edu, item_edu);
			}
			//drate
			if(true){
				BigDecimal exist_item_drate = c121m01c.getItem_drate();
				BigDecimal item_drate = c120s01c.getDRate();
				if(!OverSeaUtil.eqBigDecimal(exist_item_drate, item_drate)){
					throw new CapException("drate["+exist_item_drate+" , "+ item_drate+"]", getClass());
				}
			}
		}else{ //1.0、2.0沒模型才有D1、O1因子
			//D1_ICR
			if(true){
				String exist_item_d1_na = c121m01c.getItem_d1_na();
				String d1_na = OverSeaUtil.get_item_icr_na(c120s01c);
				cmp_diff("icr_na", Util.trim(exist_item_d1_na), Util.trim(d1_na));
				
				if(!OverSeaUtil.eqBigDecimal(c121m01c.getItem_d1_icr(), c120s01c.getICR())){
					throw new CapException("icr["+c121m01c.getItem_d1_icr()+" , "+ c120s01c.getICR()+"]", getClass());
				}
			}
			//O1_Veda Score
			if(true){
				cmp_diff("vedaScore", Util.trim(c121m01c.getItem_o1()), Util.trim(c120s01e.getVedaScore()));
			}
		}
		
		
		// 負面資訊  異動，影響 Veda Report 資訊[一般警訊、特殊警訊、其他內容]
		cmp_diff("chkItemAUG1", c121m01c.getChkItemAUG1(), _get_chkItemAUG1(c120s01e));
		cmp_diff("chkItemAUG2", c121m01c.getChkItemAUG2(), _get_chkItemAUG2(c120s01e));
		cmp_diff("chkItemAUG3", c121m01c.getChkItemAUG3(), _get_chkItemAUG3(c120s01e));
		cmp_diff("chkItemAUS1", c121m01c.getChkItemAUS1(), _get_chkItemAUS1(c120s01e));
		cmp_diff("chkItemAUS2", c121m01c.getChkItemAUS2(), _get_chkItemAUS2(c120s01e));
		cmp_diff("chkItemAUO1", c121m01c.getChkItemAUO1(), _get_chkItemAUO1(c120s01e));
	}	
	
	private void cmp_diff(String desc, String a, String b)throws CapException{
		if(Util.notEquals(a, b)){
			throw new CapException(desc+"["+a+" , "+b+"]", getClass());
		}
	}
	
	@Override	
	public void del_noneRating_score(C121M01A meta){		
		List<C120M01A> noneRating_list = clsService.filter_noneRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
		
		List<C121M01C> delList_C = new ArrayList<C121M01C>();		
		List<C121M01G> delList_G = new ArrayList<C121M01G>();		
		for(C120M01A c120m01a : noneRating_list ){			
			C121M01C c121m01c = clsService.findC121M01C_byC120M01A(c120m01a);
			if(c121m01c!=null){
				delList_C.add(c121m01c);				
			}
			
			C121M01G c121m01g = clsService.findC121M01G_byC120M01A(c120m01a);
			if(c121m01g!=null){
				delList_G.add(c121m01g);				
			}
			
		}
		
		if(delList_G.size()>0){
			c121m01gDao.delete(delList_G);	
		}
		
		if(delList_C.size()>0){
			/*
			在 SimpleContextHolder 放 N，應該對 dao 沒有影響
			SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
			 */
			c121m01cDao.delete(delList_C);	
			//--------------
			tempDataService.deleteByMainId(meta.getMainId());	
		}			
	}
	
	@Override	
	public boolean should_calc_C121_score(int page, C121M01A meta){
		List<C120M01A> shouldRating_list = new ArrayList<C120M01A>();
		if(true){
			List<C120M01A> src_list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
			shouldRating_list = clsService.filter_shouldRating(src_list);			
		}
		String debugStr = "should_calc_C121_score==TRUE";
		//房貸非房貸的版號、因子都是一樣的，因此這邊可以統一規則
		String varVerNow = scoreServiceAU.get_Version_AU(); //目前適用之評等版本
		String meta_varVer = Util.trim(meta.getVarVer()); //資料本來的評等版本
		
		
		if(true){ 
			if(Util.notEquals(varVerNow, meta_varVer)){ //目前版本不同，一定要重算
				return true;
			}else{
				Set<String> varVerSet = new HashSet<String>();
				if(Util.isNotEmpty(Util.trim(meta.getVarVer()))){
					varVerSet.add(Util.trim(meta.getVarVer()));
				}
				for(C120M01A c120m01a : shouldRating_list ){			
					C121M01C c121m01c = clsService.findC121M01C_byC120M01A(c120m01a);
					if(c121m01c==null){
						debug(debugStr+"["+c120m01a.getCustId()+"-"+c120m01a.getDupNo()+"]lost c121m01c");
						//應有評等，而無資料
						return true;	
					}
					//================
					//當 varVer 有不一致時，也要重算
					String varVer = Util.trim(c121m01c.getVarVer());
					if(Util.isNotEmpty(varVer)){
						varVerSet.add(varVer);
					}
				}	
				if(varVerSet.size()>1){
					debug(debugStr+"["+varVerSet+"]varVerSet.size()>1");
					return true;
				}
			}
		}
			
		if(page==1 || page==3){			
			if(shouldRating_list.size()>0){
				//各 custId 共用的因子
				C120M01A c120m01a = shouldRating_list.get(0);
				C121M01C c121m01c = clsService.findC121M01C_byC120M01A(c120m01a);
				
				if(isCommonFactorChg(meta, c121m01c.getRaw_a5(), c121m01c.getItem_z1(), c121m01c.getItem_z2())){
					debug(debugStr+"[isCommonFactorChg=true]");
					return true;	
				}	
			}									
		}
		if(page==2){
			for(C120M01A c120m01a: shouldRating_list){
				C121M01C c121m01c = clsService.findC121M01C_byC120M01A(c120m01a);
				
				if(isCustFactorChg(meta, c120m01a, c121m01c)){
					debug(debugStr+"["+c120m01a.getCustId()+"-"+c120m01a.getDupNo()+"][isCustFactorChg=true]");
					return true;
				}
			}
		}
		return false;
	}
	private void debug(String s){
		//logger.debug(s);
	}

	@Override	
	public Page<Map<String, Object>> queryPrint(String mainId, ISearch pageSetting) throws CapException{
		
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		C121M01A c121m01a = clsService.findC121M01AByMainId(mainId);
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1025M01Page.class);
		for(C120M01A c120m01a: clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(c121m01a)){
			Map<String, Object> data = new HashMap<String, Object>();
			
			data.put("oid", Util.trim(c120m01a.getOid()));
			data.put("mainId", Util.trim(c120m01a.getMainId()));
			data.put("type", "1");
			data.put("desc1", prop.getProperty("printItem1"));//借款人基本資料報表
			data.put("desc2", Util.trim(c120m01a.getCustId())+"-"+Util.trim(c120m01a.getDupNo())+" "+Util.trim(c120m01a.getCustName()));
			beanList.add(data);
		}
		if(true){
			Map<String, Object> data = new HashMap<String, Object>();
			
			data.put("oid", Util.trim(c121m01a.getOid()));
			data.put("mainId", Util.trim(c121m01a.getMainId()));
			data.put("type", "2");
			/*
			 * JP: Personal Credit Rating Sheet
			 * AU: Individual Credit Scorecard
			 */
			data.put("desc1", prop.getProperty("printItem2"));
			data.put("desc2", Util.trim(c121m01a.getCaseNo()));
			beanList.add(data);
		}
		
		return LMSUtil.getMapGirdDataRow(beanList, pageSetting);	
	}
	
	@Override
	public void deleteVedaReportDoc(String docOid) {
		docFileService.delete(docOid);
		
	}

	@Override
	public boolean runModelCompare(String docOid)
	throws CapMessageException{
		boolean r = false;
		DocFile docFile = docFileService.read(docOid);
		if (docFile == null) {
			return r;
		}

		WritableWorkbook workbook = null;
		String filePath = docFileService.getFilePath(docFile);

		try {

			workbook = Workbook.createWorkbook(new File(filePath),
					Workbook.getWorkbook(new File(filePath)));
			WritableSheet sheet = workbook.getSheet(0);

			if(true){
				_modelCompare(sheet);	
			}
			
			workbook.write();
			workbook.close();
			//======
			r = true;
		} catch (Exception e) {
			logger.error("parseExcel EXCEPTION!!", e);
			throw new CapMessageException(e, getClass());
		} finally {
			try {
				if (workbook != null) {
					workbook.close();
					workbook = null;
				}
			} catch (Exception e) {
				logger.debug("Workbook close EXCEPTION!", getClass());
			}
		}
		return r;
	}
	
	private String _get(WritableSheet sheet, int columnIdx, int rowIdx){
		return Util.trim(sheet.getCell(columnIdx, rowIdx).getContents());
	}
	
	private void _modelCompare(WritableSheet sheet)
	throws CapException{
		int rows = sheet.getRows();
		logger.debug("_modelCompare[rows="+rows+"]", getClass());
		
		WritableFont ok_font = new WritableFont(WritableFont.ARIAL, 10);
		WritableFont fail_font = new WritableFont(WritableFont.ARIAL, 10);
		try{
			ok_font.setColour( Colour.BLUE );
			//---
			fail_font.setColour( Colour.RED );
		}catch(WriteException e){
			
		}
		
		WritableCellFormat ok_CellFormat_right = new WritableCellFormat(ok_font);
		WritableCellFormat fail_CellFormat_left = new WritableCellFormat(fail_font);
		try{
			ok_CellFormat_right.setWrap(false);
			ok_CellFormat_right.setAlignment(Alignment.RIGHT);	
			//---
			fail_CellFormat_left.setWrap(false);
		}catch(WriteException e){
			
		}
		
		Map<String, Integer> col_idx_map = new LinkedHashMap<String, Integer>();
		
		if(true){
			col_idx_map.put("C_OWNBRID", 0);
			col_idx_map.put("C_CUSTID", 1);
			col_idx_map.put("C_DUPNO", 2);
			col_idx_map.put("C_CUSTNAME", 3);
			col_idx_map.put("C_BIRTHDAY", 4);
			col_idx_map.put("C_JOBTYPE1", 5);
			col_idx_map.put("C_JOBTYPE1_DESC", 6);
			col_idx_map.put("C_SENIORITY", 7);
			col_idx_map.put("C_ISSUELC", 8);
			col_idx_map.put("C_ICRFLAG", 9);
			col_idx_map.put("C_ICR", 10);
			col_idx_map.put("C_YFAMCURR", 11);
			col_idx_map.put("C_YFAMAMT", 12);
			col_idx_map.put("C_VEDASCORE", 13);
			col_idx_map.put("C_A5", 14);
			col_idx_map.put("C_Z1", 15);
			col_idx_map.put("C_Z2", 16);
			col_idx_map.put("C_VEDAJUDGEMENT", 17);
			col_idx_map.put("C_VEDAENQUIRIESFLAG", 18);
			col_idx_map.put("C_VEDAENQUIRIESTIMES", 19);
			col_idx_map.put("C_VEDAPROVIDER", 20);
			col_idx_map.put("C_VEDAADVERSEFILE", 21);
			col_idx_map.put("C_VEDADEFAULTAMT", 22);
			col_idx_map.put("C_VEDAFILEAGE", 23);
		}
		
		BranchRate branchRate = lmsService.getBranchRate(UtilConstants.BankNo.雪梨分行);
		List<Map<String, Object>> dw_fxrth_list = dwdbBASEService.findDW_FXRTH_LatestRate();
		Date vedaQDate = CapDate.parseDate(CapDate.ZERO_DATE);
		
		for (int row = 1; row < sheet.getRows(); row++) {
			String C_CUSTID = _get(sheet, col_idx_map.get("C_CUSTID"), row);
			//若 C_CUSTID 無資料，略過
			if (Util.isEmpty(C_CUSTID)) {
				break;
			}
//			for(String key : col_idx_map.keySet()){
//				Integer idx = col_idx_map.get(key);
//				String val = Util.trim(sheet.getCell(idx, row).getContents());
//				logger.debug("_modelCompare[row="+row+"]idx["+idx+"]key["+key+"]"+val);		
//			}
			String C_BIRTHDAY = _get(sheet, col_idx_map.get("C_BIRTHDAY"), row);
			String C_JOBTYPE1 = _get(sheet, col_idx_map.get("C_JOBTYPE1"), row);
			String C_SENIORITY = _get(sheet, col_idx_map.get("C_SENIORITY"), row);
			String C_ISSUELC = _get(sheet, col_idx_map.get("C_ISSUELC"), row);
			String C_ICRFLAG = _get(sheet, col_idx_map.get("C_ICRFLAG"), row);
			String C_ICR = _get(sheet, col_idx_map.get("C_ICR"), row);			
			String C_YFAMCURR = _get(sheet, col_idx_map.get("C_YFAMCURR"), row);
			String C_YFAMAMT = _get(sheet, col_idx_map.get("C_YFAMAMT"), row);
			String C_VEDASCORE = _get(sheet, col_idx_map.get("C_VEDASCORE"), row);
			String C_A5 = _get(sheet, col_idx_map.get("C_A5"), row);
			String C_Z1 = _get(sheet, col_idx_map.get("C_Z1"), row);
			String C_Z2 = _get(sheet, col_idx_map.get("C_Z2"), row);
			
			String C_VEDAJUDGEMENT = _get(sheet, col_idx_map.get("C_VEDAJUDGEMENT"), row);
			String C_VEDAENQUIRIESFLAG = _get(sheet, col_idx_map.get("C_VEDAENQUIRIESFLAG"), row);
			String C_VEDAENQUIRIESTIMES = _get(sheet, col_idx_map.get("C_VEDAENQUIRIESTIMES"), row);
			String C_VEDAPROVIDER = _get(sheet, col_idx_map.get("C_VEDAPROVIDER"), row);
			String C_VEDAADVERSEFILE = _get(sheet, col_idx_map.get("C_VEDAADVERSEFILE"), row);
			String C_VEDADEFAULTAMT = _get(sheet, col_idx_map.get("C_VEDADEFAULTAMT"), row);
			String C_VEDAFILEAGE = _get(sheet, col_idx_map.get("C_VEDAFILEAGE"), row);
			
			String output_age = "";
			String output_au_amt = "";
			String v1_0_pr = "";
			String v1_0_sr = "";
			String v2_0_pr = "";
			String v2_0_sr = "";
			
			if(true){
				C121M01A meta = new C121M01A();
				C121S01A c121s01a = new C121S01A();
				C120S01A c120s01a = new C120S01A();
				C120S01B c120s01b = new C120S01B();
				C120S01C c120s01c = new C120S01C();
				C120S01E c120s01e = new C120S01E();
				if(true){
					meta.setCustId(C_CUSTID);
					meta.setMowType(OverSeaUtil.C121M01A_MOW_TYPE_澳洲);
					if(true){	
						Double double_a5 = Arithmetic.round(Util.parseDouble(C_A5)*12, 0);
						int a5_months = double_a5.intValue();
						meta.setLnYear(a5_months/12);
						meta.setLnMonth(a5_months%12);
					}
					//------
					c121s01a.setFactor1(Util.parseInt(C_Z1));
					c121s01a.setFactor2(Util.parseInt(C_Z2));
					//------
					c120s01a.setBirthday(CapDate.parseDate(C_BIRTHDAY));
					//------
					c120s01b.setPayCurr("AUD");
					c120s01b.setJobType1(C_JOBTYPE1);
					c120s01b.setSeniority(Util.parseBigDecimal(C_SENIORITY));
					//------
					c120s01c.setOMoneyCurr("AUD");
					c120s01c.setRealEstateRentIncomeCurr("AUD");
					c120s01c.setInvMBalCurr("AUD");
					c120s01c.setInvOBalCurr("AUD");
					c120s01c.setBranCurr("AUD");					
					
					c120s01c.setYFamCurr(C_YFAMCURR);
					c120s01c.setYFamAmt(Util.parseBigDecimal(C_YFAMAMT));
					c120s01c.setIssueLC(C_ISSUELC);
					c120s01c.setICRFlag(C_ICRFLAG);
					c120s01c.setICR(Util.parseBigDecimal(C_ICR));			
					//------
					c120s01e.setVedaQDate(vedaQDate);
					c120s01e.setVedaScore(Util.parseInt(C_VEDASCORE));
					c120s01e.setVedaJudgement(C_VEDAJUDGEMENT);
					c120s01e.setVedaEnquiriesFlag(C_VEDAENQUIRIESFLAG);
					c120s01e.setVedaEnquiriesTimes(Util.parseInt(C_VEDAENQUIRIESTIMES));
					c120s01e.setVedaProvider(C_VEDAPROVIDER);
					c120s01e.setVedaAdverseFile(C_VEDAADVERSEFILE);
					c120s01e.setVedaDefaultAmt(C_VEDADEFAULTAMT);
					c120s01e.setVedaFileAge(Util.parseInt(C_VEDAFILEAGE));
				}
				
				String errMsg = "";
				if(true){
					List<String> err_list = new ArrayList<String>();
					if(true){
						if(c120s01a.getBirthday()==null){
							err_list.add("M1 is empty");
						}
					}
					if(true){
						String m5_val = c120s01b.getJobType1();
						if(Util.isEmpty(m5_val)){
							err_list.add("M5 is empty");
						}else{
							int i_m5_val = Util.parseInt(m5_val);
							if(m5_val.length()==2 &&(i_m5_val>=1&&i_m5_val<=14)){								
							}else{
								err_list.add("M5["+m5_val+"] should between 01~14");	
							}
						}						
					}
					if(true){
						if(Util.isEmpty(c120s01c.getYFamCurr())){
							err_list.add("P3 curr is empty");
						}else{
							if(c120s01c.getYFamAmt()==null){
								err_list.add("P3 amt is empty");	
							}
						}
					}
					if(true){
						int z1_val = c121s01a.getFactor1();
						if(z1_val>=1 && z1_val<=5){							
						}else{
							err_list.add("Z1["+z1_val+"] should between 1~5");	
						}						
					}
					if(true){
						int z2_val = c121s01a.getFactor2();
						if(z2_val>=1 && z2_val<=4){							
						}else{
							err_list.add("Z2["+z2_val+"] should between 1~4");	
						}						
					}
					if(true){
						Map<String, String> veda_map = new LinkedHashMap<String, String>();
						veda_map.put("VEDAJUDGEMENT", c120s01e.getVedaJudgement());
						veda_map.put("VEDAENQUIRIESFLAG", c120s01e.getVedaEnquiriesFlag());
						veda_map.put("VEDAPROVIDER", c120s01e.getVedaProvider());
						veda_map.put("VEDAADVERSEFILE", c120s01e.getVedaAdverseFile());
						veda_map.put("VEDADEFAULTAMT", c120s01e.getVedaDefaultAmt());
						List<String> wrongCol = new ArrayList<String>();
						for(String k : veda_map.keySet()){
							String v = veda_map.get(k);
							if(Util.equals("1", v)||Util.equals("2", v)||Util.equals("3", v)){								
							}else{
								wrongCol.add(k);	
							}
						}		
						if(wrongCol.size()>0){
							err_list.add(StringUtils.join(wrongCol,",")+" should between 1~3");	
						}							
					}
					
					errMsg = StringUtils.join(err_list, ". ");
				}
				if(Util.isEmpty(errMsg)){
					if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲, meta.getMowType())){
						
						JSONObject fetch_score_src = fetch_score_src(branchRate, dw_fxrth_list, meta, c121s01a
								, c120s01a, c120s01b, c120s01c, c120s01e, ""); 
						
						output_age = fetch_score_src.optString(ScoreAU.column.因子M1_年齡);
						output_au_amt = LMSUtil.pretty_numStr(Util.parseBigDecimal(fetch_score_src.optString(ScoreAU.column.因子P3_夫妻年收入)));
						
						if(true){//模型1.0
							JSONObject score_AU = new JSONObject();							
							score_AU.putAll(fetch_score_src);
							score_AU.putAll(scoreServiceAU.scoreAU(ScoreAU.type.澳洲消金模型基本, fetch_score_src, OverSeaUtil.V1_0_LOAN_AU, OverSeaUtil.海外評等_房貸));						
							//===	
							v1_0_pr = score_AU.getString(ScoreAU.column.初始評等);
							v1_0_sr = score_AU.getString(ScoreAU.column.獨立評等);
						}
						if(true){//模型2.0
							JSONObject score_AU = new JSONObject();							
							score_AU.putAll(fetch_score_src);
							score_AU.putAll(scoreServiceAU.scoreAU(ScoreAU.type.澳洲消金模型基本, fetch_score_src, OverSeaUtil.V2_0_LOAN_AU, OverSeaUtil.海外評等_房貸));
							//===
							v2_0_pr = score_AU.getString(ScoreAU.column.初始評等);
							v2_0_sr = score_AU.getString(ScoreAU.column.獨立評等);
						}								
						
						int output_idx = 24;
						setMsgAtColumn(sheet, output_idx++, row, output_age, ok_CellFormat_right);
						setMsgAtColumn(sheet, output_idx++, row, output_au_amt, ok_CellFormat_right);
						setMsgAtColumn(sheet, output_idx++, row, v1_0_pr, ok_CellFormat_right);
						setMsgAtColumn(sheet, output_idx++, row, v1_0_sr, ok_CellFormat_right);
						setMsgAtColumn(sheet, output_idx++, row, v2_0_pr, ok_CellFormat_right);
						setMsgAtColumn(sheet, output_idx++, row, v2_0_sr, ok_CellFormat_right);
					}	
				}else{
					int output_idx = 24;
					try{
						sheet.mergeCells(output_idx, row, output_idx+5, row);
					}catch (Exception e) {
						
					}
					setMsgAtColumn(sheet, output_idx, row, "【Error】"+errMsg, fail_CellFormat_left);
				}
					
			}
		}
	}
	
	private void setMsgAtColumn(WritableSheet sheet, int columnIdx, int rowIdx, String m
			, WritableCellFormat format){
		Label label = new Label(columnIdx, rowIdx, m);
		label.setCellFormat(format);
		try{
			sheet.addCell(label);
		}catch (Exception e) {
			
		}
	}

	private String getBigDecimal(Map<String, Object> map, String key){
		Object d = MapUtils.getObject(map, key);
		if(d==null){
			return "";	
		}else if(d instanceof Double){
			
			return Util.trim(LMSUtil.pretty_numStr(new BigDecimal((Double)d)));
		}else if(d instanceof Long){
			
			return Util.trim(LMSUtil.pretty_numStr(new BigDecimal((Long)d)));
		}else{
			
			return Util.trim(MapUtils.getString(map, key));
		}		
	}
	
	@Override
	public void expXls_modelCompareSrcData(ByteArrayOutputStream outputStream) 
	throws IOException, WriteException{
	
		
		List<String[]> rows = new ArrayList<String[]>();
		
		int intput_col_size = 24;
		
		String[] brNoArrs = (String[])new ArrayList<String>(LMSUtil.get_AU_BRNO_SET()).toArray(new String[0]);
		//在 雪梨分行(0B9)的功能選單：內部評等 > 消金申請信用評等表 > 評等比對 , click button 匯出資料檔		
		for(Map<String, Object> map : eloandbBASEService.listLMS1205(brNoArrs)){
			String[] arr = new String[intput_col_size];
			for(int i=0; i<intput_col_size; i++){
				arr[i] = "";
			}			
			//---
			arr[0] = Util.trim(MapUtils.getString(map, "OWNBRID"));
			arr[1] = Util.trim(MapUtils.getString(map, "CUSTID"));
			arr[2] = Util.trim(MapUtils.getString(map, "DUPNO"));
			arr[3] = Util.trim(MapUtils.getString(map, "CUSTNAME"));
			arr[4] = Util.trim(TWNDate.toAD((Date)MapUtils.getObject(map, "BIRTHDAY")));
			arr[5] = Util.trim(MapUtils.getString(map, "JOBTYPE1"));
			arr[6] = "";
			arr[7] = Util.trim(LMSUtil.pretty_numStr(CrsUtil.parseBigDecimal(MapUtils.getObject(map, "SENIORITY"))));
			arr[8] = Util.trim(MapUtils.getString(map, "ISSUELC"));
			arr[9] = Util.trim(MapUtils.getString(map, "ICRFLAG"));
			arr[10] = getBigDecimal(map, "ICR");
			arr[11] = Util.trim(MapUtils.getString(map, "YFAMCURR"));
			arr[12] = getBigDecimal(map, "YFAMAMT");
			arr[13] = Util.trim(MapUtils.getInteger(map, "VEDASCORE"));
			arr[14] = getBigDecimal(map, "ITEM_A5");
			arr[15] = Util.trim(MapUtils.getString(map, "ITEM_Z1"));
			arr[16] = Util.trim(MapUtils.getString(map, "ITEM_Z2"));
			arr[17] = Util.trim(MapUtils.getString(map, "VEDAJUDGEMENT"));
			arr[18] = Util.trim(MapUtils.getString(map, "VEDAENQUIRIESFLAG"));
			arr[19] = Util.trim(MapUtils.getInteger(map, "VEDAENQUIRIESTIMES"));
			arr[20] = Util.trim(MapUtils.getString(map, "VEDAPROVIDER"));
			arr[21] = Util.trim(MapUtils.getString(map, "VEDAADVERSEFILE"));
			arr[22] = Util.trim(MapUtils.getString(map, "VEDADEFAULTAMT"));
			arr[23] = Util.trim(MapUtils.getInteger(map, "VEDAFILEAGE"));
			rows.add(arr);
		}
		//===============================
		WritableWorkbook workbook = copy_from_workbook(outputStream
				, PropUtil.getProperty("loadFile.dir")+ "excel/LMS1205.xls");
					
		if(true){
			WritableFont def_font = new WritableFont(WritableFont.ARIAL, 10);
			WritableCellFormat cellFormatL = new WritableCellFormat(def_font);
			{
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setWrap(false);
			}
			WritableCellFormat cellFormatR = new WritableCellFormat(def_font);
			{
				cellFormatR.setAlignment(Alignment.RIGHT);
				cellFormatR.setWrap(false);
			}
			if(true){	
				WritableSheet sheet1 = workbook.getSheet(0);
				int i = 0;
				int headerCnt = 1; 
				
				for(String[] arr: rows){
					int colLen = arr.length;
					int rowIdx =  (i + headerCnt);
					int formulaCol = 6;
					
					if(true){
						
					}
					for(int i_col = 0; i_col <colLen ; i_col++){
						if(i_col==formulaCol){
							Formula formula = new Formula(formulaCol,rowIdx
									, "IF(F"+(rowIdx+1)+"=\"\",\"\",VLOOKUP(F"+(rowIdx+1)+",'MENU_Occupation'!$A$3:$B$16,2,FALSE)");
							sheet1.addCell(formula);	
						}else{
							//數值欄位(向右對齊)
							boolean alignRight = (i_col==7||i_col==10||i_col==12||i_col==13
									||i_col==14||i_col==15||i_col==16||i_col==19||i_col==23);
							sheet1.addCell(new Label( i_col, rowIdx
									, arr[i_col], alignRight?cellFormatR:cellFormatL));	
						}
						
					}
					//---	
					i++;
				}				
			}
		}
		// ---		
		workbook.write();
		workbook.close();	
	}
	
	private WritableWorkbook copy_from_workbook(OutputStream outputStream, String path)
	throws IOException{
		
		Workbook src_workbook = null;
		
		URL urlRpt = null;
		urlRpt = Thread.currentThread().getContextClassLoader().getResource(path);
		if (urlRpt == null)
			throw new IOException("get File fail");		
		try{
			src_workbook = Workbook.getWorkbook(new File(urlRpt.toURI()));
		}catch(Exception e){
		}	

		//產生 workbook 到 OutputStream 時，要加以下這段程式，以免 call workbook.write() 時異常
		WorkbookSettings settings = new WorkbookSettings();
		settings.setWriteAccess(null);
		
		WritableWorkbook workbook = Workbook.createWorkbook(outputStream, src_workbook, settings);
		if(src_workbook!=null){
			src_workbook.close();
		}
		return workbook;
	}
	
	@Override
	public List<C121M01E> findC121m01eByMainId(String mainId) {
		return c121m01eDao.findByMainId(mainId);
	}
	
	@Override
	public void delListC121m01e(List<C121M01E> list) {
		c121m01eDao.delete(list);
	}
	
	@Override
	public void saveListC121m01e(List<C121M01E> list) {
		if (!list.isEmpty()) {
			c121m01eDao.save(list);
		}
	}
	
	private void initC121m01c(C121M01C c121m01c){
		//重製評等相關欄位(共用欄位不用)
		c121m01c.setPRating(null);
		c121m01c.setSRating(null);
		c121m01c.setSprtRating(null);
		c121m01c.setAdjRating(null);
		c121m01c.setFRating(null);
		c121m01c.setOrgFr(null);
		c121m01c.setNoAdj(null);
		c121m01c.setAdjustStatus(null);
		c121m01c.setAdjustFlag(null);
		c121m01c.setAdjustReason(null);
		c121m01c.setDr_1yr(null);
		c121m01c.setDr_3yr(null);
		c121m01c.setPd(null);
		c121m01c.setSlope(null);
		c121m01c.setInterCept(null);
		
		c121m01c.setWeight_edu(null);
		c121m01c.setWeight_drate(null);
		c121m01c.setItem_edu(null);
		c121m01c.setScr_edu(null);
		c121m01c.setItem_drate(null);
		c121m01c.setScr_drate(null);
		c121m01c.setWeight_scr_a5(null);
		c121m01c.setWeight_scr_drate(null);
		c121m01c.setWeight_scr_edu(null);
		c121m01c.setWeight_scr_m1(null);
		c121m01c.setWeight_scr_m5(null);
		c121m01c.setWeight_scr_m7(null);
		c121m01c.setWeight_scr_p3(null);
		c121m01c.setWeight_scr_z1(null);
		c121m01c.setWeight_scr_z2(null);
		c121m01c.setStd_a5(null);
		c121m01c.setStd_core(null);
		c121m01c.setStd_m1(null);
		c121m01c.setStd_m5(null);
		c121m01c.setStd_m7(null);
		c121m01c.setStd_p3(null);
		c121m01c.setStd_z1(null);
		c121m01c.setStd_z2(null);
		
		//3.0未使用的2.0評等因子
		c121m01c.setWeight_d1(null);
		c121m01c.setItem_d1_icr(null);
		c121m01c.setItem_d1_na(null);
		c121m01c.setStd_d1(null);
		c121m01c.setScr_d1(null);
		c121m01c.setWeight_o1(null);
		c121m01c.setItem_o1(null);
		c121m01c.setStd_o1(null);
		c121m01c.setScr_o1(null);
	}
}