/* 
 * C101S01K.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金相關查詢主從債務人檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01K", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "lnGeId", "lnGeDupNo", "lnGeRe" }))
public class C101S01K extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 被擔保案身份證號
	 * <p/>
	 * (MIS.ELLNGTEE.LNGEID)
	 */
	@Size(max = 10)
	@Column(name = "LNGEID", length = 10, columnDefinition = "VARCHAR(10)")
	private String lnGeId;

	/**
	 * 被擔保案身份證號重複碼
	 * <p/>
	 * (MIS.ELLNGTEE.DUPNO1)
	 */
	@Size(max = 1)
	@Column(name = "LNGEDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String lnGeDupNo;

	/**
	 * 擔任身份
	 * <p/>
	 * 0：無<br/>
	 * (MIS.ELLNGTEE.LNGEFLAG)<br/>
	 * ※相關身份代號說明︰<br/>
	 * C：共同借款人或共同發票人<br/>
	 * E：票據債務人（指金融交易之擔保背書）<br/>
	 * G：連帶保證人或擔保品提供人兼連帶保證人<br/>
	 * L：連帶債務人或擔保品提供人兼連帶債務人<br/>
	 * N：一般保證人或擔保品提供人兼一般保證人<br/>
	 * S：擔保品提供人
	 */
	@Size(max = 1)
	@Column(name = "LNGEFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String lnGeFlag;

	/**
	 * 與主債務人關係
	 * <p/>
	 * (MIS.ELLNGTEE.LNGERE)
	 */
	@Size(max = 2)
	@Column(name = "LNGERE", length = 2, columnDefinition = "VARCHAR(2)")
	private String lnGeRe;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得被擔保案身份證號
	 * <p/>
	 * (MIS.ELLNGTEE.LNGEID)
	 */
	public String getLnGeId() {
		return this.lnGeId;
	}

	/**
	 * 設定被擔保案身份證號
	 * <p/>
	 * (MIS.ELLNGTEE.LNGEID)
	 **/
	public void setLnGeId(String value) {
		this.lnGeId = value;
	}

	/**
	 * 取得被擔保案身份證號重複碼
	 * <p/>
	 * (MIS.ELLNGTEE.DUPNO1)
	 */
	public String getLnGeDupNo() {
		return this.lnGeDupNo;
	}

	/**
	 * 設定被擔保案身份證號重複碼
	 * <p/>
	 * (MIS.ELLNGTEE.DUPNO1)
	 **/
	public void setLnGeDupNo(String value) {
		this.lnGeDupNo = value;
	}

	/**
	 * 取得擔任身份
	 * <p/>
	 * 0：無<br/>
	 * (MIS.ELLNGTEE.LNGEFLAG)<br/>
	 * ※相關身份代號說明︰<br/>
	 * C：共同借款人或共同發票人<br/>
	 * E：票據債務人（指金融交易之擔保背書）<br/>
	 * G：連帶保證人或擔保品提供人兼連帶保證人<br/>
	 * L：連帶債務人或擔保品提供人兼連帶債務人<br/>
	 * N：一般保證人或擔保品提供人兼一般保證人<br/>
	 * S：擔保品提供人
	 */
	public String getLnGeFlag() {
		return this.lnGeFlag;
	}

	/**
	 * 設定擔任身份
	 * <p/>
	 * 0：無<br/>
	 * (MIS.ELLNGTEE.LNGEFLAG)<br/>
	 * ※相關身份代號說明︰<br/>
	 * C：共同借款人或共同發票人<br/>
	 * E：票據債務人（指金融交易之擔保背書）<br/>
	 * G：連帶保證人或擔保品提供人兼連帶保證人<br/>
	 * L：連帶債務人或擔保品提供人兼連帶債務人<br/>
	 * N：一般保證人或擔保品提供人兼一般保證人<br/>
	 * S：擔保品提供人
	 **/
	public void setLnGeFlag(String value) {
		this.lnGeFlag = value;
	}

	/**
	 * 取得與主債務人關係
	 * <p/>
	 * (MIS.ELLNGTEE.LNGERE)
	 */
	public String getLnGeRe() {
		return this.lnGeRe;
	}

	/**
	 * 設定與主債務人關係
	 * <p/>
	 * (MIS.ELLNGTEE.LNGERE)
	 **/
	public void setLnGeRe(String value) {
		this.lnGeRe = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
