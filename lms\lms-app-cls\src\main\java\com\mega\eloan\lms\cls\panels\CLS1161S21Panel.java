/* 
 * CLS1161S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 中鋼整批評等檔 - 基本資訊
 * </pre>
 * 
 * @since 2015/07/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/07/17,EL07623,new
 *          </ul>
 */
public class CLS1161S21Panel extends Panel {

	public CLS1161S21Panel(String id) {
		super(id);
	}

	public CLS1161S21Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new DocLogPanel("_docLog").processPanelData(model, params);
	}

	/**/
	private static final long serialVersionUID = 1L;
}
