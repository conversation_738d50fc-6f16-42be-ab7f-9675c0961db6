package tw.com.jcs.flow.core;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xml.sax.Attributes;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.DefaultHandler;

import tw.com.jcs.flow.FlowDefinition;
import tw.com.jcs.flow.FlowDefinitionParser;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.model.NodeType;
import tw.com.jcs.flow.node.DecisionNode;
import tw.com.jcs.flow.node.EndNode;
import tw.com.jcs.flow.node.FlowNode;
import tw.com.jcs.flow.node.ForkNode;
import tw.com.jcs.flow.node.JoinNode;
import tw.com.jcs.flow.node.ScriptNode;
import tw.com.jcs.flow.node.StartNode;
import tw.com.jcs.flow.node.StateNode;
import tw.com.jcs.flow.node.SubProcessNode;
import tw.com.jcs.flow.node.TaskNode;
import tw.com.jcs.flow.provider.FlowHandler;
import tw.com.jcs.flow.provider.ObjectFactory;

/**
 * <pre>
 * 流程定義資訊解析
 * </pre>
 * 
 * @since 2022年12月22日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月22日
 *          </ul>
 */
public class FlowDefinitionParserImpl implements FlowDefinitionParser {

    static final Logger log = LoggerFactory.getLogger(FlowDefinitionParserImpl.class);

    static final FlowHandler EMPTY_HANDLER = new FlowHandler() {
        public void handle(FlowInstance instance, String nodeName, String transition) {
        }
    };

    ObjectFactory objectFactory;

    SAXParser parser;

    /**
     * 建立SAXParser實例
     * 
     * @param objectFactory
     */
    public FlowDefinitionParserImpl(ObjectFactory objectFactory) {
        super();
        this.objectFactory = objectFactory;
        try {
            SAXParserFactory spf = SAXParserFactory.newInstance();
            spf.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            spf.setFeature("http://xml.org/sax/features/external-general-entities", false);
            spf.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            parser = spf.newSAXParser();
        } catch (Exception e) {
            throw new FlowException("can't initialize SAXParser : " + e, e);
        }
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowDefinitionParser#parse(org.xml.sax.InputSource)
     */
    @Override
    public FlowDefinition parse(InputSource source) throws IOException, SAXException {
        final FlowDefinitionImpl def = new FlowDefinitionImpl();
        parser.parse(source, new DefaultHandler() {
            FlowNode node;
            String action = null;
            StringBuilder buf = new StringBuilder(512);

            public void characters(char[] ch, int start, int length) throws SAXException {
                if (node instanceof ScriptNode) {
                    buf.append(ch, start, length);
                }
            }

            public void endElement(String uri, String localName, String name) throws SAXException {
                if (node instanceof ScriptNode) {
                    ((ScriptNode) node).setCode(buf.toString().trim());
                    buf.setLength(0);
                }
            }

            public void startElement(String uri, String local, String name, Attributes attr) throws SAXException {

                if (NodeType.PROCESS.value.equalsIgnoreCase(name)) {
                    // 第一個節點類型必為PROCESS
                    // 設定流程定義的主要屬性
                    def.setName(attr.getValue("name"));
                    String clazz = attr.getValue("handler");
                    if (clazz != null) {
                        try {
                            // 產生Handler的實體
                            // FlowHandler handler = objectFactory.create(clazz);
                            // def.setHandler(handler);
                            // 2022/08/28 在建立 flowService 時就會呼叫到這段程式，
                            // 若在此處直接建立 FlowHandler instance、而 FlowHandler(或是 FlowHandler 的 dependencies) 又會透過 Spring 注入 flowService 的話，
                            // 會造成循環參照，故此處只先設定 class name，而不產生 instance，待真的被調用時，若 FlowDefinitionImpl 和 flow handler 未建立關係才建立。
                            // 實際上還是在 ap 啟動時就已經透過 spring component-scan 建立 flow handler bean 了，故對交易執行效能不會有影響。
                            def.setHandlerClass(clazz);
                        } catch (Exception e) {
                            log.error("Can't create handler class '{}' for definition '{}' : {}", new Object[] { clazz, def.getName(), e.getMessage() });
                            def.setHandler(EMPTY_HANDLER);
                        }
                    }
                } else if (NodeType.TRANSITION.value.equalsIgnoreCase(name)) { // 執行路徑
                    String tName = attr.getValue("name"); // 路徑名稱
                    String to = attr.getValue("to"); // 指向下一節點名稱
                    String dft = attr.getValue("default"); // 是否為預設路徑
                    // 如果沒有指定路徑名稱，則以下一節點名做為路徑名稱
                    tName = (tName != null) ? tName : to;
                    node.getTransitions().put(tName, to);
                    if ("true".equalsIgnoreCase(dft)) {
                        node.setDefaultTransition(tName);
                    }
                    // add by fantasy 2011/06/20
                    if (to != null) {
                        node.getTransitionAttr().put(to, attr2Map(attr)); // 節點Transition屬性
                    }
                    if (tName != null) {
                        node.getTransitionAttr().put(tName, attr2Map(attr)); // 節點Transition屬性
                    }

                    // add by fantasy 2011/09/13
                    action = attr.getValue("name");
                }
                // add button by fantasy 2011/09/09
                else if (NodeType.BUTTON.value.equalsIgnoreCase(name)) { // 執行路徑
                    try {
                        String bName = attr.getValue("name"); // 路徑名稱
                        if (bName != null) {
                            Map<String, String> map = attr2Map(attr);
                            if (action != null) {
                                map.put("action", action);
                            }
                            node.getButtons().put(bName.trim(), map); // 節點Transition屬性
                        }
                        action = null; // 清除action
                    } catch (Exception e) {
                        log.error("parse button error!");
                    }

                } else if (NodeType.CODE.value.equalsIgnoreCase(name)) {
                    ((ScriptNode) node).setCode("");
                } else {
                    action = null; // 清除action add by fantasy 2011/09/13
                    if (NodeType.STATE.value.equalsIgnoreCase(name)) {
                        node = new StateNode();
                        ((StateNode) node).setRole(attr.getValue("role")); // 可執行此節點的群組
                    } else if (NodeType.DECISION.value.equalsIgnoreCase(name)) {
                        node = new DecisionNode();
                        String expr = attr.getValue("expr");
                        if (expr != null) {
                            ((DecisionNode) node).setExpression(expr); // 節點用以執行的OGNL表達式
                        }
                    } else if (NodeType.FORK.value.equalsIgnoreCase(name)) {
                        node = new ForkNode();
                    } else if (NodeType.JOIN.value.equalsIgnoreCase(name)) {
                        node = new JoinNode();
                    } else if (NodeType.SUB.value.equalsIgnoreCase(name)) {
                        node = new SubProcessNode();
                        ((SubProcessNode) node).setDefinition(attr.getValue("sub-process-id")); // 子流程的流程定義名稱
                    } else if (NodeType.START.value.equalsIgnoreCase(name)) {
                        node = new StartNode();
                        def.setStartNode(node);
                    } else if (NodeType.END.value.equalsIgnoreCase(name)) {
                        node = new EndNode();
                        def.getEndNodes().add(node);
                    } else if (NodeType.SCRIPT.value.equalsIgnoreCase(name)) {
                        node = new ScriptNode();
                    } else {
                        node = new TaskNode();
                    }
                    node.setName(attr.getValue("name")); // 節點名稱
                    node.setTagName(name); // 節點名稱
                    node.setStatus(attr.getValue("status")); // 狀態
                    node.setLog(attr.getValue("log")); // LOG
                    node.setAttr(attr2Map(attr)); // 節點屬性

                    // 節點名稱(流程狀態)必須是唯一的
                    if (def.getNodes().containsKey(node.getName())) {
                        throw new RuntimeException("流程節點名稱重覆! [" + node.getName() + "]");
                    } else {
                        def.getNodes().put(node.getName(), node);
                    }
                }
            }
        });
        // 將節點定義設定為不可變
        checkSubProcess(def);
        def.setNodes(Collections.unmodifiableMap(def.getNodes()));
        return def;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowDefinitionParser#parse(java.io.InputStream)
     */
    @Override
    public FlowDefinition parse(InputStream stream) throws IOException, SAXException {
        return parse(new InputSource(stream));
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowDefinitionParser#parse(java.lang.String)
     */
    @Override
    public FlowDefinition parse(String xml) throws IOException, SAXException {
        return parse(new ByteArrayInputStream(xml.getBytes()));
    }

    /**
     * 如果subprocess的前一節點如果為decision或fork 則在中間安插一個不中斷的節點類型(ex: START)，做為停滯用途
     * 
     * @param def
     *            {@link tw.com.jcs.flow.core.FlowDefinitionImpl}
     */
    private void checkSubProcess(FlowDefinitionImpl def) {
        // 如果subprocess的前一節點如果為decision或fork
        // 則在中間安插一個不中斷的節點類型(ex: START)，做為停滯用途
        Map<String, FlowNode> nodes = def.getNodes();
        List<FlowNode> subNodes = new LinkedList<FlowNode>();
        for (FlowNode node : nodes.values()) {
            if (node instanceof SubProcessNode) {
                subNodes.add(node);
            }
        }
        for (FlowNode node : subNodes) {
            String name = node.getName();
            StartNode stop = new StartNode();
            stop.setName("Start " + name);
            stop.getTransitions().put(name, name);
            for (FlowNode anotherNode : nodes.values()) {
                boolean found = false;
                for (Entry<String, String> entry : anotherNode.getTransitions().entrySet()) {
                    if (entry.getValue().equals(name)) {
                        entry.setValue(stop.getName());
                        nodes.put(stop.getName(), stop);
                        found = true;
                        break;
                    }
                }
                if (found)
                    break;
            }
        }
    }

    /**
     * attr to map
     * 
     * @param attr
     * @return
     */
    private Map<String, String> attr2Map(Attributes attr) {
        Map<String, String> map = new LinkedHashMap<String, String>();
        for (int i = 0; i < attr.getLength(); i++) {
            String key = attr.getQName(i);
            String value = attr.getValue(i);
            if (key != null && value != null) {
                map.put(key, value);
            }
        }
        return map;
    }
}
