package com.mega.eloan.lms.lms.handler.file;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.MegaErrorResult;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocFileService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FileUploadHandler;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 評等對照 上傳
 * </pre>
 * 
 * @since 2016/4/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2016/4/12,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1025v05fileuploadhandler")
public class LMS1025V05FileUploadHandler extends FileUploadHandler {


	private static final String LMS1025V05MAINID = "lms1025v05docfile000000000000000";
	

	@Autowired
	DocFileService fileService;

	@Override
	public IResult afterUploaded(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		MultipartFile uFile = params.getFile(params.getString("fieldId"));

		String uid = params.getString(EloanConstants.MAIN_UID);
		
		boolean isGetImgDimension = params.getBoolean("getImgDimension");
		String sysId = params.getString("sysId", fileService.getSysId());

		// 設定上傳檔案資訊
        String fileName = uFile.getOriginalFilename();
		String fieldId = Util.trim(params.getString("fieldId"));
		
		if (params.containsKey("fileSize")) {
			if (uFile.getSize() > params.getLong("fileSize", 1048576)) {
				// EFD0063=ERROR|上送的檔案已超過$\{fileSize\}M的限制大小，無法執行上傳動作。|
				Map<String, String> msg = new HashMap<String, String>();
				msg.put("fileSize",
						CapMath.divide(params.getString("fileSize"), "1048576")); // 1M*1024*1024
				MegaErrorResult result = new MegaErrorResult();
				result.putError(params, new CapMessageException(RespMsgHelper.getMessage("EFD0063", msg), getClass()));
				return result;
			}
		}

		DocFile docFile = new DocFile();
		docFile.setBranchId(user.getUnitNo());
		docFile.setContentType(uFile.getContentType());
		docFile.setMainId(LMS1025V05MAINID);
		docFile.setPid(CapString.isEmpty(uid) ? null : uid);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(fieldId);
		docFile.setDeletedTime(null);

		docFile.setSrcFileName(fileName);
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(sysId);
		docFile.setFileSize(uFile.getSize());
		docFile.setFileDesc(user.getUserId());// 儲存上傳者是誰

		if (params.containsKey("deleteDup") && params.getAsBoolean("deleteDup")) {
			List<DocFile> dupFiles = fileService.findByIDAndName(LMS1025V05MAINID,
					fieldId, "");
			if (!CollectionUtils.isEmpty(dupFiles)) {
				for (DocFile dupFile : dupFiles) {
					fileService.delete(dupFile.getOid());
				}
			}
		}
		// this.checkParams(docFile);
		InputStream is = null;
		String fileKey = "";
		int[] dimension = { -1, -1 };
		try {
			// 設定上傳檔案處理物件
			is = uFile.getInputStream();
			docFile.setData(IOUtils.toByteArray(is));
			// 儲存上傳檔案
			fileKey = fileService.save(docFile);

			// 若是圖檔取得其尺寸
			if (isGetImgDimension) {
				dimension = fileService.getImageDimension(docFile);
			}
		} catch (IOException e) {
			logger.error(e.getMessage(), e);
			throw new CapMessageException("file IO ERROR", getClass());
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					logger.debug("inputStream close Error", getClass());
				}
			}

		}

		return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
				.set("fileKey", fileKey).set("imgWidth", dimension[0])
				.set("imgHeight", dimension[1]);
	}
}
