package com.mega.eloan.lms.crs.pages;


import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.panels.RetrialPtMgrIdPanel;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.panels.LMS2411S01Panel;
import com.mega.eloan.lms.crs.panels.LMS2411S02Panel;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel201907GA;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel201907GB;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel201907NA;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel201907NB;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel202008G;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel202008GA;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel202008GB;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel202008NA;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel202008NB;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel202008P;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel202105NA;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel202105NB;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel202204NA;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel202204NB;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel202209NA;
import com.mega.eloan.lms.crs.panels.LMS2411S03Panel202209NB;
import com.mega.eloan.lms.crs.panels.LMS2411S03PanelA;
import com.mega.eloan.lms.crs.panels.LMS2411S03PanelB;
import com.mega.eloan.lms.crs.panels.LMS2411S03PanelS;
import com.mega.eloan.lms.crs.panels.LMS2411S04Panel;
import com.mega.eloan.lms.crs.panels.LMS2411S04PanelS;
import com.mega.eloan.lms.crs.panels.LMS2411S05Panel;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Controller
@RequestMapping("/crs/lms2411m01/{page}")

public class LMS2411M01Page extends AbstractEloanForm {

	@Autowired
	RetrialService retrialService;

    @Autowired
    SysParameterService sysParameterService;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS2411M01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);

		/*
		 * 北一區 和 其它營運中心的 flow 不同
		 */
		// 覆審組[營運中心+007/201+900]
		// 分行端[分行+900]

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A meta = null;
		C240M01A c240m01a = null;

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean showC241M01C_N = false;
		boolean showC241M01C_G_befV202008 = false;
		boolean showC241M01C_G_aftV202008 = false;
		boolean showC241M01C_P = false;
		boolean showC241M01C_S = false;
		boolean showC241M01C_H = false;
		boolean showC241M01G = false;

		boolean showCorrectP_SellerBuyerInfo = false;
		boolean showRetrialStaff_befEndPtA = false;// 編製完成,且上傳
		boolean showRetrialStaff_befEndPtB = false;// 只上傳
		boolean showRetrialStaff_AtEndPt = false;
		boolean showLoadLms8000v04 = false;
		// J-110-0505_05097_B1001 Web
		// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
		boolean _btnPrintLatestL140M01A = false;

		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC241M01A_oid(mainOid);
			c240m01a = retrialService.findC240M01A_C241M01A(meta);
			if (meta != null && c240m01a != null) {
				if (Util.equals(RetrialDocStatusEnum.已產生覆審名單報告檔.getCode(),
						c240m01a.getDocStatus())
						&& Util.equals("Y", meta.getRetrialYN())) {
					if (CrsUtil.isCaseN(meta)
							|| CrsUtil.isCaseG___N_Detail(meta)) {
						showC241M01C_N = true;
					} else if (CrsUtil.isCaseG_Parent(meta)) {
						if (CrsUtil.docKindG_since_V202008(meta)) {
							showC241M01C_G_aftV202008 = true;
						} else {
							showC241M01C_G_befV202008 = true;
						}
					} else if (CrsUtil.isCaseP(meta)) {
						showC241M01C_P = true;
					} else if (CrsUtil.isCaseS(meta)) {
						showC241M01C_S = true;
					} else if (CrsUtil.isCaseH(meta)
							|| CrsUtil.isCaseG___H_Detail(meta)) {
						showC241M01C_H = true;
					}

					if (CrsUtil.showC241M01G(user)) { // J-110-0308
														// 分行及三大部隱藏「考評表」頁籤
						showC241M01G = true;
					}

					if (Util.isNotEmpty(meta.getDocStatus())
							&& CrsUtil.isRetrialTeam(user)) {
						// 覆審組人員才有的權限
						if (Util.equals(RetrialDocStatusEnum.已覆核已核定.getCode(),
								meta.getDocStatus())) {
							showRetrialStaff_AtEndPt = true;
							// 已覆核已核定, 仍可儲存後上傳
							if (CrsUtil.canSaveC241M01A(user, meta)) {
								showRetrialStaff_befEndPtB = true;
							}
						} else {
							if (CrsUtil.isCaseS(meta)) {
								if (CrsUtil.is_flowClass_throughBr(sysParameterService,c240m01a)) {
									showRetrialStaff_befEndPtB = true;
								} else {
									// 非931
									if (Util.equals(
											RetrialDocStatusEnum.區中心_編製中
													.getCode(), meta
													.getDocStatus())) {
										showRetrialStaff_befEndPtA = true;
									}
								}
							} else {
								/*
								 * 931 在到分行前 , click 上傳=> 只上傳控制檔, 不變更 docStatus
								 * 分行傳回後, click 上傳 => 變更 docStatus 為「結案」
								 * 
								 * 非931 在到分行前, click 上傳 => 要寫簽章欄, 且變更 docStatus
								 * 為「結案」
								 */
								if (CrsUtil.is_flowClass_throughBr(sysParameterService,c240m01a)) {
									if (Util.equals(RetrialDocStatusEnum.已覆核未核定
											.getCode(), meta.getDocStatus())) {
										showRetrialStaff_befEndPtA = true;
									} else {
										showRetrialStaff_befEndPtB = true;
									}
								} else {
									showRetrialStaff_befEndPtA = true;
								}
							}
						}
					}
				}

				if (CrsUtil.isCaseP(meta)) {
					showCorrectP_SellerBuyerInfo = true;
				}

				showLoadLms8000v04 = CrsUtil.isRetrialTeam(user);

				// J-110-0505_05097_B1001 Web
				// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
				// 開啟列印額度明細表/批覆書
				_btnPrintLatestL140M01A = CrsUtil.isRetrialTeam(user);

			}
		}

		// 2022/04/18 授審處連喬凱來電  分處對覆審報告表有回頭打考評表之需求
		// 1. 「已覆核已核定」之覆審報告表可以退為「已覆核未核定」修改_限制為有考評表且有傳送至分行的覆審報告表
		// 2. 不限上傳者本人，任何人都可以修改
		boolean _btnSave = false;
		_btnSave = CrsUtil.canSaveC241M01A(user, meta);
		if (!_btnSave){
			if(CrsUtil.showC241M01G(user)){// 有考評表頁籤
				String docStatus = Util.trim(meta.getDocStatus());
				if (Util.equals(docStatus, RetrialDocStatusEnum.已覆核未核定.getCode())) {
					_btnSave = true;
				}
			}
		}
		addAclLabel(model, new AclLabel("_btnSave", AuthType.Modify, params,
				_btnSave));

		if (c240m01a != null && CrsUtil.is_flowClass_throughBr(sysParameterService,c240m01a)) {
			if (CrsUtil.isCaseS(meta)) {
				addAclLabel(model, new AclLabel("_btnAREA_EDITING", AuthType.Modify, params,
						false));

				addAclLabel(model, new AclLabel("_btnAREA_EDITING_S", params,
						getDomainClass(), AuthType.Modify,
						RetrialDocStatusEnum.區中心_編製中));
				// ~~~~~~
				addAclLabel(model, new AclLabel("_btnAREA_WAIT_APPROVE", AuthType.Modify,
						params, false));
				addAclLabel(model, new AclLabel("_btnAREA_WAIT_APPROVE_S", params,
						getDomainClass(), AuthType.Modify,
						RetrialDocStatusEnum.區中心_待覆核));
			} else {
				addAclLabel(model, new AclLabel("_btnAREA_EDITING", params, getDomainClass(),
						AuthType.Modify,false,RetrialDocStatusEnum.區中心_編製中));
				addAclLabel(model, new AclLabel("_btnAREA_EDITING_S", AuthType.Modify, params,
						false));
				// ~~~~~~
				addAclLabel(model, new AclLabel("_btnAREA_WAIT_APPROVE", params,
						getDomainClass(), AuthType.Modify,
						RetrialDocStatusEnum.區中心_待覆核));
				addAclLabel(model, new AclLabel("_btnAREA_WAIT_APPROVE_S", AuthType.Modify,
						params, false));
			}
		} else {
			addAclLabel(model, new AclLabel("_btnAREA_EDITING", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnAREA_EDITING_S", AuthType.Modify, params,
					false));
			// ~~~~~~
			addAclLabel(model, new AclLabel("_btnAREA_WAIT_APPROVE", AuthType.Modify, params,
					false));
			addAclLabel(model, new AclLabel("_btnAREA_WAIT_APPROVE_S", AuthType.Modify,
					params, false));
		}

		addAclLabel(model, new AclLabel("_btnBRANCH_EDITING", params, getDomainClass(),
				AuthType.Modify,false,RetrialDocStatusEnum.編製中));

		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
				AuthType.Accept,false,RetrialDocStatusEnum.待覆核));
		// 退回「覆審單位」覆審人員
		addAclLabel(model, new AclLabel("_btnBack_A_L1", params, getDomainClass(),
				AuthType.Modify,false, RetrialDocStatusEnum.區中心_待覆核,
				RetrialDocStatusEnum.編製中,RetrialDocStatusEnum.已覆核未核定));
		// 退回「受檢單位」經辦
		addAclLabel(model, new AclLabel("_btnBack_B_L1", params, getDomainClass(),
				AuthType.Modify,false,RetrialDocStatusEnum.已覆核未核定));

		model.addAttribute("showRetrialStaff_befEndPtA",
				showRetrialStaff_befEndPtA);
		model.addAttribute("showRetrialStaff_befEndPtB",
				showRetrialStaff_befEndPtB);
		model.addAttribute("showRetrialStaff_AtEndPt",
				showRetrialStaff_AtEndPt);
		model.addAttribute("showC241M01C_N", showC241M01C_N);
		model.addAttribute("showC241M01C_G",
				showC241M01C_G_befV202008);
		model.addAttribute("showC241M01C_G_aftV202008",
				showC241M01C_G_aftV202008);
		model.addAttribute("showC241M01C_P", showC241M01C_P);
		model.addAttribute("showC241M01C_S", showC241M01C_S);
		model.addAttribute("showC241M01C_H", showC241M01C_H);
		model.addAttribute("showTitle_NG", showC241M01C_N
				|| showC241M01C_G_befV202008);
		model.addAttribute("showTitle_G_aftV202008",
				showC241M01C_G_aftV202008);
		model.addAttribute("showTitle_S", showC241M01C_S);
		model.addAttribute("showTitle_P", showC241M01C_P);
		model.addAttribute("showTitle_H", showC241M01C_H);
		model.addAttribute("showC241M01G", showC241M01G);
		model.addAttribute("showCorrectP_SellerBuyerInfo",
				showCorrectP_SellerBuyerInfo);
		model.addAttribute("showLoadLms8000v04", showLoadLms8000v04);
		// J-110-0505_05097_B1001 Web
		// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
		model.addAttribute("_btnPrintLatestL140M01A",
				_btnPrintLatestL140M01A);
		//J-111-0554 配合授審處增進管理效益，修改相關功能程式
		model.addAttribute("_btnPrintCollSet", 
				_btnPrintLatestL140M01A);
		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page, meta);
		model.addAttribute("tabID", tabID);
		panel.processPanelData(model, params);

		new RetrialPtMgrIdPanel("divRetrialPtMgrIdPanel").processPanelData(model, params);
	}

	// 頁籤
	private Panel getPanel(int index, C241M01A meta) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS2411S01Panel(TAB_CTX, meta, true);
			break;
		case 2:
			panel = new LMS2411S02Panel(TAB_CTX, true, meta);
			// ~~~~~~
			renderJsI18N(LMS2411M01Page.class);
			break;
		case 3:
			if (Util.equals(meta.getRptId(), CrsUtil.V_N_202209NA)) {
				panel = new LMS2411S03Panel202209NA(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_202209NB)) {
				panel = new LMS2411S03Panel202209NB(TAB_CTX, meta, true);			
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_202204NA)) {
				panel = new LMS2411S03Panel202204NA(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_202204NB)) {
				panel = new LMS2411S03Panel202204NB(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_202105NA)) {
				panel = new LMS2411S03Panel202105NA(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_202105NB)) {
				panel = new LMS2411S03Panel202105NB(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_202008NA)) {
				panel = new LMS2411S03Panel202008NA(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_202008NB)) {
				panel = new LMS2411S03Panel202008NB(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_202008GA)) {
				panel = new LMS2411S03Panel202008GA(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_202008GB)) {
				panel = new LMS2411S03Panel202008GB(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_P_202008)) {
				panel = new LMS2411S03Panel202008P(TAB_CTX, meta, true); // P-價金履保_Ver202008P
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_G_202008)) {
				panel = new LMS2411S03Panel202008G(TAB_CTX, meta, true); // G-團貸母戶_Ver202008G
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_201907NA)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201909NA)) {
				panel = new LMS2411S03Panel201907NA(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_201907NB)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201909NB)) {
				panel = new LMS2411S03Panel201907NB(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_201907GA)) {
				panel = new LMS2411S03Panel201907GA(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_201907GB)) {
				panel = new LMS2411S03Panel201907GB(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_201805A2)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201805A)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201707A)) {
				panel = new LMS2411S03PanelA(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_N_201805B2)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201805B)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201707B)) {
				panel = new LMS2411S03PanelB(TAB_CTX, meta, true);
			} else if (Util.equals(meta.getRptId(), CrsUtil.V_S_201902) || Util.equals(meta.getRptId(), CrsUtil.V_S_202204)) {
				panel = new LMS2411S03PanelS(TAB_CTX, meta, true);
			} else {
				panel = new LMS2411S03Panel(TAB_CTX, meta); // P-履保 or G-團貸母戶
			}
			break;
		case 4:
			if (Util.equals(meta.getRptId(), CrsUtil.V_S_201902) || Util.equals(meta.getRptId(), CrsUtil.V_S_202204)) {
				panel = new LMS2411S04PanelS(TAB_CTX, meta, true);
			} else {
				panel = new LMS2411S04Panel(TAB_CTX, true);
			}
			break;
		case 5:
			panel = new LMS2411S05Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS2411S01Panel(TAB_CTX, meta, true);
			break;
		}
		renderJsI18N(LMS2411M01Page.class);
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C241M01A.class;
	}
}
