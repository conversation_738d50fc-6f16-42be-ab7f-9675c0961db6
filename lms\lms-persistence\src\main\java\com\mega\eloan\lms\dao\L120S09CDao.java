/* 
 * L120S09CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S09C;

/** 洗錢資恐態樣檢核表 **/
public interface L120S09CDao extends IGenericDao<L120S09C> {

	L120S09C findByOid(String oid);
	
	List<L120S09C> findByMainId(String mainId);

	List<L120S09C> findByIndex01(String mainId);
}