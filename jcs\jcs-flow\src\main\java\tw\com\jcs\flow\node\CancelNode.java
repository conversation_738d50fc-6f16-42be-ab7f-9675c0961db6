package tw.com.jcs.flow.node;

import java.util.Date;

import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowInstanceImpl;

/**
 * <pre>
 * <h1>流程節點(取消)</h1>
 * </pre>
 * 
 * @since 2023年1月9日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月9日
 *          </ul>
 */
public abstract class CancelNode {

    /**
     * 流程(取消)
     * 
     * @param instance
     */
    public static void cancel(FlowInstanceImpl instance) {
        instance.handle(FlowInstance.CANCEL);

        Date endTime = new Date();
        instance.setEndTime(endTime);
        instance.saveSequence();

        instance.setSeq(instance.getSeq() + 1);
        instance.setState("CANCEL");
        instance.setBeginTime(endTime);
        instance.setEndTime(endTime);
        instance.saveSequence();

        if (instance.getParentInstanceId() != null) {
            // 流程已取消，如果有父流程，則從父流程中移除子流程
            FlowInstanceImpl parent = (FlowInstanceImpl) instance.getEngine().getPersistence().getInstance(instance.getParentInstanceId());

            String state = parent.removeSubInstance(instance.getId());

            // 如果父流程是sub-process出來的，應該next
            if (parent.getDefinition().getNodes().get(state) instanceof SubProcessNode) {
                parent.next();
            }
        } else {
            instance.getEngine().getPersistence().moveToHistory(instance.getId());
        }
    }

}
