package com.mega.eloan.lms.base.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * 定義外層的 div
 * 
 * 
 * 
 * [基本資料 | 服務單位 | 償債能力 | 配偶資料 | 相關查詢資料 ]會依企金戶、消金戶不同
 * 去 load 不同的 page( extends AbstractOverSeaCLSPage)
 * ───> 在 dynamic load page 中定義 form：overSeaCLSPageForm
 * 
 */
public class OverSeaCLSOuterPanel extends Panel {
	
	private static final long serialVersionUID = -2326463663499418856L;

	public OverSeaCLSOuterPanel(String id) {
		super(id);
	}
}
