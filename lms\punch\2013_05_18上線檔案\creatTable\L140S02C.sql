---------------------------------------------------------
-- LMS.L140S02C 分段利率主檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140S02C;
CREATE TABLE LMS.L140S02C (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	SUBMITRATE    DECIMAL(6,4) ,
	PAYNUM        DECIMAL(3,0) ,
	PREDSCR       VARCHAR(3072),
	TAXRATE       DECIMAL(7,5) ,
	INTWAY        CHAR(1)      ,
	RINTWAY       CHAR(1)      ,
	DECFLAG       CHAR(1)      ,
	DESC          VARCHAR(3087),
	ISINPUTDESC   CHAR(1)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140S02C PRIMARY KEY(OID)
) IN EL_DATA_8KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140S02C01;
CREATE UNIQUE INDEX LMS.XL140S02C01 ON LMS.L140S02C   (MAINID, SEQ);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140S02C IS '分段利率主檔';
COMMENT ON LMS.L140S02C (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQ           IS '序號', 
	SUBMITRATE    IS '首段適用利率', 
	PAYNUM        IS '已還期數', 
	PREDSCR       IS '前期利率說明',
	TAXRATE       IS '扣稅負擔值',
	INTWAY        IS '計息方式', 
	RINTWAY       IS '收息方式', 
	DECFLAG       IS '省息遞減',
	DESC          IS '其他', 
	ISINPUTDESC   IS '是否輸入其他', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
