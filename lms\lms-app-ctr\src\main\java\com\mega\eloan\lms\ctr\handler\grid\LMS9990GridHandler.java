/* 
 *  LMS9990GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.handler.grid;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.ctr.constants.CtrConstants;
import com.mega.eloan.lms.ctr.service.LMS9990Service;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.model.C999M01A;
import com.mega.eloan.lms.model.C999S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L999M01A;
import com.mega.eloan.lms.model.L999M01C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 企金(個金)約據書-Grid
 * </pre>
 * 
 * @since 2012/02/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/02/07,ICE,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9990gridhandler")
public class LMS9990GridHandler extends AbstractGridHandler {

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branchService;

	@Resource
	LMS9990Service lms9990Service;

	@Resource
	LMS1205Service lms1205Service;

	@Resource
	DocFileService docFileService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	ProdService prodService;

	/**
	 * 查詢簽報書(條件為custId,dupNo,typCd)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryReportList(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));
		String docType = Util.nullToSpace(params.getString("docType"));
		// String typCd = Util.nullToSpace(params.getString("typCd"));
		// 取得身分證字號
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		// 取得重複編號
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (Util.isNotEmpty(docType)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		}
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				CreditDocStatusEnum.海外_已核准.getCode());
		Page<? extends GenericBean> page = lms1205Service.findPage(
				L120M01A.class, pageSetting);

		List<L120M01A> list = (List<L120M01A>) page.getContent();
		for (L120M01A l120m01a : list) {
			l120m01a.setCaseNo(Util.toSemiCharString(l120m01a.getCaseNo()));
		}

		CapGridResult result = new CapGridResult(list, page.getTotalRow());
		return result;
	}

	/**
	 * 查詢簽報書(條件為custId,dupNo,typCd)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL999M01AList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String srcMainId = Util.nullToSpace(params.getString("srcMainId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "srcMainId",
				srcMainId);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms9990Service.findPage(
				L999M01A.class, pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		if (page.getContent() != null) {
			for (L999M01A model : (List<L999M01A>) page.getContent()) {
				model.setUid(model.getContractType());
			}
		}

		Map<String, IFormatter> dataReformatter = new LinkedHashMap<String, IFormatter>();
		dataReformatter.put("contractType", new CodeTypeFormatter(
				codeTypeService, CtrConstants.CodeType.企金約據書種類)); // codeType格式化
		// dataReformatter.put("userid",
		// new
		// UserNameFormatter(userInfoService));
		// //使用者名稱格式化
		dataReformatter.put("updater", new UserNameFormatter(userInfoService)); // 使用者名稱格式化
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 查詢個人資訊(條件為custId)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult listByNoDupNo(ISearch pageSetting,
			PageParameters params) throws CapException {
		String custId = Util.nullToSpace(params.getString("custId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);// 取得身分證字號
		pageSetting.setDistinct(true);
		Page<Map<String, Object>> page = lms9990Service.listDupNoToCustId(
				custId, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢連保人清單
	 * 
	 * @param pageSetting
	 *            mainId 文件編號
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL999M01CList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID, ""));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		Page<? extends GenericBean> page = lms9990Service.findPage(
				L999M01C.class, pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		if (page.getContent() != null) {
			for (L999M01C model : (List<L999M01C>) page.getContent()) {
				model.setCustId(model.getCustId() + " " + model.getDupNo());
			}
		}

		return result;
	}

	/**
	 * 查詢檔案上傳的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryfile(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString("srcMainId"));
		String fieldId = Util.trim(params.getString("fieldId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				"C999M01A".equals(fieldId) ? fieldId : "L999M01A");
		Page<DocFile> page = docFileService.readToGrid(pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢簽報書(條件為custId,dupNo,typCd)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC999M01AList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String srcMainId = Util.nullToSpace(params.getString("srcMainId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "srcMainId",
				srcMainId);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms9990Service.findPage(
				C999M01A.class, pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		if (page.getContent() != null) {
			for (C999M01A model : (List<C999M01A>) page.getContent()) {
				model.setUid(model.getContractType());
				model.setContractKind(
						Util.isEmpty(Util.trim(model.getContractKind()))?
								getBContractKind(Util.trim(model.getContractType2())):
						codeTypeService.getDescOfCodeType(
						CtrConstants.CodeType.個金約據書類型,
						Util.trim(model.getContractKind())));
				// model.setUpdater(userInfoService.getUserName(Util.trim(model.getUpdater())));
			}
		}

		Map<String, IFormatter> dataReformatter = new LinkedHashMap<String, IFormatter>();
		// dataReformatter.put("contractKind", new
		// CodeTypeFormatter(codeTypeService,"lms9990m01c_contractKind"));
		dataReformatter.put("contractType", new CodeTypeFormatter(
				codeTypeService, CtrConstants.CodeType.個金約據書種類)); // codeType格式化
																	// dataReformatter.put("userid",
																	// new
																	// UserNameFormatter(userInfoService));
																	// //使用者名稱格式化
		dataReformatter.put("updater", new UserNameFormatter(userInfoService)); // 使用者名稱格式化
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 取得政策性留學貸款名稱(契約書除外)
	 * @param contractType2
	 * @return
	 */
	private String getBContractKind(String contractType2){
		if("W32".equals(contractType2)){
			return "兆銀總授管字第5054號-附件9(政策留貸-切結書)";
		}else
		if("W33".equals(contractType2)){
			return "兆銀總授管字第5054號-附件11(政策留貸延期清償申請書暨切結書)";
		}else
		if("W34".equals(contractType2)){
			return "兆銀總授管字第5054號-附件13(政策留貸-修畢碩士學位後繼續修讀博士延長貸款期限及寬限期申請書)";
		}else
		if("W35".equals(contractType2)){
			return "兆銀總授管字第5054號-附件14(政策留貸-授權書)";
		}else
		if("W36".equals(contractType2)){
			return "個金2686-附件4通知書-留學生本人為申請人用";
		}else
		if("W37".equals(contractType2)){
			return "個金2686-附件7通知書-留學生本人為申請人用(英文)";
		}else
		if("W38".equals(contractType2)){
			return "教育部補助留學生就學貸款申請書每人信用查詢費300元";
		}else
		if("W39".equals(contractType2)){
			return "符合教育部採認規定之國外大學院校及其他事項確認查詢單";
		}else
		if("W40".equals(contractType2)){
			return "無法於寬限期內完成學業延期清償申請書";
		}else{
			return contractType2;
		}
	}
	
	/**
	 * 查詢個金約據書產品種類
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC999s01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		boolean isThick = params.getBoolean("isThick");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		if (isThick) {
			// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
			// "itemNo", Util.parseInt(CtrConstants.Book9990.ItemNo.刪除));
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
					"deletedTime", null);
		}
		Page<? extends GenericBean> page = lms9990Service.findPage(
				C999S01A.class, pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, String> prodMap = prodService.getProdKindName();
		Map<String, String> subMap = prodService.getSubCode();
		if (page.getContent() != null) {
			for (C999S01A model : (List<C999S01A>) page.getContent()) {
				// 產品種類名稱轉換
				model.setProdKind(prodMap.get(Util.trim(model.getProdKind())));				
				// 科目名稱轉換
				model.setSubjCode(subMap.get(Util.trim(model.getSubjCode())));
				// 幣別轉換
				model.setLoanCurr(codeTypeService.getDescOfCodeType(
						"Common_Currcy", Util.trim(model.getLoanCurr())));
				model.setProdKind(Util.trim(model.getProdKind()));
			}
		}
		return result;
	}
}
