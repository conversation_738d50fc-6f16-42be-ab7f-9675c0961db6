/* 
 * C241M01E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 覆審報告表簽章欄檔 **/
@Entity
@Table(name = "C241M01E", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "branchType", "branchId", "staffNo",
		"staffJob" }))
public class C241M01E extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** mainId **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 單位類型
	 * <p/>
	 * 1. 受檢單位<br/>
	 * 2. 覆審單位
	 */
	@Column(name = "BRANCHTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String branchType;

	/** 單位代碼 **/
	@Column(name = "BRANCHID", length = 3, columnDefinition = "CHAR(3)")
	private String branchId;

	/** 行員代碼 **/
	@Column(name = "STAFFNO", length = 6, columnDefinition = "CHAR(6)")
	private String staffNo;

	/**
	 * 人員職稱
	 * <p/>
	 * L1. 分行經辦<br/>
	 * (授管處/營運中心)<br/>
	 * L2. 帳戶管理員<br/>
	 * L3. 分行授信/覆核主管<br/>
	 * (母行)<br/>
	 * L4. 分行覆核主管<br/>
	 * (母行/授管處/營運中心)<br/>
	 * L5. 單位/授權主管<br/>
	 * (母行)<br/>
	 * L6. 分行單位主管<br/>
	 * 經副襄理<br/>
	 * 正/副營運長<br/>
	 * (母行/授管處/營運中心)
	 */
	@Column(name = "STAFFJOB", length = 2, columnDefinition = "CHAR(2)")
	private String staffJob;
	
	/**
	 * 行員姓名
	 */
	@Column(name = "STAFFNAME", length = 30, columnDefinition = "VARCHAR(30)")
	private String staffName;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得單位類型
	 * <p/>
	 * 1. 受檢單位<br/>
	 * 2. 覆審單位
	 */
	public String getBranchType() {
		return this.branchType;
	}

	/**
	 * 設定單位類型
	 * <p/>
	 * 1. 受檢單位<br/>
	 * 2. 覆審單位
	 **/
	public void setBranchType(String value) {
		this.branchType = value;
	}

	/** 取得單位代碼 **/
	public String getBranchId() {
		return this.branchId;
	}

	/** 設定單位代碼 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/** 取得行員代碼 **/
	public String getStaffNo() {
		return this.staffNo;
	}

	/** 設定行員代碼 **/
	public void setStaffNo(String value) {
		this.staffNo = value;
	}

	/**
	 * 取得人員職稱
	 * <p/>
	 * L1. 分行經辦<br/>
	 * (授管處/營運中心)<br/>
	 * L2. 帳戶管理員<br/>
	 * L3. 分行授信/覆核主管<br/>
	 * (母行)<br/>
	 * L4. 分行覆核主管<br/>
	 * (母行/授管處/營運中心)<br/>
	 * L5. 單位/授權主管<br/>
	 * (母行)<br/>
	 * L6. 分行單位主管<br/>
	 * 經副襄理<br/>
	 * 正/副營運長<br/>
	 * (母行/授管處/營運中心)
	 */
	public String getStaffJob() {
		return this.staffJob;
	}

	/**
	 * 設定人員職稱
	 * <p/>
	 * L1. 分行經辦<br/>
	 * (授管處/營運中心)<br/>
	 * L2. 帳戶管理員<br/>
	 * L3. 分行授信/覆核主管<br/>
	 * (母行)<br/>
	 * L4. 分行覆核主管<br/>
	 * (母行/授管處/營運中心)<br/>
	 * L5. 單位/授權主管<br/>
	 * (母行)<br/>
	 * L6. 分行單位主管<br/>
	 * 經副襄理<br/>
	 * 正/副營運長<br/>
	 * (母行/授管處/營運中心)
	 **/
	public void setStaffJob(String value) {
		this.staffJob = value;
	}
	
	/**
	 * 取得行員姓名
	 */	
	public String getStaffName() {
		return this.staffName;
	}
	
	/**
	 * 設定行員姓名
	 */
	public void setStaffName(String value) {
		this.staffName = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
