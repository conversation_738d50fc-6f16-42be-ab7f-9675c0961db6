<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
		<div id="C160M01DThickBox" style="display:none;" >
			<form id="C160M01DForm">
			  	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
			  		<tr>
			  			<td class="hd1"  width="50%"><th:block th:text="#{'C160M01A.custName'}"><!--主要借款人--></th:block>&nbsp;&nbsp;</td>
						<td width="50%">
							<span id="custId" class="field" ></span>
                        	<th:block th:text="#{'C160M01A.dupNo'}"><!--重覆序號--></th:block>：
							<span id="dupNo" class="field" ></span>
	                        (<span id="typCd" class="field text-red"></span>)<span id="custName" class="field" ></span>
						</td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'C160M01A.caseNo'}"><!--案號--></th:block>&nbsp;&nbsp;</td>
						<td><span id="caseNo"></span></td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'C160M01D.waitingItem'}"><!--代辦事項--></th:block>&nbsp;&nbsp;</td>
						<td><span id="waitingItem" ></span></td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'C160M01D.willFinishDate'}"><!--預定補全日--></th:block>&nbsp;&nbsp;</td>
						<td><span id="willFinishDate" ></span></td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'C160M01D.finishDate'}"><!--辦妥日期--></th:block>&nbsp;&nbsp;</td>
						<td><input type="text" id="finishDate" name="finishDate" class="date required" size="10" maxlength="10" ></td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'C160M01D.itemTrace'}"><!--追蹤辦理情形--></th:block>&nbsp;</td>
						<td><textarea id="itemTrace" name="itemTrace" class="required txt_mult" cols="50" rows="6"  maxlengthC="256"></textarea></td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'C160M01D.managerId'}"><!--甲級主管--></th:block>&nbsp;</td>
						<td>
							<select id="managerId" name="managerId" class="required"></select>
						 	<input type="text" id="managerNm" name="managerNm" size="10" maxlength="30" maxlengthC="10" class="required" style="display:none">
						</td>
			  		</tr>
			  	</table>
			</form>
		</div>
		
		<div id="filterBox" style="display:none">
	      	<form id="filterForm" name="filterForm">
	      	<table>
		      <label><b><th:block th:text="#{'C160M01A.inputQuery'}">請輸入欲查詢紀錄</th:block>：</b></label>
		      <tbody>
		        <tr>
		          <td>
		          	<label><input type="radio" name="queryData" value="1" />
		            <th:block th:text="#{'C160M01A.custId'}">主要借款人統編</th:block></label>&nbsp;&nbsp;&nbsp;&nbsp;
		            <label><input type="radio" name="queryData" value="2"/>
		         	<th:block th:text="#{'C160M01A.approveDate'}">核定動用日期範圍</th:block></label>
				  </td>
		        </tr>
		        <tr id="queryDataTr1" class="queryData" >
		        <td >
		        	<input id="custId" name="custId" type="text" size="14" maxlength="10"  class="upText required"/>
		            <span class="text-red">ex:A123456789</span>
				</td>
		        </tr>
		        <tr id="queryDataTr2" class="queryData" >
		          <td >
		          	  <input id="fromDate" name="fromDate" type="text" class="date required" size="8" maxlength="10" />~<input id="endDate"  name="endDate" type="text" class="date required" size="8" maxlength="10" />
		             <span class="text-red">ex:YYYY-MM-DD</span> 
				  </td>
		        </tr>
		      </tbody>
		    </table>
			</form>
		  </div>
	</th:block>
</body>
</html>
