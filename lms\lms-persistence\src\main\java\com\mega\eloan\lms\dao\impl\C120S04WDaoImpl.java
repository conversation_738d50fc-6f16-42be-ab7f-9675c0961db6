/* 
 * C120S04WDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C120S04WDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C120S04W;

/** RPA發查明家事公告細檔 **/
@Repository
public class C120S04WDaoImpl extends LMSJpaDao<C120S04W, String> implements C120S04WDao {

	@Override
	public C120S04W findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120S04W> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C120S04W> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C120S04W findByUniqueKey(String MainId, String branchNo, String empNo, String dataCustomerNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", MainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchNo", branchNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "empNo", empNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "dataCustomerNo", dataCustomerNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}
	
	@Override
	public List<C120S04W> findBy(String mainId, String custId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dataCustomerNo", custId);
		List<C120S04W> list = createQuery(search).getResultList();
		return list;
	}
}