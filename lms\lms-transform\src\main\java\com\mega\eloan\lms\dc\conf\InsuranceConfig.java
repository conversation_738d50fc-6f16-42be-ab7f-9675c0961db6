/* 
 * InsuranceConfig.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2013 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.dc.conf;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.base.DCException;

/**
 * <pre>
 * InsuranceConfig :保險公司代碼對照
 * </pre>
 * 
 * @since 2013/3/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/29,Bang,new
 *          </ul>
 */
public class InsuranceConfig {

	private static Logger logger = LoggerFactory.getLogger(InsuranceConfig.class);

	private static final String CONFIG_FILE = "lmsdc/conf/insurance.properties";

	private static Map<String, String> mapNameVal = new LinkedHashMap<String, String>();
	private static Map<String, String> mapCodeVal = new LinkedHashMap<String, String>();

	private static InsuranceConfig config = new InsuranceConfig();

	public static InsuranceConfig getInstance() {
		return config;
	}

	private InsuranceConfig() {
		try {
			this.load();
		} catch (Exception ex) {
			throw new DCException("讀取insurance.properties設定檔錯誤！", ex);
		}
	}

	private void load() throws Exception {
		Configuration conf = new PropertiesConfiguration(CONFIG_FILE);

		Iterator<String> itor = conf.getKeys();
		String key;
		String value;
		while (itor.hasNext()) {
			key = StringUtils.trimToEmpty(itor.next());
			value = StringUtils.trimToEmpty(conf.getString(key));
			mapNameVal.put(key, value);
			mapCodeVal.put(value, key);

			if (logger.isDebugEnabled()) {
				logger.debug("key=" + key + ",value=" + value);
			}
		}
	}

	/**
	 * 依保險公司名稱取得對應代碼
	 * 
	 * @param ctName
	 * @return
	 */
	public String getInsuranceCode(String twName) {
		return mapCodeVal.get(twName);
	}

	/**
	 * 依代碼取得對應保險公司名稱
	 * 
	 * @param ctCode
	 * @return
	 */
	public String getInsuranceName(String twCode) {
		return mapNameVal.get(twCode);
	}

	/**
	 * 取得所有保險公司代碼
	 * 
	 * @return List
	 */
	public List<String> getInsuranceCodeList() {
		List<String> keyList = new ArrayList<String>(mapCodeVal.values());
		return keyList;
	}

	@Override
	public String toString() {
		StringBuffer str = new StringBuffer();
		for (Map.Entry<String, String> entry : mapNameVal.entrySet()) {
			str.append(entry.getKey()).append("=").append(entry.getValue())
					.append("\n");
		}
		return str.toString();
	}
}
