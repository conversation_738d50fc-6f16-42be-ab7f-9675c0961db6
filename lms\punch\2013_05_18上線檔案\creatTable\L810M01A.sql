---------------------------------------------------------
-- LMS.L810M01A 優惠貸款相關控制表
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L810M01A;
CREATE TABLE LMS.L810M01A (
	OID           CHAR(32)      not null,
	<PERSON>NO          CHAR(3)      ,
	<PERSON><PERSON><PERSON><PERSON>       CHAR(1)      ,
	RPTT<PERSON><PERSON>       CHAR(1)      ,
	R<PERSON><PERSON><PERSON><PERSON>       VARCHAR(120) ,
	SENDED        CHAR(1)      ,
	B<PERSON><PERSON><PERSON><PERSON>       DATE         ,
	ENDDATE       DATE         ,
	RP<PERSON>ID        CHAR(32)     ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L810M01A PRIMARY KEY(OID)
) IN EL_DATA_4KTS
  INDEX IN EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL810M01A01;
CREATE INDEX LMS.XL810M01A01 ON LMS.L810M01A   (BRNO, USETYPE, RPTTYPE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L810M01A IS '優惠貸款相關控制表';
COMMENT ON LMS.L810M01A (
	OID           IS 'oid', 
	BRNO          IS '分行別', 
	USETYPE       IS '統計方式', 
	RPTTYPE       IS '報表類型', 
	RPTNAME       IS '報表名稱', 
	SENDED        IS '傳送至授管處', 
	BGNDATE       IS '開始日期', 
	ENDDATE       IS '截止日期', 
	RPTOID        IS '報表oid', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
