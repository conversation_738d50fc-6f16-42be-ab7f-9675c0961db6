/* 
 *CLS9041M04ervice.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service;

import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;

/**
 * <pre>
 * 每月新增就學貸款明細
 * </pre>
 * 
 * @since 2012/11/01
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/01,Vector Lo,new
 *          </ul>
 */
/* Use MIS-RDB */
public interface CLS9041M06Service {
	
	/* R6(C004M01A */
	
	@SuppressWarnings("rawtypes")
	public Page<? extends GenericBean> findPage(Class clazz, ISearch pageSetting);

	@SuppressWarnings("rawtypes")
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid);

	public void save(GenericBean... entity);

	public void delete(GenericBean... entity);

	@SuppressWarnings("rawtypes")
	public Page findFile(String mainId);
	
	/* MIS-RDB */

	public List<Map<String, Object>> getMisData(String beginDate, String endDate);

	public List<Map<String, Object>> getStuData(String beginDate, String endDate);

	public void deleteFile(String oid);
	
	public Map<String, Object> getLNN192(String LNF192_CUST_ID,String LNF192_CUST_ID_DUP
			,String LNF192_STUDENT_ID,String LNF192_BEG_DATE);

}
