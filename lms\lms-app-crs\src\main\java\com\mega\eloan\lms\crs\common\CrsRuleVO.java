package com.mega.eloan.lms.crs.common;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.CrsVO;
import com.mega.eloan.lms.base.common.LMSUtil;

public class CrsRuleVO {

	static final String Q_R5 = "Q_R5";
	static final String Q_R9 = "Q_R8";
	
	/*
	 	有效額度 和 到期日 有關
	 */
	private java.util.Date date_for_calc_LNF022_ADJ_FAMT;
	private String brNo;
	private String custId;
	private String dupNo;
	private int addedMonth;
	private TreeSet<String> elf491_remomoSet;	
	private String elf491_reportkind;
	
	/**
	 * k-額度序號
	 * v-CrsUtil 內的 [PG_S, PG_L, PG_N, PG_X]
	 */
	private Map<String, String> pg_cntrNo_catByCollAndLNAP_Map;
	//pg 的縮寫：是否十足擔保
	private Map<String, BigDecimal> pg_factBalMap;
	private CrsVO crsVO;
	private Map<String,Boolean> qMap;
	
	private boolean forceFlag;
	private boolean newCustIn3M;
	/**
	 * 可能的來源 [1]帳務已銷戶，但CMS未做「重設定」   <BR/>[2]新資料，尚未在帳務系統建資料 <br/>
	 * CMS.C100S03A 擔保品抵押設定額度資料檔                        
	 */
	private Set<String> cntrNo_onlyIn_C100S03A;
	
	/**
	 * 可能的來源 :帳務為擔保放款科目，但於CMS查無資料(未在撥款前覆核)
	 */
	private Set<String> cntrNo_guaranteeLNAP_butLost_CollMstr;
	
	private TreeSet<String> cntrNo_never_retrialData_in_ELF591H;
	private TreeSet<String> cntrNo_never_retrialData_in_ELF487_HOUSE_or_RMBINS_or_HS_CRE_FG;
	
	public CrsRuleVO(java.util.Date cmpEndDate, String brNo, String custId, String dupNo){
		this.date_for_calc_LNF022_ADJ_FAMT = cmpEndDate;
		this.brNo = brNo;	
		this.custId = custId;
		this.dupNo= dupNo;
		this.addedMonth = 0 ;
		this.elf491_remomoSet = new TreeSet<String>();
		this.elf491_reportkind = "";
		this.pg_cntrNo_catByCollAndLNAP_Map = new HashMap<String, String>();
		this.pg_factBalMap = new HashMap<String, BigDecimal>();
		this.crsVO = new CrsVO();
		this.qMap = new HashMap<String, Boolean>();		
		this.cntrNo_onlyIn_C100S03A = new HashSet<String>();
		this.cntrNo_guaranteeLNAP_butLost_CollMstr = new HashSet<String>();
		this.cntrNo_never_retrialData_in_ELF591H = new TreeSet<String>();		
		this.cntrNo_never_retrialData_in_ELF487_HOUSE_or_RMBINS_or_HS_CRE_FG = new TreeSet<String>();
		this.forceFlag = false;
		this.newCustIn3M = false;
	}
	
	// J-108-0078 首次往來之新授信戶(純新貸戶)3個月 
	public boolean isNewCustIn3M() {
		return newCustIn3M;
	}
	public void setNewCustIn3M(boolean newCustIn3M) {
		this.newCustIn3M = newCustIn3M;
	}

	public boolean isDue(Date lnf020_END_DATE) {
    	if(lnf020_END_DATE==null){
    		return false;
    	}
		return LMSUtil.cmpDate(lnf020_END_DATE, "<", date_for_calc_LNF022_ADJ_FAMT);
	}

	public String getBrNo() {
		return brNo;
	}

	public String getCustId() {
		return custId;
	}

	public String getDupNo() {
		return dupNo;
	}

	public int getAddedMonth() {
		return addedMonth;
	}

	public String getElf491_reportkind() {
		return elf491_reportkind;
	}
	
	public boolean containsQ_R5(){
		return this.qMap.containsKey(Q_R5);
	}
	public boolean containsQ_R9(){
		return this.qMap.containsKey(Q_R9);
	}
	
	public void q_R5_markRun(Boolean r5Match){
		this.qMap.put(Q_R5, r5Match);
	}
	
	public void q_R9_markRun(Boolean r9Match){
		this.qMap.put(Q_R9, r9Match);
	}
	
	public void addLNData(List<Map<String, Object>> list){
		crsVO.add_LNData(this.custId, this.dupNo, list);
	}
	
	public CrsVO getLN_VO(){
		return this.crsVO;
	}
	
	public Set<String> getLNF020_CONTRACT(){
		Set<String> r = new HashSet<String>();
		for(Map<String, Object> m : crsVO.getLNF020(custId, dupNo)){
			r.add(Util.trim(m.get("LNF020_CONTRACT")));
		}
		return r;
	}
	
	public Set<String> getLNF020_CONTRACT_except_proj_class_69(){
		Set<String> r = new HashSet<String>();
		for(Map<String, Object> m : crsVO.getLNF020(custId, dupNo)){
			String lnf020_proj_class = Util.trim(m.get("LNF020_PROJ_CLASS"));
			if(CrsUtil.is_69(lnf020_proj_class)){
				continue;
			}
			r.add(Util.trim(m.get("LNF020_CONTRACT")));
		}
		return r;
	}
	
	public void addCntrNo_onlyIn_C100S03A(String s){
		if(Util.isNotEmpty(s)){
			this.cntrNo_onlyIn_C100S03A.add(s);			
		}		
	}
	
	public void addCntrNo_guaranteeLNAP_butLost_CollMstr(String s){
		if(Util.isNotEmpty(s)){
			this.cntrNo_guaranteeLNAP_butLost_CollMstr.add(s);			
		}		
	}
	
	public boolean rule_decided(){
		return elf491_remomoSet.size()>0;
	}
	
	private Set<String> filter_cntrNo_by_CollAndLNAP(String[] flagArr){
		Set<String> r = new HashSet<String>();
		for(String cntrNo : pg_cntrNo_catByCollAndLNAP_Map.keySet()){
			String v = pg_cntrNo_catByCollAndLNAP_Map.get(cntrNo);
			if(CrsUtil.inCollection(v, flagArr)){
				r.add(cntrNo);
			}
		}
		return r; 
	}
	
	private boolean not_cmsOnlyC01_and_loanNoOnly246(){
		if(CollectionUtils.isNotEmpty(cntrNo_guaranteeLNAP_butLost_CollMstr)){
			return true;
		}
		
		//非不動產十足擔保
		//當有 非不動產的擔保品(EX:股票) || 非十足擔保(含無擔科目)
		if(CollectionUtils.isNotEmpty(filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_COLLNO_ONLY_01___LNAP_IN_135, CrsUtil.PG_HAS_COLL___COLLNO_NOT_ONLY_01})) ){
			return true;
		}
		
		if(CollectionUtils.isNotEmpty(filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_COLLNO_ONLY_01___LNAP_ONLY_2, CrsUtil.PG_COLLNO_ONLY_01___LNPA_46_OR_246})) ){
			//另外判斷...
		}else{
			return true;//當抓到0筆，不能被剔除
		}
		return false;
	}
	
	public void addCntrNo_never_retrialData_in_ELF591H(String s){
		if(Util.isNotEmpty(s)){
			this.cntrNo_never_retrialData_in_ELF591H.add(s);			
		}		
	}
	
	public void addCntrNo_never_retrialData_in_ELF487_HOUSE_or_RMBINS_or_HS_CRE_FG(List<String> list){
		for(String cntrNo : list){
			if(Util.isNotEmpty(cntrNo)){
				this.cntrNo_never_retrialData_in_ELF487_HOUSE_or_RMBINS_or_HS_CRE_FG.add(cntrNo);			
			}	
		}	
	}
	
	/**
	 * 在 ELF490 的規則舊案:9, 新案:1
	 * 而擔保品中，無01開頭的不動產，只有 09-01-001 參貸他行(內容有寫到不動產)
	 * 當建檔錯誤的狀況下 
	 */
	public boolean rule1_newRule_match__and_adjAmt_over_threshold(){		
		if(not_cmsOnlyC01_and_loanNoOnly246()){
			return true;
		}		
		if(CollectionUtils.isNotEmpty(cntrNo_never_retrialData_in_ELF591H)
			|| CollectionUtils.isNotEmpty(cntrNo_never_retrialData_in_ELF487_HOUSE_or_RMBINS_or_HS_CRE_FG)){
			return true;
		}
		
		Set<String> cntrNoSet = filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_COLLNO_ONLY_01___LNAP_ONLY_2, CrsUtil.PG_COLLNO_ONLY_01___LNPA_46_OR_246});
		return amt_over_threshold_3000WAN_1500WAN___cmp_0(cntrNoSet.size(), _sum_FactBal(cntrNoSet));
	}		
	
	public boolean rule2_match__and_adjAmt_over_threshold(){		
		if(not_cmsOnlyC01_and_loanNoOnly246()){
			return true;
		}
		if(CollectionUtils.isNotEmpty(cntrNo_never_retrialData_in_ELF591H)
			|| CollectionUtils.isNotEmpty(cntrNo_never_retrialData_in_ELF487_HOUSE_or_RMBINS_or_HS_CRE_FG)){
			return true;
		}
		
		Set<String> cntrNoSet = filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_COLLNO_ONLY_01___LNAP_ONLY_2});
		int cntrNo_at_retrialBr = 0;
		for(String cntrNo : cntrNoSet){
			if(Util.equals(brNo, CrsUtil.getBrNoFromLNF020_CONTRACT(cntrNo))){
				cntrNo_at_retrialBr++;	
			}
		}
		if(cntrNo_at_retrialBr>0){
			return amt_over_threshold_3000WAN_1500WAN___cmp_0(cntrNoSet.size(), _sum_FactBal(cntrNoSet));		 	
		}else{
			return false; //不符合
		}		
	}	
	
	public boolean rule2_match__but_adjAmt_below_threshold(){		
		if(not_cmsOnlyC01_and_loanNoOnly246()){
			return true;
		}
		if(CollectionUtils.isNotEmpty(cntrNo_never_retrialData_in_ELF591H)
			|| CollectionUtils.isNotEmpty(cntrNo_never_retrialData_in_ELF487_HOUSE_or_RMBINS_or_HS_CRE_FG)){
			return true;
		}
		
		Set<String> cntrNoSet = filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_COLLNO_ONLY_01___LNAP_ONLY_2});
		int cntrNo_at_retrialBr = 0;
		for(String cntrNo : cntrNoSet){
			if(Util.equals(brNo, CrsUtil.getBrNoFromLNF020_CONTRACT(cntrNo))){
				cntrNo_at_retrialBr++;	
			}
		}
		if(cntrNo_at_retrialBr>0){
			if(CrsUtil.amt_over_threshold_3000WAN_1500WAN_by_brNo(_isTpBr(), _sum_FactBal(cntrNoSet))){
				return false;
			}else{
				return true;
			}				 	
		}else{
			return false; //不符合，該分行的額度0筆
		}		
	}
	
	public boolean rule4_match(){
		if(not_cmsOnlyC01_and_loanNoOnly246()){
			return true;
		}
		return false;
	}
	
	public boolean rule5_match(){
		return MapUtils.getBooleanValue( qMap, Q_R5);
	}
	public boolean rule9_match(){
		return MapUtils.getBooleanValue( qMap, Q_R9);
	}
	
	private BigDecimal _sum_FactBal(Set<String> cntrNoSet){
		BigDecimal r = new BigDecimal("0");
		for(String specific_cntrNo : cntrNoSet){
			r = r.add(this.pg_factBalMap.get(specific_cntrNo));
		}
		return r;
	}	

	public void set_cntrNo_catByCollAndLNAP_Map(Map<String, String> m){
		this.pg_cntrNo_catByCollAndLNAP_Map.putAll(m);
		
		for(String specific_cntrNo : filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_COLLNO_ONLY_01___LNAP_ONLY_2, CrsUtil.PG_COLLNO_ONLY_01___LNPA_46_OR_246})){
			this.pg_factBalMap.put(specific_cntrNo, _cntrNo_FactBal(specific_cntrNo));
		}
	}
	
	/**
	 * 當有母戶(30)子戶(31, 31) 的情況出現
	 * cntrNo 可能會依 lnf020_ln_br_no 不同，而 出現多次
	 */
	private BigDecimal _cntrNo_FactBal(String specific_cntrNo){
		BigDecimal sum = new BigDecimal("0");		
		for(Map<String, Object> m: this.crsVO.getLNF020(this.custId, this.dupNo)){
			
			String cntrNo = Util.trim(m.get("LNF020_CONTRACT"));
			if(Util.notEquals(specific_cntrNo, cntrNo)){
				continue;
			}
			
			String lnf020_ln_br_no = Util.trim(m.get("LNF020_LN_BR_NO"));
			Date lnf020_END_DATE = (Date)m.get("LNF020_END_DATE");
			boolean revolve = Util.trim(m.get("LNF020_REVOLVE")).equals("Y");
			BigDecimal ln020fact = CrsUtil.parseBigDecimal(m.get("LNF020_FACT_AMT"));					
			BigDecimal ln020used = CrsUtil.parseBigDecimal(m.get("LNF020_USED_AMT"));
			boolean isDue = isDue(lnf020_END_DATE);			
			String lnf020_proj_class = Util.trim(m.get("LNF020_PROJ_CLASS"));
			//---				
			BigDecimal ln030bal = new BigDecimal("0");
			for(Map<String, Object> dataMap030_040 : this.crsVO.getLNF030_040_withLNF020_LN_BR_NO_NoCharcCode30(cntrNo, lnf020_ln_br_no)){						
				BigDecimal lnf030_loan_bal = CrsUtil.parseBigDecimal(dataMap030_040.get("LNF030_LOAN_BAL"));
				//---
				ln030bal = ln030bal.add(lnf030_loan_bal);
			}
			BigDecimal add_val = null;
			//---
			if (revolve) {
				if (isDue) {
					// ---[1]循 環,已到期: 餘額
					add_val = ln030bal;
				} else {
					// ---[2]循 環,未到期: 額度
					add_val = ln020fact;
				}
			} else {
				if (isDue) {
					// ---[3]不循環,已到期: 餘額
					add_val = ln030bal;
				} else {						
					
					// --- [4]不循環,未到期: bal+(額度-已用)
					
					//可能出現 LNF020_FACT_AMT < LNF020_USED_AMT
					//不要讓值變成 負的
					if(ln020fact.compareTo(ln020used)<0){
						add_val = ln030bal;
					}else{
						add_val = ln030bal.add(ln020fact).subtract(ln020used);
					}					
				}
			}
			
			if(CrsUtil.is_67(lnf020_proj_class) || CrsUtil.is_70(lnf020_proj_class)){
				/*
				 以房養老，是每期撥款，不能只看 餘額
				*/
				add_val = ln020fact;
			}
			//---
			sum = sum.add(add_val);
		}
		return sum;
	}
	
	private boolean _isTpBr(){
		return CrsUtil.is_tp_brNo(this.brNo);
	}
	
	private boolean amt_over_threshold_3000WAN_1500WAN___cmp_0(int rows, BigDecimal sum){
		
		if(rows>0 && BigDecimal.ZERO.compareTo(sum)==0){
			//餘額為0, 可能未動用
			return true;
		}
		 
		return CrsUtil.amt_over_threshold_3000WAN_1500WAN_by_brNo(_isTpBr(), sum);		
	}

	public String getInitInfo(){
		return "["+CapDate.formatDate(this.date_for_calc_LNF022_ADJ_FAMT, CapDate.DEFAULT_DATE_FORMAT)+","+this.brNo+","+this.custId+","+this.dupNo+"]";	
	}
	
	public String getDecideInfo(){
		return getInitInfo()+"{addedMonth:"+this.addedMonth+",matchRule:"+this.elf491_remomoSet+",reportkind:"+this.elf491_reportkind+"}";
	}
	
	public void forceON490_NEW_R1R2R4(String elf490_rule_no_new, BigDecimal elf490_estate_amt){
		if(rule_decided()){
			return;
		}
		
		Set<String> ruleSet = CrsUtil.parseRule(elf490_rule_no_new).keySet();
		int force_addMonth = newCustIn3M?3:6; 
		if(ruleSet.contains(CrsUtil.R1) && amt_over_threshold_3000WAN_1500WAN___cmp_0(1, elf490_estate_amt)){
			//R1的新案 6 個月
			decideCrDate(force_addMonth, CrsUtil.R1, CrsUtil.NEW_RULE);	
			this.forceFlag = true; 
		}
		if(ruleSet.contains(CrsUtil.R2) && amt_over_threshold_3000WAN_1500WAN___cmp_0(1, elf490_estate_amt)){
			//R2的新案 6 個月
			decideCrDate(force_addMonth, CrsUtil.R2, CrsUtil.NEW_RULE);
			this.forceFlag = true;
		}
		if(ruleSet.contains(CrsUtil.R4)){
			//R4的新案 6 個月
			decideCrDate(force_addMonth, CrsUtil.R4, CrsUtil.NEW_RULE);
			this.forceFlag = true;
		}			
	}
	public boolean decideCrDate(int added, String matchRule, String reportkind){
		
        if(added==0){
            //條件不符合
        	//當全都不符合時 elf491_reportkind='', elf491_remomoSet={}
        }else{
            if(this.addedMonth==0){
                this.addedMonth = added;
                {
                	this.elf491_remomoSet.clear();
                	this.elf491_remomoSet.add(matchRule);
                }
                this.elf491_reportkind = reportkind;
                return true;
            }else{
                if(added < this.addedMonth){
                	this.addedMonth = added;                	
                	{//新的 addedMonth較小, 把 elf491_remomoSet 清掉
                		this.elf491_remomoSet.clear();
                		this.elf491_remomoSet.add(matchRule);
                	}
                    this.elf491_reportkind = reportkind;
                    return true;
                }else if(added == this.addedMonth){
                	//在同一種 reportkind 狀況下(EX:同樣都是新案)
                	if(Util.equals(this.elf491_reportkind, reportkind)){
                		this.elf491_remomoSet.add(matchRule);
                	}else{
                		//既有 reportkind<>新的reportkind【新案優先於舊案】
                		if(Util.equals(CrsUtil.NEW_RULE, reportkind)){
                			{
                				this.elf491_remomoSet.clear();
                				this.elf491_remomoSet.add(matchRule);
                			}
                    		this.elf491_reportkind = reportkind;
                		}
                	}                	
                }else{
                    //added > this.addedMonth, skip
                }   
            }    
        }
        return false;
    }
	
	public String getC241M01Z_reasonDesc(){
		List<String> r = new ArrayList<String>();
		r.add("addM["+getAddedMonth()+"]");
		if(isNewCustIn3M()){
			r.add("[NewCustIn3M=Y]");
		}		
		if(CollectionUtils.isNotEmpty(cntrNo_onlyIn_C100S03A)){
			String firstStr = "";
			for(String cntrNo: cntrNo_onlyIn_C100S03A){
				firstStr = cntrNo;
				break;
			}
			r.add("_C100S03A:"+firstStr);
		}
		//X萬
		BigDecimal amtS_L = _sum_FactBal(filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_COLLNO_ONLY_01___LNAP_ONLY_2, CrsUtil.PG_COLLNO_ONLY_01___LNPA_46_OR_246})).divide(new BigDecimal("10000"), 0, BigDecimal.ROUND_DOWN);
		BigDecimal amtS = _sum_FactBal(filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_COLLNO_ONLY_01___LNAP_ONLY_2})).divide(new BigDecimal("10000"), 0, BigDecimal.ROUND_DOWN);
		r.add("c01[S+L:"+NumConverter.addComma(amtS_L)+"^S:"+NumConverter.addComma(amtS)+"]tp:"+(_isTpBr()?"Y":"N"));		
		r.add("(ng|x)"+filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_COLLNO_ONLY_01___LNAP_IN_135, CrsUtil.PG_HAS_COLL___COLLNO_NOT_ONLY_01}).size());
		r.add("R5:"+(rule5_match()?"Y":"N")+",R9:"+(rule9_match()?"Y":"N"));
		r.add((this.forceFlag?"F-":"")+"match:"+elf491_remomoSet);
		
		if(CollectionUtils.isNotEmpty(cntrNo_guaranteeLNAP_butLost_CollMstr)){
			r.add("_lostCMS:"+cntrNo_guaranteeLNAP_butLost_CollMstr); //ref LLWLN009 A-LOAN核准有擔放款卻查無E-LOAN擔保品資料檔 
		}
		if(CollectionUtils.isNotEmpty(cntrNo_never_retrialData_in_ELF591H)){
			r.add("_ELF591H:"+cntrNo_never_retrialData_in_ELF591H.first()); //若有N筆，列出部分即可 
		}
		if(CollectionUtils.isNotEmpty(cntrNo_never_retrialData_in_ELF487_HOUSE_or_RMBINS_or_HS_CRE_FG)){
			r.add("_ELF487_FG:"+cntrNo_never_retrialData_in_ELF487_HOUSE_or_RMBINS_or_HS_CRE_FG.first()); //若有N筆，列出部分即可 
		}
		r.add("c01S{"+_output(filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_COLLNO_ONLY_01___LNAP_ONLY_2}))+"}");
		r.add("c01L{"+_output(filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_COLLNO_ONLY_01___LNPA_46_OR_246}))+"}");
		r.add("c01ng:"+filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_COLLNO_ONLY_01___LNAP_IN_135}));
		r.add("c0x:"+filter_cntrNo_by_CollAndLNAP(new String[]{CrsUtil.PG_HAS_COLL___COLLNO_NOT_ONLY_01}));		
		return StringUtils.join(r, ", ");
	}
	
	private String _output(Set<String> set){
		if(CollectionUtils.isEmpty(set)){
			return "";
		}
		ArrayList<String> r = new ArrayList<String>();
		for(String cntrNo : set){
			r.add(cntrNo+":"+NumConverter.addComma(this.pg_factBalMap.get(cntrNo)));
		}
		return StringUtils.join(r, ", ");
	}
}
