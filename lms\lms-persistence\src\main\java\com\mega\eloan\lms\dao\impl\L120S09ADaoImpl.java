/* 
 * L120S09ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L120S09ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S09A;

/** 洗錢防制明細檔 **/
@Repository
public class L120S09ADaoImpl extends LMSJpaDao<L120S09A, String> implements
		L120S09ADao {

	@Override
	public L120S09A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S09A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S09A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S09A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120S09A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			search.setMaxResults(Integer.MAX_VALUE);
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S09A> findL120S09AListByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	@Override
	public List<L120S09A> findByMainIdAndCustIdDupNo(String mainId,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S09A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S09A> findByMainIdAndCustName(String mainId, String custName) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custName", custName);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S09A> list = createQuery(search).getResultList();
		return list;
	}
	
	
	@Override
	public L120S09A findMaxQDateByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("queryDateS",true);
		 
		return findUniqueOrNone(search);
	}
	
	
	@Override
	public List<L120S09A> findByMainIdWithOrder(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seqNum");
		search.addOrderBy("checkSeq");
		search.addOrderBy("custRelation");
		search.addOrderBy("custId");
		search.addOrderBy("dupNo");
		search.addOrderBy("custEName");
		search.addOrderBy("custName");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S09A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L120S09A> findByMainIdWithOrder1(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("custRelation");
		search.addOrderBy("custId");
		search.addOrderBy("dupNo");
		search.addOrderBy("custEName");
		search.addOrderBy("custName");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S09A> list = createQuery(search).getResultList();
		return list;
	}

}