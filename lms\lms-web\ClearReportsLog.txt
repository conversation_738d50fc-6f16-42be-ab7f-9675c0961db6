﻿[Config,STATUS,7/22 9:51:22,#00002] Initializing persistence in folder C:\ProgramData\i-net software\reporting_Temp_#60imported config#62
[Reporting,STATUS,7/22 9:51:24,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 1544ms.
[Reporting,INFO  ,7/22 9:51:25,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/22 9:51:25,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/22 9:51:25,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/22 9:51:25,#00002] Memory at "afterSetReportFile": max=512 MB	total=509 MB	free=125 MB	used=384 MB
[Reporting,INFO  ,7/22 9:51:25,#00002] Set Prompt[ERRORMSG]: EFD0066:Report Error [308] Invalid key: Bad Padding: message must be same length as key for rsa decryption
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.inet.report.ReportExceptionFactory.createReportException(SourceFile:44)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.inet.report.Engine.execute(SourceFile:1086)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:281)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:183)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.generateReport(AbstractReportService.java:63)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.getContent(AbstractReportService.java:102)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R02RptServiceImpl$$EnhancerBySpringCGLIB$$aad9bbad.getContent(<generated>)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at sun.reflect.GeneratedMethodAccessor227.invoke(Unknown Source)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/22 9:51:25,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 16ms.
[Reporting,DEBUG ,7/22 9:51:25,#00002] Engine Statistics:
[Reporting,DEBUG ,7/22 9:51:25,#00002]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   total running  cpu time: 15ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   report loading system time: 16ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   total running  system time: 16ms
[Reporting,INFO  ,7/22 9:51:25,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/22 9:51:25,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/22 9:51:25,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/22 9:51:25,#00002] Memory at "afterSetReportFile": max=512 MB	total=509 MB	free=151 MB	used=358 MB
[Reporting,INFO  ,7/22 9:51:25,#00002] Set Prompt[ERRORMSG]: EFD0066:java.lang.NullPointerException
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.getContent(AbstractReportService.java:122)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R02RptServiceImpl$$EnhancerBySpringCGLIB$$aad9bbad.getContent(<generated>)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at sun.reflect.GeneratedMethodAccessor227.invoke(Unknown Source)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/22 9:51:25,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/22 9:51:25,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 15ms.
[Reporting,DEBUG ,7/22 9:51:25,#00002] Engine Statistics:
[Reporting,DEBUG ,7/22 9:51:25,#00002]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   total running  cpu time: 15ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   report loading system time: 15ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/22 9:51:25,#00002]   total running  system time: 15ms
[Config,STATUS,7/22 13:41:36,#00002] Initializing persistence in folder C:\ProgramData\i-net software\reporting_Temp_#60imported config#62
[Reporting,STATUS,7/22 13:41:39,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 1752ms.
[Reporting,INFO  ,7/22 13:41:39,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/22 13:41:39,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/22 13:41:39,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/22 13:41:39,#00002] Memory at "afterSetReportFile": max=512 MB	total=509 MB	free=133 MB	used=376 MB
[Reporting,INFO  ,7/22 13:41:39,#00002] Set Prompt[ERRORMSG]: EFD0066:Report Error [308] Invalid key: Bad Padding: message must be same length as key for rsa decryption
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at com.inet.report.ReportExceptionFactory.createReportException(SourceFile:44)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at com.inet.report.Engine.execute(SourceFile:1086)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:281)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:183)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.generateReport(AbstractReportService.java:63)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.getContent(AbstractReportService.java:102)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R02RptServiceImpl$$EnhancerBySpringCGLIB$$abe61451.getContent(<generated>)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at sun.reflect.GeneratedMethodAccessor225.invoke(Unknown Source)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/22 13:41:39,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/22 13:41:39,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 45ms.
[Reporting,DEBUG ,7/22 13:41:39,#00002] Engine Statistics:
[Reporting,DEBUG ,7/22 13:41:39,#00002]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/22 13:41:39,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/22 13:41:39,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/22 13:41:39,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/22 13:41:39,#00002]   total running  cpu time: 31ms
[Reporting,DEBUG ,7/22 13:41:39,#00002]   report loading system time: 45ms
[Reporting,DEBUG ,7/22 13:41:39,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/22 13:41:39,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/22 13:41:39,#00002]   total running  system time: 45ms
[Reporting,INFO  ,7/22 13:41:39,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/22 13:41:39,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/22 13:41:40,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/22 13:41:40,#00002] Memory at "afterSetReportFile": max=512 MB	total=509 MB	free=111 MB	used=398 MB
[Reporting,INFO  ,7/22 13:41:40,#00002] Set Prompt[ERRORMSG]: EFD0066:java.lang.NullPointerException
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.getContent(AbstractReportService.java:122)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R02RptServiceImpl$$EnhancerBySpringCGLIB$$abe61451.getContent(<generated>)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at sun.reflect.GeneratedMethodAccessor225.invoke(Unknown Source)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/22 13:41:40,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/22 13:41:40,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 30ms.
[Reporting,DEBUG ,7/22 13:41:40,#00002] Engine Statistics:
[Reporting,DEBUG ,7/22 13:41:40,#00002]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/22 13:41:40,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/22 13:41:40,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/22 13:41:40,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/22 13:41:40,#00002]   total running  cpu time: 31ms
[Reporting,DEBUG ,7/22 13:41:40,#00002]   report loading system time: 30ms
[Reporting,DEBUG ,7/22 13:41:40,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/22 13:41:40,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/22 13:41:40,#00002]   total running  system time: 30ms
[Config,STATUS,7/23 12:58:42,#00002] Initializing persistence in folder C:\ProgramData\i-net software\reporting_Temp_#60imported config#62
[Reporting,STATUS,7/23 12:58:45,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 1643ms.
[Reporting,INFO  ,7/23 12:58:45,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/23 12:58:45,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 12:58:45,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 12:58:45,#00002] Memory at "afterSetReportFile": max=512 MB	total=508 MB	free=148 MB	used=360 MB
[Reporting,INFO  ,7/23 12:58:45,#00002] Set Prompt[ERRORMSG]: EFD0066:Report Error [308] Invalid key: Bad Padding: message must be same length as key for rsa decryption
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.inet.report.ReportExceptionFactory.createReportException(SourceFile:44)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.inet.report.Engine.execute(SourceFile:1086)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:281)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:183)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.generateReport(AbstractReportService.java:63)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.getContent(AbstractReportService.java:102)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R02RptServiceImpl$$EnhancerBySpringCGLIB$$bca8831a.getContent(<generated>)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at sun.reflect.GeneratedMethodAccessor218.invoke(Unknown Source)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/23 12:58:45,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 23ms.
[Reporting,DEBUG ,7/23 12:58:45,#00002] Engine Statistics:
[Reporting,DEBUG ,7/23 12:58:45,#00002]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   total running  cpu time: 31ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   report loading system time: 23ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   total running  system time: 23ms
[Reporting,INFO  ,7/23 12:58:45,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/23 12:58:45,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 12:58:45,#00002] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/23 12:58:45,#00002] Memory at "afterSetReportFile": max=512 MB	total=508 MB	free=127 MB	used=381 MB
[Reporting,INFO  ,7/23 12:58:45,#00002] Set Prompt[ERRORMSG]: EFD0066:java.lang.NullPointerException
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.getContent(AbstractReportService.java:122)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R02RptServiceImpl$$EnhancerBySpringCGLIB$$bca8831a.getContent(<generated>)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at sun.reflect.GeneratedMethodAccessor218.invoke(Unknown Source)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/23 12:58:45,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/23 12:58:45,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 22ms.
[Reporting,DEBUG ,7/23 12:58:45,#00002] Engine Statistics:
[Reporting,DEBUG ,7/23 12:58:45,#00002]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   total running  cpu time: 31ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   report loading system time: 22ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/23 12:58:45,#00002]   total running  system time: 22ms
[Config,STATUS,7/23 16:10:09,#00002] Initializing persistence in folder C:\ProgramData\i-net software\reporting_Temp_#60imported config#62
[Reporting,STATUS,7/23 16:10:12,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 1656ms.
[Reporting,INFO  ,7/23 16:10:12,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:10:12,#00002] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/23 16:10:12,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:10:12,#00002] Memory at "afterSetReportFile": max=512 MB	total=505 MB	free=95 MB	used=410 MB
[Reporting,INFO  ,7/23 16:10:12,#00002] Set Prompt[ERRORMSG]: EFD0066:Report Error [308] Invalid key: Bad Padding: message must be same length as key for rsa decryption
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.inet.report.ReportExceptionFactory.createReportException(SourceFile:44)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.inet.report.Engine.execute(SourceFile:1086)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:281)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:183)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.generateReport(AbstractReportService.java:63)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.getContent(AbstractReportService.java:102)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R02RptServiceImpl$$EnhancerBySpringCGLIB$$d2f39367.getContent(<generated>)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at sun.reflect.GeneratedMethodAccessor230.invoke(Unknown Source)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/23 16:10:12,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 27ms.
[Reporting,DEBUG ,7/23 16:10:12,#00002] Engine Statistics:
[Reporting,DEBUG ,7/23 16:10:12,#00002]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   total running  cpu time: 15ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   report loading system time: 27ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   total running  system time: 27ms
[Reporting,INFO  ,7/23 16:10:12,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:10:12,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:10:12,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:10:12,#00002] Memory at "afterSetReportFile": max=512 MB	total=505 MB	free=128 MB	used=377 MB
[Reporting,INFO  ,7/23 16:10:12,#00002] Set Prompt[ERRORMSG]: EFD0066:java.lang.NullPointerException
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.getContent(AbstractReportService.java:122)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R02RptServiceImpl$$EnhancerBySpringCGLIB$$d2f39367.getContent(<generated>)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at sun.reflect.GeneratedMethodAccessor230.invoke(Unknown Source)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/23 16:10:12,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/23 16:10:12,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 25ms.
[Reporting,DEBUG ,7/23 16:10:12,#00002] Engine Statistics:
[Reporting,DEBUG ,7/23 16:10:12,#00002]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   total running  cpu time: 15ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   report loading system time: 25ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/23 16:10:12,#00002]   total running  system time: 25ms
[Reporting,INFO  ,7/23 16:41:37,#00003] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:41:37,#00003] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:41:37,#00003] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 16:41:37,#00003] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 16:41:37,#00003] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 16:41:37,#00003] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 16:41:37,#00003] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 16:41:37,#00003] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 16:41:37,#00003] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 16:41:37,#00003] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 16:41:37,#00003] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/23 16:41:37,#00003] Memory at "afterSetReportFile": max=512 MB	total=505 MB	free=108 MB	used=397 MB
[Reporting,INFO  ,7/23 16:41:37,#00003] Set Prompt[C160M01A.BrNo]: 新店分行
[Reporting,INFO  ,7/23 16:41:37,#00003] Set Prompt[C160M01A.CUSTID]: F159894590  邱F159894590
[Reporting,INFO  ,7/23 16:41:37,#00003] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/23 16:41:37,#00003] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 16:41:37,#00003] [Engine.setData] data array with 200 columns and 23 rows.
[Reporting,INFO  ,7/23 16:41:37,#00003] Set locale: zh_TW
[Reporting,INFO  ,7/23 16:41:37,#00003] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 16:41:37,#00003] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 16:41:37,#00003] pdf export: user properties not defined
[Reporting,INFO  ,7/23 16:41:37,#00003] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:41:37,#00003] [Datasource#0] used DataFactory com.inet.report.Database@b4c2b6ed
[Reporting,INFO  ,7/23 16:41:37,#00003] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 16:41:37,#00003] Memory at "beforeSort": max=512 MB	total=505 MB	free=95 MB	used=410 MB
[Reporting,INFO  ,7/23 16:41:37,#00003] Must sort on RowSource ...
[Reporting,INFO  ,7/23 16:41:37,#00003] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 16:41:37,#00003] Memory at "afterSort": max=512 MB	total=505 MB	free=95 MB	used=410 MB
[Reporting,DEBUG ,7/23 16:41:37,#00003] Memory at "beforeRendering": max=512 MB	total=505 MB	free=94 MB	used=411 MB
[Reporting,DEBUG ,7/23 16:41:37,#00003] Memory at "afterRendering": max=512 MB	total=505 MB	free=151 MB	used=354 MB
[Reporting,DEBUG ,7/23 16:41:37,#00003] addPage:2
[Reporting,DEBUG ,7/23 16:41:37,#00003] addPage:3
[Reporting,DEBUG ,7/23 16:41:37,#00003] addPage:4
[Reporting,INFO  ,7/23 16:41:37,#00003] engine isFinish
[Reporting,STATUS,7/23 16:41:37,#00003] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 606ms.
[Reporting,DEBUG ,7/23 16:41:37,#00003] Engine Statistics:
[Reporting,DEBUG ,7/23 16:41:37,#00003]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/23 16:41:37,#00003]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:41:37,#00003]   main execution cpu time: 437ms
[Reporting,DEBUG ,7/23 16:41:37,#00003]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:41:37,#00003]   total running  cpu time: 453ms
[Reporting,DEBUG ,7/23 16:41:37,#00003]   report loading system time: 35ms
[Reporting,DEBUG ,7/23 16:41:37,#00003]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 16:41:37,#00003]   execution      system time: 570ms
[Reporting,DEBUG ,7/23 16:41:37,#00003]   total running  system time: 606ms
[Reporting,INFO  ,7/23 16:49:01,#00004] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:49:01,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:49:01,#00004] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 16:49:01,#00004] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 16:49:01,#00004] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 16:49:01,#00004] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 16:49:01,#00004] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 16:49:01,#00004] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 16:49:01,#00004] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 16:49:01,#00004] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 16:49:01,#00004] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/23 16:49:01,#00004] Memory at "afterSetReportFile": max=512 MB	total=505 MB	free=147 MB	used=358 MB
[Reporting,INFO  ,7/23 16:49:01,#00004] Set Prompt[C160M01A.BrNo]: 新店分行
[Reporting,INFO  ,7/23 16:49:01,#00004] Set Prompt[C160M01A.CUSTID]: F159894590  邱F159894590
[Reporting,INFO  ,7/23 16:49:01,#00004] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/23 16:49:01,#00004] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 16:49:01,#00004] [Engine.setData] data array with 200 columns and 23 rows.
[Reporting,INFO  ,7/23 16:49:01,#00004] Set locale: zh_TW
[Reporting,INFO  ,7/23 16:49:01,#00004] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 16:49:01,#00004] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 16:49:01,#00004] pdf export: user properties not defined
[Reporting,INFO  ,7/23 16:49:01,#00004] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:49:01,#00004] [Datasource#0] used DataFactory com.inet.report.Database@defe2055
[Reporting,INFO  ,7/23 16:49:01,#00004] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 16:49:01,#00004] Memory at "beforeSort": max=512 MB	total=505 MB	free=146 MB	used=359 MB
[Reporting,INFO  ,7/23 16:49:01,#00004] Must sort on RowSource ...
[Reporting,INFO  ,7/23 16:49:01,#00004] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 16:49:01,#00004] Memory at "afterSort": max=512 MB	total=505 MB	free=146 MB	used=359 MB
[Reporting,DEBUG ,7/23 16:49:01,#00004] Memory at "beforeRendering": max=512 MB	total=505 MB	free=146 MB	used=359 MB
[Reporting,DEBUG ,7/23 16:49:01,#00004] Memory at "afterRendering": max=512 MB	total=505 MB	free=145 MB	used=360 MB
[Reporting,DEBUG ,7/23 16:49:01,#00004] addPage:2
[Reporting,DEBUG ,7/23 16:49:01,#00004] addPage:3
[Reporting,DEBUG ,7/23 16:49:01,#00004] addPage:4
[Reporting,INFO  ,7/23 16:49:01,#00004] engine isFinish
[Reporting,STATUS,7/23 16:49:01,#00004] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 58ms.
[Reporting,DEBUG ,7/23 16:49:01,#00004] Engine Statistics:
[Reporting,DEBUG ,7/23 16:49:01,#00004]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/23 16:49:01,#00004]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:49:01,#00004]   main execution cpu time: 31ms
[Reporting,DEBUG ,7/23 16:49:01,#00004]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:49:01,#00004]   total running  cpu time: 62ms
[Reporting,DEBUG ,7/23 16:49:01,#00004]   report loading system time: 25ms
[Reporting,DEBUG ,7/23 16:49:01,#00004]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 16:49:01,#00004]   execution      system time: 32ms
[Reporting,DEBUG ,7/23 16:49:01,#00004]   total running  system time: 58ms
[Reporting,INFO  ,7/23 16:49:15,#00004] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:49:15,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:49:15,#00004] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 16:49:15,#00004] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 16:49:15,#00004] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 16:49:15,#00004] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 16:49:15,#00004] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 16:49:15,#00004] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 16:49:15,#00004] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 16:49:15,#00004] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 16:49:15,#00004] Reference counter: Init references took (2ms)
[Reporting,DEBUG ,7/23 16:49:15,#00004] Memory at "afterSetReportFile": max=512 MB	total=505 MB	free=118 MB	used=387 MB
[Reporting,INFO  ,7/23 16:49:15,#00004] Set Prompt[C160M01A.BrNo]: 新店分行
[Reporting,INFO  ,7/23 16:49:15,#00004] Set Prompt[C160M01A.CUSTID]: F159894590  邱F159894590
[Reporting,INFO  ,7/23 16:49:15,#00004] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/23 16:49:15,#00004] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 16:49:15,#00004] [Engine.setData] data array with 200 columns and 23 rows.
[Reporting,INFO  ,7/23 16:49:15,#00004] Set locale: zh_TW
[Reporting,INFO  ,7/23 16:49:15,#00004] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 16:49:15,#00004] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 16:49:15,#00004] pdf export: user properties not defined
[Reporting,INFO  ,7/23 16:49:15,#00004] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:49:15,#00004] [Datasource#0] used DataFactory com.inet.report.Database@3f1bdb95
[Reporting,INFO  ,7/23 16:49:15,#00004] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 16:49:15,#00004] Memory at "beforeSort": max=512 MB	total=505 MB	free=117 MB	used=388 MB
[Reporting,INFO  ,7/23 16:49:15,#00004] Must sort on RowSource ...
[Reporting,INFO  ,7/23 16:49:15,#00004] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 16:49:15,#00004] Memory at "afterSort": max=512 MB	total=505 MB	free=117 MB	used=388 MB
[Reporting,DEBUG ,7/23 16:49:15,#00004] Memory at "beforeRendering": max=512 MB	total=505 MB	free=117 MB	used=388 MB
[Reporting,DEBUG ,7/23 16:49:15,#00004] Memory at "afterRendering": max=512 MB	total=505 MB	free=116 MB	used=389 MB
[Reporting,DEBUG ,7/23 16:49:15,#00004] addPage:2
[Reporting,DEBUG ,7/23 16:49:15,#00004] addPage:3
[Reporting,DEBUG ,7/23 16:49:15,#00004] addPage:4
[Reporting,INFO  ,7/23 16:49:15,#00004] engine isFinish
[Reporting,STATUS,7/23 16:49:15,#00004] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 53ms.
[Reporting,DEBUG ,7/23 16:49:15,#00004] Engine Statistics:
[Reporting,DEBUG ,7/23 16:49:15,#00004]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/23 16:49:15,#00004]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:49:15,#00004]   main execution cpu time: 31ms
[Reporting,DEBUG ,7/23 16:49:15,#00004]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:49:15,#00004]   total running  cpu time: 46ms
[Reporting,DEBUG ,7/23 16:49:15,#00004]   report loading system time: 23ms
[Reporting,DEBUG ,7/23 16:49:15,#00004]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 16:49:15,#00004]   execution      system time: 30ms
[Reporting,DEBUG ,7/23 16:49:15,#00004]   total running  system time: 53ms
[Reporting,INFO  ,7/23 16:49:27,#00005] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:49:27,#00005] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:49:27,#00005] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 16:49:27,#00005] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 16:49:27,#00005] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 16:49:27,#00005] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 16:49:27,#00005] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 16:49:27,#00005] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 16:49:27,#00005] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 16:49:27,#00005] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 16:49:27,#00005] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/23 16:49:27,#00005] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=139 MB	used=365 MB
[Reporting,INFO  ,7/23 16:49:27,#00005] Set Prompt[C160M01A.BrNo]: 新店分行
[Reporting,INFO  ,7/23 16:49:27,#00005] Set Prompt[C160M01A.CUSTID]: F159894590  邱F159894590
[Reporting,INFO  ,7/23 16:49:27,#00005] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/23 16:49:27,#00005] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 16:49:27,#00005] [Engine.setData] data array with 200 columns and 23 rows.
[Reporting,INFO  ,7/23 16:49:27,#00005] Set locale: zh_TW
[Reporting,INFO  ,7/23 16:49:27,#00005] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 16:49:27,#00005] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 16:49:27,#00005] pdf export: user properties not defined
[Reporting,INFO  ,7/23 16:49:27,#00005] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:49:27,#00005] [Datasource#0] used DataFactory com.inet.report.Database@34f5f43c
[Reporting,INFO  ,7/23 16:49:27,#00005] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 16:49:27,#00005] Memory at "beforeSort": max=512 MB	total=504 MB	free=139 MB	used=365 MB
[Reporting,INFO  ,7/23 16:49:27,#00005] Must sort on RowSource ...
[Reporting,INFO  ,7/23 16:49:27,#00005] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 16:49:27,#00005] Memory at "afterSort": max=512 MB	total=504 MB	free=139 MB	used=365 MB
[Reporting,DEBUG ,7/23 16:49:27,#00005] Memory at "beforeRendering": max=512 MB	total=504 MB	free=139 MB	used=365 MB
[Reporting,DEBUG ,7/23 16:49:27,#00005] Memory at "afterRendering": max=512 MB	total=504 MB	free=137 MB	used=367 MB
[Reporting,DEBUG ,7/23 16:49:27,#00005] addPage:2
[Reporting,DEBUG ,7/23 16:49:27,#00005] addPage:3
[Reporting,DEBUG ,7/23 16:49:27,#00005] addPage:4
[Reporting,INFO  ,7/23 16:49:27,#00005] engine isFinish
[Reporting,STATUS,7/23 16:49:27,#00005] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 129ms.
[Reporting,DEBUG ,7/23 16:49:27,#00005] Engine Statistics:
[Reporting,DEBUG ,7/23 16:49:27,#00005]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/23 16:49:27,#00005]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:49:27,#00005]   main execution cpu time: 46ms
[Reporting,DEBUG ,7/23 16:49:27,#00005]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:49:27,#00005]   total running  cpu time: 78ms
[Reporting,DEBUG ,7/23 16:49:27,#00005]   report loading system time: 29ms
[Reporting,DEBUG ,7/23 16:49:27,#00005]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 16:49:27,#00005]   execution      system time: 99ms
[Reporting,DEBUG ,7/23 16:49:27,#00005]   total running  system time: 129ms
[Reporting,INFO  ,7/23 16:50:24,#00003] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:50:24,#00003] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:50:24,#00003] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 16:50:24,#00003] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 16:50:24,#00003] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 16:50:24,#00003] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 16:50:24,#00003] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 16:50:24,#00003] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 16:50:24,#00003] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 16:50:24,#00003] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 16:50:24,#00003] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/23 16:50:24,#00003] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=110 MB	used=394 MB
[Reporting,INFO  ,7/23 16:50:24,#00003] Set Prompt[C160M01A.BrNo]: 新店分行
[Reporting,INFO  ,7/23 16:50:24,#00003] Set Prompt[C160M01A.CUSTID]: F159894590  邱F159894590
[Reporting,INFO  ,7/23 16:50:24,#00003] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/23 16:50:24,#00003] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 16:50:24,#00003] [Engine.setData] data array with 200 columns and 23 rows.
[Reporting,INFO  ,7/23 16:50:24,#00003] Set locale: zh_TW
[Reporting,INFO  ,7/23 16:50:24,#00003] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 16:50:24,#00003] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 16:50:24,#00003] pdf export: user properties not defined
[Reporting,INFO  ,7/23 16:50:24,#00003] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:50:24,#00003] [Datasource#0] used DataFactory com.inet.report.Database@2ea4422f
[Reporting,INFO  ,7/23 16:50:24,#00003] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 16:50:24,#00003] Memory at "beforeSort": max=512 MB	total=504 MB	free=109 MB	used=395 MB
[Reporting,INFO  ,7/23 16:50:24,#00003] Must sort on RowSource ...
[Reporting,INFO  ,7/23 16:50:24,#00003] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 16:50:24,#00003] Memory at "afterSort": max=512 MB	total=504 MB	free=109 MB	used=395 MB
[Reporting,DEBUG ,7/23 16:50:24,#00003] Memory at "beforeRendering": max=512 MB	total=504 MB	free=109 MB	used=395 MB
[Reporting,DEBUG ,7/23 16:50:24,#00003] Memory at "afterRendering": max=512 MB	total=504 MB	free=108 MB	used=396 MB
[Reporting,DEBUG ,7/23 16:50:24,#00003] addPage:2
[Reporting,DEBUG ,7/23 16:50:24,#00003] addPage:3
[Reporting,DEBUG ,7/23 16:50:24,#00003] addPage:4
[Reporting,INFO  ,7/23 16:50:24,#00003] engine isFinish
[Reporting,STATUS,7/23 16:50:24,#00003] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 52ms.
[Reporting,DEBUG ,7/23 16:50:24,#00003] Engine Statistics:
[Reporting,DEBUG ,7/23 16:50:24,#00003]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/23 16:50:24,#00003]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:50:24,#00003]   main execution cpu time: 31ms
[Reporting,DEBUG ,7/23 16:50:24,#00003]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:50:24,#00003]   total running  cpu time: 62ms
[Reporting,DEBUG ,7/23 16:50:24,#00003]   report loading system time: 23ms
[Reporting,DEBUG ,7/23 16:50:24,#00003]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 16:50:24,#00003]   execution      system time: 29ms
[Reporting,DEBUG ,7/23 16:50:24,#00003]   total running  system time: 52ms
[Reporting,INFO  ,7/23 16:50:42,#00006] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:50:42,#00006] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:50:42,#00006] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:50:42,#00006] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=131 MB	used=373 MB
[Reporting,INFO  ,7/23 16:50:42,#00006] Set Prompt[ERRORMSG]: EFD0066:java.lang.NullPointerException
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at com.iisigroup.cap.component.impl.CapMvcParameters.getString(CapMvcParameters.java:335)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at com.iisigroup.cap.component.impl.CapMvcParameters.getString(CapMvcParameters.java:111)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at com.mega.eloan.lms.base.service.AbstractReportService.generateReport(AbstractReportService.java:60)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R01RptServiceImpl$$EnhancerBySpringCGLIB$$e3716404.generateReport(<generated>)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.genR01(CLS1161RptServiceImpl.java:641)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.generateReport(CLS1161RptServiceImpl.java:135)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:109)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$ceeb2a81.getContent(<generated>)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at sun.reflect.GeneratedMethodAccessor230.invoke(Unknown Source)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/23 16:50:42,#00006] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,INFO  ,7/23 16:50:42,#00006] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 16:50:42,#00006] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 16:50:42,#00006] pdf export: user properties not defined
[Reporting,INFO  ,7/23 16:50:42,#00006] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,INFO  ,7/23 16:50:42,#00006] No datasource name or no database request required
[Reporting,DEBUG ,7/23 16:50:42,#00006] [Datasource#0] used DataFactory com.inet.report.Database@2cba90a5
[Reporting,INFO  ,7/23 16:50:42,#00006] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 16:50:42,#00006] Memory at "beforeSort": max=512 MB	total=504 MB	free=130 MB	used=374 MB
[Reporting,INFO  ,7/23 16:50:42,#00006] Must sort on RowSource ...
[Reporting,INFO  ,7/23 16:50:42,#00006] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 16:50:42,#00006] Memory at "afterSort": max=512 MB	total=504 MB	free=130 MB	used=374 MB
[Reporting,DEBUG ,7/23 16:50:42,#00006] Memory at "beforeRendering": max=512 MB	total=504 MB	free=130 MB	used=374 MB
[Reporting,DEBUG ,7/23 16:50:42,#00006] addPage:2
[Reporting,DEBUG ,7/23 16:50:42,#00006] Memory at "afterRendering": max=512 MB	total=504 MB	free=129 MB	used=375 MB
[Reporting,DEBUG ,7/23 16:50:42,#00006] addPage:3
[Reporting,DEBUG ,7/23 16:50:42,#00006] addPage:4
[Reporting,DEBUG ,7/23 16:50:42,#00006] addPage:5
[Reporting,INFO  ,7/23 16:50:42,#00006] engine isFinish
[Reporting,STATUS,7/23 16:50:42,#00006] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 67ms.
[Reporting,DEBUG ,7/23 16:50:42,#00006] Engine Statistics:
[Reporting,DEBUG ,7/23 16:50:42,#00006]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/23 16:50:42,#00006]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:50:42,#00006]   main execution cpu time: 46ms
[Reporting,DEBUG ,7/23 16:50:42,#00006]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:50:42,#00006]   total running  cpu time: 62ms
[Reporting,DEBUG ,7/23 16:50:42,#00006]   report loading system time: 18ms
[Reporting,DEBUG ,7/23 16:50:42,#00006]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 16:50:42,#00006]   execution      system time: 48ms
[Reporting,DEBUG ,7/23 16:50:42,#00006]   total running  system time: 67ms
[Reporting,INFO  ,7/23 16:50:43,#00006] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:50:43,#00006] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 16:50:43,#00006] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 16:50:43,#00006] Reference counter: Init references took (4ms)
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=115 MB	used=389 MB
[Reporting,INFO  ,7/23 16:50:43,#00006] Set Prompt[C160M01A.CASETYPE]: 1
[Reporting,INFO  ,7/23 16:50:43,#00006] Set Prompt[BRANCHNAME]: 新店分行
[Reporting,INFO  ,7/23 16:50:43,#00006] Set Prompt[L160M01A.RANDOMCODE]: 7fc45e545c00473ca190537585a21398
[Reporting,INFO  ,7/23 16:50:43,#00006] Set Prompt[L160M01A.CASENO]: ２０２０新店(兆)授字第００６４０號
[Reporting,INFO  ,7/23 16:50:43,#00006] Set Prompt[L160M01A.CUSTNAME]: 邱F159894590
[Reporting,INFO  ,7/23 16:50:43,#00006] Set Prompt[L160M01B.CNTRNO]: 046110600176、046110600178
[Reporting,INFO  ,7/23 16:50:43,#00006] [Engine.setData] with array for id: 1
[Reporting,DEBUG ,7/23 16:50:43,#00006] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 16:50:43,#00006] [Engine.setData] data array with 200 columns and 2 rows.
[Reporting,INFO  ,7/23 16:50:43,#00006] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/23 16:50:43,#00006] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 16:50:43,#00006] [Engine.setData] data array with 200 columns and 1 rows.
[Reporting,INFO  ,7/23 16:50:43,#00006] Set locale: zh_TW
[Reporting,INFO  ,7/23 16:50:43,#00006] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 16:50:43,#00006] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 16:50:43,#00006] pdf export: user properties not defined
[Reporting,INFO  ,7/23 16:50:43,#00006] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:50:43,#00006] [Datasource#0] used DataFactory com.inet.report.Database@b8472040
[Reporting,INFO  ,7/23 16:50:43,#00006] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "beforeSort": max=512 MB	total=504 MB	free=115 MB	used=389 MB
[Reporting,INFO  ,7/23 16:50:43,#00006] Must sort on RowSource ...
[Reporting,INFO  ,7/23 16:50:43,#00006] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "afterSort": max=512 MB	total=504 MB	free=115 MB	used=389 MB
[Reporting,INFO  ,7/23 16:50:43,#00006] Fetch data for subreport1 空地貸款註記
[Reporting,DEBUG ,7/23 16:50:43,#00006] [Datasource#0] used DataFactory com.inet.report.Database@f4cdb99f
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "beforeRendering": max=512 MB	total=504 MB	free=115 MB	used=389 MB
[Reporting,DEBUG ,7/23 16:50:43,#00006] setSubreportLinksToSF(com.inet.report.Subreport@65d86e60)
[Reporting,DEBUG ,7/23 16:50:43,#00006] pass 0 prompts to 空地貸款註記
[Reporting,INFO  ,7/23 16:50:43,#00006] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "beforeSort": max=512 MB	total=504 MB	free=87 MB	used=417 MB
[Reporting,INFO  ,7/23 16:50:43,#00006] Must sort on RowSource ...
[Reporting,INFO  ,7/23 16:50:43,#00006] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "afterSort": max=512 MB	total=504 MB	free=87 MB	used=417 MB
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "beforeRendering": max=512 MB	total=504 MB	free=87 MB	used=417 MB
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "afterRendering": max=512 MB	total=504 MB	free=87 MB	used=417 MB
[Reporting,DEBUG ,7/23 16:50:43,#00006] addPage:2
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "afterRendering": max=512 MB	total=504 MB	free=87 MB	used=417 MB
[Reporting,DEBUG ,7/23 16:50:43,#00006] addPage:2
[Reporting,DEBUG ,7/23 16:50:43,#00006] addPage:3
[Reporting,INFO  ,7/23 16:50:43,#00006] engine isFinish
[Reporting,STATUS,7/23 16:50:43,#00006] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt in 514ms.
[Reporting,DEBUG ,7/23 16:50:43,#00006] Engine Statistics:
[Reporting,DEBUG ,7/23 16:50:43,#00006]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   main execution cpu time: 421ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   total running  cpu time: 437ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   report loading system time: 77ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   execution      system time: 437ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   total running  system time: 514ms
[Reporting,INFO  ,7/23 16:50:43,#00006] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/23 16:50:43,#00006] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:50:43,#00006] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=138 MB	used=366 MB
[Reporting,INFO  ,7/23 16:50:43,#00006] Set Prompt[ERRORMSG]: EFD0066:ExceptionConverter: java.io.IOException: The document has no pages.
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at com.lowagie.text.pdf.PdfPages.writePageTree(PdfPages.java:119)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at com.lowagie.text.pdf.PdfWriter.close(PdfWriter.java:1173)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at com.lowagie.text.pdf.PdfDocument.close(PdfDocument.java:868)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at com.lowagie.text.Document.close(Document.java:478)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at tw.com.jcs.common.report.PdfTools.mergeReWritePagePdf(PdfTools.java:1148)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at tw.com.jcs.common.report.PdfTools.mergeReWritePagePdf(PdfTools.java:967)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.generateReport(CLS1161RptServiceImpl.java:182)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:109)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$ceeb2a81.getContent(<generated>)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at sun.reflect.GeneratedMethodAccessor230.invoke(Unknown Source)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/23 16:50:43,#00006] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,INFO  ,7/23 16:50:43,#00006] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 16:50:43,#00006] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 16:50:43,#00006] pdf export: user properties not defined
[Reporting,INFO  ,7/23 16:50:43,#00006] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,INFO  ,7/23 16:50:43,#00006] No datasource name or no database request required
[Reporting,DEBUG ,7/23 16:50:43,#00006] [Datasource#0] used DataFactory com.inet.report.Database@c00f96ca
[Reporting,INFO  ,7/23 16:50:43,#00006] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "beforeSort": max=512 MB	total=504 MB	free=138 MB	used=366 MB
[Reporting,INFO  ,7/23 16:50:43,#00006] Must sort on RowSource ...
[Reporting,INFO  ,7/23 16:50:43,#00006] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "afterSort": max=512 MB	total=504 MB	free=138 MB	used=366 MB
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "beforeRendering": max=512 MB	total=504 MB	free=138 MB	used=366 MB
[Reporting,DEBUG ,7/23 16:50:43,#00006] addPage:2
[Reporting,DEBUG ,7/23 16:50:43,#00006] Memory at "afterRendering": max=512 MB	total=504 MB	free=136 MB	used=368 MB
[Reporting,DEBUG ,7/23 16:50:43,#00006] addPage:3
[Reporting,DEBUG ,7/23 16:50:43,#00006] addPage:4
[Reporting,DEBUG ,7/23 16:50:43,#00006] addPage:5
[Reporting,INFO  ,7/23 16:50:43,#00006] engine isFinish
[Reporting,STATUS,7/23 16:50:43,#00006] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 36ms.
[Reporting,DEBUG ,7/23 16:50:43,#00006] Engine Statistics:
[Reporting,DEBUG ,7/23 16:50:43,#00006]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   main execution cpu time: 15ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   total running  cpu time: 31ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   report loading system time: 15ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   execution      system time: 21ms
[Reporting,DEBUG ,7/23 16:50:43,#00006]   total running  system time: 36ms
[Reporting,INFO  ,7/23 17:19:25,#00007] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/23 17:19:25,#00007] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 17:19:25,#00007] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=121 MB	used=383 MB
[Reporting,INFO  ,7/23 17:19:25,#00007] Set Prompt[ERRORMSG]: EFD0066:java.lang.NullPointerException
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.iisigroup.cap.component.impl.CapMvcParameters.getString(CapMvcParameters.java:335)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.iisigroup.cap.component.impl.CapMvcParameters.getString(CapMvcParameters.java:111)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.base.service.AbstractReportService.generateReport(AbstractReportService.java:60)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R01RptServiceImpl$$EnhancerBySpringCGLIB$$e3716404.generateReport(<generated>)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.genR01(CLS1161RptServiceImpl.java:641)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.generateReport(CLS1161RptServiceImpl.java:135)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:109)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$ceeb2a81.getContent(<generated>)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at sun.reflect.GeneratedMethodAccessor230.invoke(Unknown Source)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,INFO  ,7/23 17:19:25,#00007] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 17:19:25,#00007] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 17:19:25,#00007] pdf export: user properties not defined
[Reporting,INFO  ,7/23 17:19:25,#00007] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,INFO  ,7/23 17:19:25,#00007] No datasource name or no database request required
[Reporting,DEBUG ,7/23 17:19:25,#00007] [Datasource#0] used DataFactory com.inet.report.Database@17149693
[Reporting,INFO  ,7/23 17:19:25,#00007] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "beforeSort": max=512 MB	total=504 MB	free=120 MB	used=384 MB
[Reporting,INFO  ,7/23 17:19:25,#00007] Must sort on RowSource ...
[Reporting,INFO  ,7/23 17:19:25,#00007] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "afterSort": max=512 MB	total=504 MB	free=120 MB	used=384 MB
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "beforeRendering": max=512 MB	total=504 MB	free=120 MB	used=384 MB
[Reporting,DEBUG ,7/23 17:19:25,#00007] addPage:2
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "afterRendering": max=512 MB	total=504 MB	free=119 MB	used=385 MB
[Reporting,DEBUG ,7/23 17:19:25,#00007] addPage:3
[Reporting,DEBUG ,7/23 17:19:25,#00007] addPage:4
[Reporting,DEBUG ,7/23 17:19:25,#00007] addPage:5
[Reporting,INFO  ,7/23 17:19:25,#00007] engine isFinish
[Reporting,STATUS,7/23 17:19:25,#00007] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 314ms.
[Reporting,DEBUG ,7/23 17:19:25,#00007] Engine Statistics:
[Reporting,DEBUG ,7/23 17:19:25,#00007]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   main execution cpu time: 31ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   total running  cpu time: 62ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   report loading system time: 41ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   execution      system time: 272ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   total running  system time: 314ms
[Reporting,INFO  ,7/23 17:19:25,#00007] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt
[Reporting,DEBUG ,7/23 17:19:25,#00007] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 17:19:25,#00007] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 17:19:25,#00007] Reference counter: Init references took (3ms)
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=107 MB	used=397 MB
[Reporting,INFO  ,7/23 17:19:25,#00007] Set Prompt[C160M01A.CASETYPE]: 1
[Reporting,INFO  ,7/23 17:19:25,#00007] Set Prompt[BRANCHNAME]: 新店分行
[Reporting,INFO  ,7/23 17:19:25,#00007] Set Prompt[L160M01A.RANDOMCODE]: 8f505e8926314855af9c3fc0e3e46763
[Reporting,INFO  ,7/23 17:19:25,#00007] Set Prompt[L160M01A.CASENO]: ２０２０新店(兆)授字第００６４０號
[Reporting,INFO  ,7/23 17:19:25,#00007] Set Prompt[L160M01A.CUSTNAME]: 邱F159894590
[Reporting,INFO  ,7/23 17:19:25,#00007] Set Prompt[L160M01B.CNTRNO]: 046110600176、046110600178
[Reporting,INFO  ,7/23 17:19:25,#00007] [Engine.setData] with array for id: 1
[Reporting,DEBUG ,7/23 17:19:25,#00007] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 17:19:25,#00007] [Engine.setData] data array with 200 columns and 2 rows.
[Reporting,INFO  ,7/23 17:19:25,#00007] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/23 17:19:25,#00007] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 17:19:25,#00007] [Engine.setData] data array with 200 columns and 1 rows.
[Reporting,INFO  ,7/23 17:19:25,#00007] Set locale: zh_TW
[Reporting,INFO  ,7/23 17:19:25,#00007] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 17:19:25,#00007] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 17:19:25,#00007] pdf export: user properties not defined
[Reporting,INFO  ,7/23 17:19:25,#00007] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt
[Reporting,DEBUG ,7/23 17:19:25,#00007] [Datasource#0] used DataFactory com.inet.report.Database@70e19ef0
[Reporting,INFO  ,7/23 17:19:25,#00007] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "beforeSort": max=512 MB	total=504 MB	free=107 MB	used=397 MB
[Reporting,INFO  ,7/23 17:19:25,#00007] Must sort on RowSource ...
[Reporting,INFO  ,7/23 17:19:25,#00007] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "afterSort": max=512 MB	total=504 MB	free=107 MB	used=397 MB
[Reporting,INFO  ,7/23 17:19:25,#00007] Fetch data for subreport1 空地貸款註記
[Reporting,DEBUG ,7/23 17:19:25,#00007] [Datasource#0] used DataFactory com.inet.report.Database@5070ec7a
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "beforeRendering": max=512 MB	total=504 MB	free=107 MB	used=397 MB
[Reporting,DEBUG ,7/23 17:19:25,#00007] setSubreportLinksToSF(com.inet.report.Subreport@8cbb0577)
[Reporting,DEBUG ,7/23 17:19:25,#00007] pass 0 prompts to 空地貸款註記
[Reporting,INFO  ,7/23 17:19:25,#00007] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "beforeSort": max=512 MB	total=504 MB	free=107 MB	used=397 MB
[Reporting,INFO  ,7/23 17:19:25,#00007] Must sort on RowSource ...
[Reporting,INFO  ,7/23 17:19:25,#00007] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "afterSort": max=512 MB	total=504 MB	free=107 MB	used=397 MB
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "beforeRendering": max=512 MB	total=504 MB	free=107 MB	used=397 MB
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "afterRendering": max=512 MB	total=504 MB	free=106 MB	used=398 MB
[Reporting,DEBUG ,7/23 17:19:25,#00007] addPage:2
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "afterRendering": max=512 MB	total=504 MB	free=106 MB	used=398 MB
[Reporting,DEBUG ,7/23 17:19:25,#00007] addPage:2
[Reporting,DEBUG ,7/23 17:19:25,#00007] addPage:3
[Reporting,INFO  ,7/23 17:19:25,#00007] engine isFinish
[Reporting,STATUS,7/23 17:19:25,#00007] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt in 120ms.
[Reporting,DEBUG ,7/23 17:19:25,#00007] Engine Statistics:
[Reporting,DEBUG ,7/23 17:19:25,#00007]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   main execution cpu time: 31ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   total running  cpu time: 62ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   report loading system time: 74ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   execution      system time: 45ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   total running  system time: 120ms
[Reporting,INFO  ,7/23 17:19:25,#00007] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/23 17:19:25,#00007] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 17:19:25,#00007] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=104 MB	used=400 MB
[Reporting,INFO  ,7/23 17:19:25,#00007] Set Prompt[ERRORMSG]: EFD0066:ExceptionConverter: java.io.IOException: The document has no pages.
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.lowagie.text.pdf.PdfPages.writePageTree(PdfPages.java:119)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.lowagie.text.pdf.PdfWriter.close(PdfWriter.java:1173)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.lowagie.text.pdf.PdfDocument.close(PdfDocument.java:868)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.lowagie.text.Document.close(Document.java:478)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at tw.com.jcs.common.report.PdfTools.mergeReWritePagePdf(PdfTools.java:1148)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at tw.com.jcs.common.report.PdfTools.mergeReWritePagePdf(PdfTools.java:967)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.generateReport(CLS1161RptServiceImpl.java:182)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:109)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$ceeb2a81.getContent(<generated>)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at sun.reflect.GeneratedMethodAccessor230.invoke(Unknown Source)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/23 17:19:25,#00007] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,INFO  ,7/23 17:19:25,#00007] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 17:19:25,#00007] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 17:19:25,#00007] pdf export: user properties not defined
[Reporting,INFO  ,7/23 17:19:25,#00007] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,INFO  ,7/23 17:19:25,#00007] No datasource name or no database request required
[Reporting,DEBUG ,7/23 17:19:25,#00007] [Datasource#0] used DataFactory com.inet.report.Database@8487df3e
[Reporting,INFO  ,7/23 17:19:25,#00007] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "beforeSort": max=512 MB	total=504 MB	free=103 MB	used=401 MB
[Reporting,INFO  ,7/23 17:19:25,#00007] Must sort on RowSource ...
[Reporting,INFO  ,7/23 17:19:25,#00007] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "afterSort": max=512 MB	total=504 MB	free=103 MB	used=401 MB
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "beforeRendering": max=512 MB	total=504 MB	free=103 MB	used=401 MB
[Reporting,DEBUG ,7/23 17:19:25,#00007] addPage:2
[Reporting,DEBUG ,7/23 17:19:25,#00007] Memory at "afterRendering": max=512 MB	total=504 MB	free=102 MB	used=402 MB
[Reporting,DEBUG ,7/23 17:19:25,#00007] addPage:3
[Reporting,DEBUG ,7/23 17:19:25,#00007] addPage:4
[Reporting,DEBUG ,7/23 17:19:25,#00007] addPage:5
[Reporting,INFO  ,7/23 17:19:25,#00007] engine isFinish
[Reporting,STATUS,7/23 17:19:25,#00007] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 44ms.
[Reporting,DEBUG ,7/23 17:19:25,#00007] Engine Statistics:
[Reporting,DEBUG ,7/23 17:19:25,#00007]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   main execution cpu time: 15ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   total running  cpu time: 46ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   report loading system time: 26ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   execution      system time: 18ms
[Reporting,DEBUG ,7/23 17:19:25,#00007]   total running  system time: 44ms
[Config,STATUS,7/23 18:00:45,#00002] Initializing persistence in folder C:\ProgramData\i-net software\reporting_Temp_#60imported config#62
[Reporting,STATUS,7/23 18:00:46,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 140ms.
[Reporting,INFO  ,7/23 18:00:46,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt
[Reporting,DEBUG ,7/23 18:00:46,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 18:00:47,#00003] no datasource configuration for user scope
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 18:00:47,#00002] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 18:00:47,#00002] Reference counter: Init references took (4ms)
[Reporting,DEBUG ,7/23 18:00:47,#00002] Memory at "afterSetReportFile": max=512 MB	total=512 MB	free=140 MB	used=372 MB
[Reporting,INFO  ,7/23 18:00:47,#00002] Set Prompt[C160M01A.CASETYPE]: 1
[Reporting,INFO  ,7/23 18:00:47,#00002] Set Prompt[BRANCHNAME]: 新店分行
[Reporting,INFO  ,7/23 18:00:47,#00002] Set Prompt[L160M01A.RANDOMCODE]: 38d5bf3330454f15b95a74e93e3776ac
[Reporting,INFO  ,7/23 18:00:47,#00002] Set Prompt[L160M01A.CASENO]: ２０２０新店(兆)授字第００６４０號
[Reporting,INFO  ,7/23 18:00:47,#00002] Set Prompt[L160M01A.CUSTNAME]: 邱F159894590
[Reporting,INFO  ,7/23 18:00:47,#00002] Set Prompt[L160M01B.CNTRNO]: 046110600178
[Reporting,INFO  ,7/23 18:00:47,#00002] [Engine.setData] with array for id: 1
[Reporting,DEBUG ,7/23 18:00:47,#00002] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 18:00:47,#00002] [Engine.setData] data array with 200 columns and 1 rows.
[Reporting,INFO  ,7/23 18:00:47,#00002] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/23 18:00:47,#00002] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 18:00:47,#00002] [Engine.setData] data array with 200 columns and 1 rows.
[Reporting,INFO  ,7/23 18:00:47,#00002] Set locale: zh_TW
[Reporting,STATUS,7/23 18:00:47,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt in 1165ms.
[Reporting,DEBUG ,7/23 18:00:47,#00002] Engine Statistics:
[Reporting,DEBUG ,7/23 18:00:47,#00002]   report loading cpu time: 46ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   total running  cpu time: 46ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   report loading system time: 1165ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   total running  system time: 1165ms
[Reporting,INFO  ,7/23 18:00:47,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/23 18:00:47,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 18:00:47,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 18:00:47,#00002] Memory at "afterSetReportFile": max=512 MB	total=512 MB	free=128 MB	used=384 MB
[Reporting,INFO  ,7/23 18:00:47,#00002] Set Prompt[ERRORMSG]: EFD0066:Report Error [308] Invalid key: Bad Padding: message must be same length as key for rsa decryption
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at com.inet.report.ReportExceptionFactory.createReportException(SourceFile:44)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at com.inet.report.Engine.execute(SourceFile:1086)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:281)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:183)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.genVacantLandReport(CLS1161RptServiceImpl.java:257)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.generateReport(CLS1161RptServiceImpl.java:173)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:109)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$418f8ad5.getContent(<generated>)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at sun.reflect.GeneratedMethodAccessor224.invoke(Unknown Source)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/23 18:00:47,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/23 18:00:47,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 16ms.
[Reporting,DEBUG ,7/23 18:00:47,#00002] Engine Statistics:
[Reporting,DEBUG ,7/23 18:00:47,#00002]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   total running  cpu time: 15ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   report loading system time: 16ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/23 18:00:47,#00002]   total running  system time: 16ms
[Reporting,INFO  ,7/23 18:01:00,#00004] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/23 18:01:00,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 18:01:00,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "afterSetReportFile": max=512 MB	total=512 MB	free=145 MB	used=367 MB
[Reporting,INFO  ,7/23 18:01:00,#00004] Set Prompt[ERRORMSG]: EFD0066:java.lang.NullPointerException
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at com.iisigroup.cap.component.impl.CapMvcParameters.getString(CapMvcParameters.java:335)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at com.iisigroup.cap.component.impl.CapMvcParameters.getString(CapMvcParameters.java:111)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at com.mega.eloan.lms.base.service.AbstractReportService.generateReport(AbstractReportService.java:60)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R01RptServiceImpl$$EnhancerBySpringCGLIB$$5615c458.generateReport(<generated>)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.genR01(CLS1161RptServiceImpl.java:641)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.generateReport(CLS1161RptServiceImpl.java:135)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:109)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$418f8ad5.getContent(<generated>)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at sun.reflect.GeneratedMethodAccessor224.invoke(Unknown Source)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/23 18:01:00,#00004] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,INFO  ,7/23 18:01:00,#00004] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 18:01:00,#00004] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 18:01:00,#00004] pdf export: user properties not defined
[Reporting,INFO  ,7/23 18:01:00,#00004] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,INFO  ,7/23 18:01:00,#00004] No datasource name or no database request required
[Reporting,DEBUG ,7/23 18:01:00,#00004] [Datasource#0] used DataFactory com.inet.report.Database@7d3f43a5
[Reporting,INFO  ,7/23 18:01:00,#00004] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "beforeSort": max=512 MB	total=512 MB	free=132 MB	used=380 MB
[Reporting,INFO  ,7/23 18:01:00,#00004] Must sort on RowSource ...
[Reporting,INFO  ,7/23 18:01:00,#00004] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "afterSort": max=512 MB	total=512 MB	free=131 MB	used=381 MB
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "beforeRendering": max=512 MB	total=512 MB	free=131 MB	used=381 MB
[Reporting,DEBUG ,7/23 18:01:00,#00004] addPage:2
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "afterRendering": max=512 MB	total=512 MB	free=122 MB	used=390 MB
[Reporting,DEBUG ,7/23 18:01:00,#00004] addPage:3
[Reporting,DEBUG ,7/23 18:01:00,#00004] addPage:4
[Reporting,DEBUG ,7/23 18:01:00,#00004] addPage:5
[Reporting,INFO  ,7/23 18:01:00,#00004] engine isFinish
[Reporting,STATUS,7/23 18:01:00,#00004] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 381ms.
[Reporting,DEBUG ,7/23 18:01:00,#00004] Engine Statistics:
[Reporting,DEBUG ,7/23 18:01:00,#00004]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   main execution cpu time: 296ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   total running  cpu time: 312ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   report loading system time: 19ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   execution      system time: 361ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   total running  system time: 381ms
[Reporting,INFO  ,7/23 18:01:00,#00004] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt
[Reporting,DEBUG ,7/23 18:01:00,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/23 18:01:00,#00004] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/23 18:01:00,#00004] Reference counter: Init references took (3ms)
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "afterSetReportFile": max=512 MB	total=512 MB	free=112 MB	used=400 MB
[Reporting,INFO  ,7/23 18:01:00,#00004] Set Prompt[C160M01A.CASETYPE]: 1
[Reporting,INFO  ,7/23 18:01:00,#00004] Set Prompt[BRANCHNAME]: 新店分行
[Reporting,INFO  ,7/23 18:01:00,#00004] Set Prompt[L160M01A.RANDOMCODE]: 38d5bf3330454f15b95a74e93e3776ac
[Reporting,INFO  ,7/23 18:01:00,#00004] Set Prompt[L160M01A.CASENO]: ２０２０新店(兆)授字第００６４０號
[Reporting,INFO  ,7/23 18:01:00,#00004] Set Prompt[L160M01A.CUSTNAME]: 邱F159894590
[Reporting,INFO  ,7/23 18:01:00,#00004] Set Prompt[L160M01B.CNTRNO]: 046110600178
[Reporting,INFO  ,7/23 18:01:00,#00004] [Engine.setData] with array for id: 1
[Reporting,DEBUG ,7/23 18:01:00,#00004] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 18:01:00,#00004] [Engine.setData] data array with 200 columns and 1 rows.
[Reporting,INFO  ,7/23 18:01:00,#00004] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/23 18:01:00,#00004] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/23 18:01:00,#00004] [Engine.setData] data array with 200 columns and 1 rows.
[Reporting,INFO  ,7/23 18:01:00,#00004] Set locale: zh_TW
[Reporting,INFO  ,7/23 18:01:00,#00004] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 18:01:00,#00004] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 18:01:00,#00004] pdf export: user properties not defined
[Reporting,INFO  ,7/23 18:01:00,#00004] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt
[Reporting,DEBUG ,7/23 18:01:00,#00004] [Datasource#0] used DataFactory com.inet.report.Database@c3d7d759
[Reporting,INFO  ,7/23 18:01:00,#00004] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "beforeSort": max=512 MB	total=512 MB	free=112 MB	used=400 MB
[Reporting,INFO  ,7/23 18:01:00,#00004] Must sort on RowSource ...
[Reporting,INFO  ,7/23 18:01:00,#00004] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "afterSort": max=512 MB	total=512 MB	free=112 MB	used=400 MB
[Reporting,INFO  ,7/23 18:01:00,#00004] Fetch data for subreport1 空地貸款註記
[Reporting,DEBUG ,7/23 18:01:00,#00004] [Datasource#0] used DataFactory com.inet.report.Database@61547399
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "beforeRendering": max=512 MB	total=512 MB	free=112 MB	used=400 MB
[Reporting,DEBUG ,7/23 18:01:00,#00004] setSubreportLinksToSF(com.inet.report.Subreport@41527a66)
[Reporting,DEBUG ,7/23 18:01:00,#00004] pass 0 prompts to 空地貸款註記
[Reporting,INFO  ,7/23 18:01:00,#00004] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "beforeSort": max=512 MB	total=512 MB	free=143 MB	used=369 MB
[Reporting,INFO  ,7/23 18:01:00,#00004] Must sort on RowSource ...
[Reporting,INFO  ,7/23 18:01:00,#00004] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "afterSort": max=512 MB	total=512 MB	free=143 MB	used=369 MB
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "beforeRendering": max=512 MB	total=512 MB	free=143 MB	used=369 MB
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "afterRendering": max=512 MB	total=512 MB	free=141 MB	used=371 MB
[Reporting,DEBUG ,7/23 18:01:00,#00004] addPage:2
[Reporting,DEBUG ,7/23 18:01:00,#00004] Memory at "afterRendering": max=512 MB	total=512 MB	free=141 MB	used=371 MB
[Reporting,DEBUG ,7/23 18:01:00,#00004] addPage:2
[Reporting,DEBUG ,7/23 18:01:00,#00004] addPage:3
[Reporting,INFO  ,7/23 18:01:00,#00004] engine isFinish
[Reporting,STATUS,7/23 18:01:00,#00004] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt in 426ms.
[Reporting,DEBUG ,7/23 18:01:00,#00004] Engine Statistics:
[Reporting,DEBUG ,7/23 18:01:00,#00004]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   main execution cpu time: 312ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   total running  cpu time: 343ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   report loading system time: 47ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   execution      system time: 379ms
[Reporting,DEBUG ,7/23 18:01:00,#00004]   total running  system time: 426ms
[Reporting,INFO  ,7/23 18:01:01,#00004] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/23 18:01:01,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 18:01:01,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/23 18:01:01,#00004] Memory at "afterSetReportFile": max=512 MB	total=512 MB	free=129 MB	used=383 MB
[Reporting,INFO  ,7/23 18:01:01,#00004] Set Prompt[ERRORMSG]: EFD0066:ExceptionConverter: java.io.IOException: The document has no pages.
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at com.lowagie.text.pdf.PdfPages.writePageTree(PdfPages.java:119)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at com.lowagie.text.pdf.PdfWriter.close(PdfWriter.java:1173)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at com.lowagie.text.pdf.PdfDocument.close(PdfDocument.java:868)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at com.lowagie.text.Document.close(Document.java:478)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at tw.com.jcs.common.report.PdfTools.mergeReWritePagePdf(PdfTools.java:1148)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at tw.com.jcs.common.report.PdfTools.mergeReWritePagePdf(PdfTools.java:967)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.generateReport(CLS1161RptServiceImpl.java:182)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:109)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$418f8ad5.getContent(<generated>)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at sun.reflect.GeneratedMethodAccessor224.invoke(Unknown Source)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/23 18:01:01,#00004] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,INFO  ,7/23 18:01:01,#00004] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/23 18:01:01,#00004] engineThreadPool.start(this)
[Reporting,DEBUG ,7/23 18:01:01,#00004] pdf export: user properties not defined
[Reporting,INFO  ,7/23 18:01:01,#00004] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,INFO  ,7/23 18:01:01,#00004] No datasource name or no database request required
[Reporting,DEBUG ,7/23 18:01:01,#00004] [Datasource#0] used DataFactory com.inet.report.Database@51312527
[Reporting,INFO  ,7/23 18:01:01,#00004] Sort checkpoint reached.
[Reporting,DEBUG ,7/23 18:01:01,#00004] Memory at "beforeSort": max=512 MB	total=512 MB	free=129 MB	used=383 MB
[Reporting,INFO  ,7/23 18:01:01,#00004] Must sort on RowSource ...
[Reporting,INFO  ,7/23 18:01:01,#00004] Sort on RowSource completed.
[Reporting,DEBUG ,7/23 18:01:01,#00004] Memory at "afterSort": max=512 MB	total=512 MB	free=129 MB	used=383 MB
[Reporting,DEBUG ,7/23 18:01:01,#00004] Memory at "beforeRendering": max=512 MB	total=512 MB	free=129 MB	used=383 MB
[Reporting,DEBUG ,7/23 18:01:01,#00004] addPage:2
[Reporting,DEBUG ,7/23 18:01:01,#00004] Memory at "afterRendering": max=512 MB	total=512 MB	free=127 MB	used=385 MB
[Reporting,DEBUG ,7/23 18:01:01,#00004] addPage:3
[Reporting,DEBUG ,7/23 18:01:01,#00004] addPage:4
[Reporting,DEBUG ,7/23 18:01:01,#00004] addPage:5
[Reporting,INFO  ,7/23 18:01:01,#00004] engine isFinish
[Reporting,STATUS,7/23 18:01:01,#00004] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 53ms.
[Reporting,DEBUG ,7/23 18:01:01,#00004] Engine Statistics:
[Reporting,DEBUG ,7/23 18:01:01,#00004]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/23 18:01:01,#00004]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/23 18:01:01,#00004]   main execution cpu time: 31ms
[Reporting,DEBUG ,7/23 18:01:01,#00004]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/23 18:01:01,#00004]   total running  cpu time: 46ms
[Reporting,DEBUG ,7/23 18:01:01,#00004]   report loading system time: 15ms
[Reporting,DEBUG ,7/23 18:01:01,#00004]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/23 18:01:01,#00004]   execution      system time: 37ms
[Reporting,DEBUG ,7/23 18:01:01,#00004]   total running  system time: 53ms
[Config,STATUS,7/24 11:42:11,#00002] Initializing persistence in folder C:\ProgramData\i-net software\reporting_Temp_#60imported config#62
[Reporting,STATUS,7/24 11:42:11,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 177ms.
[Reporting,INFO  ,7/24 11:42:12,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/24 11:42:12,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/24 11:42:12,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/24 11:42:12,#00002] Memory at "afterSetReportFile": max=512 MB	total=509 MB	free=152 MB	used=357 MB
[Reporting,INFO  ,7/24 11:42:12,#00002] Set Prompt[ERRORMSG]: EFD0066:java.lang.NullPointerException
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:110)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$ca4fb4ec.getContent(<generated>)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at sun.reflect.GeneratedMethodAccessor224.invoke(Unknown Source)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/24 11:42:12,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/24 11:42:12,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 16ms.
[Reporting,DEBUG ,7/24 11:42:12,#00002] Engine Statistics:
[Reporting,DEBUG ,7/24 11:42:12,#00002]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/24 11:42:12,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/24 11:42:12,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/24 11:42:12,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/24 11:42:12,#00002]   total running  cpu time: 15ms
[Reporting,DEBUG ,7/24 11:42:12,#00002]   report loading system time: 16ms
[Reporting,DEBUG ,7/24 11:42:12,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/24 11:42:12,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/24 11:42:12,#00002]   total running  system time: 16ms
[Reporting,DEBUG ,7/24 11:42:12,#00003] no datasource configuration for user scope
[Config,STATUS,7/25 15:05:51,#00002] Initializing persistence in folder C:\ProgramData\i-net software\reporting_Temp_#60imported config#62
[Reporting,STATUS,7/25 15:05:52,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 176ms.
[Reporting,INFO  ,7/25 15:05:52,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R33_zh_TW.rpt
[Reporting,DEBUG ,7/25 15:05:52,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/25 15:05:53,#00003] no datasource configuration for user scope
[Reporting,DEBUG ,7/25 15:05:53,#00002] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/25 15:05:53,#00002] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/25 15:05:53,#00002] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/25 15:05:53,#00002] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/25 15:05:53,#00002] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/25 15:05:53,#00002] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/25 15:05:53,#00002] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/25 15:05:53,#00002] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/25 15:05:53,#00002] Reference counter: Init references took (4ms)
[Reporting,DEBUG ,7/25 15:05:53,#00002] Memory at "afterSetReportFile": max=512 MB	total=509 MB	free=107 MB	used=402 MB
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[BRANCHNAME]: 新店分行
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[L160M01B.CNTRNO]: 
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[L120M01A.CASEDATE]: 
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[L120M01A.CASENO]: 918111100169(0003)
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[L120M01A.RANDOMCODE]: 2fc189c7a3cd47afaa67dd0e52e06d27
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[BLACKLISTQDATE]: 2023-05-08
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[L120S09B.NCRESULT]: 000 - 未命中疑似名單
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[L120S09B.REFNO]: 112046CLS7010195FIJAGFB1
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[L120S09B.UNIQUEKEY]: 3f2d52c7c7d24399bade03f61f3a2702
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[L120S09B.NCCASEID]: 
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[FACTORINGWITHOUTRECOURSEBUYER]: □有□無■不適用(非應收帳款承購業務)
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[L120S09A.CmfwarnpResultDesc]: 
[Reporting,INFO  ,7/25 15:05:53,#00002] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/25 15:05:53,#00002] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/25 15:05:53,#00002] [Engine.setData] data array with 200 columns and 1 rows.
[Reporting,INFO  ,7/25 15:05:53,#00002] Set locale: zh_TW
[Reporting,STATUS,7/25 15:05:53,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R33_zh_TW.rpt in 1194ms.
[Reporting,DEBUG ,7/25 15:05:53,#00002] Engine Statistics:
[Reporting,DEBUG ,7/25 15:05:53,#00002]   report loading cpu time: 156ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   total running  cpu time: 156ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   report loading system time: 1194ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   total running  system time: 1194ms
[Reporting,INFO  ,7/25 15:05:53,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/25 15:05:53,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/25 15:05:53,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/25 15:05:53,#00002] Memory at "afterSetReportFile": max=512 MB	total=509 MB	free=95 MB	used=414 MB
[Reporting,INFO  ,7/25 15:05:53,#00002] Set Prompt[ERRORMSG]: EFD0066:Report Error [308] Invalid key: Bad Padding: message must be same length as key for rsa decryption
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at com.inet.report.ReportExceptionFactory.createReportException(SourceFile:44)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at com.inet.report.Engine.execute(SourceFile:1086)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:281)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:183)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.genLMS1201R33(CLS1161RptServiceImpl.java:757)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.generateReport(CLS1161RptServiceImpl.java:145)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:109)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$472d912c.getContent(<generated>)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at sun.reflect.GeneratedMethodAccessor225.invoke(Unknown Source)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/25 15:05:53,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/25 15:05:53,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 16ms.
[Reporting,DEBUG ,7/25 15:05:53,#00002] Engine Statistics:
[Reporting,DEBUG ,7/25 15:05:53,#00002]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   total running  cpu time: 15ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   report loading system time: 16ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/25 15:05:53,#00002]   total running  system time: 16ms
[Reporting,INFO  ,7/25 15:05:57,#00004] Host name/IP address:                CurryTheGoat/*************; CurryTheGoat/************
[Config,STATUS,7/25 17:30:20,#00002] Initializing persistence in folder C:\ProgramData\i-net software\reporting_Temp_#60imported config#62
[Reporting,STATUS,7/25 17:30:21,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 198ms.
[Reporting,INFO  ,7/25 17:30:21,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R33_zh_TW.rpt
[Reporting,DEBUG ,7/25 17:30:21,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/25 17:30:22,#00003] no datasource configuration for user scope
[Reporting,DEBUG ,7/25 17:30:22,#00002] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/25 17:30:22,#00002] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/25 17:30:22,#00002] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/25 17:30:22,#00002] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/25 17:30:22,#00002] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/25 17:30:22,#00002] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/25 17:30:22,#00002] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/25 17:30:22,#00002] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/25 17:30:22,#00002] Reference counter: Init references took (5ms)
[Reporting,DEBUG ,7/25 17:30:22,#00002] Memory at "afterSetReportFile": max=512 MB	total=508 MB	free=92 MB	used=416 MB
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[BRANCHNAME]: 新店分行
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[L160M01B.CNTRNO]: 
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[L120M01A.CASEDATE]: 
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[L120M01A.CASENO]: 918110899998(0005)
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[L120M01A.RANDOMCODE]: c1901a3980fb4c74a1872f65efe17ee6
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[BLACKLISTQDATE]: 2019-04-24
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[L120S09B.NCRESULT]: 000 - 未命中疑似名單
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[L120S09B.REFNO]: 108046CLS7005186EYPDEAC1
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[L120S09B.UNIQUEKEY]: 7cade819ae514d5596bd9f36981f85cb
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[L120S09B.NCCASEID]: 
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[FACTORINGWITHOUTRECOURSEBUYER]: □有□無■不適用(非應收帳款承購業務)
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[L120S09A.CmfwarnpResultDesc]: 
[Reporting,INFO  ,7/25 17:30:22,#00002] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/25 17:30:22,#00002] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/25 17:30:22,#00002] [Engine.setData] data array with 200 columns and 1 rows.
[Reporting,INFO  ,7/25 17:30:22,#00002] Set locale: zh_TW
[Reporting,STATUS,7/25 17:30:22,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R33_zh_TW.rpt in 1158ms.
[Reporting,DEBUG ,7/25 17:30:22,#00002] Engine Statistics:
[Reporting,DEBUG ,7/25 17:30:22,#00002]   report loading cpu time: 171ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   total running  cpu time: 171ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   report loading system time: 1158ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   total running  system time: 1158ms
[Reporting,INFO  ,7/25 17:30:22,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/25 17:30:22,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/25 17:30:22,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/25 17:30:22,#00002] Memory at "afterSetReportFile": max=512 MB	total=508 MB	free=138 MB	used=370 MB
[Reporting,INFO  ,7/25 17:30:22,#00002] Set Prompt[ERRORMSG]: EFD0066:Report Error [308] Invalid key: Bad Padding: message must be same length as key for rsa decryption
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at com.inet.report.ReportExceptionFactory.createReportException(SourceFile:44)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at com.inet.report.Engine.execute(SourceFile:1086)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:281)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:183)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.genLMS1201R33(CLS1161RptServiceImpl.java:757)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.generateReport(CLS1161RptServiceImpl.java:145)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:109)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$dc344094.getContent(<generated>)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at sun.reflect.GeneratedMethodAccessor224.invoke(Unknown Source)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/25 17:30:22,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/25 17:30:22,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 17ms.
[Reporting,DEBUG ,7/25 17:30:22,#00002] Engine Statistics:
[Reporting,DEBUG ,7/25 17:30:22,#00002]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   total running  cpu time: 15ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   report loading system time: 17ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/25 17:30:22,#00002]   total running  system time: 17ms
[Reporting,INFO  ,7/25 17:30:26,#00001] Host name/IP address:                CurryTheGoat/*************; CurryTheGoat/************
[Config,STATUS,7/28 14:30:43,#00002] Initializing persistence in folder C:\ProgramData\i-net software\reporting_Temp_#60imported config#62
[Reporting,STATUS,7/28 14:30:45,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 1607ms.
[Reporting,INFO  ,7/28 14:30:45,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/28 14:30:45,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/28 14:30:45,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/28 14:30:45,#00002] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=114 MB	used=390 MB
[Reporting,INFO  ,7/28 14:30:45,#00002] Set Prompt[ERRORMSG]: EFD0066:Report Error [308] Invalid key: Bad Padding: message must be same length as key for rsa decryption
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at com.inet.report.ReportExceptionFactory.createReportException(SourceFile:44)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at com.inet.report.Engine.execute(SourceFile:1086)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:281)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:183)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.generateReport(AbstractReportService.java:63)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.getContent(AbstractReportService.java:102)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R02RptServiceImpl$$EnhancerBySpringCGLIB$$b819ad83.getContent(<generated>)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at sun.reflect.GeneratedMethodAccessor224.invoke(Unknown Source)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/28 14:30:45,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/28 14:30:45,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 27ms.
[Reporting,DEBUG ,7/28 14:30:45,#00002] Engine Statistics:
[Reporting,DEBUG ,7/28 14:30:45,#00002]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/28 14:30:45,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/28 14:30:45,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/28 14:30:45,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/28 14:30:45,#00002]   total running  cpu time: 15ms
[Reporting,DEBUG ,7/28 14:30:45,#00002]   report loading system time: 27ms
[Reporting,DEBUG ,7/28 14:30:45,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/28 14:30:45,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/28 14:30:45,#00002]   total running  system time: 27ms
[Reporting,INFO  ,7/28 14:30:46,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/28 14:30:46,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/28 14:30:46,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/28 14:30:46,#00002] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=93 MB	used=411 MB
[Reporting,INFO  ,7/28 14:30:46,#00002] Set Prompt[ERRORMSG]: EFD0066:java.lang.NullPointerException
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService.getContent(AbstractReportService.java:122)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R02RptServiceImpl$$EnhancerBySpringCGLIB$$b819ad83.getContent(<generated>)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at sun.reflect.GeneratedMethodAccessor224.invoke(Unknown Source)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/28 14:30:46,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/28 14:30:46,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 19ms.
[Reporting,DEBUG ,7/28 14:30:46,#00002] Engine Statistics:
[Reporting,DEBUG ,7/28 14:30:46,#00002]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/28 14:30:46,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/28 14:30:46,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/28 14:30:46,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/28 14:30:46,#00002]   total running  cpu time: 15ms
[Reporting,DEBUG ,7/28 14:30:46,#00002]   report loading system time: 19ms
[Reporting,DEBUG ,7/28 14:30:46,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/28 14:30:46,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/28 14:30:46,#00002]   total running  system time: 19ms
[Reporting,INFO  ,7/28 14:30:55,#00003] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/28 14:30:55,#00003] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/28 14:30:55,#00003] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/28 14:30:55,#00003] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/28 14:30:55,#00003] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/28 14:30:55,#00003] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/28 14:30:55,#00003] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/28 14:30:55,#00003] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/28 14:30:55,#00003] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/28 14:30:55,#00003] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/28 14:30:55,#00003] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/28 14:30:55,#00003] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=104 MB	used=400 MB
[Reporting,INFO  ,7/28 14:30:55,#00003] Set Prompt[C160M01A.BrNo]: 新店分行
[Reporting,INFO  ,7/28 14:30:55,#00003] Set Prompt[C160M01A.CUSTID]: 04124881  台０４１２４８８１
[Reporting,INFO  ,7/28 14:30:55,#00003] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/28 14:30:55,#00003] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/28 14:30:55,#00003] [Engine.setData] data array with 200 columns and 22 rows.
[Reporting,INFO  ,7/28 14:30:55,#00003] Set locale: zh_TW
[Reporting,INFO  ,7/28 14:30:55,#00003] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/28 14:30:55,#00003] engineThreadPool.start(this)
[Reporting,DEBUG ,7/28 14:30:55,#00003] pdf export: user properties not defined
[Reporting,INFO  ,7/28 14:30:55,#00003] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/28 14:30:55,#00003] [Datasource#0] used DataFactory com.inet.report.Database@1b1f6fce
[Reporting,INFO  ,7/28 14:30:55,#00003] Sort checkpoint reached.
[Reporting,DEBUG ,7/28 14:30:55,#00003] Memory at "beforeSort": max=512 MB	total=504 MB	free=90 MB	used=414 MB
[Reporting,INFO  ,7/28 14:30:55,#00003] Must sort on RowSource ...
[Reporting,INFO  ,7/28 14:30:55,#00003] Sort on RowSource completed.
[Reporting,DEBUG ,7/28 14:30:55,#00003] Memory at "afterSort": max=512 MB	total=504 MB	free=90 MB	used=414 MB
[Reporting,DEBUG ,7/28 14:30:55,#00003] Memory at "beforeRendering": max=512 MB	total=504 MB	free=89 MB	used=415 MB
[Reporting,DEBUG ,7/28 14:30:56,#00003] Memory at "afterRendering": max=512 MB	total=504 MB	free=79 MB	used=425 MB
[Reporting,DEBUG ,7/28 14:30:56,#00003] addPage:2
[Reporting,DEBUG ,7/28 14:30:56,#00003] addPage:3
[Reporting,DEBUG ,7/28 14:30:56,#00003] addPage:4
[Reporting,INFO  ,7/28 14:30:56,#00003] engine isFinish
[Reporting,STATUS,7/28 14:30:56,#00003] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 447ms.
[Reporting,DEBUG ,7/28 14:30:56,#00003] Engine Statistics:
[Reporting,DEBUG ,7/28 14:30:56,#00003]   report loading cpu time: 46ms
[Reporting,DEBUG ,7/28 14:30:56,#00003]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/28 14:30:56,#00003]   main execution cpu time: 328ms
[Reporting,DEBUG ,7/28 14:30:56,#00003]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/28 14:30:56,#00003]   total running  cpu time: 375ms
[Reporting,DEBUG ,7/28 14:30:56,#00003]   report loading system time: 38ms
[Reporting,DEBUG ,7/28 14:30:56,#00003]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/28 14:30:56,#00003]   execution      system time: 409ms
[Reporting,DEBUG ,7/28 14:30:56,#00003]   total running  system time: 447ms
[Reporting,INFO  ,7/28 14:31:19,#00004] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/28 14:31:19,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/28 14:31:19,#00004] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/28 14:31:19,#00004] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/28 14:31:19,#00004] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/28 14:31:19,#00004] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/28 14:31:19,#00004] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/28 14:31:19,#00004] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/28 14:31:19,#00004] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/28 14:31:19,#00004] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/28 14:31:19,#00004] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/28 14:31:19,#00004] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=94 MB	used=410 MB
[Reporting,INFO  ,7/28 14:31:19,#00004] Set Prompt[C160M01A.BrNo]: 新店分行
[Reporting,INFO  ,7/28 14:31:19,#00004] Set Prompt[C160M01A.CUSTID]: F159894590  邱F159894590
[Reporting,INFO  ,7/28 14:31:19,#00004] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/28 14:31:19,#00004] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/28 14:31:19,#00004] [Engine.setData] data array with 200 columns and 22 rows.
[Reporting,INFO  ,7/28 14:31:19,#00004] Set locale: zh_TW
[Reporting,INFO  ,7/28 14:31:19,#00004] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/28 14:31:19,#00004] engineThreadPool.start(this)
[Reporting,DEBUG ,7/28 14:31:19,#00004] pdf export: user properties not defined
[Reporting,INFO  ,7/28 14:31:19,#00004] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/28 14:31:19,#00004] [Datasource#0] used DataFactory com.inet.report.Database@d4ddd579
[Reporting,INFO  ,7/28 14:31:19,#00004] Sort checkpoint reached.
[Reporting,DEBUG ,7/28 14:31:19,#00004] Memory at "beforeSort": max=512 MB	total=504 MB	free=93 MB	used=411 MB
[Reporting,INFO  ,7/28 14:31:19,#00004] Must sort on RowSource ...
[Reporting,INFO  ,7/28 14:31:19,#00004] Sort on RowSource completed.
[Reporting,DEBUG ,7/28 14:31:19,#00004] Memory at "afterSort": max=512 MB	total=504 MB	free=93 MB	used=411 MB
[Reporting,DEBUG ,7/28 14:31:19,#00004] Memory at "beforeRendering": max=512 MB	total=504 MB	free=93 MB	used=411 MB
[Reporting,DEBUG ,7/28 14:31:19,#00004] Memory at "afterRendering": max=512 MB	total=504 MB	free=92 MB	used=412 MB
[Reporting,DEBUG ,7/28 14:31:19,#00004] addPage:2
[Reporting,DEBUG ,7/28 14:31:19,#00004] addPage:3
[Reporting,DEBUG ,7/28 14:31:19,#00004] addPage:4
[Reporting,INFO  ,7/28 14:31:19,#00004] engine isFinish
[Reporting,STATUS,7/28 14:31:19,#00004] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 62ms.
[Reporting,DEBUG ,7/28 14:31:19,#00004] Engine Statistics:
[Reporting,DEBUG ,7/28 14:31:19,#00004]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/28 14:31:19,#00004]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/28 14:31:19,#00004]   main execution cpu time: 31ms
[Reporting,DEBUG ,7/28 14:31:19,#00004]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/28 14:31:19,#00004]   total running  cpu time: 62ms
[Reporting,DEBUG ,7/28 14:31:19,#00004]   report loading system time: 28ms
[Reporting,DEBUG ,7/28 14:31:19,#00004]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/28 14:31:19,#00004]   execution      system time: 33ms
[Reporting,DEBUG ,7/28 14:31:19,#00004]   total running  system time: 62ms
[Reporting,INFO  ,7/28 14:31:48,#00005] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/28 14:31:48,#00005] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/28 14:31:48,#00005] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/28 14:31:48,#00005] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/28 14:31:48,#00005] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/28 14:31:48,#00005] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/28 14:31:48,#00005] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/28 14:31:48,#00005] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/28 14:31:48,#00005] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/28 14:31:48,#00005] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/28 14:31:48,#00005] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/28 14:31:48,#00005] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=81 MB	used=423 MB
[Reporting,INFO  ,7/28 14:31:48,#00005] Set Prompt[C160M01A.BrNo]: 新店分行
[Reporting,INFO  ,7/28 14:31:48,#00005] Set Prompt[C160M01A.CUSTID]: F159894590  邱F159894590
[Reporting,INFO  ,7/28 14:31:48,#00005] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/28 14:31:48,#00005] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/28 14:31:48,#00005] [Engine.setData] data array with 200 columns and 22 rows.
[Reporting,INFO  ,7/28 14:31:48,#00005] Set locale: zh_TW
[Reporting,INFO  ,7/28 14:31:48,#00005] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/28 14:31:48,#00005] engineThreadPool.start(this)
[Reporting,DEBUG ,7/28 14:31:48,#00005] pdf export: user properties not defined
[Reporting,INFO  ,7/28 14:31:48,#00005] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/28 14:31:48,#00005] [Datasource#0] used DataFactory com.inet.report.Database@f694ae1d
[Reporting,INFO  ,7/28 14:31:48,#00005] Sort checkpoint reached.
[Reporting,DEBUG ,7/28 14:31:48,#00005] Memory at "beforeSort": max=512 MB	total=504 MB	free=80 MB	used=424 MB
[Reporting,INFO  ,7/28 14:31:48,#00005] Must sort on RowSource ...
[Reporting,INFO  ,7/28 14:31:48,#00005] Sort on RowSource completed.
[Reporting,DEBUG ,7/28 14:31:48,#00005] Memory at "afterSort": max=512 MB	total=504 MB	free=80 MB	used=424 MB
[Reporting,DEBUG ,7/28 14:31:48,#00005] Memory at "beforeRendering": max=512 MB	total=504 MB	free=80 MB	used=424 MB
[Reporting,DEBUG ,7/28 14:31:48,#00005] Memory at "afterRendering": max=512 MB	total=504 MB	free=79 MB	used=425 MB
[Reporting,DEBUG ,7/28 14:31:48,#00005] addPage:2
[Reporting,DEBUG ,7/28 14:31:48,#00005] addPage:3
[Reporting,DEBUG ,7/28 14:31:48,#00005] addPage:4
[Reporting,INFO  ,7/28 14:31:48,#00005] engine isFinish
[Reporting,STATUS,7/28 14:31:48,#00005] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 64ms.
[Reporting,DEBUG ,7/28 14:31:48,#00005] Engine Statistics:
[Reporting,DEBUG ,7/28 14:31:48,#00005]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/28 14:31:48,#00005]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/28 14:31:48,#00005]   main execution cpu time: 46ms
[Reporting,DEBUG ,7/28 14:31:48,#00005]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/28 14:31:48,#00005]   total running  cpu time: 78ms
[Reporting,DEBUG ,7/28 14:31:48,#00005]   report loading system time: 29ms
[Reporting,DEBUG ,7/28 14:31:48,#00005]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/28 14:31:48,#00005]   execution      system time: 35ms
[Reporting,DEBUG ,7/28 14:31:48,#00005]   total running  system time: 64ms
[Reporting,INFO  ,7/28 14:31:58,#00003] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/28 14:31:58,#00003] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/28 14:31:58,#00003] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/28 14:31:58,#00003] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/28 14:31:58,#00003] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/28 14:31:58,#00003] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/28 14:31:58,#00003] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/28 14:31:58,#00003] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/28 14:31:58,#00003] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/28 14:31:58,#00003] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/28 14:31:58,#00003] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/28 14:31:58,#00003] Memory at "afterSetReportFile": max=512 MB	total=504 MB	free=101 MB	used=403 MB
[Reporting,INFO  ,7/28 14:31:58,#00003] Set Prompt[C160M01A.BrNo]: 新店分行
[Reporting,INFO  ,7/28 14:31:58,#00003] Set Prompt[C160M01A.CUSTID]: F159894590  邱F159894590
[Reporting,INFO  ,7/28 14:31:58,#00003] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/28 14:31:58,#00003] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/28 14:31:58,#00003] [Engine.setData] data array with 200 columns and 22 rows.
[Reporting,INFO  ,7/28 14:31:58,#00003] Set locale: zh_TW
[Reporting,INFO  ,7/28 14:31:58,#00003] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/28 14:31:58,#00003] engineThreadPool.start(this)
[Reporting,DEBUG ,7/28 14:31:58,#00003] pdf export: user properties not defined
[Reporting,INFO  ,7/28 14:31:58,#00003] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/28 14:31:58,#00003] [Datasource#0] used DataFactory com.inet.report.Database@1e06299d
[Reporting,INFO  ,7/28 14:31:58,#00003] Sort checkpoint reached.
[Reporting,DEBUG ,7/28 14:31:58,#00003] Memory at "beforeSort": max=512 MB	total=504 MB	free=100 MB	used=404 MB
[Reporting,INFO  ,7/28 14:31:58,#00003] Must sort on RowSource ...
[Reporting,INFO  ,7/28 14:31:58,#00003] Sort on RowSource completed.
[Reporting,DEBUG ,7/28 14:31:58,#00003] Memory at "afterSort": max=512 MB	total=504 MB	free=100 MB	used=404 MB
[Reporting,DEBUG ,7/28 14:31:58,#00003] Memory at "beforeRendering": max=512 MB	total=504 MB	free=100 MB	used=404 MB
[Reporting,DEBUG ,7/28 14:31:58,#00003] Memory at "afterRendering": max=512 MB	total=504 MB	free=99 MB	used=405 MB
[Reporting,DEBUG ,7/28 14:31:58,#00003] addPage:2
[Reporting,DEBUG ,7/28 14:31:58,#00003] addPage:3
[Reporting,DEBUG ,7/28 14:31:58,#00003] addPage:4
[Reporting,INFO  ,7/28 14:31:58,#00003] engine isFinish
[Reporting,STATUS,7/28 14:31:58,#00003] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 138ms.
[Reporting,DEBUG ,7/28 14:31:58,#00003] Engine Statistics:
[Reporting,DEBUG ,7/28 14:31:58,#00003]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/28 14:31:58,#00003]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/28 14:31:58,#00003]   main execution cpu time: 62ms
[Reporting,DEBUG ,7/28 14:31:58,#00003]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/28 14:31:58,#00003]   total running  cpu time: 78ms
[Reporting,DEBUG ,7/28 14:31:58,#00003]   report loading system time: 42ms
[Reporting,DEBUG ,7/28 14:31:58,#00003]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/28 14:31:58,#00003]   execution      system time: 96ms
[Reporting,DEBUG ,7/28 14:31:58,#00003]   total running  system time: 138ms
