#==================================================
# \u8986\u5be9\u63a7\u5236\u6a94\u7dad\u8b77
#==================================================
page.title=LMS1815M01 Credit Review Control File
doc.title=Branch Credit Review Update List:
doc.tit02=Case Information
button.save=Save
button.delete=Delete
button.send=Submit For Supervisor's Approval
button.accept=Verifier
button.print=Print
button.exit=Exit
doc.docinfo=Document Information
button.search=Inquire Borrower
button.cauculate=Trial-calculate Next Review Date
button.state=Check customer's latest account status
accept=Approval
return=Return
enterBranch=Please input Branch ID
enterCustId=Please input Borrower's UBN (with repeated serial number)
enterDupNo=Repeat Serial No.
searchTitle=Please enter Query information
ok=Confirm
cancel=Cancel
searchCustId=Branch and customer UBN can not be left blank
wrongBranch=The branch you have entered is not an overseas branch
L180M02B.elfNCkdFlag=Non-review Code
L180M02B.elfMDFlag=Abnormal Credit Reporting Code
L180M02B.elfRCkdLine=Review Cycles
noNewAdd=The review cycle is C/I, hence New/Limit Increase can not be blank
noNewDate=The New/Limit Increase field contains values, hence the New/Limit Increase Date can not be blank
alreadyHave=Unapproved data exists
lms412NoInfo=No customer data found in the Credit Review Control File; continue?
nextCTLDate=Next Review Date:
noElfRCkdLine=Please specify a review cycle
noElfLRDate=Please input the last review date
noElfMainCust=Please specify the principal borrower
noElfMDFlag=Please input the Abnormal Credit Reporting Code
noElfMDDt=Please input the Abnormal Credit Reporting Date
noElfUCkdLINE=Please provide entries to the Authority-specified Review Cases
noElfUCkdDt=Please specify the authority's advice date
infoNoCompl=Please input full information
#J-108-0078_05097_B1001 \u914d\u5408\u6388\u5be9\u8655E-LOAN\u4f01\u91d1\u3001\u6d88\u91d1\u300c\u6388\u4fe1\u8986\u5be9\u7cfb\u7d71\u300d\u4fee\u6539\u9996\u6b21\u5f80\u4f86\u4e4b\u65b0\u6388\u4fe1\u6236(\u4e0b\u7a31\u7d14\u65b0\u8cb8\u6236)\u61c9\u8fa6\u7406\u8986\u5be9\u4e4b\u671f\u9650\u5982\u4e0b\u4fee\u6539\u5167\u5bb9\u3002
noElfIsAllNew=Please input New customers for the first time
custInfo=Borrower
cstate0=No outstanding balance
cstate1=Normal
cstate2=Overdue
cstate3=Collection
cstate4=Bad Debt
noL181M01A=No Master File
err.noStatus=No information found
err.formatNewDate=Year & Month format Of New/Limit Increase/Overdue Normalized Account not match
saveBeforeSend=The data is saved automatically after executing; are you sure you want to continue?
err.check1=Credit Model Rating Category contains values, Credit Model Rating field can not be blank
err.check2=Credit Model Rating field contains values, Credit Model Rating Category can not be blank
err.check3=New/Increase Limit/Overdue Loan Normalized field contains values, New/Increase Limit/Overdue Loan Normalized Year & Month can not be blank!
err.check4=New/Increase Limit/Overdue Loan Normalized Year & Month field contains values, New/Increase Limit/Overdue Loan Normalized can not be blank!
err.check5=Abnormal Credit Reporting code contains values, Date Of Reported Issue can not be blank
err.check6=No Abnormal Credit Reporting Code, Abnormal Credit Reporting Date and Issue must be blank
err.check7=No Non-review Code, Non-review Remarks must be blank!
err.check8=The Customer is authority designated cases, Date of authority notification can not be blank!
err.check9=The Customer still has Credit Limit or Outstanding Balance, Account can not be canceled
err.check10=Temporarily Review; Please fill Next Resume Review Date.
err.check11=The next resume a review date (transaction) can not be smaller than today
err.check12=The competent authorities of the customer notification date is not blank, the competent authority designated cases can not be empty!
#J-108-0078_05097_B1001 \u914d\u5408\u6388\u5be9\u8655E-LOAN\u4f01\u91d1\u3001\u6d88\u91d1\u300c\u6388\u4fe1\u8986\u5be9\u7cfb\u7d71\u300d\u4fee\u6539\u9996\u6b21\u5f80\u4f86\u4e4b\u65b0\u6388\u4fe1\u6236(\u4e0b\u7a31\u7d14\u65b0\u8cb8\u6236)\u61c9\u8fa6\u7406\u8986\u5be9\u4e4b\u671f\u9650\u5982\u4e0b\u4fee\u6539\u5167\u5bb9\u3002
err.check13=The review cycle error\uff0cit should be 