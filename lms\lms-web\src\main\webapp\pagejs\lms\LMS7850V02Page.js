var checkboxstop = $("#checkBoxStop").html();
var pageAction = {
	handler : 'lms7850m01formhandler',
	grid : null,
	build : function(){
		pageAction.grid = $("#gridview").iGrid({
			//localFirst: true,
			handler : 'lms7850gridhandler',
			action :  "queryL785m01a",
	        width : 785,
			height: 350,
	        sortname: 'caseDate|caseNo',
	        sortorder: 'desc|asc',
	        shrinkToFit: true,
	        autowidth: false,       
	         postData: {
	            mainDocStatus: viewstatus,
	            rowNum: 15
	        },
       		rowNum: 15,
			multiselect : true,
			colModel : [{
				colHeader : "oid",
				name : 'oid',
				hidden : true //是否隱藏
			},{
				colHeader : "mainId",
				hidden : true, //是否隱藏
				name : 'mainId'
			},{
				colHeader : "docURL",
				hidden : true, //是否隱藏
				name : 'docURL'
			}, {
                colHeader: i18n.lms7850v02["mainGrid.index3"], //經辦人員
                align: "left",
                width: 70,
                sortable: true,
                name: 'updater'
            }, {
                colHeader: i18n.lms7850v02["mainGrid.index4"], //查詢日期
                align: "center",
                width: 70,
                sortable: true,
                name: 'caseDate'
            }, {
                colHeader: i18n.lms7850v02["mainGrid.index5"], //案件號碼
                align: "left",
                width: 200,
                sortable: true,
                name: 'caseNo'
            }, {
                colHeader: i18n.lms7850v02["mainGrid.index8"], //篩選條件
                align: "left",
                width: 200,
                sortable: false,
                name: 'itemDscrShow'    	
			}
		],
			ondblClickRow: function(rowid){//同修改
				var data = pageAction.grid.getRowData(rowid);
				pageAction.openBox(null, null, data);
			}				
		});
		
		//build button 
		//覆核
		$("#buttonPanel").find("#btnFCheck").click(function() {
			pageAction.checkSend();	
		}).end().find("#btnView").click(function(){
		//調閱	    
	        var id = pageAction.grid.getRowData();
	        if (!id) {	        
	            // action_004=請先選擇需「調閱」之資料列
	            return CommonAPI.showMessage(i18n.def["action_004"]);	            
	        }
	        pageAction.openBox;
	    }).end().find("#btnView").click(function(){
			//調閱	    
	        var rows = pageAction.grid.getGridParam('selarrrow');
			var row;
			if(rows.length > 1){
				// msg.alert2=此功能不能多選!
				return CommonAPI.showMessage(i18n.lms7850v02["msg.alert2"]);
			}
			row = rows[0];			
	        if (!row) {	        
	            // action_004=請先選擇需「調閱」之資料列
	            return CommonAPI.showMessage(i18n.def["action_004"]);
	        }else{
				var result = $("#gridview").getRowData(row);
				pageAction.openBox(null, null, result);
			}	        
	    });
	},	
	/**
	 * 開啟視窗
	 */
	openBox : function(cellvalue, options, rowObject){
		ilog.debug(rowObject);
	    var url = '..' + rowObject.docURL+"/00";
		$.form.submit({
	        url: url,
	        data: {
	            mainDocStatus: viewstatus,
	            mainId: rowObject.mainId,
	            mainOid: rowObject.oid,
	            docURL: rowObject.docURL,
	            oid: rowObject.oid
	        },
	        target: "_blank"
	    });
	},
	/**
	 * 檢查退回或核准
	 */
	checkSend : function(){
		var list = pageAction.getRowData();
		if (list == "") {
			// msg.alert1=尚未選取資料!
			CommonAPI.showMessage(i18n.lms7850v02["msg.alert1"]);
			return;
		}		
		$("#checkBoxStop").thickbox({
			title : i18n.lms7850v02["html.index3"],//'查詢',
			width : 300,
			height : 150,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.def,
			buttons : {
				'sure' : function(){
					var $formCheckStop = $("#formCheckStop");
					var radioVal = $formCheckStop.find("input[name='qButtonTypeStop']:radio:checked").val();
					if(radioVal == "1"){
						// 核定
						pageAction.flowAction({flowAction:"check"},list);
					}else if(radioVal == "2"){
						// 退回
						pageAction.flowAction({flowAction:"backStop"},list);
					}
				},
				'cancel' : function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
				}
			}
		});
	},
	/**
	 * 流程
	 */
	flowAction : function(sendData, list){
		$.ajax({
			handler : pageAction.handler,
			data : $.extend({
				formAction : "flowAction",
				mainOid : "",
				list : list
			}, (sendData || {})),
		}).done(function () {
			// 更新Grid
			pageAction.reloadGrid();
			API.showPopMessage(i18n.def["runSuccess"],
				$.thickbox.close);
		}).fail(function () {
			// 更新Grid
			pageAction.reloadGrid();
			pageAction.tb_removeById(TB_COUNT - 1);
			$("#checkBoxStop").html(checkboxstop);
		});
	},
	/**
	 * 取得資料表之選擇列
	 */
	getRowData : function(){
		var rows = pageAction.grid.getGridParam('selarrrow');
		var list = "";
		var sign = ",";
		for (var i=0;i<rows.length;i++){	
			//將所有已選擇的資料存進變數list裡面
			if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
				var data = pageAction.grid.getRowData(rows[i]);
				list += ((list == "") ? "" : sign ) + data.oid;
			}
		}
		return list;
	},
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				posinputata : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	},
	
	tb_removeById:function(idNum){
		$("#TB_imageOff" + idNum).off("click");
		$("#TB_closeWindowButton" + idNum).off("click");
		$("#TB_HideSelect" + idNum).trigger("unload").off().remove();
		$("#TB_window" + idNum).off().hide();
		$("#TB_window" + idNum + ",#TB_overlay" + idNum + ",#TB_HideSelect" + idNum).trigger("unload").off().remove();
		$("#TB_load" + idNum).remove();
		 if ($.loading){
			$.loading.hide();
		 }
	}	
}

$(function() {
	pageAction.build();	
});