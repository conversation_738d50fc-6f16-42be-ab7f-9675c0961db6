package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L170M01E;

/** 信用評等資料檔 **/
public interface L170M01EDao extends IGenericDao<L170M01E> {

	L170M01E findByOid(String oid);

	List<L170M01E> findByMainId(String mainId,String timeFlag);

	L170M01E findByCustIdAndDupNo(String custId, String dupNo,String timeFlag);

	List<L170M01E> findByUniqueKey(String mainId, String custId, String dupNo,String timeFlag);

	List<L170M01E> findByCntrNo(String CntrNo,String timeFlag);

	List<L170M01E> findByCustIdDupId(String custId, String DupNo,String timeFlag);

	/**
	 * crdType IN ('DB','DL','OU')
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L170M01E> findByUniqueKey2(String mainId, String custId, String dupNo,String timeFlag);

	/**
	 * crdType IN ('DB','DL','OU')
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L170M01E> findByUniqueKey6(String mainId, String custId, String dupNo,String timeFlag);

	/**
	 * crdType IN ('CA','CB'...)
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L170M01E> findByUniqueKey4(String mainId, String custId, String dupNo,String timeFlag);

	/**
	 * crdType IN ('1'....'A'...)
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L170M01E> findByUniqueKey5(String mainId, String custId, String dupNo,String timeFlag);

	// crdType NOT IN ('DB','DL','OU')
	List<Object[]> findByUniqueKey3(String mainId, String custId, String dupNo,String timeFlag);

	void deleteL170m01eList(String mainId);

}