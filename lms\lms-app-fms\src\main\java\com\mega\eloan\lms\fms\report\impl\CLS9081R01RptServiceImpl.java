package com.mega.eloan.lms.fms.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.fms.report.CLS9081R01RptService;
import com.mega.eloan.lms.fms.service.CLS9081Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Util;

@Service("cls9081r01rptservice")
public class CLS9081R01RptServiceImpl implements
	FileDownloadService, CLS9081R01RptService {

	@Resource
	CLS9081Service cls9081Service;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			String yyyy_MM = Util.trim(params.getString("yyyy_MM"));
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			if(true){
				baos = (ByteArrayOutputStream) this.generateXls(yyyy_MM, user.getUnitNo());	
			}
			if(baos==null){
				return null;
			}else{
				return baos.toByteArray();	
			}			
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}
	
	private ByteArrayOutputStream generateXls(String yyyy_MM, String brNo) throws IOException, Exception {	
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		if(true){
			cls9081Service.genExcel(outputStream, yyyy_MM, brNo);	
		}		
		if(outputStream!=null){
			outputStream.flush();	
		}		
		return outputStream;
	}
	
}
