/* 
 * DWRKCNTRNOService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dw.service;

import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONObject;

/**
 * <pre>
 * 額度序號相同對象不同明細表  AND 客戶統編修正
 * </pre>
 * 
 * @since 2012/11/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/21,Gary<PERSON><PERSON>,new
 *          </ul>
 */
public interface DWRKCNTRNOService {

	/**
	 * 查詢額度序號相同對象不同明細表內是否有資料 DW
	 * 
	 * @param CntrNo
	 *            額度序號
	 * @return
	 */
	public List<Map<String, Object>> selByCntrNo(String CntrNo);

	/**
	 * 更新額度序號相同對象不同明細表 DW
	 * 
	 * @param OldCntrNo
	 *            舊額度序號
	 * @param NewCntrNo
	 *            新額度序號
	 * @param table
	 *            Table名稱
	 * @return
	 */
	public void DwUpdatedata(String table, String OldCntrNo, String NewCntrNo);
	/**
	 * 更新客戶統編 DW
	 * 
	 * @param OldCntrNo
	 *            舊額度序號
	 * @param NewCntrNo          
	 *            新額度序號
	 * @param table
	 *            Table名稱          
	 * @return
	 */
	public void DwUpdatedata(String table,String OldCustId,String OldDupNo,
			String NewCustId,String NewDupNo);
	/**
	 * 查詢客戶統編在資料表內是否有資料 DW
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重複序號
	 * @return
	 */
	public List<Map<String, Object>> selBycustIdDupNo(String dwtable[],String custId,
			String dupNo);
	

	/**
	 * @param json
	 * @return
	 */
	int update(JSONObject data);
	
	/**
	 * @param json
	 * @return
	 */
	int insert(JSONObject data);
	
	/**
	 * @param json
	 * @return
	 */
	int updateScore(JSONObject data);
	
	/**
	 * @param json
	 * @return
	 */
	int insertScore(JSONObject data);
}
