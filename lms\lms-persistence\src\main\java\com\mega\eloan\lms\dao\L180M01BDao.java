/* 
 * L180M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L180M01B;

/** 覆審名單明細檔 **/
public interface L180M01BDao extends IGenericDao<L180M01B> {

	L180M01B findByOid(String oid);

	List<L180M01B> findByMainId(String mainId, String ctlType);

	L180M01B findByUniqueKey(String mainId, String custId, String dupNo,
			String ctlType);

	List<L180M01B> findByIndex01(String mainId, String custId, String dupNo,
			String ctlType);

	void deleteL180M01BList(String mainId, String custId, String dupNo,
			String ctlType);

	L180M01B findByProjectNo(String projectNo);

	List<L180M01B> findByCustIdDupId(String custId, String DupNo, String ctlType);

}