/* 
 * C801M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個人資料檔案清冊主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C801M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class C801M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 放款帳號 **/
	@Size(max=1800)
	@Column(name="LOANNO", length=1800, columnDefinition="VARCHAR(1800)")
	private String loanNo;

	/** 配偶姓名 **/
	@Size(max=120)
	@Column(name="MNAME", length=120, columnDefinition="VARCHAR(120)")
	private String mName;

	/** 共同借款人姓名 **/
	@Size(max=1800)
	@Column(name="COCUSTNAME", length=1800, columnDefinition="VARCHAR(1800)")
	private String coCustName;

	/** 保證人姓名 **/
	@Size(max=3300)
	@Column(name="GUARANTOR", length=3300, columnDefinition="VARCHAR(3300)")
	private String guarantor;

	/** 檔案號碼 **/
	@Size(max=62)
	@Column(name="CASENO", length=62, columnDefinition="VARCHAR(62)")
	private String caseNo;

	/** 製表日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CASEDATE", columnDefinition="DATE")
	private Date caseDate;

	/** 案件核准日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CASEAPPROVETIME", columnDefinition="DATE")
	private Date caseApproveTime;

	/** 案件經辦ID **/
	@Size(max=6)
	@Column(name="CASEAPPRID", length=6, columnDefinition="CHAR(6)")
	private String caseApprId;

	/** 案件經辦 **/
	@Size(max=30)
	@Column(name="CASEAPPR", length=30, columnDefinition="VARCHAR(30)")
	private String caseAppr;

	/** 案件覆核人員ID **/
	@Size(max=6)
	@Column(name="CASERECHECKID", length=6, columnDefinition="CHAR(6)")
	private String caseRecheckId;

	/** 案件覆核人員 **/
	@Size(max=30)
	@Column(name="CASERECHECK", length=30, columnDefinition="VARCHAR(30)")
	private String caseRecheck;

	/** 案件甲級主管ID **/
	@Size(max=6)
	@Column(name="CASEBOSSID", length=6, columnDefinition="CHAR(6)")
	private String caseBossId;

	/** 案件甲級主管 **/
	@Size(max=30)
	@Column(name="CASEBOSS", length=30, columnDefinition="VARCHAR(30)")
	private String caseBoss;

	/** 案件結清日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CASECANCELDATE", columnDefinition="DATE")
	private Date caseCancelDate;

	/** 保管人員ID **/
	@Size(max=6)
	@Column(name="CUSTODIANID", length=6, columnDefinition="CHAR(6)")
	private String custodianId;

	/** 保管人員 **/
	@Size(max=30)
	@Column(name="CUSTODIAN", length=30, columnDefinition="VARCHAR(30)")
	private String custodian;

	/** 
	 * 存放地點<p/>
	 * 00: 檔案卷及保管袋<br/>
	 *  01: 本行
	 */
	@Size(max=2)
	@Column(name="STORAGELOCATION", length=2, columnDefinition="CHAR(2)")
	private String storageLocation;

	/** 保存最後日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="FINALDATE", columnDefinition="DATE")
	private Date finalDate;

	/** 銷毀日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DESTROYDATE", columnDefinition="DATE")
	private Date destroyDate;

	/** 
	 * RPTID<p/>
	 * 電子表單列印套版版本ID
	 */
	@Size(max=32)
	@Column(name="RPTID", length=32, columnDefinition="VARCHAR(32)")
	private String rptId;
	
	/**
	 * 戶況 
	 * 抓LNF030_STATUS<p/>
	 * 1：一般, 2：逾期, 3：催收, 4：追索債權
	 */
	@Size(max=1)
	@Column(name="CASESTATUS", length=1, columnDefinition="CHAR(1)")
	private String caseStatus;

	/**
	 * 企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String docType;
	
	/** 負責人姓名 **/
	@Column(name="CHAIRMANNAME", length=120, columnDefinition="VARCHAR(120)")
	private String chairmanName;
	
	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得放款帳號 **/
	public String getLoanNo() {
		return this.loanNo;
	}
	/** 設定放款帳號 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}

	/** 取得配偶姓名 **/
	public String getMName() {
		return this.mName;
	}
	/** 設定配偶姓名 **/
	public void setMName(String value) {
		this.mName = value;
	}

	/** 取得共同借款人姓名 **/
	public String getCoCustName() {
		return this.coCustName;
	}
	/** 設定共同借款人姓名 **/
	public void setCoCustName(String value) {
		this.coCustName = value;
	}

	/** 取得保證人姓名 **/
	public String getGuarantor() {
		return this.guarantor;
	}
	/** 設定保證人姓名 **/
	public void setGuarantor(String value) {
		this.guarantor = value;
	}

	/** 取得檔案號碼 **/
	public String getCaseNo() {
		return this.caseNo;
	}
	/** 設定檔案號碼 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 取得製表日期 **/
	public Date getCaseDate() {
		return this.caseDate;
	}
	/** 設定製表日期 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/** 取得案件核准日期 **/
	public Date getCaseApproveTime() {
		return this.caseApproveTime;
	}
	/** 設定案件核准日期 **/
	public void setCaseApproveTime(Date value) {
		this.caseApproveTime = value;
	}

	/** 取得案件經辦ID **/
	public String getCaseApprId() {
		return this.caseApprId;
	}
	/** 設定案件經辦ID **/
	public void setCaseApprId(String value) {
		this.caseApprId = value;
	}

	/** 取得案件經辦 **/
	public String getCaseAppr() {
		return this.caseAppr;
	}
	/** 設定案件經辦 **/
	public void setCaseAppr(String value) {
		this.caseAppr = value;
	}

	/** 取得案件覆核人員ID **/
	public String getCaseRecheckId() {
		return this.caseRecheckId;
	}
	/** 設定案件覆核人員ID **/
	public void setCaseRecheckId(String value) {
		this.caseRecheckId = value;
	}

	/** 取得案件覆核人員 **/
	public String getCaseRecheck() {
		return this.caseRecheck;
	}
	/** 設定案件覆核人員 **/
	public void setCaseRecheck(String value) {
		this.caseRecheck = value;
	}

	/** 取得案件甲級主管ID **/
	public String getCaseBossId() {
		return this.caseBossId;
	}
	/** 設定案件甲級主管ID **/
	public void setCaseBossId(String value) {
		this.caseBossId = value;
	}

	/** 取得案件甲級主管 **/
	public String getCaseBoss() {
		return this.caseBoss;
	}
	/** 設定案件甲級主管 **/
	public void setCaseBoss(String value) {
		this.caseBoss = value;
	}

	/** 取得案件結清日 **/
	public Date getCaseCancelDate() {
		return this.caseCancelDate;
	}
	/** 設定案件結清日 **/
	public void setCaseCancelDate(Date value) {
		this.caseCancelDate = value;
	}

	/** 取得保管人員ID **/
	public String getCustodianId() {
		return this.custodianId;
	}
	/** 設定保管人員ID **/
	public void setCustodianId(String value) {
		this.custodianId = value;
	}

	/** 取得保管人員 **/
	public String getCustodian() {
		return this.custodian;
	}
	/** 設定保管人員 **/
	public void setCustodian(String value) {
		this.custodian = value;
	}

	/** 
	 * 取得存放地點<p/>
	 * 00: 檔案卷及保管袋<br/>
	 *  01: 本行
	 */
	public String getStorageLocation() {
		return this.storageLocation;
	}
	/**
	 *  設定存放地點<p/>
	 *  00: 檔案卷及保管袋<br/>
	 *  01: 本行
	 **/
	public void setStorageLocation(String value) {
		this.storageLocation = value;
	}

	/** 取得保存最後日期 **/
	public Date getFinalDate() {
		return this.finalDate;
	}
	/** 設定保存最後日期 **/
	public void setFinalDate(Date value) {
		this.finalDate = value;
	}

	/** 取得銷毀日期 **/
	public Date getDestroyDate() {
		return this.destroyDate;
	}
	/** 設定銷毀日期 **/
	public void setDestroyDate(Date value) {
		this.destroyDate = value;
	}

	/** 
	 * 取得RPTID<p/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}
	/**
	 *  取得戶況
	 *  設定RPTID<p/>
	 *  電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}
	
	/** 
	 * 抓LNF030_STATUS<p/>
	 * 1：一般, 2：逾期, 3：催收, 4：追索債權
	 */
	public String getCaseStatus() {
		return caseStatus;
	}
	
	/** 
	 * 設定戶況
	 * 抓LNF030_STATUS<p/>
	 * 1：一般, 2：逾期, 3：催收, 4：追索債權
	 */
	public void setCaseStatus(String caseStatus) {
		this.caseStatus = caseStatus;
	}
	
	/**
	 * 取得企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	public String getDocType() {
		return this.docType;
	}

	/**
	 * 設定企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}
	
	/**
	 * 設定負責人姓名
	 */
	public void setChairmanName(String chairmanName) {
		this.chairmanName = chairmanName;
	}
	
	/**
	 * 取得負責人姓名
	 */
	public String getChairmanName() {
		return chairmanName;
	}
	
}
