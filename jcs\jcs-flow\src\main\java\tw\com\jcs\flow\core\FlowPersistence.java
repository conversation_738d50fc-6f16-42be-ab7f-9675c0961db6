package tw.com.jcs.flow.core;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.concurrent.ConcurrentHashMap;

import org.owasp.esapi.ESAPI;
import org.owasp.esapi.codecs.DB2Codec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.ArgumentPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowCallbackHandler;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.node.ForkNode;
import tw.com.jcs.flow.provider.DocStatusConverter;
import tw.com.jcs.flow.provider.FlowHandler;
import tw.com.jcs.flow.query.impl.QueryImpl;

/**
 * <pre>
 * FlowPersistence
 * </pre>
 * 
 * @since 2023年1月9日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月9日
 *          </ul>
 */
public class FlowPersistence extends FlowEngineUnit {
    private static final String EXCEPTION_STR = "@EXCEPTION@";
    private static final String SEPARATOR_FLD = "|";
    private static final String SEPARATOR_REC = "^";
    private static final int MAX_INSTDATA_SIZE = 1800;

    private static final Logger logger = LoggerFactory.getLogger(FlowPersistence.class);

    private static final int BUF_SIZE = 512;

    String tableInst;
    String tableSeq;
    String tableInstHistory;
    String tableSeqHistory;

    JdbcTemplate jdbcTemplate;

    NamedParameterJdbcTemplate named;

    /**
     * constructor
     * 
     * @param engine
     */
    public FlowPersistence(FlowEngineImpl engine) {
        super(engine);
        jdbcTemplate = new JdbcTemplate(engine.dataSource);
        named = new NamedParameterJdbcTemplate(engine.dataSource);
    }

    public void setInstanceTable(String tableInst) {
        this.tableInst = tableInst;
    }

    public void setSequenceTable(String tableSeq) {
        this.tableSeq = tableSeq;
    }

    public void setInstanceHistoryTable(String tableInstHistory) {
        this.tableInstHistory = tableInstHistory;
    }

    public void setSequenceHistoryTable(String tableSeqHistory) {
        this.tableSeqHistory = tableSeqHistory;
    }

    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 依給定的流程ID，取得流程實體
     * 
     * @param instId
     *            流程ID
     * @return
     */
    public FlowInstance getInstance(Object instId) {
        List<FlowInstance> list = queryForInstance(instId);
        if (list.size() > 0) {
            return list.iterator().next();
        }
        return null;
    }

    /**
     * 依給定的流程ID，取得所有的流程實體
     * 
     * @param ids
     *            流程ID(複數)
     * @return
     */
    public List<FlowInstance> queryForInstance(Object... ids) {
        List<FlowInstance> list = new LinkedList<FlowInstance>();

        // 先到Pool中查詢，是否已載入流程實體，未載入的實體才進行查詢
        List<Object> idList = new ArrayList<Object>();
        for (Object id : ids) {
            FlowInstance inst = engine.getPooledInstance(id);
            if (inst == null) {
                idList.add(id);
            } else if (inst.getEndTime() == null) {
                list.add(inst);
            }
        }

        // 如果不是全都在Pool中，則進行查詢
        if (idList.size() > 0) {
            StringBuilder sql = new StringBuilder(BUF_SIZE);
            // [refs #44] 20220623 SQL Injection 取用變數前透過ESAPI encode
            String enTableInst = ESAPI.encoder().encodeForSQL(new DB2Codec(), tableInst);
            String enTableSeq = ESAPI.encoder().encodeForSQL(new DB2Codec(), tableSeq);
            sql.append(" select").append("   inst.*,").append("   seq.*,").append("   inst2.instId subInstId,").append("   inst2.pState subpState").append(" from ").append(enTableInst).append(" inst")
                    .append("   inner join ").append(enTableSeq).append(" seq").append("   on seq.instId=inst.instId and seq.endTime is null").append("   left join ").append(enTableInst)
                    .append(" inst2").append("   on inst2.pInstId=inst.instId").append(" where inst.instId in (");

            logger.info("\n idList.size()===>" + idList.size());
            logger.info("\n ids.length===>" + ids.length);

            // Rex 2012_03_13有時候id.length 與 idList.size()長度不相等
            for (int i = 0; i < idList.size(); ++i) {
                if (i > 0)
                    sql.append(',');
                sql.append('?');
            }
            sql.append(')');

            logger.info("\n flowSql beign===>");
            logger.info("\n" + sql.toString());

            jdbcTemplate.query(sql.toString(), new ArgumentPreparedStatementSetter(idList.toArray(new Object[idList.size()])), new FlowInstanceRowHandler(list));

            logger.info("\n flowSql end ===>");
        }
        return list;
    }

    /**
     * 依給定的Query物件(ExecutionService所產生的)，查詢所有的流程實體
     * 
     * @param query
     *            Query物件
     * @return
     */
    public List<FlowInstance> queryForInstance(QueryImpl query) {
        Map<String, Object> para = new HashMap<String, Object>();

        StringBuilder sql1 = new StringBuilder(BUF_SIZE);
        // [refs #44] 20220623 SQL Injection 取用變數前透過ESAPI encode
        String enTableInst = ESAPI.encoder().encodeForSQL(new DB2Codec(), tableInst);
        String enTableInstHistory = ESAPI.encoder().encodeForSQL(new DB2Codec(), tableInstHistory);
        String enTableSeq = ESAPI.encoder().encodeForSQL(new DB2Codec(), tableSeq);
        String enTableSeqHistory = ESAPI.encoder().encodeForSQL(new DB2Codec(), tableSeqHistory);
        sql1.append(" select").append("   inst.*,").append("   seq.*,").append("   inst2.instId subInstId,").append("   inst2.pState subpState").append(" from ")
                .append(query.isHistory() ? enTableInstHistory : enTableInst).append(" inst").append("   inner join ").append(query.isHistory() ? enTableSeqHistory : enTableSeq).append(" seq")
                .append("   on seq.instId=inst.instId").append("   left join ").append(query.isHistory() ? enTableInstHistory : enTableInst).append(" inst2").append("   on inst2.pInstId=inst.instId")
                .append(" where ");
        StringBuffer sql = new StringBuffer("");
        if (query.getId() != null) {
            para.put("instId", query.getId());
            sql.append(" and inst.instId=:instId");
        }
        // 是否要包含流程執行的歷史資訊
        if (!(query.isAll() || query.isHistory())) {
            sql.append(" and seq.endTime is null");
        }
        if (query.getDefinitionId() != null) {
            para.put("defName", query.getDefinitionId());
            sql.append(" and inst.defName=:defName");
        }
        if (query.getStateId() != null) {
            para.put("state", query.getStateId());
            sql.append(" and inst.state=:state");
        }
        if (query.getRoleId() != null) {
            para.put("roleId", query.getRoleId());
            sql.append(" and seq.roleId=:roleId");
        }
        if (query.getUserId() != null) {
            para.put("userId", query.getUserId());
            sql.append(" and seq.userId=:userId and seq.deptId=:deptId");
        }
        if (query.getDeptId() != null) {
            para.put("deptId", query.getDeptId());
            sql.append(" and seq.deptId=:deptId");
        }
        if (sql.length() > 0)
            sql1.append(sql.substring(4));
        final List<FlowInstance> list = new LinkedList<FlowInstance>();
        logger.info("[queryForInstance]==>" + sql.toString());
        named.query(sql1.toString(), para, new RowMapper<FlowInstance>() {
            FlowInstanceRowHandler handler = new FlowInstanceRowHandler(list);

            @Override
            public FlowInstance mapRow(ResultSet rs, int rowNum) throws SQLException {
                handler.processRow(rs);
                return null;
            }

        });
        return list;
    }

    /**
     * 儲存流程實體，含實體檔與序列檔，如DB不存在該實體，則建立
     * 
     * @param instance
     */
    public void saveFlowInstance(FlowInstanceImpl instance) {
        saveInstance(instance);
        saveSequence(instance);
    }

    /**
     * 儲存流程實體主檔，如DB不存在該實體，則建立
     * 
     * @param instance
     *            流程實體
     */
    public void saveInstance(FlowInstanceImpl instance) {
        try {
            StringBuilder sql = new StringBuilder(BUF_SIZE);
            int count = 0;
            String enTableInst = ESAPI.encoder().encodeForSQL(new DB2Codec(), tableInst);
            sql.setLength(0);
            sql.append(" update ").append(enTableInst).append(" set data=? where instId=?");

            String data = engine.jsonMapper.writeValueAsString(instance.getData());
            count = jdbcTemplate.update(sql.toString(), new Object[] { data, instance.getId() });

            if (count == 0) {
                // 如果不存在，則新增
                sql.setLength(0);
                sql.append(" insert into ").append(enTableInst).append(" (instId, defName, pInstId, pState, data)").append(" values (?,?,?,?,?)");

                jdbcTemplate.update(sql.toString(), new Object[] { instance.getId(), instance.getDefinition().getName(), instance.getParentInstanceId(), instance.getParentInstanceState(), data });
            }
        } catch (Exception e) {
            logger.error("[{}] Can't save instance", instance.getId());
            throw new FlowException("Can't save instance", e);
        }
    }

    /**
     * 儲存流程實體目前的處理序列，如DB不存在該實體，則建立
     * 
     * @param instance
     *            流程實體
     */
    public void saveSequence(FlowInstanceImpl instance) {
        try {

            StringBuilder sql = new StringBuilder(BUF_SIZE);
            String instdata = this.getInstData(instance);
            String enTableSeq = ESAPI.encoder().encodeForSQL(new DB2Codec(), tableSeq);
            sql.append(" update ").append(enTableSeq).append(" set state=? , userId=? , roleId=? , deptId=? , beginTime=? , endTime=? ,INSTDATA=?").append(" where instId=? and seq=?");

            int count = jdbcTemplate.update(sql.toString(), new Object[] { instance.getState(), instance.getUserId(), instance.getRoleId(), instance.getDeptId(), instance.getBeginTime(),
                    instance.getEndTime(), instdata, instance.getId(), instance.getSeq() });

            if (count == 0) {

                // 如果不存在，則新增
                sql.setLength(0);
                sql.append(" insert into ").append(enTableSeq).append(" (instId, seq, state, userId, roleId, deptId, beginTime, endTime, INSTDATA)").append(" values (?,?,?,?,?,?,?,?,?)");

                jdbcTemplate.update(sql.toString(), new Object[] { instance.getId(), instance.getSeq(), instance.getState(), instance.getUserId(), instance.getRoleId(), instance.getDeptId(),
                        instance.getBeginTime(), instance.getEndTime(), instdata });
            }
        } catch (Exception e) {
            logger.error("[{}#{}] Can't save instance sequence", instance.getId(), instance.getSeq());
            throw new FlowException("Can't save instance sequence", e);
        }
    }

    /**
     * 將流程實體從現況檔移到歷史檔(包含其子流程)
     * 
     * @param id
     *            流程編號
     */
    public void moveToHistory(Object id) {
        Object[] para = new Object[] { id, id };
        StringBuilder sql = new StringBuilder(BUF_SIZE);

        // 移到歷史檔(序列)
        sql.append(" insert into ").append(tableSeqHistory).append(" (InstId, Seq, State, UserId, RoleId, DeptId, BeginTime, EndTime,INSTDATA)")
                .append(" select InstId, Seq, State, UserId, RoleId, DeptId, BeginTime, coalesce(EndTime, current timestamp),INSTDATA from ").append(tableSeq).append(" where instId in (")
                .append("   select instId from ").append(tableInst).append("   where instId=? or pInstId=?").append(" )");
        jdbcTemplate.update(sql.toString(), para);

        // 移到歷史檔(實體)
        sql.setLength(0);
        sql.append(" insert into ").append(tableInstHistory).append(" (InstId, DefName, PInstId, PState, Data)").append(" select InstId, DefName, PInstId, PState, Data from ").append(tableInst)
                .append(" where instId=? or pInstId=?");
        jdbcTemplate.update(sql.toString(), para);

        // 刪除現況檔(序列)
        sql.setLength(0);
        sql.append(" delete from ").append(tableSeq).append(" where instId in (").append("   select instId from ").append(tableInst).append("   where instId=? or pInstId=?").append(" )");
        jdbcTemplate.update(sql.toString(), para);

        // 刪除現況檔(實體)
        sql.setLength(0);
        sql.append(" delete from ").append(tableInst).append(" where instId=? or pInstId=?");
        jdbcTemplate.update(sql.toString(), para);

        engine.removeInstanceFromPool(id);
    }

    /**
     * <pre>
     * 用來處理ProcessInstance的RowCallbackHandler
     * </pre>
     * 
     * @since 2023年1月9日
     * <AUTHOR> @version
     *          <ul>
     *          <li>2023年1月9日
     *          </ul>
     */
    class FlowInstanceRowHandler implements RowCallbackHandler {

        FlowEngineImpl engine;
        FlowInstanceImpl instance;
        List<FlowInstance> list;

        public FlowInstanceRowHandler(List<FlowInstance> list) {
            this.list = list;
            engine = FlowPersistence.this.engine;
        }

        @SuppressWarnings({ "unchecked", "rawtypes" })
        public void processRow(ResultSet rs) throws SQLException {
            Object id = rs.getObject("instId");
            if (instance == null || instance.getId().equals(id)) {
                // 先嘗試從Pool中取得流程實體的參考(為了參考的一致性，避免需sync的問題)
                instance = engine.getPooledInstance(id);
                if (instance == null) {
                    // 如果Pool中沒有，則建立新的實體，並新增到Pool中
                    instance = new FlowInstanceImpl(engine);
                    instance.setId(id);
                    instance.setDefinition(engine.getDefinition(rs.getString("defName")));
                    instance.setParentInstanceId(rs.getObject("pInstId"));
                    instance.setParentInstanceState(rs.getString("pState"));
                    engine.addInstanceToPool(instance);
                    try {
                        // 轉換ForkStack
                        Object _list = instance.getAttribute(ForkNode.FORK_STACK);
                        if (_list != null) {
                            Stack<String> stack = new Stack<String>();
                            stack.addAll((List) _list);
                            instance.setAttribute(ForkNode.FORK_STACK, stack);
                        }
                    } catch (Exception e) {
                        logger.error("[{}] Can't set instance data({})", instance.getId(), rs.getString("data"));
                        throw new FlowException("Can't set instance data.", e);
                    }
                }
                instance.setSeq(rs.getInt("seq"));
                instance.setState(rs.getString("state"));
                instance.setBeginTime(rs.getDate("beginTime"));
                instance.setEndTime(rs.getDate("endTime"));
                instance.setRoleId(rs.getString("roleId"));
                instance.setUserId(rs.getString("userId"));
                instance.setDeptId(rs.getString("deptId"));
                try {
                    instance.setData(engine.jsonMapper.readValue(rs.getString("data"), ConcurrentHashMap.class));
                } catch (Exception e) {
                    logger.error("[{}] Can't set instance data({})", instance.getId(), rs.getString("data"));
                    throw new FlowException("Can't set instance data.", e);
                }
                list.add(instance);
            }

            // 判斷是否有子流程，並處理之
            Object subInstId = rs.getObject("subInstId");
            String pState = rs.getString("subpState");
            if (pState != null && !"".equals(pState)) {
                List<Object> subList = instance.getSubInstanceList().get(pState);
                if (subList == null) {
                    subList = new LinkedList<Object>();
                    instance.getSubInstanceList().put(pState, subList);
                }
                subList.add(subInstId);
            }
        }

    }

    /**
     * 將flowinst轉成字串
     * 
     * @param instance
     *            流程實體
     * @return flowinst string
     */
    private String getInstData(FlowInstanceImpl instance) {
        long t1 = System.currentTimeMillis();

        StringBuilder str = new StringBuilder();
        str.append(instance.getId()).append(SEPARATOR_FLD);
        str.append(instance.getDefinition().getName()).append(SEPARATOR_FLD);
        str.append(instance.getParentInstanceId()).append(SEPARATOR_FLD);
        str.append(instance.getParentInstanceState()).append(SEPARATOR_FLD);

        String instDataStr = null;
        try {
            String status = this.getStatus(instance);

            str.append(engine.jsonMapper.writeValueAsString(instance.getData())).append(SEPARATOR_FLD);
            str.append(instance.getSeq()).append(SEPARATOR_FLD);
            str.append(status);

            // UFOJ@20120313 增加取得Parent的inst資料
            if (instance.getParentInstanceId() != null && !"".equals(instance.getParentInstanceId())) {
                str.append(this.getPInstData(String.valueOf(instance.getParentInstanceId())));
            }
        } catch (Exception e) {
            logger.error("[getInstData]EXCEPTION!!", e);
            str.append(EXCEPTION_STR);
        } finally {
            instDataStr = str.toString();
            if (instDataStr.getBytes().length > MAX_INSTDATA_SIZE) {
                instDataStr = Util.truncateToFitUtf8ByteLength(instDataStr, MAX_INSTDATA_SIZE);
            }
            if (logger.isDebugEnabled()) {
                logger.debug("[getInstData] cost= {} ms", (System.currentTimeMillis() - t1));
            }
        }
        return instDataStr;
    }

    /**
     * 取得flow的status
     * 
     * @param instance
     *            流程實體
     * @return
     */
    private String getStatus(FlowInstanceImpl instance) {
        String status = instance.getStatus();
        if (status != null && !"".equals(status.trim())) {
            FlowHandler handler = instance.getDefinition().getHandler();
            if (status.getBytes().length == status.length() && status.length() == 3) {
                // FLOW XML內直接存代碼
                return status;
            } else {
                if (handler instanceof DocStatusConverter) {
                    status = ((DocStatusConverter) handler).getDocStatus(instance);
                }
                return status;
            }
        }
        return "";
    }

    /**
     * 
     * @param instId
     *            流程ID
     * @return
     */
    private String getPInstData(String instId) {
        StringBuffer sql = new StringBuffer("select  INSTID,DEFNAME,PINSTID,PSTATE,DATA,").append(" (SELECT max(FSEQ.SEQ)  from  ").append(this.tableSeq)
                .append(" FSEQ where FSEQ.INSTID=FINST.INSTID) AS SEQ from ").append(this.tableInst).append(" FINST where INSTID=? ");

        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql.toString(), instId);

        if (result == null || result.size() == 0) {
            return "";
        }
        Map<String, Object> flowinst = result.get(0);
        String pinstId = String.valueOf(flowinst.get("PINSTID"));
        if (pinstId != null && !"".equals(pinstId.trim()) && !"null".equals(pinstId.trim())) {
            return this.getPInstData(pinstId);
        } else {
            return this.getPInstDataSring(flowinst);
        }
    }

    /**
     * 取得流程實體資料字串
     * 
     * @param flowinst
     * @return
     */
    private String getPInstDataSring(Map<String, Object> flowinst) {
        if (flowinst == null) {
            return "";
        }
        StringBuilder str = new StringBuilder(SEPARATOR_REC);
        str.append(flowinst.get("INSTID")).append(SEPARATOR_FLD);
        str.append(flowinst.get("DEFNAME")).append(SEPARATOR_FLD);
        str.append(flowinst.get("PINSTID")).append(SEPARATOR_FLD);
        str.append(flowinst.get("PSTATE")).append(SEPARATOR_FLD);
        try {
            str.append(engine.jsonMapper.writeValueAsString(flowinst.get("DATA"))).append(SEPARATOR_FLD);
            str.append(flowinst.get("SEQ")).append(SEPARATOR_FLD);
            str.append("");

        } catch (Exception e) {
            logger.error("[getInstData]EXCEPTION!!", e);
            str.append(EXCEPTION_STR);
        }
        return str.toString();
    }
}
