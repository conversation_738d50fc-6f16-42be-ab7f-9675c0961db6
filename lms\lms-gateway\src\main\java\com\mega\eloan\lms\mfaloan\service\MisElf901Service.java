/* 
 *MisELF501Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 董事具有控制從屬關係公司資料檔
 * </pre>
 * 
 * @since 2019/11/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/11/07,Benrison,new
 *          </ul>
 */
public interface MisElf901Service {
	
	/**
	 * 新增紀錄
	 * @param map
	 * @return
	 */
	public int[] addElf901(List<Map<String, Object>> addBeanList);
}
