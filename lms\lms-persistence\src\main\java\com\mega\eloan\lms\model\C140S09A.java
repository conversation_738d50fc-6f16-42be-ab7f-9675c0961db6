package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.RelativeMeta;


/**
 * <pre>
 * C140S09A model.
 * </pre>
 * 
 * @since 2011/10/27
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/27,<PERSON>,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140S09A-entity-graph", attributeNodes = { @NamedAttributeNode("c140m01a") })
@Entity
@Table(name="C140S09A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C140S09A extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 全體授信額度 */
	@Column(name="G_ABK_AMT", precision=12)
	private BigDecimal gAbkAmt;
	/** 全體授信餘額 */
	@Column(name="G_ABK_BAL", precision=12)
	private BigDecimal gAbkBal;
	/** 負責人 */
	@Column(name="G_HOLDER", length=60)
	private String gHolder;

	@Column(name = "g_ejcicid", length = 10)
	private String gEjcicId;
	/** 統一編號 */
	@Column(name="G_ID", length=10)
	private String gId;
	/** 集團代號 */
	@Column(length=10)
	private String grpId;
	/** 重複序號 */
	@Column(length=1)
	private String dupNo;
	/** 本行DBU授信額度 */
	@Column(name="G_MBK_AMT", precision=12)
	private BigDecimal gMbkAmt;
	/** 本行DBU授信餘額 */
	@Column(name="G_MBK_BAL", precision=12)
	private BigDecimal gMbkBal;
	/** 企業名稱
	 * J-105-0080-001 Web e-Loan授信管理系統集團轄下公司名稱欄位放大為38個全形字
	 * length=60->length=120
	 *  */
	@Column(name="G_NA", length=120)
	private String gNa;
	/** 本行OBU授信額度 */
	@Column(name="G_OBU_AMT", precision=12)
	private BigDecimal gObuAmt;
	/** 本行OBU授信餘額 */
	@Column(name="G_OBU_BAL", precision=12)
	private BigDecimal gObuBal;
	/** 本行海外授信額度 */
	@Column(name="G_OVS_AMT", precision=12)
	private BigDecimal gOvsAmt;
	/** 本行海外授信餘額 */
	@Column(name="G_OVS_BAL", precision=12)
	private BigDecimal gOvsBal;
	/** 國內分行小計額度 */
	@Column(name = "TOT_LOCAL_CONT", precision = 12)
	private BigDecimal totLocalCont;
	/** 國內分行小計餘額 */
	@Column(name = "TOT_LOCAL_LOAN", precision = 12)
	private BigDecimal totLocalLoan;
	/** JPQ 額度 */
	@Column(name = "JPQ_COUNT_CONT", precision = 12)
	private BigDecimal jpqCountCont;
	/** JPQ 餘額 */
	@Column(name = "JPQ_COUNT_LOAN", precision = 12)
	private BigDecimal jpqCountLoan;
	
	//bi-directional many-to-one association to C140M01A
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
            @JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false) })
	private C140M01A c140m01a;

	/**
	 * 全體授信額度
	 * 
	 * @return the gAbkAmt
	 */    
	public BigDecimal getGAbkAmt() {
		return this.gAbkAmt;
	}
	
	/**
	 * 全體授信額度
	 * 
	 * @param gAbkAmt
	 *            the gAbkAmt to set
	 */	
	public void setGAbkAmt(BigDecimal gAbkAmt) {
		this.gAbkAmt = gAbkAmt;
	}

	/**
	 * 全體授信餘額
	 * 
	 * @return the gAbkBal
	 */	
	public BigDecimal getGAbkBal() {
		return this.gAbkBal;
	}

	/**
	 * 全體授信餘額
	 * 
	 * @param gAbkBal
	 *            the gAbkBal to set
	 */	
	public void setGAbkBal(BigDecimal gAbkBal) {
		this.gAbkBal = gAbkBal;
	}

	/**
	 * 負責人
	 * 
	 * @param gHolder
	 *            the gHolder to set
	 */	
	public String getGHolder() {
		return this.gHolder;
	}

	/**
	 * 負責人
	 * 
	 * @param gHolder
	 *            the gHolder to set
	 */	
	public void setGHolder(String gHolder) {
		this.gHolder = gHolder;
	}

	/**
	 * 統一編號
	 * 
	 * @return the gId
	 */
	public String getGId() {
		return this.gId;
	}

	/**
	 * 統一編號
	 * 
	 * @param gId
	 *            the gId to set
	 */
	public void setGId(String gId) {
		this.gId = gId;
	}

	/**
	 * 集團代號
	 * 
	 * @return the grpId
	 */
	public String getGrpId() {
		return this.grpId;
	}

	/**
	 * 集團代號
	 * 
	 * @param grpId
	 *            the grpId to set
	 */
	public void setGrpId(String grpId) {
		this.grpId = grpId;
	}

	/**
	 * 重複序號
	 * 
	 * @return the dupNo
	 */	
	public String getDupNo() {
		return this.dupNo;
	}

	/**
	 * 重複序號
	 * 
	 * @param dupNo
	 *            the dupNo to set
	 */	
	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	/**
	 * 本行DBU授信額度
	 * 
	 * @return the gMbkAmt
	 */	
	public BigDecimal getGMbkAmt() {
		return this.gMbkAmt;
	}

	/**
	 * 本行DBU授信額度
	 * 
	 * @param gMbkAmt
	 *            the gMbkAmt to set
	 */	
	public void setGMbkAmt(BigDecimal gMbkAmt) {
		this.gMbkAmt = gMbkAmt;
	}

	/**
	 * 本行DBU授信餘額
	 * 
	 * @return the gMbkBal
	 */	
	public BigDecimal getGMbkBal() {
		return this.gMbkBal;
	}

	/**
	 * 本行DBU授信餘額
	 * 
	 * @param gMbkBal
	 *            the gMbkBal to set
	 */	
	public void setGMbkBal(BigDecimal gMbkBal) {
		this.gMbkBal = gMbkBal;
	}

	/**
	 * 企業名稱
	 * 
	 * @return the gNa
	 */	
	public String getGNa() {
		return this.gNa;
	}

	/**
	 * 企業名稱
	 * 
	 * @param gNa
	 *            the gNa to set
	 */	
	public void setGNa(String gNa) {
		this.gNa = gNa;
	}

	/**
	 * 本行OBU授信額度
	 * 
	 * @return the gObuAmt
	 */	
	public BigDecimal getGObuAmt() {
		return this.gObuAmt;
	}

	/**
	 * 本行OBU授信額度
	 * 
	 * @param gObuAmt
	 *            the gObuAmt to set
	 */	
	public void setGObuAmt(BigDecimal gObuAmt) {
		this.gObuAmt = gObuAmt;
	}

	/**
	 * 本行OBU授信餘額
	 * 
	 * @return the gObuBal
	 */	
	public BigDecimal getGObuBal() {
		return this.gObuBal;
	}

	/**
	 * 本行OBU授信餘額
	 * 
	 * @param gObuBal
	 *            the gObuBal to set
	 */	
	public void setGObuBal(BigDecimal gObuBal) {
		this.gObuBal = gObuBal;
	}

	/**
	 * 本行海外授信額度
	 * 
	 * @return the gOvsAmt
	 */	
	public BigDecimal getGOvsAmt() {
		return this.gOvsAmt;
	}

	/**
	 * 本行海外授信額度
	 * 
	 * @param gOvsAmt
	 *            the gOvsAmt to set
	 */	
	public void setGOvsAmt(BigDecimal gOvsAmt) {
		this.gOvsAmt = gOvsAmt;
	}

	/**
	 * 本行海外授信餘額
	 * 
	 * @return the gOvsBal
	 */	
	public BigDecimal getGOvsBal() {
		return this.gOvsBal;
	}

	/**
	 * 本行海外授信餘額
	 * 
	 * @param gOvsBal
	 *            the gOvsBal to set
	 */	
	public void setGOvsBal(BigDecimal gOvsBal) {
		this.gOvsBal = gOvsBal;
	}

	public C140M01A getC140m01a() {
		return this.c140m01a;
	}

	public void setC140m01a(C140M01A c140m01a) {
		this.c140m01a = c140m01a;
	}

	/**
	 * 國內分行小計額度
	 * 
	 * @return the totLocalCont
	 */
	public BigDecimal getTotLocalCont() {
		return totLocalCont;
	}

	/**
	 * 國內分行小計額度
	 * 
	 * @param totLocalCont
	 *            the totLocalCont to set
	 */
	public void setTotLocalCont(BigDecimal totLocalCont) {
		this.totLocalCont = totLocalCont;
	}

	/**
	 * 國內分行小計餘額
	 * 
	 * @return the totLocalLoan
	 */
	public BigDecimal getTotLocalLoan() {
		return totLocalLoan;
	}

	/**
	 * 國內分行小計餘額
	 * 
	 * @param totLocalLoan
	 *            the totLocalLoan to set
	 */
	public void setTotLocalLoan(BigDecimal totLocalLoan) {
		this.totLocalLoan = totLocalLoan;
	}

	/**
	 * JPQ 額度
	 * 
	 * @return the jpqCountCont
	 */
	public BigDecimal getJpqCountCont() {
		return jpqCountCont;
	}

	/**
	 * JPQ 額度
	 * 
	 * @param jpqCountCont
	 *            the jpqCountCont to set
	 */
	public void setJpqCountCont(BigDecimal jpqCountCont) {
		this.jpqCountCont = jpqCountCont;
	}

	/**
	 * JPQ 餘額
	 * 
	 * @return the jpqCountLoan
	 */
	public BigDecimal getJpqCountLoan() {
		return jpqCountLoan;
	}

	/**
	 * JPQ 餘額
	 * 
	 * @param jpqCountLoan
	 *            the jpqCountLoan to set
	 */
	public void setJpqCountLoan(BigDecimal jpqCountLoan) {
		this.jpqCountLoan = jpqCountLoan;
	}

	public String getGEjcicId() {
		return gEjcicId;
	}

	public void setGEjcicId(String gEjcicId) {
		this.gEjcicId = gEjcicId;
	}	
	
}