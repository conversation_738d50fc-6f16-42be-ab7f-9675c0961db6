/* 
 * CLS1021M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.panels.CLS1021S01Panel;
import com.mega.eloan.lms.model.C102M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 購置房屋擔保放款風險權數檢核表(自用住宅貸款檢核表)
 * </pre>
 * 
 * @since 2011/10/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new</li>
 *          <li>2022/06,EL08034,J-111-0096 Web e-Loan消金因應不動產暴險以貸放比率(LTV)決定適用之風險權數，報表名稱改為「自用住宅貸款檢核表」</li>
 *          </ul>
 */
@Controller
@RequestMapping("/lms/cls1021m01/{page}")
public class CLS1021M01Page extends AbstractEloanForm {

	@Autowired
	DocCheckService docCheckService;

	@Autowired
	CLSService clsService;
	
	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params,
				getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		addAclLabel(model,
				new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
						AuthType.Accept, CreditDocStatusEnum.海外_待覆核,
						CreditDocStatusEnum.先行動用_待覆核));
		renderJsI18N(CLS1021M01Page.class);
		
		C102M01A c102m01a = clsService.findC102M01A_oid(Util.trim(params.getString(EloanConstants.OID)));
				
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		renderJsI18N(CLS1021M01Page.class);
		Panel panel = getPanel(page, params, c102m01a);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
	}// ;

	// 頁籤
	public Panel getPanel(int index, PageParameters params, C102M01A c102m01a) {
		Panel panel = null;
		// switch (index) {
		// case 1:

		panel = new CLS1021S01Panel(TAB_CTX, true, c102m01a);
		// break;
		// case 2:
		// renderRespMsgJsI18N("EFD3026"); // 多render一個msgi18n
		// // renderJsI18N(AbstractEloanPage.class, "EFD3026");
		// panel = new LMS1601S02Panel(TAB_CTX);
		// break;
		// case 3:
		// renderRespMsgJsI18N("EFD0002"); // 多render一個msgi18n
		// // renderJsI18N(AbstractEloanPage.class, "EFD0002");
		// panel = new LMS1605S03Panel(TAB_CTX, params);
		// break;
		// case 4:
		// panel = new LMS1605S04Panel(TAB_CTX);
		// break;
		// default:
		// panel = new LMS1605S01Panel(TAB_CTX);
		// break;
		// }
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C102M01A.class;
	}
	
	public static String getDocUrl() {
		return"/lms/cls1021m01";
	}
}
