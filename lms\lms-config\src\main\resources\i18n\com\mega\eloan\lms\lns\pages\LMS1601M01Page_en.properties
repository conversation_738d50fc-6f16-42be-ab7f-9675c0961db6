#==================================================
# \u52d5\u7528\u5be9\u6838\u8868 Grid
page.title=LMS1605M01 Drawdown Approval Sheet
L160M01A.mainCustId=Principal Borrower's UBN
L160M01A.mainCust=Principal Borrower
L160M01A.caseNo=Case No.
L160M01A.cntrNo=Serial Number of the Drawn Limit
L160M01A.creatorPerson=Branch Officer
L160M01A.allCanPay=Advance Drawdown
L160M01A.willFinishDate=Scheduled Full Documentation Date
L163M01A.finishDate=Completion Date
L160M01A.approveTime=Approval Date
L163M01A.CheckDate=Verification Completed
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 m01
L160M01A.title01=Drawdown Approval Sheet
L160M01A.title02=Related Reports
L160M01A.title03=Advance Drawdown Approval & Control Sheet
L160M01A.title04=IVR Phone File
L160M01A.title06=RPA Information Query
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 s01
L160M01A.caseDate=Signature Date
L160M01A.caseLvl=Approval Level
L160M01A.cntrNum=Credit Limit Serial Number
L160M01A.bt01=Select A Credit Facility Report
L160M01A.title07=Business Unit
L160M01A.managerId=Manager/Deputy Manager/Junior Manager
L160M01A.bossId=Credit Supervisor
L160M01A.reCheckId=Approver
L160M01A.apprId=Handling Officer
L160M01A.selfCntrCase=Self-produced Credit Facility Report
L160M01A.contentCntrCase=Credit Facility Report From Syndication Loan Arranger
L160M01A.title08=Please select 1 approved Case Report
L160M01A.message2=Please select a Credit Facility Report for this drawdown
L160M01A.commSno=Serial No. Of Shared Limit
L160M01A.moneyAmt=Applied Credit Limit
L160M01A.type=Nature
L140M01a.type1=New
L140M01a.type2=Renewal
L140M01a.type3=Condition Change
L140M01a.type4=Continued
L140M01a.type5=Increase
L140M01a.type6=Decrease
L140M01a.type7=Unchanged
L140M01a.type8=Cancel
L140M01a.type9=Extension
L140M01a.type10=Distress Support
L140M01a.type11=Advance Renewal
L140M01a.type12=Negotiated Settlement
L140M01a.type13=Quotation
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 s02
L160M01A.tType1=Short Term
L160M01A.tType2=Medium/Long Term
#J-110-0540_05097_B1001 Web e-Loan\u4f01\u91d1\u6388\u4fe1\u914d\u5408\u8abf\u6574E-loan\u7cfb\u7d71\u52d5\u7528\u5be9\u6838\u8868\u90e8\u5206\u5167\u5bb9
L160M01A.tType3=\u8a73\u984d\u5ea6\u52d5\u7528\u8cc7\u8a0a\u4e00\u89bd\u8868
L160M01A.tType=Credit Agreement
L160M01A.useDate=Drawdown Period
L160M01A.useFromDate=Drawdown Period
L160M01A.useEndDate=Drawdown Period - Due Date
L160M01A.useMonth=XX months since 1st drawdown date
L160M01A.useMonth1=Since 1st Drawdown Date
L160M01A.useMonth2=Months
L160M01A.useOther=Others
L160M01A.lnDate=Credit Tenor
L160M01A.lnFromDate=XX years & XX months since 1st drawdown date
L160M01A.lnEndDate=Loan Tenor
L160M01A.lnYear=Year
L160M01A.lnOther=Credit Tenor - Others
L160M01A.signDate1=Credit Agreement Signing Date
L160M01A.guCurr=Maximum Guarantee
L160M01A.guAmt=dollars only
L160M01A.guFromDate=Tenor
L160M01A.guEndDate=Maximum 5 Years
L160M01A.dMonth1=Penalty Charge Criteria
L160M01A.dRate1=Penalty Charge Criteria - Interest Rate
L160M01A.dMonth2=Pastdue More Than
L160M01A.dRate2=Charge Penalty
L160M01A.dRateAdd=Late Interest Margin
L160M01A.blackDataDate=Audit Items
L160M01A.queryDataDate=Data Inquiry Date
L160M01A.blackListTxtErr=Blacklist Search
L160M01A.blackListTxtOK=Blacklist Search Result
L160M01A.otherCase=Other Remarks
L160M01A.sign=Approval
L160M01A.comm=Review Opinion
L160M01A.allItem=Bank Common Item
L160M01A.localItem=Special Local Rules
L160M01A.selfItem=Manual Input Item
L160M01A.noNeed=Waived
L160M01A.otherfile=File Attachment
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 s03
L160M01A.unitLoanCase=Whether the case contains syndicated limits with peer banks
L160M01A.uCMainBranch=Whether the branch is the managing bank
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 s04
L163M01A.waitingItem=Pending Actions
L163M01A.willFinishDate=Scheduled Full Documentation Date
L163M01A.uploadDate=Advance Drawdown Approval Date
L163M01A.itemTrace=Follow-up Progress
L163M01A.managerId=Class A Supervisor
L163M01A.bossId=Class B Supervisor
L163M01A.appraiserId=Handling Officer
L163M01A.bfReCheckDate=Approval Date
L163M01A.check=Signature Column
L163M01A.no=\u3000
L163M01A.site=\u3000th Persons
#J-110-0547 \u70ba\u63a7\u7ba1\u5148\u884c\u52d5\u7528\u4e4b\u6388\u4fe1\u6848\u4ef6\uff0c\u589e\u52a0\u5148\u884c\u52d5\u7528\u5448\u6838\u53ca\u63a7\u5236\u8868\u9810\u5b9a\u88dc\u5168\u65e5\u671f\u4e4b\u901a\u77e5\u529f\u80fd\u3002
L163M01A.willFinishDateError=\u5148\u884c\u52d5\u7528\u63a7\u5236\u8868\u4e4b\u9810\u5b9a\u88dc\u5168\u65e5\u671f\u9700\u665a\u65bc\u4eca\u65e5\u65e5\u671f
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 s06
L163M01A.rpaItem=RPA\u8cc7\u6599\u67e5\u8a62
L163M01A.queryIdDate=\u767c\u8b49\u65e5\u671f
L163M01A.queryIdDateTitle=\u6c11\u570b
L163M01A.queryIdDateYear=\u5e74
L163M01A.queryIdDateMonth=\u6708
L163M01A.queryIdDateDay=\u65e5
L163M01A.queryIdSite=\u767c\u8b49\u5730\u9ede
L163M01A.queryIdChangeType=\u9818\u88dc\u63db\u985e\u5225
L160S01D.type=\u8cc7\u6599\u985e\u5225
L160S01D.queryTime=\u67e5\u8a62\u6642\u9593
L160S01D.docfileoid=\u5831\u8868\u6a94\u6848
L160S01D.status=\u67e5\u8a62\u72c0\u614b
L160S01D.memo=\u5099\u8a3b
L160S01D.reason=\u56de\u50b3\u7d50\u679c
L160S01D.rpaQueryReason1=\u67e5\u8a62\u689d\u4ef61
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94  \u66ab\u653e
L160M01A.branchId=Branch name
L160M01A.branchName=Branch
L160M01A.typCd=Region/Department
L160M01A.custId=Unified Business Number
L160M01A.dupNo=Repeat Serial No.
L160M01A.custName=Borrower's Name
L160M01A.unitType=Handling Unit's Category
L160M01A.ownBrId=Preparing Unit's ID
L160M01A.randomCode=Document Decode Error
L160M01A.caseGist=Agenda
L160M01A.randomcode=Random Report Code
L160M01A.ownbranch=Affiliate Bank
L160M01A.curr=Currency
L160M01A.allMoney=Total Credit Line
L160M01A.money=Dollars
L160M01A.signDate=Contract Date
L160M01A.slBank=Participating Bank/Branch
L160M01A.slBank01=Local Bank
L160M01A.slBank02=Foreign bank's branch in Taiwan
L160M01A.slBank03=Investment Trust Company
L160M01A.slBank06=Property & Life Insurance Company
L160M01A.slBank12=Foreign Bank
L160M01A.slMaster=Joint Lead Arranger
L160M01A.yes=Yes
L160M01A.no=No
L160M01A.slAccNo=Peer Account Number
L160M01A.slAmt=Allocated Loan Quantum
L160M01A.allUse=Fully Drawn
L160M01A.srcFileName=File Name
L160M01A.fileDesc=File Description
L160M01A.uploadTime=Upload Time
L160M01A.selectBoss=Number Of Credit Supervisors
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 \u9801\u7c64\u6a19\u984c
L160M01A.title09=Credit Facility Report
L160M01A.title10=Syndication Loan Allocation Table
L160M01A.title11=Borrower's & Guarantor's Profile Sheet
L160M01A.title12=Input interface for the Advance Drawdown Control Checklist
L160M01A.title13=Advance Drawdown Approval & Control Sheet
L160M01A.title14=Select 1 Credit Facility Report
L160M01A.title18=\u52d5\u64a5\u63d0\u9192\u8cc7\u8a0a
L160M01A.title19=\u52d5\u64a5\u63d0\u9192\u4e8b\u9805
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 \u6309\u9215
L160M01A.bt02=Inquiry
L160M01A.bt03=All Complete/Received
L160M01A.bt04=Attachment not required for all
L160M01A.bt05=Select Files To Attach
L160M01A.bt06=Delete
L160M01A.bt07=List of New Syndication Loan Allocated Percentages
L160M01A.bt08=Add Borrower's & Guarantor's Profile Sheet
L160M01A.bt09=Originating Branch
L160M01A.bt10=Import Borrower & Guarantor
L160M01A.bt11=Return to handling officer for correction
L160M01A.bt12=Approve Drawdown
L160M01A.bt13=Advance Drawdown Confirmation
L160M01A.bt14=Verifier
L160M01A.bt15=Printer Setting
L160M01A.bt16=Print All
L160M01A.bt17=Generate New Report
L160M01A.bt18=Close
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 error
L160M01A.error1=Multiple selections are not allowed under this function
L160M01A.error2=Please Select
L160M01A.error3=Please import the Credit Facility Report first
L160M01A.error4=The database contains no bank ID:
L160M01A.error5=Total Credit Line can not be
L160M01A.error6=The shared loan quantum can not be
L160M01A.error7=Please input
L160M01A.error8=The shared loan quantum can not exceed total credit limit
L160M01A.error9=Unable to save because the total credit limit does not equal to the shared loan quantum
L160M01A.error10=Please save first
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 \u8a0a\u606f
L160M01A.message1=Were all limits under this report utilized? If so, press [OK], other wise press [Cancel] to continue selecting the limit utilization report.
L160M01A.message3=Drawdown all limits under the Case Report
L160M01A.message4=Please make sure to input the following fields, as they can be used to calculate penalty charges when the case goes into hardcore collection. This information is not printed on reports.
L160M01A.message5=If the borrower delays principal or interest payments, then penalty charges will accrue from the maturity date (for principal) and the interest payment date (for interest). For pastdue within
L160M01A.message6=months,
L160M01A.message7=interest is charged at 
L160M01A.message8=months, interest is charged at
L160M01A.message9=Please tick "V" if received (completed), tick "X" if not received (incomplete), and tick "Waived" if not required
L160M01A.message10=For approved cases, application documents need to be photocopied and handed to Accounts, Credit Supervisor, and each circulated unit.
L160M01A.message11=Sight LC, usance LC, and export loan limits are circulated to the Foreign Currency Department; clean bill purchases are circulated to the Foreign Exchange Department; overdraft limits are circulated to the Deposits Department
L160M01A.message12=Circulated Personnel & Units: please sign and randomly check the approved document copies
L160M01A.message13=Accounts (file creation) Officer
L160M01A.message14=Foreign Currency
L160M01A.message15=Foreign Exchange
L160M01A.message16=Deposit
L160M01A.message17=Please input names in English to facilitate blacklist checks
L160M01A.message18=Only the relationship field can be left blank, if the borrower's UBN is the same as the guarantor's UBN; no other fields can be left blank
L160M01A.message19=Article 753-1 of the Civil Law states that
L160M01A.message20=persons who offer their personal guarantees for companies in which they serve as directors, supervisors, or other forms of corporate representatives will only be held liable for the liabilities incurred by their respective companies during active duty
L160M01A.message21=Therefore, if the guarantor's relationship is specified as the borrower's
L160M01A.message22=director, supervisor, or other form of corporate representative,
L160M01A.message23=the [Director's/Supervisor's Term Expiry (Expiry Date For Guarantee Obligation)] field needs to key in the year which the director's/supervisor's term ends
L160M01A.message24=Please switch to \u300eAdvance Drawdown Approval & Control Sheet\u300f Tab and Fill in\u300cScheduled Full Documentation Date\u300d Field\u3002
L160M01A.message25=Not Received (Not Completed)
L160M01A.message26=Add Borrower's & Guarantor's Profile will be cleared, whether press [Confirm] or not (Import Borrower's & Guarantor Profile)
L160M01A.message27=Whether to submit for supervisor's approval
L160M01A.message28="Borrower's & Guarantor's Profile Sheet", not entered yet, can not be submitted for supervisor's approval.
L160M01A.message29="Syndication Loan Allocation Table", not entered yet, can not be submitted for supervisor's approval.
L160M01A.message30="Syndication Loan Allocation Table", all the shared loan quantum of total not equal to total credit limit, can not be submitted for supervisor's approval.
L160M01A.message31=Supervisor's name duplicated; please select again
L160M01A.message32=Whether to return the case to the handling officer for amendments; to return, please press [OK], otherwise press [Cancel]
L160M01A.message34=Whether to proceed with approval and disbursement for this case
L160M01A.message35=Whether the "Advance Drawdown Approval & Control Sheet" is confirmed
L160M01A.message36=Whether to return the case to the handling officer for amending "Completion Date & Follow-up Progress"; to return, please press [OK], otherwise press [Cancel]
L160M01A.message37=Approved Drawdown Period
L160M01A.message38=Please input the approval date
L160M01A.message39=Not On Blacklist
L160M01A.message40=English Name Input Error; the input must be made in half-sized characters
L160M01A.message41=Possibly blacklisted
L160M01A.message42=This case is not syndicated, hence can not change the allocated percentage
L160M01A.message43=Failed to upload file
L160M01A.message44=Maybe is Black List
L160M01A.message45=Credit Limit Serial No. for non-Bank's credit Line no longer permitted & utilization
L160M01A.message46=Loans procedures are complete, We intend to Drawdown granted
L160M01A.message47=Phrases
L160M01A.message48=The related identity should be C. Common borrower, if the borrower's UBN is the same as the guarantor's UBN
L160M01A.message49=No Credit Facility Report found
L160M01A.message50=The relationship field should be left blank, if the borrower's UBN is the same as the guarantor's UBN
L160M01A.message51=Borrowers for\u300cBorrower's & Guarantor's Profile Sheet\u300dbelow, data incomplete, can not be submitted for supervisor's approval{0}
L160M01A.message52=The source of the credit limit serial No. is from Inter-Branch Credit Facility Report, can not perform data correction
L160M01A.message53=Fifth\u3001Among other things, the following items are not received (not completed), shall not be pSubmit For Supervisor's Approval:{0}
# \u4e3b\u5f9e\u50b5\u52d9\u4eba\u8cc7\u6599\u8868\u6a94 L162M01A
L162M01A.custId=Borrower's UBN
L162M01A.dupNo=Borrower's UBN Repeat Code
L162M01A.rId=Guarantor's UBN
L162M01A.rDupNo=Guarantor's UBN Repeat Code
L162M01A.rName=Guarantor's Name
L162M01A.rKindM=Relationship
L162M01A.rKindD=Relationship Sub-category
L162M01A.rCountry=Country
L162M01A.rType=Related Identity
L162M01A.dueDate=Director's/Supervisor's Term Expiry
L162M01A.dueDate2=Expiry Date For Guarantee Obligation
L162M01A.breanch=Type Of Financial Institution
L160M01A.signDate2=3~5 years
L162M01A.guaPercent=Guarantor burden the ratio of the guarantee responsibility

#J-110-0007_05097_B1001 Web e-Loan\u4f01\u91d1\u6388\u4fe1\u984d\u5ea6\u660e\u7d30\u8868\u8207\u52d5\u5be9\u8868\u589e\u52a0\u4fdd\u8b49\u4eba\u4fe1\u7528\u54c1\u8cea\u9806\u5e8f\u8a2d\u5b9a
L162M01A.priority=Credit quality priority

L162M01A.message1=\u300cBorrower's & Guarantor's Profile Sheet\u300dhas Incomplete information\uff0cdo not perform this function {0}
L162M01A.message2=The guarantor must set the priority of credit quality until the total guarantor's guaranty liability ratio of the quota reaches 100%. \u4fdd\u8b49\u4eba\u5fc5\u9700\u8a2d\u5b9a\u4fe1\u7528\u54c1\u8cea\u9806\u5e8f\uff0c\u76f4\u5230\u8a72\u984d\u5ea6\u4e4b\u4fdd\u8b49\u4eba\u4e4b\u8ca0\u62c5\u4fdd\u8b49\u8cac\u4efb\u6bd4\u7387\u5408\u8a08\u9054100%\u3002
L162M01A.message3=The guarantor's credit quality priority must be arranged in sequence starting from 1, and no skipping number is allowed. \u4fdd\u8b49\u4eba\u4fe1\u7528\u54c1\u8cea\u9806\u5e8f\u5fc5\u9808\u5f9e1\u958b\u59cb\u4f9d\u5e8f\u7de8\u6392\uff0c\u4e0d\u5f97\u8df3\u865f\u3002
L162M01A.message4=When the guarantor (general/associated) is a corporate household, the credit quality priority of the guarantor needs to be filled out. \u4fdd\u8b49\u4eba(\u4e00\u822c/\u9023\u5e36)\u70ba\u4f01\u696d\u6236\u6642\uff0c\u9700\u8981\u586b\u5217\u4fdd\u8b49\u4eba\u4fe1\u7528\u54c1\u8cea\u9806\u5e8f\u3002
L162M01A.message5=Set the priority order of each guarantor, starting from 1 in order, without skipping numbers. \u8a2d\u5b9a\u5404\u4fdd\u8b49\u4eba\u4e4b\u512a\u5148\u9806\u5e8f\uff0c\u75311\u958b\u59cb\u4f9d\u5e8f\u7de8\u6392\uff0c\u4e0d\u5f97\u8df3\u865f
L162M01A.message6=Execute Write Back Borrower's & Guarantor's Profile Sheet\u3002\u57f7\u884c\u5beb\u56de\u4e3b\u5f9e\u50b5\u52d9\u4eba\u8cc7\u6599\u8868
L162M01A.message7=Note
L162M01A.message8=Instructions
L162M01A.message9=the order of credit quality cannot be repeated.\u9806\u5e8f\u4e0d\u53ef\u91cd\u8907\u5217\u5370\u9806\u5e8f\u4e0d\u53ef\u91cd\u8907

btn.applyGuarantorCreditOrder=Obtain the guarantor\u2019s credit quality priority
btn.applyEllngteePriority=Apply the priority from Data Archiving Maintenance System
btn.setGuarantorCreditPriority=Credit quality priority setting 
btn.writeGuarantor=Write Back Borrower\u2019s & Guarantor\u2019s Profile Sheet
#J-110-0040_05097_B1001 Web e-Loan\u589e\u52a0\u300c\u672c\u884c\u570b\u5bb6\u66b4\u96aa\u662f\u5426\u4ee5\u4fdd\u8b49\u4eba\u570b\u5225\u70ba\u8a08\u7b97\u57fa\u6e96(\u53d6\u4ee3\u6700\u7d42\u98a8\u96aa\u570b\u5225)\u300d\u8a3b\u8a18
btn.btnEntireApply=Batch Setting

# \u4ee5\u4e0b\u70ba\u65b0\u589e\u7684
L160M01A.message54=Credit Limit Serial Number {0} had been marked\u3010Not signed - cancellation of quota\u3011 at {1} and shall not be used\uff0cPlease re-select!!
L160M01A.message55=\u3010\u540c\u610f\u3011\u5171\u7528\u59d3\u540d\u3001\u5730\u5740\u57fa\u672c\u8cc7\u6599
L160M01A.message56=\u3010\u4e0d\u540c\u610f\u3011\u5171\u7528\u59d3\u540d\u3001\u5730\u5740\u57fa\u672c\u8cc7\u6599
L160M01A.message57=\u3010\u540c\u610f\u3011\u5171\u7528\u59d3\u540d\u3001\u5730\u5740\u4ee5\u5916\u4e4b\u57fa\u672c\u8cc7\u6599\u53ca\u5e33\u52d9\u8cc7\u6599
L160M01A.message58=\u3010\u4e0d\u540c\u610f\u3011\u5171\u7528\u59d3\u540d\u3001\u5730\u5740\u4ee5\u5916\u4e4b\u57fa\u672c\u8cc7\u6599\u53ca\u5e33\u52d9\u8cc7\u6599
L160M01A.message65=Syndication Loan Allocation Table of the selected contract no. {0} is an unusual amount of the syndicated loan, please re-select
L160M01A.message91=\u806f\u8cb8\u6848\u53c3\u8cb8\u6bd4\u7387\u4e00\u89bd\u8868\u5146\u8c50\u5206\u884c\u6709\u52fe\u9078\u70ba\u5171\u540c\u4e3b\u8fa6\uff0c\u57fa\u672c\u8cc7\u8a0a\u9801\u7c64-\u300c\u6848\u4ef6\u6027\u8cea\u300d\u6b04\u4f4d\u5247\u5fc5\u9808\u70ba\u4e3b\u8fa6
L160M01A.message92=\u52d5\u64a5\u63d0\u9192\u8cc7\u8a0a-\u627f\u8afe\u4e8b\u9805-\u4e0b\u6b21\u6aa2\u8996\u65e5\u671f\u9078\u9805\u4e0d\u5f97\u70ba\u7a7a\u767d
L160M01A.message93=\u52d5\u64a5\u63d0\u9192\u8cc7\u8a0a-\u627f\u8afe\u4e8b\u9805-\u4e0b\u6b21\u6aa2\u8996\u65e5\u671f\u4e0d\u5f97\u70ba\u7a7a\u767d
L160M01A.message94=\u52d5\u64a5\u63d0\u9192\u8cc7\u8a0a-\u627f\u8afe\u4e8b\u9805-\u6aa2\u8996\u9031\u671f\u9078\u9805\u4e0d\u5f97\u70ba\u7a7a\u767d
L160M01A.message95=\u52d5\u64a5\u63d0\u9192\u8cc7\u8a0a-\u52d5\u64a5\u63d0\u9192\u8cc7\u8a0a-\u627f\u8afe\u4e8b\u9805-\u6aa2\u8996\u9031\u671f-\u6708 \u6b04\u4f4d\u4e0d\u5f97\u7a7a\u767d
L160M01A.message96=\u4e0b\u5217\u984d\u5ea6\u65bc\u52d5\u5be9\u8868-\u76f8\u95dc\u5831\u8868-\u984d\u5ea6\u52d5\u7528\u8cc7\u8a0a-\u52d5\u64a5\u63d0\u9192\u8cc7\u8a0a\u6709\u627f\u8afe\u4e8b\u9805\uff0c\u4f46\u672a\u8a2d\u5b9a\u662f\u5426\u9700\u8ffd\u8e64\u6aa2\u8996\uff0c\u662f\u5426\u7e7c\u7e8c?<br/>
L160M01A.message97=\u82e5\u627f\u8afe\u4e8b\u9805\u9700\u6301\u7e8c\u8ffd\u8e64\u6aa2\u8996\uff0c\u8acb\u8a2d\u5b9a\u4e0b\u5217\u9805\u76ee:<br/>
L160M01A.message98=1.\u53ef\u65bc\u52d5\u5be9\u8868-\u76f8\u95dc\u5831\u8868-\u984d\u5ea6\u52d5\u7528\u8cc7\u8a0a-\u52d5\u64a5\u63d0\u9192\u8cc7\u8a0a \u8a2d\u5b9a\u627f\u8afe\u4e8b\u9805\u4e0b\u6b21\u6aa2\u8996\u65e5\u671f\u8207\u9031\u671f\u3002<br/>
L160M01A.message99=2.\u4ea6\u53ef\u65bca-Loan L521 \u4f01\u696d\u6388\u4fe1\u6236\u627f\u8afe\u4e8b\u9805\u7dad\u8b77\u4ea4\u6613\u8a2d\u5b9a\u3002<br/>
L160M01A.message100=3.\u5b8c\u6210\u4e0a\u8ff0\u8a2d\u5b9a\u5f8c\uff0c\u8acb\u65bc\u5831\u8868\u67e5\u8a62\u7cfb\u7d71\u67e5\u8a62LLDLN229(\u6bcf\u65e5)LLMLN229(\u6bcf\u6708)\u570b\u5167\u5404\u5206\u884c\u4f01\u696d\u6236\u627f\u8afe\u4e8b\u9805\u76f8\u95dc\u5831\u8868\u4ee5\u5b9a\u671f\u6aa2\u8996\u8ffd\u8e64\u3002
#J-106-0238-001 \u56e0\u61c9\u65bce-Loan\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71\u4f01\u3001\u500b\u91d1\u5fb5\u3001\u6388\u4fe1\u696d\u52d9\u9632\u5236\u6d17\u9322\u4f5c\u696d\u9801\u7c64\uff0c\u5c0d\u61c90015\u9ed1\u540d\u55ae\u6aa2\u6838\u547d\u4e2d\u5be9\u67e5\u4e4b\u5f8c\u7e8c\u4f5c\u696d\uff0c\u589e\u52a0\u300c\u9ed1\u540d\u55ae/\u9ed1\u570b\u5bb6/\u653f\u6cbb\u654f\u611f\u4eba\u7269\u4ea4\u6613\u5177\u9ad4\u6aa2\u6838\u6a5f\u5236\u300d
L160M01A.message107=Hit Blacklist
#J-109-0209_05097_B1001 e-Loan\u570b\u5167\u4f01\u91d1\u52d5\u5be9\u8868\u589e\u52a0\u501f\u6236\u6027\u8cea\u8a3b\u8a18\u7b49\u9032\u6263\u5e33\u5c0d\u8c61\u6aa2\u6838\u9805\u76ee
L160M01A.message108=\u300c\u4e3b\u5f9e\u50b5\u52d9\u4eba\u8cc7\u6599\u8868\u300d\uff0c\u4e3b\u501f\u6b3e\u4eba\u300c{0}\u300d\u984d\u5ea6\u5e8f\u865f\u300c{1}\u300d\u4e4b\u8cc7\u6599\u4e0d\u9f4a\u5168\u300c{2}\u300d\u3002
L160M01A.message109=\u672c\u6848\u984d\u5ea6\u64a5/\u9084\u6b3e\u4e4b\u5c0d\u61c9\u5b58\u6b3e\u9032/\u6263\u5e33\u5c0d\u8c61\u9650\u70ba\u501f\u6236\u672c\u8eab\u4e4b\u5e33\u865f\u3002
L160M01A.message110=\u9664\u4e0b\u5217\u501f\u6b3e\u4eba\u5916\uff0c\u672c\u6848\u984d\u5ea6\u64a5/\u9084\u6b3e\u4e4b\u5c0d\u61c9\u5b58\u6b3e\u9032/\u6263\u5e33\u5c0d\u8c61\u9650\u70ba\u501f\u6236\u672c\u8eab\u4e4b\u5e33\u865f\u3002
L160M01A.message111=\u501f\u6b3e\u4eba\u300c{0}\u300d\u984d\u5ea6\u64a5/\u9084\u6b3e\u4e4b\u5c0d\u61c9\u5b58\u6b3e\u9032/\u6263\u5e33\u5c0d\u8c61\u9650\u70ba\u300c{1}\u300d\u4e4b\u5e33\u865f\u3002 
#J-109-0265_05097_B1001 Web e-Loan\u4f01\u91d1\u52d5\u5be9\u8868\u639b\u4ef6\u6587\u865f\u6b04\u4f4d\u8f38\u5165\u9650\u5236
L160M01A.message112=\u639b\u4ef6\u6587\u865f\u683c\u5f0f\u932f\u8aa4 \uff0c\u50c5\u80fd\u8f38\u5165 \u82f1\u6587\u5b57\u6bcd \u8207\u6578\u5b57\uff0c\u4e14\u7b2c\u4e00\u78bc\u5fc5\u9808\u70ba\u82f1\u6587\u3002
#J-110-0540_05097_B1001 Web e-Loan\u4f01\u91d1\u6388\u4fe1\u914d\u5408\u8abf\u6574E-loan\u7cfb\u7d71\u52d5\u7528\u5be9\u6838\u8868\u90e8\u5206\u5167\u5bb9
L160M01A.message113=\u5176\u4ed6\u4e8b\u9805\u6709\u52fe\u9078\u672c\u7968\u76f8\u95dc\uff0c\u4e0b\u5217\u984d\u5ea6\u5e8f\u865f\u5fc5\u9808\u65bc\u64d4\u4fdd\u54c1\u7cfb\u7d71\u4e4b\u984d\u5ea6\u672c\u7968\u985e\u5225\u5b8c\u6210\u5df2\u8986\u6838\u767b\u8a18:{0}
#J-111-0207 Web e-Loan\u570b\u5167\u4f01\u91d1\uff0c\u52d5\u7528\u6aa2\u6838\u8868\u589e\u52a0\u4e00\u9805\u76ee:\u300c\u4fe1\u4fdd\u6848\u4ef6\u8ca0\u8cac\u4eba\u5ba2\u6236\u57fa\u672c\u8cc7\u6599\u6a94\u662f\u5426\u5df2\u5efa\u6709\u914d\u5076\u8cc7\u6599\u300d
L160M01A.message114=\u203b\u4fe1\u4fdd\u6848\u4ef6\u8ca0\u8cac\u4eba\u5ba2\u6236\u57fa\u672c\u8cc7\u6599\u6a94\u662f\u5426\u5df2\u5efa\u6709\u914d\u5076\u8cc7\u6599\uff1a
L160M01A.message115=\u8acb\u6ce8\u610f\u8ca0\u8cac\u4eba\u5ba2\u6236\u57fa\u672c\u8cc7\u6599\u6a94\u7121\u914d\u5076\u8cc7\u6599
#J-111-0506_05097_B1001 Web e-Loan\u4f01\u91d1\u6388\u4fe1\u52d5\u5be9\u8868\u589e\u52a0\u6388\u4fe1\u4f5c\u696d\u624b\u7e8c\u8cbb\u4e4b\u6b04\u4f4d
L160M01A.message116=\u6b04\u4f4d\u300c{0}\u300d\u5fc5\u9808\u5927\u65bc0\u3002
L160M01A.message117=\u6b04\u4f4d\u300c{0}\u300d\u5fc5\u9808\u8207\u984d\u5ea6\u660e\u7d30\u8868\u76f8\u540c\u3002
L160M01A.message119=\u8acb\u5148\u81f3\u8cc7\u6599\u5efa\u6a94\u7cfb\u7d71\u984d\u5ea6\u9810\u7d04\u884c\u696d\u5c0d\u8c61\u5225\u7533\u8acb\u78ba\u8a8d\u66a8\u4fee\u6539\u3002
L160M01A.message120=\u300cSyndicated loan ratio information\u300d\uff0cDrawdown Approval Sheet's Applied Credit Limit({0}) must less than or equal with Sum of Mega Branches loan amount({1})
L160M01A.message121=\u8acb\u78ba\u8a8d\u6388\u4fe1\u4f5c\u696d\u624b\u7e8c\u8cbb\u91d1\u984d\u662f\u5426\u6b63\u78ba\u3002
L162M01A.error01=\u300c{0}\u300dGuarantor burden the ratio of the guarantee responsibility field can not be empty\u3002
L162M01A.error02=\u300c{0}\u300dNatural persons Guarantor burden the ratio of the guarantee responsibility field must be 100
#J-110-0040_05097_B1001 Web e-Loan\u589e\u52a0\u300c\u672c\u884c\u570b\u5bb6\u66b4\u96aa\u662f\u5426\u4ee5\u4fdd\u8b49\u4eba\u570b\u5225\u70ba\u8a08\u7b97\u57fa\u6e96(\u53d6\u4ee3\u6700\u7d42\u98a8\u96aa\u570b\u5225)\u300d\u8a3b\u8a18
L162M01A.error03=\u300c{0}\u300dthe field\u300c{1}\u300dmust not be blank 
L162M01A.error04=\u300c{0}\u300dthe field\u300c{1}\u300dAll borrowers under the same contract number must choose the same 
L162M01A.error05=\u300c{0}\u300dThe guarantee amount shall not be greater than the FCredit Facility Report's current requested amount

L160M01A.applyToAloan1=\u6ce8\u610f\u4e8b\u9805
L160M01A.applyToAloan2=\u627f\u8afe\u4e8b\u9805
L160M01A.reViewDateKind=\u4e0b\u6b21\u6aa2\u8996\u65e5\u671f
L160M01A.reViewChgKind=\u6aa2\u8996\u9031\u671f
L160M01A.toALoan2=\u4e8b\u9805\u8aaa\u660e
#J-109-0077_05097_B1008 \u56e0\u61c9\u653f\u5e9c\u56b4\u91cd\u7279\u6b8a\u50b3\u67d3\u6027\u80ba\u708e\u7d13\u56f0\u65b9\u6848\u5be6\u65bd\u9700\u8981, \u914d\u5408\u65b0\u589e\u76f8\u95dc\u4f5c\u696d
L160M01A.rescueNo=\u639b\u4ef6\u6587\u865f
L160M01A.empCount=\u73fe\u6709\u54e1\u5de5\u4eba\u6578
#J-110-0288_05097_B1002 Web e-Loan\u914d\u5408\u570b\u767c\u57fa\u91d1\u65b0\u5275\u4e8b\u696d\u7d13\u56f0\u52a0\u78bc\u65b9\u6848H01\u3001A07\u5c08\u6848\u4fee\u6539
L160M01A.isTurnoverDecreased=\u662f\u5426\u7b26\u5408110\u5e745~12\u6708\u71df\u696d\u984d\u6e1b\u5c11\u905415%
L160M01A.isTurnoverDecreased_A07=\u662f\u5426\u7b26\u5408110\u5e745~12\u6708\u71df\u696d\u984d\u6e1b\u5c11\u905415%
L160M01A.isTurnoverDecreased_A11=\u662f\u5426\u7b26\u5408111\u5e741~12\u6708\u71df\u696d\u984d\u6e1b\u5c11\u905415%
L160M01A.isTurnoverDecreased_memo_A07=\u3010\u7b26\u5408\u7d93\u6fdf\u90e8\u88dc\u52a9\u8981\u9ede\u9069\u7528\u5c0d\u8c61\u4e4b\u4e2d\u5c0f\u4f01\u696d\uff0c\u82e5\u672a\u7b26\u5408110\u5e745~12\u6708\u71df\u696d\u984d\u6e1b\u5c11\u905415%\u8005\uff0c\u7121\u7d93\u6fdf\u90e8\u5229\u606f\u88dc\u8cbc\uff0c\u6539\u7531\u570b\u767c\u57fa\u91d1\u63d0\u4f9b\u5229\u606f\u88dc\u8cbc\u3002\u3011
L160M01A.isTurnoverDecreased_memo_A11=\u3010\u7b26\u5408\u7d93\u6fdf\u90e8\u88dc\u52a9\u8981\u9ede\u9069\u7528\u5c0d\u8c61\u4e4b\u4e2d\u5c0f\u4f01\u696d\uff0c\u82e5\u672a\u7b26\u5408111\u5e741~12\u6708\u71df\u696d\u984d\u6e1b\u5c11\u905415%\u8005\uff0c\u7121\u7d93\u6fdf\u90e8\u5229\u606f\u88dc\u8cbc\uff0c\u6539\u7531\u570b\u767c\u57fa\u91d1\u63d0\u4f9b\u5229\u606f\u88dc\u8cbc\u3002\u3011


#J-110-0465_05097_B1001 Web e-Loan\u570b\u5167\u4f01\u91d1\u6388\u4fe1\u52d5\u5be9\u8868\u65b0\u589e\u984d\u5ea6\u7de8\u865f
L160M01A.rescueSn=\u984d\u5ea6\u7de8\u865f
#J-111-0506_05097_B1001 Web e-Loan\u4f01\u91d1\u6388\u4fe1\u52d5\u5be9\u8868\u589e\u52a0\u6388\u4fe1\u4f5c\u696d\u624b\u7e8c\u8cbb\u4e4b\u6b04\u4f4d
L160M01A.isOperationFee=\u662f\u5426\u6536\u53d6\u6388\u4fe1\u4f5c\u696d\u624b\u7e8c\u8cbb
L160M01A.operationFeeCurr=\u6536\u53d6\u5e63\u5225
L160M01A.operationFeeAmt=\u6536\u53d6\u91d1\u984d
L160M01A.operationFeeDueDate=\u6700\u665a\u6536\u53d6\u65e5

#J-111-0214_05097_B1001 Web e-Loan\u570b\u5167\u4f01\u91d1\u52d5\u7528\u5be9\u6838\u8868\u65b0\u589e\u53ef\u9069\u7528\u65b0\u5229\u7387\u8a08\u7b97\u6e1b\u514d\u606f\u76f8\u95dc\u529f\u80fd
L161S01A.rescueChgRateFg=\u5ba2\u6236\u662f\u5426\u7533\u8acb\u8abf\u6574\u88dc\u8cbc\u5229\u7387
L161S01A.rescueChgRateSingDate=\u7c3d\u7d04\u65e5(\u589e\u88dc\u5408\u7d04)
L161S01A.rescueChgRate=\u8abf\u6574\u5f8c\u5229\u7387
L161S01A.rescueChgRateEffectDate=\u5ba2\u6236\u5229\u7387\u8abf\u5347\u65e5

C801M01A.caseNo=\u6a94\u6848\u7de8\u865f
C801M01A.custId=\u7d71\u4e00\u7de8\u865f
C801M01A.dupNo=\u91cd\u8986\u5e8f\u865f
C801M01A.custName=\u5ba2\u6236\u540d\u7a31
C801M01A.cntrNo=\u984d\u5ea6\u5e8f\u865f
C801M01A.loanNo=\u653e\u6b3e\u5e33\u865f
C801M01A.creatorname=\u5206\u884c\u7d93\u8fa6
C801M01A.title=\u500b\u4eba\u8cc7\u6599\u6e05\u518a

#J-106-0029-004  \u6d17\u9322\u9632\u5236-\u52d5\u5be9\u8868\u65b0\u589e\u6d17\u9322\u9632\u5236\u9801\u7c64
l120s01a.chairman=Principal
l120s01a.custid=ID
l120s01a.dupno=Repeat Serial No.
l120s01a.custname=Name
L120S01p.btnApply=Apply
L120S01p.beneficiary=Effective Bene
#J-107-0070-001  Web e-Loan \u570b\u5167\u5fb5\u4fe1\u3001\u7c3d\u5831\u3001\u52d5\u5be9AML\u9801\u7c64\u8acb\u5c07\u300c\u9ad8\u968e\u7ba1\u7406\u4eba\u54e1\u300d\u7d0d\u5165\u61c9\u67e5\u8a62\u6bd4\u5c0d\u9ed1\u540d\u55ae\u4e4b\u5c0d\u8c61\u3002
L120S01p.seniorMgr=Senior management
#J-108-0039_05097_B1001 Web e-Loan \u570b\u5167\u4f01\u91d1\u6388\u4fe1\u7cfb\u7d71\u7c3d\u5831\u3001\u52d5\u5be9AML\u9801\u7c64\u5c07\u501f\u6236\u4e4b\u300c\u5177\u63a7\u5236\u6b0a\u4eba\u300d\u7d0d\u5165\u61c9\u67e5\u8a62\u6bd4\u5c0d\u9ed1\u540d\u55ae\u4e4b\u5c0d\u8c61\u3002
L120S01p.ctrlPeo=Controlling Person
L120S01p.type=Data type
L120S01p.type_01=Person
L120S01p.type_02=Company
L120S01p.custIdAndDupNo=ID & Repeat Serial No.
L120S01p.conPersonId=ID
L120S01p.conPersonNew=Company/Person
L120S01p.conPersonName=Name
L120S01p.conPersonEName=English Name
L120S01p.nation=Nation
L120S01p.birthDate=birthday
L120S01p.peps=Politically exposed persons
nohave=No
other.login=Register
L120S01p.message01=Press [Close] and write the data back to the previous page\u3002

L140M01a.isNonSMEProjLoan=\u672c\u6848\u662f\u5426\u70ba\u632f\u8208\u7d93\u6fdf\u975e\u4e2d\u5c0f\u4f01\u696d\u5c08\u6848\u8cb8\u6b3e
L140M01a.nonSMEProjLoanAmt=\u5c6c\u632f\u8208\u7d93\u6fdf\u975e\u4e2d\u5c0f\u4f01\u696d\u5c08\u6848\u8cb8\u6b3e\u91d1\u984d
L140M01a.currSameWithCurrentApply=\u5e63\u5225\u540c\u73fe\u8acb\u984d\u5ea6
#J-106-0082-001 Web e-Loan\u570b\u5167\u4f01\u91d1\u6388\u4fe1\u7cfb\u7d71\uff0c\u984d\u5ea6\u660e\u7d30\u8868\u65b0\u589e\u4e2d\u5c0f\u4f01\u696d\u5275\u65b0\u767c\u5c55\u5c08\u6848\u8cb8\u6b3e
L140M01a.inSmeFg=\u672c\u6848\u662f\u5426\u70ba\u4e2d\u5c0f\u4f01\u696d\u5275\u65b0\u767c\u5c55\u5c08\u6848\u8cb8\u6b3e
L140M01a.inSmeToAmt=\u5c6c\u4e2d\u5c0f\u4f01\u696d\u5275\u65b0\u767c\u5c55\u5c08\u6848\u8cb8\u6b3e-\u9031\u8f49\u6027\u652f\u51fa\u91d1\u984d
L140M01a.inSmeCaAmt=\u5c6c\u4e2d\u5c0f\u4f01\u696d\u5275\u65b0\u767c\u5c55\u5c08\u6848\u8cb8\u6b3e-\u8cc7\u672c\u6027\u652f\u51fa\u91d1\u984d
#\u672c\u6848\u662f\u5426\u5c6c\u56e0\u61c9\u56b4\u91cd\u7279\u6b8a\u50b3\u67d3\u6027\u80ba\u708e\u5f71\u97ff\u4e8b\u696d\u8cc7\u91d1\u7d13\u56f0
L140M01a.isRescue=\u672c\u6848\u662f\u5426\u5c6c\u56e0\u61c9\u56b4\u91cd\u7279\u6b8a\u50b3\u67d3\u6027\u80ba\u708e\u5f71\u97ff\u4e8b\u696d\u8cc7\u91d1\u7d13\u56f0 / \u75ab\u5f8c\u632f\u8208\u53ca\u4f4e\u78b3\u667a\u6167\u7d0d\u7ba1\u8cb8\u6b3e\u63aa\u65bd / \u5929\u7136\u707d\u5bb3\u8cb8\u6b3e / \u653f\u7b56\u6027\u8cb8\u6b3e
L140M01a.isRescueOn=\u662f\u5426\u5ef6\u7e8c\u524d\u4e00\u5e74\u5ea6\u7d13\u56f0\u5c08\u6848\u5229\u606f\u88dc\u8cbc\u65b9\u6848
L140M01a.isRescueOnTitle=\u524d\u6b21\u7d13\u56f0\u8cb8\u6b3e\u985e\u5225
L140M01a.rescueItem=\u7d13\u56f0\u8cb8\u6b3e\u985e\u5225
L140M01a.rescueItem2=\u75ab\u5f8c\u632f\u8208\u53ca\u4f4e\u78b3\u667a\u6167\u7d0d\u7ba1\u8cb8\u6b3e\u985e\u5225
L140M01a.rescueItem3=\u5929\u7136\u707d\u5bb3\u8cb8\u6b3e\u985e\u5225
L140M01a.rescueItem4=\u5929\u7136\u707d\u5bb3\u8cb8\u6b3e/\u632f\u8208\u8cb8\u6b3e\u985e\u5225
L140M01a.rescueItem5=\u653f\u7b56\u6027\u8cb8\u6b3e\u985e\u5225
L140M01a.rescueItemLKind.1=\u671d\u6578\u4f4d\u8f49\u578b
L140M01a.rescueItemLKind.2=\u671d\u6de8\u96f6\u8f49\u578b
L140M01a.rescueItemLKind.4=\u671d\u901a\u8def\u767c\u5c55
L140M01a.rescueRate=\u6e1b\u6536\u5229\u7387
#J-109-0077_05097_B1003 \u56e0\u61c9\u653f\u5e9c\u56b4\u91cd\u7279\u6b8a\u50b3\u67d3\u6027\u80ba\u708e\u7d13\u56f0\u65b9\u6848\u5be6\u65bd\u9700\u8981, \u914d\u5408\u65b0\u589e\u76f8\u95dc\u4f5c\u696d
L140M01a.isExtendSixMon=\u662f\u5426\u5c55\u5ef6\u5230\u671f\u65e5(\u539f\u5247\u77ed\u671f\u534a\u5e74/\u4e2d\u9577\u671f\u5bec\u96501\u5e74)
L140M01a.isExtendSixMon2=\u662f\u5426\u5c55\u5ef6\u5230\u671f\u65e5
L140M01a.rescueExpect=\u9810\u8a08
L140M01a.rescueIbDate=\u5408\u610f\u5c55\u5ef6\u65e5(\u5373\u7c3d\u7d04\u65e5\u4ea6\u70ba\u6e1b\u8b93\u5229\u606f\u4e4b\u751f\u6548\u65e5)
L140M01a.rescueIbDate2=\u5408\u610f\u6e1b\u606f\u65e5(\u5373\u7c3d\u7d04\u65e5\u4ea6\u70ba\u6e1b\u8b93\u5229\u606f\u4e4b\u751f\u6548\u65e5)
L140M01a.rescueAmt=\u622a\u81f3109.03.12\u524d\u65e2\u6709\u4e4b\u672c\u884c\u8cb8\u6b3e\u9918\u984d
L140M01a.rescueAmt_A04=\u622a\u81f3110.06.03\u524d\u65e2\u6709\u4e4b\u672c\u884c\u8cb8\u6b3e\u9918\u984d
L140M01a.rescueAmt_A08=\u622a\u81f3111.06.01\u524d\u65e2\u6709\u4e4b\u672c\u884c\u8cb8\u6b3e\u9918\u984d
L140M01a.rescueAmt_J04=\u622a\u81f3113.4.3\u524d\u65e2\u6709\u4e4b\u672c\u884c\u8cb8\u6b3e\u9918\u984d
L140M01a.rescueAmt_J05=\u622a\u81f3113.4.3\u524d\u65e2\u6709\u4e4b\u672c\u884c\u8cb8\u6b3e\u9918\u984d
L140M01a.rescueCurr=\u622a\u81f3109.03.12\u524d\u65e2\u6709\u4e4b\u672c\u884c\u8cb8\u6b3e\u5e63\u5225
L140M01a.isCbRefin=\u672c\u6848\u8cc7\u91d1\u4f86\u6e90\u662f\u5426\u70ba\u592e\u884c\u8f49\u878d\u901a\u8cc7\u91d1\u5c08\u6848
L140M01a.rescueAmtOldCase=\u820a\u8cb8(\u5df2\u8fa6\u7406\u8cb8\u6b3e)\u8cc7\u8a0a
#J-109-0077_05097_B1005 \u56e0\u61c9\u653f\u5e9c\u56b4\u91cd\u7279\u6b8a\u50b3\u67d3\u6027\u80ba\u708e\u7d13\u56f0\u65b9\u6848\u5be6\u65bd\u9700\u8981, \u914d\u5408\u65b0\u589e\u76f8\u95dc\u4f5c\u696d
L140M01a.rescueItemSub=\u7b26\u5408\u5176\u4ed6\u7d13\u56f0\u8cb8\u6b3e\u5229\u606f\u88dc\u8cbc
#J-109-0077_05097_B1006 \u56e0\u61c9\u653f\u5e9c\u56b4\u91cd\u7279\u6b8a\u50b3\u67d3\u6027\u80ba\u708e\u7d13\u56f0\u65b9\u6848\u5be6\u65bd\u9700\u8981, \u914d\u5408\u65b0\u589e\u76f8\u95dc\u4f5c\u696d
L140M01a.rescueDate=\u53d7\u7406\u65e5\u671f
#J-109-0077_05097_B1008 \u56e0\u61c9\u653f\u5e9c\u56b4\u91cd\u7279\u6b8a\u50b3\u67d3\u6027\u80ba\u708e\u7d13\u56f0\u65b9\u6848\u5be6\u65bd\u9700\u8981, \u914d\u5408\u65b0\u589e\u76f8\u95dc\u4f5c\u696d
L140M01a.headItem1=\u672c\u6848\u662f\u5426\u79fb\u9001\u4fe1\u4fdd\u57fa\u91d1\u4fdd\u8b49
L140M01a.gutCutDate=\u4fe1\u4fdd\u9996\u6b21\u52d5\u7528\u6709\u6548\u671f\u9650
#J-111-0222_05097_B1001  Web e-Loan\u4f01\u91d1\u3001\u500b\u91d1\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71\u6709\u95dc\u4f01\u3001\u6d88\u91d1\u984d\u5ea6\u660e\u7d30\u8868\u4fe1\u4fdd\u64d4\u4fdd\u54c1\u6587\u5b57\u6558\u8ff0\u4fee\u6539
L140M01a.gutCutDateMemo1=\u6216\u5f8c\u7e8c\u7372\u4fe1\u4fdd\u57fa\u91d1\u6838\u51c6\u5c55\u5ef6\u4e4b\u671f\u9650

L140M01a.gratio=\u4fdd\u8b49\u6210\u6578
L140M01a.cgfRate=\u4fe1\u4fdd\u57fa\u91d1\u6838\u51c6\u4e4b\u4fdd\u8b49\u624b\u7e8c\u8cbb\u7387
L140M01a.cgfDate=\u4fe1\u4fdd\u57fa\u91d1\u4fdd\u8b49\u66f8\u767c\u6587\u65e5\u671f
L140M01a.isGuaOldCase=\u672c\u6848\u662f\u5426\u70ba\u4fe1\u4fdd\u7e8c\u7d04\u6848
L140M01a.byNewOld=\u662f\u5426\u53ef\u501f\u65b0\u9084\u820a
#J-109-0077_05097_B1011 \u56e0\u61c9\u653f\u5e9c\u56b4\u91cd\u7279\u6b8a\u50b3\u67d3\u6027\u80ba\u708e\u7d13\u56f0\u65b9\u6848\u5be6\u65bd\u9700\u8981, \u914d\u5408\u65b0\u589e\u76f8\u95dc\u4f5c\u696d
L140M01a.isSmallBuss=\u592e\u884c\u8f49\u878d\u901a\u8fa6\u7406\u985e\u5225
L140M01a.bankSimpleScoreCard=\u9280\u884c\u7c21\u6613\u8a55\u5206\u8868
L140M01a.sbRegistPeriod=\u7a05\u7c4d\u767b\u8a18\u671f\u9593
L140M01a.sbPrincipalPeriod=\u8ca0\u8cac\u4eba\u5f9e\u4e8b\u672c\u696d\u7d93\u9a57
L140M01a.sbPrincipalPeriod_1=\u5207\u7d50\u6216\u63d0\u4f9b\u8b49\u660e
L140M01a.sbCreditRating=\u8ca0\u8cac\u4eba\u500b\u4eba\u4fe1\u7528\u8a55\u5206
L140M01a.sbHasCreditRating=\u672c\u6b21\u662f\u5426\u6709\u8a55\u5206
L140M01a.sbHasCreditRating_No=\u672c\u6b21\u7121\u6cd5\u8a55\u5206
L140M01a.sbColStatus=\u4e0d\u52d5\u7522\u64d4\u4fdd\u8a2d\u5b9a
L140M01a.sbColStatus_1=\u4f01\u696d\u6216\u8ca0\u8cac\u4eba\u6709\u4e0d\u52d5\u7522\u4e14\u8a2d\u5b9a\u672c\u884c\u7b2c\u4e00\u9806\u4f4d
L140M01a.sbColStatus_2=\u4f01\u696d\u6216\u8ca0\u8cac\u4eba\u6709\u4e0d\u52d5\u7522\u4e14\u975e\u8a2d\u5b9a\u672c\u884c\u7b2c\u4e00\u9806\u4f4d\u6216\u7121\u8a2d\u5b9a
L140M01a.sbColStatus_3=\u4f01\u696d\u6216\u8ca0\u8cac\u4eba\u7686\u7121\u4e0d\u52d5\u7522\u6216\u4e0d\u52d5\u7522\u8a2d\u5b9a\u7d66\u975e\u9280\u884c\u3001\u79df\u8cc3\u516c\u53f8\u8005
L140M01a.sbColStatus_X=\u4ee5\u4e0a\u7686\u975e
L140M01a.sbBussStatus=\u71df\u696d\u72c0\u6cc1
L140M01a.sbBussStatus_1=\u5207\u7d50\u71df\u696d\u4e2d\u6216\u63d0\u4f9b\u6700\u8fd13\u500b\u6708\u5167\u7e73\u7a05\u8b49\u660e
L140M01a.sbBussStatus_2=\u5207\u7d50\u505c\u696d\u672a\u6eff3\u500b\u6708\u60df\u6709\u7e7c\u7e8c\u7d93\u71df\u610f\u9858
L140M01a.sbBussStatus_3=\u5207\u7d50\u505c\u696d\u672a\u6eff6\u500b\u6708\u60df\u6709\u7e7c\u7e8c\u7d93\u71df\u610f\u9858
L140M01a.sbBussStatusF_1=\u6b63\u5e38\u71df\u696d
L140M01a.sbBussStatusF_2=\u6b63\u5e38\u71df\u696d(\u5fa9\u696d\u8d85\u904e6\u500b\u6708)
L140M01a.sbBussStatusF_3=\u6b63\u5e38\u71df\u696d(\u5fa9\u696d\u672a\u6eff6\u500b\u6708)
L140M01a.sbBussStatus_X=\u4ee5\u4e0a\u7686\u975e
L140M01a.sbScore=\u7e3d\u5206
#J-109-0077_05097_B1016 \u56e0\u61c9\u653f\u5e9c\u56b4\u91cd\u7279\u6b8a\u50b3\u67d3\u6027\u80ba\u708e\u7d13\u56f0\u65b9\u6848\u5be6\u65bd\u9700\u8981, \u914d\u5408\u65b0\u589e\u76f8\u95dc\u4f5c\u696d
L140M01a.sbReasonCode=\u7406\u7531\u4ee3\u78bc
L140M01a.sbReasonCode_002=\u4fe1\u7528\u8cc7\u6599\u4e0d\u8db3
L140M01a.sbReasonCode_015=\u50c5\u6709\u5b78\u8cb8
L140M01a.sbReasonCode_XXX=\u5176\u4ed6
#J-109-0811_05097_B1001 \u914d\u5408\u300c\u56b4\u91cd\u7279\u6b8a\u50b3\u67d3\u6027\u80ba\u708e\u9632\u6cbb\u53ca\u59a4\u56f0\u632f\u8208\u7279\u5225\u689d\u4f8b\u300d\u65bd\u884c\u671f\u9593\u8abf\u6574\uff0c\u52d5\u5be9\u8868\u65b0\u589e\u592e\u884c\u512a\u60e0\u5229\u7387\u878d\u901a\u671f\u9650
L140M01a.cbRefinDt=\u592e\u884c\u512a\u60e0\u5229\u7387\u878d\u901a\u671f\u9650
#J-110-0258_05097_B1002 Web e-Loan\u914d\u5408\u8fa6\u7406\u300c\u884c\u653f\u9662\u570b\u5bb6\u767c\u5c55\u57fa\u91d1\u5354\u52a9\u65b0\u5275\u4e8b\u696d\u7d13\u56f0\u878d\u8cc7\u52a0\u78bc\u65b9\u6848\u300d\uff0c\u4fee\u6539\u984d\u5ea6\u660e\u7d30\u8868\u6b04\u4f4d
L140M01a.rescueNdfGutPercent=\u570b\u767c\u57fa\u91d1\u52a0\u78bc\u4fdd\u8b49\u6210\u6578
#J-110-0555_05097_B1001 Web e-Loan\u984d\u5ea6\u660e\u7d30\u8868\u65b0\u589e\u6388\u4fe1\u4f5c\u696d\u624b\u7e8c\u8cbb\u76f8\u95dc\u8cc7\u8a0a
L140M01a.isOperationFee=\u662f\u5426\u6536\u53d6\u6388\u4fe1\u4f5c\u696d\u624b\u7e8c\u8cbb
L140M01a.noOperationFee=\u514d\u6536\u53d6\u6388\u4fe1\u4f5c\u696d\u624b\u7e8c\u8cbb\u7406\u7531
L140M01a.noOperationFeeOth=\u8aaa\u660e
L140M01a.operationFeeWay=\u624b\u7e8c\u8cbb\u6536\u53d6\u65b9\u5f0f
L140M01a.operationFeeWay1=\u4f9d\u898f\u5b9a\u8a08\u6536(\u6838\u5b9a\u984d\u5ea6*0.05%\uff0c\u6700\u4f4e\u65b0\u81fa\u5e631,500\u5143)
L140M01a.operationFeeWay2=\u5176\u4ed6
L140M01a.attachDoc=\u672c\u4e8b\u696d\u6aa2\u9644\u4f50\u8b49\u8cc7\u6599
L140M01a.attachDocA=\u539f\u7533\u8cb8\u7d13\u56f0\u632f\u8208\u8cb8\u6b3e\u4e8b\u696d
L140M01a.attachDocB=\u75ab\u5f8c\u6642\u671f\u71df\u904b\u56f0\u96e3\u4e8b\u696d
L140M01a.attachDocC=\u505c\u696d\u518d\u51fa\u767c\u4e8b\u696d\uff1a109\u5e74\u81f3111\u5e74\u505c\u696d\u6838\u51c6\u51fd\u53ca109\u5e74\u81f3114\u5e74\u5fa9\u696d\u6838\u51c6\u51fd
L140M01a.attachDocA1=\u539f\u8cb8\u91d1\u878d\u6a5f\u69cb\u51fa\u5177\u4f50\u8b49\u8cc7\u6599
L140M01a.attachDocA2=\u7d93\u6fdf\u90e8\u7c21\u6613\u8cc7\u683c\u8a8d\u5b9a\u5e73\u53f0\u958b\u7acb\u8b49\u660e
L140M01a.attachDocB1=\u71df\u696d\u4eba\u92b7\u552e\u984d\u8207\u7a05\u984d\u7533\u5831\u66f8
L140M01a.attachDocB2=\u8ca1\u52d9\u5831\u8868
L140M01a.attachDocB3=\u8cc7\u91d1\u5f80\u4f86\u660e\u7d30
L140M01a.attachDocB4=\u7d93\u6fdf\u90e8\u59d4\u8a17\u8f14\u5c0e\u55ae\u4f4d\u958b\u7acb\u8b49\u660e
L140M01a.attachDocB21=\u6703\u8a08\u5e2b\u7c3d\u8b49\u5831\u544a
L140M01a.attachDocB22=\u5831\u7a05\u5831\u8868
L140M01a.attachDocB23=\u81ea\u7de8\u5831\u8868
L140M01a.attachDocB31=\u9280\u884c\u5b58\u647a
L140M01a.attachDocB32=\u5c0d\u5e33\u55ae
L140M01a.attachDocB33=\u9001\u8ca8\u55ae
L140M01a.attachDocB34=\u767c\u7968\u660e\u7d30\u8868
L140M01a.attachDocB35=\u5176\u4ed6
L140M01a.headItem.GutBatchNo=\u4e2d\u5c0f\u4fe1\u4fdd\u57fa\u91d1\u6279\u6b21\u4fdd\u8b49\u6279\u865f
L140M01a.headItem.bgfRate=\u6279\u6b21\u4fdd\u8b49\u624b\u7e8c\u8cbb\u7387
L140M01a.headItem.bgaDate=\u4fe1\u4fdd\u57fa\u91d1\u6279\u6b21\u4fdd\u8b49\u984d\u5ea6\u67e5\u8986\u66f8\u767c\u6587\u65e5\u671f
L140M01a.headItem.bGutAmtApprCaseNo=\u4fe1\u4fdd\u57fa\u91d1\u6279\u6b21\u4fdd\u8b49\u984d\u5ea6\u67e5\u8986\u66f8\u7de8\u865f
L140M01a.bgaDate=\u6279\u6b21\u4fdd\u8b49\u984d\u5ea6\u67e5\u8986\u66f8\u767c\u6587\u65e5\u671f
#J-109-0365_05097_B1001 Web e-Loan\u570b\u5167\u4f01\u91d1\u6388\u4fe1\u984d\u5ea6\u660e\u7d30\u8868\u79d1\u76ee\u70ba\u9060\u671f\u5916\u532f\u3001\u63db\u532f\u4ea4\u6613\u6642\uff0c\u65b0\u589e\u662f\u5426\u5fb5\u63d0\u4fdd\u8b49\u91d1\u7b49\u76f8\u95dc\u6b04\u4f4d
L140M01a.marginFlag=\u662f\u5426\u5fb5\u63d0\u4fdd\u8b49\u91d1
L140M01a.derivEval=Customer Risk Attribute Assessment
L140M01a.evalDate=\u8a55\u4f30\u65e5\u671f

#J-109-0209_05097_B1001 e-Loan\u570b\u5167\u4f01\u91d1\u52d5\u5be9\u8868\u589e\u52a0\u501f\u6236\u6027\u8cea\u8a3b\u8a18\u7b49\u9032\u6263\u5e33\u5c0d\u8c61\u6aa2\u6838\u9805\u76ee
L164S01A.isSole=\u662f\u5426\u70ba\u6211\u570b\u7368\u8cc7\u6216\u5408\u5925\u4f01\u696d
L164S01A.soleType=\u4f01\u696d\u985e\u5225
L164S01A.hasRegis=\u662f\u5426\u53d6\u5f97\u5546\u696d\u767b\u8a18

#\u5229\u5bb3\u95dc\u4fc2\u4eba
ces1200.1202=<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"></head><div class="verticalPage"><p align="center">\u9280\u884c\u6cd5\u53ca\u91d1\u63a7\u6cd5\u7b2c44/45\u689d\u53ca\u516c\u53f8\u6cd5\u7b2c206\u689d\u7b2c3\u9805\u6574\u6279\u67e5\u8a62\u7d50\u679c\u5229\u5bb3\u95dc\u4fc2\u4eba</p><p align="center">(\u542b\u9280\u884c/\u91d1\u63a7\u5be6\u8cea\u95dc\u4fc2\u4eba/\u516c\u53f8\u6cd5\u70ba\u8463\u4e8b\u6709\u63a7\u5236\u5f9e\u5c6c\u95dc\u4fc2\u4e4b\u516c\u53f8)\u6574\u6279\u67e5\u8a62</p><div align="left">\u67e5\u8a62\u4eba\u54e1:{0}</div><div align="left">\u67e5\u8a62\u65e5\u671f:{1}</div><div><table border="1"><tbody><tr><td>\u5e8f\u865f</td><td>\u7d71\u4e00\u7de8\u865f</td><td>\u91cd\u8907\u5e8f\u865f</td><td>\u59d3\u540d</td><td>\u9280\u884c\u6cd5\u5229\u5bb3\u95dc\u4fc2\u4eba</td><td>\u91d1\u63a7\u6cd5\u7b2c44\u689d\u5229\u5bb3\u95dc\u4fc2\u4eba</td><td>\u91d1\u63a7\u6cd5\u7b2c45\u689d\u5229\u5bb3\u95dc\u4fc2\u4eba</td><td>\u5be6\u8cea\u95dc\u4fc2\u4eba(\u6388\u4fe1\u4ee5\u5916\u4ea4\u6613)</td><td>\u516c\u53f8\u6cd5\u8207\u672c\u884c\u8463\u4e8b\u5177<br/>\u6709\u63a7\u5236\u5f9e\u5c6c\u95dc\u4fc2\u516c\u53f8</td></tr>{2}</tbody></table></div><p align="left">1.\u516c\u53f8\u6236\u8463\u76e3\u4e8b\u8cc7\u6599\u4f86\u6e90\u70ba\u806f\u5408\u5fb5\u4fe1\u4e2d\u5fc3,\u8cc7\u6599\u65e5\u671f\uff1a  \u53ef\u81f3\u7d93\u6fdf\u90e8\u7db2\u7ad9 www.moea.gov.tw/~doc/ce/ \u67e5\u8a62\u6700\u65b0\u8463\u76e3\u4e8b\u8cc7\u6599</p><p align="left">2.\u672c\u9805\u67e5\u8a62\u5305\u542b\u5be6\u8cea\u95dc\u4fc2\u4eba\u4e4b\u67e5\u8a62\u3001\u516c\u53f8\u6cd5\u7b2c206\u689d\u7b2c\u4e09\u9805\u70ba\u8463\u4e8b\u5177\u6709\u63a7\u5236\u5f9e\u5c6c\u95dc\u4fc2\u4e4b\u516c\u53f8\u3002</p><p align="left">3.\u7531\u65bc\u5229\u5bb3\u95dc\u4fc2\u4eba\u8cc7\u6599\u5eab\u6bcf\u534a\u5e74\u4f8b\u884c\u6027\u7dad\u8b77\u4e59\u6b21\uff0c\u70ba\u514d\u7dad\u8b77\u4e4b\u6642\u9593\u843d\u5dee\uff0c\u672c\u7cfb\u7d71\u4e4b\u67e5\u8a62\u7d50\u679c\u61c9\u518d\u52a0\u4ee5\u67e5\u8b49\u5f8c\uff0c\u65b9\u80fd\u4f7f\u7528\u65bc\u6388\u4fe1\u7c3d\u5831\u6848\u4ef6\u4e2d\u3002</p></div>
ces1200.1202.1=<tr><td>{0}</td><td>{1}</td><td>{2}</td><td>{3}</td><td>{4}</td><td>{5}</td><td>{6}</td><td>{7}</td><td>{8}</td></tr>
ces1200.1200.css=<style>.verticalPage table {border-collapse: collapse;width: 100%;}.verticalPage table, .verticalPage td, .verticalPage tr {border: 1px solid black;}.verticalPage {width: 100%;}.img {text-align: center;}</style>
L120S09a.checkbox1=Borrower
L120S09a.checkbox2=Co-borrower
L120S09a.checkbox3=Principal
L120S09a.checkbox4=Association guarantor
L120S09a.checkbox5=Collateral provider
L120S09a.checkbox6=Affiliated company
L120S09a.checkbox7=Effective Bene
L120S09a.checkbox8=General guarantor
L120S09a.checkbox9=Factoring buyer without recourse
L120S09a.checkbox10=Senior management
L120S09a.checkbox11=Controlling Person
L120S09a.checkbox99=Director and Supervisor
L160S01E.custId=\u7d71\u7de8
L160S01E.custName=\u6236\u540d
L160S01E.custRelation=\u8207\u672c\u6848\u95dc\u4fc2
L160S01E.queryDateS=\u8cc7\u6599\u67e5\u8a62\u65e5
L160S01E.chkYN=\u8cc7\u6599\u6aa2\u8aa4
L160S01E.type=\u67e5\u8a62\u9805\u76ee

#J-109-0150_10702_B1001 Web e-Loan IVR\u9801\u7c64\u7531\u6a21\u64ec\u52d5\u5be9\u79fb\u81f3\u52d5\u5be9\u8868
cls3301v00.custId=\u7d71\u7de8
cls3301v00.custName=\u501f\u6b3e\u4eba
cls3301v00.record_Bran_Name=\u5206\u884c
cls3301v00.record_FileName=\u9304\u97f3\u6a94\u540d
cls3301v00.record_User_Code=\u5efa\u6a94\u4eba\u54e1
cls3301v00.record_Create_Date=\u5efa\u6a94\u65e5\u671f
cls3301v00.mgr_User_Code=\u8986\u6838\u4e3b\u7ba1
cls3301v00.mgr_Sign_Date=\u4e3b\u7ba1\u8986\u6838\u65e5\u671f
cls3301v00.record_Url=URL
cls3301v00.title=\u8a9e\u97f3\u6a94
button.selectIVR =\u9078\u64c7\u8a9e\u97f3\u6a94
button.deleteIVR =\u522a\u9664
cls3301v00.descIVR=\u5c08\u6848\u7a2e\u985e\u300e08 \u7279\u5b9a\u91d1\u9322\u4fe1\u8a17\u53d7\u76ca\u6b0a\u81ea\u884c\u8a2d\u8cea\u64d4\u4fdd\u6388\u4fe1\u300f\u9700\u9078\u64c7\u8a9e\u97f3\u6a94
cls3301v00.error.01=\u501f\u6b3e\u4eba\uff1a{0}\u3001\u984d\u5ea6\u5e8f\u865f\uff1a{1}\uff0c\u5c08\u6848\u7a2e\u985e\u300e08 \u7279\u5b9a\u91d1\u9322\u4fe1\u8a17\u53d7\u76ca\u6b0a\u81ea\u884c\u8a2d\u8cea\u64d4\u4fdd\u6388\u4fe1\u300f\u9700\u9078\u64c7\u8a9e\u97f3\u6a94
cls3301v00.error.02=\u984d\u5ea6\u660e\u7d30\u8868({0})\u6027\u8cea\u70ba\u300c\u589e\u984d\u300d\u6216\u300c\u8b8a\u66f4\u689d\u4ef6\u300d\u4e14\u300c\u5e63\u5225\u300d\u6b04\u4f4d\u6709\u7570\u52d5\u6642\uff0c\u9700\u9078\u64c7\u8a9e\u97f3\u6a94


#J-110-0540_05097_B1001 Web e-Loan\u4f01\u91d1\u6388\u4fe1\u914d\u5408\u8abf\u6574E-loan\u7cfb\u7d71\u52d5\u7528\u5be9\u6838\u8868\u90e8\u5206\u5167\u5bb9
common.1=\u4e00
common.2=\u4e8c
common.3=\u4e09
common.4=\u56db
common.5=\u4e94
common.6=\u516d
common.7=\u4e03
common.8=\u516b
common.9=\u4e5d
common.10=\u5341

#G-113-0036 \u4e3b\u5f9e\u50b5\u52d9\u4eba
L140M01a.message28=Is the accounting branch (Mandated Leader Arranger) for this credit line the same as the branch that applied for the credit line
L140M01a.message29=Please select the source of the credit limit serial number
L140M01a.message30=Generate New (applicable to "New" cases)
L140M01a.message32=Please input the original credit limit serial number
L140M01a.message33=Please input the original credit limit serial number: the old credit limit serial number needs to be converted into the new format
L140M01a.message68=The length of Credit Limit Serial Number Should be 12 bytes, encode rule:XXX(branch code)+X(1:DBU,4:OBU,5:Overseas)+YYY(Year)+99999(Serial No.)
L140M01a.message69=The old Credit Limit Serial No.: {0} not exist in account system, please comfirm and input again
L140M01a.message90=\u984d\u5ea6\u660e\u7d30\u8868\u4e4b\u984d\u5ea6\u63a7\u7ba1\u7a2e\u985e\u300e{0}\u300f\u8207a-Loan\u300e{1}\u300f\u4e0d\u540c\uff0c\u914d\u5408a-Loan\u984d\u5ea6\u63a7\u7ba1\u4f5c\u696d\u9700\u8981\u91cd\u65b0\u7522\u751f\u65b0\u984d\u5ea6\u5e8f\u865f!!<br/>\u662f\u5426\u8981\u7522\u751f\u65b0\u984d\u5ea6\u5e8f\u865f!!
btn.number=Assign Number
L162S02A.grtAmt=Guarantee Amount
L162S02A.localId=Local Id
L162S02A.message01=Guarantee Amount and local ID only need to be filled in by overseas branches
L162S02A.btn01=Excel template download
L162S02A.btn02=Excel Import  Borrower's & Guarantor's Profile
L162S02A.message1=There is no data in sheet1 in the file!
L162S02A.message2=When the principal debtor and the secondary debtor are different, the relationship with the principal debtor should not be that of the principal debtor.
L162S02A.message3=\u300cBorrower's & Guarantor's Profile Sheet\u300dhas Incomplete information, do not perform this function {0}
L162S02A.message4=The quota serial number of "Master and Subordinate Debtor Information Table" cannot be empty!!
L162S02A.message5=Cannot be blank!!
L162S02A.message6={0} This country code is not found!
L162S02A.message7={0}No such related identity found!
L162S02A.message8={0}No such debtor category found!
L162S02A.message9={0}There is no relationship between this and the principal debtor!
L162S02A.message10=The expiry date of the guarantor's guarantee/the expiration date of the term of directors and supervisors does not conform to the yyyy-MM-dd format. If there is no such date, please leave it blank.
L162S02A.message11={0}There is no reason code for requesting a guarantor!
L162S02A.message12={0} guarantees that the number must be greater than or equal to 0 and less than or equal to 100
L162S02A.message13={0}Whether the bank\u2019s national risk insurance is calculated based on the country of the guarantor (replacing the final risk country), it can only be blank or YN
L162S02A.message14=To check the unowned debtor, please reset the page and upload it again.
L162S02A.message15=When the relevant identity is C co-borrower or S collateral provider, whether the bank's country risk insurance is calculated based on the country of the guarantor (replacing the final risk country), it can only be left blank
L162S02A.message16=Amount serial number: {0}, debtor ID: {1}, already exists, please do not create another file!
L162S02A.message17=Amount serial number in Excel: {0}, debtor ID: {1}, already exists, please do not create a file again!
L162S02A.message18=Please make sure you have clicked "Obtain the Guarantor's Credit Rating Order"
L162S02A.message19=Guarantee Amount and local ID only need to be filled in by overseas branches
L162S02A.message20=If there is no customer file creation, please go to 0024 to complete the file creation first
L162S02A.message21=Credit Limit Serial Number in Excel:{0} is not on the Drawdown Approval Sheet
L162S02A.FileUpload.checkfileMsg=\u6a94\u6848\u4e2d\uff0c\u7b2c$\{0\}\u5217\u4e2d$\{1\}-$\{2\},\u8acb\u67e5\u660e\u5f8c\u518d\u57f7\u884c\uff01
L162S02A.FileUpload=FileUpload
# \u984d\u5ea6\u806f\u884c\u6524\u8cb8\u6bd4\u4f8b\u6a94
L140M01e.title01=Input Syndication Lenders' Allocation
L140M01e.shareBrId=Lending Branch
L140M01e.shareRate1=Allocated Percentage
L140M01e.shareRate2=Allocation Denominator
L140M01e.shareAmt=Allocated Amount
L140M01e.totalAmt=Total Syndication Loan
L140M01e.shareNo=Credit Limit Serial Number
L140M01e.lmterror=Unable to save because the sum of numerators is greated than the denominator
L140M01e.shareRateError=Remaining Numerator
L140M01e.shareRateError2=The numerator can not be
L140M01e.shareAmtError=Allocation Remaining
L140M01e.count=Calculate Amount
L140M01e.count2=Calculate Percentage
L140M01e.self=Self-numbering
L140M01e.message01=Input Syndication Lenders' Allocation First
L140M01e.message02=To use this feature to the calculation of the proportion of
L140M01e.message03=Denominator can not be 0
L140M01e.message04=Limit conditions for the second line to line amortization loan molecular plus the total is not equal to the denominator
L140M01e.message05=Limit conditions of the second line line amortization loan amount is not equal to the Credit Facility Report's current requested amount
L140M01e.message06=The Credit Facility Report's Syndication Lenders' Allocation Balance {0}, Please select which branch you want to join the balance
L140M01e.message07=The branch is not the managing bank, so we cannot add / modify / delete the Affiliate Bank information.
L140M01e.message08=Drawdown Approval Sheet's Applied Credit Limit and Credit Facility Report's Syndication Lenders' Allocation Balance Self loan amount not match
L140M01e.message09=Applied Credit Limit is greated than Credit Facility Report's Syndication Lenders' Allocation Balance Self loan amount
btn.changesShareRate2=Change the denominator
L160M01A.ApprovalSheetAllocationInfo=The Credit Facility Report's Drawdown Approval Sheet Syndication Lenders' Allocation
#J-113-0035  ELOAN\u570b\u5167\u5916\u4f01\u91d1\u7cfb\u7d71\u984d\u5ea6\u660e\u7d30\u8868\u5176\u4ed6\u6558\u505a\u689d\u4ef6\u589e\u52a0\u300c\u61c9\u6ce8\u610f\u627f\u8afe\u5f85\u8ffd\u8e64ESG\u9023\u7d50\u689d\u6b3e\u300d\u7684\u767b\u9304\u6a5f\u5236
L140S12A.message01=\u6709\u95dc\u627f\u8afe\u4e8b\u9805\u5167\u5bb9\u56e0\u5b57\u6578\u9650\u5236\uff0c\u8acb\u81f3\u8cb8\u5f8c\u7ba1\u7406\u5e73\u53f0\u67e5\u660e\u3002
L140S12A.message02=\u6709\u95dc\u6ce8\u610f\u4e8b\u9805\u5167\u5bb9\u56e0\u5b57\u6578\u9650\u5236\uff0c\u8acb\u81f3\u8cb8\u5f8c\u7ba1\u7406\u5e73\u53f0\u67e5\u660e\u3002
#J-112-0522 \u806f\u8cb8\u6848\u4ef6\u6aa2\u6838\u6524\u8cb8\u984d\u5ea6\u5e8f\u865f\u9700\u5b58\u5728\u65bcLNF277
L140M01E_AF.message01=\u984d\u5ea6\u5e8f\u865f{0}\uff0c\u984d\u5ea6\u660e\u7d30\u8868\u806f\u884c\u6524\u8cb8\u6bd4\u4f8b\u984d\u5ea6\u5e8f\u865f\u672a\u5b58\u5728\u65bc\u806f\u8cb8\u6848\u5efa\u6a94\u7dad\u8b77\uff0c\u8acb\u5148\u81f3\u529f\u80fd \u5efa\u6a94\u7dad\u8b77>>\u806f\u8cb8\u6848\u5efa\u6a94\u7dad\u8b77 \u7d81\u5b9a\u8a72\u984d\u5ea6\u5e8f\u865f