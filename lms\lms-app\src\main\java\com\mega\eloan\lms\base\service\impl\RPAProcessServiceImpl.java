/* 
 * AMLRelateServiceImpl.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.RPAService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.dao.C101S04WDao;
import com.mega.eloan.lms.dao.C120S04WDao;
import com.mega.eloan.lms.dao.C126S01ADao;
import com.mega.eloan.lms.dao.L820M01WDao;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120S04W;
import com.mega.eloan.lms.model.C126S01A;
import com.mega.eloan.lms.model.L820M01W;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * BY 專案共用Service
 * </pre>
 * 
 * @since 2021/04/20
 * <AUTHOR>
 * @version
 * 
 */
@Service("rpaQueryService")
public class RPAProcessServiceImpl extends AbstractCapService implements RPAProcessService {
	
	@Resource
	SysParameterService sysParamService;
	
	@Resource
	C101S04WDao c101s04wDao;
	
	@Resource
	C120S04WDao c120s04wDao;
	
	@Resource
	L820M01WDao l820m01wDao;
	
	@Resource
	RPAService rpaservice;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	C126S01ADao c126s01aDao;
	
	@Resource
	LMSService lmsService;
	
	public final static Map<String, String> statusNameMap = new HashMap<String, String>();
	static{
		statusNameMap.put(UtilConstants.RPA.STATUS.查詢失敗, "查詢失敗");
		statusNameMap.put(UtilConstants.RPA.STATUS.查詢中, "查詢中");
		statusNameMap.put(UtilConstants.RPA.SEARCH_RESULT.有案件, "有案件");
		statusNameMap.put(UtilConstants.RPA.SEARCH_RESULT.查無資料, "查無資料");
		statusNameMap.put(UtilConstants.RPA.STATUS.查詢完成, "查詢完成");
	}

	protected final Logger logger = LoggerFactory.getLogger(getClass());
	
	public String gotoRPAJobs(C101S04W c101s04w) throws CapException {
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "", processKey = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		objResult = new LinkedHashMap<String, Object>();
		try {
			
			// Step 1 取得 token
			String token = this.getToken();
			
			// Step 2 啟動JOB-受監護輔助宣告查詢
			logger.info("啟動JOB-受監護輔助宣告查詢 開始========================");

			c101s04w.setResponseURL(sysParamService.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS));
			c101s04w.setSystem("eloan");
			c101s04w.setBranchNo(user.getUnitNo());
			c101s04w.setEmpNo(CapString.isNumeric(user.getUserId()) ? String.format("%06d", Integer.valueOf(user.getUserId())) : user.getUserId());
			c101s04w.setStatus(UtilConstants.RPA.STATUS.查詢中);
			c101s04w.setUniqueID(c101s04w.getMainId() + 
							"|" + c101s04w.getDataCustomerNo() + 
							"|" + c101s04w.getBranchNo() + 
							"|" + c101s04w.getEmpNo());
			c101s04w.setQueryTime(CapDate.getCurrentTimestamp());

			this.lmsService.saveC101S04W(c101s04w);

			// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
			objResult.put("responseURL", c101s04w.getResponseURL());
														// 回傳位址
														// SIT
														// :
														// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
			objResult.put("system", c101s04w.getSystem());
			objResult.put("uniqueID", c101s04w.getUniqueID());//mainId + dataCustomerNo + branchNo + empNo
			objResult.put("branchNo", c101s04w.getBranchNo());
			objResult.put("empNo", c101s04w.getEmpNo());
			objResult.put("data_CustomerNo", c101s04w.getDataCustomerNo()); // 負責人ID

			logger.info("傳入參數==>[{}]", objResult.toString());

			rpaservice.StartRPAJobForLMS(objResult, token, processKey, SysParamConstants.RPA_受監護輔助宣告);

			logger.info("啟動JOB-受監護輔助宣告查詢 結束========================");
			
		} catch (Exception e) {
			errorMsg = "RPA Job建立失敗，請稍後再試。";
			throw new CapMessageException("RPA Job建立失敗，請稍後再試。",
					this.getClass());
		}

		return errorMsg;
	}
	
	private String getToken() throws CapMessageException{
		
		// Step 1 取得 token
		String token = null;
		JSONObject resultJson = null;
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		try {
			resultJson = rpaservice.getRPAAccessToken(objResult);
			token = resultJson != null ? resultJson.getString("result")
					: "";
			token = "Bearer " + token;
			
		} catch (Exception e) {
			
			throw new CapMessageException("取得RPA Token失敗，請稍後再試。", this.getClass());
		} finally{
			
			if (CapString.isEmpty(token)) {
				throw new CapMessageException("getRPAAccessToken失敗，請稍後再試。", this.getClass());
			}
		}	
		
		return token;
	}
	
	@Override
	public void deleteBeforeQueryData(String mainId, String dataCustomerNo){
		
		List<C101S04W> c101s04wList = c101s04wDao.findBy(mainId, dataCustomerNo);
		
		if(c101s04wList != null){
			
			for(C101S04W c101s04w : c101s04wList){
				
				String docFileOid = c101s04w.getDocfileoid();
				if (!CapString.isEmpty(docFileOid)) {
					// 先刪除附件(setDeletedTime)
					docFileService.clean(docFileOid);
//					DocFile oldDocFile = docFileService.findByOidAndSysId(docFileOid, "LMS");
//					File file = docFileService.getRealFile(oldDocFile);
//					if (file.exists()) {
//						FileUtils.deleteQuietly(file);
//					}
				}
				c101s04wDao.delete(c101s04w);
			}
		}
	}
	
	@Override
	public C101S04W getC101S04WBy (String mainId, String custId){
		List<C101S04W> c101s04wList = this.c101s04wDao.findBy(mainId, custId);
		return !c101s04wList.isEmpty() ?  c101s04wList.get(0) : null;
	}
	
	@Override
	public C120S04W getC120S04WBy (String mainId, String custId){
		List<C120S04W> c120s04wList = this.c120s04wDao.findBy(mainId, custId);
		return !c120s04wList.isEmpty() ?  c120s04wList.get(0) : null;
	}
	
	@Override
	public String getDataStatus(String status, String returnData) {
		
		try{
			
			if(UtilConstants.RPA.STATUS.查詢失敗.equals(status) || StringUtils.isBlank(returnData)){
				return UtilConstants.RPA.STATUS.查詢失敗;
			}
			
			if(UtilConstants.RPA.STATUS.查詢中.equals(status)){
				return UtilConstants.RPA.STATUS.查詢中;
			}
			
			if(UtilConstants.RPA.STATUS.查詢完成.equals(status)){
				@SuppressWarnings("unchecked")
				Map<String, String> map = new ObjectMapper().readValue(returnData, Map.class);
				return map.get("data_searchResult");// 0:有案件 or 1:查無資料
			}
			
		} catch (IOException e){
			logger.error("returnData資料轉換失敗: " + e.getMessage());
		}
		
		return UtilConstants.RPA.STATUS.查詢失敗;
	}
	
	@Override
	public String gotoRPAJobsForGettingRealEstateAgentCertNo(C126S01A c126s01a) throws CapException {
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "", processKey = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		objResult = new LinkedHashMap<String, Object>();
		try {
			
			// Step 1 取得 token
			String token = this.getToken();
			
			// Step 2 啟動JOB-受監護輔助宣告查詢
			logger.info("啟動JOB-受監護輔助宣告查詢 開始========================");

			c126s01a.setResponseURL(sysParamService.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS));
			c126s01a.setSystem("eloan");
			c126s01a.setBranchNo(user.getUnitNo());
			c126s01a.setEmpNo(CapString.isNumeric(user.getUserId()) ? String.format("%06d", Integer.valueOf(user.getUserId())) : user.getUserId());
			
			
			c126s01a.setUniqueID(c126s01a.getMainId());
			
			c126s01a.setStatus(UtilConstants.RPA.STATUS.查詢中);
			
			c126s01a.setQueryTime(CapDate.getCurrentTimestamp());

			this.saveC126S01A(c126s01a);

			// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
			objResult.put("responseURL", c126s01a.getResponseURL());
														// 回傳位址
														// SIT
														// :
														// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
			objResult.put("system", c126s01a.getSystem());
			objResult.put("uniqueID", c126s01a.getUniqueID());//mainId + dataCustomerNo + branchNo + empNo
			objResult.put("branchNo", c126s01a.getBranchNo());
			objResult.put("empNo", c126s01a.getEmpNo());//發查員工編號
			objResult.put("custId", c126s01a.getCustId() == null ? "" : c126s01a.getCustId());//客戶ID
			objResult.put("realtorName", c126s01a.getRealtorName());//引介房仲姓名

			logger.info("傳入參數==>[{}]", objResult.toString());

			rpaservice.StartRPAJobForLMS(objResult, token, processKey, SysParamConstants.RPA_查詢房仲證書字號);

			logger.info("啟動JOB-受監護輔助宣告查詢 結束========================");
			
		} catch (Exception e) {
			errorMsg = "RPA Job建立失敗，請稍後再試。";
			throw new CapMessageException("RPA Job建立失敗，請稍後再試。",
					this.getClass());
		}

		return errorMsg;
	}
	
	@Override
	public void saveC126S01A(C126S01A c126s01a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (user != null) {
			c126s01a.setUpdater(user.getUserId());
			c126s01a.setUpdateTime(CapDate.getCurrentTimestamp());
			if (c126s01a.getCreator() == null) {
				c126s01a.setCreator(user.getUserId());
				c126s01a.setCreateTime(CapDate.getCurrentTimestamp());
			}
		}
		this.c126s01aDao.save(c126s01a);
	}
	
	@Override
	public C126S01A getC126S01ABy (String mainId){
		List<C126S01A> c126s01aList = this.c126s01aDao.findByMainId(mainId);
		return !c126s01aList.isEmpty() ?  c126s01aList.get(0) : null;
	}
	
	@Override
	public List<Map<String, Object>> getC126S01ARealtorInfo(String mainId) throws CapMessageException, JsonParseException, JsonMappingException, IOException{
		
		C126S01A c126s01a = this.getC126S01ABy(mainId);
		
		if(c126s01a == null){
			return new ArrayList<Map<String, Object>>();
		}

		List<Map<String, Object>> rtnList = new ArrayList<Map<String, Object>>();

		if(UtilConstants.RPA.STATUS.查詢完成.equals(c126s01a.getStatus())){
			
			@SuppressWarnings("rawtypes")
			List<Map> lista = Arrays.asList(new ObjectMapper().readValue(c126s01a.getRealtorInfo(), Map[].class));
			
			for(Map<String, Object> m : lista){
				String licensePeriod = CapDate.formatDateFromF1ToF2(String.valueOf(m.get("licensePeriod")), "YYY/MM/DD", "yyyy-MM-dd");
				String licenseFullName = String.valueOf(m.get("licenseYear")) + m.get("licenseword") + m.get("licenseNumber") + "號";
				m.put("licenseFullName", licenseFullName);
				m.put("licensePeriod", licensePeriod);
				rtnList.add(m);
			}
			
			return rtnList;
		}
		
		return rtnList;
	}
	
	@Override
	public void deleteBeforeQueryRealEstateAgentCertNo(String mainId){
		
		List<C126S01A> c126s01aList = this.c126s01aDao.findByMainId(mainId);
		if(c126s01aList != null){
			
			for(C126S01A c126s01a : c126s01aList){
				c126s01aDao.delete(c126s01a);
			}
		}
	}

	@Override
	public String gotoRPAJobsForL820(L820M01W l820m01w) throws CapException {

		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String errorMsg = "", processKey = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		objResult = new LinkedHashMap<String, Object>();
		try {
			
			// Step 1 取得 token
			String token = this.getToken();
			
			// Step 2 啟動JOB-受監護輔助宣告查詢
			logger.info("啟動JOB-受監護輔助宣告查詢 開始========================");

			l820m01w.setResponseURL(sysParamService.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS));
			l820m01w.setSystem("eloan");
			l820m01w.setBranchNo(user.getUnitNo());
			l820m01w.setEmpNo(CapString.isNumeric(user.getUserId()) ? String.format("%06d", Integer.valueOf(user.getUserId())) : user.getUserId());
			l820m01w.setStatus(UtilConstants.RPA.STATUS.查詢中);
			l820m01w.setUniqueID(l820m01w.getMainId() + 
							"|" + l820m01w.getDataCustomerNo() + 
							"|" + l820m01w.getBranchNo() + 
							"|" + l820m01w.getEmpNo());
			l820m01w.setQueryTime(CapDate.getCurrentTimestamp());

			this.saveL820M01W(l820m01w);

			// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
			objResult.put("responseURL", l820m01w.getResponseURL());
														// 回傳位址
														// SIT
														// :
														// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
			objResult.put("system", l820m01w.getSystem());
			objResult.put("uniqueID", l820m01w.getUniqueID());//mainId + dataCustomerNo + branchNo + empNo
			objResult.put("branchNo", l820m01w.getBranchNo());
			objResult.put("empNo", l820m01w.getEmpNo());
			objResult.put("data_CustomerNo", l820m01w.getDataCustomerNo()); // 負責人ID

			logger.info("傳入參數==>[{}]", objResult.toString());

			rpaservice.StartRPAJobForLMS(objResult, token, processKey, SysParamConstants.RPA_受監護輔助宣告);

			logger.info("啟動JOB-受監護輔助宣告查詢 結束========================");
			
		} catch (Exception e) {
			errorMsg = "RPA Job建立失敗，請稍後再試。";
			throw new CapMessageException("RPA Job建立失敗，請稍後再試。",
					this.getClass());
		}

		return errorMsg;
	
	}

	@Override
	public L820M01W getL820M01WBy(String mainId, String custId) {
		List<L820M01W> l820m01wList = this.l820m01wDao.findBy(mainId, custId);
		return !l820m01wList.isEmpty() ?  l820m01wList.get(0) : null;
	}
	
	@Override
	public void saveL820M01W(L820M01W l820m01w) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (user != null) {
			l820m01w.setUpdater(user.getUserId());
			l820m01w.setUpdateTime(CapDate.getCurrentTimestamp());
			if (l820m01w.getCreator() == null) {
				l820m01w.setCreator(user.getUserId());
				l820m01w.setCreateTime(CapDate.getCurrentTimestamp());
			}
		}
		l820m01wDao.save(l820m01w);
	}

	@Override
	public void deleteBeforeQueryL820(String mainId, String dataCustomerNo) {

		
		List<L820M01W> l820m01wList = l820m01wDao.findBy(mainId, dataCustomerNo);
		
		if(l820m01wList != null){
			
			for(L820M01W l820m01w : l820m01wList){
				
				String docFileOid = l820m01w.getDocfileoid();
				if (!CapString.isEmpty(docFileOid)) {
					// 先刪除附件(setDeletedTime)
					docFileService.clean(docFileOid);
//					DocFile oldDocFile = docFileService.findByOidAndSysId(docFileOid, "LMS");
//					File file = docFileService.getRealFile(oldDocFile);
//					if (file.exists()) {
//						FileUtils.deleteQuietly(file);
//					}
				}
				l820m01wDao.delete(l820m01w);
			}
		}
	
	}
}
