package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisLN811Service;

@Service
public class MisLN811ServiceImpl extends AbstractMFAloanJdbc implements
		MisLN811Service {
	public List<?> findLN811ForL120S05A2(String grpNo) {
		return this.getJdbc().queryForList("LN811.selL120s05a2",
				new String[] { grpNo });
	}

	public List<?> findLN811ForL120S05C2(String custId, String dupNo) {
		return this.getJdbc().queryForList("LN811.selL120s05c2",
				new Object[] { custId, dupNo, custId, dupNo });
	}

	public List<?> findLNFE0811(String id, String dupno) {
		return this.getJdbc().queryForList("LNLNFE0811.selLNFE0811",
				new String[] { id, dupno });
	}

	public List<?> findLN811ForL120S05A2_A(String custId, String dupNo) {
		return this.getJdbc().queryForList("LN811.selL120s05a2_A",
				new Object[] { custId, dupNo, custId, dupNo });
	}
}
