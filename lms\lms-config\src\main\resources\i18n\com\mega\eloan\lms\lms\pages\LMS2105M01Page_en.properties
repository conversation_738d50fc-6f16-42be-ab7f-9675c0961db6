#==================================================
# \u4fee\u6539\u8cc7\u6599\u7279\u6b8a\u6d41\u7a0b js\u7528
L210M01A.message01=Please input the approval date
L210M01A.message02=Whether to return the case to the handling officer for amendments; to return, please press [OK], otherwise press [Cancel]
L210M01A.message03=Whether to approve the case; to confirm, press [OK], otherwise press [Cancel] to exit
L210M01A.message04=Whether to return the case; to confirm, press [OK], otherwise press [Cancel] to exit
L210M01A.message05=Please Select
L210M01A.message06=Peer list of Tam loan ratio molecular aggregate is not equal to the denominator
L210M01A.message07=Peer-amortization loans ratio list of the share of loan-to-aggregate is not equal to the total
L210M01A.message08=Syndicated loan Participation loan ratio of a list of the aggregate total is not equal to
L210M01A.message09=Syndicated Loan Participation loan ratio List can not be empty
L210M01A.message10=Peer-amortization loan ratio List can not be empty
# \u4fee\u6539\u8cc7\u6599\u7279\u6b8a\u6d41\u7a0bGrid
page.title=LMS2105M01 Special Procedures For Editing Data
L210M01A.mainCustId=Principal Borrower's UBN
L210M01A.mainCust=Principal Borrower
L210M01A.caseNo=Case No.
L210M01A.creatorPerson=Handling Officer
L210M01A.modifyType=Alteration Categories
L210M01A.modifyDate=Date Of Change
L210M01A.caseDate=Signature Date
L210M01A.type=Change To Syndication Loan Allocation
L210M01A.docStatus=Status
## \u4fee\u6539\u8cc7\u6599\u7279\u6b8a\u6d41\u7a0b\u4e3b\u6a94 m01
L210M01A.title01=Special procedures for editing data
L210M01A.title02=Application Summary
L210M01A.title03=Before Condition Change
L210M01A.title04=After Condition Change
L210M01A.bt01=Return to handling officer for correction
L210M01A.bt02=Approval
L210M01A.bt03=Return
L210M01A.bt04=Verifier
L210M01A.bt05=Submit For Supervisor's Approval
L210M01A.managerId=Unit/Authorizer
L210M01A.bossId=Account Administrator
L210M01A.appraiserId=Handling Officer
L210M01A.bfReCheckDate=Approval Date
L210M01A.check=Signature Column
L210M01A.no=\u3000
L210M01A.site=\u3000th Persons
L210M01A.selectBoss=Number Of Credit Supervisors
L210M01A.reCheckId=Approver
L210M01A.apprId=Handling Officer
#\u4fee\u6539\u8cc7\u6599\u7279\u6b8a\u6d41\u7a0b\u4e3b\u6a94 s01
L210M01A.randomCode=Random Report Code
L210M01A.bussin=Business Unit
# \u4fee\u6539\u8cc7\u6599\u7279\u6b8a\u6d41\u7a0b\u4e3b\u6a94 s02
L210M01A.s02title01=Application Summary - Loan Information
L210M01A.s02title02=Currency
L210M01A.s02title03=Total Credit Line
L210M01A.s02title04=Credit Limit Serial Number
L210M01A.s02title05=Unit
L210M01A.s02title06=Dollars
#\u4fee\u6539\u8cc7\u6599\u7279\u6b8a\u6d41\u7a0b\u4e3b\u6a94 s03
L210M01A.s03title01=Before-change
L210M01A.s03title02=Allocation Rate
L210M01A.s03title03=Participating Bank/Branch
L210M01A.s03title04=Joint Lead Arranger
L210M01A.s03title05=Peer Account Number
L210M01A.s03title06=Allocated Loan Quantum
L210M01A.s03title07=Type Of Financial Institution
L210M01A.s03title08=Syndication Loan Allocation Table
L210M01A.s03title09=Lending Branch
L210M01A.s03title10=Allocated Amount
L210M01A.s03title11=Allocated Percentage
L210M01A.s03title12=Credit Limit Serial Number
L210M01A.s03title13=Peer-amortization loan ratio List
L210M01A.s03title14=Input Syndication Lenders' Allocation
L210M01A.s03title15=Please input
L210M01A.s03type01=Syndicated Across Banking Peers
L210M01A.s03type02=Syndicated Across Internal Branches
# \u4fee\u6539\u8cc7\u6599\u7279\u6b8a\u6d41\u7a0b\u4e3b\u6a94 s04
L210M01A.s04Bt01=Add Syndication Loan Allocated Percentages
L210M01A.s04Bt02=Originating Branch
L210M01A.s04Bt03=Calculate Amount
L210M01A.s04Bt04=Calculate Percentage
L210M01A.s04Bt05=If the old ginseng loan LaSalle / branch new syndicated loan ratio does not account for any amount Do not delete the branch set, only the parameters loan amount is set to zero can be.
L210M01A.error1=The database contains no bank ID:
L210M01A.s04message01=Please select the source of the credit limit serial number
L210M01A.s04message02=Generate New (applicable to "New" cases)
L210M01A.s04message03=Please input the original credit limit serial number (applicable to renewal cases and cases involving change of terms)
L210M01A.s04message04=Please input the ID of the bookkeeping branch (3 digits) that will be generating the credit limit serial number
L210M01A.s04message05=Please input the original credit limit serial number: the old credit limit serial number needs to be converted into the new format
L210M01A.s04message06=The length of Credit Limit Serial Number Should be 12 bytes, encode rule:XXX(branch code)+X(1:DBU,4:OBU,5:Overseas)+YYY(Year)+99999(Serial No.)


