package com.mega.eloan.lms.mfaloan.service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;

public interface MisRptDataService {
	/**
	 * 取得 已敘作消金案件清單
	 * 
	 * @param brno
	 *            分行編號
	 * @param bgnDate
	 *            起日
	 * @param endDate
	 *            迄日
	 * @return List<Map<String, Object>> data
	 */
	List<Map<String, Object>> getCLS180R01Data(String brno, String bgnDate,
			String endDate);

	/**
	 * 取得 已敘作消金案件清單
	 * 
	 * @param formId
	 *            報表名
	 * @return List<Map<String, Object>> data
	 */
	List<Map<String, Object>> getCLS180R11Data(String formId);

	/**
	 * 取得 消金授信已逾期控制表
	 * 
	 * @param String
	 *            報表名
	 * @return List<Map<String, Object>> data
	 */
	List<Map<String, Object>> getCLS180R10Data(String brno, String bgnDate,
			String endDate);

	/**
	 * 用統編+重覆碼 查詢 使用者名稱
	 * 
	 * @param String
	 *            報表名
	 * @return List<Map<String, Object>> data
	 */
	Map<String, Object> getCustData(String custId, String dupNo);

	/**
	 * 管理報表 type1=授信契約已逾期控制表
	 * 
	 * @param brNo
	 * @param baseDateStr
	 * @return
	 */

	public List<Map<String, Object>> findforNewReportType1ByBrNo(String brNo,
			String dateStartDate, String dateEndDate);

	/**
	 * 取得額度資料
	 * 
	 * @param custId
	 * @return
	 */
	public List<Map<String, Object>> findTotAmt(String custId);

	/**
	 * 計算月報該總金額
	 * 
	 * @param custId
	 *            客戶統編
	 * @return 幣別 、 金額
	 */
	public List<Map<String, Object>> caleLNF020AMT(HashSet<String> custIds);

	List<Map<String, Object>> getCLS250R01Data_ELF516_ForRptDataNoEdit();

	List<Map<String, Object>> getCLS250R01Data_ELF516_ForRptDataEdit(
			String begDate, String edDate);

	List<Map<String, Object>> getCLS250R01Data_ELF516_ForRptDataAll(
			String begDate, String edDate);
}
