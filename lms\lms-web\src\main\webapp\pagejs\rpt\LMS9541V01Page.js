var brno = userInfo ? userInfo.unitNo : "";

var pageAction = {
	LIMIT : 5000,
	tooMuch : false,
	handler : 'lms9541v01formhandler',
	grid : null,
	viewGrid : null,
	build : function(){
		pageAction.grid = $("#gridview").iGrid({
			handler : 'lms9541v01gridhandler',
			height : 400,
			action :  "query",
			rowNum:15,
			rownumbers:false,
			colModel : [{
				colHeader : "oid",
				name : 'oid',
				hidden : true //是否隱藏
			},{
				colHeader : "brno", //分行代號
				hidden : true,
				name : 'brno' //col.id
			},{
				colHeader : "rptType", //報表類型
				hidden : true,
				name : 'rptType' //col.id
			},{
				colHeader : i18n.lms9541v01["rptName"], //報表名稱
				align : "center",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				hidden : false,
				//formatter : 'click',
				//onclick : function,
				name : 'rptName' //col.id
			},{
				colHeader : "rptOid", //建立人員號碼
				hidden : true,
				name : 'rptOid' //col.id
			},{
				colHeader : "creator", //建立人員號碼
				hidden : true,
				name : 'creator' //col.id
			},{
				colHeader : i18n.lms9541v01["rptTime"], //建立日期
				align : "center",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				hidden : false,
				//formatter : 'click',
				//onclick : function,
				name : 'createTime' //col.id
			},{
				colHeader : "updater", //異動人員號碼
				hidden : true,
				name : 'updater' //col.id
			},{
				colHeader : "updateTime", //異動日期
				hidden : true,
				name : 'updateTime' //col.id
			}],
			ondblClickRow: function(rowid){//同列印
				var data = pageAction.grid.getRowData(rowid);
				pageAction.openView(data);
			}				
		});
		//build button 
		//列印
		$("#buttonPanel").find("#btnAdd").click(function() {
			//開窗~~
			$("div#addThickBox").thickbox({
				title : i18n.lms9541v01["addTitle"],
				width : 430,
				height : 280,
				modal : true,
				align : 'center',
				valign: 'bottom',
				i18n: i18n.def,
				buttons : {
					'sure' : function(){
						$.ajax({
							handler : pageAction.handler,
							action : 'add',
							data : {
								type:$("input[name=type]:checked").val()
							},
							success:function(response){
								if(response["exist"]){
									MegaApi.showErrorMessage(i18n.def["confirmTitle"],i18n.lms9541v01["isRepeat"]);
								}
								else{
									$.thickbox.close();
									pageAction.reloadGrid();
									MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["addSuccess"]);
								}
							}
						});
					},
					'close' : function(){//關閉
						$.thickbox.close();
					}
				}
			});
		})
		//調閱
		.end().find("#btnView").click(function() {
			var data = pageAction.getRowData();
			if (data){
				pageAction.openView(data);
			}
		})
		//列印(輸出excel)
		.end().find("#btnPrint").click(function() {
			var data = pageAction.getRowData();
			if (data){
				
				MegaApi.confirmMessage(i18n.lms9541v01["confirmOutput"], function(action){
					if (action){
						$.capFileDownload({          
								 handler:"lmsdownloadformhandler",
								 data: {
								      	oid : data.oid,
								      	rptOid : data.rptOid,
								      	kindNo : data.rptType,
									 	tooMuch : pageAction.tooMuch,
										brno : pageAction.tooMuch?$("select#brnoSel").val():brno,
										fileDownloadName : data.rptName+".xls",
										serviceName : "lms9541v01xlsservice"
									   	//其他參數
								}
							});
					}
				});
			}
		});
	},
	openView : function(data){
		$.ajax({
			handler : pageAction.handler,
			action : 'getTotal',
			data : data,
			success:function(response){
				$("input#total").val(response.total);
				$("input#totAppMoney").val(response.totAppMoney);
				$("input#totFavloan").val(response.totFavloan);
				

				//grid設定~~~
				if(pageAction.viewGrid==null){
					pageAction.viewGrid = $("#viewGrid").iGrid({
						handler : 'lms9541v01gridhandler',
						postData : {
							type: data.rptType,
							tooMuch : pageAction.tooMuch,
							brno: (response.totalNum>=pageAction.LIMIT)?"":brno
						},
						height : 300,
						action : "queryView",
						rowNum : 13,
						rownumbers:false,
						//multiselect : true,
						colModel : [{
				            colHeader: i18n.lms9541v01['brno'],//分行別
				            name: 'brno',
				            width: 100,
				            align: "center",
				            sortable: true
				        },{
				            colHeader: i18n.lms9541v01['areaNo'],//地區別
				            name: 'areaNo',
				            width: 100,
				            align: "center",
				            sortable: true
				        },{
				            colHeader: i18n.lms9541v01['CName'],//貸款戶名稱
				            name: 'cName',
				            width: 100,
				            align: "center",
				            sortable: true
				        },{
				            colHeader: i18n.lms9541v01['custId'],//貸款戶統一編號
				            name: 'custId',
				            width: 150,
				            align: "center",
				            sortable: true
				        },{
				            colHeader: i18n.lms9541v01['appDate'],//額度申請日
				            name: 'appDate',
				            width: 100,
				            align: "center",
				            sortable: true,
				            formatter: 'date',
				            formatoptions: {
				            	srcformat: 'Y-m-d H:i:s',
				            	newformat: 'Y-m-d'
				            }
				        },{
				            colHeader: i18n.lms9541v01['appMoney']+i18n.lms9541v01['base'],//受理額度+(萬元)
				            name: 'appMoney',
				            width: 100,
				            align: "center",
				            sortable: true,
				            formatter: 'currency',
				            formatoptions: {
				            	thousandsSeparator: ",",
				            	decimalPlaces: 0
				            }
				        },{
				            colHeader: i18n.lms9541v01['favloan']+i18n.lms9541v01['base'],//優惠額度+(萬元)
				            name: 'favloan',
				            width: 150,
				            align: "center",
				            sortable: true,
				            formatter: 'currency',
				            formatoptions: {
				            	thousandsSeparator: ",",
				            	decimalPlaces: 0
				            }
				        },{
				            colHeader: i18n.lms9541v01['noHouse'],//新屋/中古屋
				            name: 'noHouse',
				            width: 100,
				            align: "center",
				            sortable: true
				        },{
				            colHeader: i18n.lms9541v01['apprDate'],//撥款日
				            name: 'apprDate',
				            width: 100,
				            align: "center",
				            sortable: true
				        }]			
					});
				}
//				else{
//					pageAction.viewGrid.jqGrid("setGridParam", {
//						postData: {
//							type:data.rptType,
//							tooMuch : pageAction.tooMuch,
//							brno: ""
//						},
//						page : 1,
//						search : true
//					}).trigger("reloadGrid");
//				}
				//顯示筆數控制，筆數過多增加By分行查詢
				if(response.totalNum>=pageAction.LIMIT){
					pageAction.viewGrid.jqGrid("setGridParam", {
						postData: {
							type:data.rptType,
							tooMuch : pageAction.tooMuch,
							brno: brno
						},
						page : 1,
						search : true
					}).trigger("reloadGrid");
					pageAction.tooMuch=true;
					//取得分行
					$.ajax({
						handler : pageAction.handler,
						action : 'getItem',
						data : {
							rptType:data.rptType
						},
						success:function(response){
							var brlist = {
								format : "{value} {key}",
								item : response.brno
							};
							$("select#brnoSel").setItems(brlist);
							$("#numControl").show();
							$("select#brnoSel").change(function(){
								pageAction.viewGrid.jqGrid("setGridParam", {
									postData: {
										type:data.rptType,
										tooMuch : pageAction.tooMuch,
										brno: $("select#brnoSel").val()
									},
									page : 1,
									search : true
								}).trigger("reloadGrid");
							});
						}
					});
				}
				else{
					$("#numControl").hide();
					pageAction.tooMuch=false;
					pageAction.viewGrid.jqGrid("setGridParam", {
						postData: {
							type:data.rptType,
							tooMuch : pageAction.tooMuch,
							brno: brno
						},
						page : 1,
						search : true
					}).trigger("reloadGrid");
				}
				
				//開啟畫面
				$("div#viewThickBox").thickbox({
					title : i18n.lms9541v01["viewTitle"],
					width : 1000,
					height : 600,
					modal : true,
					align : 'left',
					valign: 'top',
					i18n: i18n.lms9541v01,
					buttons : {
						'output' : function(){//輸出成Excel
							MegaApi.confirmMessage(i18n.lms9541v01["confirmOutput"], function(action){
								if (action){
									$.thickbox.close();
									$.capFileDownload({          
										handler:"lmsdownloadformhandler",
										data: {
											oid : data.oid,
										    rptOid : data.rptOid,
										    kindNo : data.rptType,
											tooMuch : pageAction.tooMuch,
											brno : pageAction.tooMuch?$("select#brnoSel").val():brno,
											fileDownloadName : data.rptName+".xls",
											serviceName : "lms9541v01xlsservice"
											//其他參數
										}
									});
								}
							});
						},
						'view' : function(){
							$.ajax({
								handler : pageAction.handler,
								action : 'getRptFile',
								data : {
									oid : data.oid
								},
								success:function(response){
									if(response["rptOid"]==null){
										MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.lms9541v01["notYetOuput"]);
									}else{
										$.thickbox.close();
										pageAction.grid.reload();
										$.capFileDownload({
								             handler: "simplefiledwnhandler",
										     data: {
										    	 fileOid: response["rptOid"]
										     }
										 }); 
									}
								}
							});
						},
						'close' : function(){//關閉
							$.thickbox.close();
						}
					}
				});
			}
		});
		
	},
	/**
	 * 取得資料表之選擇列
	 */
	getRowData : function(){
		var row = pageAction.grid.getGridParam('selrow');
		var data;
		if (row) {
			data = pageAction.grid.getRowData(row);
		}else{
			MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["grid.selrow"]);
		}
		return data;
	},
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	}
}

$(function() {
	pageAction.build();
});