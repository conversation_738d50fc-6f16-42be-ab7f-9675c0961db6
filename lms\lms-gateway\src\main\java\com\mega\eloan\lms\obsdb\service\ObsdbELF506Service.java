package com.mega.eloan.lms.obsdb.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 對大陸地區授信業務控管註記
 * </pre>
 * 
 * @since 2013/7/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/7/15,007625,new
 *          </ul>
 */
public interface ObsdbELF506Service {

	Map<String, Object> getByCntrNo(String BRNID, String cntrNo);

    List<Map<String, Object>> getByCustId(String BRNID, String custId, String dupNo);

	List<Map<String, Object>> getByAdcCaseNo(String BRNID, String adcCaseNo);

	int deleteByCntrNo(String BRNID, String cntrNo);

	void insert(String BRNID, String cntrNo, String cnLoanFg,
			String directFg, String stRadeFg, BigDecimal guar1rate,
			BigDecimal guar2rate, BigDecimal guar3rate, BigDecimal coll1rate,
			BigDecimal coll2rate, BigDecimal coll3rate, BigDecimal coll4rate,
			BigDecimal coll5rate, BigDecimal modifyTime, BigDecimal createTime,
			String createUnit, String modifyUnit, String documentNo,
			String iGolFlag, String cnTMUFg, String cnBusKind, String custId,
			String is722Flag, String modUnit, String docNo, BigDecimal modTime,
			BigDecimal sDate, String isBuy, String exItem, String loanTarget,
			String isType, String grntType, String grntClass,
			String othCrdType, String unionArea3, String nCnSblcFg, String nCnInstallMent,
            String prodKind, String adcCaseNo, String exceptFlag, String exceptFlagQAisY, 
			String exceptFlagQAPlus);

	void updateLoanTargetByCntrNo(String BRNID, String loanTarget, String cntrNo);

	public void updateExcpetByCntrNo(String BRNID, String except, String exceptQAIsY,
			String exceptQAPlus, String cntrNo);
	/**
	 * G-104-0333-001 配合萬磅分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權。
	 * 
	 * @param BRNID
	 * @param newCntrNo
	 * @param oldCntrNo
	 */
	void updateCntrNoByCntrNo(String BRNID, String newCntrNo, String oldCntrNo,
			String modifyUnit);

}
