package com.mega.eloan.lms.las.report.impl;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.report.ReportGenerator;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.las.report.LMS1915R02RptService;
import com.mega.eloan.lms.las.service.LMS1915Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * 團貸工作底稿對帳單內容列印
 * 
 * <AUTHOR>
 * 
 */
@Service("lms1915r02rptservice")
public class LMS1915R02RptServiceImpl extends AbstractReportService implements
		LMS1915R02RptService {

	private static final int FIRST_PAGE_COUNT = 5;

	@Resource
	LMS1915Service lms1915Service;

	@Resource
	BranchService branchService;
	
	@Resource
	MisdbBASEService misdbBaseService;

	@Override
	public String getReportTemplateFileName() {
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/las/LMS1915R02_" + locale.toString() + ".rpt";
	}

	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) {
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		setReport001(rptGenerator, mainOid);
	}

	@Override
	public void setReport001(ReportGenerator rptGenerator, String mainOid) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		DecimalFormat df = new DecimalFormat("###,###,###,###,###,###,###,###");

		String loginBranchId = user.getUnitNo();
		// String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = lms1915Service.getL192M01A(mainOid);

		// Set<L192S01A> l192s01as = convert(meta.getL192s01as());

		// 設定ReportBean參數
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		Map<String, String> prompts = new HashMap<String, String>();
		Date balDate = null;
		Set<L192M01B> l192m01bs = meta.getL192m01bs();
		for (L192M01B l192m01b : l192m01bs) {
			if (!"1".equals(l192m01b.getCustType())) {
				continue;
			}

			String mainId = l192m01b.getMainId();
			String mainCustId = l192m01b.getMainCustId();
			String mainDupNo = l192m01b.getMainDupNo();

			// -----------------------------------第一區段資料開始，列印------------------------------
			Map<String, String> values = reNewHashMapParams();
			values.put("ReportBean.column01", l192m01b.getCustName());
			values.put("ReportBean.column40", "section1");

			list.add(values);
			// -----------------------------------第一區段資料結束---------------------------------------------------------

			// -----------------------------------第二區段資料開始，列印------------------------------
			values = reNewHashMapParams();
			values.put("ReportBean.column40", "section2");

			list.add(values);
			// -----------------------------------第二區段資料結束---------------------------------------------------------

			// -----------------------------------第三區段資料開始，列印------------------------------

			Set<L192S01A> l192s01as = convert(meta, l192m01b.getL192s01as());
			int l192s01aMainPageCount = 0; //
			values = reNewHashMapParams();
			values.put("ReportBean.column40", "section3");
			boolean flag = true;
			for (L192S01A l192s01a : l192s01as) {
				l192s01aMainPageCount++;
				if (balDate == null) {
					balDate = l192s01a.getBalDate();
				}
				if (l192s01aMainPageCount <= FIRST_PAGE_COUNT) {
					switch (l192s01aMainPageCount) {
					case 1:
						values.put("ReportBean.column02", l192s01a.getSubject());
						values.put(
								"ReportBean.column03",
								l192s01a.getBalAmt() == null ? "" : df
										.format(l192s01a.getBalAmt()));
						break;
					case 2:
						values.put("ReportBean.column04", l192s01a.getSubject());
						values.put(
								"ReportBean.column05",
								l192s01a.getBalAmt() == null ? "" : df
										.format(l192s01a.getBalAmt()));
						break;
					case 3:
						values.put("ReportBean.column06", l192s01a.getSubject());
						values.put(
								"ReportBean.column07",
								l192s01a.getBalAmt() == null ? "" : df
										.format(l192s01a.getBalAmt()));
						break;
					case 4:
						values.put("ReportBean.column08", l192s01a.getSubject());
						values.put(
								"ReportBean.column09",
								l192s01a.getBalAmt() == null ? "" : df
										.format(l192s01a.getBalAmt()));
						break;
					case 5:
						values.put("ReportBean.column10", l192s01a.getSubject());
						values.put(
								"ReportBean.column11",
								l192s01a.getBalAmt() == null ? "" : df
										.format(l192s01a.getBalAmt()));
						break;

					}

					if (l192s01aMainPageCount == l192s01as.size()) {
						values.put("ReportBean.column12", l192m01b.gettAddr());
						values.put("ReportBean.column01",
								l192m01b.getCustName());
						list.add(values);
					}

					// -----------------------------------第三區段資料結束---------------------------------------------------------

				} else {
					// -----------------------------------第四區段資料開始，列印------------------------------
					if (l192s01aMainPageCount == (FIRST_PAGE_COUNT + 1)
							&& flag == true) {
						values = reNewHashMapParams();
						values.put("ReportBean.column39", "Y");// 判斷是否印到第二頁
						values.put("ReportBean.column40", "section4");
						flag = false;
						list.add(values);

						values = reNewHashMapParams();
					}
					// -----------------------------------第五區段資料開始，列印------------------------------
					values = values == null ? reNewHashMapParams() : values;
					values.put("ReportBean.column39", "Y");// 判斷是否印到第二頁
					values.put("ReportBean.column40", "part1");
					values.put("ReportBean.column13", l192s01a.getSubject());
					values.put(
							"ReportBean.column14",
							l192s01a.getBalAmt() == null ? "" : df
									.format(l192s01a.getBalAmt()));

					list.add(values);
					values = null;
				}

			}

		}

		rptGenerator.setRowsData(list);

		// 設定ireport 參數欄位

		prompts.put("loginBrName", branchService.getBranchName(loginBranchId));
		prompts.put("ownBrid", meta.getOwnBrId());
		prompts.put("custName", meta.getCustName());
		prompts.put("statementAddrFrom", meta.getStatementAddrFrom());
		prompts.put("statementAddrTo", meta.getStatementAddrTo());
		prompts.put("brName", branchService.getBranchName(meta.getOwnBrId()));
		prompts.put("wpNo", meta.getWpNo());
		prompts.put(
				"checkDate",
				meta.getCheckDate() == null ? "" : sdf.format(meta
						.getCheckDate()));
		prompts.put(
				"checkBase",
				meta.getCheckBase() == null ? "" : sdf.format(meta
						.getCheckBase()));

		prompts.put(
				"balDate",
				balDate == null ? (meta.getCheckDate() == null ? "" : sdf
						.format(meta.getCheckDate())) : sdf.format(balDate));
		
		rptGenerator.setVariableData(prompts);
	}

	/**
	 * 初始化map 資料，將所有的rportBean的資料初使化，避免少了而產生exception
	 * 
	 * @return
	 */
	private Map<String, String> reNewHashMapParams() {
		Map<String, String> values = new HashMap<String, String>();
		for (int i = 1; i <= 60; i++) {
			values.put("ReportBean.column" + String.format("%02d", i), "");
		}
		return values;
	}

	/**
	 * 將不寄送函證的拿掉
	 * (餘額資料為0==>J-112-0136:還是要印)
	 * 
	 * @param data
	 * @return
	 */
	private Set<L192S01A> convert(L192M01A meta, Set<L192S01A> data) {
		Set<L192S01A> returnData = new HashSet<L192S01A>();
		BigDecimal zero = BigDecimal.ZERO;
		for (L192S01A l192s01a : data) {
			//確認LNF020寄送函證FLAG若為9:不寄送函證，則不納入列印。
			boolean notice = true;
			if ("13".contains(branchService.getBranch(meta.getOwnBrId()).getBrNoFlag())){
				List<Map<String, Object>> noticetypes = misdbBaseService.findLNF020_NOTICE_TYPE(l192s01a.getQuotaNo());
				for (Map<String, Object> map : noticetypes){
					if ("9".equals(map.get("LNF020_NOTICE_TYPE"))){
						notice = false;
					}
				}
			}
			if (notice){
				returnData.add(l192s01a);
			} else {
//				returnMessage.append("額度序號"+l192s01a.getQuotaNo()+"已設定不寄送函證，不納入對帳單列印資料。<br>");
			}
		}
		return returnData;
	}
}
