/* 
 * LMS9131GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */package com.mega.eloan.lms.fms.handler.grid;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.fms.service.LMS9131Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書審核層級整批修改
 * </pre>
 * 
 * @since 2013/01/16
 * <AUTHOR> Chang
 * @version <ul>
 *          <li>2013/01/16,Gary Chang,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9131gridhandler")
public class LMS9131GridHandler extends AbstractGridHandler {

	@Resource
	DocFileService docFileService;
	@Resource
	LMS9131Service lms9131Service;

	/**
	 * 簽報書grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult querylms9131v00sel(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String branchId = params.getString("branchId", user.getUnitNo());
		if (!Util.equals(user.getUnitNo(), UtilConstants.BankNo.授管處)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"l120a01a.authUnit", user.getUnitNo());
		} else {
			
			if (!Util.equals(branchId, "999")) {
				if (!Util.equals(branchId, "")) {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							UtilConstants.Field.目前編製行, branchId);
				}
			}
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "reEstFlag",
		"A");
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, null);
		
		if (!Util.equals(user.getUnitType(), "S")  && !Util.equals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}
		Page<? extends GenericBean> page = lms9131Service.findPage(
				L120M01A.class, pageSetting);
		
		List<L120M01A> l120m01alist = (List<L120M01A>) page.getContent();
		for (L120M01A l120m01a : l120m01alist) {
			l120m01a.setDocStatus(this.getMessage("docStatus."
					+ l120m01a.getDocStatus()));
		}
		return new CapGridResult(l120m01alist, page.getTotalRow());
	}
}
