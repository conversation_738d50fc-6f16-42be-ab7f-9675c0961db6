package tw.com.jcs.auth.model.impl;

import java.util.Date;

import tw.com.jcs.auth.model.Branch;

/**
 * <pre>
 * 分行相關資訊
 * </pre>
 * 
 * @since 2022年12月22日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月22日
 *          </ul>
 */
public class BranchImpl implements Branch {

    private static final long serialVersionUID = 557153479230523814L;

    /** 分行代碼 **/
    private String brNo;

    /**
     * 分行名稱
     * <p/>
     * 21中文字
     */
    private String brName;

    /** 更新人員 **/
    private String updater;

    /** 更新日期 **/
    private Date updTime;

    /** 分行檢查碼 **/
    private String chkNo;

    /** 分行住址 **/
    private String addr;

    /** 分行電話 **/
    private String tel;

    /**
     * 分行等級
     * <p/>
     * （Ｙ：簡易分行）
     */
    private String brClass;

    /** 分行英文名稱 **/
    private String engName;

    /** 該分行所在地之本位幣 **/
    private String useSWFT;

    /** 分行名稱簡稱 **/
    private String nameABBR;

    /** １國內２國外３總處４子銀行 **/
    private String brNoFlag;

    /** １北區２中區３南區４其他 **/
    private String brNoArea;

    /** 經理 **/
    private String accManager;

    /**
     * 單位類別 (從Notes引進)
     * <p/>
     * B：一般分行<br/>
     * J：簡易型分行<br/>
     * A：區域授信中心<br/>
     * S：企金部<br/>
     * G：稽核處 (總行G)<br/>
     * H：徵信中心 (總行C)<br/>
     * O：海外分行<br/>
     * M：大陸分行 (目前Notes無)
     */
    private String unitType;

    /** 所屬區域授信中心 **/
    private String brnGroup;

    /** 所屬區域授信中心名稱 **/
    private String brnGrpName;

    /** 國別 **/
    private String countryType;

    /**
     * 時區
     * <p/>
     * 格式：GMT Sign Hours : Minutes<br/>
     * 例：GMT-08:00
     */
    private String timeZone;

    /** 主機編碼方式 **/
    private String hostCodePage;

    /**
     * 周一至週五營業開始時間
     * <p/>
     * 差勤系統是否提供？
     */
    private Date bizStartTime;

    /**
     * 周一至週五營業結束時間
     * <p/>
     * 差勤系統是否提供？
     */
    private Date bizEndTime;

    /**
     * 週六是否營業
     * <p/>
     * 差勤系統是否提供？
     */
    private String isSatOpen;

    /**
     * 週六營業開始時間
     * <p/>
     * 差勤系統是否提供？
     */
    private Date satStartTime;

    /**
     * 週六營業結束時間
     * <p/>
     * 差勤系統是否提供？
     */
    private Date satEndTime;

    /** 所屬母行 **/
    private String parentBrNo;

    private String defUnitType;

    /** 取得分行代碼 **/
    public String getBrNo() {
        return this.brNo;
    }

    /** 設定分行代碼 **/
    public void setBrNo(String value) {
        this.brNo = value;
    }

    /**
     * 取得分行名稱
     * <p/>
     * 21中文字
     */
    public String getBrName() {
        return this.brName;
    }

    /**
     * 設定分行名稱
     * <p/>
     * 21中文字
     **/
    public void setBrName(String value) {
        this.brName = value;
    }

    /** 取得更新人員 **/
    public String getUpdater() {
        return this.updater;
    }

    /** 設定更新人員 **/
    public void setUpdater(String value) {
        this.updater = value;
    }

    /** 取得更新日期 **/
    public Date getUpdTime() {
        return this.updTime;
    }

    /** 設定更新日期 **/
    public void setUpdTime(Date value) {
        this.updTime = value;
    }

    /** 取得分行檢查碼 **/
    public String getChkNo() {
        return this.chkNo;
    }

    /** 設定分行檢查碼 **/
    public void setChkNo(String value) {
        this.chkNo = value;
    }

    /** 取得分行住址 **/
    public String getAddr() {
        return this.addr;
    }

    /** 設定分行住址 **/
    public void setAddr(String value) {
        this.addr = value;
    }

    /** 取得分行電話 **/
    public String getTel() {
        return this.tel;
    }

    /** 設定分行電話 **/
    public void setTel(String value) {
        this.tel = value;
    }

    /**
     * 取得分行等級
     * <p/>
     * （Ｙ：簡易分行）
     */
    public String getBrClass() {
        return this.brClass;
    }

    /**
     * 設定分行等級
     * <p/>
     * （Ｙ：簡易分行）
     **/
    public void setBrClass(String value) {
        this.brClass = value;
    }

    /** 取得分行英文名稱 **/
    public String getEngName() {
        return this.engName;
    }

    /** 設定分行英文名稱 **/
    public void setEngName(String value) {
        this.engName = value;
    }

    /** 取得該分行所在地之本位幣 **/
    public String getUseSWFT() {
        return this.useSWFT;
    }

    /** 設定該分行所在地之本位幣 **/
    public void setUseSWFT(String value) {
        this.useSWFT = value;
    }

    /** 取得分行名稱簡稱 **/
    public String getNameABBR() {
        return this.nameABBR;
    }

    /** 設定分行名稱簡稱 **/
    public void setNameABBR(String value) {
        this.nameABBR = value;
    }

    /** 取得１國內２國外３總處４子銀行 **/
    public String getBrNoFlag() {
        return this.brNoFlag;
    }

    /** 設定１國內２國外３總處４子銀行 **/
    public void setBrNoFlag(String value) {
        this.brNoFlag = value;
    }

    /** 取得１北區２中區３南區４其他 **/
    public String getBrNoArea() {
        return this.brNoArea;
    }

    /** 設定１北區２中區３南區４其他 **/
    public void setBrNoArea(String value) {
        this.brNoArea = value;
    }

    /** 取得經理 **/
    public String getAccManager() {
        return this.accManager;
    }

    /** 設定經理 **/
    public void setAccManager(String value) {
        this.accManager = value;
    }

    /**
     * 取得單位類別 (從Notes引進)
     * <p/>
     * B：一般分行<br/>
     * J：簡易型分行<br/>
     * A：區域授信中心<br/>
     * S：企金部<br/>
     * G：稽核處 (總行G)<br/>
     * H：徵信中心 (總行C)<br/>
     * O：海外分行<br/>
     * M：大陸分行 (目前Notes無)
     */
    public String getUnitType() {
        return this.unitType;
    }

    /**
     * 設定單位類別 (從Notes引進)
     * <p/>
     * B：一般分行<br/>
     * J：簡易型分行<br/>
     * A：區域授信中心<br/>
     * S：企金部<br/>
     * G：稽核處 (總行G)<br/>
     * H：徵信中心 (總行C)<br/>
     * O：海外分行<br/>
     * M：大陸分行 (目前Notes無)
     **/
    public void setUnitType(String value) {
        this.unitType = value;
    }

    /** 取得所屬區域授信中心 **/
    public String getBrnGroup() {
        return this.brnGroup;
    }

    /** 設定所屬區域授信中心 **/
    public void setBrnGroup(String value) {
        this.brnGroup = value;
    }

    /** 取得所屬區域授信中心名稱 **/
    public String getBrnGrpName() {
        return this.brnGrpName;
    }

    /** 設定所屬區域授信中心名稱 **/
    public void setBrnGrpName(String value) {
        this.brnGrpName = value;
    }

    /** 取得國別 **/
    public String getCountryType() {
        return this.countryType;
    }

    /** 設定國別 **/
    public void setCountryType(String value) {
        this.countryType = value;
    }

    /**
     * 取得時區
     * <p/>
     * 格式：GMT Sign Hours : Minutes<br/>
     * 例：GMT-08:00
     */
    public String getTimeZone() {
        return this.timeZone;
    }

    /**
     * 設定時區
     * <p/>
     * 格式：GMT Sign Hours : Minutes<br/>
     * 例：GMT-08:00
     **/
    public void setTimeZone(String value) {
        this.timeZone = value;
    }

    /** 取得主機編碼方式 **/
    public String getHostCodePage() {
        return this.hostCodePage;
    }

    /** 設定主機編碼方式 **/
    public void setHostCodePage(String value) {
        this.hostCodePage = value;
    }

    /**
     * 取得周一至週五營業開始時間
     * <p/>
     * 差勤系統是否提供？
     */
    public Date getBizStartTime() {
        return this.bizStartTime;
    }

    /**
     * 設定周一至週五營業開始時間
     * <p/>
     * 差勤系統是否提供？
     **/
    public void setBizStartTime(Date value) {
        this.bizStartTime = value;
    }

    /**
     * 取得周一至週五營業結束時間
     * <p/>
     * 差勤系統是否提供？
     */
    public Date getBizEndTime() {
        return this.bizEndTime;
    }

    /**
     * 設定周一至週五營業結束時間
     * <p/>
     * 差勤系統是否提供？
     **/
    public void setBizEndTime(Date value) {
        this.bizEndTime = value;
    }

    /**
     * 取得週六是否營業
     * <p/>
     * 差勤系統是否提供？
     */
    public String getIsSatOpen() {
        return this.isSatOpen;
    }

    /**
     * 設定週六是否營業
     * <p/>
     * 差勤系統是否提供？
     **/
    public void setIsSatOpen(String value) {
        this.isSatOpen = value;
    }

    /**
     * 取得週六營業開始時間
     * <p/>
     * 差勤系統是否提供？
     */
    public Date getSatStartTime() {
        return this.satStartTime;
    }

    /**
     * 設定週六營業開始時間
     * <p/>
     * 差勤系統是否提供？
     **/
    public void setSatStartTime(Date value) {
        this.satStartTime = value;
    }

    /**
     * 取得週六營業結束時間
     * <p/>
     * 差勤系統是否提供？
     */
    public Date getSatEndTime() {
        return this.satEndTime;
    }

    /**
     * 設定週六營業結束時間
     * <p/>
     * 差勤系統是否提供？
     **/
    public void setSatEndTime(Date value) {
        this.satEndTime = value;
    }

    /** 取得所屬母行 **/
    public String getParentBrNo() {
        return this.parentBrNo;
    }

    /** 設定所屬母行 **/
    public void setParentBrNo(String value) {
        this.parentBrNo = value;
    }

    /**
     * get the defUnitType
     * 
     * @return the defUnitType
     */
    public String getDefUnitType() {
        return defUnitType;
    }

    /**
     * set the defUnitType
     * 
     * @param defUnitType
     *            the defUnitType to set
     */
    public void setDefUnitType(String defUnitType) {
        this.defUnitType = defUnitType;
    }

}
