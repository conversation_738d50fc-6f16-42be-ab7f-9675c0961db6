/* 
 * LMS9525V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;

/**
 * <pre>
 * 管理報表 歷史資料
 * </pre>
 * 
 * @since 2012/2/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/15,jessica,new
 *          </ul>
 */
@Controller
@RequestMapping(path = "rpt/lms9525v01")
public class LMS9525V01Page extends AbstractEloanInnerView {

	public LMS9525V01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 外Grid上的Button

		addToButtonPanel(model, LmsButtonEnum.Print,LmsButtonEnum.Filter);
		
		renderJsI18N(LMS9525V01Page.class);
		model.addAttribute("loadScript", "loadScript('pagejs/rpt/LMS9525V01Page');");
	}// ;
}
