<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
                <!-- J-112-0357 新增敘做條件異動比較表 -->
                <tr class="lmss08jk_button">
	                <th class="hd2">
	                    <th:block th:text="#{'L120S08.titleH'}">敘做條件異動比較表</th:block>
	                </th>
	                <td class="left2">
	                    <button id="lmss08m_generate" type="button">
	                        <span class="text-only"><span><th:block th:text="#{'button.create'}">產生</th:block></span></span>
	                    </button>
	                </td>
	                <td>
	                    <form id="formfile">
	                    	<div class="funcContainer">
								<button type="button" id="lmss08m_uploadFile">
									<span class="text-only"><th:block th:text="#{'button.upload'}"></th:block></span>
								</button>
		                        <button type="button" id="lmss08m_deleteFile">
		                            <span class="text-only"><th:block th:text="#{'button.delete'}"></th:block></span>
		                        </button>
								 
								<div id="lmss08m_gridFile"></div>
		                    </div>
						</form>
	                </td>
	            </tr>
        </th:block>
    </body>
</html>
