/* 
 * L140M01IDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L140M01IDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01I;

/** 連保人資料檔 **/
@Repository
public class L140M01IDaoImpl extends LMSJpaDao<L140M01I, String> implements
		L140M01IDao {

	@Override
	public L140M01I findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	// @Override
	// public List<L140M01I> findByMainId(String mainId) {
	// ISearch search = createSearchTemplete();
	// search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
	// //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	// //舊FUNCTION 預設為#，可改CALL findByMainIdWithRType
	// search.addSearchModeParameters(SearchMode.EQUALS, "rType",
	// UtilConstants.lngeFlag.連帶保證人);
	// search.addOrderBy("createTime", false);
	// List<L140M01I> list = createQuery(L140M01I.class, search)
	// .getResultList();
	// return list;
	// }

	// @Override
	// public L140M01I findByUniqueKey(String mainId, String type, String rId,
	// String rDupNo) {
	// ISearch search = createSearchTemplete();
	// search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
	// search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
	// search.addSearchModeParameters(SearchMode.EQUALS, "rId", rId);
	// search.addSearchModeParameters(SearchMode.EQUALS, "rDupNo", rDupNo);
	// //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	// //舊FUNCTION 預設為#，可改CALL findByUniqueKeyWithRType
	// search.addSearchModeParameters(SearchMode.EQUALS, "rType",
	// UtilConstants.lngeFlag.連帶保證人);
	// return findUniqueOrNone(search);
	// }

	// @Override
	// public List<L140M01I> findByIndex01(String mainId, String type, String
	// rId,
	// String rDupNo) {
	// ISearch search = createSearchTemplete();
	// List<L140M01I> list = null;
	// if (mainId != null)
	// search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
	// if (type != null)
	// search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
	// if (rId != null)
	// search.addSearchModeParameters(SearchMode.EQUALS, "rId", rId);
	// if (rDupNo != null)
	// search.addSearchModeParameters(SearchMode.EQUALS, "rDupNo", rDupNo);
	//
	// //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	// //舊FUNCTION 預設為#，可改CALL findByIndex01WithRType
	// search.addSearchModeParameters(SearchMode.EQUALS, "rType",
	// UtilConstants.lngeFlag.連帶保證人);
	// // 檢查是否有查詢參數
	// List<SearchModeParameter> searchList = search.getSearchModeParameters();
	// if (searchList.size() != 0) {
	// list = createQuery(L140M01I.class, search).getResultList();
	// }
	// return list;
	// }

	// @Override
	// public List<L140M01I> findL140m01iListByL120m01cMainId(String caseMainId,
	// String itemType) {
	// ISearch search = createSearchTemplete();
	// search.addSearchModeParameters(SearchMode.EQUALS, "l140m01a.mainId",
	// caseMainId);
	// //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	// //舊FUNCTION 預設為#，可改CALL findL140m01iListByL120m01cMainIdWithRType
	// search.addSearchModeParameters(SearchMode.EQUALS, "rType",
	// UtilConstants.lngeFlag.連帶保證人);
	// search.addOrderBy("rId");
	// return find(search);
	// }

	@Override
	public List<L140M01I> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		return find(search);
	}

	// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	@Override
	public List<L140M01I> findByMainIdWithRType(String mainId, String rType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);
		search.addOrderBy("createTime", false);
		List<L140M01I> list = createQuery(L140M01I.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L140M01I findByUniqueKeyWithRType(String mainId, String type,
			String rId, String rDupNo, String rType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		search.addSearchModeParameters(SearchMode.EQUALS, "rId", rId);
		search.addSearchModeParameters(SearchMode.EQUALS, "rDupNo", rDupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M01I> findByIndex01WithRType(String mainId, String type,
			String rId, String rDupNo, String rType) {
		ISearch search = createSearchTemplete();
		List<L140M01I> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (rId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rId", rId);
		if (rDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rDupNo", rDupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L140M01I.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140M01I> findL140m01iListByL120m01cMainIdWithRType(
			String caseMainId, String itemType, String rType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "l140m01a.mainId",
				caseMainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);
		search.addOrderBy("rId");
		return find(search);
	}

	@Override
	public List<L140M01I> findByMainIdWithAllRType(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("createTime", false);
		List<L140M01I> list = createQuery(L140M01I.class, search)
				.getResultList();
		return list;
	}

}