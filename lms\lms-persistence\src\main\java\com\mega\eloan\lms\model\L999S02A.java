/* 
 * L999S02A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 連帶保證書檔 **/
@Entity
// @EntityListeners({DocumentModifyListener.class})
@Table(name = "L999S02A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L999S02A extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 保證期間(起)_年
	 * <p/>
	 * 101/03/09調整
	 */
	@Column(name = "GUASDATEY", length = 3, columnDefinition = "VARCHAR(3)")
	private String guaSDateY;

	/**
	 * 保證期間(起)_月
	 * <p/>
	 * 101/03/09調整
	 */
	@Column(name = "GUASDATEM", length = 2, columnDefinition = "VARCHAR(2)")
	private String guaSDateM;

	/**
	 * 保證期間(起)_日
	 * <p/>
	 * 101/03/09調整
	 */
	@Column(name = "GUASDATED", length = 2, columnDefinition = "VARCHAR(2)")
	private String guaSDateD;

	/**
	 * 保證期間(迄)_年
	 * <p/>
	 * 101/03/09調整
	 */
	@Column(name = "GUAEDATEY", length = 3, columnDefinition = "VARCHAR(3)")
	private String guaEDateY;

	/**
	 * 保證期間(迄)_月
	 * <p/>
	 * 101/03/09調整
	 */
	@Column(name = "GUAEDATEM", length = 2, columnDefinition = "VARCHAR(2)")
	private String guaEDateM;

	/**
	 * 保證期間(迄) _日
	 * <p/>
	 * 101/03/09調整
	 */
	@Column(name = "GUAEDATED", length = 2, columnDefinition = "VARCHAR(2)")
	private String guaEDateD;

	/** 保證金額 **/
	@Column(name = "GUAAMT", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal guaAmt;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得保證期間(起)_年
	 * <p/>
	 * 101/03/09調整
	 */
	public String getGuaSDateY() {
		return this.guaSDateY;
	}

	/**
	 * 設定保證期間(起)_年
	 * <p/>
	 * 101/03/09調整
	 **/
	public void setGuaSDateY(String value) {
		this.guaSDateY = value;
	}

	/**
	 * 取得保證期間(起)_月
	 * <p/>
	 * 101/03/09調整
	 */
	public String getGuaSDateM() {
		return this.guaSDateM;
	}

	/**
	 * 設定保證期間(起)_月
	 * <p/>
	 * 101/03/09調整
	 **/
	public void setGuaSDateM(String value) {
		this.guaSDateM = value;
	}

	/**
	 * 取得保證期間(起)_日
	 * <p/>
	 * 101/03/09調整
	 */
	public String getGuaSDateD() {
		return this.guaSDateD;
	}

	/**
	 * 設定保證期間(起)_日
	 * <p/>
	 * 101/03/09調整
	 **/
	public void setGuaSDateD(String value) {
		this.guaSDateD = value;
	}

	/**
	 * 取得保證期間(迄)_年
	 * <p/>
	 * 101/03/09調整
	 */
	public String getGuaEDateY() {
		return this.guaEDateY;
	}

	/**
	 * 設定保證期間(迄)_年
	 * <p/>
	 * 101/03/09調整
	 **/
	public void setGuaEDateY(String value) {
		this.guaEDateY = value;
	}

	/**
	 * 取得保證期間(迄)_月
	 * <p/>
	 * 101/03/09調整
	 */
	public String getGuaEDateM() {
		return this.guaEDateM;
	}

	/**
	 * 設定保證期間(迄)_月
	 * <p/>
	 * 101/03/09調整
	 **/
	public void setGuaEDateM(String value) {
		this.guaEDateM = value;
	}

	/**
	 * 取得保證期間(迄) _日
	 * <p/>
	 * 101/03/09調整
	 */
	public String getGuaEDateD() {
		return this.guaEDateD;
	}

	/**
	 * 設定保證期間(迄) _日
	 * <p/>
	 * 101/03/09調整
	 **/
	public void setGuaEDateD(String value) {
		this.guaEDateD = value;
	}

	/** 取得保證金額 **/
	public BigDecimal getGuaAmt() {
		return this.guaAmt;
	}

	/** 設定保證金額 **/
	public void setGuaAmt(BigDecimal value) {
		this.guaAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
