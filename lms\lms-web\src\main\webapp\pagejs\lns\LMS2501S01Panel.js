
initDfd.done(function(json){

    $("#selectCaseTableBt").click(function(){
    
        //  選擇 1 自行簽報之額度明細表  
        //      2 聯貸案簽報行傳來之額度明細表
        openCntrNoDocUI().done(function(theData){
            // 輸入統編畫面
            inputCustIdUI(theData).done(function(theData){
                var theCheck = theData.data.check == "1";
                var caller = theData.data.check == "1" ? initFirstGrid : initSecondGrid;
                
                caller(theData.data.custId, "0").done(function(theData){
                
                    var selectMainId = theData.selectMainId
                    //l250m01a.message.12 = 是否引入所有額度序號資料
                    isSelectAll(i18n.lms2501m01["l250m01a.message.12"], theCheck).done(function(){
                        $.ajax({
                            handler: 'lms2501m01formhandler',
                            data: {
                                formAction: "reNewMetaDoc",
                                caseMainId: selectMainId,
                                cntrNoCustId: theData.custId,
                                cntrNoDupNo: theData.dupNo,
                                cntrNoCustName: theData.custName,
                                cntrNoAll: true
                            },
                            success: function(obj){
                                CommonAPI.triggerOpener("gridview", "reloadGrid");
                                responseJSON.oid = obj.oid;
                                responseJSON.mainOid = obj.mainOid;
                                responseJSON.mainId = obj.mainId;
								responseJSON.mainDocStatus = obj.mainDocStatus;
                                $("body").injectData(obj);
								setRequiredSave(false);
                                
                            }
                        });
                    }).fail(function(){
                        cntrNoDistinctCustIdGrid(selectMainId).done(function(theData){
                        
                            $.ajax({
                                handler: 'lms2501m01formhandler',
                                data: {
                                    formAction: "reNewMetaDoc",
                                    caseMainId: selectMainId,
                                    cntrNoCustId: theData.custId,
                                    cntrNoDupNo: theData.dupNo,
                                    cntrNoCustName: theData.custName,
                                    cntrNos: theData.cntrNos
                                },
                                success: function(obj){
                                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                                    responseJSON.oid = obj.oid;
                                    responseJSON.mainOid = obj.mainOid;
                                    responseJSON.mainId = obj.mainId;
									responseJSON.mainDocStatus = obj.mainDocStatus;
                                    $("body").injectData(obj);
									setRequiredSave(false);
                                    
                                }
                            });
                        });
                    });
                })
            });
        });
    });
    
    var openCntrNoDocUI = function(){
        var deferred = $.Deferred();
        $("#dialg_openCntrDoc").thickbox({
            title: i18n.lms2501m01["l250m01a.message.09"],// 選擇額度明細表
            width: 400,
            height: 100,
            modal: true,
            valign: "bottom",
            i18n: i18n.def,
            align: "center",
            buttons: {
                "sure": function(){
                    var checked = $("[name=caseTable]:checked").val();
                    
                    if (!checked) {
                        CommonAPI.showMessage(i18n.lms2501m01['l250m01a.message.04']);
                    }
                    else {
                        $.thickbox.close();
                        deferred.resolve({
                            data: {
                                check: checked
                            }
                        });
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
        return deferred.promise();
    }
    
    var inputCustIdUI = function(jsonData){
        var deferred = $.Deferred();
        var $form = $("#filterForm");
        $form.reset();
        $("#filterBox").thickbox({
            title: i18n.lms2501m01['l250m01a.message.10'],// "請輸入主要借款人統一編號",
            width: 300,
            height: 190,
            modal: true,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($form.valid()) {
                        $.thickbox.close();
                        var custId = $form.find("#filter_custId").val();
                        $.extend(true, jsonData, {
                            data: {
                                custId: custId
                            }
                        })
                        deferred.resolve(jsonData);
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
        return deferred.promise();
    }
    
    
    // 自行簽報之額度明細表簽報書選擇Grid
    var initFirstGrid = function(custId, dupNo){
		$("#openthetable1").empty().append("<div id='openthegrid'></div>");
        var initdfd = $.Deferred();
        var deferred = $.Deferred();
        //$("#openthegrid").jqGrid("GridUnload");
        var $openthegrid = $("#openthegrid").iGrid({
            handler: inits.ghaddle,
            height: "250px",
            width: "800",
            sortname: 'endDate|custId',
            sortorder: "desc|desc",
            rowNum: 10,
            autowidth: true,
            postData: {
                formAction: "queryL120m01a",
                custId: custId,
                dupNo: dupNo
            },
            loadComplete: function(){
                initdfd.resolve();
            },
            colModel: [{
                colHeader: i18n.lms2501m01['l250m01a.grid.001'],// 主要借款人
                name: 'custId',
                width: 150,
                sortable: true,
                align: "left"
            }, {
                colHeader: i18n.lms2501m01['l250m01a.grid.002'],// 案號
                name: 'caseNo',
                width: 120,
                sortable: true,
                align: "left"
            }, {
                colHeader: i18n.lms2501m01['l250m01a.grid.003'],// 簽案日期
                name: 'caseDate',
                width: 45,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.lms2501m01['l250m01a.grid.004'],// 核准日期
                name: 'endDate',
                width: 45,
                sortable: true,
                align: "center",
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d',
                    newformat: 'Y-m-d'
                }
            }, {
                colHeader: i18n.lms2501m01['l250m01a.grid.005'],// 經辦
                name: 'updater',
                width: 60,
                sortable: true,
                align: "updater"
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }, {
                name: 'authLvl',
                hidden: true
            }, {
                name: 'docKind',
                hidden: true
            }]
        });
        
        //$("#openthegrid").jqGrid('setGridWidth', 750);
        
        
        initdfd.done(function(){
            $("#openthetable1").thickbox({
                title: i18n.lms2501m01["l250m01a.message.14"],
                width: 800,
                height: 410,
                valign: "bottom",
                align: "center",
                modal: true,
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                    
                        var id = $openthegrid.getGridParam('selrow');
                        
                        if (!id) {
                            // action_004=請先選擇需「調閱」之資料列
                            return CommonAPI.showMessage(i18n.def["action_004"]);
                        }
                        $.thickbox.close();
                        var selectMainId = $openthegrid.getRowData(id).mainId;
                        deferred.resolve({
                            selectMainId: selectMainId
                        });
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        });
        
        
        return deferred.promise();
    }
    
    
    var initSecondGrid = function(custId, dupNo){
		$("#openthetable2").empty().append("<div id='openthegrid2'></div>");
        var initdfd = $.Deferred();
        var deferred = $.Deferred();
        //$("#openthegrid2").jqGrid("GridUnload");
        var $openthegrid2 = $("#openthegrid2").iGrid({
            handler: inits.ghaddle,
            height: "230px",
            width: "100%",
            sortname: 'caseBrId',
            postData: {
                formAction: "queryL141m01a",
                custId: custId,
                dupNo: dupNo
            },
            autowidth: true,
            loadComplete: function(){
            
                initdfd.resolve();
            },
            colModel: [{
                colHeader: i18n.lms2501m01['l250m01a.grid.006'],// 聯行
                name: 'caseBrId',
                width: 100,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.lms2501m01['l250m01a.grid.001'],// 主要借款人
                name: 'custName',
                width: 150,
                sortable: true,
                align: "left"
            }, {
                colHeader: i18n.lms2501m01['l250m01a.grid.002'],// 案號
                name: 'caseNo',
                width: 120,
                sortable: true,
                align: "left"
            }, {
                colHeader: i18n.lms2501m01['l250m01a.grid.003'],// 簽案日期
                name: 'caseDate',
                width: 90,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.lms2501m01['l250m01a.grid.005'],// 經辦
                name: 'coAppraiser',
                width: 90,
                sortable: true,
                align: "center"
            }, {
                name: 'srcMainId',
                hidden: true
            }],
            ondblClickRow: function(rowid){
            
            }
        });
        //$("#openthegrid2").jqGrid('setGridWidth', 750);
        
        initdfd.done(function(){
            $("#openthetable2").thickbox({
                // L160M01A.contentCntrCase=聯貸案簽報行傳來之額度明細表
                title: i18n.lms2501m01["l250m01a.message.11"],
                modal: true,
                width: 800,
                height: 380,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        var id = $openthegrid2.getGridParam('selrow');
                        
                        if (!id) {
                            // action_004=請先選擇需「調閱」之資料列
                            return CommonAPI.showMessage(i18n.def["action_004"]);
                        }
                        $.thickbox.close();
                        var selectMainId = $openthegrid2.getRowData(id).srcMainId;
                        deferred.resolve({
                            selectMainId: selectMainId
                        });
                        
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
            
        });
        return deferred.promise();
    }
    
    var isSelectAll = function(message, check){
        var deferred = $.Deferred();
        //是否引入所有額度序號資料
        if (check == "1") {
            API.confirmMessage(message, function(result){
                if (result) {
                    deferred.resolve();
                }
                else {
                    deferred.reject();
                }
            })
        }
        else {
            deferred.reject();
        }
        
        return deferred.promise();
    }
    
    /**
     *
     * @param {Object} caseMainId
     */
    var cntrNoDistinctCustIdGrid = function(caseMainId){
		$("#opentheCntroNo").empty().append("<div id='opentheCntroNoGrid'></div>");
        var initdfd = $.Deferred();
        var deferred = $.Deferred();
        //$("#opentheCntroNoGrid").jqGrid("GridUnload");
        var $grid = $("#opentheCntroNoGrid").iGrid({
            handler: "lms1601gridhandler",
            height: "230px",
            width: "100%",
            postData: {
                formAction: "queryL140m01a",
                mainId: caseMainId
            },
            autowidth: true,
            multiselect: true,
            loadComplete: function(){
            
                initdfd.resolve();
            },
            colModel: [{
                colHeader: i18n.lms2501m01['l250m01a.grid.007'],// 統一編號
                name: 'custId',
                width: 150,
                sortable: true,
                align: "left"
            }, {
                colHeader: i18n.lms2501m01['l250m01a.grid.008'],// 借款人名稱
                name: 'custName',
                width: 90,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.lms2501m01['l250m01a.grid.009'],// 額度序號
                name: 'useCntrNo',
                width: 90,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.lms2501m01["l250m01a.grid.010"],//"共用額度序號",
                name: 'commSno',
                width: 80,
                sortable: true
            }, {
                colHeader: "&nbsp;",
                name: 'currentApplyCurr',
                width: 30,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.lms2501m01["l250m01a.grid.011"],//現請額度,
                name: 'currentApplyAmt',
                width: 100,
                sortable: true,
                align: "right",
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    removeTrailingZero: true,
                    decimalPlaces: 2//小數點到第幾位
                }
            }, {
                colHeader: i18n.lms2501m01["l250m01a.grid.012"],//攤貸額度,
                name: 'shareAmt',
                width: 100,
                sortable: true,
                align: "right",
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    removeTrailingZero: true,
                    defaultValue: '0',
                    decimalPlaces: 2//小數點到第幾位
                }
            }, {
                colHeader: i18n.lms2501m01['l250m01a.grid.013'],// 分行別
                name: 'useBrId',
                width: 60,
                sortable: true,
                align: "center"
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }],
            ondblClickRow: function(rowid){
            
            }
        });
        //$("#opentheCntroNoGrid").jqGrid('setGridWidth', 750);
        initdfd.done(function(){
            $("#opentheCntroNo").thickbox({
                title: i18n.lms2501m01["l250m01a.message.09"],
                modal: true,
                width: 800,
                height: 380,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                    
                        var selectRows = $grid.getGridParam('selarrrow');
                        if (!selectRows) {
                            // action_004=請先選擇需「調閱」之資料列
                            return CommonAPI.showMessage(i18n.def["action_004"]);
                        }
                        
                        var cntrNos = [];
                        
                        $.each(selectRows, function(i, data){
                            var ret = $grid.getRowData(data);
                            //alert(ret.useCntrNo)
                            cntrNos.push(ret.useCntrNo);
                        })
                        var id = $grid.getGridParam('selrow');
                        
                        if (!id) {
                            // action_004=請先選擇需「調閱」之資料列
                            return CommonAPI.showMessage(i18n.def["action_004"]);
                        }
                        $.thickbox.close();
                        var custId = $grid.getRowData(id).custId;
                        var dupNo = $grid.getRowData(id).dupNo;
                        var custName = $grid.getRowData(id).custName;
                        
                        deferred.resolve({
                            custId: custId,
                            dupNo: dupNo,
                            custName: custName,
                            cntrNos: cntrNos
                        });
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        })
        return deferred.promise();
    }
});
