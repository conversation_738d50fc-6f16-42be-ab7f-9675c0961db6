
package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 同一通訊處註記- 編製中
 * </pre>
 */
@Controller
@RequestMapping("/cls/cls3101v01")
public class CLS3101V01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);

		// 加上Button
		ArrayList<LmsButtonEnum> btns = new ArrayList<LmsButtonEnum>();
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		// 主管跟經辦都會出現的按鈕
		list.add(LmsButtonEnum.View);
		list.add(LmsButtonEnum.Add);
		list.add(LmsButtonEnum.Delete);

		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
			
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			
		}

		addToButtonPanel(model, list);

		renderJsI18N(CLS3101V01Page.class);
		renderJsI18N(CLS3101M01Page.class);

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS3101V01Page');");
	}

}
