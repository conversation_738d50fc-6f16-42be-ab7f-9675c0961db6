/* 
 * L140S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金借保人檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140S01A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "custPos" }))
public class L140S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	/**
	 * join
	 *
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false) })
	private C120S01G c120s01g;

	public void setC120s01g(C120S01G c120s01g) {
		this.c120s01g = c120s01g;
	}

	public C120S01G getC120s01g() {
		return c120s01g;
	}*/

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 客戶統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重複序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 借款人姓名 **/
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 性質 (相關身份)
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)
	 */
	@Size(max = 1)
	@Column(name = "CUSTPOS", length = 1, columnDefinition = "CHAR(1)")
	private String custPos;
	
	/**
	 * 新增來源 UtilConstants.L140S01AType {1:分行新增, 2:授管處新增}
	 */
	@Size(max = 1)
	@Column(name = "TYPE", length = 1, columnDefinition = "CHAR(1)")
	private String type;

	@Transient
	private String typeStr;
	
	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 負担保證責任比率
	 * J-105-0100
	 */
	@Column(name = "GUAPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal guaPercent;
	
	/** Grid 顯示負担保證責任比率 ###.## **/
	@Transient
	private String guaPercentStr;
	
	/**
	 * 關係類別  	select * from com.bcodetype where codetype ='cls_RelClass'  {1:企業關係人(含自然人、法人) , 2:親屬關係  , 3:其他綜合關係 }
	 * <p/>
	 * 引進額度明細表之連保人－與借款人關係，引進可修改<br/>
	 * 關係類別選項：<br/>
	 * 企業關係人(含自然人、法人)：類別細項詳註一<br/>
	 * 親屬關係：類別細項詳註一<br/>
	 * 其他綜合關係：上述兩項之類別細項混合勾選<br/>
	 * 註:<br/>
	 * 只有主債務人統編與從債務人統編相同者,其關係可為空白,其餘欄位皆不可為空白
	 */
	@Size(max = 1)
	@Column(name = "RKINDM", length = 1, columnDefinition = "VARCHAR(1)")
	private String rKindM;

	/** 關係類別細項  select * from com.bcodetype where codetype in ('Relation_type1', 'Relation_type2', 'Relation_type31', 'Relation_type32')  
	 */
	@Size(max = 2)
	@Column(name = "RKINDD", length = 2, columnDefinition = "VARCHAR(2)")
	private String rKindD;
	
	/** Grid 關係類別細項 **/
	@Transient
	private String rKindDStr;
	
	/**
	 * 借保原因  select * from com.bcodetype where codetype = 'cls1161m01_reson' 
	 * <p/>
	 * 保證人相關身份為<br/>
	 * N-一般保證人時須補徵提原因<br/>
	 * 01 借款人薪資收入條件不足。"<br/>
	 * 02 借款人年齡較大致使可工作年限短於借款期限。"<br/>
	 * 03 借款人有信用不良紀錄。"<br/>
	 * 04 借款人所提供之擔保品非屬自己所有。"<br/>
	 * 99 無法歸類於前項各款者。(請說明原因)
	 */
	@Size(max = 2)
	@Column(name = "RESON", length = 2, columnDefinition = "VARCHAR(2)")
	private String reson;
	
	/**
	 * 借保原因其他<br/>
	 * reson=99時需可以輸入
	 */
	@Size(max = 300)
	@Column(name = "RESONOTHER", length = 300, columnDefinition = "VARCHAR(300)")
	private String resonOther;
	 
	/**
	 * J-109-0155 , (109)1295 , 是否與借款人同住<br/>
	 * 關係類別為親屬關係且為非X0-本人時, 需填寫
	 */
	@Size(max = 1)
	@Column(name = "ISLIVEWITHBORROWER", length = 1, columnDefinition = "CHAR(1)")
	private String isLiveWithBorrower;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得客戶統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定客戶統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重複序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重複序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得借款人姓名 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定借款人姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得性質 (相關身份)
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)
	 */
	public String getCustPos() {
		return this.custPos;
	}

	/**
	 * 設定性質 (相關身份)
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)
	 **/
	public void setCustPos(String value) {
		this.custPos = value;
	}
	
	/**
	 * 取得新增來源 UtilConstants.L140S01AType {1:分行新增, 2:授管處新增}
	 */
	public String getType() {
		return type;
	}
	/**
	 * 設定新增來源 UtilConstants.L140S01AType {1:分行新增, 2:授管處新增}
	 */
	public void setType(String type) {
		this.type = type;
	}
	
	public String getTypeStr() {
		return typeStr;
	}

	public void setTypeStr(String typeStr) {
		this.typeStr = typeStr;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 設定負担保證責任比率 **/
	public void setGuaPercent(BigDecimal guaPercent) {
		this.guaPercent = guaPercent;
	}

	/** 取得負担保證責任比率 **/
	public BigDecimal getGuaPercent() {
		return guaPercent;
	}
	/** 設定 Grid 顯示負担保證責任比率 ###.## **/
	public void setGuaPercentStr(String guaPercentStr) {
		this.guaPercentStr = guaPercentStr;
	}
	/** 取得 Grid 顯示負担保證責任比率 ###.## **/
	public String getGuaPercentStr() {
		return guaPercentStr;
	}

	public String getRKindM() {
		return rKindM;
	}

	public void setRKindM(String rKindM) {
		this.rKindM = rKindM;
	}

	public String getRKindD() {
		return rKindD;
	}

	public void setRKindD(String rKindD) {
		this.rKindD = rKindD;
	}

	/** 取得 Grid 關係類別細項 **/
	public String getRKindDStr() {
		return rKindDStr;
	}
	/** 設定 Grid 關係類別細項 **/
	public void setRKindDStr(String rKindDStr) {
		this.rKindDStr = rKindDStr;
	}

	public String getReson() {
		return reson;
	}

	public void setReson(String reson) {
		this.reson = reson;
	}

	public String getResonOther() {
		return resonOther;
	}

	public void setResonOther(String resonOther) {
		this.resonOther = resonOther;
	}

	public String getIsLiveWithBorrower() {
		return isLiveWithBorrower;
	}

	public void setIsLiveWithBorrower(String isLiveWithBorrower) {
		this.isLiveWithBorrower = isLiveWithBorrower;
	}
}
