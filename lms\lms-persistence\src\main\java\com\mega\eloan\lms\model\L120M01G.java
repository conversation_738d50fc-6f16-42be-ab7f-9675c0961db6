/* 
 * L120M01G.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 整批貸款總額度資訊檔［L140M03A.grpCntrNo 與 L120M01G.parentCntrNo］ **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120M01G", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L120M01G extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 總戶統編 **/
	@Size(max = 10)
	@Column(name = "PARENTID", length = 10, columnDefinition = "CHAR(10)")
	private String parentId;

	/** 重複序號 **/
	@Size(max = 1)
	@Column(name = "PARENTDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String parentDupNo;

	/** 團貸總戶名稱(專案名) **/
	@Size(max = 120)
	@Column(name = "PROJECTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String projectName;

	/** 團貸總戶名稱(建案名) **/
	@Size(max = 120)
	@Column(name = "BUILDNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String buildName;

	/** 簽案分行 **/
	@Size(max = 3)
	@Column(name = "ISSUEBRNO", length = 3, columnDefinition = "CHAR(3)")
	private String issueBrNo;

	/**
	 * 總額度申請年度
	 * <p/>
	 * eg.100
	 */
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "PARENTYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer parentYear;

	/**
	 * 總戶序號
	 * <p/>
	 * eg.918110001739
	 */
	@Size(max = 12)
	@Column(name = "PARENTCNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String parentCntrNo;

	/**
	 * 批　　號
	 * <p/>
	 * eg.01
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "PACKNO", columnDefinition = "DECIMAL(2,0)")
	private Integer packNo;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得總戶統編 **/
	public String getParentId() {
		return this.parentId;
	}

	/** 設定總戶統編 **/
	public void setParentId(String value) {
		this.parentId = value;
	}

	/** 取得重複序號 **/
	public String getParentDupNo() {
		return this.parentDupNo;
	}

	/** 設定重複序號 **/
	public void setParentDupNo(String value) {
		this.parentDupNo = value;
	}

	/** 取得團貸總戶名稱(專案名) **/
	public String getProjectName() {
		return this.projectName;
	}

	/** 設定團貸總戶名稱(專案名) **/
	public void setProjectName(String value) {
		this.projectName = value;
	}

	/** 取得團貸總戶名稱(建案名) **/
	public String getBuildName() {
		return this.buildName;
	}

	/** 設定團貸總戶名稱(建案名) **/
	public void setBuildName(String value) {
		this.buildName = value;
	}

	/** 取得簽案分行 **/
	public String getIssueBrNo() {
		return this.issueBrNo;
	}

	/** 設定簽案分行 **/
	public void setIssueBrNo(String value) {
		this.issueBrNo = value;
	}

	/**
	 * 取得總額度申請年度
	 * <p/>
	 * eg.100
	 */
	public Integer getParentYear() {
		return this.parentYear;
	}

	/**
	 * 設定總額度申請年度
	 * <p/>
	 * eg.100
	 **/
	public void setParentYear(Integer value) {
		this.parentYear = value;
	}

	/**
	 * 取得總戶序號
	 * <p/>
	 * eg.918110001739
	 */
	public String getParentCntrNo() {
		return this.parentCntrNo;
	}

	/**
	 * 設定總戶序號
	 * <p/>
	 * eg.918110001739
	 **/
	public void setParentCntrNo(String value) {
		this.parentCntrNo = value;
	}

	/**
	 * 取得批　　號
	 * <p/>
	 * eg.01
	 */
	public Integer getPackNo() {
		return this.packNo;
	}

	/**
	 * 設定批　　號
	 * <p/>
	 * eg.01
	 **/
	public void setPackNo(Integer value) {
		this.packNo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
