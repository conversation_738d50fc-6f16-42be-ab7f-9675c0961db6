/* 
 * LMS1415S02Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.model.L120M01A;

/**
 * <pre>
 * 聯行額度明細表 - 額度明細表明細
 * </pre>
 * 
 * @since 2011/12/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/1,REX,new
 *          </ul>
 */
public class LMS1415S02Panel extends Panel {

	private static final long serialVersionUID = -4024257163623646201L;

	private L120M01A l120m01a;

	public LMS1415S02Panel(String id) {
		super(id);
	}

	public LMS1415S02Panel(String id, boolean updatePanelName, L120M01A l120m01a) {
		super(id, updatePanelName);
		this.l120m01a = l120m01a;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new LMS1405S02Panel01("_LMS140PanelC_2_1").processPanelData(model, params);
		new LMS1405S02Panel02("_LMS140PanelC_2_2", l120m01a).processPanelData(model, params);
		new LMS1405S02Panel03("_LMS140PanelC_2_3").processPanelData(model, params);
		new LMS1405S02Panel04("_LMS140PanelC_2_4").processPanelData(model, params);
		new LMS1405S02Panel05("_LMS140PanelC_2_5", l120m01a).processPanelData(model, params);
		new LMS1405S02Panel06("_LMS140PanelC_2_6").processPanelData(model, params);
		new LMS1405S02Panel07("_LMS140PanelC_2_7").processPanelData(model, params);
		new LMS1405S02Panel08("_LMS140PanelC_2_8").processPanelData(model, params);
		new LMS1405S02Panel09("_LMS140PanelC_2_9").processPanelData(model, params);
		new LMS1405S02Panel10("_LMS140PanelC_2_10").processPanelData(model, params);
	}
}
