package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.RelativeMeta;

/**
 * <pre>
 * C140S07A model.
 * </pre>
 * 
 * @since 2011/10/04
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/04,TimChiang,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140S07A-entity-graph", attributeNodes = { @NamedAttributeNode("c140m07a") })
@Entity
@Table(name = "C140S07A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C140S07A extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;
	
	public C140S07A(){}
	
	public C140S07A(String mainId,String pid){
		super.setMainId(mainId);
		super.setPid(pid);
	}

	/** 序號 */
	@Column(precision = 2)
	private BigDecimal sn;

	/** 是否同業資料 */
	@Column(length = 1)
	private String crossFlag;

	/** 行業別 */
	@Column(length = 1)
	private String tradeType;

	/** 報表年度 */
	@Column(length = 4)
	private String year;

	/** 報表類型 */
	@Column(length = 1)
	private String periodType;

	/** 報表起日 */
	@Temporal(TemporalType.DATE)
	private Date sDate;

	/** 報表迄日 */
	@Temporal(TemporalType.DATE)
	private Date eDate;

	/** 幣別 */
	@Column(length = 3)
	private String curr;

	/** 兌換美金匯率 */
	@Column(precision = 9, scale = 2)
	private BigDecimal exchange;

	/** 單位 */
	@Column(precision = 12)
	private BigDecimal amtUnit;

	/** JSONdata */
	@Column(length = 1200)
	private String jsonData;

	/** (DBU)名稱 */
	@Column(length = 60)
	private String compareCom;

	
	// bi-directional many-to-one association to C140M07A
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false) })
	private C140M07A c140m07a;

	public C140M07A getC140m07a() {
		return this.c140m07a;
	}

	public void setC140m07a(C140M07A c140m07a) {
		this.c140m07a = c140m07a;
	}

	public BigDecimal getSn() {
		return sn;
	}

	public C140S07A setSn(BigDecimal sn) {
		this.sn = sn;
		return this;
	}

	/**
	 * 是否同業資料
	 * 
	 * @return the crossFlag
	 */
	public String getCrossFlag() {
		return crossFlag;
	}

	/**
	 * 是否同業資料
	 * 
	 * @param crossFlag
	 *            the crossFlag to set
	 */
	public C140S07A setCrossFlag(String crossFlag) {
		this.crossFlag = crossFlag;
		return this;
	}

	/**
	 * 行業別
	 * 
	 * @return the tradeType
	 */
	public String getTradeType() {
		return tradeType;
	}

	/**
	 * 行業別
	 * 
	 * @param tradeType
	 *            the tradeType to set
	 */
	public void setTradeType(String tradeType) {
		this.tradeType = tradeType;
	}

	/**
	 * 報表年度
	 * 
	 * @return the year
	 */
	public String getYear() {
		return year;
	}

	/**
	 * 報表年度
	 * 
	 * @param year
	 *            the year to set
	 */
	public C140S07A setYear(String year) {
		this.year = year;
		return this;
	}

	/**
	 * 報表類型
	 * 
	 * @return the periodType
	 */
	public String getPeriodType() {
		return periodType;
	}

	/**
	 * 報表類型
	 * 
	 * @param periodType
	 *            the periodType to set
	 */
	public void setPeriodType(String periodType) {
		this.periodType = periodType;
	}

	/**
	 * 報表起日
	 * 
	 * @return the sDate
	 */
	public Date getSDate() {
		return sDate;
	}

	/**
	 * 報表起日
	 * 
	 * @param sDate
	 *            the sDate to set
	 */
	public C140S07A setSDate(Date sDate) {
		this.sDate = sDate;
		return this;
	}

	/**
	 * 報表迄日
	 * 
	 * @return the eDate
	 */
	public Date getEDate() {
		return eDate;
	}

	/**
	 * 報表迄日
	 * 
	 * @param eDate
	 *            the eDate to set
	 */
	public C140S07A setEDate(Date eDate) {
		this.eDate = eDate;
		return this;
	}

	/**
	 * 幣別
	 * 
	 * @return the curr
	 */
	public String getCurr() {
		return curr;
	}

	/**
	 * 幣別
	 * 
	 * @param curr
	 *            the curr to set
	 */
	public void setCurr(String curr) {
		this.curr = curr;
	}

	/**
	 * 幣別單位
	 * 
	 * @return the amtUnit
	 */
	public BigDecimal getAmtUnit() {
		return amtUnit;
	}

	/**
	 * 幣別單位
	 * 
	 * @param amtUnit
	 *            the amtUnit to set
	 */
	public void setAmtUnit(BigDecimal amtUnit) {
		this.amtUnit = amtUnit;
	}

	/**
	 * 兌換美金匯率
	 * 
	 * @return BigDecimal
	 */
	public BigDecimal getExchange() {
		return exchange;
	}

	/**
	 * 兌換美金匯率
	 * 
	 * @param exchange
	 *            兌換美金匯率
	 */
	public void setExchange(BigDecimal exchange) {
		this.exchange = exchange;
	}

	/**
	 * JSONdata
	 * 
	 * @return the jsonData
	 */
	public String getJsonData() {
		return jsonData;
	}

	/**
	 * JSONdata
	 * 
	 * @param jsonData
	 *            the jsonData to set
	 */
	public void setJsonData(String jsonData) {
		this.jsonData = jsonData;
	}
	
	@Transient
	private String source;

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	/**
	 * (DBU)名稱
	 * 
	 * @return the compareCom
	 */
	public String getCompareCom() {
		return compareCom;
	}

	/**
	 * (DBU)名稱
	 * 
	 * @param compareCom
	 *            the compareCom to set
	 */
	public void setCompareCom(String compareCom) {
		this.compareCom = compareCom;
	}

	
}