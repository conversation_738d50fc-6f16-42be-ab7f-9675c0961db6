package com.mega.eloan.lms.cls.handler.grid;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.model.SysParameter;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.RelatedAccountService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.pages.CLS1131V01Page;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01F;
import com.mega.eloan.lms.model.C122M01G;
import com.mega.eloan.lms.model.C122S01H;
import com.mega.eloan.lms.model.C122S01Y;
import com.mega.eloan.lms.model.C900M01N;
import com.mega.eloan.lms.model.C900M01O;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L161S01D;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.ADDateTimeFormatter;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 進件管理原始資料(C122M01A)
 * </pre>
 * 
 * @since 2015/04/28
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/04/28,EL03738,new
 * 
 *          </ul>
 */
@Scope("request")
@Controller("cls1222gridhandler")
public class CLS1222GridHandler extends AbstractGridHandler {

	@Resource
	BranchService branchService;
	@Resource
	CLS1220Service service;
	@Resource
	UserInfoService userInfoService;
	@Resource
	RelatedAccountService relatedAccountService;
	@Resource
	RetrialService retrialService;
	@Resource
	EloandbBASEService eloandbBASEService;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	CLSService clsService;
	@Resource
	CodeTypeService codeservice;
	@Resource
	SysParameterService sysparamService;
	
	Properties prop;

	public CapGridResult queryView_E(ISearch pageSetting, PageParameters params)
			throws CapException {
		// 建立主要Search 條件
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		if(Util.notEquals(ownBrId, "943")){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		}
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyKind",
		// UtilConstants.C122_ApplyKind.E);
//		pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,"applyKind", "");
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (true) {
			String custId = Util.trim(params.getString("filter_custId"));
			if (Util.isNotEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"custId", custId);
			}

			String ploanCaseId = Util.trim(params.getString("ploanCaseId"));
			if (Util.isNotEmpty(ploanCaseId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"ploanCaseId", ploanCaseId);
			}

			String applyTS_beg = Util.trim(params.getString("applyTS_beg"));
			String applyTS_end = Util.trim(params.getString("applyTS_end"));
			if (Util.isNotEmpty(applyTS_beg) && Util.isNotEmpty(applyTS_end)) {
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"applyTS", new Object[] { Util.parseDate(applyTS_beg),
								Util.parseDate(applyTS_end + " 23:59:59") });
			}
			String SdocStatus = Util.trim(params.getString("SdocStatus"));
			if (Util.isNotEmpty(SdocStatus)) {
				if(Util.equals(SdocStatus, "E")){ //動審作業 >> 於審核中僅顯示E00、E01
					String[] docStatusArray = new String[] { "E00", "E01" };
					pageSetting.addSearchModeParameters(SearchMode.IN,
							"docStatus", docStatusArray);
				}else{
					SdocStatus = SdocStatus + "%";
					pageSetting.addSearchModeParameters(SearchMode.LIKE,
							"docStatus", SdocStatus);
				}
			} else {
				String[] docStatusArray = new String[] { "E00", "E01" };
				pageSetting.addSearchModeParameters(SearchMode.OR,
						new SearchModeParameter(SearchMode.LIKE, "docStatus","A%"),
						new SearchModeParameter(SearchMode.OR,
						new SearchModeParameter(SearchMode.LIKE, "docStatus","B%"),
						new SearchModeParameter(SearchMode.OR,
						new SearchModeParameter(SearchMode.LIKE, "docStatus","C%"),
						new SearchModeParameter(SearchMode.OR,
						new SearchModeParameter(SearchMode.LIKE, "docStatus","D%"),
						new SearchModeParameter(SearchMode.IN, "docStatus",docStatusArray)))));
			}

			String applyKind = Util.trim(params.getString("applyKind"));
			String[] applyKindArray = null;
			if (Util.equals(applyKind, "B")) { // 勞工紓困
				applyKindArray = new String[] { "B"/*, "D"*/ };
			} else if (Util.equals(applyKind, "E")) { // 線上房貸
//				applyKindArray = new String[] { "E", "F" };
				applyKindArray = new String[] { "E" };
			} else if (Util.equals(applyKind, "P")) { // 信貸
//				applyKindArray = new String[] { "P", "Q" };
				applyKindArray = new String[] { "P" };
			} else if (Util.equals(applyKind, "C")) { // 卡友信貸
				applyKindArray = new String[] { "C" };
			} else if (Util.equals(applyKind, "H")) { // 房貸增貸
				applyKindArray = new String[] { "H" };
			} else if (Util.equals(applyKind, UtilConstants.C122_ApplyKind.I )) { // 青創一百萬以下
				applyKindArray = new String[] { UtilConstants.C122_ApplyKind.I };
			} else if (Util.equals(applyKind, UtilConstants.C122_ApplyKind.J )) { // 青創一百萬以上
				applyKindArray = new String[] { UtilConstants.C122_ApplyKind.J };
			} else if (Util.equals(applyKind, UtilConstants.C122_ApplyKind.O )) { // 其他
				applyKindArray = new String[] { UtilConstants.C122_ApplyKind.O };
			}
			if (applyKindArray != null) {
				pageSetting.addSearchModeParameters(SearchMode.IN, "applyKind",
						applyKindArray);
			}else{
				applyKindArray = new String[] { "B", /*"D",*/ "E", "P", "C","H"
						,UtilConstants.C122_ApplyKind.I,UtilConstants.C122_ApplyKind.J
						,UtilConstants.C122_ApplyKind.O };
				pageSetting.addSearchModeParameters(SearchMode.IN, "applyKind",
						applyKindArray);
			}
			String incomType = Util.trim(params.getString("incomType"));
			if (Util.isNotEmpty(incomType)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"IncomType", incomType);
			}
			
			String SignMegaEmp = Util.trim(params.getString("SignMegaEmp"));
			if (Util.isNotEmpty(SignMegaEmp)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"c122m01f.signMegaEmpNo", SignMegaEmp);
			}
			String ploanPlan = Util.trim(params.getString("ploanPlan"));
			if(Util.isNotEmpty(ploanPlan)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ploanPlan", ploanPlan);		
			}
			

		}
		// 取得資料
		Page<C122M01A> page = service.getC122M01A(pageSetting);
		List<C122M01A> list = (List<C122M01A>) page.getContent();
		Map<String, String> cache_map = new HashMap<String, String>();
		for (C122M01A model : list) {
			model.setLoanBrNo(service.build_loanBrNo(cache_map, model));
			model.setPayrollTransfersBrNo(service.build_payrollTransfersBrNo(
					cache_map, model));
		}

		final Map<String, String> _ApplyDocStatusDescMap = service
				.get_ApplyDocStatusDescMap();
		final Map<String, String> _ApplyKindDescMap = service
				.get_ApplyKindDescMap();
		final Map<String, String> _PloanStatFlagDescMap = service
				.get_PloanStatFlagDescMap(UtilConstants.C122_ApplyKind.E);
		final Map<String, String> _DocStatusNewDescMap = service
				.get_DocStatusNewDescMap();
		final Map<String, String> _IncomTypeMap = service.get_IncomTypeMap();
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("applyStatus", new IFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String applyStatus = Util.trim(in);
				return LMSUtil.getDesc(_ApplyDocStatusDescMap, applyStatus);
			}
		});
		formatter.put("statFlag", new IFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String statFlag = Util.trim(in);
				return LMSUtil.getDesc(_PloanStatFlagDescMap, statFlag);
			}
		});

		formatter.put("docStatusDesc", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				String docStatusDesc = "";
				docStatusDesc = Util.nullToSpace(meta.getDocStatus()) == null ? ""
						: LMSUtil.getDesc(_DocStatusNewDescMap,
								meta.getDocStatus());
				return docStatusDesc;
			}
		});

		formatter.put("applyKindDesc", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				String applyKindDesc = "";
				applyKindDesc = Util.nullToSpace(meta.getApplyKind()) == null ? ""
						: LMSUtil.getDesc(_ApplyKindDescMap,
								meta.getApplyKind());
				return applyKindDesc;
			}
		});
		formatter.put("incomTypeDesc", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				String IncomTypeDesc = "";
				IncomTypeDesc = Util.nullToSpace(meta.getIncomType()) == null ? ""
						: LMSUtil.getDesc(_IncomTypeMap, meta.getIncomType());
				return IncomTypeDesc;
			}
		});
		formatter.put("updater", new UserNameFormatter(userInfoService));
		formatter.put("laaName", new IBeanFormatter() {//J-112-0006 改為顯示地政士
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				C122M01F c122m01f = meta.getC122m01f();
				StringBuilder laaName = new StringBuilder();
				
				if(Util.isNotEmpty(c122m01f)){
					if(Util.equals( "4", c122m01f.getIntroduceSrc())){
						List<C122S01Y> c122S01yList = service.findC122S01YbyMainId(meta.getMainId());
						if(c122S01yList.size()>0){
							for(C122S01Y c122S01y : c122S01yList){
								if(Util.isNotEmpty(c122S01y.getLaaName())){
									if(Util.isNotEmpty(laaName)){
										laaName.append("、");
									}
									laaName.append(Util.trimSpace(c122S01y.getLaaName()));
								}
							}
						}
					}
				}
				return laaName.toString();
			}
		});
		// 第三個參數為formatting
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;

	}

	public CapGridResult queryView_Q(ISearch pageSetting, PageParameters params)
			throws CapException {
		// 建立主要Search 條件
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		if(Util.notEquals(ownBrId, "943")){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		}
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyKind",
		// UtilConstants.C122_ApplyKind.E);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (true) {
			String custId = Util.trim(params.getString("custId"));
			if (Util.isNotEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"custId", custId);
			}

			String ploanCaseId = Util.trim(params.getString("ploanCaseId"));
			if (Util.isNotEmpty(ploanCaseId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"ploanCaseId", ploanCaseId);
			}

			String applyTS_beg = Util.trim(params.getString("applyTS_beg"));
			String applyTS_end = Util.trim(params.getString("applyTS_end"));
			if (Util.isNotEmpty(applyTS_beg) && Util.isNotEmpty(applyTS_end)) {
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"applyTS", new Object[] { Util.parseDate(applyTS_beg),
								Util.parseDate(applyTS_end + " 23:59:59") });
			}
			String SdocStatus = Util.trim(params.getString("SdocStatus"));
			if (Util.isNotEmpty(SdocStatus)) {
				SdocStatus = SdocStatus + "%";
				pageSetting.addSearchModeParameters(SearchMode.LIKE,
						"docStatus", SdocStatus);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.OR, 
						new SearchModeParameter(SearchMode.EQUALS, "docStatus","E02"),
						new SearchModeParameter(SearchMode.OR, 
						new SearchModeParameter(SearchMode.LIKE, "docStatus","I%"),
						new SearchModeParameter(SearchMode.OR, 
						new SearchModeParameter(SearchMode.LIKE, "docStatus","G%"),
						new SearchModeParameter(SearchMode.OR, 
						new SearchModeParameter(SearchMode.LIKE, "docStatus","F%"),
						new SearchModeParameter(SearchMode.LIKE, "docStatus","Z%")))));
			}
			String applyKind = Util.trim(params.getString("applyKind"));
			String[] applyKindArray = null;
			if (Util.equals(applyKind, "B")) {
				applyKindArray = new String[] { "B"/*, "D"*/ };
			} else if (Util.equals(applyKind, "E")) {
				applyKindArray = new String[] { "E" };
			} else if (Util.equals(applyKind, "P")) {
				applyKindArray = new String[] { "P" };
			} else if (Util.equals(applyKind, "C")) {
				applyKindArray = new String[] { "C" };
			} else if (Util.equals(applyKind, "H")) {
				applyKindArray = new String[] { "H" };
			} else if (Util.equals(applyKind, UtilConstants.C122_ApplyKind.I )) { // 青創一百萬以下
				applyKindArray = new String[] { UtilConstants.C122_ApplyKind.I };
			} else if (Util.equals(applyKind, UtilConstants.C122_ApplyKind.J )) { // 青創一百萬以上
				applyKindArray = new String[] { UtilConstants.C122_ApplyKind.J };
			} else if (Util.equals(applyKind, UtilConstants.C122_ApplyKind.O )) { // 其他
				applyKindArray = new String[] { UtilConstants.C122_ApplyKind.O };
			}
			if (applyKindArray != null) {
				pageSetting.addSearchModeParameters(SearchMode.IN, "applyKind",
						applyKindArray);
			}else{
				applyKindArray = new String[] { "B", /*"D",*/ "E", "P", "C", "H", "I", "J", "O" };
				pageSetting.addSearchModeParameters(SearchMode.IN, "applyKind",
						applyKindArray);
			}
			String incomType = Util.trim(params.getString("incomType"));
			if (Util.isNotEmpty(incomType)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"IncomType", incomType);
			}
			String SignMegaEmp = Util.trim(params.getString("SignMegaEmp"));
			if (Util.isNotEmpty(SignMegaEmp)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"c122m01f.signMegaEmpNo", SignMegaEmp);
			}
			String ploanPlan = Util.trim(params.getString("ploanPlan"));
			if(Util.isNotEmpty(ploanPlan)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ploanPlan", ploanPlan);		
			}

		}
		// 取得資料
		Page<C122M01A> page = service.getC122M01A(pageSetting);
		List<C122M01A> list = (List<C122M01A>) page.getContent();
		Map<String, String> cache_map = new HashMap<String, String>();
		for (C122M01A model : list) {
			model.setLoanBrNo(service.build_loanBrNo(cache_map, model));
			model.setPayrollTransfersBrNo(service.build_payrollTransfersBrNo(
					cache_map, model));
		}

		final Map<String, String> _ApplyDocStatusDescMap = service
				.get_ApplyDocStatusDescMap();
		final Map<String, String> _ApplyKindDescMap = service
				.get_ApplyKindDescMap();
		final Map<String, String> _PloanStatFlagDescMap = service
				.get_PloanStatFlagDescMap(UtilConstants.C122_ApplyKind.E);
		final Map<String, String> _DocStatusNewDescMap = service
				.get_DocStatusNewDescMap();
		final Map<String, String> _IncomTypeMap = service.get_IncomTypeMap();
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("applyStatus", new IFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String applyStatus = Util.trim(in);
				return LMSUtil.getDesc(_ApplyDocStatusDescMap, applyStatus);
			}
		});
		formatter.put("statFlag", new IFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String statFlag = Util.trim(in);
				return LMSUtil.getDesc(_PloanStatFlagDescMap, statFlag);
			}
		});
		formatter.put("docStatusDesc", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				String docStatusDesc = "";
				docStatusDesc = Util.nullToSpace(meta.getDocStatus()) == null ? ""
						: LMSUtil.getDesc(_DocStatusNewDescMap,
								meta.getDocStatus());
				return docStatusDesc;
			}
		});

		formatter.put("applyKindDesc", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				String applyKindDesc = "";
				applyKindDesc = Util.nullToSpace(meta.getApplyKind()) == null ? ""
						: LMSUtil.getDesc(_ApplyKindDescMap,
								meta.getApplyKind());
				return applyKindDesc;
			}
		});
		formatter.put("incomTypeDesc", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				String IncomTypeDesc = "";
				IncomTypeDesc = Util.nullToSpace(meta.getIncomType()) == null ? ""
						: LMSUtil.getDesc(_IncomTypeMap, meta.getIncomType());
				return IncomTypeDesc;
			}
		});
		formatter.put("updater", new UserNameFormatter(userInfoService));
		formatter.put("laaName", new IBeanFormatter() {//J-112-0006 改為顯示地政士
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				C122M01F c122m01f = meta.getC122m01f();
				StringBuilder laaName = new StringBuilder();
				
				if(Util.isNotEmpty(c122m01f)){
					if(Util.equals( "4", c122m01f.getIntroduceSrc())){
						List<C122S01Y> c122S01yList = service.findC122S01YbyMainId(meta.getMainId());
						if(c122S01yList.size()>0){
							for(C122S01Y c122S01y : c122S01yList){
								if(Util.isNotEmpty(c122S01y.getLaaName())){
									if(Util.isNotEmpty(laaName)){
										laaName.append("、");
									}
									laaName.append(Util.trimSpace(c122S01y.getLaaName()));
								}
							}
						}
					}
				}
				return laaName.toString();
			}
		});
		// 第三個參數為formatting

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public CapMapGridResult importRealEstateAgentInfo(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString("mainId"));
		List<Map<String, Object>> list = this.service
				.getRealEstateAgentInfo(mainId);
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
		// return null;
	}

	public CapGridResult query_applyKindE_History(ISearch pageSetting,
			PageParameters params) throws CapException {
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
		// ownBrId); => 查到客戶是否誤選其它分行
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyKind",
		// UtilConstants.C122_ApplyKind.E);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		String custId = Util.trim(params.getString("custId"));
		if (Util.equals("Y", params.getString("flag"))) {
			// 當 flag==Y，查才資料
			if (Util.isEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"custId", "");
			} else {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId",
						custId + "%");
			}
		} else {
			// 不然清空前端 grid 的資料列(用查到0筆的方式)
			pageSetting
					.addSearchModeParameters(SearchMode.EQUALS, "custId", "");
		}

		pageSetting.setMaxResults(Integer.MAX_VALUE);
		if (true) {
			pageSetting.addOrderBy("applyTS", true);
			pageSetting.addOrderBy("custId", false);
		}
		Page<C122M01A> page = service.getC122M01A(pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("applyTS", new ADDateTimeFormatter("yyyy-MM-dd"));
		formatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.IDSpaceName));
		formatter.put("orgBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.IDSpaceName));
		//案件類型
		final Map<String, String> _ApplyKindDescMap = service.get_ApplyKindDescMap();
		formatter.put("applyKind", new IFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String applyKind = Util.trim(in);
				 return LMSUtil.getDesc(_ApplyKindDescMap, applyKind);
//				return applyKind;
			}
		});
		
		//案件狀態
		final Map<String, String> _DocStatusNewDescMap = service.get_DocStatusNewDescMap();
		formatter.put("docStatus", new IFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String docStatus = Util.trim(in);
				return LMSUtil.getDesc(_DocStatusNewDescMap, docStatus);
			}
		});
		
		//案件狀態
		final Map<String, String> _IncomTypeMap = service.get_IncomTypeMap();
		formatter.put("incomType", new IFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String incomType = Util.trim(in);
				return LMSUtil.getDesc(_IncomTypeMap, incomType);
			}
		});
		
		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);
	}

	/**
	 * 查詢RPA地政士結果
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryRpaResultDetail(ISearch pageSetting,
			PageParameters params) throws CapException {
		ISearch search = createSearchTemplete();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(search);
//		Page page = service.findPage(L161S01D.class, pageSetting);
		Page<L161S01D> page = service.getL161S01D(pageSetting);

		Map<String, String> codeMap = codeTypeService
				.findByCodeType("l161s01d_status");

		List<L161S01D> list = page.getContent();
		for (L161S01D l161s01d : list) {
			l161s01d.setStatusDesc(codeMap.get(l161s01d.getStatus()));
		}

		Map<String, IFormatter> fmtMap = new HashMap<String, IFormatter>();

		fmtMap.put("type", new CodeTypeFormatter(codeTypeService,
				"l161s01d_type", CodeTypeFormatter.ShowTypeEnum.Desc));

		CapGridResult capGridResult = new CapGridResult(page.getContent(),
				page.getTotalRow(), fmtMap);
		return capGridResult;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public CapGridResult importCustInfo(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString("mainId"));
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		String custId = c122m01a.getCustId();
		
		// List<Map<String, Object>> list =
		// this.service.getRealEstateAgentInfo(mainId);
		String dupNo = null;

		String incomType = Util.trim(c122m01a.getIncomType());
		if (Util.equals(incomType, "1")) { // 線下進件
			dupNo = c122m01a.getDupNo();
		}
		
		// 搜尋By CustId
		String isSearchByCustId = Util.trim(params.getString("isSearchByCustId"));
		if ("Y".equals(isSearchByCustId)) {
			custId = Util.trim(params.getString("custId"));
			dupNo = null;
			if (custId.isEmpty()) {
//				custId = null;
//				dupNo = null;
			}
		}
		
//		Page<? extends GenericBean> page = service.findPage(C101M01A.class,pageSetting);
		List<C101M01A> c101m01aList = service.findC101M01A(c122m01a.getOwnBrId(), custId, dupNo);
		for (C101M01A model : c101m01aList) {
			// 統編+重覆序號
			model.setCustNumber(Util.trim(model.getCustId()) + " "
					+ Util.trim(model.getDupNo()));
			// 評等模型
			model.setMarkModel(Util.trim(getI18nMsg("C101M01A.markModel."
					+ Util.trim(model.getMarkModel()))));
			
			if(true){//房貸
				C101S01G c101s01g = model.getC101s01g();
				if (c101s01g != null) {
					// 調整評等
					if(ClsUtil.showMarkModelInfoByQuote(c101s01g.getQuote())){
						c101s01g.setGrade2(ClsUtil.getAdjustStatus(c101s01g));	
					}else{
						c101s01g.setGrade1("");
						c101s01g.setGrade2("");
						c101s01g.setGrade3("");
					}
					
					// 警示訊息
					c101s01g.setAlertMsg(Util.trim(
							LMSUtil.getGradeMessage(c101s01g, UtilConstants.L140S02AModelKind.房貸, 
									OverSeaUtil.TYPE_RAW, "",UtilConstants.CheckItemRange.defult)).replaceAll(
							EloanConstants.HTML_NEWLINE, " "));
				}	
			}
			
			// C101S01E c101s01e = model.getC101s01e();
			
			if(true){//非房貸
				C101S01Q c101s01q = model.getC101s01q();
				if (c101s01q != null) {
					// 調整評等
					if(ClsUtil.showMarkModelInfoByQuote(c101s01q.getQuote())){
						c101s01q.setGrade2(ClsUtil.getAdjustStatus(c101s01q));	
					}else{
						c101s01q.setGrade1("");
						c101s01q.setGrade2("");
						c101s01q.setGrade3("");
					}					
					// 警示訊息 => 前端js只用到 c101s01g.alertMsg
//					c101s01q.setAlertMsg(Util.trim(
//							LMSUtil.getGradeMessage(c101s01q, UtilConstants.L140S02AModelKind.非房貸, OverSeaUtil.TYPE_RAW)).replaceAll(
//							EloanConstants.HTML_NEWLINE, " "));
				}	
			}
			
			if(true){//卡友貸
				C101S01R c101s01r = model.getC101s01r();
				if (c101s01r != null) {
					// 調整評等
					if(ClsUtil.showMarkModelInfoByQuote(c101s01r.getQuote())){
						c101s01r.setGrade2(ClsUtil.getAdjustStatus(c101s01r));	
					}else{
						c101s01r.setGrade1("");
						c101s01r.setGrade2("");
						c101s01r.setGrade3("");
					}					
					// 警示訊息 => 前端js只用到 c101s01g.alertMsg
//					c101s01r.setAlertMsg(Util.trim(
//							LMSUtil.getGradeMessage(c101s01r, UtilConstants.L140S02AModelKind.卡友貸, OverSeaUtil.TYPE_RAW)).replaceAll(
//							EloanConstants.HTML_NEWLINE, " "));
				}	
			}
		}

//		return new CapGridResult(page.getContent(), page.getTotalRow());

//		Page<C101M01A> page = LMSUtil.getMapGirdDataRow(c101m01aList,
//				pageSetting);
//		return new CapGridResult(page.getContent(), page.getTotalRow());
		
		return new CapGridResult(c101m01aList, c101m01aList.size());
	}
	
	
	
	/**
	 * 不承做資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryItemG(ISearch pageSetting, PageParameters params)
			throws CapException {
		ISearch search = createSearchTemplete();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(search);
		Page page = null;

		page = service.getC122S01GPage(pageSetting);
		

		// 第三個參數為formatting
		CapGridResult capGridResult = new CapGridResult(page.getContent(),
				page.getTotalRow(), null);
		return capGridResult;
	}
	
	
	
	/**
	 * 案件流程紀錄
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult getDataSustus(ISearch pageSetting,
			PageParameters params) throws CapException {
		ISearch search = createSearchTemplete();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(search);
		Page page = null;

		page = service.getC122S01HPage(pageSetting);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		
		final Map<String, String> _flowIdDescMap = service.get_FlowIdDescMap();
		formatter.put("flowIdDesc", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122S01H meta = (C122S01H) in;
				String flowIdDesc = "";
				flowIdDesc = Util.nullToSpace(meta.getFlowId()) == null ? ""
						: LMSUtil.getDesc(_flowIdDescMap,
								meta.getFlowId());
				return flowIdDesc;
			}
		});
		final Map<String, String> _DocStatusNewDescMap = service.get_DocStatusNewDescMap();
		formatter.put("flowdocStatus", new IFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String flowdocStatus = Util.trim(in);
				return LMSUtil.getDesc(_DocStatusNewDescMap, flowdocStatus);
			}
		});

		formatter.put("reOid", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122S01H meta = (C122S01H) in;
				if (meta.getFlowId().equals(UtilConstants.C122s01h_flowId.案件簽報書)) {
					L120M01A l120M01A = clsService.findL120M01A_mainId(meta.getRemainId());
					return l120M01A.getOid();
				}
				return  null;
			}
		});
		formatter.put("reMainDocStatus", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122S01H meta = (C122S01H) in;
				if (meta.getFlowId().equals(UtilConstants.C122s01h_flowId.案件簽報書)) {
					L120M01A l120M01A = clsService.findL120M01A_mainId(meta.getRemainId());
					return l120M01A.getDocStatus();
				}
				return  null;
			}
		});
		

		// 第三個參數為formatting
		CapGridResult capGridResult = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return capGridResult;
	}
	
	
	/**
	 * 相同IP於30天內進件資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 * @throws ParseException 
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult getSameIpInfoIn30Day(ISearch pageSetting,
			PageParameters params) throws CapException, ParseException {
		ISearch search = createSearchTemplete();
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		//線上作業才需要找
		Page<C122M01A> page = null;
		CapGridResult capGridResult = new CapGridResult();
		String incomType = Util.trim(c122m01a.getIncomType());
		if(Util.equals(incomType, UtilConstants.C122_IncomType.線上) && Util.isNotEmpty(c122m01a.getApplyIpFreq())){ 
			BigDecimal applyIpFreq = c122m01a.getApplyIpFreq();
			if(applyIpFreq.compareTo(BigDecimal.ZERO) != 0){
				Timestamp nowTS = c122m01a.getCreateTime();
				Date sDate = CapDate.shiftDays(nowTS, -30);
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				sDate = sdf.parse((TWNDate.toAD(sDate)+" 00:00:00"));
				Date newNowTS = sdf.parse((TWNDate.toAD(nowTS)+" 23:59:59"));
				
				if(true){
					search.addSearchModeParameters(SearchMode.EQUALS, "applyIPAddr", Util.trim(c122m01a.getApplyIPAddr()));
					search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "applyTS", sDate);
					search.addSearchModeParameters(SearchMode.LESS_EQUALS, "applyTS", newNowTS);
					search.addSearchModeParameters(SearchMode.NOT_EQUALS, "mainId", mainId);
					//不抓從債務人
					pageSetting.addSearchModeParameters(SearchMode.AND,
							new SearchModeParameter(SearchMode.NOT_EQUALS, "applyKind",UtilConstants.C122_ApplyKind.F),
							new SearchModeParameter(SearchMode.NOT_EQUALS, "applyKind",UtilConstants.C122_ApplyKind.Q));
				}
				search.setMaxResults(Integer.MAX_VALUE);	
				pageSetting.addSearchModeParameters(search);
				// 取得資料
				page = service.getC122M01A(pageSetting);
				Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
				formatter.put("ownBrId", new BranchNameFormatter(branchService,
						ShowTypeEnum.IDSpaceName));
				
				formatter.put("applyAmt", new NumericFormatter()); 
				capGridResult = new CapGridResult(page.getContent(),
						page.getTotalRow(), formatter);
			}
		}
		
		// 第三個參數為formatting
		
		return capGridResult;
	}
	
	/**
	 * 取得地政士清單
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult getC122S01YList(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		List<C122S01Y> c122S01yList = service.findC122S01YbyMainId(mainId);
		
		return new CapGridResult(c122S01yList, c122S01yList.size());
	}	
	
	/**
	 * 查詢該分行的C900M01N資料
	 * 該分行可被分案的人員檔(已設定的)
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryEmpCanAssignCase(ISearch pageSetting,
			PageParameters params) throws CapException {
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		
		if(Util.notEquals(ownBrId, "943")){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "brNo",
					ownBrId);
		}
		
		// 刪除已離職的C900M01N
		SysParameter deleteLeave = sysparamService.findByParam("C900M01N_DELETE_LEAVE_USER");
		String deleteFunctionOn = Util.trim(deleteLeave.getParamValue());
		if (Util.equals(deleteFunctionOn, "Y")) {
			service.deleteC900m01nIsLeave(ownBrId);
		}

		// 這邊再查詢C900M01N
		Page<C900M01N> page = service.findC900m01nPage(pageSetting);
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		// 不用擔心效能問題，userInfoService有userNameCache的機制在(很潮)
		formatter.put("assignEmpName", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C900M01N c900m01n = (C900M01N)in;
				return userInfoService.getUserName(c900m01n.getAssignEmpNo());
			}
		});
		
		// 第三個參數為formatting
		CapGridResult capGridResult = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return capGridResult;
	}
	
	/**
	 * 查詢該分行的人員檔
	 * 想維護被分案的人員檔的時候用
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryThisBrNoEmp(ISearch pageSetting,
			PageParameters params) throws CapException {
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		
		// 採用跟大家一樣的撈法，到時候比較好說過去
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管,SignEnum.甲級主管, SignEnum.乙級主管, SignEnum.經辦人員 };
		Map<String, String> userMap = userInfoService.findByBrnoAndSignId(MegaSSOSecurityContext.getUnitNo(), signs);
		
		// 已經離職的人就拔掉!!讓他們再清單維護裡選不到
		SysParameter deleteLeave = sysparamService.findByParam("C900M01N_DELETE_LEAVE_USER");
		String deleteFunctionOn = Util.trim(deleteLeave.getParamValue());
		if (Util.equals(deleteFunctionOn, "Y")) {
			Map<String, ElsUser> userModelMap = userInfoService.getBRUser(MegaSSOSecurityContext.getUnitNo());
			List<String> needRemoveUser = new ArrayList<String>();
			for (Map.Entry<String, String> entry : userMap.entrySet()) {
				ElsUser elsUser = userModelMap.get(entry.getKey());
				if(elsUser != null){
					if(Util.equals("Y", elsUser.getLeaveFlag())){
						// 已經離職的人，需要被從userMap中移除
						needRemoveUser.add(entry.getKey());
					}
				}
			}
			for(String removeUser : needRemoveUser){
				userMap.remove(removeUser);
			}
		}
		
		// 943有設定一批專門KYC的人員，可能是消金處也有可能是非三家分行的經辦，他們也要被列在裡面
		Map<String, String> M943KycUser_Map = codeTypeService.findByCodeType(
				"c122m01a_943KycUser", LocaleContextHolder.getLocale().toString());
		
		// 2023.02.24  消金處表示先顯示消金KYC人員，再顯示分行人員
		Map<String, String> finalUserMap = new LinkedHashMap<String, String>();
		if(M943KycUser_Map != null){
			finalUserMap.putAll(M943KycUser_Map);
		}
		if(userMap != null){
			finalUserMap.putAll(userMap);
		}
		
		Map<String, C900M01N> existMap = service.findC900m01nByBrNo(ownBrId);
		
		List<Map<String, Object>> userList = new ArrayList<Map<String, Object>>();
		for (Map.Entry<String, String> entry : finalUserMap.entrySet()) {
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("userId", entry.getKey());
			data.put("userName", entry.getValue());
			
			if(existMap.get(entry.getKey()) != null){
				data.put("exist", "Y");
				data.put("assignOrder", existMap.get(entry.getKey()).getAssignOrder());
			}else{
				data.put("exist", "");
				data.put("assignOrder", "");
			}
			
			userList.add(data);
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(userList,pageSetting);

		// 第三個參數為formatting
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
	
	/**
	 * 查詢已選擇被分案人各自獲得的案件數
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryAssignCase(ISearch pageSetting,
			PageParameters params) throws CapException {
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		
		String[] assignEmpNos = StringUtils.split(params.getString("assignEmpNos"), ",");
		String lastC122m01aOid = params.getString("lastC122m01aOid");
		List<Map<String, Object>> assignCaseList = service.queryAssignCaseCount(ownBrId, assignEmpNos, lastC122m01aOid);
		
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(assignCaseList,pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		// 不用擔心效能問題，userInfoService有userNameCache的機制在(很潮)
		formatter.put("assignEmpName", new UserNameFormatter(userInfoService));
		
		// 第三個參數為formatting
		return new CapMapGridResult(page.getContent(), page.getTotalRow(), formatter);
	}
	
	/**
	 * 初始化[派案分行設定]Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("serial")
	public CapGridResult queryAutoAssignGrid(ISearch pageSetting, PageParameters params) throws CapException {
		Page<? extends GenericBean> page = service.findPage(C900M01O.class, pageSetting);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("assigneeBrchId", new IFormatter() {
			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				String brName = branchService.getBranch((String) in).getBrName();
				return in + " - " + brName;
			}
		});
		CapGridResult result = new CapGridResult(page.getContent(), page.getTotalRow(), formatter);
		return result;
	}
	
	//H-111-0199 取得IXML清單
	@SuppressWarnings("unchecked")
	public CapGridResult getC122M01GGrid(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		// 取得資料
		List<C122M01G> c122m01gList = service.findC122M01GbyMainId(mainId);
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		
		final Map<String, String> sendFTPStatusDescMap = 
			codeTypeService.findByCodeType("CLS_IXML_sendFTPStatus", "zh_TW");
		formatter.put("sendFTPStatus", new IFormatter(){
			@Override
			public String reformat(Object in) throws CapFormatException {
				String sendFTPStatus = Util.trim(in);
				if(Util.isEmpty(sendFTPStatus)){
					return "待驗章";
				}
				return LMSUtil.getDesc(sendFTPStatusDescMap, sendFTPStatus);
			}
		
		});
		
		CapGridResult c122m01gResult = new CapGridResult(c122m01gList, c122m01gList.size());
		c122m01gResult.setDataReformatter(formatter);
		return c122m01gResult;
		
	}

	
	/**
	 * @param key
	 * @return
	 */
	private String getI18nMsg(String key) {
		String result = null;
		if (prop == null){
			prop = MessageBundleScriptCreator
					.getComponentResource(CLS1131V01Page.class);
		}
		if (prop != null) {
			result = prop.getProperty(Util.trim(key));
		}
		return Util.trim(result);
	}
	
	/**
	 * 引介案件進度查詢
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult query_case_progess(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		UnitTypeEnum unitType = UnitTypeEnum.convertToUnitType(branchService.getBranch(user.getUnitNo()));
		
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		String custId = Util.trim(params.getString("custId"));
		String brIdFilter = Util.trim(params.getString("brIdFilter"));
		String introduceMegaEmp = Util.trim(params.getString("introduceMegaEmp"));
		String ploanCaseId = Util.trim(params.getString("ploanCaseId"));
		String applyTS_beg = params.getString("applyTS_beg");
		String applyTS_end = params.getString("applyTS_end");
		
		if (Util.isEmpty(applyTS_beg) || Util.isEmpty(applyTS_end)) {//沒輸入進件日期不回傳結果
			// 不然清空前端 grid 的資料列(用查到0筆的方式)
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyTS", "");
		} else {
			//進建日期
			//pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS, "applyTS", (applyTS +" 00:00:00"));
			//pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS, "applyTS", (applyTS +" 23:59:59"));		
			SearchModeParameter se_applyTS_start = new SearchModeParameter(SearchMode.GREATER_EQUALS, "applyTS", Util.parseDate(applyTS_beg +" 00:00:00"));
			SearchModeParameter se_applyTS_end = new SearchModeParameter(SearchMode.LESS_EQUALS, "applyTS", Util.parseDate(applyTS_end +" 23:59:59"));
			SearchModeParameter se_applyTS_start_end = new SearchModeParameter(SearchMode.AND, 
					new SearchModeParameter(SearchMode.IS_NOT_NULL, "applyTS", ""), 
					new SearchModeParameter(SearchMode.AND, se_applyTS_start, se_applyTS_end));
			
			SearchModeParameter caseDate_start = new SearchModeParameter(SearchMode.GREATER_EQUALS, "applyTS", Util.parseDate(applyTS_beg +" 00:00:00"));
			SearchModeParameter caseDate_end = new SearchModeParameter(SearchMode.LESS_EQUALS, "applyTS", Util.parseDate(applyTS_end +" 23:59:59"));
			SearchModeParameter caseDate_start_end = new SearchModeParameter(SearchMode.AND, 
					new SearchModeParameter(SearchMode.IS_NULL, "applyTS", ""), 
					new SearchModeParameter(SearchMode.AND, caseDate_start, caseDate_end));

			pageSetting.addSearchModeParameters(SearchMode.OR, caseDate_start_end, se_applyTS_start_end);
			//僅抓線上案件
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "IncomType", "2");
			
			//申請類別固定篩抓 C(信貸), P(卡友信貸) 
			String[] applyKindArray = new String[] {"P", "C"};
			pageSetting.addSearchModeParameters(SearchMode.IN, "applyKind", applyKindArray);
			
			//marketingStaff
			//SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管,SignEnum.甲級主管, SignEnum.乙級主管, SignEnum.經辦人員 };
			//Map<String, ElsUser> users_109 = userInfoService.getBRUser("109");
			//String.format("%05d", Integer.parseInt(Util.trim(meta.getMarketingStaff())));
			
			
			//身分證統編
			if (!Util.isEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId", custId + "%");
			}
			//引介分行
			if (!Util.isEmpty(brIdFilter)) {//篩選原始申貸行
				//pageSetting.addSearchModeParameters(SearchMode.IN, "ploanIntroduceBr1st", new String[] {"", brIdFilter});
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "orgBrId", brIdFilter);
			} else {
				if(unitType.isEquals(UnitTypeEnum.分行)){//分行使用者僅能查看所屬分行
					//pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ploanIntroduceBr1st", user.getUnitNo());
					//pageSetting.addSearchModeParameters(SearchMode.IN, "ploanIntroduceBr1st", new String[] {"", user.getUnitNo()});
					pageSetting.addSearchModeParameters(SearchMode.EQUALS, "orgBrId", user.getUnitNo());
					
				}
			}
			//引介人員
			if (!Util.isEmpty(introduceMegaEmp)) {
				//pageSetting.addSearchModeParameters(SearchMode.EQUALS, "megaEmpNo", chgSignMegaEmp);
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "marketingStaff", introduceMegaEmp);
			}
			//案件編號
			if (!Util.isEmpty(ploanCaseId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ploanCaseId", ploanCaseId);
			}
		}

		
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		if (true) {
			pageSetting.addOrderBy("applyTS", true);
			pageSetting.addOrderBy("custId", false);
		}
		Page<C122M01A> page = service.getC122M01A(pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("applyTS", new ADDateTimeFormatter("yyyy-MM-dd"));
		formatter.put("ownBrId", new BranchNameFormatter(branchService, ShowTypeEnum.IDSpaceName));
		formatter.put("orgBrId", new BranchNameFormatter(branchService, ShowTypeEnum.IDSpaceName));
		//If (ploanIntroduceBr1st) else (ORGBRID)
		formatter.put("ploanIntroduceBr1st", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				String ploanIntroduceBr1st = "";
				ploanIntroduceBr1st = Util.isEmpty(meta.getPloanIntroduceBr1st()) ? meta.getOrgBrId() : meta.getPloanIntroduceBr1st();
				return ploanIntroduceBr1st;
			}
		});
		//案件類型
		final Map<String, String> _ApplyKindDescMap = service.get_ApplyKindDescMap();
		formatter.put("applyKind", new IFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String applyKind = Util.trim(in);
				 return LMSUtil.getDesc(_ApplyKindDescMap, applyKind);
//				return applyKind;
			}
		});
		
		//案件狀態
		final Map<String, String> _DocStatusNewDescMap = service.get_DocStatusNewDescMap();
		formatter.put("docStatus", new IFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String docStatus = Util.trim(in);
				return LMSUtil.getDesc(_DocStatusNewDescMap, docStatus);
			}
		});
		
		//進件類型
		final Map<String, String> _IncomTypeMap = service.get_IncomTypeMap();
		formatter.put("incomType", new IFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String incomType = Util.trim(in);
				return LMSUtil.getDesc(_IncomTypeMap, incomType);
			}
		});
		//簽案行員
		formatter.put("signMegaEmpNo", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				C122M01F c122m01f = meta.getC122m01f();
				StringBuilder SignMegaEmpNo = new StringBuilder();				
				if(Util.isNotEmpty(c122m01f)){
					SignMegaEmpNo.append(c122m01f.getSignMegaEmpNo());
				}
				return SignMegaEmpNo.toString();
			}
		});
//		//引介人員員編
//		formatter.put("megaEmpNo", new IBeanFormatter() {
//			@Override
//			public String reformat(Object in) throws CapFormatException {
//				C122M01A meta = (C122M01A) in;
//				C122M01F c122m01f = meta.getC122m01f();
//				StringBuilder megaEmpNo = new StringBuilder();				
//				if(Util.isNotEmpty(c122m01f)){
//					megaEmpNo.append(c122m01f.getMegaEmpNo());
//				}
//				return megaEmpNo.toString();
//			}
//		});
		//引介人員員編
		formatter.put("marketingStaff", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				return ("".equals(Util.trim(meta.getMarketingStaff()))) ? "" : String.format("%06d", Integer.parseInt(Util.trim(meta.getMarketingStaff())));
			}
		});
		
		
		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);
	}
}