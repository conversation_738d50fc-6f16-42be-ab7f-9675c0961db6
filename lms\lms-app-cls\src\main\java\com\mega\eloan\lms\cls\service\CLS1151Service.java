/* 
 * CLS1151Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.model.C100M01;
import com.mega.eloan.lms.model.C102M01A;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C900M01D;
import com.mega.eloan.lms.model.C900M01F;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.eloan.lms.model.L140M01H;
import com.mega.eloan.lms.model.L140M01L;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M01P;
import com.mega.eloan.lms.model.L140M01Q;
import com.mega.eloan.lms.model.L140M01Y;
import com.mega.eloan.lms.model.L140M02A;
import com.mega.eloan.lms.model.L140M04A;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02B;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.eloan.lms.model.L140S02E;
import com.mega.eloan.lms.model.L140S02H;
import com.mega.eloan.lms.model.L140S02M;
import com.mega.eloan.lms.model.L140S10A;
import com.mega.eloan.lms.model.L140S10C;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;

/**
 * <pre>
 * 額度明細表介面
 * </pre>
 * 
 * @since 2012/12/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/10,REX,new
 *          </ul>
 */
public interface CLS1151Service extends AbstractService {
	/**
	 * 
	 * 
	 * 取得 分段利率明細檔
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @param seq
	 *            產品種類seq
	 * @return
	 */
	public List<L140S02D> findL140S02DbyMainIdAndSeq(String mainId, int seq);

	/**
	 * 新增額度明細表
	 * 
	 * @param params
	 *            前端資訊
	 */
	public void addL140M01A(String caseMainId, C120M01A c120m01a,
			boolean newCntrNoDoc_fg, boolean oldCntrNoDoc_fg,
			boolean cancelCntrNoDoc_fg, Set<String> grpCntrNos,
			String cancelCntrNos) throws CapException;

	// All

	@SuppressWarnings("rawtypes")
	public List<? extends GenericBean> findModelListByMainId(Class clazz,
			String mainId);

	/**
	 * 查詢產品相關
	 * 
	 * @param clazz
	 * @param mainId
	 * @param seq
	 *            產品seq
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public <T extends GenericBean> T findModelByMainIdAndSeq(Class clazz,
			String mainId, Integer seq);

	/**
	 * 查詢產品相關 list
	 * 
	 * @param clazz
	 * @param mainId
	 * @param seq
	 *            產品seq
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public List<? extends GenericBean> findModelListByMainIdAndSeq(Class clazz,
			String mainId, Integer seq);

	// All

	@SuppressWarnings("rawtypes")
	public <T extends GenericBean> T findModelByMainId(Class clazz,
			String mainId);

	/**
	 * 刪除byOids
	 * 
	 * @param clazz
	 *            moode class
	 * @param oids
	 *            oid array
	 */
	@SuppressWarnings("rawtypes")
	public void deleteModelListByOids(Class clazz, String[] oids);

	// All

	@SuppressWarnings("rawtypes")
	public List<? extends GenericBean> findModelListByOids(Class clazz,
			String[] oids);

	/**
	 * 引進共同借款人
	 * 
	 * @param oids
	 *            oid array
	 * @param mainId
	 *            額度明細mainId
	 * @param custPos
	 *            借款人種類
	 * @return
	 * @throws CapMessageException
	 */
	public String inculdeL140S01A(String[] oids, String mainId, String custPos,
			String rKindM, String rKindD, String reson, String resonOther,
			BigDecimal guaPercent, List<String> errList, String isLiveWithBorrower) throws CapMessageException;

	/**
	 * 查詢長擔額度序號
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @param seq
	 *            產品種類seq
	 * @param cntrNo
	 *            額度序號
	 * @return 長擔額度序號檔
	 */
	public L140S02B findL140S02B(String mainId, int seq, String cntrNo);

	/**
	 * 取得案由（註：此 method 裡有用到 l140s02aDao.flush()，在 FormHandler 去 save 其它的 table
	 * 有出現 Attempt to persist detached object......）
	 * 
	 * @param caseMainId
	 *            簽報書mainId
	 * @return 案由字串
	 */
	public String getGist(String caseMainId);

	/**
	 * 為了避免 出現 Attempt to persist detached object......
	 * <ul>
	 * <li>先執行既有的 method：getGist(String caseMainId) 去取得 gist</li>
	 * <li>在另一個 ServiceImpl.java 的 method，去把 gist 存入 L120M01A</li>
	 * </ul>
	 * 
	 * @param caseMainId
	 */
	public void set_l120m01a_gist(String caseMainId, String gist);

	/**
	 * 重新設定借保人備註
	 * 
	 * @param mainId
	 *            簽報書mainId
	 */
	public void setC120M01AsRmk(String mainId);

	/**
	 * 額度批覆表
	 * 
	 * @param mainId
	 *            文件編號
	 * @return L140M02A
	 */
	L140M02A findL140M02AByMainId(String mainId);

	/**
	 * 儲存model
	 * 
	 * @param entity
	 *            model
	 * 
	 */
	void save(GenericBean... entity);

	/**
	 * 刪除model
	 * 
	 * @param entity
	 *            model
	 * @return
	 */
	void delete(GenericBean... entity);

	/**
	 * 取得Grid呈現所需的資料
	 * 
	 * @param clazz
	 *            要搜索model的class
	 * @param search
	 *            搜索的條件
	 * @return Page
	 */
	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * 用oid取得這筆資料
	 * 
	 * @param <T>
	 *            model
	 * @param clazz
	 *            要搜索model的class
	 * @param oid
	 *            文件編號
	 * 
	 * @return GenericBean
	 */
	@SuppressWarnings("rawtypes")
	<T extends GenericBean> T findModelByOid(Class clazz, String oid);

	/**
	 * 取得額度明細表的說明敘述檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @return List<L140M01B>
	 */
	List<L140M01B> findL140m01bByMainId(String mainId);

	/**
	 * 找出關聯檔內該mainId下所有的額度明細表、額度批覆表、母行法人提案意見
	 * 
	 * @param caseMainId
	 *            案件簽報書文件編號
	 * @return List<L120M01C>
	 */
	List<L120M01C> findL120m01cListByMainId(String caseMainId);

	/**
	 * 判斷相同借款人的額度明細表，有幾種幣別，跟有幾個借款人已登錄額度明細表
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return List<Object[]>
	 */
	List<Object[]> findL140m01aListByL120m01c(String caseMainId, String caseType);

	/**
	 * 計算額度明細表合計
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainId
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return Map<String, Map<String, Long>>
	 */
	Map<String, Map<String, BigDecimal>> findL140m01Count(String caseMainId,
			String caseType) throws Exception;

	/**
	 * 計算額度明細表合計兩種以上幣別
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainId
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param curr
	 *            要算出的幣別
	 * @param showCurr
	 *            是否顯示多幣別
	 * 
	 * @return Map<String, Map<String, BigDecimal>> 計算結果
	 */
	Map<String, Map<String, BigDecimal>> findL140m01CountToTwoCurr(
			String caseMainId, String caseType, String curr, Boolean showCurr)
			throws Exception;

	/**
	 * 計算額度明細表合計給供調整視窗使用
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param countCurr
	 *            主要計算幣別
	 * @param showCurr
	 *            是否顯示多幣別
	 * @return 計算結果
	 */
	JSONArray findL140m01editCount(String caseMainId, String caseType,
			String countCurr, Boolean showCurr) throws Exception;

	/**
	 * 找出此案件簽報書底下的額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * @param docStatus
	 *            額度明細表文件狀態
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType, String docStatus);

	/**
	 * 找出此案件簽報書底下的額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            String [] 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * @param docStatus
	 *            額度明細表文件狀態
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String[] caseType, String docStatus);

	/**
	 * 找出此案件簽報書底下的額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * 
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType);

	/**
	 * 找出此案件簽報書底下的額度明細表-給列印用的順序
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * 
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainIdForPrint(String mainId,
			String caseType);

	/**
	 * 取得額度明細表
	 * 
	 * @param mainId
	 *            額度明細表文件編號
	 * @return L140M01A
	 */
	L140M01A findL140m01aByMainId(String mainId);

	/**
	 * 儲存額度明細檔主檔model list
	 * 
	 * @param list
	 *            List<L140M01A>
	 */
	void saveL140m01aList(List<L140M01A> list);

	/**
	 * 根據此額度明細表的mainId 刪除底下所有關聯table
	 * 
	 * @param mainId
	 *            文件編號
	 */
	void deleteL140m01(String mainId);

	void deleteL140m01(String mainId, Timestamp delTs);

	/**
	 * 取得額度明細表的說明敘述檔
	 * 
	 * @param mainId
	 *            額度明細表文件編號
	 * @param itemType
	 *            說明的種類
	 * 
	 *            <pre>
	 *  1、限額條件
	 * 	2、利(費)率
	 * 	3、擔保品
	 * 	4、其他敘做條件
	 * 	5、敘做條件異動情形
	 * 	6、附表第一頁
	 * 	7、附表第二頁
	 * 	8、附表第三頁
	 * </pre>
	 * 
	 * @return L140M01B
	 */
	L140M01B findL140m01bUniqueKey(String mainId, String itemType);

	/**
	 * 儲存額度明細表的說明敘述檔List
	 * 
	 * @param list
	 *            L140M01B List
	 */
	void saveL140m01bList(List<L140M01B> list);

	/**
	 * 額度聯行攤貸比率檔 List
	 * 
	 * @param mainId
	 *            文件編號
	 * @param shareBrId
	 *            攤貸分行代碼
	 * @return L140M01E
	 */
	L140M01E findL140m01eByUniqueKey(String mainId, String shareBrId);

	/**
	 * 額度聯行攤貸比率檔 刪除多筆
	 * 
	 * @param oids
	 *            文件編號陣列
	 */
	void deleteL140m01eList(String[] oids);

	// L140M01H
	/**
	 * 額度費率明細檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @param rateSeq
	 *            序列號
	 * @return L140M01H
	 */
	L140M01H findL140m01hByUniqueKey(String mainId, Integer rateSeq);

	// 方法

	/**
	 * 組成限額字串後儲存 並且 更改檢核狀態
	 * 
	 * @param mainId
	 *            文件編號
	 * @param pageNum
	 *            頁碼
	 * @param copyL140M01E
	 *            是否要組攤貸 字串
	 * @return String
	 */
	String saveL140m01bDscr1(String mainId, String pageNum, Boolean copyL140M01E);

	/**
	 * 組成聯貸字串後儲存 並且 更改檢核狀態
	 * 
	 * @param mainId
	 *            文件編號
	 * @param pageNum
	 *            頁碼
	 * @return String
	 */
	String saveL140m01bDscr1(String mainId, String pageNum, String mode,
			Boolean s);

	/**
	 * 轉換符號
	 * 
	 * @param word
	 *            JSONArray
	 * @return array
	 */
	String[] convertMark(String word);

	/**
	 * 查詢基準利率
	 * 
	 * @param type
	 *            利率的種類 6D - 本行基準利率 、6S-基準利率月指標利率 、QX-6165初級市場ＣＰ九十天期(KX)平均利率
	 * @param curr
	 *            幣別
	 * @return 幣別利率
	 */
	String queryBaseRate(String type, String curr);

	public Map<String, String> queryNPL(String cntrNo,
			List<L140M01E> l140m01es, String l140m01a_ownBrId)
			throws GWException;

	/**
	 * 取得額度明細表分行逾放比
	 * 
	 * @param ownBrIds
	 *            要引進的所有銀行列表
	 * 
	 * @return
	 */
	HashMap<String, Map<String, Object>> queryLnf226(
			HashMap<String, String> ownBrIds);

	/**
	 * 找出這個簽報書底下的額度明細表 By custId 排序
	 * 
	 * @param mainId
	 *            文件編號
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return List<L140M01A>
	 */
	public List<L140M01A> findL140m01aListByL120m01cMainIdOrderByCust(
			String mainId, String caseType);

	/**
	 * 額度序號給號
	 * 
	 * @param ownBrId
	 *            分行代碼
	 * @param unitCode
	 *            借款人類別 0-DBU OBU-4,海外-5
	 * @param classCD
	 *            String
	 * @return Map<String, Object>
	 */
	Map<String, Object> queryLnsp0050(String ownBrId, String unitCode,
			String classCD);

	/**
	 * 搜尋額度明細表是否存在此額度序號
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重覆序號
	 * @return 額度明細表
	 */
	List<L140M01A> findL140m01aBycntrNo(String cntrNo, String custId,
			String dupNo);

	/**
	 * 根據oids 抓出所有額度明細表
	 * 
	 * @param oids
	 *            陣列
	 * @return List<L140M01A>
	 */
	public List<L140M01A> findL140m01aByOids(String[] oids);

	/**
	 * 額度明細表主要儲存
	 * 
	 * @param l140m01bs
	 *            額度明細表敘述檔
	 * @param l140m01es
	 *            攤貸行
	 * @param entity
	 *            額度明細表相關檔案
	 */
	public void saveMain(List<L140M01B> l140m01bs, List<L140M01E> l140m01es,
			GenericBean... entity);

	/**
	 * 複製額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書mainId
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * @param custOid
	 *            c120m01a 借款人主檔 oid
	 * @param oidList
	 *            要複製的額度明細表oid
	 * @param isNeedL140S01A
	 *            是否同時引進借保人
	 * @param isNeedL140M01O
	 *            是否同時引進擔保品
	 * @param isNeedL140M01B
	 *            是否同時引進其他敍做條件
	 */
	public void copyCntrdocFromOidList(String mainId, String caseType,
			String custOid, String[] oidList, Boolean isNeedL140S01A,
			Boolean isNeedL140M01O, Boolean isNeedL140M01B) throws CapException;

	/**
	 * 刪除多個額度明細表
	 * 
	 * @param list
	 *            要刪除的額度明細表集合
	 */
	void delL140m01aList(List<L140M01A> list);

	/**
	 * 刪除多個收復彙計數，並更改額度明細表檢核欄位
	 * 
	 * @param oids
	 *            收付彙計數的oid
	 * @param l140m01a
	 *            額度明細表主檔
	 */
	void delL140M01KByOids(String[] oids, L140M01A l140m01a);

	/**
	 * 限額控管
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @param showCntrNo
	 *            是否顯示額度序號 true 為呈主管時檢查 false 為 給號時檢查
	 * @return 限額控管的訊息 若不需限額控管 回傳 空白
	 * @throws CapMessageException
	 */

	public String gfnDB2ChkNeedControlByCntrDoc(L140M01A l140m01a,
			Boolean showCntrNo) throws CapMessageException;

	/**
	 * 儲存聯行攤貸比率
	 * 
	 * @param l140m01es
	 *            聯行攤貸比率
	 */
	public void savelistL140M01E(List<L140M01E> l140m01es);

	/**
	 * 儲存關聯檔list
	 */

	public void savelistL120M01C(List<L120M01C> l120m01cs);

	/**
	 * 引進擔保品
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @param oids
	 *            擔保品 C100M01 oid陣列
	 * @return
	 */
	public String inculdeL140M01O(String mainId, String[] oids)
			throws CapMessageException;

	/**
	 * 刪除擔保品
	 * 
	 * @param oids
	 *            L140M01O.oid 陣列
	 * @param mainId
	 *            額度明細表 mainId
	 * 
	 *            <pre>
	 * 	 return{
	 * 	 tips:是否已在產品中被引用 Y |N,
	 * 	 drc:擔保品組字
	 * 	 }
	 * </pre>
	 */
	public Map<String, String> deleteL140M01O(String[] oids, String mainId);

	/**
	 * 重新引進擔保品描述
	 * 
	 * @param mainId
	 *            額度明細表 mainId
	 * 
	 *            return 取得擔保品描述
	 */
	public String reloadCMSDesc(String mainId);

	/**
	 * 取得產品種類
	 * 
	 * @param isforShow
	 *            是否用於呈現
	 * @return 產品種類下拉選單
	 */
	public CapAjaxFormResult getC900M01A(boolean isforShow);

	/**
	 * 新增產品種類
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @return
	 */
	public L140S02A addL140S02A(String mainId);

	/**
	 * 設定額度明細表性質、和授信科目 將所有產品的性質變成一個集合
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * 
	 */
	public void setL140M01AproPerty(String mainId);

	/**
	 * 新增查核事項檔
	 * 
	 * @param mainId
	 *            簽報書mainId
	 * @return 查核事項檔
	 */
	public List<L140M04A> addL140M04As(String mainId);

	/**
	 * 計算期付金
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @param oid
	 *            產品種類oid
	 * @param loanAmt
	 *            動撥金額
	 * @param lnYear
	 *            授信期間-年
	 * @param lnMonth授信期間
	 *            -月
	 * @return <br/>
	 *         periodChk 是否超過1/3 薪水<br/>
	 *         periodAmt 期付金
	 */
	public JSONObject queryPeriod(String mainId, String oid,
			BigDecimal loanAmt, BigDecimal lnYear, BigDecimal lnMonth);

	/**
	 * 查詢購置房屋貸款
	 * 
	 * @param mainId
	 *            C102M01A.mainId
	 * @return 主檔
	 */
	public C102M01A findC102M01AByMainId(String mainId);

	/**
	 * 刪除 L140M01P
	 * 
	 * @param l140m01ps
	 */
	public void delL140M01P(List<L140M01P> l140m01ps);

	/**
	 * 儲存 L140M01P
	 * 
	 * @param l140m01ps
	 */
	public void saveL140M01P(List<L140M01P> l140m01ps);

	/**
	 * 刪除 L140M01e
	 * 
	 * @param l140m01es
	 */
	public void delL140M01E(List<L140M01E> l140m01es);

	/**
	 * 刪除 l140s02hs
	 * 
	 * @param l140s02hs
	 */
	public void delL140S02H(List<L140S02H> l140s02hs);

	/**
	 * 引進帳務額度明細表
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @param l140m01a
	 *            額度明細表
	 * @throws CapException
	 */
	public void reloadOld_L140M01A(L120M01A l120m01a, L140M01A l140m01a)
			throws CapException;

	/**
	 * 刪除產品種類相關檔案
	 * 
	 * @param l140s02a
	 *            產品種類檔
	 */
	public void delAboutL140S02A(L140S02A l140s02a);

	/**
	 * 新增描述檔
	 * 
	 * @param newMainId
	 *            額度明細表mainid
	 * @return
	 */
	public ArrayList<L140M01B> newL140M01Bs(String newMainId);

	/**
	 * 查詢借保人
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	public L140S01A findL140S01ABykey(String mainId, String custId,
			String dupNo, String custPos);

	/**
	 * 複製產品種類
	 * 
	 * @param l140s02a
	 *            產品種類檔
	 */
	public void copyL140S02A(L140S02A l140s02a);

	/**
	 * 取得擔保品描述
	 * 
	 * @param l140m01os
	 *            擔保品明細檔
	 * @param l140m01a
	 *            額度明細表主檔
	 * @return
	 */
	public String getCMSDesc(List<L140M01O> l140m01os, L140M01A l140m01a);

	/**
	 * 根據簽報書mainId 和額度序號 取得對應的額度明細表
	 * 
	 * @param caseMainId
	 * @param cntrNo
	 * @param itemType
	 * @return
	 */
	public L140M01A findL140M01AByL120m01cMainIdAndcntrNo(String caseMainId,
			String cntrNo, String itemType);

	/**
	 * 查詢重置成本的計算方法
	 */
	public String queryL140M01OrebuildCost(L140M01O l140m01o,
			boolean rebuild_cost_since_1090101);

	public void saveByRate(List<L140S02D> l140s02ds, GenericBean... entity);

	/**
	 * 儲存借保人檔
	 * 
	 * @param l140s01as
	 */
	public void saveL140S01As(List<L140S01A> l140s01as);

	/**
	 * 更新購置房屋擔保放款風險權數檢核表 姓名by mainids
	 * 
	 * @param mainids
	 * @param custNameMap
	 *            c120m01a 所有名字
	 */
	public void setC102M01ANameByMainIds(List<String> mainids,
			HashMap<String, String> custNameMap);

	/**
	 * 儲存產品種檔
	 * 
	 * @param l140s02as
	 */
	public void saveL140S02As(List<L140S02A> l140s02as);

	/**
	 * 刪除借保人
	 * 
	 * @param l140s01as
	 */
	public void delL140S01As(List<L140S01A> l140s01as);

	/**
	 * 重新引進描述
	 * 
	 * @param mainId
	 * @param inqJcic
	 * @return
	 */
	public String reloadCMSDescByInqJcic(String mainId, String inqJcic);

	/**
	 * 重新設定簽報書團貸序號
	 * 
	 * @param mainId
	 *            簽報書mainId
	 * @param parentCntrNo
	 *            團貸額度序號
	 */
	public void setL140M03AGrpCntrNo(String mainId, String parentCntrNo,
			String welfareCmte);

	/**
	 * 查詢配偶ID
	 * 
	 * @param caseMainId
	 *            簽報書mainid
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	C120S01D findC120s01dBykey(String caseMainId, String custId, String dupNo);

	/**
	 * 查詢貸償是否已經存在
	 * 
	 * @param mainId
	 * @param seq
	 * @param bankNo
	 * @param branchNo
	 * @param subACNo
	 * @return
	 */
	L140S02H findByUniqueKey(String mainId, Integer seq, String bankNo,
			String branchNo, String subACNo);

	/**
	 * 刪除長擔額度序號
	 * 
	 * @param l140s02b
	 *            長擔額度序號檔
	 */
	public void delL140S02Bs(List<L140S02B> l140s02bs);

	L140M01M findL140m01mByMainId(String mainId);

	L140M01Q findL140m01qByMainId(String mainId);
	
	/**
	 * 在簽報書的借款人頁籤,刪除借款人 <BR/>
	 * 更改L140M01A,L140S01A,L140S02A的值
	 */
	public void saveWhenDelCust(List<L140M01A> l140m01as, String[] custIdArr,
			String[] dupNoArr);

	/**
	 * 在額度明細表的「從債務人」頁籤,刪除「從債務人」 <BR/>
	 * 更改L140M01A,L140S01A,L140S02A的值
	 */
	public void saveWhenDelL140S01A(String caseMainId,
			Set<String> l140m01a_mainIdSet);

	/**
	 * 當模型版本變更<BR/>
	 * 更改L140M01A,L140S02A的值
	 */
	public void saveWhen_VarVer_Chg(String caseMainId, String[] custIdArr,
			String[] dupNoArr);

	/**
	 * 在「產品」選擇在 C120M01A 未勾選的評等，將回寫 C120M01A.MARKMODEL
	 * 
	 * @param l120m01a_mainId
	 * @param replaceMode
	 * @param working_l140m01a
	 */
	public String syncQuoteToC120M01A_markModel(String l120m01a_mainId,
			boolean replaceMode, L140M01A working_l140m01a);

	/**
	 * 依MAINID及SEQ找出單筆L140M01P
	 * 
	 * @param mainId
	 * @param seq
	 * @return
	 */
	public L140M01P findL140M01PByMainidSeq(String mainId, Integer seq);

	/**
	 * 儲存L140M01P
	 * 
	 * @param l140m01p
	 */
	public void saveL140M01P(L140M01P l140m01p);

	public List<L140S02A> findL140S02AOrderBySeq(String mainId);

	public L140S02D findL140s02dByUniqueKey(String mainId, Integer seq,
			Integer phase);

	public List<PTEAMAPP> filter_pteamapp_grpCntrNo(
			List<PTEAMAPP> pteamapp_list, String l120m01a_caseBrId);

	public void syncL120M01E(String l120m01a_mainId, String custId,
			String dupNo, Set<PTEAMAPP> toL120M01ESet);

	public boolean isL140S02AGradeInChooseList(String l120m01a_mainId,
			L140S02A l140s02a);

	public List<C900M01D> getAllC900M01d();

	public Map<String, String> getC900M01D_map();

	public C900M01D findC900m01dUniqueKey(String subjCode);

	public C900M01D findC900m01dUniqueKey02(String subjCode2);

	public List<C900M01F> findC900m01fListByMainIdAndlmtType(String mainId,
			String lmtType);

	public C900M01F findC900m01fUniqueKey(String mainId, String lmtType,
			Integer lmtSeq);

	public int findC900M01FByMainIdAndlmtTypeMax(String mainId, String lmtType);

	public C900M01F findC900m01fByOid(String oid);

	public void deleteC900M01FList(String[] oids);

	public List<C900M01F> findC900M01FByOids(String[] oids);

	/**
	 * 組成科目限額字串後儲存 並且 更改檢核狀態
	 * 
	 * @param mainId
	 * @param pageNum
	 * @return
	 */
	String saveL140m01bDscr2(String mainId, String pageNum, Boolean s);

	/**
	 * 額度明細表登錄科目向上、下設定序號
	 * 
	 * @param model
	 *            L140M01C
	 * @param upOrDown
	 *            向上=true 向下=false
	 * @return
	 */
	boolean changeSubjectSeq(C900M01F c900m01f, boolean upOrDown);

	public void creatOldC900M01FByProduceCase(MegaSSOUserDetails user,
			List<C900M01F> newc900m01fs, L140M01A oldL140m01a,
			L140M01A newL140m01a, Set<String> c900m01fMap);

	public void savelistC900M01F(List<C900M01F> newc900m01fs);

	public Map<String, BigDecimal> findC900M01GByGrpCntrNo(String grpCntrNo);

	public void setC900M01GData(String cntrNo, String grpcntrno,
			L140M01A l140m01a, String status, BigDecimal tAmt, String tCurr,
			Timestamp tDate);

	public String fmt_l140s01a_guaPercent(BigDecimal guaPercent);

	public String createCreditCardMembersLoanData(String c120m01aOid,
			String branchNo, boolean isSubj303, String year, String month,
			BigDecimal rate, String addOrMinus, BigDecimal baseRate,
			BigDecimal loadAmount, String rateType);

	public void update_cntrNo_nplData_L140RelateClass(String tabMainId,
			String cntrNo, String npl, String nplDate,
			Map<String, String> othParam_cntrNo_nplData)
			throws CapMessageException;

	public String createCntrNo_fast_prodKind_byParam(L120M01A l120m01a,
			L120M01I l120m01i, C120M01A c120m01a, String prodKind,
			String loanTP3, String branchNo, BigDecimal loadAmount,
			Integer year, Integer month, Map<String, String> othParam);

	public String get_l140s02a_rateDesc(L140S02C l140s02c,
			List<L140S02D> l140s02dList, Properties prop_CLS1151S01Page);

	public String get_l140s02a_payoffWay(L140S02E l140s02e);

	public BigDecimal get_l140s02d_baseRate(String code);

	public String get_l140s02d_baseDesc(L140S02D l140s02d,
			LinkedHashMap<String, String> rateBaseMap, L140S02C l140s02c,
			LinkedHashMap<String, String> rateUserType);

	public String get_l140s02d_baseDesc(L140S02D l140s02d,
			LinkedHashMap<String, String> rateBaseMap, L140S02C l140s02c,
			LinkedHashMap<String, String> rateUserType,
			HashMap<String, String> rateBy070);

	public String checkGroupLoanBuildCaseMasterAccount(L140M01L l140m01l,
			String useDeadline);

	public String l140m01y_valueDesc(L140M01Y l140m01y);

	public List<Map<String, Object>> getHousingReverseMortgagesDataList(
			List<L140M01A> l140m01aList);

	// J-109-0374_10702_B1002 Web e-Loan非青創80萬以下徵提保證人改為提示
	public boolean chklngeFlag_GandBldKind(String c100m01MainId);

	public C100M01 findC100M01ByOid(String CmsOid);

	public void saveL140M01MVersion(String l140m01a_mainId, String version);

	public Map<String, BigDecimal> checkHousingReverseMortgagesData(
			List<Map<String, Object>> checkList) throws CapMessageException;

	public void saveL140M01MCollateralLoanValue(
			List<Map<String, Object>> checkList,
			BigDecimal c100m01MaxLoanValue, BigDecimal sumAssureApplyAmt);

	public String checkIsSpecificMoneyTrustCollateral(List<L140M01O> l140m01os, String currentApplyCurr);

	public void saveL140S02M(String l140m01a_mainId, String landNo1,
			String landNo2, String landCity, String landArea, String landpart1,
			String landpart2, String landAddr);

	public JSONArray getCMSLand(List<L140M01O> l140m01os) throws CapException;

	public List<L140S02M> getL140S02M(String mainId);

	public boolean checklnType_33_34(String mainId);

	public List<Map<String, Object>> getRealEstateAgentInfo(
			String l140m01a_mainId);

	public boolean checkIntroductionSource(String l120m01a_mainId,
			C120M01A c120m01a, L140M01A l140m01a, List<L140S02A> l140s02as,
			List<String> pageEmpty01, List<String> pageCheckFail);

	public void saveC126M01BForIntroductionSource(L140M01A l140m01a,
			List<String> pageCheckFail);

	public void deleteC126M01BForIntroductionSource(String l140m01a_mainId);

	public Map<String, Object> queryIndustryTypeInfo(String code);

	public Set<String> getTipsByL140m01o(List<L140M01O> l140m01os);

	public BigDecimal computeMaxInitialLoanRatio(String l120m01a_mainId,
			String custId, String dupNo, String l140m01a_mainId,
			BigDecimal realEstateRatio);

	public String chcekIsDuplicateOfSigningTemplateType(String mainId,
			String bizCat);

	public Map<String, List<CodeType>> getTemplateContentMap(String bizCat);

	public List<L140S10A> findL140s10aByMainId(String l140m01a_mainId);

	public void changeSigningTemplateSequence(String mainId, String bizCat,
			String bizItem, String templateId, int seq, boolean upOrDown)
			throws CapMessageException;

	public void deleteSigningTemplateDataByTemplateId(String mainId,
			String bizCat, String bizItem, String l140s10cOid,
			String templateId, boolean isDefaultItem) throws CapException;

	public int findMaxSequeenceOfBizCatByMainId(String l140m01a_mainId);

	public int findMaxBizItemCodeBy(String l140m01a_mainId, String bizCat);

	int findMaxBizContentCodeBy(String l140m01a_mainId, String bizCat,
			String bizItem);

	void createL140s10c_with_customContent(String l140m01a_mainId,
			String bizCat, String bizItem, String content);

	void createL140s10b_with_customItem(String l140m01a_mainId, String bizCat,
			String bizItemDesc);

	void createL140s10b_with_defaultItem(String l140m01a_mainId, String bizCat);

	void createL140s10c_with_defultContent(String l140m01a_mainId, String bizCat);

	void createSigningTemplateData(String l140m01a_mainId, String bizCatType,
			String loanTpSname);

	List<Map<String, Object>> getTemplateItemAndContentGridData(
			String l140m01a_mainid, String bizCat);

	List<L140S10C> getL140s10cContentData(String l140m01a_mainid,
			String bizCatType, String bizCatItem);

	L140S10C getL140s10cContentData(String oid);

	void updateL140s01cContent(String oid, String content);

	String getTemplatePreviewString(String mainId) throws CapMessageException;

	void updateL140s10cBizItemDesc(String mainId, String bizCat,
			String bizItem, String bizItemDesc);

	public List<Map<String, Object>> queryYoungLoanList(String ownBrId,
			String custId);

	/**
	 * J-111-0636_05097_B1001 Web e-Loan企金授信配合私人銀行帳務與金控總部分行區隔解決方案討論會議結論辦理
	 * 
	 * @param l140m01a
	 * @return
	 */
	public String chkForBranch149(L120M01A l120m01a, List<L140S02A> l140s02as,
			L140M01A l140m01a);
	public List<L140S02H> getL140s02hObject(String mainId, String custId, String dupNo, String l140m01a_MainId,Integer seq,
											MegaSSOUserDetails user);

	public boolean isShowTipsMessageForSpecificMoneyTrust(L120M01A l120m01a);

	public List<Map<String, Object>> getByMortgageLoanAndNotChangedL140m01aCase(String caseMainId, String custId, String dupNo, String itemType);

	public void saveL140m01aLtvForNotChangedCase(String l140m01a_mainId, BigDecimal ltv);

	public String checkSubjCodeByIdentityForMrRateCode(boolean isCustIdBankMan, boolean isSpouseBankMan);

	public String checkMortgageRatioL140m01o(List<L140M01O> l140m01oList, C120S01E c120s01e);

	public Set<String> getMortgageRatioTipsByL140m01o(List<L140M01O> l140m01os, C120S01E c120s01e);
}