$(function(){
    //驗證readOnly狀態
    function checkReadonly(){
        var auth = (responseJSON ? responseJSON.Auth : {}); //權限
        if (auth.readOnly || responseJSON.mainDocStatus != "010") {
            return true;
        }
        return false;
    }
    // 畫面切換table 所需設定之資料 如無設定 則直接切換
    $.extend(window.tempSave, {
        handler: "lms1705m01formhandler",
        action: "tempSave",
        beforeCheck: function(){
            if (responseJSON.page == "01") {
                return $("#L170M01aForm").valid();
            }
            else {
                return true;
            }
        },
        sendData: function(){
            if (responseJSON.page == "01") {
                return $.extend($("#L170M01aForm").serializeData(), {
                    crdType: $('#crdType').val(),
                    grade: $('#grade').val(),
                    crdTypeMow: $('#crdTypeMow').val(),
                    mow: $('#mow').val(),
					excrdType: $('#excrdType').val(),
                    exgrade: $('#exgrade').val(),
                    excrdTypeMow: $('#excrdTypeMow').val(),
                    exmow: $('#exmow').val(),
                });
            }
            else 
                if (responseJSON.page == "02") {
                    return $("#lms1705S02Button1").serializeData();
                }
                else 
                    if (responseJSON.page == "03") {
                        // alert(JSON.stringify(responseJSON));
                        //return $("#L170M01cForm").serializeData();
                        
                        return $.extend($("#L170M01cForm").serializeData(), {
                            finCurr: $('#l170m01c_curr').val(),
                            finUnit: $('#l170m01c_unit').val(),
                        });
                    }
                    else 
                        if (responseJSON.page == "04") {
							return $("#L170M01dForm").serializeData();
                        }
                        else 
                            if (responseJSON.page == "05") {
                                //return $("#L170M01F1Form").serializeData();
                                var json = {};
                                var array = {};
                                array["L1705S05Form01"] = $("#L170M01F1Form").serializeData();
                                array["L1705S05Form02"] = $("#L170M01F2Form").serializeData();
                                mergeJSON(json, array.L1705S05Form01);
                                mergeJSON(json, array.L1705S05Form02);
                                return json;
                            }
        }
    });
    
    //合併多個serializeData JSON 成一個serializeData JSON
    function mergeJSON(json, array){
        for (var data in array) {
            json[data] = array[data];
        }
    }
    
    // 權限
    var auth = (responseJSON ? responseJSON.Auth : {});
    // $("#L170M01aForm").readOnlyChilds(auth.readOnly);
    if (responseJSON.mainDocStatus != "010" || auth.readOnly) {
        $(".doc-tabs").lockDoc();
        // $("#tabForm").readOnlyChilds(true)//.find("button").remove();
    }
    // $.log(JSON.stringify(responseJSON)+"1");
    
    
    setCloseConfirm(true);/* 設定關閉此畫面時要詢問 */
    $.form.init({
        formHandler: "lms1705m01formhandler",
        formPostData: {
            formAction: "queryL170m01a",
            mainId: responseJSON.mainId
        
        },
        loadSuccess: function(json){
            if (responseJSON.page == "01") {
                $.ajax({
                    type: "POST",
                    handler: "lms1705m01formhandler",
                    action: "queryL170m01d",
                    data: {
                        mainId: responseJSON.mainId
                    },
				}).done(function(responseData){
					$("#L170M01aForm").injectData(json);
					jsSelectIsExitItem($("#excrdType"),"XC");
					jsSelectIsExitItem($("#excrdTypeMow"),"XM");

					//信用評等 
					$("#crdType").val(json.crdType);
					//信用等級
					$("#grade").val(json.grade);
					//信用風險內部評等  
					$("#crdTypeMow").val(json.crdTypeMow);
					//信用風險內部評等等級
					$("#mow").val(json.mow);
					//未評等
					if ($("#crdType").val() == "NA" || $("#crdType").val() == "") {
					    $("#grade111").hide();
					}
					else {
					    $("#grade111").show();
					}
					//免辦
					if ($("#crdTypeMow").val() == "G" || $("#crdTypeMow").val() == "") {
					    $("#mow111").hide();
					}
					else {
					    $("#mow111").show();
					}
					//前次評等資料
					$("#excrdType").val(json.excrdType);
					$("#exgrade").val(json.exgrade);
					$("#excrdTypeMow").val(json.excrdTypeMow);
					$("#exmow").val(json.exmow);
					$("#excrdTypeCN").html(json.excrdTypeCN);
					$("#excrdTypeHiddenCN").val(json.excrdTypeHiddenCN);
					exCheck($("#excrdType"),$("#exgrade111"),$("#exgrade"));
					exCheck($("#excrdTypeMow"),$("#exmow111"),$("#exmow"));

					$("#crdTypeCN").html(json.crdTypeCN);
					$("#crdTypeHiddenCN").val(json.crdTypeHiddenCN);
				});// 第二個AJAX結束
            }
            else 
                if (responseJSON.page == "02") {
                    $("#totBal").html(json.totBal);
                    $("#totQuota").html(json.totQuota);
                }
                else 
                    if (responseJSON.page == "03") {
                        $("#ratioNo1").html(i18n.lms1705s03["L170M01c.ratioNo" + json.No1]);
                        $("#ratioNo2").html(i18n.lms1705s03["L170M01c.ratioNo" + json.No2]);
                        $("#ratioNo3").html(i18n.lms1705s03["L170M01c.ratioNo" + json.No3]);
                        $("#ratioNo4").html(i18n.lms1705s03["L170M01c.ratioNo" + json.No4]);
                        $("#curapplyfor").localSave();
                    }
                    else 
                        if (responseJSON.page == "04") {
                            $.ajax({
                                type: "POST",
                                handler: "lms1705m01formhandler",
                                action: "queryL170m01d",  
                                data: {
                                    mainId: responseJSON.mainId,
                                    page: responseJSON.page,
                                    txCode: responseJSON.txCode
                                },
							}).done(function(responseData){
								//responseData = JSON.parse(responseData);
								responseData.typeJson = JSON.parse(responseData.typeJson);
								if(json.rptId == ""){	//舊版
									createL170M01DData(responseData);
								} else {	//J-107-0128海外改格式
									createL170M01DDataV2(responseData); 
								}       
								if (responseJSON.mainDocStatus != "010" || auth.readOnly) {
								    $(".doc-tabs").lockDoc();
								// $("#tabForm").readOnlyChilds(true)//.find("button").remove();
								}

								// J-108-0260登錄檢視表 按鈕都要show
								$('#InputChklist').show();
							});// 第二個AJAX結束
                        }
                        else 
                            if (responseJSON.page == "05") {
                                if ($("[name=conFlag]:checked").val() == "2") 
                                    $("#condition").prop("disable", true);
                                else 
                                    $("#condition").prop("disable", false);
                            }
                            else 
                                if (responseJSON.page == "06") {
                                    if ($("#id").val() != "") 
                                        $("#id").readOnly(true);
                                    else 
                                        $("#id").readOnly(false);
                                }
        }
    });
    
    function createL170M01DData(responseData){
        var typeJson = responseData.typeJson;
        var table = $("#L170M01dForm").find("#tableXXX");
        var array = responseData.L170M01DArray;
        // 後端塞typeJson的值有哪幾種
        for (var type in typeJson) {
            var count1 = 0;
            if (type == "A") {
                var text = i18n.lms1705m01["L170M01a.titleA"];
            }
            else 
                if (type == "B") {
                    var text = i18n.lms1705m01["L170M01a.titleB"];
                }
            // 覆審項次(itemNo)有幾筆
            var len = typeJson[type];
            for (var i = 0; i < array.length; i++) {
                var json = array[i];
                var text3 = "";
                var tex2 = "";
                if (json.itemNo == "A002") {
                    tex2 += "(";
                    tex2 += "<label><input type='checkbox' id='chkCheck' name='chkCheck' value='N' />";
                    tex2 += i18n.lms1705m01["L170M01a.info5"];
                    tex2 += "</label>)";
                    tex2 += i18n.lms1705m01["L170M01a.info6"];
                }
                else 
                    if (json.itemNo == "B003") {
                        tex2 += "<span class='text-red'>＊";
                        tex2 += i18n.lms1705m01["L170M01a.info1"];
                        tex2 += "</span>";
                    }
                    else 
                        if (json.itemNo == "B008") {
                            tex2 += "<span class='text-red'>＊";
                            tex2 += i18n.lms1705m01["L170M01a.info2"];
                            tex2 += "</span>";
                        }
                        else 
                            if (json.itemNo == "B014") {
                                tex2 += "<span class='text-red'>＊";
                                tex2 += i18n.lms1705m01["L170M01a.info3"];
                                tex2 += "</span>";
                            }
                            else 
                                if (json.itemNo == "B015") {
                                    tex2 += "<div id='show1'><span class='text-red'>＊";
                                    tex2 += i18n.lms1705m01["L170M01a.info4"];
                                    tex2 += " :<label><input id='chkPreReview' name='chkPreReview' type='radio' value='Y' /><span class='style999'>";
                                    tex2 += i18n.lms1705m01["L170M01a.y1"];
                                    tex2 += " </label><label><input id='chkPreReview' name='chkPreReview' type='radio' value='N' /><span class='style999'>";
                                    tex2 += i18n.lms1705m01["L170M01a.n1"];
                                    tex2 += "</label></span></div>";
                                }
                
                if (json.itemType == type) {
                
                    var str = "";
                    str = "<tr valign='top' " + " id='" + DOMPurify.sanitize(json.itemNo) + "'>";
                    count1++;
                    if (count1 == 1) {
                    
                        // 當每一種覆審項次的第一筆才增加
                        str += "<td rowspan='" + DOMPurify.sanitize(String(len)) + "' align='center' valign='middle' class='hd1 style42' style='text-align:center' width='10%'>";
                        str += text;
                        str += "</td>";
                    }
                    if (count1 == len) {
                        // 當每一種覆審項次的的筆數都跑完count歸零
                        count1 = 0;
                    }
                    
                    str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + DOMPurify.sanitize(json.itemNo) + "' />" + DOMPurify.sanitize(String(json.itemSeq)) + "</td>";
                    str += " <td width='25%'>" + DOMPurify.sanitize(json.itemContent) + "</td>";
                    str += " <td width='15%'>";
                    if (json.itemNo == "B015") {
                        str += "<label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='N' /><span class='style999'>";
                        str += i18n.lms1705m01["L170M01a.y2"];
                        str += "</label><label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='Y' /><span class='style999'>";
                        str += i18n.lms1705m01["L170M01a.have"];
                        str += "</label><label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='K' /><span class='style999'>";
                        str += i18n.lms1705m01["L170M01a.an"];
                        str += "</label></td>";
                    }
                    else 
                    	if (json.itemNo == "B018") {
                            str += " <label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='N' /><span class='style999'>";
                            str += i18n.lms1705m01["L170M01a.n"];
                            str += "</label><label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='Y' /><span class='style999'>";
                            str += i18n.lms1705m01["L170M01a.y"];
                            str += "</label><label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='K' /><span class='style999'>";
                            str += i18n.lms1705m01["L170M01a.an"];
                            str += "</label></td>";
                        }
                        else {
                            str += " <label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='Y' /><span class='style999'>";
                            str += i18n.lms1705m01["L170M01a.y"];
                            str += "</label><label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='N' /><span class='style999'>";
                            str += i18n.lms1705m01["L170M01a.n"];
                            str += "</label><label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='K' /><span class='style999'>";
                            str += i18n.lms1705m01["L170M01a.an"];
                            str += "</label></td>";
                        }
                    
                    str += "<td width='27%'>";
                    str += tex2;
                    // 前次覆審有無應行改善事項？(引藏)
                    if (json.itemNo == "B015") {
                        str += "<div id='show2'><span class='style44'><input type='text' id='chkText" + DOMPurify.sanitize(json.itemNo) + "' name='chkText" + DOMPurify.sanitize(json.itemNo) + "' size='40' maxlengthC='64' /></span><div>";
                    }
                    else {
                        str += "<span class='style44'><input type='text' id='chkText" + DOMPurify.sanitize(json.itemNo) + "' name='chkText" + DOMPurify.sanitize(json.itemNo) + "' size='40'maxlengthC='64' /></span>";
                    }
                    str += " </td>";
                    str += " </tr>";
                    
                    table.append(str);
                    
                    var uuu = json.chkResult;
                    var yyy1 = json.chkText;
                    var yyy2 = json.chkCheck;
                    var yyy3 = json.chkPreReview;
                    
                    $("input[type='radio'][name='chkResult" + json.itemNo + "'][value='" + uuu + "']").prop("checked", true);
                    $("input[type='radio'][name='chkPreReview'][value='" + yyy3 + "']").prop("checked", true);
                    $("input[type='checkbox'][name='chkCheck'][value='" + yyy2 + "']").prop("checked", true);
                    $("[name='chkText" + encodeURI(json.itemNo) + "']").val(yyy1);  // 掃到高風險
                    if ($("input[name=chkResultB015]:checked").val() == "N") {
                        $("#show1").hide();
                        $("#show2").hide();
                    }
                    else {
                        $("#show1").show();
                        $("#show2").show();
                        
                    }
                    
                } // End itemType
                // $("body").append(str);
            }// END len的if
        }// END ARRAY
        // 前次覆審有無應行改善事項？ 
        $("input[name=chkResultB015]").click(function(){
            //alert("!!!!");
            if ($(this).val() == "N") {
                $("#show1").hide();
                $("#show2").hide();
            }
            else {
                $("#show1").show();
                $("#show2").show();
                
            }
        });// input[name=chkResult19] END
    }
    
    // 儲存按鈕
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        saveData(true);
    }).end().find("#btnDelete").click(function(){
        $.ajax({
            type: "POST",
            handler: "lms1705m01formhandler",
            data: {
                formAction: "deleteListL170m01a",
                mainOid: $("#oid").val()
            }
        
        });//移授檢單位登錄
    }).end().find("#btnRemover").click(function(){
        if (checkReadonly()) {
            btnRemover();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforeSend"], function(b){
                if (b) {
                    saveData(false, btnRemover);
                }
            });
        }
        
    }).end().find("#btnAccept").click(function(){
        flowAction({
            flowAction: true
        });
    }).end().find("#btnReturn").click(function(){
        flowAction({
            flowAction: false
        });
    }).end().find("#btnPrint").click(function(){
        if (checkReadonly()) {
            printAction();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    saveData(false, printAction);
                }
            });
        }
    }).end().find("#btnSend").click(function(){//呈主管覆核
        if (checkReadonly()) {
            sendBoss();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforeSend"], function(b){
                if (b) {
                    saveData(false, sendBoss);
                }
            });
        }
        
    }).end().find("#btnCheck").click(function(){
        // 待覆核 - 覆核
        openCheck();
    }).end().find("#btnReturn").click(function(){
    }).end().find("#btnComplete").click(function(){ //編製完成
        openCheckComplete();
    }).end().find("#btnSend4").click(function(){
        $.ajax({
            handler: "lms1705m01formhandler",
            data: {
                formAction: "querylms1705s05BOX",
                mainId: responseJSON.mainId
            },
		}).done(function(json){
			$("#lms1705s05BOXtext").val(json.branchComm);
			$("#lms1705SENT").thickbox({
			    // 登錄受檢單位洽辦情形，登錄完成後請按編製完成
			    title: i18n.lms1705m01['lms1705.tit06'],
			    width: 450,
			    height: 200,
			    align: 'center',
			    valign: 'bottom',
			    modal: false,
			    i18n: i18n.def,
			    buttons: {
			        "sure": function(showMsg){
			            if ($("#lms1705s05BOX").valid()) {
			                $.ajax({
			                    handler: "lms1705m01formhandler",
			                    type: "POST",
			                    dataType: "json",
			                    data: $.extend($("#lms1705s05BOX").serializeData(), {
			                        formAction: "save1705M5",
			                        mainId: responseJSON.mainId,
			                        mainOid: responseJSON.oid,
			                        page: 5,
			                        showMsg: true,
			                        txCode: responseJSON.txCode
			                    }),
							}).done(function(responseData){
								$("#lms1705s05BOX").injectData(responseData);
								if ($("#L170M01F2Form")) {
								    $("#L170M01F2Form").injectData(responseData);
								}
							});
			                $.thickbox.close();
			            }
			            
			        },
			        "cancel": function(){
			            $.thickbox.close();
			        }
			    }
			});
		});
    });
    
    function btnRemover(){
        $.ajax({
            type: "POST",
            handler: "lms1705m01formhandler",
            data: {
                formAction: "checkSendBoss",
                mainId: responseJSON.mainId,
                page: responseJSON.page,
                crdType: $('#crdType').val(),
                grade: $('#grade').val(),
                crdTypeMow: $('#crdTypeMow').val(),
                mow: $('#mow').val(),
				excrdType: $('#excrdType').val(),
                exgrade: $('#exgrade').val(),
                excrdTypeMow: $('#excrdTypeMow').val(),
                exmow: $('#exmow').val(),
                docCode: responseJSON.docCode,
                docType: responseJSON.docType
            },
		}).done(function(responseData){
			if (responseData.WARNCODE) {
			    CommonAPI.showErrorMessage(responseData.WARNCODE);
			    //            		CommonAPI.confirmMessage(responseData.WARNCODE, function(b){
			    //                        if (b) {
			    //                        }
			    //            		})
			}
			else {
			    if (responseData.check) {
			        CommonAPI.confirmMessage(i18n.def["confirmSend"], function(b){
			            if (b) {
			                flowAction($.extend($("#tabForm").serializeData(), {
			                    page: responseJSON.page,
			                    saveData: true
			                }));
			            }
			        })
			    }// CHECK END
			}
		});// success 檢核結束
    }
    
    function printAction(){
        gridPrint.trigger("reloadGrid");
        $("#printView").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.def['print'],
            width: 700,
            height: 450,
            readOnly: false,
            modal: true,
            buttons: (function(){
                var btn = {};
                btn[i18n.def['print']] = function(){
                    var count = 0;
                    var content = "";
                    var id = gridPrint.getGridParam('selarrrow');
                    for (var i = 0; i < id.length; i++) {
                        if (id[i] != "") {
                            var datas = gridPrint.getRowData(id[i]);
                            content = content + datas.rpt + "^" + datas.oid + "|";
                            count++;
                        }
                    }
                    if (content.length != 0) {
                        content = content.substring(0, content.length - 1);
                    }
                    if (count == 0) {
                        CommonAPI.showMessage(i18n.def['grid.selrow']);
                    }
                    else {
                        $.form.submit({
                            url: "../../simple/FileProcessingService",
                            target: "_blank",
                            data: {
                                mainId: responseJSON.mainId,
                                rptOid: content,
                                fileDownloadName: "lms1705r01.pdf",
                                serviceName: "lms1705r01rptservice"
                            }
                        });
                    }
                }
                btn[i18n.def['close']] = function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
                return btn;
            })()
        });
    }
    function saveData(showMsg, tofn){
        if (responseJSON.page == "01") {
            if ($("#L170M01aForm").valid()) {
                if ($("#crdType").val() == "" || $("#crdType").val() == "NA") {
                    $("#grade").val('');
                }
                $.ajax({
                    type: "POST",
                    handler: "lms1705m01formhandler",
                    data: {
                        formAction: "checkSend",
                        mainId: responseJSON.mainId,
                        page: responseJSON.page,
                        crdTypeCN: $('#crdTypeHiddenCN').val(),
                        crdType: $('#crdType').val(),
                        grade: $('#grade').val(),
                        crdTypeMow: $('#crdTypeMow').val(),
                        mow: $('#mow').val(),
						excrdType: $('#excrdType').val(),
                        exgrade: $('#exgrade').val(),
                        excrdTypeMow: $('#excrdTypeMow').val(),
                        exmow: $('#exmow').val(),
                        docCode: responseJSON.docCode,
                        docType: responseJSON.docType,
                    },
				}).done(function(responseData){
					if (responseData.checkDate) {
					    return CommonAPI.showErrorMessage(i18n.lms1705m01["lms1705.message1"]);
					}
					if (responseData.check) {
					    $.ajax({
					        type: "POST",
					        handler: "lms1705m01formhandler",
					        action: "saveAll",
					        data: {
					            mainId: responseJSON.mainId,
					            showMsg: showMsg,
					            page: responseJSON.page,
					            crdType: $('#crdType').val(),
					            grade: $('#grade').val(),
					            crdTypeMow: $('#crdTypeMow').val(),
					            mow: $('#mow').val(),
								excrdType: $('#excrdType').val(),
								exgrade: $('#exgrade').val(),
								excrdTypeMow: $('#excrdTypeMow').val(),
								exmow: $('#exmow').val(),
					            sign: ",",
					            crdTypeHiddenCN: $('#crdTypeHiddenCN').val(),
								excrdTypeHiddenCN: $('#excrdTypeHiddenCN').val(),
					            txCode: responseJSON.txCode,
					        },
						}).done(function(responseData){
							$('#L170M01aForm').injectData(responseData.L170M01aForm);
							$('#docStatus').val(responseData.docStatus);
							$('#appraiser').val(responseData.appraiser);
							$('#randomCode').val(responseData.randomCode);
							$('#updater').val(responseData.updater);
							$('#updateTime').val(responseData.updateTime);
							$('#createTime').val(responseData.createTime);
							$('#typCd').val(responseData.typCd);
							$('#staffJobL1').val(responseData.staffJobL1);
							CommonAPI.triggerOpener("gridview", "reloadGrid");
							//執行列印
							if (!showMsg && tofn) {
							    tofn();
							}
						});// (2) AJAX
					}// CHECK END  
				});// (1) AJAX
            }
        }
        else 
            if (responseJSON.page == "03") {
                if ($("#L170M01cForm").valid()) {
                    if ($("#fromDate1").val().replace(/\-/g, "") > $("#endDate1").val().replace(/\-/g, "")) {
                        CommonAPI.showMessage(i18n.lms1705m01["L170M01c.errMsg01"]);
                    }
                    else 
                        if ($("#fromDate2").val().replace(/\-/g, "") > $("#endDate2").val().replace(/\-/g, "")) {
                            CommonAPI.showMessage(i18n.lms1705m01["L170M01c.errMsg01"]);
                        }
                        else 
                            if ($("#fromDate3").val().replace(/\-/g, "") > $("#endDate3").val().replace(/\-/g, "")) {
                                CommonAPI.showMessage(i18n.lms1705m01["L170M01c.errMsg01"]);
                            }
                            else {
                            
                                $.ajax({
                                    type: "POST",
                                    handler: "lms1705m01formhandler",
                                    action: "saveAll",
                                    data: {
                                        mainId: responseJSON.mainId,
                                        page: responseJSON.page,
                                        showMsg: showMsg,
                                        txCode: responseJSON.txCode,
                                        ratioNo1: $("#No1").val(),
                                        ratioNo2: $("#No2").val(),
                                        ratioNo3: $("#No3").val(),
                                        ratioNo4: $("#No4").val(),
                                        finCurr: $("#l170m01c_curr").val(),
                                        finUnit: $("#l170m01c_unit").val(),
                                    },
								}).done(function(responseData){
									$('#L170M01cForm').injectData(responseData);
									var ratioNo1 = responseData.No1;
									var ratioNo2 = responseData.No2;
									var ratioNo3 = responseData.No3;
									var ratioNo4 = responseData.No4;

									$("#ratioNo1").html(i18n.lms1705s03["L170M01c.ratioNo" + ratioNo1]);
									$("#ratioNo2").html(i18n.lms1705s03["L170M01c.ratioNo" + ratioNo2]);
									$("#ratioNo3").html(i18n.lms1705s03["L170M01c.ratioNo" + ratioNo3]);
									$("#ratioNo4").html(i18n.lms1705s03["L170M01c.ratioNo" + ratioNo4]);

									//執行列印
									if (!showMsg && tofn) {
									    tofn();
									}
								});  // AJAX
                            }
                }
            }
            else 
                if (responseJSON.page == "04") {
                    //如果有勾就要存無
                    var chkChecks = true;
                    if ($("#chkCheck").prop("checked")) {
                        chkChecks = false;
                    }
                    $.ajax({
                        type: "POST",
                        handler: "lms1705m01formhandler",
                        action: "saveAll",
                        data: {
                            mainId: responseJSON.mainId,
                            page: responseJSON.page,
                            showMsg: showMsg,
                            txCode: responseJSON.txCode,
                            chkCheck: chkChecks,
                            chkPreReview: $("input[name=chkPreReview]:checked").val(),
                        },
					}).done(function(responseData){
						$('#L170M01dForm').injectData(responseData.L170M01dForm);
						$('#totBalCurr').val(responseData.totBalCurr);
						CommonAPI.triggerOpener("gridview", "reloadGrid");
						//執行列印
						if (!showMsg && tofn) {
						    tofn();
						}
					})//AJAX
                }
                else 
                    if (responseJSON.page == "05") {
                        if ($("#L170M01F1Form").valid()) {
                            $.ajax({
                                type: "POST",
                                handler: "lms1705m01formhandler",
                                action: "saveAll",
                                data: {
                                    mainId: responseJSON.mainId,
                                    page: responseJSON.page,
                                    showMsg: showMsg,
                                    txCode: responseJSON.txCode,
                                },
							}).done(function(responseData){
								$('#L170M01dForm').injectData(responseData.L170M01dForm);
								CommonAPI.triggerOpener("gridview", "reloadGrid");
								//執行列印
								if (!showMsg && tofn) {
								    tofn();
								}
							});//AJAX
                        }
                    }
                    else {
                        $.ajax({
                            type: "POST",
                            handler: "lms1705m01formhandler",
                            action: "saveAll",
                            data: {
                                mainId: responseJSON.mainId,
                                page: responseJSON.page,
                                showMsg: showMsg,
                                txCode: responseJSON.txCode
                            
                            },
						}).done(function(responseData){
							$('#L170M01dForm').injectData(responseData.L170M01dForm);
							$('#totBalCurr').val(responseData.totBalCurr);
							CommonAPI.triggerOpener("gridview", "reloadGrid");
							//執行列印
							if (!showMsg && tofn) {
							    tofn();
							}
						});//AJAX
                    }
    }
    
    
    var gridPrint = $("#printGrid").iGrid({
        handler: 'lms1705gridhandler',
        height: 270,
        rownumbers: true,
        multiselect: true,
        hideMultiselect: false,
        
        postData: {
            formAction: "queryPrint"
        },
        colModel: [{
            colHeader: i18n.lms1705m01['print.custName'],// "借款人名稱",
            name: 'custName',
            width: 120,
            sortable: true
        }, {
            colHeader: i18n.lms1705m01['print.rptNo'],// "報表編號",
            name: 'rptNo',
            align: "center",
            width: 40,
            sortable: true
        }, {
            colHeader: i18n.lms1705m01['print.rptName'],// "報表名稱",
            name: 'rptName',
            width: 70,
            sortable: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "rpt",
            name: 'rpt',
            hidden: true
        }]
    });
    // ==============================================================================================================
    // 編製完成
    function openCheckComplete(){
        $.ajax({
            type: "POST",
            handler: "lms1705m01formhandler",
            data: {
                formAction: "checkSendBoss",
                mainId: responseJSON.mainId,
                page: responseJSON.page,
                crdType: $('#crdType').val(),
                grade: $('#grade').val(),
                crdTypeMow: $('#crdTypeMow').val(),
                mow: $('#mow').val(),
				excrdType: $('#excrdType').val(),
                exgrade: $('#exgrade').val(),
                excrdTypeMow: $('#excrdTypeMow').val(),
                exmow: $('#exmow').val(),
                docCode: responseJSON.docCode,
                docType: responseJSON.docType,
                checkBranchComm: "Y"
            },  //AJAX
		}).done(function(responseData){
			if (responseData.WARNCODE) {
			    CommonAPI.showErrorMessage(responseData.WARNCODE);
			}
			else {
			    if (responseData.check) {
			        // 核定
			        flowAction($.extend($("#tabForm").serializeData(), {
			            page: responseJSON.page,
			            saveData: true,
			            flowSeq: "2",
			            flowActionResult: "Y",
			            checkFlowUpdaterResult: "N"
			        }));
			    }// CHECK END
			}
		});// success 檢核結束
    }
    
    // 待覆核 - 覆核
    function openCheck(){
        $("#openCheckBox").thickbox({
            // lms1705.tit07=覆核
            title: i18n.lms1705m01['lms1705.tit07'],
            width: 100,
            height: 150,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        // lms1705.check=請選擇
                        return CommonAPI.showMessage(i18n.lms1705m01['lms1705.check']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            // 一般退回到編製中01O
                            // lms1705.check1=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.lms1705m01['lms1705.check1'], function(b){
                                if (b) {
                                    flowAction({
                                        flowSeq: "4",
                                        flowActionResult: "N"
                                    });
                                }
                            });
                            
                            break;
                        case "2":
                            // 核定
                            // lms1705.check2=該案件是否核准？確定請按【確定】，否則請按【取消】離開
                            CommonAPI.confirmMessage(i18n.lms1705m01["lms1705.check2"], function(b){
                                if (b) {
                                    flowAction({
                                        flowSeq: "4",
                                        flowActionResult: "Y"
                                    });
                                }
                            });
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    //=======================================================================================
    //呈主管 -  編製中
    function sendBoss(){
        // 掃到高風險 $("#selectBossForm")
        var selectBossForm = $("#selectBossForm");
        $("#numPerson").removeAttr("disabled");
        $.ajax({
            handler: "lms1705m01formhandler",
            data: {
                formAction: "queryBoss"
            },
		}).done(function(json){
			// 掃到高風險 $(".boss").setItems({
			selectBossForm.find(".boss").setItems({
			    item: json.bossList
			});
			//L141M01A.btn04=呈主管覆核
			CommonAPI.confirmMessage(i18n.def["confirmApply"], function(b){
			    if (b) {
			        // 掃到高風險 $("#selectBossForm").find("select").removeAttr("disabled");
			        selectBossForm.find("select").removeAttr("disabled");
			        $("#selectBossBox").thickbox({ // 使用選取的內容進行彈窗
			            //L141M01A.btn04=呈主管覆核
			            title: i18n.lms1705m01['L170M01A.btn04'],
			            width: 350,
			            height: 150,
			            modal: true,
			            valign: "bottom",
			            align: "center",
			            readOnly: false,
			            i18n: i18n.def,
			            buttons: {
			                "sure": function(){
			                    var $selectBossForm = $("#selectBossForm");
			                    var managerMainId = $selectBossForm.find("select#manager").val();
			                    if (!managerMainId || managerMainId == "") {
			                        //L141M01A.title010=單位/授權主管
			                        return CommonAPI.showErrorMessage(i18n.lms1705m01['L170M01A.error3'] + i18n.lms1705m01['L170M01A.managerId']);
			                    }
			                    flowAction({
			                        page: responseJSON.page,
			                        saveData: true,
			                        managerId: managerMainId
			                    });
			                    $.thickbox.close();
			                    
			                },
			                "cancel": function(){
			                    $.thickbox.close();
			                }
			            }
			        });
			    }
			});
		});
    }
    // ========================================================================================
    /** 流程動作   */
    function flowAction(sendData){
        $.ajax({
            type: "POST",
            handler: "lms1705m01formhandler",
            data: $.extend({
                formAction: "flowAction",
                oid: responseJSON.mainOid,
                mainOid: responseJSON.mainOid,
                txCode: responseJSON.txCode
            }, (sendData || {})),
		}).done(function(){
			CommonAPI.triggerOpener("gridview", "reloadGrid");
			 setCloseConfirm(false);
			 window.close();
		})
    }
    
    // 變更為最新版本
    $('#svnToLatest').click(function(){
        $.ajax({
            type: "POST",
            handler: "lms1705m01formhandler",
            action: "queryL170m01d",
            data: {
                mainId: responseJSON.mainId,
                page: responseJSON.page,
                txCode: responseJSON.txCode
            },
		}).done(function(responseData){
			var array = responseData.L170M01DArray;
			for (var i = 0; i < array.length; i++) {
			    var json = array[i];
			    // 掃到高風險
			    $("#" + encodeURI(json.itemNo)).remove();                    
			}
			$.ajax({
			    type: "POST",
			    handler: "lms1705m01formhandler",
			    action: "queryL170m01d",
			    data: {
			        mainId: responseJSON.mainId,
			        page: responseJSON.page,
			        txCode: responseJSON.txCode,
			        deleteData: "Y"
			    },
			}).done(function(responseData2){
				responseData2.typeJson = JSON.parse(responseData2.typeJson);
				createL170M01DDataV2(responseData2);
			});
		})
    })

    // J-108-0260登錄檢視表 - 「債權確保」第5、6、21、23、24及25項
    $('#InputChklist').click(function(){
        var $form = $("#chklistForm");
        $form.reset();
        $("#chklistBox").thickbox({
            title: i18n.lms1705m01['L170M01I.title'],
            width: 800,
            height: 800,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            open : function() {
                $(this).find("#chklistForm").reset();
                $.ajax({
                    handler: "lms1705m01formhandler",
                    action: 'queryL170m01i',
                    data: {
                        mainId: responseJSON.mainId
                    },
				}).done(function(obj){
					$form.injectData(obj);
					if(obj.receNA == "Y"){
					    $("input[name='receNA'][value='Y']").prop("checked", true);
					}
					if(obj.valRepNA == "Y"){
					    $("input[name='valRepNA'][value='Y']").prop("checked", true);
					}
					if(obj.mtgNA == "Y"){
					    $("input[name='mtgNA'][value='Y']").prop("checked", true);
					}
					if(obj.insNA == "Y"){
					    $("input[name='insNA'][value='Y']").prop("checked", true);
					}
				})
            },
            buttons: {
                "saveData": function(){
                    if ($form.valid()) {
	                    $.ajax({
	                        handler: "lms1705m01formhandler",
	                        action: 'saveL170m01i',
	                        data: $.extend($("#chklistForm").serializeData(), {
	                            mainId: responseJSON.mainId,
	                        }),
						}).done(function(obj){
							if (obj.msg && obj.msg != "") {
							    CommonAPI.showErrorMessage("", obj.msg);
							}
						})
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    })

    /*----------------------------------------- 更新覆審控制檔 ---------------------------------------*/
    $("#_lms1705s05ButtonUPDATE").click(function update412(){
        if (checkReadonly()) {
            updateData();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforeSend"], function(b){
                if (b) {
                    saveData(false, updateData);
                }
            });
        }
        
    });
    
    function updateData(){
        var dataCheck = "";
        if ($("#upDate").val() != '') {
            CommonAPI.confirmMessage(i18n.lms1705m01["L170M01A.check03"], function(b){
                if (b) {
                    updateElf412Sure();
                }
            });
        }
        else {
            updateElf412Sure();
        }
    }
    
    function updateElf412Sure(){
        // 更新覆審控制檔 
        $.ajax({
            handler: "lms1705m01formhandler",
            type: "POST",
            dataType: "json",
            action: "updateElf412",
            data: {
                //  formAction: "updateElf412",
                oid: responseJSON.oid,
                mainId: responseJSON.mainId,
                dupNo: responseJSON.dupNo,
                custId: responseJSON.custId,
                txCode: responseJSON.txCode
            },
		}).done(function(responseData){
			//           		 alert(JSON.stringify(responseData));
			var dataCheck = responseData.check;
			if (dataCheck == "Y") {
			    $("#L170M01F1Form").injectData(responseData);
			    $("#upDate").val(responseData.upDate);
			    CommonAPI.triggerOpener("gridview", "reloadGrid");
			}
		})
    }
    
    //=======================================================================================
  //J-107-0128海外改格式  新增版本資訊 	2018海外新版覆審報告表
    function createL170M01DDataV2(responseData){
        var typeJson = responseData.typeJson;
        var table = $("#L170M01dForm").find("#tableXXX");
        var array = responseData.L170M01DArray;
    	var rptid = responseData.rptId;	//代入報表版本
    	$("input[type='hidden'][name='rptid']").val(rptid);
    	var country = responseData.Country;	//國別	CA英文與他國不同
    	var locale = userInfo.userLocale;	//語系
    	var realRpFg = responseData.realRpFg;	//是否為實際覆審報告(土建融)
    	
        var arrayZ = new Array();
        var arrayY = new Array();
        var arrayX = new Array();
    	for (var i = 0; i < array.length; i++) {
			if(array[i].itemType == "Z"){
				arrayZ.push(array[i]);
			} else if(array[i].itemType == "Y"){
				arrayY.push(array[i]);
			} else if(array[i].itemType == "X"){
				arrayX.push(array[i]);
			}
    	}
    	
    	// 後端塞typeJson的值有哪幾種
        for (var type in typeJson) {
            var count1 = 0;
            var text = "";
            if (type == "A") {
            	if(locale == "en"){
            		text = (country == "CA") ? i18n.lms1705m01["L170M01aV2_CA.titleA"] : i18n.lms1705m01["L170M01aV2.titleA"];
            	} else {
            		text = i18n.lms1705m01["L170M01aV2.titleA"];
            	}
            } else if (type == "B") {
            	if(locale == "en"){
            		text = (country == "CA") ? i18n.lms1705m01["L170M01aV2_CA.titleB"] : i18n.lms1705m01["L170M01aV2.titleB"];
            	} else {
            		text = i18n.lms1705m01["L170M01aV2.titleB"];
            	}
            } else if (type == "C") {
            	text = i18n.lms1705m01["L170M01aV2.titleC"];
            }
            
            // 覆審項次(itemNo)有幾筆
            var len;
            if(type == "B") { 
            	var Z,Y,X;
            	Z = (typeJson.Z != undefined) ? typeJson.Z : 0;
            	Y = (typeJson.Y != undefined) ? typeJson.Y : 0;
            	X = (typeJson.X != undefined) ? typeJson.X : 0;
            	if(realRpFg == "N"){	//土建融	否-->不用輸入第11項附表 & B008
            		len = Number(typeJson[type])+Number(Y)+Number(X)-1;
            	} else {
                	len = Number(typeJson[type])+Number(Z)+Number(Y)+Number(X);
            	}
           	
            } else {
            	len = typeJson[type];
            }            
            for (var i = 0; i < array.length; i++) {
                var json = array[i];
                var text3 = "";
                var tex2 = "";
                if (json.itemNo == "A002") {
                    // J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
                    var noStr = i18n.lms1705m01["L170M01a.info5"];
                    if (responseData.rptId >= "Ver202210") {
                        if(locale == "en"){
                            if(country == "CA"){
                                noStr = i18n.lms1705m01["L170M01a.v4info5_CA"];
                            } else {
                                noStr = i18n.lms1705m01["L170M01a.v4info5"];
                            }
                        }
                    }
                    tex2 += "<span>(";
                    tex2 += "<label><input type='checkbox' id='chkCheck' name='chkCheck' value='N' />";
                    tex2 += noStr;
                    tex2 += "</label>)";
                    tex2 += i18n.lms1705m01["L170M01a.info6"];
                    tex2 += "</span><br>";
                } else if (json.itemNo == "B003") {
                    tex2 += "<span class='text-red'>＊";
                    // J-111-0031 更動覆審系統內以下九式覆審報告表之文字內容。
                    if ( responseData.rptId >= "Ver202204") {
                        tex2 += i18n.lms1705m01["L170M01a.info7v202204"];
                    } else {
                        tex2 += i18n.lms1705m01["L170M01a.info7"];
                    }
                    tex2 += "</span><br>";
                } else if (json.itemNo == "B016") {
                    tex2 += "<div id='show1'><span class='text-red'>＊";
                    tex2 += i18n.lms1705m01["L170M01a.info4"];
                    tex2 += " :<label><input id='chkPreReview' name='chkPreReview' type='radio' value='Y' /><span class='style999'>";
                    tex2 += i18n.lms1705m01["L170M01a.y1"];
                    tex2 += " </label><label><input id='chkPreReview' name='chkPreReview' type='radio' value='N' /><span class='style999'>";
                    tex2 += i18n.lms1705m01["L170M01a.n1"];
                    tex2 += "</label></span></div>";
                }
                
                if (json.itemType == type) { 
                	if (type == "Z" || type == "Y" || type == "X") {
                		continue;
                	}
                	if(realRpFg == "N" && json.itemNo == "B008"){
                		continue;	//是否為土建融	否-->不用輸入第11項附表
                	}
                    var str = "";
                    str = "<tr valign='top' " + " id='" + DOMPurify.sanitize(json.itemNo) + "'>";
                    count1++;
                    if (count1 == 1) {                   
                        // 當每一種覆審項次的第一筆才增加
                        str += "<td rowspan='" + DOMPurify.sanitize(String(len)) + "'" + " id='" + DOMPurify.sanitize(json.itemNo) +"_TD' align='center' valign='middle' class='hd1 style42' style='text-align:center' width='3%'>";
                        str += text;
                        str += "</td>";
                    }
                    if (count1 == len) {
                        // 當每一種覆審項次的的筆數都跑完count歸零
                        count1 = 0;
                    }

                    //附表
                    if(json.itemNo == "B009" && arrayZ != undefined && realRpFg == "Y"){	//B008 -> B009前在插入附表
                    //if(json.itemNo == "B009" && arrayZ != undefined){	//B008 -> B009前在插入附表
                    	table.append(createAttachedTable("Z",arrayZ,country,locale,responseData.rptId));
                    } else if(json.itemNo == "B012" && arrayY != undefined){	//B011 -> B012前在插入附表
                    	table.append(createAttachedTable("Y",arrayY,country,locale,responseData.rptId));
                    } else if(json.itemNo == "B014" && arrayX != undefined){	//B013 -> B014前在插入附表
                    	table.append(createAttachedTable("X",arrayX,country,locale,responseData.rptId));
                    }
                    
                    if ( responseData.rptId >= "Ver201907") { 
                    	if(json.itemSeqShow == ""){ 
                    		str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + DOMPurify.sanitize(json.itemNo) + "' />" + DOMPurify.sanitize(String(json.itemSeq)) + "</td>";
						}else if( json.itemNo =="B008" || json.itemNo =="B024"|| json.itemNo =="B025" ){ 
//							B007-> 11-1.中長期放款（含土建融之案件）之申貸計劃及其自籌款是否照預定進度與核定條件執行？
//							N012 工程預付款及/或履約保證其工程進度及履約情形是否正常
//							N031 XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
							
							str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + DOMPurify.sanitize(json.itemNo) + "' /></td>";
							
						}else{
							str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + DOMPurify.sanitize(json.itemNo) + "' />" + DOMPurify.sanitize(json.itemSeqShow) + "</td>";
						}

                    	
                    }else{
                    	if(json.itemSeq == 11) {
                        	str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + DOMPurify.sanitize(json.itemNo) + "' />" + DOMPurify.sanitize(String(json.itemSeq)) + "</td>";
                        } else if (json.itemSeq == 12) {
                        	str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + DOMPurify.sanitize(json.itemNo) + "' /></td>";
                        } else if (json.itemSeq > 12) {
                        	var itemSeq = json.itemSeq-1;
                        	str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + DOMPurify.sanitize(json.itemNo) + "' />" + DOMPurify.sanitize(String(itemSeq)) + "</td>";
                        } else {
                        	str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + DOMPurify.sanitize(json.itemNo) + "' />" + DOMPurify.sanitize(String(json.itemSeq)) + "</td>";
                        }
                    }
                	
                	
                    str += " <td width='45%'>" + DOMPurify.sanitize(json.itemContent) + "</td>";
                    str += " <td width='22%'>";
                    if (json.itemNo == "B016") {	//前次覆審有無應行改善事項？
                        str += "<label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='N' /><span class='style999'>";
                        if(locale == "en"){
                        	str += i18n.lms1705m01["L170M01a.n"];
                        } else {
                        	str += i18n.lms1705m01["L170M01a.y2"];
                        }
                        str += "</label><label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='Y' /><span class='style999'>";
                        if(locale == "en"){
                        	str += i18n.lms1705m01["L170M01a.y"];
                        } else {
                        	str += i18n.lms1705m01["L170M01a.have"];
                        }
                        str += "</label><label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='K' /><span class='style999'>";
                        str += i18n.lms1705m01["L170M01a.an"];
                        str += "</label></td>";
                    }
                    else 
                        if (json.itemNo == "B019") {
                            str += " <label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='N' /><span class='style999'>";
                            str += i18n.lms1705m01["L170M01a.n"];
                            str += "</label><label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='Y' /><span class='style999'>";
                            str += i18n.lms1705m01["L170M01a.y"];
                            str += "</label><label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='K' /><span class='style999'>";
                            str += i18n.lms1705m01["L170M01a.an"];
                            str += "</label></td>";
                        }
                        else {
                            str += " <label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='Y' /><span class='style999'>";
                            str += i18n.lms1705m01["L170M01a.y"];
                            str += "</label><label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='N' /><span class='style999'>";
                            str += i18n.lms1705m01["L170M01a.n"];
                            str += "</label><label><input id='chkResult" + DOMPurify.sanitize(json.itemNo) + "' name='chkResult" + DOMPurify.sanitize(json.itemNo) + "' type='radio' value='K' /><span class='style999'>";
                            str += i18n.lms1705m01["L170M01a.an"];
                            str += "</label></td>";
                        }
                    
                    str += "<td width='31%'>";
                    str += tex2;
                    // 前次覆審有無應行改善事項？(引藏)
                    if (json.itemNo == "B016") {
                        str += "<div id='show2'><span class='style44'><input type='text' id='chkText" + DOMPurify.sanitize(json.itemNo) + "' name='chkText" + DOMPurify.sanitize(json.itemNo) + "' size='40' maxlengthC='64' /></span><div>";
                    }
                    else {
                        str += "<span class='style44'><input type='text' id='chkText" + DOMPurify.sanitize(json.itemNo) + "' name='chkText" + DOMPurify.sanitize(json.itemNo) + "' size='40'maxlengthC='64' /></span>";
                    }
                    str += " </td>";
                    str += " </tr>";

                    table.append(str);

                    var uuu = json.chkResult;
                    var yyy1 = json.chkText;
                    var yyy2 = json.chkCheck;
                    var yyy3 = json.chkPreReview;
                    
                    $("input[type='radio'][name='chkResult" + json.itemNo + "'][value='" + uuu + "']").prop("checked", true);
                    $("input[type='radio'][name='chkPreReview'][value='" + yyy3 + "']").prop("checked", true);
                    $("input[type='checkbox'][name='chkCheck'][value='" + yyy2 + "']").prop("checked", true);
                    $("[name='chkText" + encodeURI(json.itemNo) + "']").val(yyy1);  // 掃到高風險
                    if ($("input[name=chkResultB016]:checked").val() == "N") {
                        $("#show1").hide();
                        $("#show2").hide();
                    }
                    else {
                        $("#show1").show();
                        $("#show2").show();
                        
                    }

                } // End itemType
                // $("body").append(str);
            }// END len的if
        }// END ARRAY
        
        
    	// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
    	var oriRowSpan=$("#B001_TD").prop('rowspan');
    
        $("input[name=chkResultY124]:radio").change(function(){
        
        	var nowRowSpan=$("#B001_TD").prop('rowspan');
        	var chkedVal = $("input[name=chkResultY124]:checked").val();    
        	var elmArr   = ["Y12A","Y12B","Y12C","Y12D","Y12E","Y12F"];
        	var elmShow  = []; 
        	var elmHide  = [];
        	
        	if(chkedVal==="N"){                                      		
          	    // 已經顯示的，才需要隱藏
                $.each(elmArr, function(idx, itemNo) {
    				if($("#"+itemNo).is(":visible")){
    				   elmHide.push(itemNo); 	 
    				}                      		                  		 
                });        	                                        		
          	}else{   
          	    // 已經隱藏的，才需要顯示
                  $.each(elmArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	 
  				    }                      		                  		 
                });                                         	
          	}
        	                                        	                                        	
        	chkedYNFunc(nowRowSpan,elmShow,elmHide);			
    	});
        
        
        $("input[name=chkResultY210]:radio").change(function(){
            
        	var nowRowSpan=$("#B001_TD").prop('rowspan');
        	var chkedVal = $("input[name=chkResultY210]:checked").val();    
        	var elmArr   = ["Y211","Y212","Y213"];
        	var elmShow  = []; 
        	var elmHide  = [];
        	
        	if(chkedVal==="N"){                                      		
          	    // 已經顯示的，才需要隱藏
                $.each(elmArr, function(idx, itemNo) {
    				if($("#"+itemNo).is(":visible")){
    				   elmHide.push(itemNo); 	 
    				}                      		                  		 
                });        	                                        		
          	}else{   
          	    // 已經隱藏的，才需要顯示
                  $.each(elmArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	 
  				    }                      		                  		 
                });                                         	
          	}
        	                                        	                                        	
        	chkedYNFunc(nowRowSpan,elmShow,elmHide);			
    	});
        
        
        $("input[name=chkResultY21A]:radio").change(function(){
        	
        	var nowRowSpan=$("#B001_TD").prop('rowspan');
          	var chkedVal = $('input[name=chkResultY21A]:checked').val();
        	var elmArr   = ["Y211","Y212","Y213"];        	
            if(rptid >= "Ver202406" ){
            	elmArr.push("Y214");
            	elmArr.push("Y215");
            }
            
        	var elmShow  = []; 
        	var elmHide  = [];
        	
        	if(chkedVal==="N"){                                      		
          	    // 已經顯示的，才需要隱藏
                $.each(elmArr, function(idx, itemNo) {
    				if($("#"+itemNo).is(":visible")){
    				   elmHide.push(itemNo); 	 
    				}                      		                  		 
                });        	                                        		
          	}else{   
          	    // 已經隱藏的，才需要顯示
                  $.each(elmArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	 
  				    }                      		                  		 
                });                                         	
          	}
        	                                     	
        	chkedYNFunc(nowRowSpan,elmShow,elmHide);
    	});
        
        
        $("input[name=chkResultY22A]:radio").change(function(){

        	var nowRowSpan=$("#B001_TD").prop('rowspan');
        	var chkedVal = $('input[name=chkResultY22A]:checked').val();
        	var elmArr   = ["Y221"];    	
        	var elmShow  = []; 
        	var elmHide  = [];
        	
        	if(chkedVal==="N"){                                      		
          	    // 已經顯示的，才需要隱藏
                $.each(elmArr, function(idx, itemNo) {
    				if($("#"+itemNo).is(":visible")){
    				   elmHide.push(itemNo); 	 
    				}                      		                  		 
                });        	                                        		
          	}else{   
          	    // 已經隱藏的，才需要顯示
                  $.each(elmArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	 
  				    }                      		                  		 
                });                                         	
          	}

        	chkedYNFunc(nowRowSpan,elmShow,elmHide);
    	});
        
        var y230Show=false;
        $("input[name=chkResultY230]:radio").change(function(){

        	var nowRowSpan=$("#B001_TD").prop('rowspan');
        	var chkedVal = $('input[name=chkResultY230]:checked').val();
        	var elmArr   = ["Y231","Y23A","Y23B",
        	                "Y232","Y23C","Y23D",
        	                "Y233","Y234","Y23E","Y23F","Y23G","Y23H","Y235",
        	                "Y236","Y23I","Y23J","Y23K",
        	                "Y237","Y238","Y23L","Y23M","Y239","Y23N","Y23O"
        	               ];    	
        	var elmShow  = []; 
        	var elmHide  = [];
        	

        	if(chkedVal==="K"){                                      		
          	    // 已經顯示的，才需要隱藏
                $.each(elmArr, function(idx, itemNo) {
    				if($("#"+itemNo).is(":visible")){
    				   elmHide.push(itemNo); 	    				   
    				}                      		                  		 
                });        	                    
                y230Show=false;
          	}else{   
          	    // 已經隱藏的，才需要顯示
                  $.each(elmArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	  					   
  				    }                      		                  		 
                });                             
                y230Show=true;                  
          	}

        	chkedYNFunc(nowRowSpan,elmShow,elmHide);
        	
            	
            $("input[name=chkResultY234]:radio").trigger('change');
            $("input[name=chkResultY236]:radio").trigger('change');
            $("input[name=chkResultY238]:radio").trigger('change');
            $("input[name=chkResultY239]:radio").trigger('change');
        	
        	
	
    	});
        
        
        
        $("input[name=chkResultY234]:radio").change(function(){
        	
        	var nowRowSpan=$("#B001_TD").prop('rowspan');
          	var chkedVal = $('input[name=chkResultY234]:checked').val();
        	var elmArr   = ["Y23E", "Y23F", "Y23G", "Y23H" ];
        	var elmShow  = []; 
        	var elmHide  = [];
        	var tempArr  = [];
        	
        	if(!y230Show){
        		return;
        	}

            if (chkedVal==="N") {                                    			
    			tempArr=["Y23E" ];
           	    // 已經顯示的，才需要隱藏
                  $.each(tempArr,function(idx, itemNo) {
                	    if($("#"+itemNo).is(":visible")){
 				       elmHide.push(itemNo); 	 
 				    }
                	    
    			    // 清除勾選，避免該選項已經隱藏，又按預設值鈕的狀況
                    $("input[name=chkResult"+itemNo+"]").removeAttr("checked");
                	    
                }); 
                                        		                    
    			                                    			
                  tempArr=["Y23F", "Y23G", "Y23H" ];
         	    // 已經隱藏的，才需要顯示
                  $.each(tempArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	 
  				    }                      		                  		 
                });   
                                        		                    
                  tempArr=[];
    		}else if (chkedVal==="Y") {                 
    			
    			tempArr=["Y23F", "Y23G", "Y23H" ];
           	    // 已經顯示的，才需要隱藏
                  $.each(tempArr,function(idx, itemNo) {
  				    if($("#"+itemNo).is(":visible")){
  				    	elmHide.push(itemNo); 	 
  				    }  
  				    
    			    // 清除勾選，避免該選項已經隱藏，又按預設值鈕的狀況
                    $("input[name=chkResult"+itemNo+"]").removeAttr("checked");
                });  
    			                                    			
                  tempArr=["Y23E" ];
         	    // 已經隱藏的，才需要顯示
                  $.each(tempArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  				    	elmShow.push(itemNo); 	 
  				    }                      		                  		 
                });   
                                        		                    
                  tempArr=[];
    			
    		}else if(chkedVal==="K"){
    			
    			tempArr=["Y23E","Y23F", "Y23G", "Y23H" ];
           	    // 已經顯示的，才需要隱藏
                  $.each(tempArr,function(idx, itemNo) {
  				    if($("#"+itemNo).is(":visible")){
  				    	elmHide.push(itemNo); 	 
  				    }  
  				    
    			    // 清除勾選，避免該選項已經隱藏，又按預設值鈕的狀況
                    $("input[name=chkResult"+itemNo+"]").removeAttr("checked");
                });  
    			                                    			                     		                    
                tempArr=[];
    		}    			    			
    		else{
          	    // 已經隱藏的，才需要顯示
                  $.each(elmArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	 
  				    }                      		                  		 
                });  
                  
                 tempArr=[];
    		}
            		                       
             chkedYNFunc(nowRowSpan,elmShow,elmHide);

    	});
        
        $("input[name=chkResultY236]:radio").change(function(){
        	
        	var nowRowSpan=$("#B001_TD").prop('rowspan');
        	var chkedVal = $('input[name=chkResultY236]:checked').val();
        	var elmArr   = ["Y23I", "Y23J", "Y23K"];    	
        	var elmShow  = []; 
        	var elmHide  = [];
        	
        	if(!y230Show){
        		return;
        	}
        	
        	if(chkedVal==="N"){                                      		
          	    // 已經顯示的，才需要隱藏
                $.each(elmArr, function(idx, itemNo) {
    				if($("#"+itemNo).is(":visible")){
    				   elmHide.push(itemNo); 	 
    				}                      		                  		 
                });        	                                        		
          	}else{   
          	    // 已經隱藏的，才需要顯示
                  $.each(elmArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	 
  				    }                      		                  		 
                });                                         	
          	}
        	
        	chkedYNFunc(nowRowSpan,elmShow,elmHide);
    	});
        
        $("input[name=chkResultY238]:radio").change(function(){
        	
        	var nowRowSpan=$("#B001_TD").prop('rowspan');
        	var chkedVal = $('input[name=chkResultY238]:checked').val();
        	var elmArr   = ["Y23L", "Y23M" ];    	
        	var elmShow  = []; 
        	var elmHide  = [];
        	
        	if(!y230Show){
        		return;
        	}
        	
        	if(chkedVal==="N"){                                      		
          	    // 已經顯示的，才需要隱藏
                $.each(elmArr, function(idx, itemNo) {
    				if($("#"+itemNo).is(":visible")){
    				   elmHide.push(itemNo); 	 
    				}                      		                  		 
                });        	                                        		
          	}else{   
          	    // 已經隱藏的，才需要顯示
                  $.each(elmArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	 
  				    }                      		                  		 
                });                                         	
          	}
        	
        	chkedYNFunc(nowRowSpan,elmShow,elmHide);
    	});
        
        
        $("input[name=chkResultY239]:radio").change(function(){
        	
        	var nowRowSpan=$("#B001_TD").prop('rowspan');
        	var chkedVal = $('input[name=chkResultY239]:checked').val();
        	var elmArr   = ["Y23N", "Y23O"];    	                                        	
        	var elmShow  = []; 
        	var elmHide  = [];
        	
        	if(!y230Show){
        		return;
        	}
        	
        	if(chkedVal==="N"){                                      		
          	    // 已經顯示的，才需要隱藏
                $.each(elmArr, function(idx, itemNo) {
    				if($("#"+itemNo).is(":visible")){
    				   elmHide.push(itemNo); 	 
    				}                      		                  		 
                });        	                                        		
          	}else{   
          	    // 已經隱藏的，才需要顯示
                  $.each(elmArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	 
  				    }                      		                  		 
                });                                         	
          	}
        	
        	chkedYNFunc(nowRowSpan,elmShow,elmHide);
    	});        
        
        $("input[name=chkResultY310]:radio").change(function(){

        	var nowRowSpan=$("#B001_TD").prop('rowspan');
        	var chkedVal = $('input[name=chkResultY310]:checked').val();
        	var elmArr   = ["Y31A"
        	               ];    	
        	var elmShow  = []; 
        	var elmHide  = [];
        	

        	if(chkedVal ==="Y" || chkedVal ==="K"){                                      		
          	    // 已經顯示的，才需要隱藏
                $.each(elmArr, function(idx, itemNo) {
    				if($("#"+itemNo).is(":visible")){
    				   elmHide.push(itemNo); 	    				   
    				}                      		                  		 
                });        	                    
                
          	}else{   
          	    // 已經隱藏的，才需要顯示
                  $.each(elmArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	  					   
  				    }                      		                  		 
                });                                                       
          	}

        	chkedYNFunc(nowRowSpan,elmShow,elmHide);
	
    	});
                
        $("input[name=chkResultX110]:radio").change(function(){
        	
        	var nowRowSpan=$("#B001_TD").prop('rowspan');
        	var chkedVal = $('input[name=chkResultX110]:checked').val();
        	var elmArr   = ["X111", "X112", "X113"];    	
        	var elmShow  = []; 
        	var elmHide  = [];
        	
        	if(chkedVal==="N"){                                      		
          	    // 已經顯示的，才需要隱藏
                $.each(elmArr, function(idx, itemNo) {
    				if($("#"+itemNo).is(":visible")){
    				   elmHide.push(itemNo); 	 
    				}                      		                  		 
                });        	                                        		
          	}else{   
          	    // 已經隱藏的，才需要顯示
                  $.each(elmArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	 
  				    }                      		                  		 
                });                                         	
          	}
        	
        	chkedYNFunc(nowRowSpan,elmShow,elmHide);
        });
        
        $("input[name=chkResultX210]:radio").change(function(){
        	
        	var nowRowSpan=$("#B001_TD").prop('rowspan');
        	var chkedVal = $('input[name=chkResultX210]:checked').val();
        	var elmArr   = ["X211", "X212", "X213"];
        	var elmShow  = []; 
        	var elmHide  = [];
        	
        	if(chkedVal==="N"){                                      		
          	    // 已經顯示的，才需要隱藏
                $.each(elmArr, function(idx, itemNo) {
    				if($("#"+itemNo).is(":visible")){
    				   elmHide.push(itemNo); 	 
    				}                      		                  		 
                });        	                                        		
          	}else{   
          	    // 已經隱藏的，才需要顯示
                  $.each(elmArr, function(idx, itemNo) {
  				    if($("#"+itemNo).is(":hidden")){
  					   elmShow.push(itemNo); 	 
  				    }                      		                  		 
                });                                         	
          	}
        	
        	chkedYNFunc(nowRowSpan,elmShow,elmHide);
        });
    	
    	 
        function chkedYNFunc(nowRowSpan,elmShow,elmHide){
        	
               
			      // 隱藏子項，並清除勾選
          	  $.each(elmHide, function(idx, itemNo) {
				      $("#"+itemNo).hide();
				      $("input[name=chkResult"+itemNo+"]").removeAttr("checked");
          	  });                                        	  
          	  
              // 顯示子項                             			
			      $.each(elmShow, function(idx, itemNo) {
				     $("#"+itemNo).show();
          	  });
			                                     		
			      // 因增刪子項，重新計算左側欄位的rowspan
			      // 表示尚未選擇任何隱藏或顯示的子項
			      var calcLen = nowRowSpan-elmHide.length+elmShow.length;
			      $("#B001_TD").prop('rowspan',calcLen);	
                                                      	                                        	    						
        	
        }
   
        
        // 前次覆審有無應行改善事項？ 
        $("input[name=chkResultB016]").click(function(){
            if ($(this).val() == "N") {
                $("#show1").hide();
                $("#show2").hide();
            }
            else {
                $("#show1").show();
                $("#show2").show();
                
            }
        });
        
        //塞附表值
    	for (var i = 0; i < array.length; i++) {
			if(array[i].itemType == "Z" || array[i].itemType == "Y" ||
					array[i].itemType == "X" ){
				$("input[type='radio'][name='chkResult" + array[i].itemNo + "'][value='" + array[i].chkResult + "']").prop("checked", true);
                $("input[type='radio'][name='chkPreReview'][value='" + array[i].chkPreReview + "']").prop("checked", true);
                $("input[type='checkbox'][name='chkCheck'][value='" + array[i].chkCheck + "']").prop("checked", true);
                $("[name='chkText" + encodeURI(array[i].itemNo) + "']").val(array[i].chkText);      // 掃到高風險
			}
    	}
    	
    	// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
        $("input[name=chkResultY124]:radio").trigger('change');
        $("input[name=chkResultY210]:radio").trigger('change');
        $("input[name=chkResultY21A]:radio").trigger('change');
        $("input[name=chkResultY22A]:radio").trigger('change');
        $("input[name=chkResultY230]:radio").trigger('change');
        $("input[name=chkResultY234]:radio").trigger('change');
        $("input[name=chkResultY236]:radio").trigger('change');
        $("input[name=chkResultY238]:radio").trigger('change');
        $("input[name=chkResultY239]:radio").trigger('change');
        $("input[name=chkResultX110]:radio").trigger('change');
        $("input[name=chkResultX210]:radio").trigger('change');
        // J-113-0204  新增及修正說明文句
        $("input[name=chkResultY310]:radio").trigger('change');


    }
    
    function createAttachedTable(arrayType, array, country, locale, rptId){
    	var ATstr = "";
    	var TAB_0 =  ['Z000', 'Y000', 'X000'];
        var TAB_1 =  ['Z100', 'Z200', 'Z300', 'Z400', 'Y100', 'Y200',
                      'X100', 'X200'];
        var TAB_2 =  ['Y110', 'Y120', 'Y130', 'Y210', 'X110', 'X210'];
        var TAB_3 =  ['Y111', 'Y112', 'Y121', 'Y122', 'Y123', 'Y124',
                      'Y131', 'Y132', 'Y133', 'Y134', 'Y135', 'Y211',
                      'Y212', 'Y213', 'X111', 'X112', 'X113', 'X211',
                      'X212', 'X213'];
        var TAB_4 =  ['Y12A', 'Y12B', 'Y12C', 'Y12D', 'Y12E', 'Y12F'];
        
        // J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
        var TAB_5 = [];
        var colspan_3 = [];
       
        var noChk =  ['Z000', 'Y000', 'Y100', 'Y110', 'Y120', 'Y130',
                      'Y200', 'X000', 'X100', 'X200'];
        var chk_3 =  ['Z100', 'Z200', 'Z300', 'Z400', 'Y211', 'Y212',
                      'Y213', 'X113', 'X211', 'X212', 'X213'];	//是 否 －
        var chk_2 =  ['Y111', 'Y112', 'Y121', 'Y122', 'Y123', 'Y12A',
                      'Y12B', 'Y12C', 'Y12D', 'Y12E', 'Y12F', 'Y131',
                      'Y132', 'Y133', 'Y134', 'Y135', 'X111', 'X112'];	//是 否
        var chk_YN = ['Y124', 'Y210', 'X110', 'X210'];
        
        // 覆審結果選項為有無
        var chk_YN2 =[]; 


		// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
        // Y121:chk_2改chk_3、Y210改Y21A、新增Y220.Y22A.Y221
        // J-111-0326 海外覆審作業系統改良第一階段： 7. Y135:chk_2改chk_3
        if ( rptId >= "Ver202210" ) {
    	    TAB_2 =  ['Y110', 'Y120', 'Y130', 'Y210', 'Y21A', 'Y220',
    	              'Y22A', 'X110', 'X210'];
            TAB_3 =  ['Y111', 'Y112', 'Y121', 'Y122', 'Y123', 'Y124',
                      'Y131', 'Y132', 'Y133', 'Y134', 'Y135', 'Y211',
                      'Y212', 'Y213', 'Y221', 'X111', 'X112', 'X113',
                      'X211', 'X212', 'X213'];
            noChk =  ['Z000', 'Y000', 'Y100', 'Y110', 'Y120', 'Y130',
                      'Y200', 'Y210', 'Y220', 'X000', 'X100', 'X200'];
            chk_3 =  ['Z100', 'Z200', 'Z300', 'Z400', 'Y121', 'Y211',
                      'Y212', 'Y213', 'X113', 'Y135', 'X211', 'X212',
                      'X213'];	//是 否 －
            chk_2 =  ['Y111', 'Y112', 'Y122', 'Y123', 'Y12A', 'Y12B',
                      'Y12C', 'Y12D', 'Y12E', 'Y12F', 'Y131', 'Y132',
                      'Y133', 'Y134', 'Y221', 'X111', 'X112'];	//是 否
            chk_YN = ['Y124', 'Y21A', 'Y22A', 'X110', 'X210'];
    	}
    
	    // J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
        if ( rptId >= "Ver202307" ) {
    		// 設定階層
    	    TAB_2 =  ['Y110', 'Y120', 'Y130', 'Y210', 'Y21A', 'Y220','Y22A', 
    	              'Y230',
    	              'X110', 'X210'];                          
            TAB_3 =  ['Y111', 'Y112', 'Y121', 'Y122', 'Y123', 'Y124',
                      'Y131', 'Y132', 'Y133', 'Y134', 'Y135', 'Y211',
                      'Y212', 'Y213', 'Y221',
                      'Y231', 'Y232', 'Y233', 'Y236', 'Y237',
                      'X111', 'X112', 'X113',
                      'X211', 'X212', 'X213'];
            

            TAB_4 =  ['Y12A', 'Y12B', 'Y12C', 'Y12D', 'Y12E', 'Y12F',
                      'Y23A', 'Y23B',
                      'Y23C', 'Y23D',
                      'Y234', 'Y235',
                      'Y23I', 'Y23J', 'Y23K',
                      'Y238', 'Y239'
                     ];
            
           colspan_3=['Y231', 'Y232', 'Y233', 'Y237',  
                     ];
           
           TAB_5 =  ['Y23E', 'Y23F', 'Y23G', 'Y23H', 
            	     'Y23L', 'Y23M', 'Y23N', 'Y23O' 
            	    ];
            
            
            
            // 不需選擇的
            noChk =  ['Z000', 'Y000', 'Y100', 'Y110', 'Y120', 'Y130',
                      'Y200', 'Y210', 'Y220',                       
                      'X000', 'X100', 'X200'];
            //  覆審結果選項為 是 、否、 －
            chk_3 =  ['Z100', 'Z200', 'Z300', 'Z400', 'Y121', 'Y211',
                      'Y212', 'Y213', 
                      'Y230',
                      'Y234',
                      'Y235',
                      'Y23O', 
                      'X113', 'Y135', 'X211', 'X212',
                      'X213'];
            // 覆審結果選項為 是、否
            chk_2 =  ['Y111', 'Y112', 'Y122', 'Y123', 'Y12A', 'Y12B',
                      'Y12C', 'Y12D', 'Y12E', 'Y12F', 'Y131', 'Y132',
                      'Y133', 'Y134', 'Y221', 
                      'Y23B', 'Y23C', 'Y23D',
                      'Y23E', 'Y23F', 'Y23G', 'Y23H', 
                      'Y23I', 'Y23J', 'Y23K',
                      'Y23L', 'Y23M', 'Y23N', 
                      'X111', 'X112'];
            // 覆審項目選項為有、無
            chk_YN = ['Y124', 'Y21A', 'Y22A',
                      'Y236', 'Y238', 'Y239',
                      'X110', 'X210'];
         // 覆審結果選項為有無
            chk_YN2 = ['Y23A'];
    	}
        
        if ( rptId >= "Ver202406" ) {
    		// J-113-0204  新增及修正說明文句
    		
    		// 設定階層
            TAB_1 =  ['Z100', 'Z200', 'Z300', 'Z400', 'Y100', 'Y200', 'Y300',
                      'X100', 'X200'];
        	        	
    	    TAB_2 =  ['Y110', 'Y120', 'Y130', 'Y210', 'Y21A', 'Y220','Y22A', 
    	              'Y230', 'Y240', 'Y250',
    	              'Y310', 'Y320', 'Y330',
    	              'X110', 'X210'];    
    	    
            TAB_3 =  ['Y111', 'Y112', 'Y113', 'Y121', 'Y122', 'Y123', 'Y124',
                      'Y131', 'Y132', 'Y133', 'Y134', 'Y135', 'Y211',
                      'Y212', 'Y213', 'Y214', 'Y215', 'Y221',
                      'Y231', 'Y232', 'Y233', 'Y236', 'Y237',
                      'Y31A', 'Y33A', 'Y33B', 'Y33C', 'Y33D',
                      'X111', 'X112', 'X113',
                      'X211', 'X212', 'X213'];
            

            TAB_4 =  ['Y12A', 'Y12B', 'Y12C', 'Y12D', 'Y12E', 'Y12F',
                      'Y23A', 'Y23B',
                      'Y23C', 'Y23D',
                      'Y234', 'Y235',
                      'Y23I', 'Y23J', 'Y23K',
                      'Y238', 'Y239'
                     ];
            
           colspan_3=['Y231', 'Y232', 'Y233', 'Y237',  
                     ];
           
           TAB_5 =  ['Y23E', 'Y23F', 'Y23G', 'Y23H', 
            	     'Y23L', 'Y23M', 'Y23N', 'Y23O' 
            	    ];
            
            
            
            // 不需選擇的
            noChk =  ['Z000', 'Y000', 'Y100', 'Y110', 'Y120', 'Y130',
                      'Y200', 'Y210', 'Y220', 'Y300', 'Y330',                        
                      'X000', 'X100', 'X200'];
            //  覆審結果選項為 是 、否、 －
            chk_3 =  ['Z100', 'Z200', 'Z300', 'Z400', 'Y121', 'Y211',
                      'Y212', 'Y213', 'Y214', 'Y215', 
                      'Y230',
                      'Y234',
                      'Y235',
                      'Y23O', 
                      'Y310', 'Y31A', 'Y320', 'Y33A', 'Y33B', 'Y33C', 'Y33D',
                      'Y240', 'Y250',
                      'X113', 'Y135', 'X211', 'X212',
                      'X213'];
            // 覆審結果選項為 是、否
            chk_2 =  ['Y111', 'Y112', 'Y113', 'Y122', 'Y123', 'Y12A', 'Y12B',
                      'Y12C', 'Y12D', 'Y12E', 'Y12F', 'Y131', 'Y132',
                      'Y133', 'Y134', 'Y221', 
                      'Y23B', 'Y23C', 'Y23D',
                      'Y23E', 'Y23F', 'Y23G', 'Y23H', 
                      'Y23I', 'Y23J', 'Y23K',
                      'Y23L', 'Y23M', 'Y23N',                       
                      'X111', 'X112'];
            // 覆審項目選項為有、無
            chk_YN = ['Y124', 'Y21A', 'Y22A',
                      'Y236', 'Y238', 'Y239',
                      'X110', 'X210'];
         // 覆審結果選項為有無
            chk_YN2 = ['Y23A'];
    	} 
    	
    	
    	for(var i = 0; i < array.length; i++){
    		ATstr += "<tr valign='top' " + " id='" + DOMPurify.sanitize(array[i].itemNo) + "'>";
    		ATstr += " <td width='5%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + DOMPurify.sanitize(array[i].itemNo) + "' /></td>";
    		
    		if(jQuery.inArray(array[i].itemNo,TAB_0) >= 0){
    			ATstr += " <td colspan='3'>" + DOMPurify.sanitize(array[i].itemContent) + "</td>";
    		} else if(jQuery.inArray(array[i].itemNo,TAB_1) >= 0){
    			ATstr += ((jQuery.inArray(array[i].itemNo,noChk) >= 0) ? " <td colspan='3'>" : " <td width='45%'>")
    						+ "　"+ DOMPurify.sanitize(array[i].itemContent) + "</td>";
    		} else if(jQuery.inArray(array[i].itemNo,TAB_2) >= 0){
    			if(jQuery.inArray(array[i].itemNo,noChk) >= 0){
    				if(array[i].itemNo == "Y130"){
    					ATstr += " <td colspan='3'>" + "　　"+ DOMPurify.sanitize(array[i].itemContent) + "　　" + "<span class='text-red'>＊";
    					ATstr += i18n.lms1705m01["L170M01a.info8"];
    					ATstr += "</span></td>";
    				} else {
    					ATstr += " <td colspan='3'>" + "　　"+ DOMPurify.sanitize(array[i].itemContent) + "</td>";
    				}
    			} else if(jQuery.inArray(array[i].itemNo,chk_YN) >= 0){	//有無
    				var str = DOMPurify.sanitize(array[i].itemContent);
    				var point = str.indexOf("有無");
    				ATstr += " <td width='45%' colspan='3'>" + "　　"+ str.slice(0,point)+" ";
    				ATstr += "<label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='Y' /><span class='style999'>";
    				if(locale == "en"){
    				    if(array[i].itemNo == "Y22A"){
                            ATstr += i18n.lms1705m01["L170M01a.y_is"];
                        } else {
                            ATstr += i18n.lms1705m01["L170M01a.y_i"];
                        }
                    } else {
                    	ATstr += i18n.lms1705m01["L170M01a.have"];
                    }
    				ATstr += "</label><label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='N' /><span class='style999'>";
    				if(locale == "en"){
    				    if(array[i].itemNo == "Y22A"){
                            ATstr += i18n.lms1705m01["L170M01a.n_is"];
                        } else {
                            ATstr += i18n.lms1705m01["L170M01a.n_d"];
                        }
                    } else {
                    	ATstr += i18n.lms1705m01["L170M01a.y2"];
                    }
    				ATstr += "</label>" + " " + str.slice(point+2) +"</td>";
    			} else {
    				ATstr += " <td width='45%'>" + "　　"+ DOMPurify.sanitize(array[i].itemContent) + "</td>";
    			}
    		} else if(jQuery.inArray(array[i].itemNo,TAB_3) >= 0){
    			if(jQuery.inArray(array[i].itemNo,chk_YN) >= 0){	//有無
    				var str = DOMPurify.sanitize(array[i].itemContent);
    				var point = str.indexOf("有無");
    				ATstr += " <td width='45%' colspan='3'>" + "　　　"+ str.slice(0,point)+" ";
    				ATstr += "<label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='Y' /><span class='style999'>";
    				if(locale == "en"){
    					// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
    					if(array[i].itemNo == "Y236"){
    						ATstr += i18n.lms1705m01["L170M01a.y"];
    					}    					
    					else if(array[i].itemNo == "Y124" && country == "CA"){
    						ATstr += i18n.lms1705m01["L170M01a.y_i_CA"];
    					} else {
    						ATstr += i18n.lms1705m01["L170M01a.y_i"];
    					}    					
                    } else {
                    	ATstr += i18n.lms1705m01["L170M01a.have"];
                    }
    				ATstr += "</label><label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='N' /><span class='style999'>";
    				if(locale == "en"){
    					// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
    					if(array[i].itemNo == "Y236"){
    						ATstr += i18n.lms1705m01["L170M01a.y2"];
    					}    					
    					else if(array[i].itemNo == "Y124" && country == "CA"){
    						ATstr += i18n.lms1705m01["L170M01a.n_d_CA"];
    					} else {
    						ATstr += i18n.lms1705m01["L170M01a.n_d"];
    					}
                    } else {
                    	ATstr += i18n.lms1705m01["L170M01a.y2"];
                    }
    				ATstr += "</label>" + " " + str.slice(point+2) +"</td>";
    			} else {
    				
    				if(jQuery.inArray(array[i].itemNo,colspan_3) >= 0){
    					ATstr += " <td width='45%' colspan='3'>" + "　　　"+ DOMPurify.sanitize(array[i].itemContent);
    				}
    			    else{
    				      ATstr += " <td width='45%'>" + "　　　"+ DOMPurify.sanitize(array[i].itemContent);
    			    }
    				
    				if(array[i].itemNo == "Y221"){
                        ATstr += "<br><span class='text-red'>";
                        if(locale == "en" && country == "CA"){
                            ATstr += i18n.lms1705m01["L170M01a.info10_CA"];
                        } else {
                            ATstr += i18n.lms1705m01["L170M01a.info10"];
                        }
                        ATstr += "</span>";
                    }
    				ATstr += "</td>";
    			}
    		} else if(jQuery.inArray(array[i].itemNo,TAB_4) >= 0){
    			
    			if(jQuery.inArray(array[i].itemNo,chk_YN) >= 0){	//有無
    				var str = DOMPurify.sanitize(array[i].itemContent);
    				var point = str.indexOf("有無");
    				
    				ATstr += " <td width='45%' colspan='3'>" + "　　　　"+ str.slice(0,point)+" ";
    				ATstr += "<label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='Y' /><span class='style999'>";
    				if(locale == "en"){
    					
    					if(array[i].itemNo == "Y124" && country == "CA"){
    						ATstr += i18n.lms1705m01["L170M01a.y_i_CA"];
    					} else {
    						ATstr += i18n.lms1705m01["L170M01a.y_i"];
    					}    					
                    } else {
                    	ATstr += i18n.lms1705m01["L170M01a.have"];
                    }
    				
    				
    				ATstr += "</label><label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='N' /><span class='style999'>";
    				if(locale == "en"){
    					
    					if(array[i].itemNo == "Y124" && country == "CA"){
    						ATstr += i18n.lms1705m01["L170M01a.n_d_CA"];
    					} else {
    						ATstr += i18n.lms1705m01["L170M01a.n_d"];
    					}
                    } else {
                    	ATstr += i18n.lms1705m01["L170M01a.y2"];
                    }
    				
    				ATstr += "</label>" + " " + str.slice(point+2) +"</td>";
    				
    				
    			}else if(jQuery.inArray(array[i].itemNo,colspan_3) >= 0){
    			     ATstr += " <td width='45%' colspan='3'>" + "　　　　"+ DOMPurify.sanitize(array[i].itemContent) + "</td>";
    			}
    			else{
    				var str=DOMPurify.sanitize(array[i].itemContent);
    				var point = "";
    				    				
    				if(array[i].itemNo == "Y23C"){
        				point = str.indexOf("是否");  
        				if(point !== -1){
        				   str=str.slice(0,point);
        				}
        			}else if(array[i].itemNo == "Y23A"){
        				point = str.indexOf("有無"); 
        				if(point !== -1){
        				   str=str.slice(0,point);
        				}
        			}
    				
    			     ATstr += " <td width='45%'>" + "　　　　"+ str + "</td>";
			    }
    		}
    		// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
    		else if(jQuery.inArray(array[i].itemNo,TAB_5) >= 0){
    			 ATstr += " <td width='45%'>" + "　　　　　"+ DOMPurify.sanitize(array[i].itemContent) + "</td>";
    		}
	    	
    		if(jQuery.inArray(array[i].itemNo,noChk) >= 0){
    		} else if(jQuery.inArray(array[i].itemNo,chk_3) >= 0){
    			ATstr += " <td width='22%'>";
        		ATstr += " <label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='Y' /><span class='style999'>";
        		ATstr += i18n.lms1705m01["L170M01a.y"];
        		ATstr += "</label><label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='N' /><span class='style999'>";
        		ATstr += i18n.lms1705m01["L170M01a.n"];
        		ATstr += "</label><label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='K' /><span class='style999'>";
        		ATstr += i18n.lms1705m01["L170M01a.an"];
        		ATstr += "</label></td>";
	    	} else if(jQuery.inArray(array[i].itemNo,chk_2) >= 0){
    			ATstr += " <td width='22%'>";
        		ATstr += " <label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='Y' /><span class='style999'>";
        		ATstr += i18n.lms1705m01["L170M01a.y"];
        		ATstr += "</label><label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='N' /><span class='style999'>";
        		ATstr += i18n.lms1705m01["L170M01a.n"];
        		ATstr += "</label></td>";
	    	}
    		// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
	    	else if(jQuery.inArray(array[i].itemNo,chk_YN2) >= 0){
    			ATstr += " <td width='22%'>";
        		ATstr += " <label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='Y' /><span class='style999'>";
        		
        		if(locale == "en"){
	                ATstr += i18n.lms1705m01["L170M01a.y3"];
                }
        		else{
        			ATstr += i18n.lms1705m01["L170M01a.have"];
        		}
        		        		        	
        		ATstr += "</label><label><input id='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkResult" + DOMPurify.sanitize(array[i].itemNo) + "' type='radio' value='N' /><span class='style999'>";
        		
        		if(locale == "en"){
	                ATstr += i18n.lms1705m01["L170M01a.n3"];
                }
        		else{
        			ATstr += i18n.lms1705m01["L170M01a.y2"];        		        		
        		}
        		
        		ATstr += "</label></td>";
	    	}
    		
    		var chkText = ['X111', 'X112', 'X113', 'X211', 'X212', 'X213']
    		if(jQuery.inArray(array[i].itemNo,chkText) >= 0){
    			ATstr += "<td width='31%'>";
    			ATstr += "<span class='text-red'>" + i18n.lms1705m01["L170M01a.info9"] + "</span><br>";
    			ATstr += "<span class='style44'><input type='text' id='chkText" + DOMPurify.sanitize(array[i].itemNo) + "' name='chkText" + DOMPurify.sanitize(array[i].itemNo) + "' size='40'maxlengthC='64' /></span>";
    			ATstr += " </td>";
    			ATstr += " </tr>";
    		}
    		ATstr += " </tr>";
    	}
    	return ATstr;
    }
	
	function exCheck(v,t,s){
		var val = v.val();
		var $target = t;
		var $select = s;
    	if (val == "NA" || val == "" || val == "G" 
				|| val == "XC" || val == "XM") {
            $target.hide();
			$select.val("");
        }
        else {
            $target.show();
        }
    }
	
	function jsSelectIsExitItem(objSelect, objItemValue) { 
		var count = 0;
		for (var i = 0; i < objSelect.find('option').length; i++) { 
			if (objSelect.get(0).options[i].value == objItemValue) { 
				count += 1;
				if(count > 1 ){
					objSelect.find('option').remove(i); 
				}
			} 
		} 		
		if(count == 0){
			if(objItemValue == "XC"){
			
			    objItemValue =  DOMPurify.sanitize(objItemValue);

				let htmlStr = $("<option></option>")
				.prop("value", objItemValue)
				.text("N.A.")[0]
				.outerHTML;
				htmlStr = DOMPurify.sanitize(htmlStr);
				$("#excrdType").append(htmlStr);
	
				$("#excrdType [value='"+objItemValue+"']")
				.prop("key", objItemValue)
				.prop("showvalue", "N.A.");
				
			} else if(objItemValue == "XM"){	

			    objItemValue =  DOMPurify.sanitize(objItemValue);

				let htmlStr = $("<option></option>")
				.prop("value", objItemValue)
				.text("N.A.")[0]
				.outerHTML;
				htmlStr = DOMPurify.sanitize(htmlStr);
				$("#excrdTypeMow").append(htmlStr);
				
				$("#excrdTypeMow [value='"+objItemValue+"']")
				.prop("key", objItemValue)
				.prop("showvalue", "N.A.");				
			}
		}
	} 

	

	
	
});
