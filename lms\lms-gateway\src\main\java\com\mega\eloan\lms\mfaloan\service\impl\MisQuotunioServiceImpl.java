/* 
 *MisQuotunioServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Types;
import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisQuotunioService;

/**
 * <pre>
 * 聯貸案參貸比率-自行參貸 QUOTUNIO(MIS.ELV38501)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
@Service
public class MisQuotunioServiceImpl extends AbstractMFAloanJdbc implements
		MisQuotunioService {

	@Override
	public void delByCntrNo(String cntrNo,String type ) {
		this.getJdbc().update("QUOTUNIO.delByCntrno", new Object[] { cntrNo ,type});

	}

	@Override
	public void insert(List<Object[]> dataList) {
		this.getJdbc().batchUpdate(
				"QUOTUNIO.insert",
				new int[] { Types.CHAR,Types.CHAR, Types.CHAR, Types.CHAR, Types.DECIMAL,
						Types.CHAR }, dataList);

	}

	@Override
	public void delByCntrNo(String cntrNo) {
		this.getJdbc().update("QUOTUNIO.delByCntrnoOnly", new Object[] { cntrNo});
		
	}

}
