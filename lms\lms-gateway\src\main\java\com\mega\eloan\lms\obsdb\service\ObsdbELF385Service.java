/* 
 *ObsdbELF385Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service;

import java.util.List;

/**
 * <pre>
 * 聯貸案參貸比率檔  ELF385
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
public interface ObsdbELF385Service {

	/**
	 * 新增
	 * 
	 * @param BRNID
	 *            上傳的銀行代碼
	 * @param dataList
	 *            sqlList
	 * 
	 *            <pre>
	 *  UNLNTYPE, 聯貸性質
	 *  CNTRNO, 額度序號
	 *  UNITNO,  參貸行庫
	 *  MAINBH, 主辦行
	 *  LNAMT, 參貸金額 DECIMAL(15)
	 * 	UPDATER,資料修改人
	 * 	TMESTAMP資料修改日期 DECIMAL(19)
	 * </pre>
	 */
	void insert(String BRNID, List<Object[]> dataList);

	/**
	 * 刪除
	 * 
	 * @param BRNID
	 *            上傳的銀行代碼
	 * @param cntrNo
	 *            額度序號
	 * @param unlnType
	 *            1.同業聯貸 2.自行聯貸
	 */
	void delByCntrNo(String BRNID, String cntrNo, String unlnType);

	/**
	 * 刪除only
	 * 
	 * @param BRNID
	 *            上傳的銀行代碼
	 * @param cntrNo
	 *            額度序號
	 */
	void delByCntrNoOnly(String BRNID, String cntrNo);

}
