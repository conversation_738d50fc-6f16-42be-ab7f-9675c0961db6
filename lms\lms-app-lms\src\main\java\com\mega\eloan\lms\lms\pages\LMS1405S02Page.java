/* 
 * LMS1405S02Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.panels.LMSL140M01MPanel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel01;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel02;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel03;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel04;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel05;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel06;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel07;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel08;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel09;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel10;
import com.mega.eloan.lms.model.L120M01A;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 額度明細表主要內容
 * </pre>
 * 
 * @since 2012/02/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/02/21,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1400m01")
public class LMS1405S02Page extends AbstractEloanForm {
	
	@Autowired
	CLSService clsService;
	
	@Override
	public void execute(ModelMap model, PageParameters params) {
		renderJsI18N(LMSL140M01MPanel.class);
		renderJsI18N(LMS1405S02Page.class);

		L120M01A l120m01a = null;
		if (true) {
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			if (Util.isNotEmpty(mainId)) {
				l120m01a = clsService.findL120M01A_mainId(mainId);
			}
		}

		new LMS1405S02Panel01("_LMS140PanelC_2_1").processPanelData(model, params);
		renderJsI18N(LMS1405S02Panel01.class);
		new LMS1405S02Panel02("_LMS140PanelC_2_2", l120m01a).processPanelData(model, params);
		renderJsI18N(LMS1405S02Panel02.class);
		new LMS1405S02Panel03("_LMS140PanelC_2_3").processPanelData(model, params);
		new LMS1405S02Panel04("_LMS140PanelC_2_4").processPanelData(model, params);
		renderJsI18N(LMS1405S02Panel04.class);
		new LMS1405S02Panel05("_LMS140PanelC_2_5", l120m01a).processPanelData(model, params);
		renderJsI18N(LMS1405S02Panel05.class);
		new LMS1405S02Panel06("_LMS140PanelC_2_6").processPanelData(model, params);
		new LMS1405S02Panel07("_LMS140PanelC_2_7").processPanelData(model, params);
		new LMS1405S02Panel08("_LMS140PanelC_2_8").processPanelData(model, params);
		new LMS1405S02Panel09("_LMS140PanelC_2_9").processPanelData(model, params);
		new LMS1405S02Panel10("_LMS140PanelC_2_10").processPanelData(model, params);

		new LMSL140M01MPanel("LMSL140M01MPanel").processPanelData(model, params);
	}

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}

	@Override
	protected String getViewName() {
		return "common/pages/None";
	}

}
