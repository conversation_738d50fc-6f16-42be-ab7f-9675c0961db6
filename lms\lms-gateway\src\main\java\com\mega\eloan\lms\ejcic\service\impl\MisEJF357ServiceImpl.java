/* 
 * MisEJF357ServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service.impl;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.mega.eloan.lms.ejcic.service.MisEJF357Service;

/**
 * <pre>
 * BAM303->EJV35701->EJF357授信保證資料(B31之從債務資料)
 * </pre>
 * 
 * @since 2012/3/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/3/22,<PERSON>,new
 *          </ul>
 */
@Service
public class MisEJF357ServiceImpl extends AbstractEjcicJdbc implements
		MisEJF357Service {

//	@Override
//	public Map<String, Object> getLoanAmtById(String custId) {
//		return getJdbc().queryForMap("BAM303.getLoanAmtById", new String[]{custId});
//	}
//
//	@Override
//	public Map<String, Object> getLoanAmtByIdAndDate(String compId, String yyy,
//			String mm) {
//		return getJdbc().queryForMap("BAM303.getLoanAmtByIdAndDate",
//				new String[] { compId, yyy, mm });
//	}
//	
//	@Override
//	public Map<String, Object> getPassDueAmtById(String compId) {
//		return getJdbc().queryForMap("BAM303.getPassDueAmtById",
//				new String[] { compId });
//	}
//
	@Override
	public List<Map<String, Object>> findGuaranteeDebt(String id, String prodId) {
		return getJdbc().queryForList("BAM303.findGuaranteeDebt", new String[]{id, prodId});
	}
//	
//	@Override
//	public List<Map<String, Object>> findLoanAmountAndBalance(String qDate,
//			List<String> compIds, List<String> bcs) {
//		String sql = getSqlBySqlId("BAM303.findLoanAmountAndBalance");
//		String compIdSql = StringUtils.repeat("?,", compIds.size());
//		String bcSql = StringUtils.repeat("?,", bcs.size());
//		sql = MessageFormat.format(
//				sql,
//				new Object[] {
//						compIdSql.substring(0, compIdSql.lastIndexOf(',')),
//						bcSql.substring(0, bcSql.lastIndexOf(',')) });
//		List<String> args = new ArrayList<String>();
//		args.addAll(compIds);
//		//#1565引金融機構往來
////		args.add(qDate);
//		for (int i = 0; i < 2; i++) {
//			args.addAll(bcs);
//		}
//		return getJdbc().queryForList(sql, args.toArray());
//	}
}
