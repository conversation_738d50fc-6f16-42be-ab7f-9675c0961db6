package com.mega.eloan.lms.eai.service;

import org.kordamp.json.JSONArray;

import tw.com.iisi.cap.exception.CapMessageException;

public abstract interface EAIService {
	public abstract JSONArray findLaw44RpsById(String paramString, String mainId)
			throws CapMessageException;

	public abstract JSONArray findCURIQ01ById(String custId, String mainId)
			throws CapMessageException;

	public JSONArray checkBankAndLaw44REByIdDup(String custId, String dupNo,
			String mainId) throws CapMessageException;
	
	public JSONArray doIPSCO01(String relId, String eName, String compare, String check, String lawNo, String nCode) throws CapMessageException;
	
	public JSONArray findStakeholderInfoByFinancialHoldingAct(String mainId, String custId, String dupNo) throws CapMessageException;

	public JSONArray findLawRpsById(String mainId, String custId, String dupNo) throws CapMessageException;
}