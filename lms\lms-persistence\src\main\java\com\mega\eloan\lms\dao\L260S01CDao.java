/* 
 * L260S01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L260S01C;

/** 貸後管理實價登錄檔 **/
public interface L260S01CDao extends IGenericDao<L260S01C> {

	L260S01C findByOid(String oid);
	
	List<L260S01C> findByMainId(String mainId, boolean notIncDel);

	List<L260S01C> findByIndex01(String mainId, String cntrNo);

	List<L260S01C> findByIndex02(String raspFileOid, boolean notIncDel);

	List<L260S01C> findByIndex03(String mainId, String refMainId, boolean notIncDel);
}