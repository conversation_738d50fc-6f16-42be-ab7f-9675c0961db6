package com.mega.eloan.lms.cls.handler.form;

import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.keyvalue.MultiKey;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.http.client.ClientProtocolException;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.aml.AmlStrategy;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.gwclient.Brmp003I;
import com.mega.eloan.common.gwclient.Brmp003O;
import com.mega.eloan.common.gwclient.Brmp004I;
import com.mega.eloan.common.gwclient.Brmp004O;
import com.mega.eloan.common.gwclient.Brmp005I;
import com.mega.eloan.common.gwclient.Brmp005O;
import com.mega.eloan.common.gwclient.BrmpGwClient;
import com.mega.eloan.common.gwclient.EJCICGwClient;
import com.mega.eloan.common.gwclient.EJCICGwReqMessage;
import com.mega.eloan.common.gwclient.ETCHGwClient;
import com.mega.eloan.common.gwclient.ETCHGwReqMessage;
import com.mega.eloan.common.gwclient.IdentificationCheckGwClient;
import com.mega.eloan.common.gwclient.IdentificationCheckGwReqMessage;
import com.mega.eloan.common.gwclient.PLOAN001;
import com.mega.eloan.common.gwclient.PLOAN002;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.ElAml;
import com.mega.eloan.common.model.ElAmlItem;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.WiseNewsService;
import com.mega.eloan.common.utils.BeanValidator;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.Score;
import com.mega.eloan.lms.base.constants.ScoreCardLoan;
import com.mega.eloan.lms.base.constants.ScoreNotHouseLoan;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.panels.CLS1131S01Panel;
import com.mega.eloan.lms.cls.report.CLS1131R03RptService;
import com.mega.eloan.lms.cls.report.CLS1131R04RptService;
import com.mega.eloan.lms.cls.report.CLS1131R05RptService;
import com.mega.eloan.lms.cls.report.CLS1131R06RptService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.dao.C101M01ADao;
import com.mega.eloan.lms.dao.C101S01ADao;
import com.mega.eloan.lms.dao.C101S01BDao;
import com.mega.eloan.lms.dao.C101S01CDao;
import com.mega.eloan.lms.dao.C101S01EDao;
import com.mega.eloan.lms.dao.C101S01GDao;
import com.mega.eloan.lms.dao.C101S01G_NDao;
import com.mega.eloan.lms.dao.C101S01QDao;
import com.mega.eloan.lms.dao.C101S01Q_NDao;
import com.mega.eloan.lms.dao.C101S01RDao;
import com.mega.eloan.lms.dao.C101S01R_NDao;
import com.mega.eloan.lms.dao.C101S01SDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eai.service.EAIService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.etch.service.EtchService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01C;
import com.mega.eloan.lms.model.C101S01D;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01F;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01G_N;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01I;
import com.mega.eloan.lms.model.C101S01J;
import com.mega.eloan.lms.model.C101S01O;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01Q_N;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C101S01R_N;
import com.mega.eloan.lms.model.C101S01S;
import com.mega.eloan.lms.model.C101S01U;
import com.mega.eloan.lms.model.C101S01V;
import com.mega.eloan.lms.model.C101S01W;
import com.mega.eloan.lms.model.C101S01X;
import com.mega.eloan.lms.model.C101S01Y;
import com.mega.eloan.lms.model.C101S01Z;
import com.mega.eloan.lms.model.C101S02A;
import com.mega.eloan.lms.model.C101S02B;
import com.mega.eloan.lms.model.C101S02C;
import com.mega.eloan.lms.model.C101S02S;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01G;
import com.mega.eloan.lms.model.C120S01O;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01R;
import com.mega.eloan.lms.model.C120S01X;
import com.mega.eloan.lms.model.C120S01Y;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01B;
import com.mega.eloan.lms.model.C900M01J;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.eloan.lms.model.L120S01N;
import com.mega.eloan.lms.model.L120S01O;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L120S19A;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.Check2;
import com.mega.eloan.lms.validation.group.Check3;
import com.mega.eloan.lms.validation.group.ImportCheck;
import com.mega.eloan.lms.validation.group.SaveCheck;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.service.MegaSSOUserService;
import com.mega.sso.userdetails.MegaSSOUserDetails;
import com.mega.sso.util.IMegaSSOUtil;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.iisi.cap.utils.CapEntityUtil;
import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.JsonMapper;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          <li>2013/06/15,Fantasy,預設不登錄配偶資料
 *          <li>2013/06/17,Fantasy,儲存重新評等時取消刪除C101S01G
 *          <li>2013/06/18,Fantasy,queryRelatedData add save all data
 *          <li>2013/06/19,Fantasy,saveCust add 個金相關查詢結果檔(C101S01J)
 *          <li>2013/07/10,Fantasy,個人負債比率和家庭負債比率 Interger改為BigDecimal
 *          <li>2013/07/10,Fantasy,引進借款人資料修改取得戶籍地址
 *          <li>2013/07/29,Rex,修改先驗證是否超過DB欄位再驗證其他規則
 *          </ul>
 */
@Scope("request")
@Controller("cls1131formhandler")
public class CLS1131FormHandler extends AbstractFormHandler {
	private static final int MAXLEN_C120M01A_CUSTNAME = StrUtils
			.getEntityFileldLegth(C120M01A.class, "custName", 120);
	private static final int MAXLEN_C101S01E_ENAME = StrUtils
			.getEntityFileldLegth(C101S01E.class, "eName", 120);
	private static final int MAXLEN_C101S01G_ADJUSTREASON = StrUtils
			.getEntityFileldLegth(C101S01G.class, "adjustReason", 300);
	private static final int MAXLEN_C101S01G_POSNAME = StrUtils
			.getEntityFileldLegth(C101S01G.class, "posName", 30);
	private static final int MAXLEN_C101S01Q_ADJUSTREASON = StrUtils
			.getEntityFileldLegth(C101S01Q.class, "adjustReason", 300);

	private static final int MAXLEN_C101S01B_EXPERIENCE = StrUtils
			.getEntityFileldLegth(C101S01B.class, "experience", 250);

	private static final int MAXLEN_C101S01B_JUID = StrUtils
			.getEntityFileldLegth(C101S01B.class, "juId", 11);

	private static final int MAXLEN_C101S01E_LAAOFFICEID = StrUtils.getEntityFileldLegth(C101S01E.class, "laaOfficeId", 10);
	
	private static final Logger logger = LoggerFactory
			.getLogger(CLS1131FormHandler.class);
	
	private static final DateFormat S_FORMAT = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
	
	private static final String EJCIC_BAM095_CONTRACT_CODE = "99999999999999999999999999999999999999999999999999";

	@Resource
	CLS1220Service cls1220Service;

	@Resource
	AMLRelateService amlRelateService;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	EloandbBASEService eloandbbaseservice;

	@Resource
	MisdbBASEService misBaseService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	EjcicService ejcicService;

	@Resource
	CLS1131Service cls1131Service;

	@Resource
	EtchService etchService;

	@Resource
	ScoreService scoreService;

	@Resource
	LMSService lmsService;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	CLSService clsService;

	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	CLS1131R03RptService cls1131R03RptService;

	@Resource
	CLS1131R04RptService cls1131R04RptService;

	@Resource
	MegaSSOUserService megaSSOUserService;

	@Resource
	CLS1131R05RptService cls1131R05RptService;

	@Resource
	CLS1131R06RptService cls1131R06RptService;

	@Resource
	EAIService eaiService;

	@Resource
	SysParameterService sysparamService;
	
	@Resource
	EJCICGwClient ejcicClient;

	@Resource
	ETCHGwClient etchClient;
	
	@Resource
	AuthService au;
	
	Properties prop;

	Properties prop_LMSCommomPage = MessageBundleScriptCreator
			.getComponentResource(LMSCommomPage.class);
	Properties prop_AbstractOverSeaCLSPage = MessageBundleScriptCreator
			.getComponentResource(AbstractOverSeaCLSPage.class);
	Properties prop_CLS1131S01Panel = MessageBundleScriptCreator
			.getComponentResource(CLS1131S01Panel.class);

	@Resource
	RPAProcessService rpaProcessService;

	@Resource
	IdentificationCheckGwClient identificationCheckGwClient;
	
	@Resource
	CLS1141Service cls1141Service;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	BrmpGwClient brmpGwClient;
	
	@Resource
	MisMislnratService misMislnratService;

	@Resource
	WiseNewsService wiseNewsService;

	@Resource
	CLS1220Service cls122Service;
	
	@Resource
	C101M01ADao c101m01aDao;

	@Resource
	C101S01ADao c101s01aDao;

	@Resource
	C101S01BDao c101s01bDao;

	@Resource
	C101S01CDao c101s01cDao;

	@Resource
	C101S01EDao c101s01eDao;
	
	@Resource
	C101S01GDao c101s01gDao;
	
	@Resource
	C101S01QDao c101s01qDao;
	
	@Resource
	C101S01RDao c101s01rDao;
	
	@Resource
	C101S01G_NDao c101s01g_nDao;
	
	@Resource
	C101S01Q_NDao c101s01q_nDao;
	
	@Resource
	C101S01R_NDao c101s01r_nDao;

	@Resource
	C101S01SDao c101s01sDao;

	@Resource
	ProdService prodService;
	
	@Autowired
	HttpServletRequest httpServletRequest;

	/**
	 * 新增借保人資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult addCust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		boolean naturalFlag = LMSUtil.check2(custId); // 是否為自然人

		JSONObject addJson = new JSONObject();
		addJson.put(EloanConstants.MAIN_ID, IDGenerator.getUUID());
		addJson.put("ownBrId", user.getUnitNo());
		addJson.put("custId", custId);
		addJson.put("dupNo",
				Util.isEmpty(dupNo) ? UtilConstants.Mark.ZEROISNODATA : dupNo);
		addJson.put("custName", custName);
		// default value
		addJson.put("markModel", UtilConstants.L140S02AModelKind.免辦);// 先填入default,讓經辦選擇[房貸、非房貸、免辦]
		addJson.put("mateFlag", ClsConstants.MateFlag.不登錄配偶資料);
		addJson.put("chkFlag", ClsConstants.Default.是否填列);
		addJson.put("isFromOld", UtilConstants.DEFAULT.否);
		addJson.put("randomCode", IDGenerator.getUUID());
		if (naturalFlag) {
			addJson.put("typCd", TypCdEnum.DBU.getCode());
			addJson.put("ntCode", ClsConstants.Default.國別);
			addJson.put("payCurr", ClsConstants.Default.幣別);
			addJson.put("othCurr", ClsConstants.Default.幣別);
			addJson.put("oMoneyCurr", ClsConstants.Default.幣別);
			addJson.put("yFamCurr", ClsConstants.Default.幣別);
			addJson.put("invMBalCurr", ClsConstants.Default.幣別);
			addJson.put("invOBalCurr", ClsConstants.Default.幣別);
			addJson.put("branCurr", ClsConstants.Default.幣別);
			addJson.put("fincomeCurr", ClsConstants.Default.幣別);
			addJson.put("mPayCurr", ClsConstants.Default.幣別);
			addJson.put("naturalFlag", UtilConstants.DEFAULT.是);
			addJson.put("importFlag", UtilConstants.DEFAULT.否);
		} else {
			addJson.put("typCd", this.checkTypCd(custId));
			addJson.put("naturalFlag", UtilConstants.DEFAULT.否);
			addJson.put("importFlag", UtilConstants.DEFAULT.是);
		}

		// 新增借保人時，由經辦輸入ID後系統先查詢該資料庫中在該分行是否已有同一ID之借款人基本資料，
		// 如有則主動帶出後供其修改，無則新增一筆
		C101M01A c101m01a = cls1131Service.findC101M01A(user.getUnitNo(),
				custId, dupNo);
		if (c101m01a == null) {
			List<GenericBean> list = new ArrayList<GenericBean>();

			Class<?>[] clazzs = { C101M01A.class, C101S01A.class,
					C101S01B.class, C101S01C.class, C101S01D.class,
					C101S01E.class, C101S01F.class, C101S01G.class,
					C101S01Q.class };
			for (Class<?> clazz : clazzs) {
				if (LMSUtil.disableC101S01F_C120S01F()
						&& clazz == C101S01F.class) {
					continue;
				}
				GenericBean model = cls1131Service.findModelByOid(clazz,
						UtilConstants.Mark.SPACE, true);
				DataParse.toBean(addJson, model);
				if(clazz == C101S01A.class){					
					// Map<String, Object> latestData = iCustomerService.findByIdDupNo(custId, dupNo); ==> 此 method 未回傳 生日  
					Map<String, Object> latestData = cls1131Service.findMisCustData(custId, dupNo);
					if(MapUtils.isNotEmpty(latestData)){
						Object birthDt_obj = MapUtils.getObject(latestData, "BIRTHDT");
						if(birthDt_obj instanceof Date){
							Date birthDt = (Date)birthDt_obj;
							if(birthDt!=null){
								((C101S01A)model).setBirthday(birthDt);
							}	
						}						
					}
				}
				list.add(model);
			}
			// 儲存
			cls1131Service.save(list);
			// 重新查詢
			c101m01a = cls1131Service.findC101M01A(user.getUnitNo(), custId,
					dupNo);
		} else {
			result.set("haveData", true);
		}

		result.set("C101M01A", DataParse.toResult(c101m01a));
		return result;
	}// ;

	/**
	 * 因應某些項目, 由S01AFORM 移至 M01AFORM
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	private JSONObject commonJSON(PageParameters params) throws CapException {
		Map<String, String> m = new HashMap<String, String>();
		if (true) {
			String c101m01aform = Util.trim(params.getString("C101M01AForm"));
			if (Util.isNotEmpty(c101m01aform)) {
				JSONObject c101m01aJSON = DataParse.toJSON(c101m01aform);
				if (c101m01aJSON != null) {
					String ntCode = Util.trim(c101m01aJSON.optString("ntCode"));
					if (Util.isNotEmpty(ntCode)) {
						m.put("ntCode", ntCode);
					}
				}
			}
		}
		if (m.size() == 0) {
			return null;
		}

		JSONObject commonJSON = new JSONObject();
		for (String k : m.keySet()) {
			commonJSON.put(k, m.get(k));
		}
		return commonJSON;
	}

	/*
		目前的 check 太多樣
		(1)前端輸入資料長度 超過 DB column 長度
			● 造成 JPA Exception
			
		(2)依[台灣自然人、法人或非台灣自然人]去檢核必填欄位 
			# SaveCheck.class  
			# Check2.class{當C101S01A,C101S01B：模型評等的因子欄位}{當C101S01E：聯徵/票信查詢日&資料日}
			# Check3.class{本行資料庫、外來資料庫}
			
			● BeanValidator.isValid(model, ?);
			● BeanValidator.getValidMsg(model, ?, ?);
			
		(3)多個欄位之間，互有關聯的檢核，例如：
			● 個人年收入+配偶年收入 vs 夫妻年收入
			● 地政士的註記是X，則欄位Y為必填
	 */
	private void saveCust_checkInputVal_outOfRange(PageParameters params)
	throws CapMessageException{
		if(true){ //C101S01E
			String className = "C101S01E";
			String form = Util.trim(params.getString(className + "Form"));
			C101S01E model = new C101S01E();
			DataParse.toBean(form, model);
			
			String laaOfficeId = Util.trim(model.getLaaOfficeId());
			if (Util.isNotEmpty(laaOfficeId)
					&& !Util.equals(laaOfficeId, Util.truncateString(laaOfficeId, MAXLEN_C101S01E_LAAOFFICEID))){							
				throw new CapMessageException(getI18nMsg("C101S01E.laaOfficeId")+"["+laaOfficeId+"]長度超過 "+MAXLEN_C101S01E_LAAOFFICEID, getClass());				
			}
		}		
	}
	
	private void saveCust_checkInputVal_clsJob(PageParameters params)
	throws CapMessageException{
		if(true){ //C101M01A , C101M01B
			String classNameC101M01A = "C101M01A";
			String formC101M01A = Util.trim(params.getString(classNameC101M01A + "Form"));
			C101M01A c101M01A = new C101M01A();
			DataParse.toBean(formC101M01A, c101M01A);
			
			
			String classNameC101S01B = "C101S01B";
			String formC101S01B = Util.trim(params.getString(classNameC101S01B + "Form"));
			C101S01B c101S01B = new C101S01B();
			DataParse.toBean(formC101S01B, c101S01B);
			
			//勾選非房貸申請信用評等:專案信貸(非團體)
			if(c101M01A.getMarkModel().contains("3")){
				if(Util.isEmpty(c101S01B.getClsJobType1())){
					throw new CapMessageException("本案包含歡喜信貸「非房貸申請信用評等:專案信貸(非團體)」信用評等，請選擇歡喜信貸行業別及職位別。", getClass());	
				}
				if(Util.isEmpty(c101S01B.getClsJobType2())){
					throw new CapMessageException("本案包含歡喜信貸「非房貸申請信用評等:專案信貸(非團體)」信用評等，請選擇歡喜信貸行業別及職位別。", getClass());	
				}
				if(Util.isEmpty(c101S01B.getClsJobTitle())){
					throw new CapMessageException("本案包含歡喜信貸「非房貸申請信用評等:專案信貸(非團體)」信用評等，請選擇歡喜信貸行業別及職位別。", getClass());	
				}
				//檢查非營利統編則一填寫
				if(!c101S01B.getIsNPO().equals("Y")){
					//沒有勾的情況。
					if(Util.isEmpty(c101S01B.getJuId())){
						throw new CapMessageException("服務單位統一編號  輸入資料不完整", getClass());	
					}
					if(Util.isEmpty(c101S01B.getJuPaidUpCapital())){
						throw new CapMessageException("服務單位實收資本額  輸入資料不完整", getClass());	
					}
				}
				
			}
			
			
		}		
	}
	
	private void saveCust_checkInputVal_shoultNotModify(PageParameters params, String mainId)
	throws CapMessageException{
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		C101S01B db_c101s01b = clsService.findC101S01B(c101m01a);
		List<String> error_list = new ArrayList<String>();
		if(true){ //C101S01B
			String className = "C101S01B";
			String form = Util.trim(params.getString(className + "Form"));
			C101S01B model = new C101S01B();
			DataParse.toBean(form, model);
	
			/*
			  	在 js 的不同寫法
			  	[X]  $('#C101S01BForm').setValue(response.C101S01BForm, false);
			  	[O]  $('#C101S01BForm').injectData(response.C101S01BForm);
			  	有碰到  othAmt 未正常 update 前端UI的欄位值 
			  	=> 在 server-side 增加檢核
			 */
			if(clsService.is_function_on_codetype("cls1131_saveCust_checkInputVal_shoultNotModify")){
				
				if(true){
					String uiValue = LMSUtil.pretty_numStr(model.getPayAmt());
					String dbValue = LMSUtil.pretty_numStr(db_c101s01b.getPayAmt());
					if(!Util.equals(uiValue, dbValue)){
						error_list.add("年薪(經常性收入){畫面上資料："+uiValue+"}應為{"+dbValue+"}");
					}
				}
				if(true){
					String uiValue = LMSUtil.pretty_numStr(model.getOthAmt());
					String dbValue = LMSUtil.pretty_numStr(db_c101s01b.getOthAmt());
					if(!Util.equals(uiValue, dbValue)){
						error_list.add("其他收入(非經常性收入){畫面上資料："+uiValue+"}應為{"+dbValue+"}");
					}
				}
			}
		}	
		if(error_list.size()>0){
			throw new CapMessageException(StringUtils.join(error_list, "<br/>"), getClass());
		}
	}
	
	private void saveCust_checkInputVal_seniority(PageParameters params)
	throws CapMessageException{
		if(true){ //C101S01E
			String className = "C101S01B";
			String form = Util.trim(params.getString(className + "Form"));
			
			JSONObject c101s01bForm = JSONObject.fromObject(params.getString(className + "Form"));
			String snrY = Util.trim(c101s01bForm.optString("snrY"));
			String snrM = Util.trim(c101s01bForm.optString("snrM"));
			if(Util.isEmpty(snrY)){
				throw new CapMessageException("年資-年 輸入資料不完整", getClass());
				
			}
			if(Util.isEmpty(snrM)){
				throw new CapMessageException("年資-月 輸入資料不完整", getClass());	
			}else{
				if(Util.parseInt(snrM)>=0 && Util.parseInt(snrM)<=11){
					
				}else{
					throw new CapMessageException("年資-月 請輸入 0~11 之間的數值", getClass());
				} 
			}
			//=====================
			C101S01B model = new C101S01B();
			DataParse.toBean(form, model);
			if(model.getSeniority()==null){
				throw new CapMessageException("年資前端輸入資料完整，但「組合年月」失敗", getClass());
			}else{
				BigDecimal encode_seniority = ClsUtility.seniorityYM_encode(Util.parseInt(snrY), Util.parseInt(snrM));
				if(encode_seniority.compareTo(model.getSeniority())!=0){
					throw new CapMessageException("年資前端輸入資料("+Util.parseInt(snrY)+"年"+Util.parseInt(snrM)+"月)，但「組合年月」後的值異常("+LMSUtil.pretty_numStr(model.getSeniority())+")應為("+LMSUtil.pretty_numStr(encode_seniority)+")", getClass());	
				}
			}			
		}		
	}
	
	/**
	 * 儲存借保人資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveCust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		JSONObject commonJSON = commonJSON(params);

		boolean triggerByFetchC122M01A = Util.equals(
				params.getString("triggerByFetchC122M01A"), "Y");
		
		boolean isNeedCheckCLSJobInput = Util.equals(params.getString("isNeedCheckCLSJobInput"),"Y" );
		
		if(true){
			saveCust_checkInputVal_outOfRange(params);
			saveCust_checkInputVal_shoultNotModify(params, mainId);
			if(isNeedCheckCLSJobInput){
				saveCust_checkInputVal_clsJob(params);	
			}
			if (LMSUtil.check2(custId)) {
				saveCust_checkInputVal_seniority(params);
			}
		}
		
		// 檢查是否已評等過
		boolean haveScore = false;
		if (!haveScore) {
			C101S01G c101s01g = cls1131Service.findModelByKey(C101S01G.class,
					mainId, custId, dupNo);
			if (c101s01g != null && Util.isNotEmpty(c101s01g.getGrade1())) {
				haveScore = true;
			}
		}

		String saveCheckMessage = "";
		if (!triggerByFetchC122M01A) {
			// 這裡傳入的參數 ClsUtil.C101CheckClass 只有{C101S01A, C101S01B, C101S01C, C101S01D} 沒有{C101S01E}
			String checkMessageBase = this.getCheckMessage(params,
					ClsUtil.C101CheckClass, Check.class, commonJSON);
			if (Util.isNotEmpty(checkMessageBase)) {
				throw new CapMessageException(checkMessageBase, getClass());
			}

			// 自然人檢核
			if (LMSUtil.check2(custId)) {
				// 台灣的自然人(不含 THZ0000000)，用 Annotation - SaveCheck.class
				String errChkMsg = getCheckMessage(params,
						ClsUtil.C101CheckClass, SaveCheck.class, commonJSON);

				if (Util.isNotEmpty(errChkMsg)) {
					if (haveScore) {
						// 有評分過出檢核錯誤,反之出檢核警示訊息 add by fantasy 2013/04/15
						throw new CapMessageException(errChkMsg, getClass());
					} else {
						saveCheckMessage += errChkMsg;
					}
				}
				if (LMSUtil.disableC101S01F_C120S01F()) {

				} else {
					// 檢核放款信用評分表
					String checkMessage = getCheckMessage(params,
							new Class<?>[] { C101S01F.class }, SaveCheck.class,
							commonJSON);
					if (Util.isNotEmpty(checkMessage)) {
						throw new CapMessageException(checkMessage, getClass());
					}
				}
				// 檢查存款帳戶
				result.putAll((CapAjaxFormResult) checkAccount(params));
			} else {
				// 非台灣的自然人(19810101LI)或 企業戶(EX:********、 THZ0000000)，用
				// Annotation - Check2.class
				if (true) {
					String ntCode = "";
					if (MapUtils.isNotEmpty(commonJSON)) {
						ntCode = Util.trim(commonJSON.optString("ntCode"));
					}

					if (Util.isEmpty(ntCode)) {
						// message.emptyField={0}[必填欄位]
						throw new CapMessageException(MessageFormat.format(
								getI18nMsg("message.emptyField"),
								getI18nMsg("C101S01A.ntCode")), getClass());
					}
				}

				// 執行 C101S01E 的檢核，用 Annotation - Check2.class{當C101S01A,C101S01B：模型評等的因子欄位}{當C101S01E：聯徵/票信查詢日&資料日}
				String errChkMsg = getCheckMessage(params,
						new Class<?>[] { C101S01E.class }, Check2.class,
						commonJSON);
				if (haveScore && Util.isNotEmpty(errChkMsg)) {
					// 有評分過出檢核錯誤,反之出檢核警示訊息 add by fantasy 2013/04/15
					throw new CapMessageException(errChkMsg, getClass());
				}

				// 執行 C101S01E 的檢核，包含 (A)Annotation - Check3.class{本行資料庫、外來資料庫}  及 (B)呼叫 getManualInputColumnCheckMsg(...)
				String chk_E = check_C101S01E(params, true);
				if (Util.isNotEmpty(chk_E)) {
					saveCheckMessage += chk_E;
				}
			}
		}
		//J-109-0178_10702_B1001 Web e-Loan 消金簽報書新增申貸資料核對表頁籤及列印功能
		String CheckListFlag=JSONObject.fromObject(params.getString("C101S01AForm")).getString("checkListFlag");
		//判斷自然人才需要選取申請核對表註記
		//J-109-0325_10702_B1001 移除申請資料核對表檢核
		if(LMSUtil.check2(custId) && clsService.is_function_on_codetype("chk_c120s01v")){
			if(Util.isEmpty(CheckListFlag)){
				saveCheckMessage +=MessageFormat.format(
						getI18nMsg("message.emptyField"),
						getI18nMsg("C101S01V.isCheckListFlag"));
			}
			else{
				if(CheckListFlag.equals(UtilConstants.DEFAULT.是)){
					JSONObject c101s01vjson = JSONObject.fromObject(params.getString("C101S01VForm"));
					String errorMsg = clsService.checkListIsNull2(c101s01vjson)+ clsService.checkListPrompt(c101s01vjson);
					
					if(Util.isNotEmpty(errorMsg)){
						//J-109-0178_10702_B1006 Web e-Loan 調整申請資料核對表儲存邏輯
						saveCheckMessage += errorMsg;
					}
				}
			}
		}
		// =================================================================================
		// 上面的程式，都是用 params 的內容，去檢核是否有誤
		List<GenericBean> list = new ArrayList<GenericBean>();

		// 共用的欄位
		JSONObject base = new JSONObject();
		if (true) {
			base.put(EloanConstants.MAIN_ID, mainId);
			base.put("custId", custId);
			base.put("dupNo", dupNo);
			if (MapUtils.isNotEmpty(commonJSON)) {
				base.putAll(commonJSON);
			}
		}

		boolean noChange_G = true;
		boolean noChange_Q = true;
		boolean noChange_R = true;
		String c101s01G_varVer = scoreService.get_Version_HouseLoan();
		String c101s01q_varVer = scoreService.get_Version_NotHouseLoan();
		String c101s01r_varVer = scoreService.get_Version_CardLoan();
		if (true) {
			Class<?>[] clazzs = { C101S01A.class, C101S01B.class,
					C101S01C.class, C101S01D.class, C101S01F.class,C101S01V.class, C101S01Z.class, C101S02C.class};

			for (Class<?> clazz : clazzs) {
				if (LMSUtil.disableC101S01F_C120S01F()
						&& clazz == C101S01F.class) {
					continue;
				}
				
				if(clazz == C101S01Z.class &&
						!Util.equals("Y", ClsUtil.getJson(params, C101M01A.class).optString("concentrateCredit"))){
					continue;
				}
				if(clazz == C101S02C.class &&
						!Util.equals("Y", ClsUtil.getJson(params, C101M01A.class).optString("prodKindFlag"))){
					continue;
				}
				// 將 className + "Form" 轉成 JSONObject
				JSONObject json = ClsUtil.getJson(params, clazz);

				GenericBean model = cls1131Service.findModelByKey(clazz,
						mainId, custId, dupNo, true);

				// 判斷［DB中的值 lms.C101S01B］與［html UI的欄位(EX:C101S01BForm)］ 是否有被 更改
				if (noChange_G && haveScore) {
					noChange_G = cls1131Service.unChg_RatingFactor_G(model,
							json, c101s01G_varVer);
				}
				if (noChange_Q && haveScore) {
					noChange_Q = cls1131Service.unChg_RatingFactor_Q(model,
							json, c101s01q_varVer);
				}
				if (noChange_R && haveScore) {
					noChange_R = cls1131Service.unChg_RatingFactor_R(model,
							json, c101s01r_varVer);
				}
				
				//雙軌模式下，若雙軌因子有變更也要重算
				boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
				if(scoreDoubleTrack){
					if (noChange_G && haveScore) {
						noChange_G = cls1131Service.unChg_RatingFactor_G(model,
								json, ClsScoreUtil.V3_0_HOUSE_LOAN);
					}
					if (noChange_Q && haveScore) {
						noChange_Q = cls1131Service.unChg_RatingFactor_Q(model,
								json, ClsScoreUtil.V4_0_NOT_HOUSE_LOAN);
					}
					if (noChange_R && haveScore) {
						noChange_R = cls1131Service.unChg_RatingFactor_R(model,
								json, ClsScoreUtil.V4_0_CARD_LOAN);
					}
				}
				

				if(clazz == C101S02C.class){
					if (model == null) {
//						model = new C101S02C();
					}
				}

				DataParse.toBean(json, model);
				DataParse.toBean(base, model);
				
				if(clazz == C101S01Z.class){
					//DataParse.toBean轉換為日期，故時間欄位要重新設定
					String kycUpdateTime=(String) json.get("kycUpdateTime");
					if(kycUpdateTime!=null){
						kycUpdateTime = Util.trim(kycUpdateTime).replaceAll("/", "-");
						if (Util.isNotEmpty(kycUpdateTime)) {
							model.set("kycUpdateTime",CapDate.convertStringToTimestamp(kycUpdateTime));
						}
					}
					String kycApprTime=(String) json.get("kycApprTime");
					if(kycApprTime!=null){
						kycApprTime = Util.trim(kycApprTime).replaceAll("/", "-");
						if (Util.isNotEmpty(kycApprTime)) {
							model.set("kycApprTime",CapDate.convertStringToTimestamp(kycApprTime));
						}
					}
				}
				//J-109-0178_10702_B1001 Web e-Loan 消金簽報書新增申貸資料核對表頁籤及列印功能
				if(clazz == C101S01V.class){
					List<C101S01V> c101s01v_list = cls1131Service.findC101S01VByMainid(mainId,null,custId,dupNo);
					json.remove("oid");
					if(CheckListFlag.equals("Y")){
						//J-109-0273_10702_B1002 Web e-Loan申請資料核對表分版本，一律刪除後重新新增
						if (c101s01v_list.size() > 0) {
							this.deleteCheckList(params);
						}
						for(Iterator iterator = json.keySet().iterator(); iterator.hasNext();) {
						    String key = (String) iterator.next();
						    String value = (String) json.get(key);
						    
						    C101S01V c101s01v = new C101S01V();
							c101s01v.setCustId(custId);
							c101s01v.setDupNo(dupNo);
							c101s01v.setMainId(mainId);
							c101s01v.setItems_Name(key);
							c101s01v.setItems_Value(value);
							list.add(c101s01v);
						}
					}
				}
				else{
					list.add(model); // 在此加入 [S01A,S01B,S01C,S01D,S01F]由 【form ->
					// json -> model】
				}
			}
		}
		if (true) {
			JSONObject json_C101S01X = ClsUtil.getJson(params, C101S01X.class);
			C101S01X c101s01x = clsService.findC101S01X(mainId, custId, dupNo);
			if(c101s01x==null && Util.equals("Y", ClsUtil.getJson(params, C101M01A.class).optString("isBailout4"))){
				c101s01x = new C101S01X();
				c101s01x.setMainId(mainId);
				c101s01x.setCustId(custId);
				c101s01x.setDupNo(dupNo);
			}
			if(c101s01x!=null){
				String applyQuota = json_C101S01X.optString("applyQuota", "");
				c101s01x.setApplyQuota(Util.isEmpty(applyQuota)?null:Util.parseInt(applyQuota));
				c101s01x.setApplyDate(CapDate.parseDate(json_C101S01X.optString("applyDate")));
				//=================
				if(true){
					//洗防的 高風險客戶，要把 C101S01X.flowFlag 設成 D
					String luvRiskLevel = amlRelateService.getCustLuvRiskLevel(user.getUnitNo(), custId, dupNo);
					if(Util.equals("H", luvRiskLevel)){
						c101s01x.setFlowFlag("D");		
					}
				}
				//=================
				list.add(c101s01x);
			}
		}
		// =================================================================================
		// 判斷「評等因子」是否異動，分2部份
		// 第1部份，判斷 【lms.C101S01B vs C101S01BForm】
		// 在本段註解 上方 的程式
		//
		// 第2部份，判斷「信用評等表」內 keep 的因子 【lms.C101S01G vs C101S01BForm】
		// 在本段註解 下方 的程式
		//

		// check 房貸(C101S01G)的值 和 UI 上的評等因子(EX:C101S01BForm) 是否更改過
		boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
		if (noChange_G) {
			C101S01G c101s01g = cls1131Service.findModelByKey(C101S01G.class,
					mainId, custId, dupNo);
			if (c101s01g != null) {
				if (Util.notEquals(c101s01g.getVarVer(), c101s01G_varVer)) {
					// 模型 變更版本，要重算
					noChange_G = false;
				} else {
					JSONObject uiC101S01AForm = ClsUtil.getJson(params,
							C101S01A.class);
					JSONObject uiC101S01BForm = ClsUtil.getJson(params,
							C101S01B.class);
					JSONObject uiC101S01CForm = ClsUtil.getJson(params,
							C101S01C.class);

					noChange_G = unChg_C101S01G_factor_vs_C101S01XForm_field(
							c101s01G_varVer, c101s01g, uiC101S01AForm,
							uiC101S01BForm, uiC101S01CForm);
										
					//雙軌運行中，同步檢查C101S01G_N
					if(noChange_G && scoreDoubleTrack){
						C101S01G_N c101s01g_n = cls1131Service.findModelByKey(C101S01G_N.class,
								mainId, custId, dupNo);
						noChange_G = unChg_C101S01G_N_factor_vs_C101S01XForm_field(
								ClsScoreUtil.V3_0_HOUSE_LOAN, c101s01g_n, uiC101S01AForm,
								uiC101S01BForm, uiC101S01CForm);
					}
				}
			}else{ //無評等資料
			}
		}
		
		// check 非房貸(C101S01Q)的值 和 UI 上的評等因子(EX:C101S01BForm) 是否更改過
		if (noChange_Q) {
			C101S01Q c101s01q = cls1131Service.findModelByKey(C101S01Q.class,
					mainId, custId, dupNo);
			if (c101s01q != null) {
				if (Util.notEquals(c101s01q.getVarVer(), c101s01q_varVer)) {
					// 模型 變更版本，要重算
					noChange_Q = false;
				} else {
					JSONObject uiC101S01AForm = ClsUtil.getJson(params,
							C101S01A.class);
					JSONObject uiC101S01BForm = ClsUtil.getJson(params,
							C101S01B.class);
					JSONObject uiC101S01CForm = ClsUtil.getJson(params,
							C101S01C.class);

					noChange_Q = unChg_C101S01Q_factor_vs_C101S01XForm_field(
							c101s01q_varVer, c101s01q, uiC101S01AForm,
							uiC101S01BForm, uiC101S01CForm);
					
					//雙軌運行中，同步檢查C101S01Q_N
					if(noChange_Q && scoreDoubleTrack){
						C101S01Q_N c101s01q_n = cls1131Service.findModelByKey(C101S01Q_N.class,
								mainId, custId, dupNo);
						noChange_Q = unChg_C101S01Q_N_factor_vs_C101S01XForm_field(
								ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, c101s01q_n, uiC101S01AForm,
								uiC101S01BForm, uiC101S01CForm);
					}
				}
			}else{ //無評等資料
			}
		}

		// check 卡友貸(C101S01R)的值 和 UI 上的評等因子(EX:C101S01BForm) 是否更改過
		if (noChange_R) {
			C101S01R c101s01r = cls1131Service.findModelByKey(C101S01R.class,
					mainId, custId, dupNo);
			if (c101s01r != null) {
				if (Util.notEquals(c101s01r.getVarVer(), c101s01r_varVer)) {
					// 模型 變更版本，要重算
					noChange_R = false;
				} else {
					JSONObject uiC101S01AForm = ClsUtil.getJson(params,
							C101S01A.class);
					JSONObject uiC101S01BForm = ClsUtil.getJson(params,
							C101S01B.class);
					JSONObject uiC101S01CForm = ClsUtil.getJson(params,
							C101S01C.class);

					noChange_R = unChg_C101S01R_factor_vs_C101S01XForm_field(
							c101s01r_varVer, c101s01r, uiC101S01AForm,
							uiC101S01BForm, uiC101S01CForm);
					
					//雙軌運行中，同步檢查C101S01Q_N
					if(noChange_R && scoreDoubleTrack){
						C101S01R_N c101s01r_n = cls1131Service.findModelByKey(C101S01R_N.class,
								mainId, custId, dupNo);
						noChange_R = unChg_C101S01R_N_factor_vs_C101S01XForm_field(
								ClsScoreUtil.V4_0_CARD_LOAN, c101s01r_n, uiC101S01AForm,
								uiC101S01BForm, uiC101S01CForm);
					}
				}
			}else{ //無評等資料
			}
		}

		// =================================================================================
		// 評分因子 未改變 => 直接儲存
		// 評分因子 已改變 => 需重新評等
		if (noChange_G && noChange_Q && noChange_R) {
			Class<?>[] Classes = { C101M01A.class, C101S01E.class };
			boolean quote_C101S01G = false;
			boolean quote_C101S01Q = false;
			boolean quote_C101S01R = false;
			for (Class<?> clazz : Classes) {
				GenericBean model = cls1131Service.findModelByKey(clazz,
						mainId, custId, dupNo, true);
				DataParse.toBean(ClsUtil.getJson(params, clazz), model);
				DataParse.toBean(base, model);
				list.add(model);

				if (clazz == C101M01A.class) {
					quote_C101S01G = (ClsUtil.isQuote((C101M01A) model,
							UtilConstants.L140S02AModelKind.房貸));
					quote_C101S01Q = (ClsUtil.isQuote((C101M01A) model,
							UtilConstants.L140S02AModelKind.非房貸));
					quote_C101S01R = (ClsUtil.isQuote((C101M01A) model,
							UtilConstants.L140S02AModelKind.卡友貸));
				}
			}
			if (true) {// 房貸
				C101S01G c101s01g = cls1131Service.findModelByKey(
						C101S01G.class, mainId, custId, dupNo);
				if (c101s01g != null) {
					c101s01g.setQuote(ClsUtil.fetchQuoteValue(quote_C101S01G));
					list.add(c101s01g);
				}
			}
			if (true) {// 非房貸
				C101S01Q c101s01q = cls1131Service.findModelByKey(
						C101S01Q.class, mainId, custId, dupNo);
				if (c101s01q != null) {
					c101s01q.setQuote(ClsUtil.fetchQuoteValue(quote_C101S01Q));
					list.add(c101s01q);
				}
			}
			if (true) {// 卡友貸
				C101S01R c101s01r = cls1131Service.findModelByKey(
						C101S01R.class, mainId, custId, dupNo);
				if (c101s01r != null) {
					c101s01r.setQuote(ClsUtil.fetchQuoteValue(quote_C101S01R));
					list.add(c101s01r);
				}
			}

			String chk_E = check_C101S01E(params, false);
			if (Util.isNotEmpty(chk_E) && LMSUtil.check2(custId)) {
				saveCheckMessage += (EloanConstants.HTML_NEWLINE + chk_E);
			}
			reOrangizeData_beforeSave_C101_Model(list);
			setImportFlag(list, chk_E);
			updateImportFlagWhenFromC122M01A(triggerByFetchC122M01A, list);
			cls1131Service.save(list);
		} else {
			// 重新取得評分結果
			List<GenericBean> appraiseList = appraise(params, mainId, custId,
					dupNo, c101s01G_varVer, c101s01q_varVer, c101s01r_varVer);

			if (appraiseList.isEmpty()) {
				reOrangizeData_beforeSave_C101_Model(list);
				cls1131Service.save(list);
			} else {
				list.addAll(appraiseList);

				String chk_E = check_C101S01E(params, false);
				if (Util.isNotEmpty(chk_E) && LMSUtil.check2(custId)) {
					saveCheckMessage += (EloanConstants.HTML_NEWLINE + chk_E);
				}
				reOrangizeData_beforeSave_C101_Model(list);
				setImportFlag(list, chk_E);
				updateImportFlagWhenFromC122M01A(triggerByFetchC122M01A, list);
				cls1131Service.save(mainId, custId, dupNo, list,
						C101S01H.class, C101S01I.class);
			}
		}
		// =================================================================================
		// 將 C101S01E 的資料，寫入C101S01J
		C101S01J c101s01j = write_C101S01E_toC101S01J(list, mainId, custId,
				dupNo);
		String msg_Laa = clsService.msg_Laa_html(c101s01j, prop_LMSCommomPage);
		String msg_agentPIdCmp = "";
		if (c101s01j != null
				&& Util.isNotEmpty(Util.trim(c101s01j.getAgentPIdCmp()))) {
			C900M01J c900m01j = clsService.findC900M01J_mainId(c101s01j
					.getAgentPIdCmp());
			String c101s01e_agentPId = Util.trim(c900m01j == null ? ""
					: c900m01j.getCustId());
			msg_agentPIdCmp = ClsUtil.msg_agentPIdCmp(c101s01e_agentPId,
					c900m01j, clsService.get_C900M01J_output_memo(c900m01j),
					prop_CLS1131S01Panel);
		}

		if (true) {
			if (Util.isNotEmpty(msg_Laa)) {
				saveCheckMessage += (EloanConstants.HTML_NEWLINE + OverSeaUtil
						.color_red(msg_Laa));
			}
			if (Util.isNotEmpty(msg_agentPIdCmp)) {
				saveCheckMessage += (EloanConstants.HTML_NEWLINE + OverSeaUtil
						.color_red(msg_agentPIdCmp));
			}
			// 地政士黑名單拒絕名單多筆判斷
			List<C101S01Y> c101s01ys = cls1131Service.getC101S01YList(mainId, custId, dupNo);
			for (C101S01Y c101s01y : c101s01ys) {
				String CtlFlagType = clsService.getCtlFlagType(Util
						.trim(c101s01y.getLaaCtlFlag()));
				//J-109-0251_10702_B1001 Web e-Loan 授信管理系統調整地政士懲戒紀錄選項判斷邏輯
				if (Util.equals(CtlFlagType, LMSUtil.地政士黑名單拒絕名單) 
						&& Util.notEquals(Util.trim(c101s01y.getLaaMatchRuleFlag()),UtilConstants.DEFAULT.是)  ) {
					// 嚴重
					for (GenericBean model : list) {
						if (model instanceof C101M01A) {
							C101M01A c101m01a = (C101M01A) model;
							// 不可引入 簽報書
							c101m01a.setImportFlag("N");
							clsService.daoSave(c101m01a);
							// ===
						}
					}
					break;
				}
			}
		}

		// 檢查0024-23項是否維護
		boolean isBailout4 = false;
		for (GenericBean model : list) {
			if (model instanceof C101M01A) {
				C101M01A c101m01a = (C101M01A) model;
				if ("Y".equals(c101m01a.getIsBailout4())) {
					isBailout4 = true;
				}
				break;
			}
		}

		if (true) {
			//	消金處明澤說：如果是勞工紓困4.0，那個金微信先暫不檢核002423，在主管簽報覆核時，再檢核
			if (!isBailout4) {
				// J-110-0456 消金放款之往來業務檢查，移至簽報書覆核時，這裡不檢查
				// String errMsg = clsService.check0024_23_LUV_DEPT_2(custId, dupNo);
				String errMsg = "";
				if (Util.isNotEmpty(errMsg)) {
					saveCheckMessage += (EloanConstants.HTML_NEWLINE + OverSeaUtil
							.color_red(errMsg));

					// 嚴重
					for (GenericBean model : list) {
						if (model instanceof C101M01A) {
							C101M01A c101m01a = (C101M01A) model;
							// 不可引入 簽報書
							c101m01a.setImportFlag("N");
							clsService.daoSave(c101m01a);
							// ===
						}
					}
				}
			}
		}

		if (true) {
			List<String> advChkMsg = new ArrayList<String>();
			String loanDocCheckFlag = null;
			if (true) {
				// String c101m01a_importFlag = "";
				Date c101m01a_abnormalReadDate = null;
				Date c101s01e_eJcicQDate = null;
				String c101s01a_mTel = null;
				for (GenericBean model : list) {
					if (model instanceof C101M01A) {
						C101M01A c101m01a = (C101M01A) model;
						// c101m01a_importFlag = c101m01a.getImportFlag();
						c101m01a_abnormalReadDate = c101m01a
								.getAbnormalReadDate();
						
						loanDocCheckFlag = c101m01a.getLoanDocCheckFlag();
						
					} else if (model instanceof C101S01E) {
						C101S01E c101s01e = (C101S01E) model;
						c101s01e_eJcicQDate = c101s01e.getEJcicQDate();
					} else if (model instanceof C101S01A) {
						C101S01A c101s01a = (C101S01A) model;
						c101s01a_mTel = c101s01a.getMTel();
					}
				}

				// 異常01：執行「相關資料查詢」後，不應出現這種狀態
				if (c101m01a_abnormalReadDate != null
						&& c101s01e_eJcicQDate != null
						&& LMSUtil.cmpDate(c101m01a_abnormalReadDate, "<",
								c101s01e_eJcicQDate)) {
					advChkMsg.add("查詢異常通報紀錄日期："
							+ TWNDate.toAD(c101m01a_abnormalReadDate) + " < "
							+ TWNDate.toAD(c101s01e_eJcicQDate)
							+ "，請先執行「相關資料查詢」");
				}
				
				// J-112-0310 暫停大數據風險報告查詢
				String witchFinOpenFlag = sysparamService.getParamValue("WITCHER_FIN_OPEN");
				if ("Y".equals(witchFinOpenFlag)) {
					C101S02B c101s02b = cls1131Service.findModelByKey(C101S02B.class,
							mainId, custId, dupNo);
					// 如果無資料時，自動發送查詢
					// 如果有資料時，手機號碼與查詢用的電話號碼不一致，且有聯徵查詢日期，重新查詢
					if (c101s02b == null 
							|| (c101s02b != null 
									&& !c101s01a_mTel.equals(c101s02b.getMpnum()) && c101s01e_eJcicQDate != null)) {
						cls1131Service.queryWitcherFin(mainId, custId, dupNo, c101s01a_mTel, null);
						c101s02b = cls1131Service.findModelByKey(C101S02B.class,
								mainId, custId, dupNo);
					}
					//J-111-0146 增加查詢大數據風險報告查詢，未完成不能引入
					if (cls1131Service.isNeedQueryWitcherFin(c101s01a_mTel) == true 
							&& c101s02b == null) {
						// C101S01E.WitcherFinNotDone=相關查詢資料-大數據風險報告查詢尚未完成
						advChkMsg.add(getI18nMsg("C101S01E.WitcherFinNotDone"));
					}
					//J-111-0146 增加查詢大數據風險報告查詢，檢核存檔時的手機號碼與查詢用的電話號碼是否一致
					if (!CapString.isEmpty(c101s01a_mTel) && c101s02b != null 
							&& !c101s01a_mTel.equals(c101s02b.getMpnum())) {
						// C101S01E.notSameMpnum=行動電話{0}與相關查詢資料-大數據風險報告查詢電話號碼{1}不一致，請確認是否重新查詢。
						saveCheckMessage += MessageFormat.format(
								getI18nMsg("C101S01E.notSameMpnum"), c101s01a_mTel, c101s02b.getMpnum());
					}
				}

			}

			if (advChkMsg.size() > 0) {
				// 將 msg 拋到前端
				saveCheckMessage += ("<br/>" + StringUtils.join(advChkMsg,
						"<br/>"));

				for (GenericBean model : list) {
					if (model instanceof C101M01A) {
						C101M01A c101m01a = (C101M01A) model;
						// 不可引入 簽報書
						c101m01a.setImportFlag("N");
						clsService.daoSave(c101m01a);
						// ===
					}
				}
			}
		}
		if(true){
			if(true){
				boolean hasHouseLoan = misBaseService.decide_cls_hasHouseLoan(custId, dupNo);
				
				Map<String, String > wm_map = run_DW_OTS_WM_CUST_SUMMARY(custId, dupNo, 12);
				String wm_12m_strVal = MapUtils.getString(wm_map, "newVal", "");				
				BigDecimal wm_12m = null;
				if(Util.isNotEmpty(wm_12m_strVal)){
					wm_12m = CrsUtil.parseBigDecimal(wm_12m_strVal);
				}
				for (GenericBean model : list) {
					if (model instanceof C101M01A) {
						C101M01A c101m01a = (C101M01A) model;
						c101m01a.setHasHouseLoan(hasHouseLoan?"Y":"N");
						c101m01a.setWm_12m(wm_12m);
						clsService.daoSave(c101m01a);
					}				
				}	
			}
			boolean upd_creditLoanReductFg = false;
//			for (GenericBean model : list) {
//				if (model instanceof C101M01A) {
//					C101M01A c101m01a = (C101M01A) model;
//					if(Util.equals("Y", c101m01a.getImportFlag())){
//						upd_creditLoanReductFg = true;	
//					}
//				}				
//			}
			upd_creditLoanReductFg = true;
			
			if(upd_creditLoanReductFg){
				boolean creditLoanReductFg = decide_creditLoanReductFg(list);
				for (GenericBean model : list) {
					if (model instanceof C101M01A) {
						C101M01A c101m01a = (C101M01A) model;
						c101m01a.setCreditLoanReductFg(creditLoanReductFg?"Y":"N");
						clsService.daoSave(c101m01a);
					}				
				}				
			}			
		}
		// =================================================================================
		// 將 save 後的資料，塞到 CapAjaxFormResult，供前端呈現
		CapAjaxFormResult c101s01vForm = new CapAjaxFormResult();
		boolean showSDT_G = false;
		boolean showSDT_Q = false;
		boolean showSDT_R = false;
		for (GenericBean model : list) {
			String className = Util.trim(DataParse.getEntityClass(model)
					.getSimpleName());
			CapAjaxFormResult formResult = DataParse.toResult(model);
			if (model instanceof C101S01G) {
				C101S01G c101s01g = (C101S01G) model;
				String checkItemRange = clsService.getCheckItemRange(c101s01g);
				formResult.putAll(ClsUtil.procMarkModel_G(c101s01g, "", checkItemRange));
			} else if (model instanceof C101S01Q) {
				C101S01Q c101s01q = (C101S01Q) model;
				String checkItemRange = clsService.getCheckItemRange(c101s01q);
				formResult.putAll(ClsUtil.procMarkModel_Q(c101s01q, "", checkItemRange));
			} else if (model instanceof C101S01R) {
				C101S01R c101s01r = (C101S01R) model;
				String checkItemRange = clsService.getCheckItemRange(c101s01r);
				formResult.putAll(ClsUtil.procMarkModel_R(c101s01r, "", checkItemRange));
			} else if (model instanceof C101S01E) {
				ClsUtil.set_msg_Laa(formResult, msg_Laa);
				ClsUtil.set_msg_agentPIdCmp(formResult, msg_agentPIdCmp);
				// 加上大數據風險資料
				C101S02B c101s02b = cls1131Service.findModelByKey(C101S02B.class, mainId,
						custId, dupNo);
				String[] s02bColumnNames = CapEntityUtil.getColumnName(new C101S02B());
				List<String> cols = new ArrayList<String>(Arrays.asList(s02bColumnNames));
				cols.remove("oid");
				cols.remove("mainId");
				cols.remove("custId");
				cols.remove("dupNo");
				if (c101s02b == null) {
					for (String col : cols) {
						formResult.set(col, "");
					}
				} else {
					for (String col : cols) {
						formResult.set(col, CapString.trimNull(c101s02b.get(col)));
					}
				}
			} else if (model instanceof C101S01V){
				//J-109-0178_10702_B1001 Web e-Loan 消金簽報書新增申貸資料核對表頁籤及列印功能
				C101S01V c101s01v = (C101S01V) model;
				c101s01vForm.set(c101s01v.getItems_Name(), c101s01v.getItems_Value());
			} else if (model instanceof C101S01Z){
				if (Util.isNotEmpty(model.get("kycUpdateTime"))) {
					formResult.set("kycUpdateTime", 
							S_FORMAT.format(model.get("kycUpdateTime")) );
				}
				if (Util.isNotEmpty(model.get("kycApprTime"))) {
					formResult.set("kycApprTime", 
							S_FORMAT.format(model.get("kycApprTime")) );
				}
			}else if (model instanceof C101S01G_N){
				if(scoreDoubleTrack){
					C101S01G_N c101s01g_n = (C101S01G_N) model;
					formResult.putAll(ClsUtil.procMarkModel_G_N(c101s01g_n));
					showSDT_G = true;
				}
			}else if (model instanceof C101S01Q_N){
				if(scoreDoubleTrack){
					C101S01Q_N c101s01q_n = (C101S01Q_N) model;
					formResult.putAll(ClsUtil.procMarkModel_Q_N(c101s01q_n));
					showSDT_Q = true;
				}
			}else if (model instanceof C101S01R_N){
				if(scoreDoubleTrack){
					C101S01R_N c101s01r_n = (C101S01R_N) model;
					formResult.putAll(ClsUtil.procMarkModel_R_N(c101s01r_n));
					showSDT_R = true;
				}
			}
			result.set(className + "Form", formResult);
		}
		result.set("showSDT_G", showSDT_G);
		result.set("showSDT_Q", showSDT_Q);
		result.set("showSDT_R", showSDT_R);
		result.set("mainId", mainId);
		
		if(c101s01vForm!=null){
			result.set("C101S01VForm", c101s01vForm);
		}

		// 印出儲存成功訊息!
 		StringBuilder sb = new StringBuilder();
		sb.append(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		/*
		 * saveCheckMessage 的內容，例如： s01b 服務單位 年薪[必填欄位] s01c 償債能力 夫妻年收入[必填欄位]
		 * s01e 相關查詢資料 婉卻紀錄[必填欄位]
		 */
		if (Util.isNotEmpty(saveCheckMessage)) {
			sb.append(EloanConstants.HTML_NEWLINE)
					.append(EloanConstants.HTML_NEWLINE)
					.append(saveCheckMessage);
		}

		if (true) {
			// 增加提示訊息
			if (true) {
				// 增加地政士的填寫提示
				for (GenericBean model : list) {
					if (model instanceof C101S01E) {
						C101S01E c101s01e = (C101S01E) model;
						
						List<C101S01Y> c101s01y = cls1131Service.getC101S01YList(mainId, custId, dupNo);
						for (C101S01Y s01y : c101s01y) {
							if (Util.notEquals("台內地登", s01y.getLaaWord())
									&& !Util.isEmpty(Util.trim(s01y.getLaaNo()))) {
								sb.append(EloanConstants.HTML_NEWLINE)
								.append(OverSeaUtil
										.color_red("【地政士證書字號(大部分為 台內地登)】、【地政士開業執照字號(大部分包含xx縣、xx市)】為不同欄位")
										+ EloanConstants.HTML_NEWLINE
										+ "目前於內政部>地政士開業資料查詢，第1個查詢結果畫面呈現的是【執照字號】而非【地政士證書字號】 "
										+ EloanConstants.HTML_NEWLINE
										+ "請參考 相關查詢資料> 進件來源 >"
										+ OverSeaUtil.color_red("※填寫說明 ")
										+ "填寫【地政士證書字號】");
								break;
							}
						}
					}
				}
			}
		}
	
		String alert = Util.trim(result.get("alert"));
		if (Util.isNotEmpty(alert)) {
			sb.append(EloanConstants.HTML_NEWLINE)
					.append(EloanConstants.HTML_NEWLINE).append(alert);
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
				triggerByFetchC122M01A ? "引入成功" : sb.toString());

		return result;
	}// ;

	private boolean decide_creditLoanReductFg(List<GenericBean> list ){
		for (GenericBean model : list) {
			if (model instanceof C101M01A) {
				C101M01A c101m01a = (C101M01A) model;
				
				Date startDateParam = CapDate.addMonth(CapDate.getCurrentTimestamp(), -12);
				if(c101m01a.getHoldMegaCardDt()!=null && LMSUtil.cmpDate(c101m01a.getHoldMegaCardDt(), "<=", startDateParam)){ // 本行信用卡往來滿一年
					return true;
				}
				
				if(Util.equals("Y", c101m01a.getHasHouseLoan())){ // 本行房貸戶
					return true;
				}
				
				if(c101m01a.getWm_12m()!=null && c101m01a.getWm_12m().compareTo(BigDecimal.valueOf(100))>0){ //近1年AUM≧100萬
					return true;					
				}
			}
			if (model instanceof C101S01B) {
				C101S01B c101s01b = (C101S01B) model;
				if(Util.equals("Y", c101s01b.getPtaFlag())){ // 本行薪轉戶
					return true;
				}
			}
		}
		
		return false;
	}
	
	/** 部分欄位之前在 前端畫面 有出現，但後來已隱藏(未出現) => 把欄位清空，避免「殘留值」
	 */
	private void reOrangizeData_beforeSave_C101_Model(List<GenericBean> list) {
		for (GenericBean model : list) {
			if (model instanceof C101S01B) {
				C101S01B c101s01b = (C101S01B) model;
				if(true){ //J-109-0183 移除[有/無]選項，故把欄位清空
					c101s01b.setYnJuId("");
					c101s01b.setYnJuTotalCapital("");
					c101s01b.setYnJuPaidUpCapital("");
				}
				if (Util.equals(c101s01b.getYnJuId(), "N")) {
					c101s01b.setJuId("");
				}
				if (Util.equals(c101s01b.getYnJuTotalCapital(), "N")) {
					c101s01b.setJuTotalCapital(null);
				}
				if (Util.equals(c101s01b.getYnJuPaidUpCapital(), "N")) {
					c101s01b.setJuPaidUpCapital(null);
				}
			} else if (model instanceof C101S01C) {
				C101S01C c101s01c = (C101S01C) model;
				if(true){ //J-109-0183 移除欄位: 夫妻年收入證明文件
					c101s01c.setYIncomeCert(""); 
				}
			} else if (model instanceof C101S01E) {
				C101S01E c101s01e = (C101S01E) model;
				if (Util.equals("L", c101s01e.getCaseSrcFlag())) {
					c101s01e.setCaseSrcMemo("");
				} else if (Util.equals("P", c101s01e.getCaseSrcFlag())) {
					c101s01e.setCaseSrcMemo("");
				} else if (Util.equals("O", c101s01e.getCaseSrcFlag())) {

				} else if (Util.equals("A", c101s01e.getCaseSrcFlag())) {
					c101s01e.setCaseSrcMemo("");
				} else if (Util.equals("C", c101s01e.getCaseSrcFlag())) {
					c101s01e.setCaseSrcMemo("");
				}
				// 檢核AML重大負面新聞 如有勾選"無"，若其它值也有勾選，則只設定為"無"
				String amlBadNews = c101s01e.getAmlBadNews();
				String[] array = amlBadNews.split("\\|");
				if (array.length > 1) {
					List<String> tmp = Arrays.asList(array);
					if (tmp.contains("0")) {
						c101s01e.setAmlBadNews("0");
					}
				}

			}
		}
	}

	private C101S01J write_C101S01E_toC101S01J(List<GenericBean> list,
			String mainId, String custId, String dupNo) {
		for (GenericBean model : list) {
			if (model instanceof C101S01E) {
				// saveCust add 個金相關查詢結果檔(C101S01J)
				// 把 C101S01E 的 column , copy to C101S01J

				C101S01E c101s01e = (C101S01E) model;
				C101S01J c101s01j = cls1131Service.findModelByKey(
						C101S01J.class, mainId, custId, dupNo);
				if (c101s01j != null) {
					c101s01j.setEName(c101s01e.getEName()); // 設置英文名
					c101s01j.setIsQdata12(c101s01e.getIsQdata12()); // 身分證補、換發紀錄
					c101s01j.setIsQdata15(c101s01e.getIsQdata15()); // 成年監護制度查詢紀錄
					
					// J-113-0227 配合房貸核貸成數新增檢核邏輯，個金徵信新增[B42從債務查詢－擔保品類別]
					// 第一階段僅新增欄位供經辦填寫(不檢核必填)，並沒有從聯徵收回資料
					// 所以這邊在儲存的時候要同步到c101s01j的tQryCollateralB42
					c101s01j.setQryCollateralB42(c101s01e.getIsQdata31());//聯徵B42從債務查詢－擔保品類別
					c101s01j.setQryMutualDebtB42(c101s01e.getIsQdata32());//聯徵B42共同債務查詢－擔保品類別
					c101s01j.setOver2DataPastY(c101s01e.getIsQdata33());  //借款人三年內購置不動產結案資訊，是否有近一年有二戶以上授信借貸結案紀錄
					c101s01j.setOver2DataPast3y(c101s01e.getIsQdata34()); //借款人三年內購置不動產結案資訊，是否有近三年有二戶以上授信借貸結案紀錄

					// 先清空資料
					c101s01j.setLaaCtlFlag("");
					c101s01j.setLaaMatchRuleFlag("");
					List<C101S01Y> c101s01ys = cls1131Service.getC101S01YList(mainId, custId, dupNo);
					if (!c101s01ys.isEmpty()) {
						// 儲存時重新檢查黑名單
						Map<String, Object> c900m01h = null;
						for (C101S01Y c101s01y : c101s01ys) {
							c900m01h = clsService
								.findActiveMajorC900M01HByCertNo2(
										c101s01y.getLaaYear(),
										c101s01y.getLaaWord(),
										c101s01y.getLaaNo());
							if (c900m01h == null) {
								c101s01y.setLaaCtlFlag("0");
								c101s01y.setLaaMatchRuleFlag("");
								c101s01y.setLaaQueryDate(new Date());
								cls1131Service.saveC101S01Y(c101s01y);
							} else { 
								String laaCtlFlag = MapUtils.getString(c900m01h, "CTLFLAG");
								String laaMatchRuleFlag = "";
								c101s01y.setLaaCtlFlag(laaCtlFlag);
								if(clsService.checkLaaMatchRuleFlag(c900m01h)) {
									laaMatchRuleFlag = UtilConstants.DEFAULT.是;
									c101s01y.setLaaMatchRuleFlag(laaMatchRuleFlag);
								}
								c101s01y.setLaaQueryDate(new Date());
								cls1131Service.saveC101S01Y(c101s01y);
								// 前端顯示資料
								String ctlFlagType = clsService.getCtlFlagType(Util.trim(laaCtlFlag));
								String exitS01JCtFlagType = clsService.getCtlFlagType(Util.trim(c101s01j.getLaaCtlFlag()));
								if (Util.equals(exitS01JCtFlagType, LMSUtil.地政士黑名單拒絕名單)) {
									// 1.若S01J已是地政士黑名單拒絕名單不須處理
								} else if (Util.equals(exitS01JCtFlagType, LMSUtil.地政士黑名單警示名單)
										&& (Util.equals(ctlFlagType, LMSUtil.地政士黑名單拒絕名單)
												|| Util.equals(ctlFlagType, LMSUtil.地政士黑名單警示名單))) {
									//2.若S01J已是警示名單，而S01Y為拒絕||警示時，塞入資料
									c101s01j.setLaaCtlFlag(laaCtlFlag);
								} else {
									// 其他情形時，皆塞入資料
									c101s01j.setLaaCtlFlag(laaCtlFlag);
								}
								//J-109-0251_10702_B1001 Web e-Loan 授信管理系統調整地政士懲戒紀錄選項判斷邏輯
								// 若無資料時，塞入資料
								if (CapString.isEmpty(c101s01j.getLaaMatchRuleFlag())) {
									c101s01j.setLaaMatchRuleFlag(laaMatchRuleFlag);
								}
							}
						}
					}
					if (true) {
						C900M01J c900m01j = null;
						if (Util.isNotEmpty(Util.trim(c101s01e.getAgentPId()))) {
							c900m01j = clsService
									.findActiveMajorC900M01JById(c101s01e
											.getAgentPId());
						}
						c101s01j.setAgentPIdCmp((c900m01j == null ? ""
								: c900m01j.getMainId()));
					}
					cls1131Service.save(c101s01j);

					return c101s01j;
				}
			}
		}
		return null;
	}

	/**
	 * 檢核 C101S01E (A)Annotation - Check3.class{本行資料庫、外來資料庫}  及 (B)呼叫 getManualInputColumnCheckMsg(...)
	 */
	private String check_C101S01E(PageParameters params,
			boolean _LMSUtil_check2_val_N) throws CapException {
		//========================================================
		//執行檢核 Annotation - Check3.class{本行資料庫、外來資料庫}
		String chk_E = getCheckMessage(params,
				new Class<?>[] { C101S01E.class }, Check3.class, null);

		C101S01E c101s01e = new C101S01E();
		DataParse.toBean(Util.trim(params.getString("C101S01E" + "Form")),
				c101s01e);
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		c101s01e.setMainId(mainId);
		//========================================================
		//公司戶的的檢核
		if (_LMSUtil_check2_val_N) {
			// 不論[個人戶, 公司戶], 若有缺少相關欄位
			chk_E = "尚未執行[相關資料查詢]";

			String custId = Util.trim(params.getString("custId"));
			if (Util.isNotEmpty(custId)) {
				boolean naturalFlag = LMSUtil.check2(custId); // 是否為自然人
				if (naturalFlag) {
					// still show msg
				} else {
					// 公司戶
					C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
					if (c101m01a != null) {
						if (c101m01a.getAbnormalReadDate() == null
								|| LMSUtil.qdate_expired(
										c101m01a.getAbnormalReadDate(),
										CapDate.getCurrentTimestamp())) {
							// still show msg
						} else {
							// 已做過 相關資料查詢
							chk_E = "";

							if (c101s01e.getEJcicQDate() == null
									|| c101s01e.getEChkQDate() == null
									|| LMSUtil.qdate_expired(
											c101s01e.getEJcicQDate(),
											c101s01e.getEChkQDate())) {
								chk_E = "公司戶的[聯徵查詢日期、票信查詢日期]請人工輸入";
							}
						}
					}
				}
			}
		}

		String _selfCheckMsg = getManualInputColumnCheckMsg(c101s01e);
		if (Util.isNotEmpty(_selfCheckMsg)) {
			chk_E += _selfCheckMsg;
		}
		return chk_E;
	}

	/**
	 * 為了讓線上申貸引入資料後，只出現「引入成功/失敗」，不出現其它必填欄位未輸入的狀況 多加了 triggerByFetchC122M01A 去區分
	 * 
	 * 若 triggerByFetchC122M01A==true, 會省略一些欄位的檢核, 但要把 importFlag 設成N 強迫 user
	 * 去按「儲存」，重跑一次完整的檢核 避免欄位 與[房貸/非房貸評等的內容不同]
	 */
	private void updateImportFlagWhenFromC122M01A(
			boolean triggerByFetchC122M01A, List<GenericBean> list) {
		if (triggerByFetchC122M01A) {
			C101M01A c101m01a = null;
			for (GenericBean m : list) {
				if (m instanceof C101M01A) {
					c101m01a = (C101M01A) m;
				}
			}
			if (c101m01a != null) {
				c101m01a.setImportFlag(UtilConstants.DEFAULT.否);
			}
		}
	}

	private boolean unChg_C101S01G_factor_vs_C101S01XForm_field(
			String version_G, C101S01G c101s01g, JSONObject uiC101S01AForm,
			JSONObject uiC101S01BForm, JSONObject uiC101S01CForm)
			throws CapException {
		JSONObject json_G = DataParse.toJSON(c101s01g);

		if (Util.equals(ClsScoreUtil.V1_3_HOUSE_LOAN, version_G)) {

			C101S01A _c101s01a = new C101S01A();
			C101S01B _c101s01b = new C101S01B();
			C101S01C _c101s01c = new C101S01C();
			/*
			 * C101S01G.edu <---> C101S01A.edu
			 */
			if (true) {
				DataParse.toBean(uiC101S01BForm, _c101s01b);
				DataParse.toBean(uiC101S01CForm, _c101s01c);
			}
			DataParse.toBean(json_G, _c101s01a);
			DataParse.toBean(json_G, _c101s01b);
			DataParse.toBean(json_G, _c101s01c);

			return cls1131Service.unChg_RatingFactor_G(_c101s01a,
					uiC101S01AForm, c101s01g.getVarVer())
					&& cls1131Service.unChg_RatingFactor_G(_c101s01b,
							uiC101S01BForm, c101s01g.getVarVer())
					&& cls1131Service.unChg_RatingFactor_G(_c101s01c,
							uiC101S01CForm, c101s01g.getVarVer());

		} else if (Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN, version_G) 
				|| Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN, version_G)) {
			if (diff_parseBigDecimal_val(json_G, Score.column.夫妻年收入,
					ClsUtil.get_BigDecimal(uiC101S01CForm, Score.column.夫妻年收入))) {
				return false;
			}
			if (diff_parseBigDecimal_val(json_G, Score.column.年資,
					ClsUtil.get_BigDecimal(uiC101S01BForm, Score.column.年資))) {
				return false;
			}
			if (diff_parseBigDecimal_val(json_G, Score.column.個人年所得,
					ClsUtil.get_pIncome_from_uiC101S01BForm(uiC101S01BForm))) {
				return false;
			}
			if (diff_str_val(json_G, Score.column.職稱,
					Util.trim(uiC101S01BForm.optString(Score.column.職稱)))) {
				return false;
			}
			return true;
		} else if (Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN, version_G)) {
			if (diff_parseBigDecimal_val(json_G, Score.column.v3_yRate因子,
					ClsUtil.get_BigDecimal(uiC101S01CForm, Score.column.v3_yRate因子))) {
				return false;
			}
			//學歷C101S01A
			if (diff_parseBigDecimal_val(json_G, Score.column.v3_Edu因子,
					ClsUtil.get_BigDecimal(uiC101S01AForm, Score.column.v3_Edu因子))) {
				return false;
			}
			if (diff_str_val(json_G, Score.column.v3_職稱因子,
					Util.trim(uiC101S01BForm.optString(Score.column.v3_職稱因子)))) {
				return false;
			}
			//檢查聯徵查詢日期，比對C101S01G.EJCICQDATE(C101S01E.EJCICQDATE)跟KRM040.QDATE，看日期是否相符，不符就要重算	
			//抓KRM040.QDATE最後一比QDATE
			Map<String, Object> map = ejcicService.getKRM040_MaxQdate(c101s01g.getCustId());
			String qdate = (String) MapUtils.getObject(map, "QDATE", null);
			if (diff_str_val(json_G, Score.column.聯徵查詢日期, qdate) && qdate != null) {
				return false;
			}
			
			return true;
		} else {
			return false;
		}
	}
	
	private boolean unChg_C101S01G_N_factor_vs_C101S01XForm_field(
			String version_G, C101S01G_N c101s01g_n, JSONObject uiC101S01AForm,
			JSONObject uiC101S01BForm, JSONObject uiC101S01CForm)
			throws CapException {
		JSONObject json_G = DataParse.toJSON(c101s01g_n);
		if (Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN, version_G)) {
			if (diff_parseBigDecimal_val(json_G, Score.column.v3_yRate因子,
					ClsUtil.get_BigDecimal(uiC101S01CForm, Score.column.v3_yRate因子))) {
				return false;
			}
			//學歷C101S01A
			if (diff_parseBigDecimal_val(json_G, Score.column.v3_Edu因子,
					ClsUtil.get_BigDecimal(uiC101S01AForm, Score.column.v3_Edu因子))) {
				return false;
			}
			if (diff_str_val(json_G, Score.column.v3_職稱因子,
					Util.trim(uiC101S01BForm.optString(Score.column.v3_職稱因子)))) {
				return false;
			}
			//檢查聯徵查詢日期，比對C101S01G.EJCICQDATE(C101S01E.EJCICQDATE)跟KRM040.QDATE，看日期是否相符，不符就要重算	
			//抓KRM040.QDATE最後一比QDATE
			Map<String, Object> map = ejcicService.getKRM040_MaxQdate(c101s01g_n.getCustId());
			String qdate = (String) MapUtils.getObject(map, "QDATE", null);
			if (diff_str_val(json_G, Score.column.聯徵查詢日期, qdate) && qdate != null) {
				return false;
			}
			
			return true;
		} else {
			return false;
		}
	}

	private boolean unChg_C101S01Q_factor_vs_C101S01XForm_field(
			String version_Q, C101S01Q c101s01q, JSONObject uiC101S01AForm,
			JSONObject uiC101S01BForm, JSONObject uiC101S01CForm)
			throws CapException {

		JSONObject json_q = DataParse.toJSON(c101s01q);

		if (Util.equals(ClsScoreUtil.V1_0_NOT_HOUSE_LOAN, version_Q)) {
			/*
			 * C101S01Q.education <---> C101S01A.edu
			 */
			C101S01A _c101s01a = new C101S01A();
			{
				JSONObject json_convert_a = new JSONObject();
				json_convert_a.putAll(json_q);
				Map<String, String> map_a = ClsUtil
						.map_c101s01q_c101s01a_V1_0();
				for (String q_column : map_a.keySet()) {
					String column_s01 = map_a.get(q_column);
					json_convert_a.put(column_s01, json_q.get(q_column));
				}
				DataParse.toBean(json_convert_a, _c101s01a);
			}
			C101S01B _c101s01b = new C101S01B();
			{
				JSONObject json_convert_b = new JSONObject();
				json_convert_b.putAll(json_q);
				Map<String, String> map_b = ClsUtil
						.map_c101s01q_c101s01b_V1_0();
				for (String q_column : map_b.keySet()) {
					String column_s01 = map_b.get(q_column);
					json_convert_b.put(column_s01, json_q.get(q_column));
				}
				DataParse.toBean(json_convert_b, _c101s01b);
			}
			return cls1131Service.unChg_RatingFactor_Q(_c101s01a,
					uiC101S01AForm, c101s01q.getVarVer())
					&& cls1131Service.unChg_RatingFactor_Q(_c101s01b,
							uiC101S01BForm, c101s01q.getVarVer());

		} else if (Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN, version_Q)) {
			JSONObject tmp = new JSONObject();
			ClsUtil.set_c101s01q_factor_V2_0(tmp, uiC101S01BForm);

			String[] factorArr = { ScoreNotHouseLoan.column.個人年所得,
					ScoreNotHouseLoan.column.年資 };
			for (String f : factorArr) {
				// 不能直接用 json.optInt(f)
				// 原因：在 DataParse.toJSON 會把數字的千分位，用,區隔
				// 所以當 optInt(123,456) 會傳回0

				// 值為 Null vs 值為0 的意義不同。為免把 Null 當成 0，當為Null時，回傳不可能的值[-1]
				int val_A = Util.parseInt(tmp.optString(f, "-1"));
				int val_B = Util.parseInt(json_q.optString(f, "-1"));

				if (val_A != val_B) {
					return false;
				}
			}
			return true;
		} else if (Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN, version_Q)) {
			JSONObject tmp = new JSONObject();
			ClsUtil.set_c101s01q_factor_V2_1(tmp, uiC101S01BForm);

			String[] factorArr = { ScoreNotHouseLoan.column.個人年所得,
					ScoreNotHouseLoan.column.年資 };
			for (String f : factorArr) {
				// 不能直接用 json.optInt(f)
				// 原因：在 DataParse.toJSON 會把數字的千分位，用,區隔
				// 所以當 optInt(123,456) 會傳回0

				// 值為 Null vs 值為0 的意義不同。為免把 Null 當成 0，當為Null時，回傳不可能的值[-1]
				int val_A = Util.parseInt(tmp.optString(f, "-1"));
				int val_B = Util.parseInt(json_q.optString(f, "-1"));

				if (val_A != val_B) {
					return false;
				}
			}
			return true;
		} else if (Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN, version_Q) 
				|| Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN, version_Q)) {
			if (diff_parseBigDecimal_val(json_q,
					ScoreNotHouseLoan.column.個人年所得,
					ClsUtil.get_pIncome_from_uiC101S01BForm(uiC101S01BForm))) {
				return false;
			}
			if (diff_str_val(json_q, ScoreNotHouseLoan.column.職稱,
					Util.trim(uiC101S01BForm
							.optString(ScoreNotHouseLoan.column.職稱)))) {
				return false;
			}
			if (diff_parseBigDecimal_val(json_q,
					ScoreNotHouseLoan.column.個人負債比率,
					ClsScoreUtil.get_dRate_from_uiC101S01CForm(uiC101S01CForm))) {
				return false;
			}
			return true;
		} else if (Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, version_Q)) { //J-111-0373
			if (diff_str_val(json_q,
					ScoreNotHouseLoan.column.nv4_學歷因子,
					ClsScoreUtil.get_edu_from_uiC101S01AForm(uiC101S01AForm))) {
				return false;
			}
			if (diff_parseBigDecimal_val(json_q,
					ScoreNotHouseLoan.column.nv4_dRate因子,
					ClsScoreUtil.get_dRate_from_uiC101S01CForm(uiC101S01CForm))) { //個人負債比
				return false;
			}
			
			//ClsUtil.get_BigDecimal()，這個會把空白轉成0  但該因子的空白=null，但有擔餘額的定義上，null跟0是不一樣的東西
			if(Util.equals(uiC101S01CForm.getString("loanBalSByidShow"), "N.A")){ //代表loanBalSByid因子 = null
				if (diff_parseBigDecimal_val(json_q,
						ScoreNotHouseLoan.column.nv4_loanBalSByid因子, null)) { //有擔餘額總額
					return false;
				}
			}else{
				if (diff_parseBigDecimal_val(json_q,
						ScoreNotHouseLoan.column.nv4_loanBalSByid因子,
						ClsUtil.get_BigDecimal(uiC101S01CForm, ScoreNotHouseLoan.column.nv4_loanBalSByid因子))) { //有擔餘額總額
					return false;
				}
			}
			return true;
		} else {
			return false;
		}
	}
	
	private boolean unChg_C101S01Q_N_factor_vs_C101S01XForm_field(
			String version_Q, C101S01Q_N c101s01q_n, JSONObject uiC101S01AForm,
			JSONObject uiC101S01BForm, JSONObject uiC101S01CForm)
			throws CapException {
		JSONObject json_q = DataParse.toJSON(c101s01q_n);
		if (Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, version_Q)) { //J-111-0373
			if (diff_str_val(json_q,
					ScoreNotHouseLoan.column.nv4_學歷因子,
					ClsScoreUtil.get_edu_from_uiC101S01AForm(uiC101S01AForm))) {
				return false;
			}
			if (diff_parseBigDecimal_val(json_q,
					ScoreNotHouseLoan.column.nv4_dRate因子,
					ClsScoreUtil.get_dRate_from_uiC101S01CForm(uiC101S01CForm))) { //個人負債比
				return false;
			}
			
			//ClsUtil.get_BigDecimal()，這個會把空白轉成0  但該因子的空白=null，但有擔餘額的定義上，null跟0是不一樣的東西
			if(Util.equals(uiC101S01CForm.getString("loanBalSByidShow"), "N.A")){ //代表loanBalSByid因子 = null
				if (diff_parseBigDecimal_val(json_q,
						ScoreNotHouseLoan.column.nv4_loanBalSByid因子, null)) { //有擔餘額總額
					return false;
				}
			}else{
				if (diff_parseBigDecimal_val(json_q,
						ScoreNotHouseLoan.column.nv4_loanBalSByid因子,
						ClsUtil.get_BigDecimal(uiC101S01CForm, ScoreNotHouseLoan.column.nv4_loanBalSByid因子))) { //有擔餘額總額
					return false;
				}
			}
			
			return true;
		} else {
			return false;
		}
	}

	private boolean unChg_C101S01R_factor_vs_C101S01XForm_field(
			String version_R, C101S01R c101s01r, JSONObject uiC101S01AForm,
			JSONObject uiC101S01BForm, JSONObject uiC101S01CForm)
			throws CapException {

		JSONObject json_r = DataParse.toJSON(c101s01r);

		if (Util.equals(ClsScoreUtil.V2_1_CARD_LOAN, version_R)) {
			JSONObject tmp = new JSONObject();
			ClsUtil.set_c101s01r_factor_V2_1(tmp, uiC101S01BForm);

			String[] factorArr = { ScoreCardLoan.column.個人年所得,
					ScoreCardLoan.column.年資 };
			for (String f : factorArr) {
				// 不能直接用 json.optInt(f)
				// 原因：在 DataParse.toJSON 會把數字的千分位，用,區隔
				// 所以當 optInt(123,456) 會傳回0

				// 值為 Null vs 值為0 的意義不同。為免把 Null 當成 0，當為Null時，回傳不可能的值[-1]
				int val_A = Util.parseInt(tmp.optString(f, "-1"));
				int val_B = Util.parseInt(json_r.optString(f, "-1"));

				if (val_A != val_B) {
					return false;
				}
			}
			return true;
		} else if (Util.equals(ClsScoreUtil.V3_0_CARD_LOAN, version_R) 
				|| Util.equals(ClsScoreUtil.V3_1_CARD_LOAN, version_R)) {
			if (diff_parseBigDecimal_val(json_r, ScoreCardLoan.column.個人年所得,
					ClsUtil.get_pIncome_from_uiC101S01BForm(uiC101S01BForm))) {
				return false;
			}
			if (diff_str_val(
					json_r,
					ScoreCardLoan.column.職稱,
					Util.trim(uiC101S01BForm.optString(ScoreCardLoan.column.職稱)))) {
				return false;
			}
			if (diff_parseBigDecimal_val(json_r, ScoreCardLoan.column.個人負債比率,
					ClsScoreUtil.get_dRate_from_uiC101S01CForm(uiC101S01CForm))) {
				return false;
			}
			return true;
		} else if (Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, version_R)) {
			if (diff_str_val(json_r,
					ScoreNotHouseLoan.column.nv4_學歷因子,
					ClsScoreUtil.get_edu_from_uiC101S01AForm(uiC101S01AForm))) {
				return false;
			}
			if (diff_parseBigDecimal_val(json_r,
					ScoreNotHouseLoan.column.nv4_dRate因子,
					ClsScoreUtil.get_dRate_from_uiC101S01CForm(uiC101S01CForm))) { //個人負債比
				return false;
			}
			
			//ClsUtil.get_BigDecimal()，這個會把空白轉成0  但該因子的空白=null，但有擔餘額的定義上，null跟0是不一樣的東西
			if(Util.equals(uiC101S01CForm.getString("loanBalSByidShow"), "N.A")){ //代表loanBalSByid因子 = null
				if (diff_parseBigDecimal_val(json_r,
						ScoreNotHouseLoan.column.nv4_loanBalSByid因子, null)) { //有擔餘額總額
					return false;
				}
			}else{
				if (diff_parseBigDecimal_val(json_r,
						ScoreNotHouseLoan.column.nv4_loanBalSByid因子,
						ClsUtil.get_BigDecimal(uiC101S01CForm, ScoreNotHouseLoan.column.nv4_loanBalSByid因子))) { //有擔餘額總額
					return false;
				}
			}
			
			return true;
		} else {
			return false;
		}
	}
	
	private boolean unChg_C101S01R_N_factor_vs_C101S01XForm_field(
			String version_R, C101S01R_N c101s01r_n, JSONObject uiC101S01AForm,
			JSONObject uiC101S01BForm, JSONObject uiC101S01CForm)
			throws CapException {

		JSONObject json_r = DataParse.toJSON(c101s01r_n);
		if (Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, version_R)) {
			if (diff_str_val(json_r,
					ScoreNotHouseLoan.column.nv4_學歷因子,
					ClsScoreUtil.get_edu_from_uiC101S01AForm(uiC101S01AForm))) {
				return false;
			}
			if (diff_parseBigDecimal_val(json_r,
					ScoreNotHouseLoan.column.nv4_dRate因子,
					ClsScoreUtil.get_dRate_from_uiC101S01CForm(uiC101S01CForm))) { //個人負債比
				return false;
			}
			
			//ClsUtil.get_BigDecimal()，這個會把空白轉成0  但該因子的空白=null，但有擔餘額的定義上，null跟0是不一樣的東西
			if(Util.equals(uiC101S01CForm.getString("loanBalSByidShow"), "N.A")){ //代表loanBalSByid因子 = null
				if (diff_parseBigDecimal_val(json_r,
						ScoreNotHouseLoan.column.nv4_loanBalSByid因子, null)) { //有擔餘額總額
					return false;
				}
			}else{
				if (diff_parseBigDecimal_val(json_r,
						ScoreNotHouseLoan.column.nv4_loanBalSByid因子,
						ClsUtil.get_BigDecimal(uiC101S01CForm, ScoreNotHouseLoan.column.nv4_loanBalSByid因子))) { //有擔餘額總額
					return false;
				}
			}
			
			return true;
		} else {
			return false;
		}
	}

	/*
	 * private boolean diff_parseBigDecimal_val(JSONObject json_GorQ, String
	 * json_key, BigDecimal cmpVal){ //不能直接用 json.optInt(f) //原因：在
	 * DataParse.toJSON 會把數字的千分位，用,區隔 //所以當 optInt(123,456) 會傳回0
	 * 
	 * //值為 Null vs 值為0 的意義不同。為免把 Null 當成 0，當為Null時，回傳不可能的值[-1] BigDecimal val_A
	 * = CrsUtil.parseBigDecimal(json_GorQ.optString(json_key, "-1")); return
	 * (val_A.compareTo(cmpVal)!=0); }
	 */

	/**
	 * dRate 在中鋼整批貸款, 可能是 null, 修改判斷
	 */
	private boolean diff_parseBigDecimal_val(JSONObject json_GorQ,
			String json_key, BigDecimal cmpVal) {
		// boolean dump_log = Util.equals("dRate", json_key) ||
		// Util.equals(ScoreNotHouseLoan.column.個人負債比率, json_key);
		// if(dump_log){
		// logger.debug("_diff_parseBigDecimal_val_step1[json_key="+json_key+"][optVal="+json_GorQ.optString(json_key,
		// "null")+"][cmpVal="+cmpVal+"]");
		// }
		// ===============================================
		boolean nullJsonVal = false;
		if (json_GorQ.containsKey(json_key)) {
			nullJsonVal = (json_GorQ.opt(json_key) == null);
		} else {
			nullJsonVal = true;
		}
		// ===============================================
		boolean is_diff = true;
		if (cmpVal == null) {
			if (nullJsonVal) {
				is_diff = false; // 兩者都是 null ---> diff為false
			} else {
				is_diff = true;
			}
		} else {
			if (nullJsonVal) {
				is_diff = true;
			} else {
				// 兩者都非null

				// 不能直接用 json.optInt(f)
				// 原因：在 DataParse.toJSON 會把數字的千分位，用,區隔
				// 所以當 optInt(123,456) 會傳回0
				BigDecimal val_A = CrsUtil.parseBigDecimal(json_GorQ.optString(
						json_key, "-1"));
				is_diff = (val_A.compareTo(cmpVal) != 0);
			}
		}
		// ===============================================
		// if(dump_log){
		// logger.debug("_diff_parseBigDecimal_val_step2[json_key="+json_key+"][nullJsonVal="+nullJsonVal+"][is_diff="+is_diff+"]");
		// }
		// ===============================================
		return is_diff;
	}

	private boolean diff_str_val(JSONObject json_GorQ, String json_key,
			String cmpVal) {
		String val_A = Util.trim(json_GorQ.optString(json_key));
		return Util.notEquals(val_A, cmpVal);
	}

	/**
	 * 刪除借保人資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteCust(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		List<GenericBean> list = new ArrayList<GenericBean>();
		for (Class<?> clazz : LMSUtil.C101Class) {
			List<? extends GenericBean> beans = cls1131Service
					.findListByRelationKey(clazz, mainId, custId, dupNo);
			for (GenericBean model : beans) {
				list.add(model);
			}
		}
		for (Class<?> clazz : LMSUtil.C101_NClass) {
			List<? extends GenericBean> beans = cls1131Service
					.findListByRelationKey(clazz, mainId, custId, dupNo);
			for (GenericBean model : beans) {
				list.add(model);
			}
		}
		// 刪除
		cls1131Service.delete(list);

		return result;
	}// ;

	/**
	 * 以｛共用 method｝來判斷，是否電銷中心  
	 */
	private boolean is_PersonalLoan_callCenter(MegaSSOUserDetails user){
		/* J-111-0169 , (111)第(1066)號 , EX00-電銷經辦與EX01-電銷主管之子系統權限
		  */
		if(user.isEXAuth()){ //是否僅有電銷權限 (EX00 或 EX01)	
//			if(Util.equals("943", user.getUnitNo())){
//				return true;	
//			}	
		}
		return false;
	}
	
	private String get_cbdeptid_brNoLen3_for_PersonalLoan_callCenter(MegaSSOUserDetails user){
		// J-111-0061 配合電銷中心信貸集中徵信，產生簽報書在 givenBrNo，最後 EJ 的費用由229去和電銷中心拆帳
		if(is_PersonalLoan_callCenter(user)){
			return "229";
		}
		return user.getUnitNo();
	}
	
	/*
	      可以	INSERT INTO com.bcodetype (OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) 
	  VALUES (get_oid(),'param_CLS1131_oneBtnQuery','openWay','A','zh_TW','','',1,'sys', current timestamp);
	  => 讓 openWay="A" 去 popup windows, 看網址列的 url 參數
	  ================
	     送到[EJ,ETCH]的參數，可由 gwlog 查到
	  	select clientip, serviceId, txid, msgid, b.* from (
	  		select sno,clientip,serviceId, txid,msgid from com.bgwlog 
	  		where serviceid in ('ETCH','EJCIC') 
	  		and reqtime between '2022-07-25 13:20:00' and '2022-07-25 13:38:00' 
	  	) a left outer join com.bgwdata b on a.sno=b.logsno 
	 */
	private String get_cbdeptid_totLen4_BrNo_ChkNo(String given_cbdeptid){
		IBranch iBranch_cbdeptid = branchService.getBranch(given_cbdeptid);
		if(iBranch_cbdeptid!=null){
			return iBranch_cbdeptid.getBrNo() + iBranch_cbdeptid.getChkNo();	
		}
		return "";
	}
	
	/**
	 * 傳送借保人資料至分行
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult sendCust(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean is_PersonalLoan_callCenter = is_PersonalLoan_callCenter(user);
		
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String brno = Util.trim(params.getString("brno"));
		String newMainId = IDGenerator.getUUID();

		C101M01A c101m01a = cls1131Service.findC101M01A(brno, custId, dupNo);
		if (c101m01a == null) {
			//J-111-0061 配合電銷中心信貸集中徵信，由943傳送至分行			
			if(is_PersonalLoan_callCenter){
				/*
				可參考 簽報書引入借款人 的寫法 cls1141formhandler :: importLendCollateral(?)
				會去呼叫 lmsService.copyC101toC120(mainId, rows)
				(1)處理 LMSUtil.C101Class
				(2)處理 「授信信用風險管理」遵循檢核 ( L120S01M,L120S01N,L120S01O )
				(3)處理 C101S04W RPA發查明家事公告細檔(除了DB的 rows以外，還有附檔的 copy)
				*/
				
			}else{			
			List<GenericBean> list = new ArrayList<GenericBean>();

			for (Class<?> clazz : LMSUtil.C101Class) {
				if (LMSUtil.disableC101S01F_C120S01F()
						&& clazz == C101S01F.class) {
					continue;
				}
				if (clazz == C101S01S.class) {
					// 因 C101S01S 會留存原分行查詢人員ID
					// 傳送時，略過
					// 新分行的經辦，自行去查詢
					continue;
				}
				if (clazz == C101S01X.class) {
					//勞工紓困評分表，因涉及送信保  ( LMS . C124M01A )，不要自動傳送 => 強迫分行重新輸入
					continue;
				}
				List<? extends GenericBean> beans = cls1131Service
						.findListByRelationKey(clazz, mainId, custId, dupNo);
				for (GenericBean bean : beans) {
					GenericBean model = cls1131Service.findModelByOid(clazz,
							null, true);
					if(model==null){
						throw new CapMessageException("lost "+clazz.getSimpleName(), getClass());
					}
					DataParse.copy(bean, model);
					// 個金徵信借款人主檔,個金信用評等表
					if (clazz == C101M01A.class || clazz == C101S01G.class
							|| clazz == C101S01Q.class
							|| clazz == C101S01R.class) {
						model.set("ownBrId", brno);
					}
					
					if(clazz == C101S02S.class){
						model.set("branchNo", brno);
					}

					if (clazz == C101S01G.class) { // 房貸
						model.set(Score.column.報表亂碼, IDGenerator.getUUID());
					}

					if (clazz == C101S01Q.class) { // 非房貸
						model.set(Score.column.報表亂碼, IDGenerator.getUUID());
					}
					if (clazz == C101S01R.class) { // 卡友貸
						model.set(Score.column.報表亂碼, IDGenerator.getUUID());
					}
					model.set(EloanConstants.OID, null);
					model.set(EloanConstants.MAIN_ID, newMainId);
					list.add(model);
				}
			}
			// 儲存
			cls1131Service.save(list);
			}
		} else {
			StringBuilder sb = new StringBuilder();
			sb.append(getI18nMsg("C101M01A.ownBrId")).append("[").append(brno)
					.append("]").append("&nbsp;")
					.append(getI18nMsg("C101M01A.custId")).append("[")
					.append(custId).append("&nbsp;").append(dupNo).append("]")
					.append(getI18nMsg("message.haveData"))
					.append(EloanConstants.HTML_NEWLINE)
					.append(getI18nMsg("message.sendAlert"));
			throw new CapMessageException(sb.toString(), getClass());
		}
		
		if(is_PersonalLoan_callCenter){
			//J-111-0061 配合電銷中心信貸集中徵信，由943傳送至分行時，不必把 importFlag 設成N（是什麼，就傳送什麼）
		}else{
			C101M01A new_c101m01a = clsService.findC101M01A_mainId(newMainId);
			if(new_c101m01a!=null){
				new_c101m01a.setImportFlag("N");
				clsService.daoSave(new_c101m01a);
			}
		}
		
		return result;
	}// ;

	private void setCm1DataBlock(C101S01B c101s01b) {
		String custId = c101s01b.getCustId();
		String dupNo = c101s01b.getDupNo();
		Map<String, Object> map = misCustdataService.findCMFCUS1ByIdAndDup(
				custId, dupNo);
		if (MapUtils.isNotEmpty(map)) {
			c101s01b.setCm1_dataDt(CapDate.getCurrentTimestamp());
			c101s01b.setCm1_serve_company(Util.trim(MapUtils.getString(map,
					"CM1_SERVE_COMPANY")));
			c101s01b.setCm1_job_business_code(Util.trim(MapUtils.getString(map,
					"CM1_JOB_BUSINESS_CODE")));
			c101s01b.setCm1_title_code(Util.trim(MapUtils.getString(map,
					"CM1_TITLE_CODE")));
			c101s01b.setCm1_job_title(Util.trim(MapUtils.getString(map,
					"CM1_JOB_TITLE")));
			// ~~~~~~~~~~~~~
			clsService.daoSave(c101s01b);
		}
	}

	@DomainAuth(AuthType.Query)
	public IResult impCm1DataBlock(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C101S01B c101s01b = (C101S01B) cls1131Service.findModelByKey(
				C101S01B.class, mainId, custId, dupNo);
		if (c101s01b != null) {
			setCm1DataBlock(c101s01b);

			LMSUtil.addMetaToResult(result, c101s01b, new String[] {
					"cm1_serve_company", "cm1_dataDt" });

			Map<String, String> _CM1_JOB_BUSINESS_CODE_map = codeTypeService
					.findByCodeType("CM1_JOB_BUSINESS_CODE");
			Map<String, String> _CM1_TITLE_CODE_map = codeTypeService
					.findByCodeType("CM1_TITLE_CODE");
			result.set("cm1_job_business_InfoStr", ClsUtil
					.getC101S01B_cm1_job_business_InfoStr(c101s01b,
							_CM1_JOB_BUSINESS_CODE_map));
			result.set("cm1_job_title_InfoStr", ClsUtil
					.getC101S01B_cm1_job_title_InfoStr(c101s01b,
							_CM1_TITLE_CODE_map));
		}
		return result;
	}

	private void setPtaData(C101S01B c101s01b) {
		String idDup = LMSUtil.getCustKey_len10custId(c101s01b.getCustId(),
				c101s01b.getDupNo());
		Map<String, Object> map = misStoredProcService.callTRPAYSQ1(idDup);
		if ("YES".equals(Util.trim(map.get("SP_RETURN")))) {
			String outResult = Util.trim(map.get("SP_OUTPUT_AREA"));

			// 第1碼是{
			Map<String, String> parseRtnMap = ClsUtility.parse_TRPAYSQ1_rtn(outResult);			
			String RETURN_CODE = Util.trim(MapUtils.getString(parseRtnMap, "RETURN_CODE"));
			String PAYLG = Util.trim(MapUtils.getString(parseRtnMap, "PAYLG"));
			String TAXNO = Util.trim(MapUtils.getString(parseRtnMap, "TAXNO"));
			String GRADE = Util.trim(MapUtils.getString(parseRtnMap, "GRADE"));
			String ACTNO = Util.trim(MapUtils.getString(parseRtnMap, "ACTNO"));
			if (Util.equals("0000", RETURN_CODE)) {
				c101s01b.setPtaDataDt(CapDate.getCurrentTimestamp());
				c101s01b.setPtaFlag(PAYLG);
				c101s01b.setPtaTaxNo(TAXNO);
				c101s01b.setPtaGrade(GRADE);
				c101s01b.setPtaBrNo(Util.trim(StringUtils.substring(ACTNO, 0, 3)));
				c101s01b.setPtaActNo(ACTNO);
				clsService.daoSave(c101s01b);
			} else {
				logger.error("setPtaData, idDup=[" + idDup + "],RETURN_CODE=["
						+ RETURN_CODE + "]");
			}
		} else {
			logger.error("setPtaData, idDup=[" + idDup + "],SP_RETURN=["
					+ Util.trim(map.get("SP_RETURN")) + "]");
		}
	}

	@DomainAuth(AuthType.Query)
	public IResult impPtaFlag(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C101S01B c101s01b = (C101S01B) cls1131Service.findModelByKey(
				C101S01B.class, mainId, custId, dupNo);
		if (c101s01b != null) {
			setPtaData(c101s01b);

			LMSUtil.addMetaToResult(result, c101s01b, new String[] { "ptaFlag",
					"ptaTaxNo", "ptaGrade", "ptaDataDt" });
		}
		return result;
	}

	/**
	 * 讀取借保人資料（CLS1141FormHandler也要一併改）
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Query)
	public IResult loadCust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		Class<?>[] clazzs = { C101M01A.class, C101S01A.class, C101S01B.class,
				C101S01C.class, C101S01D.class, C101S01E.class, C101S01F.class, C101S01X.class, C101S01Z.class, C101S02C.class};
		
		//================================
		//清資料
		if(true){
			C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
			if(c101m01a!=null){
				if(true){
					C101S01J c101s01j = clsService.findC101S01J(c101m01a);			
					if (c101s01j != null) {
						boolean chg_model = false;
						
						String amlRefNo = Util.trim(c101s01j.getAmlRefNo());
						String amlRefOid = Util.trim(c101s01j.getAmlRefOid());
						
						L120S09B l120s09b = clsService.findL120S09B_refNo_or_oid(
								amlRefNo, amlRefOid);
						if(l120s09b!=null 
								&& l120s09b.getQueryDateS()!=null
								&& ClsUtility.needRedoAction_paramDateNotNullAndBeforeNowDayCnt(l120s09b.getQueryDateS(), -32)){
							c101s01j.setAmlRefNo("");
							c101s01j.setAmlRefOid(""); 
							chg_model = true;
						}
						if(chg_model){
							clsService.daoSave(c101s01j);
						}
					}
				}
				//==============
				if(true){
					C101S01E c101s01e = clsService.findC101S01E(c101m01a);	
					if (c101s01e != null) {
						boolean chg_model = false;
						if(true){
							if (ClsUtility.needRedoAction_paramDateNotNullAndBeforeNowDayCnt(c101s01e.getOneBtnQ_qTime(), -32)) {
								c101s01e.setOneBtnQ_qTime(null);
								chg_model = true;
							}	
						}
						if(true){
							if (ClsUtility.needRedoAction_paramDateNotNullAndBeforeNowDayCnt(c101s01e.getZ13_qTime(), -32)) {
								c101s01e.setZ13_qTime(null);
								c101s01e.setZ13_html(null);
								chg_model = true;
							}	
						}
						if(true){
							if (ClsUtility.needRedoAction_paramDateNotNullAndBeforeNowDayCnt(c101s01e.getZ21_qTime(), -32)) {
								c101s01e.setZ21_qTime(null);
								c101s01e.setZ21_html(null);
								chg_model = true;
							}	
						}
						if(chg_model){
							clsService.daoSave(c101s01e);
						}						
					}
				}
			}
		}
		for (Class<?> clazz : clazzs) {
			if (LMSUtil.disableC101S01F_C120S01F() && clazz == C101S01F.class) {
				// 不傳回 C101S01FForm
				continue;
			}

			GenericBean model = cls1131Service.findModelByKey(clazz, mainId,
					custId, dupNo);
			if (model != null) {
				String className = Util.trim(clazz.getSimpleName());

				if (model instanceof C101S01B) {
					if (true) {
						C101S01B c101s01b = (C101S01B) model;
						if (true) {
							if (c101s01b.getCm1_dataDt() == null
									|| (c101s01b.getCm1_dataDt() != null && ClsUtility.needRedoAction_paramDateNotNullAndBeforeNowDayCnt(c101s01b.getCm1_dataDt(), -32))) {
								setCm1DataBlock(c101s01b);
							}
						}
						if (true) {
							if (c101s01b.getPtaDataDt() == null
									|| (c101s01b.getPtaDataDt() != null && ClsUtility.needRedoAction_paramDateNotNullAndBeforeNowDayCnt(c101s01b.getPtaDataDt(), -32))) {
								setPtaData(c101s01b);
							}
						}
						if(true){
							if(Util.isEmpty(c101s01b.getJobType1()) || Util.isEmpty(c101s01b.getJobType2())){
								//配合 勞工紓困 的 RPA作業
								//當既有資料, 有職業大類、小類時 => 沿用原本的資料
								//若無，則塞 服務業、其它
								c101s01b.setJobType1("08");
								c101s01b.setJobType2("I");
							}
						}
					}
				} else if (model instanceof C101S01C) {
					C101S01C c101s01c = (C101S01C) model;
					//配合 勞工紓困 的 RPA作業
					//若無資料，預設值塞 0
					if(c101s01c.getInvMBalAmt()==null){
						c101s01c.setInvMBalAmt(BigDecimal.ZERO);	
					}
					if(c101s01c.getInvOBalAmt()==null){
						c101s01c.setInvOBalAmt(BigDecimal.ZERO);	
					}
					if(c101s01c.getBranAmt()==null){
						c101s01c.setBranAmt(BigDecimal.ZERO);	
					}
				} else if (model instanceof C101S01E) {
					// 無法提供票信電子資料 預設值N add by fantasy 2013/05/23
					if (Util.isEmpty(model.get("isFromOld"))) {
						model.set("isFromOld", UtilConstants.DEFAULT.否);
					}
				} else if (model instanceof C101S01D) {
					// 預設不登錄配偶資料 add by fantasy 2013/06/15
					if (Util.isEmpty(model.get("mateFlag"))) {
						model.set("mateFlag", ClsConstants.MateFlag.不登錄配偶資料);
					}
				}

				CapAjaxFormResult formResult = DataParse.toResult(model);
				// ==============================================
				// CLS1131FormHandler vs CLS1141FormHandler
				// 個金徵信 是 C101S01x 系列
				if (model instanceof C101S01B) {
					formResult.set("juTotalCapital", LMSUtil
							.convert_bigvalue(((C101S01B) model)
									.getJuTotalCapital()));
					formResult.set("juPaidUpCapital", LMSUtil
							.convert_bigvalue(((C101S01B) model)
									.getJuPaidUpCapital()));
					C101S01B c101s01b = (C101S01B) model;
					if (c101s01b.getPtaDataDt() == null) {
						formResult.set("ptaDataDt", "");
					}

					Map<String, String> _CM1_JOB_BUSINESS_CODE_map = codeTypeService
							.findByCodeType("CM1_JOB_BUSINESS_CODE");
					Map<String, String> _CM1_TITLE_CODE_map = codeTypeService
							.findByCodeType("CM1_TITLE_CODE");
					formResult.set("cm1_job_business_InfoStr", ClsUtil
							.getC101S01B_cm1_job_business_InfoStr(c101s01b,
									_CM1_JOB_BUSINESS_CODE_map));
					formResult.set("cm1_job_title_InfoStr", ClsUtil
							.getC101S01B_cm1_job_title_InfoStr(c101s01b,
									_CM1_TITLE_CODE_map));
					if (c101s01b.getCm1_dataDt() == null) {
						formResult.set("cm1_dataDt", "");
					}
					if(true){
						formResult.set("snrY", ClsUtility.get_inject_snrY(c101s01b.getSeniority()));
					}
				}

				if (model instanceof C101S01C) {
					C101S01C c101s01c = (C101S01C) model;
					formResult.set("loanBalSByidShow", c101s01c.getLoanBalSByid()==null ? "N.A" : String.valueOf(c101s01c.getLoanBalSByid()));
					formResult.set("loanBalNByidShow", c101s01c.getLoanBalNByid()==null ? "N.A" : String.valueOf(c101s01c.getLoanBalNByid()));
				}
				
				if (model instanceof C101S01E) {
					LMSUtil.setL120M01M(formResult,
							cls1131Service.findL120s01m(mainId, custId, dupNo));

					ClsUtil.setC101S01E_wm_data(formResult,
							DataParse.toJSON(model));

					C101S01J c101s01j = clsService.findC101S01J(mainId, custId,
							dupNo);
					String amlRefNo = "";
					String amlRefOid = "";
					if (c101s01j != null) {
						amlRefNo = Util.trim(c101s01j.getAmlRefNo());
						amlRefOid = Util.trim(c101s01j.getAmlRefOid());
					}
					ClsUtil.set_msg_Laa(formResult, clsService.msg_Laa_html(
							c101s01j, prop_LMSCommomPage));
					String msg_agentPIdCmp = "";
					if (c101s01j != null
							&& Util.isNotEmpty(Util.trim(c101s01j
									.getAgentPIdCmp()))) {
						C900M01J c900m01j = clsService
								.findC900M01J_mainId(c101s01j.getAgentPIdCmp());
						String c101s01e_agentPId = Util
								.trim(c900m01j == null ? "" : c900m01j
										.getCustId());
						msg_agentPIdCmp = ClsUtil.msg_agentPIdCmp(
								c101s01e_agentPId, c900m01j,
								clsService.get_C900M01J_output_memo(c900m01j),
								prop_CLS1131S01Panel);
					}
					ClsUtil.set_msg_agentPIdCmp(formResult, msg_agentPIdCmp);
					// for amlDiv
					L120S09B l120s09b = clsService.findL120S09B_refNo_or_oid(
							amlRefNo, amlRefOid);
					L120S09A l120s09a = clsService
							.findL120S09A_cls1131(l120s09b);
					Map<String, String> map_ncResult = clsService
							.get_codeTypeWithOrder("SAS_NC_Result");
					ClsUtil.set_msg_L120S09B(formResult, l120s09b, l120s09a,
							map_ncResult);
					String amlBadNews = Util.trim((String) model
							.get("amlBadNews"));
					String[] array = amlBadNews.split("\\|");
					if (array.length <= 1) {
						formResult.set("amlBatNews", amlBadNews);
					} else {
						JSONArray js = new JSONArray();
						for (String s : array) {
							js.add(s);
						}

						formResult.set("amlBatNews", js.toString());
					}
					// 如果沒資料的話，糋傳到前端不會有此資料，firefox的ckeditor會怪怪的，故增加下面一行程式
					formResult.set("creditBadNews",
							Util.trim((String) model.get("creditBadNews")));
					
					// 加上大數據風險資料
					C101S02B c101s02b = cls1131Service.findModelByKey(C101S02B.class, mainId,
							custId, dupNo);
					if (c101s02b != null) {
						CapAjaxFormResult c101s02bData = DataParse.toResult(c101s02b);
						formResult.putAll(c101s02bData);
					}
				}
				
				if(model instanceof C101S01Z){
					if (Util.isNotEmpty(model.get("kycUpdater"))) {
						formResult.set("kycUpdaterName", 
								Util.trim(userInfoService.getUserName(model.get("kycUpdater").toString())) );
					}
					if (Util.isNotEmpty(model.get("kycUpdateTime"))) {
						formResult.set("kycUpdateTime", 
								S_FORMAT.format(model.get("kycUpdateTime")) );
					}
					if (Util.isNotEmpty(model.get("kycApprover"))) {
						formResult.set("kycApproverName", 
								Util.trim(userInfoService.getUserName(model.get("kycApprover").toString())) );
					}
					if (Util.isNotEmpty(model.get("kycApprTime"))) {
						formResult.set("kycApprTime", 
								S_FORMAT.format(model.get("kycApprTime")) );
					}
				}

				result.set(className + "Form", formResult);
				
			} else {
				// 無法提供票信電子資料 預設值N add by fantasy 2013/06/10
				if (clazz == C101S01E.class) {
					CapAjaxFormResult formResult = new CapAjaxFormResult();
					formResult.set("isFromOld", UtilConstants.DEFAULT.否);
					result.set("C101S01EForm", formResult);
				}
				// 預設不登錄配偶資料 add by fantasy 2013/06/15
				else if (clazz == C101S01D.class) {
					CapAjaxFormResult formResult = new CapAjaxFormResult();
					formResult.set("mateFlag", ClsConstants.MateFlag.不登錄配偶資料);
					result.set("C101S01DForm", formResult);
				}
			}
		}
		// 取得評等資訊
		JSONObject C101M01AForm = (JSONObject) result.get("C101M01AForm");
		if (C101M01AForm != null) {
			C101S01A model_s01a = cls1131Service.findModelByKey(C101S01A.class,
					mainId, custId, dupNo);
			C101S01G model = cls1131Service.findModelByKey(C101S01G.class,
					mainId, custId, dupNo);
			C101S01Q model_q = cls1131Service.findModelByKey(C101S01Q.class,
					mainId, custId, dupNo);
			C101S01R model_r = cls1131Service.findModelByKey(C101S01R.class,
					mainId, custId, dupNo);
			// ========================
			String ntCode = "";
			if (model_s01a != null) {
				ntCode = Util.trim(model_s01a.getNtCode());
			}
			ClsUtil.set_ntCode(C101M01AForm, ntCode);
			// ========================
			String bailout_flag = "";
			if (model != null) {
				boolean naturalFlag = LMSUtil.check2(custId); // 是否為自然人
				C101M01AForm.put("naturalFlag",
						naturalFlag ? UtilConstants.DEFAULT.是
								: UtilConstants.DEFAULT.否); // 是否為自然人
				String checkItemRange = clsService.getCheckItemRange(model);
				C101M01AForm.putAll(ClsUtil
						.procMarkModel_G(model, bailout_flag, checkItemRange));
				C101M01AForm.putAll(ClsUtil
						.procMarkModel_0(model, bailout_flag, checkItemRange));
			}
			// ========================
			Date date_jcicFlg_V_NN = null;
			if (model_q != null) {
				String checkItemRange = clsService.getCheckItemRange(model_q);
				C101M01AForm.putAll(ClsUtil.procMarkModel_Q(model_q,
						bailout_flag, checkItemRange));
				date_jcicFlg_V_NN = model_q.getJcicQDate();
			}

			if (model_r != null) {
				String checkItemRange = clsService.getCheckItemRange(model_r);
				C101M01AForm.putAll(ClsUtil.procMarkModel_R(model_r,
						bailout_flag, checkItemRange));
			} else {
				String c101s01r_varVer = scoreService.get_Version_CardLoan();
				C101M01AForm.putAll(ClsUtil
						.procMarkModel_R_default(c101s01r_varVer));
			}
			//增加雙軌處理
			boolean showSDT_G = false;
			boolean showSDT_Q = false;
			boolean showSDT_R = false;
			boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
			if(scoreDoubleTrack){ //雙軌處理
				C101S01G_N model_GN = cls1131Service.findModelByKey(C101S01G_N.class,
						mainId, custId, dupNo);
				C101S01Q_N model_QN = cls1131Service.findModelByKey(C101S01Q_N.class,
						mainId, custId, dupNo);
				C101S01R_N model_RN = cls1131Service.findModelByKey(C101S01R_N.class,
						mainId, custId, dupNo);
				
				if (model_GN != null) {
					C101M01AForm.putAll(ClsUtil.procMarkModel_G_N(model_GN));
					showSDT_G = true;
				}
				if (model_QN != null) {
					C101M01AForm.putAll(ClsUtil.procMarkModel_Q_N(model_QN));
					showSDT_Q = true;
				}
				if (model_RN != null) {
					C101M01AForm.putAll(ClsUtil.procMarkModel_R_N(model_RN));
					showSDT_R = true;
				}
			}
			result.set("showSDT_G", showSDT_G);
			result.set("showSDT_Q", showSDT_Q);
			result.set("showSDT_R", showSDT_R);

			ClsUtil.set_date_jcicFlg_V_NN(C101M01AForm, date_jcicFlg_V_NN);

			// J-108-0143_10702_B1001 新增新往來客戶註記並於額度明細表新做加註
			JSONObject C101S01AForm = (JSONObject) result.get("C101S01AForm");
			if (C101S01AForm.toString().contains("newCustFlag")) {
				C101M01AForm.put("newCustFlag",
						C101S01AForm.getString("newCustFlag"));
			} else {
				C101M01AForm.put("newCustFlag", "");
			}
			//=================
			if (true) { // 引入 C122M01A 資料
				if (true) { //房貸增貸 or 卡友信貸 => 有明確的 dupNo=0或1
					C122M01A c122m01a_applyKind_HorC = cls1220Service.findLatestInProgressC122M01A(
							user.getUnitNo(), new String[] {
									UtilConstants.C122_ApplyKind.H,
									UtilConstants.C122_ApplyKind.C }, custId, dupNo);
					if (c122m01a_applyKind_HorC != null) {
						result.set("c122m01a_mainId", c122m01a_applyKind_HorC.getMainId());
					}
				}
				//~~~~~~~~~~~~~~~
				int between_month = 3;
				Date sinceDate = CapDate.addMonth(CapDate.getCurrentTimestamp(), between_month * -1);
				if(true){
					C122M01A c122m01a_applyKindB = clsService.findLatestC122M01A_by_brNo_custId_applyKind_sinceDate(user.getUnitNo(), custId, UtilConstants.C122_ApplyKind.B, sinceDate);
					if (c122m01a_applyKindB != null) {
						result.set("c122m01a_mainId_applyKindB", c122m01a_applyKindB.getMainId());
						//若是勞工紓困，指定 適用之模型=非房貸申請信用評等 
						//避免跳出 「專案信貸(非團體)」 要查 J10 的 dialog
						C101M01AForm.put("markModel", "2");
					}	
				}			
				//~~~~~~~~~~~~~~~
				if(true){ //線上貸款平台{消貸、房貸} => dupNo='' 且詳細資料在 JSON 裡
					C122M01A c122m01a_applyKind_PLOAN = clsService.findLatestC122M01A_by_brNo_custId_applyKind_sinceDate(user.getUnitNo(), custId
								, new String[]{UtilConstants.C122_ApplyKind.P, UtilConstants.C122_ApplyKind.Q, UtilConstants.C122_ApplyKind.E, UtilConstants.C122_ApplyKind.F}, sinceDate);				 
					if(c122m01a_applyKind_PLOAN!=null){
						//在無 dupNo 的情況下，判斷生日是否相同
						C120S01A ploan_c120s01a = clsService.findC120S01A(c122m01a_applyKind_PLOAN.getMainId(), c122m01a_applyKind_PLOAN.getCustId(), c122m01a_applyKind_PLOAN.getDupNo());
						if(ploan_c120s01a.getBirthday()!=null){
							if(model_s01a.getBirthday()!=null && LMSUtil.cmpDate(ploan_c120s01a.getBirthday(), "==", model_s01a.getBirthday())){
								result.set("c122m01a_mainId_applyKind_PLOAN", c122m01a_applyKind_PLOAN.getMainId());
							}else if(model_s01a.getBirthday()==null){
								result.set("c122m01a_mainId_applyKind_PLOAN", c122m01a_applyKind_PLOAN.getMainId());
							}
						}
					}
				}
			}
		}

		C101S01J c101s01j = cls1131Service.findModelByKey(C101S01J.class,
				mainId, custId, dupNo);
		if (c101s01j != null) {
			if (result.containsKey("C101S01EForm")) {
				JSONObject tmp = (JSONObject) result.get("C101S01EForm");
				tmp.put("ans1", c101s01j.getAns1());
				result.set("C101S01EForm", tmp.toString());
			}
		}
		
		if (true) {
			String active_SAS_AML = "0";
			// 優先抓 ownBrId, 若無, 則抓 user.getUnitNo()
			String amlUnitNo = Util.trim(C101M01AForm.optString("ownBrId",
					user.getUnitNo()));
			if (clsService.active_SAS_AML(amlUnitNo)) {
				active_SAS_AML = "1";
				// 比照 CLS1201S20Panel 的參數定義{1:SAS_AML且未掃描/已結束,
				// 2:SAS_AML且掃描中（LOCKEDIT）}
			}
			result.set("active_SAS_AML", active_SAS_AML);
		}

		// J-108-0277 個金查詢客戶報表綜合資料
		// List<Map<String, Object>> mixPdfRecordList = cls1131Service
		// .loadDataArchivalRecordData(mainId);
		// result.set("dataArchivalRecordData",
		// JSONArray.fromObject(mixPdfRecordList));

		// J-109-0178_10702_B1001 Web e-Loan 消金簽報書新增申貸資料核對表頁籤及列印功能
		if(true){
			CapAjaxFormResult formResult = new CapAjaxFormResult();
			List<C101S01V> c101s01v_list = cls1131Service.findC101S01VByMainid(mainId,null,custId,dupNo);
			if(c101s01v_list.size() >0){
				for(C101S01V c101s01v:c101s01v_list){
					formResult.set(c101s01v.getItems_Name(), c101s01v.getItems_Value());
				}
			}
			
			result.set("C101S01VForm", formResult);
			result.set("chk_c120s01v", clsService.is_function_on_codetype("chk_c120s01v"));
		}
		
		if(true){
			CapAjaxFormResult param_oneBtnQuery = new CapAjaxFormResult();
			//select * from com.bcodetype where codetype='param_CLS1131_oneBtnQuery'
			/*
			  INSERT INTO com.bcodetype (OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (get_oid(),'param_CLS1131_oneBtnQuery','openWay','B','zh_TW','','',1,'sys', current timestamp);
		  	  INSERT INTO com.bcodetype (OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (get_oid(),'param_CLS1131_oneBtnQuery','defined_etch_timeout','950','zh_TW','','',2,'sys', current timestamp);
		  	  INSERT INTO com.bcodetype (OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (get_oid(),'param_CLS1131_oneBtnQuery','defined_ejcic_timeout','1200','zh_TW','','',3,'sys', current timestamp);
			*/

			Map<String, String> params_map = clsService.get_codeTypeWithOrder("param_CLS1131_oneBtnQuery");
			param_oneBtnQuery.set("openWay", Util.trim(MapUtils.getString(params_map, "openWay", "B"))); // CLS1131S01.data['isPopupResultWindow']= Y or N
			param_oneBtnQuery.set("defined_etch_timeout", MapUtils.getString(params_map, "defined_etch_timeout", "900"));
			param_oneBtnQuery.set("defined_ejcic_timeout", MapUtils.getString(params_map, "defined_ejcic_timeout", "900"));
			
			
			//判斷是否開啟RPA地政士
			param_oneBtnQuery.set("RPA_AGENT_SHOW_FUNC",CapString.trimNull(
					sysparamService.getParamValue("RPA_AGENT_SHOW_FUNC_LMS")));
			
			result.set("param_oneBtnQuery", param_oneBtnQuery);
		}
		//J-113-0199 行員自動過件
		if (clsService.is_function_on_codetype("autoCheckFlag")) {
			result.set("autoCheck_showPage", clsService.is_function_on_codetype("autoCheck_showPage"));
			result.set("autoCheck_bankManFlag", misBaseService.isBankMan_on_the_job(custId) || clsService.is_function_on_codetype("autoCheck_bankManFlag"));

			L120S19A l120s19a_latestOutput = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainId, ClsConstants.L120S19A_ItemTypeCode.BRMP_autoCheck_output);
			JSONObject outJs = new JSONObject();
			L120S19A l120s19aOutJs = null;
			ObjectMapper objectMapper = new ObjectMapper();
			if (Util.isNotEmpty(l120s19a_latestOutput)) {
				outJs = JSONObject.fromObject(l120s19a_latestOutput.getJsonData());
				l120s19aOutJs = l120s19a_latestOutput;
				Brmp005O brmp005o_obj = new Brmp005O();
				try {
					brmp005o_obj = objectMapper.readValue(outJs.toString(), Brmp005O.class);
				} catch (IOException e) {
					e.printStackTrace();
				}
				CapAjaxFormResult rtn_json = new CapAjaxFormResult(cls1131Service.getAutoCheck(brmp005o_obj));
				result.set("rtn_json", rtn_json);
				//案件狀態
				C101S02C c101s02c = cls1131Service.findC101S02C(mainId);
				if (c101s02c!=null) {
					String docstatus = Util.trim(c101s02c.getDocStatus());
					if (Util.isNotEmpty(docstatus)) {
						Map<String, String> _DocStatusNewDescMap = cls1220Service.get_DocStatusNewDescMap();
						rtn_json.set("autoCheckResultDesc", _DocStatusNewDescMap.get(docstatus));
						result.set("rtn_json",rtn_json);
					}
				}
			}
		}

		return result;
	}

	/**
	 * 引進借款人資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult importCust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		Map<String, Object> cust = cls1131Service
				.findMisCustData(custId, dupNo);
		if (cust != null) {
			// 個金徵信借款人主檔
			CapAjaxFormResult CustForm = new CapAjaxFormResult(
					DataParse.transformJSON(cust));
			CustForm.set("staffNo", Util.trim(cust.get("staffNo")));// 職工編號
			CustForm.set("custId", custId); // 客戶統編
			CustForm.set("dupNo", dupNo); // 重覆序號
			CustForm.set("custName", Util.trim(cust.get("CNAME"))); // 中文戶名
			// 個金基本資料檔
			CustForm.set("birthday", Util.trim(cust.get("BIRTHDT")));// 生日BIRTHDT
			CustForm.set("edu", Util.trim(cust.get("DEGREECD")));// 學歷 DEGREECD
			// 取得戶籍地址
			Map<String, Object> cmf = misCustdataService.findCMFCUS1ByIdAndDup(
					custId, dupNo);

			// 桃園升格0024還未更新時處理方法
			String tCityr = "";
			String tTownr = "";
			String tLeer = "";

			if (cmf != null) {

				// 桃園升格0024還未更新時處理方法
				tCityr = Util.trim(cmf.get("CM1_CITY_R"));
				tTownr = Util.trim(cmf.get("CM1_TOWN_R"));
				tLeer = Util.trim(cmf.get("CM1_LEE_R"));
				if ("桃園縣".equals(tCityr)) {
					tCityr = cls1131Service.changCityR(tCityr);
					tTownr = cls1131Service.changTownR(tTownr);
					tLeer = cls1131Service.changLeeR(tLeer);
				}
				cust.put("ADDRZIP", Util.trim(cmf.get("CM1_ZIP_R")));
				cust.put("CITYR", tCityr);
				cust.put("TOWNR", tTownr);
				cust.put("LEER", tLeer);
				cust.put("LINR", Util.trim(cmf.get("CM1_LIN_R")));
				cust.put("ADDRR", Util.trim(cmf.get("CM1_ADDR_R")));
			}
			// 戶籍地址
			String addrZip = Util.trim(cust.get("ADDRZIP"));
			JSONObject zipJson = cls1131Service.parseZip(addrZip);

			// 桃園升格0024還未更新時處理方法
			tCityr = Util.trim(cust.get("CITYR"));
			tTownr = Util.trim(cust.get("TOWNR"));
			tLeer = Util.trim(cust.get("LEER"));
			if ("桃園縣".equals(tCityr)) {
				tCityr = cls1131Service.changCityR(tCityr);
				tTownr = cls1131Service.changTownR(tTownr);
				tLeer = cls1131Service.changLeeR(tLeer);
			}

			CustForm.set("fCity",
					Util.trim(zipJson.get(ClsConstants.City.縣市代碼)));// 戶籍地址(縣市)
			CustForm.set("fZip", Util.trim(zipJson.get(ClsConstants.City.郵地區號))); // 戶籍地址(鄉鎮市區)

			StringBuilder sb = new StringBuilder();
			sb.append(tLeer); // 戶籍地址(村里)
			sb.append(Util.trim(cust.get("LINR"))); // 戶籍地址(鄰)
			sb.append(Util.trim(cust.get("ADDRR"))); // 戶籍地址(其餘部分)
			CustForm.set("fAddr", sb.toString());
			Util.trim(CustForm.get("fAddr"));
			sb.delete(0, sb.length());

			sb.append(tCityr); // 戶籍地址(縣市)
			sb.append(tTownr); // 戶籍地址(區鄉鎮市)
			sb.append(Util.trim(CustForm.get("fAddr"))); // 戶籍地址
			CustForm.set("fTarget", sb.toString());
			sb.delete(0, sb.length());

			// 行業對象別 BUSCD
			CustForm.set("comName", Util.trim(cust.get("SCMP")));// 個人服務公司SCMP
			CustForm.set("jobTitle", Util.trim(cust.get("POS")));// 職稱POS
			CustForm.set("mCustId", Util.trim(cust.get("MATEID")));// 配偶身分證字號MATEID
			CustForm.set("mName", Util.trim(cust.get("MATENM")));// 配偶姓名MATENM
			CustForm.set("mBirthday", Util.isEmpty(cust.get("MATEID")) ? ""
					: Util.trim(cust.get("MATEBIRT")));// 配偶出生年月日MATEBIRT

			result.set("CustForm", CustForm);
		} else {
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}

		return result;
	}// ;

	/**
	 * 引進借款人配偶資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult importMate(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult C101S01DForm = new CapAjaxFormResult();

		boolean haveData = false;

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String mCustId = Util.trim(params.getString("mCustId"));
		String mDupNo = Util.trim(params.getString("mDupNo", "0"));

		// get client data
		JSONObject json = ClsUtil.getJson(params, C101S01D.class);
		if (json != null) {
			mCustId = Util.trim(json.get("mCustId"));
			mDupNo = Util.trim(json.get("mDupNo"));
			if (Util.isEmpty(mDupNo))
				mDupNo = "0";
		}

		//
		if (Util.isEmpty(mCustId)) {
			List<Map<String, Object>> list = misBaseService
					.findCUSTDATA_selCustData(custId, dupNo);
			if (!list.isEmpty()) {
				Map<String, Object> map = list.get(0);
				if (Util.isNotEmpty(map.get("MATEID"))) {
					haveData = true;
					mCustId = Util.trim(map.get("MATEID"));
					// C101S01DForm
					C101S01DForm.set("mCustId", mCustId); // 身份證統一編號
					C101S01DForm.set("mName", Util.trim(map.get("MATENM"))); // 姓名
					C101S01DForm.set("mBirthday",
							Util.trim(map.get("MATEBIRT"))); // 出生日期
				}
			}
		}

		Map<String, Object> cust = cls1131Service.findMisCustData(mCustId,
				mDupNo);
		if (cust != null) {
			haveData = true;
			C101S01DForm.set("mCustId", mCustId); // 客戶統編
			C101S01DForm.set("mDupNo", mDupNo); // 重覆序號
			C101S01DForm.set("mName", Util.trim(cust.get("CNAME"))); // 中文戶名
			// 個金基本資料檔
			C101S01DForm.set("mBirthday", Util.trim(cust.get("BIRTHDT")));// 生日BIRTHDT
			C101S01DForm.set("mComName", Util.trim(cust.get("SCMP"))); // 個人服務公司
			C101S01DForm.set("mJobTitle", Util.trim(cust.get("POS"))); // 職稱
			C101S01DForm.set("mComTel", Util.trim(cust.get("TELNO"))); // 電話
		}

		List<Map<String, Object>> mList = misBaseService
				.findCUSTDATA_selMCustId2(mCustId);
		if (Util.isNotEmpty(mCustId) && !mList.isEmpty()) {
			haveData = true;
			Map<String, Object> map = mList.get(0);
			C101S01DForm.set("mName", Util.trim(map.get("CNAME"))); // 姓名
			C101S01DForm.set("mCustId", Util.trim(map.get("CUSTID"))); // 身份證統一編號
			C101S01DForm.set("mDupNo", Util.trim(map.get("DUPNO"))); // 重覆序號
			// C101S01DForm.set("", Util.trim(map.get("BIRTHFLG"))); // 是否為民國前
			C101S01DForm.set("mBirthday", Util.trim(map.get("BIRTHDT"))); // 出生日期
			C101S01DForm.set("mComName", Util.trim(map.get("SCMP"))); // 個人服務公司
			C101S01DForm.set("mJobTitle", Util.trim(map.get("POS"))); // 職稱
			C101S01DForm.set("mComTel", Util.trim(map.get("TELNO"))); // 電話
		}

		if (!haveData)
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());

		result.set("C101S01DForm", C101S01DForm);

		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}// ;

	/**
	 * 於消金徵信>基本資料>存款往來銀行. 若選擇本行(代碼：017) 查詢本行存款帳戶
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult queryAccount(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String brno = Util.trim(params.getString("brno"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		List<Map<String, Object>> list = dwdbService
				.findDW_IDDP_DPF_Seqno_ByCustIdBrno(brno, custId, dupNo);
		for (Map<String, Object> map : list) {
			StringBuilder sb = new StringBuilder();
			sb.append(Util.trim(map.get("BRNO")));
			sb.append(Util.trim(map.get("APCD")));
			sb.append(Util.trim(map.get("SEQNO")));
			map.put("Account", sb.toString());
		}
		JSONObject json = new JSONObject();
		json.put("list", list);
		result.set("account", new CapAjaxFormResult(json));

		return result;
	}// ;

	/**
	 * 於消金徵信>基本資料>存款往來銀行. 若選擇本行(代碼：017) 會將 帳號 的資料帶入「UI欄位」 當「UI欄位」的資料，有被 change
	 * 後，檢查是否存在於DB中
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult checkAccount(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String C101S01AForm = Util.trim(params.getString("C101S01AForm"));
		JSONObject C101S01AJson = DataParse.toJSON(C101S01AForm);

		String dpBank = Util.trim(C101S01AJson.get("dpBank"));
		String dpAcct = Util.trim(C101S01AJson.get("dpAcct"));

		if (UtilConstants.兆豐銀行代碼.equals(dpBank) && dpAcct.length() == 11) {
			String brno = dpAcct.substring(0, 3);
			if (UtilConstants.兆豐銀行代碼.equals(dpBank)) {
				String apcd = dpAcct.substring(3, 5);
				String seqno = dpAcct.substring(5, 11);
				List<Map<String, Object>> list = dwdbService
						.findDW_IDDP_DPF_CustData_ByAccount(brno, apcd, seqno);
				if (list.isEmpty()) {
					StringBuilder sb = new StringBuilder();
					sb.append(getI18nMsg("message.checkAccount1")).append(
							EloanConstants.HTML_NEWLINE);
					sb.append(getI18nMsg("message.checkAccount2")).append(
							EloanConstants.HTML_NEWLINE);
					sb.append(getI18nMsg("message.checkAccount3")).append(
							EloanConstants.HTML_NEWLINE);
					sb.append(getI18nMsg("message.checkAccount4")).append(
							EloanConstants.HTML_NEWLINE);
					sb.append(getI18nMsg("message.checkAccount5")).append(
							EloanConstants.HTML_NEWLINE);
					sb.append(getI18nMsg("message.checkAccount6"));

					result.set("alert", sb.toString());
				}
			} else {
				StringBuilder sb = new StringBuilder();
				sb.append(getI18nMsg("C101S01A.dpAcct")).append("：")
						.append(dpAcct).append(EloanConstants.HTML_NEWLINE);
				sb.append(getI18nMsg("message.checkAccount"));
				throw new CapMessageException(sb.toString(), getClass());
			}
		}

		return result;
	}// ;

	/**
	 * 相關資料查詢
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult queryRelatedData(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		JSONObject commonJSON = commonJSON(params);

		String c101s01G_varVer = scoreService.get_Version_HouseLoan();
		String c101s01q_varVer = scoreService.get_Version_NotHouseLoan();
		String c101s01r_varVer = scoreService.get_Version_CardLoan();
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);

		if (c101m01a != null) { //清掉一段時間之前的舊的查詢結果(有些項目，只有勞工紓困才有) 
			// D10, R20, Z13, Z21
			Date dt_cmpDate = CapDate.shiftDays(CapDate.getCurrentTimestamp(), -45);
			String str_cmpDate = TWNDate.toAD(dt_cmpDate);
			String sendTimeCmp = str_cmpDate+" 00:00:00";
			List<C101S01U> c101s01_list = clsService.findC101S01U_txid_sendTimeBefore(mainId, new String[]{CrsUtil.EJ_TXID_D10, CrsUtil.EJ_TXID_R20, CrsUtil.EJ_TXID_B36},sendTimeCmp);
			clsService.delC101S01U(c101s01_list);
			//==============
			// Z13 與 Z21
			C101S01E c101s01e = clsService.findC101S01E(c101m01a);
			if(c101s01e != null){
				boolean upd_c101s01e = false;
				if(c101s01e.getZ13_qTime()!=null && LMSUtil.cmpDate(c101s01e.getZ13_qTime(), "<", dt_cmpDate)){
					c101s01e.setZ13_qTime(null);
					c101s01e.setZ13_html("");
					//======
					upd_c101s01e = true;
				}
				if(c101s01e.getZ21_qTime()!=null && LMSUtil.cmpDate(c101s01e.getZ21_qTime(), "<", dt_cmpDate)){
					c101s01e.setZ21_qTime(null);
					c101s01e.setZ21_html("");
					//======
					upd_c101s01e = true;
				}
				if(upd_c101s01e){
					clsService.daoSave(c101s01e);
				}
			}			
		}
		
		
		// 檢核 借款人資料表中會影響到評等的因子
		boolean naturalFlag = LMSUtil.check2(custId); // 是否為自然人
		if (naturalFlag) {
			// 相關資料查詢檢核儲存之必填欄位 by fantasy 2013/06/14
			String errorMessage = getCheckMessage(params,
					ClsUtil.C101CheckClass, SaveCheck.class, commonJSON);
			if (Util.isNotEmpty(errorMessage)) {
				throw new CapMessageException(errorMessage, getClass());
			}

			//Check2.class{當C101S01A,C101S01B：模型評等的因子欄位}{當C101S01E：聯徵/票信查詢日&資料日}
			errorMessage = getCheckMessage(params, ClsUtil.C101CheckClass,
					Check2.class, commonJSON);
			if (Util.isNotEmpty(errorMessage)) {
				throw new CapMessageException(errorMessage, getClass());
			}
		}
		// 檢查 是否勾選無法提供票信電子資料
		JSONObject json = ClsUtil.getJson(params, C101S01E.class);
		String isFromOld = Util.trim(json.get("isFromOld"));
		if (!UtilConstants.DEFAULT.是.equals(isFromOld) && naturalFlag) {
			Map<String, Object> map = etchService.findById(custId);
			if (map == null) {
				boolean show_err_msg = true;
				if(Util.equals("Y", params.getString("isBailout4"))
						|| Util.equals("Y", ClsUtil.getJson(params, C101M01A.class).optString("isBailout4"))){ //勞工紓困
					Map<String, String> dam001_dam003_map = ejcicService.get_DAM001_DAM003_relateData(custId);
					
					if(MapUtils.isNotEmpty(dam001_dam003_map)){
						 String eChkDDate = Util.trim(MapUtils.getString(dam001_dam003_map, "eChkDDate"));
						 String eChkQDate = Util.trim(MapUtils.getString(dam001_dam003_map, "eChkQDate"));
						 if(Util.isNotEmpty(eChkDDate) && Util.isNotEmpty(eChkQDate) ){
							 //雖無  票交所  查詢日期, 但有 B36+D10 的日期
							 show_err_msg = false;
						 }
					}
				}
				
				if(show_err_msg){
					StringBuilder sb = new StringBuilder();
					sb.append("查無票信資料!").append("<br/><br/>");
					sb.append("需補查票信資料 或 勾選 [無法提供票信電子資料] 為是").append("<br/>");
					sb.append("則可以執行「相關資料查詢」。");
					throw new CapMessageException(sb.toString(), getClass());
				}
			}
		}

		if (true) {
			String busCode = clsService.get0024_busCode(custId, dupNo);
			if (LMSUtil.isBusCode_060000_130300(busCode)
					&& clsService.is_function_on_codetype("jcicMisLogfile")) {
				String prodId = ejcicService.get_cls_PRODID(custId);
				String msg = isEjciQueryFail(ejcicService.getClsRecordLOGFILE(
						custId, dupNo, prodId));
				if (Util.isNotEmpty(msg)) {
					throw new CapMessageException(msg, getClass());
				}
			}
		}

		// 執行評等並取得結果
		List<GenericBean> list = appraise(params, mainId, custId, dupNo,
				c101s01G_varVer, c101s01q_varVer, c101s01r_varVer);
		if (list.isEmpty() && naturalFlag) {
			throw new CapMessageException("查無聯徵和票信資料!", getClass());
		} else {
			// add save all data by fantasy 2013/06/18
			JSONObject base = new JSONObject();
			base.put(EloanConstants.MAIN_ID, mainId);
			base.put("custId", custId);
			base.put("dupNo", dupNo);
			if (MapUtils.isNotEmpty(commonJSON)) {
				base.putAll(commonJSON);
			}
			Class<?>[] clazzs = { C101S01A.class, C101S01B.class,
					C101S01C.class, C101S01D.class, C101S01F.class };
			for (Class<?> clazz : clazzs) {
				if (LMSUtil.disableC101S01F_C120S01F()
						&& clazz == C101S01F.class) {
					continue;
				}
				JSONObject formJson = ClsUtil.getJson(params, clazz);
				GenericBean model = cls1131Service.findModelByKey(clazz,
						mainId, custId, dupNo, true);
				DataParse.toBean(formJson, model);
				DataParse.toBean(base, model);
				list.add(model);
			}
			// -----------------------------------------
			// 在 list 之後的參數 [C101S01H.class, C101S01I.class] 代表要刪除的 model
			cls1131Service.save(mainId, custId, dupNo, list, C101S01H.class,
					C101S01I.class);
			// 輸出訊息
			for (GenericBean model : list) {
				if (model instanceof C101S01G) {
					C101S01G c101s01g = (C101S01G) model;
					String checkItemRange = clsService.getCheckItemRange(c101s01g);
					if (true) {
						JSONObject output = new JSONObject();
						output.putAll(ClsUtil.procMarkModel_G(c101s01g, "", checkItemRange));
						result.set(ClsUtil.GRADE_DIV_MARLMODEL_1,
								new CapAjaxFormResult(output));
					}
					if (true) {
						JSONObject output = new JSONObject();
						output.putAll(ClsUtil.procMarkModel_0(c101s01g, "", checkItemRange));
						result.set(ClsUtil.GRADE_DIV_MARLMODEL_0,
								new CapAjaxFormResult(output));
					}
				} else if (model instanceof C101S01Q) {
					C101S01Q c101s01q = (C101S01Q) model;
					JSONObject output = new JSONObject();
					String checkItemRange = clsService.getCheckItemRange(c101s01q);
					output.putAll(ClsUtil.procMarkModel_Q(c101s01q, "", checkItemRange));

					result.set(ClsUtil.GRADE_DIV_MARLMODEL_2,
							new CapAjaxFormResult(output));
				} else if (model instanceof C101S01R) {
					C101S01R c101s01r = (C101S01R) model;
					JSONObject output = new JSONObject();
					String checkItemRange = clsService.getCheckItemRange(c101s01r);
					output.putAll(ClsUtil.procMarkModel_R(c101s01r, "", checkItemRange));

					result.set(ClsUtil.GRADE_DIV_MARLMODEL_3,
							new CapAjaxFormResult(output));
				} else if (model instanceof C101S01E) {
					result.set("C101S01EForm", DataParse.toResult(model));
				} else if (model instanceof C101S01G_N){
					C101S01G_N c101s01g_n = (C101S01G_N) model;
					JSONObject output = new JSONObject();
					output.putAll(ClsUtil.procMarkModel_G_N(c101s01g_n));
					result.set(ClsUtil.GRADE_DIV_MARLMODEL_1,
							new CapAjaxFormResult(output));
				}else if (model instanceof C101S01Q_N){
					C101S01Q_N c101s01q_n = (C101S01Q_N) model;
					JSONObject output = new JSONObject();
					output.putAll(ClsUtil.procMarkModel_Q_N(c101s01q_n));
					result.set(ClsUtil.GRADE_DIV_MARLMODEL_2,
							new CapAjaxFormResult(output));
				}else if (model instanceof C101S01R_N){
					C101S01R_N c101s01r_n = (C101S01R_N) model;
					JSONObject output = new JSONObject();
					output.putAll(ClsUtil.procMarkModel_R_N(c101s01r_n));
					result.set(ClsUtil.GRADE_DIV_MARLMODEL_3,
							new CapAjaxFormResult(output));
				}
			}
			
			if (!result.containsKey(ClsUtil.GRADE_DIV_MARLMODEL_3)) {
				// 若缺少J10, 無法產生 C101S01R 的 model
				// 但在前端, 要顯示 △ 的符號, 讓 user 知道未查J10
				JSONObject output = new JSONObject();
				output.putAll(ClsUtil.procMarkModel_R_default(c101s01r_varVer));
				result.set(ClsUtil.GRADE_DIV_MARLMODEL_3,
						new CapAjaxFormResult(output));
			}
		}

		return result;
	}// ;

	private String isEjciQueryFail(List<Map<String, Object>> list) {
		String QDATE_latest = "";
		String CLITTIME_latest = "";

		if (list.size() > 0) {
			if (true) {
				Map<String, Object> fstRow = list.get(0);
				QDATE_latest = Util.trim(MapUtils.getString(fstRow, "QDATE"));
				CLITTIME_latest = Util.trim(MapUtils.getString(fstRow,
						"CLITTIME"));
			}

			List<String> RETCODE_fail = new ArrayList<String>();
			for (Map<String, Object> row : list) {
				String QDATE = Util.trim(MapUtils.getString(row, "QDATE"));
				String CLITTIME = Util
						.trim(MapUtils.getString(row, "CLITTIME"));
				String TXID = Util.trim(MapUtils.getString(row, "TXID"));
				String RETCODE = Util.trim(MapUtils.getString(row, "RETCODE"));

				if (Util.equals(QDATE_latest, QDATE)
						&& Util.equals(CLITTIME_latest, CLITTIME)) {
					if (Util.isEmpty(RETCODE) || Util.equals("0000", RETCODE)) {
						// ok
					} else {
						if (Util.equals(CrsUtil.EJ_TXID_Q135, TXID)) { //BAM095系列相關
							RETCODE_fail.add(TXID);
						} else if (Util.equals(CrsUtil.EJ_TXID_Q128, TXID)) { //KRM040系列相關
							RETCODE_fail.add(TXID);
						} else if (Util.equals(CrsUtil.EJ_TXID_Q116, TXID)) {
							RETCODE_fail.add(TXID);
						}
					}
				}
			}

			if (RETCODE_fail.size() > 0) {
				return "取得聯徵查詢結果異常(" + StringUtils.join(RETCODE_fail, "、")
						+ ")，請先排除後，再於 Web e-Loan 系統進行交易";
			}
		}
		return "";
	}

	/**
	 * 執行評等並取得結果<BR>
	 * resultList 包含 C101M01A, C101S01E, C101S01H, C101S01I【C101S01G, C101S01Q】
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	private List<GenericBean> appraise(PageParameters params, String mainId,
			String custId, String dupNo, String c101s01G_varVer,
			String c101s01q_varVer, String c101s01r_varVer) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<GenericBean> result = new ArrayList<GenericBean>();

		// 取得評分結果
		JSONObject fetch_score_src = fetch_score_src(params, custId, dupNo,
				c101s01G_varVer, c101s01q_varVer, c101s01r_varVer);

		// ===========================================
		// 企業戶、OBU戶不評房貸、非房貸分數
		// 可能企業戶,但在 select * from MIS.Datadate where ID=? AND PRODID = 'P7' and
		// itemname='BAI001' 有資料
		boolean del_GradeModel = Util.isEmpty(fetch_score_src
				.get(Score.column.聯徵資料日期)) || !LMSUtil.check2(custId);
		boolean delR = false;
		boolean delR_N = false;
		
		// 檢核有無聯徵&票信資料
		if (Util.isNotEmpty(fetch_score_src.get(Score.column.聯徵資料日期))) {
			String prodId = Util.trim(fetch_score_src
					.get(ClsScoreUtil.PRODID_KEY));
			
			JSONObject score_houseLoan = new JSONObject();
			if (true) {// 房貸
				score_houseLoan.putAll(fetch_score_src);
				score_houseLoan.putAll(scoreService.score(Score.type.基本,
						fetch_score_src, c101s01G_varVer));
			}
			JSONObject score_NotHouseLoan = new JSONObject();
			if (true) {// 非房貸
				score_NotHouseLoan.putAll(fetch_score_src);
				// 計算 score、評等、DR
				score_NotHouseLoan.putAll(scoreService.scoreNotHouseLoan(
						ScoreNotHouseLoan.type.非房貸基本, fetch_score_src,
						c101s01q_varVer));
			}
			JSONObject score_cardLoan = new JSONObject();
			if (clsService.doCardLoanBr(user.getUnitNo())) {
				score_cardLoan.putAll(fetch_score_src);
				// 計算 score、評等、DR
				score_cardLoan.putAll(scoreService.scoreCardLoan(
						ScoreCardLoan.type.卡友貸基本, fetch_score_src,
						c101s01r_varVer));
			}

			Class<?>[] Classes = { C101M01A.class, C101S01E.class,
					C101S01G.class, C101S01Q.class, C101S01R.class };

			for (Class<?> clazz : Classes) {
				GenericBean model = cls1131Service.findModelByKey(clazz,
						mainId, custId, dupNo, true);
				DataParse.toBean(ClsUtil.getJson(params, clazz), model);
				// ---
				if (model instanceof C101M01A) {
					// model.set("importFlag", UtilConstants.DEFAULT.是); //
					// 註記已評等可引入
					// 在 loop 的順序,先處理 C101M01A, 再處理 C101S01G,C101S01Q
					C101M01A c101m01a = (C101M01A)model;
					
					score_houseLoan.put(Score.column.引用_房貸, ClsUtil.fetchQuoteValue(ClsUtil.isQuote(c101m01a, UtilConstants.L140S02AModelKind.房貸)));
					score_NotHouseLoan.put(ScoreNotHouseLoan.column.引用_非房貸, ClsUtil.fetchQuoteValue(ClsUtil.isQuote(c101m01a, UtilConstants.L140S02AModelKind.非房貸)));
					if (!score_cardLoan.isEmpty()) {
						score_cardLoan.put(ScoreCardLoan.column.引用_卡友貸, ClsUtil.fetchQuoteValue(ClsUtil.isQuote(c101m01a, UtilConstants.L140S02AModelKind.卡友貸)));
					}
					model.set(ClsUtility.C101M01A_JCICFLG,
							Util.trim(fetch_score_src
									.get(ClsUtility.C101M01A_JCICFLG)));

					model.set(ClsUtility.C101M01A_PRIMARY_CARD, LMSUtil
							.fetch_BigDecimal_from_json(fetch_score_src,
									ClsUtility.C101M01A_PRIMARY_CARD));
					model.set(ClsUtility.C101M01A_ADDITIONAL_CARD, LMSUtil
							.fetch_BigDecimal_from_json(fetch_score_src,
									ClsUtility.C101M01A_ADDITIONAL_CARD));
					model.set(ClsUtility.C101M01A_BUSINESS_OR_P_CARD, LMSUtil
							.fetch_BigDecimal_from_json(fetch_score_src,
									ClsUtility.C101M01A_BUSINESS_OR_P_CARD));
					c101m01a.setHoldMegaCardDt(CapDate.parseDate(Util.trim(fetch_score_src.get(ClsUtility.C101M01A_HOLD_MEGA_CARD_DT))));
				} else if (model instanceof C101S01G) {
					DataParse.toBean(score_houseLoan, model);
					scoreService.clear_unUsedColumn((C101S01G) model);
				} else if (model instanceof C101S01E) {
					// 聯徵票信 資料&查詢日期
					JSONObject set = new JSONObject();
					set.put(ClsConstants.C101S01E.查詢組合, prodId);
					set.put(ClsConstants.C101S01E.聯徵資料日期,
							Util.trim(score_houseLoan.get(Score.column.聯徵資料日期)));
					set.put(ClsConstants.C101S01E.聯徵查詢日期,
							Util.trim(score_houseLoan.get(Score.column.聯徵查詢日期)));
					set.put(ClsConstants.C101S01E.票信資料截止日,
							Util.trim(score_houseLoan.get(Score.column.票信資料日期)));
					set.put(ClsConstants.C101S01E.票信查詢日期,
							Util.trim(score_houseLoan.get(Score.column.票信查詢日期)));
					DataParse.toBean(set, model);
				} else if (model instanceof C101S01Q) {
					DataParse.toBean(score_NotHouseLoan, model);
					scoreService.clear_unUsedColumn((C101S01Q) model);
				} else if (model instanceof C101S01R) {
					/*
					 * 若缺少J10, 在 score_cardLoan 裡面 ● 仍會有 input param [SENIORITY,
					 * PINCOME] ● 也會有[CHKITEM1, CHKITEM2,CHKITEM3 ......]
					 */
					if (score_cardLoan.isEmpty() == false
							&& Util.isNotEmpty(Util.trim(score_cardLoan
									.optString(ScoreCardLoan.column.最終評等)))) {
						DataParse.toBean(score_cardLoan, model);
						scoreService.clear_unUsedColumn((C101S01R) model);
					} else {
						delR = true;
						continue;
					}
				}

				if (del_GradeModel
						&& ((model instanceof C101S01G)
								|| (model instanceof C101S01Q) || (model instanceof C101S01R))) {
					continue;
				}

				result.add(model);
			}
			
			//是否執行雙軌
			boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
			if(scoreDoubleTrack){
				JSONObject fetch_score_src_N = fetch_score_src(params, custId, dupNo,
						ClsScoreUtil.V3_0_HOUSE_LOAN, ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, ClsScoreUtil.V4_0_CARD_LOAN);

				JSONObject score_houseLoan_3_0 = new JSONObject();
				if (true) {// 房貸3.0
					score_houseLoan_3_0.putAll(fetch_score_src_N);
					score_houseLoan_3_0.putAll(scoreService.score(Score.type.基本,
							fetch_score_src_N, ClsScoreUtil.V3_0_HOUSE_LOAN));
				}
				JSONObject score_NotHouseLoan_4_0 = new JSONObject();
				if (true) {// 非房貸4.0
					score_NotHouseLoan_4_0.putAll(fetch_score_src_N);
					// 計算 score、評等、DR
					score_NotHouseLoan_4_0.putAll(scoreService.scoreNotHouseLoan(
							ScoreNotHouseLoan.type.非房貸基本, fetch_score_src_N,
							ClsScoreUtil.V4_0_NOT_HOUSE_LOAN));
				}
				JSONObject score_cardLoan_4_0 = new JSONObject();
				if (clsService.doCardLoanBr(user.getUnitNo())) {
					score_cardLoan_4_0.putAll(fetch_score_src_N);
					// 計算 score、評等、DR
					score_cardLoan_4_0.putAll(scoreService.scoreCardLoan(
							ScoreCardLoan.type.卡友貸基本, fetch_score_src_N,
							ClsScoreUtil.V4_0_CARD_LOAN));
				}
				
				//存資料
				for (Class<?> clazz : LMSUtil.C101_NClass) {
					GenericBean model = cls1131Service.findModelByKey(clazz,
							mainId, custId, dupNo, true);
					DataParse.toBean(ClsUtil.getJson(params, clazz), model);
					if (model instanceof C101S01G_N) {
						DataParse.toBean(score_houseLoan_3_0, model);
					}else if (model instanceof C101S01Q_N) {
						DataParse.toBean(score_NotHouseLoan_4_0, model);
					} else if (model instanceof C101S01R_N) {
//						/*
//						 * 若缺少J10, 在 score_cardLoan 裡面 ● 仍會有 input param [SENIORITY,
//						 * PINCOME] ● 也會有[CHKITEM1, CHKITEM2,CHKITEM3 ......]
//						 */
						if (score_cardLoan_4_0.isEmpty() == false
								&& Util.isNotEmpty(Util.trim(score_cardLoan_4_0
										.optString(ScoreCardLoan.column.最終評等)))) {
							DataParse.toBean(score_cardLoan_4_0, model);
						} else {
							delR_N = true;
							continue;
						}
					}
					result.add(model);
				}
				
			}

			// 取得聯徵和票信信查詢結果(HTML) ［C101S01H、C101S01I﹞
			result.addAll(cls1131Service.getHtml(mainId, custId, dupNo, prodId));
		}

		if (del_GradeModel) {
			// 在 JCIC 查無資料, 把C101S01G, C101S01Q, C101S01R清掉
			List<GenericBean> delList = new ArrayList<GenericBean>();
			if (true) {
				C101S01G c101s01g = cls1131Service.findModelByKey(
						C101S01G.class, mainId, custId, dupNo);
				if (c101s01g != null) {
					delList.add(c101s01g);
				}
			}
			if (true) {
				C101S01Q c101s01q = cls1131Service.findModelByKey(
						C101S01Q.class, mainId, custId, dupNo);
				if (c101s01q != null) {
					delList.add(c101s01q);
				}
			}
			if (true) {
				C101S01R c101s01r = cls1131Service.findModelByKey(
						C101S01R.class, mainId, custId, dupNo);
				if (c101s01r != null) {
					delList.add(c101s01r);
				}
			}
			cls1131Service.delete(delList);
			
			//是否執行雙軌
			boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
			if(scoreDoubleTrack){ //雙軌運行中，把雙軌的刪除
				List<GenericBean> delList_N = new ArrayList<GenericBean>();
				if (true) {
					C101S01G_N c101s01g_n = cls1131Service.findModelByKey(
							C101S01G_N.class, mainId, custId, dupNo);
					if (c101s01g_n != null) {
						delList_N.add(c101s01g_n);
					}
				}
				if (true) {
					C101S01Q_N c101s01q_n = cls1131Service.findModelByKey(
							C101S01Q_N.class, mainId, custId, dupNo);
					if (c101s01q_n != null) {
						delList_N.add(c101s01q_n);
					}
				}
				if (true) {
					C101S01R_N c101s01r_n = cls1131Service.findModelByKey(
							C101S01R_N.class, mainId, custId, dupNo);
					if (c101s01r_n != null) {
						delList_N.add(c101s01r_n);
					}
				}
				cls1131Service.delete(delList_N);
			}
		}

		if (delR && !del_GradeModel) { // 若之前已進到 del_GradeModel, 就把 C101S01R 刪掉了
			List<GenericBean> delList = new ArrayList<GenericBean>();
			if (true) {
				C101S01R c101s01r = cls1131Service.findModelByKey(
						C101S01R.class, mainId, custId, dupNo);
				if (c101s01r != null) {
					delList.add(c101s01r);
				}
			}
			cls1131Service.delete(delList);
		}
		
		//是否執行雙軌
		boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
		if(scoreDoubleTrack){ //雙軌運行中，把雙軌的刪除
			if (delR_N && !del_GradeModel) { // 若之前已進到 del_GradeModel, 就把 C101S01R 刪掉了
				List<GenericBean> delList_N = new ArrayList<GenericBean>();
				if (true) {
					C101S01R_N c101s01r_n = cls1131Service.findModelByKey(
							C101S01R_N.class, mainId, custId, dupNo);
					if (c101s01r_n != null) {
						delList_N.add(c101s01r_n);
					}
				}
				cls1131Service.delete(delList_N);
			}
		}
		
		return result;
	}

	/**
	 * 房貸、非房貸評等的共用部份<br>
	 * 由 C101M01A.class, C101S01A.class, C101S01B.class,C101S01C.class 抓出共用的欄位
	 * 
	 * @param result
	 * @param params
	 * @param custId
	 * @param dupNo
	 * @throws CapException
	 */
	private JSONObject fetch_score_src(PageParameters params, String custId,
			String dupNo, String version_G, String version_NotHouseLoan,
			String version_R) throws CapException {
		JSONObject result = new JSONObject();

		Class<?>[] Classes = { C101M01A.class, C101S01A.class, C101S01B.class,
				C101S01C.class };
		for (Class<?> clazz : Classes) {
			JSONObject paramForm = ClsUtil.getJson(params, clazz);
			result.putAll(paramForm);

			// 房貸
			if (Util.equals(ClsScoreUtil.V1_3_HOUSE_LOAN, version_G)) {

			} else if (Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN, version_G) 
					|| Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN, version_G)) {
				if (clazz == C101S01B.class) {
					result.put(Score.column.個人年所得,
							ClsUtil.get_pIncome_from_uiC101S01BForm(paramForm));
				}
			}

			// 非房貸
			if (true) {
				if (Util.equals(ClsScoreUtil.V1_0_NOT_HOUSE_LOAN,
						version_NotHouseLoan)) {
					Map<String, String> m = new HashMap<String, String>();
					if (clazz == C101S01A.class) {
						m.putAll(ClsUtil.map_c101s01q_c101s01a_V1_0());
					}
					if (clazz == C101S01B.class) {
						m.putAll(ClsUtil.map_c101s01q_c101s01b_V1_0());
					}
					for (String q_column : m.keySet()) {
						String column_s01 = m.get(q_column);
						result.put(q_column, paramForm.get(column_s01));
					}
				} else if (Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN,
						version_NotHouseLoan)) {
					if (clazz == C101S01B.class) {
						ClsUtil.set_c101s01q_factor_V2_0(result, paramForm);
					}
				} else if (Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN,
						version_NotHouseLoan)) {
					if (clazz == C101S01B.class) {
						ClsUtil.set_c101s01q_factor_V2_1(result, paramForm);
					}
				} else if (Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN,version_NotHouseLoan) 
						|| Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN,version_NotHouseLoan)) {
					if (clazz == C101S01B.class) {
						ClsScoreUtil.set_c101s01q_factor_V3_0_c101s01b(result,
								paramForm);
					}
					if (clazz == C101S01C.class) {
						ClsScoreUtil.set_c101s01q_factor_V3_0_c101s01c(result,
								paramForm);
					}
				} else if (Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN,
						version_NotHouseLoan)) {
					if (clazz == C101S01A.class) {
						ClsScoreUtil.set_c101s01q_factor_V4_0_c101s01a(result,
								paramForm);
					}
					if (clazz == C101S01C.class) {
						ClsScoreUtil.set_c101s01q_factor_V4_0_c101s01c(result,
								paramForm);
					}
				}
			}

			// 卡友貸
			if (true) {
				if (Util.equals(ClsScoreUtil.V2_1_CARD_LOAN, version_R)) {
					if (clazz == C101S01B.class) {
						ClsUtil.set_c101s01r_factor_V2_1(result, paramForm);
					}
				} else if (Util.equals(ClsScoreUtil.V3_0_CARD_LOAN, version_R) 
						|| Util.equals(ClsScoreUtil.V3_1_CARD_LOAN, version_R)) {
					if (clazz == C101S01B.class) {
						ClsScoreUtil.set_c101s01r_factor_V3_0_c101s01b(result,
								paramForm);
					}
					if (clazz == C101S01C.class) {
						ClsScoreUtil.set_c101s01r_factor_V3_0_c101s01c(result,
								paramForm);
					}
				} else if (Util.equals(ClsScoreUtil.V4_0_CARD_LOAN,version_R)) {
					if (clazz == C101S01A.class) {
						ClsScoreUtil.set_c101s01r_factor_V4_0_c101s01a(result,
								paramForm);
					}
					if (clazz == C101S01C.class) {
						ClsScoreUtil.set_c101s01r_factor_V4_0_c101s01c(result,
								paramForm);
					}
					
				}
			}
		}
		// 取得聯徵和票信資料
		result.putAll(scoreService.getData(custId, dupNo, version_G,
				version_NotHouseLoan, version_R, result));

		return result;
	}

	/**
	 * 查詢資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult queryData(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<GenericBean> list = new ArrayList<GenericBean>();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String custName = Util.trim(params.getString("custName"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String type = Util.trim(params.getString("type"));

		C101S01E c101s01e = cls1131Service.findModelByKey(C101S01E.class,
				mainId, custId, dupNo, true);
		DataParse.toBean(ClsUtil.getJson(params, C101S01E.class), c101s01e);
		C101S01J c101s01j = cls1131Service.findModelByKey(C101S01J.class,
				mainId, custId, dupNo, true);
		c101s01j.setEName(c101s01e.getEName()); // 設置英文名

		if (true) {
			// 當要處理 L120S01M 時, 先 delete
			if (Util.isEmpty(type)
					|| ClsConstants.C101S01E.授信信用風險管理_遵循檢核.equals(type)) {
				cls1131Service.deleteL120s01mno(mainId, custId, dupNo);
			}
		}
		JSONObject json = new JSONObject();
		// 取得聯徵和票信資料
		params.put(ClsConstants.C101S01E.聯徵查詢日期,
				Util.toAD(c101s01e.getEJcicQDate()));
		json.putAll(cls1131Service.queryData(list, c101s01e, c101s01j, params));

		CapAjaxFormResult output_m01a_json = new CapAjaxFormResult();
		if (true) {
			// 個金徵信借款人主檔-婉卻記錄
			JSONObject C101M01AJson_1 = (JSONObject) json
					.get(ClsUtil.CLS1131_QUERY_SINGLE_C101M01A_1);

			// 異常通報 abnormalBrNo, abnormalDate, abnormalStatus, abnormalMainId
			JSONObject C101M01AJson_29 = (JSONObject) json
					.get(ClsUtil.CLS1131_QUERY_SINGLE_C101M01A_29);

			C101M01A c101m01a = cls1131Service.findModelByKey(C101M01A.class,
					mainId, custId, dupNo);
			DataParse.toBean(Util.trim(params.getString("C101M01AForm")),
					c101m01a);

			boolean update_m01a = false;

			if (C101M01AJson_1 != null) {
				update_m01a = true;
				// ~~~~~~
				DataParse.toBean(C101M01AJson_1, c101m01a);
			}
			if (C101M01AJson_29 != null) {
				update_m01a = true;
				// ~~~~~~
				DataParse.toBean(C101M01AJson_29, c101m01a);
			}

			if (update_m01a) {
				list.add(c101m01a);
			}
			// ------

			if (true) {
				if (C101M01AJson_1 != null) {
					output_m01a_json.putAll(C101M01AJson_1);
				}
				if (C101M01AJson_29 != null) {
					output_m01a_json.putAll(C101M01AJson_29);
				}
				if (true) {
					// 無 信用卡(krm040)及授信紀錄(bam095)時，出現提示訊息
					output_m01a_json.set("naturalFlag",
							Util.trim(c101m01a.getNaturalFlag()));
					output_m01a_json.set("jcicFlg",
							Util.trim(c101m01a.getJcicFlg()));
				}
			}
		}

		// 儲存
		DataParse.toBean(json, c101s01e);

		if (true) {
			if (true) {
				ClsUtil.set_date_jcicFlg_V_NN(output_m01a_json,
						c101s01e.getEJcicQDate());
			}

			result.set("C101M01AForm", output_m01a_json);
		}

		if (true) {// C101S01J.eName 長度>C101S01E.eName
			String eName = c101s01e.getEName();
			if (Util.notEquals(eName, Util.truncateString(eName, StrUtils
					.getEntityFileldLegth(C101S01E.class, "eName",
							MAXLEN_C101S01E_ENAME)), false)) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", "eName【" + eName + "】");
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
			}
		}

		// add by Fantasy 2013/03/29
		// 無法提供票信電子資料
		if (UtilConstants.DEFAULT.是.equals(c101s01e.getIsFromOld())) {
			c101s01e.setIsQdata9(UtilConstants.haveNo.NA);
			c101s01e.setIsQdata10(UtilConstants.haveNo.NA);
		}
		// 有無票信退補記錄
		if (UtilConstants.haveNo.有.equals(c101s01e.getIsQdata9())
				|| UtilConstants.haveNo.有.equals(c101s01e.getIsQdata10())) {
			c101s01e.setEChkFlag(UtilConstants.haveNo.有);
		} else if (UtilConstants.haveNo.無.equals(c101s01e.getIsQdata9())
				&& UtilConstants.haveNo.無.equals(c101s01e.getIsQdata10())) {
			c101s01e.setEChkFlag(UtilConstants.haveNo.無);
		} else {
			c101s01e.setEChkFlag(UtilConstants.haveNo.NA);
		}
		// 有無聯徵逾催呆記錄
		if (UtilConstants.haveNo.有.equals(c101s01e.getIsQdata11())) {
			c101s01e.setEJcicFlag(UtilConstants.haveNo.有);
		} else if (UtilConstants.haveNo.無.equals(c101s01e.getIsQdata11())) {
			c101s01e.setEJcicFlag(UtilConstants.haveNo.無);
		} else {
			c101s01e.setEJcicFlag(UtilConstants.haveNo.NA);
		}

		list.add(c101s01e);
		c101s01j.setIsQdata12(c101s01e.getIsQdata12()); // 身分證補、換發紀錄
		c101s01j.setIsQdata15(c101s01e.getIsQdata15()); // 成年監護制度查詢紀錄
		list.add(c101s01j);
		cls1131Service.save(mainId, custId, dupNo, type, list);

		CapAjaxFormResult _C101S01EForm = DataParse.toResult(c101s01e);
		if (true) {
			LMSUtil.setL120M01M(_C101S01EForm,
					cls1131Service.findL120s01m(mainId, custId, dupNo));

			ClsUtil.setC101S01E_wm_data(_C101S01EForm,
					DataParse.toJSON(c101s01e));

			// for amlDiv
			String amlRefNo = "";
			String amlRefOid = "";
			if (c101s01j != null) {
				amlRefNo = Util.trim(c101s01j.getAmlRefNo());
				amlRefOid = Util.trim(c101s01j.getAmlRefOid());
			}
			L120S09B l120s09b = clsService.findL120S09B_refNo_or_oid(amlRefNo,
					amlRefOid);
			L120S09A l120s09a = clsService.findL120S09A_cls1131(l120s09b);
			Map<String, String> map_ncResult = clsService
					.get_codeTypeWithOrder("SAS_NC_Result");
			//J-113-0082 web e-Loan消金徵信新增告誡戶掃描功能
			if (Util.equals(type , "cmfWarnp")) {
				Map<String, Object> cmfWarnp = lmsService.queryOdsCmfwarnp(custId);
				if (Util.isNotEmpty(cmfWarnp)) {
					if(l120s09a!=null){
						l120s09a.setCmfwarnpResult(Util.trim(cmfWarnp.get("status")));
						l120s09a.setCmfwarnpQueryResultInfo(Util.trim(cmfWarnp.get("msg")));
						l120s09a.setCmfwarnpQueryTime(new Timestamp(System.currentTimeMillis()));
						clsService.save(l120s09a);
					}
					else{
						result.set("alterMsg", "請先查詢AML後再查詢告誡戶");
					}
				}
			}
			ClsUtil.set_msg_L120S09B(_C101S01EForm, l120s09b, l120s09a,
					map_ncResult);
		}
		// 大數據風險查詢結果處理
		C101S02B c101s02b = cls1131Service.findModelByKey(C101S02B.class, mainId,
				custId, dupNo);
		if (c101s02b != null) {
			CapAjaxFormResult c101s02bData = DataParse.toResult(c101s02b);
			_C101S01EForm.add(c101s02bData);
		}
		result.set("C101S01EForm", _C101S01EForm);
		JSONObject tmp = (JSONObject) result.get("C101S01EForm");
		tmp.put("ans1", c101s01j.getAns1());
		result.set("C101S01EForm", tmp.toString());

		if (true) {
			CapAjaxFormResult param_map = new CapAjaxFormResult();
			param_map.set("type", type);
			result.set("param_map", param_map);
		}
		return result;
	}// ;

	/**
	 * 引進資料 　　個金徵信 > 放款信用評分表 > 是否填列:是 > 才出現「引進資料」
	 * 
	 * @param params
	 * @return
	 * @throws CapExceptionbrmp_creditCheck_new
	 */
	@DomainAuth(AuthType.Query)
	public IResult importData(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 檢核
		String errorMessage = getCheckMessage(params, ClsUtil.C101CheckClass,
				ImportCheck.class, null);
		if (Util.isNotEmpty(errorMessage)) {
			throw new CapMessageException(errorMessage, getClass());
		}
		// C101S01A．個金基本資料檔
		JSONObject C101S01AJson = ClsUtil.getJson(params, C101S01A.class);
		// C101S01B．個金服務單位檔
		JSONObject C101S01BJson = ClsUtil.getJson(params, C101S01B.class);
		// C101S01C．個金償債能力檔
		JSONObject C101S01CJson = ClsUtil.getJson(params, C101S01C.class);
		// C101S01F．個金放款信用評分表(無擔用)
		JSONObject C101S01FJson = ClsUtil.getJson(params, C101S01F.class);

		JSONObject data = new JSONObject();
		// 本人及配偶最近年所得
		data.put("yFamAmt", Util.trim(C101S01CJson.get("yFamAmt")));
		// 職業
		data.put("jobType1", Util.trim(C101S01BJson.get("jobType1")));
		data.put("jobTitle", Util.trim(C101S01BJson.get("jobTitle")));
		// 工作年資
		data.put("seniority", Util.trim(C101S01BJson.get("seniority")));
		// 不動產狀況
		data.put("cmsStatus", Util.trim(C101S01AJson.get("cmsStatus")));
		// 家庭狀況
		data.put("marry", Util.trim(C101S01AJson.get("marry")));
		String child = Util.trim(C101S01AJson.get("child"));
		data.put("child", Util.isEmpty(child) ? 0 : child);
		// 住宅狀況
		data.put("houseStatus", Util.trim(C101S01AJson.get("houseStatus")));
		// 負債比率
		data.put("yRate", Util.trim(C101S01CJson.get("yRate")));
		// 與銀行往來
		data.put("item08", Util.trim(C101S01CJson.get("item08")));

		// 評分
		JSONObject score = scoreService.score(Score.type.無擔, data,
				ClsScoreUtil.V1_3_HOUSE_LOAN);
		score.put(EloanConstants.OID,
				Util.trim(C101S01FJson.get(EloanConstants.OID)));// OID
		score.put("chkFlag", Util.trim(C101S01FJson.get("chkFlag")));// 是否填列
		result.set("C101S01FForm", new CapAjaxFormResult(score));

		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}// ;

	/**
	 * 讀取評等資料
	 * <ul>
	 * <li>個金徵信 > 基本資料 > 開啟等級評分表
	 * <li>個金徵信 > 相關查詢暨評等模型
	 * </ul>
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult loadScore(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C101S01G model_g = null;
		C101S01Q model_q = null;
		C101S01R model_r = null;
		
		String markModel = Util.trim(params.getString("markModel",
				UtilConstants.L140S02AModelKind.房貸));

		if (Util.equals(markModel, UtilConstants.L140S02AModelKind.房貸)) {
			model_g = cls1131Service.findModelByKey(C101S01G.class, mainId,
					custId, dupNo);
		} else if (Util.equals(markModel, UtilConstants.L140S02AModelKind.非房貸)) {
			model_q = cls1131Service.findModelByKey(C101S01Q.class, mainId,
					custId, dupNo);
		} else if (Util.equals(markModel, UtilConstants.L140S02AModelKind.卡友貸)) {
			model_r = cls1131Service.findModelByKey(C101S01R.class, mainId,
					custId, dupNo);
		}
		if (model_g != null) {
			CapAjaxFormResult formResult = cls1131Service.loadScore_G(model_g);
			// 評等訊息
			String checkItemRange = clsService.getCheckItemRange(model_g);
			formResult.putAll(ClsUtil.procMarkModel_G(model_g, "", checkItemRange));
			//J-111-0271 消金房貸3.0,計算[與最高分這差距]、[影響性]
			String varVer = Util.trim(model_g.getVarVer());
			if(Util.equals(varVer, ClsScoreUtil.V3_0_HOUSE_LOAN)){
				//與最高分差距
				//Step1. 定義9個因子分數欄位
				String[] scoreArr = ClsScoreUtil.scoreArr_G_3_0;
				String[] HighGapArr = ClsScoreUtil.HighGapArr_G_3_0;
				String[] InfluenceArr = ClsScoreUtil.InfluenceArr_G_3_0;
				String[] HighScoreArr = ClsScoreUtil.HighScoreArr_G_3_0;
				
				cls1131Service.newScoreModel_01(formResult,scoreArr,HighGapArr,InfluenceArr, HighScoreArr);
			}
			if(scoreDoubleTrack){ //雙軌資料
				cls1131Service.loadScoreSDT(formResult, markModel, mainId, custId, dupNo);
			}
			result.set("C101S01GForm", formResult);
		}
		if (model_q != null) {
			CapAjaxFormResult formResult = cls1131Service.loadScore_Q(model_q);
			// 評等訊息
			String checkItemRange = clsService.getCheckItemRange(model_q);
			formResult.putAll(ClsUtil.procMarkModel_Q(model_q, "", checkItemRange));
			ClsUtil.set_Q_chkItem(formResult, model_q);
			//J-111-0373消金非房貸4.0,計算[與最高分這差距]、[影響性]
			String varVer = Util.trim(model_q.getVarVer());
			if(Util.equals(varVer, ClsScoreUtil.V4_0_NOT_HOUSE_LOAN)){ //非房貸4.0
				//與最高分差距
				//Step1. 定義5個因子分數欄位
				String[] scoreArr = ClsScoreUtil.scoreArr_Q_4_0;
				String[] HighGapArr = ClsScoreUtil.HighGapArr_Q_4_0;
				String[] InfluenceArr = ClsScoreUtil.InfluenceArr_Q_4_0;
				String[] HighScoreArr = ClsScoreUtil.HighScoreArr_Q_4_0;
				
				cls1131Service.newScoreModel_01(formResult,scoreArr,HighGapArr,InfluenceArr,HighScoreArr);
			}
			if(scoreDoubleTrack){ //雙軌資料
				cls1131Service.loadScoreSDT(formResult, markModel, mainId, custId, dupNo);
			}
			result.set("C101S01QForm", formResult);
		}
		if (model_r != null) {
			CapAjaxFormResult formResult = cls1131Service.loadScore_R(model_r);
			// 供「總處單位」檢視「評分表內容」 => [1]針對J10分數加工, [2]初始評等在不同子頁籤出現
			String checkItemRange = clsService.getCheckItemRange(model_r);
			formResult.putAll(ClsUtil.procMarkModel_R(model_r, "", checkItemRange));
			ClsUtil.set_R_chkItem(formResult, model_r);
			//J-111-0373消金非房貸4.0,計算[與最高分這差距]、[影響性]
			String varVer = Util.trim(model_r.getVarVer());
			if(Util.equals(varVer, ClsScoreUtil.V4_0_NOT_HOUSE_LOAN)){ //非房貸4.0
				//與最高分差距
				//Step1. 定義5個因子分數欄位
				String[] scoreArr = ClsScoreUtil.scoreArr_R_4_0;
				String[] HighGapArr = ClsScoreUtil.HighGapArr_R_4_0;
				String[] InfluenceArr = ClsScoreUtil.InfluenceArr_R_4_0;
				String[] HighScoreArr = ClsScoreUtil.HighScoreArr_R_4_0;
				
				cls1131Service.newScoreModel_01(formResult,scoreArr,HighGapArr,InfluenceArr,HighScoreArr);
			}
			if(scoreDoubleTrack){ //雙軌資料
				cls1131Service.loadScoreSDT(formResult, markModel, mainId, custId, dupNo);
			}
			result.set("C101S01RForm", formResult);
		}
		
		return result;
	}// ;
	
	

	/**
	 * (PRIMARY_CARD ==0 and BUSINESS_OR_P_CARD==0 and ADDITIONAL_CARD>0)
	 * 
	 * @param c101m01a
	 * @return
	 */
	private boolean only_additional_card(C101M01A c101m01a) {
		if (c101m01a != null) {
			if (c101m01a.getPrimary_card() != null
					&& c101m01a.getPrimary_card().compareTo(BigDecimal.ZERO) > 0) {
				return false;
			}
			if (c101m01a.getBusiness_or_p_card() != null
					&& c101m01a.getBusiness_or_p_card().compareTo(
							BigDecimal.ZERO) > 0) {
				return false;
			}
			if (c101m01a.getAdditional_card() != null
					&& c101m01a.getAdditional_card().compareTo(BigDecimal.ZERO) > 0) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 讀取評等資料 當 result.set("adjust", true); → 最多可調升3等 <br/>
	 * ● 房貸　：最高可升3等 <br/>
	 * ● 非房貸：最高可升2等
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult loadAdjust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String markModel = Util.trim(params.getString("markModel"));

		if (Util.equals(UtilConstants.L140S02AModelKind.房貸, markModel)) {
			C101S01G c101s01g = cls1131Service.findModelByKey(C101S01G.class,
					mainId, custId, dupNo);

			C101M01A c101m01a = cls1131Service.findModelByKey(C101M01A.class,
					mainId, custId, dupNo);

			if (c101s01g != null) {
				String latest_G_varVer = scoreService.get_Version_HouseLoan();
				if (Util.notEquals(c101s01g.getVarVer(), latest_G_varVer)) {
					if (scoreService.clsScore_inBufferPeriod("G",
							latest_G_varVer)) {
						// 仍允許
					} else {
						// message.varVersion_diff.G=[房貸]目前版本{0}並非最新的模型版本{1}，請重新引進
						throw new CapMessageException(MessageFormat.format(
								getI18nMsg("message.varVersion_diff.G"),
								c101s01g.getVarVer(), latest_G_varVer),
								getClass());
					}
				}
				result.set("adjustForm", DataParse.toResult(c101s01g));

				// 【初始評等為6等(含)以下 Grade1 >= 6】且
				// 【未持有信用卡客戶CardFlag==Y or 只持有附卡】
				if (Util.parseInt(c101s01g.getGrade1()) >= 6
						&& (UtilConstants.DEFAULT.是.equals(Util.trim(c101s01g
								.getCardFlag())) || only_additional_card(c101m01a))) {
					C101S01B c101s01b = cls1131Service.findModelByKey(
							C101S01B.class, mainId, custId, dupNo);
					C101S01C c101s01c = cls1131Service.findModelByKey(
							C101S01C.class, mainId, custId, dupNo);
					if (c101s01c != null && c101s01b != null) {
						// 調等條件:【家庭所得收入高於120萬且家庭負債低於40%】 或【
						// 個人所得收入高於80萬且個人負債低於40%】

						// 原程式有問題
						// Util.parseInt(new BigDecimal("45.00")) 會得到 0

						// int personalIncome = 0;
						// personalIncome +=
						// Util.parseInt(c101s01b.getPayAmt());
						// personalIncome +=
						// Util.parseInt(c101s01b.getOthAmt());
						// if ((Util.parseInt(c101s01c.getYFamAmt()) >= 120 &&
						// Util
						// .parseInt(c101s01c.getYRate()) <= 40)
						// || (personalIncome >= 80 && Util
						// .parseInt(c101s01c.getDRate()) <= 40)) {
						// result.set("adjust", true);
						// }

						if (true) {
							BigDecimal personalIncome = BigDecimal.ZERO;
							if (c101s01b.getPayAmt() != null) {
								personalIncome = personalIncome.add(c101s01b
										.getPayAmt());
							}
							if (c101s01b.getOthAmt() != null) {
								personalIncome = personalIncome.add(c101s01b
										.getOthAmt());
							}

							boolean allow3 = false;
							// ======================
							BigDecimal yFamAmt = c101s01c.getYFamAmt();
							BigDecimal yRate = c101s01c.getYRate();
							BigDecimal dRate = c101s01c.getDRate();
							if (yFamAmt != null
									&& yRate != null
									&& yFamAmt.compareTo(new BigDecimal("120")) >= 0
									&& yRate.compareTo(new BigDecimal("40")) <= 0) {

								allow3 = true;
							}
							if (dRate != null
									&& personalIncome.compareTo(new BigDecimal(
											"80")) >= 0
									&& dRate.compareTo(new BigDecimal("40")) <= 0) {

								allow3 = true;
							}
							if (allow3) {
								result.set("adjust", true);
							}
						}
					}
				}
			}
		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, markModel)) {

			C101S01Q c101s01q = cls1131Service.findModelByKey(C101S01Q.class,
					mainId, custId, dupNo);
			if (c101s01q != null) {
				String latest_q_varVer = scoreService
						.get_Version_NotHouseLoan();
				if (Util.notEquals(c101s01q.getVarVer(), latest_q_varVer)) {
					if (scoreService.clsScore_inBufferPeriod("Q",
							latest_q_varVer)) {
						// 仍允許
					} else {
						// message.varVersion_diff=目前版本{0}並非最新的模型版本{1}，請重新引進
						throw new CapMessageException(MessageFormat.format(
								getI18nMsg("message.varVersion_diff"),
								c101s01q.getVarVer(), latest_q_varVer),
								getClass());
					}
				}
				result.set("adjustNotHouseLoanForm",
						DataParse.toResult(c101s01q));
				// ===========================
				// 非房貸的昇等限制(最高 提昇2 等)
				// ===========================
			}
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, markModel)) {

			C101S01R c101s01r = cls1131Service.findModelByKey(C101S01R.class,
					mainId, custId, dupNo);
			if (c101s01r != null) {
				String latest_r_varVer = scoreService.get_Version_CardLoan();
				if (Util.notEquals(c101s01r.getVarVer(), latest_r_varVer)) {
					if (scoreService.clsScore_inBufferPeriod("R",
							latest_r_varVer)) {
						// 仍允許
					} else {
						// message.varVersion_diff=目前版本{0}並非最新的模型版本{1}，請重新引進
						throw new CapMessageException(MessageFormat.format(
								getI18nMsg("message.varVersion_diff"),
								c101s01r.getVarVer(), latest_r_varVer),
								getClass());
					}
				}
				result.set("adjustCardLoanForm", DataParse.toResult(c101s01r));
				// ===========================
				// 卡友貸的昇等限制(最高 提昇2 等)
				// ===========================
			}
		}

		if(true){
			Set<String> eloanRoles = MegaSSOSecurityContext.getEloanRoles();
			String pgmDept = MegaSSOSecurityContext.getPGMDept();
			int transactionCode = Util.parseInt(params
					.getString(EloanConstants.TRANSACTION_CODE));
					
			boolean _Modify = au.auth(pgmDept, eloanRoles, transactionCode,
					AuthType.Modify);
			
			if(_Modify){
			}else{
				result.set("lockAdjust", "Y");
			}
		}
		return result;
	}// ;

	@DomainAuth(AuthType.Modify)
	public IResult saveAdjustCheckMowtypeM(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String adjustForm = Util.trim(params.getString("adjustForm"));
		JSONObject adjustJson = DataParse.toJSON(adjustForm);

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// String custId = Util.trim(params.getString("custId"));
		// String dupNo = Util.trim(params.getString("dupNo"));

		String posId = Util.trim(adjustJson.optString("posId"));
		String posDupNo = Util.trim(adjustJson.optString("posDupNo"));
		String posKindD = Util.trim(adjustJson.optString("posKindD"));
		String adjustStatus = Util.trim(adjustJson.optString("adjustStatus"));
		String adjustFlag = Util.trim(adjustJson.optString("adjustFlag"));

		String posName = "";
		String confirmMsg = "";

		if (Util.equals("1", adjustStatus) && Util.equals("4", adjustFlag)) {
			C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);

			if (true) {
				List<String> errorMsgList = new ArrayList<String>();
				if (Util.isEmpty(posId)) {
					errorMsgList.add(MessageFormat.format(
							getI18nMsg("message.emptyField"), "一般保證人身分證字號"));
				}
				if (Util.isEmpty(posDupNo)) {
					errorMsgList.add(MessageFormat.format(
							getI18nMsg("message.emptyField"), "一般保證人身分證重複碼"));
				}
				if (Util.isEmpty(posKindD)) {
					errorMsgList.add(MessageFormat.format(
							getI18nMsg("message.emptyField"), "一般保證人關係類別"));
				}
				if (true) {
					String part1 = LMSUtil.getCustKey_len10custId(
							c101m01a.getCustId(), c101m01a.getDupNo());
					String part2 = LMSUtil.getCustKey_len10custId(posId,
							posDupNo);
					if (Util.equals(part1, part2)) {
						errorMsgList.add(MessageFormat.format(
								prop_AbstractOverSeaCLSPage
										.getProperty("message.upGradePos02"),
								part1, part2));
					}
				}
				if (true) {
					// 升等時, 若一般保證人輸入 公司戶ID , 阻擋
					String busCode = clsService
							.get0024_busCode(posId, posDupNo);
					if (!LMSUtil.isBusCode_060000_130300(busCode)) {
						errorMsgList.add(MessageFormat.format(
								prop_AbstractOverSeaCLSPage
										.getProperty("message.upGradePos03"),
								posId + "-" + posDupNo));
					}
				}
				if (errorMsgList.size() > 0) {
					throw new CapMessageException(StringUtils.join(
							errorMsgList, UtilConstants.Mark.HTMLBR),
							getClass());
				}
			}
			// ===============
			if (true) {
				C101M01A pos_m01a = clsService.findC101M01A_brIdDup(
						c101m01a.getOwnBrId(), posId, posDupNo);
				if (pos_m01a == null) {
					// message.upGradePos01=一般保證人{0}請先執行「相關資料查詢」
					throw new CapMessageException(MessageFormat.format(
							prop_AbstractOverSeaCLSPage
									.getProperty("message.upGradePos01"), posId
									+ "-" + posDupNo), getClass());
				}

				C101S01G pos_s01g = clsService.findC101S01G(pos_m01a);
				if (pos_s01g == null) {
					// message.upGradePos01=一般保證人{0}請先執行「相關資料查詢」
					throw new CapMessageException(MessageFormat.format(
							prop_AbstractOverSeaCLSPage
									.getProperty("message.upGradePos01"), posId
									+ "-" + posDupNo), getClass());
				}

				// 在 c101s01g 已有 custName
				posName = Util.trim(pos_s01g.getCustName());

				List<String> errorMsgList = new ArrayList<String>();
				// ================================================
				// 以下3個條件，需全部符合
				// 1為借款人之父母、兄弟姊妹、配偶、配偶之父母(以上任一人)
				// 2個人年收入為120萬以上，負債比率為40%以下者
				// 3無任何聯徵負面資訊者

				String posIdDupNm = posId + "-" + posDupNo + " " + posName;
				// message.upGradePosR1=一般保證人{0}【與借款人關係為{1}】。
				// 請注意不得以一般保證人{2}資信佳為調升評等理由。
				if (CrsUtil.inCollection(posKindD, new String[] { "XA", "XB",
						"XD", "XI" })) {
					/*
					 * XA 配偶 XB 父母 XD 兄弟姐妹 XI 配偶之父母
					 */
				} else {
					Map<String, String> map = clsService
							.get_codeTypeWithOrder("Relation_type2");
					String posKindD_msg = posKindD + " "
							+ LMSUtil.getDesc(map, posKindD);
					errorMsgList.add(MessageFormat.format(
							prop_AbstractOverSeaCLSPage
									.getProperty("message.upGradePosR1"),
							posIdDupNm, posKindD_msg, posIdDupNm));
				}

				if (true) {
					// message.upGradePosR2=一般保證人{0}【不符合年收入120萬以上且負債比40%以下】之資信條件，請注意不得以一般保證人{1}資信佳為調升評等理由。
					C101S01B s01b = clsService.findC101S01B(pos_m01a);
					C101S01C s01c = clsService.findC101S01C(pos_m01a);
					BigDecimal personalIncome = BigDecimal.ZERO;
					if (s01b != null) {
						if (s01b.getPayAmt() != null) {
							personalIncome = personalIncome.add(s01b
									.getPayAmt());
						}
						if (s01b.getOthAmt() != null) {
							personalIncome = personalIncome.add(s01b
									.getOthAmt());
						}
					}
					BigDecimal dRate = null;
					if (s01c != null) {
						dRate = s01c.getDRate();
					}
					if (dRate != null
							&& personalIncome.compareTo(new BigDecimal("120")) >= 0
							&& dRate.compareTo(new BigDecimal("40")) <= 0) {
						// ok
					} else {
						errorMsgList.add(MessageFormat.format(
								prop_AbstractOverSeaCLSPage
										.getProperty("message.upGradePosR2"),
								posIdDupNm, posIdDupNm));
					}
				}

				if (true) {
					C101S01E pos_s01e = clsService.findC101S01E(pos_m01a);
					// message.upGradePosR3A=一般保證人{0}【無一個月內之聯徵、票信查詢紀錄】，請注意不得以一般保證人{1}資信佳為調升評等理由。
					// message.upGradePosR3B=一般保證人{0}【{1}】，請注意不得以一般保證人{2}資信佳為調升評等理由。
					if (LMSUtil.qdate_expired(pos_s01e.getEJcicQDate(),
							pos_s01e.getEChkQDate())) {
						errorMsgList.add(MessageFormat.format(
								prop_AbstractOverSeaCLSPage
										.getProperty("message.upGradePosR3A"),
								posIdDupNm, posIdDupNm));
					}

					if (true) {
						String bailout_flag = ""; // 以 c120s01g.AdjustFlag=='4'
													// 做為調升評等
						String msg = StringUtils.replace(clsService.getClsGradeMessage(pos_s01g,
										UtilConstants.L140S02AModelKind.房貸,
										OverSeaUtil.TYPE_RAW, bailout_flag),
								EloanConstants.HTML_NEWLINE, "、");
						if (Util.isNotEmpty(msg)) {
							errorMsgList
									.add(MessageFormat.format(
											prop_AbstractOverSeaCLSPage
													.getProperty("message.upGradePosR3B"),
											posIdDupNm, msg, posIdDupNm));
						}
					}
				}
				if (errorMsgList.size() > 0) {
					throw new CapMessageException(StringUtils.join(
							errorMsgList, UtilConstants.Mark.HTMLBR),
							getClass());
				} else {
					String idDupNm = Util.trim(c101m01a.getCustId()) + "-"
							+ Util.trim(c101m01a.getDupNo()) + " "
							+ Util.trim(c101m01a.getCustName());
					confirmMsg = MessageFormat.format(
							prop_AbstractOverSeaCLSPage
									.getProperty("message.upGradePosCmf"),
							idDupNm, posIdDupNm);
				}

			}
		} else {
			// 升等理由, 若非一般保證人, 不需檢核 UI 上的資料
		}

		// =========================
		CapAjaxFormResult rtnObj = new CapAjaxFormResult();
		if (true) {
			rtnObj.set("posName", posName);
		}
		result.set("rtnObj", rtnObj);
		if (Util.isNotEmpty(confirmMsg)) {
			result.set("confirmMsg", confirmMsg);
		}
		return result;
	}

	/**
	 * 儲存評等資料(調整評等)
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveAdjust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String markModel = Util.trim(params.getString("markModel"));

		if (Util.equals(UtilConstants.L140S02AModelKind.房貸, markModel)) {
			C101S01G model = cls1131Service.findModelByKey(C101S01G.class,
					mainId, custId, dupNo);
			if (model != null) {
				saveAdjust_mowType_M(result, model,
						Util.trim(params.getString("adjustForm")));
			}
		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, markModel)) {

			C101S01Q model_q = cls1131Service.findModelByKey(C101S01Q.class,
					mainId, custId, dupNo);

			if (model_q != null) {
				saveAdjust_mowType_N(result, model_q,
						Util.trim(params.getString("adjustNotHouseLoanForm")));
			}
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, markModel)) {

			C101S01R model_r = cls1131Service.findModelByKey(C101S01R.class,
					mainId, custId, dupNo);

			if (model_r != null) {
				saveAdjust_mowType_CardLoan(result, model_r,
						Util.trim(params.getString("adjustCardLoanForm")));
			}
		}

		return result;
	}// ;

	private void saveAdjust_mowType_M(CapAjaxFormResult result,
			C101S01G model, String adjustForm)
			throws CapMessageException, CapException {
		JSONObject adjustJson = DataParse.toJSON(adjustForm);
		// ---依 grade3重算 違約機率
		//房貸3.0模型不會調整評等，所以雙軌也無須重算[違約機率] >> 維持原本的作法
		JSONObject houseLoanDR = scoreService.score(Score.type.房貸違約機率,
				adjustJson, model.getVarVer());
		{
			scoreService.setHouseLoanDR(houseLoanDR, adjustJson);
		}
		// ---
		DataParse.toBean(adjustJson, model);
		model.setGrdTDate(CapDate.getCurrentTimestamp());

		if (true) {

			// J-107-0104 開放因一般保證人升等
			if (Util.equals("1", model.getAdjustStatus())
					&& Util.equals("4", model.getAdjustFlag())) {
				model.setPosName(Util.truncateString(model.getPosName(),
						MAXLEN_C101S01G_POSNAME));
			} else {
				model.setPosId("");
				model.setPosDupNo("");
				model.setPosName("");
				model.setPosKindD("");
			}
		}

		String adjustReason = Util.trim(model.getAdjustReason());
		if (Util.notEquals(
				Util.truncateString(adjustReason, MAXLEN_C101S01G_ADJUSTREASON),
				adjustReason)) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", "【調整理由】");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
		}
		if (Util.isNotEmpty(adjustReason)) {
			Map<String, String> inputMap = new HashMap<String, String>();
			Map<String, String> errorMap = new HashMap<String, String>();
			Map<String, String> confirmMap = new HashMap<String, String>();

			inputMap.put(model.getCustId() + "-" + model.getDupNo(),
					adjustReason);
			clsService.validate_adjustReason(inputMap, errorMap, confirmMap,
					"M", LMSUtil.MOWTYPE_M_CHK01);

			if (errorMap.size() > 0) {
				throw new CapMessageException(StringUtils.join(
						errorMap.values(), ""), getClass());
			}
		}
		cls1131Service.save(model);
		String checkItemRange = clsService.getCheckItemRange(model);
		adjustJson.putAll(ClsUtil.procMarkModel_G(model, "", checkItemRange));
		result.set(ClsUtil.GRADE_DIV_MARLMODEL_1, new CapAjaxFormResult(adjustJson));
	}

	private void saveAdjust_mowType_N(CapAjaxFormResult result,
			C101S01Q model_q, String adjustNotHouseLoanForm)
			throws CapMessageException, CapException {
		JSONObject adjustJson = DataParse.toJSON(adjustNotHouseLoanForm);
		// ---依 grade3重算 違約機率
		//非房貸4.0模型不會調整評等，所以雙軌也無須重算[違約機率] >> 維持原本的作法
		JSONObject notHouseLoanDR = scoreService
				.scoreNotHouseLoan(ScoreNotHouseLoan.type.非房貸違約機率, adjustJson,
						model_q.getVarVer());
		{
			scoreService.setNotHouseLoanDR(notHouseLoanDR, adjustJson);
		}
		// ---
		DataParse.toBean(adjustJson, model_q);
		model_q.setGrdTDate(CapDate.getCurrentTimestamp());

		String adjustReason = Util.trim(model_q.getAdjustReason());
		if (Util.notEquals(
				Util.truncateString(adjustReason, MAXLEN_C101S01Q_ADJUSTREASON),
				adjustReason)) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", "【調整理由】");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
		}
		if (Util.isNotEmpty(adjustReason)) {
			Map<String, String> inputMap = new HashMap<String, String>();
			Map<String, String> errorMap = new HashMap<String, String>();
			Map<String, String> confirmMap = new HashMap<String, String>();

			inputMap.put(model_q.getCustId() + "-" + model_q.getDupNo(),
					adjustReason);
			clsService.validate_adjustReason(inputMap, errorMap, confirmMap);

			if (errorMap.size() > 0) {
				throw new CapMessageException(StringUtils.join(
						errorMap.values(), ""), getClass());
			}
		}
		cls1131Service.save(model_q);
		String checkItemRange = clsService.getCheckItemRange(model_q);
		adjustJson.putAll(ClsUtil.procMarkModel_Q(model_q, "", checkItemRange));
		result.set(ClsUtil.GRADE_DIV_MARLMODEL_2, new CapAjaxFormResult(
				adjustJson));

	}

	private void saveAdjust_mowType_CardLoan(CapAjaxFormResult result,
			C101S01R model_r, String adjustCardLoanForm)
			throws CapMessageException, CapException {
		JSONObject adjustJson = DataParse.toJSON(adjustCardLoanForm);
		// ---依 grade3重算 違約機率
		//專案信貸(非團體)4.0模型不會調整評等，所以雙軌也無須重算[違約機率] >> 維持原本的作法
		JSONObject cardLoanDR = scoreService.scoreCardLoan(
				ScoreCardLoan.type.卡友貸違約機率, adjustJson, model_r.getVarVer());
		{
			scoreService.setCardLoanDR(cardLoanDR, adjustJson);
		}
		// ---
		DataParse.toBean(adjustJson, model_r);
		model_r.setGrdTDate(CapDate.getCurrentTimestamp());

		String adjustReason = Util.trim(model_r.getAdjustReason());
		if (Util.notEquals(
				Util.truncateString(adjustReason, MAXLEN_C101S01Q_ADJUSTREASON),
				adjustReason)) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", "【調整理由】");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
		}
		if (Util.isNotEmpty(adjustReason)) {
			Map<String, String> inputMap = new HashMap<String, String>();
			Map<String, String> errorMap = new HashMap<String, String>();
			Map<String, String> confirmMap = new HashMap<String, String>();

			inputMap.put(model_r.getCustId() + "-" + model_r.getDupNo(),
					adjustReason);
			clsService.validate_adjustReason(inputMap, errorMap, confirmMap);

			if (errorMap.size() > 0) {
				throw new CapMessageException(StringUtils.join(
						errorMap.values(), ""), getClass());
			}
		}
		cls1131Service.save(model_r);
		String checkItemRange = clsService.getCheckItemRange(model_r);
		adjustJson.putAll(ClsUtil.procMarkModel_R(model_r, "", checkItemRange));
		result.set(ClsUtil.GRADE_DIV_MARLMODEL_3, new CapAjaxFormResult(
				adjustJson));

	}

	/**
	 * 原本應該寫在 getCheckMessage(..., ..., Check3.class) 裡 但在公司戶時 會用 Check3.class
	 * 的結果去回應【尚未執行[相關資料查詢]】
	 * 
	 * 而「銀行法第33條之2、銀行法第33條之4」是由經辦自行輸入的 (無法由系統決定)
	 * 
	 * 為避免 c101s01e.mbRlt33 是空的，但卻出現【尚未執行[相關資料查詢]】，讓人誤會 所以多出此 method
	 */
	private String getManualInputColumnCheckMsg(C101S01E c101s01e) {
		List<String> msg_list = new ArrayList<String>();
		String mbRlt33 = Util.trim(c101s01e.getMbRlt33());
		String mbRltDscr33 = Util.trim(c101s01e.getMbRltDscr33());

		String isQdata2 = Util.trim(c101s01e.getIsQdata2());
		String isQdata3 = Util.trim(c101s01e.getIsQdata3());

		if (Util.isEmpty(mbRlt33)) {
			msg_list.add(MessageFormat.format(getI18nMsg("message.emptyField"),
					getI18nMsg("C101S01E.mbRlt33")));
		}
		if (UtilConstants.haveNo.有.equals(mbRlt33) && Util.isEmpty(mbRltDscr33)) {

		}
		String caseSrcFlag = Util.trim(c101s01e.getCaseSrcFlag());
		if (Util.isEmpty(caseSrcFlag) || caseSrcFlag.matches("[LPO]")) {
			msg_list.add(MessageFormat.format(getI18nMsg("message.emptyField"),
					getI18nMsg("C101S01E.caseSrcFlag")));
		} else {

			if (Util.equals("A", caseSrcFlag)) {
				// 經由地政士進件="是"
				List<C101S01Y> c101s01ys = cls1131Service.getC101S01YList(
						c101s01e.getMainId(), c101s01e.getCustId(),
						c101s01e.getDupNo());
				for (C101S01Y c101s01y : c101s01ys) {
					String laaName = Util.trim(c101s01y.getLaaName());
					String laaYear = Util.trim(c101s01y.getLaaYear());
					String laaWord = Util.trim(c101s01y.getLaaWord());
					String laaNo = Util.trim(c101s01y.getLaaNo());
					String laaDesc = Util.trim(c101s01y.getLaaDesc());
					
					if (Util.isEmpty(laaName)) {
						msg_list.add(MessageFormat.format(
								getI18nMsg("message.emptyField"),
								getI18nMsg("C101S01E.laaName")));
					}
					if (Util.isEmpty(laaYear) || Util.isEmpty(laaWord)
							|| Util.isEmpty(laaNo)) {
						msg_list.add(MessageFormat.format(
								getI18nMsg("message.emptyField"),
								getI18nMsg("C101S01E.label.laaCert")));
					}
					Map<String, Object> c900m01h = clsService
					.findActiveMajorC900M01HByCertNo2(laaYear, laaWord, laaNo);
					if (c900m01h != null) {
						String CtlFlagType = clsService.getCtlFlagType(MapUtils
								.getString(c900m01h, "CTLFLAG"));
						if (Util.isEmpty(laaDesc)
								&& Util.equals(CtlFlagType, LMSUtil.地政士黑名單警示名單)) {
							msg_list.add(MessageFormat.format(
									getI18nMsg("message.emptyField"),
									getI18nMsg("C101S01E.laaDesc")));
						}
						
						//J-109-0251_10702_B1001 Web e-Loan 授信管理系統調整地政士懲戒紀錄選項判斷邏輯
						if(Util.isEmpty(laaDesc) && clsService.checkLaaMatchRuleFlag(c900m01h)){
							msg_list.add(MessageFormat.format(
									getI18nMsg("message.emptyField"),
									getI18nMsg("C101S01E.laaDesc")));
						}
					}
				}
			} else if (Util.equals("B", caseSrcFlag)) {
				if (Util.isEmpty(Util.trim(c101s01e.getCaseSrcMemo()))) {
					msg_list.add("當" + getI18nMsg("C101S01E.caseSrcFlag") + "為"
							+ getI18nMsg("C101S01E.caseSrcFlag.B") + "，"
							+ getI18nMsg("C101S01E.label.caseSrcMemo") + "為必填");
				}
			}

			// if(CrsUtil.hasLaaCertData(c101s01e)
			// && !CrsUtil.hasLaaOfficeData(c101s01e)
			// &&
			// clsService.is_function_on_codetype("chk_laaOffice_when_haveCert")){
			// msg_list.add("當有輸入地政士資料，"+getI18nMsg("C101S01E.laaOffice")+"為必填");
			// }

//			if (Util.isNotEmpty(Util.trim(c101s01e.getLaaOfficeId()))
//					&& !CrsUtil.hasLaaOfficeData(c101s01e)) {
//				msg_list.add("當有輸入地政士事務所統編，" + getI18nMsg("C101S01E.laaOffice")
//						+ "為必填");
//			}
		}

		L120S01M l120s01m = cls1131Service.findL120s01m(c101s01e.getMainId(),
				c101s01e.getCustId(), c101s01e.getDupNo());
		if (l120s01m != null) {
			if (Util.notEquals(isQdata2, l120s01m.getMbRlt())
					|| Util.notEquals(isQdata3, l120s01m.getMhRlt44())) {
				// message.reQuery_l120s01m=借款人之「利害關係人」與「授信信用風險管理」遵循檢核引進時狀態有不一致，請重新引進！
				msg_list.add(getI18nMsg("message.reQuery_l120s01m"));
			}
		}
		
		if("3".equals(c101s01e.getIsQdata30())){
			// 聯徵T70尚未查詢成功
			msg_list.add(getI18nMsg("message.ejcicT70.inquiryNotBeenSuccessfulYet"));
		}
		
		return StringUtils.join(msg_list, EloanConstants.HTML_NEWLINE);
	}

	/**
	 * 取得檢核訊息
	 * 
	 * @param params
	 * @param clazzs
	 * @param checkClass
	 * @return
	 * @throws CapException
	 */
	private String getCheckMessage(PageParameters params, Class<?>[] clazzs,
			Class<?> checkClass, JSONObject commonJSON) throws CapException {
		StringBuilder sb = new StringBuilder();
		boolean J_113_0341_ON = clsService.is_function_on_codetype("J_113_0341_ON");
		String youngCareCheck = Util.trim(sysparamService.getParamValue("J_113_0341_youngCareCheck"));
		if (clazzs != null) {
			for (Class<?> clazz : clazzs) {
				String className = Util.trim(clazz.getSimpleName());
				String form = Util.trim(params.getString(className + "Form"));
				GenericBean model = cls1131Service.findModelByOid(clazz, null,
						true);
				DataParse.toBean(form, model);
				if (MapUtils.isNotEmpty(commonJSON)) {
					DataParse.toBean(commonJSON, model);
				}
				// ================
				StringBuilder title = new StringBuilder();
				/*
					title.C101S01A=基本資料
					title.C101S01B=服務單位
					title.C101S01C=償債能力
					title.C101S01D=配偶資料
					title.C101S01E=相關查詢資料
				*/
				title.append("<b>").append(getI18nMsg("title." + className))
						.append("</b>").append(EloanConstants.HTML_NEWLINE);
				// 個金基本資料檔
				boolean valid = BeanValidator.isValid(model, checkClass);
				if (!valid) {
					StringBuilder msg = new StringBuilder();
					msg.append(title.toString()).append(
							BeanValidator.getValidMsg(model,
									CLS1131S01Panel.class, checkClass));
					// 個金配偶資料檔(是否有配偶資料) add 2013/04/10
					if (clazz == C101S01D.class
							&& !ClsConstants.MateFlag.列於本欄.equals(Util
									.trim(model.get("mateFlag")))) {
						msg.delete(0, msg.length());
					}
					sb.append(msg.toString());
				}
				// check.class 儲存前的檢查
				if (checkClass == SaveCheck.class) {
					// 個金基本資料檔
					if (clazz == C101S01A.class) {
						// 存款往來分行代碼有值時,存款帳戶為必填
						if (Util.isNotEmpty(model.get("dpBrno"))) {
							if (Util.isEmpty(model.get("dpAcct"))) {
								if (valid){
									sb.append(title.toString());
								}
								sb.append(getI18nMsg(className + ".dpAcct"))
										.append("[必填欄位]")
										.append(EloanConstants.HTML_NEWLINE);
							}
						}
					}
					// 個金償債能力檔
					else if (clazz == C101S01B.class) {
						C101S01B c101s01b = (C101S01B) model;

						List<String> msg_list = new ArrayList<String>();
						if (true) {
							String experience = Util.trim(c101s01b
									.getExperience());
							if (Util.notEquals(Util.truncateString(experience,
									MAXLEN_C101S01B_EXPERIENCE), experience)) {

								msg_list.add(getI18nMsg("message.error01")); // message.error01=經歷不可超過80個字
							}
							
							if(clsService.is_function_on_codetype("c101s01b_comTel_notEmpty")){ 
								//J-109-0183 將「服務單位電話」改為必填。詢問過消金處，即使［退休、家管、待業］也可填入家中電話
								if (Util.isEmpty(Util.trim(c101s01b.getComTel()))) {
									if (valid){
										sb.append(title.toString());
									}
									sb.append(getI18nMsg("C101S01B.comTel"))
											.append("[必填欄位]")
											.append(EloanConstants.HTML_NEWLINE);
								}
							}
							String juId = Util.trim(c101s01b.getJuId());
							if (Util.equals("Y", c101s01b.getYnJuId())
									&& Util.isEmpty(juId)) {
								msg_list.add(MessageFormat
										.format(getI18nMsg("message.hasData_canNotEmpty"),
												getI18nMsg("C101S01B.juId")));
							}
							if (Util.isNotEmpty(juId)
									&& Util.notEquals(Util.truncateString(juId,
											MAXLEN_C101S01B_JUID), juId)) {
								msg_list.add(getI18nMsg("message.error02")); // message.error02=服務單位統一編號資料過長(請輸入統編，而非公司名稱)
							}
							if (Util.equals("Y", c101s01b.getYnJuTotalCapital())
									&& c101s01b.getJuTotalCapital() == null) {
								msg_list.add(MessageFormat
										.format(getI18nMsg("message.hasData_canNotEmpty"),
												getI18nMsg("C101S01B.juTotalCapital")));
							}
							if (Util.equals("Y",
									c101s01b.getYnJuPaidUpCapital())
									&& c101s01b.getJuPaidUpCapital() == null) {
								msg_list.add(MessageFormat
										.format(getI18nMsg("message.hasData_canNotEmpty"),
												getI18nMsg("C101S01B.juPaidUpCapital")));
							}
							
							if("Y".equals(c101s01b.getIsHasEsgScore())){
								
								BigDecimal esgScore = c101s01b.getEsgScore();
								//ESG分數選有，ESG分數欄位需填寫！
								if(esgScore == null){
									msg_list.add(MessageFormat
											.format(getI18nMsg("message.error03.esgScore.required"), getI18nMsg("C101S01B.juPaidUpCapital")));
								}
								//ESG分數欄位最高為100，請確認！
								if(esgScore != null && esgScore.compareTo(new BigDecimal(100)) > 0){
									msg_list.add(MessageFormat
											.format(getI18nMsg("message.error03.esgScore.moreThan100"), getI18nMsg("C101S01B.juPaidUpCapital")));
								}
								
							}
						}

						if (msg_list.size() > 0) {
							sb.append(title.toString());
							sb.append(StringUtils.join(msg_list,
									EloanConstants.HTML_NEWLINE));
						}
					}
					// 個金償債能力檔
					else if (clazz == C101S01C.class) {
						C101S01C c101s01c = (C101S01C) model;
						// 檢查夫妻年收入不能小於 (年薪 + 其他收入 + 配偶所得)
						if (!yFamAmtCheck(params)) {
							if (valid){
								sb.append(title.toString());
							}
							sb.append(getI18nMsg("message.checkFamAmt"))
									.append(EloanConstants.HTML_NEWLINE);
						}

						if (true) {
							if (c101s01c.getFincome()==null) {
								// message.emptyField={0}[必填欄位]
								/* throw new CapMessageException(
										MessageFormat
												.format(getI18nMsg("message.emptyField"),
														getI18nMsg("C101S01C.fincome")),
										getClass()); */
								if (valid){
									sb.append(title.toString());
								}
								sb.append(MessageFormat.format(getI18nMsg("message.emptyField"), getI18nMsg("C101S01C.fincome"))).append(EloanConstants.HTML_NEWLINE);
							}
						}
						// message.checkFamAmt_fincome=「夫妻年收入」不應大於「家庭年收入」
						if (!fincomeCheck(params)) {
							if (valid){
								sb.append(title.toString());
							}
							sb.append(getI18nMsg("message.checkFamAmt_fincome"))
									.append(EloanConstants.HTML_NEWLINE);
						}
						
						String mainId = Util.trim(params
								.getString(EloanConstants.MAIN_ID));
						String custId = Util.trim(params.getString("custId"));
						String dupNo = Util.trim(params.getString("dupNo"));
						C101S01C c101s01c2 = cls1131Service.findModelByKey(C101S01C.class, mainId, custId, dupNo);
						
						//J-110-0073 Web eloan授信系統因應負債比計算系統化，申請修改e-Loan程式 
						if (c101s01c2 != null) {
							if (CapString.trimNull(c101s01c2.getReCalFlg()).equals("N")) {
								sb.append("收入已異動，請重新執行負債比計算")
										.append(EloanConstants.HTML_NEWLINE);
							}
						}
						
						BigDecimal familyTotAsset = c101s01c.getfAssetTotAmt();
						BigDecimal indTotAsset = c101s01c.getIndAssetTotAmt();
						if(familyTotAsset != null && indTotAsset != null && familyTotAsset.compareTo(indTotAsset) <= 0){
							//「家庭資產總額」需大於等於「個人資產總額」
							sb.append(getI18nMsg("C101S01C.message.error.familyAssetTotAmtMustBeyondIndAssetTotAmt")).append(EloanConstants.HTML_NEWLINE);
						}
						
						//J-111-0373 檢核[本行有擔餘額]、[本行無擔餘額] 引入日期須在一個月內(30天)
						Date loanBalSTime = c101s01c.getLoanBalSTime();
						Date loanBalNTime = c101s01c.getLoanBalNTime();
						Date nowDate = new Date();
						if(loanBalSTime != null){
							int ss = Math.abs(CapDate.calculateDays(loanBalSTime, nowDate));
							if(ss > 30){
								sb.append(getI18nMsg("C101S01C.message.error.C101S01C.loanBalSByid.month")).append(EloanConstants.HTML_NEWLINE);
							}
						}else{
							sb.append(getI18nMsg("C101S01C.message.error.C101S01C.loanBalSByid.null")).append(EloanConstants.HTML_NEWLINE);
						}
						if(loanBalNTime != null){
							int nn = Math.abs(CapDate.calculateDays(loanBalNTime, nowDate));
							if(nn > 30){
								sb.append(getI18nMsg("C101S01C.message.error.C101S01C.loanBalNByid.month")).append(EloanConstants.HTML_NEWLINE);
							}
						}else{
							sb.append(getI18nMsg("C101S01C.message.error.C101S01C.loanBalNByid.null")).append(EloanConstants.HTML_NEWLINE);
						}

					}
					// 個金配偶資料檔
					else if (clazz == C101S01D.class) {
					}
				}
				// ========================
				if (checkClass == Check3.class) {
					if (clazz == C101S01E.class) {
						String mainId = Util.trim(params
								.getString(EloanConstants.MAIN_ID));
						String custId = Util.trim(params.getString("custId"));
						String dupNo = Util.trim(params.getString("dupNo"));

						L120S01M l120s01m = cls1131Service.findL120s01m(mainId,
								custId, dupNo);
						if (l120s01m == null) {
							sb.append(
									MessageFormat.format(
											getI18nMsg("message.notQuery"),
											getI18nMsg("l120s01m.item26")))
									.append(EloanConstants.HTML_NEWLINE);
						}
					}
				}
				
				//J-113-0341 年輕族群客戶加強關懷提問單相關欄位檢核
				if (clazz == C101M01A.class) {
					String C101S01AForm = Util.trim(params.getString("C101S01AForm"));
					JSONObject C101S01AJson = DataParse.toJSON(C101S01AForm);
					String birthday = Util.trim(C101S01AJson.get("birthday"));
					if(J_113_0341_ON && Util.isNotEmpty(birthday) && "Y".equals(youngCareCheck)){
						//18~22歲(不含)才需檢核年輕族群客戶加強關懷提問單，所有欄位皆必填
						Date dt_birthday = CapDate.parseDate(birthday);
						int age = cls1131Service.getAge(dt_birthday);
						if(18 <= age && age < 22){
							String C101M01AForm = Util.trim(params.getString("C101M01AForm"));
							JSONObject C101M01AJson = DataParse.toJSON(C101M01AForm);
							
							String youngCareResult1 = Util.trim(C101M01AJson.get("youngCareResult1"));
							if(Util.isEmpty(youngCareResult1)){
								sb.append(getI18nMsg("C101M01A.youngCareList"))
								.append("-")
								.append(getI18nMsg("C101M01A.youngCareItem1"))
								.append("[必填欄位]")
								.append(EloanConstants.HTML_NEWLINE);
							}
							
							String youngCareResult2_1 = Util.trim(C101M01AJson.get("youngCareResult2_1"));
							String youngCareResult2_2 = Util.trim(C101M01AJson.get("youngCareResult2_2"));
							if(Util.isEmpty(youngCareResult2_1) || Util.isEmpty(youngCareResult2_2)){
								sb.append(getI18nMsg("C101M01A.youngCareList"))
								.append("-")
								.append(getI18nMsg("C101M01A.youngCareItem2"))
								.append("[必填欄位]")
								.append(EloanConstants.HTML_NEWLINE);
							}
							
							String youngCareResult3_1 = Util.trim(C101M01AJson.get("youngCareResult3_1"));
							String youngCareResult3_2 = Util.trim(C101M01AJson.get("youngCareResult3_2"));
							if(Util.isEmpty(youngCareResult3_1) || Util.isEmpty(youngCareResult3_2)){
								sb.append(getI18nMsg("C101M01A.youngCareList"))
								.append("-")
								.append(getI18nMsg("C101M01A.youngCareItem3"))
								.append("[必填欄位]")
								.append(EloanConstants.HTML_NEWLINE);
							}
							
							String youngCareResult4_1 = Util.trim(C101M01AJson.get("youngCareResult4_1"));
							String youngCareResult4_2 = Util.trim(C101M01AJson.get("youngCareResult4_2"));
							if(Util.isEmpty(youngCareResult4_1) || Util.isEmpty(youngCareResult4_2)){
								sb.append(getI18nMsg("C101M01A.youngCareList"))
								.append("-")
								.append(getI18nMsg("C101M01A.youngCareItem4"))
								.append("[必填欄位]")
								.append(EloanConstants.HTML_NEWLINE);
							}
							
							String youngCareResult5 = Util.trim(C101M01AJson.get("youngCareResult5"));
							if(Util.isEmpty(youngCareResult5)){
								sb.append(getI18nMsg("C101M01A.youngCareList"))
								.append("-")
								.append(getI18nMsg("C101M01A.youngCareItem5"))
								.append("[必填欄位]")
								.append(EloanConstants.HTML_NEWLINE);
							}
							
							String youngCareMemo = Util.trim(C101M01AJson.get("youngCareMemo"));
							if(Util.isEmpty(youngCareMemo)){
								sb.append(getI18nMsg("C101M01A.youngCareList"))
								.append("-")
								.append(getI18nMsg("C101M01A.youngCareMemo"))
								.append("[必填欄位]")
								.append(EloanConstants.HTML_NEWLINE);
							}
						}
					}
				}
				
			}
		}
		logger.trace("getCheckMessage(?, ?, "+(checkClass!=null?checkClass.getSimpleName()+".class":"")+", ?) will_return["+sb.toString()+"]");
		return sb.toString();
	}

	/**
	 * 檢核家庭所得收入
	 * 
	 * @param params
	 * @return
	 */
	private boolean yFamAmtCheck(PageParameters params) throws CapException {
		BigDecimal sum = new BigDecimal(UtilConstants.Mark.ZEROISNODATA);
		BranchRate br = lmsService.getBranchRate(MegaSSOSecurityContext
				.getUnitNo());
		if (br != null) {
			// 個金服務單位檔
			JSONObject C101S01BJson = ClsUtil.getJson(params, C101S01B.class);
			String payCurr = Util.trim(C101S01BJson.get("payCurr"));
			String payAmt = Util.trim(C101S01BJson.get("payAmt"));
			sum = sum.add(br.toLocalAmt(payCurr, payAmt));
			String othCurr = Util.trim(C101S01BJson.get("payCurr"));
			String othAmt = Util.trim(C101S01BJson.get("othAmt"));
			sum = sum.add(br.toLocalAmt(othCurr, othAmt));

			// 個金償債能力檔
			JSONObject C101S01CJson = ClsUtil.getJson(params, C101S01C.class);
			String yFamCurr = Util.trim(C101S01CJson.get("yFamCurr"));
			String yFamAmt = Util.trim(C101S01CJson.get("yFamAmt"));
			BigDecimal yFam = br.toLocalAmt(yFamCurr, yFamAmt);

			// 個金配偶資料檔
			JSONObject C101S01DJson = ClsUtil.getJson(params, C101S01D.class);
			String mPayCurr = Util.trim(C101S01DJson.get("mPayCurr"));
			String mPayAmt = Util.trim(C101S01DJson.get("mPayAmt"));
			sum = sum.add(br.toLocalAmt(mPayCurr, mPayAmt));

			// 容許值
			//因為單位是萬元，這樣會變成誤差容許100萬元，這樣不合理，通知柏翰後，拿掉此檢核
			//String currExchange = lmsService.getCurrExchangeRate(br.getMCurr());

			logger.info("合計收入:{}", new Object[] { sum.toString() });
			logger.info("夫妻收入:{}", new Object[] { yFam.toString() });
			logger.info("本位幣幣別:{}", new Object[] { br.getMCurr() });
			//logger.info("容許值:{}", new Object[] { currExchange });
			// a.compareTo(b) 比大小。如果　a=b返回0,a>b返回正數　a<b返回負數
			if (sum.compareTo(yFam) > 0) {
				return false;
			}
		}

		return true;
	}

	private boolean fincomeCheck(PageParameters params) throws CapException {
		if (true) {
			// 個金償債能力檔
			JSONObject C101S01CJson = ClsUtil.getJson(params, C101S01C.class);
			String yFamCurr = Util.trim(C101S01CJson.get("yFamCurr"));
			String yFamAmt = Util.trim(C101S01CJson.get("yFamAmt"));

			String fincomeCurr = Util.trim(C101S01CJson.get("fincomeCurr"));
			String fincome = Util.trim(C101S01CJson.get("fincome"));

			if (Util.equals("", fincome)) {
				// 未輸入
			} else {
				if (Util.equals(yFamCurr, fincomeCurr)) {
					if (CrsUtil.parseBigDecimal(yFamAmt).compareTo(
							CrsUtil.parseBigDecimal(fincome)) > 0) {
						return false;
					}
				} else {
					// 不同 curr
				}
			}
		}

		return true;
	}

	/**
	 * 讀取關聯戶資料表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Query)
	public IResult loadRelation(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		List<GenericBean> list = new ArrayList<GenericBean>();

		C101M01A c101m01a = cls1131Service.findModelByKey(C101M01A.class,
				mainId, custId, dupNo);
		if (c101m01a != null) {
			List<C101S01O> o_list = (List<C101S01O>) cls1131Service
					.findListByRelationKey(C101S01O.class, mainId, custId,
							dupNo);
			for (C101S01O o : o_list) {
				list.add(o);
			}
		} else {
			C120M01A c120m01a = cls1131Service.findModelByKey(C120M01A.class,
					mainId, custId, dupNo);
			if (c120m01a != null) {
				List<C120S01O> o_list = (List<C120S01O>) cls1131Service
						.findListByRelationKey(C120S01O.class, mainId, custId,
								dupNo);
				for (C120S01O o : o_list) {
					list.add(o);
				}
			} else {

			}
		}
		String jsonStr = JsonMapper.toJSON(list);
		result.set("detials", jsonStr);

		for (GenericBean model : list) {
			JSONObject json = DataParse.toJSON(model);
			result.set("queryDate", json.getString("queryDate"));
		}
		return result;
	}// ;

	/**
	 * @param key
	 * @return
	 */
	private String getI18nMsg(String key) {
		String result = null;
		if (prop == null) {
			prop = MessageBundleScriptCreator
					.getComponentResource(CLS1131S01Panel.class);
		}
		if (prop != null) {
			result = prop.getProperty(Util.trim(key));
		}
		return Util.trim(result);
	}

	/**
	 * 判斷借款人區部別
	 * 
	 * @param custId
	 *            客戶統編
	 * @return DBU | OBU
	 */
	private String checkTypCd(String custId) {
		boolean isObuId = LMSUtil.isObuId(custId); // 是否為自然人
		String typCd = "";
		if (isObuId) {
			typCd = TypCdEnum.OBU.getCode();
		} else {
			typCd = TypCdEnum.DBU.getCode();
		}
		return typCd;
	}

	private void setImportFlag(List<GenericBean> list, String chk_E) {
		C101M01A c101m01a = null;
		C101S01E c101s01e = null;
		C101S01G c101s01g = null;
		for (GenericBean m : list) {
			if (m instanceof C101M01A) {
				c101m01a = (C101M01A) m;
			} else if (m instanceof C101S01E) {
				c101s01e = (C101S01E) m;
			} else if (m instanceof C101S01G) {
				c101s01g = (C101S01G) m;
			}
		}

		if (c101m01a != null) {
			if (LMSUtil.check2(c101m01a.getCustId())) {
				// 只在自然人時,才 check C101S01G
				boolean passc101s01g = (c101s01g != null && Util
						.isNotEmpty(c101s01g.getGrdCDate()));

				if (Util.isEmpty(chk_E) && passc101s01g) {
					if (c101s01e == null) {
						logger.trace(c101m01a.getOwnBrId() + "/"
								+ c101m01a.getCustId()
								+ " setImportFlag::c101s01e==null");
					}
					c101m01a.setImportFlag(UtilConstants.DEFAULT.是);
				} else {
					logger.info(c101m01a.getOwnBrId() + ","
							+ c101m01a.getCustId() + ",mainId["
							+ c101m01a.getMainId() + "] setImportFlag::chk_E["
							+ chk_E + "]isNatural["
							+ LMSUtil.check2(c101m01a.getCustId())
							+ "]passc101s01g[" + passc101s01g + "]");
					c101m01a.setImportFlag(UtilConstants.DEFAULT.否);
				}
			} else {
				c101m01a.setImportFlag(UtilConstants.DEFAULT.是);// 評等已可引入
			}
		}
	}

	/**
	 * 查詢信用風險管理遵循主檔
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult queryL120s01m(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult formL120s01m = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		L120S01M l120s01m = cls1131Service.findL120s01m(mainId, custId, dupNo);
		String hasLocalAmt = "";

		String busCode = "";
		boolean hasGrp = false;
		if (l120s01m != null) {
			busCode = l120s01m.getBusCode();
			hasLocalAmt = Util.trim(l120s01m.getHasLocalAmt());

			// G-104-0097-001 Web e-Loan
			// 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
			if (Util.equals(hasLocalAmt, "Y")) {
				formL120s01m.set("hasLocalAmt", hasLocalAmt);
			}

			formL120s01m.add(DataParse.toResult(l120s01m));

			// J-105-0078-001 Web
			// e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
			if (Util.equals(Util.trim(l120s01m.getLocalGroup()), "")) {
				formL120s01m.set("localGroup", "");
				// 舊案沒有90 91 92
				formL120s01m.set("dataNotShow_90", "Y");
				formL120s01m.set("dataNotShow_91", "Y");
				formL120s01m.set("dataNotShow_92", "Y");
			}

			formL120s01m.set("grpYear", NumConverter.delCommaString(Util
					.trim(formL120s01m.get("grpYear"))));
			if (Util.equals(l120s01m.getGrpNo(), "")) {
				formL120s01m.set("grpName", "N.A.");
			} else {
				hasGrp = true;
				// J-104-0240-001 Web e-Loan授信簽報書與額度明細表增加列示借款戶所屬集團企業代號、名稱與註記。
				Map<String, String> grpMap = lmsService
						.getGroupNameWithBadFlag(mainId, custId, dupNo);
				String grpId = Util.trim(grpMap.get("grpId"));
				String grpName = Util.trim(grpMap.get("grpName"));
				if (Util.equals(l120s01m.getGrpNo(), grpId)) {
					formL120s01m.set("grpName", grpName);
				}
			}

			formL120s01m.set(
					"netVal",
					l120s01m.getNetVal() == null ? "N.A." : String
							.valueOf(l120s01m
									.getNetVal()
									.divide(new BigDecimal("1000"), 2,
											BigDecimal.ROUND_HALF_UP)
									.toString()));

			if (Util.equals(hasLocalAmt, "Y")) {
				formL120s01m.set("localNetValCurr",
						l120s01m.getLocalNetValCurr());
				formL120s01m.set(
						"localNetVal",
						l120s01m.getLocalNetVal() == null ? "N.A." : String
								.valueOf(l120s01m
										.getLocalNetVal()
										.divide(new BigDecimal("1000"), 2,
												BigDecimal.ROUND_HALF_UP)
										.toString()));
			}

		}
		List<L120S01N> list = cls1131Service.findL120s01nByCustId(mainId,
				custId, dupNo);

		String custKind = null;
		if (LMSUtil.isBusCode_060000_130300(busCode)) {
			custKind = "CLS";
		} else {
			custKind = "LMS";
		}

		// 信用風險管理遵循主檔list
		Date dfDataDate = null;
		if (!list.isEmpty()) {
			for (L120S01N l120s01n : list) {

				String dataKind = l120s01n.getDataKind();

				if (Util.equals(dataKind, "010")) {
					dfDataDate = l120s01n.getDataDate();
				}

				formL120s01m.set("dataNotShow_" + dataKind, "");

				if (Util.equals(dataKind, "030") || Util.equals(busCode, "031")) {
					if (Util.equals(custKind, "CLS")) {
						formL120s01m.set("dataNotShow_" + dataKind, "Y");
					}
				}

				if (Util.equals(dataKind, "020") || Util.equals(busCode, "021")
						|| Util.equals(dataKind, "080")
						|| Util.equals(busCode, "081")) {
					if (Util.equals(custKind, "LMS")) {
						formL120s01m.set("dataNotShow_" + dataKind, "Y");
					}
				}

				// G-104-0097-001 Web e-Loan
				// 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
				if (Util.equals(dataKind, "090") || Util.equals(busCode, "091")
						|| Util.equals(dataKind, "092")) {
					// Y01 02904陳輝揚副理說泰國沒有集團企業，只有關係企業
					// G-104-0097-001 Web e-Loan
					// 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
					// J-105-0078-001 Web
					// e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
					// formL120s01m.set("dataNotShow_" + dataKind, "Y");
					// if (!hasGrp) {
					//
					// formL120s01m.set("dataNotShow_" + dataKind, "Y");
					//
					// } else {
					// if (Util.notEquals(hasLocalAmt, "Y")) {
					// formL120s01m.set("dataNotShow_" + dataKind, "Y");
					// }
					// }
				}

				formL120s01m
						.set("dataKind_" + dataKind, l120s01n.getDataKind());
				if (l120s01n.getDataDate() != null) {
					formL120s01m.set("dataDate_" + dataKind,
							(Date) l120s01n.getDataDate());
				} else {
					if (Util.equals(hasLocalAmt, "Y")) {
						if (Util.equals(dataKind, "090")) {
							if (dfDataDate != null) {
								formL120s01m.set("dataDate_" + dataKind,
										(Date) dfDataDate);
							} else {
								formL120s01m.set("dataDate_" + dataKind, "");
							}

						} else {
							formL120s01m.set("dataDate_" + dataKind, "");
						}
					} else {
						formL120s01m.set("dataDate_" + dataKind, "");
					}
				}

				// formL120s01m.set(
				// "totalBal_" + dataKind,
				// l120s01n.getTotalBal() == null ? "N.A." : String
				// .valueOf(l120s01n
				// .getTotalBal()
				// .divide(new BigDecimal("1000"), 2,
				// BigDecimal.ROUND_HALF_UP)
				// .toString()));
				//
				// formL120s01m.set("shareOfNet_" + dataKind, l120s01n
				// .getShareOfNet() == null ? "N.A." : l120s01n
				// .getShareOfNet().toString());

				if ((Util.equals(dataKind, "090")
						|| Util.equals(dataKind, "091") || Util.equals(
						dataKind, "092"))
						&& (Util.equals(Util.trim(l120s01m.getLocalGroup()),
								"000000000") || Util.equals(
								Util.trim(l120s01m.getLocalGroup()), ""))) {

					formL120s01m.set("totalBal_" + dataKind, "N.A.");

					formL120s01m.set("localCurrentAdjValT_" + dataKind, "N.A.");

					formL120s01m.set("localCurrentTotal_" + dataKind, "N.A.");

					formL120s01m.set("shareOfNet_" + dataKind, "N.A.");
				} else {
					formL120s01m.set(
							"totalBal_" + dataKind,
							l120s01n.getTotalBal() == null ? "N.A." : String
									.valueOf(l120s01n
											.getTotalBal()
											.divide(new BigDecimal("1000"), 2,
													BigDecimal.ROUND_HALF_UP)
											.toString()));

					formL120s01m.set(
							"localCurrentAdjValT_" + dataKind,
							l120s01n.getLocalCurrentAdjValT() == null ? "N.A."
									: String.valueOf(l120s01n
											.getLocalCurrentAdjValT()
											.divide(new BigDecimal("1000"), 2,
													BigDecimal.ROUND_HALF_UP)
											.toString()));

					formL120s01m.set(
							"localCurrentTotal_" + dataKind,
							l120s01n.getLocalCurrentTotal() == null ? "N.A."
									: String.valueOf(l120s01n
											.getLocalCurrentTotal()
											.divide(new BigDecimal("1000"), 2,
													BigDecimal.ROUND_HALF_UP)
											.toString()));

					formL120s01m.set("shareOfNet_" + dataKind, l120s01n
							.getShareOfNet() == null ? "N.A." : l120s01n
							.getShareOfNet().toString());
				}

				formL120s01m.set("lawLimit_" + dataKind,
						l120s01n.getLawLimit() == null ? "N.A." : l120s01n
								.getLawLimit().toString());
			}
		}

		// J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
		Properties pop02 = MessageBundleScriptCreator
				.getComponentResource(CLS1131S01Panel.class);

		// J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
		if (hasGrp) {
			// 集團企業
			formL120s01m.set("showGroupKind",
					pop02.getProperty("l120s01m.item37"));

			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			boolean newGrpGrade = lmsService.isNewGrpGrade(
					Util.trim(l120s01m.getGrpYear()), true);
			String defultNoGrade = lmsService.getGrpNoGrade(newGrpGrade);

			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			if (newGrpGrade) {
				// (大A)、(中B)....
				formL120s01m.set(
						"grpSizeLvlShow",
						lmsService.getGrpSizeLvlShow(
								Util.trim(l120s01m.getGrpSize()),
								Util.trim(l120s01m.getGrpLevel())));
			} else {
				formL120s01m.set("grpSizeLvlShow", "");
			}
		} else {
			// 同一關係企業/集團企業<br>(因無集團代號，故其限額比照本辦法集團評等第六級未評等之限額)
			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			String grpYear = Util.equals(Util.trim(l120s01m.getGrpYear()), "") ? Util
					.trim(l120s01m.getGrpLastYear()) : Util.trim(l120s01m
					.getGrpYear());

			boolean newGrpGrade = lmsService.isNewGrpGrade(grpYear, true);
			String defultNoGrade = lmsService.getGrpNoGrade(newGrpGrade);
			if (newGrpGrade) {
				// l120s01m.item71_2017=同一關係企業/集團企業<br>(因無集團代號，故其限額比照本辦法集團評等第0級未評等之限額)
				formL120s01m.set("showGroupKind",
						pop02.getProperty("l120s01m.item71_2017"));
			} else {
				// l120s01m.item71=同一關係企業/集團企業<br>(因無集團代號，故其限額比照本辦法集團評等第六級未評等之限額)
				formL120s01m.set("showGroupKind",
						pop02.getProperty("l120s01m.item71"));
			}

		}

		result.set("formL120s01m", formL120s01m);
		return result;
	}

	/**
	 * 查詢信用風險管理遵循明細
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult queryL120s01o(PageParameters params) throws CapException {
		String relType = Util.trim(params.getString("relType"));
		if (Util.equals(relType, "040") || Util.equals(relType, "050")) {
			return queryL120s01oDw(params);
		} else {
			return queryL120s01o_33_3(params);
		}
	}

	/**
	 * 查詢信用風險管理遵明細 信用風險集中
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	private IResult queryL120s01oDw(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS1131S01Panel.class);

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String relType = Util.trim(params.getString("relType"));

		List<L120S01O> list = cls1131Service.findL120s01oByCustIdRelType(
				mainId, custId, dupNo, relType);

		// formL120s01m.set("dataNotShow_" , "");
		// 信用風險管理遵循明細list
		StringBuffer html = new StringBuffer();
		html.append("<table class=\"tb2\" width=\"98%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">");
		html.append("<tr style=\"border: 0px hidden;border-width:0px;\">");
		html.append("<td colspan=\"8\" style=\"border : 0px;\" align=\"left\" >"
				+ pop.getProperty("l120s01m.item58") + "</td>"); // (單位:TWD 元)
		html.append("</tr>");
		html.append("<tr>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item42")); // 統編
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item43")); // 戶名
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item36")); // 列入限額控管之金額
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item45")); // 授信餘額
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item46")); // 長投餘額
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item47")); // 存放同業
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item48")); // 拆放同業
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item49")); // 有價証券
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item50")); // 應收款
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item51")); // 衍生性金融商品
		html.append("</td>");

		html.append("</tr>");
		StringBuffer nCustNm = null;
		DecimalFormat df = new DecimalFormat(
				"###,###,###,###,###,###,###,##0.##");

		if (!list.isEmpty()) {
			for (L120S01O l120s01o : list) {

				html.append("<tr>");

				nCustNm = new StringBuffer(l120s01o.getRCustId());
				html.append("<td >");
				html.append(nCustNm.append(l120s01o.getRDupNo()));
				html.append("</td>");

				html.append("<td >");
				html.append(Util.trim(l120s01o.getRCustName()));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util.parseBigDecimal(
						l120s01o.getCostLncAmt()).setScale(2,
						BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util
						.parseBigDecimal(l120s01o.getTotAmt()).setScale(2,
								BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util
						.parseBigDecimal(l120s01o.getCrdAmt()).setScale(2,
								BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util
						.parseBigDecimal(l120s01o.getLntAmt()).setScale(2,
								BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util
						.parseBigDecimal(l120s01o.getLnsAmt()).setScale(2,
								BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util
						.parseBigDecimal(l120s01o.getLncAmt()).setScale(2,
								BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util.parseBigDecimal(
						l120s01o.getCostLntAmt()).setScale(2,
						BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util.parseBigDecimal(
						l120s01o.getCostLnsAmt()).setScale(2,
						BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("</tr>");
			}
		}

		html.append("</table>");
		result.set("showDetailResult", html.toString());

		// result.set("formL120s01m", formL120s01m);
		return result;
	}

	/**
	 * 查詢信用風險管理遵循明細 銀行法33條之3
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	private IResult queryL120s01o_33_3(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS1131S01Panel.class);

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String relType = Util.trim(params.getString("relType"));

		List<L120S01O> list = cls1131Service.findL120s01oByCustIdRelType(
				mainId, custId, dupNo, relType);

		// formL120s01m.set("dataNotShow_" , "");
		// 信用風險管理遵循明細list
		StringBuffer html = new StringBuffer();
		html.append("<table class=\"tb2\" width=\"98%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">");
		html.append("<tr style=\"border: 0px hidden;border-width:0px;\">");
		html.append("<td colspan=\"8\" style=\"border : 0px;\" align=\"left\" >"
				+ pop.getProperty("l120s01m.item58") + "</td>"); // (單位:TWD 元)
		html.append("</tr>");
		html.append("<tr>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item42")); // 統編
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item43")); // 戶名
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item52")); // 不計入同一關係企業授信總餘額(國內)
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item53")); // 不計入同一關係企業授信總餘額(海外)
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item54")); // 不計入同一關係企業【有擔保】授信總餘額(國內)
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item55")); // 不計入同一關係企業【有擔保】授信總餘額(海外)
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item56")); // 不計入同一關係企業【無擔保】授信總餘額(國內)
		html.append("</td>");

		html.append("<td class=\"hd2\">");
		html.append(pop.getProperty("l120s01m.item57")); // 不計入同一關係企業【無擔保】授信總餘額(海外)
		html.append("</td>");

		html.append("</tr>");
		StringBuffer nCustNm = null;
		DecimalFormat df = new DecimalFormat(
				"###,###,###,###,###,###,###,##0.##");

		if (!list.isEmpty()) {
			for (L120S01O l120s01o : list) {

				html.append("<tr>");

				nCustNm = new StringBuffer(l120s01o.getRCustId());
				html.append("<td >");
				html.append(nCustNm.append(l120s01o.getRDupNo()));
				html.append("</td>");

				html.append("<td >");
				html.append(Util.trim(l120s01o.getRCustName()));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util.parseBigDecimal(
						l120s01o.getCostLntAmtA()).setScale(2,
						BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util.parseBigDecimal(
						l120s01o.getCostLntAmtB()).setScale(2,
						BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util.parseBigDecimal(
						l120s01o.getCostLnsAmtA()).setScale(2,
						BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util.parseBigDecimal(
						l120s01o.getCostLnsAmtB()).setScale(2,
						BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util.parseBigDecimal(
						l120s01o.getCostLncAmtA()).setScale(2,
						BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("<td align=\"right\"  >");
				html.append(df.format(Util.parseBigDecimal(
						l120s01o.getCostLncAmtB()).setScale(2,
						BigDecimal.ROUND_HALF_UP)));
				html.append("</td>");

				html.append("</tr>");
			}
		}

		html.append("</table>");
		result.set("showDetailResult", html.toString());

		// result.set("formL120s01m", formL120s01m);
		return result;
	}

	public IResult chgCustName(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));

		C120M01A c120m01a = cls1131Service.findModelByKey(C120M01A.class,
				mainId, custId, dupNo);
		if (c120m01a != null) {
			if (Util.notEquals(
					Util.truncateString(custName, MAXLEN_C120M01A_CUSTNAME),
					custName)) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", "【" + custName + "】");
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
			}

			c120m01a.setCustName(custName);
			cls1131Service.saveC120M01A(c120m01a);
		}

		return result;
	}

	public IResult isChgC122M01A_applyStatus(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String c122m01a_mainId = Util.trim(params.getString("c122m01a_mainId"));

		C122M01A c122m01a = cls1220Service
				.getC122M01A_byMainId(c122m01a_mainId);
		String c122_applyStatus = "";
		if (c122m01a == null) {
			throw new CapMessageException("查無資料:" + c122m01a_mainId, getClass());
		}
		if (Util.equals(c122m01a.getApplyStatus(),
				UtilConstants.C122_ApplyStatus.受理中)
				|| Util.equals(c122m01a.getApplyStatus(),
						UtilConstants.C122_ApplyStatus.審核中)) {
			c122_applyStatus = c122m01a.getApplyStatus();
		} else {
			throw new CapMessageException("狀態已變更:" + c122m01a_mainId,
					getClass());
		}
		if (Util.isNotEmpty(c122_applyStatus)) {
			result.set("c122_applyStatus", c122_applyStatus);
			result.set("c122_docStatus", Util.trim(c122m01a.getDocStatus()));
		}
		return result;
	}

	public IResult fetchC122M01A_to_C101(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// =======================
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String c122m01a_mainId = Util.trim(params.getString("c122m01a_mainId"));
		C122M01A c122m01a = cls1220Service
				.getC122M01A_byMainId(c122m01a_mainId);
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		if (c122m01a == null) {
			throw new CapMessageException("查無資料:" + c122m01a_mainId, getClass());
		}
		if (Util.equals(c122m01a.getApplyStatus(),
				UtilConstants.C122_ApplyStatus.受理中)
				|| Util.equals(c122m01a.getApplyStatus(),
						UtilConstants.C122_ApplyStatus.審核中)) {
			String empNo = Util.trim(user.getUserId());
			cls1131Service.c122m01aStatusTo0B0(c122m01a.getOwnBrId(),
					new String[] { c122m01a.getApplyKind() },
					c122m01a.getCustId(), c122m01a.getDupNo(), empNo);
			// "C101M01AForm", "C101S01AForm", "C101S01BForm", "C101S01CForm"
			if (true) {
				CapAjaxFormResult formResult = new CapAjaxFormResult();
				C120S01A c122_s01a = cls1220Service.findC120S01A(
						c122m01a.getMainId(), c122m01a.getCustId(),
						c122m01a.getDupNo());
				LMSUtil.addMetaToResult(formResult, c122_s01a,
						ClsUtil.C122_C120S01A_COLS);
				if (true) {
					String[] targetArr = cls1131Service.parseAddr(c122_s01a
							.getFTarget());
					formResult.set("fCity", targetArr[0]);
					formResult.set("fZip", targetArr[1]);
					formResult.set("fAddr", targetArr[2]);
				}
				if (true) {
					String coCity = "";
					String coZip = "";
					String coAddr = "";
					if (Util.isNotEmpty(Util.trim(c122_s01a.getCoCity()))
							&& Util.isNotEmpty(Util.trim(c122_s01a.getCoZip()))) {
						coCity = c122_s01a.getCoCity();
						coZip = c122_s01a.getCoZip();
						coAddr = c122_s01a.getCoAddr();
					} else {
						String[] targetArr = cls1131Service.parseAddr(c122_s01a
								.getCoTarget());
						coCity = targetArr[0];
						coZip = targetArr[1];
						coAddr = targetArr[2];
					}
					formResult.set("coCity", coCity);
					formResult.set("coZip", coZip);
					formResult.set("coAddr", coAddr);
				}
				formResult.set("marry",
						ClsUtil.convert_nb_to_eloan_marry(c122_s01a));
				if (true) { // 若 child=0 回傳前端 "數字0", 不會更新 ui 的數值 回傳前端 "文字0", 才會更新 ui 的數值
					formResult
							.set("child", String.valueOf(Util
									.parseInt(c122_s01a.getChild())));
				}
				result.set("C101S01AForm", formResult);
			}
			if (true) {
				CapAjaxFormResult formResult = new CapAjaxFormResult();
				C120S01B c122_s01b = cls1220Service.findC120S01B(
						c122m01a.getMainId(), c122m01a.getCustId(),
						c122m01a.getDupNo());
				LMSUtil.addMetaToResult(formResult, c122_s01b,
						ClsUtil.C122_C120S01B_COLS);
				inject_snrY_snrM_when_fetchC122M01A_to_C101(formResult, c122_s01b);
				if (true) {
					String comCity = "";
					String comZip = "";
					String comAddr = "";
					if (Util.isNotEmpty(Util.trim(c122_s01b.getComCity()))
							&& Util.isNotEmpty(Util.trim(c122_s01b.getComZip()))) {
						comCity = c122_s01b.getComCity();
						comZip = c122_s01b.getComZip();
						comAddr = c122_s01b.getComAddr();
					} else {
						String[] targetArr = cls1131Service.parseAddr(c122_s01b
								.getComTarget());
						comCity = targetArr[0];
						comZip = targetArr[1];
						comAddr = targetArr[2];
					}
					formResult.set("comCity", comCity);
					formResult.set("comZip", comZip);
					formResult.set("comAddr", comAddr);
				}
				result.set("C101S01BForm", formResult);
			}
			if (true) {
				CapAjaxFormResult formResult = new CapAjaxFormResult();
				C120S01C c122_s01c = cls1220Service.findC120S01C(
						c122m01a.getMainId(), c122m01a.getCustId(),
						c122m01a.getDupNo());
				LMSUtil.addMetaToResult(formResult, c122_s01c,
						ClsUtil.C122_C120S01C_COLS);
				result.set("C101S01CForm", formResult);
			}
		} else {
			throw new CapMessageException("狀態已變更:" + c122m01a_mainId,
					getClass());
		}

		return result;
	}

	public IResult fetchC122M01A_to_C101_Prod69(PageParameters params) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// =======================
		CapAjaxFormResult result = new CapAjaxFormResult();
		String c122m01a_mainId = Util.trim(params.getString("c122m01a_mainId"));
		C122M01A c122m01a = cls1220Service
				.getC122M01A_byMainId(c122m01a_mainId);
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		if (c122m01a == null) {
			throw new CapMessageException("查無資料:" + c122m01a_mainId, getClass());
		}

		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		
		if (true) {
			if (true) {
				CapAjaxFormResult formResult = new CapAjaxFormResult();
				formResult.set("isBailout4", "Y");
				result.set("C101M01AForm", formResult);
			}
			if (true) {
				CapAjaxFormResult formResult = new CapAjaxFormResult();
				C120S01A c122_s01a = cls1220Service.findC120S01A(
						c122m01a.getMainId(), c122m01a.getCustId(),
						c122m01a.getDupNo());
				if (c122_s01a != null) {
					LMSUtil.addMetaToResult(formResult, c122_s01a,
							ClsUtil.C122_C120S01A_COLS_Prod69);
					if(true){
						String ntCode = Util.trim(c122_s01a.getNtCode());
						if(Util.isNotEmpty(ntCode)){
							formResult.set("ntCode", ntCode);	
						}							
					}
					
					formResult.set("marry",
							ClsUtil.convert_nb_to_eloan_marry(c122_s01a));
					if (true) { // 若 child=0 回傳前端 "數字0", 不會更新 ui 的數值 回傳前端 "文字0", 才會更新 ui 的數值
						formResult.set("child", String.valueOf(Util
								.parseInt(c122_s01a.getChild())));
					}
					if (true) {
						if (c122_s01a.getIdCardIssueDate() != null) {
							formResult.set("idCardIssueDate", TWNDate
									.toAD(c122_s01a.getIdCardIssueDate()));
						}
						if (Util.isNotEmpty(Util.trim(c122_s01a
								.getIdCard_siteId()))) {
							formResult.set("idCard_siteId",
									c122_s01a.getIdCard_siteId());
						}
						if (Util.isNotEmpty(Util.trim(c122_s01a
								.getIdCardChgFlag()))) {
							formResult.set("idCardChgFlag",
									c122_s01a.getIdCardChgFlag());
						}
						if (Util.isNotEmpty(Util.trim(c122_s01a
								.getIdCardPhoto()))) {
							formResult.set("idCardPhoto",
									c122_s01a.getIdCardPhoto());
						}
					}
				}
				result.set("C101S01AForm", formResult);
			}
			if (true) {
				CapAjaxFormResult formResult = new CapAjaxFormResult();
				C120S01B c122_s01b = cls1220Service.findC120S01B(
						c122m01a.getMainId(), c122m01a.getCustId(),
						c122m01a.getDupNo());
				if (c122_s01b != null) {
					LMSUtil.addMetaToResult(formResult, c122_s01b,
							ClsUtil.C122_C120S01B_COLS_Prod69);
					inject_snrY_snrM_when_fetchC122M01A_to_C101(formResult, c122_s01b);
					if(c101m01a!=null){
						C101S01B c101s01b = clsService.findC101S01B(c101m01a);
						if(c101s01b!=null){
							resetPersonalIncomeDetail(c101s01b);
							//=============
							c101s01b.setOthType("");//清空舊的 {複選項目}
							//=============
							c101s01b.setPositionType("1"); // {1:非藍領}
							c101s01b.setMainIncomeType("A");
							c101s01b.setItemAvalue(c122_s01b.getPayAmt().multiply(new BigDecimal(10000)));
							cls1131Service.calPersonalIncomeDetail(c101s01b);
							//=============
							clsService.daoSave(c101s01b);
							//=============
							/*
							   	● itemAvalue 會帶入 前端的金額
							  	● resetPersonalIncomeDetail(c101s01b) 會把 othAmt 清空
							  	===> 經過 calPersonalIncomeDetail(c101s01b) 之後，payAmt 與 othAmt 都被異動
							 */
							LMSUtil.addMetaToResult(formResult, c101s01b, new String[] {"payAmt", "othType", "othAmt"});
							// p.s. 不要在這裡增加 jobType1, jobType2 => 因為 塞入 jobType1 之後，會 reload jobType2  的下拉選單 => 在 loadCust(...) 裡去做 
						}
					}
					
					if (true) {
						//勞工紓困 前端頁面，有輸入 職稱 的欄位
						formResult.set("jobTitle", Util.trim(c122_s01b.getJobTitle()));	
						//============
						String comCity = "";
						String comZip = "";
						String comAddr = "";
						if (Util.isNotEmpty(Util.trim(c122_s01b.getComCity()))
								&& Util.isNotEmpty(Util.trim(c122_s01b
										.getComZip()))) {
							comCity = c122_s01b.getComCity();
							comZip = c122_s01b.getComZip();
							comAddr = c122_s01b.getComAddr();
						} else {
							String[] targetArr = cls1131Service
									.parseAddr(c122_s01b.getComTarget());
							comCity = targetArr[0];
							comZip = targetArr[1];
							comAddr = targetArr[2];
						}
						formResult.set("comCity", comCity);
						formResult.set("comZip", comZip);
						formResult.set("comAddr", comAddr);
					}
				}
				result.set("C101S01BForm", formResult);
			}
		
			if(true){
				CapAjaxFormResult formResult = new CapAjaxFormResult();
				C120S01B c122_s01b = cls1220Service.findC120S01B(
						c122m01a.getMainId(), c122m01a.getCustId(),
						c122m01a.getDupNo());
				if (c122_s01b != null) {
					String str_payAmt = LMSUtil.pretty_numStr(c122_s01b.getPayAmt());
					formResult.set("yFamAmt", str_payAmt);
					formResult.set("fincome", str_payAmt);					
				}
				formResult.set("dRate", "");
				formResult.set("yRate", "");
				if(c101m01a!=null){
					C101S01C c101s01c = clsService.findC101S01C(c101m01a);
					if(c101s01c!=null){
						JSONObject rateData = new JSONObject();
						rateData.put("mode_1", "1"); // {1:期付金 , 2:按月計息 }
						rateData.put("loan_1", c122m01a.getApplyAmt().multiply(new BigDecimal(10))); //單位:仟元
						rateData.put("period_1", "36"); //期間: 36期
						rateData.put("extPeriod_1", "6"); //寬限期: 6期
						rateData.put("rate_1", "1.84"); //應為 P7+1%, 目前為 1.845%, 但前端UI只有小數點後2位
						rateData.put("yFamAmtR", c122_s01b.getPayAmt().multiply(new BigDecimal(10000))); //夫妻年收入 (當成 客戶單身, 個人年收人=夫妻年收入)
						rateData.put("yPeriod", "0"); //配偶貸款期付金 
						rateData.put("yCycle", "0"); //配偶信用卡循環
						rateData.put("yPeriodUnpay", "0"); //配偶分期未償還金額 
						c101s01c.setRateData(rateData.toString());
						//=============
						/* if(true){
							c101s01c.setReCalFlg("N"); //比照 cls1131Service.calPersonalIncomeDetail( ? ) 的寫法，標註S01B與 S01C 的收入不同，需要再按「計算」
						}*/
						if(true){
							c101s01c.setReCalFlg("Y"); //比照  saveC120S01CrateData(PageParameters params, Component parent)
						}
						//=============
						clsService.daoSave(c101s01c);
						//=============						
					}
				}
				result.set("C101S01CForm", formResult);
			}
			if(true){
				CapAjaxFormResult formResult = new CapAjaxFormResult();
				if(true){
					formResult.set("caseSrcFlag", "P"); //進件來源{L:經地政士進件, P:個人送件,O:其它} 
					formResult.set("laaName", "");
					formResult.set("laaYear", "");
					formResult.set("laaWord", "");
					formResult.set("laaNo", "");
					formResult.set("laaOfficeId", "");
					formResult.set("laaOffice", "");
					formResult.set("laaDesc", ""); //理由說明
					//--------------------
					formResult.set("agentPId", "");
					formResult.set("agentPName", "");
				}
				result.set("C101S01EForm", formResult);
			}
			if(true){
				CapAjaxFormResult formResult = new CapAjaxFormResult();
				if(true){
					formResult.set("applyQuota", Util.trim(LMSUtil.pretty_numStr(c122m01a.getApplyAmt()))); 
					formResult.set("applyDate", Util.trim(TWNDate.toAD(c122m01a.getApplyTS())));
					
					List<C122M01A> c122m01aList = this.cls1141Service.findC122M01A_by_brNo_custId_applyKind_orderByApplyTSDesc(c122m01a.getOwnBrId(), c122m01a.getCustId(), 
																								UtilConstants.C122_ApplyKind.B, ClsUtility.get_labor_bailout_2021_since_ts());
					C122M01A c122m01aTmp = null;
					if(c122m01aList != null && !c122m01aList.isEmpty()){
						c122m01aTmp = c122m01aList.get(0);
					}
					
					C122M01B c122m01b = cls1220Service.getC122M01B_byMainIdItemType(c122m01aTmp.getMainId(), "0");
					String isOtherAssets = "N";
					if(c122m01b!= null && Util.isNotEmpty(c122m01b.getJsonData())){
						JSONObject jsonObj = JSONObject.fromObject(c122m01b.getJsonData());
						String noLabInsrMonth = Util.trim(jsonObj.optString("noLabInsrMonth"));
						if(StringUtils.isNotEmpty(noLabInsrMonth) && Integer.parseInt(noLabInsrMonth) > 0){
							isOtherAssets = "Y";
						}
					}
					
					String applyTs = this.sysparamService.getParamValue("APPLY_DATE_LABOUR_BAILOUT4");
					C101S01X c101s01x = this.cls1131Service.getC101S01XObject(mainId, c101m01a.getCustId(), c101m01a.getDupNo());
					c101s01x.setIsOtherAssets(isOtherAssets);
					c101s01x.setIp(c122m01aTmp.getAgreeQueryEJIp());
					c101s01x.setTel(c122m01aTmp.getAgreeQueryEJMtel());
					c101s01x.setIpCount(this.cls1131Service.findC122m01aAgreeQueryEJIpCount(c122m01aTmp.getAgreeQueryEJIp(), applyTs));
					c101s01x.setTelCount(this.cls1131Service.findC122m01aAgreeQueryEJMtelCount(c122m01aTmp.getAgreeQueryEJMtel(), applyTs));
					this.cls1131Service.saveC101S01X(c101s01x);
				}
				result.set("C101S01XForm", formResult);
			}
		}

		return result;
	}

	public IResult fetchC122M01A_to_C101_PLOAN(PageParameters params) throws CapException { // 在 c122m01a_mainId_applyKind_PLOAN 已判斷｛生日｝需相同
		//********************
		// select * from lms.c122m01a where applykind in ('P','Q','E','F') and custid=? 
		//********************
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// =======================
		CapAjaxFormResult result = new CapAjaxFormResult();
		String c122m01a_mainId = Util.trim(params.getString("c122m01a_mainId"));
		C122M01A c122m01a = cls1220Service
				.getC122M01A_byMainId(c122m01a_mainId);
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		if (c122m01a == null) {
			throw new CapMessageException("查無資料:" + c122m01a_mainId, getClass());
		}
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		
		if (true) { 
			String itemType = "0";
			C122M01B c122m01b = cls1220Service.getC122M01B_byMainIdItemType(c122m01a_mainId, itemType);
			//~~~~~~~~~~~
			String ploan_basicInfo_titleType = "";
			try{
				if(c122m01b!=null){
					if(Util.equals(UtilConstants.C122_ApplyKind.P, c122m01a.getApplyKind())
							|| Util.equals(UtilConstants.C122_ApplyKind.Q, c122m01a.getApplyKind())){
						if(Util.equals("PLOAN001", c122m01b.getJsonVoClass())){
							ObjectMapper objectMapper = new ObjectMapper();
							PLOAN001 ploan_obj = objectMapper.readValue(JSONObject.fromObject(c122m01b.getJsonData()).toString(), PLOAN001.class);
							////////////
							ploan_basicInfo_titleType = ploan_obj.getBasicInfo().getTitleType();
						}
					}else if(Util.equals(UtilConstants.C122_ApplyKind.E, c122m01a.getApplyKind())
							|| Util.equals(UtilConstants.C122_ApplyKind.F, c122m01a.getApplyKind())){
						if(Util.equals("PLOAN002", c122m01b.getJsonVoClass())){
							ObjectMapper objectMapper = new ObjectMapper();
							PLOAN002 ploan_obj = objectMapper.readValue(JSONObject.fromObject(c122m01b.getJsonData()).toString(), PLOAN002.class);
							////////////
							ploan_basicInfo_titleType = ploan_obj.getBasicInfo().getTitleType();
						}
					}
				}
				
			}catch (Exception e) {			
				throw new CapException("[mainId="+mainId+"][c122m01a_mainId="+c122m01a_mainId+"]轉換資料發生錯誤", getClass());
			}
			//~~~~~~~~~~~
			if (true) {
				CapAjaxFormResult formResult = new CapAjaxFormResult();
				C120S01A c122_s01a = cls1220Service.findC120S01A(
						c122m01a.getMainId(), c122m01a.getCustId(),
						c122m01a.getDupNo());
				if (c122_s01a != null) {
					LMSUtil.addMetaToResult(formResult, c122_s01a, new String[]{"birthday", "edu"
							, "fCity", "fZip", "fAddr", "fTarget"   
							, "coCity", "coZip", "coAddr", "coTarget"  
							, "houseStatus"
							, "mTel"
							, "email"}); //線上貸款的{住宅電話}可能是 租屋處 電話，不應寫入 0024 的戶籍電話   

					formResult.set("marry", ClsUtil.convert_nb_to_eloan_marry(c122_s01a));
					if (true) { // 若 child=0 回傳前端 "數字0", 不會更新 ui 的數值 回傳前端 "文字0", 才會更新 ui 的數值
						formResult.set("child", String.valueOf(Util.parseInt(c122_s01a.getChild())));
					}
				}				
				result.set("C101S01AForm", formResult);
			}
			
			if (true) {
				CapAjaxFormResult formResult = new CapAjaxFormResult();
				C120S01B c122_s01b = cls1220Service.findC120S01B(
						c122m01a.getMainId(), c122m01a.getCustId(),
						c122m01a.getDupNo());
				if (c122_s01b != null) {
					LMSUtil.addMetaToResult(formResult, c122_s01b, new String[]{"jobType1", "jobType2"
							, "comName"
							, "seniority"											
							, "juId"
							, "comTel"
							, "comCity", "comZip", "comAddr", "comTarget"
							, "payAmt"});					
					inject_snrY_snrM_when_fetchC122M01A_to_C101(formResult, c122_s01b);					
					if(c101m01a!=null){
						C101S01B c101s01b = clsService.findC101S01B(c101m01a);
						if(c101s01b!=null){
							resetPersonalIncomeDetail(c101s01b);
							//=============
							c101s01b.setOthType("");//清空舊的 {複選項目}
							//=============
							c101s01b.setPositionType("1"); // {1:非藍領}
							c101s01b.setMainIncomeType("A");
							c101s01b.setItemAvalue(c122_s01b.getPayAmt().multiply(new BigDecimal(10000)));
							cls1131Service.calPersonalIncomeDetail(c101s01b);
							//=============
							clsService.daoSave(c101s01b);
							//=============
							/*
							   	● itemAvalue 會帶入 前端的金額
							  	● resetPersonalIncomeDetail(c101s01b) 會把 othAmt 清空
							  	===> 經過 calPersonalIncomeDetail(c101s01b) 之後，payAmt 與 othAmt 都被異動
							 */
							LMSUtil.addMetaToResult(formResult, c101s01b, new String[] {"payAmt", "othType", "othAmt"});
							// p.s. 不要在這裡增加 jobType1, jobType2 => 因為 塞入 jobType1 之後，會 reload jobType2  的下拉選單 => 在 loadCust(...) 裡去做 
						}
					}
					
					if(Util.equals("06", ploan_basicInfo_titleType)){ //06 一般職員, where codetype='ploan_basicInfo_titleType'
						formResult.set("jobTitle", "d");
					}else{
						//0024職稱，無法對應到 e-Loan 的職稱
					}
				}
				result.set("C101S01BForm", formResult);
			}
			
			if (Util.equals(c122m01a.getApplyStatus(),UtilConstants.C122_ApplyStatus.受理中)){
				String empNo = ""; //Util.trim(user.getUserId());
				cls1131Service.c122m01aStatusTo0B0(c122m01a.getOwnBrId(),
						new String[] { c122m01a.getApplyKind() },
						c122m01a.getCustId(), c122m01a.getDupNo(), empNo);
			}
		}

		return result;
	}

	public IResult impCustName(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C101M01A c101m01a = cls1131Service.findModelByKey(C101M01A.class,
				mainId, custId, dupNo);
		String custName = "";
		if (c101m01a != null) {
			custName = Util.trim(c101m01a.getCustName());
			// ~~~
			Map<String, Object> latestData = iCustomerService.findByIdDupNo(
					custId, dupNo);
			if (MapUtils.isNotEmpty(latestData)) {
				String newCustName = Util.trim(latestData.get("CNAME"));

				if (Util.isNotEmpty(newCustName)
						&& Util.notEquals(custName, newCustName)) {
					// ~~~
					custName = newCustName;
					// ~~~
					c101m01a.setCustName(custName);
					cls1131Service.save(c101m01a);
				}
			}
		}
		result.set("custName", custName);
		return result;
	}

	@DomainAuth(AuthType.Query)
	public IResult getAbnormalDocParam(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		result.set("findDoc", "N");
		C101M01A model = cls1131Service.findModelByKey(C101M01A.class, mainId,
				custId, dupNo);
		if (model != null) {
			L120M01A l120m01a = clsService.findL120M01A_mainId(model
					.getAbnormalMainId());
			if (l120m01a != null) {
				result.set("findDoc", "Y");
				result.set("open_docURL", Util.trim(l120m01a.getDocURL()));
				if (true) {
					cls1131Service.injectAbnormalDocParam(result, l120m01a,
							"url_data");
				}
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult decide_cls1131_amlflag(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Boolean isC120M01A = params.getBoolean("isC120M01A");
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		boolean active_SAS_AML = false;
		L120S09B l120s09b = null;
		if (isC120M01A) {
			L120M01A l120m01a = clsService.findL120M01A_mainId(mainId);
			active_SAS_AML = clsService.active_SAS_AML(l120m01a);
		} else {
			C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
			active_SAS_AML = clsService.active_SAS_AML(c101m01a);
			if (c101m01a != null) {
				C101S01J c101s01j = clsService.findC101S01J(c101m01a);
				if (c101s01j != null) {
					String amlRefNo = Util.trim(c101s01j.getAmlRefNo());
					String amlRefOid = Util.trim(c101s01j.getAmlRefOid());
					l120s09b = clsService.findL120S09B_refNo_or_oid(amlRefNo,
							amlRefOid);
				}

			}
		}

		result.set("mainId", mainId);
		result.set("idDup", custId + "-" + dupNo);
		result.set("isC120M01A", isC120M01A);
		if (active_SAS_AML) {
			boolean bool = true;
			if (isC120M01A) {
				bool = true;// 在簽報書去open CL1131借款人明細時,是唯讀狀態 => 直接當成是 lock 狀態
			} else {
				bool = clsService.is_aml_lockEdit_cls1131(l120s09b);
			}
			result.set("flag", bool ? "2" : "1"); // 參考 CLS1201S20Panel 的值
		} else {
			result.set("flag", "0");
		}

		if (Util.isNotEmpty(custId)) {
			Map<String, Object> map = iCustomerService.findByIdDupNo(custId,
					dupNo);
			String eName0024 = Util.toSemiCharString(Util.trim(MapUtils
					.getString(map, "ENAME", "")));
			result.set("eName0024", eName0024);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult send_cls1131_AmlList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String clientIP = httpServletRequest.getRemoteHost();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		String ownBrId = Util.trim(c101m01a.getOwnBrId());
		String custId = Util.trim(c101m01a.getCustId());
		String dupNo = Util.trim(c101m01a.getDupNo());

		String custName = Util.trim(params.getString("custName"));
		if (c101m01a != null) {
			custName = Util.trim(c101m01a.getCustName());
		}
		String eName = Util.trim(params.getString("eName"));
		String birthday = Util.trim(params.getString("birthday"));
		String sex = Util.trim(params.getString("sex"));
		String ntCode = Util.trim(params.getString("ntCode"));
		String busCode = clsService.get0024_busCode(custId, dupNo);
		// ===============================================================
		if (Util.notEquals(eName, Util.truncateString(eName, StrUtils
				.getEntityFileldLegth(C101S01E.class, "eName",
						MAXLEN_C101S01E_ENAME)), false)) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", "eName【" + eName + "】");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
		}
		if (true) {
			boolean notEmpty_birthday = false;
			boolean notEmpty_sex = false;
			if (LMSUtil.isBusCode_060000_130300(busCode)) {
				notEmpty_birthday = true;
				notEmpty_sex = true;

			} else {
				// 公司戶 UI上未呈現[birthday, sex]
			}

			if (notEmpty_birthday && Util.isEmpty(birthday)) {
				HashMap<String, String> msg = new HashMap<String, String>();
				msg.put("colName", getI18nMsg("C101S01A.birthday"));

				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.欄位不得為空, msg), getClass());
			}
			if (notEmpty_sex && Util.isEmpty(sex)) {
				HashMap<String, String> msg = new HashMap<String, String>();
				msg.put("colName", getI18nMsg("C101S01A.sex"));

				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.欄位不得為空, msg), getClass());
			}

			if (Util.isEmpty(ntCode)) {
				HashMap<String, String> msg = new HashMap<String, String>();
				msg.put("colName", getI18nMsg("C101S01A.ntCode"));

				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.欄位不得為空, msg), getClass());
			}
		}
		// ===============================================================

		C101S01J c101s01j = clsService.findC101S01J(c101m01a);
		if (c101s01j == null) {
			c101s01j = new C101S01J();
			// =========
			c101s01j.setMainId(c101m01a.getMainId());
			c101s01j.setCustId(c101m01a.getCustId());
			c101s01j.setDupNo(c101m01a.getDupNo());
		}

		if (true) {
			boolean injectFlag = false;
			String amlRefNo = Util.trim(c101s01j.getAmlRefNo());
			String empNo = Util.trim(user.getUserId());
			if (Util.isEmpty(amlRefNo)) {
				injectFlag = true;
			} else {
				injectFlag = _injectFlag(amlRefNo, empNo);
			}

			if (injectFlag) {
				try {
					clsService.inject_AMLRefNo(c101m01a, c101s01j, clientIP,
							empNo);
				} catch (CapException e) {
					throw new CapMessageException(e.getMessage(), getClass());
				}
			}
		}

		if (true) {
			L120S09B l120s09b = _aml_initL120S09B(mainId, ownBrId, c101s01j);
			L120S09A l120s09a = clsService.findL120S09A_cls1131(l120s09b);
			if (l120s09a == null) {
				l120s09a = new L120S09A();
				// ~~~~~~~~~~~~~~~~
				l120s09a.setMainId(mainId);
				l120s09a.setCreateBY(UtilConstants.Casedoc.L120s04aCreateBY.系統產生);
				l120s09a.setCustId(custId);
				l120s09a.setDupNo(dupNo);
				l120s09a.setCustRelation("");
				l120s09a.setQueryDateS(null);
				l120s09a.setBlackListCode("");
				l120s09a.setChkYN(UtilConstants.DEFAULT.否);
				l120s09a.setMemo("");
				l120s09a.setCreateTime(CapDate.getCurrentTimestamp());
				l120s09a.setCreator(user.getUserId());
			}
			l120s09a.setCustName(custName);
			l120s09a.setCustEName(eName);
			l120s09a.setCountry(ntCode);

			Map<String, String> idDup_busCode_map = new HashMap<String, String>();
			Map<String, String> idDup_country_map = new HashMap<String, String>();
			Map<String, String> idDup_idNo_map = new HashMap<String, String>();
			Map<String, String> idDup_birth_map = new HashMap<String, String>();
			Map<String, String> idDup_gender_map = new HashMap<String, String>();
			String idDup = LMSUtil.getCustKey_len10custId(custId, dupNo);
			// ====================
			if (true) {
				idDup_busCode_map.put(idDup, busCode);
			}

			if (Util.equals(l120s09b.getClassName(), "C101M01A")) {
				if (true) {
					// 國籍為 TW，才傳送 id 至 COM.BELAMLITEM 的 "IDNO X(20)"
					// 其它國籍, 可能是稅籍編號, [1]無比對意義 [2]會重複
					if (Util.equals("TW", ntCode)) {
						idDup_idNo_map.put(idDup, custId);
					}
				}
				if (true) {
					idDup_country_map.put(idDup, ntCode);
				}
				/*
				 * 
				 * 企業戶，先不傳送 birthday
				 * 
				 * 個人戶 也可能是 S-擔保品提供人
				 * 
				 * 若要傳送 ID, 要考量 '外籍人士' 是否取得本國國籍［稅籍編號 → 統一證號 → 國民身分證統一編號］
				 */
				if (LMSUtil.isBusCode_060000_130300(idDup_busCode_map
						.get(idDup))) {
					if (true) {
						// ElAmlItem.birth, 註解內容 YYYY格式
						Date dt_birthday = CapDate.parseDate(birthday);
						if (dt_birthday != null) {
							String aml_fmt_val = ClsUtil
									.to_AML_fmt_birth(dt_birthday);
							if (Util.isNotEmpty(aml_fmt_val)) {
								idDup_birth_map.put(idDup, aml_fmt_val);
							}
						}
					}

					if (true) {
						// ElAmlItem.gender 的值域M/F
						String aml_fmt_val = ClsUtil.to_AML_fmt_sex(sex);
						if (Util.isNotEmpty(aml_fmt_val)) {
							idDup_gender_map.put(idDup, aml_fmt_val);
						}
					}
				}
			}

			l120s09b.setQueryDateS(CapDate.getCurrentTimestamp());
			_aml_sendAmlListInner(l120s09b, l120s09a, idDup_country_map,
					idDup_idNo_map, idDup_birth_map, idDup_gender_map);

			l120s09b.setNcResult(UtilConstants.SasNcResult.掃描中);
			clsService.save(l120s09b);
			if (true) {
				c101s01j.setAmlRefOid(l120s09b.getOid());
				clsService.save(c101s01j);
			}
		}

		if (true) {
			CapAjaxFormResult param_map = new CapAjaxFormResult();
			param_map.set("mainId", mainId);
			param_map.set("action", "send_cls1131_AmlList");
			param_map.set("idDup", custId + "-" + dupNo);
			param_map.set("eName", eName);
			param_map.set("birthday", birthday);
			param_map.set("sex", sex);
			param_map.set("ntCode", ntCode);
			result.set("send_param_map", param_map);
		}
		return result;
	}

	private boolean _injectFlag(String refNo, String empNo) {

		String _date = clsService.get_C900S02B_ref_cat_data(refNo, 1);
		String _empNo = clsService.get_C900S02B_ref_cat_data(refNo, 2);

		if (!Util.equals(empNo, _empNo)) {
			return true;
		}
		// =============
		int days = 90;
		Date using = CapDate.parseDate(_date);
		if (using == null
				|| LMSUtil.cmpDate(CapDate.shiftDays(using, days), "<",
						CapDate.getCurrentTimestamp())) {
			return true;
		}
		// =============
		if (using != null) {
			String memoYear = Util.trim(StringUtils.substring(
					TWNDate.toAD(using), 0, 4));
			String nowYear = Util.trim(StringUtils.substring(
					TWNDate.toAD(CapDate.getCurrentTimestamp()), 0, 4));
			if (!Util.equals(memoYear, nowYear)) {
				return true;
			}
		}
		// =============
		return false;
	}

	private L120S09B _aml_initL120S09B(String mainId, String caseBrId,
			C101S01J c101s01j) {
		String refNo = Util.trim(c101s01j.getAmlRefNo());
		// String l120s09b_oid = Util.trim(c101s01j.getAmlRefOid());
		String l120s09b_oid = ""; // 每次送出AML查詢, 可能需寫入 l120s09b_oid
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		L120S09B l120s09b = clsService.findL120S09B_refNo_or_oid(refNo,
				l120s09b_oid);
		if (l120s09b == null) {
			l120s09b = new L120S09B();
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			l120s09b.setMainId(mainId);
			l120s09b.setCreateTime(nowTS);
			l120s09b.setCreator(user.getUserId());
			l120s09b.setClassName("C101M01A");

			l120s09b.setCaseBrId(caseBrId);
			l120s09b.setCaseYear(Integer.parseInt(StringUtils.substring(
					TWNDate.toAD(nowTS), 0, 4)));
			l120s09b.setCaseSeq(null);
			l120s09b.setCaseNo("");
			l120s09b.setCaseDate(null);
			l120s09b.setRefNo(refNo);
		}

		if (l120s09b != null) {
			l120s09b.setNcResult(""); // 每次重新查詢都要設為未掃描
										// UtilConstants.SasNcResult.未掃描
			l120s09b.setUniqueKey(IDGenerator.getUUID()); // 每次重新查詢都要重編
			l120s09b.setNcCaseId("");

			// P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML相關功能。
			if (amlRelateService.isOracle(Util.trim(l120s09b.getCaseBrId()))) {
				LMSUtil.setRefNo_inOracleFormat(l120s09b);
			}

		}
		return l120s09b;
	}

	private void _aml_sendAmlListInner(L120S09B l120s09b, L120S09A l120s09a,
			Map<String, String> idDup_country_map,
			Map<String, String> idDup_idNo_map,
			Map<String, String> idDup_birth_map,
			Map<String, String> idDup_gender_map) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String countryType = Util.trim(branchService
				.getBranch(user.getUnitNo()).getCountryType());

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		NumberFormat nf = new DecimalFormat("000");

		ArrayList<L120S09A> newL120s09as = new ArrayList<L120S09A>();
		ArrayList<ElAmlItem> elamlitems = new ArrayList<ElAmlItem>();
		int checkSeq = 0;

		// 設定AML HEADER
		ElAml elaml = clsService.aml_generateElAml(l120s09b);

		if (l120s09a != null) {

			// for (L120S09A l120s09a : l120s09as) {
			String tCustId = Util.trim(l120s09a.getCustId());
			String tDupNo = Util.trim(l120s09a.getDupNo());
			String idDup = LMSUtil.getCustKey_len10custId(tCustId, tDupNo);
			String tCustName = Util.toSemiCharString(Util.trim(l120s09a
					.getCustName()));
			String tCustEName = Util.trim(l120s09a.getCustEName());
			String qName = Util.notEquals(tCustEName, "") ? tCustEName
					: tCustName;

			if (Util.equals(qName, "")) {
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("L120S09a.message07"),
						pop.getProperty("L120S09a.custName")), getClass());
			}

			l120s09a.setBlackListCode("");
			l120s09a.setMemo("");
			l120s09a.setQueryDateS(l120s09b.getQueryDateS());
			l120s09a.setSeqNum(0);
			l120s09a.setCheckSeq(nf.format(++checkSeq));
			l120s09a.setCheckResult("");
			l120s09a.setHitList("");
			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」

			// ======================
			// 個金徵信, 尚不需判斷 CM1_AML_STATUS , luvRiskLevel
			// if (Util.notEquals(tCustId, "") && Util.notEquals(tDupNo, "")) {
			// String CM1_AML_STATUS = amlRelateService
			// .getCustIn0024AmlStatus(tCustId, tDupNo);
			// l120s09a.setCm1AmlStatus(CM1_AML_STATUS);
			// -----------
			// String luvRiskLevel =
			// amlRelateService.getCustLuvRiskLevel(user.getUnitNo(), tCustId,
			// tDupNo);
			// l120s09a.setLuvRiskLevel(luvRiskLevel);
			// }

			// 設定AML DETAIL-*************************
			ElAmlItem elamlitem = new ElAmlItem();

			String checkseq = l120s09a.getCheckSeq();
			String entityType = "01"; // 名稱 (無法區分名單種類)
			String entityRel = ""; // Entity_Relationship

			// ======================
			// 個金徵信, 不知道身份別
			// String[] newItem = Util.trim(l120s09a.getCustRelation()).split(
			// ",");
			if (true) {
				if (Util.notEquals(entityRel, "01")
						&& Util.notEquals(entityRel, "02")) {
					entityRel = "03"; // 03 其他關係人(可敘做)
				}
			}
			String enitityRelDes = "";// 相關身分

			String eName = Util.toSemiCharString(Util.trim(l120s09a
					.getCustEName()));
			String nonEname = Util.trim(l120s09a.getCustName());
			String country = Util.trim(l120s09a.getCountry());
			if (Util.isEmpty(country)) {
				country = Util.trim(MapUtils
						.getString(idDup_country_map, idDup));
			}
			String idNo = Util.trim(MapUtils.getString(idDup_idNo_map, idDup));
			String birth = Util
					.trim(MapUtils.getString(idDup_birth_map, idDup));
			String gender = Util.trim(MapUtils.getString(idDup_gender_map,
					idDup));
			String checkResult = ""; // AML_NameCheck (WS回傳或主動寫回結果) 回傳值不用塞
			String routeRule = ""; // AML_NameCheck (WS回傳或主動寫回結果) 回傳值不用塞
			String hitList = ""; // AML_NameCheck (WS回傳或主動寫回結果) 回傳值不用塞

			elamlitem.setUniKey(Util.trim(l120s09b.getUniqueKey()));
			elamlitem.setCheckseq(checkseq);
			elamlitem.setEntityType(entityType);
			elamlitem.setEntityRel(entityRel);
			elamlitem.setEnitityRelDes(enitityRelDes);
			elamlitem.seteName(eName);

			if (Util.notEquals(countryType, "US")) {
				elamlitem.setNonEname(nonEname);
			} else {
				elamlitem.setNonEname("");
			}

			elamlitem.setCountry(country);
			elamlitem.setIdNo(idNo);
			elamlitem.setBsCode("");
			elamlitem.setBirth(birth);
			elamlitem.setGender(gender);
			elamlitem.setCheckResult(checkResult);
			elamlitem.setRouteRule(routeRule);
			elamlitem.setHitList(hitList);

			// ******************************************
			elamlitems.add(elamlitem);
			newL120s09as.add(l120s09a);
			// }

		}

		if (newL120s09as != null && !newL120s09as.isEmpty()) {

			AmlStrategy as = amlRelateService.getAmlStrategy("");

			as.sendAmlList(elaml, elamlitems);

			amlRelateService.saveL120s09aList(newL120s09as);
		}
	}

	/*
	 * $.ajax({ handler: "cls1131formhandler", action: "qDate", data:
	 * {'keys':'A1xxxxxxxx-0'}, success: function(json){ alert(json.msg||'ok');
	 * } });
	 */
	/*
	 * public IResult qDate(PageParameters params, Component parent) throws
	 * CapException { //XXX qDate(...) CapAjaxFormResult result = new
	 * CapAjaxFormResult(); MegaSSOUserDetails user =
	 * MegaSSOSecurityContext.getUserDetails();
	 * 
	 * String keys = Util.trim(params.getString("keys")); String[] uckeys =
	 * StringUtils.split(keys, "-"); String custId = Util.trim(uckeys[0]);
	 * String dupNo = Util.trim(uckeys[1]);
	 * 
	 * if(Util.isEmpty(custId)){ throw new CapMessageException("empty custId",
	 * getClass()); }
	 * 
	 * C101M01A c101m01a = clsService.findC101M01A_brIdDup(user.getUnitNo(),
	 * custId, dupNo); List<String> msg = new ArrayList<String>();
	 * if(c101m01a==null){ msg.add("find none"); }else{
	 * msg.add("["+custId+"-"+dupNo+"]"); if(!Util.equals("Y",
	 * c101m01a.getImportFlag())){ c101m01a.setImportFlag("Y"); //===
	 * msg.add("c101m01a"); clsService.daoSave(c101m01a); } C101S01E c101s01e =
	 * clsService.findC101S01E(c101m01a); if(c101s01e!=null){
	 * if(LMSUtil.qdate_expired(c101s01e.getEJcicQDate(),
	 * c101s01e.getEChkQDate())){
	 * c101s01e.setEJcicQDate(CapDate.getCurrentTimestamp());
	 * c101s01e.setEChkQDate(CapDate.getCurrentTimestamp()); //===
	 * msg.add("c101s01e"); clsService.daoSave(c101s01e); } } msg.add("ok"); }
	 * 
	 * result.set("msg", StringUtils.join(msg, " , ")); return result; }
	 */

	/**
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult c_query_credit(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		// String dupNo = Util.trim(params.getString("dupNo"));

		boolean injectVal = true;
		String prodId = ejcicService.get_cls_PRODID(custId);
		Map<String, Object> dateMap = ejcicService.getDate(custId, prodId);
		if (dateMap == null) {
			// message.c_query_credit.noJCIC=無聯徵查詢結果。請先查詢聯徵後，再執行引進！
			throw new CapMessageException( "【"+getI18nMsg("C101S01C.credit")+"】"+
					getI18nMsg("message.c_query_credit.noJCIC"), getClass());
		}

		// 取得相關日期
		String QDATE = Util.trim(dateMap.get("QDATE"));

		boolean hasA = false;
		Set<String> krm040_ISSUE_set = new HashSet<String>();

		boolean hasB = false;
		Set<String> bam095_ISSUE_set = new HashSet<String>();
		if (true) {
			// 最近一年有使用兩家(含)以上銀行之信用卡循環信用紀錄
			for (Map<String, Object> row : ejcicService.getKRM040_data(custId,
					prodId, QDATE)) {
				String PAY_STAT = Util
						.trim(MapUtils.getString(row, "PAY_STAT"));
				String ISSUE = Util.trim(MapUtils.getString(row, "ISSUE"));
				if (Util.equals(PAY_STAT, "X") || Util.equals(PAY_STAT, "1")) {

				} else {
					krm040_ISSUE_set.add(ISSUE);
				}
			}
			hasA = (krm040_ISSUE_set.size() >= 2);
		}
		if (true) {
			for (Map<String, Object> row : ejcicService.getBAM095_data(custId,
					prodId, QDATE)) {
				String ACCOUNT_CODE = Util.trim(MapUtils.getString(row,
						"ACCOUNT_CODE"));
				if (Util.equals(ACCOUNT_CODE, "Y")) {
					String BANK_CODE = Util.trim(MapUtils.getString(row,
							"BANK_CODE"));
					// 8090131 凱基商業銀行北高雄分行
					// 8090072 凱基商業銀行城東分行
					String ISSUE = StringUtils.substring(BANK_CODE, 0, 3);
					String PAY_CODE_12 = Util.trim(MapUtils.getString(row,
							"PAY_CODE_12"));
					Map<String, Integer> map_parse = parsePAY_CODE_12(PAY_CODE_12);
					map_parse.remove("");
					map_parse.remove(" ");
					map_parse.remove("X");
					if (map_parse.size() > 0) {
						bam095_ISSUE_set.add(ISSUE);
					}
				}
			}
			hasB = (bam095_ISSUE_set.size() >= 2);
		}

		// ==============================================
		result.set("raw_QDATE", QDATE);
		result.set("raw_A", "[hasA=" + (hasA ? "Y" : "N") + "],issue="
				+ krm040_ISSUE_set);
		result.set("raw_B", "[hasB=" + (hasB ? "Y" : "N") + "],issue="
				+ bam095_ISSUE_set);

		result.set("injectVal", injectVal ? "Y" : "N");
		if (injectVal) {
			List<String> list = new ArrayList<String>();
			if (hasA) {
				list.add("A");
			}
			if (hasB) {
				list.add("B");
			}
			result.set("newVal", StringUtils.join(list, "|"));
		}

		return result;
	}

	private Map<String, Integer> parsePAY_CODE_12(String PAY_CODE_12) {
		int size = PAY_CODE_12.length();
		Map<String, Integer> result = new HashMap<String, Integer>();
		for (int i = 0; i < size; i++) {
			String v = StringUtils.substring(PAY_CODE_12, i, i + 1);
			if (result.containsKey(v)) {
				result.put(v, 1 + (result.get(v)));
			} else {
				result.put(v, 1);
			}
		}
		return result;
	}

	/**
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult c_query_isPeriodFund(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		boolean injectVal = true;

		String cusCode = "";
		if (Util.equals(dupNo, "0")) {
			cusCode = custId;
		} else {
			cusCode = custId + dupNo;
		}

		List<Map<String, Object>> list = dwdbService
				.findDW_OTS_ASFDAC2_J_107_0073(cusCode);
		int active_cnt = list.size();
		result.set("raw_size", active_cnt);

		result.set("injectVal", injectVal ? "Y" : "N");
		if (injectVal) {
			result.set("newVal", active_cnt > 0 ? "Y" : "N");
		}

		return result;
	}

	/**
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult c_query_busi(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		Map<String, Object> map = misBaseService.findCMFLUNVA_byUk(custId,
				dupNo);
		String LUV_DEPT_6 = Util.trim(MapUtils.getString(map, "LUV_DEPT_6"));
		String LUV_DEPT_9 = Util.trim(MapUtils.getString(map, "LUV_DEPT_9"));

		boolean injectVal = true;
		String newVal = "";

		if (map == null || map.size() == 0) {
			// 若全新戶, 可能根本未維護 0024-23往來業務項目
			// throw new
			// CapMessageException(getI18nMsg("message.c_query_busi.none"),
			// getClass());
			newVal = "0";
		} else {
			// ~~~~~~~~
			if (Util.equals("V", LUV_DEPT_6) || Util.equals("V", LUV_DEPT_9)) {
				newVal = "1";
			} else {
				newVal = "0";
			}
		}

		result.set("raw_value", "[LUV_DEPT_6=" + LUV_DEPT_6 + "][LUV_DEPT_9="
				+ LUV_DEPT_9 + "]");
		result.set("injectVal", injectVal ? "Y" : "N");
		if (injectVal) {
			result.set("newVal", newVal);
		}
		return result;
	}

	private Map<String, String > run_DW_OTS_WM_CUST_SUMMARY(String custId, String dupNo, int period){
		Map<String, String> result = new LinkedHashMap<String, String>(); 
		boolean injectVal = true;
		BigDecimal newVal = BigDecimal.ZERO;

		String cusCode = "";
		if (Util.equals(dupNo, "0")) {
			cusCode = custId;
		} else {
			cusCode = custId + dupNo;
		}
		
		String cyc_mn_beg = TWNDate
				.toAD(CapDate.addMonth(CrsUtil.get_sysMonth_1st(), -1
						* (period))).substring(0, 7)
				+ "-01";
		List<Map<String, Object>> list = dwdbService
				.findDW_OTS_WM_CUST_SUMMARY_J_107_0073(cusCode, cyc_mn_beg);
		BigDecimal sum = BigDecimal.ZERO;
		int current_idx = 0;
		for (Map<String, Object> rows : list) {
			String CYC_MN = Util.trim(MapUtils.getString(rows, "CYC_MN"));
			BigDecimal INV_AMT_WMS = CrsUtil.parseBigDecimal(MapUtils
					.getObject(rows, "INV_AMT_WMS"));

			result.put("raw_value_" + current_idx++, "[CYC_MN=" + CYC_MN
					+ "][INV_AMT_WMS=" + LMSUtil.pretty_numStr(INV_AMT_WMS)
					+ "]");
			// ===
			sum = sum.add(INV_AMT_WMS);
		}
		result.put("raw_value_sum", LMSUtil.pretty_numStr(sum));

		result.put("injectVal", injectVal ? "Y" : "N");
		if (injectVal) {
			// 前端 萬元 (整數位)
			BigDecimal period_cnt_wan = new BigDecimal(10000 * period);
			if (sum.compareTo(period_cnt_wan) < 0) {
				newVal = BigDecimal.ZERO;
			} else {
				newVal = Arithmetic.div(sum, period_cnt_wan, 0);
			}
			result.put("newVal", LMSUtil.pretty_numStr(newVal));
		}
		return result;
	}
	/**
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult c_query_invMBalAmt(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		if (clsService.is_function_on_codetype("DW_OTS_WM_CUST_SUMMARY")) {
			int period = 3;
			Map<String, String > wm_map = run_DW_OTS_WM_CUST_SUMMARY(custId, dupNo, period);
			for(String key: wm_map.keySet()){
				result.set(key, wm_map.get(key));
			}
		} else {
			throw new CapMessageException(
					getI18nMsg("message.c_query_invMBalAmt.stop"), getClass());
		}
		return result;
	}
	
	/**
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult query_loanBalSByid(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		
		if(true){
			//P01 近 12 個月授信帳戶繳款狀況出現 0 的總次數，不含本行
			Map<String, Object> map = misBaseService.getLNF022_loanBalSByid(custId+dupNo);
			String loanBal = MapUtils.getString(map, "LOANBAL_S", "");
			result.set("loanBalSByid", loanBal); //因子
			String loanBalShow = loanBal.isEmpty() ? "N.A" : loanBal;
			result.set("loanBalSByidShow", loanBalShow); //因子顯示用
			result.set("loanBalSTime", CapDate.convertTimestampToString(nowTS,"yyyy-MM-dd")); //資料更新時間
		}
		return result;
	}
	
	/**
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult query_loanBalNByid(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		if(true){
			//P01 近 12 個月授信帳戶繳款狀況出現 0 的總次數，不含本行
			Map<String, Object> map = misBaseService.getLNF022_loanBalNByid(custId+dupNo);
			String loanBal = MapUtils.getString(map, "LOANBAL_N", "");
			result.set("loanBalNByid", loanBal); //因子原始值
			String loanBalShow = loanBal.isEmpty() ? "N.A" : loanBal;
			result.set("loanBalNByidShow", loanBalShow); //因子顯示用
			result.set("loanBalNTime", CapDate.convertTimestampToString(nowTS,"yyyy-MM-dd")); //資料更新時間
		}
		return result;
	}
	
	

	/**
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult viewPage_init_param(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String caseBrId = user.getUnitNo();
		result.set(ClsUtil.JSON_OUTPUT_PARAM_SHOW_S01R,
				clsService.doCardLoanBr(caseBrId) ? "Y" : "N");
		return result;
	}

	/*
	 * @DomainAuth(value = AuthType.Query , CheckDocStatus = false) public
	 * IResult test_sso(PageParameters params, Component parent) throws
	 * CapException { CapAjaxFormResult result = new CapAjaxFormResult();
	 * 
	 * // $.ajax({handler: "cls1131formhandler", action: "test_sso",data:
	 * {'param_userId':'008034'},success: function(json){}});
	 * 
	 * String userId = ""; String param_userId =
	 * Util.trim(params.getString("param_userId"));
	 * if(Util.isEmpty(param_userId)){ userId =
	 * MegaSSOSecurityContext.getUserId(); }else{ userId = param_userId; }
	 * 
	 * IMegaSSOUtil megaSSO = megaSSOUserService.getMegaSSO();
	 * if(megaSSO==null){ throw new CapMessageException("megaSSO==null",
	 * getClass()); } String ssoSystemKey = megaSSO.getSsoSystemKey(); String
	 * ssoResp = megaSSO.ssoDoService(ssoSystemKey, "DoService", "GETAPCLASS",
	 * "TXT", "EMPID="+ userId + "|APID=EL"); result.set("param_userId",
	 * param_userId); result.set("userId", userId); result.set("ssoSystemKey",
	 * ssoSystemKey); result.set("ssoResp", ssoResp); return result;
	 * 
	 * }
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult get_sso_auth(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		// $.ajax({handler: "cls1131formhandler", action: "get_sso_auth",
		// formId: 'empty',data: {'param_userId':'008034',
		// 'param_apid':'ETCH'},success: function(json){}});
		IMegaSSOUtil megaSSO = megaSSOUserService.getMegaSSO();
		if (megaSSO == null) {
			throw new CapMessageException("megaSSO==null", getClass());
		}
		String userId = "";
		String apId = ""; // 在 EJ 底下，有［LN01, PB01, EP01］ 在 ETCH 底下，有［ETCH01,
							// ETCH02］
		String param_userId = Util.trim(params.getString("param_userId"));
		String param_apid = Util.trim(params.getString("param_apid"));
		if (Util.isEmpty(param_userId)) {
			userId = MegaSSOSecurityContext.getUserId();
		} else {
			userId = param_userId;
		}

		if (Util.isEmpty(param_apid)) {
			apId = "EJ"; // default
		} else {
			apId = param_apid;
		}
		String ssoResp = megaSSO.ssoGetPermission(userId, 1, apId);
		result.set("param_userId", param_userId);
		result.set("userId", userId);
		result.set("apId", apId);
		result.set("ssoResp", ssoResp);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean has_EJ_LN01 = user.isEJCICAuth();
		boolean has_ETCH_ETCH01 = user.isETCHAuth();
		result.set("has_EJ_LN01", has_EJ_LN01);
		result.set("has_ETCH_ETCH01", has_ETCH_ETCH01);

		return result;

	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult build_C101S01S_RPS(PageParameters params)
			throws CapException, ClientProtocolException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		String custId = Util.trim(c101m01a.getCustId());
		String dupNo = Util.trim(c101m01a.getDupNo());
		String custName = Util.trim(c101m01a.getCustName());

		// 檢核是否 stop 連至外部系統
		verify_call_outerSys_RPS();

		try {
			// 往來客戶信用異常資料
			Map<String, Object> map1 = cls1131Service.getCreditAnomalyData(
					custId, dupNo, custName);
			byte[] report1 = cls1131Service.getReportOfCreditAnomalyData(map1);
			C101S01S entity1 = cls1131Service.modifyC101S01SForMixRecordData(
					mainId, custId, dupNo, ClsConstants.C101S01S_dataType.往來客戶信用異常資料,
					(String) map1.get("dataStatus"), report1);

			// 客戶是否為利害關係人資料
			Map<String, Object> map2 = cls1131Service.getStakeholderData(
					custId, dupNo, custName);
			params.put("stakeholderMap", map2);
			byte[] report2 = cls1131R04RptService.getContent(params);
			C101S01S entity2 = cls1131Service.modifyC101S01SForMixRecordData(
					mainId, custId, dupNo, ClsConstants.C101S01S_dataType.客戶是否為利害關係人資料,
					(String) map2.get("dataStatus"), report2);

			// 婉卻紀錄資料
			Map<String, Object> map3 = cls1131Service.getRefusedData(custId,
					dupNo, custName);
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			params.put("queryMan", user.getUserId() + " " + user.getUserName());
			params.put("refuseList", map3.get("refuseList"));
			byte[] pdfReport = cls1131R05RptService.getContent(params);
			C101S01S entity3 = cls1131Service.modifyC101S01SForMixRecordData(
					mainId, custId, dupNo, ClsConstants.C101S01S_dataType.婉卻紀錄資料,
					(String) map3.get("dataStatus"), pdfReport);

			result.set("dataType1_oid", entity1.getOid());
			result.set("dataType2_oid", entity2.getOid());
			result.set("dataType3_oid", entity3.getOid());
		} catch (Exception e) {
			throw new CapException(e, getClass());
		}

		return result;
	}

	public IResult build_C101S01S_RPS_by_type(PageParameters params) throws CapException, ClientProtocolException,
			IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String type = Util.trim(params.getString("type"));
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		String custId = Util.trim(c101m01a.getCustId());
		String dupNo = Util.trim(c101m01a.getDupNo());
		String custName = Util.trim(c101m01a.getCustName());

		// 檢核是否 stop 連至外部系統
		verify_call_outerSys_RPS();

		try {
			if (Util.equals("1", type)) {
				// 往來客戶信用異常資料
				Map<String, Object> map1 = cls1131Service.getCreditAnomalyData(
						custId, dupNo, custName);
				byte[] report1 = cls1131Service
						.getReportOfCreditAnomalyData(map1);
				C101S01S entity1 = cls1131Service
						.modifyC101S01SForMixRecordData(mainId, custId, dupNo,
								ClsConstants.C101S01S_dataType.往來客戶信用異常資料, (String) map1.get("dataStatus"), report1);
				result.set("dataType1_oid", entity1.getOid());
			}
			if (Util.equals("2", type)) {
				// 客戶是否為利害關係人資料
				Map<String, Object> map2 = cls1131Service.getStakeholderData(
						custId, dupNo, custName);
				params.put("stakeholderMap", map2);
				byte[] report2 = cls1131R04RptService.getContent(params);
				C101S01S entity2 = cls1131Service
						.modifyC101S01SForMixRecordData(mainId, custId, dupNo,
								ClsConstants.C101S01S_dataType.客戶是否為利害關係人資料, (String) map2.get("dataStatus"), report2);
				result.set("dataType2_oid", entity2.getOid());
			}
			if (Util.equals("3", type)) {
				// 婉卻紀錄資料
				Map<String, Object> map3 = cls1131Service.getRefusedData(
						custId, dupNo, custName);
				MegaSSOUserDetails user = MegaSSOSecurityContext
						.getUserDetails();
				params.put("queryMan",
						user.getUserId() + " " + user.getUserName());
				params.put("refuseList", map3.get("refuseList"));
				byte[] pdfReport = cls1131R05RptService.getContent(params);
				C101S01S entity3 = cls1131Service
						.modifyC101S01SForMixRecordData(mainId, custId, dupNo,
								ClsConstants.C101S01S_dataType.婉卻紀錄資料, (String) map3.get("dataStatus"), pdfReport);
				result.set("dataType3_oid", entity3.getOid());
			}

		} catch (Exception e) {
			throw new CapException(e, getClass());
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult build_C101S01S_CURIQ01(PageParameters params) throws CapException, ClientProtocolException,
			IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		String custId = Util.trim(c101m01a.getCustId());
		String dupNo = Util.trim(c101m01a.getDupNo());
		
		C101S01E c101se01e = this.clsService.findC101S01E(c101m01a);
		if(c101se01e == null){
			throw new CapMessageException("請先按儲存，再執行此功能", getClass());
		}

		// 檢核是否 stop 連至外部系統
		verify_call_outerSys_FH_EAI();

		try {
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			Map<String, Object> mapRtn = cls1131Service.getCURIQ01(mainId,
					custId);
			params.put("eaiCURIQ01_list", mapRtn.get("eaiCURIQ01_list"));
			params.put("eaiCURIQ01_ts", CapDate.convertTimestampToString(nowTS,
					UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS));

			byte[] pdfReport = cls1131R06RptService.getContent(params);
			// ~~~
			C101S01S c101s01s = cls1131Service.modifyC101S01SForMixRecordData(
					mainId, custId, dupNo, ClsConstants.C101S01S_dataType.證券暨期貨違約交割紀錄,
					(String) mapRtn.get("dataStatus"), pdfReport);
			if (c101s01s != null) {
				c101s01s.setCreateTime(nowTS);
				c101s01s.setUpdateTime(nowTS);
				c101s01s.setDataCreateTime(nowTS);
				clsService.save(c101s01s);
			}
			
			this.cls1131Service.setC101s01eVersion("1.0", c101m01a);
			
		} catch (Exception e) {
			throw new CapException(e, getClass());
		}
		return result;
	}

	private String convert_c101s01a_IdCardChgFlag_to_z21_applyReasonCode(
			String idCardChgFlag) {
		return ClsUtility.convert_c101s01a_IdCardChgFlag_to_z21_applyReasonCode(idCardChgFlag);
//		if (Util.equals("0", idCardChgFlag)) { // 初發
//			return "1";
//		} else if (Util.equals("1", idCardChgFlag)) { // 補發
//			return "2";
//		} else if (Util.equals("2", idCardChgFlag)) { // 換發
//			return "3";
//		} else if (Util.equals("", idCardChgFlag)) {
//			return " ";
//		}
//		return idCardChgFlag;
	}

	private String convert_c101s01a_IdCardPhoto_to_z21_picCd(String idCardPhoto) {
		if (Util.equals("Y", idCardPhoto)) {
			return "0";
		} else if (Util.equals("N", idCardPhoto)) {
			return "1";
		} else if (Util.equals("", idCardPhoto)) {
			return " ";
		}
		return idCardPhoto;
	}

	/**
	 * X101010107▲1000210▲2▲1000110▲0▲68000000
	 * X123400025▲0950301▲1▲0300201▲1▲66000000 <br/>
	 * [0] 身分證號 char(10) <br/>
	 * [1] 領補換日期 char(7) yyy+mm+dd <br/>
	 * [2] 領補換代號 char(1) 1:初領,2:補領,3:換領 <br/>
	 * [3] 出生日期 char(7) yyy+mm+dd <br/>
	 * [4] 有無相片 char(1) 0:有,1:無 <br/>
	 * [5] 發證地點 char(8) 66000000:中市
	 */
	private String[] get_Z21_qkey1_dataArr(C101S01A c101s01a) {
		String[] arr = new String[6];
		for (int i = 0; i < arr.length; i++) {
			arr[i] = "";
		}
		if (c101s01a != null) {
			String char10 = "          ";
			// ~~~~~~~~~~
			arr[0] = Util.trim(c101s01a.getCustId());
			arr[1] = c101s01a.getIdCardIssueDate() == null ? Util.getLeftStr(
					char10, 7) : TWNDate.valueOf(c101s01a.getIdCardIssueDate())
					.toTW();
			arr[2] = convert_c101s01a_IdCardChgFlag_to_z21_applyReasonCode(c101s01a
					.getIdCardChgFlag());
			arr[3] = c101s01a.getBirthday() == null ? Util
					.getLeftStr(char10, 7) : TWNDate.valueOf(
					c101s01a.getBirthday()).toTW();
			arr[4] = convert_c101s01a_IdCardPhoto_to_z21_picCd(c101s01a
					.getIdCardPhoto());
			arr[5] = Util.trim(c101s01a.getIdCard_siteId());
		}
		return arr;
	}

	private void validate_Z21_qkey1_dataArr(String[] dataArr)
			throws CapMessageException {
		if (dataArr != null && dataArr.length == 6) {
			List<String> msg_list = new ArrayList<String>();
			if (Util.isEmpty(Util.trim(dataArr[3]))) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC)
						+ "需輸入「" + getI18nMsg("C101S01A.birthday") + "」");
			} else if (dataArr[3].length() != 7) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC)
						+ getI18nMsg("C101S01A.birthday") + "(" + dataArr[3]
						+ ")資料長度錯誤");
			}

			if (Util.isEmpty(Util.trim(dataArr[1]))) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC)
						+ "需輸入「" + getI18nMsg("label.idCardIssueDate") + "」");
			} else if (dataArr[1].length() != 7) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC)
						+ getI18nMsg("label.idCardIssueDate") + "("
						+ dataArr[1] + ")資料長度錯誤");
			}

			if (Util.isEmpty(Util.trim(dataArr[5]))) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC)
						+ "需輸入「" + getI18nMsg("label.idCard_siteId") + "」");
			} else if (dataArr[5].length() != 8) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC)
						+ getI18nMsg("label.idCard_siteId") + "(" + dataArr[5]
						+ ")資料長度應為8");
			}

			if (Util.isEmpty(Util.trim(dataArr[2]))) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC)
						+ "需輸入「" + getI18nMsg("label.idCardChgFlag") + "」");
			} else if (dataArr[2].length() != 1) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC)
						+ getI18nMsg("label.idCardChgFlag") + "(" + dataArr[2]
						+ ")資料長度應為1");
			}

			if (Util.isEmpty(Util.trim(dataArr[4]))) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC)
						+ "需輸入「" + getI18nMsg("label.idCardPhoto") + "」");
			} else if (dataArr[4].length() != 1) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC)
						+ getI18nMsg("label.idCardPhoto") + "，資料長度應為1");
			}

			if (msg_list.size() > 0) {
				throw new CapMessageException(StringUtils.join(msg_list,
						"<br/>"), getClass());
			}
		}
	}

	private void verify_call_outerSys_RPS() throws CapMessageException {
		_verify_call_outerSys("RPS");
	}

	private void verify_call_outerSys_FH_EAI() throws CapMessageException {
		_verify_call_outerSys("FH_EAI");
	}

	private void verify_call_outerSys_ETCH() throws CapMessageException {
		_verify_call_outerSys("ETCH");
	}

	private void verify_call_outerSys_EJ() throws CapMessageException {
		_verify_call_outerSys("EJ");
	}

	private void verify_call_outerSys() throws CapMessageException {
		_verify_call_outerSys("RPS|FH_EAI|ETCH|EJ");
	}

	private void _verify_call_outerSys(String paramArr)
			throws CapMessageException {
		Map<String, String> map = clsService
				.get_codeTypeWithOrder("J-108-0277_stop_call");
		List<String> errMsg = new ArrayList<String>();
		for (String param : paramArr.split("\\|")) {
			if (!map.containsKey(param)) {
				continue;
			}
			String desc = Util.trim(map.get(param));
			if (Util.isNotEmpty(desc)) {
				errMsg.add(desc);
			}
		}
		if (errMsg.size() > 0) {
			throw new CapMessageException(StringUtils.join(errMsg, "<br/>"),
					getClass());
		}
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult keep_EJ_ST_queryOutput(PageParameters params) throws CapMessageException, ClientProtocolException, IOException, Exception  {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String txId = Util.trim(params.getString("txId"));
		String param_txId = txId;
//		if (Util.equals(param_txId, "Z21_Prod69")) {
//			txId = CrsUtil.EJ_TXID_Z21;
//		} else if (Util.equals(param_txId, "Z13_Prod69")) {
//			txId = CrsUtil.EJ_TXID_Z13;
//		}
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		String userId = user.getUserId();
		String empname = lmsService.getUserName(userId);
		String deptid = user.getUnitNo();
		String cbdeptid = "";
		String deptnm = "";
		IBranch iBranch_deptid = branchService.getBranch(deptid);
		if (iBranch_deptid != null) {			
			deptnm = iBranch_deptid.getBrName();		
		}
		
		cbdeptid = get_cbdeptid_totLen4_BrNo_ChkNo(get_cbdeptid_brNoLen3_for_PersonalLoan_callCenter(user));		
		if (Util.isEmpty(cbdeptid)) {
			throw new CapMessageException("cbdeptid=", getClass());
		}

		// 檢核是否 stop 連至外部系統
		verify_call_outerSys_EJ();

		String pur_A4A = "A4A"; //新業務申請-當事人書面同意
		String pur_A4G = "A4G"; //新業務申請-當事人書面同意-辦理勞工紓困貸款，用在 B36,D10,R20
		CapAjaxFormResult result = new CapAjaxFormResult();

		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		C101S01A c101s01a = clsService.findC101S01A(c101m01a);
		C101S01E c101s01e = clsService.findC101S01E(c101m01a);

		if (Util.equals(param_txId, CrsUtil.API_TXID_ID_CARD_CHECK)
			|| Util.equals(param_txId, CrsUtil.RPA_TXID_FA)
			|| Util.equals(param_txId, CrsUtil.WiseNews)){
			//有一些功能，已經由查EJCIC 改為其它代替方式
			/*
			  例如：
			 	+ 身分證換補 由 Z21 改查 內政部API   => Z21 檢核 PB01權限
			 	+ 受監護/輔助宣告 由 Z13 改以RPA查詢 司法院網站 => Z13 檢核 LN01權限
			 
			 若「單查」這幾個項目，應不須去檢核，是否具有行內的 EJ(聯徵查詢系統) - LN01 或 PB01 的權限
			*/
		}else{
//			if (Util.equals(txId, CrsUtil.EJ_TXID_Z21)) {
//				_oneBtnQueryAuthChk(user, false, true, false);
//			} else {
				// 例如 Z13, D10, R20 ，只需 LN01 即可
				_oneBtnQueryAuthChk(user, true, false, false);
//			}
		}
		// =====================
		if (c101s01e == null) {
			c101s01e = new C101S01E();
			c101s01e.setMainId(c101m01a.getMainId());
			c101s01e.setCustId(c101m01a.getCustId());
			c101s01e.setDupNo(c101m01a.getDupNo());
		}
		String custId = c101m01a.getCustId();
//		if (Util.equals(param_txId, CrsUtil.EJ_TXID_Z21)
//				|| Util.equals(param_txId, "Z21_Prod69")) {
//			if (c101s01a == null) {
//				throw new CapMessageException("c101s01a==null", getClass());
//			}
//			String[] _Z21_qkey1_dataArr = get_Z21_qkey1_dataArr(c101s01a);
//			validate_Z21_qkey1_dataArr(_Z21_qkey1_dataArr);
//
//			EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
//			ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
//			ejcicReq_ST.setMsgId(IDGenerator.getUUID());
//			ejcicReq_ST.setQueryid(custId);
//			ejcicReq_ST.setEmpid(userId);
//			ejcicReq_ST.setEmpname(empname);
//			ejcicReq_ST.setDeptid(deptid);
//			ejcicReq_ST.setCbdeptid(cbdeptid);
//			ejcicReq_ST.setBranchnm(deptnm);
//			ejcicReq_ST.setPur(pur_A4A);
//			if (Util.equals(param_txId, "Z21_Prod69")) {
//				ejcicReq_ST.setPur(pur_A4G);
//			}
//			if (true) {
//				ejcicReq_ST.setProdid("");
//				ejcicReq_ST.setTxid(txId);
//				ejcicReq_ST.setQkey1(StringUtils.join(_Z21_qkey1_dataArr));// 由N個欄位組合
//				ejcicReq_ST.setQkey2("");
//			}
			
//			String url_Z21 = Util
//					.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST)); // 取得 url
//			keep_url_htmloutput_to_c101s01e(txId, c101s01e, url_Z21);
			
//		} else if (Util.equals(param_txId, CrsUtil.EJ_TXID_Z13)
//				|| Util.equals(param_txId, "Z13_Prod69")) {
//			EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
//			ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
//			ejcicReq_ST.setMsgId(IDGenerator.getUUID());
//			ejcicReq_ST.setQueryid(custId);
//			ejcicReq_ST.setEmpid(userId);
//			ejcicReq_ST.setEmpname(empname);
//			ejcicReq_ST.setDeptid(deptid);
//			ejcicReq_ST.setCbdeptid(cbdeptid);
//			ejcicReq_ST.setBranchnm(deptnm);
//			ejcicReq_ST.setPur(pur_A4A);
//			if (Util.equals(param_txId, "Z13_Prod69")) {
//				ejcicReq_ST.setPur(pur_A4G);
//			}
//			if (true) {
//				ejcicReq_ST.setProdid("");
//				ejcicReq_ST.setTxid(txId);
//				ejcicReq_ST.setQkey1(custId);// custId
//				ejcicReq_ST.setQkey2("");
//			}
//			String url_Z13 = Util
//					.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST)); // 取得 url
//			keep_url_htmloutput_to_c101s01e(txId, c101s01e, url_Z13);
//			
//		} else 
		if (Util.equals(param_txId, CrsUtil.EJ_TXID_B36)) {
			EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
			ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
			ejcicReq_ST.setMsgId(IDGenerator.getUUID());
			ejcicReq_ST.setQueryid(custId);
			ejcicReq_ST.setEmpid(userId);
			ejcicReq_ST.setEmpname(empname);
			ejcicReq_ST.setDeptid(deptid);
			ejcicReq_ST.setCbdeptid(cbdeptid);
			ejcicReq_ST.setBranchnm(deptnm);
			ejcicReq_ST.setPur(pur_A4G);
			if (true) {
				ejcicReq_ST.setProdid("");
				ejcicReq_ST.setTxid(txId);
				ejcicReq_ST.setQkey1(custId);// custId
				ejcicReq_ST.setQkey2("");
			}
			String url_B36 = Util
					.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST)); // 取得 url
			keep_url_htmloutput_to_c101s01u(c101m01a, txId, url_B36);
		} else if (Util.equals(param_txId, CrsUtil.EJ_TXID_D10)) {
			EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
			ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
			ejcicReq_ST.setMsgId(IDGenerator.getUUID());
			ejcicReq_ST.setQueryid(custId);
			ejcicReq_ST.setEmpid(userId);
			ejcicReq_ST.setEmpname(empname);
			ejcicReq_ST.setDeptid(deptid);
			ejcicReq_ST.setCbdeptid(cbdeptid);
			ejcicReq_ST.setBranchnm(deptnm);
			ejcicReq_ST.setPur(pur_A4G);
			if (true) {
				ejcicReq_ST.setProdid("");
				ejcicReq_ST.setTxid(txId);
				ejcicReq_ST.setQkey1(custId);// custId
				ejcicReq_ST.setQkey2("");
			}
			String url_D10 = Util
					.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST)); // 取得 url
			keep_url_htmloutput_to_c101s01u(c101m01a, txId, url_D10);
		} else if (Util.equals(param_txId, CrsUtil.EJ_TXID_R20)) {
			EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
			ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
			ejcicReq_ST.setMsgId(IDGenerator.getUUID());
			ejcicReq_ST.setQueryid(custId);
			ejcicReq_ST.setEmpid(userId);
			ejcicReq_ST.setEmpname(empname);
			ejcicReq_ST.setDeptid(deptid);
			ejcicReq_ST.setCbdeptid(cbdeptid);
			ejcicReq_ST.setBranchnm(deptnm);
			ejcicReq_ST.setPur(pur_A4G);
			if (true) {
				ejcicReq_ST.setProdid("");
				ejcicReq_ST.setTxid(txId);
				ejcicReq_ST.setQkey1(custId);// custId
				ejcicReq_ST.setQkey2("");
			}
			String url_R20 = Util
					.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST)); // 取得 url
			keep_url_htmloutput_to_c101s01u(c101m01a, txId, url_R20);
		} else if (Util.equals(param_txId, CrsUtil.EJ_TXID_S11)) {
				EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
				ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
				ejcicReq_ST.setMsgId(IDGenerator.getUUID());
				ejcicReq_ST.setQueryid(custId);
				ejcicReq_ST.setEmpid(userId);
				ejcicReq_ST.setEmpname(empname);
				ejcicReq_ST.setDeptid(deptid);
				ejcicReq_ST.setCbdeptid(cbdeptid);
				ejcicReq_ST.setBranchnm(deptnm);
				ejcicReq_ST.setPur(pur_A4A);
				if (true) {
					ejcicReq_ST.setProdid("");
					ejcicReq_ST.setTxid(txId);
					ejcicReq_ST.setQkey1(custId);// custId
					ejcicReq_ST.setQkey2("");
				}
				String url_S11 = Util
						.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST)); // 取得 url
				keep_url_htmloutput_to_c101s01u(c101m01a, txId, url_S11);
		} else if(Util.equals(CrsUtil.EJ_TXID_B29, param_txId)){
			EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
			ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
			ejcicReq_ST.setMsgId(IDGenerator.getUUID());
			ejcicReq_ST.setQueryid(custId);
			ejcicReq_ST.setEmpid(userId);
			ejcicReq_ST.setEmpname(empname);
			ejcicReq_ST.setDeptid(deptid);
			ejcicReq_ST.setCbdeptid(cbdeptid);
			ejcicReq_ST.setBranchnm(deptnm);
			ejcicReq_ST.setPur(pur_A4A);
			if (true) {
				ejcicReq_ST.setProdid("");
				ejcicReq_ST.setTxid(txId);
				ejcicReq_ST.setQkey1(custId);// custId
				ejcicReq_ST.setQkey2("");
			}
			String url_B29 = Util.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST)); // 取得 url
			keep_url_htmloutput_to_c101s01u(c101m01a, txId, url_B29);
		} else if(Util.equals(CrsUtil.EJ_TXID_B68, param_txId)){
			EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
			ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
			ejcicReq_ST.setMsgId(IDGenerator.getUUID());
			ejcicReq_ST.setQueryid(custId);
			ejcicReq_ST.setEmpid(userId);
			ejcicReq_ST.setEmpname(empname);
			ejcicReq_ST.setDeptid(deptid);
			ejcicReq_ST.setCbdeptid(cbdeptid);
			ejcicReq_ST.setBranchnm(deptnm);
			ejcicReq_ST.setPur(pur_A4A);
			if (true) {
				ejcicReq_ST.setProdid("");
				ejcicReq_ST.setTxid(txId);
				ejcicReq_ST.setQkey1(custId);// custId
				ejcicReq_ST.setQkey2("");
			}
			String url_B68 = Util.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST)); // 取得 url
			keep_url_htmloutput_to_c101s01u(c101m01a, txId, url_B68);
		} else if(Util.equals(CrsUtil.EJ_TXID_B33, param_txId)){
			EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
			ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
			ejcicReq_ST.setMsgId(IDGenerator.getUUID());
			ejcicReq_ST.setQueryid(custId);
			ejcicReq_ST.setEmpid(userId);
			ejcicReq_ST.setEmpname(empname);
			ejcicReq_ST.setDeptid(deptid);
			ejcicReq_ST.setCbdeptid(cbdeptid);
			ejcicReq_ST.setBranchnm(deptnm);
			ejcicReq_ST.setPur(pur_A4A);
			if (true) {
				ejcicReq_ST.setProdid("");
				ejcicReq_ST.setTxid(txId);
				ejcicReq_ST.setQkey1(custId);// custId
				ejcicReq_ST.setQkey2("");
			}
			String url_B33 = Util.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST)); // 取得 url
			keep_url_htmloutput_to_c101s01u(c101m01a, txId, url_B33);
		} else if (Util.equals(param_txId, CrsUtil.API_TXID_ID_CARD_CHECK)){
			//check 身分證領補換資料「發證日期 發證地點 領補換類別」是否有輸入
			if (c101s01a == null) {
				throw new CapMessageException("c101s01a==null", getClass());
			}
			String[] _Z21_qkey1_dataArr = get_Z21_qkey1_dataArr(c101s01a);
			validate_Z21_qkey1_dataArr(_Z21_qkey1_dataArr);
			//------------------------------------------------------------
			
			String applyCode = convert_c101s01a_IdCardChgFlag_to_z21_applyReasonCode(c101s01a.getIdCardChgFlag());
			String applyYYYMMDD = CapDate.formatDate(c101s01a.getIdCardIssueDate(), "YYYMMDD");
			IdentificationCheckGwReqMessage req = new IdentificationCheckGwReqMessage(c101s01a.getCustId(), applyCode, applyYYYMMDD, 
															this.clsService.getSiteIdByApiInquiry(c101s01a.getIdCard_siteId()), user.getUserId());
			identificationCheckGwClient.send(req);
			
			Calendar currentDateTime = Calendar.getInstance();
			Timestamp nowTS = new Timestamp(currentDateTime.getTimeInMillis());
			byte[] reportData = this.cls1131Service.generateJSONObjectForIdCardCheck(req, currentDateTime);
			/*
			 	1=國民身分證資料與檔存資料相符。 
			 	2=身分證字號 XXXXXXXXXX 目前驗證資料錯誤次數已達 1 次，今日錯誤累積達 3 次後，此身分證字號將無法查詢。 
			 	3=身分證字號 XXXXXXXXXX 目前驗證資料錯誤次數已達 2 次，今日錯誤累積達 3 次後，此身分證字號將無法查詢。 
			 	4=身分證字號 XXXXXXXXXX 目前驗證資料錯誤次數已達 3 次，今日錯誤累積達 3 次後，此身分證字號將無法查詢。 
			 	5=身分證字號 XXXXXXXXXX 驗證資料錯誤次數已達 3 次。今 日無法查詢，請明日再查！！ 
			 	6=您所查詢的國民身分證字號 XXXXXXXXXX 已停止使用。 
			 	7=您所查詢的國民身分證 XXXXXXXXXX，業依當事人申請登 錄掛失。 
			 	8=單一使用者出現異常使用情形，暫停使用者權限。
			*/
			String dataStatus = "1".equals(req.getCheckIdCardApply()) ? "0" : "1";
			C101S01S c101s01s = cls1131Service.modifyC101S01SForMixRecordData(mainId, c101m01a.getCustId(), c101m01a.getDupNo(), ClsConstants.C101S01S_dataType.行內_身分證驗證, dataStatus, reportData);
			if (c101s01s != null) {
				c101s01s.setCreateTime(nowTS);
				c101s01s.setUpdateTime(nowTS);
				c101s01s.setDataCreateTime(nowTS);
				clsService.save(c101s01s);
			}
		}
		else if(Util.equals(param_txId, CrsUtil.RPA_TXID_FA)){
			
			this.rpaProcessService.deleteBeforeQueryData(c101m01a.getMainId(), c101m01a.getCustId());
			this.rpaProcessService.gotoRPAJobs(new C101S04W(c101m01a.getMainId(), c101m01a.getCustId()));
		}
		else if(Util.equals(param_txId, CrsUtil.WiseNews)){

			JSONObject wiseNewsJson = this.wiseNewsService.getWiseNewsData(c101m01a.getCustName() ,c101m01a.getCustId(), c101m01a.getMainId());
			c101s01e.setWiseNews(wiseNewsJson.toString());
			c101s01e.setUpdateTime(CapDate.getCurrentTimestamp());
			clsService.save(c101s01e);

		} else if (Util.equals(param_txId, CrsUtil.EJ_TXID_B95)) {
			EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
			ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
			ejcicReq_ST.setMsgId(IDGenerator.getUUID());
			ejcicReq_ST.setQueryid(custId);
			ejcicReq_ST.setEmpid(userId);
			ejcicReq_ST.setEmpname(empname);
			ejcicReq_ST.setDeptid(deptid);
			ejcicReq_ST.setCbdeptid(cbdeptid);
			ejcicReq_ST.setBranchnm(deptnm);
			ejcicReq_ST.setPur(pur_A4A);
			if (true) {
				ejcicReq_ST.setProdid("");
				ejcicReq_ST.setTxid(txId);
				ejcicReq_ST.setQkey1(custId);// custId
				ejcicReq_ST.setQkey2("");
			}
			String url_B95 = Util
					.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST)); // 取得 url
			keep_url_htmloutput_to_c101s01u(c101m01a, txId, url_B95);
		}
		else if (Util.equals(param_txId, CrsUtil.EJ_TXID_B98)) {
			EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
			ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
			ejcicReq_ST.setMsgId(IDGenerator.getUUID());
			ejcicReq_ST.setQueryid(custId);
			ejcicReq_ST.setEmpid(userId);
			ejcicReq_ST.setEmpname(empname);
			ejcicReq_ST.setDeptid(deptid);
			ejcicReq_ST.setCbdeptid(cbdeptid);
			ejcicReq_ST.setBranchnm(deptnm);
			ejcicReq_ST.setPur(pur_A4A);
			if (true) {
				ejcicReq_ST.setProdid("");
				ejcicReq_ST.setTxid(txId);
				ejcicReq_ST.setQkey1(custId);// custId
				ejcicReq_ST.setQkey2("");
			}
			String url_B98 = Util
					.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST)); // 取得 url
			keep_url_htmloutput_to_c101s01u(c101m01a, txId, url_B98);
		}
		else {
			throw new CapMessageException("unknown txId[" + txId + "]",
					getClass());
		}
		result.set("txId", txId);
		result.set("isDone", "Y");
		return result;
	}

	private void keep_url_htmloutput_to_c101s01e(String txId,
			C101S01E c101s01e, String url) {
		InputStream in = null;
		String resp_html = "";
		try {
			in = new URL(url).openStream();
			StringWriter writer = new StringWriter();
			IOUtils.copy(in, writer, "Big5_HKSCS");
			resp_html = writer.toString();

			if (Util.equals(txId, CrsUtil.EJ_TXID_Z13)) {
				c101s01e.setZ13_html(resp_html);
				c101s01e.setZ13_qTime(CapDate.getCurrentTimestamp());
				clsService.daoSave(c101s01e);
			} else if (Util.equals(txId, CrsUtil.EJ_TXID_Z21)) {
				c101s01e.setZ21_html(resp_html);
				c101s01e.setZ21_qTime(CapDate.getCurrentTimestamp());
				clsService.daoSave(c101s01e);
			}
		} catch (IOException ioe) {
			logger.error("url={}, resp_html={}", url, resp_html);
			logger.error(StrUtils.getStackTrace(ioe));
		} catch (Exception e) {
			logger.error("url={}, resp_html={}", url, resp_html);
			logger.error(StrUtils.getStackTrace(e));
		} finally {
			IOUtils.closeQuietly(in);
		}
	}

	private void keep_url_htmloutput_to_c101s01u(C101M01A c101m01a,
			String txId, String url) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		InputStream in = null;
		String resp_html = "";
		try {
			in = new URL(url).openStream();
			StringWriter writer = new StringWriter();
			IOUtils.copy(in, writer, "Big5_HKSCS");
			resp_html = writer.toString();

			List<C101S01U> c101s01u_list = clsService.findC101S01U_txid(
					c101m01a.getMainId(), c101m01a.getCustId(),
					c101m01a.getDupNo(), txId);
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			C101S01U c101s01u = null;
			if (c101s01u_list.size() > 0) {
				c101s01u = c101s01u_list.get(0);
			} else {
				c101s01u = new C101S01U();
				c101s01u.setMainId(c101m01a.getMainId());
				c101s01u.setCustId(c101m01a.getCustId());
				c101s01u.setDupNo(c101m01a.getDupNo());
				c101s01u.setTxid(txId);
				c101s01u.setCreator(user.getUserId());
				c101s01u.setCreateTime(nowTS);
			}
			c101s01u.setHtmlData(resp_html);
			c101s01u.setSendTime(nowTS);
			c101s01u.setUpdater(user.getUserId());
			c101s01u.setUpdateTime(nowTS);
			clsService.daoSave(c101s01u);
		} catch (IOException ioe) {
			logger.error("url={}, resp_html={}", url, resp_html);
			logger.error(StrUtils.getStackTrace(ioe));
		} catch (Exception e) {
			logger.error("url={}, resp_html={}", url, resp_html);
			logger.error(StrUtils.getStackTrace(e));
		} finally {
			IOUtils.closeQuietly(in);
		}
	}

	/**
	 * @param user
	 * @param check_EJ_LN01
	 * @param check_EJ_PB01
	 * @param check_ETCH
	 * @throws CapMessageException
	 */
	private void _oneBtnQueryAuthChk(MegaSSOUserDetails user,
			boolean check_EJ_LN01, boolean check_when_Z21, boolean check_ETCH)
			throws CapMessageException {
		List<String> needRolesMsgList = new ArrayList<String>();
		if (check_EJ_LN01 && !user.isEJCICAuth()) {
			needRolesMsgList.add("需具有 EJ(聯徵查詢系統) LN01(授信查詢)權限");
		}
		// if(check_when_Z21 && !user.isEJCIC_PB01_Auth()){
		// needRolesMsgList.add("查詢 "+(CrsUtil.EJ_TXID_Z21+CrsUtil.EJ_TXID_Z21_DESC)+" 需具有 EJ(聯徵查詢系統) PB01(存款查詢)權限");
		// }
		if (check_when_Z21 && !user.isEJCICAuth()) { // 異動案
														// O-109-0109，程式修改申請(109)第(0886)號，Z21原核核PB01，改為具
														// LN01即可查詢
			needRolesMsgList.add("查詢 "
					+ (CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC)
					+ " 需具有 EJ(聯徵查詢系統) LN01(授信查詢)權限");
		}

		if (check_ETCH && !user.isETCHAuth()) {
			needRolesMsgList.add("需具有 ETCH(MQ票信查詢系統) ETCH01(票信查詢作業)權限");
		}
		// ====================
		if (true) {
			if (clsService.is_function_on_codetype("J-108-0277_sso_authChk")) {
			} else {
				// testing env, 因 Ejcic, Etch 的 API 會在收到 request,
				// 要對外送出查詢時［再一次］去檢核, 是否具有「對應權限」
				// 需請 10組先在 testing 的SSO環境, 把要測試的帳號{例如：006343, 006347, 008034},
				// 加入 LN01, PB01, ETCH01 的權限
				// 而在 本機localhost 執行時, 不確定應如何去設定 lms-ssoDummy.properties 內的內容
				// 即使模擬出具有 LN01, PB01, ETCH01 的權限(通過第1關檢核), 在送到10組的程式後,
				// 會仍執行失敗(被卡在第2關檢核)

				// 以下做法, 只能在本機localhost 開發測試時, 讓已具有 SSO權限的帳號, 能送出 request
				String userId = user.getUserId();
				Map<String, String> map = clsService.get_codeTypeWithOrder(
						"J-108-0277_test_sso_user", "zh_TW");
				if (map.containsKey(userId)) {
					needRolesMsgList = new ArrayList<String>();
				}
			}
		}
		if (needRolesMsgList.size() > 0) {
			throw new CapMessageException(StringUtils.join(needRolesMsgList,
					"<br/>"), getClass());
		}
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult test_dam001_dam003(PageParameters params)
			throws CapException {
		/*
		  	$.ajax({ handler: "cls1131formhandler", action:"test_dam001_dam003", data: {'custId':?, 'debug':'N'}, success:function(json){ } });
		 */
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		result.set("custId", custId);
		if(Util.equals("Y", "debug")){
			result.set("rtn_debug", new CapAjaxFormResult(ejcicService.get_DAM001_DAM003_relateData_debug(custId)));	
		}else{
			result.set("rtn_general", new CapAjaxFormResult(ejcicService.get_DAM001_DAM003_relateData(custId)));
		}
		
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult fix_laborLoan_c120s01b(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String mainId = Util.trim(params.getString("mainId"));
		String comName = Util.trim(params.getString("comName"));
		String comTel = Util.trim(params.getString("comTel"));
		String jobTitle = Util.trim(params.getString("jobTitle"));
		String seniority = Util.trim(params.getString("seniority"));
		String payAmt = Util.trim(params.getString("payAmt"));
		String othAmt = Util.trim(params.getString("othAmt"));
		C120S01B c120s01b = clsService.findC120S01B(mainId, custId, dupNo);
		if(c120s01b==null ){
			throw new CapMessageException(mainId+"_"+custId+"_"+dupNo+" not found", getClass());
		}else{
			c120s01b.setComName(comName);
			c120s01b.setComTel(comTel);
			c120s01b.setJobTitle(jobTitle);
			c120s01b.setSeniority(CrsUtil.parseBigDecimal(seniority));
			c120s01b.setPayAmt(CrsUtil.parseBigDecimal(payAmt));
			c120s01b.setOthAmt(CrsUtil.parseBigDecimal(othAmt));
			//~~~~~~~~~
			clsService.daoSave(c120s01b);
		}		
		
//		$.ajax({ handler: "cls1131formhandler", action:"fix_laborLoan_c120s01b", data: {'custId':'L257749598', 'dupNo':'1', mainId:?,'comName':'台灣佳能股份有限公司','comTel':'0425322123#4321','jobTitle':'d','seniority':'11','payAmt':'30','othAmt':'0'}, success:function(json){ } });
		/*
		 *  
		String sql = "update LMS.C120S01B set COMNAME='"+comName+"', COMTEL='"+comTel+"', jobTitle='"+jobTitle+"', seniority="+seniority+" , PAYAMT="+payAmt+", OTHAMT= "+othAmt +" ";
		sql	+=" where 1=1 ";
		if(Util.isNotEmpty(mainId)){
			sql	+="and mainid='"+mainId+"' ";	
		}		
		if(Util.isNotEmpty(custId)){
			sql	+="and custId='"+custId+"' ";
		}
		sql	+=" ";
		
		eloandbbaseservice.update(sql);*/
		return result;
	}
	
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult test_oneBtnQuery_auth(PageParameters params)
			throws CapException {
		/*
		 * $.ajax({ handler: "cls1131formhandler", action:
		 * "test_oneBtnQuery_auth", data: {'cyc_mn':'2018-12-01'}, success:
		 * function(json){ } });
		 */
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		result.set("LN01", user.isEJCICAuth() ? "O" : "X");
		result.set("PB01", user.isEJCIC_PB01_Auth() ? "O" : "X");
		result.set("ETCH01", user.isETCHAuth() ? "O" : "X");
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult get_callAPI_URL(PageParameters params)
			throws CapException, ClientProtocolException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String sysType = Util.trim(params.getString("sysType"));
		// ===============
		String prodId = Util.trim(params.getString("prodId"));
		String param_prodId = prodId;
		if (Util.equals(param_prodId, "P7_Prod69")) {
			prodId = "P7";
		}
		// ===============
		String txId = Util.trim(params.getString("txId"));
		String param_txId = txId;
		if (Util.equals(param_txId, "Z21_Prod69")) {
			txId = CrsUtil.EJ_TXID_Z21;
		} else if (Util.equals(param_txId, "Z13_Prod69")) {
			txId = CrsUtil.EJ_TXID_Z13;
		}
		// ===============
		String purpose_for_PACK = Util.trim(params
				.getString("purpose_for_PACK")); // 查詢目的{1:企業授信, 2:房屋貸款,
													// 3:消費性貸款, 4:留學生貸款}
		String isC120M01A = Util.trim(params.getString("isC120M01A"));

		C101M01A c101m01a = null;
		if (Util.equals("Y", isC120M01A)) {
			throw new CapMessageException("isC120M01A=Y", getClass());
		} else {
			c101m01a = clsService.findC101M01A_mainId(mainId);
		}

		if (c101m01a == null) {
			throw new CapMessageException(mainId + "_" + custId + "_" + dupNo
					+ " not found", getClass());
		}
		boolean naturalFlag = LMSUtil.check2(custId); // 是否為自然人
		if (naturalFlag == false) {
			throw new CapMessageException("限查詢自然人。輸入資料=" + custId, getClass());
		}
		C101S01A c101s01a = clsService.findC101S01A(c101m01a);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String userId = user.getUserId();
		// ================================
		boolean check_EJ_LN01 = true;
		boolean check_when_Z21 = false; // 把 Z21 放在「最前面」
		boolean check_ETCH = true;
		_oneBtnQueryAuthChk(user, check_EJ_LN01, check_when_Z21, check_ETCH);
		// ================================
		String empname = lmsService.getUserName(userId);
		String deptid = user.getUnitNo();
		String cbdeptid = "";
		String deptnm = "";
		IBranch iBranch = branchService.getBranch(deptid);
		if (iBranch != null) {
			deptnm = iBranch.getBrName();
		}
		
		cbdeptid = get_cbdeptid_totLen4_BrNo_ChkNo(get_cbdeptid_brNoLen3_for_PersonalLoan_callCenter(user));		
		if (Util.isEmpty(cbdeptid)) {
			throw new CapMessageException("cbdeptid=", getClass());
		}
		String pur_A4A = "A4A";
		String pur_A4G = "A4G";
		String callAPI_URL_ejcic_PACK = "";
		String callAPI_URL_ejcic_ST_Z21 = "";
		String callAPI_URL_ejcic_ST_Z13 = "";
		String callAPI_URL_ejcic_ST_B36 = "";
		String callAPI_URL_ejcic_ST_D10 = "";
		String callAPI_URL_ejcic_ST_R20 = "";
		String callAPI_URL_ejcic_ST_B29 = "";
		String callAPI_URL_ejcic_ST_B68 = "";
		String callAPI_URL_ejcic_ST_B33 = "";
		String callAPI_URL_etch = "";
		String resp_url = "";
		// ================================
		if (Util.equals("sysType_EJ", sysType)) {
			// 檢核是否 stop 連至外部系統
			verify_call_outerSys_EJ();

			if (Util.equals("P7_Prod69", param_prodId)
					&& Util.equals("P7", prodId)) {
				EJCICGwReqMessage ejcicReq_PACK = new EJCICGwReqMessage();
				ejcicReq_PACK.setSysId(UtilConstants.CaseSchema.個金);
				ejcicReq_PACK.setMsgId(IDGenerator.getUUID());
				ejcicReq_PACK.setQueryid(custId);
				ejcicReq_PACK.setEmpid(userId);
				ejcicReq_PACK.setEmpname(empname);
				ejcicReq_PACK.setDeptid(deptid);
				ejcicReq_PACK.setCbdeptid(cbdeptid);
				ejcicReq_PACK.setBranchnm(deptnm);
				ejcicReq_PACK.setPur(pur_A4G);
				if (true) {
					ejcicReq_PACK.setProdid(prodId);// input是 P7_Prod69
					ejcicReq_PACK.setPurpose(purpose_for_PACK);
				}
				callAPI_URL_ejcic_PACK = Util.trim(ejcicClient
						.get_callAPI_URL(ejcicReq_PACK));
				resp_url = callAPI_URL_ejcic_PACK;
			} else if (Util.equals("P7", prodId) || Util.equals("P9", prodId)) { // 組合查詢
				EJCICGwReqMessage ejcicReq_PACK = new EJCICGwReqMessage();
				ejcicReq_PACK.setSysId(UtilConstants.CaseSchema.個金);
				ejcicReq_PACK.setMsgId(IDGenerator.getUUID());
				ejcicReq_PACK.setQueryid(custId);
				ejcicReq_PACK.setEmpid(userId);
				ejcicReq_PACK.setEmpname(empname);
				ejcicReq_PACK.setDeptid(deptid);
				ejcicReq_PACK.setCbdeptid(cbdeptid);
				ejcicReq_PACK.setBranchnm(deptnm);
				ejcicReq_PACK.setPur(pur_A4A);
				if (true) {
					ejcicReq_PACK.setProdid(prodId);
					ejcicReq_PACK.setPurpose(purpose_for_PACK);
				}
				callAPI_URL_ejcic_PACK = Util.trim(ejcicClient
						.get_callAPI_URL(ejcicReq_PACK));
				resp_url = callAPI_URL_ejcic_PACK;
			} else { // 標準查詢
				if (Util.equals(CrsUtil.EJ_TXID_Z21, param_txId)
						|| Util.equals("Z21_Prod69", param_txId)) { // Z21身分證領補換資料查詢驗證
					String[] _Z21_qkey1_dataArr = get_Z21_qkey1_dataArr(c101s01a);
					validate_Z21_qkey1_dataArr(_Z21_qkey1_dataArr);
					// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
					EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
					ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
					ejcicReq_ST.setMsgId(IDGenerator.getUUID());
					ejcicReq_ST.setQueryid(custId);
					ejcicReq_ST.setEmpid(userId);
					ejcicReq_ST.setEmpname(empname);
					ejcicReq_ST.setDeptid(deptid);
					ejcicReq_ST.setCbdeptid(cbdeptid);
					ejcicReq_ST.setBranchnm(deptnm);
					ejcicReq_ST.setPur(pur_A4A);
					if (Util.equals(param_txId, "Z21_Prod69")) {
						ejcicReq_ST.setPur(pur_A4G);
					}
					if (true) {
						ejcicReq_ST.setProdid("");
						ejcicReq_ST.setTxid(txId);
						ejcicReq_ST.setQkey1(StringUtils
								.join(_Z21_qkey1_dataArr));// 由N個欄位組合
						ejcicReq_ST.setQkey2("");
					}
					callAPI_URL_ejcic_ST_Z21 = Util.trim(ejcicClient
							.get_callAPI_URL(ejcicReq_ST));
					resp_url = callAPI_URL_ejcic_ST_Z21;
				} else if (Util.equals(CrsUtil.EJ_TXID_Z13, param_txId)
						|| Util.equals("Z13_Prod69", param_txId)) { // Z13補充註記/消債條例信用註記資訊{受監護宣告/受輔助宣告}
					EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
					ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
					ejcicReq_ST.setMsgId(IDGenerator.getUUID());
					ejcicReq_ST.setQueryid(custId);
					ejcicReq_ST.setEmpid(userId);
					ejcicReq_ST.setEmpname(empname);
					ejcicReq_ST.setDeptid(deptid);
					ejcicReq_ST.setCbdeptid(cbdeptid);
					ejcicReq_ST.setBranchnm(deptnm);
					ejcicReq_ST.setPur(pur_A4A);
					if (Util.equals(param_txId, "Z13_Prod69")) {
						ejcicReq_ST.setPur(pur_A4G);
					}
					if (true) {
						ejcicReq_ST.setProdid("");
						ejcicReq_ST.setTxid(txId);
						ejcicReq_ST.setQkey1(custId);// custId
						ejcicReq_ST.setQkey2("");
					}
					callAPI_URL_ejcic_ST_Z13 = Util.trim(ejcicClient
							.get_callAPI_URL(ejcicReq_ST));
					resp_url = callAPI_URL_ejcic_ST_Z13;
				} else if (Util.equals(CrsUtil.EJ_TXID_B36, param_txId)) { // B36 新版授信(含票信)、擔保品、還款紀錄與保證資訊
						EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
						ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
						ejcicReq_ST.setMsgId(IDGenerator.getUUID());
						ejcicReq_ST.setQueryid(custId);
						ejcicReq_ST.setEmpid(userId);
						ejcicReq_ST.setEmpname(empname);
						ejcicReq_ST.setDeptid(deptid);
						ejcicReq_ST.setCbdeptid(cbdeptid);
						ejcicReq_ST.setBranchnm(deptnm);
						ejcicReq_ST.setPur(pur_A4G);
						if (true) {
						ejcicReq_ST.setProdid("");
						ejcicReq_ST.setTxid(txId);
						ejcicReq_ST.setQkey1(custId);// custId
						ejcicReq_ST.setQkey2("");
						}
						callAPI_URL_ejcic_ST_B36 = Util.trim(ejcicClient
						.get_callAPI_URL(ejcicReq_ST));
						resp_url = callAPI_URL_ejcic_ST_B36;
				} else if (Util.equals(CrsUtil.EJ_TXID_D10, param_txId)) { // D10
																			// 50萬元以下退票紀錄資訊
					EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
					ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
					ejcicReq_ST.setMsgId(IDGenerator.getUUID());
					ejcicReq_ST.setQueryid(custId);
					ejcicReq_ST.setEmpid(userId);
					ejcicReq_ST.setEmpname(empname);
					ejcicReq_ST.setDeptid(deptid);
					ejcicReq_ST.setCbdeptid(cbdeptid);
					ejcicReq_ST.setBranchnm(deptnm);
					ejcicReq_ST.setPur(pur_A4G);
					if (true) {
						ejcicReq_ST.setProdid("");
						ejcicReq_ST.setTxid(txId);
						ejcicReq_ST.setQkey1(custId);// custId
						ejcicReq_ST.setQkey2("");
					}
					callAPI_URL_ejcic_ST_D10 = Util.trim(ejcicClient
							.get_callAPI_URL(ejcicReq_ST));
					resp_url = callAPI_URL_ejcic_ST_D10;
				} else if (Util.equals(CrsUtil.EJ_TXID_R20, param_txId)) { // R20
													// 勞工保險投保/勞工退休金提繳資訊
					EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
					ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
					ejcicReq_ST.setMsgId(IDGenerator.getUUID());
					ejcicReq_ST.setQueryid(custId);
					ejcicReq_ST.setEmpid(userId);
					ejcicReq_ST.setEmpname(empname);
					ejcicReq_ST.setDeptid(deptid);
					ejcicReq_ST.setCbdeptid(cbdeptid);
					ejcicReq_ST.setBranchnm(deptnm);
					ejcicReq_ST.setPur(pur_A4G);
					if (true) {
						ejcicReq_ST.setProdid("");
						ejcicReq_ST.setTxid(txId);
						ejcicReq_ST.setQkey1(custId);// custId
						ejcicReq_ST.setQkey2("");
					}
					callAPI_URL_ejcic_ST_R20 = Util.trim(ejcicClient
							.get_callAPI_URL(ejcicReq_ST));
					resp_url = callAPI_URL_ejcic_ST_R20;
				} else {
					throw new CapMessageException("[param_prodId="
							+ param_prodId + "][prodId=" + prodId
							+ "][param_txId=" + param_txId + "][txId=" + txId
							+ "]", getClass());
				}
			}
		} else if (Util.equals("sysType_ETCH", sysType)) {
			// 檢核是否 stop 連至外部系統
			verify_call_outerSys_ETCH();

			ETCHGwReqMessage etchReq = new ETCHGwReqMessage();
			etchReq.setSysId(UtilConstants.CaseSchema.個金);
			etchReq.setMsgId(IDGenerator.getUUID());
			etchReq.setEmpid(userId);
			etchReq.setDeptid(deptid);
			etchReq.setProdid("4111"); // 4111,4114
			etchReq.setId(custId);
			etchReq.setCbdeptid(cbdeptid);
			etchReq.setReasonid("08"); // 08-其它作業前之客戶資料查詢
			etchReq.setEmpname(empname);
			etchReq.setDeptnm(deptnm);
			callAPI_URL_etch = etchClient.get_callAPI_URL(etchReq);
			resp_url = callAPI_URL_etch;
		}

		// ================================
		result.set("mainId", mainId);
		result.set("custId", custId);
		result.set("dupNo", dupNo);
		result.set("prodId", prodId);
		result.set("callAPI_URL_ejcic_PACK_" + prodId, callAPI_URL_ejcic_PACK);
		result.set("callAPI_URL_ejcic_ST_Z21", callAPI_URL_ejcic_ST_Z21);
		result.set("callAPI_URL_ejcic_ST_Z13", callAPI_URL_ejcic_ST_Z13);
		result.set("callAPI_URL_ejcic_ST_B36", callAPI_URL_ejcic_ST_B36);
		result.set("callAPI_URL_ejcic_ST_D10", callAPI_URL_ejcic_ST_D10);
		result.set("callAPI_URL_ejcic_ST_R20", callAPI_URL_ejcic_ST_R20);
		result.set("callAPI_URL_ejcic_ST_B29", callAPI_URL_ejcic_ST_B29);
		result.set("callAPI_URL_ejcic_ST_B68", callAPI_URL_ejcic_ST_B68);
		result.set("callAPI_URL_ejcic_ST_B33", callAPI_URL_ejcic_ST_B33);
		result.set("callAPI_URL_etch", callAPI_URL_etch);
		if (Util.isNotEmpty(resp_url)) {
			result.set("resp_url", resp_url);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkRecentEjQuery(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String prodId_now = Util.trim(params.getString("prodId")); //判斷點選的是[房貸P7]還是[信貸P9]
		if (true) {
			boolean naturalFlag = LMSUtil.check2(custId); // 是否為自然人
			if (naturalFlag == false) {
				throw new CapMessageException("限查詢自然人。輸入資料=" + custId,
						getClass());
			}
		}
		//檢查上一次查詢是P7還是P9，預設P7
		String prodId = ejcicService.get_cls_PRODID(custId);
//		String prodId = "P7";

		//從MIS.DATADATE查詢上一次的查詢紀錄
		List<Map<String, Object>> list = ejcicService.get_mis_datadate_records(
				custId, prodId);
		
		
		boolean newMessageType = false; 
		if (list.size() == 0) {
			String msg = "是否要以﹝查詢理由：新業務申請﹞發送聯徵查詢？ </br> 若本次交易非屬「新業務申請」，請勿使用「一鍵查詢功能」";
			result.set("msg", msg);
		}else{
			if(prodId_now.equals("P9")){ //信貸一件查詢 >> 適用新規則
				
				Map<String, Object> map = list.get(0); // 已依時間去 sort 過了
				TWNDate qDate = TWNDate.valueOf(MapUtils.getString(map, "QDATE"));
				String msg = "已於 " + TWNDate.toAD(qDate) + " 查詢過"+prodId+"，是否仍要以﹝查詢理由：新業務申請﹞重新發送聯徵查詢？ " +
						"</br> 若本次交易非屬「新業務申請」，請勿使用「一鍵查詢功能」";
				result.set("msg", msg);
				newMessageType = true;
			}else{ //點選為[房貸一鍵查詢] OR 上一次組合查詢為P9，維持原本作業模式
				Map<String, Object> map = list.get(0); // 已依時間去 sort 過了
				TWNDate qDate = TWNDate.valueOf(MapUtils.getString(map, "QDATE"));
				if (qDate != null) {
					if (LMSUtil.cmpDate(qDate, "==", CapDate.getCurrentTimestamp())) {
						// 同一天, 已查過一次
						
					} else {
						String msg = "已於 " + TWNDate.toAD(qDate) + " 查詢過聯徵，是否仍要以﹝查詢理由：新業務申請﹞發送聯徵查詢？" +
							"</br> 若本次交易非屬「新業務申請」，請勿使用「一鍵查詢功能」";
						result.set("msg", msg);
					}
				}
			}
		}
		result.set("newMessageType", newMessageType);
		
		
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkRecentEjQuery_Prod69(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		if (true) {
			boolean naturalFlag = LMSUtil.check2(custId); // 是否為自然人
			if (naturalFlag == false) {
				throw new CapMessageException("限查詢自然人。輸入資料=" + custId,
						getClass());
			}
		}
		String prodId = ejcicService.get_cls_PRODID(custId);

		List<Map<String, Object>> list = ejcicService.get_mis_datadate_records(
				custId, prodId);
		if (list.size() == 0) {
			// 首次查
			result.set("msg", "是否要以﹝查詢理由：G-辦理勞工紓困貸款﹞發送聯徵查詢？");
		} else {
			Map<String, Object> map = list.get(0); // 已依時間去 sort 過了
			TWNDate qDate = TWNDate.valueOf(MapUtils.getString(map, "QDATE"));
			if (qDate != null) {
				if (LMSUtil.cmpDate(qDate, "==", CapDate.getCurrentTimestamp())) {
					// 同一天, 已查過一次
				} else {
					result.set("msg", "已於 " + TWNDate.toAD(qDate)
							+ " 查詢過聯徵，是否仍要以﹝查詢理由：G-辦理勞工紓困貸款﹞發送聯徵查詢？");
				}
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult preActionOneBtnQuery(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (true) {
			boolean check_EJ_LN01 = true;
			boolean check_when_Z21 = false;
			boolean check_ETCH = true;
			_oneBtnQueryAuthChk(user, check_EJ_LN01, check_when_Z21, check_ETCH);
		}

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String birthday = Util.trim(params.getString("birthday"));
		String idCardIssueDate = Util.trim(params.getString("idCardIssueDate"));
		String idCard_siteId = Util.trim(params.getString("idCard_siteId"));
		String idCardChgFlag = Util.trim(params.getString("idCardChgFlag"));
		String idCardPhoto = Util.trim(params.getString("idCardPhoto"));
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		if (c101m01a == null) {
			throw new CapMessageException("mainId[" + mainId + "] not found",
					getClass());
		}

		if (true) {
			C101S01A c101s01a = clsService.findC101S01A(c101m01a);
			if (c101s01a == null) {
				c101s01a = new C101S01A();
				c101s01a.setMainId(c101m01a.getMainId());
				c101s01a.setCustId(c101m01a.getCustId());
				c101s01a.setDupNo(c101m01a.getDupNo());
			}
			c101s01a.setBirthday(CapDate.parseDate(birthday));
			// 將身分證的{發證日期, 發證地, 換補註記, 有無照片} 存檔
			c101s01a.setIdCardIssueDate(CapDate.parseDate(idCardIssueDate));
			c101s01a.setIdCard_siteId(idCard_siteId);
			c101s01a.setIdCardChgFlag(idCardChgFlag);
			c101s01a.setIdCardPhoto(idCardPhoto);
			clsService.daoSave(c101s01a);
		}
		// 檢核是否 stop 連至外部系統
		verify_call_outerSys();

		if (true) {
			C101S01E c101s01e = clsService.findC101S01E(c101m01a);
			if (c101s01e == null) {
				c101s01e = new C101S01E();
				c101s01e.setMainId(c101m01a.getMainId());
				c101s01e.setCustId(c101m01a.getCustId());
				c101s01e.setDupNo(c101m01a.getDupNo());
			}
			c101s01e.setOneBtnQ_qTime(CapDate.getCurrentTimestamp());
			clsService.daoSave(c101s01e);
		}

		return result;
	}

	/**
	 * 目前，若發送 P9 查詢，會在 mis.CPXQueryLog 裡有 HZ13的資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult sync_ej_data_to_C101S01E(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		if (c101m01a == null) {
			throw new CapMessageException("mainId[" + mainId + "] not found",
					getClass());
		}
		String sync_result = cls1131Service.sync_ej_data_to_C101S01E(c101m01a);
		result.set("sync_result", sync_result);

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getOneBtnQuery(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		if (c101m01a == null) {
			throw new CapMessageException("mainId[" + mainId + "] not found",
					getClass());
		}

		C101S01E c101s01e = clsService.findC101S01E(c101m01a);
		String custId = c101m01a.getCustId();
		String dupNo = c101m01a.getDupNo();
		if (c101s01e != null && c101s01e.getOneBtnQ_qTime() != null) {
			// (1) C101S01E 裡有 Z13, Z21
			cls1131Service.sync_ej_data_to_C101S01E(c101m01a);

			// (2) C101S01H（來源： EjcicDB 裡的 mis.CPXQueryLog） 裡可能會有 P9 的Z13
			// (3) C101S01I（來源： EtchDB）
			String prodId = ejcicService.get_cls_PRODID(custId);
			// ~~~
			List<GenericBean> c101_list = new ArrayList<GenericBean>();
			c101_list.addAll(cls1131Service.getHtml(c101m01a.getMainId(),
					c101m01a.getCustId(), c101m01a.getDupNo(), prodId));
			if (c101_list.size() > 0) {
				cls1131Service.save(mainId, custId, dupNo, c101_list,
						C101S01H.class, C101S01I.class);
			}
			if(prodId.equals("P9")){ //J-111-0602消金徵審優化
				//如果本來是由P7+各子項目的查詢，資料將分別存在C101S01H、C101S01U，因此，若重新查詢為P9就要清掉C101S01U的資料
				List<C101S01U> c101s01_list = clsService.findC101S01U_txid_sendTimeBefore(mainId, new String[]{CrsUtil.EJ_TXID_B29, CrsUtil.EJ_TXID_B33, CrsUtil.EJ_TXID_B68, "HJ10"},null);
				clsService.delC101S01U(c101s01_list);
			}
		} else {
			// 若 OneBtnQ_qTime 為 null，表示從未按過 => 沒有 sync data 的必要
			throw new CapMessageException("尚未執行「一鍵查詢」", getClass());
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult clearAmlRelateDataForProd69(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		if (c101m01a == null) {
			throw new CapMessageException("mainId[" + mainId + "] not found",
					getClass());
		}

		C101S01E c101s01e = clsService.findC101S01E(c101m01a);
		C101S01J c101s01j = clsService.findC101S01J(c101m01a);
		// String custId = c101m01a.getCustId();
		// String dupNo = c101m01a.getDupNo();
		if (c101s01e != null) {
			c101s01e.setIsQdata7("3");// N.A.
			cls1131Service.save(c101s01e);
		}

		if (c101s01j != null) {

			String amlRefNo = Util.trim(c101s01j.getAmlRefNo());
			String amlRefOid = Util.trim(c101s01j.getAmlRefOid());

			L120S09B l120s09b = clsService.findL120S09B_refNo_or_oid(amlRefNo,
					amlRefOid);
			L120S09A l120s09a = clsService.findL120S09A_cls1131(l120s09b);

			if (l120s09b != null) {
				l120s09b.setUniqueKey("");
				l120s09b.setNcResult("");
				l120s09b.setQueryDateS(null);
				clsService.save(l120s09b);
			}

			if (l120s09a != null) {

				l120s09a.setBlackListCode("");
				l120s09a.setQueryDateS(null);
				l120s09a.setMemo("");
				l120s09a.setCm1AmlStatus("");
				l120s09a.setLuvRiskLevel("");
				clsService.save(l120s09a);
			}

			// c101s01j.setAmlRefOid("");
			c101s01j.setBlackRecQry(null);
			c101s01j.setAns1("");

			cls1131Service.save(c101s01j);
		}

		return result;
	}
	/**
	 * 刪除申請資料核對表資料
	 * J-109-0273_10702_B1002 Web e-Loan 申請資料核對表分版本，一律刪除後重新新增
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteCheckList(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		List<GenericBean> list = new ArrayList<GenericBean>();
		List<C101S01V> c101s01v_list = cls1131Service.findC101S01VByMainid(mainId,null,custId,dupNo);
		for (C101S01V model : c101s01v_list) {
			list.add(model);
		}
		// 刪除
		cls1131Service.delete(list);

		return result;
	}// ;
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult isOpenSusHeadAccountAddColumn(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("isOpen", "Y".equals(this.lmsService.getSysParamDataValue("IS_CHECK_SUS_HEAD_ACCOUNT_FUN")) ? "Y" : "N");
		return result;
	}

	/**
	 * 儲存經常性/非經常性收入
	 *
	 * @param params
	 * 
	 * @return
	 * 
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult savePersonalIncomeDetail(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C101S01B c101s01b = cls1131Service.findModelByKey(C101S01B.class, mainId, custId, dupNo);

		if (c101s01b != null) {

			//清除經常性收入，非經常性收入值，設為null
			this.resetPersonalIncomeDetail(c101s01b);

			String positionType = params.getString("positionType");
			String mainIncomeType = params.getString("mainIncomeType");

			c101s01b.setPositionType(positionType);
			c101s01b.setMainIncomeType(mainIncomeType);

			if ("A".equals(mainIncomeType)) {

				// 1.非藍領　2.藍領才可以輸入 [ 所得清單/扣繳憑單] 來源
				if ("1".equals(positionType) || "2".equals(positionType)) {
					String itemAvalue = params.getString("itemAvalue");
					c101s01b.setItemAvalue(CapMath.getBigDecimal(itemAvalue));
				}
			} else if ("B1".equals(mainIncomeType)) {

				String itemB1value1 = params.getString("itemB1value1");
				String itemB1value2 = params.getString("itemB1value2");
				String itemB1value3 = params.getString("itemB1value3");
				String itemB1value4 = params.getString("itemB1value4");
				String itemB1value5 = params.getString("itemB1value5");
				String itemB1value6 = params.getString("itemB1value6");
				String itemB1HolidayBonus = params.getString("itemB1HolidayBonus");
				String itemB1YearEndBonus = params.getString("itemB1YearEndBonus");

				c101s01b.setItemB1value1(NumberUtils.isNumber(itemB1value1) ? new BigDecimal(itemB1value1) : null);
				c101s01b.setItemB1value2(NumberUtils.isNumber(itemB1value2) ? new BigDecimal(itemB1value2) : null);
				c101s01b.setItemB1value3(NumberUtils.isNumber(itemB1value3) ? new BigDecimal(itemB1value3) : null);

				if ("3".equals(positionType)) {
					c101s01b.setItemB1value4(NumberUtils.isNumber(itemB1value4) ? new BigDecimal(itemB1value4) : null);
					c101s01b.setItemB1value5(NumberUtils.isNumber(itemB1value5) ? new BigDecimal(itemB1value5) : null);
					c101s01b.setItemB1value6(NumberUtils.isNumber(itemB1value6) ? new BigDecimal(itemB1value6) : null);
				}
				c101s01b.setItemB1HolidayBonus(
						NumberUtils.isNumber(itemB1HolidayBonus) ? new BigDecimal(itemB1HolidayBonus) : null);
				c101s01b.setItemB1YearEndBonus(
						NumberUtils.isNumber(itemB1YearEndBonus) ? new BigDecimal(itemB1YearEndBonus) : null);

			} else if ("B2".equals(mainIncomeType)) {
				// 1.非藍領　2.藍領才可以輸入 [ 勞保投保明細/個人投保紀錄 ] 來源
				if ("1".equals(positionType) || "2".equals(positionType)) {
					String itemB2value = params.getString("itemB2value");
					c101s01b.setItemB2value(CapMath.getBigDecimal(itemB2value));
				}
			} else if ("B3".equals(mainIncomeType)) {
				String itemB3ReportType = params.getString("itemB3ReportType");
				c101s01b.setItemB3ReportType(itemB3ReportType);
				String itemB3value1 = params.getString("itemB3value1");
				String itemB3value2 = params.getString("itemB3value2");
				String itemB3value3 = params.getString("itemB3value3");
				//String itemB3value4 = params.getString("itemB3value4");
				//String itemB3value5 = params.getString("itemB3value5");
				//String itemB3value6 = params.getString("itemB3value6");
				String itemB3InProfit = params.getString("itemB3InProfit");
				String itemB3Holding = params.getString("itemB3Holding");

				c101s01b.setItemB3value1(NumberUtils.isNumber(itemB3value1) ? new BigDecimal(itemB3value1) : null);
				c101s01b.setItemB3value2(NumberUtils.isNumber(itemB3value2) ? new BigDecimal(itemB3value2) : null);
				c101s01b.setItemB3value3(NumberUtils.isNumber(itemB3value3) ? new BigDecimal(itemB3value3) : null);

				c101s01b.setItemB3InProfit(CapMath.getBigDecimal(itemB3InProfit));
				c101s01b.setItemB3Holding(CapMath.getBigDecimal(itemB3Holding));

			} else if ("B4".equals(mainIncomeType)) {

				String[] b4Columns = new String[] { "itemB4value1", "itemB4value2", "itemB4value3", "itemB4value4",
						"itemB4value5", "itemB4value6", "itemB4DisRate" };

				for (String column : b4Columns) {
					String itemB4value = params.getString(column);
					c101s01b.set(column, NumberUtils.isNumber(itemB4value) ? new BigDecimal(itemB4value) : null);
				}
			}

			String[] loops = new String[] { "C1", "C2", "C3", "D4", "D5", "D6", "D7", "D8", "D9" };
			for (String loop : loops) {
				String value = params.getString("other" + loop);

				if ("C1".equals(loop)) {
					//若前面選擇「A 所得清單/扣繳憑單」，則此欄不開放key in
					if ("A".equals(mainIncomeType)) {
						continue;
					}
				}

				if ("Y".equals(value)) {
					String loopValue1 = params.getString("item" + loop + "value1");
					String loopValue2 = params.getString("item" + loop + "value2");
					String loopValue3 = params.getString("item" + loop + "value3");
					String loopValue4 = params.getString("item" + loop + "value4");
					String loopValue5 = params.getString("item" + loop + "value5");
					String loopValue6 = params.getString("item" + loop + "value6");
					c101s01b.set("other" + loop, "Y");
					c101s01b.set("item" + loop + "value1",
							NumberUtils.isNumber(loopValue1) ? new BigDecimal(loopValue1) : null);
					c101s01b.set("item" + loop + "value2",
							NumberUtils.isNumber(loopValue2) ? new BigDecimal(loopValue2) : null);
					c101s01b.set("item" + loop + "value3",
							NumberUtils.isNumber(loopValue3) ? new BigDecimal(loopValue3) : null);
					c101s01b.set("item" + loop + "value4",
							NumberUtils.isNumber(loopValue4) ? new BigDecimal(loopValue4) : null);
					c101s01b.set("item" + loop + "value5",
							NumberUtils.isNumber(loopValue5) ? new BigDecimal(loopValue5) : null);
					c101s01b.set("item" + loop + "value6",
							NumberUtils.isNumber(loopValue6) ? new BigDecimal(loopValue6) : null);

					if ("D9".equals(loop)) {
						String itemD9DisRate = params.getString("itemD9DisRate");
						c101s01b.setItemD9DisRate(NumberUtils.isNumber(itemD9DisRate) ? new BigDecimal(itemD9DisRate) : null);
					}
				}
			}

			cls1131Service.calPersonalIncomeDetail(c101s01b);
			
			result.putAll((CapAjaxFormResult) getPersonalIncomeDetail(params));
			result.set("payAmt", c101s01b.getPayAmt());
			result.set("othAmt", c101s01b.getOthAmt());
		}

		return result;
	}

	/**
	 * 儲存經常性/非經常性收入
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult savePersonalIncomeDetailV1(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C101S01B c101s01b = cls1131Service.findModelByKey(C101S01B.class, mainId, custId, dupNo);

		if (c101s01b != null) {
			String c101S01B_incomeDetailVer = sysparamService.getParamValue("C101S01B_incomeDetailVer");

			c101s01b.setIncomeDetailVer(CapMath.getBigDecimal(c101S01B_incomeDetailVer));

			//清除經常性收入，非經常性收入值，設為null(這是舊版的欄位)
			//以後新版的個人收入明細，統一都使用colume-table的方式，塞到c101s01w
			this.resetPersonalIncomeDetail(c101s01b);

			JSONObject jsObject = new JSONObject();
			String has1year = params.getString("has1year");
			String positionType = params.getString("positionType");
			String salaryStructure = params.getString("salaryStructure");
			jsObject.put("has1year", has1year);
			jsObject.put("positionType", positionType);
			jsObject.put("salaryStructure", salaryStructure);

			if("1".equals(salaryStructure)){
				if(!"1".equals(positionType) && !"2".equals(positionType)){
					throw new CapMessageException("固定薪只可選擇職位別為「非藍領 或 藍領」", this.getClass());
				}
			} else if ("2".equals(salaryStructure)){
				if(!"3".equals(positionType)){
					throw new CapMessageException("業務職只可選擇職位別為「業務職(底薪+獎金者)」", this.getClass());
				}
			}

			// 因為決策系統有用到這爛位，所以還是要塞回去
			c101s01b.setPositionType(positionType);

			String[] loopItems = new String[] { "A", "B", "C", "D", "E", "F", "G", "H" };

			for (String loopItem : loopItems) {
				String hasItem = params.getString("hasItem" + loopItem);
				jsObject.put("hasItem" + loopItem, hasItem);

				if ("A".equals(loopItem) && "Y".equals(hasItem)) {
					jsObject.put("itemAvalue", params.getString("itemAvalue"));
				} else if ("B".equals(loopItem) && "Y".equals(hasItem)) {
					int base = 12;
					if (!"Y".equals(has1year)) {
						// 年資未滿一年，固定薪看3個月，業務職看6個月
						base = "1".equals(positionType) ? 3 : 6;
					}
					for (int i = 1; i <= base; i++) {
						jsObject.put("itemBvalue" + i, params.getString("itemBvalue" + i));
					}
					jsObject.put("itemBRegularBonus", params.getString("itemBRegularBonus"));

				} else if ("C".equals(loopItem) && "Y".equals(hasItem)) {
					for (int i = 1; i <= 12; i++) {
						jsObject.put("itemCvalue" + i, params.getString("itemCvalue" + i));
					}
					jsObject.put("itemCInProfit", params.getString("itemCInProfit"));
					jsObject.put("itemCHolding", params.getString("itemCHolding"));
					jsObject.put("itemCReportType", params.getString("itemCReportType"));
				} else if ("D".equals(loopItem) && "Y".equals(hasItem)) {
					for (int i = 1; i <= 12; i++) {
						jsObject.put("itemDvalue" + i, params.getString("itemDvalue" + i));
					}
					jsObject.put("itemDDisRate", params.getString("itemDDisRate"));
				} else if ("E".equals(loopItem) && "Y".equals(hasItem)) {
					for (int i = 1; i <= 12; i++) {
						jsObject.put("itemEvalue" + i, params.getString("itemEvalue" + i));
					}
				} else if ("F".equals(loopItem) && "Y".equals(hasItem)) {
					for (int i = 1; i <= 12; i++) {
						jsObject.put("itemFvalue" + i, params.getString("itemFvalue" + i));
					}
				} else if ("G".equals(loopItem) && "Y".equals(hasItem)) {
					for (int i = 1; i <= 12; i++) {
						jsObject.put("itemGvalue" + i, params.getString("itemGvalue" + i));
					}
				} else if ("H".equals(loopItem) && "Y".equals(hasItem)) {
					for (int i = 1; i <= 12; i++) {
						jsObject.put("itemHvalue" + i, params.getString("itemHvalue" + i));
					}
					jsObject.put("itemHDisRate", params.getString("itemHDisRate"));
				}
			}
			cls1131Service.calPersonalIncomeDetailV1(c101s01b, jsObject);

			result.putAll((CapAjaxFormResult) getPersonalIncomeDetailWithVersion(params));
			result.set("payAmt", c101s01b.getPayAmt());
			result.set("othAmt", c101s01b.getOthAmt());
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPersonalIncomeDetail(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C101S01B c101s01b = cls1131Service.findModelByKey(C101S01B.class, mainId, custId, dupNo);

		if (c101s01b != null) {
			result.add(new CapAjaxFormResult(
					c101s01b.toJSONObject(cls1131Service.getPersonalIncomeDetailColumns(), null)));
		}
		return result;
	}

	/**
	 * 取得新版本的個人收入版本version
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPersonalIncomeVersion(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		Boolean isC120M01A = params.getBoolean("isC120M01A");

		//int systemVersion = Util.parseInt(sysparamService.getParamValue("personalIncomeVersion"));
		int systemVersion = Util.parseInt(sysparamService.getParamValue("C101S01B_incomeDetailVer"));
		C101S01B c101s01b = cls1131Service.findModelByKey(C101S01B.class, mainId, custId, dupNo);

		if (isC120M01A) {
			result.set("incomeVersion", c101s01b.getIncomeDetailVer() == null ? 0 : c101s01b.getIncomeDetailVer().intValue());
			result.set("needReset", false);
		} else {
			boolean needReset = false;

			BigDecimal incomeDetailVersion = c101s01b.getIncomeDetailVer();
			int cVersion = incomeDetailVersion == null ? 0 : incomeDetailVersion.intValue();
			if (cVersion != systemVersion) {
				needReset = true;
			}
			result.set("incomeVersion", systemVersion);
			result.set("needReset", needReset);

		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPersonalIncomeFormula(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String incomeItem = Util.trim(params.getString("incomeItem"));
		String actuallyIncomeSum = Util.trim(params.getString("actuallyIncomeSum"));

		CodeType c101s01w_incomeItem = codeTypeService.findByCodeTypeAndCodeValue("c101s01w_incomeItem", incomeItem+actuallyIncomeSum, "zh_TW");

		if (c101s01w_incomeItem != null) {
			String formula = Util.trim(c101s01w_incomeItem.getCodeDesc2());
			result.set("formula",formula);
		}
		return result;
	}

	/**
	 * 儲存收收明細，從v2版開始，明細c101s01b與c101s01w關係為1對多
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult savePersonalIncomeByFormula(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String sOid = Util.trim(params.getString("sOid"));

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		String positionType = params.getString("positionType");

		C101S01B c101s01b = cls1131Service.findModelByKey(C101S01B.class, mainId, custId, dupNo);
		String c101S01B_incomeDetailVer = sysparamService.getParamValue("C101S01B_incomeDetailVer");
		//清除經常性收入，非經常性收入值，設為null(這是舊版的欄位)
		//以後新版的個人收入明細，統一都使用colume-table的方式，塞到c101s01w
		this.resetPersonalIncomeDetail(c101s01b);

		c101s01b.setIncomeDetailVer(CapMath.getBigDecimal(c101S01B_incomeDetailVer));
		// 因為決策系統有用到這爛位，所以還是要塞回去
		c101s01b.setPositionType(positionType);

		String incomeViewForm = Util.trim(params.getString("incomeViewForm"));
		JSONObject inputJson = JSONObject.fromObject(incomeViewForm);

		//收入項目對應的公式及說明定義在codetype裡面
		CodeType c101s01w_incomeItem = codeTypeService.findByCodeTypeAndCodeValue("c101s01w_incomeItem", inputJson.getString("incomeItem")+inputJson.getString("actuallyIncomeSum"), "zh_TW");

		C101S01W c101s01w = null;
		if (Util.isEmpty(sOid)) {
			c101s01w = new C101S01W();
		} else {
			c101s01w = cls1131Service.findModelByOid(C101S01W.class, sOid);
		}

		String[] arrs = new String[]{"value01", "value02", "value03", "value04",
				"value05", "value06", "value07", "value08", "value09", "value10",
				"value11", "value12", "bonus", "inProfit", "disRate", "holding", "valueYear"};
		// 將資料和前端畫面欄位做處理
		for (String arr : arrs) {
			inputJson.put(arr, inputJson.optString(c101s01w_incomeItem.getCodeDesc2() + "_" + arr));
		}

		DataParse.toBean(inputJson, c101s01w);
		c101s01w.setMainId(mainId);
		c101s01w.setCustId(custId);
		c101s01w.setDupNo(dupNo);
		c101s01w.setFormulaType(c101s01w_incomeItem.getCodeDesc2().replaceAll("formula_", ""));


		// 檢核職業別和收入項目的對應
		boolean check = false;
		String incomeItem = c101s01w.getIncomeItem();
		if ("3".equals(positionType)) {
			if ("B01".equals(incomeItem) || "B04".equals(incomeItem)) {
				check = true;
			}
		} else {
			if ("B02".equals(incomeItem) || "B05".equals(incomeItem)) {
				check = true;
			}
		}
		if (check) {
			throw new CapMessageException("收入項目和職位別不相符。", this.getClass());
		}

		cls1131Service.calPersonalIncomeItem(c101s01w);

		String checkIncomeItemWithPosition = this.checkIncomeItemWithPosition(positionType, c101s01b);
		if (Util.isNotEmpty(checkIncomeItemWithPosition)) {
			result.set("warnMsg", checkIncomeItemWithPosition);
		}

		cls1131Service.calPersonalIncomeDetailV2(c101s01b);
		result.set("formula_" + Util.trim(c101s01w.getFormulaType()) + "_valueYear", c101s01w.getValueYear());
		result.set("sOid", c101s01w.getOid());
		result.set("payAmt", c101s01b.getPayAmt());
		result.set("othAmt", c101s01b.getOthAmt());

		return result;
	}

	private String checkIncomeItemWithPosition(String positionType, C101S01B s01b){
		List<C101S01W> c101s01ws = clsService.findC101S01W(s01b.getMainId(), s01b.getCustId(), s01b.getDupNo());
		String check = "";
		for (C101S01W c101s01w : c101s01ws) {
			String incomeItem = c101s01w.getIncomeItem();
			if ("3".equals(positionType)) {
				if ("B01".equals(incomeItem) || "B04".equals(incomeItem)) {
					check = "收入項目 " + incomeItem + " 和職位別不一致，系統將自動刪除";
					cls1131Service.delete(c101s01w);
				}
			} else {
				if ("B02".equals(incomeItem) || "B05".equals(incomeItem)) {
					check = "收入項目 " + incomeItem + " 和職位別不一致，系統將自動刪除";
					cls1131Service.delete(c101s01w);
				}
			}
		}
		return check;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult removePersonalIncomeByFormula(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String sOid = Util.trim(params.getString("sOid"));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C101S01B c101s01b = cls1131Service.findModelByKey(C101S01B.class, mainId, custId, dupNo);

		C101S01W c101s01w = cls1131Service.findModelByOid(C101S01W.class, sOid);
		if (c101s01w != null) {
			cls1131Service.delete(c101s01w);
		}
		cls1131Service.calPersonalIncomeDetailV2(c101s01b);
		result.set("payAmt", c101s01b.getPayAmt());
		result.set("othAmt", c101s01b.getOthAmt());

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPersonalIncomeByFormula(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String sOid = Util.trim(params.getString("sOid"));

		C101S01W c101s01w = cls1131Service.findModelByOid(C101S01W.class, sOid);

		String[] arrs = new String[]{"value01", "value02", "value03", "value04",
				"value05", "value06", "value07", "value08", "value09", "value10",
				"value11", "value12","bonus","inProfit","disRate","holding","valueYear"};

		String formulaType = Util.trim(c101s01w.getFormulaType());
		JSONObject jsonObject = DataParse.toJSON(c101s01w);
		for (String arr : arrs) {
			jsonObject.put("formula_" + formulaType + "_" + arr, jsonObject.optString(arr));
		}

		result.set("c101s01w", new CapAjaxFormResult(jsonObject));

		return result;
	}

	/**
	 * 儲存經常性/非經常性收入
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult savePersonalIncomeDetailV2(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C101S01B c101s01b = cls1131Service.findModelByKey(C101S01B.class, mainId, custId, dupNo);

		if (c101s01b != null) {
			String c101S01B_incomeDetailVer = sysparamService.getParamValue("C101S01B_incomeDetailVer");
			//清除經常性收入，非經常性收入值，設為null(這是舊版的欄位)
			//以後新版的個人收入明細，統一都使用colume-table的方式，塞到c101s01w
			this.resetPersonalIncomeDetail(c101s01b);
			c101s01b.setIncomeDetailVer(CapMath.getBigDecimal(c101S01B_incomeDetailVer));

			String positionType = params.getString("positionType");
			// 因為決策系統有用到這爛位，所以還是要塞回去
			c101s01b.setPositionType(positionType);

			String checkIncomeItemWithPosition = this.checkIncomeItemWithPosition(positionType, c101s01b);
			if (Util.isNotEmpty(checkIncomeItemWithPosition)) {
				result.set("warnMsg", checkIncomeItemWithPosition);
			}
			cls1131Service.calPersonalIncomeDetailV2(c101s01b);
			result.set("payAmt", c101s01b.getPayAmt());
			result.set("othAmt", c101s01b.getOthAmt());
		}

		return result;
	}

	/**
	 * 取得新版收入明細，使用column-table的方式儲存
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPersonalIncomeDetailWithVersion(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));


		List<C101S01W> c101s01ws = (List<C101S01W>) cls1131Service.findListByRelationKey(C101S01W.class, mainId, custId, dupNo);
		if (CollectionUtils.isNotEmpty(c101s01ws)) {
			for (C101S01W w : c101s01ws) {
				String keyString = Util.trim(w.getKeyString());
				String valueString = Util.trim(w.getValueString());
				result.set(keyString, valueString);
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPersonalIncomeDetailWithVersionExt(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C101S01B c101s01b = cls1131Service.findModelByKey(C101S01B.class, mainId, custId, dupNo);
		if (c101s01b != null) {
			result.set("positionType", Util.trim(c101s01b.getPositionType()));
		}
		return result;
	}

	/**
	 * 將經常性，非常性收入欄位清空
	 * @param s01b
	 * @throws CapException
	 */
	private void resetPersonalIncomeDetail(C101S01B s01b) throws CapException {

		for (String column : cls1131Service.getPersonalIncomeDetailColumns()) {
			s01b.set(column, null);
		}

	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult defaultParam_OTS_TRPAYLG(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Date nowDate = CapDate.getCurrentTimestamp();
		/*
		  前端頁面
		 ● 年資滿一年＝Y，要輸入12個月
		 ● 年資滿一年＝N，薪資結構=固定薪，要輸入3個月
		 ● 年資滿一年＝N，薪資結構=業務職，要輸入6個月
		*/
		int itemBvalue_cnt = params.getInt("itemBvalue_cnt", 12);
		result.set("begDate", StringUtils.substring(TWNDate.toAD(CapDate.addMonth(nowDate, -1*itemBvalue_cnt)), 0, 7)+"-01");
		result.set("endDate", TWNDate.toAD(nowDate));
		return result;
	}
	
	//J-111-0128 Web e-Loan消金徵信「薪轉戶年薪查詢」功能增加寫入個資查詢及交易記錄(會在 LLDCMILG 出表)
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult log_OTS_TRPAYLG(PageParameters params) throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		Date begDate = TWNDate.valueOf(Util.trim(params.getString("trpaylg_begDate")));
		Date endDate = TWNDate.valueOf(Util.trim(params.getString("trpaylg_endDate")));
		String inqcode = Util.trim(params.getString("inqcode", "08"));
		
		if(begDate==null){
			throw new CapMessageException("查詢起日錯誤", getClass());
		}
		if(endDate==null){
				throw new CapMessageException("查詢迄日錯誤", getClass());
		}
		if(LMSUtil.cmpDate(CapDate.shiftDays(begDate, 365+90), "<", endDate)){
			throw new CapMessageException("查詢起迄日間隔不可超過一年", getClass());
		}
		if(Util.isEmpty(custId)){
			throw new CapMessageException("查詢ID錯誤", getClass());
		}
		String str_begDate = TWNDate.toAD(begDate);
		String str_endDate = TWNDate.toAD(endDate);
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("host", "DW");
		jsonObject.put("ID", custId);
		jsonObject.put("Period", str_begDate+" ~ "+str_endDate);
		
		String user_charset = "Big5";
		if(true){
			user_charset = sysparamService.getParamValue(SysParamConstants.DWFTP_ENCODING);			
		}
		String line = _getInqReason(user_charset, inqcode, "個金徵信╱薪轉戶年薪查詢", "", jsonObject.toString());
		//比照 I-101-0215 TCSIQLG 個資查詢及交易記錄，在 LLDCMILG 出表
		ClsUtility.writeToLog("/elnfs/COM/900/QueryReason.log", line, user_charset);
		return result;
	}
	
	private String _getInqReason(String encoding, String inqcode, String src_txnName, String src_inqreason, String queryObject) 
	throws UnsupportedEncodingException {
		MegaSSOUserDetails megaSSOUserDetails = MegaSSOSecurityContext
				.getUserDetails();

		int maxObjLen = 1925;
		StringBuffer s = new StringBuffer();
		SimpleDateFormat dateformat = new SimpleDateFormat("yyyyMMddHHmmss");

		// TCSIQLG-TXN-DATE TCSIQLG-TXN-TIME
		s.append(dateformat.format(new java.util.Date()));
		// TCSIQLG-TXN-BR
		s.append(megaSSOUserDetails.getSsoUnitNo());// megaSSOUserDetails.getUnitNo()
		// TCSIQLG-RUN-STAGE 09.ELOAN
		s.append("09");
		// TCSIQLG-DEPT
		s.append("EL");
		// TCSIQLG-EMP-NO 交易櫃員
		s.append(megaSSOUserDetails.getUserId());
		// TCSIQLG-SUP-NO 覆核主管
		s.append(CapString.fillBlankTail(CapConstants.EMPTY_STRING, 6));
		// TCSIQLG-TRM-ID
		s.append(CapString.fillBlankTail(megaSSOUserDetails.getLoginIP(), 20));
		// TCSIQLG-TXN-CODE 交易代號
		String className = getClass().getName();
		int pos = className.lastIndexOf(".") + 1;
		s.append(className.substring(pos, pos + 10));
		// TCSIQLG-TXN-FUNC
		s.append(CapString.fillBlankTail(CapConstants.EMPTY_STRING, 8));
		// TCSIQLG-PGM
		s.append(className.substring(pos, pos + 10));
		// TCSIQLG-INQ-CODE
		s.append(inqcode);
		// TCSIQLG-USER-ID
		s.append(CapString.fillBlankTail(CapConstants.EMPTY_STRING, 10));
		// TCSIQLG-FILLER
		s.append(CapString.fillBlankTail(CapConstants.EMPTY_STRING, 17));
		// TCSIQLG-OBJ-LEN
		byte[] cond = null;
		try {
			cond = queryObject.toString().getBytes(encoding);
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		s.append(CapString.fillZeroHead(String
				.valueOf((cond.length > maxObjLen ? maxObjLen : cond.length)),
				4));
		// TCSIQLG-TXN-NAME
		String txnName = CapString.halfWidthToFullWidth(src_txnName);
//		s.append(txnName).append(
//				CapString.fillBlankTail(CapConstants.EMPTY_STRING,
//						30 - txnName.length() * 2));
		//比照 資料建檔的寫法		
		// 有中文字時須保留2位供主機轉碼，內容僅可填入（欄位長度-3）位，結尾以1位半形空白表示該欄位結束，如欄位長度30，
        // 內容為長度26位全型字+2位半型空白，若內容長度<26位剩餘長度以半型空白填滿，欄位合計28位。
        txnName = txnName + CapString.fillBlankTail(CapConstants.EMPTY_STRING, 28 - txnName.getBytes(encoding).length);
        s.append(txnName);
        //===================================================
		// TCSIQLG-INQ-REASON
		String inqreason = CapString.halfWidthToFullWidth(src_inqreason);
		s.append(inqreason).append(
				CapString.fillBlankTail(CapConstants.EMPTY_STRING,
						30 - inqreason.length() * 2));
		// TCSIQLG-OBJ-TYPE
		s.append("2");
		// TCSIQLG-OBJ-COND
		try {
			s.append(
					new String(cond, 0, cond.length > maxObjLen ? maxObjLen
							: cond.length, encoding)).append(
					CapString.fillBlankTail(CapConstants.EMPTY_STRING,
							maxObjLen - cond.length));
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		s.append(CapConstants.LINE_BREAK);
		return s.toString();
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult query_OTS_TRPAYLG(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		Date begDate = TWNDate.valueOf(Util.trim(params.getString("trpaylg_begDate")));
		Date endDate = TWNDate.valueOf(Util.trim(params.getString("trpaylg_endDate")));
		if(begDate==null){
			throw new CapMessageException("查詢起日錯誤", getClass());
		}
		if(endDate==null){
				throw new CapMessageException("查詢迄日錯誤", getClass());
		}
		if(LMSUtil.cmpDate(CapDate.shiftDays(begDate, 365+90), "<", endDate)){
			throw new CapMessageException("查詢起迄日間隔不可超過一年", getClass());
		}
		if(Util.isEmpty(custId)){
			throw new CapMessageException("查詢ID錯誤", getClass());
		}
		String str_begDate = TWNDate.toAD(begDate);
		String str_endDate = TWNDate.toAD(endDate);
		List<Map<String, Object>> list = dwdbService.find_OTS_TRPAYLG(custId, dupNo, str_begDate, str_endDate);
		HashMap<String, JSONArray> mapTrData = new HashMap<String, JSONArray>();
		if (true) {
			JSONArray jsonArray_detail = new JSONArray();

			for (Map<String, Object> mapRow : list) {
				String tax_no = Util.trim(MapUtils.getString(mapRow, "TAX_NO"));
				String tax_nm = Util.trim(MapUtils.getString(mapRow, "TAX_NM"));
				String tx_acct_dt = Util.trim(TWNDate.toAD((Date)MapUtils.getObject(mapRow, "TX_ACCT_DT")));
				BigDecimal tx_amt = CrsUtil.parseBigDecimal(mapRow.get("TX_AMT"));
				//~~~~~~~~~
				JSONObject o = new JSONObject();
				o.put("ID_NO", Util.trim(MapUtils.getString(mapRow, "ID_NO")));
				o.put("BR_CD", Util.trim(MapUtils.getString(mapRow, "BR_CD")));
				o.put("TX_ACCT_DT", tx_acct_dt);
				o.put("TAX_NO", tax_no);
				o.put("TAX_NM", tax_nm);
				o.put("TRM_ID", Util.trim(MapUtils.getString(mapRow, "TRM_ID")));
				o.put("ACCT_KEY", Util.trim(MapUtils.getString(mapRow, "ACCT_KEY")));
				o.put("TX_AMT", LMSUtil.pretty_numStr(tx_amt));
				o.put("MISC", Util.trim(MapUtils.getString(mapRow, "MISC")));
				o.put("is_even", Util.parseInt(StringUtils.substring(tx_acct_dt, 5, 7))%2==0?"Y":"N");
				jsonArray_detail.add(o);
			}
			
			mapTrData.put("arr_detail", jsonArray_detail);
			mapTrData.put("arr_summary", summary_OTS_TRPAYLG(list));
		}
		result.set("begDate", str_begDate);
		result.set("endDate", str_endDate);
		result.set("list_data", new CapAjaxFormResult(mapTrData));
		result.set("list_hasData", list.size()>0?"Y":"N");

		return result;
	}
	
	private JSONArray summary_OTS_TRPAYLG(List<Map<String, Object>> list){
		/* 代發薪資公司統編 | 代發公司名稱 | 查詢期間加總金額
		 */		
		Map<MultiKey, BigDecimal> map = new LinkedHashMap<MultiKey, BigDecimal>();
		for (Map<String, Object> mapRow : list) {
			//J-111-0301 加總改依 入薪帳號 加總
			//String tax_no = Util.trim(MapUtils.getString(mapRow, "TAX_NO"));
			//String tax_nm = Util.trim(MapUtils.getString(mapRow, "TAX_NM"));
			String id_no = Util.trim(MapUtils.getString(mapRow, "ID_NO"));
			String acct_key = Util.trim(MapUtils.getString(mapRow, "ACCT_KEY"));
			BigDecimal tx_amt = CrsUtil.parseBigDecimal(mapRow.get("TX_AMT"));
		
			//MultiKey multiKey = new MultiKey(tax_no, tax_nm);
			MultiKey multiKey = new MultiKey(id_no,acct_key);
			if(!map.containsKey(multiKey)){
				map.put(multiKey, BigDecimal.ZERO);
			}
			map.put(multiKey, map.get(multiKey).add(tx_amt));
		}
		 
		JSONArray jsonArray_summary = new JSONArray();
		for (MultiKey key : map.keySet()) {			
			String id_no = Util.trim(key.getKey(0));
			String acct_key = Util.trim(key.getKey(1));
			BigDecimal tx_amt = map.get(key);
			//~~~~~~~~~
			JSONObject o = new JSONObject();;
			o.put("ID_NO", id_no);
			o.put("ACCT_KEY", acct_key);
			o.put("TX_AMT", LMSUtil.pretty_numStr(tx_amt));
			jsonArray_summary.add(o);
		}
		return jsonArray_summary; 
	}
	
	/**
	 * RPA地政士一鍵發查
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify)
	public IResult queryRpaQueryLaaName(PageParameters params) throws CapException{
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String oid = Util.trim(params.getString("oid"));
		String mainId = Util.trim(params.getString("mainId"));
		String queryLaaName = Util.trim(params.getString("queryLaaName"));
		
		cls1131Service.queryRpaQueryLaaName(mainId, queryLaaName);
		
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, "發查成功");
		
		return result;
	}
	
	/**
	 * RPA匯入
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify)
	public IResult importRpaDetail(PageParameters params) throws CapException{
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult reObj = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String oid = params.getString("oid", "");

		reObj = cls1131Service.importRpaDetail(mainId, oid);
		
		result.set("returnObj", reObj);
		//改為前端判斷alter
		//result.set(CapConstants.AJAX_NOTIFY_MESSAGE, "引入成功");
		// 將 C101S01E 的資料，寫入C101S01J
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		C101S01E c101s01e = clsService.findC101S01E(c101m01a);
		List<GenericBean> list = new ArrayList<GenericBean>();
		list.add(c101s01e);
		C101S01J c101s01j = write_C101S01E_toC101S01J(list, mainId, c101m01a.getCustId(),
				c101m01a.getDupNo());
		String msg_Laa = clsService.msg_Laa_html(c101s01j, prop_LMSCommomPage);
		result.set("msg_Laa", msg_Laa);
		return result;
	}
	
	/**
	 * RPA匯入ByAgent
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify)
	public IResult importRpaDetailByAgent(PageParameters params) throws CapException{
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult reObj = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String oid = params.getString("oid", "");
		String laaName = params.getString("laaName", "");
		String laaYear = params.getString("laaYear", "");
		String laaWord = params.getString("laaWord", "");
		String laaNo = params.getString("laaNo", "");
		String laaOffice = params.getString("laaOffice", "");
		
		reObj = cls1131Service.importRpaDetailByAgent(mainId, oid, laaName, laaYear, laaWord, laaNo, laaOffice);
		
		result.set("returnObj", reObj);
		//改為前端判斷alter
		//result.set(CapConstants.AJAX_NOTIFY_MESSAGE, "引入成功");
		// 將 C101S01E 的資料，寫入C101S01J
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		C101S01E c101s01e = clsService.findC101S01E(c101m01a);
		List<GenericBean> list = new ArrayList<GenericBean>();
		list.add(c101s01e);
		C101S01J c101s01j = write_C101S01E_toC101S01J(list, mainId, c101m01a.getCustId(),
				c101m01a.getDupNo());
		String msg_Laa = clsService.msg_Laa_html(c101s01j, prop_LMSCommomPage);
		result.set("msg_Laa", msg_Laa);
		return result;
	}
	
	/**
	 * 儲存負債比系統化欄位
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(AuthType.Modify)
	public IResult saveC120S01CrateData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		
		C101S01C c101s01c = cls1131Service.findModelByKey(C101S01C.class,
					mainId, custId, dupNo);
		
		String rateForm = Util.trim(params.getString("rateForm"));
		if (c101s01c != null && Util.isNotEmpty(rateForm)) {
			JSONObject rateDataJSON = DataParse.toJSON(rateForm);
			
			c101s01c.setRateData(rateDataJSON.toString());
			c101s01c.setReCalFlg("Y"); //異動flag
			
			//同時回存dRate、yRate、fRate
			String dRate = (String) rateDataJSON.get("dRateR");
			c101s01c.setDRate(new BigDecimal((String)rateDataJSON.get("dRateR")));
			c101s01c.setYRate(new BigDecimal((String)rateDataJSON.get("yRateR")));
			c101s01c.setFRate(new BigDecimal((String)rateDataJSON.get("fRateR")));
			cls1131Service.save(c101s01c);
		}
			
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		
		return result;
	}// ;
	
	/**
	 * 撈取C120S01C
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult queryC120S01C(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Boolean isC120M01A = params.getBoolean("isC120M01A");
		
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		
		if (!isC120M01A) {
			C101S01C c101s01c = cls1131Service.findModelByKey(C101S01C.class,
					mainId, custId, dupNo);
		
			if (c101s01c != null) {
				CapAjaxFormResult formResult = DataParse.toResult(c101s01c);
				result.set("c101s01c", formResult);
			}
			
			//預設個人收入
			C101S01B s01b = cls1131Service.findModelByKey(C101S01B.class,
					mainId, custId, dupNo);
		
			if (s01b != null) {
				List<C101S01W> c101s01ws = (List<C101S01W>) cls1131Service.findListByRelationKey(C101S01W.class, mainId, custId, dupNo);
				BigDecimal yearIncome = ClsUtility.s01c_pAllAmt___yearIncome(s01b, c101s01ws);
				BigDecimal otherIncome = ClsUtility.s01c_pAllAmt___otherIncome(s01b, c101s01ws);
				
				result.set("pAllAmt", yearIncome.add(otherIncome));
			}
		} else {
			//簽報書
			C120S01C c120s01c = cls1131Service.findModelByKey(C120S01C.class,
					mainId, custId, dupNo);
		
			if (c120s01c != null) {
				CapAjaxFormResult formResult = DataParse.toResult(c120s01c);
				result.set("c101s01c", formResult);
			}
		}
		
		return result;
	}
	
	/**
	 * 撈取C120S01C聯徵
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult queryC120S01C_JCIC(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		
		Map<String, Object> map = ejcicService.getLatestQueryRecordOfCreditInfoByIdInP7P9(custId);

		int itemCount = 0;
		int jcicCreditCount = 0;
		if(map == null || map.isEmpty() || map.get("PRODID") == null){
			result.set("jcicCount", 0);
			
			//信用卡循環(本行)
			result.set("jcic_credit_017", "0");
			//信用卡循環(他行)
			result.set("jcic_credit_not_017", "0");
			//分期未償還金額
			result.set("jcic_credit_unpay", "0");
			
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, "無聯徵資料，請重新查詢聯徵！");
		}
		else{
			
			String prodId = String.valueOf(map.get("PRODID"));
			String queryPersonId = String.valueOf(map.get("REQID"));
			String queryPersonBranchNo = String.valueOf(map.get("QBRANCH"));
			String queryDate = String.valueOf(map.get("QDATE"));
			List<CodeType> jcicCodeTypes = codeTypeService.findByCodeTypeList("LMS_JCIC_PERIOD");
			//借款人於聯徵中心BAM095授信資料
			List<Map<String, Object>> listBAM095 = ejcicService.getBAM095Data(custId, prodId, queryPersonId, queryPersonBranchNo, queryDate);
			
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			
			//分群 依BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2,CONTRACT_CODE,CONTRACT_AMT1判斷
			for (Map<String, Object> mapBAM095 : listBAM095) {
				if (!checkBAM095Exist(list, mapBAM095)) {
					list.add(mapBAM095);
				} else {
					continue;
				}
			}
			
			//借款人於聯徵中心BAM305授信資料
			List<Map<String, Object>> listBAM305 = ejcicService.getBAM305Data(custId, prodId, queryPersonId, queryPersonBranchNo, queryDate);
			
			//合併
			for (Map<String, Object> mapBAM305 : listBAM305) {
				list.add(mapBAM305);
			}
			
			int i = 0;
			for (i = 0 ; i < list.size() ; i++) {
				/*只要這幾類
				 * C	不限	透支
					CS	不限	透支
					E	不限	短期放款
					H	不限	中期放款
					I	不限	長期放款
					K	不限	存單質借
					M	不限	擔保透支
					ES	不限	短期擔保放款
					HS	車貸	中期擔保放款
						不動產	
						其他	
					IS	不限	長期擔保放款
					L	不限	應收保證款項
					Y	不限	現金卡
					Z	不限	學生助學貸款
					G	不限	貸款基金
				 */

				String item = CapString.trimNull(list.get(i).get("ACCOUNT_CODE")) +
								CapString.trimNull(list.get(i).get("ACCOUNT_CODE2"));
				
				//改抓代碼檔 LMS_JCIC_PERIOD 比對
//				if (!item.matches("/^C|CS|E|H|I|K|M|ES|HS|IS|L|Y|Z|G$/")) {
//					continue;
//				}
				Boolean isMatch = false;
				String matchItem = "";
				if (item.equals("HS") & CapString.trimNull(list.get(i).get("IS_KIND")).equals("31")) {
					matchItem = "HS1";
				} else if (item.equals("HS") && CapString.trimNull(list.get(i).get("PURPOSE_CODE")).equals("1")) {
					matchItem = "HS2";
				} else if (item.equals("HS")) {
					matchItem = "HS3";
				} else {
					matchItem = item;
				}
				
				for (CodeType jcicitem : jcicCodeTypes) {
					if (matchItem.equals(jcicitem.getCodeValue())) {
						isMatch = true;
					}
				}
				if (!isMatch) {
					continue;
				}
				
				if (CapString.trimNull(list.get(i).get("CONTRACT_AMT1")).equals("0") ) {
					continue;
				}
				
				//項目
				result.set("jcic_" + (itemCount + 1) , itemCount + 1);

				//行庫名稱 BANK_NAME
				result.set("jcic_bankcode_" + (itemCount + 1) , CapString.trimNull(list.get(i).get("BANK_CODE")));
				
				//行庫名稱 BANK_NAME
				result.set("jcic_bankname_" + (itemCount + 1) , CapString.trimNull(list.get(i).get("BANK_NAME")));
				
				//聯徵科目
				result.set("jcic_item_" + (itemCount + 1) , item);
				
				//中文名稱
				result.set("jcic_itemname_" + (itemCount + 1) , this.getJcicItemName(item, 
						CapString.trimNull(list.get(i).get("PURPOSE_CODE")),
						CapString.trimNull(list.get(i).get("IS_KIND"))));
				
				//list 包含BAM095 & BAM305 之資料, BAM095.CONTRACT_AMT1 , BAM305.CONTRACT_AMT
				//BAM305 沒有 CONTRACT_AMT1, 沒取到代表這筆來源是BAM305
				BigDecimal contractAmt = Util.isEmpty(list.get(i).get("CONTRACT_AMT1"))
						? Util.parseBigDecimal(list.get(i).get("CONTRACT_AMT"))//BAM305
						: Util.parseBigDecimal(list.get(i).get("CONTRACT_AMT1"));//BAM095
				
				//額度(仟元)=綜合額度(仟)
				result.set("jcic_loan_" + (itemCount + 1) , CapString.trimNull(contractAmt));
				
				//試算月付金=綜合額度(仟)/10 * 預計月付金
				BigDecimal preJcicPeriod = this.getPreJcicPeriod(item, 
						CapString.trimNull(list.get(i).get("PURPOSE_CODE")),
						CapString.trimNull(list.get(i).get("IS_KIND")));
				
				result.set("jcic_period_" + (itemCount + 1) , contractAmt.divide(new BigDecimal(10)).multiply(
						preJcicPeriod).setScale(0, BigDecimal.ROUND_HALF_UP));

				//未逾期金額 (千元)
				result.set("jcic_loanamt_" + (itemCount + 1) , CapString.trimNull(list.get(i).get("LOAN_AMT")));

				itemCount ++;
			}
			
			result.set("jcicCount", itemCount);
			
			//借款人於聯徵中心KRM040授信資料
			Map<String, Object> mapKRM040 = ejcicService.getKRM040getRevolBalByDebtRate(custId, prodId, queryDate);
			
			//信用卡循環(本行)
			result.set("jcic_credit_017", CapString.trimNull(mapKRM040.get("revol_bal_017")));
			//信用卡循環(他行)
			result.set("jcic_credit_not_017", CapString.trimNull(mapKRM040.get("revol_bal_other")));
			//分期未償還金額
			result.set("jcic_credit_unpay", CapString.trimNull(mapKRM040.get("pre_owed")));

			//J-112-0205 Web e-Loan新增代償相關欄位
			List<Map<String, Object>> mapCreditKRM040 = ejcicService.getKRM040getRevolBalByDebtRateWithoutSum(custId, prodId, queryDate);
            if (mapCreditKRM040.size() > 0) {
                for (i = 0 ; i < mapCreditKRM040.size() ; i++) {
					//項目
					result.set("jcic_credit_" + (jcicCreditCount + 1) , jcicCreditCount + 1);

					//行庫名稱 BANK_NAME
					result.set("jcic_credit_bankcode_" + (jcicCreditCount + 1) , CapString.trimNull(mapCreditKRM040.get(i).get("ISSUE")));

					//行庫名稱 BANK_NAME
					result.set("jcic_credit_bankname_" + (jcicCreditCount + 1) , CapString.trimNull(mapCreditKRM040.get(i).get("ISSUE_NAME")));

					//信用卡循環
					result.set("jcic_credit_revol_" + (jcicCreditCount + 1) , CapString.trimNull(mapCreditKRM040.get(i).get("REVOL_BAL")));

					//分期未償還金額
					result.set("jcic_credit_pre_" + (jcicCreditCount + 1) , CapString.trimNull(mapCreditKRM040.get(i).get("PRE_OWED")));


                    jcicCreditCount++;
                }
            }
            result.set("jcicCreditCount", jcicCreditCount);
		}
		
		return result;
	}

	private boolean checkBAM095Exist(List<Map<String, Object>> list,
			Map<String, Object> mapBAM095) {
		
		String contractCode = CapString.trimNull(mapBAM095.get("CONTRACT_CODE"));
		String contractCode1 = CapString.trimNull(mapBAM095.get("CONTRACT_CODE1"));
		String[] contractCodeArray = new String[] {contractCode, contractCode1};
		       
		if (list.size() == 0) {
			return false;
		} else if (CapString.isEmpty(contractCode)) {
			return false;
		}else if(Arrays.asList(contractCodeArray).contains(CLS1131FormHandler.EJCIC_BAM095_CONTRACT_CODE)){
			return false;
		}
		else {
			//依BANK_CODE,ACCOUNT_CODE,ACCOUNT_CODE2,CONTRACT_CODE,CONTRACT_AMT1判斷
			boolean isExist = false;
			for (Map<String, Object> map : list) {
				if (CapString.trimNull(map.get("BANK_CODE")).equals(CapString.trimNull(mapBAM095.get("BANK_CODE")))
						&& CapString.trimNull(map.get("ACCOUNT_CODE")).equals(CapString.trimNull(mapBAM095.get("ACCOUNT_CODE")))
						&& CapString.trimNull(map.get("ACCOUNT_CODE2")).equals(CapString.trimNull(mapBAM095.get("ACCOUNT_CODE2")))
						&& CapString.trimNull(map.get("CONTRACT_CODE")).equals(CapString.trimNull(mapBAM095.get("CONTRACT_CODE")))
						&& CapString.trimNull(map.get("CONTRACT_AMT1")).equals(CapString.trimNull(mapBAM095.get("CONTRACT_AMT1")))) {
					isExist = true;
				}
			}
			
			return isExist;
		}
	}

	private String getJcicItemName (String item, String purposeCode, String isKind) {		
		CodeType codeType = null;
		if (item.equals("HS") & isKind.equals("31")) {
			codeType = codeTypeService.findByCodeTypeAndCodeValue("LMS_JCIC_PERIOD", "HS1");
		} else if (item.equals("HS") && purposeCode.equals("1")) {
			codeType = codeTypeService.findByCodeTypeAndCodeValue("LMS_JCIC_PERIOD", "HS2");
		} else if (item.equals("HS")) {
			codeType = codeTypeService.findByCodeTypeAndCodeValue("LMS_JCIC_PERIOD", "HS3");
		} else {
			codeType = codeTypeService.findByCodeTypeAndCodeValue("LMS_JCIC_PERIOD", item);
		}
		
		if (codeType == null) {
			return "";
		} else {
			return CapString.trimNull(codeType.getCodeDesc2());
		}
		
	}
	
	private BigDecimal getPreJcicPeriod(String item, String purposeCode, String isKind) {
		CodeType codeType = null;
		if (item.equals("HS") & isKind.equals("31")) {
			codeType = codeTypeService.findByCodeTypeAndCodeValue("LMS_JCIC_PERIOD", "HS1");
		} else if (item.equals("HS") && purposeCode.equals("1")) {
			codeType = codeTypeService.findByCodeTypeAndCodeValue("LMS_JCIC_PERIOD", "HS2");
		} else if (item.equals("HS")) {
			codeType = codeTypeService.findByCodeTypeAndCodeValue("LMS_JCIC_PERIOD", "HS3");
		} else {
			codeType = codeTypeService.findByCodeTypeAndCodeValue("LMS_JCIC_PERIOD", item);
		}
		
		if (codeType == null) {
			return new BigDecimal(0);
		} else {
			BigDecimal amt = new BigDecimal(codeType.getCodeDesc());
			return amt;
		}
	}
	
	/**
	 * 判斷是否有夫妻收入有異動，若是，c101s01c.reCalFlg = N
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult checkYFamAmtisChange(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		BigDecimal yFamAmt = new BigDecimal(params.getAsInteger("yFamAmt" , 0));
		
		C101S01C c101s01c = cls1131Service.findModelByKey(C101S01C.class, mainId, custId, dupNo);
		
		if (c101s01c != null) {
			if (c101s01c.getYFamAmt() != null && !c101s01c.getYFamAmt().equals(yFamAmt)) {
				c101s01c.setReCalFlg("N");
				cls1131Service.save(c101s01c);		
			}
		}
		
		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult executeLaborBailoutSimpleCreditScoreTalbe(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		String b1ScoreStr = Util.trim(params.getString("b1Score"));
		String c1ScoreStr = Util.trim(params.getString("c1Score"));
		String applyDate = Util.trim(params.getString("applyDate"));
		String applyQuota = Util.trim(params.getString("applyQuota"));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean isManualReview = user.getUserId().startsWith("07") ? false : true;
		
		this.cls1131Service.checkForLabourBailout4(applyDate);
		
		int b1Score = StringUtils.isEmpty(b1ScoreStr) ? 0 : Integer.valueOf(b1ScoreStr);
		int c1Score = StringUtils.isEmpty(c1ScoreStr) ? 0 : Integer.valueOf(c1ScoreStr);
		
		C101S01X c101s01x = this.cls1131Service.getC101S01XObject(mainId, custId, dupNo);
		
		this.cls1131Service.computeScoreForLabourBailout(custId, c101m01a.getOwnBrId(), c101s01x, b1Score, c1Score);
		this.cls1131Service.processEjcicAndEtchDataForC101S01X(mainId, custId, dupNo, c101m01a.getOwnBrId(), c101s01x);
		boolean isApproved = this.cls1131Service.checkIsApprovedForLabourBailout(c101s01x, isManualReview);
		this.cls1131Service.processIsApprovedFlagAndFlowFlag(isApproved, c101s01x);
		if(true){
			//洗防的 高風險客戶，要把 C101S01X.flowFlag 設成 D
			String luvRiskLevel = amlRelateService.getCustLuvRiskLevel(c101m01a.getOwnBrId(), custId, dupNo);
			if(Util.equals("H", luvRiskLevel)){
				c101s01x.setFlowFlag("D");		
			}
		}
		this.cls1131Service.saveC101S01X(c101s01x, applyDate, applyQuota);
		
		result.set("a1Score", c101s01x.getA1Score());
		result.set("b1Score", c101s01x.getB1Score());
		result.set("c1Score", c101s01x.getC1Score());
		result.set("totalScore", c101s01x.getTotalScore());
		result.set("notAllowA", c101s01x.getNotAllowA());
		result.set("notAllowB", c101s01x.getNotAllowB());
		result.set("notAllowC", c101s01x.getNotAllowC());
		result.set("notAllowD", c101s01x.getNotAllowD());
		result.set("notAllowE", c101s01x.getNotAllowE());
		result.set("isApproved", c101s01x.getIsApproved());
		result.set("flowFlag", c101s01x.getFlowFlag());
		result.set("ip", c101s01x.getIp());
		result.set("ipCount", c101s01x.getIpCount());
		result.set("tel", c101s01x.getTel());
		result.set("telCount", c101s01x.getTelCount());
		return result;
	}
	
	@DomainAuth(AuthType.Query)
	public IResult sync_C122M01A_when_cls1131(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);		
		clsService.sync_c122m01a_applyKindD_statFlag_when_cls1131(c101m01a);
		return result;
	}
	
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult calc_seniority_from_snrY_snrM(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		JSONObject c101s01bForm = JSONObject.fromObject(params.getString("C101S01BForm"));
		String oid = Util.trim(c101s01bForm.optString("oid"));
		String snrY = Util.trim(c101s01bForm.optString("snrY"));
		String snrM = Util.trim(c101s01bForm.optString("snrM"));
		String seniority = LMSUtil.pretty_numStr(ClsUtility.seniorityYM_encode(Util.parseInt(snrY), Util.parseInt(snrM)));
		if(Util.isNotEmpty(oid)){
			C101S01B c101s01b = cls1131Service.findModelByOid(C101S01B.class, oid);
			if(c101s01b!=null && Util.isNotEmpty(Util.trim(c101s01b.getCustId())) && !LMSUtil.check2(c101s01b.getCustId())){
				seniority = ""; //公司戶 不需輸入年資
			}
		}
		
		result.set("seniority", seniority);
		return result;
	}
	
	private void inject_snrY_snrM_when_fetchC122M01A_to_C101(CapAjaxFormResult formResult, C120S01B s01b){
		formResult.set("snrY", ClsUtility.get_inject_snrY(s01b.getSeniority()));
		formResult.set("snrM", s01b.getSnrM()==null?"0":LMSUtil.pretty_numStr(s01b.getSnrM()));
	}	
	
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult fix_coCityZipTarget(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();		
		for (String c101m01a_mainId : Util.trim(params.getString("c101m01a_mainId_arr"))
				.split("\\|")) {
			C101M01A c101m01a = clsService.findC101M01A_mainId(c101m01a_mainId);
			if(c101m01a==null){
				continue;
			}
			C101S01A c101s01a = clsService.findC101S01A(c101m01a);
			if (Util.isNotEmpty(Util.trim(c101s01a.getCoTarget())) 
					&& (Util.isEmpty(c101s01a.getCoCity()) && Util.isEmpty(c101s01a.getCoZip()) )) {
				String[] parseArr = cls1131Service.parseAddr(Util.trim(c101s01a.getCoTarget()));
				String _City = parseArr[0];
				String _Zip = parseArr[1];
				String _Addr = parseArr[2];
				
				c101s01a.setCoCity(_City); 
				c101s01a.setCoZip(_Zip);	
				c101s01a.setCoAddr(_Addr);		
				clsService.daoSave(c101s01a);
			}
		}	
		for (String c122m01a_mainId : Util.trim(params.getString("c122m01a_mainId_arr"))
				.split("\\|")) {
			C122M01A c122m01a = clsService.findC122M01A_mainId(c122m01a_mainId);
			if(c122m01a==null){
				continue;
			}
			C120S01A c120s01a = clsService.findC120S01A(c122m01a.getMainId(), c122m01a.getCustId(), c122m01a.getDupNo());
			if (Util.isNotEmpty(Util.trim(c120s01a.getCoTarget())) 
					&& (Util.isEmpty(c120s01a.getCoCity()) && Util.isEmpty(c120s01a.getCoZip()) )) {
				String[] parseArr = cls1131Service.parseAddr(Util.trim(c120s01a.getCoTarget()));
				String _City = parseArr[0];
				String _Zip = parseArr[1];
				String _Addr = parseArr[2];
				
				c120s01a.setCoCity(_City); 
				c120s01a.setCoZip(_Zip);	
				c120s01a.setCoAddr(_Addr);		
				clsService.daoSave(c120s01a);
			}
		}	
		return result;		
	}
	
	@DomainAuth(AuthType.Query)
	public IResult getC101S01X(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		Boolean isC120M01A = params.getBoolean("isC120M01A");
		if(isC120M01A){
			C120S01X c101s01x = this.cls1131Service.getC120S01X(mainId, custId, dupNo);
			if(c101s01x != null){
				result.set("a1Score", c101s01x.getA1Score());
				result.set("b1Score", c101s01x.getB1Score());
				result.set("c1Score", c101s01x.getC1Score());
				result.set("totalScore", c101s01x.getTotalScore());
				result.set("notAllowA", c101s01x.getNotAllowA());
				result.set("notAllowB", c101s01x.getNotAllowB());
				result.set("notAllowC", c101s01x.getNotAllowC());
				result.set("notAllowD", c101s01x.getNotAllowD());
				result.set("notAllowE", c101s01x.getNotAllowE());
				result.set("isApproved", c101s01x.getIsApproved());
				result.set("flowFlag", c101s01x.getFlowFlag());
				result.set("applyDate", c101s01x.getApplyDate());
				result.set("applyQuota", c101s01x.getApplyQuota());
				result.set("isOtherAssets", c101s01x.getIsOtherAssets());
				result.set("ip", c101s01x.getIp());
				result.set("ipCount", c101s01x.getIpCount());
				result.set("tel", c101s01x.getTel());
				result.set("telCount", c101s01x.getTelCount());
			}	
		}else{
			C101S01X c101s01x = this.cls1131Service.getC101S01X(mainId, custId, dupNo);
			if(c101s01x != null){
				result.set("a1Score", c101s01x.getA1Score());
				result.set("b1Score", c101s01x.getB1Score());
				result.set("c1Score", c101s01x.getC1Score());
				result.set("totalScore", c101s01x.getTotalScore());
				result.set("notAllowA", c101s01x.getNotAllowA());
				result.set("notAllowB", c101s01x.getNotAllowB());
				result.set("notAllowC", c101s01x.getNotAllowC());
				result.set("notAllowD", c101s01x.getNotAllowD());
				result.set("notAllowE", c101s01x.getNotAllowE());
				result.set("isApproved", c101s01x.getIsApproved());
				result.set("flowFlag", c101s01x.getFlowFlag());
				result.set("applyDate", c101s01x.getApplyDate());
				result.set("applyQuota", c101s01x.getApplyQuota());
				result.set("isOtherAssets", c101s01x.getIsOtherAssets());
				result.set("ip", c101s01x.getIp());
				result.set("ipCount", c101s01x.getIpCount());
				result.set("tel", c101s01x.getTel());
				result.set("telCount", c101s01x.getTelCount());
			}
		}
		return result;
	}
	
	/**
	 * 儲存地政士資料
	 * @param params
	 * @return
	 * @throws CapException 
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveC101S01Y(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String sOid = Util.trim(params.getString("sOid"));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String laaYear = Util.trim(params.getString("laaYear"));
		String laaWord = Util.trim(params.getString("laaWord"));
		String laaNo = Util.trim(params.getString("laaNo"));
		String caseSrcFlag = Util.trim(params.getString("caseSrcFlag"));
		Boolean isC120M01A = params.getBoolean("isC120M01A");
		// 只有徵信才存檔
		if (!isC120M01A) {
			C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
			String custId = c101m01a.getCustId();
			String dupNo = c101m01a.getDupNo();
			C101S01Y s01y = null;
			// 全新增加並選擇地政士進件時，先判斷是否已存在資料
			if (CapString.isEmpty(sOid) && "L".equals(caseSrcFlag)) {
				s01y = this.cls1131Service.getC101S01Y(mainId, custId, dupNo, laaYear, laaWord, laaNo);
				if (s01y != null) {
					// 若已存在同一證號資料跳提示不新增
					result.set(CapConstants.AJAX_NOTIFY_MESSAGE, "已有相同證號資料");
					return result;
				}
			}
			// 依OID尋找
			s01y = this.cls1131Service.getC101S01Y(sOid);
			// 非已存在資料時用照號資料尋找
			if (s01y == null) {
				s01y = new C101S01Y();
				CapBeanUtil.map2Bean(params, s01y);
				s01y.setOid(null);
				s01y.setCustId(custId);
				s01y.setDupNo(dupNo);
			} else {
				CapBeanUtil.map2Bean(params, s01y);
				s01y.setOid(sOid);
				s01y.setCustId(custId);
				s01y.setDupNo(dupNo);
			}
			// 查詢是否為黑名單
			CapAjaxFormResult r = (CapAjaxFormResult) checkC101S01Y(params);
			s01y.setLaaCtlFlag((String) r.get("laaCtlFlag"));
			s01y.setLaaMatchRuleFlag((String) r.get("laaMatchRuleFlag"));
			s01y.setLaaQueryDate(new Date());
			cls1131Service.saveC101S01Y(s01y);
			// 將 C101S01E 的資料，寫入C101S01J
			C101S01E c101s01e = clsService.findC101S01E(c101m01a);
			List<GenericBean> list = new ArrayList<GenericBean>();
			list.add(c101s01e);
			C101S01J c101s01j = write_C101S01E_toC101S01J(list, mainId, c101m01a.getCustId(),
					c101m01a.getDupNo());
			String msg_Laa = clsService.msg_Laa_html(c101s01j, prop_LMSCommomPage);
			result.set("msg_Laa", msg_Laa);
		}
		return result;
	}
	
	/**
	 * 找地政士資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult getC101S01Y(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String sOid = Util.trim(params.getString("sOid"));
		Boolean isC120M01A = params.getBoolean("isC120M01A");
		if (isC120M01A) {
			C120S01Y s01y = this.cls1131Service.getC120S01Y(sOid);
			if (s01y != null) {
				result.putAll(new CapAjaxFormResult(s01y.toJSONObject(
						CapEntityUtil.getColumnName(s01y), null)));
			}
		} else {
			C101S01Y s01y = this.cls1131Service.getC101S01Y(sOid);
			if (s01y != null) {
				result.putAll(new CapAjaxFormResult(s01y.toJSONObject(
						CapEntityUtil.getColumnName(s01y), null)));
			}
		}
		return result;
	}
	
	/**
	 * 刪除地政士資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteC101S01Y(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String sOid = Util.trim(params.getString("sOid"));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Boolean isC120M01A = params.getBoolean("isC120M01A");
		if (isC120M01A) {
			C120S01Y s01y = this.cls1131Service.getC120S01Y(sOid);
			if (s01y != null) {
				cls1131Service.deleteC120S01Y(s01y);
			}
		} else {
			C101S01Y s01y = this.cls1131Service.getC101S01Y(sOid);
			if (s01y != null) {
				cls1131Service.deleteC101S01Y(s01y);
			}
		}
		// 將 C101S01E 的資料，寫入C101S01J
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		C101S01E c101s01e = clsService.findC101S01E(c101m01a);
		List<GenericBean> list = new ArrayList<GenericBean>();
		list.add(c101s01e);
		C101S01J c101s01j = write_C101S01E_toC101S01J(list, mainId, c101m01a.getCustId(),
				c101m01a.getDupNo());
		String msg_Laa = clsService.msg_Laa_html(c101s01j, prop_LMSCommomPage);
		result.set("msg_Laa", msg_Laa);
		return result;
	}
	
	/**
	 * 檢查地政士資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult checkC101S01Y(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String laaYear = Util.trim(params.getString("laaYear"));
		String laaWord = Util.trim(params.getString("laaWord"));
		String laaNo = Util.trim(params.getString("laaNo"));
		String laaDesc = Util.trim(params.getString("laaDesc"));
		Map<String, Object> c900m01h = clsService
			.findActiveMajorC900M01HByCertNo2(laaYear, laaWord, laaNo);
		String laaCtlFlag = "0";
		String laaMatchRuleFlag = "";
		if (c900m01h != null) {
			laaCtlFlag = MapUtils.getString(c900m01h, "CTLFLAG");
			String CtlFlagType = clsService.getCtlFlagType(MapUtils
					.getString(c900m01h, "CTLFLAG"));
			if (Util.isEmpty(laaDesc)
					&& Util.equals(CtlFlagType, LMSUtil.地政士黑名單警示名單)) {
				result.set("msg", "命中地政士黑名單警示名單，" + MessageFormat.format(
						getI18nMsg("message.emptyField"),
						getI18nMsg("C101S01E.laaDesc")));
			}
			//J-109-0251_10702_B1001 Web e-Loan 授信管理系統調整地政士懲戒紀錄選項判斷邏輯
			if(clsService.checkLaaMatchRuleFlag(c900m01h)){
				laaMatchRuleFlag = UtilConstants.DEFAULT.是;
			}
			if(Util.isEmpty(laaDesc) && clsService.checkLaaMatchRuleFlag(c900m01h)){
				result.set("msg", "命中地政士黑名單警示名單懲戒紀錄，且有勾選 永慶房屋直營店或信義房屋名義仲介成交案件 及 懲戒紀錄是否屬地政士法第17條，" + MessageFormat.format(
						getI18nMsg("message.emptyField"),
						getI18nMsg("C101S01E.laaDesc")));
			}
		}
		result.set("laaCtlFlag", laaCtlFlag);
		result.set("laaMatchRuleFlag", laaMatchRuleFlag);
		return result;
	}
	
	/**
	 * 於消金徵信<基本資料>信貸集中徵信執行引入資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult impKYCData(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Date today = Calendar.getInstance().getTime();

		result.set("userId", Util.trim(user.getUserCName()));
		result.set("userName", Util.trim(user.getUserId()));
		result.set("dataTime", Util.trim(S_FORMAT.format(today)));

		return result;
	}
	
	/**
	 * 紀錄聯徵查詢結果
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult recordEjcicResultData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		if (c101m01a == null) {
			throw new CapMessageException("mainId[" + mainId + "] not found",
					getClass());
		}

		C101S01E c101s01e = clsService.findC101S01E(c101m01a);
		String custId = c101m01a.getCustId();
		String dupNo = c101m01a.getDupNo();
		if (c101s01e != null && c101s01e.getOneBtnQ_qTime() != null) {
			String prodId = ejcicService.get_cls_PRODID(custId);
			Map<String, Object> dateMap = ejcicService.getDate(custId, prodId);
			if (dateMap == null) {
				// message.c_query_credit.noJCIC=無聯徵查詢結果。請先查詢聯徵後，再執行引進！
				throw new CapMessageException( "【"+getI18nMsg("C101S01C.credit")+"】"+
						getI18nMsg("message.c_query_credit.noJCIC"), getClass());
			}
			// 取得相關日期
			String QDATE = Util.trim(dateMap.get("QDATE"));
			Map<String, Object> ejcicRecord = cls1131Service.getEjcicReusltRecord(custId, prodId, QDATE, mainId, dupNo);
			List<CodeType> codeTypes = codeTypeService.findByCodeTypeList("C101S02A_ejcicItem");
			for (CodeType codeType : codeTypes) {
				String item = codeType.getCodeValue();
				C101S02A s02a = clsService.findC101S02AByItem(mainId, custId, dupNo, item);
				if (s02a == null) {
					s02a = new C101S02A();
					s02a.setMainId(mainId);
					s02a.setCustId(custId);
					s02a.setDupNo(dupNo);
					s02a.setEjcicItem(item);
					s02a.setJsonOb(new JSONObject().toString());
				}
				if (ejcicRecord.containsKey(item)) {
					JSONObject json = JSONObject.fromObject(ejcicRecord.get(item));
					s02a.setJsonOb(json.toString());
					s02a.setDataDate(TWNDate.valueOf(QDATE).toDate());
				}
				clsService.save(s02a);
			}
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkIsQueryEjcicS11(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String checkMessage = this.cls1131Service.checkIsQueryEjcicS11(mainId, custId, dupNo);
		result.set("chekcMsg", checkMessage);
		return result;
	}
	
	/**
	 * 判斷登入者是否僅有EL電銷權限
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult check_only_expermission(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		result.set("only_ex_permission", user.isEXAuth());//是否僅有電銷權限
		
		return result;

	}
	
	/**
	 * 決策api /api/eloan/homeLoanRule
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult brmp_homeloanrule(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String mainid = Util.trim(params.getString("mainId"));
		String custid = Util.trim(params.getString("custId"));
		String fntype = Util.trim(params.getString("fntype"));
		String ralevel = Util.trim(params.getString("ralevel"));
		String ltv = Util.trim(params.getString("ltv")); 
		String cd01 = Util.trim(params.getString("cd01")); 
		String cd02 = Util.trim(params.getString("cd02")); 
		String disoption = Util.trim(params.getString("disoption")); //1.加碼 2.減碼
		BigDecimal cd03 = BigDecimal.ZERO;
		if(Util.isNotEmpty(Util.trim(params.getString("cd03")))){
			cd03 = CrsUtil.parseBigDecimal(params.getString("cd03")); 
			if("2".equals(disoption)){//減碼
				cd03 = cd03.negate();
			}
		}
		String cd04 = Util.trim(params.getString("cd04"));
		String ce01 = Util.trim(params.getString("ce01"));
		String ce02 = Util.trim(params.getString("ce02"));
		String ex01 = Util.trim(params.getString("ex01"));
		String ex02 = Util.trim(params.getString("ex02"));

		String sp01 = Util.trim(params.getString("sp01"));
		String sp02 = Util.trim(params.getString("sp02"));
		String sp03 = Util.trim(params.getString("sp03"));
		String sp04 = Util.trim(params.getString("sp04"));
		String sp05 = Util.trim(params.getString("sp05"));
		String n6r = "";
		//取得現行的6R 台幣利率
		List<Map<String, Object>> misrate6r =  misMislnratService.findMislnratByLRRate("6R", "TWD");
		if(misrate6r.size() > 0){
			n6r = String.valueOf(misrate6r.get(0).get("LR_RATE"));
		}
		
		Brmp003I brmpInput = this.brmp_homeloanrule_buildInput(mainid, custid, fntype, ralevel, ltv, cd01, cd02, cd03, disoption,
				cd04, ce01, ce02, ex01, ex02, sp01, sp02, sp03, sp04, sp05, n6r);
		JSONObject brmp003O_raw_json = brmpGwClient.send_homeloanrule_raw_json(brmpInput);
		//Brmp003O brmp003o_obj = brmpGwClient.send_homeloanrule_rtnJsonObj(brmp003O_raw_json);
		//Brmp003O brmp003o_obj = new Brmp003O();
		if(brmp003O_raw_json==null){
			throw new CapMessageException("決策平台執行「房貸利率試算」非回傳JSON格式", getClass());
		} 
		result.set("mainid", mainid);
		result.set("brmp003data", brmp003O_raw_json.toString());
		
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainid);
		if(c101m01a!=null){
			if(true){
				//儲存決策回傳結果
				L120S19A l120s19a_inputObj = null;
				L120S19A l120s19a_outputObj = null;
				L120S19A l120s19a_latestInput = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainid, ClsConstants.L120S19A_ItemTypeCode.BRMP_homeloanrule_input);
				L120S19A l120s19a_latestOutput = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainid, ClsConstants.L120S19A_ItemTypeCode.BRMP_homeloanrule_output);
				String l140m01a_mainId = "";//沒用到 放空
				BigDecimal inputItemVer = (l120s19a_latestInput ==null) ? BigDecimal.ONE : l120s19a_latestInput.getItemVersion().add(BigDecimal.ONE);
				BigDecimal onputItemVer = (l120s19a_latestOutput ==null) ? BigDecimal.ONE : l120s19a_latestOutput.getItemVersion().add(BigDecimal.ONE);
				l120s19a_inputObj = new L120S19A(mainid, ClsConstants.L120S19A_ItemTypeCode.BRMP_homeloanrule_input, l140m01a_mainId, BrmpGwClient.convert_to_jsonStr(brmpInput), inputItemVer, user.getUserId());
				l120s19a_outputObj = new L120S19A(mainid, ClsConstants.L120S19A_ItemTypeCode.BRMP_homeloanrule_output, l140m01a_mainId, brmp003O_raw_json.toString(), onputItemVer, user.getUserId());
				clsService.save(l120s19a_inputObj, l120s19a_outputObj);
			}
		}
		
		return result;

	}
	/**
	 * 製作 決策api /api/eloan/homeLoanRule input
	 */
	private Brmp003I brmp_homeloanrule_buildInput(String mainid, String custid, String fntype, String ralevel, String ltv, String cd01, String cd02, BigDecimal cd03, String disoption, 
			String cd04, String ce01, String ce02, String ex01, String ex02, String sp01, String sp02, String sp03, String sp04, String sp05, String n6r){
		Brmp003I brmp003InputObj = new Brmp003I();
		if(true){
			brmp003InputObj.setUuid(IDGenerator.getUUID());
			brmp003InputObj.setMainId(mainid);
			brmp003InputObj.setCustId(custid);
			brmp003InputObj.setFntype(fntype);
			brmp003InputObj.setRalevel(ralevel);
			brmp003InputObj.setLtv(ltv);
			brmp003InputObj.setCd01(cd01);
			brmp003InputObj.setCd02(cd02);
			brmp003InputObj.setCd03(cd03);	
			brmp003InputObj.setDisoption(disoption);
			brmp003InputObj.setCd04(cd04);
			brmp003InputObj.setCe01(ce01);
			brmp003InputObj.setCe02(ce02);
			brmp003InputObj.setEx01(ex01);
			brmp003InputObj.setEx02(ex02);
			brmp003InputObj.setSp01(sp01);
			brmp003InputObj.setSp02(sp02);
			brmp003InputObj.setSp03(sp03);
			brmp003InputObj.setSp04(sp04);
			brmp003InputObj.setSp05(sp05);
			brmp003InputObj.setN6r(n6r);
		}
		return brmp003InputObj;
	}
	
	/**
	 * 取得該筆徵信作業最後一次 call 決策api /api/eloan/homeLoanRule 搜尋條件/查詢結果
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult show_last_brmp_homeloanrule(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean hasbrmp003 = false;
		String mainid = Util.trim(params.getString("mainId"));
		//取得搜尋條件
		L120S19A l120s19a = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainid, ClsConstants.L120S19A_ItemTypeCode.BRMP_homeloanrule_input);
		Brmp003I brmpIutput = null;
		if(l120s19a!=null){
			hasbrmp003 = true;
			try{
				ObjectMapper objectMapper = new ObjectMapper();
				brmpIutput = objectMapper.readValue(JSONObject.fromObject(l120s19a.getJsonData()).toString(), Brmp003I.class);						
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				brmpIutput = null;
			}
		}
		String ralevel = "";
		String grade3 = "";
		if(brmpIutput != null){
			ralevel = Util.trim(brmpIutput.getRalevel());
			//取得平等模型之[模型最終評等]
			C101S01G c101s01g = cls1131Service.findModelByKey(C101S01G.class,mainid, null, null);
			if(c101s01g != null){
				grade3 = Util.trim(c101s01g.getGrade3());
			}
			
		}
		Brmp003O brmpOutput = null;
		if(Util.equals(ralevel, grade3)){ //評等分數與計算房貸成數使用之分數相等時，才顯示
			//取得查詢結果
			L120S19A l120s19b = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainid, ClsConstants.L120S19A_ItemTypeCode.BRMP_homeloanrule_output);
			if(l120s19b!=null){
				try{
					ObjectMapper objectMapper = new ObjectMapper();
					brmpOutput = objectMapper.readValue(JSONObject.fromObject(l120s19b.getJsonData()).toString(), Brmp003O.class);						
				}catch(Exception e){
					logger.error(StrUtils.getStackTrace(e));
					brmpOutput = null;
				}
			}
		}else{
			hasbrmp003 = false;
		}

		result.set("hasbrmp003", hasbrmp003);
		result.set("mainid", mainid);
		result.set("brmp003Input", new CapAjaxFormResult(brmpIutput));
		result.set("brmp003Output", new CapAjaxFormResult(brmpOutput));
		
		return result;
	}
	
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult mappingClsJob(PageParameters params) throws CapException {
		//get value from req
		String clsJobType1Val = Util.trim(params.getString("clsJobType1"));
		String clsJobType2Val = Util.trim(params.getString("clsJobType2"));
		String clsJobTitleVal = Util.trim(params.getString("clsJobTitle"));
		String capital = Util.trim(params.getString("capital"));
		boolean isNPO = params.getBoolean("isNPO");
		
		return cls1131Service.mappingClsJob(clsJobType1Val, clsJobType2Val, clsJobTitleVal, capital,isNPO);

	}

	/**
	 * J-112-0467 歡喜信貸客群查詢
	 * 決策api /api/eloan/termGroupRule
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult brmp_termGroupRule(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString("mainId"));//個金徵信mainid
		String custId = Util.trim(params.getString("custId"));//借款人身分證字號
		String dupNo  = Util.trim(params.getString("dupNo"));//重複碼
		String lnClass = Util.trim(params.getString("lnClass"));//產品代號
		String jobCode = Util.trim(params.getString("clsJobType2"));//行業代碼
		String positionCode	= Util.trim(params.getString("positionCode"));//職位代碼
		String hasJuTotalCapital = Util.trim(params.getString("hasJuTotalCapital"));//有無服務單位實收資本額(新台幣元) > 有:Y 無:N
		BigDecimal regCapital = BigDecimal.ZERO;//資本額 (若非公司請帶0) //服務單位實收資本額(新台幣元)勾無的話帶1
		BigDecimal payAmt = BigDecimal.ZERO;//年收入
		
		if(Util.equals("N", hasJuTotalCapital)){//服務單位實收資本額(新台幣元)勾無的話帶1
			regCapital = BigDecimal.ONE;
		}else{
			if(Util.isNotEmpty(Util.trim(params.getString("juPaidUpCapital")))){
				regCapital = CrsUtil.parseBigDecimal(params.getString("juPaidUpCapital"));
			}
		}

		if(Util.isNotEmpty(Util.trim(params.getString("payAmt")))){
			payAmt = CrsUtil.parseBigDecimal(params.getString("payAmt"));
		}
		
		Brmp004I brmpInput = this.brmp_termGroupRule_buildInput(mainId, custId, lnClass, jobCode, positionCode, regCapital, payAmt);
		//決策api termGroupRule
		JSONObject brmp004O_raw_json = brmpGwClient.send_termGroupRule_raw_json(brmpInput);

		if(brmp004O_raw_json==null){
			throw new CapMessageException("決策平台執行「 歡喜信貸客群查詢」非回傳JSON格式", getClass());
		} 
		result.set("mainid", mainId);
		result.set("brmp004data", brmp004O_raw_json.toString());
		
		//歡喜信貸客群查詢(試算) & 個金徵信用
		//無mainid則不用儲存結果
		if(Util.isNotEmpty(mainId)){
			C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
			C120M01A c120m01a = cls1131Service.findModelByKey(C120M01A.class,
					mainId, custId, dupNo);
			if(c101m01a!=null || c120m01a!=null){
				if(true){
					//儲存決策回傳結果
					L120S19A l120s19a_inputObj = null;
					L120S19A l120s19a_outputObj = null;
					L120S19A l120s19a_latestInput = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainId, ClsConstants.L120S19A_ItemTypeCode.BRMP_termGroupRule_input);
					L120S19A l120s19a_latestOutput = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainId, ClsConstants.L120S19A_ItemTypeCode.BRMP_termGroupRule_output);
					String l140m01a_mainId = "";//沒用到 放空
					BigDecimal inputItemVer = (l120s19a_latestInput ==null) ? BigDecimal.ONE : l120s19a_latestInput.getItemVersion().add(BigDecimal.ONE);
					BigDecimal onputItemVer = (l120s19a_latestOutput ==null) ? BigDecimal.ONE : l120s19a_latestOutput.getItemVersion().add(BigDecimal.ONE);
					l120s19a_inputObj = new L120S19A(mainId, ClsConstants.L120S19A_ItemTypeCode.BRMP_termGroupRule_input, l140m01a_mainId, BrmpGwClient.convert_to_jsonStr(brmpInput), inputItemVer, user.getUserId());
					l120s19a_outputObj = new L120S19A(mainId, ClsConstants.L120S19A_ItemTypeCode.BRMP_termGroupRule_output, l140m01a_mainId, brmp004O_raw_json.toString(), onputItemVer, user.getUserId());
					clsService.save(l120s19a_inputObj, l120s19a_outputObj);
				}
				// J-113-0285 因應歡喜信貸審核需求，申請新增徵信及簽報書書面相關欄位，詳如附件
				if(c101m01a!=null){//個金徵信發查要寫回個金徵信的欄位
					try {
						Brmp004O brmpOutput = 
							new ObjectMapper().readValue(
									JSONObject.fromObject(brmp004O_raw_json.toString()).toString(), Brmp004O.class);
						if(brmpOutput != null && Util.isNotEmpty(brmpOutput)){
							String termGroup = Util.trim(brmpOutput.getResult().getTermGroup());
							String applyDBRType = Util.trim(brmpOutput.getResult().getApplyDBRType());
							if(Util.isNotEmpty(termGroup)){
								// 找到C101S01B
								C101S01B c101s01b = cls1131Service.findModelByKey(C101S01B.class, mainId,
										c101m01a.getCustId(), c101m01a.getDupNo());
								if(c101s01b != null && Util.isNotEmpty(c101s01b)){
									c101s01b.setTermGroup(termGroup);
									c101s01b.setApplyDBRType(applyDBRType);
									cls1131Service.save(c101s01b);
								}
							}
						}
					} catch (IOException e) {
						logger.error("save c101s01b TermGroup error, c101m01a MAINID=[" + mainId + "],brmp004data=["
								+ brmp004O_raw_json.toString() + "]");
					}
				}
			}
		}
		return result;
	}
	
	/**
	 * 製作 決策api /api/eloan/termGroupRule input
	 * @param mainid
	 * @param custid 客戶統編(不含重複碼)
	 * @param lnClass 產品代號
	 * @param jobCode 行業代碼
	 * @param positionCode 職位代碼
	 * @param regCapital 資本額
	 * @param payAmt 年收入
	 * @return
	 */
	private Brmp004I brmp_termGroupRule_buildInput(String mainid, String custid, String lnClass, String jobCode, String positionCode, 
			BigDecimal regCapital, BigDecimal payAmt){
		Brmp004I brmp004InputObj = new Brmp004I();
		if(true){
			brmp004InputObj.setUuid(IDGenerator.getUUID());
			brmp004InputObj.setMainId(mainid);
			brmp004InputObj.setCustId(custid);
			brmp004InputObj.setLnClass(lnClass);
			brmp004InputObj.setJobCode(jobCode);
			brmp004InputObj.setPositionCode(positionCode);
			brmp004InputObj.setRegCapital(regCapital);
			brmp004InputObj.setPayAmt(payAmt);
		}
		return brmp004InputObj;
	}
	
	/**
	 * 取得該筆徵信作業最後一次 call 決策api /api/eloan/termGroupRule 搜尋條件/查詢結果
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult show_last_brmp_termGroupRule(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainid = Util.trim(params.getString("mainId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String checkDate = Util.trim(sysparamService.getParamValue("J-112-0467_CHKDATE"));
		
		//取得查詢結果
		boolean hasbrmp004 = false;
		Brmp004O brmpOutput = null;
		String brmpErrorMsg = "";
		String brmpTermGroup = "";
		String brmpApplyDBRType = "";
		
		if(clsService.is_function_on_codetype("J-113-0285")){
			//改成撈既有欄位
			Map<String,String> termGroupResultMap = cls1131Service.findTermGroup(mainid, custId, dupNo);
			if(termGroupResultMap != null && Util.isNotEmpty(termGroupResultMap)){
				if(Util.equals("Y", Util.trim(termGroupResultMap.get("haveResult")))){
					hasbrmp004 = true;
					brmpErrorMsg = Util.trim(termGroupResultMap.get("errorMsg"));
					brmpTermGroup = Util.trim(termGroupResultMap.get("termGroup"));
					brmpApplyDBRType = Util.trim(termGroupResultMap.get("applyDBRType"));
				}
			}
		}else{
			L120S19A l120s19a = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainid, ClsConstants.L120S19A_ItemTypeCode.BRMP_termGroupRule_output);
			if(l120s19a!=null){
				try{
					ObjectMapper objectMapper = new ObjectMapper();
					brmpOutput = objectMapper.readValue(JSONObject.fromObject(l120s19a.getJsonData()).toString(), Brmp004O.class);
					hasbrmp004 = true;
					brmpErrorMsg = Util.trim(brmpOutput.getErrorMsg());
					brmpTermGroup = Util.trim(brmpOutput.getResult().getTermGroup());
					brmpApplyDBRType = Util.trim(brmpOutput.getResult().getApplyDBRType());
				}catch(Exception e){
					logger.error(StrUtils.getStackTrace(e));
					brmpOutput = null;
					hasbrmp004 = false;
				}
			}
		}
	
		result.set("mainid", mainid);
		result.set("checkDate", checkDate);
		result.set("hasbrmp004", hasbrmp004);
//		result.set("brmp004data", new CapAjaxFormResult(brmpOutput));
		result.set("brmpErrorMsg", brmpErrorMsg);
		result.set("brmpTermGroup", brmpTermGroup);
		result.set("brmpApplyDBRType", brmpApplyDBRType);
		
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult build_C101S01S_EJCIC_T70(PageParameters params) throws CapException, ClientProtocolException,
			IOException {
		// 檢核是否 stop 連至外部系統
		verify_call_outerSys_EJ();
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		_oneBtnQueryAuthChk(user, true, false, false);
		
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
		String custId = Util.trim(c101m01a.getCustId());
		String dupNo = Util.trim(c101m01a.getDupNo());
		String branchNo = c101m01a.getOwnBrId();
		
		C101S01E c101se01e = this.clsService.findC101S01E(c101m01a);
		if(c101se01e == null){
			throw new CapMessageException("請先按儲存，再執行此功能", getClass());
		}

		try {
			

			// ELF690_EJ_TMESTAMP 有時間, 但 MIS.TAS700 沒資料, 代表超過15天資料被清掉了
			boolean isQueryT70 = this.cls1131Service.isQueryEjcicT70Info(custId, dupNo, user.getUnitNo(), mainId);
			
			if(isQueryT70){
				
				/*  查詢理由:A4A => 
				 *	第一層 A.新業務申請
				 *  第二層 4放款業務(c) 
				 *	第三層 A.取得當事人書面同意
				 */
				this.misBaseService.insertELF690_EjcicT70Inquiry(custId, dupNo, "A4A", user.getUnitNo(), user.getUserId());
				this.cls1131Service.deleteC101S02S(mainId, custId, dupNo);
				this.cls1131Service.saveC101S02S(mainId, custId, dupNo, branchNo);
			}
			
			this.cls1131Service.setC101s01eVersion("2.0", c101m01a);

		} catch (Exception e) {
			throw new CapException(e, getClass());
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult isCloseFinHoldingDefaultDeliveryFunction(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean isClose = this.cls1131Service.isCloseFinHoldingDefaultDeliveryFunction();
		result.set("isCloseFunc", isClose ? "Y" : "N");
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult testRatingDocDW(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String proc_model = "R";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		ISearch search = clsService.getMetaSearch();
		if(true){
			search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "ownBrId", user.getUnitNo());
//			search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "grdCDate", "2023-07-04");			
//			search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "custId", "K297777898");
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
			search.setFirstResult(0).setMaxResults(Integer.MAX_VALUE);
			//==========	
			if(Util.equals("G", proc_model)){
				search.addSearchModeParameters(SearchMode.EQUALS, "varVer", scoreService.get_Version_HouseLoan());
				for (C101S01G model : c101s01gDao.find(search)){
					if(LMSUtil.check2(model.getCustId())){
						_upDW_C101(model, null, null, null, null, null);			
					}
				}
			}else if(Util.equals("Q", proc_model)){
				search.addSearchModeParameters(SearchMode.EQUALS, "varVer", scoreService.get_Version_NotHouseLoan());
				
				for (C101S01Q model : c101s01qDao.find(search)){
					if(LMSUtil.check2(model.getCustId())){
						_upDW_C101(null, model, null, null, null, null);			
					}
				}
			}else if(Util.equals("R", proc_model)){
				search.addSearchModeParameters(SearchMode.EQUALS, "varVer", scoreService.get_Version_CardLoan());
				
				for (C101S01R model : c101s01rDao.find(search)){
					if(LMSUtil.check2(model.getCustId())){
						_upDW_C101(null, null, model, null, null, null);			
					}
				}
			}
			
			//測試雙軌
			boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
			if(scoreDoubleTrack){
				ISearch search_n = clsService.getMetaSearch();
				search_n.addSearchModeParameters(SearchMode.GREATER_EQUALS, "ownBrId", user.getUnitNo());
				search_n.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
				search_n.setFirstResult(0).setMaxResults(Integer.MAX_VALUE);
				
				if(Util.equals("G", proc_model)){
					search_n.addSearchModeParameters(SearchMode.EQUALS, "varVer", ClsScoreUtil.V3_0_HOUSE_LOAN);
					for (C101S01G_N model : c101s01g_nDao.find(search_n)){
						if(LMSUtil.check2(model.getCustId())){
							_upDW_C101(null, null, null, model, null, null);			
						}
					}
				}else if(Util.equals("Q", proc_model)){
					search_n.addSearchModeParameters(SearchMode.EQUALS, "varVer", ClsScoreUtil.V4_0_NOT_HOUSE_LOAN);
					
					for (C101S01Q_N model : c101s01q_nDao.find(search_n)){
						if(LMSUtil.check2(model.getCustId())){
							_upDW_C101(null, null, null, null, model, null);			
						}
					}
				}else if(Util.equals("R", proc_model)){
					search_n.addSearchModeParameters(SearchMode.EQUALS, "varVer", ClsScoreUtil.V4_0_CARD_LOAN);
					
					for (C101S01R_N model : c101s01r_nDao.find(search_n)){
						if(LMSUtil.check2(model.getCustId())){
							_upDW_C101(null, null, null, null, null, model);			
						}
					}
				}
			}
		}
		
		return result;
	}

	private void _upDW_C101(C101S01G model_G, C101S01Q model_Q, C101S01R model_R, C101S01G_N model_NG, C101S01Q_N model_NQ, C101S01R_N model_NR) throws CapException{
		C120M01A c120m01a = null;
		C120S01A c120s01a = null;
		C120S01B c120s01b = null;
		C120S01C c120s01c = null;
		C120S01E c120s01e = null;
		C120S01G c120s01G = null;
		C120S01Q c120s01Q = null;
		C120S01R c120s01R = null;

		if(true){
			String mainId = "";
			String custId = "";
			String dupNo = "";
			if(true){
				if(model_G!=null){
					mainId = model_G.getMainId();
					custId = model_G.getCustId();
					dupNo = model_G.getDupNo();
					c120s01G = LMSUtil.copy_to_C120S01G(model_G);
				}else if(model_Q!=null){
					mainId = model_Q.getMainId();
					custId = model_Q.getCustId();
					dupNo = model_Q.getDupNo();
					c120s01Q = LMSUtil.copy_to_C120S01Q(model_Q);
				}else if(model_R!=null){
					mainId = model_R.getMainId();
					custId = model_R.getCustId();
					dupNo = model_R.getDupNo();
					c120s01R = LMSUtil.copy_to_C120S01R(model_R);
				}else if(model_NG!=null){
					mainId = model_NG.getMainId();
					custId = model_NG.getCustId();
					dupNo = model_NG.getDupNo();
				}else if(model_NQ!=null){
					mainId = model_NQ.getMainId();
					custId = model_NQ.getCustId();
					dupNo = model_NQ.getDupNo();
				}else if(model_NR!=null){
					mainId = model_NR.getMainId();
					custId = model_NR.getCustId();
					dupNo = model_NR.getDupNo();
				}else{
					return;
				}
			}
			if(true){
				C101M01A c101m01a = c101m01aDao.findByUniqueKey(mainId, null, custId, dupNo);
				if(true){
					c120m01a = new C120M01A();
					c120m01a.setCustId(c101m01a.getCustId());
					c120m01a.setDupNo(c101m01a.getDupNo());
					c120m01a.setModelTyp("");// 房貸模型上線前舊案
					//=============
					c120m01a.setPrimary_card(c101m01a.getPrimary_card());
					c120m01a.setAdditional_card(c101m01a.getAdditional_card());
					c120m01a.setBusiness_or_p_card(c101m01a.getBusiness_or_p_card());
				}
			}
			c120s01a = LMSUtil.copy_to_C120S01A(c101s01aDao.findByUniqueKey(mainId, custId, dupNo));
			c120s01b = LMSUtil.copy_to_C120S01B(c101s01bDao.findByUniqueKey(mainId, custId, dupNo));
			c120s01c = LMSUtil.copy_to_C120S01C(c101s01cDao.findByUniqueKey(mainId, custId, dupNo));
			c120s01e = LMSUtil.copy_to_C120S01E(c101s01eDao.findByUniqueKey(mainId, custId, dupNo));
			//copy from c101 to c120
			if(model_G!=null){
				clsService.verify_GradeModel_upDW(c120m01a, c120s01a, c120s01b, c120s01c, c120s01G, null, null, c120s01e);
			}
			if(model_Q!=null){
				clsService.verify_GradeModel_upDW(c120m01a, c120s01a, c120s01b, c120s01c, null, c120s01Q, null, c120s01e);
			}
			if(model_R!=null){
				clsService.verify_GradeModel_upDW(c120m01a, c120s01a, c120s01b, c120s01c, null, null, c120s01R, c120s01e);
			}
			if(model_NG!=null){
				clsService.verify_GradeModel_U_upDW(c120m01a, c120s01a, c120s01b, c120s01c, model_NG, null, null, c120s01e);
			}
			if(model_NQ!=null){
				clsService.verify_GradeModel_U_upDW(c120m01a, c120s01a, c120s01b, c120s01c, null, model_NQ, null, c120s01e);
			}
			if(model_NR!=null){
				clsService.verify_GradeModel_U_upDW(c120m01a, c120s01a, c120s01b, c120s01c, null, null, model_NR, c120s01e);
			}
		}
	}
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult brmp_autoCheck_new(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		if (Util.isEmpty(Util.trim(params.getString("loanAmt")))) {
			throw new CapMessageException("系統初評 > 申貸額度不可空白",
					getClass());
		}
		if (Util.isEmpty(Util.trim(params.getString("lnYear"))) || Util.isEmpty(Util.trim(params.getString("lnMonth")))) {
			throw new CapMessageException("系統初評 > 清償期限不可空白",
					getClass());
		}
		if (Util.isEmpty(Util.trim(params.getString("installmentPay")))) {
			throw new CapMessageException("系統初評 > 期付金不可空白",
					getClass());
		}
		String brNo = user.getUnitNo();
		C101M01A c101m01a = clsService
				.findC101M01A_brIdDup(brNo, custId, dupNo);
		C101S01E c101s01e = clsService.findC101S01E(c101m01a);
		if("3".equals(c101s01e.getIsQdata30())){
			// 聯徵T70尚未查詢成功
			throw new CapMessageException("系統初評 > "+ getI18nMsg("message.ejcicT70.inquiryNotBeenSuccessfulYet"),
					getClass());
		}

		int loanAmt = params.getInt("loanAmt", 0);
		BigDecimal applyAmt = loanAmt!= 0 ? ClsUtility.get_value_multiplyWAN(Util.parseBigDecimal(loanAmt)) : BigDecimal.ZERO;
		int lnYear = params.getInt("lnYear", 0);
		int lnMonth = params.getInt("lnMonth", 0);
		//int pConBegEnd_fg = Util.parseInt(params.getString("pConBegEnd_fg"));
		String onlineCaseNo = Util.trim(params.getString("onlineCaseNo"));
//		String l140m01a_introduceSrc = Util.trim(params
//				.getString("introduceSrc"));

//		String termGroup = Util.trim(params.getString("termGroup"));
		String termGroup = "";
//		String termGroupSub = Util.trim(params.getString("termGroupSub"));

//		String esggnLoanFg = Util.trim(params.getString("esggnLoanFg"));
//		String esggtype = Util.trim(params.getString("esggtype"));
//		String esggtypeZMemo = Util.trim(params.getString("esggtypeZMemo"));
		BigDecimal installmentPay = new BigDecimal(params.getInt("installmentPay", 0));
		// ~~~~~~

//		String lnap = brmp_lnap_from_creditLoanPurpose(creditLoanPurpose);
		C101S01A c101s01a = clsService.findC101S01A(c101m01a);
		C101S01B c101s01b = clsService.findC101S01B(c101m01a);
		C101S01C c101s01c = clsService.findC101S01C(c101m01a);
		C101S01D c101s01d = clsService.findC101S01D(c101m01a);
		C101S01J c101s01j = clsService.findC101S01J(c101m01a);
		C101S01Q c101s01q = clsService.findC101S01Q(c101m01a);
		C101S01R c101s01r = clsService.findC101S01R(c101m01a);
		C101S02C c101s02c = cls1131Service.findC101S02C(c101m01a.getMainId());
		List<C101S01W> c101s01ws = clsService
				.findC101S01W(c101m01a.getMainId(), c101m01a.getCustId(),
						c101m01a.getDupNo());

		if (c101m01a == null) {
			throw new CapMessageException(custId + "-" + dupNo + "查無「個金徵信」資料",
					getClass());
		}
		if (Util.isEmpty(Util.trim(c101s01b.getPositionType()))) {
			throw new CapMessageException(c101s01b.getCustId()
					+ "{c101s01b.positionType is empty}", getClass());
		}
		String amlRefNo = "";
		String amlRefOid = "";
		if (c101s01j != null) {
			amlRefNo = Util.trim(c101s01j.getAmlRefNo());
			amlRefOid = Util.trim(c101s01j.getAmlRefOid());
		}
//		boolean s01s_dataType1_abnormal = c101s01s_dataType1_abnormal(clsService
//				.findC101S01S_byIdDupDataType(c101m01a.getMainId(),
//						c101m01a.getCustId(), c101m01a.getDupNo(),
//						ClsConstants.C101S01S_dataType.往來客戶信用異常資料));

		List<C101S04W> c101s04w_list = clsService
				.findC101S04W_by_mainId_custId(c101m01a.getMainId(), custId);
		L120S09B l120s09b = clsService.findL120S09B_refNo_or_oid(amlRefNo,
				amlRefOid);
		// ~~~~~~
		boolean isBankMan_borrower = misBaseService.isBankMan_on_the_job(custId);
		String target_prodKind = prodService.get_brmp_target_prodKind(
				c101s01e.getIsQdata2(), c101s01e.getIsQdata3(),
				isBankMan_borrower);
		//改取個金徵信上的選項
		target_prodKind = Util.trim(params.getString("prodKind"));
		//取得客群結果
		L120S19A l120s19b = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(c101m01a.getMainId(), ClsConstants.L120S19A_ItemTypeCode.BRMP_termGroupRule_output);
		Brmp004O brmpOutput = null;
		if (l120s19b != null) {
			try {
				ObjectMapper objectMapper = new ObjectMapper();
				brmpOutput = objectMapper.readValue(JSONObject.fromObject(l120s19b.getJsonData()).toString(), Brmp004O.class);
				termGroup = brmpOutput.getResult().getTermGroup();
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
				brmpOutput = null;
			}
		}
		if (misBaseService.isBankMan_on_the_job(custId)) {
			termGroup = "E";
		}

		Brmp005I brmp005i_obj = brmp_autoCheck_buildInput(
				brNo,
				custId,
				dupNo,
				c101m01a,
				c101s01a,
				c101s01b,
				c101s01c,
				c101s01e,
				c101s01q,
				c101s01r,
				l120s09b,
				 false, termGroup,
				applyAmt, lnYear, lnMonth,
				onlineCaseNo, target_prodKind, c101s01ws, installmentPay);

		JSONObject brmp005O_raw_json = brmpGwClient
				.send_autoCheck_raw_json(brmp005i_obj);
		Brmp005O brmp005o_obj = brmpGwClient
				.send_autoCheck_rtnJsonObj(brmp005O_raw_json);
		if (brmp005o_obj == null) {
			throw new CapMessageException("決策平台執行「系統初評」非回傳JSON格式", getClass());
		}
		if (brmp005o_obj != null && !ClsUtility.is_Brmp005O_ok(brmp005o_obj)) {
			logger.error("{" + custId + "-" + dupNo + "}is_Brmp005O_ok==false");
			logger.error(brmp005O_raw_json.toString());
			throw new CapMessageException("決策平台執行「系統初評」失敗", getClass());
		}

		if (true) {
			List<String> rejectDesc_list = ClsUtility
					.get_Brmp005O_rejectDesc(brmp005o_obj);
			if (rejectDesc_list.size() > 0) {
				logger.info("{" + custId + "-" + dupNo + "}rejectDesc_list="
						+ StringUtils.join(rejectDesc_list, "、"));
				/*
				 * if(l120m01a!=null){
				 * l120m01a.setDeletedTime(CapDate.getCurrentTimestamp());
				 * clsService.save(l120m01a); }
				 */
				// 不出現提示訊息 result.set("rejectDesc",
				// "客戶 "+custId+"-"+dupNo+" 不應承做：<br/>"+StringUtils.join(rejectDesc_list,
				// "<br/>"));
			}
//			List<String> statusMissing_list = ClsUtility
//					.get_Brmp005O_statusMissing(brmp005o_obj);
//			if (statusMissing_list.size() > 0) {
//				logger.info("{" + custId + "-" + dupNo + "}statusMissing_list="
//						+ StringUtils.join(statusMissing_list, "、"));
//				// 不出現提示訊息 result.set("statusMissing",
//				// "客戶 "+custId+"-"+dupNo+" 的「消貸檢核」決策回傳值異常：<br/>"+StringUtils.join(statusMissing_list,
//				// "<br/>"));
//			}
		}

		C122M01A c122m01a = cls1220Service
				.getC122M01A_byMainId(mainId);
		if (c122m01a!=null) {
			Boolean isAppove =false;
			Boolean isReject =false;
			Boolean isNeedCheck =false;
			for (Brmp005O.Brmp005O_result_policyObj obj:brmp005o_obj.getResult().getPolicyResult())
			{
				if (Util.equals(obj.getActionType(), "D")) {
					isReject = true;
				} else if (Util.equals(obj.getActionType(), "Q")) {
					isNeedCheck = true;
				} else {
					isAppove = true;
				}
			}
			if (isReject) {
				if (!c122m01a.getDocStatus().equals(UtilConstants.C122_DocStatus.系統建議婉拒)) {
					c122m01a.setDocStatus(UtilConstants.C122_DocStatus.系統建議婉拒);
					c101s02c.setDocStatus(UtilConstants.C122_DocStatus.系統建議婉拒);
				}
			} else if (isNeedCheck) {
				if (!c122m01a.getDocStatus().equals(UtilConstants.C122_DocStatus.人工審核)) {
					c122m01a.setDocStatus(UtilConstants.C122_DocStatus.人工審核);
					c101s02c.setDocStatus(UtilConstants.C122_DocStatus.人工審核);
				}
			} else {
				if (!c122m01a.getDocStatus().equals(UtilConstants.C122_DocStatus.系統建議核准)) {
					c122m01a.setDocStatus(UtilConstants.C122_DocStatus.系統建議核准);
					c101s02c.setDocStatus(UtilConstants.C122_DocStatus.系統建議核准);
				}
			}
			CapAjaxFormResult rtn_json = new CapAjaxFormResult(cls1131Service.getAutoCheck(brmp005o_obj));
			result.set("rtn_json", rtn_json);
			//案件狀態
			String docstatus = Util.trim(c101s02c.getDocStatus());
			if (Util.isNotEmpty(docstatus)) {
				Map<String, String> _DocStatusNewDescMap = cls1220Service.get_DocStatusNewDescMap();
				rtn_json.set("autoCheckResultDesc", _DocStatusNewDescMap.get(docstatus));
				result.set("rtn_json",rtn_json);
			}
		}

		if (c101m01a != null) {
			L120S19A l120s19a_inputObj = null;
			L120S19A l120s19a_outputObj = null;
			if (true) {
				// 決策平台回傳 的字串，長度超過 BGWDATA的長度，為進行 debug，先把 jsonString 存入
				// DB（避免在產出 L140M01A 時，因為 Exception 導致看不到回傳的 jsonString）
				// 待產出 L140M01A 之後，再把 l140m01a_mainId 更新到 L120S19A
				String l140m01a_mainId = "";
				L120S19A l120s19a_latestInput = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(c101m01a.getMainId(), ClsConstants.L120S19A_ItemTypeCode.BRMP_autoCheck_input);
				L120S19A l120s19a_latestOutput = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(c101m01a.getMainId(), ClsConstants.L120S19A_ItemTypeCode.BRMP_autoCheck_output);
				BigDecimal inputItemVer = (l120s19a_latestInput ==null) ? BigDecimal.ONE : l120s19a_latestInput.getItemVersion().add(BigDecimal.ONE);
				BigDecimal onputItemVer = (l120s19a_latestOutput ==null) ? BigDecimal.ONE : l120s19a_latestOutput.getItemVersion().add(BigDecimal.ONE);
				l120s19a_inputObj = new L120S19A(
						c101m01a.getMainId(),
						ClsConstants.L120S19A_ItemTypeCode.BRMP_autoCheck_input,
						l140m01a_mainId, BrmpGwClient
						.convert_to_jsonStr(brmp005i_obj),
						inputItemVer, user.getUserId());
				l120s19a_outputObj = new L120S19A(
						c101m01a.getMainId(),
						ClsConstants.L120S19A_ItemTypeCode.BRMP_autoCheck_output,
						l140m01a_mainId, brmp005O_raw_json.toString(),
						onputItemVer, user.getUserId());
				clsService.save(l120s19a_inputObj, l120s19a_outputObj);
			}
		}
		return result;

	}
	private Brmp005I brmp_autoCheck_buildInput(String brNo, String custId,
												 String dupNo, C101M01A c101m01a, C101S01A c101s01a,
												 C101S01B c101s01b, C101S01C c101s01c,
												 C101S01E c101s01e, C101S01Q c101s01q, C101S01R c101s01r,
												 L120S09B l120s09b,
												 boolean isC120, String termGroup, BigDecimal applyAmt,
												 int lnYear, int lnMonth,String onlineCaseNo,
											     String target_prodKind,
												 List<C101S01W> c101s01ws,BigDecimal installmentPay)
			throws CapMessageException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		if (Util.equals("E", termGroup) && !misBaseService.isBankMan_on_the_job(custId)) {
			throw new CapMessageException("此客戶不適用「行員方案」", getClass());
		}
		if (Util.isEmpty(termGroup)) {
			throw new CapMessageException("「客群方案」不可空白", getClass());
		}
		if (Util.isEmpty(target_prodKind)) {
			throw new CapMessageException("輸入資料無對應的「產品種類」", getClass());
		}
		if (Util.equals(ProdService.ProdKindEnum.一般消貸含團體消貸_07.getCode(),
				target_prodKind)) {
			if (Util.isEmpty(c101s01q.getGrade3())) {
				throw new CapMessageException("輸入資料無對應的「評等」", getClass());
			}
		} else {
			if (Util.isEmpty(c101s01r.getGrade3())) {
				throw new CapMessageException("輸入資料無對應的「評等」", getClass());
			}
		}
		// ==============================================
		Brmp005I brmpObj = new Brmp005I();
		if (true) {
			brmpObj.setUuid(IDGenerator.getUUID());
			brmpObj.setCustId(custId);
			brmpObj.setCustIdDupNo(dupNo);

			brmpObj.setBirthDate(StringUtils.replace(
					Util.trim(TWNDate.toAD(c101s01a.getBirthday())), "-", "/"));

			brmpObj.setApplyAmt(applyAmt);
			brmpObj.setLnYear(lnYear);
			brmpObj.setLnMonth(lnMonth);
			brmpObj.setOnlineCaseNo(onlineCaseNo);

			// 2:藍領, 3:業務職}
			if (true) {
				int[] arr_Seniority_currnt_ex = brmp_s01b_seniorityYear_month___exSeniorityYear_month(c101s01b);

				brmpObj.setSeniorityYear(arr_Seniority_currnt_ex[0]); // 現職年資(年)
				brmpObj.setSeniorityMonth(arr_Seniority_currnt_ex[1]); // 現職年資(月)
			}
			brmpObj.setPayAmt(ClsUtility.s01c_pAllAmt___yearIncome(c101s01b,
					c101s01ws)); // 借款人年薪
			if (Util.equals(ProdService.ProdKindEnum.一般消貸含團體消貸_07.getCode(),
					target_prodKind)) {
				brmpObj.setModelKindGrade3(c101s01q.getGrade3());
			} else {
				brmpObj.setModelKindGrade3(c101s01r.getGrade3()); // 非房貸(?)最終評等,
				// 本行消金信用評等作業要點
				// 第三條（適用對象）【借款人、G-連帶保證人或
				// C-共同借款人】，應分別辦理信用評等，並就其評等結果擇一適用。
			}

			//計算代償
			if (true) {
				JSONObject json = JSONObject.fromObject(c101s01c
						.getRateData());
				BigDecimal othercompensationAmt = BigDecimal.ZERO;
				BigDecimal megacompensationAmt = BigDecimal.ZERO;
				if (Util.isNotEmpty(json)) {
					int jcicCount = Util.parseInt(json.optString("jcicCount"));
					int jcicCreditCount = Util.parseInt(json.optString("jcicCreditCount"));
					//貸款代償
					if (jcicCount > 0 ){
						for (int i = 1; i <= jcicCount; i++) {
							String jcic_period_needmegapay = Util.trim(json.optString("jcic_period_needmegapay_"+i));
							if ( Util.equals(jcic_period_needmegapay,UtilConstants.DEFAULT.是)) {
								String loan= NumConverter.delCommaString(Util.trim(json.optString("jcic_loanamt_"+i)));
								String repaymentProductType = Util.trim(json.optString("jcic_item_"+i));
								String jcic_bankcode = Util.trim(json.optString("jcic_bankcode_"+i));
								boolean isMatch = false;
								List<CodeType> jcicCodeTypes = codeTypeService.findByCodeTypeList("repaymentProductId");
								for (CodeType jcicitem : jcicCodeTypes) {
									if (repaymentProductType.equals(jcicitem.getCodeValue())) {
										isMatch = true;
										break;
									}
								}
								if (isMatch) {
									BigDecimal jcic_loan = Util.parseBigDecimal(loan).multiply(new BigDecimal(1000));
									if (jcic_bankcode.contains("017")) {
										megacompensationAmt = megacompensationAmt.add(jcic_loan);
									} else {
										othercompensationAmt = othercompensationAmt.add(jcic_loan);
									}
								}
							}
						}
					}

					//信用卡代償
					if (jcicCreditCount > 0 ){
						for (int i = 1; i <= jcicCreditCount; i++) {
							String jcic_credit_bankcode = Util.trim(json.optString("jcic_credit_bankcode_"+i));
							//循環
							String jcic_credit_revol_needmegapay = Util.trim(json.optString("jcic_credit_revol_needmegapay_"+i));
							if ( Util.equals(jcic_credit_revol_needmegapay,UtilConstants.DEFAULT.是)) {
								String credit_revol=NumConverter.delCommaString(Util.trim(json.optString("jcic_credit_revol_"+i)));
								BigDecimal jcic_credit_revol = Util.parseBigDecimal(credit_revol);
								if (jcic_credit_bankcode.contains("017")) {
									megacompensationAmt = megacompensationAmt.add(jcic_credit_revol);
								} else {
									othercompensationAmt = othercompensationAmt.add(jcic_credit_revol);
								}
							}
							//分期償還
							String jcic_credit_pre_needmegapay = Util.trim(json.optString("jcic_credit_pre_needmegapay_"+i));
							if ( Util.equals(jcic_credit_pre_needmegapay,UtilConstants.DEFAULT.是)) {
								String credit_pre=NumConverter.delCommaString(Util.trim(json.optString("jcic_credit_pre_"+i)));
								BigDecimal jcic_credit_pre = Util.parseBigDecimal(credit_pre);
								if (jcic_credit_bankcode.contains("017")) {
									megacompensationAmt = megacompensationAmt.add(jcic_credit_pre);
								} else {
									othercompensationAmt = othercompensationAmt.add(jcic_credit_pre);
								}
							}
						}
					}
				}
				brmpObj.setMegaCompensationAmt(megacompensationAmt);
				brmpObj.setOtherCompensationAmt(othercompensationAmt);
			}

			//判斷是否為行員
			brmpObj.setIsNowEmployee( misBaseService.isBankMan_on_the_job(custId)? "1" : "0");
			//期付金
			brmpObj.setInstallmentPay(installmentPay);
			//本行往來戶負面信用資料
			String clientAbnormalData = null;
			C101S01S c101s01sAbData = c101s01sDao.findByUniqueKey(c101m01a.getMainId(), custId, dupNo, ClsConstants.C101S01S_dataType.往來客戶信用異常資料, "1");
			if (c101s01sAbData != null) {
				clientAbnormalData = c101s01sAbData.getDataStatus().contains("1") ? ClsConstants.BRMP_domainvalue.是 : ClsConstants.BRMP_domainvalue.否;
			} else {
				clientAbnormalData = null;
			}
			brmpObj.setMegaNegativeCredit(clientAbnormalData);
			//本行利害關係人
			String isQdata2 =  Util.equals(c101s01e.getIsQdata2(),"1")? ClsConstants.BRMP_domainvalue.是 : Util.equals(c101s01e.getIsQdata2(),"2")? ClsConstants.BRMP_domainvalue.否 :null;
			//金控利害關係人(44條)
			String isQdata3 =  Util.equals(c101s01e.getIsQdata3(),"1")? ClsConstants.BRMP_domainvalue.是 : Util.equals(c101s01e.getIsQdata3(),"2")? ClsConstants.BRMP_domainvalue.否 :null;
			//退票紀錄
			String isQdata9 =  Util.equals(c101s01e.getIsQdata9(),"1")? ClsConstants.BRMP_domainvalue.是 : Util.equals(c101s01e.getIsQdata9(),"2")? ClsConstants.BRMP_domainvalue.否 :null;
			//拒絕往來紀錄
			String isQdata10= Util.equals(c101s01e.getIsQdata10(),"1")? ClsConstants.BRMP_domainvalue.是 : Util.equals(c101s01e.getIsQdata9(),"2")? ClsConstants.BRMP_domainvalue.否 :null;
			brmpObj.setMegaRejectCust(isQdata9);
			brmpObj.setMegaRefundCust(isQdata10);
			brmpObj.setBankingInterestedPerson(isQdata2);
			brmpObj.setFinancialControlInterestedPerson(isQdata3);
			// 有婉卻紀錄需須注意辦理註記{0:否,1:是}
			brmpObj.setDeclineRecordFlag(Util.equals("1",
					c101s01e.getIsQdata1()) ? ClsConstants.BRMP_domainvalue.是
					: Util.equals(c101s01e.getIsQdata1(),"2")? ClsConstants.BRMP_domainvalue.否 :null); // 有婉卻紀錄需須注意辦理註記{0:否,
			// 1:是}

			//負面新聞
			JSONObject queryDataJSON = JSONObject.fromObject(c101s01e.getWiseNews());
			JSONArray datas = queryDataJSON.optJSONArray("data");
			brmpObj.setHasBadNew(datas.isEmpty()? ClsConstants.BRMP_domainvalue.否 : ClsConstants.BRMP_domainvalue.是);

			//身分證領換補資料查詢結果
			C101S01S c101s01s = this.c101s01sDao.findByUniqueKey(c101m01a.getMainId(), custId, dupNo, ClsConstants.C101S01S_dataType.行內_身分證驗證, "1");
			String hasIdCardReplace = null;
			if(c101s01s != null){
				hasIdCardReplace = Util.equals(c101s01s.getDataStatus(),"0") ? ClsConstants.BRMP_domainvalue.否 : ClsConstants.BRMP_domainvalue.是; //0:相符、1:不相符
			}
			brmpObj.setHasIdCardReplace(hasIdCardReplace);
		}
		return brmpObj;
	}
	
	//J-113-0341 個金徵信作業新增「年輕族群客戶加強關懷提問單」
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult youngCareListShowHide(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean isShow = false;
		String C101S01AForm = Util.trim(params.getString("C101S01AForm"));
		JSONObject C101S01AJson = DataParse.toJSON(C101S01AForm);
		String birthday = Util.trim(C101S01AJson.get("birthday"));
		boolean J_113_0341_ON = clsService.is_function_on_codetype("J_113_0341_ON");
		if(J_113_0341_ON && Util.isNotEmpty(birthday)){
			Date dt_birthday = CapDate.parseDate(birthday);
			int age = cls1131Service.getAge(dt_birthday);
			if(18 <= age && age < 22){
				isShow = true;
			}
		}
		result.set("isShow", isShow);
		return result;
	}
		
	private int[] brmp_s01b_seniorityYear_month___exSeniorityYear_month(
			C101S01B c10s01b) {
		int[] arr = new int[] { 0, 0, 0, 0 };
		BigDecimal s01b_seniority = c10s01b.getSeniority();
		Date workDate = c10s01b.getWorkDate(); // 到職日期
		String isSameWorkAttributes = Util.trim(c10s01b
				.getIsSameWorkAttributes()); // 前職與現職同屬性 (限前一份工作)

		int seniorityYear = 0; // 現職年資(年)
		int seniorityMonth = 0; // 現職年資(月)
		int exSeniorityYear = 0; // 前職年資(年)
		int exSeniorityMonth = 0; // 前職年資(月)
		// ===============================================
		// 判斷 logic

		if (true) {
			Integer[] seniorityYM_arr = ClsUtility
					.seniorityYM_decode(s01b_seniority);
			seniorityYear = seniorityYM_arr[0]; // 現職年資(年)
			seniorityMonth = seniorityYM_arr[1]; // 現職年資(月)
		}
		// ===============================================
		if (true) { // 現職
			arr[0] = seniorityYear;
			arr[1] = seniorityMonth;
		}
		if (true) { // 前職
			arr[2] = exSeniorityYear;
			arr[3] = exSeniorityMonth;
		}
		return arr;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult isOpenMortgageRatioCheck(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("isOpen", this.clsService.isOpenMortgageRatioCheck() ? "Y" : "N");
		return result;
	}
}
