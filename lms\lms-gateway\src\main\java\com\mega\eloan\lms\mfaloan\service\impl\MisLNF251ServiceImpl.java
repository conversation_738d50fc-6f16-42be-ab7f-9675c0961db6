
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisLNF251Service;

/**<pre>
 *
 *
 */
@Service
public class MisLNF251ServiceImpl extends AbstractMFAloanJdbc implements MisLNF251Service {
	
	public Map<String, Object> findEarliestOverdueDate(String custId, String dupNo){
		return this.getJdbc().queryForMap("LNF251.findEarliestOverdueDate", new Object[]{custId + dupNo});
	}
}
