package com.mega.eloan.lms.fms.flow;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.dao.C900M01HDao;
import com.mega.eloan.lms.model.C900M01H;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;


@Component
public class CLS2601Flow extends AbstractFlowHandler {
	public static final String FLOW_CODE = "CLS2601Flow";


	@Resource
	C900M01HDao c900m01hDao;
	
	@Transition(node = "開始", value = "起案")
	public void init_flow(FlowInstance instance) {		
		String oid = Util.trim(instance.getId());
		
		C900M01H meta = c900m01hDao.findByOid(oid);
		{
			meta.setApprover(null);
			meta.setApproveTime(null);	
		}		
		c900m01hDao.save(meta);
	}
	
	
	@Transition(node = "確認", value = "核定")
	public void apply(FlowInstance instance) {
		String oid = Util.trim(instance.getId());
		
		C900M01H meta = c900m01hDao.findByOid(oid);
		{
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			meta.setApprover(user.getUserId());
			meta.setApproveTime(CapDate.getCurrentTimestamp());	
		}
		c900m01hDao.save(meta);
	}
	
	@Transition(node = "解除確認", value = "核定")
	public void removeApply(FlowInstance instance) {
		String oid = Util.trim(instance.getId());
		
		C900M01H meta = c900m01hDao.findByOid(oid);
		{
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			meta.setApprover(user.getUserId());
			meta.setApproveTime(CapDate.getCurrentTimestamp());	
		}
		c900m01hDao.save(meta);
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return C900M01H.class;
	}


	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return FlowDocStatusEnum.class;
	}
}