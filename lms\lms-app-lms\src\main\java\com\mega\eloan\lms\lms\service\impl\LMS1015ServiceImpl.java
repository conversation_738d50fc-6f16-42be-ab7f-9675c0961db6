package com.mega.eloan.lms.lms.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.ScoreJP;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ScoreServiceJP;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.C120S01BDao;
import com.mega.eloan.lms.dao.C120S01CDao;
import com.mega.eloan.lms.dao.C120S01DDao;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.dao.C121M01ADao;
import com.mega.eloan.lms.dao.C121M01BDao;
import com.mega.eloan.lms.dao.C121M01EDao;
import com.mega.eloan.lms.dao.C121M01FDao;
import com.mega.eloan.lms.dao.C121S01ADao;
import com.mega.eloan.lms.dao.L120S01MDao;
import com.mega.eloan.lms.dao.L120S01NDao;
import com.mega.eloan.lms.dao.L120S01ODao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.lms.pages.LMS1015M01Page;
import com.mega.eloan.lms.lms.service.LMS1015Service;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01B;
import com.mega.eloan.lms.model.C121M01E;
import com.mega.eloan.lms.model.C121M01F;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;


@Service("LMS1015Service")
public class LMS1015ServiceImpl extends AbstractCapService implements
		LMS1015Service {

	private static Logger logger = LoggerFactory
			.getLogger(LMS1015ServiceImpl.class);
	@Resource
	CLSService clsService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	TempDataService tempDataService;

	@Resource
	C120M01ADao c120m01aDao;
	
	@Resource
	C120S01ADao c120s01aDao;
	
	@Resource
	C120S01BDao c120s01bDao;

	@Resource
	C120S01CDao c120s01cDao;

	@Resource
	C120S01DDao c120s01dDao;
	
	@Resource
	C120S01EDao c120s01eDao;
	
	@Resource
	C121M01ADao c121m01aDao;
	
	@Resource
	C121S01ADao c121s01aDao;
	
	@Resource
	C121M01BDao c121m01bDao;
	
	@Resource
	C121M01FDao c121m01fDao;
	
	@Resource
	ScoreServiceJP scoreServiceJP;
	
	@Resource
	L120S01MDao l120s01mDao;
	
	@Resource
	L120S01NDao l120s01nDao;

	@Resource
	L120S01ODao l120s01oDao;
		
	@Resource
	DwdbBASEService dwdbBASEService;
	
	@Resource
	C121M01EDao c121m01eDao;
	
	@Override
	public String checkIncompleteMsg(C121M01A meta, List<String> adjReasonCnt, LinkedHashMap<String, String> adjReasonCfmMap){
		C121S01A c121s01a = clsService.findC121S01A(meta);	
		Properties prop_lms1015m01 = MessageBundleScriptCreator.getComponentResource(LMS1015M01Page.class);
		Properties prop_abstractOverSeaCLS = MessageBundleScriptCreator.getComponentResource(AbstractOverSeaCLSPage.class);
		List<C120M01A> c120m01a_list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
		List<C120M01A> c120m01a_shouldRating_list = clsService.filter_shouldRating(c120m01a_list);
				
		if(true){			
			List<String> panel1 = new ArrayList<String>();//文件資訊
			if(true){
				List<String> overRange = new ArrayList<String>();
				if (true) {
					if(meta.getLnYear()!=null && (meta.getLnYear()<0)){
						overRange.add(prop_lms1015m01.getProperty("tab01.lnPeriod")+"-"+prop_lms1015m01.getProperty("tab01.lnYear"));
					}
					if(meta.getLnMonth()!=null && (meta.getLnMonth()<0||meta.getLnMonth()>=12)){
						overRange.add(prop_lms1015m01.getProperty("tab01.lnPeriod")+"-"+prop_lms1015m01.getProperty("tab01.lnMonth"));
					}
					OverSeaUtil.add_empty_to_list(panel1, meta.getLnYear(), prop_lms1015m01.getProperty("tab01.lnPeriod")+"-"+prop_lms1015m01.getProperty("tab01.lnYear"));
					OverSeaUtil.add_empty_to_list(panel1, meta.getLnMonth(), prop_lms1015m01.getProperty("tab01.lnPeriod")+"-"+prop_lms1015m01.getProperty("tab01.lnMonth"));
					
					if(Util.equals("2", meta.getRepaymentSchFmt())){
						OverSeaUtil.add_empty_to_list(panel1, meta.getRepaymentSchDays(), prop_lms1015m01.getProperty("tab01.repaymentSch"));
					}
					if(OverSeaUtil.valid_RepaymentSchDays_LoanTenor(meta)==false){
						panel1.add(prop_lms1015m01.getProperty("tab01.repaymentSchDays.invalid"));
					}
				}
				
				for(String colDesc :overRange){
					HashMap<String, String> msg = new HashMap<String, String>();
					msg.put("colName", colDesc);
					panel1.add(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.輸入位數超過, msg));
				}									
			}
			List<String> panel2 = new ArrayList<String>();//本案關係人基本資料
			if(true){
				int hasKeyMan = 0;
				for(C120M01A c120m01a: c120m01a_list){
					if(Util.equals("Y", c120m01a.getKeyMan())){
						hasKeyMan++;
					}
				}
				if(hasKeyMan==1){
					for(C120M01A c120m01a: c120m01a_list){
						if(Util.notEquals("Y", c120m01a.getO_chkYN())){
							panel2.add(MessageFormat.format(prop_lms1015m01.getProperty("msg.004")
									, c120m01a.getCustId()+"-"+c120m01a.getDupNo()) );
							continue;
						}
						
						if( Util.equals("Y", c120m01a.getKeyMan())){
							
						}else{
							//非主借人，要有 與主要借款人關係
							if(Util.isEmpty(Util.trim(c120m01a.getO_custRlt()))){
								panel2.add(MessageFormat.format(prop_lms1015m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_lms1015m01.getProperty("l120s01a.custrlt")) );
								continue;
							}
							
							if(Util.isEmpty(Util.trim(c120m01a.getCustPos()))){
								panel2.add(MessageFormat.format(prop_lms1015m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_lms1015m01.getProperty("l120s01a.custpos")) );
								continue;
							}
						}
					}	
				}else{
					panel2.add(MessageFormat.format(prop_lms1015m01.getProperty("msg.004"), prop_lms1015m01.getProperty("C121M01A.custId")) );
				}				
			}
			
			List<String> panel3 = new ArrayList<String>();//擔保品資料
			if(true){

				if(c121s01a==null || Util.isEmpty(Util.trim(c121s01a.getCmsType()))){
					OverSeaUtil.add_empty_to_list(panel3, "", prop_lms1015m01, "C121S01A.cmsType");
				}else{
					OverSeaUtil.add_empty_to_list(panel3, c121s01a.getCollUsage(), prop_lms1015m01, "C121S01A.collUsage");			
					OverSeaUtil.add_empty_to_list(panel3, c121s01a.getLocationType(), prop_lms1015m01, "C121S01A.locationType");			
					
					if(true){
						boolean notMatch = false;
						if(Util.equals("1", c121s01a.getCmsType()) && Util.equals("0", c121s01a.getCollUsage())){
							//種類=不動產, 但選擇 不適用
							notMatch = true;
						}	
						if(!Util.equals("1", c121s01a.getCmsType()) && (Util.equals("1", c121s01a.getCollUsage())||Util.equals("2", c121s01a.getCollUsage()))){
							//種類!=不動產, 但選擇 [自住, 非自住]
							notMatch = true;
						}
						if(notMatch){
							Map<String, String> map_cmsType = clsService.get_codeTypeWithOrder("c121s01a_cmsType");
							Map<String, String> map_usage = clsService.get_codeTypeWithOrder("c121s01a_collUsage");
							String val_A = prop_lms1015m01.getProperty("C121S01A.cmsType")+":"+LMSUtil.getDesc(map_cmsType, c121s01a.getCmsType());
							String val_B = prop_lms1015m01.getProperty("C121S01A.collUsage")+":"+LMSUtil.getDesc(map_usage, c121s01a.getCollUsage());
							panel3.add(MessageFormat.format(prop_lms1015m01.getProperty("msg.008"), val_A, val_B));
						}
						
						
						boolean notMatch_locationType = false;
						if(Util.equals("1", c121s01a.getCmsType()) && Util.equals("0", c121s01a.getLocationType())){
							//種類=不動產, 但選擇 不適用
							notMatch_locationType = true;
						}	
						if(!Util.equals("1", c121s01a.getCmsType()) && (Util.equals("1", c121s01a.getLocationType())||Util.equals("2", c121s01a.getLocationType())||Util.equals("3", c121s01a.getLocationType()))){
							//種類!=不動產, 但選擇 [自住, 非自住]
							notMatch_locationType = true;
						}
						if(notMatch_locationType){
							Map<String, String> map_cmsType = clsService.get_codeTypeWithOrder("c121s01a_cmsType");
							Map<String, String> map_locationType = clsService.get_codeTypeWithOrder("c121s01a_locationType");
							String val_A = prop_lms1015m01.getProperty("C121S01A.cmsType")+":"+LMSUtil.getDesc(map_cmsType, c121s01a.getCmsType());
							String val_B = prop_lms1015m01.getProperty("C121S01A.locationType")+":"+LMSUtil.getDesc(map_locationType, c121s01a.getLocationType());
							panel3.add(MessageFormat.format(prop_lms1015m01.getProperty("msg.008"), val_A, val_B));
						}
					}
					if(Util.equals("1", c121s01a.getCmsType())){
						OverSeaUtil.add_empty_to_list(panel3, c121s01a.getLocation(), prop_lms1015m01, "C121S01A.location");
						OverSeaUtil.add_empty_to_list(panel3, c121s01a.getRegion(), prop_lms1015m01, "C121S01A.region");
						OverSeaUtil.add_empty_to_list(panel3, c121s01a.getHouseAge(), prop_lms1015m01, "C121S01A.houseAge");
						OverSeaUtil.add_empty_to_list(panel3, c121s01a.getHouseArea(), prop_lms1015m01, "C121S01A.houseArea");						
					}
				}	
			}
			
			
			List<String> panel6 = new ArrayList<String>();//主觀評等更新
			String varVer = meta.getVarVer();
			if(true){
				//主觀評等只有模型1.0開放調整評等，因此2.0無需檢核
				if(Util.equals(varVer, OverSeaUtil.V1_0_LOAN_JP)){
					LinkedHashMap<String, String> adjReasonMap = new LinkedHashMap<String, String>();
					LinkedHashMap<String, String> adjReasonErrMap = new LinkedHashMap<String, String>();
									
					for(C120M01A c120m01a: c120m01a_shouldRating_list){
						C121M01B c121m01b = clsService.findC121M01B_byC120M01A(c120m01a);
						if(c121m01b==null || Util.isEmpty(Util.trim(c121m01b.getNoAdj()))){
							panel6.add(MessageFormat.format(prop_lms1015m01.getProperty("msg.004")
									, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_lms1015m01.getProperty("tab06.desc03")) );
						}
						if(c121m01b==null){
							continue;
						}
						if(Util.equals(c121m01b.getNoAdj(),"2")){
							if(Util.isEmpty(Util.trim(c121m01b.getAdjustStatus()))){
								panel6.add(MessageFormat.format(prop_lms1015m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_abstractOverSeaCLS.getProperty("message.adjustStatus")) );
							}	
							if(Util.equals(c121m01b.getSprtRating(), c121m01b.getFRating())){
								panel6.add(MessageFormat.format(prop_lms1015m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_abstractOverSeaCLS.getProperty("message.adjustLevel")) );
							}
							if(Util.equals(c121m01b.getAdjustStatus(),"1") && Util.isEmpty(Util.trim(c121m01b.getAdjustFlag()))){
								panel6.add(MessageFormat.format(prop_lms1015m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_abstractOverSeaCLS.getProperty("message.adjustFlag")) );
							}
							if(Util.isEmpty(Util.trim(c121m01b.getAdjustReason()))){
								panel6.add(MessageFormat.format(prop_lms1015m01.getProperty("msg.004")
										, c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+prop_abstractOverSeaCLS.getProperty("message.adjustReason")) );
							}
						}
						
						//(JP)切換 tab 頁面，可能使不完整的 data 存進DB
						String adjustReason = Util.trim(c121m01b.getAdjustReason());
						if(Util.isNotEmpty(adjustReason)){
							adjReasonMap.put(c120m01a.getCustId()+"-"+c120m01a.getDupNo()+" "+c120m01a.getCustName()
									, adjustReason);						
						}
					}
					
					if(true){
						adjReasonCnt.add(String.valueOf(adjReasonMap.size()));
						
						clsService.validate_adjustReason(adjReasonMap, adjReasonErrMap, adjReasonCfmMap);
						
						if(adjReasonErrMap.size()>0){
							for(String idDupName : adjReasonErrMap.keySet()){
								panel6.add(idDupName+" "+adjReasonErrMap.get(idDupName));
							}
						}					
					}
				}
			}
			
			List<String> panel7 = new ArrayList<String>();//評等等級
			if(true){
				//主觀評等只有模型1.0開放調整評等，因此2.0無需檢核
				if(Util.equals(varVer, OverSeaUtil.V1_0_LOAN_JP)){
					for(C120M01A c120m01a: c120m01a_shouldRating_list){
						C121M01B c121m01b = clsService.findC121M01B_byC120M01A(c120m01a);
						if(c121m01b==null||c121m01b.getFRating()==null||CrsUtil.isNull_or_ZeroDate(c121m01b.getRatingDate())){
							panel7.add(MessageFormat.format(prop_lms1015m01.getProperty("msg.001"), c120m01a.getCustId()+"-"+c120m01a.getDupNo()) );	
						}
					}
				}
			}
			
			List<String> r = new ArrayList<String>();
			add_errMsg(r, prop_lms1015m01.getProperty("tab.01"), panel1);
			add_errMsg(r, prop_lms1015m01.getProperty("tab.02"), panel2);
			add_errMsg(r, prop_lms1015m01.getProperty("tab.03"), panel3);
			add_errMsg(r, prop_lms1015m01.getProperty("tab.06"), panel6);
			add_errMsg(r, prop_lms1015m01.getProperty("tab.07"), panel7);
			
			String msg = StringUtils.join(r, "<br>");
			if(Util.isNotEmpty(msg)){
				logger.trace("checkIncompleteMsg:"+msg);
				return msg;
			}	
		}
		
		return "";
	}
	
	private void add_errMsg(List<String> errMsg, String panelTitle, List<String> panelErrMsg){
		if(panelErrMsg.size()>0){
			errMsg.add("【"+panelTitle+"】");
			for(String m : panelErrMsg){
				errMsg.add("&nbsp;&nbsp;&nbsp;&nbsp;"+m);	
			}
		}
	}
	
	@Override	
	public void delRatingDocCust(C121M01A c121m01a, C120M01A c120m01a){
		C121M01B c121m01b = clsService.findC121M01B_byC120M01A(c120m01a);
		if(c121m01b!=null){
			c121m01bDao.delete(c121m01b);	
		}
		//-------------
		clsService.delC120Relate(c120m01a);
	}
	
	@Override
	public void calc_C121_score(C121M01A meta)
	throws CapException{
		List<C120M01A> c120m01a_list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if(CollectionUtils.isEmpty(c120m01a_list)){
			return;
		}
		List<C121M01B> c121m01b_list = new ArrayList<C121M01B>();
		
		if(true){
			BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());
			List<Map<String, Object>> dw_fxrth_list = dwdbBASEService.findDW_FXRTH_LatestRate();
			String varVer = scoreServiceJP.get_Version_JP();
			for(C120M01A c120m01a : c120m01a_list ){
				String custId = c120m01a.getCustId();
				String dupNo = c120m01a.getDupNo();
				
				C120S01A c120s01a = clsService.findC120S01A(c120m01a);
				C120S01B c120s01b = clsService.findC120S01B(c120m01a);
				C120S01C c120s01c = clsService.findC120S01C(c120m01a);
				C120S01E c120s01e = clsService.findC120S01E(c120m01a);
				C121S01A c121s01a = clsService.findC121S01A(meta);
				C121M01B c121m01b = clsService.findC121M01B_byC120M01A(c120m01a);
				if(clsService.custPosHasRating(c120m01a)){
					
					JSONObject fetch_score_src = fetch_score_src(branchRate,dw_fxrth_list,meta, c121s01a
							, c120s01a, c120s01b, c120s01c, c120s01e, varVer);
					
					if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, meta.getMowType())){
						if(c121m01b==null){
							c121m01b = new C121M01B();
							OverSeaUtil.copyC121M01B(c121m01b, meta, custId, dupNo);
						}
						
						//重製數值(C121M01B)
						this.initC121m01b(c121m01b);
						
						JSONObject score_JP = new JSONObject();							
						score_JP.putAll(fetch_score_src);
//						score_JP.putAll(scoreServiceJP.scoreJP(ScoreJP.type.日本消金模型基本, fetch_score_src, varVer));
						//1.0版本沒有區分房貸非房貸，最後一個版本參數，使用房貸
						score_JP.putAll(scoreServiceJP.scoreJP(ScoreJP.type.日本消金模型基本, fetch_score_src, varVer, OverSeaUtil.海外評等_房貸));
						DataParse.toBean(score_JP, c121m01b);
						
						c121m01b_list.add(c121m01b);
					}					
				}				
			}
		}
		
		if(c121m01b_list.size()>0){
			//XXX 重算分數時，不應該用 tempSave
			SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
			//~~~~~~
			List<GenericBean> saved_list = new ArrayList<GenericBean>();
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			String creator = user.getUserId();
			for(C121M01B c121m01b: c121m01b_list){
				c121m01b.setCreator(creator);
				c121m01b.setCreateTime(nowTS);
				c121m01b.setUpdater(null);
				c121m01b.setUpdateTime(null);
				//===
				saved_list.add(c121m01b);
			}
			if(true){
				//以 C121M01B 的評等日期、版本為主
				C121M01B c121m01b = c121m01b_list.get(0);
				String varVer = c121m01b.getVarVer();
				
				meta.setRatingDate(null);
				meta.setVarVer(varVer);
				//------
				saved_list.add(meta);
			}
			clsService.daoSave(saved_list);
		}
	}
	
	@Override
	public void calc_C121_score_v2_0(C121M01A meta)
	throws CapException{
		List<C120M01A> c120m01a_list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if(CollectionUtils.isEmpty(c120m01a_list)){
			return;
		}
		List<GenericBean> saved_list = new ArrayList<GenericBean>();
		if(true){
			BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());
			List<Map<String, Object>> dw_fxrth_list = dwdbBASEService.findDW_FXRTH_LatestRate();
			String varVer = scoreServiceJP.get_Version_JP();
			
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			String creator = user.getUserId();
			
			for(C120M01A c120m01a : c120m01a_list ){
				String custId = c120m01a.getCustId();
				String dupNo = c120m01a.getDupNo();
				
				C120S01A c120s01a = clsService.findC120S01A(c120m01a);
				C120S01B c120s01b = clsService.findC120S01B(c120m01a);
				C120S01C c120s01c = clsService.findC120S01C(c120m01a);
				C120S01E c120s01e = clsService.findC120S01E(c120m01a);
				C121S01A c121s01a = clsService.findC121S01A(meta);
				//1.0版本無區分房貸非房貸，2.0開始拆分C121M01B=房貸、C121M01F=非房貸
				C121M01B c121m01b_house = clsService.findC121M01B_byC120M01A(c120m01a); //房貸
				C121M01F c121m01f_notHouse = clsService.findC121M01F_byC120M01A(c120m01a); //非房貸
				
				if(clsService.custPosHasRating(c120m01a)){
					JSONObject fetch_score_src = fetch_score_src(branchRate,dw_fxrth_list,meta, c121s01a
							, c120s01a, c120s01b, c120s01c, c120s01e, varVer);
					if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, meta.getMowType())){
						//處理房貸模型評等
						if(c121m01b_house==null){
							c121m01b_house = new C121M01B();
							OverSeaUtil.copyC121M01B(c121m01b_house, meta, custId, dupNo);
						}
						//重製數值(C121M01B)
						this.initC121m01b(c121m01b_house);
						
						JSONObject score_JP = new JSONObject();							
						score_JP.putAll(fetch_score_src);
						score_JP.putAll(scoreServiceJP.scoreJP(ScoreJP.type.日本消金模型基本, fetch_score_src, varVer, OverSeaUtil.海外評等_房貸));
						DataParse.toBean(score_JP, c121m01b_house);
						c121m01b_house.setCreator(creator);
						c121m01b_house.setCreateTime(nowTS);
						c121m01b_house.setUpdater(creator);
						c121m01b_house.setUpdateTime(nowTS);
						c121m01b_house.setRatingDate(nowTS);
						//2.0不可做評等調整，故noAdj[註記不需調整]，直接寫[1]=是
						c121m01b_house.setNoAdj("1");
						
						saved_list.add(c121m01b_house);
						
						//處理非房貸模型評等
						if(c121m01f_notHouse==null){
							c121m01f_notHouse = new C121M01F();
							OverSeaUtil.copyC121M01F(c121m01f_notHouse, meta, custId, dupNo);
						}
						//重製數值(C121M01F)
						
						
						JSONObject score_JP_NotHouse = new JSONObject();							
						score_JP_NotHouse.putAll(fetch_score_src);
						score_JP_NotHouse.putAll(scoreServiceJP.scoreJP(ScoreJP.type.日本消金模型基本, fetch_score_src, varVer, OverSeaUtil.海外評等_非房貸));
						DataParse.toBean(score_JP_NotHouse, c121m01f_notHouse);
						c121m01f_notHouse.setCreator(creator);
						c121m01f_notHouse.setCreateTime(nowTS);
						c121m01f_notHouse.setUpdater(creator);
						c121m01f_notHouse.setUpdateTime(nowTS);
						c121m01f_notHouse.setRatingDate(nowTS);
						//2.0不可做評等調整，故noAdj[註記不需調整]，直接寫[1]=是
						c121m01f_notHouse.setNoAdj("1");
						//------
						saved_list.add(c121m01f_notHouse);
					}					
				}				
			}
			
			meta.setRatingDate(nowTS);
			meta.setVarVer(varVer);
			//避免評等升版時，缺乏國別資料
			if(Util.isEmpty(Util.trim(meta.getMowTypeCountry()))){
				meta.setMowTypeCountry(OverSeaUtil.getMowTypeCountry(meta.getCaseBrId()));
			}
			//------
			saved_list.add(meta);
		}
		if(saved_list.size()>0){
			clsService.daoSave(saved_list);
		}
	}
	
	
	private JSONObject fetch_score_src(BranchRate branchRate, List<Map<String, Object>> dw_fxrth_list, C121M01A meta, C121S01A c121s01a
			, C120S01A c120s01a, C120S01B c120s01b
			, C120S01C c120s01c, C120S01E c120s01e, String varVer)
	throws CapException{
		JSONObject fetch_score_src = new JSONObject();
		
		JSONObject json_c120s01b = DataParse.toJSON(c120s01b);
		JSONObject json_c120s01e = DataParse.toJSON(c120s01e);
		JSONObject json_c121s01a = DataParse.toJSON(c121s01a);
		
		fetch_score_src.put(ScoreJP.column.聯徵查詢日期, json_c120s01e.get("eJcicQDate") );
				
		String chkItem1 = _get_chkItem1(c120s01e);
		String chkItem1a = _get_chkItem1a(c120s01e);
		String chkItem1b = _get_chkItem1b(c120s01e);
		String chkItem1c = _get_chkItem1c(c120s01e);
		String chkItem1d = _get_chkItem1d(c120s01e);
		String chkItem2 = _get_chkItem2(c120s01e);
		String chkItem4 = _get_chkItem4(c120s01e);
		String chkItem5 = _get_chkItem5(c120s01e);
		String chkItem7 = _get_chkItem7(c120s01e);
		String chkItem9 = _get_chkItem9(c120s01e);
		String chkItem10 = _get_chkItem10(c120s01e);
		String chkItem11 = _get_chkItem11(c120s01e);
		String chkItem12 = _get_chkItem12(c120s01e);
		String chkItem13 = _get_chkItem13(c120s01e);
		
		fetch_score_src.put(ScoreJP.column.負面資訊_01, chkItem1);
		fetch_score_src.put(ScoreJP.column.負面資訊_01_退票, chkItem1a);
		fetch_score_src.put(ScoreJP.column.負面資訊_01_拒往, chkItem1b);
		fetch_score_src.put(ScoreJP.column.負面資訊_01_信用卡強停, chkItem1c);
		fetch_score_src.put(ScoreJP.column.負面資訊_01_催收呆帳, chkItem1d);
		fetch_score_src.put(ScoreJP.column.負面資訊_02, chkItem2);
		fetch_score_src.put(ScoreJP.column.負面資訊_04, chkItem4);
		fetch_score_src.put(ScoreJP.column.負面資訊_05, chkItem5);
		fetch_score_src.put(ScoreJP.column.負面資訊_07, chkItem7);
		fetch_score_src.put(ScoreJP.column.負面資訊_09, chkItem9);
		fetch_score_src.put(ScoreJP.column.負面資訊_10, chkItem10);
		fetch_score_src.put(ScoreJP.column.負面資訊_11, chkItem11);
		fetch_score_src.put(ScoreJP.column.負面資訊_12, chkItem12);
		fetch_score_src.put(ScoreJP.column.負面資訊_13, chkItem13);
		
		fetch_score_src.put(ScoreJP.column.J10信用評分種類, Util.trim(c120s01e.getJ10_score_flag()));
		fetch_score_src.put(ScoreJP.column.J10信用評分, Util.trim(c120s01e.getJ10_score()));
		
		
		String item_m1 = "";
		Date raw_m1 = null;
		String raw_payCurr = Util.trim(c120s01b.getPayCurr());
		BigDecimal exRate_pay = _proc_exRate(raw_payCurr, branchRate, dw_fxrth_list);
		BigDecimal raw_payAmt = c120s01b.getPayAmt();
		
		String raw_otherCurr = Util.trim(c120s01c.getOMoneyCurr());
		BigDecimal exRate_oth = _proc_exRate(raw_otherCurr, branchRate, dw_fxrth_list);
		BigDecimal raw_otherAmt = c120s01c.getOMoneyAmt();
		
		String raw_hincomeCurr = Util.trim(c120s01c.getYFamCurr());		
		BigDecimal exRate_hincome = _proc_exRate(raw_hincomeCurr, branchRate, dw_fxrth_list);
		
		String raw_rincomeCurr = Util.trim(c120s01c.getRealEstateRentIncomeCurr());
		BigDecimal exRate_rincome = _proc_exRate(raw_rincomeCurr, branchRate, dw_fxrth_list);
		
		String raw_invMBalCurr = Util.trim(c120s01c.getInvMBalCurr());
		BigDecimal exRate_invMBal = _proc_exRate(raw_invMBalCurr, branchRate, dw_fxrth_list);
		
		String raw_invOBalCurr = Util.trim(c120s01c.getInvOBalCurr());
		BigDecimal exRate_invOBal = _proc_exRate(raw_invOBalCurr, branchRate, dw_fxrth_list);
		
		String raw_branAmtCurr = Util.trim(c120s01c.getBranCurr());	
		BigDecimal exRate_branAmt = _proc_exRate(raw_branAmtCurr, branchRate, dw_fxrth_list);
		
		BigDecimal p2 = null;
		Integer raw_a5 = null;
		String item_a5 = ""; 
		
		if(true){
			if(true){
				raw_m1 = OverSeaUtil.get_raw_m1(c120s01a);
				item_m1 = Util.trim(OverSeaUtil.getAge(raw_m1));
			}
			if(exRate_pay!=null && raw_payAmt!=null){
				if(p2==null){
					p2 = BigDecimal.ZERO;
				}
				p2 = p2.add(Arithmetic.mul(exRate_pay, raw_payAmt));
			}
			if(exRate_oth!=null && raw_otherAmt!=null){
				if(p2==null){
					p2 = BigDecimal.ZERO;
				}		
				p2 = p2.add(Arithmetic.mul(exRate_oth, raw_otherAmt));
			}
			
			if(true){
				raw_a5 = OverSeaUtil.get_raw_a5(meta);
				
				if(raw_a5!=null){
					item_a5 = String.valueOf(OverSeaUtil.yearmonth_toYear(raw_a5) );	
				}				
			}
		}
		if(p2!=null){
			p2 = p2.divide(new BigDecimal("1000"));
			p2 = Arithmetic.div(p2, BigDecimal.ONE, 3);
		}
		
		if(true){
			fetch_score_src.put(ScoreJP.column.出生日M1, Util.trim(TWNDate.toAD(raw_m1)));
			fetch_score_src.put(ScoreJP.column.因子M1_年齡, item_m1);	
		}
		
		fetch_score_src.put(ScoreJP.column.因子M5_職業, json_c120s01b.get("jobType1"));
		fetch_score_src.put(ScoreJP.column.因子M7_年資, json_c120s01b.get("seniority"));
		if(true){			
			fetch_score_src.put(ScoreJP.column.年薪幣別P2, raw_payCurr);
			fetch_score_src.put(ScoreJP.column.轉換匯率_年薪, Util.trim(exRate_pay));
			fetch_score_src.put(ScoreJP.column.年薪金額P2, raw_payAmt);
			
			fetch_score_src.put(ScoreJP.column.其它收入幣別P2, raw_otherCurr);
			fetch_score_src.put(ScoreJP.column.轉換匯率_其他收入, Util.trim(exRate_oth));
			fetch_score_src.put(ScoreJP.column.其它收入金額P2, raw_otherAmt);
			
			fetch_score_src.put(ScoreJP.column.家庭所得幣別, raw_hincomeCurr);
			fetch_score_src.put(ScoreJP.column.轉換匯率_家庭所得, Util.trim(exRate_hincome));
			
			fetch_score_src.put(ScoreJP.column.本次新做案下不動產租金收入幣別 , raw_rincomeCurr);
			fetch_score_src.put(ScoreJP.column.轉換匯率_本次新做案下不動產租金收入, Util.trim(exRate_rincome));
			
			fetch_score_src.put(ScoreJP.column.財富管理_本行幣別 , raw_invMBalCurr);
			fetch_score_src.put(ScoreJP.column.轉換匯率_財富管理本行, Util.trim(exRate_invMBal));
			
			fetch_score_src.put(ScoreJP.column.財富管理_它行幣別 , raw_invOBalCurr);
			fetch_score_src.put(ScoreJP.column.轉換匯率_財富管理它行, Util.trim(exRate_invOBal));
			
			fetch_score_src.put(ScoreJP.column.金融機構存款往來情形幣別 , raw_branAmtCurr);
			fetch_score_src.put(ScoreJP.column.轉換匯率_金融機構存款往來情形, Util.trim(exRate_branAmt));
			
			fetch_score_src.put(ScoreJP.column.因子P2_年收入_日幣仟元, Util.trim(p2));						
		}
		
		if(true){
			fetch_score_src.put(ScoreJP.column.月份數A5, Util.trim(raw_a5));
			fetch_score_src.put(ScoreJP.column.因子A5_契約年限, item_a5 );
		}
		fetch_score_src.put(ScoreJP.column.因子Z1, json_c121s01a.get("factor1") );
		fetch_score_src.put(ScoreJP.column.因子Z2, json_c121s01a.get("factor2") );
		
		//J-111-470，日本模型2.0新增兩個因子(職業別、學歷)
		if(varVer.equals(OverSeaUtil.V2_0_LOAN_JP)){
			fetch_score_src.put(ScoreJP.column.因子edu_教育程度, c120s01a.getEdu() );
			fetch_score_src.put(ScoreJP.column.因子drate_個人負債比率, c120s01c.getDRate() );
		}
		
		
	
		return fetch_score_src;
	}
	
	/**
	 * 取得的匯率可能是 3.3783783784
	 * 但在 eloan 只存到小數點後5位
	 * 
	 * 若直接用 branchRate.toLocalAmt(curr, amt)
	 * 可能會和 amt*匯率 的值，有一些差異 
	 */
//	private BigDecimal _proc_exRate(String inputCurr, BranchRate branchRate){
//		if(Util.isEmpty(Util.trim(inputCurr))){
//			return null;
//		}
//		int _scale = 5;//小數點後5位
//		return Arithmetic.div(branchRate.toLocalRate(inputCurr), BigDecimal.ONE, _scale);
//	}
	
	private BigDecimal _proc_exRate(String inputCurr, BranchRate branchRate, List<Map<String, Object>> dw_fxrth_list){
		int _scale = 5;//小數點後5位
		return clsService.proc_exRate(inputCurr, branchRate.getMCurr(), _scale, dw_fxrth_list);
	}
		
	/**
	退票紀錄 	  isQdata9
	<br/>拒絕往來紀錄 	  isQdata10
	<br/>主債務逾期、催收、呆帳紀錄 	  isQdata11
	<br/>信用卡強停紀錄 	  isQdata13
	 */
	private String _get_chkItem1(C120S01E c120s01e){		
		if(Util.equals(UtilConstants.haveNo.有, c120s01e.getIsQdata9())
			|| Util.equals(UtilConstants.haveNo.有, c120s01e.getIsQdata10())
			|| Util.equals(UtilConstants.haveNo.有, c120s01e.getIsQdata11())
			|| Util.equals(UtilConstants.haveNo.有, c120s01e.getIsQdata13()) ){
			return UtilConstants.haveNo.有;
		}
		if(Util.equals(UtilConstants.haveNo.無, c120s01e.getIsQdata9())
			&& Util.equals(UtilConstants.haveNo.無, c120s01e.getIsQdata10())
			&& Util.equals(UtilConstants.haveNo.無, c120s01e.getIsQdata11())
			&& Util.equals(UtilConstants.haveNo.無, c120s01e.getIsQdata13()) ){
			return UtilConstants.haveNo.無;
		}
		return UtilConstants.haveNo.NA;
	}	
	private String _get_chkItem1a(C120S01E c120s01e){ //退票		
		return __get_chkItem_default(c120s01e.getIsQdata9());		
	}	
	private String _get_chkItem1b(C120S01E c120s01e){ //拒往
		return __get_chkItem_default(c120s01e.getIsQdata10());
	}	
	private String _get_chkItem1c(C120S01E c120s01e){ //信用卡強停
		return __get_chkItem_default(c120s01e.getIsQdata13());
	}
	private String _get_chkItem1d(C120S01E c120s01e){ //催收呆帳
		return __get_chkItem_default(c120s01e.getIsQdata11());
	}
	private String _get_chkItem2(C120S01E c120s01e){
		return __get_chkItem_default(c120s01e.getIsQdata19());
	}
	
	private String __get_chkItem_default(String val){
		if(Util.equals(UtilConstants.haveNo.有, val)){
			return UtilConstants.haveNo.有;
		}else if(Util.equals(UtilConstants.haveNo.無, val)){
			return UtilConstants.haveNo.無;
		}
		return UtilConstants.haveNo.NA;
	}	
	
	private String _get_chkItem_when_GE_cmpVal(String isQdata, Integer cntQdata, int cmpVal){
		if(Util.equals(UtilConstants.haveNo.有, isQdata)){
			if(cntQdata==null){
				return UtilConstants.haveNo.無;
			}else{
				if(cntQdata>=cmpVal){
					return UtilConstants.haveNo.有;
				}else{
					return UtilConstants.haveNo.無;
				}	
			}			
		}else if(Util.equals(UtilConstants.haveNo.無, isQdata)){
			return UtilConstants.haveNo.無;
		}
		return UtilConstants.haveNo.NA;	
	}
	private String _get_chkItem4(C120S01E c120s01e){
		return _get_chkItem_when_GE_cmpVal(c120s01e.getIsQdata21(), c120s01e.getCntQdata21(), 2);
	}	
	private String _get_chkItem5(C120S01E c120s01e){
		return _get_chkItem_when_GE_cmpVal(c120s01e.getIsQdata22(), c120s01e.getCntQdata22(), 2);
	}
	private String _get_chkItem7(C120S01E c120s01e){
		return _get_chkItem_when_GE_cmpVal(c120s01e.getIsQdata24(), c120s01e.getCntQdata24(), 2);
	}
	private String _get_chkItem9(C120S01E c120s01e){
		return __get_chkItem_default(c120s01e.getIsQdata20());
	}
	private String _get_chkItem10(C120S01E c120s01e){
		return _get_chkItem_when_GE_cmpVal(c120s01e.getIsQdata23(), c120s01e.getCntQdata23(), 2);
	}
	private String _get_chkItem11(C120S01E c120s01e){
		return __get_chkItem_default(c120s01e.getIsQdata25());
	}
	private String _get_chkItem12(C120S01E c120s01e){
		String isQdata26 = c120s01e.getIsQdata26();
		
		if(Util.equals(UtilConstants.haveNo.有, isQdata26)){
			
			BigDecimal v1 = c120s01e.getBalQdata26();
			BigDecimal v2 = null;
			if(Util.equals(UtilConstants.haveNo.有, c120s01e.getIsQdata28())){
				v2 = c120s01e.getBalQdata28();
			}	
			if(v1==null){
				v1 = BigDecimal.ZERO;
			}
			if(v2==null){
				v2 = BigDecimal.ZERO;
			}			
			BigDecimal v = Arithmetic.sub(v1, v2);
			
			boolean match = (v.compareTo(new BigDecimal("1000"))>0);			
			
			return match?UtilConstants.haveNo.有:UtilConstants.haveNo.無;
		}else if(Util.equals(UtilConstants.haveNo.無, isQdata26)){
			return UtilConstants.haveNo.無;
		}
		return UtilConstants.haveNo.NA;	
	}
	private String _get_chkItem13(C120S01E c120s01e){
		//查詢總家數超過3次(含)
		return _get_chkItem_when_GE_cmpVal(c120s01e.getIsQdata27(), c120s01e.getCntQdata27(), 3);
	}
	
	private boolean isCommonFactorChg(C121M01A meta, Integer c121m01_grade_a5, Integer c121m01_grade_z1, Integer c121m01_grade_z2){
		
		if(Util.notEquals(Util.trim(c121m01_grade_a5), Util.trim(OverSeaUtil.get_raw_a5(meta)))){	
			return true;
		}
		C121S01A c121s01a = clsService.findC121S01A(meta);
		if(c121s01a==null){
			return true;
		}else{
			
			if(Util.notEquals(Util.trim(c121m01_grade_z1), Util.trim(c121s01a.getFactor1()))){
				return true;
			}
			if(Util.notEquals(Util.trim(c121m01_grade_z2), Util.trim(c121s01a.getFactor2()))){
				return true;
			}
			
		}
		
		return false;
	}
	
	private boolean isCustFactorChg(C121M01A meta, C120M01A c120m01a, C121M01B c121m01b){
		try{
			isCustFactorChg_str(meta, c120m01a, c121m01b);
			return false;
		}catch(CapException r){
			debug("isCustFactorChg【"+r.getMessage()+"】");
			return true;
		}		
	}
	private void isCustFactorChg_str(C121M01A meta, C120M01A c120m01a, C121M01B c121m01b)
	throws CapException{		
		C120S01A c120s01a = clsService.findC120S01A(c120m01a);
		C120S01B c120s01b = clsService.findC120S01B(c120m01a);
		C120S01C c120s01c = clsService.findC120S01C(c120m01a);
		C120S01E c120s01e = clsService.findC120S01E(c120m01a);		
		//M1_age
		if(true){
			Date exist_raw_m1 = c121m01b.getRaw_m1();
			Date raw_m1 = OverSeaUtil.get_raw_m1(c120s01a);
			
			if(!OverSeaUtil.eqDate(exist_raw_m1, raw_m1)){				
				throw new CapException("raw_m1["+exist_raw_m1+" , "+ raw_m1+"]", getClass());
			}
		}
		//M5_occupation
		if(true){
			String exist_item_m5 = c121m01b.getItem_m5();
			String item_m5 = c120s01b.getJobType1();
			cmp_diff("m5", exist_item_m5, item_m5);
		}
		//M7_seniority
		if(true){
//			Integer exist_item_m7 = c121m01b.getItem_m7();
//			Integer m7 = c120s01b.getSeniority();
//			cmp_diff("m7", Util.trim(exist_item_m7), Util.trim(m7));
			if(!OverSeaUtil.eqBigDecimal(c121m01b.getItem_m7(), c120s01b.getSeniority())){
				throw new CapException("m7["+c121m01b.getItem_m7()+" , "+ c120s01b.getSeniority()+"]", getClass());
			}	
		}
		//P2_pincome	
		if(true){			
			cmp_diff("raw_payCurr", c121m01b.getRaw_payCurr(), c120s01b.getPayCurr());
			cmp_diff("raw_otherCurr", c121m01b.getRaw_otherCurr(), c120s01c.getOMoneyCurr());
			cmp_diff("raw_hincomeCurr", c121m01b.getRaw_hincomeCurr(), c120s01c.getYFamCurr());
			cmp_diff("raw_rincomeCurr", c121m01b.getRaw_rincomeCurr(), c120s01c.getRealEstateRentIncomeCurr());
			cmp_diff("raw_invMBalCurr", c121m01b.getRaw_invMBalCurr(), c120s01c.getInvMBalCurr());
			cmp_diff("raw_invOBalCurr", c121m01b.getRaw_invOBalCurr(), c120s01c.getInvOBalCurr());
			cmp_diff("raw_branAmtCurr", c121m01b.getRaw_branAmtCurr(), c120s01c.getBranCurr());
			
			BigDecimal raw_payAmt = c120s01b.getPayAmt();
			BigDecimal raw_otherAmt = c120s01c.getOMoneyAmt();
				
			if(!OverSeaUtil.eqBigDecimal(c121m01b.getRaw_payAmt(), raw_payAmt)){
				throw new CapException("raw_payAmt["+c121m01b.getRaw_payAmt()+" , "+ raw_payAmt+"]", getClass());
			}
			if(!OverSeaUtil.eqBigDecimal(c121m01b.getRaw_otherAmt(), raw_otherAmt)){
				throw new CapException("raw_otherAmt["+c121m01b.getRaw_otherAmt()+" , "+ raw_otherAmt+"]", getClass());
			}			
		}
		
		//模型2.0要再判斷[學歷]、[個人負債比]
		String varVer = Util.trim(meta.getVarVer());
		if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_JP)){
			//edu
			if(true){
				String exist_item_edu = c121m01b.getItem_edu();
				String item_edu = c120s01a.getEdu();
				cmp_diff("edu", exist_item_edu, item_edu);
			}
			//drate
			if(true){
				BigDecimal exist_item_drate = c121m01b.getItem_drate();
				BigDecimal item_drate = c120s01c.getDRate();
				if(!OverSeaUtil.eqBigDecimal(exist_item_drate, item_drate)){
					throw new CapException("drate["+exist_item_drate+" , "+ item_drate+"]", getClass());
				}
			}
		}
		// 負面資訊/J10 異動，影響[獨立評等、支援評等]
		cmp_diff("chkItem1", c121m01b.getChkItem1(), _get_chkItem1(c120s01e));
		cmp_diff("chkItem1a", c121m01b.getChkItem1a(), _get_chkItem1a(c120s01e));
		cmp_diff("chkItem1b", c121m01b.getChkItem1b(), _get_chkItem1b(c120s01e));
		cmp_diff("chkItem1c", c121m01b.getChkItem1c(), _get_chkItem1c(c120s01e));
		cmp_diff("chkItem1d", c121m01b.getChkItem1d(), _get_chkItem1d(c120s01e));
		cmp_diff("chkItem2", c121m01b.getChkItem2(), _get_chkItem2(c120s01e));
		cmp_diff("chkItem4", c121m01b.getChkItem4(), _get_chkItem4(c120s01e));
		cmp_diff("chkItem6", c121m01b.getChkItem5(), _get_chkItem5(c120s01e));
		cmp_diff("chkItem7", c121m01b.getChkItem7(), _get_chkItem7(c120s01e));
		cmp_diff("chkItem9", c121m01b.getChkItem9(), _get_chkItem9(c120s01e));
		cmp_diff("chkItem10", c121m01b.getChkItem10(), _get_chkItem10(c120s01e));
		cmp_diff("chkItem11", c121m01b.getChkItem11(), _get_chkItem11(c120s01e));
		cmp_diff("chkItem12", c121m01b.getChkItem12(), _get_chkItem12(c120s01e));		
		cmp_diff("chkItem13", c121m01b.getChkItem13(), _get_chkItem13(c120s01e));
	}	
	
	private void cmp_diff(String desc, String a, String b)throws CapException{
		if(Util.notEquals(a, b)){
			throw new CapException(desc+"["+a+" , "+b+"]", getClass());
		}
	}
	@Override	
	public void del_noneRating_score(C121M01A meta){		
		List<C120M01A> noneRating_list = clsService.filter_noneRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
		
		List<C121M01B> delList_B = new ArrayList<C121M01B>();	
		List<C121M01F> delList_F = new ArrayList<C121M01F>();	
		for(C120M01A c120m01a : noneRating_list ){			
			C121M01B c121m01b = clsService.findC121M01B_byC120M01A(c120m01a);
			if(c121m01b!=null){
				delList_B.add(c121m01b);				
			}
			//檢查C121M01F，有的話一起刪
			C121M01F c121m01f = clsService.findC121M01F_byC120M01A(c120m01a);
			if(c121m01f!=null){
				delList_F.add(c121m01f);			
			}
		}
		if(delList_F.size()>0){
			c121m01fDao.delete(delList_F);
		}
		if(delList_B.size()>0){
			/*
			在 SimpleContextHolder 放 N，應該對 dao 沒有影響
			SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
			 */
			c121m01bDao.delete(delList_B);	
			//--------------
			tempDataService.deleteByMainId(meta.getMainId());	
		}			
	}
	
	@Override	
	public void del_c101m01f(C121M01A meta){		
		List<C121M01F> delList = new ArrayList<C121M01F>();		
		delList = c121m01fDao.findByMainId(meta.getMainId());
		if(delList.size()>0){
			c121m01fDao.delete(delList);	
			//--------------
			tempDataService.deleteByMainId(meta.getMainId());	
		}			
	}
	
	
	/**
	若一個評等包含5個人
	不應只重評1個而已(6/20, 6/21的匯率會不同)
	要重評，就一次全部5個都重評
	*/
	@Override	
	public boolean should_calc_C121_score(int page, C121M01A meta){
		List<C120M01A> shouldRating_list = new ArrayList<C120M01A>();
		if(true){
			List<C120M01A> src_list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
			shouldRating_list = clsService.filter_shouldRating(src_list);			
		}
		String debugStr = "should_calc_C121_score==TRUE";
		//房貸非房貸的版號、因子都是一樣的，所以這邊統一規則。
		String varVerNow = scoreServiceJP.get_Version_JP(); //目前適用之評等版本
		String meta_varVer = Util.trim(meta.getVarVer()); //資料本來的評等版本
		if(true){ 
			if(Util.notEquals(varVerNow, meta_varVer)){ //目前版本不同，一定要重算
				return true;
			}else{
				Set<String> varVerSet = new HashSet<String>();
				if(Util.isNotEmpty(meta_varVer)){
					varVerSet.add(meta_varVer);
				}
				for(C120M01A c120m01a : shouldRating_list ){		
					//房貸重算，非房貸重算，統一處理
					C121M01B c121m01b = clsService.findC121M01B_byC120M01A(c120m01a);
					if(c121m01b==null){
						debug(debugStr+"["+c120m01a.getCustId()+"-"+c120m01a.getDupNo()+"]lost c121m01b");
						//應有評等，而無資料
						return true;	
					}
					//================
					//當 varVer 有不一致時，也要重算
					String varVer = Util.trim(c121m01b.getVarVer());
					if(Util.isNotEmpty(varVer)){
						varVerSet.add(varVer);
					}
				}
				if(varVerSet.size()>1){
					debug(debugStr+"["+varVerSet+"]varVerSet.size()>1");
					return true;
				}
			}
		}
			
		if(page==1 || page==3){			
			if(shouldRating_list.size()>0){
				//各 custId 共用的因子
				C120M01A c120m01a = shouldRating_list.get(0);
				C121M01B c121m01b = clsService.findC121M01B_byC120M01A(c120m01a);
				
				if(isCommonFactorChg(meta, c121m01b.getRaw_a5(), c121m01b.getItem_z1(), c121m01b.getItem_z2())){
					debug(debugStr+"[isCommonFactorChg=true]");
					return true;	
				}	
			}									
		}
		if(page==2){
			for(C120M01A c120m01a: shouldRating_list){
				C121M01B c121m01b = clsService.findC121M01B_byC120M01A(c120m01a);
				
				if(isCustFactorChg(meta, c120m01a, c121m01b)){
					debug(debugStr+"["+c120m01a.getCustId()+"-"+c120m01a.getDupNo()+"][isCustFactorChg=true]");
					return true;
				}
			}
		}
		return false;
	}
	private void debug(String s){
		//logger.debug(s);
	}
	
	@Override	
	public Page<Map<String, Object>> queryPrint(String mainId, ISearch pageSetting) throws CapException{
		
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		C121M01A c121m01a = clsService.findC121M01AByMainId(mainId);
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1015M01Page.class);
		for(C120M01A c120m01a: clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(c121m01a)){
			Map<String, Object> data = new HashMap<String, Object>();
			
			data.put("oid", Util.trim(c120m01a.getOid()));
			data.put("mainId", Util.trim(c120m01a.getMainId()));
			data.put("type", "1");
			data.put("desc1", prop.getProperty("printItem1"));//借款人基本資料報表
			data.put("desc2", Util.trim(c120m01a.getCustId())+"-"+Util.trim(c120m01a.getDupNo())+" "+Util.trim(c120m01a.getCustName()));
			beanList.add(data);
		}
		if(true){
			Map<String, Object> data = new HashMap<String, Object>();
			
			data.put("oid", Util.trim(c121m01a.getOid()));
			data.put("mainId", Util.trim(c121m01a.getMainId()));
			data.put("type", "2");
			/*
			 * JP: Personal Credit Rating Sheet
			 * AU: Individual Credit Scorecard
			 */
			data.put("desc1", prop.getProperty("printItem2"));
			data.put("desc2", Util.trim(c121m01a.getCaseNo()));
			beanList.add(data);
		}
		
		return LMSUtil.getMapGirdDataRow(beanList, pageSetting);		
	}
	
	@Override
	public List<C121M01E> findC121m01eByMainId(String mainId) {
		return c121m01eDao.findByMainId(mainId);
	}
	
	@Override
	public void delListC121m01e(List<C121M01E> list) {
		c121m01eDao.delete(list);
	}
	
	@Override
	public void saveListC121m01e(List<C121M01E> list) {
		if (!list.isEmpty()) {
			c121m01eDao.save(list);
		}
	}
	
	private void initC121m01b(C121M01B c121m01b){
		//重製評等相關欄位(共用欄位不用)
		c121m01b.setPRating(null);
		c121m01b.setSRating(null);
		c121m01b.setSprtRating(null);
		c121m01b.setAdjRating(null);
		c121m01b.setFRating(null);
		c121m01b.setOrgFr(null);
		c121m01b.setNoAdj(null);
		c121m01b.setAdjustStatus(null);
		c121m01b.setAdjustFlag(null);
		c121m01b.setAdjustReason(null);
		c121m01b.setDr_1yr(null);
		c121m01b.setDr_3yr(null);
		c121m01b.setPd(null);
		c121m01b.setSlope(null);
		c121m01b.setInterCept(null);
		
		c121m01b.setWeight_edu(null);
		c121m01b.setWeight_drate(null);
		c121m01b.setItem_edu(null);
		c121m01b.setScr_edu(null);
		c121m01b.setItem_drate(null);
		c121m01b.setScr_drate(null);
		c121m01b.setWeight_scr_a5(null);
		c121m01b.setWeight_scr_drate(null);
		c121m01b.setWeight_scr_edu(null);
		c121m01b.setWeight_scr_m1(null);
		c121m01b.setWeight_scr_m5(null);
		c121m01b.setWeight_scr_m7(null);
		c121m01b.setWeight_scr_p2(null);
		c121m01b.setWeight_scr_z1(null);
		c121m01b.setWeight_scr_z2(null);
		c121m01b.setStd_a5(null);
		c121m01b.setStd_core(null);
		c121m01b.setStd_m1(null);
		c121m01b.setStd_m5(null);
		c121m01b.setStd_m7(null);
		c121m01b.setStd_p2(null);
		c121m01b.setStd_z1(null);
		c121m01b.setStd_z2(null);
	}
	
	private void initC121m01f(C121M01F c121m01f){
		
		
		
	}
	
}