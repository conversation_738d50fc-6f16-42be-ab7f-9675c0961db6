package com.mega.eloan.lms.model;

import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 可疑代辦案件註記作業主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C250M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C250M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;


	/**
	 * JOIN條件
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "c250m01a", fetch = FetchType.LAZY)
	private Set<C250A01A> c250a01a;
	
	public Set<C250A01A> getC250a01a() {
		return c250a01a;
	}

	public void setC250a01a(Set<C250A01A> c250a01a) {
		this.c250a01a = c250a01a;
	}
	
	
	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 
	 * 資料年月<p/>
	 * EX:201412
	 */
	@Size(max=6)
	@Column(name="YYYYMM", length=6, columnDefinition="CHAR(06)")
	private String yyyymm;

	/** 
	 * 性質<p/>
	 * EX: A.增額、N.新作
	 */
	@Size(max=1)
	@Column(name="STATUS", length=1, columnDefinition="CHAR(01)")
	private String status;

	/** 帳號 **/
	@Size(max=400)
	@Column(name="LOANNO", length=400, columnDefinition="VARCHAR(400)")
	private String loanNo;

	/** 
	 * 疑似代辦案件訊息［選項E:正常案件］［非E其它選項:異常］(ref C900M01J) <br/>
	 * select codetype, codeValue, codeDesc from com.bcodetype where locale='zh_TW' and codetype in('C250M01A_lnFlag', 'lnFlag_extend_C250M01A_C900M01H') order by codetype, codeOrder   <br/>
	 * A. 撥款後回查有其他金融機構短期內接續撥款情形 (指3個月內)<br/>
	 * B. 貸款後不久即延滯情形 (指3個月內)<br/>
	 * C. 跨區承作之情形 (對於借款人居住、工作地與案件來源無地源關係之申貸案件)<br/>
	 * D. 其他可疑情形：                            (請自行填寫)<br/>
	 * E. 無以上情形   (此項設為預設值)
	 */
	@Size(max=2)
	@Column(name="LNFLAG", length=2, columnDefinition="CHAR(02)")
	private String lnflag;

	/** 其他可疑情形 **/
	@Size(max=300)
	@Column(name="OTHERMEMO", length=300, columnDefinition="VARCHAR(300)")
	private String othermemo;

	/** RptId **/
	@Size(max=32)
	@Column(name="RPTID", length=32, columnDefinition="VARCHAR(32)")
	private String rptId;
	
	/** 查證結果 **/
	@Size(max=400)
	@Column(name="BRANCHCOMM", length=400, columnDefinition="VARCHAR(400)")
	private String branchComm;
	
	/**
	 * 滯延還款JSON資料
	 * 資料來源: MIS.ELFDELYD
	 */
	@Column(name = "OVERDUEJSON", length = 1024, columnDefinition = "VARCHAR(1024)")
	private String overDueJson;
	
	/** 聯徵B29來源檔案oid **/
	@Column(name="B29SRCOID", length = 32, columnDefinition = "CHAR(32)")
	private String b29SrcOid;
	
	/** 聯徵B29來源檔案名稱
	 *  PRODID: P9 -> C101S01H
	 *  PRODID: ST -> C101S01U
	 */
	@Column(name="B29SRCFILENAME", length=8, columnDefinition="CHAR(8)")
	private String b29SrcFileName;
	
	/** 其他情形 **/
	@Size(max=300)
	@Column(name="OTHERDESC", length=300, columnDefinition="VARCHAR(300)")
	private String otherDesc;
	
	
	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 
	 * 取得資料年月<p/>
	 * EX:201412
	 */
	public String getYyyymm() {
		return this.yyyymm;
	}
	/**
	 *  設定資料年月<p/>
	 *  EX:201412
	 **/
	public void setYyyymm(String value) {
		this.yyyymm = value;
	}

	/** 
	 * 取得性質<p/>
	 * EX: A.增額、N.新作
	 */
	public String getStatus() {
		return this.status;
	}
	/**
	 *  設定性質<p/>
	 *  EX: A.增額、N.新作
	 **/
	public void setStatus(String value) {
		this.status = value;
	}

	/** 取得帳號 **/
	public String getLoanNo() {
		return this.loanNo;
	}
	/** 設定帳號 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}

	/** 取得疑似代辦案件訊息 */
	public String getLnflag() {
		return this.lnflag;
	}
	/** 設定疑似代辦案件訊息 */
	public void setLnflag(String value) {
		this.lnflag = value;
	}

	/** 取得其他可疑情形 **/
	public String getOthermemo() {
		return this.othermemo;
	}
	/** 設定其他可疑情形 **/
	public void setOthermemo(String value) {
		this.othermemo = value;
	}

	/** 取得RptId **/
	public String getRptId() {
		return this.rptId;
	}
	/** 設定RptId **/
	public void setRptId(String value) {
		this.rptId = value;
	}

	/** 設定查證結果 **/
	public void setBranchComm(String value) {
		this.branchComm = value;
	}

	/** 取得查證結果 **/
	public String getBranchComm() {
		return this.branchComm;
	}

	public String getB29SrcOid() {
		return b29SrcOid;
	}

	public void setB29SrcOid(String b29SrcOid) {
		this.b29SrcOid = b29SrcOid;
	}

	public String getB29SrcFileName() {
		return b29SrcFileName;
	}

	public void setB29SrcFileName(String b29SrcFileName) {
		this.b29SrcFileName = b29SrcFileName;
	}

	public String getOverDueJson() {
		return overDueJson;
	}

	public void setOverDueJson(String overDueJson) {
		this.overDueJson = overDueJson;
	}

	public String getOtherDesc() {
		return otherDesc;
	}

	public void setOtherDesc(String otherDesc) {
		this.otherDesc = otherDesc;
	}
}
