var PanelAction06 = {
    isInit: false,
    /**
     *頁面初始化的動作
     * */
    initAction: function(){
        //_M.initItem("06");
        this.initEvent();
        this.initGrid();
        
    },
    /**
     *載入頁面後的動作
     * */
    afterAction: function(){
        CLS_BranchAciton.grid.reload({
            tabFormMainId: _M.tabMainId
        });
    },
    /**
     * 初始化事件
     */
    initEvent: function(){
        var $itemDrc = $("#clsItemDscrC");
        //組成文字
        $("#CLS_limitWordAll").click(function(){
        
            //組成字串從sql 裡面撈
            $itemDrc.html("");
            _M.doAjax({
                action: "saveDscr1",
                data: {
                    pageNum: $("#pageNum1").val()
                },
                success: function(obj){
                    $itemDrc.val(obj.drc);
                }//close success
            });//close ajax
        });
        
        //新增攤待比率
        $("#cls_newItemChildren3Bt").click(function(){
            //新增前先儲存額度序號與現請額度
            var page04Data = _M.AllFormData["04"];
            var currAmt = $.trim(util.delComma(page04Data["currentApplyAmt"]));
            var cntrNo = $.trim(page04Data["cntrNo"]);
            if (currAmt == "" || cntrNo == "") {
                // page6.007=請先登錄現請額度與額度序號並儲存後，在執行此動作。
                return API.showMessage(i18n.cls1151s01["page6.007"]);
            }
            CLS_BranchAciton.query();
        });
        $("#cls_removeGridviewitemChildren3").click(function(){
            CLS_BranchAciton.remove();
        });
        
        //變更分母
        $("#cls_changesShareRate2").click(function(){
            CLS_BranchAciton.changesShareRate();
        });
        
        // 新號，舊號的隱藏條件
        $("input[name=numberType]").change(function(){
            var $cntrNoForm = $("#cntrNoBoxforItem3Form");
            var type = $cntrNoForm.find("#cntrNoType").val(type);
            $cntrNoForm.find(".ForOriginal,.ForInSide").hide();
            //海外不行選輸入分行 和 是否遠匯 
            var value = $(this).val();
            switch (value) {
                case "1":
                    if (type != "5") {
                        $cntrNoForm.find(".ForInSide").show();
                    }
                    $("#showBrNoTr").show();
                    break;
                case "2":
                    $cntrNoForm.find(".ForOriginal").show();
                    break;
            }
        });
        
        
        //變更攤貸行計算方式
        $("[name=shareFlag]").click(function(){
            CLS_BranchAciton.controlShow();
        });
        
        
        
        
        $("#shareMoneyCount1").click(function(){//計算攤貸金額
            CLS_BranchAciton.countByAMT();
        });
        
        
        $("#shareMoneyCount2").click(function(){//計算攤貸比率
            var shareAmt = $("#shareAmt").val().replace(/,/g, "");
            var totel = $("#totalAmt").val().replace(/,/g, "");
            var shareRate2 = $("#shareRate2").val().replace(/,/g, "");
            var gcd = CLS_BranchAciton.getGCD(shareAmt, totel);
            var countGrid = CLS_BranchAciton.grid.jqGrid('getGridParam', 'records');
            if (shareRate2 == "" || countGrid == 0) {
                $("#shareRate1").val(Math.round(shareAmt / gcd));
                $("#shareRate2").val(Math.round(totel / gcd));
            } else {
                $("#shareRate1").val((shareAmt * shareRate2) / totel);
            }
            
        });
        
    },
    initGrid: function(){
        CLS_BranchAciton.initGrid();
    }
};
//登錄聯行攤貸比例
var CLS_BranchAciton = {
    formId: "CLS_L140M01EForm",
    gridId: "#cls_gridviewitemChildren3",
    grid: null,
    initGrid: function(){
        if (!this.grid) {
            this.grid = $(this.gridId).iGrid({
                handler: _M.ghandle,
                height: 170,
                rowNum: 10,
                rownumbers: true,
                multiselect: true,
                hideMultiselect: false,
                sortname: 'createTime',
                sortorder: 'asc',
                postData: {
                    formAction: "queryL140m01e",
                    tabFormMainId: _M.tabMainId
                },
                autowidth: true,
                colModel: [{
                    name: 'shareRate2',
                    hidden: true
                }, {
                    colHeader: i18n.cls1151s01["L140M01E.shareBrId"],//"攤貸分行",
                    name: 'shareBrId',
                    align: "left",
                    width: 110,
                    sortable: true,
                    formatter: 'click',
                    onclick: CLS_BranchAciton.query
                }, {
                    colHeader: i18n.cls1151s01["L140M01E.shareAmt"],//"攤貸金額",
                    name: 'shareAmt',
                    width: 160,
                    sortable: true,
                    align: "right",
                    formatter: 'currency',
                    formatoptions: {
                        thousandsSeparator: ",",
                        decimalPlaces: 0//小數點到第幾位
                    }
                }, {
                    colHeader: i18n.cls1151s01["L140M01E.shareRate1"],//"攤貸比例",
                    width: 140,
                    name: 'showRate',
                    align: "right",
                    sortable: false //在 L140M01E 為 @Transient 欄位
                }, {
                    colHeader: i18n.cls1151s01["L140M01A.cntrNo"],//"額度序號",
                    width: 140,
                    name: 'shareNo',
                    sortable: true
                }, {
                    name: 'oid',
                    hidden: true
                }],
                ondblClickRow: function(rowid){
                    var data = CLS_BranchAciton.grid.getRowData(rowid);
                    CLS_BranchAciton.query(null, null, data);
                }
            });
        }
    },
    //驗證是不是數字   
    isNumber: function(val){
        return /\d/.test(val);
    },
    
    /**
     * 取得最大公因數
     * @param {Object} m 數字
     * @param {Object} n 數字
     */
    getGCD: function(m, n){
        var num = 1;
        while (num > 0) {
            num = m % n;
            m = n;
            n = num;
        }
        return m;
    },
    /**
     * 查詢
     * @param {Object} cellvalue 欄位顯示值
     * @param {Object} type  欄位選項
     * @param {Object} data  欄位資料
     */
    query: function(cellvalue, type, data){
        if (!data) {
            data = {
                oid: ""
            };
        }
        var $form = $("#" + CLS_BranchAciton.formId);
        util.init($form);
        $form.reset();
        
        _M.doAjax({
            action: "queryL140m01e",
            data: {
                oid: data.oid
            },
            success: function(obj){
                $("#shareBrId").setItems({
                    item: obj.item,
                    format: "{value} {key}",
                    space: false
                });
                $form.injectData(obj.formData);
                $("#totalAmt").val(util.addComma($("#currentApplyAmt").val()));
                if (data.oid != "") {
                    $("#shareBrId").attr("disabled", true);
                } else {
                    $("#shareBrId").attr("disabled", false);
                }
                $("#shareAmt,#shareRate1,#shareRate2").attr("readonly", true);
                $("#shareMoneyCount1,#shareMoneyCount2").hide().parents(".fg-buttonset").hide();
                if (obj.role != "0") {
                    $("#shareRate2").val(obj.role);
                    $("#shareRate2").attr("readonly", true);
                    $("[name=shareFlag]").attr("disabled", true);
                    $("[name=shareFlag][value=" + obj.shareFlag + "]").attr("checked", true);
                } else {
                    $("[name=shareFlag]").removeAttr("disabled");
                }//close if
                CLS_BranchAciton.controlShow();
                CLS_BranchAciton.openBox();
            }//close success
        }); //close ajax  
    },
    openBox: function(){
        var $form = $("#" + CLS_BranchAciton.formId);
        $("#cls_newItemChildrenBox3").thickbox({
            //page6.005=登錄聯行攤貸比例
            title: i18n.cls1151s01["page6.005"],
            width: 600,
            height: 300,
            modal: true,
            readOnly: _openerLockDoc == "1" || _M.toreadOnly,
            i18n: i18n.def,
            open: function(){
                if (_openerLockDoc == "1" || _M.toreadOnly) {
                    //鎖定box
                    $form.readOnlyChilds(_openerLockDoc == "1", "#totalAmt,#shareBrId,#shareRate1");
                }
            },
            buttons: {
                "saveData": function(){
                    if ($("[name=shareFlag]:checked").val() == "1") {
                        $form.find("#shareRate1,#shareRate2").removeClass("required");
                    } else {
                        CLS_BranchAciton.countByAMT();
                        $form.find("#shareRate1,#shareRate2").addClass("required");
                        if ($("#shareRate2").val() == 0) {
                            //page6.004=分母不可為0
                            return API.showMessage(i18n.cls1151s01["page6.004"]);
                        }
                    }
                    
                    if (!$form.valid()) {
                        return false;
                    }
                    var shareRate1 = parseInt($("#shareRate1").val(), 10), shareRate2 = parseInt($("#shareRate2").val(), 10);
                    if (shareRate1 > shareRate2) {
                        //page6.006=分子總和大於分母無法儲存
                        return API.showMessage(i18n.cls1151s01["page6.006"]);
                    }
                    var shareBrId = $("#shareBrId").val();
                    _M.doAjax({
                        action: "queryShareBrIdType",//查詢目前分行為海外還是國內，若為海外第四碼為5，若為國內判斷
                        data: {
                            shareBrId: shareBrId
                        },
                        success: function(obj){
                            //當為國內分行直接執行儲存
                            if (obj.type != "5") {
                                //若該分行已存在只執行儲存
                                CLS_BranchAciton.save("");
                            } else {
                                //新增案件開起給號視窗
                                CLS_BranchAciton.getCntrNo(obj.type, shareBrId);
                            }
                        }//close success
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**儲存
     *
     * @param {Object} type 1.DBU,4.OBU,5.海外
     */
    save: function(type){
        _M.doAjax({
            formId: CLS_BranchAciton.formId,
            action: "saveL140m01e",
            data: {
                type: type
            },
            success: function(obj){
                if (obj && obj.drc) {
                    $("#clsItemDscrC").val(obj.drc);
                }
                $.thickbox.close();
                CLS_BranchAciton.grid.trigger("reloadGrid");
                
            }
        });
    },
    
    /**
     * 給號畫面
     * @param {String} type 5.海外
     * @param {String} brId 分行代號
     */
    getCntrNo: function(type, brId){
        var $cntrNoForm = $("#cntrNoBoxforItem3Form");
        $("#cls_cntrNoBoxforItem3").thickbox({
            //btn.number=給號
            title: i18n.cls1151s01["btn.number"],
            width: 640,
            height: 320,
            modal: true,
            align: "center",
            readOnly: _openerLockDoc == "1" || _M.toreadOnly,
            valign: "bottom",
            i18n: i18n.def,
            open: function(){
                //初始化
                $cntrNoForm.reset();
                //帶入分行預設值
                $cntrNoForm.find("#branchNoItem3").val(brId);
                $cntrNoForm.find("#cntrNoType").val(type);
                $cntrNoForm.find(".ForOriginal,.ForInSide").hide();
            },
            buttons: {
                "sure": function(){
                    if (!$cntrNoForm.valid()) {
                        return false;
                    }
                    var numberType = $cntrNoForm.find("input[name=numberType]:checked").val();
                    var originalCntrNo = $cntrNoForm.find("#originalCntrNo").val();
                    brId = $cntrNoForm.find("#branchNoItem3").val();
                    //給新號
                    if (numberType == "1") {
                        if (type != "5") {
                            //國內的可以自己選分行和DBU or OBU
                            type = $cntrNoForm.find("input[name=typeCd]:checked").val();
                        }
                    } else {
                        //舊號
                        var cntrno = PanelAction04.queryOriginalCntrNo(originalCntrNo, "3");
                        if ($.isEmptyObject(cntrno) || !cntrno.cntrNo) {
                            return false;
                        }
                    }
                    _M.doAjax({
                        action: "saveL140m01e",
                        formId: CLS_BranchAciton.formId,
                        data: {
                            numberType: numberType,
                            originalCntrNo: originalCntrNo,
                            type: type,
                            classCD: "0",
                            selectBrNo: brId
                        },
                        success: function(obj){
                            if (obj && obj.drc) {
                                $("#clsItemDscrC").val(obj.drc);
                            }
                            $.thickbox.close();
                            $.thickbox.close();
                            CLS_BranchAciton.grid.trigger("reloadGrid");
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 刪除
     */
    remove: function(){
        var gridData = CLS_BranchAciton.grid;
        var gridID = gridData.getGridParam('selarrrow');
        if (gridID == "") {
            //TMMDeleteError=請先選擇需修改(刪除)之資料列
            return API.showMessage(i18n.def["TMMDeleteError"]);
        }
        //confirmDelete=是否確定刪除?
        API.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var gridIDList = [];
                for (var i = 0; i < gridID.length; i++) {
                    gridIDList[i] = gridData.getRowData(gridID[i]).oid;
                }
                _M.doAjax({
                    action: "deleteL140m01e",
                    data: {
                        Idlist: gridIDList
                    },
                    success: function(obj){
                        if (obj && obj.drc) {
                            $("#clsItemDscrC").val(obj.drc);
                        }
                        gridData.trigger("reloadGrid");
                    }
                });
            }
        });
    },
    /**
     * 控制計算方式顯示
     */
    controlShow: function(){
        var value = $("[name=shareFlag]:checked").val();
        $("#shareAmt,#shareRate1,#shareRate2").attr("readonly", true);
        $("#shareMoneyCount1,#shareMoneyCount2").hide().parents(".fg-buttonset").hide();
        //顯示隱藏
        switch (value) {
            case "1":
                // 1.依攤貸金額
                $("#shareMoneyCount2").show().parents(".fg-buttonset").show();
                if (!_M.isReadOnly) {
                    $("#shareAmt").removeAttr("readonly");
                }
                $("#shareRate1,#shareRate2").val("");
                break;
            case "2":
                //2.依攤貸比例          
                $("#shareMoneyCount1").show().parents(".fg-buttonset").show();
                if (!$("[name=shareFlag]:checked").attr("disabled")) {
                    $("#shareAmt").val("");
                }
                if (!_M.isReadOnly) {
                    $("#shareRate1").removeAttr("readonly");
                }
                var countGrid = CLS_BranchAciton.grid.jqGrid('getGridParam', 'records');
                if (countGrid == 0) {
                    $("#shareRate2").removeAttr("readonly");
                }
                break;
            default:
                break;
        }
    },
    /**
     *以比例計算金額
     */
    countByAMT: function(){
        var totel = $("#totalAmt").val().replace(/,/g, ""), value1 = $("#shareRate1").val(), value2 = $("#shareRate2").val(), shareRate1 = parseInt(value1, 10), shareRate2 = parseInt(value2, 10), end = (totel * shareRate1) / shareRate2;
        if (!CLS_BranchAciton.isNumber(end)) {
            end = "";
        } else {
            end = util.addComma(parseInt(end, 10));
        }
        $("#shareAmt").val(end);
    },
    /**
     *變更分母
     */
    changesShareRate: function(){
        $("#newSharteNew").val("");
        _M.doAjax({
            action: "queryChangesShareRate",
            success: function(obj){
                $("#cls_newSharteNewBox").thickbox({
                    title: i18n.cls1151s01["page6.003"],//變更分母
                    width: 200,
                    height: 100,
                    readOnly: _openerLockDoc == "1" || _M.toreadOnly,
                    i18n: i18n.def,
                    modal: true,
                    align: "center",
                    valign: "bottom",
                    open: function(){
                    },
                    buttons: {
                        "sure": function(){
                            if ($("#newSharteNew").val() == 0) {
                                //page6.004=分母不可為0
                                return API.showMessage(i18n.cls1151s01["page6.004"]);
                            }
                            if (!$("#newSharteNewForm").valid()) {
                                return false;
                            }
                            _M.doAjax({
                                action: "saveChangesShareRate",
                                data: {
                                    shareRate: $("#newSharteNew").val()
                                },
                                success: function(obj){
                                    if (obj && obj.drc) {
                                        $("#clsItemDscrC").val(obj.drc);
                                    }
                                    CLS_BranchAciton.grid.trigger("reloadGrid");
                                    $.thickbox.close();
                                }
                            });
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        });
    }
};
_M.pageInitAcion["06"] = PanelAction06;
