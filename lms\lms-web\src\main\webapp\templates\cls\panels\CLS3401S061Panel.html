<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <script type="text/javascript">
					loadScript('pagejs/cls/CLS3401S061Panel');
			</script>
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.baseInfo'}"><!--基本資訊--></th:block></b>
                    </legend>
                    <table class="tb2 alignTopTab " id="top_part" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
							<tr>
							<td width="20%" class="hd2" align="right"><th:block th:text="#{'C340M01A.ownBrId'}">分行名稱</th:block>&nbsp;&nbsp;
							</td>
							<td width="30%" >
								<span id="ownBrId" class="field" />&nbsp;<span id="ownBrIdName" class="field" ></span>
							</td>
							<td width="18%" class="hd2" align="right"><th:block th:text="#{'C340M01A.docStatus'}">文件狀態</th:block>&nbsp;&nbsp;
							</td>
							<td width="32%" ><b class="text-red"><span id="docStatus" ></span>&nbsp;</b>
							</td>
						</tr>
						
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.custId'}">主要借款人</th:block>&nbsp;&nbsp;
							</td>
							<td colspan='3'><span id="custId" class="field" ></span>-<span id="dupNo" class="field" />&nbsp;&nbsp;<span id="custName"></span>
							</td>
							
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.rptId'}">契約書版本</th:block>&nbsp;&nbsp;
							</td>
							<td colspan='3'><span id="rptId_desc" /> 
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C340M01A.ploanCtrNo'}">線上對保契約編號</th:block>&nbsp;
							</td>
							<td><span id='ploanCtrNo' />&nbsp;
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.ploanCtrStatus'}">線上對保契約狀態</th:block>&nbsp;&nbsp;
							</td>
							<td ><b class="text-red"><span id="ploanCtrStatus" ></span></b>  <!-- <span id="rptId" /> -->
							</td>
						</tr>
						<th:block th:if="${showPloanColumn_visible}">
							<!--
							<th:block th:text="#{'label.ploanDataArea'}">線上對保契約</th:block>
							-->							
						<tr>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'C340M01A.ploanCtrExprDate'}">線上對保契約期限</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='ploanCtrExprDate' ></span>&nbsp;
							</td>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'C340M01A.ploanCtrSignTimeM'}">線上對保借款人完成時間</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='ploanCtrSignTimeM' ></span>&nbsp;
								<div>
									<span id='ploanBorrowerIPAddr' ></span>&nbsp;
								</div>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'C340M01A.ploanCtrDcTime'}">線上對保契約作廢時間</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='ploanCtrDcTime' ></span>&nbsp;(<span id='ploanCtrDcUser' ></span>)
							</td>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'C340M01A.ploanCtrSignTime1'}">線上對保保證人完成時間</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='ploanCtrSignTime1' ></span>&nbsp;
								<div>
									<span id='ploanStakeholderIPAddr' />&nbsp;
								</div>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'label.ploanCtrBegDateToEndDate'}">線上對保契約起迄日</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='ploanCtrBegDateToEndDate' ></span>&nbsp;
							</td>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'label.ploanStakeholder'}">利害關係人</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='ploanStakeholder' ></span>&nbsp;
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'label.ploanUploadGrid'}">線上對保檔案</th:block>&nbsp;
							</td>
							<td colspan='3'>
								<div id="test123" style="max-width:600px;" >
									<div id='ploanUploadGrid'></div>
								</div>
							</td>
						</tr>
						</th:block>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.caseNo'}">案號</th:block>&nbsp;&nbsp;</td>
							<td><span id="caseNo" ></span>
								<!-- 
								可能在006號簽報書的性質=新做
								但在008號簽報書的性質=變更條件
								此欄位需要出現在前端，以供釐清是否選錯簽報書 
								-->
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01B.cntrNo'}">額度序號</th:block>&nbsp;
							</td>
							<td><span id="cntrNo" ></span>
							</td>
						</tr>

							<tr>
								<td class="hd2" align="right">生日</td>
								<td><input type="text" id="borrowerBirthDate" name="borrowerBirthDate" readonly="readonly"></td>
								<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'borrowerMobileNumber'}">手機</th:block></td>
								<td><input type="text" id="borrowerMobileNumber" name="borrowerMobileNumber" ></td>
							</tr>

							<tr>
								<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'borrowerEmail'}">信箱</th:block></td>
								<td><input type="text" id="borrowerEmail" name="borrowerEmail" size='45' ></td>
                                <td class="hd2" align="right">存款帳號</td>
                                <td><input type="text" id="loanAcct" name="loanAcct" readonly="readonly" size='40' >
									<!---
									loanAcct : dwdbBASEService.getGuarOnLineAccount(l140m01a.getCustId())
									-->
								</td>
							</tr>


							<tr>
								<td class="ct hd2" colspan="4" style="background-color:rgb(186, 214, 255)">借款條件</td>
							</tr>
						<tr>
							<td class="hd2" align="right">借款金額</td>
							<td><input type="text" id="loanAmt" name="loanAmt" class="numeric" size="7">元</td>
							<td class="hd2" align="right">借款期閒(月)</td>
							<td><input type="text" id="loanPeriod" name="loanPeriod" class="numeric" readonly="readonly" size="2">月</td>
						</tr>
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'loanPurpose'}">借款用途</th:block></td>
								<td colspan='3'>
									本借款用途為甲方受嚴重傳染性肺炎影響致收入減少，供甲方生活支出所需或週轉之用。
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'rawDownType'}">動用方式</th:block></td>
								<td><input type="text" id="drawDownType" name="drawDownType" readonly="readonly"></td>
								<td class="hd2" align="right"><th:block th:text="#{'repaymentMethod'}">還款方式</th:block></td>
								<td><input type="text" id="repaymentMethod" name="repaymentMethod" readonly="readonly" size="40"></td>
							</tr>
							<tr>
								<td class="ct hd2" colspan="4" style="background-color:rgb(186, 214, 255)">借款方案：<span id="lendingPlanInfo_showOption" name="lendingPlanInfo_showOption" class="field"></span></td>
							</tr>
							<tr>	
								<td class="hd2" align="right"><span id="rateTitle" name="rateTitle" class="field">利率</span></td>
								<td colspan="3">
									<span id='rateDesc'></span>&nbsp;
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right"><span id="redemptionTitle" name="redemptionTitle" class="field"></span></td>
								<td colspan="3"><textarea name="redemptionDesc" id="redemptionDesc" cols="100" rows="4" readonly="readonly"></textarea></td>
							</tr>
							<th:block th:if="${showOtherInfoTitle_visible}">
							<tr>
								<td class="hd2" align="right"><span id='otherInfoTitle' ></span>&nbsp;
								</td>
								<td colspan='3'><textarea name="otherInfoDesc" id="otherInfoDesc" cols="80" rows="4" readonly="readonly"></textarea>&nbsp;
								</td>
							</tr>
							</th:block>
							
							<tr>
								<td class="hd2" align="right">管轄法院</td>
								<td colspan="3"><th:block th:text="#{'label.countName.prefix'}">本契約涉訟時，除法律規定具管轄權之法院外，甲乙雙方及保證人同意以臺灣臺北地方法院或</th:block>
												<input type="text" name="courtName" id="courtName" size="10">
												<th:block th:text="#{'label.countName.postfix'}">法院為第一審管轄法院。但法律有專屬管轄法院之特別規定者，從其規定。</th:block>
								</td>
							</tr>
						                        </tbody>
                    </table>
                </fieldset>
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.docUpdateLog'}"><!-- 文件異動紀錄 --></th:block></b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer">
                            <!-- 文件異動紀錄 --><div th:include="common/panels/DocLogPanel :: DocLogPanel"></div>
                        </div>
                    </div>
                    <table class="tb2" id="top_part" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="15%" class="hd1">
                                    <th:block th:text="#{'doc.creator'}"><!--  文件建立者--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="35%">
                                    <span id='creator'></span>(<span id='createTime'></span>)
                                </td>
                                <td width="15%" class="hd1">
                                    <th:block th:text="#{'doc.lastUpdater'}"><!--  最後異動者--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="35%">
                                    <span id='updater'></span>(<span id='updateTime'></span>)
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
               
           
        </th:block>
    </body>
</html>