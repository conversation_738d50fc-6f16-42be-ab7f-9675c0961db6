/*
 * CapPDFResult.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,
		Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.response;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.OutputStream;

import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tw.com.iisi.cap.exception.CapException;

/**
 * <pre>
 * PDF Result
 * PDF檔案下載
 * </pre>
 * 
 * <AUTHOR> Wang
 * @version
 *          <ul>
 *          <li>2011/6/8,Sunkist,new
 *          <li>2011/10/11,Sunkist,update fileName.
 *          </ul>
 */
public class CapPDFResult extends CapByteArrayDownloadResult {

    final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 要下載的PDF內容
     */
    private byte[] byteArrayReport = null;

    /**
     * 檔案名稱
     */
    private String fileName;

    /**
     * 建構子
     */
    public CapPDFResult() {
        super();
    }

    /**
     * 建構子
     * 
     * @param byteArrayReport
     *            要下載的PDF內容
     */
    public CapPDFResult(byte[] byteArrayReport) {
        this.byteArrayReport = byteArrayReport;
    }

    /**
     * 建構子
     * 
     * @param byteArrayReport
     *            要下載的PDF內容
     * @param fileName
     *            檔案名稱
     */
    public CapPDFResult(byte[] byteArrayReport, String fileName) {
        this.byteArrayReport = byteArrayReport;
        this.fileName = fileName;
    }

    /*
     * 取得Log訊息, 顯示檔案長度
     * 
     * @see tw.com.iisi.cap.response.CapByteArrayDownloadResult#getLogMessage()
     */
    @Override
    public String getLogMessage() {
        if (this.byteArrayReport == null) {
            return super.getLogMessage();
        }
        return "report byteArray: " + this.byteArrayReport.length;
    }

    /*
     * 加入結果資料
     * 
     * @see tw.com.iisi.cap.response.CapByteArrayDownloadResult#add(tw.com.iisi.cap.response.IResult)
     */
    @Override
    public void add(IResult result) {
        if (result instanceof CapPDFResult) {
            CapPDFResult r = (CapPDFResult) result;
            this.byteArrayReport = r.byteArrayReport;
            this.fileName = r.fileName;
        }
    }

    /*
     * 輸出檔案, 回傳結果
     * 
     * @see tw.com.iisi.cap.response.CapByteArrayDownloadResult#respondResult(javax.servlet.ServletResponse)
     */
    @Override
    public void respondResult(ServletResponse response) throws CapException {
        int length = -1;
        InputStream in = null;
        OutputStream output = null;
        try {
            response.setContentType("application/pdf");
            response.setContentLength(byteArrayReport.length);
            if (fileName != null && response instanceof HttpServletResponse) {
                HttpServletResponse resp = (HttpServletResponse) response;
                HttpServletRequest req = (HttpServletRequest) _request.getServletRequest();
                String userAgent = req.getHeader("USER-AGENT");
                if (StringUtils.contains(userAgent, "MSIE")) {
                    resp.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                } else {
                    resp.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + fileName);
                }
                resp.setHeader("Cache-Control", "public");
                resp.setHeader("Pragma", "public");
            }
            output = response.getOutputStream();
            // Stream to the requester.
            byte[] bbuf = new byte[1024 * 1024];

            in = new ByteArrayInputStream(byteArrayReport);
            while ((in != null) && ((length = in.read(bbuf)) != -1)) {
                output.write(bbuf, 0, length);
            }
            output.flush();
        } catch (Exception e) {
            throw new CapException(e.getMessage(), getClass());
        } finally {
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(output);
        }
    }
}
