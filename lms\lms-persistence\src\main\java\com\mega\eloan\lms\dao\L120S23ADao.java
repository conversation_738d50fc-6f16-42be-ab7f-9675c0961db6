/* 
 * L120S23ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S23A;

/** RWA風險性資產資料檔 **/
public interface L120S23ADao extends IGenericDao<L120S23A> {

	L120S23A findByOid(String oid);
	
	List<L120S23A> findByMainId(String mainId);

	List<L120S23A> findByMainIdCntrNo(String mainId, String rwaCntrNo);
}