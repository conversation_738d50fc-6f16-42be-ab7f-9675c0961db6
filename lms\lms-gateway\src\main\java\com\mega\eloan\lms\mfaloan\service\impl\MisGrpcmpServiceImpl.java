package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.exception.CapException;

import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.sso.service.BranchService;

@Service
public class MisGrpcmpServiceImpl extends AbstractMFAloanJdbc implements
		MisGrpcmpService {

	@Resource
	BranchService branchService;

	public List<?> findGrpcmpForL120S05A1(String grpNo) {
		return this.getJdbc().queryForList("MISGRPCMP.selL120s05a1",
				new String[] { grpNo, grpNo });
	}

	public List<?> findGrpcmpForL120S05A2(String grpNo) {
		return this.getJdbc().queryForList("MISGRPCMP.getL120s05a2",
				new String[] { grpNo });
	}

	public List<?> findGrpcmpForL120S05B1(String grpNo) {
		return this.getJdbc().queryForList("MISGRPCMP.selL120s05b1",
				new String[] { grpNo });
	}

	public List<?> findGrpcmpForLNF022V15(String grpNo) {
		return this.getJdbc().queryForList("MISGRPCMP.selLNF022V15",
				new String[] { grpNo });
	}

	public List<?> findGrpcmpForLNF022V11(String grpNo) {
		return this.getJdbc().queryForList("MISGRPCMP.selLNF022V11",
				new String[] { grpNo });
	}

	public List<?> findGrpcmpForGrpid(String id, String dupno) {
		return this.getJdbc().queryForList("MISGRPCMP.selGrpid",
				new String[] { id, dupno },0,1);
	}

	public List<?> findGrpcmpForCmpnm(String grpNo) {
		return this.getJdbc().queryForList("MISGRPCMP.selCmpnm",
				new String[] { grpNo });
	}
	
	public List<Map<String, Object>> findGrpcmpForCmpnm1(String grpNo) {
		return this.getJdbc().queryForList("MISGRPCMP.selCmpnm",
				new String[] { grpNo });
	}

	public List<?> findGrpcmpForDW_ROCLIST_ECUS_Main1(String val) {
		return this.getJdbc().queryForList(
				"MISGRPCMP.selDW_ROCLIST_ECUS_Main1", new String[] { val });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisGrpcmpService#findAllByGrpId(java
	 * .lang.String)
	 */
	@Override
	public List<Map<String, Object>> findAllByGrpId(String grpId) {
		return getJdbc().queryForList("GROCMP.findAllByGrpId",
				new String[] { grpId });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisGrpcmpService#findAllByGrpId2(java
	 * .lang.String)
	 */
	@Override
	public List<Map<String, Object>> findAllByGrpId2(String grpId) {
		return getJdbc().queryForList("GROCMP.findAllByGrpId2",
				new String[] { grpId });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisGrpcmpService#findByCustIdAndDupNo
	 * (java.lang.String, java.lang.String)
	 */
	@Override
	public Map<String, Object> findByCustIdAndDupNo(String custId, String dupNo) {
		Map<String, Object> result = getJdbc().queryForMap(
				"GROCMP.findByCustIdAndDupNo", new String[] { custId, dupNo });
		if (result != null && result.get("BAN") != null) {
			return result;
		}
		return null;
	}

	@Override
	public List<Map<String, Object>> findGrpbrnBranch() throws CapException {
		return getJdbc().queryForList("GRPCMP.findGrpbrnBranch", null);
	}

	@Override
	public List<Map<String, Object>> findGrpbrnBranchByBan(String ban,
			String dupNo) {
		return getJdbc().queryForList("GRPCMP.findGrpbrnBranchByBan",
				new Object[] { ban, dupNo });
	}

	@Override
	public int addGrpcmp(String grpid, String ben, String dupno, String cmpnm,
			String kind, String poscd, String typecd, String gbanyn,
			String gban, String branch, String overseas, String updater,
			Timestamp updateTime) {
		// 新增
		// INSERT INTO MIS.GRPCMP(GRPID, BAN, DUPNO, CMPNM, KIND, POSCD, TYPCD,
		// GBANYN, GBAN, BRANCH, OVERSEAS, UPDATER, TMESTAMP)
		// VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?)
		return getJdbc().update(
				"GRPCMP.insert",
				new Object[] { grpid, ben, dupno, cmpnm, kind, poscd, typecd,
						gbanyn, gban, branch, overseas, updater, updateTime });
	}

	/**
	 * 刪除集團轄下公司基本資料檔
	 * 
	 * @param grpid
	 *            集團代號
	 * @param ben
	 *            轄下公司（個人戶）統一編號
	 * @param dupno
	 *            轄下公司重覆序號
	 * @return 成功筆數
	 */
	public int delGrpcmp(String grpid, String ben, String dupno) {
		return getJdbc().update("GRPCMP.delete",
				new Object[] { grpid, ben, dupno });
	}

	@Override
	public int delGrpcmpByGrpid(String grpid) {
		return getJdbc().update("GRPCMP.deleteByGrpid", new Object[] { grpid });
	}


	@Override
	public List<Map<String, Object>> findGrpcmpSelGrpdtl(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("MISGRPCMP.selGrpdtl",
				new String[] { custId, dupNo });
	}

	public List<Map<String, Object>> findGrpcmpSelGrpGrade(String grpId,
			String grpYY) {
		return this.getJdbc().queryForList("MISGRPCMP.selGrpGrade",
				new String[] { grpId, grpYY },0,1);
	}	
	
	@Override
	public List<Map<String, Object>> findByGrpIDOnly100Record(String groupId) {
		return getJdbc().queryForList("GRPCMP.findByGrpIDOnly100Record",
				new String[] { groupId }, 1, Integer.MAX_VALUE);
	}
}
