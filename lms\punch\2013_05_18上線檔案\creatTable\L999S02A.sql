---------------------------------------------------------
-- LMS.L999S02A 連帶保證書檔
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L999S02A;
CREATE TABLE LMS.L999S02A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	GUASDATEY     VARCHAR(3)   ,
	GUASDATEM     VARCHAR(2)   ,
	GUASDATED     VARCHAR(2)   ,
	GUAEDATEY     VARCHAR(3)   ,
	GUAEDATEM     VARCHAR(2)   ,
	GUAEDATED     VARCHAR(2)   ,
	GUAAMT        DECIMAL(15,0),
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L999S02A PRIMARY KEY(OID)
) IN  EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L999S02A IS '連帶保證書檔';
COMMENT ON LMS.L999S02A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	GUASDATEY     IS '保證期間(起)_年', 
	GUASDATEM     IS '保證期間(起)_月', 
	GUASDATED     IS '保證期間(起)_日', 
	GUAEDATEY     IS '保證期間(迄)_年', 
	GUAEDATEM     IS '保證期間(迄)_月', 
	GUAEDATED     IS '保證期間(迄) _日', 
	GUAAMT        IS '保證金額', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
