/* 
 * L140MM5ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140MM5A;

/** eLoan各系統客戶ID欄位檔 **/
public interface L140MM5ADao extends IGenericDao<L140MM5A> {

	L140MM5A findByOid(String oid);
	
	L140MM5A findByMainIdUnique(String mainId);
	
	List<L140MM5A> findByMainId(String mainId);

	List<L140MM5A> findByIndex01(String mainId);

	List<L140MM5A> findByIndex02(String ownBrId, String dataYM);
}