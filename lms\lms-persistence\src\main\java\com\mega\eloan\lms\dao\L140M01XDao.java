/* 
 * L140M01XDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01X;

/** 高價住宅貸款檢核檔 **/
public interface L140M01XDao extends IGenericDao<L140M01X> {

	L140M01X findByOid(String oid);
	
	List<L140M01X> findByMainId(String mainId);
	
	L140M01X findByUniqueKey(String mainId, String custid, String dupno);
}