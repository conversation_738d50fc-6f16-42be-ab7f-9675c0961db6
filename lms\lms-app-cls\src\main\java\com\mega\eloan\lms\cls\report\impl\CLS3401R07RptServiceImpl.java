package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.Phrase;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.ColumnText;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfGState;
import com.lowagie.text.pdf.PdfReader;
import com.lowagie.text.pdf.PdfStamper;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.cls.report.CLS3401R07RptService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01E;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C340M01A;

@Service("cls3401r07rptservice")
public class CLS3401R07RptServiceImpl implements CLS3401R07RptService {
	private static final DateFormat S_FORMAT = new SimpleDateFormat(
			UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS3401R07RptServiceImpl.class);
	
	@Resource
	CLSService clsService;

	@Resource
	ContractDocService contractDocService;
	
	@Override
	public byte[] gen_ctrTypeC_pdf_contract(C340M01A c340m01a, C160S01D c160s01d, C122M01A c122m01a, C122M01E c122m01e)  
	throws Exception{
		return getDocumentWithWaterMark(to_byte_array(gen_pdf_contract(c340m01a, c160s01d, c122m01a, c122m01e)), get_waterMark_text(c340m01a, c122m01a));
	}

	@Override
	public byte[] gen_ctrTypeC_pdf_deductWageAgrmt(C340M01A c340m01a,
			C160S01D c160s01d, C122M01A c122m01a, C122M01E c122m01e) 
	throws Exception{
		return getDocumentWithWaterMark(to_byte_array(gen_pdf_deductWageAgrmt(c340m01a, c160s01d, c122m01a, c122m01e)), get_waterMark_text(c340m01a, c122m01a));
	}
	
	//https://stackoverflow.com/questions/52748248
	private byte[] getDocumentWithWaterMark(byte[] documentBytes, String waterMark_text) {
		BaseFont bfont;
		String ttcFile = null;
		float font_size = 24;
		float rotation = 60;;
		float opacity_depth_color = 0.1f;
		try{
			ttcFile = PropUtil.getProperty("ttcFile.dir") + PropUtil.getProperty("itext.mingliuFile") + ",0";
			bfont = BaseFont.createFont(ttcFile, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
			//===========================
		    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		    // pdf
		    PdfReader reader = new PdfReader(documentBytes);
		    int n = reader.getNumberOfPages();
		    PdfStamper stamper = new PdfStamper(reader, outputStream);
		    // text watermark
		    //用 Font.BOLD 的寫法，不能顯示中文字 Font font = new Font(Font.BOLD, 36);
		    Font font = new Font(bfont, font_size);
		    Phrase phrase = new Phrase(waterMark_text, font);
		    // transparency
		    PdfGState gs1 = new PdfGState();
		    gs1.setFillOpacity(opacity_depth_color);
		    // properties
		    PdfContentByte over;
		    Rectangle pagesize;
		    float x, y;
		    // loop over every page (in case more than one page)
		    for (int i = 1; i <= n; i++) {
		        pagesize = reader.getPageSizeWithRotation(i);
		        x = (pagesize.getLeft() + pagesize.getRight()) / 2;
		        y = (pagesize.getTop() + pagesize.getBottom()) / 2;
		        over = stamper.getOverContent(i);
		        over.saveState();
		        over.setGState(gs1);
		        // add text
		        ColumnText.showTextAligned(over, Element.ALIGN_CENTER, phrase, x, y, rotation);
		        over.restoreState();
		    }
		    stamper.close();
		    reader.close();
		    return outputStream.toByteArray();
		}catch(Exception e){
			LOGGER.error(StrUtils.getStackTrace(e));
			return documentBytes;
		}
	}

	private String get_waterMark_text(C340M01A c340m01a, C122M01A c122m01a){
		String ctrNo = Util.trim(c340m01a.getPloanCtrNo());
		String ts = S_FORMAT.format(c122m01a.getAgreeQueryEJTs());
		String yyyy = StringUtils.substring(ts, 0, 4);
		String month = StringUtils.substring(ts, 5, 7);
		String day = StringUtils.substring(ts, 8, 10);
		String hour = StringUtils.substring(ts, 11, 13);
		String minutes = StringUtils.substring(ts, 14, 16);
		String seconds = StringUtils.substring(ts, 17, 19);
		return "契約編號:" + ctrNo + " 簽約時間:"+yyyy+"年"+month+"月"+day+"日"+hour+"時"+minutes+"分"+seconds+"秒";
	}
	
	private byte[] to_byte_array(OutputStream os) {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) os;
			return baos.toByteArray();
		} catch(Exception e) {
			LOGGER.error(StrUtils.getStackTrace(e));
		} finally {
			IOUtils.closeQuietly(baos);
		}
		return null;
	}

	private OutputStream gen_pdf_contract(C340M01A c340m01a, C160S01D c160s01d, C122M01A c122m01a, C122M01E c122m01e)
			throws Exception {
		Locale locale = LMSUtil.getLocale();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		Map<String, String> ctrParam_from_C122M01A = ClsUtility.get_ctrParam_from_C122M01A(c122m01a);
		// =========================
		String rpt_path = "report/cls/CLS3401R07C"+MapUtils.getString(ctrParam_from_C122M01A, "rptVer")+"_" + locale.toString()
				+ ".rpt";
		String lnFromDate = TWNDate.toAD(c160s01d.getLnFromDate());
		String lnEndDate = TWNDate.toAD(c160s01d.getLnEndDate());
		String ctrCheckDate = TWNDate.toAD(c340m01a.getCtrCheckDate());
		int int_totTerm = c160s01d.getMonth();
		String c160s01d_accNo = Util.trim(c160s01d.getAccNo());
		
		String c122m01e_appnWay = c122m01e!=null?Util.trim(c122m01e.getAppnWay()):"";
		String c122m01e_appnBankCode = c122m01e!=null?Util.trim(c122m01e.getAppnBankCode()):"";
		String c122m01e_dpAcct = c122m01e!=null?Util.trim(c122m01e.getDpAcct()):"";
		// =========================
		ReportGenerator generator = new ReportGenerator(rpt_path);
		rptVariableMap.put("custId", c340m01a.getCustId());
		rptVariableMap.put("custName", Util.trim(c340m01a.getCustName()));
		rptVariableMap.put("ploanCtrNo", Util.trim(c340m01a.getPloanCtrNo()));
		rptVariableMap.put("ctrNumberOfConsumerLoan_numeric", MapUtils.getString(ctrParam_from_C122M01A, "ctrNumberOfConsumerLoan_numeric"));
		//個別商議條款 
		//...甲方所出具之「中鋼集團第四十六次從業人員消費性貸款分期扣薪及扣款同意書」...
		rptVariableMap.put("ctrNumberOfConsumerLoan_twNumber", MapUtils.getString(ctrParam_from_C122M01A, "ctrNumberOfConsumerLoan_twNumber")); 
		rptVariableMap.put("ctrYear", MapUtils.getString(ctrParam_from_C122M01A, "ctrYear"));
		rptVariableMap.put("loanAmt", NumConverter.addComma(LMSUtil.pretty_numStr(c160s01d.getLoanTotAmt()))); //申貸金額，002分行會依客戶的DBR22限制去將{原始申貸金額}{往下調整}
		rptVariableMap.put("lnFromDateY_roc", _dt_roc_year(lnFromDate));
		rptVariableMap.put("lnFromDateM", _dt_month(lnFromDate));
		rptVariableMap.put("lnFromDateD", _dt_day(lnFromDate));
		rptVariableMap.put("lnEndDateY_roc", _dt_roc_year(lnEndDate));
		rptVariableMap.put("lnEndDateM", _dt_month(lnEndDate));
		rptVariableMap.put("lnEndDateD", _dt_day(lnEndDate));
		rptVariableMap.put("lnYear", String.valueOf(int_totTerm/12));
		rptVariableMap.put("lnMonth", String.valueOf(int_totTerm%12));
		rptVariableMap.put("ctrCheckDateY_roc", _dt_roc_year(ctrCheckDate));
		rptVariableMap.put("ctrCheckDateM", _dt_month(ctrCheckDate));
		rptVariableMap.put("ctrCheckDateD", _dt_day(ctrCheckDate));
		if(true){
			String rptVer = MapUtils.getString(ctrParam_from_C122M01A, "rptVer");
			if(Util.equals("046", rptVer)){
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_C, "Y");//週轉
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_G, "");//投資理財
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_H, "");//購置耐久性消費品
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_J, "");//子女教育
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_K, "");//繳稅
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_F, "");//留學/遊學
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_OTHERDESC, "");//其他
			}
			if(Util.equals("048", rptVer) || Util.equals("049", rptVer)){
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_N, "Y");//個人週轉金 -->中鋼第48次改為個人週轉金 
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_O, "");//個人消費性用途
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_G, "");//投資理財
			}
		}
		rptVariableMap.put("ploanPlan", Util.trim(c122m01a.getPloanPlan())); 
		String dlvrDate = "";
		String dlvrPbAcctNo = "";
		String dlvrPbBank_mega_flag = "";
		String dlvrPbBank_other_flag = "";
		String dlvrCheck_flag = "";
		
		if( StringUtils.isNotBlank(c160s01d_accNo)){ //撥至 本行帳號
			if(true){
				dlvrDate = TWNDate.toAD(CrsUtil.isNOT_null_and_NOTZeroDate(c160s01d.getRctDate())?c160s01d.getRctDate():c160s01d.getLnFromDate());
			}
			if(true){
				dlvrPbAcctNo = c160s01d_accNo;
			}
			if(true){
				if(Util.equals(ClsUtility.get_ploanPlan_ChinaSteelCorp_HQ(), c122m01a.getPloanPlan())){
					//中鋼總公司
					if(Util.equals("B", c122m01e_appnWay) && Util.equals("017", c122m01e_appnBankCode)){
						dlvrPbBank_mega_flag = "Y";
					}
				}else{
					//中鋼子公司
				}
			}
		}else{
			//非自動進帳 => 應該是 中鋼總公司(支票或郵局)
			if(Util.equals("C", c122m01e_appnWay)){
				dlvrCheck_flag = "Y";
			}else if(Util.equals("B", c122m01e_appnWay)){
				if(StringUtils.isNotBlank(c122m01e_dpAcct)){
					dlvrDate = TWNDate.toAD(CrsUtil.isNOT_null_and_NOTZeroDate(c160s01d.getRctDate())?c160s01d.getRctDate():c160s01d.getLnFromDate());	
				}				
				if(true){
					dlvrPbAcctNo = c122m01e_dpAcct;	
				}
				if(true){
					if(StringUtils.isNotBlank(c122m01e_appnBankCode)){
						if(Util.equals("017", c122m01e_appnBankCode)){
							dlvrPbBank_mega_flag = "Y";
						}else{
							dlvrPbBank_other_flag = "Y";
						}
					}else{
						//異常的資料，撥款至銀行/郵局帳戶，但無「銀行代號」
					}	
				}
			}
		}
		rptVariableMap.put("dlvrDateY_roc", _dt_roc_year(dlvrDate));
		rptVariableMap.put("dlvrDateM", _dt_month(dlvrDate));
		rptVariableMap.put("dlvrDateD", _dt_day(dlvrDate));
		rptVariableMap.put("dlvrPbAcctNo", StringUtils.isNotBlank(dlvrPbAcctNo)?dlvrPbAcctNo:Util.addSpaceWithValue("", 14));
		rptVariableMap.put("dlvrPbBank_mega_flag", dlvrPbBank_mega_flag);
		rptVariableMap.put("dlvrPbBank_other_flag", dlvrPbBank_other_flag);
		rptVariableMap.put("dlvrCheck_flag", dlvrCheck_flag);
		rptVariableMap.put("preliminaryFee", Util.trim(MapUtils.getString(ctrParam_from_C122M01A, "preliminaryFee"))); //開辦手續費 
		rptVariableMap.put("creditCheckFee", Util.trim(MapUtils.getString(ctrParam_from_C122M01A, "creditCheckFee"))); //信用查詢費
		rptVariableMap.put("rateChgRange", Util.trim(MapUtils.getString(ctrParam_from_C122M01A, "rateChgRange"))); //借款利率調整區間
		rptVariableMap.put("totTerm", String.valueOf(int_totTerm));  //雖然一開始是固定申貸7年，但002分行會依客戶何時退休，去調整XLS上傳的借款期限(月)
		if(true){
			/* 無限制清償期 vs 限制清償期
			 * PLOAN的變數，是用 advancedXXX vs limitedXXX 
			*/
			rptVariableMap.put("advancedBaseRate", MapUtils.getString(ctrParam_from_C122M01A, "advancedBaseRate"));
			rptVariableMap.put("advancedPmRate", MapUtils.getString(ctrParam_from_C122M01A, "advancedPmRate"));
			rptVariableMap.put("advancedNowRate", MapUtils.getString(ctrParam_from_C122M01A, "advancedNowRate"));
			rptVariableMap.put("advancedAPR", MapUtils.getString(ctrParam_from_C122M01A, "advancedAPR"));//總費用年百分率
		}
		rptVariableMap.put("crossMarket", Util.trim(c122m01a.getCrossMarket()));
		rptVariableMap.put("rateAdjNotify", Util.trim(c122m01a.getRateAdjNotify()));
		rptVariableMap.put("borrowerSigningDate", Util.trim(TWNDate.toAD(c122m01a.getAgreeQueryEJTs())));
		rptVariableMap.put("borrowerSigningTS", Util.trim(S_FORMAT.format(c122m01a.getAgreeQueryEJTs())));
		rptVariableMap.put("borrowerSigningIP", Util.trim(c122m01a.getAgreeQueryEJIp()));
		rptVariableMap.put("borrowerIdentityType", LMSUtil.getDesc(ploan_code_identity_type_map(), contractDocService.get_ploanIdentityType(c122m01a))); 
		rptVariableMap.put("guaranteeIdentityType", "");
		rptVariableMap.put("guaranteeSigningDate", "");
		rptVariableMap.put("courtName", Util.trim(MapUtils.getString(ctrParam_from_C122M01A, "courtName")));
		String agreeQueryEJDate = Util.trim(MapUtils.getString(ctrParam_from_C122M01A, "agreeQueryEJDate"));
		rptVariableMap.put("agreeEJ_Y_roc", _dt_roc_year(agreeQueryEJDate));//簽署日期-中華民國年
		rptVariableMap.put("agreeEJ_M", _dt_month(agreeQueryEJDate));//簽署日期-月
		rptVariableMap.put("agreeEJ_D", _dt_day(agreeQueryEJDate));//簽署日期-日

		generator.setVariableData(rptVariableMap);
		OutputStream outputStream = generator.generateReport();

		return outputStream;

	}
	
	private String _dt_roc_year(String yyyy_MM_dd){
		if(Util.isEmpty(yyyy_MM_dd)){
			return "   ";
		}
		return Util.getRightStr("000"+(Util.parseInt(StringUtils.substring(yyyy_MM_dd, 0, 4))-1911), 3);
	}
	private String _dt_month(String yyyy_MM_dd){
		if(Util.isEmpty(yyyy_MM_dd)){
			return "  ";
		}
		return Util.getRightStr("00"+(StringUtils.substring(yyyy_MM_dd, 5, 7)), 2);
	}
	private String _dt_day(String yyyy_MM_dd){
		if(Util.isEmpty(yyyy_MM_dd)){
			return "  ";
		}
		return Util.getRightStr("00"+(StringUtils.substring(yyyy_MM_dd, 8, 10)), 2);
	}
	
	private OutputStream gen_pdf_deductWageAgrmt(C340M01A c340m01a,
			C160S01D c160s01d, C122M01A c122m01a, C122M01E c122m01e) throws Exception {
		Locale locale = LMSUtil.getLocale();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		Map<String, String> ctrParam_from_C122M01A = ClsUtility.get_ctrParam_from_C122M01A(c122m01a);
		// =========================
		String rpt_path = "report/cls/CLS3401R07D"+MapUtils.getString(ctrParam_from_C122M01A, "rptVer")+"_" + locale.toString()
				+ ".rpt";

		C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(c122m01a.getMainId(), c122m01a.getCustId(), c122m01a.getDupNo());
		String staffNo = Util.trim(c160s01d.getStaffNo());
		String coTel = "";
		String mTel = "";
		String comName = "";
		String comTel = "";
		if(c120m01a!=null){
			C120S01A c120s01a = clsService.findC120S01A(c120m01a);
			if(c120s01a!=null){
				mTel = Util.trim(c120s01a.getMTel());
				coTel = Util.trim(c120s01a.getCoTel()); //線上貸款的「住宅電話」，帶入 e-Loan 的「通訊電話」欄位
			}
			C120S01B c120s01b = clsService.findC120S01B(c120m01a);
			if(c120s01b!=null){
				comName = Util.trim(c120s01b.getComName());
				comTel = Util.trim(c120s01b.getComTel());
			}
		}
		if(Util.isEmpty(coTel) && Util.equals(ClsUtility.get_ploanPlan_ChinaSteelCorp_HQ(), c122m01a.getPloanPlan())){
			String homePhoneCode = Util.trim(c122m01e.getHomePhoneCode());
			String homePhone = Util.trim(c122m01e.getHomePhone());
			if(Util.isNotEmpty(homePhone)){
				coTel = get_tel_output(homePhoneCode, homePhone);				
			}
		}
		if(Util.isEmpty(comTel) && Util.equals(ClsUtility.get_ploanPlan_ChinaSteelCorp_HQ(), c122m01a.getPloanPlan())){
			String companyPhoneCode = Util.trim(c122m01e.getCompanyPhoneCode());
			String companyPhone = Util.trim(c122m01e.getCompanyPhone());
			if(Util.isNotEmpty(companyPhone)){
				comTel = get_tel_output(companyPhoneCode, companyPhone);				
			}
		}
		ReportGenerator generator = new ReportGenerator(rpt_path);
		rptVariableMap.put("custId", c340m01a.getCustId());
		rptVariableMap.put("custName", Util.trim(c340m01a.getCustName()));
		rptVariableMap.put("ploanCtrNo", Util.trim(c340m01a.getPloanCtrNo()));
		rptVariableMap.put("ctrNumberOfConsumerLoan_twNumber", MapUtils.getString(ctrParam_from_C122M01A, "ctrNumberOfConsumerLoan_twNumber"));
		rptVariableMap.put("ctrYear", MapUtils.getString(ctrParam_from_C122M01A, "ctrYear"));
		rptVariableMap.put("staffNo", staffNo);
		rptVariableMap.put("coTel", coTel);
		rptVariableMap.put("comName", comName);
		rptVariableMap.put("comTel", comTel);
		rptVariableMap.put("mTel", mTel);
		rptVariableMap.put("borrowerSigningDate", Util.trim(TWNDate.toAD(c122m01a.getAgreeQueryEJTs())));
		rptVariableMap.put("borrowerSigningTS", Util.trim(S_FORMAT.format(c122m01a.getAgreeQueryEJTs())));
		rptVariableMap.put("borrowerSigningIP", Util.trim(c122m01a.getAgreeQueryEJIp()));
		rptVariableMap.put("borrowerIdentityType", LMSUtil.getDesc(ploan_code_identity_type_map(), contractDocService.get_ploanIdentityType(c122m01a)));
		generator.setVariableData(rptVariableMap);
		OutputStream outputStream = generator.generateReport();

		return outputStream;
	}
	
	private String get_tel_output(String code, String phone){
		if(Util.isEmpty(phone)){
			return "";
		}
		if(Util.isEmpty(code)){
			return phone;
		}
		return "("+code+")"+phone;
	}
		
	/* select * from dbo.code_identity_type 
	 */
	private Map<String, String> ploan_code_identity_type_map(){
		Map<String, String> r = new HashMap<String, String>(); 
		r.put("credit", 	"簡訊密碼認證(信用卡戶)");
		r.put("deposit", 	"簡訊密碼認證(存款戶)");
		r.put("eloan", 		"eloan");
		r.put("loan", 		"簡訊密碼認證(貸款戶)");
		r.put("moica", 		"自然人憑證");
		r.put("other-bank", "他行認證");
		r.put("otp", 		"簡訊密碼認證");
		r.put("pib", 		"網銀認證");
		r.put("skip", 		"略過認證");
		return r;
	} 
}
