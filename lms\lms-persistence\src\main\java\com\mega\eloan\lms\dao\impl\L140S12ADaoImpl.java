/* 
 * L140S12ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L140S12ADao;
import com.mega.eloan.lms.model.L140S12A;

/** 其他續做條件追蹤分項 **/
@Repository
public class L140S12ADaoImpl extends LMSJpaDao<L140S12A, String>
	implements L140S12ADao {

	@Override
	public List<L140S12A> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		return createQuery(search).getResultList();
	}
	
	@Override
	public L140S12A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S12A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		List<L140S12A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seqNum", false);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140S12A> findByIndex01(String mainId){
		ISearch search = createSearchTemplete();
		List<L140S12A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public L140S12A findMaxSeqNumByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seqNum", true);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S12A> findByCaseMainId(String caseMainId) {
		ISearch search = createSearchTemplete();
		List<L140S12A> list = null;
		if (caseMainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "caseMainId", caseMainId);
		search.addOrderBy("seqNum", false);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}