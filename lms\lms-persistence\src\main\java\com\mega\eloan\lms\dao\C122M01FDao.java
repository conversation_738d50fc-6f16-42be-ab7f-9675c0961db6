/* 
 * C122M01FDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C122M01F;

/** 進件管理資料檔 **/
public interface C122M01FDao extends IGenericDao<C122M01F> {

	C122M01F findByOid(String oid);
	
	List<C122M01F> findByMainId(String mainId);
	
	C122M01F findByUniqueKey(String mainId);

	List<C122M01F> findByIndex01(String mainId);
}