/* 
 * C122M01EDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.C122M01EDao;
import com.mega.eloan.lms.model.C122M01E;

/** 團體消貸名單控制檔 **/
@Repository
public class C122M01EDaoImpl extends LMSJpaDao<C122M01E, String>
	implements C122M01EDao {

	@Override
	public C122M01E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public C122M01E findByUniqueKey(String grpCntrNo, String custId){
		ISearch search = createSearchTemplete();
		if (grpCntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "grpCntrNo", grpCntrNo);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C122M01E> findByIndex01(String grpCntrNo, String custId){
		ISearch search = createSearchTemplete();
		List<C122M01E> list = null;
		if (grpCntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "grpCntrNo", grpCntrNo);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public C122M01E findByRefMainId(String refMainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "refMainId", refMainId);
		return findUniqueOrNone(search);
	}
}