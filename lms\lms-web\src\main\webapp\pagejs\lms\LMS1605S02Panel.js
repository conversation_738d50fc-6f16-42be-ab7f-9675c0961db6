var initDfd = initDfd || $.Deferred()
initDfd.done(function(json){

    //全部已收 //全部免付
    $("#allCast,#allCancel").click(function(){
        $(this).attr("id") === "allCast" ? $(".s1").val("1") : $(".s1").val("0");
    });
    
	
	
	
    $('#useSelect,#lnSelect,#tType').change(function(){
    	switch ($(this).attr("id")) {
            case "useSelect": //動用期限切換 
            	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
                $("#the" + DOMPurify.sanitize($(this).val())).show().siblings(".the").hide().find(":input").val("");
                break;
            case "lnSelect": //授信期限切換
            	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
                $("#two" + DOMPurify.sanitize($(this).val())).show().siblings(".two").hide().find(":input").val("");
                break;
            case "tType"://授信契約書
            	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
            	DOMPurify.sanitize($(this).val()) == "1" ? $("#long").hide() : $("#long").show();
                break;
				
        }
    });
	
	//BGN J-105-0079-001 Web e-Loan授信管理系統修改柬埔寨地區分行動審表。
	$('#noEdit1,#noEdit2,#noEdit3,#noEdit4').change(function(){
		
        switch ($(this).attr("id")) {
            case "noEdit1": //免填 授信約定書簽約日期
                 $(this).is(':checked') ? $(".hideEdit1").hide() : $(".hideEdit1").show();
                break;
            case "noEdit2": //免填 連帶保證書最高本金
               $(this).is(':checked') ? $(".hideEdit2").hide() : $(".hideEdit2").show();
                break;
            case "noEdit3"://免填 違約金計算條件
                $(this).is(':checked') ? $(".hideEdit3").hide() : $(".hideEdit3").show();
                break;
			case "noEdit4"://免填 計收遲延利息加碼
                $(this).is(':checked') ? $(".hideEdit4").hide() : $(".hideEdit4").show();
                break;	
				
        }
    });
	
	
	/**
     * 重新引進
     */
    $("#reloadAll").click(function(){
        $.ajax({
            handler: inits.fhandle,
            action: "reloadL160M01C",
            data: {
                mainId: $("#mainId").val()
            }
        }).done(
            function(obj){
                $(".creatTr").html("");
                creatL160M01C(obj);
            }
        );
    });
	
	//END J-105-0079-001 Web e-Loan授信管理系統修改柬埔寨地區分行動審表。
    
    //L160M01A.message46=貸放手續齊全，擬准予動用
    $("#getWord").click(function(){
        $("#comm").val(i18n.lms1605m01['L160M01A.message46']);
    })
    
	/**
     * 查詢主債務人+從債務人名單
     */
    function queryCustList(){
        var result = {};
        $.ajax({
            async: false,
            handler: inits.fhandle,
            action: 'queryCust',
            formId: 'empty',
            data: {
                mainId: $("#mainId").val()
            }
        }).done(
            function(response){
                $.extend(result, response.cust);
            }
        );
        return result;
    }
	
    //黑名單查詢
//    $("#selecttheblack").click(function(){
//        //queryBlackBox();
//        $.ajax({
//            handler: inits.fhandle,
//            data: {
//                formAction: "queryBlackInit",
//                mainId: $("#mainId").val()
//            },
//            success: function(obj){
//                $.thickbox.close();
//                if (obj.showBox) {
//                    queryBlackBox();
//                } else {
//                    $("#blackDataDate").val(obj.blackDataDate);
//                    var regex = /<br\s*[\/]?>/gi;
//                    $('#blackListTxtOK').val(obj.blackListTxtOK.replace(regex, "\n"));
//                }
//                
//                
//            }
//        });
//    });
    
	//黑名單查詢
    $("#selecttheblack").click(function(){
        BlackNameAction.query({
            cust: queryCustList(),
            callback: function(response){
                $('#blackDataDate').val(util.getToday());
				var regex = /<br\s*[\/]?>/gi;
                $('#blackListTxtOK').val(BlackNameAction.parseMessage().replace(regex, "\n"));
				
            }
        });
        //queryBlackBox();
        //        $.ajax({
        //            handler: inits.fhandle,
        //            data: {
        //                formAction: "queryBlackInit",
        //                mainId: $("#mainId").val()
        //            },
        //            success: function(obj){
        //                $.thickbox.close();
        //                if (obj.showBox) {
        //                    queryBlackBox();
        //                } else {
        //                    $("#blackDataDate").val(obj.blackDataDate);
        //                    $("#blackListTxtOK").val(obj.blackListTxtOK);
        //                }
        //                
        //                
        //            }
        //        });
    });
	
    //黑名單查詢視窗
    function queryBlackBox(){
        $("#blackbox").thickbox({
            // L160M01A.blackListTxtErr = 黑名單查詢
            title: i18n.lms1605m01['L160M01A.blackListTxtErr'],
            width: 400,
            height: 120,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    //只能打英文的 reg
                    var engNameReg = /\w/;
                    if (!$("#blackName").val().match(engNameReg)) {
                    
                        //L160M01A.message40=英文名稱輸入錯誤，必須為半形英文字
                        return CommonAPI.showErrorMessage(i18n.lms1605m01["L160M01A.message40"]);
                    }
                    
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "queryBlackPage",
                            name: $("#blackName").val()
                        }
                    }).done(
                        function(obj){
                            $.thickbox.close();
                            $("#blackDataDate").val(obj.blackDataDate);
                            $("#blackListTxtOK").val(obj.blackListTxtOK);
                        }
                    );
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    
    //上傳檔案按鈕
    $("#uploadFile").click(function(){
        var limitFileSize = 3145728;
        MegaApi.uploadDialog({
            //動用審核表
            fieldId: "useDoc",
            fieldIdHtml: "size='30'",
            fileDescId: "fileDesc",
            fileDescHtml: "size='30' maxlength='30'",
            subTitle: i18n.def('insertfileSize', {
                'fileSize': (limitFileSize / 1048576).toFixed(2)
            }),
            limitSize: limitFileSize,
            width: 320,
            height: 190,
            data: {
                mainId: $("#mainId").val(),
                sysId: "LMS"
            },
            success: function(){
                gridfile.trigger("reloadGrid");
            }
        });
    });
    
    //刪除檔案按鈕
    $("#deleteFile").click(function(){
        var select = gridfile.getGridParam('selarrrow');
        if (select == "") {
        
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var data = [];
                for (var i =0; i < select.length; i++) {
                    data.push(gridfile.getRowData(select[i]).oid);
                }
                
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteUploadFile",
                        oids: data
                    }
                }).done(
                    function(obj){
                        gridfile.trigger("reloadGrid");
                    }
                );
            } else {
                return;
            }
        });
    });
    
    
    //檔案上傳grid
    var gridfile = $("#gridfile").iGrid({
        handler: inits.ghaddle,
        height: 150,
        postData: {
            formAction: "queryfile",
            //動用審核表
            fieldId: "useDoc",
            mainId: responseJSON.mainId
        },
        rowNum: 15,
        multiselect: true,
        colModel: [{
            colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
            name: 'srcFileName',
            width: 120,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: download
        }, {
            colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
            name: 'fileDesc',
            width: 140,
            align: "center",
            sortable: true
        }, {
            colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
            name: 'uploadTime',
            width: 140,
            align: "center",
            sortable: true
        }, {
            name: 'oid',
            hidden: true
        }]
    });
    
    
    //檔案下載
    function download(cellvalue, options, data){
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
                fileOid: data.oid
            }
        });
    }
    
    
    
});
