package com.mega.eloan.lms.lrs.panels;

import com.mega.eloan.common.panels.Panel;
import org.springframework.ui.ModelMap;

import tw.com.jcs.auth.AuthType;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.model.L181M01A;

/**
 * <pre>
 * 覆審名單
 * </pre>
 */
public class LMS1810S02Panel extends Panel {

	public LMS1810S02Panel(String id) {
		super(id);
	}
	
	public LMS1810S02Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		addAclLabel(model, new AclLabel("w_btn_fcrdGrad", params, L181M01A.class,
				AuthType.Modify, RetrialDocStatusEnum.編製中));;
	}

	private static final long serialVersionUID = 1L;

}
