var _handler = "cls2701formhandler";
$(function(){
	
	if(viewstatus=="010"){
		var pageAction_grid_height = 250;
		
		$.ajax({
	        handler: _handler,
	        type: "POST",
	        dataType: "json",
	        data: {
	            formAction: "loadData"
	        },
	        success: function(json){
	        	var _addSpace = true;
				$.each(json.itemOrder, function(idx, k) {
	        		var currobj = {};
	        		currobj[k] = json.item[k];
	        		
	        		//select
					$("#selectC900M01I").setItems({ item: currobj, format: "{key}", clear:false, space: (_addSpace?(idx==0):false) });
				});
	        }
	    });	
		
		if($("#gridL120M01A").length>0){
			$("#selectC900M01I").change(function(){
				   
	    		$("#gridL120M01A").setGridParam({
	                postData: {
	                	'c900m01i_mainId': $("#selectC900M01I").val()
	                },
	                search: true
	            }).trigger("reloadGrid");
	        });
	        
			var gridL120M01A = $("#gridL120M01A").iGrid({
		        handler: "cls2701gridhandler",
		        height: pageAction_grid_height,
		        rowNum: 15,
		        shrinkToFit: false,
		        multiselect: true,
		        needPager: false,
	            postData: {
	                'formAction': 'queryL120M01A'
	                ,'c900m01i_mainId': $("#selectC900M01I").val() || ''
	            },		       
		        colModel: [
		            {
			            colHeader: i18n.cls2701v01["l120m01a.approvetime"], // 核准日期
			            align: "center",
			            width: 65, // 設定寬度
			            sortable: true, // 是否允許排序			
			            name: 'endDate',
			            formatter: 'date',
			            formatoptions: {
			                srcformat: 'Y-m-d',
			                newformat: 'Y-m-d'
			            },
			            hidden: false 
			        }, {
			            colHeader: i18n.cls2701v01["l120m01a.casedate"], // 簽案日期
			            align: "center",
			            width: 70,
			            sortable: true,
			            name: 'caseDate'
			        }, {
			            colHeader: i18n.cls2701v01["l120m01a.custid"], // 統一編號
			            align: "left",
			            width: 80,
			            sortable: true,
			            name: 'custId'
			        }, {
			            colHeader: i18n.cls2701v01["l120m01a.custname"], // 客戶名稱
			            align: "left",
			            width: 120,
			            sortable: true,
			            name: 'custName'
			        }, {
			            colHeader: i18n.cls2701v01["l120m01a.caseno"], // 案件號碼
			            align: "left",
			            width: 230,
			            sortable: true,
			            name: 'caseNo'
			        }
			        , { name: 'oid', hidden: true }
			        , { name: 'mainId', hidden: true }
			        ]
		    });
			    
		}
	}
	
	
	//default div
	var $gridview = $("#gridview").iGrid({
        handler: "cls2701gridhandler",
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        multiselect: true,  
        postData: {
            formAction: "queryC900S01I",
            docStatus : viewstatus	
        },
        colModel: [
            {
            	colHeader: "",name: 'oid', hidden: true
	        }, {
	        	colHeader: "",name: 'mainId', hidden: true
	        }, {
	        	colHeader: i18n.cls2701v01["C900M01I.orgCustId"],
	            align: "left", width: 80, sortable: true, name: 'orgCustId'
	        }, {
	            colHeader: ' ',
	            align: "left", width: 20, sortable: true, name: 'orgDupNo'
	        }, {
	            colHeader: i18n.cls2701v01["C900M01I.custId"],
	            align: "left", width: 80, sortable: true, name: 'custId'
	        }, {
	            colHeader: ' ',
	            align: "left", width: 20, sortable: true, name: 'dupNo'
	        }, {
	            colHeader: i18n.cls2701v01["C900M01I.custName"],
	            align: "left", width: 110, sortable: true, name: 'custName'
	        }, {
	            colHeader: i18n.cls2701v01["C900S01I.caseDate"], //簽案日期
	            align: "left", width: 70, sortable: true, name: 'caseDate'
	        }, {
	            colHeader: i18n.cls2701v01["C900S01I.caseNo"], //簽報書案號
	            align: "left", width: 160, sortable: true, name: 'caseNo'
	        }, {
	        	colHeader: i18n.cls2701v01["C900S01I.updater"], //異動人員
	            align: "left", width: 80, sortable: true, name: 'updater'
	        }, {
	        	colHeader: i18n.cls2701v01["C900S01I.approver"], //核准人員
	            align: "left", width: 80, sortable: true, name: 'approver'
	        }, {
	        	colHeader: i18n.cls2701v01["C900S01I.approveTime"], //核准日期
	            align: "left", width: 80, sortable: true, name: 'approveTime'
	        }
	     ]
    });
		
	
	
    $("#buttonPanel").find("#btnFilter").click(function(){
    	var _id = "_div_cls2701v01_filter";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).size() == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' >");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls2701v01["C900M01I.orgCustId"]+"</td><td>"
						+"<input type='text' id='search_orgCustId' name='search_orgCustId'  maxlength='10'>"
						+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls2701v01["C900M01I.custId"]+"</td><td>"
					+"<input type='text' id='search_custId' name='search_custId'  maxlength='10'>"
					+"</td></tr>");
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
			//ui_lms2401.msg02=請選擇不覆審原因
	       title: '篩選',
	       width: 550,
           height: 190,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
                  $.thickbox.close();
                  //=============
                  $gridview.setGridParam({
  	                postData: $.extend(
  	                	{}
  	                	,$("#"+_form).serializeData()
  	                ),
  	                search: true
                  }).trigger("reloadGrid");
               },
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
    }).end().find("#btnAdd").click(function(){
    	$("#div_insertC900S01I").thickbox({
			title : '',
			width : 780,
			height : pageAction_grid_height + 200,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.def,
			buttons : {
				'sure' : function(){
					var rowId_arr = $("#gridL120M01A").getGridParam('selarrrow');
			      	var oid_arr = [];
			      	for (var i = 0; i < rowId_arr.length; i++) {
			      		var data = $("#gridL120M01A").getRowData(rowId_arr[i]);
			      		oid_arr.push(data.oid);
			        }

			      	if(oid_arr.length==0){
			      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
			      		return;
			      	}
			      	
			      	$.ajax({
			            type: "POST",
			            handler: _handler,
			            data: {
			                formAction: "insertC900S01I" ,
			                'c900m01i_mainId': $("#selectC900M01I").val() || '' , 
			                'oid_arr': oid_arr
			            },
			            success: function(json){
			               $.thickbox.close();
			         	   $gridview.trigger("reloadGrid");	
			            }
			         });
				},
				'close' : function(){	
					$.thickbox.close();
				}
			}
		});
    }).end().find("#btnDeliver").click(function(){ //編製中-呈主管
    	var rowId_arr = $gridview.getGridParam('selarrrow');
      	var oid_arr = [];
      	for (var i = 0; i < rowId_arr.length; i++) {
      		var data = $gridview.getRowData(rowId_arr[i]);
      		oid_arr.push(data.oid);
        }

      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
      	$.ajax({
            type: "POST",
            handler: _handler,
            data: {
                formAction: "to_020",
                'oid_arr': oid_arr
            },
            success: function(json){
         	   $gridview.trigger("reloadGrid");
           	    
         	   API.showMessage(i18n.def.runSuccess);       	
            }
         });
      	
    }).end().find("#btnDelete").click(function(){ //編製中-刪除
    	var rowId_arr = $gridview.getGridParam('selarrrow');
      	var oid_arr = [];
      	for (var i = 0; i < rowId_arr.length; i++) {
      		var data = $gridview.getRowData(rowId_arr[i]);
      		oid_arr.push(data.oid);
        }

      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
      	
      	$.ajax({
           type: "POST",
           handler: _handler,
           data: {
               formAction: "deleteC900S01I",
               'oid_arr': oid_arr
           },
           success: function(json){
        	   $gridview.trigger("reloadGrid");
          	    
        	   API.showMessage(i18n.def.runSuccess);       	
           }
        });
    }).end().find("#btnAllSend").click(function(){ //待覆核-核准
    	CommonAPI.confirmMessage("是否確定執行？", function(b){
            if (b) {
            	var rowId_arr = $gridview.getGridParam('selarrrow');
              	var oid_arr = [];
              	for (var i = 0; i < rowId_arr.length; i++) {
              		var data = $gridview.getRowData(rowId_arr[i]);
              		oid_arr.push(data.oid);
                }

              	if(oid_arr.length==0){
              		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
              		return;
              	}
              	
              	var isFail = false;
              	for(var i = 0; i < oid_arr.length; i++){
              		var oid = oid_arr[i];
              		ilog.debug("["+i+"]"+oid);
              		$.ajax({
                        type: "POST", async: false , handler: _handler,
                        data: {
                            formAction: "to_030",
                            'oid_arr': oid
                        },
                        success: function(responseData){
             	          
                        },
                        error: function(responseData){
                        	//break;
            	           	i = oid_arr.length;
            	           	
            	           	isFail = true;
                        }
                    });
              	}
              	
              	$gridview.trigger("reloadGrid");
              	if(isFail){
              		
              	}else{
              		API.showMessage(i18n.def.runSuccess);   
              	}
            }
        });
		
    }).end().find("#btnReturnToCompiling").click(function(){ //待覆核-退回
    	var rowId_arr = $gridview.getGridParam('selarrrow');
      	var oid_arr = [];
      	for (var i = 0; i < rowId_arr.length; i++) {
      		var data = $gridview.getRowData(rowId_arr[i]);
      		oid_arr.push(data.oid);
        }

      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
      	$.ajax({
            type: "POST",
            handler: _handler,
            data: {
                formAction: "backto_010",
                'oid_arr': oid_arr
            },
            success: function(json){
         	   $gridview.trigger("reloadGrid");
           	    
         	   API.showMessage(i18n.def.runSuccess);       	
            }
         });
    });
});
