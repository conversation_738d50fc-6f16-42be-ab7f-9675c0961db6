package com.mega.eloan.lms.dc.bean;

import java.io.Serializable;

import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

public class L140M01CBean extends BaseBean implements Serializable {

	private static final long serialVersionUID = -3566004707184692890L;

	private String loanTP = "";// 科目代碼
	private String subjSeq = "";// 科目順序
	private String subjDscr = "";// 科目補充說明
	private String lmtDays = "";// 清償期限－天數
	private String lmtOther = "";// 清償期限－詳其他敘作條件

	public String getLoanTP() {
		return loanTP;
	}

	public void setLoanTP(String loanTP) {
		this.loanTP = loanTP;
	}

	public String getSubjSeq() {
		return subjSeq;
	}

	public void setSubjSeq(String subjSeq) {
		this.subjSeq = subjSeq;
	}

	public String getSubjDscr() {
		return subjDscr;
	}

	public void setSubjDscr(String subjDscr) {
		this.subjDscr = subjDscr;
	}

	public String getLmtDays() {
		return lmtDays;
	}

	public void setLmtDays(String lmtDays) {
		this.lmtDays = lmtDays;
	}

	public String getLmtOther() {
		return lmtOther;
	}

	public void setLmtOther(String lmtOther) {
		this.lmtOther = lmtOther;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer();
		sb.append(Util.nullToSpace(this.getOid()))
				.append(TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getMainId()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getLoanTP()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getSubjSeq()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getSubjDscr()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getLmtDays()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getLmtOther()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(this.getCreator()).append(TextDefine.FILE_DELIM);
		sb.append(this.getCreateTime()).append(TextDefine.FILE_DELIM);
		sb.append(this.getUpdater()).append(TextDefine.FILE_DELIM);
		sb.append(this.getUpdateTime());
		return sb.toString();
	}

}
