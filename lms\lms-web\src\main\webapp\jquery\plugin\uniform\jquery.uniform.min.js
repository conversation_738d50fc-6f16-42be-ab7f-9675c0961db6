(function(a){a.uniform={options:{selectClass:"selector",radioClass:"radio",checkboxClass:"checker",fileClass:"uploader",filenameClass:"filename",fileBtnClass:"action",fileDefaultText:"No file selected",fileBtnText:"Choose File",checkedClass:"checked",focusClass:"focus",disabledClass:"disabled",buttonClass:"button",activeClass:"active",hoverClass:"hover",useID:true,idPrefix:"uniform",resetSelector:false},elements:[]};if(a.browser.msie&&a.browser.version<7){a.support.selectOpacity=false}else{a.support.selectOpacity=true}a.fn.uniform=function(k){k=a.extend(a.uniform.options,k);var d=this;if(k.resetSelector!=false){a(k.resetSelector).mouseup(function(){function l(){a.uniform.update(d)}setTimeout(l,10)})}function j(l){$el=a(l);$el.addClass($el.attr("type"));b(l)}function g(l){a(l).addClass("uniform");b(l)}function i(n){$el=n;var o=a("<div>"),l=a("<span>");o.addClass(k.buttonClass);if(k.useID&&$el.attr("id")!=""){o.attr("id",k.idPrefix+"-"+$el.attr("id"))}var m;if($el.is("a")){m=$el.text()}else{if($el.is("button")){m=$el.text()}else{if($el.is(":submit")||$el.is("input[type=button]")){m=$el.attr("value")}}}if(m==""){m="Submit"}l.html(m);$el.hide();$el.wrap(o);$el.wrap(l);o=$el.closest("div");l=$el.closest("span");if($el.is(":disabled")){o.addClass(k.disabledClass)}o.bind({"mouseenter.uniform":function(){o.addClass(k.hoverClass)},"mouseleave.uniform":function(){o.removeClass(k.hoverClass)},"mousedown.uniform touchbegin.uniform":function(){o.addClass(k.activeClass)},"mouseup.uniform touchend.uniform":function(){o.removeClass(k.activeClass)},"click.uniform touchend.uniform":function(q){if(a(q.target).is("span")||a(q.target).is("div")){if(n[0].dispatchEvent){var p=document.createEvent("MouseEvents");p.initEvent("click",true,true);n[0].dispatchEvent(p)}else{n[0].click()}}}});n.bind({"focus.uniform":function(){o.addClass(k.focusClass)},"blur.uniform":function(){o.removeClass(k.focusClass)}});a.uniform.noSelect(o);b(n)}function e(n){var o=a("<div />"),l=a("<span />");o.addClass(k.selectClass);if(k.useID&&n.attr("id")!=""){o.attr("id",k.idPrefix+"-"+n.attr("id"))}var m=n.find(":selected:first");if(m.length==0){m=n.find("option:first")}l.html(m.text());n.css("opacity",0);n.wrap(o);n.before(l);o=n.parent("div");l=n.siblings("span");n.bind({"change.uniform":function(){l.text(n.find(":selected").text());o.removeClass(k.activeClass)},"focus.uniform":function(){o.addClass(k.focusClass)},"blur.uniform":function(){o.removeClass(k.focusClass);o.removeClass(k.activeClass)},"mousedown.uniform touchbegin.uniform":function(){o.addClass(k.activeClass)},"mouseup.uniform touchend.uniform":function(){o.removeClass(k.activeClass)},"click.uniform touchend.uniform":function(){o.removeClass(k.activeClass)},"mouseenter.uniform":function(){o.addClass(k.hoverClass)},"mouseleave.uniform":function(){o.removeClass(k.hoverClass)},"keyup.uniform":function(){l.text(n.find(":selected").text())}});if(a(n).attr("disabled")){o.addClass(k.disabledClass)}a.uniform.noSelect(l);b(n)}function f(m){var n=a("<div />"),l=a("<span />");n.addClass(k.checkboxClass);if(k.useID&&m.attr("id")!=""){n.attr("id",k.idPrefix+"-"+m.attr("id"))}a(m).wrap(n);a(m).wrap(l);l=m.parent();n=l.parent();a(m).css("opacity",0).bind({"focus.uniform":function(){n.addClass(k.focusClass)},"blur.uniform":function(){n.removeClass(k.focusClass)},"click.uniform touchend.uniform":function(){if(!a(m).attr("checked")){l.removeClass(k.checkedClass)}else{l.addClass(k.checkedClass)}},"mousedown.uniform touchbegin.uniform":function(){n.addClass(k.activeClass)},"mouseup.uniform touchend.uniform":function(){n.removeClass(k.activeClass)},"mouseenter.uniform":function(){n.addClass(k.hoverClass)},"mouseleave.uniform":function(){n.removeClass(k.hoverClass)}});if(a(m).attr("checked")){l.addClass(k.checkedClass)}if(a(m).attr("disabled")){n.addClass(k.disabledClass)}b(m)}function c(m){var n=a("<div />"),l=a("<span />");n.addClass(k.radioClass);if(k.useID&&m.attr("id")!=""){n.attr("id",k.idPrefix+"-"+m.attr("id"))}a(m).wrap(n);a(m).wrap(l);l=m.parent();n=l.parent();a(m).css("opacity",0).bind({"focus.uniform":function(){n.addClass(k.focusClass)},"blur.uniform":function(){n.removeClass(k.focusClass)},"click.uniform touchend.uniform":function(){if(!a(m).attr("checked")){l.removeClass(k.checkedClass)}else{a("."+k.radioClass+" span."+k.checkedClass+":has([name='"+a(m).attr("name")+"'])").removeClass(k.checkedClass);l.addClass(k.checkedClass)}},"mousedown.uniform touchend.uniform":function(){if(!a(m).is(":disabled")){n.addClass(k.activeClass)}},"mouseup.uniform touchbegin.uniform":function(){n.removeClass(k.activeClass)},"mouseenter.uniform touchend.uniform":function(){n.addClass(k.hoverClass)},"mouseleave.uniform":function(){n.removeClass(k.hoverClass)}});if(a(m).attr("checked")){l.addClass(k.checkedClass)}if(a(m).attr("disabled")){n.addClass(k.disabledClass)}b(m)}function h(q){var o=a(q);var r=a("<div />"),p=a("<span>"+k.fileDefaultText+"</span>"),m=a("<span>"+k.fileBtnText+"</span>");r.addClass(k.fileClass);p.addClass(k.filenameClass);m.addClass(k.fileBtnClass);if(k.useID&&o.attr("id")!=""){r.attr("id",k.idPrefix+"-"+o.attr("id"))}o.wrap(r);o.after(m);o.after(p);r=o.closest("div");p=o.siblings("."+k.filenameClass);m=o.siblings("."+k.fileBtnClass);if(!o.attr("size")){var l=r.width();o.attr("size",l/10)}var n=function(){var s=o.val();if(s===""){s=k.fileDefaultText}else{s=s.split(/[\/\\]+/);s=s[(s.length-1)]}p.text(s)};n();o.css("opacity",0).bind({"focus.uniform":function(){r.addClass(k.focusClass)},"blur.uniform":function(){r.removeClass(k.focusClass)},"mousedown.uniform":function(){if(!a(q).is(":disabled")){r.addClass(k.activeClass)}},"mouseup.uniform":function(){r.removeClass(k.activeClass)},"mouseenter.uniform":function(){r.addClass(k.hoverClass)},"mouseleave.uniform":function(){r.removeClass(k.hoverClass)}});if(a.browser.msie){o.bind("click.uniform.ie7",function(){setTimeout(n,0)})}else{o.bind("change.uniform",n)}if(o.attr("disabled")){r.addClass(k.disabledClass)}a.uniform.noSelect(p);a.uniform.noSelect(m);b(q)}a.uniform.restore=function(l){if(l==undefined){l=a(a.uniform.elements)}a(l).each(function(){if(a(this).is(":checkbox")){a(this).unwrap().unwrap()}else{if(a(this).is("select")){a(this).siblings("span").remove();a(this).unwrap()}else{if(a(this).is(":radio")){a(this).unwrap().unwrap()}else{if(a(this).is(":file")){a(this).siblings("span").remove();a(this).unwrap()}else{if(a(this).is("button, :submit, a, input[type='button']")){a(this).unwrap().unwrap()}}}}}a(this).unbind(".uniform");a(this).css("opacity","1");var m=a.inArray(a(l),a.uniform.elements);a.uniform.elements.splice(m,1)})};function b(l){l=a(l).get();if(l.length>1){a.each(l,function(m,n){a.uniform.elements.push(n)})}else{a.uniform.elements.push(l)}}a.uniform.noSelect=function(l){function m(){return false}a(l).each(function(){this.onselectstart=this.ondragstart=m;a(this).mousedown(m).css({MozUserSelect:"none"})})};a.uniform.update=function(l){if(l==undefined){l=a(a.uniform.elements)}l=a(l);l.each(function(){var n=a(this);if(n.is("select")){var m=n.siblings("span");var p=n.parent("div");p.removeClass(k.hoverClass+" "+k.focusClass+" "+k.activeClass);m.html(n.find(":selected").text());if(n.is(":disabled")){p.addClass(k.disabledClass)}else{p.removeClass(k.disabledClass)}}else{if(n.is(":checkbox")){var m=n.closest("span");var p=n.closest("div");p.removeClass(k.hoverClass+" "+k.focusClass+" "+k.activeClass);m.removeClass(k.checkedClass);if(n.is(":checked")){m.addClass(k.checkedClass)}if(n.is(":disabled")){p.addClass(k.disabledClass)}else{p.removeClass(k.disabledClass)}}else{if(n.is(":radio")){var m=n.closest("span");var p=n.closest("div");p.removeClass(k.hoverClass+" "+k.focusClass+" "+k.activeClass);m.removeClass(k.checkedClass);if(n.is(":checked")){m.addClass(k.checkedClass)}if(n.is(":disabled")){p.addClass(k.disabledClass)}else{p.removeClass(k.disabledClass)}}else{if(n.is(":file")){var p=n.parent("div");var o=n.siblings(k.filenameClass);btnTag=n.siblings(k.fileBtnClass);p.removeClass(k.hoverClass+" "+k.focusClass+" "+k.activeClass);o.text(n.val());if(n.is(":disabled")){p.addClass(k.disabledClass)}else{p.removeClass(k.disabledClass)}}else{if(n.is(":submit")||n.is("button")||n.is("a")||l.is("input[type=button]")){var p=n.closest("div");p.removeClass(k.hoverClass+" "+k.focusClass+" "+k.activeClass);if(n.is(":disabled")){p.addClass(k.disabledClass)}else{p.removeClass(k.disabledClass)}}}}}}})};return this.each(function(){if(a.support.selectOpacity){var l=a(this);if(l.is("select")){if(l.attr("multiple")!=true){if(l.attr("size")==undefined||l.attr("size")<=1){e(l)}}}else{if(l.is(":checkbox")){f(l)}else{if(l.is(":radio")){c(l)}else{if(l.is(":file")){h(l)}else{if(l.is(":text, :password, input[type='email']")){j(l)}else{if(l.is("textarea")){g(l)}else{if(l.is("a")||l.is(":submit")||l.is("button")||l.is("input[type=button]")){i(l)}}}}}}}}})}})(jQuery);