/* 
 * L999S01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 綜合授信契約書借款種類檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L999S01B", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "itemType" }))
public class L999S01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 借款種類
	 * <p/>
	 * A.購料借款<br/>
	 * B.外銷借款<br/>
	 * C.營運週轉借款<br/>
	 * D.貼現<br/>
	 * E.透支<br/>
	 * F.委任票據保證<br/>
	 * G.委任票據承兌<br/>
	 * H.委任保證
	 */
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String itemType;

	/**
	 * 透支支票存款帳號
	 * <p/>
	 * 透支_借款用途(支票存款帳戶)
	 */
	@Column(name = "ITEM01", length = 20, columnDefinition = "VARCHAR(20)")
	private String item01;

	/**
	 * 是否為循環額度
	 * <p/>
	 * ※Y.空白, N.非<br/>
	 * 購料借款_借款額度是否循環<br/>
	 * 外銷借款_借款額度是否循環<br/>
	 * 營運週轉借款_借款額度是否循環<br/>
	 * 貼現_借款額度是否循環<br/>
	 * 透支_借款額度是否循環<br/>
	 * 票據保證_額度是否循環<br/>
	 * 票據承兌_額度是否循環<br/>
	 * 委任保證_額度是否循環
	 */
	@Column(name = "ITEM02", length = 1, columnDefinition = "CHAR(1)")
	private String item02;

	/**
	 * 借款額度幣別
	 */
	@Column(name = "ITEM03CURR", columnDefinition = "CHAR(3)")
	private String item03Curr;

	/**
	 * 借款額度
	 * <p/>
	 * 購料借款_借款額度<br/>
	 * 外銷借款_借款額度<br/>
	 * 營運週轉借款_借款額度<br/>
	 * 貼現_借款額度<br/>
	 * 透支_借款額度<br/>
	 * 票據保證_額度<br/>
	 * 票據承兌_額度<br/>
	 * 委任保證_額度
	 */
	@Column(name = "ITEM03", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal item03;

	/**
	 * 借款額度單位
	 */
	@Column(name = "ITEM03UNIT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal item03Unit;

	/**
	 * 利息計付/手續費計付
	 * <p/>
	 * 購料借款_利息計付(一)計息標準(新台幣)<br/>
	 * 外銷借款_利息計付<br/>
	 * 營運週轉借款_利息計付<br/>
	 * 貼現_利息計付<br/>
	 * 透支_利息計付<br/>
	 * 票據保證_手續費計付<br/>
	 * 票據承兌_手續費計付<br/>
	 * 委任保證_手續費計付
	 */
	@Column(name = "ITEM04", length = 768, columnDefinition = "VARCHAR(768)")
	private String item04;

	/**
	 * 清償期限/保證期限/承兌期限(天數)
	 * <p/>
	 * 購料借款_清償期限(一)(天數)<br/>
	 * 貼現_清償期限(天數)<br/>
	 * 票據保證_保證期限(天數)<br/>
	 * 票據承兌_承兌期限(天數)
	 */
	@Column(name = "ITEM05", columnDefinition = "DECIMAL(3,0)")
	private Integer item05;

	/**
	 * 清償期限(天數)
	 * <p/>
	 * 購料借款_清償期限(二)天數1
	 */
	@Column(name = "ITEM06", columnDefinition = "DECIMAL(3,0)")
	private Integer item06;

	/**
	 * 清償期限(天數)
	 * <p/>
	 * 購料借款_清償期限(二)天數2
	 */
	@Column(name = "ITEM07", columnDefinition = "DECIMAL(3,0)")
	private Integer item07;

	/**
	 * 清償期限(天數)
	 * <p/>
	 * 購料借款_清償期限(三)天數
	 */
	@Column(name = "ITEM08", columnDefinition = "DECIMAL(3,0)")
	private Integer item08;

	/**
	 * 動用之方式及條件(成數)
	 * <p/>
	 * 購料借款_動用之方式及條件(二)成數
	 */
	@Column(name = "ITEM09", columnDefinition = "DECIMAL(3,0)")
	private Integer item09;

	/**
	 * 動用之方式及條件(天數)
	 * <p/>
	 * 購料借款_動用之方式及條件(二)天數
	 */
	@Column(name = "ITEM10", columnDefinition = "DECIMAL(3,0)")
	private Integer item10;

	/**
	 * 利息計付(計息標準)/清償期限/委任保證範圍
	 * <p/>
	 * 購料借款_利息計付(一)計息標準(美金)<br/>
	 * 營運週轉借款_清償期限<br/>
	 * 透支_清償期限<br/>
	 * 委任保證_委任保證範圍
	 */
	@Column(name = "ITEM11", length = 768, columnDefinition = "VARCHAR(768)")
	private String item11;

	/**
	 * 利息計付(計息標準)/動用之方式及條件/委任保證方式
	 * <p/>
	 * 購料借款_利息計付(一)計息標準(其他外幣)<br/>
	 * 營運週轉借款_動用之方式及條件<br/>
	 * 委任保證_委任保證方式
	 */
	@Column(name = "ITEM12", length = 768, columnDefinition = "VARCHAR(768)")
	private String item12;

	/**
	 * 利息計付(繳息方式)
	 * <p/>
	 * 購料借款_利息計付(二)繳息方式
	 */
	@Column(name = "ITEM13", length = 768, columnDefinition = "VARCHAR(768)")
	private String item13;

	/**
	 * 利息計付(匯票承兌)
	 * <p/>
	 * 購料借款_利息計付(四)匯票承兌
	 */
	@Column(name = "ITEM14", length = 768, columnDefinition = "VARCHAR(768)")
	private String item14;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得借款種類
	 * <p/>
	 * A.購料借款<br/>
	 * B.外銷借款<br/>
	 * C.營運週轉借款<br/>
	 * D.貼現<br/>
	 * E.透支<br/>
	 * F.委任票據保證<br/>
	 * G.委任票據承兌<br/>
	 * H.委任保證
	 */
	public String getItemType() {
		return this.itemType;
	}

	/**
	 * 設定借款種類
	 * <p/>
	 * A.購料借款<br/>
	 * B.外銷借款<br/>
	 * C.營運週轉借款<br/>
	 * D.貼現<br/>
	 * E.透支<br/>
	 * F.委任票據保證<br/>
	 * G.委任票據承兌<br/>
	 * H.委任保證
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/**
	 * 取得透支支票存款帳號
	 * <p/>
	 * 透支_借款用途(支票存款帳戶)
	 */
	public String getItem01() {
		return this.item01;
	}

	/**
	 * 設定透支支票存款帳號
	 * <p/>
	 * 透支_借款用途(支票存款帳戶)
	 **/
	public void setItem01(String value) {
		this.item01 = value;
	}

	/**
	 * 取得是否為循環額度
	 * <p/>
	 * ※Y.空白, N.非<br/>
	 * 購料借款_借款額度是否循環<br/>
	 * 外銷借款_借款額度是否循環<br/>
	 * 營運週轉借款_借款額度是否循環<br/>
	 * 貼現_借款額度是否循環<br/>
	 * 透支_借款額度是否循環<br/>
	 * 票據保證_額度是否循環<br/>
	 * 票據承兌_額度是否循環<br/>
	 * 委任保證_額度是否循環
	 */
	public String getItem02() {
		return this.item02;
	}

	/**
	 * 設定是否為循環額度
	 * <p/>
	 * ※Y.空白, N.非<br/>
	 * 購料借款_借款額度是否循環<br/>
	 * 外銷借款_借款額度是否循環<br/>
	 * 營運週轉借款_借款額度是否循環<br/>
	 * 貼現_借款額度是否循環<br/>
	 * 透支_借款額度是否循環<br/>
	 * 票據保證_額度是否循環<br/>
	 * 票據承兌_額度是否循環<br/>
	 * 委任保證_額度是否循環
	 **/
	public void setItem02(String value) {
		this.item02 = value;
	}

	/**
	 * 取得借款額度
	 * <p/>
	 * 購料借款_借款額度<br/>
	 * 外銷借款_借款額度<br/>
	 * 營運週轉借款_借款額度<br/>
	 * 貼現_借款額度<br/>
	 * 透支_借款額度<br/>
	 * 票據保證_額度<br/>
	 * 票據承兌_額度<br/>
	 * 委任保證_額度
	 */
	public BigDecimal getItem03() {
		return this.item03;
	}

	/**
	 * 設定借款額度
	 * <p/>
	 * 購料借款_借款額度<br/>
	 * 外銷借款_借款額度<br/>
	 * 營運週轉借款_借款額度<br/>
	 * 貼現_借款額度<br/>
	 * 透支_借款額度<br/>
	 * 票據保證_額度<br/>
	 * 票據承兌_額度<br/>
	 * 委任保證_額度
	 **/
	public void setItem03(BigDecimal value) {
		this.item03 = value;
	}

	/**
	 * 取得利息計付/手續費計付
	 * <p/>
	 * 購料借款_利息計付(一)計息標準(新台幣)<br/>
	 * 外銷借款_利息計付<br/>
	 * 營運週轉借款_利息計付<br/>
	 * 貼現_利息計付<br/>
	 * 透支_利息計付<br/>
	 * 票據保證_手續費計付<br/>
	 * 票據承兌_手續費計付<br/>
	 * 委任保證_手續費計付
	 */
	public String getItem04() {
		return this.item04;
	}

	/**
	 * 設定利息計付/手續費計付
	 * <p/>
	 * 購料借款_利息計付(一)計息標準(新台幣)<br/>
	 * 外銷借款_利息計付<br/>
	 * 營運週轉借款_利息計付<br/>
	 * 貼現_利息計付<br/>
	 * 透支_利息計付<br/>
	 * 票據保證_手續費計付<br/>
	 * 票據承兌_手續費計付<br/>
	 * 委任保證_手續費計付
	 **/
	public void setItem04(String value) {
		this.item04 = value;
	}

	/**
	 * 取得清償期限/保證期限/承兌期限(天數)
	 * <p/>
	 * 購料借款_清償期限(一)(天數)<br/>
	 * 貼現_清償期限(天數)<br/>
	 * 票據保證_保證期限(天數)<br/>
	 * 票據承兌_承兌期限(天數)
	 */
	public Integer getItem05() {
		return this.item05;
	}

	/**
	 * 設定清償期限/保證期限/承兌期限(天數)
	 * <p/>
	 * 購料借款_清償期限(一)(天數)<br/>
	 * 貼現_清償期限(天數)<br/>
	 * 票據保證_保證期限(天數)<br/>
	 * 票據承兌_承兌期限(天數)
	 **/
	public void setItem05(Integer value) {
		this.item05 = value;
	}

	/**
	 * 取得清償期限(天數)
	 * <p/>
	 * 購料借款_清償期限(二)天數1
	 */
	public Integer getItem06() {
		return this.item06;
	}

	/**
	 * 設定清償期限(天數)
	 * <p/>
	 * 購料借款_清償期限(二)天數1
	 **/
	public void setItem06(Integer value) {
		this.item06 = value;
	}

	/**
	 * 取得清償期限(天數)
	 * <p/>
	 * 購料借款_清償期限(二)天數2
	 */
	public Integer getItem07() {
		return this.item07;
	}

	/**
	 * 設定清償期限(天數)
	 * <p/>
	 * 購料借款_清償期限(二)天數2
	 **/
	public void setItem07(Integer value) {
		this.item07 = value;
	}

	/**
	 * 取得清償期限(天數)
	 * <p/>
	 * 購料借款_清償期限(三)天數
	 */
	public Integer getItem08() {
		return this.item08;
	}

	/**
	 * 設定清償期限(天數)
	 * <p/>
	 * 購料借款_清償期限(三)天數
	 **/
	public void setItem08(Integer value) {
		this.item08 = value;
	}

	/**
	 * 取得動用之方式及條件(成數)
	 * <p/>
	 * 購料借款_動用之方式及條件(二)成數
	 */
	public Integer getItem09() {
		return this.item09;
	}

	/**
	 * 設定動用之方式及條件(成數)
	 * <p/>
	 * 購料借款_動用之方式及條件(二)成數
	 **/
	public void setItem09(Integer value) {
		this.item09 = value;
	}

	/**
	 * 取得動用之方式及條件(天數)
	 * <p/>
	 * 購料借款_動用之方式及條件(二)天數
	 */
	public Integer getItem10() {
		return this.item10;
	}

	/**
	 * 設定動用之方式及條件(天數)
	 * <p/>
	 * 購料借款_動用之方式及條件(二)天數
	 **/
	public void setItem10(Integer value) {
		this.item10 = value;
	}

	/**
	 * 取得利息計付(計息標準)/清償期限/委任保證範圍
	 * <p/>
	 * 購料借款_利息計付(一)計息標準(美金)<br/>
	 * 營運週轉借款_清償期限<br/>
	 * 透支_清償期限<br/>
	 * 委任保證_委任保證範圍
	 */
	public String getItem11() {
		return this.item11;
	}

	/**
	 * 設定利息計付(計息標準)/清償期限/委任保證範圍
	 * <p/>
	 * 購料借款_利息計付(一)計息標準(美金)<br/>
	 * 營運週轉借款_清償期限<br/>
	 * 透支_清償期限<br/>
	 * 委任保證_委任保證範圍
	 **/
	public void setItem11(String value) {
		this.item11 = value;
	}

	/**
	 * 取得利息計付(計息標準)/動用之方式及條件/委任保證方式
	 * <p/>
	 * 購料借款_利息計付(一)計息標準(其他外幣)<br/>
	 * 營運週轉借款_動用之方式及條件<br/>
	 * 委任保證_委任保證方式
	 */
	public String getItem12() {
		return this.item12;
	}

	/**
	 * 設定利息計付(計息標準)/動用之方式及條件/委任保證方式
	 * <p/>
	 * 購料借款_利息計付(一)計息標準(其他外幣)<br/>
	 * 營運週轉借款_動用之方式及條件<br/>
	 * 委任保證_委任保證方式
	 **/
	public void setItem12(String value) {
		this.item12 = value;
	}

	/**
	 * 取得利息計付(繳息方式)
	 * <p/>
	 * 購料借款_利息計付(二)繳息方式
	 */
	public String getItem13() {
		return this.item13;
	}

	/**
	 * 設定利息計付(繳息方式)
	 * <p/>
	 * 購料借款_利息計付(二)繳息方式
	 **/
	public void setItem13(String value) {
		this.item13 = value;
	}

	/**
	 * 取得利息計付(匯票承兌)
	 * <p/>
	 * 購料借款_利息計付(四)匯票承兌
	 */
	public String getItem14() {
		return this.item14;
	}

	/**
	 * 設定利息計付(匯票承兌)
	 * <p/>
	 * 購料借款_利息計付(四)匯票承兌
	 **/
	public void setItem14(String value) {
		this.item14 = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/**
	 * @param item03Unit
	 *            the item03Unit to set
	 */
	public void setItem03Unit(BigDecimal item03Unit) {
		this.item03Unit = item03Unit;
	}

	/**
	 * @return the item03Unit
	 */
	public BigDecimal getItem03Unit() {
		return item03Unit;
	}

	/**
	 * @param item03Curr
	 *            the item03Curr to set
	 */
	public void setItem03Curr(String item03Curr) {
		this.item03Curr = item03Curr;
	}

	/**
	 * @return the item03Curr
	 */
	public String getItem03Curr() {
		return item03Curr;
	}

	/**
	 * 借款額度文字描述
	 * <p/>
	 * ※2013/07/24,Rex,新增借款額度描述 <br/>
	 * 512個全型字
	 */
	@Column(name = "ITEM15", length = 1536, columnDefinition = "VARCHAR(1536)")
	private String item15;

	/**
	 * 借款額度文字描述
	 * <p/>
	 * ※2013/07/24,Rex,新增借款額度描述 <br/>
	 * 512個全型字
	 */
	public String getItem15() {
		return item15;
	}

	/**
	 * 借款額度文字描述
	 * <p/>
	 * ※2013/07/24,Rex,新增借款額度描述 <br/>
	 * 512個全型字
	 */
	public void setItem15(String item15) {
		this.item15 = item15;
	}

}
