/* 
 * L120M01L.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 簽報書各業務審核層級記錄檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120M01L", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120M01L extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 身分證統編<p/>
	 * L120M01A.MAINID
	 */
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 借款人姓名 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 
	 * 業務別<p/>
	 * 1: 授信<br/>
	 *  2: 應收帳款(無追)<br/>
	 *  3: 供應鏈融資<br/>
	 *  4: 出口押匯<br/>
	 *  5: 開發即期信用狀<br/>
	 *  6: 買入光票
	 */
	@Size(max=2)
	@Column(name="LOANKIND", length=2, columnDefinition="VARCHAR(2)")
	private String loanKind;

	/** 
	 * PD分組<p/>
	 * 內部/外部風險評等等級
	 */
	@Size(max=10)
	@Column(name="PDGROUP", length=10, columnDefinition="VARCHAR(10)")
	private String pdGroup;

	/** 
	 * 業務合計授權額度_本人<p/>
	 * 該業務只算本人的額度合計
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="SINGLESELFTOTAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal singleSelfTotAmt;

	/** 
	 * 業務合計授權額度_含合併關係企業<p/>
	 * 該業務本人+合併關係企業的額度合計
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="SINGLERELTOTAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal singleRelTotAmt;

	/** 
	 * 業務授權層級<p/>
	 * 參考lms1205m01_caseLvl<br/>
	 *  1.董事會/常務董事會<br/>
	 *  6.總經理<br/>
	 *  7.副總經理<br/>
	 *  8.授信審查處處長<br/>
	 *  B.區域營運中心營運長<br/>
	 *  最大的董事會那筆應該不用建
	 */
	@Size(max=2)
	@Column(name="SINGLECASELVL", length=2, columnDefinition="VARCHAR(2)")
	private String singleCaseLvl;

	/** 
	 * 合併業務合計授權額度_本人<p/>
	 * 全授信業務只算本人的額度合計
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="ALLSELFTOTAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal allSelfTotAmt;

	/** 
	 * 合併業務合計授權額度_含合併關係企業<p/>
	 * 全授信業務本人+合併關係企業的額度合計
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="ALLRELTOTAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal allRelTotAmt;

	/** 合併業務授權層級 **/
	@Size(max=2)
	@Column(name="ALLCASELVL", length=2, columnDefinition="VARCHAR(2)")
	private String allCaseLvl;

	/** 最終授權層級 **/
	@Size(max=2)
	@Column(name="FINALCASELVL", length=2, columnDefinition="VARCHAR(2)")
	private String finalCaseLvl;

	/** 
	 * 細項1<p/>
	 * 1~12皆為存放細項金額用<br/>
	 *  看不同的各業務需要放什麼欄位各自表述<br/>
	 *  授信-自己的LGD_1
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DETAIL1", columnDefinition="DECIMAL(17,2)")
	private BigDecimal detail1;

	/** 
	 * 細項2<p/>
	 * 授信-合併的LGD_1
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DETAIL2", columnDefinition="DECIMAL(17,2)")
	private BigDecimal detail2;

	/** 
	 * 細項3<p/>
	 * 授信-自己的LGD_2
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DETAIL3", columnDefinition="DECIMAL(17,2)")
	private BigDecimal detail3;

	/** 
	 * 細項4<p/>
	 * 授信-合併的LGD_2
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DETAIL4", columnDefinition="DECIMAL(17,2)")
	private BigDecimal detail4;

	/** 
	 * 細項5<p/>
	 * 授信-自己的LGD_3
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DETAIL5", columnDefinition="DECIMAL(17,2)")
	private BigDecimal detail5;

	/** 
	 * 細項6<p/>
	 * 授信-合併的LGD_3
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DETAIL6", columnDefinition="DECIMAL(17,2)")
	private BigDecimal detail6;

	/** 
	 * 細項7<p/>
	 * 授信-自己的LGD_4
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DETAIL7", columnDefinition="DECIMAL(17,2)")
	private BigDecimal detail7;

	/** 
	 * 細項8<p/>
	 * 授信-合併的LGD_4
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DETAIL8", columnDefinition="DECIMAL(17,2)")
	private BigDecimal detail8;

	/** 
	 * 細項9<p/>
	 * 授信-自己的LGD_5
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DETAIL9", columnDefinition="DECIMAL(17,2)")
	private BigDecimal detail9;

	/** 
	 * 細項10<p/>
	 * 授信-合併的LGD_5
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DETAIL10", columnDefinition="DECIMAL(17,2)")
	private BigDecimal detail10;

	/** 
	 * 細項11<p/>
	 * 授信-自己的LGD_6
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DETAIL11", columnDefinition="DECIMAL(17,2)")
	private BigDecimal detail11;

	/** 
	 * 細項12<p/>
	 * 授信-合併的LGD_6
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DETAIL12", columnDefinition="DECIMAL(17,2)")
	private BigDecimal detail12;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 備註資訊 **/
	@Size(max=1000)
	@Column(name="INFO", length=2, columnDefinition="VARCHAR(1000)")
	private String info;
	
	/** 全案借款人合併資料註記 **/
	@Size(max=2)
	@Column(name="ISFULLCASEFLAG", length=2, columnDefinition="VARCHAR(2)")
	private String isFullCaseFlag;
	
	/** 業務合計授權額度_本人_授權層級 **/
	@Size(max=2)
	@Column(name="SELFTOTAMTCASELVL", length=2, columnDefinition="VARCHAR(2)")
	private String selfTotAmtCaseLvl;
	
	/** 業務合計授權額度_含合併關係企業_授權層級 **/
	@Size(max=2)
	@Column(name="RELTOTAMTCASELVL", length=2, columnDefinition="VARCHAR(2)")
	private String relTotAmtCaseLvl;
	
	/** 細項1_授權層級 **/
	@Size(max=2)
	@Column(name="DETAILCASELVL1", length=2, columnDefinition="VARCHAR(2)")
	private String detailCaseLvl1;
	
	/** 細項2_授權層級 **/
	@Size(max=2)
	@Column(name="DETAILCASELVL2", length=2, columnDefinition="VARCHAR(2)")
	private String detailCaseLvl2;
	
	/** 細項3_授權層級 **/
	@Size(max=2)
	@Column(name="DETAILCASELVL3", length=2, columnDefinition="VARCHAR(2)")
	private String detailCaseLvl3;
	
	/** 細項4_授權層級 **/
	@Size(max=2)
	@Column(name="DETAILCASELVL4", length=2, columnDefinition="VARCHAR(2)")
	private String detailCaseLvl4;
	
	/** 細項5_授權層級 **/
	@Size(max=2)
	@Column(name="DETAILCASELVL5", length=2, columnDefinition="VARCHAR(2)")
	private String detailCaseLvl5;
	
	/** 細項6_授權層級 **/
	@Size(max=2)
	@Column(name="DETAILCASELVL6", length=2, columnDefinition="VARCHAR(2)")
	private String detailCaseLvl6;
	
	/** 細項7_授權層級 **/
	@Size(max=2)
	@Column(name="DETAILCASELVL7", length=2, columnDefinition="VARCHAR(2)")
	private String detailCaseLvl7;
	
	/** 細項8_授權層級 **/
	@Size(max=2)
	@Column(name="DETAILCASELVL8", length=2, columnDefinition="VARCHAR(2)")
	private String detailCaseLvl8;
	
	/** 細項9_授權層級 **/
	@Size(max=2)
	@Column(name="DETAILCASELVL9", length=2, columnDefinition="VARCHAR(2)")
	private String detailCaseLvl9;
	
	/** 細項10_授權層級 **/
	@Size(max=2)
	@Column(name="DETAILCASELVL10", length=2, columnDefinition="VARCHAR(2)")
	private String detailCaseLvl10;
	
	/** 細項11_授權層級 **/
	@Size(max=2)
	@Column(name="DETAILCASELVL11", length=2, columnDefinition="VARCHAR(2)")
	private String detailCaseLvl11;
	
	/** 細項12_授權層級 **/
	@Size(max=2)
	@Column(name="DETAILCASELVL12", length=2, columnDefinition="VARCHAR(2)")
	private String detailCaseLvl12;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得身分證統編<p/>
	 * L120M01A.MAINID
	 */
	public String getCustId() {
		return this.custId;
	}
	/**
	 *  設定身分證統編<p/>
	 *  L120M01A.MAINID
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得借款人姓名 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定借款人姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 
	 * 取得業務別<p/>
	 * 1: 授信<br/>
	 *  2: 應收帳款(無追)<br/>
	 *  3: 供應鏈融資<br/>
	 *  4: 出口押匯<br/>
	 *  5: 開發即期信用狀<br/>
	 *  6: 買入光票
	 */
	public String getLoanKind() {
		return this.loanKind;
	}
	/**
	 *  設定業務別<p/>
	 *  1: 授信<br/>
	 *  2: 應收帳款(無追)<br/>
	 *  3: 供應鏈融資<br/>
	 *  4: 出口押匯<br/>
	 *  5: 開發即期信用狀<br/>
	 *  6: 買入光票
	 **/
	public void setLoanKind(String value) {
		this.loanKind = value;
	}

	/** 
	 * 取得PD分組<p/>
	 * 內部/外部風險評等等級
	 */
	public String getPdGroup() {
		return this.pdGroup;
	}
	/**
	 *  設定PD分組<p/>
	 *  內部/外部風險評等等級
	 **/
	public void setPdGroup(String value) {
		this.pdGroup = value;
	}

	/** 
	 * 取得業務合計授權額度_本人<p/>
	 * 該業務只算本人的額度合計
	 */
	public BigDecimal getSingleSelfTotAmt() {
		return this.singleSelfTotAmt;
	}
	/**
	 *  設定業務合計授權額度_本人<p/>
	 *  該業務只算本人的額度合計
	 **/
	public void setSingleSelfTotAmt(BigDecimal value) {
		this.singleSelfTotAmt = value;
	}

	/** 
	 * 取得業務合計授權額度_含合併關係企業<p/>
	 * 該業務本人+合併關係企業的額度合計
	 */
	public BigDecimal getSingleRelTotAmt() {
		return this.singleRelTotAmt;
	}
	/**
	 *  設定業務合計授權額度_含合併關係企業<p/>
	 *  該業務本人+合併關係企業的額度合計
	 **/
	public void setSingleRelTotAmt(BigDecimal value) {
		this.singleRelTotAmt = value;
	}

	/** 
	 * 取得業務授權層級<p/>
	 * 參考lms1205m01_caseLvl<br/>
	 *  1.董事會/常務董事會<br/>
	 *  6.總經理<br/>
	 *  7.副總經理<br/>
	 *  8.授信審查處處長<br/>
	 *  B.區域營運中心營運長<br/>
	 *  最大的董事會那筆應該不用建
	 */
	public String getSingleCaseLvl() {
		return this.singleCaseLvl;
	}
	/**
	 *  設定業務授權層級<p/>
	 *  參考lms1205m01_caseLvl<br/>
	 *  1.董事會/常務董事會<br/>
	 *  6.總經理<br/>
	 *  7.副總經理<br/>
	 *  8.授信審查處處長<br/>
	 *  B.區域營運中心營運長<br/>
	 *  最大的董事會那筆應該不用建
	 **/
	public void setSingleCaseLvl(String value) {
		this.singleCaseLvl = value;
	}

	/** 
	 * 取得合併業務合計授權額度_本人<p/>
	 * 全授信業務只算本人的額度合計
	 */
	public BigDecimal getAllSelfTotAmt() {
		return this.allSelfTotAmt;
	}
	/**
	 *  設定合併業務合計授權額度_本人<p/>
	 *  全授信業務只算本人的額度合計
	 **/
	public void setAllSelfTotAmt(BigDecimal value) {
		this.allSelfTotAmt = value;
	}

	/** 
	 * 取得合併業務合計授權額度_含合併關係企業<p/>
	 * 全授信業務本人+合併關係企業的額度合計
	 */
	public BigDecimal getAllRelTotAmt() {
		return this.allRelTotAmt;
	}
	/**
	 *  設定合併業務合計授權額度_含合併關係企業<p/>
	 *  全授信業務本人+合併關係企業的額度合計
	 **/
	public void setAllRelTotAmt(BigDecimal value) {
		this.allRelTotAmt = value;
	}

	/** 取得合併業務授權層級 **/
	public String getAllCaseLvl() {
		return this.allCaseLvl;
	}
	/** 設定合併業務授權層級 **/
	public void setAllCaseLvl(String value) {
		this.allCaseLvl = value;
	}

	/** 取得最終授權層級 **/
	public String getFinalCaseLvl() {
		return this.finalCaseLvl;
	}
	/** 設定最終授權層級 **/
	public void setFinalCaseLvl(String value) {
		this.finalCaseLvl = value;
	}

	/** 
	 * 取得細項1<p/>
	 * 1~12皆為存放細項金額用<br/>
	 *  看不同的各業務需要放什麼欄位各自表述<br/>
	 *  授信-自己的LGD_1
	 */
	public BigDecimal getDetail1() {
		return this.detail1;
	}
	/**
	 *  設定細項1<p/>
	 *  1~12皆為存放細項金額用<br/>
	 *  看不同的各業務需要放什麼欄位各自表述<br/>
	 *  授信-自己的LGD_1
	 **/
	public void setDetail1(BigDecimal value) {
		this.detail1 = value;
	}

	/** 
	 * 取得細項2<p/>
	 * 授信-合併的LGD_1
	 */
	public BigDecimal getDetail2() {
		return this.detail2;
	}
	/**
	 *  設定細項2<p/>
	 *  授信-合併的LGD_1
	 **/
	public void setDetail2(BigDecimal value) {
		this.detail2 = value;
	}

	/** 
	 * 取得細項3<p/>
	 * 授信-自己的LGD_2
	 */
	public BigDecimal getDetail3() {
		return this.detail3;
	}
	/**
	 *  設定細項3<p/>
	 *  授信-自己的LGD_2
	 **/
	public void setDetail3(BigDecimal value) {
		this.detail3 = value;
	}

	/** 
	 * 取得細項4<p/>
	 * 授信-合併的LGD_2
	 */
	public BigDecimal getDetail4() {
		return this.detail4;
	}
	/**
	 *  設定細項4<p/>
	 *  授信-合併的LGD_2
	 **/
	public void setDetail4(BigDecimal value) {
		this.detail4 = value;
	}

	/** 
	 * 取得細項5<p/>
	 * 授信-自己的LGD_3
	 */
	public BigDecimal getDetail5() {
		return this.detail5;
	}
	/**
	 *  設定細項5<p/>
	 *  授信-自己的LGD_3
	 **/
	public void setDetail5(BigDecimal value) {
		this.detail5 = value;
	}

	/** 
	 * 取得細項6<p/>
	 * 授信-合併的LGD_3
	 */
	public BigDecimal getDetail6() {
		return this.detail6;
	}
	/**
	 *  設定細項6<p/>
	 *  授信-合併的LGD_3
	 **/
	public void setDetail6(BigDecimal value) {
		this.detail6 = value;
	}

	/** 
	 * 取得細項7<p/>
	 * 授信-自己的LGD_4
	 */
	public BigDecimal getDetail7() {
		return this.detail7;
	}
	/**
	 *  設定細項7<p/>
	 *  授信-自己的LGD_4
	 **/
	public void setDetail7(BigDecimal value) {
		this.detail7 = value;
	}

	/** 
	 * 取得細項8<p/>
	 * 授信-合併的LGD_4
	 */
	public BigDecimal getDetail8() {
		return this.detail8;
	}
	/**
	 *  設定細項8<p/>
	 *  授信-合併的LGD_4
	 **/
	public void setDetail8(BigDecimal value) {
		this.detail8 = value;
	}

	/** 
	 * 取得細項9<p/>
	 * 授信-自己的LGD_5
	 */
	public BigDecimal getDetail9() {
		return this.detail9;
	}
	/**
	 *  設定細項9<p/>
	 *  授信-自己的LGD_5
	 **/
	public void setDetail9(BigDecimal value) {
		this.detail9 = value;
	}

	/** 
	 * 取得細項10<p/>
	 * 授信-合併的LGD_5
	 */
	public BigDecimal getDetail10() {
		return this.detail10;
	}
	/**
	 *  設定細項10<p/>
	 *  授信-合併的LGD_5
	 **/
	public void setDetail10(BigDecimal value) {
		this.detail10 = value;
	}

	/** 
	 * 取得細項11<p/>
	 * 授信-自己的LGD_6
	 */
	public BigDecimal getDetail11() {
		return this.detail11;
	}
	/**
	 *  設定細項11<p/>
	 *  授信-自己的LGD_6
	 **/
	public void setDetail11(BigDecimal value) {
		this.detail11 = value;
	}

	/** 
	 * 取得細項12<p/>
	 * 授信-合併的LGD_6
	 */
	public BigDecimal getDetail12() {
		return this.detail12;
	}
	/**
	 *  設定細項12<p/>
	 *  授信-合併的LGD_6
	 **/
	public void setDetail12(BigDecimal value) {
		this.detail12 = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	/** 取得備註資訊**/
	public String getInfo() {
		return this.info;
	}
	/** 設定備註資訊 **/
	public void setInfo(String value) {
		this.info = value;
	}
	
	/** 取得全案借款人合併資料註記**/
	public String getIsFullCaseFlag() {
		return this.isFullCaseFlag;
	}
	/** 設定全案借款人合併資料註記 **/
	public void setIsFullCaseFlag(String value) {
		this.isFullCaseFlag = value;
	}
	
	/** 取得業務合計授權額度_本人_授權層級**/
	public String getSelfTotAmtCaseLvl() {
		return this.selfTotAmtCaseLvl;
	}
	/** 設定業務合計授權額度_本人_授權層級 **/
	public void setSelfTotAmtCaseLvl(String value) {
		this.selfTotAmtCaseLvl = value;
	}
	
	/** 取得業務合計授權額度_含合併關係企業_授權層級**/
	public String getRelTotAmtCaseLvl() {
		return this.relTotAmtCaseLvl;
	}
	/** 設定業務合計授權額度_含合併關係企業_授權層級 **/
	public void setRelTotAmtCaseLvl(String value) {
		this.relTotAmtCaseLvl = value;
	}
	
	/** 取得細項1_授權層級**/
	public String getDetailCaseLvl1() {
		return this.detailCaseLvl1;
	}
	/** 設定細項1_授權層級 **/
	public void setDetailCaseLvl1(String value) {
		this.detailCaseLvl1 = value;
	}
	
	/** 取得細項2_授權層級**/
	public String getDetailCaseLvl2() {
		return this.detailCaseLvl2;
	}
	/** 設定細項2_授權層級 **/
	public void setDetailCaseLvl2(String value) {
		this.detailCaseLvl2 = value;
	}
	
	/** 取得細項3_授權層級**/
	public String getDetailCaseLvl3() {
		return this.detailCaseLvl3;
	}
	/** 設定細項3_授權層級 **/
	public void setDetailCaseLvl3(String value) {
		this.detailCaseLvl3 = value;
	}
	
	/** 取得細項4_授權層級**/
	public String getDetailCaseLvl4() {
		return this.detailCaseLvl4;
	}
	/** 設定細項4_授權層級 **/
	public void setDetailCaseLvl4(String value) {
		this.detailCaseLvl4 = value;
	}
	
	/** 取得細項5_授權層級**/
	public String getDetailCaseLvl5() {
		return this.detailCaseLvl5;
	}
	/** 設定細項5_授權層級 **/
	public void setDetailCaseLvl5(String value) {
		this.detailCaseLvl5 = value;
	}
	
	/** 取得細項6_授權層級**/
	public String getDetailCaseLvl6() {
		return this.detailCaseLvl6;
	}
	/** 設定細項6_授權層級 **/
	public void setDetailCaseLvl6(String value) {
		this.detailCaseLvl6 = value;
	}
	
	/** 取得細項7_授權層級**/
	public String getDetailCaseLvl7() {
		return this.detailCaseLvl7;
	}
	/** 設定細項7_授權層級 **/
	public void setDetailCaseLvl7(String value) {
		this.detailCaseLvl7 = value;
	}
	
	/** 取得細項8_授權層級**/
	public String getDetailCaseLvl8() {
		return this.detailCaseLvl8;
	}
	/** 設定細項8_授權層級 **/
	public void setDetailCaseLvl8(String value) {
		this.detailCaseLvl8 = value;
	}
	
	/** 取得細項9_授權層級**/
	public String getDetailCaseLvl9() {
		return this.detailCaseLvl9;
	}
	/** 設定細項9_授權層級 **/
	public void setDetailCaseLvl9(String value) {
		this.detailCaseLvl9 = value;
	}
	
	/** 取得細項10_授權層級**/
	public String getDetailCaseLvl10() {
		return this.detailCaseLvl10;
	}
	/** 設定細項10_授權層級 **/
	public void setDetailCaseLvl10(String value) {
		this.detailCaseLvl10 = value;
	}
	
	/** 取得細項11_授權層級**/
	public String getDetailCaseLvl11() {
		return this.detailCaseLvl11;
	}
	/** 設定細項11_授權層級 **/
	public void setDetailCaseLvl11(String value) {
		this.detailCaseLvl11 = value;
	}
	
	/** 取得細項12_授權層級**/
	public String getDetailCaseLvl12() {
		return this.detailCaseLvl12;
	}
	/** 設定細項12_授權層級 **/
	public void setDetailCaseLvl12(String value) {
		this.detailCaseLvl12 = value;
	}
}
