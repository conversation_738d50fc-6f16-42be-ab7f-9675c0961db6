/* 
 * LMS1401Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01D;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.eloan.lms.model.L140M01E_AF;
import com.mega.eloan.lms.model.L140M01F;
import com.mega.eloan.lms.model.L140M01G;
import com.mega.eloan.lms.model.L140M01H;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L140M01K;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01N;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M01O_0307;
import com.mega.eloan.lms.model.L140M01Q;
import com.mega.eloan.lms.model.L140M01S;
import com.mega.eloan.lms.model.L140M01T;
import com.mega.eloan.lms.model.L140M01U;
import com.mega.eloan.lms.model.L140M02A;
import com.mega.eloan.lms.model.L140M02S;
import com.mega.eloan.lms.model.L140S03A;
import com.mega.eloan.lms.model.L140S06A;
import com.mega.eloan.lms.model.L782A01A;
import com.mega.eloan.lms.model.L782M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;

/**
 * <pre>
 * [國內企金]額度明細表介面
 * </pre>
 * 
 * @since 2012/10/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/21,REX,new
 *          </ul>
 */
public interface LMS1401Service extends AbstractService {

	// All

	@SuppressWarnings("rawtypes")
	public List<? extends GenericBean> findModelListByMainId(Class clazz,
			String mainId);

	/**
	 * 額度批覆表
	 * 
	 * @param mainId
	 *            文件編號
	 * @return L140M02A
	 */
	L140M02A findL140M02AByMainId(String mainId);

	/**
	 * 儲存model
	 * 
	 * @param entity
	 *            model
	 * 
	 */
	void save(GenericBean... entity);

	/**
	 * 刪除model
	 * 
	 * @param entity
	 *            model
	 * @return
	 */
	void delete(GenericBean... entity);

	/**
	 * 取得Grid呈現所需的資料
	 * 
	 * @param clazz
	 *            要搜索model的class
	 * @param search
	 *            搜索的條件
	 * @return Page
	 */
	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * 用oid取得這筆資料
	 * 
	 * @param <T>
	 *            model
	 * @param clazz
	 *            要搜索model的class
	 * @param oid
	 *            文件編號
	 * 
	 * @return GenericBean
	 */
	@SuppressWarnings("rawtypes")
	<T extends GenericBean> T findModelByOid(Class clazz, String oid);

	/**
	 * 取得額度明細表的說明敘述檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @return List<L140M01B>
	 */
	List<L140M01B> findL140m01bByMainId(String mainId);

	// L782M01A
	/**
	 * 取得特殊案件登錄的紀錄
	 * 
	 * @param mainId
	 *            文件編號
	 * @param loanTP
	 *            授信科目代碼
	 * @return L782M01A
	 */
	L782M01A findL782m01aByUniqueKey(String mainId, String loanTP);

	// L120M01B
	/**
	 * 取得額度種類的紀錄
	 * 
	 * @param mainId
	 *            文件編號
	 * @return L120M01B
	 */
	L120M01B findL120m01bByUniqueKey(String mainId);

	/**
	 * 找出關聯檔內該mainId下所有的額度明細表、額度批覆表、母行法人提案意見
	 * 
	 * @param caseMainId
	 *            案件簽報書文件編號
	 * @return List<L120M01C>
	 */
	List<L120M01C> findL120m01cListByMainId(String caseMainId);

	/**
	 * 找出關聯檔內該mainId下所有的額度明細表、額度批覆表、母行法人提案意見
	 * 
	 * @param caseMainId
	 *            案件簽報書文件編號
	 * @return List<L120M01C>
	 */
	List<L120M01C> findL120m01cListByMainId(String caseMainId,
			String docStatus, String[] caseType);

	/**
	 * 判斷相同借款人的額度明細表，有幾種幣別，跟有幾個借款人已登錄額度明細表
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return List<Object[]>
	 */
	List<Object[]> findL140m01aListByL120m01c(String caseMainId, String caseType);

	/**
	 * 計算額度明細表合計
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return Map<String, Map<String, Long>>
	 */
	Map<String, Map<String, BigDecimal>> findL140m01Count(String caseMainId,
			String caseType) throws Exception;

	/**
	 * 計算額度明細表合計兩種以上幣別
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param curr
	 *            要算出的幣別
	 * @param showCurr
	 *            是否顯示多幣別
	 * 
	 * @return Map<String, Map<String, BigDecimal>> 計算結果
	 */
	Map<String, Map<String, BigDecimal>> findL140m01CountToTwoCurr(
			String caseMainId, String caseType, String curr, Boolean showCurr,
			Boolean saveRateFg) throws Exception;

	/**
	 * 計算額度明細表合計給供調整視窗使用
	 * 
	 * J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param countCurr
	 *            主要計算幣別
	 * @param showCurr
	 *            是否顯示多幣別
	 * @return 計算結果
	 */
	CapAjaxFormResult findL140m01editCount(String caseMainId, String caseType,
			String countCurr, Boolean showCurr, Boolean saveRateFg,
			CapAjaxFormResult result) throws Exception;

	/**
	 * 找出此案件簽報書底下的額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * @param docStatus
	 *            額度明細表文件狀態
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType, String docStatus);

	/**
	 * 找出此案件簽報書底下的額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * @param docStatus
	 *            額度明細表文件狀態
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType, String docStatus, String[] dataSrc);

	/**
	 * 找出此案件簽報書底下的額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            String [] 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * @param docStatus
	 *            額度明細表文件狀態
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String[] caseType, String docStatus);

	/**
	 * 找出此案件簽報書底下的額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * 
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType);

	/**
	 * 找出此案件簽報書底下的額度明細表-給列印用的順序
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * 
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainIdForPrint(String mainId,
			String caseType);

	// L140M01a

	/**
	 * 刪除案件簽報書下所有的額度明細表相關資料
	 * 
	 * 根據要被刪除的oid找出底下所有的額度明細表mainId在做刪除
	 * 
	 * @param mainId
	 *            傳進來的oid
	 * @return boolean 回傳執行結果
	 */
	boolean deleteL140m01All(String mainId);

	/**
	 * 取得額度明細表
	 * 
	 * @param mainId
	 *            額度明細表文件編號
	 * @return L140M01A
	 */
	L140M01A findL140m01aByMainId(String mainId);

	/**
	 * 儲存額度明細檔主檔model list
	 * 
	 * @param list
	 *            List<L140M01A>
	 */
	void saveL140m01aList(List<L140M01A> list);

	/**
	 * 根據此額度明細表的mainId 刪除底下所有關聯table
	 * 
	 * @param mainId
	 *            文件編號
	 */
	void deleteL140m01(String mainId);

	/**
	 * 根據案件簽報書的mainId、借款人統編、重複序號 找出額度明細表 並將計算的授信總額度放入
	 * 
	 * @param caseMainId
	 *            簽報書的mainId
	 * @param caseType
	 *            額度明細表種類
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param count
	 *            總和
	 * @return boolean
	 */
	boolean findL140m01aListByMainIdCount(String caseMainId, String caseType,
			String custId, String dupNo, long count);

	/**
	 * 取得額度明細表的說明敘述檔
	 * 
	 * @param mainId
	 *            額度明細表文件編號
	 * @param itemType
	 *            說明的種類
	 * 
	 *            <pre>
	 *  1、限額條件
	 * 	2、利(費)率
	 * 	3、擔保品
	 * 	4、其他敘做條件
	 * 	5、敘做條件異動情形
	 * 	6、附表第一頁
	 * 	7、附表第二頁
	 * 	8、附表第三頁
	 * </pre>
	 * 
	 * @return L140M01B
	 */
	L140M01B findL140m01bUniqueKey(String mainId, String itemType);

	/**
	 * 儲存額度明細表的說明敘述檔List
	 * 
	 * @param list
	 *            L140M01B List
	 */
	void saveL140m01bList(List<L140M01B> list);

	/**
	 * 額度授信科目資料檔
	 * 
	 * @param mainId
	 *            文件編號
	 * 
	 * @return List<L140M01C>
	 */
	List<L140M01C> findL140m01cListByMainId(String mainId);

	/**
	 * 額度授信科目刪除多筆
	 * 
	 * @param oids
	 *            文件編號
	 * @param mainId
	 *            額度明細表mainId
	 */
	void deleteL140m01cList(String[] oids, String mainId);

	// L140M01D

	/**
	 * 額度授信科目限額檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @param lmtType
	 *            <pre>
	 *            1科子目限額
	 *            2科子目合併限額
	 * </pre>
	 * @param lmtSeq
	 *            序列號
	 * @return L140M01D
	 */
	L140M01D findL140m01dUniqueKey(String mainId, String lmtType, Integer lmtSeq);

	/**
	 * 取出額度授信科目限額檔最大的seq
	 * 
	 * @param mainId
	 *            文件編號
	 * @param lmtType
	 *            序號
	 * @return 最大的seq
	 */
	int findL140m01dByMainIdAndlmtTypeMax(String mainId, String lmtType);

	/**
	 * 額度授信科目限額檔刪除多筆
	 * 
	 * @param oids
	 *            文件編號陣列
	 * 
	 */
	void deleteL140m01dList(String[] oids);

	/**
	 * 額度聯行攤貸比率檔 List
	 * 
	 * @param mainId
	 *            文件編號
	 * @param shareBrId
	 *            攤貸分行代碼
	 * @return L140M01E
	 */
	L140M01E findL140m01eByUniqueKey(String mainId, String shareBrId);
	
	/**
	 * 動審額度聯行攤貸比率檔 List
	 * 
	 * @param mainId
	 *            文件編號
	 * @param shareBrId
	 *            攤貸分行代碼
	 * @return L140M01E
	 */
	L140M01E_AF findL140m01e_afByUniqueKey(String mainId, String shareBrId);

	/**
	 * 額度聯行攤貸比率檔 刪除多筆
	 * 
	 * @param oids
	 *            文件編號陣列
	 */
	void deleteL140m01eList(String[] oids);
	
	/**
	 * 動審額度聯行攤貸比率檔 刪除多筆
	 * 
	 * @param oids
	 *            文件編號陣列
	 */
	void deleteL140m01e_afList(String[] oids);

	// L140M01F

	/**
	 * 額度利費率主檔 取出這個mainID底下的最大Seq
	 * 
	 * @param mainId
	 *            文件編號
	 * @return 最大Seq
	 */
	int findL140m01fByMainIdMax(String mainId);

	/**
	 * 額度利費率主檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @param rateSeq
	 *            序列號
	 * @return L140M01F
	 */
	L140M01F findL140m01fByUniqueKey(String mainId, Integer rateSeq);

	/**
	 * 額度利費率主檔刪除，並同時刪除L140M01G、L140M01H
	 * 
	 * @param oids
	 *            文件編號
	 * @param Seqs
	 *            序號
	 * @param mainId
	 *            額度明細表tabFormMainId
	 * 
	 */
	void deleteL140m01fList(String[] oids, String[] Seqs, String mainId);

	/**
	 * 額度利率明細檔特定幣別
	 * 
	 * @param mainId
	 *            文件編號
	 * @param rateSeq
	 *            序列號
	 * @param rateType
	 *            幣別種類
	 * 
	 *            <pre>
	 * 1新台幣、2美金、3日幣、4歐元、5雜幣
	 * </pre>
	 * @return L140M01G
	 */
	L140M01G findL140m01gByUniqueKey(String mainId, Integer rateSeq,
			String rateType);

	/**
	 * 額度利率明細檔所有幣別
	 * 
	 * @param mainId
	 *            文件編號
	 * @param rateSeq
	 *            序列號
	 * @return List<L140M01G>
	 */
	List<L140M01G> findL140m01gListByMainIdAndRateSeq(String mainId,
			Integer rateSeq);

	// L140M01H
	/**
	 * 額度費率明細檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @param rateSeq
	 *            序列號
	 * @return L140M01H
	 */
	L140M01H findL140m01hByUniqueKey(String mainId, Integer rateSeq);

	// 方法

	/**
	 * 組成幣別和利率說明
	 * 
	 * @param MainId
	 *            文件編號
	 * @param RateSeq
	 *            序列號
	 * @return String
	 */
	String toRateDrc(String MainId, Integer RateSeq);

	/**
	 * 組成限額字串後儲存 並且 更改檢核狀態
	 * 
	 * @param mainId
	 *            文件編號
	 * @param pageNum
	 *            頁碼
	 * @param copyL140M01E
	 *            是否要組攤貸 字串
	 * @return String
	 */
	String saveL140m01bDscr1(String mainId, String pageNum, Boolean copyL140M01E);

	/**
	 * 組成限額字串後儲存 並且 更改檢核狀態
	 * 
	 * @param mainId
	 *            文件編號
	 * @param pageNum
	 *            頁碼
	 * @return String
	 */
	String saveL140m01bDscr1(String mainId, String pageNum);

	/**
	 * 組成費率字串後儲存 並且 更改檢核狀態
	 * 
	 * @param mainId
	 *            文件編號
	 * @param pageNum
	 *            頁碼
	 * @return String
	 */

	String saveL140m01bDscr2(String mainId, String pageNum, boolean getDscr) throws CapMessageException;

	/**
	 * 轉換符號
	 * 
	 * @param word
	 *            JSONArray
	 * @return String
	 */
	String convertMark(JSONArray word);

	/**
	 * 轉換符號
	 * 
	 * @param word
	 *            JSONArray
	 * @return array
	 */
	String[] convertMark(String word);

	/**
	 * 查詢基準利率
	 * 
	 * @param type
	 *            利率的種類 6D - 本行基準利率 、6S-基準利率月指標利率 、QX-6165初級市場ＣＰ九十天期(KX)平均利率
	 * @param curr
	 *            幣別
	 * @return 幣別利率
	 */
	String queryBaseRate(String type, String curr);

	/**
	 * 取得額度明細表分行逾放比
	 * 
	 * @param ownBrIds
	 *            要引進的所有銀行列表
	 * 
	 * @return
	 */
	TreeMap<String, Map<String, Object>> queryLnf226(
			HashMap<String, String> ownBrIds);

	/**
	 * 找出這個簽報書底下的額度明細表 By custId 排序
	 * 
	 * @param mainId
	 *            文件編號
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return List<L140M01A>
	 */
	public List<L140M01A> findL140m01aListByL120m01cMainIdOrderByCust(
			String mainId, String caseType);

	/**
	 * 額度序號給號
	 * 
	 * @param ownBrId
	 *            分行代碼
	 * @param unitCode
	 *            借款人類別 0-DBU OBU-4,海外-5
	 * @param classCD
	 *            String
	 * @return Map<String, Object>
	 */
	Map<String, Object> queryLnsp0050(String ownBrId, String unitCode,
			String classCD);

	/**
	 * 引進帳務資料 購料放款案下已開狀未到單金額
	 * 
	 * @param custId
	 *            客戶ID
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @param lastcurr
	 *            前准額度幣別
	 * @return Map<String, String>
	 */
	Map<String, String> queryDwlnquotov(String custId, String dupNo,
			String cntrNo, String lastcurr);

	// void startFlow(String mainOid);
	/*
	 * public void flowAction(String mainOid, GenericBean model, boolean
	 * setResult, boolean resultType) throws Throwable;
	 */
	/**
	 * 判斷是否已經登錄國內分行
	 * 
	 * @param MainId
	 *            文件編號
	 * @param flag
	 *            String
	 * @return List<L140M01E>
	 */
	List<L140M01E> queryByMainIdAndFlag(String MainId, String[] flag);

	/**
	 * 搜尋這個連保人是否已經登錄 J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	 * 
	 * @param mainId
	 *            文件編號
	 * @param type
	 *            1.自然人 2.法人
	 * 
	 * @param rId
	 *            連保人統編
	 * @param rDupNo
	 *            連保人統編重覆碼
	 * @return L140M01I
	 */
	// L140M01I findL140m01iByUniqueKey(String mainId, String type, String rId,
	// String rDupNo);

	/**
	 * 搜尋額度明細表是否存在此額度序號
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重覆序號
	 * @return 額度明細表
	 */
	List<L140M01A> findL140m01aBycntrNo(String cntrNo, String custId,
			String dupNo);

	/**
	 * 刪除連保人list
	 * 
	 * @param oids
	 *            文件編號陣列
	 * @param mainId
	 *            文件編號
	 * @return boolean
	 */
	boolean deleteListL140m01i(String[] oids, String mainId);

	/**
	 * 根據mainId Array 找出所有的額度明細表
	 * 
	 * @param mainId
	 *            mainId陣列
	 * @return List<L140M01A>
	 */
	List<L140M01A> findL140m01aListByMainIdList(String[] mainId);

	/**
	 * 取得該ID 在徵信報告書底的連保人
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cesMainId
	 *            徵信報告書
	 * @return Map<String, Map<String, JSONObject>>
	 * 
	 *         <pre>
	 * N 自然人  Map<id , name >
	 * </pre>
	 * 
	 */
	Map<String, Map<String, JSONObject>> findL140m01iPeopleData(String custId,
			String dupNo, String cesMainId);

	/**
	 * 儲存連保人資料
	 * 
	 * @param L140m01iList
	 *            連保人陣列
	 */
	void saveL140m01iList(List<L140M01I> L140m01iList);

	/**
	 * 根據oids 抓出所有額度明細表
	 * 
	 * @param oids
	 *            陣列
	 * @return List<L140M01A>
	 */
	public List<L140M01A> findL140m01aByOids(String[] oids);

	/**
	 * 特殊登錄案件
	 * 
	 * @param mainId
	 *            文件編號
	 * @param authUnit
	 *            目前分行
	 * @return L782A01A
	 */
	public L782A01A findL782A01AByMainId(String mainId, String authUnit);

	/**
	 * 額度明細表主要儲存
	 * 
	 * @param l140m01bs
	 *            額度明細表敘述檔
	 * @param l140m01es
	 *            攤貸行
	 * @param entity
	 *            額度明細表相關檔案
	 */
	public void saveMain(List<L140M01B> l140m01bs, List<L140M01E> l140m01es,
			GenericBean... entity);

	/**
	 * 複製額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書mainId
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * @param mainName
	 *            要設定的借款人名稱和統一編號
	 * @param oidList
	 *            要複製的額度明細表oid
	 */
	public CapAjaxFormResult copyCntrdoc(String mainId, String caseType,
			String[] mainName, String[] oidList, Map<String, Object> excelMap) throws CapException;

	/**
	 * 複製聯行額度明細表 <br>
	 * Uid 塞入原轉入聯行的簽報書mainId
	 * 
	 * @param mainId
	 *            目前案件簽報書mainId
	 * @param selectMainid
	 *            原聯行案件簽報書mainId
	 */
	public void copyCntrdocByl141m01a(String mainId, String[] oidList)
			throws CapException;

	/**
	 * 刪除多個額度明細表
	 * 
	 * @param list
	 *            要刪除的額度明細表集合
	 */
	void delL140m01aList(List<L140M01A> list);

	/**
	 * 刪除多個收復彙計數，並更改額度明細表檢核欄位
	 * 
	 * @param oids
	 *            收付彙計數的oid
	 * @param l140m01a
	 *            額度明細表主檔
	 */
	void delL140M01KByOids(String[] oids, L140M01A l140m01a);

	/**
	 * 刪除多個收復彙計數BY LIST，並更改額度明細表檢核欄位
	 * 
	 * @param l140m01a
	 * @param l140m01ks
	 */
	void delL140M01KList(L140M01A l140m01a, List<L140M01K> l140m01ks);

	/**
	 * 儲存多個收復彙計數BY LIST
	 * 
	 * @param list
	 */
	void saveL140m01kList(List<L140M01K> list);

	/**
	 * 限額控管
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @param showCntrNo
	 *            是否顯示額度序號 true 為呈主管時檢查 false 為 給號時檢查
	 * @return 限額控管的訊息 若不需限額控管 回傳 空白
	 * @throws CapMessageException
	 */

	public Map<String, String> gfnDB2ChkNeedControlByCntrDoc(L140M01A l140m01a,
			Boolean showCntrNo) throws CapMessageException;

	/**
	 * 限額控管 呈案 檢查
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @return 限額控管的訊息 若不需限額控管 回傳 空白
	 * @throws CapMessageException
	 */

	public String gfnDB2ChkNeedControl(L140M01A l140m01a) throws CapMessageException;

	/**
	 * 儲存聯行攤貸比率
	 * 
	 * @param l140m01es
	 *            聯行攤貸比率
	 */
	public void savelistL140M01E(List<L140M01E> l140m01es);

	/**
	 * 儲存聯行攤貸比率
	 * 
	 * @param l140m01es
	 *            聯行攤貸比率
	 */
	public void savelistL140M01E_AF(List<L140M01E_AF> l140m01e_afs);
	
	/**
	 * 儲存關聯檔list
	 */

	public void savelistL120M01C(List<L120M01C> l120m01cs);

	/**
	 * 引進擔保品
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @param oids
	 *            擔保品 C100M01 oid陣列
	 * @return
	 */
	public String inculdeL140M01O(String mainId, String[] oids)	throws CapMessageException;

	/**
	 * 刪除擔保品
	 * 
	 * @param oids
	 *            L140M01O.oid 陣列
	 * @param mainId
	 *            額度明細表 mainId
	 */
	public String deleteL140M01O(String[] oids, String mainId);

	/**
	 * 重新引進擔保品描述
	 * 
	 * @param mainId
	 *            額度明細表 mainId
	 * 
	 *            return 取得擔保品描述
	 */
	public String reloadCMSDesc(String mainId);

	/**
	 * J-113-0121 企金額度明細表-擔保品-引進擔保品-增加以重置成本計算火險，查詢後顯示重置成本金額供參考
	 * 引入擔保品時，設定[火險金額計算方式]及[火險金額](預設以重置成本作為火險金額)
	 * 查詢重置成本
	 * @param l140m01o
	 *            授信擔保品明細檔
	 * @param fireInsTypeVal
	 * 			     火險金額計算方式
	 * @return
	 */
	public Map<String,String> queryL140M01OrebuildCost(L140M01O l140m01o, String fireInsTypeVal);
	/**
	 * 重新引進共同借款人
	 * 
	 * @param oids
	 *            L120S01A.oid 陣列
	 * @param mainId
	 *            額度明細表 mainId
	 * @param custPos
	 *            性質
	 * @return
	 * @throws CapMessageException
	 */
	public String inculdeL140M01J(String[] oids, String mainId, String custPos) throws CapMessageException;

	/**
	 * 刪除共同借款人
	 * 
	 * @param oids
	 *            L140M01J.oid 陣列
	 * @param mainId
	 *            額度明細表 mainId
	 * @return
	 */
	public String deleteL140M01J(String[] oids, String mainId);

	/**
	 * 取得國別
	 * 
	 * @param mainId
	 *            簽報書mainId
	 * @return Map<custid+dupno , 國別>
	 */
	public HashMap<String, String> getCustCounty(String mainId);

	/**
	 * 組共用借款人字串
	 * 
	 * @param l140m01js
	 *            共用借款人檔
	 * @return 組成字串
	 */
	public String getL140M01JStr(List<L140M01J> l140m01js);

	/**
	 * 組共用借款人字串
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @return 組成字串
	 */
	public String getL140M01JStr(String mainId);

	/**
	 * 組利率結構化字串
	 * 
	 * @param l140m01n
	 *            利率結構化主檔
	 * @return 組成字串 利率結構化主檔
	 */
	public L140M01N setL140M01NStr(L140M01N l140m01n, boolean getDscr) throws CapMessageException;

	/**
	 * 組成費率字串
	 * 
	 * @param MainId
	 *            額度明細表mainId
	 * @param RateSeq
	 *            利率主檔Seq
	 * @return
	 */
	public String toRateDrcForL140M01N(String MainId, Integer RateSeq);

	/**
	 * 查詢簽章欄
	 * 
	 * @param MainId
	 *            簽報書文件編號
	 * @return List<L120M01F>
	 */
	public List<L120M01F> findL120m01fByMainId(String MainId);

	/**
	 * 刪除結構利率化檔
	 * 
	 * @param oids
	 *            L140M01N.oid 陣列
	 * @param mainId
	 *            額度明細表 mainId return 利率組合字串
	 */
	public String deleteL140M01N(String[] oids, String mainId) throws CapMessageException;

	/**
	 * 複製結構利率化檔
	 * 
	 * @param oids
	 *            L140M01N.oid 陣列
	 * @param mainId
	 *            額度明細表 mainId return 利率組合字串
	 */
	public String copyL140M01N(String[] oids, String mainId) throws CapException;

	/**
	 * 儲存利率檔
	 * 
	 * @param params
	 *            前端回傳資料
	 * @return <pre>
	 *  { 
	 * 	rateId: L140M01F 利費率主檔oid 
	 * rateSeq:L140M01F 利費率主檔rateSeq
	 * L140M01NDrc: 利費率所有描述
	 * upRateDscr : 利費率上傳用的描述
	 * }
	 * </pre>
	 */
	public CapAjaxFormResult saveL140M01N(PageParameters params) throws CapMessageException;

	/**
	 * 查詢結構利率化檔L140M01N
	 * 
	 * @param mainId
	 *            L140M01F 利費率主檔mainId
	 * @param rateSeq
	 *            L140M01F 利費率主檔rateSeq
	 */
	public List<L140M01N> findL140m01nByMainIdRateSeq(String mainId,
			Integer rateSeq);

	/**
	 * 複製額度明細表 來源為簽報書
	 * 
	 * @param caseMainId
	 *            簽報書mainId
	 * @param oldCaseMainId
	 *            舊案簽報書mainId
	 * @param oids
	 * 
	 *            額度明細表oid陣列
	 * @throws CapException
	 */
	public void copySrcByL140M01A(String caseMainId, String oldCaseMainId,
			String[] oids) throws CapException;

	/**
	 * 重新引進 更新該份額度明細表 姓名 案號
	 * 
	 * @param caseMainId
	 *            簽報書mainId
	 * @param parent
	 * @throws CapMessageException
	 */
	public void reloadCustName(String caseMainId) throws CapMessageException;

	/**
	 * 
	 * 企金銀行法／金控法利害關係人檔
	 * 
	 * @param caseMainId
	 *            簽報書mainId
	 * @return
	 */
	public L120S01D findL120S01DByKey(String caseMainId, String custId,
			String dupNo);

	/**
	 * 新增額度明細表
	 * 
	 * @param params
	 * @return
	 */
	public L140M01A addL140M01A(PageParameters params) throws CapException;

	/**
	 * 產生舊案額度明細表
	 * 
	 * @param params
	 */
	public void create_L140M01A_OLD(PageParameters params) throws CapException;

	/**
	 * 取得央行房貸註記
	 * 
	 * @param mainId
	 * @return
	 */
	L140M01M findL140m01mByMainId(String mainId);

	/**
	 * 取得大陸地區投資控管註記
	 * 
	 * @param mainId
	 * @return
	 */
	L140M01Q findL140m01qByMainId(String mainId);

	/**
	 * 取得簽報書項下符合條件(統編、重複序號與額度序號)的額度明細表
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	public List<L140M01A> findL140m01aListByainIdCustIdCntrno(String mainId,
			String custId, String dupNo, String cntrNo);

	/**
	 * 額度明細表登錄科目向上、下排序用
	 * 
	 * @param oid
	 * @return
	 */
	L140M01C findL140m01cByOid(String oid);

	/**
	 * 額度明細表登錄科目向上、下設定序號
	 * 
	 * @param model
	 *            L140M01C
	 * @param upOrDown
	 *            向上=true 向下=false
	 * @return
	 */
	boolean changeSeqNum(L140M01C l140m01c, boolean upOrDown);

	/**
	 * 重設額度明細表項下所有科目之seqNum
	 * 
	 * @param l140m01c
	 * @param upOrDown
	 * @return
	 */
	boolean resetL140M01CAllSeqNum(String mainId);

	/**
	 * 引進收付彙計數(L202)
	 * 
	 * @param ownBrId
	 *            分行代碼
	 * @param unitCode
	 *            借款人類別 0-DBU OBU-4,海外-5
	 * @param classCD
	 *            String
	 * @return Map<String, Object>
	 */
	Map<String, Object> queryLnsp0330(String cntrNo, String begDate,
			String endDate);

	/**
	 * J-104-0270-001 Web e-Loan國內授信管理系統OBU戶檢核要有聯徵虛擬統編才能送呈主管
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L140M01A> findL140m01aListByMainIdCustId(String caseMainId,
			String custId, String dupNo, String itemType);

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
	 * 計算授信額度合計時，將本案異動金額加入信用風險遵循 FOR LOCAL
	 * 
	 * @param caseMainId
	 * @param caseType
	 * @return
	 */
	public String reCaculateL120S01M(String caseMainId, String caseType);

	public String resetLocalL120S01OAndReCaculateImpls(PageParameters params);

	/**
	 * 判斷額度明細表下所有利率條件是否有下限利率，若有一筆有則視為該額度有下限利率 J-105-0088-001 Web e-Loan
	 * 企金授信系統利率條件無下限利率時，新增a-Loan是否需建置下限利率欄位並上傳a-Loan檢核。
	 * 
	 * @param l140m01a
	 * @param parent
	 * @throws CapMessageException
	 */
	public String saveL140M01A_hasRateLimit(L140M01A l140m01a) throws CapMessageException;

	/**
	 * J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等 額度明細表取得個金評等
	 * 
	 * @param params
	 * @param l140m01a
	 * @param tabFormMainId
	 * @param cntrNo
	 * @param allCheackedVal
	 * @return
	 */
	public CapAjaxFormResult findL140s03a(PageParameters params,
			L140M01A l140m01a, String tabFormMainId, String cntrNo,
			String allCheackedVal);

	/**
	 * J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等 刪除額度明細表之個金評等
	 * 
	 * @param list
	 */
	public void deleteListL140s03a(List<L140S03A> list);

	/**
	 * J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等 儲存額度明細表之個金評等
	 * 
	 * @param list
	 */
	public void saveListL140s03a(List<L140S03A> list);

	/***
	 * J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等 組成額度明細表個金評等字串(顯示與列印用)
	 * 
	 * @param tabFormMainId
	 * @return
	 */
	public String buildL140S03AStr(String tabFormMainId);

	/**
	 * 取得額度性質非不變或解除之額度明細表/批覆書 J-105-0179-001 Web
	 * e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
	 */
	public List<L140M01A> findL140m01aListByMainIdCustIdWithoutProperty7Or8(
			String caseMainId, String custId, String dupNo, String itemType);

	/**
	 * 取得額度明細表主借款人與所有共借人L120S01D List J-105-0250-001 Web e-Loan 新增利害關係人檢核
	 * 
	 * @param l140m01a
	 * @return
	 */
	public List<L120S01D> getCntrDocAllBorrowerL120S01D(L140M01A l140m01a);

	/**
	 * 檢查額度明細表是否有無擔科目 J-105-0250-001 Web e-Loan 新增利害關係人檢核
	 * 
	 * @param l140m01a
	 * @param exceptSubject
	 * @return
	 */
	public boolean chkCntrDocHasUnSecureSubject(L140M01A l140m01a,
			String sbjProperty, String[] exceptSubject);

    public boolean chkIsNeedDerivEval(L140M01A l140m01a);

    public boolean chkIsNeedEvalDate(L140M01A l140m01a);

    public boolean chkEvalDate(L140M01A l140m01a);

	public boolean chkEvalDateOk(L140M01A l140m01a);

	/**
	 * J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public L120S01B findL120S01BByKey(String caseMainId, String custId,
			String dupNo);

	/*
	 * J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	 */
	public L140M01I findL140m01iByUniqueKeyWithRType(String mainId,
			String type, String rId, String rDupNo, String rType);

	/*
	 * J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	 */
	public List<L140M01I> findL140m01iListWithRType(String mainId, String rType);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public L140M01S findL140m01sByOid(String oid);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public List<L140M01S> findL140m01sByMainIdTypeCustId(String mainId,
			String type, String custId, String dupNo);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public int findL140m01sByMainIdAndTypeMax(String mainId, String type);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public void deleteL140m01sList(String[] oids);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public List<L140M01S> findL140m01sByMainIdType(String mainId, String type);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public void deleteL140m01sAll(String mainId, String type);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public List<L140M02S> findL140m02sByMainIdType(String mainId, String type);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public void deleteL140m02sAll(String mainId, String type);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public List<L140M02S> findL140m02sByMainIdTypeCustId(String mainId,
			String type, String custId, String dupNo);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public L140M02S findL140m02sByMainIdTypeCustIdCntrNo(String mainId,
			String type, String custId, String dupNo, String cntrNo);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public int findL140m02sByMainIdAndTypeMax(String mainId, String type);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public L140M02S findL140m02sByMainIdTypeItemSeq(String mainId, String type,
			Integer itemSeq);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public L140M01S findL140m01sByMainIdTypeItemSeqCustIdDupNo(String mainId,
			String type, Integer itemSeq, String custId, String dupNo);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public void saveL140m01sList(List<L140M01S> list);

	/**
	 * 儲存72-2註記資料
	 * 
	 * @param list
	 */
	void saveL140m01tList(List<L140M01T> list);

	/**
	 * 刪除72-2註記資料和附件
	 * 
	 * @param oid
	 */
	void deleteL140m01tAndFile(String oid);

	/**
	 * 取得都更危老母戶預約資料
	 * 
	 * @param mCntrNo
	 * @return
	 */
	L140M01T getBuildInfoByMcntrNo(String mCntrNo);

	/**
	 * 刪除本案72-2註記資料
	 * 
	 * @param mainId
	 */
	void deleteCurrentL140m01ts(String mainId);

	/**
	 * 本案72-2註記資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<L140M01T> findCurrentL140m01ts(String mainId);

	/**
	 * 前案72-2註記資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<L140M01T> findLastL140m01ts(String mainId);

	L140M01T findL140m01t(String mainId, String estateType);

	/**
	 * 儲存額度明細表的共同行銷檔List
	 * 
	 * @param list
	 */
	void saveL140m01uList(List<L140M01U> list);

	public List<L140M01U> findL140m01us(String mainId);

	public L140M01U findL140m01uByMainidType(String mainId, String type);

	public List<L140M01U> transformL140m01us(List<L140M01U> l140m01uTemp,
			String[] csType, Map<String, String> value, L140M01A l140m01a);

	public void deleteL140m01us(String mainId);

	public CapAjaxFormResult getL140m01us(CapAjaxFormResult result,
			String mainId, String type) throws CapException;

	/**
	 * 刪除額度名細表引進的個金信評資料
	 * 
	 * @param mainId
	 */
	void deleteL140s03aByMainId(String mainId);

	/**
	 * J-108-0303 連鎖店Chain store J-108-0304 投資台灣三大方案 取得專案種類細項資訊
	 */
	public CapAjaxFormResult getL140m01w(CapAjaxFormResult result,
			String mainId, String projClass) throws CapException;

	/**
	 * J-108-0303 連鎖店Chain store 取得主事業體額度序號
	 */
	public String findL140m01vById(String custId, String dupNo);

	public List<L140S06A> findL140s06as(String mainId);

	public L140S06A findL140s06aByMainIdAndIntReg(String mainId, String intReg);

	void saveL140s06aList(List<L140S06A> list);

	void deleteL140s06a(String oid);

	public void deleteL140s06aAll(String mainId);

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L140M01O_0307> findL140m01o_0307as(String mainId);

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * @param list
	 */
	public void saveL140m01o_0307List(List<L140M01O_0307> list);

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * @param oids
	 * @param mainId
	 */
	public void deleteL140m01o_0307(String[] oids, String mainId);

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * @param mainId
	 */
	public void deleteL140m01o_0307All(String mainId);

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * @param mainId
	 * @param stkNo
	 * @param StkNm
	 * @return
	 */
	public List<L140M01O_0307> findL140m01o_0307ByStkNoAndStkNm(String mainId,
			String stkNo, String StkNm);

	public void modifyElf447nRescueData(L120M01A l120m01a, L140M01A l140m01a);

	public void updateElf447nRescueData(L120M01A l120m01a, L140M01A l140m01a,
			boolean isDelete);

	public void clearNotExistCntrDocElf447nRescueData(L120M01A l120m01a,
			String itemType);

	/**
	 * 產生產生小規模營業人額度明細表
	 * 
	 * 
	 * J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * @param params
	 */
	public CapAjaxFormResult create_L140M01A_SmallBuss_TypeC(
			PageParameters params, String rpaCesMainId,
			boolean isNewCase, String templateMainId) throws CapException;

	/**
	 * 引進分行逾放比率
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public JSONObject queryNPLInner(String mainId, String cntrNo) throws CapException;

	/**
	 * 取得代碼轉換
	 * 
	 * J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public String queryL140s08aByItemNameAndSubItem(String itemName,
			String subItem) throws CapException;

	/**
	 * 新增經濟部B方案(營運週轉金)搭配央行A、B專案明細表
	 * 
	 * 
	 * J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * @param params
	 */
	public CapAjaxFormResult create_L140M01A_A02(PageParameters params,
		String caseSubType) throws CapException;

	/**
	 * 產生青年創業及啟動金貸款額度明細表
	 * 
	 * 
	 * J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
	 * 
	 * @param params
	 */
	public CapAjaxFormResult create_L140M01A_lnType_61(PageParameters params) throws CapException;

	/**
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param itemType
	 * @return
	 */
	public L140M01A findL140m01aByCaseMainIdCustIdCntrNoItemType(
			String caseMainId, String custId, String dupNo, String cntrNo,
			String itemType);

	/**
	 * J-110-0195_05097_B1001 Web e-Loan 企金授信額度明細表新增以整批匯入方式填列
	 * 
	 * @param l140m01a
	 * @param l120m01b
	 * @param prop
	 * @param itemMap
	 * @param l140m01js
	 * @param custIdSet
	 * @param l120s01d
	 * @param l140m01m
	 * @param l140m01ns
	 * @param l140m01q
	 * @param l120m01a
	 * @return
	 * @throws CapException
	 */
	public String isCheckData(L140M01A l140m01a, L120M01B l120m01b,
			Properties prop, HashMap<String, String> itemMap,
			List<L140M01J> l140m01js, HashSet<String> custIdSet,
			L120S01D l120s01d, L140M01M l140m01m, List<L140M01N> l140m01ns,
			L140M01Q l140m01q, L120M01A l120m01a) throws CapException;

	/**
	 * 設定案由
	 * 
	 * @param l140m01cs
	 * @param l140m01a
	 * @param codeMap
	 * @param prop
	 * @return
	 */
	public String getGistStr(List<L140M01C> l140m01cs, L140M01A l140m01a,
			Map<String, String> codeMap, Properties prop);

	/**
	 * 引進平均動用率
	 * 
	 * @param mainId
	 * @param cntrNo
	 * @return
	 * @throws CapException
	 */
	public Map<String, Object> queryUseParAloan(String mainId, String cntrNo)
			throws CapException;

	/**
	 * 
	 * 簽報書檢核小規模營業人額度是否超過限額
	 * 
	 * @param l120m01a
	 * @return
	 */
	public String chkCurrentApplyAmtForSmallBussC(L120M01A l120m01a);

	/**
	 * 檢核借款人於ELOAN、ALOAN尚有其他小規模額度(含簽案中、ALOAN未消戶額度)
	 * 
	 * @param l120m01a
	 * @return
	 * @throws CapException
	 */
	public String chkHasOtherSmallBussCNotInCaseReport(L120M01A l120m01a)
			throws CapException;

	public List<L140M01A> findLastSmallBussCL140m01aWithCustid(String custId,
			boolean onlyApprove);

	public List<L140M01A> findLastStartUpReliefL140m01aWithCustid(
			String custId, boolean onlyApprove);

	/**
	 * J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
	 * 
	 * @param params
	 * @throws CapException
	 */
	public CapAjaxFormResult create_L140M01A_By_Template(PageParameters params,
			String rpaCesMainId, boolean isNewCase,
			String templateMainId) throws CapException;

	/**
	 * 
	 * 簽報書檢核國發基金協助新創事業紓困融資加碼方案是否超過簽報書限額 J-110-0CCC_05097_B1001 Web
	 * e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
	 * 
	 * @param l120m01a
	 * @return
	 */
	public String chkCurrentApplyAmtForStartUpReliefPackage(L120M01A l120m01a);

	/**
	 * 由額度批覆表找出對應的那筆額度明細表
	 * 
	 * @param l140m01aItemType2
	 * @return
	 */
	public L140M01A findItemType1FromItemType2(L140M01A l140m01aItemType2);

	/**
	 * 檢核借款人於ELOAN、ALOAN尚有其他國發基金協助新創事業紓困融資加碼方案額度(含簽案中、ALOAN未消戶額度)
	 * 
	 * @param l120m01a
	 * @return
	 * @throws CapException
	 */
	public String chkHasOtherStartUpReliefCntrNoNotInCaseReport(
			L120M01A l120m01a) throws CapException;

	/**
	 * J-111-0268_05097_B1001 Web e-Loan修改額度明細表信保案件相關功能
	 * 
	 * 額度明細表信保案件類別
	 * 
	 * @param l140m01a
	 * @return 1:中小信保 2:海外信保 3:國家融資保證機制
	 */
	public String getSmeKind(L140M01A l140m01a);

	/**
	 * J-111-0268_05097_B1001 Web e-Loan修改額度明細表信保案件相關功能
	 * 
	 * @param l140m01a
	 * @param prop
	 * @return
	 */
	public String getSmeStr(L140M01A l140m01a);

	/**
	 * J-111-0411_05097_B1001 Web e-Loan企金授信新增不動產授信例外管理相關功能
	 * 
	 * @param l140m01a
	 * @return
	 */
	public boolean displayIntRegReason(L140S06A l140s06a);

	public String chkCurrentApplyAmtForRescueItem(L120M01A l120m01a);

	/**
	 * 經濟部協助中小型事業疫後振興專案貸款 & 經濟部協助中小企業轉型發展專案貸款
	 * 
	 * 檢查紓困代碼檢核信保保證成數(額度明細表、動審表都會呼叫)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param isRescue
	 * @param rescueItem
	 * @param headItem1
	 * @param gutPercent
	 * @return
	 */
	public String chkSmeRateForRescueItem(String custId, String dupNo,
			String cntrNo, String isRescue, String rescueItem,
			String headItem1, BigDecimal gutPercent);

	/**
	 * J-112-0148_05097_B1001 Web
	 * e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
	 * 
	 * 檢核紓困代碼掛件文號可以為空白
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isRescueItemCanEmptyRescueNo(String rescueItem);

	/**
	 * J-112-0148_05097_B1002 Web
	 * e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
	 * 
	 * @param rescueItem
	 * @param l140m01a
	 * @return
	 */
	public String chkRescueItemReUse(String rescueItem, L140M01A l140m01a);

	/**
	 * 檢核「企業戶各類模型評等等級8級以上或因無「模型評等」其企業資信評等C級以上者」，倘評等不符合，則提示錯誤訊息交易失敗
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return List<String> 各種錯誤訊息
	 */
	public List<String> checkCreditRatingProjClass22(L120M01A l120m01a);

	public boolean isExTotalCreditLimit(L140M01A l140m01a);

	public void saveCountEditForm(PageParameters params) throws CapException;

	/**
	 * 是否徵提保證金
	 * 
	 * J-109-0365_05097_B1001 Web e-Loan國內企金授信額度明細表科目為遠期外匯、換匯交易時，新增是否徵提保證金等相關欄位
	 * 
	 * @param l140m01a
	 * @return
	 */
	public boolean needMarginFlag(L140M01A l140m01a);

    List<Object[]> findSMEAList(Date sDate, Date eDate);
}