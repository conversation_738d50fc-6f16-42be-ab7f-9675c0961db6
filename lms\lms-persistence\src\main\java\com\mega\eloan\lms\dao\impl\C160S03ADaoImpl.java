package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C160S03ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C160S03A;

/**
 * C160S03A 整批自動開戶分戶檔
 * 
 * <AUTHOR>
 * 
 */
@Repository
public class C160S03ADaoImpl extends LMSJpaDao<C160S03A, String> implements
		C160S03ADao {

	@Override
	public C160S03A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C160S03A findByUk(String cntrNo, int seqNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "seqNo", seqNo);
		return findUniqueOrNone(search);
	}
	
	@Override
	public int findMaxSeqNoByCntrNo(String cntrNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addOrderBy("seqNo", true);
		C160S03A bean = findUniqueOrNone(search);
		if(bean==null){
			return 0;
		}
		return bean.getSeqNo();
	}
	
	@Override
	public List<C160S03A> findByMainIdOrderBySeqNo(String mainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seqNo", false);
		List<C160S03A> list = createQuery(C160S03A.class,search).getResultList();
		return list;
	}	
	
	@Override
	public List<C160S03A> findByCntrNo_hasMemo(String cntrNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.GREATER_THAN, "memo_s", "");
		search.addOrderBy("seqNo", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C160S03A> list = createQuery(C160S03A.class,search).getResultList();
		return list;
	}	
}
