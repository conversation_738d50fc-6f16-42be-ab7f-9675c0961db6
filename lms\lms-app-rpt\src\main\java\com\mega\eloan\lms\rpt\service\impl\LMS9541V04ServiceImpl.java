/* 
 *  LMS9541ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service.impl;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.impl.AbstractMFAloanJdbc;
import com.mega.eloan.lms.rpt.service.LMS9541V04Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS9541V04ServiceImpl extends AbstractMFAloanJdbc implements
		LMS9541V04Service {

	private static final Logger logger = LoggerFactory
			.getLogger(LMS9541V04ServiceImpl.class);

	@Override
	public Map<String, Object> findMisData(String custId, String kindNo) {
		try {
			return getJdbc().queryForMap("MIS.SEARCH.MORTGAGE",
					new String[] { custId, kindNo });
		} catch (Exception e) {
			logger.error("[getContent] Exception!!", e.getMessage());
		}
		return null;
	}

	@Override
	public boolean save(String[] data, String custId, String kindNo) {
		try {
			Map<String, Object> oldData = getJdbc().queryForMap(
					"MIS.SEARCH.MORTGAGE", new String[] { custId, kindNo });
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			Object[] record;
			String query;
			if (oldData != null && oldData.size() != 0) {
				record = new Object[data.length + 2];
				// data exist=>update
				query = "MIS.UPDATE.MORTGAGE";
			} else {
				// just save
				record = new Object[data.length + 2];
				query = "MIS.INSERT.MORTGAGE";
			}

			int index = 0;
			for (int i = 0; i < record.length; i++, index++) {
				switch (i) {
				case 7:
				case 8:
					record[i] = NumConverter.delComma(data[index]);
					break;
				case 6:
				case 10:
				case 17:
					if(Util.isNotEmpty(data[index])){
						if(data[index].lastIndexOf(".")!=-1){
							record[i] = Util.parseDate(data[index].substring(0,data[index].lastIndexOf(".")));
						}else{
							record[i] = data[index];
						}
					}
					else{
						record[i]=null;
					}
					break;
				case 14:
					record[i] = user.getUserId();
					break;
				case 15:
					record[i] = CapDate.getCurrentDate("yyyy-MM-dd");
					break;
				case 20:
					record[i] = custId;
					break;
				case 16:
				case 21:
					record[i] = kindNo;
					break;
				default:
					record[i] = data[index];
				}
			}
			getJdbc().update(query, record);
			return true;
		} catch (Exception e) {
			logger.error(e.getMessage());
			return false;
		}
	}

}
