/* 
 * L120S24C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 外部評等風險權數對照表 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S24C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S24C extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 評等分數代碼 **/
	@Size(max=6)
	@Column(name="GRADE", length=6, columnDefinition="VARCHAR(6)")
	private String grade;

	/** S&P等級 **/
	@Size(max=10)
	@Column(name="SPRATING", length=10, columnDefinition="VARCHAR(10)")
	private String SPRating;

	/** Moody’s等級 **/
	@Size(max=10)
	@Column(name="MOODYSRATING", length=10, columnDefinition="VARCHAR(10)")
	private String MoodysRating;

	/** Fitch等級 **/
	@Size(max=10)
	@Column(name="FITCHRATING", length=10, columnDefinition="VARCHAR(10)")
	private String FitchRating;

	/** 中華信評等級 **/
	@Size(max=10)
	@Column(name="TWRATING", length=10, columnDefinition="VARCHAR(10)")
	private String twRating;
	
	/** FitchTW等級 **/
	@Size(max=10)
	@Column(name="FITCHTWRATING", length=10, columnDefinition="VARCHAR(10)")
	private String FitchTWRating;

	/** 中央政府風險權數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="CENTRALGOV", columnDefinition="DECIMAL(5,2)")
	private BigDecimal centralGov;

	/** 地方政府及非營利國營事業風險權數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="LOCALGOV", columnDefinition="DECIMAL(5,2)")
	private BigDecimal localGov;

	/** 銀行債權風險權數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="BANKBOND", columnDefinition="DECIMAL(5,2)")
	private BigDecimal bankBond;

	/** 企業債權風險權數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="ENTERBOND", columnDefinition="DECIMAL(5,2)")
	private BigDecimal enterBond;

	/** 
	 * 版本日期<p/>
	 * 因應有新版的風險權數
	 * 使用到的參數也要切版本
	 * 
	 */
	@Size(max=10)
	@Column(name="VERSIONDATE", length=10, columnDefinition="VARCHAR(10)")
	private String versionDate;
	
	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得評等分數代碼 **/
	public String getGrade() {
		return this.grade;
	}
	/** 設定評等分數代碼 **/
	public void setGrade(String value) {
		this.grade = value;
	}

	/** 取得S&P等級 **/
	public String getSPRating() {
		return this.SPRating;
	}
	/** 設定S&P等級 **/
	public void setSPRating(String value) {
		this.SPRating = value;
	}

	/** 取得Moody’s等級 **/
	public String getMoodysRating() {
		return this.MoodysRating;
	}
	/** 設定Moody’s等級 **/
	public void setMoodysRating(String value) {
		this.MoodysRating = value;
	}

	/** 取得Fitch等級 **/
	public String getFitchRating() {
		return this.FitchRating;
	}
	/** 設定Fitch等級 **/
	public void setFitchRating(String value) {
		this.FitchRating = value;
	}

	/** 取得中華信評等級 **/
	public String getTwRating() {
		return this.twRating;
	}
	/** 設定中華信評等級 **/
	public void setTwRating(String value) {
		this.twRating = value;
	}

	/** 取得FitchTW等級 **/
	public String getFitchTWRating() {
		return this.FitchTWRating;
	}
	/** 設定FitchTW等級 **/
	public void setFitchTWRating(String value) {
		this.FitchTWRating = value;
	}
	
	/** 取得中央政府風險權數 **/
	public BigDecimal getCentralGov() {
		return this.centralGov;
	}
	/** 設定中央政府風險權數 **/
	public void setCentralGov(BigDecimal value) {
		this.centralGov = value;
	}

	/** 取得地方政府及非營利國營事業風險權數 **/
	public BigDecimal getLocalGov() {
		return this.localGov;
	}
	/** 設定地方政府及非營利國營事業風險權數 **/
	public void setLocalGov(BigDecimal value) {
		this.localGov = value;
	}

	/** 取得銀行債權風險權數 **/
	public BigDecimal getBankBond() {
		return this.bankBond;
	}
	/** 設定銀行債權風險權數 **/
	public void setBankBond(BigDecimal value) {
		this.bankBond = value;
	}

	/** 取得企業債權風險權數 **/
	public BigDecimal getEnterBond() {
		return this.enterBond;
	}
	/** 設定企業債權風險權數 **/
	public void setEnterBond(BigDecimal value) {
		this.enterBond = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	/** 
	 * 取得版本日期<p/>
	 * 第一版為20220812
	 */
	public String getVersionDate() {
		return this.versionDate;
	}
	/**
	 *  設定版本日期<p/>
	 *  第一版為20220812
	 **/
	public void setVersionDate(String value) {
		this.versionDate = value;
	}
}
