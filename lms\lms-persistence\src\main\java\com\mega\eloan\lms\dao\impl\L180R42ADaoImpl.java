/* 
 * L180R42ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L180R42ADao;
import com.mega.eloan.lms.model.L180R42A;

/** 企金新核准往來客戶月檔 **/
@Repository
public class L180R42ADaoImpl extends LMSJpaDao<L180R42A, String> implements
		L180R42ADao {

	@Override
	public L180R42A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180R42A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L180R42A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L180R42A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L180R42A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R42A> findByIndex02(Date endDate) {
		ISearch search = createSearchTemplete();
		List<L180R42A> list = null;
		if (endDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "endDate",
					endDate);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R42A> findByIndex03(String caseBrId, Date endDate) {
		ISearch search = createSearchTemplete();
		List<L180R42A> list = null;
		if (caseBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					caseBrId);
		if (endDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "endDate",
					endDate);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R42A> findByIndex04(String cntrBrId, Date endDate) {
		ISearch search = createSearchTemplete();
		List<L180R42A> list = null;
		if (cntrBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrBrId",
					cntrBrId);
		if (endDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "endDate",
					endDate);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R42A> findByIndex05(String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<L180R42A> list = null;
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R42A> findByIndex06(String cntrMainId) {
		ISearch search = createSearchTemplete();
		List<L180R42A> list = null;
		if (cntrMainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrMainId",
					cntrMainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	public L180R42A findByMainIdAndCntrMainId(String mainId, String cntrMainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrMainId",
				cntrMainId);
		return findUniqueOrNone(search);
	}

	@Override
	public L180R42A findByCustIdExcludeSelfRptMainId(String rptMainId,
			String custId, String dupNo, String bgnDate, String endDate) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "mainId",
				rptMainId);

		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		String[] reasonStr = { bgnDate, endDate };
		search.addSearchModeParameters(SearchMode.BETWEEN, "endDate", reasonStr);

		search.addOrderBy("endDate");

		return findUniqueOrNone(search);
	}

}