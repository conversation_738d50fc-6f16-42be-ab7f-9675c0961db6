$(function() {
	setCloseConfirm(true);
	gridOther();
});

function cesGridOther(){
	   $("#gridOther").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIds2",
			rowNum:10
		},
		search: true
	   }).trigger("reloadGrid");
	}

function gridOther(){
    var gridbox1 = $("#gridOther").iGrid({
		handler : 'lms1201gridhandler',
		height : 175,
		sortname : 'createTime',
		postData : {
			formAction : "queryCesMainIds2",
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers:true,
		rowNum:10,
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lms1201s06["L120M01D.grid2"], //建立日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'createTime' //col.id
		}, {
			colHeader : i18n.lms1201s06["L120M01D.grid4"], //核准日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'approveTime' //col.id
		}, {
			colHeader : i18n.lms1201s06["L120M01D.grid3"], //文件狀態
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'docStatus' //col.id
		}, {
			colHeader : i18n.lms1201s06["L120M01D.grid1"], //主要借款人
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		}],
		ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
    });
}

function openOther(){
	cesGridOther();
	$("#openOther").thickbox({     // 使用選取的內容進行彈窗
		title : i18n.lms1201s06["L120M01D.thickbox1"],
		width : 640,
		height : 350,
		align : 'center',
		valign: 'bottom',
		modal : true,
		i18n:i18n.def,
		buttons: {
			"sure": function() {
				var row = $("#gridOther").getGridParam('selrow'); 
				var list = "";
				var data = $("#gridOther").getRowData(row);
				list = data.mainId;
				list = (list == undefined ? "" : list);
				if(list != ""){
					$.ajax({ // 查詢主要借款人資料
						handler : "lms1201formhandler",
						type : "POST",
						dataType : "json",
						data : {
							formAction : "findOther",
							mainId : responseJSON.mainId,
							cesMainId : list,
							custName : data.custName
						}
					}).done(function(json) {
						// alert(JSON.stringify(json));
						setCkeditor2("itemDscr03",json.itemDscr03);
						//$("#itemDscr03").val(json.itemDscr03);
					});
				}
				$.thickbox.close();
			},            
			"close": function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}



function applyEquatorPrinciples(){

    var $LMS1205S07Form05 = $("#LMS1205S07Form05");
	
    $.ajax({ // 查詢主要借款人資料
		handler : "lms1201formhandler",
		type : "POST",
		dataType : "json",
		data : {
			formAction : "applyEquatorPrinciples",
			mainId : responseJSON.mainId,
			LMS1205S07Form05: JSON.stringify($LMS1205S07Form05.serializeData())
		}
	}).done(function(json) {
		var newstr = json.equatorPrinciples;
		setCkeditor2("itemDscr03",newstr+$("#itemDscr03").val());
	});
}
