/* 
 *VL015M01A01.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/**
 * <pre>
 * 待辦事項view
 * </pre>
 * 
 * @since 2012/1/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/18,REX,new
 *          </ul>
 */
@Entity
@Table(name = "VL015M01A01")
public class VL015M01A01 extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	/** 文件編號 */
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 被授權分行 */
	@Column(name = "OWNBRID", length = 32, columnDefinition = "CHAR(32)")
	private String ownBrid;
	
	

	/** 建立日期 */
	private Timestamp createTime;

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public String getOwnBrid() {
		return ownBrid;
	}

	public void setOwnBrid(String ownBrid) {
		this.ownBrid = ownBrid;
	}

	/**
	 * 簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	public Date getCaseDate() {
		return caseDate;
	}

	public void setCaseDate(Date caseDate) {
		this.caseDate = caseDate;
	}

	/**
	 * 授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 */
	@Column(name = "DOCKIND", length = 1, columnDefinition = "CHAR(1)")
	private String docKind;

	/**
	 * 案件別
	 * <p/>
	 * 1一般<br/>
	 * 2其他<br/>
	 * 3陳復/陳述案<br/>
	 * 4異常通報
	 */
	@Column(name = "DOCCODE", length = 1, columnDefinition = "CHAR(1)")
	private String docCode;

	/**
	 * 三個月以上協議案註記(0:無,1:授管處三個月以內,2:債管處三個月以上)
	 * **/
	@Column(name = "NGFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String ngFlag;
	
	/** 簽案分行 */
	@Column(name = "CASEBRID", length = 32, columnDefinition = "CHAR(3)")
	private String caseBrId;
	
	/**
	 * 是否加送會審單位
	 * <p/>
	 * ※docKind=授權外<br/>
	 * 1無2送會簽3送審查
	 */
	@Column(name = "AREACHK", length = 1, columnDefinition = "VARCHAR(1)")
	private String areaChk;
	

	public String getDocKind() {
		return docKind;
	}

	public void setDocKind(String docKind) {
		this.docKind = docKind;
	}

	public String getDocCode() {
		return docCode;
	}

	public void setDocCode(String docCode) {
		this.docCode = docCode;
	}

	/** 統一編號 */
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 */
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 客戶名稱 */
	// G(38)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/** 目前文件狀態 */
	@Column(name = "DOCSTATUS", length = 3, columnDefinition = "CHAR(3)")
	private String docStatus;

	/** 文件url */
	@Column(name = "DOCURL", length = 40, columnDefinition = "VARCHAR(40)")
	private String docURL;

	/** 交易代碼 */
	@Column(name = "TXCODE", length = 6, columnDefinition = "char(6)")
	private String txCode;

	/** 建立人員 */
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 核准人員 */
	@Column(name = "APPROVER", length = 6, columnDefinition = "CHAR(6)")
	private String approver;

	/** 案件號碼 */
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/** 用來顯示文件的名稱 */
	@Column(name = "TYPESHOW", length = 62, columnDefinition = "VARCHAR(62)")
	private String typeShow;

	/**
	 * 企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String docType;

	public String getDocType() {
		return docType;
	}

	public void setDocType(String docType) {
		this.docType = docType;
	}

	/** 取得用來顯示文件的名稱 */
	public String getTypeShow() {
		return typeShow;
	}

	/** 設定用來顯示文件的名稱 */
	public void setTypeShow(String typeShow) {
		this.typeShow = typeShow;
	}

	/** 取得案件號碼 */
	public String getCaseNo() {
		return caseNo;
	}

	/** 設定案件號碼 */
	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getDupNo() {
		return dupNo;
	}

	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getDocStatus() {
		return docStatus;
	}

	public void setDocStatus(String docStatus) {
		this.docStatus = docStatus;
	}

	@SuppressWarnings("rawtypes")
	public void setDocStatus(Enum docStatusEnum) {
		this.docStatus = docStatusEnum.toString();
	}

	public String getDocURL() {
		return docURL;
	}

	public void setDocURL(String docURL) {
		this.docURL = docURL;
	}

	public String getTxCode() {
		return txCode;
	}

	public void setTxCode(String txCode) {
		this.txCode = txCode;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}

	public String getApprover() {
		return approver;
	}

	public void setApprover(String approver) {
		this.approver = approver;
	}

	/**
	 * 
	 * type 文件種類
	 * 
	 * <pre>
	 * L120M01A=案件簽報書
	 * L141M01A=聯行額度明細表
	 * L160M01A=動用審核表
	 * </pre>
	 */
	@Column(length = 32, columnDefinition = "CHAR(32)")
	private String type;

	/**
	 * 
	 * type 取得文件種類
	 * 
	 * <pre>
	 * L120M01A=案件簽報書
	 * L141M01A=聯行額度明細表
	 * L160M01A=動用審核表
	 * </pre>
	 */
	public String getType() {
		return type;
	}

	/**
	 * 
	 * type 設定文件種類
	 * 
	 * <pre>
	 * L120M01A=案件簽報書
	 * L141M01A=聯行額度明細表
	 * L160M01A=動用審核表
	 * 
	 * </pre>
	 */
	public void setType(String type) {
		this.type = type;
	}

	/**
	 * get the ngFlag
	 * 
	 * @return the ngFlag
	 */
	public String getNgFlag() {
		return ngFlag;
	}

	/**
	 * set the ngFlag
	 * 
	 * @param ngFlag
	 *            the ngFlag to set
	 */
	public void setNgFlag(String ngFlag) {
		this.ngFlag = ngFlag;
	}

	public void setCaseBrId(String caseBrId) {
		this.caseBrId = caseBrId;
	}

	public String getCaseBrId() {
		return caseBrId;
	}
	
	
	/**
	 * 取得是否加送會審單位
	 * <p/>
	 * ※docKind=授權外<br/>
	 * 1無2送會簽3送審查
	 */
	public String getAreaChk() {
		return this.areaChk;
	}

	/**
	 * 設定是否加送會審單位
	 * <p/>
	 * ※docKind=授權外<br/>
	 * 1無2送會簽3送審查
	 **/
	public void setAreaChk(String value) {
		this.areaChk = value;
	}
	

}
