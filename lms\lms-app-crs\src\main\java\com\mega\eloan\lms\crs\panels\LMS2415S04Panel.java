/* 
 * LMS2415S04Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * [個金]覆審報告表  覆審意見
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/29,jessica,new
 *          </ul>
 */
public class LMS2415S04Panel extends Panel {

	public LMS2415S04Panel(String id) {
		super(id);

	}
	
	public LMS2415S04Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;

}
