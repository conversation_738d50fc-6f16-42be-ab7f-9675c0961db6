<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
		<div id="thickboxaddborrow" style="display: none;">
			<form id="addborrowForm">				
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td width="20%" class="hd1">
							<input type="radio" name="rborrowA" checked="checked" value="1" onclick="$('._rborrowa').prop('disabled',false);$('._rborrowb').val('').prop('disabled',true);"/>
							<th:block th:text="#{'l120s01a.custid'}">身分證統編</th:block>&nbsp;&nbsp;						
						</td>
						<td width="80%">
							<input type="text" id="addborrowForm_custId" name="addborrowForm_custId" class="max upText _rborrowa" size="10" maxlength="10" />
							&nbsp;&nbsp; 
							<input type="hidden" id="addborrowForm_dupNo" name="addborrowForm_dupNo" value="">
							<button type="button" id="getCustData" name="getCustData">
								<span class="text-only"><th:block th:text="#{'tab02.btnImport'}">引進</th:block></span>
							</button>
						</td>
					</tr>
					<tr>
						<td width="20%" class="hd1">
							<input type="radio" name="rborrowA" value="2" onclick="$('._rborrowa').val('').prop('disabled',true);$('._rborrowb').prop('disabled',false);"/>
							<th:block th:text="#{'l120s01a.custname'}">借款人姓名</th:block>&nbsp;&nbsp;
						</td>
						<td width="80%">
							<input type="text" id="addborrowForm_custName" name="addborrowForm_custName" class="max _rborrowb" maxlength="120" disabled="true" /> &nbsp;&nbsp;							
						</td>
					</tr>
					<tr>
						<td colspan="2" class="text-red">
							<th:block th:utext="#{'l120s01a.other16'}">說明...</th:block>							
						</td>
					</tr>
				</table>
			</form>
		</div>
	</th:block>
</body>
</html>
