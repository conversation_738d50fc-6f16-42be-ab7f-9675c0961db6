package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.C122M01DDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C122M01D;


@Repository
public class C122M01DDaoImpl extends LMSJpaDao<C122M01D, String>
	implements C122M01DDao {

	@Override
	public C122M01D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C122M01D findByMainIdSeq(String mainId, int seq){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C122M01D> findByMainIdOrderBySeqAsc(String mainId){
		return findByMainIdOrderBySeqAssign(mainId, "asc");
	}
		
	private List<C122M01D> findByMainIdOrderBySeqAssign(String mainId, String ordStr){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		if(Util.equals(ordStr, "asc")){
			search.addOrderBy("seq", false);
		}else if(Util.equals(ordStr, "desc")){
			search.addOrderBy("seq", true);
		}
		List<C122M01D> list = createQuery(search).getResultList();
		return list;
	}
}