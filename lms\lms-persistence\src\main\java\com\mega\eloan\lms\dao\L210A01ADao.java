/* 
 * L210A01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L210A01A;

/** 異動聯貸案參貸比率授權檔 **/
public interface L210A01ADao extends IGenericDao<L210A01A> {

	L210A01A findByOid(String oid);
	
	List<L210A01A> findByMainId(String mainId);
	
	L210A01A findByUniqueKey(String mainId, String ownUnit, String authType, String authUnit);

	List<L210A01A> findByIndex01(String mainId, String ownUnit, String authType, String authUnit);
}