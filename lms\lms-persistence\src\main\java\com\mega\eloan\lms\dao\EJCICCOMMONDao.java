/* 
 * EJCICCOMMONDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.EJCICCOMMON;

/** 個金歡喜信貸徵審初審及iLoan EJ查詢檔 **/
public interface EJCICCOMMONDao extends IGenericDao<EJCICCOMMON> {

	public EJCICCOMMON findByOid(String oid);
	
	public List<EJCICCOMMON> findByMainId(String mainId);

	public EJCICCOMMON findBy(String custId, String dupNo, String txid, String prodid, String qBranch, String qDate, String qEmpCode, String qFuncSrc);

	public List<EJCICCOMMON> findBy(String mainId, String custId, String dupNo, String prodId, String txId);

	public List<EJCICCOMMON> findByInquiryRecord(String custId, String prodId, String txId, String qDate, String qFuncSrc);

	public List<EJCICCOMMON> findByJcicKey(String custId, String prodId, String txId, String qDate);
}