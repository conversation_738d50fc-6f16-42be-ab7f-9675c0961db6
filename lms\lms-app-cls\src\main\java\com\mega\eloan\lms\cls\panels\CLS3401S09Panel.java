
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 消金契約書
 * </pre>
 * 
 * @since 2020/02/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/02/14,EL08034,new
 *          </ul>
 */
public class CLS3401S09Panel extends Panel { //仿 CLSS10APanel 簽報書「附加檔案」

	public CLS3401S09Panel(String id) {
		super(id);
	}

	public CLS3401S09Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;
}
