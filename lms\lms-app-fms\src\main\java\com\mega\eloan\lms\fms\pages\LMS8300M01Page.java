package com.mega.eloan.lms.fms.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;


/**
 * <pre>
 * AO帳務管理原維護 - 編製中
 * </pre>
 * 
 * @since 2018
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller@RequestMapping(path = "/fms/lms8300m01")
public class LMS8300M01Page extends AbstractEloanInnerView {

	public LMS8300M01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);

		// 加上Button
		List<EloanPageFragment> btns = new ArrayList<>();
//		// 主管跟經辦都會出現的按鈕
//
//		// 只有主管出現的按鈕
//		if (this.getAuth(AuthType.Accept)) {
//			btns.add(CreditButtonEnum.View);
//		}
//		// 只有經辦出現的按鈕
//		if (this.getAuth(AuthType.Modify)) {
//			btns.add(CreditButtonEnum.SingleMaintain);
//			btns.add(CreditButtonEnum.BatchMaintain);
//			// btns.add(CreditButtonEnum.Modify);
//			btns.add(CreditButtonEnum.Delete);
//		}
		addToButtonPanel(model,	btns);

//		setNeedHtml(true); // need html
//
//		add(new CLS1131S01Panel("CLS1131S01",params.getString("mainId"))); // add panel
		
		
		renderJsI18N(LMS8300M01Page.class);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS8300M01Page.js" };
	}

}