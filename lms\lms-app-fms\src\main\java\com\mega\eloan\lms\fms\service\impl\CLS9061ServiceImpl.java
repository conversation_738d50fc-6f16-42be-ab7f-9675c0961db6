package com.mega.eloan.lms.fms.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.dao.C900M01EDao;
import com.mega.eloan.lms.fms.service.CLS9061Service;
import com.mega.eloan.lms.model.C900M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

@Service
public class CLS9061ServiceImpl extends AbstractCapService implements
		CLS9061Service {
	private static final Logger logger = LoggerFactory
			.getLogger(CLS9061ServiceImpl.class);
	
	@Resource
	C900M01EDao c900m01eDao;

	@Resource
	TempDataService tempDataService;
	
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C900M01E) {
					C900M01E c900m01e = (C900M01E) model;
					if (Util.isNotEmpty(c900m01e.getOid()) ) {
						c900m01e.setUpdater(user.getUserId());
						c900m01e.setUpdateTime(CapDate.getCurrentTimestamp());
					}
					c900m01eDao.save(c900m01e);
					
					if (Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(c900m01e.getMainId());
					}
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C900M01E.class) {
			return c900m01eDao.findPage(search);
		}
		return null;
	}

	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C900M01E.class) {
			return (T) c900m01eDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public C900M01E findC900M01E_oid(String oid){
		return c900m01eDao.findByOid(oid);
	}
	
}
