package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 評分變量紀錄 **/
public class OTS_RKSCOREOVS extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;

	/** NOTES文件編號 **/
	@Column(name="NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String noteid;

	/** 
	 * 評等日期<p/>
	 * 最終評等日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="RATING_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date rating_date;

	/** 評等文件編號 **/
	@Column(name="RATING_ID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String rating_id;

	/** 客戶統一編號 **/
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String custid;

	/** 重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String dupno;

	/** 主借款人統一編號 **/
	@Column(name="CUST_KEY", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String cust_key;

	/** 
	 * 授信科目<p/>
	 * 3-4碼授信科目
	 */
	@Column(name="LOAN_CODE", length=4, columnDefinition="VARCHAR(4)", nullable=false,unique = true)
	private String loan_code;

	/** 
	 * 評等模型類別(c121m01a.mowType)
	 */
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String mowtype;
	
	/** 
	 * 房貸/非房貸註記(l141m01c.modelType)
	 * N=非房貸、M=房貸
	 */
	@Column(name="MOWTYPE2", length=1, columnDefinition="CHAR(1)")
	private String mowtype2;
	
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	@Column(name="MOWTYPE_COUNTRY", length=2, columnDefinition="CHAR(2)", nullable=false,unique = true)
	private String mowtype_country;

	/** 模型版本-大版 **/
	@Column(name="MOWVER1", columnDefinition="DEC(5,0)", nullable=false,unique = true)
	private Integer mowver1;

	/** 模型版本-小版 **/
	@Column(name="MOWVER2", columnDefinition="DEC(5,0)", nullable=false,unique = true)
	private Integer mowver2;

	/** 
	 * 科目<p/>
	 * 8碼會計科目
	 */
	@Column(name="SUBJCODE", length=8, columnDefinition="VARCHAR(8)")
	private String subjcode;

	/** 相關身分 **/
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String lngeflag;

	/** 文件狀態 **/
	@Column(name="DOCSTATUS", length=2, columnDefinition="CHAR(2)")
	private String docstatus;

	/** 金額單位 **/
	@Column(name="AMTUNIT", columnDefinition="DEC(8,0)")
	private BigDecimal amtunit;

	/** 初始評等日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="PR_DATE", columnDefinition="Date")
	private Date pr_date;

	/** M1_AGE因子值 **/
	@Column(name="M1_AGE", columnDefinition="DEC(15,5)")
	private BigDecimal m1_age;

	/** M5_OCCUPATION因子值 **/
	@Column(name="M5_OCCUPATION", columnDefinition="DEC(15,5)")
	private BigDecimal m5_occupation;

	/** M7_SENIORITY因子值 **/
	@Column(name="M7_SENIORITY", columnDefinition="DEC(15,5)")
	private BigDecimal m7_seniority;

	/** D1_ICR因子值 **/
	@Column(name="D1_ICR", columnDefinition="DEC(15,5)")
	private BigDecimal d1_icr;

	/** 
	 * D1_ICR因子值NA情況判斷<p/>
	 * 檢核D1_ICR，若填寫"利息保障倍數ICR"=0、勾選"借款目的為開立保證函"=1、"未進行ICR分析"=2
	 */
	@Column(name="D1_ICR_NA", length=1, columnDefinition="VARCHAR(1)")
	private String d1_icr_na;

	/** P2_PINCOME因子值 **/
	@Column(name="P2_PINCOME", columnDefinition="DEC(15,5)")
	private BigDecimal p2_pincome;

	/** P3_HINCOME因子值 **/
	@Column(name="P3_HINCOME", columnDefinition="DEC(15,5)")
	private BigDecimal p3_hincome;

	/** A5_LOAN_PERIOD因子值 **/
	@Column(name="A5_LOAN_PERIOD", columnDefinition="DEC(15,5)")
	private BigDecimal a5_loan_period;

	/** O1_VEDASCORE因子值 **/
	@Column(name="O1_VEDASCORE", columnDefinition="DEC(15,5)")
	private BigDecimal o1_vedascore;

	/** Z1因子值 **/
	@Column(name="Z1", columnDefinition="DEC(15,5)")
	private BigDecimal z1;

	/** Z2因子值 **/
	@Column(name="Z2", columnDefinition="DEC(15,5)")
	private BigDecimal z2;

	/** YPAY轉換匯率 **/
	@Column(name="YPAY_EX_RATE", columnDefinition="DEC(9,5)")
	private BigDecimal ypay_ex_rate;

	/** OMONEY轉換匯率 **/
	@Column(name="OMONEY_AMT_EX_RATE", columnDefinition="DEC(9,5)")
	private BigDecimal omoney_amt_ex_rate;

	/** HINCOME轉換匯率 **/
	@Column(name="HINCOME_EX_RATE", columnDefinition="DEC(9,5)")
	private BigDecimal hincome_ex_rate;

	/** M1之0~100分數 **/
	@Column(name="M1_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal m1_score;

	/** M5之0~100分數 **/
	@Column(name="M5_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal m5_score;

	/** M7之0~100分數 **/
	@Column(name="M7_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal m7_score;

	/** D1之0~100分數 **/
	@Column(name="D1_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal d1_score;

	/** P2之0~100分數 **/
	@Column(name="P2_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal p2_score;

	/** P3之0~100分數 **/
	@Column(name="P3_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal p3_score;

	/** A5之0~100分數 **/
	@Column(name="A5_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal a5_score;

	/** O1之0~100分數 **/
	@Column(name="O1_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal o1_score;

	/** Z1之0~100分數 **/
	@Column(name="Z1_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal z1_score;

	/** Z2之0~100分數 **/
	@Column(name="Z2_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal z2_score;

	/** M1標準化分數 **/
	@Column(name="M1_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal m1_std_score;

	/** M5標準化分數 **/
	@Column(name="M5_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal m5_std_score;

	/** M7標準化分數 **/
	@Column(name="M7_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal m7_std_score;

	/** D1標準化分數 **/
	@Column(name="D1_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal d1_std_score;

	/** P2標準化分數 **/
	@Column(name="P2_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal p2_std_score;

	/** P3標準化分數 **/
	@Column(name="P3_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal p3_std_score;

	/** A5標準化分數 **/
	@Column(name="A5_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal a5_std_score;

	/** O1標準化分數 **/
	@Column(name="O1_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal o1_std_score;

	/** Z1標準化分數 **/
	@Column(name="Z1_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal z1_std_score;

	/** Z2標準化分數 **/
	@Column(name="Z2_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal z2_std_score;

	/** 核心模型標準化分數 **/
	@Column(name="CORE_STDSCORE", columnDefinition="DEC(15,5)")
	private BigDecimal core_stdscore;

	/** 上傳資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATA_SRC_DT", columnDefinition="DATE")
	private Date data_src_dt;
	
	/** 借款人及連保人之年收入合計_因子值 **/
	@Column(name="P3_TH_TOTINCOME", columnDefinition="DEC(15,5)")
	private BigDecimal p3_th_totincome;

	/** 個人負債_因子值 **/
	@Column(name="P4_TH_DRATE", columnDefinition="DEC(15,5)")
	private BigDecimal p4_th_drate;

	/** 擔保率_因子值 **/
	@Column(name="Z3_TH_SECURITY_RATIO", columnDefinition="DEC(15,5)")
	private BigDecimal z3_th_security_ratio;

	/** P3之0~100分數 **/
	@Column(name="P3_TH_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal p3_th_score;

	/** P4之0~100分數 **/
	@Column(name="P4_TH_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal p4_th_score;

	/** Z3之0~100分數 **/
	@Column(name="Z3_TH_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal z3_th_score;

	/** P3標準化分數 **/
	@Column(name="P3_TH_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal p3_th_std_score;

	/** P4標準化分數 **/
	@Column(name="P4_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal p4_std_score;

	/** Z3標準化分數 **/
	@Column(name="Z3_TH_STD_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal z3_th_std_score;
	
	/**--------日本模型 2.0新增欄位  Start--------**/
	/** 學歷(因子) **/
	@Column(name="EDU_ITEM", columnDefinition="VARCHAR(4)")
	private String edu_item;
	
	/** 學歷(原始分數) **/
	@Column(name="EDU_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal edu_score;
	
	/** 學歷(權重分數) **/
	@Column(name="EDU_WEIGHT_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal edu_weight_score;
	
	/** 個人負債比(權重分數) **/
	@Column(name="DRATE_WEIGHT_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal drate_weight_score;
	
	/** 年齡(權重分數) **/
	@Column(name="M1_WEIGHT_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal m1_weight_score;
	
	/** 年資(權重分數) **/
	@Column(name="M7_WEIGHT_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal m7_weight_score;
	
	/** 契約年限(權重分數) **/
	@Column(name="A5_WEIGHT_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal a5_weight_score;
	
	/** 職業(權重分數) **/
	@Column(name="M5_WEIGHT_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal m5_weight_score;
	
	/** 年收入(權重分數) **/
	@Column(name="P2_WEIGHT_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal p2_weight_score;

	/** 擔保品種類(權重分數) **/
	@Column(name="Z1_WEIGHT_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal z1_weight_score;
	
	/** 市場環境及變現性(權重分數) **/
	@Column(name="Z2_WEIGHT_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal z2_weight_score;
	/**--------日本模型 2.0新增欄位  End--------**/
	
	/**--------澳洲模型 3.0新增欄位  Start--------**/
	/** P3_HINCOME幣別 **/
	@Column(name="P3_HINCOME_CURR", length=1, columnDefinition="VARCHAR(3)")
	private String p3_hincome_curr;
	
	@Column(name="P3_WEIGHT_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal p3_weight_score;
	/**--------澳洲模型 3.0新增欄位  End--------**/
	
	/** 取得分行別 **/
	public String getBr_cd() {
		return this.br_cd;
	}
	/** 設定分行別 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}

	/** 取得NOTES文件編號 **/
	public String getNoteid() {
		return this.noteid;
	}
	/** 設定NOTES文件編號 **/
	public void setNoteid(String value) {
		this.noteid = value;
	}

	/** 
	 * 取得評等日期<p/>
	 * 最終評等日
	 */
	public Date getRating_date() {
		return this.rating_date;
	}
	/**
	 *  設定評等日期<p/>
	 *  最終評等日
	 **/
	public void setRating_date(Date value) {
		this.rating_date = value;
	}

	/** 取得評等文件編號 **/
	public String getRating_id() {
		return this.rating_id;
	}
	/** 設定評等文件編號 **/
	public void setRating_id(String value) {
		this.rating_id = value;
	}

	/** 取得客戶統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定客戶統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得主借款人統一編號 **/
	public String getCust_key() {
		return this.cust_key;
	}
	/** 設定主借款人統一編號 **/
	public void setCust_key(String value) {
		this.cust_key = value;
	}

	/** 
	 * 取得授信科目<p/>
	 * 3-4碼授信科目
	 */
	public String getLoan_code() {
		return this.loan_code;
	}
	/**
	 *  設定授信科目<p/>
	 *  3-4碼授信科目
	 **/
	public void setLoan_code(String value) {
		this.loan_code = value;
	}

	/** 
	 * 取得評等模型類別(c121m01a.mowType)
	 */
	public String getMowtype() {
		return this.mowtype;
	}
	/**
	 *  設定評等模型類別(c121m01a.mowType)
	 **/
	public void setMowtype(String value) {
		this.mowtype = value;
	}
	
	/** 
	 * 取得房貸/非房貸註記(l141m01c.modelType)
	 * N=非房貸、M=房貸
	 */
	public String getMowtype2() {
		return this.mowtype2;
	}
	/**
	 *  設定房貸/非房貸註記(l141m01c.modelType)
	 *  N=非房貸、M=房貸
	 **/
	public void setMowtype2(String value) {
		this.mowtype2 = value;
	}
	
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	public String getMowtype_country() {
		return this.mowtype_country;
	}
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	public void setMowtype_country(String value) {
		this.mowtype_country = value;
	}

	/** 取得模型版本-大版 **/
	public Integer getMowver1() {
		return this.mowver1;
	}
	/** 設定模型版本-大版 **/
	public void setMowver1(Integer value) {
		this.mowver1 = value;
	}

	/** 取得模型版本-小版 **/
	public Integer getMowver2() {
		return this.mowver2;
	}
	/** 設定模型版本-小版 **/
	public void setMowver2(Integer value) {
		this.mowver2 = value;
	}

	/** 
	 * 取得科目<p/>
	 * 8碼會計科目
	 */
	public String getSubjcode() {
		return this.subjcode;
	}
	/**
	 *  設定科目<p/>
	 *  8碼會計科目
	 **/
	public void setSubjcode(String value) {
		this.subjcode = value;
	}

	/** 取得相關身分 **/
	public String getLngeflag() {
		return this.lngeflag;
	}
	/** 設定相關身分 **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 取得文件狀態 **/
	public String getDocstatus() {
		return this.docstatus;
	}
	/** 設定文件狀態 **/
	public void setDocstatus(String value) {
		this.docstatus = value;
	}

	/** 取得金額單位 **/
	public BigDecimal getAmtunit() {
		return this.amtunit;
	}
	/** 設定金額單位 **/
	public void setAmtunit(BigDecimal value) {
		this.amtunit = value;
	}

	/** 取得初始評等日期 **/
	public Date getPr_date() {
		return this.pr_date;
	}
	/** 設定初始評等日期 **/
	public void setPr_date(Date value) {
		this.pr_date = value;
	}

	/** 取得M1_AGE因子值 **/
	public BigDecimal getM1_age() {
		return this.m1_age;
	}
	/** 設定M1_AGE因子值 **/
	public void setM1_age(BigDecimal value) {
		this.m1_age = value;
	}

	/** 取得M5_OCCUPATION因子值 **/
	public BigDecimal getM5_occupation() {
		return this.m5_occupation;
	}
	/** 設定M5_OCCUPATION因子值 **/
	public void setM5_occupation(BigDecimal value) {
		this.m5_occupation = value;
	}

	/** 取得M7_SENIORITY因子值 **/
	public BigDecimal getM7_seniority() {
		return this.m7_seniority;
	}
	/** 設定M7_SENIORITY因子值 **/
	public void setM7_seniority(BigDecimal value) {
		this.m7_seniority = value;
	}

	/** 取得D1_ICR因子值 **/
	public BigDecimal getD1_icr() {
		return this.d1_icr;
	}
	/** 設定D1_ICR因子值 **/
	public void setD1_icr(BigDecimal value) {
		this.d1_icr = value;
	}

	/** 
	 * 取得D1_ICR因子值NA情況判斷<p/>
	 * 檢核D1_ICR，若填寫"利息保障倍數ICR"=0、勾選"借款目的為開立保證函"=1、"未進行ICR分析"=2
	 */
	public String getD1_icr_na() {
		return this.d1_icr_na;
	}
	/**
	 *  設定D1_ICR因子值NA情況判斷<p/>
	 *  檢核D1_ICR，若填寫"利息保障倍數ICR"=0、勾選"借款目的為開立保證函"=1、"未進行ICR分析"=2
	 **/
	public void setD1_icr_na(String value) {
		this.d1_icr_na = value;
	}

	/** 取得P2_PINCOME因子值 **/
	public BigDecimal getP2_pincome() {
		return this.p2_pincome;
	}
	/** 設定P2_PINCOME因子值 **/
	public void setP2_pincome(BigDecimal value) {
		this.p2_pincome = value;
	}

	/** 取得P3_HINCOME因子值 **/
	public BigDecimal getP3_hincome() {
		return this.p3_hincome;
	}
	/** 設定P3_HINCOME因子值 **/
	public void setP3_hincome(BigDecimal value) {
		this.p3_hincome = value;
	}
	
	/** 取得A5_LOAN_PERIOD因子值 **/
	public BigDecimal getA5_loan_period() {
		return this.a5_loan_period;
	}
	/** 設定A5_LOAN_PERIOD因子值 **/
	public void setA5_loan_period(BigDecimal value) {
		this.a5_loan_period = value;
	}

	/** 取得O1_VEDASCORE因子值 **/
	public BigDecimal getO1_vedascore() {
		return this.o1_vedascore;
	}
	/** 設定O1_VEDASCORE因子值 **/
	public void setO1_vedascore(BigDecimal value) {
		this.o1_vedascore = value;
	}

	/** 取得Z1因子值 **/
	public BigDecimal getZ1() {
		return this.z1;
	}
	/** 設定Z1因子值 **/
	public void setZ1(BigDecimal value) {
		this.z1 = value;
	}

	/** 取得Z2因子值 **/
	public BigDecimal getZ2() {
		return this.z2;
	}
	/** 設定Z2因子值 **/
	public void setZ2(BigDecimal value) {
		this.z2 = value;
	}

	/** 取得YPAY轉換匯率 **/
	public BigDecimal getYpay_ex_rate() {
		return this.ypay_ex_rate;
	}
	/** 設定YPAY轉換匯率 **/
	public void setYpay_ex_rate(BigDecimal value) {
		this.ypay_ex_rate = value;
	}

	/** 取得OMONEY轉換匯率 **/
	public BigDecimal getOmoney_amt_ex_rate() {
		return this.omoney_amt_ex_rate;
	}
	/** 設定OMONEY轉換匯率 **/
	public void setOmoney_amt_ex_rate(BigDecimal value) {
		this.omoney_amt_ex_rate = value;
	}

	/** 取得HINCOME轉換匯率 **/
	public BigDecimal getHincome_ex_rate() {
		return this.hincome_ex_rate;
	}
	/** 設定HINCOME轉換匯率 **/
	public void setHincome_ex_rate(BigDecimal value) {
		this.hincome_ex_rate = value;
	}

	/** 取得M1之0~100分數 **/
	public BigDecimal getM1_score() {
		return this.m1_score;
	}
	/** 設定M1之0~100分數 **/
	public void setM1_score(BigDecimal value) {
		this.m1_score = value;
	}

	/** 取得M5之0~100分數 **/
	public BigDecimal getM5_score() {
		return this.m5_score;
	}
	/** 設定M5之0~100分數 **/
	public void setM5_score(BigDecimal value) {
		this.m5_score = value;
	}

	/** 取得M7之0~100分數 **/
	public BigDecimal getM7_score() {
		return this.m7_score;
	}
	/** 設定M7之0~100分數 **/
	public void setM7_score(BigDecimal value) {
		this.m7_score = value;
	}

	/** 取得D1之0~100分數 **/
	public BigDecimal getD1_score() {
		return this.d1_score;
	}
	/** 設定D1之0~100分數 **/
	public void setD1_score(BigDecimal value) {
		this.d1_score = value;
	}

	/** 取得P2之0~100分數 **/
	public BigDecimal getP2_score() {
		return this.p2_score;
	}
	/** 設定P2之0~100分數 **/
	public void setP2_score(BigDecimal value) {
		this.p2_score = value;
	}

	/** 取得P3之0~100分數 **/
	public BigDecimal getP3_score() {
		return this.p3_score;
	}
	/** 設定P3之0~100分數 **/
	public void setP3_score(BigDecimal value) {
		this.p3_score = value;
	}

	/** 取得A5之0~100分數 **/
	public BigDecimal getA5_score() {
		return this.a5_score;
	}
	/** 設定A5之0~100分數 **/
	public void setA5_score(BigDecimal value) {
		this.a5_score = value;
	}

	/** 取得O1之0~100分數 **/
	public BigDecimal getO1_score() {
		return this.o1_score;
	}
	/** 設定O1之0~100分數 **/
	public void setO1_score(BigDecimal value) {
		this.o1_score = value;
	}

	/** 取得Z1之0~100分數 **/
	public BigDecimal getZ1_score() {
		return this.z1_score;
	}
	/** 設定Z1之0~100分數 **/
	public void setZ1_score(BigDecimal value) {
		this.z1_score = value;
	}

	/** 取得Z2之0~100分數 **/
	public BigDecimal getZ2_score() {
		return this.z2_score;
	}
	/** 設定Z2之0~100分數 **/
	public void setZ2_score(BigDecimal value) {
		this.z2_score = value;
	}

	/** 取得M1標準化分數 **/
	public BigDecimal getM1_std_score() {
		return this.m1_std_score;
	}
	/** 設定M1標準化分數 **/
	public void setM1_std_score(BigDecimal value) {
		this.m1_std_score = value;
	}

	/** 取得M5標準化分數 **/
	public BigDecimal getM5_std_score() {
		return this.m5_std_score;
	}
	/** 設定M5標準化分數 **/
	public void setM5_std_score(BigDecimal value) {
		this.m5_std_score = value;
	}

	/** 取得M7標準化分數 **/
	public BigDecimal getM7_std_score() {
		return this.m7_std_score;
	}
	/** 設定M7標準化分數 **/
	public void setM7_std_score(BigDecimal value) {
		this.m7_std_score = value;
	}

	/** 取得D1標準化分數 **/
	public BigDecimal getD1_std_score() {
		return this.d1_std_score;
	}
	/** 設定D1標準化分數 **/
	public void setD1_std_score(BigDecimal value) {
		this.d1_std_score = value;
	}

	/** 取得P2標準化分數 **/
	public BigDecimal getP2_std_score() {
		return this.p2_std_score;
	}
	/** 設定P2標準化分數 **/
	public void setP2_std_score(BigDecimal value) {
		this.p2_std_score = value;
	}

	/** 取得P3標準化分數 **/
	public BigDecimal getP3_std_score() {
		return this.p3_std_score;
	}
	/** 設定P3標準化分數 **/
	public void setP3_std_score(BigDecimal value) {
		this.p3_std_score = value;
	}

	/** 取得A5標準化分數 **/
	public BigDecimal getA5_std_score() {
		return this.a5_std_score;
	}
	/** 設定A5標準化分數 **/
	public void setA5_std_score(BigDecimal value) {
		this.a5_std_score = value;
	}

	/** 取得O1標準化分數 **/
	public BigDecimal getO1_std_score() {
		return this.o1_std_score;
	}
	/** 設定O1標準化分數 **/
	public void setO1_std_score(BigDecimal value) {
		this.o1_std_score = value;
	}

	/** 取得Z1標準化分數 **/
	public BigDecimal getZ1_std_score() {
		return this.z1_std_score;
	}
	/** 設定Z1標準化分數 **/
	public void setZ1_std_score(BigDecimal value) {
		this.z1_std_score = value;
	}

	/** 取得Z2標準化分數 **/
	public BigDecimal getZ2_std_score() {
		return this.z2_std_score;
	}
	/** 設定Z2標準化分數 **/
	public void setZ2_std_score(BigDecimal value) {
		this.z2_std_score = value;
	}

	/** 取得核心模型標準化分數 **/
	public BigDecimal getCore_stdscore() {
		return this.core_stdscore;
	}
	/** 設定核心模型標準化分數 **/
	public void setCore_stdscore(BigDecimal value) {
		this.core_stdscore = value;
	}

	/** 取得上傳資料日期 **/
	public Date getData_src_dt() {
		return this.data_src_dt;
	}
	/** 設定上傳資料日期 **/
	public void setData_src_dt(Date value) {
		this.data_src_dt = value;
	}
	
	/** 取得借款人及連保人之年收入合計_因子值 **/
	public BigDecimal getP3_th_totincome() {
		return p3_th_totincome;
	}
	/** 設定借款人及連保人之年收入合計_因子值 **/
	public void setP3_th_totincome(BigDecimal p3_th_totincome) {
		this.p3_th_totincome = p3_th_totincome;
	}
	
	/** 取得個人負債_因子值 **/	
	public BigDecimal getP4_th_drate() {
		return p4_th_drate;
	}
	/** 設定個人負債_因子值 **/
	public void setP4_th_drate(BigDecimal p4_th_drate) {
		this.p4_th_drate = p4_th_drate;
	}

	/** 取得擔保率_因子值 **/
	public BigDecimal getZ3_th_security_ratio() {
		return z3_th_security_ratio;
	}
	/** 設定擔保率_因子值 **/
	public void setZ3_th_security_ratio(BigDecimal z3_th_security_ratio) {
		this.z3_th_security_ratio = z3_th_security_ratio;
	}
	
	/** 取得P3之0~100分數 **/
	public BigDecimal getP3_th_score() {
		return p3_th_score;
	}
	/** 設定P3之0~100分數 **/
	public void setP3_th_score(BigDecimal p3_th_score) {
		this.p3_th_score = p3_th_score;
	}
	
	/** 取得P4之0~100分數 **/
	public BigDecimal getP4_th_score() {
		return p4_th_score;
	}
	/** 設定P4之0~100分數 **/
	public void setP4_th_score(BigDecimal p4_th_score) {
		this.p4_th_score = p4_th_score;
	}
	
	/** 取得Z3之0~100分數 **/
	public BigDecimal getZ3_th_score() {
		return z3_th_score;
	}
	/** 設定Z3之0~100分數 **/
	public void setZ3_th_score(BigDecimal z3_th_score) {
		this.z3_th_score = z3_th_score;
	}
	
	/** 取得P3標準化分數 **/
	public BigDecimal getP3_th_std_score() {
		return p3_th_std_score;
	}
	/** 設定P3標準化分數 **/
	public void setP3_th_std_score(BigDecimal p3_th_std_score) {
		this.p3_th_std_score = p3_th_std_score;
	}
	
	/** 取得P4標準化分數 **/
	public BigDecimal getP4_std_score() {
		return p4_std_score;
	}
	/** 設定P4標準化分數 **/
	public void setP4_std_score(BigDecimal p4_std_score) {
		this.p4_std_score = p4_std_score;
	}
	
	/** 取得Z3標準化分數 **/
	public BigDecimal getZ3_th_std_score() {
		return z3_th_std_score;
	}
	/** 設定Z3標準化分數 **/
	public void setZ3_th_std_score(BigDecimal z3_th_std_score) {
		this.z3_th_std_score = z3_th_std_score;
	}	
	
	/**--------日本模型 2.0新增欄位  Start--------**/
	/** 取得學歷(因子) **/
	public String getEdu_item() {
		return this.edu_item;
	}
	/** 設定學歷(因子) **/
	public void setEdu_item(String edu_item) {
		this.edu_item = edu_item;
	}
	
	/** 取得學歷(原始分數) **/
	public BigDecimal getEdu_score() {
		return edu_score;
	}
	/** 設定學歷(原始分數) **/
	public void setEdu_score(BigDecimal edu_score) {
		this.edu_score = edu_score;
	}
	
	/** 取得學歷(權重分數) **/
	public BigDecimal getEdu_weight_score() {
		return edu_weight_score;
	}
	/** 設定學歷(權重分數) **/
	public void setEdu_weight_score(BigDecimal edu_weight_score) {
		this.edu_weight_score = edu_weight_score;
	}
	
	/** 取得個人負債比(權重分數) **/
	public BigDecimal getDrate_weight_score() {
		return drate_weight_score;
	}
	/** 設定個人負債比(權重分數) **/
	public void setDrate_weight_score(BigDecimal drate_weight_score) {
		this.drate_weight_score = drate_weight_score;
	}
	
	/** 取得年齡(權重分數) **/
	public BigDecimal getM1_weight_score() {
		return m1_weight_score;
	}
	/** 設定年齡(權重分數) **/
	public void setM1_weight_score(BigDecimal m1_weight_score) {
		this.m1_weight_score = m1_weight_score;
	}
	
	/** 取得年資(權重分數) **/
	public BigDecimal getM7_weight_score() {
		return m7_weight_score;
	}
	/** 設定年資(權重分數) **/
	public void setM7_weight_score(BigDecimal m7_weight_score) {
		this.m7_weight_score = m7_weight_score;
	}
	
	/** 取得契約年限(權重分數) **/
	public BigDecimal getA5_weight_score() {
		return a5_weight_score;
	}
	/** 設定契約年限(權重分數) **/
	public void setA5_weight_score(BigDecimal a5_weight_score) {
		this.a5_weight_score = a5_weight_score;
	}
	
	/** 取得職業(權重分數) **/
	public BigDecimal getM5_weight_score() {
		return m5_weight_score;
	}
	/** 設定職業(權重分數) **/
	public void setM5_weight_score(BigDecimal m5_weight_score) {
		this.m5_weight_score = m5_weight_score;
	}
	
	/** 取得年收入(權重分數) **/
	public BigDecimal getP2_weight_score() {
		return p2_weight_score;
	}
	/** 設定年收入(權重分數) **/
	public void setP2_weight_score(BigDecimal p2_weight_score) {
		this.p2_weight_score = p2_weight_score;
	}
	
	/** 取得擔保品種類(權重分數) **/
	public BigDecimal getZ1_weight_score() {
		return z1_weight_score;
	}
	/** 設定擔保品種類(權重分數) **/
	public void setZ1_weight_score(BigDecimal z1_weight_score) {
		this.z1_weight_score = z1_weight_score;
	}
	
	/** 取得市場環境及變現性(權重分數) **/
	public BigDecimal getZ2_weight_score() {
		return z2_weight_score;
	}
	/** 設定市場環境及變現性(權重分數) **/
	public void setZ2_weight_score(BigDecimal z2_weight_score) {
		this.z2_weight_score = z2_weight_score;
	}
	/**--------日本模型 2.0新增欄位 End--------**/
	
	/**--------澳洲模型 3.0新增欄位 Start--------**/
	/** 
	 * 取得P3_HINCOME_CURR因子值
	 */
	public String getP3_hincome_curr() {
		return this.p3_hincome_curr;
	}
	/**
	 *  設定P3_HINCOME_CURR因子值
	 **/
	public void setP3_hincome_curr(String value) {
		this.p3_hincome_curr = value;
	}
	
	/** 取得夫妻負債比(權重分數) **/
	public BigDecimal getP3_weight_score() {
		return p3_weight_score;
	}
	/** 設定夫妻負債比(權重分數) **/
	public void setP3_weight_score(BigDecimal p3_weight_score) {
		this.p3_weight_score = p3_weight_score;
	}
	
	
	
	/**--------澳洲模型 3.0新增欄位 End--------**/
}
