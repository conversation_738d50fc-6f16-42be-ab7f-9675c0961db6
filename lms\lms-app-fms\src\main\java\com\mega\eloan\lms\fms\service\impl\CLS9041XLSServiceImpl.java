package com.mega.eloan.lms.fms.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.mfaloan.service.MisStuDataService;
import com.mega.sso.service.BranchService;

import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.exception.CapException;

@Service("cls9041xlsservice")
public class CLS9041XLSServiceImpl implements FileDownloadService {
	@Resource
	MisStuDataService misstudataservice;
	@Resource
	BranchService branchservice;
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS9041XLSServiceImpl.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = null;
		List<Map<String, Object>> list = null;
		list = misstudataservice.getStuData();
		try {
			baos = new ByteArrayOutputStream();

			WritableWorkbook book = Workbook.createWorkbook(baos);
			WritableSheet sheet = book.createSheet("1", 0);
			// --------------標題列格式----------
			WritableFont detail = new WritableFont(
					WritableFont.createFont("標楷體"), 12, WritableFont.NO_BOLD,
					false);
			WritableCellFormat cellformat = new WritableCellFormat(detail);
			// --------------標題列--------------
			String[] title = { "承作分行", "借款人統編", "借款人姓名", "額度序號", "貸款起日",
					"貸款迄日", "貸款金額", "留學生統編", "留學生姓名", "郵遞區號", "學生戶籍地址",
					"國內最高學歷", "畢業科系代碼", "出國地區", "就讀學校", "就讀科系代碼", "教育階段",
					"借款人與留學生關係", "保證人統編", "保證人姓名", "保證人與學生關係", "保證人統編",
					"保證人姓名", "保證人與學生關係" };
			for (int j = 0; j < title.length; j++) {
				Label labelT1 = new Label(j, 0, title[j], cellformat);
				sheet.addCell(labelT1);
			}
			// -------------資料內容------------
			for (int i = 0; i < list.size(); i++) {
				if (list.get(i).get("BRANCH") != null) {
					if (branchservice.getBranchName(list.get(i).get("BRANCH")
							.toString()) != null) {
						Label label = new Label(0, i + 1, branchservice
								.getBranchName(
										list.get(i).get("BRANCH").toString())
								.toString());
						sheet.addCell(label);
					} else {
						Label label = new Label(0, i + 1, list.get(i)
								.get("BRANCH").toString());
						sheet.addCell(label);
					}
				}
				if (list.get(i).get("CUSTID") != null) {
					Label label = new Label(1, i + 1, list.get(i).get("CUSTID")
							.toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("CNAME") != null) {
					Label label = new Label(2, i + 1, list.get(i).get("CNAME")
							.toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("CNTRNO") != null) {
					Label label = new Label(3, i + 1, list.get(i).get("CNTRNO")
							.toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("CNTFROM") != null) {
					Label label = new Label(4, i + 1, list.get(i)
							.get("CNTFROM").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("CNTEND") != null) {
					Label label = new Label(5, i + 1, list.get(i).get("CNTEND")
							.toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("FACTAMT") != null) {
					Label label = new Label(6, i + 1, list.get(i)
							.get("FACTAMT").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("STUID") != null) {
					Label label = new Label(7, i + 1, list.get(i).get("STUID")
							.toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("RSFLD1") != null) {
					Label label = new Label(8, i + 1, list.get(i).get("RSFLD1")
							.toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("ZIP") != null) {
					Label label = new Label(9, i + 1, list.get(i).get("ZIP")
							.toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("ADDR1") != null) {
					Label label = new Label(10, i + 1, list.get(i).get("ADDR1")
							.toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("GRATSCHL") != null) {
					Label label = new Label(11, i + 1, list.get(i)
							.get("GRATSCHL").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("GRATDEP") != null) {
					Label label = new Label(12, i + 1, list.get(i)
							.get("GRATDEP").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("SCHLLNO") != null) {
					Label label = new Label(13, i + 1, list.get(i)
							.get("SCHLLNO").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("SCHLNM") != null) {
					Label label = new Label(14, i + 1, list.get(i)
							.get("SCHLNM").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("DEPTNO") != null) {
					Label label = new Label(15, i + 1, list.get(i)
							.get("DEPTNO").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("EDUCLS") != null) {
					Label label = new Label(16, i + 1, list.get(i)
							.get("EDUCLS").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("STUREL") != null) {
					Label label = new Label(17, i + 1, list.get(i)
							.get("STUREL").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("LNGEID1") != null) {
					Label label = new Label(18, i + 1, list.get(i)
							.get("LNGEID1").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("LNGENM1") != null) {
					Label label = new Label(19, i + 1, list.get(i)
							.get("LNGENM1").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("STUREL2") != null) {
					Label label = new Label(20, i + 1, list.get(i)
							.get("STUREL2").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("LNGEID2") != null) {
					Label label = new Label(21, i + 1, list.get(i)
							.get("LNGEID2").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("LNGENM2") != null) {
					Label label = new Label(22, i + 1, list.get(i)
							.get("LNGENM2").toString());
					sheet.addCell(label);
				}
				if (list.get(i).get("STUREL3") != null) {
					Label label = new Label(23, i + 1, list.get(i)
							.get("STUREL3").toString());
					sheet.addCell(label);
				}
			}
			// -------附加說明------
			String[] end = {
					"※出國地區：01-美國，02加拿大，03-英國，04-法國，05-德國，06-澳大利亞，07-紐西蘭，08-日本，99-其他",
					"※教育階段：1-博士(碩士無借款)，2-博士(碩士有借款)，3-碩士，4-學士直攻博士",
					"※借款人/保證人與學生關係：1-父母，2-本人及配偶之兄弟姊妹，3-公婆或岳父母，4-配偶，5-伯叔阿姨，6-朋友，9-其他" };
			for (int i = 0; i < end.length; i++) {
				Label label = new Label(1, list.size() + 2 + i, end[i]);
				sheet.addCell(label);
			}

			book.write();
			book.close();
			return baos.toByteArray();
		} catch (Exception ex) {

		} finally {
			if (baos != null) {
				try {
					System.out.print("close");
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}
}
