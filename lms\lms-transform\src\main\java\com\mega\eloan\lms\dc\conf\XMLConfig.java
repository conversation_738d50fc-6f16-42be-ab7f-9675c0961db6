package com.mega.eloan.lms.dc.conf;

import java.io.File;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOCase;
import org.apache.commons.io.filefilter.FileFilterUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.action.ParserDB2XML;
import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.util.TextDefine;

public class XMLConfig {
	private static Logger logger = LoggerFactory.getLogger(XMLConfig.class);

	private static Map<String, ParserDB2XML> mapLMS = new LinkedHashMap<String, ParserDB2XML>();
	private static Map<String, ParserDB2XML> mapCLS = new LinkedHashMap<String, ParserDB2XML>();

	private static XMLConfig config = new XMLConfig();

	public static XMLConfig getInstance() {
		return config;
	}

	public ParserDB2XML getDB2XmlBySysId(String schema, String xmlName) {
		return TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema) ? XMLConfig
				.getInstance().getLMS(xmlName) : XMLConfig.getInstance()
				.getCLS(xmlName);
	}

	public ParserDB2XML getLMS(String id) {
		return mapLMS.get(id);
	}

	public ParserDB2XML getCLS(String id) {
		return mapCLS.get(id);
	}

	private XMLConfig() {
		try {
			this.load();
		} catch (Exception ex) {
			logger.error("DB2XmlConfigManager init() EXCEPTION!!", ex);
			throw new DCException("讀取Config設定檔錯誤！", ex);
		}
	}

	private void load() throws Exception {
		ConfigData databean = MainConfig.getInstance().getConfig();
		String lmsXmlPath = databean.getDC_ROOT() + databean.getXML_LMS();
		this.loasXml(mapLMS, lmsXmlPath);

		String clsXmlPath = databean.getDC_ROOT() + databean.getXML_CLS();
		this.loasXml(mapCLS, clsXmlPath);
	}

	private void loasXml(Map<String, ParserDB2XML> map, String path) {
		try {
			Collection<File> files = FileUtils.listFiles(new File(path),
					FileFilterUtils
							.suffixFileFilter(".xml", IOCase.INSENSITIVE),
					FileFilterUtils.trueFileFilter());
			map.clear();
			if (logger.isDebugEnabled()) {
				logger.debug("----------------------------------------------------");
				logger.debug(" path=" + path);
			}
			for (File file : files) {
				ParserDB2XML value = new ParserDB2XML();
				value.readDB2XML(file.getPath());
				String key = FilenameUtils.getName(file.getPath());
				map.put(key, value);

				if (logger.isDebugEnabled()) {
					logger.debug("key=" + key + ",value=" + value);
				}
			}
			if (logger.isDebugEnabled()) {
				logger.debug("----------------------------------------------------");
			}
		} catch (Exception e) {
			logger.error("讀取XML檔時產生錯誤 : ", e);
		}
	}

}
