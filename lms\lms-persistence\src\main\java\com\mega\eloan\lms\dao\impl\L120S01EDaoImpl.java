package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S01EDao;
import com.mega.eloan.lms.model.L120S01E;


/** 企金營收獲利財務狀況檔 **/
@Repository
public class L120S01EDaoImpl extends LMSJpaDao<L120S01E, String>
	implements L120S01EDao {

	@Override
	public L120S01E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	@Override
	public List<L120S01E> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L120S01E> list = createQuery(L120S01E.class,search).getResultList();
		return list;
	}
	@Override
	public List<L120S01E> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S01E> list = createQuery(L120S01E.class,search).getResultList();
		return list;
	}
	@Override
	public List<L120S01E> findByKindMainId(String mainId,String finKind) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "finKind", finKind);
		List<L120S01E> list = createQuery(L120S01E.class,search).getResultList();
		return list;
	}
	
	@Override
	public List<L120S01E> findByKey(String finkind,String mainid,String custid,String dupno){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "finKind", finkind);
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custid);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupno);
		List<L120S01E> list = createQuery(L120S01E.class,search).getResultList();
		return list;
	}

	@Override
	public List<L120S01E> findByKey2(String finkind,String mainid,String custid,String dupno){
		ISearch search = createSearchTemplete();
		search.addOrderBy("finYear");
		search.addOrderBy("finItem");
		search.addSearchModeParameters(SearchMode.EQUALS, "finKind", finkind);
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custid);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupno);
		List<L120S01E> list = createQuery(L120S01E.class,search).getResultList();
		return list;
	}	
	
	@Override
	public L120S01E findByUniqueKey(String mainId,String custId,String dupNo,String finKind,Date finYear,String finItem){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "finKind", finKind);
		search.addSearchModeParameters(SearchMode.EQUALS, "finYear", finYear);
		search.addSearchModeParameters(SearchMode.EQUALS, "finItem", finItem);
	
		return findUniqueOrNone(search);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findMainId(String custId, String dupNo) {
		Query query = getEntityManager().createNamedQuery(
				"c120m01a.selMainId");
		query.setParameter("custId", custId); //設置參數
		query.setParameter("dupNo", dupNo); //設置參數
		return query.getResultList();
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findRate(String mainId) {
		Query query = getEntityManager().createNamedQuery(
				"c120m01c.selRate");
		query.setParameter("mainId", mainId); //設置參數
		return query.getResultList();
	}	
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findL120s01e(String mainId, String custId, String dupNo, String finKind){
		Query query = getEntityManager().createNamedQuery("L120S01E.selL120s01e");
		query.setParameter("MAINID", mainId); //設置參數
		query.setParameter("CUSTID", custId); //設置參數
		query.setParameter("DUPNO", dupNo); //設置參數
		query.setParameter("FINKIND", finKind); //設置參數
		return query.getResultList();
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Date> findFinYear(String mainId, String custId, String dupNo, String finKind){
		Query query = getEntityManager().createNamedQuery("L120S01E.selFinYear");
		query.setParameter("MAINID", mainId); //設置參數
		query.setParameter("CUSTID", custId); //設置參數
		query.setParameter("DUPNO", dupNo); //設置參數
		query.setParameter("FINKIND", finKind); //設置參數
		return query.getResultList();
	}
	
	@Override
	public int delModel(String mainId){
		Query query = getEntityManager().createNamedQuery("L120S01E.delModel");
		query.setParameter("MAINID", mainId); //設置參數
		return query.executeUpdate();
	}
}