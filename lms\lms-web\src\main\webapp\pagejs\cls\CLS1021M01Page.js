var initDfd = initDfd||$.Deferred(), inits = {
    fhandle: "cls1021m01formhandler",
    ghaddle: "cls1021gridhandler"
};
// 驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    // auth.readOnly ||
    if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
        return true;
    }
    return false;
}

pageJsInit(function(){
    $(function(){
        $.form.init({
            formHandler: inits.fhandle,
            formPostData: {// 把form上貼上資料
                formAction: "queryC102m01a",
                oid: responseJSON.oid
            },
            loadSuccess: function(json){
                responseJSON.mainDocStatus = json.docStatusVal;
                responseJSON.unitLoanCase = json.unitLoanCase;
                var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
                initDfd.resolve(json, auth);
                responseJSON.mainId = json.mainId;
                
                $("#check1").show();
                
                if (responseJSON.page == "01") {
                    $("#bossId").html(json.bossId);
                }
                // $("label").lockDoc();
                if (checkReadonly()) {
                    $(".readOnlyhide").hide();
                    $("form").lockDoc();
                }
            }
        });// close form init
        var openCustNameGrid = $('#openCustNameGrid').iGrid({
            handler: "cls1021gridhandler",
            height: 150,
            scroll: 200,
            rowNum: 200,
            sortable: false,
            // loadtext : i18n.lms9121v00['L912V00.Updating'],
            colModel: [{
                colHeader: i18n.cls1021m01['C102M01A.custName'],
                name: 'CNAME',
                width: 120,
                align: "left",
                sortable: false
            }]
        });
        var openaLoanACGrid = $('#openaLoanACGrid').iGrid({
            handler: "cls1021gridhandler",
            height: 150,
            scroll: 200,
            rowNum: 200,
            sortable: false,
            // loadtext : i18n.lms9121v00['L912V00.Updating'],
            colModel: [{
                colHeader: i18n.cls1021m01['C102M01A.cntrNo'],//
                // 更新Table,
                name: 'aLoanAC',
                width: 120,
                align: "left",
                sortable: false
            }, {
                colHeader: i18n.cls1021m01['C102M01A.aLoanDate'],//
                // 更新Table,
                name: 'aLoanDate',
                width: 120,
                align: "left",
                sortable: false
            }]
        });
        // 呈主管覆核 選授信主管人數
        $("#numPerson").change(function(){
            $('#bossItem').empty();
            var value = $(this).val();
            if (value) {
                var html = '';
                for (var i = 1; i <= value; i++) {
                    var name = 'boss' + i;
                    html += i + '. ' // +i18n.cls1161m01['manager.L3']
                    // || '授信主管'
                    html += '<select id="' + name + '" name="boss"' +
                    
                    '" class="required" CommonManager="kind:2;type:2" ></select>';
                    html += '<br/>';
                }
                $('#bossItem').append(html).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
            }
            
        });
        
        // 當項目不是3的倍數，補成3的倍數的陣列
        function checkArray(Item){
            var size = Item.length;
            if (size % 3 != 0) {
                var max = 3 - (size % 3);
                for (var i = 0; i < max; i++) {
                    Item.push({
                        "id": "0",
                        "drc": ""
                    });
                }
            }
            return Item;
        }
        
        // 畫稽核項目的table
        function creatItemTable(itemObject, itemName){
            var tr = "";
            var td = "";
            for (var b in itemObject) {
                td += "<td><select name='" + itemName +
                itemObject[b].id +
                "' class='s1'></select></td><td>" +
                itemObject[b].drc +
                "</td>";
                
                // 當已經到三欄就換行
                if ((b + 1) % 3 == 0) {
                    tr += ("<tr>" + td + "</tr>");
                    td = "";
                }
            }
            $("#" + itemName + "Branch").after(tr);
        }
        $("#getcustName").click(function(){
            openCustNameBox();
        });
        $("#getaLoanAC").click(function(){
            openaLoanACBox();
        });
        $("#getQuest").click(function(){
            getExplain();
        });
        var btn = $("#buttonPanel");
        btn.find("#btnSave").click(function(showMsg){
        
            saveData(true);
            
        }).end().find("#btnDelete").click(function(){
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "delete",
                            mainOid: $("#oid").val()
                        }
                    });
                }
            });
        }).end().find("#btnTest").click(function(){
            $.ajax({
                handler: inits.fhandle,
                data: {
                    formAction: "TETSMIS",
                    mainOid: $("#mainOid").val()
                },
            }).done(function(){

            });              
            
        }).end().find("#btnSend").click(function(){
            saveData(false, sendBoss);
        }).end().find("#btnAccept").click(function(){
            flowAction({
                flowAction: true
            });
        }).end().find("#btnCheck").click(function(){
            openCheck();
        }).end().find("#btnPrint").click(function(){
            if (checkReadonly()) {
                printAction();
            }
            else {
                // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
                CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                    if (b) {
                        saveData(false, printAction);
                    }
                });
            }
        });
        
        // 列印動作
        function printAction(){
            $.form.submit({
                url: "../../simple/FileProcessingService",
                target: "_blank",
                data: {
                    mainId: responseJSON.mainId,
                    mainOid: $("#oid").val(),
                    fileDownloadName: "cls1021r01.pdf",
                    serviceName: "cls1021r01rptservice"
                }
            });
        }
        // 取得 問與答
        function getExplain(){
            $.form.submit({
                url: "../../simple/FileProcessingService",
                target: "_blank",
                data: {
                    fileDownloadName: "CLS1021S01Explain.pdf",
                    serviceName: "cls1021s01pdfservice"
                }
            });
        }
        
        // 儲存的動作
        function saveData(showMsg, tofn){
			var $form = $("#C102M01AForm");
			var formData = $form.serializeData(); 
            if ($("#C102M01AForm").valid()) {
                if ($("#aLoanAC").val() != "" &&
                $("#aLoanDate").val() != "") {
                    var allresult = {};
                    var localresult = {};
                    var selfresult = {};
                    FormAction.open = true;
                    $.ajax({
                        handler: inits.fhandle,
                        data: {// 把資料轉成json
                            formAction: "saveC102m01a",
                            page: responseJSON.page,
                            txCode: responseJSON.txCode,
                            showMsg: showMsg,
                            selfresult: JSON.stringify(selfresult),
                            localresult: JSON.stringify(localresult),
                            allresult: JSON.stringify(allresult),
                            custName: $("#custName").val(),
                            aLoanAC: $("#aLoanAC").val(),
                            aLoanDate: $("#aLoanDate").val(),
							C102M01AForm:JSON.stringify(formData)
                        },
                    }).done(function(obj){
                        if (responseJSON.page == "01") {
                            $('body').injectData(obj);
                        }
                        
                        checkMsg(obj).done(function(){
                            if ($("#mainOid").val()) {
                                setRequiredSave(false);
                            }
                            else {
                                setRequiredSave(true);
                            }
                            
                            // 執行列印
                            if (!showMsg && tofn) {
                                tofn();
                            }	
                        });  
                        
                        CommonAPI.triggerOpener("gridview", "reloadGrid");  
                    });
                    
                }
                else {
                    CommonAPI.showErrorMessage(i18n.cls1021m01["C102M01A.error7"]);
                }
            }
        }
        
        function checkMsg(json){
            var my_dfd = $.Deferred();    	
            if(json.tReCheckDataMsg) {
                /*
                 * 待 click confirmMessage 視窗的「確定」才觸發 dfd 的 event
                 */
                API.showPopMessage("", json.tReCheckDataMsg, function(){
                    my_dfd.resolve();
                });
            }else{
                my_dfd.resolve();
            }	
            return my_dfd.promise();
        }
        
        //    
        function flowAction(sendData){
            $.ajax({
                handler: inits.fhandle,
                data: $.extend({
                    formAction: "flowAction",
                    mainOid: $("#mainOid").val()
                }, (sendData || {})),
               
            }).done(function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                API.showPopMessage(i18n.def["runSuccess"], window.close);
            });
        }
        var item;
        // 呈主管 - 編製中
        function sendBoss(){
            $.ajax({
                handler: "cls1021m01formhandler",
                action: "checkData",
                data: {},
                
            }).done(function(json){
                $('#managerItem').empty();
                    $('#bossItem').empty();
                    item = json.bossList;
                    var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"></select>';
                    $('#bossItem').append(bhtml).find('select').each(function(){
                        $(this).setItems({
                            item: item,
                            format: "{value} {key}"
                        });
                    });
                    var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:2" ></select>';
                    $('#managerItem').append(html).find('select').each(function(){
                        $(this).setItems({
                            item: item,
                            format: "{value} {key}"
                        });
                    });
                    
                    // C102M01A.message27=是否呈主管覆核？
                    CommonAPI.confirmMessage(i18n.cls1021m01["C102M01B.message27"], function(b){
                        if (b) {
                            $("#selectBossBox").thickbox({
                                // C102M01A.bt14=覆核
                                title: i18n.cls1021m01['C102M01A.bt14'],
                                width: 500,
                                height: 300,
                                modal: true,
                                readOnly: false,
                                valign: "bottom",
                                align: "center",
                                i18n: i18n.def,
                                buttons: {
                                    "sure": function(){
                                    
                                        var selectBoss = $("select[name^=boss]").map(function(){
                                            return $(this).val();
                                        }).toArray();
                                        
                                        for (var i in selectBoss) {
                                            if (selectBoss[i] == "") {
                                                // C102M01A.error2=請選擇+
                                                // C102M01B.bossId=授信主管
                                                return CommonAPI.showErrorMessage(i18n.cls1021m01['C102M01A.error2'] +
                                                i18n.cls1021m01['C102M01B.bossId']);
                                            }
                                        }
                                        if ($("#manager").val() ==
                                        "") {
                                            // C102M01A.error2=請選擇+
                                            // C102M01B.managerId=經副襄理
                                            return CommonAPI.showErrorMessage(i18n.cls1021m01['C102M01A.error2'] +
                                            i18n.cls1021m01['C102M01B.managerId']);
                                        }
                                        // 驗證是否有重複的主管
                                        if (checkArrayRepeat(selectBoss)) {
                                            // 主管人員名單重複請重新選擇
                                            return CommonAPI.showErrorMessage(i18n.cls1021m01['C102M01B.message31']);
                                        }
                                        
                                        flowAction({
                                            page: responseJSON.page,
                                            saveData: true,
                                            selectBoss: selectBoss,
                                            manager: $("#manager").val()
                                        });
                                        $.thickbox.close();
                                        
                                    },
                                    
                                    "cancel": function(){
                                        $.thickbox.close();
                                    }
                                }
                            });
                        }
                    });
            });
        }
        
        // 待覆核 - 覆核
        function openCheck(){
            // thickboxOptions.readOnly= false;
            $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
                // C102M01A.bt14=覆核
                title: i18n.cls1021m01['C102M01A.bt14'],
                width: 100,
                height: 100,
                modal: true,
                readOnly: false,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                    
                        var val = $("[name=checkRadio]:checked").val();
                        if (!val) {
                            // C102M01A.error2=請選擇
                            return CommonAPI.showMessage(i18n.cls1021m01['C102M01A.error2']);
                        }
                        $.thickbox.close();
                        switch (val) {
                            case "1":
                                // 一般退回到編製中01O
                                // C102M01B.message32=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                                CommonAPI.confirmMessage(i18n.cls1021m01['C102M01B.message32'], function(b){
                                    if (b) {
                                        flowAction({
                                            flowAction: false
                                        });
                                    }
                                });
                                
                                break;
                            case "3":
                                // C102M01B.message34=該案件是否確定執行核定作業
                                CommonAPI.confirmMessage(i18n.cls1021m01["C102M01B.message34"], function(b){
                                    if (b) {
                                        checkDate();
                                    }
                                });
                                break;
                        }
                        
                    },
                    
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        }
        
        // 輸入核定日期視窗
        function checkDate(){
            // 帶入今天日期
            $("#forCheckDate").val(CommonAPI.getToday());
            $("#openChecDatekBox").thickbox({ // 使用選取的內容進行彈窗
                // C102M01B.message38 = 請輸入核定日
                title: i18n.cls1021m01['C102M01B.message38'],
                width: 100,
                height: 100,
                modal: true,
                valign: "bottom",
                align: "center",
                readOnly: false,
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        var forCheckDate = $("#forCheckDate").val();
                        if ($.trim(forCheckDate) == "") {
                            // C102M01B.message38 =
                            // 請輸入核定日
                            return CommonAPI.showErrorMessage(i18n.cls1021m01['C102M01B.message38']);
                        }
                        flowAction({
                            flowAction: true,
                            checkDate: forCheckDate
                        });
                        $.thickbox.close();
                    },
                    
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        }
        // 檢查陣列內容是否重複
        function checkArrayRepeat(arrVal){
            var newArray = [];
            for (var i = arrVal.length; i--;) {
                var val = arrVal[i];
                if ($.inArray(val, newArray) == -1) {
                    newArray.push(val);
                }
                else {
                    return true;
                }
            }
            return false;
        }
    });
});


