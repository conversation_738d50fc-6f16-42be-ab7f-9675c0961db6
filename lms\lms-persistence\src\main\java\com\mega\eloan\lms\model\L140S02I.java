/* 
 * L140S02I.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 留學貸款檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140S02I", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "seq" }))
public class L140S02I extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/** 留學生身分證字號 **/
	@Size(max = 10)
	@Column(name = "STDCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String stdCustId;

	/** 留學生身分證重覆序號 **/
	@Size(max = 1)
	@Column(name = "STDDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String stdDupNo;

	/** 留學生姓名 **/
	@Size(max = 60)
	@Column(name = "STDNAME", length = 60, columnDefinition = "VARCHAR(60)")
	private String stdName;

	/**
	 * 家庭所得類別
	 * <p/>
	 * 單選：<br/>
	 * 1.全額<br/>
	 * 2.不補貼利息<br/>
	 * 3.半額
	 */
	@Size(max = 1)
	@Column(name = "STDICTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String stdICType;

	/**
	 * 國內最高學歷
	 * <p/>
	 * 單選：<br/>
	 * codeType =L140S02I_eduSchl
	 */
	@Size(max = 6)
	@Column(name = "EDUSCHL", length = 6, columnDefinition = "VARCHAR(6)")
	private String eduSchl;

	/**
	 * 科系
	 * <p/>
	 * 單選：<br/>
	 * codeType =L140S02I_eduDep
	 */
	@Size(max = 6)
	@Column(name = "EDUDEP", length = 6, columnDefinition = "VARCHAR(6)")
	private String eduDep;

	/**
	 * 出國地區
	 * <p/>
	 * 單選：<br/>
	 * 改建置於codeType(國別代碼)??<br/>
	 * ※現行notes項目：<br/>
	 * US：1.美國<br/>
	 * CA：2.加拿大<br/>
	 * GB：3.英國<br/>
	 * FR：4.法國<br/>
	 * DE：5.德國<br/>
	 * AU：6.澳大利亞<br/>
	 * NZ：7.紐西蘭<br/>
	 * JP：8.日本<br/>
	 * 99其他
	 */
	@Size(max = 2)
	@Column(name = "STDCOUNTRY", length = 2, columnDefinition = "VARCHAR(2)")
	private String stdCountry;

	/**
	 * 出國地區-其他
	 * <p/>
	 * (預留)未使用
	 */
	@Size(max = 60)
	@Column(name = "STDCOUOTH", length = 60, columnDefinition = "VARCHAR(60)")
	private String stdCouOth;

	/** 就讀學校名稱 **/
	@Size(max = 120)
	@Column(name = "FEDUSCHL", length = 120, columnDefinition = "VARCHAR(120)")
	private String feduSchl;

	/**
	 * 就讀科系
	 * <p/>
	 * 單選：<br/>
	 * codeType =L140S02I_eduDep
	 */
	@Size(max = 6)
	@Column(name = "FEDUDEP", length = 6, columnDefinition = "VARCHAR(6)")
	private String feduDep;

	/**
	 * 教育階段
	 * <p/>
	 * 單選：<br/>
	 * 1.博士(碩士無借款)<br/>
	 * 2.博士(碩士有借款)<br/>
	 * 3.碩士<br/>
	 * 4.學士直攻博士<br/>
	 * 5.碩士續攻博士
	 */
	@Size(max = 1)
	@Column(name = "EDUKIND", length = 1, columnDefinition = "CHAR(1)")
	private String eduKind;

	/**
	 * 保費
	 * <p/>
	 * 新台幣元
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "STDMONEY", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal stdMoney;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}

	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 取得留學生身分證字號 **/
	public String getStdCustId() {
		return this.stdCustId;
	}

	/** 設定留學生身分證字號 **/
	public void setStdCustId(String value) {
		this.stdCustId = value;
	}

	/** 取得留學生身分證重覆序號 **/
	public String getStdDupNo() {
		return this.stdDupNo;
	}

	/** 設定留學生身分證重覆序號 **/
	public void setStdDupNo(String value) {
		this.stdDupNo = value;
	}

	/** 取得留學生姓名 **/
	public String getStdName() {
		return this.stdName;
	}

	/** 設定留學生姓名 **/
	public void setStdName(String value) {
		this.stdName = value;
	}

	/**
	 * 取得家庭所得類別
	 * <p/>
	 * 單選：<br/>
	 * 1.全額<br/>
	 * 2.不補貼利息<br/>
	 * 3.半額
	 */
	public String getStdICType() {
		return this.stdICType;
	}

	/**
	 * 設定家庭所得類別
	 * <p/>
	 * 單選：<br/>
	 * 1.全額<br/>
	 * 2.不補貼利息<br/>
	 * 3.半額
	 **/
	public void setStdICType(String value) {
		this.stdICType = value;
	}

	/**
	 * 取得國內最高學歷
	 * <p/>
	 * 單選：<br/>
	 * codeType =L140S02I_eduSchl
	 */
	public String getEduSchl() {
		return this.eduSchl;
	}

	/**
	 * 設定國內最高學歷
	 * <p/>
	 * 單選：<br/>
	 * codeType =L140S02I_eduSchl
	 **/
	public void setEduSchl(String value) {
		this.eduSchl = value;
	}

	/**
	 * 取得科系
	 * <p/>
	 * 單選：<br/>
	 * codeType =L140S02I_eduDep
	 */
	public String getEduDep() {
		return this.eduDep;
	}

	/**
	 * 設定科系
	 * <p/>
	 * 單選：<br/>
	 * codeType =L140S02I_eduDep
	 **/
	public void setEduDep(String value) {
		this.eduDep = value;
	}

	/**
	 * 取得出國地區
	 * <p/>
	 * 單選：<br/>
	 *Codetype=L140S02I_stdCountry<br/>
	 * ※現行notes項目：<br/>
	 * US：1.美國<br/>
	 * CA：2.加拿大<br/>
	 * GB：3.英國<br/>
	 * FR：4.法國<br/>
	 * DE：5.德國<br/>
	 * AU：6.澳大利亞<br/>
	 * NZ：7.紐西蘭<br/>
	 * JP：8.日本<br/>
	 * 99其他
	 */
	public String getStdCountry() {
		return this.stdCountry;
	}

	/**
	 * 設定出國地區
	 * <p/>
	 * 單選：<br/>
	 * Codetype=L140S02I_stdCountry<br/>
	 * ※現行notes項目：<br/>
	 * US：1.美國<br/>
	 * CA：2.加拿大<br/>
	 * GB：3.英國<br/>
	 * FR：4.法國<br/>
	 * DE：5.德國<br/>
	 * AU：6.澳大利亞<br/>
	 * NZ：7.紐西蘭<br/>
	 * JP：8.日本<br/>
	 * 99其他
	 **/
	public void setStdCountry(String value) {
		this.stdCountry = value;
	}

	/**
	 * 取得出國地區-其他
	 * <p/>
	 * (預留)未使用
	 */
	public String getStdCouOth() {
		return this.stdCouOth;
	}

	/**
	 * 設定出國地區-其他
	 * <p/>
	 * (預留)未使用
	 **/
	public void setStdCouOth(String value) {
		this.stdCouOth = value;
	}

	/** 取得就讀學校名稱 **/
	public String getFeduSchl() {
		return this.feduSchl;
	}

	/** 設定就讀學校名稱 **/
	public void setFeduSchl(String value) {
		this.feduSchl = value;
	}

	/**
	 * 取得就讀科系
	 * <p/>
	 * 單選：<br/>
	 * codeType =L140S02I_eduDep
	 */
	public String getFeduDep() {
		return this.feduDep;
	}

	/**
	 * 設定就讀科系
	 * <p/>
	 * 單選：<br/>
	 * codeType =L140S02I_eduDep
	 **/
	public void setFeduDep(String value) {
		this.feduDep = value;
	}

	/**
	 * 取得教育階段
	 * <p/>
	 * 單選：<br/>
	 * 1.博士(碩士無借款)<br/>
	 * 2.博士(碩士有借款)<br/>
	 * 3.碩士<br/>
	 * 4.學士直攻博士<br/>
	 * 5.碩士續攻博士
	 */
	public String getEduKind() {
		return this.eduKind;
	}

	/**
	 * 設定教育階段
	 * <p/>
	 * 單選：<br/>
	 * 1.博士(碩士無借款)<br/>
	 * 2.博士(碩士有借款)<br/>
	 * 3.碩士<br/>
	 * 4.學士直攻博士<br/>
	 * 5.碩士續攻博士
	 **/
	public void setEduKind(String value) {
		this.eduKind = value;
	}

	/**
	 * 取得保費
	 * <p/>
	 * 新台幣元
	 */
	public BigDecimal getStdMoney() {
		return this.stdMoney;
	}

	/**
	 * 設定保費
	 * <p/>
	 * 新台幣元
	 **/
	public void setStdMoney(BigDecimal value) {
		this.stdMoney = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
