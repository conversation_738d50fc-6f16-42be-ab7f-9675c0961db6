/* 
 * L300M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 覆審排名表檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L300M01C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L300M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 新產生時：getUUID()
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	@Size(max=32)
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 編製單位代號 **/
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 
	 * 資料產生日期<p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="CREATDATE", columnDefinition="DATE")
	private Date creatDate;

	/** 
	 * 資料起日<p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="BGNDATE", columnDefinition="DATE")
	private Date bgnDate;

	/** 
	 * 資料迄日<p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ENDDATE", columnDefinition="DATE")
	private Date endDate;

	/** 送件日 **/
	@Column(name="SENDTIME", columnDefinition="TIMESTAMP")
	private Timestamp sendTime;

	/** 報表檔檔案位置 **/
	@Size(max=64)
	@Column(name="REPORTFILE", length=64, columnDefinition="VARCHAR(64)")
	private String reportFile;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 新產生時：getUUID()
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  新產生時：getUUID()
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	public String getRandomCode() {
		return this.randomCode;
	}
	/**
	 *  設定文件亂碼<p/>
	 *  每次儲存：getRandomCode()
	 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得編製單位代號 **/
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/** 設定編製單位代號 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 
	 * 取得資料產生日期<p/>
	 * YYYY-MM-DD
	 */
	public Date getCreatDate() {
		return this.creatDate;
	}
	/**
	 *  設定資料產生日期<p/>
	 *  YYYY-MM-DD
	 **/
	public void setCreatDate(Date value) {
		this.creatDate = value;
	}

	/** 
	 * 取得資料起日<p/>
	 * YYYY-MM-DD
	 */
	public Date getBgnDate() {
		return this.bgnDate;
	}
	/**
	 *  設定資料起日<p/>
	 *  YYYY-MM-DD
	 **/
	public void setBgnDate(Date value) {
		this.bgnDate = value;
	}

	/** 
	 * 取得資料迄日<p/>
	 * YYYY-MM-DD
	 */
	public Date getEndDate() {
		return this.endDate;
	}
	/**
	 *  設定資料迄日<p/>
	 *  YYYY-MM-DD
	 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/** 取得送件日 **/
	public Timestamp getSendTime() {
		return this.sendTime;
	}
	/** 設定送件日 **/
	public void setSendTime(Timestamp value) {
		this.sendTime = value;
	}

	/** 取得報表檔檔案位置 **/
	public String getReportFile() {
		return this.reportFile;
	}
	/** 設定報表檔檔案位置 **/
	public void setReportFile(String value) {
		this.reportFile = value;
	}
}
