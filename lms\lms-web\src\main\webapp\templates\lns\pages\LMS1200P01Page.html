<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
	<body>
		<style>
			@media print {
			  #func_div {
			    display: none;
			  }
			  .pageBreak {
					page-break-after: always;
				}
			}

			p.watermark {
				font-size:38;
				color=#FCFCFC;
				z-index:-1;
				position:absolute;
				/*filter:Alpha(Opacity=30, FinishOpacity=30, Style=2);*/
				filter: Alpha(Opacity=30);/* for IE */
				Opacity:0.3;/* for Firefox */
			}
		</style>
		<script>
			var sameIdIndex = 0;
			function reNameSameId(id) {
				var e = document.getElementById(id);
				if (e) {
					document.getElementById(id).setAttribute("id", id + sameIdIndex);
					sameIdIndex++;
					reNameSameId(id);
				}
			}
			function watermark(msg){
				//var lastone = document.getElementById('wmitest1');
				reNameSameId('wmitest1');
				var lastone = document.getElementById('wmitest1' + (sameIdIndex -1));
				var str="";
				var tH=lastone.offsetTop;   
			
				var iT=Math.round(tH/1056);
				if(iT<1) iT=1;
			 
			    var kk=200;
			    var to=0;
			    var iO=0;
			    
			    while (to < tH){
					to=kk*iO;
					if (to < tH){
			           str=str+"<P class='watermark' style='top:"+
					       (kk*iO)+"px;left:"+(10)+"px\'>"+msg+"</P>";
			           iO++;
				    }
				}
				lastone.innerHTML=str;
				/*	
				 var str1=''; var str2='';var str3='';var str4='';
				 var tH = document.body.scrollHeight;
				 var tH1=tH/190 -1;
				 var tH2=tH/370 -1;
				 if(tH1<1) tH1=1;
				 if(tH2<1) tH2=1;
	
				 for(var i=1;i<tH1;i++){
					str1+='<P class="watermark" style="top:'+190*i+'px;left:50px" >'+msg+'</P>';
				 }
			
				 for(var i=1;i<tH2;i++){
			        str2+='<P class="watermark" style="top:'+370*i+'px;left:200px">'+msg+'</P>';
				 }
				 
				 for(var i=1;i<tH1;i++){
			        str3+='<P class="watermark" style="top:'+(tH-190*i)+'px;left:350px">'+msg+'</P>';
				 }
				 
				 for(var i=1;i<tH2;i++){
			        str4+='<P class="watermark" style="top:'+(tH-370*i)+'px;left:500px">'+msg+'</P>';
				 }
	
				 document.getElementById('wmitest1').innerHTML=str1;
				 document.getElementById('wmitest2').innerHTML=str2;
				 document.getElementById('wmitest3').innerHTML=str3;
				 document.getElementById('wmitest4').innerHTML=str4;
				*/
			}
			function doPrint(){
				window.print();
			}
		</script>
		<div id="func_div" style="position:absolute; top:30px; left:12px; ">
			<input type="button" name="B0" value="列印" onclick="doPrint()">
		</div>
		<div id="wmitest1"></div>
		<div id="wmitest2"></div>
		<div id="wmitest3"></div>
		<div id="wmitest4"></div>
		<!--<div wicket:id="RPSHtml" />-->
	</body>
</html>
