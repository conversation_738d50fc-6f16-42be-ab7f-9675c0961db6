/* 
 * C160S01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C160S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C160S01A;

/** 擔保品資料明細檔 **/
@Repository
public class C160S01ADaoImpl extends LMSJpaDao<C160S01A, String>
	implements C160S01ADao {

	@Override
	public C160S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C160S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C160S01A> list = createQuery(search).getResultList();
		return list;
	}
	@Override
	public List<C160S01A> findByMainIdRefMainId(String mainId,String refmainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		List<C160S01A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C160S01A findByUniqueKey(String mainId, Integer seqNo, String refmainId){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seqNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seqNo", seqNo);
		if (refmainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C160S01A> findByIndex01(String mainId, Integer seqNo, String refmainId){
		ISearch search = createSearchTemplete();
		List<C160S01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seqNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seqNo", seqNo);
		if (refmainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C160S01A> findByIndex02(String mainId, String collNo, String custId){
		ISearch search = createSearchTemplete();
		List<C160S01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (collNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "collNo", collNo);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}