/* 
 * LMS1405S02Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.panels.LMSL140M01MPanel;
import com.mega.eloan.lms.model.L120M01A;

/**
 * <pre>
 * 額度明細表 - 額度批覆表
 * </pre>
 * 
 * @since 2011/11/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new
 *          </ul>
 */
public class LMS7405S01Panel extends Panel {

	private static final long serialVersionUID = -4024257163623646201L;

	private L120M01A l120m01a;

	public LMS7405S01Panel(String id, L120M01A l120m01a) {
		super(id);
		this.l120m01a = l120m01a;

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能

		// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		// Map<String, String> lgdMap = lmsService.getLgdTotAmtParam(null, null,
		// null);
		//
		// // T=授信授權額度合計
		// String label_lgdTotAmt_T = MapUtils.getString(lgdMap, "label_T");
		// // add(new Label("label_lgdTotAmt_T", label_lgdTotAmt_T));
		//
		// int lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
		// "lmsLgdCount", "0"));
		// int lmsLgdCountTotal = Util.parseInt(MapUtils.getString(lgdMap,
		// "lmsLgdCountTotal", "0"));
		//
		// for (int i = 1; i <= lmsLgdCountTotal; i++) {
		// String label_lgdTotAmt = MapUtils.getString(lgdMap, "label_" + i,
		// "");
		// add(new Label("label_lgdTotAmt_" + i, label_lgdTotAmt));
		// String label_lgdTotAmt_U_1 = MapUtils.getString(lgdMap, "label_1_"
		// + i, "");
		// add(new Label("label_lgdTotAmt_" + i + "_1", label_lgdTotAmt_U_1));
		// }

		// // U=LGD其中擬制無擔保合計
		// // P=LGD其中擬制部分擔保合計
		// // S=LGD其中擬制十足擔保合計
		// // T=授信授權額度合計
		// String label_lgdTotAmt_U = MapUtils.getString(lgdMap, "label_U");
		// String label_lgdTotAmt_P = MapUtils.getString(lgdMap, "label_P");
		// String label_lgdTotAmt_S = MapUtils.getString(lgdMap, "label_S");
		// String label_lgdTotAmt_T = MapUtils.getString(lgdMap, "label_T");
		//
		// add(new Label("label_lgdTotAmt_U", label_lgdTotAmt_U));
		// add(new Label("label_lgdTotAmt_P", label_lgdTotAmt_P));
		// add(new Label("label_lgdTotAmt_S", label_lgdTotAmt_S));
		// // add(new Label("label_lgdTotAmt_T", label_lgdTotAmt_T));
		//
		// //
		// 「其中擬制無擔保(LGD>=50%)合計」、「其中擬制部分擔保(10%<LGD<50%)合計」、「其中擬制十足擔保(LGD<=10%)合計」
		// String label_lgdTotAmt_U_1 = MapUtils.getString(lgdMap, "label_1_U");
		// String label_lgdTotAmt_P_1 = MapUtils.getString(lgdMap, "label_1_P");
		// String label_lgdTotAmt_S_1 = MapUtils.getString(lgdMap, "label_1_S");
		//
		// add(new Label("label_lgdTotAmt_U_1", label_lgdTotAmt_U_1));
		// add(new Label("label_lgdTotAmt_P_1", label_lgdTotAmt_P_1));
		// add(new Label("label_lgdTotAmt_S_1", label_lgdTotAmt_S_1));

	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new LMS1405S02Panel01("_LMS140PanelC_2_1").processPanelData(model, params);
		new LMS1405S02Panel02("_LMS140PanelC_2_2", l120m01a).processPanelData(model, params);
		new LMS1405S02Panel03("_LMS140PanelC_2_3").processPanelData(model, params);
		new LMS1405S02Panel04("_LMS140PanelC_2_4").processPanelData(model, params);
		new LMS1405S02Panel05("_LMS140PanelC_2_5", l120m01a).processPanelData(model, params);
		new LMS1405S02Panel06("_LMS140PanelC_2_6").processPanelData(model, params);
		new LMS1405S02Panel07("_LMS140PanelC_2_7").processPanelData(model, params);
		new LMS1405S02Panel08("_LMS140PanelC_2_8").processPanelData(model, params);
		new LMS1405S02Panel09("_LMS140PanelC_2_9").processPanelData(model, params);
		new LMS1405S02Panel10("_LMS140PanelC_2_10").processPanelData(model, params);
		new LMS1405S02Panel11("_LMS140PanelC_2_11").processPanelData(model, params);
		new LMS1405S02Panel12("_LMS140PanelC_2_12").processPanelData(model, params);
		new LMSL140M01MPanel("LMSL140M01MPanel").processPanelData(model, params);
	}
}
