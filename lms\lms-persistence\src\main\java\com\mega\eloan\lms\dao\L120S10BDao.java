/* 
 * L120S10BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S10B;

/** 微型企業主檔 **/
public interface L120S10BDao extends IGenericDao<L120S10B> {

	L120S10B findByOid(String oid);
	
	List<L120S10B> findByMainId(String mainId);

	List<L120S10B> findByIndex01(String mainId);
	
	L120S10B findByUniqueKey(String mainId);
}