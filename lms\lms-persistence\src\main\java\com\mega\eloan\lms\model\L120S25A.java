/* 
 * L120S25A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** BIS評估表 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S25A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S25A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * 文件編號
	 * <p/>
	 * L120M01A.mainId
	 */
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 本案授信戶統編
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 10)
	@Column(name = "BISCUSTID_S25A", length = 10, columnDefinition = "VARCHAR(10)")
	private String bisCustId_s25a;

	/**
	 * 本案授信戶重複序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 1)
	@Column(name = "BISDUPNO_S25A", length = 1, columnDefinition = "CHAR(1)")
	private String bisDupNo_s25a;

	/**
	 * 本案授信戶額度序號
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * (
	 */
	@Size(max = 12)
	@Column(name = "BISCNTRNO_S25A", length = 12, columnDefinition = "CHAR(12)")
	private String bisCntrNo_s25a;

	/**
	 * 性質
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 30)
	@Column(name = "BISPROPERTY", length = 30, columnDefinition = "VARCHAR(30)")
	private String bisProPerty;

	/**
	 * 主要動用幣別
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 現請額度幣別<br/>
	 * (
	 */
	@Size(max = 3)
	@Column(name = "BISAPPLYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String bisApplyCurr;

	/**
	 * 新做/增額之額度
	 * <p/>
	 * 資料來源：額度明細表主檔單位：元<br/>
	 * 新做：currentApplyAmt現請額度增額：以”現請額度幣別”為主currentApplyAmt現請額度- LV2Amt前准批覆授信額度
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BISAPPLYAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bisApplyAmt;

	/**
	 * 匯率
	 * <p/>
	 * (：額度幣別兌台幣匯率<br/>
	 * MIS.RATETBL會計結帳匯率
	 */
	@Digits(integer = 9, fraction = 5, groups = Check.class)
	@Column(name = "BISTWDRATE", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal bisTwdRate;

	/**
	 * 表內外
	 * <p/>
	 * (：2種選項：(表內(表外<br/>
	 * 會計科目前四碼這些是表外<br/>
	 * 1482, 1760,1761,1762,1769
	 */
	@Size(max = 2)
	@Column(name = "BISSHEETITEM", length = 2, columnDefinition = "VARCHAR(2)")
	private String bisSheetItem;

	/**
	 * 信用轉換係數
	 * <p/>
	 * (：<br/>
	 * 當(表內/表外科目為表外時，<br/>
	 * 3種選項：(20%、(50%、(100%
	 */
	@Digits(integer = 7, fraction = 2, groups = Check.class)
	@Column(name = "BISCCF", columnDefinition = "DECIMAL(7,2)")
	private BigDecimal bisCcf;

	/**
	 * 合格擔保品抵減額
	 * <p/>
	 * (<br/>
	 * 資料來源為額度明細表>九、風險權數
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BISCOLLAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bisCollAmt;

	/**
	 * 風險抵減後暴險額
	 * <p/>
	 * (<br/>
	 * 資料來源為額度明細表>九、風險權數
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BISRSKAMT1", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bisRskAmt1;

	/**
	 * 授信收益率
	 * <p/>
	 * (：自額度明細表>八、RWA報酬率表引入<br/>
	 * 信收益率以利息收入、手續費及帳管費等歸屬事業群收入，加總依額度換算為年化之收益率，另或有性收入不計(懲罰性收益、違約金、未達承諾事項之利率等)。
	 */
	@Digits(integer = 8, fraction = 5, groups = Check.class)
	@Column(name = "BISINCOMERATE", columnDefinition = "DECIMAL(8,5)")
	private BigDecimal bisIncomeRate;

	/**
	 * FTP
	 * <p/>
	 * (：自額度明細表>八、RWA報酬率表引入<br/>
	 * 授信天期對應之FTP請以BTT(交易代號0190)或資料倉儲查詢，雜幣請以本行借入資金成本估算，並以授信條件中最常授信天期所對應之FTP填寫(
	 * 授信期限每筆借款最常180天，請填寫對應180天之FTP，不考慮客戶未來實際動撥天期)。<br/>
	 * 2.若該額度”全數”皆為保證額度，對應之FTP請填寫0
	 */
	@Digits(integer = 8, fraction = 5, groups = Check.class)
	@Column(name = "BISFTPRATE", columnDefinition = "DECIMAL(8,5)")
	private BigDecimal bisFtpRate;

	/**
	 * 營運成本
	 * <p/>
	 * (：%<br/>
	 * 資料來源為授審處於BTT輸入，中心主機table為 LN.LNF07A，倉儲報表為DW_LNAI370R
	 */
	@Digits(integer = 9, fraction = 5, groups = Check.class)
	@Column(name = "BISBANKWORKCOST", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal bisBankWorkCost;

	/**
	 * 預期損失率
	 * <p/>
	 * 資料來源為授審處於BTT輸入，中心主機table為 LN.LNF07A，倉儲報表為DW_LNAI370R
	 */
	@Digits(integer = 9, fraction = 5, groups = Check.class)
	@Column(name = "BISEXPTLOSS", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal bisExptLoss;

	/**
	 * 企業模型評等各等級擬制呆帳損失差異率(表訂)
	 * <p/>
	 * 資料來源為授審處於BTT輸入，中心主機table為 LN.LNF07A，倉儲報表為DW_LNAI370R
	 */
	@Digits(integer = 9, fraction = 5, groups = Check.class)
	@Column(name = "BISBADDEBTEXP", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal bisBadDebtExp;

	/**
	 * 風險成本(預期損失)
	 * <p/>
	 * ⑪：%<br/>
	 * 資料來源為授審處於BTT輸入，中心主機table為 LN.LNF07A，倉儲報表為DW_LNAI370R<br/>
	 * 風險成本(預期損失)：請加總【預期損失率】及【企業模型評等各等級擬制呆帳損失差異率(表訂)】。
	 */
	@Digits(integer = 9, fraction = 5, groups = Check.class)
	@Column(name = "BISRISKCOST", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal bisRiskCost;

	/**
	 * 風險調整後收益
	 * <p/>
	 * ⑭：(⑬要不要帶入?要不要BY額度拆比例)<br/>
	 * (敘做收益率– (FTP –(營運成本–⑪風險成本(預期損失)
	 */
	@Digits(integer = 17, fraction = 5, groups = Check.class)
	@Column(name = "BISRISKADJRETURN", columnDefinition = "DECIMAL(17,5)")
	private BigDecimal bisRiskAdjReturn;

	/**
	 * 抵減後風險權數
	 * <p/>
	 * ⑮資料來源為額度明細表>九、風險權數
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "BISRITEMD", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal bisRItemD;

	/**
	 * 風險抵減後風險性資產
	 * <p/>
	 * ⑯：<br/>
	 * 資料來源為額度明細表>九、風險權數
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BISRWA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bisRwa;

	/**
	 * RORWA
	 * <p/>
	 * ⑰：%<br/>
	 * ⑭風險調整後收益/ ⑮抵減後風險權數
	 */
	@Digits(integer = 17, fraction = 5, groups = Check.class)
	@Column(name = "BISRORWA", columnDefinition = "DECIMAL(17,5)")
	private BigDecimal bisRorwa;

	/**
	 * RAROC
	 * <p/>
	 * ⑱：<br/>
	 * RAROC= ⑰RORWA / 標準法資本要求
	 */
	@Digits(integer = 17, fraction = 5, groups = Check.class)
	@Column(name = "BISRAROC", columnDefinition = "DECIMAL(17,5)")
	private BigDecimal bisRaroc;

	/**
	 * BIS影響數
	 * <p/>
	 * ⑲：<br/>
	 * 〔自有資本/(全行風險性資產+⑯風險抵減後風險性資產)〕- (自有資本/全行風險性資產)
	 */
	@Digits(integer = 10, fraction = 5, groups = Check.class)
	@Column(name = "BISIMPACTNUM", columnDefinition = "DECIMAL(10,5)")
	private BigDecimal bisImpactNum;

	/**
	 * 標準法資本要求年度
	 * <p/>
	 * YYYY
	 */
	@Size(max = 4)
	@Column(name = "BISCAPITALREQYEAR", length = 4, columnDefinition = "CHAR(4)")
	private String bisCapitalReqYear;

	/**
	 * 標準法資本要求
	 * <p/>
	 * %<br/>
	 * 年度 <br/>
	 * 111年、112年、113年、114年~<br/>
	 * 標準法資本要求<br/>
	 * 12.5% 、13.5%、14.0%、14.5%
	 */
	@Digits(integer = 9, fraction = 4, groups = Check.class)
	@Column(name = "BISCAPITALREQ", columnDefinition = "DECIMAL(9,4)")
	private BigDecimal bisCapitalReq;

	/**
	 * 自有資本
	 * <p/>
	 * 單位：元<br/>
	 * 自有資本及全行風險性資產資料來源為會計處每月15日自結報表<br/>
	 * 請設計檔案上傳功能，並抓取自有資本(E46)及風險性資產(E61)兩欄
	 */
	@Digits(integer = 31, fraction = 2, groups = Check.class)
	@Column(name = "BISSELFCAPITAL", columnDefinition = "DECIMAL(25,2)")
	private BigDecimal bisSelfCapital;

	/**
	 * 全行風險性資產
	 * <p/>
	 * 單位：元<br/>
	 * 自有資本及全行風險性資產資料來源為會計處每月15日自結報表<br/>
	 * 請設計檔案上傳功能，並抓取自有資本(E46)及風險性資產(E61)兩欄
	 */
	@Digits(integer = 31, fraction = 2, groups = Check.class)
	@Column(name = "BISMEGARWA", columnDefinition = "DECIMAL(25,2)")
	private BigDecimal bisMegaRwa;

	/**
	 * 備註
	 * <p/>
	 * (中文字數200)
	 */
	@Size(max = 600)
	@Column(name = "BISMEMO", length = 600, columnDefinition = "VARCHAR(600)")
	private String bisMemo;

	/**
	 * 檢視結果
	 * <p/>
	 * Y：需預約<br/>
	 * N：不需<br/>
	 * X：未完成計算
	 */
	@Size(max = 1)
	@Column(name = "BISRESULT", length = 1, columnDefinition = "VARCHAR(1)")
	private String bisResult;

	/** 列印順序 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "BISPRINTSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer bisPrintSeq;

	/**
	 * 抵減後風險權數加權 平均
	 * <p/>
	 * 加總現請額度*抵減後風險權數/加總現請額度
	 */
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "BISTOTRITEMD", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal bisTotRItemD;

	/**
	 * 風險抵減後風險性資產加總
	 * <p/>
	 * (台幣/元)<br/>
	 * 加總
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BISTOTRWA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bisTotRwa;

	/**
	 * RORWA加權平均
	 * <p/>
	 * 加總現請額度*RORWA/加總現請額度
	 */
	@Digits(integer = 17, fraction = 5, groups = Check.class)
	@Column(name = "BISTOTRORWA", columnDefinition = "DECIMAL(17,5)")
	private BigDecimal bisTotRorwa;

	/**
	 * RAROC加權平均
	 * <p/>
	 * 加總現請額度* RAROC /加總現請額
	 */
	@Digits(integer = 17, fraction = 5, groups = Check.class)
	@Column(name = "BISTOTRAROC", columnDefinition = "DECIMAL(17,5)")
	private BigDecimal bisTotRaroc;

	/**
	 * BIS影響數加總
	 * <p/>
	 * 加總
	 */
	@Digits(integer = 10, fraction = 5, groups = Check.class)
	@Column(name = "BISTOTIMPACTNUM", columnDefinition = "DECIMAL(10,5)")
	private BigDecimal bisTotImpactNum;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** BIS評估表版本_大版 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "BISVER1", columnDefinition = "DECIMAL(4,0)")
	private Integer bisVer1;

	/** BIS評估表版本_小版 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "BISVER2", columnDefinition = "DECIMAL(4,0)")
	private Integer bisVer2;

	/**
	 * 授信收益率加權平均
	 * <p/>
	 * (：自額度明細表>八、RWA報酬率表引入<br/>
	 * 信收益率以利息收入、手續費及帳管費等歸屬事業群收入，加總依額度換算為年化之收益率，另或有性收入不計(懲罰性收益、違約金、未達承諾事項之利率等)。
	 */
	@Digits(integer = 8, fraction = 5, groups = Check.class)
	@Column(name = "BISTOTINCOMERATE", columnDefinition = "DECIMAL(8,5)")
	private BigDecimal bisTotIncomeRate;

	/**
	 * 非授信收益預估對象
	 */
	@Size(max = 2)
	@Column(name = "BISESTIMATEDTYPE", length = 2, columnDefinition = "VARCHAR(2)")
	private String bisEstimatedType;

	/**
	 * 預估非授信收益率
	 */
	@Digits(integer = 17, fraction = 5, groups = Check.class)
	@Column(name = "BISESTIMATEDRETURN", columnDefinition = "DECIMAL(17,5)")
	private BigDecimal bisEstimatedReturn;

	/**
	 * 資料基準年月-年
	 */
	@Size(max = 4)
	@Column(name = "BISESTIMATEDDATEYEAR", length = 4, columnDefinition = "CHAR(4)")
	private String bisEstimatedDateYear;

	/**
	 * 資料基準年月-月
	 */
	@Size(max = 2)
	@Column(name = "BISESTIMATEDDATEMONTH", length = 2, columnDefinition = "CHAR(2)")
	private String bisEstimatedDateMonth;

	/**
	 * 近一年非授信貢獻度
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BISNONELOANPROFIT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bisNoneLoanProfit;

	/**
	 * 授信餘額
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BISLOANBAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bisLoanBal;

	/**
	 * 新作增額額度
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BISFACTAMTINCREASE", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bisFactAmtIncrease;

	/**
	 * 風險調整後收益_集團(借款人+集團企業)
	 * <p/>
	 * ⑭：(⑬要不要帶入?要不要BY額度拆比例)<br/>
	 * (敘做收益率– (FTP –(營運成本–⑪風險成本(預期損失)
	 */
	@Digits(integer = 17, fraction = 5, groups = Check.class)
	@Column(name = "BISRISKADJRETURN_1", columnDefinition = "DECIMAL(17,5)")
	private BigDecimal bisRiskAdjReturn_1;

	/**
	 * RORWA_集團(借款人+集團企業)
	 * <p/>
	 * ⑰：%<br/>
	 * ⑭風險調整後收益/ ⑮抵減後風險權數
	 */
	@Digits(integer = 17, fraction = 5, groups = Check.class)
	@Column(name = "BISRORWA_1", columnDefinition = "DECIMAL(17,5)")
	private BigDecimal bisRorwa_1;

	/**
	 * RAROC_集團(借款人+集團企業)
	 * <p/>
	 * ⑱：<br/>
	 * RAROC= ⑰RORWA / 標準法資本要求
	 */
	@Digits(integer = 17, fraction = 5, groups = Check.class)
	@Column(name = "BISRAROC_1", columnDefinition = "DECIMAL(17,5)")
	private BigDecimal bisRaroc_1;

	/**
	 * RORWA加權平均_集團(借款人+集團企業)
	 * <p/>
	 * 加總現請額度*RORWA/加總現請額度
	 */
	@Digits(integer = 17, fraction = 5, groups = Check.class)
	@Column(name = "BISTOTRORWA_1", columnDefinition = "DECIMAL(17,5)")
	private BigDecimal bisTotRorwa_1;

	/**
	 * RAROC加權平均_集團(借款人+集團企業)
	 * <p/>
	 * 加總現請額度* RAROC /加總現請額
	 */
	@Digits(integer = 17, fraction = 5, groups = Check.class)
	@Column(name = "BISTOTRAROC_1", columnDefinition = "DECIMAL(17,5)")
	private BigDecimal bisTotRaroc_1;

	/**
	 * 預估非授信收益率_集團(借款人+集團企業)
	 */
	@Digits(integer = 17, fraction = 5, groups = Check.class)
	@Column(name = "BISESTIMATEDRETURN_1", columnDefinition = "DECIMAL(17,5)")
	private BigDecimal bisEstimatedReturn_1;

	/**
	 * 近一年非授信貢獻度_集團(借款人+集團企業)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BISNONELOANPROFIT_1", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bisNoneLoanProfit_1;

	/**
	 * 授信餘額_集團(借款人+集團企業)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BISLOANBAL_1", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bisLoanBal_1;

	/**
	 * 新作增額額度_集團(借款人+集團企業)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BISFACTAMTINCREASE_1", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bisFactAmtIncrease_1;

	/**
	 * 成本類別
	 */
	@Size(max = 4)
	@Column(name = "BISCOSTRATETYPE", length = 2, columnDefinition = "CHAR(2)")
	private String bisCostRateType;

	/** 本額度僅有保證科目、應收信用狀款項科目 **/
	@Size(max = 2)
	@Column(name = "ONLYGUAR_S25A", length = 2, columnDefinition = "VARCHAR(2)")
	private String onlyGuar_s25a;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得文件編號
	 * <p/>
	 * L120M01A.mainId
	 */
	public String getMainId() {
		return this.mainId;
	}

	/**
	 * 設定文件編號
	 * <p/>
	 * L120M01A.mainId
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得本案授信戶統編
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getBisCustId_s25a() {
		return this.bisCustId_s25a;
	}

	/**
	 * 設定本案授信戶統編
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setBisCustId_s25a(String value) {
		this.bisCustId_s25a = value;
	}

	/**
	 * 取得本案授信戶重複序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getBisDupNo_s25a() {
		return this.bisDupNo_s25a;
	}

	/**
	 * 設定本案授信戶重複序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setBisDupNo_s25a(String value) {
		this.bisDupNo_s25a = value;
	}

	/**
	 * 取得本案授信戶額度序號
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * (
	 */
	public String getBisCntrNo_s25a() {
		return this.bisCntrNo_s25a;
	}

	/**
	 * 設定本案授信戶額度序號
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * (
	 **/
	public void setBisCntrNo_s25a(String value) {
		this.bisCntrNo_s25a = value;
	}

	/**
	 * 取得性質
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getBisProPerty() {
		return this.bisProPerty;
	}

	/**
	 * 設定性質
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setBisProPerty(String value) {
		this.bisProPerty = value;
	}

	/**
	 * 取得主要動用幣別
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 現請額度幣別<br/>
	 * (
	 */
	public String getBisApplyCurr() {
		return this.bisApplyCurr;
	}

	/**
	 * 設定主要動用幣別
	 * <p/>
	 * 資料來源：額度明細表主檔<br/>
	 * 現請額度幣別<br/>
	 * (
	 **/
	public void setBisApplyCurr(String value) {
		this.bisApplyCurr = value;
	}

	/**
	 * 取得新做/增額之額度
	 * <p/>
	 * 資料來源：額度明細表主檔單位：元<br/>
	 * 新做：currentApplyAmt現請額度增額：以”現請額度幣別”為主currentApplyAmt現請額度- LV2Amt前准批覆授信額度
	 */
	public BigDecimal getBisApplyAmt() {
		return this.bisApplyAmt;
	}

	/**
	 * 設定新做/增額之額度
	 * <p/>
	 * 資料來源：額度明細表主檔單位：元<br/>
	 * 新做：currentApplyAmt現請額度增額：以”現請額度幣別”為主currentApplyAmt現請額度- LV2Amt前准批覆授信額度
	 **/
	public void setBisApplyAmt(BigDecimal value) {
		this.bisApplyAmt = value;
	}

	/**
	 * 取得匯率
	 * <p/>
	 * (：額度幣別兌台幣匯率<br/>
	 * MIS.RATETBL會計結帳匯率
	 */
	public BigDecimal getBisTwdRate() {
		return this.bisTwdRate;
	}

	/**
	 * 設定匯率
	 * <p/>
	 * (：額度幣別兌台幣匯率<br/>
	 * MIS.RATETBL會計結帳匯率
	 **/
	public void setBisTwdRate(BigDecimal value) {
		this.bisTwdRate = value;
	}

	/**
	 * 取得表內外
	 * <p/>
	 * (：2種選項：(表內(表外<br/>
	 * 會計科目前四碼這些是表外<br/>
	 * 1482, 1760,1761,1762,1769
	 */
	public String getBisSheetItem() {
		return this.bisSheetItem;
	}

	/**
	 * 設定表內外
	 * <p/>
	 * (：2種選項：(表內(表外<br/>
	 * 會計科目前四碼這些是表外<br/>
	 * 1482, 1760,1761,1762,1769
	 **/
	public void setBisSheetItem(String value) {
		this.bisSheetItem = value;
	}

	/**
	 * 取得信用轉換係數
	 * <p/>
	 * (：<br/>
	 * 當(表內/表外科目為表外時，<br/>
	 * 3種選項：(20%、(50%、(100%
	 */
	public BigDecimal getBisCcf() {
		return this.bisCcf;
	}

	/**
	 * 設定信用轉換係數
	 * <p/>
	 * (：<br/>
	 * 當(表內/表外科目為表外時，<br/>
	 * 3種選項：(20%、(50%、(100%
	 **/
	public void setBisCcf(BigDecimal value) {
		this.bisCcf = value;
	}

	/**
	 * 取得合格擔保品抵減額
	 * <p/>
	 * (<br/>
	 * 資料來源為額度明細表>九、風險權數
	 */
	public BigDecimal getBisCollAmt() {
		return this.bisCollAmt;
	}

	/**
	 * 設定合格擔保品抵減額
	 * <p/>
	 * (<br/>
	 * 資料來源為額度明細表>九、風險權數
	 **/
	public void setBisCollAmt(BigDecimal value) {
		this.bisCollAmt = value;
	}

	/**
	 * 取得風險抵減後暴險額
	 * <p/>
	 * (<br/>
	 * 資料來源為額度明細表>九、風險權數
	 */
	public BigDecimal getBisRskAmt1() {
		return this.bisRskAmt1;
	}

	/**
	 * 設定風險抵減後暴險額
	 * <p/>
	 * (<br/>
	 * 資料來源為額度明細表>九、風險權數
	 **/
	public void setBisRskAmt1(BigDecimal value) {
		this.bisRskAmt1 = value;
	}

	/**
	 * 取得授信收益率
	 * <p/>
	 * (：自額度明細表>八、RWA報酬率表引入<br/>
	 * 信收益率以利息收入、手續費及帳管費等歸屬事業群收入，加總依額度換算為年化之收益率，另或有性收入不計(懲罰性收益、違約金、未達承諾事項之利率等)。
	 */
	public BigDecimal getBisIncomeRate() {
		return this.bisIncomeRate;
	}

	/**
	 * 設定授信收益率
	 * <p/>
	 * (：自額度明細表>八、RWA報酬率表引入<br/>
	 * 信收益率以利息收入、手續費及帳管費等歸屬事業群收入，加總依額度換算為年化之收益率，另或有性收入不計(懲罰性收益、違約金、未達承諾事項之利率等)。
	 **/
	public void setBisIncomeRate(BigDecimal value) {
		this.bisIncomeRate = value;
	}

	/**
	 * 取得FTP
	 * <p/>
	 * (：自額度明細表>八、RWA報酬率表引入<br/>
	 * 授信天期對應之FTP請以BTT(交易代號0190)或資料倉儲查詢，雜幣請以本行借入資金成本估算，並以授信條件中最常授信天期所對應之FTP填寫(
	 * 授信期限每筆借款最常180天，請填寫對應180天之FTP，不考慮客戶未來實際動撥天期)。<br/>
	 * 2.若該額度”全數”皆為保證額度，對應之FTP請填寫0
	 */
	public BigDecimal getBisFtpRate() {
		return this.bisFtpRate;
	}

	/**
	 * 設定FTP
	 * <p/>
	 * (：自額度明細表>八、RWA報酬率表引入<br/>
	 * 授信天期對應之FTP請以BTT(交易代號0190)或資料倉儲查詢，雜幣請以本行借入資金成本估算，並以授信條件中最常授信天期所對應之FTP填寫(
	 * 授信期限每筆借款最常180天，請填寫對應180天之FTP，不考慮客戶未來實際動撥天期)。<br/>
	 * 2.若該額度”全數”皆為保證額度，對應之FTP請填寫0
	 **/
	public void setBisFtpRate(BigDecimal value) {
		this.bisFtpRate = value;
	}

	/**
	 * 取得營運成本
	 * <p/>
	 * (：%<br/>
	 * 資料來源為授審處於BTT輸入，中心主機table為 LN.LNF07A，倉儲報表為DW_LNAI370R
	 */
	public BigDecimal getBisBankWorkCost() {
		return this.bisBankWorkCost;
	}

	/**
	 * 設定營運成本
	 * <p/>
	 * (：%<br/>
	 * 資料來源為授審處於BTT輸入，中心主機table為 LN.LNF07A，倉儲報表為DW_LNAI370R
	 **/
	public void setBisBankWorkCost(BigDecimal value) {
		this.bisBankWorkCost = value;
	}

	/**
	 * 取得預期損失率
	 * <p/>
	 * 資料來源為授審處於BTT輸入，中心主機table為 LN.LNF07A，倉儲報表為DW_LNAI370R
	 */
	public BigDecimal getBisExptLoss() {
		return this.bisExptLoss;
	}

	/**
	 * 設定預期損失率
	 * <p/>
	 * 資料來源為授審處於BTT輸入，中心主機table為 LN.LNF07A，倉儲報表為DW_LNAI370R
	 **/
	public void setBisExptLoss(BigDecimal value) {
		this.bisExptLoss = value;
	}

	/**
	 * 取得企業模型評等各等級擬制呆帳損失差異率(表訂)
	 * <p/>
	 * 資料來源為授審處於BTT輸入，中心主機table為 LN.LNF07A，倉儲報表為DW_LNAI370R
	 */
	public BigDecimal getBisBadDebtExp() {
		return this.bisBadDebtExp;
	}

	/**
	 * 設定企業模型評等各等級擬制呆帳損失差異率(表訂)
	 * <p/>
	 * 資料來源為授審處於BTT輸入，中心主機table為 LN.LNF07A，倉儲報表為DW_LNAI370R
	 **/
	public void setBisBadDebtExp(BigDecimal value) {
		this.bisBadDebtExp = value;
	}

	/**
	 * 取得風險成本(預期損失)
	 * <p/>
	 * ⑪：%<br/>
	 * 資料來源為授審處於BTT輸入，中心主機table為 LN.LNF07A，倉儲報表為DW_LNAI370R<br/>
	 * 風險成本(預期損失)：請加總【預期損失率】及【企業模型評等各等級擬制呆帳損失差異率(表訂)】。
	 */
	public BigDecimal getBisRiskCost() {
		return this.bisRiskCost;
	}

	/**
	 * 設定風險成本(預期損失)
	 * <p/>
	 * ⑪：%<br/>
	 * 資料來源為授審處於BTT輸入，中心主機table為 LN.LNF07A，倉儲報表為DW_LNAI370R<br/>
	 * 風險成本(預期損失)：請加總【預期損失率】及【企業模型評等各等級擬制呆帳損失差異率(表訂)】。
	 **/
	public void setBisRiskCost(BigDecimal value) {
		this.bisRiskCost = value;
	}

	/**
	 * 取得風險調整後收益
	 * <p/>
	 * ⑭：(⑬要不要帶入?要不要BY額度拆比例)<br/>
	 * (敘做收益率– (FTP –(營運成本–⑪風險成本(預期損失)
	 */
	public BigDecimal getBisRiskAdjReturn() {
		return this.bisRiskAdjReturn;
	}

	/**
	 * 設定風險調整後收益
	 * <p/>
	 * ⑭：(⑬要不要帶入?要不要BY額度拆比例)<br/>
	 * (敘做收益率– (FTP –(營運成本–⑪風險成本(預期損失)
	 **/
	public void setBisRiskAdjReturn(BigDecimal value) {
		this.bisRiskAdjReturn = value;
	}

	/**
	 * 取得抵減後風險權數
	 * <p/>
	 * ⑮資料來源為額度明細表>九、風險權數
	 */
	public BigDecimal getBisRItemD() {
		return this.bisRItemD;
	}

	/**
	 * 設定抵減後風險權數
	 * <p/>
	 * ⑮資料來源為額度明細表>九、風險權數
	 **/
	public void setBisRItemD(BigDecimal value) {
		this.bisRItemD = value;
	}

	/**
	 * 取得風險抵減後風險性資產
	 * <p/>
	 * ⑯：<br/>
	 * 資料來源為額度明細表>九、風險權數
	 */
	public BigDecimal getBisRwa() {
		return this.bisRwa;
	}

	/**
	 * 設定風險抵減後風險性資產
	 * <p/>
	 * ⑯：<br/>
	 * 資料來源為額度明細表>九、風險權數
	 **/
	public void setBisRwa(BigDecimal value) {
		this.bisRwa = value;
	}

	/**
	 * 取得RORWA
	 * <p/>
	 * ⑰：%<br/>
	 * ⑭風險調整後收益/ ⑮抵減後風險權數
	 */
	public BigDecimal getBisRorwa() {
		return this.bisRorwa;
	}

	/**
	 * 設定RORWA
	 * <p/>
	 * ⑰：%<br/>
	 * ⑭風險調整後收益/ ⑮抵減後風險權數
	 **/
	public void setBisRorwa(BigDecimal value) {
		this.bisRorwa = value;
	}

	/**
	 * 取得RAROC
	 * <p/>
	 * ⑱：<br/>
	 * RAROC= ⑰RORWA / 標準法資本要求
	 */
	public BigDecimal getBisRaroc() {
		return this.bisRaroc;
	}

	/**
	 * 設定RAROC
	 * <p/>
	 * ⑱：<br/>
	 * RAROC= ⑰RORWA / 標準法資本要求
	 **/
	public void setBisRaroc(BigDecimal value) {
		this.bisRaroc = value;
	}

	/**
	 * 取得BIS影響數
	 * <p/>
	 * ⑲：<br/>
	 * 〔自有資本/(全行風險性資產+⑯風險抵減後風險性資產)〕- (自有資本/全行風險性資產)
	 */
	public BigDecimal getBisImpactNum() {
		return this.bisImpactNum;
	}

	/**
	 * 設定BIS影響數
	 * <p/>
	 * ⑲：<br/>
	 * 〔自有資本/(全行風險性資產+⑯風險抵減後風險性資產)〕- (自有資本/全行風險性資產)
	 **/
	public void setBisImpactNum(BigDecimal value) {
		this.bisImpactNum = value;
	}

	/**
	 * 取得標準法資本要求年度
	 * <p/>
	 * YYYY
	 */
	public String getBisCapitalReqYear() {
		return this.bisCapitalReqYear;
	}

	/**
	 * 設定標準法資本要求年度
	 * <p/>
	 * YYYY
	 **/
	public void setBisCapitalReqYear(String value) {
		this.bisCapitalReqYear = value;
	}

	/**
	 * 取得標準法資本要求
	 * <p/>
	 * %<br/>
	 * 年度 <br/>
	 * 111年、112年、113年、114年~<br/>
	 * 標準法資本要求<br/>
	 * 12.5% 、13.5%、14.0%、14.5%
	 */
	public BigDecimal getBisCapitalReq() {
		return this.bisCapitalReq;
	}

	/**
	 * 設定標準法資本要求
	 * <p/>
	 * %<br/>
	 * 年度 <br/>
	 * 111年、112年、113年、114年~<br/>
	 * 標準法資本要求<br/>
	 * 12.5% 、13.5%、14.0%、14.5%
	 **/
	public void setBisCapitalReq(BigDecimal value) {
		this.bisCapitalReq = value;
	}

	/**
	 * 取得自有資本
	 * <p/>
	 * 自有資本及全行風險性資產資料來源為會計處每月15日自結報表<br/>
	 * 請設計檔案上傳功能，並抓取自有資本(E46)及風險性資產(E61)兩欄
	 */
	public BigDecimal getBisSelfCapital() {
		return this.bisSelfCapital;
	}

	/**
	 * 設定自有資本
	 * <p/>
	 * 自有資本及全行風險性資產資料來源為會計處每月15日自結報表<br/>
	 * 請設計檔案上傳功能，並抓取自有資本(E46)及風險性資產(E61)兩欄
	 **/
	public void setBisSelfCapital(BigDecimal value) {
		this.bisSelfCapital = value;
	}

	/**
	 * 取得全行風險性資產
	 * <p/>
	 * 自有資本及全行風險性資產資料來源為會計處每月15日自結報表<br/>
	 * 請設計檔案上傳功能，並抓取自有資本(E46)及風險性資產(E61)兩欄
	 */
	public BigDecimal getBisMegaRwa() {
		return this.bisMegaRwa;
	}

	/**
	 * 設定全行風險性資產
	 * <p/>
	 * 自有資本及全行風險性資產資料來源為會計處每月15日自結報表<br/>
	 * 請設計檔案上傳功能，並抓取自有資本(E46)及風險性資產(E61)兩欄
	 **/
	public void setBisMegaRwa(BigDecimal value) {
		this.bisMegaRwa = value;
	}

	/**
	 * 取得備註
	 * <p/>
	 * (中文字數200)
	 */
	public String getBisMemo() {
		return this.bisMemo;
	}

	/**
	 * 設定備註
	 * <p/>
	 * (中文字數200)
	 **/
	public void setBisMemo(String value) {
		this.bisMemo = value;
	}

	/**
	 * 取得檢視結果
	 * <p/>
	 * Y：需預約<br/>
	 * N：不需<br/>
	 * X：未完成計算
	 */
	public String getBisResult() {
		return this.bisResult;
	}

	/**
	 * 設定檢視結果
	 * <p/>
	 * Y：需預約<br/>
	 * N：不需<br/>
	 * X：未完成計算
	 **/
	public void setBisResult(String value) {
		this.bisResult = value;
	}

	/** 取得列印順序 **/
	public Integer getBisPrintSeq() {
		return this.bisPrintSeq;
	}

	/** 設定列印順序 **/
	public void setBisPrintSeq(Integer value) {
		this.bisPrintSeq = value;
	}

	/**
	 * 取得抵減後風險權數加權 平均
	 * <p/>
	 * 加總現請額度*抵減後風險權數/加總現請額度
	 */
	public BigDecimal getBisTotRItemD() {
		return this.bisTotRItemD;
	}

	/**
	 * 設定抵減後風險權數加權 平均
	 * <p/>
	 * 加總現請額度*抵減後風險權數/加總現請額度
	 **/
	public void setBisTotRItemD(BigDecimal value) {
		this.bisTotRItemD = value;
	}

	/**
	 * 取得風險抵減後風險性資產加總
	 * <p/>
	 * (台幣/元)<br/>
	 * 加總
	 */
	public BigDecimal getBisTotRwa() {
		return this.bisTotRwa;
	}

	/**
	 * 設定風險抵減後風險性資產加總
	 * <p/>
	 * (台幣/元)<br/>
	 * 加總
	 **/
	public void setBisTotRwa(BigDecimal value) {
		this.bisTotRwa = value;
	}

	/**
	 * 取得RORWA加權平均
	 * <p/>
	 * 加總現請額度*RORWA/加總現請額度
	 */
	public BigDecimal getBisTotRorwa() {
		return this.bisTotRorwa;
	}

	/**
	 * 設定RORWA加權平均
	 * <p/>
	 * 加總現請額度*RORWA/加總現請額度
	 **/
	public void setBisTotRorwa(BigDecimal value) {
		this.bisTotRorwa = value;
	}

	/**
	 * 取得RAROC加權平均
	 * <p/>
	 * 加總現請額度* RAROC /加總現請額
	 */
	public BigDecimal getBisTotRaroc() {
		return this.bisTotRaroc;
	}

	/**
	 * 設定RAROC加權平均
	 * <p/>
	 * 加總現請額度* RAROC /加總現請額
	 **/
	public void setBisTotRaroc(BigDecimal value) {
		this.bisTotRaroc = value;
	}

	/**
	 * 取得BIS影響數加總
	 * <p/>
	 * 加總
	 */
	public BigDecimal getBisTotImpactNum() {
		return this.bisTotImpactNum;
	}

	/**
	 * 設定BIS影響數加總
	 * <p/>
	 * 加總
	 **/
	public void setBisTotImpactNum(BigDecimal value) {
		this.bisTotImpactNum = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 設定BIS評估表版本_大版 **/
	public void setBisVer1(Integer bisVer1) {
		this.bisVer1 = bisVer1;
	}

	/** 取得BIS評估表版本_大版 **/
	public Integer getBisVer1() {
		return bisVer1;
	}

	/** 設定BIS評估表版本_小版 **/
	public void setBisVer2(Integer bisVer2) {
		this.bisVer2 = bisVer2;
	}

	/** 取得BIS評估表版本_小版 **/
	public Integer getBisVer2() {
		return bisVer2;
	}

	/** 設定授信收益率加權平均 **/
	public void setBisTotIncomeRate(BigDecimal bisTotIncomeRate) {
		this.bisTotIncomeRate = bisTotIncomeRate;
	}

	/** 取得授信收益率加權平均 **/
	public BigDecimal getBisTotIncomeRate() {
		return bisTotIncomeRate;
	}

	/** 設定非授信收益預估對象 **/
	public void setBisEstimatedType(String bisEstimatedType) {
		this.bisEstimatedType = bisEstimatedType;
	}

	/** 取得非授信收益預估對象 **/
	public String getBisEstimatedType() {
		return bisEstimatedType;
	}

	/** 設定預估非授信收益率 **/
	public void setBisEstimatedReturn(BigDecimal bisEstimatedReturn) {
		this.bisEstimatedReturn = bisEstimatedReturn;
	}

	/** 取得預估非授信收益率 **/
	public BigDecimal getBisEstimatedReturn() {
		return bisEstimatedReturn;
	}

	/** 設定資料基準年月-年 **/
	public void setBisEstimatedDateYear(String bisEstimatedDateYear) {
		this.bisEstimatedDateYear = bisEstimatedDateYear;
	}

	/** 取得資料基準年月-年 **/
	public String getBisEstimatedDateYear() {
		return bisEstimatedDateYear;
	}

	/** 設定資料基準年月-月 **/
	public void setBisEstimatedDateMonth(String bisEstimatedDateMonth) {
		this.bisEstimatedDateMonth = bisEstimatedDateMonth;
	}

	/** 取得資料基準年月-月 **/
	public String getBisEstimatedDateMonth() {
		return bisEstimatedDateMonth;
	}

	/** 設定近一年非授信貢獻度 **/
	public void setBisNoneLoanProfit(BigDecimal bisNoneLoanProfit) {
		this.bisNoneLoanProfit = bisNoneLoanProfit;
	}

	/** 取得近一年非授信貢獻度 **/
	public BigDecimal getBisNoneLoanProfit() {
		return bisNoneLoanProfit;
	}

	/** 設定授信餘額 **/
	public void setBisLoanBal(BigDecimal bisLoanBal) {
		this.bisLoanBal = bisLoanBal;
	}

	/** 取得授信餘額 **/
	public BigDecimal getBisLoanBal() {
		return bisLoanBal;
	}

	/** 設定新作增額額度 **/
	public void setBisFactAmtIncrease(BigDecimal bisFactAmtIncrease) {
		this.bisFactAmtIncrease = bisFactAmtIncrease;
	}

	/** 取得新作增額額度 **/
	public BigDecimal getBisFactAmtIncrease() {
		return bisFactAmtIncrease;
	}

	/**
	 * 取得風險調整後收益_集團(借款人+集團企業)
	 * <p/>
	 * ⑭：(⑬要不要帶入?要不要BY額度拆比例)<br/>
	 * (敘做收益率– (FTP –(營運成本–⑪風險成本(預期損失)
	 */
	public BigDecimal getBisRiskAdjReturn_1() {
		return this.bisRiskAdjReturn_1;
	}

	/**
	 * 設定風險調整後收益_集團(借款人+集團企業)
	 * <p/>
	 * ⑭：(⑬要不要帶入?要不要BY額度拆比例)<br/>
	 * (敘做收益率– (FTP –(營運成本–⑪風險成本(預期損失)
	 **/
	public void setBisRiskAdjReturn_1(BigDecimal value) {
		this.bisRiskAdjReturn_1 = value;
	}

	/**
	 * 取得RORWA_集團(借款人+集團企業)
	 * <p/>
	 * ⑰：%<br/>
	 * ⑭風險調整後收益/ ⑮抵減後風險權數
	 */
	public BigDecimal getBisRorwa_1() {
		return this.bisRorwa_1;
	}

	/**
	 * 設定RORWA_集團(借款人+集團企業)
	 * <p/>
	 * ⑰：%<br/>
	 * ⑭風險調整後收益/ ⑮抵減後風險權數
	 **/
	public void setBisRorwa_1(BigDecimal value) {
		this.bisRorwa_1 = value;
	}

	/**
	 * 取得RAROC_集團(借款人+集團企業)
	 * <p/>
	 * ⑱：<br/>
	 * RAROC= ⑰RORWA / 標準法資本要求
	 */
	public BigDecimal getBisRaroc_1() {
		return this.bisRaroc_1;
	}

	/**
	 * 設定RAROC_集團(借款人+集團企業)
	 * <p/>
	 * ⑱：<br/>
	 * RAROC= ⑰RORWA / 標準法資本要求
	 **/
	public void setBisRaroc_1(BigDecimal value) {
		this.bisRaroc_1 = value;
	}

	/**
	 * 取得RORWA加權平均_集團(借款人+集團企業)
	 * <p/>
	 * 加總現請額度*RORWA/加總現請額度
	 */
	public BigDecimal getBisTotRorwa_1() {
		return this.bisTotRorwa_1;
	}

	/**
	 * 設定RORWA加權平均_集團(借款人+集團企業)
	 * <p/>
	 * 加總現請額度*RORWA/加總現請額度
	 **/
	public void setBisTotRorwa_1(BigDecimal value) {
		this.bisTotRorwa_1 = value;
	}

	/**
	 * 取得RAROC加權平均_集團(借款人+集團企業)
	 * <p/>
	 * 加總現請額度* RAROC /加總現請額
	 */
	public BigDecimal getBisTotRaroc_1() {
		return this.bisTotRaroc_1;
	}

	/**
	 * 設定RAROC加權平均_集團(借款人+集團企業)
	 * <p/>
	 * 加總現請額度* RAROC /加總現請額
	 **/
	public void setBisTotRaroc_1(BigDecimal value) {
		this.bisTotRaroc_1 = value;
	}

	/** 設定預估非授信收益率_集團(借款人+集團企業) **/
	public void setBisEstimatedReturn_1(BigDecimal value) {
		this.bisEstimatedReturn_1 = value;
	}

	/** 取得預估非授信收益率_集團(借款人+集團企業) **/
	public BigDecimal getBisEstimatedReturn_1() {
		return bisEstimatedReturn_1;
	}

	/** 設定近一年非授信貢獻度_集團(借款人+集團企業) **/
	public void setBisNoneLoanProfit_1(BigDecimal value) {
		this.bisNoneLoanProfit_1 = value;
	}

	/** 取得近一年非授信貢獻度_集團(借款人+集團企業) **/
	public BigDecimal getBisNoneLoanProfit_1() {
		return bisNoneLoanProfit_1;
	}

	/** 設定授信餘額_集團(借款人+集團企業) **/
	public void setBisLoanBal_1(BigDecimal value) {
		this.bisLoanBal_1 = value;
	}

	/** 取得授信餘額_集團(借款人+集團企業) **/
	public BigDecimal getBisLoanBal_1() {
		return bisLoanBal_1;
	}

	/** 設定新作增額額度_集團(借款人+集團企業) **/
	public void setBisFactAmtIncrease_1(BigDecimal value) {
		this.bisFactAmtIncrease_1 = value;
	}

	/** 取得新作增額額度_集團(借款人+集團企業) **/
	public BigDecimal getBisFactAmtIncrease_1() {
		return bisFactAmtIncrease_1;
	}

	/** 設定成本類別 **/
	public void setBisCostRateType(String bisCostRateType) {
		this.bisCostRateType = bisCostRateType;
	}

	/** 取得成本類別 **/
	public String getBisCostRateType() {
		return bisCostRateType;
	}

	/** 設定本額度僅有保證科目、應收信用狀款項科目 **/
	public void setOnlyGuar_s25a(String onlyGuar_s25a) {
		this.onlyGuar_s25a = onlyGuar_s25a;
	}

	/** 取得本額度僅有保證科目、應收信用狀款項科目 **/
	public String getOnlyGuar_s25a() {
		return onlyGuar_s25a;
	}
}
