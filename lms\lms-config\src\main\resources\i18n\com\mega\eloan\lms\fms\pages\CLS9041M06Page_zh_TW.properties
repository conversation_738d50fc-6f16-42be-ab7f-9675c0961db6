thickbox.addMsg=\u662f\u5426\u65b0\u589e\u8cc7\u6599\uff1f
detailTitle=\u653f\u7b56\u6027\u7559\u5b78\u751f\u8cb8\u6b3e\u88dc\u8cbc\u606f\u76f8\u95dc\u5831\u8868
canotInsert=\u7121\u6cd5\u65b0\u589e\uff0c\u8acb\u5148\u522a\u9664\u6a94\u6848
uploadTxt=\u9078\u64c7\u9644\u52a0\u6a94\u6848
deleteTxt=\u522a\u9664
receipt=\u7522\u751f\u6536\u64da
close=\u95dc\u9589
#==================================================
# \u653f\u7b56\u6027\u7559\u5b78\u751f\u8cb8\u6b3e\u88dc\u8cbc\u606f\u76f8\u95dc\u5831\u8868
#==================================================
C004M01A.detailTilte=\u5831\u9001\u8cc7\u6599
C004M01A.date=\u8cc7\u6599\u65e5\u671f
C004M01A.rptType=\u5831\u8868\u985e\u5225
C004M01A.bgnDate=\u8cc7\u6599\u8d77\u65e5
C004M01A.endDate=\u8cc7\u6599\u8fc4\u65e5
C004M01A.rptDate=\u78ba\u5b9a\u5831\u9001\u65e5\u671f
C004M01A.rptName=\u5831\u8868\u540d\u7a31
C004M01A.filePath=\u9644\u52a0\u6a94\u6848\u8def\u5f91
C004M01A.fileName=\u9644\u52a0\u6a94\u6848\u540d\u7a31
C004M01A.rmk=\u5099\u8a3b
C004M01A.creator=\u5efa\u7acb\u4eba\u54e1\u865f\u78bc
C004M01A.createTime=\u5efa\u7acb\u65e5\u671f
C004M01A.updater=\u7570\u52d5\u4eba\u54e1\u865f\u78bc
C004M01A.updateTime=\u7570\u52d5\u65e5\u671f
C004M01A.confirmDelete=\u662f\u5426\u522a\u9664\u8a72\u7b46\u8cc7\u6599?
C004M01A.S1Name=\u653f\u7b56\u6027\u7559\u5b78\u751f\u8cb8\u6b3eS1\u5831\u8868
C004M01A.uploadData=\u4e0a\u50b3\u8cc7\u6599
C004M01A.delData=\u522a\u9664\u8cc7\u6599