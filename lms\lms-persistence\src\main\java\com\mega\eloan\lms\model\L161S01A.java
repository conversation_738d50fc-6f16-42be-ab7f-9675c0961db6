/* 
 * L161S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 聯貸案參貸比率一覽表主檔 **/
@NamedEntityGraph(name = "L161S01A-entity-graph", attributeNodes = { @NamedAttributeNode("l160m01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L161S01A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "uid" }))
public class L161S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L160M01A l160m01a;

	public L160M01A getL160m01a() {
		return l160m01a;
	}

	public void setL160m01a(L160M01A l160m01a) {
		this.l160m01a = l160m01a;
	}
	
	/**
	 * JOIN條件 L161S01B．動審表額度序號資料
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l161s01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L161S01B> l161s01b;

	public Set<L161S01B> getL161s01b() {
		return l161s01b;
	}

	public void setL161S01b(Set<L161S01B> l161s01b) {
		this.l161s01b = l161s01b;
	}

	/**
	 * 排序JOIN條件 L161S01B．動審表額度序號資料
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l161s01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@OrderBy("createTime")
	private List<L161S01B> l161s01bs;

	public List<L161S01B> getL161s01bs() {
		return l161s01bs;
	}

	public void setL161S01bs(List<L161S01B> l161s01bs) {
		this.l161s01bs = l161s01bs;
	}

	@ToStringExclude
	@OneToMany(mappedBy = "l161s01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L161S01C> l161s01c;

	public Set<L161S01C> getL161s01c() {
		return l161s01c;
	}

	public void setL161S01c(Set<L161S01C> l161s01c) {
		this.l161s01c = l161s01c;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 案件號碼-年度
	 * <p/>
	 * 資料來源：動用審核表
	 */
	@Column(name = "CASEYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer caseYear;

	/**
	 * 案件號碼-分行
	 * <p/>
	 * 資料來源：動用審核表
	 */
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/**
	 * 案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：動用審核表
	 */
	@Column(name = "CASESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer caseSeq;

	/**
	 * 案件號碼
	 * <p/>
	 * 資料來源：動用審核表
	 */
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/**
	 * 簽案日期
	 * <p/>
	 * 資料來源：動用審核表
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/**
	 * 案由
	 * <p/>
	 * 101/06/25調整<br/>
	 * VARCHAR(768)(VARCHAR(4096)
	 */
	@Column(name = "GIST", length = 4096, columnDefinition = "VARCHAR(4096)")
	private String gist;

	/** 幣別 **/
	@Column(name = "QUOTACURR", length = 3, columnDefinition = "CHAR(3)")
	private String quotaCurr;

	/** 總額度 **/
	@Column(name = "QUOTAAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal quotaAmt;

	/** 簽約日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "SIGNDATE", columnDefinition = "DATE")
	private Date signDate;

	/** 額度序號 **/
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 文件編號UID
	 * <p/>
	 * 102/01/17新增
	 */
	@Column(name = "UID", length = 32, columnDefinition = "CHAR(32)")
	private String uid;

	/**
	 * 統一編號
	 * <p/>
	 * 102/01/17新增
	 */
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/**
	 * 重覆序號
	 * <p/>
	 * 102/01/17新增
	 */
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 客戶名稱
	 * <p/>
	 * 102/01/17新增
	 */
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 性質
	 * <p/>
	 * 102/01/17新增<br/>
	 * 複選：<br/>
	 * 1報價(報價|13<br/>
	 * 2新作(新做|1<br/>
	 * 3增額(增額|5<br/>
	 * 4紓困(紓困|10<br/>
	 * 5協議清償(協議清償|12<br/>
	 * 6減額(減額|6<br/>
	 * 7變更條件(變更條件|3<br/>
	 * 8續約(續約|2<br/>
	 * 9提前續約(提前續約|11<br/>
	 * 10展期（不良授信案）(展期(不良授信案)|9<br/>
	 * 11流用(流用|4<br/>
	 * 12取消(取消|8<br/>
	 * 13不變(不變|7
	 */
	@Column(name = "PROPERTY", length = 30, columnDefinition = "VARCHAR(30)")
	private String property;

	/**
	 * 現請額度－幣別
	 * <p/>
	 * 102/01/17新增
	 */
	@Column(name = "CURRENTAPPLYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String currentApplyCurr;

	/**
	 * 現請額度－金額
	 * <p/>
	 * 102/01/17新增
	 */
	@Column(name = "CURRENTAPPLYAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal currentApplyAmt;

	/** 額度序號MAINID **/
	@Column(name = "CNTRMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String cntrMainId;

	/**
	 * 額度控管種類
	 * <p/>
	 * 102/01/17新增<br/>
	 * 10.一般<br/>
	 * 20.信保<br/>
	 * 30.聯貸<br/>
	 * 40.合作母<br/>
	 * 41.合作子<br/>
	 * 51.個人戶一般
	 */
	@Column(name = "SNOKIND", length = 2, columnDefinition = "CHAR(2)")
	private String snoKind;

	/**
	 * 案件性質
	 * <p/>
	 * 102/01/17新增<br/>
	 * 同業聯貸主辦<br/>
	 * 同業聯貸主辦（含自行聯貸）<br/>
	 * 同業聯貸參貸<br/>
	 * 同業聯貸參貸（含自行聯貸）<br/>
	 * 自行聯貸<br/>
	 * 一般貸款<br/>
	 * 合作業務母戶<br/>
	 * 合作業務子戶
	 */
	@Column(name = "CASETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String caseType;

	/**
	 * 聯貸比率-同業
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	@Column(name = "COBANK", length = 1, columnDefinition = "VARCHAR(1)")
	private String coBank;

	/**
	 * 聯貸比率-聯行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	@Column(name = "COBRANCH", length = 1, columnDefinition = "VARCHAR(1)")
	private String coBranch;

	/**
	 * 本案是否有同業聯貸案額度
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	@Column(name = "UNITCASE", length = 1, columnDefinition = "VARCHAR(1)")
	private String unitCase;

	/**
	 * 本行是否為管理行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	@Column(name = "UCMAINBRANCH", length = 1, columnDefinition = "VARCHAR(1)")
	private String uCMainBranch;

	/**
	 * 本案是否有同行(本行)聯貸案額度行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	@Column(name = "UNITMEGA", length = 1, columnDefinition = "VARCHAR(1)")
	private String unitMega;

	/**
	 * 本分行是否為額度管理行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	@Column(name = "UCNTBRANCH", length = 1, columnDefinition = "VARCHAR(1)")
	private String uCntBranch;

	/**
	 * 本分行是否為擔保品管理行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	@Column(name = "UCMSBRANCH", length = 1, columnDefinition = "VARCHAR(1)")
	private String uCMSBranch;

	/**
	 * 本案是否為隱名參貸
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	@Column(name = "UHIDENAME", length = 1, columnDefinition = "VARCHAR(1)")
	private String uHideName;

	/**
	 * 本案為國內聯貸/國際聯貸
	 * <p/>
	 * 102/01/17新增<br/>
	 * 國內聯貸 | A<br/>
	 * 國際聯貸 | B
	 */
	@Column(name = "UAREA", length = 1, columnDefinition = "VARCHAR(1)")
	private String uArea;

	/**
	 * 報核方式（國金部報核、分行報核）
	 * <p/>
	 * 102/01/17新增<br/>
	 * 國金部報核 | A<br/>
	 * 分行報核 | B
	 */
	@Column(name = "URP1", length = 1, columnDefinition = "VARCHAR(1)")
	private String uRP1;

	/**
	 * 報核方式（國金部對外、分行對外）
	 * <p/>
	 * 102/01/17新增<br/>
	 * 國金部對外| A<br/>
	 * 分行對外 | B
	 */
	@Column(name = "URP2", length = 1, columnDefinition = "VARCHAR(1)")
	private String uRP2;

	/**
	 * 報核方式（國金部掛帳、分行掛帳）
	 * <p/>
	 * 102/01/17新增<br/>
	 * 國金部掛帳 | A<br/>
	 * 分行掛帳 | B
	 */
	@Column(name = "URP3", length = 1, columnDefinition = "VARCHAR(1)")
	private String uRP3;

	/**
	 * 合作業務種類
	 * <p/>
	 * 102/01/17新增<br/>
	 * 非合作業務 | 0<br/>
	 * 價金履約保證 | 1<br/>
	 * 合作外匯 | 2<br/>
	 * 其他合作業務 | Z
	 */
	@Column(name = "COKIND", length = 1, columnDefinition = "VARCHAR(1)")
	private String coKind;

	/**
	 * 其他合作業務母戶
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	@Column(name = "MCNTRT", length = 1, columnDefinition = "VARCHAR(1)")
	private String mCntrt;

	/**
	 * 其他合作業務子戶
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	@Column(name = "SCNTRT", length = 1, columnDefinition = "VARCHAR(1)")
	private String sCntrt;

	/**
	 * 其他合作業務母戶之額度序號
	 * <p/>
	 * 102/01/17新增
	 */
	@Column(name = "MSCNTRT", length = 12, columnDefinition = "VARCHAR(12)")
	private String mScntrt;

	/**
	 * 其他合作業務子戶之代收帳號
	 * <p/>
	 * 102/01/17新增
	 */
	@Column(name = "MSACC", length = 16, columnDefinition = "VARCHAR(16)")
	private String mSAcc;

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;

	/**
	 * 列印順序
	 * <p/>
	 * 102/01/17新增
	 */
	@Column(name = "PRINTSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer printSeq;

	/**
	 * 修改資料特殊原因 例如 不動用，僅修改ELF385聯貸參貸比率
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N<br/>
	 * 若為Y，則上傳ELF383時LNFLAG為U（取代特殊案件修改流程）。
	 */
	@Column(name = "USESPECIALREASON", length = 2, columnDefinition = "CHAR(2)")
	private String useSpecialReason;

	/**
	 * 衍生性商品現請額度種類 1.交易額度(授權額度) 2.名目額度(名目本金)
	 */
	@Column(name = "DERVAPPLYAMTTYPE", length = 2, columnDefinition = "CHAR(1)")
	private String dervApplyAmtType;

	/**
	 * 是否為衍生性金融商品
	 * <p/>
	 * Y/N
	 */
	@Column(name = "ISDERIVATIVES", length = 1, columnDefinition = "CHAR(1)")
	private String isDerivatives;

	/** 顯示額度控管種類文字說明(非DB欄位) **/
	@Transient
	private String snoKindDscr;

	/** 顯示案件性質文字說明(非DB欄位) **/
	@Transient
	private String caseTypeDscr;

	/** 顯示額度性質文字說明(非DB欄位) **/
	@Transient
	private String propertyDscr;

	/** 顯示聯貸總額度(非DB欄位) **/
	@Transient
	private String quotaAmtDscr;

	/**
	 * 下次檢視日期種類
	 * <p/>
	 * 104/01/15新增<br/>
	 * 00: 不檢視<br/>
	 * 01: YYYY-MM-DD
	 */
	@Column(name = "REVIEWDATEKIND", length = 2, columnDefinition = "CHAR(2)")
	private String reViewDateKind;

	/**
	 * 下次檢視日期
	 * <p/>
	 * 104/01/15新增
	 */
	@Column(name = "REVIEWDATE", columnDefinition = "DATE")
	private Date reViewDate;

	/**
	 * 檢視週期種類
	 * <p/>
	 * 104/01/15新增<br/>
	 * 00: 無<br/>
	 * 01: 每X個月
	 */
	@Column(name = "REVIEWCHGKIND", length = 2, columnDefinition = "CHAR(2)")
	private String reViewChgKind;

	/**
	 * 檢視週期
	 * <p/>
	 * 104/01/15新增
	 */
	@Column(name = "REVIEWCHG1", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal reViewChg1;

	/**
	 * 是否為振興經濟非中小企業專案貸款 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	@Column(name = "ISNONSMEPROJLOAN", length = 1, columnDefinition = "VARCHAR(1)")
	private String isNonSMEProjLoan;

	/**
	 * J-105-0135-001 Web e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 * 屬振興經濟非中小企業專案貸款金額
	 * **/
	@Column(name = "NONSMEPROJLOANAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal nonSMEProjLoanAmt;

	/**
	 * J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
	 */
	@Column(name = "INSMEFG", length = 1, columnDefinition = "VARCHAR(1)")
	private String inSmeFg;

	/**
	 * J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
	 * 
	 * **/
	@Column(name = "INSMETOAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal inSmeToAmt;

	/**
	 * J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
	 * 
	 * **/
	@Column(name = "INSMECAAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal inSmeCaAmt;

	/**
	 * 額度序號是否在空地貸款控制檔中
	 */
	@Column(name = "ISCLEARLAND", length = 1, columnDefinition = "CHAR(1)")
	private String isClearLand;

	/**
	 * 控管類別
	 */
	@Column(name = "CTLTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String ctlType;

	/**
	 * 初次核定預計動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "FSTDATE", columnDefinition = "DATE")
	private Date fstDate;

	/**
	 * 最新核定(動審)預計動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LSTDATE", columnDefinition = "DATE")
	private Date lstDate;

	/**
	 * 是否變更預計動工日
	 */
	@Column(name = "ISCHGSTDATE", length = 1, columnDefinition = "CHAR(1)")
	private String isChgStDate;

	/**
	 * 變更預計動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CSTDATE", columnDefinition = "DATE")
	private Date cstDate;

	/**
	 * 變更預計動工日原因
	 */
	@Column(name = "CSTREASON", length = 2, columnDefinition = "CHAR(2)")
	private String cstReason;

	/**
	 * 是否調降利率
	 */
	@Column(name = "ISCHGRATE", length = 1, columnDefinition = "CHAR(1)")
	private String isChgRate;

	/**
	 * 輸入本次採行措施
	 */
	@Column(name = "ADOPTFG", length = 30, columnDefinition = "VARCHAR(30)")
	private String adoptFg;

	/**
	 * 再加減碼幅度
	 */
	@Digits(integer = 4, fraction = 5)
	@Column(name = "RATEADD", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal rateAdd;

	/**
	 * 借款人ROA
	 */
	@Digits(integer = 13, fraction = 2)
	@Column(name = "CUSTROA", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal custRoa;

	/**
	 * 關係人ROA
	 */
	@Digits(integer = 13, fraction = 2)
	@Column(name = "RELROA", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal relRoa;

	/**
	 * ROA查詢起日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ROABGNDATE", columnDefinition = "DATE")
	private Date roaBgnDate;

	/**
	 * ROA查詢迄日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ROAENDDATE", columnDefinition = "DATE")
	private Date roaEndDate;

	/**
	 * 是否符合本行規定
	 */
	@Column(name = "ISLEGAL", length = 1, columnDefinition = "CHAR(1)")
	private String isLegal;

	/**
	 * 本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
	 */
	@Column(name = "ISRESCUE", length = 1, columnDefinition = "CHAR(1)")
	private String isRescue;

	/**
	 * 紓困貸款類別
	 */
	@Column(name = "RESCUEITEM", length = 3, columnDefinition = "CHAR(3)")
	private String rescueItem;

	/**
	 * 減收利率
	 */
	@Column(name = "RESCUERATE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rescueRate;

	/**
	 * 本案是否展延六個月
	 */
	@Column(name = "ISEXTENDSIXMON", length = 1, columnDefinition = "CHAR(1)")
	private String isExtendSixMon;

	/**
	 * 合意展延日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RESCUEIBDATE", columnDefinition = "DATE")
	private Date rescueIbDate;

	/**
	 * 截至1090312前既有之本行貸款餘額
	 */
	@Digits(integer = 15, fraction = 2)
	@Column(name = "RESCUEAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal rescueAmt;

	/**
	 * 本案資金來源是否為央行轉融通
	 */
	@Column(name = "ISCBREFIN", length = 1, columnDefinition = "CHAR(1)")
	private String isCbRefin;

	/**
	 * 截至1090312前既有之本行貸款幣別
	 */
	@Column(name = "RESCUECURR", length = 3, columnDefinition = "CHAR(3)")
	private String rescueCurr;

	/**
	 * 合併申請紓困方案
	 */
	@Column(name = "RESCUEITEMSUB", length = 3, columnDefinition = "CHAR(3)")
	private String rescueItemSub;

	/**
	 * 受理日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RESCUEDATE", columnDefinition = "DATE")
	private Date rescueDate;

	/**
	 * 本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 */
	@Column(name = "HEADITEM1", length = 1, columnDefinition = "CHAR(1)")
	private String headItem1;

	/** 保證成數 **/
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "GUTPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal gutPercent;

	/** 信保首次動用有效期限 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "GUTCUTDATE", columnDefinition = "DATE")
	private Date gutCutDate;

	/**
	 * J-107-0137_05097_B1001 Web
	 * e-Loan企金授信額度明細表，新增「信保基金保證書發文日期」與「信保基金核准之保證手續費率」
	 * 
	 * **/
	@Column(name = "CGFRATE", columnDefinition = "DECIMAL(6,4)")
	private BigDecimal cgfRate;

	/**
	 * J-107-0137_05097_B1001 Web
	 * e-Loan企金授信額度明細表，新增「信保基金保證書發文日期」與「信保基金核准之保證手續費率」
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CGFDATE", columnDefinition = "DATE")
	private Date cgfDate;

	/**
	 * 本案是否為信保續約案
	 */
	@Column(name = "ISGUAOLDCASE", columnDefinition = "CHAR(1)")
	private String isGuaOldCase;

	/**
	 * 可否借新還舊
	 */
	@Column(name = "BYNEWOLD", columnDefinition = "CHAR(1)")
	private String byNewOld;

	/**
	 * 掛件文號
	 */
	@Column(name = "RESCUENO", columnDefinition = "VARCHAR(60)")
	private String rescueNo;

	/**
	 * 現有員工人數
	 */
	@Column(name = "EMPCOUNT", columnDefinition = "DECIMAL(10,0)")
	private BigDecimal empCount;

	/**
	 * 央行優惠利率融通期限
	 */
	@Column(name = "CBREFINDT", columnDefinition = "CHAR(2)")
	private String cbRefinDt;

	/** 國發基金加碼保證成數 **/
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "RESCUENDFGUTPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal rescueNdfGutPercent;

	/**
	 * 是否符合110年5~12月營業額減少達15%(FOR紓困代碼A07)
	 */
	@Column(name = "ISTURNOVERDECREASED", columnDefinition = "CHAR(1)")
	private String isTurnoverDecreased;

	/**
	 * 額度編號
	 */
	@Column(name = "RESCUESN", columnDefinition = "VARCHAR(20)")
	private String rescueSn;

	/**
	 * 授信契約書期別
	 * <p/>
	 * 1短期2中長期
	 */
	@Size(max = 1)
	@Column(name = "TTYPE_S01A", length = 1, columnDefinition = "CHAR(1)")
	private String tType_s01a;

	/**
	 * 動用期間選項
	 * <p/>
	 * 100/10/06新增<br/>
	 * YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 自首動日起MM個月<br/>
	 * 其他
	 */
	@Size(max = 1)
	@Column(name = "USESELECT_S01A", length = 1, columnDefinition = "CHAR(1)")
	private String useSelect_s01a;

	/**
	 * 動用期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "USEFROMDATE_S01A", columnDefinition = "DATE")
	private Date useFromDate_s01a;

	/**
	 * 動用期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "USEENDDATE_S01A", columnDefinition = "DATE")
	private Date useEndDate_s01a;

	/**
	 * 動用期間-月數
	 * <p/>
	 * 2.自首動日起MM個月
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "USEMONTH_S01A", columnDefinition = "DECIMAL(2,0)")
	private Integer useMonth_s01a;

	/**
	 * 動用期間-其他
	 * <p/>
	 * 3.其他 <br/>
	 * 102.02.18 欄位擴大 60 -> 900
	 */
	@Size(max = 900)
	@Column(name = "USEOTHER_S01A", length = 900, columnDefinition = "VARCHAR(900)")
	private String useOther_s01a;

	/**
	 * 授信期間選項
	 * <p/>
	 * 100/10/06新增<br/>
	 * YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 自首動日起MM個月<br/>
	 * 其他
	 */
	@Size(max = 1)
	@Column(name = "LNSELECT_S01A", length = 1, columnDefinition = "CHAR(1)")
	private String lnSelect_s01a;

	/**
	 * 授信期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNFROMDATE_S01A", columnDefinition = "DATE")
	private Date lnFromDate_s01a;

	/**
	 * 授信期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNENDDATE_S01A", columnDefinition = "DATE")
	private Date lnEndDate_s01a;

	/**
	 * 授信期間-年數
	 * <p/>
	 * 100/10/11新增<br/>
	 * 2.自首動日起YY年
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNYEAR_S01A", columnDefinition = "DECIMAL(2,0)")
	private Integer lnYear_s01a;

	/**
	 * 授信期間-月數
	 * <p/>
	 * 2.自首動日起MM個月
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNMONTH_S01A", columnDefinition = "DECIMAL(2,0)")
	private Integer lnMonth_s01a;

	/**
	 * 授信期間-其他
	 * <p/>
	 * 3.其他
	 * 
	 * <br/>
	 * 102.02.18 欄位擴大 60 -> 900
	 */
	@Size(max = 900)
	@Column(name = "LNOTHER_S01A", length = 900, columnDefinition = "VARCHAR(900)")
	private String lnOther_s01a;

	/**
	 * 本次是否客戶申請調升補貼利率
	 * 
	 */
	@Size(max = 1)
	@Column(name = "RESCUECHGRATEFG", length = 1, columnDefinition = "CHAR(1)")
	private String rescueChgRateFg;

	/**
	 * 調升簽約日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RESCUECHGRATESINGDATE", columnDefinition = "DATE")
	private Date rescueChgRateSingDate;

	/**
	 * 調整後利率
	 */
	@Digits(integer = 2, fraction = 5, groups = Check.class)
	@Column(name = "RESCUECHGRATE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rescueChgRate;

	/**
	 * 調升起息日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RESCUECHGRATEEFFECTDATE", columnDefinition = "DATE")
	private Date rescueChgRateEffectDate;

	/**
	 * 實際動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ACTSTARTDATE", columnDefinition = "DATE")
	private Date actStartDate;

	/**
	 * 信保案件 配偶建檔檢核 0 : NA(不需檢核) 1 : 有(有建配偶檔) 2 : 無(未建配偶檔)
	 */
	@Column(name = "SMEMATEINFO", length = 1, columnDefinition = "CHAR(1)")
	private String smeMateInfo;

	/**
	 * 信保案件檢核配偶建檔之負責人
	 */
	@Column(name = "SMECHKMATEPRINCIPAL", length = 11, columnDefinition = "VARCHAR(11)")
	private String smeChkMatePrincipal;

	/**
	 * 是否收取企金授信作業手續費
	 */
	@Column(name = "ISOPERATIONFEE", length = 1, columnDefinition = "CHAR(1)")
	private String isOperationFee;

	/**
	 * 授信作業手續費幣別
	 */
	@Column(name = "OPERATIONFEECURR", length = 3, columnDefinition = "CHAR(3)")
	private String operationFeeCurr;

	/**
	 * 授信作業手續費金額
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "OPERATIONFEEAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal operationFeeAmt;

	/**
	 * 授信作業手續費最晚收取日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "OPERATIONFEEDUEDATE", columnDefinition = "DATE")
	private Date operationFeeDueDate;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得案件號碼-年度
	 * <p/>
	 * 資料來源：動用審核表
	 */
	public Integer getCaseYear() {
		return this.caseYear;
	}

	/**
	 * 設定案件號碼-年度
	 * <p/>
	 * 資料來源：動用審核表
	 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/**
	 * 取得案件號碼-分行
	 * <p/>
	 * 資料來源：動用審核表
	 */
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/**
	 * 設定案件號碼-分行
	 * <p/>
	 * 資料來源：動用審核表
	 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/**
	 * 取得案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：動用審核表
	 */
	public Integer getCaseSeq() {
		return this.caseSeq;
	}

	/**
	 * 設定案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：動用審核表
	 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/**
	 * 取得案件號碼
	 * <p/>
	 * 資料來源：動用審核表
	 */
	public String getCaseNo() {
		return this.caseNo;
	}

	/**
	 * 設定案件號碼
	 * <p/>
	 * 資料來源：動用審核表
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/**
	 * 取得簽案日期
	 * <p/>
	 * 資料來源：動用審核表
	 */
	public Date getCaseDate() {
		return this.caseDate;
	}

	/**
	 * 設定簽案日期
	 * <p/>
	 * 資料來源：動用審核表
	 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/**
	 * 取得案由
	 * <p/>
	 * 101/06/25調整<br/>
	 * VARCHAR(768)(VARCHAR(4096)
	 */
	public String getGist() {
		return this.gist;
	}

	/**
	 * 設定案由
	 * <p/>
	 * 101/06/25調整<br/>
	 * VARCHAR(768)(VARCHAR(4096)
	 **/
	public void setGist(String value) {
		this.gist = value;
	}

	/** 取得幣別 **/
	public String getQuotaCurr() {
		return this.quotaCurr;
	}

	/** 設定幣別 **/
	public void setQuotaCurr(String value) {
		this.quotaCurr = value;
	}

	/** 取得總額度 **/
	public BigDecimal getQuotaAmt() {
		return this.quotaAmt;
	}

	/** 設定總額度 **/
	public void setQuotaAmt(BigDecimal value) {
		this.quotaAmt = value;
	}

	/** 取得簽約日期 **/
	public Date getSignDate() {
		return this.signDate;
	}

	/** 設定簽約日期 **/
	public void setSignDate(Date value) {
		this.signDate = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/**
	 * 取得文件編號UID
	 * <p/>
	 * 102/01/17新增
	 */
	public String getUid() {
		return this.uid;
	}

	/**
	 * 設定文件編號UID
	 * <p/>
	 * 102/01/17新增
	 **/
	public void setUid(String value) {
		this.uid = value;
	}

	/**
	 * 取得統一編號
	 * <p/>
	 * 102/01/17新增
	 */
	public String getCustId() {
		return this.custId;
	}

	/**
	 * 設定統一編號
	 * <p/>
	 * 102/01/17新增
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/**
	 * 取得重覆序號
	 * <p/>
	 * 102/01/17新增
	 */
	public String getDupNo() {
		return this.dupNo;
	}

	/**
	 * 設定重覆序號
	 * <p/>
	 * 102/01/17新增
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得客戶名稱
	 * <p/>
	 * 102/01/17新增
	 */
	public String getCustName() {
		return this.custName;
	}

	/**
	 * 設定客戶名稱
	 * <p/>
	 * 102/01/17新增
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得性質
	 * <p/>
	 * 102/01/17新增<br/>
	 * 複選：<br/>
	 * 1報價(報價|13<br/>
	 * 2新作(新做|1<br/>
	 * 3增額(增額|5<br/>
	 * 4紓困(紓困|10<br/>
	 * 5協議清償(協議清償|12<br/>
	 * 6減額(減額|6<br/>
	 * 7變更條件(變更條件|3<br/>
	 * 8續約(續約|2<br/>
	 * 9提前續約(提前續約|11<br/>
	 * 10展期（不良授信案）(展期(不良授信案)|9<br/>
	 * 11流用(流用|4<br/>
	 * 12取消(取消|8<br/>
	 * 13不變(不變|7
	 */
	public String getProperty() {
		return this.property;
	}

	/**
	 * 設定性質
	 * <p/>
	 * 102/01/17新增<br/>
	 * 複選：<br/>
	 * 1報價(報價|13<br/>
	 * 2新作(新做|1<br/>
	 * 3增額(增額|5<br/>
	 * 4紓困(紓困|10<br/>
	 * 5協議清償(協議清償|12<br/>
	 * 6減額(減額|6<br/>
	 * 7變更條件(變更條件|3<br/>
	 * 8續約(續約|2<br/>
	 * 9提前續約(提前續約|11<br/>
	 * 10展期（不良授信案）(展期(不良授信案)|9<br/>
	 * 11流用(流用|4<br/>
	 * 12取消(取消|8<br/>
	 * 13不變(不變|7
	 **/
	public void setProperty(String value) {
		this.property = value;
	}

	/**
	 * 取得現請額度－幣別
	 * <p/>
	 * 102/01/17新增
	 */
	public String getCurrentApplyCurr() {
		return this.currentApplyCurr;
	}

	/**
	 * 設定現請額度－幣別
	 * <p/>
	 * 102/01/17新增
	 **/
	public void setCurrentApplyCurr(String value) {
		this.currentApplyCurr = value;
	}

	/**
	 * 取得現請額度－金額
	 * <p/>
	 * 102/01/17新增
	 */
	public BigDecimal getCurrentApplyAmt() {
		return this.currentApplyAmt;
	}

	/**
	 * 設定現請額度－金額
	 * <p/>
	 * 102/01/17新增
	 **/
	public void setCurrentApplyAmt(BigDecimal value) {
		this.currentApplyAmt = value;
	}

	/** 取得額度序號MAINID **/
	public String getCntrMainId() {
		return this.cntrMainId;
	}

	/** 設定額度序號MAINID **/
	public void setCntrMainId(String value) {
		this.cntrMainId = value;
	}

	/**
	 * 取得額度控管種類
	 * <p/>
	 * 102/01/17新增<br/>
	 * 10.一般<br/>
	 * 20.信保<br/>
	 * 30.聯貸<br/>
	 * 40.合作母<br/>
	 * 41.合作子<br/>
	 * 51.個人戶一般
	 */
	public String getSnoKind() {
		return this.snoKind;
	}

	/**
	 * 設定額度控管種類
	 * <p/>
	 * 102/01/17新增<br/>
	 * 10.一般<br/>
	 * 20.信保<br/>
	 * 30.聯貸<br/>
	 * 40.合作母<br/>
	 * 41.合作子<br/>
	 * 51.個人戶一般
	 **/
	public void setSnoKind(String value) {
		this.snoKind = value;
	}

	/**
	 * 取得案件性質
	 * <p/>
	 * 102/01/17新增<br/>
	 * 同業聯貸主辦<br/>
	 * 同業聯貸主辦（含自行聯貸）<br/>
	 * 同業聯貸參貸<br/>
	 * 同業聯貸參貸（含自行聯貸）<br/>
	 * 自行聯貸<br/>
	 * 一般貸款<br/>
	 * 合作業務母戶<br/>
	 * 合作業務子戶
	 */
	public String getCaseType() {
		return this.caseType;
	}

	/**
	 * 設定案件性質
	 * <p/>
	 * 102/01/17新增<br/>
	 * 同業聯貸主辦<br/>
	 * 同業聯貸主辦（含自行聯貸）<br/>
	 * 同業聯貸參貸<br/>
	 * 同業聯貸參貸（含自行聯貸）<br/>
	 * 自行聯貸<br/>
	 * 一般貸款<br/>
	 * 合作業務母戶<br/>
	 * 合作業務子戶
	 **/
	public void setCaseType(String value) {
		this.caseType = value;
	}

	/**
	 * 取得聯貸比率-同業
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	public String getCoBank() {
		return this.coBank;
	}

	/**
	 * 設定聯貸比率-同業
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 **/
	public void setCoBank(String value) {
		this.coBank = value;
	}

	/**
	 * 取得聯貸比率-聯行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	public String getCoBranch() {
		return this.coBranch;
	}

	/**
	 * 設定聯貸比率-聯行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 **/
	public void setCoBranch(String value) {
		this.coBranch = value;
	}

	/**
	 * 取得本案是否有同業聯貸案額度
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	public String getUnitCase() {
		return this.unitCase;
	}

	/**
	 * 設定本案是否有同業聯貸案額度
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 **/
	public void setUnitCase(String value) {
		this.unitCase = value;
	}

	/**
	 * 取得本行是否為管理行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	public String getUCMainBranch() {
		return this.uCMainBranch;
	}

	/**
	 * 設定本行是否為管理行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 **/
	public void setUCMainBranch(String value) {
		this.uCMainBranch = value;
	}

	/**
	 * 取得本案是否有同行(本行)聯貸案額度行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	public String getUnitMega() {
		return this.unitMega;
	}

	/**
	 * 設定本案是否有同行(本行)聯貸案額度行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 **/
	public void setUnitMega(String value) {
		this.unitMega = value;
	}

	/**
	 * 取得本分行是否為額度管理行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	public String getUCntBranch() {
		return this.uCntBranch;
	}

	/**
	 * 設定本分行是否為額度管理行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 **/
	public void setUCntBranch(String value) {
		this.uCntBranch = value;
	}

	/**
	 * 取得本分行是否為擔保品管理行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	public String getUCMSBranch() {
		return this.uCMSBranch;
	}

	/**
	 * 設定本分行是否為擔保品管理行
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 **/
	public void setUCMSBranch(String value) {
		this.uCMSBranch = value;
	}

	/**
	 * 取得本案是否為隱名參貸
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	public String getUHideName() {
		return this.uHideName;
	}

	/**
	 * 設定本案是否為隱名參貸
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 **/
	public void setUHideName(String value) {
		this.uHideName = value;
	}

	/**
	 * 取得本案為國內聯貸/國際聯貸
	 * <p/>
	 * 102/01/17新增<br/>
	 * 國內聯貸 | A<br/>
	 * 國際聯貸 | B
	 */
	public String getUArea() {
		return this.uArea;
	}

	/**
	 * 設定本案為國內聯貸/國際聯貸
	 * <p/>
	 * 102/01/17新增<br/>
	 * 國內聯貸 | A<br/>
	 * 國際聯貸 | B
	 **/
	public void setUArea(String value) {
		this.uArea = value;
	}

	/**
	 * 取得報核方式（國金部報核、分行報核）
	 * <p/>
	 * 102/01/17新增<br/>
	 * 國金部報核 | A<br/>
	 * 分行報核 | B
	 */
	public String getURP1() {
		return this.uRP1;
	}

	/**
	 * 設定報核方式（國金部報核、分行報核）
	 * <p/>
	 * 102/01/17新增<br/>
	 * 國金部報核 | A<br/>
	 * 分行報核 | B
	 **/
	public void setURP1(String value) {
		this.uRP1 = value;
	}

	/**
	 * 取得報核方式（國金部對外、分行對外）
	 * <p/>
	 * 102/01/17新增<br/>
	 * 國金部對外| A<br/>
	 * 分行對外 | B
	 */
	public String getURP2() {
		return this.uRP2;
	}

	/**
	 * 設定報核方式（國金部對外、分行對外）
	 * <p/>
	 * 102/01/17新增<br/>
	 * 國金部對外| A<br/>
	 * 分行對外 | B
	 **/
	public void setURP2(String value) {
		this.uRP2 = value;
	}

	/**
	 * 取得報核方式（國金部掛帳、分行掛帳）
	 * <p/>
	 * 102/01/17新增<br/>
	 * 國金部掛帳 | A<br/>
	 * 分行掛帳 | B
	 */
	public String getURP3() {
		return this.uRP3;
	}

	/**
	 * 設定報核方式（國金部掛帳、分行掛帳）
	 * <p/>
	 * 102/01/17新增<br/>
	 * 國金部掛帳 | A<br/>
	 * 分行掛帳 | B
	 **/
	public void setURP3(String value) {
		this.uRP3 = value;
	}

	/**
	 * 取得合作業務種類
	 * <p/>
	 * 102/01/17新增<br/>
	 * 非合作業務 | 0<br/>
	 * 價金履約保證 | 1<br/>
	 * 合作外匯 | 2<br/>
	 * 其他合作業務 | Z
	 */
	public String getCoKind() {
		return this.coKind;
	}

	/**
	 * 設定合作業務種類
	 * <p/>
	 * 102/01/17新增<br/>
	 * 非合作業務 | 0<br/>
	 * 價金履約保證 | 1<br/>
	 * 合作外匯 | 2<br/>
	 * 其他合作業務 | Z
	 **/
	public void setCoKind(String value) {
		this.coKind = value;
	}

	/**
	 * 取得其他合作業務母戶
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	public String getMCntrt() {
		return this.mCntrt;
	}

	/**
	 * 設定其他合作業務母戶
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 **/
	public void setMCntrt(String value) {
		this.mCntrt = value;
	}

	/**
	 * 取得其他合作業務子戶
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 */
	public String getSCntrt() {
		return this.sCntrt;
	}

	/**
	 * 設定其他合作業務子戶
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N（是/否）
	 **/
	public void setSCntrt(String value) {
		this.sCntrt = value;
	}

	/**
	 * 取得其他合作業務母戶之額度序號
	 * <p/>
	 * 102/01/17新增
	 */
	public String getMScntrt() {
		return this.mScntrt;
	}

	/**
	 * 設定其他合作業務母戶之額度序號
	 * <p/>
	 * 102/01/17新增
	 **/
	public void setMScntrt(String value) {
		this.mScntrt = value;
	}

	/**
	 * 取得其他合作業務子戶之代收帳號
	 * <p/>
	 * 102/01/17新增
	 */
	public String getMSAcc() {
		return this.mSAcc;
	}

	/**
	 * 設定其他合作業務子戶之代收帳號
	 * <p/>
	 * 102/01/17新增
	 **/
	public void setMSAcc(String value) {
		this.mSAcc = value;
	}

	/**
	 * 取得輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}

	/**
	 * 設定輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 102/01/17新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/**
	 * 設定列印順序
	 * <p/>
	 * 102/01/17新增
	 **/
	public void setPrintSeq(Integer printSeq) {
		this.printSeq = printSeq;
	}

	/**
	 * 取得列印順序
	 * <p/>
	 * 102/01/17新增
	 */
	public Integer getPrintSeq() {
		return printSeq;
	}

	/**
	 * 設定控管種類文字說明
	 * <p/>
	 * 102/01/17新增
	 */
	public void setSnoKindDscr(String snoKindDscr) {
		this.snoKindDscr = snoKindDscr;
	}

	/**
	 * 取得控管種類文字說明
	 * <p/>
	 * 102/01/17新增
	 */
	public String getSnoKindDscr() {
		return snoKindDscr;
	}

	/**
	 * 設定案件性質文字說明
	 * <p/>
	 * 102/01/17新增
	 */
	public void setCaseTypeDscr(String caseTypeDscr) {
		this.caseTypeDscr = caseTypeDscr;
	}

	/**
	 * 取得案件性質文字說明
	 * <p/>
	 * 102/01/17新增
	 */
	public String getCaseTypeDscr() {
		return caseTypeDscr;
	}

	/**
	 * 設定額度性質文字說明
	 * <p/>
	 * 102/01/17新增
	 */
	public void setPropertyDscr(String propertyDscr) {
		this.propertyDscr = propertyDscr;
	}

	/**
	 * 取得額度性質文字說明
	 * <p/>
	 * 102/01/17新增
	 */
	public String getPropertyDscr() {
		return propertyDscr;
	}

	/**
	 * 設定修改資料特殊原因
	 * <p/>
	 * 102/01/17新增
	 */
	public void setUseSpecialReason(String useSpecialReason) {
		this.useSpecialReason = useSpecialReason;
	}

	/**
	 * 取得修改資料特殊原因
	 * <p/>
	 * 102/01/17新增
	 */
	public String getUseSpecialReason() {
		return useSpecialReason;
	}

	/**
	 * 設定顯示聯貸總額度
	 * <p/>
	 * 102/01/17新增
	 */
	public void setQuotaAmtDscr(String quotaAmtDscr) {
		this.quotaAmtDscr = quotaAmtDscr;
	}

	/**
	 * 取得顯示聯貸總額度
	 * <p/>
	 * 102/01/17新增
	 */
	public String getQuotaAmtDscr() {
		return quotaAmtDscr;
	}

	/**
	 * 設定衍生性商品現請額度種類
	 * <p/>
	 * 103/09/23新增 J-103-0202-005
	 */
	public void setDervApplyAmtType(String dervApplyAmtType) {
		this.dervApplyAmtType = dervApplyAmtType;
	}

	/**
	 * 取得衍生性商品現請額度種類
	 * <p/>
	 * 103/09/23新增 J-103-0202-005
	 */
	public String getDervApplyAmtType() {
		return dervApplyAmtType;
	}

	/**
	 * 取得是否為衍生性金融商品
	 * <p/>
	 * Y/N
	 */
	public String getIsDerivatives() {
		return this.isDerivatives;
	}

	/**
	 * 設定是否為衍生性金融商品
	 * <p/>
	 * Y/N
	 **/
	public void setIsDerivatives(String value) {
		this.isDerivatives = value;
	}

	/**
	 * 取得下次檢視日期種類
	 * <p/>
	 * 104/01/15新增<br/>
	 * 00: 不檢視<br/>
	 * 01: YYYY-MM-DD
	 */
	public String getReViewDateKind() {
		return this.reViewDateKind;
	}

	/**
	 * 設定下次檢視日期種類
	 * <p/>
	 * 104/01/15新增<br/>
	 * 00: 不檢視<br/>
	 * 01: YYYY-MM-DD
	 **/
	public void setReViewDateKind(String value) {
		this.reViewDateKind = value;
	}

	/**
	 * 取得下次檢視日期
	 * <p/>
	 * 104/01/15新增
	 */
	public Date getReViewDate() {
		return this.reViewDate;
	}

	/**
	 * 設定下次檢視日期
	 * <p/>
	 * 104/01/15新增
	 **/
	public void setReViewDate(Date value) {
		this.reViewDate = value;
	}

	/**
	 * 取得檢視週期種類
	 * <p/>
	 * 104/01/15新增<br/>
	 * 00: 無<br/>
	 * 01: 每X個月
	 */
	public String getReViewChgKind() {
		return this.reViewChgKind;
	}

	/**
	 * 設定檢視週期種類
	 * <p/>
	 * 104/01/15新增<br/>
	 * 00: 無<br/>
	 * 01: 每X個月
	 **/
	public void setReViewChgKind(String value) {
		this.reViewChgKind = value;
	}

	/**
	 * 取得檢視週期
	 * <p/>
	 * 104/01/15新增
	 */
	public BigDecimal getReViewChg1() {
		return this.reViewChg1;
	}

	/**
	 * 設定檢視週期
	 * <p/>
	 * 104/01/15新增
	 **/
	public void setReViewChg1(BigDecimal value) {
		this.reViewChg1 = value;
	}

	/**
	 * 設定是否為振興經濟非中小企業專案貸款 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public void setIsNonSMEProjLoan(String isNonSMEProjLoan) {
		this.isNonSMEProjLoan = isNonSMEProjLoan;
	}

	/**
	 * 取得是否為振興經濟非中小企業專案貸款 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public String getIsNonSMEProjLoan() {
		return isNonSMEProjLoan;
	}

	/**
	 * 設定振興經濟非中小企業專案貸款金額 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public void setNonSMEProjLoanAmt(BigDecimal nonSMEProjLoanAmt) {
		this.nonSMEProjLoanAmt = nonSMEProjLoanAmt;
	}

	/**
	 * 取得振興經濟非中小企業專案貸款金額 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public BigDecimal getNonSMEProjLoanAmt() {
		return nonSMEProjLoanAmt;
	}

	/** 設定是否屬中小企業創新發展專案貸款 **/
	public void setInSmeFg(String inSmeFg) {
		this.inSmeFg = inSmeFg;
	}

	/** 取得是否屬中小企業創新發展專案貸款 **/
	public String getInSmeFg() {
		return inSmeFg;
	}

	/** 設定週轉性支出 **/
	public void setInSmeToAmt(BigDecimal inSmeToAmt) {
		this.inSmeToAmt = inSmeToAmt;
	}

	/** 取得週轉性支出 **/
	public BigDecimal getInSmeToAmt() {
		return inSmeToAmt;
	}

	/** 設定資本性支出 **/
	public void setInSmeCaAmt(BigDecimal inSmeCaAmt) {
		this.inSmeCaAmt = inSmeCaAmt;
	}

	/** 取得資本性支出 **/
	public BigDecimal getInSmeCaAmt() {
		return inSmeCaAmt;
	}

	/** 設定額度序號是否在空地貸款控制檔中 **/
	public void setIsClearLand(String isClearLand) {
		this.isClearLand = isClearLand;
	}

	/** 取得額度序號是否在空地貸款控制檔中 **/
	public String getIsClearLand() {
		return isClearLand;
	}

	/** 設定控管類別 **/
	public void setCtlType(String ctlType) {
		this.ctlType = ctlType;
	}

	/** 取得控管類別 **/
	public String getCtlType() {
		return ctlType;
	}

	/** 設定初次核定預計動工日 **/
	public void setFstDate(Date fstDate) {
		this.fstDate = fstDate;
	}

	/** 取得初次核定預計動工日 **/
	public Date getFstDate() {
		return fstDate;
	}

	/** 設定最新核定(動審)預計動工日 **/
	public void setLstDate(Date lstDate) {
		this.lstDate = lstDate;
	}

	/** 取得最新核定(動審)預計動工日 **/
	public Date getLstDate() {
		return lstDate;
	}

	/** 設定是否變更預計動工日 **/
	public void setIsChgStDate(String isChgStDate) {
		this.isChgStDate = isChgStDate;
	}

	/** 取得是否變更預計動工日 **/
	public String getIsChgStDate() {
		return isChgStDate;
	}

	/** 設定變更預計動工日 **/
	public void setCstDate(Date cstDate) {
		this.cstDate = cstDate;
	}

	/** 取得變更預計動工日 **/
	public Date getCstDate() {
		return cstDate;
	}

	/** 設定變更預計動工日原因 **/
	public void setCstReason(String cstReason) {
		this.cstReason = cstReason;
	}

	/** 取得變更預計動工日原因 **/
	public String getCstReason() {
		return cstReason;
	}

	/** 設定是否調降利率 **/
	public void setIsChgRate(String isChgRate) {
		this.isChgRate = isChgRate;
	}

	/** 取得是否調降利率 **/
	public String getIsChgRate() {
		return isChgRate;
	}

	/** 設定輸入本次採行措施 **/
	public void setAdoptFg(String adoptFg) {
		this.adoptFg = adoptFg;
	}

	/** 取得輸入本次採行措施 **/
	public String getAdoptFg() {
		return adoptFg;
	}

	/** 設定再加減碼幅度 **/
	public void setRateAdd(BigDecimal rateAdd) {
		this.rateAdd = rateAdd;
	}

	/** 取得再加減碼幅度 **/
	public BigDecimal getRateAdd() {
		return rateAdd;
	}

	/** 設定借款人ROA **/
	public void setCustRoa(BigDecimal custRoa) {
		this.custRoa = custRoa;
	}

	/** 取得借款人ROA **/
	public BigDecimal getCustRoa() {
		return custRoa;
	}

	/** 設定關係人ROA **/
	public void setRelRoa(BigDecimal relRoa) {
		this.relRoa = relRoa;
	}

	/** 取得關係人ROA **/
	public BigDecimal getRelRoa() {
		return relRoa;
	}

	/** 設定ROA查詢起日 **/
	public void setRoaBgnDate(Date roaBgnDate) {
		this.roaBgnDate = roaBgnDate;
	}

	/** 取得ROA查詢起日 **/
	public Date getRoaBgnDate() {
		return roaBgnDate;
	}

	/** 設定ROA查詢迄日 **/
	public void setRoaEndDate(Date roaEndDate) {
		this.roaEndDate = roaEndDate;
	}

	/** 取得ROA查詢迄日 **/
	public Date getRoaEndDate() {
		return roaEndDate;
	}

	/** 設定是否符合本行規定 **/
	public void setIsLegal(String isLegal) {
		this.isLegal = isLegal;
	}

	/** 取得是否符合本行規定 **/
	public String getIsLegal() {
		return isLegal;
	}

	/**
	 * 設定本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
	 * 
	 * @param isRescue
	 */
	public void setIsRescue(String isRescue) {
		this.isRescue = isRescue;
	}

	/**
	 * 取得本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
	 * 
	 * @param isRescue
	 */
	public String getIsRescue() {
		return isRescue;
	}

	/**
	 * 設定紓困貸款類別
	 * 
	 * @param isRescue
	 */
	public void setRescueItem(String rescueItem) {
		this.rescueItem = rescueItem;
	}

	/**
	 * 取得紓困貸款類別
	 * 
	 * @param isRescue
	 */
	public String getRescueItem() {
		return rescueItem;
	}

	/**
	 * 設定減收利率
	 * 
	 * @param isRescue
	 */
	public void setRescueRate(BigDecimal rescueRate) {
		this.rescueRate = rescueRate;
	}

	/**
	 * 取得減收利率
	 * 
	 * @param isRescue
	 */
	public BigDecimal getRescueRate() {
		return rescueRate;
	}

	public void setIsExtendSixMon(String isExtendSixMon) {
		this.isExtendSixMon = isExtendSixMon;
	}

	public String getIsExtendSixMon() {
		return isExtendSixMon;
	}

	public void setRescueIbDate(Date rescueIbDate) {
		this.rescueIbDate = rescueIbDate;
	}

	public Date getRescueIbDate() {
		return rescueIbDate;
	}

	public void setRescueAmt(BigDecimal rescueAmt) {
		this.rescueAmt = rescueAmt;
	}

	public BigDecimal getRescueAmt() {
		return rescueAmt;
	}

	public void setIsCbRefin(String isCbRefin) {
		this.isCbRefin = isCbRefin;
	}

	public String getIsCbRefin() {
		return isCbRefin;
	}

	public void setRescueCurr(String rescueCurr) {
		this.rescueCurr = rescueCurr;
	}

	public String getRescueCurr() {
		return rescueCurr;
	}

	public void setRescueItemSub(String rescueItemSub) {
		this.rescueItemSub = rescueItemSub;
	}

	public String getRescueItemSub() {
		return rescueItemSub;
	}

	public void setRescueDate(Date rescueDate) {
		this.rescueDate = rescueDate;
	}

	public Date getRescueDate() {
		return rescueDate;
	}

	public void setHeadItem1(String headItem1) {
		this.headItem1 = headItem1;
	}

	public String getHeadItem1() {
		return headItem1;
	}

	public void setGutPercent(BigDecimal gutPercent) {
		this.gutPercent = gutPercent;
	}

	public BigDecimal getGutPercent() {
		return gutPercent;
	}

	public void setGutCutDate(Date gutCutDate) {
		this.gutCutDate = gutCutDate;
	}

	public Date getGutCutDate() {
		return gutCutDate;
	}

	public void setCgfRate(BigDecimal cgfRate) {
		this.cgfRate = cgfRate;
	}

	public BigDecimal getCgfRate() {
		return cgfRate;
	}

	public void setCgfDate(Date cgfDate) {
		this.cgfDate = cgfDate;
	}

	public Date getCgfDate() {
		return cgfDate;
	}

	public void setIsGuaOldCase(String isGuaOldCase) {
		this.isGuaOldCase = isGuaOldCase;
	}

	public String getIsGuaOldCase() {
		return isGuaOldCase;
	}

	public void setByNewOld(String byNewOld) {
		this.byNewOld = byNewOld;
	}

	public String getByNewOld() {
		return byNewOld;
	}

	public void setRescueNo(String rescueNo) {
		this.rescueNo = rescueNo;
	}

	public String getRescueNo() {
		return rescueNo;
	}

	public void setEmpCount(BigDecimal empCount) {
		this.empCount = empCount;
	}

	public BigDecimal getEmpCount() {
		return empCount;
	}

	public void setCbRefinDt(String cbRefinDt) {
		this.cbRefinDt = cbRefinDt;
	}

	public String getCbRefinDt() {
		return cbRefinDt;
	}

	/**
	 * 設定國發基金加碼保證成數
	 * 
	 * @param guaNaExposure
	 */
	public void setRescueNdfGutPercent(BigDecimal rescueNdfGutPercent) {
		this.rescueNdfGutPercent = rescueNdfGutPercent;
	}

	/**
	 * 取得是否符合110年5~12月營業額減少達15%
	 * 
	 * @return
	 */
	public BigDecimal getRescueNdfGutPercent() {
		return rescueNdfGutPercent;
	}

	/**
	 * 設定是否符合110年5~12月營業額減少達15%
	 * 
	 * @param guaNaExposure
	 */
	public void setIsTurnoverDecreased(String isTurnoverDecreased) {
		this.isTurnoverDecreased = isTurnoverDecreased;
	}

	/**
	 * 取得國發基金加碼保證成數
	 * 
	 * @return
	 */
	public String getIsTurnoverDecreased() {
		return isTurnoverDecreased;
	}

	/**
	 * 設定額度編號
	 * 
	 * @param guaNaExposure
	 */
	public void setRescueSn(String rescueSn) {
		this.rescueSn = rescueSn;
	}

	/**
	 * 取得額度編號
	 * 
	 * @return
	 */
	public String getRescueSn() {
		return rescueSn;
	}

	/**
	 * 取得授信契約書期別
	 * <p/>
	 * 1短期2中長期
	 */
	public String getTType_s01a() {
		return this.tType_s01a;
	}

	/**
	 * 設定授信契約書期別
	 * <p/>
	 * 1短期2中長期
	 **/
	public void setTType_s01a(String value) {
		this.tType_s01a = value;
	}

	/**
	 * 取得動用期間選項
	 * <p/>
	 * 100/10/06新增<br/>
	 * YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 自首動日起MM個月<br/>
	 * 其他
	 */
	public String getUseSelect_s01a() {
		return this.useSelect_s01a;
	}

	/**
	 * 設定動用期間選項
	 * <p/>
	 * 100/10/06新增<br/>
	 * YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 自首動日起MM個月<br/>
	 * 其他
	 **/
	public void setUseSelect_s01a(String value) {
		this.useSelect_s01a = value;
	}

	/**
	 * 取得動用期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getUseFromDate_s01a() {
		return this.useFromDate_s01a;
	}

	/**
	 * 設定動用期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setUseFromDate_s01a(Date value) {
		this.useFromDate_s01a = value;
	}

	/**
	 * 取得動用期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getUseEndDate_s01a() {
		return this.useEndDate_s01a;
	}

	/**
	 * 設定動用期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setUseEndDate_s01a(Date value) {
		this.useEndDate_s01a = value;
	}

	/**
	 * 取得動用期間-月數
	 * <p/>
	 * 2.自首動日起MM個月
	 */
	public Integer getUseMonth_s01a() {
		return this.useMonth_s01a;
	}

	/**
	 * 設定動用期間-月數
	 * <p/>
	 * 2.自首動日起MM個月
	 **/
	public void setUseMonth_s01a(Integer value) {
		this.useMonth_s01a = value;
	}

	/**
	 * 取得動用期間-其他
	 * <p/>
	 * 3.其他
	 */
	public String getUseOther_s01a() {
		return this.useOther_s01a;
	}

	/**
	 * 設定動用期間-其他
	 * <p/>
	 * 3.其他
	 **/
	public void setUseOther_s01a(String value) {
		this.useOther_s01a = value;
	}

	/**
	 * 取得授信期間選項
	 * <p/>
	 * 100/10/06新增<br/>
	 * YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 自首動日起MM個月<br/>
	 * 其他
	 */
	public String getLnSelect_s01a() {
		return this.lnSelect_s01a;
	}

	/**
	 * 設定授信期間選項
	 * <p/>
	 * 100/10/06新增<br/>
	 * YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 自首動日起MM個月<br/>
	 * 其他
	 **/
	public void setLnSelect_s01a(String value) {
		this.lnSelect_s01a = value;
	}

	/**
	 * 取得授信期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getLnFromDate_s01a() {
		return this.lnFromDate_s01a;
	}

	/**
	 * 設定授信期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setLnFromDate_s01a(Date value) {
		this.lnFromDate_s01a = value;
	}

	/**
	 * 取得授信期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getLnEndDate_s01a() {
		return this.lnEndDate_s01a;
	}

	/**
	 * 設定授信期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setLnEndDate_s01a(Date value) {
		this.lnEndDate_s01a = value;
	}

	/**
	 * 取得授信期間-年數
	 * <p/>
	 * 100/10/11新增<br/>
	 * 2.自首動日起YY年
	 */
	public Integer getLnYear_s01a() {
		return this.lnYear_s01a;
	}

	/**
	 * 設定授信期間-年數
	 * <p/>
	 * 100/10/11新增<br/>
	 * 2.自首動日起YY年
	 **/
	public void setLnYear_s01a(Integer value) {
		this.lnYear_s01a = value;
	}

	/**
	 * 取得授信期間-月數
	 * <p/>
	 * 2.自首動日起MM個月
	 */
	public Integer getLnMonth_s01a() {
		return this.lnMonth_s01a;
	}

	/**
	 * 設定授信期間-月數
	 * <p/>
	 * 2.自首動日起MM個月
	 **/
	public void setLnMonth_s01a(Integer value) {
		this.lnMonth_s01a = value;
	}

	/**
	 * 取得授信期間-其他
	 * <p/>
	 * 3.其他
	 */
	public String getLnOther_s01a() {
		return this.lnOther_s01a;
	}

	/**
	 * 設定授信期間-其他
	 * <p/>
	 * 3.其他
	 **/
	public void setLnOther_s01a(String value) {
		this.lnOther_s01a = value;
	}

	/**
	 * 設定本次是否客戶申請調升補貼利率
	 * 
	 * @param rescueChgRateFg
	 */
	public void setRescueChgRateFg(String rescueChgRateFg) {
		this.rescueChgRateFg = rescueChgRateFg;
	}

	/**
	 * 取得本次是否客戶申請調升補貼利率
	 * 
	 * @return
	 */
	public String getRescueChgRateFg() {
		return rescueChgRateFg;
	}

	/**
	 * 設定調升簽約日
	 * 
	 * @param rescueChgRateSingDate
	 */
	public void setRescueChgRateSingDate(Date rescueChgRateSingDate) {
		this.rescueChgRateSingDate = rescueChgRateSingDate;
	}

	/**
	 * 取得調升簽約日
	 * 
	 * @return
	 */
	public Date getRescueChgRateSingDate() {
		return rescueChgRateSingDate;
	}

	/**
	 * 設定調整後利率
	 * 
	 * @param rescueChgRate
	 */
	public void setRescueChgRate(BigDecimal rescueChgRate) {
		this.rescueChgRate = rescueChgRate;
	}

	/**
	 * 取得調整後利率
	 * 
	 * @return
	 */
	public BigDecimal getRescueChgRate() {
		return rescueChgRate;
	}

	/**
	 * 設定調升起息日
	 * 
	 * @param rescueChgRateEffectDate
	 */
	public void setRescueChgRateEffectDate(Date rescueChgRateEffectDate) {
		this.rescueChgRateEffectDate = rescueChgRateEffectDate;
	}

	/**
	 * 取得調升起息日
	 * 
	 * @return
	 */
	public Date getRescueChgRateEffectDate() {
		return rescueChgRateEffectDate;
	}

	public Date getActStartDate() {
		return actStartDate;
	}

	public void setActStartDate(Date actStartDate) {
		this.actStartDate = actStartDate;
	}

	/**
	 * 取得信保案件 配偶建檔檢核
	 * 
	 * @param smeMateInfo
	 */
	public String getSmeMateInfo() {
		return smeMateInfo;
	}

	/**
	 * 設定信保案件 配偶建檔檢核
	 * 
	 * @param smeMateInfo
	 */
	public void setSmeMateInfo(String smeMateInfo) {
		this.smeMateInfo = smeMateInfo;
	}

	/**
	 * 取得信保案件檢核配偶建檔之負責人
	 * 
	 * @param smeChkMatePrincipal
	 */
	public String getSmeChkMatePrincipal() {
		return smeChkMatePrincipal;
	}

	/**
	 * 設定信保案件檢核配偶建檔之負責人
	 * 
	 * @param smeChkMatePrincipal
	 */
	public void setSmeChkMatePrincipal(String smeChkMatePrincipal) {
		this.smeChkMatePrincipal = smeChkMatePrincipal;
	}

	/**
	 * 設定是否收取企金授信作業手續費
	 */
	public void setIsOperationFee(String isOperationFee) {
		this.isOperationFee = isOperationFee;
	}

	/**
	 * 取得 是否收取企金授信作業手續費
	 */
	public String getIsOperationFee() {
		return isOperationFee;
	}

	/**
	 * 設定授信作業手續費幣別
	 */
	public void setOperationFeeCurr(String operationFeeCurr) {
		this.operationFeeCurr = operationFeeCurr;
	}

	/**
	 * 取得授信作業手續費幣別
	 */
	public String getOperationFeeCurr() {
		return operationFeeCurr;
	}

	/**
	 * 設定授信作業手續費金額
	 */
	public void setOperationFeeAmt(BigDecimal operationFeeAmt) {
		this.operationFeeAmt = operationFeeAmt;
	}

	/**
	 * 取得授信作業手續費金額
	 */
	public BigDecimal getOperationFeeAmt() {
		return operationFeeAmt;
	}

	/**
	 * 設定授信作業手續費最晚收取日
	 */
	public void setOperationFeeDueDate(Date operationFeeDueDate) {
		this.operationFeeDueDate = operationFeeDueDate;
	}

	/**
	 * 取得授信作業手續費最晚收取日
	 */
	public Date getOperationFeeDueDate() {
		return operationFeeDueDate;
	}
}
