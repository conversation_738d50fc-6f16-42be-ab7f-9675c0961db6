/* 
 * LMS2415V04Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.panels.LMS2415FilterPanel;

/**
 * <pre>
 * [個金]覆審報告表 分行_已覆核已核定
 * </pre>
 * 
 * @since 2012/2/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/15,jessica,new
 *          </ul>
 */
@Controller
@RequestMapping("/crs/lms2415v04")
public class LMS2415V04Page extends AbstractEloanInnerView {

	@Autowired
	RetrialService retrialService;
	
	public LMS2415V04Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {

		setGridViewStatus(RetrialDocStatusEnum.已覆核已核定);
		// 外Grid上的Button
		if(retrialService.overSeaProgram()){
			addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Filter);
		}else{
			addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Filter,
					LmsButtonEnum.Print);
		}
		
		renderJsI18N(LMS2415V01Page.class);
		renderRespMsgJsI18N("EFD3026"); // 多render一個msgi18n

		setupIPanel(new LMS2415FilterPanel(PANEL_ID), model, params);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript","loadScript('pagejs/crs/LMS2415V01Page');");
	}
}
