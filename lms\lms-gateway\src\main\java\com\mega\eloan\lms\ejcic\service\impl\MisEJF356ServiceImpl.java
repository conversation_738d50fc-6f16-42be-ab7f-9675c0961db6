/* 
 * MisEJF356ServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service.impl;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.mega.eloan.lms.ejcic.service.MisEJF356Service;

/**
 * <pre>
 * BAM302->EJV35601->EJF356(共同債務資料)
 * </pre>
 * @since 2012/8/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/8/7,TimChiang,new
 *          </ul>
 */
@Service
public class MisEJF356ServiceImpl extends AbstractEjcicJdbc implements MisEJF356Service {

	@Override
	public List<Map<String, Object>> findLoanAmountAndBalance(String qDate, List<String> compIds) {
		String sql = getSqlBySqlId("BAM302.findLoanAmountAndBalance");
		String compIdSql = StringUtils.repeat("?,", compIds.size());
		sql = MessageFormat.format(sql, new Object[]{ compIdSql.substring(0, compIdSql.lastIndexOf(',')) });
		List<String> args = new ArrayList<String>();
		args.addAll(compIds);
		args.add(qDate);
		return getJdbc().queryForList(sql, args.toArray());
	}
	
	@Override
	public List<Map<String, Object>> findLoanAmountAndBalance(String qDate, List<String> compIds
			, List<String> prIds) {
		String sql = getSqlBySqlId("BAM302.findLoanAmountAndBalanceInPrIds");
		String compIdSql = StringUtils.repeat("?,", compIds.size());
		String prIdSql = StringUtils.repeat("?,", prIds.size());
		sql = MessageFormat.format(sql, new Object[]{ compIdSql.substring(0, compIdSql.lastIndexOf(','))
				, prIdSql.substring(0, prIdSql.lastIndexOf(',')) });
		List<String> args = new ArrayList<String>();
		args.addAll(compIds);
		args.addAll(prIds);
		args.add(qDate);
		return getJdbc().queryForList(sql, args.toArray());
	}	
}
