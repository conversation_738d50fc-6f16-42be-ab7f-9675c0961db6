var initDfd = initDfd || new $.Deferred();
initDfd.done(function(json){
	build_c241m01c(json);
	//在 build 完頁面後,若 M01 是 lockDoc, 也跟著 lock
	if(json['initControl_lockDoc']){
		$("#tabForm").lockDoc();
	}
	//===================================================
	$("#btn_c241m01c_latestVer").click(function(){
		saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){
				$.ajax({
		           type: "POST",
		           handler: _handler,
		           data: {
		               formAction: "c241m01c_latestVer",
		               mainOid: $("#mainOid").val()
		           }
		           }).done(function(responseData){
		        	   	var page = responseJSON.page;
	        	   		var tData = {'mainDocStatus': $("#mainDocStatus").val()
   		        	   		, 'mainId': $("#mainId").val()
   		        	   		, 'mainOid': $("#mainOid").val()
		        	   	};
		        	   	$.form.submit({ url: page , data: tData });
		       });
			}
        });
	});
	//===================================================
	$("#btn_c241m01c_defaultVal").click(function(){
		$.ajax({
           type: "POST",
           handler: _handler,
           data: {
               formAction: "c241m01c_defaultVal",
               mainOid: $("#mainOid").val()
           }
           }).done(function(responseData){
           	var tabForm = $("#tabForm");
           	
           	var map = responseData.defVal;
           	$.each(map, function(def_key, def_val) {
           		$("[name=_chkResult_"+def_key+"][value="+def_val+"]").prop("checked", true);
           	});
           				
       });
	});	
	//===================================================
	function build_c241m01c(json){
		{
			var dyna = [];		
			
			$.each(['A', 'B', 'C', 'D', 'E', 'F'], function(idx_itemType, itemType) {
				var itemTypeDesc = json.c241m01c_title[itemType];
				var arr = json.c241m01c_list[itemType];
				var c241m01c_chkText_maxlength = json.c241m01c_chkText_maxlength; 
				var	c241m01c_chkText_maxlengthC = json.c241m01c_chkText_maxlengthC;
				if(arr.length>0){
					var atFirst = true;
					//每一個 itemType 包含的項目
					$.each(arr, function(idx, jsonItem) {
						//不要只取 oid, 可能和 responseJSON 內的值重複
						dyna.push("<tr style='vertical-align:top' c241m01c_oid='c241m01c_"+jsonItem.oid+"' c241m01c_itemSeq='"+jsonItem.itemSeq+"'>");
						if(atFirst==true){						
							dyna.push("   <td rowspan='"+arr.length+"'>"+(to_vertical_str(itemTypeDesc))+"</td>");
							atFirst = false;
						}					
						dyna.push("   <td align='right'>&nbsp;&nbsp;"+jsonItem.itemSeq+"&nbsp;&nbsp;<div class='item' style='display:none'>"+jsonItem.itemNo+":"+jsonItem.chkResult+"</div></td>");
						dyna.push("   <td>"+jsonItem.chkItem);
						if(jsonItem.ptItem=='Y'){
							dyna.push("<div style='margin-top:12px;'>");
							
							dyna.push(((json['initControl_lockDoc'])?"<span>應負責經理：</span>":"<input type='button' value='應負責經理：' class='btnUpdateItemNoPtMgrId'>")
								+"<span class='color-blue ptMgrIdName' id='ptMgrIdName_"+(jsonItem.itemNo)+"'>"
								+jsonItem.ptMgrId+" "+jsonItem._ptMgrName
								+"</span>"
							);	
							
							dyna.push("</div>");
						}
						
						// J-108-0268 逾期情形
//						if(jsonItem._overDue=='Y'){
//							dyna.push("<div style='margin-top:12px;'>");
//							dyna.push(((json['initControl_lockDoc'])?"<span>逾期情形：</span>":"<input type='button' value='查詢逾期情形' class='btnOverDue'>")
//								+"<span class='overDueText' id='overDueText'>"+jsonItem.overDueText+"</span>");
//							dyna.push("</div>");
//						}
												
						dyna.push("   </td>");
						{//======================
						 //覆審結果 	
							//[]是 []否 [] 一
							var _name_chkResult = "_chkResult_"+(jsonItem.itemNo);
							var fmt = jsonItem._chkResult_fmt;
							dyna.push("   <td nowrap>");		
							build_radio(dyna, _name_chkResult, fmt, jsonItem.chkResult);						
							dyna.push("   </td>");	
						}
						{//======================
						 //內容說明 
							var _td_border = "";
							if(jsonItem._td_border && jsonItem._td_border=="Y"){
								_td_border = " style='border:2px solid red;' "
							}						
							dyna.push("<td "+_td_border+">");
							//處理prefix
							if(jsonItem._prefix && jsonItem._prefix.length>0){
								dyna.push("<span style='font-size:x-small;'>"+jsonItem._prefix+"</span>");
							}
							{//處理chkPreReview
								if(jsonItem._chkPreReview_fmt){
									var _chkPreReview_fmt = jsonItem._chkPreReview_fmt;
									var _name_chkPreReview = "_chkPreReview_"+(jsonItem.itemNo);
									dyna.push("<div style='white-space: nowrap;'>");								
									build_radio(dyna, _name_chkPreReview, _chkPreReview_fmt, jsonItem.chkPreReview);
									dyna.push("</div>");
								}	
							}
							
							var _name_chkText = "_chkText_"+(jsonItem.itemNo);
							
							dyna.push("<textarea name='"+_name_chkText+"' id='"+_name_chkText+"' maxlength='"+c241m01c_chkText_maxlength+"' maxlengthC='"+c241m01c_chkText_maxlengthC+"' class='my_taClass' rows='1' ></textarea>");
							
							dyna.push("</td>");	
						}						
						dyna.push("</tr>");
						
					});	
				}else{
					//itemType 下的項目若為0,不顯示
				}
			});
			$("#c241m01c_content").append(dyna.join("\n"));
			
			$.each(['A', 'B', 'C', 'D', 'E', 'F'], function(idx_itemType, itemType) {
				var itemTypeDesc = json.c241m01c_title[itemType];
				var arr = json.c241m01c_list[itemType];
				var c241m01c_chkText_maxlength = json.c241m01c_chkText_maxlength; 
				var	c241m01c_chkText_maxlengthC = json.c241m01c_chkText_maxlengthC;
				if(arr.length>0){
					$.each(arr, function(idx, jsonItem) {
						var _name_chkText = "_chkText_"+(jsonItem.itemNo);
						$("textarea.my_taClass[name="+_name_chkText+"]").val(jsonItem.chkText);
					});
				}
			});
			
			$(".btnUpdateItemNoPtMgrId").click(function(){
				//為讓 ajax 能存取
				var $tr = $(this).closest("tr");
				
				var c241m01c_oid = $tr.attr("c241m01c_oid");
				var c241m01c_itemSeq = $tr.attr("c241m01c_itemSeq");
				var exist_val = $tr.find(".ptMgrIdName").val();
				//======
				var options = {'title':"第"+c241m01c_itemSeq+"項" ,'exist_val': exist_val};
				
				var my_dfd = $.Deferred();				
				my_dfd.done(function(json){					
					$.ajax({type: "POST", handler: _handler,
						data: { formAction: "update_itemNo_ptMgrId"
	        	            , 'model_oid':c241m01c_oid
	        	            , 'ptMgrId':json.ptMgrId
	        	        }
	        	        }).done(function(json){
	        	        	$tr.find(".ptMgrIdName").val(json.id+" "+json.name);
	        		});
		    	});    		
				
				RetrialPtMgrIdPanelAction.open(options, my_dfd);
						
			});
			
			// 逾期情形
			$(".btnOverDue").click(function(){
				$.ajax({
					type: "POST",
		            handler: _handler,	//LMS2411M01Formhandler
		            data: {
						formAction: "getOverDueData",
						mainOid: $("#mainOid").val()
					}
		            }).done(function(json){
						$(".overDueText").val(json.overDueText);
		        });			
			});
		}
		
		//=====================
		{//Z_電腦建檔
			var dyna = [];		
			var fieldTitle = "";
			var td_colCnt = 5;//每1個rdo,算1個td
			$.each(['Z'], function(idx_itemType, itemType) {
				var itemTypeDesc = json.c241m01c_title[itemType];
				var arr = json.c241m01c_list[itemType];
				
				//指定 title
				fieldTitle = itemTypeDesc;
				
				if(arr.length>0){
					//每一個 itemType 包含的項目
					$.each(arr, function(idx, jsonItem) {
						
						var _name_chkResult = "_chkResult_"+(jsonItem.itemNo);
						var fmt = jsonItem._chkResult_fmt;
						dyna.push("<tr style='vertical-align:top' class='"+(fmt.length==0?"":"z_chkItem")+"'>");						
						if(fmt.length==0){
							
							dyna.push("   <td nowrap colspan='"+td_colCnt+"'>"+jsonItem.chkItem);
							if(jsonItem._u_note){
								dyna.push("<br><span class='color-red'>"+jsonItem._u_note+"</span>");
							}
							dyna.push("   </td>");
						}else{
							if(jsonItem._itemPost){
								dyna.push("   <td nowrap colspan='"+td_colCnt+"'>"+jsonItem.chkItem );
								
								var local_dyna = [];
								build_radio(local_dyna, _name_chkResult, fmt, jsonItem.chkResult);
								
								$.each(local_dyna, function(idx, v) {
									dyna.push("&nbsp;"+v+"&nbsp;&nbsp;");	
								});
								
								dyna.push(jsonItem._itemPost+"</td>");
							}else{
								dyna.push("   <td style='width:15px;'>&nbsp;</td><td nowrap>"+jsonItem.chkItem+"</td>");
								{	
									var local_dyna = [];
									build_radio(local_dyna, _name_chkResult, fmt, jsonItem.chkResult);
									
									$.each(local_dyna, function(idx, v) {
										dyna.push("   <td nowrap>"+v+"</td>");	
									});								
								}	
							}
						}
						dyna.push("</tr>");
					});	
				}else{
					//itemType 下的項目若為0,不顯示
				}
			});
			
			if(dyna.length>0){
				$("#btn_all_z_y").click(function(){
					$(".z_chkItem").find("input[type=radio][value=Y]").prop("checked", true);
				});
				
				//---
				$("#c241m01c_zsysinfo_title").html(fieldTitle);
				$("#c241m01c_zsysinfo_content").html(dyna.join("\n"));
				//---
				$("#c241m01c_zsysinfo").show();
				//---
				$("input[name=_chkResult_ZB1A]:radio").change(function(){
					if($(this).val()=="N"){
						$("input[name=_chkResult_ZB11][value=K]:radio").prop("checked", true);
						$("input[name=_chkResult_ZB12][value=K]:radio").prop("checked", true);
						$("input[name=_chkResult_ZB13][value=K]:radio").prop("checked", true);
					}
				});
			}			
		}
		
		//=====================
		{//特別處理 textarea 的高度
			$("textarea.my_taClass").css('overflow-y','hidden').css('width', '150px').bind("keyup focus", expandText );
	        //在初始化時,若 textarea 有N列,展開
	        $.each( $("textarea.my_taClass"), function (idx, element) {
	            if ( $(element).val().length > 0) {                
	                $(element).trigger('focus');            
	            }            
	        });	
		}
	}	
	
	function build_radio(dyna, radioName, srcStr, chooseVal){
		$.each(srcStr.split("|"), function(idx, val_item) {
			var radioVal = val_item.substring(0, 1);
			var attr = (chooseVal==radioVal)?" checked ":"";
			dyna.push("<label><input name='"+radioName+"' id='"+(radioName+"_v_"+radioVal)+"' type='radio' value='"+radioVal +"' "+attr+">"+i18n.lms2411m01[("label."+val_item)]+"</label>");
		});
	}

	function to_vertical_str(src){
		var charArr = [];						
		for (var idx_charc = 0; idx_charc < src.length; idx_charc++)
		{
			charArr.push( src.charAt(idx_charc) );						    
		}
		return charArr.join("<br/>");
	}	
});

//http://perplexed.co.uk/596_expanding_textarea_as_you_type.htm
var expandText = function(){    
    var el = this;
    //if(el.tagName!=="textarea"){return;}
    // has the scroll height changed?, we do this because we can successfully change the height
    var prvLen = el.preValueLength;
    el.preValueLength = el.value.length;
    if(el.scrollHeight===el.prvScrollHeight&&el.prvOffsetHeight===el.offsetHeight&&el.value.length>=prvLen){    	
        return;
    }
    while(el.rows>1 && el.scrollHeight<el.offsetHeight){
        el.rows--;
    }
    var h=0;
    while(el.scrollHeight > el.offsetHeight && h!==el.offsetHeight && (h=el.offsetHeight) ){
        el.rows++;
    }    
    el.rows++;    
    el.prvScrollHeight = el.scrollHeight;
    el.prvOffsetHeight = el.offsetHeight;     
};

function showItemNo(){
	$(".item").show().css("color","blue");
}