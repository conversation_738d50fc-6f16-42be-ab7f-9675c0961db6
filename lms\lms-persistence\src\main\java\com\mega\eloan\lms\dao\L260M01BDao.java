/* 
 * L260M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L260M01B;

/** 貸後管理簽章欄檔 **/
//
public interface L260M01BDao extends IGenericDao<L260M01B> {

	L260M01B findByOid(String oid);
	
	List<L260M01B> findByMainId(String mainId);
	
	L260M01B findByUniqueKey(String mainId, String branchType, String branchId, String staffNo, String staffJob);

	List<L260M01B> findByIndex01(String mainId, String branchType, String branchId, String staffNo, String staffJob);
}