/* 
 * ELF509.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;


/** 各項費用介面檔 **/

public class ELF509 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 
	 * 簽案案件編號<p/>
	 * ELF509_CASE_NO<br/>
	 */
	@Column(name="ELF509_CASE_NO", length=20, columnDefinition="CHAR(20)", nullable=false,unique = true)
	private String elf509_case_no;

	/** 
	 * 費用代碼<p/>
	 * ELF509_FEE_ITEM<br/>
	 */
	@Column(name="ELF509_FEE_ITEM", length=2, columnDefinition="CHAR(2)", nullable=false,unique = true)
	private String elf509_fee_item;

	/** 
	 * 案件序號<p/>
	 * ELF509_SEQ_NO	 
	*@Id
	*@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	*/
	@Column(name="ELF509_SEQ_NO", length=5, columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer elf509_seq_no;

	/** 
	 * 費用幣別<p/>
	 * ELF509_FEE_SWFT
	 */
	@Column(name="ELF509_FEE_SWFT", length=3, columnDefinition="CHAR(3)")
	private String elf509_fee_swft;

	/** 
	 * 簽案之費用金額<p/>
	 * ELF509_FEE_AMT
	 */
	@Column(name="ELF509_FEE_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf509_fee_amt;
	
	/** 
	 * 已收費用金額<p/>
	 * ELF509_FEE_AMT_RE
	 */
	@Column(name="ELF509_FEE_AMT_RE", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf509_fee_amt_re;

	/** 
	 * 狀態碼<p/>
	 * ELF509_STATUS
	 */
	@Column(name="ELF509_STATUS", length=1, columnDefinition="CHAR(1)")
	private String elf509_status;
	
	/** 
	 * 借款人身分證統一編號<p/>
	 * ELF509_CUSTID<br/>
	 */
	@Column(name="ELF509_CUSTID", length=10, columnDefinition="CHAR(10)")
	private String elf509_custid;

	/** 
	 * 重複序號<p/>
	 * ELF509_DUPNO<br/>
	 */
	@Column(name="ELF509_DUPNO", length=1, columnDefinition="CHAR(1)")
	private String elf509_dupno;

	/** 
	 * 本件額度序號<p/>
	 * ELF509_CNTRNO
	 */
	@Column(name="ELF509_CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String elf509_cntrno;

	/** 
	 * 授信帳號<p/>
	 * ELF509_LOAN_NO
	 */
	@Column(name="ELF509_LOAN_NO", length=14, columnDefinition="CHAR(14)")
	private String elf509_loan_no;
	
	/** 櫃員代號 **/
	@Column(name="ELF509_TELLER", length=6, columnDefinition="CHAR(06)")
	private String elf509_teller;

	/** 主管代號 **/
	@Column(name="ELF509_SUPVNO", length=6, columnDefinition="CHAR(06)")
	private String elf509_supvno;

	/** ELOAN寫入時間 **/
	@Column(name="ELF509_ELOAN_TIME", columnDefinition="TIMESTAMP")
	private Date elf509_eloan_time;

	/** 
	 * ALOAN寫入時間<p/>
	 * N
	 */
	@Column(name="ELF509_ALOAN_TIME", columnDefinition="TIMESTAMP")
	private Date elf509_aloan_time;
	
	
	/** 
	 * 設定簽案案件編號<p/>
	 * ELF509_CASE_NO<br/>
	 */
	public void setElf509_case_no(String value) {
		this.elf509_case_no = value;
	}

	/** 
	 * 取得簽案案件編號<p/>
	 * ELF509_CASE_NO<br/>
	 */
	public String getElf509_case_no() {
		return this.elf509_case_no;
	}
	
	/** 
	 * 設定費用代碼<p/>
	 * ELF509_FEE_ITEM<br/>
	 */
	public void setElf509_fee_item(String value) {
		this.elf509_fee_item = value;
	}

	/** 
	 * 取得費用代碼<p/>
	 * ELF509_FEE_ITEM<br/>
	 */
	public String getElf509_fee_item() {
		return this.elf509_fee_item;
	}

	/** 
	 * 設定費用幣別<p/>
	 * ELF509_FEE_SWFT
	 */
	public void setElf509_fee_swft(String value) {
		this.elf509_fee_swft = value;
	}

	/** 
	 * 取得費用幣別<p/>
	 * ELF509_FEE_SWFT
	 */
	public String getElf509_fee_swft() {
		return this.elf509_fee_swft;
	}

	/** 
	 * 設定簽案之費用金額<p/>
	 * ELF509_FEE_AMT
	 */
	public void setElf509_fee_amt(BigDecimal value) {
		this.elf509_fee_amt = value;
	}

	/** 
	 * 取得簽案之費用金額<p/>
	 * ELF509_FEE_AMT
	 */
	public BigDecimal getElf509_fee_amt() {
		return this.elf509_fee_amt;
	}

	/** 
	 * 設定已收費用金額<p/>
	 * ELF509_FEE_AMT_RE
	 */
	public void setElf509_fee_amt_re(BigDecimal value) {
		this.elf509_fee_amt_re = value;
	}

	/** 
	 * 取得已收費用金額<p/>
	 * ELF509_FEE_AMT_RE
	 */
	public BigDecimal getElf509_fee_amt_re() {
		return this.elf509_fee_amt_re;
	}

	/** 
	 * 設定狀態碼<p/>
	 * ELF509_STATUS
	 */
	public void setElf509_status(String value) {
		this.elf509_status = value;
	}

	/** 
	 * 取得狀態碼<p/>
	 * ELF509_STATUS
	 */
	public String getElf509_status() {
		return this.elf509_status;
	}

	/** 
	 * 設定借款人身分證統一編號<p/>
	 * ELF509_CUSTID<br/>
	 */
	public void setElf509_custid(String value) {
		this.elf509_custid = value;
	}

	/** 
	 * 取得借款人身分證統一編號<p/>
	 * ELF509_CUSTID<br/>
	 */
	public String getElf509_custid() {
		return this.elf509_custid;
	}

	/** 
	 * 設定重複序號<p/>
	 * ELF509_DUPNO<br/>
	 */
	public void setElf509_dupno(String value) {
		this.elf509_dupno = value;
	}

	/** 
	 * 取得重複序號<p/>
	 * ELF509_DUPNO<br/>
	 */
	public String getElf509_dupno() {
		return this.elf509_dupno;
	}

	/** 
	 * 設定本件額度序號<p/>
	 * ELF509_CNTRNO
	 */
	public void setElf509_cntrno(String value) {
		this.elf509_cntrno = value;
	}

	/** 
	 * 取得本件額度序號<p/>
	 * ELF509_CNTRNO
	 */
	public String getElf509_cntrno() {
		return this.elf509_cntrno;
	}

	/** 
	 * 設定授信帳號<p/>
	 * ELF509_LOAN_NO
	 */
	public void setElf509_loan_no(String value) {
		this.elf509_loan_no = value;
	}

	/** 
	 * 取得授信帳號<p/>
	 * ELF509_LOAN_NO
	 */
	public String getElf509_loan_no() {
		return this.elf509_loan_no;
	}

	/** 設定櫃員代號 **/
	public void setElf509_teller(String value) {
		this.elf509_teller = value;
	}

	/** 取得櫃員代號 **/
	public String getElf509_teller() {
		return this.elf509_teller;
	}

	/** 設定主管代號 **/
	public void setElf509_supvno(String value) {
		this.elf509_supvno = value;
	}

	/** 取得主管代號 **/
	public String getElf509_supvno() {
		return this.elf509_supvno;
	}

	/** 設定ELOAN寫入時間 **/
	public void setElf509_eloan_time(Date value) {
		this.elf509_eloan_time = value;
	}

	/** 取得ELOAN寫入時間 **/
	public Date getElf509_eloan_time() {
		return this.elf509_eloan_time;
	}

	/** 
	 * 設定ALOAN寫入時間<p/>
	 * N
	 */
	public void setElf509_aloan_time(Date value) {
		this.elf509_aloan_time = value;
	}

	/** 
	 * 取得ALOAN寫入時間<p/>
	 * N
	 */
	public Date getElf509_aloan_time() {
		return this.elf509_aloan_time;
	}

	/** 
	 * 設定案件序號<p/>
	 * ELF509_SEQ_NO	 
	*/
	public void setElf509_seq_no(Integer value) {
		this.elf509_seq_no = value;
	}

	/** 
	 * 取得案件序號<p/>
	 * ELF509_SEQ_NO	 
	*/
	public Integer getElf509_seq_no() {
		return this.elf509_seq_no;
	}


}
