/* 
 * LMSService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.common.aml.AmlStrategy;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01P;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L120S09C;
import com.mega.eloan.lms.model.L120S26A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01S;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L164S01A;

/**
 * <pre> 
 * BY 專案共用Service 
 * </pre>
 * 
 * @since 2012/1/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/13,REX,new
 *          </ul>
 */
/**
 * <AUTHOR>
 * 
 */
public interface AMLRelateService extends ICapService {

	public <T extends GenericBean> T findModelByMainId(Class clazz,
			String mainId);

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	public L120S09A findL120s09aByOid(String oid);

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	public List<L120S09A> findL120s09asByOids(String[] oids);

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	public List<L120S09A> findL120s09aByMainId(String mainId);

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	public void saveL120s09aList(List<L120S09A> list);

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	public void deleteListL120s09a(List<L120S09A> list);

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	public List<L120S09A> findListL120s09aByCustId(String mainId,
			String custId, String dupNo);

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	public List<L120S09A> findListL120s09aByCustName(String mainId,
			String custName);

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	public L120S09A findL120s09aMaxQDateByMainId(String mainId);

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	public List<L120S09A> findL120s09aByMainIdWithOrder(String mainId);

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	public List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType);

	// J-106-0029-003 洗錢防制-新增實質受益人
	public L120S01P findL120s01pByOid(String oid);

	// J-106-0029-003 洗錢防制-新增實質受益人
	public L120S01P findL120s01pByRNameWithRType(String mainId, String custId,
			String dupNo, String rType, String rName);

	// J-106-0029-003 洗錢防制-新增實質受益人
	public boolean deleteListL120s01p(String[] oids);

	// J-106-0029-003 洗錢防制-新增實質受益人
	public L120S01P findL120s01pMaxSeqNumByMainIdAndCustIdWithRType(
			String mainId, String custId, String dupNo, String rType);

	// J-106-0029-003 洗錢防制-新增實質受益人
	public List<L120S01P> findL120s01pByMainIdAndCustIdWithRType(String mainId,
			String custId, String dupNo, String rType);

	// J-106-0029-003 洗錢防制-新增實質受益人
	public List<L120S01P> findL120s01pByMainIdAndCustIdWithoutRType(
			String mainId, String custId, String dupNo);

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId);

	// J-106-0029-003 洗錢防制-新增實質受益人
	public L120S01B findL120s01bByUniqueKey(String mainId, String custId,
			String dupNo);

	// J-106-0029-003 洗錢防制-新增實質受益人
	public List<L120S01P> findL120s01pByMainIdAndCustIdWithRTypeOrderForBuildStr(
			String mainId, String custId, String dupNo, String rType);

	// J-106-0029-003 洗錢防制-新增實質受益人
	public void saveL120s01pList(List<L120S01P> list);

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	public void deleteListL120s01p(List<L120S01P> list);

	// J-106-0029-003 洗錢防制-新增實質受益人
	public List<L120S01P> findL120s01pByMainIdWithRType(String mainId,
			String rType);

	// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	public L164S01A findL164s01aByUniqueKey(String mainId, String custId,
			String dupNo);

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤 送呈主管時檢核黑名單是否有漏掉的
	 */
	public void chkBlackListFullExitForRptDoc(String mainId, boolean chkScanDone)
			throws CapException;

	/**
	 * J-106-0029-003 洗錢防制-新增實質受益人
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	public String chkBeneficiaryIsOkForRptDoc(String mainId,
			LinkedHashMap<String, String> custIdMapAml) throws CapException;

	/**
	 * 組出實質受益人資訊
	 * 
	 * @param l140m01a
	 * @return 實質受益人資訊
	 */
	public String contactBeneficiary(String mainId, String custId,
			String dupNo, String rType);

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤送呈主管時檢核黑名單是否有漏掉的 (動審表用)
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	public String chkBeneficiaryIsOkForDrawDown(String mainId,
			LinkedHashMap<String, String> allBorrowerIdMap) throws CapException;

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤送呈主管時檢核黑名單是否有漏掉的 (動審表用)
	 */
	public void chkBlackListFullExitForDrawDown(String mainId,
			boolean chkScanDone) throws CapException;

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤 (動審表)
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	public String chkMainBorrowerInL162S01A(String mainId,
			LinkedHashMap<String, String> cntrNoMainBorrowerMap)
			throws CapException;

	public List<L120S01P> findL120s01pByMainIdWithoutRType(String mainId);

	public List<L120M01E> findL120m01eByMainId(String mainId);

	/**
	 * J-106-0029-002/003/004 洗錢防制-新增洗錢防制頁籤 新增實質受益人 動審表新增洗錢防制頁籤
	 * 判斷登入分行是否需檢核無實質受益人不得送呈主管
	 * 
	 * @return
	 */
	public boolean needChkBeneficiaryBeforeSendBoss(String QueryBrId);

	/**
	 * 判斷關係戶是否需要統編 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param custRelationStr
	 * @return
	 */
	public boolean isCustRelationNeedCustId(String custRelationStr);

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @param relation
	 */
	public void reSetL120S09A(String mainId, String custId, String dupNo,
			String custName, String relation, String custEName, String country);

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 */
	public void getLMSAndCesDocAMLCustNameMap(L120M01A l120m01a,
			Map<String, String> lmsAndCesAmlListIdCNameMap,
			Map<String, String> lmsAndCesAmlListIdENameMap,
			Map<String, String> lmsAndCesAmlListNameMap);

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param mode
	 *            1: ALL NAME(中+英文戶名) 2: ONLY ENAME(僅英文戶名)
	 * @param mainId
	 *            動審表或簽報書MAINID，串L120S01P用
	 * @param l120m01a
	 * @param custRelation
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @param custEName
	 * @return
	 * @throws CapException
	 */
	public Map<String, String> queryL120s09aNewCustNameForAML(String mode,
			String mainId, L120M01A l120m01a, String custRelation,
			String custId, String dupNo, String custName, String custEName)
			throws CapException;

	/**
	 * 從動審表取得簽報書 J-106-0029-003 洗錢防制-新增實質受益人
	 * 
	 * @param l160m01a
	 * @return
	 */
	public L120M01A findL120m01aByL160m01a(L160M01A l160m01a);

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」
	 */
	public L120S09B findL120s09bByMainId(String mainId);

	/**
	 * 設定案號 J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業
	 * ，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
	 * 
	 * P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML相關功能。
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 */
	public L120S09B initL120s09b(GenericBean model, String queryBrId)
			throws CapMessageException;

	/**
	 * 取得調查編號-> 民國年 + 分行別+{LMS企金、CLS個金}+末五碼流水號 J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵
	 * 、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
	 * 
	 * @param l120m01a
	 *            簽報書
	 * @param showSchema
	 *            是否顯示Schema
	 * @return 案號 民國年 + 分行別+{LMS企金、CLS個金}+末五碼流水號
	 */
	public String getRefNo(L120S09B l120s09b, String docType);

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」
	 */
	public List<L120S09A> findL120s09aByMainIdWithOrder1(String mainId);

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 判斷是不是走新的AML FUNCTION
	 * 
	 * @return
	 */
	public Map<String, String> checkAmlNewFuncMode(String QueryBrId);

	/**
	 * J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
	 * 判斷"制裁/管制名單掃描調查結果說明"欄位是否要在畫面上顯示
	 * 
	 * @param mainId
	 * @param isCls
	 *            是否為國內消金
	 * @return
	 */
	public boolean isNcResultRemarkShow(String mainId, boolean isCls);

	/**
	 * J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位 判斷"高風險調查結果說明"欄位是否要在畫面上顯示
	 * 
	 * @param mainId
	 * @param isCls
	 *            是否為國內消金
	 * @return
	 */
	public boolean isHighRiskRemarkShow(String mainId, boolean isCls);

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 取得AmlStrategy
	 * 
	 * @return
	 */
	public AmlStrategy getAmlStrategy(String QueryBrId);

	/**
	 * 判斷簽報書AML 結果 J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業
	 * ，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
	 * 
	 * @param ncResutl
	 * @return
	 */
	public Map<String, String> getCaseReportAmlStatus(String ncResutl,
			Map<String, String> queryMap);

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 取得借款人基本資料
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L120S01A> findL120s01aByMainId(String mainId);

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 檢核借款人的0024拒絕交易狀態
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public HashMap<String, String> chkCustIn0024Reject(String mainId)
			throws CapException;

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 取得借款人的0024拒絕交易狀態
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public String getCustIn0024AmlStatus(String custId, String dupNo);

	/**
	 * 檢核簽報書調查狀態是否已經完成可以送呈主管 J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 取得借款人的0024拒絕交易狀態
	 */
	public void chkNcResultFinishCanSendBoss(String mainId) throws CapException;

	/**
	 * J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
	 * 
	 * 判斷"制裁/管制名單掃描調查結果說明"、"高風險調查結果說明"欄位 如果這兩個欄位該顯示代表應該要輸入值，但卻沒有值的話要出現在提示訊息
	 * 
	 * isCls : 是否為國內消金
	 */
	public String chkNcResultMarkAndHighRiskMarkForRejtMsg(String mainId,
			boolean isCls);

	/**
	 * J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
	 * 
	 * 判斷"制裁/管制名單掃描調查結果說明"、"高風險調查結果說明"欄位 如果這兩個欄位該顯示代表應該要輸入值，但卻沒有值的話要擋傳送
	 */
	public void chkNcResultMarkAndHighRiskMarkCanSendBoss(String mainId)
			throws CapException;

	/**
	 * 檢核0024有拒絕交易時不得送呈 J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 取得借款人的0024拒絕交易狀態
	 */
	public void chk0024RejecjTrancCanNotSendBoss(String mainId)
			throws CapException;

	public List<L140M01A> findL140m01aListByMainIdList(String[] mainId);

	/**
	 * 判斷簽報書AML/CFT頁籤是否要顯是0024 AML STATUS 與提示訊息
	 * 
	 * @param ncResutl
	 * @return
	 */
	public String getIsShow0024AmlStatus(String QueryBrId);

	/**
	 * 檢核L120S09A是否需要英文戶名
	 */
	public boolean isL120s09ANeedEngName(String custRelation, String QueryBrId);

	/**
	 * J-106-0238-003 判斷登入分行是否需檢核AML不OK時不得送呈主管
	 * 
	 * @return
	 */
	public boolean needChkAmlOkBeforeSendBoss(String QueryBrId);

	/**
	 * J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public String getCustLuvRiskLevel(String brNo, String custId, String dupNo);

	/**
	 * J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
	 * 
	 * @return
	 */
	public boolean needChkAmlCustRiskLevel(String QueryBrId);

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 
	 * @param mainId
	 * @param custIdMapAml
	 * @return
	 * @throws CapException
	 */
	public String chkSeniorMgrIsOkForRptDoc(String mainId,
			LinkedHashMap<String, String> custIdMapAml) throws CapException;

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	public String chkSeniorMgrIsOkForDrawDown(String mainId,
			LinkedHashMap<String, String> allBorrowerIdMap) throws CapException;

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 判斷登入分行是否需檢核無高階管理人員不得送呈主管
	 * 
	 * @return
	 */
	public boolean needChkSeniorMgrBeforeSendBoss(String QueryBrId);

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 判斷登入分行是否需檢核無高階管理人員不得送呈主管
	 * 
	 * @return
	 */
	public String[] getSortCustRelation(String[] strs);

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 判斷登入分行是否需檢核無高階管理人員不得送呈主管
	 * 
	 * @return
	 */
	public boolean isSeniorMgrEffective(Date queryDateS);

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 判斷送呈時不用檢核是否0024至少要有一筆高階管理人員，OV代表所有海外分行不檢核
	 * 
	 * @return
	 */
	public boolean needChk0024MustHaveSeniorMgrBeforeSendBoss(String QueryBrId);

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L120S09A> findL120s09aByMainIdWithShowOrder(String mainId);

	/**
	 * J-107-0176 代發機制 - 判斷是否為代發
	 * 
	 * @param mainId
	 * @return
	 */
	public Map<String, Object> checkInstead(String mainId);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public List<L140M01S> findL140m01sByMainIdType(String mainId, String type);

	/**
	 * 判斷關係戶是否需要國別 J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
	 * 
	 * @param custRelationStr
	 * @return
	 */
	public boolean isCustRelationNeedCountry(String custRelationStr);

	/**
	 * J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
	 */
	public void chkBlackListDataOk(String mainId) throws CapException;

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
	 * 
	 * @param mainId
	 * @param custIdMapAml
	 * @return
	 * @throws CapException
	 */
	public String chkCtrlPeoIsOkForRptDoc(String mainId,
			LinkedHashMap<String, String> custIdMapAml) throws CapException;

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	public String chkCtrlPeoIsOkForDrawDown(String mainId,
			LinkedHashMap<String, String> allBorrowerIdMap) throws CapException;

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
	 * 
	 * @return
	 */
	public boolean needChkCtrlPeoBeforeSendBoss(String QueryBrId);

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。 判斷登入分行是否需檢核無具控制權人不得送呈主管
	 * 
	 * @return
	 */
	public boolean isCtrlPeoEffective(Date queryDateS);

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
	 * 判斷送呈時不用檢核是否0024至少要有一筆具控制權人，OV代表所有海外分行不檢核
	 * 
	 * @return
	 */
	public boolean needChk0024MustHaveCtrlPeoBeforeSendBoss(String QueryBrId);

	/**
	 * J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public String canPassAmlRelativeAndRiskLvlChk(GenericBean mainEntity,
			String custId, String dupNo);

	/**
	 * #J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管
	 * 
	 * @param type
	 *            1:簽報書 2:動審表
	 * @param mainId
	 * @return
	 * @throws CapException
	 */
	public String getPassAmlRelativeAndRiskLvlChkWarnMsg(String type,
			String mainId) throws CapException;

	/**
	 * I-107-0260_05097_B1001 Web e-Loan企金授信系統增加提示訊息【該客戶屬於巴拿馬文件名單】
	 * 
	 * @param custIdMapAml
	 * @return
	 * @throws CapException
	 */
	public String chkCustListInPana(LinkedHashMap<String, String> custIdMapAml)
			throws CapException;

	/**
	 * I-107-0260_05097_B1001 Web e-Loan企金授信系統增加提示訊息【該客戶屬於巴拿馬文件名單】
	 * 
	 * @return
	 */
	public boolean needShowPanaMsg(String QueryBrId);

	/**
	 * I-110-0329_11557_B1001 針對既有自然人客戶增加KYC系統異常機制，KYC異常時可BYPASS
	 * 
	 * @param custId
	 * @param dupNo
	 * @param dept
	 *            查詢客戶之業務往來項目 若為ALL則撈出會用到的01,02,03,07 1 存匯業務 2 授信業務 3 進出口業務 4
	 *            票債券業務 5 信託業務 6 財富管理業務 7 衍生性金融商品業務 8 電子金融業務 9 信用卡 ( 已停用 ) 10 其他
	 * @return
	 * @throws CapException
	 */
	public Map<String, Object> getSCMLUINQListByCustId(String custId,
			String dupNo, String dept) throws CapException;

	/**
	 * P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML相關功能。
	 * 
	 * @return
	 */
	public boolean isOracle(String queryBrId);

	/**
	 * P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML相關功能。
	 * 
	 * @return
	 */
	public boolean isOracle(L120M01A l120m01a);

	/**
	 * 海外分行檢查需不需實質受益人
	 * 
	 * @param c120m01a
	 * @param needCheck
	 * @return
	 */
	public boolean foreignBranchCheckExcl(String unitNo, String custId,
			String dupNo);

	/**
	 * 海外分行檢查需不需實質受益人 J-109-0067_05097_B1001 Web e-Loan
	 * 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
	 * 
	 * @param c120m01a
	 * @param needCheck
	 * @return
	 */
	public boolean needChk0024Exist(String QueryBrId);

	/**
	 * 海外分行檢查需不需實質受益人 J-109-0067_05097_B1001 Web e-Loan
	 * 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
	 * 
	 * @param c120m01a
	 * @param needCheck
	 * @return
	 */
	public boolean needShowNotApplicableWhenNoEffective(String QueryBrId);

	/**
	 * P-108-0046_05097_B1003 Web e-Loan授信系統傳送婉卻紀錄給ORACLE CIF
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean needSendReject(L120M01A l120m01a);

	/**
	 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信 查詢L120S09C資料
	 * 
	 * @param l120s09c
	 * @return
	 */
	public L120S09C findL120s09cByOid(String oid);

	/**
	 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信 查詢L120S09C資料by mainId
	 * 
	 * @param l120s09c
	 * @return
	 */
	public List<L120S09C> findL120s09cByMainId(String mainId);

	/**
	 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信 刪除L120S09C資料by oids
	 * 
	 * @param l120s09c
	 * @return
	 */
	public void deleteL120s09cByOid(String[] oids);

	/**
	 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信 新增態樣檢核表時查詢預設資料
	 * 
	 * @param l120s09c
	 * @return
	 */
	public Map<String, String> queryL120s09cInit(String mainId,
			String[] chooseCust, String versionDate);

	/**
	 * I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
	 * <p/>
	 * 取得簽報書AML List
	 */
	public Map<String, String[]> getAmlListPepsMap(GenericBean model);

	/**
	 * I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
	 * <p/>
	 * 參照 getLMSAndCesDocAMLCustNameMap 內的撈取方法 取得簽報書內徵信文件MainId List
	 */
	public List<String> getCesMainIdList(L120M01A l120m01a);

	/**
	 * J-111-0278_05097_B1001 Web
	 * e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
	 * 
	 * AML本案戶名只能是英數字或標點符號
	 * 
	 * @param caseBrId
	 * @return
	 */
	public boolean isAmlCustNameOnlyEng(String caseBrId);

	/**
	 * J-111-0278_05097_B1001 Web
	 * e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
	 * 
	 * 判斷內容是否有非英文、數字、標點符號
	 * 
	 * @param text
	 * @return
	 */
	public boolean hasFullCharInStringText(String semiText);

	/**
	 * AML傳送掃描時只送英文戶名
	 * 
	 * @param caseBrId
	 * @return
	 */
	public boolean sendAmlOnlyEngName(String caseBrId);

	/**
	 * J-112-0534 取得引入之T70資訊
	 * 
	 * @param mainId
	 * @param custId
	 * @return
	 */
	public L120S26A findL120s26aByMainIdCustId(String mainId, String custId);

	/**
	 * 列印T70功能僅限有負面紀錄的時候才可列印(列印時印出該案件全部的T70清單)
	 * 
	 * @param mainId
	 * @param l120s09aList
	 * @param negCase
	 * @return
	 */
	public List<L120S26A> findL120s26aListForRpt(String mainId,
			List<L120S09A> l120s09aList, boolean negCase);

	/**
	 * 確認是否至少有引入一筆主借款人之徵信報告或資信簡表
	 * 
	 * @param l120m01a
	 * @return
	 */
	public void checkL120m01eForT70(L120M01A l120m01a) throws CapException;

	/**
	 * 撈取L120s09a清單後引入T70資料
	 * 
	 * @param l120m01a
	 * @return
	 */
	public StringBuilder importL120s09aT70Data(L120M01A l120m01a);

	/**
	 * 從聯徵及徵信系統引入T70
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @param cesMainIdList
	 * @return
	 */
	public String importT70(String mainId, String custId, String dupNo,
			String custName, List<String> cesMainIdList);

	/**
	 * 撈取L120s09a清單後引入受告誡處分資訊
	 * 
	 * @param mainId
	 */
	public void importL120s09aWarnData(String mainId);

	/**
	 * 需要引入受告誡處分
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 */
	public void checkCmfwarnpNeed(String mainId) throws CapException;

	/**
	 * 需要引入受告誡處分
	 * 
	 * @param mainId
	 * @param isCls
	 * @return
	 */
	public String checkCmfwarnpNeedMsg(String mainId);

	/**
	 * 取得受告誡處分訊息
	 * 
	 * @param mainId
	 * @return
	 */
	public String checkCmfwarnpResult(String mainId);
	
	/**
	 * 受告誡處分海外案件
	 * 
	 * @param mainId
	 * @return
	 */
	public boolean cmfwarnpOverSea(String mainId);

}