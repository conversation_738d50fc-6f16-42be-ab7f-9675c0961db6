/* 
 * LMS1605S02Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 約據書清單
 * </pre>
 * 
 * @since 2012/02/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/02/21,ICE,new
 *          </ul>
 */
public class LMS9990S03Panel extends Panel {
	
	public LMS9990S03Panel(String id) {
		super(id);
	}

	public LMS9990S03Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;

}
