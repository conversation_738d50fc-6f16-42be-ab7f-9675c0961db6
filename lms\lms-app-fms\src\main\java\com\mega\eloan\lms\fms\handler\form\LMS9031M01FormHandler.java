/* 
 * LMS9031M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.lms.mfaloan.service.MisELF348Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 同一地號擔保品資料查詢
 * </pre>
 * 
 * @since 2012/11/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/02,gary,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9031m01formhandler")
public class LMS9031M01FormHandler extends AbstractFormHandler {

	@Resource
	MisELF348Service mis348Service;

	/**
	 * 取得段(SITE3)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult querySIET3(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult resultSITE3 = new CapAjaxFormResult();
		String SITE1 = params.getString("fCity");
		String SITE2 = params.getString("fZip");
		List<Map<String, Object>> SITE3 = mis348Service.getELF348SITE3(SITE1,
				SITE2);
		Map<String, Object> SITE = new HashMap<String, Object>();
		for (int i = 0; i < SITE3.size(); i++) {
			SITE.put(SITE3.get(i).get("SITE3").toString(),
					SITE3.get(i).get("SITE3"));
		}
		resultSITE3.putAll(SITE);
		result.set("SITE3", resultSITE3);
		return result;
	}// ;

	/**
	 * 取得小段(SITE4)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult querySIET4(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult resultSITE4 = new CapAjaxFormResult();
		String SITE1 = params.getString("fCity");
		String SITE2 = params.getString("fZip");
		String fSITE3 = params.getString("fSITE3");
		List<Map<String, Object>> SITE4 = mis348Service.getELF348SITE4(SITE1,
				SITE2, fSITE3);
		Map<String, Object> SITE = new HashMap<String, Object>();
		for (int i = 0; i < SITE4.size(); i++) {
			SITE.put(SITE4.get(i).get("SITE4").toString(),
					SITE4.get(i).get("SITE4"));
		}
		resultSITE4.putAll(SITE);
		result.set("SITE4", resultSITE4);
		return result;
	}
}
