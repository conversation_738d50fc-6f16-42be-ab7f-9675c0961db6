/* 
 * LocaleChangeInterceptor.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.iisigroup.cap.mvc.i18n;

import java.util.Locale;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.support.RequestContextUtils;

import tw.com.iisi.cap.utils.CapWebUtil;

/**
 * <pre>
 * 切換語系動作
 * </pre>
 * 
 * @since 2011/11/30
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/11/30,rodeschen,new
 *          <li>2013/12/30,tammy,以RequestContextUtils.getLocale(request)取得
 *          </ul>
 */
public class LocaleChangeInterceptor extends org.springframework.web.servlet.i18n.LocaleChangeInterceptor {

    /*
     * 資料進入Controller前的預處理
     * 
     * @see org.springframework.web.servlet.i18n.LocaleChangeInterceptor#preHandle(javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse, java.lang.Object)
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws ServletException {
        String newLocale = request.getParameter(getParamName());
        if (newLocale != null) {
            super.preHandle(request, response, handler);
            Locale locale = RequestContextUtils.getLocale(request);
            request.getSession(false).setAttribute(CapWebUtil.localeKey, locale);
        }
        return true;
    }

}
