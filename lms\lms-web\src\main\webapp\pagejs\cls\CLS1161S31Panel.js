var panelAction = {
    handler: 'cls1161m03formhandler',
    gridhandler: 'cls1161gridhandler',
    xlsFrm : 'xlsFrm',
    grid: null,
    init: function(){
    },
    build: function(){
        //匯入EXCEL
        $('#btExcel').click(function(){
            panelAction.openTbox();
        });
                
        $("#btPackNo").click(function(){
        	var packNo = $("#tabForm #packNo").val()||'';
        	
        	chkPackNo( packNo ).done(function(){
        		$.ajax({
    	            handler: _handler,
    	            action: 'getNumber',
    	            data: {
    	            	
    	            },
    	            success: function(json){
    	                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
    	                $("#tabForm").injectData(json);
    	                //更新opener
    	                CommonAPI.triggerOpener("gridview", "reloadGrid");    	              
    	            }
    	        });
    		});
        });
        	
        //build button
        $("#"+panelAction.xlsFrm).find('#downloadExcel').click(function(){
            //下載EXCEL
            $.capFileDownload({
                data: {
                    fileOid: $('#excelId').val()
                }
            });
        }).end().find('#btUploadExcel').click(function(){
            //上傳EXCEL
            MegaApi.uploadDialog({
                fieldId: "fileName",
                width: 320,
                height: 100,
                fileCheck: ['.xls'],
                data: {
                    deleteDup: true,
                    uid: responseJSON.oid, //避免此檔案被列為此文件之附加檔案
                    mainId: responseJSON.mainId
                },
                success: function(response){
                	 $.ajax({
                         handler: panelAction.handler,
                         action: 'delC160S03A',            
                         data: {}, 
                         success: function(json_dc_detail){
                        	 //del 完再 insert
                        	 var $form = $("#"+panelAction.xlsFrm);                                
                             $form.find('#progress').html('初始化中，請稍後...'); //finish 100%
                             $form.find('#progressTr').show();
                             panelAction.callSrvImpXls( {'excelId':response.fileKey } );     	
                         }
                     });
                }
            });
        });
    },
    
    openTbox: function(){
        $('#progressTr').hide();
        $('#downloadExcel').hide();
        if ($('#excelId').val()){ 
            $('#downloadExcel').show();
        }
        //=================
        var btnObj = {};

        if( $("#btUploadExcel").length>0 ){
			btnObj['saveData'] = function() {
				if ($("#"+panelAction.xlsFrm).valid()) {
					//之前的程式，在 xlsFrm 還有其它欄位, 需要輸入
                    //MegaApi.confirmMessage(i18n.def["actoin_001"], function(action){
                    //    if (action) {
                            panelAction.openTbox_Sure({});
                    //    }
                    //});
                }	
            };
		}
		btnObj['close'] = function() {
		   	$.thickbox.close();
		};
		//=================
		if(!$("#buttonPanel").find("#btnSend").is("button")) {
        	$("#btUploadExcel").addClass(" ui-state-disabled ").attr("disabled", "true");
        }else{
        	
        }
        $('#divXlsBox').thickbox({
            title: i18n.cls1161m03['button.btExcel'],
            width: 600,
            height: 250,
            align: 'center',
            valign: 'bottom',
            buttons: btnObj
        });
    },
    callSrvImpXls: function(passdata){
    	panelAction.callSrvImpXls_batch(passdata);
    },
    callSrvImpXls_direct: function(passdata){
        var $form = $("#"+panelAction.xlsFrm);
        var progress = 10;
        delayProgress(progress);
        var my_timeout = 1000 * 60 * 120;//120分鐘
        if(true){//先延長 timer，不然在處理過程中，會 timeout
			timer_long_ajax_beg(my_timeout);	
		}
        
        $.ajax({
            handler: panelAction.handler,
            action: 'srvImpXlsData',            
            data: $.extend(passdata||{}, {}),
            timeout: my_timeout, 
            success: function(response){
            	if(true){//恢復 timer
            		timer_long_ajax_end();
				}
            	
                if (response.impXlsDataResultForm) {
                    $form.setValue(response.impXlsDataResultForm);
                    
                    if($form.find('#excelId').val().length>0){
                    	$form.find('#downloadExcel').show();	
                    }                    
                }
                progress = 100;
                //finish progress
                $form.find('#progress').html(progress);
                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
                $form.find('#progressTr').hide().end().find("#btUploadExcel").hide();
                //更新opener
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            },
            error: function(){
                MegaApi.showPopMessage(i18n.def["confirmTitle"], "上傳錯誤請聯絡資訊處!");
                $.thickbox.close();
            }
        });
    },
    callSrvImpXls_batch: function(passdata){
    	var $form = $("#"+panelAction.xlsFrm);
    	
    	var my_timeout = 7200000;//ms
		if(true){//先延長 timer，不然在處理過程中，會 timeout
			timer_long_ajax_beg(my_timeout);	
		}
		$.ajax({
            handler: _handler, action: "callBatch", timeout: my_timeout,
            data: $.extend({'act':'procC160M03A_xls'
            	, 'jq_timeout': (my_timeout/1000)}, passdata ),
            success: function(json_callBatch){
            	if(true){//恢復 timer
            		timer_long_ajax_end();
				}
            	
            	$form.find('#progress').html(100);
            	
            	if(json_callBatch.r.response==="SUCCESS"){
            		if(true){
            			$form.setValue( {'excelId':passdata.excelId} );
            			                        
                        if($form.find('#excelId').val().length>0){
                        	$form.find('#downloadExcel').show();	
                        }	
            		}            		     
                    
                    MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
                    $form.find('#progressTr').hide().end().find("#btUploadExcel").hide();
                    //更新opener
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
            	}else{
            		API.showErrorMessage(json_callBatch.r.response);
            	}
    		}
        });
    },
    
    /**
     * (確定)
     */
    openTbox_Sure: function(data){
        $.thickbox.close();
        MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
        panelAction.init();
    }
};
function delayProgress(progress){
    setTimeout(function(){
        if (progress < 100) {
            $('#progress').html(progress += 5);
            delayProgress(progress);
        }
    }, 800);
}

function chkPackNo(uiVal){
	var my_dfd = my_dfd || $.Deferred();
	if(uiVal==""){
		my_dfd.resolve();		
	}else{
		CommonAPI.confirmMessage("是否替換 "+i18n.cls1161m03['C160M03A.packNo']+" "+uiVal+"？", function(b){
            if (b) {
            	my_dfd.resolve();    	
            }else{
            	my_dfd.reject();
            }
        });
	}
	return my_dfd.promise();
}

pageJsInit(function() {
	$(function() {
		panelAction.build();
		panelAction.init();
	});
});
