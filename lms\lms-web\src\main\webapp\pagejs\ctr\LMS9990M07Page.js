/**
 * 個金約據書子明細-一般契約書js
 */
var initDfd = $.Deferred();
var initsM07 = {
    // handler name
    fhandle: "lms9990m06formhandler",
    // main method
    saveAction: "noTempSave",
	tempSaveAction : "tempSave",
    queryAction: (responseJSON.contractType == 'A') ? "queryL999m01aM07" : "queryL999m01aM07A",
	setM07 : function(json, page){		
		switch(page){
			case 2:
		        var $formTabs2 = $("#formTabs2");
				$formTabs2.setData(json.formTabs2, false);
				$formTabs2.find("#jsonDataA").html(json.formTabs2.jsonDataA);			
				break;
			case 3:
				var $formTabs3 = $("#formTabs3");
				$formTabs3.setData(json.formTabs3, false);
				$formTabs3.find("#jsonDataB").html(json.formTabs3.jsonDataB);			
				break;
			case 4:
				var $formTabs4 = $("#formTabs4");
				$formTabs4.setData(json.formTabs4, false);
				$formTabs4.find("#jsonDataC").html(json.formTabs4.jsonDataC);			
				break;
			case 5:
				var $formTabs5 = $("#formTabs5");
				$formTabs5.setData(json.formTabs5, false);
				$formTabs5.find("#jsonDataD").html(json.formTabs5.jsonDataD);			
				break;
			case 6:
				var $formTabs6 = $("#formTabs6");
				$formTabs6.setData(json.formTabs6, false);
				$formTabs6.find("#jsonDataE").html(json.formTabs6.jsonDataE);			
				break;
			case 7:
				var $formTabs7 = $("#formTabs7");
				$formTabs7.setData(json.formTabs7, false);
		        $formTabs7.find("#jsonDataF01").html(json.formTabs7.jsonDataF01);
		        $formTabs7.find("#jsonDataF02").html(json.formTabs7.jsonDataF02);			
				break;
			case 13:
				var $formTabs13 = $("#formTabs13");
				$formTabs13.setData(json.formTabs13, false);			
				break;
			case 15:
				var $formTabs15 = $("#formTabs15");
				$formTabs15.setData(json.formTabs15, false);
		        // 設定資料提供radio
		        $formTabs15.find("[name='_rS31Com']").each(function(i){
		            var $this = $(this);
		            if ($this.val() == json.formTabs15._rS31Com) {
		                $this.prop("checked", true);
		            }
		        });
		        
		        // 設定資料提供checkBox
		        for (o in json.formTabs15.dataUseItem) {
		            $formTabs15.find("#_" + json.formTabs15.dataUseItem[o]).prop("checked", true);
		        }			
				break;
			case 16:
				$("#formTabs16").setData(json.formTabs16, false);			
				break;
			case 17:
				$("#formTabs17").setData(json.formTabs17, false);			
				break;
			case 8:
				$("#formTabs8").setData(json.formTabs8, false);			
				break;
			default:
		}
	},
	setM07A : function(json, page){		
		switch(page){
			case 10:
				$("#formTabs10").setData(json.formTabs10, false);
				break;
			case 11:
				$("#formTabs11").setData(json.formTabs11, false);			
				break;
			case 9:
				$("#formTabs9").setData(json.formTabs9, false);			
				break;
			case 12:
				$("#formTabs12").setData(json.formTabs12, false);			
				break;
			case 18:
				$("#formTabs18").setData(json.formTabs18, false);			
				break;
			case 19:
				$("#formTabs19").setData(json.formTabs19, false);			
				break;
			case 20:
				$("#formTabs20").setData(json.formTabs20, false);			
				break;
			case 14:
				$("#formTabs14").setData(json.formTabs14, false);			
				break;
			case 15:
				var $formTabs15 = $("#formTabs15");
				$formTabs15.setData(json.formTabs15, false);
		        // 設定資料提供radio
		        $formTabs15.find("[name='_rS31Com']").each(function(i){
		            var $this = $(this);
		            if ($this.val() == json.formTabs15._rS31Com) {
		                $this.prop("checked", true);
		            }
		        });		        
		        // 設定資料提供checkBox
		        for (o in json.formTabs15.dataUseItem) {
		            $formTabs15.find("#_" + json.formTabs15.dataUseItem[o]).prop("checked", true);
		        }			
				break;
			case 16:
				$("#formTabs16").setData(json.formTabs16, false);
				break;
			case 17:
				$("#formTabs17").setData(json.formTabs17, false);
				break;				
			default:
		}
	},
	saveM07 : function(showMsg){
		if(responseJSON.contractType == 'A'){
			return{
				showMsg : showMsg,
	            mainId: responseJSON.mainId,
	            contractNo: $("#ActionMForm").find("#contractNo").val(),
	            itemContentA: $("#formTabs8").find("#itemContentA").val(),
	            itemContentC: $("#formTabs8").find("#itemContentC").val(),
	            formTabs2: JSON.stringify($("#formTabs2").serializeData()),
				formTabs3: JSON.stringify($("#formTabs3").serializeData()),
				formTabs4: JSON.stringify($("#formTabs4").serializeData()),
				formTabs5: JSON.stringify($("#formTabs5").serializeData()),
				formTabs6: JSON.stringify($("#formTabs6").serializeData()),
				formTabs7: JSON.stringify($("#formTabs7").serializeData()),
				formTabs13: JSON.stringify($("#formTabs13").serializeData()),
				formTabs15: JSON.stringify($("#formTabs15").serializeData()),
				formTabs16: JSON.stringify($("#formTabs16").serializeData()),
				formTabs17: JSON.stringify($("#formTabs17").serializeData()),
				formTabs8: JSON.stringify($("#formTabs8").serializeData()),
	            _rS31Com: $("#formTabs15").find("[name='_rS31Com']:checked").val()			
			}			
		}else{
			return{
				showMsg : showMsg,
	            mainId: responseJSON.mainId,
	            contractNo: $("#ActionMForm").find("#contractNo").val(),
	            formTabs10: JSON.stringify($("#formTabs10").serializeData()),
				formTabs11: JSON.stringify($("#formTabs11").serializeData()),
				formTabs9: JSON.stringify($("#formTabs9").serializeData()),
				formTabs12: JSON.stringify($("#formTabs12").serializeData()),
				formTabs18: JSON.stringify($("#formTabs18").serializeData()),
				formTabs19: JSON.stringify($("#formTabs19").serializeData()),
				formTabs20: JSON.stringify($("#formTabs20").serializeData()),
				formTabs14: JSON.stringify($("#formTabs14").serializeData()),
				formTabs15: JSON.stringify($("#formTabs15").serializeData()),
				formTabs16: JSON.stringify($("#formTabs16").serializeData()),
				formTabs17: JSON.stringify($("#formTabs17").serializeData()),
	            _rS31Com: $("#formTabs15").find("[name='_rS31Com']:checked").val()			
			}			
		}
	},	
	checkValid : function(){		
		var thisPage = parseInt(responseJSON.page,10);		
		if (responseJSON.contractType == 'A') {
			switch(thisPage){
				case 1:
					return true;
				case 2:
					return $("#formTabs2").valid();
				case 3:
					return $("#formTabs3").valid();				
				case 4:
					return $("#formTabs4").valid();				
				case 5:
					return $("#formTabs5").valid();				
				case 6:
					return $("#formTabs6").valid();				
				case 7:
					return $("#formTabs7").valid();				
				case 13:
					return $("#formTabs13").valid();				
				case 15:
					return $("#formTabs15").valid();				
				case 16:
					return $("#formTabs16").valid();				
				case 17:
					return $("#formTabs17").valid();				
				case 8:
					return $("#formTabs8").valid();			
			}
		}else{
			switch(thisPage){
				case 1:
					return true;
				case 10:
					return $("#formTabs10").valid();				
				case 11:
					return $("#formTabs11").valid();				
				case 9:
					return $("#formTabs9").valid();				
				case 12:
					return $("#formTabs12").valid();				
				case 18:
					return $("#formTabs18").valid();				
				case 19:
					return $("#formTabs19").valid();				
				case 20:
					return $("#formTabs20").valid();				
				case 14:
					return $("#formTabs14").valid();				
				case 15:
					return $("#formTabs15").valid();				
				case 16:
					return $("#formTabs16").valid();				
				case 17:
					return $("#formTabs17").valid();
			}			
		}		
	}	
};

//畫面切換table 所需設定之資料 如無設定 則直接切換
$.extend(window.tempSave, {
    handler: initsM07.fhandle,
    action: initsM07.tempSaveAction,
	beforeCheck: function(){
		// 這裡實作TempSave前的檢核
		return ($("#ActionMForm").valid() && initsM07.checkValid());
    },	
    sendData: function(){
		return initsM07.saveM07(false);
	}
});

$(function() {
	$("#mainOid").val(responseJSON.mainOid);
    // 檢查資料提供"上述所有公司"是否被點擊
    var $formTabs15 = $("#formTabs15");
    $formTabs15.find("#_cS31Com8").click(function(i){
        // 若被勾選則將其它checkBox取消勾選
        if ($(this).prop("checked")) {
            $formTabs15.find(".cDataUseItem").each(function(j){
                $(this).prop("checked", false);
            });
        }
    });
    /**
     * 查詢約據書個金外標籤資料
     */
    $.form.init({
        formHandler: initsM07.fhandle,
        formPostData: {//把form上貼上資料
            formAction: initsM07.queryAction,
            mainId: responseJSON.mainId
        },
        loadSuccess: function(json){
			(responseJSON.contractType == 'A') ? initsM07.setM07(json,parseInt(json.page,10)) : initsM07.setM07A(json,parseInt(json.page,10));
            initDfd.resolve(json);
        }
    });
    
    var btn = $("#buttonPanel");
    /**
     * 儲存約據書個金外標籤資料
     */
    btn.find("#btnSave").click(function(showMsg){
        saveM07(showMsg, false);
    }).end().find("#btnPrint").click(function(showMsg){
        if (checkReadOnly()) {
            runPrint();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    saveM07(showMsg, true);
                }
            });
        }
    });
});

/**
 * 執行儲存
 * needPrint : 儲存後是否需要立即列印
 */
function saveM07(showMsg, needPrint){
	if(responseJSON.page == "15"){
	    // 檢查資料提供"上述所有公司"是否被勾選
	    // 若被勾選則將其它checkBox取消勾選
	    var $formTabs15 = $("#formTabs15");
	    if ($formTabs15.find("#_cS31Com8").prop("checked")) {
	        $formTabs15.find(".cDataUseItem").each(function(j){
	            $(this).prop("checked", false);
	        });
	    }		
	}
    var $actionMForm = $("#ActionMForm");
    if ($actionMForm.valid() && initsM07.checkValid()) {
        $.ajax({
            type: "POST",
            handler: initsM07.fhandle,
            action: initsM07.saveAction,
			data : initsM07.saveM07(true),
/*
            data: {
                mainId: responseJSON.mainId,
                contractNo: $actionMForm.find("#contractNo").val(),
                itemContentA: $("#formTabs8").find("#itemContentA").val(),
                itemContentC: $("#formTabs8").find("#itemContentC").val(),
                ActionSForm: JSON.stringify($("#ActionSForm").serializeData()),
                _rS31Com: $("#formTabs15").find("[name='_rS31Com']:checked").val()
            },
*/
        }).done(function(responseData){
			// alert(JSON.stringify(responseData));
			 $.thickbox.close();
			 $.thickbox.close();
			 if (needPrint) {
			     runPrint();
			 }else{
			 	CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
			}			
		});
    }
}

/**
 * 執行列印
 */
function runPrint(){
	$.capFileDownload({					     
		handler:"lmsdownloadformhandler",
        data: {
            mainId: responseJSON.mainId,
            contractType: responseJSON.contractType,//W31
            chkColIdVal : (responseJSON.contractType == "B")? "W31" : "",
            fileDownloadName: "LMS9990W01C.doc",
            serviceName: "lms9990doc02service"
        }
    });
}

/**
 * 檢查唯讀狀態
 */
function checkReadOnly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); //權限
    if (auth.readOnly) {
        return true;
    }
    return false;
}
