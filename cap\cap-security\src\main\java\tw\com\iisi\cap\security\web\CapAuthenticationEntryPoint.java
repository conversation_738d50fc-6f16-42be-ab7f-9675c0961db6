/*
 * CapAuthenticationEntryPoint.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.security.web;

import javax.naming.AuthenticationException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;

/**
 * <p>
 * 當sessin過期時的動作， 若Ajax Request時需記錄為AjaxRequest，導致loginFormUrl以便判別 若為一般頁面之Request時，需導到loginFormUrl
 * </p>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/11/2,iristu,new
 *          </ul>
 */
public class CapAuthenticationEntryPoint extends LoginUrlAuthenticationEntryPoint {

    /**
     * @param loginFormUrl
     */
    public CapAuthenticationEntryPoint(String loginFormUrl) {
        super(loginFormUrl);
    }

    /*
     * 若為Ajax Request時記錄為AjaxRequest，以便判別<br> 若為一般頁面之Request時，需導到loginFormUrl
     * 
     * @see org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint#determineUrlToUseForThisRequest(javax.servlet.http.HttpServletRequest,
     * javax.servlet.http.HttpServletResponse, org.springframework.security.core.AuthenticationException)
     */
    protected String determineUrlToUseForThisRequest(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String xReq = httpRequest.getHeader("x-requested-with");
        if ("XMLHttpRequest".equalsIgnoreCase(xReq)) {
            return new StringBuffer(getLoginFormUrl()).append("?ajax").toString();
        } else {
            return getLoginFormUrl();
        }
    }

}
