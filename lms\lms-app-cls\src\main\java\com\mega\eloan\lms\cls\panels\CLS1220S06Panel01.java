/* 
 * CLS1220S06Panel01
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**<pre>
 * 進件管理 結案、徵信視窗
 * </pre>
 * @since  2012/7/25
 * <AUTHOR>
 * @version <ul>
 *           <li>new
 *          </ul>
 */
@SuppressWarnings("serial")
public class CLS1220S06Panel01 extends Panel {

	public CLS1220S06Panel01(String id) {
		super(id);
	}

	/**
	 * @param id
	 * @param updatePanelName
	 */
	public CLS1220S06Panel01(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

}
