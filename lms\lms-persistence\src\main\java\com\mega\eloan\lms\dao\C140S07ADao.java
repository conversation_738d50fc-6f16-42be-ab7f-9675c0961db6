/* 
 * C140S07ADao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C140M07A;
import com.mega.eloan.lms.model.C140S07A;

/**
 * <pre>
 * 徵信調查報告書第柒章 財務分析(子)
 * </pre>
 * 
 * @since 2011/10/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/05,TimChiang, new
 *          </ul>
 */
public interface C140S07ADao extends IGenericDao<C140S07A> {

	/**
	 * 刪除所有資料
	 * 
	 * @param meta
	 *            C140M07A
	 * @return Integer
	 */
	int deleteByMeta(C140M07A meta);

	List<C140S07A> findByMainIdAndTab(String mainId, String tab, String subtab);

	List<C140S07A> findBySubDocAndTab(String mainId, String pid, String tab,
			String subtab);
}
