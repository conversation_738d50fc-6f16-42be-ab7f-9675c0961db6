/* 
 * C900M01EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01E;

/** 疑似利用偽造證件或財力證明用以申辦信用卡或貸款警示訊息檔 **/
public interface C900M01EDao extends IGenericDao<C900M01E> {

	C900M01E findByOid(String oid);
	
	C900M01E findByMainId(String mainId);
	
	C900M01E findActiveByCustId(String custId);

}