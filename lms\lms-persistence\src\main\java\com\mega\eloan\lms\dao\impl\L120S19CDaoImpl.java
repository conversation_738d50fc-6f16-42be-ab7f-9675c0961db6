/* 
 * L120S19CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L120S19CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S19C;

/** 無紙化簽報貸款費用檔 **/
@Repository
public class L120S19CDaoImpl extends LMSJpaDao<L120S19C, String>
	implements L120S19CDao {

	@Override
	public L120S19C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S19C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S19C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L120S19C> findByMainIdFeeNoRole(String mainId, String feeNo, String role) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "feeNo", feeNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "role", role);
		List<L120S19C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L120S19C> findByMainIdRole(String mainId, String role) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "role", role);
		search.addOrderBy("feeNo");
		List<L120S19C> list = createQuery(search).getResultList();
		return list;
	}
}