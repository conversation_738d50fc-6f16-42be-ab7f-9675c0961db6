package com.mega.eloan.lms.cls.service.impl;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.cls.service.CLS3501Service;
import com.mega.eloan.lms.dao.C124M01ADao;
import com.mega.eloan.lms.dao.C124M01BDao;
import com.mega.eloan.lms.dao.C125M01ADao;
import com.mega.eloan.lms.model.C124M01A;
import com.mega.eloan.lms.model.C124M01B;
import com.mega.eloan.lms.model.C125M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;
import org.springframework.stereotype.Service;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class CLS3501ServiceImpl extends AbstractCapService implements
		CLS3501Service {

	@Resource
	FlowService flowService;

	@Resource
	TempDataService tempDataService;

	@Resource
	DocLogService docLogService;

	@Resource
	C124M01ADao c124m01aDao;

	@Resource
	C124M01BDao c124m01bDao;

	@Resource
	C125M01ADao c125m01aDao;

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C124M01A) {
					if (Util.isEmpty(((C124M01A) model).getOid())) {
						((C124M01A) model).setCreator(user.getUserId());
						((C124M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
						c124m01aDao.save((C124M01A) model);

						flowService.start("CLS3501Flow",
								((C124M01A) model).getOid(), user.getUserId(),
								user.getUnitNo());
					} else {
						if (((C124M01A) model).getDocStatus().equals(
								CreditDocStatusEnum.海外_編製中.getCode())
								|| ((C124M01A) model).getDocStatus().equals(
								CreditDocStatusEnum.海外_待補件.getCode())) {
							// 當文件狀態為編製中時文件亂碼才變更
							((C124M01A) model).setUpdater(user.getUserId());
							((C124M01A) model).setUpdateTime(CapDate
									.getCurrentTimestamp());
							if (!"Y".equals(SimpleContextHolder
									.get(EloanConstants.TEMPSAVE_RUN))) {
								tempDataService.deleteByMainId(((C124M01A) model)
										.getMainId());
								docLogService.record(((C124M01A) model).getOid(),
										DocLogEnum.SAVE);
							}
						}
						c124m01aDao.save((C124M01A) model);

					}
				} else if (model instanceof C124M01B) {
					((C124M01B) model).setUpdater(user.getUserId());
					((C124M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					c124m01bDao.save((C124M01B) model);
				} else if (model instanceof C125M01A) {
					((C125M01A) model).setUpdater(user.getUserId());
					((C125M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					c125m01aDao.save((C125M01A) model);
				}
			}
		}
	}

	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C124M01A.class) {
			return c124m01aDao.findPage(search);
		} else if(clazz == C124M01B.class){
			return c124m01bDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C124M01A.class) {
			return (T) c124m01aDao.findByOid(oid);
		} else if(clazz == C124M01B.class){
			return (T) c124m01bDao.findByOid(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
														String mainId) {
		if (clazz == C124M01A.class) {
			return c124m01aDao.findByIndex01(mainId);
		} else if (clazz == C124M01B.class) {
			return c124m01bDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub
	}

	@Override
	public List<C124M01A> findC124m01aByCustIdAndDupNo(
			String custId, String dupNo, Integer verNo){
		List<C124M01A> list = new ArrayList<C124M01A>();
		if(Util.isEmpty(dupNo)){
			dupNo = "0";
		}
		if(Util.isNotEmpty(custId)){
			list = c124m01aDao.findByCustIdAndDupNo(custId, dupNo, verNo);
		}
		return list;
	}

	@Override
	public boolean deleteC124M01As(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<C124M01A> c124m01as = new ArrayList<C124M01A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			C124M01A c124m01a = (C124M01A) findModelByOid(C124M01A.class, oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			c124m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			c124m01a.setUpdater(user.getUserId());
			c124m01as.add(c124m01a);
			docLogService.record(c124m01a.getOid(), DocLogEnum.DELETE);
		}
		if (!c124m01as.isEmpty()) {
			c124m01aDao.save(c124m01as);
			flag = true;
		}
		return flag;
	}

	@Override
	public void deleteC124m01bs(List<C124M01B> c124m01bs, boolean isAll) {
		if (isAll) {
			c124m01bDao.delete(c124m01bs);
		} else {
			List<C124M01B> C124M01BsOld = new ArrayList<C124M01B>();
			for (C124M01B C124m01b : c124m01bs) {
				String staffJob = C124m01b.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					C124M01BsOld.add(C124m01b);
				}
			}
			c124m01bDao.delete(C124M01BsOld);
		}
	}

	@Override
	public void saveC124m01bList(List<C124M01B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (C124M01B c124m01b : list) {
			c124m01b.setUpdater(user.getUserId());
			c124m01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		c124m01bDao.save(list);
	}

	@Override
	public C124M01B findC124m01b(String mainId, String branchType,
								 String branchId, String staffNo, String staffJob) {
		return c124m01bDao.findByUniqueKey(mainId, branchType, branchId,
				staffNo, staffJob);
	}

	@Override
	public void flowAction(String mainOid, C124M01A model, boolean setResult,
						   boolean resultType, boolean upMis) throws Throwable {
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("CLS3501Flow",
						((C124M01A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// resultType 控制前進還是後退
				// 當有先行動用的狀態 是到03O 非先行動用表示已完成 到05O
				inst.setAttribute("result", resultType ? "核准" : "退回");
				if (resultType) {
					if (upMis) {
						C124M01A c124m01a = (C124M01A) findModelByOid(
								C124M01A.class, mainOid);
						// 動審表簽章欄檔取得人員職稱
						List<C124M01B> c124m01blist = c124m01bDao
								.findByMainId(c124m01a.getMainId());
						String apprId = "";
						String reCheckId = "";

						for (C124M01B c124m01b : c124m01blist) {
							String StaffJob = Util.trim(c124m01b.getStaffJob());// 取得人員職稱
							String StaffNo = Util.trim(c124m01b.getStaffNo());// 取得行員代碼
							if (Util.equals(StaffJob, "L1")) {// 分行經辦
								apprId = StaffNo;
							} else if (Util.equals(StaffJob, "L4")) {// 分行覆核主管
								reCheckId = StaffNo;
							}
						}
						// 若人員職稱為空值改取m01a上的人員資料
						if (Util.isEmpty(apprId)) {
							apprId = c124m01a.getUpdater();
						}
						if (Util.isEmpty(reCheckId)) {
							reCheckId = c124m01a.getApprover();
						}
					}
				}
			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	@Override
	public void saveC124m01a(C124M01A meta) {
		c124m01aDao.save(meta);
	}
}
