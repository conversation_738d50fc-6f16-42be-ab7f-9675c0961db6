<html xmlns="http://www.w3.org/1999/xhtml"
xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
		
		<div id='div_applyKindB_History' style='display:none;' >
			<form id='div_applyKindB_History_form'>
				<table class='tb2' width='95%' >
					<tr>
				  		<td class='hd2' width='20%' nowrap><span id="div_applyKindB_History_label_custId"></span> </td>				 
				  		<td width='80%'>
				  		<input type='text' id='search_custId' name='search_custId' maxlength='10' size='10' class="alphanum"/>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<button type="button" id='filter_historyBtn'>
								<span class='text-only'><th:block th:text="#{'button.search'}"></th:block></span>
							</button>
				  		</td>						
					</tr>
					
				</table>
			</form>
			<div id='grid_applyKindB_History'></div>
		</div>
	</th:block>
</body>
</html>
