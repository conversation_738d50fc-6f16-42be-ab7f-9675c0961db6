$(function(){
    // 預設值
    $('#returnBefore2415').click(function(){
        $.ajax({
            handler: "lms2415m01formhandler",
            type: "POST",
            dataType: "json",
            data: {
                formAction: "returnC241s03Val"
            }
            }).done(function(obj){
                // alert(obj.ChkResult5);
                $("# L241M01cForm").injectData(obj);
                var array = obj.L170M01DArray;
                var rptId = obj.rptId;	//rptId == "" 舊版
                var count2 = 0;
                for (var i = 0; i < array.length; i++) {
                    count2 = count2 + 1;
                    var json = array[i];
                    var uuu = json.chkResult;
                    var itemCount = json.itemNo;
                    $("input[type='radio'][name='chkResult" + ((rptId == "") ? count2 : itemCount) + "'][value=" + uuu + "]").prop("checked", true);
                }
        });
    })
})
