pageJsInit(function() {
	$(function() {
		var setOption = [{
			colHeader: i18n.lms2105m01['L210M01A.modifyType'],//異動類別,
			name: 'rptId',
			width: 90,
			align: "left",
			sortable: true
		}, {
			colHeader: i18n.lms2105m01['L210M01A.modifyDate'],//異動日期,
			name: 'modifyDate',
			width: 45,
			sortable: true
		}, {
			colHeader: i18n.lms2105m01['L210M01A.caseDate'],//簽案日期,
			name: 'caseDate',
			width: 45,
			sortable: true
		}, {
			colHeader: i18n.lms2105m01['L210M01A.mainCustId'],//主要借款人統編,
			name: 'custId',
			width: 70,
			sortable: true,
			formatter: 'click',
			onclick: openDoc
		}, {
			colHeader: i18n.lms2105m01['L210M01A.mainCust'],//主要借款人,
			name: 'custName',
			width: 80,
			sortable: true,
			align: "center"
		}, {
			colHeader: i18n.lms2105m01['L210M01A.caseNo'],//案號,
			name: 'caseNo',
			width: 140,
			sortable: true,
			align: "left"
		}, {
			colHeader: i18n.lms2105m01['L210M01A.creatorPerson'],//經辦,
			name: 'apprId',
			width: 60,
			sortable: true,
			align: "center"
		}, {
			name: 'oid',
			hidden: true
		}, {
			name: 'mainId',
			hidden: true
		}, {
			name: 'docURL',
			hidden: true
		}];
		if (viewstatus.search("05O") >= 0) {
			setOption.push({
				colHeader: i18n.lms2105m01['L210M01A.docStatus'],//狀態,
				name: 'docStatus',
				width: 30,
				align: "left",
				sortable: true
			});
		}

		var grid = $("#gridview").iGrid({
			handler: 'lms2105gridhandler',
			height: 350,
			width: 785,
			autowidth: false,
			sortname: 'createTime',
			sortorder: 'desc',
			postData: {
				formAction: "queryL210m01a",
				docStatus: viewstatus
			},
			rowNum: 15,
			colModel: setOption,
			ondblClickRow: function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
				var data = $("#gridview").getRowData(rowid);
				openDoc(null, null, data);
			}
		});

		function openDoc(cellvalue, options, rowObject) {
			$.form.submit({
				url: '..' + rowObject.docURL + '/01',//'../lms/lms2105m01/01'
				data: {
					//formAction : "queryL210m01a",
					oid: rowObject.oid,
					mainId: rowObject.mainId,
					mainOid: rowObject.oid,
					mainDocStatus: viewstatus,
					txCode: txCode
				},
				target: rowObject.oid
			});
		}

		$("#buttonPanel").find("#btnModify").click(function() {
			var id = $("#gridview").getGridParam('selrow');
			if (!id) {

				// action_004=請先選擇需「調閱」之資料列
				return CommonAPI.showMessage(i18n.def["action_004"]);
			}

			var result = $("#gridview").getRowData(id);
			openDoc(null, null, result);
		}).end().find("#btnView").click(function() {
			var id = $("#gridview").getGridParam('selrow');
			if (!id) {
				// action_004=請先選擇需「調閱」之資料列
				return CommonAPI.showMessage(i18n.def["action_004"]);
			}
			var result = $("#gridview").getRowData(id);
			openDoc(null, null, result);

		}).end().find("#btnDelete").click(function() {
			var $grid = grid;
			//單筆
			var rowData = $grid.getSingleData();
			if (rowData) {
				//confirmDelete=是否確定刪除?
				API.confirmMessage(i18n.def["confirmDelete"], function(b) {
					if (b) {
						$.ajax({
							handler: "lms2105m01formhandler",
							formId: "empty",
							action: "deleteL210m01a",
							data: {
								oid: rowData.oid
							},
							success: function(obj) {
								$grid.reload();
							}
						});
					}
				});
			}
		}).end().find("#btnUpTransferId").click(function() {

			$("#uploadFileAml").val('');

			var fileSize = 5 * 1024 * 1024;
			var s = $.extend({
				handler: 'lms2105v01fileuploadhandler',
				fieldId: "uploadFileAml",
				title: i18n && i18n.def.insertfile || "請選擇附加檔案",
				fileCheck: ['xls'],
				successMsg: false,
				success: function() {
				},
				data: {
					fileSize: fileSize,
					mainId: "",
					deleteDup: true,
					changeUploadName: "AMLExcelList_01.xls"
				}
			}, s);


			$("#loginImportByExl").thickbox({ // 使用選取的內容進行彈窗
				title: "匯入EXCEL名單",
				width: 500,
				height: 200,
				modal: true,
				i18n: i18n.def,
				buttons: (function() {
					var b = {};
					b['上傳'] = function() {

						$.capFileUpload({
							handler: s.handler,
							fileCheck: s.fileCheck,
							fileElementId: s.fieldId,
							successMsg: s.successMsg,
							data: $.extend({
								fieldId: "uploadFileAml"
							}, s.data || {}),
							success: function(json) {
								$.thickbox.close();
								API.showPopMessage("上傳成功");
							}
						});
					};
					b[i18n && i18n.def.cancel || "取消"] = function() {
						$.thickbox.close();
					};
					return b;
				})()
			});
		}).end().find("#btnUpCapital").click(function() {

			$("#uploadCapital").val('');

			var fileSize = 10 * 1024 * 1024;
			var s = $.extend({
				handler: 'lms2105v01fileuploadhandler',
				fieldId: "uploadCapital",
				title: i18n && i18n.def.insertfile || "請選擇附加檔案",
				fileCheck: ['xls'],
				successMsg: false,
				success: function() {
				},
				data: {
					fileSize: fileSize,
					mainId: "",
					deleteDup: true,
					changeUploadName: "CaptialList_01.xls"
				}
			}, s);


			$("#importByExl_capital").thickbox({ // 使用選取的內容進行彈窗
				title: "匯入EXCEL名單",
				width: 500,
				height: 200,
				modal: true,
				i18n: i18n.def,
				buttons: (function() {
					var b = {};
					b['上傳'] = function() {

						$.capFileUpload({
							handler: s.handler,
							fileCheck: s.fileCheck,
							fileElementId: s.fieldId,
							successMsg: s.successMsg,
							data: $.extend({
								fieldId: "uploadCapital"
							}, s.data || {}),
							success: function(json) {
								$.thickbox.close();
								API.showPopMessage("上傳成功");
							}
						});
					};
					b[i18n && i18n.def.cancel || "取消"] = function() {
						$.thickbox.close();
					};
					return b;
				})()
			});
		}).end().find("#btnUpSole_FOR_Y01").click(function() {
			$("#uploadSole").val('');

			var fileSize = 10 * 1024 * 1024;
			var s = $.extend({
				handler: 'lms2105v01fileuploadhandler',
				fieldId: "uploadSole",
				title: i18n && i18n.def.insertfile || "請選擇附加檔案",
				fileCheck: ['xls'],
				successMsg: false,
				success: function() {
				},
				data: {
					fileSize: fileSize,
					mainId: "",
					deleteDup: true,
					changeUploadName: "SoleList.xls"
				}
			}, s);


			$("#importByExl_sole").thickbox({ // 使用選取的內容進行彈窗
				title: "匯入EXCEL名單",
				width: 500,
				height: 200,
				modal: true,
				i18n: i18n.def,
				buttons: (function() {
					var b = {};
					b['上傳'] = function() {
						$.capFileUpload({
							handler: 'Simplefileuploadhandler',//s.handler,
							fileCheck: s.fileCheck,
							fileElementId: "uploadSole",//s.fieldId,
							successMsg: s.successMsg,
							data: $.extend({
								fieldId: "uploadSole"
							}, s.data || {}),
							success: function(json) {
								ilog.debug("fileKey==" + json.fileKey);

								$.form.submit({
									url: __ajaxHandler,
									target: "_blank",
									data: {
										_pa: 'lmsdownloadformhandler',
										fileKey: json.fileKey,
										'mode': 'uploadSoleX',
										'fileDownloadName': 'SoleList.xls',
										'serviceName': 'lms7600r01rptservice'
									}
								});
							}
						});
					};
					b[i18n && i18n.def.cancel || "取消"] = function() {
						$.thickbox.close();
					};
					return b;
				})()
			});
		}).end().find("#btnUpSole").click(function() {
			$("#uploadSole").val('');

			var fileSize = 10 * 1024 * 1024;
			var s = $.extend({
				handler: 'lms2105v01fileuploadhandler',
				fieldId: "uploadSole",
				title: i18n && i18n.def.insertfile || "請選擇附加檔案",
				fileCheck: ['xls'],
				successMsg: false,
				success: function() {
				},
				data: {
					fileSize: fileSize,
					mainId: "",
					deleteDup: true,
					changeUploadName: "SoleList.xls"
				}
			}, s);


			$("#importByExl_sole").thickbox({ // 使用選取的內容進行彈窗
				title: "匯入EXCEL名單",
				width: 500,
				height: 200,
				modal: true,
				i18n: i18n.def,
				buttons: (function() {
					var b = {};
					b['上傳'] = function() {
						$.capFileUpload({
							handler: 'Simplefileuploadhandler',//s.handler,
							fileCheck: s.fileCheck,
							fileElementId: "uploadSole",//s.fieldId,
							successMsg: s.successMsg,
							data: $.extend({
								fieldId: "uploadSole"
							}, s.data || {}),
							success: function(json) {
								ilog.debug("fileKey==" + json.fileKey);

								$.form.submit({
									url: __ajaxHandler,
									target: "_blank",
									data: {
										_pa: 'lmsdownloadformhandler',
										fileKey: json.fileKey,
										'mode': 'uploadSole',
										'fileDownloadName': 'SoleList.xls',
										'serviceName': 'lms7600r01rptservice'
									}
								});
							}
						});
					};
					b[i18n && i18n.def.cancel || "取消"] = function() {
						$.thickbox.close();
					};
					return b;
				})()
			});
		}).end().find("#btnGetLatestCol").click(function() {
			$("#getLatestCol").val('');
			var tDate = new Date();
			$("#bgnDT").val(tDate.getFullYear() + "-01-01");
			tDate.setDate(1); // going to 1st of the month
			tDate.setHours(-1); // going to last hour before this date even started.
			$("#endDT").val(tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate());
			var fileSize = 10 * 1024 * 1024;
			var s = $.extend({
				handler: 'Simplefileuploadhandler',
				fieldId: "getLatestCol",
				title: i18n && i18n.def.insertfile || "請選擇附加檔案",
				fileCheck: ['xls'],
				successMsg: false,
				success: function() {
				},
				data: {
					fileSize: fileSize,
					mainId: "",
					deleteDup: true,
					changeUploadName: "LatestColList.xls"
				}
			}, s);

			$("#importByExl_latestCol").thickbox({ // 使用選取的內容進行彈窗
				title: "匯入EXCEL名單",
				width: 500,
				height: 200,
				modal: true,
				i18n: i18n.def,
				buttons: (function() {
					var b = {};
					b['上傳'] = function() {
						var bgnDT = $('#bgnDT').val();
						var endDT = $('#endDT').val();

						var regEx = /^\d{4}-\d{2}-\d{2}$/;

						if (bgnDT == null || bgnDT == "") {
							return CommonAPI.showErrorMessage("請輸入日期");
						}
						if (bgnDT && !bgnDT.match(regEx)) {
							return CommonAPI.showErrorMessage("日期格式錯誤(yyyy-MM-dd)");
						}
						if (endDT == null || endDT == "") {
							return CommonAPI.showErrorMessage("請輸入日期");
						}
						if (endDT && !endDT.match(regEx)) {
							return CommonAPI.showErrorMessage("日期格式錯誤(yyyy-MM-dd)");
						}

						$.capFileUpload({
							handler: s.handler,
							fileCheck: s.fileCheck,
							fileElementId: s.fieldId,
							successMsg: s.successMsg,
							data: $.extend({
								fieldId: "getLatestCol"
							}, s.data || {}),
							success: function(json) {
								ilog.debug("fileKey==" + json.fileKey + "===bgnDT===" + bgnDT + "===endDT===" + endDT);

								$.form.submit({
									url: __ajaxHandler,
									target: "_blank",
									data: {
										_pa: 'lmsdownloadformhandler',
										fileKey: json.fileKey,
										bgnDT: bgnDT,
										endDT: endDT,
										'mode': 'getLatestCol',
										'fileDownloadName': 'LatestColList.xls',
										'serviceName': 'lms7600r01rptservice'
									}
								});
							}
						});
					};
					b[i18n && i18n.def.cancel || "取消"] = function() {
						$.thickbox.close();
					};
					return b;
				})()
			});
		}).end().find("#btnUpTripleCoupon").click(function() {
			//J-109-0519_05097_B1001 Web e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單
			$("#uploadTripleCoupon").val('');

			var fileSize = 10 * 1024 * 1024;
			var s = $.extend({
				handler: 'lms2105v01fileuploadhandler',
				fieldId: "uploadTripleCoupon",
				title: i18n && i18n.def.insertfile || "請選擇附加檔案",
				fileCheck: ['xls'],
				successMsg: false,
				success: function() {
				},
				data: {
					fileSize: fileSize,
					mainId: "",
					deleteDup: true,
					changeUploadName: "TripleCouponList_01.xls"
				}
			}, s);


			$("#importByExl_tripleCoupon").thickbox({ // 使用選取的內容進行彈窗
				title: "匯入EXCEL名單",
				width: 500,
				height: 200,
				modal: true,
				i18n: i18n.def,
				buttons: (function() {
					var b = {};
					b['上傳'] = function() {

						$.capFileUpload({
							handler: s.handler,
							fileCheck: s.fileCheck,
							fileElementId: s.fieldId,
							successMsg: s.successMsg,
							data: $.extend({
								fieldId: "uploadTripleCoupon"
							}, s.data || {}),
							success: function(json) {
								$.thickbox.close();
								API.showPopMessage("上傳成功");
							}
						});
					};
					b[i18n && i18n.def.cancel || "取消"] = function() {
						$.thickbox.close();
					};
					return b;
				})()
			});
		}).end().find("#btnUpRetrialL224File").click(function() {
			//J-110-0304_05097_B1003 Web e-Loan授信覆審配合RPA作業修改
			$("#uploadRetrialL224File").val('');
			var limitFileSize = 3145728 * 500;   //3M * 100 = 300M
			var s = $.extend({
				handler: 'lms2105v01fileuploadhandler',
				fieldId: "uploadRetrialL224File",
				title: i18n && i18n.def.insertfile || "請選擇附加檔案",
				fileCheck: ['xlsx'],
				limitSize: limitFileSize,
				successMsg: false,
				success: function() {
				},
				data: {
					fileSize: limitFileSize,
					mainId: "",
					deleteDup: true,
					changeUploadName: "uploadRetrialL224File.xlsx"
				}
			}, s);

			//FOR TEST
			//        $("#retrialL224File").find("#custId").val("04211365");
			//        $("#retrialL224File").find("#dupNo").val("0");
			//        $("#retrialL224File").find("#brNo").val("010");
			//        $("#retrialL224File").find("#areaNo").val("931");
			//        $("#retrialL224File").find("#custType").val("1");
			//        $("#retrialL224File").find("#retrialDate").val("2021-06-17");
			//        $("#retrialL224File").find("#rpaKey").val("8ed29502_A");
			//        $("#retrialL224File").find("#rpaUserId").val("007623");
			//        $("#retrialL224File").find("#isLast").val("Y");
			//        $("#retrialL224File").find("#chkCheck").val("Y");
			//        $("#retrialL224File").find("#chkResult").val("Y");
			//        $("#retrialL224File").find("#responseCode").val("0");
			//      $("#retrialL224File").find("#data_searchResult").val("0");
			//        $("#retrialL224File").find("#responseMsg").val("查詢成功");


			$("#importByExl_retrialL224File").thickbox({ // 使用選取的內容進行彈窗
				title: "匯入覆審L224附加檔案",
				width: 500,
				height: 500,
				modal: true,
				i18n: i18n.def,
				buttons: (function() {
					var b = {};
					b['上傳'] = function() {

						$.capFileUpload({
							handler: s.handler,
							fileCheck: s.fileCheck,
							fileElementId: s.fieldId,
							successMsg: s.successMsg,
							limitSize: limitFileSize,
							data: $.extend({
								fieldId: "uploadRetrialL224File",
								custId: $("#retrialL224File").find("#custId").val(),
								dupNo: $("#retrialL224File").find("#dupNo").val(),
								brNo: $("#retrialL224File").find("#brNo").val(),
								areaNo: $("#retrialL224File").find("#areaNo").val(),
								custType: $("#retrialL224File").find("#custType").val(),
								retrialDate: $("#retrialL224File").find("#retrialDate").val(),
								rpaKey: $("#retrialL224File").find("#rpaKey").val(),
								rpaUserId: $("#retrialL224File").find("#rpaUserId").val(),
								isLast: $("#retrialL224File").find("#isLast").val(),
								chkCheck: $("#retrialL224File").find("#chkCheck").val(),
								chkResult: $("#retrialL224File").find("#chkResult").val(),
								responseCode: $("#retrialL224File").find("#responseCode").val(),
								responseMsg: $("#retrialL224File").find("#responseMsg").val(),
								data_searchResult: $("#retrialL224File").find("#data_searchResult").val()
							}, s.data || {}),
							success: function(json) {
								$.thickbox.close();
								if (json.errorMsg != "") {
									CommonAPI.showErrorMessage("上傳失敗:" + json.errorMsg);
								} else {
									API.showPopMessage("上傳成功");
								}

								//產生批覆書檔案

							}
						});
					};
					b[i18n && i18n.def.cancel || "取消"] = function() {
						$.thickbox.close();
					};
					return b;
				})()
			});
		}).end().find("#btnUpEsgFile").click(function() {
			//J-111-0423_05097_B1001 Web e-Loan企金授信就海外分行承做永續績效連結授信案(如附件)，於E-Loan「永續績效連結授信」相關註記
			$("#uploadEsgFile").val('');

			var fileSize = 10 * 1024 * 1024;
			var s = $.extend({
				handler: 'lms2105v01fileuploadhandler',
				fieldId: "uploadEsgFile",
				title: i18n && i18n.def.insertfile || "請選擇附加檔案",
				fileCheck: ['xlsx|xls|csv'],
				successMsg: false,
				success: function() {
				},
				data: {
					fileSize: fileSize,
					mainId: "",
					deleteDup: true,
					changeUploadName: "uploadEsgFile.xlsx"
				}
			}, s);


			$("#importByExl_esgFile").thickbox({ // 使用選取的內容進行彈窗
				title: "匯入EXCEL名單",
				width: 500,
				height: 200,
				modal: true,
				i18n: i18n.def,
				buttons: (function() {
					var b = {};
					b['上傳'] = function() {

						$.capFileUpload({
							handler: s.handler,
							fileCheck: s.fileCheck,
							fileElementId: s.fieldId,
							successMsg: s.successMsg,
							data: $.extend({
								fieldId: "uploadEsgFile"
							}, s.data || {}),
							success: function(json) {
								$.thickbox.close();
								API.showPopMessage("上傳成功");
							}
						});
					};
					b[i18n && i18n.def.cancel || "取消"] = function() {
						$.thickbox.close();
					};
					return b;
				})()
			});
		}).end().find("#btnUpBisParam").click(function() {

			$("#importBisParam").thickbox({
				title: "更新BIS參數",
				width: 700,
				height: 200,
				modal: true,
				align: "center",
				valign: "bottom",
				buttons: {
					"sure": function() {
						var $form = $("#bisParamForm");
						if ($form.valid()) {
							$.ajax({
								handler: "lms1401s11formhandler",
								action: "saveBisParamToL120s25c",
								type: "POST",
								dataType: "json",
								data: {
									bisParamForm: JSON.stringify($("#bisParamForm").serializeData())
								},
								success: function(obj) {
									$.thickbox.close();
									$.thickbox.close();

									API.showPopMessage("更新成功");
								}
							}); //close ajax

						}
					},
					"cancel": function() {
						$.thickbox.close();
					}
				}
			});
		}).end().find("#btnUpBatGutFile").click(function() {
			//J-112-0366_12473_B1001 新增信保檔案FTP DW資料倉儲系統功能
			$("#uploadBatGutFile").val('');

			var fileSize = 10 * 1024 * 1024;
			var s = $.extend({
				handler: 'lms2105v01fileuploadhandler',
				fieldId: "uploadBatGutFile",
				title: i18n && i18n.def.insertfile || "請選擇附加檔案",
				fileCheck: ['xlsx|xls|csv'],
				successMsg: false,
				success: function() {
				},
				data: {
					fileSize: fileSize,
					mainId: "",
					deleteDup: true,
					changeUploadName: "uploadBatGutFile.xlsx"
				}
			}, s);


			$("#importByExl_batGutFile").thickbox({ // 使用選取的內容進行彈窗
				title: "匯入EXCEL名單",
				width: 500,
				height: 200,
				modal: true,
				i18n: i18n.def,
				buttons: (function() {
					var b = {};
					b['上傳'] = function() {

						$.capFileUpload({
							handler: s.handler,
							fileCheck: s.fileCheck,
							fileElementId: s.fieldId,
							successMsg: s.successMsg,
							data: $.extend({
								fieldId: "uploadBatGutFile"
							}, s.data || {}),
							success: function(json) {
								$.thickbox.close();
								API.showPopMessage("上傳成功");
							}
						});
					};
					b[i18n && i18n.def.cancel || "取消"] = function() {
						$.thickbox.close();
					};
					return b;
				})()
			});
		})



		//其他按鈕EVENT*************************************************************************


		$("#importBisSelfCapitalExl").click(function() {
			//J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
			$("#uploadBisFile").val('');
			var limitFileSize = 3145728 * 500;   //3M * 100 = 300M
			var s = $.extend({
				handler: 'lms2105v01fileuploadhandler',
				fieldId: "uploadBisFile",
				title: i18n && i18n.def.insertfile || "請選擇附加檔案",
				fileCheck: ['xls'],
				limitSize: limitFileSize,
				successMsg: false,
				success: function() {
				},
				data: {
					fileSize: limitFileSize,
					mainId: "",
					deleteDup: true,
					changeUploadName: "uploadBisFile.xlsx"
				}
			}, s);

			$("#importByExl_bisFile").thickbox({ // 使用選取的內容進行彈窗
				title: "引進BIS自有資本與風險性資產",
				width: 500,
				height: 200,
				modal: true,
				i18n: i18n.def,
				buttons: (function() {
					var b = {};
					b['引進'] = function() {

						$.capFileUpload({
							handler: s.handler,
							fileCheck: s.fileCheck,
							fileElementId: s.fieldId,
							successMsg: s.successMsg,
							limitSize: limitFileSize,
							data: $.extend({
								fieldId: "uploadBisFile",

							}, s.data || {}),
							success: function(json) {
								$.thickbox.close();
								if (json.errorMsg != "") {
									CommonAPI.showErrorMessage("引進失敗:" + json.errorMsg);
								} else {
									$("#bisParamForm").find("#lmsBisSelfCapital").val(json.lmsBisSelfCapital);
									$("#bisParamForm").find("#lmsBisMegaRwa").val(json.lmsBisMegaRwa);
									API.showPopMessage("引進成功");
								}
							}
						});
					};
					b[i18n && i18n.def.cancel || "取消"] = function() {
						$.thickbox.close();
					};
					return b;
				})()
			});
		})
	});
});
