package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01S;

/** 個金客戶報表綜合資訊檔 **/
public interface C120S01SDao extends IGenericDao<C120S01S> {

	C120S01S findByOid(String oid);
	
	List<C120S01S> findByMainId(String mainId);
	
	public C120S01S findByUniqueKey(String mainId, String custId, String dupNo, String dataType, String fileSeq);
	
	public List<C120S01S> findByMainIdAndCustIdAndDupNo(String mainId, String custId, String dupNo);
	
	public List<C120S01S> findByList(String mainId, String custId, String dupNo);
	
	public List<C120S01S> findByIdDupDataType(String mainId, String custId, String dupNo, String dataType);
	
	public int deleteByOid(String oid);
}