package com.mega.eloan.lms.batch.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringEscapeUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.crs.service.LMS2405Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lrs.service.LMS1805Service;
import com.mega.eloan.lms.lrs.service.LMS1825Service;
import com.mega.eloan.lms.model.C240M01Z;
import com.mega.eloan.lms.model.L180M01Z;
import com.mega.eloan.lms.model.L182M01A;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

@Service("lrsBatchServiceImpl")
public class LrsBatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(LrsBatchServiceImpl.class);

	private static final long serialVersionUID = 1L;

	@Resource
	LMS1805Service lms1805Service;

	@Resource
	LMS1825Service lms1825Service;

	@Resource
	LMS2405Service lms2405Service;

	@Resource
	BranchService branch;

	@Resource
	EloandbBASEService r6dbService;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		// 可以從json內取得參數
		if (LOGGER.isTraceEnabled()) {
			LOGGER.trace("傳入參數" + json.toString());
		}
		StringBuilder failItem = new StringBuilder();
//		if("01".equals(CapDate.getCurrentDate("dd"))){
		// 測試用
//		if (true) {
			Date dataDate = CapDate.parseDate(CapDate.formatDate(
					CapDate.getCurrentTimestamp(), "yyyy-MM-01"));
			// "P"海外分行(當地有總行) "Q"海外總行(澳洲/加拿大) "R"海外總行(泰國)
			List<IBranch> bank = branch.getBranchByUnitType(
					BranchTypeEnum.海外分行.getCode(), 
					BranchTypeEnum.海外分行當地有總行.getCode(), 
					BranchTypeEnum.海外總行泰國.getCode(), 
					BranchTypeEnum.海外總行澳洲加拿大.getCode());

			List<String> banks = new ArrayList<String>();
			for (int i = 0, size = bank.size(); i < size; i++) {
				banks.add(bank.get(i).getBrNo());
			}

			lms1805Service.deleteL180M01Z(banks, dataDate);

			long t1 = System.currentTimeMillis();
			LOGGER.info("企金[" + dataDate + "] update LMS.LMS412.");
			for (int i = 0, size = banks.size(); i < size; i++) {
				//檢查是否更新過
				L180M01Z l180m01z = lms1805Service.findByDataDateBranch(banks.get(i), dataDate);
				if(l180m01z != null){
					//更新過即不再更新
					LOGGER.info("Branch:["+banks.get(i)+"]"+"DataDate:[" + dataDate + "] already update.");
					continue;
				}
				try {
					//更新
					lms1805Service.updateResoure(banks.get(i), dataDate);
				} catch (Exception e) {
					failItem.append(banks.get(i) + " update LMS.LMS412 Fail;");
					LOGGER.error(e.getMessage());
					LOGGER.info(StrUtils.concat("init() all COST= ",
							(System.currentTimeMillis() - t1), " ms"));
				}
			}
			LOGGER.info("企金[" + dataDate + "] update LMS.LMS412 Finish.");
			LOGGER.info("個金[" + dataDate + "] update LMS.LMS491.");
			for (int i = 0, size = banks.size(); i < size; i++) {
				try {
					//檢查是否更新過
					C240M01Z c240m01z = lms2405Service.findCycmnBrno(dataDate, banks.get(i));
					if(!Util.isEmpty(c240m01z)){
						LOGGER.info("Branch:["+banks.get(i)+"]"+"DataDate:[" + dataDate + "] already update.");
						continue;
					}
					lms2405Service.update490(banks.get(i), dataDate);
				} catch (Exception e) {
					failItem.append(banks.get(i) + " update LMS.LMS491 Fail;");
					LOGGER.error(e.getMessage());
					LOGGER.info(StrUtils.concat("init() all COST= ",
							(System.currentTimeMillis() - t1), " ms"));
				}
			}
			LOGGER.info("個金[" + dataDate + "] update LMS.LMS491 Finish.");
//		}

		
		List<L182M01A> l182m01as = lms1825Service.findUnProcessedTypCd5();
		LOGGER.info("List Handle [" + l182m01as.size() + "]");
		Map<String, List<Date>> map = new HashMap<String, List<Date>>();
		for (L182M01A l182m01a : l182m01as) {
			String oid = StringEscapeUtils.escapeSql(l182m01a.getOid());
			String branchList = l182m01a.getBranchList();
			LOGGER.info("Branch [" + branchList + "]");
			String[] branchArray = branchList.split(",");
			List<String> branchs = new ArrayList<String>();
			boolean success = true;
			for (String branch : branchArray) {
				// 檢查是否有更新完成
				if (lms1805Service.checkOneUpdate(branch)) {
					branchs.add(branch);
				} else {
					if (success) {
						// 產生失敗
						success = false;
						LOGGER.info("Branch:"+branchs.toArray()+"====>未更新");
					}
				}
			}

			t1 = System.currentTimeMillis();
			for (int i = 0, size = branchs.size(); i < size; i++) {
				List<Date> dataDates = map.get(branchs.get(i));
				if (Util.isEmpty(dataDates) || dataDates.size() == 0) {
					dataDates = new ArrayList<Date>();
				}
				// 重複名單
				if (dataDates.contains(l182m01a.getBaseDate())) {
					success = false;
					LOGGER.info("Date:["+dataDates+"]Branch:["+branchs.get(i)+"]有重複預約名單");
					continue;
				}
				boolean produceSuccess = true;
				try {
					LOGGER.info("[" + l182m01a.getBaseDate()
							+ "] Produce LMS.L180M01A.");
					if (success) {
						LOGGER.info("Update Unnormal.");
						produceSuccess = lms1805Service.updateUnusual(branchs
								.get(i));
					}
					if (!produceSuccess) {
						LOGGER.info("異常更新失敗");
						failItem.append("[" + oid
								+ "] Produce LMS.L180M01A Fail.(異常更新失敗);");
					} else {
						// 產生名單
						produceSuccess = lms1805Service.produceList(
								l182m01a.getBaseDate(), branchs.get(i), "1",
								l182m01a.getOwnBrId());
					}
					success = produceSuccess;
					LOGGER.info("[" + l182m01a.getBaseDate() + "]名單產生完成");
				} catch (Exception e) {
					failItem.append(oid + "企金產生名單失敗;");
					LOGGER.error(e.getMessage());
					LOGGER.info(StrUtils.concat("init() all COST= ",
							(System.currentTimeMillis() - t1), " ms"));
				}
				dataDates.add(l182m01a.getBaseDate());
				map.put(branchs.get(i), dataDates);
			}

			try {
				if (success) {
					lms1825Service.flowAction(oid, l182m01a,
							true, true);
				} else {
					lms1825Service.flowAction(oid, l182m01a,
							true, false);
					failItem.append(oid + "企金產生名單失敗;");
				}
			} catch (Throwable e) {
				LOGGER.error(e.getMessage());
				failItem.append(oid + "企金產生名單失敗(流程);");
			}
		}

		// **********************************************************************
		// 回傳範例(需要回傳結果值或錯誤資訊
		// **********************************************************************
		// 回傳請組JSONObject ==> {"rc":xx , "rcmsg":"xxxx", "response":"xxxxx" }
		// boolean isSuccess = true;
		// JSONObject result = null;
		// if (isSuccess) {
		// result = WebBatchCode.RC_SUCCESS;
		// result.element(WebBatchCode.P_RESPONSE, "需要回傳的執行結果資料");
		//
		// } else {
		// result = WebBatchCode.RC_ERROR;
		// result.element(WebBatchCode.P_RC_MSG, "這是錯誤資訊!");
		// }
		// return result;

		JSONObject mag = new JSONObject();
		if (Util.isEmpty(failItem.toString())) {
			mag = WebBatchCode.RC_SUCCESS;
		} else {
			mag = WebBatchCode.RC_ERROR;
			mag.element(WebBatchCode.P_RC_MSG, failItem.toString());
		}
		return mag;
	}

}