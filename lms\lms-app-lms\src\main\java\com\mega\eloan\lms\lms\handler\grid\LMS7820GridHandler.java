/* 
 *  LMS7820GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.grid;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;


import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.lms.service.LMS7820Service;
import com.mega.eloan.lms.model.L782M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;

/**
 * <pre>
 * 特殊登錄案件紀錄表
 * </pre>
 * 
 * @since 2011/12/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/8,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7820gridhandler")
public class LMS7820GridHandler extends AbstractGridHandler {

	@Resource
	LMS7820Service lms7820Service;

	@Resource
	CodeTypeService codeTypeService;

	/**
	 * 查詢特殊登錄案件紀錄表外部的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL782m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l782a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms7820Service.findPage(
				L782M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		// lms1405m01_SubItem授信科目
		dataReformatter.put("loanTP", new CodeTypeFormatter(codeTypeService,
				UtilConstants.CodeTypeItem.授信科目)); // codeType格式化
		result.setDataReformatter(dataReformatter);
		return result;

	}
}
