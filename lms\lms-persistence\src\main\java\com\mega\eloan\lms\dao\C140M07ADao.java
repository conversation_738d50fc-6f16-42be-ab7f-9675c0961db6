/* 
 * C140M07ADao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C140M07A;

/**
 * <pre>
 * 徵信調查報告書第柒章主檔
 * </pre>
 * 
 * @since 2011/10/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/05,TimChiang, new
 *          </ul>
 */
public interface C140M07ADao extends IGenericDao<C140M07A> {

	/**
	 * 取得財務概況主檔
	 * 
	 * @param pid
	 *            pid
	 * @param mainId
	 *            mainId
	 * @param tab
	 *            頁籤
	 * @param subtab
	 *            子頁籤
	 * @return List<C140M07A>
	 */
	List<C140M07A> findByMainPidTab(String pid, String mainId, String tab,
			String subtab);

}
