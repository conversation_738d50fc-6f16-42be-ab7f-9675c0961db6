initDfd.done(function(json){
	
	var $gridview = $("#gridview").iGrid({
        handler: 'lms1700gridhandler',
        height: 320,
        sortname: 'projectNo|custId',
        sortorder: 'asc|asc',
        multiselect: false,
        needPager: false,        
        shrinkToFit: false,
        postData: {
            formAction: "queryL170M01AFromL180"
            , 'pid': json.mainId	
        },      
        colModel: [{
            // "序號",
            colHeader: i18n.lms1800m01['grid.projectSeq'],name: 'projectNo', width : 50, sortable : true
        }, {
            // "借款人統編",
            colHeader: i18n.lms1800m01['grid.custId'],name: 'custId', width : 80, sortable : true, formatter : 'click', onclick : openDoc
        }, {
            // "借款人姓名",
            colHeader: i18n.lms1800m01['L180M01B.elfCName'],name: 'custName', width : 130, sortable : true
        }, {
            // "主要戶",
            colHeader: i18n.lms1800m01['grid.elfMainCust'],name: 'm<PERSON><PERSON><PERSON><PERSON>', align : "center", width : 45, sortable : true,
            formatter: function(value){
            	if(value == "Y"){
            		return "<img src='webroot/img/lms/M.png'>";
            	}else if(value == "N"){
            		return "";
            	}else{
            		return value;
            	}
            }
        }, {
            // "覆審日期",
            colHeader: i18n.lms1800m01['label.retrialDate'],name: 'retrialDate', width : 70, sortable : true
        }, {
            // "上次覆審日",
            colHeader: i18n.lms1800m01['grid.newLRDate'],name:'lastRetrialDate', width : 70, sortable : true
        }, {
            // "上傳",
            colHeader: i18n.lms1800m01['label.upFlag'],name:'upFlag', align : "center", width : 40, sortable : false,
            formatter: function(value){
            	if(value == "N"){
            		return "<img src='webroot/img/lms/X.png'>";
            	}else if(value == "Y"){
            		return "<img src='webroot/img/lms/V.png'>";
            	}else{
            		return value;
            	}
            }
        }, {
            // "覆審人員",
            colHeader: i18n.lms1800m01['label.retrialStaff'],name : 'retrialStaff', width : 60, sortable : false
        }, {
            // "文件狀態",
            colHeader: i18n.lms1800m01['label.docStatus'],name:'docStatus', width : 100, sortable : true
        }, {
            // "不覆審註記",
            colHeader: i18n.lms1800m01['label.nCkdFlag'],name:'nCkdFlag', width : 40, sortable : true
        }, {
            // "覆審意見",
            colHeader: i18n.lms1800m01['label.condition'],name:'condition', width : 160, sortable : false
        }, {
            // "覆審意見",
            colHeader: i18n.lms1800m01['label.branchComm'],name:'branchComm', width : 120, sortable : false
        }, {
            // "RPA狀態",
            colHeader: i18n.lms1800m01['L180M01A.status'],name:'status', width : 60, sortable : false    
        }
        , { name: 'oid', hidden: true }
        , { name: 'mainId', hidden: true }
        , { name: 'mainDocStatus', hidden: true }
        ]
    });	
});

function openDoc(cellvalue, options, rowObject){
	var postData = {
		'mainOid': rowObject.oid, 
		'mainId': rowObject.mainId,
		'mainDocStatus': rowObject.mainDocStatus
	}
	$.form.submit({ url:'../lms1700m01/01', data:postData, target:rowObject.oid});
}