/* 
 * LMS1415ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.L000M01ADao;
import com.mega.eloan.lms.dao.L120M01BDao;
import com.mega.eloan.lms.dao.L120M01FDao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L141A01ADao;
import com.mega.eloan.lms.dao.L141M01ADao;
import com.mega.eloan.lms.dao.L141M01BDao;
import com.mega.eloan.lms.dao.L141M01CDao;
import com.mega.eloan.lms.dao.L141M01DDao;
import com.mega.eloan.lms.lms.pages.LMS1415M01Page;
import com.mega.eloan.lms.lms.report.impl.LMS1205R01RptServiceImpl;
import com.mega.eloan.lms.lms.service.LMS1415Service;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.L000M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L141A01A;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.eloan.lms.model.L141M01B;
import com.mega.eloan.lms.model.L141M01C;
import com.mega.eloan.lms.model.L141M01D;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 聯行額度明細表
 * </pre>
 * 
 * @since 2011/12/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/2,REX,new
 *          <li>2013/04/22,UFO,增加L000M01A設ngflag值
 *          <li>2013-08-05,Rex,#646排除已經刪除的額度明細表不傳到聯行額度明細表
 *          </ul>
 */
@Service
public class LMS1415ServiceImpl extends AbstractCapService implements
		LMS1415Service {

	@Resource
	L141M01ADao l141m01aDao;

	@Resource
	L141M01BDao l141m01bDao;

	@Resource
	L141M01CDao l141m01cDao;

	@Resource
	L141M01DDao l141m01dDao;

	@Resource
	L141A01ADao l141a01aDao;

	@Resource
	L120M01BDao l120m01bDao;

	@Resource
	L120S01BDao l120s01bDao;
	@Resource
	C120S01ADao c120s01aDao;
	@Resource
	L120S01ADao l120s01aDao;
	@Resource
	C120M01ADao c120m01aDao;
	@Resource
	L120M01FDao l120m01fDao;
	@Resource
	L000M01ADao l000m01aDao;

	@Resource
	FlowService flowService;

	@Resource
	DocLogService docLogService;

	@Resource
	FlowNameService flowNameService;

	@Resource
	UserInfoService userInfoService;
	@Resource
	LMSService lmsService;

	@Resource
	L140M01ADao l140m01aDao;
	
	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L141M01A.class) {
			return l141m01aDao.findPage(search);
		} else if (clazz == L141M01B.class) {
			return l141m01bDao.findPage(search);
		} else if (clazz == L141M01C.class) {
			return l141m01cDao.findPage(search);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L141M01A) {
					if (Util.isEmpty(((L141M01A) model).getOid())) {

						l141m01aDao.save((L141M01A) model);
						flowService.start("LMS1415Flow",
								((L141M01A) model).getOid(), user.getUserId(),
								user.getUnitNo());
					} else {
						((L141M01A) model).setUpdater(user.getUserId());
						((L141M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						l141m01aDao.save((L141M01A) model);
						docLogService.record(((L141M01A) model).getOid(),
								DocLogEnum.SAVE);

					}

				} else if (model instanceof L141M01B) {
					((L141M01B) model).setUpdater(user.getUserId());
					((L141M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l141m01bDao.save((L141M01B) model);
				} else if (model instanceof L141M01C) {
					((L141M01C) model).setUpdater(user.getUserId());
					((L141M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l141m01cDao.save((L141M01C) model);
				} else if (model instanceof L141M01D) {
					((L141M01D) model).setUpdater(user.getUserId());
					((L141M01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l141m01dDao.save((L141M01D) model);
				} else if (model instanceof L141A01A) {
					l141a01aDao.save((L141A01A) model);
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L141M01A) {
					((L141M01A) model).setDeletedTime(CapDate
							.getCurrentTimestamp());
					((L141M01A) model).setUpdater(user.getUserId());
					l141m01aDao.save((L141M01A) model);
					docLogService.record(((L141M01A) model).getOid(),
							DocLogEnum.DELETE);
				}
			}

		}
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L141M01A.class) {
			return (T) l141m01aDao.findByOid(oid);
		} else if (clazz == L141M01B.class) {
			return (T) l141m01bDao.findByOid(oid);
		} else if (clazz == L141M01C.class) {
			return (T) l141m01cDao.findByOid(oid);
		} else if (clazz == L141M01D.class) {
			return (T) l141m01dDao.findByOid(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L141M01B.class) {
			return l141m01bDao.findByMainId(mainId);
		} else if (clazz == L141M01C.class) {
			return l141m01cDao.findByMainId(mainId);
		} else if (clazz == L141M01D.class) {
			return l141m01dDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public L141M01A findL141M01AByMainId(String mainId) {
		return l141m01aDao.findByMainId(mainId);
	}

	@Override
	public void saveL141m01bs(List<L141M01B> l141m01bs) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L141M01B l141m01b : l141m01bs) {
			l141m01b.setUpdater(user.getUserId());
			l141m01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l141m01bDao.save(l141m01bs);
	}

	@Override
	public void saveL141m01cs(List<L141M01B> l141m01bs,
			List<L141M01C> l141m01cs, GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L141M01C l141m01c : l141m01cs) {
			l141m01c.setUpdater(user.getUserId());
			l141m01c.setUpdateTime(CapDate.getCurrentTimestamp());
			if (Util.isEmpty(l141m01c.getOid())) {
				l141m01c.setCreator(user.getUserId());
				l141m01c.setCreateTime(CapDate.getCurrentTimestamp());
			}
		}
		for (L141M01B l141m01b : l141m01bs) {
			l141m01b.setUpdater(user.getUserId());
			l141m01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l141m01cDao.save(l141m01cs);
		l141m01bDao.save(l141m01bs);
		save(entity);
	}

	@Override
	public void saveL141m01ds(List<L141M01D> l141m01ds) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L141M01D l141m01d : l141m01ds) {
			l141m01d.setUpdater(user.getUserId());
			l141m01d.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l141m01dDao.save(l141m01ds);
	}

	@Override
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, String next) throws Throwable {
		try {

			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS1415Flow", mainOid,
						user.getUserId(), user.getUnitNo());
			}
			if (setResult) {

				// 當有setResult值才要塞
				inst.setAttribute("result", flowNameService.getKey(next));
			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	@Override
	public void deleteL141m01ds(List<L141M01D> l141m01ds) {
		l141m01dDao.delete(l141m01ds);
	}

	@Override
	public L141M01A copyL120M01A(L120M01A l120m01a, String brank,
			List<L120M01C> l120m01cs) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 複製案件簽報書主檔資料
		L141M01A l141m01a = new L141M01A();
		// UPGRADE: 待確認，URL是否正確
		l141m01a.setDocURL(CapWebUtil.getDocUrl(LMS1415M01Page.class));
		l141m01a.setMainId(IDGenerator.getUUID());
		l141m01a.setRandomCode(IDGenerator.getRandomCode());
		l141m01a.setTypCd(l120m01a.getTypCd());
		l141m01a.setCustId(l120m01a.getCustId());
		l141m01a.setDupNo(l120m01a.getDupNo());
		l141m01a.setCustName(l120m01a.getCustName());
		l141m01a.setUnitType(l120m01a.getUnitType());
		l141m01a.setOwnBrId(brank);
		l141m01a.setCaseBrId(l120m01a.getCaseBrId());
		l141m01a.setCreator(user.getUserId());
		l141m01a.setCreateTime(CapDate.getCurrentTimestamp());
		l141m01a.setCaseNo(l120m01a.getCaseNo());
		l141m01a.setCaseDate(l120m01a.getCaseDate());
		l141m01a.setFinalDate(l120m01a.getEndDate());
		l141m01a.setDocStatus(CreditDocStatusEnum.海外_編製中);
		l141m01a.setSrcMainId(l120m01a.getMainId());
		l141m01a.setDocType(l120m01a.getDocType());
		l141m01a.setDocKind(l120m01a.getDocKind());
		l141m01a.setAuthLvl(l120m01a.getAuthLvl());
		l141m01a.setCaseLvl(l120m01a.getCaseLvl());
		l141m01a.setApprover(l120m01a.getApprover());

		L120M01B l120m01b = l120m01bDao.findByUniqueKey(l120m01a.getMainId());
		if (l120m01b != null) {
			l141m01a.setUnitCase(l120m01b.getUnitCase());
		}

		// 複製共同借款人
		StringBuilder custIdKey = new StringBuilder("");
		Map<String, String> custCounty = new HashMap<String, String>();
		// 1企金 2個金 、取得借款人國別
		if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a.getDocType())) {
			List<L120S01B> l120s01bs = l120s01bDao.findByMainId(l120m01a
					.getMainId());
			for (L120S01B l120s01b : l120s01bs) {
				custIdKey.append(l120s01b.getCustId()).append(
						l120s01b.getDupNo());
				custCounty.put(custIdKey.toString(), l120s01b.getNtCode());
				custIdKey.setLength(0);
			}
		} else if (UtilConstants.Casedoc.DocType.個金.equals(l120m01a
				.getDocType())) {
			List<C120S01A> c120s01as = c120s01aDao.findByMainId(l120m01a
					.getMainId());
			for (C120S01A c120s01a : c120s01as) {
				custIdKey.append(c120s01a.getCustId()).append(
						c120s01a.getDupNo());
				custCounty.put(custIdKey.toString(), c120s01a.getNtCode());
				custIdKey.setLength(0);
			}
		}

		List<L141M01C> l141m01cs = new ArrayList<L141M01C>();
		if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a.getDocType())) {
			List<L120S01A> l120s01as = l120s01aDao.findByMainId(l120m01a
					.getMainId());

			for (L120S01A l120s01a : l120s01as) {
				L141M01C l141m01c = new L141M01C();
				// 國內個金案件新增　※如本欄為空白或null則表示全部。
				l141m01c.setCntrNo("");
				l141m01c.setCustId(l120s01a.getCustId());
				l141m01c.setDupNo(l120s01a.getDupNo());
				l141m01c.setCustName(l120s01a.getCustName());
				l141m01c.setDocType(l120s01a.getDocType());
				l141m01c.setTypCd(l120s01a.getTypCd());
				l141m01c.setCustNo(l120s01a.getCustNo());
				l141m01c.setKeyMan(l120s01a.getKeyMan());
				custIdKey.append(l120s01a.getCustId()).append(l120s01a.getDupNo());
				l141m01c.setNtCode(custCounty.get(custIdKey.toString()));
				custIdKey.setLength(0);
				l141m01c.setCustRlt(l120s01a.getCustRlt());
				l141m01c.setCustPos(l120s01a.getCustPos());
				l141m01c.setMainId(l141m01a.getMainId());
				l141m01cs.add(l141m01c);
			}
		}else if (UtilConstants.Casedoc.DocType.個金.equals(l120m01a.getDocType())) {
			List<C120M01A> c120m01as = c120m01aDao.findByMainId(l120m01a
					.getMainId());

			for (C120M01A c120m01a : c120m01as) {
				L141M01C l141m01c = new L141M01C();
				// 國內個金案件新增　※如本欄為空白或null則表示全部。
				l141m01c.setCntrNo("");
				l141m01c.setCustId(c120m01a.getCustId());
				l141m01c.setDupNo(c120m01a.getDupNo());
				l141m01c.setCustName(c120m01a.getCustName());
				l141m01c.setDocType(l120m01a.getDocType());
				l141m01c.setTypCd(c120m01a.getTypCd());
				l141m01c.setCustNo(c120m01a.getCustNo());
				l141m01c.setKeyMan(c120m01a.getKeyMan());
				custIdKey.append(c120m01a.getCustId()).append(c120m01a.getDupNo());
				l141m01c.setNtCode(custCounty.get(custIdKey.toString()));
				custIdKey.setLength(0);
				l141m01c.setCustRlt(c120m01a.getO_custRlt());
				l141m01c.setCustPos(c120m01a.getCustPos());
				l141m01c.setMainId(l141m01a.getMainId());
				l141m01cs.add(l141m01c);
			}
		}
		
		// lms1415Service.saveL141m01cs(l141m01cs);

		// 新增授權檔
		L141A01A l141a01a = new L141A01A();
		l141a01a.setAuthTime(CapDate.getCurrentTimestamp());
		l141a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
		l141a01a.setAuthUnit(brank);
		l141a01a.setMainId(l141m01a.getMainId());
		l141a01a.setOwner(user.getUserId());
		l141a01a.setOwnUnit(user.getUnitNo());
		List<L141M01B> l141m01bs = new ArrayList<L141M01B>();

		// 複製關聯檔
		for (L120M01C l120m01c : l120m01cs) {

			L140M01A l140m01a = l120m01c.getL140m01a();
			if (l140m01a == null) {
				continue;
			}
			// 2013-08-05,Rex,#646排除已經刪除的額度明細表不傳到聯行額度明細表
			if (Util.isNotEmpty(l140m01a.getDeletedTime())) {
				continue;
			}
			L141M01B l141m01b = new L141M01B();

			l141m01b.setItemType(l120m01c.getItemType());
			// 將聯行額度明細表mainId存入
			l141m01b.setMainId(l141m01a.getMainId());
			l141m01b.setRefMainId(l120m01c.getRefMainId());
			l141m01bs.add(l141m01b);
		}

		// 複製簽章欄
		List<L141M01D> l141m01ds = new ArrayList<L141M01D>();

		List<L120M01F> l120m01fs = l120m01fDao.findByMainId(l120m01a
				.getMainId());
		// staffNo CHAR(6)
		// staffJob CHAR(2) L1. 分行經辦
		// (授管處/營運中心)
		// L2. 帳戶管理員
		// L3. 分行授信/覆核主管
		// (母行)
		// L4. 分行覆核主管
		// (母行/授管處/營運中心)
		// L5. 單位/授權主管
		// (母行)
		// L6. 分行單位主管
		// (母行/授管處/營運中心)

		for (L120M01F l120m01f : l120m01fs) {
			// 如果不屬於分行的不儲存
			if (!UtilConstants.BRANCHTYPE.分行.equals(l120m01f.getBranchType())) {
				continue;
			}
			L141M01D l141m01d = new L141M01D();
			l141m01d.setBranchType("1");
			l141m01d.setMainId(l141m01a.getMainId());
			l141m01d.setBranchId(l120m01f.getBranchId());
			l141m01d.setStaffNo(l120m01f.getStaffNo());
			l141m01d.setStaffJob(l120m01f.getStaffJob());
			l141m01d.setCreateTime(CapDate.getCurrentTimestamp());
			l141m01d.setCreator(user.getUnitNo());
			l141m01d.setSeq(l120m01f.getSeq());
			if (Util.isEmpty(userInfoService.getUserName(l120m01f.getStaffNo()))) {
				l141m01d.setStaffName(l120m01f.getStaffNo());
			} else {
				l141m01d.setStaffName(userInfoService.getUserName(l120m01f
						.getStaffNo()));
			}
			if (UtilConstants.STAFFJOB.經辦L1.equals(l120m01f.getStaffJob())) {
				l141m01a.setCoAppraiser(l120m01f.getStaffNo());
			}
			l141m01ds.add(l141m01d);
		}
		this.saveL141m01ds(l141m01ds);
		this.saveL141m01cs(l141m01bs, l141m01cs, l141a01a, l141m01a);

		L000M01A l000m01a = new L000M01A();
		l000m01a.setCaseNo(l141m01a.getCaseNo());
		l000m01a.setCaseType("L141M01A");
		l000m01a.setCustId(l141m01a.getCustId());
		l000m01a.setCustName(l141m01a.getCustName());
		l000m01a.setDocStatus(l141m01a.getDocStatus());
		l000m01a.setDocURL(l141m01a.getDocURL());
		l000m01a.setDupNo(l141m01a.getDupNo());
		l000m01a.setOwnBrId(l141m01a.getOwnBrId());
		l000m01a.setSendBrid(user.getUnitNo());
		l000m01a.setSendInfo(CapDate.getCurrentTimestamp());
		l000m01a.setSrcMainId(l141m01a.getMainId());
		l000m01a.setSrcOid(l141m01a.getOid());
		l000m01a.setSrcTxCode(l141m01a.getTxCode());
		l000m01a.setNgFlag(l120m01a.getNgFlag());
		l000m01aDao.save(l000m01a);
		return l141m01a;
	}

	@Override
	public List<L141M01D> findL141m01dsByMainIdAndBranchType(String mainId,
			String branchType) {
		return l141m01dDao.findByMainIdAndBranchType(mainId, branchType);
	}

	@Override
	public L141M01D findL141m01dsByUniqueKey(String mainId, String branchType,
			String staffJob) {
		return l141m01dDao.findByUniqueKey(mainId, branchType, staffJob);
	}

	@Override
	public Page<Map<String, Object>> getPringMenu(String mainId,
			ISearch pageSetting) {
		Properties rptProperties = MessageBundleScriptCreator
				.getComponentResource(LMS1205R01RptServiceImpl.class);
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		L141M01A l141m01a = l141m01aDao.findByMainId(mainId);
		String itemType = lmsService.checkL140M01AItemType(
				l141m01a.getCaseBrId(), l141m01a.getDocKind(),
				l141m01a.getAuthLvl());
		//List<L141M01B> l141m01bs = l141m01bDao.findByIndex01(mainId, itemType,
		//		null);
		
		List<L140M01A> l140m01as = l140m01aDao
		.findL140m01aListByL141m01bMainIdForPrint(mainId, itemType,
				null);
		
		String rptNo = "";
		String rpt = "";
		String rptName = "";
		if (UtilConstants.Cntrdoc.ItemType.額度明細表.equals(itemType)) {
			rptNo = "LMS1405R01";
			rpt = "R12";
			rptName = rptProperties.getProperty("TITLE.RPTNAME8");
		} else {
			rptNo = "LMS1405R01";
			rpt = "R13";
			rptName = rptProperties.getProperty("TITLE.RPTNAME9");
		}
		// L140M01A．額度明細表主檔
		for (L140M01A l140m01a : l140m01as) {
			// L140M01A l140m01a = l141m01b.getL140m01a();
			if (l140m01a != null && Util.isEmpty(l140m01a.getDeletedTime())) {
				data = new HashMap<String, Object>();
				data.put("custName",
						l140m01a.getCustName() + " " + l140m01a.getCustId()
								+ " " + l140m01a.getDupNo());
				data.put("rptName", rptName);
				data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
				data.put("oid", Util.nullToSpace(l140m01a.getOid()));
				data.put("rpt", rpt);
				data.put("rptNo", rptNo);

				data.put("printSeq", l140m01a.getPrintSeq());
				data.put("custId", Util.nullToSpace(l140m01a.getCustId()));

				beanList.add(data);
			}
		}

		return LMSUtil.getMapGirdDataRow(beanList, pageSetting);
	}
}
