package com.mega.eloan.lms.cls.service.impl;

import java.util.Date;
import java.util.List;
import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.cls.service.CLS3801Service;
import com.mega.eloan.lms.dao.C103M01ADao;
import com.mega.eloan.lms.dao.C103M01EDao;
import com.mega.eloan.lms.model.C103M01A;
import com.mega.eloan.lms.model.C103M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class CLS3801ServiceImpl extends AbstractCapService implements
		CLS3801Service {

	@Resource
	C103M01ADao c103m01aDao;

	@Resource
	C103M01EDao c103m01eDao;

	@Resource
	DocLogService docLogService;

	@Resource
	TempDataService tempDataService;

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == C103M01A.class) {
			return c103m01aDao.findByMainId(mainId);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C103M01A.class) {
			return (T) c103m01aDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C103M01A) {
					C103M01A c103m01a = (C103M01A) model;
					if (Util.isEmpty(c103m01a.getOid())) {
						c103m01a.setUpdater(user.getUserId());
						c103m01a.setUpdateTime(CapDate.getCurrentTimestamp());
						c103m01aDao.save(c103m01a);
						docLogService.record(c103m01a.getOid(),
								DocLogEnum.CREATE);
					} else {
						if (c103m01a.getDocStatus().equals(
								CreditDocStatusEnum.海外_編製中.getCode())) {

							// 當文件狀態為編製中時文件亂碼才變更
							c103m01a.setRandomCode(IDGenerator.getRandomCode());
							c103m01a.setUpdater(user.getUserId());
							c103m01a.setUpdateTime(CapDate
									.getCurrentTimestamp());
							// 清掉覆核資訊
							c103m01a.setApprover(null);
							c103m01a.setApproveTime(null);
							if (Util.notEquals("Y", SimpleContextHolder
									.get(EloanConstants.TEMPSAVE_RUN))) {
								tempDataService.deleteByMainId(c103m01a
										.getMainId());
								docLogService.record(c103m01a.getOid(),
										DocLogEnum.SAVE);
							}
						} else if (c103m01a.getDocStatus().equals(
								CreditDocStatusEnum.海外_待覆核.getCode())) {

							// 清掉覆核資訊
							c103m01a.setApprover(null);
							c103m01a.setApproveTime(null);
						} else if (c103m01a.getDocStatus().equals(
								CreditDocStatusEnum.海外_已核准.getCode())) {

							c103m01a.setApprover(user.getUserId());
							c103m01a.setApproveTime(CapDate
									.getCurrentTimestamp());
							c103m01a.setIsClosed(UtilConstants.DEFAULT.是);

						}
						c103m01aDao.save(c103m01a);
					}
				} else if (model instanceof C103M01E) {
					c103m01eDao.save(((C103M01E) model));
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C103M01A) {
					c103m01aDao.delete((C103M01A) model);
				} else if (model instanceof C103M01E) {
					c103m01eDao.delete((C103M01E) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C103M01A.class) {
			return c103m01aDao.findPage(search);
		}
		return null;
	}

	@Override
	public void daoSave(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C103M01A) {
					c103m01aDao.save((C103M01A) model);
				} else if (model instanceof C103M01E) {
					c103m01eDao.save((C103M01E) model);
				}
			}
		}
	}

	@Override
	public List<C103M01A> findRptCls180r58(String ownBrId, Date rptStartDate,
			Date rptEndDate) {
		return c103m01aDao.findRptCls180r58(ownBrId, rptStartDate, rptEndDate);
	}

	@Override
	public List<C103M01E> findC103M01E(String mainId) {
		return c103m01eDao.findByMainId(mainId);
	}

}
