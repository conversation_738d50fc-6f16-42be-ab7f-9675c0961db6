/* 
 * L120S05FDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S05F;

/** 借款人集團授信明細檔 **/
public interface L120S05FDao extends IGenericDao<L120S05F> {

	L120S05F findByOid(String oid);
	
	List<L120S05F> findByMainId(String mainId);
	
	L120S05F findByUniqueKey(String mainId, String custId, String dupNo, String custId1, String dupNo1);

	List<L120S05F> findByIndex01(String mainId, String custId, String dupNo, String custId1, String dupNo1);

	List<L120S05F> findByIndex02(String mainId, String custId, String dupNo);
	
	int delModel(String mainId, String custId, String dupNo);
}