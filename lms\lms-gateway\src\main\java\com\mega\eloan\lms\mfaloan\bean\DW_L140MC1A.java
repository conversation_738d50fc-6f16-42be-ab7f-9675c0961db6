package com.mega.eloan.lms.mfaloan.bean;

import javax.persistence.Column;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;
import java.sql.Timestamp;
import tw.com.iisi.cap.model.GenericBean;


@Entity
@Table(name="DW_L140MC1A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class DW_L140MC1A extends GenericBean{
	/** 
	 * 
	 * MAINID 文件編號
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)", nullable=false, unique = true)
	private String mainid;
	
	/** 額度序號  **/
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrno;
	
	/** 客戶ID **/
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10) ")
	private String custid;
	
	/** 因子A 借款人所得與借款金額顯不相稱且無其他財力文件可供說明**/
	@Column(name="ITEM_A", length=2, columnDefinition="VARCHAR(2)")
	private String item_a;
	
	/** 因子B 借款人現居地或工作地與案件來源無地緣關係**/
	@Column(name="ITEM_B", length=2, columnDefinition="VARCHAR(2)")
	private String item_b;
	
	/** 因子C 保證人財力明顯高於借款人且二者無親屬或共同生活關係**/
	@Column(name="ITEM_C", length=2, columnDefinition="VARCHAR(2)")
	private String item_c;
	/** 因子D 擔保品為法拍屋_建商餘屋_長期空屋**/
	@Column(name="ITEM_D", length=2, columnDefinition="VARCHAR(2)")
	private String item_d;
	/** 因子E 非行員之同一介紹人轉介整批之申請案件**/
	@Column(name="ITEM_E", length=2, columnDefinition="VARCHAR(2)")
	private String item_e;
	/** 因子F 借款人提供之資料與聯徵中心或與本行內部留存資料不相符 **/
	@Column(name="ITEM_F", length=2, columnDefinition="VARCHAR(2)")
	private String item_f;
	/** 因子G 年資小於等於一年**/
	@Column(name="ITEM_G", length=2, columnDefinition="VARCHAR(2)")
	private String item_g;
	/** 因子H 前工作屬性是否與現工作相同**/
	@Column(name="ITEM_H", length=2, columnDefinition="VARCHAR(2)")
	private String item_h;
	/** 因子I 本案是否已確實直接照會借保人本人無誤**/
	@Column(name="ITEM_I", length=2, columnDefinition="VARCHAR(2)")
	private String item_i;
	/** 因子J 已確實核對申貸文件正本資料**/
	@Column(name="ITEM_J", length=2, columnDefinition="VARCHAR(2)")
	private String item_j;
	/** 因子K  借款人或保證人提供申請資料及證明文件過於完整 **/
	@Column(name="ITEM_K", length=2, columnDefinition="VARCHAR(2)")
	private String item_k;
	/** 因子L 檢查擔保品所有權人_其是否在別的案子也是擔保品提供人**/
	@Column(name="ITEM_L", length=2, columnDefinition="VARCHAR(2)")
	private String item_l;
	/** 因子M 不同借款人案下保證人_擔保品提供人或聯絡資料相同**/
	@Column(name="ITEM_M", length=2, columnDefinition="VARCHAR(2)")
	private String item_m;
	/** 因子N 借款人名下有多筆非自住房貸貸款**/
	@Column(name="ITEM_N", length=2, columnDefinition="VARCHAR(2)")
	private String item_n;
	/** 因子O 借款人於聯徵中心無授信資料**/
	@Column(name="ITEM_O", length=2, columnDefinition="VARCHAR(2)")
	private String item_o;
	/** 因子P 借款人於聯徵中心被查詢次數密集**/
	@Column(name="ITEM_P", length=2, columnDefinition="VARCHAR(2)")
	private String item_p;
	/** 因子Q 借款人於聯徵中心無信用卡記錄或持卡小於一年**/
	@Column(name="ITEM_Q", length=2, columnDefinition="VARCHAR(2)")
	private String item_q;
	/** 因子R 評等升等**/
	@Column(name="ITEM_R", length=2, columnDefinition="VARCHAR(2)")
	private String item_r;
	/** 因子S 有寬限期案件**/
	@Column(name="ITEM_S", length=2, columnDefinition="VARCHAR(2)")
	private String item_s;
	/** 因子T 有辦房貸壽險案**/
	@Column(name="ITEM_T", length=2, columnDefinition="VARCHAR(2)")
	private String item_t;
	/** 因子U 承辦案件的地政士有受警示紀錄 **/
	@Column(name="ITEM_U", length=2, columnDefinition="VARCHAR(2)")
	private String item_u;
	/** 因子V 買賣契約書_如有_與借款契約_借款申請書簽名不一致**/
	@Column(name="ITEM_V", length=2, columnDefinition="VARCHAR(2)")
	private String item_v;
	/** 因子W  借款人_擔保品所有權人與房屋契約書之買方不同人**/
	@Column(name="ITEM_W", length=2, columnDefinition="VARCHAR(2)")
	private String item_w;
	/** 因子X 由非親屬之第三人陪同申辦貸款並回答詢問案件問題 **/
	@Column(name="ITEM_X", length=2, columnDefinition="VARCHAR(2)")
	private String item_x;
	/** 因子Y 借款人指定撥款日期及時間且無法提出合理解釋**/
	@Column(name="ITEM_Y", length=2, columnDefinition="VARCHAR(2)")
	private String item_y;
	/** 因子Z 對自己從事之行業或職業性質與內容及貸款用途不瞭解或毫無概念**/
	@Column(name="ITEM_Z", length=2, columnDefinition="VARCHAR(2)")
	private String item_z;
	/** 因子1 於聯徵具被查詢紀錄_卻無法說明原因 **/
	@Column(name="ITEM_1", length=2, columnDefinition="VARCHAR(2)")
	private String item_1;
	/** 因子2  支付銀行收取開辦手續費以外之費用 **/
	@Column(name="ITEM_2", length=2, columnDefinition="VARCHAR(2)")
	private String item_2;
	/** 因子3 借款人或保證人之扣繳憑單給付總額或收入入帳金額為整數**/
	@Column(name="ITEM_3", length=2, columnDefinition="VARCHAR(2)")
	private String item_3;
	/** 因子4 借戶所得來自建築業者_代銷_仲介_是否購屋價金來自第三人_且無法佐證其與第三人之關係**/
	@Column(name="ITEM_4", length=2, columnDefinition="VARCHAR(2)")
	private String item_4;
	
	/**  是否為疑似人頭戶案件 **/
	@Column(name="IS_SUS_HEAD_ACCOUNT", length=1, columnDefinition="CHAR(1)")
	private String is_sus_head_account;
	
	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;
	
	/** 版本序號 **/
	@Column(name="VERSION", length=20, columnDefinition="VARCHAR(20)")
	private String version;

	public String getMainid() {
		return mainid;
	}

	public void setMainid(String mainid) {
		this.mainid = mainid;
	}

	public String getCntrno() {
		return cntrno;
	}

	public void setCntrno(String cntrno) {
		this.cntrno = cntrno;
	}

	public String getCustid() {
		return custid;
	}

	public void setCustid(String custid) {
		this.custid = custid;
	}

	public String getItem_a() {
		return item_a;
	}

	public void setItem_a(String item_a) {
		this.item_a = item_a;
	}

	public String getItem_b() {
		return item_b;
	}

	public void setItem_b(String item_b) {
		this.item_b = item_b;
	}

	public String getItem_c() {
		return item_c;
	}

	public void setItem_c(String item_c) {
		this.item_c = item_c;
	}

	public String getItem_d() {
		return item_d;
	}

	public void setItem_d(String item_d) {
		this.item_d = item_d;
	}

	public String getItem_e() {
		return item_e;
	}

	public void setItem_e(String item_e) {
		this.item_e = item_e;
	}

	public String getItem_f() {
		return item_f;
	}

	public void setItem_f(String item_f) {
		this.item_f = item_f;
	}

	public String getItem_g() {
		return item_g;
	}

	public void setItem_g(String item_g) {
		this.item_g = item_g;
	}

	public String getItem_h() {
		return item_h;
	}

	public void setItem_h(String item_h) {
		this.item_h = item_h;
	}

	public String getItem_i() {
		return item_i;
	}

	public void setItem_i(String item_i) {
		this.item_i = item_i;
	}

	public String getItem_j() {
		return item_j;
	}

	public void setItem_j(String item_j) {
		this.item_j = item_j;
	}

	public String getItem_k() {
		return item_k;
	}

	public void setItem_k(String item_k) {
		this.item_k = item_k;
	}

	public String getItem_l() {
		return item_l;
	}

	public void setItem_l(String item_l) {
		this.item_l = item_l;
	}

	public String getItem_m() {
		return item_m;
	}

	public void setItem_m(String item_m) {
		this.item_m = item_m;
	}

	public String getItem_n() {
		return item_n;
	}

	public void setItem_n(String item_n) {
		this.item_n = item_n;
	}

	public String getItem_o() {
		return item_o;
	}

	public void setItem_o(String item_o) {
		this.item_o = item_o;
	}

	public String getItem_p() {
		return item_p;
	}

	public void setItem_p(String item_p) {
		this.item_p = item_p;
	}

	public String getItem_q() {
		return item_q;
	}

	public void setItem_q(String item_q) {
		this.item_q = item_q;
	}

	public String getItem_r() {
		return item_r;
	}

	public void setItem_r(String item_r) {
		this.item_r = item_r;
	}

	public String getItem_s() {
		return item_s;
	}

	public void setItem_s(String item_s) {
		this.item_s = item_s;
	}

	public String getItem_t() {
		return item_t;
	}

	public void setItem_t(String item_t) {
		this.item_t = item_t;
	}

	public String getItem_u() {
		return item_u;
	}

	public void setItem_u(String item_u) {
		this.item_u = item_u;
	}

	public String getItem_v() {
		return item_v;
	}

	public void setItem_v(String item_v) {
		this.item_v = item_v;
	}

	public String getItem_w() {
		return item_w;
	}

	public void setItem_w(String item_w) {
		this.item_w = item_w;
	}

	public String getItem_x() {
		return item_x;
	}

	public void setItem_x(String item_x) {
		this.item_x = item_x;
	}

	public String getItem_y() {
		return item_y;
	}

	public void setItem_y(String item_y) {
		this.item_y = item_y;
	}

	public String getItem_z() {
		return item_z;
	}

	public void setItem_z(String item_z) {
		this.item_z = item_z;
	}

	public String getItem_1() {
		return item_1;
	}

	public void setItem_1(String item_1) {
		this.item_1 = item_1;
	}

	public String getItem_2() {
		return item_2;
	}

	public void setItem_2(String item_2) {
		this.item_2 = item_2;
	}

	public String getItem_3() {
		return item_3;
	}

	public void setItem_3(String item_3) {
		this.item_3 = item_3;
	}

	public String getItem_4() {
		return item_4;
	}

	public void setItem_4(String item_4) {
		this.item_4 = item_4;
	}

	public String getIs_sus_head_account() {
		return is_sus_head_account;
	}

	public void setIs_sus_head_account(String is_sus_head_account) {
		this.is_sus_head_account = is_sus_head_account;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	
}