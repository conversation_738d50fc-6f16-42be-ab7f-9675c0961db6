package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.fms.panels.CLS9061FilterPanel;

@Controller
@RequestMapping(path = "/fms/cls9061v03")
public class CLS9061V03Page extends AbstractEloanInnerView {

	public CLS9061V03Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.已核准);
		//---
		addToButtonPanel(model, LmsButtonEnum.Filter);
		
		renderJsI18N(CLS9061V01Page.class);
		
		setupIPanel(new CLS9061FilterPanel(PANEL_ID), model, params);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/CLS9061V01Page.js" };
	}
}
