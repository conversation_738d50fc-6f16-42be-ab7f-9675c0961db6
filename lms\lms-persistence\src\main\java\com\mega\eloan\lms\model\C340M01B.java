 
package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 消金契約書關聯額度檔 **/
@NamedEntityGraph(name = "C340M01B-entity-graph", attributeNodes = { @NamedAttributeNode("c340m01a") })
@Entity
@Table(name="C340M01B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C340M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;


	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false) })
	private C340M01A c340m01a;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 額度mainId **/
	@Column(name="TABMAINID", length=32, columnDefinition="CHAR(32)")
	private String tabMainId;
	
	/** 額度序號 **/
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;
	
	public String getOid() {
		return this.oid;
	}
	public void setOid(String value) {
		this.oid = value;
	}

	public String getMainId() {
		return this.mainId;
	}
	public void setMainId(String value) {
		this.mainId = value;
	}
	
	public String getTabMainId() {
		return tabMainId;
	}
	public void setTabMainId(String tabMainId) {
		this.tabMainId = tabMainId;
	}
	
	public String getCntrNo() {
		return cntrNo;
	}
	public void setCntrNo(String cntrNo) {
		this.cntrNo = cntrNo;
	}

	public C340M01A getC340m01a() {
		return c340m01a;
	}

	public void setC340m01a(C340M01A c340m01a) {
		this.c340m01a = c340m01a;
	}
}