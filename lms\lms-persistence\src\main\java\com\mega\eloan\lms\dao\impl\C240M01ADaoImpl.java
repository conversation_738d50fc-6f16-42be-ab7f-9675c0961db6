/* 
 * C240M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.C240M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/** 覆審工作底稿主檔 **/
@Repository
public class C240M01ADaoImpl extends LMSJpaDao<C240M01A, String> implements
		C240M01ADao {

	@Override
	public C240M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C240M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C240M01A> findByDocStatus(String docStatus) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		List<C240M01A> list = createQuery(C240M01A.class, search)
				.getResultList();
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findMaxbatchNO() {
		Date dataDateMin = Util.parseDate(TWNDate.toAD(new Date()).substring(0,
				4)
				+ "-01-01");
		Date dataDateMax = Util.parseDate(Integer.toString((Util
				.parseInt(TWNDate.toAD(new Date()).substring(0, 4)) + 1))
				+ "-12-31");
		Query query = getEntityManager().createNamedQuery(
				"C240M01A.selMaxbatchNO");
		query.setParameter("dataDateMin", dataDateMin); // 設置參數
		query.setParameter("dataDateMax", dataDateMax);

		return query.getResultList();
	}

	@Override
	public List<C240M01A> findByBranchAndDataDate(String branchId, Date dataDate) {
		ISearch search = createSearchTemplete();
		MegaSSOUserDetails userId = MegaSSOSecurityContext.getUserDetails();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				userId.getUnitNo());
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dataEndDate",
				dataDate);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		List<C240M01A> list = createQuery(C240M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<C240M01A> findMaxDataDate(String branch, Date retrialDate) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branch);
		search.addSearchModeParameters(SearchMode.EQUALS,
				"expectedRetrialDate", retrialDate);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		return find(search);
	}

	/**
	 * J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
	 * 
	 * @param branch
	 * @param rpaKey
	 * @return
	 */
	@Override
	public List<C240M01A> findByBranchIdAndLikeMainId(String branch,
			String likeMainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branch);
		search.addSearchModeParameters(SearchMode.LIKE, "mainId", "%"
				+ likeMainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		return find(search);
	}

	@Override
	public List<C240M01A> findByDefaultCTLDate(String docStatus,
			String bgnDate, String endDate) {
		ISearch search = createSearchTemplete();

		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);

		String[] reasonStr = { bgnDate, endDate };
		search.addSearchModeParameters(SearchMode.BETWEEN, "expectedRetrialDate",
				reasonStr);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		return find(search);
	}

}