/* 
 * MisELF500ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF500;
import com.mega.eloan.lms.mfaloan.service.MisELF500Service;

/**
 * <pre>
 * 消金額度介面檔 MIS.ELF500
 * </pre>
 * 
 * @since 2013/1/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/17,REX,new
 *          </ul>
 */
@Service
public class MisELF500ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF500Service {

	@Override
	public List<ELF500> findByCustId_tmeStamp_1year(String custId, String dupNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF500_findByCustIdNoOverYear", new Object[] { custId, dupNo });

		List<ELF500> list = new ArrayList<ELF500>();
		for (Map<String, Object> row : rowData) {
			ELF500 model = new ELF500();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public ELF500 findByCustIdAndCntrno_tmeStamp_1year(String custId, String dupNo,
			String cntrNo) {
		Map<String, Object> rowData = this.getJdbc().queryForMap(
				"MIS.ELF500_findByCustIdAndCntrNoNoOverYear",
				new Object[] { custId, dupNo, cntrNo });
		
		if (rowData == null) {
			return null;
		} else {
			ELF500 model = new ELF500();
			DataParse.map2Bean(rowData, model);
			return model;
		}
	}
	
	@Override
	public ELF500 findByCustIdAndCntrno1(String custId, String dupNo,
			String cntrNo) {
		Map<String, Object> rowData = this.getJdbc().queryForMap(
		"MIS.ELF500_findByCustIdAndCntrNo",
		new Object[] { custId, dupNo, cntrNo });
		
		if (rowData == null) {
			return null;
		} else {
			ELF500 model = new ELF500();
			DataParse.map2Bean(rowData, model);
			return model;
		}
	}
	
	@Override
	public ELF500 findByCntrNo(String cntrNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListByCustParam("select"
				, new Object[] { "ELF500", "elf500_cntrno=?  " }
				, new Object[] {cntrNo});		
		List<ELF500> list = toELF500(rowData);
		
		if(list.size()==1){			
			return list.get(0);
		}else{
			return null;
		}
	}
	
	@Override
	public List<ELF500> findByCustIdALL(String custId, String dupNo) {

		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF500_findByCustId", new Object[] { custId, dupNo });
		List<ELF500> list = new ArrayList<ELF500>();
		for (Map<String, Object> row : rowData) {
			ELF500 model = new ELF500();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
	
	@Override
	public List<Map<String, Object>> findNeedUpdateFincomeData(String eloanTime) {
		return this.getJdbc().queryForList("MIS.ELF500.findNeedUpdateFincomeData",
				new String[] { eloanTime }, 0, Integer.MAX_VALUE);
	}
	
	@Override
	public void updateFincomeByCntrNo(int fincome, String cntrNo) {
		this.getJdbc().update("MIS.ELF500.updateFincomeByCntrNo",
				new Object[] { fincome, cntrNo });
	}

	private List<ELF500> toELF500(List<Map<String, Object>> rowData){
		List<ELF500> list = new ArrayList<ELF500>();
		for (Map<String, Object> row : rowData) {
			ELF500 model = new ELF500();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
}
