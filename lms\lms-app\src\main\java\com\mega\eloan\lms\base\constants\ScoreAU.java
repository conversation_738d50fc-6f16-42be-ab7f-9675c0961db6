package com.mega.eloan.lms.base.constants;


/**
 * <pre>
 * 澳洲評等表
 * </pre>
 * 
 * @since 2015/08/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/08/11,EL08034,new
 *          </ul>
 */
public interface ScoreAU {
	
	//-----房貸-----
	static final String 設定檔_House_AU_V1_0 = "cls/scoreAU.properties";
	static final String 設定檔_House_AU_V2_0 = "cls/scoreAU_V2_0.properties";
	static final String 設定檔_House_AU_V3_0 = "cls/scoreAU_V3_0.properties";
	
	//-----非房貸-----
	static final String 設定檔_notHouse_AU_V1_0 = "cls/scoreAU.properties";
	static final String 設定檔_notHouse_AU_V2_0 = "cls/scoreAU_V2_0.properties";
	static final String 設定檔_notHouse_AU_V3_0 = "cls/scoreNotHouseLoanAU_V3_0.properties";
	

	static final String Comma = ",";
	static final String Point = ".";
	static final String Semicolon = ";";

	static final String 分數 = "score";
	static final String 公式 = "formula";
	static final String 欄位 = "columns";

	interface column {
		
		String 評等建立日期 = "grdCDate";
		String 評等調整日期 = "grdTDate";
		String 完成最終評等日期 = "ratingDate";		
		String VEDA查詢日期 = "vedaQDate";
		String 模型版本 = "varVer";		
		String 初始評等 = "pRating";
		String 獨立評等 = "sRating";
		String 支援評等 = "sprtRating";
		String 調整評等 = "adjRating";
		String 最終評等 = "fRating";
		String 原始最終評等 = "orgFr";
		
		String 升降等_依風險點數 = "adj_pts";
		String 升降等_依特殊警訊	= "adj_sw";
		String 升降等_依其他資訊	= "adj_oi";

		
		String 註記不調整 = "noAdj";
		String 調整狀態 = "adjustStatus";
		String 調整註記 = "adjustFlag";
		String 調整理由 = "adjustReason";
		
		//因子的原始資料
		String 出生日M1 = "raw_m1";		
		String 年薪幣別 = "raw_payCurr";
		String 其它收入幣別 = "raw_otherCurr";		
		String 家庭所得幣別 = "raw_hincomeCurr";
		String 家庭所得金額P3 = "raw_hincomeAmt";
		String 本次新做案下不動產租金收入幣別 = "raw_rincomeCurr";
		String 財富管理_本行幣別 = "raw_invMBalCurr";
		String 財富管理_它行幣別 = "raw_invOBalCurr";
		String 金融機構存款往來情形幣別 = "raw_branAmtCurr";		
		String 轉換匯率_年薪 = "exRate_pay";
		String 轉換匯率_其他收入 = "exRate_oth";
		String 轉換匯率_家庭所得 = "exRate_hincome";
		String 轉換匯率_本次新做案下不動產租金收入 = "exRate_rincome";
		String 轉換匯率_財富管理本行  = "exRate_invMBal";
		String 轉換匯率_財富管理它行  = "exRate_invOBal";
		String 轉換匯率_金融機構存款往來情形 = "exRate_branAmt";
		
		String 月份數A5 = "raw_a5";
		
		//負面資訊
		String AU一般警訊1 = "chkItemAUG1";
		String AU一般警訊2 = "chkItemAUG2";
		String AU一般警訊3 = "chkItemAUG3";
		String AU特殊警訊1 = "chkItemAUS1";
		String AU特殊警訊2 = "chkItemAUS2";
		String AU其他資訊1 = "chkItemAUO1";
		String 累加風險點數 = "sumRiskPt";
		
		//因子
		String 因子M1_年齡 = "item_m1";
		String 因子M5_職業 = "item_m5";
		String 因子M7_年資 = "item_m7";
		String 因子D1_ICR註記 = "item_d1_na";
		String 因子D1_ICR值 = "item_d1_icr";
		String 因子P3_夫妻年收入_幣別 = "item_p3_curr";
		String 因子P3_夫妻年收入 = "item_p3";
		String 因子A5_契約年限 = "item_a5";
		String 因子O1_VEDASCORE = "item_o1";
		String 因子Z1 = "item_z1";		
		String 因子Z2 = "item_z2";
		//3.0新增因子
		String 因子edu = "item_edu";
		String 因子drate = "item_drate";
		
		//標準化因子
		String 標準化M1 = "std_m1";
		String 標準化M5 = "std_m5";
		String 標準化M7 = "std_m7";
		String 標準化D1 = "std_d1";
		String 標準化P3 = "std_p3";
		String 標準化A5 = "std_a5";
		String 標準化O1 = "std_o1";
		String 標準化Z1 = "std_z1";		
		String 標準化Z2 = "std_z2";
		//加權因子
		String 加權M1 = "weight_m1";
		String 加權M5 = "weight_m5";
		String 加權M7 = "weight_m7";
		String 加權D1 = "weight_d1";
		String 加權P3 = "weight_p3";		
		String 加權A5 = "weight_a5";
		String 加權O1 = "weight_o1";
		String 加權Z1 = "weight_z1";		
		String 加權Z2 = "weight_z2";
		//3.0新增因子
		String 加權edu = "weight_edu";
		String 加權drate = "weight_drate";
		
		//權重分數(3.0新增)
		String 權重分數M1 = "weight_scr_m1";
		String 權重分數M5 = "weight_scr_m5";
		String 權重分數M7 = "weight_scr_m7";
		String 權重分數P3 = "weight_scr_p3";
		String 權重分數A5 = "weight_scr_a5";
		String 權重分數Z1 = "weight_scr_z1";		
		String 權重分數Z2 = "weight_scr_z2";
		String 權重分數edu = "weight_scr_edu";
		String 權重分數drate = "weight_scr_drate";
		
		String 合計WeightedScore = "scr_core";
		String 核心模型分數 = "std_core";
		
		//3.0版新增
		String 預測壞率 = "pd";
		String 截距 = "interCept";
		String 斜率 = "slope";
	}

	interface type {
		String 澳洲消金模型基本 = "auBase";
		String 澳洲消金模型評等 = "auGrade";
		String 澳洲消金模型違約機率 = "auDR";
	}
	
	/**
	 * <pre>
	 * 消金模型基本
	 * 名稱要和 score*.properties 中的設定相同 ，由 interfaceName+"."+fieldName
	 * </pre>
	 */
	interface auBase {
		String 年齡_M1= "scr_m1";		
		String 職業_M5= "scr_m5";
		String 年資_M7= "scr_m7"; 
		String ICR_D1 = "scr_d1";
		String 夫妻年收入_P3 = "scr_p3";
		String 契約年限_A5 = "scr_a5";
		String VEDASCORE_O1 = "scr_o1";
		String 擔保品地點及種類_Z1 = "scr_z1";
		String 市場環境及變現性_Z2 = "scr_z2";
	}
	
	interface auBase_V3_0 {
		String 年齡_M1= "scr_m1";		
		String 職業_M5= "scr_m5";
		String 年資_M7= "scr_m7"; 
		String 契約年限_A5 = "scr_a5";
		String 擔保品地點及種類_Z1 = "scr_z1";
		String 市場環境及變現性_Z2 = "scr_z2";
		String 教育程度 = "scr_edu";
		String 個人負債比率 = "scr_drate";
	}
	interface auBase_V3_0_P3_AU {
		String 夫妻年收入_P3_AU = "scr_p3";
	}
	interface auBase_V3_0_P3_CA {
		String 夫妻年收入_P3_CA = "scr_p3";
	}
	interface auBase_V3_0_P3_FR {
		String 夫妻年收入_P3_FR = "scr_p3";
	}
	

	/**
	 * <pre>
	 * 消金模型評等等級項目
	 * </pre>
	 */
	interface auGrade {
		String 等級 = "level";
	}	

	/**
	 * <pre>
	 * 消金模型違約機率
	 * </pre>
	 */
	interface auDR {
		String 違約機率_預估3年期 = "dr_3yr";
		String 違約機率_預估1年期 = "dr_1yr";
	}
	
	/**
	 * <pre>
	 * 亞洲以外其他區消金模型違約機率_V2.0
	 * </pre>
	 */
	interface auDR_V3_0 {
		String 違約機率_預估1年期 = "dr_1yr";
	}
}
