var modfixBox = {
    isInit: false,
    formId: "#editBoxForm",
    /**
     *初始化
     * @param {Object} $form 表單物件
     *  @param {Object} obj前端資訊
     */
    init: function($form, obj){
        if (!this.isInit) {
            var codeTypeMap = API.loadCombos(["cls1141_docCode"]);
            var itemObj = codeTypeMap.cls1141_docCode;
            
            //當為團貸案 不能變更案件
            if (obj.docCode == "4" || obj.docCode == "5") {
                for (var key in itemObj) {
                    if (key != obj.docCode) {
                        delete itemObj[key];
                    }
                }
                $("#changeAuthLvl").removeOptions(["1", "2", "4"]);
            } else {
                delete itemObj["4"];
                delete itemObj["5"];
            }
            //當沒有母行移除母行授權內的選項
            if (!obj.parentBrNo) {
                $("#changeAuthLvl").removeOptions(["2", "4"]);
            }
            if (util.isSpectialBank(obj.caseBrid)) {
                $("#changeAuthLvl").removeOptions(["2", "3", "4"]);
            }
            
            
            
            $("#changeDocCode").setItems({
                item: itemObj,
                format: "{key}",
                size: "1"
            });
            this.isInit = true;
        } else {
            $form.find("#changeArea").hide();
            $form.reset();
        }
        $("[name=changeDocCode][value=" + obj.docCode + "]").attr("checked", "checked");
        $("[name=changeNgFlag][value=" + obj.ngFlag + "]").attr("checked", "checked");
        $("#changeAuthLvl").val(obj.authLvl).trigger("change");
        
        
    },
    L140S02HGrid: null,
    /**
     * 初始化grid
     */
    initGrid: function(){
        this.L140M01RGrid = $("#L140M01RGrid").iGrid({
            handler: _M.ghandle,
            height: 230,
            rownumbers: true,
            multiselect: true,
            hideMultiselect: false,
            rowNum: 10,
            postData: {
                formAction: "queryL140M01R",
                tabFormMainId: _M.tabMainId,
                L140S02ASeq: L140S02Action.L140M01RSeq
            },
            colModel: [{
                colHeader: i18n.cls1151s01["L140M01R.branchNo"],//分行代號,
                name: 'branchNo',
                align: "left",
                width: 60,
                sortable: true,
                formatter: "click",
                onclick: L140S02HAction.openL140M01R
            }, {
                colHeader: i18n.cls1151s01["L140M01R.branchName"],//分行名稱,
                name: 'branchName',
                align: "left",
                width: 60,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L140M01R.subAmt"],//代償金額,
                name: 'subAmt',
                align: "right",
                width: 60,
                sortable: true,
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    decimalPlaces: 0
                }
            }, {
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = L140S02HAction.L140M01RGrid.getRowData(rowid);
                L140S02HAction.openL140M01R(null, null, data);
            }
        });
    },
    /**
     *修改案件視窗
     * @param {Object} obj前端資訊
     */
    openBox: function(obj){
        var $form = $(this.formId);
        this.init($form, obj);
        
        $("#editBox").thickbox({
            title: i18n.def.confirmTitle,
            width: 500,
            height: 300,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($form.valid()) {
                        var $packLoanTr = $("#packLoanTr");
                        var $docCode = $form.find("[name=changeDocCode]:checked");
                        var docCode = $docCode.val();
                        var authLvl = $form.find("#changeAuthLvl").val();
                        var areaBrid = $form.find("#changeArea").val();
                        var changeNgFlag = $form.find("[name=changeNgFlag]:checked").val();
                        $packLoanTr.hide();
                        //當案件性質為一般才顯示整批團貸的選項
                        if (docCode == "1") {
                            $packLoanTr.show();
                        }
                        obj.docCode = docCode;
                        obj.authLvl = authLvl;
                        obj.ngFlag = changeNgFlag;
                        $("#ngFlag").val(changeNgFlag);
                        $("#docCode").val(docCode);
                        $("#docCodeShow").val($docCode.parent("label").text());
                        $("#authLvl").val(authLvl).trigger("change");
                        $("#areaBrId").val(areaBrid);
                        $.ajax({
                            handler: CLSAction.fhandle,
                            formId: CLSAction.mainFormId,
                            action: "modifCaseType",
                            data: {
                                docCode: docCode,
                                authLvl: authLvl,
                                areaBrid: areaBrid,
                                mainOid: $("#mainOid").val(),
                                changeNgFlag: changeNgFlag
                            },
                            success: function(obj){
                                CLSAction.controlBook();
                                
                                if (obj.showTitle) {
                                	// 修正Client DOM Stored XSS
                                	// $("#showTitle").html(obj.showTitle);
                                	var showTitle = $("#showTitle");
                                	showTitle.injectData(obj);
                                }
                                if (docCode == "1") {
                                    $("#book98").show();
                                }
                                // 修正Client DOM Stored XSS
                                // $("#caseLvl").html(obj.caseLvl);
                                var caseLvl = $("#caseLvl");
                                caseLvl.injectData(obj);
                                $.thickbox.close();
                            }
                        });
                        
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
};

/**
 * 團貸案相關問題
 */
var ParentAction = {
    ParentGrid: null,
    openbox: function(){
        if (this.ParentGrid) {
            this.ParentGrid.setGridParam({
                postData: {
                    custId: $("#parentId").val(),
                    dupNo: $("#parentDupNo").val()
                },
                search: true
            }).trigger("reloadGrid");
        } else {
            this.ParentGrid = $("#ParentGrid").iGrid({
                handler: CLSAction.ghandle,
                rowNum: 10,
                postData: {
                    formAction: "PTEAMAPPQuery",
                    custId: $("#parentId").val(),
                    dupNo: $("#parentDupNo").val()
                },
                rowNum: 10,
                autowidth: true,
                colModel: [{
                    colHeader: i18n.cls1141m01["CLS1141.037"],//"團貸總戶名稱(建案名)",
                    name: 'BUILDNAME',
                    align: "left",
                    width: 110,
                    hidden: true,
                    sortable: false
                }, {
                    colHeader: i18n.cls1141m01["CLS1141.037"],//"總額度申請年度",
                    name: 'YEAR',
                    align: "left",
                    width: 110,
                    sortable: false
                }, {
                    colHeader: i18n.cls1141m01["CLS1141.038"],//"簽案行",
                    name: 'ISSUEBRNO',
                    width: 160,
                    sortable: false,
                    align: "left"
                }, {
                    colHeader: i18n.cls1141m01["CLS1141.039"],//"總戶名稱",
                    width: 140,
                    name: 'PROJECTNM',
                    align: "left",
                    sortable: true
                }, {
                    colHeader: i18n.cls1141m01["CLS1141.040"],//"總戶序號",
                    width: 140,
                    name: 'GRPCNTRNO',
                    sortable: true
                }, {
                    colHeader: i18n.cls1141m01["CLS1141.041"],//"批　　號",
                    width: 140,
                    name: 'AMTAPPNO',
                    sortable: true
                }]
            });
        }
        $("#ParentBox").thickbox({
            title: i18n.cls1141m01["CLS1141.042"],
            width: 800,
            height: 300,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $grid = ParentAction.ParentGrid;
                    var data = $grid.getSingleData();
                    if (data) {
                        $.ajax({
                            handler: CLSAction.fhandle,
                            formId: "empty",
                            action: "saveL120M01G",
                            data: {
                                data: JSON.stringify(data),
                                custId: $("#parentId").val(),
                                dupNo: $("#parentDupNo").val()
                            },
                            success: function(obj){
                                $.thickbox.close();
                                var $form = $("#" + CLSAction.mainFormId);
                                $form.injectData(obj);
                            }
                        });
                    }
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
};
initDfd.done(function(obj){
    var $CLSForm = $("#CLSForm");
    //是否有總行
    if (!obj.hasAdmin) {
        $CLSForm.find(".hasAdmin1").css("border", "0");
        $CLSForm.find(".hasAdmin2").hide();
    }
    $CLSForm.find("." + obj.hideboss).show();
	
	
	var isOpenFun;
	$.ajax({
        handler: 'lmscommonformhandler',
        action: "isOpenPaperlessSigningFunction",
		async:false,
        data: {
        },
        success: function(obj){
			isOpenFun = obj.isOpenPaperlessSigningFunction;
        }
	});
	
	if(isOpenFun){
		
		if(obj.is_L120M01A_contains_brmpJsonData=="Y" && obj.simplifyFlag == 'E'){

	    }else{
	    	$CLSForm.find("#simplifyFlag").removeOptions(["E"]);
	    }
	}
	else{
		
		if(obj.is_L120M01A_contains_brmpJsonData=="Y"){
    	
	    }else{
	    	$CLSForm.find("#simplifyFlag").removeOptions(["E"]);
	    }
	}
    
    /**檢查退補件是否呈現*/
    if (obj.returnBHDate) {
        $("#returnBH").show();
    } else {
        $("#returnBH").hide();
    }
    
    /** 無「營運中心放行時間」資料時須隱藏*/
    if (obj.areaSendInfo || obj.rptTitleArea1) {
        $("#divAreaTime").show();
    } else {
        $("#divAreaTime").hide();
    }
    
    /**檢查授審會/催收會會期 非授權外案件時須隱藏*/
    if (obj.docKind == "2") {
        $("#dockind2Show").show();
    } else {
        $("#dockind2Show").hide();
    }
    
    var $packLoanTr = $("#packLoanTr");
    $packLoanTr.hide();
    if (obj.docCode == "1") {
        $packLoanTr.show();
        
    }
    if (obj.packLoan == "Y") {
        $("#packLoanTb").show();
    }
    
    /**檢查報送授權外審核之主要理由 是否呈現*/
    var $caseLvlReasonTr = $("#caseLvlReasonTr");
    if (obj.authLvl == "5" || obj.authLvl == "3") {
        $caseLvlReasonTr.show();
    } else {
        $caseLvlReasonTr.hide();
    }
    var $areaBridSelect = $(".areaBridSelect");
    var $areaBrId = $("#areaBridSelect");
    $areaBridSelect.setItems({
        item: obj.areaData,
        value: obj.areaBrId || obj.brnGroup || "",
        format: "{value} {key}",
        space: false
    });
    $areaBridSelect.change(function(){
        $("#areaBrId").val($(this).val());
    });
    if (obj.brnGroup) {
        $(".areaBridSelect").val(obj.brnGroup);
    }
    var $areaChkDiv = $("#areaChkDiv");
    if (obj.authLvl == "5") {
        $areaChkDiv.show();
    }
    
    
    $CLSForm.find("[name=areaChk]").click(function(i){
    	
        if ($(this).val() == "2" || $(this).val() == "4") {
            if (!lmsM01Json.isSpectialBank()) {
                //$CLSForm.find("#divArea1").show().find("#areaBrId1").removeAttr("disabled");
            }
            $CLSForm.find("#divArea2").hide();
            
            //J-108-0316_10702_B1001 Web e-Loan調整國外部、國際金融業務分行與金控總部分行等原總處營業單位會簽流程
        	if ($(this).val() == "2"){
        		
        		//消金處簽案流程調整回原會簽流程
        		if (lmsM01Json.isSpectialBranchOnlyForChkFlow()) {
        			CommonAPI.showErrorMessage("原「送會簽」流程已調整為「送審查(授審處處長權限外)」流程替代，系統自動替換為「送審查(授審處處長權限外)」。");
        			$CLSForm.find("[name='areaChk'][value='5']:radio").attr("checked" , "checked" );
            		
            	    /*
            		CommonAPI.confirmMessage("自109年01月起，原「送會簽」流程應調整為「送審查(授審處處長權限外)」流程替代，是否替換為「送審查(授審處處長權限外)」?", function(b){
    				    if (b) {
    				    	$("#LMS1205S01Form").find("[name='areaChk'][value='5']:radio").attr("checked" , "checked" );
    					}			
    				});
    				*/
        		}
        		
        	}
        } else if ($(this).val() == "5" || $(this).val() == "6") {
        	//J-108-0316_10702_B1001 Web e-Loan調整國外部、國際金融業務分行與金控總部分行等原總處營業單位會簽流程
            if (!lmsM01Json.isSpectialBank()) {
            	$CLSForm.find("#divArea1").show();
            }
            $CLSForm.find("#divArea2").hide();

			//把額度明細表的狀態變成編製中
			$.ajax({
    			type : "POST",
    			async: false,
    			handler : "lms1101formhandler",
    			data : 
    			{
    				formAction : "chgL140m01aNotApprove",
    				mainId : responseJSON.mainId 
    			},
    			success:function(responseData){
    				$CLSForm.find("#divArea3").show();
    			},
    	        error: function(){
    	        	$CLSForm.find("[name=authLvl]").val(authLvlPreVal);
    	        	$CLSForm.find("#divArea3").hide();
                }
			});   	 
        } else if ($(this).val() == "3") {
            $CLSForm.find("#divArea1").hide();
            $CLSForm.find("#divArea2").show().find("#areaBrId2").removeAttr("disabled");
        } else {
            $CLSForm.find("#divArea1").hide();
            $CLSForm.find("#divArea2").hide();
        }
    });
    
    var $divArea = $("#divArea1,#divArea2");
    /**授權等級 切換*/
    $("#authLvl").change(function(){
        var $this = $(this);
        var value = $(this).val();
        $caseLvlReasonTr.hide();
        $areaChkDiv.hide();
        if (value == "5" || value == "3") {
            $caseLvlReasonTr.show().find("#caseLvlReason").val("").end().find("#caseLvlReasonSpan").hide();
        }
        
        if (value == "5") {
            //當為授權外隱藏AuthLvl的選項
            $this.hide();
            /**檢查授審會/催收會會期 非授權外案件時須隱藏*/
            $("#dockind2Show").show();
            $areaChkDiv.show();
            //當非特殊分行預設都是審查中
            var $areaChk = $areaChkDiv.find("[name=areaChk]")
            if (!lmsM01Json.isSpectialBank()) {
                $areaChk.attr("disabled", "disabled");
                $("[name=areaChk][value=3]").attr("checked", "checked").trigger("click");
            } else {
                $areaChk.removeAttr("disabled");
                $divArea.hide();
                if (responseJSON.docStatus == "01O") {
                    //顯示登錄按鈕
                    $("#btnSLogIN,#hideSpecialBtn").show();
                }
            }
        } else {
            //隱藏登錄按鈕
            $("#btnSLogIN").hide();
            
            /**檢查授審會/催收會會期 非授權外案件時須隱藏*/
            $("#dockind2Show").hide();
            $this.show();
        }
        
        
        if (value == "3") {
            $areaBrId.show();
        } else {
            $areaBrId.hide().val("");
        }
    });
    $("#authLvl").val(obj.authLvl);
    $("#authLvl").trigger("change");
    
    var $changeArea = $("#changeArea");
    $changeArea.setItems({
        item: obj.areaData,
        format: "{value} {key}",
        space: false
    });
    $("#changeAuthLvl").change(function(){
        if ($(this).val() == "3") {
            $changeArea.show();
        } else {
            $changeArea.hide().val("");
        }
    });
    
    
    
    /**報送授權外審核之主要理由 切換*/
    var $caseLvlReasonSpan = $("#caseLvlReasonSpan");
    var $caseLvlReason = $("#caseLvlReason");
    $caseLvlReason.change(function(){
        if ($(this).val() == "7") {
            $caseLvlReasonSpan.show();
        } else {
            $caseLvlReasonSpan.hide();
        }
    });
    $caseLvlReason.val(obj.caseLvlReason)
    $caseLvlReason.trigger("change");
    
    /**修改案件性質按鈕*/
    $("#modifyBt").click(function(){
        modfixBox.openBox(obj);
    });
    //當為異常通報隱藏按鈕
    if (obj.docCode == "4") {
        $("#modifyBt").hide();
    }
    // 控制總處分行會簽後修改送會簽與送會簽審查選項顯示隱藏
    // Miller added at 2013/05/02
    if (responseJSON.areaChk == "4" && responseJSON.docStatus != "07O") {
        $CLSForm.find("#areaChk4").show();
        $CLSForm.find("#areaChk2").hide();
    }else if (responseJSON.areaChk == "6" && responseJSON.docStatus != "07O") {
            $CLSForm.find("#areaChk6").show();
            $CLSForm.find("#areaChk5").hide();    
    } else {
        if (responseJSON.areaChk == "4" && responseJSON.docStatus == "07O") {
            // 當送會簽審查從授管處退回待補件時，將送會簽審查改成送會簽
            setTimeout(function(){
                $CLSForm.find("[name='areaChk']:radio:eq(1)").attr("checked", true);
            }, 500)
        }else if (responseJSON.areaChk == "6" && responseJSON.docStatus == "07O") {
            // 當送會簽審查從授管處退回待補件時，將送會簽審查改成送會簽
            setTimeout(function(){
                $CLSForm.find("[name='areaChk']:radio:eq(1)").attr("checked", true);
            }, 500)
        }
        
        $CLSForm.find("#areaChk2").show();
        $CLSForm.find("#areaChk4").hide();
        
        $CLSForm.find("#areaChk5").show();
        $CLSForm.find("#areaChk6").hide();
    }
    
    
    /**
     *是否整批貸款
     */
    var $packLoanTb = $("#packLoanTb");
    $("input[name=packLoan]").click(function(){
        if ($(this).val() == "Y") {
            $packLoanTb.show();
        } else {
            $packLoanTb.hide();
			$packLoanTb.find("span.field").html("");
			$packLoanTb.find("input").val("");
        }
    });
    
    /**
     *取得總戶資訊
     */
    $("#getParentInfoBt").click(function(){
        var $form = $("#" + CLSAction.mainFormId);
        $.ajax({
            handler: CLSAction.fhandle,
            action: "getParentInfo",
            data: {
                custId: $("#parentId").val(),
                dupNo: $("#parentDupNo").val()
            },
            success: function(obj){
                if (obj.msg) {
                    API.showMessage(obj.msg);
                } else {
                    if (obj.size == "1") {
                        $form.injectData(obj);
                    } else {
                        ParentAction.openbox();
                    }
                }
            }
        });
    });
	
	// ???
        $("#docStatusShow").dblclick(function(){
            $.ajax({
                handler: CLSAction.fhandle,
                formId: CLSAction.mainFormId,
                action: "modifCaseType",
                data: {
                    docCode: docCode,
                    authLvl: authLvl,
                    areaBrid: areaBrid,
                    mainOid: $("#mainOid").val(),
                    changeNgFlag: changeNgFlag
                },
                success: function(obj){
                    CLSAction.controlBook();
                    
                    if (obj.showTitle) {
                    	// 修正Client DOM Stored XSS
                    	// $("#showTitle").html(obj.showTitle);
                    	var showTitle = $("#showTitle");
                    	showTitle.injectData(obj);
                    }
                    if (docCode == "1") {
                        $("#book98").show();
                    }
                    // 修正修正Client DOM Stored XSS
                    // $("#caseLvl").html(obj.caseLvl);
                    var caseLvl = $("#caseLvl");
                    caseLvl.injectData(obj);
                    $.thickbox.close();
                }
             });
        });
	
        
	$('#simplifyFlag').change(function(){
        if (!$("#CLSForm").valid()) {
            $(this).val('');
            return;
        }
		
		var reminder = obj.simplifyFlag == 'E' 
					? i18n.cls1141m01['CLS1141.warn.message.switchPaperlessToOtherFlag'] 
					: i18n.cls1141m01['CLS1141.135'];
					
        //CLS1141.135=變更簡化簽報註記需儲存資料並更新畫面，<br/>是否繼續此動作?
		API.confirmMessage(reminder , function(result){
            if (result) {
				var dowork = function(){
		            $.form.submit({
		                url: location.href,
		                target: $("#mainOid").val(),
		                data: {
		                    txCode: responseJSON.txCode,
		                    mainId: $("#mainId").val(),
		                    mainOid: $("#mainOid").val(),
		                    mainDocStatus: $("#docStatus").val()
		                }
		            });
		        };
		        var save = $("#buttonPanel").find("#btnSave:visible");
		        save.size() &&
		        save.trigger('click', [{
		            success: dowork
		        }]);
            }
        });
    });
	
	//產生文件掃描封面條碼
	$("#btnPrintBarcode").click(function(){
		$.form.submit({
           url: "../../simple/FileProcessingService",
           target: "_blank",
           data: {
               'mainId': responseJSON.mainId,
               'fileDownloadName': "CLS1141BarcodeR01.pdf",
               serviceName: "cls1141barcoder01rptservice"
           }
       });
	});
});
