/* 
 * C241M01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C241M01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C241M01B;


/** 授信帳務資料檔 **/
@Repository
public class C241M01BDaoImpl extends LMSJpaDao<C241M01B, String> implements
		C241M01BDao {

	@Override
	public C241M01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C241M01B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		List<C241M01B> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(C241M01B.class, search).getResultList();
		}		
		return list;
	}

	@Override
	public List<C241M01B> findByMainIdByYLnDataDate(String mainId) {
		ISearch search = createSearchTemplete();
		List<C241M01B> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "lnDataDate",
				null);
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(C241M01B.class, search).getResultList();
		}
		return list;
	}

	@Override
	public C241M01B findByUniqueKey(String mainId, String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C241M01B> findByIndex01(String mainId, String custId,
			String dupNo) {
		ISearch search = createSearchTemplete();
		List<C241M01B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(C241M01B.class, search).getResultList();
		}
		return list;
	}

	@Override
	public void deleteByC240M01AMainid(String mainId) {
		Query query = getEntityManager().createNamedQuery(
				"C241M01B.delByC240m01aMainid");
		query.setParameter("mainId", mainId); // 設置參數
		query.executeUpdate();
	}

	@Override
	public List<C241M01B> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "quotaNo", CntrNo);
		search.addOrderBy("quotaNo");
		List<C241M01B> list = createQuery(C241M01B.class, search)
				.getResultList();

		return list;
	}
	@Override
	public List<C241M01B> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<C241M01B> list = createQuery(C241M01B.class,search).getResultList();
		return list;
	}
}