/* 
 * MicroEntServiceImpl.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.panels.LMSS23APanel;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.MicroEntService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01EDao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L120S04CDao;
import com.mega.eloan.lms.dao.L120S10ADao;
import com.mega.eloan.lms.dao.L120S10BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01SDao;
import com.mega.eloan.lms.dao.L160M01ADao;
import com.mega.eloan.lms.dao.L162S01ADao;
import com.mega.eloan.lms.dao.L164S01ADao;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S04C;
import com.mega.eloan.lms.model.L120S10A;
import com.mega.eloan.lms.model.L120S10B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L140M01S;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L164S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * BY 專案共用Service
 * </pre>
 * 
 * @since 2019/09
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/09,009301,new
 *          </ul>
 */
@Service("MicroEntService")
public class MicroEntServiceImpl extends AbstractCapService implements
		MicroEntService {

	protected final Logger logger = LoggerFactory.getLogger(getClass());

	@Resource
	LMSService lmsService;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	EjcicService ejcicService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L160M01ADao l160m01aDao;

	@Resource
	L120S01BDao l120s01bDao;

	@Resource
	L120S04CDao l120s04cDao;

	@Resource
	L120S10ADao l120s10aDao;

	@Resource
	L140M01SDao l140m01sDao;

	@Resource
	L162S01ADao l162s01aDao;

	@Resource
	L164S01ADao l164s01aDao;

	@Resource
	L120S10BDao l120s10bDao;

	@Resource
	L120M01EDao l120m01eDao;

	@Resource
	EloandbBASEService eloandbService;

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByMainId(Class clazz,
			String mainId) {
		if (clazz == L120M01A.class) {
			return (T) l120m01aDao.findByMainId(mainId);
		} else if (clazz == L140M01A.class) {
			return (T) l140m01aDao.findByMainId(mainId);
		} else if (clazz == L160M01A.class) {
			return (T) l160m01aDao.findByMainId(mainId);
		} else if (clazz == L120S10B.class) {
			return (T) l120s10bDao.findByUniqueKey(mainId);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes" })
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L120S01B.class) {
			return l120s01bDao.findByMainId(mainId);
		} else if (clazz == L162S01A.class) {
			return l162s01aDao.findByMainId(mainId);
		} else if (clazz == L120S10A.class) {
			return l120s10aDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public L120S10A findL120s10aByOid(String oid) {
		return l120s10aDao.findByOid(oid);
	}

	@Override
	public List<L120S10A> findL120s10aByMainId(String mainId) {
		return l120s10aDao.findByMainId(mainId);
	}

	@Override
	public void deleteListL120s10a(List<L120S10A> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S10A model : list) {
			listOid.add(model.getOid());
		}
		l120s10aDao.delete(list);
	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType) {
		return l140m01aDao.findL140m01aListByL120m01cMainId(mainId, caseType,
				null);
	}

	@Override
	public L120S01B findL120s01bByUniqueKey(String mainId, String custId,
			String dupNo) {
		return l120s01bDao.findByUniqueKey(mainId, custId, dupNo);
	}

	@Override
	public void reSetL120S10A(String mainId, String custId, String dupNo,
			String custName, String newRelation) {

		List<L120S10A> l120s10as = null;
		custName = Util.toSemiCharString(Util.trim(custName));

		if (Util.equals(Util.trim(custId), "")
				&& Util.equals(Util.trim(dupNo), "")
				&& Util.equals(Util.trim(custName), "")) {
			return;
		}

		if (Util.notEquals(custId, "") && Util.notEquals(dupNo, "")) {
			// BY ID
			l120s10as = this.findListL120s10aByCustId(mainId, custId, dupNo);
		} else {
			// BY CUSTNAME
			if (Util.notEquals(custName, "")) {
				l120s10as = this.findListL120s10aByCustName(mainId, custName);
			} else {
				l120s10as = null;
			}
		}

		if (l120s10as != null && !l120s10as.isEmpty()) {
			for (L120S10A l120s10a : l120s10as) {
				String custRelation = Util.trim(l120s10a.getCustRelation());

				String[] item = custRelation.split(",");
				List<String> asList = Arrays.asList(item);

				String[] newRelationItem = newRelation.split(",");
				for (int n = newRelationItem.length - 1; n >= 0; n = n - 1) {
					String relation = newRelationItem[n];

					if (asList.contains(relation)) {
						continue;
					} else {
						if (Util.equals(custRelation, "")) {
							custRelation = relation;
						} else {
							custRelation = custRelation + "," + relation;
						}
					}

					String[] newItem = custRelation.split(",");

					int i, j;
					String tmp;
					for (i = newItem.length - 1; i >= 0; i = i - 1) {
						for (j = 0; j < i; j = j + 1) {
							// 換（"小於"是由大到小）
							if (Util.parseInt((String) newItem[j]) > Util
									.parseInt((String) newItem[i])) {
								tmp = newItem[j];
								newItem[j] = newItem[i];
								newItem[i] = tmp;
							}
						}
					}

					StringBuffer itemBuf = new StringBuffer("");
					for (String tItem : newItem) {
						itemBuf.append(Util.equals(itemBuf, "") ? "" : ",");
						itemBuf.append(tItem);
					}

					l120s10a.setCustRelation(itemBuf.toString());

					if (Util.equals(l120s10a.getCustId(), "")
							&& Util.equals(l120s10a.getDupNo(), "")) {
						if (Util.notEquals(custId, "")
								&& Util.notEquals(dupNo, "")) {
							l120s10a.setCustId(Util.trim(custId));
							l120s10a.setDupNo(Util.trim(dupNo));
						}
					}

					lmsService.save(l120s10a);
				}
			}
		} else {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			L120S10A l120s10a = new L120S10A();
			l120s10a.setMainId(mainId);
			l120s10a.setCustId(custId);
			l120s10a.setDupNo(dupNo);
			l120s10a.setCustName(custName); // 已經轉半形
			l120s10a.setCustRelation(newRelation);
			l120s10a.setCreateTime(CapDate.getCurrentTimestamp());
			l120s10a.setCreator(user.getUserId());

			lmsService.save(l120s10a);
		}

	}

	@Override
	public List<L120S10A> findListL120s10aByCustId(String mainId,
			String custId, String dupNo) {
		return l120s10aDao.findByMainIdAndCustIdDupNo(mainId, custId, dupNo);
	}

	@Override
	public List<L120S10A> findListL120s10aByCustName(String mainId,
			String custName) {
		return l120s10aDao.findByMainIdAndCustName(mainId, custName);
	}

	@Override
	public L164S01A findL164s01aByUniqueKey(String mainId, String custId,
			String dupNo) {
		return l164s01aDao.findByUniqueKey(mainId, custId, dupNo);
	}

	@Override
	public L120S10A importJ10(L120S10A l120s10a,
			TreeMap<String, String> J10_BREACH_MAP,
			TreeMap<String, String> J10_PERCENTILE_MAP,
			List<Map<String, Object>> cesJ10List) {
		if (l120s10a != null && Util.isNotEmpty(l120s10a)) {
			String xCustId = l120s10a.getCustId();

			Map<String, Object> kcs003_map = null;
			if (MapUtils.isEmpty(kcs003_map)) {
				List<Map<String, Object>> kcs003_list = ejcicService
						.getKCS003_data_ordByQdateDesc(xCustId);
				if (kcs003_list.size() > 0) {
					kcs003_map = kcs003_list.get(0);
				}
			}
			String j10score = "";
			String j10Date = "";

			// 小規模營業人簽報書
			// JCIC 沒資料，可能已經超過15天
			// 改抓資信簡表--FOR 小規模 有銀行簡易評等

			// 相關文件 借款人資信簡表連結如果有的話
			// 開始引進名單開始

			boolean findCesData = false;
			String j10Percentile1 = "";
			String j10JCICscore1 = "";
			String j10Date1 = "";
			String j10Breach1 = "";
			String j10ChairMan1 = "";
			String j10CustId1 = "";
			String j10DupNo1 = "";

			// 無資料時，暫時針對小規模簽報書才去抓資信簡表
			if ((kcs003_map == null || kcs003_map.isEmpty())) {

				if (cesJ10List != null && !cesJ10List.isEmpty()) {
					for (Map<String, Object> cesJ10Map : cesJ10List) {
						String JSONOB = Util.trim(MapUtils.getString(cesJ10Map,
								"JSONOB"));
						String SUBTAB = Util.trim(MapUtils.getString(cesJ10Map,
								"SUBTAB"));
						String fieldIndex = "";
						if (Util.notEquals(SUBTAB, "")) {
							fieldIndex = Util.getRightStr(SUBTAB,
									Util.strLength(SUBTAB) - 1);
						}

						if (Util.notEquals(JSONOB, "")) {

							JSONObject json = JSONObject.fromObject(JSONOB);

							if (json != null) {

								// j10Percentile1=百分位點區間
								// j10JCICscore1=JCIC信用評分
								// j10Date1=資料日期
								// j10Breach1=違約率

								// {"j10Percentile0":"50.00%~60.00%","j10JCICscore0":"730","j10Date0":"108/08/13","j10Breach0":"0.14%","j10ChairMan0":"楊式昌","j10CustId0":"C120328100","j10DupNo0":"0"}
								// {"j10Percentile0":"0%~10.00%","j10JCICscore0":"0","j10Date0":"108/08/23","j10Breach0":"91.76%","j10ChairMan0":"隋國鑫"}
								// {"j10Percentile0":"N.A.","j10JCICscore0":"N.A.","j10Date0":"109/05/21","j10Breach0":"N.A.","j10ChairMan0":"徐黃秋香","j10CustId0":"K201387919","j10DupNo0":"0"}
								j10Percentile1 = json.optString("j10Percentile"
										+ fieldIndex, "");
								j10JCICscore1 = json.optString("j10JCICscore"
										+ fieldIndex, "");
								j10Date1 = json.optString("j10Date"
										+ fieldIndex, "");
								j10Breach1 = json.optString("j10Breach"
										+ fieldIndex, "");
								j10ChairMan1 = json.optString("j10ChairMan"
										+ fieldIndex, "");
								j10CustId1 = json.optString("j10CustId"
										+ fieldIndex, "");
								j10DupNo1 = json.optString("j10DupNo"
										+ fieldIndex, "");

								if (Util.trim(l120s10a.getCustId()).equals(
										Util.trim(j10CustId1))
										&& Util.trim(l120s10a.getDupNo())
												.equals(Util.trim(j10DupNo1))) {
									findCesData = true;
									break;
								}

							}
						}

					}
				}

				if (findCesData) {
					l120s10a.setJ10Score(j10JCICscore1);
					l120s10a.setJ10Date(TWNDate.valueOf(j10Date1)); // 轉西元
					l120s10a.setJ10Percentile(j10Percentile1);
					l120s10a.setJ10Breach(j10Breach1);
				} else {
					l120s10a.setJ10Score("");
					l120s10a.setJ10Date(null);
					l120s10a.setJ10Percentile(null);
					l120s10a.setJ10Breach(null);
				}

			} else {
				// JCIC 有資料
				if (kcs003_map != null && !kcs003_map.isEmpty()) {
					j10score = CapString.trimNull(kcs003_map.get("SCORE"));
					j10Date = CapString.trimNull(kcs003_map.get("QDATE")); // 民國
				}
				l120s10a.setJ10Score(j10score);
				l120s10a.setJ10Date(TWNDate.valueOf(j10Date)); // 轉西元

				if (Util.isEmpty(j10score)) {
					l120s10a.setJ10Percentile(null);
					l120s10a.setJ10Breach(null);
				} else if (Util.equals("0", j10score)) {
					l120s10a.setJ10Score("N.A.");
					l120s10a.setJ10Percentile("N.A.");
					l120s10a.setJ10Breach("N.A.");
				} else {
					Entry<String, String> scoreEntry = J10_PERCENTILE_MAP
							.ceilingEntry(j10score);
					if (scoreEntry != null) {
						l120s10a.setJ10Percentile(scoreEntry.getValue());
					}
					Entry<String, String> entry = J10_BREACH_MAP
							.ceilingEntry(j10score);
					if (entry != null) {
						l120s10a.setJ10Breach(entry.getValue());
					}
				}
			}
			lmsService.save(l120s10a);
		}

		return l120s10a;
	}

	@Override
	public void deleteL120s10as(String mainId) {
		List<L120S10A> datas = l120s10aDao.findByMainId(mainId);
		if (datas != null && datas.size() > 0) {
			l120s10aDao.delete(datas);
		}
	}

	@Override
	public void deleteL120s10b(String mainId) {
		L120S10B l120s10b = l120s10bDao.findByUniqueKey(mainId);
		if (l120s10b != null) {
			l120s10bDao.delete(l120s10b);
		}
	}

	@Override
	public List<L140M01S> findL140m01sByMainIdType(String mainId, String type) {
		return l140m01sDao.findByMainIdType(mainId, type);
	}

	@Override
	public List<L120S04C> findL120s04cByMainIdDocKind(String mainId,
			String[] docKind) {
		return l120s04cDao.findByMainIdDocKind(mainId, docKind);
	}

	@Override
	public void chkListFull(String mainId) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS23APanel.class);
		Locale locale = LMSUtil.getLocale();
		Map<String, String> relationMap = null;
		relationMap = codetypeservice.findByCodeType("BlackListRelation",
				locale.toString());

		// J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		L120M01A l120m01a = this.findModelByMainId(L120M01A.class, mainId);

		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		Map<String, String> lostMap = new HashMap<String, String>();
		// J10 現有名單
		Map<String, String> currentMap = new HashMap<String, String>();
		Map<String, String> currentNameMap = new HashMap<String, String>();
		List<L120S10A> l120s10as = this.findL120s10aByMainId(mainId);
		if (l120s10as != null && !l120s10as.isEmpty()) {
			for (L120S10A l120s10a : l120s10as) {
				String tCustId = Util.trim(l120s10a.getCustId());
				String tDupNo = Util.trim(l120s10a.getDupNo());
				String tCustName = Util.toSemiCharString(String.valueOf(Util
						.trim(l120s10a.getCustName())));
				String fullKey = tCustId + tDupNo;
				if (Util.notEquals(fullKey, "")) {
					if (!currentMap.containsKey(fullKey)) {
						currentMap.put(fullKey, tCustName);
					}
				}
				if (Util.notEquals(tCustName, "")) {
					if (!currentNameMap.containsKey(tCustName)) {
						currentNameMap.put(tCustName, "");
					}
				}
			}
		}

		// 當下資料名單
		String chkId = "";
		String chkName = "";
		// 借款人+共借人(要掃其負責人與實質受益人)
		Map<String, String> cntrAllCustIdMap = new HashMap<String, String>();
		List<L140M01A> l140m01as = this.findL140m01aListByL120m01cMainId(
				mainId, UtilConstants.Cntrdoc.ItemType.額度明細表);
		for (L140M01A l140m01a : l140m01as) {
			// 額度明細表借款人
			String bId = l140m01a.getCustId();
			String bNo = l140m01a.getDupNo();
			String bName = Util.toSemiCharString(Util.trim(l140m01a
					.getCustName()));

			if (!cntrAllCustIdMap.containsKey(bId + bNo)) {
				cntrAllCustIdMap.put(bId + bNo, bName);
			}

			// 額度明細表共同借款人
			Set<L140M01J> l140m01js = l140m01a.getL140m01j();
			for (L140M01J l140m01j : l140m01js) {
				String cId = l140m01j.getCustId();
				String cNo = l140m01j.getDupNo();
				String cName = Util.toSemiCharString(Util.trim(l140m01j
						.getCustName()));

				if (!cntrAllCustIdMap.containsKey(cId + cNo)) {
					cntrAllCustIdMap.put(cId + cNo, cName);
				}
			}

			// 連帶保證人
			Set<L140M01I> l140m01is = l140m01a.getL140m01i();
			for (L140M01I l140m01i : l140m01is) {
				String gId = l140m01i.getRId();
				String gNo = l140m01i.getRDupNo();
				String gName = Util.toSemiCharString(Util.trim(l140m01i
						.getRName()));
				chkId = gId + gNo;
				chkName = gName;
				if (Util.equals(UtilConstants.lngeFlag.連帶保證人,
						l140m01i.getRType())) {
					if (Util.notEquals(Util.trim(l140m01a.getGuarantorType()),
							"2")) {
						if (Util.notEquals(chkId, "")) {
							if (!currentMap.containsKey(chkId)) {
								if (!lostMap.containsKey(chkId)) {
									lostMap.put(
											chkId,
											chkName
													+ "("
													+ Util.trim(relationMap
															.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.連保人))
													+ ")");
								}
							}
						}
					}
				}
			}
		}

		// 可能沒有ID*****************************************************************
		// 負責人+實質受益人
		List<L120S01B> l120s01bs = (List<L120S01B>) this.findListByMainId(
				L120S01B.class, mainId);
		if (l120s01bs != null && !l120s01bs.isEmpty()) {
			for (L120S01B l120s01b : l120s01bs) {
				String s01bCustId = Util.trim(l120s01b.getCustId());
				String s01bDupNo = Util.trim(l120s01b.getDupNo());
				// 借款人或共借人才要
				if (cntrAllCustIdMap.containsKey(s01bCustId + s01bDupNo)) {
					// 負責人
					String chairmanId = Util.trim(l120s01b.getChairmanId());
					String chairmanDupNo = Util.trim(l120s01b
							.getChairmanDupNo());
					String chairman = Util.toSemiCharString(Util.trim(l120s01b
							.getChairman()));
					chkId = chairmanId + chairmanDupNo;
					chkName = chairman;
					if (Util.notEquals(chkId, "")
							|| Util.notEquals(chkName, "")) {
						if (Util.notEquals(chkId, "")) { // 有ID就用ID查
							if (!currentMap.containsKey(chkId)) {
								if (!lostMap.containsKey(chkId)) {
									lostMap.put(
											chkId,
											chkName
													+ "("
													+ Util.trim(relationMap
															.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人))
													+ ")");
								}
							}
						} else { // 沒有ID但有戶名
							if (!currentNameMap.containsKey(chkName)) {
								if (!lostMap.containsKey(chkName)) {
									lostMap.put(
											chkName,
											""
													+ "("
													+ Util.trim(relationMap
															.get(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人))
													+ ")");
								}
							}

						}
					}
				}
			}
		}

		StringBuffer lostStrBuff = new StringBuffer("");
		if (lostMap != null && !lostMap.isEmpty()) {
			for (String keySet : lostMap.keySet()) {
				String tKey = Util.trim(keySet);
				String tValue = Util.trim(lostMap.get(tKey));
				if (Util.equals(tKey, tValue)) {
					lostStrBuff.append(tKey).append("<BR>");
				} else {
					lostStrBuff.append(tKey).append(" ").append(tValue)
							.append("<BR>");
				}

			}
		}
		if (Util.notEquals(lostStrBuff, "")) {
			// Mini.error001=下列名單於微型企業頁籤無對應的J10查詢結果<BR>{0}
			throw new CapMessageException(MessageFormat.format(
					pop.getProperty("Mini.error001"), lostStrBuff.toString()),
					getClass());
		}

		if (l120s10as == null || l120s10as.isEmpty()) {
			// Mini.error002=微型企業頁籤請查詢J10
			throw new CapMessageException(pop.getProperty("Mini.error002"),
					getClass());
		}

		L120S10B l120s10b = this.findModelByMainId(L120S10B.class, mainId);
		if (l120s10b == null) {
			// Mini.error003=微型企業頁籤未完成審查意見欄位
			throw new CapMessageException(pop.getProperty("Mini.error003"),
					getClass());
		} else {
			String credit = Util.trim(Util.nullToSpace(l120s10b.getCredit()));
			String deposit = Util.trim(Util.nullToSpace(l120s10b.getDeposit()));
			String operation = Util.trim(Util.nullToSpace(l120s10b
					.getOperation()));
			String guarantee = Util.trim(Util.nullToSpace(l120s10b
					.getGuarantee()));
			String finance = Util.trim(Util.nullToSpace(l120s10b.getFinance()));
			String prospect = Util
					.trim(Util.nullToSpace(l120s10b.getProspect()));
			if (Util.isEmpty(credit) && Util.isEmpty(deposit)
					&& Util.isEmpty(operation) && Util.isEmpty(guarantee)
					&& Util.isEmpty(finance) && Util.isEmpty(prospect)) {
				throw new CapMessageException(pop.getProperty("Mini.error003"),
						getClass());
			}
		}

		// J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		// J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
		// J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
        // J-112-0148 疫後振興
		if (!lmsService.hidePanelbyCaseType(l120m01a)
				&& !lmsService.hidePanelbyCaseType_lnType61(l120m01a)
				&& !lmsService.hidePanelbyCaseType_003(l120m01a)
				&& !lmsService.hidePanelbyCaseType_004(l120m01a)
                && !lmsService.hidePanelbyCaseTypeF(l120m01a)) {
			// 檢查ROA表
			List<L120S04C> l120s04cList = this.findL120s04cByMainIdDocKind(
					mainId, new String[] { "1", "2" });
			if (l120s04cList == null || l120s04cList.isEmpty()) {
				// Mini.error004=尚未引進借戶暨關係戶與本行往來實績彙總表
				throw new CapMessageException(pop.getProperty("Mini.error004"),
						getClass());
			}
		}

	}
}
