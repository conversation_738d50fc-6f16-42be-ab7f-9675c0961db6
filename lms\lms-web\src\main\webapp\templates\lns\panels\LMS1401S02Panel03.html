<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="LMS1401S02Panel03">
		<div id="tabs-c" class="tabs">
			<ul>
				<li id="tab03_1">
					<a href="#tabs-c01">
						<b><th:block th:text="#{'L140S02Tab.3_01'}"><!--科子目限額--></th:block></b>
					</a>
				</li>
				<li id="tab03_2">
					<a href="#tabs-c01_2">
						<b><th:block th:text="#{'L140S02Tab.3_02'}"><!--科子目合併限額--></th:block></b>
					</a>
				</li>
				<li id="tab03_3">
					<a href="#tabs-c01_3">
						<b><th:block th:text="#{'L140S02Tab.3_03'}"><!--聯行攤貸比例--></th:block></b>
					</a>
				</li>
				<li id="tab03_4">
					<a href="#tabs-c01_4">
						<b><th:block th:text="#{'L140S02Tab.3_04'}"><!--限額條件敘述--></th:block></b>
					</a>
				</li>
			</ul>
			<div class="tabCtx-warp">
				<!-- 請在tab content 外加上 div.tabCtx-warp -->
				<div id="tabs-c01" class="content">
					<table class="tb2" width="800px" border="0" cellpadding="0" cellspacing="0">
						<tr class="hd1" style="text-align:left; margin-top:0px; padding-top:0px;">
							<td>
								<button type="button" id="newItemChildren1Bt" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.add'}"><!--新增--></th:block>
									</span>
								</button>
								<button type="button" id="removeGridviewitemChildren" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.delete'}"><!--刪除--></th:block>
									</span>
								</button>
							</td>
						</tr>
						<tr>
							<td>
								<div id="gridviewitemChildren"></div>
							</td>
						</tr>
					</table>
				</div><!--end tabs-c01-->

				<div id="tabs-c01_2" class="content">
					<table class="tb2" width="800px" border="0" cellpadding="0" cellspacing="0">
						<tr class="hd1" style="text-align:left">
							<td>
								<button type="button" id="newItemChildren2Bt" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.add'}"><!--新增--></th:block>
									</span>
								</button>
								<button type="button" id="removeGridviewitemChildren2" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.delete'}"><!--刪除--></th:block>
									</span>
								</button>
							</td>
						</tr>
						<tr>
							<td>
								<div id="gridviewitemChildren2"></div>
							</td>
						</tr>
					</table>
				</div><!--end tabs-c01_2-->

				<div id="tabs-c01_3" class="content">
					<table class="tb2" width="800px" border="0" cellpadding="0" cellspacing="0">
						<tr class="hd1" style="text-align:left">
							<td>
								<button type="button" id="newItemChildren3Bt" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.add'}"><!--新增--></th:block>
									</span>
								</button>
								<button type="button" id="removeGridviewitemChildren3" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.delete'}"><!--刪除--></th:block>
									</span>
								</button>
								<button type="button" id="changesShareRate2" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'btn.changesShareRate2'}"><!--變更分母--></th:block>
									</span>
								</button>
							</td>
						</tr>
						<tr>
							<td>
								<div id="gridviewitemChildren3"></div>
							</td>
						</tr>
					</table>
					<table id="div_gridviewitemChildren4" class="tb2" width="800px" border="0" cellpadding="0" cellspacing="0">
						<tr class="hd1" style="text-align:left">
							<td>
								<span class="text-only">
									<th:block th:text="#{'L140S02Tab.3_03_1'}"><!--動審表聯行攤貸比例--></th:block>
								</span>
							</td>
						</tr>
						<tr>
							<td>
								<div id="gridviewitemChildren4"></div>
							</td>
						</tr>
					</table>
				</div><!--end tabs-c01_3-->

				<div id="tabs-c01_4" class="content">
					<table class="tb2" width="800px" border="1" cellpadding="0" cellspacing="0">
						<tr class="hd1" style="text-align:left">
							<td>
								<span style="display:none" class="caseSpan">
									<label>
										<input id="tab03" type="checkbox" class="caseBox tabBox"></input>
										<th:block th:text="#{'button.modify'}"><!--修改--></th:block>
									</label>
								</span>
								<select id="pageNum1" name="pageNum1" class="nodisabled">
									<option value="0" selected="selected">
										<th:block th:text="#{'L140M01b.printMain'}"><!--印於主表--></th:block>
									</option>
									<option value="1">
										<th:block th:text="#{'L140M01b.print01'}"><!--印於附表(一)--></th:block>
									</option>
									<option value="2">
										<th:block th:text="#{'L140M01b.print02'}"><!--印於附表(二)--></th:block>
									</option>
									<option value="3">
										<th:block th:text="#{'L140M01b.print03'}"><!--印於附表(三)--></th:block>
									</option>
								</select> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<button id="limitWordAll" type="button">
									<span class="text-only">
										<th:block th:text="#{'other.toword'}"><!--組成文字串--></th:block>
									</span>
								</button>
							</td>
						</tr>
						<tr>
							<td>
								<textarea id="itemDscr1" name="itemDscr1" cols="130" rows="13%" style="width: 840px; height: 200px;" readonly="readonly" class="caseReadOnly"></textarea>
							</td>
						</tr>
					</table><br><br>
				</div><!--end tabs-c01_4-->
			</div><!--end class tabCtx-warp-->
		</div><!--end tabs-c-->

		<div id="newItemChildrenBox1" style="display:none;"><!-- 登錄科子目限額 thinkBox -->
			<form id="L140M01DForm1" name="L140M01DForm1">
				<table width="100%" class="tb2" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td width="5%" class="hd1">
							<th:block th:text="#{'L782M01A.loanTP'}"><!--科目--></th:block>&nbsp;&nbsp;
						</td>
						<td width="95%">
							<!--<select id="subject1" name="subject" combokey="lms1405m01_SubItem" />-->
							<!--科目位置-->
							<select id="subject1" name="subject" class="nodisabled required"></select>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01d.lmtMoney'}"><!--限額--></th:block>&nbsp;&nbsp;
						</td>
						<td>
							<select id="lmtCurr1" name="lmtCurr1" class="money nodisabled"></select>
							<input type="text" id="lmtAmt1" name="lmtAmt1" class="numeric required number" size="19" maxlength="22" integer="13" fraction="2"></input>
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
					</tr>
				</table>
				<input type="text" id="lmtSeq1" name="lmtSeq1" style="display:none" size="16" maxlength="16"></input>
			</form>
		</div><!-- 登錄科子目限額 thinkBox END-->

		<div id="newItemChildrenBox2" style="display:none;"><!--登錄科子目合併限額 thinkBox -->
			<form id="L140M01DForm2" name="L140M01DForm2">
				<table style="width:530px" class="tb2" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="hd1" width="15%">
							<th:block th:text="#{'L140M01d.lmtMoney2'}"><!--合併限額--></th:block>&nbsp;&nbsp;
						</td>
						<td width="85%">
							<select id="lmtCurr2" name="lmtCurr2" class="money nodisabled required"></select>
							<input type="text" id="lmtAmt2" name="lmtAmt2" class="numeric required number" size="19" maxlength="22" integer="13" fraction="2"></input>
							<th:block th:text="#{'other.money'}"><!--元--></th:block>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L782M01A.loanTP'}"><!--科目--></th:block>
						</td>
						<td>
							<input type="checkbox" id="subject2" name="subject2" class="nodisabled"></input>
							<!--科目位置 -->
						</td>
					</tr>
				</table>
				<input type="text" id="lmtSeq2" name="lmtSeq2" style="display:none" size="16" maxlength="16"></input>
			</form>
		</div><!--登錄科子目合併限額 thinkBox END-->

		<div id="newItemChildrenBox3" style="display:none;"><!-- 登錄聯行攤貸比例 thinkBox -->
			<form id="L140M01EForm" name="L140M01EForm">
				<table width="100%" class="tb2" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td width="60%" class="hd1">
							<th:block th:text="#{'L140M01a.moneyAmt'}"><!--現請額度--></th:block>&nbsp;&nbsp;
						</td>
						<td width="40%">
							<input id="totalAmt" name="totalAmt" readonly="readonly" class="caseReadOnly"></input>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L140M01e.shareBrId'}"><!--攤貸分行--></th:block>&nbsp;&nbsp;
						</td>
						<td>
							<select id="shareBrId" name="shareBrId"></select>
							<!--分行位置-->
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<label>
								<input type="radio" id="shareFlag" name="shareFlag" value="1" class="required"></input>
								<th:block th:text="#{'L140M01e.shareAmt'}"><!--攤貸金額--></th:block>
							</label>&nbsp;&nbsp;
						</td>
						<td>
							<input type="text" id="shareAmt" name="shareAmt" size="13" maxlength="22" integer="13" fraction="2" class="required numeric"></input>
							<!--<button type="button" id="shareMoneyCount2" class="noHideBt" ><span class="text-only"><wicket:message key="L140M01e.count2">計算比率</wicket:message></span></button>-->
							<span id="tips2" class="text-red"></span>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<label>
								<input type="radio" id="shareFlag" name="shareFlag" value="2" class="required"></input>
								<th:block th:text="#{'L140M01e.shareRate1'}"><!--攤貸比例--></th:block>
							</label>&nbsp;&nbsp;
						</td>
						<td>
							<input type="text" id="shareRate1" name="shareRate1" size="5" maxlength="5" integer="5" fraction="0" class="numeric required"></input>/
							<input type="text" id="shareRate2" name="shareRate2" size="5" maxlength="5" integer="5" fraction="0" class="numeric required"></input>
							<!--<button type="button" id="shareMoneyCount1" class="noHideBt"><span class="text-only"><wicket:message key="L140M01e.count"> 計算金額</wicket:message></span></button>-->
							<span id="tips" class="text-red"></span>
						</td>
					</tr>
				</table>
			</form>
		</div><!-- 登錄聯行攤貸比例 thinkBox END-->

		<!-- 登錄聯行攤貸比例 國內額度序號給號 thinkBox -->
		<div id="cntrNoBoxforItem3" style="display:none;">
			<form id="cntrNoBoxforItem3Form" name="cntrNoBoxforItem3Form">
				<table width="100%" class="tb2" border="1">
					<tr>
						<!--用來放額度序號類型-->
						<input type="text" id="cntrNoType" style="display:none;"></input>
					</tr>
					<tr>
						<td width="80%" class="hd1">
							<th:block th:text="#{'L140M01a.message29'}"><!--請選擇額度序號來源--></th:block>&nbsp;&nbsp;
						</td>
						<td width="20%">
							<label>
								<input type="radio" name="numberType" value="1" class="required"></input>
								<th:block th:text="#{'L140M01a.message30'}"><!--產生新號(適用於「新做」案件)--></th:block>
							</label><br>
							<label>
								<input type="radio" name="numberType" value="2" class="required"></input>
								<th:block th:text="#{'L140M01a.message32'}"><!-- 登錄原案額度序號(適用於舊案續約及條件變更)--></th:block>
							</label>
						</td>
					</tr>
					<tr class="ForInSide">
						<td class="hd1">
							<th:block th:text="#{'typCd.title'}"><!--類別--></th:block>&nbsp;&nbsp;
						</td>
						<td>
							<label>
								<input type="radio" name="typeCd" value="1" class="required"></input>DBU
							</label>&nbsp;&nbsp;
							<label>
								<input type="radio" name="typeCd" value="4" class="required"></input>OBU
							</label>
						</td>
					</tr>
					<tr id="showBrNoTr" class="ForInSide">
						<td class="hd1">
							<th:block th:text="#{'L140M01a.message28'}"><!--請輸入欲產生額度序號之作帳行分行代碼(三碼)--></th:block>&nbsp;&nbsp;
						</td>
						<td>
							<input type="text" id="branchNoItem3" maxlength="3" minlength="3" size="3" class="required upText"></input>
						</td>
					</tr>
					<tr class="ForOriginal">
						<td class="hd1">&nbsp;&nbsp;</td>
						<td>
							<b>
								<th:block th:text="#{'L140M01a.message33'}"><!--請輸入原額度序號: 該舊額度序號須已執行轉換，轉換後新編碼之額度序號--></th:block><br>
								【<th:block th:text="#{'L140M01a.message68'}"><!--額度序號長度應為12碼，編碼原則:XXX(分行代號)+X(1:DBU,4:OBU,5:海外)+YYY(年度)+99999(流水號)--></th:block>】
							</b><br>
							<input type="text" id="originalCntrNo" size="11" minlength="12" maxlength="12" class="upText required"></input>
						</td>
					</tr>
				</table>
			</form>
		</div><!--登錄聯行攤貸比例 國內額度序號給號 -->

		<div id="newSharteNewBox" style="display:none;">
			<!-- 選擇分母 thinkBox -->
			<span>
				<form id="newSharteNewForm" name="newSharteNewForm">
					<input type="text" id="newSharteNew" name="newSharteNew" size="5" maxlength="5" integer="5" fraction="0" class="numeric required"></input>
				</form>
			</span>
		</div><!-- 選擇分母 thinkBox END-->
	</th:block>
</body>
</html>
