$(document).ready(
		function() {
		var grid = $("#gridview").iGrid({
			handler : 'lms7005gridhandler',
			height : 350,
			sortname : 'branchId',
			postData : {
				docStatus : viewstatus,
				formAction: "queryL700m01a",
				rowNum:15
			},
			rowNum:15,
			multiselect : true,
			colModel : [  {
				colHeader : i18n.lms7005v00['L700M01A.branchId'],//"分行別",
				name : 'branchId',
				width : 100,
				sortable : true,
				formatter : 'click',
				onclick : openDoc
			}, {
				colHeader : i18n.lms7005v00['L700M01A.groupId'],//"隸屬區域營運中心",
				name : 'groupId',
				align : "center",
				width : 70,
				sortable : true
			}, {
				colHeader : i18n.lms7005v00['L700M01A.userNo'],//"負責經辦",
				name : 'userNo',
				align : "left",
				width : 100,
				sortable : true
			}, {
				colHeader : i18n.lms7005v00['L700M01A.reCheck'],//"負責主管",
				name : 'reCheck',
				align : "left",
				width : 100,
				sortable : true
			}, {
				colHeader : "ownBrId",
				name : 'ownBrId',
				hidden : true
			}, {
				colHeader : "oid",
				name : 'oid',
				hidden : true
			} ],
	        ondblClickRow: function(rowid){		//當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
	            var data = grid.getRowData(rowid);
	            openDoc(null,null,data);
	        }
		});
		$("#L700M01AForm").find("#branchId").change(function(){	//當使用者選好BranchId後檢查			
		});
		$("#buttonPanel").find("#btnDelete").click(function() {		//當使用者點選刪除按鈕時處理刪除工作
			var rows = $("#gridview").getGridParam('selarrrow');
			var list = "";
			var sign = ",";
			for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
				if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
					var data = $("#gridview").getRowData(rows[i]);
					list += ((list == "") ? "" : sign ) + data.oid;
				}
			}
			if (list == "") {
				return CommonAPI.showMessage(i18n.lms7005v00['l720v00.alert1']);
				}
			$.ajax({
				type : "POST",
				handler : "lms7005formhandler",
				data : 
				{
					formAction : "deleteL700m01a",
					listOid : list,
					sign : sign
				},
				success:function(json){
					CommonAPI.triggerOpener("gridview","reloadGrid");
					$("#gridview").trigger("reloadGrid");//更新Grid內容						
				}
			});			
		}).end().find("#btnAdd").click(function() {		//當使用者點選新增按鈕時檢查沒有和Grid重複的BranchId及Subject
			$.ajax({
				type : "POST",
				handler : "lms7005formhandler",
				data : 
				{
					formAction : "queryAddData"
				},
				success:function(responseData){
					var json = {
		       	    format : "{key}({value})",
		       	    item : responseData.itemBranch
					};
					var json2 = {
			       	    format : "{key}",
			       	    item : responseData.userNo
			       	};
					var json3 = {
				       	    format : "{key}",
				       	    item : responseData.reCheck
				    };
					$("#branchId").setItems(json);
					$("#userNo").setItems(json2);
					$("#reCheck").setItems(json3);
//					alert(JSON.stringify(responseData.groupId));
					//20130416 Vector 解決"修改後，新增的更新人員未清除"問題
					$("#updater").val("");
					$("#item_007").hide();
					$("#L700M01AForm").setData(responseData);
//					alert(JSON.stringify(jsonGroup));
					thickBoxOpen("");
				}
			});
		}).end().find("#btnModify").click(function(){		//當使用者點選修改按鈕時讀取已有資料進行修改
			var rows = $("#gridview").getGridParam('selarrrow');
			var list = "";
			var sign = ",";
			for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
				if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
					var data = $("#gridview").getRowData(rows[i]);
					list += ((list == "") ? "" : sign ) + data.oid;
				}
			}
			if (list == "") {
				return CommonAPI.showMessage(i18n.lms7005v00['l720v00.alert1']);
				}
			if(rows.length!=1)
			{
				return CommonAPI.showMessage(i18n.lms7005v00['l720v00.alert2']);				
			}
			else
			{
				openDoc(null,null,data);
			}
		});
		$("#branchId").change(function(){
			$.ajax({
				type : "POST",
				handler : "lms7005formhandler",
				data : 
				{
					formAction : "queryGrorpId",
					branch : $(this).val()
				},
				success:function(queryGrorpId){					
					$("#L700M01AForm").find("#cgroupId").html(DOMPurify.sanitize(String(queryGrorpId.cgroupId)));				
					$("#L700M01AForm").find("#groupId").html(DOMPurify.sanitize(String(queryGrorpId.groupId)));
				}
			});
		});
});

function openDoc(cellvalue, options, rowObject) {	//讀取已有的資料以進行修改
	ilog.debug(rowObject);
	$.ajax({
		type : "POST",
		handler : "lms7005formhandler",
		data : 
		{
			formAction : "queryL700m01a",
			oid : rowObject.oid
		},
		success:function(responseData){
			//alert(JSON.stringify(responseData));
			var json = {
	       	    format : "{key}({value})",
	       	    item : responseData.itemBranch,
	       	    value : responseData.L700M01AForm.branchId
				};
				var json2 = {
		       	    format : "{key}",
		       	    item : responseData.userNo,
		       	    value : responseData.L700M01AForm.userNo
		       	};
				var json3 = {
			       	    format : "{key}",
			       	    item : responseData.reCheck,
			       	    value : responseData.L700M01AForm.reCheck
			    };
				$("#branchId").setItems(json);
				$("#userNo").setItems(json2);
				$("#reCheck").setItems(json3);
				$("#L700M01AForm").setData(responseData);			
				thickBoxOpen(responseData.L700M01AForm.oid);
		}
	});
};

function thickBoxOpen(oid){			//負責處理打開ThickBox功能
	$("#openThickbox").thickbox({	// 使用選取的內容進行彈窗
		title : i18n.lms7005v00['l720v00.index01'],
		width : 800,
		height : 300,
		align : 'center',
		valign: 'bottom',
		modal: false,
		i18n : i18n.def,
		buttons : {
			"saveData" : function(showMsg){
				if($("#L700M01AForm").valid()){
					//20130416 Vector補上檢查條件
					if($("#branchId").val()==""){
						CommonAPI.showMessage(i18n.lms7005v00['l720v00.plsSelBrno']);
					}
					else{
						$.ajax({
							type : "POST",
							handler : "lms7005formhandler",
							data : 
							{
								formAction : "saveL700m01a",
								branchId : $("#branchId").val(),
								oid : oid
							},
							success:function(json){
		//						alert(JSON.stringify(json));
								CommonAPI.triggerOpener("gridview","reloadGrid");
								$("#gridview").trigger("reloadGrid");//更新Grid內容						
							}
						});
						$.thickbox.close();
					}
				}
			},
			"close" : function(){
				$.thickbox.close();
			}
		}
	});
}
