<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="LMS1401S02Panel06">
		<span id="page08isGutCut" style="display:none">
			<font style='font-size:16px; font-weight: bold;' color="red">
				<th:block th:text="#{'L140M01b.page08isGutCut'}">※信保機構保證書所囑事項，應詳實列入額度明細表中</th:block>
			</font>
		</span>
		<table class="tb2" width="100%" border="0" cellpadding="0" cellspacing="0">
			<tr class="hd1" style="text-align:left">
				<td colspan="2">
					<span style="display:none" class="caseSpan">
						<label>
							<input id="tab06" type="checkbox" class="caseBox"></input>
							<th:block th:text="#{'button.modify'}"><!--修改--></th:block>
						</label>
					</span>
					<select id="pageNum4" name="pageNum4" class="nodisabled" onchange="changedistanceWord('4','44','53');">
						<option value="0" selected="selected">
							<th:block th:text="#{'L140M01b.printMain'}"><!--印於主表--></th:block>
						</option>
						<option value="1">
							<th:block th:text="#{'L140M01b.print01'}"><!--印於附表(一)--></th:block>
						</option>
						<option value="2">
							<th:block th:text="#{'L140M01b.print02'}"><!--印於附表(二)--></th:block>
						</option>
						<option value="3">
							<th:block th:text="#{'L140M01b.print03'}"><!--印於附表(三)--></th:block>
						</option>
					</select>&nbsp;&nbsp;&nbsp;
					<select id="formatType" name="formatType" class="nodisabled">
						<option value="1" selected="selected">
							<th:block th:text="#{'L140M01b.formatType1'}"><!--以自由格式編輯--></th:block>
						</option>
						<option value="2">
							<th:block th:text="#{'L140M01b.formatType2'}"><!--以樣版格式編輯--></th:block>
						</option>
					</select>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<div id="showL140S09" style="display:none">
						<button type="button" id="btnOpenL140S09A" class="forview">
							<th:block th:text="#{'btn.L140S09A'}">以樣版格式編輯</th:block>
						</button>
						<!--<button type="button" id="btnImportL140S09Str"><wicket:message key="btn.ImportStr">引進樣版條件</wicket:message></button>-->
					</div>
					<button type="button" id="btn_ESG" class="forview">
						<th:block th:text="#{'L140S12A.memoEsgTerms'}"><!--登錄應注意/承諾/待追蹤/ESG連結條款--></th:block>
					</button>
					<textarea cols="100" rows="10%" id="itemDscr4" name="itemDscr4" class="tckeditor"  showType="b" th:attr="displayMessage=#{'L140S02Tab.6'}"
						t_width="800" t_height="500" showNewLineMessage="Y" distanceWord="44" preview="width:800;height:900"></textarea>
				</td>
			</tr>
			<tr class="J-113-0035_check">
				<td class="hd1">
					<span class="text-red">
						<th:block th:text="#{'L140M01b.toALoan'}"><!--動撥提醒事項--></th:block>&nbsp;&nbsp;
					</span>
				</td>
				<td>
					<th:block th:text="#{'L140M01b.toALoan1'}"><!--注意事項--></th:block>：
					<input type="text" id="toALoan1" name="toALoan1" maxlength="30" maxlengthC="30" size="80" class="nodisabled fullText"></input><br>
					<th:block th:text="#{'L140M01b.toALoan2'}"><!--承諾事項--></th:block>：<br>
					<textarea id="toALoan2" name="toALoan2" maxlengthC="390" rows="5" cols="70" class="nodisabled fullText"></textarea>
				</td>
			</tr>
			<tr>
				<td class="hd1">
					<th:block th:text="#{'l1405s02p06.001'}"><!--附加檔案--></th:block>&nbsp;&nbsp;
				</td>
				<td>
					<button type="button" id="uploadFile" class="noHideBt">
						<span class="text-only">
							<th:block th:text="#{'l1405s02p06.002'}"><!--選擇附加檔案--></th:block>
						</span>
					</button>
					<button type="button" id="deleteFile" class="noHideBt">
						<span class="text-only">
							<th:block th:text="#{'button.delete'}"><!--刪除--></th:block>
						</span>
					</button><br>
					<div id="gridfile"></div>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<div id="bizCatThickbox" style="display:none;">
						<!--<form id="formBizCatDetail">-->
						<button type="button" id="btnNewL140S09A">
							<span class="text-only">
								<th:block th:text="#{'btn.addL140S09A'}">選取簽報樣版</th:block>
							</span>
						</button>
						<button type="button" id="btnDelL140S09AByBizCat">
							<span class="text-only">
								<th:block th:text="#{'button.delete'}">刪除</th:block>
							</span>
						</button>
						<button type="button" id="btnChgLoanName">
							<span class="text-only">
								<th:block th:text="#{'btn.chgLoanName'}">更改科目別</th:block>
							</span>
						</button>
						<button type="button" onclick="Panel06Action.upDown(true, 'bizCatGrid')">
							<span class="text-only">
								<th:block th:text="#{'btn.upSeqno'}">向上移動</th:block>
							</span>
						</button>
						<button type="button" onclick="Panel06Action.upDown(false, 'bizCatGrid')">
							<span class="text-only">
								<th:block th:text="#{'btn.downSeqno'}">向下移動</th:block>
							</span>
						</button>
						<button type="button" onclick="previewL140S09()">
							<span class="text-only">
								<th:block th:text="#{'button.preview'}">預覽</th:block>
							</span>
						</button>
						<div id="bizCatGrid"></div>
						<!--</form>-->
					</div>

					<div id="l140s09aThickbox" style="display:none;">
						<!--<form id="formL140S09ADetail">-->
						<button type="button" id="btnNewL140S09A_XX">
							<span class="text-only">
								<th:block th:text="#{'btn.addL140S09A_ItemXX'}">新增自訂項目</th:block>
							</span>
						</button>
						<button type="button" id="btnChgBizItemName">
							<span class="text-only">
								<th:block th:text="#{'btn.chgBizItemName'}">更改項目名稱</th:block>
							</span>
						</button>
						<button type="button" id="btnDelL140S09A">
							<span class="text-only">
								<th:block th:text="#{'button.delete'}">刪除</th:block>
							</span>
						</button>
						<button type="button" id="btnUpdIsPrint">
							<span class="text-only">
								<th:block th:text="#{'btn.updIsPrint'}">設定是否列印</th:block>
							</span>
						</button>
						<button type="button" onclick="Panel06Action.upDown(true, 'l140s09aGrid')">
							<span class="text-only">
								<th:block th:text="#{'btn.upSeqno'}">向上移動</th:block>
							</span>
						</button>
						<button type="button" onclick="Panel06Action.upDown(false, 'l140s09aGrid')">
							<span class="text-only">
								<th:block th:text="#{'btn.downSeqno'}">向下移動</th:block>
							</span>
						</button>
						<button type="button" onclick="previewL140S09()">
							<span class="text-only">
								<th:block th:text="#{'button.preview'}">預覽</th:block>
							</span>
						</button><br>
						<span class="text-red">
							<th:block th:text="#{'L140S09A.msg'}"></th:block>
						</span><br>
						<div id="l140s09aGrid"></div>
						<!--</form>-->
					</div>

					<div id="l140s09bThickbox" style="display:none;">
						<!--<form id="formL140S09BDetail">-->
						<span id="s09aOid" style="display:none"></span>
						<button type="button" id="btnNewL140S09B">
							<span class="text-only">
								<th:block th:text="#{'button.add'}"></th:block>
							</span>
						</button>
						<button type="button" id="btnDelL140S09B">
							<span class="text-only">
								<th:block th:text="#{'button.delete'}">刪除</th:block>
							</span>
						</button>
						<button type="button" onclick="Panel06Action.upDown(true, 'l140s09bGrid')">
							<span class="text-only">
								<th:block th:text="#{'btn.upSeqno'}">向上移動</th:block>
							</span>
						</button>
						<button type="button" onclick="Panel06Action.upDown(false, 'l140s09bGrid')">
							<span class="text-only">
								<th:block th:text="#{'btn.downSeqno'}">向下移動</th:block>
							</span>
						</button>
						<button type="button" onclick="previewL140S09()">
							<span class="text-only">
								<th:block th:text="#{'button.preview'}">預覽</th:block>
							</span>
						</button>
						<div id="l140s09bGrid"></div>
						<!--</form>-->
					</div>

					<div id="newL140S09ABox" style="display:none;">
						<!--<form id="L140S09AForm">-->
						<table width="100%" class="tb2" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td class="hd1">
									<th:block th:text="#{'L140S09A.bizCat'}"></th:block>
								</td>
								<td>
									<select id="bizCat" name="bizCat"></select>
									<span id="isCommon" style="display:none"></span>
								</td>
							</tr>
							<tr>
								<td class="hd1">
									<th:block th:text="#{'L782M01A.loanTP'}"></th:block>
								</td>
								<td>
									<span id="loanTPsSpan"></span>
								</td>
							</tr>
							<tr>
								<td class="hd1">
									<th:block th:text="#{'L140S09A.loanTPsName'}"></th:block>
								</td>
								<td>
									<input type="text" id="loanTPsName" name="loanTPsName" maxlengthC="20"></input>(如：A、A~B，不輸入則不呈現)
								</td>
							</tr>
						</table>
						<!--</form>-->
					</div>

					<div id="newL140S09BBox" style="display:none;">
						<!--<form id="L140S09BForm">-->
						<table id="L140S09BForm">
							<tr>
								<td>
									<span class="text-red">
										<th:block th:text="#{'L140S09B.msg'}"></th:block>
									</span><br><br>
									<input type="checkbox" id="contList" name="contList"></input>
									<span id="XXDiv" class="showXXDiv" style="display:none;">
										<textarea id="XXcont" name="XXcont" cols="75" rows="3" maxlength="150" maxlengthC="450"></textarea>
									</span>
								</td>
							</tr>
						</table>
						<!--</form>-->
					</div>

					<div id="s09bDetailBox" style="display:none;">
						<!--<form id="L140S09BDetailForm">--> <!--style="white-space:pre-wrap;"> position:absolute;">-->
						<table id="L140S09BDetailForm">
							<tr>
								<td>
									<input id="isPostLoan" name="isPostLoan" type="checkbox" value="Y"></input>
									<th:block th:text="#{'L140S09B.isPostLoan'}">是否納入貸後追蹤項目</th:block><br>
									<span id="contDiv" class="showDiv" style="display:none;">
										<b class="star">
											<th:block th:text="#{'lms.ckeditRemark3'}">註1:|←建議換行</th:block>
										</b>
										<br>|←
										<textarea id="cont" name="cont" cols="75" rows="3" maxlength="1024" maxlengthC="1024"></textarea>
									</span>
									<!-- 各欄位取名規則：項目的英文 + bizCat第一碼 + bizItem +"_"+ contNo +"_"+ 第幾個 -->
									<!-- 用途、授信用途 => purpose
										 動用方式 => useWay
										 保證期限、授信期限、清償期限 => period
										 承諾事項 => commitments
										 動用條件 => useCond
										 清償方式 => payOff -->
	
									<!-- span取名規則：bizCat第一碼 + bizItem +"_"+ contNo +"_Div" -->
									<span id="A01_01_Div" class="showDiv" style="display:none;">
										<th:block th:text="#{'L140S09B.A_01.01.001'}">供</th:block>
										<input type="text" id="purposeA01_01_1" name="purposeA01_01_1" maxlengthC="100" size="30"></input>
										<th:block th:text="#{'L140S09B.A_01.01.002'}">使用。</th:block>
									</span>
									<span id="B01_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.B_01.01.001'}">供借款人</th:block>
										<input type="text" id="purposeB01_01_1" name="purposeB01_01_1" maxlengthC="100" size="30"></input>
										<th:block th:text="#{'L140S09B.B_01.01.002'}">之用。</th:block>
									</span>
									<span id="B02_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.B_02.01.001'}">憑開狀申請書開發國內外買賣方遠期信用狀，</th:block>
										<input type="text" id="useWayB02_01_1" name="useWayB02_01_1" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.B_02.01.002'}">成融資或承兌，每筆融資加承兌期間最長不逾</th:block>
										<input type="text" id="useWayB02_01_2" name="useWayB02_01_2" size="3" maxlength="3" class="digits"></input>
										<th:block th:text="#{'L140S09B.B_02.01.003'}">天，貨運提單得免以本行為CONSIGNEE(屬專案申請項目)。</th:block>
									</span>
									<span id="B02_02_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.B_02.02.001'}">以D/A、D/P、O/A方式動用時，憑借款支用書及相關單據</th:block>
										<input type="text" id="useWayB02_02_1" name="useWayB02_02_1" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.B_02.02.002'}">成動用。</th:block>
									</span>
									<span id="B02_05_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.B_02.05.001'}">動用D/A、D/P擔保提貨副提單背書合併限額</th:block>
										<input type="text" id="useWayB02_05_1" name="useWayB02_05_1" size="10" maxlength="20" class="numeric number" positiveonly="false" integer="13" fraction="2"></input>
										<th:block th:text="#{'L140S09B.B_02.05.002'}">。</th:block>
									</span>
									<span id="B03_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.B_03.01.001'}">每筆清償期限</th:block>
										<input type="text" id="periodB03_01_1" name="periodB03_01_1" maxlengthC="30" size="10"></input>
										<th:block th:text="#{'L140S09B.B_03.01.002'}">(含匯票承兌期間)。</th:block>
									</span>
									<span id="C01_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.C_01.01.001'}">憑L/C金額</th:block>
										<input type="text" id="useWayC01_01_1" name="useWayC01_01_1" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.C_01.01.002'}">成或憑D/A、D/P、O/A之</th:block>
										<input type="text" id="useWayC01_01_2" name="useWayC01_01_2" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.C_01.01.003'}">成動用，並以在本行辦理出口押匯、託收或匯入匯款所得款項償還。</th:block>
									</span>
									<span id="D02_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.D_02.01.001'}">憑保證支用書及本行認可相關保證函格式申請動用，動用時應提供保證金額</th:block>
										<input type="text" id="useWayD02_01_1" name="useWayD02_01_1" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.D_02.01.002'}">成存款設質/或備償。</th:block>
									</span>
									<span id="D03_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.D_03.01.001'}">除押標金保證最長不逾</th:block>
										<input type="text" id="periodD03_01_1" name="periodD03_01_1" size="2" maxlength="2" class="digits"></input>
										<th:block th:text="#{'L140S09B.D_03.01.002'}">年外，其他保證期限最長不逾</th:block>
										<input type="text" id="periodD03_01_2" name="periodD03_01_2" size="2" maxlength="2" class="digits"></input>
										<th:block th:text="#{'L140S09B.D_03.01.003'}">年。</th:block>
									</span>
									<span id="E01_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.E_01.01.001'}">憑保證支用書及借款人提供之財政部關稅局標準保函格式開發保證函，每筆保證期限最長不逾</th:block>
										<input type="text" id="useWayE01_01_1" name="useWayE01_01_1" size="2" maxlength="2" class="digits"></input>
										<th:block th:text="#{'L140S09B.E_01.01.002'}">年。</th:block>
									</span>
									<span id="G01_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.G_01.01.001'}">供借款人在</th:block>
										<input type="text" id="purposeG01_01_1" name="purposeG01_01_1" maxlengthC="20" size="10"></input>
										<th:block th:text="#{'L140S09B.G_01.01.002'}">支票存款第</th:block>
										<input type="text" id="purposeG01_01_2" name="purposeG01_01_2" size="14" maxlength="14" class="digits"></input>
										<th:block th:text="#{'L140S09B.G_01.01.003'}">號帳戶內，無存款或存款餘額不足支付票款時透支之用，惟須於動用期限屆滿前清償。</th:block>
									</span>
									<span id="H02_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.H_02.01.001'}">自核准日/首次動用日起</th:block>
										<input type="text" id="periodH02_01_1" name="periodH02_01_1" size="2" maxlength="2" class="digits"></input>
										<th:block th:text="#{'L140S09B.H_02.01.002'}">年。</th:block>
									</span>
									<span id="H04_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.H_04.01.001'}">按正式鑑價報告金額</th:block>
										<input type="text" id="useWayH04_01_1" name="useWayH04_01_1" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.H_04.01.002'}">成範圍內與本案額度二者取孰低作為本案動用限額，憑動用申請書申請動用。</th:block>
									</span>
									<span id="H06_04_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.H_06.04.001'}">借款人應於首動日起</th:block>
										<input type="text" id="commitmentsH06_04_1" name="commitmentsH06_04_1" size="3" maxlength="3" class="digits"></input>
										<th:block th:text="#{'L140S09B.H_06.04.002'}">個月內取得建造執照，並於取得建造執照後</th:block>
										<input type="text" id="commitmentsH06_04_2" name="commitmentsH06_04_2" size="3" maxlength="3" class="digits"></input>
										<th:block th:text="#{'L140S09B.H_06.04.003'}">個月內動工。如未如期成就，應提高利率加碼0.25%，迄改善之日止，並應申請展延相關期限。</th:block>
									</span>
									<span id="I03_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.I_03.01.001'}">銷售率達</th:block>
										<input type="text" id="useCondI03_01_1" name="useCondI03_01_1" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.I_03.01.002'}">成；或工程進度達一樓底板後；(視個案而定)。</th:block>
									</span>
									<span id="I04_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.I_04.01.001'}"></th:block>
										<input type="text" id="useWayI04_01_1" name="useWayI04_01_1" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.I_04.01.002'}">成內動撥。倘銷售率未達</th:block>
										<input type="text" id="useWayI04_01_2" name="useWayI04_01_2" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.I_04.01.003'}">成前，動用成數降為</th:block>
										<input type="text" id="useWayI04_01_3" name="useWayI04_01_3" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.I_04.01.004'}">成；俟銷售率達</th:block>
										<input type="text" id="useWayI04_01_4" name="useWayI04_01_4" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.I_04.01.005'}">成後方得補撥保留未動用之一成額度。</th:block>
									</span>
									<span id="I06_05_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.I_06.05.001'}">建物施工期間應投保營造綜合險，投保金額不得低於</th:block>
										<input type="text" id="commitmentsI06_05_1" name="commitmentsI06_05_1" size="10" maxlength="20" class="numeric number" positiveonly="false" integer="13" fraction="2"></input>。
										<th:block th:text="#{'L140S09B.I_06.05.002'}"></th:block>
									</span>
									<span id="J02_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.J_02.01.001'}">自首動日起</th:block>
										<input type="text" id="periodJ02_01_1" name="periodJ02_01_1" size="2" maxlength="2" class="digits"></input>
										<th:block th:text="#{'L140S09B.J_02.01.002'}">年。</th:block>
									</span>
									<span id="J03_03_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.J_03.03.001'}">按正式鑑價報告金額</th:block>
										<input type="text" id="useWayJ03_03_1" name="useWayJ03_03_1" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.J_03.03.002'}"></th:block>
									</span>
									<span id="J03_04_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.J_03.04.001'}"></th:block>
										<input type="text" id="useWayJ03_04_1" name="useWayJ03_04_1" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.J_03.04.002'}">成，及本行核計擔保放款值二者取孰低可動用額度。</th:block>
									</span>
									<span id="J04_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.J_04.01.001'}"></th:block>
										<input type="text" id="payOffJ04_01_1" name="payOffJ04_01_1" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.J_04.01.002'}">成清償本案借款，並應於授信期限屆期時清償剩餘本息。</th:block>
									</span>
									<span id="J05_03_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.J_05.03.001'}">授信期限屆期擬申請新案代償時，應使未銷售戶數應降至</th:block>
										<input type="text" id="commitmentsJ05_03_1" name="commitmentsJ05_03_1" size="3" maxlength="5" class="digits"></input>
										<th:block th:text="#{'L140S09B.J_05.03.002'}">戶及</th:block>
										<input type="text" id="commitmentsJ05_03_2" name="commitmentsJ05_03_2" size="3" maxlength="5" class="digits"></input>
										<th:block th:text="#{'L140S09B.J_05.03.003'}">個車位(含已簽約未交屋戶數)。</th:block>
									</span>
									<span id="N02_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.N_02.01.001'}">自首次動用日起</th:block>
										<input type="text" id="periodN02_01_1" name="periodN02_01_1" size="2" maxlength="2" class="digits"></input>
										<th:block th:text="#{'L140S09B.N_02.01.002'}">年。</th:block>
									</span>
									<span id="N03_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.N_03.01.001'}"></th:block>
										<input type="text" id="useWayN03_01_1" name="useWayN03_01_1" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.N_03.01.002'}">成內，</th:block>
									</span>
									<span id="N04_01_Div" class="showDiv">
										<th:block th:text="#{'L140S09B.N_04.01.001'}">自首次動用日起滿</th:block>
										<input type="text" id="periodN04_01_1" name="periodN04_01_1" size="3" maxlength="3" class="digits"></input>
										<th:block th:text="#{'L140S09B.N_04.01.002'}">個月之日為第一期，其後每</th:block>
										<input type="text" id="periodN04_01_2" name="periodN04_01_2" size="3" maxlength="3" class="digits"></input>
										<th:block th:text="#{'L140S09B.N_04.01.003'}">個月為一期，共分</th:block>
										<input type="text" id="periodN04_01_3" name="periodN04_01_3" size="3" maxlength="3" class="digits"></input>
										<th:block th:text="#{'L140S09B.N_04.01.004'}">期平均攤還本金。</th:block>
									</span>
									<span id="O01_01_Div" class="showDiv" style="display:none;">
										<th:block th:text="#{'L140S09B.O_01.01.001'}">供</th:block>
										<input type="text" id="purposeO01_01_1" name="purposeO01_01_1" maxlengthC="100" size="30"></input>
										<th:block th:text="#{'L140S09B.O_01.01.002'}">使用。</th:block>
									</span>
									<span id="O02_01_Div" class="showDiv" style="display:none;">
										<th:block th:text="#{'L140S09B.O_02.01.001'}">貼現票據金額</th:block>
										<input type="text" id="useWayO02_01_1" name="useWayO02_01_1" size="2" maxlength="6" class="numeric number" positiveonly="false" integer="3" fraction="2"></input>
										<th:block th:text="#{'L140S09B.O_02.01.002'}">成範圍內由銀行墊付。</th:block>
									</span>
								</td>
							</tr>
						</table>
						<!--</form>-->
					</div>

					<div id="previewBox" style="display:none;">
						<span id="previewSpan"></span>
					</div>

					<div id="actionBox" style="display:none;">
						<table>
							<tr class="isPrint_Tr">
								<td>
									<label>
										<input name="isPrintRadio" type="radio" value="Y"></input>
										<th:block th:text="#{'L140S09A.isPrint_Y'}"></th:block>
									</label>
								</td>
								<td>
									<label>
										<input name="isPrintRadio" type="radio" value="N"></input>
										<th:block th:text="#{'L140S09A.isPrint_N'}"></th:block>
									</label>
								</td>
							</tr>
							<tr class="chgLoanName_Tr">
								<td colspan="2">
									<th:block th:text="#{'L140S09A.bizCat'}"></th:block>：
									<span id="typeN_bizCatStr"></span>
									<span id="typeN_bizCat" style="display:none"></span><br>
								</td>
							</tr>
							<tr class="chgLoanName_Tr">
								<td colspan="2">
									<input type="text" id="chgLoanName" name="chgLoanName" maxlengthC="20"></input>
								</td>
							</tr>
							<tr class="bizItem_XX_Tr">
								<td class="hd1">
									<th:block th:text="#{'L140S09A.bizItem'}"></th:block>：
								</td>
								<td>
									<input type="text" id="itemStr" name="itemStr" maxlengthC="50"></input>
								</td>
							</tr>
						</table>
					</div>

					<div id="divESG" style="display:none;">
						<button type="button" id="btnESGNew" class="forview">
							<th:block th:text="#{'btn.add'}">新增</th:block>
						</button>
						<button type="button" id="btnESGMod" class="forview">
							<th:block th:text="#{'btn.mod'}">修改</th:block>
						</button>
						<button type="button" id="btnESGDel" class="forview">
							<th:block th:text="#{'btn.delete'}">刪除</th:block>
						</button>
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td>
									<div id="l140s12aGrid"></div>
								</td>
							</tr>
						</table>
					</div>
					<div id="divESGDetail" style="display:none;">
						<!--<form id="l140s12aFormDetail">-->
							<table id="l140s12aFormDetail" class="tb2" width="100%" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<td class="hd1">
										<th:block th:text="#{'L140S12A.type'}">類別</th:block>
									</td>
									<td colspan="4">
										<table width="100%" border="0" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td style="border:none;padding:0px;margin:0px;">
														<label style="letter-spacing:0px;cursor:pointer;">
															<input type="checkbox" id="esgType" name="esgType" class=""></input>
														</label>
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<!--ESG 模板選項-->
								<tr class="tr_esgMsg_type">
									<td class="hd1 esgMsgSample" rowspan="4">
										<th:block th:text="#{'L140S12A.esgModel'}">ESG模板</th:block>
									</td>
									<td id="td_esgMsgE" class="td_esgMsg" colspan="4">
										<table id="table_esgMsgE_type2" width="100%" border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td style="border:none;padding:0px;margin:0px;">
													<th:block th:text="#{'L140S12A.esgModelE'}">E環境:</th:block>
												</td>
											</tr>
											<tr class="">
												<td style="border:none;padding:0px;margin:0px;">
													<label style="letter-spacing:0px;cursor:pointer;">
														<input type="checkbox" id="esgMsgE" name="esgMsgE" class="checkbox_esgMsgAll"></input>
													</label>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr class="tr_esgMsg_type">
									<!--<td class="hd1"></td>上面rowspan-->
									<td id="td_esgMsgS" class="td_esgMsg" colspan="4">
										<table id="table_esgMsgS_type3" width="100%" border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td style="border:none;padding:0px;margin:0px;">
													<th:block th:text="#{'L140S12A.esgModelS'}">S社會責任:</th:block>
												</td>
											</tr>
											<!--永續績效連結授信條件(跟利率計價或手續費減免等優惠措施有關)-->
											<tr class="">
												<td style="border:none;padding:0px;margin:0px;">
													<label style="letter-spacing:0px;cursor:pointer;">
														<input type="checkbox" id="esgMsgS" name="esgMsgS" class="checkbox_esgMsgAll"></input>
													</label>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr class="tr_esgMsg_type">
									<!--<td class="hd1"></td>上面rowspan-->
									<td id="td_esgMsgG" class="td_esgMsg" colspan="4">
										<table id="table_esgMsgG_type3" width="100%" border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td style="border:none;padding:0px;margin:0px;">
													<th:block th:text="#{'L140S12A.esgModelG'}">G公司治理:</th:block>
												</td>
											</tr>
											<!--永續績效連結授信條件(跟利率計價或手續費減免等優惠措施有關)-->
											<tr class="">
												<td style="border:none;padding:0px;margin:0px;">
													<label style="letter-spacing:0px;cursor:pointer;">
														<input type="checkbox" id="esgMsgG" name="esgMsgG" class="checkbox_esgMsgAll"></input>
													</label>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr class="tr_esgMsgImporBtn">
									<!--<td class="hd1"></td>-->
									<td id="td_esgMsgBtn" class="td_esgMsg" colspan="4">
										<button type="button" id="btmImportEsgMsg">
											<span class="text-only">
												<th:block th:text="#{'btn.importText'}">勾選項目引入內容</th:block>
											</span>
										</button>
									</td>
								</tr>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'L140S12A.traceConditionTitle'}">設定追蹤條件</th:block>
									</td>
									<td class="hd1">
										<span class="text-red">＊</span>
										<th:block th:text="#{'L140S12A.traceCondition'}">起始追蹤日</th:block>
									</td>
									<td style="width:100px">
										<label>
											<input name="traceCondition" type="radio" value="1"></input>
											<th:block th:text="#{'L140S12A.traceCondition_1'}">首次撥款</th:block>
										</label><br>
										<label>
											<input name="traceCondition" type="radio" value="2"></input>
											<th:block th:text="#{'L140S12A.traceCondition_2'}">每次撥款</th:block>
										</label><br>
										<label>
											<input name="traceCondition" type="radio" value="3"></input>
											<th:block th:text="#{'L140S12A.traceCondition_3'}">其他</th:block>
										</label>
									</td>
									<td class="hd1 td_traceProfiling">
										<th:block th:text="#{'L140S12A.traceProfiling'}">追蹤週期</th:block>
									</td>
									<td class="td_traceProfiling">
										<label>
											<input name="traceProfiling" type="radio" value="1"></input>
											<th:block th:text="#{'L140S12A.traceProfiling_1'}">一次</th:block>
										</label><br>
										<label>
											<input name="traceProfiling" type="radio" value="2"></input>
											<th:block th:text="#{'L140S12A.traceProfiling_2'}">週期:</th:block>
											<input type="text" name="traceProfilingMonth" id="traceProfilingMonth" class="numeric" positiveonly="false" integer="3" maxlength="3" size="3"></input>
											<th:block th:text="#{'L140S12A.traceProfiling_2_1'}">月</th:block>
										</label><br>
										<label>
											<input name="traceProfiling" type="radio" value="3"></input>
											<th:block th:text="#{'L140S12A.traceProfiling_3'}">核准日後6個月內</th:block>
										</label><br>
										<label>
											<input name="traceProfiling" type="radio" value="4"></input>
											<th:block th:text="#{'L140S12A.traceProfiling_4'}">核准日後6個月內第一次，其後每12個月一次(中長期適用)</th:block>
										</label>
									</td>
								</tr>
								<tr>
									<td colspan="5">
										<span class="color-red">
											<b>
												<span id="showDocStatus">
													<th:block th:text="#{'L140S12A.promptMsg_1'}">[請注意將同一次要追蹤的所有工作應寫在一同一追蹤內容。若簽報有拆分表述需要時，可於轉入主表後，再依需要重新編輯。]</th:block>
												</span>
											</b>
										</span>
									</td>
								</tr>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'L140S12A.contentText'}">內容</th:block>
									</td>
									<td colspan="4">
										<textarea id="contentText" name="contentText" maxlengthc="500" rows="10" cols="90" class="nodisabled fullText"></textarea>
									</td>
								</tr>
							</table>
						<!--</form>-->
					</div>
				</td>
			</tr>
		</table>
	</th:block>
</body>
</html>
