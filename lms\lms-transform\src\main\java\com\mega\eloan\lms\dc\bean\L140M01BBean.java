package com.mega.eloan.lms.dc.bean;

import java.io.Serializable;

import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

public class L140M01BBean extends BaseBean implements Serializable {
	private static final long serialVersionUID = 7135046786082541150L;

	private String itemType = "";// 項目類別
	private String pageNum = "";// 列印於主表
	private String itemDscr = "";// 項目說明
	private String toALoan = "";// 項目補充

	public String getItemType() {
		return itemType;
	}

	public void setItemType(String itemType) {
		this.itemType = itemType;
	}

	public String getPageNum() {
		return pageNum;
	}

	public void setPageNum(String pageNum) {
		this.pageNum = pageNum;
	}

	public String getItemDscr() {
		return itemDscr;
	}

	public void setItemDscr(String itemDscr) {
		this.itemDscr = itemDscr;
	}

	public String getToALoan() {
		return toALoan;
	}

	public void setToALoan(String toALoan) {
		this.toALoan = toALoan;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer();
		sb.append(Util.nullToSpace(this.getOid()))
				.append(TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getMainId()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getItemType()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getPageNum()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getItemDscr()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getToALoan()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(this.getCreator()).append(TextDefine.FILE_DELIM);
		sb.append(this.getCreateTime()).append(TextDefine.FILE_DELIM);
		sb.append(this.getUpdater()).append(TextDefine.FILE_DELIM);
		sb.append(this.getUpdateTime());
		return sb.toString();
	}

}
