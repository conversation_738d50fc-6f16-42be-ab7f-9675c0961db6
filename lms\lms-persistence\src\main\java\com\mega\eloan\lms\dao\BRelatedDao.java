/* 
 * BRelatedDao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.BRelated;
import com.mega.eloan.lms.model.BRelated.DocTypeEnum;

/**
 * <pre>
 * 引用資料關聯記錄
 * </pre>
 * 
 * @since 2011/9/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/30,billWang,new
 *          </ul>
 */
public interface BRelatedDao extends IGenericDao<BRelated> {
//	int deleteByBRelated(String mainId1, String docType2);

//	int deleteByTab(String mainId1, String tab, String subtab);
//	
//	int deleteByTab2(String mainId1, String tab, String subtab,String pid1);

	BRelated findBymainId1AnddocType2(String mainId1, String docType2);
	
	List<BRelated> findByAll(String mainId1, String docType1, String docType2, String tab, String subtab, String relatedflag, boolean icludeFlag);

	/**
	 * 取得引用文件資訊
	 * 
	 * @param mainId1
	 *            引用文件之mainId
	 * @param docType2
	 *            被引用文件之類型
	 * @return List<BRelated>
	 */
	List<BRelated> findByMainIdAndDocType2(String mainId1, DocTypeEnum docType2);
	
	List<BRelated> findByMainId(String mainId);
}
