/* 
 * L160M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;


import com.mega.eloan.lms.model.L160M01B;

/** 動審表額度序號資料 **/
public interface L160M01BDao extends IGenericDao<L160M01B> {

	L160M01B findByOid(String oid);

	List<L160M01B> findByMainId(String mainId);

	L160M01B findByUniqueKey(String mainId, String cntrNo);

	List<L160M01B> findByIndex01(String mainId, String cntrNo);

	List<L160M01B> findByCntrNo(String CntrNo);
	
	public List<L160M01B> findByReMainId(String cntrMainId);
}