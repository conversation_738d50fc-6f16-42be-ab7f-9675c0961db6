/* 
 * L120S04DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S04D;

/** 各項往來資料檔 **/
public interface L120S04DDao extends IGenericDao<L120S04D> {

	L120S04D findByOid(String oid);
	
	List<L120S04D> findByMainId(String mainId);
	
	L120S04D findByUniqueKey(String mainId, String keyCustId, String keyDupNo);

	List<L120S04D> findByIndex01(String mainId, String keyCustId, String keyDupNo);
}