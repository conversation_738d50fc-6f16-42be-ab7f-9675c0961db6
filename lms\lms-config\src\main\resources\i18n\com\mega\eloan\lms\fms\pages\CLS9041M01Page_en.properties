sendQ=determining submitted
outputDoc=generate word file
createQ=generate reports
sure=OK
close=Close
uploadTxt=upload files
deleteTxt=delete
onlyOneFile=only sum data, delete them, and then upload
noFileError=upload files
sendSuccess=submitted successfully

#==================================================
# \u52d5\u7528\u5be9\u6838\u8868\u7a3d\u6838\u9805\u76ee
#==================================================
C004M01A.detailTilte=submit information
C004M01A.date=Data Date
C004M01A.rptType=statements Category
Data from the date C004M01A.bgnDate =
C004M01A.endDate=date are to
C004M01A.rptDate=determine the submitted date
C004M01A.rptName=Report Name
C004M01A.filePath=Additional file path
C004M01A.fileName=attached file name
C004M01A.rmk=Remarks
C004M01A.creator=establish personnel numbers
C004M01A.createTime=creation date
C004M01A.updater =The transaction staff numbers for 
C004M01A.updateTime=transaction date
C004M01A.confirmDelete=whether to delete such data?
C004M01A.S1=S1
C004M01A.Q1=Q1
C004M01A.S2=S2
C004M01A.Q2=Q2
C004M01A.S3=S3
C004M01A.Q3=Q3
C004M01A.S1Name=Student loans S1 \u200b\u200bpolicy statements
C004M01A.uploadData=upload data
C004M01A.delData=delete data
C004M01A.createQ1=produce Q1 statements
#==================================================
# \u653f\u7b56\u6027\u7559\u5b78\u751f\u8cb8\u6b3e\u5831\u9001\u8cc7\u6599Q\u6a94
#==================================================
C004S01A.oid=oid
C004S01A.mainId=mainId
C004S01A.brno=Branch Code
C004S01A.num=number of
C004S01A.amt=loan amount
C004S01A.oweInterest=owed \u200b\u200binterest
C004S01A.dueInterest=overdue interest
C004S01A.litigate=cost of litigation
C004S01A.creator=establish personnel numbers
C004S01A.createTime=creation date
C004S01A.updater =The transaction staff numbers for 
C004S01A.updateTime=transaction date
C004S01A.outputWord=output word file
