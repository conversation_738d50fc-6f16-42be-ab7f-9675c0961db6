/* 
 * LMS1405ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.CodeTypeEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.SQLParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.Table;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSLgdService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C100M01Dao;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01BDao;
import com.mega.eloan.lms.dao.L120M01CDao;
import com.mega.eloan.lms.dao.L120M01FDao;
import com.mega.eloan.lms.dao.L120M01GDao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L120S01DDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01ATMP1Dao;
import com.mega.eloan.lms.dao.L140M01BDao;
import com.mega.eloan.lms.dao.L140M01CDao;
import com.mega.eloan.lms.dao.L140M01DDao;
import com.mega.eloan.lms.dao.L140M01EDao;
import com.mega.eloan.lms.dao.L140M01E_AFDao;
import com.mega.eloan.lms.dao.L140M01FDao;
import com.mega.eloan.lms.dao.L140M01GDao;
import com.mega.eloan.lms.dao.L140M01HDao;
import com.mega.eloan.lms.dao.L140M01IDao;
import com.mega.eloan.lms.dao.L140M01JDao;
import com.mega.eloan.lms.dao.L140M01KDao;
import com.mega.eloan.lms.dao.L140M01MDao;
import com.mega.eloan.lms.dao.L140M01ODao;
import com.mega.eloan.lms.dao.L140M01O_0307Dao;
import com.mega.eloan.lms.dao.L140M01QDao;
import com.mega.eloan.lms.dao.L140M01TDao;
import com.mega.eloan.lms.dao.L140M02ADao;
import com.mega.eloan.lms.dao.L140M03ADao;
import com.mega.eloan.lms.dao.L140S04ADao;
import com.mega.eloan.lms.dao.L140S06ADao;
import com.mega.eloan.lms.dao.L140S08ADao;
import com.mega.eloan.lms.dao.L140S12ADao;
import com.mega.eloan.lms.dao.L782A01ADao;
import com.mega.eloan.lms.dao.L782M01ADao;
import com.mega.eloan.lms.dao.L999LOG01ADao;
import com.mega.eloan.lms.dw.service.DWASLNOVEROVSService;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.lms.bean.CountTOT;
import com.mega.eloan.lms.lms.bean.CountTOTAction;
import com.mega.eloan.lms.lms.pages.LMS1015M01Page;
import com.mega.eloan.lms.lms.pages.LMS1405S02Page;
import com.mega.eloan.lms.lms.pages.LMSS02Page;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel05;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.mfaloan.bean.ELF515;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF442Service;
import com.mega.eloan.lms.mfaloan.service.MisELF515Service;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF150Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF226Service;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.mfaloan.service.MisRatetblService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C100M01;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01B;
import com.mega.eloan.lms.model.C121M01C;
import com.mega.eloan.lms.model.C121M01D;
import com.mega.eloan.lms.model.C121M01F;
import com.mega.eloan.lms.model.C121M01G;
import com.mega.eloan.lms.model.C121M01H;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120M01G;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.eloan.lms.model.L120S11A;
import com.mega.eloan.lms.model.L120S11A_LOC;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01ATMP1;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01D;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.eloan.lms.model.L140M01E_AF;
import com.mega.eloan.lms.model.L140M01F;
import com.mega.eloan.lms.model.L140M01G;
import com.mega.eloan.lms.model.L140M01H;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L140M01K;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M01O_0307;
import com.mega.eloan.lms.model.L140M01Q;
import com.mega.eloan.lms.model.L140M01T;
import com.mega.eloan.lms.model.L140M02A;
import com.mega.eloan.lms.model.L140M03A;
import com.mega.eloan.lms.model.L140S04A;
import com.mega.eloan.lms.model.L140S05A;
import com.mega.eloan.lms.model.L140S06A;
import com.mega.eloan.lms.model.L140S08A;
import com.mega.eloan.lms.model.L140S11A;
import com.mega.eloan.lms.model.L140S12A;
import com.mega.eloan.lms.model.L782A01A;
import com.mega.eloan.lms.model.L782M01A;
import com.mega.eloan.lms.model.L999LOG01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 額度明細表
 * </pre>
 * 
 * @since 2011/9/27
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/27,REX,new
 *          </ul>
 */
@Service
public class LMS1405ServiceImpl extends AbstractCapService implements
		LMS1405Service {

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L120M01FDao l120m01fDao;

	@Resource
	L120M01BDao l120m01bDao;

	@Resource
	L120M01CDao l120m01cDao;

	@Resource
	C120M01ADao c120m01aDao;
	@Resource
	L120S01ADao l120s01aDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L140M01BDao l140m01bDao;

	@Resource
	L140M01CDao l140m01cDao;

	@Resource
	L140M01DDao l140m01dDao;

	@Resource
	L140M01EDao l140m01eDao;

	@Resource
	L140M01E_AFDao l140m01e_afDao;
	
	@Resource
	L140M01FDao l140m01fDao;

	@Resource
	L140M01GDao l140m01gDao;

	@Resource
	L140M01HDao l140m01hDao;

	@Resource
	L140M01IDao l140m01iDao;

	@Resource
	L140M01KDao l140m01kDao;

	@Resource
	L140M01ODao l140m01oDao;
	
	@Resource
	L140S12ADao l140s12aDao;

	@Resource
	L782M01ADao l782m01aDao;

	@Resource
	L782A01ADao l782a01aDao;

	@Resource
	L140M02ADao l140m02aDao;

	@Resource
	L120S01BDao l120s01bDao;

	@Resource
	C120S01ADao c120s01aDao;

	@Resource
	L140M01QDao l140m01qDao;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	DwdbBASEService dwdbBaseService;

	@Resource
	EloandbBASEService eloandbService;

	@Resource
	EloandbcmsBASEService eloandbcmsService;

	@Resource
	MisELF442Service misELF442Service;

	@Resource
	MisLNF226Service misLNF226Service;

	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	MisMislnratService misMislnratService;
	@Resource
	DwLnquotovService dwLnquotovService;

	@Resource
	DWASLNOVEROVSService dWASLNOVEROVSService;

	@Resource
	MisLNF022Service misLNF022Service;

	@Resource
	MisLNF150Service misLNF150Service;
	@Resource
	MisRatetblService misRatetblService;
	@Resource
	LMSService lmsService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisGrpcmpService misGrpcmpService;
	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	DocFileService docFileService;
	@Resource
	C100M01Dao c100m01Dao;

	@Resource
	L140M01JDao l140m01jDao;

	@Resource
	LMS1205Service lms1205Service;

	@Resource
	CLSService clsService;

	@Resource
	L120S01DDao l120s01dDao;

	@Resource
	LMS1205Service service1205;

	// J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
	@Resource
	L140S04ADao l140s04aDao;

	@Resource
	L140M01ATMP1Dao l140m01atmp1Dao;

	@Resource
	L999LOG01ADao l999log01aDao;

	@Resource
	L140M01TDao l140m01tDao;

	@Resource
	MisELF515Service misELF515Service;

	// J-108-0293 Web e-Loan 未依銀行內部規定
	@Resource
	L140S06ADao l140s06aDao;

	// J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	@Resource
	L140M01O_0307Dao l140m01o_0307Dao;

	// J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
	@Resource
	L140S08ADao l140s08aDao;

	// J-110-0211_11557_B1001 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
	@Resource
	L120M01GDao l120m01gDao;

	@Resource
	L140M03ADao l140m03aDao;

	@Resource
	L140M01MDao l140m01mDao;

	@Resource
	LMSLgdService lmsLgdService;

	private static final Logger logger = LoggerFactory
			.getLogger(LMS1405ServiceImpl.class);

	private static final String MARKSTAR = "※";
	private static final String MARKCOLON = "：";
	private static final String MARKCOMMA = "，";
	private static final String MARKPERIOD = "。";
	private static final String MARKVIRGULE = "/";
	private static final String MARKDAN = "、";
	private static final String HTMLR = "\r";
	private static final String SEP_SIGN_TEXT = "\r\n";
	private static final String 授信額度合計 = "loanTotAmt";
	private static final String 擔保授信合計 = "gtAmt";
	private static final String 衍生性商品原始合計 = "loanTotZAmt";
	private static final String 衍生性商品相當合計 = "loanTotLAmt";
	private static final String 前准額度批覆合計 = "LVTOTAMT";
	private static final String 前准額度批覆擔保合計 = "LVASSTOTAMT";
	private static final String 減額額度合計 = "MinTOTAMT";
	private static final String 減額擔保額度合計 = "MinAssTOTAMT";
	private static final String 授信額度合計多幣別說明 = "MultiAmt";
	private static final String 擔保授信合計多幣別說明 = "MultiAssureAmt";
	private static final BigDecimal 一百 = new BigDecimal(100);
	private static final String 衍生性授信額度合計多幣別說明 = "DervMultiAmt";
	// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
	// private static final String LGD其中無擔保合計 = "lgdTotAmt_U";
	// private static final String LGD其中擬制部分擔保合計 = "lgdTotAmt_P";
	// private static final String LGD其中擬制十足擔保合計 = "lgdTotAmt_S";
	// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	private static final String LGD合計 = "lgdTotAmt";
	// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
	private static final String 瑕疵押匯額度合計 = "flawAmtTotal";
	private static final String 總授信額度合計 = "generalLoanTotAmt";
	// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
	private static final String 單獨另計授權額度合計 = "standAloneAuthTotal";

	// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
	private static final String 瑕疵押匯額度合計_全案 = "flawAmtMgTotal";
	private static final String 總授信額度合計_全案 = "generalLoanMgTotAmt";
	private static final String LGD合計_全案 = "lgdTotMgAmt";
	private static final String LGD合計_全案_幣別 = "lgdTotMgCurr";

	// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
	private static final String 瑕疵押匯額度合計_合併關係 = "flawAmtRcTotal";
	private static final String 總授信額度合計_合併關係 = "generalLoanRcTotAmt";
	private static final String LGD合計_合併關係 = "lgdTotRcAmt";

	private static final String 行業別代碼 = "L";
	private static final String 集團代碼 = "M";
	private static final String 國別代碼 = "N";
	private static final String 區域代碼 = "O";
	private static final String 單一授信戶 = "P";
	private static final String 產品種類 = "Q";

	private static final String 集團代碼_資料建檔07 = "GRP-07";

	@Override
	public void deleteL140m01(String mainId) {
		L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (l140m01a != null) {
			l140m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l140m01a.setUpdater(user.getUserId());
			save(l140m01a);
		}

	}

	@Override
	public void saveL140m01aList(List<L140M01A> list) {
		l140m01aDao.save(list);
	}

	@Override
	public void delL140m01aList(List<L140M01A> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L140M01A l140m01a : list) {
			l140m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l140m01a.setUpdater(user.getUserId());
		}
		l140m01aDao.save(list);
	}

	@Override
	public L120M01B findL120m01bByUniqueKey(String mainId) {
		return l120m01bDao.findByUniqueKey(mainId);
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L140M01A.class) {
			return l140m01aDao.findPage(search);
		} else if (clazz == L140M01C.class) {
			return l140m01cDao.findPage(search);
		} else if (clazz == L140M01D.class) {
			return l140m01dDao.findPage(search);
		} else if (clazz == L140M01E.class) {
			return l140m01eDao.findPage(search);
		} else if (clazz == L140M01E_AF.class) {
			return l140m01e_afDao.findPage(search);
		} else if (clazz == L140M01F.class) {
			return l140m01fDao.findPage(search);
		} else if (clazz == L140M01G.class) {
			return l140m01gDao.findPage(search);
		} else if (clazz == L140M01I.class) {
			return l140m01iDao.findPage(search);
		} else if (clazz == L120M01C.class) {
			return l120m01cDao.findPage(search);
		} else if (clazz == L140M01K.class) {
			return l140m01kDao.findPage(search);
		} else if (clazz == L140M01O.class) {
			return l140m01oDao.findPage(search);
		} else if (clazz == C100M01.class) {
			return c100m01Dao.findPage(search);
		} else if (clazz == L140M01J.class) {
			return l140m01jDao.findPage(search);
		} else if (clazz == L140S04A.class) {
			// J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
			return l140s04aDao.findPage(search);
		} else if (clazz == L140M01ATMP1.class) {
			// J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
			return l140m01atmp1Dao.findPage(search);
		} else if (clazz == L140M01T.class) {
			return l140m01tDao.findPage(search);
		} else if (clazz == L140S06A.class) {
			// J-108-0293 Web e-Loan 未依銀行內部規定
			return l140s06aDao.findPage(search);
		} else if (clazz == L140M01O_0307.class) {
			// J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
			return l140m01o_0307Dao.findPage(search);
		}

		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findModelListByMainId(Class clazz,
			String mainId) {
		if (clazz == L140M01B.class) {
			return l140m01bDao.findByMainId(mainId);
		} else if (clazz == L140M01C.class) {
			return l140m01cDao.findByMainId(mainId);
		} else if (clazz == L140M01D.class) {
			return l140m01dDao.findByMainId(mainId);
		} else if (clazz == L140M01E.class) {
			return l140m01eDao.findByMainId(mainId);
		}  else if (clazz == L140M01E_AF.class) {
			return l140m01e_afDao.findByMainId(mainId);
		} else if (clazz == L140M01F.class) {
			return l140m01fDao.findByMainId(mainId);
		} else if (clazz == L140M01G.class) {
			return l140m01gDao.findByMainId(mainId);
		} else if (clazz == L140M01I.class) {
			// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
			return l140m01iDao.findByMainIdWithAllRType(mainId);
		} else if (clazz == L120M01C.class) {
			return l120m01cDao.findByMainId(mainId);
		} else if (clazz == L120S01A.class) {
			return l120s01aDao.findByMainId(mainId);
		} else if (clazz == L140M01K.class) {
			return l140m01kDao.findByMainId(mainId);
		} else if (clazz == L140M01O.class) {
			return l140m01oDao.findByMainId(mainId);
		} else if (clazz == L140M01J.class) {
			return l140m01jDao.findByMainId(mainId);
		} else if (clazz == L140M01A.class) {
			// //J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
			return l140s04aDao.findByMainId(mainId);
		} else if (clazz == L140M01T.class) {
			return l140m01tDao.findByMainId(mainId);
		} else if (clazz == L140M01O_0307.class) {
			// J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
			return l140m01o_0307Dao.findByMainId(mainId);
		}

		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L140M01A) {
					((L140M01A) model).setUpdater(user.getUserId());
					((L140M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01aDao.save((L140M01A) model);
				} else if (model instanceof L140M01B) {
					((L140M01B) model).setUpdater(user.getUserId());
					((L140M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01bDao.save((L140M01B) model);
				} else if (model instanceof L140M01C) {
					((L140M01C) model).setUpdater(user.getUserId());
					((L140M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01cDao.save((L140M01C) model);
				} else if (model instanceof L140M01D) {
					((L140M01D) model).setUpdater(user.getUserId());
					((L140M01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01dDao.save((L140M01D) model);
				} else if (model instanceof L140M01E) {
					((L140M01E) model).setUpdater(user.getUserId());
					((L140M01E) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01eDao.save((L140M01E) model);
				} else if (model instanceof L140M01E_AF) {
					((L140M01E_AF) model).setUpdater(user.getUserId());
					((L140M01E_AF) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01e_afDao.save((L140M01E_AF) model);
				} else if (model instanceof L140M01F) {
					((L140M01F) model).setUpdater(user.getUserId());
					((L140M01F) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01fDao.save((L140M01F) model);
				} else if (model instanceof L140M01G) {
					((L140M01G) model).setUpdater(user.getUserId());
					((L140M01G) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01gDao.save((L140M01G) model);
				} else if (model instanceof L140M01H) {
					((L140M01H) model).setUpdater(user.getUserId());
					((L140M01H) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01hDao.save((L140M01H) model);
				} else if (model instanceof L140M01I) {
					((L140M01I) model).setUpdater(user.getUserId());
					((L140M01I) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01iDao.save((L140M01I) model);
				} else if (model instanceof L120M01C) {
					((L120M01C) model).setUpdater(user.getUserId());
					((L120M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120m01cDao.save((L120M01C) model);
				} else if (model instanceof L120M01B) {
					((L120M01B) model).setUpdater(user.getUserId());
					((L120M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120m01bDao.save((L120M01B) model);
				} else if (model instanceof L782M01A) {
					((L782M01A) model).setUpdater(user.getUserId());
					((L782M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l782m01aDao.save((L782M01A) model);
				} else if (model instanceof L140M02A) {
					((L140M02A) model).setUpdater(user.getUserId());
					((L140M02A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m02aDao.save((L140M02A) model);
				} else if (model instanceof L782A01A) {
					l782a01aDao.save((L782A01A) model);
				} else if (model instanceof L140M01K) {
					((L140M01K) model).setUpdater(user.getUserId());
					((L140M01K) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01kDao.save((L140M01K) model);
				} else if (model instanceof L140M01O) {
					((L140M01O) model).setUpdater(user.getUserId());
					((L140M01O) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01oDao.save((L140M01O) model);
				} else if (model instanceof L140M01J) {
					((L140M01J) model).setUpdater(user.getUserId());
					((L140M01J) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01jDao.save((L140M01J) model);
				} else if (model instanceof L140S04A) {
					// J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
					((L140S04A) model).setUpdater(user.getUserId());
					((L140S04A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140s04aDao.save((L140S04A) model);
				} else if (model instanceof L999LOG01A) {
					// J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
					((L999LOG01A) model).setUpdater(user.getUserId());
					((L999LOG01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l999log01aDao.save((L999LOG01A) model);
				} else if (model instanceof L140M01T) {
					((L140M01T) model).setUpdater(user.getUserId());
					((L140M01T) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01tDao.save((L140M01T) model);
				} else if (model instanceof L140S06A) {
					// J-108-0293 Web e-Loan 未依銀行內部規定
					((L140S06A) model).setUpdater(user.getUserId());
					((L140S06A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140s06aDao.save((L140S06A) model);
				} else if (model instanceof L140M01O_0307) {
					// J-108-0225_05097_B1001 Web
					// e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
					((L140M01O_0307) model).setUpdater(user.getUserId());
					((L140M01O_0307) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m01o_0307Dao.save((L140M01O_0307) model);
				} else if (model instanceof L120M01G) {
					// J-110-0211_11557_B1001
					// 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
					l120m01gDao.save((L120M01G) model);
				} else if (model instanceof L140M03A) {
					// J-110-0211_11557_B1001
					// 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
					l140m03aDao.save((L140M03A) model);
				} else if (model instanceof L140S12A) {
					// J-113-0035 e-Loan企金授信額度明細表新增貸後管裡追蹤分項
					((L140S12A) model).setUpdater(user.getUserId());
					((L140S12A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140s12aDao.save((L140S12A) model);
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L140M01A) {
					((L140M01A) model).setDeletedTime(CapDate
							.getCurrentTimestamp());
					save((L140M01A) model);
				} else if (model instanceof L120M01C) {
					l120m01cDao.delete((L120M01C) model);
				} else if (model instanceof L140M01B) {
					l140m01bDao.delete((L140M01B) model);
				} else if (model instanceof L140M01C) {
					l140m01cDao.delete((L140M01C) model);
				} else if (model instanceof L140M01D) {
					l140m01dDao.delete((L140M01D) model);
				} else if (model instanceof L140M01E) {
					l140m01eDao.delete((L140M01E) model);
				} else if (model instanceof L140M01F) {
					l140m01fDao.delete((L140M01F) model);
				} else if (model instanceof L140M01G) {
					l140m01gDao.delete((L140M01G) model);
				} else if (model instanceof L140M01H) {
					l140m01hDao.delete((L140M01H) model);
				} else if (model instanceof L140M01I) {
					l140m01iDao.delete((L140M01I) model);
				} else if (model instanceof L140M01K) {
					l140m01kDao.delete((L140M01K) model);
				} else if (model instanceof L140S04A) {
					// J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
					l140s04aDao.delete((L140S04A) model);
				} else if (model instanceof L140M01T) {
					l140m01tDao.delete((L140M01T) model);
				} else if (model instanceof L140M01O_0307) {
					// J-108-0225_05097_B1001 Web
					// e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
					l140m01o_0307Dao.delete((L140M01O_0307) model);
				} else if (model instanceof L120M01G) {
					// J-110-0211_11557_B1001
					// 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
					l120m01gDao.delete((L120M01G) model);
				}
			}

		}
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L140M01A.class) {
			return (T) l140m01aDao.findByOid(oid);
		} else if (clazz == L140M01B.class) {
			return (T) l140m01bDao.findByOid(oid);
		} else if (clazz == L140M01C.class) {
			return (T) l140m01cDao.findByOid(oid);
		} else if (clazz == L140M01D.class) {
			return (T) l140m01dDao.findByOid(oid);
		} else if (clazz == L140M01E.class) {
			return (T) l140m01eDao.findByOid(oid);
		} else if (clazz == L140M01E_AF.class) {
			return (T) l140m01e_afDao.findByOid(oid);
		} else if (clazz == L140M01F.class) {
			return (T) l140m01fDao.findByOid(oid);
		} else if (clazz == L140M01G.class) {
			return (T) l140m01gDao.findByOid(oid);
		} else if (clazz == L140M01H.class) {
			return (T) l140m01hDao.findByOid(oid);
		} else if (clazz == L120M01C.class) {
			return (T) l120m01cDao.findByOid(oid);
		} else if (clazz == L140M01I.class) {
			return (T) l140m01iDao.findByOid(oid);
		} else if (clazz == L140M02A.class) {
			return (T) l140m02aDao.findByOid(oid);
		} else if (clazz == L140M01K.class) {
			return (T) l140m01kDao.findByOid(oid);
		} else if (clazz == L140M01O.class) {
			return (T) l140m01oDao.findByOid(oid);
		} else if (clazz == L140M01J.class) {
			return (T) l140m01jDao.findByOid(oid);
		} else if (clazz == L140S04A.class) {
			// J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
			return (T) l140s04aDao.findByOid(oid);
		} else if (clazz == L140M01T.class) {
			return (T) l140m01tDao.findByOid(oid);
		} else if (clazz == L140S06A.class) {
			// J-108-0293 Web e-Loan 未依銀行內部規定
			return (T) l140s06aDao.findByOid(oid);
		} else if (clazz == L140M01O_0307.class) {
			// J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
			return (T) l140m01o_0307Dao.findByOid(oid);
		}
		return null;
	}

	@Override
	public L140M01G findL140m01gByUniqueKey(String mainId, Integer rateSeq,
			String rateType) {
		return l140m01gDao.findByUniqueKey(mainId, rateSeq, rateType);
	}

	@Override
	public int findL140m01fByMainIdMax(String mainId) {
		List<L140M01F> l140m01fs = l140m01fDao.findByMainId(mainId);
		int count = 0;
		int seqval = 0;
		for (L140M01F l140m01f : l140m01fs) {// 取出這個mainID底下的最大Seq
			seqval = l140m01f.getRateSeq();
			if (seqval > count) {
				count = seqval;
			}
		}
		return ++count;
	}

	@Override
	public int findL140m01dByMainIdAndlmtTypeMax(String mainId, String lmtType) {// 取得尚未使用的seq
		List<L140M01D> l140m01ds = l140m01dDao.findByMainIdAndlmtType(mainId,
				lmtType);
		int count = 0;
		int seqval = 0;
		for (L140M01D l140m01d : l140m01ds) {// 取出這個mainID底下的最大Seq
			seqval = l140m01d.getLmtSeq();
			if (seqval > count) {
				count = seqval;
			}
		}
		return ++count;
	}

	@Override
	public List<L140M01G> findL140m01gListByMainIdAndRateSeq(String mainId,
			Integer rateSeq) {
		return l140m01gDao.findByMainIdAndRateSeq(mainId, rateSeq);
	}

	@Override
	public L140M01F findL140m01fByUniqueKey(String mainId, Integer rateSeq) {
		return l140m01fDao.findByUniqueKey(mainId, rateSeq);
	}

	@Override
	public void deleteL140m01fList(String[] oids, String[] seqs, String mainId) {
		L140M01F l140m01f = l140m01fDao.find(oids[0]);
		// String mainId = l140m01f.getMainId();
		for (int i = 0, all = oids.length; i < all; i++) {
			delete(l140m01fDao.find(oids[i]));
		}
		for (int j = 0, SeqlistAll = seqs.length; j < SeqlistAll; j++) {
			List<L140M01G> list = l140m01gDao.findByMainIdAndRateSeq(mainId,
					Util.parseInt(seqs[j]));
			List<L140M01H> listh = l140m01hDao.findByMainIdAndRateSeq(mainId,
					Util.parseInt(seqs[j]));
			l140m01gDao.delete(list);
			l140m01hDao.delete(listh);
		}
	}

	@Override
	public List<L120M01C> findL120m01cListByMainId(String caseMainId) {
		return l120m01cDao.findByMainId(caseMainId);
	}

	@Override
	public L140M01H findL140m01hByUniqueKey(String mainId, Integer rateSeq) {
		return l140m01hDao.findByUniqueKey(mainId, rateSeq);
	}

	@Override
	public L140M01B findL140m01bUniqueKey(String mainId, String itemType) {
		return l140m01bDao.findByUniqueKey(mainId, itemType);
	}

	@Override
	public L140M01D findL140m01dUniqueKey(String mainId, String lmtType,
			Integer lmtSeq) {
		return l140m01dDao.findByUniqueKey(mainId, lmtType, lmtSeq);
	}

	@Override
	public void deleteL140m01dList(String[] oids) {
		List<L140M01D> l140m01dList = l140m01dDao.findByOids(oids);
		if (!l140m01dList.isEmpty()) {
			l140m01dDao.delete(l140m01dList);
		}
	}

	@Override
	public void deleteL140m01cList(String[] oids, String mainId) {
		List<L140M01C> l140m01cList = l140m01cDao.findByOids(oids);

		if (!l140m01cList.isEmpty()) {
			l140m01cDao.delete(l140m01cList);
		}
		L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
		l140m01a.setChkYN(null);
		// 國外部建議事項 修改項目5 額度明細表新增差異數欄位，由經辦自行輸入，再由系統納入合計計算(本欄位不列印於額度明細表)
		l140m01a.setCntrChgOid("");
		l140m01aDao.save(l140m01a);
	}

	@Override
	public L140M01E findL140m01eByUniqueKey(String mainId, String shareBrId) {
		return l140m01eDao.findByUniqueKey(mainId, shareBrId);
	}

	@Override
	public void deleteL140m01eList(String[] oids) {
		List<L140M01E> l140m01eList = l140m01eDao.findByOids(oids);

		if (!l140m01eList.isEmpty()) {
			l140m01eDao.delete(l140m01eList);
		}

	}

	@Override
	public String queryBaseRate(String type, String curr) {
		List<?> rows = this.misMislnratService.findMislnratByLRRate(type, curr);

		CapAjaxFormResult result = new CapAjaxFormResult(rows.get(0));
		return result.get("LR_RATE").toString();
	}

	@Override
	public void saveL140m01bList(List<L140M01B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L140M01B l140m01b : list) {
			l140m01b.setUpdater(user.getUserId());
			l140m01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l140m01bDao.save(list);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.service.LMS1405Service#convertMark(net.sf.json.JSONArray
	 * )
	 */
	@Override
	public String convertMark(JSONArray word) {
		StringBuilder temp = new StringBuilder("");
		temp.setLength(0);
		for (int b = 0, count = word.size(); b < count; b++) {
			temp.append(temp.length() > 0 ? UtilConstants.Mark.MARK : "");
			temp.append(word.get(b));
		}
		return temp.toString();
	}

	@Override
	public String[] convertMark(String word) {
		return word.split(UtilConstants.Mark.SPILT_MARK);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1405Service#saveL140m01bDscr1(java.
	 * lang.String, java.lang.String)
	 */
	@Override
	public String saveL140m01bDscr1(String mainId, String pageNum) {
		return this.saveL140m01bDscr1(mainId, pageNum, true);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.service.LMS1405Service#toSavaDscr1(java.lang.String,
	 * java.lang.String)
	 */
	@Override
	public String saveL140m01bDscr1(String mainId, String pageNum,
			Boolean copyL140M01E) {
		Map<String, String> codeMap = codeTypeService
				.findByCodeType(UtilConstants.CodeTypeItem.授信科目);
		// 全部的描述
		StringBuilder drc = new StringBuilder("");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		// 科子目描述
		StringBuilder subjectdrc1 = new StringBuilder("");
		// 科子目合併描述
		StringBuilder subjectdrc2 = new StringBuilder("");
		// 攤貸比率描述
		StringBuilder subjectdrc3 = new StringBuilder("");
		L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
		List<L140M01D> modelD = l140m01dDao.findByMainId(mainId);
		List<L140M01E> modelE = l140m01eDao.findByMainId(mainId);
		for (L140M01D modeld : modelD) {
			String[] subject = convertMark(modeld.getSubject());

			if ("1".equals(modeld.getLmtType())) {
				// 授信科目

				subjectdrc1.append(codeMap.get(subject[0]));
				for (int i = 1, subjects = subject.length; i < subjects; i++) {
					subjectdrc1.append(MARKDAN + codeMap.get(subject[i]));
				}// close for
				subjectdrc1.append(pop2.get("L140M01d.lmtMoney"));
				subjectdrc1.append(" ");
				subjectdrc1.append(modeld.getLmtCurr());
				subjectdrc1.append(" ");
				subjectdrc1.append(NumConverter.addComma(modeld.getLmtAmt()));
				subjectdrc1.append(" ");
				// other.money = 元
				subjectdrc1.append(pop2.get("other.money") + MARKCOMMA);
			} else {
				subjectdrc2.append(codeMap.get(subject[0]));
				for (int i = 1, subjects = subject.length; i < subjects; i++) {
					subjectdrc2.append(MARKDAN + codeMap.get(subject[i]));
				}// close for
				subjectdrc2.append(pop2.get("L140M01d.lmtMoney2"));
				subjectdrc2.append(" ");
				subjectdrc2.append(modeld.getLmtCurr());
				subjectdrc2.append(" ");
				subjectdrc2.append(NumConverter.addComma(modeld.getLmtAmt()));
				subjectdrc2.append(" ");
				// other.money = 元
				subjectdrc2.append(pop2.get("other.money") + MARKCOMMA);
			}// close if
		}// close for

		for (L140M01E modele : modelE) {
			subjectdrc3.append(Util.trim(branchService.getBranchName(modele
					.getShareBrId())));
			subjectdrc3.append(MARKCOLON);
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(modele
					.getShareFlag())) {
				subjectdrc3.append(modele.getShareRate1() + MARKVIRGULE);
				subjectdrc3.append(modele.getShareRate2());
			} else {
				subjectdrc3.append(NumConverter.addComma(modele.getShareAmt()));
				// other.money = 元
				subjectdrc3.append(pop2.get("other.money"));
			}
			// 當額度序號與額度明細表不相同或者為國外分行時列印出額度序號
			if (!Util.trim(l140m01a.getCntrNo()).equals(
					Util.trim(modele.getShareNo()))
					|| UtilConstants.BrNoType.國外.equals(modele.getFlag())) {
				subjectdrc3.append("(");
				subjectdrc3.append(Util.trim(modele.getShareNo()));
				subjectdrc3.append(")");

			}
			subjectdrc3.append(MARKCOMMA);
		}

		if (Util.trim(subjectdrc1.toString()).length() > 0
				|| Util.trim(subjectdrc2.toString()).length() > 0) {
			drc.append(MARKSTAR).append(pop2.get("L140S02Tab.3_01"))
					.append(MARKCOLON);
		}
		if (!Util.isEmpty(Util.trim(subjectdrc1.toString()))) {
			drc.append(Util.trim(subjectdrc1.toString())).append(MARKPERIOD);
		}
		if (!Util.isEmpty(Util.trim(subjectdrc2.toString()))) {
			drc.append(Util.trim(subjectdrc2.toString())).append(MARKPERIOD);
		}
		if (Util.trim(subjectdrc1.toString()).length() > 0
				|| Util.trim(subjectdrc2.toString()).length() > 0) {
			drc.append(HTMLR);
		}

		if (!Util.isEmpty(Util.trim(subjectdrc3.toString())) && copyL140M01E) {
			drc.append(MARKSTAR + pop2.get("L140S02Tab.3_03") + MARKCOLON
					+ Util.trim(subjectdrc3.toString()) + MARKPERIOD + HTMLR);// 轉成全形回傳
		}
		L140M01B modelB = l140m01bDao.findByUniqueKey(mainId,
				UtilConstants.Cntrdoc.l140m01bItemType.限額條件);
		if (modelB == null) {
			modelB = new L140M01B();
			modelB.setMainId(mainId);
			modelB.setCreateTime(CapDate.getCurrentTimestamp());
			modelB.setCreator(user.getUserId());
			modelB.setItemType(UtilConstants.Cntrdoc.l140m01bItemType.限額條件);
		}// close if
		if (!Util.isEmpty(pageNum)) {
			modelB.setPageNum(pageNum);
		}

		modelB.setItemDscr(drc.toString().replace(MARKCOMMA + MARKPERIOD,
				MARKPERIOD));// 將描述存入DB
		this.save(modelB);
		if (drc.length() == 0) {

			drc.append(" ");
		}

		if (!Util.isEmpty(pageNum) && l140m01a != null) {
			// 當明細異動時要同時更改檢核
			l140m01a.setChkYN(null);
			l140m01aDao.save(l140m01a);
			modelB.setPageNum(pageNum);
		}
		return drc.toString().replace(MARKCOMMA + MARKPERIOD, MARKPERIOD);
	}

	@Override
	public String saveL140m01bDscr2(String mainId, String pageNum, boolean getDscr) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L140M01F> l140m01fs = l140m01fDao.findByMainId(mainId);
		List<L140M01C> l140m01cs = l140m01cDao.findByMainId(mainId);
		// 儲存授信科目到map為<科目代碼,A.B.C....>
		HashMap<String, String> itemMap = new HashMap<String, String>();
		int intValue = 65;
		for (L140M01C l140m01c : l140m01cs) {
			itemMap.put(l140m01c.getLoanTP(), String.valueOf((char) intValue));
			intValue++;
		}
		L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
		StringBuilder item = new StringBuilder("");
		StringBuilder drc = new StringBuilder("");

		for (L140M01F modelf : l140m01fs) {
			ArrayList<String> itemList = new ArrayList<String>();
			if (!CapString.isEmpty(modelf.getLoanTPList())) {

				// 取得目前有的項目數
				String[] strb = modelf.getLoanTPList().split(
						UtilConstants.Mark.SPILT_MARK);

				for (String s : strb) {
					if (itemMap.containsKey(s)) {
						itemList.add(itemMap.get(s));
					}
				}// close for

				// 先讓A B 等項目編號進行排序
				Collections.sort(itemList);
				for (String itemKey : itemList) {
					item.append(item.length() > 0 ? MARKDAN : "");
					item.append(itemKey);
				}
				drc.append(item.toString() + HTMLR + modelf.getRateDscr()
						+ HTMLR);
			}
			item.setLength(0);
		}// close for

		// 當無項目時不組成字串所以不需存入敘述檔中
		if (!getDscr) {
			L140M01B modelB = l140m01bDao.findByUniqueKey(mainId,
					UtilConstants.Cntrdoc.l140m01bItemType.利費率);
			if (modelB == null) {
				modelB = new L140M01B();
				modelB.setMainId(mainId);
				modelB.setCreateTime(CapDate.getCurrentTimestamp());
				modelB.setCreator(user.getUserId());
				modelB.setItemType(UtilConstants.Cntrdoc.l140m01bItemType.利費率);
			}// close if
			if (!Util.isEmpty(pageNum) && l140m01a != null) {
				// 當明細異動時要同時更改檢核
				l140m01a.setChkYN(null);
				l140m01aDao.save(l140m01a);
				modelB.setPageNum(pageNum);
			}

			modelB.setItemDscr(drc.toString());// 將描述存入DB
			this.save(modelB);
			if (drc.length() == 0) {
				drc.append(" ");
			}
		}
		return drc.toString();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.service.LMS1405Service#getRateDrc(java.lang.String,
	 * java.lang.Integer)
	 */
	@Override
	public String toRateDrc(String MainId, Integer RateSeq) {// 組成幣別和費率的說明
		StringBuilder drc = new StringBuilder("");
		List<L140M01G> moneyModel = l140m01gDao.findByMainIdAndRateSeq(MainId,
				RateSeq);

		String otherDesc = "";
		String FeeDesc = "";
		for (L140M01G modelg : moneyModel) {// 把幣別描述塞到對應的位置上
			// 因新增泰銖等幣別，要組在雜幣之前，故將雜幣、費率暫存後最後再加上
			if (Util.equals(modelg.getRateType(), "5")) {
				// 雜幣
				otherDesc = modelg.getRateDscr();
			} else if (Util.equals(modelg.getRateType(), "6")) {
				// 費率
				FeeDesc = modelg.getRateDscr();
			} else {
				drc.append(modelg.getRateDscr() + HTMLR);
			}
		}
		if (Util.notEquals(otherDesc, "")) {
			drc.append(otherDesc + HTMLR);
		}
		if (Util.notEquals(FeeDesc, "")) {
			drc.append(FeeDesc + HTMLR);
		}

		L140M01H moneyModelh = l140m01hDao.findByUniqueKey(MainId, RateSeq);
		if (moneyModelh != null) {
			drc.append(moneyModelh.getRateDscr());
		}
		L140M01F l140m01fmodel = l140m01fDao.findByUniqueKey(MainId, RateSeq);
		if (l140m01fmodel == null) {
			l140m01fmodel = new L140M01F();
			l140m01fmodel.setRateSeq(this.findL140m01fByMainIdMax(MainId));
		}
		l140m01fmodel.setRateDscr(drc.toString());
		return drc.toString();
	}

	@Override
	public L782M01A findL782m01aByUniqueKey(String mainId, String loanTp) {
		return l782m01aDao.findByUniqueKey(mainId, loanTp);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.service.LMS1405Service#deleteAll(java.lang.String)
	 */
	@Override
	public boolean deleteL140m01All(String mainId) {
		List<L120M01C> l120m01List = findL120m01cListByMainId(mainId);
		for (L120M01C l120m01c : l120m01List) {
			this.deleteL140m01(l120m01c.getRefMainId());
		}
		return false;
	}

	@Override
	public void copyL140m01All(String newMainId, String mainId,
			Boolean copyL140M01E, Boolean copyL140M01Q, Boolean copyL140M01T,
			Boolean copyL140S05A, Boolean copyL140S11A, Boolean copyL140S12A) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 來源端資料
		Map<String, Object> origin = new HashMap<String, Object>();
		// 目的端資料(需修改建立者、修改時間)
		Map<String, Object> target = new HashMap<String, Object>();

		origin.put(EloanConstants.MAIN_ID, mainId);
		target.put(EloanConstants.MAIN_ID, newMainId);

		target.put("CREATOR", user.getUserId());
		// target.put("CREATETIME", "current timestamp");
		target.put(DataParse.createTime, SQLParse.NOT_UPDATE);
		target.put("UPDATER", user.getUserId());
		target.put("UPDATETIME", "current timestamp");

		String modelB = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01B.class);
		String modelC = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01C.class);
		String modelD = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01D.class);
		String modelE = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01E.class);
		String modelF = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01F.class);
		String modelG = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01G.class);
		String modelH = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01H.class);
		String modelI = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01I.class);
		String modelK = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01K.class);
		String modelO = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01O.class);
		String modelQ = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01Q.class);
		String modelj = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01J.class);
		String modelt = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01T.class);

		// J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
		String model_l140s04a = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140S04A.class);
		// J-108-0283 變更條件Condition Change
		String model140S05A = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140S05A.class);

		// J-108-0293 Web e-Loan 未依銀行內部規定 internal regulations特別加註"特殊案件"
		String model140S06A = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140S06A.class);

		// J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
		String modelO_0307 = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M01O_0307.class);

		// #J-110-0211_11557_B1001
		// 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
		String model_l140m03a = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140M03A.class);
		String model_l120m01g = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L120M01G.class);

		// J-112-0357 新增敘做條件異動比較表
		String model140S11A = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140S11A.class);
		
		//J-113-0035  ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意承諾待追蹤ESG連結條款」的登錄機制
		String model140S12A = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L140S12A.class);

		// J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
		this.eloandbService.update(model_l140s04a);
		this.eloandbService.update(modelj);
		this.eloandbService.update(modelO);
		this.eloandbService.update(modelB);
		this.eloandbService.update(modelC);
		this.eloandbService.update(modelD);
		// 2012_05_28_建霖說 除了複製額度明細表 不複製聯行攤貸比率
		if (copyL140M01E) {
			this.eloandbService.update(modelE);
		} else {
			// 針對重組字串
			this.saveL140m01bDscr1(newMainId, null, copyL140M01E);
		}

		this.eloandbService.update(modelF);
		this.eloandbService.update(modelG);
		this.eloandbService.update(modelH);
		this.eloandbService.update(modelI);
		this.eloandbService.update(modelK);
		if (copyL140M01Q) {
			this.eloandbService.update(modelQ);
		}
		if (copyL140M01T) {
			this.eloandbService.update(modelt);
		}
		// J-108-0283 變更條件Condition Change
		if (copyL140S05A) {
			this.eloandbService.update(model140S05A);
		}

		// J-108-0293 Web e-Loan 未依銀行內部規定 internal regulations特別加註"特殊案件"
		this.eloandbService.update(model140S06A);

		// J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
		this.eloandbService.update(modelO_0307);

		// #J-110-0211_11557_B1001
		// 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
		this.eloandbService.update(model_l140m03a);
		this.eloandbService.update(model_l120m01g);

		// J-112-0357 新增敘做條件異動比較表
		if (copyL140S11A) {
			this.eloandbService.update(model140S11A);
		}

		//J-113-0035  ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意承諾待追蹤ESG連結條款」的登錄機制
		if (copyL140S12A) {
			this.eloandbService.update(model140S12A);
		}
		
		List<L140M01B> l140m01bs = l140m01bDao.findByMainId(mainId);
		for (L140M01B l140m01b : l140m01bs) {
			// 1限額條件<br/>
			// 2利(費)率<br/> 這兩個沒有CKEditor
			if ("1".equals(l140m01b.getItemType())
					|| "2".equals(l140m01b.getItemType())) {
				continue;
			}
			// 只有針對有CKEditor 的欄位複製
			try {
				l140m01b.setItemDscr(docFileService.copyCKEditorImageFile(
						Util.trim(l140m01b.getItemDscr()), newMainId));
			} catch (CapException e) {
				logger.info("copyCKEditorImageFile EXCEPTION!", e);
			}
		}

		// 處理附加檔案複製
		List<DocFile> listFile = docFileService.findByIDAndPid(mainId, null);
		if (!listFile.isEmpty()) {
			for (DocFile file : listFile) {
				DocFile newFile = new DocFile();
				try {
					DataParse.copy(file, newFile);
				} catch (CapException e) {
					logger.info("copy DocFile EXCEPTION!", e);
				}
				newFile.setMainId(newMainId);
				newFile.setCrYear(CapDate.getCurrentDate("yyyy"));
				newFile.setBranchId(user.getUnitNo());
				newFile.setOid(null);
				docFileService.copy(file.getOid(), newFile);
			}
		}

	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L140M01C.class) {
			return l140m01cDao.findByMainId(mainId);
		} else if (clazz == L140M01D.class) {
			return l140m01dDao.findByMainId(mainId);
		} else if (clazz == L140M01E.class) {
			return l140m01eDao.findByMainId(mainId);
		} else if (clazz == L140M01E_AF.class) {
			return l140m01e_afDao.findByMainId(mainId);
		} else if (clazz == L140M01F.class) {
			return l140m01fDao.findByMainId(mainId);
		} else if (clazz == L140M01G.class) {
			return l140m01gDao.findByMainId(mainId);
		} else if (clazz == L120M01C.class) {
			return l120m01cDao.findByMainId(mainId);
		} else if (clazz == L120S01A.class) {
			return l120s01aDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public List<Object[]> findL140m01aListByL120m01c(String caseMainId,
			String itemType) {

		return l140m01aDao.findCurrByMainId(caseMainId, itemType);
	}

	@Override
	public Map<String, Map<String, BigDecimal>> findL140m01Count(
			String caseMainId, String caseType) throws Exception {
		// 用來儲存借款人的各個合計
		Map<String, Map<String, BigDecimal>> custIdMap = new HashMap<String, Map<String, BigDecimal>>();

		// 取出案件簽報書底下所有額度明細表
		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId, caseType, null);
		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);
		// 依目前簽案行做計算幣別
		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());
		// 取得被共用的額度
		Map<String, Integer> allCommsno = queryMainCommSno(l140m01as);

		// 把已知的共用額度序號set到 這model List裡面
		l140m01as = this.putCommsnoSameInL140m01as(l140m01as, allCommsno);
		// 存放該共用額度所屬人
		Map<String, String> commonCust = new HashMap<String, String>();
		for (L140M01A l140m01a : l140m01as) {
			String commSno = Util.trim(l140m01a.getCommSno());
			String cntrNo = Util.trim(l140m01a.getCntrNo());
			if (Util.isEmpty(commSno) || Util.isEmpty(cntrNo)) {
				continue;
			}

			if (cntrNo.equals(commSno)) {
				commonCust.put(
						cntrNo,
						Util.trim(l140m01a.getCustId())
								+ Util.trim(l140m01a.getDupNo()));
			}
		}

		// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		boolean showLgdTotAmt = lmsService.showLgdTotAmt(l120m01a,
				l120m01a.getCaseBrId(), caseType);
		String lgdTotAmtVer = null;
		if (showLgdTotAmt) {
			lgdTotAmtVer = lmsService.getLgdTotAmtVerByCaseDate(l120m01a);
		}

		for (L140M01A l140m01a : l140m01as) {
			// 當經過重新計算要把調整的註記拿掉
			l140m01a.setValueTune(UtilConstants.DEFAULT.否);

			// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
			l140m01a.setValueTuneLgd(UtilConstants.DEFAULT.否);

			// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			l140m01a.setLgdTotAmt_Ver(lgdTotAmtVer);

			// 當不為空表示已做過儲存檢核
			if (UtilConstants.Cntrdoc.CHKYN.通過檢核.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.已計算);
			}
			l140m01a.setCntrChgOid(l120m01a.getOid());
			// 目前的借款人custId+dupNo
			String nowCustId = Util.trim(l140m01a.getCustId())
					+ Util.trim(l140m01a.getDupNo());
			String nowCntrNo = Util.trim(l140m01a.getCntrNo());

			// 目前借款人的共用額度序號
			String commSno = Util.trim(l140m01a.getCommSno());
			Boolean toDoCount = false;
			// 當共用額度序號不等於空
			if (!Util.isEmpty(commSno)) {
				// 若為主額度借款人則該共用額度只能加一次，若非主額度借款人則每筆都要加,(即若非額度借款人則共用額度序號忽略不需理會，每筆都要加)
				if (allCommsno.containsKey(nowCustId + commSno)) {
					allCommsno.put(nowCustId + commSno,
							allCommsno.get(nowCustId + commSno) + 1);
				}
				if (nowCntrNo.equals(commSno)) {
					toDoCount = true;
					// custIdMap = this.baseCount(custIdMap, l140m01a,
					// branchRate);
				} else {
					// 當有共用額度序號 且 這custId+共用額度序號 不包含在共用額度序號下 才加總
					if (!allCommsno.containsKey(nowCustId + commSno)) {
						toDoCount = true;
						// custIdMap = this.baseCount(custIdMap, l140m01a,
						// branchRate);
					}
					// 當這共用額度序號非該共用額度主借人則需要加總
					if (commonCust.containsKey(commSno)
							&& !commonCust.get(commSno).equals(nowCustId)) {
						toDoCount = true;
					}

				}
			} else {
				// 當共用額度序號等於空
				// 如果此額度序號尚未在共額度序號內 加過 或不是主要借款人
				toDoCount = true;
				// custIdMap = this.baseCount(custIdMap, l140m01a, branchRate);

			}
			custIdMap = this.baseCount(custIdMap, l140m01a, branchRate,
					toDoCount);
		}// close

		// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
		// 合併改成一筆所有借款人合計，所以改到baseCount後才合計**********************
		custIdMap = this.baseCountMerge(custIdMap, l140m01as, branchRate);

		this.saveCountResult(custIdMap, l140m01as, branchRate);
		return custIdMap;
	}

	public Map<String, CountTOT> testCount(String caseMainId, String caseType) {
		// 用來儲存借款人的各個合計
		Map<String, CountTOT> custIdMap = new HashMap<String, CountTOT>();
		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);
		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());
		// 取出案件簽報書底下所有額度明細表
		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId, caseType, null);
		// 依目前簽案行做計算幣別

		CountTOTAction CountTOTAction = new CountTOTAction(l140m01as,
				branchRate, "");
		CountTOTAction.count();
		CountTOTAction.showFinalCount();
		return custIdMap;
	}

	/**
	 * 判斷是否有當前id在map中 沒有的話就初始化一個
	 * 
	 * @param custIdMap
	 * @param l140m01a
	 * @return
	 */
	private Map<String, Map<String, BigDecimal>> ckeckCustIdInMap(
			Map<String, Map<String, BigDecimal>> custIdMap, L140M01A l140m01a) {
		// 目前的借款人custId+dupNo 當key
		String nowCustId = l140m01a.getCurrentApplyCurr()
				+ l140m01a.getCustId() + l140m01a.getDupNo();

		Map<String, BigDecimal> moneyMap = null;
		if (custIdMap.containsKey(nowCustId)) {
			moneyMap = custIdMap.get(nowCustId);
		} else {
			// 初始化
			BigDecimal totalZero = BigDecimal.ZERO;
			moneyMap = new HashMap<String, BigDecimal>();
			moneyMap.put(授信額度合計, totalZero);
			moneyMap.put(擔保授信合計, totalZero);
			moneyMap.put(衍生性商品原始合計, totalZero);
			moneyMap.put(衍生性商品相當合計, totalZero);
			moneyMap.put(前准額度批覆合計, totalZero);
			moneyMap.put(前准額度批覆擔保合計, totalZero);
			moneyMap.put(減額額度合計, totalZero);
			moneyMap.put(減額擔保額度合計, totalZero);
			// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
			moneyMap.put(瑕疵押匯額度合計, totalZero);
			moneyMap.put(總授信額度合計, totalZero);
			// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
			moneyMap.put(單獨另計授權額度合計, totalZero);
			// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
			moneyMap.put(瑕疵押匯額度合計_全案, totalZero);
			moneyMap.put(總授信額度合計_全案, totalZero);
			// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
			moneyMap.put(瑕疵押匯額度合計_合併關係, totalZero);
			moneyMap.put(總授信額度合計_合併關係, totalZero);

			// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
			Map<String, String> lgdMap = lmsService.getLgdTotAmtParam(l140m01a,
					null);

			int lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
					"lmsLgdCount", "0"));

			for (int i = 1; i <= lmsLgdCount; i++) {
				moneyMap.put(LGD合計 + "_" + i, totalZero);
				// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
				moneyMap.put(LGD合計_全案 + "_" + i, totalZero);
				// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
				moneyMap.put(LGD合計_合併關係 + "_" + i, totalZero);
			}
			// moneyMap.put(LGD其中無擔保合計, totalZero);
			// moneyMap.put(LGD其中擬制部分擔保合計, totalZero);
			// moneyMap.put(LGD其中擬制十足擔保合計, totalZero);
		}
		custIdMap.put(nowCustId, moneyMap);
		return custIdMap;
	}

	/**
	 * 把將額度序號與共用額度序號相同的model set共用額度序號進去
	 * 
	 * @param l140m01as
	 * @param commSnoMap
	 * @return
	 */
	private List<L140M01A> putCommsnoSameInL140m01as(List<L140M01A> l140m01as,
			Map<String, Integer> commSnoMap) {
		for (L140M01A l140m01a : l140m01as) {
			String cntrNo = Util.trim(l140m01a.getCntrNo());
			String custId = Util.trim(l140m01a.getCustId())
					+ Util.trim(l140m01a.getDupNo());
			String commSno = Util.trim(l140m01a.getCommSno());

			// 當共用額度序號為空並且共用額度序號清單內有這個額度序號 就把共用額度序號放入
			if (Util.isEmpty(commSno)
					&& commSnoMap.containsKey(custId + cntrNo)) {
				l140m01a.setCommSno(cntrNo);
			}
		}
		return l140m01as;
	}

	/**
	 * 計算加總的基本流程
	 * 
	 * @param custIdMap
	 *            所有借款人集合
	 * @param l140m01a
	 *            額度明細表
	 * @param branchRate
	 *            利率轉換檔
	 * @param toDocount
	 *            是否要加總 若此值為 false 只加總 衍生性金融商品
	 * @return
	 */
	private Map<String, Map<String, BigDecimal>> baseCount(
			Map<String, Map<String, BigDecimal>> custIdMap, L140M01A l140m01a,
			BranchRate branchRate, Boolean toDocount) {

		// 目前的借款人custId+dupNo 當key
		String nowCustId = StrUtils.concat(l140m01a.getCurrentApplyCurr(),
				l140m01a.getCustId(), l140m01a.getDupNo());
		// 確認是否有此id Map
		custIdMap = this.ckeckCustIdInMap(custIdMap, l140m01a);
		// 取出該Id的 moneyMap
		Map<String, BigDecimal> moneyMap = custIdMap.get(nowCustId);
		// 取得現請額度
		BigDecimal amtValue = l140m01a.getCurrentApplyAmt();
		// 擔保額度
		BigDecimal gamtValue = BigDecimal.ZERO;
		// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		// 瑕疵押匯額度合計
		HashMap<String, String> newList = LMSUtil.getItemList(l140m01a);
		String flaw_fg = ""; // 瑕疵額度控管方式
		BigDecimal flaw_amt = BigDecimal.ZERO; // 瑕疵額度限額
		String[] sysExperf = StringUtils.split(
				Util.trim(lmsService.getSysParamDataValue("EXPERF")), ",");
		boolean hasEXPERF = false;
		// J-108-0302 是否符合出口實績規範 942
		for (String key : newList.keySet()) {
			if (Arrays.asList(sysExperf).contains(key)) {
				flaw_fg = Util.trim(l140m01a.getFlaw_fg());
				flaw_amt = Util.parseBigDecimal(l140m01a.getFlaw_amt());
				hasEXPERF = true;
				break;
			}
		}

		// 1: 同出口押匯額度
		// 2: 另訂限額
		// 3: 不得瑕疵單據押匯
		if (Util.equals(flaw_fg, "1")) {
			flaw_amt = amtValue;
		} else if (Util.equals(flaw_fg, "3")) {
			flaw_amt = BigDecimal.ZERO;
		}

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		boolean showLgdTotAmt = false;
		L120M01C l120m01c = l140m01a.getL120m01c();
		L120M01A l120m01a = null;
		if (l120m01c != null) {
			l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());
			if (l120m01a != null) {
				showLgdTotAmt = lmsService.showLgdTotAmt(l120m01a,
						l120m01a.getCaseBrId(), l120m01c.getItemType());
			}
		}
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		boolean needShowGeneralLoanTotal = lmsService.needShowGeneralLoanTotal(
				l120m01a, l120m01a.getCaseBrId(), l120m01c.getItemType());

		// J-111-0343_05097_B1004 Web e-Loan修改企金額度明細表合計之功能
		l140m01a.setExpectLgd(null);
		l140m01aDao.save(l140m01a);

		// 當有衍生性金融商品 要做計算
		if ("Y".equals(l140m01a.getIsDerivatives())
				&& "Y".equals(l140m01a.getHasDerivatives())) {
			// 衍生性商品 目前原始合計 總和+現請額度
			// BigDecimal loanTotZAmtTotal = (BigDecimal)
			// custIdMap.get(nowCustId)
			// .get(衍生性商品原始合計).add(amtValue);

			BigDecimal loanTotZAmtTotal = BigDecimal.ZERO;

			List<L140M01C> l140m01cs = this.findL140m01cListByMainId(l140m01a
					.getMainId());
			ArrayList<String> itemsAll = new ArrayList<String>();
			if (!l140m01cs.isEmpty()) {
				// 用來存放衍生性金融商品授信科目代號
				for (L140M01C l140m01c : l140m01cs) {
					itemsAll.add(l140m01c.getLoanTP());

				}
			}
			Boolean hasDerivateSubject961Flag = false;
			hasDerivateSubject961Flag = lmsService
					.hasDerivateSubject961(itemsAll.toArray(new String[itemsAll
							.size()]));

			// 授管處蘇金柱3/27:如果是961 962 Z12科目時，衍生性合計要先將現請額度*風險係數後再相加
			if (hasDerivateSubject961Flag == true) {
				loanTotZAmtTotal = (BigDecimal) custIdMap
						.get(nowCustId)
						.get(衍生性商品原始合計)
						.add(amtValue.multiply(BigDecimal.ZERO
								.compareTo(LMSUtil
										.nullToZeroBigDecimal(l140m01a
												.getDerivativesNum())) == 0 ? BigDecimal.ONE
								: l140m01a.getDerivativesNum().divide(一百)));
			} else {
				loanTotZAmtTotal = (BigDecimal) custIdMap.get(nowCustId)
						.get(衍生性商品原始合計).add(amtValue);
			}

			moneyMap.put(衍生性商品原始合計,
					loanTotZAmtTotal.setScale(2, BigDecimal.ROUND_HALF_UP));
			// custIdMap.put(nowCustId, moneyMap);
			// 衍生性商品 相當授信目前合計
			BigDecimal loanTotLAmtTotal = (BigDecimal) custIdMap.get(nowCustId)
					.get(衍生性商品相當合計);
			// 總和+(現請額度*風險係數(%))
			BigDecimal nowloanTotLAmt = loanTotLAmtTotal.add((amtValue
					.multiply((BigDecimal.ZERO.compareTo(l140m01a
							.getDerivativesNum()) == 0 ? 一百 : l140m01a
							.getDerivativesNum()).divide(一百))));
			moneyMap.put(衍生性商品相當合計,
					nowloanTotLAmt.setScale(2, BigDecimal.ROUND_HALF_UP));
		}

		// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
		// LMS_LGD_EX_TOTAL_CREDIT_LIMIT 9,A 排除計算授信額度合計
		if (this.isExTotalCreditLimit(l140m01a)) {
			// 排除計算授信額度合計.....等等
			return custIdMap;
		}

		// 當toDocount =false 只加 衍生性金融商品
		if (toDocount) {

			// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
			if (hasEXPERF && needShowGeneralLoanTotal) {

				// 出口押匯--瑕疵押匯*********************************************************************************

				// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
				// [下午 02:57] 黃建霖(資訊處,高級專員)
				// 我出口押匯 的現請額度，就不加入原本的授信額度合計內喔，要不然變成
				// 假設:出口押匯 100萬 瑕疵押匯 90 萬
				// 1.授信額度合計100萬，瑕疵押匯額度90萬，總授信額度 190萬， 虛增了
				// 2.授信額度合計100萬，LGD1-5 因為不含出口押匯，導致檢核會出現錯誤訊息，授信額度合計 不等於 LGD1+++++
				// LGD5
				// [下午 02:58] 金至忠(授信審查處,襄理)
				// 出口押匯不用
				BigDecimal flawAmtTotal = moneyMap.get(瑕疵押匯額度合計).add(flaw_amt);
				moneyMap.put(瑕疵押匯額度合計, flawAmtTotal);
				// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
				// 合併改成一筆所有借款人合計，所以改到baseCount後才合計**********************
				// moneyMap.put(瑕疵押匯額度合計_全案, flawAmtTotal);

				BigDecimal generalLoanTotAmt = moneyMap.get(總授信額度合計).add(
						flaw_amt);
				moneyMap.put(總授信額度合計, generalLoanTotAmt);
				// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
				// 合併改成一筆所有借款人合計，所以改到baseCount後才合計**********************
				// moneyMap.put(總授信額度合計_全案, generalLoanTotAmt);
			} else {

				// 非出口押匯******************************************************************************

				// J-111-0461_05097_B1006
				// 授信額度合計新增單獨另計授權及各組LGD合計檢核***********************
				// J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
				// J-111-0343_05097_B1003 Web e-Loan修改消金額度明細表合計之功能
				boolean isNeed = lmsLgdService.isL140m01aNeedCountLgdTotal(
						l140m01a, "1");

				// J-112-0037_05097_B1005 Web
				// eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
				boolean isCustNeed = lmsLgdService
						.isCustNeedCountLgdTotal(l140m01a);

				// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
				// 金爺:授信額度合計=LGD合計
				// 是否啟用 授信額度合計=LGD合計
				String LMS_LGD_LGDTOTAMT_E_LOANTOTAMT = Util
						.trim(lmsService
								.getSysParamDataValue("LMS_LGD_LGDTOTAMT_E_LOANTOTAMT"));

				boolean needCount_LgdTotAmt = false; // 是否要計算LGD合計
				if (isNeed && isCustNeed) {
					needCount_LgdTotAmt = true;
				}

				boolean isEuroyenTiborExitCase = lmsService.isEuroyenTiborExitCase(l120m01a);

				boolean needCount_LoanTotAmt = true; // 是否要計算授信額度合計
				if (Util.equals(LMS_LGD_LGDTOTAMT_E_LOANTOTAMT, "Y")) {
					// 啟用 授信額度合計=LGD合計，則改用LGD是否計算判斷
					needCount_LoanTotAmt = needCount_LgdTotAmt;
				}

				boolean needCount_StandAloneAuth = false; // 是否要計算單獨另計授權額度
				if (Util.equals(LMS_LGD_LGDTOTAMT_E_LOANTOTAMT, "Y")) {
					// 啟用 授信額度合計=LGD合計，則改用LGD是否計算判斷
					// 判斷---納入授權額度限額計算註記=2-8 + LGD業務別
					needCount_StandAloneAuth = lmsLgdService
							.isL140m01aNeedCountLgdTotal(l140m01a, "3");
				} else {
					if (Util.isNotEmpty(Util.trim(l140m01a
							.getIsStandAloneAuth()))
							&& Util.notEquals(
									Util.trim(l140m01a.getIsStandAloneAuth()),
									"1")) {
						// 單純判斷-+--納入授權額度限額計算註記=2-8
						needCount_StandAloneAuth = true;
					}
				}

				// 啟用LGD才要判斷=>LGD合計=授信額度合計，授信額度合計 與 LGD合計 計算範圍要一樣
				// 但是所排除的單獨另計授權額度還是要另外合計於畫面顯示

				// 不要算LGD的，也不要算授信額度合計，但是要顯示單獨另計授權額度合計
				if (needCount_StandAloneAuth) {
					if (Util.isNotEmpty(Util.trim(l140m01a
							.getIsStandAloneAuth()))
							&& Util.notEquals(
									Util.trim(l140m01a.getIsStandAloneAuth()),
									"1")) {

						BigDecimal standAloneAuthTotal = moneyMap.get(
								單獨另計授權額度合計).add(amtValue);
						moneyMap.put(單獨另計授權額度合計, standAloneAuthTotal);
					}
				}

				if (needCount_LoanTotAmt || isEuroyenTiborExitCase) {
					// 該id現請額度合計
					BigDecimal total = moneyMap.get(授信額度合計).add(amtValue);
					moneyMap.put(授信額度合計, total);

					BigDecimal gtAmtTotal = BigDecimal.ZERO;
					// 檢查有無送保 並且 保證成數不等於空值
					if (("Y".equals(l140m01a.getHeadItem1()) && (!Util
							.isEmpty(l140m01a.getGutPercent())))) {
						// 目前這份額度明細表 擔保合計等於現請額度 * 保證成數 - 擔保授信額度調整
						gamtValue = (amtValue.multiply(l140m01a.getGutPercent()
								.divide(一百)).subtract(!Util.isEmpty(l140m01a
								.getAssureTotEAmt()) ? l140m01a
								.getAssureTotEAmt() : BigDecimal.ZERO));
						gtAmtTotal = custIdMap.get(nowCustId).get(擔保授信合計)
								.add(gamtValue);
						moneyMap.put(擔保授信合計, gtAmtTotal);
					} else {
						// 非信保案件計算其中擔保 判斷授信性質為擔保或無擔保　
						// 額度性質為擔保(S)時 才去做加總
						if (UtilConstants.Cntrdoc.sbjProperty.擔保
								.equals(l140m01a.getSbjProperty())) {

							// 現請額度 - 擔保授信額度調整
							gamtValue = (amtValue
									.subtract(!Util.isEmpty(l140m01a
											.getAssureTotEAmt()) ? l140m01a
											.getAssureTotEAmt()
											: BigDecimal.ZERO));
							// 該id擔保合計總和
							gtAmtTotal = (BigDecimal) custIdMap.get(nowCustId)
									.get(擔保授信合計).add(gamtValue);
							moneyMap.put(擔保授信合計, gtAmtTotal);
						}
					}

					// 前准批覆授信額度 當有幣別才做計算
					if (Util.isNotEmpty(Util.trim(l140m01a.getLV2Curr()))) {
						BigDecimal nowLv = l140m01a.getLV2Amt();
						if (nowLv == null) {
							nowLv = BigDecimal.ZERO;
						}
						// 將幣別轉為現請額度幣別做加總
						nowLv = branchRate.toOtherAmt(l140m01a.getLV2Curr(),
								l140m01a.getCurrentApplyCurr(), nowLv);
						branchRate.toLocalAmt(l140m01a.getLV2Curr(), nowLv);
						// 將幣別轉為現請額度幣別做加總
						BigDecimal lvtot = moneyMap.get(前准額度批覆合計).add(nowLv);
						logger.info("lvtot===>" + lvtot);
						moneyMap.put(前准額度批覆合計, moneyMap.get(前准額度批覆合計)
								.add(nowLv));
						// 減額計算 前准額度批覆 > 現請額度 減額計算
						if (nowLv.compareTo(amtValue) == 1) {
							// (前准額度批覆 - 現請額度)
							moneyMap.put(
									減額額度合計,
									moneyMap.get(減額額度合計).add(
											nowLv.subtract(amtValue)));
						}

					}
					// 前准額度批覆擔保合計 當有幣別才做計算
					if (!Util.isEmpty(Util.trim(l140m01a.getLVAssureCurr()))) {
						BigDecimal nowLvass = l140m01a.getLVAssureAmt();
						if (nowLvass == null) {
							nowLvass = BigDecimal.ZERO;
						}
						// 將幣別轉為現請額度幣別做加總
						nowLvass = branchRate.toOtherAmt(
								l140m01a.getLVAssureCurr(),
								l140m01a.getCurrentApplyCurr(), nowLvass);
						BigDecimal lvasstot = moneyMap.get(前准額度批覆擔保合計).add(
								nowLvass);
						logger.info("lvasstot===>" + lvasstot);
						moneyMap.put(前准額度批覆擔保合計,
								moneyMap.get(前准額度批覆擔保合計).add(nowLvass));

						// 減額擔保計算
						if (nowLvass.compareTo(gamtValue) == 1) {
							moneyMap.put(
									減額擔保額度合計,
									moneyMap.get(減額擔保額度合計).add(
											nowLvass.subtract(gamtValue)));
						}
					}
				}

				if (showLgdTotAmt && needCount_LgdTotAmt) {
					// 1.納入授權額度計算註記「外匯業務、一般應收帳款承購業務、EDI預約付款應收帳款承購業務、衍生性金融商品交易業務等，請一律勾選本項」

					BigDecimal expectLgd = this.setExpectLgd(l140m01a);

					if (expectLgd != null) {

						Map<String, String> lgdMap = lmsService
								.getLgdTotAmtParam(l140m01a, expectLgd);

						// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
						int lmsLgdCount = Util.parseInt(MapUtils.getString(
								lgdMap, "lmsLgdCount", "0"));
						int lgdAuthGroup = Util.parseInt(MapUtils.getString(
								lgdMap, "lgdAuthGroup", "0"));
						BigDecimal lgdTotal = moneyMap.get(
								LGD合計 + "_" + lgdAuthGroup).add(amtValue);
						moneyMap.put(LGD合計 + "_" + lgdAuthGroup, lgdTotal);
						// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
						// 合併改成一筆所有借款人合計，所以改到baseCount後才合計**********************
						// BigDecimal lgdTotal_mg = moneyMap.get(
						// LGD合計_全案 + "_" + lgdAuthGroup).add(amtValue);
						// moneyMap.put(LGD合計_全案 + "_" + lgdAuthGroup,
						// lgdTotal_mg);

						// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
						// J-111-0461_05097_B1004
						// 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
						if (needShowGeneralLoanTotal) {
							// J-111-0461_05097_B1002
							// 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
							// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
							BigDecimal generalLoanTotAmt = moneyMap
									.get(總授信額度合計).add(amtValue);
							moneyMap.put(總授信額度合計, generalLoanTotAmt);

							// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
							// 合併改成一筆所有借款人合計，所以改到baseCount後才合計**********************
							// BigDecimal generalLoanTotAmt_mg = moneyMap.get(
							// 總授信額度合計_全案).add(amtValue);
							// moneyMap.put(總授信額度合計_全案, generalLoanTotAmt_mg);

						}
					}
				}
			}
		}

		custIdMap.put(nowCustId, moneyMap);
		return custIdMap;
	}

	/**
	 * 計算加總的基本流程(全案所有借款人授權)
	 * 
	 * @param custIdMap
	 *            所有借款人集合
	 * @param l140m01a
	 *            額度明細表
	 * @param branchRate
	 *            利率轉換檔
	 * @param toDocount
	 *            是否要加總 若此值為 false 只加總 衍生性金融商品
	 * @return
	 */
	private Map<String, Map<String, BigDecimal>> baseCountMerge(
			Map<String, Map<String, BigDecimal>> custIdMap,
			List<L140M01A> l140m01as, BranchRate branchRate) {

		L140M01A l140m01aLgd = null;
		if (l140m01as != null && !l140m01as.isEmpty()) {
			l140m01aLgd = l140m01as.get(0);
		}

		L120M01C l120m01c = l140m01aLgd.getL120m01c();
		String itemType = l120m01c.getItemType();
		String caseBrid = null;
		String l120m01aMainId = "";

		// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
		L120M01A l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());
		l120m01aMainId = l120m01a.getMainId();
		caseBrid = l120m01a.getCaseBrId();
		Map<String, String> lgdMap = lmsService.getLgdTotAmtParam(l140m01aLgd,
				null);
		int lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
				"lmsLgdCount", "0"));

		// 所有借款人合計
		BigDecimal allCase_flawAmtMgTotal = BigDecimal.ZERO;
		BigDecimal allCase_generalLoanMgTotAmt = BigDecimal.ZERO;
		JSONObject allCase_count = new JSONObject();
		String allCase_lgdTotMgCurr = "";
		for (int i = 1; i <= lmsLgdCount; i++) {
			allCase_count.put(LGD合計_全案 + "_" + i, 0);
		}

		String mainCustId = Util.trim(l120m01a.getCustId())
				+ Util.trim(l120m01a.getDupNo());
		// 取得合併的主幣別
		String firstCurr = "";
		for (String name : custIdMap.keySet()) {
			String custCurr = name.substring(0, 3);
			String custId = Util.trim(name.substring(3, name.length()));
			if (Util.equals(custId, mainCustId)) {
				allCase_lgdTotMgCurr = custCurr;
				break;
			}
			if (Util.equals(firstCurr, "")) {
				firstCurr = custCurr;
			}
		}

		// 找不到主幣別，則以第一個抓到的借款人幣別為主
		if (Util.equals(allCase_lgdTotMgCurr, "")) {
			allCase_lgdTotMgCurr = firstCurr;
		}

		// 清除MAP裡面的合併欄位，因為如果有轉過多次幣別會有lgdTotMgCurr_XXX
		// 會有多筆，例如lgdTotMgCurr_TWD、lgdTotMgCurr_USD
		for (String name : custIdMap.keySet()) {
			Map<String, BigDecimal> moneyMap = null;
			moneyMap = custIdMap.get(name);

			moneyMap.put(瑕疵押匯額度合計_全案, BigDecimal.ZERO);
			moneyMap.put(總授信額度合計_全案, BigDecimal.ZERO);

			for (int i = 1; i <= lmsLgdCount; i++) {
				moneyMap.put(LGD合計_全案 + "_" + i, BigDecimal.ZERO);
			}

			Map<String, BigDecimal> copyMap = new HashMap<String, BigDecimal>();
			copyMap.putAll(moneyMap);
			for (String n : copyMap.keySet()) {
				if (StringUtils.contains(n, LGD合計_全案_幣別)) {
					moneyMap.remove(n);
				}
			}

			// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
			moneyMap.put(瑕疵押匯額度合計_合併關係, BigDecimal.ZERO);
			moneyMap.put(總授信額度合計_合併關係, BigDecimal.ZERO);

			for (int i = 1; i <= lmsLgdCount; i++) {
				moneyMap.put(LGD合計_合併關係 + "_" + i, BigDecimal.ZERO);
			}

		}

		// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
		// **************************************************************************************************************
		// 合併關係企業(去除有本次簽案借款人的額度)*******************************************************
		// **************************************************************************************************************
		// 有簽案的借款人
		boolean needShowLoanCountRcTotal = lmsService.needShowLoanCountRcTotal(
				l120m01a, l120m01a.getCaseBrId(), itemType);

		if (needShowLoanCountRcTotal) {

			Map<String, String> inCaseCustMap = new HashMap<String, String>();
			for (String name : custIdMap.keySet()) {
				String custId = Util.trim(name.substring(3, name.length()));
				String gCustId = Util.trim(Util.getLeftStr(custId,
						custId.length() - 1));
				String gDupNo = Util.trim(Util.getRightStr(custId, 1));
				String gFullCustId = gCustId + "-" + gDupNo;
				inCaseCustMap.put(gFullCustId, gFullCustId);
			}

			for (String name : custIdMap.keySet()) {

				Map<String, BigDecimal> moneyMap = null;
				moneyMap = custIdMap.get(name);

				Map<String, BigDecimal> rcMap = this.baseCountMergeRcMap(
						custIdMap, name, l140m01as, branchRate, l120m01a,
						lmsLgdCount, inCaseCustMap);

				BigDecimal flawAmtRcTotal = BigDecimal.ZERO;
				flawAmtRcTotal = Util.parseBigDecimal(
						MapUtils.getString(rcMap, "flawRc", "0")).setScale(2,
						BigDecimal.ROUND_HALF_UP);
				moneyMap.put(瑕疵押匯額度合計_合併關係, flawAmtRcTotal);

				BigDecimal lgdTotRcAmt = BigDecimal.ZERO;
				for (int i = 1; i <= lmsLgdCount; i++) {
					String lgdItem = LGD合計 + "_" + Util.trim(i);

					BigDecimal tmpLgd = Util.parseBigDecimal(
							MapUtils.getString(rcMap, lgdItem, "0")).setScale(
							2, BigDecimal.ROUND_HALF_UP);

					moneyMap.put(LGD合計_合併關係 + "_" + i, tmpLgd);

					lgdTotRcAmt = lgdTotRcAmt.add(tmpLgd);
				}

				moneyMap.put(總授信額度合計_合併關係, (lgdTotRcAmt.add(flawAmtRcTotal))
						.setScale(2, BigDecimal.ROUND_HALF_UP));

			}
		}
		// **************************************************************************************************************
		// 計算全案**************************************************************
		// **************************************************************************************************************
		// 轉換幣別再合計
		for (String name : custIdMap.keySet()) {
			Map<String, BigDecimal> moneyMap = null;
			String custCurr = name.substring(0, 3);
			String custId = name.substring(3, name.length());
			moneyMap = custIdMap.get(name);

			for (int i = 1; i <= lmsLgdCount; i++) {
				BigDecimal tmpLgdCount = Util.parseBigDecimal(allCase_count
						.optString(LGD合計_全案 + "_" + i, "0"));

				BigDecimal tmpLgd = moneyMap.get(LGD合計 + "_" + i);
				if (Util.notEquals(allCase_lgdTotMgCurr, custCurr)) {
					tmpLgd = branchRate.toOtherAmt(custCurr,
							allCase_lgdTotMgCurr, tmpLgd);
				}

				tmpLgdCount = tmpLgdCount.add(tmpLgd);

				allCase_count.put(LGD合計_全案 + "_" + i, tmpLgdCount);

			}

			BigDecimal flawAmtTotal = moneyMap.get(瑕疵押匯額度合計);
			if (Util.notEquals(allCase_lgdTotMgCurr, custCurr)) {
				flawAmtTotal = branchRate.toOtherAmt(custCurr,
						allCase_lgdTotMgCurr, flawAmtTotal);
			}

			BigDecimal generalLoanTotAmt = moneyMap.get(總授信額度合計);
			if (Util.notEquals(allCase_lgdTotMgCurr, custCurr)) {
				generalLoanTotAmt = branchRate.toOtherAmt(custCurr,
						allCase_lgdTotMgCurr, generalLoanTotAmt);
			}

			// 合併
			allCase_flawAmtMgTotal = allCase_flawAmtMgTotal.add(flawAmtTotal);
			allCase_generalLoanMgTotAmt = allCase_generalLoanMgTotAmt
					.add(generalLoanTotAmt);

		}

		for (String name : custIdMap.keySet()) {
			Map<String, BigDecimal> moneyMap = null;
			moneyMap = custIdMap.get(name);

			moneyMap.put(瑕疵押匯額度合計_全案, allCase_flawAmtMgTotal.setScale(2,
					BigDecimal.ROUND_HALF_UP));
			moneyMap.put(總授信額度合計_全案, allCase_generalLoanMgTotAmt.setScale(2,
					BigDecimal.ROUND_HALF_UP));
			moneyMap.put(LGD合計_全案_幣別 + "_" + allCase_lgdTotMgCurr,
					BigDecimal.ZERO);
			for (int i = 1; i <= lmsLgdCount; i++) {
				moneyMap.put(
						LGD合計_全案 + "_" + i,
						Util.parseBigDecimal(
								allCase_count
										.optString(LGD合計_全案 + "_" + i, "0"))
								.setScale(2, BigDecimal.ROUND_HALF_UP));
			}

		}

		return custIdMap;
	}

	/**
	 * 新增合併關係企業合計
	 * 
	 * J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 * 
	 * @param custIdMap
	 * @param name
	 * @param l140m01as
	 * @param branchRate
	 * @param l120m01a
	 * @param lmsLgdCount
	 * @param inCaseCustMap
	 * @return
	 */
	public Map<String, BigDecimal> baseCountMergeRcMap(
			Map<String, Map<String, BigDecimal>> custIdMap, String name,
			List<L140M01A> l140m01as, BranchRate branchRate, L120M01A l120m01a,
			int lmsLgdCount, Map<String, String> inCaseCustMap) {

		Map<String, BigDecimal> moneyMap = null;
		String custCurr = name.substring(0, 3);
		String custId = name.substring(3, name.length());

		String gCustId = Util
				.trim(Util.getLeftStr(custId, custId.length() - 1));
		String gDupNo = Util.trim(Util.getRightStr(custId, 1));
		moneyMap = custIdMap.get(name);

		Map<String, BigDecimal> mergeAllMap = new HashMap<String, BigDecimal>();

		boolean hasSelf = false;

		if (Util.equals(l120m01a.getTypCd(), TypCdEnum.海外.getCode())) {
			// 海外!!!!
			L120S11A_LOC totL120s11a = lmsService
					.findL120s11a_locByMainIdCsutIdItemSeq(
							l120m01a.getMainId(), gCustId, gDupNo, 99999);// 合計的那一筆關係企業

			// 一般狀況，要處理合併關係企業資料
			// 先把合計放進去
			if (totL120s11a.getFactAmt() != null) {
				mergeAllMap.put("total", totL120s11a.getFactAmt());// 總額度
			}

			if (totL120s11a.getExFlawAmt() != null) {
				mergeAllMap.put("flawRc", totL120s11a.getExFlawAmt());// 瑕疵押匯額度
			}

			for (int i = 1; i <= lmsLgdCount; i++) {
				// LGD_X
				String lgdItem = LGD合計 + "_" + Util.trim(i);

				try {
					if (totL120s11a.get(lgdItem) != null) {
						mergeAllMap.put(lgdItem,
								(BigDecimal) totL120s11a.get(lgdItem));
					}
				} catch (CapException e) {
					// L120S11A.LGD找不到欄位的狀況，有錯就只是檢查不到而已
					e.printStackTrace();
					logger.error("取得L120S11A.LGD欄位有誤", e);
				}
			}

			// 替換合併關係企業成員有本次簽案時的額度**********************************
			List<L120S11A_LOC> l120s11aListBythisCustId = lmsService
					.findL120s11a_locByMainIdCustId(l120m01a.getMainId(),
							gCustId, gDupNo);

			// 減掉本次有簽案的關係戶
			for (L120S11A_LOC l110s11a : l120s11aListBythisCustId) {
				String s11aCustId = Util.trim(l110s11a.getCustId2());
				String s11aDupNo = Util.trim(l110s11a.getDupNo2());

				if (Util.equals(s11aCustId, "9999999999")
						&& Util.equals(s11aDupNo, "9")) {
					// 合計
					continue;
				} else if (Util.equals(s11aCustId, "")
						&& Util.equals(s11aDupNo, "")) {
					// 無資料
					continue;
				}

				// 本人有可能沒有在 l110s11a list裡面，所以要補回來
				if (Util.equals(s11aCustId, gCustId)
						&& Util.equals(s11aDupNo, gDupNo)) {
					// 本人有在合併關係企業名單內
					hasSelf = true;
				}

				if (inCaseCustMap.containsKey(s11aCustId + "-" + s11aDupNo)) {
					// 如果命中的話，就是合計那筆要減掉L120S11A
					if (l110s11a.getFactAmt() != null) {
						// 總額度
						BigDecimal temp = mergeAllMap.get("total").subtract(
								l110s11a.getFactAmt());
						mergeAllMap.put("total", temp);// 總額度
					}

					if (l110s11a.getExFlawAmt() != null) {
						// 瑕疵押匯額度
						BigDecimal temp = mergeAllMap.get("flawRc").subtract(
								l110s11a.getExFlawAmt());
						mergeAllMap.put("flawRc", temp);// 瑕疵押匯額度
					}

					for (int i = 1; i <= lmsLgdCount; i++) {
						// LGD_X
						String lgdItem = LGD合計 + "_" + Util.trim(i);

						try {
							if (l110s11a.get(lgdItem) != null) {
								BigDecimal temp = mergeAllMap.get(lgdItem)
										.subtract(
												(BigDecimal) l110s11a
														.get(lgdItem));
								mergeAllMap.put(lgdItem, temp);
							}
						} catch (CapException e) {
							// L120S11A找不到欄位的狀況，有錯就只是檢查不到而已
							e.printStackTrace();
							logger.error("取得L120S11A.LGD欄位有誤", e);
						}
					}
				}
			}

			// TWD 轉合計幣別******************************************************
			BigDecimal tmpTot = mergeAllMap.get("total");
			if (Util.notEquals("TWD", custCurr)) {
				tmpTot = branchRate.toOtherAmt("TWD", custCurr,
						mergeAllMap.get("total"));
			}
			mergeAllMap.put("total", tmpTot);// 轉換幣別後的總額度

			// 合併關係企業額度為USD
			// MIS.LNF022.exportLoan 的 FLAW_AMT_USD
			BigDecimal tmpFlaw = mergeAllMap.get("flawRc");
			if (Util.notEquals("USD", custCurr)) {
				tmpTot = branchRate.toOtherAmt("USD", custCurr,
						mergeAllMap.get("flawRc"));
			}
			mergeAllMap.put("flawRc", tmpFlaw);// 轉換幣別後的瑕疵押匯額度

			for (int i = 1; i <= lmsLgdCount; i++) {
				// LGD_X
				String lgdItem = LGD合計 + "_" + Util.trim(i);

				if (mergeAllMap.get(lgdItem) != null) {
					BigDecimal tmpLgd = mergeAllMap.get(lgdItem);
					if (Util.notEquals("TWD", custCurr)) {
						tmpLgd = branchRate.toOtherAmt("TWD", custCurr,
								mergeAllMap.get(lgdItem));
					}

					mergeAllMap.put(lgdItem, tmpLgd);
				}
			}

			// 加回來本次有簽案的關係戶
			for (L120S11A_LOC l110s11a : l120s11aListBythisCustId) {
				String s11aCustId = Util.trim(l110s11a.getCustId2());
				String s11aDupNo = Util.trim(l110s11a.getDupNo2());
				if (inCaseCustMap.containsKey(s11aCustId + "-" + s11aDupNo)) {
					// 加回來
					for (String nameR : custIdMap.keySet()) {
						Map<String, BigDecimal> moneyMapR = null;
						String custCurrR = nameR.substring(0, 3);
						String custIdR = nameR.substring(3, nameR.length());

						String gCustIdR = Util.trim(Util.getLeftStr(custIdR,
								custIdR.length() - 1));
						String gDupNoR = Util
								.trim(Util.getRightStr(custIdR, 1));
						moneyMapR = custIdMap.get(nameR);

						if (Util.equals(s11aCustId, gCustIdR)
								&& Util.equals(s11aDupNo, gDupNoR)) {

							// 授信額度合計**********************************************
							BigDecimal loanTotAmt = moneyMapR.get(授信額度合計);
							if (Util.notEquals(custCurr, custCurrR)) {
								loanTotAmt = branchRate.toOtherAmt(custCurrR,
										custCurr, loanTotAmt);
							}

							BigDecimal temp = mergeAllMap.get("total").add(
									loanTotAmt);
							mergeAllMap.put("total", temp);// 總額度

							// 瑕疵押匯額度合計**********************************************
							BigDecimal flawAmtTotal = moneyMapR.get(瑕疵押匯額度合計);
							if (Util.notEquals(custCurr, custCurrR)) {
								flawAmtTotal = branchRate.toOtherAmt(custCurrR,
										custCurr, flawAmtTotal);
							}

							BigDecimal tempFlawRc = mergeAllMap.get("flawRc")
									.add(flawAmtTotal);
							mergeAllMap.put("flawRc", tempFlawRc);// 瑕疵押匯額度合計

							// LGD合計**********************************************
							for (int i = 1; i <= lmsLgdCount; i++) {
								// LGD_X
								String lgdItem = LGD合計 + "_" + Util.trim(i);

								BigDecimal lgdTotAmt = moneyMapR.get(lgdItem);
								if (Util.notEquals(custCurr, custCurrR)) {
									lgdTotAmt = branchRate.toOtherAmt(
											custCurrR, custCurr, lgdTotAmt);
								}
								BigDecimal tempLgd = mergeAllMap.get(lgdItem)
										.add(lgdTotAmt);
								mergeAllMap.put(lgdItem, tempLgd);// LGD額度

							}

						}

					}

				}
			}

		} else {
			// 國內!!!!
			L120S11A totL120s11a = lmsService
					.findL120s11aByMainIdCsutIdItemSeq(l120m01a.getMainId(),
							gCustId, gDupNo, 99999);// 合計的那一筆關係企業

			// 一般狀況，要處理合併關係企業資料
			// 先把合計放進去
			if (totL120s11a.getFactAmt() != null) {
				mergeAllMap.put("total", totL120s11a.getFactAmt());// 總額度
			}
			// 瑕疵押匯額度
			if (totL120s11a.getExFlawAmt() != null) {
				mergeAllMap.put("flawRc", totL120s11a.getExFlawAmt());// 瑕疵押匯額度
			}

			for (int i = 1; i <= lmsLgdCount; i++) {
				// LGD_X
				String lgdItem = LGD合計 + "_" + Util.trim(i);

				try {
					if (totL120s11a.get(lgdItem) != null) {
						mergeAllMap.put(lgdItem,
								(BigDecimal) totL120s11a.get(lgdItem));
					}
				} catch (CapException e) {
					// L120S11A.LGD找不到欄位的狀況，有錯就只是檢查不到而已
					e.printStackTrace();
					logger.error("取得L120S11A.LGD欄位有誤", e);
				}
			}

			List<L120S11A> l120s11aListBythisCustId = lmsService
					.findL120s11aByMainIdCustId(l120m01a.getMainId(), gCustId,
							gDupNo);
			for (L120S11A l110s11a : l120s11aListBythisCustId) {
				String s11aCustId = Util.trim(l110s11a.getCustId2());
				String s11aDupNo = Util.trim(l110s11a.getDupNo2());

				if (Util.equals(s11aCustId, "9999999999")
						&& Util.equals(s11aDupNo, "9")) {
					// 合計
					continue;
				} else if (Util.equals(s11aCustId, "")
						&& Util.equals(s11aDupNo, "")) {
					// 無資料
					continue;
				}

				// 本人有可能沒有在 l110s11a list裡面，所以要補回來
				if (Util.equals(s11aCustId, gCustId)
						&& Util.equals(s11aDupNo, gDupNo)) {
					// 本人有在合併關係企業名單內
					hasSelf = true;
				}

				if (inCaseCustMap.containsKey(s11aCustId + "-" + s11aDupNo)) {
					// 如果命中的話，就是合計那筆要減掉L120S11A
					if (l110s11a.getFactAmt() != null) {
						// 總額度
						BigDecimal temp = mergeAllMap.get("total").subtract(
								l110s11a.getFactAmt());
						mergeAllMap.put("total", temp);// 總額度
					}

					if (l110s11a.getExFlawAmt() != null) {
						// 瑕疵押匯額度
						BigDecimal temp = mergeAllMap.get("flawRc").subtract(
								l110s11a.getExFlawAmt());
						mergeAllMap.put("flawRc", temp);// 瑕疵押匯額度
					}

					for (int i = 1; i <= lmsLgdCount; i++) {
						// LGD_X
						String lgdItem = LGD合計 + "_" + Util.trim(i);

						try {
							if (l110s11a.get(lgdItem) != null) {
								BigDecimal temp = mergeAllMap.get(lgdItem)
										.subtract(
												(BigDecimal) l110s11a
														.get(lgdItem));
								mergeAllMap.put(lgdItem, temp);
							}
						} catch (CapException e) {
							// L120S11A找不到欄位的狀況，有錯就只是檢查不到而已
							e.printStackTrace();
							logger.error("取得L120S11A.LGD欄位有誤", e);
						}
					}
				}
			}

			// TWD 轉合計幣別******************************************************
			BigDecimal tmpTot = mergeAllMap.get("total");
			if (Util.notEquals("TWD", custCurr)) {
				tmpTot = branchRate.toOtherAmt("TWD", custCurr,
						mergeAllMap.get("total"));
			}
			mergeAllMap.put("total", tmpTot);// 轉換幣別後的總額度

			// 合併關係企業額度為USD
			// MIS.LNF022.exportLoan 的 FLAW_AMT_USD
			BigDecimal tmpFlaw = mergeAllMap.get("flawRc");
			if (Util.notEquals("USD", custCurr)) {
				tmpTot = branchRate.toOtherAmt("USD", custCurr,
						mergeAllMap.get("flawRc"));
			}
			mergeAllMap.put("flawRc", tmpFlaw);// 轉換幣別後的瑕疵押匯額度

			for (int i = 1; i <= lmsLgdCount; i++) {
				// LGD_X
				String lgdItem = LGD合計 + "_" + Util.trim(i);

				if (mergeAllMap.get(lgdItem) != null) {
					BigDecimal tmpLgd = mergeAllMap.get(lgdItem);
					if (Util.notEquals("TWD", custCurr)) {
						tmpLgd = branchRate.toOtherAmt("TWD", custCurr,
								mergeAllMap.get(lgdItem));
					}

					mergeAllMap.put(lgdItem, tmpLgd);
				}
			}

			// 加回來本次有簽案的關係戶
			for (L120S11A l110s11a : l120s11aListBythisCustId) {
				String s11aCustId = Util.trim(l110s11a.getCustId2());
				String s11aDupNo = Util.trim(l110s11a.getDupNo2());
				if (inCaseCustMap.containsKey(s11aCustId + "-" + s11aDupNo)) {
					// 加回來
					for (String nameR : custIdMap.keySet()) {
						Map<String, BigDecimal> moneyMapR = null;
						String custCurrR = nameR.substring(0, 3);
						String custIdR = nameR.substring(3, nameR.length());

						String gCustIdR = Util.trim(Util.getLeftStr(custIdR,
								custIdR.length() - 1));
						String gDupNoR = Util
								.trim(Util.getRightStr(custIdR, 1));
						moneyMapR = custIdMap.get(nameR);

						if (Util.equals(s11aCustId, gCustIdR)
								&& Util.equals(s11aDupNo, gDupNoR)) {

							// 授信額度合計**********************************************
							BigDecimal loanTotAmt = moneyMapR.get(授信額度合計);
							if (Util.notEquals(custCurr, custCurrR)) {
								loanTotAmt = branchRate.toOtherAmt(custCurrR,
										custCurr, loanTotAmt);
							}

							BigDecimal temp = mergeAllMap.get("total").add(
									loanTotAmt);
							mergeAllMap.put("total", temp);// 總額度

							// 瑕疵押匯額度合計**********************************************
							BigDecimal flawAmtTotal = moneyMapR.get(瑕疵押匯額度合計);
							if (Util.notEquals(custCurr, custCurrR)) {
								flawAmtTotal = branchRate.toOtherAmt(custCurrR,
										custCurr, flawAmtTotal);
							}

							BigDecimal tempFlawRc = mergeAllMap.get("flawRc")
									.add(flawAmtTotal);
							mergeAllMap.put("flawRc", tempFlawRc);// 瑕疵押匯額度合計

							// LGD合計**********************************************
							for (int i = 1; i <= lmsLgdCount; i++) {
								// LGD_X
								String lgdItem = LGD合計 + "_" + Util.trim(i);

								BigDecimal lgdTotAmt = moneyMapR.get(lgdItem);
								if (Util.notEquals(custCurr, custCurrR)) {
									lgdTotAmt = branchRate.toOtherAmt(
											custCurrR, custCurr, lgdTotAmt);
								}
								BigDecimal tempLgd = mergeAllMap.get(lgdItem)
										.add(lgdTotAmt);
								mergeAllMap.put(lgdItem, tempLgd);// LGD額度

							}

						}

					}

				}
			}

		}

		// 若本人沒有出現在合併關係企業內，則要補回來
		if (!hasSelf) {
			// 本人前面沒有在合併關係企業，這邊要加回來
			for (String nameR : custIdMap.keySet()) {
				Map<String, BigDecimal> moneyMapR = null;
				String custCurrR = nameR.substring(0, 3);
				String custIdR = nameR.substring(3, nameR.length());

				String gCustIdR = Util.trim(Util.getLeftStr(custIdR,
						custIdR.length() - 1));
				String gDupNoR = Util.trim(Util.getRightStr(custIdR, 1));
				moneyMapR = custIdMap.get(nameR);

				if (Util.equals(gCustId, gCustIdR)
						&& Util.equals(gDupNo, gDupNoR)) {
					// 補本人回來

					// 授信額度合計**********************************************
					BigDecimal loanTotAmt = moneyMapR.get(授信額度合計);
					if (Util.notEquals(custCurr, custCurrR)) {
						loanTotAmt = branchRate.toOtherAmt(custCurrR, custCurr,
								loanTotAmt);
					}

					BigDecimal temp = mergeAllMap.get("total").add(loanTotAmt);
					mergeAllMap.put("total", temp);// 總額度

					// 瑕疵押匯額度合計**********************************************
					BigDecimal flawAmtTotal = moneyMapR.get(瑕疵押匯額度合計);
					if (Util.notEquals(custCurr, custCurrR)) {
						flawAmtTotal = branchRate.toOtherAmt(custCurrR,
								custCurr, flawAmtTotal);
					}

					BigDecimal tempFlawRc = mergeAllMap.get("flawRc").add(
							flawAmtTotal);
					mergeAllMap.put("flawRc", tempFlawRc);// 瑕疵押匯額度合計

					// LGD合計**********************************************
					for (int i = 1; i <= lmsLgdCount; i++) {
						// LGD_X
						String lgdItem = LGD合計 + "_" + Util.trim(i);

						BigDecimal lgdTotAmt = moneyMapR.get(lgdItem);
						if (Util.notEquals(custCurr, custCurrR)) {
							lgdTotAmt = branchRate.toOtherAmt(custCurrR,
									custCurr, lgdTotAmt);
						}
						BigDecimal tempLgd = mergeAllMap.get(lgdItem).add(
								lgdTotAmt);
						mergeAllMap.put(lgdItem, tempLgd);// LGD額度

					}

				}

			}
		}

		return mergeAllMap;
	}

	/**
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 * 
	 * @param l140m01a
	 * @return
	 */
	BigDecimal setExpectLgd(L140M01A l140m01a) {

		L120M01A l120m01a = null;
		L120M01C l120m01c = l140m01a.getL120m01c();
		if (l120m01c != null) {
			l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());

		}
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		BigDecimal expectLgd = this.lmsService.processLgd(l120m01a, l140m01a,
				l120m01c);

		l140m01a.setExpectLgd(expectLgd);

		l140m01aDao.save(l140m01a);

		return expectLgd;
	}

	/**
	 * 儲存計算結果 (多幣別計算)
	 * 
	 * @param custIdMap
	 *            借款人 加總後資料
	 * @param allcurr
	 *            多幣別的文字
	 * @param l140m01as
	 *            該簽報書下所有的額度明細表
	 * @param showCurr
	 *            是否顯示多幣別
	 * @param branchRate
	 *            該分行幣別匯率
	 */
	private void saveCountResult(
			Map<String, Map<String, BigDecimal>> custIdMap,
			HashMap<String, HashMap<String, String>> allcurr,
			List<L140M01A> l140m01as, Boolean showCurr, BranchRate branchRate,
			Boolean saveRateFg, Map<String, Integer> twoMap,
			Map<String, String> twoMapNowRate) throws Exception {

		Map<String, String> saveRateCustIdMap = new HashMap<String, String>();
		String l120m01aMainId = "";// L120M01A.MAINID

		String allCase_lgdTotMgCurr = "";
		for (String name : custIdMap.keySet()) {
			Map<String, BigDecimal> moneyMap = null;
			moneyMap = custIdMap.get(name);
			for (String n : moneyMap.keySet()) {
				if (StringUtils.contains(n, LGD合計_全案_幣別)) {
					String curr = n.split("_")[1];
					allCase_lgdTotMgCurr = curr;
					break;
				}
			}
			break;
		}

		// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
		String itemType = "";
		String caseBrid = null;
		L120M01A l120m01a = null;
		L120M01C l120m01c = null;
		if (l140m01as != null && !l140m01as.isEmpty()) {
			l120m01c = l140m01as.get(0).getL120m01c();
			if (l120m01c != null) {
				itemType = l120m01c.getItemType();
				l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());
				caseBrid = l120m01a.getCaseBrId();
			}
		}

		boolean needShowLoanCountRcTotal = lmsService.needShowLoanCountRcTotal(
				l120m01a, l120m01a.getCaseBrId(), itemType);

		// 將計算的結果放入到對應欄位內
		for (String name : custIdMap.keySet()) {
			Map<String, BigDecimal> moneyMap = null;
			String custCurr = name.substring(0, 3);
			String custId = name.substring(3, name.length());
			moneyMap = custIdMap.get(name);

			// 將計算的結果放入到對應欄位內
			for (L140M01A l140m01a : l140m01as) {

				String custIdKey = l140m01a.getCustId() + l140m01a.getDupNo();
				// 當有key值在做儲存
				if (custId.equals(custIdKey)) {

					// 每個借款人第一筆額度明細表才寫
					if (saveRateFg) {
						if (!twoMap.get(custIdKey).equals(0)) {
							if (!saveRateCustIdMap.containsKey(custIdKey)) {
								l140m01a.setRmk(Util.equals(l140m01a.getRmk(),
										"") ? twoMapNowRate.get(custIdKey)
										: l140m01a.getRmk() + "\r"
												+ twoMapNowRate.get(custIdKey));
								saveRateCustIdMap.put(custIdKey, "Y");
							}
						}
					}

					BigDecimal loanTotAmt = moneyMap.get(授信額度合計).setScale(2,
							BigDecimal.ROUND_HALF_UP);
					BigDecimal gtAmt = moneyMap.get(擔保授信合計).setScale(2,
							BigDecimal.ROUND_HALF_UP);

					BigDecimal lvTotAmt = moneyMap.get(前准額度批覆合計).setScale(2,
							BigDecimal.ROUND_HALF_UP);
					BigDecimal lvassTotAmt = moneyMap.get(前准額度批覆擔保合計).setScale(
							2, BigDecimal.ROUND_HALF_UP);
					BigDecimal loanTotLAmt = moneyMap.get(衍生性商品相當合計).setScale(
							2, BigDecimal.ROUND_HALF_UP);
					BigDecimal loanTotZAmt = moneyMap.get(衍生性商品原始合計).setScale(
							2, BigDecimal.ROUND_HALF_UP);

					// J-111-0461_05097_B1002
					// 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
					BigDecimal flawAmtTotal = moneyMap.get(瑕疵押匯額度合計).setScale(
							2, BigDecimal.ROUND_HALF_UP);
					BigDecimal generalLoanTotAmt = moneyMap.get(總授信額度合計)
							.setScale(2, BigDecimal.ROUND_HALF_UP);

					// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
					BigDecimal standAloneAuthTotal = moneyMap.get(單獨另計授權額度合計)
							.setScale(2, BigDecimal.ROUND_HALF_UP);

					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					BigDecimal flawAmtMgTotal = moneyMap.get(瑕疵押匯額度合計_全案)
							.setScale(2, BigDecimal.ROUND_HALF_UP);
					BigDecimal generalLoanMgTotAmt = moneyMap.get(總授信額度合計_全案)
							.setScale(2, BigDecimal.ROUND_HALF_UP);
					// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
					BigDecimal flawAmtRcTotal = moneyMap.get(瑕疵押匯額度合計_合併關係)
							.setScale(2, BigDecimal.ROUND_HALF_UP);
					BigDecimal generalLoanRcTotAmt = moneyMap.get(總授信額度合計_合併關係)
							.setScale(2, BigDecimal.ROUND_HALF_UP);

					l140m01a.setLoanTotAmt(loanTotAmt);
					l140m01a.setLoanTotLAmt(loanTotLAmt);
					l140m01a.setLoanTotZAmt(loanTotZAmt);
					l140m01a.setAssureTotAmt(gtAmt);
					l140m01a.setLVTotAmt(lvTotAmt);
					l140m01a.setLVAssTotAmt(lvassTotAmt);
					// J-111-0461_05097_B1002
					// 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
					l140m01a.setFlawAmtTotal(flawAmtTotal);
					l140m01a.setGeneralLoanTotAmt(generalLoanTotAmt);
					// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
					l140m01a.setStandAloneAuthTotal(standAloneAuthTotal);
					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					l140m01a.setFlawAmtMgTotal(flawAmtMgTotal);
					l140m01a.setGeneralLoanMgTotAmt(generalLoanMgTotAmt);
					l140m01a.setLgdTotMgCurr(allCase_lgdTotMgCurr);
					// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
					if (needShowLoanCountRcTotal) {
						l140m01a.setFlawAmtRcTotal(flawAmtRcTotal);
						l140m01a.setGeneralLoanRcTotAmt(generalLoanRcTotAmt);
					} else {
						l140m01a.setFlawAmtRcTotal(null);
						l140m01a.setGeneralLoanRcTotAmt(null);
					}

					// 取消減額(額度)
					BigDecimal decAppAmt = moneyMap.get(減額額度合計).setScale(2,
							BigDecimal.ROUND_HALF_UP);
					l140m01a.setDecApplyTotCurr(custCurr);
					// 如果授信額度合計小於前准額度批覆合計 為取消、減額合計
					l140m01a.setDecApplyTotAmt(decAppAmt);

					l140m01a.setIncApplyTotCurr(custCurr);
					// 新做 增額 = 授信額度合計 -前准額度批覆合計 + 取消減額合計
					l140m01a.setIncApplyTotAmt(loanTotAmt.subtract(lvTotAmt)
							.add(decAppAmt));

					// 取消減額(擔保)
					BigDecimal decAssAmt = moneyMap.get(減額擔保額度合計)
							.setScale(2, BigDecimal.ROUND_HALF_UP).abs();
					l140m01a.setDecAssTotCurr(custCurr);
					// 如果擔保授信合計小於前准額度批覆擔保合計為取消、減額合計
					l140m01a.setDecAssTotAmt(decAssAmt);
					// 新做 增額 = 擔保授信合計 -前准額度批覆擔保合計 + 取消減額合計
					l140m01a.setIncAssTotAmt(gtAmt.subtract(lvassTotAmt)
							.add(decAssAmt).abs());
					l140m01a.setIncAssTotCurr(custCurr);
					// 幣別
					l140m01a.setLVAssTotCurr(custCurr);
					l140m01a.setLVTotCurr(custCurr);
					l140m01a.setLoanTotCurr(custCurr);
					l140m01a.setLoanTotLCurr(custCurr);
					l140m01a.setLoanTotZCurr(custCurr);
					l140m01a.setAssureTotCurr(custCurr);
					if (allcurr != null && allcurr.containsKey(custIdKey)
							&& showCurr) {
						l140m01a.setMultiAmt(allcurr.get(custIdKey).get(
								授信額度合計多幣別說明));
						l140m01a.setMultiAssureAmt(allcurr.get(custIdKey).get(
								擔保授信合計多幣別說明));

					} else {
						l140m01a.setMultiAmt("");
						l140m01a.setMultiAssureAmt("");
					}

					if (allcurr != null && allcurr.containsKey(custIdKey)) {
						l140m01a.setDervMultiAmt(allcurr.get(custIdKey).get(
								衍生性授信額度合計多幣別說明));
						l140m01a.setNewDervMultiAmtFlag("Y");

					} else {
						l140m01a.setDervMultiAmt("");
						l140m01a.setNewDervMultiAmtFlag("Y");
					}

					l120m01aMainId = l120m01a.getMainId();

					Map<String, String> lgdMap = lmsService.getLgdTotAmtParam(
							l140m01a, null);
					int lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
							"lmsLgdCount", "0"));

					int lmsLgdCountTotal = Util.parseInt(MapUtils.getString(
							lgdMap, "lmsLgdCountTotal", "0"));

					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					for (int i = 1; i <= lmsLgdCountTotal; i++) {
						l140m01a.set(LGD合計 + "_" + i, null);

						// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
						l140m01a.set(LGD合計_全案 + "_" + i, null);

						// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
						l140m01a.set(LGD合計_合併關係 + "_" + i, null);
					}
					// l140m01a.setLgdTotAmt_U(null);
					// l140m01a.setLgdTotAmt_P(null);
					// l140m01a.setLgdTotAmt_S(null);

					if (l120m01c != null) {

						if (l120m01a != null) {

							boolean showLgdTotAmt = lmsService.showLgdTotAmt(
									l120m01a, l120m01a.getCaseBrId(), itemType);

							if (showLgdTotAmt) {

								// J-111-0343_05097_B1003 Web
								// e-Loan修改企金額度明細表合計之功能

								for (int i = 1; i <= lmsLgdCount; i++) {
									BigDecimal lgdTotAmt = moneyMap.get(
											LGD合計 + "_" + i).setScale(2,
											BigDecimal.ROUND_HALF_UP);

									l140m01a.set(LGD合計 + "_" + i, lgdTotAmt);

									// J-111-0461_05097_B1006
									// 授信額度合計新增單獨另計授權及各組LGD合計檢核
									BigDecimal lgdTotAmt_mg = moneyMap.get(
											LGD合計_全案 + "_" + i).setScale(2,
											BigDecimal.ROUND_HALF_UP);

									l140m01a.set(LGD合計_全案 + "_" + i,
											lgdTotAmt_mg);

									// J-111-0461_05097_B1009
									// 授信額度合計新增單獨另計授權及各組LGD合計檢核
									BigDecimal lgdTotAmt_rc = moneyMap.get(
											LGD合計_合併關係 + "_" + i).setScale(2,
											BigDecimal.ROUND_HALF_UP);

									// J-111-0461_05097_B1009
									// 授信額度合計新增單獨另計授權及各組LGD合計檢核
									if (needShowLoanCountRcTotal) {
										l140m01a.set(LGD合計_合併關係 + "_" + i,
												lgdTotAmt_rc);
									} else {
										l140m01a.set(LGD合計_合併關係 + "_" + i, null);
									}

								}

								// BigDecimal lgdTotAmt_U = moneyMap.get(
								// LGD其中無擔保合計).setScale(2,
								// BigDecimal.ROUND_HALF_UP);
								// BigDecimal lgdTotAmt_P = moneyMap.get(
								// LGD其中擬制部分擔保合計).setScale(2,
								// BigDecimal.ROUND_HALF_UP);
								// BigDecimal lgdTotAmt_S = moneyMap.get(
								// LGD其中擬制十足擔保合計).setScale(2,
								// BigDecimal.ROUND_HALF_UP);
								//
								// l140m01a.setLgdTotAmt_U(lgdTotAmt_U);
								// l140m01a.setLgdTotAmt_P(lgdTotAmt_P);
								// l140m01a.setLgdTotAmt_S(lgdTotAmt_S);

							}
						}
					}
				}
			}
		}

		// J-111-0461 建議授權層級
		// 建議審核層級
		// set完值後利用LGD_X來計算建議層級
		if (Util.isNotEmpty(l120m01aMainId)) {
			// L120M01A l120m01a = l120m01aDao.findByMainId(l120m01aMainId);
			Map<String, L140M01A> needCountLGDMap = lmsService
					.getNeedCountLgdTotalL140m01a(l140m01as);
			lmsService.suggestCaseLvlCheck(l120m01a.getMainId(), l120m01a,
					l140m01as, needCountLGDMap);
		}

		l140m01aDao.save(l140m01as);
	}

	/**
	 * 儲存計算結果
	 * 
	 * @param custIdMap
	 *            借款人資料
	 * @param l140m01as
	 *            該簽報書下所有的額度明細表
	 */
	private void saveCountResult(
			Map<String, Map<String, BigDecimal>> custIdMap,
			List<L140M01A> l140m01as, BranchRate branchRate) throws Exception {

		Properties Prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		String l120m01aMainId = "";// L120M01A.MAINID

		// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
		String itemType = "";
		String caseBrid = null;
		L120M01A l120m01a = null;
		L120M01C l120m01c = null;
		if (l140m01as != null && !l140m01as.isEmpty()) {
			l120m01c = l140m01as.get(0).getL120m01c();
			if (l120m01c != null) {
				itemType = l120m01c.getItemType();
				l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());
				l120m01aMainId = l120m01a.getMainId();
				caseBrid = l120m01a.getCaseBrId();
			}
		}

		boolean needShowLoanCountRcTotal = lmsService.needShowLoanCountRcTotal(
				l120m01a, l120m01a.getCaseBrId(), itemType);

		String allCase_lgdTotMgCurr = "";
		for (String name : custIdMap.keySet()) {
			Map<String, BigDecimal> moneyMap = null;
			moneyMap = custIdMap.get(name);
			for (String n : moneyMap.keySet()) {
				if (StringUtils.contains(n, LGD合計_全案_幣別)) {
					String curr = n.split("_")[1];
					allCase_lgdTotMgCurr = curr;
				}
			}
			break;
		}

		for (String name : custIdMap.keySet()) {
			Map<String, BigDecimal> moneyMap = null;
			String custCurr = name.substring(0, 3);
			String custId = name.substring(3, name.length());
			moneyMap = custIdMap.get(name);

			// 將計算的結果放入到對應欄位內
			for (L140M01A l140m01a : l140m01as) {
				String custIdKey = l140m01a.getCustId() + l140m01a.getDupNo();

				// 當有key值在做儲存
				if (custId.equals(custIdKey)) {
					BigDecimal loanTotAmt = moneyMap.get(授信額度合計);
					BigDecimal loanTotLAmt = moneyMap.get(衍生性商品相當合計);
					BigDecimal loanTotZAmt = moneyMap.get(衍生性商品原始合計);
					BigDecimal gtAmt = moneyMap.get(擔保授信合計);

					// J-111-0461_05097_B1002
					// 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
					BigDecimal flawAmtTotal = moneyMap.get(瑕疵押匯額度合計);
					BigDecimal generalLoanTotAmt = moneyMap.get(總授信額度合計);

					// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
					BigDecimal standAloneAuthTotal = moneyMap.get(單獨另計授權額度合計);

					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					BigDecimal flawAmtMgTotal = moneyMap.get(瑕疵押匯額度合計_全案);
					BigDecimal generalLoanMgTotAmt = moneyMap.get(總授信額度合計_全案);

					// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
					BigDecimal flawAmtRcTotal = moneyMap.get(瑕疵押匯額度合計_合併關係);
					BigDecimal generalLoanRcTotAmt = moneyMap.get(總授信額度合計_合併關係);

					l140m01a.setLoanTotAmt(loanTotAmt);
					l140m01a.setLoanTotLAmt(loanTotLAmt);
					l140m01a.setLoanTotZAmt(loanTotZAmt);
					l140m01a.setAssureTotAmt(gtAmt);

					// J-111-0461_05097_B1002
					// 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
					l140m01a.setFlawAmtTotal(flawAmtTotal);
					l140m01a.setGeneralLoanTotAmt(generalLoanTotAmt);

					// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
					l140m01a.setStandAloneAuthTotal(standAloneAuthTotal);

					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					l140m01a.setFlawAmtMgTotal(flawAmtMgTotal);
					l140m01a.setGeneralLoanMgTotAmt(generalLoanMgTotAmt);
					l140m01a.setLgdTotMgCurr(allCase_lgdTotMgCurr);

					// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
					if (needShowLoanCountRcTotal) {
						l140m01a.setFlawAmtRcTotal(flawAmtRcTotal);
						l140m01a.setGeneralLoanRcTotAmt(generalLoanRcTotAmt);
					} else {
						l140m01a.setFlawAmtRcTotal(null);
						l140m01a.setGeneralLoanRcTotAmt(null);
					}

					logger.debug("前准額度批覆合計===>" + moneyMap.get(前准額度批覆合計));
					BigDecimal lvTotAmt = moneyMap.get(前准額度批覆合計).setScale(2,
							BigDecimal.ROUND_HALF_UP);
					logger.debug("前准額度批覆擔保合計===>" + moneyMap.get(前准額度批覆擔保合計));
					BigDecimal lvassTotAmt = moneyMap.get(前准額度批覆擔保合計).setScale(
							2, BigDecimal.ROUND_HALF_UP);
					l140m01a.setLVTotAmt(lvTotAmt);
					l140m01a.setLVAssTotAmt(lvassTotAmt);
					// 取消減額(額度)
					BigDecimal decAppAmt = moneyMap.get(減額額度合計).setScale(2,
							BigDecimal.ROUND_HALF_UP);
					l140m01a.setDecApplyTotCurr(custCurr);
					// 如果授信額度合計小於前准額度批覆合計 為取消、減額合計
					l140m01a.setDecApplyTotAmt(decAppAmt);

					l140m01a.setIncApplyTotCurr(custCurr);
					// 新做 增額 = 授信額度合計 -前准額度批覆合計 + 取消減額合計
					l140m01a.setIncApplyTotAmt(loanTotAmt.subtract(lvTotAmt)
							.add(decAppAmt));

					// 取消減額(擔保)
					BigDecimal decAssAmt = moneyMap.get(減額擔保額度合計)
							.setScale(2, BigDecimal.ROUND_HALF_UP).abs();
					l140m01a.setDecAssTotCurr(custCurr);
					// 如果擔保授信合計小於前准額度批覆擔保合計為取消、減額合計
					l140m01a.setDecAssTotAmt(decAssAmt);
					// 新做 增額 = 擔保授信合計 -前准額度批覆擔保合計 + 取消減額合計
					l140m01a.setIncAssTotAmt(gtAmt.subtract(lvassTotAmt)
							.add(decAssAmt).abs());
					l140m01a.setIncAssTotCurr(custCurr);
					// 幣別
					l140m01a.setLVAssTotCurr(custCurr);
					l140m01a.setLVTotCurr(custCurr);

					l140m01a.setLoanTotCurr(custCurr);
					l140m01a.setLoanTotLCurr(custCurr);
					l140m01a.setLoanTotZCurr(custCurr);
					l140m01a.setAssureTotCurr(custCurr);
					l140m01a.setMultiAmt("");
					String money = Prop.getProperty("other.money");
					l140m01a.setDervMultiAmt(custCurr + " "
							+ NumConverter.addComma(loanTotZAmt) + money);
					l140m01a.setNewDervMultiAmtFlag("Y");

					l120m01aMainId = l120m01a.getMainId();

					Map<String, String> lgdMap = lmsService.getLgdTotAmtParam(
							l140m01a, null);
					int lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
							"lmsLgdCount", "0"));
					int lmsLgdCountTotal = Util.parseInt(MapUtils.getString(
							lgdMap, "lmsLgdCountTotal", "0"));

					for (int i = 1; i <= lmsLgdCountTotal; i++) {
						l140m01a.set(LGD合計 + "_" + i, null);
						// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
						l140m01a.set(LGD合計_全案 + "_" + i, null);
						// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
						l140m01a.set(LGD合計_合併關係 + "_" + i, null);
					}

					if (l120m01c != null) {
						itemType = Util.trim(l120m01c.getItemType());

						if (l120m01a != null) {
							boolean showLgdTotAmt = lmsService.showLgdTotAmt(
									l120m01a, l120m01a.getCaseBrId(), itemType);

							if (showLgdTotAmt) {

								// J-111-0343_05097_B1003 Web
								// e-Loan修改企金額度明細表合計之功能

								for (int i = 1; i <= lmsLgdCount; i++) {
									BigDecimal lgdTotAmt = moneyMap.get(
											LGD合計 + "_" + i).setScale(2,
											BigDecimal.ROUND_HALF_UP);

									l140m01a.set(LGD合計 + "_" + i, lgdTotAmt);

									// J-111-0461_05097_B1006
									// 授信額度合計新增單獨另計授權及各組LGD合計檢核
									BigDecimal lgdTotAmt_mg = moneyMap.get(
											LGD合計_全案 + "_" + i).setScale(2,
											BigDecimal.ROUND_HALF_UP);

									l140m01a.set(LGD合計_全案 + "_" + i,
											lgdTotAmt_mg);

									// J-111-0461_05097_B1009
									// 授信額度合計新增單獨另計授權及各組LGD合計檢核
									BigDecimal lgdTotAmt_rc = moneyMap.get(
											LGD合計_合併關係 + "_" + i).setScale(2,
											BigDecimal.ROUND_HALF_UP);

									if (needShowLoanCountRcTotal) {
										l140m01a.set(LGD合計_合併關係 + "_" + i,
												lgdTotAmt_rc);
									} else {
										l140m01a.set(LGD合計_合併關係 + "_" + i, null);
									}

								}
							}
						}
					}

				}
			}
		}

		// J-111-0461 建議授權層級
		// 建議審核層級
		// set完值後利用LGD_X來計算建議層級
		if (Util.isNotEmpty(l120m01aMainId)) {
			// L120M01A l120m01a = l120m01aDao.findByMainId(l120m01aMainId);
			Map<String, L140M01A> needCountLGDMap = lmsService
					.getNeedCountLgdTotalL140m01a(l140m01as);
			lmsService.suggestCaseLvlCheck(l120m01a.getMainId(), l120m01a,
					l140m01as, needCountLGDMap);
		}

		saveL140m01aList(l140m01as);
	}

	@Override
	public boolean findL140m01aListByMainIdCount(String caseMainId,
			String caseType, String custId, String dupNo, long count) {
		boolean reurst = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L140M01A> l140m01as = l140m01aDao.findByMainIdAndCustId(
				caseMainId, custId, dupNo);
		for (L140M01A l140m01a : l140m01as) {
			l140m01a.setLoanTotAmt(new BigDecimal(count));
			l140m01a.setLoanTotCurr("TWD");
			l140m01a.setUpdater(user.getUserId());
			l140m01a.setUpdateTime(CapDate.getCurrentTimestamp());
			reurst = true;
		}
		saveL140m01aList(l140m01as);
		return reurst;
	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType, String docStatus) {
		return l140m01aDao.findL140m01aListByL120m01cMainId(mainId, caseType,
				docStatus);
	}

	@Override
	public HashMap<String, Map<String, Object>> queryLnf226(
			HashMap<String, String> ownBrIds) {
		List<String> lBanks = new ArrayList<String>();
		List<String> fBanks = new ArrayList<String>();
		for (String ownBrId : ownBrIds.keySet()) {
			IBranch ibranch = branchService.getBranch(ownBrId);
			// 0E1或0E3 屬於子銀行
			if (UtilConstants.BrNoType.國外.equals(ibranch.getBrNoFlag())
					|| UtilConstants.BrNoType.子銀行.equals(ibranch.getBrNoFlag())) {
				fBanks.add(ownBrId);
			} else {
				lBanks.add(ownBrId);
			}
		}

		String[] fOwnBrId = fBanks.toArray(new String[fBanks.size()]);
		String[] lOwnBrId = lBanks.toArray(new String[lBanks.size()]);

		HashMap<String, Map<String, Object>> brankDatas = new HashMap<String, Map<String, Object>>();
		if (fOwnBrId != null && fOwnBrId.length > 0) {
			List<Map<String, Object>> rows = dWASLNOVEROVSService
					.findDW_ASLNOVEROVSForRate(fOwnBrId);
			for (Map<String, Object> row : rows) {
				BigDecimal tot = BigDecimal.ZERO;
				Map<String, Object> data = new HashMap<String, Object>();
				BigDecimal amt = (BigDecimal) row.get("AMT");
				BigDecimal bal = (BigDecimal) row.get("BAL");
				if (BigDecimal.ZERO.compareTo(bal) != 0) {
					tot = amt.divide(bal, 11, BigDecimal.ROUND_HALF_UP)
							.multiply(一百);
				}
				data.put("BRNO", (String) row.get("BRNO"));
				data.put("DATEYM", CapDate.formatDate((Date) row.get("DATEYM"),
						UtilConstants.DateFormat.YYYY_MM));
				data.put("RATE", LMSUtil.calcZero(tot));
				brankDatas.put((String) row.get("BRNO"), data);
			}
		}

		if (lOwnBrId != null && lOwnBrId.length > 0) {
			List<Map<String, Object>> rows = misLNF226Service
					.findLNF226ForRate(lOwnBrId);
			for (Map<String, Object> row : rows) {
				BigDecimal tot = BigDecimal.ZERO;
				Map<String, Object> data = new HashMap<String, Object>();
				BigDecimal bal = (BigDecimal) row.get("BAL");
				BigDecimal amt = (BigDecimal) row.get("AMT");
				if (BigDecimal.ZERO.compareTo(bal) != 0) {
					tot = amt.divide(bal, 11, BigDecimal.ROUND_HALF_UP)
							.multiply(一百);
				}
				data.put("BRNO", (String) row.get("BRNO"));
				data.put("DATEYM", (String) row.get("DATEYM"));
				data.put("RATE", LMSUtil.calcZero(tot));
				brankDatas.put((String) row.get("BRNO"), data);
			}
		}
		return brankDatas;
	}

	@Override
	public Map<String, Object> queryLnsp0050(String ownBrId, String unitCode,
			String classCD) {

		return this.misStoredProcService.callLNSP0050(ownBrId, unitCode,
				classCD);

	}

	@Override
	public Map<String, String> queryDwlnquotov(String custId, String dupNo,
			String cntrNo, String lastcurr) {
		dupNo = Util.trim(dupNo);
		custId = Util.trim(custId);
		// THZ0066190 0 Y02500500038

		Map<String, String> countMap = new HashMap<String, String>();

		// 2為國外 ，當行別為國外時查DW 其他查 mis
		if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(
				cntrNo.substring(0, 3)).getBrNoFlag())) {
			// 當重覆序號為不等於0 要加在custId之後
			// ex1 A123456789 0 ==A123456789
			// ex2 A123456789 1 ==A1234567891
			if (!("0".equals(dupNo))) {
				custId = StrUtils.concat(custId, dupNo);
			}
			// 購料放款案下已開狀未到單金額
			List<Map<String, Object>> rows = this.dwLnquotovService
					.findDW_LNQUOTOV_Lcamt(custId, Util.trim(cntrNo));

			for (Map<String, Object> dataMap : rows) {
				String flag = Util.trim(((String) dataMap.get("FLAG")));
				BigDecimal lcamt = (BigDecimal) dataMap.get("LCAMT");
				String lccurr = Util.trim((String) dataMap.get("LCCURR"));
				countMap.put("flag", flag);
				countMap.put("lCAmt", lcamt.toString());
				countMap.put("lCCurr", lccurr);
			}
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

			// CR0002-引進帳務資訊不再引進帳務系統之收付彙計數
			// // 借方彙計數,貸方彙計數
			// List<Map<String, Object>> rows2 = this.dwdbBaseService
			// .findDW_LNCNTROVS_TOT(custId, cntrNo);
			//
			// for (Map<String, Object> dataMap : rows2) {
			// String curr = Util.trim(((String) dataMap.get("CURR")));
			// String crtot = Util.trim((String) dataMap.get("CRTOT"));
			// String drtot = Util.trim((String) dataMap.get("DRTOT"));
			// countMap.put("curr", curr);
			// // 收 借款收付彙計數 金額
			// countMap.put("crtot", crtot);
			// // 付 借款收付彙計數 金額
			// countMap.put("drtot", drtot);
			// }

			// 餘額
			// 測試資料
			// List<?> rows3 =
			// this.dwdbService.findDW_LNQUOTOV_selBLAmt("PHZ0007123",
			// "0B2", "0B2500110730");
			// this.dwdbService.findDW_LNQUOTOV_selBLAmt("USZ0143437",
			// "0M2", "0M2501000659");
			// List<Map<String, Object>> rows3 = this.dwdbBaseService
			// .findDW_LNQUOTOV_selBLAmt(custId, user.getUnitNo(), cntrNo);
			// for (Map<String, Object> dataMap : rows3) {
			// String curr = Util.trim(((String) dataMap.get("CURR")));
			// BigDecimal count = ((BigDecimal) dataMap.get("COUNT"));
			// // 餘額幣別
			// countMap.put("BLCurr", curr);
			// // 餘額金額
			// countMap.put("BLAmt", String.valueOf(count.setScale(0,
			// BigDecimal.ROUND_HALF_UP)));
			// }

			// J-110-0485_05097_B1001 於簽報書新增LGD欄位
			BranchRate branchRate = lmsService.getBranchRate(user.getUnitNo());

			String ovsCurr = "";
			List<Map<String, Object>> lncntrovsList = dwdbBaseService
					.findDW_LNCNTROVS_By_CntrNo(cntrNo);
			if (lncntrovsList != null && !lncntrovsList.isEmpty()) {
				for (Map<String, Object> lncntrovs : lncntrovsList) {

					String LNF022_FACT_SWFT = Util.trim(MapUtils.getString(
							lncntrovs, "LNF022_FACT_SWFT"));
					ovsCurr = LNF022_FACT_SWFT;
					BigDecimal LNF022_ADJ_FAMT = Util.equals(
							MapUtils.getString(lncntrovs, "LNF022_ADJ_FAMT"),
							"") ? BigDecimal.ZERO : Util
							.parseBigDecimal(MapUtils.getString(lncntrovs,
									"LNF022_ADJ_FAMT"));

					BigDecimal LNF022_ADJ_FAMT_T = Util.equals(
							MapUtils.getString(lncntrovs, "LNF022_ADJ_FAMT_T"),
							"") ? BigDecimal.ZERO : Util
							.parseBigDecimal(MapUtils.getString(lncntrovs,
									"LNF022_ADJ_FAMT_T"));

					BigDecimal LNF022_LOAN_BAL_S = Util.equals(
							MapUtils.getString(lncntrovs, "LNF022_LOAN_BAL_S"),
							"") ? BigDecimal.ZERO : Util
							.parseBigDecimal(MapUtils.getString(lncntrovs,
									"LNF022_LOAN_BAL_S"));
					BigDecimal LNF022_LOAN_BAL_N = Util.equals(
							MapUtils.getString(lncntrovs, "LNF022_LOAN_BAL_N"),
							"") ? BigDecimal.ZERO : Util
							.parseBigDecimal(MapUtils.getString(lncntrovs,
									"LNF022_LOAN_BAL_N"));

					BigDecimal LOAN_BAL_OS = Util.equals(
							MapUtils.getString(lncntrovs, "LOAN_BAL_OS"), "") ? BigDecimal.ZERO
							: Util.parseBigDecimal(MapUtils.getString(
									lncntrovs, "LOAN_BAL_OS"));
					BigDecimal LOAN_BAL_ON = Util.equals(
							MapUtils.getString(lncntrovs, "LOAN_BAL_ON"), "") ? BigDecimal.ZERO
							: Util.parseBigDecimal(MapUtils.getString(
									lncntrovs, "LOAN_BAL_ON"));

					BigDecimal LNF022_LOAN_BAL = LOAN_BAL_OS.add(LOAN_BAL_ON);
					BigDecimal LNF022_LOAN_BAL_TW = LNF022_LOAN_BAL_S
							.add(LNF022_LOAN_BAL_N);

					// 餘額幣別
					countMap.put("BLCurr", LNF022_FACT_SWFT);
					// 餘額金額
					countMap.put("BLAmt", String.valueOf(LNF022_LOAN_BAL
							.setScale(0, BigDecimal.ROUND_HALF_UP)));
					break;
				}
			}

			// J-110-0485_05097_B1001 於簽報書新增LGD欄位
			// 應收利息金額
			if (!Util.isEmpty(ovsCurr)) {

				List<Map<String, Object>> dwAslndavgovsList = dwdbBaseService
						.findSumDW_ASLNDAVGOVSByCntrNo(cntrNo);
				BigDecimal INT_AMT_T = BigDecimal.ZERO;
				if (dwAslndavgovsList != null && !dwAslndavgovsList.isEmpty()) {
					for (Map<String, Object> dwAslndavgovs : dwAslndavgovsList) {
						String INT_CURR = MapUtils.getString(dwAslndavgovs,
								"INT_CURR");
						BigDecimal xINT_AMT_T = Util.equals(
								MapUtils.getString(dwAslndavgovs, "INT_AMT_T"),
								"") ? BigDecimal.ZERO : Util
								.parseBigDecimal(MapUtils.getString(
										dwAslndavgovs, "INT_AMT_T"));

						if (!Util.isEmpty(INT_CURR)
								&& xINT_AMT_T.compareTo(BigDecimal.ZERO) > 0) {
							if (Util.equals(INT_CURR, ovsCurr)) {
								INT_AMT_T = INT_AMT_T.add(xINT_AMT_T);
							} else {
								INT_AMT_T = INT_AMT_T.add(branchRate
										.toOtherAmt(INT_CURR, ovsCurr,
												xINT_AMT_T));
							}

						}

					}

					countMap.put("rcvCurr", ovsCurr);
					countMap.put("rcvInt", String.valueOf(INT_AMT_T));

				}
			}

		} else {
			// 前准額度幣別
			String lvCurr = lastcurr;
			// 國內　購料放款案下已開狀未到單金額
			List<Map<String, Object>> rows = misLNF022Service
					.findByContractAndCustId(
							Util.addSpaceWithValue(custId, 10), dupNo, cntrNo);
			for (Map<String, Object> dataMap : rows) {
				String flag = Util.trim(((String) dataMap.get("FLAG")));
				BigDecimal lcamt = (BigDecimal) dataMap.get("LCAMT");
				String lccurr = "TWD";
				countMap.put("flag", flag);
				countMap.put("lCAmt", String.valueOf(lcamt));
				countMap.put("lCCurr", lccurr);
			}

			// 國內　餘額 要做計算
			// LNF150_CUST_DUP
			// 2012_07_12_建霖提餘額移除分行條件
			// List<Map<String, Object>> rows2 = misLNF150Service
			// .findByContractAndCustId(custId, dupNo,
			// cntrNo.substring(0, 3), cntrNo);
			List<Map<String, Object>> rows2 = misLNF150Service
					.findByContractAndCustId(custId, dupNo, cntrNo);
			// 用來檢查幣別的有幾種
			HashMap<String, BigDecimal> mapCurr = new HashMap<String, BigDecimal>();

			// 台幣餘額加總
			BigDecimal TWDCount = BigDecimal.ZERO;
			for (Map<String, Object> dataMap : rows2) {
				String curr = (String) dataMap.get("CURR");
				BigDecimal gBal = (BigDecimal) dataMap.get("LOANBAL");
				BigDecimal N_Bal = (BigDecimal) dataMap.get("NTDLBAL");
				TWDCount = TWDCount.add(N_Bal);
				if (mapCurr.containsKey(curr)) {
					mapCurr.put(curr, mapCurr.get(curr).add(gBal));
				} else {
					mapCurr.put(curr, gBal);
				}

			}

			String blCurr = lvCurr;
			String blAmt = "";
			// 當 前準額度的幣別為台幣，就拿台幣加總當餘額
			if (UtilConstants.CURR.TWD.equals(lvCurr) || "".equals(lvCurr)) {
				// 餘額金額
				blAmt = String.valueOf(TWDCount.setScale(0,
						BigDecimal.ROUND_HALF_UP));
			} else {
				// 多種幣別--> 用台幣餘額直接換算為前準額度幣別
				if (mapCurr.size() > 1) {
					Map<String, Object> currConver = misRatetblService
							.getNewestByCurr(blCurr);

					BigDecimal rate = (BigDecimal) currConver.get("ENDRATE");
					blAmt = String.valueOf(TWDCount.setScale(0,
							BigDecimal.ROUND_HALF_UP).divide(rate, 0,
							RoundingMode.HALF_DOWN));
				} else {
					// 只有一種幣別-->若同前準額度 則不用換算 否則就用台幣餘額直接換算為前準額度幣別
					// 同前準額度 不用換算
					if (mapCurr.containsKey(lvCurr)) {
						blAmt = String.valueOf(mapCurr.get(lvCurr).setScale(0,
								BigDecimal.ROUND_HALF_UP));

					} else {

						Map<String, Object> currConver = misRatetblService
								.getNewestByCurr(blCurr);

						BigDecimal rate = (BigDecimal) currConver
								.get("ENDRATE");
						// 與前準額度不同 要用台幣換算
						blAmt = String.valueOf(TWDCount.setScale(0,
								BigDecimal.ROUND_HALF_UP).divide(rate, 0,
								RoundingMode.HALF_DOWN));
					}
				}
			}

			countMap.put("BLCurr", blCurr);
			// 餘額金額
			countMap.put("BLAmt", blAmt);

			// J-110-0485_05097_B1001 於簽報書新增LGD欄位
			// 應收利息金額
			Map<String, Object> lnf252Map = misdbBASEService
					.findLnf252ByCntrNo(cntrNo);
			if (lnf252Map != null) {
				BigDecimal LNF252_FACT_AMT_NT = Util
						.equals(MapUtils.getString(lnf252Map,
								"LNF252_FACT_AMT_NT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf252Map,
								"LNF252_FACT_AMT_NT"));
				BigDecimal LNF252_LOAN_BAL_NT = Util
						.equals(MapUtils.getString(lnf252Map,
								"LNF252_LOAN_BAL_NT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf252Map,
								"LNF252_LOAN_BAL_NT"));
				BigDecimal LNF252_RCV_INT_NT = Util.equals(
						MapUtils.getString(lnf252Map, "LNF252_RCV_INT_NT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf252Map,
								"LNF252_RCV_INT_NT"));

				countMap.put("rcvCurr", UtilConstants.CURR.TWD);
				countMap.put("rcvInt", String.valueOf(LNF252_RCV_INT_NT));
			}

		}

		return countMap;
	}

	/**
	 * 取得所有的共用額度序號
	 * 
	 * @param l140m01as
	 */
	private Map<String, Integer> queryMainCommSno(List<L140M01A> l140m01as) {
		Map<String, Integer> custMap = new HashMap<String, Integer>();
		for (L140M01A l140m01a : l140m01as) {
			String cust = Util.trim(l140m01a.getCustId())
					+ Util.trim(l140m01a.getDupNo());
			String commSno = Util.trim(l140m01a.getCommSno());
			// 取得所有不重複的共用額度序號
			if (!commSno.isEmpty() && !custMap.containsKey(cust + commSno)) {
				custMap.put(cust + commSno, 0);
			}
		}
		return custMap;
	}

	@Override
	public List<L140M01E> queryByMainIdAndFlag(String mainId, String[] flag) {
		return l140m01eDao.findByMainIdAndFlag(mainId, flag);
	}

	/**
	 * J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 */
	@Override
	public CapAjaxFormResult findL140m01editCount(String caseMainId,
			String caseType, String countCurr, Boolean showCurr,
			Boolean saveRateFg, CapAjaxFormResult result) throws Exception {

		Map<String, Map<String, BigDecimal>> l140m01as = null;
		if (Util.isEmpty(countCurr)) {
			l140m01as = findL140m01Count(caseMainId, caseType);
		} else {
			l140m01as = findL140m01CountToTwoCurr(caseMainId, caseType,
					countCurr, showCurr, saveRateFg);
		}

		JSONArray jsonArray1 = findL140m01editCount_1(caseMainId, caseType,
				countCurr, showCurr, saveRateFg, l140m01as);

		result.set("size1", jsonArray1.size());
		result.set("item1", jsonArray1);

		JSONArray jsonArray2 = findL140m01editCount_2(caseMainId, caseType,
				countCurr, showCurr, saveRateFg, l140m01as);
		result.set("size2", jsonArray2.size());
		result.set("item2", jsonArray2);

		JSONArray jsonArray3 = findL140m01editCount_3(caseMainId, caseType,
				countCurr, showCurr, saveRateFg, l140m01as);
		result.set("size3", jsonArray3.size());
		result.set("item3", jsonArray3);

		JSONArray jsonArray4 = findL140m01editCount_4(caseMainId, caseType,
				countCurr, showCurr, saveRateFg, l140m01as);
		result.set("size4", jsonArray4.size());
		result.set("item4", jsonArray4);

		// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
		JSONArray jsonArray5 = findL140m01editCount_5(caseMainId, caseType,
				countCurr, showCurr, saveRateFg, l140m01as);
		result.set("size5", jsonArray5.size());
		result.set("item5", jsonArray5);

		JSONArray jsonArray6 = findL140m01editCount_6(caseMainId, caseType,
				countCurr, showCurr, saveRateFg, l140m01as);
		result.set("size6", jsonArray6.size());
		result.set("item6", jsonArray6);

		return result;
	}

	/**
	 * J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 * 
	 * @param caseMainId
	 * @param caseType
	 * @param countCurr
	 * @param showCurr
	 * @param saveRateFg
	 * @param l140m01as
	 * @return
	 * @throws Exception
	 */
	public JSONArray findL140m01editCount_1(String caseMainId, String caseType,
			String countCurr, Boolean showCurr, Boolean saveRateFg,
			Map<String, Map<String, BigDecimal>> l140m01as) throws Exception {

		List<L140M01A> l140m01aList = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId, caseType, null);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);

		HashMap<String, HashMap<String, String>> mapName = new HashMap<String, HashMap<String, String>>();
		// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		HashMap<String, String> mapCustLgdTotNeed = new HashMap<String, String>();
		StringBuffer temp = new StringBuffer();
		// 把所有的借款人姓名放進map
		for (L140M01A l140m01a : l140m01aList) {
			String id = Util.trim(l140m01a.getCustId());
			String dupNo = Util.trim(l140m01a.getDupNo());
			String name = Util.trim(l140m01a.getCustName());
			HashMap<String, String> tempMap = new HashMap<String, String>();
			tempMap.put("name", LMSUtil.concat(temp, id, " ", dupNo, " ", name));

			if (!Util.isEmpty(Util.trim(l140m01a.getDervMultiAmt()))) {
				tempMap.put(衍生性授信額度合計多幣別說明, l140m01a.getDervMultiAmt());
			}

			if (!Util.isEmpty(Util.trim(l140m01a.getMultiAmt()))) {
				tempMap.put(授信額度合計多幣別說明, l140m01a.getMultiAmt());
			}

			if (!Util.isEmpty(Util.trim(l140m01a.getMultiAssureAmt()))) {
				tempMap.put(擔保授信合計多幣別說明, l140m01a.getMultiAssureAmt());
			}

			mapName.put(LMSUtil.concat(temp, id, dupNo), tempMap);

		}
		JSONArray jsonArray = new JSONArray();
		String type = ",##0.##";

		DecimalFormat dollarFormat = new DecimalFormat(type);

		// 計算授信額度合計TITLE
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		// LGD合計=授信額度合計
		String LMS_LGD_LGDTOTAMT_E_LOANTOTAMT = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_LGDTOTAMT_E_LOANTOTAMT"));
		boolean showLgdTotAmt = lmsService.showLgdTotAmt(l120m01a,
				user.getUnitNo(), caseType);

		for (String name : l140m01as.keySet()) {
			String custInfo = mapName.get(name.substring(3, name.length()))
					.get("name");
			JSONObject json = new JSONObject();
			json.put("id", name.substring(3, name.length()));

			// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			JSONObject title = new JSONObject();

			// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
			String custInfoMemo = "";
			if (Util.equals(LMS_LGD_LGDTOTAMT_E_LOANTOTAMT, "Y")) {
				if (showLgdTotAmt) {
					// LGD合計=授信額度合計
					// L140M01a.saveCount_7=不含單獨另計授權額度
					custInfoMemo = prop.getProperty("L140M01a.saveCount_7")
							+ ":"
							+ "<br>"
							+ NumConverter
									.addComma(dollarFormat.format(l140m01as
											.get(name).get(單獨另計授權額度合計)));
				}
			}

			json.put("name", custInfo);
			json.put("nameMemo", custInfoMemo);

			// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
			String custNameTitleMemo = "";
			if (Util.equals(LMS_LGD_LGDTOTAMT_E_LOANTOTAMT, "Y")) {
				if (showLgdTotAmt) {
					// LGD合計=授信額度合計
					// L140M01a.saveCount_8=以下金額不含單獨另計授權額度
					custNameTitleMemo = "<br><div style='text-align: center;'><span class='text-red' >"
							+ prop.getProperty("L140M01a.saveCount_8")
							+ "</span></div>";
				}
			}

			title.put("借款人名稱", prop.getProperty("L140M01a.custName")
					+ custNameTitleMemo);
			json.put("curr", name.substring(0, 3));
			title.put("幣別", prop.getProperty("L782M01A.applyCurr"));

			JSONObject count = new JSONObject();
			JSONObject readOnly = new JSONObject();

			// count.put(
			// 單獨另計授權額度合計,
			// NumConverter.addComma(dollarFormat.format(l140m01as.get(
			// name).get(單獨另計授權額度合計))));
			// // L140M01a.standAloneAuthTotal=單獨另計授權額度合計
			// title.put(單獨另計授權額度合計,
			// prop.getProperty("L140M01a.standAloneAuthTotal") + "("
			// + prop.getProperty("other.money") + ")");
			// readOnly.put(單獨另計授權額度合計, "N");

			count.put(
					前准額度批覆合計,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(前准額度批覆合計))));
			title.put(前准額度批覆合計, prop.getProperty("L140M01a.LVTotAmt2") + "("
					+ prop.getProperty("other.money") + ")");

			count.put(
					前准額度批覆擔保合計,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(前准額度批覆擔保合計))));
			title.put(前准額度批覆擔保合計, prop.getProperty("L140M01a.LVAssTotAmt")
					+ "(" + prop.getProperty("other.money") + ")");

			count.put(
					減額額度合計,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(減額額度合計))));
			title.put(減額額度合計, prop.getProperty("L140M01a.decApplyTotAmt") + "("
					+ prop.getProperty("other.money") + ")");

			count.put(
					減額擔保額度合計,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(減額擔保額度合計))));
			title.put(
					減額擔保額度合計,
					prop.getProperty("L140M01a.decApplyTotAmt")
							+ prop.getProperty("L140M01a.count2") + "("
							+ prop.getProperty("other.money") + ")");

			count.put(
					授信額度合計,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(授信額度合計))));
			title.put(
					授信額度合計,
					prop.getProperty("L140M01a.count") + "("
							+ prop.getProperty("other.money") + ")");

			count.put(
					擔保授信合計,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(擔保授信合計))));
			title.put(
					擔保授信合計,
					prop.getProperty("L140M01a.count2") + "("
							+ prop.getProperty("other.money") + ")");

			count.put(
					衍生性商品原始合計,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(衍生性商品原始合計))));
			title.put(
					衍生性商品原始合計,
					prop.getProperty("L140M01a.loanTotLCurr")
							+ prop.getProperty("L140M01a.loanTotLCurr1") + "("
							+ prop.getProperty("other.money") + ")");

			count.put(
					衍生性商品相當合計,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(衍生性商品相當合計))));
			title.put(
					衍生性商品相當合計,
					prop.getProperty("L140M01a.loanTotLCurr")
							+ prop.getProperty("L140M01a.loanTotLCurr2") + "("
							+ prop.getProperty("other.money") + ")");

			JSONObject drc = new JSONObject();

			if (mapName.get(name.substring(3, name.length())).containsKey(
					衍生性授信額度合計多幣別說明)) {
				drc.put(衍生性授信額度合計多幣別說明,
						mapName.get(name.substring(3, name.length())).get(
								衍生性授信額度合計多幣別說明));
				title.put(衍生性授信額度合計多幣別說明,
						prop.getProperty("L140M01a.dervMultiAssureAmt"));
			} else {
				// 配合動態LGD TITLE，後面欄位只能固定顯示
				drc.put(衍生性授信額度合計多幣別說明, "readOnly");
				title.put(衍生性授信額度合計多幣別說明,
						prop.getProperty("L140M01a.dervMultiAssureAmt"));
			}

			if (mapName.get(name.substring(3, name.length())).containsKey(
					授信額度合計多幣別說明)) {
				drc.put(授信額度合計多幣別說明,
						mapName.get(name.substring(3, name.length())).get(
								授信額度合計多幣別說明));
				title.put(授信額度合計多幣別說明, prop.getProperty("L140M01a.multiAmt"));
			} else {
				// LMS1401S02Panel.js \editCount ->//無多幣別敘述開空欄位
				// 配合動態LGD TITLE，後面欄位只能固定顯示
				drc.put(授信額度合計多幣別說明, "readOnly");
				title.put(授信額度合計多幣別說明, prop.getProperty("L140M01a.multiAmt"));
			}
			if (mapName.get(name.substring(3, name.length())).containsKey(
					擔保授信合計多幣別說明)) {
				drc.put(擔保授信合計多幣別說明,
						mapName.get(name.substring(3, name.length())).get(
								擔保授信合計多幣別說明));
				title.put(擔保授信合計多幣別說明,
						prop.getProperty("L140M01a.multiAssureAmt"));
			} else {
				// LMS1401S02Panel.js \editCount ->//無多幣別敘述開空欄位
				// 配合動態LGD TITLE，後面欄位只能固定顯示
				drc.put(擔保授信合計多幣別說明, "readOnly");
				title.put(擔保授信合計多幣別說明,
						prop.getProperty("L140M01a.multiAssureAmt"));
			}

			json.put("count", count);
			json.put("drc", drc);
			json.put("title", title);
			json.put("readOnly", readOnly);

			jsonArray.add(json);
		}
		return jsonArray;
	}

	/**
	 * J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 * 
	 * @param caseMainId
	 * @param caseType
	 * @param countCurr
	 * @param showCurr
	 * @param saveRateFg
	 * @param l140m01as
	 * @return
	 * @throws Exception
	 */
	public JSONArray findL140m01editCount_2(String caseMainId, String caseType,
			String countCurr, Boolean showCurr, Boolean saveRateFg,
			Map<String, Map<String, BigDecimal>> l140m01as) throws Exception {

		List<L140M01A> l140m01aList = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId, caseType, null);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);

		// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		// boolean needShowGeneralLoanTotal =
		// lmsService.needShowGeneralLoanTotal(
		// l120m01a, l120m01a.getCaseBrId(), caseType);

		HashMap<String, HashMap<String, String>> mapName = new HashMap<String, HashMap<String, String>>();
		// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		HashMap<String, String> mapCustLgdTotNeed = new HashMap<String, String>();
		StringBuffer temp = new StringBuffer();
		// 把所有的借款人姓名放進map
		for (L140M01A l140m01a : l140m01aList) {
			String id = Util.trim(l140m01a.getCustId());
			String dupNo = Util.trim(l140m01a.getDupNo());
			String name = Util.trim(l140m01a.getCustName());
			HashMap<String, String> tempMap = new HashMap<String, String>();
			tempMap.put("name", LMSUtil.concat(temp, id, " ", dupNo, " ", name));

			mapName.put(LMSUtil.concat(temp, id, dupNo), tempMap);

			// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			boolean isCustNeedLgdTot = lmsLgdService
					.isCustNeedCountLgdTotal(l140m01a);
			mapCustLgdTotNeed.put(LMSUtil.concat(temp, id, dupNo),
					isCustNeedLgdTot ? "Y" : "N");

		}
		JSONArray jsonArray = new JSONArray();
		String type = ",##0.##";

		DecimalFormat dollarFormat = new DecimalFormat(type);

		// 計算授信額度合計TITLE
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		// l140m01a 或 itemType 擇一
		// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		boolean showLgdTotAmt = lmsService.showLgdTotAmt(l120m01a,
				user.getUnitNo(), caseType);
		int lmsLgdCount = 0;
		Map<String, String> lgdMap = null;
		if (showLgdTotAmt) {
			// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
			L140M01A l140m01aLgd = null;
			if (l140m01aList != null && !l140m01aList.isEmpty()) {
				l140m01aLgd = l140m01aList.get(0);
			}
			lgdMap = lmsService.getLgdTotAmtParam(l140m01aLgd, null);
			lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
					"lmsLgdCount", "0"));
		}

		if (!showLgdTotAmt) {
			return jsonArray;
		}

		for (String name : l140m01as.keySet()) {
			String custInfo = mapName.get(name.substring(3, name.length()))
					.get("name");
			JSONObject json = new JSONObject();
			json.put("id", name.substring(3, name.length()));

			// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			JSONObject title = new JSONObject();
			json.put("name", custInfo);
			title.put("借款人名稱", prop.getProperty("L140M01a.custName"));

			JSONObject count = new JSONObject();
			JSONObject readOnly = new JSONObject();

			String tmpTitleField = "";
			String tmpTitleName = "";

			tmpTitleField = "show_" + 授信額度合計;
			tmpTitleName = prop.getProperty("L140M01a.count") + "("
					+ prop.getProperty("other.money") + ")";
			count.put(
					tmpTitleField,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(授信額度合計))));
			title.put(tmpTitleField, tmpTitleName);
			readOnly.put(tmpTitleField, "Y");

			BigDecimal lgdTotal = BigDecimal.ZERO;
			for (int i = 1; i <= lmsLgdCount; i++) {
				lgdTotal = lgdTotal.add(l140m01as.get(name)
						.get(LGD合計 + "_" + i));
			}

			// count.put(
			// 單獨另計授權額度合計,
			// NumConverter.addComma(dollarFormat.format(l140m01as.get(
			// name).get(單獨另計授權額度合計))));
			// // L140M01a.standAloneAuthTotal=單獨另計授權額度合計
			// title.put(單獨另計授權額度合計,
			// prop.getProperty("L140M01a.standAloneAuthTotal") + "("
			// + prop.getProperty("other.money") + ")");
			// readOnly.put(單獨另計授權額度合計, "N");

			tmpTitleField = "show_" + LGD合計;
			tmpTitleName = prop.getProperty("L140M01a.saveCount_6"); // "授信授權額度"
			count.put(tmpTitleField,
					NumConverter.addComma(dollarFormat.format(lgdTotal)));
			title.put(tmpTitleField, tmpTitleName);
			readOnly.put(tmpTitleField, "Y");

			for (int i = 1; i <= lmsLgdCount; i++) {

				// J-112-0037_05097_B1005 Web
				// eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計

				String tmpId = Util.trim(name.substring(3, name.length()));

				if (mapCustLgdTotNeed.containsKey(tmpId)) {
					if (Util.equals("Y", mapCustLgdTotNeed.get(tmpId))) {
						count.put(LGD合計 + "_" + i, NumConverter
								.addComma(dollarFormat.format(l140m01as.get(
										name).get(LGD合計 + "_" + i))));
					} else {
						// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
						// count.put(LGD合計 + "_" + i, "readOnly");
						count.put(LGD合計 + "_" + i, "");
						readOnly.put(LGD合計 + "_" + i, "Y");
					}
				} else {
					count.put(LGD合計 + "_" + i, NumConverter
							.addComma(dollarFormat.format(l140m01as.get(name)
									.get(LGD合計 + "_" + i))));
				}

				String label_lgdTotAmt = MapUtils.getString(lgdMap, "label_"
						+ i, "");
				String label_lgdTotAmt_U_1 = MapUtils.getString(lgdMap,
						"label_1_" + i, "");
				title.put(LGD合計 + "_" + i,
						label_lgdTotAmt + "(" + prop.getProperty("other.money")
								+ ")" + "<br>" + label_lgdTotAmt_U_1);
				// readOnly.put(LGD合計 + "_" + i, "N");
			}

			json.put("count", count);
			json.put("readOnly", readOnly);
			json.put("title", title);

			jsonArray.add(json);
		}
		return jsonArray;
	}

	/**
	 * J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 * 
	 * @param caseMainId
	 * @param caseType
	 * @param countCurr
	 * @param showCurr
	 * @param saveRateFg
	 * @param l140m01as
	 * @return
	 * @throws Exception
	 */
	public JSONArray findL140m01editCount_3(String caseMainId, String caseType,
			String countCurr, Boolean showCurr, Boolean saveRateFg,
			Map<String, Map<String, BigDecimal>> l140m01as) throws Exception {

		List<L140M01A> l140m01aList = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId, caseType, null);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);

		// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		boolean needShowGeneralLoanTotal = lmsService.needShowGeneralLoanTotal(
				l120m01a, l120m01a.getCaseBrId(), caseType);

		HashMap<String, HashMap<String, String>> mapName = new HashMap<String, HashMap<String, String>>();
		// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		HashMap<String, String> mapCustLgdTotNeed = new HashMap<String, String>();
		StringBuffer temp = new StringBuffer();
		// 把所有的借款人姓名放進map
		for (L140M01A l140m01a : l140m01aList) {
			String id = Util.trim(l140m01a.getCustId());
			String dupNo = Util.trim(l140m01a.getDupNo());
			String name = Util.trim(l140m01a.getCustName());
			HashMap<String, String> tempMap = new HashMap<String, String>();
			tempMap.put("name", LMSUtil.concat(temp, id, " ", dupNo, " ", name));

			mapName.put(LMSUtil.concat(temp, id, dupNo), tempMap);

			// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			boolean isCustNeedLgdTot = lmsLgdService
					.isCustNeedCountLgdTotal(l140m01a);
			mapCustLgdTotNeed.put(LMSUtil.concat(temp, id, dupNo),
					isCustNeedLgdTot ? "Y" : "N");

		}
		JSONArray jsonArray = new JSONArray();
		String type = ",##0.##";

		DecimalFormat dollarFormat = new DecimalFormat(type);

		// 計算授信額度合計TITLE
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		// l140m01a 或 itemType 擇一
		// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		boolean showLgdTotAmt = lmsService.showLgdTotAmt(l120m01a,
				user.getUnitNo(), caseType);
		int lmsLgdCount = 0;
		Map<String, String> lgdMap = null;
		if (showLgdTotAmt) {
			// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
			L140M01A l140m01aLgd = null;
			if (l140m01aList != null && !l140m01aList.isEmpty()) {
				l140m01aLgd = l140m01aList.get(0);
			}
			lgdMap = lmsService.getLgdTotAmtParam(l140m01aLgd, null);
			lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
					"lmsLgdCount", "0"));
		}

		if (!needShowGeneralLoanTotal) {
			return jsonArray;
		}

		for (String name : l140m01as.keySet()) {
			String custInfo = mapName.get(name.substring(3, name.length()))
					.get("name");
			JSONObject json = new JSONObject();
			json.put("id", name.substring(3, name.length()));

			// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			JSONObject title = new JSONObject();
			json.put("name", custInfo);
			title.put("借款人名稱", prop.getProperty("L140M01a.custName"));

			JSONObject count = new JSONObject();
			JSONObject readOnly = new JSONObject();

			String tmpTitleField = "";
			String tmpTitleName = "";

			BigDecimal lgdTotal = BigDecimal.ZERO;
			for (int i = 1; i <= lmsLgdCount; i++) {
				lgdTotal = lgdTotal.add(l140m01as.get(name)
						.get(LGD合計 + "_" + i));
			}

			tmpTitleField = "show_" + LGD合計;
			tmpTitleName = prop.getProperty("L140M01a.saveCount_6"); // "授信授權額度"
			count.put(tmpTitleField,
					NumConverter.addComma(dollarFormat.format(lgdTotal)));
			title.put(tmpTitleField, tmpTitleName);
			readOnly.put(tmpTitleField, "Y");

			count.put(
					瑕疵押匯額度合計,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(瑕疵押匯額度合計))));
			title.put(瑕疵押匯額度合計, prop.getProperty("L140M01a.flawAmtTotal") + "("
					+ prop.getProperty("other.money") + ")");
			readOnly.put(瑕疵押匯額度合計, "N");

			count.put(
					總授信額度合計,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(總授信額度合計))));
			title.put(總授信額度合計, prop.getProperty("L140M01a.generalLoanTotAmt")
					+ "(" + prop.getProperty("other.money") + ")" + "<BR>"
					+ prop.getProperty("L140M01a.generalLoanTotAmt_memo1"));
			readOnly.put(總授信額度合計, "Y");

			json.put("count", count);
			json.put("readOnly", readOnly);
			json.put("title", title);

			jsonArray.add(json);
		}
		return jsonArray;
	}

	/**
	 * J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 * 
	 * @param caseMainId
	 * @param caseType
	 * @param countCurr
	 * @param showCurr
	 * @param saveRateFg
	 * @param l140m01as
	 * @return
	 * @throws Exception
	 */
	public JSONArray findL140m01editCount_4(String caseMainId, String caseType,
			String countCurr, Boolean showCurr, Boolean saveRateFg,
			Map<String, Map<String, BigDecimal>> l140m01as) throws Exception {

		List<L140M01A> l140m01aList = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId, caseType, null);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);

		// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		boolean needShowGeneralLoanTotal = lmsService.needShowGeneralLoanTotal(
				l120m01a, l120m01a.getCaseBrId(), caseType);

		HashMap<String, HashMap<String, String>> mapName = new HashMap<String, HashMap<String, String>>();
		// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		HashMap<String, String> mapCustLgdTotNeed = new HashMap<String, String>();
		StringBuffer temp = new StringBuffer();
		// 把所有的借款人姓名放進map
		for (L140M01A l140m01a : l140m01aList) {
			String id = Util.trim(l140m01a.getCustId());
			String dupNo = Util.trim(l140m01a.getDupNo());
			String name = Util.trim(l140m01a.getCustName());
			HashMap<String, String> tempMap = new HashMap<String, String>();
			tempMap.put("name", LMSUtil.concat(temp, id, " ", dupNo, " ", name));

			mapName.put(LMSUtil.concat(temp, id, dupNo), tempMap);

			// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			boolean isCustNeedLgdTot = lmsLgdService
					.isCustNeedCountLgdTotal(l140m01a);

			// J-111-0461_05097_B1006-2 授信額度合計新增單獨另計授權及各組LGD合計檢核
			// 合併改成一筆所有借款人合計，所以改到baseCount後才合計**********************
			mapCustLgdTotNeed.put(LMSUtil.concat(temp, id, dupNo),
					isCustNeedLgdTot ? "Y" : "Y"); // 合併一律都要顯示，免得第一筆是屬於不用的LGD的造成無法顯示全案合計編輯欄位

		}
		JSONArray jsonArray = new JSONArray();
		String type = ",##0.##";

		DecimalFormat dollarFormat = new DecimalFormat(type);

		// 計算授信額度合計TITLE
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		// l140m01a 或 itemType 擇一
		// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		boolean showLgdTotAmt = lmsService.showLgdTotAmt(l120m01a,
				user.getUnitNo(), caseType);
		int lmsLgdCount = 0;
		Map<String, String> lgdMap = null;
		if (showLgdTotAmt) {
			// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
			L140M01A l140m01aLgd = null;
			if (l140m01aList != null && !l140m01aList.isEmpty()) {
				l140m01aLgd = l140m01aList.get(0);
			}
			lgdMap = lmsService.getLgdTotAmtParam(l140m01aLgd, null);
			lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
					"lmsLgdCount", "0"));
		}

		if (!showLgdTotAmt || !needShowGeneralLoanTotal) {
			return jsonArray;
		}

		String allCase_lgdTotMgCurr = "";
		for (String name : l140m01as.keySet()) {
			for (String n : l140m01as.get(name).keySet()) {
				if (StringUtils.contains(n, LGD合計_全案_幣別)) {
					String curr = n.split("_")[1];
					allCase_lgdTotMgCurr = curr;
					break;
				}
			}
			break;
		}

		for (String name : l140m01as.keySet()) {
			String custInfo = mapName.get(name.substring(3, name.length()))
					.get("name");
			JSONObject json = new JSONObject();
			json.put("id", name.substring(3, name.length()));

			// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			JSONObject title = new JSONObject();
			// json.put("name", "全 案");
			// title.put("借款人名稱", "");

			json.put(LGD合計_全案_幣別, allCase_lgdTotMgCurr);
			title.put("幣別", prop.getProperty("L782M01A.applyCurr"));

			JSONObject count = new JSONObject();
			JSONObject readOnly = new JSONObject();

			String tmpTitleField = "";
			String tmpTitleName = "";

			BigDecimal lgdMgTotal = BigDecimal.ZERO;
			for (int i = 1; i <= lmsLgdCount; i++) {
				lgdMgTotal = lgdMgTotal.add(l140m01as.get(name).get(
						LGD合計_全案 + "_" + i));
			}

			// tmpTitleField = "tmp_" + LGD合計_全案;
			// tmpTitleName = prop.getProperty("L140M01a.saveCount_6"); //
			// "授信授權額度"
			// count.put(tmpTitleField,
			// NumConverter.addComma(dollarFormat.format(lgdMgTotal)));
			// title.put(tmpTitleField, tmpTitleName);
			// readOnly.put(tmpTitleField, "X"); // 只能編輯，但要顯示異動前

			for (int i = 1; i <= lmsLgdCount; i++) {

				// J-112-0037_05097_B1005 Web
				// eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計

				String tmpId = Util.trim(name.substring(3, name.length()));

				if (mapCustLgdTotNeed.containsKey(tmpId)) {
					if (Util.equals("Y", mapCustLgdTotNeed.get(tmpId))) {
						count.put(LGD合計_全案 + "_" + i, NumConverter
								.addComma(dollarFormat.format(l140m01as.get(
										name).get(LGD合計_全案 + "_" + i))));
					} else {
						// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
						// count.put(LGD合計_全案 + "_" + i, "readOnly");
						count.put(LGD合計_全案 + "_" + i, "");
						readOnly.put(LGD合計_全案 + "_" + i, "Y");
					}
				} else {
					count.put(LGD合計_全案 + "_" + i, NumConverter
							.addComma(dollarFormat.format(l140m01as.get(name)
									.get(LGD合計_全案 + "_" + i))));
				}

				String label_lgdTotAmt = MapUtils.getString(lgdMap, "label_"
						+ i, "");
				String label_lgdTotAmt_U_1 = MapUtils.getString(lgdMap,
						"label_1_" + i, "");
				title.put(LGD合計_全案 + "_" + i,
						label_lgdTotAmt + "(" + prop.getProperty("other.money")
								+ ")" + "<br>" + label_lgdTotAmt_U_1);
				// readOnly.put(LGD合計_全案 + "_" + i, "N");
			}

			count.put(
					瑕疵押匯額度合計_全案,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(瑕疵押匯額度合計_全案))));
			title.put(瑕疵押匯額度合計_全案, prop.getProperty("L140M01a.flawAmtTotal")
					+ "(" + prop.getProperty("other.money") + ")");
			readOnly.put(瑕疵押匯額度合計_全案, "N");

			count.put(
					總授信額度合計_全案,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(總授信額度合計_全案))));
			title.put(
					總授信額度合計_全案,
					prop.getProperty("L140M01a.generalLoanTotAmt")
							+ "("
							+ prop.getProperty("other.money")
							+ ")"
							+ "<BR>"
							+ prop.getProperty("L140M01a.generalLoanTotAmt_memo1"));
			readOnly.put(總授信額度合計_全案, "X"); // 要顯示show

			json.put("count", count);
			json.put("readOnly", readOnly);
			json.put("title", title);

			jsonArray.add(json);
			break;
		}
		return jsonArray;
	}

	/**
	 * J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 * 
	 * @param caseMainId
	 * @param caseType
	 * @param countCurr
	 * @param showCurr
	 * @param saveRateFg
	 * @param l140m01as
	 * @return
	 * @throws Exception
	 */
	public JSONArray findL140m01editCount_5(String caseMainId, String caseType,
			String countCurr, Boolean showCurr, Boolean saveRateFg,
			Map<String, Map<String, BigDecimal>> l140m01as) throws Exception {

		List<L140M01A> l140m01aList = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId, caseType, null);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);

		// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		// boolean needShowGeneralLoanTotal =
		// lmsService.needShowGeneralLoanTotal(
		// l120m01a, l120m01a.getCaseBrId(), caseType);

		boolean needShowLoanCountRcTotal = lmsService.needShowLoanCountRcTotal(
				l120m01a, l120m01a.getCaseBrId(), caseType);

		HashMap<String, HashMap<String, String>> mapName = new HashMap<String, HashMap<String, String>>();
		// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		HashMap<String, String> mapCustLgdTotNeed = new HashMap<String, String>();
		StringBuffer temp = new StringBuffer();
		// 把所有的借款人姓名放進map
		for (L140M01A l140m01a : l140m01aList) {
			String id = Util.trim(l140m01a.getCustId());
			String dupNo = Util.trim(l140m01a.getDupNo());
			String name = Util.trim(l140m01a.getCustName());
			HashMap<String, String> tempMap = new HashMap<String, String>();
			tempMap.put("name", LMSUtil.concat(temp, id, " ", dupNo, " ", name));

			mapName.put(LMSUtil.concat(temp, id, dupNo), tempMap);

			// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			boolean isCustNeedLgdTot = lmsLgdService
					.isCustNeedCountLgdTotal(l140m01a);
			mapCustLgdTotNeed.put(LMSUtil.concat(temp, id, dupNo),
					isCustNeedLgdTot ? "Y" : "N");

		}
		JSONArray jsonArray = new JSONArray();
		String type = ",##0.##";

		DecimalFormat dollarFormat = new DecimalFormat(type);

		// 計算授信額度合計TITLE
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		// l140m01a 或 itemType 擇一
		// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		boolean showLgdTotAmt = lmsService.showLgdTotAmt(l120m01a,
				user.getUnitNo(), caseType);
		int lmsLgdCount = 0;
		Map<String, String> lgdMap = null;
		if (showLgdTotAmt) {
			// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
			L140M01A l140m01aLgd = null;
			if (l140m01aList != null && !l140m01aList.isEmpty()) {
				l140m01aLgd = l140m01aList.get(0);
			}
			lgdMap = lmsService.getLgdTotAmtParam(l140m01aLgd, null);
			lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
					"lmsLgdCount", "0"));
		}

		if (!showLgdTotAmt) {
			return jsonArray;
		}

		if (!needShowLoanCountRcTotal) {
			return jsonArray;
		}

		for (String name : l140m01as.keySet()) {
			String custInfo = mapName.get(name.substring(3, name.length()))
					.get("name");
			JSONObject json = new JSONObject();
			json.put("id", name.substring(3, name.length()));

			// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			JSONObject title = new JSONObject();
			json.put("name", custInfo);
			title.put("借款人名稱", prop.getProperty("L140M01a.custName"));

			JSONObject count = new JSONObject();
			JSONObject readOnly = new JSONObject();

			String tmpTitleField = "";
			String tmpTitleName = "";

			BigDecimal lgdTotal = BigDecimal.ZERO;
			for (int i = 1; i <= lmsLgdCount; i++) {
				lgdTotal = lgdTotal.add(l140m01as.get(name).get(
						LGD合計_合併關係 + "_" + i));
			}

			// count.put(
			// 單獨另計授權額度合計,
			// NumConverter.addComma(dollarFormat.format(l140m01as.get(
			// name).get(單獨另計授權額度合計))));
			// // L140M01a.standAloneAuthTotal=單獨另計授權額度合計
			// title.put(單獨另計授權額度合計,
			// prop.getProperty("L140M01a.standAloneAuthTotal") + "("
			// + prop.getProperty("other.money") + ")");
			// readOnly.put(單獨另計授權額度合計, "N");

			tmpTitleField = "show_" + LGD合計_合併關係;
			tmpTitleName = prop.getProperty("L140M01a.saveCount_6"); // "授信授權額度"
			count.put(tmpTitleField,
					NumConverter.addComma(dollarFormat.format(lgdTotal)));
			title.put(tmpTitleField, tmpTitleName);
			readOnly.put(tmpTitleField, "Y");

			for (int i = 1; i <= lmsLgdCount; i++) {

				// J-112-0037_05097_B1005 Web
				// eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計

				String tmpId = Util.trim(name.substring(3, name.length()));

				if (mapCustLgdTotNeed.containsKey(tmpId)) {
					if (Util.equals("Y", mapCustLgdTotNeed.get(tmpId))) {
						count.put(LGD合計_合併關係 + "_" + i, NumConverter
								.addComma(dollarFormat.format(l140m01as.get(
										name).get(LGD合計_合併關係 + "_" + i))));
					} else {
						// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
						// count.put(LGD合計 + "_" + i, "readOnly");
						count.put(LGD合計_合併關係 + "_" + i, "");
						readOnly.put(LGD合計_合併關係 + "_" + i, "Y");
					}
				} else {
					count.put(LGD合計_合併關係 + "_" + i, NumConverter
							.addComma(dollarFormat.format(l140m01as.get(name)
									.get(LGD合計_合併關係 + "_" + i))));
				}

				String label_lgdTotAmt = MapUtils.getString(lgdMap, "label_"
						+ i, "");
				String label_lgdTotAmt_U_1 = MapUtils.getString(lgdMap,
						"label_1_" + i, "");
				title.put(LGD合計_合併關係 + "_" + i,
						label_lgdTotAmt + "(" + prop.getProperty("other.money")
								+ ")" + "<br>" + label_lgdTotAmt_U_1);
				// readOnly.put(LGD合計 + "_" + i, "N");
			}

			json.put("count", count);
			json.put("readOnly", readOnly);
			json.put("title", title);

			jsonArray.add(json);
		}
		return jsonArray;
	}

	/**
	 * J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 * 
	 * @param caseMainId
	 * @param caseType
	 * @param countCurr
	 * @param showCurr
	 * @param saveRateFg
	 * @param l140m01as
	 * @return
	 * @throws Exception
	 */
	public JSONArray findL140m01editCount_6(String caseMainId, String caseType,
			String countCurr, Boolean showCurr, Boolean saveRateFg,
			Map<String, Map<String, BigDecimal>> l140m01as) throws Exception {

		List<L140M01A> l140m01aList = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId, caseType, null);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);

		// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		boolean needShowGeneralLoanTotal = lmsService.needShowGeneralLoanTotal(
				l120m01a, l120m01a.getCaseBrId(), caseType);

		boolean needShowLoanCountRcTotal = lmsService.needShowLoanCountRcTotal(
				l120m01a, l120m01a.getCaseBrId(), caseType);

		HashMap<String, HashMap<String, String>> mapName = new HashMap<String, HashMap<String, String>>();
		// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		HashMap<String, String> mapCustLgdTotNeed = new HashMap<String, String>();
		StringBuffer temp = new StringBuffer();
		// 把所有的借款人姓名放進map
		for (L140M01A l140m01a : l140m01aList) {
			String id = Util.trim(l140m01a.getCustId());
			String dupNo = Util.trim(l140m01a.getDupNo());
			String name = Util.trim(l140m01a.getCustName());
			HashMap<String, String> tempMap = new HashMap<String, String>();
			tempMap.put("name", LMSUtil.concat(temp, id, " ", dupNo, " ", name));

			mapName.put(LMSUtil.concat(temp, id, dupNo), tempMap);

			// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			boolean isCustNeedLgdTot = lmsLgdService
					.isCustNeedCountLgdTotal(l140m01a);
			mapCustLgdTotNeed.put(LMSUtil.concat(temp, id, dupNo),
					isCustNeedLgdTot ? "Y" : "N");

		}
		JSONArray jsonArray = new JSONArray();
		String type = ",##0.##";

		DecimalFormat dollarFormat = new DecimalFormat(type);

		// 計算授信額度合計TITLE
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		// l140m01a 或 itemType 擇一
		// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		boolean showLgdTotAmt = lmsService.showLgdTotAmt(l120m01a,
				user.getUnitNo(), caseType);
		int lmsLgdCount = 0;
		Map<String, String> lgdMap = null;
		if (showLgdTotAmt) {
			// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
			L140M01A l140m01aLgd = null;
			if (l140m01aList != null && !l140m01aList.isEmpty()) {
				l140m01aLgd = l140m01aList.get(0);
			}
			lgdMap = lmsService.getLgdTotAmtParam(l140m01aLgd, null);
			lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
					"lmsLgdCount", "0"));
		}

		if (!needShowGeneralLoanTotal) {
			return jsonArray;
		}

		if (!needShowLoanCountRcTotal) {
			return jsonArray;
		}

		for (String name : l140m01as.keySet()) {
			String custInfo = mapName.get(name.substring(3, name.length()))
					.get("name");
			JSONObject json = new JSONObject();
			json.put("id", name.substring(3, name.length()));

			// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			JSONObject title = new JSONObject();
			json.put("name", custInfo);
			title.put("借款人名稱", prop.getProperty("L140M01a.custName"));

			JSONObject count = new JSONObject();
			JSONObject readOnly = new JSONObject();

			String tmpTitleField = "";
			String tmpTitleName = "";

			BigDecimal lgdTotal = BigDecimal.ZERO;
			for (int i = 1; i <= lmsLgdCount; i++) {
				lgdTotal = lgdTotal.add(l140m01as.get(name).get(
						LGD合計_合併關係 + "_" + i));
			}

			tmpTitleField = "show_" + LGD合計_合併關係;
			tmpTitleName = prop.getProperty("L140M01a.saveCount_6"); // "授信授權額度"
			count.put(tmpTitleField,
					NumConverter.addComma(dollarFormat.format(lgdTotal)));
			title.put(tmpTitleField, tmpTitleName);
			readOnly.put(tmpTitleField, "Y");

			count.put(
					瑕疵押匯額度合計_合併關係,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(瑕疵押匯額度合計_合併關係))));
			title.put(瑕疵押匯額度合計_合併關係, prop.getProperty("L140M01a.flawAmtTotal")
					+ "(" + prop.getProperty("other.money") + ")");
			readOnly.put(瑕疵押匯額度合計_合併關係, "N");

			count.put(
					總授信額度合計_合併關係,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(總授信額度合計_合併關係))));
			title.put(
					總授信額度合計_合併關係,
					prop.getProperty("L140M01a.generalLoanTotAmt")
							+ "("
							+ prop.getProperty("other.money")
							+ ")"
							+ "<BR>"
							+ prop.getProperty("L140M01a.generalLoanTotAmt_memo1"));
			readOnly.put(總授信額度合計_合併關係, "Y");

			json.put("count", count);
			json.put("readOnly", readOnly);
			json.put("title", title);

			jsonArray.add(json);
		}
		return jsonArray;
	}

	public JSONArray findL140m01editCount_4_XXXXXXXXXXXX(String caseMainId,
			String caseType, String countCurr, Boolean showCurr,
			Boolean saveRateFg, Map<String, Map<String, BigDecimal>> l140m01as)
			throws Exception {

		List<L140M01A> l140m01aList = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId, caseType, null);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);

		// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		boolean needShowGeneralLoanTotal = lmsService.needShowGeneralLoanTotal(
				l120m01a, l120m01a.getCaseBrId(), caseType);

		HashMap<String, HashMap<String, String>> mapName = new HashMap<String, HashMap<String, String>>();
		// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		HashMap<String, String> mapCustLgdTotNeed = new HashMap<String, String>();
		StringBuffer temp = new StringBuffer();

		JSONArray jsonArray = new JSONArray();
		String type = ",##0.##";

		DecimalFormat dollarFormat = new DecimalFormat(type);

		// 計算授信額度合計TITLE
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		// l140m01a 或 itemType 擇一
		// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		boolean showLgdTotAmt = lmsService.showLgdTotAmt(l120m01a,
				user.getUnitNo(), caseType);
		int lmsLgdCount = 0;
		Map<String, String> lgdMap = null;
		if (showLgdTotAmt) {
			// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
			L140M01A l140m01aLgd = null;
			if (l140m01aList != null && !l140m01aList.isEmpty()) {
				l140m01aLgd = l140m01aList.get(0);
			}
			lgdMap = lmsService.getLgdTotAmtParam(l140m01aLgd, null);
			lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
					"lmsLgdCount", "0"));
		}

		if (!showLgdTotAmt || !needShowGeneralLoanTotal) {
			return jsonArray;
		}

		// 合計
		BigDecimal allCase_lgdTotMgAmt = BigDecimal.ZERO;
		BigDecimal allCase_flawAmtMgTotal = BigDecimal.ZERO;
		BigDecimal allCase_generalLoanMgTotAmt = BigDecimal.ZERO;
		JSONObject allCase_count = new JSONObject();
		for (int i = 1; i <= lmsLgdCount; i++) {
			allCase_count.put(LGD合計_全案 + "_" + i, 0);
		}

		for (String name : l140m01as.keySet()) {
			if (showLgdTotAmt) {
				for (int i = 1; i <= lmsLgdCount; i++) {
					allCase_lgdTotMgAmt = allCase_lgdTotMgAmt.add(l140m01as
							.get(name).get(LGD合計_全案 + "_" + i));

					BigDecimal tmpLgdCount = Util.parseBigDecimal(allCase_count
							.optString(LGD合計_全案 + "_" + i, "0"));
					tmpLgdCount = tmpLgdCount.add(l140m01as.get(name).get(
							LGD合計_全案 + "_" + i));

					allCase_count.put(LGD合計_全案 + "_" + i, tmpLgdCount);

				}
			}

			if (needShowGeneralLoanTotal) {
				allCase_flawAmtMgTotal = allCase_flawAmtMgTotal.add(l140m01as
						.get(name).get(瑕疵押匯額度合計_全案));
				allCase_generalLoanMgTotAmt = allCase_generalLoanMgTotAmt
						.add(l140m01as.get(name).get(總授信額度合計_全案));
			}

		}

		JSONObject json = new JSONObject();
		json.put("id", "");

		// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		JSONObject title = new JSONObject();
		json.put("name", "全案");
		title.put("借款人名稱", "");

		JSONObject count = new JSONObject();
		JSONObject readOnly = new JSONObject();

		String tmpTitleField = "";
		String tmpTitleName = "";

		tmpTitleField = "tmp_" + LGD合計_全案;
		tmpTitleName = prop.getProperty("L140M01a.saveCount_6"); // "授信授權額度"
		count.put(tmpTitleField,
				NumConverter.addComma(dollarFormat.format(allCase_lgdTotMgAmt)));
		title.put(tmpTitleField, tmpTitleName);
		readOnly.put(tmpTitleField, "X"); // 只能編輯，但要顯示異動前

		for (int i = 1; i <= lmsLgdCount; i++) {

			// J-112-0037_05097_B1005 Web
			// eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計

			BigDecimal tmpLgdCount = Util.parseBigDecimal(allCase_count
					.optString(LGD合計_全案 + "_" + i, "0"));
			count.put(LGD合計_全案 + "_" + i,
					NumConverter.addComma(dollarFormat.format(tmpLgdCount)));

			String label_lgdTotAmt = MapUtils.getString(lgdMap, "label_" + i,
					"");
			String label_lgdTotAmt_U_1 = MapUtils.getString(lgdMap, "label_1_"
					+ i, "");
			title.put(LGD合計_全案 + "_" + i,
					label_lgdTotAmt + "(" + prop.getProperty("other.money")
							+ ")" + "<br>" + label_lgdTotAmt_U_1);
			readOnly.put(LGD合計_全案 + "_" + i, "N");
		}

		count.put(瑕疵押匯額度合計_全案, NumConverter.addComma(dollarFormat
				.format(allCase_flawAmtMgTotal)));
		title.put(瑕疵押匯額度合計_全案, prop.getProperty("L140M01a.flawAmtTotal") + "("
				+ prop.getProperty("other.money") + ")");
		readOnly.put(瑕疵押匯額度合計_全案, "N");

		count.put(總授信額度合計_全案, NumConverter.addComma(dollarFormat
				.format(allCase_generalLoanMgTotAmt)));
		title.put(
				總授信額度合計_全案,
				prop.getProperty("L140M01a.generalLoanTotAmt") + "("
						+ prop.getProperty("other.money") + ")" + "<BR>"
						+ prop.getProperty("L140M01a.generalLoanTotAmt_memo1"));
		readOnly.put(總授信額度合計_全案, "X"); // 要顯示show

		json.put("count", count);
		json.put("readOnly", readOnly);
		json.put("title", title);

		jsonArray.add(json);

		return jsonArray;
	}

	private JSONArray findL140m01editCount_4_BK1(String caseMainId,
			String caseType, String countCurr, Boolean showCurr,
			Boolean saveRateFg, Map<String, Map<String, BigDecimal>> l140m01as)
			throws Exception {

		List<L140M01A> l140m01aList = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId, caseType, null);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);

		// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		boolean needShowGeneralLoanTotal = lmsService.needShowGeneralLoanTotal(
				l120m01a, l120m01a.getCaseBrId(), caseType);

		HashMap<String, HashMap<String, String>> mapName = new HashMap<String, HashMap<String, String>>();
		// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		HashMap<String, String> mapCustLgdTotNeed = new HashMap<String, String>();
		StringBuffer temp = new StringBuffer();
		// 把所有的借款人姓名放進map
		for (L140M01A l140m01a : l140m01aList) {
			String id = Util.trim(l140m01a.getCustId());
			String dupNo = Util.trim(l140m01a.getDupNo());
			String name = Util.trim(l140m01a.getCustName());
			HashMap<String, String> tempMap = new HashMap<String, String>();
			tempMap.put("name", LMSUtil.concat(temp, id, " ", dupNo, " ", name));

			mapName.put(LMSUtil.concat(temp, id, dupNo), tempMap);

			// J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			boolean isCustNeedLgdTot = lmsLgdService
					.isCustNeedCountLgdTotal(l140m01a);
			mapCustLgdTotNeed.put(LMSUtil.concat(temp, id, dupNo),
					isCustNeedLgdTot ? "Y" : "N");

		}
		JSONArray jsonArray = new JSONArray();
		String type = ",##0.##";

		DecimalFormat dollarFormat = new DecimalFormat(type);

		// 計算授信額度合計TITLE
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		// l140m01a 或 itemType 擇一
		// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		boolean showLgdTotAmt = lmsService.showLgdTotAmt(l120m01a,
				user.getUnitNo(), caseType);
		int lmsLgdCount = 0;
		Map<String, String> lgdMap = null;
		if (showLgdTotAmt) {
			// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
			L140M01A l140m01aLgd = null;
			if (l140m01aList != null && !l140m01aList.isEmpty()) {
				l140m01aLgd = l140m01aList.get(0);
			}
			lgdMap = lmsService.getLgdTotAmtParam(l140m01aLgd, null);
			lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
					"lmsLgdCount", "0"));
		}

		if (!showLgdTotAmt || !needShowGeneralLoanTotal) {
			return jsonArray;
		}

		for (String name : l140m01as.keySet()) {
			String custInfo = mapName.get(name.substring(3, name.length()))
					.get("name");
			JSONObject json = new JSONObject();
			json.put("id", name.substring(3, name.length()));

			// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
			JSONObject title = new JSONObject();
			json.put("name", custInfo);
			title.put("借款人名稱", prop.getProperty("L140M01a.custName"));

			JSONObject count = new JSONObject();
			JSONObject readOnly = new JSONObject();

			String tmpTitleField = "";
			String tmpTitleName = "";

			BigDecimal lgdTotal = BigDecimal.ZERO;
			for (int i = 1; i <= lmsLgdCount; i++) {
				lgdTotal = lgdTotal.add(l140m01as.get(name)
						.get(LGD合計 + "_" + i));
			}

			tmpTitleField = "tmp_" + LGD合計_全案;
			tmpTitleName = prop.getProperty("L140M01a.saveCount_6"); // "授信授權額度"
			count.put(tmpTitleField,
					NumConverter.addComma(dollarFormat.format(lgdTotal)));
			title.put(tmpTitleField, tmpTitleName);
			readOnly.put(tmpTitleField, "X"); // 只能編輯，但要顯示異動前

			for (int i = 1; i <= lmsLgdCount; i++) {

				// J-112-0037_05097_B1005 Web
				// eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計

				String tmpId = Util.trim(name.substring(3, name.length()));

				if (mapCustLgdTotNeed.containsKey(tmpId)) {
					if (Util.equals("Y", mapCustLgdTotNeed.get(tmpId))) {
						count.put(LGD合計_全案 + "_" + i, NumConverter
								.addComma(dollarFormat.format(l140m01as.get(
										name).get(LGD合計_全案 + "_" + i))));
					} else {
						// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
						// count.put(LGD合計_全案 + "_" + i, "readOnly");
						count.put(LGD合計_全案 + "_" + i, "");
						readOnly.put(LGD合計_全案 + "_" + i, "Y");
					}
				} else {
					count.put(LGD合計_全案 + "_" + i, NumConverter
							.addComma(dollarFormat.format(l140m01as.get(name)
									.get(LGD合計_全案 + "_" + i))));
				}

				String label_lgdTotAmt = MapUtils.getString(lgdMap, "label_"
						+ i, "");
				String label_lgdTotAmt_U_1 = MapUtils.getString(lgdMap,
						"label_1_" + i, "");
				title.put(LGD合計_全案 + "_" + i,
						label_lgdTotAmt + "(" + prop.getProperty("other.money")
								+ ")" + "<br>" + label_lgdTotAmt_U_1);
				// readOnly.put(LGD合計_全案 + "_" + i, "N");
			}

			count.put(
					瑕疵押匯額度合計_全案,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(瑕疵押匯額度合計_全案))));
			title.put(瑕疵押匯額度合計_全案, prop.getProperty("L140M01a.flawAmtTotal")
					+ "(" + prop.getProperty("other.money") + ")");
			readOnly.put(瑕疵押匯額度合計_全案, "N");

			count.put(
					總授信額度合計_全案,
					NumConverter.addComma(dollarFormat.format(l140m01as.get(
							name).get(總授信額度合計_全案))));
			title.put(
					總授信額度合計_全案,
					prop.getProperty("L140M01a.generalLoanTotAmt")
							+ "("
							+ prop.getProperty("other.money")
							+ ")"
							+ "<BR>"
							+ prop.getProperty("L140M01a.generalLoanTotAmt_memo1"));
			readOnly.put(總授信額度合計_全案, "X"); // 要顯示show

			json.put("count", count);
			json.put("readOnly", readOnly);
			json.put("title", title);

			jsonArray.add(json);
		}
		return jsonArray;
	}

	// // J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	// @Override
	// public L140M01I findL140m01iByUniqueKey(String mainId, String type,
	// String rId, String rDupNo) {
	// return l140m01iDao.findByUniqueKeyWithRType(mainId, type, rId, rDupNo,
	// UtilConstants.lngeFlag.連帶保證人);
	// }

	@Override
	public boolean deleteListL140m01i(String[] oids, String mainId) {
		boolean flag = false;
		List<L140M01I> l140m01is = l140m01iDao.findByOids(oids);
		L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
		if (!l140m01is.isEmpty()) {
			// 變更明細檢核
			if (l140m01a != null) {
				l140m01a.setChkYN(null);
				l140m01aDao.save(l140m01a);
			}

			l140m01iDao.delete(l140m01is);
			flag = true;
		}
		return flag;
	}

	@Override
	public Map<String, Map<String, BigDecimal>> findL140m01CountToTwoCurr(
			String caseMainId, String caseType, String countCurr,
			Boolean showCurr, Boolean saveRateFg) throws Exception {
		Properties Prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		Map<String, Map<String, BigDecimal>> countMap = findL140m01Count(
				caseMainId, caseType);
		// other.money
		String money = Prop.getProperty("other.money");
		// 存放最後的加總結果
		Map<String, Map<String, BigDecimal>> countEndMap = new HashMap<String, Map<String, BigDecimal>>();

		// 取出案件簽報書底下所有額度明細表
		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId, caseType, null);
		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);
		// 依目前簽案行做計算幣別
		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());

		// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
		L140M01A l140m01aLgd = null;
		if (l140m01as != null && !l140m01as.isEmpty()) {
			l140m01aLgd = l140m01as.get(0);
		}
		Map<String, String> lgdMap = lmsService.getLgdTotAmtParam(l140m01aLgd,
				null);
		int lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
				"lmsLgdCount", "0"));

		// 用來標記有多幣別的custId
		Map<String, Integer> twoMap = new HashMap<String, Integer>();
		Map<String, String> twoMapNowRate = new HashMap<String, String>();
		for (String name : countMap.keySet()) {
			String custId = name.substring(3, name.length());
			if (!twoMap.containsKey(custId)) {
				twoMap.put(custId, 0);

				String curr = Util.getLeftStr(name, 3);
				if (Util.notEquals(curr, countCurr)) {
					StringBuffer nowCurrRate = new StringBuffer("");
					NumberFormat n = new DecimalFormat("#.#####");

					BigDecimal nowRate = branchRate
							.toOtherRate(curr, countCurr);

					nowCurrRate.append(curr);
					nowCurrRate.append(":");

					String rateMaxStr = n.format(nowRate.setScale(5,
							BigDecimal.ROUND_HALF_UP));

					nowCurrRate.append(rateMaxStr);

					twoMapNowRate.put(custId,
							Util.equals(Util.trim(twoMapNowRate.get(custId)),
									"") ? nowCurrRate.toString()
									: twoMapNowRate.get(custId) + "，"
											+ nowCurrRate.toString());

				}

			} else {
				twoMap.put(custId, twoMap.get(custId) + 1);

				String curr = Util.getLeftStr(name, 3);
				if (Util.notEquals(curr, countCurr)) {
					StringBuffer nowCurrRate = new StringBuffer("");
					NumberFormat n = new DecimalFormat("#.#####");

					BigDecimal nowRate = branchRate
							.toOtherRate(curr, countCurr);

					// nowCurrRate.append("，");

					nowCurrRate.append(curr);
					nowCurrRate.append(":");

					String rateMaxStr = n.format(nowRate.setScale(5,
							BigDecimal.ROUND_HALF_UP));

					nowCurrRate.append(rateMaxStr);

					twoMapNowRate.put(custId,
							Util.equals(Util.trim(twoMapNowRate.get(custId)),
									"") ? nowCurrRate.toString()
									: twoMapNowRate.get(custId) + "，"
											+ nowCurrRate.toString());

				}

			}
		}

		Map<String, BigDecimal> moneyMap = null;
		// 用來存放多幣別顯示
		HashMap<String, HashMap<String, String>> allcurr = new HashMap<String, HashMap<String, String>>();
		for (String name : countMap.keySet()) {
			String custCurr = name.substring(0, 3);
			String custKey = name.substring(3, name.length());
			BigDecimal count1 = BigDecimal.ZERO;
			BigDecimal count2 = BigDecimal.ZERO;
			BigDecimal count3 = BigDecimal.ZERO;
			BigDecimal count4 = BigDecimal.ZERO;
			BigDecimal count5 = BigDecimal.ZERO;
			BigDecimal count6 = BigDecimal.ZERO;
			BigDecimal count7 = BigDecimal.ZERO;
			BigDecimal count8 = BigDecimal.ZERO;
			// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
			BigDecimal count9 = BigDecimal.ZERO;
			BigDecimal count10 = BigDecimal.ZERO;
			// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
			BigDecimal count11 = BigDecimal.ZERO;
			// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
			BigDecimal count12 = BigDecimal.ZERO;
			BigDecimal count13 = BigDecimal.ZERO;
			// J-111-0461_05097_B1009 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
			BigDecimal count14 = BigDecimal.ZERO;
			BigDecimal count15 = BigDecimal.ZERO;

			// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
			// BigDecimal count9 = BigDecimal.ZERO;
			// BigDecimal count10 = BigDecimal.ZERO;
			// BigDecimal count11 = BigDecimal.ZERO;

			// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
			HashMap<String, BigDecimal> lgdCount = new HashMap<String, BigDecimal>();
			for (int i = 1; i <= lmsLgdCount; i++) {
				lgdCount.put(Util.trim(i), BigDecimal.ZERO);
			}

			// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
			HashMap<String, BigDecimal> lgdCount_mg = new HashMap<String, BigDecimal>();
			for (int i = 1; i <= lmsLgdCount; i++) {
				lgdCount_mg.put(Util.trim(i), BigDecimal.ZERO);
			}

			// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
			HashMap<String, BigDecimal> lgdCount_rc = new HashMap<String, BigDecimal>();
			for (int i = 1; i <= lmsLgdCount; i++) {
				lgdCount_rc.put(Util.trim(i), BigDecimal.ZERO);
			}

			// 當幣別不只一種時才乘上匯率
			if (!twoMap.get(custKey).equals(0)) {
				if (allcurr.containsKey(custKey)) {
					HashMap<String, String> custMapSay = allcurr.get(custKey);
					String temp1 = custMapSay.get(授信額度合計多幣別說明);
					String temp2 = custMapSay.get(擔保授信合計多幣別說明);
					custMapSay.put(
							授信額度合計多幣別說明,
							temp1
									+ "\r\n"
									+ custCurr
									+ " "
									+ NumConverter.addComma(countMap.get(name)
											.get(授信額度合計)) + money);
					custMapSay.put(
							擔保授信合計多幣別說明,
							temp2
									+ "\r\n"
									+ custCurr
									+ " "
									+ NumConverter.addComma(countMap.get(name)
											.get(擔保授信合計)) + money);

					// TODO
					String temp3 = custMapSay.get(衍生性授信額度合計多幣別說明);

					custMapSay.put(
							衍生性授信額度合計多幣別說明,
							temp3
									+ "\r\n"
									+ custCurr
									+ " "
									+ NumConverter.addComma(countMap.get(name)
											.get(衍生性商品原始合計)) + money);

					allcurr.put(custKey, custMapSay);

				} else {
					HashMap<String, String> custMapSay = new HashMap<String, String>();
					custMapSay.put(
							授信額度合計多幣別說明,
							custCurr
									+ " "
									+ NumConverter.addComma(countMap.get(name)
											.get(授信額度合計)) + money);
					custMapSay.put(
							擔保授信合計多幣別說明,
							custCurr
									+ " "
									+ NumConverter.addComma(countMap.get(name)
											.get(擔保授信合計)) + money);

					custMapSay.put(
							衍生性授信額度合計多幣別說明,
							custCurr
									+ " "
									+ NumConverter.addComma(countMap.get(name)
											.get(衍生性商品原始合計)) + money);

					allcurr.put(custKey, custMapSay);
				}
				// 將目前幣別轉換成本位幣
				count1 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(授信額度合計));
				count2 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(擔保授信合計));
				count3 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(衍生性商品原始合計));
				count4 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(衍生性商品相當合計));
				count5 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(前准額度批覆合計));
				count6 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(前准額度批覆擔保合計));
				count7 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(減額額度合計));
				count8 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(減額擔保額度合計));

				// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
				count9 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(瑕疵押匯額度合計));
				count10 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(總授信額度合計));
				// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
				count11 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(單獨另計授權額度合計));
				// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
				count14 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(瑕疵押匯額度合計_合併關係));
				count15 = branchRate.toLocalAmt(custCurr, countMap.get(name)
						.get(總授信額度合計_合併關係));

				// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
				// count12 = branchRate.toLocalAmt(custCurr, countMap.get(name)
				// .get(瑕疵押匯額度合計_全案));
				//
				// count13 = branchRate.toLocalAmt(custCurr, countMap.get(name)
				// .get(總授信額度合計_全案));

				// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
				for (int i = 1; i <= lmsLgdCount; i++) {
					lgdCount.put(Util.trim(i), branchRate.toLocalAmt(custCurr,
							countMap.get(name).get(LGD合計 + "_" + i)));

					// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
					lgdCount_rc.put(Util.trim(i), branchRate.toLocalAmt(
							custCurr,
							countMap.get(name).get(LGD合計_合併關係 + "_" + i)));

					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					// lgdCount_mg.put(Util.trim(i), branchRate.toLocalAmt(
					// custCurr, countMap.get(name)
					// .get(LGD合計_全案 + "_" + i)));
				}
				// count9 = branchRate.toLocalAmt(custCurr, countMap.get(name)
				// .get(LGD其中無擔保合計));
				// count10 = branchRate.toLocalAmt(custCurr, countMap.get(name)
				// .get(LGD其中擬制部分擔保合計));
				// count11 = branchRate.toLocalAmt(custCurr, countMap.get(name)
				// .get(LGD其中擬制十足擔保合計));

				// 該分行主要計價幣別
				String mainCurr = branchRate.getMCurr();
				if (countEndMap.containsKey(mainCurr + custKey)) {
					moneyMap = countEndMap.get(mainCurr + custKey);

					// 如果該幣別已存在就要取出來在作加總
					moneyMap.put(授信額度合計, moneyMap.get(授信額度合計).add(count1));
					moneyMap.put(擔保授信合計, moneyMap.get(擔保授信合計).add(count2));
					moneyMap.put(衍生性商品原始合計, moneyMap.get(衍生性商品原始合計).add(count3));
					moneyMap.put(衍生性商品相當合計, moneyMap.get(衍生性商品相當合計).add(count4));
					moneyMap.put(前准額度批覆合計, moneyMap.get(前准額度批覆合計).add(count5));
					moneyMap.put(前准額度批覆擔保合計,
							moneyMap.get(前准額度批覆擔保合計).add(count6));
					moneyMap.put(減額額度合計, moneyMap.get(減額額度合計).add(count7));
					moneyMap.put(減額擔保額度合計, moneyMap.get(減額擔保額度合計).add(count8));

					// J-111-0461_05097_B1002
					// 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
					moneyMap.put(瑕疵押匯額度合計, moneyMap.get(瑕疵押匯額度合計).add(count9));
					moneyMap.put(總授信額度合計, moneyMap.get(總授信額度合計).add(count10));

					// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
					moneyMap.put(單獨另計授權額度合計,
							moneyMap.get(單獨另計授權額度合計).add(count11));

					// J-111-0461_05097_B1009
					// 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
					moneyMap.put(瑕疵押匯額度合計_合併關係, moneyMap.get(瑕疵押匯額度合計_合併關係)
							.add(count14));
					moneyMap.put(總授信額度合計_合併關係,
							moneyMap.get(總授信額度合計_合併關係).add(count15));

					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					// moneyMap.put(瑕疵押匯額度合計_全案,
					// moneyMap.get(瑕疵押匯額度合計_全案).add(count12));
					// moneyMap.put(總授信額度合計_全案,
					// moneyMap.get(總授信額度合計_全案).add(count13));

					// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
					// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
					for (int i = 1; i <= lmsLgdCount; i++) {
						moneyMap.put(
								LGD合計 + "_" + i,
								moneyMap.get(LGD合計 + "_" + i).add(
										lgdCount.get(Util.trim(i))));

						// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
						moneyMap.put(
								LGD合計_合併關係 + "_" + i,
								moneyMap.get(LGD合計_合併關係 + "_" + i).add(
										lgdCount_rc.get(Util.trim(i))));

						// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
						// moneyMap.put(
						// LGD合計_全案 + "_" + i,
						// moneyMap.get(LGD合計_全案 + "_" + i).add(
						// lgdCount_mg.get(Util.trim(i))));

					}

					// moneyMap.put(LGD其中無擔保合計,
					// moneyMap.get(LGD其中無擔保合計).add(count9));
					// moneyMap.put(LGD其中擬制部分擔保合計, moneyMap.get(LGD其中擬制部分擔保合計)
					// .add(count10));
					// moneyMap.put(LGD其中擬制十足擔保合計, moneyMap.get(LGD其中擬制十足擔保合計)
					// .add(count11));
				} else {
					moneyMap = new HashMap<String, BigDecimal>();
					moneyMap.put(授信額度合計, count1);
					moneyMap.put(擔保授信合計, count2);
					moneyMap.put(衍生性商品原始合計, count3);
					moneyMap.put(衍生性商品相當合計, count4);
					moneyMap.put(前准額度批覆合計, count5);
					moneyMap.put(前准額度批覆擔保合計, count6);
					moneyMap.put(減額額度合計, count7);
					moneyMap.put(減額擔保額度合計, count8);

					// J-111-0461_05097_B1002
					// 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
					moneyMap.put(瑕疵押匯額度合計, count9);
					moneyMap.put(總授信額度合計, count10);

					// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
					moneyMap.put(單獨另計授權額度合計, count11);

					// J-111-0461_05097_B1009
					// 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
					moneyMap.put(瑕疵押匯額度合計_合併關係, count14);
					moneyMap.put(總授信額度合計_合併關係, count15);

					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					// moneyMap.put(瑕疵押匯額度合計_全案, count12);
					// moneyMap.put(總授信額度合計_全案, count13);

					// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
					for (int i = 1; i <= lmsLgdCount; i++) {
						moneyMap.put(LGD合計 + "_" + i,
								lgdCount.get(Util.trim(i)));

						// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
						moneyMap.put(LGD合計_合併關係 + "_" + i,
								lgdCount_rc.get(Util.trim(i)));

						// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
						// moneyMap.put(LGD合計_全案 + "_" + i,
						// lgdCount_mg.get(Util.trim(i)));
					}

					// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
					// moneyMap.put(LGD其中無擔保合計, count9);
					// moneyMap.put(LGD其中擬制部分擔保合計, count10);
					// moneyMap.put(LGD其中擬制十足擔保合計, count11);

				}
				countEndMap.put(mainCurr + custKey, moneyMap);
			} else {
				// 當沒有標記為多幣別的就直接放到最後結果的map
				countEndMap.put(name, countMap.get(name));

				HashMap<String, String> custMapSay = new HashMap<String, String>();

				/*
				 * custMapSay.put( 授信額度合計多幣別說明, custCurr + " " +
				 * NumConverter.addComma(countMap.get(name) .get(授信額度合計)) +
				 * money); custMapSay.put( 擔保授信合計多幣別說明, custCurr + " " +
				 * NumConverter.addComma(countMap.get(name) .get(擔保授信合計)) +
				 * money);
				 */
				custMapSay.put(
						衍生性授信額度合計多幣別說明,
						custCurr
								+ " "
								+ NumConverter.addComma(countMap.get(name).get(
										衍生性商品原始合計)) + money);

				allcurr.put(custKey, custMapSay);

			}
		}

		// 儲存要刪除的key值
		Map<String, Map<String, BigDecimal>> countEndMap2 = new HashMap<String, Map<String, BigDecimal>>();
		// 將有多幣別的客戶 的幣別換成 在畫面選擇的幣別
		for (String name : countEndMap.keySet()) {
			String custId = name.substring(3, name.length());

			// 把多幣別的客戶加總金額(乘/除?)上最後所選擇的匯率
			if (!twoMap.get(custId).equals(0)) {
				moneyMap = countEndMap.get(name);
				BigDecimal countTemp = null;
				// 將本位幣轉換成 計價幣別
				// type 當為本位幣 轉 其他幣別時 M 為除、D 為乘 ，
				for (String name2 : moneyMap.keySet()) {
					countTemp = branchRate.toOtherAmt(branchRate.getMCurr(),
							countCurr, moneyMap.get(name2));
					moneyMap.put(name2, countTemp);
				}

				// 將map的key 換成使用者所選的幣別
				countEndMap2.put(countCurr + custId, moneyMap);
			} else {
				countEndMap2.put(name, countEndMap.get(name));
			}
		}

		// J-111-0461_05097_B1006-2 授信額度合計新增單獨另計授權及各組LGD合計檢核
		// 合併改成一筆所有借款人合計，所以改到baseCount後才合計**********************
		countEndMap2 = this.baseCountMerge(countEndMap2, l140m01as, branchRate);

		this.saveCountResult(countEndMap2, allcurr, l140m01as, showCurr,
				branchRate, saveRateFg, twoMap, twoMapNowRate);
		return countEndMap2;
	}

	@Override
	public L140M01A findL140m01aByMainId(String mainId) {
		return l140m01aDao.findByUniqueKey(mainId);
	}

	@Override
	public List<L140M01A> findL140m01aListByMainIdList(String[] mainId) {
		return l140m01aDao.findL140m01aListByMainIdList(mainId);
	}

	@Override
	public Map<String, Map<String, JSONObject>> findL140m01iPeopleData(
			String custId, String dupNo) {
		Map<String, Map<String, JSONObject>> peoele = new HashMap<String, Map<String, JSONObject>>();

		List<Map<String, Object>> rows = this.eloandbService
				.findC140M04A_Natural(custId, dupNo);
		List<Map<String, Object>> rows2 = this.eloandbService
				.findC140M04B_Corporate(custId, dupNo);
		Map<String, JSONObject> idName = new HashMap<String, JSONObject>();
		for (Map<String, Object> row : rows) {
			String id = Util.trim(((String) row.get("ID")));
			String name = Util.trim(((String) row.get("NAME")));
			String dupNo1 = Util.trim(((String) row.get("DUPNO")));
			if (!Util.isEmpty(id) || !Util.isEmpty(name)) {
				JSONObject cust = new JSONObject();
				cust.put("ID", id);
				cust.put("NAME", name);
				cust.put("DUPNO", dupNo1);
				idName.put(id, cust);
			}
		}
		peoele.put("N", idName);
		Map<String, JSONObject> idName2 = new HashMap<String, JSONObject>();
		for (Map<String, Object> row : rows2) {
			String id = Util.trim(((String) row.get("ID")));
			String name = Util.trim(((String) row.get("NAME")));
			String dupNo1 = Util.trim(((String) row.get("DUPNO")));
			if (!Util.isEmpty(id) || !Util.isEmpty(name)) {
				JSONObject cust = new JSONObject();
				cust.put("ID", id);
				cust.put("NAME", name);
				cust.put("DUPNO", dupNo1);
				idName.put(id, cust);
			}

		}
		peoele.put("C", idName2);
		return peoele;
	}

	@Override
	public void saveL140m01iList(List<L140M01I> L140m01iList) {
		l140m01iDao.save(L140m01iList);

	}

	@Override
	public List<L140M01A> findL140m01aByOids(String[] oids) {
		return l140m01aDao.findL140m01aListByOids(oids);
	}

	@Override
	public L140M02A findL140M02AByMainId(String mainId) {
		return l140m02aDao.findByUniqueKey(mainId);
	}

	@Override
	public L782A01A findL782A01AByMainId(String mainId, String authUnit) {
		return l782a01aDao.findByUniqueKey(mainId, authUnit, "1", authUnit);
	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType) {
		return l140m01aDao.findL140m01aListByL120m01cMainId(mainId, caseType,
				null);

	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainIdForPrint(
			String mainId, String caseType) {
		return l140m01aDao.findL140m01aListByL120m01cMainIdForPrint(mainId,
				caseType, null);
	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainIdOrderByCust(
			String mainId, String caseType) {
		return l140m01aDao.findL140m01aListByL120m01cMainIdOrderByCust(mainId,
				caseType);

	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String[] caseType, String docStatus) {
		return l140m01aDao.findL140m01aListByL120m01cMainId(mainId, caseType,
				docStatus);
	}

	@Override
	public void saveMain(List<L140M01B> l140m01bs, List<L140M01E> l140m01es,
			GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (l140m01bs != null) {
			for (L140M01B l140m01b : l140m01bs) {
				l140m01b.setUpdater(user.getUserId());
				l140m01b.setUpdateTime(CapDate.getCurrentTimestamp());
			}
			l140m01bDao.save(l140m01bs);
		}

		if (l140m01es != null) {
			for (L140M01E l140m01e : l140m01es) {
				l140m01e.setUpdater(user.getUserId());
				l140m01e.setUpdateTime(CapDate.getCurrentTimestamp());
			}
			l140m01eDao.save(l140m01es);
		}
		if (entity != null) {
			save(entity);
		}

	}

	@Override
	public List<L140M01C> findL140m01cListByMainId(String mainId) {
		return l140m01cDao.findByMainId(mainId);
	}

	@Override
	public List<L140M01B> findL140m01bByMainId(String mainId) {
		return l140m01bDao.findByMainId(mainId);
	}

	@Override
	public List<L120M01C> findL120m01cListByMainId(String caseMainId,
			String docStatus, String[] caseType) {
		return l120m01cDao.findByMainId(caseMainId);

	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType, String docStatus, String[] dataSrc) {
		List<L140M01A> l140m01as = new ArrayList<L140M01A>();
		List<L140M01A> l140m01asOld = l140m01aDao
				.findL140m01aListByL120m01cMainId(mainId, caseType, docStatus);
		if (dataSrc == null || dataSrc.length == 0) {
			return l140m01as;
		}
		for (L140M01A l140m01a : l140m01asOld) {

			for (String src : dataSrc) {
				if (src.equals(l140m01a.getDataSrc())) {
					l140m01as.add(l140m01a);
				}
			}

		}
		return l140m01as;
	}

	@Override
	public void copyCntrdoc(String mainId, String caseType, String[] mainName,
			String[] oidList) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 新增一個List 存放要儲存的新L140M01A List
		ArrayList<L140M01A> newL140m01as = new ArrayList<L140M01A>();
		ArrayList<L120M01C> newl120m01cs = new ArrayList<L120M01C>();
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);

		// if (l120m01a == null) {
		// logger.error("L120M01A is null");
		// // EFD0025=ERROR|執行有誤$\{msg\}|
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// "EFD0025"), getClass());
		// }
		// 抓要複製的oid清單

		List<L140M01A> copyL140m01as = findL140m01aByOids(oidList);
		ArrayList<String> mainids = new ArrayList<String>();
		// 存放多筆欄位資料
		for (L140M01A l140m01aOld : copyL140m01as) {

			// 要儲存到的新model
			L140M01A newLm140m01a = new L140M01A();

			CapBeanUtil.copyBean(l140m01aOld, newLm140m01a,
					CapBeanUtil.getFieldName(L140M01A.class, true));// 複製的語法

			// 清空複製出來的oid
			newLm140m01a.setOid(null);

			// 設定要更換的借款人名稱
			if (true) {
				if (LMSUtil.isOverSea_CLS(l120m01a)) {
					C120M01A c120m01a = c120m01aDao.findByUniqueKey(mainId,
							mainName[0].substring(0, mainName[0].length() - 1),
							mainName[0].substring(mainName[0].length() - 1));

					newLm140m01a.setCustId(c120m01a.getCustId());
					newLm140m01a.setDupNo(c120m01a.getDupNo());
					newLm140m01a.setTypCd(c120m01a.getTypCd());
					newLm140m01a.setCustName(c120m01a.getCustName());
					OverSeaUtil.clearOldColumn(newLm140m01a);
				} else {
					L120S01A l120s01a = l120s01aDao.findByUniqueKey(mainId,
							mainName[0].substring(0, mainName[0].length() - 1),
							mainName[0].substring(mainName[0].length() - 1));

					newLm140m01a.setCustId(l120s01a.getCustId());
					newLm140m01a.setDupNo(l120s01a.getDupNo());
					newLm140m01a.setTypCd(l120s01a.getTypCd());
					newLm140m01a.setCustName(l120s01a.getCustName());
				}
			}

			newLm140m01a.setDocStatus(FlowDocStatusEnum.編製中.getCode());

			// 清空部分欄位
			newLm140m01a.setCntrNo("");
			newLm140m01a.setCommSno("");
			newLm140m01a.setProPerty("");
			newLm140m01a.setBrdTime(null);
			newLm140m01a.setChkYN(null);
			newLm140m01a.setPrintSeq(null);
			newLm140m01a.setCesRjtCause("");
			newLm140m01a.setCesRjtReason("");
			// 因為其額度序號 已經 清空分行逾放比率
			newLm140m01a.setNpl(null);
			newLm140m01a.setNpldate(null);
			// J-112-0082 約定融資額度註記部分，原以表列式勾選方式，改以問答方式，非既有額度要清空欄位
			newLm140m01a.setExceptFlagQAisY("");
			// J-112-0566 約定融資額度註記針對[無條件可取消]、[有條件可取消]新增勾選項目，非既有額度要清空欄位
			newLm140m01a.setExceptFlagQAPlus("");

			// set須更換欄位
			// 分行號碼
			newLm140m01a.setOwnBrId(user.getUnitNo());
			// 案號
			newLm140m01a.setCaseNo(l120m01a.getCaseNo());
			// 簽案日期
			newLm140m01a.setCaseDate(l120m01a.getCaseDate());
			newLm140m01a.setUpdateTime(CapDate.getCurrentTimestamp());
			newLm140m01a.setCreateTime(CapDate.getCurrentTimestamp());
			newLm140m01a.setCreator(user.getUserId());
			newLm140m01a.setUpdater(user.getUserId());
			newLm140m01a.setCntrChgOid("");
			// 創一個新的mainId
			newLm140m01a.setMainId(IDGenerator.getUUID());
			newLm140m01a.setL120m01c(null);
			newLm140m01a.setL140m01b(null);
			newLm140m01a.setL140m01c(null);
			newLm140m01a.setL140m01d(null);
			newLm140m01a.setL140m01e(null);
			newLm140m01a.setL140m01f(null);
			newLm140m01a.setL140m01i(null);
			newLm140m01a.setL140m01j(null); // J-106-0029-002
			newLm140m01a.setL120m01g(null); // J-110-0211_11557_B1001

			// 複製來源 2
			newLm140m01a.setDataSrc(UtilConstants.Cntrdoc.DataSrc.複製額度明細表);
			// J-108-0283 變更條件Condition Change
			copyL140m01All(newLm140m01a.getMainId(), l140m01aOld.getMainId(),
					false, false, true, false, false, true);
			clearL140M01CGrage(newLm140m01a);
			// 關聯檔
			// 把案件簽報書的mainId放進來
			L120M01C l120m01c = new L120M01C();
			l120m01c.setMainId(mainId);
			// 額度明細表新的mainId放到關係的RefMainId
			l120m01c.setRefMainId(newLm140m01a.getMainId());

			// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
			l120m01c.setItemType(caseType);
			l120m01c.setCreateTime(CapDate.getCurrentTimestamp());
			// 抓目前使用者名稱
			l120m01c.setCreator(user.getUserId());
			newl120m01cs.add(l120m01c);
			newL140m01as.add(newLm140m01a);// 將修改過的資料放入ArrayList的陣列中
			mainids.add(newLm140m01a.getMainId());
		}// close for
		l140m01aDao.save(newL140m01as);// 儲存資料傳出mainOid，在service判斷是否有mainOid來新增創文件者，若沒有OID新建Oid
		l120m01cDao.save(newl120m01cs);// 儲存資料傳出mainOid，在service判斷是否有mainOid來新增創文件者，若沒有OID新建Oid

		// #J-110-0211_11557_B1001
		// 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
		String parentCntrNo = "";
		List<L140M03A> l140m03as = l140m03aDao.findByMainIds(mainids);
		for (L140M03A l140m03a : l140m03as) {
			// 清空
			// (1)團貸總戶編號
			// (2)是否已動用
			// (3)動用日期清掉。

			l140m03a.setGrpCntrNo(parentCntrNo);
			// 2013/08/05,Rex,修改是否動用寫入判斷
			if (Util.isNotEmpty(parentCntrNo)) {
				l140m03a.setIsUse(UtilConstants.DEFAULT.否);
			} else {
				l140m03a.setIsUse("");
			}
			l140m03a.setIsUseDate(null);
		}
	}

	@Override
	public void copyCntrdocByl141m01a(String mainId, String selectMainid)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		ArrayList<L140M01A> newL140m01as = new ArrayList<L140M01A>();
		ArrayList<L120M01C> newl120m01cs = new ArrayList<L120M01C>();

		/*
		 * Notes e-Loan的舊案轉檔，在 L120M01C 裡面，可能只有 itemType=='2' 的資料 而下列條件用
		 * itemType=='1' 去搜尋，會查到0筆
		 * 
		 * WorkAround： 把 itemType=='2'的 L120M01C 再 copy 一份 itemType=='1' 讓分行能引入
		 * 聯行額度明細表 的內容
		 */
		List<L140M01A> l140m01as = this.findL140m01aListByL120m01cMainId(
				selectMainid, UtilConstants.Cntrdoc.ItemType.額度明細表, null);
		ArrayList<String> mainids = new ArrayList<String>();

		for (L140M01A l140m01aOld : l140m01as) {

			// 要儲存到的新model
			L140M01A newLm140m01a = new L140M01A();

			CapBeanUtil.copyBean(l140m01aOld, newLm140m01a,
					CapBeanUtil.getFieldName(L140M01A.class, true));// 複製的語法

			// 清空複製出來的oid
			newLm140m01a.setOid(null);
			newLm140m01a.setChkYN(null);
			newLm140m01a.setCntrChgOid("");
			newLm140m01a.setPrintSeq(null);

			// 創一個新的mainId
			newLm140m01a.setMainId(IDGenerator.getUUID());
			newLm140m01a.setL120m01c(null);
			newLm140m01a.setL120m01g(null);
			newLm140m01a.setL140m01b(null);
			newLm140m01a.setL140m01c(null);
			newLm140m01a.setL140m01d(null);
			newLm140m01a.setL140m01e(null);
			newLm140m01a.setL140m01f(null);
			newLm140m01a.setL140m01i(null);
			newLm140m01a.setL140m01j(null); // J-106-0029-002
			newLm140m01a.setL120m01g(null); // J-110-0211_11557_B1001

			newLm140m01a.setUpdateTime(CapDate.getCurrentTimestamp());
			newLm140m01a.setCreateTime(CapDate.getCurrentTimestamp());
			newLm140m01a.setCreator(user.getUserId());
			newLm140m01a.setUpdater(user.getUserId());
			newLm140m01a.setDocStatus(FlowDocStatusEnum.編製中.getCode());
			// Uid 塞入原轉入聯行的簽報書mainId
			newLm140m01a.setUid(selectMainid);
			// 轉入聯行額度明細表 性質要為不變
			newLm140m01a.setProPerty(UtilConstants.Cntrdoc.Property.不變);
			// J-108-0283 變更條件Condition Change
			copyL140m01All(newLm140m01a.getMainId(), l140m01aOld.getMainId(),
					true, true, false, false, false, true);
			lmsService.reset72_2information(newLm140m01a);

			// 複製額度明細表時以新資料取代
			// J-105-0074-001 Web e-Loan
			// 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
			lmsService.resetCntrDocRelateDataWhenCopy(newLm140m01a);

			// 複製來源 3聯行
			newLm140m01a.setDataSrc(UtilConstants.Cntrdoc.DataSrc.轉入額度明細表);
			// 關係檔
			// 把案件簽報書的mainId放進來
			L120M01C l120m01c = new L120M01C();
			l120m01c.setMainId(mainId);
			// 額度明細表新的mainId放到關係的RefMainId
			l120m01c.setRefMainId(newLm140m01a.getMainId());
			// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
			l120m01c.setItemType(UtilConstants.Cntrdoc.ItemType.額度明細表);
			l120m01c.setCreateTime(CapDate.getCurrentTimestamp());
			// 抓目前使用者名稱
			l120m01c.setCreator(user.getUserId());
			newl120m01cs.add(l120m01c);
			newL140m01as.add(newLm140m01a);// 將修改過的資料放入ArrayList的陣列中
			mainids.add(newLm140m01a.getMainId());
		}
		l120m01cDao.save(newl120m01cs);
		l140m01aDao.save(newL140m01as);// 儲存資料傳出mainOid，在service判斷是否有mainOid來新增創文件者，若沒有OID新建Oid

		String parentCntrNo = "";
		List<L140M03A> l140m03as = l140m03aDao.findByMainIds(mainids);
		for (L140M03A l140m03a : l140m03as) {
			// 清空
			// (1)團貸總戶編號
			// (2)是否已動用
			// (3)動用日期清掉。

			l140m03a.setGrpCntrNo(parentCntrNo);
			// 2013/08/05,Rex,修改是否動用寫入判斷
			if (Util.isNotEmpty(parentCntrNo)) {
				l140m03a.setIsUse(UtilConstants.DEFAULT.否);
			} else {
				l140m03a.setIsUse("");
			}
			l140m03a.setIsUseDate(null);
		}
	}

	@Override
	public List<L140M01A> findL140m01aBycntrNo(String cntrNo, String custId,
			String dupNo) {
		return l140m01aDao.findL140m01aListBycntrNo(cntrNo, custId, dupNo);
	}

	@Override
	public void copyCntrdoc(String mainId, String caseType, String newMainId,
			String caseNo, Date caseDate) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 新增一個List 存放要儲存的新L140M01A List
		ArrayList<L140M01A> newL140M01as = new ArrayList<L140M01A>();

		ArrayList<L120M01C> newL120M01cs = new ArrayList<L120M01C>();
		List<L140M01A> copyL140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(mainId, caseType, null);
		// 存放多筆欄位資料
		for (L140M01A l140m01aOld : copyL140m01as) {
			// 要儲存到的新model
			L140M01A newLm140m01a = new L140M01A();

			CapBeanUtil.copyBean(l140m01aOld, newLm140m01a,
					CapBeanUtil.getFieldName(L140M01A.class, true));// 複製的語法
			// 清空部分欄位
			// 當等於轉入的額度明細表不清空
			if (!UtilConstants.Cntrdoc.DataSrc.轉入額度明細表.equals(l140m01aOld
					.getDataSrc())) {
				newLm140m01a.setBrdTime(null);
			}

			// 清空複製出來的oid
			newLm140m01a.setOid(null);
			newLm140m01a.setChkYN(UtilConstants.DEFAULT.否);
			// set須更換欄位
			// 分行號碼
			newLm140m01a.setOwnBrId(user.getUnitNo());
			// 案號
			newLm140m01a.setCaseNo(caseNo);
			// 簽案日期
			newLm140m01a.setCaseDate(caseDate);
			newLm140m01a.setCesRjtCause("");
			newLm140m01a.setCesRjtReason("");

			// 創一個新的mainId
			newLm140m01a.setMainId(IDGenerator.getUUID());
			newLm140m01a.setPrintSeq(null);
			newLm140m01a.setDataSrc(UtilConstants.Cntrdoc.DataSrc.條件續約變更產生);
			// 帶入來源額度明細表
			newLm140m01a.setMainIdSrc(l140m01aOld.getMainId());
			logger.info("new mainId===>" + newLm140m01a.getMainId());
			newLm140m01a.setL120m01c(null);
			newLm140m01a.setL140m01b(null);
			newLm140m01a.setL140m01c(null);
			newLm140m01a.setL140m01d(null);
			newLm140m01a.setL140m01e(null);
			newLm140m01a.setL140m01f(null);
			newLm140m01a.setL140m01i(null);
			newLm140m01a.setL140m01j(null); // J-106-0029-002
			newLm140m01a.setL120m01g(null); // J-110-0211_11557_B1001

			newLm140m01a.setDocStatus(FlowDocStatusEnum.編製中.getCode());
			newLm140m01a.setUpdateTime(CapDate.getCurrentTimestamp());
			newLm140m01a.setCreateTime(CapDate.getCurrentTimestamp());
			newLm140m01a.setCreator(user.getUserId());
			newLm140m01a.setUpdater(user.getUserId());
			this.copyL140m01All(newLm140m01a.getMainId(),
					l140m01aOld.getMainId(), true, true, false, false, false, true);
			lmsService.reset72_2information(newLm140m01a);

			clearL140M01CGrage(newLm140m01a);
			// 關聯檔
			// 把案件簽報書的mainId放進來
			L120M01C l120m01c = new L120M01C();
			l120m01c.setMainId(newMainId);
			// 額度明細表新的mainId放到關係的RefMainId
			l120m01c.setRefMainId(newLm140m01a.getMainId());
			// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
			l120m01c.setItemType(UtilConstants.Cntrdoc.ItemType.額度明細表);
			l120m01c.setCreator(user.getUserId());
			l120m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l120m01c.setUpdater(user.getUserId());
			l120m01c.setUpdateTime(CapDate.getCurrentTimestamp());
			// 抓目前使用者名稱

			newL120M01cs.add(l120m01c);
			newL140M01as.add(newLm140m01a);// 將修改過的資料放入ArrayList的陣列中
		}// close for
		l120m01cDao.save(newL120M01cs);
		l140m01aDao.save(newL140M01as);// 儲存資料傳出mainOid，在service判斷是否有mainOid來新增創文件者，若沒有OID新建Oid

	}

	/**
	 * 在 copy 額度明細表時，有時是複製批覆書
	 */
	private void clearL140M01CGrage(L140M01A newL140M01A) {
		for (L140M01C l140m01c : l140m01cDao.findByMainId(newL140M01A
				.getMainId())) {
			OverSeaUtil.clearL140M01C_rating(l140m01c);
			l140m01cDao.save(l140m01c);
		}
	}

	@Override
	public void delL140M01KByOids(String[] oids, L140M01A l140m01a) {
		List<L140M01K> l140m01ks = l140m01kDao.findByOids(oids);
		if (!l140m01ks.isEmpty()) {
			l140m01kDao.delete(l140m01ks);
			l140m01a.setChkYN(null);
			l140m01aDao.save(l140m01a);
		}

	}

	@Override
	public Map<String, String> gfnDB2ChkNeedControlByCntrDoc(L140M01A l140m01a, Boolean showCntrNo) throws CapMessageException {
		Map<String, String> returnMap = new HashMap<String, String>();
		HashMap<String, String> tempMap = new HashMap<String, String>();
		String proPerty = l140m01a.getProPerty();
		String riskArea = l140m01a.getRiskArea();
		String custId = l140m01a.getCustId();
		String dupNo = l140m01a.getDupNo();

		// J-110-0458 企金授權內其他 - 「簡易簽報」選項，適用方案「LIBOR退場變更利率條件簡易簽報」
		L120M01C l120m01c = l140m01a.getL120m01c();
		L120M01A l120m01a = null;
		if (l120m01c != null) {
			l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());
		}
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}
		if (lmsService.isLiborExitCase(l120m01a)) {
			return returnMap;
		}
		if (lmsService.isEuroyenTiborExitCase(l120m01a)) {
			return returnMap;
		}

		// 當為不變時不須控管
		if (!LMSUtil
				.isContainValue(proPerty, UtilConstants.Cntrdoc.Property.不變)) {

			logger.info("引進借款人行業別及次產業別資訊======>");
			Map<String, Object> busCDMap = misCustdataService
					.findBUSCDByCustIdANdDupNo(custId, dupNo);
			String DBELF447N_BUS_CD = "";
			String DBELF447N_BUS_SUB_CD = "";
			if (busCDMap != null) {
				// 行業別代碼
				DBELF447N_BUS_CD = Util.trim(busCDMap.get("BUSCD"));
				DBELF447N_BUS_SUB_CD = Util.trim(busCDMap.get("BUSSKIND"));
			}

			// 引進借款人行業別及次產業別資訊錯誤",16,gMsgTitle
			if (!Util.isEmpty(DBELF447N_BUS_CD)) {
				tempMap.put(行業別代碼, DBELF447N_BUS_CD);
			} else {
				// EFD3009=ERROR|$\{custId\}客戶中文檔0024 無此借款人資料 ！！|
				HashMap<String, String> msg = new HashMap<String, String>();
				msg.put("custId",
						custId + " " + dupNo + " " + l140m01a.getCustName());
				throw new CapMessageException(RespMsgHelper.getMessage("EFD3009", msg), getClass());
			}

			if (!Util.isEmpty(DBELF447N_BUS_SUB_CD)) {
				tempMap.put(行業別代碼, tempMap.get(行業別代碼) + DBELF447N_BUS_SUB_CD);
			} else {
				tempMap.put(行業別代碼, tempMap.get(行業別代碼) + "00");
			}

			// 集團代碼
			String DBELF447N_GRPNO = "";
			List<Map<String, Object>> grpcmp = misGrpcmpService
					.findGrpcmpSelGrpdtl(custId, dupNo);

			for (Map<String, Object> data : grpcmp) {
				DBELF447N_GRPNO = Util.trim((String) data.get("GRPID"));
			}

			logger.info("查詢集團代碼misGrpcmp=======>" + DBELF447N_GRPNO);
			if (!Util.isEmpty(DBELF447N_GRPNO)) {
				// tempMap.put(集團代碼,DBELF447N_GRPNO.substring(1,
				// DBELF447N_GRPNO.length()));

				tempMap.put(集團代碼, DBELF447N_GRPNO);
			}
			// 國別代碼
			// 修改事項：大陸授信額度簽報預約原則 (e-loan管理系統-資料建檔維護系統-其他建檔維護-預約額度申請作業)
			// 修改內容：
			// 一、目前大陸授信額度簽報預約原則：
			// 1.風險國別是CN，且非遠匯案件。
			// 或
			// 2.風險國別非CN，大陸地區控管授信為是，且非兩岸間短期貿易融資額度註記及非風險移轉就要預約。
			//
			//
			// 二、煩請修改為以下:
			// 1.預約原則仍同上揭一、1及2點。
			// 2.惟排除以下案件(即以下案件不必預約)：
			// 風險國別是CN，大陸地區控管授信為是，且屬於兩岸間短期貿易融資額度註記者。
			// if (!Util.isEmpty(riskArea)) {
			//
			// if (riskArea.equals("CN")) {
			//
			// String isChinaLoan = "";
			// String sTradeFg = "";
			//
			// L140M01Q l140m01q = l140m01qDao.findByMainId(l140m01a
			// .getMainId());
			//
			// if (!Util.isEmpty(l140m01q)) {
			// isChinaLoan = l140m01q.getCnLoanFg(); //
			// 本額度是否屬「本行對大陸地區授信業務管理要點」定義之放款
			// sTradeFg = l140m01q.getsTradeFg(); // 兩岸間短期貿易融資額度註記
			// }
			//
			// if (StringUtils.substring(Util.trim(l140m01a.getCntrNo()),
			// 7, 8).equals("X")) {
			// // 國別為CN且為遠匯額度則不要預約額度。
			// } else if (isChinaLoan.equals("Y") && sTradeFg.equals("Y")) {
			// // 風險國別是CN，大陸地區控管授信為是，且屬於兩岸間短期貿易融資額度註記者，不必預約額度。
			// } else {
			// tempMap.put(國別代碼, riskArea);
			// }
			// } else {
			// tempMap.put(國別代碼, riskArea);
			// }
			// } else {
			// Properties prop = MessageBundleScriptCreator
			// .getComponentResource(LMS1405S02Panel.class);
			// // L140M01a.risknt =本額度風險歸屬國別
			// HashMap<String, String> msg = new HashMap<String, String>();
			// msg.put("colName", prop.getProperty("L140M01a.risknt"));
			// throw new CapMessageException(RespMsgHelper.getMessage(parent,
			// UtilConstants.AJAX_RSP_MSG.欄位不得為空, msg), getClass());
			//
			// }
			//
			// // 屬「本行對大陸地區授信業務管理要點」時，就算風險國別不是CN，也要加CN進去
			// if (!riskArea.equals("CN")) {
			// L140M01Q l140m01q = l140m01qDao.findByMainId(l140m01a
			// .getMainId());
			// if (!Util.isEmpty(l140m01q)) {
			// String isChinaLoan = "";
			// String sTradeFg = "";
			// BigDecimal guar1Rate = l140m01q.getGuar1Rate() == null ?
			// BigDecimal.ZERO
			// : l140m01q.getGuar1Rate();
			// BigDecimal guar2Rate = l140m01q.getGuar2Rate() == null ?
			// BigDecimal.ZERO
			// : l140m01q.getGuar2Rate();
			// BigDecimal guar3Rate = l140m01q.getGuar3Rate() == null ?
			// BigDecimal.ZERO
			// : l140m01q.getGuar3Rate();
			// BigDecimal coll1Rate = l140m01q.getColl1Rate() == null ?
			// BigDecimal.ZERO
			// : l140m01q.getColl1Rate();
			// BigDecimal coll2Rate = l140m01q.getColl2Rate() == null ?
			// BigDecimal.ZERO
			// : l140m01q.getColl2Rate();
			// BigDecimal coll3Rate = l140m01q.getColl3Rate() == null ?
			// BigDecimal.ZERO
			// : l140m01q.getColl3Rate();
			// BigDecimal coll4Rate = l140m01q.getColl4Rate() == null ?
			// BigDecimal.ZERO
			// : l140m01q.getColl4Rate();
			// BigDecimal coll5Rate = l140m01q.getColl5Rate() == null ?
			// BigDecimal.ZERO
			// : l140m01q.getColl5Rate();
			// BigDecimal totalTransfer = guar1Rate.add(guar2Rate)
			// .add(guar3Rate).add(coll1Rate).add(coll2Rate)
			// .add(coll3Rate).add(coll4Rate).add(coll5Rate);
			//
			// isChinaLoan = l140m01q.getCnLoanFg(); //
			// 本額度是否屬「本行對大陸地區授信業務管理要點」定義之放款
			// sTradeFg = l140m01q.getsTradeFg(); // 兩岸間短期貿易融資額度註記
			// // 有赴大陸投資且不為兩岸間短期貿易融資額度註記且非有風險移轉就要預約額度
			// if (isChinaLoan.equals("Y")) {
			// if (!(totalTransfer.intValue() > 0 || sTradeFg
			// .equals("Y"))) {
			// if (riskArea.equals("CN")
			// && StringUtils.substring(
			// Util.trim(l140m01a.getCntrNo()), 7,
			// 8).equals("X")) {
			// // 國別為CN且為遠匯額度則不要預約額度
			// } else {
			// tempMap.put(
			// UtilConstants.Cntrdoc.NeedControl.大陸地區授信,
			// "CN");
			// }
			// }
			//
			// }
			// }
			//
			// }

			if (Util.isEmpty(riskArea)) {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1405S02Panel.class);
				// L140M01a.risknt =本額度風險歸屬國別
				HashMap<String, String> msg = new HashMap<String, String>();
				msg.put("colName", prop.getProperty("L140M01a.risknt"));
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.欄位不得為空, msg), getClass());
			}

			// 修改內容：
			// 一、目前大陸授信額度簽報預約原則:
			// 1.風險國別是CN，且非遠匯案件，大陸地區授信控管為是，且非短期貿易融資額度註記。
			// 2.風險國別非CN，大陸地區授信控管為是，且非短期貿易融資額度註記及非風險移轉。
			//
			// 二、煩請修改大陸授信額度簽報預約原則為以下:
			// 不論風險國別為何，且非遠匯案件，大陸地區授信控管為是，且非短期貿易融資額度註記及非風險移轉。
			L140M01Q l140m01q = l140m01qDao.findByMainId(l140m01a.getMainId());
			if (!Util.isEmpty(l140m01q)) {
				if (Util.equals(
						lmsService.check_CN_NeedBooking(l140m01a, l140m01q),
						"Y")) {

					tempMap.put(UtilConstants.Cntrdoc.NeedControl.大陸地區授信, "CN");

				} else {
					// 若LNF262 還有其他國別要控管，還是得加上去判斷
					if (Util.notEquals(riskArea, "CN")) {
						tempMap.put(UtilConstants.Cntrdoc.NeedControl.國別代碼,
								riskArea);
					}
				}
			}

			String areaNo = "";
			// 區域代碼
			List<Map<String, Object>> areaMap = dwdbBaseService
					.findDW_CNTRYAREAByCountryCode(riskArea);
			if (!areaMap.isEmpty()) {
				for (Map<String, Object> data : areaMap) {
					areaNo = Util.trim((String) data.get("AREA_NO"));
				}
			}

			if (!Util.isEmpty(areaNo)) {
				tempMap.put(區域代碼, areaNo);
			}

			// 單一授信戶
			tempMap.put(單一授信戶, custId);

			// 2014-06-16產品種類
			if (Util.notEquals(l140m01a.getLnType(), "")) {
				tempMap.put(產品種類, Util.trim(l140m01a.getLnType()));
			}

			returnMap.put("showMsg", "");
			for (String key : tempMap.keySet()) {
				logger.info("查詢額度限額控管檔LNF262====key{}==vaule{}=>",
						new Object[] { key, tempMap.get(key) });
				int count = misdbBASEService
						.countByLNF262_KINDAndLNF262_LOAN_NO(
								key.substring(0, 1), tempMap.get(key));
				if (count > 0) {
					Map<String, String> nameMap = codeTypeService
							.findByCodeType("lms1405s02_limitType", LocaleContextHolder.getLocale().toString());

					returnMap.put(key.substring(0, 1), tempMap.get(key)); // 把所有符合LNF262要預約的條件放到MAP，供後續檢核用

					HashMap<String, String> custMsg = new HashMap<String, String>();
					custMsg.put("rControName", nameMap.get(key));
					custMsg.put("rControVal", tempMap.get(key));
					String showMsg = "";

					// J-107-0158_05097_B1001 Web
					// e-loan企金簽案,凡屬於L534交易所設定依單一授信對象控管者,於未辦理預約額度者,增加提示"本案屬授審處控管對象,請先辦理預約額度申請,有問題請洽授審處林宇豪,分機#2716"
					if (Util.equals(key, 單一授信戶)) {
						if (showCntrNo) {
							// EFD3059=INFO|$\{rControName\}【$\{rControVal\}】屬授審處控管對象，額度序號$\{cntrNo\}請先辦理預約額度申請，有問題請洽授審處林宇豪,分機#2716。|
							custMsg.put("cntrNo", l140m01a.getCntrNo());
							showMsg = RespMsgHelper.getMessage("EFD3059", custMsg);
						} else {
							// EFD3058=INFO|
							// $\{rControName\}【$\{rControVal\}】屬授審處控管對象，請先辦理預約額度申請，有問題請洽授審處林宇豪,分機#2716。|
							showMsg = RespMsgHelper.getMessage("EFD3058", custMsg);
						}
					} else {
						if (showCntrNo) {
							// EFD3033=INFO|
							// $\{rControName\}【$\{rControVal\}】屬於授管處控管案件，額度序號$\{cntrNo\}必須於e-Loan資料建檔先行預約額度。|
							custMsg.put("cntrNo", l140m01a.getCntrNo());
							showMsg = RespMsgHelper.getMessage("EFD3033", custMsg);
						} else {
							// EFD3032=INFO|$\{rControName\}【$\{rControVal\}】屬於授管處控管案件，必須於e-Loan資料建檔先行預約額度後再引進。|
							showMsg = RespMsgHelper.getMessage("EFD3032", custMsg);
						}
					}

					returnMap.put("showMsg", showMsg);
				}
			}

			// J-110-0531_05097_B1001 企金授信簽報書新增72-2簽案預約控管
			// 72-2簽案預約****************************************************************************
			String showMsg722 = lmsService.chk722NeedElf442(l140m01a);
			if (Util.notEquals(showMsg722, "")) {
				returnMap.put("72-2", "72-2控管");
				returnMap.put("showMsg722", showMsg722);
			}

			// J-111-0163_05097_B1001 Web e-Loan企金國內、海外簽報書新增中租集團(代號1208)預約額度檢核作業
			String CLASSCODE07_GRPIDS = Util.trim(lmsService
					.getSysParamDataValue("CLASSCODE07_GRPIDS"));

			List<String> grpIds = Arrays.asList(CLASSCODE07_GRPIDS.split(","));
			if (grpIds.contains(DBELF447N_GRPNO)) {

				String showMsgGrp07 = "[INFO]EFD3033- 本案集團" + DBELF447N_GRPNO
						+ "屬於企金處控管對象，額度序號" + l140m01a.getCntrNo()
						+ "必須於e-Loan資料建檔系統-其他建檔-企金處預約額度先行預約額度";

				returnMap.put(集團代碼_資料建檔07, "企金集團預約控管");
				returnMap.put("showMsgGrp07", showMsgGrp07);
			}

			String showMsgRwaCase2 = lmsService.chkRwaCase2NeedElf442(l140m01a);
			if (Util.notEquals(showMsgRwaCase2, "")) {
				returnMap.put("RWACASE2", "企金授信RWA控管案件");
				returnMap.put("showMsgRwaCase2", showMsgRwaCase2);
			}

		}

		return returnMap;
	}

	@Override
	public String gfnDB2ChkNeedControl(L140M01A l140m01a)
			throws CapMessageException {
		String ownBrid = l140m01a.getOwnBrId();
		String custId = l140m01a.getCustId();
		String dupNo = l140m01a.getDupNo();
		String cntrNo = l140m01a.getCntrNo();
		// ownBrid = "006";
		// cntrNo = "006109800005";
		Map<String, String> returnMap = this.gfnDB2ChkNeedControlByCntrDoc(
				l140m01a, true);
		String needControl = MapUtils.getString(returnMap, "showMsg", "");
		// J-110-0455_05097_B1001 企金授信簽報書新增72-2簽案預約控管
		// J-110-0531_05097_B1001 企金授信簽報書新增72-2簽案預約控管
		String needControl722 = MapUtils.getString(returnMap, "showMsg722", "");

		// J-111-0163_05097_B1001 Web e-Loan企金國內、海外簽報書新增中租集團(代號1208)預約額度檢核作業
		String needControlGrp07 = MapUtils.getString(returnMap, "showMsgGrp07", "");

		// J-113-0374 海外企金RWA預約
		String needControlRwaCase2 = MapUtils.getString(returnMap, "showMsgRwaCase2", "");

		// J-110-0455_05097_B1001 企金授信簽報書新增72-2簽案預約控管
		// 是否需要限額控管 為空則為 否
		// J-111-0163_05097_B1001 Web e-Loan企金國內、海外簽報書新增中租集團(代號1208)預約額度檢核作業
		if (Util.isEmpty(needControl) && Util.isEmpty(needControl722)
				&& Util.isEmpty(needControlGrp07)
				&& Util.isEmpty(needControlRwaCase2)) {
			return "";
		}

		// J-110-0455_05097_B1001 企金授信簽報書新增72-2簽案預約控管
		String controlMsg = "";

		// 其他簽案預約(非72-2)
		if (Util.notEquals(needControl, "")) {
			String showMsgNot722 = this.gfnDB2ChkNeedControlForGeneral(
					l140m01a, returnMap);
			if (Util.notEquals(showMsgNot722, "")) {
				controlMsg = controlMsg
						+ (Util.equals(controlMsg, "") ? "" : "<BR>")
						+ showMsgNot722;

			}
		}

		// J-110-0455_05097_B1001 企金授信簽報書新增72-2簽案預約控管
		// 有72-2預約
		if (Util.notEquals(needControl722, "")) {
			// 72-2 跟其他預約同時並存，可能同時會有兩筆預約
			String showMsg722 = this.gfnDB2ChkNeedControlFor722(l140m01a, returnMap);
			if (Util.notEquals(showMsg722, "")) {
				controlMsg = controlMsg
						+ (Util.equals(controlMsg, "") ? "" : "<BR>")
						+ showMsg722;
			}
		}

		// J-111-0163_05097_B1001 Web e-Loan企金國內、海外簽報書新增中租集團(代號1208)預約額度檢核作業
		if (Util.notEquals(needControlGrp07, "")) {
			// 72-2 跟其他預約同時並存，可能同時會有兩筆預約
			String showMsgGrp07 = this.gfnDB2ChkNeedControlForGrp07(l140m01a, returnMap);
			if (Util.notEquals(showMsgGrp07, "")) {
				controlMsg = controlMsg
						+ (Util.equals(controlMsg, "") ? "" : "<BR>")
						+ showMsgGrp07;
			}
		}

		if (Util.notEquals(needControlRwaCase2, "")) {
			String showMsgRwaCase2 = this.gfnDB2ChkNeedControlForRwaCase2(l140m01a, returnMap);
			if (Util.notEquals(showMsgRwaCase2, "")) {
				controlMsg = controlMsg + (Util.equals(controlMsg, "") ? "" : "<BR>") + showMsgRwaCase2;
			}
		}

		if (Util.notEquals(controlMsg, "")) {
			return controlMsg;
		}

		return "";
	}

	@Override
	public void savelistL120M01C(List<L120M01C> l120m01cs) {
		l120m01cDao.save(l120m01cs);
	}

	@Override
	public void savelistL140M01E(List<L140M01E> l140m01es) {
		l140m01eDao.save(l140m01es);
	}
	
	@Override
	public void savelistL140M01E_AF(List<L140M01E_AF> l140m01e_afs) {
		l140m01e_afDao.save(l140m01e_afs);
	}

	/**
	 * 擔保品組字
	 * 
	 * @param cmsMainId
	 *            擔保品文件編號
	 * @param cmscollTyp1
	 *            擔保品種類
	 * @param l140m01o
	 *            額度明細表擔保品檔案
	 * @param brId
	 *            額度明細表簽案行
	 * @return
	 */
	private String getCMSDataFor140(String cmsMainId, String cmscollTyp1,
			L140M01O l140m01o, String brId) {
		String br = "<br/>";
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		StringBuffer str = new StringBuffer();
		String forL140M01Bstr = "";
		List<Map<String, Object>> detailList = null;
		Map<String, Object> map = null;
		Map<String, Object> map2 = null;
		Map<String, CapAjaxFormResult> allMap = codeTypeService
				.findByCodeType(new String[] { "lmsUseCms_collTyp1",
						"cms1030_collTyp2", "cms1090_collTyp2_5" });
		CapAjaxFormResult collType1Map = allMap.get("lmsUseCms_collTyp1");
		CapAjaxFormResult collType32Map = allMap.get("cms1030_collTyp2");
		CapAjaxFormResult collType52Map = allMap.get("cms1090_collTyp2_5");

		if (collType1Map == null) {
			collType1Map = new CapAjaxFormResult();
		}
		if (collType32Map == null) {
			collType32Map = new CapAjaxFormResult();
		}
		String cmsBrid = "";
		String collName = "";
		String 擔保品名稱 = prop.getProperty("l1405s02p05.001") + "：";
		String 筆數 = prop.getProperty("l1405s02c01.001");
		String 鑑估日期 = prop.getProperty("l1405s02c01.002");
		String 土地總面積 = prop.getProperty("l1405s02c01.003");
		String 建物總面積 = prop.getProperty("l1405s02c01.004");
		String 不動產面積單位 = prop.getProperty("l1405s02c01.005");
		String 坪 = prop.getProperty("l1405s02c01.006");
		String 鑑估值 = prop.getProperty("l1405s02c01.007");
		String 房地購價或時價 = prop.getProperty("l1405s02c01.008");
		String 元 = prop.getProperty("l1405s02c01.009");
		String 第 = prop.getProperty("l1405s02c01.010");
		String 順位設定金額 = prop.getProperty("l1405s02c01.011");
		String 估價 = prop.getProperty("l1405s02c01.012");
		String 押值 = prop.getProperty("l1405s02c01.013");
		String 金額單位 = prop.getProperty("l1405s02c01.014");
		String curr = "";
		Date setEstDate = null;
		str.append(擔保品名稱);
		// 不動產
		if (UtilConstants.CollTyp1.不動產.equals(cmscollTyp1)) {
			map = eloandbcmsService.getShowDataCollType1For140(cmsMainId);
			detailList = eloandbcmsService
					.getlandBuildDataCollType1For140(cmsMainId);
			if (map != null) {
				cmsBrid = Util.trim(map.get("BRANCH"));
				if (!cmsBrid.equals(brId)) {
					collName = "【" + branchService.getBranchName(cmsBrid) + "】";
				}
				collName += collType1Map.get(cmscollTyp1);
				str.append(collName).append(" - ");
				if (detailList.size() > 0) {
					map2 = detailList.get(0);
					str.append(map2.get("target"));
				}
				curr = Util.trim(map.get("CURRCD")) + " ";
				setEstDate = (Date) map.get("ESTDATE");
				// l1405s02c01.021={0}等{1}筆{2}，土地面積{3}坪，建物面積{4}坪，{5}經{6}核估押值為{7}元，時價約為{8}元，已設定第{9}順位抵押權{10}元予本行。
				forL140M01Bstr = MessageFormat
						.format(prop.getProperty("l1405s02c01.021"),
								Util.trim(map2.get("target")),
								Util.trim(map.get("COUNTNUM")),
								collType1Map.get(cmscollTyp1),
								Util.trim(NumConverter.addComma(
										map.get("LNDTAREAP"), "#,##0.00")),
								Util.trim(NumConverter.addComma(
										map.get("BLDAREAP"), "#,##0.00")),
								TWNDate.toAD(setEstDate),
								branchService.getBranchName(Util.trim(map
										.get("BRANCH"))),
								curr
										+ " "
										+ NumConverter.addComma(map
												.get("LOANAMT")),
								curr
										+ " "
										+ NumConverter.addComma(map
												.get("INAMT")),
								Util.trim(NumConverter.toChineseNumber(map
										.get("SETODR"))),
								curr
										+ " "
										+ Util.trim(NumConverter.addComma(map
												.get("SETAMT"))));
				str.append(br);

				str.append(筆數)
						.append(Util.trim(NumConverter.addComma(map
								.get("COUNTNUM")))).append(br);
				str.append(鑑估日期)
						.append(TWNDate.toAD((Date) map.get("ESTDATE")))
						.append(" ")
						.append(土地總面積)
						.append(Util.trim(NumConverter.addComma(
								map.get("LNDTAREAP"), "#,##0.00"))).append(br);
				str.append(建物總面積)
						.append(NumConverter.addComma(map.get("BLDAREAP"),
								"#,##0.00")).append(" ").append(不動產面積單位)
						.append(坪).append(br);

				str.append(鑑估值)
						.append(NumConverter.addComma(map.get("OPTION2")))
						.append(元).append(" ").append(房地購價或時價)
						.append(NumConverter.addComma(map.get("INAMT")))
						.append(元).append(br);

				str.append(第)
						.append(Util.trim(NumConverter.toChineseNumber(map
								.get("SETODR")))).append(順位設定金額)
						.append(NumConverter.addComma(map.get("SETAMT")))
						.append(元).append(" ").append(金額單位)
						.append(Util.trim(map.get("CURRCD"))).append(" ")
						.append(元);
			}
		} else {
			// (5)【忠孝分行】動產共4筆，估值：新台幣40,303,283元，押值：新台幣28,212,298元
			map = eloandbcmsService.getShowDataFor140(cmsMainId);
			curr = Util.trim(map.get("CURRCD")) + " ";
			cmsBrid = Util.trim(map.get("BRANCH"));
			setEstDate = (Date) map.get("ESTDATE");
			if (!cmsBrid.equals(brId)) {
				collName = "【" + branchService.getBranchName(cmsBrid) + "】";
			}
			if (UtilConstants.CollTyp1.權利質權.equals(cmscollTyp1)) {
				collName += collType1Map.get(cmscollTyp1) + " - "
						+ collType32Map.get(Util.trim(map.get("COLLTYP2")));
			} else if (UtilConstants.CollTyp1.保證.equals(cmscollTyp1)) {
				collName += collType1Map.get(cmscollTyp1) + " - "
						+ collType52Map.get(Util.trim(map.get("COLLTYP2")));
			} else {
				collName += collType1Map.get(cmscollTyp1);
			}

			str.append(collName).append(br);
			str.append(筆數)
					.append(NumConverter.addComma(Util.trim(map.get("LNDNUM"))))
					.append(" ");
			str.append(估價).append(NumConverter.addComma(map.get("APPAMT")))
					.append(" ");
			str.append(押值).append(NumConverter.addComma(map.get("LOANAMT")))
					.append(br);
			str.append(元).append(" ").append(金額單位)
					.append(Util.trim(map.get("CURRCD"))).append(" ").append(元);
			// l1405s02c01.020={0}共{1}筆，估值：{2}元，押值：{3}元。
			forL140M01Bstr = MessageFormat
					.format(prop.getProperty("l1405s02c01.020"),
							new Object[] {
									collName,
									NumConverter.addComma(Util.trim(map
											.get("LNDNUM"))),
									curr
											+ NumConverter.addComma(map
													.get("APPAMT")),
									curr
											+ NumConverter.addComma(map
													.get("LOANAMT")) });
		}
		l140m01o.setBuild(forL140M01Bstr);
		l140m01o.setCmsDesc(str.toString());
		return str.toString();
	}

	@Override
	public String inculdeL140M01O(String mainId, String[] oids)
			throws CapMessageException {
		L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
		List<L140M01O> l140m01os = l140m01oDao.findByMainId(mainId);
		List<String> checkL140m01O = new ArrayList<String>();
		int seqNo = 0;
		// 用來檢查是否已經引進相同條件擔保品[分行別,客戶統編 ,重覆序號,擔保品大類]
		for (L140M01O l140m01o : l140m01os) {
			seqNo = l140m01o.getSeqNo() > seqNo ? l140m01o.getSeqNo() : seqNo;
			checkL140m01O.add(Util.trim(l140m01o.getBranch()) + "^"
					+ Util.trim(l140m01o.getCustId()) + "^"
					+ Util.trim(l140m01o.getDupNo()) + "^"
					+ Util.trim(l140m01o.getCollTyp1()));
		}
		List<C100M01> c100m01s = c100m01Dao.findByOids(oids);
		List<L140M01O> newl140m01os = new ArrayList<L140M01O>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (C100M01 c100m01 : c100m01s) {
			if (!checkL140m01O.contains(Util.trim(c100m01.getBranch()) + "^"
					+ Util.trim(c100m01.getCustId()) + "^"
					+ Util.trim(c100m01.getDupNo()) + "^"
					+ Util.trim(c100m01.getCollTyp1()))) {
				L140M01O l140m01o = new L140M01O();
				try {
					CapBeanUtil.copyBean(c100m01, l140m01o, new String[] {
							"branch", "estBrn", "estDate", "docStatus",
							"loanTwd", "custName", "dupNo", "custId", "collNo",
							"collTyp1" });
				} catch (CapException e) {
					logger.error("[inculdeL140M01O]copyBean Exception", e);
				}
				l140m01o.setMainId(mainId);
				l140m01o.setCreateTime(CapDate.getCurrentTimestamp());
				l140m01o.setCreator(user.getUserId());
				l140m01o.setCmsOid(c100m01.getOid());
				// J-110-0485_05097_B1001 於簽報書新增LGD欄位
				l140m01o.setCmsCollKey(c100m01.getCollKey());
				seqNo++;
				l140m01o.setSeqNo(seqNo);
				this.getCMSDataFor140(c100m01.getMainId(),
						c100m01.getCollTyp1(), l140m01o, l140m01a.getOwnBrId());
				newl140m01os.add(l140m01o);

			} else {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1405S02Panel.class);
				// l1405s02c01.019=已有相同擔保品請刪除後再執行引進
				String msg = prop.getProperty("l1405s02c01.019");
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
			}
		}
		l140m01oDao.save(newl140m01os);
		return this.reloadCMSDesc(mainId);
	}

	@Override
	public String deleteL140M01O(String[] oids, String mainId) {
		List<L140M01O> l140m01os = l140m01oDao.findByOids(oids);
		l140m01oDao.delete(l140m01os);
		L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
		l140m01a.setChkYN(null);
		this.save(l140m01a);
		return this.reloadCMSDesc(mainId);
	}

	@Override
	public String reloadCMSDesc(String mainId) {

		L140M01B l140m01b = l140m01bDao.findByUniqueKey(mainId,
				UtilConstants.Cntrdoc.l140m01bItemType.擔保品);
		StringBuffer dscr = new StringBuffer();
		// l1405s02c01.022=核貸成數
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		String mess = prop.getProperty("l1405s02c01.022");
		List<L140M01O> l140m01os = l140m01oDao.findByMainId(mainId);
		int count = 0;
		for (L140M01O l140m01o : l140m01os) {
			count++;
			dscr.append(dscr.length() > 0 ? "<br/>" : "");
			dscr.append("(");
			dscr.append(count);
			dscr.append(")");
			dscr.append(l140m01o.getBuild());
			if (l140m01o.getPayPercent() != null) {
				dscr.append(mess);
				dscr.append(" ");
				dscr.append(l140m01o.getPayPercent());
				dscr.append(" ％。");
			}
		}
		String title = "";
		if (dscr.length() > 0) {
			// l1405s02c01.024=提供
			title = prop.getProperty("l1405s02c01.024") + "<br/>";
		}
		l140m01b.setItemDscr(title + dscr.toString());
		l140m01bDao.save(l140m01b);
		return title + dscr.toString();
	}

	@Override
	public L140M01Q findL140m01qByMainId(String mainId) {
		return l140m01qDao.findByMainId(mainId);
	}

	@Override
	public String deleteL140M01J(String[] oids, String mainId) {
		List<L140M01J> l140m01js = l140m01jDao.findByOids(oids);
		l140m01jDao.delete(l140m01js);
		L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
		List<L140M01J> newl140m01j = l140m01jDao.findByMainId(mainId);
		String l140m01jStr = this.getL140M01JStr(newl140m01j);
		l140m01a.setL140m01jStr(l140m01jStr);
		l140m01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
		l140m01aDao.save(l140m01a);
		return l140m01jStr;
	}

	@Override
	public String inculdeL140M01J(String[] oids, String mainId, String custPos) throws CapMessageException {

		L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
		if (l140m01a != null) {
			L120M01C l120m01c = l120m01cDao.findoneByRefMainId(l140m01a
					.getMainId());
			if (l120m01c != null) {
				L120M01A l120m01a = l120m01aDao.findByMainId(l120m01c
						.getMainId());
				if (l120m01a != null) {
					if (LMSUtil.isOverSea_CLS(l120m01a)) {
						return _inculdeL140M01J_docType2(oids, l120m01a,
								l140m01a, custPos);
					} else {
						return _inculdeL140M01J_docType1(oids, l120m01a,
								l140m01a, custPos);
					}
				}
			}
		}
		return "";
	}

	/** 企金 */
	private String _inculdeL140M01J_docType1(String[] oids, L120M01A l120m01a,
			L140M01A l140m01a, String custPos)
			throws CapMessageException {
		List<L120S01A> l120s01as = l120s01aDao.findByOids(oids);
		HashMap<String, String> custCounty = new HashMap<String, String>();

		if (l120s01as.size() > 0) {
			List<L120S01B> l120s01bs = l120s01bDao.findByMainId(l120m01a
					.getMainId());
			StringBuilder custIdKey = new StringBuilder("");
			for (L120S01B l120s01b : l120s01bs) {
				custIdKey.append(l120s01b.getCustId()).append(
						l120s01b.getDupNo());
				custCounty.put(custIdKey.toString(),
						Util.trim(l120s01b.getNtCode()));
				custIdKey.setLength(0);
			}
		}

		List<L140M01J> l140m01js = l140m01jDao.findByMainId(l140m01a
				.getMainId());
		List<String> checkL140m01j = new ArrayList<String>();
		List<L140M01J> newl140m01js = new ArrayList<L140M01J>();
		for (L140M01J l140m01j : l140m01js) {
			checkL140m01j.add(Util.trim(l140m01j.getCustId())
					+ Util.trim(l140m01j.getDupNo()));
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L120S01A l120s01a : l120s01as) {
			if (!checkL140m01j.contains(Util.trim(l120s01a.getCustId())
					+ Util.trim(l120s01a.getDupNo()))) {
				L140M01J l140m01j = new L140M01J();
				try {
					CapBeanUtil.copyBean(l120s01a, l140m01j, new String[] {
							"custId", "dupNo", "dupNo", "custName" });
				} catch (CapException e) {
					logger.error("[inculdeL140M01J]copyBean Exception", e);
				}
				l140m01j.setMainId(l140m01a.getMainId());
				l140m01j.setCreateTime(CapDate.getCurrentTimestamp());
				l140m01j.setCreator(user.getUserId());
				l140m01j.setNtCode(Util.trim(custCounty.get(l120s01a
						.getCustId() + l120s01a.getDupNo())));
				l140m01j.setCustPos(custPos);
				newl140m01js.add(l140m01j);
			} else {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMSCommomPage.class);
				// message.001=已有相同借款人 {0} 請刪除後在執行引進
				String msg = MessageFormat.format(
						prop.getProperty("message.001"),
						new Object[] { Util.trim(l120s01a.getCustId())
								+ " "
								+ Util.trim(l120s01a.getDupNo() + " "
										+ l120s01a.getCustName()) });
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
			}
		}
		l140m01jDao.save(newl140m01js);
		List<L140M01J> newl140m01j = l140m01jDao.findByMainId(l140m01a
				.getMainId());
		String l140m01jStr = this.getL140M01JStr(newl140m01j);
		l140m01a.setL140m01jStr(l140m01jStr);
		l140m01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
		l140m01aDao.save(l140m01a);
		return l140m01jStr;
	}

	/** 消金 */
	private String _inculdeL140M01J_docType2(String[] oids, L120M01A l120m01a,
			L140M01A l140m01a, String custPos)
			throws CapMessageException {
		List<C120M01A> c120m01as = c120m01aDao.findByOids(oids);
		HashMap<String, String> custCounty = new HashMap<String, String>();

		if (c120m01as.size() > 0) {
			List<C120S01A> c120s01as = c120s01aDao.findByMainId(l120m01a
					.getMainId());
			StringBuilder custIdKey = new StringBuilder("");
			for (C120S01A c120s01a : c120s01as) {
				custIdKey.append(c120s01a.getCustId()).append(
						c120s01a.getDupNo());
				custCounty.put(custIdKey.toString(),
						Util.trim(c120s01a.getNtCode()));
				custIdKey.setLength(0);
			}
		}

		List<L140M01J> l140m01js = l140m01jDao.findByMainId(l140m01a
				.getMainId());
		List<String> checkL140m01j = new ArrayList<String>();
		List<L140M01J> newl140m01js = new ArrayList<L140M01J>();
		for (L140M01J l140m01j : l140m01js) {
			checkL140m01j.add(Util.trim(l140m01j.getCustId())
					+ Util.trim(l140m01j.getDupNo()));
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (C120M01A c120m01a : c120m01as) {
			if (!checkL140m01j.contains(Util.trim(c120m01a.getCustId())
					+ Util.trim(c120m01a.getDupNo()))) {
				L140M01J l140m01j = new L140M01J();
				if (true) {
					l140m01j.setCustId(c120m01a.getCustId());
					l140m01j.setDupNo(c120m01a.getDupNo());
					l140m01j.setCustName(c120m01a.getCustName());
				}

				l140m01j.setMainId(l140m01a.getMainId());
				l140m01j.setCreateTime(CapDate.getCurrentTimestamp());
				l140m01j.setCreator(user.getUserId());
				l140m01j.setNtCode(Util.trim(custCounty.get(c120m01a
						.getCustId() + c120m01a.getDupNo())));
				l140m01j.setCustPos(custPos);
				newl140m01js.add(l140m01j);
			} else {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMSCommomPage.class);
				// message.001=已有相同借款人 {0} 請刪除後在執行引進
				String msg = MessageFormat.format(
						prop.getProperty("message.001"),
						new Object[] { Util.trim(c120m01a.getCustId())
								+ " "
								+ Util.trim(c120m01a.getDupNo() + " "
										+ c120m01a.getCustName()) });
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
			}
		}
		l140m01jDao.save(newl140m01js);
		List<L140M01J> newl140m01j = l140m01jDao.findByMainId(l140m01a
				.getMainId());
		String l140m01jStr = this.getL140M01JStr(newl140m01j);
		l140m01a.setL140m01jStr(l140m01jStr);
		l140m01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
		l140m01aDao.save(l140m01a);
		return l140m01jStr;
	}

	@Override
	public String getL140M01JStr(List<L140M01J> l140m01js) {
		StringBuffer temp = new StringBuffer();
		int comBorrCount = 0;
		boolean needIndex = false;

		if (l140m01js != null && !l140m01js.isEmpty()) {
			if (l140m01js.size() > 1) {
				needIndex = true;
			}
		}
		for (L140M01J l140m01j : l140m01js) {
			temp.append(temp.length() > 0 ? "、" : "");
			comBorrCount = comBorrCount + 1;
			if (needIndex == true) {
				temp.append(comBorrCount + ".");
			}
			temp.append(l140m01j.getCustName());
		}
		return temp.toString();
	}

	@Override
	public String getL140M01JStr(String mainId) {
		List<L140M01J> l140m01js = l140m01jDao.findByMainId(mainId);
		return this.getL140M01JStr(l140m01js);
	}

	@Override
	public HashMap<String, String> getCustCounty(String mainId) {
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);
		Map<String, String> codeMap = codeTypeService
				.findByCodeType(CodeTypeEnum.國家代碼.getCode());
		StringBuilder custIdKey = new StringBuilder("");
		HashMap<String, String> custCounty = new HashMap<String, String>();
		// 1企金 2個金 、取得借款人國別
		if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a.getDocType())) {
			List<L120S01B> l120s01bs = l120s01bDao.findByMainId(l120m01a
					.getMainId());
			for (L120S01B l120s01b : l120s01bs) {
				custIdKey.append(l120s01b.getCustId()).append(
						l120s01b.getDupNo());
				custCounty.put(custIdKey.toString(),
						Util.trim(codeMap.get(l120s01b.getNtCode())));
				custIdKey.setLength(0);
			}
		} else if (UtilConstants.Casedoc.DocType.個金.equals(l120m01a
				.getDocType())) {
			List<C120S01A> c120s01as = c120s01aDao.findByMainId(l120m01a
					.getMainId());
			for (C120S01A c120s01a : c120s01as) {
				custIdKey.append(c120s01a.getCustId()).append(
						c120s01a.getDupNo());
				custCounty.put(custIdKey.toString(),
						Util.trim(codeMap.get(c120s01a.getNtCode())));
				custIdKey.setLength(0);
			}
		}
		return custCounty;

	}

	/**
	 * 重新引進 更新該份額度明細表 姓名 案號
	 * 
	 * @param caseMainId
	 *            caseMainId
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@Override
	public void reloadCustName(String caseMainId)
			throws CapMessageException {
		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		if (l120m01a == null || Util.isEmpty(Util.trim(l120m01a.getCaseNo()))) {
			// EFD2180=請先儲存後再執行此功能
			throw new CapMessageException(RespMsgHelper.getMessage("EFD2180"), getClass());
		}

		HashMap<String, String> custNameMap = new HashMap<String, String>();
		if (true) {
			// 先把借款人custId + dupNo 當Key 把名子存起來
			if (LMSUtil.isOverSea_CLS(l120m01a)) {
				List<C120M01A> list = c120m01aDao.findByMainId(caseMainId);
				for (C120M01A c120m01a : list) {
					custNameMap.put(c120m01a.getCustId() + c120m01a.getDupNo(),
							c120m01a.getCustName());
				}
			} else {
				List<L120S01A> list = l120s01aDao.findByMainId(caseMainId);
				for (L120S01A l120s01a : list) {
					custNameMap.put(l120s01a.getCustId() + l120s01a.getDupNo(),
							l120s01a.getCustName());
				}

			}
		}
		if (MapUtils.isEmpty(custNameMap)) {
			HashMap<String, String> msg = new HashMap<String, String>();
			// L140M01a.error01 = 請先登錄借款人資料
			msg.put("msg", prop.getProperty("L140M01a.error01"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}

		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId,
						UtilConstants.Cntrdoc.ItemType.額度明細表, null);
		// if (l140m01as == null || l140m01as.isEmpty()) {
		// HashMap<String, String> msg = new HashMap<String, String>();
		// // L140M01a.error38=尚未登錄額度明細表，無法執行此動作
		// msg.put("msg", prop.getProperty("L140M01a.error38"));
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		// }

		HashMap<String, String> custCounty = new HashMap<String, String>();
		StringBuilder custIdKey = new StringBuilder("");
		// 1企金 2個金 、取得借款人國別
		List<L120S01B> l120s01bs = l120s01bDao.findByMainId(l120m01a
				.getMainId());
		for (L120S01B l120s01b : l120s01bs) {
			custIdKey.append(l120s01b.getCustId()).append(l120s01b.getDupNo());
			custCounty.put(custIdKey.toString(),
					Util.trim(l120s01b.getNtCode()));
			custIdKey.setLength(0);
		}

		if (l140m01as != null && !l140m01as.isEmpty()) {

			String custKey = "";
			String l140m01jKey = "";
			List<L140M01J> newL140m01js = new ArrayList<L140M01J>();
			for (L140M01A l140m01a : l140m01as) {
				List<L140M01J> l140m01js = l140m01jDao.findByMainId(l140m01a
						.getMainId());
				for (L140M01J l140m01j : l140m01js) {
					l140m01jKey = l140m01j.getCustId() + l140m01j.getDupNo();
					if (custNameMap.containsKey(l140m01jKey)) {
						l140m01j.setCustName(custNameMap.get(l140m01jKey));
					}
					if (custCounty.containsKey(l140m01jKey)) {
						l140m01j.setNtCode(custCounty.get(l140m01jKey));
					}
				}
				newL140m01js.addAll(l140m01js);

				String l140m01jStr = this.getL140M01JStr(l140m01js);
				l140m01jStr = l140m01js.size() == 0 ? Util.trim(prop
						.getProperty("nohave")) : l140m01jStr;
				l140m01a.setL140m01jStr(l140m01jStr);
				custKey = l140m01a.getCustId() + l140m01a.getDupNo();
				if (custNameMap.containsKey(custKey)) {
					l140m01a.setCustName(custNameMap.get(custKey));
				}
				// 只更新非聯行轉入的案號 和簽案日期
				if (!UtilConstants.Cntrdoc.DataSrc.轉入額度明細表.equals(l140m01a
						.getDataSrc())) {
					l140m01a.setCaseNo(l120m01a.getCaseNo());
					l140m01a.setCaseDate(l120m01a.getCaseDate());
				}
			}
			l140m01aDao.save(l140m01as);
			l140m01jDao.save(newL140m01js);
		}
	}

	@Override
	public L140M01C findL140m01cByOid(String oid) {
		return l140m01cDao.findByOid(oid);
	}

	/**
	 * 調整科目顯示順序
	 * 重要!!執行此功能前記得先呼叫resetL140M01CAllSeqNum已確保所有科目都有塞seqNum(因為舊案沒有seqNum)
	 */
	@Override
	public boolean changeSeqNum(L140M01C l140m01c, boolean upOrDown) {

		ISearch search = l140m01cDao.createSearchTemplete();
		// 不能在這邊呼叫resetL140M01CAllSeqNum，因為l140m01c.getSeqNum()可能是舊的值，所以要再取得l140m01c前呼叫
		// search.addSearchModeParameters(SearchMode.EQUALS,
		// EloanConstants.MAIN_ID, l140m01c.getMainId());
		// List<L140M01C> chkL140m01cs = l140m01cDao.find(search);
		// if (!(Util.isEmpty(chkL140m01cs) || chkL140m01cs == null)){
		// boolean setFlag = resetL140M01CAllSeqNum(l140m01c);
		// if(setFlag == false){
		// return setFlag;
		// }
		// }
		//
		search = l140m01cDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, l140m01c.getMainId());
		if (upOrDown) {
			search.addSearchModeParameters(SearchMode.LESS_EQUALS, "seqNum",
					l140m01c.getSeqNum());
			search.addOrderBy("seqNum", true);
		} else {
			search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "seqNum",
					l140m01c.getSeqNum());
			search.addOrderBy("seqNum", false);
		}

		List<L140M01C> l140m01cs = l140m01cDao.find(search);

		if (Util.isEmpty(l140m01cs) || l140m01cs.size() < 2) {
			return false;
		} else {
			L140M01C l140m01cFocus = l140m01cs.get(0);
			L140M01C l140m01cLess = l140m01cs.get(1);

			Integer focusSeq = l140m01cFocus.getSeqNum();
			if (focusSeq == l140m01cLess.getSeqNum()) {
				if (upOrDown) {
					focusSeq = focusSeq + 1;
				} else {
					focusSeq = focusSeq - 1;
				}
			}
			l140m01cFocus.setSeqNum(l140m01cLess.getSeqNum());
			l140m01cLess.setSeqNum(focusSeq);

			save(l140m01cFocus, l140m01cLess);
			return true;
		}

	}

	@Override
	public boolean resetL140M01CAllSeqNum(String mainId) {

		ISearch search = l140m01cDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);

		search.addOrderBy("seqNum", false);
		search.addOrderBy("createTime", false);
		search.addOrderBy("subjSeq", false);

		List<L140M01C> l140m01cs = l140m01cDao.find(search);
		Integer focusSeq = 0;
		if (Util.isEmpty(l140m01cs)) {
			return true;
		} else {
			for (L140M01C tL140M01C : l140m01cs) {
				focusSeq = focusSeq + 1;
				tL140M01C.setSeqNum(focusSeq);

			}
			l140m01cDao.save(l140m01cs);
			return true;
		}

	}

	@Override
	public List<L140M01A> findL140m01aListByainIdCustIdCntrno(String mainId,
			String custId, String dupNo, String cntrNo) {
		return l140m01aDao.findByMainIdCustIdCntrno(mainId, custId, dupNo,
				cntrNo);
	}

	@Override
	public String build_l140m01cAdoptGrade(L140M01C l140m01c) {
		String c121MainId = Util.trim(l140m01c.getC121MainId());
		String c121CustId = Util.trim(l140m01c.getC121CustId());
		String c121DupNo = Util.trim(l140m01c.getC121DupNo());
		String grade = Util.trim(l140m01c.getGrade());
		String modelType = Util.trim(l140m01c.getModelType());
		return build_l140m01cAdoptGrade(c121MainId, c121CustId, c121DupNo,
				grade, modelType);
	}

	@Override
	public String build_l140m01cAdoptGrade(String c121MainId,
			String c121CustId, String c121DupNo, String grade, String modelType) {
		String c121RelateMsg = "";
		if (true) {
			String c121CaseNo = "";
			String c121Name = "";
			String c121CustPos = "";
			String c121MowType = "";
			String c121O_grade = "";
			String varVer = "1.0";
			if (Util.isNotEmpty(c121MainId)) {
				C121M01A c121m01a = clsService.findC121M01AByMainId(c121MainId);
				if (c121m01a != null) {
					c121CaseNo = Util.trim(c121m01a.getCaseNo());
					C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(
							c121MainId, c121CustId, c121DupNo);
					if (c120m01a != null) {
						c121Name = Util.trim(c120m01a.getCustName());
						String custPos = Util.equals(c120m01a.getKeyMan(), "Y") ? OverSeaUtil.M
								: Util.trim(c120m01a.getCustPos());
						if (Util.isNotEmpty(custPos)) {
							CodeType codeType = codeTypeService
									.findByCodeTypeAndCodeValue(
											"lms1015_custPos", custPos);
							if (codeType != null) {
								c121CustPos = Util.trim(codeType.getCodeDesc());
							}
						}
						C120S01A c120s01a = clsService.findC120S01A(c120m01a);
						if (c120s01a != null) {
							c121O_grade = Util.trim(c120s01a.getO_grade());
						}
					}
					c121MowType = c121m01a.getMowType();
					varVer = c121m01a.getVarVer();
				}
			}

			if (Util.isEmpty(grade)) {
				c121RelateMsg = "N/A";
			} else {
				String msgFmt = "1";
				if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, c121MowType)) {
					msgFmt = "3";
				} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲,
						c121MowType)) {
					msgFmt = "4";
				} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國,
						c121MowType)) {
					msgFmt = "2";
				}
				// ==========
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1405S02Page.class);
				// ==========
				if (Util.equals("1", msgFmt)) { //預設
					String pattern = prop
							.getProperty("other.l140m01c_loanTP_adopt_grade");
					c121RelateMsg = MessageFormat
							.format(pattern, c121CaseNo, c121CustPos,
									c121CustId, c121DupNo, c121Name, grade);
				} else if (Util.equals("2", msgFmt)) { //泰國
					Properties propAbstractOverSeaCLSPage = MessageBundleScriptCreator
							.getComponentResource(AbstractOverSeaCLSPage.class);
					String idvLabel = propAbstractOverSeaCLSPage
							.getProperty("l120s02.index94");
					String str_1 = MessageFormat
					.format(prop
							.getProperty("other.l140m01c_loanTP_adopt_gradeTH_1"),
							idvLabel, c121O_grade, c121CustPos,
							c121CustId, c121DupNo, c121Name);
					String str_2 = "";
					
					if (Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)) {
						String modelTypeS = "";
						if (Util.equals(modelType, OverSeaUtil.海外評等_房貸)) {
							modelTypeS = prop.getProperty("L140m01c.mortgage");
						}else{
							modelTypeS = prop.getProperty("L140m01c.nonMortgage");
						}
						str_2 = MessageFormat
						.format(prop
								.getProperty("other.l140m01c_loanTP_adopt_gradeTH_3"),
								c121CaseNo, c121CustPos, c121CustId,
								c121DupNo, c121Name, modelTypeS,grade);
					}else{
						str_2 = MessageFormat
						.format(prop
								.getProperty("other.l140m01c_loanTP_adopt_gradeTH_2"),
								c121CaseNo, c121CustPos, c121CustId,
								c121DupNo, c121Name, grade);
					}
					c121RelateMsg = str_1 + SEP_SIGN_TEXT + str_2;
				} else if (Util.equals("3", msgFmt)) {
					// 日本模型，拆出來
					if (Util.equals(varVer, OverSeaUtil.V2_0_LOAN_JP)) {
						if (Util.equals(modelType, OverSeaUtil.海外評等_房貸)) {
							String pattern = prop
									.getProperty("other.l140m01c_loanTP_adopt_grade_2_0");
							c121RelateMsg = MessageFormat.format(pattern,
									c121CaseNo, c121CustPos, c121CustId,
									c121DupNo, c121Name,
									prop.getProperty("L140m01c.mortgage"),
									grade);
						} else {
							String pattern = prop
									.getProperty("other.l140m01c_loanTP_adopt_grade_2_0");
							c121RelateMsg = MessageFormat.format(pattern,
									c121CaseNo, c121CustPos, c121CustId,
									c121DupNo, c121Name,
									prop.getProperty("L140m01c.nonMortgage"),
									grade);
						}
					} else { // 原始1.0版本
						String pattern = prop
								.getProperty("other.l140m01c_loanTP_adopt_grade");
						c121RelateMsg = MessageFormat.format(pattern,
								c121CaseNo, c121CustPos, c121CustId, c121DupNo,
								c121Name, grade);
					}
				}else if(Util.equals("4", msgFmt)){ //澳洲模型，拆出來
					if (Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU)) {
						if (Util.equals(modelType, OverSeaUtil.海外評等_房貸)) {
							String pattern = prop
									.getProperty("other.l140m01c_loanTP_adopt_grade_2_0");
							c121RelateMsg = MessageFormat.format(pattern,
									c121CaseNo, c121CustPos, c121CustId,
									c121DupNo, c121Name,
									prop.getProperty("L140m01c.mortgage"),
									grade);
						} else {
							String pattern = prop
									.getProperty("other.l140m01c_loanTP_adopt_grade_2_0");
							c121RelateMsg = MessageFormat.format(pattern,
									c121CaseNo, c121CustPos, c121CustId,
									c121DupNo, c121Name,
									prop.getProperty("L140m01c.nonMortgage"),
									grade);
						}
					}else{
						String pattern = prop
						.getProperty("other.l140m01c_loanTP_adopt_grade");
					c121RelateMsg = MessageFormat.format(pattern,
						c121CaseNo, c121CustPos, c121CustId, c121DupNo,
						c121Name, grade);
					}
				}
			}
		}
		return c121RelateMsg;
	}
	
	@Override
	public String build_LastGradeForTH(String c121MainId,
			String c121CustId, String c121DupNo, String grade, String modelType) {
		String c121RelateMsg = "";
		if (true) {
			String c121CaseNo = "";
			String c121Name = "";
			String c121CustPos = "";
			String c121MowType = "";
			String varVer = "1.0";
			String ratingDate = "";
			
			if (Util.isNotEmpty(c121MainId)) {
				C121M01A c121m01a = clsService.findC121M01AByMainId(c121MainId);
				if (c121m01a != null) {
					c121CaseNo = Util.trim(c121m01a.getCaseNo());
					C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(
							c121MainId, c121CustId, c121DupNo);
					if (c120m01a != null) {
						c121Name = Util.trim(c120m01a.getCustName());
						String custPos = Util.equals(c120m01a.getKeyMan(), "Y") ? OverSeaUtil.M
								: Util.trim(c120m01a.getCustPos());
						if (Util.isNotEmpty(custPos)) {
							CodeType codeType = codeTypeService
									.findByCodeTypeAndCodeValue(
											"lms1015_custPos", custPos);
							if (codeType != null) {
								c121CustPos = Util.trim(codeType.getCodeDesc());
							}
						}
					}
					c121MowType = c121m01a.getMowType();
					varVer = c121m01a.getVarVer();
					ratingDate = CapDate.formatDate(c121m01a.getRatingDate(), "yyyy/MM/dd");
				}
			}
			if (Util.isEmpty(grade)) {
				c121RelateMsg = "N/A";
			} else {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1405S02Page.class);
				// ==========
					Properties propAbstractOverSeaCLSPage = MessageBundleScriptCreator
							.getComponentResource(AbstractOverSeaCLSPage.class);
//					String idvLabel = propAbstractOverSeaCLSPage
//							.getProperty("l120s02.index94");
					
					String last_gread = MessageFormat
							.format(prop
									.getProperty("other.l140m01c_loanTP_adopt_gradeTH_Last"),
									c121CaseNo, grade, ratingDate);

					c121RelateMsg = last_gread;
				
			}
		}
		return c121RelateMsg;
	}
	
	@Override
	public String build_l140m01aAdoptGrade(L140M01A l140m01a) {
		return build_l140m01aAdoptGrade(l140m01a, "、");
	}

	@Override
	public String build_l140m01aAdoptGrade(L140M01A l140m01a, String sepSign) {
		// 已在此 method 內order by seqNum ...
		List<L140M01C> l140m01cs = findL140m01cListByMainId(l140m01a
				.getMainId());
		StringBuilder tempGrade = new StringBuilder("");	
		if (Util.equals("<br/>", sepSign)) {
			tempGrade.append("<table border='0'>");
			int intValue = 65;
			for (L140M01C l140m01c : l140m01cs) {
				char ch1 = (char) intValue;

				if (true) {
					tempGrade.append("<tr>");
					tempGrade
							.append("<td style='vertical-align:top;border:0'>")
							.append(ch1).append(".").append("</td>");
					tempGrade
							.append("<td style='vertical-align:top;border:0'>")
							.append(build_l140m01cAdoptGrade(l140m01c).replaceAll(SEP_SIGN_TEXT, "<br/>"));
					if(LMSUtil.get_TH_BRNO_SET().contains(l140m01a.getOwnBrId())){//泰國模型，如果沒分數要加註前一次的評等
						String grade = Util.trim(l140m01c.getGrade());
						if((Util.isEmpty(grade))){
							tempGrade
									.append(getLastRating(l140m01a, l140m01c).replaceAll(SEP_SIGN_TEXT, "<br/>"));
						}
					}
									
					tempGrade
							.append("</td>");
					tempGrade.append("</tr>");
				}
				intValue++;
			}
			tempGrade.append("</table>");
		} else {
			int intValue = 65;
			for (L140M01C l140m01c : l140m01cs) {
				char ch1 = (char) intValue;

				if (true) {
					tempGrade.append(tempGrade.length() > 0 ? sepSign : "");
					tempGrade.append(ch1).append(".");
					tempGrade.append(build_l140m01cAdoptGrade(l140m01c));
					
					if(LMSUtil.get_TH_BRNO_SET().contains(l140m01a.getOwnBrId())){//泰國模型，如果沒分數要加註前一次的評等
						String grade = Util.trim(l140m01c.getGrade());
						if((Util.isEmpty(grade))){
							tempGrade
									.append(getLastRating(l140m01a, l140m01c).replaceAll(SEP_SIGN_TEXT, "<br/>"));
						}
					}
				}
				intValue++;
			}
		}
		return tempGrade.toString();
	}
	
	private String getLastRating(L140M01A l140m01a, L140M01C l140m01c){
		//當筆無資料，開始往前找
		//由於AS400的邏輯為 >> 抓最新一筆資料 (依照客戶ID、會計科目、額度序號)
		//因不確定會不會有不變的複製，會導致前一筆也是空白
		String reMessage = "";
		String loanTP = Util.trim(l140m01c.getLoanTP());
		List<L140M01A> l140m01as = l140m01aDao.findL140m01aListBycntrNo_orderByCT(l140m01a.getCntrNo(),l140m01a.getCustId(),l140m01a.getDupNo());
		outer:for (L140M01A l140m01aScr : l140m01as) {
			String mainid = Util.trim(l140m01aScr.getMainId());
			//比對會科
			List<L140M01C> l140m01cs = findL140m01cListByMainId(mainid);
			for (L140M01C l140m01cScr : l140m01cs) {
				String grade = Util.trim(l140m01cScr.getGrade());
				if((!Util.isEmpty(grade))){ //有分數，代表該筆有引評等
					if(Util.equals(Util.trim(l140m01cScr.getLoanTP()), loanTP)){
						//科目一樣 >> 使用該筆資料
						String c121MainId = Util.trim(l140m01cScr.getC121MainId());
						String c121CustId = Util.trim(l140m01cScr.getC121CustId());
						String c121DupNo = Util.trim(l140m01cScr.getC121DupNo());
						String modelType = Util.trim(l140m01cScr.getModelType());
						reMessage = build_LastGradeForTH(c121MainId, c121CustId, c121DupNo,
								grade, modelType);
						break outer; //跳出
					}
				}
			}
		}
		return reMessage;
	}

	/**
	 * {key:idDup, val:[custName, ntCode, custRlt, busCode]}
	 */
	private Map<String, String[]> _filter_c120m01a_idDup_with_custPos(
			List<C120M01A> c120m01a_list, String lngeFlag) {
		Map<String, String[]> r = new LinkedHashMap<String, String[]>();
		for (C120M01A c120m01a : c120m01a_list) {
			if (Util.equals(lngeFlag, c120m01a.getCustPos())) {
				String idDup = LMSUtil.getCustKey_len10custId(
						c120m01a.getCustId(), c120m01a.getDupNo());
				String custName = Util.trim(c120m01a.getCustName());
				String ntCode = "";
				String busCode = "";
				C120S01A c120s01a = clsService.findC120S01A(c120m01a);
				if (c120s01a != null) {
					ntCode = Util.trim(c120s01a.getNtCode());
					busCode = Util.trim(c120s01a.getBusCode());
				}
				String custRlt = Util.trim(c120m01a.getO_custRlt());
				r.put(idDup,
						new String[] { custName, ntCode, custRlt, busCode });
			}
		}
		return r;
	}

	/**
	 * {key:rKindD, val:rKindM]}
	 */
	private Map<String, String> _get_rKindM_from_rKindD() {
		Map<String, String> r = new HashMap<String, String>();

		if (true) {
			String rKindM = "1";
			Map<String, String> map = codeTypeService
					.findByCodeType(UtilConstants.CodeTypeItem.企業關係);
			for (String k : map.keySet()) {
				String rKindD = Util.trim(k);
				r.put(rKindD, rKindM);
			}
		}
		if (true) {
			String rKindM = "2";
			Map<String, String> map = codeTypeService
					.findByCodeType(UtilConstants.CodeTypeItem.親屬關係);
			for (String k : map.keySet()) {
				String rKindD = Util.trim(k);
				r.put(rKindD, rKindM);
			}
		}
		if (true) {
			String rKindM = "3";
			Map<String, String> map1 = codeTypeService
					.findByCodeType(UtilConstants.CodeTypeItem.綜合關係_企業);
			Map<String, String> map2 = codeTypeService
					.findByCodeType(UtilConstants.CodeTypeItem.綜合關係_親屬);
			for (String first : map1.keySet()) {
				for (String second : map2.keySet()) {
					String rKindD = Util.trim(first) + Util.trim(second);
					r.put(rKindD, rKindM);
				}
			}
		}
		return r;
	}

	@Override
	public void syncL140Rating(L140M01A l140m01a, String c121MainId,
			L140M01C l140m01c) {
		List<C120M01A> c120m01a_list = clsService
				.findC120M01A_mainId_orderBy_keymanCustposCustid(c121MainId);
		C121M01A c121m01a = clsService.findC121M01AByMainId(c121MainId);

		if (true) {
			l140m01c.setLnYear(c121m01a.getLnYear());
			l140m01c.setLnMonth(c121m01a.getLnMonth());
		}
		if (true) { // 共同借款人檔 L140M01J
			List<L140M01J> saveJ = new ArrayList<L140M01J>();
			String custPos = UtilConstants.lngeFlag.共同借款人;
			Map<String, String[]> cMap = _filter_c120m01a_idDup_with_custPos(
					c120m01a_list, custPos);
			List<L140M01J> l140m01j_list = clsService.findL140M01J(l140m01a);
			Set<String> existIdDupL140M01J = new HashSet<String>();
			for (L140M01J l140m01j : l140m01j_list) {
				String idDup = LMSUtil.getCustKey_len10custId(
						l140m01j.getCustId(), l140m01j.getDupNo());
				existIdDupL140M01J.add(idDup);
			}
			for (String idDup : cMap.keySet()) {
				if (existIdDupL140M01J.contains(idDup)) {
					continue;
				}
				String custName = cMap.get(idDup)[0];
				String ntCode = cMap.get(idDup)[1];
				String custRlt = cMap.get(idDup)[2];

				L140M01J l140m01j = new L140M01J();
				l140m01j.setMainId(l140m01a.getMainId());
				l140m01j.setCustId(Util.trim(StringUtils
						.substring(idDup, 0, 10)));
				l140m01j.setDupNo(Util.trim(StringUtils
						.substring(idDup, 10, 11)));
				l140m01j.setCustName(custName);
				l140m01j.setNtCode(ntCode);
				l140m01j.setCustRlt(custRlt);
				l140m01j.setCustPos(custPos);
				// ---
				saveJ.add(l140m01j);
			}
			if (saveJ.size() > 0) {
				for (L140M01J l140m01j : saveJ) {
					clsService.save(l140m01j);
				}
				// ref:: inclueL140M01J
				// 把 insert 的 l140m01j 給存進 db後，再組字
				l140m01a.setL140m01jStr(getL140M01JStr(clsService
						.findL140M01J(l140m01a)));
				l140m01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
			}
		}
		if (true) { // 連保人 L140M01I
			List<L140M01I> saveI = new ArrayList<L140M01I>();
			String custPos = UtilConstants.lngeFlag.連帶保證人;
			Map<String, String[]> gMap = _filter_c120m01a_idDup_with_custPos(
					c120m01a_list, custPos);
			List<L140M01I> l140m01i_list = clsService.findL140M01IWithRType(
					l140m01a, UtilConstants.lngeFlag.連帶保證人);
			Set<String> existIdDupL140M01I = new HashSet<String>();
			for (L140M01I l140m01i : l140m01i_list) {
				String idDup = LMSUtil.getCustKey_len10custId(
						l140m01i.getRId(), l140m01i.getRDupNo());
				existIdDupL140M01I.add(idDup);
			}
			Map<String, String> rKindD_rKindM_map = _get_rKindM_from_rKindD();
			for (String idDup : gMap.keySet()) {
				if (existIdDupL140M01I.contains(idDup)) {
					continue;
				}
				String custName = gMap.get(idDup)[0];
				String ntCode = gMap.get(idDup)[1];
				String custRlt = gMap.get(idDup)[2];
				String busCode = gMap.get(idDup)[3];

				L140M01I l140m01i = new L140M01I();
				l140m01i.setMainId(l140m01a.getMainId());
				l140m01i.setType(LMSUtil.isBusCode_060000_130300(busCode) ? "1"
						: "2");
				l140m01i.setRId(Util.trim(StringUtils.substring(idDup, 0, 10)));
				l140m01i.setRDupNo(Util.trim(StringUtils.substring(idDup, 10,
						11)));
				l140m01i.setRName(custName);
				l140m01i.setRKindM(rKindD_rKindM_map.containsKey(custRlt) ? rKindD_rKindM_map
						.get(custRlt) : "");
				l140m01i.setRKindD(custRlt);
				l140m01i.setRCountry(ntCode);
				l140m01i.setGuaPercent(BigDecimal.valueOf(100));
				// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
				l140m01i.setRType(UtilConstants.lngeFlag.連帶保證人);
				// ---
				saveI.add(l140m01i);
			}
			if (saveI.size() > 0) {
				for (L140M01I l140m01i : saveI) {
					clsService.save(l140m01i);
				}
				// 把 insert 的 l140m01i 給存進 db後，再組字
				// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
				l140m01a.setGuarantor(this.contactGuarantor(l140m01a,
						UtilConstants.lngeFlag.連帶保證人));
				l140m01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
			}
		}
		if (true) { // synL140Rating 寫入擔保品
			String key = UtilConstants.Cntrdoc.l140m01bItemType.引入評等質化內容;
			L140M01B l140m01b = clsService.findL140M01B(l140m01a.getMainId(),
					key);
			if (l140m01b == null) {
				l140m01b = new L140M01B();
				l140m01b.setItemType(key);
				l140m01b.setMainId(l140m01a.getMainId());
			}
			if (true) {
				String itemDscr = "";
				if (c121m01a != null) {
					C121S01A c121s01a = clsService.findC121S01A(c121m01a);
					if (c121s01a != null) {
						JSONObject json = buildQualitativeFactor(c121m01a,
								c121s01a);
						if (!json.isEmpty()) {
							itemDscr = json.toString();
						}
					}
				}
				l140m01b.setItemDscr(itemDscr);
			}

			clsService.save(l140m01b);
		}
	}

	@Override
	public JSONObject buildQualitativeFactor(C121M01A c121m01a,
			C121S01A c121s01a) {
		String cmsType = Util.trim(c121s01a.getCmsType());
		String location = Util.trim(c121s01a.getLocation());
		String region = Util.trim(c121s01a.getRegion());
		String houseAge = Util.trim(c121s01a.getHouseAge());
		String houseArea = OverSeaUtil.pretty_numStr(Util.trim(c121s01a
				.getHouseArea()));
		String factor1 = Util.trim(c121s01a.getFactor1());
		String factor2 = Util.trim(c121s01a.getFactor2());
		String collUsage = Util.trim(c121s01a.getCollUsage());
		String locationType = Util.trim(c121s01a.getLocationType());
		String securityRate = "";
		if (c121s01a.getSecurityRate() != null) {
			securityRate = LMSUtil.pretty_numStr(c121s01a.getSecurityRate());
		}
		Map<String, String> map = new HashMap<String, String>();
		map.put("cmsType", Util.trim(cmsType));
		map.put("location", Util.trim(location));
		map.put("region", Util.trim(region));
		map.put("houseAge", Util.trim(houseAge));
		map.put("houseArea", Util.trim(houseArea));
		map.put("factor1", Util.trim(factor1));
		map.put("factor2", Util.trim(factor2));
		map.put("securityRate", securityRate);
		map.put("collUsage", collUsage);
		map.put("locationType", locationType);

		JSONObject r = new JSONObject();
		// 依不同的海外地區，回傳 json 欄位
		for (String col : c121s01a_used_key(c121m01a)) {
			if (map.containsKey(col)) {
				r.put(col, map.get(col));
			}
		}
		return r;
	}

	private Set<String> c121s01a_used_key(C121M01A c121m01a) {
		Set<String> r = new HashSet<String>();
		r.add("cmsType");
		if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, c121m01a.getMowType())) {
			r.add("location");
			r.add("region");
			r.add("collUsage");
			r.add("locationType");
		} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲,
				c121m01a.getMowType())) {

		} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國,
				c121m01a.getMowType())) {
			r.add("securityRate");
			r.add("location");
		}
		r.add("houseAge");
		r.add("houseArea");// 建物面積(平方公尺)
		r.add("factor1");
		r.add("factor2");

		return r;
	}

	@Override
	public String contactGuarantor(L140M01A l140m01a, String rType) {
		StringBuilder temp = new StringBuilder("");
		// // J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
		List<L140M01I> l140m01i_list = clsService.findL140M01IWithRType(
				l140m01a, rType);

		if (l140m01i_list.size() > 0) {
			// 組連保人
			for (L140M01I l140m01i : l140m01i_list) {
				if (Util.equals(Util.trim(l140m01i.getRType()), rType)) {
					// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
					temp.append(temp.length() > 0 ? UtilConstants.Mark.MARKDAN
							: "");
					temp.append(l140m01i.getRName());
				}

			}
		}

		// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
		temp.append(" ");
		if (Util.equals(rType, UtilConstants.lngeFlag.連帶保證人)) {
			temp.append(Util.trim(l140m01a.getGuarantorMemo()));
		}

		return temp.toString();
	}

	@Override
	public Set<String> l140m01aMainBorrower_notInRating(L120M01A l120m01a,
			List<L140M01A> listL140m01a) {
		List<C120M01A> c120m01a_list = clsService
				.findC120M01A_caseId_keyMan_distinctIdDup_ratingKind1(l120m01a
						.getMainId());
		Set<String> idDupSet = new HashSet<String>();
		for (C120M01A c120m01a : c120m01a_list) {
			idDupSet.add(LMSUtil.getCustKey_len10custId(c120m01a.getCustId(),
					c120m01a.getDupNo()));
		}
		Set<String> r = new LinkedHashSet<String>();
		for (L140M01A l140m01a : listL140m01a) {
			String property = l140m01a.getProPerty();
			if (UtilConstants.Cntrdoc.Property.不變.equals(property)
					|| UtilConstants.Cntrdoc.Property.取消.equals(property)) {
				/*
				 * 可能在簽報書，引入配偶的額度，但配偶不擔任"從債務人". 性質為不變
				 */
				continue;
			}
			if (idDupSet.contains(LMSUtil.getCustKey_len10custId(
					l140m01a.getCustId(), l140m01a.getDupNo()))) {
				continue;
			}
			r.add(Util.trim(l140m01a.getCntrNo()));
		}
		return r;
	}

	private String get_loanTP_desc(String loanTP, Map<String, String> loanTpMap) {
		String loanTPDesc = Util.trim(LMSUtil.getDesc(loanTpMap, loanTP));
		return loanTP
				+ (Util.equals(loanTP, loanTPDesc) ? "" : "-" + loanTPDesc);
	}

	private boolean isL140M01C_NoRating(L140M01C l140m01c) {
		if (Util.isEmpty(Util.trim(l140m01c.getC121MainId()))
				|| Util.isEmpty(Util.trim(l140m01c.getC121RatingId()))
				|| Util.isEmpty(Util.trim(l140m01c.getGrade()))) {
			return true;
		}
		return false;
	}

	@Override
	public List<String> ratingElm_1_noGrade(List<L140M01C> l140m01c_list,
			Map<String, String> loanTpMap) {
		List<String> r = new ArrayList<String>();
		for (L140M01C l140m01c : l140m01c_list) {
			// 暫訂，所有科目都要評等
			if (isL140M01C_NoRating(l140m01c)) {
				String loanTP = Util.trim(l140m01c.getLoanTP());
				r.add(get_loanTP_desc(loanTP, loanTpMap));
			}
		}
		return r;
	}

	@Override
	public List<String> ratingElm_2_notExist(L120M01A l120m01a,
			List<L140M01C> l140m01c_list, Map<String, String> loanTpMap) {
		List<String> r = new ArrayList<String>();
		for (L140M01C l140m01c : l140m01c_list) {
			if (isL140M01C_NoRating(l140m01c)) {
				continue;
			}
			String loanTP = Util.trim(l140m01c.getLoanTP());
			String varVer = Util.trim(l140m01c.getVarVer());
			// !!!
			List<C120M01A> c120m01a_list = clsService
					.findC120M01A_mainId_orderBy_keymanCustposCustid(l140m01c
							.getC121MainId());
			Map<String, String> idDup_grade_map = new HashMap<String, String>();
			for (C120M01A c120m01a : c120m01a_list) {
				String grade = "";
				 // 抓true=抓C121M01B，false=抓C121M01F
				if (OverSeaUtil.isCaseDoc_CLS_JP_ON(l120m01a)) {
					boolean getC121M01B = true;
					if (Util.equals(varVer, OverSeaUtil.V2_0_LOAN_JP)) { // 2.0模型判斷是抓房貸還是非房貸
						String modelType = Util.trim(l140m01c.getModelType());
						if (Util.equals(modelType, OverSeaUtil.海外評等_非房貸)) {
							getC121M01B = false;
						}
					}
					if (getC121M01B) {
						C121M01B c121m01b = c120m01a.getC121m01b();
						if (c121m01b != null) {
							grade = Util.trim(c121m01b.getFRating());
						}
					} else { // 2.0非房貸，抓C121M01F
						C121M01F c121m01f = c120m01a.getC121m01f();
						if (c121m01f != null) {
							grade = Util.trim(c121m01f.getFRating());
						}
					}
				} else if (OverSeaUtil.isCaseDoc_CLS_AU_ON(l120m01a)) {
					boolean getC121M01C = true; // 抓true=抓C121M01C，false=抓C121M01G
					if (Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU)) { // 2.0模型判斷是抓房貸還是非房貸
						String modelType = Util.trim(l140m01c.getModelType());
						if (Util.equals(modelType, OverSeaUtil.海外評等_非房貸)) {
							getC121M01C = false;
						}
					}
					if (getC121M01C) {
						C121M01C c121m01c = c120m01a.getC121m01c();
						if (c121m01c != null) {
							grade = Util.trim(c121m01c.getFRating());
						}
					} else { // 3.0非房貸，抓C121M01G
						C121M01G c121m01g = c120m01a.getC121m01g();
						if (c121m01g != null) {
							grade = Util.trim(c121m01g.getFRating());
						}
					}
				} else if (OverSeaUtil.isCaseDoc_CLS_TH_ON(l120m01a)) {
					boolean getC121M01D = true; // 抓true=抓C121M01C，false=抓C121M01G
					if (Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)) { // 2.0模型判斷是抓房貸還是非房貸
						String modelType = Util.trim(l140m01c.getModelType());
						if (Util.equals(modelType, OverSeaUtil.海外評等_非房貸)) {
							getC121M01D = false;
						}
					}
					if(getC121M01D){
						C121M01D c121m01d = c120m01a.getC121m01d();
						if (c121m01d != null) {
							grade = Util.trim(c121m01d.getFRating());
						}
					}else{
						C121M01H c121m01h = c120m01a.getC121m01h();
						if (c121m01h != null) {
							grade = Util.trim(c121m01h.getFRating());
						}
					}
				}
				idDup_grade_map.put(LMSUtil.getCustKey_len10custId(
						c120m01a.getCustId(), c120m01a.getDupNo()), grade);
			}

			if (MapUtils.isEmpty(idDup_grade_map)) {
				// l140m01c.c121MainId 已被刪掉
				r.add(get_loanTP_desc(loanTP, loanTpMap));
			} else {
				String l140m01c_choose_idDup = LMSUtil.getCustKey_len10custId(
						l140m01c.getC121CustId(), l140m01c.getC121DupNo());
				if (idDup_grade_map.containsKey(l140m01c_choose_idDup)) {
					if (Util.equals(idDup_grade_map.get(l140m01c_choose_idDup),
							l140m01c.getGrade())) {
						// ok
					} else {
						// l140m01c.c121CustId 最終評等!=評等文件的最終評等
						r.add(get_loanTP_desc(loanTP, loanTpMap));
					}
				} else {
					// l140m01c.c121CustId 已自 評等文件 中移除
					r.add(get_loanTP_desc(loanTP, loanTpMap));
				}
			}

		}
		return r;
	}

	@Override
	public List<String> ratingElm_3_diff(L120M01A l120m01a,
			List<L140M01C> l140m01c_list) {
		List<String> r = new ArrayList<String>();
		Set<String> c121MainIdSet = new HashSet<String>();
		for (L140M01C l140m01c : l140m01c_list) {
			if (isL140M01C_NoRating(l140m01c)) {
				continue;
			}
			c121MainIdSet.add(l140m01c.getC121MainId());
		}
		if (c121MainIdSet.size() <= 1) {
			// 只有1筆評等文件，就沒有差別
		} else {
			List<String> c121MainId_list = new ArrayList<String>(c121MainIdSet);
			int size = c121MainId_list.size();
			for (int idxA = 0; idxA < size - 1; idxA++) {
				for (int idxB = idxA + 1; idxB < size; idxB++) {
					cmp_3(r, l120m01a, c121MainId_list, idxA, idxB);
				}
			}
		}
		return r;
	}

	@Override
	public List<String> ratingElm_4_vsl140m01a(L120M01A l120m01a,
			L120M01C l120m01c, L140M01A l140m01a, List<L140M01C> l140m01c_list) {
		List<String> r = new ArrayList<String>();

		if (l120m01c != null) {
			if (Util.equals(UtilConstants.Cntrdoc.ItemType.額度明細表,
					l120m01c.getItemType())) {
				TreeSet<String> c121MainIdSet = new TreeSet<String>();
				for (L140M01C l140m01c : l140m01c_list) {
					if (isL140M01C_NoRating(l140m01c)) {
						continue;
					}
					c121MainIdSet.add(l140m01c.getC121MainId());
				}
				// ======================
				// 在 ratingElm_3_diff 已確保，同一個額度下，引入的N份評等文件(從債務人、擔保品)相同
				// 所以只要取任1筆評等文件( C121M01A )和額度的內容相比
				if (c121MainIdSet.size() > 0) {
					String c121MainId = c121MainIdSet.first();
					C121M01A c121m01a = clsService
							.findC121M01AByMainId(c121MainId);
					// ~~~
					cmp_4(r, l120m01a, l140m01a, c121m01a);
				}
				// ======================
				// 比對 lnYear,lnMoth
				if (true) {
					for (L140M01C l140m01c : l140m01c_list) {
						if (isL140M01C_NoRating(l140m01c)) {
							continue;
						}
						String c121MainId = Util.trim(l140m01c.getC121MainId());
						C121M01A c121m01a = clsService
								.findC121M01AByMainId(c121MainId);
						// =====================
						// 仍會把 授信期間 上傳至 DW 的 OTS_RKCNTRNOOVS.Loan_period 及
						// OTS_RKCREDITOVS.Loan_period
						// 所以, 維持比對 data
						boolean diffRepaymentSch = false;
						if (Util.equals(l140m01c.getLnYear(),
								c121m01a.getLnYear())
								&& Util.equals(l140m01c.getLnMonth(),
										c121m01a.getLnMonth())) {

						} else {
							diffRepaymentSch = true;
						}

						if (Util.equals("2", c121m01a.getRepaymentSchFmt())
								&& !Util.equals(Util.trim(c121m01a
										.getRepaymentSchDays()), Util
										.trim(l140m01c.getLmtDays()))) {
							diffRepaymentSch = true;
						}

						if (diffRepaymentSch) {
							Properties propPage = MessageBundleScriptCreator
									.getComponentResource(LMS1405S02Page.class);
							Properties prop = MessageBundleScriptCreator
									.getComponentResource(LMS1405S02Panel.class);
							// other.l140m01c_ratingDocElm_vs_cntrDocElm=額度：{0}「{1}」與評等文件：{2}
							// 不一致
							String pattern = propPage
									.getProperty("other.l140m01c_ratingDocElm_vs_cntrDocElm");
							r.add(MessageFormat.format(
									pattern,
									l140m01a.getCntrNo(),
									prop.getProperty("L140M01c.item") // 授信科目
											+ l140m01c.getLoanTP()
											+ prop.getProperty("L140M01c.lmtDays") // 清償期限
									, c121m01a.getCaseNo()));
						}
					}

				}

			} else {
				// 918可再加從債務人(能不在評等文件內)，不能被卡住
			}
		}

		return r;
	}

	private void cmp_3(List<String> rtnList, L120M01A l120m01a,
			List<String> c121MainId_list, int idxA, int idxB) {
		C121M01A elmA_c121m01a = clsService
				.findC121M01AByMainId(c121MainId_list.get(idxA));
		C121M01A elmB_c121m01a = clsService
				.findC121M01AByMainId(c121MainId_list.get(idxB));

		// 同一額度下[共借人、連保人 的人數、資料應相同]
		boolean eqBorrower = Util.equals(
				_build_c121m01a_idDupCustPosCustRltStr(elmA_c121m01a),
				_build_c121m01a_idDupCustPosCustRltStr(elmB_c121m01a));

		// 同一額度下[擔保品的因子、地址應相同]
		boolean eqCms = false;
		if (OverSeaUtil.isCaseDoc_CLS_RatingFlag_ON(l120m01a)) {
			// 因 JP、AU 的評等文件的擔保品，都是共用C121S01A，進行比對
			C121S01A elmA_c121s01a = clsService.findC121S01A(elmA_c121m01a);
			C121S01A elmB_c121s01a = clsService.findC121S01A(elmB_c121m01a);

			Set<String> colSet = c121s01a_used_key(elmA_c121m01a);

			Map<String, Object[]> diffCols = LMSUtil.diffFieldValue_inColumn(
					elmA_c121s01a, elmB_c121s01a, colSet);
			eqCms = MapUtils.isEmpty(diffCols);
		}
		List<String> fail_list = new ArrayList<String>();
		if (true) {
			Properties prop_lms1015m01Page = MessageBundleScriptCreator
					.getComponentResource(LMS1015M01Page.class);
			if (!eqBorrower) {
				// 本案關係人基本資料
				fail_list.add(prop_lms1015m01Page.getProperty("tab.02"));
			}
			if (!eqCms) {
				// 擔保品資料
				fail_list.add(prop_lms1015m01Page.getProperty("tab.03"));
			}
		}

		if (fail_list.size() > 0) {
			Properties propPage = MessageBundleScriptCreator
					.getComponentResource(LMS1405S02Page.class);
			// 同一額度下，評等文件 {0}、{1}「{2}」不一致
			String pattern = propPage
					.getProperty("other.l140m01c_ratingDocElmDiff");
			rtnList.add(MessageFormat.format(pattern,
					elmA_c121m01a.getCaseNo(), elmB_c121m01a.getCaseNo(),
					StringUtils.join(fail_list, "、")));
		}
	}

	private void cmp_4(List<String> rtnList, L120M01A l120m01a,
			L140M01A l140m01a, C121M01A c121m01a) {
		// 比對 評等文件[共借人、連保人][擔保品] 與額度明細表內容[L140M01I 連保人, L140M01J 共借人]
		Set<String> condSet = new LinkedHashSet<String>();
		if (true) { // 可能加入企業戶的從債務人
			String custPos = UtilConstants.lngeFlag.連帶保證人;

			boolean hasG = false;
			if (OverSeaUtil.isCaseDoc_CLS_JP_ON(l120m01a)) {
				hasG = true;
			} else if (OverSeaUtil.isCaseDoc_CLS_AU_ON(l120m01a)) {
				// still false
			} else if (OverSeaUtil.isCaseDoc_CLS_TH_ON(l120m01a)) {
				hasG = true;
			}
			if (hasG) {
				// from 額度
				Set<String> m01iIdDup = new HashSet<String>();
				for (L140M01I l140m01i : clsService.findL140M01IWithRType(
						l140m01a, UtilConstants.lngeFlag.連帶保證人)) {
					if (Util.equals("1", l140m01i.getType())) {// 自然人
						String custId = Util.trim(l140m01i.getRId());
						String dupNo = Util.trim(l140m01i.getRDupNo());

						m01iIdDup.add(LMSUtil.getCustKey_len10custId(custId,
								dupNo));
					}
				}
				// ---
				// from 評等文件
				Set<String> c121IdDup = new HashSet<String>();
				for (C120M01A c120m01a : clsService
						.findC120M01A_ByC121M01A_custPos_orderBy_keymanCustposCustid(
								c121m01a, custPos)) {
					C120S01A c120s01a = clsService.findC120S01A(c120m01a);
					if (c120s01a != null
							&& LMSUtil.isBusCode_060000_130300(c120s01a
									.getBusCode())) {
						c121IdDup.add(LMSUtil.getCustKey_len10custId(
								c120m01a.getCustId(), c120m01a.getDupNo()));
					}
				}
				// ---
				if (LMSUtil.elm_onlyLeft(m01iIdDup, c121IdDup).size() > 0
						|| LMSUtil.elm_onlyRight(m01iIdDup, c121IdDup).size() > 0) {
					Properties prop = MessageBundleScriptCreator
							.getComponentResource(LMS1405S02Panel.class);
					condSet.add(prop.getProperty("L140M01a.conPersonNew"));// 連保人
				}
			} else if (OverSeaUtil.isCaseDoc_CLS_AU_ON(l120m01a)) {

			}

		}

		if (true) { // 可能加入企業戶的從債務人
			String custPos = UtilConstants.lngeFlag.共同借款人;
			// from 額度
			boolean notInRatingDoc = false;
			Set<String> m01jIdDup = new HashSet<String>();
			for (L140M01J l140m01j : clsService.findL140M01J(l140m01a)) {
				String custId = Util.trim(l140m01j.getCustId());
				String dupNo = Util.trim(l140m01j.getDupNo());
				C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(
						l120m01a.getMainId(), custId, dupNo);
				if (c120m01a == null) {
					// 當由「舊的額度」複製時，會copy L140M01J，但這樣子就不存在於評等文件內
					notInRatingDoc = true;
					continue;
				}
				C120S01A c120s01a = clsService.findC120S01A(c120m01a);
				if (c120s01a != null
						&& LMSUtil.isBusCode_060000_130300(c120s01a
								.getBusCode())) {
					m01jIdDup
							.add(LMSUtil.getCustKey_len10custId(custId, dupNo));
				}
			}
			// ---
			// from 評等文件
			Set<String> c121IdDup = new HashSet<String>();
			for (C120M01A c120m01a : clsService
					.findC120M01A_ByC121M01A_custPos_orderBy_keymanCustposCustid(
							c121m01a, custPos)) {
				C120S01A c120s01a = clsService.findC120S01A(c120m01a);
				if (c120s01a != null
						&& LMSUtil.isBusCode_060000_130300(c120s01a
								.getBusCode())) {
					c121IdDup.add(LMSUtil.getCustKey_len10custId(
							c120m01a.getCustId(), c120m01a.getDupNo()));
				}
			}
			// ---
			if (LMSUtil.elm_onlyLeft(m01jIdDup, c121IdDup).size() > 0
					|| LMSUtil.elm_onlyRight(m01jIdDup, c121IdDup).size() > 0
					|| notInRatingDoc) {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1405S02Panel.class);
				condSet.add(prop.getProperty("L140M01a.message86"));// 共同借款人
			}
		}

		if (true) {
			C121S01A c121s01a_cntrNo = new C121S01A();
			if (true) {
				String itemDscr = "";
				L140M01B l140m01b = clsService.findL140M01B(
						l140m01a.getMainId(),
						UtilConstants.Cntrdoc.l140m01bItemType.引入評等質化內容);
				if (l140m01b != null) {
					itemDscr = Util.trim(l140m01b.getItemDscr());
				}
				JSONObject json = Util.isNotEmpty(itemDscr) ? JSONObject
						.fromObject(itemDscr) : new JSONObject();

				DataParse.toBean(json, c121s01a_cntrNo);
			}
			C121S01A c121s01a = clsService.findC121S01A(c121m01a);
			Map<String, Object[]> diff_cms = LMSUtil.diffFieldValue_inColumn(
					c121s01a_cntrNo, c121s01a, c121s01a_used_key(c121m01a));
			if (diff_cms.size() > 0) {
				logger.trace("dump_cmpC121S01A,key="
						+ c121s01a_used_key(c121m01a) + "。"
						+ LMSUtil.dump_diffFieldValue(diff_cms));
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1405S02Panel05.class);
				condSet.add(prop.getProperty("L140S02Tab.5_03"));
			}
		}
		if (condSet.size() > 0) {
			Properties propPage = MessageBundleScriptCreator
					.getComponentResource(LMS1405S02Page.class);
			// other.l140m01c_ratingDocElm_vs_cntrDocElm=額度：{0} {1} 與評等文件：{2}
			// 不一致
			String pattern = propPage
					.getProperty("other.l140m01c_ratingDocElm_vs_cntrDocElm");
			rtnList.add(MessageFormat.format(pattern, l140m01a.getCntrNo(),
					StringUtils.join(condSet, "、"), c121m01a.getCaseNo()));
		}

	}

	private String _build_c121m01a_idDupCustPosCustRltStr(C121M01A c121m01a) {
		List<C120M01A> c120m01a_list = clsService
				.findC120M01A_mainId_orderBy_keymanCustposCustid(c121m01a
						.getMainId());
		List<String> r = new ArrayList<String>();
		for (C120M01A c120m01a : c120m01a_list) {
			String idDup = LMSUtil.getCustKey_len10custId(c120m01a.getCustId(),
					c120m01a.getDupNo());
			String custPos = Util.equals("Y", c120m01a.getKeyMan()) ? OverSeaUtil.M
					: Util.trim(c120m01a.getCustPos());
			String custRlt = Util.equals("Y", c120m01a.getKeyMan()) ? "ZZ"
					: Util.trim(c120m01a.getO_custRlt());
			r.add(idDup + "-" + custPos + "-" + custRlt);
		}
		return StringUtils.join(r, "、");
	}

	/**
	 * J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 * 依額度明細表借款人取得借款人基本資料
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public L120S01D findL120S01DByKey(String caseMainId, String custId,
			String dupNo) {
		return l120s01dDao.findByUniqueKey(caseMainId, custId, dupNo);
	}

	// G-104-0097-001 Web e-Loan
	// 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
	@Override
	public String reCaculateL120S01M(String caseMainId, String caseType) {

		// 取得各借款人額度明細表本次額度異動合計
		// 新作、增額合計幣別 incApplyTotCurr CHAR(3)
		// 新作、增額合計金額 incApplyTotAmt DECIMAL(17,2)
		// 取消、減額合計幣別 decApplyTotCurr CHAR(3)
		// 取消、減額合計金額 decApplyTotAmt DECIMAL(17,2)

		StringBuffer errMsg = new StringBuffer("");
		// 用來儲存借款人的各個合計
		LinkedHashMap<String, BigDecimal> custIdTotalAdd = new LinkedHashMap<String, BigDecimal>();
		LinkedHashMap<String, BigDecimal> custIdTotalMinus = new LinkedHashMap<String, BigDecimal>();

		// 取出案件簽報書底下所有額度明細表
		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(caseMainId, caseType, null);

		L120M01A l120m01a = l120m01aDao.findByMainId(caseMainId);

		// 依目前簽案行做計算幣別
		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());

		for (L140M01A l140m01a : l140m01as) {

			if (!UtilConstants.Cntrdoc.CHKYN.已計算.equals(l140m01a.getChkYN())) {
				// 要計算後才能重新計算L120S01M
			}
			String custId = Util.trim(l140m01a.getCustId());
			String dupNo = Util.trim(l140m01a.getDupNo());
			String fullCustId = custId + "-" + dupNo;

			if (Util.notEquals(custId, "")) {
				// 增額
				if (!custIdTotalAdd.containsKey(fullCustId)) {
					String frmCurr = l140m01a.getIncApplyTotCurr();
					BigDecimal addAmt = l140m01a.getIncApplyTotAmt() == null ? BigDecimal.ZERO
							: l140m01a.getIncApplyTotAmt();
					// 轉換為本位幣
					BigDecimal addLocalAmt = branchRate.toLocalAmt(frmCurr,
							addAmt);
					custIdTotalAdd.put(fullCustId, addLocalAmt);

				}

				// 減額
				if (!custIdTotalMinus.containsKey(fullCustId)) {
					String frmCurr = l140m01a.getDecApplyTotCurr();
					BigDecimal minusAmt = l140m01a.getDecApplyTotAmt() == null ? BigDecimal.ZERO
							: l140m01a.getDecApplyTotAmt();
					// 轉換為本位幣
					BigDecimal minusLocalAmt = branchRate.toLocalAmt(frmCurr,
							minusAmt);
					custIdTotalMinus.put(fullCustId, minusLocalAmt);

				}
			}

		}// close

		// 檢核額度明細表有沒有對應的L120S01M
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSS02Page.class);
		for (String custKey : custIdTotalAdd.keySet()) {
			String custId = custKey.split("-")[0];
			String dupNo = custKey.split("-")[1];
			L120S01M l120s01m = service1205.findL120s01mByIndex01(caseMainId,
					custId, dupNo);
			if (l120s01m == null) {
				errMsg.append(custId + dupNo + pop2.get("l120s01m.alert5"));
				return errMsg.toString();
			}
		}

		// 1.於簽報書->借款人基本資料->信用風險管理遵循檢核表，新增「海外當地限額辦法」之遵循
		//
		// 2.「海外當地限額辦法」之遵循幣別與金額以泰銖顯示
		//
		// 3.欄位--授信總額度(當地帳務)：於產生該表時抓取同一人、同一關係人、同一關係企業或集團與泰子行有往來之AS400已動用額度
		//
		// 4.欄位--授信總額度(本案異動)：於計算額度合計時，將本次增額/減額之金額寫回信用風險管理遵循檢核表，加總授信總額度(當地帳務)後，除上海外當地淨值得到佔淨值比

		// 以下解決問題：欄位--授信總額度(本案異動)會有問題，若新做一個額度明細表核准後(申請額度500萬)，AS400尚未動用時，此時若客戶要求變更條件，泰行再報送一個變更條件的額度明細表(假設減額為400萬)，此時依照第4項的計算方式，會變成只抓到-100萬，可能造成數字失真
		// 檢查借款人在簽報書中的額度序號，如果符合下列條件，則將前准核准額度加回該ID的額度
		// 1.AS400尚未動用
		// 2.額度性質非新做(有前准核准額度)
		// 3.無共用或有共用時額度序號等於共用額度序號
		for (L140M01A l140m01a : l140m01as) {
			String custId = Util.trim(l140m01a.getCustId());
			String dupNo = Util.trim(l140m01a.getDupNo());
			String cntrNo = Util.trim(l140m01a.getCntrNo());
			String fullCustId = custId + "-" + dupNo;
			if (Util.equals(cntrNo, "")) {
				continue;
			}
			Map<String, Object> dataMap = dwdbBaseService.findDWCntrnoByCntrNo(
					custId, dupNo, cntrNo);
			if (dataMap == null || dataMap.isEmpty()) {
				// 1.AS400尚未動用
				String LV2Curr = Util.trim(l140m01a.getLV2Curr());
				BigDecimal LV2Amt = l140m01a.getLV2Amt() == null ? BigDecimal.ZERO
						: l140m01a.getLV2Amt();
				if (Util.notEquals(LV2Curr, "")
						&& LV2Amt.compareTo(BigDecimal.ZERO) > 0) {
					// 2.額度性質非新做(有前准核准額度)
					String commSno = Util.trim(l140m01a.getCommSno());
					if (Util.equals(commSno, "")
							|| (Util.notEquals(commSno, "") && Util.equals(
									commSno, cntrNo))) {
						// 3.無共用或有共用時額度序號等於共用額度序號

						// 增額
						if (custIdTotalAdd.containsKey(fullCustId)) {
							String frmCurr = LV2Curr;
							// 轉換為本位幣
							BigDecimal newLV2Amt = branchRate.toLocalAmt(
									frmCurr, LV2Amt);
							BigDecimal orgAmt = custIdTotalAdd.get(fullCustId);
							custIdTotalAdd.put(fullCustId,
									orgAmt.add(newLV2Amt));
						}

					}
				}
			}

		}

		// 清除L120S01M、L120S01N、L120S01O 當地帳務合計與註記
		eloandbService.setCaculate_L120S01M("", caseMainId);
		eloandbService.cleanLoacal_L120S01N(caseMainId); // dataKind IN //
															// ('012','022','032','082','092')
		eloandbService.cleanLoacal_L120S01O(caseMainId);

		// 塞本案增減額到L120S01O
		for (String custKey : custIdTotalAdd.keySet()) {
			String rCustId = custKey.split("-")[0];
			String rDupNo = custKey.split("-")[1];
			BigDecimal netValue = custIdTotalAdd.get(custKey).subtract(
					custIdTotalMinus.get(custKey));
			if (netValue.compareTo(BigDecimal.ZERO) < 0) {
				netValue = BigDecimal.ZERO;
			}
			eloandbService.setLocalCurrentAdjVal_L120S01O(caseMainId, rCustId,
					rDupNo, netValue);
		}

		// 重新計算L120S01N dataKind IN ('012','022','032','082','092')
		List<Map<String, Object>> rows = this.eloandbService
				.findAllL120S01NSumByMainId(caseMainId);

		for (Map<String, Object> row : rows) {

			String custId = Util.trim(((String) row.get("CUSTID")));
			String dupNo = Util.trim(((String) row.get("DUPNO")));
			String dataKind = Util.trim(((String) row.get("DATAKIND")));

			String hasLocalAmt = Util.trim(((String) row.get("HASLOCALAMT")));
			String localNetValCurr = Util.trim(((String) row
					.get("LOCALNETVALCURR")));

			BigDecimal localNetVal = row.get("LOCALNETVAL") == null ? BigDecimal.ZERO
					: (BigDecimal) row.get("LOCALNETVAL");
			BigDecimal localCurrentAdjVal = row.get("LOCALCURRENTADJVAL") == null ? BigDecimal.ZERO
					: (BigDecimal) row.get("LOCALCURRENTADJVAL");
			BigDecimal totalBal = row.get("TOTALBAL") == null ? BigDecimal.ZERO
					: (BigDecimal) row.get("TOTALBAL");

			BigDecimal localCurrentTotal = totalBal.add(localCurrentAdjVal);

			// if (Util.equals(hasLocalAmt, "Y")) {
			BigDecimal shareOfNet = BigDecimal.ZERO;
			if (localNetVal.compareTo(BigDecimal.ZERO) > 0) {
				shareOfNet = localCurrentTotal
						.divide(localNetVal, 10, BigDecimal.ROUND_HALF_UP)
						.multiply(BigDecimal.valueOf(100))
						.setScale(2, BigDecimal.ROUND_UP);
			}

			eloandbService
					.setLoacal_L120S01N(caseMainId, custId, dupNo, dataKind,
							localCurrentAdjVal, localCurrentTotal, shareOfNet);
			// }

		}

		// 設定L120S01M、L120S01N、L120S01O 當地帳務合計與註記
		eloandbService.setCaculate_L120S01M("Y", caseMainId);

		return errMsg.toString();
	}

	// G-104-0097-001 Web e-Loan
	// 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
	@Override
	public String resetLocalL120S01OAndReCaculateImpls(PageParameters params) {

		// 取得各借款人額度明細表本次額度異動合計
		// 新作、增額合計幣別 incApplyTotCurr CHAR(3)
		// 新作、增額合計金額 incApplyTotAmt DECIMAL(17,2)
		// 取消、減額合計幣別 decApplyTotCurr CHAR(3)
		// 取消、減額合計金額 decApplyTotAmt DECIMAL(17,2)

		StringBuffer errMsg = new StringBuffer("");
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String relType = Util.trim(params.getString("relType"));
		String caseMainId = Util.trim(params.getString("mainId"));

		String formL120s01o = Util.trim(params.getString("FORML120S01O"));
		JSONObject jsonL120m01b = JSONObject.fromObject(formL120s01o);

		// 清除L120S01M、L120S01N、L120S01O 當地帳務合計與註記
		eloandbService.setCaculate_L120S01M("", caseMainId);

		for (int index = 1; index < Integer.MAX_VALUE; index++) {
			if (jsonL120m01b.containsKey("rCustId_" + index)) {
				String rCustId = Util.trim(jsonL120m01b.optString("rCustId_"
						+ index, ""));
				String rDupNo = Util.trim(jsonL120m01b.optString("rDupNo_"
						+ index, ""));
				BigDecimal localCurrentAdjVal = Util.equals(
						Util.trim(
								jsonL120m01b.optString("localCurrentAdjVal_"
										+ index, "")).replace(",", ""), "") ? BigDecimal.ZERO
						: BigDecimal.valueOf(Double.parseDouble(Util.trim(
								jsonL120m01b.optString("localCurrentAdjVal_"
										+ index, "")).replace(",", "")));
				eloandbService.setLocalCurrentAdjVal_L120S01O(caseMainId,
						rCustId, rDupNo, localCurrentAdjVal);

			} else {
				break;
			}
		}

		// 重新計算L120S01N dataKind IN ('012','022','032','082','092')
		List<Map<String, Object>> rows = this.eloandbService
				.findAllL120S01NSumByMainId(caseMainId);

		for (Map<String, Object> row : rows) {

			String tcustId = Util.trim(((String) row.get("CUSTID")));
			String tdupNo = Util.trim(((String) row.get("DUPNO")));
			String tdataKind = Util.trim(((String) row.get("DATAKIND")));

			String localNetValCurr = Util.trim(((String) row
					.get("LOCALNETVALCURR")));

			BigDecimal localNetVal = row.get("LOCALNETVAL") == null ? BigDecimal.ZERO
					: (BigDecimal) row.get("LOCALNETVAL");
			BigDecimal localCurrentAdjVal = row.get("LOCALCURRENTADJVAL") == null ? BigDecimal.ZERO
					: (BigDecimal) row.get("LOCALCURRENTADJVAL");
			BigDecimal totalBal = row.get("TOTALBAL") == null ? BigDecimal.ZERO
					: (BigDecimal) row.get("TOTALBAL");

			BigDecimal localCurrentTotal = totalBal.add(localCurrentAdjVal);

			BigDecimal shareOfNet = BigDecimal.ZERO;

			if (localNetVal.compareTo(BigDecimal.ZERO) > 0) {
				shareOfNet = localCurrentTotal
						.divide(localNetVal, 10, BigDecimal.ROUND_HALF_UP)
						.multiply(BigDecimal.valueOf(100))
						.setScale(2, BigDecimal.ROUND_UP);
			}

			eloandbService.setLoacal_L120S01N(caseMainId, tcustId, tdupNo,
					tdataKind, localCurrentAdjVal, localCurrentTotal,
					shareOfNet);

		}

		// 設定L120S01M、L120S01N、L120S01O 當地帳務合計與註記
		eloandbService.setCaculate_L120S01M("Y", caseMainId);

		return errMsg.toString();
	}

	/**
	 * 確認海外額度明細表內是否有台灣的額度序號
	 * 
	 * @param l140m01a
	 * @return
	 */
	@Override
	public boolean hasTaiwanCntrno(L140M01A l140m01a) {
		boolean hasTW = false;
		String cntrNo = Util.trim(l140m01a.getCntrNo());
		if (Util.equals(cntrNo, "")) {
			return hasTW;
		}

		if (!TypCdEnum.海外.getCode().equals(cntrNo.substring(3, 4))) {
			hasTW = true;
			return hasTW;
		}

		List<L140M01E> l140m01es = l140m01eDao.findByMainId(l140m01a
				.getMainId());

		for (L140M01E l140m01e : l140m01es) {
			cntrNo = Util.trim(l140m01e.getShareNo());
			if (Util.notEquals(cntrNo, "")) {
				if (!TypCdEnum.海外.getCode().equals(cntrNo.substring(3, 4))) {
					hasTW = true;
					return hasTW;
				}
			}

		}

		return hasTW;
	}

	/**
	 * 取得額度性質非不變或解除之額度明細表/批覆書 J-105-0179-001 Web
	 * e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
	 */
	@Override
	public List<L140M01A> findL140m01aListByMainIdCustIdWithoutProperty7Or8(
			String caseMainId, String custId, String dupNo, String itemType) {
		return l140m01aDao.findByMainIdAndCustIdWithoutProperty7Or8(caseMainId,
				custId, dupNo, itemType);
	}

	/**
	 * 取得額度明細表主借款人與所有共借人L120S01D List J-105-0250-001 Web e-Loan 新增利害關係人檢核
	 * 
	 * @param l140m01a
	 * @return
	 */
	@Override
	public List<L120S01D> getCntrDocAllBorrowerL120S01D(L140M01A l140m01a) {

		List<L120S01D> l120s01ds = new ArrayList<L120S01D>();
		L120M01A l120m01a = null;

		if (l140m01a == null) {
			return l120s01ds;
		}

		L120M01C l120m01c = l140m01a.getL120m01c();
		if (l120m01c == null) {
			return l120s01ds;
		}

		l120m01a = service1205.findL120m01aByMainId(l120m01c.getMainId());

		if (l120m01a == null) {
			return l120s01ds;
		}

		// 主借款人
		L120S01D xl120s01d = service1205
				.findL120s01dByUniqueKey(l120m01a.getMainId(),
						l140m01a.getCustId(), l140m01a.getDupNo());
		l120s01ds.add(xl120s01d);

		// 共同借款人
		List<L140M01J> l140m01js = (List<L140M01J>) this.findModelListByMainId(
				L140M01J.class, l140m01a.getMainId());

		if (l140m01js != null && !l140m01js.isEmpty()) {
			for (L140M01J l140m01j : l140m01js) {

				if (l120m01a != null) {
					L120S01D tl120s01d = service1205.findL120s01dByUniqueKey(
							l120m01a.getMainId(), l140m01j.getCustId(),
							l140m01j.getDupNo());
					if (tl120s01d != null) {
						l120s01ds.add(tl120s01d);
					}
				}

			}
		}

		return l120s01ds;

	}

	/**
	 * 檢查額度明細表是否有無擔科目 J-105-0250-001 Web e-Loan 新增利害關係人檢核
	 * 
	 * @param l140m01a
	 * @param exceptSubject
	 * @return
	 */
	@Override
	public boolean chkCntrDocHasUnSecureSubject(L140M01A l140m01a,
			String sbjProperty, String[] exceptSubject) {
		boolean hasUnsecureFlag = false;
		int countSubject = 0;
		int countContainExceptSubject = 0;
		List<String> exceptSubjectList = null;
		if (exceptSubject != null) {
			exceptSubjectList = Arrays.asList(exceptSubject);
		} else {
			exceptSubjectList = Arrays.asList(new String[] { "" });
		}

		List<L140M01C> l140m01cs = l140m01cDao.findByMainId(l140m01a
				.getMainId());
		if (l140m01cs != null && !l140m01cs.isEmpty()) {
			for (L140M01C l140m01c : l140m01cs) {
				String loanTp = Util.trim(l140m01c.getLoanTP());
				countSubject = countSubject + 1;
				if (Util.notEquals(loanTp, "")) {
					String loanTpLeft = Util.getLeftStr(loanTp, 1);
					if (Util.equals(loanTpLeft, "1")
							|| Util.equals(loanTpLeft, "3")
							|| Util.equals(loanTpLeft, "5")) {
						if (!exceptSubjectList.contains(Util.getLeftStr(loanTp,
								3))) {
							hasUnsecureFlag = true;
						} else {
							countContainExceptSubject = countContainExceptSubject + 1;
						}
					}
				}
			}
		}

		if (!hasUnsecureFlag) {
			if (UtilConstants.Cntrdoc.sbjProperty.無擔保.equals(sbjProperty)
					&& countSubject > countContainExceptSubject) {
				hasUnsecureFlag = true;
			}
		}

		return hasUnsecureFlag;

	}

    @Override
    public boolean chkIsNeedDerivEval(L140M01A l140m01a) {
        boolean isDerivEval = false;

        if (l140m01a == null) {
            return isDerivEval;
        }

        String[] derivEval = new String[] { "Z09", "Z16", "Z11", "Z10", "963", "964" };
        List<L140M01C> l140m01cs = this.findL140m01cListByMainId(l140m01a.getMainId());
        if (l140m01cs != null) {
            for (L140M01C tl140m01c : l140m01cs) {
                if (Arrays.asList(derivEval).contains(Util.trim(tl140m01c.getLoanTP()))) {
                    isDerivEval = true;
                    break;
                }
            }
        }

        return isDerivEval;
    }

	/**
	 * J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public L120S01B findL120S01BByKey(String caseMainId, String custId,
			String dupNo) {
		return l120s01bDao.findByUniqueKey(caseMainId, custId, dupNo);
	}

	/*
	 * J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	 */
	@Override
	public L140M01I findL140m01iByUniqueKeyWithRType(String mainId,
			String type, String rId, String rDupNo, String rType) {
		return l140m01iDao.findByUniqueKeyWithRType(mainId, type, rId, rDupNo,
				rType);
	}

	/*
	 * J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	 */
	@Override
	public List<L140M01I> findL140m01iListWithRType(String mainId, String rType) {
		return l140m01iDao.findByMainIdWithRType(mainId, rType);
	}

	/**
	 * 不符合授信政策案 J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
	 */
	@Override
	public List<L140S04A> findL140s04aListByMainIdWithItemType(String mainId,
			String itemType) {
		return l140s04aDao.findByMainIdWithItemType(mainId, itemType);
	}

	/**
	 * 不符合授信政策案 J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
	 */
	@Override
	public L140S04A findL140s04aByOid(String oid) {
		return l140s04aDao.findByOid(oid);
	}

	/**
	 * 不符合授信政策案 J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
	 */
	@Override
	public void deleteListL140s04a(List<L140S04A> l140s04as) {
		// 刪除多筆資料
		for (L140S04A l140s04a : l140s04as) {
			l140s04aDao.delete(l140s04a);
		}
	}

	@Override
	public int delPreSearchResult(String userId) throws CapException {
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int tCount = 0;

		List list = l140m01atmp1Dao.findByUserId(userId);
		if (list != null && !list.isEmpty()) {
			tCount = l140m01atmp1Dao.delByUserId(userId);
		}
		return tCount;
	}

	// J-107-0069-001
	// e-Loan授信系統「同類授信對象」之搜尋條件請增加「模型評等等級」，並請開放區域營運中心可代營業單位搜尋全行符合條件之同類授信對象。
	@NonTransactional
	@Override
	public int execFullTextSearch(String fxUserId, String fxGroupId,
			String fxCaseDateName, String fxCaseDateS, String fxCaseDateE,
			String fxEndDateS, String fxEndDateE, String fxTypCd,
			String fxDocType, String fxDocKind, String fxDocCode,
			String fxUpdaterName, String fxUpdater, String fxCaseBrId,
			String fxCustId, String fxDocRslt, String fxDocStatus,
			String fxLnSubject, String fxRateText, String fxOtherCondition,
			String fxReportOther, String fxReportReason1,
			String fxReportReason2, String fxAreaOption, String fxCollateral,
			String fxCustName, String fxBusCode, String fxCurr,
			String fxLmtDays1, String fxLmtDays2, String fxRateText1,
			String fxGuarantor, String fxCntrNo, String fxCollateral1,
			String unitType, String fxIsCls, String fxProdKind,
			String fxLnSubjectCls, String fxRateTextCls, String fxLnMonth1,
			String fxLnMonth2, String fxCrGrade, String fxCrKind, String uid,
			String fxBldUse, String fxOnlyLand) throws CapException {

		int tCount = 0;

		List<Object[]> metaList = null;
		try {

			if (Util.equals(fxIsCls, "Y")) {
				// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
				metaList = l140m01aDao.findFullTextSearch_CLS(fxUserId,
						fxGroupId, fxCaseDateName, fxCaseDateS, fxCaseDateE,
						fxEndDateS, fxEndDateE, fxTypCd, fxDocType, fxDocKind,
						fxDocCode, fxUpdaterName, fxUpdater, fxCaseBrId,
						fxCustId, fxDocRslt, fxDocStatus, fxLnSubject,
						fxRateText, fxOtherCondition, fxReportOther,
						fxReportReason1, fxReportReason2, fxAreaOption,
						fxCollateral, fxCustName, fxBusCode, fxCurr,
						fxLmtDays1, fxLmtDays2, fxRateText1, fxGuarantor,
						fxCntrNo, fxCollateral1, unitType, fxIsCls, fxProdKind,
						fxLnSubjectCls, fxRateTextCls, fxLnMonth1, fxLnMonth2,
						fxBldUse, fxOnlyLand);

			} else {
				// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
				metaList = l140m01aDao.findFullTextSearch(fxUserId, fxGroupId,
						fxCaseDateName, fxCaseDateS, fxCaseDateE, fxEndDateS,
						fxEndDateE, fxTypCd, fxDocType, fxDocKind, fxDocCode,
						fxUpdaterName, fxUpdater, fxCaseBrId, fxCustId,
						fxDocRslt, fxDocStatus, fxLnSubject, fxRateText,
						fxOtherCondition, fxReportOther, fxReportReason1,
						fxReportReason2, fxAreaOption, fxCollateral,
						fxCustName, fxBusCode, fxCurr, fxLmtDays1, fxLmtDays2,
						fxRateText1, fxGuarantor, fxCntrNo, fxCollateral1,
						unitType, fxCrGrade, fxCrKind, fxBldUse, fxOnlyLand);

			}

		} catch (Exception e) {
			HashMap<String, String> msgMap = new HashMap<String, String>();
			msgMap.put("msg", " " + e.getMessage());
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", msgMap), getClass());
		}

		L140M01ATMP1 l140m01atmp1 = null;
		for (Object[] meta : metaList) {
			tCount = tCount + 1;

			l140m01atmp1 = new L140M01ATMP1();

			/*
			 * JSONObject data = new JSONObject(); data.put("OID", (String)
			 * meta[0]); data.put("UID", (String) meta[1]); data.put("MAINID",
			 * (String) meta[2]); data.put("TYPCD", (String) meta[3]);
			 * data.put("CUSTID", (String) meta[4]); data.put("DUPNO", (String)
			 * meta[5]); data.put("CUSTNAME", (String) meta[6]);
			 * data.put("UNITTYPE", (String) meta[7]); data.put("OWNBRID",
			 * (String) meta[8]); data.put("DOCSTATUS", (String) meta[9]);
			 * data.put("RANDOMCODE", (String) meta[10]); data.put("DOCURL",
			 * (String) meta[11]); data.put("TXCODE", (String) meta[12]);
			 * data.put("CREATOR", (String) meta[13]); data.put("CREATETIME",
			 * Util.trim((Timestamp) meta[14])); data.put("UPDATER", (String)
			 * meta[15]); data.put("UPDATETIME", Util.trim((Timestamp)
			 * meta[16])); data.put("APPROVER", (String) meta[17]);
			 * data.put("APPROVETIME", Util.trim((Timestamp) meta[18]));
			 * data.put("ISCLOSED", (String) meta[19]); data.put("DELETEDTIME",
			 * Util.trim((Timestamp) meta[20]));
			 */

			// l120m01atmp1.setOid((String) meta[0]);
			// l140m01atmp1.setUid((String) meta[1]);
			l140m01atmp1.setUid(uid); // UID 換成L785M01A 的 MAINID
			l140m01atmp1.setMainId((String) meta[2]);
			l140m01atmp1.setTypCd((String) meta[3]);
			l140m01atmp1.setCustId((String) meta[4]);
			l140m01atmp1.setDupNo((String) meta[5]);
			l140m01atmp1.setCustName((String) meta[6]);
			l140m01atmp1.setUnitType((String) meta[7]);
			l140m01atmp1.setOwnBrId((String) meta[8]);
			l140m01atmp1.setDocStatus((String) meta[9]);
			l140m01atmp1.setRandomCode((String) meta[10]);
			l140m01atmp1.setDocURL((String) meta[11]);
			l140m01atmp1.setTxCode((String) meta[12]);
			l140m01atmp1.setCreator((String) meta[13]);
			l140m01atmp1.setCreateTime((Timestamp) meta[14]);
			l140m01atmp1.setUpdater((String) meta[15]);
			l140m01atmp1.setUpdateTime((Timestamp) meta[16]);
			l140m01atmp1.setApprover((String) meta[17]);
			l140m01atmp1.setApproveTime((Timestamp) meta[18]);
			l140m01atmp1.setIsClosed((String) meta[19]);
			l140m01atmp1.setDeletedTime(CapDate.getCurrentTimestamp()); // meta[20]

			l140m01atmp1.setDataSrc((String) meta[21]);
			l140m01atmp1.setMainIdSrc((String) meta[22]);
			l140m01atmp1.setPrintSeq(meta[23] == null ? null : new Integer(
					((BigDecimal) meta[23]).intValue()));
			l140m01atmp1.setCaseNo((String) meta[24]);
			l140m01atmp1.setCaseDate(meta[25] == null ? null : (Date) meta[25]);

			l140m01atmp1.setGrant((String) meta[26]);
			l140m01atmp1.setCntrNo((String) meta[45]);
			l140m01atmp1.setLnSubject((String) meta[52]);

			l140m01atmp1.setCurrentApplyCurr((String) meta[70]);
			l140m01atmp1.setCurrentApplyAmt(meta[71] == null ? null
					: (BigDecimal) meta[71]);

			l140m01atmp1.setNotesUp(fxUserId); // meta[76]

			l140m01atmp1Dao.save(l140m01atmp1);
		}

		return tCount;

	}

	@Override
	public Page<L140M01A> findL140m01aByL140m01atmp1UserId(ISearch search,
			String userId) {
		return l140m01aDao.findL140m01aListByL140m01atmp1UserIdForSearch(
				search, userId);
	}

	@Override
	public Page<L140M01ATMP1> findL140m01atmp1ByUserId(ISearch search,
			String userId) {
		return l140m01atmp1Dao.findL140m01atmp1UserIdForSearch(search, userId);
	}

	@Override
	public L999LOG01A findLatestL999log01a(String creator, String itemType) {
		return l999log01aDao.findLatestByCreatorAndItemType(creator, itemType);
	}

	@Override
	public L999LOG01A findL999log01aByMainId(String logMainId) {
		return l999log01aDao.findByMainId(logMainId);
	}

	@Override
	public int delL140m01aTmp1ByUid(String uid) throws CapException {

		return l140m01atmp1Dao.delByUid(uid);
	}

	@Override
	public void saveL140m01tList(List<L140M01T> list) {
		l140m01tDao.save(list);
	}

	@Override
	public void deleteL140m01tAndFile(String oid) {
		L140M01T data = l140m01tDao.find(oid);
		if (data != null) {
			String estateType = "estateType" + Util.trim(data.getEstateType());

			List<DocFile> docs = docFileService.findByIDAndName(
					data.getMainId(), estateType, "");

			if (docs != null && docs.size() > 0) {
				for (DocFile doc : docs) {
					docFileService.clean(doc.getOid());
				}
			}

			l140m01tDao.delete(data);
		}
	}

	@Override
	public L140M01T getBuildInfoByMcntrNo(String mCntrNo) {
		ELF515 elf515 = misELF515Service.findMcntrNo(mCntrNo);
		L140M01T l140m01t = null;
		if (elf515 != null) {
			l140m01t = new L140M01T();
			l140m01t.setEstateType(elf515.getElf515_type());
			l140m01t.setEstateSubType(elf515.getElf515_sub_type1());
			l140m01t.setEstateStatus(elf515.getElf515_sub_type2());
			l140m01t.setEstateCityId(elf515.getElf515_site1());
			l140m01t.setEstateAreaId(elf515.getElf515_site2());
			l140m01t.setEstateSit3No(elf515.getElf515_site3());
			l140m01t.setEstateSit4No(elf515.getElf515_site4());
			l140m01t.setBuildWay(elf515.getElf515_sub_type4());
			l140m01t.setmCntrNo(mCntrNo);
			l140m01t.setSubTypeNote(elf515.getElf515_data1());
			l140m01t.setEstateNote(elf515.getElf515_data2());
			l140m01t.setLandlordNum(NumberUtils.isNumber(elf515
					.getElf515_data3()) ? CapMath.getBigDecimal(elf515
					.getElf515_data3()) : null);
			l140m01t.setOverDate(CapDate.parseDate(elf515.getElf515_sub_type3()));
			l140m01t.setOtherDesc(elf515.getElf515_data4()); // J-112-0460_12473_B1001
																// 重建方式新增 05-其他
																// 選項之自行輸入內容
		}

		return l140m01t;

	}

	@Override
	public void deleteCurrentL140m01ts(String mainId) {
		List<L140M01T> datas = l140m01tDao.findCurrentByMainId(mainId);
		if (datas != null && datas.size() > 0) {
			for (L140M01T data : datas) {
				this.deleteL140m01tAndFile(data.getOid());
			}
		}
	}

	@Override
	public List<L140M01T> findCurrentL140m01ts(String mainId) {
		return l140m01tDao.findCurrentByMainId(mainId);
	}

	@Override
	public List<L140M01T> findLastL140m01ts(String mainId) {
		return l140m01tDao.findLastByMainId(mainId);
	}

	@Override
	public L140M01T findL140m01t(String mainId, String estateType) {
		return l140m01tDao.findByMainIdEstateType(mainId, estateType);
	}

	@Override
	public List<L140S06A> findL140s06as(String mainId) {
		return l140s06aDao.findByMainId(mainId);
	}

	@Override
	public void saveL140s06aList(List<L140S06A> list) {
		l140s06aDao.save(list);
	}

	@Override
	public void deleteL140s06a(String oid) {
		L140S06A data = l140s06aDao.findByOid(oid);
		if (data != null) {
			l140s06aDao.delete(data);
		}
	}

	@Override
	public void deleteL140s06aAll(String mainId) {
		List<L140S06A> datas = l140s06aDao.findByIndex01(mainId);
		if (datas != null && datas.size() > 0) {
			l140s06aDao.delete(datas);
		}
	}

	// J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	@Override
	public List<L140M01O_0307> findL140m01o_0307as(String mainId) {
		return l140m01o_0307Dao.findByMainId(mainId);

	}

	@Override
	public void saveL140m01o_0307List(List<L140M01O_0307> list) {
		l140m01o_0307Dao.save(list);
	}

	@Override
	public void deleteL140m01o_0307All(String mainId) {
		List<L140M01O_0307> datas = l140m01o_0307Dao.findByMainId(mainId);
		if (datas != null && datas.size() > 0) {
			l140m01o_0307Dao.delete(datas);
		}
	}

	@Override
	public void deleteL140m01o_0307(String[] oids, String mainId) {
		List<L140M01O_0307> l140m01o_0307s = l140m01o_0307Dao.findByOids(oids);
		l140m01o_0307Dao.delete(l140m01o_0307s);
		L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
		l140m01a.setChkYN(null);
		this.save(l140m01a);
		return;
	}

	// J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	@Override
	public List<L140M01O_0307> findL140m01o_0307ByStkNoAndStkNm(String mainId,
			String stkNo, String StkNm) {
		return l140m01o_0307Dao.findByStkNoAndStkNm(mainId, stkNo, StkNm);

	}

	/**
	 * 取得代碼轉換
	 * 
	 * J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@Override
	public String queryL140s08aByItemNameAndSubItem(String itemName,
			String subItem) throws CapException {

		String mainItem = "";

		List<L140S08A> l140s08as = l140s08aDao.findByIndex02(itemName, subItem);
		if (l140s08as != null && !l140s08as.isEmpty()) {
			for (L140S08A l140s08a : l140s08as) {
				if (l140s08a != null) {
					mainItem = l140s08a.getMainItem();
				}

			}
		}

		return mainItem;

	}

	@Override
	public <T extends GenericBean> T findModelByManId(Class clazz, String mainId) {
		if (clazz == L120M01G.class) {
			return (T) l120m01gDao.findByUniqueKey(mainId);
		}
		logger.debug("{} is not set", clazz);
		return null;

	}

	@Override
	public L140M01A findItemType1FromItemType2(L140M01A l140m01aItemType2) {
		L120M01C l120m01c = l120m01cDao.findoneByRefMainId(l140m01aItemType2
				.getMainId());
		L140M01A l140m01aItemType1 = l140m01aDao.findByL120m01cMainIdAndcntrNo(
				l120m01c.getMainId(), l140m01aItemType2.getCntrNo(),
				UtilConstants.Cntrdoc.ItemType.額度明細表);
		return l140m01aItemType1;
	}

	@Override
	public void setL140M03AGrpCntrNo(String mainId, String parentCntrNo,
			String welfareCmte) {
		ArrayList<String> mainids = new ArrayList<String>();
		mainids.add(mainId);

		L140M03A l140m03a = l140m03aDao.findByMainId(mainId);
		if (l140m03a == null) {
			// 如果沒有L140M03A，就新增一筆，舊案沒有L140M03A
			// 純粹串接L120M01G用，沒有團貸其他功能
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			l140m03a = new L140M03A();
			l140m03a.setIsUse(UtilConstants.DEFAULT.否);
			l140m03a.setOid(null);
			l140m03a.setMainId(mainId);
			l140m03a.setCreateTime(CapDate.getCurrentTimestamp());
			l140m03a.setCreator(user.getUserId());
			l140m03a.setUpdater(user.getUserId());
			l140m03a.setUpdateTime(CapDate.getCurrentTimestamp());
			l140m03a.setGrpCntrNo(parentCntrNo);
			l140m03a.setWelfare_cmte(welfareCmte);
		} else {
			l140m03a.setGrpCntrNo(parentCntrNo);
			l140m03a.setWelfare_cmte(welfareCmte);
		}

		l140m03aDao.save(l140m03a);
	}

	// J-110-0455_05097_B1001 企金授信簽報書新增72-2簽案預約控管
	private String gfnDB2ChkNeedControlForGeneral(L140M01A l140m01a, Map<String, String> returnMap)
			throws CapMessageException {

		String ownBrid = l140m01a.getOwnBrId();
		String custId = l140m01a.getCustId();
		String dupNo = l140m01a.getDupNo();
		String cntrNo = l140m01a.getCntrNo();

		String needControl = MapUtils.getString(returnMap, "showMsg", "");

		// 是否需要限額控管 為空則為 否
		if (Util.isEmpty(needControl)) {
			return "";
		}

		List<Map<String, Object>> checkAmtMap = misELF442Service.findELF442ByCntrNoByCheckForGeneral(
				l140m01a.getOwnBrId(), l140m01a.getCustId(),
				l140m01a.getDupNo(), l140m01a.getCntrNo());

		if (checkAmtMap.size() == 0) {
			return needControl;
		}

		L140M01M l140m01m = this.findL140m01mByMainId(l140m01a.getMainId());

		String nowCurr = l140m01a.getCurrentApplyCurr();
		BigDecimal nowAMT = l140m01a.getCurrentApplyAmt();

		String ELF442_CURR = "";
		BigDecimal ELF442_QUOTA = BigDecimal.ZERO;
		String ELF442_PROD_CLASS = "";
		String ELF442_GRPID = "";
		String ELF442_RISK_AREA = "";
		String ELF442_BUS_CD = "";
		String ELF442_BUS_SUB_CD = "";
		String ELF442_CN_LOAN_FG = "";
		String ELF442_RISK_CNTRY = "";
		String ELF442_CUSTID = "";
		for (Map<String, Object> checkAmt : checkAmtMap) {

			ELF442_CURR = Util.trim(checkAmt.get("ELF442_CURR"));
			ELF442_QUOTA = (BigDecimal) checkAmt.get("ELF442_QUOTA");
			ELF442_PROD_CLASS = Util.trim(MapUtils.getString(checkAmt,
					"ELF442_PROD_CLASS", ""));

			ELF442_GRPID = Util.trim(MapUtils.getString(checkAmt,
					"ELF442_GRPID", ""));
			ELF442_RISK_AREA = Util.trim(MapUtils.getString(checkAmt,
					"ELF442_RISK_AREA", ""));
			ELF442_BUS_CD = Util.trim(MapUtils.getString(checkAmt,
					"ELF442_BUS_CD", ""));
			ELF442_BUS_SUB_CD = Util.trim(MapUtils.getString(checkAmt,
					"ELF442_BUS_SUB_CD", ""));
			ELF442_CN_LOAN_FG = Util.trim(MapUtils.getString(checkAmt,
					"ELF442_CN_LOAN_FG", ""));
			ELF442_RISK_CNTRY = Util.trim(MapUtils.getString(checkAmt,
					"ELF442_RISK_CNTRY", ""));
			ELF442_CUSTID = Util.trim(MapUtils.getString(checkAmt,
					"ELF442_CUSTID", ""));

		}
		if (!checkAmtMap.isEmpty()) {
			HashMap<String, String> custMsg = new HashMap<String, String>();
			custMsg.put("cntrNo", cntrNo);
			if (!nowCurr.equals(ELF442_CURR)) {
				custMsg.put("ELF442_CURR", ELF442_CURR);
				custMsg.put("nowCurr", nowCurr);
				// EFD3034=INFO|額度序號$\{cntrNo\}之預約額度幣別『$\{ELF442_CURR\}』與本案申請額度幣別『$\{nowCurr\}』不同。|
				return needControl = RespMsgHelper.getMessage("EFD3034", custMsg);
			}
			if (nowAMT.compareTo(ELF442_QUOTA) == 1) {
				custMsg.put("ELF442_QUOTA", NumConverter.addComma(ELF442_QUOTA));
				custMsg.put("nowAMT", NumConverter.addComma(nowAMT));
				// EFD3035=INFO|額度序號$\{cntrNo\}於額度明細表之現請額度金額『$\{nowAMT\}』大於資料建檔系統預約之額度金額『$\{ELF442_QUOTA\}』。|
				return needControl = RespMsgHelper.getMessage("EFD3035", custMsg);
			}
			if (Util.equals(ELF442_PROD_CLASS, "33")
					|| Util.equals(ELF442_PROD_CLASS, "34")) {

				if (l140m01m != null) {
					if (Util.notEquals(Util.trim(l140m01m.getProdClass()),
							ELF442_PROD_CLASS)) {
						// EFD3052=INFO|額度序號$\{cntrNo\}於額度明細表央行房貸註記之產品種類『$\{ProdClass\}』與資料建檔系統預約之產品種類『$\{ELF442_PROD_CLASS\}』不一致。|
						custMsg.put("ProdClass",
								Util.trim(l140m01m.getProdClass()));
						custMsg.put("ELF442_PROD_CLASS", ELF442_PROD_CLASS);
						return needControl = RespMsgHelper.getMessage("EFD3052", custMsg);
					}
				}
			}

			// J-106-0246-002 Web
			// e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
			if (Util.equals(Util.trim(l140m01a.getProjClass()), "G1")
					|| Util.equals(ELF442_PROD_CLASS, "G1")) {
				if (Util.notEquals(Util.trim(l140m01a.getProjClass()), "G1")
						|| Util.notEquals(ELF442_PROD_CLASS, "G1")) {
					// EFD3056=INFO|額度序號$\{cntrNo\}於額度明細表產品種類『$\{LnType\}』與資料建檔系統預約之產品種類『$\{ELF442_PROD_CLASS\}』不一致。|
					custMsg.put("LnType", Util.trim(l140m01a.getProjClass()));
					custMsg.put("ELF442_PROD_CLASS", ELF442_PROD_CLASS);
					return needControl = RespMsgHelper.getMessage("EFD3056", custMsg);
				}
			}

			String chkElf442TheSame = Util.trim(lmsService
					.getSysParamDataValue("LMS_J1060246_ELF442_CHK_ON"));

			if (Util.equals(chkElf442TheSame, "Y")) {

				for (String key : returnMap.keySet()) {
					// 判斷額度明細表符合L262要預約的條件，額度明細表內容是否與資料建檔ELF442相同
					String tKey = key;
					String tVal = returnMap.get(tKey);

					if (Util.equals(行業別代碼, tKey)) {
						String fullBusCd = ELF442_BUS_CD
								+ (Util.equals(ELF442_BUS_SUB_CD, "") ? "00"
										: ELF442_BUS_SUB_CD);
						if (Util.notEquals(tVal, fullBusCd)) {
							// 額度明細表大陸地區註記與資料建檔系統預約之ELF442不同
							// ERROR
							// 以下暫時不生效
							custMsg.put("KeyStr", "行業別代碼");
							custMsg.put("EloanVal", Util.trim(tVal));
							custMsg.put("ELF442_VAL", fullBusCd);
							return needControl = RespMsgHelper.getMessage("EFD3057", custMsg);
						}
					} else if (Util.equals(集團代碼, tKey)) {

						if (Util.notEquals(tVal, ELF442_GRPID)) {
							// 額度明細表
							// EFD3057=INFO|額度序號$\{cntrNo\}於額度明細表$\{KeyStr\}『$\{EloanVal\}』與資料建檔系統預約之『$\{ELF442_VAL\}』不一致。|

							// 以下暫時不生效
							custMsg.put("KeyStr", "集團代碼");
							custMsg.put("EloanVal", Util.trim(tVal));
							custMsg.put("ELF442_VAL", ELF442_GRPID);
							return needControl = RespMsgHelper.getMessage("EFD3057", custMsg);
						}

					} else if (Util.equals(國別代碼, tKey)) {
						if (Util.equals(tVal, "CN")) {
							if (Util.notEquals(ELF442_CN_LOAN_FG, "Y")) {
								// 額度明細表大陸地區註記與資料建檔系統預約之ELF442不同
								// ERROR
								// 以下暫時不生效
								custMsg.put("KeyStr", "大陸地區註記");
								custMsg.put("EloanVal", "Y");
								custMsg.put("ELF442_VAL", ELF442_CN_LOAN_FG);
								return needControl = RespMsgHelper.getMessage("EFD3057", custMsg);
							}
						}
					} else if (Util.equals(區域代碼, tKey)) {
						if (Util.notEquals(tVal, ELF442_RISK_AREA)) {
							// 額度明細表風險國別所屬支區域代碼與資料建檔系統預約之ELF442不同
							// ERROR
							// 以下暫時不生效
							custMsg.put("KeyStr", "區域代碼");
							custMsg.put("EloanVal", tVal);
							custMsg.put("ELF442_VAL", ELF442_RISK_AREA);
							return needControl = RespMsgHelper.getMessage("EFD3057", custMsg);
						}
					} else if (Util.equals(單一授信戶, tKey)) {

						if (Util.notEquals(tVal, ELF442_CUSTID)) {
							// 額度明細表客戶統編與資料建檔系統預約之ELF442不同
							// ERROR
							// 以下暫時不生效
							custMsg.put("KeyStr", "單一授信戶");
							custMsg.put("EloanVal", tVal);
							custMsg.put("ELF442_VAL", ELF442_CUSTID);
							return needControl = RespMsgHelper.getMessage("EFD3057", custMsg);
						}
					} else if (Util.equals(產品種類, tKey)) {

						if (Util.notEquals(tVal, ELF442_PROD_CLASS)) {
							// 額度明細表客戶統編與資料建檔系統預約之ELF442不同
							// ERROR
							// 以下暫時不生效
							custMsg.put("KeyStr", "產品種類");
							custMsg.put("EloanVal", tVal);
							custMsg.put("ELF442_VAL", ELF442_PROD_CLASS);
							return needControl = RespMsgHelper.getMessage("EFD3057", custMsg);
						}
					}

				}

			}

		}
		return "";
	}

	// J-110-0455_05097_B1001 企金授信簽報書新增72-2簽案預約控管
	private String gfnDB2ChkNeedControlFor722(L140M01A l140m01a, Map<String, String> returnMap)
			throws CapMessageException {
		String ownBrid = l140m01a.getOwnBrId();
		String custId = l140m01a.getCustId();
		String dupNo = l140m01a.getDupNo();
		String cntrNo = l140m01a.getCntrNo();

		String needControl = MapUtils.getString(returnMap, "showMsg722", "");

		// 是否需要限額控管 為空則為 否
		if (Util.isEmpty(needControl)) {
			return "";
		}

		// J-110-0507 不動產暨72-2針對都更危老，增加一個欄位{授信條件是否載明}
		// 如果有勾是就不擋傳送 不擋儲存
		List<L140M01T> l140m01tList = l140m01tDao.findByMainId(l140m01a
				.getMainId());
		// 如果StatedCheck有值，代表一定是都更/危老且授信條件是否載明勾了是，則不檢核該額度序號是否需要通過72-2預約核准
		// 每筆L140M01A對應的L140M01T只會有一筆
		for (L140M01T l140m01t : l140m01tList) {
			if (l140m01t.getStatedCheck() != null
					&& "Y".equals(l140m01t.getStatedCheck())) {
				return "";
			}
		}

		List<Map<String, Object>> checkAmtMap = misELF442Service.findELF442ByCntrNoByCheckFor722(
				l140m01a.getOwnBrId(), l140m01a.getCustId(),
				l140m01a.getDupNo(), l140m01a.getCntrNo());

		if (checkAmtMap.size() == 0) {
			return needControl;
		}


		String nowCurr = l140m01a.getCurrentApplyCurr();
		BigDecimal nowAMT = l140m01a.getCurrentApplyAmt();

		String ELF442_CURR = "";
		BigDecimal ELF442_QUOTA = BigDecimal.ZERO;

		for (Map<String, Object> checkAmt : checkAmtMap) {

			ELF442_CURR = Util.trim(checkAmt.get("ELF442_CURR"));
			ELF442_QUOTA = (BigDecimal) checkAmt.get("ELF442_QUOTA");

		}
		if (!checkAmtMap.isEmpty()) {
			HashMap<String, String> custMsg = new HashMap<String, String>();
			custMsg.put("cntrNo", cntrNo);
			if (!nowCurr.equals(ELF442_CURR)) {
				custMsg.put("ELF442_CURR", ELF442_CURR);
				custMsg.put("nowCurr", nowCurr);
				// EFD3034=INFO|額度序號$\{cntrNo\}之預約額度幣別『$\{ELF442_CURR\}』與本案申請額度幣別『$\{nowCurr\}』不同。|
				return needControl = RespMsgHelper.getMessage("EFD3034", custMsg);
			}
			if (nowAMT.compareTo(ELF442_QUOTA) == 1) {
				custMsg.put("ELF442_QUOTA", NumConverter.addComma(ELF442_QUOTA));
				custMsg.put("nowAMT", NumConverter.addComma(nowAMT));
				// EFD3035=INFO|額度序號$\{cntrNo\}於額度明細表之現請額度金額『$\{nowAMT\}』大於資料建檔系統預約之額度金額『$\{ELF442_QUOTA\}』。|
				return needControl = RespMsgHelper.getMessage("EFD3035", custMsg);
			}

		}
		return "";
	}

	// J-111-0163_05097_B1001 Web e-Loan企金國內、海外簽報書新增中租集團(代號1208)預約額度檢核作業
	private String gfnDB2ChkNeedControlForGrp07(L140M01A l140m01a, Map<String, String> returnMap)
			throws CapMessageException {
		String ownBrid = l140m01a.getOwnBrId();
		String custId = l140m01a.getCustId();
		String dupNo = l140m01a.getDupNo();
		String cntrNo = l140m01a.getCntrNo();

		String needControl = MapUtils.getString(returnMap, "showMsgGrp07", "");

		// 是否需要限額控管 為空則為 否
		if (Util.isEmpty(needControl)) {
			return "";
		}

		List<Map<String, Object>> checkAmtMap = misELF442Service.findELF442ByCntrNoByCheckForGrp07(
				l140m01a.getOwnBrId(), l140m01a.getCustId(),
				l140m01a.getDupNo(), l140m01a.getCntrNo());

		if (checkAmtMap.size() == 0) {
			return needControl;
		}


		String nowCurr = l140m01a.getCurrentApplyCurr();
		BigDecimal nowAMT = l140m01a.getCurrentApplyAmt();

		String ELF442_CURR = "";
		BigDecimal ELF442_QUOTA = BigDecimal.ZERO;

		for (Map<String, Object> checkAmt : checkAmtMap) {

			ELF442_CURR = Util.trim(checkAmt.get("ELF442_CURR"));
			ELF442_QUOTA = (BigDecimal) checkAmt.get("ELF442_QUOTA");

		}
		if (!checkAmtMap.isEmpty()) {
			HashMap<String, String> custMsg = new HashMap<String, String>();
			custMsg.put("cntrNo", cntrNo);
			if (!nowCurr.equals(ELF442_CURR)) {
				custMsg.put("ELF442_CURR", ELF442_CURR);
				custMsg.put("nowCurr", nowCurr);
				// EFD3034=INFO|額度序號$\{cntrNo\}之預約額度幣別『$\{ELF442_CURR\}』與本案申請額度幣別『$\{nowCurr\}』不同。|
				return needControl = RespMsgHelper.getMessage("EFD3034", custMsg);
			}
			if (nowAMT.compareTo(ELF442_QUOTA) == 1) {
				custMsg.put("ELF442_QUOTA", NumConverter.addComma(ELF442_QUOTA));
				custMsg.put("nowAMT", NumConverter.addComma(nowAMT));
				// EFD3035=INFO|額度序號$\{cntrNo\}於額度明細表之現請額度金額『$\{nowAMT\}』大於資料建檔系統預約之額度金額『$\{ELF442_QUOTA\}』。|
				return needControl = RespMsgHelper.getMessage("EFD3035", custMsg);
			}

		}
		return "";
	}

	private String gfnDB2ChkNeedControlForRwaCase2(L140M01A l140m01a, Map<String, String> returnMap) {
		String cntrNo = l140m01a.getCntrNo();
		String needControl = MapUtils.getString(returnMap, "showMsgRwaCase2", "");

		// 是否需要限額控管 為空則為 否
		if (Util.isEmpty(needControl)) {
			return "";
		}

		List<Map<String, Object>> checkAmtMap = misELF442Service.findELF442ByCntrNoByCheckForRwaCase2(l140m01a.getOwnBrId(),
				l140m01a.getCustId(), l140m01a.getDupNo(), l140m01a.getCntrNo());

		if (checkAmtMap.size() == 0) {
			return needControl;
		}

		String nowCurr = l140m01a.getCurrentApplyCurr();
		BigDecimal nowAMT = l140m01a.getCurrentApplyAmt();

		String ELF442_CURR = "";
		BigDecimal ELF442_QUOTA = BigDecimal.ZERO;
		for (Map<String, Object> checkAmt : checkAmtMap) {

			ELF442_CURR = Util.trim(checkAmt.get("ELF442_CURR"));
			ELF442_QUOTA = (BigDecimal) checkAmt.get("ELF442_QUOTA");

		}
		if (!checkAmtMap.isEmpty()) {
			HashMap<String, String> custMsg = new HashMap<String, String>();
			custMsg.put("cntrNo", cntrNo);
			if (!nowCurr.equals(ELF442_CURR)) {
				custMsg.put("ELF442_CURR", ELF442_CURR);
				custMsg.put("nowCurr", nowCurr);
				// EFD3034=INFO|額度序號$\{cntrNo\}之預約額度幣別『$\{ELF442_CURR\}』與本案申請額度幣別『$\{nowCurr\}』不同。|
				return RespMsgHelper.getMessage("EFD3034", custMsg);
			}
			if (nowAMT.compareTo(ELF442_QUOTA) == 1) {
				custMsg.put("ELF442_QUOTA", NumConverter.addComma(ELF442_QUOTA));
				custMsg.put("nowAMT", NumConverter.addComma(nowAMT));
				// EFD3035=INFO|額度序號$\{cntrNo\}於額度明細表之現請額度金額『$\{nowAMT\}』大於資料建檔系統預約之額度金額『$\{ELF442_QUOTA\}』。|
				return RespMsgHelper.getMessage("EFD3035", custMsg);
			}

		}
		return "";
	}

	@Override
	public L140M01M findL140m01mByMainId(String mainId) {
		return l140m01mDao.findByMainId(mainId);
	}

	/**
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param itemType
	 * @return
	 */
	@Override
	public L140M01A findL140m01aByCaseMainIdCustIdCntrNoItemType(
			String caseMainId, String custId, String dupNo, String cntrNo,
			String itemType) {
		return l140m01aDao.findByL120m01cMainIdCustIdAndcntrNo(caseMainId,
				custId, dupNo, cntrNo, itemType);
	}

	// J-104-0270-001 Web e-Loan國內授信管理系統OBU戶檢核要有聯徵虛擬統編才能送呈主管
	@Override
	public List<L140M01A> findL140m01aListByMainIdCustId(String caseMainId,
			String custId, String dupNo, String itemType) {
		return l140m01aDao.findByMainIdAndCustId(caseMainId, custId, dupNo,
				itemType);
	}

	/**
	 * 排除計算授信額度合計
	 * 
	 * @param l140m01a
	 * @return
	 */
	@Override
	public boolean isExTotalCreditLimit(L140M01A l140m01a) {

		boolean hasItem = false;
		// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
		// LMS_LGD_EX_TOTAL_CREDIT_LIMIT 9,A 排除計算授信額度合計

		String exTotalCreditLimit = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_EX_TOTAL_CREDIT_LIMIT"));

		String[] item = exTotalCreditLimit.split(",");
		List<String> asListItem = Arrays.asList(item);

		if (asListItem.contains(Util.trim(l140m01a.getIsStandAloneAuth()))) {
			hasItem = true;
		}
		return hasItem;
	}

	/**
	 * 
	 * @param params
	 * @param parent
	 * @throws CapException
	 */
	@Override
	public void saveCountEditForm(PageParameters params)
			throws CapException {

		String mainId = params.getString(EloanConstants.MAIN_ID);
		String caseType = params.getString("caseType");

		// 調整筆數
		int size = params.getInt("size");
		String formCountEdit = params.getString("countEditForm");
		JSONObject jsonCountEdit = JSONObject.fromObject(formCountEdit);

		// 把依據借款人id 當key把相對應的資料放到map

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		L120M01A l120m01a = service1205.findL120m01aByMainId(Util.trim(mainId));
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		boolean showLgdTotAmt = lmsService.showLgdTotAmt(l120m01a,
				l120m01a.getCaseBrId(), caseType);

		// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
		String suggestMode = Util.trim(jsonCountEdit.optString("suggestMode",
				"1"));

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		HashMap<String, JSONObject> custData = this.putCustIdAndMoney(
				jsonCountEdit, size, l120m01a, caseType);

		List<L140M01A> l140m01as = this.findL140m01aListByL120m01cMainId(
				mainId, caseType, null);
		StringBuffer temp = new StringBuffer();

		String editLoanTotCurr = "";
		BigDecimal editLoanTotAmt = BigDecimal.ZERO;
		BigDecimal editAssureTotAmt = BigDecimal.ZERO;
		BigDecimal editLoanTotZAmt = BigDecimal.ZERO;
		BigDecimal editLoanTotLAmt = BigDecimal.ZERO;
		BigDecimal editLVTotAmt = BigDecimal.ZERO;
		BigDecimal editLVAssTotAmt = BigDecimal.ZERO;
		BigDecimal editDecApplyTotAmt = BigDecimal.ZERO;
		BigDecimal editDecAssTotAmt = BigDecimal.ZERO;

		// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		BigDecimal editFlawAmtTotal = BigDecimal.ZERO;
		BigDecimal editGeneralLoanTotAmt = BigDecimal.ZERO;

		// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
		BigDecimal editStandAloneAuthTotal = BigDecimal.ZERO;

		// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
		BigDecimal editFlawAmtMgTotal = BigDecimal.ZERO;
		BigDecimal editGeneralLoanMgTotAmt = BigDecimal.ZERO;
		String editLgdTotMgCurr = "";

		// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
		BigDecimal editFlawAmtRcTotal = BigDecimal.ZERO;
		BigDecimal editGeneralLoanRcTotAmt = BigDecimal.ZERO;

		// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		boolean needShowGeneralLoanTotal = lmsService.needShowGeneralLoanTotal(
				l120m01a, l120m01a.getCaseBrId(), caseType);

		// J-111-0461_05097_B1009 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		boolean needShowLoanCountRcTotal = lmsService.needShowLoanCountRcTotal(
				l120m01a, l120m01a.getCaseBrId(), caseType);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		// BigDecimal editLgdTotAmt_U = null;
		// BigDecimal editLgdTotAmt_P = null;
		// BigDecimal editLgdTotAmt_S = null;

		// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
		Map<String, String> errLgdTotAmtTotalCust = new HashMap<String, String>();
		Map<String, String> errGeneralLoanTotAmtCust = new HashMap<String, String>();
		Map<String, String> errLgdTotAmtCust = new HashMap<String, String>();
		Map<String, String> errFlawAmtTotal = new HashMap<String, String>();
		Map<String, String> errLgdTotMgAmtCust = new HashMap<String, String>();
		Map<String, String> errGeneralLoanMgTotAmtCust = new HashMap<String, String>();
		Map<String, String> errFlawAmtMgTotal = new HashMap<String, String>();

		// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
		Map<String, String> errLgdTotRcAmtCust = new HashMap<String, String>();
		Map<String, String> errGeneralLoanRcTotAmtCust = new HashMap<String, String>();
		Map<String, String> errFlawAmtRcTotal = new HashMap<String, String>();
		Map<String, String> errLgdTotRcAmtTotalCust = new HashMap<String, String>();

		// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
		L140M01A l140m01aLgd = null;
		if (l140m01as != null && !l140m01as.isEmpty()) {
			l140m01aLgd = l140m01as.get(0);
		}
		Map<String, String> lgdMap = lmsService.getLgdTotAmtParam(l140m01aLgd,
				null);
		int lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
				"lmsLgdCount", "0"));
		int lmsLgdCountTotal = Util.parseInt(MapUtils.getString(lgdMap,
				"lmsLgdCountTotal", "0"));

		for (L140M01A l140m01a : l140m01as) {
			String custId = LMSUtil.concat(temp, l140m01a.getCustId(),
					l140m01a.getDupNo());
			if (custData.containsKey(custId)) {
				editLoanTotCurr = custData.get(custId).getString("curr");

				editLoanTotAmt = new BigDecimal(
						NumConverter.delCommaString(custData.get(custId)
								.getString(授信額度合計)));
				editAssureTotAmt = new BigDecimal(
						NumConverter.delCommaString(custData.get(custId)
								.getString(擔保授信合計)));
				editLoanTotZAmt = new BigDecimal(
						NumConverter.delCommaString(custData.get(custId)
								.getString(衍生性商品原始合計)));
				editLoanTotLAmt = new BigDecimal(
						NumConverter.delCommaString(custData.get(custId)
								.getString(衍生性商品相當合計)));
				editLVTotAmt = new BigDecimal(
						NumConverter.delCommaString(custData.get(custId)
								.getString(前准額度批覆合計)));
				editLVAssTotAmt = new BigDecimal(
						NumConverter.delCommaString(custData.get(custId)
								.getString(前准額度批覆擔保合計)));
				editDecApplyTotAmt = new BigDecimal(
						NumConverter.delCommaString(custData.get(custId)
								.getString(減額額度合計)));
				editDecAssTotAmt = new BigDecimal(
						NumConverter.delCommaString(custData.get(custId)
								.getString(減額擔保額度合計)));

				// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
				// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
				if (needShowGeneralLoanTotal) {
					editFlawAmtTotal = new BigDecimal(
							NumConverter.delCommaString(custData.get(custId)
									.getString(瑕疵押匯額度合計)));

					editGeneralLoanTotAmt = new BigDecimal(
							NumConverter.delCommaString(custData.get(custId)
									.getString(總授信額度合計)));

					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					editFlawAmtMgTotal = new BigDecimal(
							NumConverter.delCommaString(custData.get(custId)
									.getString(瑕疵押匯額度合計_全案)));

					editGeneralLoanMgTotAmt = new BigDecimal(
							NumConverter.delCommaString(custData.get(custId)
									.getString(總授信額度合計_全案)));
					editLgdTotMgCurr = custData.get(custId).getString(
							LGD合計_全案_幣別);

					// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
					if (needShowLoanCountRcTotal) {
						editFlawAmtRcTotal = new BigDecimal(
								NumConverter.delCommaString(custData
										.get(custId).getString(瑕疵押匯額度合計_合併關係)));

						editGeneralLoanRcTotAmt = new BigDecimal(
								NumConverter.delCommaString(custData
										.get(custId).getString(總授信額度合計_合併關係)));
					}
				}

				// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
				// if (showLgdTotAmt) {
				// editStandAloneAuthTotal = new BigDecimal(
				// NumConverter.delCommaString(custData.get(custId)
				// .getString(單獨另計授權額度合計)));
				// }

				// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
				HashMap<String, BigDecimal> lgdCount = new HashMap<String, BigDecimal>();
				for (int i = 1; i <= lmsLgdCount; i++) {
					lgdCount.put(Util.trim(i), BigDecimal.ZERO);
				}

				// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
				HashMap<String, BigDecimal> lgdCount_mg = new HashMap<String, BigDecimal>();
				for (int i = 1; i <= lmsLgdCount; i++) {
					lgdCount_mg.put(Util.trim(i), BigDecimal.ZERO);
				}

				// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
				HashMap<String, BigDecimal> lgdCount_rc = new HashMap<String, BigDecimal>();
				for (int i = 1; i <= lmsLgdCount; i++) {
					lgdCount_rc.put(Util.trim(i), BigDecimal.ZERO);
				}

				if (showLgdTotAmt) {
					// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
					// J-112-0037_05097_B1005 Web
					// eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
					for (int i = 1; i <= lmsLgdCount; i++) {
						lgdCount.put(Util.trim(i), Util
								.parseBigDecimal(NumConverter
										.delCommaString(custData.get(custId)
												.getString(LGD合計 + "_" + i))));
					}

					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					for (int i = 1; i <= lmsLgdCount; i++) {
						lgdCount_mg
								.put(Util.trim(i), Util
										.parseBigDecimal(NumConverter
												.delCommaString(custData.get(
														custId).getString(
														LGD合計_全案 + "_" + i))));
					}

					// J-111-0461_05097_B10096 授信額度合計新增單獨另計授權及各組LGD合計檢核
					if (needShowLoanCountRcTotal) {
						for (int i = 1; i <= lmsLgdCount; i++) {
							lgdCount_rc.put(Util.trim(i), Util
									.parseBigDecimal(NumConverter
											.delCommaString(custData
													.get(custId).getString(
															LGD合計_合併關係 + "_"
																	+ i))));
						}
					}

					// editLgdTotAmt_U = new BigDecimal(
					// NumConverter.delCommaString(custData.get(custId)
					// .getString(LGD其中無擔保合計)));
					// editLgdTotAmt_P = new BigDecimal(
					// NumConverter.delCommaString(custData.get(custId)
					// .getString(LGD其中擬制部分擔保合計)));
					// editLgdTotAmt_S = new BigDecimal(
					// NumConverter.delCommaString(custData.get(custId)
					// .getString(LGD其中擬制十足擔保合計)));
				}

				// 不管是金額或者是 幣別 與原始計算出來的 有不同就要註記調整
				if (l140m01a.getLoanTotAmt().compareTo(editLoanTotAmt) != 0
						|| l140m01a.getAssureTotAmt().compareTo(
								editAssureTotAmt) != 0
						|| l140m01a.getLoanTotZAmt().compareTo(editLoanTotZAmt) != 0
						|| l140m01a.getLoanTotLAmt().compareTo(editLoanTotLAmt) != 0
						|| l140m01a.getLVTotAmt().compareTo(editLVTotAmt) != 0
						|| l140m01a.getLVAssTotAmt().compareTo(editLVAssTotAmt) != 0
						|| l140m01a.getDecApplyTotAmt().compareTo(
								editDecApplyTotAmt) != 0
						|| l140m01a.getDecAssTotAmt().compareTo(
								editDecAssTotAmt) != 0

				) {
					l140m01a.setValueTune("Y");
				}

				// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
				if (needShowGeneralLoanTotal) {
					if (l140m01a.getFlawAmtTotal().compareTo(editFlawAmtTotal) != 0
							|| l140m01a.getGeneralLoanTotAmt().compareTo(
									editGeneralLoanTotAmt) != 0) {
						l140m01a.setValueTune("Y");
					}
				}

				// J-111-0461_05097_B1009 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
				if (needShowGeneralLoanTotal) {
					if (needShowLoanCountRcTotal) {
						if (l140m01a.getFlawAmtRcTotal().compareTo(
								editFlawAmtRcTotal) != 0
								|| l140m01a.getGeneralLoanRcTotAmt().compareTo(
										editGeneralLoanRcTotAmt) != 0) {
							l140m01a.setValueTune("Y");
						}
					}
				}

				// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
				// if (showLgdTotAmt) {
				// if (l140m01a.getStandAloneAuthTotal().compareTo(
				// editStandAloneAuthTotal) != 0) {
				// l140m01a.setValueTune("Y");
				// }
				// }

				// 如果幣別變更也註記調整
				if (!l140m01a.getLoanTotCurr().equals(editLoanTotCurr)) {
					l140m01a.setValueTune("Y");
				}

				// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
				if (showLgdTotAmt) {

					for (int i = 1; i <= lmsLgdCount; i++) {
						boolean hasChange = false;
						if (Util.parseBigDecimal(l140m01a.get(LGD合計 + "_" + i))
								.compareTo(
										Util.parseBigDecimal(MapUtils
												.getString(lgdCount,
														Util.trim(i)))) != 0) {
							hasChange = true;
						}

						if (hasChange) {
							l140m01a.setValueTuneLgd("Y");
							break;
						}
					}

					// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
					if (needShowLoanCountRcTotal) {
						for (int i = 1; i <= lmsLgdCount; i++) {
							boolean hasChange = false;
							if (Util.parseBigDecimal(
									l140m01a.get(LGD合計_合併關係 + "_" + i))
									.compareTo(
											Util.parseBigDecimal(MapUtils
													.getString(lgdCount_rc,
															Util.trim(i)))) != 0) {
								hasChange = true;
							}

							if (hasChange) {
								l140m01a.setValueTuneLgd("Y");
								break;
							}
						}
					}

					// if (l140m01a.getLgdTotAmt_U().compareTo(editLgdTotAmt_U)
					// != 0
					// || l140m01a.getLgdTotAmt_P().compareTo(
					// editLgdTotAmt_P) != 0
					// || l140m01a.getLgdTotAmt_S().compareTo(
					// editLgdTotAmt_S) != 0) {
					// l140m01a.setValueTuneLgd("Y");
					// }

					// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
					// BigDecimal lgdTotal =
					// editLgdTotAmt_U.add(editLgdTotAmt_P)
					// .add(editLgdTotAmt_S);
					// if (editLoanTotAmt.compareTo(lgdTotal) != 0) {
					// if (!errLgdCust.containsKey(custId)) {
					// errLgdCust.put(custId, l140m01a.getCustName());
					// }
					// }

					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					// 1.個別借款人歸戶授權***********************************************************************
					BigDecimal lgdTotal = BigDecimal.ZERO;
					for (int i = 1; i <= lmsLgdCount; i++) {
						lgdTotal = lgdTotal.add(Util.parseBigDecimal(MapUtils
								.getString(lgdCount, Util.trim(i))));
					}

					// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
					for (int i = 1; i <= lmsLgdCount; i++) {
						if (Util.parseBigDecimal(l140m01a.get(LGD合計 + "_" + i))
								.compareTo(
										Util.parseBigDecimal(MapUtils
												.getString(lgdCount,
														Util.trim(i)))) < 0) {
							// other.errMsg3=借款人統編「{0}」之「2.計算授信授權額度頁籤【LGD各組合計】」不得大於系統初始計算之合計。
							if (!errLgdTotAmtCust.containsKey(custId)) {
								errLgdTotAmtCust.put(custId,
										l140m01a.getCustName());
								break;
							}
						}
					}

					// 授信額度合計 = LGD 1+5
					if (editLoanTotAmt.compareTo(lgdTotal) != 0) {
						// other.errMsg1=借款人統編「{0}」之「2.計算授信授權額度頁籤【{1}】」不等於授信額度合計。
						if (!errLgdTotAmtTotalCust.containsKey(custId)) {
							errLgdTotAmtTotalCust.put(custId,
									l140m01a.getCustName());
						}
					}

					// J-111-0461_05097_B1004
					// 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
					if (needShowGeneralLoanTotal) {
						if (editGeneralLoanTotAmt.compareTo(lgdTotal
								.add(editFlawAmtTotal)) != 0) {
							// other.errMsg2=借款人統編「{0}」之「2.計算授信授權額度頁籤【總授信授權額度合計】」不等於LGD合計+出口瑕疵額度合計。
							if (!errGeneralLoanTotAmtCust.containsKey(custId)) {
								errGeneralLoanTotAmtCust.put(custId,
										l140m01a.getCustName());
							}
						}

						// 瑕疵押匯額度合計 不可已超過原始系統計算
						// 授信額度合計 = LGD 1+5
						if (editFlawAmtTotal.compareTo(l140m01a
								.getFlawAmtTotal()) > 0) {
							// other.errMsg4=借款人統編「{0}」之「2.計算授信授權額度頁籤【瑕疵押匯額度合計】」不得大於原始計算之合計。
							if (!errFlawAmtTotal.containsKey(custId)) {
								errFlawAmtTotal.put(custId,
										l140m01a.getCustName());
							}
						}
					}

					// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
					// 3.借款人合併關係企業授信授權額度檢核***********************************************************************
					if (needShowLoanCountRcTotal) {

						BigDecimal lgdTotal_rc = BigDecimal.ZERO;
						for (int i = 1; i <= lmsLgdCount; i++) {
							lgdTotal_rc = lgdTotal_rc.add(Util
									.parseBigDecimal(MapUtils.getString(
											lgdCount_rc, Util.trim(i))));
						}

						for (int i = 1; i <= lmsLgdCount; i++) {
							if (Util.parseBigDecimal(
									l140m01a.get(LGD合計_合併關係 + "_" + i))
									.compareTo(
											Util.parseBigDecimal(MapUtils
													.getString(lgdCount_rc,
															Util.trim(i)))) < 0) {
								// other.errMsg9=借款人統編「{0}」之「3.計算合併關係企業授信授權額度頁籤【LGD各組合計】」不得大於異動前。
								if (!errLgdTotRcAmtCust.containsKey(custId)) {
									errLgdTotRcAmtCust.put(custId,
											l140m01a.getCustName());
									break;
								}
							}
						}

						// 授信額度合計 要 < 合併關係企業 LGD 1+5
						if (editLoanTotAmt.compareTo(lgdTotal_rc) > 0) {
							// other.errMsg11=借款人統編「{0}」之「3.計算合併關係企業授信授權額度頁籤【{1}】」不得小於授信額度合計。
							if (!errLgdTotRcAmtTotalCust.containsKey(custId)) {
								errLgdTotRcAmtTotalCust.put(custId,
										l140m01a.getCustName());
							}
						}

						// 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
						if (needShowGeneralLoanTotal) {
							if (editGeneralLoanRcTotAmt.compareTo(lgdTotal_rc
									.add(editFlawAmtRcTotal)) != 0) {
								// other.errMsg8=借款人統編「{0}」之「3.計算合併關係企業授信授權額度頁籤【總授信授權額度合計】」不等於LGD合計+出口瑕疵額度合計。
								if (!errGeneralLoanRcTotAmtCust
										.containsKey(custId)) {
									errGeneralLoanRcTotAmtCust.put(custId,
											l140m01a.getCustName());
								}
							}

							// 瑕疵押匯額度合計 不可已超過原始系統計算
							// 授信額度合計 = LGD 1+5
							if (editFlawAmtRcTotal.compareTo(l140m01a
									.getFlawAmtRcTotal()) > 0) {
								// other.errMsg10=借款人統編「{0}」之「3.計算合併關係企業授信授權額度頁籤【瑕疵押匯額度合計】」不得大於異動前。
								if (!errFlawAmtRcTotal.containsKey(custId)) {
									errFlawAmtRcTotal.put(custId,
											l140m01a.getCustName());
								}
							}
						}

					}

				} else {
					l140m01a.setValueTuneLgd(null);
				}

				// 金額
				/** 授信額度合計金額 */
				l140m01a.setLoanTotAmt(editLoanTotAmt);
				/** 擔保授信額度合計金額 */
				l140m01a.setAssureTotAmt(editAssureTotAmt);
				/** 衍生性金融商品合計金額 */
				l140m01a.setLoanTotZAmt(editLoanTotZAmt);
				/** 衍生性金融商品相當授信額度合計金額 */
				l140m01a.setLoanTotLAmt(editLoanTotLAmt);
				/** 前准(批覆)額度合計金額 */
				l140m01a.setLVTotAmt(editLVTotAmt);
				/** 前准擔保授信額度合計金額 */
				l140m01a.setLVAssTotAmt(editLVAssTotAmt);
				/** 取消、減額合計金額 */
				l140m01a.setDecApplyTotAmt(editDecApplyTotAmt);
				/** 取消、減額擔保額度合計金額 */
				l140m01a.setDecAssTotAmt(editDecAssTotAmt);
				// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
				/** 瑕疵押匯額度合計金額 */
				l140m01a.setFlawAmtTotal(editFlawAmtTotal);
				/** 總授信額度合計金額 */
				l140m01a.setGeneralLoanTotAmt(editGeneralLoanTotAmt);
				// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
				/** 單獨另計授權額度合計金額 */
				l140m01a.setStandAloneAuthTotal(editStandAloneAuthTotal);

				// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
				/** 瑕疵押匯額度合計金額-全案 */
				l140m01a.setFlawAmtMgTotal(editFlawAmtMgTotal);
				/** 總授信額度合計金額 -全案 */
				l140m01a.setGeneralLoanMgTotAmt(editGeneralLoanMgTotAmt);
				/** LGD合計_全案_幣別 */
				l140m01a.setLgdTotMgCurr(editLgdTotMgCurr);

				// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
				if (needShowLoanCountRcTotal) {
					/** 瑕疵押匯額度合計金額-合併關係 */
					l140m01a.setFlawAmtRcTotal(editFlawAmtRcTotal);
					/** 總授信額度合計金額 -合併關係 */
					l140m01a.setGeneralLoanRcTotAmt(editGeneralLoanRcTotAmt);
				} else {
					/** 瑕疵押匯額度合計金額-合併關係 */
					l140m01a.setFlawAmtRcTotal(null);
					/** 總授信額度合計金額 -合併關係 */
					l140m01a.setGeneralLoanRcTotAmt(null);
				}

				// 新做 增額 = 授信額度合計 -前准額度批覆合計 + 取消減額合計
				l140m01a.setIncApplyTotAmt(editLoanTotAmt
						.subtract(editLVTotAmt).add(editDecApplyTotAmt));
				// 新做 增額 = 擔保授信合計 -前准額度批覆擔保合計 + 取消減額合計
				l140m01a.setIncAssTotAmt(editAssureTotAmt.subtract(
						editLVAssTotAmt).add(editDecAssTotAmt));

				// 幣別
				l140m01a.setLVAssTotCurr(editLoanTotCurr);
				l140m01a.setLVTotCurr(editLoanTotCurr);
				l140m01a.setLoanTotLCurr(editLoanTotCurr);
				l140m01a.setLoanTotZCurr(editLoanTotCurr);
				l140m01a.setAssureTotCurr(editLoanTotCurr);
				l140m01a.setLoanTotCurr(editLoanTotCurr);
				l140m01a.setDecAssTotCurr(editLoanTotCurr);
				l140m01a.setDecApplyTotCurr(editLoanTotCurr);
				l140m01a.setIncAssTotCurr(editLoanTotCurr);
				l140m01a.setIncApplyTotCurr(editLoanTotCurr);
				l140m01a.setMultiAmt(custData.get(custId).get(授信額度合計多幣別說明)
						.toString());
				l140m01a.setMultiAssureAmt(custData.get(custId)
						.get(擔保授信合計多幣別說明).toString());
				l140m01a.setDervMultiAmt(custData.get(custId)
						.get(衍生性授信額度合計多幣別說明).toString());
				l140m01a.setNewDervMultiAmtFlag("Y");

				// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
				// l140m01a.setLgdTotAmt_U(editLgdTotAmt_U);
				// l140m01a.setLgdTotAmt_P(editLgdTotAmt_P);
				// l140m01a.setLgdTotAmt_S(editLgdTotAmt_S);

				// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能

				for (int i = 1; i <= lmsLgdCountTotal; i++) {
					l140m01a.set(LGD合計 + "_" + i, null);
					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					l140m01a.set(LGD合計_全案 + "_" + i, null);
					// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
					l140m01a.set(LGD合計_合併關係 + "_" + i, null);
				}

				for (int i = 1; i <= lmsLgdCount; i++) {
					l140m01a.set(LGD合計 + "_" + i, Util.parseBigDecimal(MapUtils
							.getString(lgdCount, Util.trim(i))));

					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					l140m01a.set(LGD合計_全案 + "_" + i, Util
							.parseBigDecimal(MapUtils.getString(lgdCount_mg,
									Util.trim(i))));

					// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
					if (needShowLoanCountRcTotal) {
						l140m01a.set(LGD合計_合併關係 + "_" + i, Util
								.parseBigDecimal(MapUtils.getString(
										lgdCount_rc, Util.trim(i))));
					} else {
						l140m01a.set(LGD合計_合併關係 + "_" + i, null);
					}

				}

			}

		}

		// J-111-0461_05097_B1006-2 授信額度合計新增單獨另計授權及各組LGD合計檢核
		// 合併改成一筆所有借款人合計，所以改到baseCount後才合計**********************
		// 將調整的金額放到對應的借款人
		// 所有借款人合計
		if (needShowGeneralLoanTotal && showLgdTotAmt) {
			if (Util.equals(suggestMode, "2")) {
				BigDecimal allCase_flawAmtMgTotal = BigDecimal.ZERO;
				BigDecimal allCase_generalLoanMgTotAmt = BigDecimal.ZERO;
				JSONObject allCase_count = new JSONObject();
				String allCase_lgdTotMgCurr = "";

				for (int i = 1; i <= lmsLgdCount; i++) {
					allCase_count.put(LGD合計_全案 + "_" + i, 0);
				}

				// 先計算個別借款人歸戶授權合計

				for (String name : custData.keySet()) {
					JSONObject moneyJson = null;
					moneyJson = custData.get(name);
					allCase_lgdTotMgCurr = moneyJson.getString(LGD合計_全案_幣別);
					String custCurr = moneyJson.getString("curr");

					// 要用轉換匯率
					// 合併:editLgdTotMgCurr
					// 個體:curr
					for (int i = 1; i <= lmsLgdCount; i++) {
						BigDecimal tmpLgdCount = Util
								.parseBigDecimal(allCase_count.optString(
										LGD合計_全案 + "_" + i, "0"));
						BigDecimal lgdAmt = Util.parseBigDecimal(NumConverter
								.delCommaString(moneyJson.getString(LGD合計 + "_"
										+ i)));
						if (Util.notEquals(allCase_lgdTotMgCurr, custCurr)) {
							lgdAmt = branchRate.toOtherAmt(custCurr,
									allCase_lgdTotMgCurr, lgdAmt);
						}

						tmpLgdCount = tmpLgdCount.add(lgdAmt);
						allCase_count.put(LGD合計_全案 + "_" + i, tmpLgdCount);

					}

					BigDecimal flawAmtTotal = new BigDecimal(
							NumConverter.delCommaString(moneyJson
									.getString(瑕疵押匯額度合計)));
					if (Util.notEquals(allCase_lgdTotMgCurr, custCurr)) {
						flawAmtTotal = branchRate.toOtherAmt(custCurr,
								allCase_lgdTotMgCurr, flawAmtTotal);
					}

					BigDecimal generalLoanTotAmt = new BigDecimal(
							NumConverter.delCommaString(moneyJson
									.getString(總授信額度合計)));
					if (Util.notEquals(allCase_lgdTotMgCurr, custCurr)) {
						generalLoanTotAmt = branchRate.toOtherAmt(custCurr,
								allCase_lgdTotMgCurr, generalLoanTotAmt);
					}

					// 合併
					allCase_flawAmtMgTotal = allCase_flawAmtMgTotal
							.add(flawAmtTotal);
					allCase_generalLoanMgTotAmt = allCase_generalLoanMgTotAmt
							.add(generalLoanTotAmt);

				}

				BigDecimal bufferRate = Util.parseBigDecimal("1.00");
				String LMS_LGD_SAVE_COUNT_RATE = Util.trim(lmsService
						.getSysParamDataValue("LMS_LGD_SAVE_COUNT_RATE"));
				if (Util.notEquals(LMS_LGD_SAVE_COUNT_RATE, "")) {
					bufferRate = Util.parseBigDecimal(LMS_LGD_SAVE_COUNT_RATE);
				}

				// 個別借款人歸戶授權合計，再跟使用者輸入的全案所有借款人授權比較
				for (String name : custData.keySet()) {
					JSONObject moneyJson = null;
					moneyJson = custData.get(name);

					for (int i = 1; i <= lmsLgdCount; i++) {
						if (Util.parseBigDecimal(
								NumConverter.delCommaString(moneyJson
										.getString(LGD合計_全案 + "_" + i)))
								.compareTo(
										Util.parseBigDecimal(
												allCase_count
														.getString(LGD合計_全案
																+ "_" + i))
												.multiply(bufferRate)) > 0) {
							// other.errMsg5=頁籤「3.計算授信授權額度之」之各組LGD合計數值，不得大於頁籤「2.計算授信授權額度」所有借款人加總之各組LGD合計。
							errLgdTotMgAmtCust.put(name, name);
							break;
						}
					}

					if (needShowGeneralLoanTotal) {

						BigDecimal edit_flawAmtMgTotal = new BigDecimal(
								NumConverter.delCommaString(moneyJson
										.getString(瑕疵押匯額度合計_全案)));

						BigDecimal edit_generalLoanMgTotAmt = new BigDecimal(
								NumConverter.delCommaString(moneyJson
										.getString(總授信額度合計_全案)));

						if (edit_generalLoanMgTotAmt
								.compareTo(allCase_generalLoanMgTotAmt
										.multiply(bufferRate)) > 0) {
							// other.errMsg6=頁籤「3.計算授信授權額度之」之【總授信授權額度合計】數值，不得大於頁籤「2.計算授信授權額度」所有借款人加總之【總授信授權額度合計】合計。
							errGeneralLoanMgTotAmtCust.put(name, name);
						}

						// 合併瑕疵押匯額度合計 不可以超過 瑕疵押匯額度合計
						if (edit_flawAmtMgTotal
								.compareTo(allCase_flawAmtMgTotal
										.multiply(bufferRate)) > 0) {
							// other.errMsg7=頁籤「3.計算授信授權額度之」之【瑕疵押匯額度合計】數值，不得大於頁籤「2.計算授信授權額度」所有借款人加總之【瑕疵押匯額度合計】合計。
							errFlawAmtMgTotal.put(name, name);
						}
					}

					// 因為每一個借款人的全案合計數值都一樣，所以檢查一筆就好
					break;
				}

			}
		}

		// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核***************
		Properties comPop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		if (!errLgdTotAmtTotalCust.isEmpty()) {
			if (l120m01a != null) {
				String LMS_LGD_SAVE_COUNT_CHK_1 = Util.trim(lmsService
						.getSysParamDataValue("LMS_LGD_SAVE_COUNT_CHK_1"));
				if (Util.equals(LMS_LGD_SAVE_COUNT_CHK_1, "Y")) {
					StringBuffer tX = new StringBuffer("");
					for (int i = 1; i <= lmsLgdCount; i++) {
						tX.append((Util.equals(tX.toString(), "") ? "" : "+"))
								.append(MapUtils.getString(lgdMap,
										"label_" + i, ""));
					}

					// other.errMsg1=借款人統編「{0}」之「2.計算授信授權額度頁籤【{1}】」不等於授信額度合計。
					throw new CapMessageException(MessageFormat.format(
							comPop.getProperty("other.errMsg1"),
							errLgdTotAmtTotalCust.keySet().toString(),
							tX.toString()), getClass());
				}
			}
		}

		if (needShowGeneralLoanTotal) {
			if (!errGeneralLoanTotAmtCust.isEmpty()) {
				if (l120m01a != null) {
					String LMS_LGD_SAVE_COUNT_CHK_2 = Util.trim(lmsService
							.getSysParamDataValue("LMS_LGD_SAVE_COUNT_CHK_2"));
					if (Util.equals(LMS_LGD_SAVE_COUNT_CHK_2, "Y")) {
						// other.errMsg2=借款人統編「{0}」之「2.計算授信授權額度頁籤【總授信授權額度合計】」不等於LGD合計+出口瑕疵額度合計。
						throw new CapMessageException(MessageFormat.format(
								comPop.getProperty("other.errMsg2"),
								errGeneralLoanTotAmtCust.keySet().toString()),
								getClass());
					}
				}
			}
		}

		if (!errLgdTotAmtCust.isEmpty()) {
			if (l120m01a != null) {
				String LMS_LGD_SAVE_COUNT_CHK_3 = Util.trim(lmsService
						.getSysParamDataValue("LMS_LGD_SAVE_COUNT_CHK_3"));
				if (Util.equals(LMS_LGD_SAVE_COUNT_CHK_3, "Y")) {
					// other.errMsg3=借款人統編「{0}」之「2.計算授信授權額度頁籤【LGD各組合計】」不得大於異動前。
					throw new CapMessageException(MessageFormat.format(
							comPop.getProperty("other.errMsg3"),
							errLgdTotAmtCust.keySet().toString()), getClass());
				}
			}
		}

		if (!errFlawAmtTotal.isEmpty()) {
			if (l120m01a != null) {
				String LMS_LGD_SAVE_COUNT_CHK_4 = Util.trim(lmsService
						.getSysParamDataValue("LMS_LGD_SAVE_COUNT_CHK_4"));
				if (Util.equals(LMS_LGD_SAVE_COUNT_CHK_4, "Y")) {
					// other.errMsg4=借款人統編「{0}」之「2.計算授信授權額度頁籤【瑕疵押匯額度合計】」不得大於異動前。
					throw new CapMessageException(MessageFormat.format(
							comPop.getProperty("other.errMsg4"),
							errFlawAmtTotal.keySet().toString()), getClass());
				}
			}
		}

		// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
		// 檢核計算授信額度合計-合併關係企業*********************************************************

		if (true) {
			if (needShowLoanCountRcTotal) {
				if (!errLgdTotRcAmtTotalCust.isEmpty()) {
					if (l120m01a != null) {
						String LMS_LGD_SAVE_COUNT_CHK_11 = Util
								.trim(lmsService
										.getSysParamDataValue("LMS_LGD_SAVE_COUNT_CHK_11"));
						if (Util.equals(LMS_LGD_SAVE_COUNT_CHK_11, "Y")) {
							StringBuffer tX = new StringBuffer("");
							for (int i = 1; i <= lmsLgdCount; i++) {
								tX.append(
										(Util.equals(tX.toString(), "") ? ""
												: "+")).append(
										MapUtils.getString(lgdMap,
												"label_" + i, ""));
							}

							// other.errMsg11=借款人統編「{0}」之「3.計算合併關係企業授信授權額度頁籤【{1}】」不得小於授信額度合計。
							throw new CapMessageException(
									MessageFormat.format(comPop
											.getProperty("other.errMsg11"),
											errLgdTotRcAmtTotalCust.keySet()
													.toString(), tX.toString()),
									getClass());
						}
					}
				}

				if (needShowGeneralLoanTotal) {
					if (!errGeneralLoanRcTotAmtCust.isEmpty()) {
						if (l120m01a != null) {
							String LMS_LGD_SAVE_COUNT_CHK_8 = Util
									.trim(lmsService
											.getSysParamDataValue("LMS_LGD_SAVE_COUNT_CHK_8"));
							if (Util.equals(LMS_LGD_SAVE_COUNT_CHK_8, "Y")) {
								// other.errMsg8=借款人統編「{0}」之「3.計算合併關係企業授信授權額度頁籤【總授信授權額度合計】」不等於LGD合計+出口瑕疵額度合計。
								throw new CapMessageException(
										MessageFormat.format(comPop
												.getProperty("other.errMsg8"),
												errGeneralLoanRcTotAmtCust
														.keySet().toString()),
										getClass());
							}
						}
					}
				}

				if (!errLgdTotRcAmtCust.isEmpty()) {
					if (l120m01a != null) {
						String LMS_LGD_SAVE_COUNT_CHK_9 = Util
								.trim(lmsService
										.getSysParamDataValue("LMS_LGD_SAVE_COUNT_CHK_9"));
						if (Util.equals(LMS_LGD_SAVE_COUNT_CHK_9, "Y")) {
							// other.errMsg9=借款人統編「{0}」之「3.計算合併關係企業授信授權額度頁籤【LGD各組合計】」不得大於異動前。
							throw new CapMessageException(MessageFormat.format(
									comPop.getProperty("other.errMsg9"),
									errLgdTotRcAmtCust.keySet().toString()),
									getClass());
						}
					}
				}

				if (!errFlawAmtRcTotal.isEmpty()) {
					if (l120m01a != null) {
						String LMS_LGD_SAVE_COUNT_CHK_10 = Util
								.trim(lmsService
										.getSysParamDataValue("LMS_LGD_SAVE_COUNT_CHK_10"));
						if (Util.equals(LMS_LGD_SAVE_COUNT_CHK_10, "Y")) {
							// other.errMsg10=借款人統編「{0}」之「3.計算合併關係企業授信授權額度頁籤【瑕疵押匯額度合計】」不得大於異動前。
							throw new CapMessageException(MessageFormat.format(
									comPop.getProperty("other.errMsg10"),
									errFlawAmtRcTotal.keySet().toString()),
									getClass());
						}
					}
				}
			}
		}
		// ********************************************************
		// 檢核計算授信額度合計-全案

		if (true) {
			if (!errLgdTotMgAmtCust.isEmpty()) {
				if (l120m01a != null) {
					String LMS_LGD_SAVE_COUNT_CHK_5 = Util.trim(lmsService
							.getSysParamDataValue("LMS_LGD_SAVE_COUNT_CHK_5"));
					if (Util.equals(LMS_LGD_SAVE_COUNT_CHK_5, "Y")) {
						// other.errMsg5=頁籤「3.計算授信授權額度之」之各組LGD合計數值，不得大於頁籤「2.計算授信授權額度」所有借款人加總之各組LGD合計。
						throw new CapMessageException(
								comPop.getProperty("other.errMsg5"), getClass());
					}
				}

			}

			if (!errFlawAmtMgTotal.isEmpty()) {
				if (l120m01a != null) {
					String LMS_LGD_SAVE_COUNT_CHK_7 = Util.trim(lmsService
							.getSysParamDataValue("LMS_LGD_SAVE_COUNT_CHK_7"));
					if (Util.equals(LMS_LGD_SAVE_COUNT_CHK_7, "Y")) {
						// other.errMsg7=頁籤「3.計算授信授權額度之」之【瑕疵押匯額度合計】數值，不得大於頁籤「2.計算授信授權額度」所有借款人加總之【瑕疵押匯額度合計】合計。
						throw new CapMessageException(
								comPop.getProperty("other.errMsg7"), getClass());
					}
				}
			}

			if (!errGeneralLoanMgTotAmtCust.isEmpty()) {
				if (l120m01a != null) {
					String LMS_LGD_SAVE_COUNT_CHK_6 = Util.trim(lmsService
							.getSysParamDataValue("LMS_LGD_SAVE_COUNT_CHK_6"));
					if (Util.equals(LMS_LGD_SAVE_COUNT_CHK_6, "Y")) {
						// other.errMsg6=頁籤「3.計算授信授權額度之」之【總授信授權額度合計】數值，不得大於頁籤「2.計算授信授權額度」所有借款人加總之【總授信授權額度合計】合計。
						throw new CapMessageException(
								comPop.getProperty("other.errMsg6"), getClass());
					}
				}
			}

		}

		// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
		lmsService.setSuggestCaseLvlMode(mainId, suggestMode);

		// J-111-0461 建議授權層級
		// set完值後利用LGD_X來計算建議層級
		Map<String, L140M01A> needCountLGDMap = lmsService
				.getNeedCountLgdTotalL140m01a(l140m01as);
		lmsService.suggestCaseLvlCheck(mainId, l120m01a, l140m01as,
				needCountLGDMap);

		this.saveL140m01aList(l140m01as);
		return;
	}

	/**
	 * 把依據借款人id 把相對應的資料放到map
	 * 
	 * @param jsonCountEdit
	 *            form的資料
	 * @param size
	 *            筆數
	 * @return
	 */
	private HashMap<String, JSONObject> putCustIdAndMoney(
			JSONObject jsonCountEdit, int size, L120M01A l120m01a,
			String itemType) {

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
		L140M01A l140m01aLgd = null;
		List<L140M01A> l140m01as = this.findL140m01aListByL120m01cMainId(
				l120m01a.getMainId(), itemType, null);
		if (l140m01as != null && !l140m01as.isEmpty()) {
			l140m01aLgd = l140m01as.get(0);
		}

		Map<String, String> lgdMap = lmsService.getLgdTotAmtParam(l140m01aLgd,
				null);
		int lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
				"lmsLgdCount", "0"));

		// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		boolean needShowGeneralLoanTotal = lmsService.needShowGeneralLoanTotal(
				l120m01a, l120m01a.getCaseBrId(), itemType);

		// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
		boolean needShowLoanCountRcTotal = lmsService.needShowLoanCountRcTotal(
				l120m01a, l120m01a.getCaseBrId(), itemType);

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		// l140m01a 或 itemType 擇一
		boolean showLgdTotAmt = lmsService.showLgdTotAmt(l120m01a,
				user.getUnitNo(), itemType);

		// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
		String suggestMode = Util.trim(jsonCountEdit.optString("suggestMode",
				"1"));

		LinkedHashMap<String, JSONObject> custCount = new LinkedHashMap<String, JSONObject>();

		// 計算全案授信授權額度-只會有一筆，所以只抓第一筆的合併*******************************************************
		JSONObject contactZ = new JSONObject();
		// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
		contactZ.put(瑕疵押匯額度合計_全案, "0");
		contactZ.put(總授信額度合計_全案, "0");
		contactZ.put(LGD合計_全案_幣別, "TWD");
		for (int x = 1; x <= lmsLgdCount; x++) {
			contactZ.put(LGD合計_全案 + "_" + x, "0");
		}

		if (Util.equals(suggestMode, "2")) {
			if (needShowGeneralLoanTotal) {
				// suggestMode = 2 2.全案所有借款人授權
				contactZ.put(瑕疵押匯額度合計_全案,
						jsonCountEdit.optString(瑕疵押匯額度合計_全案 + 0, "0"));
				contactZ.put(總授信額度合計_全案,
						jsonCountEdit.optString(總授信額度合計_全案 + 0, "0"));
				contactZ.put(LGD合計_全案_幣別,
						jsonCountEdit.optString(LGD合計_全案_幣別 + 0));
			}

			if (showLgdTotAmt) {
				for (int x = 1; x <= lmsLgdCount; x++) {
					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					// suggestMode = 2 2.全案所有借款人授權
					if (needShowGeneralLoanTotal) {
						contactZ.put(LGD合計_全案 + "_" + x, jsonCountEdit
								.optString(LGD合計_全案 + "_" + x + 0, "0"));
					}
				}
			}
		}
		// ********************************************************************************

		for (int i = 0; i < size; i++) {
			// 使用者統編跟重覆序號
			JSONObject contact = new JSONObject();
			String custId = jsonCountEdit.getString("custId" + i);
			contact.put("curr", jsonCountEdit.getString("countCurr" + i));
			contact.put(授信額度合計, jsonCountEdit.getString(授信額度合計 + i));
			contact.put(擔保授信合計, jsonCountEdit.getString(擔保授信合計 + i));
			contact.put(衍生性商品原始合計, jsonCountEdit.getString(衍生性商品原始合計 + i));
			contact.put(衍生性商品相當合計, jsonCountEdit.getString(衍生性商品相當合計 + i));
			contact.put(前准額度批覆合計, jsonCountEdit.getString(前准額度批覆合計 + i));
			contact.put(前准額度批覆擔保合計, jsonCountEdit.getString(前准額度批覆擔保合計 + i));
			contact.put(減額額度合計, jsonCountEdit.getString(減額額度合計 + i));
			contact.put(減額擔保額度合計, jsonCountEdit.getString(減額擔保額度合計 + i));

			// J-111-0461_05097_B1004 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
			contact.put(瑕疵押匯額度合計, "0");
			contact.put(總授信額度合計, "0");
			if (needShowGeneralLoanTotal) {
				// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
				contact.put(瑕疵押匯額度合計, jsonCountEdit.getString(瑕疵押匯額度合計 + i));
				contact.put(總授信額度合計, jsonCountEdit.getString(總授信額度合計 + i));
			}

			// J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
			contact.put(單獨另計授權額度合計, "0");
			if (showLgdTotAmt) {
				// 金爺:單獨另計授權額度合計取消
				// contact.put(單獨另計授權額度合計, jsonCountEdit.getString(單獨另計授權額度合計 +
				// i));
			}

			// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
			contact.put(瑕疵押匯額度合計_全案, contactZ.getString(瑕疵押匯額度合計_全案));
			contact.put(總授信額度合計_全案, contactZ.getString(總授信額度合計_全案));
			contact.put(LGD合計_全案_幣別, contactZ.getString(LGD合計_全案_幣別));

			// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
			contact.put(瑕疵押匯額度合計_合併關係, "0");
			contact.put(總授信額度合計_合併關係, "0");
			if (needShowGeneralLoanTotal && needShowLoanCountRcTotal) {
				// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
				contact.put(瑕疵押匯額度合計_合併關係,
						jsonCountEdit.getString(瑕疵押匯額度合計_合併關係 + i));
				contact.put(總授信額度合計_合併關係,
						jsonCountEdit.getString(總授信額度合計_合併關係 + i));
			}

			if (jsonCountEdit.containsKey(授信額度合計多幣別說明 + i)) {
				contact.put(授信額度合計多幣別說明,
						jsonCountEdit.getString(授信額度合計多幣別說明 + i));
			} else {
				contact.put(授信額度合計多幣別說明, "");
			}
			if (jsonCountEdit.containsKey(擔保授信合計多幣別說明 + i)) {
				contact.put(擔保授信合計多幣別說明,
						jsonCountEdit.getString(擔保授信合計多幣別說明 + i));
			} else {
				contact.put(擔保授信合計多幣別說明, "");
			}
			if (jsonCountEdit.containsKey(衍生性授信額度合計多幣別說明 + i)) {
				contact.put(衍生性授信額度合計多幣別說明,
						jsonCountEdit.getString(衍生性授信額度合計多幣別說明 + i));
			} else {
				contact.put(衍生性授信額度合計多幣別說明, "");
			}

			// initial
			for (int x = 1; x <= lmsLgdCount; x++) {
				contact.put(LGD合計 + "_" + x, "0");

				// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
				contact.put(LGD合計_全案 + "_" + x, "0");

				// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
				contact.put(LGD合計_合併關係 + "_" + x, "0");
			}
			if (showLgdTotAmt) {

				// J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
				for (int x = 1; x <= lmsLgdCount; x++) {
					contact.put(LGD合計 + "_" + x,
							jsonCountEdit.getString(LGD合計 + "_" + x + i));

					// J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					// suggestMode = 2 2.全案所有借款人授權
					contact.put(LGD合計_全案 + "_" + x,
							contactZ.getString(LGD合計_全案 + "_" + x));

					if (needShowLoanCountRcTotal) {
						// J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
						contact.put(
								LGD合計_合併關係 + "_" + x,
								jsonCountEdit.getString(LGD合計_合併關係 + "_" + x
										+ i));
					}
				}

				// contact.put(LGD其中無擔保合計, jsonCountEdit.getString(LGD其中無擔保合計 +
				// i));
				// contact.put(LGD其中擬制部分擔保合計,
				// jsonCountEdit.getString(LGD其中擬制部分擔保合計 + i));
				// contact.put(LGD其中擬制十足擔保合計,
				// jsonCountEdit.getString(LGD其中擬制十足擔保合計 + i));
			}

			custCount.put(custId, contact);
		}
		return custCount;
	}

	/**
	 * J-111-0411_05097_B1001 Web e-Loan企金授信新增不動產授信例外管理相關功能
	 * 
	 * @param l140m01a
	 * @return
	 */
	@Override
	public boolean displayIntRegReason(L140S06A l140s06a) {
		boolean isShow = false;

		// 5.工業用地及廠房貸款作業要點」
		// 32.興建房屋及土地抵押貸款管理要點
		// 36.辦理都市更新授信作業須知
		// 46.辦理危險及老舊建築物重建貸款要點

		String intReg = l140s06a.getIntReg();
		if (!Util.isEmpty(intReg)) {

			String LMS_L140M01A_NEED_INTREGREASON = Util.trim(lmsService
					.getSysParamDataValue("LMS_L140M01A_NEED_INTREGREASON"));
			if (Util.notEquals(LMS_L140M01A_NEED_INTREGREASON, "")) {
				for (String xx : LMS_L140M01A_NEED_INTREGREASON.split(",")) {
					if (Util.equals(xx, intReg)) {
						isShow = true;
						break;
					}
				}
			}
		}

		return isShow;

	}

	@Override
	public L140M01E_AF findL140m01e_afByUniqueKey(String mainId, String shareBrId) {
		return l140m01e_afDao.findByUniqueKey(mainId, shareBrId);
	}
	
	@Override
	public void deleteL140m01e_afList(String[] oids) {
		List<L140M01E_AF> l140m01e_afList = l140m01e_afDao.findByOids(oids);
		if (!l140m01e_afList.isEmpty()) {
			l140m01e_afDao.delete(l140m01e_afList);
		}

	}
}
