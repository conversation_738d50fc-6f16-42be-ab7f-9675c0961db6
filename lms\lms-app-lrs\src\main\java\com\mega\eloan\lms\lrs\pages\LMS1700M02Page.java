
package com.mega.eloan.lms.lrs.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import com.iisigroup.cap.component.PageParameters;

import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.model.L170M01B;

import tw.com.jcs.common.Util;


@Controller
@RequestMapping("/lrs/lms1700m02")
public class LMS1700M02Page extends AbstractEloanForm {

	@Autowired
	RetrialService retrialService;

	public LMS1700M02Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		
		String l170m01b_oid = Util.trim(params.getString("l170m01b_oid"));
		L170M01B l170m01b = null;
		if(Util.isNotEmpty(l170m01b_oid)){
			l170m01b = retrialService.findL170M01B_oid(l170m01b_oid); 
		}
		if(l170m01b==null){
			l170m01b = new L170M01B();
		}

		boolean rSYS = l170m01b.getLnDataDate()!=null;
		boolean rPEO = l170m01b.getLnDataDate()==null;
		
	    model.addAttribute("HS_SUBJECT_R", !LrsUtil.isL170M01B_Subject_W(l170m01b));
        model.addAttribute("HS_SUBJECT_W", LrsUtil.isL170M01B_Subject_W(l170m01b));
	        
        model.addAttribute("HS_SYS_CNTRNO", rSYS);
        model.addAttribute("HS_PEO_CNTRNO", rPEO);

        model.addAttribute("HS_SYS_REVOLVE", rSYS);
        model.addAttribute("HS_PEO_REVOLVE", rPEO);

        model.addAttribute("HS_SYS_QUOTA", rSYS);
        model.addAttribute("HS_PEO_QUOTA", rPEO);

        model.addAttribute("HS_SYS_FROMENDDATE", rSYS);
        model.addAttribute("HS_PEO_FROMENDDATE", rPEO);	
        
        renderJsI18N(LMS1700M02Page.class);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		//L170M01B 不是 extends Meta, 而是 extends GenericBean
		return null;
	}
	
	@Override
	protected String getViewName() {
		return "common/pages/None";
	}
}
