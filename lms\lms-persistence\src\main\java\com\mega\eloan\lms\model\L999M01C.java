/* 
 * L999M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 企金約據書連保人(保證人)檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L999M01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "custPos" }))
public class L999M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 性質(相關身份)
	 * <p/>
	 * C：共同借款人或共同發票人<br/>
	 * D.共同發票人(企金)<br/>
	 * E：票據債務人（指金融交易之擔保背書）<br/>
	 * G：連帶保證人或擔保品提供人兼連帶保證人<br/>
	 * L：連帶債務人或擔保品提供人兼連帶債務人<br/>
	 * N：一般保證人或擔保品提供人兼一般保證人<br/>
	 * S：擔保品提供人
	 */
	@Column(name = "CUSTPOS", length = 1, columnDefinition = "CHAR(1)")
	private String custPos;

	/**
	 * 名稱
	 * <p/>
	 * 資料來源：LMS.L120S01A.custName
	 */
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 郵遞區號
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 */
	@Column(name = "ADDRZIP", columnDefinition = "DECIMAL(5,0)")
	private Integer addrZip;

	/**
	 * 地址(縣市)
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 */
	@Column(name = "ADDRCITY", length = 12, columnDefinition = "VARCHAR(12)")
	private String addrCity;

	/**
	 * 地址(區鄉鎮市)
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 */
	@Column(name = "ADDRTOWN", length = 12, columnDefinition = "VARCHAR(12)")
	private String addrTown;

	/**
	 * 地址
	 * <p/>
	 * (含縣市、鄉鎮市區)<br/>
	 * 資料來源：LMS.L120S01B.cmpAddr
	 */
	@Column(name = "ADDR", length = 300, columnDefinition = "VARCHAR(300)")
	private String addr;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得性質(相關身份)
	 * <p/>
	 * C：共同借款人或共同發票人<br/>
	 * D.共同發票人(企金)<br/>
	 * E：票據債務人（指金融交易之擔保背書）<br/>
	 * G：連帶保證人或擔保品提供人兼連帶保證人<br/>
	 * L：連帶債務人或擔保品提供人兼連帶債務人<br/>
	 * N：一般保證人或擔保品提供人兼一般保證人<br/>
	 * S：擔保品提供人
	 */
	public String getCustPos() {
		return this.custPos;
	}

	/**
	 * 設定性質(相關身份)
	 * <p/>
	 * C：共同借款人或共同發票人<br/>
	 * D.共同發票人(企金)<br/>
	 * E：票據債務人（指金融交易之擔保背書）<br/>
	 * G：連帶保證人或擔保品提供人兼連帶保證人<br/>
	 * L：連帶債務人或擔保品提供人兼連帶債務人<br/>
	 * N：一般保證人或擔保品提供人兼一般保證人<br/>
	 * S：擔保品提供人
	 **/
	public void setCustPos(String value) {
		this.custPos = value;
	}

	/**
	 * 取得名稱
	 * <p/>
	 * 資料來源：LMS.L120S01A.custName
	 */
	public String getCustName() {
		return this.custName;
	}

	/**
	 * 設定名稱
	 * <p/>
	 * 資料來源：LMS.L120S01A.custName
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得郵遞區號
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 */
	public Integer getAddrZip() {
		return this.addrZip;
	}

	/**
	 * 設定郵遞區號
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 **/
	public void setAddrZip(Integer value) {
		this.addrZip = value;
	}

	/**
	 * 取得地址(縣市)
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 */
	public String getAddrCity() {
		return this.addrCity;
	}

	/**
	 * 設定地址(縣市)
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 **/
	public void setAddrCity(String value) {
		this.addrCity = value;
	}

	/**
	 * 取得地址(區鄉鎮市)
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 */
	public String getAddrTown() {
		return this.addrTown;
	}

	/**
	 * 設定地址(區鄉鎮市)
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 **/
	public void setAddrTown(String value) {
		this.addrTown = value;
	}

	/**
	 * 取得地址
	 * <p/>
	 * (含縣市、鄉鎮市區)<br/>
	 * 資料來源：LMS.L120S01B.cmpAddr
	 */
	public String getAddr() {
		return this.addr;
	}

	/**
	 * 設定地址
	 * <p/>
	 * (含縣市、鄉鎮市區)<br/>
	 * 資料來源：LMS.L120S01B.cmpAddr
	 **/
	public void setAddr(String value) {
		this.addr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
