/**
 * ContextHolder.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
*/
package tw.com.iisi.cap.operation;

import java.io.Serializable;

import com.iisigroup.cap.component.PageParameters;

/**
 * <pre>
 * 從Application Context中取得對應資訊
 * </pre>
 * 
 * <AUTHOR>
 * @version $Revision: 372 $
 * @version
 *          <ul>
 *          <li>2010/7/22,iristu,new
 *          </ul>
 */
public interface ContextHolder {

    /**
     * 取得Request參數
     * 
     * @return
     */
    PageParameters getParameters();

    /**
     * 設置Request參數
     * 
     * @param params
     *            Request傳入資訊
     */
    void setParameters(PageParameters params);

    /**
     * 取得回傳結果
     * 
     * @return
     */
    Serializable getResult();

    /**
     * 設置回傳結果
     * 
     * @param result
     */
    void setResult(Serializable result);

    /**
     * 取得資料
     * 
     * @param key
     *            欄位名
     * @return
     */
    Object getData(String key);

    /**
     * 設置資料
     * 
     * @param key
     *            欄位名
     * @param value
     *            儲存資料
     * @return
     */
    ContextHolder setData(String key, Object value);

}
