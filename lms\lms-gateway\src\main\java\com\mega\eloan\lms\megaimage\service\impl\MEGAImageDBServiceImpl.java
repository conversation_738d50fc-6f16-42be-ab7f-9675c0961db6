/*
 * MEGAImageServiceImpl
 */
package com.mega.eloan.lms.megaimage.service.impl;

import java.sql.Types;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.SqlParameter;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.exception.CapMessageException;

import com.mega.eloan.lms.megaimage.service.MEGAImageDBService;

/**
 * <pre>
 * 取得MEGAImage Table
 * </pre>
 * 
 * @since 2022/12/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/12/07,009763,new
 *          <li>2023/05/15,009763,因JAVA6無法使用TLS1.2與SQLSERVER連線<BR>
 *          改透過GW方式作業，此之程式不再使用
 *          </ul>
 */
@Service
@Deprecated
public class MEGAImageDBServiceImpl extends AbstractMEGAImageJdbc implements MEGAImageDBService {

	protected final Logger logger = LoggerFactory.getLogger(getClass());

	@Override
	public void changeBranchCodeByCaseNo(String caseNo, String branchCd) throws CapMessageException {
		logger.info("changeBranchCodeByCaseNo=>CLCaseNo:" + caseNo + ",BranchCode:" + branchCd);
		String spName = "dbo.changeBranchCodeByCaseNo";

		Object[] inParamsVal = { caseNo, branchCd };
		SqlParameter[] params = {
				new SqlParameter("CLCaseNo", Types.VARCHAR),
				new SqlParameter("BranchCode", Types.VARCHAR)};

		this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

}
