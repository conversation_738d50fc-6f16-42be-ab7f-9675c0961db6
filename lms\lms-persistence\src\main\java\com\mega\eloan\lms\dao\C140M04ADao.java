/* 
 * C140M04ADao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C140M04A;


/**
 * <pre>
 * 徵信調查報告書第四章主檔
 * </pre>
 * 
 * @since 2011/9/23
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
public interface C140M04ADao extends IGenericDao<C140M04A> {

	/**
	 * 以mainId查詢徵信調查報告書第四章主檔
	 * @param mainId 文件編號
	 * @return C140M04A
	 */
	List<C140M04A> findByMainId(String mainId);
	
	List<C140M04A> findByCustIdDupId(String custId,String DupNo);

}
