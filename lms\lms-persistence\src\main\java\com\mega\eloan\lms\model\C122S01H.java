/* 
 * C122S01H.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 進件狀態明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C122S01H", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","flowId"}))
public class C122S01H extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * uid<p/>
	 * not null (先放mainid，未來有複製功能的時候在改成複製的mainid)
	 */
	@Size(max=32)
	@Column(name="UID", length=32, columnDefinition="CHAR(32)")
	private String uid;

	/** 
	 * 流程代號<p/>
	 * 1. 借保人資料<br/>
	 *  2. 案件簽報書<br/>
	 *  3. 消金契約書<br/>
	 *  4. 動用審核表
	 */
	@Size(max=1)
	@Column(name="FLOWID", length=1, columnDefinition="CHAR(1)")
	private String flowId;

	/** 對應案件mainId **/
	@Size(max=32)
	@Column(name="REMAINID", length=32, columnDefinition="CHAR(32)")
	private String remainId;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 流程狀態 **/
	@Size(max=3)
	@Column(name="FLOWDOCSTATUS", length=3, columnDefinition="CHAR(3)")
	private String flowdocStatus;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得uid<p/>
	 * not null (先放mainid，未來有複製功能的時候在改成複製的mainid)
	 */
	public String getUid() {
		return this.uid;
	}
	/**
	 *  設定uid<p/>
	 *  not null (先放mainid，未來有複製功能的時候在改成複製的mainid)
	 **/
	public void setUid(String value) {
		this.uid = value;
	}

	/** 
	 * 取得流程代號<p/>
	 * 1. 借保人資料<br/>
	 *  2. 案件簽報書<br/>
	 *  3. 消金契約書<br/>
	 *  4. 動用審核表
	 */
	public String getFlowId() {
		return this.flowId;
	}
	/**
	 *  設定流程代號<p/>
	 *  1. 借保人資料<br/>
	 *  2. 案件簽報書<br/>
	 *  3. 消金契約書<br/>
	 *  4. 動用審核表
	 **/
	public void setFlowId(String value) {
		this.flowId = value;
	}

	/** 取得對應案件mainId **/
	public String getRemainId() {
		return this.remainId;
	}
	/** 設定對應案件mainId **/
	public void setRemainId(String value) {
		this.remainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得流程狀態 **/
	public String getFlowdocStatus() {
		return this.flowdocStatus;
	}
	/** 設定流程狀態 **/
	public void setFlowdocStatus(String value) {
		this.flowdocStatus = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}
}
