package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01G;


/** 企金分析與評估檔 **/
public interface L120S01GDao extends IGenericDao<L120S01G> {

	L120S01G findByOid(String oid);
	
	List<L120S01G> findByMainId(String mainId);
	
	List<L120S01G> findByTypeMainId(String mainId,String dataType);
	
	L120S01G findByUniqueKey(String mainId,String custId,String dupNo,String dataType);

	List<L120S01G> findByCustIdDupId(String custId,String DupNo);
	int delModel(String mainId);
}