package com.mega.eloan.lms.mfaloan.service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.sso.model.IBranch;

/**
 * <pre>
 * 授信授權人員檔 MIS.ELF447
 * </pre>
 * 
 * @since 2012/1/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/6,jessica,new
 *          </ul>
 */
public interface MisELF447Service {

	/**
	 * (管理報表) 5. 授信案件統計表
	 * 
	 * @param ovUnitNo
	 * @param benDate
	 * @param endDate
	 * @param otherCondition
	 * @return
	 */
	List<Map<String, Object>> findElf447forNewReportType5ByBrNo(
			List<IBranch> ovUnitNo, String benDate, String endDate,
			String otherCondition);

	/**
	 * 上傳MIS ELF447
	 * 
	 * @param staffNo
	 * @param approveTime
	 * @param staffJob
	 * @param caseBrid
	 * @param caseBrid2
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param mainId
	 * @param caseLvl
	 * @param property
	 * @param upTime
	 * @param currentApplyAmt
	 * @param tCaseNo
	 * @param LoanTotCurr
	 * @param LoanTotAmt
	 * @param assureTotCurr
	 * @param assureTotAmt
	 * @param LoanTotZCurr
	 * @param LoanTotZAmt
	 * @param LoanTotLCurr
	 * @param LoanTotLAmt
	 * @param LVCurr
	 * @param LVAmt
	 * @param currentApplyCurr
	 * @param liHaiBank
	 * @param liHai44
	 * @param liHai45
	 * @param sysNo
	 */
	void insertElf447(String staffNo, Date approveTime, String staffJob,
			String caseBrid, String caseBrid2, String custId, String dupNo,
			String cntrNo, String mainId, String caseLvl, String property,
			Timestamp upTime, double currentApplyAmt, String tCaseNo,
			String LoanTotCurr, double LoanTotAmt, String assureTotCurr,
			double assureTotAmt, String LoanTotZCurr, double LoanTotZAmt,
			String LoanTotLCurr, double LoanTotLAmt, String LVCurr,
			double LVAmt, String currentApplyCurr, String liHaiBank,
			String liHai44, String liHai45, String sysNo);

	
	public void insertELF4472(List<Object[]> dataList,List<Object[]> deleteList);
	/**
	 * 更新MIS ELF447
	 * 
	 * @param staffNo
	 * @param approveTime
	 * @param staffJob
	 * @param caseBrid
	 * @param caseBrid2
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param mainId
	 * @param caseLvl
	 * @param property
	 * @param currentApplyAmt
	 * @param tCaseNo
	 * @param LoanTotCurr
	 * @param LoanTotAmt
	 * @param assureTotCurr
	 * @param assureTotAmt
	 * @param LoanTotZCurr
	 * @param LoanTotZAmt
	 * @param LoanTotLCurr
	 * @param LoanTotLAmt
	 * @param LVCurr
	 * @param LVAmt
	 * @param currentApplyCurr
	 * @param liHaiBank
	 * @param liHai44
	 * @param liHai45
	 * @param sysNo
	 */
	void upDateElf447(String staffNo, Date approveTime, String staffJob,
			String caseBrid, String caseBrid2, String custId, String dupNo,
			String cntrNo, String mainId, String caseLvl, String property,
			double currentApplyAmt, String tCaseNo, String LoanTotCurr,
			double LoanTotAmt, String assureTotCurr, double assureTotAmt,
			String LoanTotZCurr, double LoanTotZAmt, String LoanTotLCurr,
			double LoanTotLAmt, String LVCurr, double LVAmt,
			String currentApplyCurr, String liHaiBank, String liHai44,
			String liHai45, String sysNo);

	/**
	 * 刪除MIS ELF447
	 * 
	 * @param mainId
	 */
	void delElf447(String mainId);

	/**
	 * 刪除MIS ELF447
	 * 
	 * @param UNID
	 *            unid
	 * @param BRNO
	 *            分行代號
	 */
	public void delByUNID(String UNID, String BRNO);

	int findByUnid(String mainId);
	
	public String findProperty1UnidByCntrNo(String cntrNo);
	public List<Map<String, Object>> findForC241M01A(String unid);
	
	public Map<String, Object> findLandOrConstructionLoanContrNo(String cntrNo);

}
