/* 
 * RATETBL.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.lms.validation.group.Check;

/** 額度信用評等資料檔 **/
@Entity
@Table(name="RATETBL", uniqueConstraints = @UniqueConstraint(columnNames = {"curr","dataYmd"}))
public class RATETBL extends GenericBean  {

	private static final long serialVersionUID = 1L;

	/** 幣別 **/
	@Size(max=3)
	@Column(name="CURR", length=3, columnDefinition="CHAR(3)")
	private String curr;

	/** 日期 **/
	@Size(max=7)
	@Column(name="DATAYMD", length=7, columnDefinition="CHAR(7)")
	private String dataYmd;

	/** 匯率 **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="ENDRATE", columnDefinition="DECIMAL(9,5)")
	private BigDecimal endRate;

	/** 日期 **/
	@Size(max=10)
	@Column(name="RATEYMD", length=10, columnDefinition="CHAR(10)")
	private String rateYmd;

	/** 取得幣別 **/
	public String getCurr() {
		return this.curr;
	}
	/** 設定幣別 **/
	public void setCurr(String value) {
		this.curr = value;
	}

	/** 取得日期 **/
	public String getDataYmd() {
		return this.dataYmd;
	}
	/** 設定日期 **/
	public void setDataYmd(String value) {
		this.dataYmd = value;
	}

	/** 取得匯率 **/
	public BigDecimal getEndRate() {
		return this.endRate;
	}
	/** 設定匯率 **/
	public void setEndRate(BigDecimal value) {
		this.endRate = value;
	}

	/** 取得日期 **/
	public String getRateYmd() {
		return this.rateYmd;
	}
	/** 設定日期 **/
	public void setRateYmd(String value) {
		this.rateYmd = value;
	}
}
