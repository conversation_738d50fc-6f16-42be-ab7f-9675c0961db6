var initS08dJson = {
	isSea : false,	
	handlerName : null,
	gridName : null,
	// 設定formHandler名稱
	setHandler : function(){
		if(responseJSON.docURL == "/lms/lms1205m01"){
			// 授權外企金(海外)
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1105m01"){
			// 授權內企金(海外)
			this.handlerName = "lms1105formhandler";
		}else if(responseJSON.docURL == "/lms/lms1215m01"){
			// 授權外個金(海外)
			this.handlerName = "lms1215formhandler";
		}else if(responseJSON.docURL == "/lms/lms1115m01"){
			// 授權內個金(海外)
			this.handlerName = "lms1115formhandler";
		}else if(responseJSON.docURL == "/lms/lms1305m01"){
			// 陳復述案(海外)
			this.handlerName = "lms1305formhandler";
		}else if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金(國內)
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金(國內)
			this.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金(國內)
			this.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			// 授權內個金(國內)
			this.handlerName = "lms1111formhandler";
		}else{
			// 陳復述案(國內)
			this.handlerName = "lms1301formhandler";
		}		
	},
	// 設定GridHandler名稱
	setGHandler : function(){
		if(responseJSON.typCd == "5"){
			// (海外)
			this.gridName = "lms1201gridhandler";
			this.isSea = true;
		}else{
			// (國內)
			this.gridName = "lms1201gridhandler";
			this.isSea = false;
		}		
	},	
	// 設定Grid相關參數
	setDataGrid : function(){
			return {
				branchId : $("#tformL120m01e5").find("#branchId").val(),
				cmsCustId : $("#tformL120m01e5").find("#cmsCustId").val(),
				cmsDupNo : $("#tformL120m01e5").find("#cmsDupNo").val(),
				collTyp1 : $("#tformL120m01e5").find("#collTyp1").val()
			};	
/*
		var radioVal = $("input[name='rdanbow']:radio:checked").val();
		if(radioVal == "1"){
			// by 分行
			return {
				branchId : $("#tformL120m01e5").find("#branchId").val(),
				type : 1
			};
		}else if(radioVal == "2"){
			// 簽報書主要借款人
			return {
				mainId : responseJSON.mainId,
				type : 2
			};
		}else if(radioVal == "3"){
			// 擔保品大類
			return {
				collTyp1 : $("#tformL120m01e5").find("#collTyp1").val(),
				type : 3
			};
		}
*/		
	},
	// 查詢後的前端設定
	afterQuery : function(jsonInit){
		$("#formL120m01e").find("#docDscr5").html(DOMPurify.sanitize(jsonInit.formL120m01e.docDscr5));
		if(jsonInit.formL120m01e.docDscr5 != "" 
		&& jsonInit.formL120m01e.docDscr5 != undefined 
		&& jsonInit.formL120m01e.docDscr5 != null){
			$("#docDscr5 a").attr({"href":"#"});	
		}else{
			$("#docDscr5").html("");
			$("#docDscr5").val("");
		}		
	}
};

var _handler = "";
initDfd.done(function() {
	//2012_07_20_rex add 取得sso 連線資訊 
	// 若測試Dev連結，則下面程式碼需註解掉
	BrowserAction.init();
	setCloseConfirm(true);
	initS08dJson.setHandler();
	initS08dJson.setGHandler();
	beforeClearCMS();
	
	//J-112-0586_05097_B1001 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
	$("#docDscr5 a").live( "click", function() {
		//擔保品估價報告書
		getCms(DOMPurify.sanitize(this.title));
	});
	
});

/**
 * 更新擔保品Grid
 */
function cmsGrid(sendData) {
	$("#griddanbow2").jqGrid("setGridParam", {
		handler : initS08dJson.gridName,
		action : "queryCMS",
	    postData: $.extend({
			rowNum : 10
	    },sendData || {}),
		search : true
	}).trigger("reloadGrid");
}

/**
 * 更新清除擔保品Grid
 */
function ubeforeClearCMS() {
	$("#clearGrid").jqGrid("setGridParam", {
		handler : initS08dJson.gridName,
		action : "beforeClearCMS",
	    postData: {
			rowNum : 10,
			mainId : responseJSON.mainId
	    },
		search : true
	}).trigger("reloadGrid");
}
	
/**
 * 擔保品連結
 */
function getCms(cms){	
	$.ajax({ // 查詢主要借款人資料
		handler : initS08dJson.handlerName,
		type : "POST",
		dataType : "json",
		data : {
			formAction : "getRelate3",
			mainId : responseJSON.mainId,
			cmsMainId : cms
		},
		success : function(json) {
			
			var isHis = "N";
			if(json.OWNBRID == userInfo.unitNo){
				isHis = "Y";
			}
			// 擔保品
			// 舊方法
			BrowserAction.submit({
				system : "cms",
		        url : json.url,
		        mainId : cms,
		        txCode : json.txCode,
				oid : json.oid,
		        mainOid:json.mainOid,
				mainDocStatus : json.mainDocStatus,				
		        data :{
					//其它參數
					//txCode : json.txCode,
					oid : json.oid,
		        	mainOid:json.mainOid,
					mainDocStatus : json.mainDocStatus,
					collKind : json.collKind,
					collTyp2 : json.collTyp2,
					//不動產
					pndFlag : json.pndFlag,
					isHis : isHis
		       }
			});			
			// MegaApi.linkToMS 新的連結方法
/*
		      MegaApi.linkToMS({
			      	system : 'CMS',
			        url : json.url,
			        mainId : cms,
			        stxCode : json.txCode,
			        data :{
						//其它參數
						txCode : json.txCode,
						oid : json.oid,
			        	mainOid:json.mainOid,
						mainDocStatus : json.mainDocStatus,
						collKind : json.collKind,
						collTyp2 : json.collTyp2,
						//不動產
						pndFlag : json.pndFlag,
						isHis : "N"
			       }
			  });
*/			
		}
	});
}

/**
 * 擔保品分頁查詢
 */
function seachKind5() {
	if($("#lmss08d_page").attr("open") == "true"){
		$("#lmss08d_page").load("../../lms/baselmss08d",function(){
			initS08dJson.setGHandler();
			griddanbow2();
/*
			$("#tformL120m01e5").find("#hchildMap").hide();
			$("#tformL120m01e5").find("#hbranchId").hide();			
			//使用者點選擔保品種類Radio的動作
			$("#tformL120m01e5").find("input[name='rdanbow']:radio").click(function(){
				if($(this).val() == '1'){
					$("#tformL120m01e5").find("#hbranchId").show();
					$("#tformL120m01e5").find("#hchildMap").hide();
				}else if($(this).val() == '2'){
					$("#tformL120m01e5").find("#hchildMap").hide();
					$("#tformL120m01e5").find("#hbranchId").hide();			
				}else if($(this).val() == '3'){
					$("#tformL120m01e5").find("#hchildMap").show();
					$("#tformL120m01e5").find("#hbranchId").hide();
				}
			});
*/	
			
			// 擔保品查詢
			$("#showdanbow").click(function() {
				if($("#tformL120m01e5").valid()){
					$("#hidedanbowtr").show();
					cmsGrid(initS08dJson.setDataGrid());
				}
			});
			danBowThick();
		});
		$("#lmss08d_page").attr("open",false);
	}else{
		danBowThick();		
	}	
}

/**
 * 擔保品明細ThickBox
 */
function danBowThick(){
	//$("#tformL120m01e5").find("input[name='rdanbow']:radio").attr("checked", false);
	$("#hidedanbowtr").hide();
	$("#hidedanbow2").hide();
	$("#griddanbow2").setGridParam({'selrow' : null});
	$("#tformL120m01e5 #cmsCustId").val($("#showBorrowData #custId").html());
	$("#tformL120m01e5 #cmsDupNo").val($("#showBorrowData #dupNo").html());
	//$("#griddanbow2").resetSelection();

	// 載入擔保品大類
	var obj= CommonAPI.loadCombos("cms1090_collTyp1");
	$("#tformL120m01e5").find("#collTyp1").setItems({
		item:obj.cms1090_collTyp1,
		format : "{value} - {key}",
		space: true
	});	

	// 載入所有分行Map
	$("#tformL120m01e5").find("#branchId").setItems({
		 item: getAllBranchMap().childMap,
		 value : userInfo.unitNo,
		 format : "{value} - {key}",
		 space: true
	});
	
	$("#seachKind5").thickbox({ // 使用選取的內容進行彈窗
		title : (initS08dJson.isSea)?i18n.lmss08["L120S08.thickbox8"] : i18n.lmss08a["L120S08.thickbox8"],
		width : 850,
		height : 450,
		modal : true,
		align : "center",
		valign : "bottom",
		i18n : i18n.def,
		buttons : {
			"sure" : function() {
				var $formL120m01e = $("#formL120m01e");
				var rows3 = $("#griddanbow2").getGridParam('selarrrow');
				var list3 = "";
				var sign3 = ",";
				for (var k=0;k<rows3.length;k++){	//將所有已選擇的資料存進變數list裡面
					if (rows3[k] != 'undefined' && rows3[k] != null && rows3[k] != 0){
						var data3 = $("#griddanbow2").getRowData(rows3[k]);
						list3 += ((list3 == "") ? "" : sign3 ) + data3.mainId;						
					}
				}				
				if (list3 != "") {
					var list = "";
					var data = "";
					//取得確切選擇的那筆資料文件編號(MainId)
					list = list3;
					data = data3; 
					$.ajax({ // 查詢主要借款人資料
						handler : initS08dJson.handlerName,
						type : "POST",
						dataType : "json",
						data : {
							formAction : "findRelate3",
							mainId : responseJSON.mainId,
							formL120m01e : JSON.stringify($formL120m01e.serializeData()),
							cmsMainId : list
						},
						success : function(json) {
							//alert(JSON.stringify(json));
							$formL120m01e.find("#docDscr5").html(DOMPurify.sanitize(json.docDscr5));
							if(json.docDscr5){
								$formL120m01e.find("#docDscr5 a").attr({"href":"#"});	
							}							
						}
					});
					$.thickbox.close();
					$.thickbox.close();
				}else{
					//並未選擇任何資料
					CommonAPI.showMessage((initS08dJson.isSea)?i18n.lmss08["L120S08.alert1"] : i18n.lmss08a["L120S08.alert1"]);
				}				
				
				$.thickbox.close();
			},
			"cancel" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});	
}

/**
 * 擔保品Grid
 */
function griddanbow2() {	
	var griddanbow2 = $("#griddanbow2").iGrid({
		handler : initS08dJson.gridName,
		height : 225,
		//sortname: 'estDate',
        //sortname: 'estDate|docStatus|collKind',
        //sortorder : 'desc|asc|asc',		
		action : "queryCMS",
	    //postData: initS08dJson.gridJson,
		caption: "&nbsp;",
		hiddengrid : false,
		//rownumbers : true,
		rowNum : 10,
		multiselect: true,
		hideMultiselect:false,				
		colModel : [ {
			colHeader : (initS08dJson.isSea)?i18n.lmss08["L120S08.grid7"] : i18n.lmss08a["L120S08.grid7"],
			name : 'estDate',
			align : "left",
			width : 150,
			sortable : false
		}, {
			colHeader : (initS08dJson.isSea)?i18n.lmss08["L120S08.grid8"] : i18n.lmss08a["L120S08.grid8"],
			name : 'custName',
			align : "left",
			width : 200,
			sortable : false
		}, {
			colHeader : (initS08dJson.isSea)?i18n.lmss08["L120S08.grid9"] : i18n.lmss08a["L120S08.grid9"],
			name : 'collKind',
			align : "left",
			width : 80,
			sortable : false
		}, {
			colHeader : (initS08dJson.isSea)?i18n.lmss08["L120S08.grid10"] : i18n.lmss08a["L120S08.grid10"],
			name : 'docStatus',
			align : "left",
			width : 80,
			sortable : false
		}, {
			colHeader : (initS08dJson.isSea)?i18n.lmss08["L120S08.grid11"] : i18n.lmss08a["L120S08.grid11"],
			name : 'loanAmt',
			align : "right",
			width : 150,
			sortable : false
		}, {
			name : 'mainId',
			hidden : true
		}, {
			name : 'oid',
			hidden : true
		} ],
		ondblClickRow : function(rowid) {
		}
	});
}

/**
 * 清除擔保品Grid
 */
function beforeClearCMS() {	
	var clearGrid = $("#clearGrid").iGrid({
		handler : initS08dJson.gridName,
		height : 225,
		//sortname: 'estDate',
        //sortname: 'estDate|docStatus|collKind',
        //sortorder : 'desc|asc|asc',		
		action : "beforeClearCMS",
	    //postData: initS08dJson.gridJson,
		caption: "&nbsp;",
		hiddengrid : false,
		//rownumbers : true,
		rowNum : 10,
		multiselect: true,
		hideMultiselect:false,				
		colModel : [ {
			colHeader : (initS08dJson.isSea)?i18n.lmss08["L120S08.index8"] : i18n.lmss08a["L120S08.index8"],
			name : 'docDscr',
			align : "left",
			width : 200,
			sortable : false
		}, {
			name : 'mainId',
			hidden : true
		}, {
			name : 'oid',
			hidden : true
		}, {
			name : 'docOid',
			hidden : true
		} ],
		ondblClickRow : function(rowid) {
		}
	});
}

/**
 * 擔保品清除內容
 */
function clearDanCont(docType){
	initS08dJson.setGHandler();
	ubeforeClearCMS();
	$("#tclearDanBow").thickbox({ // 使用選取的內容進行彈窗
		title : (initS08dJson.isSea)?i18n.lmss08["L120S08.btn4"] : i18n.lmss08a["L120S08.btn4"],
		width : 850,
		height : 450,
		modal : true,
		align : "center",
		valign : "bottom",
		i18n : i18n.def,
		buttons : {
			"sure" : function() {
				var rows = $("#clearGrid").getGridParam('selarrrow');
				var list = "";
				var sign = ",";
				for (var k=0;k<rows.length;k++){	//將所有已選擇的資料存進變數list裡面
					if (rows[k] != 'undefined' && rows[k] != null && rows[k] != 0){
						var data = $("#clearGrid").getRowData(rows[k]);
						list += ((list == "") ? "" : sign ) + data.docOid;						
					}
				}				
				if (list != "") {
					//取得確切選擇的那筆資料文件編號(MainId)
					$.ajax({ // 查詢主要借款人資料
						handler : initS08dJson.handlerName,
						type : "POST",
						dataType : "json",
						data : {
							formAction : "clearContent",
							mainId : responseJSON.mainId,
							list : list,
							docType : docType
						},
						success : function(json) {
							if(docType == "3"){
								$("#formL120m01e").find("#docDscr5").html(DOMPurify.sanitize(json.content));
								if(json.content != "" 
								&& json.content != undefined 
								&& json.content != null){
									$("#docDscr5 a").attr({"href":"#"});	
								}else{
									$("#docDscr5").html("");
									$("#docDscr5").val("");
								}
							}
						}
					});
					$.thickbox.close();
					$.thickbox.close();
				}else{
					//並未選擇任何資料
					CommonAPI.showMessage((initS08dJson.isSea)?i18n.lmss08["L120S08.alert1"] : i18n.lmss08a["L120S08.alert1"]);
				}				
				
				$.thickbox.close();
			},
			"cancel" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});		
}

/**
 * 取得所有分行Map
 */
function getAllBranchMap(){
	var jsonData = {};
	$.ajax({ // 查詢主要借款人資料
		handler : initS08dJson.handlerName,
		type : "POST",
		dataType : "json",
		async: false,
		data : {
			formAction : "getAllBranchMap"
		},
		success : function(json) {
			jsonData = json;			
		}
	});
	return jsonData;	
}