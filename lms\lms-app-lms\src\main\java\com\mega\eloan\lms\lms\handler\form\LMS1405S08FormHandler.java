/*
 * LMSM01FormHandler.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.handler.form.LMSCOMMONFormHandler;
import com.mega.eloan.lms.base.service.LMSLgdService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.impl.EloandbBASEServiceImpl;
import com.mega.eloan.lms.lms.panels.LMS1405S08Panel;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S21A;
import com.mega.eloan.lms.model.L120S21B;
import com.mega.eloan.lms.model.L120S21C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 額度明細表 - AIRB (EAD/LGD) 
 * J-110-0986_05097_B1001 於簽報書新增LGD欄位
 * </pre>
 * 
 * @since 2021/10/
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/10/,009301,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1405s08formhandler")
@DomainClass(L120M01A.class)
public class LMS1405S08FormHandler extends LMSCOMMONFormHandler {

	@Resource
	LMSService lmsService;

	@Resource
	LMSLgdService lmsLgdService;

	@Resource
	LMS1205Service lms1205Service;

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisMISLN20Service misMisLN20Service;

	@Resource
	MisELLNGTEEService misEllngteeService;

	@Resource
	SysParameterService sysParamService;

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	BranchService branchService;

	@Resource
	DwdbBASEService dwdbBaseService;

	private static final Logger LOGGER = LoggerFactory
			.getLogger(EloandbBASEServiceImpl.class);

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s21a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String cntrNoCo_s21a = Util.nullToSpace(params
				.getString("cntrNoCo_s21a"));

		result.set("cntrNoCo_s21a", "");
		result.set("BFcntrNoCo_s21a", "");
		result.set("currCo_s21a", "");
		result.set("factAmtCo_s21a", "");
		List<L120S21A> l120s21aList = lmsLgdService
				.findL120s21aByMainIdAndCntrNoCo(mainId, cntrNoCo_s21a);
		if (l120s21aList != null && !l120s21aList.isEmpty()) {
			L120S21A l120s21a = l120s21aList.get(0);
			result.set("cntrNoCo_s21a",
					Util.nullToSpace(l120s21a.getCntrNoCo_s21a()));
			result.set("BFcntrNoCo_s21a",
					Util.nullToSpace(l120s21a.getCntrNoCo_s21a()));
			result.set("currCo_s21a",
					Util.nullToSpace(l120s21a.getCurrCo_s21a()));
			result.set("factAmtCo_s21a",
					Util.nullToSpace(l120s21a.getFactAmtCo_s21a()));
		}
		return result;
	}

	public IResult saveL120S21A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String cntrNoCo_s21a = Util.nullToSpace(params
				.getString("cntrNoCo_s21a"));
		String BFcntrNoCo_s21a = Util.nullToSpace(params
				.getString("BFcntrNoCo_s21a"));
		String currCo_s21a = Util.nullToSpace(params.getString("currCo_s21a"));
		String factAmtCo_s21a = params.getString("factAmtCo_s21a");

		boolean chgCntrNoCo = false;
		if (Util.notEquals(cntrNoCo_s21a, BFcntrNoCo_s21a)) {
			chgCntrNoCo = true; // 變更 共用額度
		}
		// if(Util.isEmpty(BFcntrNoCo_s21a)){
		// BFcntrNoCo_s21a = cntrNoCo_s21a;
		// }
		List<L120S21A> newL120s21aList = new ArrayList<L120S21A>();
		List<L120S21A> l120s21aList = lmsLgdService
				.findL120s21aByMainIdAndCntrNoCo(mainId, BFcntrNoCo_s21a);
		if (l120s21aList != null && !l120s21aList.isEmpty()) {
			for (L120S21A l120s21a : l120s21aList) {
				if (chgCntrNoCo) {
					l120s21a.setCntrNoCo_s21a(cntrNoCo_s21a);
				}
				l120s21a.setCurrCo_s21a(Util.isEmpty(currCo_s21a) ? null
						: currCo_s21a);
				l120s21a.setFactAmtCo_s21a(factAmtCo_s21a == null ? BigDecimal.ZERO
						: Util.parseBigDecimal(factAmtCo_s21a.replace(",", "")));
				newL120s21aList.add(l120s21a);
			}
			lmsLgdService.saveL120s21aList(newL120s21aList);
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s21aSub(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));

		result.set("oid_s21a", oid);
		result.set("cntrNo_s21a", "");
		result.set("currentApplyCurr_s21a", "");
		result.set("currentApplyAmt_s21a", "");
		result.set("blCurr_s21a", "");
		result.set("blAmt_s21a", "");
		result.set("rcvCurr_s21a", "");
		result.set("rcvInt_s21a", "");
		result.set("reUse_s21a", "");

		L120S21A l120s21a = lmsLgdService.findL120s21aByOid(oid);
		if (l120s21a != null) {
			result.set("oid_s21a", Util.nullToSpace(l120s21a.getOid()));
			result.set("cntrNo_s21a",
					Util.nullToSpace(l120s21a.getCntrNo_s21a()));
			result.set("currentApplyCurr_s21a",
					Util.nullToSpace(l120s21a.getCurrentApplyCurr_s21a()));
			result.set("currentApplyAmt_s21a",
					Util.nullToSpace(l120s21a.getCurrentApplyAmt_s21a()));
			result.set("blCurr_s21a",
					Util.nullToSpace(l120s21a.getBlCurr_s21a()));
			result.set("blAmt_s21a", Util.nullToSpace(l120s21a.getBlAmt_s21a()));
			result.set("rcvCurr_s21a",
					Util.nullToSpace(l120s21a.getRcvCurr_s21a()));
			result.set("rcvInt_s21a",
					Util.nullToSpace(l120s21a.getRcvInt_s21a()));
			result.set("reUse_s21a", Util.nullToSpace(l120s21a.getReUse_s21a()));
		}
		return result;
	}

	public IResult saveL120S21Asub(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S08Panel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String type = Util.nullToSpace(params.getString("type"));
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String cntrNo_s21a = Util.nullToSpace(params.getString("cntrNo_s21a"));
		String cntrNoCo_s21a = Util.nullToSpace(params
				.getString("cntrNoCo_s21a"));
		String currCo_s21a = Util.nullToSpace(params.getString("currCo_s21a"));
		String factAmtCo_s21a = params.getString("factAmtCo_s21a");

		if (Util.equals(type, "new")) {
			List<L120S21A> l120s21aList = lmsLgdService
					.findL120s21aByMainIdAndCntrNoCo(mainId, cntrNoCo_s21a);
			if (l120s21aList != null && !l120s21aList.isEmpty()) {
				for (L120S21A l120s21a : l120s21aList) {
					if (Util.equals(cntrNo_s21a,
							Util.nullToSpace(l120s21a.getCntrNo_s21a()))) {
						throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤,
								pop.getProperty("errMsg01")), getClass());
					}
				}
			}
		}

		L120S21A l120s21a = lmsLgdService.findL120s21aByOid(oid);
		if (l120s21a != null) {

		} else {
			l120s21a = new L120S21A();
			l120s21a.setMainId(mainId);
			l120s21a.setCntrNoCo_s21a(cntrNoCo_s21a);
			l120s21a.setCurrCo_s21a(Util.isEmpty(currCo_s21a) ? null
					: currCo_s21a);
			l120s21a.setFactAmtCo_s21a(factAmtCo_s21a == null ? BigDecimal.ZERO
					: Util.parseBigDecimal(factAmtCo_s21a.replace(",", "")));
			l120s21a.setCntrNo_s21a(cntrNo_s21a);
			l120s21a.setCreator(user.getUserId());
			l120s21a.setCreateTime(CapDate.getCurrentTimestamp());
		}
		String currentApplyCurr_s21a = Util.nullToSpace(params
				.getString("currentApplyCurr_s21a"));
		String currentApplyAmt_s21a = params.getString("currentApplyAmt_s21a");
		String blCurr_s21a = Util.nullToSpace(params.getString("blCurr_s21a"));
		String blAmt_s21a = params.getString("blAmt_s21a");
		String rcvCurr_s21a = Util
				.nullToSpace(params.getString("rcvCurr_s21a"));
		String rcvInt_s21a = params.getString("rcvInt_s21a");
		String reUse_s21a = Util.nullToSpace(params.getString("reUse_s21a"));
		l120s21a.setCurrentApplyCurr_s21a(Util.isEmpty(currentApplyCurr_s21a) ? null
				: currentApplyCurr_s21a);
		l120s21a.setCurrentApplyAmt_s21a(currentApplyAmt_s21a == null ? BigDecimal.ZERO
				: Util.parseBigDecimal(currentApplyAmt_s21a.replace(",", "")));
		l120s21a.setBlCurr_s21a(Util.isEmpty(blCurr_s21a) ? null : blCurr_s21a);
		l120s21a.setBlAmt_s21a(blAmt_s21a == null ? BigDecimal.ZERO : Util
				.parseBigDecimal(blAmt_s21a.replace(",", "")));
		l120s21a.setBlAmtTwd_s21a(BigDecimal.ZERO);
		l120s21a.setRcvCurr_s21a(Util.isEmpty(rcvCurr_s21a) ? null
				: rcvCurr_s21a);
		l120s21a.setRcvInt_s21a(rcvInt_s21a == null ? BigDecimal.ZERO : Util
				.parseBigDecimal(rcvInt_s21a.replace(",", "")));
		l120s21a.setRcvIntTwd_s21a(BigDecimal.ZERO);
		l120s21a.setReUse_s21a(Util.trim(reUse_s21a));

		lmsLgdService.save(l120s21a);

		lmsLgdService.syncL120s21bInfo(mainId, false);

		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
		// RespMsgHelper.getMainMessage(
		// this.getComponent(), UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult delL120S21A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// String cntrNoCo_s21a = Util.nullToSpace(params
		// .getString("cntrNoCo_s21a"));

		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.trim(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);

		for (String cntrNoCo_s21a : oidArray) {
			List<L120S21A> l120s21aList = lmsLgdService
					.findL120s21aByMainIdAndCntrNoCo(mainId, cntrNoCo_s21a);
			if (l120s21aList != null && !l120s21aList.isEmpty()) {
				lmsLgdService.deleteListL120s21a(l120s21aList);
			}
		}

		lmsLgdService.syncL120s21bInfo(mainId, false);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult delL120S21AByOid(PageParameters params)
			throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.trim(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);
		// 刪除
		lmsLgdService.deleteListL120s21aByOid(oidArray);

		lmsLgdService.syncL120s21bInfo(mainId, false);

		// 印出刪除成功訊息
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;

	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s21b(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));

		L120S21B l120s21b = lmsLgdService.findL120s21bByOid(oid);
		result = DataParse.toResult(l120s21b, DataParse.Delete, new String[] {
				EloanConstants.MAIN_ID, EloanConstants.OID });

		// J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
		// 下面這行很重要，因為會把comma消掉變成 1000000，沒這行會guarantorCptlUnit_s21b會變成 1,000,000
		result.set("guarantorCptlUnit_s21b",
				Util.trim(l120s21b.getGuarantorCptlUnit_s21b()));

		// J-112-0210_05097_B1002 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
		// 股票、公司債、可轉換公司債擔保品，自建時請增加是否為「同一關係企業」之勾選欄位
		// 030600,030601,030602,030603,030700,030701
		String LGD_L120S21C_ISELCRECOM_SHOW = Util.trim(lmsService
				.getSysParamDataValue("LGD_L120S21C_ISELCRECOM_SHOW"));
		JSONArray showIsElcreComItem = new JSONArray();
		result.set("showIsElcreComItem", "");
		if (Util.notEquals(LGD_L120S21C_ISELCRECOM_SHOW, "")) {
			for (String sbj : LGD_L120S21C_ISELCRECOM_SHOW.split(",")) {
				showIsElcreComItem.add(sbj);
			}
			result.set("showIsElcreComItem", showIsElcreComItem.toString());
		}

		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveL120s21b(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuffer temp = new StringBuffer();
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S08Panel.class);

		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String formL120s21b = Util.nullToSpace(params
				.getString("formLgdDetail"));
		L120S21B l120s21b = lmsLgdService.findL120s21bByOid(oid);
		if (l120s21b == null) {
			l120s21b = new L120S21B();
			l120s21b.setMainId(mainId);
			l120s21b.setCreator(user.getUserId());
			l120s21b.setCreateTime(CapDate.getCurrentTimestamp());
		}

		L120M01A l120m01a = lms1205Service.findL120m01aByMainId(l120s21b
				.getMainId());
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		DataParse.toBean(formL120s21b, l120s21b);

		// 加檢核**************************************************************

		// J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
		lmsLgdService.chkL120s21b(l120s21b);

		// 計算*****************************************************************
		BigDecimal collateralRecoveryOth = BigDecimal.ZERO;// 未建檔
		// 未建檔

		List<L120S21C> l120s21clist2 = lmsLgdService
				.findL120s21cByMainIdAndCollType(l120s21b.getMainId(),
						l120s21b.getCntrNo_s21b(), "2"); // 未建檔
		if (l120s21clist2 != null && !l120s21clist2.isEmpty()) {
			// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
			// 重算未建檔擔保品明細
			for (L120S21C l120s21c : l120s21clist2) {
				String calcMsg = lmsLgdService.calcL120s21c(l120s21c);
				if (Util.notEquals(calcMsg, "")) {
					throw new CapMessageException(calcMsg, getClass());
				}
				lmsLgdService.save(l120s21c);
			}

			for (L120S21C l120s21c : l120s21clist2) {
				// J-110-0485_05097_B1006 Web
				// e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
				if (Util.notEquals(
						Util.getLeftStr(l120s21c.getColKind_s21c(), 4), "0503")) {
					collateralRecoveryOth = collateralRecoveryOth.add(l120s21c
							.getColRecoveryTwd_s21c() == null ? BigDecimal.ZERO
							: l120s21c.getColRecoveryTwd_s21c());
				}

			}
		}

		// 如果聯貸案且兆豐為擔保品管理行=Y，要乘上攤貸比例
		// 擔保品建檔的時候呼叫擔保品API時已經乘上攤貸比例
		// 針對未建擔保品的才要乘攤貸比例

		// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
		// 已經改成明細儲存時計算
		// if (Util.equals(l120s21b.getUnionFlag(), "Y")) {
		// collateralRecoveryOth = collateralRecoveryOth.multiply(l120s21b
		// .getSyndAmt().divide(l120s21b.getUnionAmt(), 16,
		// BigDecimal.ROUND_HALF_UP));
		// }

		l120s21b.setCollateralRecoveryOth(collateralRecoveryOth);

		l120s21b.setHasCntrDoc_s21b(lmsLgdService.isHasCntrDoc(mainId,
				Util.nullToSpace(l120s21b.getCntrNo_s21b())) ? "Y" : "N");
		// J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		lmsLgdService.clearL120s21bLgdData(l120s21b);
		lmsLgdService.save(l120s21b);

		result = DataParse.toResult(l120s21b, DataParse.Delete, new String[] {
				EloanConstants.MAIN_ID, EloanConstants.OID });

		// J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
		// 下面這行很重要，因為會把comma消掉變成 1000000，沒這行會guarantorCptlUnit_s21b會變成 1,000,000
		result.set("guarantorCptlUnit_s21b",
				Util.trim(l120s21b.getGuarantorCptlUnit_s21b()));

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getNewCntrNoCo_s21a(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S08Panel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String cntrNoCo_s21a = Util.trim(params.getString("cntrNoCo_s21a", ""));
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String newCoBrno_s21a = Util.trim(params
				.getString("newCoBrno_s21a", ""));
		List<L120S21A> newL120s21aList = new ArrayList<L120S21A>();

		String newCntrNoCo_s21aStr = "";

		L120M01A l120m01a = lms1205Service.findL120m01aByMainId(mainId);
		String unitCode = "6"; // 額度共管編號
		String classCd = "0";
		String brNo = Util.equals(newCoBrno_s21a, "") ? l120m01a.getCaseBrId()
				: newCoBrno_s21a;
		Map<String, Object> cntrNo = lms1405Service.queryLnsp0050(brNo,
				unitCode, classCd);
		// 是否成功
		if ("YES".equals(cntrNo.get("chkFlag"))) {
			newCntrNoCo_s21aStr = Util.trim((String) cntrNo.get("cntrNo"))
					+ "EL";
		} else {
			HashMap<String, String> msg = new HashMap<String, String>();
			msg.put("msg", Util.trim((String) cntrNo.get("errMsg")));
			// 錯誤訊息
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());

		}

		List<L120S21A> s21aOrgList = lmsLgdService
				.findL120s21aByMainIdAndCntrNoCo(mainId, newCntrNoCo_s21aStr);
		if (s21aOrgList != null && !s21aOrgList.isEmpty()
				&& s21aOrgList.size() > 0) {
			// errMsg03=共用序號已產生，請至該筆資料內進行維護！
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤,
					newCntrNoCo_s21aStr + pop.getProperty("errMsg03")), getClass());
		} else {
			List<L120S21A> l120s21aList = lmsLgdService
					.findL120s21aByMainIdAndCntrNoCo(mainId, cntrNoCo_s21a);
			if (l120s21aList != null && !l120s21aList.isEmpty()
					&& l120s21aList.size() > 0) {
				for (L120S21A l120s21a : l120s21aList) {
					l120s21a.setCntrNoCo_s21a(newCntrNoCo_s21aStr);
					newL120s21aList.add(l120s21a);
				}
			}
		}
		if (newL120s21aList.size() > 0) {
			lmsLgdService.saveL120s21aList(newL120s21aList);
		}

		result.set("newCntrNoCo_s21aStr", newCntrNoCo_s21aStr);

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult cleanCntrNoCo_s21a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String cntrNoCo_s21a = Util.trim(params.getString("cntrNoCo_s21a", ""));
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		List<L120S21A> newL120s21aList = new ArrayList<L120S21A>();

		List<L120S21A> l120s21aList = lmsLgdService
				.findL120s21aByMainIdAndCntrNoCo(mainId, cntrNoCo_s21a);
		if (l120s21aList != null && !l120s21aList.isEmpty()
				&& l120s21aList.size() > 0) {
			for (L120S21A l120s21a : l120s21aList) {
				l120s21a.setCntrNoCo_s21a("");
				newL120s21aList.add(l120s21a);
			}
		}
		if (newL120s21aList.size() > 0) {
			lmsLgdService.saveL120s21aList(newL120s21aList);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult chkCntrNoCo_s21a(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S08Panel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String cntrNoCo_s21a = Util.trim(params.getString("cntrNoCo_s21a", ""));
		String orgCntrNoCo_s21a = Util.trim(params.getString(
				"orgCntrNoCo_s21a", ""));
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		Boolean save = params.getAsBoolean("save", false);
		List<L120S21A> newL120s21aList = new ArrayList<L120S21A>();

		List<L120S21A> s21aOrgList = lmsLgdService
				.findL120s21aByMainIdAndCntrNoCo(mainId, orgCntrNoCo_s21a);
		if (s21aOrgList != null && !s21aOrgList.isEmpty()
				&& s21aOrgList.size() > 0) {
			// errMsg03=共用序號已產生，請至該筆資料內進行維護！
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤,
					pop.getProperty("errMsg03")), getClass());
		} else {
			List<L120S21A> l120s21aList = lmsLgdService
					.findL120s21aByMainIdAndCntrNoCo(mainId, cntrNoCo_s21a);
			if (l120s21aList != null && !l120s21aList.isEmpty()) {
				for (L120S21A l120s21a : l120s21aList) {
					l120s21a.setCntrNoCo_s21a(orgCntrNoCo_s21a);
					newL120s21aList.add(l120s21a);
				}
			}
		}
		if (newL120s21aList.size() > 0) {
			lmsLgdService.saveL120s21aList(newL120s21aList);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult importCoData(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String cntrNoCo_s21a = Util.trim(params.getString("cntrNoCo_s21a", ""));
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		L120M01A l120m01a = lms1205Service.findL120m01aByMainId(mainId);
		// 依目前簽案行做計算幣別
		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());

		List<L120S21A> newL120s21aList = new ArrayList<L120S21A>();
		List<L120S21A> l120s21aList = lmsLgdService
				.findL120s21aByMainIdAndCntrNoCo(mainId, cntrNoCo_s21a);
		if (l120s21aList != null && !l120s21aList.isEmpty()) {
			lmsLgdService.deleteListL120s21a(l120s21aList); // 全部刪掉重新引進
		}

		String currCo_s21a = "";
		BigDecimal factAmtCo_s21a = BigDecimal.ZERO;
		List<Map<String, Object>> lnf025List = misdbBASEService
				.findlnf025ByCntrNoCo(cntrNoCo_s21a);
		for (Map<String, Object> lnf025 : lnf025List) {
			String LNF025_CONTRACT = Util.nullToSpace(MapUtils.getString(
					lnf025, "LNF025_CONTRACT"));
			if (Util.isEmpty(LNF025_CONTRACT)) {
				continue; // 共管序號資料列
			}

			String LIMIT_SWIFT = MapUtils.getString(lnf025, "LIMIT_SWIFT");
			BigDecimal LIMIT_FACT_AMT = Util.equals(
					MapUtils.getString(lnf025, "LIMIT_FACT_AMT"), "") ? BigDecimal.ZERO
					: Util.parseBigDecimal(MapUtils.getString(lnf025,
							"LIMIT_FACT_AMT"));
			if (LIMIT_FACT_AMT.compareTo(BigDecimal.ZERO) == 0) {
				continue; // 限額為0時，不是額度共管，而是科目共管
			}

			currCo_s21a = LIMIT_SWIFT;
			factAmtCo_s21a = LIMIT_FACT_AMT;
			L120S21A l120s21a = new L120S21A();
			l120s21a.setMainId(mainId);
			l120s21a.setCntrNoCo_s21a(cntrNoCo_s21a);
			l120s21a.setCurrCo_s21a(LIMIT_SWIFT);
			l120s21a.setFactAmtCo_s21a(LIMIT_FACT_AMT);
			l120s21a.setCntrNo_s21a(LNF025_CONTRACT);
			/*
			 * 引進時先不轉，計算時再統一換台幣 if (Util.notEquals(currCo_s21a, "TWD")) {
			 * l120s21a.setFactAmtCoTwd_s21a(branchRate.toTWDAmt(
			 * (Util.isEmpty(currCo_s21a) ? "TWD" : currCo_s21a),
			 * (factAmtCo_s21a == null ? BigDecimal.ZERO : factAmtCo_s21a))); }
			 * else { l120s21a.setFactAmtCoTwd_s21a(factAmtCo_s21a == null ?
			 * BigDecimal.ZERO : factAmtCo_s21a); }
			 */
			// String LNF020_SWFT = Util.nullToSpace(MapUtils.getString(lnf025,
			// "LNF020_SWFT"));
			// l120s21a.setCurrentApplyCurr_s21a(LNF020_SWFT);
			// BigDecimal LNF020_FACT_AMT = Util.equals(
			// MapUtils.getString(lnf025, "LNF020_FACT_AMT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF020_FACT_AMT"));
			// l120s21a.setCurrentApplyAmt_s21a(LNF020_FACT_AMT);
			// BigDecimal LNF252_FACT_AMT_NT = Util.equals(
			// MapUtils.getString(lnf025, "LNF252_FACT_AMT_NT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF252_FACT_AMT_NT"));
			// l120s21a.setCurrentApplyAmtTwd_s21a(LNF252_FACT_AMT_NT);
			// BigDecimal LNF252_LOAN_BAL_NT = Util.equals(
			// MapUtils.getString(lnf025, "LNF252_LOAN_BAL_NT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF252_LOAN_BAL_NT"));
			// l120s21a.setBlCurr_s21a(UtilConstants.CURR.TWD);
			// l120s21a.setBlAmt_s21a(LNF252_LOAN_BAL_NT);
			// l120s21a.setBlAmtTwd_s21a(LNF252_LOAN_BAL_NT);
			// BigDecimal LNF252_RCV_INT_NT = Util.equals(
			// MapUtils.getString(lnf025, "LNF252_RCV_INT_NT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF252_RCV_INT_NT"));
			// l120s21a.setRcvCurr_s21a(UtilConstants.CURR.TWD);
			// l120s21a.setRcvInt_s21a(LNF252_RCV_INT_NT);
			// l120s21a.setRcvIntTwd_s21a(LNF252_RCV_INT_NT);
			l120s21a.setCreator(user.getUserId());
			l120s21a.setCreateTime(CapDate.getCurrentTimestamp());

			lmsLgdService.setLatestLoanDataByCntrNo(mainId,
					Util.nullToSpace(l120s21a.getCntrNo_s21a()), l120s21a,
					false);

			newL120s21aList.add(l120s21a);
		}

		// 海外
		List<Map<String, Object>> lnf025OvsList = dwdbBaseService
				.findlnf025OvsByCntrNoCo(cntrNoCo_s21a);
		for (Map<String, Object> lnf025Ovs : lnf025OvsList) {
			String LNF025_CONTRACT = Util.nullToSpace(MapUtils.getString(
					lnf025Ovs, "CONTRACT"));
			if (Util.isEmpty(LNF025_CONTRACT)) {
				continue; // 共管序號資料列
			}

			String LIMIT_SWIFT = MapUtils.getString(lnf025Ovs, "LIMIT_SWIFT");
			BigDecimal LIMIT_FACT_AMT = Util.equals(
					MapUtils.getString(lnf025Ovs, "LIMIT_FACT_AMT"), "") ? BigDecimal.ZERO
					: Util.parseBigDecimal(MapUtils.getString(lnf025Ovs,
							"LIMIT_FACT_AMT"));
			if (LIMIT_FACT_AMT.compareTo(BigDecimal.ZERO) == 0) {
				continue; // 限額為0時，不是額度共管，而是科目共管
			}

			currCo_s21a = LIMIT_SWIFT;
			factAmtCo_s21a = LIMIT_FACT_AMT;
			L120S21A l120s21a = new L120S21A();
			l120s21a.setMainId(mainId);
			l120s21a.setCntrNoCo_s21a(cntrNoCo_s21a);
			l120s21a.setCurrCo_s21a(LIMIT_SWIFT);
			l120s21a.setFactAmtCo_s21a(LIMIT_FACT_AMT);
			l120s21a.setCntrNo_s21a(LNF025_CONTRACT);
			/*
			 * 引進時先不轉，計算時再統一換台幣 if (Util.notEquals(currCo_s21a, "TWD")) {
			 * l120s21a.setFactAmtCoTwd_s21a(branchRate.toTWDAmt(
			 * (Util.isEmpty(currCo_s21a) ? "TWD" : currCo_s21a),
			 * (factAmtCo_s21a == null ? BigDecimal.ZERO : factAmtCo_s21a))); }
			 * else { l120s21a.setFactAmtCoTwd_s21a(factAmtCo_s21a == null ?
			 * BigDecimal.ZERO : factAmtCo_s21a); }
			 */
			// String LNF020_SWFT = Util.nullToSpace(MapUtils.getString(lnf025,
			// "LNF020_SWFT"));
			// l120s21a.setCurrentApplyCurr_s21a(LNF020_SWFT);
			// BigDecimal LNF020_FACT_AMT = Util.equals(
			// MapUtils.getString(lnf025, "LNF020_FACT_AMT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF020_FACT_AMT"));
			// l120s21a.setCurrentApplyAmt_s21a(LNF020_FACT_AMT);
			// BigDecimal LNF252_FACT_AMT_NT = Util.equals(
			// MapUtils.getString(lnf025, "LNF252_FACT_AMT_NT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF252_FACT_AMT_NT"));
			// l120s21a.setCurrentApplyAmtTwd_s21a(LNF252_FACT_AMT_NT);
			// BigDecimal LNF252_LOAN_BAL_NT = Util.equals(
			// MapUtils.getString(lnf025, "LNF252_LOAN_BAL_NT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF252_LOAN_BAL_NT"));
			// l120s21a.setBlCurr_s21a(UtilConstants.CURR.TWD);
			// l120s21a.setBlAmt_s21a(LNF252_LOAN_BAL_NT);
			// l120s21a.setBlAmtTwd_s21a(LNF252_LOAN_BAL_NT);
			// BigDecimal LNF252_RCV_INT_NT = Util.equals(
			// MapUtils.getString(lnf025, "LNF252_RCV_INT_NT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF252_RCV_INT_NT"));
			// l120s21a.setRcvCurr_s21a(UtilConstants.CURR.TWD);
			// l120s21a.setRcvInt_s21a(LNF252_RCV_INT_NT);
			// l120s21a.setRcvIntTwd_s21a(LNF252_RCV_INT_NT);
			l120s21a.setCreator(user.getUserId());
			l120s21a.setCreateTime(CapDate.getCurrentTimestamp());
			lmsLgdService.setLatestLoanDataByCntrNo(mainId,
					Util.nullToSpace(l120s21a.getCntrNo_s21a()), l120s21a,
					false);
			newL120s21aList.add(l120s21a);
		}

		if (newL120s21aList.size() > 0) {
			lmsLgdService.saveL120s21aList(newL120s21aList);
		}

		lmsLgdService.syncL120s21bInfo(mainId, false);

		result.set("currCo_s21a", currCo_s21a);
		result.set("factAmtCo_s21a", factAmtCo_s21a);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult impALoan(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String delFlag = Util.nullToSpace(params.getString("delFlag"));
		if (Util.equals(delFlag, "Y")) {
			// 先刪除所有已存在資料
			List<L120S21A> listL120s21a = lmsLgdService
					.findL120s21aByMainId(mainId);
			if (listL120s21a != null && !listL120s21a.isEmpty()) {
				lmsLgdService.deleteListL120s21a(listL120s21a);
			}
		}

		L120M01A l120m01a = lms1205Service.findL120m01aByMainId(mainId);
		// 依目前簽案行做計算幣別
		BranchRate branchRate = lmsService
				.getBranchRate(l120m01a.getCaseBrId());

		List<String> cntrNoList = lmsLgdService.getCntrNoList(mainId);
		// J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		if (cntrNoList == null || cntrNoList.isEmpty()) {
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, "簽報書無額度明細表可引進"),
					getClass());
		}

		List<L120S21A> newL120s21aList = new ArrayList<L120S21A>();

		// 國內*******************************************************************
		List<Map<String, Object>> lnf025List = misdbBASEService
				.findlnf025CoByCntrNoForLgd(cntrNoList);
		for (Map<String, Object> lnf025 : lnf025List) {
			String LNF025_CONTRACT_CO = MapUtils.getString(lnf025,
					"LNF025_CONTRACT_CO");
			String LNF025_CONTRACT = MapUtils.getString(lnf025,
					"LNF025_CONTRACT");

			// 確認DB是否已經存在
			List<L120S21A> existlistL120s20a = lmsLgdService
					.findL120s21aByMainIdAndCntrNoCoAndCntrNo(mainId,
							LNF025_CONTRACT_CO, LNF025_CONTRACT);
			if (existlistL120s20a != null && !existlistL120s20a.isEmpty()) {
				continue; // 已經存在
			}

			String LIMIT_SWIFT = MapUtils.getString(lnf025, "LIMIT_SWIFT");
			BigDecimal LIMIT_FACT_AMT = Util.equals(
					MapUtils.getString(lnf025, "LIMIT_FACT_AMT"), "") ? BigDecimal.ZERO
					: Util.parseBigDecimal(MapUtils.getString(lnf025,
							"LIMIT_FACT_AMT"));
			if (LIMIT_FACT_AMT.compareTo(BigDecimal.ZERO) == 0) {
				continue; // 限額為0時，不是額度共管，而是科目共管
			}

			BigDecimal LIMIT_FACT_AMT_TW = LIMIT_FACT_AMT;
			if (Util.notEquals(LIMIT_SWIFT, "TWD")) {
				LIMIT_FACT_AMT_TW = branchRate.toOtherAmt(LIMIT_SWIFT, "TWD",
						LIMIT_FACT_AMT).setScale(0, BigDecimal.ROUND_HALF_UP);
			}

			// String LNF020_SWFT = MapUtils.getString(lnf025, "LNF020_SWFT");
			// BigDecimal LNF020_FACT_AMT = Util.equals(
			// MapUtils.getString(lnf025, "LNF020_FACT_AMT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF020_FACT_AMT"));
			// BigDecimal LNF252_FACT_AMT_NT = Util.equals(
			// MapUtils.getString(lnf025, "LNF252_FACT_AMT_NT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF252_FACT_AMT_NT"));
			// BigDecimal LNF252_LOAN_BAL_NT = Util.equals(
			// MapUtils.getString(lnf025, "LNF252_LOAN_BAL_NT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF252_LOAN_BAL_NT"));
			// BigDecimal LNF252_RCV_INT_NT = Util.equals(
			// MapUtils.getString(lnf025, "LNF252_RCV_INT_NT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF252_RCV_INT_NT"));

			L120S21A l120s21a = new L120S21A();
			l120s21a.setMainId(mainId);
			l120s21a.setCntrNoCo_s21a(LNF025_CONTRACT_CO);
			l120s21a.setCurrCo_s21a(LIMIT_SWIFT);
			l120s21a.setFactAmtCo_s21a(LIMIT_FACT_AMT);
			l120s21a.setFactAmtCoTwd_s21a(LIMIT_FACT_AMT_TW.setScale(0,
					BigDecimal.ROUND_HALF_UP));
			l120s21a.setCntrNo_s21a(LNF025_CONTRACT);
			// l120s21a.setCurrentApplyCurr_s21a(LNF020_SWFT);
			// l120s21a.setCurrentApplyAmt_s21a(LNF020_FACT_AMT);
			// l120s21a.setCurrentApplyAmtTwd_s21a(LNF252_FACT_AMT_NT);
			// l120s21a.setBlCurr_s21a(UtilConstants.CURR.TWD);
			// l120s21a.setBlAmt_s21a(LNF252_LOAN_BAL_NT);
			// l120s21a.setBlAmtTwd_s21a(LNF252_LOAN_BAL_NT);
			// l120s21a.setRcvCurr_s21a(UtilConstants.CURR.TWD);
			// l120s21a.setRcvInt_s21a(LNF252_RCV_INT_NT);
			// l120s21a.setRcvIntTwd_s21a(LNF252_RCV_INT_NT);
			l120s21a.setCreator(user.getUserId());
			l120s21a.setCreateTime(CapDate.getCurrentTimestamp());

			lmsLgdService.setLatestLoanDataByCntrNo(mainId,
					Util.nullToSpace(l120s21a.getCntrNo_s21a()), l120s21a,
					false);

			newL120s21aList.add(l120s21a);
		}

		// 海外*******************************************************************

		List<Map<String, Object>> lnf025OvsList = dwdbBaseService
				.findlnf025OvsCoByCntrNoForLgd(cntrNoList);
		for (Map<String, Object> lnf025Ovs : lnf025OvsList) {
			String LNF025_CONTRACT_CO = MapUtils.getString(lnf025Ovs,
					"CONTRACT_CO");
			String LNF025_CONTRACT = MapUtils.getString(lnf025Ovs, "CONTRACT");

			// 確認DB是否已經存在
			List<L120S21A> existlistL120s20a = lmsLgdService
					.findL120s21aByMainIdAndCntrNoCoAndCntrNo(mainId,
							LNF025_CONTRACT_CO, LNF025_CONTRACT);
			if (existlistL120s20a != null && !existlistL120s20a.isEmpty()) {
				continue; // 已經存在
			}

			String LIMIT_SWIFT = MapUtils.getString(lnf025Ovs, "LIMIT_SWIFT");
			BigDecimal LIMIT_FACT_AMT = Util.equals(
					MapUtils.getString(lnf025Ovs, "LIMIT_FACT_AMT"), "") ? BigDecimal.ZERO
					: Util.parseBigDecimal(MapUtils.getString(lnf025Ovs,
							"LIMIT_FACT_AMT"));
			if (LIMIT_FACT_AMT.compareTo(BigDecimal.ZERO) == 0) {
				continue; // 限額為0時，不是額度共管，而是科目共管
			}

			BigDecimal LIMIT_FACT_AMT_TW = LIMIT_FACT_AMT;
			if (Util.notEquals(LIMIT_SWIFT, "TWD")) {
				LIMIT_FACT_AMT_TW = branchRate.toOtherAmt(LIMIT_SWIFT, "TWD",
						LIMIT_FACT_AMT).setScale(0, BigDecimal.ROUND_HALF_UP);
			}

			// String LNF020_SWFT = MapUtils.getString(lnf025, "LNF020_SWFT");
			// BigDecimal LNF020_FACT_AMT = Util.equals(
			// MapUtils.getString(lnf025, "LNF020_FACT_AMT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF020_FACT_AMT"));
			// BigDecimal LNF252_FACT_AMT_NT = Util.equals(
			// MapUtils.getString(lnf025, "LNF252_FACT_AMT_NT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF252_FACT_AMT_NT"));
			// BigDecimal LNF252_LOAN_BAL_NT = Util.equals(
			// MapUtils.getString(lnf025, "LNF252_LOAN_BAL_NT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF252_LOAN_BAL_NT"));
			// BigDecimal LNF252_RCV_INT_NT = Util.equals(
			// MapUtils.getString(lnf025, "LNF252_RCV_INT_NT"), "") ?
			// BigDecimal.ZERO
			// : Util.parseBigDecimal(MapUtils.getString(lnf025,
			// "LNF252_RCV_INT_NT"));

			L120S21A l120s21a = new L120S21A();
			l120s21a.setMainId(mainId);
			l120s21a.setCntrNoCo_s21a(LNF025_CONTRACT_CO);
			l120s21a.setCurrCo_s21a(LIMIT_SWIFT);
			l120s21a.setFactAmtCo_s21a(LIMIT_FACT_AMT);
			l120s21a.setFactAmtCoTwd_s21a(LIMIT_FACT_AMT_TW.setScale(0,
					BigDecimal.ROUND_HALF_UP));
			l120s21a.setCntrNo_s21a(LNF025_CONTRACT);
			// l120s21a.setCurrentApplyCurr_s21a(LNF020_SWFT);
			// l120s21a.setCurrentApplyAmt_s21a(LNF020_FACT_AMT);
			// l120s21a.setCurrentApplyAmtTwd_s21a(LNF252_FACT_AMT_NT);
			// l120s21a.setBlCurr_s21a(UtilConstants.CURR.TWD);
			// l120s21a.setBlAmt_s21a(LNF252_LOAN_BAL_NT);
			// l120s21a.setBlAmtTwd_s21a(LNF252_LOAN_BAL_NT);
			// l120s21a.setRcvCurr_s21a(UtilConstants.CURR.TWD);
			// l120s21a.setRcvInt_s21a(LNF252_RCV_INT_NT);
			// l120s21a.setRcvIntTwd_s21a(LNF252_RCV_INT_NT);
			l120s21a.setCreator(user.getUserId());
			l120s21a.setCreateTime(CapDate.getCurrentTimestamp());

			lmsLgdService.setLatestLoanDataByCntrNo(mainId,
					Util.nullToSpace(l120s21a.getCntrNo_s21a()), l120s21a,
					false);

			newL120s21aList.add(l120s21a);
		}

		if (newL120s21aList.size() > 0) {
			lmsLgdService.saveL120s21aList(newL120s21aList);
		}

		lmsLgdService.syncL120s21bInfo(mainId, false);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getLatestLnf252(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String cntrNoCo_s21a = Util.trim(params.getString("cntrNoCo_s21a", ""));
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		List<L120S21A> newL120s21aList = new ArrayList<L120S21A>();
		List<L120S21A> l120s21aList = lmsLgdService
				.findL120s21aByMainIdAndCntrNoCo(mainId, cntrNoCo_s21a);
		if (l120s21aList != null && !l120s21aList.isEmpty()) {
			for (L120S21A l120s21a : l120s21aList) {
				if (lmsLgdService.setLatestLoanDataByCntrNo(mainId,
						Util.nullToSpace(l120s21a.getCntrNo_s21a()), l120s21a,
						false)) {
					newL120s21aList.add(l120s21a);
				}

			}
		}
		if (newL120s21aList.size() > 0) {
			lmsLgdService.saveL120s21aList(newL120s21aList);
		}

		lmsLgdService.syncL120s21bInfo(mainId, false);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult calcEAD(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		String errorMsg = lmsLgdService.calcEADInner(mainId);
		result.set("errorMsg", "");

		if (Util.equals(errorMsg, "")) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		} else {
			result.set("errorMsg", errorMsg);
			// throw new
			// CapMessageException(RespMsgHelper.getMessage(parent,
			// UtilConstants.AJAX_RSP_MSG.執行有誤, "calcLGD「取得分配後擔保品回收」執行失敗！"
			// + errorMsgBuf.toString()), getClass());

		}

		return result;
	}

	/**
	 * XXXXXXXXXXXXXXXX目前沒有使用XXXXXXXXXXXXXXXXX
	 * XXXXXXXXXXXXXXXX目前沒有使用XXXXXXXXXXXXXXXXX
	 * XXXXXXXXXXXXXXXX目前沒有使用XXXXXXXXXXXXXXXXX
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult impCntr(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S08Panel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		// step 1. 檢查額度明細表是否為共管額度序號且都有存在 L120S21A
		List<String> cntrNoList = lmsLgdService.getCntrNoList(mainId);
		List<Map<String, Object>> lnf025List = misdbBASEService
				.findlnf025CoByCntrNoForLgd(cntrNoList);
		Map<String, List<String>> cntrNoMap = new HashMap<String, List<String>>();
		if (lnf025List != null && !lnf025List.isEmpty()) {
			for (Map<String, Object> lnf025 : lnf025List) {
				String LNF025_CONTRACT = Util.nullToSpace(MapUtils.getString(
						lnf025, "LNF025_CONTRACT"));
				String LNF025_CONTRACT_CO = Util.nullToSpace(MapUtils
						.getString(lnf025, "LNF025_CONTRACT_CO"));
				if (Util.isEmpty(LNF025_CONTRACT)) {
					continue; // 共管序號資料列
				} else {
					if (cntrNoList.contains(LNF025_CONTRACT)) {
						if (!cntrNoMap.containsKey(LNF025_CONTRACT_CO)) {
							cntrNoMap.put(LNF025_CONTRACT_CO,
									new ArrayList<String>());
						}
						cntrNoMap.get(LNF025_CONTRACT_CO).add(LNF025_CONTRACT);
					}
				}
			}
		}
		boolean notComplete = false;
		if (cntrNoMap != null && !cntrNoMap.isEmpty()) {
			for (String cntrNoCo : cntrNoMap.keySet()) {
				List<String> cntrNoS = cntrNoMap.get(cntrNoCo);
				for (String no : cntrNoS) {
					List<L120S21A> l120s21aList = lmsLgdService
							.findL120s21aByMainIdAndCntrNoCoAndCntrNo(mainId,
									cntrNoCo, no);
					if (l120s21aList != null && !l120s21aList.isEmpty()) {
						// 有存在
					} else {
						notComplete = true;
						break;
					}
				}
				if (notComplete) {
					break;
				}
			}
		}
		if (notComplete) {
			throw new CapMessageException(
					RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, pop.getProperty("errMsg05")), getClass());
		}

		// step 2. 補齊 L120S21B - 補齊有額度明細表且無共用
		lmsLgdService.furtherL120s21b(mainId);

		// step 3. 清查 S21B - 只留共用及額度明細表
		lmsLgdService.inventoryL120s21b(mainId);

		// step 4. 補齊 L120S21B 資訊
		lmsLgdService.syncL120s21bInfo(mainId, true);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult calcLGD(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		String errMsg = "";

		// 一、重算EAD**********************************
		errMsg = lmsLgdService.calcEADInner(mainId);
		if (Util.notEquals(errMsg, "")) {
			throw new CapMessageException(
					RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, "calcEAD「計算EAD」執行失敗！" + errMsg),
					getClass());
		}

		// 二、計算LGD**********************************
		errMsg = lmsLgdService.calcLGDInner(mainId);
		if (Util.notEquals(errMsg, "")) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, "calcLGD「計算LGD」執行失敗！"
							+ errMsg), getClass());
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s21c(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));
		String cntrNoS21c = Util.nullToSpace(params.getString("cntrNoS21c"));

		result.set("oid_s21c", oid);
		result.set("cntrNo_s21c", cntrNoS21c);

		L120S21C l120s21c = lmsLgdService.findL120s21cByOid(oid);

		result = DataParse.toResult(l120s21c, DataParse.Delete, new String[] {
				EloanConstants.MAIN_ID, EloanConstants.OID });
		if (l120s21c != null) {
			result.set("oid_s21c", Util.nullToSpace(l120s21c.getOid()));
			result.set("cntrNo_s21c",
					Util.nullToSpace(l120s21c.getCntrNo_s21c()));
		}
		return result;
	}

	public IResult saveL120s21c(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));
		String type = Util.nullToSpace(params.getString("type"));
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String cntrNoS21c = Util.nullToSpace(params.getString("cntrNoS21c"));

		String formL120s21c = Util.nullToSpace(params
				.getString("formLgdDetail_S21C"));
		L120S21C l120s21c = lmsLgdService.findL120s21cByOid(oid);
		if (l120s21c == null) {
			l120s21c = new L120S21C();
			l120s21c.setMainId(mainId);
			l120s21c.setCntrNo_s21c(cntrNoS21c);
			l120s21c.setCollType_s21c("2");
			l120s21c.setCreator(user.getUserId());
			l120s21c.setCreateTime(CapDate.getCurrentTimestamp());
		}
		DataParse.toBean(formL120s21c, l120s21c);

		// 加檢核**************************************************************

		// J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
		lmsLgdService.chkL120s21c(l120s21c);

		// 計算*****************************************************************

		String errorMsg = lmsLgdService.calcL120s21c(l120s21c);
		if (Util.notEquals(errorMsg, "")) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, errorMsg), getClass());

		} else {
			lmsLgdService.save(l120s21c);
			result.set("oid_s21c", l120s21c.getOid());
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}

		result = DataParse.toResult(l120s21c, DataParse.Delete, new String[] {
				EloanConstants.MAIN_ID, EloanConstants.OID });
		if (l120s21c != null) {
			result.set("oid_s21c", Util.nullToSpace(l120s21c.getOid()));
			result.set("cntrNo_s21c",
					Util.nullToSpace(l120s21c.getCntrNo_s21c()));
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult delL120S21CByOid(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S08Panel.class);

		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.trim(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);

		boolean hasCollType1 = false;
		List<String> oidArrayNew = new ArrayList();
		for (String oid : oidArray) {
			L120S21C l120s21c = lmsLgdService.findL120s21cByOid(oid);
			if (l120s21c != null) {
				if (Util.equals(Util.trim(l120s21c.getCollType_s21c()), "1")) {
					// 已建檔
					hasCollType1 = true;
				} else {
					oidArrayNew.add(oid);
				}
			}
		}

		// 刪除
		if (oidArrayNew != null && !oidArrayNew.isEmpty()) {
			lmsLgdService.deleteListL120s21cByOid(oidArrayNew
					.toArray(new String[oidArrayNew.size()]));
		}

		// 印出刪除成功訊息
		// errMsg12=已建檔之擔保品資料請透過重新引進擔保品建檔資料按鈕更新
		result.set(
				CapConstants.AJAX_NOTIFY_MESSAGE,
				RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功)
						+ (hasCollType1 ? pop.getProperty("errMsg12") : ""));
		return result;

	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult applyL120s21cCmsData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String cntrNo = Util.nullToSpace(params.getString("cntrNo"));

		// 開始取得分配擔保品
		List<L120S21B> listL120s21b = lmsLgdService
				.findL120s21bByMainId(mainId);
		// Map<String, String> cntrEadMap = new HashMap<String, String>();
		// for (L120S21B l120s21b : listL120s21b) {
		// String tCntrNo = Util.nullToSpace(l120s21b.getCntrNo_s21b());
		// cntrEadMap.put(tCntrNo, l120s21b.getCntrEad_s21b() == null ? "0"
		// : l120s21b.getCntrEad_s21b().toPlainString());
		// }

		// 取得分配擔保品
		L120S21B l120s21b = lmsLgdService.findL120s21bByMainIdAndCntrNo(mainId,
				cntrNo);

		String errorMsg = lmsLgdService.getCollateralRecovery(l120s21b);

		if (Util.notEquals(errorMsg, "")) {
			throw new CapMessageException(
					RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, "calcLGD「取得分配後擔保品回收」執行失敗！"
							+ errorMsg), getClass());
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}

		result.set("collateralRecoveryCms", l120s21b.getCollateralRecoveryCms());

		return result;

	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult updateCollateralRecoveryOth(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String cntrNoS21c = Util.nullToSpace(params.getString("cntrNoS21c"));

		L120S21B l120s21b = lmsLgdService.findL120s21bByMainIdAndCntrNo(mainId,
				cntrNoS21c);

		if (l120s21b == null) {
			return result;
		}
		BigDecimal collateralRecoveryOth = BigDecimal.ZERO;// 未建檔
		// 未建檔

		List<L120S21C> l120s21clist2 = lmsLgdService
				.findL120s21cByMainIdAndCollType(l120s21b.getMainId(),
						l120s21b.getCntrNo_s21b(), "2"); // 未建檔
		if (l120s21clist2 != null && !l120s21clist2.isEmpty()) {
			for (L120S21C l120s21c : l120s21clist2) {
				// J-110-0485_05097_B1008 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
				String othCollKind = Util.trim(l120s21c.getColKind_s21c()); // L120S21C.colKind_s21c=擔保品種類
				if (Util.notEquals(Util.getLeftStr(othCollKind, 4), "0503")) {
					collateralRecoveryOth = collateralRecoveryOth.add(l120s21c
							.getColRecoveryTwd_s21c() == null ? BigDecimal.ZERO
							: l120s21c.getColRecoveryTwd_s21c());
				}
			}
		}

		l120s21b.setProdRecvTwd(null); // 產品自償回收
		l120s21b.setCollateralRecovery(null); // 擔保品預期回收合計
		l120s21b.setCreditRecovery(null); // 信用保證回收
		l120s21b.setExpectSecuredRecovery(null); // A.額度預期擔保品回收=
													// 擔保品預期回收合計
													// + 信用保證回收
		l120s21b.setExpectUnsecuredRecovery(null); // 額度預期無擔保回收

		l120s21b.setPayOffLossRate(null); // 清償損失率

		// J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		lmsLgdService.clearL120s21bLgdData(l120s21b);

		l120s21b.setCollateralRecoveryOth(collateralRecoveryOth);
		lmsLgdService.save(l120s21b);
		// result.set("collateralRecoveryOth", collateralRecoveryOth);

		result = DataParse.toResult(l120s21b, DataParse.Delete, new String[] {
				EloanConstants.MAIN_ID, EloanConstants.OID });

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult canShowDetail(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String ssoUnitNo = user.getSsoUnitNo();
		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean canShowDetail = false;
		boolean canEditReadOnly = false;

		// LMS_LGD_CAN_SHOW_DETAIL 900,912,918 登錄單位可以看到LGD明細參數欄位

		String LMS_LGD_CAN_SHOW_DETAIL = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_CAN_SHOW_DETAIL"));

		// if (Util.notEquals(LMS_LGD_CAN_SHOW_DETAIL, "")) {
		// for (String xx : LMS_LGD_CAN_SHOW_DETAIL.split(",")) {
		// if (Util.equals(xx, ssoUnitNo)) {
		// canShowDetail = true;
		// break;
		// }
		// }
		// }

		String countryType = branchService.getBranch(ssoUnitNo)
				.getCountryType();
		if (Util.notEquals(countryType, "TW")) {
			countryType = "OV";
		}

		if (Util.notEquals(LMS_LGD_CAN_SHOW_DETAIL, "")) {
			for (String xx : LMS_LGD_CAN_SHOW_DETAIL.split(",")) {
				if (Util.equals(xx, ssoUnitNo) || Util.equals(countryType, xx)) {
					canShowDetail = true;
					break;
				}
			}
		}

		// J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		String LMS_LGD_CAN_EDIT_READONLY = Util.trim(lmsService
				.getSysParamDataValue("LMS_LGD_CAN_EDIT_READONLY"));

		if (Util.notEquals(LMS_LGD_CAN_EDIT_READONLY, "")) {
			for (String xx : LMS_LGD_CAN_EDIT_READONLY.split(",")) {
				if (Util.equals(xx, ssoUnitNo)) {
					canEditReadOnly = true;
					break;
				}
			}
		}

		if (!canShowDetail && canEditReadOnly) {
			canEditReadOnly = false;
		}

		result.set("canShowDetail", canShowDetail ? "Y" : "N");

		// J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		result.set("canEditReadOnly", canEditReadOnly ? "Y" : "N");

		return result;
	}

	/**
	 * J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getLatestLnf252All(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		List<L120S21A> newL120s21aList = new ArrayList<L120S21A>();
		List<L120S21A> l120s21aList = lmsLgdService
				.findL120s21aByMainId(mainId);
		if (l120s21aList != null && !l120s21aList.isEmpty()) {
			for (L120S21A l120s21a : l120s21aList) {
				if (lmsLgdService.setLatestLoanDataByCntrNo(mainId,
						Util.nullToSpace(l120s21a.getCntrNo_s21a()), l120s21a,
						false)) {
					newL120s21aList.add(l120s21a);
				}
			}
		}
		if (newL120s21aList.size() > 0) {
			lmsLgdService.saveL120s21aList(newL120s21aList);
		}

		lmsLgdService.syncL120s21bInfo(mainId, false);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

}
