/* 
 * LMS9515M01Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.form;

import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.map.LinkedMap;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.model.LPDFS01A;
import com.mega.eloan.lms.rpt.pages.LMS9530V01Page;
import com.mega.eloan.lms.rpt.service.LMS9530Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 舊案轉檔
 * </pre>
 * 
 * @since 2011/11/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/26,Vector,new
 *          </ul>
 */

@Scope("request")
@Controller("lms9530formhandler")
public class LMS9530Formhandler extends AbstractFormHandler {

	@Resource
	LMS9530Service service;

	@Resource
	UserInfoService userinfoservice;

	@Resource
	BranchService branchService;

	final String[] types = { "lms", "cls", "lrs", "crs", "log" };

	final String[][] rpts = {
			{ "案件簽報書", "小放會會議記錄", "動用審核表" },
			{ "案件簽報書", "審核書", "小放會會議記錄", "動用審核表", "政策性留學生貸款報送項目" },
			{ "覆審名單", "覆審報告表" },
			{ "覆審工作底稿", "覆審報告表", "消金授信覆審考核表" },
			{ "案件簽報書", "審核書", "小放會會議記錄", "動用審核表", "政策性留學生貸款報送項目", "覆審名單",
					"覆審報告表", "覆審工作底稿", "消金授信覆審考核表", "已敘做授信案件清單",
					"已敘做授信案件清單(總行批示意見)", "每月常董會報告事項彙總及申報案件數統計表",
					"各級授權範圍內承做授信案件統計表", "授信契約已逾期控制表", "信保案件未動用屆期清單",
					"分行敘做房屋貸款月報", "敘做無自用住宅購屋放款明細表", "已敘做個人消費金融業務月報表",
					"已敘做消金案件清單", "已敘做授信案件清單(營運中心)", "營運中心每日授權外授信案件清單",
					"疑似代辦案件訊息明細表"} };

	/**
	 * 取得 報表名稱rptName下拉單選項
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public IResult getItem(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9530V01Page.class);

		// rptName,rpt額外處理
		Map<String, String> rptName = new LinkedMap();
		String type = params.getString("type");
		for (int i = 0; i < types.length; i++) {
			if (types[i].equals(type)) {
				for (int j = 0; j < rpts[i].length; j++) {
					rptName.put(rpts[i][j], pop.getProperty(rpts[i][j]));
				}
			}
		}

		// brno & 判定branch類型
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<IBranch> brnos = null;
		Map<String, String> brno = new LinkedMap();
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {// 授管處
			result.set("isBranch", "false");
			brnos = branchService.getAllBranch();
			if ("rpt".equals(type)) {
				rptName.put("已敘做授信案件清單", pop.getProperty("已敘做授信案件清單"));
				rptName.put("已敘做授信案件清單(總行批示意見)",
						pop.getProperty("已敘做授信案件清單(總行批示意見)"));
				rptName.put("每月常董會報告事項彙總及申報案件數統計表",
						pop.getProperty("每月常董會報告事項彙總及申報案件數統計表"));
				rptName.put("各級授權範圍內承做授信案件統計表",
						pop.getProperty("各級授權範圍內承做授信案件統計表"));
			}
			for (int i = 0; i < brnos.size(); i++) {
				IBranch pivot = brnos.get(i);
				brno.put(pivot.getBrNo(), pivot.getBrName());
			}
		} else {
			brnos = branchService.getBranchOfGroup(user.getUnitNo());
			if (brnos.isEmpty()) {// 分行
				result.set("isBranch", "true");
				brno.put(user.getUnitNo(), user.getUnitCName());
				if ("rpt".equals(type)) {
					if ("1".equals(params.getString("loanType"))) {
						rptName.put("已敘做授信案件清單", pop.getProperty("已敘做授信案件清單"));
						rptName.put("授信契約已逾期控制表", pop.getProperty("授信契約已逾期控制表"));
						rptName.put("信保案件未動用屆期清單",
								pop.getProperty("信保案件未動用屆期清單"));
					} else {
						rptName.put("分行敘做房屋貸款月報", pop.getProperty("分行敘做房屋貸款月報"));
						rptName.put("敘做無自用住宅購屋放款明細表",
								pop.getProperty("敘做無自用住宅購屋放款明細表"));
						rptName.put("已敘做個人消費金融業務月報表",
								pop.getProperty("已敘做個人消費金融業務月報表"));
						rptName.put("已敘做消金案件清單", pop.getProperty("已敘做消金案件清單"));
						rptName.put("疑似代辦案件訊息明細表", pop.getProperty("疑似代辦案件訊息明細表"));
					}
				}
			} else {// 營運中心
				result.set("isBranch", "false");
				for (int i = 0; i < brnos.size(); i++) {
					IBranch pivot = brnos.get(i);
					brno.put(pivot.getBrNo(), pivot.getBrName());
				}
				if ("rpt".equals(type)) {
					rptName.put("已敘做授信案件清單(營運中心)",
							pop.getProperty("已敘做授信案件清單(營運中心)"));
					rptName.put("營運中心每日授權外授信案件清單",
							pop.getProperty("營運中心每日授權外授信案件清單"));
				}
			}
		}

		result.set("rptName", new CapAjaxFormResult(rptName));
		result.set("brno", new CapAjaxFormResult(brno));
		return result;
	}

	/**
	 * 刪除 列印清單
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult delete(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		LPDFS01A record = service.findModelByOid(LPDFS01A.class,
				params.getString("oid"));
		service.delete(record);
		return result;
	}

	/**
	 * 加入 列印清單
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("rawtypes")
	public IResult addPrint(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		List data = service.findListByMainId(LPDFS01A.class, mainId);
		if (data == null || data.size() == 0) {// 沒有才新增
			LPDFS01A record = new LPDFS01A();
			record.setMainId(mainId);
			record.setRptUNID(params.getString("mainId"));
			record.setRptName(params.getString("formText"));
			record.setRptType(params.getString("formName"));
			record.setRandomCode(params.getString("randomCode"));
			record.setCntrNo(params.getString("cntrNo"));
			String fileName = "/" + Util.trim(params.getString("ownBrId"))
					+ "-" + Util.trim(params.getString("randomCode")) + "_"
					+ Util.trim(params.getString("formType")) + ".pdf";
			record.setRptFile(fileName);
			service.save(record);
		}

		return result;
	}
}
