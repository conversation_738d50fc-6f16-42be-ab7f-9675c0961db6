package com.mega.eloan.lms.batch.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisELF447nService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

/**
 * J-111-0515_05097_B1001 Web
 * e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
 * 
 * <AUTHOR>
 * 
 *         因現行系統額度計算範圍僅限於在帳務系統有啟動額度檔之案件，對於已核准未簽約或雖已簽約惟尚未引入帳務系統之授信案件(即在途案件)，
 *         未計入於系統額度內，在額度控管上可能會有額度低估之虞，請依修改內辦理前述在途案件之額度修正加入簽報書及額度檢覈表有關額度限額比率計算內，
 *         及徵信管理系統之集團企業項下各查詢項目及倉儲系統部分報表有關額度暨限額比率計算之調整。
 * 
 *         一、eloan簽報管理系統： 　　就簽報書中額度明細表性質別為新做、增額及變更條件之已核准授信案件，該額度
 *         　　序號未引入帳務系統(BTT及AS400)或已核准現請額度仍大於帳務系統之額度，且未
 *         　　在eloan簽報書-已核准授信額度辦理狀態報送作業-選擇D-不簽約-註銷額度或
 *         　　E-單純敘做條件或豁免條件變更，無須辦理訂約手續者，並加計該筆簽報案件之所
 *         　　有授信額度之淨增減額（以下簡稱”在途授信額度”），請於： 1、消、企金之「授信信用風險管理」遵循檢核表中「銀行法第33條之3授權規定事
 *         項辦法」之遵循，新增欄位表內容如附檔一及二。 2、簽報書部分，請修正第三點-借款人暨關係戶與本行授信往來情形及利潤貢獻度
 *         ，修改內容請詳附檔三。 二、eloan徵信管理系統之集團企業：
 *         　　請修正「本行授信往來查詢」及「本行授信明細查詢」所查詢產生之報表內容，新
 *         增「在途授信額度」欄位，「在途授信額度」定義為，已核准未簽約或雖已簽約惟 尚未引入帳務系統之授信案已核准額度，修改內容詳附檔四及五。
 *         三、eloan資料建檔維護系統之同一關係人/同一關係企業頁籤，請修正「同一關係人授
 *         信餘額查詢」及「關係企業暨集團企業查詢」之報表內容，新增「在途授信額度」 欄位，修改內容詳附檔六及七。 四、資料倉儲系統：
 *         請新增及修正有關同一關係人、同一關係企業及集團企業之相關報表，修改內容詳 　　附檔八至十三。
 */
@Service("lmsbatch0009serviceimpl")
public class LmsBatch0009ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	SysParameterService sysParamService;

	@Resource
	MisELF447nService misELF447nService;

	@Resource
	EloandbBASEService eloandbService;

	@Resource
	LMSService lmsService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L120S01BDao l120s01bDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		// @NonTransactional
		JSONObject result = new JSONObject();
		JSONObject request = json.getJSONObject("request");

		String errMsg = this.doLmsBatch0001(request);
		if (Util.notEquals(errMsg, "")) {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RESPONSE,
					"LmsBatch0009ServiceImpl-doLmsBatch0001執行失敗！==>" + errMsg);
			return result;
		} else {
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"LmsBatch0009ServiceImpl執行成功！");
		}

		return result;
	}

	/**
	 * J-111-0515_05097_B1001 Web
	 * e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
	 */
	@NonTransactional
	public String doLmsBatch0001(JSONObject request) {

		StringBuffer errMsg = new StringBuffer("");

		// String twClsUseType = Util.trim(request.getString("twClsUseAssure"));
		String elf447nClassVersion = Util.trim(request
				.getString("elf447nClassVersion"));
		// boolean twClsUseAssure = Util.equals(twClsUseType, "Y") ? true :
		// false; // 已經沒用，改讀系統設定LMS_ELF447N_AMT_USE_CLS_ASSURE

		// 國內消金，有一個現請擔保額度的欄位,
		// TRUE.以實際直接用消金的現請擔保額度來用，無擔保就是現請額度-現請擔保額度
		// FALSE.計算科目權重

		String dataStartDate = Util.trim(request.getString("dataStartDate"));
		String dataEndDate = Util.trim(request.getString("dataEndDate"));

		if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
			errMsg.append("無法取得完整資料期間 " + dataStartDate + "~" + dataEndDate
					+ " ");
			System.out.println(errMsg.toString());
			return errMsg.toString();

		}

		List<Map<String, Object>> listL120m01a = eloandbService
				.findL120m01aByEndDate(dataStartDate, dataEndDate);

		if (listL120m01a != null && !listL120m01a.isEmpty()) {

			for (Map<String, Object> l120Map : listL120m01a) {

				String docType = Util.trim(MapUtils.getObject(l120Map,
						"DOCTYPE"));
				String mainId = Util
						.trim(MapUtils.getObject(l120Map, "MAINID"));

				L120M01A l120m01a = l120m01aDao.findByMainId(mainId);
				if (l120m01a == null) {
					continue;
				}

				// 根據來源不同出現的表不一樣 ，當授權外一定是批覆表，而當授權內為營運中心授權內還是以批覆表為準
				String itemType = lmsService.checkL140M01AItemType(l120m01a);
				List<L140M01A> l140m01as = l140m01aDao
						.findL140m01aListByL120m01cMainId(mainId, itemType,
								null);

				for (L140M01A l140m01a : l140m01as) {
					// String custId = l140m01a.getCustId();
					// String dupNo = l140m01a.getDupNo();
					String cntrNo = l140m01a.getCntrNo();
					// String ownBrid = l140m01a.getOwnBrId();
					//
					// boolean isOnlyMackUpProperty7 = false;
					if (LMSUtil.isContainValue(l140m01a.getProPerty(),
							UtilConstants.Cntrdoc.Property.不變)) {
						continue;
					}

					BigDecimal USEL230S01A = BigDecimal.ZERO;
					String ELF447N_CLASS = "";// 報案類別
					String ELF447N_LNNOFLAG = "";// 不計入授信項目代號
					BigDecimal ELF447N_CURAMT_S = null;
					BigDecimal ELF447N_CURAMT_N = null;
					
					if (LMSUtil.isClsCase(l120m01a)) {
						// 國內個金

						// 團貸不要補
						if (LMSUtil.isParentCase(l120m01a)) {
							continue;
						}

						ELF447N_CLASS = LMSUtil.isParentCase(l120m01a) ? "5"
								: "1";
						ELF447N_LNNOFLAG = "";// 消金沒有

					} else {
						// 海外個金、國內海外企金
						ELF447N_CLASS = lmsService.checkELF447NCLASS(l140m01a,
								elf447nClassVersion);
						ELF447N_LNNOFLAG = Util.trim(l140m01a.getNoLoan());
					}

					Map<String, BigDecimal> currAmtMap = lmsService
							.getCurrentApplyAmt_S_N(l120m01a, l140m01a, true);
					if (currAmtMap != null && !currAmtMap.isEmpty()) {
						ELF447N_CURAMT_S = currAmtMap.get("ELF447N_CURAMT_S") == null ? null
								: Util.parseBigDecimal(currAmtMap
										.get("ELF447N_CURAMT_S"));
						ELF447N_CURAMT_N = currAmtMap.get("ELF447N_CURAMT_N") == null ? null
								: Util.parseBigDecimal(currAmtMap
										.get("ELF447N_CURAMT_N"));
						USEL230S01A = currAmtMap.get("USEL230S01A") == null ? BigDecimal.ZERO
								: Util.parseBigDecimal(currAmtMap
										.get("USEL230S01A"));
					}

					logger.info("[LmsBatch0009ServiceImpl]   ====>"
							+ "l120m01a" + mainId + "，l140m01a ="
							+ l140m01a.getMainId() + "，ELF447N_CLASS ="
							+ ELF447N_CLASS + "，ELF447N_LNNOFLAG ="
							+ ELF447N_LNNOFLAG + "，ELF447N_CURAMT_S ="
							+ Util.trim(ELF447N_CURAMT_S)
							+ "，ELF447N_CURAMT_N ="
							+ Util.trim(ELF447N_CURAMT_N) + "，useL230s01a =");

					Map<String, Object> rows447n = misELF447nService
							.findByUnidAndCntrNo(mainId, cntrNo);

					if (rows447n != null && !rows447n.isEmpty()) {
						misELF447nService.updateCurrAmtSNByUnidAndContract(
								mainId, cntrNo, ELF447N_CLASS,
								ELF447N_LNNOFLAG, ELF447N_CURAMT_S,
								ELF447N_CURAMT_N);
					}

				}

			}

		}

		return errMsg.toString();
	}

}
