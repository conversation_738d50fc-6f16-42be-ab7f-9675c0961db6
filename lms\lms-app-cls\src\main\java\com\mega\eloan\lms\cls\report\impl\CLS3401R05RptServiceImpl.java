package com.mega.eloan.lms.cls.report.impl;

import java.text.ParseException;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.report.CLS3401R05RptService;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;


@Service("cls3401r05rptservice")
public class CLS3401R05RptServiceImpl extends AbstractReportService implements CLS3401R05RptService  {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS3401R05RptServiceImpl.class);
	
	@Resource
	LMSService lmsService;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	CLSService clsService;

	@Resource
	CLS3401Service cls3401service;

	@Resource
	ContractDocService contractDocService;
	
	@Override
	public String getReportTemplateFileName() {
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		// 測試用
		return "report/cls/CLS3401R05_" + locale.toString() + ".rpt";
	}

	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) throws CapException, ParseException {
		// Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3401M05Page.class);
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		
		try {
			C340M01A meta = clsService.findC340M01A_oid(mainOid);
			String cntrNo = meta.getC340m01bs().get(0).getCntrNo();
			String ownBrId = meta.getOwnBrId();
			
			
			rptVariableMap.put("cntrNo", cntrNo);
			rptVariableMap.put("caseNo", meta.getCaseNo());
			rptVariableMap.put("custName", meta.getCustId() + " " + meta.getCustName());
			rptVariableMap.put("branchName", StrUtils.concat(ownBrId, " ", branchService.getBranchName(ownBrId)));
			rptVariableMap.put("ploanCtrNo", Util.trim(meta.getPloanCtrNo()));
			rptVariableMap.put("ploanPosSId", Util.trim(meta.getPloanPosSId()));
			rptVariableMap.put("ploanPosSId_name", cls3401service.get_ploanPosSId_name(meta));
			
			rptGenerator.setVariableData(rptVariableMap);
		} catch(Exception e) {
			LOGGER.error(StrUtils.getStackTrace(e));
		}		
	}
}
