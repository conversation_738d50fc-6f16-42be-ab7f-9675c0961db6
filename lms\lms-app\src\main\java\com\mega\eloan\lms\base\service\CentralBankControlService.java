
package com.mega.eloan.lms.base.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Properties;

import tw.com.iisi.cap.service.ICapService;

/**
 * <pre>
 * BY 專案共用Service
 * </pre>
 * 
 * @since 2022/11/18
 * <AUTHOR>
 * @version
 * 
 */
public interface CentralBankControlService extends ICapService {
	
	public void includeCMStSumAmtAdjToL140m01mTimeVal(String mainId);

	public BigDecimal getCMStSumAmtAdj(String l140m01a_mainId);

	public void checkIsLandOrConstructionFinancingCase(String landBuildYN, String prodClass, Properties prop, List<String> errList);

	public boolean isRemarkRrmovedt(Date actStartDate, String cntrNo);

	public boolean checkContractNoIsExisted(String cntrNo);

	public void checkAllVersionL140m01mData(List<String> errList, Properties prop, String cbcCase, String plusReason, BigDecimal appAmt);
	
}