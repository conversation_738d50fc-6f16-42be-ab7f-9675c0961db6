/* 
 * L160M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L160M01A;

/** 動用審核表主檔 **/
public interface L160M01ADao extends IGenericDao<L160M01A> {

	L160M01A findByOid(String oid);

	List<L160M01A> findByMainIds(String mainId);

	List<L160M01A> findByOids(String[] oids);

	L160M01A findByMainId(String mainId);

	List<L160M01A> findByDocStatus(String docStatus);

	/**
	 * 查詢先行動用清單
	 * 
	 * @param docStatus
	 *            文件狀態
	 * @param ownBrId
	 *            登入分行
	 *
	 * @return 動審表
	 */
	List<L160M01A> findByFitstUse(String[] docStatus, String ownBrId);
}