package tw.com.jcs.flow.node;

import tw.com.jcs.flow.core.FlowInstanceImpl;

/**
 * <pre>
 * 流程節點(起案)
 * </pre>
 * 
 * @since 2023年1月10日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月10日
 *          </ul>
 */
public class StartNode extends FlowNode {

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.node.FlowNode#next(tw.com.jcs.flow.core.FlowInstanceImpl)
     */
    @Override
    public void next(FlowInstanceImpl instance) {
        instance.handle();
        finishCurrentNode(instance);
        changeToThisNode(instance);
        instance.next();
    }

}
