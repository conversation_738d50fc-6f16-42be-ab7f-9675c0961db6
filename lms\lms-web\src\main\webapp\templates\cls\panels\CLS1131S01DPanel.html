<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="panelFragmentBody">
            <!-- 個金配偶資料檔 -->
			<div id="C101S01DDiv" name="C101S01DDiv" >
				<form id="C101S01DForm" name="C101S01DForm" >
					<input type="radio" id="mateFlag" name="mateFlag" />
					<br/>
					<div id="mateDiv" style="display:none;">
						<button type="button" id="btImportMate" name="btImportMate"><span class="text-only"><th:block th:text="#{'C101S01D.btImportMate'}">引進配偶資料</th:block></span></button>
						<table class="tb2" width="100%">
							<tr>
								<td width="18%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01D.mCustId'}">配偶統一編號</th:block>&nbsp;&nbsp;</td>
								<td width="32%" >
									<input type="text" id="mCustId" name="mCustId" class="checkID max required" maxlength="10" size="10"/>
									<input type="text" id="mDupNo" name="mDupNo" class="max required" maxlength="1" size="1"/>
								</td>
								<td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101S01D.mForeginal'}">外國人無特定住所</th:block>&nbsp;&nbsp;</td>
								<td width="32%" ><input type="checkbox" id="mForeginal" name="mForeginal" value="Y" /></td>
							</tr>
							<tr>
								<td class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01D.mName'}">配偶姓名</th:block>&nbsp;&nbsp;</td>
								<td colspan="3" ><input type="text" id="mName" name="mName" class="max required" maxlength="250" /></td>
							</tr>
							<tr>
								<td class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01D.mSex'}">性別</th:block>&nbsp;&nbsp;</td>
								<td ><input type="radio" id="mSex" name="mSex" class="required" codeType="sex" /></td>
								<td class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01D.mBirthday'}">出生日期</th:block>&nbsp;&nbsp;</td>
								<td ><input type="text" id="mBirthday" name="mBirthday" class="date required"  /></td>
							</tr>
							<tr>
								<td class="hd2" align="right" ><th:block th:text="#{'C101S01D.mJobKind'}">行業別</th:block>&nbsp;&nbsp;</td>
								<td ><select id="mJobKind" name="mJobKind" codeType="lms1205s01_mJobKind" ></select></td>
								<td class="hd2" align="right" ><th:block th:text="#{'C101S01D.mComName'}">服務單位名稱</th:block>&nbsp;&nbsp;</td>
								<td ><input type="text" id="mComName" name="mComName" class="max" maxlength="250" /></td>
							</tr>
							<tr>
								<td class="hd2" align="right" ><th:block th:text="#{'C101S01D.mComAddr'}">服務單位地址</th:block>&nbsp;&nbsp;</td>
								<td colspan="3" >
									<input type="text" id="mComCity" name="mComCity" style="display:none;" />
									<input type="text" id="mComZip" name="mComZip" style="display:none;" />
									<input type="text" id="mComAddr" name="mComAddr" style="display:none;" />
									<a href="#" id="mComTargetLink" class="readOnly" ><span id="mComTarget" class="field comboSpace" ></span></a>
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right" ><th:block th:text="#{'C101S01D.mComTel'}">服務單位電話</th:block>&nbsp;&nbsp;</td>
								<td ><input type="text" id="mComTel" name="mComTel" class="max" maxlength="150" /></td>
								<td class="hd2" align="right" ><th:block th:text="#{'C101S01D.mJobTitle'}">職稱</th:block>&nbsp;&nbsp;</td>
								<td ><input type="text" id="mJobTitle" name="mJobTitle" class="max" maxlength="60" /></td>
							</tr>
							<tr>
								<td class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01D.mSeniority'}">年資</th:block>&nbsp;&nbsp;</td>
								<td ><input type="text" id="mSeniority" name="mSeniority" class="required max number" maxlength="2" size="2" /></td>
								<td class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01D.mPayAmt'}">年薪</th:block>&nbsp;&nbsp;</td>
								<td >
									<select id="mPayCurr" name="mPayCurr" class="required" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>
									<input type="text" id="mPayAmt" name="mPayAmt" class="max number required" maxlength="13" size="13" />
									<th:block th:text="#{'money.unit'}">萬元</th:block>
								</td>
							</tr>
						</table>
					</div>
				</form>
			</div>
		</th:block>
    </body>
</html>
