/* 
 *LNLNF070Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.util.HashMap;
import java.util.LinkedHashMap;

/**
 * <pre>
 * LN.LN070
 * </pre>
 * 
 * @since 2012/11/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/7,REX,new
 *          </ul>
 */
public interface LNLNF070Service {

	/**
	 * 取得USD市場利率選項 <br/>
	 * LNF070_TYPE = 'LNF030-INT-CODE'
	 * 
	 * @return <br/>
	 *         <代碼,顯示名稱>
	 */
	public LinkedHashMap<String, String> getCode();

	/**
	 * 取得自定利率選單
	 * 
	 * @param curr
	 *            幣別
	 * 
	 * @return<代碼,顯示名稱>
	 */
	public LinkedHashMap<String, String> getPrRate(String curr);

	/**
	 * 取得自定利率選單
	 * 
	 * 
	 * 
	 * TWD = 00<br/>
	 * USD = "01"<br/>
	 * CNY = "39"<br/>
	 * Else<br/>
	 * rateType = "99"<br/>
	 * 
	 * @return<幣別,<代碼,顯示名稱>>
	 */
	public HashMap<String, LinkedHashMap<String, String>> getPrRate();

	/**
	 * 取得資金來源小類
	 * 
	 * 
	 * @param fFund
	 *            資金來源大類(代碼)
	 * @return map 資金來源小類map
	 */
	public LinkedHashMap<String, String> getdFund(String fFund);

	/**
	 * 取得資金來源小類 (全部)
	 * 
	 * @return map 資金來源小類map
	 */
	public HashMap<String, LinkedHashMap<String, String>> getdFundAll();

	/**
	 * 取得所有在 MISLNRAT 找不到的利率代碼
	 * 
	 * @return
	 */
	public HashMap<String, String> getRateBy070();

}
