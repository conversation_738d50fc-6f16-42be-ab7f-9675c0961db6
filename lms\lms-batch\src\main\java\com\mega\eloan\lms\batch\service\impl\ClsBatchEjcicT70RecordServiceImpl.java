package com.mega.eloan.lms.batch.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dao.C101S02SDao;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.model.C101S02S;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 *  J-112-0558 聯徵T70證券暨期貨違約交割批次記錄
 * </pre>
 * 
 * @since 2023/12/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2023/12/18,new
 *          </ul>
 */
@Service("clsBatchEjcicT70RecordServiceImpl")
public class ClsBatchEjcicT70RecordServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory.getLogger(ClsBatchEjcicT70RecordServiceImpl.class);
	
	
	protected static final int timeout = 60;
	
	@Resource
	C101S02SDao c101s02sDao;
	
	@Resource
	EjcicService ejcicService;
	
	@Resource
	CLSService clsService;
	


	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		
		JSONObject result = null;
		String mainId = null;
		String custId = null;
		String dupNo = null;
		String branchNo = null;
		try {
			
			List<C101S02S> c101s02sList = this.c101s02sDao.findByisRefreshStatusNotEqualsY();
			for(C101S02S c101s02s : c101s02sList){
				
				mainId = c101s02s.getMainId();
				custId = c101s02s.getCustId();
				dupNo = c101s02s.getDupNo();
				branchNo = c101s02s.getBranchNo();
				
				Map<String, Object> tas700Map = this.ejcicService.getTAS700DataBy(c101s02s.getCustId(), "ST");
				String rStatus = null;
				boolean isRecordHtmlSuccess = false;
				if(tas700Map != null){
					
					Date qSuccessDate = null;
					rStatus = tas700Map.get("R_STATUS") == null ? null : (String)tas700Map.get("R_STATUS");
					String negFlag = tas700Map.get("NEG_FLAG") == null ? null : (String)tas700Map.get("NEG_FLAG");
					BigDecimal negAmt = tas700Map.get("NEG_AMT") == null ? null : LMSUtil.nullToZeroBigDecimal(tas700Map.get("NEG_AMT"));
					Date qdate = tas700Map.get("QDATE") == null ? null : CapDate.parseDate(CapDate.formatDateFormatToyyyyMMdd((String)tas700Map.get("QDATE"), "YYY/MM/DD"));
					c101s02s.setDataStatus(rStatus);
					c101s02s.setNegFlag(negFlag);
					c101s02s.setNegAmt(negAmt);
					c101s02s.setQueryTime(qdate);
					
					if("A3".equals(rStatus)){
						String txId = String.valueOf(tas700Map.get("QUERY_ITEM"));
						String qDateStr = (String)tas700Map.get("QDATE");
						String qEmpCode = String.valueOf(tas700Map.get("QEMPCODE"));
						String qBranch = String.valueOf(tas700Map.get("QBRANCH"));
						Map<String, Object> bt2File = this.ejcicService.getBT2FileDataBy(custId, "H"+txId, qDateStr, qEmpCode, qBranch);
						
						if(bt2File != null){
							String resp_html = Util.trim(bt2File.get("QRPT"));
							this.clsService.keep_htmlData_to_c101s01u(mainId, custId, dupNo, txId, resp_html);
							qSuccessDate = CapDate.parseDate(CapDate.formatDateFormatToyyyyMMdd((String)bt2File.get("QDATE"), "YYY/MM/DD"));
							isRecordHtmlSuccess = true;
						}
					}
					
					c101s02s.setqSuccessDate(qSuccessDate);
					c101s02s.setUpdateTime(CapDate.getCurrentTimestamp());
					c101s02s.setUpdater("system");
					c101s02s.setRefreshStatus(isRecordHtmlSuccess ? "Y" : "X");
					this.clsService.saveC101S02S(c101s02s);
					LOGGER.debug("聯徵T70證券暨期貨違約交割批次記錄執行成功 ===> mainId: {}, custId: {}, dupNo: {}, rStatus: {}, isRecordHtmlSuccess: {}", new String[]{mainId, custId, dupNo, rStatus, isRecordHtmlSuccess ? "Y" : "N"});
				}
			}
			
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE, "clsBatchEjcicT70RecordServiceImpl 執行成功！");

		} catch (Exception ex) {
			LOGGER.error(StrUtils.getStackTrace(ex));
			result = WebBatchCode.RC_ERROR;
			String msg = ex.getMessage() + "===> mainId:" + mainId + ", custId:" + custId + ", dupNo:" + dupNo + ", branchNo:" + branchNo;
			result.element(
					WebBatchCode.P_RESPONSE, "clsBatchEjcicT70RecordServiceImpl 執行失敗！==>" + msg + " - " + ex.getLocalizedMessage());
		}

		return result;
	}

}
