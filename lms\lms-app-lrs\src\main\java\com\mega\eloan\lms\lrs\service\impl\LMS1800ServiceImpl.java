package com.mega.eloan.lms.lrs.service.impl;

import java.io.File;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.dao.L180M01BDao;
import com.mega.eloan.lms.dao.L180M01CDao;
import com.mega.eloan.lms.dao.L180M01DDao;
import com.mega.eloan.lms.dao.L180M01ZDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lrs.constants.lrsConstants;
import com.mega.eloan.lms.lrs.service.LMS1800Service;
import com.mega.eloan.lms.lrs.service.LMS1810Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L180M01C;
import com.mega.eloan.lms.model.L180M01D;
import com.mega.sso.service.BranchService;

@Service
public class LMS1800ServiceImpl extends AbstractCapService implements
		LMS1800Service {

	protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

	@Resource
	FlowSimplifyService flowSimplifyService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L170M01ADao l170m01aDao;

	@Resource
	L180M01ADao l180m01aDao;

	@Resource
	L180M01BDao l180m01bDao;

	@Resource
	L180M01CDao l180m01cDao;

	@Resource
	L180M01DDao l180m01dDao;

	@Resource
	L180M01ZDao l180m01zDao;

	@Resource
	LMS1810Service lms1810Service;

	@Resource
	TempDataService tempDataService;

	@Resource
	RetrialService retrialService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	BranchService branchService;

	@Resource
	EmailClient emailClient;

	@Resource
	CodeTypeService codeTypeService;

	@Override
	public boolean produce(String branch, Date baseDate, String userId,
			String unitNo, String unitType, String createBy,
			List<String> existBrSkipList) throws CapException {
		List<String> instIdList = new ArrayList<String>();

		if (isL180M01AInCompiling(unitNo, branch, baseDate)) {
			// 略過同一 endDate 且仍在 編製中 的分行
			existBrSkipList.add(branch);
			return true;
		}
		boolean is_exMode_A = false;
		boolean r = lms1810Service.gfnGenCTLList_ByBrno(is_exMode_A,
				instIdList, branch, baseDate, userId, unitNo, unitType,
				createBy, new ArrayList<L180M01B>());
		if (r) {
			for (String instId : instIdList) {
				flowSimplifyService.flowStart("LMS1800Flow", instId, userId,
						unitNo);
			}
		} else {
			LOGGER.error(branch + "[" + LrsUtil.toStrYM(baseDate)
					+ "] produce fail");
		}

		return r;
	}

	@Override
	public boolean produceNew(L180M01A meta, String custId, String dupNo,
			String cName, String ctlType) throws CapException {
		L180M01B l180m01b = lms1810Service.gfnDB2CTLInsertNewList(meta, custId,
				dupNo, cName, ctlType);
		return (l180m01b != null);
	}

	@Override
	public boolean isL180M01AInCompiling(String ownBrId, String branchId,
			Date dataDate) {
		ISearch search = l180m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dataDate", dataDate);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				RetrialDocStatusEnum.編製中.getCode());

		List<L180M01A> list = l180m01aDao.find(search);
		return CollectionUtils.isNotEmpty(list);
	}

	@Override
	public void deleteMeta(L180M01A meta) {
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		meta.setDeletedTime(nowTS);
		retrialService.save(meta);

		// 可能由已傳送分行，再退回編製中，刪除
		if (true) {
			for (L170M01A l170m01a : retrialService.findL170M01A(meta)) {
				L170M01F l170m01f = l170m01a.getL170m01f();
				if (l170m01f != null && l170m01f.getUpDate() != null) {
					continue;
				}
				l170m01a.setDeletedTime(nowTS);
				retrialService.save(l170m01a);
			}
		}

		if (true) {
			// 刪除 暫時的PDF檔
			// Excel檔...先留著
			String filename = LMSUtil.getUploadFilePath(meta.getOwnBrId(),
					meta.getMainId(), LrsUtil.ATTCH_L180M01A_ZIP);
			try {
				FileUtils.deleteDirectory(new File(filename));
			} catch (Exception e) {
				LOGGER.error(StrUtils.getStackTrace(e));
			}
		}
	}

	@Override
	public Map<String, L120M01A> findPrint_L120M01A(String brNo,
			List<String> custId_list, List<String> dupNo_list, String ctlType) {
		int size = custId_list.size();
		Map<String, L120M01A> r = new HashMap<String, L120M01A>();
		if (size > 0) {
			Set<String> keySet = new HashSet<String>();
			if (true) {
				for (int i = 0; i < size; i++) {
					keySet.add(LMSUtil.getCustKey_len10custId(
							custId_list.get(i), dupNo_list.get(i)));
				}
			}

			// 若用DataParse.map2Bean(m_l120m01a, l120m01a); 可能因資料的關係，有時拋出
			// java.lang.IllegalArgumentException

			// ●●純常董會(董事會)權限實地覆審用(僅適用一般分行(純實地覆審)，不含 007、201、025)
			// 分行的常董會實地覆審報告表，通常都是國外部、金控總部簽的，所以原來用CASEBRID=分行的條件，分行反映不適合
			List<Map<String, Object>> m_l120m01as = Util.equals(ctlType,
					LrsUtil.CTLTYPE_自辦覆審) ? eloandbBASEService
					.findL120M01A_lrs_ctlType_B(brNo, custId_list)
					: eloandbBASEService.findL120M01A_lrs(brNo, custId_list);

			for (Map<String, Object> m_l120m01a : m_l120m01as) {
				/*
				 * 查出來的 list 未加入 dupNo，可能同一個 custId 會查到 N 筆 dupNo 在這裡去排除多餘的
				 * dupNo
				 */
				String custId = Util.trim(MapUtils.getString(m_l120m01a, Util
						.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審) ? "ncustId"
						: "custId"));
				String dupNo = Util.trim(MapUtils
						.getString(m_l120m01a, "dupNo"));
				String mainId = Util.trim(MapUtils.getString(m_l120m01a,
						"mainId"));
				String isHeadCheck = Util.trim(MapUtils.getString(m_l120m01a,
						"isHeadCheck"));
				String k = LMSUtil.getCustKey_len10custId(custId, dupNo);
				if (!keySet.contains(k)) {
					continue;
				}
				L120M01A l120m01a = new L120M01A();
				l120m01a.setCustId(custId);
				l120m01a.setDupNo(dupNo);
				l120m01a.setMainId(mainId);
				l120m01a.setIsHeadCheck(isHeadCheck);
				r.put(k, l120m01a);
			}
		}
		return r;
	}

	@Override
	public List<L140M01A> findPrint_L140M01A(String l120m01a_mainId) {
		ISearch search = l140m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				l120m01a_mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				FlowDocStatusEnum.已核准.getCode());
		Map<String, Boolean> orderByMap = new LinkedHashMap<String, Boolean>();
		orderByMap.put("printSeq", false);
		orderByMap.put("createTime", false);
		search.setOrderBy(orderByMap);
		return l140m01aDao.find(search);
	}

	@Override
	public void saveNoCTL(L180M01A meta, List<L180M01B> l180m01b_list,
			String newNCkdFlag, Date newNextNwDt) throws CapException {
		for (L180M01B model : l180m01b_list) {
			if (Util.equals(lrsConstants.CREATEBY.人工產生, model.getCreateBY())) {
				// '人工產生的名單 刪除實際文件
				delL180M01B_C_D(model);
			} else {
				if (Util.equals(LrsUtil.NCKD_8_本次暫不覆審, newNCkdFlag)) {
					model.setNewNextNwDt(newNextNwDt);
				} else {
					model.setNewNextNwDt(null);
				}
				model.setDocStatus1(lrsConstants.docStatus1.不覆審);
				model.setNewNCkdFlag(newNCkdFlag);
				model.setNewLRDate(null);
				// ---
				retrialService.save(model);
			}
		}
		retrialService.save(meta);
	}

	@Override
	public void saveNoCTL_O(L180M01A meta, List<L180M01B> l180m01b_list,
			Date cmp_defaultCTLDate) {
		String memo = "已列為" + Util.trim(TWNDate.toAD(cmp_defaultCTLDate))
				+ "應覆審案件";
		for (L180M01B model : l180m01b_list) {
			model.setNewNCkdFlag(LrsUtil.NCKD_8_本次暫不覆審);
			model.setNewNCkdMemo(memo);
			model.setNewNextNwDt(cmp_defaultCTLDate);
			model.setDocStatus1(lrsConstants.docStatus1.不覆審);
			retrialService.save(model);
		}
		retrialService.save(meta);
	}

	@Override
	public void saveReCTL_1(L180M01A meta, List<String> oid_arr,
			List<String> needLrDateOidList, List<String> needLrDateDescList,
			List<String> newLRDateList, List<String> elfRckdLineList) {
		Date sysDate = new Date();
		for (String oid : oid_arr) {
			L180M01B model = retrialService.findL180M01B_oid(oid);
			// ---
			if (Util.isNotEmpty(Util.trim(model.getElfNCkdFlag()))) {
				needLrDateOidList.add(model.getOid());
				needLrDateDescList.add(Util.trim(model.getCustId()) + " "
						+ Util.trim(model.getElfCName()));
				newLRDateList.add(Util.trim(TWNDate.toAD(CrsUtil
						.isNull_or_ZeroDate(model.getElfLRDate()) ? sysDate
						: model.getElfLRDate())));
				elfRckdLineList.add(Util.trim(model.getElfRCkdLine()));
			} else {
				lms1810Service.l180m01b_reCtl(model);
				retrialService.save(model);
			}
		}
		retrialService.save(meta);
	}

	@Override
	public void saveReCTL_2(L180M01A meta, String oid, Date newLRDate) {
		L180M01B model = retrialService.findL180M01B_oid(oid);
		lms1810Service.l180m01b_reCtl(model);
		model.setNewLRDate(newLRDate);// 當 ElfNCkdFlag 有值時,要填入newLRDate

		retrialService.save(model);
		retrialService.save(meta);
	}

	private void delL180M01B_C_D(L180M01B model) {
		// delete D
		Set<L180M01D> l180m01d_list = model.getL180m01ds();
		if (CollectionUtils.isNotEmpty(l180m01d_list)) {
			for (L180M01D d : l180m01d_list) {
				l180m01dDao.delete(d);
			}
		}
		// delete C
		List<L180M01C> l180m01c_list = retrialService.findL180M01C(model);
		if (CollectionUtils.isNotEmpty(l180m01c_list)) {
			l180m01cDao.delete(l180m01c_list);
		}

		// delete B
		l180m01bDao.delete(model);
	}

	@Override
	public void batch_toEnd(String userId, String unitNo) {
		String decisionExpr = "to_已覆核已核定";

		ISearch search = l170m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "typCd", new String[] {
				TypCdEnum.DBU.getCode(), TypCdEnum.OBU.getCode() });
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				RetrialDocStatusEnum.已覆核未核定.getCode());
		// 流程在從覆審組→受檢單位編製中, 會先上傳 ELF412
		// search.addSearchModeParameters(SearchMode.EQUALS, "approveTime",
		// null);
		search.addSearchModeParameters(SearchMode.EQUALS, "l170m01f.conFlag",
				"1");// 覆審正常
		search.setMaxResults(Integer.MAX_VALUE);

		for (L170M01A meta : l170m01aDao.find(search)) {

			try {
				flowSimplifyService.flowNext(meta.getOid(), decisionExpr,
						userId, unitNo);
				tempDataService.deleteByMainId(meta.getMainId());
			} catch (Exception e) {
				LOGGER.error("【" + meta.getOwnBrId() + "," + meta.getCustId()
						+ "," + meta.getCustName() + "】"
						+ StrUtils.getStackTrace(e));
			}

		}
	}

	@Override
	public boolean mail_lrs_processingOverDays(int overDays, boolean skipBrT1) {
		Date sysDate = CapDate.parseDate(CapDate
				.getCurrentDate(CapDate.DEFAULT_DATE_FORMAT));
		String d = Util.trim(TWNDate.toAD(CapDate.shiftDays(sysDate, overDays
				* -1)));

		Set<String> mail_br_list = _mailList("lms1701v01_mailSitesB");
		Set<String> mail_area_list = _mailList("lms1701v01_mailSitesA");

		Set<String> fnCheckArea = eloandbBASEService.findLrsMailBr(d, true);
		Set<String> fnCheckBranch = eloandbBASEService.findLrsMailBr(d, false);

		for (String brNo : fnCheckBranch) {
			List<String> toAddr = new ArrayList<String>();
			if (skipBrT1 == false) {
				toAddr.add(brNo + "<EMAIL>");
			}
			toAddr.addAll(mail_br_list);
			// ---
			String subject = "【" + brNo + " "
					+ branchService.getBranchName(brNo) + "】已逾二個星期尚未回覆" + "  "
					+ d + "  " + "企金覆審報告表之通知!!";
			emailClient.send(toAddr.toArray(new String[toAddr.size()]),
					subject, "");
		}

		for (String brNo : fnCheckArea) {
			List<String> toAddr = new ArrayList<String>();
			toAddr.addAll(mail_area_list);
			// ---
			String subject = "【" + brNo + " "
					+ branchService.getBranchName(brNo) + "】企金覆審：中心逾二個星期尚核定。";
			emailClient.send(toAddr.toArray(new String[toAddr.size()]),
					subject, "");
		}
		return true;
	}

	private Set<String> _mailList(String codeType) {
		Set<String> r = new HashSet<String>();
		for (CodeType obj : codeTypeService.findByCodeTypeList(codeType)) {
			String target = Util.trim(obj.getCodeDesc());
			if (Util.isNotEmpty(target)) {
				r.add(target);
			}
		}
		return r;
	}
}
