/* 
 *DWRKCNTRNOServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dw.service.impl;

import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONObject;
import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dw.service.DWRKCNTRNOService;

import tw.com.jcs.common.Util;

/**
 * 
 * @since 2012/11/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/21,Gary<PERSON><PERSON>,new
 *          </ul>
 */
@Service
public class DWRKCNTRNOServiceImpl extends AbstractDWJdbc implements
		DWRKCNTRNOService {
	/**
	 * 查詢額度序號相同對象不同明細表內是否有資料 DW
	 * 
	 * @param OldCntrNo
	 *            舊額度序號
	 * @return
	 */
	@Override
	public List<Map<String, Object>> selByCntrNo(String cntrNo) {
		return this.getJdbc().queryForList("DWADM.DW_RKCNTRNO.selectByCntrNo",
				new Object[] { cntrNo });
	}

	/**
	 * 更新額度序號相同對象不同明細表 DW
	 * 
	 * @param OldCntrNo
	 *            舊額度序號
	 * @param NewCntrNo
	 *            新額度序號
	 * @param table
	 *            Table名稱
	 * @return
	 */
	public void DwUpdatedata(String table, String OldCntrNo, String NewCntrNo) {
		getJdbc().update("DWADM." + table + ".Update",
				new String[] { NewCntrNo, OldCntrNo });
	}

	/**
	 * 更新客戶統編 DW
	 * 
	 * @param OldCntrNo
	 *            舊額度序號
	 * @param NewCntrNo
	 *            新額度序號
	 * @param table
	 *            Table名稱
	 * @return
	 */
	public void DwUpdatedata(String table, String OldCustId, String OldDupNo,
			String NewCustId, String NewDupNo) {

		getJdbc().update("DWADM." + table + ".UpdateByCustIdDupNo",
				new String[] { NewCustId, NewDupNo, OldCustId, OldDupNo });

	}

	/**
	 * 查詢客戶統編在資料表內是否有資料 DW
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重複序號
	 * @return
	 */
	public List<Map<String, Object>> selBycustIdDupNo(String table[],
			String custId, String dupNo) {
		String[] custIds = new String[table.length * 2];

		for (int i = 0; i < custIds.length; i = i + 2) {
			custIds[i] = custId;
			custIds[i + 1] = dupNo;
		}
		return this.getJdbc().queryForList(
				"DWADM.DW_RKCNTRNO.selBycustIddupNo", custIds);
	}

	@Override
	public int update(JSONObject data) {
		String[] keys = { "CUST_KEY", "LNGEFLAG", "BASE_A", "BASE_B", "BASE_S",
				"BASE_SCORE", "TOTAL_SCORE", "INITIAL_SCORE",
				"PREDICT_BAD_RATE", "INITIAL_RATING", "ADJ_RATING",
				"FINAL_RATING", "JCIC_WARNING_FLAG", "FINAL_RATING_FLAG",
				"DELETE_REASON", "REJECT_OTHEREASON_TEXT", "DOCSTATUS",
				"DR", "DR_1YR","DATA_SRC_DT" };

		String[] whereKeys = { "BR_CD", "NOTEID", "CUSTID", "DUPNO", "MOWTYPE",
				"MOWVER1", "MOWVER2", "JCIC_DATE", "ACCT_KEY" };

		Object[] args = new Object[keys.length + whereKeys.length];
		int count = 0;
		StringBuilder sql = new StringBuilder();
		for (String key : keys) {
			sql.append(sql.length() <= 0 ? "update DWADM.DW_RKCREDIT set "
					: ",");
			sql.append(key).append("=?");

			args[count++] = data.get(key);
		}

		StringBuilder sb = new StringBuilder();
		for (String key : whereKeys) {
			sb.append(sb.length() <= 0 ? "" : " and ");
			sb.append(key).append("=?");

			args[count++] = data.get(key);
		}

		sql.append(" where ").append(sb.toString());
		return this.getJdbc().updateBySQL(sql.toString(), args);
	}

	@Override
	public int insert(JSONObject data) {		
		String[] keys = { "BR_CD", "NOTEID", "CUSTID", "DUPNO", "MOWTYPE",
				"MOWVER1", "MOWVER2", "JCIC_DATE","ACCT_KEY", "CUST_KEY", "LNGEFLAG",
				"BASE_A", "BASE_B", "BASE_S", "BASE_SCORE", "TOTAL_SCORE",
				"INITIAL_SCORE", "PREDICT_BAD_RATE", "INITIAL_RATING",
				"ADJ_RATING", "FINAL_RATING", "JCIC_WARNING_FLAG",
				"FINAL_RATING_FLAG", "DELETE_REASON", "REJECT_OTHEREASON_TEXT",
				"DOCSTATUS", "DR","DR_1YR",  "DATA_SRC_DT" };
		Object[] args = new Object[keys.length];
		int count = 0;

		StringBuilder sql = new StringBuilder();
		StringBuilder sb = new StringBuilder();

		for (String key : keys) {
			sql.append(sql.length() <= 0 ? "insert into DWADM.DW_RKCREDIT("
					: ",");
			sql.append(key);
			sb.append(sb.length() <= 0 ? "" : ",").append("?");

			args[count++] = data.get(key);
		}
		sql.append(") values (").append(sb.toString()).append(")");
		return this.getJdbc().updateBySQL(sql.toString(), args);
	}

	@Override
	public int updateScore(JSONObject data) {
		String[] keys = { "CUST_KEY", "LNGEFLAG", "CC12_REVOL_BAL",
				"CC12_REVOL_LIMIT", "R01_CC12_MAX_REVOL_RATE",
				"R01_CC12_MAX_REVOL_RATE_SCORE", "CC_REVOL_BAL",
				"CC_REVOL_PERMIT_LIMIT", "R10_REVOL_RATE",
				"R10_REVOL_RATE_SCORE", "D07_LN_NOS_TAMT",
				"D07_LN_NOS_TAMT_SCORE", "D12_CC12_MAX_AVGLIMIT",
				"D12_CC12_MAX_AVGLIMIT_SCORE", "D15_CC6_AVG_RC",
				"D15_CC6_AVG_RC_SCORE", "P19_CC12_PCODE_A_TIMES",
				"P19_CC12_PCODE_A_TIMES_SCORE", "MARRIAGE", "CHILDREN",
				"MARRIAGE_SCORE", "OCCUPATION_1", "OCCUPATION_1_SCORE",
				"COLL_3", "COLL_3_SCORE", "EDUCATION", "EDUCATION_SCORE",
				"N11_INQ3_LN_BANK", "N11_INQ3_LN_BANK_SCORE",
				"N06_INQ12_NAPP_BANK", "N06_INQ12_NAPP_BANK_SCORE",
				"H01_CC_MAX_MONTH", "H01_CC_MAX_MONTH_SCORE",
				"D33_LN6_AVG_NOS_AMT", "C01_LN6_TAMT_ADDRATIO", "D01_LN_TAMT",
				"LN6_6TH_TAMT", "D33_C01_SCORE", "RATING_DATE", "DOCSTATUS",
				"DATA_SRC_DT", "R01_CC12_REVOL_RATE",
				"R01_CC12_REVOL_RATE_SCORE", "HINCOME_REG",
				"HINCOME_REG_SCORE", "P69_CC12_DELAY_RC_TIMES",
				"P25_CC6_PCODE_A_TIMES", "P25_CC6_PCODE_A_TIMES_SCORE",
				"MARR_EDU_SCORE", "P69_P19_SCORE"
				
				,"YPAY","YPAY_SCORE","SENIORITY","SENIORITY_SCORE","EDUCATION_N_SCORE"
				,"D63_LN_NOS_BANK","D63_SCORE","A21_CC6_RC_USE_MONTH","A21_SCORE"
				,"A11_CC6_RC_USE_BANK","A11_SCORE","D53_LN_6_TIMES_FLAG","D53_SCORE"
				, "PINCOME", "PINCOME_SCORE", "P68_CC6_DELAY_RC_TIMES", "P68_P19_SCORE"
				};

		String[] whereKeys = { "BR_CD", "NOTEID", "CUSTID", "DUPNO", "MOWTYPE",
				"MOWVER1", "MOWVER2", "JCIC_DATE", "ACCT_KEY" };

		Object[] args = new Object[keys.length + whereKeys.length];
		int count = 0;
		StringBuilder sql = new StringBuilder();
		for (String key : keys) {
			Object value = data.get(key);
			sql.append(sql.length() <= 0 ? "update DWADM.DW_RKSCORE set " : ",");
			sql.append(key).append("=?");

			args[count++] = value == null ? null : Util.trim(value).replaceAll(
					",", "");
		}

		StringBuilder sb = new StringBuilder();
		for (String key : whereKeys) {
			sb.append(sb.length() <= 0 ? "" : " and ");
			sb.append(key).append("=?");

			args[count++] = data.get(key);
		}

		sql.append(" where ").append(sb.toString());
		return this.getJdbc().updateBySQL(sql.toString(), args);
	}

	@Override
	public int insertScore(JSONObject data) {
		String[] keys = { "BR_CD", "NOTEID", "CUSTID", "DUPNO", "MOWTYPE",
				"MOWVER1", "MOWVER2", "JCIC_DATE","ACCT_KEY", "CUST_KEY", "LNGEFLAG",
				"CC12_REVOL_BAL", "CC12_REVOL_LIMIT",
				"R01_CC12_MAX_REVOL_RATE", "R01_CC12_MAX_REVOL_RATE_SCORE",
				"CC_REVOL_BAL", "CC_REVOL_PERMIT_LIMIT", "R10_REVOL_RATE",
				"R10_REVOL_RATE_SCORE", "D07_LN_NOS_TAMT",
				"D07_LN_NOS_TAMT_SCORE", "D12_CC12_MAX_AVGLIMIT",
				"D12_CC12_MAX_AVGLIMIT_SCORE", "D15_CC6_AVG_RC",
				"D15_CC6_AVG_RC_SCORE", "P19_CC12_PCODE_A_TIMES",
				"P19_CC12_PCODE_A_TIMES_SCORE", "MARRIAGE", "CHILDREN",
				"MARRIAGE_SCORE", "OCCUPATION_1", "OCCUPATION_1_SCORE",
				"COLL_3", "COLL_3_SCORE", "EDUCATION", "EDUCATION_SCORE",
				"N11_INQ3_LN_BANK", "N11_INQ3_LN_BANK_SCORE",
				"N06_INQ12_NAPP_BANK", "N06_INQ12_NAPP_BANK_SCORE",
				"H01_CC_MAX_MONTH", "H01_CC_MAX_MONTH_SCORE",
				"D33_LN6_AVG_NOS_AMT", "C01_LN6_TAMT_ADDRATIO", "D01_LN_TAMT",
				"LN6_6TH_TAMT", "D33_C01_SCORE", "RATING_DATE", "DOCSTATUS",
				"DATA_SRC_DT", "R01_CC12_REVOL_RATE",
				"R01_CC12_REVOL_RATE_SCORE", "HINCOME_REG",
				"HINCOME_REG_SCORE", "P69_CC12_DELAY_RC_TIMES",
				"P25_CC6_PCODE_A_TIMES", "P25_CC6_PCODE_A_TIMES_SCORE",
				"MARR_EDU_SCORE", "P69_P19_SCORE"
				

				,"YPAY","YPAY_SCORE","SENIORITY","SENIORITY_SCORE","EDUCATION_N_SCORE"
				,"D63_LN_NOS_BANK","D63_SCORE","A21_CC6_RC_USE_MONTH","A21_SCORE"
				,"A11_CC6_RC_USE_BANK","A11_SCORE","D53_LN_6_TIMES_FLAG","D53_SCORE"
				, "PINCOME", "PINCOME_SCORE", "P68_CC6_DELAY_RC_TIMES", "P68_P19_SCORE"};
		Object[] args = new Object[keys.length];
		int count = 0;

		StringBuilder sql = new StringBuilder();
		StringBuilder sb = new StringBuilder();

		for (String key : keys) {
			Object value = data.get(key);
			sql.append(sql.length() <= 0 ? "insert into DWADM.DW_RKSCORE("
					: ",");
			sql.append(key);
			sb.append(sb.length() <= 0 ? "" : ",").append("?");
			// sb.append(sb.length() <= 0 ? "" : ",").append(data.get(key));
			args[count++] = value == null ? null : Util.trim(value).replaceAll(
					",", "");
		}
		sql.append(") values (").append(sb.toString()).append(")");
		return this.getJdbc().updateBySQL(sql.toString(), args);
	}
}
