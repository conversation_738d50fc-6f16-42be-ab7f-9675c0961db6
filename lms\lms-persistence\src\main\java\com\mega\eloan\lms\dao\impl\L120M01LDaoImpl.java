/* 
 * L120M01LDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120M01LDao;
import com.mega.eloan.lms.model.L120M01L;

/** 簽報書各業務審核層級記錄檔 **/
@Repository
public class L120M01LDaoImpl extends LMSJpaDao<L120M01L, String> implements
		L120M01LDao {

	@Override
	public L120M01L findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01L> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120M01L> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120M01L> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120M01L> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120M01L> findByMainIdAndLoanKind(String mainId, String loanKind) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "loanKind", loanKind);

		search.addOrderBy("isFullCaseFlag", true);// 全案合併那筆一定放在最後
		search.addOrderBy("custId", true);// 照custId來排序，才不會看起很亂
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120M01L> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L120M01L findIsFullCaseByMainIdAndLoanKind(String mainId,
			String loanKind) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "loanKind", loanKind);
		search.addSearchModeParameters(SearchMode.EQUALS, "isFullCaseFlag", "Y");// 全案合併那筆
		return findUniqueOrNone(search);
	}

	@Override
	public L120M01L findByMainIdAndLoanKindAndCust(String mainId,
			String loanKind, String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "loanKind", loanKind);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01L> findByMainIdAndCust(String mainId, String custId,
			String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		search.setMaxResults(Integer.MAX_VALUE);
		List<L120M01L> list = createQuery(search).getResultList();
		return list;
	}
}