/*Q1和Q2類似，因此都擺一起處理  */
var data;
var pageAction = {
	handler : 'cls9041formhandler',
	grid : null,
	gridfile : null,//上傳檔案用grid
	Q1grid : null,
	Q3grid : null,
	build : function(){
		pageAction.grid = $("#gridview").iGrid({
			handler : 'cls9041gridhandler',
			height : 400,
			action :  "queryC004M01A",
			rowNum : 15,
			sortname: "bgnDate|rptType|createTime",
	        sortorder: "desc|asc|asc",
			//rownumbers:true,
			colModel : [{
					colHeader : "oid",
					name : 'oid',
					hidden : true //是否隱藏
				},{
					colHeader : i18n.cls9041m01["C004M01A.mainId"], //mainId
					hidden : true ,//是否隱藏
					name : 'mainId' //col.id
				},{
					colHeader : i18n.cls9041m01["C004M01A.unid"], //unid
					hidden : true ,//是否隱藏
					name : 'unid' //col.id
				},{
					colHeader : i18n.cls9041m01["C004M01A.rptType"], //報表類別
					align : "left",
					width : 3, //設定寬度
					sortable : true, //是否允許排序
					//formatter : 'click',
					//onclick : function,
					name : 'rptType' //col.id
				},{
					colHeader : i18n.cls9041m01["C004M01A.bgnDate"], //資料起日
					hidden : true ,//是否隱藏
					name : 'bgnDate' //col.id
				},{
					colHeader : i18n.cls9041m01["C004M01A.endDate"], //資料迄日
					hidden : true ,//是否隱藏
					name : 'endDate' //col.id
				},{
					colHeader : i18n.cls9041m01["C004M01A.rptName"], //報表名稱
					align : "left",
					width : 6, //設定寬度
					sortable : true, //是否允許排序
					//formatter : 'click',
					//onclick : alert(filePath),
					name : 'rptName' //col.id
				},{
					colHeader : i18n.cls9041m01["C004M01A.rptDate"], //確定報送日期
					width: 2 ,
					name : 'rptDate' //col.id						
				},{
					colHeader : i18n.cls9041m01["C004M01A.rmk"], //備註
					hidden : true ,//是否隱藏
					name : 'rmk' //col.id
				},{
					colHeader : i18n.cls9041m01["C004M01A.creator"], //建立人員號碼
					align : "left",
					width : 3, //設定寬度
					sortable : true, //是否允許排序
					//formatter : 'click',
					//onclick : function,
					name : 'creator' //col.id
				},{
					colHeader : i18n.cls9041m01["C004M01A.createTime"], //建立日期
					align : "center",
					width : 3, //設定寬度
					sortable : true, //是否允許排序
					//formatter : 'click',
					//onclick : function,
					name : 'createTime' //col.id
				},{
					colHeader : i18n.cls9041m01["C004M01A.updater"], //異動人員號碼
					hidden : true ,//是否隱藏
					name : 'updater' //col.id
				},{
					colHeader : i18n.cls9041m01["C004M01A.updateTime"], //異動日期
					hidden : true ,//是否隱藏
					name : 'updateTime' //col.id
				}
			],
			ondblClickRow: function(rowid){
				data = pageAction.grid.getRowData(rowid);
				if(data.rptType.charAt(0)=='S'){
					pageAction.openDetail(data);
				} else if(data.rptType.charAt(0)=='Q'){
					pageAction.openQ(data);
				}
			}				
		});
		//上傳檔案grid
		pageAction.gridfile = $("div#fileGrid").iGrid({
	        handler: 'cls9041gridhandler',
	        height: 50,
	        action: 'queryfile',
	        /*postData: {
	            formAction: "queryfile",
	            fileName: "useDoc",
	            mainId: responseJSON.mainId
	        },*/
	        rowNum: 1,
	        //multiselect: true,
	        colModel: [{
	            colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
	            name: 'srcFileName',
	            width: 120,
	            align: "left",
	            sortable: true,
	            formatter: 'click',
	            onclick: pageAction.download
	        }, {
	            colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
	            name: 'fileDesc',
	            width: 140,
	            align: "center",
	            sortable: true
	        }, {
	            colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
	            name: 'uploadTime',
	            hidden: true
	            /*width: 140,
	            align: "center",
	            sortable: true*/
	        }, {
	            name: 'oid',
	            hidden: true
	        }, {
	            name: 'mainid',
	            hidden: true
	        }, {
	            name: 'unid',
	            hidden: true
	        }]
	    });
		//Q1grid
		pageAction.Q1grid= $("div#Q1Grid").iGrid({
	        handler: 'cls9041gridhandler',
	        height: 250,
	        rowNum: 10,
	        action :  "showQ",
	        rownumbers:true,
	        colModel: [{
	            colHeader: i18n.cls9041m01['C004S01A.brno'],//分行代號
	            name: 'brno',
	            width: 140,
	            align: "left",
	            sortable: true
	        }, {
	            colHeader: i18n.cls9041m01['C004S01A.num'],//件數
	            name: 'num',
	            width: 140,
	            align: "center",
	            sortable: true
	        }, {
	            colHeader: i18n.cls9041m01['C004S01A.amt'],//貸款金額
	            name: 'amt',
	            width: 140,
	            align: "right",
	            sortable: true
	        }, {
	            name: 'oid',
	            hidden: true
	        }, {
	            name: 'mainid',
	            hidden: true
	        }, {
	            name: 'unid',
	            hidden: true
	        }]
	    });
		//Q3grid
		pageAction.Q3grid= $("div#Q3Grid").iGrid({
	        handler: 'cls9041gridhandler',
	        height: 250,
	        rowNum: 10,
	        action :  "showQ",
	        rownumbers:true,
	        colModel: [{
				colHeader : "oid",
				name : 'oid',
				hidden : true //是否隱藏
			},{
				colHeader : i18n.cls9041m01["C004S01A.mainId"], //mainId
				hidden : true, //是否隱藏
				name : 'mainId' //col.id
			},{
				colHeader : i18n.cls9041m01["C004S01A.brno"], //分行代號
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'brno' //col.id
			},{
				colHeader : i18n.cls9041m01["C004S01A.num"], //件數
				align : "right",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'num' //col.id
			},{
				colHeader : i18n.cls9041m01["C004S01A.amt"], //貸款金額
				align : "right",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'amt' //col.id
			},{
				colHeader : i18n.cls9041m01["C004S01A.oweInterest"], //積欠利息
				align : "right",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'oweInterest' //col.id
			},{
				colHeader : i18n.cls9041m01["C004S01A.dueInterest"], //逾期利息
				align : "right",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'dueInterest' //col.id
			},{
				colHeader : i18n.cls9041m01["C004S01A.litigate"], //訴訟費用
				align : "right",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'litigate' //col.id
			},{
				colHeader : i18n.cls9041m01["C004S01A.creator"], //建立人員號碼
				hidden : true ,//是否隱藏
				name : 'creator' //col.id
			},{
				colHeader : i18n.cls9041m01["C004S01A.createTime"], //建立日期
				hidden : true ,//是否隱藏
				name : 'createTime' //col.id
			},{
				colHeader : i18n.cls9041m01["C004S01A.updater"], //異動人員號碼
				hidden : true ,//是否隱藏
				name : 'updater' //col.id
			},{
				colHeader : i18n.cls9041m01["C004S01A.updateTime"], //異動日期
				hidden : true ,//是否隱藏
				name : 'updateTime' //col.id
			}
		]
	    });
		//build button 
		//上傳檔案按鈕
	    $("#uploadFile").click(function(){
	    	if(data.rptDate!=""){
	    		MegaApi.showErrorMessage(i18n.def["confirmTitle"],i18n.cls9041m01["hadSend"]);
	    		return;
	    	}else if(pageAction.gridfile.jqGrid('getDataIDs').length>0){//如grid已有資料=>無法insert，另外如果不加length他會連續執行此段數次
	    		MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.cls9041m01["onlyOneFile"]);
	    		return;
	    	}else{
	    		var limitFileSize = 3145728;
		        MegaApi.uploadDialog({
		        	
		            fieldId: "fms",
		            fieldIdHtml: "size='30'",
		            fileDescId: "fileDesc",
		            fileDescHtml: "size='30' maxlength='30'",
		            subTitle: i18n.def('insertfileSize', {
		                'fileSize': (limitFileSize / 1048576).toFixed(2)
		            }),
		            limitSize: limitFileSize,
		            width: 320,
		            height: 190,
		            data: {
		            	mainId: data.mainId,
		                sysId: "LMS"
		            },
		            success: function(){
		            	pageAction.gridfile.trigger("reloadGrid");
		            }
		        });
	    	}
	    });
	    $("#deleteFile").click(function(){
	    	if(pageAction.grid.getSingleData().rptDate==""){
	    		var rowData = pageAction.gridfile.getSingleData();
				if(rowData){
					MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
						if (action){
							$.ajax({
								handler : pageAction.handler,
								action : 'deleteFile',
								data : {
									oid: rowData.oid
								},
								success:function(responseData){
									pageAction.gridfile.trigger("reloadGrid");
									MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["confirmDeleteSuccess"]);
								}
							});
						}
					});
				}
	    	}
	    	else{
	    		MegaApi.showErrorMessage(i18n.def["confirmTitle"],i18n.cls9041m01["hadSend"]);
	    	}
	    });
		//篩選
		$("#buttonPanel").find("#btnFilter").click(function() {
			$("#filter").thickbox({
				title : '', 
				width : 400,
				height : 200,
				modal : true,
				align : 'center',
				valign: 'bottom',
				i18n: i18n.def,
				buttons : {
					'sure' : function(){
						if($('#filterForm').valid()){
							var type = new Array();
							$('input:checkbox:checked[name="rptType"]').each(function(i) {
								type[i] = this.value; 
							});
							$.thickbox.close();
							
							pageAction.grid.jqGrid("setGridParam", {
								postData : {
									rptType: type,
									creator: $('#creator').val(),
									bgnDate: $('#bgnDate').val(),
									endDate: $('#endDate').val()
								},
								page : 1,
								search : true
							}).trigger("reloadGrid");
						}
					},
					'close' : function(){	
						$.thickbox.close();
					}
				}
			});
		})
		//新增
		.end().find("#btnAdd").click(function() {
			$("#add").thickbox({
				title : i18n.cls9041m01["C004M01A.addTitle"],
				width : 300,
				height : 190,
				modal : true,
				align : 'center',
				valign: 'bottom',
				i18n: i18n.def,
				buttons : {
					'sure' : function(){
						var val_rptType = $("input[name=rptType]:checked").val();
						$.thickbox.close();
						//=====						
						chooseDataYm(val_rptType).done(function(json_chooseDataYm){
							//json.aaa 為1
							$.ajax({
								handler : pageAction.handler,
								action : 'addC004M01A',
								data : $.extend({'rptType':val_rptType}, json_chooseDataYm||{}),
								success:function(responseData){									
									pageAction.reloadGrid();
									MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["addSuccess"]);
								}
							});
						});
						
					},
					
					'close' : function(){	
						$.thickbox.close();
					}
				}
			});
		})
		//調閱
		.end().find("#btnView").click(function() {
			data = pageAction.getRowData();
			if(data){
				if(data.rptType.charAt(0)=='S'){
					pageAction.openDetail(data);
				} else if(data.rptType.charAt(0)=='Q'){
					pageAction.openQ(data);
				}					
			}
		})
		//刪除
		.end().find("#btnDelete").click(function() {
			data = pageAction.getRowData();
			if (data){
				if(data.rptDate==""){
					MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
						if (action){
							$.ajax({
								handler : pageAction.handler,
								action : 'deleteC004M01A',
								data : data,
								success:function(responseData){
									pageAction.reloadGrid();
									MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["confirmDeleteSuccess"]);
								}
							});
						}
					});
				}else{
					MegaApi.showErrorMessage(i18n.def["confirmTitle"],i18n.cls9041m01["hadSend"]);
				}
			}
		})
		
	    
	},
	/**
	 * 開啟detail
	 */
	openDetail : function(data){
		//套進數字
		var form = $("#detailForm");
		form.find("#createTime").val(data.createTime);
		form.find("#bgnDate").val(data.bgnDate);
		form.find("#endDate").val(data.endDate);
		form.find("#creator").val(data.creator);
		form.find("#rptDate").val(data.rptDate);
		form.find("#rptName").val(data.rptName);
		form.find("#rptType").val(data.rptType);
		form.find("#oid").val(data.oid);
		form.find("#mainId").val(data.mainId);
		
		//檔案IO部分
		pageAction.gridfile.jqGrid("setGridParam", {
			postData : data,
			page : 1,
			search : true
		}).trigger("reloadGrid");
		//開視窗
		$("#detail").thickbox({
			title : i18n.cls9041m01["C004M01A.detailTitle"],
			width : 600,
			height : 350,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.cls9041m01,
			buttons : {				
				'createQ' : function(){ //產生報表
					if(data.rptDate==""||data.rptDate==null){
						var rows = pageAction.gridfile.jqGrid('getDataIDs').length;
						if (rows>0){	
							$.ajax({
								handler : pageAction.handler,
								action : 'createRpt',
								data : {},
								form : 'detailForm',
								success:function(response){
									$.thickbox.close();
									pageAction.reloadGrid();
									pageAction.openQ(response);
								}
							});
						}
						else
							MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.cls9041m01["noFileError"]);
					}else{
						MegaApi.showErrorMessage(i18n.def["confirmTitle"],i18n.cls9041m01["hadSend"]);
					}
				},
				'close' : function(){	
					$.thickbox.close();
				}
			}
		});
	},
	/**
	 * 開啟Q1
	 */
	openQ : function(data){
		//套進數字
		var form,frame;
		if(data.rptType.charAt(1)=="3"){
			form = $("#Q3Form");
			frame = $("#Q3");
			pageAction.Q3grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			form=$("#Q1Form");
			frame=$("#Q1");
			pageAction.Q1grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}
		form.find("#createTime").val(data.createTime);
		form.find("#bgnDate").val(data.bgnDate);
		form.find("#endDate").val(data.endDate);
		form.find("#creator").val(data.creator);
		form.find("#rptDate").val(data.rptDate);
		form.find("#rptName").val(data.rptName);		
		
		//開視窗
		frame.thickbox({
			title : i18n.cls9041m01["C004M01A.Q1Title"],
			width : data.rptType=="Q3"?1000:500,
			height : 530,
			modal : true,
			align : 'top',
			valign: 'left',
			i18n: i18n.cls9041m01,
			buttons : {
				'sendQ' : function(){//確定報送
					if(data.rptDate==""||data.rptDate==null){
						MegaApi.confirmMessage("此功能確定之後無法回復，是否繼續執行？", function(bool){
							if (bool){
								$.ajax({
									handler : pageAction.handler,
									action : 'sendQ',
									data : {
										unid : data.unid
									},
									success:function(response){
										$.thickbox.close();
										pageAction.reloadGrid();
										MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.cls9041m01["sendSuccess"]);
									}
								});		
							}
						});
					}else{
						MegaApi.showErrorMessage(i18n.def["confirmTitle"],i18n.cls9041m01["hadSend"]);
					}
				},
				'outputDoc' : function(){//產生word檔
					 $.capFileDownload({
				            handler: "lmsdownloadformhandler",
				            data: {
				            	type: 1,
				            	rptOid: data.oid,
				            	mainId: data.mainId,
				            	rptType: data.rptType,
				                fileDownloadName: "cls9041W0"+data.rptType.charAt(1)+".doc",
				                serviceName: "cls9041docservice"
				            }
				        });
					/*$.form.submit({
			            url: "../simple/FileProcessingService",
			            target: "_blank",
			            data: {
			            	type: 1,
			            	rptOid: data.oid,
			            	mainId: data.mainId,
			            	rptType: data.rptType,
			                fileDownloadName: "cls9041W0"+data.rptType.charAt(1)+".doc",
			                serviceName: "cls9041docservice"
			            }
			        });*/
				},
				'close' : function(){	
					$.thickbox.close();
				}
			}
		});
	},
	/**
	 * 取得資料表之選擇列
	 */
	getRowData : function(){
		var row = pageAction.grid.getGridParam('selrow');
		if (row) {
			data = pageAction.grid.getRowData(row);
		}else{
			MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["grid.selrow"]);
		}
		return data;
	},
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	},
	/**
	 * 檔案下載
	 */
    download : function(cellvalue, options, data){
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
            	fileOid: data.oid
            }
        });
    }
}

function chooseDataYm(val_rptType){
	var my_dfd = $.Deferred();
	if(val_rptType=="S3"){
		var sysdate = CommonAPI.getToday().split("-");
		
		var $f  = $("#S3_chooseDataYmForm");	
		//init val
		$f.find("#dataYearB").val(sysdate[0]);
		$f.find("#dataMmB").val(sysdate[1]);
		$f.find("#dataYearE").val(sysdate[0]);
		$f.find("#dataMmE").val(sysdate[1]);
		
		$("#div_S3_chooseDataYm").thickbox({		
			title : '', width : 400, height : 150, modal : true, i18n:i18n.def,
			buttons : {		
				"sure" : function(){
					if (!$f.valid()) {
                        return;
                    }
					$.thickbox.close();
					my_dfd.resolve( $f.serializeData() );	
				},
				"cancel" : function(){
					$.thickbox.close();
					my_dfd.reject();	
				}
			}
		});
			
	}else{
		my_dfd.resolve();
	}	
	return my_dfd.promise();
}

$(document).ready(function(){
	pageAction.build();
});