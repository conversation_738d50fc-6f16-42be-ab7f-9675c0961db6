package com.mega.eloan.lms.fms.handler.form;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.pages.LMS7600M01Page;
import com.mega.eloan.lms.fms.service.LMS7600Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L140MM4A;
import com.mega.eloan.lms.model.L140MM4B;
import com.mega.eloan.lms.model.L140MM4C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 空地貸款維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms7600m01formhandler")
@DomainClass(L140MM4A.class)
public class LMS7600M01FormHandler extends AbstractFormHandler {

	@Resource
	LMS7600Service lms7600Service;
	
	@Resource
	BranchService branchService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	EloandbBASEService eloandbService;
	
	@Resource
	DwLnquotovService dwLnquotovService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	Properties pop = MessageBundleScriptCreator.getComponentResource(LMS7600M01Page.class);
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		
		L140MM4A l140mm4a = lms7600Service.findModelByOid(L140MM4A.class, oid);
		result.set("showCustId", StrUtils.concat(CapString.trimNull(l140mm4a.getCustId()), " ",
				CapString.trimNull(l140mm4a.getDupNo()), " ", CapString.trimNull(l140mm4a.getCustName())));
			
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL140mm4a(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> dataMap = new HashMap<String, String>();

		List<L140MM4C> lastData = lms7600Service.findL140mm4csByMainId(mainId);
		if(lastData == null || lastData.isEmpty()){
			if (!Util.isEmpty(oid)) {
				L140MM4A l140mm4a = lms7600Service.findModelByOid(L140MM4A.class, oid);
				//第一次啟案自動塞入前案資訊
				dataMap = lms7600Service.getElfData(l140mm4a);
				result.putAll(dataMap);
				result = formatResultShow(result, l140mm4a, page);
			} else {
				// 開啟新案帶入起案的分行和目前文件狀態
				result.set(
						"docStatus",
						this.getMessage("docStatus."
								+ CreditDocStatusEnum.海外_編製中.getCode()));
				result.set("ownBrId", user.getUnitNo());
				result.set(
						"ownBrName",
						StrUtils.concat(" ",
								branchService.getBranchName(user.getUnitNo())));
				result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode());
			}
		} else {
			if (!Util.isEmpty(oid)) {
				L140MM4A l140mm4a = lms7600Service.findModelByOid(L140MM4A.class, oid);
				dataMap = lms7600Service.getData(l140mm4a);
				result.putAll(dataMap);
				result = formatResultShow(result, l140mm4a, page);
			}
		}
		
		return result;
	}
	
	/**
	 * 刪除L140MM4A 空地貸款維護作業
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL140mm4a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			if (lms7600Service.deleteL140mm4as(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}
	
	/**
	 * 格式化顯示訊息
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L140MM4A l140mm4a, Integer page) throws CapException {
		String mainId = l140mm4a.getMainId();

		switch (page) {
		case 1:
			result = DataParse.toResult(l140mm4a);
			List<L140MM4B> l140mm4blist = (List<L140MM4B>) lms7600Service
					.findListByMainId(L140MM4B.class, mainId);
			if (!Util.isEmpty(l140mm4blist)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L140MM4B l140mm4b : l140mm4blist) {
					// 要加上人員代碼
					String type = Util.trim(l140mm4b.getStaffJob());
					String userId = Util.trim(l140mm4b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						result.set("showApprId", userId + " " + value);
					} else if ("L3".equals(type)) {
						bossId.append(bossId.length() > 0 ? "<br/>" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {
						result.set("reCheckId", userId + " " + value);
					} else if ("L5".equals(type)) {
						result.set("managerId", userId + " " + value);
					} else if ("L6".equals(type)) {
						result.set("mainApprId", userId + " " + value);
					} else if ("L7".equals(type)) {
						result.set("mainReCheckId", userId + " " + value);
					}
				}
				result.set("bossId", bossId.toString());
			}
			result.set("ownBrName",
					" " + branchService.getBranchName(l140mm4a.getOwnBrId()));

			StringBuilder cntrNo = new StringBuilder("");

			result.set("creator", lmsService.getUserName(l140mm4a.getCreator()));
			result.set("updater", lmsService.getUserName(l140mm4a.getUpdater()));
			result.set("docStatus",
					getMessage("docStatus." + l140mm4a.getDocStatus()));
			result.set("cntrNo", cntrNo.toString());
			break;
		}// close switch case

		result.set("showCustId", StrUtils.concat(CapString.trimNull(l140mm4a.getCustId()), " ",
				CapString.trimNull(l140mm4a.getDupNo()), " ", CapString.trimNull(l140mm4a.getCustName())));
		result.set("docStatusVal", l140mm4a.getDocStatus());
		result.set("docStatusVal", l140mm4a.getDocStatus());
		result.set("cntrNo", l140mm4a.getCntrNo());
		result.set(EloanConstants.OID, CapString.trimNull(l140mm4a.getOid()));
		result.set(EloanConstants.MAIN_OID,	CapString.trimNull(l140mm4a.getOid()));
		result.set(EloanConstants.MAIN_ID, CapString.trimNull(l140mm4a.getMainId()));
		

		L140MM4C onData = lms7600Service.findLastL140mm4c(mainId);
		if(onData != null){
			result.set("cntrNoOn", onData.getContract());
			result.set("useDateOn", lms7600Service.changeDateToString(onData.getUse_Date()));
			result.set("useCdOn", onData.getUseCd());
			result.set("useTypeOn", onData.getUseType());
			result.set("landTypeOn", onData.getLandType());
			result.set("landKindOn", onData.getLandKind());
			result.set("idleLandOn", Util.isEmpty(onData.getIdleLand()) ? "" :
				pop.getProperty(onData.getIdleLand()));
			result.set("ctlTypeOn", onData.getCtlType());
			result.set("fstDateOn", lms7600Service.changeDateToString(onData.getFstDate()));
			result.set("lstDateOn", lms7600Service.changeDateToString(onData.getLstDate()));
			result.set("removeDtOn", lms7600Service.changeDateToString(onData.getRrmoveDate()));
			result.set("elFlagOn", onData.getElFlag());
			result.set("endDateOn", lms7600Service.changeDateToString(onData.getEndDate()));
			result.set("documentNoOn", onData.getDocumentNo());
			result.set("cstDateOn", lms7600Service.changeDateToString(onData.getCstDate()));
			result.set("adoptFgOn", onData.getAdoptFg());
			result.set("rateAddOn", NumConverter.addComma(
					LMSUtil.toBigDecimal(onData.getRateAdd())));
			result.set("custRoaOn", NumConverter.addComma(
					LMSUtil.toBigDecimal(onData.getCustRoa())));
			result.set("relRoaOn", NumConverter.addComma(
					LMSUtil.toBigDecimal(onData.getRelRoa())));
			result.set("cstReasonOn", onData.getCstReason());
			result.set("isLegalOn", onData.getIsLegal());
		}
		
		L140MM4C currentData = lms7600Service.findCurrentL140mm4c(mainId);
		if(currentData != null){
			result.set("cntrNo", Util.trim(currentData.getContract()));
			result.set("useDate", lms7600Service.changeDateToString(currentData.getUse_Date()));
			result.set("useCd", Util.trim(currentData.getUseCd()));
			result.set("useType", Util.trim(currentData.getUseType()));
			result.set("landType", Util.trim(currentData.getLandType()));
			result.set("landKind", Util.trim(currentData.getLandKind()));
			result.set("idleLand", Util.trim(currentData.getIdleLand()));
			result.set("ctlType", Util.trim(currentData.getCtlType()));
			result.set("fstDate", lms7600Service.changeDateToString(currentData.getFstDate()));
			result.set("lstDate", lms7600Service.changeDateToString(currentData.getLstDate()));
			result.set("removeDt", lms7600Service.changeDateToString(currentData.getRrmoveDate()));
			result.set("elFlag", Util.trim(currentData.getElFlag()));
			result.set("endDate", lms7600Service.changeDateToString(currentData.getEndDate()));
			result.set("documentNo", Util.trim(currentData.getDocumentNo()));
			result.set("cstDate", lms7600Service.changeDateToString(currentData.getCstDate()));
			result.set("adoptFg", Util.trim(currentData.getAdoptFg()));
			result.set("rateAdd", NumConverter.addComma(
					LMSUtil.toBigDecimal(currentData.getRateAdd())));
			result.set("custRoa", NumConverter.addComma(
					LMSUtil.toBigDecimal(currentData.getCustRoa())));
			result.set("relRoa", NumConverter.addComma(
					LMSUtil.toBigDecimal(currentData.getRelRoa())));
			result.set("cstReason", Util.trim(currentData.getCstReason()));
			result.set("isLegal", Util.trim(currentData.getIsLegal()));
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newl140mm4a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String l140mm4aMainid = "";
		L140MM4A l140mm4a = new L140MM4A();
		l140mm4a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		l140mm4a.setOwnBrId(user.getUnitNo());

		l140mm4aMainid = IDGenerator.getUUID();

		l140mm4a.setMainId(l140mm4aMainid);
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		l140mm4a.setTxCode(txCode);
		// UPGRADE: 待確認，URL是否正確
		l140mm4a.setDocURL(params.getString("docUrl"));
		l140mm4a.setDeletedTime(CapDate.getCurrentTimestamp());
		l140mm4a.setCntrNo(params.getString("cntrNo"));
		l140mm4a.setCustId(params.getString("custId", null));
		l140mm4a.setDupNo(params.getString("dupNo", null));
		l140mm4a.setCustName(params.getString("custName", null));		
		
		lms7600Service.save(l140mm4a);

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(EloanConstants.OID, l140mm4a.getOid());
		result.set(EloanConstants.MAIN_ID, l140mm4a.getMainId());
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryOnLine(PageParameters params)
			throws CapException {
		String mainId = params.getString("mainId");

		CapAjaxFormResult result = new CapAjaxFormResult();

		L140MM4A meta = lms7600Service.findL140mm4aByUniqueKey(mainId);

		Map<String, String> dataMap = new HashMap<String, String>();

		dataMap = lms7600Service.getElf600Data(meta);
		result.putAll(dataMap);

		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL140mm4a(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String oid = Util.trim(params.getString(EloanConstants.OID));

		String form = Util.trim(params.getString("mainPanel"));
		JSONObject jsonData = null;

		L140MM4A l140mm4a = null;
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		String showMsg1 = "";
		String custId = "";
		String dupNo = "";
		if (Util.isNotEmpty(oid)) {
			l140mm4a = lms7600Service.findModelByOid(L140MM4A.class, oid);
			l140mm4a.setRandomCode(IDGenerator.getRandomCode());
			custId = l140mm4a.getCustId();
			dupNo = l140mm4a.getDupNo();
		}
		l140mm4a.setDeletedTime(null);

		String validate = null;
		switch (page) {
		case 1:
			jsonData = JSONObject.fromObject(form);
			DataParse.toBean(jsonData, l140mm4a);
			validate = Util.validateColumnSize(l140mm4a, pop, "L140MM4A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}

			lms7600Service.save(l140mm4a);
			result.set("randomCode", l140mm4a.getRandomCode());
			break;
		}

		String cntrNo = params.getString("cntrNo", "");
		String useDate = params.getString("useDate", "");
		String useCd = params.getString("useCd", "");
		String useType = params.getString("useType", "");
		String landType = params.getString("landType", "");
		
		String landKind = params.getString("landKind", "");
		
		String idleLand = params.getString("idleLand", "");
		String ctlType = params.getString("ctlType", "");
		String fstDate = params.getString("fstDate", "");
		String lstDate = params.getString("lstDate", "");
		
		String removeDt = params.getString("removeDt", "");
		String elFlag = params.getString("elFlag", "");
		String endDate = params.getString("endDate", "");
		String documentNo = params.getString("documentNo", "");
		String cstDate = params.getString("cstDate", "");
		String adoptFg = params.getString("adoptFg", "");
		
		BigDecimal rateAdd = Util.parseBigDecimal(0);
		if(!Util.isEmpty(params.getString("rateAdd"))){
			rateAdd = LMSUtil.nullToZeroBigDecimal(
					NumConverter.delCommaString(params.getString("rateAdd")));
		}
		
		BigDecimal custRoa = Util.parseBigDecimal(0);
		if(!Util.isEmpty(params.getString("custRoa"))){
			custRoa = LMSUtil.nullToZeroBigDecimal(
					NumConverter.delCommaString(params.getString("custRoa")));
		}
		
		BigDecimal relRoa = Util.parseBigDecimal(0);
		if(!Util.isEmpty(params.getString("relRoa"))){
			relRoa = LMSUtil.nullToZeroBigDecimal(
					NumConverter.delCommaString(params.getString("relRoa")));
		}
		
		String cstReason = params.getString("cstReason", "");
		
		String isLegal = params.getString("isLegal", "");
		
		Map<String, Object> cantEmpty = new HashMap<String, Object> ();
		cantEmpty.put("cntrNo", cntrNo);
		cantEmpty.put("useDate", useDate);
		cantEmpty.put("useCd", useCd);
		cantEmpty.put("useType", useType);
		cantEmpty.put("landType", landType);
		cantEmpty.put("landKind", landKind);
		cantEmpty.put("ctlType", ctlType);
		if(adoptFg.contains("3")){
			cantEmpty.put("rateAdd", params.getString("rateAdd"));
			cantEmpty.put("custRoa", params.getString("custRoa"));
			cantEmpty.put("relRoa", params.getString("relRoa"));
		}

		for(Map.Entry<String, Object> entry : cantEmpty.entrySet()){
			if(Util.isEmpty(entry.getValue())){
				// 不得為空白
				throw new CapMessageException((pop.getProperty("L140MM4A."+entry.getKey())+
						pop.getProperty("cantEmpty")), getClass());
			}
		}
		
		//判斷cntrNo
		//參考LMS7500Grid 	queryGetCntrno
		boolean checkCntrNo = true;
		if(!custId.isEmpty() && !dupNo.isEmpty()){
			//簽報書的額度明細表額度序號
			List<Map<String, Object>> l140m01as = eloandbService.selDistinctCntrnoByCustidDupno(custId, dupNo);
			for (Map<String, Object> l140m01a : l140m01as) {
				String getCntrNo = Util.trim(l140m01a.get("CNTRNO"));
				if(!getCntrNo.isEmpty()){
					if(Util.equals(getCntrNo, cntrNo)){
						checkCntrNo = false;
						break;
					}
				}
			}
			
			if(checkCntrNo){
				// 海外查 DW_ASLNQUOT
				List<Map<String, Object>> dwLncntrovs = dwLnquotovService.selDistinctCntrnoByCustidDupno(custId, dupNo);
				for (Map<String, Object> lncntr : dwLncntrovs) {
					String dwCntrNo = Util.trim(lncntr.get("CNTRNO"));
					if(!dwCntrNo.isEmpty()){
						if(Util.equals(dwCntrNo, cntrNo)){
							checkCntrNo = false;
							break;
						}
					}
				}
			}
			
			if(checkCntrNo){
				custId = Util.addSpaceWithValue(custId, 10);
				//遠匯	LNF197		非遠匯	LNF020
				List<Map<String, Object>> lnfs = misdbBASEService.selDistinctCntrnoByCustidDupno(custId, dupNo);
				for (Map<String, Object> lnf : lnfs) {		
					String misCntrNo = Util.trim(lnf.get("CNTRNO"));
					if(!misCntrNo.isEmpty()){
						if(Util.equals(misCntrNo, cntrNo)){
							checkCntrNo = false;
							break;
						}
					}
				}
			}
		}
		if(checkCntrNo){	// 客戶名下無此額度序號
			throw new CapMessageException(
					pop.getProperty("cntrNoNotExist"), getClass());
		}
		
		
		//判斷documentNo	民國年 + 分行別+{LMS企金、CLS個金}+流水號
		// 舊案號 12碼 099005LMS001	新案號14碼 105005LMS00001
		if(Util.isNotEmpty(Util.trim(documentNo)) && this.notDocumentNoFmt(documentNo)){
			throw new CapMessageException((pop.getProperty("L140MM4A.documentNo")+
					pop.getProperty("notMatchFormat")), getClass());
		}

		L140MM4C l140mm4c = lms7600Service.findCurrentL140mm4c(l140mm4a.getMainId());
		if (l140mm4c != null) {
			l140mm4c.setContract(cntrNo);
			l140mm4c.setUse_Date(Util.isEmpty(useDate) ? null : CapDate.parseDate(useDate));
			l140mm4c.setUseCd(useCd);
			l140mm4c.setUseType(useType);
			l140mm4c.setLandType(landType);
			
			l140mm4c.setLandKind(landKind);
			
			l140mm4c.setIdleLand(idleLand);
			l140mm4c.setCtlType(ctlType);
			l140mm4c.setFstDate(Util.isEmpty(fstDate) ? null : CapDate.parseDate(fstDate));
			l140mm4c.setLstDate(Util.isEmpty(lstDate) ? null : CapDate.parseDate(lstDate));
			
			l140mm4c.setRrmoveDate(Util.isEmpty(removeDt) ? null : CapDate.parseDate(removeDt));
			l140mm4c.setElFlag(elFlag);
			l140mm4c.setEndDate(Util.isEmpty(endDate) ? null : CapDate.parseDate(endDate));
			l140mm4c.setDocumentNo(documentNo.toUpperCase());
			l140mm4c.setCstDate(Util.isEmpty(cstDate) ? null : CapDate.parseDate(cstDate));
			l140mm4c.setAdoptFg(adoptFg);
			
			l140mm4c.setRateAdd(rateAdd);
			l140mm4c.setCustRoa(custRoa);
			l140mm4c.setRelRoa(relRoa);
			l140mm4c.setCstReason(cstReason);
			
			boolean firstChange = (Util.equals(fstDate, lstDate)) ? true : false;
			boolean cst_Date = (Util.equals(Util.nullToSpace(cstDate),"")) ? false : true;
			isLegal = lmsService.checkIsLegal(adoptFg, ctlType, firstChange, cst_Date, 
					rateAdd, custRoa, custRoa);

			l140mm4c.setIsLegal(isLegal);
			lms7600Service.save(l140mm4c);
		} else {
			throw new CapMessageException("ERROR!!", getClass());
		}

		if (Util.isEmpty(showMsg1)) {
			if (showMsg) {
				showMsg1 = RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功);
			}
			l140mm4a.setChkYN("Y");
			lms7600Service.save(l140mm4a);
		} else {
			if (showMsg) {

			}else{
				throw new CapMessageException(showMsg1,	getClass());
			}
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, showMsg1);

		result.set(EloanConstants.OID, CapString.trimNull(l140mm4a.getOid()));
		result.set(EloanConstants.MAIN_OID,	CapString.trimNull(l140mm4a.getOid()));
		result.set(EloanConstants.MAIN_ID, CapString.trimNull(l140mm4a.getMainId()));
		result.set("showCustId", CapString.trimNull(l140mm4a.getCustId()) + " "
						+ CapString.trimNull(l140mm4a.getDupNo()) + " "
						+ CapString.trimNull(l140mm4a.getCustName()));
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;

	}
	
	 /*** 呈主管覆核(呈主管 主管覆核 拆2個method) */
	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L140MM4A l140mm4a = (L140MM4A) lms7600Service.findModelByOid(L140MM4A.class, oid);
		String[] formSelectBoss = params.getStringArray("selectBoss");

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		if (!Util.isEmpty(formSelectBoss)) {

			String manager = Util.trim(params.getString("manager"));
			List<L140MM4B> models = (List<L140MM4B>) lms7600Service
					.findListByMainId(L140MM4B.class, l140mm4a.getMainId());
			if (!models.isEmpty()) {
				lms7600Service.deleteL140mm4bs(models, false);
			}
			List<L140MM4B> l140mm4bs = new ArrayList<L140MM4B>();
			for (String people : formSelectBoss) {
				L140MM4B l140mm4b = new L140MM4B();
				l140mm4b.setCreator(user.getUserId());
				l140mm4b.setCreateTime(CapDate.getCurrentTimestamp());
				l140mm4b.setMainId(l140mm4a.getMainId());
				l140mm4b.setBranchType(user.getUnitType());
				l140mm4b.setBranchId(user.getUnitNo());
				// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
				l140mm4b.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				l140mm4b.setStaffNo(people);
				l140mm4bs.add(l140mm4b);
			}
			L140MM4B managerL140mm4b = new L140MM4B();
			managerL140mm4b.setCreator(user.getUserId());
			managerL140mm4b.setCreateTime(CapDate.getCurrentTimestamp());
			managerL140mm4b.setMainId(l140mm4a.getMainId());
			managerL140mm4b.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			managerL140mm4b.setStaffNo(manager);
			managerL140mm4b.setBranchType(user.getUnitType());
			managerL140mm4b.setBranchId(user.getUnitNo());
			l140mm4bs.add(managerL140mm4b);
			L140MM4B apprL140mm4b = new L140MM4B();
			apprL140mm4b.setCreator(user.getUserId());
			apprL140mm4b.setCreateTime(CapDate.getCurrentTimestamp());
			apprL140mm4b.setMainId(l140mm4a.getMainId());
			apprL140mm4b.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprL140mm4b.setStaffNo(user.getUserId());
			apprL140mm4b.setBranchType(user.getUnitType());
			apprL140mm4b.setBranchId(user.getUnitNo());
			l140mm4bs.add(apprL140mm4b);
			lms7600Service.saveL140mm4bList(l140mm4bs);
		}
		Boolean upMis = false;
		L140MM4B l140mm4bL4 = new L140MM4B();
		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {
			l140mm4a.setApprover(user.getUserId());
			l140mm4a.setApproveTime(CapDate.getCurrentTimestamp());
			upMis = true;
			L140MM4B l140mm4b = lms7600Service.findL140mm4b(
					l140mm4a.getMainId(), user.getUnitType(), user.getUnitNo(),
					user.getUserId(), UtilConstants.STAFFJOB.執行覆核主管L4);
			if (l140mm4b == null) {
				l140mm4b = new L140MM4B();
				l140mm4b.setCreator(user.getUserId());
				l140mm4b.setCreateTime(CapDate.getCurrentTimestamp());
				l140mm4b.setMainId(l140mm4a.getMainId());
				l140mm4b.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				l140mm4b.setStaffNo(user.getUserId());
				l140mm4b.setBranchType(user.getUnitType());
				l140mm4b.setBranchId(user.getUnitNo());
			}
			l140mm4bL4 = l140mm4b;
		}

		if (!Util.isEmpty(l140mm4a)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {
					// 退回部檢查
					if (params.getBoolean("flowAction")) {
						L140MM4B l140mm4b = lms7600Service.findL140mm4b(
								l140mm4a.getMainId(), user.getUnitType(),
								user.getUnitNo(), user.getUserId(),
								UtilConstants.STAFFJOB.經辦L1);

						if (l140mm4b != null) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						} else {
							lms7600Service.save(l140mm4bL4);
							upMis = true;
						}
					}
				}
				lms7600Service.flowAction(l140mm4a.getOid(), l140mm4a,
						params.containsKey("flowAction"),
						params.getAsBoolean("flowAction", false), upMis);
			} catch (FlowException t1) {
				logger.error(
						"[flowAction] lms7600Service.flowAction FlowException!!",
						t1);
				throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
			} catch (Throwable t1) {
				logger.error(
						"[flowAction]  lms7600Service.flowAction EXCEPTION!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}

		return new CapAjaxFormResult();
	}
	
	public IResult echo_custId(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set("custId", Util.trim(params.getString("custId")));
		result.set("dupNo", Util.trim(params.getString("dupNo")));
		result.set("custName", Util.trim(params.getString("custName")));
		result.set("cntrNo", Util.trim(params.getString("cntrNo")));
		return result;
	}

	
	public IResult checkIsLegal(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String ctlType = params.getString("ctlType", "");
		String fstDate = params.getString("fstDate", "");
		String lstDate = params.getString("lstDate", "");
		String cst_Date = params.getString("cstDate", "");
		String adoptFg = params.getString("adoptFg", "");
		
		boolean need = false;
		if(adoptFg.contains("3")){
			need = true;
		}
		
		BigDecimal rateAdd = Util.parseBigDecimal(0);
		if(Util.notEquals("", params.getString("rateAdd")) 
				&& Util.isNumeric(NumConverter.delCommaString(params.getString("rateAdd")))){
			rateAdd = Util.parseBigDecimal(
					NumConverter.delCommaString(String.valueOf(params.getDouble("rateAdd"))));
		} else {
			if(need){
				throw new CapMessageException((pop.getProperty("L140MM4A.rateAdd")+
						pop.getProperty("notMatchFormat")), getClass());
			}
		}
		BigDecimal custRoa = Util.parseBigDecimal(0);		
		if(Util.notEquals("", params.getString("custRoa")) 
				&& Util.isNumeric(NumConverter.delCommaString(params.getString("custRoa")))){
			custRoa = Util.parseBigDecimal(
					NumConverter.delCommaString(String.valueOf(params.getDouble("custRoa"))));
		} else {
			if(need){
				throw new CapMessageException((pop.getProperty("L140MM4A.custRoa")+
						pop.getProperty("notMatchFormat")), getClass());
			}
		}
		BigDecimal relRoa = Util.parseBigDecimal(0);
		if(Util.notEquals("", params.getString("relRoa")) 
				&& Util.isNumeric(NumConverter.delCommaString(params.getString("relRoa")))){
			relRoa = Util.parseBigDecimal(
					NumConverter.delCommaString(String.valueOf(params.getDouble("relRoa"))));
		} else {
			if(need){
				throw new CapMessageException((pop.getProperty("L140MM4A.relRoa")+
						pop.getProperty("notMatchFormat")), getClass());
			}
		}
//		boolean firstChange = (fstDate == lstDate) ? true : false;
//		boolean cstDate = (Util.nullToSpace(cst_Date) == "") ? false : true;
		boolean firstChange = (Util.equals(fstDate, lstDate)) ? true : false;
		boolean cstDate = (Util.equals(Util.nullToSpace(cst_Date),"")) ? false : true;

		String outcome = lmsService.checkIsLegal(adoptFg, ctlType, firstChange, cstDate, 
				rateAdd, custRoa, relRoa);
		result.set("isLegal", outcome);
		
		return result;
	}
	
	public boolean notDocumentNoFmt(String documentNo) {
		documentNo = Util.trim(documentNo);
		if(StringUtils.length(documentNo) != 12 &&
				StringUtils.length(documentNo) != 14 ){
			return true;
		} else {
			// 舊案號 12碼 099005LMS001	新案號14碼 105005LMS00001
			String caseYear = documentNo.substring(0, 3);
			String caseBrId = documentNo.substring(3, 6);
			String schema = documentNo.substring(6, 9);
//			String caseSeq = documentNo.substring(9);
			
			Calendar calendar = Calendar.getInstance();
			String todayYear = TWNDate.toTW(calendar.getTime()).substring(0, 3);
			if(Util.parseInt(caseYear) > Util.parseInt(todayYear)){
				return true;	//不會是未來日
			}
			List<IBranch> bank = branchService.getAllBranch();
			ArrayList<String> arr = new ArrayList<String>();
			for (IBranch b : bank) {
				arr.add(b.getBrNo());
			}
			if(!arr.contains(caseBrId)){
				return true;	//分行別要存在
			}
			if(Util.notEquals(UtilConstants.CaseSchema.個金, schema) &&
					Util.notEquals(UtilConstants.CaseSchema.企金, schema)){
				return true;	//一定是企個金
			}
			return false;
		}		
	}
}
