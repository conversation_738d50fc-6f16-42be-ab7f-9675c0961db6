/* 
 *  LMS1705ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.service.BstblService;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.F101M01ADao;
import com.mega.eloan.lms.dao.L170A01ADao;
import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.dao.L170M01BDao;
import com.mega.eloan.lms.dao.L170M01CDao;
import com.mega.eloan.lms.dao.L170M01DDao;
import com.mega.eloan.lms.dao.L170M01EDao;
import com.mega.eloan.lms.dao.L170M01FDao;
import com.mega.eloan.lms.dao.L170M01GDao;
import com.mega.eloan.lms.dao.L170M01IDao;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.dao.L180M01BDao;
import com.mega.eloan.lms.dao.L180M01CDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.Lms412Service;
import com.mega.eloan.lms.lrs.pages.LMS1705M01Page;
import com.mega.eloan.lms.lrs.panels.LMS1705S02Panel;
import com.mega.eloan.lms.lrs.report.impl.LMS1705R01RptServiceImpl;
import com.mega.eloan.lms.lrs.service.LMS1705Service;
import com.mega.eloan.lms.lrs.service.LMS1805Service;
import com.mega.eloan.lms.mfaloan.service.MisELFMOW1Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElCUS25Service;
import com.mega.eloan.lms.mfaloan.service.MisLMS338NService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.F101M01A;
import com.mega.eloan.lms.model.L170A01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01C;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L170M01G;
import com.mega.eloan.lms.model.L170M01I;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L180M01C;
import com.mega.eloan.lms.obsdb.service.ObsdbELFFORLRSService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS1705ServiceImpl extends AbstractCapService implements
		LMS1705Service {

	private static Logger logger = LoggerFactory
			.getLogger(LMS1705ServiceImpl.class);

	@Resource
	ObsdbELFFORLRSService obsdbELFFORLRSService;
	@Resource
	BranchService branchService;

	@Resource
	LMSService lmsService;

	@Resource
	TempDataService tempDataService;

	@Resource
	FlowService flowService;

	@Resource
	L180M01BDao l180m01bDao;

	@Resource
	L180M01ADao l180m01aDao;

	@Resource
	L180M01CDao l180m01cDao;

	@Resource
	L170A01ADao l170a01aDao;

	@Resource
	L170M01ADao l170m01aDao;

	@Resource
	L170M01BDao l170m01bDao;

	@Resource
	L170M01CDao l170m01cDao;

	@Resource
	L170M01DDao l170m01dDao;

	@Resource
	L170M01EDao l170m01eDao;

	@Resource
	L170M01FDao l170m01fDao;

	@Resource
	L170M01GDao l170m01gDao;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	LMS1805Service lms1805Service;

	@Resource
	MisdbBASEService misdbService;

	@Resource
	Lms412Service lms412Service;

	@Resource
	MisELFMOW1Service miselfmow1Service;

	@Resource
	MisLMS338NService mislms338nService;

	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	MisELLNGTEEService misEllngteeService;

	@Resource
	MisElCUS25Service misElcus25Service;

	@Resource
	BstblService bstblService;

	@Resource
	DocLogService docLogService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	private F101M01ADao f101m01aDao;

	@Resource
	RetrialService retrialService;

	@Resource
	L170M01IDao l170m01iDao;

	static final String commonDate = "0001-01-01";

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public <T extends GenericBean> T findModelByMainId(Class clazz,
			String mainId) {
		if (clazz == L170M01A.class) {
			return (T) l170m01aDao.findByMainId(mainId);
		} else if (clazz == L170M01C.class) {
			return (T) l170m01cDao.findByMainId(mainId);
		} else if (clazz == L170M01F.class) {
			return (T) l170m01fDao.findByMainId(mainId);
		} else if (clazz == L170M01I.class) {
			return (T) l170m01iDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public List<L170M01D> findL170m01dByMainId(String mainId) {
		return l170m01dDao.findByMainId(mainId);
	}

	@Override
	public List<L170M01E> findL170m01eByMainId(String mainId, String timeFlag) {
		return l170m01eDao.findByMainId(mainId,
				(Util.equals("", timeFlag) ? "T" : timeFlag));
	}

	@Override
	public List<L170M01G> findL170m01gByMainId(String mainId) {
		return l170m01gDao.findByMainId(mainId);
	}

	@Override
	public L170M01D findL170m01dByMainId(String mainId, String custId,
			String dupNo, String itemNo) {
		return l170m01dDao.findByIndex02(mainId, custId, dupNo, itemNo);
	}

	@Override
	public boolean deleteL170m01aList(String[] oids) {
		boolean flag = false;
		List<L170M01A> l170m01as = new ArrayList<L170M01A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			L170M01A l170m01a = (L170M01A) findModelByOid(L170M01A.class,
					oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			l170m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l170m01as.add(l170m01a);
			docLogService.record(l170m01a.getOid(), DocLogEnum.DELETE);

			List<L170M01B> l170m01bList = this.findL170m01bList(l170m01a
					.getMainId());
			if (!l170m01bList.isEmpty()) {
				l170m01bDao.delete(l170m01bList);
			}
			L170M01C l170m01c = this.findModelByMainId(L170M01C.class,
					l170m01a.getMainId());

			if (l170m01c != null) {
				l170m01cDao.delete(l170m01c);
			}
			List<L170M01D> l170m01dList = this.findL170m01dByMainId(l170m01a
					.getMainId());
			if (!l170m01dList.isEmpty()) {
				l170m01dDao.delete(l170m01dList);
			}
			List<L170M01E> l170m01eList = this.findL170m01eByMainId(
					l170m01a.getMainId(), "T");
			if (!l170m01eList.isEmpty()) {
				l170m01eDao.delete(l170m01eList);
			}
			// 前次
			List<L170M01E> exL170m01eList = this.findL170m01eByMainId(
					l170m01a.getMainId(), "L");
			if (!exL170m01eList.isEmpty()) {
				l170m01eDao.delete(exL170m01eList);
			}
			L170M01F l170m01f = this.findModelByMainId(L170M01F.class,
					l170m01a.getMainId());

			if (l170m01f != null) {
				l170m01fDao.delete(l170m01f);
			}

		}
		if (!l170m01as.isEmpty()) {
			l170m01aDao.save(l170m01as);
			flag = true;
		}
		return flag;

	}

	@Override
	public boolean deleteL170m01bListNotLnDataDate(String mainId) {
		return l170m01bDao.deleteL170m01bListNotLnDataDate(mainId);
	}

	@Override
	public void deleteL170m01dList(String mainId) {
		List<L170M01D> L170m01dList = l170m01dDao.findByMainId(mainId);
		l170m01dDao.delete(L170m01dList);
	}

	@Override
	public void deleteL170m01eList(String mainId) {
		l170m01eDao.deleteL170m01eList(mainId);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L170M01A.class) {
			return (T) l170m01aDao.find(oid);
		} else if (clazz == L170M01B.class) {
			return (T) l170m01bDao.find(oid);
		} else if (clazz == L170M01C.class) {
			return (T) l170m01cDao.find(oid);
		} else if (clazz == L170M01D.class) {
			return (T) l170m01dDao.find(oid);
		}
		return null;
	}

	@Override
	public void delete(Class<?> clazz, String oid) {
		if (clazz == L170M01A.class) {
			L170M01A l170m01a = l170m01aDao.findByOid(oid);
			l170m01aDao.delete(l170m01a);
		} else if (clazz == L170M01C.class) {
			L170M01C l170m01c = l170m01cDao.findByOid(oid);
			l170m01cDao.delete(l170m01c);
		}
	}

	@Override
	public void deleteByMainId(Class<?> clazz, String mainId) {
		if (clazz == L170M01C.class) {
			L170M01C l170m01c = findModelByMainId(L170M01C.class, mainId);
			l170m01cDao.delete(l170m01c);
		}
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L170M01A) {
					// ((L170M01A) model).setDeletedTime(null);
					((L170M01A) model).setUpdater(user.getUserId());
					((L170M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if ("".equals(Util.nullToSpace(((L170M01A) model)
							.getCreateTime()))) {
						((L170M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L170M01A) model)
							.getCreator()))) {
						((L170M01A) model).setCreator(user.getUserId());
					}
					l170m01aDao.save((L170M01A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L170M01A) model)
								.getMainId());
						// 記錄文件異動記錄
						docLogService.record(((L170M01A) model).getOid(),
								DocLogEnum.SAVE);
					}
				} else if (model instanceof L170M01B) {
					((L170M01B) model).setUpdater(user.getUserId());
					((L170M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if ("".equals(Util.nullToSpace(((L170M01B) model)
							.getCreateTime()))) {
						((L170M01B) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L170M01B) model)
							.getCreator()))) {
						((L170M01B) model).setCreator(user.getUserId());
					}
					l170m01bDao.save((L170M01B) model);

				} else if (model instanceof L170M01C) {
					((L170M01C) model).setUpdater(user.getUserId());
					((L170M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if ("".equals(Util.nullToSpace(((L170M01C) model)
							.getCreateTime()))) {
						((L170M01C) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L170M01C) model)
							.getCreator()))) {
						((L170M01C) model).setCreator(user.getUserId());
					}
					l170m01cDao.save((L170M01C) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L170M01C) model)
								.getMainId());
					}

				} else if (model instanceof L170M01D) {
					((L170M01D) model).setUpdater(user.getUserId());
					((L170M01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if ("".equals(Util.nullToSpace(((L170M01D) model)
							.getCreateTime()))) {
						((L170M01D) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L170M01D) model)
							.getCreator()))) {
						((L170M01D) model).setCreator(user.getUserId());
					}
					l170m01dDao.save((L170M01D) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L170M01D) model)
								.getMainId());
					}
				} else if (model instanceof L170M01E) {
					((L170M01E) model).setUpdater(user.getUserId());
					((L170M01E) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if ("".equals(Util.nullToSpace(((L170M01E) model)
							.getCreateTime()))) {
						((L170M01E) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L170M01E) model)
							.getCreator()))) {
						((L170M01E) model).setCreator(user.getUserId());
					}
					l170m01eDao.save((L170M01E) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L170M01E) model)
								.getMainId());
					}
				} else if (model instanceof L170M01F) {
					((L170M01F) model).setUpdater(user.getUserId());
					((L170M01F) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if ("".equals(Util.nullToSpace(((L170M01F) model)
							.getCreateTime()))) {
						((L170M01F) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L170M01F) model)
							.getCreator()))) {
						((L170M01F) model).setCreator(user.getUserId());
					}
					l170m01fDao.save((L170M01F) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L170M01F) model)
								.getMainId());
					}
				} else if (model instanceof L170M01G) {
					((L170M01G) model).setUpdater(user.getUserId());
					((L170M01G) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if ("".equals(Util.nullToSpace(((L170M01G) model)
							.getCreateTime()))) {
						((L170M01G) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L170M01G) model)
							.getCreator()))) {
						((L170M01G) model).setCreator(user.getUserId());
					}
					l170m01gDao.save((L170M01G) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L170M01G) model)
								.getMainId());
					}
				} else if (model instanceof L170A01A) {
					l170a01aDao.save((L170A01A) model);

				} else if (model instanceof L170M01I) {
					((L170M01I) model).setUpdater(user.getUserId());
					((L170M01I) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if ("".equals(Util.nullToSpace(((L170M01I) model)
							.getCreateTime()))) {
						((L170M01I) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L170M01I) model)
							.getCreator()))) {
						((L170M01I) model).setCreator(user.getUserId());
					}
					l170m01iDao.save((L170M01I) model);
				}
			}
		}
	}

	@Override
	public List<L170M01A> findL170m01aList(ISearch search) {
		return l170m01aDao.find(search);
	}

	@Override
	public List<L170M01B> findL170m01bList(String mainId) {
		return l170m01bDao.findByMainId(mainId);
	}

	@Override
	public void saveL170m01bList(List<L170M01B> list) {
		l170m01bDao.save(list);
	}

	@Override
	public void saveL170m01cList(List<L170M01C> list) {
		l170m01cDao.save(list);
	}

	@Override
	public void saveL170m01dList(List<L170M01D> list) {
		l170m01dDao.save(list);
	}

	@Override
	public void saveL170m01dList2(List<L170M01D> models) {

		if (models != null) {
			l170m01dDao.save(models);
		}

	}

	@Override
	public Map<String, Object> findMisByCustIdDupNoBranch(String custId,
			String dupNo, String branch) {

		Map<String, Object> dataMap = lms412Service
				.findELF412ByCustIdDupNoBranch(custId, dupNo, branch);

		Map<String, Object> dataCollection = null;

		if (dataMap != null) {
			dataCollection = new HashMap<String, Object>();
			dataCollection.put("custId", Util.trim(dataMap.get("CUSTID")));
			dataCollection.put("dupNo", Util.trim(dataMap.get("DUPNO")));
			dataCollection.put("maincust", Util.trim(dataMap.get("MAINCUST")));
			dataCollection.put("crdtype", Util.trim(dataMap.get("CRDTYPE")));
			dataCollection.put("crdttbl", Util.trim(dataMap.get("CRDTTBL")));
			dataCollection.put("mowtype", Util.trim(dataMap.get("MOWTYPE")));
			dataCollection.put("mowtbl1", Util.trim(dataMap.get("MOWTBL1")));
			dataCollection.put("llrdate", (Date) dataMap.get("LLRDATE"));
			dataCollection.put("lrdate", (Date) dataMap.get("LRDATE"));
			dataCollection.put("rckdline", Util.trim(dataMap.get("RCKDLINE")));
			dataCollection.put("ockdline", Util.trim(dataMap.get("OCKDLINE")));
			dataCollection.put("cstate", Util.trim(dataMap.get("CSTATE")));
			dataCollection.put("mdflag", Util.trim(dataMap.get("MDFLAG")));
			dataCollection.put("mddt", (Date) dataMap.get("MDDT"));
			dataCollection.put("process", Util.trim(dataMap.get("PROCESS")));
			dataCollection.put("newadd", Util.trim(dataMap.get("NEWADD")));
			dataCollection.put("newdate", Util.trim(dataMap.get("NEWDATE")));
			dataCollection.put("nckdflag", Util.trim(dataMap.get("NCKDFLAG")));
			dataCollection.put("nckddate", (Date) dataMap.get("NCKDDATE"));
			dataCollection.put("nckdmemo", Util.trim(dataMap.get("NCKDMEMO")));
			dataCollection.put("canceldt", (Date) dataMap.get("CANCELDT"));
			dataCollection.put("dbuobu", Util.trim(dataMap.get("DBUOBU")));
			dataCollection.put("upddate", (Date) dataMap.get("UPDDATE"));
			dataCollection.put("updater", Util.trim(dataMap.get("UPDATER")));
			dataCollection.put("memo", Util.trim(dataMap.get("MEMO")));
			dataCollection.put("uckdline", Util.trim(dataMap.get("UCKDLINE")));
			dataCollection.put("uckddt", (Date) dataMap.get("UCKDDT"));
			dataCollection.put("datadt", (Date) dataMap.get("DATADT"));
			dataCollection.put("nextnwdt", (Date) dataMap.get("NEXTNWDT"));
			dataCollection.put("nextltdt", (Date) dataMap.get("NEXTLTDT"));

			// J-108-0078_05097_B1001 Web e-Loan企金授信覆審系統修改首次往來之新授信戶應辦理覆審之期限
			dataCollection.put("isAllNew",
					Util.trim(MapUtils.getString(dataMap, "ISALLNEW", "")));

		}

		return dataCollection;

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L170M01A.class) {
			return l170m01aDao.findPage(search);
		} else if (clazz == L170M01B.class) {
			return l170m01bDao.findPage(search);
		} else if (clazz == L170M01C.class) {
			return l170m01cDao.findPage(search);
		} else if (clazz == L170M01D.class) {
			return l170m01dDao.findPage(search);
		} else if (clazz == L170M01E.class) {
			return l170m01eDao.findPage(search);
		}
		return null;
	}

	@Override
	public boolean updateElf412(L170M01A l170m01a, String mainId,
			String custId, String dupNo, String branch) {
		boolean updateSt = false;
		// 文件資訊先引入信用評等才找的到

		String creditType = "";
		String creditGrade = "";

		String mowType = "";
		String mowGrade = "";

		String fcrdType = "";
		String fcrdArea = "";
		String fcrdPred = "";
		String fcrdGrad = "";

		List<L170M01E> l170m01eList = l170m01eDao.findByUniqueKey6(mainId,
				custId, dupNo, "T");
		if (true) {
			// 會有多筆

			// 信用評等類型 (覆審報告表傳入)
			// String mowType = "";
			// String mowGrade = "";
			// String gradeType = "";
			// String grade = "";
			//
			// String fcrdType = "";
			// String fcrdArea = "";
			// String fcrdPred = "";
			// String fcrdGrad = "";

			for (L170M01E l170m01e : l170m01eList) {
				String crdType = Util.trim(l170m01e.getCrdType());
				String tempGrade = Util.trim(l170m01e.getGrade());
				// 取得信用平等
				if (UtilConstants.crdType.DBU大型企業.equals(crdType)) {
					creditType = "B";
					creditGrade = tempGrade;
				} else if (UtilConstants.crdType.DBU中小型企業.equals(crdType)) {
					creditType = "L";
					creditGrade = tempGrade;
				} else if (UtilConstants.crdType.海外.equals(crdType)) {
					creditType = "O";
					creditGrade = tempGrade;
				} else {
					mowType = crdType;
					mowGrade = tempGrade;
				}
				// 取得DBU大型企業.......模型平等
				// else if(crdType.startsWith("M")){
				// if(crdType.length() >= 2){
				// mowType = crdType.substring(1, 2);
				// }
				// mowGrade = tempGrade;
				// }
			}

			// if (true) {
			// Map<String, List<L170M01E>> m_l170m01e = retrialService
			// .findL170M01E_type(retrialService
			// .findL170M01E(l170m01a));
			// L170M01E l170m01e_C = LrsUtil.firstElm(
			// m_l170m01e.get(LrsUtil.M01E_FLAG_C), "T");
			// L170M01E l170m01e_M = LrsUtil.firstElm(
			// m_l170m01e.get(LrsUtil.M01E_FLAG_M), "T");
			// L170M01E l170m01e_F = LrsUtil.firstElm(
			// m_l170m01e.get(LrsUtil.M01E_FLAG_F), "T");
			// Integer fcrdScore = null;
			//
			// if (l170m01e_C != null) {
			// creditType = Util.trim(l170m01e_C.getCrdType());
			// creditGrade = Util.trim(l170m01e_C.getGrade());
			// }
			//
			// if (l170m01e_M != null) {
			// mowType = Util.trim(l170m01e_M.getCrdType());
			// mowGrade = Util.trim(l170m01e_M.getGrade());
			// }
			//
			// if (l170m01e_F != null) {
			// fcrdType = Util.trim(l170m01e_F.getCrdType());
			// fcrdArea = Util.trim(l170m01e_F.getFcrdArea());
			// fcrdPred = Util.trim(l170m01e_F.getFcrdPred());
			// fcrdGrad = Util.trim(l170m01e_F.getGrade());
			// fcrdScore = l170m01e_F.getScore();
			// }
			// // ======
			// if (Util.equals(UtilConstants.crdType.未評等, creditType)
			// || Util.isEmpty(creditType)) {
			// creditType = "";
			// creditGrade = "";
			// }
			// if (Util.equals(UtilConstants.Casedoc.CrdType2.免辦, mowType)
			// || Util.isEmpty(mowType)) {
			// mowType = "";
			// mowGrade = "";
			// }
			// if (Util.isEmpty(fcrdGrad)
			// || (fcrdScore != null && fcrdScore == 90)) {
			// fcrdType = "";
			// fcrdArea = "";
			// fcrdPred = "";
			// fcrdGrad = "";
			// }
			// // ======
			// // 把 2 碼的 creditType,mowType 轉成 1碼的格式，來上傳
			// if (Util.isNotEmpty(creditType)) {
			// creditType = LMSUtil.getDesc(
			// retrialService.get_lrs_CrdtType_2to1(), creditType);
			// }
			// if (Util.isNotEmpty(mowType)) {
			// mowType = LMSUtil.getDesc(
			// retrialService.get_lrs_MowType_2to1(), mowType);
			// }
			// }

			// (信用評等類型,信用評等)寫入ELF412
			boolean st = this.updateGrade(creditType, creditGrade, mowType,
					mowGrade, fcrdType, fcrdArea, fcrdPred, fcrdGrad, custId,
					dupNo, branch);

			if (st) {
				// ELF412撈此筆資料
				Map<String, Object> dataMap = this.findMisByCustIdDupNoBranch(
						custId, dupNo, branch);
				// Map<String,Object> dataMap =
				// lms412Service.findELF412ByCustIdDupNoBranch(custId,
				// dupNo, branch);
				if (dataMap != null) {
					// ELF412_MAINCUST 主要借款人 (覆審報告表主表傳入)
					String maincust = l170m01a.getMLoanPerson();
					if (!"Y".equals(maincust)) {
						maincust = "N";
					}

					// J-108-0078_05097_B1001
					// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。s
					String newadd = Util.trim(dataMap.get("newadd"));
					String newdate = Util.trim(dataMap.get("newdate"));
					String isAllNew = Util.trim(dataMap.get("isAllNew"));

					// ELF412_RCKDLINE 覆審週期
					String rckdline = Util.trim(dataMap.get("rckdline"));
					String uckdline = Util.trim(dataMap.get("uckdline"));
					Date uckddt = (Date) dataMap.get("uckddt");
					String uckddtStr = String.valueOf(uckddt);

					// ELF412_MDFLAG 異常通報代碼
					String mdflag = Util.trim(dataMap.get("mdflag"));
					// 用異常通報代碼判斷週期

					/**
					 * <pre>
					 * 執行 [更新ELF412 - 計算週期 (caculateElf412)]
					 * tELF412.ELF412_MOWTYPE = ELF412_MOWTYPE
					 * </pre>
					 */

					Date canceldt = (Date) dataMap.get("canceldt");
					String nckdflag = Util.trim(dataMap.get("nckdflag"));
					Date nckddate = (Date) dataMap.get("nckddate");
					Date nextltdt = (Date) dataMap.get("nextltdt");
					Date nextnwdt = (Date) dataMap.get("nextnwdt");
					Date lrdate = l170m01a.getRetrialDate();
					Date llrdate = l170m01a.getLastRetrialDate() == null ? (Date) dataMap
							.get("lrdate") : l170m01a.getLastRetrialDate();

					// 這時候的評等已經是剛剛上過後本次覆審報告表最新內容
					// String mowtbl1 = Util.trim(dataMap.get("mowtbl1"));
					String cstate = Util.trim(dataMap.get("cstate"));
					String ockdline = Util.trim(dataMap.get("ockdline"));
					// String crdttbl = Util.trim(dataMap.get("crdttbl"));
					String nckdmemo = Util.trim(dataMap.get("nckdmemo"));

					// J-108-0078_05097_B1001
					// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
					// D.異常戶已三個月覆審過- 爾後半年覆審一次。
					// F.會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。
					if ("C".equals(rckdline) || "I".equals(rckdline)) {
						if (Util.isNotEmpty(mdflag)) {
							if ("10".equals(mdflag)) {
								rckdline = "F";
							} else {
								rckdline = "D";
							}
						} else {
							rckdline = "A";
						}
					} else if ("E".equals(rckdline)) {
						rckdline = "D";
					} else if ("G".equals(rckdline)) {
						rckdline = "F";
					}

					// J-108-0078_05097_B1001
					// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。s
					// ELF412_NEWADD 新作/增額 清空
					// ELF412_NEWDATE 新作/增額資料日期 清空
					newadd = "";
					newdate = "";
					isAllNew = "";

					// 判斷 主管機關指定覆審案件 ELF412_UCKDLINE
					if (uckdline.isEmpty()) {
						uckdline = "N";
					}
					// 主管機關通知日期 ELF412_UCKDDT

					if ("0001-01-01".equals(uckddtStr)) {
						uckddt = null;
					}

					// a[0]canceldt 銷戶日
					// a[1]nckdflag 不覆審代碼 NCKDFLAG CHAR(02)
					// a[2] nckddate 不覆審日期
					// a[3]nextltdt 上次設定之下次恢復覆審日
					// a[4] nextnwdt 最新一次下次恢復覆審日
					// a[5]mdflag 異常通報代碼 CHAR(02)
					// a[6]rckdline 覆審週期 CHAR(02)
					// a[7] newadd 新作/增額 CHAR(01)
					// a[8]uckdline 主管機關指定覆審案件 CHAR(02)
					// a[9] mowtbl1 信用模型評等 CHAR(02)
					// a[10] mowType 信用模型評等類別 CHAR(01)
					// a[11] maincust 主要授信戶 CHAR(01)
					// a[12]cstate 戶況 CHAR(01)
					// a[13] ockdline 原始週期 CHAR(02)
					// a[14]crdttbl 資信評等 CHAR(02)
					// a[15] nckdmemo 不覆審備註 CHAR(202)

					// 計算週期 (caculateElf412)
					// Object[] a = lms1805Service.getRckLine(mdflag, rckdline,
					// newadd, uckdline, mowType, mowtbl1, crdttbl, maincust);

					// J-108-0078_05097_B1001
					// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
					// String[] rckLineResult = lms1805Service.getRckLine(
					// Util.trim(mdflag), Util.trim(rckdline),
					// Util.trim(newadd), Util.trim(uckdline),
					// Util.trim(mowType), Util.trim(mowGrade),
					// Util.trim(gradeType),
					// Util.trim(l170m01a.getMLoanPerson()), isAllNew,
					// branch);
					//
					// if ("Y".equals(rckLineResult[1])) {
					// rckdline = rckLineResult[0];
					// } else {
					// rckdline = "";
					// }

					// J-108-0078_05097_B1001
					// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。

					Object[] a = lms1805Service.caculateElf412(canceldt,
							nckdflag, nckddate, nextltdt, nextnwdt, mdflag,
							rckdline, newadd, uckdline, mowGrade, mowType,
							maincust, cstate, ockdline, creditGrade, nckdmemo,
							branch, isAllNew, fcrdType, fcrdArea, fcrdPred,
							fcrdGrad);

					// [最近一次複審日] l170m01a的最近一次複審日期是lms412的上次複審日期
					// Date lastRetrialDate = l170m01a.getRetrialDate();

					// 清空以下欄位
					canceldt = null;
					// 不覆審代碼
					a[1] = Util.trim(l170m01a.getNCkdFlag());
					// 不覆審日期
					nckddate = null;
					// a[2] = "0000-01-01";
					// 不覆審備註
					a[15] = ""; // nckdmemo = "";
					// 最新一次下次恢復覆審日
					nextnwdt = null;
					// a[4] = "0000-01-01";
					// 上次設定之下次恢復覆審日
					nextltdt = null;
					// a[3] = "0000-01-01";

					/**
					 * <pre>
					 * 執行[寫回ELF412]
					 * </pre>
					 */

					// a[0]canceldt 銷戶日
					// a[1]nckdflag 不覆審代碼 NCKDFLAG CHAR(02)
					// a[2] nckddate 不覆審日期
					// a[3]nextltdt 上次設定之下次恢復覆審日
					// a[4] nextnwdt 最新一次下次恢復覆審日
					// a[5]mdflag 異常通報代碼 CHAR(02)
					// a[6]rckdline 覆審週期 CHAR(02)
					// a[7] newadd 新作/增額 CHAR(01)
					// a[8]uckdline 主管機關指定覆審案件 CHAR(02)
					// a[9] mowtbl1 信用模型評等 CHAR(02)
					// a[10] mowType 信用模型評等類別 CHAR(01)
					// a[11] maincust 主要授信戶 CHAR(01)
					// a[12]cstate 戶況 CHAR(01)
					// a[13] ockdline 原始週期 CHAR(02)
					// a[14]crdttbl 資信評等 CHAR(02)
					// a[15] nckdmemo 不覆審備註 CHAR(202)
					lms412Service.updateLMS412All(null, a[1], null,
							(Date) a[3], (Date) a[4], a[5], a[8], a[6], a[7],
							Util.trim(l170m01a.getMLoanPerson()), null, uckddt,
							newdate, lrdate, llrdate, custId, dupNo, branch);
					// dataMap結束
				} else {
					// 當ELF412沒有此筆筆資料時
					cleanElf412(mainId, custId, dupNo, branch, creditType,
							creditGrade, mowType, mowGrade);

				}

			} else {
				// 當ELF412沒有此筆筆資料時
				cleanElf412(mainId, custId, dupNo, branch, creditType,
						creditGrade, mowType, mowGrade);

			}
			// 更新ELF412成功
			updateSt = true;

		}
		return updateSt;

	}

	private void cleanElf412(String mainId, String custId, String dupNo,
			String branch, String crdtype, String crdttbl, String mowType,
			String mowGrade) {

		// rckdline 覆審週期 CHAR(02)
		String rckdline = "A";
		// newadd 新作/增額 CHAR(01)
		String newadd = "";
		// 新作/增額資料日期
		String newdate = "";
		// uckdline 主管機關指定覆審案件 CHAR(02)
		String uckdline = "N";
		// 主管機關通知日期
		Date uckddt = null;

		L170M01A l170m01a = findModelByMainId(L170M01A.class, mainId);
		// MAINCUST 主要借款人 (L170M01A 覆審報告表主檔傳入)
		String maincust = l170m01a.getMLoanPerson();
		if (!"Y".equals(maincust)) {
			maincust = "N";
		}
		// 上次覆審日期 (L170M01A 覆審報告表主檔傳入)
		Date lastRetrialDate = l170m01a.getLastRetrialDate();
		// 覆審日期 (L170M01A 覆審報告表主檔傳入)
		// Date retrialDate = l170m01a.getRetrialDate();

		// J-108-0078_05097_B1001
		// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
		lms412Service.addLMS412ByMainTain(maincust, crdtype, crdttbl, mowType,
				mowGrade, lastRetrialDate, rckdline, "", null, "", newadd,
				newdate, "", null, "", "", CapDate.getCurrentTimestamp(), "",
				"", CapDate.getCurrentTimestamp(), uckdline, uckddt, null, "",
				"", "", branch, custId, dupNo);
	}

	/**
	 * (信用評等類型,信用評等)寫入ELF412
	 * 
	 * @param gradeType
	 * @param borrGrade
	 * @param mowType
	 * @param mowGrade
	 * @param fcrdType
	 * @param fcrdArea
	 * @param fcrdPred
	 * @param fcrdGrad
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	private boolean updateGrade(String gradeType, String borrGrade,
			String mowType, String mowGrade, String fcrdType, String fcrdArea,
			String fcrdPred, String fcrdGrad, String custId, String dupNo,
			String branch) {
		boolean st = false;
		int i = lms412Service.updateLMS412ByGrade(gradeType, borrGrade,
				mowType, mowGrade, fcrdType, fcrdArea, fcrdPred, fcrdGrad,
				custId, dupNo, branch);
		if (i == 1) {
			st = true;
		}
		return st;
	}

	@Override
	public List<Map<String, Object>> findElf338nByType(String custId,
			String dupNo, String ovUnitNo, String type1, String type2,
			String type3, String type4, String type5, String type6) {

		// 客戶統一編號+重複序號+評等單位+評等表類別+評等財報年度
		return mislms338nService.findLMS338NByCustId(custId, dupNo, ovUnitNo,
				type1, type2, type3, type4, type5, type6);
	}

	@Override
	public List<Map<String, Object>> findElfmow1ByType(String custId,
			String dupNo, String type) {

		// 客戶統一編號+重複序號+評等單位+評等表類別+評等財報年度
		if ("Y".equals(type)) {
			return miselfmow1Service.findelfmow1ByCustIdYMoney(custId, dupNo);
		} else {
			return miselfmow1Service.findelfmow1ByCustIdNMoney(custId, dupNo);
		}
	}

	private List<String> removeDuplicate(List<String> list) {
		HashSet<String> hashSet = new HashSet<String>(list);
		list.clear();
		list.addAll(hashSet);
		return list;
	}

	public Object[] getGuarantor(String custId, String dupNo, String mainId) {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1705M01Page.class);
		List<L170M01B> l170m01blis = this.findL170m01bList(mainId);
		List<String> cntrNoList = new LinkedList<String>();
		for (L170M01B l170m01b : l170m01blis) {
			cntrNoList.add(Util.trim(l170m01b.getCntrNo()));
		}
		String getResult = "X";
		Object[] temp = new Object[2];
		List<String> cnameGList = new LinkedList<String>();
		List<String> cnameCList = new LinkedList<String>();
		List<String> cnameIList = new LinkedList<String>();
		StringBuffer str = new StringBuffer();
		if (!cntrNoList.isEmpty()) {
			for (String cntrNo : cntrNoList) {
				// 保證人
				List<Map<String, Object>> dataMaprltGuarantor = this
						.findEllngteeByCustIdAndDupNo(custId, dupNo, cntrNo);
				for (Map<String, Object> map : dataMaprltGuarantor) {
					getResult = "Y";
					if ("G".equals(Util.trim(map.get("LNGEFLAG")))) {
						cnameGList.add(Util.trim(map.get("LNGENM")));
					} else if ("C".equals(Util.trim(map.get("LNGEFLAG")))) {
						cnameCList.add(Util.trim(map.get("LNGENM")));
					} else if ("I".equals(Util.trim(map.get("LNGEFLAG")))) {
						cnameIList.add(Util.trim(map.get("LNGENM")));
					}
				}
			}
		} else {
			getResult = "N";
		}
		cnameGList = this.removeDuplicate(cnameGList);
		cnameCList = this.removeDuplicate(cnameCList);
		cnameIList = this.removeDuplicate(cnameIList);
		int i = 0;
		for (String cname : cnameGList) {
			if (i == 0) {
				i++;
				str.append(prop.getProperty("L170M01A.guarantor01"));
			} else if (str.length() > 0) {
				str.append("、");
			}
			str.append(cname);
		}
		i = 0;
		for (String cname : cnameIList) {
			if (i == 0) {
				i++;
				str.append(prop.getProperty("L170M01A.guarantor03"));
			} else if (str.length() > 0) {
				str.append("、");
			}
			str.append(cname);
		}
		i = 0;
		for (String cname : cnameCList) {
			if (i == 0) {
				i++;
				str.append(prop.getProperty("L170M01A.guarantor02"));
			} else if (str.length() > 0) {
				str.append("、");
			}
			str.append(cname);
		}
		temp[0] = getResult;
		temp[1] = str.toString();
		return temp;
	}

	/**
	 * 跑flow
	 * 
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */
	public void flowAction(String oid, MegaSSOUserDetails user,
			HashMap<String, Object> data) {

		FlowInstance inst = flowService.createQuery().id(oid).query();
		if (inst == null) {
			throw new RuntimeException("FlowInstance is null : OID[" + oid
					+ "]");
		}

		for (String key : data.keySet()) {
			inst.setAttribute(key, data.get(key));
		}
		inst.next();
	}

	@Override
	public void startFlow(String oid, String flow_code) throws FlowException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		FlowInstance inst = flowService.createQuery().id(oid).query();

		if ("".equals(Util.nullToSpace(flow_code))) {
			flow_code = "LMS1705Flow";
		}
		if (inst == null) {
			logger.debug("流程啟動：流程代號[{}]OID[{}]", flow_code, oid);
			flowService.start(flow_code, oid, user.getUserId(),
					user.getUnitNo());
		} else {
			if (flow_code.equals(inst.getDefinition().getName())) {
				logger.warn("欲啟動之流程已存在：流程名稱[{}]OID[{}]", inst.getDefinition()
						.getName(), oid);
			} else {
				logger.error("尚未結束原流程：流程名稱[{}]OID[{}]", inst.getDefinition()
						.getName(), oid);
				throw new FlowException("FlowError: oid[" + oid + "]name["
						+ inst.getDefinition().getName() + "]");
			}
		}
	}

	@Override
	public void insertUpdate180M01B(String mainId, String custId, String dupNo,
			String branch, Date retrialDate, L170M01A l170m01a,
			L180M01A l180m01a) {

		String ctlType = Util.equals(Util.trim(l170m01a.getCtlType()), "") ? LrsUtil.CTLTYPE_主辦覆審
				: Util.trim(l170m01a.getCtlType());
		L180M01B l180m01b = l180m01bDao.findByUniqueKey(mainId, custId, dupNo,
				ctlType);
		if (l180m01b == null) {
			l180m01b = new L180M01B();
			l180m01b.setCreateBY("PEO");
			l180m01b.setMainId(mainId);
			l180m01b.setDupNo(dupNo);
			l180m01b.setCustId(custId);
			l180m01b.setCtlType(ctlType);
			l180m01b.setTypCd(l170m01a.getTypCd());
			l180m01b.setDocStatus1("1");
			l180m01b.setElfCName(l170m01a.getCustName());
			l180m01b.setECoNm(l170m01a.getTradeType());
			l180m01b.setSup3CNm(l170m01a.getChairman());
			l180m01b.setRltGuarantor(l170m01a.getRltGuarantor());
			l180m01b.setNewNCkdFlag("");
			l180m01b.setNewNCkdMemo("");
			// LMS412
			Map<String, Object> dataMap = lms412Service
					.findELF412ByCustIdDupNoBranch(custId, dupNo, branch);
			Date newNextNwDt = (Date) dataMap.get("nextnwdt");
			l180m01b.setNewNextNwDt(newNextNwDt);

			Date newLRDate = (Date) dataMap.get("nextltdt");
			l180m01b.setNewLRDate(newLRDate);

			l180m01b.setElfDataDt(null);

			String elfMainCust = l170m01a.getMLoanPerson();
			l180m01b.setElfMainCust(elfMainCust);

			String elfCrdType = Util.trim(dataMap.get("crdtype"));
			l180m01b.setElfCrdType(elfCrdType);

			String elfCrdTTbl = Util.trim(dataMap.get("crdttbl"));
			l180m01b.setElfCrdTTbl(elfCrdTTbl);

			String elfMowType = Util.trim(dataMap.get("mowtype"));
			l180m01b.setElfMowType(elfMowType);

			String elfMowTbl1 = Util.trim(dataMap.get("mowtbl1"));
			l180m01b.setElfMowTbl1(elfMowTbl1);

			Date elfLLRDate = (Date) dataMap.get("llrdate");
			l180m01b.setElfLLRDate(elfLLRDate);

			Date elfLRDate = (Date) dataMap.get("lrdate");
			l180m01b.setElfLRDate(elfLRDate);

			String elfRCkdLine = Util.trim(dataMap.get("rckdline"));
			l180m01b.setElfRCkdLine(elfRCkdLine);

			String elfUCkdLINE = Util.trim(dataMap.get("uckdline"));
			l180m01b.setElfUCkdLINE(elfUCkdLINE);

			Date elfUCkdDt = (Date) dataMap.get("uckddt");
			l180m01b.setElfUCkdDt(elfUCkdDt);

			String elfCState = Util.trim(dataMap.get("cstate"));
			l180m01b.setElfCState(elfCState);

			Date elfCancelDt = (Date) dataMap.get("canceldt");
			l180m01b.setElfCancelDt(elfCancelDt);

			String elfMDFlag = Util.trim(((String) dataMap.get("mdflag")));
			l180m01b.setElfMDFlag(elfMDFlag);

			Date elfMDDt = (Date) dataMap.get("mddt");
			l180m01b.setElfMDDt(elfMDDt);

			String elfProcess = Util.trim(((String) dataMap.get("process")));
			l180m01b.setElfProcess(elfProcess);

			String elfNewAdd = Util.trim(((String) dataMap.get("newadd")));
			l180m01b.setElfNewAdd(elfNewAdd);

			String elfNewDate = Util.trim(((String) dataMap.get("newdate")));
			l180m01b.setElfNewDate(elfNewDate);

			String elfNCkdFlag = "";
			l180m01b.setElfNCkdFlag(elfNCkdFlag);

			l180m01b.setElfNCkdDate(null);

			String elfNCkdMemo = "";
			l180m01b.setElfNCkdMemo(elfNCkdMemo);

			Date elfNextNwDt = (Date) dataMap.get("nextnwdt");
			l180m01b.setElfNextNwDt(elfNextNwDt);

			Date elfUpdDate = (Date) dataMap.get("upddate");
			l180m01b.setElfUpdDate(elfUpdDate);

			String elfUpdater = Util.trim(((String) dataMap.get("updater")));
			l180m01b.setElfUpdater(elfUpdater);

			String elfMemo = Util.trim(((String) dataMap.get("memo")));
			l180m01b.setElfMemo(elfMemo);

			Date elfTmeStamp = (Date) dataMap.get("tmestamp");
			l180m01b.setElfTmeStamp(elfTmeStamp);

			String creator = l170m01a.getCreator();
			l180m01b.setCreator(creator);

			Timestamp createTime = l170m01a.getCreateTime();
			l180m01b.setCreateTime(createTime);
		}
		l180m01b.setNewBy170M01("Y");

		String updater = l170m01a.getUpdater();
		l180m01b.setUpdater(updater);

		l180m01b.setUpdateTime(CapDate.getCurrentTimestamp());

		l180m01bDao.save(l180m01b);

		// 新增一筆至 L180M01C
		// insert180M01C(mainId, custId, dupNo, branch, mainIdMax);

		// 至Service1805 取得覆審案號
		// projectNumber = Service1805.l170M01Aproject(mainIdMax);

	}

	@Override
	public void insert180M01C(String mainId, String custId, String dupNo,
			String branch, String mainIdMax, String ctlType) {

		// 額度序號至L170M01B找
		List<L170M01B> list = this.findL170m01bList(mainId);
		List<L180M01C> l180m01cList = new LinkedList<L180M01C>();
		List<String> cntrnoList = new LinkedList<String>();

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		if (Util.equals(ctlType, "")) {
			ctlType = LrsUtil.CTLTYPE_主辦覆審;
		}

		for (L170M01B l170m01b : list) {
			String cntrno = l170m01b.getCntrNo();
			if (!cntrnoList.contains(cntrno)) {
				L180M01C l180m01c = new L180M01C();
				l180m01c.setMainId(mainIdMax);
				l180m01c.setCustId(custId);
				l180m01c.setDupNo(dupNo);
				l180m01c.setElfCntrType("");
				l180m01c.setElfCustCoId("");
				l180m01c.setElfCntrNo(cntrno);
				l180m01c.setCtlType(ctlType);
				l180m01cList.add(l180m01c);
				cntrnoList.add(cntrno);
			}

		}
		l180m01cDao.save(l180m01cList);
	}

	@Override
	public L170M01C findF101s01ABymainId(String mainId, String[] list,
			String custId, String dupNo, L170M01C l170m01c, L170M01A l170m01a) {

		// BranchRate branchRate =
		// lmsService.getBranchRate(Util.trim(l170m01a.getOwnBrId()));
		for (int i = 0; i < list.length; i++) {
			// 這是F101S01A的mainId
			String mainIdIn = list[i];

			// J-107-0045-001 Web e-Loan企金授信簽報書配合海外啟用IFRS徵信報告調整財報引進相關功能。
			F101M01A f101m01a = f101m01aDao.getF101M01AbyMainId(mainIdIn);

			// List<Map<String, Object>> row = eloandbBaseService
			// .CESF101S01ABymaiinId(mainIdIn);

			// J-107-0045-001 Web e-Loan企金授信簽報書配合海外啟用IFRS徵信報告調整財報引進相關功能。
			String[] _subNoArr = _subNoArr(f101m01a);
			List<Map<String, Object>> row = eloandbBaseService
					.CESF101S01AByMainIdSubNo(mainIdIn, _subNoArr[0],
							_subNoArr[1], _subNoArr[2]);

			for (Map<String, Object> dataMap : row) {
				Date sDate = (Date) dataMap.get("SDATE");
				Date eDate = (Date) dataMap.get("EDATE");

				String subNo = Util.trim(dataMap.get("SUBNO"));
				BigDecimal amt = LMSUtil.toBigDecimal(dataMap.get("AMT"));
				BigDecimal unit = LMSUtil.toBigDecimal(dataMap.get("AMTUNIT"));
				String curr = Util.nullToSpace(dataMap.get("CURR"));
				l170m01c.setCurr(curr);
				// if(unit == null){
				// unit = new BigDecimal(1);
				// }
				// if(l170m01c.getUnit() == null){
				// l170m01c.setUnit(new BigDecimal(1000));
				// }
				l170m01c.setUnit(unit);
				// if("".equals(Util.trim(l170m01c.getCurr()))){
				// l170m01c.setCurr("".equals(Util.trim(l170m01a.getTotBalCurr()))
				// ?
				// branchService.getBranch(Util.trim(l170m01a.getOwnBrId())).getUseSWFT()
				// : l170m01a.getTotBalCurr());
				// }
				// if(amt == null){
				// }else{
				// //換算單位後的價格
				// amt = amt.multiply((unit.divide(l170m01c.getUnit())));
				// // amt = branchRate.toOtherAmt(curr, l170m01c.getCurr(),
				// amt);
				// }

				// J-107-0045-001 Web e-Loan企金授信簽報書配合海外啟用IFRS徵信報告調整財報引進相關功能。
				if (i == 0) {
					l170m01c.setFromDate1(sDate);
					l170m01c.setEndDate1(eDate);

					// J-107-0045-001 Web e-Loan企金授信簽報書配合海外啟用IFRS徵信報告調整財報引進相關功能。
					if (_subNoArr[0].equals(subNo)) {
						// 營業收入
						l170m01c.setAmt11(amt);
					} else if (_subNoArr[1].equals(subNo)) {
						// 營業利益
						l170m01c.setAmt12(amt);
					} else if (_subNoArr[2].equals(subNo)) {
						// 稅前淨利(淨損)
						l170m01c.setAmt13(amt);
					}
					l170m01c.setRateDate1(eDate);
					// l170m01c.setRateDate1((Date) dataMap.get("APPROVETIME"));
				} else if (i == 1) {
					l170m01c.setFromDate2(sDate);
					l170m01c.setEndDate2(eDate);

					if (_subNoArr[0].equals(subNo)) {
						// 營業收入
						l170m01c.setAmt21(amt);
					} else if (_subNoArr[1].equals(subNo)) {
						l170m01c.setAmt22(amt);
					} else if (_subNoArr[2].equals(subNo)) {
						l170m01c.setAmt23(amt);
					}
					l170m01c.setRateDate2(eDate);
					// l170m01c.setRateDate2((Date) dataMap.get("APPROVETIME"));
				} else if (i == 2) {
					l170m01c.setFromDate3(sDate);
					l170m01c.setEndDate3(eDate);
					if (_subNoArr[0].equals(subNo)) {
						// 營業收入
						l170m01c.setAmt31(amt);
					} else if (_subNoArr[1].equals(subNo)) {
						l170m01c.setAmt32(amt);
					} else if (_subNoArr[2].equals(subNo)) {
						l170m01c.setAmt33(amt);
					}
					l170m01c.setRateDate3(eDate);
					// l170m01c.setRateDate3((Date) dataMap.get("APPROVETIME"));
				}
			}
			// this.saveL170m01cList(l170m01clist);
		}

		return l170m01c;

	}

	@Override
	public L170M01C findF101S01BBymainId(String mainId, String[] list,
			String custId, String dupNo, String[] ratioNolist, L170M01C l170m01c) {
		l170m01c.setRate11(null);
		l170m01c.setRate12(null);
		l170m01c.setRate13(null);
		l170m01c.setRate14(null);
		l170m01c.setRate21(null);
		l170m01c.setRate22(null);
		l170m01c.setRate23(null);
		l170m01c.setRate24(null);
		l170m01c.setRate31(null);
		l170m01c.setRate32(null);
		l170m01c.setRate33(null);
		l170m01c.setRate34(null);
		l170m01c.setRatioNo1(null);
		l170m01c.setRatioNo2(null);
		l170m01c.setRatioNo3(null);
		l170m01c.setRatioNo4(null);
		for (int i = 0; i < list.length; i++) {
			String mainIdIn = list[i];

			for (int j = 0; j < ratioNolist.length; j++) {
				String no = ratioNolist[j];
				switch (j) {
				case 0:
					// l170m01c.setRatioNo1(Util.isEmpty(no) ?
					// UtilConstants.ratioNo.負債比率
					// : no);
					l170m01c.setRatioNo1(Util.isEmpty(no) ? null : no);
					break;
				case 1:
					// l170m01c.setRatioNo2(Util.isEmpty(no) ?
					// UtilConstants.ratioNo.流動比率
					// : no);
					l170m01c.setRatioNo2(Util.isEmpty(no) ? null : no);
					break;
				case 2:
					// l170m01c.setRatioNo3(Util.isEmpty(no) ?
					// UtilConstants.ratioNo.速動比率
					// : no);
					l170m01c.setRatioNo3(Util.isEmpty(no) ? null : no);
					break;
				case 3:
					// l170m01c.setRatioNo4(Util.isEmpty(no) ?
					// UtilConstants.ratioNo.固定長期適合率
					// : no);
					l170m01c.setRatioNo4(Util.isEmpty(no) ? null : no);
					break;

				}
			}

			l170m01c = this.upL170m01c(ratioNolist, l170m01c, mainIdIn, i);
		}
		return l170m01c;
	}

	private L170M01C upL170m01c(String[] ratioNolist, L170M01C l170m01c,
			String mainId, int i) {

		for (int j = 0; j < ratioNolist.length; j++) {

			String ratioNo = ratioNolist[j];
			// J-107-0045-001 Web e-Loan企金授信簽報書配合海外啟用IFRS徵信報告調整財報引進相關功能。
			// F101M01A f101m01a = f101m01aDao.getF101M01AbyMainId(mainId);
			// if (f101m01a != null) {
			// String gaapFlag = Util.trim(f101m01a.getGaapFlag());
			// String tradeType = Util.trim(f101m01a.getTradeType());
			//
			// // IFRS / GAAP 財務比率
			// // GAAP 的 r36(財務費用率) = IFRS 的 r38(財務成本率)
			// if (Util.equals("0", gaapFlag) || Util.equals("", gaapFlag)) {//
			// GAAP
			//
			// } else {
			// // IFRS 沒有r36，改成以r38(財務成本率) 替代
			// if (Util.equals("36", ratioNo)) {
			// ratioNo = "38";
			// }
			// }
			//
			// }

			Map<String, Object> dataMap = eloandbBaseService
					.CESF101S01BBymainIdAndratioNo(mainId, ratioNo);

			if (dataMap != null) {
				String ratioNoData = Util.trim(dataMap.get("RATIONO"));
				BigDecimal ratio = LMSUtil.toBigDecimal(dataMap.get("RATIO"));
				if (i == 0) {
					if (j == 0) {
						l170m01c.setRate11(ratio);
						l170m01c.setRatioNo1(ratioNoData);
					} else if (j == 1) {
						l170m01c.setRate12(ratio);
						l170m01c.setRatioNo2(ratioNoData);
					} else if (j == 2) {
						l170m01c.setRate13(ratio);
						l170m01c.setRatioNo3(ratioNoData);
					} else {
						l170m01c.setRate14(ratio);
						l170m01c.setRatioNo4(ratioNoData);
					}
				} else if (i == 1) {
					if (j == 0) {
						l170m01c.setRate21(ratio);
						l170m01c.setRatioNo1(ratioNoData);
					} else if (j == 1) {
						l170m01c.setRate22(ratio);
						l170m01c.setRatioNo2(ratioNoData);
					} else if (j == 2) {
						l170m01c.setRate23(ratio);
						l170m01c.setRatioNo3(ratioNoData);
					} else {
						l170m01c.setRate24(ratio);
						l170m01c.setRatioNo4(ratioNoData);
					}
				} else if (i == 2) {
					if (j == 0) {
						l170m01c.setRate31(ratio);
						l170m01c.setRatioNo1(ratioNoData);
					} else if (j == 1) {
						l170m01c.setRate32(ratio);
						l170m01c.setRatioNo2(ratioNoData);
					} else if (j == 2) {
						l170m01c.setRate33(ratio);
						l170m01c.setRatioNo3(ratioNoData);
					} else {
						l170m01c.setRate34(ratio);
						l170m01c.setRatioNo4(ratioNoData);
					}

				}

			}// if (dataMap != null)
		}// j
		return l170m01c;

	}

	// //J-107-0045-001 Web e-Loan企金授信簽報書配合海外啟用IFRS徵信報告調整財報引進相關功能。
	@Override
	public List<Map<String, Object>> getCesf101(String brNo, String custId,
			String dupNo) {
		List<Map<String, Object>> rows = eloandbBaseService
				.findCESF101A01AJoinCESF101M01A(custId, dupNo, brNo);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Map<String, String> periodTypeMap = codeTypeService
				.findByCodeType("PeriodType");
		Map<String, String> consoGAAP = codeTypeService
				.findByCodeType("Common_YesNo10");
		Map<String, String> consoIFRS = codeTypeService
				.findByCodeType("IFRSConso");
		Map<String, String> tradeTypeGAAP = codeTypeService
				.findByCodeType("FssTradeType");
		Map<String, String> tradeTypeIFRS = codeTypeService
				.findByCodeType("IFRSTradeType");
		Map<String, String> amtUnitMap = codeTypeService
				.findByCodeType("CurrUnit");
		Map<String, String> gaapFlagMap = new HashMap<String, String>();
		if (true) {
			gaapFlagMap.put("0", "GAAP");
			gaapFlagMap.put("1", "IFRS");
			// J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
			gaapFlagMap.put("2", "EAS");
		}
		for (Map<String, Object> dataMap : rows) {

			Map<String, Object> data = new HashMap<String, Object>();
			data.put("mainId", Util.trim(dataMap.get("MAINID")));
			data.put("year", Util.trim(dataMap.get("YEAR")));
			data.put("sDate", (Date) dataMap.get("SDATE"));
			data.put("eDate", (Date) dataMap.get("EDATE"));
			data.put("curr", Util.trim(dataMap.get("CURR")));

			String gaapFlag = Util.trim(dataMap.get("GAAPFLAG"));
			String conso = Util.trim(dataMap.get("CONSO"));
			String tradeType = Util.trim(dataMap.get("TRADETYPE"));

			if (Util.equals("0", gaapFlag)) {// GAAP
				conso = LMSUtil.getDesc(consoGAAP, conso);
				tradeType = LMSUtil.getDesc(tradeTypeGAAP, tradeType);
			} else if (Util.equals("1", gaapFlag)) {// IFRS
				conso = LMSUtil.getDesc(consoIFRS, conso);
				tradeType = LMSUtil.getDesc(tradeTypeIFRS, tradeType);
			} else if (Util.equals("2", gaapFlag)) {// EAS
				// J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
				conso = LMSUtil.getDesc(consoIFRS, conso);
				tradeType = LMSUtil.getDesc(tradeTypeIFRS, tradeType);
			}
			data.put("gaapFlag", LMSUtil.getDesc(gaapFlagMap, gaapFlag));
			data.put("conso", Util.trim(conso));
			data.put("tradeType", Util.trim(tradeType));

			data.put("approveTime",
					TWNDate.toAD((Date) dataMap.get("APPROVETIME")));
			String periodType = Util.trim(dataMap.get("PERIODTYPE"));
			if (true) {
				if (Util.equals("9", periodType)) {// 其他
					String othType = Util.trim(dataMap.get("OTHTYPE"));
					if (Util.isNotEmpty(othType)) {
						periodType = othType;
					} else {
						periodType = LMSUtil.getDesc(periodTypeMap, periodType);
					}
				} else {
					periodType = LMSUtil.getDesc(periodTypeMap, periodType);
				}
			}
			data.put("periodType", periodType);
			data.put(
					"amtUnit",
					LMSUtil.getDesc(amtUnitMap,
							Util.trim(dataMap.get("AMTUNIT"))));

			list.add(data);
		}

		return list;
	}

	@SuppressWarnings({ "rawtypes" })
	@Override
	public Map<String, Object> finLms412ByCustIdAndDupNo(String custId,
			String dupNo, String brNo) {
		Map dataMap = this.lms412Service.findELF412ByCustIdDupNoBranch(custId,
				dupNo, brNo);
		Map<String, Object> dadaCollection = new HashMap<String, Object>();
		if (dataMap != null) {
			// LMS412覆審控制檔
			String maincust = Util.trim((String) dataMap.get("MAINCUST"));
			dadaCollection.put("maincust", maincust);
		}
		return dadaCollection;
	}

	@Override
	public List<Map<String, Object>> findEllngteeByCustIdAndDupNo(
			String custId, String dupNo, String cntrNo) {
		return misEllngteeService.getByCustIdCntrNo(custId, dupNo, cntrNo);

	}

	@Override
	public Map<String, Object> findCharminByCustIdDupNoBranch(String custId,
			String dupNo, String brNo) {

		Map<String, Object> dadaCollection = new HashMap<String, Object>();

		/**
		 * <pre>
		 * STEP1:SELECT T1.CNAME,T1.ENAME, T1.BUSCD FROM MIS.CUSTDATA WHERE
		 *       CUSTID = '12345'AND DUPNO = '1' AND BRNO='002'
		 * STEP2:SELECT T3.CUSTID, T3.DUPNO ,T3.SUP1CNM, T3.SUP3CNM FROM
		 *       MIS.ELCUS25 T3 WHERE CUSTID=? AND DUPNO=?
		 * 
		 * </pre>
		 */

		Map<String, Object> dataMapMiselcus25 = misElcus25Service
				.getMiselcus25(custId, dupNo);

		if (dataMapMiselcus25 != null) {

			// From MIS.ELCUS25
			String sup1cnm = Util.trim((String) dataMapMiselcus25
					.get("SUP1CNM"));
			// String sup3cnm = Util.trim((String) dataMapMiselcus25
			// .get("SUP3CNM"));

			// From MIS.CUSTDATA
			// String cname = Util.trim((String)
			// dataMapMiselcus25.get("CNAME"));
			// String ename = Util.trim((String)
			// dataMapMiselcus25.get("ENAME"));
			String buscd = Util.trim((String) dataMapMiselcus25.get("BUSCD"));
			// J-111-0326 海外覆審作業系統改良第一階段： 10. 加行業別代碼
			dadaCollection.put("busCd", buscd);
			String bussKind = Util.trim((String) dataMapMiselcus25
					.get("BUSSKIND"));
			dadaCollection.put("bussKind", bussKind);
			// 中文戶名
			// dadaCollection.put("cname", cname);
			// 英文戶名
			// dadaCollection.put("ename", ename);
			// 行業對象別
			// dadaCollection.put("buscd", buscd);

			// 董事長中文姓名
			dadaCollection.put("sup1cnm", sup1cnm);
			// 業務負責人中文姓名
			// dadaCollection.put("sup3cnm", sup3cnm);

			/**
			 * <pre>
			 * step3:select T2.ECONM from MIS.BSTBL T2 where T2.ECOCD = T1.BUSCD
			 * </pre>
			 */

			// Bstbl dataMapBstbl = bstblService.findByEcocd(buscd);
			// String ecocd = dataMapBstbl.getEcocd();

			// dadaCollection.put("ecocd", buscd);
			dadaCollection.put(
					"tradeType",
					Util.isEmpty(Util.nullToSpace(bstblService.findByEcocd(Util
							.trim(buscd)))) ? "" : Util
							.nullToSpace(bstblService.findByEcocd(
									Util.trim(buscd)).getEconm()));

		}

		return dadaCollection;
	}

	@Override
	public Page<Map<String, Object>> getBorrows(String mainId, ISearch search)
			throws CapException {
		Properties rptProperties = MessageBundleScriptCreator
				.getComponentResource(LMS1705R01RptServiceImpl.class);
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		L170M01A l170m01a = this.findModelByMainId(L170M01A.class, mainId);
		List<L170M01B> l170m01bList = this.findL170m01bList(mainId);

		data = new HashMap<String, Object>();
		data.put(
				"custName",
				Util.nullToSpace(l170m01a.getCustName()) + " "
						+ Util.nullToSpace(l170m01a.getCustId()) + " "
						+ Util.nullToSpace(l170m01a.getDupNo()));
		data.put("rptName", rptProperties.getProperty("TITLE.RPTNAME1"));
		data.put("rptNo", "LMS1705R01");
		data.put("rpt", "R01");
		data.put("oid", Util.nullToSpace(l170m01a.getOid()));
		beanList.add(data);

		if (l170m01bList.size() > 1) {
			data = new HashMap<String, Object>();
			data.put(
					"custName",
					Util.nullToSpace(l170m01a.getCustName()) + " "
							+ Util.nullToSpace(l170m01a.getCustId()) + " "
							+ Util.nullToSpace(l170m01a.getDupNo()));
			data.put("rptName", rptProperties.getProperty("TITLE.RPTNAME2"));
			data.put("rptNo", "LMS1705R02");
			data.put("rpt", "R02");
			data.put("oid", Util.nullToSpace(l170m01a.getOid()));
			beanList.add(data);
		}

		return new Page<Map<String, Object>>(beanList, beanList.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public List<L180M01A> findL180m01a(String branch, Date retrialDate) {
		List<L180M01A> l180m01a = l180m01aDao.findMaxDataDate(branch,
				retrialDate);
		return l180m01a;
	}

	@Override
	public boolean findL170M01AByUnkey(String custId, String dupNo,
			String branch, String docStatus, String ctlType) {
		boolean fg = false;
		L170M01A l170m01a = l170m01aDao.findByUniqueKey3(custId, dupNo, branch,
				docStatus, ctlType);
		if (l170m01a != null) {
			if (l170m01a.getRetrialDate() == new Date()) {
				fg = false;
			} else {
				fg = true;
			}
			// Date deletedTime = l170m01a.getDeletedTime();
			// if (deletedTime == null) {
			// fg = false;
			// } else {
			// fg = true;
			// }

		} else {
			fg = true;
		}
		return fg;
	}

	@Override
	public List<L170M01E> findL170m01eByCrdType(String mainId, String custId,
			String dupNo, int i, String timeFlag) {
		List<L170M01E> l170m01elist = null;
		timeFlag = (Util.equals("", timeFlag) ? "T" : timeFlag);
		switch (i) {
		case 1:
			l170m01elist = l170m01eDao.findByUniqueKey2(mainId, custId, dupNo,
					timeFlag);
			break;
		case 2:
			l170m01elist = l170m01eDao.findByUniqueKey5(mainId, custId, dupNo,
					timeFlag);
			break;
		case 3:
			l170m01elist = l170m01eDao.findByUniqueKey4(mainId, custId, dupNo,
					timeFlag);
			break;
		case 4:
			l170m01elist = l170m01eDao.findByUniqueKey(mainId, custId, dupNo,
					timeFlag);
			break;
		}
		return l170m01elist;
	}

	@Override
	public L170M01G findL170m01gByBranchTypeStaffJob(String mainId,
			String branchType, String staffJob) {
		return l170m01gDao.findByBranchTypeStaffJob(mainId, branchType,
				staffJob);
	}

	@Override
	public boolean findL170m01eFindCrdType(String mainId, String custId,
			String dupNo, String timeFlag) {

		boolean st = false;
		timeFlag = (Util.equals("", timeFlag) ? "T" : timeFlag);
		List<L170M01E> l170m01eList = l170m01eDao.findByUniqueKey(mainId,
				custId, dupNo, timeFlag);
		if (l170m01eList.size() > 0) {
			// 檢查信用評等是否有資料
			String crdTyp = l170m01eList.get(0).getCrdType();
			if (!crdTyp.isEmpty()) {
				st = true;
			}
		}
		return st;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public int checkDataBeforUpdate412(String mainId, String custId,
			String dupNo) {

		int i = 0;
		// 信用評等
		boolean st1 = false;
		// 授信資料
		boolean st2 = false;

		// (1)檢查信用評等是否有資料才可以更新覆審控制檔,
		st1 = this.findL170m01eFindCrdType(mainId, custId, dupNo, "T");

		// (2)檢查L170M01B是否有授信資料才可以更新覆審控制檔
		List list = this.findL170m01bList(mainId);

		if (!list.isEmpty()) {
			st2 = true;
		}

		if (st1 && st2) {
			// 信用評等和授信資料皆有資料
			i = 0;
		} else if (st1 && !st2) {
			// 信用評等有,授信資料無資料
			i = 1;
		} else if (!st1 && st2) {
			// 信用評等無,授信資料有資料
			i = 2;
		} else {
			// 信用評等和授信資料皆無資料
			i = 3;
		}
		return i;
	}

	@Override
	public String findCrtdttblByCrdtype(String custId, String dupNo,
			String crdtype, String brNo) {
		String grade = "";
		Map<?, ?> dataMap = mislms338nService.findGradeByCrdtype(custId, dupNo,
				crdtype, brNo);
		if (dataMap != null) {
			grade = Util.trim(dataMap.get("ELF338N_GRADE"));
		}
		return grade;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public List<Map<String, Object>> findcrdTypeByCustId(String custId,
			String dupNo, String brNo) {
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		List<?> list = mislms338nService.findCrdtypeByCustId(custId, dupNo,
				brNo);
		if (list.size() > 0) {
			Iterator it = list.iterator();
			while (it.hasNext()) {
				Map dataMap = (Map) it.next();
				dataList.add(dataMap);
			}
		}
		return dataList;
	}

	@Override
	public L170M01A findModelByDocStatus(String custId, String dupNo,
			String ownBrId, String docStatus) {
		L170M01A l170m01a = new L170M01A();
		// List<Object[]> list = l170m01aDao.findByUniqueKey3(custId, dupNo,
		// ownBrId, docStatus);
		// for (Object[] meta : list) {
		// // 已覆核-上次覆審日
		// l170m01a.setLastRetrialDate((Date) meta[21]);
		// }

		return l170m01a;
	}

	@Override
	public Page<Map<String, Object>> findL170m01AJoin(String docStatus,
			String brNo, String retrialDate1, String retrialDate2,
			String custId, String custName, ISearch search) {
		List<Map<String, Object>> list2 = eloandbBaseService.findL170M10AJoin(
				docStatus, brNo, retrialDate1, retrialDate2, custId, custName,
				search);
		return LMSUtil.getMapGirdDataRow(list2, search);
	}

	@Override
	public Map<String, Object> getCMSData(L170M01A l170m01a, String cntrNo) {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1705S02Panel.class);
		Map<String, Object> returnMap = new LinkedHashMap<String, Object>();
		String branchId = Util.trim(l170m01a.getOwnBrId());
		String custId = Util.trim(l170m01a.getCustId());
		String dupNo = Util.trim(l170m01a.getDupNo());
		Map<String, Set<String>> reCallMap = new LinkedHashMap<String, Set<String>>();
		StringBuffer str = new StringBuffer();
		String curr = branchService.getBranch(Util.trim(l170m01a.getOwnBrId()))
				.getUseSWFT();
		String usdCurr = "USD";
		// 匯率轉換處理
		BranchRate branchRate = lmsService.getBranchRate(branchId);
		List<BigDecimal> appAmtList = new LinkedList<BigDecimal>();
		List<BigDecimal> loanAmtList = new LinkedList<BigDecimal>();
		List<BigDecimal> rgAmtList = new LinkedList<BigDecimal>();
		List<BigDecimal> rsgAmtList = new LinkedList<BigDecimal>();
		String rgstCur = null;
		BigDecimal rgstAmt = null;
		BigDecimal gLoanRT = BigDecimal.ZERO;
		BigDecimal thr = new BigDecimal(1000);
		List<Map<String, Object>> list = null;
		List<Map<String, Object>> elf347List = null;
		StringBuffer gCollIns2 = new StringBuffer();
		StringBuffer tmpcollins = new StringBuffer();
		list = obsdbELFFORLRSService.listAMTDataByCustIdDupNoBranchCntrNo(
				branchId, cntrNo);
		Map<String, String> insTypeMap = codetypeService
				.findByCodeType("insType");
		if (insTypeMap == null) {
			insTypeMap = new LinkedHashMap<String, String>();
		}
		if (list.size() > 0) {
			// 92/04/04 李松明提:若擔保品大類為06(額度本票/備償票據),則不顯示名稱,亦不累加其估值及押值
			for (Map<String, Object> map : list) {
				String gCollno = Util.trim(map.get("COLLNO"));// 以擔保品編號(aa-bb-ccc)串出擔保品名稱
				String dbTYPCD = Util.trim(map.get("TYPCD"));
				String dbCUSTID = Util.trim(map.get("CUSTID"));
				String dbDUPNO = Util.trim(map.get("DUPNO"));
				String dbBRANCH = Util.trim(map.get("BRANCH"));
				String collType1 = gCollno.length() >= 2 ? gCollno.substring(0,
						2) : "";// 擔保品大類
				String collType2 = gCollno.length() >= 5 ? gCollno.substring(3,
						5) : "";// 擔保品小類
				if (!"06".equals(collType1)) {
					reCallMap = this.getCollDetailData(collType1, collType2,
							dbBRANCH, dbCUSTID, dbDUPNO, gCollno, reCallMap);
					// Map<String,Map<String,Object>> mapIn =
					// this.gfnGetColl_Name_CTL(dbTYPCD , dbBRANCH , dbCUSTID ,
					// dbDUPNO, gCollno , "N");
					// if(!){
					// returnMap.put("EXCEPTION", "取得借款人"+dbCUSTID+dbDUPNO+"  "+
					// gCollno+" 之擔保品明細資料錯誤");
					// return returnMap;
					// // throw new
					// RuntimeException("取得借款人"+dbCUSTID+dbDUPNO+"  "+
					// gCollno+" 之擔保品明細資料錯誤");
					// }
					// 堪估值 & 押值 轉回本位幣後塞到L170M01B
					// [J-94-0209] 信保估值和押質欄位調換 begin
					// String rgstcur = null;
					// 記得尚未轉換本位幣 TODO
					if ("05".equals(collType1) && "03".equals(collType2)) {
						if (Util.isNotEmpty(Util.trim(map.get("CURR")))) {
							appAmtList.add(branchRate.toOtherAmt(Util.trim(map
									.get("CURR")), curr, LMSUtil
									.nullToZeroBigDecimal(map.get("APPAMT"))));
							loanAmtList.add(branchRate.toOtherAmt(Util.trim(map
									.get("CURR")), curr, LMSUtil
									.nullToZeroBigDecimal(map.get("LOANAMT"))));
							// 1001013郭慧珠來電要求調整部份- - - - 設定金額
							rgAmtList.add(branchRate.toOtherAmt(Util.trim(map
									.get("CURR")), curr, LMSUtil
									.nullToZeroBigDecimal(map.get("LOANAMT"))));
						}
					} else if ("07".equals(collType1)) {
						if ("5".equals(dbTYPCD)) {
							if (Util.isNotEmpty(Util.trim(map.get("CURR")))) {
								appAmtList.add(branchRate.toOtherAmt(Util
										.trim(map.get("CURR")), curr,
										LMSUtil.nullToZeroBigDecimal(map
												.get("APPAMT"))));
								loanAmtList.add(branchRate.toOtherAmt(Util
										.trim(map.get("CURR")), curr, LMSUtil
										.nullToZeroBigDecimal(map
												.get("LOANAMT"))));
							}
						} else {
							appAmtList.add(branchRate.toOtherAmt(usdCurr, curr,
									LMSUtil.nullToZeroBigDecimal(map
											.get("LOANUSD"))));
							loanAmtList.add(branchRate.toOtherAmt(usdCurr,
									curr, LMSUtil.nullToZeroBigDecimal(map
											.get("LOANUSD"))));
						}
						// 1001013郭慧珠來電要求調整部份- - - - 設定金額
						if (Util.isNotEmpty(Util.trim(map.get("CURR")))) {
							rgAmtList.add(branchRate.toOtherAmt(Util.trim(map
									.get("CURR")), curr, LMSUtil
									.nullToZeroBigDecimal(map.get("LOANAMT"))));
						}
					} else {
						if (Util.isNotEmpty(Util.trim(map.get("CURR")))) {
							appAmtList.add(branchRate.toOtherAmt(Util.trim(map
									.get("CURR")), curr, LMSUtil
									.nullToZeroBigDecimal(map.get("APPAMT"))));
							loanAmtList.add(branchRate.toOtherAmt(Util.trim(map
									.get("CURR")), curr, LMSUtil
									.nullToZeroBigDecimal(map.get("LOANAMT"))));
						}

						// 1001013郭慧珠來電要求調整部份- - - - 設定金額
						if ("01".equals(collType1) || "02".equals(collType1)
								|| "08".equals(collType1)) {
							// 有設定金額
							if (Util.isNotEmpty(map.get("RGSTCUR"))) {
								rgAmtList.add(branchRate.toOtherAmt(Util
										.trim(map.get("RGSTCUR")), curr,
										LMSUtil.nullToZeroBigDecimal(map
												.get("RGSTAMT"))));
							}
						} else {
							if (Util.isNotEmpty(Util.trim(map.get("CURR")))) {
								rgAmtList.add(branchRate.toOtherAmt(Util
										.trim(map.get("CURR")), curr, LMSUtil
										.nullToZeroBigDecimal(map
												.get("LOANAMT"))));
							}

						}
					}
					// [J-94-0209] 信保估值和押質欄位調換 end End If
					// [J-95-0190] 覆審報告加入擔保品保險資料
					gLoanRT = LMSUtil.nullToZeroBigDecimal(map.get("LOANRT")); // 貸放成數

					// rgstcur = Util.trim(l170m01a.getTotBalCurr());// 放款幣別
					if ("07".equals(collType1)) {
						if ("5".equals(dbTYPCD)) {
							// RGSTCUR=Cstr(result.GetValue("CURR")) '放款幣別
							if (Util.isNotEmpty(map.get("RGSTCUR"))) {
								rgstCur = Util.trim(map.get("RGSTCUR"));
								rgstAmt = LMSUtil.nullToZeroBigDecimal(map
										.get("RGSTAMT"));
								rsgAmtList
										.add(branchRate.toOtherAmt(
												Util.trim(map.get("RGSTCUR")),
												curr,
												Arithmetic.div(
														LMSUtil.nullToZeroBigDecimal(map
																.get("RGSTAMT")),
														thr)));// 放款金額
							}
						} else {
							// RGSTCUR=Cstr(result.GetValue("CURR")) '放款幣別
							rsgAmtList.add(branchRate.toOtherAmt(usdCurr, curr,
									Arithmetic.div(LMSUtil
											.nullToZeroBigDecimal(map
													.get("RGSTUDS")), thr)));// 放款金額
							rgstCur = Util.trim(map.get("usdCurr"));
							rgstAmt = LMSUtil.nullToZeroBigDecimal(map
									.get("RGSTUDS"));
						}
					} else if ("01".equals(collType1) || "02".equals(collType1)
							|| "08".equals(collType1)) {
						// RGSTCUR=Cstr(result.GetValue("RGSTCUR")) '放款幣別
						if (Util.isNotEmpty(map.get("RGSTCUR"))) {
							rgstCur = Util.trim(map.get("RGSTCUR"));
							rgstAmt = LMSUtil.nullToZeroBigDecimal(map
									.get("RGSTAMT"));
							rsgAmtList.add(branchRate.toOtherAmt(Util.trim(map
									.get("RGSTCUR")), curr, Arithmetic.div(
									LMSUtil.nullToZeroBigDecimal(map
											.get("RGSTAMT")), thr)));// 放款金額
						}
					} else {
						// RGSTCUR=Cstr(result.GetValue("CURR")) '放款幣別
						if (Util.isNotEmpty(Util.trim(map.get("CURR")))) {
							rsgAmtList.add(branchRate.toOtherAmt(Util.trim(map
									.get("CURR")), curr, Arithmetic.div(LMSUtil
									.nullToZeroBigDecimal(map.get("LOANAMT")),
									thr)));// 放款金額
						}
					}
					// 保險
					elf347List = obsdbELFFORLRSService.listELF347Data(branchId,
							dbTYPCD, dbCUSTID, dbDUPNO, gCollno);
					gCollIns2.setLength(0);
					if (elf347List.size() > 0) {
						for (Map<String, Object> elf347Map : elf347List) {
							String[] gCollInsTemp = {
									Util.trim(elf347Map.get("INSKND1")),
									Util.trim(elf347Map.get("INSKND2")),
									Util.trim(elf347Map.get("INSKND")) }; // 保險種類
							String gCollInn = Util.trim(elf347Map.get("INSNM")); // 保險名稱
							String gCollICur = Util.trim(elf347Map
									.get("INSCUR")); // 保險幣別
							BigDecimal gCollIAmt = LMSUtil
									.toBigDecimal(elf347Map.get("INSAMT")); // 保險金額
							gCollIAmt = gCollIAmt == null ? BigDecimal.ZERO
									: Arithmetic.div(gCollIAmt, thr);
							String gCollInsDT = Util.trim(elf347Map
									.get("INSEDT")); // 保險到期日
							boolean checkCollIn = true;
							for (String gCollIns : gCollInsTemp) {
								if (!"0".equals(gCollIns)) {
									if (gCollIns.length() >= 2) {
										String tempCollIn = gCollIns.substring(
												0, 2);
										if ("01".equals(tempCollIn)) {
											if (gCollIns2.length() > 0) {
												gCollIns2.append("、");
											}
											gCollIns2.append(insTypeMap
													.get("1"));
										} else if ("02".equals(tempCollIn)) {
											if (gCollIns2.length() > 0) {
												gCollIns2.append("、");
											}
											gCollIns2.append(insTypeMap
													.get("2"));
										} else if ("03".equals(tempCollIn)) {
											if (gCollIns2.length() > 0) {
												gCollIns2.append("、");
											}
											gCollIns2.append(insTypeMap
													.get("4"));
										} else if ("04".equals(tempCollIn)) {
											if (gCollIns2.length() > 0) {
												gCollIns2.append("、");
											}
											gCollIns2.append(insTypeMap
													.get("8"));
										} else if ("05".equals(tempCollIn)) {
											if (gCollIns2.length() > 0) {
												gCollIns2.append("、");
											}
											gCollIns2.append(gCollInn);
										} else {
											checkCollIn = false;
										}
									}
								}
							}
							if (Util.isNotEmpty(elf347Map.get("INSNM"))) {
								gCollIns2.append("、").append(
										Util.trim(elf347Map.get("INSNM")));
								checkCollIn = true;
							}
							if (checkCollIn) {
								gCollIns2
										.append(" ")
										.append(gCollICur)
										.append(" ")
										.append(NumConverter
												.addComma(gCollIAmt))
										.append("千元，到期日")
										.append(Util.isEmpty(gCollInsDT) ? ""
												: TWNDate.toAD(TWNDate
														.valueOf(gCollInsDT)));
							}
						}
					}
					tmpcollins.append(prop.getProperty("ImportCMSData.056"));
					tmpcollins.append(NumConverter.addComma(gLoanRT, "#,###"));
					tmpcollins.append("％。 ");
					tmpcollins.append("\n");
					tmpcollins.append(prop.getProperty("ImportCMSData.057"));
					tmpcollins.append(Util.trim(rgstCur));
					tmpcollins.append(" ");
					tmpcollins.append(NumConverter.addComma(rgstAmt));
					tmpcollins.append(prop.getProperty("ImportCMSData.058"));
					tmpcollins.append("\n");
					if (Util.isNotEmpty(gCollIns2)) {
						tmpcollins
								.append(prop.getProperty("ImportCMSData.059"));
						tmpcollins.append(gCollIns2.toString());
						tmpcollins
								.append(prop.getProperty("ImportCMSData.060"));
						tmpcollins.append("\n");
					}
				}
			}
		}
		// 組合擔保品字串************************************************************************************************
		Set<String> set = null;
		int count = 0;
		StringBuffer temp = new StringBuffer();
		if (reCallMap.size() > 0) {
			for (String key : reCallMap.keySet()) {
				if (key.equals(prop.getProperty("ImportCMSData.001"))) {
					temp.setLength(0);
					count = 0;
					set = reCallMap.get(prop.getProperty("ImportCMSData.001"));
					if (set != null && set.size() > 0) {
						temp.append(key);
						for (String value : set) {
							if (Util.isNotEmpty(value)) {
								if (count > 0 && str.length() > 0) {
									temp.append("、");
								}
								temp.append(Util.trim(value));
								if (Util.isNotEmpty(Util.trim(value))) {
									count++;
								}
							}
						}
						temp.append("。\n");
					}
					if (count > 0) {
						str.append(temp);
					}
				} else if (key.equals(prop.getProperty("ImportCMSData.002"))) {
					temp.setLength(0);
					set = reCallMap.get(prop.getProperty("ImportCMSData.002"));
					if (set != null && set.size() > 0) {
						temp.append(key);
						count = 0;
						for (String value : set) {
							if (Util.isNotEmpty(value)) {
								if (count > 0 && str.length() > 0) {
									temp.append("、");
								}
								temp.append(Util.trim(value));
								if (Util.isNotEmpty(Util.trim(value))) {
									count++;
								}
							}
						}
						temp.append("。\n");
					}
					if (count > 0) {
						str.append(temp);
					}
				} else if (key.equals(prop.getProperty("ImportCMSData.003"))) {
					temp.setLength(0);
					set = reCallMap.get(prop.getProperty("ImportCMSData.003"));
					if (set != null && set.size() > 0) {
						temp.append(key);
						count = 0;
						for (String value : set) {
							if (Util.isNotEmpty(value)) {
								if (count > 0 && str.length() > 0) {
									temp.append("、");
								}
								temp.append(Util.trim(value));
								if (Util.isNotEmpty(Util.trim(value))) {
									count++;
								}
							}
						}
						temp.append("。\n");
					}
					if (count > 0) {
						str.append(temp);
					}
				} else {
					temp.setLength(0);
					set = reCallMap.get(key);
					if (set != null && set.size() > 0) {
						temp.append(key);
						count = 0;
						for (String value : set) {
							if (Util.isNotEmpty(value)) {
								if (count > 0 && str.length() > 0) {
									temp.append("、");
								}
								temp.append(Util.trim(value));
								if (Util.isNotEmpty(Util.trim(value))) {
									count++;
								}
							}
						}
						temp.append("。\n");
					}
					if (count > 0) {
						str.append(temp);
					}
				}
			}
		} else {
			str.append(prop.getProperty("ImportCMSData.004"));
		}

		BigDecimal appAmt = BigDecimal.ZERO;
		BigDecimal loanAmt = BigDecimal.ZERO;
		BigDecimal rgAmt = BigDecimal.ZERO;
		BigDecimal rsgAmt = BigDecimal.ZERO;
		for (BigDecimal amt : appAmtList) {
			appAmt = appAmt.add(amt);
		}
		for (BigDecimal amt : loanAmtList) {
			loanAmt = loanAmt.add(amt);
		}
		for (BigDecimal amt : rgAmtList) {
			rgAmt = rgAmt.add(amt);
		}
		for (BigDecimal amt : rsgAmtList) {
			rsgAmt = rsgAmt.add(amt);
		}
		returnMap.put("loanCurr", curr);
		returnMap.put("estCurr", curr);
		BigDecimal one = new BigDecimal(1);
		rsgAmt = Arithmetic.div(rsgAmt, one, 2);
		loanAmt = Arithmetic.div(loanAmt, one, 2);
		rgAmt = Arithmetic.div(rgAmt, one, 2);
		appAmt = Arithmetic.div(appAmt, one, 2);
		if (rgAmt.compareTo(loanAmt) == 1) {
			returnMap.put("loanAmt", loanAmt);
		} else {
			returnMap.put("loanAmt", rgAmt);
		}
		returnMap.put("estAmt", appAmt);
		returnMap.put("guaranteeName", str.toString());

		String collins = Util.truncateString(tmpcollins.toString(), 768);
		returnMap.put("insMemo", collins);

		return returnMap;
	}

	/**
	 * 取得擔保品資料
	 * 
	 * @param collType1
	 * @param collType2
	 * @param tBranch
	 * @param tCustID
	 * @param tDupNo
	 * @param tCollno
	 * @return
	 */
	private Map<String, Set<String>> getCollDetailData(String collType1,
			String collType2, String tBranch, String tCustID, String tDupNo,
			String tCollno, Map<String, Set<String>> returnMap) {
		tBranch = StringEscapeUtils.escapeSql(tBranch);
		tCustID = StringEscapeUtils.escapeSql(tCustID);
		tDupNo = StringEscapeUtils.escapeSql(tDupNo);
		tCollno = StringEscapeUtils.escapeSql(tCollno);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1705S02Panel.class);
		String collName = null;
		String table = null;
		String selectField = null;
		List<Map<String, Object>> list = null;
		Set<String> collList = null;
		// boolean buildOnLand = false;
		StringBuffer str = new StringBuffer();
		if ("01".equals(collType1)) {
			// outputData.setLength(0);
			str.setLength(0);
			collName = prop.getProperty("ImportCMSData.001");
			// 先抓建物
			// 一定要ORDER BY ASC 要不然如果先抓到 新湖二路 ２３４號 ２樓之１再抓到 新湖二路 ２３４號 ２
			// 樓，會變成計算成同一個建物
			list = obsdbELFFORLRSService
					.list349ColDataByCustIdDupNoCollNoBranch(tCustID, tDupNo,
							tCollno, tBranch);
			if (list.size() > 0) {
				if (returnMap.containsKey(collName)) {
					collList = returnMap.get(collName);
				} else {
					collList = new HashSet<String>();
				}
				for (Map<String, Object> map : list) {
					str.setLength(0);
					if (Util.isNotEmpty(map.get("LADDR"))) {
						str.append(Util.trim(map.get("LADDR")));
					}
					// if (Util.isNotEmpty(map.get("SITE1"))) {
					// str.append(Util.trim(map.get("SITE1")));
					// }
					// if (Util.isNotEmpty(map.get("SITE2"))) {
					// str.append(Util.trim(map.get("SITE2")));
					// }
					// if (Util.isNotEmpty(map.get("SITE3"))) {
					// str.append(Util.trim(map.get("SITE3")));
					// }
					// if (Util.isNotEmpty(map.get("SITE4"))) {
					// str.append(Util.trim(map.get("SITE4"))).append("鄰");
					// }
					// if (Util.isNotEmpty(map.get("SITE5"))) {
					// str.append(Util.trim(map.get("SITE5")));
					// }
					// if (Util.isNotEmpty(map.get("SITE6"))) {
					// str.append(Util.trim(map.get("SITE6"))).append("段");
					// }
					// if (Util.isNotEmpty(map.get("SITE7"))) {
					// str.append(Util.trim(map.get("SITE7"))).append("巷");
					// }
					// if (Util.isNotEmpty(map.get("SITE8"))) {
					// str.append(Util.trim(map.get("SITE8"))).append("弄");
					// }
					// if (Util.isNotEmpty(map.get("SITE9"))) {
					// str.append(Util.trim(map.get("SITE9"))).append("號");
					// }
					// if (Util.isNotEmpty(map.get("SITE10"))) {
					// str.append(Util.trim(map.get("SITE10"))).append("樓");
					// }
					// if (Util.isNotEmpty(map.get("SITE11"))) {
					// str.append(Util.trim(map.get("SITE11")));
					// }
					// if (Util.isNotEmpty(map.get("SITE12"))) {
					// str.append(Util.trim(map.get("SITE12"))).append("室");
					// }
					collList.add(str.toString());
				}
				returnMap.put(collName, collList);
			}
			collName = "";
			str.setLength(0);
			// 再抓建物
			list = obsdbELFFORLRSService.listColDataByCustIdDupNoCollNoBranch(
					tCustID, tDupNo, tCollno, tBranch);
			if (list.size() > 0) {
				for (Map<String, Object> map : list) {
					str.setLength(0);
					// if (str.length() > 0) {
					// str.append("、");
					// }
					if (Util.isNotEmpty(map.get("LADDR"))) {
						str.append(Util.trim(map.get("LADDR")));
					}

					// if (Util.isNotEmpty(map.get("SITE1"))) {
					// str.append(Util.trim(map.get("SITE1")));
					// }
					// if (Util.isNotEmpty(map.get("SITE2"))) {
					// str.append(Util.trim(map.get("SITE2")));
					// }
					// if (Util.isNotEmpty(map.get("SITE3"))) {
					// str.append(Util.trim(map.get("SITE3"))).append("段");
					// }
					// if (Util.isNotEmpty(map.get("SITE4"))) {
					// str.append(Util.trim(map.get("SITE4"))).append("小段");
					// }
					// if (Util.isNotEmpty(map.get("LNNO1"))) {
					// str.append(Util.trim(map.get("LNNO1")));
					// if (Util.isNotEmpty(map.get("LNNO2"))) {
					// str.append("-").append(Util.trim(map.get("LNNO2")));
					// }
					// } else {
					// str.append(Util.trim(map.get("IMMNO")));
					// }
					if (Util.isNotEmpty(map.get("BN1"))
							|| Util.isNotEmpty(map.get("BN2"))) {
						collName = prop.getProperty("ImportCMSData.003");
					} else {
						// 土地跟建物是一體
						// buildOnLand = true;
						collName = prop.getProperty("ImportCMSData.002");
					}
					if (returnMap.containsKey(collName)) {
						collList = returnMap.get(collName);
					} else {
						collList = new HashSet<String>();
					}
					collList.add(str.toString());
					returnMap.put(collName, collList);
				}
			}
		} else {
			// String condition = " BRANCH = '" + tBranch + "' AND CUSTID = '"
			// + tCustID + "' AND DUPNO = '" + tDupNo + "' AND COLLNO = '"
			// + tCollno + "' ";
			String condition = " BRANCH = ? AND CUSTID = ? AND DUPNO = ? AND COLLNO = ? ";

			String orderBy = "";
			boolean collCheck = true;
			if ("02".equals(collType1)) {
				table = "ELF350 ";
				selectField = " COLLNM ";
			} else if ("03".equals(collType1)) {
				if ("01".equals(collType2)) {
					table = "ELF351 ";
					selectField = " FLG,DEPBKNM,DEPBRCH,DEPNO ";
				} else if ("02".equals(collType2)) {
					table = "ELF352 ";
					selectField = " KIND,QNTY ";
				} else if ("03".equals(collType2)) {
					table = "ELF353 ";
					selectField = " BONDNM,QNTY ";
				} else if ("04".equals(collType2)) {
					table = "ELF354 ";
					selectField = " BBONDNM,QNTY ";
				} else if ("05".equals(collType2)) {
					table = "ELF355 ";
					selectField = " PARVAL ";
				} else if ("06".equals(collType2)) {
					table = "ELF356 ";
					selectField = " CBNM,QNTY ";
				} else if ("07".equals(collType2)) {
					table = "ELF357 ";
					selectField = " STKNM,QNTY ";
				} else if ("08".equals(collType2)) {
					table = "ELF358 ";
					selectField = " FUNDCD,QNTY ";
				}
			} else if ("04".equals(collType1)) {
				table = "ELF359 ";
				selectField = " TRSTNM,QNTY ";
			} else if ("05".equals(collType1)) {
				if ("01".equals(collType2)) {
					table = "ELF360 ";
					selectField = " GRTBKNM,GRTKIND,CURR,OGRTAMT,GRTRT ";
				} else if ("02".equals(collType2)) {
					table = "ELF361 ";
					selectField = " GRTDEPT,GRTKIND,CURR,OGRTAMT,GRTRT ";
				} else if ("03".equals(collType2)) {
					table = "ELF362 ";
					selectField = " GRTNM,GRTITEM,CURR,OGRTAMT,GRTRT ";
				} else if ("04".equals(collType2)) {
					table = "ELF363 ";
					selectField = " GRTNM,GRTITEM,CURR,OGRTAMT ,GRTRT ";
				}
			} else if ("06".equals(collType1)) {
				table = "ELF364 ";
				selectField = " KIND,NTTYPE,NTNO ";
			} else if ("07".equals(collType1)) {
				table = "ELF365 ";
				selectField = " NTTYPE,ISUNM,NTNO ";
			} else if ("08".equals(collType1)) {
				table = "ELF366 ";
				selectField = " COLLNM,UNIT,QNTY ";
			} else if ("09".equals(collType1)) {
				table = "ELF367 ";
				selectField = " COLLNM,UNIT,QNTY ";
			} else if ("10".equals(collType1)) {
				table = "ELF368 ";
				selectField = " COLLNM,UNIT,QNTY ";
			} else if ("11".equals(collType1)) {
				table = "ELF449 ";
				selectField = " KINDDESC ";
			} else if ("12".equals(collType1)) {
				table = "ELFCO1201 ";
				selectField = " COLLKIND,COLLNAME ";
			} else {
				collCheck = false;
			}
			if (collCheck) {
				if (Util.isNotEmpty(table) && Util.isNotEmpty(selectField)) {
					list = obsdbELFFORLRSService.listCMSDataByCondition(
							tBranch, selectField, table, condition, orderBy,
							tCustID, tDupNo, tCollno);
				}
			}

			String grtName = null;
			// outputData.setLength(0);
			str.setLength(0);
			if ("02".equals(collType1)) {
				if (list != null && list.size() > 0) {
					if ("01".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.005");
					} else if ("02".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.006");
					} else if ("03".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.007");
					} else if ("04".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.008");
					} else if ("05".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.009");
					} else if ("06".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.010");
					} else if ("07".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.011");
					} else if ("08".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.012");
					} else if ("09".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.013");
					}
					if (returnMap.containsKey(collName)) {
						collList = returnMap.get(collName);
					} else {
						collList = new HashSet<String>();
					}
					for (Map<String, Object> map : list) {
						grtName = Util.trim(map.get("COLLNM"));
						// if (str.length() > 0) {
						// str.append("、");
						// }
						// str.append(grtName);
						collList.add(grtName);
					}
					returnMap.put(collName, collList);
				}
			} else if ("03".equals(collType1)) {
				// 92/06/25 李松明更改：若為有價證券則只顯示到小類名稱即可，數量不顯示
				if (list != null && list.size() > 0) {
					collName = prop.getProperty("ImportCMSData.014");
					if (returnMap.containsKey(collName)) {
						collList = returnMap.get(collName);
					} else {
						collList = new HashSet<String>();
					}
					for (Map<String, Object> map : list) {
						if ("01".equals(collType2)) {
							if ("1".equals(Util.trim(map.get("FLG")))) {
								grtName = prop.getProperty("ImportCMSData.015");
							} else if ("2".equals(Util.trim(map.get("FLG")))) {
								grtName = prop.getProperty("ImportCMSData.016");
							} else if ("3".equals(Util.trim(map.get("FLG")))) {
								grtName = prop.getProperty("ImportCMSData.017");
							} else if ("4".equals(Util.trim(map.get("FLG")))) {
								grtName = prop.getProperty("ImportCMSData.018");
							}
						} else if ("02".equals(collType2)) {
							if ("1".equals(Util.trim(map.get("KIND")))) {
								grtName = prop.getProperty("ImportCMSData.019");
							} else if ("2".equals(Util.trim(map.get("KIND")))) {
								grtName = prop.getProperty("ImportCMSData.020");
							}
						} else if ("03".equals(collType2)) {
							grtName = prop.getProperty("ImportCMSData.021");
						} else if ("04".equals(collType2)) {
							grtName = prop.getProperty("ImportCMSData.022");
						} else if ("05".equals(collType2)) {
							grtName = prop.getProperty("ImportCMSData.023");
						} else if ("06".equals(collType2)) {
							grtName = prop.getProperty("ImportCMSData.024");
						} else if ("07".equals(collType2)) {
							grtName = prop.getProperty("ImportCMSData.025");
						} else if ("08".equals(collType2)) {
							grtName = prop.getProperty("ImportCMSData.026");
						}
						collList.add(grtName);
					}
					returnMap.put(collName, collList);
				}
			} else if ("04".equals(collType1)) {
				if (list != null && list.size() > 0) {
					collName = prop.getProperty("ImportCMSData.027");
					if (returnMap.containsKey(collName)) {
						collList = returnMap.get(collName);
					} else {
						collList = new HashSet<String>();
					}
					for (Map<String, Object> map : list) {
						grtName = Util.trim(map.get("TRSTNM"));
						// if (str.length() > 0) {
						// str.append("、");
						// }
						// str.append(grtName);
						collList.add(grtName);
					}
					returnMap.put(collName, collList);
				}
			} else if ("05".equals(collType1)) {
				if (list != null && list.size() > 0) {
					BigDecimal ten = new BigDecimal(10);
					collName = prop.getProperty("ImportCMSData.028");
					if (returnMap.containsKey(collName)) {
						collList = returnMap.get(collName);
					} else {
						collList = new HashSet<String>();
					}
					for (Map<String, Object> map : list) {
						str.setLength(0);
						if ("01".equals(collType2)) {
							grtName = prop.getProperty("ImportCMSData.029");
						} else if ("02".equals(collType2)) {
							grtName = prop.getProperty("ImportCMSData.030");
						} else if ("03".equals(collType2)) {
							grtName = prop.getProperty("ImportCMSData.031");
						} else if ("04".equals(collType2)) {
							grtName = prop.getProperty("ImportCMSData.032");
						}
						BigDecimal tStrPa = Arithmetic.div(
								LMSUtil.nullToZeroBigDecimal(map.get("GRTRT")),
								ten);
						// if (str.length() > 0) {
						// str.append("、");
						// }
						str.append(grtName)
								.append(NumConverter
										.addComma(tStrPa, "#,##0.0"))
								.append(prop.getProperty("ImportCMSData.039"));
						collList.add(str.toString());
					}
					returnMap.put(collName, collList);
				}
			} else if ("06".equals(collType1)) {
				if (list != null && list.size() > 0) {
					for (Map<String, Object> map : list) {
						if ("1".equals(Util.trim(map.get("KIND")))) {
							collName = prop.getProperty("ImportCMSData.033");
						} else if ("2".equals(Util.trim(map.get("KIND")))) {
							collName = prop.getProperty("ImportCMSData.034");
						}
						if ("1".equals(Util.trim(map.get("NTTYPE")))) {
							grtName = prop.getProperty("ImportCMSData.035")
									+ "("
									+ prop.getProperty("ImportCMSData.038")
									+ Util.trim(map.get("NTNO")) + ")";
						} else if ("2".equals(Util.trim(map.get("NTTYPE")))) {
							grtName = prop.getProperty("ImportCMSData.036")
									+ "("
									+ prop.getProperty("ImportCMSData.038")
									+ Util.trim(map.get("NTNO")) + ")";
						} else if ("3".equals(Util.trim(map.get("NTTYPE")))) {
							grtName = prop.getProperty("ImportCMSData.037")
									+ "("
									+ prop.getProperty("ImportCMSData.038")
									+ Util.trim(map.get("NTNO")) + ")";
						}
						if (returnMap.containsKey(collName)) {
							collList = returnMap.get(collName);
						} else {
							collList = new HashSet<String>();
						}
						collList.add(grtName);
						// if (str.length() > 0) {
						// str.append("、");
						// }
						// str.append(grtName);
					}
					returnMap.put(collName, collList);
					// outputData.append(collName).append(grtName);
				}
			} else if ("07".equals(collType1)) {
				if (list != null && list.size() > 0) {
					collName = prop.getProperty("ImportCMSData.040");
					if (returnMap.containsKey(collName)) {
						collList = returnMap.get(collName);
					} else {
						collList = new HashSet<String>();
					}
					for (Map<String, Object> map : list) {
						if ("1".equals(Util.trim(map.get("NTTYPE")))) {
							grtName = prop.getProperty("ImportCMSData.035")
									+ "("
									+ prop.getProperty("ImportCMSData.042")
									+ Util.trim(map.get("ISUNM")) + "　"
									+ prop.getProperty("ImportCMSData.038")
									+ Util.trim(map.get("NTNO")) + ")";
						} else if ("2".equals(Util.trim(map.get("NTTYPE")))) {
							grtName = prop.getProperty("ImportCMSData.036")
									+ "("
									+ prop.getProperty("ImportCMSData.042")
									+ Util.trim(map.get("ISUNM")) + "　"
									+ prop.getProperty("ImportCMSData.038")
									+ Util.trim(map.get("NTNO")) + ")";
						}
						// if (str.length() > 0) {
						// str.append("、");
						// }
						// str.append(grtName);
						collList.add(grtName);
					}
					returnMap.put(collName, collList);
					// outputData.append(collName).append(grtName);
				}

			} else if ("08".equals(collType1)) {
				if (list != null && list.size() > 0) {
					if ("01".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.043");
					} else if ("02".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.044");
					} else if ("03".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.045");
					} else if ("04".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.046");
					} else if ("05".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.047");
					} else if ("06".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.048");
					} else if ("07".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.049");
					} else if ("08".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.050");
					} else if ("09".equals(collType2)) {
						collName = prop.getProperty("ImportCMSData.051");
					}
					if (returnMap.containsKey(collName)) {
						collList = returnMap.get(collName);
					} else {
						collList = new HashSet<String>();
					}
					for (Map<String, Object> map : list) {
						grtName = Util.trim(map.get("COLLNM"));
						// if (str.length() > 0) {
						// str.append("、");
						// }
						// str.append(grtName);
						collList.add(grtName);
					}
					returnMap.put(collName, collList);
					// outputData.append(collName).append(grtName);
				}
			} else if ("09".equals(collType1)) {
				if (list != null && list.size() > 0) {
					collName = prop.getProperty("ImportCMSData.052");
					if (returnMap.containsKey(collName)) {
						collList = returnMap.get(collName);
					} else {
						collList = new HashSet<String>();
					}
					for (Map<String, Object> map : list) {
						grtName = Util.trim(map.get("COLLNM"));
						// if (str.length() > 0) {
						// str.append("、");
						// }
						// str.append(grtName);
						collList.add(grtName);
					}
					returnMap.put(collName, collList);
					// outputData.append(collName).append(grtName);
				}
			} else if ("10".equals(collType1)) {
				if (list != null && list.size() >= 1) {
					collName = prop.getProperty("ImportCMSData.053");
					if (returnMap.containsKey(collName)) {
						collList = returnMap.get(collName);
					} else {
						collList = new HashSet<String>();
					}
					for (Map<String, Object> map : list) {
						grtName = Util.trim(map.get("COLLNM"));
						collList.add(grtName);
					}
					returnMap.put(collName, collList);
				}
			} else if ("11".equals(collType1)) {
				if (list != null && list.size() >= 1) {
					collName = prop.getProperty("ImportCMSData.054");
					if (returnMap.containsKey(collName)) {
						collList = returnMap.get(collName);
					} else {
						collList = new HashSet<String>();
					}
					for (Map<String, Object> map : list) {
						grtName = Util.trim(map.get("KINDDESC"));
						collList.add(grtName);
					}
					returnMap.put(collName, collList);
				}
			} else if ("12".equals(collType1)) {
				if (list != null && list.size() >= 1) {
					collName = prop.getProperty("ImportCMSData.055");
					if (returnMap.containsKey(collName)) {
						collList = returnMap.get(collName);
					} else {
						collList = new HashSet<String>();
					}
					for (Map<String, Object> map : list) {
						grtName = Util.trim(map.get("COLLKIND")) + " "
								+ Util.trim(map.get("COLLNAME"));
						collList.add(grtName);
					}
					returnMap.put(collName, collList);
				}
			}

		}

		return returnMap;
	}

	// J-107-0045-001 Web e-Loan企金授信簽報書配合海外啟用IFRS徵信報告調整財報引進相關功能。
	private String[] _subNoArr(F101M01A f101m01a) {
		String subNo1 = "50000000";
		String subNo2 = "57000000";
		String subNo3 = "70000000";
		// ===============================================
		String gaapFlag = Util.trim(f101m01a.getGaapFlag());
		String tradeType = Util.trim(f101m01a.getTradeType());
		if (Util.equals("0", gaapFlag)) {// GAAP
			// the same
		} else if (Util.equals("1", gaapFlag)) {// IFRS
			if (Util.equals("M", tradeType)) {
				// '一般行業
				// 'a41000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "41000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("I", tradeType)) {
				// '壽險業
				// 'a47000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "47000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("P", tradeType)) {
				// '產險業
				// 'a47000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "47000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("S", tradeType)) {
				// '證券業
				// 'a44000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "44000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("L", tradeType)) {
				// '租賃業
				// 'a43000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "43000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("V", tradeType)) {
				// '投資業
				// 'a42000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "42000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("H", tradeType)) {
				// '金控業
				// 'a45000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "45000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("B", tradeType)) {
				// '銀行業
				// 'a46000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "46000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else {
				subNo1 = "41000";
				subNo2 = "61000";
				subNo3 = "63000";
			}
		} else if (Util.equals("2", gaapFlag)) {// EAS
			// J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
			if (Util.equals("M", tradeType)) {
				// '一般行業
				// 'a41000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "41000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("I", tradeType)) {
				// '壽險業
				// 'a47000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "47000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("P", tradeType)) {
				// '產險業
				// 'a47000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "47000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("S", tradeType)) {
				// '證券業
				// 'a44000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "44000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("L", tradeType)) {
				// '租賃業
				// 'a43000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "43000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("V", tradeType)) {
				// '投資業
				// 'a42000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "42000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("H", tradeType)) {
				// '金控業
				// 'a45000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "45000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else if (Util.equals("B", tradeType)) {
				// '銀行業
				// 'a46000 營業收入
				// 'a61000 營業利益
				// 'a63000 稅前損益
				subNo1 = "46000";
				subNo2 = "61000";
				subNo3 = "63000";
			} else {
				subNo1 = "41000";
				subNo2 = "61000";
				subNo3 = "63000";
			}
		}
		// ===============================================
		String[] arr = new String[3];
		arr[0] = subNo1;
		arr[1] = subNo2;
		arr[2] = subNo3;
		return arr;
	}

	/**
	 * J-108-0260 海外覆審檢視表 取得該案複審內容版本
	 */
	public String getReviewType(L170M01A l170m01a) {
		String reviewType = "lms1705s04_reviewTypeV3"; // 預設最新

		if (l170m01a != null) {
			if ("".equals(Util.nullToSpace(l170m01a.getRptId()))) {
				reviewType = "lms1705s04_reviewType";
			} else {
				if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
						LrsUtil.V_O_201907)) {
					reviewType = "lms1705s04_reviewTypeV3";
				} else {
					reviewType = "lms1705s04_reviewTypeV2";
				}

			}
		}

		return reviewType;
	}

	/**
	 * J-108-0260 海外覆審檢視表 取得檢視表項目
	 */
	public HashMap<String, String> getChkList(String reviewType) {
		HashMap<String, String> item = new HashMap<String, String>();

		item.put("0", "B001"); // 各項債權憑證之內容是否妥適且齊全？
		item.put("1", "B002"); // 契約書等是否辦妥對保手續？
		if (Util.equals(reviewType, "lms1705s04_reviewType")) {
			item.put("2", "B017"); // 應收客票之金額是否足夠？
			item.put("3", ""); // 擔保品估價是否按照規定？
			item.put("4", ""); // 擔保品設定擔保物權、順位及金額是否與核貸條件相符？
			item.put("5", "B021"); // 保險受益人、標的、期間、投保金額、保費收據是否完善？
		} else if (Util.equals(reviewType, "lms1705s04_reviewTypeV2")
				|| Util.equals(reviewType, "lms1705s04_reviewTypeV3")) {
			item.put("2", "B018"); // 應收客票之金額是否足夠？
			item.put("3", "B020"); // 擔保品估價是否按照規定？
			item.put("4", "B021"); // 擔保品設定擔保物權、順位及金額是否與核貸條件相符？
			item.put("5", "B022"); // 保險受益人、標的、期間、投保金額、保費收據是否完善？
		} else {
			item.put("2", ""); // 應收客票之金額是否足夠？
			item.put("3", ""); // 擔保品估價是否按照規定？
			item.put("4", ""); // 擔保品設定擔保物權、順位及金額是否與核貸條件相符？
			item.put("5", ""); // 保險受益人、標的、期間、投保金額、保費收據是否完善？
		}

		return item;
	}
}
