/* 
 * LMS9541Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L810M01A;

public interface LMS9541V01Service extends AbstractService {
	final String[] RPTNAMES = { "", "八仟億優惠房貸額度控制表", "三千億優惠房貸額度控制表",
			"九十七年度二千億優惠房貸額度控制表", "青年安心成家優惠貸款-購置住宅", "全部優惠房貸" };

	final int BASE = 10000;// 單位

	public boolean isRepeat(L810M01A data);

	// use MIS-RDB
	public List<Map<String, Object>> findMisData(String kindNo,
			boolean countData, boolean tooMuch, String brno);

	public List<Map<String, Object>> getDataBrno(String kindNo);
}
