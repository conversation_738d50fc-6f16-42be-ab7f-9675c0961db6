package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C122M01C;

public interface C122M01CDao extends IGenericDao<C122M01C> {

	public C122M01C findByOid(String oid);	

	public C122M01C findByMainIdSeq(String mainId, int seq);
	
	public List<C122M01C> findByMainIdOrderBySeqAsc(String mainId);
	public List<C122M01C> findByMainIdOrderBySeqDesc(String mainId);
}
