/* 
 * CLS8011Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service;


/**
 * <pre>
 * 個金個人資料清冊作業Service
 * </pre>
 * 
 * @since 2014/04/01
 * <AUTHOR> @version <ul>
 *         
 *          </ul>
 */
public interface CLS8011Service  {
	/*
	public C801M01A findC801M01A_oid(String oid);
	public C801M01A findC801M01A_mainId(String mainId);
	public C801M01A findC801M01A_cntrNo(String cntrNo);
	public List<C801M01A> findC801M01A_ownBrId(String ownBrId);
	public List<C801M01B> findC801M01B_mainId(String mainId);
	public List<C801M01B> findC801M01B_mainId_itemType(String mainId, String itemType);
	public C801M01B findC801M01B_oid(String oid);
	public void saveData(C801M01A meta, List<C801M01B> addList);
	public List<C801M01B> genLatest(String mainId);
	 
	public boolean deleteC801m01as(String[] oids);
	public void deleteC801M01B(String mainId);	
	public C801M01A initModel(String custId, String dupNo, String custName, String cntrNo);
	public void setStatusEdit(C801M01A meta);
	public void setStatusFinish(C801M01A meta);
	
	public boolean isSelfAddItem(C801M01B c801m01b);
	public C801M01B selfAddItem(C801M01A meta, String itemType, String itemContent);
	public void saveAndAdjustSeq(C801M01A meta, C801M01B dcC801M01B);
	
	public void exportExcel(ByteArrayOutputStream outputStream, List<C801M01A> list ) throws IOException, WriteException;
	*/
}
