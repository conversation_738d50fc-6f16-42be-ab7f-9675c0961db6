/* 
 *LMS9121Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service;

import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.model.GenericBean;

/**
 * <pre>
 * 資料修正
 * </pre>
 * 
 * @since 2012/11/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/23,<PERSON>,new
 *          </ul>
 */
public interface LMS9121Service {
	public List<Map<String, Object>> sntrnoupdate(String OldCntrNo,
			String NewCntrNo);
	public List<Map<String, Object>> custIdupdate(String OldCustId,
			String OlddupId, String NewCustId, String NewdupId);
	public boolean checkNewCntrNo(String[] dwtable, String[] mistable,
			String NewCntrNo);

	public List<Map<String, Object>> MisCntrNoUpdate(String[] table,
			List<Map<String, Object>> miscntrnodata, String OldCntrNo,
			String NewCntrNo);

	public List<Map<String, Object>> DwCntrNoUpdate(String[] table,
			List<Map<String, Object>> dwcntrnodata, String OldCntrNo,
			String NewCntrNo);

	public List<Map<String, Object>> R6CntrNoUpdate(String OldCntrNo,
			String NewCntrNo);

	public void save(GenericBean... entity);
}
