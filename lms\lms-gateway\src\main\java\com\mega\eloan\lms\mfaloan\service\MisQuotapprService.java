/* 
 *MisQuotapprService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 授信額度檔 QUOTAPPR (MIS.ELV38301)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          2011/12/23,REX,new
 *          </ul>
 */
public interface MisQuotapprService {

	/**
	 * 修改資料流程 更新
	 * 
	 * @param unionAmt
	 *            聯貸額度
	 * @param updater
	 *            更新者
	 * @param curAmt
	 *            現請額度
	 * @param caseType
	 *            案件性質
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 */
	public void updateByUniqueKey(BigDecimal unionAmt, String updater,
			BigDecimal curAmt, String caseType, String custId, String dupNo,
			String cntrNo);

	/**
	 * 刪除
	 * 
	 * @param custId
	 *            身分證/統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統當天日期
	 */
	void delByUniqueKey(String custId, String dupNo, String cntrNo, String sDate);

	/**
	 * 新增(海外)
	 * 
	 * @param custid
	 *            借款人統編
	 * @param dupno
	 *            DUPNO
	 * @param cntrno
	 *            CNTRNO
	 * @param sdate
	 *            系統當天日期
	 * @param casetype
	 *            案件性質
	 * @param lnflag
	 *            性質A.增額、D.減額 N.新作 C.額度取消 U.僅聯貸參貸比例變更
	 * 
	 * @param oldamt
	 *            原請額度
	 * @param curamt
	 *            現請額度
	 * 
	 * @param curcurr
	 *            現請額度幣別
	 * @param lnqtflag
	 *            科子目限額變更
	 * @param reclflag
	 *            循環額度變更
	 * @param sguflag
	 *            送註記
	 * @param lrptype
	 *            動用審核表－授信契約書型態，短期為１；中長期為2
	 * @param llnno
	 *            動用審核表-授信期間代碼 1. YYY/MM/DD～YYY/MM/DD 2. 自首動日起YY年XX個月 3. 其他
	 * 
	 * @param llnfdate
	 *            中長期授信期間代碼為1時為起始日期否則設為0001-01-01
	 * @param llnedate
	 *            中長期授信期間代碼為1時為終止日期否則設為0001-01-01
	 * @param llnmon
	 *            中長期授信期間代碼為2時會將年月轉換成月
	 * 
	 * @param lnuseno
	 *            1.YYY/MM/DD～YYY/MM/DD 2.自首次動用日起XX月 3.其他
	 * 
	 * @param usefmdt
	 *            動用期限代碼為1時為起始日期 動用期限代碼為2,3時 設為0001-01-01
	 * 
	 * @param useendt
	 *            動用期限代碼為1時 為終止日期 動用期限代碼為2,3時 設為0001-01-01
	 * 
	 * @param useftmn
	 *            動用期限代碼為2時 以月為單位,若為１年則為12
	 * 
	 * @param memo
	 *            其他敘作條件提示用語
	 * @param grantno
	 *            授權等級
	 * @param commborw
	 *            是否有共同借款人
	 * @param updater
	 *            資料修改人（行員代號）
	 * 
	 * @param reclchg
	 *            循環/不循環是否變更
	 * @param sguchg
	 *            送保註記是否變更
	 * @param gutflag
	 *            信保保證成數是否變更
	 * @param gutper
	 *            信保保證成數DECIMAL(3)
	 * @param llneflag
	 *            授信期間－終止日期是否變更(若非新作案件而此欄有變更者為Y)
	 * @param useeflag
	 *            動用期限－終止日期是否變更(若非新作案件而此欄有變更者為Y)
	 * @param lnnoflag
	 *            不計入授信項目代號
	 * @param unichgflag
	 *            聯貸案參貸金額是否變更(Y.是、N.否(若為新案則皆為Y))
	 * @param reflag
	 *            是否為銀行法或金控法利害關係人(Y.是、N.否(只要為其中之一即為Y))
	 * @param unionamt
	 *            聯貸額度(包含同業)
	 * @param shareamt
	 *            本行參貸總額度
	 * @param permittype
	 *            報核方式
	 * @param hideunion
	 *            是否為隱名參貸
	 * @param setdate
	 *            聯貸合約訂定日
	 * @param unionarea
	 *            國內聯貸或國際聯貸 A=國內　B=國際
	 * @param unionrole
	 *            聯行主辦(管理)行註記 Y=聯貸主辦行 L=聯貸案額度管理行 C=聯貸案擔保品管理行
	 * @param riskarea
	 *            本額度風險歸屬國別
	 * 
	 * @param existdate
	 *            OBU公司存續證明到期日
	 * @param feedate
	 *            OBU公司繳交年費證明到期日
	 * @param countrydt
	 *            OBU公司註冊國有效期
	 * @param crdttbl
	 *            信用評等等級
	 * @param mowtype
	 *            信用風險模型評等類別
	 * @param mowtbl1
	 *            信用風險模型評等等級
	 * @param syndipfd
	 *            聯貸信保註記
	 * @param cokind
	 *            合作業務種類 1:價金履約保證 2:合作外匯 Z:其他
	 * 
	 * @param cntrnom
	 *            合作業務母戶額度序號(12)
	 * @param rclno
	 *            合作業務子戶代收帳號(14)
	 * @param documentno
	 *            簽報書案號(20)
	 * @param crdtymd
	 *            舊評等日期
	 * @param crdtbr
	 *            舊評等分行
	 * @param mowymd
	 *            MOW 評等日期
	 * @param mowbr
	 *            MOW 評等分行
	 * @param controlcd
	 *            央行購住/空地/建屋貸款註記 屬於自然人購入住宅者(不含購入謄本登記主要用途為廠辦、商辦、店舖者)|1
	 *            屬於自然人/法人空地抵押貸款者(含擔保或副擔保)|2 屬於自然人/法人興建房屋貸款者 |3 非屬以上性質者 |4
	 * 
	 * @param duringflag
	 *            是否所有權取得日期在99/6/25以後或99/6/25後曾受央行管制客戶之增貸或轉貸案
	 * @param ltvrate
	 *            貸款成數
	 * @param locationcd
	 *            擔保品座落地區別 縣市別+區域別
	 * @param jcicmark
	 *            本次聯徵查詢名下有其他房貸資料 是 |Y 否 |N
	 * @param promise
	 *            承諾事項
	 * @param factType
	 *            額度控管種類
	 * @param commsno
	 *            是否有共用額度序號
	 */
	// G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
	// J-113-0377 海外分(子)行新增「社會責任授信」註記
	void insert(String custId, String dupNo, String cntrNo, String sDate,
			String caseType, String lnFlag, BigDecimal oldAmt,
			BigDecimal curAmt, String oldCurr, String curCurr, String lnqtFlag,
			String reclFlag, String sguFlag, String lrpType, String llnno,
			String llnfDate, String llnedate, int llnmon, String lnuseno,
			String usefmdt, String useendt, int useftmn, String memo,
			String grantno, String commborw, String updater, String reclchg,
			String sguchg, String gutflag, BigDecimal gutper, String llneflag,
			String useeflag, String lnnoflag, String unichgflag, String reflag,
			BigDecimal unionamt, BigDecimal shareamt, String permittype,
			String hideunion, String setdate, String unionarea,
			String unionrole, String riskarea, String existdate,
			String feedate, String countrydt, String crdttbl, String mowtype,
			String mowtbl1, String syndipfd, String cokind, String cntrnom,
			String rclno, String documentno, String crdtYmd, String crdtBr,
			String mowYmd, String mowbr, String controlcd, String duringFlag,
			BigDecimal ltvRate, String locationcd, String jcicMark,
			String promise, String factType, String commsno,
			BigDecimal riskFactors, BigDecimal riskActAmt, String reViewDate,
			BigDecimal reViewChg1, String unsecureFlag, String isEfin,
			String prodKind, String isNonSMEProjLoan,
			BigDecimal nonSMEProjLoanAmt, String exceptFlag, String itwCode,
			String inSmeFg, BigDecimal inSmeToAmt, BigDecimal inSmeCaAmt,
			String isHedge, BigDecimal enhanceAmt, String netSwft,
			BigDecimal netAmt, BigDecimal netAmtUnit, BigDecimal cgfRate,
			String cgfDate, String projClass, String isRevive,
			String revTarget, String revSubItem, String revPurpose,
			String itwCodeCoreBuss, String esggFg, String esggType,
			String esgsFg, String esgsType, String esgsUnre,
			BigDecimal cntrLgd, String stdAuth, String depositFg,
			String experf_fg, String flaw_fg, BigDecimal flaw_amt,
			String socialFlag, String socialKind, String socialTa, String socialResp);

	/**
	 * 新增
	 * 
	 * @param custid
	 *            借款人統編
	 * @param dupno
	 *            DUPNO
	 * @param cntrno
	 *            CNTRNO
	 * @param sdate
	 *            系統當天日期
	 * @param casetype
	 *            案件性質
	 * @param lnflag
	 *            性質A.增額、D.減額 N.新作 C.額度取消 U.僅聯貸參貸比例變更
	 * 
	 * @param oldamt
	 *            原請額度
	 * @param curamt
	 *            現請額度
	 * 
	 * @param curcurr
	 *            現請額度幣別
	 * @param lnqtflag
	 *            科子目限額變更
	 * @param reclflag
	 *            循環額度變更
	 * @param sguflag
	 *            送註記
	 * @param lrptype
	 *            動用審核表－授信契約書型態，短期為１；中長期為2
	 * @param llnno
	 *            動用審核表-授信期間代碼 1. YYY/MM/DD～YYY/MM/DD 2. 自首動日起YY年XX個月 3. 其他
	 * 
	 * @param llnfdate
	 *            中長期授信期間代碼為1時為起始日期否則設為0001-01-01
	 * @param llnedate
	 *            中長期授信期間代碼為1時為終止日期否則設為0001-01-01
	 * @param llnmon
	 *            中長期授信期間代碼為2時會將年月轉換成月
	 * 
	 * @param lnuseno
	 *            1.YYY/MM/DD～YYY/MM/DD 2.自首次動用日起XX月 3.其他
	 * 
	 * @param usefmdt
	 *            動用期限代碼為1時為起始日期 動用期限代碼為2,3時 設為0001-01-01
	 * 
	 * @param useendt
	 *            動用期限代碼為1時 為終止日期 動用期限代碼為2,3時 設為0001-01-01
	 * 
	 * @param useftmn
	 *            動用期限代碼為2時 以月為單位,若為１年則為12
	 * 
	 * @param memo
	 *            其他敘作條件提示用語
	 * @param grantno
	 *            授權等級
	 * @param commborw
	 *            是否有共同借款人
	 * @param updater
	 *            資料修改人（行員代號）
	 * 
	 * @param reclchg
	 *            循環/不循環是否變更
	 * @param sguchg
	 *            送保註記是否變更
	 * @param gutflag
	 *            信保保證成數是否變更
	 * @param gutper
	 *            信保保證成數DECIMAL(3)
	 * @param llneflag
	 *            授信期間－終止日期是否變更(若非新作案件而此欄有變更者為Y)
	 * @param useeflag
	 *            動用期限－終止日期是否變更(若非新作案件而此欄有變更者為Y)
	 * @param lnnoflag
	 *            不計入授信項目代號
	 * @param unichgflag
	 *            聯貸案參貸金額是否變更(Y.是、N.否(若為新案則皆為Y))
	 * @param reflag
	 *            是否為銀行法或金控法利害關係人(Y.是、N.否(只要為其中之一即為Y))
	 * @param unionamt
	 *            聯貸額度(包含同業)
	 * @param shareamt
	 *            本行參貸總額度
	 * @param permittype
	 *            報核方式
	 * @param hideunion
	 *            是否為隱名參貸
	 * @param setdate
	 *            聯貸合約訂定日
	 * @param unionarea
	 *            國內聯貸或國際聯貸 A=國內　B=國際
	 * @param unionrole
	 *            聯行主辦(管理)行註記 Y=聯貸主辦行 L=聯貸案額度管理行 C=聯貸案擔保品管理行
	 * @param riskarea
	 *            本額度風險歸屬國別
	 * 
	 * @param existdate
	 *            OBU公司存續證明到期日
	 * @param feedate
	 *            OBU公司繳交年費證明到期日
	 * @param countrydt
	 *            OBU公司註冊國有效期
	 * @param crdttbl
	 *            信用評等等級
	 * @param mowtype
	 *            信用風險模型評等類別
	 * @param mowtbl1
	 *            信用風險模型評等等級
	 * @param syndipfd
	 *            聯貸信保註記
	 * @param cokind
	 *            合作業務種類 1:價金履約保證 2:合作外匯 Z:其他
	 * 
	 * @param cntrnom
	 *            合作業務母戶額度序號(12)
	 * @param rclno
	 *            合作業務子戶代收帳號(14)
	 * @param documentno
	 *            簽報書案號(20)
	 * @param crdtymd
	 *            舊評等日期
	 * @param crdtbr
	 *            舊評等分行
	 * @param mowymd
	 *            MOW 評等日期
	 * @param mowbr
	 *            MOW 評等分行
	 * @param controlcd
	 *            央行購住/空地/建屋貸款註記 屬於自然人購入住宅者(不含購入謄本登記主要用途為廠辦、商辦、店舖者)|1
	 *            屬於自然人/法人空地抵押貸款者(含擔保或副擔保)|2 屬於自然人/法人興建房屋貸款者 |3 非屬以上性質者 |4
	 * 
	 * @param duringflag
	 *            是否所有權取得日期在99/6/25以後或99/6/25後曾受央行管制客戶之增貸或轉貸案
	 * @param ltvrate
	 *            貸款成數
	 * @param locationcd
	 *            擔保品座落地區別 縣市別+區域別
	 * @param jcicmark
	 *            本次聯徵查詢名下有其他房貸資料 是 |Y 否 |N
	 * @param promise
	 *            承諾事項
	 * @param factType
	 *            額度控管種類
	 * @param commsno
	 *            是否有共用額度序號
	 * @param CHINAIVT
	 *            借款人有無赴大陸投資 CHAR(01) Y/N
	 * @param CHINACUR
	 *            赴大陸投資金額幣別 CHAR(03)
	 * @param CHINAAMT赴大陸投資金額
	 *            DECIMAL(15,2) 仟元
	 * @param SIGNAMT
	 *            經濟部投審會核准金額(TWD) DECIMAL(15,2) TWD 仟元
	 * @param NOISUREA
	 *            未送信保-原因 CHAR(02) 1　非中小企業 2　本行績優客戶 3　十足擔保 4　擔保率超過50% 5　營業未滿一年
	 *            6　外國人資本超過50% 7　單一法人所佔資本額超過50% 8　其他(請自行輸入)
	 * @param NOISUORT
	 *            未送信保-擔保率超過% DECIMAL(5,2) %
	 * @param NOISUDESP
	 *            未送信保-其他 VCHAR(4096) 經辦輸入描述
	 * @param PLUSREASON
	 *            屬央行控管對象者需要選擇一個理由 建物所有權取得日期在99/6/25以前 |1<br/>
	 *            建物登記用途無「住」字樣 |2 <br/>
	 *            擔保品非屬土地、建物 |3<br/>
	 *            非屬購屋/空地／建屋貸款 |4<br/>
	 *            其他 |5<br/>
	 * 
	 * @param RESIDENTIAL
	 *            是否屬興建住宅
	 * @param REG_PURPOSE
	 *            個人:「住」或「住商」無營業註記 法人: 建物謄本登記用途是否屬「住」類 (Y/N)
	 * @param EST_AMT
	 *            擔保品鑑價
	 * @param LAWVAL
	 *            擔保品估價
	 * @param COCOLL_FG
	 *            有無與其他額度共用擔保品
	 * @param PART_FUND
	 *            有無授權外核准得分批動用
	 * @param SUM_FACTAMT
	 *            共用同一擔保品總額度金額
	 * @param RESTRICT
	 *            是否為受限戶(Y/N)
	 * @param HP_HOUSE
	 *            是否為高價住宅(Y/N)
	 * @param PLAN_AREA
	 *            是否屬都市計畫劃定之區域(Y/N)
	 * @param P_USETYPE
	 *            都市計畫劃定之使用分區
	 * @param P_LOANUSE
	 *            借款用途
	 * @param COLL_CHAR
	 *            擔保品性質別
	 * @param KEEP_LAWVAL
	 *            保留一成估值(Y/N)
	 * @param PLUS_MEMO
	 *            非屬央行填報對象理由
	 * @param SITE3NO
	 *            擔保品座落 - 段
	 * @param SITE4NO
	 *            擔保品座落 - 村
	 * @param cmsOther
	 *            擔保品性質別3(其他)之說明
	 * @param loanPer
	 *            按轉讓發票金額 % 動用
	 * 
	 */
	void insertForInside(String custId, String dupNo, String cntrNo,
			String sDate, String caseType, String lnFlag, BigDecimal oldAmt,
			BigDecimal curAmt, String oldCurr, String curCurr, String lnqtFlag,
			String reclFlag, String sguFlag, String lrpType, String llnno,
			String llnfDate, String llnedate, int llnmon, String lnuseno,
			String usefmdt, String useendt, int useftmn, String memo,
			String grantno, String commborw, String updater, String reclchg,
			String sguchg, String gutflag, BigDecimal gutper, String llneflag,
			String useeflag, String lnnoflag, String unichgflag, String reflag,
			BigDecimal unionamt, BigDecimal shareamt, String permittype,
			String hideunion, String setdate, String unionarea,
			String unionrole, String riskarea, String existdate,
			String feedate, String countrydt, String crdttbl, String mowtype,
			String mowtbl1, String syndipfd, String cokind, String cntrnom,
			String rclno, String documentno, String crdtYmd, String crdtBr,
			String mowYmd, String mowbr, String controlcd, String duringFlag,
			BigDecimal ltvRate, String locationcd, String jcicMark,
			String promise, String factType, String commsno, String chinaivt,
			String chinacur, BigDecimal chinaamt, BigDecimal signamt,
			String noisurea, BigDecimal noisuort, String noisudesp,
			String plusreason, String residential, String buildYN,
			BigDecimal nowAMT, BigDecimal valueAMT, String commonYN,
			String shareCollYN, BigDecimal shareCollAmt, String isLimitCust,
			String isHighHouse, String houseYN, String houseType,
			String purposeType, String cmsType, String keepYN,
			String plusReasonMeMo, String sit3No, String sit4No,
			String cmsOther, BigDecimal loanPer, String cbHlChk,
			BigDecimal appAmt, String applyDate, BigDecimal riskFactors,
			BigDecimal riskActAmt, String reViewDate, BigDecimal reViewChg1,
			String unsecureFlag, String isEfin, String prodKind,
			String isNonSMEProjLoan, BigDecimal nonSMEProjLoanAmt,
			String exceptFlag, String itwCode, String inSmeFg,
			BigDecimal inSmeToAmt, BigDecimal inSmeCaAmt, String isHedge,
			BigDecimal enhanceAmt, String netSwft, BigDecimal netAmt,
			BigDecimal netAmtUnit, BigDecimal cgfRate, String cgfDate,
			String projClass, String experf_fg, String flaw_fg,
			BigDecimal flaw_amt, String isRescue, String rescueItem,
			BigDecimal rescueRate, String rescueIbDate, String isCbRefin,
			String rescueItemSub, String rescueNo, BigDecimal empCount,
			String isSmallBuss, String isSole, String soleType,
			String hasRegis, String isRevive, String revTarget,
			String revSubItem, String revPurpose, String cbRefinDt,
			String rescueIndustry, String rescueCity, String version,
			String realEstateLoanLimitReason, String isRescue_el,
			String rescue110, String rescueItemN, BigDecimal rescueRateN,
			String rescueItem_el, String rescueIbDateN, String rescueNoN,
			BigDecimal rescueNdfGutPercent, String isTurnoverDecreased,
			String rescueSn, String esggFg, String esggType, String esgsFg,
			String esgsType, String esgsUnre, String hLoanLimit_2,
			Date endDate, String itwCodeCoreBuss, BigDecimal cntrLgd,
			String stdAuth, String depositFg, String RESCUE_C_FG,
			Date RESCUE_C_SD, BigDecimal RESCUE_C_RT, Date RESCUE_C_ED,
			String isRenew, String isPayOldQuota, BigDecimal oldQuota,
			BigDecimal payOldAmt, String payOldAmtItem,
			String isMatchUnsoldHouseItem, String isSaleCase, Date lstDate,
			BigDecimal timeVal, 
			String socialFlag, String socialKind, String socialTa, String socialResp);

	/**
	 * 
	 * 查詢
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統時間
	 * @return
	 */
	Map<String, Object> findByKey(String custId, String dupNo, String cntrNo,
			String sDate);

	/**
	 * 查詢 onlntime is not null or bthtime is not null
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	public Map<String, Object> findByKey2(String custId, String dupNo,
			String cntrNo);

	/**
	 * 刪除 (SQL不加上ONLNTIME IS NULL，參考delByUniqueKey)
	 * 
	 * @param custId
	 *            身分證/統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統當天日期
	 */
	void delByUniqueKeyWithoutONLNTIME(String custId, String dupNo,
			String cntrNo, String sDate);

	/**
	 * 查詢 By 修改資料流程
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	public List<Map<String, Object>> findBy2105(String custId, String dupNo,
			String cntrNo);

	/**
	 * 新增 By 修改資料流程
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @param sdate
	 *            簽案日期
	 * @param unionamt
	 *            總金額
	 * @param updater
	 *            更新人員
	 * @param curAmt
	 *            現請額度
	 * @param documentNo
	 *            案號
	 * @param caseType
	 *            案件性質
	 * @param permitType
	 *            報核方式
	 * @param hideunion
	 *            是否為隱名參貸
	 * @param setDate
	 *            聯貸合約訂定日
	 * @param UArea
	 *            國內聯貸或國際聯貸
	 * @param unionRole
	 *            聯行主辦(管理)行註記
	 * @param riskArea
	 *            本額度風險歸屬國別
	 */

	public void insertBy2105(String custId, String dupNo, String cntrNo,
			String sdate, BigDecimal unionamt, String updater,
			BigDecimal curAmt, String documentNo, String caseType,
			String permitType, String hideunion, String setDate, String UArea,
			String unionRole, String riskArea);

	/**
	 * 查詢 By 修改資料流程 LNFLAG = U
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param sDate
	 * @return
	 */
	List<Map<String, Object>> findBy2105OnlyU(String custId, String dupNo,
			String cntrNo, String sDate);

	/**
	 * 刪除 By 修改資料流程 LNFLAG = U
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 */
	void delBy2105OnlyU(String custId, String dupNo, String cntrNo, String sDate);

	/**
	 * 查詢 By 修改資料流程 判斷ELF383是否有相同KEY的資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param sDate
	 * @return
	 */
	List<Map<String, Object>> findBy2105IsExist(String custId, String dupNo,
			String cntrNo, String sDate);

	/**
	 * J-109-0077_05097_B1013 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param isSmallBusArr
	 * @return
	 */
	public List<Map<String, Object>> findHasOtherIsSmallBus(String custId,
			String dupNo, String cntrNo, String[] isSmallBusArr);

	/**
	 * J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findLastIsSoleByCustId(String custId,
			String dupNo);

	public List<Map<String, Object>> findByCustIdCntrNo(String custId,
			String dupNo, String cntrNo);

	public int updateSole(String hasRegis, String custId, String dupNo,
			String cntrNo);

	/**
	 * J-110-0182_05097_B1001 Web e-Loan國內企金授信配合經濟部紓困貸款更改為非紓困案仍需持續補貼，修改相關程式。
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param isRescue
	 * @param rescueItem
	 * @return
	 */
	public List<Map<String, Object>> findByLastRescue(String custId,
			String dupNo, String cntrNo, String isRescue, String rescueItem);

	/**
	 * // 經濟部
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param isRescue
	 * @return
	 */
	public Map<String, Object> findByLastIsRescue(String custId, String dupNo,
			String cntrNo, String isRescue);

	/**
	 * J-110-0182_05097_B1001 Web e-Loan國內企金授信配合經濟部紓困貸款更改為非紓困案仍需持續補貼，修改相關程式。
	 * 
	 * @return
	 */
	public int updateIsRescue_elByIsRescue();

	/**
	 * J-110-0182_05097_B1001 Web e-Loan國內企金授信配合經濟部紓困貸款更改為非紓困案仍需持續補貼，修改相關程式。
	 * 
	 * @return
	 */
	public int updateRescueItem_elByRescueItem();

	/**
	 * J-110-0209紓困掛件文號新舊案不能相同
	 * 
	 * @param custId
	 * @param dupNo
	 * @param rescueNo
	 * @return
	 */
	public List<Map<String, Object>> findRescueByCsutIdAndRescueNo(
			String custId, String dupNo, String rescueNo);
}
