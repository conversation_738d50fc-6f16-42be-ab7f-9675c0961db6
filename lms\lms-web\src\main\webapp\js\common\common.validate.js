require(['validate'], function() {
  jQuery.validator.setDefaults({
    init: true,
    //onkeyup: false,
    showErrors: function(errorMap, errorList){
        var myBody = $('body');
        try {
            var o = $(this.currentElements);
            o.removeClass("data-error").removeClass("item-data-error");
            if ((o.data("qtip"))) 
                o.qtip("destroy");
        } 
        catch (e) {
            ilog.debug("common.validate destory error");
        }
        var _item;
        for (var i = 0; this.successList[i]; i++) {
            var successElement = $(this.successList[i]);
            if (successElement.data("qtip")) 
                successElement.qtip("destroy");
            successElement.parent().removeClass("data-error");
            successElement.removeClass("data-error");
            if (i == 0) {
                _item = successElement;
            }
        }
        var errorList = [];
        for (var i = 0; this.errorList[i]; i++) {
            var error = this.errorList[i];
            var self = $(error.element);
            if (self.data("qtip")) 
                self.qtip("destroy");
            jQuery.validator.addErrorClass(self, error.message);
            // self.doTimeout('destoryTip');
            if (self.is(':hidden')) {
                // this.errorList.length -= 1;
                self.removeClass("data-error").parent().removeClass("data-error");
                ilog.debug((self.attr('id') || self.attr('name')) + ' ' +
                error.message);
            }
            else {
                errorList.push(error);
                jQuery.validator.showTip(self, error.message);
            }
            if (!_item && i == 0) {
                _item = self;
            }
        }
        this.errorList = errorList;
        this.settings.init = false;
    }
});

$.extend($.validator.messages, {
    required: i18n.def['val.required'],
    remote: i18n.def['val.remote'],
    email: i18n.def['val.email'],
    url: i18n.def['val.url'],
    date: i18n.def['val.date'],
    dateISO: i18n.def['val.dateISO'],
    number: i18n.def['val.number'],
    digits: i18n.def['val.digits'],
    creditcard: i18n.def['val.creditcard'],
    equalTo: i18n.def['val.equalTo'],
    accept: i18n.def['val.accept'],
    maxlength: $.validator.format(i18n.def['val.maxlength']),
    minlength: $.validator.format(i18n.def['val.minlength']),
    rangelength: $.validator.format(i18n.def['val.rangelength']),
    range: $.validator.format(i18n.def['val.range']),
    max: $.validator.format(i18n.def['val.max']),
    min: $.validator.format(i18n.def['val.min']),
	phone: $.validator.format(i18n.def['val.phone']),
	alphanum: $.validator.format(i18n.def['val.alphanum']),
	date3: $.validator.format(i18n.def['val.date3']),
	date4: $.validator.format(i18n.def['val.date4']), // 2012.08.24 Mike Add
	halfword: $.validator.format(i18n.def['val.halfword']),	
	obuText: $.validator.format(i18n.def['val.obuText']),
	numText: $.validator.format(i18n.def['val.numText']),
	enText: $.validator.format(i18n.def['val.enText']),
	enText2: $.validator.format(i18n.def['val.enText'])
});
// add validate
jQuery.validator.addMethod("_twID", function(value, element){
    return this.optional(element) || CommonAPI.checkTWID(value, element);
}, i18n.def['val.twid']);
jQuery.validator.addMethod("_compNo", function(value, element){
    return this.optional(element) || CommonAPI.checkCompanyNo(value, element);
}, i18n.def['val.compNo']);
jQuery.validator.addMethod("_foreign", function(value, element){
    return this.optional(element) || CommonAPI.checkForeign(value, element);
}, i18n.def['val.foreign']);
jQuery.validator.addMethod("_requiredLength", function(value, element, param){
    var length = this.getLength($.trim(value), element);
    return this.optional(element) || (length == param);
}, $.validator.format(i18n.def['val.requiredLength']));

jQuery.validator.addMethod("_checkID", function(value, element){
    //return this.optional(element) ||
    //((value.length == 8) ? CommonAPI.checkCompanyNo(value, element) : (value.length == 10) ? CommonAPI.checkTWID(value, element) : false);
	var s = /^[A-Z]{2}Z\d{7}/;
	return this.optional(element) || (CommonAPI.checkTWID(value, element) ? true : 
		(CommonAPI.checkCompanyNo(value, element) ? true : (
				CommonAPI.checkForeign(value, element) ? true :
					s.test(value) )));
}, i18n.def['val.checkID']);
jQuery.validator.addMethod("_date", function(value, element){
    return this.optional(element) || CommonAPI.date(value, element);
}, i18n.def['val.date']);
jQuery.validator.addMethod("_date3", function(value, element){
    return this.optional(element) || CommonAPI.date3(value, element);
}, i18n.def['val.date3']);
// 2012.08.24 Mike Add Start
jQuery.validator.addMethod("_date4", function(value, element){
    return this.optional(element) || CommonAPI.date4(value, element);
}, i18n.def['val.date4']);
// 2012.08.24 Mike Add End
//2013.02.01 Mike Add Start
jQuery.validator.addMethod("_regExp", function(value, element){
    return this.optional(element) || CommonAPI.regExp(value, element);
}, "");
// 2013.02.01 Mike Add End
jQuery.validator.addMethod("_halfword", function(value, element, param){
	var halfWord = /^[\x00-\xff]+$/;
	return this.optional(element) || (halfWord.test(value));
}, i18n.def['val.halfword']);
jQuery.validator.addMethod("_twDate", function(value, element){
    return this.optional(element) || CommonAPI.twDate(value, element);
}, i18n.def['val.twdate']);
jQuery.validator.addMethod("ip", function(value, element){
    return this.optional(element) || CommonAPI.checkIPV4(value, element);
}, i18n.def['val.ip']);
jQuery.validator.addMethod("time", function(value, element){
    return this.optional(element) || CommonAPI.checkTime(value, element);
}, i18n.def['val.time']);
jQuery.validator.addMethod("_numeric", function(value, element){
    return this.optional(element) ||
    (function(){
    	if(value == "N.A."){
    		return true;
    	}
		value = value.replace(/,/g,"");
        var $this = $(element), positiveonly = ($this.attr("positiveonly") == "true"),_sf=($this.attr("subfix") ||""), integer = parseInt($this.attr("integer") || 14), fraction = parseInt($this.attr("fraction") || 0), intReg = (positiveonly ? "" : "-?") + "[0-9]{0," + integer + "}", reg = (fraction ? ("(" + intReg + "[.][0-9]{0," + fraction + "}|" + intReg + ")") : intReg)+ ((_sf)?"("+_sf+"?)":""), msg = i18n.def('val.numeric', [integer]) + (fraction ? i18n.def('val.numericFraction', [fraction]) : "");
		if ((new RegExp("^" + reg + "$")).test(value)) {
            $this.data("realErrorMsg", "");
            return true;
        }
        else {
            $this.data("realErrorMsg", msg);
            return false;
        }
    })();
}, " ");
jQuery.validator.addMethod("maxlengthC", function(value, element, param){
    var length = value.countLength(), maxlength = param*3;
    if(length > maxlength){
        var finalvalue=0,result=value.substr(0,param).countLength();
        for(var i = param;i < value.length;i++){
            var c = value.charCodeAt(i);
            result += (c<=126 && c>=32) ? 1 : 3 ; 
            if(result>maxlength){
                finalvalue = i;
                break;
            }else if(i==value.length-1){
                finalvalue = i+1;
            }
        }
        var e = $("input[maxlengthC].data-error,textarea[maxlengthC].data-error");
        if(e.length==0 || e[0]==element){
        	setTimeout(function(){
                try {createSelection(element, finalvalue, value.length);}catch(e){}
            }, 10);
        }
    }
    return this.optional(element) || (length <= maxlength);
}, $.validator.format(i18n.def['val.maxlength']));
jQuery.validator.addMethod("minlengthC", function(value, element, param){
    var length = value.countLength();
    return this.optional(element) || (length >= param*3);
}, $.validator.format(i18n.def['val.minlength']));
jQuery.validator.addMethod("maxlength", function(value, element, param){
	//使用common.js的val(),取得numeric欄位略掉撇節
    var val =(element.id==""?element.name:element.id) ? $("#"+(element.id==""?element.name:element.id)).val() :value;
	return this.optional(element) || this.getLength(val, element) <= param;
}, $.validator.format(i18n.def['val.maxlength']));
jQuery.validator.addMethod("_phone", function(value, element, param){
	var tel = /^(\({0,1})\d{0,}(\){0,1})\d{0,}(\-{0,1})\d{0,}(#{0,1})\d{0,}$/;
	return this.optional(element) || (tel.test(value));
}, $.validator.format(i18n.def['val.phone']));
jQuery.validator.addMethod("_alphanum", function(value, element, param){
	if (/trim/.test($(element).attr('class'))){
		value = $.trim(value);
	}
	return this.optional(element) || /^[a-z0-9\-]+$/i.test(value);
}, $.validator.format(i18n.def['val.alphanum']));

jQuery.validator.addMethod("obuText", function(value, element){
	//obu文字欄位僅能輸入英數字
	var p = /^[\u0000-\u007F]+$/;
    return this.optional(element) || (p.test(value));
}, i18n.def['val.obuText']);
jQuery.validator.addMethod("numText", function(value, element){
	//文字欄位僅能輸入數字
	var p = /^[0-9]+$/;
    return this.optional(element) || (p.test(value));
}, i18n.def['val.numText']);
jQuery.validator.addMethod("enText", function(value, element){
	//文字欄位僅能輸入英文
	var p = /^[a-zA-Z]+$/;
    return this.optional(element) || (p.test(value));
}, i18n.def['val.enText']);
jQuery.validator.addMethod("enText2", function(value, element){
	//文字欄位僅能輸入英文
	var p = /^[a-zA-Z\s]+$/;
    return this.optional(element) || (p.test(value));
}, i18n.def['val.enText']);
jQuery.validator.addMethod("numberAndEnAndChineseStyleSeparator", function(value, element){
	//文字欄位僅能輸入'數字''英文'和'、'
	var p = /^[a-zA-Z0-9、]+$/;
    return this.optional(element) || (p.test(value));
}, i18n.def['val.numberAndEnAndChineseStyleSeparator']);
jQuery.validator.addMethod("numberEnZh", function(value, element){
	//文字欄位僅能輸入中文英文數字
	var p = /^[\u4E00-\u9FFF\u3400-\u4DBF\uF900-\uFAFFa-zA-Z0-9]+$/;
	return this.optional(element) || (p.test(value));
}, i18n.def['val.numberEnZh']);
jQuery.validator.addMethod("numberEnZhSign", function(value, element){
	//文字欄位僅能輸入中文英文數字符號
	var p = /^[\u4E00-\u9FFF\u3400-\u4DBF\uF900-\uFAFFa-zA-Z0-9`~!@#$%^&*()\-_+=\[\]{}|;:'",.<>/?\\]+$/;
	return this.optional(element) || (p.test(value));
}, i18n.def['val.numberEnZhSign']);

jQuery.validator.addMethod("maxlength390", function(value, element, param){
	var length = value.length;
	var currentIsChinese = false;
	var totalLength = 0;
	var finalvalue = 0;
	//上傳中心主機390 欄位，中文字前後會加0E,0F，所以要特別計算
	for (var i = 0; i < length; i++) {
		if (i != 0) {
			var theChar = value.charAt(i)
			var currrentCharLen = theChar.countLength();
			if (currrentCharLen == 1) {
				if (currentIsChinese) {
					totalLength = totalLength + 1;
				}
				totalLength = totalLength + 1;
				currentIsChinese = false;
			} else {
				if (currentIsChinese) {
					totalLength = totalLength + 2;
				} else {
					totalLength = totalLength + 2 + 1;
				}
				currentIsChinese = true;
			}
		} else {
			var firstChar = value.charAt(0);

			if (firstChar.countLength() != 1) {
				currentIsChinese = true;
				totalLength = 1 + 2;
			} else {
				currentIsChinese = false;
				totalLength = 1;
			}
		}

		if ((i + 1) == length) {
			if (currentIsChinese) {
				totalLength = totalLength + 1;
			}
		}
		
		if (totalLength >  param && finalvalue == 0){
			finalvalue = i		
		}

	}

	if(totalLength > param){
		var e = $("input[maxlength390].data-error,textarea[maxlength390].data-error");
	    if(e.length==0 || e[0]==element){
	    	setTimeout(function(){
	            try {createSelection(element, finalvalue, value.length);}catch(e){}
	        }, 10);
	    }
	}
	
	
    return this.optional(element) || (totalLength <= param);
}, i18n.def['val.tooLong']);

jQuery.validator.addClassRules({
    twID: {
        maxlength: 10,
        _twID: true
    },
    name: {
        rangelength: [2, 40]
    },
    compNo: {
        maxlength: 8,
        _compNo: true
    },
    checkID: {
        maxlength: 10,
        _checkID: true
    },
    foreign: {
        maxlength: 10,
        _foreign: true
    },
    date: {
        maxlength: 10,
        _date: true
    },
    twDate: {
        maxlength: 9,
        _twDate: true
    },
    numeric: {
        // number: true,
        _numeric: true
    },
    branchNo: {
        _requiredLength: 3
    },
    dbuNo: {
        _requiredLength: 8
    },
    obuNo: {
        _requiredLength: 10
    },
	phone:{
		_phone: true
	},
	alphanum:{
		_alphanum:true
	},
	date3:{
		_date3:true
	},
	date4:{ // 2012.08.24 Mike Add
		_date4:true
	},
	regExp:{ // 2013.02.01 Mike Add
		_regExp:true
	},
	halfword:{
		_halfword:true
	}
});

//common validation
jQuery.commonVal = {
    checkMaxLength: function(length){
        return {
            validate: function(value, element){
                return value.length <= parseInt(length, 10);
            },
            message: $.validator.format(i18n.def['val.checkmaxlength'], [length])
        };
    }
};


$.extend(jQuery.validator, {
    fieldValidate: function(json, inject){
        jQuery.validator._fieldValidate = jQuery.extend(jQuery.validator._fieldValidate || {}, json || {});
        if (inject != false) {
            jQuery.validator.injectFieldVal();
        }
    },
    injectFieldVal: function(){
        for (var itemId in jQuery.validator._fieldValidate) {
            var vals = (jQuery.validator._fieldValidate[itemId].constructor == Array) ? jQuery.validator._fieldValidate[itemId] : [jQuery.validator._fieldValidate[itemId]];
            for (var i in vals) {
                jQuery.validator._injectVal(itemId, vals[i], i);
            }
        }
    },
    _injectVal: function(item, val, valName){
		var dyn = "dynval" + parseInt(Math.random()*2000,10) + valName;
        jQuery.validator.addMethod(dyn, function(value, element){
            return (val.alwaysCheck ? false : this.optional(element)) ||
            val.validate(value, element);
        }, val.message || "");
        $(item).addClass(dyn);
    },
    showTip: function(item, msg){
        msg = item.data("realErrorMsg") || msg;
        if (!msg || !item) 
            return;
        var myBody = $('body');
        item.qtip({
            content: {
                text: msg
            },
            position: {
                my: ((myBody.width() -
                item.width()) <
                350) ? 'bottom right' : 'bottom left',
                at: "top right"
            },
            show: {
                //event: false, // Don't specify a show event...
                ready: true // ... but show the tooltip when ready
            },
            hide: false, // Don't specify a hide event either!
            style: {
                classes: 'ui-tooltip-shadow ui-tooltip-plain',
                tip: {
                    corner: true, // Give it a speech bubble tip with automatic corner detection
                    border: 1 // Give our tips a border
                }
            }
        });
        var tipid = item.data("qtip").id;
        setTimeout(function(){
            try {
				item.qtip("destroy", true);
				$('#qtip-' + tipid).remove();
                //item.qtip("destroy");
                //$("#ui-tooltip-" + tipid).remove();
            } 
            catch (e) {
            }
        }, 3000);
        item.data("errorMsg", msg).attr("errormsg", msg).removeData("realErrorMsg");
    },
    
    addErrorClass: function(item, msg, css){
        switch (item.prop('tagName')) {
            case 'SELECT':
				//UPGRADE TODO：CES版本無SELECT下列內容，且$.browser.msie為jQuery 1.9版後刪除之用法，故先註解
 /*               if ($.browser.msie) {
                    if (item.parent().is(".errordiv")) {
                        item.parent().addClass(css || "data-error").data("errorMsg", msg);
                    }else {
                       //$("<span />").insertAfter(item.element).append(item.element).addClass(css || "data-error").addClass("errordiv");
                       //fix by fantasy 2012/09/14
                       var span = $("<span/>").addClass(css || "data-error").addClass("errordiv");
                       span.css({padding: '0 0 2px 0'})
                       item.wrap(span);
                    }
*/

            case 'TEXTAREA':
            case 'INPUT':
            	/*
            	var type = (item.attr("type") || '').toLowerCase();
            	if ('radio,checkbox'.indexOf(type) != -1){
            		var name = item.attr('name');
            		if (name) $('input[name='+name+']').addClass(css || "data-error");
            	}
            	*/
            	item.addClass(css || "data-error").data("errorMsg", msg);
        }
        
    }
});
$.fieldValidate = jQuery.validator.fieldValidate;


jQuery.fn.extend({
    __hide: jQuery.fn.hide,
    /**
     * 掛附validate plugin 後增加hide動作後將其hide 欄位 error class 移除
     * @param {Object} s
     */
    hide: function(s){
        if (!this.is("input,select,textarea")) {
            this.find(".data-error,.item-data-error").removeClass("data-error").removeClass("item-data-error");
        }
        return this.__hide(s);
    },
    /**
     * 增加itemVal
     * @param {function,[function,function.....]} vals
     */
    itemVal: function(vals, onError){
        var $this = $(this);
        if (!vals) {
            $this.trigger("blur.itemVal");
            return $this;
        }
        $this.unbind("blur.itemVal").unbind("focus.itemVal").bind("blur.itemVal", function(event){
            var $this = $(this).removeClass("item-data-error").removeData("errorMsg");
            if ($this.is(".data-error")) 
                return;
            vals = (vals.constructor == Array) ? vals : [vals];
            try {
                //有值才trigger val || allowemptydata 永遠檢查
                if ($this.attr("allowemptydata") == "false" || $this.val()) {
                    for (var i in vals) {
                        vals[i].call($this, event);
                    }
                }
            } 
            catch (e) {
                try {
                    jQuery.validator.addErrorClass($this, e, "item-data-error");
                    jQuery.validator.showTip($this, e);
                    
                    onError && $.isFunction(onError) && onError();
                } 
                catch (e1) {
                    alert(e1);
                }
                event.preventDefault();
            }
        }).bind("focus.itemVal", function(event){
            $(this).removeClass("item-data-error");//.removeData("errorMsg");
        });
        return $this;
    },
    /**
     * Ajax itemVal 功能
     * @param {JSON} s  ajax 參數
     * @param {function} onError 產生錯誤時執行之動作
     */
    ajax: function(s, onError){
        var $this = this;
        var tmp = s.success, errorTmp = s.error, errTmp = s.successError;
        $.ajax($.extend({}, s, {
            success: function(data, status){
                try {
                    tmp && tmp(data, status);
                } 
                catch (e) {
                    jQuery.validator.addErrorClass($this, e, "item-data-error");
                    jQuery.validator.showTip($this, e);
                    
                    onError && $.isFunction(onError) && onError();
                }
            },
            error: function(xhr){
                var errorMessage = getErrorMessage(xhr);
                if (errorMessage) {
                    jQuery.validator.addErrorClass($this, errorMessage, "item-data-error");
                    jQuery.validator.showTip($this, errorMessage);
                }
                errorTmp && errorTmp();
            },
            successError: function(data, status){
                errTmp && errTmp(data.status);
            }
        }));
    }
});
function createSelection(field, start, end) {      
    if( field.createTextRange ) {
        //IE
        var selRange = field.createTextRange();
        selRange.collapse(true);
        selRange.moveStart('character', start);
        selRange.moveEnd('character', end);
        selRange.select();
    } else if( field.setSelectionRange ) {
        //firefox
        field.setSelectionRange(start, end);
    } else if( field.selectionStart ) {
        field.selectionStart = start;
        field.selectionEnd = end;
    }
    field.focus();
}
  
});