/* 
 *LMS9131Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01F;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;


/**
 * <pre>
 * 授信簽報書審核層級整批修改
 * </pre>
 * 
 * @since 2013/01/24
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/24,<PERSON>,new
 *          </ul>
 */
/* Use MIS-RDB & R6 */
public interface LMS9131Service {
	public List<Map<String, Object>> findLNF447ByBranchIdSysType(String branchId,String systype);

	@SuppressWarnings("rawtypes")
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	@SuppressWarnings("rawtypes")
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid);
	@SuppressWarnings("rawtypes")
	public List<? extends GenericBean> findListByMainId(Class clazz, String mainId);

	public void deleteL120m01fs(List<L120M01F> l120m01fs, boolean isAll);

	public void saveL120m01fList(List<L120M01F> list);

	public void upLoadELF447(String[] oidlist) throws CapMessageException;

	void saveL120M01A(L120M01A l120m01a);

	public void ClearL120M01A(String[] oidlist,String flag) throws CapMessageException;
}
