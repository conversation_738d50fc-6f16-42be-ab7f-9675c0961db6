/* 
 * F101A01ADaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.F101A01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.F101A01A;
import com.mega.eloan.lms.model.F101M01A;

/**
 * <pre>
 * F101A01ADao dao impl.
 * </pre>
 * 
 * @since 2011/8/1
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/8/1,<PERSON><PERSON><PERSON> Wang,new</li>
 *          <li>2011/8/13,<PERSON><PERSON><PERSON>,add
 *          {@link F101A01ADao#deleteByMeta(F101M01A)}</li>
 *          </ul>
 */
@Repository
public class F101A01ADaoImpl extends LMSJpaDao<F101A01A, String> implements
		F101A01ADao {

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.dao.F101A01ADao#deleteByMeta(com.mega.eloan.ces
	 * .fss.model.F101M01A)
	 */
	public int deleteByMeta(F101M01A meta) {
		Query query = entityManager
				.createNamedQuery("f101a01a.deleteByMainIdAndPid");

		query.setParameter("mainId", meta.getMainId());
		query.setParameter("pid", meta.getUid());
		return query.executeUpdate();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.dao.F101A01ADao#findF101A01AByBranch(java.lang
	 * .String, java.lang.String, java.lang.String)
	 */
	public F101A01A findF101A01AByBranch(String mainId, String pid,
			String branch) {
		ISearch mySearch = createSearchTemplete();
		mySearch.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		mySearch.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		mySearch.addSearchModeParameters(SearchMode.EQUALS, "authUnit", branch);
		return findUniqueOrNone(mySearch);
	}

}
