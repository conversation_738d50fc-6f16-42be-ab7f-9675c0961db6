package com.mega.eloan.lms.batch.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01B;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

/**
 * <pre>
 *  SLMS-00014 企金個人基本資料補行業別(含海外個人戶)
 * </pre>
 * 
 * @since 2013/8/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/8/13,007625,new
 *          </ul>
 */
@Service("lmsbatch0001serviceimpl")
public class LmsBatch0001ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(LmsBatch0001ServiceImpl.class);

	@Resource
	L120M01ADao l120m01aDao;
	
	@Resource
	C120S01ADao c120s01aDao;

	@Resource
	L120S01BDao l120s01bDao;

	@Resource
	MisdbBASEService misdbBASEService;

	@Override
	public JSONObject execute(JSONObject json) {
		long t1 = System.currentTimeMillis();
		JSONObject result = new JSONObject();
		int errorCount = 0;

		try {
			List<L120S01B> l120s01bs = l120s01bDao.findAllNoNoteUp();
			LOGGER.info("L120S01B size : " + l120s01bs.size());
			for (L120S01B l120s01b : l120s01bs) {
				String custId = l120s01b.getCustId();
				String dupNo = l120s01b.getDupNo();
				Map<String, Object> map = misdbBASEService
						.findCustBussDataByIdAndDup(custId, dupNo);
				if (MapUtils.isNotEmpty(map)) {
					String busCode = Util.trim(map.get("BUSCD"));
					String cltype = Util.trim(map.get("CLTYPE"));
					String bussKind = Util.trim(map.get("BUSSKIND"));
					String ecoNm = Util.trim(map.get("ECONM"));
					String ecoNm07A = Util.trim(map.get("ecoNM07A"));
					if (!"".equals(busCode)) {
						String custClass = getCustClass(busCode, cltype);
						l120s01b.setBusCode(busCode);
						l120s01b.setBussKind(bussKind);
						l120s01b.setEcoNm(ecoNm);
						l120s01b.setEcoNm07A(ecoNm07A);
						l120s01b.setCustClass(custClass);
					}
					try {
						l120s01bDao.save(l120s01b);
					} catch (Exception e) {
						errorCount++;
						LOGGER.error("[execute] save L120S01B Exception!!", e);
					}

				}
			}
			
			boolean updateL120S01H = false;
			/*
			  海外消金
			  L120S01A - 借款人主檔
			  L120S01H - 個金基本資料檔
			 → 都有  busCode，要以 L120S01H為主
			 
			 svn 34204 重新引入時，同時更新 L120S01A, L120S01H
			     l120s01a.setBusCode(l120s01h.getBusCode());
			 【之前的 L120S01A 有可能不準】
			  
			 把 L120S01H 搬到 C120S01A
			 */
			if(updateL120S01H){
				List<String> mainId_list = get_docType2_typCd5_l120m01aMainId();
				int size_mainId_list = mainId_list.size();
				for(int i_mainId_list=0; i_mainId_list<size_mainId_list; i_mainId_list++){
					String l120m01a_mainId = mainId_list.get(i_mainId_list);
					LOGGER.trace("updateOverSeaCLS["+i_mainId_list+"/"+size_mainId_list+"]"+l120m01a_mainId);
					List<C120S01A> c120s01a_list = c120s01aDao.findByMainId(l120m01a_mainId);
					
					for (C120S01A c120s01a : c120s01a_list) {
						String custId = c120s01a.getCustId();
						String dupNo = c120s01a.getDupNo();				
						Map<String, Object> map = misdbBASEService
								.findCustBussDataByIdAndDup(custId, dupNo);
						if (MapUtils.isEmpty(map)) {
							continue;							
						}
						String busCode = Util.trim(map.get("BUSCD"));
						String cltype = Util.trim(map.get("CLTYPE"));
						String ecoNm = Util.trim(map.get("ECONM"));
						
						if (Util.isEmpty(busCode)) {
							continue;
						}
						
						String diff = "";
						String custClass = getCustClass(busCode, cltype);
						if(true){
							//判斷 isUpd
							if(Util.notEquals(c120s01a.getBusCode(), busCode)){
								diff += ("[busCode:"+c120s01a.getBusCode()+" , "+ busCode+"]");
							}
							if(Util.notEquals(c120s01a.getEcoNm(), ecoNm)){
								diff += ("[ecoNm:"+c120s01a.getEcoNm()+" , "+ ecoNm+"]");
							}
							if(Util.notEquals(c120s01a.getO_custClass(), custClass)){
								diff += ("[custClass:"+c120s01a.getO_custClass()+" , "+ custClass+"]");
							}
						}		
						//====================
						if(Util.isEmpty(diff)){
							continue;								
						}
						LOGGER.info("updateOverSeaCLS_diff["+l120m01a_mainId+" "+custId+"-"+dupNo+"]："+diff);
						try {
							c120s01a.setBusCode(busCode);						
							c120s01a.setEcoNm(ecoNm);
							c120s01a.setO_custClass(custClass);
							//==================
							c120s01aDao.save(c120s01a);
						} catch (Exception e) {
							errorCount++;
							LOGGER.error("[execute] save L120S01H Exception!!", e);
						}
					}
				}
			}
			

			if (errorCount > 0) {
				LOGGER.warn("LmsBatch0001ServiceImpl fail count: ", errorCount);
				result = WebBatchCode.RC_ERROR;
				result.element(WebBatchCode.P_RC_MSG, errorCount);
			} else {
				result = WebBatchCode.RC_SUCCESS;
				result.element(WebBatchCode.P_RESPONSE,
						"LmsBatch0001ServiceImpl執行成功！");
			}

		} catch (Exception ex) {
			LOGGER.error("[execute] LmsBatch0001ServiceImpl  Exception!!", ex);
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, ex.getLocalizedMessage()
					+ "==>" + ex.toString());
		} finally {
			LOGGER.info("RESULT={}", result.toString());
			LOGGER.info(StrUtils.concat("TOTAL_COST= ",
					(System.currentTimeMillis() - t1), " ms"));

		}

		return result;
	}

	private List<String> get_docType2_typCd5_l120m01aMainId(){
		List<String> r = new ArrayList<String>();
		ISearch search = l120m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docType", "2");
		search.addSearchModeParameters(SearchMode.EQUALS, "typCd", "5");
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120M01A> list = l120m01aDao.find(search);
		for(L120M01A l120m01a: list){
			r.add(l120m01a.getMainId());
		}	
		return r;
	}
	private String getCustClass(String buscd, String cltype) {
		String custClass = "";

		if (Util.equals(Util.getLeftStr(buscd, 2), "02")) {
			custClass = "5";
		} else if (Util.equals(Util.getLeftStr(buscd, 2), "03")) {
			custClass = "6";
		} else if (Util.equals(Util.getLeftStr(buscd, 2), "05")) {
			custClass = "7";
		} else if (Util.equals(Util.getLeftStr(buscd, 2), "06")) {
			custClass = "2";
		} else {
			if (Util.equals(cltype, "1")) {
				custClass = "1";
			} else if (Util.equals(cltype, "2")) {
				custClass = "8";
			} else {
				custClass = "4";
			}
		}

		if (Util.equals(buscd, "130300")) {
			custClass = "2";
		}

		return custClass;
	}

}
