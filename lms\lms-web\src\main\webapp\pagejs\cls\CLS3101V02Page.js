var _handler = "cls3101m01formhandler";
var text_grid_height = 120;
$(function(){	
	
	//default div
	var $gridview = $("#gridview").iGrid({
        handler: "cls3101gridhandler",
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        multiselect: false,  
        sortname: 'approveTime|custId' ,
       	sortorder: 'desc|asc' ,
        postData: {
            formAction: "queryView",
            docStatus : viewstatus	
        },
        colModel: [
            {
            	colHeader: "",name: 'oid', hidden: true
	        }, {
	        	colHeader: "",name: 'mainId', hidden: true
	        }, {
	        	colHeader: "",name: 'ownBrId', hidden: true
	        }, {
	            colHeader: i18n.cls3101v01["C310M01A.custId"], 
	            align: "left", width: 85, sortable: true, name: 'custId',
				onclick : openDoc, formatter : 'click'
	        }, {
	            colHeader: i18n.cls3101v01["C310M01A.custName"], 
	            align: "left", width: 100, sortable: true, name: 'custName'
	        }, {
	            colHeader: i18n.cls3101v01["C900S02E.cyc_mn"], 
	            align: "left", width: 100, sortable: false, name: 'cyc_mn'
	        }, {
	            colHeader: i18n.cls3101v01["C900S02E.text"], 
	            align: "left", width: 160, sortable: false, name: 'text'
	        }, {
	            colHeader: i18n.cls3101v01["C310M01A.chk_result"], 
	            align: "left", width: 80, sortable: true, name: 'chk_result'
	        }, {
	            colHeader: i18n.cls3101v01["C310M01A.chk_memo"], 
	            align: "left", width: 150, sortable: true, name: 'chk_memo'
	        }, {
	        	colHeader: i18n.cls3101v01["C310M01A.updater"], //異動人員
	            align: "left", width: 80, sortable: true, name: 'updater'
	        }, {
	        	colHeader: i18n.cls3101v01["C310M01A.approver"], //核准人員
	            align: "left", width: 80, sortable: true, name: 'approver'
	        }, {
	        	colHeader: i18n.cls3101v01["C310M01A.approveTime"], //核准時間
	            align: "left", width: 80, sortable: true, name: 'approveTime'
	        }
	     ],
		ondblClickRow : function(rowid){
			openDoc(null, null, $gridview.getRowData(rowid));
		}
    });
	

	
	function openDoc(cellvalue, options, rowObject) {
		//ilog.debug(rowObject);		
		$.form.submit({
			url : '../cls/cls3101m01/01',
			data : {
				'oid' : rowObject.oid,
				'mainOid' : rowObject.oid,
				'mainId' : rowObject.mainId,
				'mainDocStatus' : viewstatus
			},
			target : rowObject.oid
		});					
	};
	
	
    $("#buttonPanel").find("#btnView").click(function(){
    	var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
        	//
        }else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    });
    
    
    function chose_text(resultFrom_chose_custId){
		var my_dfd = $.Deferred();
		
		TextGrid.jqGrid("setGridParam", {
            postData: {
                'custId': resultFrom_chose_custId.custId
				,'dupNo': resultFrom_chose_custId.dupNo
            },
            search: true
        }).trigger("reloadGrid");

		$("#TextThickBox").thickbox({
	       title: (resultFrom_chose_custId.custId + "-" +resultFrom_chose_custId.dupNo), 
	       width: 600,height: (text_grid_height+160),align: "center",valign: "bottom",
           modal: false, i18n: i18n.def,
		   buttons: {
                "sure": function(){
					 var data = TextGrid.getSingleData();
                     if (data) {
						 $.thickbox.close();
						 //---
                    	 var refMainId = data.mainId;
        				 my_dfd.resolve($.extend(resultFrom_chose_custId, {'refMainId':refMainId} ));
                     }     	
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }	
		});	
		
		return my_dfd.promise();
	}
});
