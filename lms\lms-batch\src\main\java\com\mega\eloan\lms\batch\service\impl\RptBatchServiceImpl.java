package com.mega.eloan.lms.batch.service.impl;

import java.util.LinkedList;
import java.util.List;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.dao.LMSBATCHDao;
import com.mega.eloan.lms.dao.LMSRPTDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisRptDataService;
import com.mega.eloan.lms.model.LMSBATCH;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * batch 報表
 * 
 * 此批次判斷： 當 年,月,日  的「日」為 1 時(EX：每月1日、每季1日). 要 產製 的 報表
 * </pre>
 * 
 * @since 2013/01/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/14,Vector,new
 *          </ul>
 */
@Service("RptBatchServiceImpl")
public class RptBatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(RptBatchServiceImpl.class);

	private static final long serialVersionUID = 1L;

	@Resource
	LMSBATCHDao lmsbatchDao;

	@Resource
	LMSRPTDao lmsrptDao;

	@Resource
	DocFileService docFileService;

	@Resource
	DocFileDao docFileDao;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	BranchService branch;

	@Resource
	MisRptDataService misService;

	@Resource
	EloandbBASEService eloandbBaseService;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		String today = CapDate.getCurrentDate("yyyy-MM-dd");
		long t1 = System.currentTimeMillis();

		LOGGER.info(StrUtils.concat("[" + today
				+ "] The Batch for report is starting."));
		// 可以從json內取得參數
		if (LOGGER.isTraceEnabled()) {
			LOGGER.trace("傳入參數" + json.toString());
		}
		StringBuilder failItem = new StringBuilder();
		/** 將今日須更新的報表insert lmsbatch */
		List<LMSBATCH> needBatchs = new LinkedList<LMSBATCH>();
		int year = Util.parseInt(CapDate.getCurrentDate("yyyy"));
		int month = Util.parseInt(CapDate.getCurrentDate("MM"));
		int day = Util.parseInt(CapDate.getCurrentDate("dd"));
		int premonth = ((month - 1) == 0) ? 12 : month - 1;
		switch (day) {
		case 1:// 已敘作消金案件清單+優惠房貸明細表(授管用)+(季報表)已核准尚未撥款案件報表
				// 已敘作消金案件清單
			List<IBranch> branchList = branch.getAllBranch();
			for (IBranch branch : branchList) {
				LMSBATCH record = new LMSBATCH();

				record.setBgnDate(Util.parseDate(year + "-" + premonth + "-01"));
				record.setBranch(branch.getBrNo());
				record.setBthDate(CapDate.getCurrentTimestamp());
				record.setEndDate(Util.parseDate(year + "-" + premonth + "-"
						+ CapDate.getDayOfMonth(year, premonth)));
				record.setNowRpt("Y");
				record.setRptName("已敘作消金案件清單");
				record.setRptNo(UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單);
				record.setUpdater("system");
				record.setUpdateTime(CapDate.getCurrentTimestamp());
				needBatchs.add(record);
			}
			
			//優惠房貸明細表(授管用)
			//TODO
			
			// 已核准尚未撥款案件報表
			if (month == 1 || month == 4 || month == 7 || month == 10) {
				LMSBATCH record = new LMSBATCH();
				switch (month) {
				case 1:
					year--;
					month = 10;
					break;
				case 4:
					month = 1;
					break;
				case 7:
					month = 4;
					break;
				case 10:
					month = 7;
				}
				record.setBgnDate(Util.parseDate(year + "-" + month + "-01"));
				record.setBranch(UtilConstants.BankNo.授管處);
				record.setBthDate(CapDate.getCurrentTimestamp());
				record.setEndDate(Util.parseDate(year + "-" + (month + 2) + "-"
						+ CapDate.getDayOfMonth(year, (month + 2))));
				record.setNowRpt("Y");
				record.setRptName("已核准尚未撥款案件報表");
				record.setRptNo(UtilConstants.RPTREPORT.DOCTYPE2.已核准尚未撥款案件報表);
				record.setUpdater("system");
				record.setUpdateTime(CapDate.getCurrentTimestamp());
				needBatchs.add(record);
			}
			break;
		}

		lmsbatchDao.save(needBatchs);
		LOGGER.info("[" + today
				+ "]The Batch for report is finish.Cost time : "
				+ (System.currentTimeMillis() - t1));
		// **********************************************************************
		// 回傳範例(需要回傳結果值或錯誤資訊
		// **********************************************************************
		// 回傳請組JSONObject ==> {"rc":xx , "rcmsg":"xxxx", "response":"xxxxx" }
		// boolean isSuccess = true;
		// JSONObject result = null;
		// if (isSuccess) {
		// result = WebBatchCode.RC_SUCCESS;
		// result.element(WebBatchCode.P_RESPONSE, "需要回傳的執行結果資料");
		//
		// } else {
		// result = WebBatchCode.RC_ERROR;
		// result.element(WebBatchCode.P_RC_MSG, "這是錯誤資訊!");
		// }
		// return result;

		JSONObject mag = new JSONObject();
		if (Util.isEmpty(failItem.toString())) {
			mag = WebBatchCode.RC_SUCCESS;
		} else {
			mag = WebBatchCode.RC_ERROR;
			mag.element(WebBatchCode.P_RC_MSG, failItem.toString());
		}
		return mag;
	}
}
