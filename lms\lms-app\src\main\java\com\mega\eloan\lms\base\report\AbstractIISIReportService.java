/*
 * AbstractReportService.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.report;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.Vector;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.CollectionUtils;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Area;
import com.inet.report.Element;
import com.inet.report.Engine;
import com.inet.report.Field;
import com.inet.report.FieldElement;
import com.inet.report.FormulaField;
import com.inet.report.ReportException;
import com.inet.report.Section;
import com.inet.report.TextInterpretationProperties;
import com.lowagie.text.pdf.BaseFont;
import com.mega.eloan.common.service.SysBaseUrlService;
import com.mega.eloan.lms.base.constants.SimplePDFConstant;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.AbstractCapService;

/**
 * <p>
 * eloan中報表服務的抽象類別，提供產生報表，以及取得報表頁數的方法。
 * </p>
 * 
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/6/13,Sunkist,new
 *          <li>2011/8/15,RodesChen,修改可放入多個DataBean
 *          <li>2011/12/13,Sunkist,由PageParameter傳入要產出的報表類型及ContentType
 *          <li>2012/2/2,iristu,增加取得報表的語系套表
 *          <li>2013/2/8,EL07623,將ckeditor裡檔案的baseUrl修改為sysBaseUrlService
 *          </ul>
 */
public abstract class AbstractIISIReportService extends AbstractCapService
		implements IReportService, SimplePDFConstant {

	protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

	@Autowired
	protected SysBaseUrlService sysBaseUrlService;

	public final static String REPORT_SUFFIX = ".rpt";

	public static BaseFont baseFont;

	@Autowired
	private HttpServletRequest req;

	static {
		try {
			baseFont = BaseFont.createFont("MHei-Medium", "UniCNS-UCS2-H",
					BaseFont.NOT_EMBEDDED);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * Gets the report parameter.
	 * 
	 * @param params
	 *            PageParameters
	 * @param reportData
	 *            ReportData
	 * @param engine
	 *            i-net Report Engine
	 * @return the report parameter
	 */
	public abstract ReportData getReportParameter(PageParameters params,
			ReportData reportData, Engine engine);

	/**
	 * 依user's locale取得報表套表的URL, 若套表無locale設定，則取default
	 * 
	 * @return URL
	 */
	protected URL getReportFile() {
		// 指定拉好的套表檔名稱。
		if (getReportDefinition() == null) {
			return null;
		}
		StringBuffer reportDefinition = new StringBuffer(getReportDefinition());
		reportDefinition.append('_')
				.append(LocaleContextHolder.getLocale().toString())
				.append(REPORT_SUFFIX);
		URL urlRpt = Thread.currentThread().getContextClassLoader()
				.getResource(reportDefinition.toString());
		if (urlRpt == null) {
			reportDefinition = new StringBuffer(getReportDefinition());
			reportDefinition.append('_').append(Locale.getDefault().toString())
					.append(REPORT_SUFFIX);
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(reportDefinition.toString());
		}
		return urlRpt;
	}

	/**
	 * 依user's locale取得報表套表的URL, 若套表無locale設定，則取default
	 * 
	 * @return URL
	 */
	protected URL getReportFile(PageParameters params) {
		// 指定拉好的套表檔名稱。
		if (getReportDefinition(params) == null) {
			return getReportFile();
		}
		StringBuffer reportDefinition = new StringBuffer(
				getReportDefinition(params));
		reportDefinition.append('_')
				.append(LocaleContextHolder.getLocale().toString())
				.append(REPORT_SUFFIX);
		URL urlRpt = Thread.currentThread().getContextClassLoader()
				.getResource(reportDefinition.toString());
		if (urlRpt == null) {
			reportDefinition = new StringBuffer(getReportDefinition(params));
			reportDefinition.append('_').append(Locale.getDefault().toString())
					.append(REPORT_SUFFIX);
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(reportDefinition.toString());
		}
		return urlRpt;
	}

	/**
	 * Override getReportDefinition add Params
	 * 
	 * @param params
	 * @return
	 */
	public String getReportDefinition(PageParameters params) {
		return null;
	}

	/**
	 * Gets the report definition.
	 * 
	 * @return the report definition
	 */
	public abstract String getReportDefinition();

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.mega.eloan.sample.report.IReportService#generateReport()
	 */
	public OutputStream generateReport() throws CapException {
		return generateReport(null);
	}

	@Override
	public OutputStream generateReport(PageParameters params) throws CapException {
		ByteArrayOutputStream out = new ByteArrayOutputStream();
		try {
			Engine engine = prepareEngine(params);
			int pageCount = engine.getPageCount();
			params.put(CHAPTER_TOTAL_PAGE, pageCount);
			for (int i = 1; i <= pageCount; i++) {
				out.write(engine.getPageData(i));
			}
		} catch (Exception e) {
			if (e.getCause() != null) {
				throw new CapException(e.getCause(), e.getClass());
			} else {
				throw new CapException(e, e.getClass());
			}
		} finally {
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
					if (LOGGER.isErrorEnabled()) {
						LOGGER.error(e.getMessage());
					}
				}
			}
		}
		return out;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.mega.eloan.sample.report.IReportService#getReportPageCount()
	 */
	public int getReportPageCount() throws CapException {
		return getReportPageCount(null);
	}

	@Override
	public int getReportPageCount(PageParameters params)
			throws CapException {
		if (params != null && params.containsKey(CHAPTER_TOTAL_PAGE)) {
			return params.getInt(CHAPTER_TOTAL_PAGE, 0);
		}
		int page = 0;
		try {
			Engine engine = prepareEngine(params);
			page = engine.getPageCount();
		} catch (Exception e) {
			if (e.getCause() != null) {
				throw new CapException(e.getCause(), e.getClass());
			} else {
				throw new CapException(e, e.getClass());
			}
		}
		return page;
	}

	/**
	 * 產生com.inet.report.Engine。
	 * 
	 * @param params
	 *            PageParameters
	 * @return Engine
	 * @throws com.inet.report.ReportException
	 */
	
	private Engine prepareEngine(PageParameters params)
			throws ReportException {
		// TO-DO create a new report.
		Engine engine = new Engine(params.getString("exportType",
				Engine.EXPORT_PDF));
		Engine.setLogLevel(Engine.LOG_DEBUG);
		engine.setReportFile(getReportFile(params));

		// 設定報表參數。
		ReportData rpData = new ReportData();
		if (params != null) {
			rpData = getReportParameter(params, rpData, engine);
		}
		Map<String, Object> para = rpData.getFields();
		Map<Integer, List<List<String>>> details = rpData.getDetails();
		if (para != null && !para.isEmpty()) {
			Iterator<String> i = para.keySet().iterator();
			while (i.hasNext()) {
				String key = i.next();
				Object value = para.get(key);
				// 設定報表的資料來源。
				if (value instanceof IDataBean) {
					engine.setData(((IDataBean) value).getColumns(),
							((IDataBean) value).getData());
				} else {
					engine.setPrompt(key, String.valueOf(para.get(key)));
				}
			}
		}
		// 取得所有details(Report裡的詳細資料區)
		Area area = engine.getArea(Engine.DETAILS);

		int sectionCount = area.getSectionCount();
		// sectionCount = 1; // 暫時只可使用一個report
		Vector<String> columns = getReportDetailColumns(params, engine);
		if (CollectionUtils.isEmpty(columns)) {
			columns = new Vector<String>();
			for (int i = 0; i < sectionCount; i++) {
				Section section = area.getSection(i);
				Element[] elements = section.getElements();
				// String[] columns = new String[elements.length];
				for (int j = 0; j < elements.length; j++) {
					Element e = elements[j];
					if (e instanceof FieldElement) {
						if (e.getField() instanceof FormulaField) {
							// 公式欄位:取得該欄位所需的CommonBean.Field
							processFormulaFieldColumns(e.getField(), columns);
						} else {
							// 一般的CommonBean.Field
							columns.add(e.getField().getName());
						}
					}
				}
			}
		}
		Vector<Vector<String>> datas = null;
		Set<Integer> keys = details.keySet();
		datas = new Vector<Vector<String>>();
		for (Integer ikey : keys) {
			List<List<String>> detail = details.get(ikey);
			// for (int i = 0; i < sectionCount; i++) {
			for (List<String> slist : detail) {
				datas.add(new Vector<String>(slist));
			}
		}
		engine = getSubReportData(params, engine);
		engine.setData(columns, datas, true);

		// 20150602,EL07623, set all Elements ckeditor baseUrl
		for (int i = 0; i < sectionCount; i++) {
			Section section = area.getSection(i);
			Element[] elements = section.getElements();			
			for (int j = 0; j < elements.length; j++) {
				Element e = (Element) elements[j];
				if (e instanceof FieldElement) {
					FieldElement fElem = (FieldElement) e;
					if (fElem.getTextInterpretation() == TextInterpretationProperties.HTML_TEXT
							|| fElem.getTextInterpretation() == TextInterpretationProperties.ADVANCED_HTML_TEXT) {
						// 設定ckeditor裡檔案的baseUrl
						fElem.setBaseUrl(getServerBaseURL().lastIndexOf(":") > 7 ? getServerBaseURL()
								: sysBaseUrlService.getServerBaseUrl());
						LOGGER.debug(
								"field:{} setBaseUrl:{}",
								e.getField().getName(),
								getServerBaseURL().lastIndexOf(":") > 7 ? getServerBaseURL()
										: sysBaseUrlService.getServerBaseUrl());
					}
				}
			}
		}
		try {
			engine.execute();
		} catch (Exception e) {
			LOGGER.error(e.getMessage(), e);
		}
		return engine;
	}

	/**
	 * Process the SubReport
	 * 
	 * @param params
	 *            PageParameters
	 * @param engine
	 *            Engine
	 * @return the report Engine
	 */
	public Engine getSubReportData(PageParameters params, Engine engine) {
		return engine;
	}

	/**
	 * 增加空白欄位for每頁填滿固定筆數
	 * 
	 * @param details
	 *            資料已完成
	 * @param columnCounts
	 *            欄位總數
	 * @param pageRows
	 *            每頁固定筆數
	 * @return details
	 */
	protected List<List<String>> addEmptyFields(List<List<String>> details,
			int columnCounts, int pageRows) {
		List<String> emptyList = new ArrayList<String>();
		for (int i = 0; i < columnCounts; i++) {
			emptyList.add(CapConstants.EMPTY_STRING);
		}
		for (int i = 0; i < details.size() % pageRows; i++) {
			details.add(emptyList);
		}
		return details;
	}

	/**
	 * Gets the report (CommonBean) details columns.
	 * 
	 * @return the report details columns
	 */
	protected Vector<String> getReportDetailColumns(PageParameters params,
			Engine engine) {
		return new Vector<String>();
	}

	private void processFormulaFieldColumns(Field field, Vector<String> columns) {
		if (field instanceof FormulaField) {
			FormulaField ff = (FormulaField) field;
			ff.getRealReferencedObjectCount();
			Object[] refFields = ff.getReferencedObjects();
			if (refFields != null) {
				List<Field> fields = new ArrayList<Field>();
				for (Object ob : refFields) {
					if (ob instanceof FormulaField)
						continue;
					else
						fields.add((Field) ob);
				}
				for (Object ob : refFields) {
					if (ob instanceof FormulaField)
						fields.add((Field) ob);
				}
				for (Field tmp : fields) {
					if (tmp instanceof FormulaField)
						processFormulaFieldColumns(tmp, columns);
					else if (tmp instanceof Field)
						columns.add(tmp.getName());
				}
			}
		}
	}

	protected String getServerBaseURL() {
		return req.getRequestURL().substring(0, req.getRequestURL().indexOf("app"));
	}

	/**
	 * ReportData
	 */
	protected class ReportData {
		private Map<String, Object> fields;
		private Map<Integer, List<List<String>>> details;
		private boolean nulltoSpace = false;
		private int index = 0;

		/**
		 * 建構子
		 */
		private ReportData() {
			fields = new HashMap<String, Object>();
			details = new HashMap<Integer, List<List<String>>>();
		}

		public void setField(String name, String value) {
			if (nulltoSpace && value == null)
				value = CapConstants.EMPTY_STRING;
			fields.put(name, value);
		}

		public void setField(String name, int value) {
			fields.put(name, value);
		}

		public void setAll(Map<String, Object> map) {
			fields.putAll(map);
		}

		public void addDetail(int order, List<List<String>> list) {
			if (list != null) {
				details.put(order, list);
			}
		}

		public void addDetail(List<List<String>> list) {
			addDetail(++index, list);
		}

		public Map<String, Object> getFields() {
			return fields;
		}

		@SuppressWarnings("unchecked")
		public <T> T getField(String name) {
			return (T) fields.get(name);
		}

		private Map<Integer, List<List<String>>> getDetails() {
			return details;
		}

		public boolean isNulltoSpace() {
			return nulltoSpace;
		}

		public void setNulltoSpace(boolean nulltoSpace) {
			this.nulltoSpace = nulltoSpace;
		}

	}
}
