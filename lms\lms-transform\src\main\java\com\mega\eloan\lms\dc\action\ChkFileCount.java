/**
 * 
 */
package com.mega.eloan.lms.dc.action;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.HashMap;

import java.util.Map;

import com.mega.eloan.lms.dc.conf.ConfigData;
import com.mega.eloan.lms.dc.conf.MainConfig;
import com.mega.eloan.lms.dc.util.RptUtil;
import com.mega.eloan.lms.dc.util.TextDefine;

/**
 * 用來計算各流程結束後的資料量
 * 
 * <AUTHOR>
 * @since 2013/01/29
 * 
 * 
 */
public class ChkFileCount extends BaseAction {

	private static String home_path = "";
	private static String test_Date = "";
	private static String lmsView = "";
	private static String clsView = "";
	private static String kind = "";// 設定企金LMS或個金資料夾
	private static String db2data_path="";

	/**
	 * 計算產生的目錄及文字檔數量
	 * 
	 * @param args
	 *            [0]=LMS/CLS分辨企金個金
	 * @param args
	 *            [1]=E→產生export的數量 P→產生parser後各分行資料 M→計算MERGE最後的筆數
	 */
	public static void main(String[] args) {
		ConfigData config = MainConfig.getInstance().getConfig();
		test_Date = config.getTODAY();
		db2data_path =  config.getDC_ROOT() + "/"+"load_db2";
		home_path = config.getDC_ROOT() + config.getHOME_NAME();
		// 2013-03-26 改為讀取設定檔
		lmsView = config.getLMSViewName();
		clsView = config.getCLSViewName();
		kind = args[0];
		try {
			File files = new File(home_path + "/" + test_Date + "/" + kind);
			String[] brnolist = files.list();
			if (args[1].equalsIgnoreCase("E")) {
				countAfterExport(brnolist);
			} else if (args[1].equalsIgnoreCase("P")) {
				countAfterParser(brnolist);
			} else if (args[1].equalsIgnoreCase("T")){
				getAfterTruncateDataCount();
			}else {
				throw new Exception("參數不正確");
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 計算在Export後產出多少dxl
	 */
	private static void countAfterExport(String[] brnolist) {
		ChkFileCount chkFileCount = new ChkFileCount();

		// Iterator<String> itor = conf.getKeys();
		String[] view = null;
		if (kind.equalsIgnoreCase("LMS")) {
			view = lmsView.split(TextDefine.SYMBOL_SEMICOLON);
		} else if (kind.equalsIgnoreCase("CLS")) {
			view = clsView.split(TextDefine.SYMBOL_SEMICOLON);
		}
		/*
		 * while (itor.hasNext()) { String key =
		 * StringUtils.trimToEmpty(itor.next()); for (String tmp : view) {
		 * chkFileCount.getFormCount(key, tmp); } }
		 */
		for (String brno : brnolist) {
			for (String tmp : view) {
				chkFileCount.getFormCount(brno, tmp);
			}
		}
	}

	/**
	 * 計算在Parser後產出多少dxl
	 */
	private static void countAfterParser(String[] brnolist) {
		ChkFileCount chkFileCount = new ChkFileCount();
		// Iterator<String> itor = conf.getKeys();
		String[] view = null;
		if (kind.equalsIgnoreCase("LMS")) {
			view = lmsView.split(TextDefine.SYMBOL_SEMICOLON);
		} else if (kind.equalsIgnoreCase("CLS")) {
			view = clsView.split(TextDefine.SYMBOL_SEMICOLON);
		}
		/*
		 * while (itor.hasNext()) { String key =
		 * StringUtils.trimToEmpty(itor.next()); for (String tmp : view) {
		 * chkFileCount.getDb2FileDataCount(key, tmp); } }
		 */
		for (String brno : brnolist) {
			for (String tmp : view) {
				chkFileCount.getDb2FileDataCount(brno, tmp);
			}
		}
	}

	/**
	 * 取回view下所有的dxl的unid 清單(用來放在export中跳過簽報書用)
	 * 
	 * @param brno
	 *            分行別
	 * @return
	 * 
	 * 
	 *         可用下面程式來跳過簽報書 if(hm201.isEmpty()) { Test test = new Test(); hm201
	 *         = test.getDxlList(strBrn, "VLMSDB201B"); key201Set =
	 *         hm201.keySet(); }
	 * 
	 * 
	 */
	public Map<String, String> getDxlList(String brno, String viewName) {
		Map<String, String> map = new HashMap<String, String>();

		File files = new File(home_path + "/" + test_Date + "/" + kind + "/"
				+ "/" + brno + "/" + viewName);
		String[] filelist = files.list();
		this.logger.debug("length=" + filelist.length);
		for (int i = 0; i < filelist.length; i++) {
			String tmp = new File(filelist[i]).getName();
			if (tmp.length() > 10)
				tmp = tmp.substring(11, tmp.length() - 4);
			map.put(tmp, tmp);
		}

		return map;
	}

	/**
	 * 統計view下所有的Export後Form的個數
	 * 
	 * @param brno
	 *            分行別
	 * @return
	 */
	private void getFormCount(String brno, String viewName) {
		StringBuffer sb = new StringBuffer();
		try {
			File files = new File(home_path + File.separator + test_Date
					+ File.separator + kind + File.separator + brno
					+ File.separator + viewName);
			String[] filelist = files.list();
			if (filelist != null && filelist.length > 0) {
				Arrays.sort(filelist);
				this.logger.debug("========以下為分行" + brno + "的" + viewName
						+ " 筆數資訊========");
				/*
				 * sb.append(fillL("Export",10)).append("=").append(fillL(viewName
				 * ,
				 * 12)).append("=").append(fillR(filelist.length,7)).append("=個"
				 * ); System.out.println(sb.toString());
				 */sb.setLength(0);
				String tmpF = "";
				int count = 0;
				for (int i = 0; i < filelist.length; i++) {
					String tmp = new File(filelist[i]).getName();
					if (!tmp.endsWith(".dxl")) {
						continue;
					}
					if (tmp.length() > 10)
						tmp = tmp.substring(0, 10);
					if (count != 0 && !tmp.equalsIgnoreCase(tmpF)) {
						sb.append(RptUtil.fillL("", 10)).append("=")
								.append(RptUtil.fillL(tmpF, 12)).append("=")
								.append(RptUtil.fillR(count, 7)).append("筆");
						this.logger.debug(sb.toString());
						sb.setLength(0);
						count = 0;
					}
					count++;
					tmpF = tmp;
				}
				if (count != 0) {
					sb.append(RptUtil.fillL("", 10)).append("=")
							.append(RptUtil.fillL(tmpF, 12)).append("=")
							.append(RptUtil.fillR(count, 7)).append("筆");
					this.logger.debug(sb.toString());
				}
				sb.setLength(0);
				count = 0;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 統計Parser後產生的檔案中共有多少筆資料
	 * 
	 * @param brno
	 *            分行別
	 * @return
	 */
	private void getDb2FileDataCount(String brno, String viewName) {
		StringBuffer sb = new StringBuffer();
		String target = home_path + "/" + test_Date + "/" + kind + "/" + brno
				+ "/" + viewName + "/TEXT/";
		File files = new File(target);
		String[] filelist = files.list();
		if (filelist != null && filelist.length > 0) {
			Arrays.sort(filelist);
			// System.out.println("========以下為分行" + brno + "的"+ viewName
			// +" 筆數資訊========");
			sb.append("分行").append(brno).append("的").append(viewName)
					.append("=").append(RptUtil.fillL(viewName, 12))
					.append("=").append(RptUtil.fillR(filelist.length, 7))
					.append("=個");
			this.logger.debug(sb.toString());
			sb.setLength(0);
			int count = 0;
			for (int i = 0; i < filelist.length; i++) {
				String tmp = new File(filelist[i]).getName();
				count = getFileLen(target + tmp);
				sb.append(RptUtil.fillL("", 18)).append("=")
						.append(RptUtil.fillL(tmp, 28)).append("=")
						.append(RptUtil.fillR(count, 7));
				this.logger.debug(sb.toString());
				sb.setLength(0);
				count = 0;
			}
		}
		// 算files
		target = home_path + "/" + test_Date + "/" + kind + "/" + brno + "/"
				+ viewName + "/";
		files = new File(target + "FILES");
		filelist = files.list();
		if (filelist != null && filelist.length > 0) {
			sb.append(RptUtil.fillL("", 18)).append("=")
					.append(RptUtil.fillL("FILES", 26)).append("=")
					.append(RptUtil.fillR(filelist.length, 7));
			this.logger.debug(sb.toString());
			sb.setLength(0);
		}
		// 算html
		target = home_path + "/" + test_Date + "/" + kind + "/" + brno + "/"
				+ viewName + "/";
		files = new File(target + "HTML");
		filelist = files.list();
		if (filelist != null && filelist.length > 0) {
			sb.append(RptUtil.fillL("", 18)).append("=")
					.append(RptUtil.fillL("HTML", 26)).append("=")
					.append(RptUtil.fillR(filelist.length, 7));
			this.logger.debug(sb.toString());
			sb.setLength(0);
		}
	}
	
	
	
	/**
	 * 統計Truncate後產生的檔案中共有多少筆資料
	 * 
	 * @param brno
	 *            分行別
	 * @return
	 */
	private static void getAfterTruncateDataCount() {
		StringBuffer sb = new StringBuffer();
		String target =  db2data_path + "/" + test_Date + "/" + kind +  "/data/";
		//System.out.println("target==="+target);
		File files = new File(target);
		String[] filelist = files.list();
		if (filelist != null && filelist.length > 0) {
			Arrays.sort(filelist);

			sb.setLength(0);
			int count = 0;
			for (int i = 0; i < filelist.length; i++) {
				String tmp = new File(filelist[i]).getName();
				if (!tmp.startsWith("LMS.")) {
					continue;
				}
				count = getFileLen(target + tmp);
				sb.append(RptUtil.fillL(tmp, 28)).append("=")
						.append(RptUtil.fillR(count, 7));
				//System.out.println(sb.toString());
				sb.setLength(0);
				count = 0;
			}
		}

	}
	

	/**
	 * 取得指定檔案的行數
	 * 
	 * @param filepath
	 * @return
	 */
	private static int getFileLen(String filepath) {
		int count = 0;
		try {
			BufferedReader br = new BufferedReader(new InputStreamReader(
					new FileInputStream(filepath)));
			while (br.readLine() != null) {
				count++;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count;
	}

}
