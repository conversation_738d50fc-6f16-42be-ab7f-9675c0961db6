/* 
 *MisELF503Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;

/**
 * <pre>
 * 利率條件MIS.ELF503
 * </pre>
 * 
 * @since 2013/01/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/14,<PERSON><PERSON><PERSON>,new
 *          </ul>
 */
public interface MisELF503Service {

	/**
	 * 新增
	 * 
	 * @param dataList
	 *            <pre>
	 *   object[] content
	 *   	ELF503_CUSTID  統一編號
	 * 	ELF503_DUPNO 重覆序號
	 * 	ELF503_SDATE 核准日期
	 * 	ELF503_DOCUMENTNO 案號
	 * 	ELF503_CNTRNO 額度序號
	 * 	ELF503_INDEXNO 額度明細表登錄利率INDEX
	 * 	ELF503_CURR 幣別
	 * 	ELF503_LOANTP 科目
	 * 	ELF503_SECNO 段
	 * 	ELF503_SECKIND 適用期間種類
	 * 
	 * 	ELF503_MONBGN 起月
	 * 	ELF503_MONEND 迄月
	 * 	ELF503_DATEBGN 起日
	 * 	ELF503_DATEEND 迄日
	 * 	ELF503_RATEKIND 利率基礎
	 * 	ELF503_CRSAMEFG 借款同天期
	 * 	ELF503_CRPTR 自訂利率參考指標
	 * 	ELF503_CRPRIOD 自訂利率天期
	 * 	ELF503_CRURKIND U01/U02市場利率種類
	 * 	ELF503_CRURSAFG U01/U02借款同天期
	 * 
	 * 	ELF503_CRURRATE U01/U02市場利率代碼
	 * 	ELF503_CRUTAXFG U01/U02稅賦負擔
	 * 	ELF503_CRUTAXRT U01/U02內含賦稅值
	 * 	ELF503_ADJFG 加減年率
	 * 	ELF503_ADJRT 加減年率%
	 * 	ELF503_RATEOTH 其他
	 * 	ELF503_RATEADDM 利率補充說明
	 * 	ELF503_RATETYPE 利率方式
	 * 	ELF503_RATECHG 利率變動方式
	 * 	ELF503_RATECHGP 變動週期
	 * 
	 * 	ELF503_RATECHGD 指定下次變動日期
	 * 	ELF503_INTWAY 收息方式
	 * 	ELF503_TAXWAY 稅負負擔方式
	 * 	ELF503_LIMITFG 限制條件/說明
	 * 	ELF503_LIMITVR1 固定利率
	 * 	ELF503_LIMITOTH 其他限制
	 * 	ELF503_UPDATER 資料修改人（行員代號）
	 * 	ELF503_TAIFXDIF S/LBOR與TAIFX差額逾「XX」％部分由借戶負擔
	 * 	ELF503_LRCODE 下限利率代碼
	 * 	ELF503_LRSAMEFG 下限利率借款同天期
	 * 
	 * 	ELF503_LRADJFG 下限利率加減年率
	 * 	ELF503_LRADJRT 下限利率加減年率%
	 * 	ELF503_LRRATE01 下限利率自訂利率%
	 * 	ELF503_LRTAXWAY 下限利率稅負負擔方式
	 * 	ELF503_TAXRATE 扣稅負擔碼
	 * 	ELF503_LRTAXBRT 下限利率扣稅負擔
	 * 	ELF503_DESCRIPTION 描述說明
	 * </pre>
	 * 
	 */
	public void insert(List<Object[]> DeleteList, List<Object[]> dataList);

	/**
	 * 簽約未動用報送，註銷額度時，一併刪除ELF503資料 J-109-0202_05097_B1001 Web
	 * e-Loan利費率資料提前至授信案件經核定後逕行寫入
	 * 
	 * @param DeleteList
	 */
	public void deleteByDocumentNo(List<Object[]> DeleteList);
}
