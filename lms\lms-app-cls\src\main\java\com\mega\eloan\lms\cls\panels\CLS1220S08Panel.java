package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 線上貸款-青創 100萬以上
 * </pre>
 * 
 * @since 2022/03/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/03/16,011879,new
 *          </ul>
 */
public class CLS1220S08Panel extends Panel {

	private static final long serialVersionUID = 1L;

	/**
	 * @param id
	 */
	public CLS1220S08Panel(String id) {
		super(id);
	}

	/**
	 * @param id
	 * @param updatePanelName
	 */
	public CLS1220S08Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

}
