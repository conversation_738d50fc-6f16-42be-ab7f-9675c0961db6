package com.mega.eloan.lms.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.RelativeMeta;


/**
 * <pre>
 * C140JSON model.
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/20,<PERSON>,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140JSON-entity-graph", attributeNodes = { 
		@NamedAttributeNode("c140m01a"),
		@NamedAttributeNode("c140m04a"),
		@NamedAttributeNode("c140m07a"),
        @NamedAttributeNode("c140m04b") })
@Entity
@Table(name="C140JSON", uniqueConstraints = @UniqueConstraint(columnNames ={"oid"}))
public class C140JSON extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

	@Column(length=4000)
	private String jsonOb;

	@Column(length=1)
	private String flag;

	@Column(length=3)
	private String subtab;

	@Column(length=3)
	private String tab;

	//bi-directional many-to-one association to C140M01A
    @ManyToOne(fetch=FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name="MAINID", referencedColumnName="MAINID", nullable=false, insertable = false, updatable = false),
		@JoinColumn(name="PID", referencedColumnName="UID", nullable=false, insertable = false, updatable = false)
		})
	private C140M01A c140m01a;

	//bi-directional many-to-one association to C140M04A
    @ManyToOne(fetch=FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name="MAINID", referencedColumnName="MAINID", nullable=false, insertable = false, updatable = false),
		@JoinColumn(name="PID", referencedColumnName="UID", nullable=false, insertable = false, updatable = false)
		})
	private C140M04A c140m04a;
    
	//bi-directional many-to-one association to C140M07A
    @ManyToOne(fetch=FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name="MAINID", referencedColumnName="MAINID", nullable=false, insertable = false, updatable = false),
		@JoinColumn(name="PID", referencedColumnName="UID", nullable=false, insertable = false, updatable = false)
		})
	private C140M07A c140m07a;
    
  //bi-directional many-to-one association to C140M04B
	@ManyToOne(fetch=FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name="MAINID", referencedColumnName="MAINID", nullable=false, insertable = false, updatable = false),
		@JoinColumn(name="PID", referencedColumnName="UID", nullable=false, insertable = false, updatable = false)
		})
	private C140M04B c140m04b;

	public String getJsonOb() {
		return jsonOb;
	}

	public void setJsonOb(String jsonOb) {
		this.jsonOb = jsonOb;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public String getSubtab() {
		return this.subtab;
	}

	public void setSubtab(String subtab) {
		this.subtab = subtab;
	}

	public String getTab() {
		return this.tab;
	}

	public void setTab(String tab) {
		this.tab = tab;
	}

	public C140M01A getC140m01a() {
		return this.c140m01a;
	}

	public void setC140m01a(C140M01A c140m01a) {
		this.c140m01a = c140m01a;
	}
	
	public C140M04A getC140m04a() {
		return this.c140m04a;
	}

	public void setC140m04a(C140M04A c140m04a) {
		this.c140m04a = c140m04a;
	}

	public C140M07A getC140m07a() {
		return this.c140m07a;
	}

	public void setC140m07a(C140M07A c140m07a) {
		this.c140m07a = c140m07a;
	}
	
	public C140M04B getC140m04b() {
		return this.c140m04b;
	}

	public void setC140m04b(C140M04B c140m04b) {
		this.c140m04b = c140m04b;
	}
}