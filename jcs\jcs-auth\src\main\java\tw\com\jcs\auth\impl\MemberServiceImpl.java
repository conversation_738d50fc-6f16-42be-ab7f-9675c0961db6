package tw.com.jcs.auth.impl;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowCallbackHandler;

import tw.com.jcs.auth.AuthQueryFactory;
import tw.com.jcs.auth.MemberService;
import tw.com.jcs.auth.model.Branch;
import tw.com.jcs.auth.model.Department;
import tw.com.jcs.auth.model.User;
import tw.com.jcs.auth.util.StringUtil;

/**
 * <pre>
 * MemberServiceImpl
 * </pre>
 * 
 * @since 2022年12月22日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月22日
 *          </ul>
 */
// @Deprecated
public class MemberServiceImpl implements MemberService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private AuthQueryFactory queryFactory;

    // roleid -> pgmcode -> auth mapping
    Map<String, Map<Integer, Integer>> roleAuthes = new ConcurrentHashMap<String, Map<Integer, Integer>>();

    // key=role+"_"+pgmcode, value=指定部門
    Map<String, String> pgmDepts = new ConcurrentHashMap<String, String>();

    // role add by fantasy 2012/02/29
    Map<String, String> roles = new ConcurrentHashMap<String, String>();

    // role add by fantasy 2012/04/13
    Map<String, Map<String, Integer>> roleDocAuth = new ConcurrentHashMap<String, Map<String, Integer>>();

    private String systemType;

    /**
     * 取得systemType
     */
    void initSystemType() {
        this.systemType = queryFactory.getSystemType();
    }

    @Resource
    public void setQueryFactory(AuthQueryFactory queryFactory) {
        this.queryFactory = queryFactory;
    }

    /**
     * constructor
     */
    public MemberServiceImpl() {
        super();
    }

    /**
     * 初始化參數
     */
    @PostConstruct
    void init() {
        roleAuthes.clear();
        pgmDepts.clear();
        roles.clear();
        roleDocAuth.clear();

        // initDept();
        // initUsers();
        logger.info("initRoleAuthes");
        initRoleAuthes();
        // initUserRoles();
        logger.info("initSystemType");
        initSystemType();
        logger.info("initRole");
        initRole(); // add by fantasy 2012/02/12
        logger.info("initRoleDocAuth");
        initRoleDocAuth(); // add by fantasy 2012/02/12
    }

    /**
     * 初始化參數(Role)
     */
    // initRole add by fantasy 2012/02/12
    void initRole() {
        queryFactory.execRoleQuery(new RowCallbackHandler() {
            public void processRow(ResultSet rs) throws SQLException {
                String key = StringUtil.trim(rs.getString("ROLCODE"));
                roles.put(key, key);
            }
        });
    }

    /**
     * 初始化參數(RoleDocAuth)
     */
    // initRoleDocAuth add by fantasy 2012/04/13
    void initRoleDocAuth() {
        queryFactory.execDocAuthQuery(new RowCallbackHandler() {
            public void processRow(ResultSet rs) throws SQLException {
                String docid = StringUtil.trim(rs.getString("DOCID"));
                String role = StringUtil.trim(rs.getString("ROLCODE"));
                int auth = rs.getInt("PGMAUTH");
                Map<String, Integer> docAuth = roleDocAuth.get(role);
                if (docAuth == null) {
                    docAuth = new HashMap<String, Integer>();
                    roleDocAuth.put(role, docAuth);
                }
                docAuth.put(docid, auth);
            }
        });
    }

    void initDept() {
    }

    void initUsers() {
    }

    /**
     * 初始化參數(RoleAuthes)
     */
    void initRoleAuthes() {

        queryFactory.execRoleAuthQuery(new RowCallbackHandler() {
            public void processRow(ResultSet rs) throws SQLException {
                String role = StringUtil.trim(rs.getString("ROLE"));
                Map<Integer, Integer> authes = roleAuthes.get(role);
                if (authes == null) {
                    authes = new HashMap<Integer, Integer>();
                    roleAuthes.put(role, authes);
                }
                authes.put(rs.getInt("AUTHCODE"), rs.getInt("AUTH"));

                // *************************************************************
                try {
                    // 20120210:UFO Add : 新增部門權限
                    String authDept = StringUtil.trim(rs.getString("AUTHDEPT"));
                    if (!"".equals(authDept)) {
                        String key = role + "_" + rs.getInt("AUTHCODE");
                        pgmDepts.put(key, authDept);
                        logger.debug("[initRoleAuthes][execRoleAuthQuery] key=[" + key + "], value=[" + authDept + "]");
                    }
                } catch (SQLException ex) {
                    logger.error("[initRoleAuthes][execRoleAuthQuery]取得PGMDEPT資料發生錯誤!!", ex);
                }
                // *************************************************************
            }
        });
    }

    void initUserRoles() {
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getUser(java.lang.String)
     */
    @Override
    public User getUser(String userId) {
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getDept(java.lang.String)
     */
    @Override
    public Department getDept(String branchId) {
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getDeptByType(java.lang.String[])
     */
    @Override
    public List<Department> getDeptByType(String... type) {
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getUsersByDept(java.lang.String)
     */
    @Override
    public List<User> getUsersByDept(String deptId) {
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getRolesByUser(java.lang.String)
     */
    @Override
    public Set<String> getRolesByUser(String userId) {
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getAuthesByRole(java.lang.String)
     */
    @Override
    public Map<Integer, Integer> getAuthesByRole(String roleId) {
        Map<Integer, Integer> map = roleAuthes.get(roleId);
        if (map == null) {
            map = Collections.emptyMap();
        }
        return map;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getAuthesByRoleAndDocid(java.lang.String)
     */
    @Override
    public Map<String, Integer> getAuthesByRoleAndDocid(String roleId) {
        Map<String, Integer> map = roleDocAuth.get(roleId);
        if (map == null) {
            map = Collections.emptyMap();
        }
        return map;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getUsersByAuth(int[])
     */
    @Override
    public List<User> getUsersByAuth(int... authCode) {
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getUsersByDeptAndAuth(java.lang.String, int[])
     */
    @Override
    public List<User> getUsersByDeptAndAuth(String deptId, int... authCode) {
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getAuthTypeByUserAndAuth(java.lang.String, int)
     */
    @Override
    public int getAuthTypeByUserAndAuth(String userId, int auth) {
        Set<String> roles = getRolesByUser(userId);
        int value = 0;
        for (String role : roles) {
            Map<Integer, Integer> authes = getAuthesByRole(role);
            Integer authValue = authes.get(auth);
            if (authValue != null) {
                value |= authValue.intValue();
            }
        }
        return value;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getUsersByRole(java.lang.String[])
     */
    @Override
    public List<User> getUsersByRole(String... roleId) {
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getUsersByDeptAndRole(java.lang.String, java.lang.String[])
     */
    @Override
    public List<User> getUsersByDeptAndRole(String deptId, String... roleId) {
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getAuthTypeByRoles(java.util.Set, int)
     */
    // add by fantasy 2011/05/10
    @Override
    public int getAuthTypeByRoles(Set<String> roles, int auth) {
        if (roles == null) {
            roles = Collections.emptySet();
        }
        int value = 0;
        for (String role : roles) {
            Map<Integer, Integer> authes = getAuthesByRole(role);
            Integer authValue = authes.get(auth);
            if (authValue != null) {
                value |= authValue.intValue();
            }
        }
        return value;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getAuthTypeByRoles(java.lang.String, java.util.Set, int)
     */
    @Override
    public int getAuthTypeByRoles(String pgmDept, Set<String> roles, int pgmcode) {
        if (roles == null) {
            roles = Collections.emptySet();
        }
        int value = 0;
        for (String role : roles) {
            // ************************
            if (!isPGMUnitCheckOK(pgmDepts, role, pgmcode, pgmDept)) {
                continue;
            }
            // ************************
            // Map<Integer, Integer> authes = getAuthesByRole(role);
            Map<Integer, Integer> authes = getAuthesByRole(role);
            Integer authValue = authes.get(pgmcode);
            if (authValue != null) {
                value |= authValue.intValue();
            }
        }
        return value;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#getAuthTypeByRoles(java.lang.String, java.util.Set, int, java.lang.String)
     */
    @Override
    public int getAuthTypeByRoles(String pgmDept, Set<String> roles, int pgmcode, String docid) {
        if (roles == null) {
            roles = Collections.emptySet();
        }
        int value = 0;
        for (String role : roles) {
            if (!isPGMUnitCheckOK(pgmDepts, role, pgmcode, pgmDept)) {
                continue;
            }
            Map<String, Integer> authes = getAuthesByRoleAndDocid(role);
            Integer authValue = authes.get(StringUtil.trim(docid));
            if (authValue != null) {
                value |= authValue.intValue();
            }
        }
        return value;
    }

    /**
     * 角色代號_功能項目代號是否有指定部門,若有指定需要判斷是否登錄分行有在部門清單用
     * 
     * @param pgmDepts
     *            指定部門
     * @param rolecode
     *            角色代號
     * @param pgmcode
     *            功能項目代號
     * @param workUnit
     * @return
     */
    private boolean isPGMUnitCheckOK(Map<String, String> pgmDepts, String rolecode, int pgmcode, String workUnit) {
        String key = rolecode + "_" + pgmcode;
        String deptList = StringUtil.trim(pgmDepts.get(key));

        // 沒有指定workUnit時
        if ("".equals(StringUtil.trim(workUnit)))
            return true;
        // adm 設定時會存成字串(待修正..)
        if ("null".equals(deptList.toLowerCase()))
            return true;

        boolean isWorkUnitCheckOK = ("".equals(deptList) || deptList.indexOf(workUnit) != -1);
        return isWorkUnitCheckOK;
    }

    private String getSystemType() {
        return systemType;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#megaSSORotesToEloanRotes(java.util.Map, tw.com.jcs.auth.model.Branch)
     */
    @SuppressWarnings("rawtypes")
    @Override
    public Set<String> megaSSORotesToEloanRotes(Map<String, String> mapRolse, Branch branch) {
        Set<String> roles = new HashSet<String>();
        String systemType = getSystemType();
        String deptType = (branch == null ? "X" : StringUtil.trim(branch.getUnitType()));
        if (mapRolse != null) {
            Iterator iter = mapRolse.entrySet().iterator();
            while (iter.hasNext()) {
                Map.Entry entry = (Map.Entry) iter.next();
                String key = StringUtil.trim((String) entry.getKey());

                // 依登入系統＋登錄分行＋SSO角色，判別組成角色代號。
                // 命名規則:角色系統別+B分行/C營運中心/H總行+SSO角色
                String role = systemType + deptType + key;
                roles.add(role);
            }
        }
        return roles;
    }

    /**
     * @return the roleAuthDepts
     */
    public Map<String, String> getPGMDepts() {
        return pgmDepts;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#hasRole(java.lang.String)
     */
    @Override
    public boolean hasRole(String role) {
        return roles.containsKey(StringUtil.trim(role));
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.MemberService#reload()
     */
    public synchronized void reload() {
        this.init();
    }
}
