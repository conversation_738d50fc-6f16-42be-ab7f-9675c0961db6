---------------------------------------------------------
-- LMS.L140M01P 敘做條件異動比較表
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140M01P;
CREATE TABLE LMS.L140M01P (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0) ,
	<PERSON><PERSON><PERSON>R<PERSON><PERSON>      VARC<PERSON>R(12)  ,
	AFCNTRNO      VARCHAR(12)  ,
	ITEMNAME      VARCHAR(100) ,
	BFITEMDRC     VARCHAR(1536),
	AFITEMDRC     VARCHAR(1536),
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140M01P PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140M01P01;
--2013-06-13,Rex,移除此index
--CREATE UNIQUE INDEX LMS.XL140M01P01 ON LMS.L140M01P   (MAINID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140M01P IS '敘做條件異動比較表';
COMMENT ON LMS.L140M01P (
	OID           IS 'oid', 
	MAINID        IS 'mainId', 
	SEQ           IS '序號', 
	BFCNTRNO      IS '變更前額度序號', 
	AFCNTRNO      IS '變更後額度序號', 
	ITEMNAME      IS '項目', 
	BFITEMDRC     IS '前准敘做條件', 
	AFITEMDRC     IS '本次申請敘做條件', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
