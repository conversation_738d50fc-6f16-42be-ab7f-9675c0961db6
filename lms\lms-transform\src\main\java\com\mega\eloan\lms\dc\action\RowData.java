package com.mega.eloan.lms.dc.action;

public class RowData {
	private boolean isClobTb = false;
	private String sql = null;
	private String clobString = null;

	/**
	 * @return the isClobTb
	 */
	public boolean isClobTb() {
		return isClobTb;
	}

	/**
	 * @param isClobTb
	 *            the isClobTb to set
	 */
	public void setClobTb(boolean isClobTb) {
		this.isClobTb = isClobTb;
	}

	/**
	 * @return the sql
	 */
	public String getSql() {
		return sql;
	}

	/**
	 * @param sql
	 *            the sql to set
	 */
	public void setSql(String sql) {
		this.sql = sql;
	}

	/**
	 * @return the clobString
	 */
	public String getClobString() {
		return clobString;
	}

	/**
	 * @param clobString
	 *            the clobString to set
	 */
	public void setClobString(String clobString) {
		this.clobString = clobString;
	}

	public String toString() {
		return this.getSql();
	}
}
