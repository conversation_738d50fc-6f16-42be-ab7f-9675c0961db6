package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.lms.dw.service.DwFxrthovsService;
import com.mega.eloan.lms.mfaloan.service.MisRatetblService;
import com.mega.sso.model.IBranch;

@Service
public class MisRatetblServiceImple extends AbstractMFAloanJdbc implements
		MisRatetblService {

	private static final Logger logger = LoggerFactory.getLogger("com/mega/eloan/lms/mfaloan/service/MisRatetblService");
	
	@Resource
	DwFxrthovsService dwRateService;
	
	@Override
	public Map<String, Object> getNewestByCurr(String curr) {
		// 幣別如果是台幣，就回傳1
		if ("TWD".equals(curr) || "NTD".equals(curr)) {
			Map<String, Object> currData = new HashMap<String, Object>();
			currData.put("CURR", "TWD");
			currData.put("DATAYMD", "");
			currData.put("ENDRATE", new BigDecimal(1.0));
			return currData;
		}
		return getJdbc().queryForMap("Ratetbl.getNewestByCurr",
				new String[] { curr });

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisRatetblService#findByYMDCurr(java
	 * .lang.String, java.lang.String)
	 */
	@Override
	public Map<String, Object> findByCurrDate(String curr, String date) {
		String qDate = CapString.fillString(date, 7, false, '_');
		return getJdbc().queryForMap("RATETBL.findByDateCurr",
				new Object[] { curr, qDate });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.mega.eloan.ces.mfaloan.service.MisRatetblService#listNewest()
	 */
	@Override
	public List<Map<String, Object>> listNewest() {
		return getJdbc().queryForList("RATETBL.listNewest", new Object[0]);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisRatetblService#findNewestByCurr
	 * (java.lang.String)
	 */
	@Override
	public List<Map<String, Object>> findNewestByCurr(String curr) {
		return getJdbc().queryForList("RATETBL.findByDateCurr",
				new Object[] { curr, "0000000" });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisRatetblService#findNewestByCurr
	 * (java.lang.String)
	 */
	@Override
	public Map<String, Object> findByCurr(String curr) {
		return getJdbc().queryForMap("RATETBL.findByCurr",
				new Object[] { curr });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisRatetblService#findNewestByCurr
	 * (java.lang.String)
	 */
	@Override
	public Map<String, Object> findByDate(String date) {
		String dateStr = (date.length() == 5) ? date + "%" : date;
		List<Map<String, Object>> curr = getJdbc().queryForList(
				"RATETBL.findByDate", new Object[] { dateStr });
		Map<String, Object> result = new HashMap<String, Object>();
		if (curr != null) {
			for (int i = 0; i < curr.size(); i++) {
				Map<String, Object> pivot = curr.get(i);
				result.put(Util.trim(pivot.get("curr")), pivot.get("endRate"));
			}
		}
		result.put("TWD", "1");
		return result;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisRatetblService#findExRateOfLastWorkingDayOfPreviousMonth
	 * (java.lang.String)
	 */
	@Override
	public Map<String, Object> findExRateOfLastWorkingDayOfPreviousMonth(String ROCDateYM) {
		Map<String, Object> exRateMap = new HashMap<String, Object>();
		exRateMap.put("TWD", "1");
		if (ROCDateYM != null) {
			ROCDateYM = ROCDateYM.length() == 5 ? ROCDateYM : String.format("%05d", Integer.parseInt(ROCDateYM));// 未滿民國100年字串須被補0
			List<Map<String, Object>> exRateDataList = getJdbc().queryForListWithMax(
					"RATETBL.findExRateOfLastWorkingDayOfPreviousMonth", new Object[] { ROCDateYM + "%" });
			if (!exRateDataList.isEmpty()) {
				for (Map<String, Object> exRateData : exRateDataList) {
					exRateMap.put(Util.trim(exRateData.get("curr")), exRateData.get("endRate"));
				}
			}
		}
		return exRateMap;
	}
	
	@Override
	public BigDecimal getCur1ToCur2Rate(String date, IBranch branch,
			String sourceCurr, String destCur) {
		String br = branch.getUnitType();
		BigDecimal tmpBD = null;
		// 找不到海外匯率檔,改找國內匯率檔
		boolean isLocalBranch = true;
		if (BranchTypeEnum.海外分行.isEquals(br)
				|| BranchTypeEnum.海外總行澳洲加拿大.isEquals(br)
				|| BranchTypeEnum.海外總行泰國.isEquals(br)
				|| BranchTypeEnum.大陸分行.isEquals(br)
				|| BranchTypeEnum.海外分行當地有總行.isEquals(br)) {// 海外分行
			isLocalBranch = false;
			tmpBD = dwRateService.getCur1ToCur2Rate(date, branch, sourceCurr,
					destCur);
		}
		if (tmpBD == null || isLocalBranch) {
			// 國內分行
			String rocDate; // 民國年
			if (CapString.isEmpty(date)) {
				rocDate = "0000000"; // 最新匯率
			} else {
				String yyy = CapString.fillZeroHead(
						CapMath.subtract(date.substring(0, 4), "1911"), 3);
				rocDate = yyy + date.substring(4).replace("-", ""); // YYYMMDD
			}
			if ("TWD".equals(sourceCurr)) { // 台幣折合destCur的匯率
				Map<String, Object> rtn = findByCurrDate(destCur, rocDate);
				if (rtn != null && !rtn.isEmpty()) {
					tmpBD = CapMath.getBigDecimal(CapMath.divide("1",
							CapMath.bigDecimalToString((BigDecimal) rtn
									.get("ENDRATE")), 8));
				}
			} else if ("TWD".equals(destCur)) {// sourceCurr折合台幣的匯率
				Map<String, Object> rtn = findByCurrDate(sourceCurr, rocDate);
				if (rtn != null && !rtn.isEmpty()) {
					tmpBD = (BigDecimal) rtn.get("ENDRATE");
				}
			} else { // sourceCurr折合destCur的匯率 (sourceCurr->台幣->destCur)
				BigDecimal rate = null;
				// sourceCurr->台幣
				Map<String, Object> rtn1 = findByCurrDate(sourceCurr, rocDate);
				// 台幣->destCur
				Map<String, Object> rtn2 = findByCurrDate(destCur, rocDate);
				// 20121109,0076236,增加rtn2檢查是否為空值
				if (rtn1 != null && !rtn1.isEmpty() && rtn2 != null
						&& !rtn2.isEmpty()) {
					rate = (BigDecimal) rtn1.get("ENDRATE");

					rate = rate.multiply(CapMath.getBigDecimal(CapMath.divide(
							"1", CapMath.bigDecimalToString((BigDecimal) rtn2
									.get("ENDRATE")), 8)));
					tmpBD = rate;

				}
			}
		}
		if (tmpBD == null) {
			logger.error("can't find exachange rate by conditions:" + date
					+ "," + branch.getBrNo() + "," + sourceCurr + "," + destCur
					+ ". so return one instead");
			tmpBD = BigDecimal.ONE;
		}
		return tmpBD;// 20120705, CP, 查詢不到時預設值回傳 1 。
	}
	
	/**
	 * 取得起迄日期中最新的利率日期
	 * J-105-0185-001 請提供103年度及104年度國內所有分行之新作、增額及續約之授信件數及額度金額(包含企金及消金)
	 * @param begDate
	 * @param endDate
	 * @return
	 */
	@Override
	public Map<String, Object> findLastDateBetweenRATEYMDRange(String begDate,String endDate){
		// 幣別如果是台幣，就回傳1
		return getJdbc().queryForMap("RATETBL.selLastDateBetweenRATEYMDRange",
				new String[] { begDate,endDate });

	}
	
	/**
	 * 取得指定日期之所有利率資料
	 * J-105-0185-001 請提供103年度及104年度國內所有分行之新作、增額及續約之授信件數及額度金額(包含企金及消金)
	 * @param date
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findByDateForList(String date) {
		String dateStr =  date + "%" ;
		return getJdbc().queryForListWithMax(
				"RATETBL.findByDate", new Object[] { dateStr });
		
	}
	
}
