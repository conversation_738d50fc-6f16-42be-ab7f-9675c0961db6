package com.mega.eloan.lms.eloandb.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 海外 - Dw_elf411ovs(r6) / 國內 - MIS.ELF411
 * </pre>
 * 
 * @since 2011/11/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/7,irene,new
 *          </ul>
 */
public interface Dw_elf411ovsService {

	List<Map<String, Object>> findELF411ByNewDate(String branch, String custId,
			String dupNo, String newDate);

	List<Map<String, Object>> findELF411ByCycMn(String dataDate, String branch,
			String custId, String dupNo);

	/**
	 * 【新增/增額/逾放轉正】名單
	 * 
	 * @param dataDate
	 * @return
	 */
	Map<String, Object> findELF411ForMaxDate();

	List<Map<String, Object>> findELF411ByMaxDate(String brNo, Date dataDate);

	/**
	 * 覆審管理報表(TYPE3)
	 * 
	 * @param brNo
	 * @param beDate
	 * @param enDate
	 * @return
	 */
	List<Map<String, Object>> find411ovsByBranchForReportType3(String brNo,
			String beDate, String enDate);

}
