/* 
 * LMS1855V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.lrs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;

/**
 * <pre>
 * 
 * </pre>
 * 
 * @since 2012/2/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/15,jessica,new
 *          </ul>
 */
@Controller
@RequestMapping(path = "/lrs/lms1855v01")
public class LMS1855V01Page extends AbstractEloanInnerView {

	public LMS1855V01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		addToButtonPanel(model, 
				LmsButtonEnum.InsertInExcelData, LmsButtonEnum.InsertExcelData,
				LmsButtonEnum.InsertLateExcelData,
				LmsButtonEnum.InsertRecExcelData,
				LmsButtonEnum.InsertUnExcelData, LmsButtonEnum.View,
				LmsButtonEnum.Filter, LmsButtonEnum.Delete);
		renderJsI18N(LMS1855V01Page.class);

	}// ;

	public String[] getJavascriptPath() {

		return new String[] { "pagejs/lrs/LMS1855V01Page.js" };
	}
}
