/* 
 * LMS1705M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.auth.CodeItemService;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.service.FlowService;

import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.lrs.panels.LMS1705S01Panel;
import com.mega.eloan.lms.lrs.panels.LMS1705S02Panel;
import com.mega.eloan.lms.lrs.panels.LMS1705S03Panel;
import com.mega.eloan.lms.lrs.panels.LMS1705S04Panel;
import com.mega.eloan.lms.lrs.panels.LMS1705S05Panel;
import com.mega.eloan.lms.model.L170M01A;

/**
 * <pre>
 * 企金覆審報告表
 * </pre>
 * 
 * @since 2012/2/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/15,jessica,new
 *          </ul>
 */
@Controller
@RequestMapping("/lrs/LMS1705M01Page/{page}")
public class LMS1705M01Page extends AbstractEloanForm {

	@Autowired
	CodeItemService cis;

	@Autowired
	FlowService flowService;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS1705M01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);

		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
		AuthType.Modify, RetrialDocStatusEnum.編製中));

		// 待受檢單位回覆
		addAclLabel(model, new AclLabel("_btnDOC_RECEIVES", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.待受檢單位回覆));

		// 編製完成
		addAclLabel(model, new AclLabel("_btn_COMPLETE", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.編製完成));

		// 待覆核
		addAclLabel(model, new AclLabel("_btnWAIT_CHECK", params, getDomainClass(),
				AuthType.Accept, RetrialDocStatusEnum.待覆核));

		addAclLabel(model, new AclLabel("_btn_APPROVE", params, getDomainClass(),
				AuthType.Accept, RetrialDocStatusEnum.已核准));

		renderJsI18N(LMS1705M01Page.class);

		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2);
		
		model.addAttribute("tabIdx", tabID);
		Panel panel = getPanel(page);
		panel.processPanelData(model, params);

	}// ;

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS1705S01Panel(TAB_CTX, true);
			renderJsI18N(LMS1705S01Panel.class);
			break;
		case 2:
			panel = new LMS1705S02Panel(TAB_CTX, true);

			renderJsI18N(LMS1705S02Panel.class);

			break;
		case 3:
			panel = new LMS1705S03Panel(TAB_CTX, true);
			renderJsI18N(LMS1705S03Panel.class);

			break;
		case 4:
			panel = new LMS1705S04Panel(TAB_CTX, true);
			renderJsI18N(LMS1705S04Panel.class);

			break;
		case 5:
			panel = new LMS1705S05Panel(TAB_CTX, true);
			renderJsI18N(LMS1705S05Panel.class);
			break;

		default:
			panel = new LMS1705S02Panel(TAB_CTX, true);
			break;
		}
		if (panel == null)
			panel = new Panel(TAB_CTX, true);
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L170M01A.class;
	}

}//
