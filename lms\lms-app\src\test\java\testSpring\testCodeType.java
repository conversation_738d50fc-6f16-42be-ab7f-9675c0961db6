package testSpring;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;

import com.mega.eloan.common.dao.CodeTypeDao;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;

import tw.com.jcs.common.Util;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:testCodeType/testCodeType.xml" })
@Transactional(transactionManager = "transactionManager")
@Rollback(value = true)
public class testCodeType {

	private static Logger logger = LoggerFactory.getLogger(testCodeType.class);

	private static List<String> allCodeTypeKey = new ArrayList<String>();

	private static HashMap<String, CodeType> CodeTypeTw = new HashMap<String, CodeType>();
	private static HashMap<String, CodeType> CodeTypeCn = new HashMap<String, CodeType>();
	private static HashMap<String, CodeType> CodeTypeEn = new HashMap<String, CodeType>();

	@Resource
	CodeTypeDao codeTypeDao;

	@Resource
	CodeTypeService codeTypeService;

	@Before
	public void init() {
		allCodeTypeKey = codeTypeDao.findCodeType();
		List<CodeType> listTw = codeTypeDao.findByCodeType(
				allCodeTypeKey.toArray(new String[] {}), "zh_TW");

		for (CodeType codetype : listTw) {
			CodeTypeTw.put(
					codetype.getCodeType() + "_" + codetype.getCodeValue(),
					codetype);
		}
		List<CodeType> listCn = codeTypeDao.findByCodeType(
				allCodeTypeKey.toArray(new String[] {}), "zh_CN");
		for (CodeType codetype : listCn) {
			CodeTypeCn.put(
					codetype.getCodeType() + "_" + codetype.getCodeValue(),
					codetype);
		}
		List<CodeType> listEn = codeTypeDao.findByCodeType(
				allCodeTypeKey.toArray(new String[] {}), "en");

		for (CodeType codetype : listEn) {
			CodeTypeEn.put(
					codetype.getCodeType() + "_" + codetype.getCodeValue(),
					codetype);
		}

	}

	/**
	 * 測試codetype內容值是否正確
	 * 
	 */
	@Test
	public void checkValue() {
		logger.info("Tw Count==>[{}]", CodeTypeTw.size());
		logger.info("CN Count==>[{}]", CodeTypeCn.size());
		logger.info("EN Count==>[{}]", CodeTypeEn.size());

		CodeType soureCodeType = null;
		CodeType targerCodeType = null;
		for (String key : CodeTypeTw.keySet()) {
			soureCodeType = CodeTypeTw.get(key);

			// 檢查CN
			if (CodeTypeCn.containsKey(key)) {
				targerCodeType = CodeTypeCn.get(key);
				if (Util.isNotEmpty(soureCodeType.getCodeDesc2())) {
					if (!soureCodeType.getCodeDesc2().equals(
							targerCodeType.getCodeDesc2())) {
						logger.error(
								"cn CodeDesc2有誤 key==>[{}] ,TW.CodeDesc2==>[{}],cn.CodeDesc2==>[{}]",
								new Object[] { key,
										soureCodeType.getCodeDesc2(),
										targerCodeType.getCodeDesc2() });
					}
				}
				if (Util.isNotEmpty(soureCodeType.getCodeDesc3())) {
					if (!soureCodeType.getCodeDesc3().equals(
							targerCodeType.getCodeDesc3())) {
						logger.error(
								"cn 有誤 key==>[{}] ,TW.getCodeDesc3==>[{}],cn.getCodeDesc3==>[{}]",
								new Object[] { key,
										soureCodeType.getCodeDesc3(),
										targerCodeType.getCodeDesc3() });
					}
				}
			} else {
				logger.error("cn少Codetype==>[{}]", key);
			}
			if (CodeTypeEn.containsKey(key)) {
				targerCodeType = CodeTypeEn.get(key);
				if (Util.isNotEmpty(soureCodeType.getCodeDesc2())) {
					if (!soureCodeType.getCodeDesc2().equals(
							targerCodeType.getCodeDesc2())) {
						logger.error(
								"En 有誤 key==>[{}] ,TW.getCodeDesc2==>[{}],En.getCodeDesc2==>[{}]",
								new Object[] { key,
										soureCodeType.getCodeDesc2(),
										targerCodeType.getCodeDesc2() });
					}
				}
				if (Util.isNotEmpty(soureCodeType.getCodeDesc3())) {
					if (!soureCodeType.getCodeDesc3().equals(
							targerCodeType.getCodeDesc3())) {
						logger.error(
								"En 有誤 key==>[{}] ,TW.getCodeDesc3==>[{}],En.getCodeDesc3==>[{}]",
								new Object[] { key,
										soureCodeType.getCodeDesc3(),
										targerCodeType.getCodeDesc3() });
					}
				}
			} else {
				logger.error("En少Codetype==>[{}]", key);
			}
		}
	}

}
