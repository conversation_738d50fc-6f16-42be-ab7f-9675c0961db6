/* 
 * C122M01EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C122M01E;

/** 團體消貸名單控制檔 **/
public interface C122M01EDao extends IGenericDao<C122M01E> {

	C122M01E findByOid(String oid);
	
	C122M01E findByUniqueKey(String grpCntrNo, String custId);

	List<C122M01E> findByIndex01(String grpCntrNo, String custId);
	
	C122M01E findByRefMainId(String refMainId);
}