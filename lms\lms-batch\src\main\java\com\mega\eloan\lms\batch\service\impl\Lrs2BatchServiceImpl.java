package com.mega.eloan.lms.batch.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.lowagie.text.pdf.PdfCopyFields;
import com.lowagie.text.pdf.PdfReader;
import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.SpringContextHelper;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lrs.constants.lrsConstants;
import com.mega.eloan.lms.lrs.flow.LMS1700Flow;
import com.mega.eloan.lms.lrs.service.LMS1700Service;
import com.mega.eloan.lms.lrs.service.LMS1800Service;
import com.mega.eloan.lms.lrs.service.LMS1810Service;
import com.mega.eloan.lms.lrs.service.LMS1825Service;
import com.mega.eloan.lms.mfaloan.service.MisELF411Service;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L182M01A;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Service("lrs2BatchServiceImpl")
public class Lrs2BatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger logger = LoggerFactory
			.getLogger(Lrs2BatchServiceImpl.class);

	@Resource
	RetrialService retrialService;

	@Resource
	BranchService branchService;

	@Resource
	LMS1700Service lms1700Service;

	@Resource
	LMS1800Service lms1800Service;

	@Resource
	LMS1810Service lms1810Service;

	@Resource
	LMS1825Service lms1825Service;

	@Resource
	MisELF411Service misELF411Service;

	@Resource
	FlowSimplifyService flowSimplifyService;

	@Resource
	TempDataService tempDataService;

	@Autowired
	DocFileService fileService;

	@Resource
	DocCheckService docCheckService;

	@Autowired
	DocFileDao docFileDao;

	@Override
	public JSONObject execute(JSONObject json) {
		boolean isSuccess = false;
		JSONObject result = null;
		JSONObject rq = json.getJSONObject("request");

		String act = rq.optString("act");
		String msg = "";
		try {
			if (Util.equals("StartGenerateCTLList", act)) {
				String limit_branch = Util.trim(rq.optString("limit_branch"));
				elf411_to_elf412(limit_branch);
			} else if (Util.equals("procL182M01A", act)) {
				boolean setExeTimeAndRun = false;
				L182M01A l182m01a = retrialService
						.findL182M01A_typCd1_inProcess();

				int normalMin = Util.parseInt(rq.optString("normalMin"));
				if (normalMin > 0) {
					// ok
				} else {
					normalMin = 80;// default value
				}

				// 已存在處理中
				if (l182m01a != null) {
					Timestamp nowTS = CapDate.getCurrentTimestamp();
					long difInMin = (nowTS.getTime() - l182m01a.getExeTime()
							.getTime()) / (1000 * 60);
					if (difInMin > normalMin) {
						// restart
						// 也要重設 exeTime, 避免 server 重啟後的第2個 batch 又start一次
						// produce() 略過同一 endDate 且仍在 編製中 的分行
						setExeTimeAndRun = true;
					} else {
						// 仍在處理中
					}
				} else {
					l182m01a = retrialService.findL182M01A_typCd1_notProcess();
					if (l182m01a != null) {
						setExeTimeAndRun = true;
					}
				}

				if (setExeTimeAndRun) {
					String oid = StringEscapeUtils.escapeSql(l182m01a.getOid());
					List<String> existBrSkipList = new ArrayList<String>();
					// 在 procStart 裡，重設 exeTime
					retrialService.saveL182M01A_procStart(l182m01a);
					// ---
					boolean procResult = false;
					try {
						// 參考 gfnStartNorthBranch_Reserve_Begin
						String[] brList = Util.trim(l182m01a.getBranchList())
								.split(",");
						_produceL180M01A_from1(l182m01a.getCreator(),
								l182m01a.getOwnBrId(), l182m01a.getUnitType(),
								l182m01a.getBaseDate(), brList, existBrSkipList);
						// ---
						procResult = true;
					} catch (Exception e) {
						logger.error(StrUtils.getStackTrace(e));
					}

					lms1825Service.flowAction(oid, l182m01a,
							true, procResult);
					if (CollectionUtils.isNotEmpty(existBrSkipList)) {
						logger.trace("skipBr:["
								+ StringUtils.join(existBrSkipList, "、") + "]");
					}
				}
			} else if (Util.equals("genL180M01A", act)) {
				String userId = rq.optString("userId");
				String unitNo = rq.optString("unitNo");
				String unitType = rq.optString("unitType");
				String par_arr = rq.optString("par_arr");
				List<String> existBrSkipList = new ArrayList<String>();
				String[] dataSplit = Util.trim(par_arr).split("\\|");

				List<Date> baseDateList = new ArrayList<Date>();
				List<String> brList = new ArrayList<String>();
				for (String item_branch_basedata : dataSplit) {
					String[] item = item_branch_basedata.split("\\^");
					if (item == null || item.length != 2) {
						throw new CapException("分項資料 " + item_branch_basedata
								+ " 格式錯誤", getClass());
					}
					String branch = item[0];
					String str_basedate = item[1];
					Date baseDate = CapDate.parseDate(str_basedate + "-01");
					if (baseDate == null) {
						throw new CapException(branch + "分行的資料年月:"
								+ str_basedate + "錯誤", getClass());
					}
					baseDateList.add(baseDate);
					brList.add(branch);
				}
				_produceL180M01A_from2(userId, unitNo, unitType, baseDateList,
						brList, existBrSkipList);
			} else if (Util.equals("sendBr_genL170M01A", act)) {
				String mainOid = rq.optString(EloanConstants.MAIN_OID);
				String autoGetLoanData = rq.optString(LrsUtil.K_GET_LOAN_DATA);
				String decisionExpr = rq.optString("decisionExpr");
				String userId = rq.optString("userId");
				String unitNo = rq.optString("unitNo");
				// ---
				if (Util.isEmpty(Util.trim(autoGetLoanData))) {
					autoGetLoanData = "N";
				}

				L180M01A meta = retrialService.findL180M01A_oid(mainOid);
				if (meta != null) {
					String oid = StringEscapeUtils.escapeSql(meta.getOid());
					Map<String, String> paramMap = new HashMap<String, String>();
					paramMap.put(LrsUtil.K_GET_LOAN_DATA, autoGetLoanData);

					// =============
					if (Util.equals(RetrialDocStatusEnum.已核准.getCode(),
							meta.getDocStatus())) {

						flowSimplifyService.flowNext(oid,
								decisionExpr, paramMap);

						generate_L170M01A(meta, autoGetLoanData, userId, unitNo);

						tempDataService.deleteByMainId(meta.getMainId());
						docCheckService.unlockDocByMainIdUser(meta.getMainId(),
								userId);
					} else if (Util.equals(
							RetrialDocStatusEnum.已產生覆審名單報告檔.getCode(),
							meta.getDocStatus())) {
						generate_L170M01A(meta, autoGetLoanData, userId, unitNo);
					}
				}
			} else if (Util.equals("l170m01a_approveToEnd", act)) {
				// 在 JS 端指定 900 - BATCH
				String userId = rq.optString("userId");
				String unitNo = rq.optString("unitNo");
				_approveToEnd(userId, unitNo);
			} else if (Util.equals("imp_l170m01b", act)) {
				List<String> errMsg = new ArrayList<String>();
				List<L170M01A> list = impBySelOrDate(rq, errMsg);

				if (CollectionUtils.isEmpty(errMsg)) {
					for (L170M01A meta : list) {
						retrialService.importLNtoL170M01B(meta);
						if (true) {
							Map<String, String> showItem = new HashMap<String, String>();
							lms1700Service.impData(meta, "07", showItem);
							lms1700Service.impData(meta, "08", showItem);
						}
					}
				} else {
					throw new CapMessageException(
							StringUtils.join(errMsg, "，"), getClass());
				}

			} else if (Util.equals("imp_l170m01c", act)) {
				List<String> errMsg = new ArrayList<String>();
				List<L170M01A> list = impBySelOrDate(rq, errMsg);

				if (CollectionUtils.isEmpty(errMsg)) {
					for (L170M01A l170m01a : list) {
						lms1700Service.setFinToDoc(l170m01a);
					}
				} else {
					throw new CapMessageException(
							StringUtils.join(errMsg, "，"), getClass());
				}
			} else if (Util.equals("update_ptMgrId", act)) {
				List<String> errMsg = new ArrayList<String>();
				List<L170M01A> list = impBySelOrDate(rq, errMsg);

				if (CollectionUtils.isEmpty(errMsg)) {
					Map<String, String> ownBrId_mgrId_map = new HashMap<String, String>();
					for (L170M01A l170m01a : list) {
						if(Util.equals(LrsUtil.CTLTYPE_價金履約, l170m01a.getCtlType())){
							continue;
						}
						String ownBrId = l170m01a.getOwnBrId();
						if (ownBrId_mgrId_map.containsKey(ownBrId)) {
							continue;
						}

						IBranch branch = branchService.getBranch(ownBrId);
						String mgrId = Util.trim(branch.getBrnMgr());

						ownBrId_mgrId_map.put(ownBrId, mgrId);
					}

					String ptMgrId = Util.trim(rq.optString("ptMgrId"));
					if (Util.isNotEmpty(ptMgrId)) {
						for (String key : ownBrId_mgrId_map.keySet()) {
							ownBrId_mgrId_map.put(key, ptMgrId);
						}
					}

					for (L170M01A l170m01a : list) {
						if(Util.equals(LrsUtil.CTLTYPE_價金履約, l170m01a.getCtlType())){
							continue;
						}
						lms1700Service.update_ptMgrId(l170m01a, Util
								.trim(ownBrId_mgrId_map.get(l170m01a
										.getOwnBrId())));
					}
				} else {
					throw new CapMessageException(
							StringUtils.join(errMsg, "，"), getClass());
				}
			} else if (Util.equals("update_l170m01a_rpid", act)) {

				// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
				lms1700Service.update_l170m01a_rpid(rq);

			} else if (Util.equals("gen_file_for_download", act)) {
				String serviceName = Util.trim(rq.optString("serviceName"));
				String fileName = Util.trim(rq.optString("zfile_loc"));
				if (Util.isNotEmpty(serviceName) && Util.isNotEmpty(fileName)) {
					try {
						PageParameters params = new CapMvcParameters();
						for (String _p : Util.trim(rq.optString("Z_ALL_KEY"))
								.split("\\|")) {
							params.put(_p, rq.optString(_p));
						}
						byte[] bytes = ((FileDownloadService) SpringContextHelper
								.getBean(serviceName)).getContent(params);

						FileUtils.forceMkdir(new File(FilenameUtils
								.getFullPath(fileName)));
						FileUtils.writeByteArrayToFile(new File(fileName),
								bytes);
					} catch (Exception e) {
						logger.error(StrUtils.getStackTrace(e));
					}
				}
			} else if (Util.equals("downloadZip_cntrNoPdf", act)) {
				String mainOid = Util.trim(rq.optString("mainOid"));
				String serviceName = Util.trim(rq.optString("serviceName"));
				String[] multiple_dataSplit = Util.trim(rq.optString("rptOid"))
						.split("\\|");

				L180M01A meta = retrialService.findL180M01A_oid(mainOid);
				if (meta != null) {
					String fieldId = LrsUtil.ATTCH_L180M01A_ZIP;
					String ext_file = ".zip";
					String fileName = "下載" + ext_file;
					try {
						if (true) {
							List<DocFile> docFiles = get_unDeleted_docFile(
									meta.getMainId(), fieldId);

							if (CollectionUtils.isNotEmpty(docFiles)) {
								Date sysDate = new Date();
								for (DocFile file : docFiles) {
									// 先用 delete，暫不用 clean
									// 若 user 正在產生中，又把 window
									// 關掉。再重產生，可能第1個file仍在寫入中
									if (file.getUploadTime() != null
											&& LMSUtil.cmpDate(
													file.getUploadTime(), "<",
													sysDate)) {
										fileService.clean(file.getOid());
									} else {
										fileService.delete(file.getOid());
									}
								}
							}
						}

						DocFile docFile = new DocFile();
						docFile.setBranchId(meta.getOwnBrId());
						docFile.setContentType("application/octet-stream");
						docFile.setMainId(meta.getMainId());
						docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
						docFile.setFieldId(fieldId);
						docFile.setSrcFileName(fileName);
						docFile.setUploadTime(CapDate.getCurrentTimestamp());
						docFile.setSysId(fileService.getSysId());
						docFile.setData(new byte[] {});
						docFile.setDeletedTime(null);
						docFile.setFlag("P");// 在前端標註為
						fileService.save(docFile, false);

						String current_docFile_oid = docFile.getOid();
						boolean isBreak = false;
						// ===============================
						File file = null;
						String filename = LMSUtil.getUploadFilePath(
								docFile.getBranchId(), docFile.getMainId(),
								docFile.getFieldId());
						if (true) {
							file = new File(filename);
							file.mkdirs();
						}
						Map<String, List<String>> idDup_cntrNo_map = new HashMap<String, List<String>>();
						Map<String, String> cntrNo_realPath_map = new HashMap<String, String>();
						cntrNo_realPath_map(serviceName, multiple_dataSplit,
								filename, idDup_cntrNo_map, cntrNo_realPath_map);

						if (true) {
							file = new File(filename + "/" + docFile.getOid()
									+ ext_file);
							if (true) {
								ZipOutputStream zops = new ZipOutputStream(
										new FileOutputStream(file));

								int total_size = idDup_cntrNo_map.size();
								boolean occur_error = false;
								int cnt = 0;
								for (String idDup : idDup_cntrNo_map.keySet()) {
									cnt++;

									ByteArrayOutputStream fos = new ByteArrayOutputStream();
									try {
										if (true) {
											PdfCopyFields copy = new PdfCopyFields(
													fos);
											for (String cntrNo : idDup_cntrNo_map
													.get(idDup)) {
												PdfReader pdf_file = new PdfReader(
														cntrNo_realPath_map
																.get(cntrNo));

												copy.addDocument(pdf_file);
											}
											copy.close();
										}

										byte[] bytes = fos.toByteArray();
										// ===
										String entryDesc = idDup + ".pdf";
										ZipEntry entry = new ZipEntry(entryDesc);

										// TODO 超過300筆時，putNextEntry會丟出
										// Exception
										_msg("[" + cnt + "/" + total_size
												+ "]put entry[" + entryDesc
												+ "]");

										zops.putNextEntry(entry);

										_msg("[" + cnt + "/" + total_size
												+ "]bf copy-zops");
										IOUtils.write(bytes, zops);
									} catch (Exception e) {
										occur_error = true;

										logger.error("=== writeEntry error, idDup="
												+ idDup);
										logger.error(StrUtils.getStackTrace(e));
									} finally {
										IOUtils.closeQuietly(fos);

										_msg("[" + cnt + "/" + total_size
												+ "]bf closeEntry - "
												+ occur_error);
										zops.closeEntry();

										if (occur_error) {
											break;
										}
									}
								}
								IOUtils.closeQuietly(zops);
							}
						}
						// =======================
						// 調整 flag 的狀態
						if (true) {
							List<DocFile> docFile_list = docFileDao
									.findAllByOid(new String[] { current_docFile_oid });
							if (CollectionUtils.isNotEmpty(docFile_list)) {
								docFile = docFile_list.get(0);
								docFile.setFlag(isBreak ? "B" : "");
								fileService.save(docFile, false);
							}
						}
						// =======================
						// 刪掉 .del.pdf
						if (true) {
							int procCnt = 0;
							if (true) {
								List<DocFile> docFiles = get_unDeleted_docFile(
										meta.getMainId(), fieldId);
								for (DocFile current_docFile : docFiles) {
									String flag = Util.trim(current_docFile
											.getFlag());
									if (Util.isEmpty(flag)
											|| Util.equals(flag, "B")) {

									} else if (Util.equals(flag, "P")) {
										procCnt++;
										break;
									}
								}

							}

							if (procCnt == 0) {
								String ext = LrsUtil.TEMP_PDF_EXT;
								if (ext.startsWith(".")) {
									ext = StringUtils.substring(ext, 1);
								}
								Collection<File> del_file_list = FileUtils
										.listFiles(new File(filename),
												new String[] { ext }, false);
								if (CollectionUtils.isNotEmpty(del_file_list)) {
									for (File delFile : del_file_list) {
										FileUtils.deleteQuietly(delFile);
									}
								}
							}
						}

					} catch (Exception e) {
						logger.error(StrUtils.getStackTrace(e));
					}
				}
			} else if (Util.equals("mail_lrs_processingOverDays", act)) {
				int overDays = Util.parseInt(rq.optString("overDays"));
				if (overDays > 0) {
					// ok
				} else {
					overDays = 14;// default value
				}
				boolean skipBrT1 = "Y".equals(Util.trim(rq
						.optString("skipBrT1")));
				boolean rMail = lms1800Service.mail_lrs_processingOverDays(
						overDays, skipBrT1);
				if (rMail) {

				} else {
					throw new CapException("mail_lrs_processingOverDays fail",
							getClass());
				}
			}
			isSuccess = true;
		} catch (Throwable e) {
			msg = e.getMessage();
			logger.error(msg, e);
			isSuccess = false;
		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;// 此json object 內已包含 SUCCESS
			result.element(WebBatchCode.P_RESPONSE,
					result.get(WebBatchCode.P_RC_MSG));
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, msg);
			result.element(WebBatchCode.P_RESPONSE, msg);
		}
		return result;
	}

	private void _msg(String s) {

	}

	private void cntrNo_realPath_map(String serviceName,
			String[] multiple_dataSplit, String dirName,
			Map<String, List<String>> idDup_cntrNo_map,
			Map<String, String> cntrNo_realPath_map) {

		if (true) {
			FileDownloadService pdf_service = ((FileDownloadService) SpringContextHelper
					.getBean(serviceName));

			int cnt = 0;
			int total_size = multiple_dataSplit.length;
			for (String rptOid : multiple_dataSplit) {
				cnt++;

				PageParameters params = new CapMvcParameters();
				params.put("rptOid", rptOid);
				String[] columnSplit = rptOid.split("\\^");
				if (columnSplit.length >= 5) {
					String custId = columnSplit[2];
					String dupNo = columnSplit[3];
					String cntrNo = columnSplit[4];
					// ===
					String str_target = dirName + "/" + cntrNo
							+ LrsUtil.TEMP_PDF_EXT;

					String idDup = LMSUtil
							.getCustKey_len10custId(custId, dupNo);
					if (!idDup_cntrNo_map.containsKey(idDup)) {
						idDup_cntrNo_map.put(idDup, new ArrayList<String>());
					}
					idDup_cntrNo_map.get(idDup).add(cntrNo);
					cntrNo_realPath_map.put(cntrNo, dirName + "/" + cntrNo
							+ LrsUtil.TEMP_PDF_EXT);
					// ===
					File target_pdf = new File(str_target);
					if (target_pdf.isFile() && target_pdf.exists()) {
						continue;
					}
					byte[] bytes = null;
					try {
						_msg("[" + cnt + "/" + total_size + "]bf getContent"
								+ cntrNo);
						bytes = pdf_service.getContent(params);
						_msg("[" + cnt + "/" + total_size + "]af getContent");
					} catch (Exception e) {
						logger.error("=== getContent error, cntrNo=" + cntrNo);
						logger.error(StrUtils.getStackTrace(e));
					} finally {
						if (bytes != null) {
							try {
								FileUtils.writeByteArrayToFile(target_pdf,
										bytes);
							} catch (IOException ioe) {
								logger.error(StrUtils.getStackTrace(ioe));
							}
						}
					}
				} else {
					_msg("[" + cnt + "/" + total_size + "]columnSplit.length=="
							+ columnSplit.length);
				}
			}
		}

	}

	private List<DocFile> get_unDeleted_docFile(String mainId, String field) {
		return fileService.findByIDAndName(mainId, field, "");
	}

	private List<L170M01A> impBySelOrDate(JSONObject rq, List<String> errMsg) {
		return lms1700Service.impBySelOrDate(Util.trim(rq.optString("oids")),
				Util.trim(rq.optString("retrialDate")),
				Util.trim(rq.optString("unitNo")), errMsg);
	}

	private void generate_L170M01A(L180M01A meta, String autoGetLoanData,
			String userId, String unitNo) {
		if (true) {
			lms1700Service.save_basicL170M01A(meta);
		}

		if (true) {
			List<L170M01A> l170m01a_list = retrialService.findL170M01A(meta);
			for (L170M01A l170m01a : l170m01a_list) {

				L170M01F l170m01f = retrialService.findL170M01F(l170m01a);
				if (l170m01f == null) {
					lms1700Service._genL170M01A(meta.getOwnBrId(), l170m01a,
							Util.equals("Y", autoGetLoanData));
				}

				if (Util.isEmpty(Util.trim(l170m01a.getDocStatus()))) {
					flowSimplifyService.flowStart(LMS1700Flow.LMS1700FLOW,
							l170m01a.getOid(), userId, unitNo);
				}
			}
		}
	}

	private void _produceL180M01A_from1(String userId, String unitNo,
			String unitType, Date baseDate, String[] _brList,
			List<String> existBrSkipList) throws CapException {
		if (Util.isEmpty(userId)) {
			throw new CapException("userId is empty", getClass());
		}
		if (Util.isEmpty(unitNo)) {
			throw new CapException("unitNo is empty", getClass());
		}

		if (_brList == null || _brList.length == 0) {
			throw new CapException("gen_br is empty:", getClass());
		}

		for (String branch : _brList) {
			// ======================
			// 當該分行 無 ELF411 轉 ELF412 的記錄(L180M01Z),略過
			if (retrialService.existL180M01Z_sysMonth(branch) == false) {
				throw new CapException("NO L180M01Z:" + branch, getClass());
			}

			// ---
			boolean r = lms1800Service.produce(branch, baseDate, userId,
					unitNo, unitType, "1" // 1:預約單, 2:手動
					, existBrSkipList);
			if (r == false) {
				throw new CapException("produce[" + branch + " , "
						+ TWNDate.toAD(baseDate) + "]fail", getClass());
			}
		}
	}

	private void _produceL180M01A_from2(String userId, String unitNo,
			String unitType, List<Date> baseDateList, List<String> brList,
			List<String> existBrSkipList) throws CapException {
		int size = brList.size();
		for (int i = 0; i < size; i++) {
			String branch = brList.get(i);
			Date baseDate = baseDateList.get(i);
			// ======================
			// 當該分行 無 ELF411 轉 ELF412 的記錄(L180M01Z),略過
			if (retrialService.existL180M01Z_sysMonth(branch) == false) {
				continue;
			}
			// ---
			boolean r = lms1800Service.produce(branch, baseDate, userId,
					unitNo, unitType, "2" // 1:預約單, 2:手動
					, existBrSkipList);
			if (r == false) {
				throw new CapException(branch + "執行失敗", getClass());
			}
		}
	}

	private void elf411_to_elf412(String limit_branch) throws CapException {
		List<String> branchList = new ArrayList<String>();
		branchList.addAll(retrialService.getBranch(
				Util.isNotEmpty(limit_branch) ? limit_branch : "900").keySet());

		Date sysMonth_1st = CrsUtil.get_sysMonth_1st();
		String tELF411_DATADATE = LrsUtil.elf412_rocDateStr_from_Date(CapDate
				.addMonth(sysMonth_1st, -1));
		if (misELF411Service.isELF411Ready(tELF411_DATADATE) == false) {
			return;
		}

		for (String branch : branchList) {
			if (retrialService.overSeaProgram(branch)) {
				continue;
			}

			try {
				lms1810Service.gfnStartNorthBranch_BuildData(
						lrsConstants.mode._2, branch, sysMonth_1st);
			} catch (Exception e) {
				logger.error("【" + branch + "】" + StrUtils.getStackTrace(e));
			}
		}
	}

	private void _approveToEnd(String userId, String unitNo)
			throws CapException {
		if (Util.isEmpty(userId)) {
			throw new CapException("userId is empty", getClass());
		}
		if (Util.isEmpty(unitNo)) {
			throw new CapException("unitNo is empty", getClass());
		}
		lms1800Service.batch_toEnd(userId, unitNo);
	}
}
