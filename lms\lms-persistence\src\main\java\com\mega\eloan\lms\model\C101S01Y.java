/*
 * C101S01Y.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 個金地政士名單 **/
@NamedEntityGraph(name = "C101S01Y-entity-graph", attributeNodes = { @NamedAttributeNode("c101m01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01Y", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class C101S01Y extends GenericBean implements IDataObject, IDocObject {

    private static final long serialVersionUID = 1L;

    /**
     * oid
     * <p/>
     * ROWID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
    @Size(max = 32)
    @Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
    private String oid;

    /** 文件編號 **/
    @Size(max = 32)
    @Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
    private String mainId;
    
    /** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

    /** 地政士姓名 **/
    @Size(max = 120)
    @Column(name = "LAANAME", length = 120, columnDefinition = "VARCHAR(120)")
    private String laaName;

    /** 地政士證書-年 **/
    @Size(max = 3)
    @Column(name = "LAAYEAR", length = 3, columnDefinition = "VARCHAR(3)")
    private String laaYear;

    /** 地政士證書-字號 **/
    @Size(max = 15)
    @Column(name = "LAAWORD", length = 15, columnDefinition = "VARCHAR(15)")
    private String laaWord;

    /** 地政士證書-流水號 **/
    @Size(max = 6)
    @Column(name = "LAANO", length = 6, columnDefinition = "VARCHAR(6)")
    private String laaNo;

    /** 地政士事務所統編 **/
    @Column(name = "LAAOFFICEID", length = 10, columnDefinition = "VARCHAR(10)")
    private String laaOfficeId;

    /** 地政士事務所名稱 **/
    @Column(name = "LAAOFFICE", length = 150, columnDefinition = "VARCHAR(150)")
    private String laaOffice;

    /** 地政士黑名單警示名單敘述 */
    @Size(max=300)
    @Column(name="LAADESC", length=300, columnDefinition="VARCHAR(300)")
    private String laaDesc;
    
    /** 地政士比對結果(ref C900M01H.ctlFlag){0:比對後無match資料, '':未執行比對} **/
    @Column(name = "LAACTLFLAG", length = 1, columnDefinition = "VARCHAR(1)")
    private String laaCtlFlag;
    
    /** 地政士比對 拒絕地政士，判斷ctl是否等於5，且 符合 永慶房屋直營店或信義房屋名義仲介成交案件 及 懲戒紀錄是否屬地政士法第17條 ，則可續做**/
	@Size(max=1)
	@Column(name="LAAMATCHRULEFLAG", length=1, columnDefinition="VARCHAR(1)")
	private String laaMatchRuleFlag;

    /** 查詢地政士黑名單日期 */
    @Column(name="LAAQUERYDATE", columnDefinition="DATE")
    private Date laaQueryDate;

    /**
     * 取得oid
     * <p/>
     * ROWID
     */
    public String getOid() {
        return this.oid;
    }

    /**
     * 設定oid
     * <p/>
     * ROWID
     **/
    public void setOid(String value) {
        this.oid = value;
    }

    /** 取得文件編號 **/
    public String getMainId() {
        return this.mainId;
    }

    /** 設定文件編號 **/
    public void setMainId(String value) {
        this.mainId = value;
    }

    /** 取得地政士姓名 **/
    public String getLaaName() {
        return laaName;
    }
    /** 設定地政士姓名 **/
    public void setLaaName(String value) {
        this.laaName = value;
    }

    /** 取得地政士證書-年 **/
    public String getLaaYear() {
        return laaYear;
    }
    /** 設定地政士證書-年 **/
    public void setLaaYear(String value) {
        this.laaYear = value;
    }

    /** 取得地政士證書-字號 **/
    public String getLaaWord() {
        return laaWord;
    }
    /** 設定地政士證書-字號 **/
    public void setLaaWord(String value) {
        this.laaWord = value;
    }

    /** 取得地政士證書-流水號 **/
    public String getLaaNo() {
        return laaNo;
    }
    /** 設定地政士證書-流水號 **/
    public void setLaaNo(String value) {
        this.laaNo = value;
    }

    public String getLaaOfficeId() {
        return laaOfficeId;
    }

    public void setLaaOfficeId(String laaOfficeId) {
        this.laaOfficeId = laaOfficeId;
    }

    public String getLaaOffice() {
        return laaOffice;
    }

    public void setLaaOffice(String laaOffice) {
        this.laaOffice = laaOffice;
    }

    public String getLaaDesc() {
        return this.laaDesc;
    }
    public void setLaaDesc(String value) {
        this.laaDesc = value;
    }

    /**
     * join
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({
            @JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false)})
    private C101M01A c101m01a;

    public void setC101m01a(C101M01A c101m01a) {
        this.c101m01a = c101m01a;
    }

    public C101M01A getC101m01a() {
        return c101m01a;
    }

	public String getLaaCtlFlag() {
		return laaCtlFlag;
	}

	public void setLaaCtlFlag(String laaCtlFlag) {
		this.laaCtlFlag = laaCtlFlag;
	}

	public String getLaaMatchRuleFlag() {
		return laaMatchRuleFlag;
	}

	public void setLaaMatchRuleFlag(String laaMatchRuleFlag) {
		this.laaMatchRuleFlag = laaMatchRuleFlag;
	}

	public Date getLaaQueryDate() {
		return laaQueryDate;
	}

	public void setLaaQueryDate(Date laaQueryDate) {
		this.laaQueryDate = laaQueryDate;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getDupNo() {
		return dupNo;
	}

	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}


}
