<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="innerPageBody">
			<script type="text/javascript"> 
   			 	loadScript('pagejs/lrs/LMS1705M01Page');
			</script>
            <div class="button-menu funcContainer" id="buttonPanel">
                <th:block th:if="${_btnDOC_EDITING_visible}">
                    <button id="btnSave">
                        <span class="ui-icon ui-icon-jcs-04"></span>
                        <th:block th:text="#{'button.save'}">
                            儲存
                        </th:block>
                    </button>
                    <button id="btnRemover">
                        <span class="ui-icon ui-icon-jcs-02"></span>
                        <th:block th:text="#{'button.remover'}">
                            移受檢單位登錄
                        </th:block>
                    </button>
                </th:block>
                <!--待受檢調單位回覆  -->
                <th:block th:if="${_btnDOC_RECEIVES_visible}">
                    <button id="btnSend4">
                        <span class="ui-icon ui-icon-jcs-02">登錄受檢單位洽辦情形</span>
                        <th:block th:text="#{'button.send4'}">
                        </th:block>
                    </button>
                    <button id="btnComplete">
                        <span class="ui-icon ui-icon-jcs-02">編製完成</span>
                        <th:block th:text="#{'button.editOK'}">
                        </th:block>
                    </button>
                </th:block><!--編製完成  -->
                <th:block th:if="${_btn_COMPLETE_visible}">
                    <button id="btnSend">
                        <span class="ui-icon ui-icon-jcs-02"></span>
                        <th:block th:text="#{'button.send'}">
                            呈主管覆核
                        </th:block>
                    </button>
                </th:block>
                <!--待覆核  -->
                <th:block th:if="${_btnWAIT_CHECK_visible}">
                    <button id="btnCheck">
                        <span class="ui-icon ui-icon-jcs-02"></span>
                        <th:block th:text="#{'button.check'}">
                            覆核
                        </th:block>
                    </button>
                </th:block>
                <!--已覆核 -->
                <th:block th:if="${_btn_APPROVE_visible}">
                </th:block>
                <button id="btnPrint" class="forview">
                    <span class="ui-icon ui-icon-jcs-03"></span>
                    <th:block th:text="#{'button.print'}">
                        列印
                    </th:block>
                </button>
                <!--離開-->
                <button id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <th:block th:text="#{'button.exit'}">
                        <!--離開-->
                    </th:block>
                </button>
            </div>
            <div class="tit2 color-black">
                <th:block th:text="#{'page.title1'}">
                </th:block>:
                (<span class="color-red" id="typCd1"></span>)<span class="color-blue" id="custId1"></span>&nbsp;<span class="color-blue" id="dupNo1"></span>&nbsp;&nbsp;<span class="color-blue" id="custName1"></span>
            </div>
            <div class="tabs doc-tabs">
                <ul>
                    <li>
                        <a href="#tab-01" goto="01"><b>
                                <th:block th:text="#{'lms1705.tit01'}">
                                    文件資訊
                                </th:block>
                            </b></a>
                    </li>
                    <li>
                        <a href="#tab-02" goto="02"><b>
                                <th:block th:text="#{'lms1705.tit02'}">
                                    一般授信資料
                                </th:block>
                            </b></a>
                    </li>
                    <li>
                        <a href="#tab-03" goto="03"><b>
                                <th:block th:text="#{'lms1705.tit03'}">
                                    最近三次業務及財務資料
                                </th:block>
                            </b></a>
                    </li>
                    <li>
                        <a href="#tab-04" goto="04"><b>
                                <th:block th:text="#{'lms1705.tit04'}">
                                    覆審內容
                                </th:block>
                            </b></a>
                    </li>
                    <li>
                        <a href="#tab-05" goto="05"><b>
                                <th:block th:text="#{'lms1705.tit05'}">
                                    覆審意見及受檢單位洽辦情形
                                </th:block>
                            </b></a>
                    </li>
                </ul>
                <div class="tabCtx-warp">
                   	<div th:id="${tabIdx}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
                </div>
            </div><!--登錄受檢單位洽辦情形，登錄完成後請按編製完成-->
            <div id="lms1705SENT" style="display:none">
                <FORM id="lms1705s05BOX">
                    <textarea id="lms1705s05BOXtext" name="lms1705s05BOXtext" class="save txt_mult" maxlengthC="128" style="width:400px;height:100px;">
                    </textarea>
                </FORM>
                <div id="printView" style="display:none;">
                    <div id="printGrid"></div>
                </div>
            </div><!--覆核視窗-->
            <div id="openCheckBox" style="display:none">
                <div>
                    <label>
	                    <input name="checkRadio" type="radio" value="2">
	                    <th:block th:text="#{'lms1705.choose2'}">
	                        核准
	                    </th:block>
					</label>
                    <br/>
                    <label>
                    	<input name="checkRadio" type="radio" value="1">
	                    <th:block th:text="#{'lms1705.choose1'}">
	                        退回經辦修改
	                    </th:block>
					</label>
                    <br/>
                </div>
            </div>
			<div id="selectBossBox"  style="display:none;">
			  <form id="selectBossForm">
	         	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                 <tr >
	            		<td class="hd1" width="50%"><th:block th:text="#{'L170M01A.managerId'}"><!-- 經副襄理--></th:block>&nbsp;&nbsp;</td>
	                    <td width="50%"><select id="manager" name="manager" class="boss"></select></td>
	                 </tr>
	           	 </table>
				</form>
  			</div>
            <!-- 控制LMS170MO1畫面上的按鈕等功能 -->
			
        </th:block>
    </body>
</html>
