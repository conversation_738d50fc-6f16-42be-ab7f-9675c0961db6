/* 
 * C900M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01C;

/** 產品種類關聯檔 **/
public interface C900M01CDao extends IGenericDao<C900M01C> {

	C900M01C findByOid(String oid);
	
	List<C900M01C> getAll();

	List<C900M01C> findByMainId(String mainId);

	C900M01C findByUniqueKey(String prodKind, String subjCode, String type);

	List<C900M01C> findByIndex01(String prodKind, String subjCode, String type);
}