package com.mega.eloan.lms.base.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.acl.EjcicAcl;
import com.mega.eloan.common.acl.EtchAcl;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.model.L120M01A;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 相關文件-連保人分頁
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/baselmss08a/{page}")
public class LMSS08APage extends AbstractOutputPage {

	@Autowired
	L120M01ADao l120m01aDao;

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		EjcicAcl ejcicAcl = new EjcicAcl();
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// add(new EloanAcl("_s41t2btnQryEjcic", ejcicAcl));		
		// add(new EloanAcl("_s41t5f3btnQryEjcic", ejcicAcl));	
		// add(new EloanAcl("_s41t7btnQryEjcic", ejcicAcl));
		model.addAttribute("_s41t2btnQryEjcic_visible", ejcicAcl.isVisible());
		model.addAttribute("_s41t5f3btnQryEjcic_visible", ejcicAcl.isVisible());
		model.addAttribute("_s41t7btnQryEjcic_visible", ejcicAcl.isVisible());
		
		EtchAcl etachAcl = new EtchAcl();
		model.addAttribute("_s41t7btnQryEtch_visible", etachAcl.isVisible());
//		// #1809 (#443)********** 補已刪除資料 ********** start
//		add(new AclLabel("_s41t7btnReQry", params, getDomainClass(),
//				AuthType.Query, false, CreditDocStatusEnum.海外_已核准,
//				CreditDocStatusEnum.海外_已核准).setAcl(new IAcl(params)));
//		// #1809 (#443)********** 補已刪除資料 ********** end		
		String docType = Util.trim(params.getString("docType"));
		setNeedHtml(true);		// need html
		if(Util.isEmpty(docType)){
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			L120M01A meta = l120m01aDao.findByMainId(mainId);
			if(meta == null){
				meta = new L120M01A();
			}
			docType = Util.trim(meta.getDocType());
		}
		
//		if(UtilConstants.Casedoc.DocType.個金.equals(docType)){
//			setJavascript(new String[] { "pagejs/cls/CLSS08APage01.js" });
//		}else{
//			setJavascript(new String[] { "pagejs/lns/LMSS08APage01.js" });
//		}
		setJavascript(new String[] { "pagejs/base/LMSS08APage01.js" });
		
		return "&nbsp;";
	}

	@Override
	protected String getViewName() {
		// UPGRADE: 待確認是否一樣換成Thymeleaf的None.html頁面
		return "common/pages/None";
	}
}
