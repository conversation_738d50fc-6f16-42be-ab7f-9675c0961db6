/* 
 * RPAQueryService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120S04W;
import com.mega.eloan.lms.model.C126S01A;
import com.mega.eloan.lms.model.L820M01W;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.service.ICapService;

/**
 * <pre>
 * BY 專案共用Service
 * </pre>
 * 
 * @since 2021/04/20
 * <AUTHOR>
 * @version
 * 
 */
public interface RPAProcessService extends ICapService {
	
	public void deleteBeforeQueryData(String mainId, String dataCustomerNo);
	
	public String gotoRPAJobs(C101S04W c101s04w) throws CapException;
	
	public C101S04W getC101S04WBy (String mainId, String custId);
	
	public C120S04W getC120S04WBy (String mainId, String custId);

	public String getDataStatus(String status, String returnData);

	public void saveC126S01A(C126S01A c126s01a);

	public String gotoRPAJobsForGettingRealEstateAgentCertNo(C126S01A c126s01a) throws CapException;

	public C126S01A getC126S01ABy(String mainId);
	
	public List<Map<String, Object>> getC126S01ARealtorInfo(String mainId) throws CapMessageException, JsonParseException, JsonMappingException, IOException;

	public void deleteBeforeQueryRealEstateAgentCertNo(String mainId);
	
	public String gotoRPAJobsForL820(L820M01W l820m01w) throws CapException;
	
	public L820M01W getL820M01WBy (String mainId, String custId);
	
	public void saveL820M01W(L820M01W l820m01w);
	
	public void deleteBeforeQueryL820(String mainId, String dataCustomerNo);
	
}