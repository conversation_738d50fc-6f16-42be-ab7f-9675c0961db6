/* 
 * L130S01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L130S01B;

/** 異常通報表參貸行檔 **/
public interface L130S01BDao extends IGenericDao<L130S01B> {

	L130S01B findByOid(String oid);
	
	List<L130S01B> findByMainId(String mainId);
	
	L130S01B findByUniqueKey(String mainId);

	List<L130S01B> findByIndex01(String mainId);
}