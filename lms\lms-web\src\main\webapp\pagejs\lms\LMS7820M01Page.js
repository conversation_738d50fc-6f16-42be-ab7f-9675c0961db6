var inits={
		fhandle:"lms7820m01formhandler"
	};
//var auth = (responseJSON ? responseJSON.Auth : {}); //權限
//if (auth.readOnly){
//	$("form").readOnlyChilds(true).find("button").remove();
//}
	$(function(){
		$.form.init({
	        formHandler: inits.fhandle,
	        formPostData:{//把form上貼上資料
	        	formAction : "queryL782m01a",
	        	oid : responseJSON.oid
	        },
			loadSuccess:function(json){
		
			}
	    });//close form init
	
	
	var btn = $("#buttonPanel");
	btn.find("#btnSave").click(function(showMsg){
			
			$.ajax({
				handler :inits.fhandle,
				data : {//把資料轉成json
					formAction : "saveL782m01a",
					oid:$("#oid").val(),
					page : responseJSON.page,
					txCode:responseJSON.txCode,
					showMsg : showMsg,
				}
			}).done(function(obj) {
				$('body').injectData(obj);
				CommonAPI.triggerOpener("gridview","reloadGrid");
			});
		
	}).end().find("#btnDelete").click(function(){
		CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
			if(b){					
				$.ajax({
					handler :deleteL782m01a,
					data : {
						formAction : "delete",
						mainOid : $("#oid").val()
					}
				});
			}
		});
	}).end().find("#btnPrint").click(function(){
		$.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
            	mainId : responseJSON.mainId,
            	mainOid : $("#oid").val(),
				fileDownloadName : "lms1605r01.pdf",
				serviceName : "lms1605r01rptservice"
            }
        });
	}).end().find("#btnExit").click(function(){
		setCloseConfirm(true);
	});
});



