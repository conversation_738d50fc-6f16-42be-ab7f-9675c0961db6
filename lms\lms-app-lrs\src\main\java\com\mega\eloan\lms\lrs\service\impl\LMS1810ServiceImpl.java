package com.mega.eloan.lms.lrs.service.impl;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DurationFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.common.RO412;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L180M01ZDao;
import com.mega.eloan.lms.dao.L181M01ADao;
import com.mega.eloan.lms.lrs.constants.lrsConstants;
import com.mega.eloan.lms.lrs.service.LMS1810Service;
import com.mega.eloan.lms.mfaloan.bean.ELF411;
import com.mega.eloan.lms.mfaloan.bean.ELF412;
import com.mega.eloan.lms.mfaloan.bean.ELF412B;
import com.mega.eloan.lms.mfaloan.bean.ELF412C;
import com.mega.eloan.lms.mfaloan.service.MisELF411Service;
import com.mega.eloan.lms.mfaloan.service.MisELF412BService;
import com.mega.eloan.lms.mfaloan.service.MisELF412CService;
import com.mega.eloan.lms.mfaloan.service.MisELF412Service;
import com.mega.eloan.lms.mfaloan.service.MisELF447nService;
import com.mega.eloan.lms.mfaloan.service.MisELF495Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L180A01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L180M01C;
import com.mega.eloan.lms.model.L180M01D;
import com.mega.eloan.lms.model.L180M01Z;
import com.mega.eloan.lms.model.L181M01A;
import com.mega.eloan.lms.model.L181M01B;
import com.mega.eloan.lms.model.L186M01A;

@Service
public class LMS1810ServiceImpl extends AbstractCapService implements
		LMS1810Service {
	private static final Logger logger = LoggerFactory
			.getLogger(LMS1810ServiceImpl.class);
	private static final int MAXLEN_L180M01B_RLTGUARANTOR = StrUtils
			.getEntityFileldLegth(L180M01B.class, "rltGuarantor", 300);
	private static final int MAXLEN_L180M01B_RLTBORROWER = StrUtils
			.getEntityFileldLegth(L180M01B.class, "rltBorrower", 300);
	private static final int MAXLEN_ELF412_MEMO = StrUtils
			.getEntityFileldLegth(ELF412.class, "elf412_memo", 202);
	@Resource
	RetrialService retrialService;

	@Resource
	L180M01ZDao l180m01zDao;

	@Resource
	L181M01ADao l181m01aDao;

	@Resource
	MisLNF022Service misLNF022Service;

	@Resource
	MisELF411Service misELF411Service;

	@Resource
	MisELF412Service misELF412Service;

	@Resource
	MisELF495Service misELF495Service;

	@Resource
	MisELLNGTEEService misELLNGTEEService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisELF412BService misELF412BService;

	@Resource
	MisELF447nService misELF447nService;

	// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	@Resource
	MisELF412CService misELF412CService;

	@Resource
	MisMISLN20Service misLNF020Service;

	// J-108-0078_05097_B1001 Web e-Loan企金授信覆審系統修改首次往來之新授信戶應辦理覆審之期限
	@Resource
	private SysParameterService sysParameterService;

	@Resource
	LMSService lmsService;

	@Override
	public L181M01A findInProcessData(String branch, String[] docStatusArr,
			String ctlType, String ownBrId) {
		return l181m01aDao.findInProcessData(branch, null, null, docStatusArr,
				ctlType, ownBrId);
	}

	@Override
	public L181M01A findInProcessData(String branch, String custId,
			String dupNo, String[] docStatusArr, String ctlType, String ownBrId) {
		return l181m01aDao.findInProcessData(branch, custId, dupNo,
				docStatusArr, ctlType, ownBrId);
	}

	private Object[] _MINCHKDATE(String mode, Date sysDate, String brNo,
			RO412 ro412, Map<String, String> elf412_DBUOBUCOID) {
		Date _date = null;
		String _ID = "";
		// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
		String[] cond_nckdFlagArr = { "", "0", "1", "2", "3", "4", "5", "8",
				"9", "10" };
		for (String lnf022_id : elf412_DBUOBUCOID.keySet()) {
			ELF412 elf412 = misELF412Service.findByPk(brNo,
					StringUtils.substring(lnf022_id, 0, 10),
					StringUtils.substring(lnf022_id, 10));
			if (elf412 == null) {
				continue;
			}
			String elf412_nckdFlag = Util.trim(elf412.getElf412_nckdFlag());
			if (!CrsUtil.inCollection(elf412_nckdFlag, cond_nckdFlagArr)) {
				continue;
			}

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			Date ndDate = retrialService.gfnCTL_Caculate_DueDate(mode, sysDate,
					new RO412(ro412, elf412), null, null);

			if (CrsUtil.isNOT_null_and_NOTZeroDate(ndDate)) {
				boolean setV = false;
				if (Util.isEmpty(_ID)) {
					// 第1次進到loop
					setV = true;
				} else {
					// 第2,3次進到loop
					setV = LMSUtil.cmpDate(ndDate, "<", _date);
				}

				if (setV) {
					_date = ndDate;
					_ID = lnf022_id;
				}
			}
		}

		Object[] arr = new Object[2];
		arr[0] = _date;
		arr[1] = _ID;
		return arr;
	}

	@Override
	public String[] gfnGetNEW_DBUOBU_MINCHKDATE(String mode, String brNo,
			RO412 ro412, Map<String, String> elf412_DBUCOID,
			Map<String, String> elf412_OBUCOID) {
		String mainCoFlag = "";
		String minChkDate = "";
		String mainChkID = "";
		// ===
		if (!CollectionUtils.isEmpty(elf412_DBUCOID)
				|| !CollectionUtils.isEmpty(elf412_OBUCOID)) {
			Date CHKDATE_DBU = null;
			String CHKDATE_DBUID = "";

			Date CHKDATE_OBU = null;
			String CHKDATE_OBUID = "";

			Date sysDate = CapDate.getCurrentTimestamp();

			if (!CollectionUtils.isEmpty(elf412_DBUCOID)) {
				Object[] arr = _MINCHKDATE(mode, sysDate, brNo, ro412,
						elf412_DBUCOID);
				CHKDATE_DBU = (Date) arr[0];
				CHKDATE_DBUID = (String) arr[1];
			}
			if (!CollectionUtils.isEmpty(elf412_OBUCOID)) {
				Object[] arr = _MINCHKDATE(mode, sysDate, brNo, ro412,
						elf412_OBUCOID);
				CHKDATE_OBU = (Date) arr[0];
				CHKDATE_OBUID = (String) arr[1];
			}

			boolean chooseOBU = false;
			if (Util.isNotEmpty(CHKDATE_DBUID)
					&& Util.isNotEmpty(CHKDATE_OBUID)) {
				chooseOBU = LMSUtil.cmpDate(CHKDATE_OBU, "<", CHKDATE_DBU);
			} else if (Util.isNotEmpty(CHKDATE_DBUID)
					&& Util.isEmpty(CHKDATE_OBUID)) {
				chooseOBU = false;
			} else if (Util.isEmpty(CHKDATE_DBUID)
					&& Util.isNotEmpty(CHKDATE_OBUID)) {
				chooseOBU = true;
			}
			if (chooseOBU) {
				mainCoFlag = "OBU";
				minChkDate = Util.trim(TWNDate.toAD(CHKDATE_OBU));
				mainChkID = CHKDATE_OBUID;
			} else {
				mainCoFlag = "DBU";
				minChkDate = Util.trim(TWNDate.toAD(CHKDATE_DBU));
				mainChkID = CHKDATE_DBUID;
			}
		}
		// =================
		String[] r = new String[3];
		r[0] = mainCoFlag;
		r[1] = minChkDate;
		r[2] = mainChkID;
		return r;
	}

	@Override
	public List<String> gfnCTL_Get_ELF411_CONTRACT(String branch,
			String custId, String dupNo, Date elf412_newDate) {
		List<String> r = new ArrayList<String>();

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		// ELF411 2017/06 額度序號 025410600065
		// 025分行發生同一月同一個ID同一個額度序號有兩筆，所有資料都一樣除了額度不同
		// select * from mis.elf411 where (ELF411_DATAYM = '010606' or
		// ELF411_DATAYM = '000000' ) and ELF411_BRNO = '025' and
		// ELF411_CONTRACT = '025410600065' and ELF411_CUSTID = 'MHZ0013568'
		Map<String, String> process_data = new HashMap<String, String>();

		for (ELF411 elf411 : misELF411Service.find_ELF411_NEWADD_notEmpty(
				LrsUtil.elf412_rocDateStr_from_Date(elf412_newDate), branch,
				custId, dupNo)) {

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			// String buildKey = LMSUtil.getCustKey_len10custId(
			// elf411.getElf411_custid(), elf411.getElf411_dupno())
			// + Util.trim(elf411.getElf411_contract());

			String buildKey = Util.trim(elf411.getElf411_contract());
			if (process_data.containsKey(buildKey)) {
				continue;
			}

			r.add(Util.trim(elf411.getElf411_contract()));

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			process_data.put(buildKey, buildKey);
		}
		return r;
	}

	private void _copyL180M01B(String branch, L180M01A meta, L180M01B l180m01b,
			Map<String, Object> elf412data) {
		ELF412 elf412 = new ELF412();
		DataParse.map2Bean(elf412data, elf412);
		// ------
		String BUSCD = Util.trim(elf412data.get("BUSCD"));
		String BUSSKIND = Util.trim(elf412data.get("BUSSKIND"));
		String ECONM07A = Util.trim(elf412data.get("ECONM07A"));
		String CNAME = LMSUtil.getNotEmptyVal_str(elf412data, "CNAME", "ENAME");
		String ECONM = Util.trim(elf412data.get("ECONM"));
		String SUP3CNM = LMSUtil.getNotEmptyVal_str(elf412data, "SUP1CNM",
				"SUP3CNM");
		// J-110-0396 配合授審處，E-Loan企金授信覆審系統修改每月需覆審名單報表，優化由系統排除免覆審名單等事項。
		String ELF415_NREVIEW = Util.trim(elf412data.get("ELF415_NREVIEW"));
		String ELF415_DATAYM = Util.trim(elf412data.get("ELF415_DATAYM"));

		String rlt_Borrower = misELLNGTEEService.get_lrs_Rlt_Borrower(branch,
				elf412.getElf412_custId(), elf412.getElf412_dupNo());
		String rlt_Guarantor = misELLNGTEEService.get_lrs_Rlt_Guarantor(branch,
				elf412.getElf412_custId(), elf412.getElf412_dupNo());

		l180m01b.setMainId(meta.getMainId());
		l180m01b.setCustId(elf412.getElf412_custId());
		l180m01b.setDupNo(elf412.getElf412_dupNo());
		l180m01b.setTypCd(LrsUtil.isCustId_Z(elf412.getElf412_custId()) ? "4"
				: "1");
		if (Util.isNotEmpty(meta.getCreateBy())) {
			l180m01b.setCreateBY(lrsConstants.CREATEBY.系統產生);
		}
		l180m01b.setNewBy170M01("");
		l180m01b.setDocStatus1(lrsConstants.docStatus1.要覆審);
		l180m01b.setProjectSeq(null);
		l180m01b.setProjectNo(null);
		l180m01b.setElfCName(CNAME);
		l180m01b.setECoNm(ECONM);
		l180m01b.setECoNm07A(ECONM07A);
		l180m01b.setBusCd(BUSCD);
		l180m01b.setBussKind(BUSSKIND);
		l180m01b.setSup3CNm(SUP3CNM);
		l180m01b.setRltGuarantor(Util.truncateString(rlt_Guarantor,
				MAXLEN_L180M01B_RLTGUARANTOR));
		l180m01b.setRltBorrower(Util.truncateString(rlt_Borrower,
				MAXLEN_L180M01B_RLTBORROWER));
		l180m01b.setULoan("");
		l180m01b.setNewNCkdFlag("");
		l180m01b.setNewNCkdMemo("");
		l180m01b.setNewNextNwDt(null);
		l180m01b.setNewLRDate(null);
		l180m01b.setElfDataDt(elf412.getElf412_dataDt());
		l180m01b.setElfMainCust(Util.trim(elf412.getElf412_mainCust()));
		l180m01b.setElfCrdType(Util.trim(elf412.getElf412_crdType()));
		l180m01b.setElfCrdTTbl(Util.trim(elf412.getElf412_crdtTbl()));
		l180m01b.setElfMowType(Util.trim(elf412.getElf412_mowType()));
		l180m01b.setElfMowTbl1(Util.trim(elf412.getElf412_mowTbl1()));
		l180m01b.setElfLLRDate(elf412.getElf412_llrDate());
		l180m01b.setElfLRDate(elf412.getElf412_lrDate());
		l180m01b.setElfRCkdLine(Util.trim(elf412.getElf412_rckdLine()));
		l180m01b.setElfOCkdLine(Util.trim(elf412.getElf412_ockdLine()));
		l180m01b.setElfUCkdLINE(Util.trim(elf412.getElf412_uckdLine()));
		l180m01b.setElfUCkdDt(elf412.getElf412_uckdDt());
		l180m01b.setElfCState(Util.trim(elf412.getElf412_cState()));
		l180m01b.setElfCancelDt(elf412.getElf412_cancelDt());
		l180m01b.setElfMDFlag(CrsUtil.mdFlag_with_leadingZero(Util.trim(elf412
				.getElf412_mdFlag())));
		l180m01b.setElfMDDt(elf412.getElf412_mdDt());
		l180m01b.setElfProcess(Util.trim(elf412.getElf412_process()));
		l180m01b.setElfNewAdd(Util.trim(elf412.getElf412_newAdd()));
		l180m01b.setElfNewDate(LrsUtil.elf412_rocDateStr_toYYYYMM(elf412
				.getElf412_newDate()));
		l180m01b.setElfNCkdFlag(Util.trim(elf412.getElf412_nckdFlag()));
		l180m01b.setElfNCkdDate(elf412.getElf412_nckdDate());
		l180m01b.setElfNCkdMemo(Util.trim(elf412.getElf412_nckdMemo()));
		l180m01b.setElfNextNwDt(elf412.getElf412_nextNwDt());
		l180m01b.setElfDBUOBU(Util.trim(elf412.getElf412_dbuObu()));
		l180m01b.setElfUpdDate(elf412.getElf412_upddate());
		l180m01b.setElfUpdater(Util.trim(elf412.getElf412_updater()));
		l180m01b.setElfMemo(Util.trim(elf412.getElf412_memo()));
		l180m01b.setElfTmeStamp(elf412.getElf412_tmestamp());
		l180m01b.setCreator(meta.getCreator());
		l180m01b.setCreateTime(meta.getCreateTime());
		l180m01b.setUpdater(meta.getUpdater());
		l180m01b.setUpdateTime(meta.getUpdateTime());
		l180m01b.setCoMainId(null);
		l180m01b.setElfFcrdType(Util.trim(elf412.getElf412_fcrdType()));
		l180m01b.setElfFcrdArea(Util.trim(elf412.getElf412_fcrdArea()));
		l180m01b.setElfFcrdPred(Util.trim(elf412.getElf412_fcrdPred()));
		l180m01b.setElfFcrdGrad(Util.trim(elf412.getElf412_fcrdGrad()));

		// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
		l180m01b.setElfRealCkFg(Util.trim(elf412.getElf412_realCkFg()));
		l180m01b.setElfRealDt(elf412.getElf412_realDt());

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		l180m01b.setCtlType(LrsUtil.CTLTYPE_主辦覆審);

		// J-108-0078_05097_B1001
		// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
		l180m01b.setElfIsAllNew(Util.trim(elf412.getElf412_isAllNew()));
		// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
		l180m01b.setElfIsRescue(Util.trim(elf412.getElf412_isRescue()));
		l180m01b.setElfGuarFlag(Util.trim(elf412.getElf412_guarFlag()));
		l180m01b.setElfNewRescue(Util.trim(elf412.getElf412_newRescue()));
		l180m01b.setElfNewRescueYM(LrsUtil.elf412_rocDateStr_toYYYYMM(elf412
				.getElf412_newRescueYM()));
		// J-110-0272 抽樣覆審
		l180m01b.setElfRandomType(Util.trim(elf412.getElf412_randomType()));

		// J-110-0396 配合授審處，E-Loan企金授信覆審系統修改每月需覆審名單報表，優化由系統排除免覆審名單等事項。
		l180m01b.setElfNReview(ELF415_NREVIEW);
		l180m01b.setElfNReviewYM(LrsUtil.elf412_rocDateStr_toYYYYMM(ELF415_DATAYM));

		LrsUtil.model_zeroDate_to_null(l180m01b);
		LrsUtil.model_elf412Str_to_N(l180m01b);
	}

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	private void _copyL180M01B_ELF412B(String branch, L180M01A meta,
			L180M01B l180m01b, Map<String, Object> elf412bdata) {
		ELF412B elf412b = new ELF412B();
		DataParse.map2Bean(elf412bdata, elf412b);
		// ------
		String BUSCD = Util.trim(elf412bdata.get("BUSCD"));
		String BUSSKIND = Util.trim(elf412bdata.get("BUSSKIND"));
		String ECONM07A = Util.trim(elf412bdata.get("ECONM07A"));
		String CNAME = LMSUtil
				.getNotEmptyVal_str(elf412bdata, "CNAME", "ENAME");
		String ECONM = Util.trim(elf412bdata.get("ECONM"));
		String SUP3CNM = LMSUtil.getNotEmptyVal_str(elf412bdata, "SUP1CNM",
				"SUP3CNM");

		String rlt_Borrower = misELLNGTEEService.get_lrs_Rlt_Borrower(branch,
				elf412b.getElf412b_custId(), elf412b.getElf412b_dupNo());
		String rlt_Guarantor = misELLNGTEEService.get_lrs_Rlt_Guarantor(branch,
				elf412b.getElf412b_custId(), elf412b.getElf412b_dupNo());

		l180m01b.setMainId(meta.getMainId());
		l180m01b.setCustId(elf412b.getElf412b_custId());
		l180m01b.setDupNo(elf412b.getElf412b_dupNo());
		l180m01b.setTypCd(LrsUtil.isCustId_Z(elf412b.getElf412b_custId()) ? "4"
				: "1");
		if (Util.isNotEmpty(meta.getCreateBy())) {
			l180m01b.setCreateBY(lrsConstants.CREATEBY.系統產生);
		}
		l180m01b.setNewBy170M01("");
		l180m01b.setDocStatus1(lrsConstants.docStatus1.要覆審);
		l180m01b.setProjectSeq(null);
		l180m01b.setProjectNo(null);
		l180m01b.setElfCName(CNAME);
		l180m01b.setECoNm(ECONM);
		l180m01b.setECoNm07A(ECONM07A);
		l180m01b.setBusCd(BUSCD);
		l180m01b.setBussKind(BUSSKIND);
		l180m01b.setSup3CNm(SUP3CNM);
		l180m01b.setRltGuarantor(Util.truncateString(rlt_Guarantor,
				MAXLEN_L180M01B_RLTGUARANTOR));
		l180m01b.setRltBorrower(Util.truncateString(rlt_Borrower,
				MAXLEN_L180M01B_RLTBORROWER));
		l180m01b.setULoan("");
		l180m01b.setNewNCkdFlag("");
		l180m01b.setNewNCkdMemo("");
		l180m01b.setNewNextNwDt(null);
		l180m01b.setNewLRDate(null);
		l180m01b.setElfDataDt(elf412b.getElf412b_dataDt());
		l180m01b.setElfMainCust(Util.trim(elf412b.getElf412b_mainCust()));
		l180m01b.setElfCrdType(Util.trim(elf412b.getElf412b_crdType()));
		l180m01b.setElfCrdTTbl(Util.trim(elf412b.getElf412b_crdtTbl()));
		l180m01b.setElfMowType(Util.trim(elf412b.getElf412b_mowType()));
		l180m01b.setElfMowTbl1(Util.trim(elf412b.getElf412b_mowTbl1()));
		l180m01b.setElfLLRDate(elf412b.getElf412b_llrDate());
		l180m01b.setElfLRDate(elf412b.getElf412b_lrDate());

		if (Util.equals(Util.trim(elf412b.getElf412b_rckdLine()), "")) {
			l180m01b.setElfRCkdLine("A"); // 預設一年一次
		} else {
			l180m01b.setElfRCkdLine(Util.trim(elf412b.getElf412b_rckdLine()));
		}

		l180m01b.setElfOCkdLine(Util.trim(elf412b.getElf412b_ockdLine()));
		l180m01b.setElfUCkdLINE(Util.trim(elf412b.getElf412b_uckdLine()));
		l180m01b.setElfUCkdDt(elf412b.getElf412b_uckdDt());
		l180m01b.setElfCState(Util.trim(elf412b.getElf412b_cState()));
		l180m01b.setElfCancelDt(elf412b.getElf412b_cancelDt());

		l180m01b.setElfNewAdd(Util.trim(elf412b.getElf412b_newAdd()));
		l180m01b.setElfNewDate(LrsUtil.elf412_rocDateStr_toYYYYMM(elf412b
				.getElf412b_newDate()));
		l180m01b.setElfNCkdFlag(Util.trim(elf412b.getElf412b_nckdFlag()));
		l180m01b.setElfNCkdDate(elf412b.getElf412b_nckdDate());
		l180m01b.setElfNCkdMemo(Util.trim(elf412b.getElf412b_nckdMemo()));
		l180m01b.setElfNextNwDt(elf412b.getElf412b_nextNwDt());
		l180m01b.setElfUpdDate(elf412b.getElf412b_upddate());
		l180m01b.setElfUpdater(Util.trim(elf412b.getElf412b_updater()));
		l180m01b.setElfMemo(Util.trim(elf412b.getElf412b_memo()));
		l180m01b.setElfTmeStamp(elf412b.getElf412b_tmestamp());
		l180m01b.setCreator(meta.getCreator());
		l180m01b.setCreateTime(meta.getCreateTime());
		l180m01b.setUpdater(meta.getUpdater());
		l180m01b.setUpdateTime(meta.getUpdateTime());
		l180m01b.setCoMainId(null);
		l180m01b.setElfFcrdType(Util.trim(elf412b.getElf412b_fcrdType()));
		l180m01b.setElfFcrdArea(Util.trim(elf412b.getElf412b_fcrdArea()));
		l180m01b.setElfFcrdPred(Util.trim(elf412b.getElf412b_fcrdPred()));
		l180m01b.setElfFcrdGrad(Util.trim(elf412b.getElf412b_fcrdGrad()));

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		l180m01b.setCtlType(LrsUtil.CTLTYPE_自辦覆審);
		l180m01b.setOldRptId(Util.trim(elf412b.getElf412b_oldRptId()));
		l180m01b.setOldRptDt(elf412b.getElf412b_oldRptDt());
		l180m01b.setNewRptId(Util.trim(elf412b.getElf412b_newRptId()));
		l180m01b.setNewRptDt(elf412b.getElf412b_newRptDt());

		LrsUtil.model_zeroDate_to_null(l180m01b);
		LrsUtil.model_elf412Str_to_N(l180m01b);
	}

	@Override
	public boolean gfnGenCTLList_ByBrno(boolean is_exMode_A,
			List<String> instIdList, String branch, Date baseDate,
			String userId, String unitNo, String unitType, String createBy,
			List<L180M01B> l180m01b_list) {
		if (CrsUtil.isNull_or_ZeroDate(baseDate)) {
			return false;
		}

		/**
		 * * <br/>
		 * exMode = "1" ' 手動程式 <br/>
		 * exMode = "2" ' 背景代理程式執行 <br/>
		 * exMode = "A" ' 產生報表 'J-100-0241 e-Loan授信管理之覆審名單檔
		 * '產生覆審名單'中，增列針對特定或全部中心轄下分行截至某月份止依規『必定要』辦理覆審之客戶名單及戶數之功能。
		 */

		// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		List<Map<String, Object>> elf412data_list = null;
		if (retrialService.chkCtlTypeByBrNo(unitNo, LrsUtil.CTLTYPE_主辦覆審)) {
			elf412data_list = misELF412Service.getByKeyWithBasicData(branch);
			if (CollectionUtils.isEmpty(elf412data_list)) {
				// 沒有ELF412 且不是自辦覆審或價金履約覆審分行，就不用再繼續跑
				if (!retrialService.chkCtlTypeByBrNo(unitNo,
						LrsUtil.CTLTYPE_自辦覆審)
						&& !retrialService.chkCtlTypeByBrNo(unitNo,
								LrsUtil.CTLTYPE_價金履約)) {
					return true;
				}

			}
		}

		L180M01A meta = new L180M01A();
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		if (is_exMode_A == false) {
			meta.setMainId(IDGenerator.getUUID());
			meta.setTypCd(TypCdEnum.DBU.getCode());
			meta.setUnitType(unitType);
			meta.setOwnBrId(unitNo);
			meta.setCreator(userId);
			meta.setCreateTime(nowTS);
			meta.setUpdater(userId);
			meta.setUpdateTime(nowTS);
			meta.setDataDate(baseDate);
			meta.setBranchId(branch);
			meta.setGenerateDate(nowTS);
			meta.setCreateBy(createBy);
		}
		Set<String> tmpBN = new HashSet<String>();
		Map<String, String> needGenId_coMainIdMap = new HashMap<String, String>();
		Map<String, L180M01B> m_L180M01B = new HashMap<String, L180M01B>();
		Map<String, List<L180M01C>> m_L180M01C = new HashMap<String, List<L180M01C>>();
		Map<String, List<L180M01D>> m_L180M01D = new HashMap<String, List<L180M01D>>();

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		Map<String, L180M01B> m_L180M01B_elf412b = new HashMap<String, L180M01B>();
		Map<String, String> process_L180M01B_elf412b = new HashMap<String, String>();

		// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
		Map<String, L180M01B> m_L180M01B_elf412c = new HashMap<String, L180M01B>();
		Map<String, String> process_L180M01B_elf412c = new HashMap<String, String>();

		if (!CollectionUtils.isEmpty(elf412data_list)) {
			for (Map<String, Object> elf412data : elf412data_list) {
				L180M01B l180m01b = new L180M01B();
				_copyL180M01B(branch, meta, l180m01b, elf412data);

				// ------
				RO412 ro412 = new RO412(branch, l180m01b);
				LinkedHashMap<String, String> elf412_DBUCOID_map = new LinkedHashMap<String, String>();
				LinkedHashMap<String, String> elf412_OBUCOID_map = new LinkedHashMap<String, String>();
				if (Util.equals("Y", l180m01b.getElfDBUOBU())) {
					// '如果有共管 抓共管額度********************************************
					retrialService.gfnCTL_Import_LNF025(branch,
							l180m01b.getCustId(), l180m01b.getDupNo(),
							elf412_DBUCOID_map, elf412_OBUCOID_map);

					// '調整共用額度時採用之信評
					RO412 src_ro412 = new RO412(branch, l180m01b);
					ro412 = retrialService.gfnGenCTLList_PROCESS_SHARE_GRADE(
							branch, LMSUtil.getCustKey_len10custId(
									l180m01b.getCustId(), l180m01b.getDupNo()),
							src_ro412, elf412_DBUCOID_map, elf412_OBUCOID_map);

					if (Util.notEquals(src_ro412.get_rckdLine(),
							ro412.get_rckdLine())) {
						l180m01b.setElfRCkdLine(ro412.get_rckdLine());
					}
				}
				/*
				 * exMode==A 用 mode 2 跑 else 用 mode 1 跑
				 */
				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				Date ndDate = null;
				L180M01B l180m01b_412b = null;
				RO412 ro412b = null;
				// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
				L180M01B l180m01b_412c = null;
				RO412 ro412c = null;

				if (retrialService.chkCtlTypeByBrNo(unitNo,
						LrsUtil.CTLTYPE_自辦覆審)) {
					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					// 自辦覆審要把ELF412B 一起計算DUE DATE
					Map<String, Object> elf412bdata = misELF412BService
							.getByKeyWithBasicData(branch,
									l180m01b.getCustId(), l180m01b.getDupNo());
					if (elf412bdata != null && !elf412bdata.isEmpty()) {
						l180m01b_412b = new L180M01B();
						_copyL180M01B_ELF412B(branch, meta, l180m01b_412b,
								elf412bdata);
						String buildKey = LMSUtil.getCustKey_len10custId(
								l180m01b_412b.getCustId(),
								l180m01b_412b.getDupNo());
						ro412b = new RO412(branch, l180m01b_412b);

						process_L180M01B_elf412b.put(buildKey, buildKey);

					}
				}

				if (retrialService.chkCtlTypeByBrNo(unitNo,
						LrsUtil.CTLTYPE_價金履約)) {
					// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
					// 自辦覆審要把ELF412C 一起計算DUE DATE
					Map<String, Object> elf412cdata = misELF412CService
							.getByKeyWithBasicData(branch,
									l180m01b.getCustId(), l180m01b.getDupNo());
					if (elf412cdata != null && !elf412cdata.isEmpty()) {
						l180m01b_412c = new L180M01B();
						_copyL180M01B_ELF412C(branch, meta, l180m01b_412c,
								elf412cdata);
						String buildKey = LMSUtil.getCustKey_len10custId(
								l180m01b_412c.getCustId(),
								l180m01b_412c.getDupNo());
						ro412c = new RO412(branch, l180m01b_412c);

						process_L180M01B_elf412c.put(buildKey, buildKey);

					}
				}

				ndDate = retrialService.gfnCTL_Caculate_DueDate(
						is_exMode_A ? lrsConstants.mode._2
								: lrsConstants.mode._1, baseDate, ro412,
						ro412b, ro412c);

				if (CrsUtil.isNull_or_ZeroDate(ndDate)) {
					continue;
				}

				if (CrsUtil.isNOT_null_and_NOTZeroDate(ndDate)) {

					if (MapUtils.isNotEmpty(elf412_DBUCOID_map)
							|| MapUtils.isNotEmpty(elf412_OBUCOID_map)) {
						String coMainId = LrsUtil.build_coMainId(l180m01b);
						String l180m01b_idDup = LMSUtil.getCustKey_len10custId(
								l180m01b.getCustId(), l180m01b.getDupNo());
						// ---
						// keep 共管的 id
						for (String lnf020_id : elf412_DBUCOID_map.keySet()) {
							if (!tmpBN.contains(lnf020_id)) {
								tmpBN.add(lnf020_id);
								if (!needGenId_coMainIdMap
										.containsKey(lnf020_id)) {
									needGenId_coMainIdMap.put(lnf020_id,
											coMainId);
								}
								if (!needGenId_coMainIdMap
										.containsKey(l180m01b_idDup)) {
									needGenId_coMainIdMap.put(l180m01b_idDup,
											coMainId);
								}
							}
						}
						for (String lnf020_id : elf412_OBUCOID_map.keySet()) {
							if (!tmpBN.contains(lnf020_id)) {
								tmpBN.add(lnf020_id);
								if (!needGenId_coMainIdMap
										.containsKey(lnf020_id)) {
									needGenId_coMainIdMap.put(lnf020_id,
											coMainId);
								}
								if (!needGenId_coMainIdMap
										.containsKey(l180m01b_idDup)) {
									needGenId_coMainIdMap.put(l180m01b_idDup,
											coMainId);
								}
							}
						}
					}
					if (is_exMode_A == false) {
						List<String> cntrNo_list = new ArrayList<String>();
						// '如果是新案
						// 抓新案額度********************************************************************************
						if (Util.isNotEmpty(l180m01b.getElfNewAdd())) {
							cntrNo_list.addAll(gfnCTL_Get_ELF411_CONTRACT(
									branch, l180m01b.getCustId(),
									l180m01b.getDupNo(), ro412.get_newDate()));

						}
						String coMainId = "";
						if (true) {
							String lnf020_id = LMSUtil.getCustKey_len10custId(
									l180m01b.getCustId(), l180m01b.getDupNo());
							if (needGenId_coMainIdMap.containsKey(lnf020_id)) {
								coMainId = needGenId_coMainIdMap.get(lnf020_id);
							}
						}
						gfnGenCTLList_ByBrno_GenDoc(m_L180M01B, m_L180M01C,
								m_L180M01D, l180m01b, cntrNo_list,
								elf412_DBUCOID_map, elf412_OBUCOID_map,
								coMainId);

						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						if (l180m01b_412b != null) {
							String buildKeyB = LMSUtil.getCustKey_len10custId(
									l180m01b_412b.getCustId(),
									l180m01b_412b.getDupNo());
							m_L180M01B_elf412b.put(buildKeyB, l180m01b_412b);
						}

						// J-107-0254_05097_B1001 Web e-Loan
						// 新增對合作房仲業價金履約保證額度覆審報告表
						if (l180m01b_412c != null) {
							String buildKeyC = LMSUtil.getCustKey_len10custId(
									l180m01b_412c.getCustId(),
									l180m01b_412c.getDupNo());
							m_L180M01B_elf412c.put(buildKeyC, l180m01b_412c);
						}

					} else {
						String buildKey = LMSUtil.getCustKey_len10custId(
								l180m01b.getCustId(), l180m01b.getDupNo());
						m_L180M01B.put(buildKey, l180m01b);

						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						if (l180m01b_412b != null) {
							String buildKeyB = LMSUtil.getCustKey_len10custId(
									l180m01b_412b.getCustId(),
									l180m01b_412b.getDupNo());
							m_L180M01B_elf412b.put(buildKeyB, l180m01b_412b);
						}

						// J-107-0254_05097_B1001 Web e-Loan
						// 新增對合作房仲業價金履約保證額度覆審報告表
						if (l180m01b_412c != null) {
							String buildKeyC = LMSUtil.getCustKey_len10custId(
									l180m01b_412c.getCustId(),
									l180m01b_412c.getDupNo());
							m_L180M01B_elf412c.put(buildKeyC, l180m01b_412c);
						}

					}
				}
			}
		}

		// '抓DUB OBU 共管ID
		if (true) {
			for (String lnf020_id : m_L180M01B.keySet()) {
				needGenId_coMainIdMap.remove(lnf020_id);
			}
			// 在底稿，產生共管ID
			gfnGenCTLList_GenDBUOBUList(m_L180M01B, m_L180M01C, m_L180M01D,
					is_exMode_A, branch, meta, needGenId_coMainIdMap);

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			// DBU OBU共用ID 如果有ELF412B 也要補上名單
			if (retrialService.chkCtlTypeByBrNo(unitNo, LrsUtil.CTLTYPE_自辦覆審)) {

				for (String lnf020_custId : needGenId_coMainIdMap.keySet()) {
					Map<String, Object> elf412b_data = misELF412BService
							.getByKeyWithBasicData(
									branch,
									StringUtils.substring(lnf020_custId, 0, 10),
									StringUtils.substring(lnf020_custId, 10));
					if (Util.isEmpty(elf412b_data)) {
						continue;
					}

					L180M01B l180m01b = new L180M01B();
					_copyL180M01B_ELF412B(branch, meta, l180m01b, elf412b_data);

					if (is_exMode_A) {
						m_L180M01B_elf412b.put(lnf020_custId, l180m01b);
					} else {
						m_L180M01B_elf412b.put(lnf020_custId, l180m01b);
					}

					process_L180M01B_elf412b.put(lnf020_custId, lnf020_custId);

				}
			}
			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			// DBU OBU共用ID 如果有ELF412C 也要補上名單
			if (retrialService.chkCtlTypeByBrNo(unitNo, LrsUtil.CTLTYPE_價金履約)) {

				for (String lnf020_custId : needGenId_coMainIdMap.keySet()) {
					Map<String, Object> elf412c_data = misELF412CService
							.getByKeyWithBasicData(
									branch,
									StringUtils.substring(lnf020_custId, 0, 10),
									StringUtils.substring(lnf020_custId, 10));
					if (Util.isEmpty(elf412c_data)) {
						continue;
					}

					L180M01B l180m01b = new L180M01B();
					_copyL180M01B_ELF412C(branch, meta, l180m01b, elf412c_data);

					if (is_exMode_A) {
						m_L180M01B_elf412c.put(lnf020_custId, l180m01b);
					} else {
						m_L180M01B_elf412c.put(lnf020_custId, l180m01b);
					}

					process_L180M01B_elf412c.put(lnf020_custId, lnf020_custId);

				}
			}
		}

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		// 補沒有ELF412 但有ELF412B的資料
		List<Map<String, Object>> elf412bdata_list = null;
		if (retrialService.chkCtlTypeByBrNo(unitNo, LrsUtil.CTLTYPE_自辦覆審)) {
			elf412bdata_list = misELF412BService.getByKeyWithBasicData(branch);
			if (!CollectionUtils.isEmpty(elf412bdata_list)) {
				for (Map<String, Object> elf412bdata : elf412bdata_list) {

					Date ndDate = null;
					RO412 ro412b = null;

					String custId_elf412b = Util.trim(MapUtils.getString(
							elf412bdata, "ELF412B_CUSTID", ""));
					String dupNo_elf412b = Util.trim(MapUtils.getString(
							elf412bdata, "ELF412B_DUPNO", ""));

					String buildKeyB = LMSUtil.getCustKey_len10custId(
							custId_elf412b, dupNo_elf412b);

					// ELF412B 前面已經處理過了，就不用再處理了
					if (process_L180M01B_elf412b.containsKey(buildKeyB)) {
						continue;
					}
					process_L180M01B_elf412b.put(buildKeyB, buildKeyB);

					L180M01B l180m01b_412b = new L180M01B();
					_copyL180M01B_ELF412B(branch, meta, l180m01b_412b,
							elf412bdata);
					ro412b = new RO412(branch, l180m01b_412b);

					ndDate = retrialService.gfnCTL_Caculate_DueDate(
							is_exMode_A ? lrsConstants.mode._2
									: lrsConstants.mode._1, baseDate, null,
							ro412b, null);

					if (CrsUtil.isNOT_null_and_NOTZeroDate(ndDate)) {
						m_L180M01B_elf412b.put(buildKeyB, l180m01b_412b);
					}

				}

			}
		}

		// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
		// 補沒有ELF412 但有ELF412C的資料
		List<Map<String, Object>> elf412cdata_list = null;
		if (retrialService.chkCtlTypeByBrNo(unitNo, LrsUtil.CTLTYPE_價金履約)) {
			elf412cdata_list = misELF412CService.getByKeyWithBasicData(branch);
			if (!CollectionUtils.isEmpty(elf412cdata_list)) {
				for (Map<String, Object> elf412cdata : elf412cdata_list) {

					Date ndDate = null;
					RO412 ro412c = null;

					String custId_elf412c = Util.trim(MapUtils.getString(
							elf412cdata, "ELF412C_CUSTID", ""));
					String dupNo_elf412c = Util.trim(MapUtils.getString(
							elf412cdata, "ELF412C_DUPNO", ""));

					String buildKeyB = LMSUtil.getCustKey_len10custId(
							custId_elf412c, dupNo_elf412c);

					// ELF412B 前面已經處理過了，就不用再處理了
					if (process_L180M01B_elf412c.containsKey(buildKeyB)) {
						continue;
					}
					process_L180M01B_elf412c.put(buildKeyB, buildKeyB);

					L180M01B l180m01b_412c = new L180M01B();
					_copyL180M01B_ELF412C(branch, meta, l180m01b_412c,
							elf412cdata);
					ro412c = new RO412(branch, l180m01b_412c);

					ndDate = retrialService.gfnCTL_Caculate_DueDate(
							is_exMode_A ? lrsConstants.mode._2
									: lrsConstants.mode._1, baseDate, null,
							ro412c, null);

					if (CrsUtil.isNOT_null_and_NOTZeroDate(ndDate)) {
						m_L180M01B_elf412c.put(buildKeyB, l180m01b_412c);
					}

				}

			}
		}

		if (is_exMode_A == false) {
			L180A01A l180a01a = new L180A01A();
			l180a01a.setMainId(meta.getMainId());
			l180a01a.setOwnUnit(meta.getOwnBrId());
			l180a01a.setOwner(meta.getCreator());
			l180a01a.setAuthTime(meta.getCreateTime());
			l180a01a.setAuthType("1");
			l180a01a.setAuthUnit(meta.getOwnBrId());
			retrialService.save(l180a01a);

			for (String lnf020_id : m_L180M01B.keySet()) {
				L180M01B l180m01b = m_L180M01B.get(lnf020_id);
				// J-109-0313 小規模覆審 - 判斷是否為純小規模
				l180m01b = retrialService.getOnlySmallBussCaseC(l180m01b);
				// J-110-0272 抽樣覆審 覆審週期除了A以外都要清空 L180M01B.ELFRANDOMTYPE
				// 排除 LMS.L180M01B.ISSMALLBUSS = 'Y' 小規案件
				if(Util.notEquals(Util.trim(l180m01b.getElfRCkdLine()), "A")
						|| Util.equals(Util.trim(l180m01b.getIsSmallBuss()), "Y")){
					l180m01b.setElfRandomType("");
				}
				List<L180M01C> l180m01c_list = m_L180M01C.get(lnf020_id);
				List<L180M01D> l180m01d_list = m_L180M01D.get(lnf020_id);
				retrialService.save(l180m01b);
				if (!CollectionUtils.isEmpty(l180m01c_list)) {
					for (L180M01C l180m01c : l180m01c_list) {
						retrialService.save(l180m01c);
					}
				}
				if (!CollectionUtils.isEmpty(l180m01d_list)) {
					for (L180M01D l180m01d : l180m01d_list) {
						l180m01d.setPid(l180m01b.getOid());
						retrialService.save(l180m01d);
					}
				}
			}

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			for (String lnf020_id : m_L180M01B_elf412b.keySet()) {
				L180M01B l180m01b_elf412b = m_L180M01B_elf412b.get(lnf020_id);
				retrialService.save(l180m01b_elf412b);
			}

			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			for (String lnf020_id : m_L180M01B_elf412c.keySet()) {
				L180M01B l180m01b_elf412c = m_L180M01B_elf412c.get(lnf020_id);
				retrialService.save(l180m01b_elf412c);
			}

			// ---
			retrialService.save(meta);
			instIdList.add(meta.getOid());
		}

		if (m_L180M01B.size() > 0) {
			l180m01b_list.addAll(m_L180M01B.values());
		}

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		if (m_L180M01B_elf412b.size() > 0) {
			l180m01b_list.addAll(m_L180M01B_elf412b.values());
		}

		// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
		if (m_L180M01B_elf412c.size() > 0) {
			l180m01b_list.addAll(m_L180M01B_elf412c.values());
		}

		return true;
	}

	@Override
	public L180M01B gfnDB2CTLInsertNewList(L180M01A meta, String custId,
			String dupNo, String cName, String ctlType) throws CapException {
		String branch = meta.getBranchId();

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		L180M01B l180m01b = new L180M01B();

		if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
			Map<String, Object> elf412bdata = misELF412BService.getDataWithPEO(
					branch, custId, dupNo);

			_copyL180M01B_ELF412B(branch, meta, l180m01b, elf412bdata);
			l180m01b.setCreateBY(lrsConstants.CREATEBY.人工產生);

			retrialService.save(l180m01b);
		} else if (Util.equals(ctlType, LrsUtil.CTLTYPE_價金履約)) {
			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			Map<String, Object> elf412cdata = misELF412CService.getDataWithPEO(
					branch, custId, dupNo);

			_copyL180M01B_ELF412C(branch, meta, l180m01b, elf412cdata);
			l180m01b.setCreateBY(lrsConstants.CREATEBY.人工產生);

			retrialService.save(l180m01b);
		} else {
			Map<String, Object> elf412data = misELF412Service.getDataWithPEO(
					branch, custId, dupNo);

			_copyL180M01B(branch, meta, l180m01b, elf412data);
			l180m01b.setCreateBY(lrsConstants.CREATEBY.人工產生);
			// ---
			LinkedHashMap<String, String> elf412_DBUCOID_map = new LinkedHashMap<String, String>();
			LinkedHashMap<String, String> elf412_OBUCOID_map = new LinkedHashMap<String, String>();

			retrialService
					.gfnCTL_Import_LNF025(branch, l180m01b.getCustId(),
							l180m01b.getDupNo(), elf412_DBUCOID_map,
							elf412_OBUCOID_map);

			if (elf412_DBUCOID_map.size() > 0 || elf412_OBUCOID_map.size() > 0) {
				l180m01b.setElfDBUOBU("Y");
				l180m01b.setCoMainId(LrsUtil.build_coMainId(l180m01b));
			}
			// J-109-0313 小規模覆審 - 判斷是否為純小規模
			l180m01b = retrialService.getOnlySmallBussCaseC(l180m01b);
			// J-110-0272 抽樣覆審 覆審週期除了A以外都要清空 L180M01B.ELFRANDOMTYPE
			// 排除 LMS.L180M01B.ISSMALLBUSS = 'Y' 小規案件
			if(Util.notEquals(Util.trim(l180m01b.getElfRCkdLine()), "A")
					|| Util.equals(Util.trim(l180m01b.getIsSmallBuss()), "Y")){
				l180m01b.setElfRandomType("");
			}
			retrialService.save(l180m01b);

			if (Util.equals("Y", l180m01b.getElfDBUOBU())) {
				List<L180M01D> l180m01d_list = new ArrayList<L180M01D>();
				// l180m01d
				_proc_DBUOBUCOID_map_L180M01D(l180m01d_list, "0",
						elf412_DBUCOID_map);
				_proc_DBUOBUCOID_map_L180M01D(l180m01d_list, "1",
						elf412_OBUCOID_map);

				for (L180M01D l180m01d : l180m01d_list) {
					l180m01d.setPid(l180m01b.getOid());
					retrialService.save(l180m01d);
				}
			}
		}

		retrialService.save(meta);

		return l180m01b;
	}

	@Override
	public void l180m01b_reCtl(L180M01B model) {
		model.setDocStatus1(lrsConstants.docStatus1.要覆審);
		model.setNewNCkdFlag("");
		model.setNewNCkdMemo("");
		model.setNewNextNwDt(null);
	}

	@Override
	public void gfnGenCTLList_ByBrno_GenDoc(Map<String, L180M01B> m_L180M01B,
			Map<String, List<L180M01C>> m_L180M01C,
			Map<String, List<L180M01D>> m_L180M01D, L180M01B l180m01b,
			List<String> cntrNo_list, Map<String, String> elf412_DBUCOID_map,
			Map<String, String> elf412_OBUCOID_map, String coMainId) {
		List<L180M01C> l180m01c_list = new ArrayList<L180M01C>();
		List<L180M01D> l180m01d_list = new ArrayList<L180M01D>();
		// l180m01c
		if (!CollectionUtils.isEmpty(cntrNo_list)) {
			for (String cntrNo : cntrNo_list) {
				L180M01C l180m01c = new L180M01C();
				l180m01c.setMainId(l180m01b.getMainId());
				l180m01c.setCustId(l180m01b.getCustId());
				l180m01c.setDupNo(l180m01b.getDupNo());
				l180m01c.setElfCntrType(l180m01b.getElfNewAdd());
				l180m01c.setElfCustCoId("");
				l180m01c.setElfCntrNo(cntrNo);
				l180m01c.setCreator(l180m01b.getCreator());
				l180m01c.setCreateTime(l180m01b.getCreateTime());
				l180m01c.setUpdater(l180m01b.getUpdater());
				l180m01c.setUpdateTime(l180m01b.getUpdateTime());
				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				l180m01c.setCtlType(Util.trim(l180m01b.getCtlType()));
				// ---
				l180m01c_list.add(l180m01c);
			}
		}
		// l180m01b
		if (Util.isNotEmpty(coMainId)) {
			l180m01b.setCoMainId(coMainId);
		}
		String[] condArr = { LrsUtil.NCKD_1_本行或同業主辦之聯貸案件_非擔任管理行,
				LrsUtil.NCKD_2_十成定存, LrsUtil.NCKD_3_純進出押戶 };
		if (CrsUtil.inCollection(l180m01b.getElfNCkdFlag(), condArr)) {
			l180m01b.setNewNCkdFlag(l180m01b.getElfNCkdFlag());
			l180m01b.setNewNCkdMemo(l180m01b.getElfNCkdMemo());
			l180m01b.setDocStatus1(lrsConstants.docStatus1.不覆審);
		}
		// J-110-0396 配合授審處，E-Loan企金授信覆審系統修改每月需覆審名單報表，優化由系統排除免覆審名單等事項。
		if(Util.isEmpty(l180m01b.getNewNCkdFlag()) && Util.isNotEmpty(l180m01b.getElfNReview())){
			l180m01b.setNewNCkdFlag(l180m01b.getElfNReview());
			l180m01b.setNewNCkdMemo(l180m01b.getElfNReviewYM());
			l180m01b.setDocStatus1(lrsConstants.docStatus1.不覆審);
		}

		// l180m01d
		_proc_DBUOBUCOID_map_L180M01D(l180m01d_list, "0", elf412_DBUCOID_map);
		_proc_DBUOBUCOID_map_L180M01D(l180m01d_list, "1", elf412_OBUCOID_map);

		// ===
		String buildKey = LMSUtil.getCustKey_len10custId(l180m01b.getCustId(),
				l180m01b.getDupNo());
		m_L180M01B.put(buildKey, l180m01b);
		m_L180M01C.put(buildKey, l180m01c_list);
		m_L180M01D.put(buildKey, l180m01d_list);
	}

	private void _proc_DBUOBUCOID_map_L180M01D(List<L180M01D> l180m01d_list,
			String type, Map<String, String> m) {
		if (CollectionUtils.isEmpty(m)) {
			return;
		}

		for (String lnf020_custId : m.keySet()) {
			L180M01D l180m01d = new L180M01D();
			l180m01d.setPid("");// 等到 save L180M01B 之後,再填補 pid
			l180m01d.setDbuObuType(type);
			l180m01d.setDbuObuId(StringUtils.substring(lnf020_custId, 0, 10));
			l180m01d.setDbuObuDupNo(StringUtils.substring(lnf020_custId, 10));
			l180m01d.setDbuObuName(m.get(lnf020_custId));
			// ---
			l180m01d_list.add(l180m01d);
		}
	}

	@Override
	public void gfnGenCTLList_GenDBUOBUList(Map<String, L180M01B> m_L180M01B,
			Map<String, List<L180M01C>> m_L180M01C,
			Map<String, List<L180M01D>> m_L180M01D, boolean is_exMode_A,
			String branch, L180M01A meta,
			Map<String, String> needGenId_coMainIdMap) {
		for (String lnf020_custId : needGenId_coMainIdMap.keySet()) {
			L180M01B l180m01b = new L180M01B();

			// 可能共管的ID,會有個人戶的ID,不在ELF412之內
			Map<String, Object> elf412_data = misELF412Service
					.getByKeyWithBasicData(branch,
							StringUtils.substring(lnf020_custId, 0, 10),
							StringUtils.substring(lnf020_custId, 10));
			if (Util.isEmpty(elf412_data)) {
				logger.trace("GenDBUOBUList noELF412,skip[" + branch + " , "
						+ StringUtils.substring(lnf020_custId, 0, 10) + " , "
						+ StringUtils.substring(lnf020_custId, 10) + "]");
				continue;
			}
			_copyL180M01B(branch, meta, l180m01b, elf412_data);
			// ------
			LinkedHashMap<String, String> elf412_DBUCOID_map = new LinkedHashMap<String, String>();
			LinkedHashMap<String, String> elf412_OBUCOID_map = new LinkedHashMap<String, String>();
			if (Util.equals("Y", l180m01b.getElfDBUOBU())) {
				// '如果有共管 抓共管額度********************************************
				retrialService.gfnCTL_Import_LNF025(branch,
						l180m01b.getCustId(), l180m01b.getDupNo(),
						elf412_DBUCOID_map, elf412_OBUCOID_map);
			}

			if (is_exMode_A) {
				m_L180M01B.put(lnf020_custId, l180m01b);
			} else {
				List<String> cntrNo_list = new ArrayList<String>();
				gfnGenCTLList_ByBrno_GenDoc(m_L180M01B, m_L180M01C, m_L180M01D,
						l180m01b, cntrNo_list, elf412_DBUCOID_map,
						elf412_OBUCOID_map,
						needGenId_coMainIdMap.get(lnf020_custId));
			}
		}
	}

	@Override
	public void gfnStartNorthBranch_BuildData(String exMode, String branch,
			Date baseDate) {
		if (Util.equals(lrsConstants.mode._1, exMode)) {

		} else {
			L180M01Z l180m01z = l180m01zDao.findByUniqueKey(baseDate, branch);
			if (l180m01z != null) {
				// 已跑過 ELF411 轉 ELF412
				return;
			}
		}
		// ================
		// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
		Map<String, String[]> isRescueMap = new TreeMap<String, String[]>();

		String tELF411_DATADATE = LrsUtil.elf412_rocDateStr_from_Date(CapDate
				.addMonth(baseDate, -1));
		long t = 0;
		// Step 1
		t = new Date().getTime();
		gfnCTL_Add_ELF411(branch, tELF411_DATADATE, isRescueMap);
		logger.trace("cost gfnCTL_Add_ELF411:"
				+ DurationFormatUtils.formatDurationHMS(new Date().getTime()
						- t));
		// Step 2
		t = new Date().getTime();
		gfnCTL_Import_DATA(branch, isRescueMap, baseDate);
		logger.trace("cost gfnCTL_Import_DATA:"
				+ DurationFormatUtils.formatDurationHMS(new Date().getTime()
						- t));
		// ================
		L180M01Z l180m01z = l180m01zDao.findByUniqueKey(baseDate, branch);
		if (l180m01z == null) {
			l180m01z = new L180M01Z();
			l180m01z.setDataDate(baseDate);
			l180m01z.setBranchId(branch);
		}
		l180m01z.setFinishTime(CapDate.getCurrentTimestamp());
		retrialService.save(l180m01z);
	}

	private void gfnCTL_Add_ELF411(String branch, String tELF411_DATADATE,
			Map<String, String[]> isRescueMap) {
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		Map<String, String> mowType_2to1 = retrialService
				.get_lrs_MowType_2to1();
		Map<String, String> crdtType_2to1 = retrialService
				.get_lrs_CrdtType_2to1();

		Map<String, String> processBorrIsNew = new TreeMap<String, String>();

		Date newDateOrg = LrsUtil.elf412_rocDateStr_to_Date(tELF411_DATADATE); // 010804->2019-04-01
		Date newDateAdj = CapDate.shiftDays(newDateOrg, -1); // 2019-03-31
		String maxDate = CapDate.formatDate(newDateAdj, "yyyy-MM-dd");

		// J-108-0078_05097_B1001 Web e-Loan企金授信覆審系統修改首次往來之新授信戶應辦理覆審之期限
		boolean isRckdLineIEffective = retrialService
				.isRckdLine_I_Effective(CapDate.getCurrentTimestamp());

		for (ELF411 elf411 : misELF411Service.find_ELF411_NEWADD_notEmpty(
				tELF411_DATADATE, branch)) {
			// 在 ELF411 中 group by
			// elf411_dataym,elf411_brno,elf411_custid,elf411_dupno
			// 可能有 N 筆 → N個 cntrNo
			String custId = Util.trim(elf411.getElf411_custid());
			String dupNo = Util.trim(elf411.getElf411_dupno());
			String mainCust = Util.trim(elf411.getElf411_maincust());
			String newAdd = Util.trim(elf411.getElf411_newadd());

			// J-108-0078_05097_B1001
			// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
			// 判斷是否為首次往來之新授信
			String fullCustIdIsNew = custId + "-" + dupNo;

			// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
			// 判斷是否為 純紓困客戶
			String isRescue = "";
			String guarFlag = "";
			String newRescue = "";
			if (isRescueMap == null) {
				isRescueMap = new TreeMap<String, String[]>();
			}
			if (!isRescueMap.containsKey(fullCustIdIsNew)) {
				String[] arr = retrialService.getOnlyRescueCase(branch, custId,
						dupNo, Util.trim(elf411.getElf411_contract()));
				isRescue = arr[0];
				guarFlag = arr[1];
				newRescue = arr[2];
				isRescueMap.put(fullCustIdIsNew, arr);
			} else {
				String[] valArr = isRescueMap.get(fullCustIdIsNew);
				isRescue = valArr[0];
				guarFlag = valArr[1];
				// 每個額度序號去判斷是否為紓困額度序號
				boolean cntrNoIsRescue = retrialService.getOnlyRescueCase_ByCntrNo(
				        Util.trim(elf411.getElf411_contract()));
                newRescue = cntrNoIsRescue ? "Y" : "N";
			}
			boolean rescue = Util.equals(newRescue, "Y") ? true : false;

			ELF412 elf412 = misELF412Service.findByPk(branch, custId, dupNo);
			if (elf412 == null) {
				elf412 = new ELF412();
				elf412.setElf412_branch(branch);
				elf412.setElf412_custId(custId);
				elf412.setElf412_dupNo(dupNo);
				elf412.setElf412_mainCust(mainCust);

				// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
				elf412.setElf412_isRescue(isRescue);
				elf412.setElf412_guarFlag(guarFlag);
				if (rescue) {
					elf412.setElf412_newRescue(newRescue);
					elf412.setElf412_newRescueYM(elf411.getElf411_dataym());
				} else {
//					elf412.setElf412_newRescue(newRescue);
//					elf412.setElf412_newRescueYM("");
					elf412.setElf412_newAdd(newAdd);
					elf412.setElf412_newDate(elf411.getElf411_dataym());
				}

				String isAllNew = "";
				if (isRckdLineIEffective) {
					// J-108-0078_05097_B1001
					// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
					// 判斷是否為首次往來之新授信
					// **************************************************************************
					isAllNew = "Y";
					if (processBorrIsNew.containsKey(fullCustIdIsNew)) {
						isAllNew = processBorrIsNew.get(fullCustIdIsNew);
					} else {
						// ELF411 2019/4月的新案增額額度(ELF411_DATAYM =
						// 010804)，要看3月底以前LNF020
						// 有沒有曾經有額度(銷戶日或額度建檔日小於3月底)
						if (Util.notEquals(elf411.getElf411_dataym(), "")) {

							if (Util.equals(newAdd, UtilConstants.NEWADD.新作)) {

								List<Map<String, Object>> listLnf020 = misLNF020Service
										.findIsNewCustForCTL(custId, dupNo,
												maxDate);

								if (listLnf020 != null && !listLnf020.isEmpty()) {
									// 曾經有往來之舊戶
									isAllNew = "N";
								} else {
									// 首次往來之新授信
									isAllNew = "Y";
								}
							} else {
								isAllNew = "N";
							}
						} else {
							isAllNew = "N";
						}
						processBorrIsNew.put(fullCustIdIsNew, isAllNew);
					}
				} else {
					isAllNew = "";
				}

				elf412.setElf412_isAllNew(isAllNew);
				// **************************************************************************

				// 全新戶不要自動引進評等，要不然原本覆審的名單甲為A一年一次，若因為全新戶乙進來有設共用，且評等不好，會導致甲因為共用的關係，覆審週期改成半年一次，造成忽然間就逾期未覆審
				// if (Util.notEquals(newAdd, UtilConstants.NEWADD.新作)) {

				// 還是引進信評，但再計算共用的時候，覆審週期為C的信評不納入考慮
				fill_elf412_MowOrCrd(elf412, mowType_2to1, crdtType_2to1);
				// }

				elf412.setElf412_tmestamp(nowTS);
				// ---
				retrialService.upELF412_DelThenInsert(elf412);
			} else {
				boolean needUpdate = true;

				if (Util.notEquals(LrsUtil.NCKD_7_銷戶,
						elf412.getElf412_nckdFlag())
						&& Util.isNotEmpty(Util.trim(elf412.getElf412_newAdd()))
						&& Util.isNotEmpty(Util.trim(elf412.getElf412_newDate()))) {
					// 更新前狀況未銷戶
					// '99/07/30
					// 授管處郭慧珠:如果目前覆審控制檔的新案/增額欄位尚有值(前次新案增額尚未覆審)，則不更新最新的新案/增額欄位
					needUpdate = false;
					if (rescue) {
						if(Util.isEmpty(Util.trim(elf412.getElf412_newRescue()))
								&& Util.isEmpty(Util.trim(elf412.getElf412_newRescueYM()))) {
							elf412.setElf412_newRescue(newRescue);
							elf412.setElf412_newRescueYM(elf411.getElf411_dataym());
							needUpdate = true;
						}
					}
				} else if (Util.notEquals(LrsUtil.NCKD_7_銷戶,
						elf412.getElf412_nckdFlag())
						&& Util.isNotEmpty(Util.trim(elf412
								.getElf412_newRescue()))
						&& Util.isNotEmpty(Util.trim(elf412
								.getElf412_newRescueYM()))) {
					// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
					// 更新前狀況未銷戶 (前次純紓困尚未覆審)，則不更新最新的紓困欄位
					needUpdate = false;
					// 若本次新作之額度序號非紓困(3個月或6個月內覆審) 因為覆審期間較短(紓困為1年內覆審) 需更新
					if (!rescue) {
						if(Util.isEmpty(Util.trim(elf412.getElf412_newAdd()))
								&& Util.isEmpty(Util.trim(elf412.getElf412_newDate()))) {
							elf412.setElf412_newAdd(newAdd);
							elf412.setElf412_newDate(elf411.getElf411_dataym());
							needUpdate = true;
						}
					}
				} else {

					if (elf412.getElf412_cancelDt() == null) {
						elf412.setElf412_cancelDt(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}
					if (elf412.getElf412_lrDate() == null) {
						elf412.setElf412_lrDate(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}

					boolean alreadyCTL = false;
					Date ELF495_LRDATE = null;

					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能 +
					// ctlType
					// '判斷新案增額是否已於上月覆審過
					List<Map<String, Object>> m495 = misELF495Service
							.findWithELF411(
									branch,
									custId,
									dupNo,
									elf411.getElf411_dataym(),
									StringUtils.substring(TWNDate.toAD(LrsUtil
											.elf412_rocDateStr_to_Date(elf411
													.getElf411_dataym())), 0, 7),
									LrsUtil.CTLTYPE_主辦覆審);
					if (!CollectionUtils.isEmpty(m495)
							&& _cnt_emptyELF495_CNTRNO(m495) == 0) {
						// 無資料代表本月新案/增額之額度 都複審過了 (ELF495_CNTRNO IS NULL)
						ELF495_LRDATE = (Date) (m495.get(0)
								.get("ELF495_LRDATE"));
						alreadyCTL = true;
					}

					if (alreadyCTL) {
						String strNEWADD = "";
						Map<String, String> m_newAdd = retrialService
								.get_lrs_NewAdd();
						if (m_newAdd.containsKey(newAdd)) {
							strNEWADD = m_newAdd.get(newAdd);
						}
						String alreadCTLStr = Util.toFullCharString(StringUtils
								.substring(TWNDate.toTW(LrsUtil
										.elf412_rocDateStr_to_Date(elf411
												.getElf411_dataym())), 0, 6)
								+ strNEWADD
								+ "已於"
								+ TWNDate.toTW(ELF495_LRDATE) + "辦理覆審");

						String elf412_memo = Util.trim(elf412.getElf412_memo());
						if (elf412_memo.indexOf(alreadCTLStr) < 0) {
							elf412.setElf412_memo(Util.truncateString(
									alreadCTLStr + "、" + elf412_memo,
									MAXLEN_ELF412_MEMO));
							elf412.setElf412_tmestamp(nowTS);
						} else {
							needUpdate = false;
						}
					} else {
						// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
						elf412.setElf412_isRescue(isRescue);
						elf412.setElf412_guarFlag(guarFlag);
						if (rescue) {
							elf412.setElf412_newRescue(newRescue);
							elf412.setElf412_newRescueYM(elf411
									.getElf411_dataym());
						} else {
//							elf412.setElf412_newRescue(newRescue);
//							elf412.setElf412_newRescueYM("");
							elf412.setElf412_newAdd(newAdd);
							elf412.setElf412_newDate(elf411.getElf411_dataym());
						}

						// J-108-0078_05097_B1001
						// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
						// 判斷是否為首次往來之新授信
						// **************************************************************************
						String isAllNew = "";
						if (isRckdLineIEffective) {
							isAllNew = "N";
							if (processBorrIsNew.containsKey(fullCustIdIsNew)) {
								isAllNew = processBorrIsNew
										.get(fullCustIdIsNew);
							} else {
								// ELF411 2019/4月的新案增額額度(ELF411_DATAYM =
								// 010804)，要看3月底以前LNF020
								// 有沒有曾經有額度(銷戶日或額度建檔日小於3月底)
								if (Util.notEquals(elf411.getElf411_dataym(),
										"")) {

									if (Util.equals(newAdd,
											UtilConstants.NEWADD.新作)) {

										List<Map<String, Object>> listLnf020 = misLNF020Service
												.findIsNewCustForCTL(custId,
														dupNo, maxDate);

										if (listLnf020 != null
												&& !listLnf020.isEmpty()) {
											// 曾經有往來之舊戶
											isAllNew = "N";
										} else {
											// 首次往來之新授信
											isAllNew = "Y";
										}
									} else {
										isAllNew = "N";
									}
								} else {
									isAllNew = "N";
								}
								processBorrIsNew.put(fullCustIdIsNew, isAllNew);
							}
						} else {
							isAllNew = "";
						}

						elf412.setElf412_isAllNew(isAllNew);
						// **************************************************************************

						if (Util.notEquals(LrsUtil.NCKD_8_本次暫不覆審,
								elf412.getElf412_nckdFlag())) {
							// 已存在ELF412且原本為不覆審(例如銷戶、十成定存...等)，本次因為有ELF411新做增額要恢復為要覆審時，要把前次異常通報記錄清掉，免得因為舊資料有異常通報註記，造成後續gfnCTL_Import_DATA計算覆審週期的時候，造成誤判為D
							// 若恢復要覆審的當時仍有異常通報，則gfnCTL_Import_DATA\gfnCTL_Import_LNFE0851會更新
							elf412.setElf412_mdFlag("");
							elf412.setElf412_mdDt(CapDate
									.parseDate(CapDate.ZERO_DATE));
							elf412.setElf412_process("");
						}

						if (Util.equals(LrsUtil.NCKD_7_銷戶,
								elf412.getElf412_nckdFlag())
								&& CrsUtil.isNOT_null_and_NOTZeroDate(elf412
										.getElf412_cancelDt())) {
							elf412.setElf412_llrDate(elf412.getElf412_lrDate());
							elf412.setElf412_lrDate(CapDate
									.parseDate(CapDate.ZERO_DATE));
						}

						if (Util.notEquals(LrsUtil.NCKD_8_本次暫不覆審,
								elf412.getElf412_nckdFlag())) {
							elf412.setElf412_nckdFlag("");
							elf412.setElf412_nckdDate(CapDate
									.parseDate(CapDate.ZERO_DATE));
							elf412.setElf412_nckdMemo("");
							elf412.setElf412_nextNwDt(CapDate
									.parseDate(CapDate.ZERO_DATE));
							elf412.setElf412_nextLtDt(CapDate
									.parseDate(CapDate.ZERO_DATE));
						}
						// ---
						// 之前有用到 cancelDt 去判斷,所以最後再設成 zeroDate
						elf412.setElf412_cancelDt(CapDate
								.parseDate(CapDate.ZERO_DATE));

						fill_elf412_MowOrCrd(elf412, mowType_2to1,
								crdtType_2to1);

						elf412.setElf412_tmestamp(nowTS);
					}
				}

				if (needUpdate) {
					// ---
					retrialService.upELF412_DelThenInsert(elf412);
				}
			}

			// *********************************************************************************************************
			// ELF447N 最新一筆是不是授權外且為實地覆審主辦行
			// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
			/*
			 * 免辦理實地覆審: 1.參貸同業主辦之聯合授信案件 2.國內營業單位辦理之境外公司授信案件（含對大陸地區授信）及
			 * 3.國外營業單位單獨承做之跨國(非當地國 )授信案件，
			 * 除經首長或主管單位指定辦理實地覆審之案件外，得免辦理實地覆審，由主辦單位依據本要點第六條及本條規定辦理一般覆審。
			 */

			// B 董事會(或常董會)權限案件實地覆審
			String cntrNo = Util.trim(elf411.getElf411_contract());
			String cntrBranch = Util.getLeftStr(cntrNo, 3);
			if (Util.notEquals(custId.substring(2, 3), "Z")) {
				// OBU戶不實地覆審
				Map<String, Object> elf447n = misELF447nService
						.findByMaxChkDate(custId, dupNo, cntrNo);
				if (elf447n != null && !elf447n.isEmpty()) {
					String caseLvl = MapUtils.getString(elf447n,
							"ELF447N_CASELEVEL", "");

					if (retrialService
							.chkNeedRealReview(custId, dupNo, caseLvl)) {
						/*
						 * 1常董會權限 2常董會權限簽奉總經理核批 3常董會權限簽准由副總經理核批 4利費率變更案件由總處經理核定
						 * 5屬常董會授權總經理逕核案件 6總經理權限內 7副總經理權限 8處長權限 9其他(經理) A董事會權限
						 * B營運中心營運長/副營運長權限 C利費率變更案件由董事長核定 D個金處經理權限
						 */

						// 參貸同業主辦之聯合授信案件不用覆審
						String LNF020_SYND_TYPE = Util.trim(MapUtils.getString(
								elf447n, "LNF020_SYND_TYPE", ""));
						if (Util.notEquals(LNF020_SYND_TYPE, "2")) {

							String reviewBrNo = MapUtils.getString(elf447n,
									"ELF447N_REVIEWBR", "");
							if (Util.equals(reviewBrNo, "")) {
								reviewBrNo = cntrBranch;
							}

							if (Util.equals(cntrBranch, reviewBrNo)) {

								ELF412B elf412b = misELF412BService.findByPk(
										branch, custId, dupNo);
								String unid = MapUtils.getString(elf447n,
										"ELF447N_UNID", "");
								Date chkDate = Util.parseDate(MapUtils
										.getString(elf447n, "ELF447_CHKDATE",
												""));

								if (elf412b == null) {
									elf412b = new ELF412B();
									elf412b.setElf412b_branch(branch);
									elf412b.setElf412b_custId(custId);
									elf412b.setElf412b_dupNo(dupNo);
									elf412b.setElf412b_newAdd(newAdd);
									elf412b.setElf412b_newDate(elf411
											.getElf411_dataym());
									elf412b.setElf412b_tmestamp(nowTS);
									// ---
									elf412b.setElf412b_nckdFlag("");
									elf412b.setElf412b_nckdDate(CapDate
											.parseDate(CapDate.ZERO_DATE));
									// elf412b.setElf412b_nckdMemo("");
									elf412b.setElf412b_nextNwDt(CapDate
											.parseDate(CapDate.ZERO_DATE));
									elf412b.setElf412b_nextLtDt(CapDate
											.parseDate(CapDate.ZERO_DATE));

									elf412b.setElf412b_newRptId(unid);
									elf412b.setElf412b_newRptDt(chkDate);
									elf412b.setElf412b_newDraId("");
									elf412b.setElf412b_newDraDt(null);

									if (Util.equals(Util.trim(elf412b
											.getElf412b_oldRptId()), "")) {
										elf412b.setElf412b_oldRptId(unid);
										elf412b.setElf412b_oldRptDt(chkDate);
									}
									if (Util.equals(Util.trim(elf412b
											.getElf412b_oldDraId()), "")) {
										elf412b.setElf412b_oldDraId("");
										elf412b.setElf412b_oldDraDt(null);
									}

									if (elf412b.getElf412b_llrDate() == null) {
										elf412b.setElf412b_llrDate(CapDate
												.parseDate(CapDate.ZERO_DATE));
									}
									if (elf412b.getElf412b_lrDate() == null) {
										elf412b.setElf412b_lrDate(CapDate
												.parseDate(CapDate.ZERO_DATE));
									}

									if (elf412b.getElf412b_nckdDate() == null) {
										elf412b.setElf412b_nckdDate(CapDate
												.parseDate(CapDate.ZERO_DATE));
									}
									if (elf412b.getElf412b_cancelDt() == null) {
										elf412b.setElf412b_cancelDt(CapDate
												.parseDate(CapDate.ZERO_DATE));
									}
									if (elf412b.getElf412b_uckdDt() == null) {
										elf412b.setElf412b_uckdDt(CapDate
												.parseDate(CapDate.ZERO_DATE));
									}
									if (elf412b.getElf412b_nextNwDt() == null) {
										elf412b.setElf412b_nextNwDt(CapDate
												.parseDate(CapDate.ZERO_DATE));
									}
									if (elf412b.getElf412b_nextLtDt() == null) {
										elf412b.setElf412b_nextLtDt(CapDate
												.parseDate(CapDate.ZERO_DATE));
									}

									retrialService
											.upELF412B_DelThenInsert(elf412b);
								} else {
									boolean needUpdateB = true;
									if (retrialService
											.isElf412B_NckdFlagHasNoReviewFlag(Util.trim(elf412b
													.getElf412b_nckdFlag()))) {
										// 目前有不覆審
										if (Util.notEquals(
												elf412b.getElf412b_oldRptId(),
												"")
												&& Util.equals(unid, elf412b
														.getElf412b_oldRptId())) {
											// 重覆的簽報書就不寫了
											needUpdateB = false;
										}
									} else {
										// 目前要覆審
										if (Util.notEquals(Util.trim(elf412b
												.getElf412b_newAdd()), "")) {
											// 已經有新案註記，不更新後面的新案
											needUpdateB = false;
										} else {

											if (Util.notEquals(elf412b
													.getElf412b_oldRptId(), "")
													&& Util.equals(
															unid,
															elf412b.getElf412b_oldRptId())) {
												// 重覆的簽報書就不寫了
												needUpdateB = false;
											}
										}
									}

									if (needUpdateB) {
										elf412b.setElf412b_newAdd(newAdd);
										elf412b.setElf412b_newDate(elf411
												.getElf411_dataym());
										elf412b.setElf412b_tmestamp(nowTS);
										elf412b.setElf412b_newRptId(unid);
										elf412b.setElf412b_newRptDt(chkDate);
										elf412b.setElf412b_newDraId("");
										elf412b.setElf412b_newDraDt(null);

										// J-106-0145-004 Web e-Loan
										// 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
										retrialService
												.gfnCTL_Caculate_ELF412B(elf412b);

										if (Util.equals(LrsUtil.NCKD_7_銷戶,
												elf412b.getElf412b_nckdFlag())
												&& CrsUtil
														.isNOT_null_and_NOTZeroDate(elf412b
																.getElf412b_cancelDt())) {
											elf412b.setElf412b_llrDate(elf412b
													.getElf412b_lrDate());
											elf412b.setElf412b_lrDate(CapDate
													.parseDate(CapDate.ZERO_DATE));
										}

										if (Util.notEquals(
												LrsUtil.NCKD_8_本次暫不覆審,
												elf412b.getElf412b_nckdFlag())) {
											elf412b.setElf412b_nckdFlag("");
											elf412b.setElf412b_nckdDate(CapDate
													.parseDate(CapDate.ZERO_DATE));
											elf412b.setElf412b_nckdMemo("");
											elf412b.setElf412b_nextNwDt(CapDate
													.parseDate(CapDate.ZERO_DATE));
											elf412b.setElf412b_nextLtDt(CapDate
													.parseDate(CapDate.ZERO_DATE));
										}
										// ---
										// 之前有用到 cancelDt 去判斷,所以最後再設成 zeroDate
										elf412.setElf412_cancelDt(CapDate
												.parseDate(CapDate.ZERO_DATE));

										if (Util.equals(Util.trim(elf412b
												.getElf412b_oldRptId()), "")) {
											elf412b.setElf412b_oldRptId(unid);
											elf412b.setElf412b_oldRptDt(chkDate);
										}
										if (Util.equals(Util.trim(elf412b
												.getElf412b_oldDraId()), "")) {
											elf412b.setElf412b_oldDraId("");
											elf412b.setElf412b_oldDraDt(null);
										}

										if (elf412b.getElf412b_llrDate() == null) {
											elf412b.setElf412b_llrDate(CapDate
													.parseDate(CapDate.ZERO_DATE));
										}
										if (elf412b.getElf412b_lrDate() == null) {
											elf412b.setElf412b_lrDate(CapDate
													.parseDate(CapDate.ZERO_DATE));
										}

										if (elf412b.getElf412b_nckdDate() == null) {
											elf412b.setElf412b_nckdDate(CapDate
													.parseDate(CapDate.ZERO_DATE));
										}
										if (elf412b.getElf412b_cancelDt() == null) {
											elf412b.setElf412b_cancelDt(CapDate
													.parseDate(CapDate.ZERO_DATE));
										}
										if (elf412b.getElf412b_uckdDt() == null) {
											elf412b.setElf412b_uckdDt(CapDate
													.parseDate(CapDate.ZERO_DATE));
										}
										if (elf412b.getElf412b_nextNwDt() == null) {
											elf412b.setElf412b_nextNwDt(CapDate
													.parseDate(CapDate.ZERO_DATE));
										}
										if (elf412b.getElf412b_nextLtDt() == null) {
											elf412b.setElf412b_nextLtDt(CapDate
													.parseDate(CapDate.ZERO_DATE));
										}

										retrialService
												.upELF412B_DelThenInsert(elf412b);
									}
								}
							}
						}
					}
				}

			}
			// END分行實地覆審名單新增*****************************************

			// *********************************************************************************************************
			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			// C 價金履約
			// String cntrNo = Util.trim(elf411.getElf411_contract());

			if (retrialService.isPricePperformanceGuarantee(custId, dupNo,
					cntrNo)) {

				ELF412C elf412c = misELF412CService.findByPk(branch, custId,
						dupNo);

				if (elf412c == null) {
					elf412c = new ELF412C();
					elf412c.setElf412c_branch(branch);
					elf412c.setElf412c_custId(custId);
					elf412c.setElf412c_dupNo(dupNo);
					elf412c.setElf412c_newAdd(newAdd);
					elf412c.setElf412c_newDate(elf411.getElf411_dataym());
					elf412c.setElf412c_tmestamp(nowTS);
					// ---
					elf412c.setElf412c_nckdFlag("");
					elf412c.setElf412c_nckdDate(CapDate
							.parseDate(CapDate.ZERO_DATE));

					retrialService.gfnCTL_Caculate_ELF412C(elf412c);

					// elf412c.setElf412c_nckdMemo("");
					elf412c.setElf412c_nextNwDt(CapDate
							.parseDate(CapDate.ZERO_DATE));
					elf412c.setElf412c_nextLtDt(CapDate
							.parseDate(CapDate.ZERO_DATE));

					if (elf412c.getElf412c_llrDate() == null) {
						elf412c.setElf412c_llrDate(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}
					if (elf412c.getElf412c_lrDate() == null) {
						elf412c.setElf412c_lrDate(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}

					if (elf412c.getElf412c_nckdDate() == null) {
						elf412c.setElf412c_nckdDate(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}
					if (elf412c.getElf412c_cancelDt() == null) {
						elf412c.setElf412c_cancelDt(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}
					if (elf412c.getElf412c_uckdDt() == null) {
						elf412c.setElf412c_uckdDt(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}
					if (elf412c.getElf412c_nextNwDt() == null) {
						elf412c.setElf412c_nextNwDt(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}
					if (elf412c.getElf412c_nextLtDt() == null) {
						elf412c.setElf412c_nextLtDt(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}

					retrialService.upELF412C_DelThenInsert(elf412c);
				} else {

					elf412c.setElf412c_newAdd(newAdd);
					elf412c.setElf412c_newDate(elf411.getElf411_dataym());
					elf412c.setElf412c_tmestamp(nowTS);

					// J-106-0145-004 Web e-Loan
					// 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					retrialService.gfnCTL_Caculate_ELF412C(elf412c);

					if (Util.equals(LrsUtil.NCKD_7_銷戶,
							elf412c.getElf412c_nckdFlag())
							&& CrsUtil.isNOT_null_and_NOTZeroDate(elf412c
									.getElf412c_cancelDt())) {
						elf412c.setElf412c_llrDate(elf412c.getElf412c_lrDate());
						elf412c.setElf412c_lrDate(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}

					if (Util.notEquals(LrsUtil.NCKD_8_本次暫不覆審,
							elf412c.getElf412c_nckdFlag())) {
						elf412c.setElf412c_nckdFlag("");
						elf412c.setElf412c_nckdDate(CapDate
								.parseDate(CapDate.ZERO_DATE));
						elf412c.setElf412c_nckdMemo("");
						elf412c.setElf412c_nextNwDt(CapDate
								.parseDate(CapDate.ZERO_DATE));
						elf412c.setElf412c_nextLtDt(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}
					// ---
					// 之前有用到 cancelDt 去判斷,所以最後再設成 zeroDate
					elf412.setElf412_cancelDt(CapDate
							.parseDate(CapDate.ZERO_DATE));

					if (elf412c.getElf412c_llrDate() == null) {
						elf412c.setElf412c_llrDate(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}
					if (elf412c.getElf412c_lrDate() == null) {
						elf412c.setElf412c_lrDate(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}

					if (elf412c.getElf412c_nckdDate() == null) {
						elf412c.setElf412c_nckdDate(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}
					if (elf412c.getElf412c_cancelDt() == null) {
						elf412c.setElf412c_cancelDt(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}
					if (elf412c.getElf412c_uckdDt() == null) {
						elf412c.setElf412c_uckdDt(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}
					if (elf412c.getElf412c_nextNwDt() == null) {
						elf412c.setElf412c_nextNwDt(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}
					if (elf412c.getElf412c_nextLtDt() == null) {
						elf412c.setElf412c_nextLtDt(CapDate
								.parseDate(CapDate.ZERO_DATE));
					}

					retrialService.upELF412C_DelThenInsert(elf412c);

				}
			}

			// END價金履約覆審名單新增*****************************************

		}
	}

	/**
	 * 不是在每月1日的批次，都把最新的評等寫到ELF412(會讓 應覆審日 很頻繁的變化)
	 * 
	 * 在覆審辦法裡有一段描述 ...... 以前次覆審日之企業模型評等或資信評等為準 ......
	 * 
	 * 
	 * 判斷當 ELF412 ● 無評等時，才把最新的評等寫入 ● 若已有評等，則不寫入
	 */
	private void fill_elf412_MowOrCrd(ELF412 elf412,
			Map<String, String> mowType_2to1, Map<String, String> crdtType_2to1) {
		// 若已有評等，不填入最新的值
		if (Util.isNotEmpty(Util.trim(elf412.getElf412_crdType()))
				|| Util.isNotEmpty(Util.trim(elf412.getElf412_mowType()))) {
			return;
		}

		String custId = Util.trim(elf412.getElf412_custId());
		String dupNo = Util.trim(elf412.getElf412_dupNo());
		L170M01E l170m01e = new L170M01E();
		if (true) {
			// 先判斷 mowTbl
			retrialService.gfnCTL_Import_MONTHBAL(l170m01e, custId, dupNo);

			if (Util.isNotEmpty(Util.trim(l170m01e.getGrade()))) {
				// ======
				// 把 2 碼的 mowType 轉成 1碼的格式，來上傳
				elf412.setElf412_mowType(LMSUtil.getDesc(mowType_2to1,
						l170m01e.getCrdType()));
				elf412.setElf412_mowTbl1(l170m01e.getGrade());

			} else {
				// 若無，再判斷 crdTbl
				retrialService.gfnCTL_Import_CRDTTBL(l170m01e, custId, dupNo);
				if (Util.isNotEmpty(Util.trim(l170m01e.getGrade()))) {
					// ======
					// 把 2 碼的 creditType 轉成 1碼的格式，來上傳
					elf412.setElf412_crdType(LMSUtil.getDesc(crdtType_2to1,
							l170m01e.getCrdType()));
					elf412.setElf412_crdtTbl(l170m01e.getGrade());
				} else {
					// still empty
				}
			}
		}
	}

	private int _cnt_emptyELF495_CNTRNO(List<Map<String, Object>> list) {
		int r = 0;
		for (Map<String, Object> m : list) {
			String ELF495_CNTRNO = Util.trim(m.get("ELF495_CNTRNO"));
			if (Util.isEmpty(ELF495_CNTRNO)) {
				r++;
			}
		}
		return r;
	}

	// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
    // J-110-0272 抽樣覆審 baseDate為執行當下的當月1號
	private void gfnCTL_Import_DATA(String branch,
			Map<String, String[]> isRescueMap, Date baseDate) {

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		Map<String, String> process_elf412b = new HashMap<String, String>();

		// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
		Map<String, String> process_elf412c = new HashMap<String, String>();

		// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
		if (isRescueMap == null) {
			isRescueMap = new TreeMap<String, String[]>();
		}

		for (ELF412 elf412 : misELF412Service.findByBranch(branch)) {

			// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
			String fullCustId = Util.trim(elf412.getElf412_custId()) + "-"
					+ Util.trim(elf412.getElf412_dupNo());
			String isRescue = "";
			String guarFlag = "";
			if (!isRescueMap.containsKey(fullCustId)) {
				String[] arr = retrialService.getOnlyRescueCase(branch,
						Util.trim(elf412.getElf412_custId()),
						Util.trim(elf412.getElf412_dupNo()), "");
				isRescue = arr[0];
				guarFlag = arr[1];
				isRescueMap.put(fullCustId, arr);
			} else {
				String[] valArr = isRescueMap.get(fullCustId);
				isRescue = valArr[0];
				guarFlag = valArr[1];
			}
			elf412.setElf412_isRescue(isRescue);
			elf412.setElf412_guarFlag(guarFlag);

			// if (Util.equals(elf412.getElf412_custId(), "13085136")
			// || Util.equals(elf412.getElf412_custId(), "80293513")) {
			// System.out.println("elf412.getElf412_custId()="
			// + elf412.getElf412_custId());
			// }

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			ELF412B elf412b = misELF412BService.findByPk(
					elf412.getElf412_branch(), elf412.getElf412_custId(),
					elf412.getElf412_dupNo());

			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			ELF412C elf412c = misELF412CService.findByPk(
					elf412.getElf412_branch(), elf412.getElf412_custId(),
					elf412.getElf412_dupNo());

			// 註記這筆已經處理過，後續補沒有ELF412 但有 ELF412B時就不再處理
			String buildKey = LMSUtil.getCustKey_len10custId(
					elf412.getElf412_custId(), elf412.getElf412_dupNo());
			process_elf412b.put(buildKey, buildKey);

			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			process_elf412c.put(buildKey, buildKey);

			// 匯入LNF022 gfnCTL_Import_LNF022
			// BY********************************************************************************************
			// '1.匯入LNF022 gfnCTL_Import_LNF022 BY
			String[] impLNF022_arr = retrialService.gfnCTL_Import_LNF022(
					elf412.getElf412_branch(), elf412.getElf412_custId(),
					elf412.getElf412_dupNo(), elf412.getElf412_cancelDt());

			// ELF412
			elf412.setElf412_cState(impLNF022_arr[0]);
			elf412.setElf412_cancelDt(CapDate.parseDate(impLNF022_arr[1]));

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			// ELF412B
			if (elf412b != null) {
				elf412b.setElf412b_cState(impLNF022_arr[0]);
				elf412b.setElf412b_cancelDt(CapDate.parseDate(impLNF022_arr[1]));
			}

			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			// ELF412C
			if (elf412c != null) {
				elf412c.setElf412c_cState(impLNF022_arr[0]);
				elf412c.setElf412c_cancelDt(CapDate.parseDate(impLNF022_arr[1]));
			}

			// 已銷戶，但不覆審註記不為7，則將不覆審註記就改成7，要不然覆審名單會出來********************************************************************************************
			// 'ELF412-已銷戶，但不覆審註記不為7，則將不覆審註記就改成7，要不然覆審名單會出來
			if (Util.notEquals(LrsUtil.NCKD_7_銷戶, elf412.getElf412_nckdFlag())
					&& CrsUtil.isNOT_null_and_NOTZeroDate(elf412
							.getElf412_cancelDt())) {
				elf412.setElf412_nckdFlag(LrsUtil.NCKD_7_銷戶);
				elf412.setElf412_nckdDate(elf412.getElf412_cancelDt());

				// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
				// 銷戶時，也要一併把實地覆審註記清掉
				elf412.setElf412_realCkFg("");
				elf412.setElf412_realDt(null);
			}

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			// 'ELF412B-已銷戶，但不覆審註記不為7，則將不覆審註記就改成7，要不然覆審名單會出來
			if (elf412b != null) {
				if (Util.notEquals(LrsUtil.NCKD_7_銷戶,
						elf412b.getElf412b_nckdFlag())
						&& CrsUtil.isNOT_null_and_NOTZeroDate(elf412b
								.getElf412b_cancelDt())) {

					elf412b.setElf412b_nckdFlag(LrsUtil.NCKD_7_銷戶);
					elf412b.setElf412b_nckdDate(elf412b.getElf412b_cancelDt());
				}
			}

			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			// 'ELF412C-已銷戶，但不覆審註記不為7，則將不覆審註記就改成7，要不然覆審名單會出來
			if (elf412c != null) {
				if (Util.notEquals(LrsUtil.NCKD_7_銷戶,
						elf412c.getElf412c_nckdFlag())
						&& CrsUtil.isNOT_null_and_NOTZeroDate(elf412c
								.getElf412c_cancelDt())) {

					elf412c.setElf412c_nckdFlag(LrsUtil.NCKD_7_銷戶);
					elf412c.setElf412c_nckdDate(elf412c.getElf412c_cancelDt());
				}
			}

			// 加強銷戶***********************************************************************************************
			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			// 加強銷戶，價金履約只針對價金履約額度覆審

			// ***重要:如果新案增額NEWADD有註記，但判斷銷戶時，為避免新案沒有被覆審到，所以再gfnCTL_Caculate_ELF412C
			// 的 gfnCTL_Caculate_ELF412C // '處理不覆審註記***** if
			// (Util.isNotEmpty(Util.trim(elf412c.getElf412c_newAdd()))) { if
			// (Util.notEquals(LrsUtil.NCKD_8_本次暫不覆審, 會再把銷戶註記拿掉

			Map<String, Object> lnf020CtlTypeMap = misLNF020Service
					.findCtlTypeByBrNoAndCustId(elf412.getElf412_branch(),
							elf412.getElf412_custId(), elf412.getElf412_dupNo());

			String CTLTYPE_A = Util.trim(MapUtils.getString(lnf020CtlTypeMap,
					"CTLTYPE_A", "N")); // 一般授信額度
			String CTLTYPE_C = Util.trim(MapUtils.getString(lnf020CtlTypeMap,
					"CTLTYPE_C", "N")); // 價金履約額度
			if (Util.equals(CTLTYPE_A, "")) {
				CTLTYPE_A = "N";
			}
			if (Util.equals(CTLTYPE_C, "")) {
				CTLTYPE_C = "N";
			}

			if (Util.equals(CTLTYPE_C, "N")) {
				// 沒有價金履約保證，ELF412C所以再變成銷戶
				if (elf412c != null) {
					if (Util.notEquals(LrsUtil.NCKD_7_銷戶,
							elf412c.getElf412c_nckdFlag())) {

						elf412c.setElf412c_nckdFlag(LrsUtil.NCKD_7_銷戶);
						elf412c.setElf412c_nckdDate(new Date());
						elf412c.setElf412c_cancelDt(new Date());
					} else {
						// 這段一定要加，免得retrialService.gfnCTL_Caculate_ELF412
						// 因為CANCEL_DATE= 0001-01-01 造成銷戶註記又被取消
						if (CrsUtil.isNull_or_ZeroDate(elf412c
								.getElf412c_cancelDt())) {
							elf412c.setElf412c_cancelDt(new Date());
						}
					}
				}

			}

			if (Util.equals(CTLTYPE_A, "N") && Util.equals(CTLTYPE_C, "Y")) {
				// 只有價金履約保證，所以ELF412 與 ELF412B 再變成銷戶
				if (elf412 != null) {
					if (Util.notEquals(LrsUtil.NCKD_7_銷戶,
							elf412.getElf412_nckdFlag())) {
						elf412.setElf412_nckdFlag(LrsUtil.NCKD_7_銷戶);
						elf412.setElf412_nckdDate(new Date());
						elf412.setElf412_cancelDt(new Date());
					} else {
						// 這段一定要加，免得retrialService.gfnCTL_Caculate_ELF412
						// 因為CANCEL_DATE= 0001-01-01 造成銷戶註記又被取消
						if (CrsUtil.isNull_or_ZeroDate(elf412
								.getElf412_cancelDt())) {
							elf412.setElf412_cancelDt(new Date());
						}
					}
				}

				if (elf412b != null) {
					if (Util.notEquals(LrsUtil.NCKD_7_銷戶,
							elf412b.getElf412b_nckdFlag())) {
						elf412b.setElf412b_nckdFlag(LrsUtil.NCKD_7_銷戶);
						elf412b.setElf412b_nckdDate(new Date());
						elf412b.setElf412b_cancelDt(new Date());
					} else {
						// 這段一定要加，免得retrialService.gfnCTL_Caculate_ELF412
						// 因為CANCEL_DATE= 0001-01-01 造成銷戶註記又被取消
						if (CrsUtil.isNull_or_ZeroDate(elf412b
								.getElf412b_cancelDt())) {
							elf412b.setElf412b_cancelDt(new Date());
						}
					}
				}

			}

			// 逾催呆戶不覆審********************************************************************************************
			// 'ELF412-逾催呆戶不覆審
			if (LrsUtil.isNckdFlag_EMPTY_8(elf412.getElf412_nckdFlag())) {
				String[] cStateArr = new String[] { "2", "3", "4" };
				if (CrsUtil.inCollection(elf412.getElf412_cState(), cStateArr)) {
					elf412.setElf412_nckdFlag(LrsUtil.NCKD_6_已列報為逾期放款或轉列催收款項之案件);
					elf412.setElf412_nckdDate(new Date());
				}
			}

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			// 'ELF412B-逾催呆戶不覆審
			if (elf412b != null) {
				if (LrsUtil.isNckdFlag_EMPTY_8(elf412b.getElf412b_nckdFlag())) {
					String[] cStateArr = new String[] { "2", "3", "4" };
					if (CrsUtil.inCollection(elf412b.getElf412b_cState(),
							cStateArr)) {
						elf412b.setElf412b_nckdFlag(LrsUtil.NCKD_6_已列報為逾期放款或轉列催收款項之案件);
						elf412b.setElf412b_nckdDate(new Date());
					}
				}
			}

			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			// 'ELF412C-逾催呆戶不覆審
			if (elf412c != null) {
				if (LrsUtil.isNckdFlag_EMPTY_8(elf412c.getElf412c_nckdFlag())) {
					String[] cStateArr = new String[] { "2", "3", "4" };
					if (CrsUtil.inCollection(elf412c.getElf412c_cState(),
							cStateArr)) {
						elf412c.setElf412c_nckdFlag(LrsUtil.NCKD_6_已列報為逾期放款或轉列催收款項之案件);
						elf412c.setElf412c_nckdDate(new Date());
					}
				}
			}

			// ********************************************************************************************

			LinkedHashMap<String, String> elf412_DBUCOID_map = new LinkedHashMap<String, String>();
			LinkedHashMap<String, String> elf412_OBUCOID_map = new LinkedHashMap<String, String>();

			// '如果未銷戶，則才要引進下列資料，以節省時間
			ELF412 nextMdElf412 = new ELF412();
			if (CrsUtil.isNull_or_ZeroDate(elf412.getElf412_cancelDt())) {

				// if(Util.equals("22839564", elf412.getElf412_custId())){
				// System.out.println("22839564");
				// }

				// '1.LN.LNFE0851 異常戶檔 gfnCTL_Import_LNFE0851 BY 分行
				if (Util.isEmpty(Util.trim(elf412.getElf412_mdFlag()))) {
					// 沒有異常通報過，不需再去抓LNFE0851，因為本行沒有取消異常通報之機制
					gfnCTL_Import_LNFE0851(elf412);
				} else {
					// '已經有異常通報者，再去抓LNFE0851，看還有沒有更新的異常通報
					// J-104-0192-001 修改Web e-Loan企金授信覆審異常通報發生後之覆審周期計算
					// 新舊異常通報狀態判斷在 gfnCTL_Caculate_ELF412 比對
					gfnCTL_Import_LNFE0851_Next(elf412, nextMdElf412);
				}

				// '3.匯入LNF022 LNF025 DBU、OBU 共管 gfnCTL_Import_LNF025
				retrialService.gfnCTL_Import_LNF025(elf412.getElf412_branch(),
						elf412.getElf412_custId(), elf412.getElf412_dupNo(),
						elf412_DBUCOID_map, elf412_OBUCOID_map);

				if (!CollectionUtils.isEmpty(elf412_DBUCOID_map)
						|| !CollectionUtils.isEmpty(elf412_OBUCOID_map)) {
					elf412.setElf412_dbuObu("Y");
				} else {
					elf412.setElf412_dbuObu("");
				}

				// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
				String custId = Util.trim(elf412.getElf412_custId());
				String dupNo = Util.trim(elf412.getElf412_dupNo());
				String brNo = Util.trim(elf412.getElf412_branch());

				// 以下為Ver20161101
				// 對本行主辦之聯貸案件(不含聯行間參貸案件)或主辦單位(或主管單位)認為有實際需要指定之重要企金授信個案，應每年辦理一次實地覆審，主辦單位應由營業單位派員會同辦理實地覆審。
				// 已經於修改案取消，改由土建融才要實地覆審
				// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
				// 取得ALOAN實際註記與實地覆審基準日
				// String aReealDt = "";
				// String aRealCkFg = "";
				// String aRealContract = "";
				// if (Util.notEquals(custId, "") && Util.notEquals(dupNo, "")
				// && Util.notEquals(brNo, "")) {
				// Map<String, String> minDataMap = retrialService
				// .gfnGetAloanRealDt(custId, dupNo, brNo);
				// if (minDataMap != null && !minDataMap.isEmpty()) {
				// aReealDt = MapUtils.getString(minDataMap, "realDt", "");
				// aRealCkFg = MapUtils.getString(minDataMap, "realCkFg",
				// "");
				// aRealContract = Util.trim(MapUtils.getString(
				// minDataMap, "realContract", ""));
				// }
				//
				// elf412.setElf412_realCkFg(aRealCkFg);
				//
				// if (Util.equals(aRealCkFg, "Y")) {
				// // ALOAN要實地覆審
				// if (CrsUtil.isNull_or_ZeroDate(elf412
				// .getElf412_realDt())) {
				// // DB沒有實地覆審基準日
				// if (Util.notEquals(aReealDt, "")) {
				// // 有ALOAN最小實地覆審日
				// elf412.setElf412_realDt(Util
				// .parseDate(aReealDt));
				// } else {
				// // 真的沒有就塞今天----不應該執行到此行，但以防萬一(代表gfnGetAloanRealDt
				// // 有問題)
				// elf412.setElf412_realDt(CapDate
				// .getCurrentTimestamp());
				// }
				//
				// } else {
				// // DB有實地覆審基準日，不異動DB目前實地覆審基準日
				//
				// }
				// } else if (Util.equals(aRealCkFg, "N")) {
				// // ALOAN不要實地覆審
				// elf412.setElf412_realDt(null);
				// }
				//
				// }

				// 取得ALOAN實際註記與實地覆審基準日
				// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行

				// A 一般覆審/土建融實地覆審
				if (Util.notEquals(custId.substring(2, 3), "Z")) {
					// OBU戶不實地覆審
					String aReealDt = "";
					String aRealCkFg = "";
					String aRealContract = "";
					if (Util.notEquals(custId, "") && Util.notEquals(dupNo, "")
							&& Util.notEquals(brNo, "")) {
						Map<String, String> minDataMap = retrialService
								.gfnGetAloanRealDt2(custId, dupNo, brNo);
						if (minDataMap != null && !minDataMap.isEmpty()) {
							aReealDt = MapUtils.getString(minDataMap, "realDt",
									"");
							aRealCkFg = MapUtils.getString(minDataMap,
									"realCkFg", "");
							aRealContract = Util.trim(MapUtils.getString(
									minDataMap, "realContract", ""));
						}

						elf412.setElf412_realCkFg(aRealCkFg);

						if (Util.equals(aRealCkFg, "Y")) {
							// ALOAN要實地覆審
							if (CrsUtil.isNull_or_ZeroDate(elf412
									.getElf412_realDt())) {
								// DB沒有實地覆審基準日
								if (Util.notEquals(aReealDt, "")) {
									// 有ALOAN最小實地覆審日
									elf412.setElf412_realDt(Util
											.parseDate(aReealDt));
								} else {
									// 真的沒有就塞今天----不應該執行到此行，但以防萬一(代表gfnGetAloanRealDt
									// 有問題)
									elf412.setElf412_realDt(CapDate
											.getCurrentTimestamp());
								}

							} else {
								// DB有實地覆審基準日，不異動DB目前實地覆審基準日

							}
						} else if (Util.equals(aRealCkFg, "N")) {
							// ALOAN不要實地覆審
							elf412.setElf412_realDt(null);
						}

					}
				}

			}

			// 7.算週期***************************************************************************************
			// '7.算週期

			retrialService.gfnCTL_Caculate_ELF412(elf412, nextMdElf412);
            // J-110-0272 抽樣覆審
			String randomOn = Util.trim(lmsService.getSysParamDataValue("LRS_RANDOMTYPE_ON"));
			if(Util.equals(randomOn, "Y")) {
				if (baseDate != null) {
					DateFormat sdf = new SimpleDateFormat("MM");
					int month = Util.parseInt(sdf.format(baseDate));
					if (month == 1) { // 1月份才執行
						elf412.setElf412_randomType("");    // 先清空 去年抽樣(有中有覆審會清空、沒中還會有值) 今年可能不是抽樣
						// 上次覆審日為去年1/1~12/31 且 不覆審代碼為空或8.本次暫不覆審或13
						if (LrsUtil.isNckdFlag_EMPTY_8(elf412.getElf412_nckdFlag())) {
							if (!CrsUtil.isNull_or_ZeroDate(elf412.getElf412_lrDate())) {
								String yyyyStr = TWNDate.toAD(CapDate.addYears(baseDate, -1)).substring(0, 4);
								Date bgnDate = CapDate.parseDate(yyyyStr + "-01-01");
								Date endDate = CapDate.parseDate(yyyyStr + "-12-31");
								if (LMSUtil.cmpDate(elf412.getElf412_lrDate(), ">=", bgnDate)
										&& LMSUtil.cmpDate(elf412.getElf412_lrDate(), "<=", endDate)) {
									String custId = Util.trim(elf412.getElf412_custId());
									String dupNo = Util.trim(elf412.getElf412_dupNo());
									String endDateStr = TWNDate.toAD(endDate);
									if (retrialService.isSamplingType(custId, dupNo, endDateStr)) {
										elf412.setElf412_randomType(LrsUtil.RANDOMTYPE_A_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件);

										// baseDate 應該為 yyyy-01-01，兩個應該會等於，保險起見還是直接塞當年度01月01號，方便後續抓Key值
										Date thisYear = CapDate.getDate(
												TWNDate.toAD(new Date()).substring(0, 4) + "-01-01", "yyyy-MM-dd");
										L186M01A l186m01a = retrialService.findL186M01AByUniqueKey(thisYear, Util.trim(elf412.getElf412_branch()),
												Util.trim(elf412.getElf412_custId()), Util.trim(elf412.getElf412_dupNo()),
												LrsUtil.RANDOMTYPE_A_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件);
										if (l186m01a == null) {
											l186m01a = new L186M01A();
											l186m01a.setDataDate(thisYear);
											l186m01a.setBranchId(Util.trim(elf412.getElf412_branch()));
											l186m01a.setCustId(Util.trim(elf412.getElf412_custId()));
											l186m01a.setDupNo(Util.trim(elf412.getElf412_dupNo()));
											l186m01a.setRandomType(LrsUtil.RANDOMTYPE_A_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件);
										}
										l186m01a.setUpdateTime(CapDate.getCurrentTimestamp());
										retrialService.save(l186m01a);
									}
								}
							}
						}
					}
				}
			}
			elf412.setElf412_tmestamp(CapDate.getCurrentTimestamp());
			retrialService.upELF412_DelThenInsert(elf412);

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			// '7.算週期
			if (elf412b != null) {
				retrialService.gfnCTL_Caculate_ELF412B(elf412b);
				elf412b.setElf412b_tmestamp(CapDate.getCurrentTimestamp());
				retrialService.upELF412B_DelThenInsert(elf412b);
			}

			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			// '7.算週期
			if (elf412c != null) {
				retrialService.gfnCTL_Caculate_ELF412C(elf412c);
				elf412c.setElf412c_tmestamp(CapDate.getCurrentTimestamp());
				retrialService.upELF412C_DelThenInsert(elf412c);
			}

		}

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		// 補沒有ELF412 但有ELF412B的名單
		for (ELF412B elf412b : misELF412BService.findByBranch(branch)) {
			String buildKey = LMSUtil.getCustKey_len10custId(
					elf412b.getElf412b_custId(), elf412b.getElf412b_dupNo());

			// 前面處理ELF412時已經一起處理了
			if (process_elf412b.containsKey(buildKey)) {
				continue;
			}

			// 匯入LNF022 gfnCTL_Import_LNF022
			// BY********************************************************************************************
			// '1.匯入LNF022 gfnCTL_Import_LNF022 BY
			String[] impLNF022_arr = retrialService.gfnCTL_Import_LNF022(
					elf412b.getElf412b_branch(), elf412b.getElf412b_custId(),
					elf412b.getElf412b_dupNo(), elf412b.getElf412b_cancelDt());

			elf412b.setElf412b_cState(impLNF022_arr[0]);
			elf412b.setElf412b_cancelDt(CapDate.parseDate(impLNF022_arr[1]));

			// 已銷戶，但不覆審註記不為7，則將不覆審註記就改成7，要不然覆審名單會出來********************************************************************************************
			// 'ELF412B-已銷戶，但不覆審註記不為7，則將不覆審註記就改成7，要不然覆審名單會出來
			if (Util.notEquals(LrsUtil.NCKD_7_銷戶, elf412b.getElf412b_nckdFlag())
					&& CrsUtil.isNOT_null_and_NOTZeroDate(elf412b
							.getElf412b_cancelDt())) {
				elf412b.setElf412b_nckdFlag(LrsUtil.NCKD_7_銷戶);
				elf412b.setElf412b_nckdDate(elf412b.getElf412b_cancelDt());
			}

			// 逾催呆戶不覆審********************************************************************************************
			// 'ELF412B-逾催呆戶不覆審
			if (LrsUtil.isNckdFlag_EMPTY_8(elf412b.getElf412b_nckdFlag())) {
				String[] cStateArr = new String[] { "2", "3", "4" };
				if (CrsUtil
						.inCollection(elf412b.getElf412b_cState(), cStateArr)) {
					elf412b.setElf412b_nckdFlag(LrsUtil.NCKD_6_已列報為逾期放款或轉列催收款項之案件);
					elf412b.setElf412b_nckdDate(new Date());
				}
			}

			// 加強銷戶*********************************************************************************************************
			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			// 加強銷戶，價金履約只針對價金履約額度覆審
			Map<String, Object> lnf020CtlTypeMap = misLNF020Service
					.findCtlTypeByBrNoAndCustId(elf412b.getElf412b_branch(),
							elf412b.getElf412b_custId(),
							elf412b.getElf412b_dupNo());

			String CTLTYPE_A = Util.trim(MapUtils.getString(lnf020CtlTypeMap,
					"CTLTYPE_A", "N")); // 一般授信額度
			String CTLTYPE_C = Util.trim(MapUtils.getString(lnf020CtlTypeMap,
					"CTLTYPE_C", "N")); // 價金履約額度
			if (Util.equals(CTLTYPE_A, "")) {
				CTLTYPE_A = "N";
			}
			if (Util.equals(CTLTYPE_C, "")) {
				CTLTYPE_C = "N";
			}
			if (Util.equals(CTLTYPE_A, "N") && Util.equals(CTLTYPE_C, "Y")) {
				// 只有價金履約保證，所以ELF412再變成銷戶
				if (elf412b != null) {
					if (Util.notEquals(LrsUtil.NCKD_7_銷戶,
							elf412b.getElf412b_nckdFlag())) {
						elf412b.setElf412b_nckdFlag(LrsUtil.NCKD_7_銷戶);
						elf412b.setElf412b_nckdDate(new Date());
						elf412b.setElf412b_cancelDt(new Date());
					} else {
						// 這段一定要加，免得retrialService.gfnCTL_Caculate_ELF412
						// 因為CANCEL_DATE= 0001-01-01 造成銷戶註記又被取消
						if (CrsUtil.isNull_or_ZeroDate(elf412b
								.getElf412b_cancelDt())) {
							elf412b.setElf412b_cancelDt(new Date());
						}
					}
				}

			}

			// '7.算週期
			retrialService.gfnCTL_Caculate_ELF412B(elf412b);
			elf412b.setElf412b_tmestamp(CapDate.getCurrentTimestamp());
			retrialService.upELF412B_DelThenInsert(elf412b);

		}

		// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
		// 補沒有ELF412 但有ELF412C的名單
		for (ELF412C elf412c : misELF412CService.findByBranch(branch)) {
			String buildKey = LMSUtil.getCustKey_len10custId(
					elf412c.getElf412c_custId(), elf412c.getElf412c_dupNo());

			// 前面處理ELF412時已經一起處理了
			if (process_elf412c.containsKey(buildKey)) {
				continue;
			}

			// 匯入LNF022 gfnCTL_Import_LNF022
			// BY********************************************************************************************
			// '1.匯入LNF022 gfnCTL_Import_LNF022 BY
			String[] impLNF022_arr = retrialService.gfnCTL_Import_LNF022(
					elf412c.getElf412c_branch(), elf412c.getElf412c_custId(),
					elf412c.getElf412c_dupNo(), elf412c.getElf412c_cancelDt());

			elf412c.setElf412c_cState(impLNF022_arr[0]);
			elf412c.setElf412c_cancelDt(CapDate.parseDate(impLNF022_arr[1]));

			// 已銷戶，但不覆審註記不為7，則將不覆審註記就改成7，要不然覆審名單會出來********************************************************************************************
			// 'ELF412C-已銷戶，但不覆審註記不為7，則將不覆審註記就改成7，要不然覆審名單會出來
			if (Util.notEquals(LrsUtil.NCKD_7_銷戶, elf412c.getElf412c_nckdFlag())
					&& CrsUtil.isNOT_null_and_NOTZeroDate(elf412c
							.getElf412c_cancelDt())) {
				elf412c.setElf412c_nckdFlag(LrsUtil.NCKD_7_銷戶);
				elf412c.setElf412c_nckdDate(elf412c.getElf412c_cancelDt());
			}

			// 加強銷戶*********************************************************************************************************
			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			// 加強銷戶，價金履約只針對價金履約額度覆審
			Map<String, Object> lnf020CtlTypeMap = misLNF020Service
					.findCtlTypeByBrNoAndCustId(elf412c.getElf412c_branch(),
							elf412c.getElf412c_custId(),
							elf412c.getElf412c_dupNo());

			String CTLTYPE_A = Util.trim(MapUtils.getString(lnf020CtlTypeMap,
					"CTLTYPE_A", "N")); // 一般授信額度
			String CTLTYPE_C = Util.trim(MapUtils.getString(lnf020CtlTypeMap,
					"CTLTYPE_C", "N")); // 價金履約額度
			if (Util.equals(CTLTYPE_A, "")) {
				CTLTYPE_A = "N";
			}
			if (Util.equals(CTLTYPE_C, "")) {
				CTLTYPE_C = "N";
			}
			if (Util.equals(CTLTYPE_C, "N")) {
				// 沒有價金履約保證，ELF412C所以再變成銷戶
				if (elf412c != null) {
					if (Util.notEquals(LrsUtil.NCKD_7_銷戶,
							elf412c.getElf412c_nckdFlag())) {

						elf412c.setElf412c_nckdFlag(LrsUtil.NCKD_7_銷戶);
						elf412c.setElf412c_nckdDate(new Date());
						elf412c.setElf412c_cancelDt(new Date());
					} else {
						// 這段一定要加，免得retrialService.gfnCTL_Caculate_ELF412
						// 因為CANCEL_DATE= 0001-01-01 造成銷戶註記又被取消
						if (CrsUtil.isNull_or_ZeroDate(elf412c
								.getElf412c_cancelDt())) {
							elf412c.setElf412c_cancelDt(new Date());
						}
					}
				}

			}

			// 逾催呆戶不覆審********************************************************************************************
			// 'ELF412C-逾催呆戶不覆審
			if (LrsUtil.isNckdFlag_EMPTY_8(elf412c.getElf412c_nckdFlag())) {
				String[] cStateArr = new String[] { "2", "3", "4" };
				if (CrsUtil
						.inCollection(elf412c.getElf412c_cState(), cStateArr)) {
					elf412c.setElf412c_nckdFlag(LrsUtil.NCKD_6_已列報為逾期放款或轉列催收款項之案件);
					elf412c.setElf412c_nckdDate(new Date());
				}
			}

			// '7.算週期
			retrialService.gfnCTL_Caculate_ELF412C(elf412c);
			elf412c.setElf412c_tmestamp(CapDate.getCurrentTimestamp());
			retrialService.upELF412C_DelThenInsert(elf412c);

		}

	}

	private void gfnCTL_Import_LNFE0851(ELF412 elf412) {
		Map<String, Object> map = misdbBASEService.getLrsLNFE0854(
				elf412.getElf412_custId(), elf412.getElf412_dupNo());
		if (!CollectionUtils.isEmpty(map)) {
			String mdFlag = Util.trim(map.get("LNFE0854_MDFLAG"));
			if (Util.isEmpty(mdFlag)) {
				mdFlag = "9";
			}
			elf412.setElf412_mdFlag(mdFlag);
			elf412.setElf412_mdDt((Date) map.get("MDDT"));
			elf412.setElf412_process(StringUtils.replace(
					Util.trim(map.get("LNFE0855_CONTENT")), "'", "’"));
		}
	}

	/**
	 * J-104-0192-001 修改Web e-Loan企金授信覆審異常通報發生後之覆審周期計算
	 * 
	 * @param elf412
	 */
	private void gfnCTL_Import_LNFE0851_Next(ELF412 elf412, ELF412 nextMdElf412) {
		Map<String, Object> map = misdbBASEService
				.getLrsLNFE0854Next(
						elf412.getElf412_custId(),
						elf412.getElf412_dupNo(),
						CrsUtil.isNull_or_ZeroDate(elf412.getElf412_mdDt()) ? CapDate.ZERO_DATE
								: Util.trim(TWNDate.toAD(elf412
										.getElf412_mdDt())),
						CrsUtil.isNull_or_ZeroDate(elf412.getElf412_lrDate()) ? CapDate.ZERO_DATE
								: Util.trim(TWNDate.toAD(elf412
										.getElf412_lrDate())));
		if (!CollectionUtils.isEmpty(map)) {
			String mdFlag = Util.trim(map.get("LNFE0854_MDFLAG"));
			if (Util.isEmpty(mdFlag)) {
				mdFlag = "9";
			}
			nextMdElf412.setElf412_mdFlag(mdFlag);
			nextMdElf412.setElf412_mdDt((Date) map.get("MDDT"));
			nextMdElf412.setElf412_process(StringUtils.replace(
					Util.trim(map.get("LNFE0855_CONTENT")), "'", "’"));
		}
	}

	@Override
	public void setterL181Model(L181M01A meta, L181M01B bfObj, L181M01B afObj,
			String ctlType) throws CapException {
		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能

		if (Util.equals(ctlType, "")) {
			ctlType = LrsUtil.CTLTYPE_主辦覆審;
		}
		meta.setCtlType(ctlType);
		if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
			ELF412B elf412b = misELF412BService.findByPk(meta.getElfBranch(),
					meta.getCustId(), meta.getDupNo());
			if (elf412b != null) {
				meta.setElfDataDt(elf412b.getElf412b_dataDt());
				meta.setElfLLRDate(elf412b.getElf412b_llrDate());
				meta.setElfOCkdLine(Util.trim(elf412b.getElf412b_ockdLine()));
				meta.setElfCState(Util.trim(elf412b.getElf412b_cState()));
				meta.setElfCancelDt(elf412b.getElf412b_cancelDt());
				meta.setElfUpdDate(elf412b.getElf412b_upddate());
				meta.setElfUpdater(elf412b.getElf412b_updater());
				meta.setElfTmeStamp(elf412b.getElf412b_tmestamp());

				if (CrsUtil.isNull_or_ZeroDate(meta.getElfCancelDt())) {
					meta.setElfCancelDt(null);
				}
				if (CrsUtil.isNull_or_ZeroDate(meta.getElfUpdDate())) {
					meta.setElfUpdDate(null);
				}
			}
			// ---
			if (true) {
				bfObj.setMainId(meta.getMainId());
				bfObj.setType("1");
				if (elf412b != null) {
					bfObj.setElfMainCust(Util.trim(elf412b
							.getElf412b_mainCust()));
					bfObj.setElfCrdType(Util.trim(elf412b.getElf412b_crdType()));
					bfObj.setElfCrdTTbl(Util.trim(elf412b.getElf412b_crdtTbl()));
					bfObj.setElfMowType(Util.trim(elf412b.getElf412b_mowType()));
					bfObj.setElfMowTbl1(Util.trim(elf412b.getElf412b_mowTbl1()));
					bfObj.setElfLRDate(elf412b.getElf412b_lrDate());
					bfObj.setElfRCkdLine(Util.trim(elf412b
							.getElf412b_rckdLine()));
					bfObj.setElfUCkdLINE(Util.trim(elf412b
							.getElf412b_uckdLine()));
					bfObj.setElfUCkdDt(elf412b.getElf412b_uckdDt());

					bfObj.setElfNewAdd(Util.trim(elf412b.getElf412b_newAdd()));
					bfObj.setElfNewDate(LrsUtil
							.elf412_rocDateStr_toYYYYMM(elf412b
									.getElf412b_newDate()));
					bfObj.setElfNCkdFlag(Util.trim(elf412b
							.getElf412b_nckdFlag()));
					bfObj.setElfNCkdDate(elf412b.getElf412b_nckdDate());
					bfObj.setElfNCkdMemo(Util.trim(elf412b
							.getElf412b_nckdMemo()));
					bfObj.setElfNextNwDt(elf412b.getElf412b_nextNwDt());

					bfObj.setElfMemo(Util.trim(elf412b.getElf412b_memo()));
					bfObj.setElfFcrdType(Util.trim(elf412b
							.getElf412b_fcrdType()));
					bfObj.setElfFcrdArea(Util.trim(elf412b
							.getElf412b_fcrdArea()));
					bfObj.setElfFcrdPred(Util.trim(elf412b
							.getElf412b_fcrdPred()));
					bfObj.setElfFcrdGrad(Util.trim(elf412b
							.getElf412b_fcrdGrad()));

					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					bfObj.setElfOldRptId(Util.trim(elf412b
							.getElf412b_oldRptId()));
					bfObj.setElfOldRptDt(elf412b.getElf412b_oldRptDt());
					bfObj.setElfNewRptId(Util.trim(elf412b
							.getElf412b_newRptId()));
					bfObj.setElfNewRptDt(elf412b.getElf412b_newRptDt());

				}

				LrsUtil.model_zeroDate_to_null(bfObj);
				LrsUtil.model_elf412Str_to_N(bfObj);

			}
		} else if (Util.equals(ctlType, LrsUtil.CTLTYPE_價金履約)) {
			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			ELF412C elf412c = misELF412CService.findByPk(meta.getElfBranch(),
					meta.getCustId(), meta.getDupNo());
			if (elf412c != null) {
				meta.setElfDataDt(elf412c.getElf412c_dataDt());
				meta.setElfLLRDate(elf412c.getElf412c_llrDate());
				meta.setElfOCkdLine(Util.trim(elf412c.getElf412c_ockdLine()));
				meta.setElfCState(Util.trim(elf412c.getElf412c_cState()));
				meta.setElfCancelDt(elf412c.getElf412c_cancelDt());
				meta.setElfUpdDate(elf412c.getElf412c_upddate());
				meta.setElfUpdater(elf412c.getElf412c_updater());
				meta.setElfTmeStamp(elf412c.getElf412c_tmestamp());

				if (CrsUtil.isNull_or_ZeroDate(meta.getElfCancelDt())) {
					meta.setElfCancelDt(null);
				}
				if (CrsUtil.isNull_or_ZeroDate(meta.getElfUpdDate())) {
					meta.setElfUpdDate(null);
				}
			}
			// ---
			if (true) {
				bfObj.setMainId(meta.getMainId());
				bfObj.setType("1");
				if (elf412c != null) {
					bfObj.setElfMainCust(Util.trim(elf412c
							.getElf412c_mainCust()));
					bfObj.setElfCrdType(Util.trim(elf412c.getElf412c_crdType()));
					bfObj.setElfCrdTTbl(Util.trim(elf412c.getElf412c_crdtTbl()));
					bfObj.setElfMowType(Util.trim(elf412c.getElf412c_mowType()));
					bfObj.setElfMowTbl1(Util.trim(elf412c.getElf412c_mowTbl1()));
					bfObj.setElfLRDate(elf412c.getElf412c_lrDate());
					bfObj.setElfRCkdLine(Util.trim(elf412c
							.getElf412c_rckdLine()));
					bfObj.setElfUCkdLINE(Util.trim(elf412c
							.getElf412c_uckdLine()));
					bfObj.setElfUCkdDt(elf412c.getElf412c_uckdDt());

					bfObj.setElfNewAdd(Util.trim(elf412c.getElf412c_newAdd()));
					bfObj.setElfNewDate(LrsUtil
							.elf412_rocDateStr_toYYYYMM(elf412c
									.getElf412c_newDate()));
					bfObj.setElfNCkdFlag(Util.trim(elf412c
							.getElf412c_nckdFlag()));
					bfObj.setElfNCkdDate(elf412c.getElf412c_nckdDate());
					bfObj.setElfNCkdMemo(Util.trim(elf412c
							.getElf412c_nckdMemo()));
					bfObj.setElfNextNwDt(elf412c.getElf412c_nextNwDt());

					bfObj.setElfMemo(Util.trim(elf412c.getElf412c_memo()));
					bfObj.setElfFcrdType(Util.trim(elf412c
							.getElf412c_fcrdType()));
					bfObj.setElfFcrdArea(Util.trim(elf412c
							.getElf412c_fcrdArea()));
					bfObj.setElfFcrdPred(Util.trim(elf412c
							.getElf412c_fcrdPred()));
					bfObj.setElfFcrdGrad(Util.trim(elf412c
							.getElf412c_fcrdGrad()));

				}

				LrsUtil.model_zeroDate_to_null(bfObj);
				LrsUtil.model_elf412Str_to_N(bfObj);

			}
		} else {
			ELF412 elf412 = misELF412Service.findByPk(meta.getElfBranch(),
					meta.getCustId(), meta.getDupNo());
			if (elf412 != null) {
				meta.setElfDataDt(elf412.getElf412_dataDt());
				meta.setElfLLRDate(elf412.getElf412_llrDate());
				meta.setElfOCkdLine(Util.trim(elf412.getElf412_ockdLine()));
				meta.setElfCState(Util.trim(elf412.getElf412_cState()));
				meta.setElfCancelDt(elf412.getElf412_cancelDt());
				meta.setElfUpdDate(elf412.getElf412_upddate());
				meta.setElfUpdater(elf412.getElf412_updater());
				meta.setElfTmeStamp(elf412.getElf412_tmestamp());

				if (CrsUtil.isNull_or_ZeroDate(meta.getElfCancelDt())) {
					meta.setElfCancelDt(null);
				}
				if (CrsUtil.isNull_or_ZeroDate(meta.getElfUpdDate())) {
					meta.setElfUpdDate(null);
				}
			}
			// ---
			if (true) {
				bfObj.setMainId(meta.getMainId());
				bfObj.setType("1");
				if (elf412 != null) {
					bfObj.setElfMainCust(Util.trim(elf412.getElf412_mainCust()));
					bfObj.setElfCrdType(Util.trim(elf412.getElf412_crdType()));
					bfObj.setElfCrdTTbl(Util.trim(elf412.getElf412_crdtTbl()));
					bfObj.setElfMowType(Util.trim(elf412.getElf412_mowType()));
					bfObj.setElfMowTbl1(Util.trim(elf412.getElf412_mowTbl1()));
					bfObj.setElfLRDate(elf412.getElf412_lrDate());
					bfObj.setElfRCkdLine(Util.trim(elf412.getElf412_rckdLine()));
					bfObj.setElfUCkdLINE(Util.trim(elf412.getElf412_uckdLine()));
					bfObj.setElfUCkdDt(elf412.getElf412_uckdDt());
					bfObj.setElfMDFlag(CrsUtil.mdFlag_with_leadingZero(Util
							.trim(elf412.getElf412_mdFlag())));
					bfObj.setElfMDDt(elf412.getElf412_mdDt());
					bfObj.setElfProcess(Util.trim(elf412.getElf412_process()));
					bfObj.setElfNewAdd(Util.trim(elf412.getElf412_newAdd()));
					bfObj.setElfNewDate(LrsUtil
							.elf412_rocDateStr_toYYYYMM(elf412
									.getElf412_newDate()));
					bfObj.setElfNCkdFlag(Util.trim(elf412.getElf412_nckdFlag()));
					bfObj.setElfNCkdDate(elf412.getElf412_nckdDate());
					bfObj.setElfNCkdMemo(Util.trim(elf412.getElf412_nckdMemo()));
					bfObj.setElfNextNwDt(elf412.getElf412_nextNwDt());
					bfObj.setElfDBUOBU(Util.trim(elf412.getElf412_dbuObu()));
					bfObj.setElfMemo(Util.trim(elf412.getElf412_memo()));
					bfObj.setElfFcrdType(Util.trim(elf412.getElf412_fcrdType()));
					bfObj.setElfFcrdArea(Util.trim(elf412.getElf412_fcrdArea()));
					bfObj.setElfFcrdPred(Util.trim(elf412.getElf412_fcrdPred()));
					bfObj.setElfFcrdGrad(Util.trim(elf412.getElf412_fcrdGrad()));

					// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
					bfObj.setElfRealCkFg(Util.trim(elf412.getElf412_realCkFg()));
					bfObj.setElfRealDt(elf412.getElf412_realDt());

					// J-108-0078_05097_B1001
					// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
					bfObj.setElfIsAllNew(Util.trim(elf412.getElf412_isAllNew()));
					// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
					bfObj.setElfIsRescue(Util.trim(elf412.getElf412_isRescue()));
					bfObj.setElfGuarFlag(Util.trim(elf412.getElf412_guarFlag()));
					bfObj.setElfNewRescue(Util.trim(elf412
							.getElf412_newRescue()));
					bfObj.setElfNewRescueYM(LrsUtil
							.elf412_rocDateStr_toYYYYMM(elf412
									.getElf412_newRescueYM()));
					// J-110-0272 抽樣覆審
					bfObj.setElfRandomType(Util.trim(elf412.getElf412_randomType()));

				}

				LrsUtil.model_zeroDate_to_null(bfObj);
				LrsUtil.model_elf412Str_to_N(bfObj);

			}
		}

		// ============
		if (true) {
			// 複製
			DataParse.copy(bfObj, afObj);
			afObj.setType("2");
		}
	}

	// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	private void _copyL180M01B_ELF412C(String branch, L180M01A meta,
			L180M01B l180m01b, Map<String, Object> elf412cdata) {
		ELF412C elf412c = new ELF412C();
		DataParse.map2Bean(elf412cdata, elf412c);
		// ------
		String BUSCD = Util.trim(elf412cdata.get("BUSCD"));
		String BUSSKIND = Util.trim(elf412cdata.get("BUSSKIND"));
		String ECONM07A = Util.trim(elf412cdata.get("ECONM07A"));
		String CNAME = LMSUtil
				.getNotEmptyVal_str(elf412cdata, "CNAME", "ENAME");
		String ECONM = Util.trim(elf412cdata.get("ECONM"));
		String SUP3CNM = LMSUtil.getNotEmptyVal_str(elf412cdata, "SUP1CNM",
				"SUP3CNM");

		String rlt_Borrower = misELLNGTEEService.get_lrs_Rlt_Borrower(branch,
				elf412c.getElf412c_custId(), elf412c.getElf412c_dupNo());
		String rlt_Guarantor = misELLNGTEEService.get_lrs_Rlt_Guarantor(branch,
				elf412c.getElf412c_custId(), elf412c.getElf412c_dupNo());

		l180m01b.setMainId(meta.getMainId());
		l180m01b.setCustId(elf412c.getElf412c_custId());
		l180m01b.setDupNo(elf412c.getElf412c_dupNo());
		l180m01b.setTypCd(LrsUtil.isCustId_Z(elf412c.getElf412c_custId()) ? "4"
				: "1");
		if (Util.isNotEmpty(meta.getCreateBy())) {
			l180m01b.setCreateBY(lrsConstants.CREATEBY.系統產生);
		}
		l180m01b.setNewBy170M01("");
		l180m01b.setDocStatus1(lrsConstants.docStatus1.要覆審);
		l180m01b.setProjectSeq(null);
		l180m01b.setProjectNo(null);
		l180m01b.setElfCName(CNAME);
		l180m01b.setECoNm(ECONM);
		l180m01b.setECoNm07A(ECONM07A);
		l180m01b.setBusCd(BUSCD);
		l180m01b.setBussKind(BUSSKIND);
		l180m01b.setSup3CNm(SUP3CNM);
		l180m01b.setRltGuarantor(Util.truncateString(rlt_Guarantor,
				MAXLEN_L180M01B_RLTGUARANTOR));
		l180m01b.setRltBorrower(Util.truncateString(rlt_Borrower,
				MAXLEN_L180M01B_RLTBORROWER));
		l180m01b.setULoan("");
		l180m01b.setNewNCkdFlag("");
		l180m01b.setNewNCkdMemo("");
		l180m01b.setNewNextNwDt(null);
		l180m01b.setNewLRDate(null);
		l180m01b.setElfDataDt(elf412c.getElf412c_dataDt());
		l180m01b.setElfMainCust(Util.trim(elf412c.getElf412c_mainCust()));
		l180m01b.setElfCrdType(Util.trim(elf412c.getElf412c_crdType()));
		l180m01b.setElfCrdTTbl(Util.trim(elf412c.getElf412c_crdtTbl()));
		l180m01b.setElfMowType(Util.trim(elf412c.getElf412c_mowType()));
		l180m01b.setElfMowTbl1(Util.trim(elf412c.getElf412c_mowTbl1()));
		l180m01b.setElfLLRDate(elf412c.getElf412c_llrDate());
		l180m01b.setElfLRDate(elf412c.getElf412c_lrDate());

		if (Util.equals(Util.trim(elf412c.getElf412c_rckdLine()), "")) {
			l180m01b.setElfRCkdLine("A"); // 預設一年一次
		} else {
			l180m01b.setElfRCkdLine(Util.trim(elf412c.getElf412c_rckdLine()));
		}

		l180m01b.setElfOCkdLine(Util.trim(elf412c.getElf412c_ockdLine()));
		l180m01b.setElfUCkdLINE(Util.trim(elf412c.getElf412c_uckdLine()));
		l180m01b.setElfUCkdDt(elf412c.getElf412c_uckdDt());
		l180m01b.setElfCState(Util.trim(elf412c.getElf412c_cState()));
		l180m01b.setElfCancelDt(elf412c.getElf412c_cancelDt());

		l180m01b.setElfNewAdd(Util.trim(elf412c.getElf412c_newAdd()));
		l180m01b.setElfNewDate(LrsUtil.elf412_rocDateStr_toYYYYMM(elf412c
				.getElf412c_newDate()));
		l180m01b.setElfNCkdFlag(Util.trim(elf412c.getElf412c_nckdFlag()));
		l180m01b.setElfNCkdDate(elf412c.getElf412c_nckdDate());
		l180m01b.setElfNCkdMemo(Util.trim(elf412c.getElf412c_nckdMemo()));
		l180m01b.setElfNextNwDt(elf412c.getElf412c_nextNwDt());
		l180m01b.setElfUpdDate(elf412c.getElf412c_upddate());
		l180m01b.setElfUpdater(Util.trim(elf412c.getElf412c_updater()));
		l180m01b.setElfMemo(Util.trim(elf412c.getElf412c_memo()));
		l180m01b.setElfTmeStamp(elf412c.getElf412c_tmestamp());
		l180m01b.setCreator(meta.getCreator());
		l180m01b.setCreateTime(meta.getCreateTime());
		l180m01b.setUpdater(meta.getUpdater());
		l180m01b.setUpdateTime(meta.getUpdateTime());
		l180m01b.setCoMainId(null);
		l180m01b.setElfFcrdType(Util.trim(elf412c.getElf412c_fcrdType()));
		l180m01b.setElfFcrdArea(Util.trim(elf412c.getElf412c_fcrdArea()));
		l180m01b.setElfFcrdPred(Util.trim(elf412c.getElf412c_fcrdPred()));
		l180m01b.setElfFcrdGrad(Util.trim(elf412c.getElf412c_fcrdGrad()));

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		l180m01b.setCtlType(LrsUtil.CTLTYPE_價金履約);

		LrsUtil.model_zeroDate_to_null(l180m01b);
		LrsUtil.model_elf412Str_to_N(l180m01b);
	}

}