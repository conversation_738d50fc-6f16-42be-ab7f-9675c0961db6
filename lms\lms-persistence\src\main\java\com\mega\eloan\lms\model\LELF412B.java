/* 
 * LELF412B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 逾期未覆審名單自辦覆審控制歷史檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "LELF412B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class LELF412B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * mainId
	 * <p/>
	 * ELF412A/B mainId
	 */
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 資料日期
	 * <p/>
	 * ELF412A/B DATADATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "DATADATE", columnDefinition = "DATE")
	private Date dataDate;

	/** 分行代號 **/
	@Size(max = 3)
	@Column(name = "BRANCH", length = 3, columnDefinition = "CHAR(03)")
	private String branch;

	/** 借款人統一編號 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "CHAR(10)")
	private String custId;

	/** 重複序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(01)")
	private String dupNo;

	/**
	 * 主要授信戶
	 * <p/>
	 * 1.新戶增額由ELF411<br/>
	 * 2.舊案由STORE PROCEDURE
	 */
	@Size(max = 1)
	@Column(name = "MAINCUST", length = 1, columnDefinition = "CHAR(01)")
	private String mainCust;

	/**
	 * 資信評等類別
	 * <p/>
	 * B=DBU、大型企業、L=DBU中小型企業、O=OBU
	 */
	@Size(max = 1)
	@Column(name = "CRDTYPE", length = 1, columnDefinition = "CHAR(01)")
	private String crdType;

	/**
	 * 資信評等
	 * <p/>
	 * MIS.CRDTTBL
	 */
	@Size(max = 2)
	@Column(name = "CRDTTBL", length = 2, columnDefinition = "CHAR(02)")
	private String crdTtbl;

	/**
	 * 信用模型評等類別
	 * <p/>
	 * 1:DBU大型企業<br/>
	 * 2:DBU中型企業<br/>
	 * 3:DBU中小型企業<br/>
	 * 4:DBU不動產有建案規劃<br/>
	 * 5:DBU專案融資<br/>
	 * 6:DBU本國證券公司<br/>
	 * 8:DBU投資公司一般情況<br/>
	 * 9:DBU租賃公司<br/>
	 * A:DBU一案建商<br/>
	 * B:DBU非一案建商(擔保/土融)<br/>
	 * C:DBU非一案建商(無擔)<br/>
	 * D:投資公司情況一<br/>
	 * E:投資公司情況二
	 */
	@Size(max = 1)
	@Column(name = "MOWTYPE", length = 1, columnDefinition = "CHAR(01)")
	private String mowType;

	/**
	 * 信用模型評等
	 * <p/>
	 * MIS.MOWTBL1
	 */
	@Size(max = 2)
	@Column(name = "MOWTBL1", length = 2, columnDefinition = "CHAR(02)")
	private String mowTbl1;

	/**
	 * 上上次覆審日
	 * <p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LLRDATE", columnDefinition = "DATE")
	private Date llrDate;

	/**
	 * 上次覆審日
	 * <p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LRDATE", columnDefinition = "DATE")
	private Date lrDate;

	/**
	 * 覆審週期
	 * <p/>
	 * A.一年覆審一次。<br/>
	 * B.半年覆審一次(主要授信戶並符合信評條件)。<br/>
	 * C.新戶/增額戶。<br/>
	 * D.異常戶已三個月覆審過- 爾後半年覆審一次。<br/>
	 * E.首次通報之異常戶。（必需在首次通報日後3月內覆審）<br/>
	 * F.會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。<br/>
	 * G.首次通報有會計師出具保留意見之異常戶。（必需在首次通報日後3月內覆審）<br/>
	 * H.主管機關指定覆審案件。
	 */
	@Size(max = 2)
	@Column(name = "RCKDLINE", length = 2, columnDefinition = "CHAR(02)")
	private String rckdLine;

	/**
	 * 原始週期
	 * <p/>
	 * 如果沒有異常通報、增額、主管機關指定時之週期，只會為A或B
	 */
	@Size(max = 2)
	@Column(name = "OCKDLINE", length = 2, columnDefinition = "CHAR(02)")
	private String ockdLine;

	/**
	 * 戶況
	 * <p/>
	 * 0.無餘額<br/>
	 * 1.正常<br/>
	 * 2.逾期<br/>
	 * 3.催收<br/>
	 * 4.呆帳<br/>
	 * (該戶項下最嚴重的代碼)
	 */
	@Size(max = 1)
	@Column(name = "CSTATE", length = 1, columnDefinition = "CHAR(01)")
	private String cstate;

	/**
	 * 新作/增額
	 * <p/>
	 * N.新做/ C.增貸
	 */
	@Size(max = 1)
	@Column(name = "NEWADD", length = 1, columnDefinition = "CHAR(01)")
	private String newAdd;

	/**
	 * 新作/增額資料日期
	 * <p/>
	 * ELF411_DATAYM 之資料
	 */
	@Size(max = 6)
	@Column(name = "NEWDATE", length = 6, columnDefinition = "CHAR(06)")
	private String newDate;

	/**
	 * 不覆審代碼
	 * <p/>
	 * 1.本行或同業主辦之聯貸案件，非擔任管理行。<br/>
	 * 2.十成定存。<br/>
	 * 3.純進出押戶。<br/>
	 * 4.對政府或政府所屬機關、學校之授信案件。<br/>
	 * 5.拆放同業或對同業之融通。<br/>
	 * 6.已列報為逾期放款或轉列催收款項之案件。<br/>
	 * 7.銷戶<br/>
	 * 8.本次暫不覆審<br/>
	 * 9. 已專案核准免辦理覆審之房屋仲介價金履約保證案件。<br/>
	 * 10. 企業戶之外勞保證中長期授信案件，除於新作後辦理一次覆審外，免再辦理覆審，但嗣後如有增額、減額、變更條件或續約時，
	 * 仍應依本要點第五條規定辦理一次覆審。
	 */
	@Size(max = 2)
	@Column(name = "NCKDFLAG", length = 2, columnDefinition = "CHAR(02)")
	private String nckdFlag;

	/**
	 * 不覆審日期
	 * <p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "NCKDDATE", columnDefinition = "DATE")
	private Date nckdDate;

	/**
	 * 不覆審備註
	 * <p/>
	 * 約100個中文字
	 */
	@Size(max = 300)
	@Column(name = "NCKDMEMO", length = 300, columnDefinition = "VARCHAR(300)")
	private String nckdMemo;

	/**
	 * 銷戶日
	 * <p/>
	 * YYYY-MM-DD<br/>
	 * 全部都沒有額度的日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CANCELDT", columnDefinition = "DATE")
	private Date cancelDt;

	/**
	 * 人工維護日
	 * <p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "UPDDATE", columnDefinition = "DATE")
	private Date updDate;

	/** 人工調整ID **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(06)")
	private String updater;

	/**
	 * 其他備註
	 * <p/>
	 * 約100個中文字
	 */
	@Size(max = 300)
	@Column(name = "MEMO", length = 300, columnDefinition = "VARCHAR(300)")
	private String memo;

	/** 資料更新日 **/
	@Column(name = "TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp tmestamp;

	/** 主管機關指定覆審案件 **/
	@Size(max = 2)
	@Column(name = "UCKDLINE", length = 2, columnDefinition = "CHAR(02)")
	private String uckdLine;

	/** 主管機關通知日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "UCKDDT", columnDefinition = "DATE")
	private Date uckdDt;

	/** 資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATADT", columnDefinition = "DATE")
	private Date dataDt;

	/**
	 * 最新一次下次恢復覆審日
	 * <p/>
	 * 不覆審代碼註記為不覆審時，可設定下次恢復覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "NEXTNWDT", columnDefinition = "DATE")
	private Date nextNwDt;

	/** 上次設定之下次恢復覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "NEXTLTDT", columnDefinition = "DATE")
	private Date nextLtDt;

	/**
	 * 外部評等類別
	 * <p/>
	 * 標準普爾 | 1<br/>
	 * 穆迪信評 | 2<br/>
	 * 惠譽信評 | 3<br/>
	 * 中華信評 | 4
	 */
	@Size(max = 1)
	@Column(name = "FCRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String fcrdType;

	/**
	 * 外部評等地區別
	 * <p/>
	 * 國際 | 1<br/>
	 * 本國 | 2
	 */
	@Size(max = 1)
	@Column(name = "FCRDAREA", length = 1, columnDefinition = "CHAR(1)")
	private String fcrdArea;

	/**
	 * 外部評等期間別
	 * <p/>
	 * 長期 | 1<br/>
	 * 短期 | 2
	 */
	@Size(max = 1)
	@Column(name = "FCRDPRED", length = 1, columnDefinition = "CHAR(1)")
	private String fcrdPred;

	/**
	 * 外部評等等級
	 * <p/>
	 * 【Aaa(tw)|11】整段字
	 */
	@Size(max = 30)
	@Column(name = "FCRDGRAD", length = 30, columnDefinition = "VARCHAR(30)")
	private String fcrdGrad;

	/** 舊簽報書MAINID **/
	@Size(max = 32)
	@Column(name = "OLDRPTID", length = 32, columnDefinition = "CHAR(32)")
	private String oldRptId;

	/** 舊簽報書核准日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "OLDRPTDT", columnDefinition = "DATE")
	private Date oldRptDt;

	/** 舊動審表MAINID **/
	@Size(max = 32)
	@Column(name = "OLDDRAID", length = 32, columnDefinition = "CHAR(32)")
	private String oldDraId;

	/** 舊動審表核准日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "OLDDRADT", columnDefinition = "DATE")
	private Date oldDraDt;

	/**
	 * 新簽報書MAINID
	 * <p/>
	 * NEWADD為N時有值
	 */
	@Size(max = 32)
	@Column(name = "NEWRPTID", length = 32, columnDefinition = "CHAR(32)")
	private String newRptId;

	/**
	 * 新簽報書核准日期
	 * <p/>
	 * NEWADD為N時有值
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "NEWRPTDT", columnDefinition = "DATE")
	private Date newRptDt;

	/**
	 * 新動審表MAINID
	 * <p/>
	 * NEWADD為N時有值
	 */
	@Size(max = 32)
	@Column(name = "NEWDRAID", length = 32, columnDefinition = "CHAR(32)")
	private String newDraId;

	/**
	 * 新動審表核准日期
	 * <p/>
	 * NEWADD為N時有值
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "NEWDRADT", columnDefinition = "DATE")
	private Date newDraDt;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得mainId
	 * <p/>
	 * ELF412A/B mainId
	 */
	public String getMainId() {
		return this.mainId;
	}

	/**
	 * 設定mainId
	 * <p/>
	 * ELF412A/B mainId
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得資料日期
	 * <p/>
	 * ELF412A/B DATADATE
	 */
	public Date getDataDate() {
		return this.dataDate;
	}

	/**
	 * 設定資料日期
	 * <p/>
	 * ELF412A/B DATADATE
	 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 取得分行代號 **/
	public String getBranch() {
		return this.branch;
	}

	/** 設定分行代號 **/
	public void setBranch(String value) {
		this.branch = value;
	}

	/** 取得借款人統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定借款人統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重複序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重複序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得主要授信戶
	 * <p/>
	 * 1.新戶增額由ELF411<br/>
	 * 2.舊案由STORE PROCEDURE
	 */
	public String getMainCust() {
		return this.mainCust;
	}

	/**
	 * 設定主要授信戶
	 * <p/>
	 * 1.新戶增額由ELF411<br/>
	 * 2.舊案由STORE PROCEDURE
	 **/
	public void setMainCust(String value) {
		this.mainCust = value;
	}

	/**
	 * 取得資信評等類別
	 * <p/>
	 * B=DBU、大型企業、L=DBU中小型企業、O=OBU
	 */
	public String getCrdType() {
		return this.crdType;
	}

	/**
	 * 設定資信評等類別
	 * <p/>
	 * B=DBU、大型企業、L=DBU中小型企業、O=OBU
	 **/
	public void setCrdType(String value) {
		this.crdType = value;
	}

	/**
	 * 取得資信評等
	 * <p/>
	 * MIS.CRDTTBL
	 */
	public String getCrdTtbl() {
		return this.crdTtbl;
	}

	/**
	 * 設定資信評等
	 * <p/>
	 * MIS.CRDTTBL
	 **/
	public void setCrdTtbl(String value) {
		this.crdTtbl = value;
	}

	/**
	 * 取得信用模型評等類別
	 * <p/>
	 * 1:DBU大型企業<br/>
	 * 2:DBU中型企業<br/>
	 * 3:DBU中小型企業<br/>
	 * 4:DBU不動產有建案規劃<br/>
	 * 5:DBU專案融資<br/>
	 * 6:DBU本國證券公司<br/>
	 * 8:DBU投資公司一般情況<br/>
	 * 9:DBU租賃公司<br/>
	 * A:DBU一案建商<br/>
	 * B:DBU非一案建商(擔保/土融)<br/>
	 * C:DBU非一案建商(無擔)<br/>
	 * D:投資公司情況一<br/>
	 * E:投資公司情況二
	 */
	public String getMowType() {
		return this.mowType;
	}

	/**
	 * 設定信用模型評等類別
	 * <p/>
	 * 1:DBU大型企業<br/>
	 * 2:DBU中型企業<br/>
	 * 3:DBU中小型企業<br/>
	 * 4:DBU不動產有建案規劃<br/>
	 * 5:DBU專案融資<br/>
	 * 6:DBU本國證券公司<br/>
	 * 8:DBU投資公司一般情況<br/>
	 * 9:DBU租賃公司<br/>
	 * A:DBU一案建商<br/>
	 * B:DBU非一案建商(擔保/土融)<br/>
	 * C:DBU非一案建商(無擔)<br/>
	 * D:投資公司情況一<br/>
	 * E:投資公司情況二
	 **/
	public void setMowType(String value) {
		this.mowType = value;
	}

	/**
	 * 取得信用模型評等
	 * <p/>
	 * MIS.MOWTBL1
	 */
	public String getMowTbl1() {
		return this.mowTbl1;
	}

	/**
	 * 設定信用模型評等
	 * <p/>
	 * MIS.MOWTBL1
	 **/
	public void setMowTbl1(String value) {
		this.mowTbl1 = value;
	}

	/**
	 * 取得上上次覆審日
	 * <p/>
	 * YYYY-MM-DD
	 */
	public Date getLlrDate() {
		return this.llrDate;
	}

	/**
	 * 設定上上次覆審日
	 * <p/>
	 * YYYY-MM-DD
	 **/
	public void setLlrDate(Date value) {
		this.llrDate = value;
	}

	/**
	 * 取得上次覆審日
	 * <p/>
	 * YYYY-MM-DD
	 */
	public Date getLrDate() {
		return this.lrDate;
	}

	/**
	 * 設定上次覆審日
	 * <p/>
	 * YYYY-MM-DD
	 **/
	public void setLrDate(Date value) {
		this.lrDate = value;
	}

	/**
	 * 取得覆審週期
	 * <p/>
	 * A.一年覆審一次。<br/>
	 * B.半年覆審一次(主要授信戶並符合信評條件)。<br/>
	 * C.新戶/增額戶。<br/>
	 * D.異常戶已三個月覆審過- 爾後半年覆審一次。<br/>
	 * E.首次通報之異常戶。（必需在首次通報日後3月內覆審）<br/>
	 * F.會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。<br/>
	 * G.首次通報有會計師出具保留意見之異常戶。（必需在首次通報日後3月內覆審）<br/>
	 * H.主管機關指定覆審案件。
	 */
	public String getRckdLine() {
		return this.rckdLine;
	}

	/**
	 * 設定覆審週期
	 * <p/>
	 * A.一年覆審一次。<br/>
	 * B.半年覆審一次(主要授信戶並符合信評條件)。<br/>
	 * C.新戶/增額戶。<br/>
	 * D.異常戶已三個月覆審過- 爾後半年覆審一次。<br/>
	 * E.首次通報之異常戶。（必需在首次通報日後3月內覆審）<br/>
	 * F.會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。<br/>
	 * G.首次通報有會計師出具保留意見之異常戶。（必需在首次通報日後3月內覆審）<br/>
	 * H.主管機關指定覆審案件。
	 **/
	public void setRckdLine(String value) {
		this.rckdLine = value;
	}

	/**
	 * 取得原始週期
	 * <p/>
	 * 如果沒有異常通報、增額、主管機關指定時之週期，只會為A或B
	 */
	public String getOckdLine() {
		return this.ockdLine;
	}

	/**
	 * 設定原始週期
	 * <p/>
	 * 如果沒有異常通報、增額、主管機關指定時之週期，只會為A或B
	 **/
	public void setOckdLine(String value) {
		this.ockdLine = value;
	}

	/**
	 * 取得戶況
	 * <p/>
	 * 0.無餘額<br/>
	 * 1.正常<br/>
	 * 2.逾期<br/>
	 * 3.催收<br/>
	 * 4.呆帳<br/>
	 * (該戶項下最嚴重的代碼)
	 */
	public String getCstate() {
		return this.cstate;
	}

	/**
	 * 設定戶況
	 * <p/>
	 * 0.無餘額<br/>
	 * 1.正常<br/>
	 * 2.逾期<br/>
	 * 3.催收<br/>
	 * 4.呆帳<br/>
	 * (該戶項下最嚴重的代碼)
	 **/
	public void setCstate(String value) {
		this.cstate = value;
	}

	/**
	 * 取得新作/增額
	 * <p/>
	 * N.新做/ C.增貸
	 */
	public String getNewAdd() {
		return this.newAdd;
	}

	/**
	 * 設定新作/增額
	 * <p/>
	 * N.新做/ C.增貸
	 **/
	public void setNewAdd(String value) {
		this.newAdd = value;
	}

	/**
	 * 取得新作/增額資料日期
	 * <p/>
	 * ELF411_DATAYM 之資料
	 */
	public String getNewDate() {
		return this.newDate;
	}

	/**
	 * 設定新作/增額資料日期
	 * <p/>
	 * ELF411_DATAYM 之資料
	 **/
	public void setNewDate(String value) {
		this.newDate = value;
	}

	/**
	 * 取得不覆審代碼
	 * <p/>
	 * 1.本行或同業主辦之聯貸案件，非擔任管理行。<br/>
	 * 2.十成定存。<br/>
	 * 3.純進出押戶。<br/>
	 * 4.對政府或政府所屬機關、學校之授信案件。<br/>
	 * 5.拆放同業或對同業之融通。<br/>
	 * 6.已列報為逾期放款或轉列催收款項之案件。<br/>
	 * 7.銷戶<br/>
	 * 8.本次暫不覆審<br/>
	 * 9. 已專案核准免辦理覆審之房屋仲介價金履約保證案件。<br/>
	 * 10. 企業戶之外勞保證中長期授信案件，除於新作後辦理一次覆審外，免再辦理覆審，但嗣後如有增額、減額、變更條件或續約時，
	 * 仍應依本要點第五條規定辦理一次覆審。
	 */
	public String getNckdFlag() {
		return this.nckdFlag;
	}

	/**
	 * 設定不覆審代碼
	 * <p/>
	 * 1.本行或同業主辦之聯貸案件，非擔任管理行。<br/>
	 * 2.十成定存。<br/>
	 * 3.純進出押戶。<br/>
	 * 4.對政府或政府所屬機關、學校之授信案件。<br/>
	 * 5.拆放同業或對同業之融通。<br/>
	 * 6.已列報為逾期放款或轉列催收款項之案件。<br/>
	 * 7.銷戶<br/>
	 * 8.本次暫不覆審<br/>
	 * 9. 已專案核准免辦理覆審之房屋仲介價金履約保證案件。<br/>
	 * 10. 企業戶之外勞保證中長期授信案件，除於新作後辦理一次覆審外，免再辦理覆審，但嗣後如有增額、減額、變更條件或續約時，
	 * 仍應依本要點第五條規定辦理一次覆審。
	 **/
	public void setNckdFlag(String value) {
		this.nckdFlag = value;
	}

	/**
	 * 取得不覆審日期
	 * <p/>
	 * YYYY-MM-DD
	 */
	public Date getNckdDate() {
		return this.nckdDate;
	}

	/**
	 * 設定不覆審日期
	 * <p/>
	 * YYYY-MM-DD
	 **/
	public void setNckdDate(Date value) {
		this.nckdDate = value;
	}

	/**
	 * 取得不覆審備註
	 * <p/>
	 * 約100個中文字
	 */
	public String getNckdMemo() {
		return this.nckdMemo;
	}

	/**
	 * 設定不覆審備註
	 * <p/>
	 * 約100個中文字
	 **/
	public void setNckdMemo(String value) {
		this.nckdMemo = value;
	}

	/**
	 * 取得銷戶日
	 * <p/>
	 * YYYY-MM-DD<br/>
	 * 全部都沒有額度的日期
	 */
	public Date getCancelDt() {
		return this.cancelDt;
	}

	/**
	 * 設定銷戶日
	 * <p/>
	 * YYYY-MM-DD<br/>
	 * 全部都沒有額度的日期
	 **/
	public void setCancelDt(Date value) {
		this.cancelDt = value;
	}

	/**
	 * 取得人工維護日
	 * <p/>
	 * YYYY-MM-DD
	 */
	public Date getUpdDate() {
		return this.updDate;
	}

	/**
	 * 設定人工維護日
	 * <p/>
	 * YYYY-MM-DD
	 **/
	public void setUpdDate(Date value) {
		this.updDate = value;
	}

	/** 取得人工調整ID **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定人工調整ID **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/**
	 * 取得其他備註
	 * <p/>
	 * 約100個中文字
	 */
	public String getMemo() {
		return this.memo;
	}

	/**
	 * 設定其他備註
	 * <p/>
	 * 約100個中文字
	 **/
	public void setMemo(String value) {
		this.memo = value;
	}

	/** 取得資料更新日 **/
	public Timestamp getTmestamp() {
		return this.tmestamp;
	}

	/** 設定資料更新日 **/
	public void setTmestamp(Timestamp value) {
		this.tmestamp = value;
	}

	/** 取得主管機關指定覆審案件 **/
	public String getUckdLine() {
		return this.uckdLine;
	}

	/** 設定主管機關指定覆審案件 **/
	public void setUckdLine(String value) {
		this.uckdLine = value;
	}

	/** 取得主管機關通知日期 **/
	public Date getUckdDt() {
		return this.uckdDt;
	}

	/** 設定主管機關通知日期 **/
	public void setUckdDt(Date value) {
		this.uckdDt = value;
	}

	/** 取得資料日期 **/
	public Date getDataDt() {
		return this.dataDt;
	}

	/** 設定資料日期 **/
	public void setDataDt(Date value) {
		this.dataDt = value;
	}

	/**
	 * 取得最新一次下次恢復覆審日
	 * <p/>
	 * 不覆審代碼註記為不覆審時，可設定下次恢復覆審日
	 */
	public Date getNextNwDt() {
		return this.nextNwDt;
	}

	/**
	 * 設定最新一次下次恢復覆審日
	 * <p/>
	 * 不覆審代碼註記為不覆審時，可設定下次恢復覆審日
	 **/
	public void setNextNwDt(Date value) {
		this.nextNwDt = value;
	}

	/** 取得上次設定之下次恢復覆審日 **/
	public Date getNextLtDt() {
		return this.nextLtDt;
	}

	/** 設定上次設定之下次恢復覆審日 **/
	public void setNextLtDt(Date value) {
		this.nextLtDt = value;
	}

	/**
	 * 取得外部評等類別
	 * <p/>
	 * 標準普爾 | 1<br/>
	 * 穆迪信評 | 2<br/>
	 * 惠譽信評 | 3<br/>
	 * 中華信評 | 4
	 */
	public String getFcrdType() {
		return this.fcrdType;
	}

	/**
	 * 設定外部評等類別
	 * <p/>
	 * 標準普爾 | 1<br/>
	 * 穆迪信評 | 2<br/>
	 * 惠譽信評 | 3<br/>
	 * 中華信評 | 4
	 **/
	public void setFcrdType(String value) {
		this.fcrdType = value;
	}

	/**
	 * 取得外部評等地區別
	 * <p/>
	 * 國際 | 1<br/>
	 * 本國 | 2
	 */
	public String getFcrdArea() {
		return this.fcrdArea;
	}

	/**
	 * 設定外部評等地區別
	 * <p/>
	 * 國際 | 1<br/>
	 * 本國 | 2
	 **/
	public void setFcrdArea(String value) {
		this.fcrdArea = value;
	}

	/**
	 * 取得外部評等期間別
	 * <p/>
	 * 長期 | 1<br/>
	 * 短期 | 2
	 */
	public String getFcrdPred() {
		return this.fcrdPred;
	}

	/**
	 * 設定外部評等期間別
	 * <p/>
	 * 長期 | 1<br/>
	 * 短期 | 2
	 **/
	public void setFcrdPred(String value) {
		this.fcrdPred = value;
	}

	/**
	 * 取得外部評等等級
	 * <p/>
	 * 【Aaa(tw)|11】整段字
	 */
	public String getFcrdGrad() {
		return this.fcrdGrad;
	}

	/**
	 * 設定外部評等等級
	 * <p/>
	 * 【Aaa(tw)|11】整段字
	 **/
	public void setFcrdGrad(String value) {
		this.fcrdGrad = value;
	}

	/** 取得舊簽報書MAINID **/
	public String getOldRptId() {
		return this.oldRptId;
	}

	/** 設定舊簽報書MAINID **/
	public void setOldRptId(String value) {
		this.oldRptId = value;
	}

	/** 取得舊簽報書核准日期 **/
	public Date getOldRptDt() {
		return this.oldRptDt;
	}

	/** 設定舊簽報書核准日期 **/
	public void setOldRptDt(Date value) {
		this.oldRptDt = value;
	}

	/** 取得舊動審表MAINID **/
	public String getOldDraId() {
		return this.oldDraId;
	}

	/** 設定舊動審表MAINID **/
	public void setOldDraId(String value) {
		this.oldDraId = value;
	}

	/** 取得舊動審表核准日期 **/
	public Date getOldDraDt() {
		return this.oldDraDt;
	}

	/** 設定舊動審表核准日期 **/
	public void setOldDraDt(Date value) {
		this.oldDraDt = value;
	}

	/**
	 * 取得新簽報書MAINID
	 * <p/>
	 * NEWADD為N時有值
	 */
	public String getNewRptId() {
		return this.newRptId;
	}

	/**
	 * 設定新簽報書MAINID
	 * <p/>
	 * NEWADD為N時有值
	 **/
	public void setNewRptId(String value) {
		this.newRptId = value;
	}

	/**
	 * 取得新簽報書核准日期
	 * <p/>
	 * NEWADD為N時有值
	 */
	public Date getNewRptDt() {
		return this.newRptDt;
	}

	/**
	 * 設定新簽報書核准日期
	 * <p/>
	 * NEWADD為N時有值
	 **/
	public void setNewRptDt(Date value) {
		this.newRptDt = value;
	}

	/**
	 * 取得新動審表MAINID
	 * <p/>
	 * NEWADD為N時有值
	 */
	public String getNewDraId() {
		return this.newDraId;
	}

	/**
	 * 設定新動審表MAINID
	 * <p/>
	 * NEWADD為N時有值
	 **/
	public void setNewDraId(String value) {
		this.newDraId = value;
	}

	/**
	 * 取得新動審表核准日期
	 * <p/>
	 * NEWADD為N時有值
	 */
	public Date getNewDraDt() {
		return this.newDraDt;
	}

	/**
	 * 設定新動審表核准日期
	 * <p/>
	 * NEWADD為N時有值
	 **/
	public void setNewDraDt(Date value) {
		this.newDraDt = value;
	}
}
