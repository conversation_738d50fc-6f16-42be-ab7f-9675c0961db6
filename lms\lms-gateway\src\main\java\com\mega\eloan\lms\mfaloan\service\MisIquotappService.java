/* 
 *MisIquotappService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.IQUOTAPP;

/**
 * <pre>
 * 核准額度資料檔 IQUOTAPP(MIS.ELV38801)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
public interface MisIquotappService {
	/**
	 * 根據額度序號查詢查詢
	 * 
	 * @param cntrNos
	 *            所有的額度序號
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> findByCntrNo(String... cntrNos);

	/**
	 * 新增
	 * 
	 * @param quotaNo
	 *            額度序號
	 * @param custId
	 *            CUSTID
	 * @param dupNo
	 *            重複序號
	 * @param icbcNo
	 *            授信經辦行員代號
	 * @param cName
	 *            授信經辦姓名
	 * @param omgrName
	 *            初放主管姓名
	 * @param fmgrName
	 *            敘作主管姓名
	 * @param approLvl
	 *            授權等級
	 * @param updater
	 *            資料修改人
	 */
	public void insert(String quotaNo, String custId, String dupNo,
			String icbcNo, String cName, String omgrName, String fmgrName,
			String approLvl, String updater);

	/**
	 * 動審表 -更新 授信額度檔(MIS.IQUOTAPP)
	 * 
	 * @param quotaNo
	 *            額度序號
	 * @param custId
	 *            CUSTID
	 * @param dupNo
	 *            重複序號
	 * @param icbcNo
	 *            授信經辦行員代號
	 * @param cName
	 *            授信經辦姓名
	 * @param fmgrName
	 *            敘作主管姓名
	 * @param approLvl
	 *            授權等級
	 * @param updater
	 *            資料修改人
	 */
	public void update(String quotaNo, String custId, String dupNo,
			String icbcNo, String cName, String fmgrName, String approLvl,
			String updater);

	/**
	 * 動審表 -查詢 授信額度檔(MIS.IQUOTAPP)
	 * 
	 * @param QUOTANO
	 * 			  額度序號
	 */
	public IQUOTAPP findIQUOTAPPByUKey(String QUOTANO);

}
