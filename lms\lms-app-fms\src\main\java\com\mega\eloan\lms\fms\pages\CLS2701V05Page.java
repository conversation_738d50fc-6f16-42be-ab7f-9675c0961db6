package com.mega.eloan.lms.fms.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.common.Util;

@Controller
@RequestMapping(path = "/fms/cls2701v05")
public class CLS2701V05Page extends AbstractEloanInnerView {

	public CLS2701V05Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.已確認);
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		//---
		List<EloanPageFragment> list = new ArrayList<>();
		list.add(LmsButtonEnum.Add);
		if(Util.equals(user.getSsoUnitNo(), "900")){
			list.add(LmsButtonEnum.Delete);
		}		
		addToButtonPanel(model, list);
		
		renderJsI18N(CLS2701V01Page.class);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/CLS2701V05Page.js" };
	}
}
