var pageAction = {
	handler : 'lms7120formhandler',
	grid : null,
	build : function(){
		pageAction.grid = $("#gridview").iGrid({
			//localFirst: true,
			handler : 'lms7120gridhandler',
			action :  "queryL712m01a",
	        width : 785,
			height: 350,
	        sortname: 'caseDate|caseNo',
	        sortorder: 'desc|asc',
	        shrinkToFit: true,
	        autowidth: false,       
	         postData: {
	            mainDocStatus: viewstatus,
	            rowNum: 15
	        },
       		rowNum: 15,
			//multiselect : true,
			colModel : [{
				colHeader : "oid",
				name : 'oid',
				hidden : true //是否隱藏
			},{
				colHeader : "mainId",
				hidden : true, //是否隱藏
				name : 'mainId'
			},{
				colHeader : "docURL",
				hidden : true, //是否隱藏
				name : 'docURL'
			},{
				colHeader : i18n.lms7120v03["mainGrid.index1"], //分行代號
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'caseBrId'
			},{
				colHeader : i18n.lms7120v03["mainGrid.index2"], //統一編號
	            align: "left",
	            width: 80,
	            sortable: true,
	            formatter: 'click',
	            onclick: pageAction.openBox,
	            name: 'custId'
			},{
				colHeader : i18n.lms7120v03["mainGrid.index3"], //經辦人員
	            align: "left",
	            width: 70,
	            sortable: true,
	            name: 'stopUpdater'
			},{
				colHeader : i18n.lms7120v03["mainGrid.index4"], //查詢日期
	            align: "center",
	            width: 70,
	            sortable: true,
	            name: 'caseDate'
			},{
				colHeader : "額度序號",//i18n.lms7120v03["mainGrid.index5"], //額度序號
	            align: "left",
	            width: 90,
	            sortable: true,
	            name: 'cntrNo'	
			},{
				colHeader : i18n.lms7120v03["mainGrid.index5"], //案件號碼
	            align: "left",
	            width: 170,
	            sortable: true,
	            name: 'caseNo'
			},{
				colHeader : i18n.lms7120v03["mainGrid.index6"], //覆核人員
	            align: "left",
	            width: 70,
	            sortable: true,				
				name : 'stopApprover'
			},{
				colHeader : i18n.lms7120v03["mainGrid.index7"], //覆核日期
	            align: "left",
	            width: 70,
	            sortable: true,				
				name : 'stopApprTime'
			}
		],
			ondblClickRow: function(rowid){//同修改
				var data = pageAction.grid.getRowData(rowid);
				pageAction.openBox(null, null, data);
			}				
		});
		$("#buttonPanel").find("#btnView").click(function(){
			//調閱	    
	        var row = pageAction.grid.getGridParam('selrow');
	        if (!row) {	        
	            // action_004=請先選擇需「調閱」之資料列
	            return CommonAPI.showMessage(i18n.def["action_004"]);	            
	        }else{
				var result = $("#gridview").getRowData(row);
				pageAction.openBox(null, null, result);
			}	        
	    });	
	},	
	/**
	 * 開啟視窗
	 */
	openBox : function(cellvalue, options, rowObject){
		ilog.debug(rowObject);
	    var url = '..' + rowObject.docURL;
		$.form.submit({
	        url: url,
	        data: {
	            mainDocStatus: viewstatus,
	            mainId: rowObject.mainId,
	            mainOid: rowObject.oid,
	            docURL: rowObject.docURL,
	            oid: rowObject.oid
	        },
	        target: "_blank"
	    });
	},		
	/**
	 * 取得資料表之選擇列
	 */
	getRowData : function(){
		var row = pageAction.grid.getGridParam('selrow');
		var data;
		if (row) {
			data = pageAction.grid.getRowData(row);
		}else{
			MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["grid.selrow"]);
		}
		return data;
	},
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				posinputata : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	}
}

$(function() {
	pageAction.build();	
});