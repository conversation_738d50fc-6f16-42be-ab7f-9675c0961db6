<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
    	<th:block th:fragment="CLS1220S06Panel01">
			<!-- 結案 -->	
			 <fieldset>
				<div id="CaseClosedDiv" class="content" style="display:none">
					<table width="100%" class="tb2">
						<tr>
							<td class=''>
								<div id="CaseClosedTypeG" style="width: 80px; display:inline;">
									<input type='radio' id='CaseClosedType' name='CaseClosedType' value='G' checked="true">
										<th:block th:text="#{'button.radio.reject'}">拒絕</th:block>
									</input>
								</div>	
								<div id="CaseClosedTypeI" style="width: 80px; display:inline;">
									<input type='radio' id='CaseClosedType' name='CaseClosedType' value='I'>
										<th:block th:text="#{'button.radio.cancel'}">取消</th:block>
									</input>
								</div>
								<div id="CaseClosedTypeE" style="width: 80px; display:inline;">
									<input type='radio' id='CaseClosedType' name='CaseClosedType' value='E'>
										<th:block th:text="#{'button.radio.E02'}">已對保</th:block>
									</input>
								</div>
							</td>
						</tr>
						<tr id="ItemI" style="display:none">
							<td ><select id="CaseClosedItemI" name="CaseClosedItemI" class="required" combokey="c122m01a_docstatus_I" style="width:100%" itemStyle="format:{value} - {key}" ></select>
							</td>
						</tr>
						
						<tr id="ItemG">
							<td class=''>
								<div>
									<select id="CaseClosedItemG" name="CaseClosedItemG" combokey="c122m01a_docstatus_G" style="width:100%" itemStyle="format:{value} - {key}" class="required" ></select>
								</div>
								<div id="otherReasonDiv" style="display:none">
									<th:block th:text="#{'C122S01G.otherReason'}"><!--其他不承作理由：--></th:block>  	
									<input type="text" id="otherReason" name="otherReason">
								</div>	
								<div>
									<button type="button" id="addReasonG">
										<span class="text-only"><th:block th:text="#{'button.add'}">新增</th:block></span>
									</button>
									<button type="button" id="deleteReasonG">
										<span class="text-only"><th:block th:text="#{'button.delete2'}">刪除</th:block></span>
									</button>
								</div>
								<div id="ItemGGrid" ></div>
							</td>
						</tr>
					</table>
				</div>
			</fieldset>
			
			<!-- 產生個金徵信資料 -->
			<fieldset>
				<div id="OpenCustInfo" style="display:none;" >
					<div>
						<span class="color-red">
							<th:block th:text="#{'Message.sendCheck.07'}"><!-- 同步個金徵信資料，請注意[重複序號]是否正確，避免資料同步錯誤 --></th:block>
						</span>
					</div>
					<div>
						<button id="btnMakeCust">
							<span class="ui-icon ui-icon-jcs-106"></span>
							<th:block th:text="#{'button.makeCust'}">產生個金徵徵信資料</th:block>
						</button>
					</div>
					<div id="queryCustIdDiv">
						<input type="text" id="queryCustId" name="queryCustId" size="13" maxlength="10" class="required alphanum"/>
						<button type="button" id="resetOpenCustInfoDiv" >
                			<span class="text-only"><th:block th:text="#{'button.query'}">查詢</th:block></span>
            			</button>
					</div>
				<div id="OpenCustInfoDiv" ></div>
				</div>
			</fieldset>
			<div id="ClsCustInfo" ></div>
			
			
			<!-- 變更原始申貸分行 -->
			<div id="changeOrgBrIdDiv" class="content" style="display:none">
				<form id="changeOrgBrIdForm">
					<table width="100%" class="tb2">
						<tr>
							<td class="hd2" nowrap><th:block th:text="#{'C122M01A.orgBrId'}"><!--原始申貸分行--></th:block> 
							</td>
							<td colspan="3">
								<div id="BrIdDiv">
									<select id="chgOrgBrId" name="chgOrgBrId" class="boss"></select> 
								</div>
							</td>
						</tr>
					</table>
				</form>
			</div>
			
			<!-- 30日內同IP來源明細 -->
			<fieldset>
				<div id="SameIpInfoIn30DayDiv" style="display:none;" >
					<div id="SameIpInfoIn30Day" ></div>
				</div>
			</fieldset>
			
		</th:block>
	</body>
</html>
