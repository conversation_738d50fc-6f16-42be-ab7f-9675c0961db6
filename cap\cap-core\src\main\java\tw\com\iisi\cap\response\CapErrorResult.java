/*
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
*/
package tw.com.iisi.cap.response;

import java.util.Locale;

import javax.servlet.ServletResponse;

import org.kordamp.json.JSONObject;
import org.springframework.context.i18n.LocaleContextHolder;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.StringResponse;
import com.iisigroup.cap.utils.CapAppContext;

import tw.com.iisi.cap.exception.CapClosePageException;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * 錯誤訊息回應
 * </pre>
 * 
 * @since 2010/11/24
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>iristu,2010/11/24,new
 *          <li>RodesChen,2011/6/2,增加關閉畫面錯誤
 *          </ul>
 */
public class CapErrorResult implements IErrorResult {

    /**
     * {@value #AJAX_HANDLER_EXCEPTION}
     */
    public static final String AJAX_HANDLER_EXCEPTION = "AJAX_HANDLER_EXCEPTION";
    /**
     * {@value #AJAX_MESSAGE_HANDLER_EXCEPTION}
     */
    public static final String AJAX_MESSAGE_HANDLER_EXCEPTION = "AJAX_MESSAGE_HANDLER_EXCEPTION";
    /**
     * <pre>
     * 關閉畫面錯誤類別
     * {@value #AJAX_CLOSE_PAGE_HANDLER_EXCEPTION}
     * </pre>
     * 
     */
    public static final String AJAX_CLOSE_PAGE_HANDLER_EXCEPTION = "AJAX_CLOSE_PAGE_HANDLER_EXCEPTION";

    /**
     * 錯誤訊息物件
     */
    JSONObject errorMessage = new JSONObject();

    /**
     * Log 訊息
     */
    String logMessage = "";

    /**
     * 建構子
     */
    public CapErrorResult() {
    }

    /**
     * {@linkplain tw.com.iisi.cap.response.CapErrorResult#putError(PageParameters, Exception) putError}(request, e)
     * 
     * @param request
     *            前端請求
     * @param e
     *            例外
     */
    public CapErrorResult(PageParameters request, Exception e) {
        this.putError(request, e);
    }

    /**
     * 取得錯誤訊息JSON字串
     * 
     * @return {@code errorMessage.toString()}
     */
    @Override
    public String getResult() {
        return errorMessage.toString();
    }

    /*
     * 取得Log訊息
     */
    @Override
    public String getLogMessage() {
        return logMessage;
    }

    /*
     * 錯誤訊息設置
     * 
     * @see tw.com.iisi.cap.response.IErrorResult#putError(com.iisigroup.cap.component.PageParameters, java.lang.Exception)
     */
    @Override
    public void putError(PageParameters request, Exception e) {
        if (e instanceof CapMessageException) {
            CapMessageException ce = (CapMessageException) e;
            logMessage = ce.getMessage();
            if (!CapString.isEmpty(ce.getMessageKey())) {
                logMessage = ce.getMessageKey();
            }
            logMessage = formatMessage(request, logMessage, ce.getExtraInformation());
            errorMessage.put(AJAX_MESSAGE_HANDLER_EXCEPTION, "[" + CapDate.getCurrentTimestamp() + "] " + logMessage);
        } else if (e instanceof CapClosePageException) {
            CapClosePageException ce = (CapClosePageException) e;
            logMessage = ce.getMessage();
            if (!CapString.isEmpty(ce.getMessageKey())) {
                logMessage = ce.getMessageKey();
            }
            logMessage = formatMessage(request, logMessage, ce.getExtraInformation());
            errorMessage.put(AJAX_CLOSE_PAGE_HANDLER_EXCEPTION, "[" + CapDate.getCurrentTimestamp() + "] " + logMessage);
        } else if (e instanceof CapException) {
            CapException ce = (CapException) e;
            logMessage = new StringBuffer(ce.getCauseClass().getName()).append(":").append(e.getMessage()).toString();
            errorMessage.put(AJAX_HANDLER_EXCEPTION, logMessage);
        } else {
            logMessage = e.getLocalizedMessage();
            errorMessage.put(AJAX_HANDLER_EXCEPTION, logMessage);
        }
    }// ;

    /*
     * 加入錯誤訊息
     * 
     * @see tw.com.iisi.cap.response.IResult#add(tw.com.iisi.cap.response.IResult)
     */
    @Override
    public void add(IResult result) {
        JSONObject json = JSONObject.fromObject(result);
        this.errorMessage.putAll(json);
        this.logMessage = result.getLogMessage();
    }

    /*
     * 返回結果字串
     * 
     * @see tw.com.iisi.cap.response.IResult#respondResult(javax.servlet.ServletResponse)
     */
    @Override
    public void respondResult(ServletResponse response) throws CapException {
        new StringResponse("text/plain", "utf-8", getResult()).respond(response);
    }

    /**
     * 格式化訊息
     * 
     * @param request
     *            前端請求
     * @param msgKey
     *            MessageKey
     * @param extraInfo
     *            額外資訊
     */
    protected String formatMessage(PageParameters request, String msgKey, Object extraInfo) {
        Locale locale = (Locale) LocaleContextHolder.getLocale();
        if (extraInfo != null) {
            return CapAppContext.getMessage(msgKey, (Object[]) extraInfo, locale);
        } else {
            return CapAppContext.getMessage(msgKey, locale);
        }

    }

}
