$(document).ready(function(){
	var grid = $("#gridview").iGrid({
        height: "400",sortname:'creDate',sortorder:'desc',handler: "lms9550v01gridhandler",action: 'queryView',localFirst:true,
        postData: {},
        colModel: [
        { colHeader: i18n.lms9550r01['grid.qryDate'], //"資料查詢日"
            name: 'qryDate', align: "center", width: 4, formatter: 'click', onclick: openDoc }
        , {colHeader: i18n.lms9550r01['grid.creDate'], //"資料產生日"
            name: 'creDate', align: "center", width: 8 }
		, { colHeader: i18n.lms9550r01['grid.sendMsg'], //"最後傳送檔案結果"
            name: 'exeMsg', align: "center", width: 10}
		, {colHeader: i18n.lms9550r01['grid.A01'], name: 'dataCntA01', align: "center", width: 10, sortable:false}
//		, {colHeader: i18n.lms9550r01['grid.Z93'], name: 'dataCntZ93', align: "center", width: 2, sortable:false}
//		, {colHeader: i18n.lms9550r01['grid.Z96'], name: 'dataCntZ96', align: "center", width: 2, sortable:false}
//		, {colHeader: i18n.lms9550r01['grid.98F'], name: 'dataCnt98F', align: "center", width: 2, sortable:false}
//		, {colHeader: i18n.lms9550r01['grid.98E'], name: 'dataCnt98E', align: "center", width: 2, sortable:false}
//		, {colHeader: i18n.lms9550r01['grid.Z78'], name: 'dataCntZ78', align: "center", width: 2, sortable:false}
//		, {colHeader: i18n.lms9550r01['grid.Z63'], name: 'dataCntZ63', align: "center", width: 2, sortable:false}
		, { name: 'oid', hidden: true }, { name: 'mainId', hidden: true }, { name: 'uid', hidden: true }]
    });
    
    function openDoc(cellvalue, options, ret){
    	$.form.submit({
            url: '../rpt/lms9550r01',target: ret.oid,
            data: {
                formAction: "query",fromView: true,
                mainOid: ret.oid,mainId: ret.mainId,
                mainDocStatus: ret.docStatus,uid: ret.uid,noOpenDoc:true, branchVar: branchVar
            }
        });
    };
    $("#buttonPanel").find("#btnCreFile").click(function(){
    	$.ajax({
             handler: "lms9550r01formhandler",
             action: "generate",
             timeout:60 * 1000 * 5,
             success:function(json){
            	 API.showPopMessage(i18n.def['runFinished']);
            	 grid.trigger("reloadGrid");
             },
             error:function(json){
            	 grid.trigger("reloadGrid");
             }
         });
    }).end().find("#btnFilter").click(function(){
    	openFilter(i18n.lms9550r01['grid.Filter'], grid, true);
    }).trigger('click');
});
