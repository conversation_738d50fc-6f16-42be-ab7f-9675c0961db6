package com.mega.eloan.lms.crs.handler.form;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.common.CrsRuleVO;
import com.mega.eloan.lms.crs.pages.LMS2401M01Page;
import com.mega.eloan.lms.crs.pages.LMS2405M01Page;
import com.mega.eloan.lms.crs.service.LMS2400Service;
import com.mega.eloan.lms.crs.service.LMS2401Service;
import com.mega.eloan.lms.crs.service.LMS2405Service;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.mfaloan.bean.ELF490;
import com.mega.eloan.lms.mfaloan.bean.ELF490B;
import com.mega.eloan.lms.mfaloan.bean.ELF491;
import com.mega.eloan.lms.mfaloan.bean.MISLN20;
import com.mega.eloan.lms.mfaloan.bean.MISLN30;
import com.mega.eloan.lms.mfaloan.service.MisELF487Service;
import com.mega.eloan.lms.mfaloan.service.MisELF490BService;
import com.mega.eloan.lms.mfaloan.service.MisELF490Service;
import com.mega.eloan.lms.mfaloan.service.MisELF491CService;
import com.mega.eloan.lms.mfaloan.service.MisELF491Service;
import com.mega.eloan.lms.mfaloan.service.MisELF492Service;
import com.mega.eloan.lms.mfaloan.service.MisELF591Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C240M01C;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金覆審交易
 * </pre>
 * 
 */
@Scope("request")
@Controller("lms2401m01formhandler")
@DomainClass(C240M01A.class)
public class LMS2401M01FormHandler extends AbstractFormHandler {
	private static final Logger logger = LoggerFactory.getLogger(LMS2401M01FormHandler.class);
	@Resource
	CLSService clsService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	ICustomerService customerService; 
	
	@Resource
	BranchService branchService;
	
	@Resource
	LMS2400Service lms2400Service;

	@Resource
	LMS2401Service lms2401Service;
	
	@Resource
	LMS2405Service lms2405Service;
	
	@Resource
	RetrialService retrialService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource 
	FlowSimplifyService flowSimplifyService;
	
	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;
	
	@Resource
	MisELF487Service misELF487Service;
	
	@Resource
	MisELF490Service misELF490Service;
	
	@Resource
	MisELF490BService misELF490BService;
	
	@Resource
	MisELF491Service misELF491Service;
	
	@Resource
	MisELF491CService misELF491CService;
	
	@Resource
	MisELF492Service misELF492Service;

	@Resource
	MisELF591Service misELF591Service;
	
	@Resource
	MisMISLN20Service misMISLN20Service;
	
	@Resource
	MisLNF030Service misLNF030Service;
	
	@Resource
	MisdbBASEService misdbBASEService;

    @Resource
    SysParameterService sysParameterService;
	
	Properties prop_lms2401m01 = MessageBundleScriptCreator.getComponentResource(LMS2401M01Page.class);
	Properties prop_lms2405m01 = MessageBundleScriptCreator.getComponentResource(LMS2405M01Page.class);
	Properties prop_abstractEloan = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
	
	private final String C240_TOTRETRIALCNT = "c240_totRetrialCnt";
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC240M01A_oid(mainOid);	
			
			String page = params.getString(EloanConstants.PAGE);		
			if ("01".equals(page)) {
				String branchName = branchService.getBranchName(meta.getBranchId());
				{
					LMSUtil.addMetaToResult(result, meta, new String[]{ "expectedRetrialDate","randomCode" });	
				}
				result.set("dataEndDate", StringUtils.substring(TWNDate.toAD(meta.getDataEndDate()), 0,7));
				result.set("branchName", meta.getBranchId() + " "+ branchName);
				result.set("status",prop_abstractEloan.getProperty("docStatus."+ meta.getDocStatus()));
				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime", Util.nullToSpace(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime",Util.nullToSpace(TWNDate.valueOf(meta.getUpdateTime())));
				result.set("approvercn", _id_name(meta.getApprover()));
				result.set("apprId", _id_name(meta.getHqAppraiserId()));
						
			} else if ("02".equals(page)) {
				{					
					LMSUtil.addMetaToResult(result, meta, new String[]{ "expectedRetrialDate"
							,"reQuantity","yearOfReview","plotsOfReview"
							,"samplingCount", "samplingRate"
							,"effectiveCount", "thisSamplingRate", "thisSamplingCount" });
					result.set("dataEndDate", StringUtils.substring(TWNDate.toAD(meta.getDataEndDate()), 0,7));
				}
				
				if(true){
					if(true){
						String ruleNo = CrsUtil.R95_1;
						C240M01C c240m01c_95_1 = retrialService.findC240M01C(meta.getMainId(), ruleNo);					
						result.set("c240m01c_R95_1_oid", c240m01c_95_1==null?"":c240m01c_95_1.getOid());
						result_setObj_c240m01c_R95_1(result, meta.getMainId(), ruleNo, c240m01c_95_1);
					}
					if(true){
						String ruleNo = CrsUtil.R1R2S;
						C240M01C c240m01c_R1R2S = retrialService.findC240M01C(meta.getMainId(), ruleNo);
						result.set("c240m01c_R1R2S_oid", c240m01c_R1R2S==null?"":c240m01c_R1R2S.getOid());
						result_setObj_c240m01c_R1R2S(result, meta.getMainId(), ruleNo, c240m01c_R1R2S);
					}
					if(true){
						String ruleNo = CrsUtil.R14;
						C240M01C c240m01c_R14 = retrialService.findC240M01C(meta.getMainId(), ruleNo);
						result.set("c240m01c_R14_oid", c240m01c_R14==null?"":c240m01c_R14.getOid());
						result_setObj_c240m01c_R14(result, meta.getMainId(), ruleNo, c240m01c_R14);
					}
					if(true){
						String ruleNo = CrsUtil.R13;
						C240M01C c240m01c_R_projectCreditLoan = retrialService.findC240M01C(meta.getMainId(), ruleNo);					
						result.set("c240m01c_R_projectCreditLoan_oid", c240m01c_R_projectCreditLoan==null?"":c240m01c_R_projectCreditLoan.getOid());
						result_setObj_c240m01c_R_projectCreditLoan(result, meta.getMainId(), ruleNo, c240m01c_R_projectCreditLoan);
					}					
				}
				
				HashMap<String, JSONArray> map = new HashMap<String, JSONArray>(); 
				setAttchFile(map, "excelFile", meta.getMainId(), CrsUtil.ATTCH_C240M01A_0);
				setAttchFile(map, "branchFile", meta.getMainId(), CrsUtil.ATTCH_C240M01A_1);
				setAttchFile(map, "chkExcelFile", meta.getMainId(), CrsUtil.ATTCH_C240M01A_2);
				setAttchFile(map, "ejcicFile", meta.getMainId(), CrsUtil.ATTCH_C240M01A_3A);
				setAttchFile(map, "ejcicFile", meta.getMainId(), CrsUtil.ATTCH_C240M01A_3B);
				result.set("attch", new CapAjaxFormResult(map));
			}
		}

		return defaultResult(params, meta, result);
	}
	
	private void setAttchFile(HashMap<String, JSONArray> map, String ui_id, String mainId, String fieldId)
	throws CapException{
		JSONArray jsonAraay = null;
		if(map.containsKey(ui_id)){
			jsonAraay = map.get(ui_id);
		}else{
			jsonAraay = new JSONArray();
		}
		//---
		List<DocFile> docFileList = retrialService.findDocFileByMainIdFieldId(mainId, fieldId);
		for(DocFile docFile:docFileList){
			if(Util.equals(fieldId, docFile.getFieldId())){
				JSONObject o = new JSONObject();
				o.putAll(DataParse.toJSON(docFile, true ));
				o.put("uploadTime", TWNDate.toFullTW(docFile.getUploadTime()) );
				jsonAraay.add(o);	
			}			
		}
		//---
		map.put(ui_id, jsonAraay);
	}
	
	private String _id_name(String raw_id){
		String id = Util.trim(raw_id);
		String name = "";
		if(Util.isNotEmpty(id)){
			name = Util.trim(userInfoService.getUserName(id));
		}
		return Util.trim(id+" "+name);
	}
	
	private CapAjaxFormResult defaultResult(PageParameters params, C240M01A meta,
			CapAjaxFormResult result) throws CapException {
		String branchName = Util.trim(branchService.getBranchName(meta.getBranchId()));
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
		result.set("titleInfo", meta.getBranchId() + " " + branchName);
		result.set("is_flowClass_throughBr", CrsUtil.is_flowClass_throughBr(sysParameterService,meta)?"Y":"N");
		{
			boolean isLock = false;
			RetrialDocStatusEnum docStatusEnum = RetrialDocStatusEnum.getEnum(meta.getDocStatus());			
			if (docStatusEnum == RetrialDocStatusEnum.待覆核) {
				isLock = true;
			}			
			result.set("lock", isLock);	
		}		
		return result;
	}
	
	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}

	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params)
	throws CapException {
		return _saveAction(params, "Y");
	}
	
	/**
	 * 修改預計覆審日
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveDefault(PageParameters params)
			throws CapException {	
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String defaultCTLDate = params.getString("defaultCTLDate");
		Date defaultDate = null;
		if (Util.isNotEmpty(defaultCTLDate)) {
			defaultDate = CapDate.parseDate(defaultCTLDate);
		}
		C240M01A c240m01a = retrialService.findC240M01A_oid(mainOid);
		MegaSSOUserDetails userId = MegaSSOSecurityContext.getUserDetails();		
		c240m01a.setUpdater(userId.getUserId());
		c240m01a.setExpectedRetrialDate(defaultDate);		
		lms2400Service.c240m01a_ExpectedRetrialDate(c240m01a, defaultDate);
		//---
		return query(params);
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,String tempSave)
	throws CapException{
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		//===
		String KEY = "saveOkFlag";
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try{
				meta = retrialService.findC240M01A_oid(mainOid);	
				
				String page = params.getString(EloanConstants.PAGE);		
				if ("01".equals(page)) {
					
				} else if ("02".equals(page)) {
					CapBeanUtil.map2Bean(params, meta, new String[] { "thisSamplingRate" });
				}
				
				retrialService.save(meta);
				//===
				result.set(KEY, true);
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}			
		}
		
		result.add(query(params));		
		return result;
	}
	
	/**
	 * 呈主管覆核
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		C240M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC240M01A_oid(mainOid);	
			
			if(Util.isEmpty(meta.getExpectedRetrialDate())){
				throw new CapMessageException(prop_lms2405m01.getProperty("err.noDate"), getClass());
			}
			String errMsg = "";
			if(Util.equals("核定", decisionExpr)){
				//檢查經辦和主管是否為同一人
				if(Util.equals(user.getUserId(), meta.getUpdater())){
					//覆審不擋 approver 是否為同一人
					//errMsg = RespMsgHelper.getMessage("EFD0053");	
				}				
			}

			if(Util.isNotEmpty(errMsg)){
				throw new CapMessageException(errMsg, getClass());
			}
			flowSimplifyService.flowNext(meta.getOid(), decisionExpr);
			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
	
	/**
	 * 本次欲抽樣 ％, 產生8_1資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult produce8_1(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		if (meta == null){
			meta = new C240M01A();
		}
				
		meta.setThisSamplingRate(params.getAsInteger("thisSamplingRate", 0));
		if(meta.getThisSamplingRate()>100){
			throw new CapMessageException("抽樣比率不可>100", getClass());
		}
		lms2400Service.produce8_1(meta);

		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity", "thisSamplingRate", "thisSamplingCount" });	
		return defaultResult( params, meta, result);
	}
	
	/**
	 * 新增覆審客戶
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult produceNew(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		
		//Map<String, Object> custMap = customerService.findByIdDupNo(Util.trim(params.getString("custId")).toUpperCase(), Util.trim(params.getString("dupNo")));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String cName = Util.trim(params.getString("custName"));
		
		//檢查 custId 是否已存在工作底稿
		for(C241M01A c241m01a :retrialService.findC241M01A_C240M01A(meta.getMainId())){
			if(CrsUtil.isCaseP(c241m01a)){
				//在同一份覆審工作底稿, 若同 1 個人有 N 個存款(代收)帳號, 則價金履約保證
				continue;
			}
			if(CrsUtil.isCaseS(c241m01a)){
				continue;
			}
			if (Util.equals(c241m01a.getCustId(), custId) && Util.equals(c241m01a.getDupNo(), dupNo)) {
				throw new CapMessageException(prop_lms2405m01.getProperty("alreadyHave"), getClass());
			}	
		}
				
		boolean r = lms2400Service.produceNew(meta, custId, dupNo, cName);
		if(r==false){
			//ui_lms2401.msg04={0}於{1}分行無帳務資料
			String errMsg = MessageFormat.format(prop_lms2401m01.getProperty("ui_lms2401.msg04"),(custId+"-"+dupNo+" "+cName), meta.getBranchId());
			throw new CapMessageException(errMsg, getClass());
		}else{
			retrialService.genProjectNo_append(meta);
		}

		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});	
		return defaultResult( params, meta, result);
	}

	/**
	 * 重新傳送覆審名單至BTT
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult sendBtt(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		if (meta == null){
			meta = new C240M01A();
		}	
		retrialService.sendBtt(meta);
			
		return defaultResult( params, meta, result);
	}
	
	/**
	 * 刪除(註記)不覆審客戶
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveNoCTL(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String nCkdFlag = Util.trim(params.getString("nCkdFlag"));		
		String nCkdDetail = Util.trim(params.getString("nCkdDetail"));
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		
		List<String> oid_arr = new ArrayList<String>();
		List<C241M01A> skipArr_upload = new ArrayList<C241M01A>();
		List<C241M01A> skipArr_R98 = new ArrayList<C241M01A>();
		for(String oid:params.getStringArray("oid_arr")){
			oid_arr.add(oid);
		}
		lms2400Service.saveNoCTL(meta, oid_arr, nCkdFlag, nCkdDetail, skipArr_upload, skipArr_R98);
				
		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});
		
		List<String> msg_list = new ArrayList<String>();
		if(true){
			if(skipArr_upload.size()>0){
				List<String> notify_upload_List = new ArrayList<String>();
				for(C241M01A skipItem : skipArr_upload){
					notify_upload_List.add(skipItem.getCustId()+" "+ skipItem.getCustName());
				}
				//ui_lms2401.msg07=已上傳過之案件{0}不可註記不覆審			
				msg_list.add(MessageFormat.format(prop_lms2401m01.getProperty("ui_lms2401.msg07")
						, StringUtils.join(notify_upload_List, "、")));
			}	
			if(skipArr_R98.size()>0){
				List<String> notify_R98_List = new ArrayList<String>();
				for(C241M01A skipItem : skipArr_R98){
					notify_R98_List.add(skipItem.getCustId()+" "+ skipItem.getCustName());
				}
				//ui_lms2401.msg12=此分行{0}有未解除之已核准異常通報表，不得為不覆審案件
				msg_list.add(MessageFormat.format(prop_lms2401m01.getProperty("ui_lms2401.msg12")
						, StringUtils.join(notify_R98_List, "、")));
			}
		}
		
		result.set("notifyMsgYN", msg_list.size()>0?"Y":"N");
		result.set("notifyMsg", StringUtils.join(msg_list, "<br/>"));
		
		if(true){
			if(true){
				String ruleNo = CrsUtil.R95_1;
				C240M01C c240m01c_95_1 = retrialService.findC240M01C(meta.getMainId(), ruleNo);					
				result.set("c240m01c_R95_1_oid", c240m01c_95_1==null?"":c240m01c_95_1.getOid());
				result_setObj_c240m01c_R95_1(result, meta.getMainId(), ruleNo, c240m01c_95_1);
			}
			if(true){
				String ruleNo = CrsUtil.R1R2S;
				C240M01C c240m01c_R1R2S = retrialService.findC240M01C(meta.getMainId(), ruleNo);
				result.set("c240m01c_R1R2S_oid", c240m01c_R1R2S==null?"":c240m01c_R1R2S.getOid());
				result_setObj_c240m01c_R1R2S(result, meta.getMainId(), ruleNo, c240m01c_R1R2S);
			}
			if(true){
				String ruleNo = CrsUtil.R14;
				C240M01C c240m01c_R14 = retrialService.findC240M01C(meta.getMainId(), ruleNo);
				result.set("c240m01c_R14_oid", c240m01c_R14==null?"":c240m01c_R14.getOid());
				result_setObj_c240m01c_R14(result, meta.getMainId(), ruleNo, c240m01c_R14);
			}
			if(true){
				String ruleNo = CrsUtil.R13;
				C240M01C c240m01c_R_projectCreditLoan = retrialService.findC240M01C(meta.getMainId(), ruleNo);					
				result.set("c240m01c_R_projectCreditLoan_oid", c240m01c_R_projectCreditLoan==null?"":c240m01c_R_projectCreditLoan.getOid());
				result_setObj_c240m01c_R_projectCreditLoan(result, meta.getMainId(), ruleNo, c240m01c_R_projectCreditLoan);
			}					
		}
		
		return defaultResult( params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult saveNoCTL_O(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String cmpOid = Util.trim(params.getString("cmpOid"));
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		C240M01A cmpMeta = retrialService.findC240M01A_oid(cmpOid);
		
		lms2400Service.saveNoCTL_O(meta, cmpMeta);
		
		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});	
		return defaultResult( params, meta, result);
	}

	
	/**
	 * 還原不覆審客戶
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveReCTL(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		
		List<String> oid_arr = new ArrayList<String>();
		for(String oid:params.getStringArray("oid_arr")){
			oid_arr.add(oid);
		}
		lms2400Service.saveReCTL(meta, oid_arr);
		retrialService.genProjectNo_append(meta);
		
		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});	
		return defaultResult( params, meta, result);
	}
	
	public IResult load_nckdFlag(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();		
		result.set("nckdFlag", new CapAjaxFormResult(retrialService.get_crs_NckdFlagMapActive()));
		return result;
	}	
	
	@DomainAuth(AuthType.Modify)
	public IResult addNotShowCust(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A c240m01a = retrialService.findC240M01A_oid(mainOid);
		List<String> msg_list = new ArrayList<String>();
		int tot_modify_cnt = 0;
		if(c240m01a!=null){
			Date fetch_date = c240m01a.getDataEndDate();
			if(CrsUtil.isNull_or_ZeroDate(fetch_date)){
				msg_list.add( prop_lms2401m01.getProperty("C240M01A.dataEndDate")+"無資料" );
			}else{
				List<String> insert_list = new ArrayList<String>();
				List<String> retrialYN_NtoY_list = new ArrayList<String>();
				String brNo = c240m01a.getBranchId();
				
				Set<String> exist_idDup_retrialYN_Y = new HashSet<String>();
				Set<String> exist_idDup_retrialYN_N = new HashSet<String>();
				Map<String, C241M01A> map_idDup_C241M01A = new HashMap<String, C241M01A>();
				for(C241M01A c241m01a : retrialService.findC241M01A_C240M01A(c240m01a.getMainId())){
					if(CrsUtil.isCaseN(c241m01a)){
						String idDup_C241M01A = LMSUtil.getCustKey_len10custId(c241m01a.getCustId(), c241m01a.getDupNo());
						map_idDup_C241M01A.put(idDup_C241M01A, c241m01a);
						//~~~~~
						if(Util.equals("Y", c241m01a.getRetrialYN())){
							exist_idDup_retrialYN_Y.add(idDup_C241M01A);
						}else{
							exist_idDup_retrialYN_N.add(idDup_C241M01A);
						}
					}else if(CrsUtil.isCaseP(c241m01a)){
						
					}else if(CrsUtil.isCaseS(c241m01a)){	
					}
				}
				//====================
				if(true){
					LinkedHashSet<String> reCTL_oid_set = new LinkedHashSet<String>();
					for(ELF491 elf491: misELF491Service.selByBranch_activeCrDate(brNo)){
						if(LMSUtil.cmp_yyyyMM(elf491.getElf491_crdate(), ">", fetch_date)){ //非本次工作底稿的資料範圍
							continue;
						}
						String custId = elf491.getElf491_custid();
						String dupNo =  elf491.getElf491_dupno();
						String idDup_elf491 = LMSUtil.getCustKey_len10custId(elf491.getElf491_custid(), elf491.getElf491_dupno());
						if(exist_idDup_retrialYN_Y.contains(idDup_elf491)){ //已在 需覆審清單 之中
							continue;
						}
						//~~~~~~~~~~~~~~~~~
						if(exist_idDup_retrialYN_N.contains(idDup_elf491)){
							if(map_idDup_C241M01A.containsKey(idDup_elf491)){
								C241M01A c241m01a = map_idDup_C241M01A.get(idDup_elf491);
								if(c241m01a!=null){
									reCTL_oid_set.add(c241m01a.getOid());
								}							
							}
						}else{
							String cName = clsService.get0024_custName(custId, dupNo);
							if(lms2400Service.produceNew(c240m01a,  custId,  dupNo, cName)){
								insert_list.add(custId+"-"+dupNo+" "+cName);
							}
						}
					}
					
					if(reCTL_oid_set.size()>0){
						lms2400Service.saveReCTL(c240m01a, new ArrayList<String>(reCTL_oid_set));
						//~~~~~~~~~~~~~~~~~
						for(String c241m01a_oid : reCTL_oid_set){
							C241M01A c241m01a = retrialService.findC241M01A_oid(c241m01a_oid);
							if(c241m01a!=null && Util.equals("Y", c241m01a.getRetrialYN()) ){ // RetrialYN 已經由 N to Y 
								String custId = c241m01a.getCustId();
								String dupNo = c241m01a.getDupNo();
								String cName = c241m01a.getCustName();
								retrialYN_NtoY_list.add(custId+"-"+dupNo+" "+cName);
							}
						}					
					}
				}
				//====================
				if(true){
					if(insert_list.size()>0 ){
						msg_list.add("已新增"+insert_list.size()+"筆資料：");
						msg_list.addAll(insert_list);
						tot_modify_cnt += insert_list.size();
					}
					if(retrialYN_NtoY_list.size()>0){
						msg_list.add("已將"+retrialYN_NtoY_list.size()+"筆資料，恢復覆審：");
						msg_list.addAll(retrialYN_NtoY_list);
						tot_modify_cnt += retrialYN_NtoY_list.size();
					}
				}
			}
		}
		if(tot_modify_cnt==0 && msg_list.size()==0){
			msg_list.add("執行成功。");
			msg_list.add("本次未異動工作底稿。");			
		}
		if(tot_modify_cnt>0){	
			retrialService.genProjectNo_append(c240m01a);
			//~~~
			LMSUtil.addMetaToResult(result, c240m01a, new String[]{ "reQuantity"});
			//~~~
			//若有異動, 補上傳BTT, 讓分行能印出
			if(Util.equals(RetrialDocStatusEnum.已產生覆審名單報告檔.getCode(), c240m01a.getDocStatus()) ){
				retrialService.sendBtt(c240m01a);
			}
		}
		result.set("msg", StringUtils.join(msg_list, "<br/>")); 
		return defaultResult( params, c240m01a, result);
	}

	
	/**
	 * 重新編排覆審序號
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult genCrsProjectNo(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		
		CapAjaxFormResult result = new CapAjaxFormResult();				
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		
		retrialService.genProjectNo(meta);
		
		return defaultResult( params, meta, result);		
	}
	
	/**
	 * 重新整理99
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deriveFrom99(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		
		CapAjaxFormResult result = new CapAjaxFormResult();				
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		lms2400Service.deriveFrom99(meta, retrialService.findC241M01A_C240M01A(meta.getMainId()), user.getUserId());
		
		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});		
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return defaultResult( params, meta, result);		
	}
	
	
	/**
	 * 重引帳務
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult importLN(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		
		ArrayList<String> oid_list = new ArrayList<String>();
		
		if(Util.equals("Y", params.getString("allOidFlag"))){			
			for(C241M01A c241m01a : retrialService.findC241M01A_C240M01A(meta.getMainId())){
				oid_list.add(c241m01a.getOid());
			}
			
		}else{
			String[] oid_arr = params.getStringArray("oid_arr");
			for(String oid: oid_arr){
				oid_list.add(oid);
			}
		}
		
		if(CollectionUtils.isNotEmpty(oid_list)){
			retrialService.importLNtoC241M01B(meta, oid_list);	
		}		
		
		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});	
		return defaultResult( params, meta, result);
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult produceExcel(PageParameters params)
			throws Exception {
		CapAjaxFormResult result = new CapAjaxFormResult();				
		if(Util.isEmpty(params.get("mode"))){
			throw new CapMessageException(prop_lms2405m01.getProperty("err.chooseOne"), getClass());
		}
		
		HashSet<String> failSet = new HashSet<String>();
		String[] oids = Util.trim(params.getString("oids")).split("\\|");
		int mode = params.getInt("mode");
		for(String oid: oids){
			C240M01A meta = retrialService.findC240M01A_oid(oid);
			if(meta!=null){
				boolean success = false;
				if (mode == 0) {
					success = lms2400Service.produce_attch(meta, CrsUtil.ATTCH_C240M01A_0);
				} else if (mode == 1) {
					success = lms2400Service.produce_attch(meta, CrsUtil.ATTCH_C240M01A_1);
				} else if (mode == 2) {
					success = lms2400Service.produce_attch(meta, CrsUtil.ATTCH_C240M01A_2);
				} else if (mode == 3) {
					success = lms2400Service.produceEjcicExcel(meta);
				}
				
				if(success==false){
					failSet.add(meta.getOwnBrId());
				}
			}
		}
		
		if (CollectionUtils.isEmpty(failSet)) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		} else {
			Map<String, String> errmap = new HashMap<String, String>();
			errmap.put("msg", StringUtils.join(failSet, "、"));
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
					RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, errmap));
		}

		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult deleteMark(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = Util.trim(params.getString("oids")).split("\\|");
		HashMap<String, C240M01A> map = new HashMap<String, C240M01A>();
		for(String oid : oids){
			C240M01A c240m01a = retrialService.findC240M01A_oid(oid);
			if(c240m01a!=null){
				map.put(oid, c240m01a);
			}
		}
		
		HashSet<String> failSet = new HashSet<String>();
		for(String oid:map.keySet()){
			C240M01A c240m01a = map.get(oid);
			List<DocOpener> docOpeners = docCheckService.findByMainId(c240m01a.getMainId());
			for(DocOpener docOpener : docOpeners){
				if(OpenTypeCode.Writing.getCode().equals(docOpener.getOpenType())){
					HashMap<String, String> hm = new HashMap<String, String>();
					hm.put("userId", docOpener.getOpener());
					hm.put("userName",
							userInfoService.getUserName(docOpener.getOpener()));
					failSet.add(RespMsgHelper.getMessage("EFD0009", hm));
					break;
				}
			}
		}
		if (CollectionUtils.isNotEmpty(failSet)) {
			throw new CapMessageException(StringUtils.join(failSet, "、"), getClass());
		}
		
		for(String oid:map.keySet()){			
			lms2405Service.deleteMainMark(oid);
		}
		// EFD0019=刪除成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
		return result;
	}
	
	/**
	 * 刪除上傳檔案
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteUploadFile(PageParameters params)
			throws CapException {

		String fid = params.getString("fileOid");
		if (retrialService.delfile(fid)) {
			//...
		}
		return query(params);
	}
	
	public IResult test(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		return result;
	}
	
	private Map<String, C241M01A> _existAllPMap(C240M01A meta){
		Map<String, C241M01A> existAllPMap = new HashMap<String, C241M01A>();
		
		//檢查 同一 cntrNo, lcNo 是否已存在工作底稿
		for(C241M01A c241m01a :retrialService.findC241M01A_C240M01A(meta.getMainId())){
			if(CrsUtil.isCaseP(c241m01a)){
				//在同一份覆審工作底稿, 若同 1 個人有 N 個存款(代收)帳號, 則價金履約保證要出現 N 筆
				for(C241M01B c241m01b : retrialService.findC241M01B_c241m01a(c241m01a)){
					String key = CrsUtil.build_P_Key(c241m01b);
					existAllPMap.put(key, c241m01a);
				}
			}
		}
		
		return existAllPMap;
	} 
	
	private Set<String> getIdDup_docKindS(C240M01A meta){
		Set<String> r = new HashSet<String>();
		for(C241M01A c241m01a :retrialService.findC241M01A_C240M01A(meta.getMainId())){
			if(CrsUtil.isCaseS(c241m01a)){
				String idDup = LMSUtil.getCustKey_len10custId(c241m01a.getCustId(), c241m01a.getDupNo());
				r.add(idDup);
			}	
		}
		return r;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult produce95_1_multiple(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);

		//檢查 custId 是否已存在工作底稿
		Set<String> existIdDupSet = new HashSet<String>();
		for(C241M01A c241m01a :retrialService.findC241M01A_C240M01A(meta.getMainId())){
			if(CrsUtil.isCaseP(c241m01a)){
				continue;
			}
			if(CrsUtil.isCaseS(c241m01a)){
				continue;
			}
			
			existIdDupSet.add(LMSUtil.getCustKey_len10custId(c241m01a.getCustId(), c241m01a.getDupNo()));
		}
		
		
		LinkedHashSet<String> idDupSet = new LinkedHashSet<String>();
		for(String idDup:params.getStringArray("choose_arr")){
			idDupSet.add(idDup);
		}
		
		int done_cnt = 0;
		int fail_cnt = 0;
		Set<String> joinSet = LMSUtil.elm_onlyLeft(idDupSet, existIdDupSet);
		for(String idDup : idDupSet){
			if(joinSet.contains(idDup)){
				String custId = CrsUtil.get_custId_from_custKey(idDup);
				String dupNo = CrsUtil.get_dupNo_from_custKey(idDup);		
						
				String cName = clsService.get0024_custName(custId, dupNo);
				
				boolean r = lms2400Service.produce95_1(meta, custId, dupNo, cName);
				if(r){
					++done_cnt;
					if(true){
						//放在loop迴圈裡, 才會依「選擇的順序」去編號
						//若在loop外, 因為勾選N筆ID的 projectNo都空白, 會依「ID順序」去編號
						retrialService.genProjectNo_append(meta);
					}
				}else{
					++fail_cnt;
				}
			}
		}
		
		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});	
		if(true){ //應更新 本次抽樣戶數為：N 筆
			String ruleNo = CrsUtil.R95_1;
			C240M01C c240m01c_95_1 = retrialService.findC240M01C(meta.getMainId(), ruleNo);
			result_setObj_c240m01c_R95_1(result, meta.getMainId(), ruleNo, c240m01c_95_1);
		}
		return defaultResult( params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult produce96(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
/*
		$.ajax({
		    handler: "lms2401m01formhandler", action: "produce96",
		    data: {'custId':'A1...', 'dupNo':'0', 'custName':'X','mainOid': responseJSON.mainOid, 'pa_ym':'2018-12', 'pa_trg':''},
		    success: function(json){
			
		    }
		});
*/
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		
		String cName = clsService.get0024_custName(custId, dupNo);
		
		String pa_ym = Util.trim(params.getString("pa_ym"));
		String pa_trg = Util.trim(params.getString("pa_trg"));
		if(Util.isEmpty(pa_ym)){
			throw new CapMessageException(prop_lms2401m01.getProperty("label.docKindS_pa_period")+"="+pa_ym, getClass());
		}
		
		//檢查 custId 是否已存在工作底稿
		Set<String> existDocKindSSet = getIdDup_docKindS(meta);
		if(existDocKindSSet.contains(LMSUtil.getCustKey_len10custId(custId, dupNo))){
			throw new CapMessageException(prop_lms2405m01.getProperty("alreadyHave"), getClass());
		}
				
		boolean r = lms2400Service.produce96(meta, custId, dupNo, cName, pa_ym, pa_trg);
		if(r==false){
			//ui_lms2401.msg04={0}於{1}分行無帳務資料
			String errMsg = MessageFormat.format(prop_lms2401m01.getProperty("ui_lms2401.msg04"),(custId+"-"+dupNo+" "+cName), meta.getBranchId());
			throw new CapMessageException(errMsg, getClass());
		}else{
			retrialService.genProjectNo_append(meta);
		}

		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});	
		return defaultResult( params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult produce96_multiple(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);

		String pa_ym = Util.trim(params.getString("pa_ym"));
		String pa_trg = Util.trim(params.getString("pa_trg"));
		if(Util.isEmpty(pa_ym)){
			throw new CapMessageException(prop_lms2401m01.getProperty("label.docKindS_pa_period")+"="+pa_ym, getClass());
		}

		//檢查 custId 是否已存在工作底稿
		Set<String> existDocKindSSet = getIdDup_docKindS(meta);
		
		LinkedHashSet<String> idDupSet = new LinkedHashSet<String>();
		for(String idDup:params.getStringArray("choose_arr")){
			idDupSet.add(idDup);
		}
		
		int done_cnt = 0;
		int fail_cnt = 0;
		Set<String> joinSet = LMSUtil.elm_onlyLeft(idDupSet, existDocKindSSet);
		for(String idDup : idDupSet){
			if(joinSet.contains(idDup)){
				String custId = CrsUtil.get_custId_from_custKey(idDup);
				String dupNo = CrsUtil.get_dupNo_from_custKey(idDup);		
				
				String cName = clsService.get0024_custName(custId, dupNo);
				
				boolean r = lms2400Service.produce96(meta, custId, dupNo, cName, pa_ym, pa_trg);
				if(r){
					++done_cnt;
					if(true){
						//放在loop迴圈裡, 才會依「選擇的順序」去編號
						//若在loop外, 因為勾選N筆ID的 projectNo都空白, 會依「ID順序」去編號
						retrialService.genProjectNo_append(meta);
					}
				}else{
					++fail_cnt;
				}
			}
		}
		
		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});	
		return defaultResult( params, meta, result);
	}
	
	private void result_setObj_c240m01c_R95_1(CapAjaxFormResult result, String c240m01a_maiId, String ruleNo, C240M01C c240m01c_95_1){
		if(c240m01c_95_1==null){
			return;
		}
		C240M01C baseYM_c241m01c = retrialService.findC240M01C(c240m01a_maiId, CrsUtil.LIST_BASE_95_1_RULE_NO);
		CapAjaxFormResult result_c240m01c_95_1 = new CapAjaxFormResult();
		result_c240m01c_95_1.set(C240_TOTRETRIALCNT, retrialService.count_retrialKind_in_c240m01a(c240m01a_maiId, ruleNo));
		result_c240m01c_95_1.set("hasData", c240m01c_95_1.getAllCnt()>0?"Y":"N");
		result_c240m01c_95_1.set("baseYM", Util.trim(TWNDate.toAD(baseYM_c241m01c.getAllEndDate())));
		result_c240m01c_95_1.set("baseYM_cnt", baseYM_c241m01c.getAllCnt());
		inject_c240m01c_to_CapAjaxFormResult(result_c240m01c_95_1, c240m01c_95_1);
		//~~~~~~
		result.set("c240m01c_R95_1", result_c240m01c_95_1);
	}
	
	private void result_setObj_c240m01c_R1R2S(CapAjaxFormResult result, String c240m01a_maiId, String ruleNo, C240M01C c240m01c_R1R2S){
		if(c240m01c_R1R2S==null){
			return;
		}
		
		CapAjaxFormResult result_by_rule = new CapAjaxFormResult();	
		result_by_rule.set(C240_TOTRETRIALCNT, retrialService.count_retrialKind_in_c240m01a(c240m01a_maiId, ruleNo));
		result_by_rule.set("hasData", c240m01c_R1R2S.getAllCnt()>0?"Y":"N");
		result_by_rule.set("allYearStr", Util.getLeftStr(Util.trim(TWNDate.toAD(c240m01c_R1R2S.getAllBegDate())), 4)); //只取年
		result_by_rule.set("doneYearStr", Util.getLeftStr(Util.trim(TWNDate.toAD(c240m01c_R1R2S.getDoneBegDate())), 4)); //只取年
		inject_c240m01c_to_CapAjaxFormResult(result_by_rule, c240m01c_R1R2S);
		//~~~~~~
		result.set("c240m01c_R1R2S", result_by_rule);		
	}
	
	private void result_setObj_c240m01c_R14(CapAjaxFormResult result, String c240m01a_maiId, String ruleNo, C240M01C c240m01c_R14){
		if(c240m01c_R14==null){
			return;
		}
		
		CapAjaxFormResult result_by_rule = new CapAjaxFormResult();	
		result_by_rule.set(C240_TOTRETRIALCNT, retrialService.count_retrialKind_in_c240m01a(c240m01a_maiId, ruleNo));
		result_by_rule.set("hasData", c240m01c_R14.getAllCnt()>0?"Y":"N");
		result_by_rule.set("allYearStr", Util.getLeftStr(Util.trim(TWNDate.toAD(c240m01c_R14.getAllBegDate())), 4)); //只取年
		result_by_rule.set("doneYearStr", Util.getLeftStr(Util.trim(TWNDate.toAD(c240m01c_R14.getDoneBegDate())), 4)); //只取年
		inject_c240m01c_to_CapAjaxFormResult(result_by_rule, c240m01c_R14);
		//~~~~~~
		result.set("c240m01c_R14", result_by_rule);		
	}
	
	private void result_setObj_c240m01c_R_projectCreditLoan(CapAjaxFormResult result, String c240m01a_maiId, String ruleNo, C240M01C c240m01c_R_projectCreditLoan){
		if(c240m01c_R_projectCreditLoan==null){
			return;
		}
		
		CapAjaxFormResult result_by_rule = new CapAjaxFormResult();	
		result_by_rule.set(C240_TOTRETRIALCNT, retrialService.count_retrialKind_in_c240m01a(c240m01a_maiId, ruleNo));
		inject_c240m01c_to_CapAjaxFormResult(result_by_rule, c240m01c_R_projectCreditLoan);
		//~~~~~~
		result.set("c240m01c_R_projectCreditLoan", result_by_rule);		
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult produce_R1R2S(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);

		//檢查 custId 是否已存在工作底稿
		Set<String> existIdDupSet = new HashSet<String>();
		for(C241M01A c241m01a :retrialService.findC241M01A_C240M01A(meta.getMainId())){
			if(CrsUtil.isCaseP(c241m01a)){
				continue;
			}
			if(CrsUtil.isCaseS(c241m01a)){
				continue;
			}
			
			existIdDupSet.add(LMSUtil.getCustKey_len10custId(c241m01a.getCustId(), c241m01a.getDupNo()));
		}
		
		LinkedHashSet<String> idDupSet = new LinkedHashSet<String>();
		
		String brNo = meta.getBranchId();
		int samplingCnt_R1R2S = params.getInt("samplingCnt_R1R2S", 0);
		int samplingRate_R1R2S = params.getInt("samplingRate_R1R2S", 0);
		String[] choose_arr = null;
		if(Util.isNotEmpty(params.getString("idDup_arr"))){
			choose_arr = params.getString("idDup_arr").split("\\|"); 
		}
		int sample_cnt = 0;
		int c240m01c_all_cnt = 0;
		if(samplingCnt_R1R2S==0 && samplingRate_R1R2S==0 ){
			/*  $.ajax({
			    handler: "lms2401m01formhandler", action: "produce_R1R2S",
			    data: {'samplingRate_R1R2S': 0, idDup_arr:'idDupA|idDupB'},
			    success: function(json){  alert('ok');  }
			}); */
			if(choose_arr!=null && choose_arr.length>0){
				for(String idDup : choose_arr){
					idDupSet.add(idDup);
				}		
			}
			
			if(idDupSet.size()==0){
				throw new CapMessageException("請輸入抽樣資訊", getClass());
			}else{
				sample_cnt = idDupSet.size();	
			}			
		}else{
			if(samplingCnt_R1R2S > 0){
				sample_cnt = samplingCnt_R1R2S;
			}else{
				if(samplingRate_R1R2S > 100){
					throw new CapMessageException("抽樣比率不可 > 100", getClass());
				}
				
				C240M01C c240m01c = retrialService.findC240M01C(meta.getMainId(), CrsUtil.R1R2S);
				if(c240m01c!=null){
					c240m01c_all_cnt = c240m01c.getAllCnt();
					sample_cnt = samplingRate_R1R2S * c240m01c_all_cnt / 100;
				}
			}
			
			if(sample_cnt > 0){
				int buffer_cnt = 25; //為了避免 (1)有些當月已銷戶 (2)已存在工作底稿
				int fetch_size = sample_cnt + buffer_cnt;
				BigDecimal threshold_amt = CrsUtil.is_tp_brNo(brNo)? CrsUtil.R1R2_IN_TPE_AMT_3000WAN : CrsUtil.R1R2_NO_TPE_AMT_1500WAN;
				for(Map<String, Object> map :misELF491CService.selByBrno_R1R2S(brNo, threshold_amt, fetch_size)){
					String custId = Util.trim(MapUtils.getString(map, "ELF488_CUST_ID"));
					String dupNo = Util.trim(MapUtils.getString(map, "ELF488_DUP_NO"));
					idDupSet.add(LMSUtil.getCustKey_len10custId(custId, dupNo));
				}
			}
		}
		
		int done_cnt = 0;
		int fail_cnt = 0;
		Set<String> joinSet = LMSUtil.elm_onlyLeft(idDupSet, existIdDupSet);
		ArrayList<String> joinSet_skip_list = new ArrayList<String>();
		if(joinSet.size()==0){
			throw new CapMessageException("無符合抽樣資訊", getClass());
		}
		for(String idDup : idDupSet){
			if(joinSet.contains(idDup)){
				if(idDup.length()!=11){
					throw new CapMessageException("輸入["+idDup+"]格式錯誤", getClass()); 
				}
				
				String custId = CrsUtil.get_custId_from_custKey(idDup);
				String dupNo = CrsUtil.get_dupNo_from_custKey(idDup);		
						
				String cName = clsService.get0024_custName(custId, dupNo);	
				boolean r = lms2400Service.produce_R1R2S(meta, custId, dupNo, cName);
				if(r){
					++done_cnt;
					if(true){
						//放在loop迴圈裡, 才會依「選擇的順序」去編號
						//若在loop外, 因為勾選N筆ID的 projectNo都空白, 會依「ID順序」去編號
						retrialService.genProjectNo_append(meta);
					}
				}else{
					++fail_cnt;
				}
			}else{
				joinSet_skip_list.add(idDup);
			}
			
			if(done_cnt >= sample_cnt){
				break;
			}
		}
		
		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});
		if(true){ //應更新 本次抽樣戶數為：N 筆
			String ruleNo = CrsUtil.R1R2S;
			C240M01C c240m01c_R1R2S = retrialService.findC240M01C(meta.getMainId(), ruleNo);
			result_setObj_c240m01c_R1R2S(result, meta.getMainId(), ruleNo, c240m01c_R1R2S);
		}	
		
		CapAjaxFormResult proc_info = new CapAjaxFormResult();
		if(samplingCnt_R1R2S>0){
			proc_info.set("i_sample_cnt", String.valueOf(samplingCnt_R1R2S));
		}else{
			proc_info.set("i_sample_rate", String.valueOf(samplingRate_R1R2S));
			proc_info.set("i_c240m01c_all_cnt", String.valueOf(c240m01c_all_cnt));	
		}		
		
		proc_info.set("o_sample_cnt", String.valueOf(sample_cnt));
		proc_info.set("o_done_cnt", String.valueOf(done_cnt));
		proc_info.set("o_fail_cnt", String.valueOf(fail_cnt));
		proc_info.set("joinSet_cnt", String.valueOf(joinSet.size()));
		proc_info.set("joinSet_arr", new ArrayList<String>(joinSet));
		proc_info.set("joinSet_skip_cnt", joinSet_skip_list.size());
		proc_info.set("joinSet_skip_list", joinSet_skip_list);
		result.set("proc_info", proc_info);
		
		return defaultResult( params, meta, result);
		
	}
	
	/**
	 * J-111-0622 修改為(不含個人授信額度以不動產十足擔保之授信案件)，所以不判斷R1 R2的不動產十足擔保，只判斷R3 R4非不動產的
	 * 配合「本行授信覆審作業須知」111.12.1修訂，請協助修改E-Loan系統企金與消金「授信覆審作業系統」如下修改內容：
	 * 2.授信覆審作業須知第十一條第九款不含個人授信額度以不動產十足擔保之授信案件
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult produce_R14(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);

		//檢查 custId 是否已存在工作底稿
		Set<String> existIdDupSet = new HashSet<String>();
		for(C241M01A c241m01a :retrialService.findC241M01A_C240M01A(meta.getMainId())){
			if(CrsUtil.isCaseP(c241m01a)){
				continue;
			}
			if(CrsUtil.isCaseS(c241m01a)){
				continue;
			}
			
			existIdDupSet.add(LMSUtil.getCustKey_len10custId(c241m01a.getCustId(), c241m01a.getDupNo()));
		}
		
		LinkedHashSet<String> idDupSet = new LinkedHashSet<String>();
		
		String brNo = meta.getBranchId();
		int samplingCnt_R14 = params.getInt("samplingCnt_R14", 0);
		int samplingRate_R14 = params.getInt("samplingRate_R14", 0);
		String[] choose_arr = null;
		if(Util.isNotEmpty(params.getString("idDup_arr"))){
			choose_arr = params.getString("idDup_arr").split("\\|"); 
		}
		int sample_cnt = 0;
		int c240m01c_all_cnt = 0;
		if(samplingCnt_R14==0 && samplingRate_R14==0 ){
			/*  $.ajax({
			    handler: "lms2401m01formhandler", action: "produce_R14",
			    data: {'samplingRate_R14': 0, idDup_arr:'idDupA|idDupB'},
			    success: function(json){  alert('ok');  }
			}); */
			if(choose_arr!=null && choose_arr.length>0){
				for(String idDup : choose_arr){
					idDupSet.add(idDup);
				}		
			}
			
			if(idDupSet.size()==0){
				throw new CapMessageException("請輸入抽樣資訊", getClass());
			}else{
				sample_cnt = idDupSet.size();	
			}			
		}else{
			if(samplingCnt_R14 > 0){
				sample_cnt = samplingCnt_R14;
			}else{
				if(samplingRate_R14 > 100){
					throw new CapMessageException("抽樣比率不可 > 100", getClass());
				}
				
				C240M01C c240m01c = retrialService.findC240M01C(meta.getMainId(), CrsUtil.R14);
				if(c240m01c!=null){
					c240m01c_all_cnt = c240m01c.getAllCnt();
					sample_cnt = samplingRate_R14 * c240m01c_all_cnt / 100;
				}
			}
			
			if(sample_cnt > 0){
				int buffer_cnt = 25; //為了避免 (1)有些當月已銷戶 (2)已存在工作底稿
				int fetch_size = sample_cnt + buffer_cnt;
				List<Map<String, Object>> list = misELF491CService.selByBrno_R14(brNo, fetch_size);
				for(Map<String, Object> map : list){
					String custId = Util.trim(MapUtils.getString(map, "ELF487_CUST_ID"));
					String dupNo = Util.trim(MapUtils.getString(map, "ELF487_DUP_NO"));
					idDupSet.add(LMSUtil.getCustKey_len10custId(custId, dupNo));
				}
			}
		}
		
		int done_cnt = 0;
		int fail_cnt = 0;
		Set<String> joinSet = LMSUtil.elm_onlyLeft(idDupSet, existIdDupSet);
		ArrayList<String> joinSet_skip_list = new ArrayList<String>();
		if(joinSet.size()==0){
			throw new CapMessageException("無符合抽樣資訊", getClass());
		}
		for(String idDup : idDupSet){
			if(joinSet.contains(idDup)){
				if(idDup.length()!=11){
					throw new CapMessageException("輸入["+idDup+"]格式錯誤", getClass()); 
				}
				
				String custId = CrsUtil.get_custId_from_custKey(idDup);
				String dupNo = CrsUtil.get_dupNo_from_custKey(idDup);		
						
				String cName = clsService.get0024_custName(custId, dupNo);	
				boolean r = lms2400Service.produce_R14(meta, custId, dupNo, cName);
				if(r){
					++done_cnt;
					if(true){
						//放在loop迴圈裡, 才會依「選擇的順序」去編號
						//若在loop外, 因為勾選N筆ID的 projectNo都空白, 會依「ID順序」去編號
						retrialService.genProjectNo_append(meta);
					}
				}else{
					++fail_cnt;
				}
			}else{
				joinSet_skip_list.add(idDup);
			}
			
			if(done_cnt >= sample_cnt){
				break;
			}
		}
		
		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});
		if(true){ //應更新 本次抽樣戶數為：N 筆
			String ruleNo = CrsUtil.R14;
			C240M01C c240m01c_R14 = retrialService.findC240M01C(meta.getMainId(), ruleNo);
			result_setObj_c240m01c_R14(result, meta.getMainId(), ruleNo, c240m01c_R14);
		}	
		
		CapAjaxFormResult proc_info = new CapAjaxFormResult();
		if(samplingCnt_R14>0){
			proc_info.set("i_sample_cnt", String.valueOf(samplingCnt_R14));
		}else{
			proc_info.set("i_sample_rate", String.valueOf(samplingRate_R14));
			proc_info.set("i_c240m01c_all_cnt", String.valueOf(c240m01c_all_cnt));	
		}		
		
		proc_info.set("o_sample_cnt", String.valueOf(sample_cnt));
		proc_info.set("o_done_cnt", String.valueOf(done_cnt));
		proc_info.set("o_fail_cnt", String.valueOf(fail_cnt));
		proc_info.set("joinSet_cnt", String.valueOf(joinSet.size()));
		proc_info.set("joinSet_arr", new ArrayList<String>(joinSet));
		proc_info.set("joinSet_skip_cnt", joinSet_skip_list.size());
		proc_info.set("joinSet_skip_list", joinSet_skip_list);
		result.set("proc_info", proc_info);
		
		return defaultResult( params, meta, result);
		
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult produce_projectCreditLoan(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);

		//檢查 custId 是否已存在工作底稿
		Set<String> existIdDupSet = new HashSet<String>();
		for(C241M01A c241m01a :retrialService.findC241M01A_C240M01A(meta.getMainId())){
			if(CrsUtil.isCaseP(c241m01a)){
				continue;
			}
			if(CrsUtil.isCaseS(c241m01a)){
				continue;
			}
			
			existIdDupSet.add(LMSUtil.getCustKey_len10custId(c241m01a.getCustId(), c241m01a.getDupNo()));
		}
		
		
		LinkedHashSet<String> idDupSet = new LinkedHashSet<String>();
		for(String idDup:params.getStringArray("choose_arr")){
			idDupSet.add(idDup);
		}
		
		int done_cnt = 0;
		int fail_cnt = 0;
		Set<String> joinSet = LMSUtil.elm_onlyLeft(idDupSet, existIdDupSet);
		String brNo = meta.getBranchId();
		for(String idDup : idDupSet){
			if(joinSet.contains(idDup)){
				String custId = CrsUtil.get_custId_from_custKey(idDup);
				String dupNo = CrsUtil.get_dupNo_from_custKey(idDup);		
						
				String cName = clsService.get0024_custName(custId, dupNo);
				boolean is_only_projectCreditLoan = lms2400Service.is_only_projectCreditLoan(brNo, custId, dupNo);
				result.set("is_only_projectCreditLoan_"+brNo+"_"+idDup, is_only_projectCreditLoan?"Y":"N"); //為檢視 is_only_projectCreditLoan 的分類結果
				boolean r = lms2400Service.produce_projectCreditLoan(meta, custId, dupNo, cName, is_only_projectCreditLoan);
				if(r){
					++done_cnt;
					if(true){
						//放在loop迴圈裡, 才會依「選擇的順序」去編號
						//若在loop外, 因為勾選N筆ID的 projectNo都空白, 會依「ID順序」去編號
						retrialService.genProjectNo_append(meta);
					}
				}else{
					++fail_cnt;
				}
			}
		}
		
		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});	
		if(true){ //應更新 本次抽樣戶數為：N 筆
			String ruleNo = CrsUtil.R13;
			C240M01C c240m01c_R_projectCreditLoan = retrialService.findC240M01C(meta.getMainId(), ruleNo);
			result_setObj_c240m01c_R_projectCreditLoan(result, meta.getMainId(), ruleNo, c240m01c_R_projectCreditLoan);
		}	
		return defaultResult( params, meta, result);
		
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult queryDocKindS_param_pa_ym(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		
		JSONArray jsonAraay = new JSONArray();
		List<String> ym_list = new ArrayList<String>();
		if(true){
			if(true){
				String pa_ym = CrsUtil.get_R96_base_ym(TWNDate.toAD(CapDate.getCurrentTimestamp()));
				ym_list.add(pa_ym);
			}
			//==========
			if(clsService.is_function_on_codetype("C240M01A_rm_previousYM")){
			}else{
				String pa_ym = CrsUtil.get_R96_base_ym(TWNDate.toAD(CapDate.getCurrentTimestamp()));
				String previous_pa_ym = CrsUtil.get_previous_R96_base_ym(pa_ym);
				ym_list.add(previous_pa_ym);
			}
		}
		for(String pa_ym : ym_list){
			JSONObject o = new JSONObject();
			o.put("k", pa_ym);
			o.put("v", StringUtils.join(CrsUtil.get_CLS180R18_period_byEndYM(pa_ym, 7), "~"));
			//---
			jsonAraay.add(o);
		}
		HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
		map.put("key", jsonAraay);
		result.set("selBox_pa_period", new CapAjaxFormResult(map));	
	
		return result;		
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult queryDocKindS_param_pa_trg_empNo(PageParameters params)
			throws CapException {
		String pa_ym = params.getString("pa_ym");
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC240M01A_oid(mainOid);
		}
		String brNo = Util.trim(meta==null?"":meta.getBranchId());
			
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		if(true){
			JSONArray jsonAraay = new JSONArray();
			if(true){
				LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
				map.put("R1", "新做前10大");
				map.put("R2", "新做跨區前10大");
				map.put("R3", "最近1年轉逾放前10大");
				map.put("R4", "首撥日2年內發生逾放");
				map.put("R5", "前次覆審異常");
				map.put("R6", "新承做無擔案件數最多");
				//==================================				
				List<ELF490B> elf490b_list = misELF490BService.selBy_dataym_flag_brNo(pa_ym, "3", brNo);
				TreeSet<String> pa_trg_set = new TreeSet<String>();
				for(ELF490B elf490b : elf490b_list){
					pa_trg_set.add(elf490b.getElf490b_rule_no());
				}
				
				for(String pa_trg: pa_trg_set){
					for(ELF490B elf490b : elf490b_list){
						String empNo = elf490b.getElf490b_emp_no();
						String desc = "";
						if(elf490b.getElf490b_count()==CrsUtil.get_ELF490B_cnt_for_top3()){
							desc = "【該項前三大】";
						}
						if(Util.equals(pa_trg, elf490b.getElf490b_rule_no())){
							JSONObject o = new JSONObject();
							
							o.put("k", pa_trg+"|"+empNo);
							o.put("v", map.get(pa_trg)+"("+empNo+")"+Util.trim(userInfoService.getUserName(empNo))+desc);
							//---
							jsonAraay.add(o);
						}
					}
				}				
			}
			
			if(jsonAraay.size()>0){
				HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
				map.put("key", jsonAraay);
				result.set("selBox_pa_trg_empNo", new CapAjaxFormResult(map));
			}else{
				result.set("selBox_pa_trg_empNo", "");
			}				
		}
					
		return result;		
	}
	
	public IResult saveR97(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String type = Util.trim(params.getString("type"));
		String sDate = Util.trim(params.getString("sDate"));		
		//---
		String lnf660_m_contract = Util.trim(params.getString("cntrNo"));
		String lnf660_loan_class = Util.trim(params.getString("lnf660_loan_class"));
		//String DP_AC = Util.trim(params.getString("DP_AC"));
		String comId_dupNo = Util.trim(params.getString("comId"));
		String comId = StringUtils.substring(comId_dupNo, 0,10);
		String comDupNo = StringUtils.substring(comId_dupNo, 10,11);
		String comName = Util.trim(params.getString("comName"));
		
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		
		Map<String, C241M01A> _existAllPMap = _existAllPMap(meta);
		Map<String, C241M01A> existMap = new HashMap<String, C241M01A>();
		LinkedHashSet<String> newkeyset = new LinkedHashSet<String>();		
		
		for(String cntrNoLcNo:params.getStringArray("cntrNoLcNo_arr")){
			if(_existAllPMap.containsKey(cntrNoLcNo)){
				existMap.put(cntrNoLcNo, _existAllPMap.get(cntrNoLcNo));	
			}
			newkeyset.add(cntrNoLcNo);			
		}
		
		if(newkeyset.size()>0){
			List<Map<String, Object>> list = chooseList(lms2400Service.escrowList(type, lnf660_loan_class
					, comId, comName, lnf660_m_contract, meta.getBranchId(), sDate ), newkeyset);
			
			lms2400Service.produce97(meta, list, existMap
					, lnf660_m_contract, lnf660_loan_class, comId, comDupNo, comName);
			
			retrialService.genProjectNo_append(meta);
		}		
		
		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});	
		return defaultResult( params, meta, result);
	}
	
	public IResult saveR97_byLcNo(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String lcNo = Util.trim(params.getString("escrowMenu4_lcNo"));
		//---
		String lnf660_m_contract = "";
		String lnf660_loan_class = "";
		String comId_dupNo = "";
		String comName = "";
		
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);

		Map<String, Object> dataMap = misdbBASEService.findEscrowDataByLcNo(lcNo, meta.getBranchId());
		if(MapUtils.isNotEmpty(dataMap)){
			String contract_H = Util.trim(MapUtils.getString(dataMap, "LNF030_CONTRACT"));
			String class_H = Util.trim(MapUtils.getString(dataMap, "LNF020_LOAN_CLASS"));
			String contract_B = Util.trim(MapUtils.getString(dataMap, "LNF020_CONTRACT_M"));
			String class_B = Util.trim(MapUtils.getString(dataMap, "LNF660_LOAN_CLASS"));
			if(Util.isNotEmpty(class_B)){
				lnf660_m_contract = contract_B;
				lnf660_loan_class = class_B;
			}else{
				lnf660_m_contract = contract_H;
				lnf660_loan_class = class_H;
			}
			
			for(Map<String, Object> lnf660_map : misMISLN20Service.findLNF660() ){
				if(Util.equals(MapUtils.getString(lnf660_map, "LNF660_M_CONTRACT"), lnf660_m_contract)){
					comId_dupNo = Util.trim(MapUtils.getString(lnf660_map, "LNF020_CUST_ID"));
					comName = Util.trim(MapUtils.getString(lnf660_map, "CNAME"));
				}
			}
		}else{
			throw new CapMessageException(prop_lms2401m01.getProperty("C241M01B.lcNo")+" "+lcNo+" 於"+meta.getBranchId()+"分行查無資料", getClass());
		}
		String comId = StringUtils.substring(comId_dupNo, 0,10);
		String comDupNo = StringUtils.substring(comId_dupNo, 10,11);
		
		Map<String, C241M01A> _existAllPMap = _existAllPMap(meta);
		Map<String, C241M01A> existMap = new HashMap<String, C241M01A>();
		LinkedHashSet<String> newkeyset = new LinkedHashSet<String>();		
		
		List<String> cntrNoLcNo_arr = new ArrayList<String>();
		if(true){
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("LNF020_CONTRACT", Util.trim(MapUtils.getString(dataMap, "LNF030_CONTRACT"))); 
			param.put("LNF034_LC_NO", lcNo);
			cntrNoLcNo_arr.add(CrsUtil.build_P_Key(param));
		}
		for(String cntrNoLcNo : cntrNoLcNo_arr){
			if(_existAllPMap.containsKey(cntrNoLcNo)){
				existMap.put(cntrNoLcNo, _existAllPMap.get(cntrNoLcNo));	
			}
			newkeyset.add(cntrNoLcNo);			
		}
		
		if(newkeyset.size()> 0){
			List<Map<String, Object>> list = lms2400Service.escrowList_lcNo(lnf660_loan_class
					, comId, comName, lnf660_m_contract, meta.getBranchId(), lcNo );
			
			lms2400Service.produce97(meta, list, existMap
					, lnf660_m_contract, lnf660_loan_class, comId, comDupNo, comName);
			
			retrialService.genProjectNo_append(meta);						
		}		
		
		LMSUtil.addMetaToResult(result, meta, new String[]{ "reQuantity"});	
		return defaultResult( params, meta, result);
	}
	
	private List<Map<String, Object>> chooseList(List<Map<String, Object>> list, LinkedHashSet<String> newset){
		List<Map<String, Object>> r = new ArrayList<Map<String, Object>>();
		for(Map<String, Object> m : list){
			
			String buildKey =  CrsUtil.build_P_Key(m);
			if(newset.contains(buildKey)){
				r.add(m);	
			}			
		}		
		return r;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult empty900toNew(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String newBrId = Util.trim(params.getString("newBrId"));
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		logger.trace("empty 900 to newBrId="+newBrId);
		if(Util.equals(meta.getOwnBrId(), "900") 
				&& Util.equals(meta.getBranchId(), "900") 
				&& CollectionUtils.isEmpty(retrialService.findC240M01B(meta)) ){
			
			meta.setBranchId(newBrId);
			retrialService.save(meta);
		}			
		return defaultResult( params, meta, result);
	}
	
	public IResult checkExist010(PageParameters params)
	throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String par_arr = Util.trim(params.getString("par_arr"));
		String unitNo = Util.trim(params.getString("unitNo"));
		List<String> existBrSkipList = new ArrayList<String>();
		try{
			String[] dataSplit = Util.trim(par_arr).split("\\|");
			if(dataSplit==null || dataSplit.length==0){
				//未輸入分行、資料年月
			}else{
				List<String> branchList = new ArrayList<String>();
				List<String> basedateList = new ArrayList<String>();
				for(String item_branch_basedata : dataSplit){
					String[] item = item_branch_basedata.split("\\^");
					if(item==null || item.length!=2){
						continue;
					}
					String branch = item[0];
					String basedate = item[1];
					
					if(CapDate.parseDate(basedate+"-01")==null){
						continue;
					}
					
					branchList.add(branch);	
					basedateList.add(basedate);				
				}
				
				int cnt = branchList.size();
				for(int i=0; i<cnt; i++){
					String branch = branchList.get(i);
					String basedate = basedateList.get(i);
					
					if(lms2400Service.isC240M01AInCompiling(unitNo, branch, CapDate.parseDate(CrsUtil.getDataEndDate(basedate)))){
						existBrSkipList.add(branch+"分行"+basedate);
					}	
				}
			}
				
		}catch(Exception e){
			logger.error(StrUtils.getStackTrace(e));
		}

		String msg = "";
		if(existBrSkipList.size()>0){
			msg = StringUtils.join(existBrSkipList, "、")+"已有編製中資料，不重複產生";
		}
		//---
		result.set("isMsg", (Util.isNotEmpty(msg)?"Y":"N"));
		result.set("msg", msg);
		return result;
	}
	
	public IResult query_custIdDupNo(PageParameters params)
	throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String custId = Util.trim(params.getString("q_custId"));
		String dupNo = Util.trim(params.getString("q_dupNo"));
		if(!(custId.length()==8||custId.length()==10)){
			throw new CapMessageException("統一編號長度錯誤", getClass());
		}
		if(Util.isEmpty(dupNo)){
			throw new CapMessageException("請輸入重複序號", getClass());
		}
		String idDup = LMSUtil.getCustKey_len10custId(custId, dupNo);
		String custName = clsService.get0024_custName(custId, dupNo);
		String elf490_data_ym_E = misELF490Service.findMaxDataYM();
		String elf490_data_ym_S = CrsUtil.elf490YM_from_adDate(CapDate.addMonth(CrsUtil.elf490YM_to_adDate_d(elf490_data_ym_E), -12)) ;
		List<ELF490> elf490_list = misELF490Service.findCustIdRecord(elf490_data_ym_S, elf490_data_ym_E, custId, dupNo);
		List<ELF491> elf491_list = misELF491Service.selByCustIdDupNo(custId, dupNo);
		List<MISLN20> lnf020_list = misMISLN20Service.findByCustId(custId, dupNo);
		List<Map<String, Object>> cms_list = eloandbcmsBASEService.getCrsCollCntrNoByCustIdDupNo(custId, dupNo);
		List<Map<String, Object>> elf591h_list = misELF591Service.sel_info_in_ELF591H(idDup, TWNDate.toAD(CapDate.addMonth(CrsUtil.elf490YM_to_adDate_d(elf490_data_ym_E), -12)));
		Map<String, TreeSet<String>> elf591_output_map = new TreeMap<String, TreeSet<String>>();
		List<Map<String, Object>> raw_elf487_list = misELF487Service.sel_by_brNo_idDup("", custId, dupNo);
		Map<String, List<Map<String, Object>>> group_elf487_list = new TreeMap<String, List<Map<String, Object>>>();
		
		HashMap<String, JSONArray> map490 = new HashMap<String, JSONArray>();
		HashMap<String, JSONArray> map491 = new HashMap<String, JSONArray>();
		HashMap<String, JSONArray> map020 = new HashMap<String, JSONArray>();
		HashMap<String, JSONArray> mapCMS = new HashMap<String, JSONArray>();
		HashMap<String, JSONArray> map591 = new HashMap<String, JSONArray>();
		HashMap<String, JSONArray> map487 = new HashMap<String, JSONArray>();
		
		{
			JSONArray jsonArray = new JSONArray();
			for(ELF490 item490 : elf490_list){
				
				JSONObject o = new JSONObject();
				o.put("data_ym", item490.getElf490_data_ym());
				o.put("branch", item490.getElf490_br_no());
				o.put("ruleNo", item490.getElf490_rule_no());
				o.put("ruleNoNew", item490.getElf490_rule_no_new()); 
				o.put("houseFg", item490.getElf490_house_fg()); 
				o.put("rmbinsFg", item490.getElf490_rmbins_fg()); 
				o.put("hsCreFg", item490.getElf490_hs_cre_fg()); 
				o.put("areaFg", item490.getElf490_area_fg()); 
				jsonArray.add(o);	
			}		
			map490.put("arr", jsonArray);
		}
		{
			HashMap<String, String> cntrNo_inLN020 = new HashMap<String, String>();
			for(MISLN20 lnf020: lnf020_list){
				String cntrNo = Util.trim(lnf020.getLnf020_contract());
				String lnf020_cancel_date = Util.trim(TWNDate.toAD(lnf020.getLnf020_cancel_date()));
				cntrNo_inLN020.put(cntrNo, lnf020_cancel_date);
			}
			
			JSONArray jsonArray = new JSONArray();
			for(Map<String, Object> cmsRow : cms_list){
				String cntrNo = Util.trim(cmsRow.get("CNTRNO")); 
				//---
				JSONObject o = new JSONObject();
				o.put("flag", cntrNo_inLN020.containsKey(cntrNo)?(Util.isEmpty(cntrNo_inLN020.get(cntrNo))?"":"X"):"X");
				o.put("typCd", Util.trim(cmsRow.get("TYPCD")));
				o.put("branch", Util.trim(cmsRow.get("BRANCH")));
				o.put("custId", Util.trim(cmsRow.get("CUSTID")));
				o.put("dupNo", Util.trim(cmsRow.get("DUPNO")));
				o.put("collNo", Util.trim(cmsRow.get("COLLNO")));
				o.put("docStatus", Util.trim(cmsRow.get("DOCSTATUS")));
				o.put("cntrNo", cntrNo);
				jsonArray.add(o);	
			}		
			mapCMS.put("arr", jsonArray);
		}
		{
			JSONArray jsonArray = new JSONArray();
			for(ELF491 item491 : elf491_list){
				
				JSONObject o = new JSONObject();
				o.put("branch", item491.getElf491_branch());
				o.put("lrDate", Util.trim(TWNDate.toAD(item491.getElf491_lrdate())));
				o.put("lastRealDt", Util.trim(TWNDate.toAD(item491.getElf491_lastRealDt())));
				o.put("crDate", Util.trim(TWNDate.toAD(item491.getElf491_crdate())));
				o.put("newflag", Util.trim(item491.getElf491_newflag()));
				o.put("nckdflag", Util.trim(item491.getElf491_nckdflag()));
				o.put("nckddate", Util.trim(TWNDate.toAD(item491.getElf491_nckddate())));
				o.put("nckdmemo", Util.trim(item491.getElf491_nckdmemo()));
				o.put("remomo", Util.trim(item491.getElf491_remomo()));
				jsonArray.add(o);	
			}		
			map491.put("arr", jsonArray);
		}
		{
			JSONArray jsonArray = new JSONArray();
			//因為 LNF020_FACT_TYPE  會有30,31
			//可能 1 個額度序號出現多次
			Set<String> existSet = new HashSet<String>();
			for(MISLN20 lnf020: lnf020_list){
				
				JSONObject o = new JSONObject();
				String cntrNo = Util.trim(lnf020.getLnf020_contract());
				String lnf020_cancel_date = Util.trim(TWNDate.toAD(lnf020.getLnf020_cancel_date()));
				
				String _build_key = cntrNo+"-"+lnf020_cancel_date;
				if(existSet.contains(_build_key)){
					continue;
				}else{
					existSet.add(_build_key);
				}
				
				JSONArray jsonArray030 = new JSONArray();
				for(MISLN30 misLN30: misLNF030Service.selByCntrNo(cntrNo)){					
					JSONObject obj030 = new JSONObject();								
					obj030.put("LNF030_LOAN_NO", Util.trim(misLN30.getLnf030_loan_no()));
					obj030.put("LNF030_CANCEL_DATE", Util.trim(TWNDate.toAD(misLN30.getLnf030_cancel_date())));
					obj030.put("LNF030_CHARC_CODE", Util.trim(misLN30.getLnf030_charc_code()));
					obj030.put("LNF030_LOAN_CLASS", Util.trim(misLN30.getLnf030_loan_class()));
					obj030.put("LNF030_RESIDENTIAL", Util.trim(misLN30.getLnf030_residential()));
					obj030.put("LNF030_OPEN_DATE", Util.trim(TWNDate.toAD(misLN30.getLnf030_open_date())));
					obj030.put("LNF030_LOAN_DATE", Util.trim(TWNDate.toAD(misLN30.getLnf030_loan_date())));
					jsonArray030.add(obj030);	
				}
				
				o.put("LNF020_CONTRACT", cntrNo);
				o.put("LNF020_CANCEL_DATE", lnf020_cancel_date);
				o.put("LNF020_CREATE_DT", Util.trim(TWNDate.toAD(lnf020.getLnf020_create_dt())));
				o.put("LNF020_REVOLVE", Util.trim(lnf020.getLnf020_revolve()));
				o.put("LNF020_BEG_DATE", Util.trim(TWNDate.toAD(lnf020.getLnf020_beg_date())));
				o.put("LNF020_END_DATE", Util.trim(TWNDate.toAD(lnf020.getLnf020_end_date())));
				o.put("LNF020_DURATION_BG", Util.trim(TWNDate.toAD(lnf020.getLnf020_duration_bg())));
				o.put("LNF020_DURATION_ED", Util.trim(TWNDate.toAD(lnf020.getLnf020_duration_ed())));
				
				o.put("LNF030_DATA", jsonArray030);
				jsonArray.add(o);	
			}		
			map020.put("arr", jsonArray);
		}
		if(true){
			for(Map<String, Object> item591 : elf591h_list){
				String data_source = Util.trim(MapUtils.getString(item591, "ELF591H_DATA_SOURCE"));
				String cntrNo = Util.trim(MapUtils.getString(item591, "LNF030_CONTRACT"));
				if(!elf591_output_map.containsKey(cntrNo)){
					elf591_output_map.put(cntrNo, new TreeSet<String>());
				}
				elf591_output_map.get(cntrNo).add(data_source);
			}

			JSONArray jsonArray = new JSONArray();
			HashMap<String, String> cntrNo_cancelDate_inLN020 = new HashMap<String, String>();
			HashMap<String, String> cntrNo_lrDate = new HashMap<String, String>();
			for(MISLN20 lnf020: lnf020_list){
				String cntrNo = Util.trim(lnf020.getLnf020_contract());
				String lnf020_cancel_date = Util.trim(TWNDate.toAD(lnf020.getLnf020_cancel_date()));
				cntrNo_cancelDate_inLN020.put(cntrNo, lnf020_cancel_date);
				//~~~~~~
				if(!cntrNo_lrDate.containsKey(cntrNo)){
					Date lr_date = misELF492Service.selMaxLrDateByBrNoCustIdDupNoCntrNo(CrsUtil.getBrNoFromLNF020_CONTRACT(cntrNo), custId, dupNo, cntrNo);
					cntrNo_lrDate.put(cntrNo, Util.trim(TWNDate.toAD(lr_date)));
				}
			}
			
			for(String cntrNo : elf591_output_map.keySet()){
				String rptNo = StringUtils.join(elf591_output_map.get(cntrNo), "、");
				
				JSONObject o = new JSONObject();
				o.put("cntrNo", cntrNo);
				o.put("rptNo", rptNo);
				o.put("cancelDate", MapUtils.getString(cntrNo_cancelDate_inLN020, cntrNo, ""));
				o.put("lrDate", MapUtils.getString(cntrNo_lrDate, cntrNo, ""));
				jsonArray.add(o);	
			}
			map591.put("arr", jsonArray);
		}
		if(true){			 
			for(Map<String, Object> map : raw_elf487_list){
				String brNo = MapUtils.getString(map, "ELF487_BR_NO");
				if(!group_elf487_list.containsKey(brNo)){
					group_elf487_list.put(brNo, new ArrayList<Map<String, Object>>());
				}
				group_elf487_list.get(brNo).add(map);
			}

			JSONArray jsonArray = new JSONArray();
			for(String brNo : group_elf487_list.keySet()){

				boolean have_ELF487_HOUSE_FG = false;
				boolean have_ELF487_RMBINS_FG = false;
				boolean have_ELF487_HS_CRE_FG = false;
				for(Map<String, Object> map : group_elf487_list.get(brNo)){
					if(Util.equals("Y", MapUtils.getObject(map, "ELF487_HOUSE_FG"))){
						have_ELF487_HOUSE_FG = true;
					}
					if(Util.equals("Y", MapUtils.getObject(map, "ELF487_RMBINS_FG"))){
						have_ELF487_RMBINS_FG = true;
					}
					if(Util.equals("Y", MapUtils.getObject(map, "ELF487_HS_CRE_FG"))){
						have_ELF487_HS_CRE_FG = true;
					}
				}
				JSONObject o = new JSONObject();
				o.put("branch", brNo);
				o.put("have_ELF487_HOUSE_FG", have_ELF487_HOUSE_FG?"Y":"");
				o.put("have_ELF487_RMBINS_FG", have_ELF487_RMBINS_FG?"Y":"");
				o.put("have_ELF487_HS_CRE_FG", have_ELF487_HS_CRE_FG?"Y":"");
				jsonArray.add(o);	
			}		
			map487.put("arr", jsonArray);
		}
		result.set("custName", custName);
		result.set("custId", custId);
		result.set("dupNo", dupNo);
		
		result.set("elf490_data_ym_S", elf490_data_ym_S);
		result.set("elf490_data_ym_E", elf490_data_ym_E);
		result.set("elf490_size", elf490_list.size());
		result.set("elf490_data", new CapAjaxFormResult(map490));
		result.set("cms_size", cms_list.size());
		result.set("cms_data", new CapAjaxFormResult(mapCMS));
		result.set("elf491_size", elf491_list.size());
		result.set("elf491_data", new CapAjaxFormResult(map491));
		result.set("lnf020_size", lnf020_list.size());
		result.set("lnf020_data", new CapAjaxFormResult(map020));
		result.set("elf591_size", elf591_output_map.size());
		result.set("elf591_data", new CapAjaxFormResult(map591));
		result.set("elf487_size", group_elf487_list.size());
		result.set("elf487_data", new CapAjaxFormResult(map487));
		return result;
	}
	
	public IResult query_cntrNo(PageParameters params)
	throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String cntrNo = Util.trim(params.getString("q_cntrNo"));
		if(cntrNo.length()<12){
			throw new CapMessageException("額度序號長度為12", getClass());
		}
		
		List<Map<String, Object>> coll_list = eloandbcmsBASEService.getCrsCollInfoByCntrNo(cntrNo);
		HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
		{
			JSONArray jsonArray = new JSONArray();
			for(Map<String, Object> coll : coll_list){
				
				JSONObject o = new JSONObject();
				o.putAll(coll);
				jsonArray.add(o);	
			}		
			map.put("arr", jsonArray);
		}
		
		result.set("cntrNo", cntrNo);
		result.set("_data", new CapAjaxFormResult(map));
		result.set("_size", coll_list.size());
		return result;
	}
	
	public IResult test_calc(PageParameters params)
	throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String tc_flag = Util.trim(params.getString("tc_flag"));
		String tc_brNo = Util.trim(params.getString("tc_brNo"));
		String tc_custId = Util.trim(params.getString("tc_custId"));
		String tc_dupNo = Util.trim(params.getString("tc_dupNo"));
		String tc_rule_no = Util.trim(params.getString("tc_rule_no"));
		String tc_rule_no_new = Util.trim(params.getString("tc_rule_no_new"));
		if(Util.isEmpty(tc_custId)){
			throw new CapMessageException("empty custId", getClass());
		}
		Date sysDate = CapDate.parseDate(CapDate
				.getCurrentDate(CapDate.DEFAULT_DATE_FORMAT));
		
		CrsRuleVO crsRuleVO = new CrsRuleVO(sysDate, tc_brNo, tc_custId, tc_dupNo);
		boolean use_491 = false;
		if(Util.isEmpty(tc_rule_no) && Util.isEmpty(tc_rule_no_new) ){
			ELF491 elf491 = misELF491Service.findByPk(crsRuleVO.getBrNo(), crsRuleVO.getCustId(), crsRuleVO.getDupNo());
			if(elf491!=null){
				if (Util.equals(CrsUtil.ELF491_NEWFLAG_Y, elf491.getElf491_newflag())) {
					tc_rule_no_new = elf491.getElf491_remomo();
				} else {
					tc_rule_no = elf491.getElf491_remomo();
				}	
				use_491 = true;	
			}			
		}
		
		if(Util.isEmpty(tc_rule_no) && Util.isEmpty(tc_rule_no_new) ){
			throw new CapMessageException("rule_no、rule_no_new同時空白,且ELF491抓不到資料", getClass());
		}
		
		lms2400Service.test_calc(tc_flag, crsRuleVO, tc_rule_no_new, tc_rule_no);
		result.set("rule_decided", crsRuleVO.rule_decided());
		result.set("rule_mode", tc_flag);
		result.set("use_rule", "from "+(use_491?"491":"ui")+": old["+tc_rule_no+"]、new["+tc_rule_no_new+"]");
		result.set("decideInfo", crsRuleVO.getDecideInfo());
		result.set("reasonDesc", crsRuleVO.getC241M01Z_reasonDesc());
		return result;
	}
	
	public IResult test_crDate(PageParameters params)
	throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		
		String uc_keys = Util.trim(params.getString("uc_keys"));
		String[] uckeys = StringUtils.split(uc_keys, "-");
		String uc_brNo = Util.trim(uckeys[0]);
		String uc_custId = Util.trim(uckeys[1]);
		String uc_dupNo = Util.trim(uckeys[2]);
		String uc_crDate = Util.trim(params.getString("uc_crDate"));
		if(Util.isEmpty(uc_custId)){
			throw new CapMessageException("empty custId", getClass());
		}
		
		ELF491 elf491 = misELF491Service.findByPk(uc_brNo, uc_custId, uc_dupNo);
		String msg = "";
		if(elf491!=null){
			String exist_crDate = Util.trim(TWNDate.toAD(elf491.getElf491_crdate()));
			elf491.setElf491_crdate(CapDate.parseDate(uc_crDate));
			//====
			List<ELF491> elf491_list = new ArrayList<ELF491>();
			elf491_list.add(elf491);
			retrialService.upELF491_DelThenInsert(elf491_list);
			msg = uc_keys+"["+exist_crDate+"]to["+uc_crDate+"]";
		}else{
			msg = "find none";	
		}			
		
		result.set("msg", msg);
		return result;
	}
	/*
	$.ajax({ handler: "lms2401m01formhandler", action: "query_cState",
	    data: {'uc_keys':'brNo-custId-0'},
	    success: function(json){}
	}); */
	public IResult query_cState(PageParameters params)
	throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();		
		
		String uc_keys = Util.trim(params.getString("uc_keys"));
		String[] uckeys = StringUtils.split(uc_keys, "-");
		String uc_brNo = Util.trim(uckeys[0]);
		String uc_custId = Util.trim(uckeys[1]);
		String uc_dupNo = Util.trim(uckeys[2]);
		
		if(Util.isEmpty(uc_custId)){
			throw new CapMessageException("empty custId", getClass());
		}
		
		String[] lnStatusArr = retrialService.gfnCTL_Import_LNF022(uc_brNo, uc_custId, uc_dupNo, null);
		String cState = lnStatusArr[0];
		
		result.set("cState", cState);
		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult oid_end_to_01A(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		
		List<String> c241m01a_list = new ArrayList<String>();
		
		if(true){
			String[] oid_arr = params.getStringArray("oid_arr");
			List<String> oid_list = new ArrayList<String>();
			for(String oid: oid_arr){
				oid_list.add(oid);
			}
			for(C241M01A c241m01a : retrialService.findC241M01A_oid(oid_list)){
				if(Util.equals(RetrialDocStatusEnum.已覆核已核定.getCode(), c241m01a.getDocStatus())){
					c241m01a_list.add(c241m01a.getOid());
				}
			}
		}
		
		if(CollectionUtils.isEmpty(c241m01a_list)){
			throw new CapMessageException(prop_lms2401m01.getProperty("ui_lms2401.msg11"), getClass());
		}		
		result.set("choseOid", c241m01a_list);
			
		return defaultResult( params, meta, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult update_ptMgrId(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		String ptMgrId = Util.trim(params.getString("ptMgrId"));
		ArrayList<String> oid_list = new ArrayList<String>();
		
		if(Util.equals("Y", params.getString("allOidFlag"))){			
			for(C241M01A c241m01a : retrialService.findC241M01A_C240M01A(meta.getMainId())){
				oid_list.add(c241m01a.getOid());
			}
			
		}else{
			String[] oid_arr = params.getStringArray("oid_arr");
			for(String oid: oid_arr){
				oid_list.add(oid);
			}
		}
		
		if(CollectionUtils.isNotEmpty(oid_list)){
			lms2400Service.update_ptMgrId(meta, oid_list, ptMgrId);	
		}		
						
		return result;		
	}

	@DomainAuth(AuthType.Modify)
	public IResult update_docFmt(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		ArrayList<String> oid_list = new ArrayList<String>();
		
		if(Util.equals("Y", params.getString("allOidFlag"))){			
			for(C241M01A c241m01a : retrialService.findC241M01A_C240M01A(meta.getMainId())){
				oid_list.add(c241m01a.getOid());
			}
			
		}else{
			String[] oid_arr = params.getStringArray("oid_arr");
			for(String oid: oid_arr){
				oid_list.add(oid);
			}
		}
		
		int updateCnt = 0;
		if(CollectionUtils.isNotEmpty(oid_list)){
			updateCnt = lms2400Service.update_docFmt(meta, oid_list);	
		}		
		result.set("updateCnt", updateCnt);				
		return result;		
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getSignList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getStringArray(EloanConstants.MAIN_OID)[0];
		String signRole = Util.trim(params.getString("signRole"));
		C240M01A c240m01a = retrialService.findC240M01A_oid(mainOid);
		String chooseUnitNo = "";
		//=====================
		if(c240m01a!=null){
			if (Util.equals("A", signRole)) {
				//受檢單位
				chooseUnitNo = c240m01a.getBranchId();
			}else if (Util.equals("B", signRole)) {
				//覆審單位
				chooseUnitNo = c240m01a.getOwnBrId();
			}	
		}
		
		SignEnum[] signs_l4 = { SignEnum.甲級主管, SignEnum.乙級主管, SignEnum.首長,
				SignEnum.單位主管 };
		SignEnum[] signs_l5 = { SignEnum.甲級主管, SignEnum.首長, SignEnum.單位主管 };
		Map<String, String> l4_list = userInfoService.findByBrnoAndSignId(chooseUnitNo, signs_l4);
		Map<String, String> l5_list = userInfoService.findByBrnoAndSignId(chooseUnitNo, signs_l5);
		
		IBranch ibranch = branchService.getBranch(chooseUnitNo);
		if (ibranch != null) {
			String l5Id = Util.trim(ibranch.getBrnMgr());
			l5_list.put(l5Id, Util.trim(userInfoService.getUserName(l5Id)));
		} 
		result.set("chooseUnitNo", chooseUnitNo);
		result.set("l4_list", new CapAjaxFormResult(l4_list));
		result.set("l5_list", new CapAjaxFormResult(l5_list));
		
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignList(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		
		String key = "saveSignFlag";		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(key, "N");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String[] mainOidArray = params.getStringArray("mainOidArray");
		String signRole = Util.trim(params.getString("signRole"));
		List<String> l4List = _staffStr(params.getString("l4Arr"));
		List<String> l5List = _staffStr(params.getString("l5Arr"));
		
		C241M01A meta = null;
		for(int i=0; i<mainOidArray.length; i++){
			meta = retrialService.findC241M01A_oid(mainOidArray[i]);
			//=====================
			String branchType = "";
			if (Util.equals("A", signRole)) {
				//受檢單位
				branchType = "1";
			}else if (Util.equals("B", signRole)) {
				//覆審單位
				branchType = "2";
			}
			retrialService.saveC241M01E_L1L4L5(meta, branchType, user, l4List, l5List);
			//==========
		}
		
		_signInfo(result, meta);
		result.set(key, "Y");
		return result;
	}
	
	private List<String> _staffStr(String src){
		List<String> list = new ArrayList<String>();
		for(String raw_staffNo : Util.trim(src).split("\\|")){
			String s = Util.trim(raw_staffNo);
			if(Util.isEmpty(s)){
				continue;
			}
			if(list.contains(s)){
				continue;
			}
			list.add(s);
		}
		return list;		
	}
	
	private void _signInfo(CapAjaxFormResult result, C241M01A meta){
		
		List<C241M01E> list = retrialService.findC241M01E_c241m01a(meta);
		String branch_L1 = _filter_branchType_staffJob(list, "1", UtilConstants.STAFFJOB.經辦L1);
		String branch_L4 = _filter_branchType_staffJob(list, "1", UtilConstants.STAFFJOB.執行覆核主管L4);
		String branch_L5 = _filter_branchType_staffJob(list, "1", UtilConstants.STAFFJOB.單位授權主管L5);
		
		String area_L1 = _filter_branchType_staffJob(list, "2", UtilConstants.STAFFJOB.經辦L1);
		String area_L4 = _filter_branchType_staffJob(list, "2", UtilConstants.STAFFJOB.執行覆核主管L4);
		String area_L5 = _filter_branchType_staffJob(list, "2", UtilConstants.STAFFJOB.單位授權主管L5);
		
		//受檢		
		result.set("appraiser1", branch_L1);
		result.set("reCheck1", branch_L4);
		result.set("manager1", branch_L5);
		//覆審組
		result.set("appraiser2", area_L1);
		result.set("reCheck2", area_L4);
		result.set("manager2", area_L5);
		
	}
	
	private String _filter_branchType_staffJob(List<C241M01E> list, String branchType, String staffJob){
		List<C241M01E> filter = retrialService.findC241M01E_byBranchTypeStaffJob(list, branchType, staffJob);		
		List<String> staffNoStr = new ArrayList<String>();
		if(CollectionUtils.isNotEmpty(filter)){
			for(C241M01E c241m01e:filter){
				staffNoStr.add( Util.trim(c241m01e.getStaffNo())+" "+Util.trim(c241m01e.getStaffName())  );
			}
		}
		
		return StringUtils.join(staffNoStr, "&nbsp;&nbsp;、<br/>");
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult rule95_1_summary_sync_cntrNo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		/*
			J-109-0344, 申請單號 (109) 第 2561 號, 將第1批名單寫入婉卻檔
			J-109-0372, 申請單號 (109) 第 2669 號, 109.8.25兆銀總授審字第1090045985號函,疑似人頭戶專案覆審(1)第一年全面辦理專案覆審 (2)嗣後逐年按受檢追蹤對象之10%範圍內
		*/
		String steps = "1";
		if(true){
			String batchNo = "J-109-0344(109)2561";
			String postDocNoByBatchNo = "兆銀總授審字第1090045985號函";
			Date postDateByBatchNo = CapDate.parseDate("2020-08-25");
			Date firstCrDateByBatchNo = CapDate.parseDate("2020-12-31");
			
			lms2401Service.update491_J_109_0372_byBatchNo(steps, batchNo, postDocNoByBatchNo, postDateByBatchNo, firstCrDateByBatchNo);
		}
		if(true){
			String batchNo = "J-109-0344(109)2561_20201021";
			String postDocNoByBatchNo = "兆銀總授審字第1090057721號函";
			Date postDateByBatchNo = CapDate.parseDate("2020-10-21");
			Date firstCrDateByBatchNo = CapDate.parseDate("2020-12-31");
			
			lms2401Service.update491_J_109_0372_byBatchNo(steps, batchNo, postDocNoByBatchNo, postDateByBatchNo, firstCrDateByBatchNo);
		}
		if(true){
			String batchNo = "J-109-0344(109)2561_20201103";
			String postDocNoByBatchNo = "兆銀總授審字第1090060861號函";
			Date postDateByBatchNo = CapDate.parseDate("2020-11-03");
			Date firstCrDateByBatchNo = CapDate.parseDate("2020-12-31");
			
			lms2401Service.update491_J_109_0372_byBatchNo(steps, batchNo, postDocNoByBatchNo, postDateByBatchNo, firstCrDateByBatchNo);
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult rule95_1_prepareData(PageParameters params)
			throws CapException {
		/*
		 	把需「控管名單」	在 e-Loan 的資料 	select codeType, count(*) from com.bCodeType where codeType like 'J-109-0344%' group by codeType
		 	
		 	SLMS-00111
			step 1) 寫入 LNF07A（由 cntrNo 找到對應的 custId）=> select LNF07A_KEY_1, count(*) from ln.lnf07a where LNF07A_KEY_1 like 'J-109-0344(109)2561%' group by LNF07A_KEY_1
			step 2) 寫入 mis.LnunId
			step 3) 寫入ELF491 
			
			若 LNF07A 由PROD 倒檔至 SIT , 導致 SIT 的ID對應不起來，重執行 SLMS-00111 且指定 steps='1'
			第1批名單
			● URL=http://127.0.0.1:9081/lms-web/app/scheduler,TIMEOUT=3000,SERVICEID=crsBatchServiceImpl,REQUEST={act:'update491_J_109_0372_byBatchNo', steps:'1', batchNo:'J-109-0344(109)2561',postDocNoByBatchNo:'兆銀總授審字第1090045985號函',postDateByBatchNo:'2020-08-25', firstCrDateByBatchNo:'2020-12-31'}
			第2批名單
			● URL=http://127.0.0.1:9081/lms-web/app/scheduler,TIMEOUT=3000,SERVICEID=crsBatchServiceImpl,REQUEST={act:'update491_J_109_0372_byBatchNo', steps:'1', batchNo:'J-109-0344(109)2561_20201021',postDocNoByBatchNo:'兆銀總授審字第1090057721號函',postDateByBatchNo:'2020-10-21', firstCrDateByBatchNo:'2020-12-31'}
			第3批名單
			● URL=http://127.0.0.1:9081/lms-web/app/scheduler,TIMEOUT=3000,SERVICEID=crsBatchServiceImpl,REQUEST={act:'update491_J_109_0372_byBatchNo', steps:'1', batchNo:'J-109-0344(109)2561_20201103',postDocNoByBatchNo:'兆銀總授審字第1090060861號函',postDateByBatchNo:'2020-11-03', firstCrDateByBatchNo:'2020-12-31'}
		 */
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A c240m01a = retrialService.findC240M01A_oid(mainOid);
		String brNo = "";
		Date retrial_dt = null;
		//=====================		
		if(c240m01a!=null){
			brNo = c240m01a.getBranchId();
			retrial_dt = c240m01a.getExpectedRetrialDate();
		}
		int rule95_allCnt = misELF491CService.countRule95_all(brNo);		
		if(rule95_allCnt>0 && CrsUtil.isNull_or_ZeroDate(retrial_dt)){
			throw new CapMessageException(prop_lms2405m01.getProperty("err.noDate"), getClass());
		}	
		String ruleNo = CrsUtil.R95_1;
		result.set("hasData", (rule95_allCnt > 0)?"Y":"N");
		CapAjaxFormResult rtn = new CapAjaxFormResult();
		if(true){
			//inject_c240m01c_to_CapAjaxFormResult(rtn, c240m01c);
			rtn.set(C240_TOTRETRIALCNT, retrialService.count_retrialKind_in_c240m01a(c240m01a.getMainId(), ruleNo));	
		}		
		result.set("rtn", rtn);	
		
		return result;
	}
		
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult prepare_projectCreditLoan_param(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C240M01A c240m01a = retrialService.findC240M01A_oid(mainOid);
		String brNo = "";
		if(c240m01a!=null){
			brNo = c240m01a.getBranchId();
		}
		
		Date nowDate = CapDate.getCurrentTimestamp();
		result.set("projectCreditLoanSDate", TWNDate.toAD(CrsUtil.get_R13_begDate(nowDate)));
		result.set("projectCreditLoanEDate", TWNDate.toAD(nowDate));
		if(true){
			String ruleNo = CrsUtil.R13;
			C240M01C c240m01c = lms2401Service.rule_projectCreditLoan_summary(ruleNo, brNo, nowDate);
			CapAjaxFormResult rtn = new CapAjaxFormResult();
			if(true){
				inject_c240m01c_to_CapAjaxFormResult(rtn, c240m01c);
				rtn.set(C240_TOTRETRIALCNT, retrialService.count_retrialKind_in_c240m01a(c240m01a.getMainId(), ruleNo));
			}
			result.set("rtn", rtn);	
		}		
		return result;
	}

    @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
    public IResult updateRetrialDataJ1130519(PageParameters params)
            throws CapException {
        CapAjaxFormResult result = new CapAjaxFormResult();

        int rows = retrialService.updateRetrialDataJ1130519();

        result.set("rows", rows);

        return result;
    }

	private void inject_c240m01c_to_CapAjaxFormResult(CapAjaxFormResult result, C240M01C c240m01c){
		result.set("allBegDate", Util.trim(TWNDate.toAD(c240m01c.getAllBegDate())));
		result.set("allEndDate", Util.trim(TWNDate.toAD(c240m01c.getAllEndDate())));
		result.set("allCnt", String.valueOf(c240m01c.getAllCnt()));
		result.set("activeCnt", String.valueOf(c240m01c.getActiveCnt()));	
		result.set("doneBegDate", Util.trim(TWNDate.toAD(c240m01c.getDoneBegDate())));
		result.set("doneEndDate", Util.trim(TWNDate.toAD(c240m01c.getDoneEndDate())));
		result.set("doneCnt", String.valueOf(c240m01c.getDoneCnt()));	
		result.set("doneRate", LMSUtil.pretty_numStr(c240m01c.getDoneRate()));
	}	
}
