/* 
 * L120M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.model;

import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 簽報書額度明細關聯檔 **/
@NamedEntityGraph(name = "L120M01C-entity-graph", attributeNodes = { @NamedAttributeNode("l140m01a"), @NamedAttributeNode("l120m01a") })
@Entity
@Table(name = "L120M01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "itemType", "refMainId" }))
public class L120M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumn(name = "REFMAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false)
	private L140M01A l140m01a;

	public L140M01A getL140m01a() {
		return l140m01a;
	}

	public void setL140m01a(L140M01A l140m01a) {
		this.l140m01a = l140m01a;
	}
	
	
	
	/**
	 * JOIN條件 L120A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l120m01c", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L120A01A> l120a01a;

	public Set<L120A01A> getL120a01a() {
		return l120a01a;
	}

	public void setL120a01a(Set<L120A01A> l120a01a) {
		this.l120a01a = l120a01a;
	}
	
	/**
	 * JOIN條件 L120M01A．關聯檔
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false)
	private L120M01A l120m01a;

	public L120M01A getL120m01a() {
		return l120m01a;
	}

	public void setL120m01a(L120M01A l120m01a) {
		this.l120m01a = l120m01a;
	}
	

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 額度明細種類
	 * <p/>
	 * 1額度明細表<br/>
	 * 2額度批覆表<br/>
	 * 3母行法人提案意見
	 */
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String itemType;

	/**
	 * 額度明細文件編號
	 * <p/>
	 * L140M01A_mainId
	 */
	@Column(name = "REFMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String refMainId;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得額度明細種類
	 * <p/>
	 * 1額度明細表<br/>
	 * 2額度批覆表<br/>
	 * 3母行法人提案意見
	 */
	public String getItemType() {
		return this.itemType;
	}

	/**
	 * 設定額度明細種類
	 * <p/>
	 * 1額度明細表<br/>
	 * 2額度批覆表<br/>
	 * 3母行法人提案意見
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/**
	 * 取得額度明細文件編號
	 * <p/>
	 * L140M01A_mainId
	 */
	public String getRefMainId() {
		return this.refMainId;
	}

	/**
	 * 設定額度明細文件編號
	 * <p/>
	 * L140M01A_mainId
	 **/
	public void setRefMainId(String value) {
		this.refMainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
