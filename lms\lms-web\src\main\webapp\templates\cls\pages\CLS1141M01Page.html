<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:extend>
            <script type="text/javascript" src="pagejs/cls/CLSM01APage.js?ver=20241204"></script>
            <script type="text/javascript" src="pagejs/base/LMSM02Page.js?20230927"></script>
            <script type="text/javascript" src="pagejs/base/LMSM02BPage.js?20220301"></script>
            <script type="text/javascript" src="pagejs/cls/CLSM03Page.js"></script>
            <script type="text/javascript" src="pagejs/cls/CLS1141M01Page.js?ver=20241223"></script>
            <div class="button-menu funcContainer" id="buttonPanel">
                <!--海外分行--><!--海外分行 編製中--><!-- 特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)授權外編製中顯示按鈕 --><span id="hideSpecialBtn">
                    <button type="button" id="btnSLogIN">
                        <span class="ui-icon ui-icon-tag"></span>
                        <wicket:message key="button.login">登錄</wicket:message>
                    </button>
                    <span id="showAfterSignBtn" class="hide">
                    	<wicket:enclosure><span wicket:id="_btnSAVE" />
                    		<button type="button" id="btnSave">
                            	<span class="ui-icon ui-icon-jcs-04" /><wicket:message key="button.save">儲存</wicket:message>
                        	</button>
                		</wicket:enclosure>                        
                    </span></span>
                <!-- 特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)授權外已會簽(會簽後修改)顯示按鈕 --><span id="hideSpecialBtn2">
                    <button type="button" id="btnLogeIN">
                        <span class="ui-icon ui-icon-tag"></span>
                        <wicket:message key="button.login">登錄</wicket:message>
                    </button>
                    <button type="button" id="btnSendCase">
                        <span class="ui-icon ui-icon-jcs-15"></span>
                        <wicket:message key="button.sendcase">提會</wicket:message>
                    </button>
                    <button type="button" id="btnSend">
                        <span class="ui-icon ui-icon-jcs-02" /><wicket:message key="button.send">呈主管覆核</wicket:message>
                    </button>
                    <span id="sAfterSign">
                        <button type="button" id="btnAfterSign">
                            <span class="ui-icon ui-icon-jcs-101"></span>會簽/審查後修改
                        </button>
                    </span><span id="sAfterSign2" class="hide">
                        <button type="button" id="btnAfterSign2">
                            <span class="ui-icon ui-icon-jcs-101"></span>回上一步
                        </button>
                    </span>
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.OpenLmsCase">開啟授信報案考核表</wicket:message>
                    </button>
                </span>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_EDITING" />
                    <button type="button" id="btnSave">
                        <span class="ui-icon ui-icon-jcs-04" /><wicket:message key="button.save">儲存</wicket:message>
                    </button>
                    <button type="button" id="btnSend">
                        <span class="ui-icon ui-icon-jcs-02" /><wicket:message key="button.send">呈主管覆核</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnWAIT_APPROVE2" />
                    <button type="button" id="btnCheck">
                        <span class="ui-icon ui-icon-jcs-106" /><wicket:message key="button.check">覆核</wicket:message>
                    </button>
                </wicket:enclosure>
                <!--海外分行 提會待登錄案件-->
                <wicket:enclosure>
                    <span wicket:id="_btnSEND_WAITLOGIN" />
                    <button type="button" id="btnSendWaitLogin">
                        <span class="ui-icon ui-icon-jcs-02" /><wicket:message key="button.send2">呈主管審核</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnSEND_WAITLOGIN3" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.OpenLmsCase">開啟授信報案考核表</wicket:message>
                    </button>
                </wicket:enclosure>
                <!--海外分行 提會待覆核案件-->
                <wicket:enclosure>
                    <span wicket:id="_btnSEND_WAITAPPROVE2" />
                    <button type="button" id="btnCheckWaitApprove">
                        <span class="ui-icon ui-icon-jcs-106" /><wicket:message key="button.check">覆核</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnSEND_WAITAPPROVE3" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.OpenLmsCase">開啟授信報案考核表</wicket:message>
                    </button>
                </wicket:enclosure>
                <!--海外分行 待補件/撤件-->
                <wicket:enclosure>
                    <span wicket:id="_btnWAIT_GETORBACK" />
                    <button type="button" id="btnSave">
                        <span class="ui-icon ui-icon-jcs-04" /><wicket:message key="button.save">儲存</wicket:message>
                    </button>
                    <button type="button" id="btnSend">
                        <span class="ui-icon ui-icon-jcs-02" /><wicket:message key="button.send">呈主管覆核</wicket:message>
                    </button>
                </wicket:enclosure>
                <!--海外分行 已核准受理-->
                <wicket:enclosure>
                    <span wicket:id="_btnALREADY_OK3" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.OpenLmsCase">開啟授信報案考核表</wicket:message>
                    </button>
                </wicket:enclosure>
                <!--海外分行 婉卻-->
                <wicket:enclosure>
                    <span wicket:id="_btnALREADY_REJECT3" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.OpenLmsCase">開啟授信報案考核表</wicket:message>
                    </button>
                </wicket:enclosure>
                <!--海外分行 陳復案/陳述案-->
                <wicket:enclosure>
                    <span wicket:id="_btnSAY_CASE" />
                    <button type="button" id="btnSave">
                        <span class="ui-icon ui-icon-jcs-04" /><wicket:message key="button.save">儲存</wicket:message>
                    </button>
                    <button type="button" id="btnSend">
                        <span class="ui-icon ui-icon-jcs-02" /><wicket:message key="button.send">呈主管覆核</wicket:message>
                    </button>
                </wicket:enclosure>
                <!--營運中心-->
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_CEDITING" />
                    <button type="button" id="btnLogeIN">
                        <span class="ui-icon ui-icon-tag"></span>
                        <wicket:message key="button.login">登錄</wicket:message>
                    </button>
                    <button type="button" id="btnBackUnit">
                        <span class="ui-icon ui-icon-mail-closed"></span>
                        <wicket:message key="button.backunit">退回分行更正</wicket:message>
                    </button>
					<button type="button" id="btnBackCase2">
				    	<span class="ui-icon ui-icon-mail-closed"></span><wicket:message key="button.backcase2">撤件</wicket:message>
					</button>	
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.OpenLmsCase">開啟授信報案考核表</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btn4AreaAuthLvl3Hide" />
                    <button type="button" id="btnSend">
                        <span class="ui-icon ui-icon-jcs-02" /><wicket:message key="button.send">呈主管覆核</wicket:message>
                    </button>
                </wicket:enclosure>
                <!-- 2012-11-26 Rex add 國內營運中心陳覆陳述案增加免批覆按鈕 -->
                <wicket:enclosure>
                    <span wicket:id="_btn4AreaAuthLvl3" />
                    <button type="button" id="btnNoCheck">
                        <span class="ui-icon ui-icon-jcs-02"></span>
                        <wicket:message key="button.noNeedCheck">免批覆</wicket:message>
                    </button>
                </wicket:enclosure>
                <!-- end 國內營運中心陳覆陳述案增加免批覆按鈕 -->
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_CEDITING3" />
                    <button type="button" id="btnCaseToChange">
                        <span class="ui-icon ui-icon-jcs-10"></span>
                        <wicket:message key="button.casetochange">案件改分派</wicket:message>
                    </button>
                </wicket:enclosure> 
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_CWAITCHECK2" />
                    <button type="button" id="btnCheckArea">
                        <span class="ui-icon ui-icon-jcs-106" /><wicket:message key="button.check">覆核</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_CTOADMIN" />
                    <button type="button" id="btnBackCase">
                        <span class="ui-icon ui-icon-mail-closed"></span>
                        <wicket:message key="button.backcase">撤件/陳復</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_CTOADMIN2" />
                    <button type="button" id="btnCheck">
                        <span class="ui-icon ui-icon-jcs-106" /><wicket:message key="button.check">覆核</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_CALREADYOK3" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.openlms">開啟授信報案考核表</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_CALREADYREJECT" />
                    <button type="button" id="btnSendCase">
                        <span class="ui-icon ui-icon-jcs-15"></span>
                        <wicket:message key="button.sendcase">提會</wicket:message>
                    </button>
                    <button type="button" id="btnSend3">
                        <span class="ui-icon ui-icon-jcs-02"></span>
                        <wicket:message key="button.send3">呈主管放行</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_CALLSEND" />
                    <button type="button" id="btnSend3">
                        <span class="ui-icon ui-icon-jcs-02"></span>
                        <wicket:message key="button.send3">呈主管放行</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_CSAY_CASE" />
                    <button type="button" id="btnSend3">
                        <span class="ui-icon ui-icon-jcs-02"></span>
                        <wicket:message key="button.send3">呈主管放行</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_SEA_SIGNING" />
                    <button type="button" id="btnLogeIN">
                        <span class="ui-icon ui-icon-tag"></span>
                        <wicket:message key="button.login">登錄</wicket:message>
                    </button>
                    <button type="button" id="btnSend3">
                        <span class="ui-icon ui-icon-jcs-02"></span>
                        <wicket:message key="button.send3">呈主管放行</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_SEA_CWAITGO2" />
                    <button type="button" id="btnCheckSea">
                        <span class="ui-icon ui-icon-jcs-106" /><wicket:message key="button.check">覆核</wicket:message>
                    </button>
                </wicket:enclosure>
                <!--授管處-->
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HEDITING0" />
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HEDITING" />
                    <button type="button" id="btnLogeIN">
                        <span class="ui-icon ui-icon-tag"></span>
                        <wicket:message key="button.login">登錄</wicket:message>
                    </button>
                    <button type="button" id="btnSendCase">
                        <span class="ui-icon ui-icon-jcs-15"></span>
                        <wicket:message key="button.sendcase">提會</wicket:message>
                    </button>
                    <button type="button" id="btnBackCase">
                        <span class="ui-icon ui-icon-mail-closed"></span>
                        <wicket:message key="button.backcase">撤件/陳復</wicket:message>
                    </button>
                    <button type="button" id="btnBackInHead">
                        <span class="ui-icon ui-icon-mail-closed"></span>
                        <wicket:message key="button.backInHead">退回更正</wicket:message>
                    </button>
                    <button type="button" id="btnSend">
                        <span class="ui-icon ui-icon-jcs-02" /><wicket:message key="button.send3">呈主管放行</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HEDITING3" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.openlms">開啟授信報案考核表</wicket:message>
                    </button>
                    <button type="button" id="btnCaseToChange">
                        <span class="ui-icon ui-icon-jcs-10"></span>
                        <wicket:message key="button.casetochange">案件改分派</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HALREADY" />
                    <button type="button" id="btnLogeIN">
                        <span class="ui-icon ui-icon-tag"></span>
                        <wicket:message key="button.login">登錄</wicket:message>
                    </button>
                    <button type="button" id="btnSendCase">
                        <span class="ui-icon ui-icon-jcs-15"></span>
                        <wicket:message key="button.sendcase">提會</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HALREADY3" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.openlms">開啟授信報案考核表</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HSEND1" />
                    <button type="button" id="btnLogeIN">
                        <span class="ui-icon ui-icon-tag"></span>
                        <wicket:message key="button.login">登錄</wicket:message>
                    </button>
                    <button type="button" id="btnSendCase">
                        <span class="ui-icon ui-icon-jcs-15"></span>
                        <wicket:message key="button.sendcase">提會</wicket:message>
                    </button>
                    <button type="button" id="btnBackCase">
                        <span class="ui-icon ui-icon-mail-closed"></span>
                        <wicket:message key="button.backcase">撤件/陳復</wicket:message>
                    </button>
                    <button type="button" id="btnBackUnit">
                        <span class="ui-icon ui-icon-mail-closed"></span>
                        <wicket:message key="button.backunit">退回分行更正</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HSEND1b" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.openlms">開啟授信報案考核表</wicket:message>
                    </button>
                    <button type="button" id="btnCaseToChange">
                        <span class="ui-icon ui-icon-jcs-10"></span>
                        <wicket:message key="button.casetochange">案件改分派</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HSEND2" />
                    <button type="button" id="btnLogeIN">
                        <span class="ui-icon ui-icon-tag"></span>
                        <wicket:message key="button.login">登錄</wicket:message>
                    </button>
                    <button type="button" id="btnSendCase">
                        <span class="ui-icon ui-icon-jcs-15"></span>
                        <wicket:message key="button.sendcase">提會</wicket:message>
                    </button>
                    <button type="button" id="btnBackCase">
                        <span class="ui-icon ui-icon-mail-closed"></span>
                        <wicket:message key="button.backcase">撤件/陳復</wicket:message>
                    </button>
                    <button type="button" id="btnBackUnit">
                        <span class="ui-icon ui-icon-mail-closed"></span>
                        <wicket:message key="button.backunit">退回分行更正</wicket:message>
                    </button>
                    <button type="button" id="btnSend3">
                        <span class="ui-icon ui-icon-jcs-02"></span>
                        <wicket:message key="button.send3">呈主管放行</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HSEND2b" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.openlms">開啟授信報案考核表</wicket:message>
                    </button>
                    <button type="button" id="btnCaseToChange">
                        <span class="ui-icon ui-icon-jcs-10"></span>
                        <wicket:message key="button.casetochange">案件改分派</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HSEND3" />
                    <button type="button" id="btnLogeIN">
                        <span class="ui-icon ui-icon-tag"></span>
                        <wicket:message key="button.login">登錄</wicket:message>
                    </button>
                    <button type="button" id="btnSendCase">
                        <span class="ui-icon ui-icon-jcs-15"></span>
                        <wicket:message key="button.sendcase">提會</wicket:message>
                    </button>
                    <button type="button" id="btnBackCase">
                        <span class="ui-icon ui-icon-mail-closed"></span>
                        <wicket:message key="button.backcase">撤件/陳復</wicket:message>
                    </button>
                    <button type="button" id="btnBackUnit">
                        <span class="ui-icon ui-icon-mail-closed"></span>
                        <wicket:message key="button.backunit">退回分行更正</wicket:message>
                    </button>
                    <button type="button" id="btnSend">
                        <span class="ui-icon ui-icon-jcs-02" /><wicket:message key="button.send">呈主管覆核</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HSEND3b" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.openlms">開啟授信報案考核表</wicket:message>
                    </button>
                    <button type="button" id="btnCaseToChange">
                        <span class="ui-icon ui-icon-jcs-10"></span>
                        <wicket:message key="button.casetochange">案件改分派</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HWAITCHECK" />
                    <button type="button" id="btnBackCase">
                        <span class="ui-icon ui-icon-mail-closed"></span>
                        <wicket:message key="button.backcase">撤件/陳復</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HWAITCHECK2" />
                    <button type="button" id="btnCheckHead">
                        <span class="ui-icon ui-icon-jcs-106" /><wicket:message key="button.check">覆核</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HWAITCHECK3" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.openlms">開啟授信報案考核表</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HWAITCHANGE3" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.openlms">開啟授信報案考核表</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HALREADYOK3" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.openlms">開啟授信報案考核表</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HALREADYREJECT" />
                    <button type="button" id="backToHeadFirst">
                        <span class="ui-icon ui-icon-jcs-210"></span>
                        <wicket:message key="button.backToHeadFirst">退回審核中</wicket:message>
                    </button>
                </wicket:enclosure>
                <wicket:enclosure>
                    <span wicket:id="_btnDOC_HALREADYREJECT3" />
                    <button type="button" id="btnOpenLmsCase" class="only-ex-permission">
                        <span class="ui-icon ui-icon-jcs-07"></span>
                        <wicket:message key="button.openlms">開啟授信報案考核表</wicket:message>
                    </button>
                </wicket:enclosure>
				 <button type="button" id="btnFindL140M01ACase" class="forview only-ex-permission">
                    <span class="ui-icon ui-icon-jcs-103"></span>
                    <wicket:message key="CLS1141.111">查詢額度明細表</wicket:message>
                </button>
				<wicket:enclosure><span wicket:id="isShowPrintButton_paperlessFlagE"/>
	                <button type="button" id="btnPrint" class="forview only-ex-permission">
	                    <span class="ui-icon ui-icon-jcs-03"></span>
	                    <wicket:message key="button.print">列印</wicket:message>
	                </button>
				</wicket:enclosure>
                <wicket:enclosure><span wicket:id="isShowReSendBRMPButton_paperlessFlagE"/>
                    <button type="button" id="btnReSend" class="forview">
                        <span class="ui-icon ui-icon-jcs-10"></span>
                        <wicket:message key="button.pullinAgain">重新引進</wicket:message>
                    </button>
                </wicket:enclosure>
                <!--<span id="showExl" class="hide">
                <button id="btnCreateExl">
                <span class="ui-icon ui-icon-jcs-15"></span>
                <wicket:message key="l120m01a.bt06">產生借戶未結案待辦事項清單EXCEL</wicket:message>
                </button>
                </span>-->
                <button type="button" id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <wicket:message key="button.exit">離開</wicket:message>
                </button>
				<!--J-111-0551_05097_B1006 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度-->
				<button type="button" id="btnApproveUnestablshExl" class="forview">
					<span class="ui-icon ui-icon-jcs-110"></span>
					<wicket:message key="button.approveUnestablshExl">查詢在途授信額度</wicket:message>
				</button>	
            </div>
            <div class="tit2 color-black">
                <span id="showTitle" />：
                (<span id="showTypCd" class="text-red"/>)<span id="showCustId" class="color-blue" /><span id="showDocCode" class="text-red"/>
                <input type="hidden" id="mainCustId" name="mainCustId" />
                <input type="hidden" id="mainDupNo" name="mainDupNo" />
            </div>
            <div id="printView" style="display:none;">
                <div id="printGrid" />
            </div>
            <div id="printView2" style="display:none;">
                <div id="printGrid2" />
            </div>
            <div id="printThickBox" style="display:none;">
                <table width="100%" class="tb2">
                    <tbody>
                        <tr id="printUnitTypeA">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="A" checked="true"/>
                                    <wicket:message key="THICKBOX.RPTNAMEA"><!--簽報書及相關附表--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr id="printUnitTypeB">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="B"/>
                                    <wicket:message key="THICKBOX.RPTNAMEB"><!--母行法人代表提案意見--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr id="printUnitTypeC">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="C"/>
                                    <wicket:message key="THICKBOX.RPTNAMEC"><!--營運中心說明及意見--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr id="printUnitTypeD">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="D"/>
                                    <wicket:message key="THICKBOX.RPTNAMED"><!--審查意見及補充說明/會簽--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr id="printUnitTypeZ">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="Z"/>
                                    <wicket:message key="THICKBOX.RPTNAMEZ"><!--營運中心會議決議--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr id="printUnitTypeE">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="E"/>
                                    <wicket:message key="THICKBOX.RPTNAMEE"><!--授審會會議決議--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr id="printUnitTypeF">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="F"/>
                                    <wicket:message key="THICKBOX.RPTNAMEF"><!--催收會會議決議--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr id="printUnitTypeF2">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="F2"/>
                                    <wicket:message key="THICKBOX.RPTNAMEF2"><!--常董會會議決議--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr id="printUnitTypeG">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="G"/>
                                    <span id="spectialPrint" class="hide"><wicket:message key="THICKBOX.RPTNAMEI"><!--授管處會簽意見--></wicket:message></span><span id="signPrint"><wicket:message key="THICKBOX.RPTNAMEG"><!--海外聯貸案會簽意見--></wicket:message></span>
                                </label>
                            </td>
                        </tr>
                        <tr id="printUnitTypeH">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="H"/>
                                    <wicket:message key="THICKBOX.RPTNAMEH"><!--批覆書--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr id="printUnitTypeJ">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="J"/>
                                    <wicket:message key="THICKBOX.RPTNAMEJ"><!--授信戶異常通報表--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr id="printUnitTypeX">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="X"/>
                                    <wicket:message key="THICKBOX.RPTNAMEX"><!--資信簡表--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr id="printUnitTypeY">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="Y"/>
                                    <wicket:message key="l120m01a.bt06"><!--產生借戶未結案待辦事項清單EXCEL--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr >
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="simplePrint"/>
                                    <wicket:message key="THICKBOX.RPTNAMESIMPLEPRINT">簡易列印</wicket:message>
                                </label>
                            </td>
                        </tr>
						<tr id="printUnitTypeI">
                            <td style='border:none;padding:0px;margin:0px;'>
                                <label>
                                    <input name="printCondition" type="radio" value="I"/>
                                    <wicket:message key="THICKBOX.RPTNAMEK"><!--借款人申請書--></wicket:message>									
                                </label>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div id="tabs-lms" class="tabs doc-tabs">
                <ul>
                    <li><a href="#tab-01" goto="01"><b><wicket:message key="l120m01a.status">文件資訊</wicket:message></b></a></li>
                    <li><a href="#tab-02" goto="02"><b><wicket:message key="l120m01a.borrowdata">借款人基本資料</wicket:message></b></a></li>
                    <li class="ordinary" id="book99"><a href="#tab-19" goto="19"><b>異常通報表</b></a></li>
                    <li class="ordinary" id="book98"><a href="#tab-03" goto="03"><b><wicket:message key="l120m01a.l140m01a">額度明細表</wicket:message></b></a></li>
                    <li class="ordinary" id="book1"><a href="#tab-11" goto="11"><b><wicket:message key="l120m01a.l140m01b">額度批覆表</wicket:message></b></a></li>
					<wicket:enclosure><span wicket:id="show_checkList_panel"/>
						<li class="ordinary"><a href="#tab-24" goto="24"><b><wicket:message key="c120s01v.title">申請資料核對表</wicket:message></b></a></li>
					</wicket:enclosure>
                    <li class="ordinary"><a href="#tab-04" goto="04"><b><wicket:message key="l120m01a.casebecause">案由</wicket:message></b></a></li>
                    <li class="ordinary" id="page05"><a href="#tab-05" goto="05"><b><wicket:message key="l120m01a.say">借款用途/還款財源/各項費用</wicket:message></b></a></li>
                    <wicket:enclosure><span wicket:id="tab_l120m01a_simplifyFlagB"/>	
						<li class="ordinary"><a href="#tab-21" goto="21"><b><wicket:message key="l120m01a.simplifyFlagB">卡友信貸審核表</wicket:message></b></a></li>
					</wicket:enclosure>
					<wicket:enclosure><span wicket:id="tab_l120m01a_simplifyFlagA"/>	
						<li class="ordinary"><a href="#tab-22" goto="22"><b><wicket:message key="l120m01a.simplifyFlagA">簡化授信簽報書</wicket:message></b></a></li>
					</wicket:enclosure>
					<wicket:enclosure><span wicket:id="tab_l120m01a_simplifyFlagC"/>	
						<li class="ordinary"><a href="#tab-23" goto="23"><b><wicket:message key="l120m01a.simplifyFlagC">勞工紓困審核表</wicket:message></b></a></li>
					</wicket:enclosure>
					<wicket:enclosure><span wicket:id="tab_l120m01a_simplifyFlagD"/>	
						<li class="ordinary"><a href="#tab-25" goto="25"><b><wicket:message key="l120m01a.simplifyFlagD">歡喜信貸審核表</wicket:message></b></a></li>
					</wicket:enclosure>
					<wicket:enclosure><span wicket:id="tab_brmpPanel"/>
						<!-- 行員不能做71 只能選07  => 簽報書的頁籤名稱 不應固定為「歡喜信貸決策審核表」 -->
                        <li class="ordinary"><a href="#tab-26" goto="26"><b>信貸決策審核表</b></a></li>
                    </wicket:enclosure>					
					<li class="ordinary" id="page07"><a href="#tab-07" goto="07"><b><wicket:message key="l120m01a.lookall">綜合評估/往來彙總</wicket:message></b></a></li>					
					<!-- J-112-0123 開啟Eloan系統簽報書無紙化簽報下之相關文件頁籤 -->
                    <li class="" id="page08"><a href="#tab-08" goto="08"><b><wicket:message key="l120m01a.relationshipdoc">相關文件</wicket:message></b></a></li>
                    <li class="ordinary"><a href="#tab-09" goto="09"><b><wicket:message key="l120m01a.moresay">補充說明</wicket:message></b></a></li>
                    <li class="ordinary" id="book3"><a href="#tab-12" goto="12"><b><wicket:message key="l120m01a.decide">會簽/會議決議</wicket:message></b></a></li>
                    <li class="ordinary" id="book4"><a href="#tab-13" goto="13"><b><wicket:message key="l120m01a.admin">授管處意見</wicket:message></b></a></li>
                    <li class="ordinary hide" id="book5"><a href="#tab-14" goto="14"><b><wicket:message key="l120m01a.otherplace">國金部/營運中心會簽意見</wicket:message></b></a></li>
                    <li class="ordinary" id="book6"><a href="#tab-15" goto="15"><b><wicket:message key="l120m01a.otherplace2">營運中心意見</wicket:message></b></a></li>
                    <li><a href="#tab-10" goto="10"><b><wicket:message key="l120m01a.addfile">附加檔案</wicket:message></b></a></li>
                    <li class="ordinary" id="book7"><a href="#tab-17" goto="17"><b><wicket:message key="l120m01a.addfile1">常董稿附加檔案</wicket:message></b></a></li>
					<!--J-106-0029-001 新增洗錢防制頁籤-->
					<li class=""><a href="#tab-20" goto="20"><b>AML/CFT</b></a></li>
                    <li class="ordinary" id="book8"><a href="#tab-18" goto="18"><b><wicket:message key="CLS1141.tab10">主管批示</wicket:message></b></a></li>
                    <!----><wicket:enclosure><span wicket:id="tab_l120m01a_paperlessFlagE"/>
                        <li ><a href="#tab-27" goto="27"><b>授信審核</b></a></li>
                    </wicket:enclosure>
                </ul>
                <div id="PageContext" class="tabCtx-warp">
                    <input type="text" class="hide" id="mainOid" name="mainOid" />
                    <input type="text" class="hide" id="mainId" name="mainId" />
                    <input type="text" class="hide" id="areaChk" name="areaChk" />
                    <input type="text" class="hide" id="docKind" name="docKind" />
                    <div id="tabs-00" wicket:id="_tabCtx" />
                </div>
            </div>
            <div id="checkBoxSea" style="display:none">
                <table>
                    <tr>
                        <td>
                            <label>
                                <input name="qButtonTypeSea" type="radio" value="8" checked="checked"/>
                                <wicket:message key="l120m01a.queryButtonType8"><!-- 確認 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input name="qButtonTypeSea" type="radio" value="1"/>
                                <wicket:message key="l120m01a.queryButtonType1"><!--退回經辦修改 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                </table>
            </div>
            <div id="checkBox" style="display:none">
                <table>
                    <tr style="display:none">
                        <td>
                            <label>
                                <input name="queryButtonType" type="radio" value="8"/>
                                <wicket:message key="l120m01a.queryButtonType8"><!--確認--></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr style="display:none">
                        <td>
                            <label>
                                <input name="queryButtonType" type="radio" value="2"/>
                                <wicket:message key="l120m01a.queryButtonType2"><!-- 呈授管處--></wicket:message><span id="sendSign" class="hide"><wicket:message key="l120m01a.decide1"><!-- 會簽--></wicket:message></span><span id="sendCheck" class="hide"><wicket:message key="l120m01a.decide4"><!-- 審查--></wicket:message></span><span id="sendSpec" class="hide"><wicket:message key="l120m01a.queryButtonType10"><!-- 審核批覆--></wicket:message></span>
                            </label>
                        </td>
                    </tr>
                    <tr style="display:none">
                        <td>
                            <label>
                                <input name="queryButtonType" type="radio" value="3"/>
                                <wicket:message key="l120m01a.queryButtonType3"><!--呈營運中心 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr style="display:none">
                        <td>
                            <label>
                                <input name="queryButtonType" type="radio" value="4"/>
                                <wicket:message key="l120m01a.queryButtonType4"><!--呈總行--></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr style="display:none">
                        <td>
                            <label>
                                <input name="queryButtonType" type="radio" value="5"/>
                                <span id="goSend"><wicket:message key="l120m01a.queryButtonType5"><!--核定放行--></wicket:message></span><span id="goSign" class="hide"><wicket:message key="l120m01a.decide5"><!-- 會簽內容放行--></wicket:message></span>
                            </label>
                        </td>
                    </tr>
                    <tr style="display:none">
                        <td>
                            <label>
                                <input name="queryButtonType" type="radio" value="6"/>
                                <wicket:message key="l120m01a.queryButtonType6"><!--退回分行更正--></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr style="display:none">
                        <td>
                            <label>
                                <input name="queryButtonType" type="radio" value="7"/>
                                <wicket:message key="l120m01a.queryButtonType7"><!--退回區域中心更正--></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr style="display:none">
                        <td>
                            <label>
                                <input name="queryButtonType" type="radio" value="9"/>
                                <wicket:message key="docStatus.L3M"><!--提會待登錄--></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr style="display:none">
                        <td>
                            <label>
                                <input name="queryButtonType" type="radio" value="10"/>
                                <wicket:message key="docStatus.L4M"><!--提會待覆核--></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr style="display:none">
                        <td>
                            <label>
                                <input name="queryButtonType" type="radio" value="1"/>
                                <wicket:message key="l120m01a.queryButtonType1"><!--退回經辦修改 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                </table>
            </div>
            <div id="sendTo" style="display:none">
                <table>
                    <tr>
                        <td>
                            <label>
                                <input name="hqMeetFlag" type="radio" value="1"/>
                                <wicket:message key="l120m01a.sendTo1"><!--提授審會 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input name="hqMeetFlag" type="radio" value="2"/>
                                <wicket:message key="l120m01a.sendTo2"><!--提催收會--></wicket:message>
                            </label>
                        </td>
                    </tr>
					<!--J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，請於授審處之授信管理系統→「授信審查」頁面→「案件簽報書」項下增加「提審計委員會」選項。-->
					<tr>
                        <td>
                            <label>
                                <input name="hqMeetFlag" type="radio" value="4"/>
                                <wicket:message key="l120m01a.sendTo4"><!--提審計委員會--></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input name="hqMeetFlag" type="radio" value="3"/>
                                <wicket:message key="l120m01a.sendTo3"><!--提常董會--></wicket:message>
                            </label>
                        </td>
                    </tr>
                </table>
            </div>
            <div id="backCaseBox" style="display:none">
                <table>
                    <tr>
                        <td>
                            <label>
                                <input name="backCaseRadio" type="radio" value="1"/>
                                <wicket:message key="l120m01a.backCaseRadio1"><!--撤件 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input name="backCaseRadio" type="radio" value="2"/>
                                <wicket:message key="l120m01a.backCaseRadio2"><!--待陳復--></wicket:message>
                            </label>
                        </td>
                    </tr>
                </table>
            </div>
			<!--J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率, 增加企/消金授信簽報案件經區域營運中心流程進度控管表-->
			<!-- 撤件日ThickBox內容 -->
			<div id="divCaseCancelDate" style="display:none" >
				<form id="formCaseCancelDate">
					<table>
				      <tr>
				      	<td>
							 <td class="hd1">
	                            <wicket:message key="l120s17a.caseCancelDate">撤件日期</wicket:message>：&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <input id="caseCancelDate" name="caseCancelDate"  class="date required"  type="text"   size="8" maxlength="10" />
	                        </td>
						</td>
					  </tr>
				    </table>
				</form>
			</div>
            <div id="backReasonBox" style="display:none">
                <textarea id="backReasonTextarea" maxlengthC="20"></textarea>
            </div>
			
			<!--J-111-0551_05097_B1006 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度-->
			<div id="approveUnestablshExlBox" style="display:none" >
				<span>
					若有多個統編要查詢請用逗號隔開，每筆統編最後一碼須為重複序號
					<br>
					(企業戶8碼統編+1碼重複序號共9碼，OBU統編或個人戶身分證ID 10碼+1碼重複序號共11碼)
					<br>
	                                          例如:123456780,876543210,A1234567890
					<br>
					請不要按Enter換行
				</span>
				<br>
				<textarea id="approveUnestablshExlTextarea" maxlengthC="512" rows="3" cols="80"></textarea>
				<br>
				<span>
					說明：
					<br>
					在途授信額度係指已核准未簽約或雖已簽約惟尚未引入帳務系統之授信額度，倘授信案件客戶確實已不同意敘做或實際簽約額度低於核准額度時，請確實於eloan簽報系統「已核准授信額度辦理狀態報送作業」執行，將該額度序號選擇「不簽約-註銷額度」之選項，或選擇「已簽約」之選項，並完成該選項內實際簽約(承做)額度之建置作業。
					 
				</span>
			</div>
		
			<!-- 特殊分行會簽後呈主管覆核選擇界面 -->
			<div id="spectialSendTo" style="display:none" >
				<table>
			      <tr>
			      	<td>
			      		<label><input name="spectialFlag" type="radio" value="B" checked="checked"/><wicket:message key="l120m01a.queryButtonType9"><!--呈授管處審核批覆 --></wicket:message></label>
					</td>
				  </tr>
			      <tr id="spectialSendToShowForSign">
			    	<td>
			    		<label><input name="spectialFlag" type="radio" value="A"/><wicket:message key="button.send"><!--呈主管覆核--></wicket:message></label>
					</td>
				  </tr>
			    </table>
			</div>			
            <!-- 登錄 ThickBox內容 -->
            <div id="signThick" style="display:none">
                <input type="hidden" id="_tItemDscr09" name="_tItemDscr09"/>
                <table>
                    <tr>
                        <td>
                            <label>
                                <input name="sign" type="radio" value="1" checked="checked" onclick="showCk(this,'showCk');"/>
                                <wicket:message key="l120m01a.sign"><!--會簽意見 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr id="showCk" style="display:;">
                        <td>
                            <div style="width:100%">
                                &nbsp;&nbsp;<textarea class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="49" wicket:message="displayMessage:l120m01a.otherplace" id="tItemDscr09" name="tItemDscr09" cols="115" rows="10"></textarea>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input name="sign" type="radio" value="999" onclick="hideCk(this,'showCk');checkCk('signThick','showCk');"/>
                                <wicket:message key="l120m01a.sign2"><!--登錄簽章欄人員 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                </table>
            </div>
            <!-- 登錄 ThickBox內容 -->
            <div id="signThick2" style="display:none">
                <input type="hidden" id="_tItemDscr0A" name="_tItemDscr0A"/>
                <input type="hidden" id="_tItemDscr0B" name="_tItemDscr0B"/>
                <input type="hidden" id="_htItemDscr0C" name="_htItemDscr0C"/>
                <table>
                    <tr>
                        <td>
                            <label class="sSpectial">
                                <input name="login" type="radio" value="1" onclick="hideCk(this,'showCk2');hideCk(this,'showCkHead');checkCk('signThick2','showCk2');checkCk('signThick2a','showCkHead');" checked="checked"/>
                                <wicket:message key="l120m01a.caselevel"><!--案件審核層級 --></wicket:message>
                            </label>
                        </td>
                    </tr>
					<!--J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率, 增加企/消金授信簽報案件經區域營運中心流程進度控管表-->
					  <tr>
				      	<td>
				      		<label>
				      			<input name="login" type="radio" value="991" onclick="hideCk(this,'showCk2');hideCk(this,'showCkHead');checkCk('signThick2','showCk2');checkCk('signThick2a','showCkHead');" />
								<wicket:message key="l120s17a.caseReceivedDate">案件收件日期</wicket:message>
							</label>
						</td>
					  </tr>
                    <tr id="hideHeadSign">
                        <td>
                            <label class="hSpectial">
                                <input name="login" type="radio" value="2" onclick="showCk(this,'showCk2');hideCk(this,'showCkHead');checkCk('signThick2a','showCkHead');"/>
                                <wicket:message key="l120m01a.looksay"><!--補充意見及審查意見 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr id="showCk2" style="display:none;">
                        <td>
                            <div style="width:100%">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="hareaChk"><textarea class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="49" wicket:message="displayMessage:l120m01a.moresay" id="tItemDscr0A" name="tItemDscr0A" cols="115" rows="10"></textarea>&nbsp;&nbsp;</span>
                                <textarea class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="49" wicket:message="displayMessage:l120m01a.admin1" id="tItemDscr0B" name="tItemDscr0B" cols="115" rows="10"></textarea>
                            </div>
                        </td>
                    </tr>
                    <tr class="hideUnNormal">
                        <td>
                            <label>
                                <input name="login" type="radio" value="4" onclick="hideCk(this,'showCk2');hideCk(this,'showCkHead');checkCk('signThick2','showCk2');checkCk('signThick2a','showCkHead');" />
                                異常通報需後續追蹤事項
                            </label>
                        </td>
                    </tr>
                    <tr class="hideUnNormal">
                        <td>
                            <label>
                                <input name="login" type="radio" value="5" onclick="hideCk(this,'showCk2');hideCk(this,'showCkHead');checkCk('signThick2','showCk2');checkCk('signThick2a','showCkHead');" />
                                異常通報案件批覆內容
                            </label>
                        </td>
                    </tr>					
                    <tr>
                        <td>
                            <label id="spectialId" class="hide">
                                <input name="login" type="radio" value="3" onclick="hideCk(this,'showCk2');hideCk(this,'showCkHead');checkCk('signThick2','showCk2');checkCk('signThick2a','showCkHead');"/>
                                <wicket:message key="l120m01a.decide2"><!--會議決議--></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="hSpectial">
                                <input name="login" type="radio" value="999" onclick="hideCk(this,'showCk2');hideCk(this,'showCkHead');checkCk('signThick2','showCk2');checkCk('signThick2a','showCkHead');"/>
                                <wicket:message key="l120m01a.sign2"><!--登錄簽章欄人員 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr id="specitalHead">
                        <td>
                            <label>
                                <input name="login" type="radio" value="998" onclick="hideCk(this,'showCk2');showCk(this,'showCkHead');checkCk('signThick2','showCk2');"/>
                                <wicket:message key="l120m01a.sign"><!--會簽意見 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr id="showCkHead" class="hide">
                        <td>
                            <div style="width:100%">
                                &nbsp;&nbsp;<span id="hCkHead" class="hide"><textarea class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="49" wicket:message="displayMessage:l120m01a.otherplace1" id="htItemDscr0C" name="htItemDscr0C" cols="115" rows="10"></textarea></span>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <!-- 登錄 ThickBox內容 -->
            <div id="signCaseLvl" style="display:none">
                <form id="formCaseLvl">
                    <table>
                        <tr>
                            <td>
                                <select class="max" maxlength="2" id="caseLvl" name="caseLvl">
                                    <!--案件審核層級-->
                                </select>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            <div id="LMS1200V62Thickbox1" style="display:none">
                <form id="LMS1200V62Form1">
                    <table width="100%" class="tb2">
                        <tbody>
                            <tr>
                                <td>
                                    <input type="text" class="max required" id="rptTitle1a" name="rptTitle1a" maxlength="3" size="5" />
                                    <wicket:message key="_l120m01a.year">民國年</wicket:message>
                                    <input type="text" class="max required" id="rptTitle1b" name="rptTitle1b" maxlength="2" size="5" />
                                    <wicket:message key="_l120m01a.month">月</wicket:message>
                                    <input type="text" class="max required" id="rptTitle1c" name="rptTitle1c" maxlength="2" size="5" />
                                    <wicket:message key="_l120m01a.day">日</wicket:message>
                                    <input type="text" class="max required" id="rptTitle1d" name="rptTitle1d" maxlength="4" size="4" />
                                    <wicket:message key="_l120m01a.times">次
                                        <br/>
                                    </wicket:message>
                                    <b><span class="field" id="rptTitle1e" name="rptTitle1e"/><wicket:message key="_l120m01a.select1">授信審議小組會議</wicket:message></b>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
            <!-- 登錄 ThickBox內容 -->
            <div id="signThick3" style="display:none">
                <input type="hidden" id="_tItemDscr07" name="_tItemDscr07"/>
                <input type="hidden" id="_tItemDscr08" name="_tItemDscr08"/>
                <table>
                    <tr id="sCaseLvl" class="hide">
                        <td>
                            <label>
                                <input name="login2" type="radio" value="998" onclick="hideCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3');checkCk('signThick3','showCk3a');" checked="checked"/>
                                <wicket:message key="l120m01a.caselevel"><!--案件審核層級 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                <input name="login2" type="radio" value="1" onclick="hideCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3');checkCk('signThick3','showCk3a');"/>
                                <wicket:message key="l120m01a.casetitle"><!--營運中心授審會會期 --></wicket:message>
                            </label>
                        </td>
                    </tr>
					<!--J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率, 增加企/消金授信簽報案件經區域營運中心流程進度控管表-->
					  <tr>
				      	<td>
				      		<label>
				      			<input name="login2" type="radio" value="991" onclick="hideCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3');checkCk('signThick3','showCk3a');" />
								<wicket:message key="l120s17a.caseReceivedDate">案件收件日期</wicket:message>
							</label>
						</td>
					  </tr>
                    <tr>
                        <td>
                            <label>
                                <input name="login2" type="radio" value="2" onclick="showCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3a');"/>
                                <wicket:message key="l120m01a.looksay2"><!--營運中心說明及意見 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr id="showCk3" style="display:none;">
                        <td>
                            <div style="width:100%">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<textarea class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="49" wicket:message="displayMessage:l120m01a.otherplace2" id="tItemDscr07" name="tItemDscr07" cols="115" rows="10"></textarea>
                            </div>
                        </td>
                    </tr>
                    <tr class="hideUnNormal">
                        <td>
                            <label>
                                <input name="login2" type="radio" value="4" onclick="hideCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3');checkCk('signThick3','showCk3a');" />
                                異常通報需後續追蹤事項
                            </label>
                        </td>
                    </tr>					
                    <tr>
                        <td>
                            <label>
                                <input name="login2" type="radio" value="3" onclick="showCk(this,'showCk3a');hideCk(this,'showCk3');checkCk('signThick3','showCk3');"/>
                                <wicket:message key="l120m01a.looksay3"><!--營運中心會議決議 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                    <tr id="showCk3a" style="display:none;">
                        <td>
                            <div style="width:100%">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <button type="button" onclick="loginTitle()">
                                    <span class="text-only"><wicket:message key="l120m01a.bt04">登錄標題</wicket:message></span>
                                </button>
                                <!--
                                <br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;<textarea class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="49" wicket:message="displayMessage:l120m01a.decide2" id="tItemDscr08" name="tItemDscr08" cols="115" rows="10"></textarea>-->
                            </div>
                        </td>
                    </tr>
                    <!--<tr>
                    <td>
                    <label><input name="login2" type="radio" value="3" onclick="hideCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3');checkCk('signThick3','showCk3a');" /><wicket:message key="l120m01a.bt04">登錄標題</wicket:message></label>
                    </td>
                    </tr>-->
                    <tr>
                        <td>
                            <label>
                                <input name="login2" type="radio" value="999" onclick="hideCk(this,'showCk3');hideCk(this,'showCk3a');checkCk('signThick3','showCk3');checkCk('signThick3','showCk3a');"/>
                                <wicket:message key="l120m01a.sign2"><!--登錄簽章欄人員 --></wicket:message>
                            </label>
                        </td>
                    </tr>
                </table>
            </div>
            <!--授信異常通報外層(營運中心、授管處)共用元件-->
            <div wicket:id="lmsm02b_panel" id="lmsm02b_panel">
            </div>
            <div id="thickLms7205Grid" style="display:none;">
                <div id="lms7205Grid" />
            </div>
            <div class="tb2" id="signContent" style="display:none;">
                <div>
                    <form id="formSea">
                        <table width=100%>
                            <tr>
                                <td class="hd1" width="50%">
                                    <wicket:message key="l120m01a.title16">單位/授權主管：</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="50%">
                                    <select class="boss" id="sSeaManager" name="sSeaManager">
                                    </select>
                                    <button type="button" onclick="lmsM03Json.beforeOpen('sSeaManager')">
                                        <span class="text-only">常用主管</span>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <wicket:message key="l120m01a.title17">授信/覆核主管：</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <select class="boss" id="sSeaBoss" name="sSeaBoss">
                                    </select>
                                    <button type="button" onclick="lmsM03Json.beforeOpen('sSeaBoss')">
                                        <span class="text-only">常用主管</span>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <wicket:message key="l120m01a.title18">帳戶管理員：</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <select class="boss" id="sSeaAoName" name="sSeaAoName">
                                    </select>
                                    <button type="button" onclick="lmsM03Json.beforeOpen('sSeaAoName')">
                                        <span class="text-only">常用主管</span>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <wicket:message key="l120m01a.title19">經辦：</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <select class="boss" id="sSeaAppraiserCN" name="sSeaAppraiserCN">
                                    </select>
                                    <button type="button" onclick="lmsM03Json.beforeOpen('sSeaAppraiserCN')">
                                        <span class="text-only">常用主管</span>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <wicket:message key="l120m01a.managerId2"><!--單位主管--></wicket:message>：&nbsp;&nbsp;
                                </td>
                                <td>
                                    <select class="boss2" id="sUnitManager1" name="sUnitManager1">
                                    </select>
                                </td>
                            </tr>
                        </table>
                    </form>
                </div>
            </div>
            <div id="signContent2" style="display:none">
                <table class="tb2" width=100%>
                    <tr>
                        <td class="hd1" width="50%">
                            <wicket:message key="l120m01a.title19">經辦：</wicket:message>&nbsp;&nbsp;
                        </td>
                        <td width="50%">
                            <select class="boss" id="sHeadAppraiser" name="sHeadAppraiser">
                            </select>
                            <!--<button type="button" onclick="lmsM03Json.beforeOpen('sHeadAppraiser')">
                            <span class="text-only">常用主管</span>
                            </button>-->
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <wicket:message key="l120m01a.title26">覆核：</wicket:message>&nbsp;&nbsp;
                        </td>
                        <td>
                            <select class="boss" id="sHeadReCheck" name="sHeadReCheck">
                            </select>
                            <button type="button" onclick="lmsM03Json.beforeOpen('sHeadReCheck')">
                                <span class="text-only">常用主管</span>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <wicket:message key="l120m01a.title25">副處長：</wicket:message>&nbsp;&nbsp;
                        </td>
                        <td>
                            <select class="boss" id="sHeadSubLeader" name="sHeadSubLeader">
                            </select>
                            <button type="button" onclick="lmsM03Json.beforeOpen('sHeadSubLeader')">
                                <span class="text-only">常用主管</span>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <wicket:message key="l120m01a.title24">協理：</wicket:message>&nbsp;&nbsp;
                        </td>
                        <td>
                            <select class="boss" id="sHeadLeader" name="sHeadLeader">
                            </select>
                            <button type="button" onclick="lmsM03Json.beforeOpen('sHeadLeader')">
                                <span class="text-only">常用主管</span>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <wicket:message key="l120m01a.managerId2"><!--單位主管--></wicket:message>：&nbsp;&nbsp;
                        </td>
                        <td>
                            <select class="boss2" id="sUnitManager2" name="sUnitManager2">
                            </select>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="tb2" id="signContent3" style="display:none">
                <table width=100%>
                    <tr>
                        <td class="hd1" width="50%">
                            <wicket:message key="l120m01a.title19">經辦：</wicket:message>&nbsp;&nbsp;
                        </td>
                        <td width="50%">
                            <select class="boss" id="sAreaAppraiser" name="sAreaAppraiser">
                            </select>
                            <!--<button type="button" onclick="lmsM03Json.beforeOpen('sAreaAppraiser')">
                            <span class="text-only">常用主管</span>
                            </button>-->
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <wicket:message key="l120m01a.title23">襄理：</wicket:message>&nbsp;&nbsp;
                        </td>
                        <td>
                            <select class="boss" id="sAreaManager" name="sAreaManager">
                            </select>
                            <button type="button" onclick="lmsM03Json.beforeOpen('sAreaManager')">
                                <span class="text-only">常用主管</span>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <wicket:message key="l120m01a.title22">副營運長：</wicket:message>&nbsp;&nbsp;
                        </td>
                        <td>
                            <select class="boss" id="sAreaSubLeader" name="sAreaSubLeader">
                            </select>
                            <button type="button" onclick="lmsM03Json.beforeOpen('sAreaSubLeader')">
                                <span class="text-only">常用主管</span>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <wicket:message key="l120m01a.title21">營運長：</wicket:message>&nbsp;&nbsp;
                        </td>
                        <td>
                            <select class="boss" id="sAreaLeader" name="sAreaLeader">
                            </select>
                            <button type="button" onclick="lmsM03Json.beforeOpen('sAreaLeader')">
                                <span class="text-only">常用主管</span>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <wicket:message key="l120m01a.managerId2"><!--單位主管--></wicket:message>：&nbsp;&nbsp;
                        </td>
                        <td>
                            <select class="boss2" id="sUnitManager3" name="sUnitManager3">
                            </select>
                        </td>
                    </tr>
                </table>
            </div>
            <div id="signContent3a" style="display:none">
                <form id="areaForm">
                    <input type="hidden" id="_itemTitle" name="_itemTitle">
                    <input type="hidden" id="_itemTitle2" name="_itemTitle2">
                    <b>
                        <input type="text" class="max" id="itemTitle" name="itemTitle" size="50" maxlength="120" maxlengthC="40" value="Title" disabled="true">
                    </b>
                    <button type="button" onclick="logTitle()">
                        <span class="text-only"><wicket:message key="l120m01a.bt04">登錄標題</wicket:message></span>
                    </button>
                </form>
            </div>
            <div id="selectItemTitle" style="display:none;">
                <select id="sItemTitle" name="sItemTitle">
                </select>
            </div>
            <div id="selectHqAppraiser" style="display:none">
                <form id="selectHqForm">
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td>
                                <select id="hqAppraiser" name="hqAppraiser"/>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            <div id="selectBossBox" style="display:none;">
                <form id="selectBossForm">
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="hd1">
                                <wicket:message key="l120m01a.aoPerson"><!--  帳戶管理員--></wicket:message>&nbsp;&nbsp;
                            </td>
                            <td>
                                <select id="AOPerson" name="AOPerson" class="boss"/>&nbsp;
							<span id="bAOPerson">
								<button type="button" onclick="lmsM03Json.beforeOpen('AOPerson')">
									<span class="text-only">常用主管</span>
								</button>								
							</span>							
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" width="60%">
                                <wicket:message key="l120m01a.selectBoss"><!--  授信主管人數--></wicket:message>&nbsp;&nbsp;
                            </td>
                            <td width="40%">
                                <select id="numPerson" name="numPerson">
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6">6</option>
                                    <option value="7">7</option>
                                    <option value="8">8</option>
                                    <option value="9">9</option>
                                    <option value="10">10</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                                <wicket:message key="l120m01a.bossId"><!--  授信主管--></wicket:message>&nbsp;&nbsp;
                            </td>
                            <td>
                                <div>
                                    <wicket:message key="l120m01a.no"><!-- 第--></wicket:message>1<wicket:message key="l120m01a.site"><!--  位--></wicket:message>
                                    <wicket:message key="l120m01a.bossId"><!--  授信主管--></wicket:message>&nbsp;&nbsp;
                                    <select id="mainBoss" name="boss1" class="boss"/>
                                    <button b-id="mainBoss" type="button" onclick="lmsM03Json.beforeOpen($(this).attr('b-id'))">
                                        <span class="text-only">常用主管</span>
                                    </button>
                                    <span id="newBossSpan" />
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                                <wicket:message key="l120m01a.managerId"><!--  經副襄理--></wicket:message>&nbsp;&nbsp;
                            </td>
                            <td>
                                <select id="sManager" name="sManager" class="boss"/>&nbsp;
								<button type="button" onclick="lmsM03Json.beforeOpen('sManager')">
									<span class="text-only">常用主管</span>
								</button>							
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                                <wicket:message key="l120m01a.managerId2"><!--單位主管--></wicket:message>&nbsp;&nbsp;
                            </td>
                            <td>
                                <select class="boss2" id="sUnitManager" name="sUnitManager">
                                </select>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            <div id="openChecDatekBox" style="display:none">
                <div>
                    <input id="forCheckDate" type="text" size="10" maxlength="10" class="date"/>
                </div>
            </div>
            <!-- 常用主管名單共用元件 -->
            <div wicket:id="lmsm03_panel" id="lmsm03_panel">
            </div>
            <div wicket:id="lmss7305_panel" id="lmss7305_panel" open="true">
            </div>
            <!-- 營運中心會議決議共用元件 -->
            <div wicket:id="lmsm04_panel" id="lmsm04_panel">
            </div>
            <form action="" id="showBorrowData">
            </form>
            <!-- 授管處列印資信簡表Grid -->
            <div id="tcesGrid120" style="display: none;">
                <form id="formCesGrid120">
                    <div>
                        <div id="gridSelCes120" width="100%" style="margin-left: 10px; margin-right: 10px">
                        </div>
                    </div>
                </form>
            </div>
			<!--J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率, 增加企/消金授信簽報案件經區域營運中心流程進度控管表-->
			<!-- 登錄 案件收件日期ThickBox內容 -->
			<div id="signCaseReceivedDate" style="display:none" >
				<form id="formCaseReceivedDate">
					<table>
				      <tr>
				      	<td>
							 <td class="hd1">
	                            <wicket:message key="l120s17a.caseReceivedDate">案件收件日期</wicket:message>：&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <input id="caseReceivedDate" name="caseReceivedDate"  class="date"  type="text"   size="8" maxlength="10" />
	                            <span class="text-red">
								<br>
								<wicket:message key="l120s17a.caseReceivedDateMemo">(YYYY-MM-DD，分行簽報內容最終定稿送呈日)</wicket:message>
								</span>
	                        </td>
						</td>
					  </tr>
				    </table>
				</form>
			</div>
            <div id="createCntrNo_brmp_creditCheckDiv" style="display:none">
                <form id="createCntrNo_brmp_creditCheckForm">
                    <table class="tb2"  style="width:800px">
                        <tr>
                            <td class="hd2 rt">
                                信貸客群 / 行銷代碼
                            </td>
                            <td>
                            	<div style="font-weight:bold;font-size: 13px;"><span id="span_createCntrNo_brmp_creditCheck_termGroupRuleResultText">--請選擇--</span></div>
                                <select id="createCntrNo_brmp_creditCheck_termGroup" name="createCntrNo_brmp_creditCheck_termGroup" space="true" combokey="L140S02A_termGroup_brmp" comboType="2" class="required" style="display:none;"/>
                            	<span class="text-red">此處客群依個金徵信所選資料代入，如需調整客群請至個金徵信修改。</span>
								<input type="hidden" id="createCntrNo_brmp_creditCheck_termGroupSub" name="createCntrNo_brmp_creditCheck_termGroupSub" value="" />
							</td>
                        </tr>
                        <tr>
                            <td class="hd2 rt" style='vertical-align:top;'>
                                借款用途
                            </td>
                            <td>
                                <table border='0' class='tb2'>
                                    <wicket:enclosure><span wicket:id="trPurpose"/>
                                        <tr>
                                            <td class='noborder' style='padding:0px;'>
                                                <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="B" class="required" ><wicket:message key="l120m01i.creditLoanPurpose.B">房屋修繕</wicket:message>
                                                </label>
                                            </td>
                                            <td class='noborder' style='padding:0px;'>
                                                <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="D" class="required" ><wicket:message key="l120m01i.creditLoanPurpose.D">購車</wicket:message>
                                                </label>
                                            </td>
                                            <td class='noborder' style='padding:0px;' colspan='3'>
                                                <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="H" class="required" ><wicket:message key="l120m01i.creditLoanPurpose.H">購買耐久性消費財(不含汽車)</wicket:message>
                                                </label>
                                            </td>
                                            <td class='noborder' style='padding:0px;'>
                                                <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="G" class="required" ><wicket:message key="l120m01i.creditLoanPurpose.G">投資理財</wicket:message>
                                                </label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class='noborder' style='padding:0px;'>
                                                <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="C" class="required" ><wicket:message key="l120m01i.creditLoanPurpose.C">週轉</wicket:message>
                                                </label>
                                            </td>
                                            <td class='noborder' style='padding:0px;'>
                                                <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="J" class="required" ><wicket:message key="l120m01i.creditLoanPurpose.J">子女教育</wicket:message>
                                                </label>
                                            </td>
                                            <td class='noborder' style='padding:0px;'>
                                                <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="K" class="required" ><wicket:message key="l120m01i.creditLoanPurpose.K">繳稅</wicket:message>
                                                </label>
                                            </td>
                                            <td class='noborder' style='padding:0px;'>
                                                <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="3" class="required" ><wicket:message key="l120m01i.creditLoanPurpose.3">其他消費性支出</wicket:message>
                                                </label>
                                            </td>
                                        </tr>
                                    </wicket:enclosure>
                                    <wicket:enclosure><span wicket:id="trPurpose202209"/>
                                        <tr>
                                            <td class='noborder' style='padding:0px;'>
                                                <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="N" class="required" ><wicket:message key="l120m01i.creditLoanPurpose.N">個人週轉金</wicket:message>
                                                </label>
                                            </td>
                                            <td class='noborder' style='padding:0px;' colspan='3'>
                                                <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="O" class="required" ><wicket:message key="l120m01i.creditLoanPurpose.O">個人消費性用途</wicket:message>
                                                </label>
                                            </td>
                                            <td class='noborder' style='padding:0px;'>
                                                <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="G" class="required" ><wicket:message key="l120m01i.creditLoanPurpose.G">投資理財</wicket:message>
                                                </label>
                                            </td>
                                            <td class='noborder' style='padding:0px;' colspan='3'>
                                                <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="P" class="required" ><wicket:message key="l120m01i.creditLoanPurpose.P">企業員工認購股票</wicket:message>
                                                </label>
                                            </td>
                                        </tr>
                                    </wicket:enclosure>
                                </table>
                                <div id="div_createCntrNo_brmp_creditCheck_induceFlagOrNot">
                                    <label style='padding:0px;'><input type='checkbox' id="createCntrNo_brmp_creditCheck_induceFlagOrNot" name="createCntrNo_brmp_creditCheck_induceFlagOrNot" value="T" class="">是否購買本行理財產品
                                    </label>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt">
                                引介來源
                            </td>
                            <td>
                                <select name="createCntrNo_brmp_creditCheck_introduceSrc" id="createCntrNo_brmp_creditCheck_introduceSrc" space="true" combokey="L140M01A_introductionSource" comboType="4" class="" />
                                <div id="employeeDiv" style="display:none;">
                                    <table>
                                        <tr>
                                            <td class="hd1">
                                                <wicket:message key="L140M01a.megaEmpno">引介行員代號</wicket:message>
                                            </td>
                                            <td width="40%">
                                                <input type="text" id="megaEmpNo" name="megaEmpNo" maxlength="6" size="6" class='numText required'
                                                       onblur="if($.trim($(this).val()).length > 0){var var_megaEmpNo = '000000'+$(this).val();$(this).val(var_megaEmpNo.substr(var_megaEmpNo.length-6, 6));}" />
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div id="megaSubCompanyDiv" style="display:none;">
                                    <table>
                                        <tr class="docCode5Hide" >
                                            <td class="hd1" nowrap><wicket:message key="L140M01a.megaCode">引介子公司代號</wicket:message>
                                            </td>
                                            <td><select id="megaCode" name="megaCode" itemType="L140S02A_megaCode" class="required"/>
                                            </td>
                                        </tr>
                                        <tr class="docCode5Hide" >
                                            <td class="hd1"><wicket:message key="L140M01a.subUnitNo">引介子公司分支代號</wicket:message>
                                            </td>
                                            <td>
                                                <select id="subUnitNo" name="subUnitNo" class="required"/> <!-- J-107-0136 -->
                                            </td>
                                        </tr>
                                        <tr class="docCode5Hide" >
                                            <td class="hd1"  ><wicket:message key="L140M01a.subEmpNo">引介子公司員工編號</wicket:message>
                                            </td>
                                            <td><input type="text" id="subEmpNo" name="subEmpNo" maxlength="6" size="6" class='required'
                                                       onblur="if($.trim($(this).val()).length > 0 && (new RegExp('[a-zA-Z]').test($(this).val())==false)){var var_subEmpNo = '000000'+$(this).val();$(this).val(var_subEmpNo.substr(var_subEmpNo.length-6, 6));}" />
                                            </td>
                                        </tr>
                                        <tr class="docCode5Hide" >
                                            <td class="hd1"  ><wicket:message key="L140M01a.subEmpNm">引介子公司員工姓名</wicket:message>
                                            </td>
                                            <td><input type="text" id="subEmpNm" name="subEmpNm"  maxlength="10" maxlengthC="10" size="18" class='required'/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div id="customerOrCompanyDiv" style="display:none;">
                                    <button type="button" id="importCustOrComButton"><wicket:message key="button.importCustomerOrCompanyInfo">引進客戶/企業資訊</wicket:message></button>
                                    <table>
                                        <tr>
                                            <td class="hd1" nowrap><wicket:message key="L140M01A.introCustId">客戶ID/企業統編</wicket:message></td>
                                            <td>
                                                <input type="text" id="introCustId" name="introCustId" readonly size="8" class='required'/>
                                                <input type="text" id="introDupNo" name="introDupNo" readonly size="2"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="hd1" ><wicket:message key="L140M01A.introCustName">客戶名稱/企業名稱</wicket:message></td>
                                            <td><input type="text" id="introCustName" name="introCustName" class='required' readonly /></td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt">
                                綁約期
                            </td>
                            <td>
                                <select id="createCntrNo_brmp_creditCheck_pConBegEnd_fg" name="createCntrNo_brmp_creditCheck_pConBegEnd_fg" space="true" combokey="pConBegEnd_fg" comboType="2"  class="required" />
                                期
                            </td>
                        </tr>
                    </table>
                </form>
                <span style="color:red;font-size:1.2em">*將重新引進徵信資料及各項欄位計算審核結果，上述三個欄位開放修改，其餘依新增簽報書時輸入之欄位帶入</span>
            </div>
        </wicket:extend>
    </body>
</html>
