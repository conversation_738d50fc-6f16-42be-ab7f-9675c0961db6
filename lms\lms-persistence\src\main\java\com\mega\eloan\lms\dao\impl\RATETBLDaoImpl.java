/* 
 * RATETBLDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.RATETBLDao;
import com.mega.eloan.lms.model.RATETBL;

/** 額度信用評等資料檔 **/
@Repository
public class RATETBLDaoImpl extends LMSJpaDao<RATETBL, String>
	implements RATETBLDao {

	@Override
	public RATETBL findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<RATETBL> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<RATETBL> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public RATETBL findByUniqueKey(String curr, String dataYmd){
		ISearch search = createSearchTemplete();
		if (curr != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "curr", curr);
		if (dataYmd != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataYmd", dataYmd);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<RATETBL> findByIndex01(String curr, String dataYmd){
		ISearch search = createSearchTemplete();
		List<RATETBL> list = null;
		if (curr != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "curr", curr);
		if (dataYmd != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataYmd", dataYmd);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<RATETBL> findByIndex02(String dataYmd){
		ISearch search = createSearchTemplete();
		List<RATETBL> list = null;
		if (dataYmd != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataYmd", dataYmd);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}