/* 
 * L180R02ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L180R02A;

/** 營運中心授權內外已核准已婉卻授信案件 **/
public interface L180R02ADao extends IGenericDao<L180R02A> {

	L180R02A findByOid(String oid);

	List<L180R02A> findByMainId(String mainId);

	List<L180R02A> findByIndex01(String mainId,String docType,String docKind, String approver, String[] brnos);

	List<L180R02A> findByIndex02(String mainId, String areaBranchId,String audit);

	List<L180R02A> findByIndex03(String[] oids, String mainId,String docType,String docKind);
}