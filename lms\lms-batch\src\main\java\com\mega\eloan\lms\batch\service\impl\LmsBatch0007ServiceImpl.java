/* 
 * ADEB001101ServiceImpl.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.batch.service.impl;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.gwclient.AUDITFTPClient;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.DebConfig;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.cls.report.impl.CLS1141R01RptServiceImpl;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S11A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.rpt.pages.LMS9511V01Page;
import com.mega.eloan.lms.rpt.report.LMS9511R01RptService;
import com.mega.eloan.lms.rpt.service.LMS9551R01RptService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 產生傳送卡務檔案 Service
 * </pre>
 * 
 * @since 2012/7/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/17,TammyChen,new
 *          <li>2012/11/29,Sunkist Wang,add interface for spring trasaction aop
 *          config
 *          <li>2012/11/30,Sunkist Wang,but!在LNSPServiceImpl.callSP() throw
 *          Exception 時將會導致這隻批次執行失敗，所以在還沒方案前改回來WebBatchService
 *          <li>2013/1/15,Tammy Chen,#1377 傳送卡務所有檔案內日期，均以YYYY/MM/DD格式顯示
 *          </ul>
 */
@Service("lmsbatch0007serviceimpl")
public class LmsBatch0007ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	DebConfig debConfig;
	@Resource
	LMS9551R01RptService lms9551r01rptservice; // deb1010GService;

	@Resource
	AUDITFTPClient ftpClient;
	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	LMS1201Service service1201;
	@Resource
	LMS1401Service service1401;

	@Resource
	UserInfoService userInfoService;
	@Resource
	ProdService prodService;
	@Resource
	LMS9511R01RptService lms9511r01rptservice;

	@Resource
	CodeTypeService codetypeService;

	final String UTF8 = "UTF-8";
	final String ContentType = "application/octet-stream";
	final String success = "Y";
	final String fail = "N";
	final String TW_DATE_FORMAT_STR = "YYYMMDD";
	final String TW_DATE_FORMAT_STR2 = "YYY/MM/DD";
	final String DATE_FORMAT_STR = "yyyy-MM-dd";
	final String DATE_FORMAT_STR_2 = "yyyy/MM/dd";
	final String DATE_FORMAT_STR_3 = "yyyy.MM.dd";
	final String ADD_DATA = "addList";
	final String SYSTEM = "system";
	File fileDir = null;
	final String getBrNoCol = "ICBCBR_BRNO_BRNM";
	boolean isSuccess = true;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		JSONObject result = null;
		isSuccess = true;
		result = exeMode(json);
		return result;
	}

	/**
	 * 整批第一次執行
	 * 
	 * @param json
	 * @return
	 */
	public JSONObject exeMode(JSONObject json) {
		JSONObject result = null;
		isSuccess = true;

		// 產生主檔

		JSONObject request = json.getJSONObject("request");

		String dataStartDate = Util.trim(request.getString("dataStartDate"));
		String dataEndDate = Util.trim(request.getString("dataEndDate"));

		if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
			final String DATE_FORMAT_STR = "yyyy-MM-dd";
			String currentDate = Util.getLeftStr(
					CapDate.getCurrentDate(DATE_FORMAT_STR), 7)
					+ "-01";
			dataStartDate = Util.getLeftStr(
					CapDate.shiftDaysString(currentDate, DATE_FORMAT_STR, -1),
					7) + "-01";
			dataEndDate = CapDate.shiftDaysString(currentDate, DATE_FORMAT_STR,
					-1);

		}

		String localFilePath = PropUtil.getProperty("docFile.dir")
				+ File.separator + PropUtil.getProperty(

				"systemId") + File.separator + "906" + File.separator
				+ "LMS180R01" + File.separator;

		File bkFolder = new File(localFilePath);

		try {
			if (!bkFolder.exists()) {
				FileUtils.forceMkdir(bkFolder);
			}

			// 將執行完成的檔案備份到bk
			// FileUtils.copyFile(inFile, new File(bkFolder.toString(), file));
			// FileUtils.forceDelete(inFile);
		} catch (IOException ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName()
					+ "執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		List<Map<String, String>> doMonthList = new ArrayList<Map<String, String>>();

		int i = 0;
		int colCount = 0;
		int intExeMonth = 1;

		ArrayList<String> filePath = new ArrayList<String>();
		ArrayList<String> fileName = new ArrayList<String>();

		while (CapDate.addMonth(Util.parseDate(dataStartDate), i).compareTo(
				Util.parseDate(dataEndDate)) <= 0) {
			colCount = colCount + 1;

			String colDate = TWNDate.toAD(CapDate.addMonth(
					Util.parseDate(dataStartDate), i));

			final String DATE_FORMAT_STR = "yyyy-MM-dd";

			String tDataStartDate = Util.getLeftStr(colDate, 7) + "-01";

			String xDataEndDate = TWNDate.toAD(CapDate.addMonth(
					Util.parseDate(dataStartDate), i + intExeMonth));

			String tDataEndDate = CapDate.shiftDaysString(xDataEndDate,
					DATE_FORMAT_STR, -1);

			Map<String, String> txMap = new HashMap<String, String>();

			txMap.put("dataStartDate", tDataStartDate);
			txMap.put("dataEndDate", tDataEndDate);

			doMonthList.add(txMap);

			i = i + intExeMonth;

		}

		for (Map<String, String> doMonthMap : doMonthList) {

			String tDataStartDate = MapUtils.getString(doMonthMap,
					"dataStartDate");

			String tDataEndDate = MapUtils.getString(doMonthMap, "dataEndDate");

			Map resultMap = this.subCreateBatchExcel(localFilePath,
					tDataStartDate, tDataEndDate, filePath, fileName);

			if (resultMap != null && !resultMap.isEmpty()) {
				String tIsSuccess = MapUtils.getString(resultMap, "isSuccess");
				String errMsg = MapUtils.getString(resultMap, "errMsg");
				if (Util.notEquals(tIsSuccess, "Y")) {
					isSuccess = false;
					result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
					result.element(WebBatchCode.P_RESPONSE, this.getClass()
							.getName() + "執行失敗！==>" + errMsg);
					logger.error(errMsg);
					return result;
				}

			}

		}

		try {
			if (filePath != null && !filePath.isEmpty()) {
				Map ftpResultMap = sendToFTP_Batch(filePath, fileName);

				String tIsSuccess = MapUtils.getString(ftpResultMap,
						"isSuccess");
				String errMsg = MapUtils.getString(ftpResultMap, "errMsg");

				if (Util.notEquals(tIsSuccess, "Y")) {
					isSuccess = false;
					result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
					result.element(WebBatchCode.P_RESPONSE, this.getClass()
							.getName() + "執行失敗！==>" + errMsg);
					logger.error(errMsg);
					return result;
				}

			}

		} catch (Exception e) {
			isSuccess = false;
			e.printStackTrace();

			logger.error(e.getMessage());

			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName()
					+ "執行失敗！==>" + e.getMessage());

			return result;

		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;
			result.remove(WebBatchCode.P_RC_MSG);
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, "產檔失敗");
		}

		return result;
	}

	public Map<String, String> subCreateBatchExcel(String localFilePath,
			String tDataStartDate, String tDataEndDate,
			ArrayList<String> filePaths, ArrayList<String> fileNames) {

		boolean isSuccess = true;
		String errMsg = "";
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);

		Properties prop1405 = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		Map<String, String> result = new HashMap<String, String>();
		List<String> outList = null;
		List<Map<String, Object>> elData = null;
		// StringBuffer fileNameList = new StringBuffer("");

		try {

			Map<String, IFormatter> reformat = null;

			String fileName = CapDate.formatDate(
					Util.parseDate(tDataStartDate), "yyyy-MM")
					+ "_"
					+ "企金已敘做授信案件清單_全行";

			File txtFile = new File(localFilePath, fileName + ".csv");

			logger.info(tDataStartDate + "~" + tDataEndDate);

			outList = new ArrayList<String>();

			Map<String, String> subItemeMap = null;
			Map<String, String> subCodeMap = null;
			subCodeMap = prodService.getSubCode();
			subItemeMap = codetypeService.findByCodeType("lms1405m01_SubItem");
			if (subItemeMap == null) {
				subItemeMap = new LinkedHashMap<String, String>();
			}
			if (subCodeMap == null) {
				subCodeMap = new LinkedHashMap<String, String>();
			}
			elData = eloandbBaseService.findforNewReportType2ByBrNo(null,
					CreditDocStatusEnum.海外_已核准.getCode(),
					UtilConstants.Casedoc.DocKind.授權內, tDataStartDate,
					tDataEndDate, FlowDocStatusEnum.已核准.getCode(),
					UtilConstants.Casedoc.typCd.DBU,
					UtilConstants.Casedoc.typCd.OBU);

			// 開始放查出資料
			if (elData != null && !CollectionUtils.isEmpty(elData)) {
				StringBuffer newValBuffer = null;
				for (Map<String, Object> dataMap : elData) {

					newValBuffer = new StringBuffer("");

					String docType = Util.trim(dataMap.get("DOCTYPE"));
					String brId = Util.trim(dataMap.get("CASEBRID"));
					String custId = Util.trim(dataMap.get("CUSTID"));
					String dupNo = Util.trim(dataMap.get("DUPNO"));
					String cntrNo = Util.trim(dataMap.get("CNTRNO"));
					String custName = Util.trim(dataMap.get("CUSTNAME"));
					String lnSubject = Util.trim(subItemeMap.get(Util
							.trim(dataMap.get("LNSUBJECT"))))
							+ (Integer.parseInt(Util.trim(dataMap
									.get("LNSUBJECTCOUNT"))) > 1 ? prop
									.getProperty("L784M01a.less") : "");
					String lnSubject2 = Util.trim(subCodeMap.get(Util
							.trim(dataMap.get("LNSUBJECT2"))))
							+ (Integer.parseInt(Util.trim(dataMap
									.get("LNSUBJECTCOUNT2"))) > 1 ? prop
									.getProperty("L784M01a.less") : "");
					String currentApplyCurr = Util.trim(dataMap
							.get("CURRENTAPPLYCURR"));
					BigDecimal currentApplyAmt = LMSUtil.toBigDecimal(dataMap
							.get("CURRENTAPPLYAMT"));
					String useDeadline = Util.trim(dataMap.get("USEDEADLINE"));
					String desp1 = Util.trim(dataMap.get("DESP1"));
					String property = Util.trim(dataMap.get("PROPERTY"));
					String randomCode = Util.trim(dataMap.get("RANDOMCODE"));
					Date endDate = (Date) dataMap.get("ENDDATE");
					String staffNo = Util.trim(dataMap.get("STAFFNO"));

					String L120M01A_MainId = Util.trim(dataMap.get("MAINID"));
					String L140M01A_MainId = Util
							.trim(dataMap.get("REFMAINID"));

					// l784s01a.setBrId(brId);
					// l784s01a.setCustId(custId);
					// l784s01a.setDupNo(dupNo);
					// l784s01a.setMainId(batchtbl.getMainId());
					// l784s01a.setCntrNo(cntrNo);
					// l784s01a.setCustName(custName);
					// if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {
					// l784s01a.setLnSubject(lnSubject);
					// } else {
					// l784s01a.setLnSubject(lnSubject2);
					// }
					// l784s01a.setCurrentApplyCurr(currentApplyCurr);
					// l784s01a.setCurrentApplyAmt(currentApplyAmt);
					// l784s01a.setUseDeadline(useDeadline);
					// l784s01a.setDesp1(desp1);
					// l784s01a.setProperty(property);
					// l784s01a.setRandomCode(randomCode);
					// l784s01a.setEndDate(endDate);
					// l784s01a.setStaffNo(staffNo);
					// l784s01a.setL120M01A_MainId(L120M01A_MainId);
					// l784s01a.setL140M01A_MainId(L140M01A_MainId);
					// l784s01List.add(l784s01a);

					String crdType = "";
					
					// J-111-0331新增
					String headItem1 = "";// 本額度有無送保
					String gutPercent = "";// 保證成數

					if (Util.isNotEmpty(L120M01A_MainId)
							&& Util.isNotEmpty(L140M01A_MainId)) {
						L120M01A l120m01a = service1201
								.findL120m01aByMainId(L120M01A_MainId);
						String docType120 = l120m01a.getDocType();
						// 企金
						if (Util.nullToSpace(docType120).equals("1")) {
							// L120S01A．借款人主檔
							List<L120S01A> l120s01aList = service1201
									.findL120s01aByMainIdForOrder(L120M01A_MainId);

							L140M01A l140m01a = service1401
									.findL140m01aByMainId(L140M01A_MainId);
							if (l140m01a == null) {
								l140m01a = new L140M01A();
							}
							
							headItem1 = Util.trim(l140m01a.getHeadItem1());
							// 因為有些舊案，非送信保卻還是有信保成數(舊額度明細一直被續約or複製可以達到此效果)
							// 稽核處覺得資料很奇怪，這邊需要加工處理
							if (Util.equals(headItem1, "Y")) {
								if (Util.equals(Util.trim(l140m01a.getGutType()), "3")) {
									// 批次保證因為沒有保證成數，所以預設為100
									gutPercent = "100";
								} else {
									gutPercent = Util.trim(l140m01a.getGutPercent());
								}
							}
							
							List<L120S01C> l120s01cList = service1201
									.findL120s01cByMainId(L120M01A_MainId);
							L120S01A l120s01aFor140 = null;
							for (L120S01A l120s01a : l120s01aList) {
								if (Util.nullToSpace(l120s01a.getCustId())
										.equals(Util.nullToSpace(l140m01a
												.getCustId()))
										&& Util.nullToSpace(
												l120s01a.getCustId()).equals(
												Util.nullToSpace(l140m01a
														.getCustId()))) {
									l120s01aFor140 = l120s01a;
								}
							}
							if (l120s01aFor140 == null)
								l120s01aFor140 = new L120S01A();
							Map<String, String> crdTypeMap = codetypeService
									.findByCodeType("CRDType", LMSUtil
											.getLocale().toString());
							crdType = lms9511r01rptservice.setL120S01CData(
									l120s01aFor140, l120s01cList, crdTypeMap,
									prop1405, l140m01a);
							
							if(crdType.contains("\r")){
								crdType = crdType.replaceAll("\r", "");
							}
						}
						// 消金
						else {
							crdType = "";
						}
					}
					
					// J-111-0331 開始
					// 為稽核業務之需，原按月提供近一年(上年度1月至今年度最近月份)分行授權內企金業務
					// 「已敘做授信案件清單」月報予稽核處，增列欄位「合併關係企業授信額度」等。
					// 1.加合併關係企業額度三個欄位
					String l120s11aTot = "";// 合併關係企業授信合計(TWD)
					String l120s11aTotS = "";// 合併關係企業有擔合計(TWD)
					String l120s11aTotN = "";// 合併關係企業無擔合計(TWD)
					if (Util.isNotEmpty(L120M01A_MainId)) {
						// 拿合計99999那一筆
						// L120S11A取出的三個欄位皆為台幣幣別不需換算
						L120S11A totL120s11a = service1201.findL120s11aByMainIdCsutIdItemSeq(
								L120M01A_MainId, custId, dupNo, 99999);// 合計的那一筆關係企業
						// 合計那一筆正常都要有，但有擔無擔是新增欄位，可能會沒有值
						if (totL120s11a != null) {
							if (totL120s11a.getFactAmt() != null) {
								l120s11aTot = Util.trim(totL120s11a.getFactAmt());
							}
							if (totL120s11a.getFactAmtS() != null) {
								l120s11aTotS = Util.trim(totL120s11a.getFactAmtS());
							}
							if (totL120s11a.getFactAmtN() != null) {
								l120s11aTotN = Util.trim(totL120s11a.getFactAmtN());
							}
						}
					}
					
					// 2.加疑似逾越授權
					// J-111-0283  E-LOAN簽報，關係企業額度已逾分行權限，在案件簽報畫面明顯位置處，加註「疑似逾越授權」之註記
					String overAuth = "";
					if (Util.isNotEmpty(L120M01A_MainId)) {
						L120M01I l120m01i = service1201.findL120m01iByMainId(L120M01A_MainId);
						if(l120m01i != null){
							if ("Y".equals(Util.trim(l120m01i.getOverAuthLoan()))
									|| "Y".equals(Util.trim(l120m01i
											.getOverAuthExperf()))
									|| "Y".equals(Util.trim(l120m01i
											.getOverAuthAloneLoan()))) {
								overAuth = "Y";
							}
						}
					}
					// J-111-0331 結束
					
					

					newValBuffer.append("\"");
					newValBuffer.append(CapDate.formatDate(endDate,
							"yyyy-MM-dd")); // 核定日
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(custId); // 統編
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(custName); // 戶名
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(cntrNo); // 額度序號
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(lnSubject); // 授信科目
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(currentApplyCurr); // 額度幣別
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(currentApplyAmt); // 額度金額
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer
							.append(LMSUtil.getUseDeadline(
									Util.trim(useDeadline),
									Util.trim(desp1),
									MessageBundleScriptCreator
											.getComponentResource(CLS1141R01RptServiceImpl.class))); // 期間
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer
							.append(LMSUtil.getProPerty(property, prop1405)); // 敘作續約或變更條件
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(Util.trim(userInfoService
							.getUserName(staffNo))); // 核准人
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(crdType); // 信用評等
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(l120s11aTot); // 合併關係企業授信額度
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(l120s11aTotS); // 合併關係企業有擔額度
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(l120s11aTotN); // 合併關係企業無擔額度
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(overAuth); // 逾權註記
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(headItem1); // 本案是否移送信保基金保證 
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(gutPercent); // 保證成數
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(randomCode); // 報表亂碼
					newValBuffer.append("\"");
					outList.add(newValBuffer.toString());
					newValBuffer = null;

				}

				elData.clear();
			}

			if (outList != null) {

				filePaths.add(txtFile.toString());
				fileNames.add(fileName + ".csv");

				FileUtils.writeLines(txtFile, "UTF8", outList);

				outList.clear();
			}

		} catch (Exception e) {
			isSuccess = false;
			e.printStackTrace();
			errMsg = e.getMessage();
			logger.error(e.getMessage());
		}

		result.put("isSuccess", isSuccess ? "Y" : "N");
		result.put("errMsg", errMsg);
		// result.put(
		// "fileName",
		// Util.notEquals(fileNameList.toString(), "") ? fileNameList
		// .toString() : "");

		return result;

	}

	/**
	 * 傳送檔案至卡務中心
	 * 
	 * @param meta
	 *            D201S99C
	 * @return D201S99C
	 * @throws CapException
	 */
	public Map<String, String> sendToFTP_Batch(ArrayList<String> filePath,
			ArrayList<String> fileName) throws CapException {

		boolean isSuccess = true;
		String errMsg = "";
		// ArrayList<String> filePath = new ArrayList<String>();
		// ArrayList<String> fileName = new ArrayList<String>();
		Map<String, String> result = new HashMap<String, String>();
		ftpClient.test();
		try {

			// 後台管理->系統設定維護->LMS_ArCtrlCanShow
			// Map<String, Object> onlineDateMap = eloandbBaseService
			// .getSysParamData("UCCFTP_DEF_DIR");
			//
			// String serverDir = Util.trim(onlineDateMap.get("PARAMVALUE"));
			String msgId = IDGenerator.getUUID();
			String serverDir = ftpClient.getServerDir();
			boolean isBinaryFile = true;
			boolean delFtpFile = true;
			boolean isAddDate = false;

			ftpClient.send(msgId, filePath.toArray(new String[] {}),
					serverDir, // dw/ftpdata/ftpucc1/ftpout/
					// debConfig.getUCCFTPUploadPath(), // 切換路徑至：/ftpout/
					fileName.toArray(new String[] {}), isBinaryFile,
					delFtpFile, isAddDate);
		} catch (Exception e) {
			isSuccess = false;
			e.printStackTrace();
			errMsg = e.getMessage();
			logger.error(e.getMessage());
		}

		result.put("isSuccess", isSuccess ? "Y" : "N");
		result.put("errMsg", errMsg);
		return result;
	}

	class BrFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		String custId = "";
		String applyDate = "";

		public BrFormatter(String custId, String applyDate) {
			this.custId = custId;
			this.applyDate = applyDate;
		}

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			Map<String, Object> misData = (Map<String, Object>) in;
			StringBuffer sb = new StringBuffer();
			return sb.toString();
		}
	}

	class DateFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		String fromFormat = TW_DATE_FORMAT_STR;
		String toFormat = DATE_FORMAT_STR_2;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String val = (String) in;
			if (!CapString.isEmpty(val)) {
				if (CapDate.isMatchPattern(val, TW_DATE_FORMAT_STR2)) {
					fromFormat = TW_DATE_FORMAT_STR2;
				} else if (CapDate.isMatchPattern(val, DATE_FORMAT_STR)) {
					fromFormat = DATE_FORMAT_STR;
				}
				return CapDate.formatDateFromF1ToF2(val, fromFormat, toFormat);
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

	class DateYMFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		String fromFormat = TW_DATE_FORMAT_STR;
		String toFormat = "yyyy/MM";

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String val = (String) in;
			if (!CapString.isEmpty(val)) {
				return CapDate.formatDateFromF1ToF2(val + "01", fromFormat,
						toFormat);
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

	class BigDecimalFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String val = (String) in;
			if (!CapString.isEmpty(val)) {
				BigDecimal dec = in instanceof BigDecimal ? (BigDecimal) in
						: CapMath.getBigDecimal((String) in);
				String re = dec.stripTrailingZeros().toPlainString();
				if ("0.00".equals(re)) {
					re = "0";
				}
				return re;
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

}
