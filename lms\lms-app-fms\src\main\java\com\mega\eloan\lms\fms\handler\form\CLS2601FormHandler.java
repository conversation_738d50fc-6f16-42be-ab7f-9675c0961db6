package com.mega.eloan.lms.fms.handler.form;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.fms.flow.CLS2601Flow;
import com.mega.eloan.lms.fms.pages.CLS2601M01Page;
import com.mega.eloan.lms.fms.service.CLS2601Service;
import com.mega.eloan.lms.model.C900M01H;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;


@Scope("request")
@Controller("cls2601formhandler")
public class CLS2601FormHandler extends AbstractFormHandler {

	@Resource
	CLS2601Service service;

	@Resource
	CLSService clsService;
	
	@Resource
	ICustomerService iCustomerService;
	
	@Resource
	UserInfoService userInfoService;

	@Resource 
	FlowSimplifyService flowSimplifyService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	UserInfoService userService;
	
	@Resource
	BranchService branchService;
	
	Properties prop = MessageBundleScriptCreator
		.getComponentResource(CLS2601M01Page.class);

	Properties prop_AbstractEloanPage = MessageBundleScriptCreator
	.getComponentResource(AbstractEloanPage.class);
	
	@DomainAuth(AuthType.Modify)
	public IResult deleteMark(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String KEY = "saveOkFlag";
		
		String list = params.getString("list");
		String docStatus = params.getString("docStatus");
		
		result.set(KEY, false);
		
		C900M01H meta = null;
		if (Util.isNotEmpty(list)) {
			meta = service.findC900M01H_oid(list);	
			
			if(meta!=null){
				
				List<DocOpener> docOpeners = docCheckService.findByMainId(meta.getMainId());
				for(DocOpener docOpener : docOpeners){
					if(OpenTypeCode.Writing.getCode().equals(docOpener.getOpenType())){
						HashMap<String, String> hm = new HashMap<String, String>();
						hm.put("userId", docOpener.getOpener());
						hm.put("userName",
								userInfoService.getUserName(docOpener.getOpener()));
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0009", hm), getClass());
					}
				}
				
				//判斷編制中刪除，其它 更新DeletedTime
				if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
					flowSimplifyService.flowCancel(meta.getOid());
					service.delete(meta);
					// 刪除文件異動記錄
					docLogService.deleteLog(meta.getOid());
				}
				result.set(KEY, true);
			}			
		}
		return defaultResult(params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify)
	public C900M01H newC900M01H(PageParameters params)
			throws CapException {
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		
		String custName = Util.trim(params.getString("custName"));
	
		C900M01H meta = new C900M01H();
		
		//---	
		
		meta.setMainId(IDGenerator.getUUID());
		meta.setCustName(custName);
		meta.setOwnBrId(user.getUnitNo());
		meta.setUnitType(user.getUnitType());		
		meta.setCreator(user.getUserId());
		meta.setCreateTime(nowTS);
		meta.setUpdater(user.getUserId());
		meta.setUpdateTime(nowTS);
		meta.setIsClosed(false);
		meta.setRandomCode(IDGenerator.getRandomCode());
		CapBeanUtil.map2Bean(params, meta, new String[] {"agentCertYear","agentCertWord","agentCertNo","ctlFlag", "memo","estateAgentFlag","record17Flag"
		});
		
		//---
		
		
		return meta;
		
	}

	private CapAjaxFormResult defaultResult(PageParameters params, C900M01H meta,
			CapAjaxFormResult result) throws CapException {		
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		UserNameFormatter userNameFormatter;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C900M01H meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = service.findC900M01H_oid(mainOid);	
			
			LMSUtil.addMetaToResult(result, meta, new String[]{"custId", "dupNo", "custName", "isClosed"
					, "createTime", "updateTime","agentCertYear","agentCertWord","ctlFlag","memo","ownBrId","estateAgentFlag","record17Flag"});
			result.set("agentCertNo", Util.trim(meta.getAgentCertNo()));
			result.set("creator", Util.trim(userInfoService.getUserName(meta.getCreator())));
			result.set("updater", Util.trim(userInfoService.getUserName(meta.getUpdater())));
			result.set("ownBrIdDesc", meta.getOwnBrId() + branchService.getBranchName(meta.getOwnBrId()));
			
			String docStatus = meta.getDocStatus();			
			if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.010");
			}else if(Util.equals(FlowDocStatusEnum.待覆核.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.020");
			}else if(Util.equals(FlowDocStatusEnum.已核准.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.030");
			}else if(Util.equals(FlowDocStatusEnum.待解除.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.0C0");
			}else if(Util.equals(FlowDocStatusEnum.已解除.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.0D0");
			}
			result.set("docStatus", docStatus);
			
			
		}else{
			meta = new C900M01H();
			// common 欄位塞值
			meta.setDocStatus(FlowDocStatusEnum.編製中);
			
			//新增,畫面預設值
			result.set("agentCertWord", "台內地登");
			result.set("docStatus", prop_AbstractEloanPage.getProperty("docStatus.010"));
			result.set("ownBrId", user.getUnitNo());
			result.set("ownBrIdDesc", user.getUnitNo() + branchService.getBranchName(user.getUnitNo()));
			// 文件異動紀錄區
			userNameFormatter = new UserNameFormatter(userService);
			result.set("creator", userNameFormatter.reformat(user.getUserId()));
			
		}
		
		return defaultResult(params, meta, result);
	}	
	
	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,String tempSave)
	throws CapException{
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		//===
		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C900M01H meta = null;
		if (Util.isNotEmpty(mainOid)) {
			//Update
			try{
				meta = service.findC900M01H_oid(mainOid);
				//---
				CapBeanUtil.map2Bean(params, meta, new String[] {"custName","agentCertYear","agentCertWord","agentCertNo","ctlFlag", "memo","estateAgentFlag","record17Flag"
				});
				//---
				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(CapDate.getCurrentTimestamp());
				meta.setRandomCode(IDGenerator.getRandomCode());
				
				service.save(meta);				
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}
			result.add(query(params));
		}else{
			//Insert
			try{
				meta = newC900M01H(params);
				service.save(meta);
				mainOid = meta.getOid();
				flowSimplifyService.flowStart(CLS2601Flow.FLOW_CODE, meta.getOid(), user.getUserId(), user.getUnitNo());
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}
			
			//重新撈資料
			meta = service.findC900M01H_oid(mainOid);	
			
			LMSUtil.addMetaToResult(result, meta, new String[]{"custId", "dupNo", "custName", "isClosed"
					, "createTime", "updateTime","agentCertYear","agentCertWord","ctlFlag","memo","estateAgentFlag","record17Flag"});
			result.set("agentCertNo", Util.trim(meta.getAgentCertNo()));
			result.set("creator", Util.trim(userInfoService.getUserName(meta.getCreator())));
			result.set("updater", Util.trim(userInfoService.getUserName(meta.getUpdater())));
			
			String docStatus = meta.getDocStatus();			
			if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.010");
			}else if(Util.equals(FlowDocStatusEnum.待覆核.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.020");
			}else if(Util.equals(FlowDocStatusEnum.已核准.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.030");
			}else if(Util.equals(FlowDocStatusEnum.待解除.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.0C0");
			}else if(Util.equals(FlowDocStatusEnum.已解除.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.0D0");
			}
			result.set("docStatus", docStatus);
			
			// common 要求塞值欄位
			result.set(EloanConstants.MAIN_OID, meta.getOid());
			result.set(EloanConstants.MAIN_ID, meta.getMainId());
			result.set(EloanConstants.MAIN_UID, meta.getUid());
			result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());			
			result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
			
		}
		
		if(meta!=null){
			if(!Util.equals(UtilConstants.BankNo.授管處, user.getUnitNo()) 
					&& Util.equals("4", meta.getCtlFlag())){
				Map<String, String> descMap = clsService.get_codeTypeWithOrder("cls260CtlFlagType",
						LocaleContextHolder.getLocale().toString());
				
				String message = MessageFormat.format(prop.getProperty("msg.01")
						, LMSUtil.getDesc(descMap, "4")
						, "授管處");
				throw new CapMessageException(message, getClass());
			}
		}
		return result;
	}
	
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C900M01H meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = service.findC900M01H_oid(mainOid);
			
			String errMsg = "";
			if(Util.equals("核定", decisionExpr) || Util.equals("解除核定", decisionExpr)){
				//檢查經辦和主管是否為同一人
				if(Util.equals(user.getUserId(), meta.getUpdater())){
					errMsg = RespMsgHelper.getMessage("EFD0053");
				}			
			}
			if(Util.isNotEmpty(errMsg)){
				throw new CapMessageException(errMsg, getClass());
			}
			
			flowSimplifyService.flowNext(meta.getOid(), decisionExpr);
			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
}
