---------------------------------------------------------
-- LMS.L999M01D 企金約據書項目描述檔
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L999M01D;
CREATE TABLE LMS.L999M01D (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	ITEMTYP<PERSON>      CHAR(1)       not null,
	ITEMCONTENT   VARCHAR(3072),
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>    TIMESTAMP    ,

	constraint P_L999M01D PRIMARY KEY(OID)
) IN  EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL999M01D01;
CREATE UNIQUE INDEX LMS.XL999M01D01 ON LMS.L999M01D   (MAINID, ITEMTYPE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L999M01D IS '企金約據書項目描述檔';
COMMENT ON LMS.L999M01D (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	ITEMTYPE      IS '項目', 
	ITEMCONTENT   IS '說明', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
