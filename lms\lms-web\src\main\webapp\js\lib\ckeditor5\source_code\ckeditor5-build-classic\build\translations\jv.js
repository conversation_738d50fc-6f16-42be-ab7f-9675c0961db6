!function(a){const e=a.jv=a.jv||{};e.dictionary=Object.assign(e.dictionary||{},{"%0 of %1":"%0 saking %1","Align center":"Rata tengah","Align left":"<PERSON>a kiwa","Align right":"Rata tengen",Big:"Ageng",Bold:"Kandhel","Break text":"","Bulleted List":"",Cancel:"Batal","Cannot upload file:":"Mboden saged ngirim berkas:","Caption for image: %0":"","Caption for the image":"","Centered image":"Gambar ing tengah","Change image text alternative":"","Choose heading":"","Could not insert image at the current position.":"Mboten saged mlebetaken gambar wonten papan menika","Could not obtain resized image URL.":"Mboten saged mundhut URL gambar ingkang dipunebah ukuranipun",Default:"Default","Document colors":"Warni dokumen","Enter image caption":"","Font Background Color":"Warni Latar Aksara","Font Color":"Warni aksara","Font Size":"Ukuran aksara","Full size image":"Gambar ukuran kebak",Heading:"","Heading 1":"","Heading 2":"","Heading 3":"","Heading 4":"","Heading 5":"","Heading 6":"","HTML object":"Obyek HTML",Huge:"Langkung ageng","Image resize list":"","Image toolbar":"","image widget":"","In line":"",Insert:"Tambah","Insert image":"Tambahaken gambar","Insert image or file":"Tambahaken berkas utawi gambar","Insert image via URL":"Tambah gambar saking URL","Inserting image failed":"Gagal mlebetaken gambar",Italic:"Miring",Justify:"Rata kiwa tengen","Left aligned image":"Gambar ing kiwa","Numbered List":"",Original:"Asli",Paragraph:"","Remove color":"Busek warni","Resize image":"","Resize image to %0":"","Resize image to the original size":"","Restore default":"Mangsulaken default","Right aligned image":"Gambar ing tengen",Save:"Rimat","Selecting resized image failed":"Gagal milih gambar ingkang dipunebah ukuranipun","Show more items":"Tampilaken langkung kathah","Side image":"",Small:"Alit",Strikethrough:"Seratan dicoret","Text alignment":"Perataan seratan","Text alignment toolbar":"","Text alternative":"",Tiny:"Langkung alit",Underline:"Garis ngandhap",Update:"","Update image URL":"","Upload failed":"","Wrap text":""}),e.getPluralForm=function(a){return 0}}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));