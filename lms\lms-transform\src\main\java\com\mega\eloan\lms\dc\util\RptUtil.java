/**
 * 
 */
package com.mega.eloan.lms.dc.util;

import java.io.BufferedReader;

import java.io.FileInputStream;
import java.io.InputStreamReader;


/**
 * 在產生各式報表或log會用到的util
 * 
 * <AUTHOR>
 * @since 2013/01/29
 * 
 * 
 */
public class RptUtil {




	public static void main(String[] args) {

		try {



		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	 public RptUtil(){
		 
	 }


	/**
	 * 取得指定檔案的行數
	 * 
	 * @param filepath
	 * @return
	 */
	public static int getFileLen(String filepath) {
		int count = 0;
		try {
			BufferedReader br = new BufferedReader(new InputStreamReader(
					new FileInputStream(filepath)));
			while (br.readLine() != null) {
				count++;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count;
	}



	// 將字串靠左，右補空白
	public static String fillL(String oldstr, int len) {
		return fillSpace(oldstr, len, "L");
	}

	// 將字串靠右，左補空白
	public static String fillR(int oldstr, int len) {
		return fillSpace(String.valueOf(oldstr), len, "R");
	}

	/**
	 * 指定在字串左右邊加空白
	 * 
	 * @param oldstr
	 * @param len
	 * @param side
	 * @return
	 */
	public static String fillSpace(String oldstr, int len, String side) {
		len = len - oldstr.length();
		StringBuffer sb = new StringBuffer();
		int i = 0;
		if (side.equalsIgnoreCase("L")) {
			sb.append(oldstr);
			while (i < len) {
				sb.append(" ");
				i++;
			}
		} else if (side.equalsIgnoreCase("R")) {
			while (i < len) {
				sb.append(" ");
				i++;
			}
			sb.append(oldstr);
		}
		return sb.toString();
	}

}
