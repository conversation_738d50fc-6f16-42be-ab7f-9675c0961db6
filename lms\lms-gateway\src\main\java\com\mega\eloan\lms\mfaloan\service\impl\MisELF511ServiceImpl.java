/* 
 * MisELF501ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisELF511Service;

/**
 * <pre>
 * 消金額度介面檔 MIS.ELF501
 * </pre>
 * 
 * @since 2013/1/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/17,REX,new
 *          </ul>
 */
@Service
public class MisELF511ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF511Service {

	@Override
	public List<Map<String, Object>> selStop1(String custId) {
		return this.getJdbc().queryForList("ELF511.selStop2",
				new String[] { custId });
	}

	@Override
	public List<Map<String, Object>> selStop2(String brno) {
		return this.getJdbc().queryForList("ELF511.selStop1",
				new String[] { brno });
	}

	@Override
	public List<Map<String, Object>> selStop3(String custId, String brno) {
		return this.getJdbc().queryForList("ELF511.selStop3",
				new String[] { brno, custId });
	}

}
