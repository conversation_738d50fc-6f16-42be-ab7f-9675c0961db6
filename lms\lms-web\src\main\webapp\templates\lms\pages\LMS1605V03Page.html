<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
	      <div id="filterBox" style="display:none">
	      	<form id="filterForm" name="filterForm">
	      	<table>
		      <label><b><th:block th:text="#{'L160M01A.message01'}"><!-- 請輸入欲查詢紀錄--></th:block>：</b></label>
		      <tbody>
		        <tr>
		          <td>
		          	<label><input type="radio" name="queryData" value="1" checked="checked"/>
		            <th:block th:text="#{'L160M01A.mainCustId'}"><!--主要借款人統編--></th:block></label>&nbsp;&nbsp;&nbsp;&nbsp;
		            <label><input type="radio" name="queryData" value="2"/>
		         	<th:block th:text="#{'L160M01A.message02'}"><!--核定動用日期範圍--></th:block></label>
				  </td>
		        </tr>
		        <tr id="queryDataTr1">
		        <td >
		        	<input id="custId" name="custId" type="text" size="14" maxlength="11"  class="upText required"/>
		            <span class="text-red"><th:block th:text="#{'L160M01A.custId'}"></th:block>+<th:block th:text="#{'L160M01A.dupNo'}"></th:block>&nbsp;&nbsp;ex:A1234567890</span>
				</td>
		        </tr>
		        <tr id="queryDataTr2" style="display:none">
		          <td >
		          	  <input id="fromDate" name="fromDate" type="text" class="date required" size="8" maxlength="10" />~<input id="endDate"  name="endDate" type="text" class="date required" size="8" maxlength="10" />
		             <span class="text-red">ex:YYYY-MM-DD</span> 
				  </td>
		        </tr>
		      </tbody>
		    </table>
			</form>
		  </div>
		  
		  <div id="UseFirstBox" style="display:none">
	      		<div id="gridviewUseFirst" ></div>
		  </div>
		  
		  <div id="cntrCaseBox" style="display:none">
	      		<div id="gridviewCase" ></div>
		  </div>
		  
		  <div id="logeINBox" style="display:none">
		  	<form id="L160M01AForm">
			  	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
			  		<tr>
			  			<td class="hd1"  width="50%"><th:block th:text="#{'L160M01A.mainCust'}"><!--主要借款人--></th:block>&nbsp;&nbsp;</td>
						<td width="50%"><span id="custId" ></span>
	                            		　 <th:block th:text="#{'L160M01A.dupNo'}"><!--重覆序號--></th:block>：
										  <span id="dupNo" ></span>
	                                    <br>
	                                    (<span id="typCd" class="text-red"></span>)<span id="custName" ></span>
						</td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'L160M01A.caseNo'}"><!--案號--></th:block>&nbsp;&nbsp;</td>
						<td><span id="caseNo"></span></td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'L163M01A.waitingItem'}"><!--代辦事項--></th:block>&nbsp;&nbsp;</td>
						<td><span id="waitingItem" ></span></td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'L160M01A.willFinishDate'}"><!--預定補全日--></th:block>&nbsp;&nbsp;</td>
						<td><span id="willFinishDate" ></span></td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'L163M01A.finishDate'}"><!--辦妥日期--></th:block>&nbsp;&nbsp;</td>
						<td><input type="text" id="finishDate" name="finishDate" class="date required" size="10" maxlength="10" ></td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'L163M01A.itemTrace'}"><!--追蹤辦理情形--></th:block>&nbsp;</td>
						<td><textarea id="itemTrace" name="itemTrace" class="required txt_mult" cols="50" rows="6"  maxlengthC="256"></textarea></td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'L160M01A.managerId'}"><!--甲級主管--></th:block>&nbsp;</td>
						<td>
							<select id="managerId" name="managerId" class="required"></select>
						 	<input type="text" id="managerNm" name="managerNm" size="10" maxlength="30" maxlengthC="10" class="required" style="display:none">
						</td>
			  		</tr>
			  	</table>
			</form>
		  </div>
	</th:block>
</body>
</html>
