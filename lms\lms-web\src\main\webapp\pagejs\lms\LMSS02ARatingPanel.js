initDfd.done(function(json){
	var val_L120M01A_ratingFlag = $("#val_L120M01A_ratingFlag").val()||'';
	
	
	
	if(true){
		//之前海外簽報書的 LMSS02APanel
		//控制 button 是否隱藏，是寫在  LMSM01Page.js 的 setReadOnly1
		if(!$("#buttonPanel").find("#btnSave").is("button") ) {	
			$("div#lmss02aratingpanelFuncContainer.funcContainer").find("button").hide();
            thickboxOptions.readOnly = true;
		}
	}
	
	//先抓版本訊息
	$.ajax({							
		type : "POST", handler : 'lms1115formhandler',
		data : { 
			formAction : "getVarverFormC121M01A", 
			'mainId' : responseJSON.mainId
		},
		success:function(responseData){
			var _grid_house_fRating = "";
			var _grid_notHouse_fRating = "";
			var _grid_varVer = "";
			var _grid_cmsLocation_hidden = false;
			var _grid_notHouse_fRating_hidden = false;
			var house_fRating_title = i18n.lmss02arating["label.fRating"];
			
			if(val_L120M01A_ratingFlag=="JP"){
				_grid_varVer = "c121m01b.varVer";
				_grid_cmsLocation_hidden = false;
				if(responseData.varVer == "2.0"){
					_grid_house_fRating = "c121m01b.fRating";
					_grid_notHouse_fRating = "c121m01f.fRating";
					house_fRating_title = i18n.lmss02arating["label.house.fRating"];
				}else{
					_grid_house_fRating = "c121m01b.fRating";
					_grid_notHouse_fRating_hidden = true;
				}
			}else if(val_L120M01A_ratingFlag=="AU"){
				_grid_varVer = "c121m01c.varVer";
				_grid_cmsLocation_hidden = true;
				if(responseData.varVer == "3.0"){
					_grid_house_fRating = "c121m01c.fRating";
					_grid_notHouse_fRating = "c121m01g.fRating";
					house_fRating_title = i18n.lmss02arating["label.house.fRating"];
				}else{
					_grid_house_fRating = "c121m01c.fRating";
					_grid_notHouse_fRating_hidden = true;
				}
			}else if(val_L120M01A_ratingFlag=="TH"){
				_grid_varVer = "c121m01d.varVer";
				_grid_cmsLocation_hidden = false;
				if(responseData.varVer == "2.0"){
					_grid_house_fRating = "c121m01d.fRating";
					_grid_notHouse_fRating = "c121m01h.fRating";
					house_fRating_title = i18n.lmss02arating["label.house.fRating"];
				}else{
					_grid_house_fRating = "c121m01d.fRating";
					_grid_notHouse_fRating_hidden = true;
				}
			}
			
			var ratingPanelGrid = $("#ratingPanelGrid").iGrid({		//借款人基本資料GridView
				handler : 'lms1205gridhandler',
				height : 280,
				postData : {
					formAction : "queryC120Rating",
					'caseId' : responseJSON.mainId,
					rowNum:10
				},
				rownumbers:true,
				rowNum:10,
				sortname: 'ratingKind|ratingDesc|mainId|keyMan|custPos|custId',
				sortorder: 'asc|asc|asc|desc|asc|asc',
				colModel : [{
						colHeader : ' ', width : 15, name : 'keyMan', sortable : true, 'align':'center'
					}, {
						colHeader : i18n.lmss02arating["l120s01a.custid"],
						name : 'custId', align : "left", width : 80, sortable : true, formatter : 'click', onclick : openDocC120
					}, {
						colHeader : ' ',
						name : 'dupNo', align : "dupNo", width : 6, sortable : true
					}, {
						colHeader : i18n.lmss02arating["l120s01a.custname"],
						name : 'custName', align : "left", width : 100, sortable : true
					}, {
						colHeader : i18n.lmss02arating["label.caseNo"],
						name : 'ratingDesc', align : "left", width : 170, sortable : true, formatter : 'click', onclick : openDocC121FromC120M01A_oid
					}, {
						colHeader : house_fRating_title,
						name : _grid_house_fRating, align : "left", width : 50, sortable : false
					}, {
						colHeader : i18n.lmss02arating["label.notHouse.fRating"],
						name : _grid_notHouse_fRating, align : "left", width : 50, sortable : false,
						hidden : _grid_notHouse_fRating_hidden
					}, {
						colHeader : i18n.lmss02arating["label.varVer"],
						name : _grid_varVer, align : "left", width : 50, sortable : false
					}, {
						colHeader : i18n.lmss02arating["label.lnPeriod"],
						name : 'lnPeriod', align : "left", width : 80, sortable : false
					}, {
						colHeader : i18n.lms1015v01["label.cmsLocation"],
						name : 'cmsLocation', align : "left", width : 200, sortable : false,
						hidden : _grid_cmsLocation_hidden
					}, {
						colHeader : ' ', width : 15, name : 'o_chkYN', sortable : true
					}, {
						colHeader : "oid", name : 'oid',hidden : true
					}, {
						colHeader : "mainId", name : 'mainId',hidden : true
					}, {
						colHeader : "ratingKind", name : 'ratingKind',hidden : true
					}],
				ondblClickRow : function(rowid) { 
					
				}
			});	

		}
	})
	
	
	
	$("#btnImpRatingDoc").click(function(){
		impRatingDoc();
	});
	$("#btnDelRatingDoc").click(function(){
		delRatingDoc();
	});
	$("#btnChooseKeyBorrower").click(function(){
		chooseKeyBorrower(true);
	});
	$("#btnAddOtherCust").click(function(){
		var formId = "addborrowForm";
		var $addborrow = $("#"+formId);
		$addborrow.reset();
		$addborrow.find("input[name=rborrowA]:checked").trigger('click');
		/*
		
		 在 thickbox 的 div 內，包含 addborrowForm
		
		     addborrowForm 內的  btn 引進，參考 $("#getCustData").click(function(){
		
		*/		
		$("#thickboxaddborrow").thickbox({		
			title : '', width : 800, height : 380, modal : true, i18n:i18n.def,
			buttons : {				
				"close" : function(){
					 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
				     });
				}
			}
		});
	});
	
	$("#getCustData").click(function(){
		var formId = "addborrowForm";
		var $addborrow = $("#"+formId);
		
		var $custId = $addborrow.find("[name=addborrowForm_custId]").val();
		var $custName = $addborrow.find("[name=addborrowForm_custName]").val();
		if(($custId != null && $custId != undefined && $custId != '')
		&& ($custName != null && $custName != undefined && $custName != '')){
			// 統一編號、名稱擇一輸入引進即可
			CommonAPI.showErrorMessage(i18n.lmss02arating["l120s02.alert26"]);
		}else if(($custId == null || $custId == undefined || $custId == '')
		&& ($custName == null || $custName == undefined || $custName == '')){
			// 請輸入統一編號或名稱
			CommonAPI.showErrorMessage(i18n.lmss02arating["l120s02.alert27"]);
		}else{
		    var defaultOption = {};
			if($custId != null && $custId != undefined && $custId != ''){
				defaultOption = {
					defaultValue: $custId //預設值 
				};
			}else{
				defaultOption = {
					defaultName : $custName
				};				
			}			
			//綁入MegaID
			CommonAPI.openQueryBox(
				$.extend({
					defaultCustType : ($custId != null && $custId != undefined && $custId != '') ? "1" : ($custName != null && $custName != undefined && $custName != '') ? "2" : "",
	                divId: formId, //在哪個div 底下
	                isInSide:false, 
	                autoResponse: { // 是否自動回填資訊 
	                       id: "addborrowForm_custId", // 統一編號欄位ID 
	                       dupno: "addborrowForm_dupNo", // 重覆編號欄位ID 
	                       name: "addborrowForm_custName" // 客戶名稱欄位ID 
	                },fn:function(obj){	
	                	/*
						obj.custid "A123456789"
						obj.dupno "0"
						obj.name "TESTT"
						obj.buscd 	"060000"
	                	*/
						if( $addborrow.valid()){
							$.ajax({							
								type : "POST", handler : 'lms1115formhandler',
								data : { formAction : "caseDocAddOtherCust", 
									custId: obj.custid, dupNo: obj.dupno, custName: obj.name, busCode: obj.buscd
								},
								success:function(responseData){
									$("#ratingPanelGrid").trigger("reloadGrid");
									
									call_z_grid_openDoc(responseData.c120m01a_oid, true, true, val_L120M01A_ratingFlag);
									
									$.thickbox.close();		
								}
							}).fail(function(){ $addborrow.reset(); });						
						}
					}
				},defaultOption)
			);			
		}
	});	
	
	/*
	 引入時，可用 custId 來篩選
	 */
	$("#findRatingIdBt").click(function(){	
		var search_custId = $("#findRatingId").val();
		var grid_id = "grid_fromRatingDoc";
		
		var my_post_data = {
			formAction : "queryFromRatingDoc"		
			, search_custId: search_custId
		};
		
		
		$("#"+grid_id).jqGrid("setGridParam", {
			postData : my_post_data,
			search : true
		}).trigger("reloadGrid");			
	});
	
	function impRatingDoc(){		
		var grid_id = "grid_fromRatingDoc";
			
		var my_post_data = {
			formAction : "queryFromRatingDoc"
		};
		var grid_height = 280;
		if($("#"+grid_id+".ui-jqgrid-btable").length >0){
			$("#"+grid_id).jqGrid("setGridParam", {
				postData : my_post_data,
				search : true
			}).trigger("reloadGrid");	        		
		}else{
			var _grid_cmsLocation_hidden = false;
			
			if(val_L120M01A_ratingFlag=="AU"){
				_grid_cmsLocation_hidden = true;
			}else if(val_L120M01A_ratingFlag=="TH"){
				_grid_cmsLocation_hidden = false;
			}  
			
			$("#"+grid_id).iGrid({
				handler : 'lms1015gridhandler',
				height : grid_height,
				postData : my_post_data,		
				sortname: 'approveTime|ratingDate|caseNo',
				sortorder: 'desc|desc|desc',
				colModel : [{ name : 'oid', hidden : true}
				,{name : 'mainId',hidden : true}
				,{name : 'ratingId',hidden : true}
				,{colHeader : i18n.lms1015v01["C121M01A.ratingDate"], width : 80, name : 'ratingDate', sortable : true}
				,{colHeader : i18n.lms1015v01["C121M01A.caseNo"], width : 190, name : 'caseNo', sortable : true, formatter : 'click', onclick : openDocC121FromChooseTb}
				,{colHeader : i18n.lms1015v01["C121M01A.custId"], width:90, name:'custId' }
				,{colHeader : ' ', width : 10, name : 'dupNo', sortable : true}
				,{colHeader : i18n.lms1015v01["C121M01A.custName"], width : 140, name : 'custName', sortable : true}
				,{colHeader : i18n.lms1015v01["label.lnPeriod"],name : 'lnPeriod', align : "left", width : 80, sortable : false}
				,{colHeader : i18n.lms1015v01["C121M01A.varVer"],name : 'varVer', align : "left", width : 70, sortable : false}
				,{colHeader : i18n.lms1015v01["label.cmsLocation"],name : 'cmsLocation', align : "left", width : 200, sortable : false,		
					hidden : _grid_cmsLocation_hidden
				 }
				,{colHeader : i18n.def["lastUpdater"], width : 90, name : 'updater', sortable : true}
				,{colHeader : i18n.def["lastUpdateTime"], width : 110, name : 'updateTime', sortable : true}
				]
			});
		}    
		
		$("#label_findRatingId").val(i18n.lms1015v01["C121M01A.custId"]+"：");
	            
		$("#thickBoxFromRatingDoc").thickbox({
	        title: '',
	        width: 940, height: (grid_height+200), align: 'center', valign: 'bottom', modal: false,
	        buttons: {
	            "sure": function(){            	
	            	var $gridview = $("#"+grid_id);
	            	var row = $gridview.getGridParam('selrow');
	            	if(row){
	            		var data = $gridview.getRowData(row);	            		
	            		chkRepeatRatingDoc( responseJSON.mainId, data.ratingId ).done(function(json_helper){
	            			//在簽報書中  {mainId, mainDocStatus, ...} 會自動附加
		            		$.ajax({							
								type : "POST", handler : 'lms1115formhandler',
								data : $.extend(
									{ formAction :"caseDocAddRatingDoc"
									, 'c121m01a_oid': data.oid
									}
									, json_helper||{}
								),
								success:function(responseData){
									$("#ratingPanelGrid").trigger("reloadGrid");								
									$.thickbox.close();
									if(responseData.errMsg){
										API.showErrorMessage(responseData.errMsg);
									}
									//~~~
		    						chooseKeyBorrower(false);
								}
							});
	            		});
	            		            		
	            	}else{
	            		API.showMessage(i18n.def.action_005);//請先選取一筆以上之資料列
	            	}
	            },
	            "cancel": function(){
	               $.thickbox.close();
	            }
	        }
	    });
	}
	
	function chkRepeatRatingDoc(caseId, ratingId){
		var my_dfd = $.Deferred(); 
		
		var param = {'caseId':caseId, 'ratingId':ratingId};
		
		$.ajax({							
			type : "POST", handler : 'lms1115formhandler',
			data : $.extend({ formAction :"chkRepeatRatingDoc"}, param),
			success:function(json){
				if(json.hasRepeat=="Y"){
					API.confirmMessage(i18n.def.reQuery+("【"+json.desc+"】")+"？", function(res){
		    			if(res){
		    				my_dfd.resolve( param );
		    			}
		    	    });
					
				}else{					   	
			    	my_dfd.resolve();
				}								
			}
		});
		
		return my_dfd.promise();
	}
	
	function isRatingDocUsed(ratingDoc_mainId){
		var my_dfd = $.Deferred();   
		$.ajax({							
			type : "POST", handler : _handler,
			data : { formAction : "caseDocIsRatingDocUsed"
				, 'ratingDoc_mainId': ratingDoc_mainId										
			},
			success:function(jsonIsRatingDocUsed){
				if(jsonIsRatingDocUsed.isUsed){
					if(jsonIsRatingDocUsed.isUsed=="Y"){
						API.confirmMessage(jsonIsRatingDocUsed.showMsg, function(res){ 
							if(res){
								my_dfd.resolve();
							}else{
								my_dfd.reject();
							}
						});
					}else{
						my_dfd.resolve();
					}	
				}else{
					//avoid js cache
					my_dfd.reject();
				}				
			}
		});
		return my_dfd.promise();
	}
	function delRatingDoc(){	
		var ratingPanelGrid = $("#ratingPanelGrid");
		var row = ratingPanelGrid.getGridParam('selrow');
    	if(row){
    		var data = ratingPanelGrid.getRowData(row);
    		if(data.ratingKind=="1"){
    			API.confirmMessage(i18n.def['confirmDelete']+("  "+data.ratingDesc||''), function(res){
        			if(res){
        				ilog.debug("@delRatingDoc, ratingDoc_mainId="+data.mainId +", case_Id="+responseJSON.mainId );
        				var ratingDoc_mainId = data.mainId;
        				isRatingDocUsed(ratingDoc_mainId).done(function(){
        					$.ajax({							
            					type : "POST", handler : _handler,
            					data : { formAction : "caseDocDelRatingDoc"
            						, 'ratingDoc_mainId': ratingDoc_mainId											
            					},
            					success:function(){
            						ratingPanelGrid.trigger("reloadGrid");
            						$.thickbox.close();
            						//~~~
            						chooseKeyBorrower(false);
            					}
            				});
        				});
        			}
        	    });
    		}else{
    			API.showMessage(i18n.def.grid_selector);//請選擇資料	
    		}			            		
    	}else{
    		API.showMessage(i18n.def.grid_selector);//請選擇資料
    	} 
	}
		
	function queryC120BusCd(c120m01a_oid){
		return $.ajax({							
			type : "POST", handler : 'lms1015m01formhandler',
			data : { formAction : "queryC120BusCd", 'c120m01a_oid':c120m01a_oid
			},
			success:function(json_queryC120BusCd){
				
			}
		});
	}
	
	function openDocC120(cellvalue, options, rowObject){
		var c120m01a_oid = rowObject.oid;
		var doOpenDoc = false;
		
		var showModifyBtn = false;
		if(val_L120M01A_ratingFlag=="" || val_L120M01A_ratingFlag=="00"){			
			showModifyBtn = true;
		}else{
			if(rowObject.ratingKind=="1"){
				
			}else{
				showModifyBtn = true;
			}
		}
		call_z_grid_openDoc(c120m01a_oid, doOpenDoc, showModifyBtn, val_L120M01A_ratingFlag);
	}
	function call_z_grid_openDoc(c120m01a_oid, doOpenDoc, showModifyBtn, val_L120M01A_ratingFlag){
		queryC120BusCd(c120m01a_oid).done(function(json_queryC120BusCd){
			if(json_queryC120BusCd.busCode){
				var loadUrl = "";
				var formAttrTxFlag = "";
				//有額度明細表的文件 EX: 一般
				var isCls = (json_queryC120BusCd.busCode=="060000" || json_queryC120BusCd.busCode=="130300");
				if(val_L120M01A_ratingFlag=="JP"){
					if(isCls){
						loadUrl = "../../lms/lms1015s02c";	
					}else{
						loadUrl = "../../lms/lms1015s02b";
					}					
					formAttrTxFlag = "lms1015s02cb";
				}else if(val_L120M01A_ratingFlag=="AU"){
					if(isCls){
						loadUrl = "../../lms/lms1025s02c";	
					}else{
						loadUrl = "../../lms/lms1025s02b";
					}					
					formAttrTxFlag = "lms1025s02cb";
				}else if(val_L120M01A_ratingFlag=="TH"){
					if(isCls){
						loadUrl = "../../lms/lms1035s02c";	
					}else{
						loadUrl = "../../lms/lms1035s02b";
					}					
					formAttrTxFlag = "lms1035s02cb";
				}else{
					if(val_L120M01A_ratingFlag=="00"){
						
					}else{
						
					}
					
					//default
					if(isCls){
						loadUrl = "../../lms/lms1115s02c";
					}else{
						loadUrl = "../../lms/lms1115s02b";
					}					
					formAttrTxFlag = "lms1115s02cb";
				}
												
				ilog.debug("hasCntrNo@divOverSeaCLSPage_content will load:"+loadUrl+"[formAttrTxFlag="+formAttrTxFlag+"]");
				$("#divOverSeaCLSPage_content").load(loadUrl,function(){	
					
					$.ajax({type : "POST", handler : 'lms1015m01formhandler',
						data : $.extend(
							{
								'formAction':'overSeaCLS_query','mainId':responseJSON.mainId
								,'c120m01a_oid' : c120m01a_oid

								//在抓金額時 ，依 formAttrTxFlag 來抓[元、仟元、萬元]
								,'formAttrTxFlag': formAttrTxFlag 
							}
							, {}
						),success:function(json_overSeaCLS_query){
							var $form = overSeaCLSPage_getForm();
							$form.buildItem();
							overSeaCLSPage_buildItem();
							if($("#oIncome").length>0){
								var item = API.loadOrderCombosAsList("cls1131m01_othType")["cls1131m01_othType"];
						        $("#oIncome").setItems({ size: "1", item: item, clear: true, itemType: 'checkbox' })
							}
							$form.injectData(json_overSeaCLS_query);							
							if(true){
								overSeaCLSPage_initEvent(json_overSeaCLS_query);
								overSeaCLSPage_2ndInjectData_afterInitEvent(json_overSeaCLS_query);							 
							}
							//~~~~~~~~~~~~
							if(json_overSeaCLS_query.c120m01a_ratingKind=="2"){
								$form.find("#keyMan").readOnly(true);
								//----------
								if(json_queryC120BusCd.busCode=="060000" || json_queryC120BusCd.busCode=="130300"){
									if(val_L120M01A_ratingFlag=="JP"){
										//日本的 C、G 的個人戶，都要產生評等
										$form.find("#custPos option[value=C]").remove();
										$form.find("#custPos option[value=G]").remove();
									}else if(val_L120M01A_ratingFlag=="AU"){
										//澳洲的 C 的個人戶，要產生評等
										$form.find("#custPos option[value=C]").remove();
									}else if(val_L120M01A_ratingFlag=="TH"){
										//泰國的 C、G  的個人戶，要產生評等
										$form.find("#custPos option[value=C]").remove();
										$form.find("#custPos option[value=G]").remove();
									}						
								}else{
									//若以公司戶當連保人，應該也ok
								}
							}
							//~~~~~~~~~~~~
							var tb_btnArr = {};
							overSeaCLSPage_saveAjaxParam(
									{'mainId':responseJSON.mainId
										,'c120m01a_oid' : c120m01a_oid
									}
							);
							var memo_thickboxOptions_lockDoc = thickboxOptions.lockDoc;
							var memo_thickboxOptions_readOnly= thickboxOptions.readOnly;	
							if( $("#buttonPanel").find("#btnSave").is("button") && showModifyBtn){
								tb_btnArr["saveData"] = function(){
									overSeaCLSPage_runSaveAjax().done(function(json_overSeaCLS_save){
										$("#ratingPanelGrid").trigger("reloadGrid");
										if(json_overSeaCLS_save.l120m01a_custId){//更改主借人
											//...
										}
										CommonAPI.triggerOpener("gridview", "reloadGrid");
										
										if(json_overSeaCLS_save.errMsg){
											CommonAPI.showErrorMessage(json_overSeaCLS_save.errMsg);	
										}else{
											CommonAPI.showMessage(i18n.def.saveSuccess);
										}
							        });
								};
								
								tb_btnArr["del"] = function(){
									API.confirmMessage(i18n.def['confirmDelete'], function(res){
										if(res){							
											$.ajax({							
												type : "POST", handler : 'lms1015m01formhandler',
												data : $.extend(
													{
														'formAction':'overSeaCLS_del'
														,'mainId':responseJSON.mainId
														,'c120m01a_oid' : c120m01a_oid
														,'formAttrTxFlag':overSeaCLSPage_getForm().attr(FORM_ATTR_TX_FLAG)||''
													}
													, {}
												),
												success:function(json_delCust){
													$("#ratingPanelGrid").trigger("reloadGrid");	//更新Grid內容
													$.thickbox.close();
												}
											});						
										}
								     });
								};
							}else{
								//當無 saveBtn，以唯讀開啟 thickbox
								thickboxOptions.lockDoc = true;
								thickboxOptions.readOnly = true;					
							}
							tb_btnArr["close"] = function(){
								 API.confirmMessage(i18n.def['flow.exit'], function(res){
									if(res){
										$.thickbox.close();
									}
							     });
							};
							//~~~~~~~~~~~~
							$("#divOverSeaCLSPage").thickbox({		
								title : (json_overSeaCLS_query.custId||'')
										+'-'+(json_overSeaCLS_query.dupNo||'')
										+' '+(json_overSeaCLS_query.custName||'')
								, width : 950, height : 580, modal : true, i18n:i18n.def,						
								buttons : tb_btnArr
							});
							
							thickboxOptions.lockDoc = memo_thickboxOptions_lockDoc;
							thickboxOptions.readOnly = memo_thickboxOptions_readOnly;
							
							$form.closest('div.tabs')[0].scrollIntoView();

						    if (doOpenDoc) {
						    	overSeaCLSPage_reg_imp_custData();
						    }
						}
					});
				});
			}
		});			
	}

	function openDocC121FromC120M01A_oid(cellvalue, options, rowObject){
		var c120m01a_oid = rowObject.oid;
		var pageUrl = "";
		if(val_L120M01A_ratingFlag=="JP"){
			pageUrl = "../../lms/lms1015m01/07";
		}else if(val_L120M01A_ratingFlag=="AU"){
			pageUrl = "../../lms/lms1025m01/06";
		}else if(val_L120M01A_ratingFlag=="TH"){
			pageUrl = "../../lms/lms1035m01/07";
		}else{
			return;
		}
		
		$.ajax({							
			type : "POST", handler : 'lms1015m01formhandler',
			data : { formAction :"convertToC121Param"
				, 'c120m01a_oid': c120m01a_oid
			},
			success:function(json){
				$.form.submit({ url: pageUrl
					, data:$.extend(json, 
						{
							 'noOpenDoc':true
							,'mainDocStatus': '05O'  //已覆核的 status
						}
					)					
					, target:json.mainOid});								
			}
		});		
	}
	
	function openDocC121FromChooseTb(cellvalue, options, rowObject){
		var json = {'mainOid':rowObject.oid, 'mainId': rowObject.mainId};
		var pageUrl = "";
		if(val_L120M01A_ratingFlag=="JP"){
			pageUrl = "../../lms/lms1015m01/07";
		}else if(val_L120M01A_ratingFlag=="AU"){
			pageUrl = "../../lms/lms1025m01/06";
		}else if(val_L120M01A_ratingFlag=="TH"){
			pageUrl = "../../lms/lms1035m01/07";
		}else{
			return;
		}
		
		$.form.submit({ url: pageUrl
			, data:$.extend(json, 
				{
					 'noOpenDoc':true
					,'mainDocStatus': '05O'  //已覆核的 status
				}
			)					
			, target:json.mainOid});	
	}
});


function chooseKeyBorrower(forceShowTb){	
	$.ajax({							
		type : "POST", handler : 'lms1115formhandler',
		data : { formAction : "caseDocChooseKeyBorrower", 'mainId':responseJSON.mainId
		},
		success:function(json){
			if(json.matchL120M01A=="N" || forceShowTb){
				if(json.idDupName_list){
					var divId = "div_chooseKeyBorrower";
					if($("#"+divId).length>0){
						
					}else{
						$('body').append("<div id='"+divId+"' style='display: none;' ></div>");
					}
					
					var elmId = divId+"_idDupName_list";
					if(json.idDupName_list.key.length>0){
						var dyna = [];
						dyna.push("<select id='"+elmId+"' name='"+elmId+"' >");
						$.each(json.idDupName_list.key, function(idx, jsonItem) {
							dyna.push("<option value='"+jsonItem.custId
									+"|"+jsonItem.dupNo
									+"|"+jsonItem.custName+"'>"+jsonItem.custId+"-"+jsonItem.dupNo+" "+jsonItem.custName+"</option>");
						});	
						dyna.push("</select>");
						$("#"+divId).html(dyna.join(""));
						//==========
						$("#"+divId).thickbox({
					       title: i18n.lmss02arating['btn.setCaseDocKeyMan'], 
					       width: 350, height: 20, align: "center", valign: "bottom", i18n: i18n.def,
					       //當 modal==true,右上角就沒有 close 的 icon
				           modal: true ,
				           buttons: {
				               "sure": function(){
				            	   var val = $("#"+elmId).val();
				                   var custId = "";
				                   var dupNo = "";
				                   var custName = "";
				                   if(val){
				                	   var arr = val.split('|');
				                	   custId = arr[0];
					                   dupNo = arr[1];
					                   custName = arr[2];
				                   }else{
				                	   CommonAPI.showMessage(i18n.def["grid_selector"]);
				                       return;
				                   }
				                   $.ajax({							
				           			type : "POST", handler : 'lms1115formhandler',
				           			data : { formAction : "caseDocSetKeyBorrower"
				           				, 'mainId':responseJSON.mainId
				           				, 'custId':custId
				           				, 'dupNo':dupNo
				           				, 'custName':custName
				           			},
				           			success:function(json){
				           				$("form#showBorrowData").injectData(json);
				           				CommonAPI.triggerOpener("gridview", "reloadGrid");
				           			}});           
				                   $.thickbox.close();				                  	
				               }
				           }
						});
					}else{
						//無可供選擇的資料
					}
					
				}
			}
		}
	});
}