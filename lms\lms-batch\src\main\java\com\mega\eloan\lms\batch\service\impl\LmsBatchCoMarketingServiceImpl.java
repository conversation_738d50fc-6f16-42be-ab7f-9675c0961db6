/* 
 * ADEB001101ServiceImpl.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.batch.service.impl;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.gwclient.DWUFX1FTPClient;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.DebConfig;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.rpt.pages.LMS9511V01Page;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 傳送海外信保基金案件表到協銷 Service
 * </pre>
 * 
 * @since 2012/7/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/17,TammyChen,new
 *          <li>2012/11/29,Sunkist Wang,add interface for spring trasaction aop
 *          config
 *          <li>2012/11/30,Sunkist Wang,but!在LNSPServiceImpl.callSP() throw
 *          Exception 時將會導致這隻批次執行失敗，所以在還沒方案前改回來WebBatchService
 *          <li>2013/1/15,Tammy Chen,#1377 傳送卡務所有檔案內日期，均以YYYY/MM/DD格式顯示
 *          </ul>
 */
@Service("lmsbatchcomarketingserviceimpl")
public class LmsBatchCoMarketingServiceImpl extends AbstractCapService
		implements WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	DebConfig debConfig;

	@Resource
	DWUFX1FTPClient ftpClient;
	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	MisLNF022Service misLNF022Service;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	MisdbBASEService misdbBASEService;

	final String UTF8 = "UTF-8";
	final String ContentType = "application/octet-stream";
	final String success = "Y";
	final String fail = "N";
	final String TW_DATE_FORMAT_STR = "YYYMMDD";
	final String TW_DATE_FORMAT_STR2 = "YYY/MM/DD";
	final String DATE_FORMAT_STR = "yyyy-MM-dd";
	final String DATE_FORMAT_STR_2 = "yyyy/MM/dd";
	final String DATE_FORMAT_STR_3 = "yyyy.MM.dd";
	final String ADD_DATA = "addList";
	final String SYSTEM = "system";
	File fileDir = null;
	final String getBrNoCol = "ICBCBR_BRNO_BRNM";
	boolean isSuccess = true;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		JSONObject result = null;
		isSuccess = true;
		result = exeMode(json);
		return result;
	}

	/**
	 * 整批第一次執行
	 * 
	 * @param json
	 * @return
	 */
	public JSONObject exeMode(JSONObject json) {
		JSONObject result = null;
		isSuccess = true;

		// 產生主檔

		JSONObject request = json.getJSONObject("request");

		String dataStartDate = Util.trim(request.getString("dataStartDate"));
		String dataEndDate = Util.trim(request.getString("dataEndDate"));

		if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
			final String DATE_FORMAT_STR = "yyyy-MM-dd";
			String currentDate = Util.getLeftStr(
					CapDate.getCurrentDate(DATE_FORMAT_STR), 7)
					+ "-01";
			dataStartDate = Util.getLeftStr(
					CapDate.shiftDaysString(currentDate, DATE_FORMAT_STR, -1),
					7) + "-01";
			dataEndDate = CapDate.shiftDaysString(currentDate, DATE_FORMAT_STR,
					-1);

		}

		String localFilePath = PropUtil.getProperty("docFile.dir")
				+ File.separator + PropUtil.getProperty(

				"systemId") + File.separator + "916" + File.separator
				+ "LMS180R67" + File.separator;

		File bkFolder = new File(localFilePath);

		try {
			if (!bkFolder.exists()) {
				FileUtils.forceMkdir(bkFolder);
			}

			// 將執行完成的檔案備份到bk
			// FileUtils.copyFile(inFile, new File(bkFolder.toString(), file));
			// FileUtils.forceDelete(inFile);
		} catch (IOException ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName()
					+ "執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		List<Map<String, String>> doMonthList = new ArrayList<Map<String, String>>();

		int i = 0;
		int colCount = 0;
		int intExeMonth = 1;

		ArrayList<String> filePath = new ArrayList<String>();
		ArrayList<String> fileName = new ArrayList<String>();

		Map resultMap = this.subCreateBatchExcel(localFilePath, dataStartDate,
				dataEndDate, filePath, fileName);

		try {
			if (filePath != null && !filePath.isEmpty()) {
				Map ftpResultMap = sendToFTP_Batch(filePath, fileName);

				String tIsSuccess = MapUtils.getString(ftpResultMap,
						"isSuccess");
				String errMsg = MapUtils.getString(ftpResultMap, "errMsg");

				if (Util.notEquals(tIsSuccess, "Y")) {
					isSuccess = false;
					result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
					result.element(WebBatchCode.P_RESPONSE, this.getClass()
							.getName() + "執行失敗！==>" + errMsg);
					logger.error(errMsg);
					return result;
				}

			}

		} catch (Exception e) {
			isSuccess = false;
			e.printStackTrace();

			logger.error(e.getMessage());

			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName()
					+ "執行失敗！==>" + e.getMessage());

			return result;

		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;
			result.remove(WebBatchCode.P_RC_MSG);
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, "產檔失敗");
		}

		return result;
	}

	public Map<String, String> subCreateBatchExcel(String localFilePath,
			String tDataStartDate, String tDataEndDate,
			ArrayList<String> filePaths, ArrayList<String> fileNames) {

		boolean isSuccess = true;
		String errMsg = "";
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);

		Properties prop1405 = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		Map<String, String> result = new HashMap<String, String>();
		List<String> outList = null;
		List<Map<String, Object>> elData = null;
		// StringBuffer fileNameList = new StringBuffer("");

		try {

			Map<String, IFormatter> reformat = null;

			String fileName = "CSCPMUEL001";

			File txtFile = new File(localFilePath, fileName + ".D");
			File headerTxtFile = new File(localFilePath, fileName + ".H");

			logger.info(tDataStartDate + "~" + tDataEndDate);

			outList = new ArrayList<String>();

			elData = eloandbBaseService.listLMS180R67(tDataStartDate,
					tDataEndDate);

			char[] newLine = { 13, 10 };
			int count = 0;
			// 開始放查出資料
			if (elData != null && !CollectionUtils.isEmpty(elData)) {
				StringBuffer newValBuffer = null;
				for (Map<String, Object> map : elData) {

					newValBuffer = new StringBuffer("");

					count = count + 1;
					String BRNO = Util.trim(map.get("BRNO")); // 分行別
					String BRNAME = Util.trim(map.get("BRNAME")); // 分行別
					String CUSTNAME = Util.trim(map.get("CUSTNAME")); // 戶名
					String CUSTID = Util.trim(map.get("CUSTID")); // 統一編號
					String CNTRNO = Util.trim(map.get("CNTRNO")); // 額度序號
					String CURRENTAPPLYCURR = Util.trim(map
							.get("CURRENTAPPLYCURR")); // 現請額度幣別
					BigDecimal CURRENTAPPLYAMT = Util.equals(
							Util.trim(map.get("CURRENTAPPLYAMT")), "") ? BigDecimal.ZERO
							: new BigDecimal(Util.trim(map
									.get("CURRENTAPPLYAMT"))); // 現請額度
					BigDecimal AMT = null;
					Map<String, Object> lnf022_map = misLNF022Service
							.findSumBalGroupByCntrNo(CNTRNO);

					String noLnf022Memo = "";
					if (lnf022_map != null && !lnf022_map.isEmpty()) {
						AMT = Util.equals(Util.trim(lnf022_map.get("BAL")), "") ? null
								: new BigDecimal(Util.trim(lnf022_map
										.get("BAL")));

					} else {
						List<Map<String, Object>> lnf020List = misdbBASEService
								.findLnf020(CNTRNO);
						if (lnf020List != null && !lnf020List.isEmpty()
								&& lnf020List.size() > 0) {
							String LNF020_CANCEL_DATE = Util.trim(lnf020List
									.get(0).get("LNF020_CANCEL_DATE"));
							if (Util.notEquals(LNF020_CANCEL_DATE, "")
									&& Util.notEquals(LNF020_CANCEL_DATE,
											"0001-01-01")) {
								noLnf022Memo = "已銷戶";
							} else {
								noLnf022Memo = "未撥款";
							}

						} else {
							noLnf022Memo = "未撥款";
						}
					}

					newValBuffer.append("\"");
					newValBuffer.append(BRNO); // 分行代碼
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(BRNAME); // 分行名稱
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(CUSTNAME); // 客戶名稱
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(CUSTID); // 統一編號
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(CNTRNO); // 額度序號
					newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(CURRENTAPPLYCURR); // 授信額度-幣別
					newValBuffer.append("\"");
					newValBuffer.append(",");
					// newValBuffer.append("\"");
					newValBuffer
							.append(CURRENTAPPLYAMT == null ? BigDecimal.ZERO
									: CURRENTAPPLYAMT.toPlainString()); // 授信額度-金額
					// newValBuffer.append("\"");
					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append("TWD"); // 授信餘額-幣別
					newValBuffer.append("\"");
					newValBuffer.append(",");
					// newValBuffer.append("\"");
					newValBuffer
							.append(AMT == null ? "0" : AMT.toPlainString()); // 授信餘額-金額
					// newValBuffer.append("\"");

					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append(noLnf022Memo); // 未撥款註記
					newValBuffer.append("\"");

					newValBuffer.append(",");
					newValBuffer.append("\"");
					newValBuffer.append("E0"); // 協銷註記
					newValBuffer.append("\"");

					outList.add(newValBuffer.toString());
					newValBuffer = null;

				}

				elData.clear();
			}

			if (outList != null) {

				// write header
				StringBuilder headerSB = new StringBuilder();
				headerSB.append(
						StringUtils.replace(CapDate.formatDate(
								Util.parseDate(tDataStartDate),
								DATE_FORMAT_STR_3), ".", ""))
						.append(StringUtils.replace(
								CapDate.formatDate(
										Util.parseDate(tDataEndDate),
										DATE_FORMAT_STR_3), ".", ""))
						.append(fileName + ".D")
						.append(StringUtils.replace(
								CapDate.getCurrentDate(DATE_FORMAT_STR_3
										+ "HHMMSS"), ".", ""))
						.append(CapString.fillZeroHead(
								String.valueOf(outList.size()), 9));
				FileUtils.write(headerTxtFile, headerSB.toString(), UTF8);

				filePaths.add(headerTxtFile.toString());
				fileNames.add(fileName + ".H");

				// write data
				filePaths.add(txtFile.toString());
				fileNames.add(fileName + ".D");

				FileUtils.writeLines(txtFile, "UTF8", outList);

				outList.clear();

			}

		} catch (Exception e) {
			isSuccess = false;
			e.printStackTrace();

			logger.error(e.getMessage());

			result.put("isSuccess", isSuccess ? "Y" : "N");
			result.put("errMsg", e.getMessage());

			return result;

		}

		result.put("isSuccess", isSuccess ? "Y" : "N");
		result.put("errMsg", errMsg);
		// result.put(
		// "fileName",
		// Util.notEquals(fileNameList.toString(), "") ? fileNameList
		// .toString() : "");

		return result;

	}

	/**
	 * 傳送檔案至卡務中心
	 * 
	 * @param meta
	 *            D201S99C
	 * @return D201S99C
	 * @throws CapException
	 */
	public Map<String, String> sendToFTP_Batch(ArrayList<String> filePath,
			ArrayList<String> fileName) throws CapException {

		boolean isSuccess = true;
		String errMsg = "";
		// ArrayList<String> filePath = new ArrayList<String>();
		// ArrayList<String> fileName = new ArrayList<String>();
		Map<String, String> result = new HashMap<String, String>();
		ftpClient.test();
		try {

			// 後台管理->系統設定維護->LMS_ArCtrlCanShow
			// Map<String, Object> onlineDateMap = eloandbBaseService
			// .getSysParamData("UCCFTP_DEF_DIR");
			//
			// String serverDir = Util.trim(onlineDateMap.get("PARAMVALUE"));
			String msgId = IDGenerator.getUUID();
			String serverDir = ftpClient.getServerDir();
			boolean isBinaryFile = true;
			boolean delFtpFile = true;
			boolean isAddDate = false;

			ftpClient.send(msgId, filePath.toArray(new String[] {}),
					serverDir, // /dw/ftpdata/ftpufx1/ftpout/
					// debConfig.getUCCFTPUploadPath(), // 切換路徑至：/ftpout/
					fileName.toArray(new String[] {}), isBinaryFile,
					delFtpFile, isAddDate);
		} catch (Exception e) {
			isSuccess = false;
			e.printStackTrace();
			errMsg = e.getMessage();
			logger.error(e.getMessage());
		}

		result.put("isSuccess", isSuccess ? "Y" : "N");
		result.put("errMsg", errMsg);
		return result;
	}

	class BrFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		String custId = "";
		String applyDate = "";

		public BrFormatter(String custId, String applyDate) {
			this.custId = custId;
			this.applyDate = applyDate;
		}

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			Map<String, Object> misData = (Map<String, Object>) in;
			StringBuffer sb = new StringBuffer();
			return sb.toString();
		}
	}

	class DateFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		String fromFormat = TW_DATE_FORMAT_STR;
		String toFormat = DATE_FORMAT_STR_2;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String val = (String) in;
			if (!CapString.isEmpty(val)) {
				if (CapDate.isMatchPattern(val, TW_DATE_FORMAT_STR2)) {
					fromFormat = TW_DATE_FORMAT_STR2;
				} else if (CapDate.isMatchPattern(val, DATE_FORMAT_STR)) {
					fromFormat = DATE_FORMAT_STR;
				}
				return CapDate.formatDateFromF1ToF2(val, fromFormat, toFormat);
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

	class DateYMFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		String fromFormat = TW_DATE_FORMAT_STR;
		String toFormat = "yyyy/MM";

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String val = (String) in;
			if (!CapString.isEmpty(val)) {
				return CapDate.formatDateFromF1ToF2(val + "01", fromFormat,
						toFormat);
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

	class BigDecimalFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String val = (String) in;
			if (!CapString.isEmpty(val)) {
				BigDecimal dec = in instanceof BigDecimal ? (BigDecimal) in
						: CapMath.getBigDecimal((String) in);
				String re = dec.stripTrailingZeros().toPlainString();
				if ("0.00".equals(re)) {
					re = "0";
				}
				return re;
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

}
