/* 
 * CLS8011GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.handler.grid;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.service.CLS8011Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C801M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金個人資料清冊作業 GridHandler
 * </pre>
 * 
 * @since 2014/04/01
 * <AUTHOR> @version <ul>
 *
 *          </ul>
 */
@Scope("request")
@Controller("cls8011gridhandler")
public class CLS8011GridHandler extends AbstractGridHandler {

	@Resource
	CLS8011Service cls8011Service;

	@Resource
	EloandbBASEService eloandbbaseservice;


	/**
	 * 查詢個金個人資料清冊主檔
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 * @throws ParseException 
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryViewData(ISearch pageSetting,
			PageParameters params) throws CapException, ParseException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String docStatus = Util.trim(params.getString(EloanConstants.DOC_STATUS));
		
		String custId = Util.trim(params.getString("custId"));
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String caseStatus = Util.trim(params.getString("caseStatus"));
		String docType = Util.trim(params.getString("docType"));
//		String flt_approveTime_beg = Util.trim(params.getString("flt_approveTime_beg"));
//		String flt_approveTime_end = Util.trim(params.getString("flt_approveTime_end"));
		String approveTime_beg_str = Util.trim(params.getString("flt_approveTime_beg"));
		String approveTime_end_str = Util.trim(params.getString("flt_approveTime_end"));
		if(Util.isNotEmpty(custId)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);	
		}
		if(Util.isNotEmpty(cntrNo)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);	
		}
		
		if(Util.isEmpty(custId) && Util.isEmpty(cntrNo) ){
			if(Util.equals(docType, "1")){
				//企業戶
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType", docType);	
			}	
			else if(Util.equals(docType, "2")){
				//個人戶  2  或 空白
				pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "docType", "1");	
			}else{
				//全部
				
			}
		}
			
		if(Util.isNotEmpty(caseStatus)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseStatus", caseStatus);	
		}
		if(Util.equals(docStatus, CLSDocStatusEnum.已核准.getCode())){
			if(true){
				String caseApprId = Util.trim(params.getString("flt_caseApprId"));
				if(Util.isNotEmpty(caseApprId)){
					pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseApprId", caseApprId);	
				}	
			}
			
			//先將接近來自串轉成日期,因為pageinit關係吃不到datepikcer
			if(Util.isNotEmpty(approveTime_beg_str)){
			    String fullDateTimeStr = approveTime_beg_str + " 00:00:00";
			    
			    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			    Date approveTime_beg = dateFormat.parse(fullDateTimeStr);
			    
			    pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS, "approveTime", approveTime_beg);
			}
			//先將接近來自串轉成日期,因為pageinit關係吃不到datepikcer
			if(Util.isNotEmpty(approveTime_end_str)){
			    String fullDateTimeStrEnd = approveTime_end_str + " 23:59:59";
			    
			    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			    Date approveTime_end = dateFormat.parse(fullDateTimeStrEnd);
			    
			    pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS, "approveTime", approveTime_end);
			}
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,"deletedTime", null);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		
		Page<? extends GenericBean> page = cls8011Service.findPage(C801M01A.class,
				pageSetting);
		List<C801M01A> list = (List<C801M01A>) page.getContent();
		for (C801M01A model : list) {
			model.setCustId(Util.trim(model.getCustId()));

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
//		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 由個金「動審表」查
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC801M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		logger.debug("mainId : " + params.getString(EloanConstants.MAIN_ID));

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID)); // CLS1161M01.mainId
		
		List<String> cntrNoList = new ArrayList<String>();
		for(Map<String, Object> m : eloandbbaseservice.findL140M01AByC160M01A(mainId)){
			String cntrNo = Util.trim(m.get("CNTRNO"));
			cntrNoList.add(cntrNo);
		}
		String[] cntrNos = cntrNoList.toArray(new String[cntrNoList.size()]);
		pageSetting.addSearchModeParameters(SearchMode.IN, "cntrNo",cntrNos);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, UtilConstants.Field.刪除時間, "");
		
		Page<? extends GenericBean> page = cls8011Service.findPage( C801M01A.class, pageSetting);

		List<C801M01A> c801m01alist = (List<C801M01A>) page.getContent();

		return new CapGridResult(c801m01alist, page.getTotalRow());
	}
	
	/**
	 * 由企金「動審表」查
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC801M01AFromLMS(ISearch pageSetting,
			PageParameters params) throws CapException {
		logger.debug("mainId : " + params.getString(EloanConstants.MAIN_ID));

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID)); // CLS1161M01.mainId
		
		List<String> cntrNoList = new ArrayList<String>();
		for(Map<String, Object> m : eloandbbaseservice.findCntrnoByL161S01A(mainId)){
			String cntrNo = Util.trim(m.get("CNTRNO"));
			cntrNoList.add(cntrNo);
		}
		String[] cntrNos = cntrNoList.toArray(new String[cntrNoList.size()]);
		pageSetting.addSearchModeParameters(SearchMode.IN, "cntrNo",cntrNos);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, UtilConstants.Field.刪除時間, "");
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		Page<? extends GenericBean> page = cls8011Service.findPage( C801M01A.class, pageSetting);

		List<C801M01A> c801m01alist = (List<C801M01A>) page.getContent();

		return new CapGridResult(c801m01alist, page.getTotalRow());
	}
}