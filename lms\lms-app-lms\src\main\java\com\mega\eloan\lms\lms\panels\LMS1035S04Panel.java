package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.lms.pages.LMS1035M01Page;

/**
 * <pre>
 * 消金信用評等模型(擔保品)
 * 此 Panel 含 JavaScript
 * 再另外 include 純 html 的 LMS1035S04PanelA
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
public class LMS1035S04Panel extends Panel {
	private static final long serialVersionUID = 1L;

	public LMS1035S04Panel(String id) {
		super(id);
		// add(new LMS1035S04PanelA("includePanel", false));
	}

	public LMS1035S04Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		renderJsI18N(LMS1035S04PanelA.class);
		new LMS1035S04PanelA("includePanel", false).processPanelData(model, params);
	}

}
