<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
			<form id="formTabs6">
	            <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                <tbody>
	                    <tr>
	                        <td width="35%" class="hd1">
	                          <th:block th:text="#{'C999M01AM07.title06'}"><!--  償還辦法--></th:block>&nbsp;&nbsp;
	                        </td>
	                        <td width="65%">
						              本借款還本付息方式如下列第 <span class="color-red" id="kJsonDataE" name="kJsonDataE"></span>&nbsp;款：<br />
						              <table width="99%">
						                <tr>
						                  <td>（一）</td>
						                  <td>
						                    自實際借用日起，按月付息一次，到期還清本金。
						                  </td>
						                </tr>
						                <tr>
						                  <td>（二）</td>
						                  <td>
						                    自實際借用日起，按年金法，按月攤付本息。
						                  </td>
						                </tr>
						                <tr>
						                  <td>（三）</td>
						                  <td>
						                  	<!-- E1 ~ E2-->
						                    自實際借用日起，前<span class="color-red" id="jsonDataE1" name="jsonDataE1"></span>年（個月）按月付息，自第<span class="color-red" id="jsonDataE2" name="jsonDataE2"></span>年(個月)起，再按月攤付本息。
						                  </td>
						                </tr>
						                <tr>
						                  <td>（四）</td>
						                  <td>
						                  	<!-- E3 ~ E4-->
						                    自實際借用日起，以每十四天為一期，分<span class="color-red" id="jsonDataE3" name="jsonDataE3"></span>期償還。(每期償還之本息金額為：以<span class="color-red" id="jsonDataE4" name="jsonDataE4"></span>年為期，依年金法按月攤還本息計算所得每月應攤付金額之二分之一)。
						                  </td>
						                </tr>
						                <tr>
						                  <td>（五）</td>
						                  <td>
						                    （由甲方與乙方個別約定）：<br />
						                    	<span class="color-red" id="jsonDataE" name="jsonDataE"></span>
						                  </td>
						                </tr>
						              </table>                        	
							  <!--<textarea id="jsonDataE" name="jsonDataE" class="txt_mult" maxlengthC="256" cols="90" rows="10" readonly="readonly"
											style="line-height: 20px;"></textarea>-->
	                        </td>
	                    </tr>
	                </tbody>
	            </table>
			</form>
		</th:block>
    </body>
</html>
