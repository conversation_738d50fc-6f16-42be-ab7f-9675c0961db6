/* 
 * L820M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L820M01B;

/** 以房養老貸款撥款前查詢簽章檔 **/
public interface L820M01BDao extends IGenericDao<L820M01B> {

	L820M01B findByOid(String oid);
	
	List<L820M01B> findByMainId(String mainId);
	
	List<L820M01B> findByDocStatus(String docStatus);
	
	L820M01B findByUniqueKey(String mainId, String branchType, String branchId, String staffNo, String staffJob);

	List<L820M01B> findByIndex01(String mainId, String branchType, String branchId, String staffNo, String staffJob);
}