var initDfd;
pageJsInit(function() {
	$(function() {
		initDfd = initDfd || $.Deferred();
		initDfd.done(function(json) {
			if (true) { //依 seq 呈現選項
				var item_cls1141_purpose = [];
				var raw_cls1141_purpose = API.loadOrderCombosAsList("cls1141_purpose")["cls1141_purpose"];
				raw_cls1141_purpose = convertItems(raw_cls1141_purpose);
				for (var i = 0; i < raw_cls1141_purpose.length; i++) {
					var jsonItem = raw_cls1141_purpose[i];
					/*
					A	購置住宅
					B	修繕房屋
					E	購置停車位貸款
					L	青年創業貸款
					M	生活安養所需資金
					*/
					var key = jsonItem["value"];
					var desc = jsonItem["desc"];
					if (key == "A" || key == "B" || key == "E" || key == "L" || key == "M") {
						continue;
					}
					item_cls1141_purpose.push(jsonItem);
				}

				$("#purposeTemp").html(_add_div_json(5, "purpose", item_cls1141_purpose));
				//==================================
				var item_cls1141_resource = API.loadOrderCombosAsList("cls1141_resource")["cls1141_resource"];
				item_cls1141_resource = convertItems(item_cls1141_resource);
				$("#resourceTemp").html(_add_div_json(5, "resource", item_cls1141_resource));
			}

			if (true) {
				$("[name=purpose]").val(json.purpose.split("|")).change();
				$("[name=resource]").val(json.resource.split("|")).change();
			}
			if (true) {
				$("input[name=purpose]").closest('label').css('color', 'lightgray');
				$("input[name=resource]").closest('label').css('color', 'lightgray');

				$("input[name=purpose]:checked").closest('label').css('color', 'black');
				$("input[name=resource]:checked").closest('label').css('color', 'black');
			}

			$("input[name=purpose]").readOnly();
			$("input[name=resource]").readOnly();
			$("input[name=nowExtend]").readOnly();
			$("input[name=notifyWay]").readOnly();

			$("input[name=idCardChgFlag]").readOnly();
			$("input[name=idCardPhoto]").readOnly();


			if (json.applyDocStatus == "Z03") {
				$("select#n_applyStatus").find(":not(option[value=Z03])").prop('disabled', true);
			}

			if (json.applyDocStatus == "0A0" || json.applyDocStatus == "Z01" || json.applyDocStatus == "Z02") {
				//受理中(未收件),不承做,轉臨櫃
				$(".onlyZ03").hide();
			} else if (json.applyDocStatus == "0B0") {
				//審核中
				if (json.mainDocStatus == "") {
					//尚未轉編製中
					$(".onlyZ03").hide();
				} else {
					//可能是[編製中,待覆核]
				}
			} else if (json.applyDocStatus == "Z03") {
				//已核貸
			}

			if (true) {
				$("select#n_applyStatus").change(function() {
					var val = $(this).val()
					if (val == "Z03") {
						$(".onlyZ03").show();
					} else {
						$(".onlyZ03").hide();
					}
				});
				$("select#n_applyStatus").trigger('change');
			}
			//========================
			var attchGrid = $("#attchGrid").iGrid({
				handler: 'cls1221gridhandler',
				height: 100,
				width: 400,
				autowidth: false,
				action: "queryAttch",
				postData: {
					mainId: responseJSON.mainId
				},
				needPager: false,
				colModel: [{
					colHeader: '檔案名稱',
					name: 'srcFileName',
					width: 120,
					align: "left",
					sortable: false,
					formatter: 'click',
					onclick: openFile
				}, {
					colHeader: '上傳時間',
					name: 'uploadTime',
					width: 100,
					sortable: false
				}, {
					name: 'oid',
					hidden: true
				}]
			});
			//========================
			if (true) {
				var batchNo = getSelectedBatchNo();
				var grid_id = "gridCntrInfo";
				if ($("#" + grid_id + ".ui-jqgrid-btable").length > 0) {
					$("#" + grid_id).jqGrid("setGridParam", {
						postData: { mainId: responseJSON.mainId, batchNo: batchNo },
						search: true
					}).trigger("reloadGrid");
				} else {
					//核貸資料Grid
					$("#" + grid_id).iGrid({
						handler: 'cls1221gridhandler',
						height: 180,
						width: 800,
						shrinkToFit: true,
						autowidth: false,
						action: "queryCntrInfo",
						sortname: 'cntrNo|cntrNoMainId|seq',
						sortorder: 'asc|asc|asc',
						multiselect: true,
						postData: { mainId: responseJSON.mainId, batchNo: batchNo },
						colModel: [{
							colHeader: i18n.cls1220m02['C122S01B.batchNo'],
							name: 'batchNo', width: 34, align: "center", sortable: false
						}, {
							colHeader: i18n.cls1220m02['C122S01B.cntrNo'],
							name: 'cntrNo', width: 100, align: "left", sortable: true
							, formatter: 'click', onclick: editC122S01B
						}, {
							colHeader: i18n.cls1220m02['C122S01B.seq'],
							name: 'seq', width: 34, align: "center", sortable: false
						}, {
							colHeader: i18n.cls1220m02['C122S01B.prodKind'],
							name: 'prodKind', width: 100, sortable: false
						}, {
							colHeader: i18n.cls1220m02['C122S01B.loanCurr'],
							name: 'loanCurr', width: 60, align: "center", sortable: false
						}, {
							colHeader: i18n.cls1220m02['C122S01B.loanAmt'],
							name: 'loanAmt', width: 70, align: "right", sortable: false,
							formatter: function(data) {
								if (data == null) { return ""; } else { return util.addComma(data); }
							}
						}, {
							colHeader: i18n.cls1220m02['C122S01B.rateDesc'],
							name: 'rateDesc', width: 400, sortable: false
						}, {
							colHeader: ' ',
							name: 'link', width: 40, sortable: false, formatter: 'click', onclick: openL140M01A
						}, {
							name: 'cntrNoMainId', hidden: true
						}, {
							name: 'oid', hidden: true
						}]
					});
				}

				if (true) {
					$("select#c122s01a_batchNo").change(function(k, v) {
						var batchNo = $(this).val();
						$("#" + grid_id).jqGrid("setGridParam", {
							postData: { mainId: responseJSON.mainId, batchNo: batchNo },
							search: true
						}).trigger("reloadGrid");
					});
					$("select#c122s01a_batchNo").trigger('change');
				}
				$("select#c122s01a_batchNo").prop("disabled", false).prop("readonly", false)
			}

			//引進核准額度
			$("#btImpCntrInfo").click(function() {
				var grid_height = 300;
				if (true) {
					var grid_id = "gridImpCntrInfo";
					if ($("#" + grid_id + ".ui-jqgrid-btable").length > 0) {
						$("#" + grid_id).trigger("reloadGrid");
					} else {
						$("#" + grid_id).iGrid({
							handler: 'cls1221gridhandler',
							height: grid_height,
							width: 960,
							action: "queryImpCntrInfo",
							multiselect: true,
							postData: {
								mainId: responseJSON.mainId
							},
							colModel: [{
								colHeader: i18n.cls1220m02['label.L120M01A.endDate'],
								name: 'endDate', width: 100, align: "center", sortable: true
							}, {
								colHeader: i18n.cls1220m02['label.L140M01A.caseNo'],
								name: 'caseNo', width: 140, align: "left", sortable: false
							}, {
								colHeader: i18n.cls1220m02['C122S01B.cntrNo'],
								name: 'cntrNo', width: 120, align: "left", sortable: false
							}, {
								colHeader: i18n.cls1220m02['C122S01B.seq'],
								name: 'seq', width: 40, align: "center", sortable: false
							}, {
								colHeader: i18n.cls1220m02['C122S01B.prodKind'],
								name: 'prodKind', width: 100, sortable: false
							}, {
								colHeader: i18n.cls1220m02['label.property'],
								name: 'property', width: 100, sortable: false
							}, {
								colHeader: i18n.cls1220m02['C122S01B.loanCurr'],
								name: 'loanCurr', width: 60, align: "center", sortable: false
							}, {
								colHeader: i18n.cls1220m02['C122S01B.loanAmt'],
								name: 'loanAmt', width: 100, align: "right", sortable: false,
								formatter: function(data) {
									if (data == null) { return ""; } else { return util.addComma(data); }
								}
							}, {
								colHeader: i18n.cls1220m02['C122S01B.rateDesc'],
								name: 'rateDesc', width: 400, sortable: false
							}, {
								name: 'oid', hidden: true
							}]
						});
					}
				}

				$("#div_gridImpCntrInfo").thickbox({
					title: '', width: 850, height: (grid_height + 150), modal: false, i18n: i18n.def, readOnly: false,
					buttons: {
						"sure": function() {
							var list = [];
							if (true) {
								var $gridImpCntrInfo = $("#gridImpCntrInfo");
								var rows = $gridImpCntrInfo.getGridParam('selarrrow');
								for (var i = 0; i < rows.length; i++) {	//將所有已選擇的資料存進變數list裡面
									var data = $gridImpCntrInfo.getRowData(rows[i]);
									list.push(data.oid);
								}
							}

							if (list.length == "") {
								CommonAPI.showMessage(i18n.def["grid_selector"]);
								return;
							}
							$.ajax({
								type: "POST",
								handler: _handler,
								action: "addC122S01A_B",
								data: {
									mainOid: json.oid,
									mainId: json.mainId,
									mainDocStatus: json.mainDocStatus,
									oids: list.join("|"),
									batchNo: getSelectedBatchNo()
								}
							}).done(function(respObj) {
								$.thickbox.close();
								if (respObj.promptMsg) {
									API.showMessage(respObj.promptMsg);
								}
								/*
								 若 id=c122s01a_batchNo 是 input type=hidden
								以下就不會跑
								 */
								var existSel = $("select#c122s01a_batchNo").length > 0;
								ilog.debug("@ajax addC122S01A_B,existSel=" + existSel);
								if (existSel) {
									ilog.debu
									$("select#c122s01a_batchNo").empty();
									build_selItem(respObj.selItem, respObj.selItemOrder, ".", respObj.currentBatchNo);
									$("select#c122s01a_batchNo").trigger('change');
								} else {
									var batchNo = respObj.currentBatchNo;
									$("input#c122s01a_batchNo").val(batchNo);
									$("#gridCntrInfo").jqGrid("setGridParam", {
										postData: { mainId: responseJSON.mainId, batchNo: batchNo },
										search: true
									}).trigger("reloadGrid");
								}
							});
						},
						"close": function() {
							$.thickbox.close();
						}
					}
				});

			});
			$("#btDelCntrInfo").click(function() {
				var gridId = "gridCntrInfo";
				var rows = $("#" + gridId).getGridParam('selarrrow');
				var list = [];
				for (var i = 0; i < rows.length; i++) {	//將所有已選擇的資料存進變數list裡面
					var data = $("#" + gridId).getRowData(rows[i]);
					list.push(data.oid);
				}
				if (list.length == "") {
					CommonAPI.showMessage(i18n.def["grid_selector"]);
					return;
				}
				API.confirmMessage(i18n.def.confirmDelete, function(result) {
					if (result) {
						$.ajax({
							type: "POST",
							handler: _handler,
							action: "deleteC122S01B",
							data: {
								mainOid: json.oid,
								mainId: json.mainId,
								mainDocStatus: json.mainDocStatus,
								oids: list.join("|"),
								batchNo: getSelectedBatchNo()
							}
						}).done(function(responseData) {
							$("#" + gridId).trigger("reloadGrid");
						});
					}
				});

			});
			//========================	
			function editC122S01B(cellvalue, options, rowObject) {

				$.ajax({
					type: "POST", handler: _handler, action: "getC122S01B",
					data: {
						'c122s01b_oid': rowObject.oid
					}
				}).done(function(respObj) {
					$("#editC122S01BForm").injectData(respObj);
					//===========

					var btnObj = {};
					if ($("#buttonPanel").find("#btnSave").is("button")) {
						btnObj['saveData'] = function() {
							//===========
							$.thickbox.close();
							$.ajax({
								type: "POST", handler: _handler, action: "saveC122S01B",
								data: $.extend($("#editC122S01BForm").serializeData(),
									{ 'c122s01b_oid': rowObject.oid }
								)
							}).done(function(respObj) {
								$("#gridCntrInfo").trigger("reloadGrid");
							});
						};
					} else {
						$("#editC122S01BForm").lockDoc();
					}
					btnObj['close'] = function() {
						$.thickbox.close();
					};
					$("#div_C122S01BForm").thickbox({
						title: '', width: 800, height: 350, modal: true, i18n: i18n.def, readOnly: false,
						buttons: btnObj
					});
				});
			}
			function openL140M01A(cellvalue, options, rowObject) {
				$.ajax({
					type: "POST", handler: _handler, action: "getL140M01AParam",
					data: {
						'cntrNoMainId': rowObject.cntrNoMainId
					},
				}).done(function(json) {

					if (json.isFound == "Y") {
						$.form.submit({
							url: "../../cls/cls1151s01",
							data: $.extend(
								json
								, {}
							),
							target: json.l140m01a_oid
						});
					}
				});
			}
		});
	});
});
function getSelectedBatchNo(){
	if( $("select#c122s01a_batchNo").length>0 ){
		return $("select#c122s01a_batchNo option:selected").val();
	}else if( $("input#c122s01a_batchNo").length>0 ){		
		return $("input#c122s01a_batchNo").val();
	}else{
		return -1;//在 CLS1220ServiceImpl 用 -1 代表 "新增版本"
	}
}

function openFile(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}

function _add_div_json(col_cnt, assignId, jsonList){
	var elmArr = [];
	$.each(jsonList, function(idx, jsonItem) {
		
		var k = jsonItem.value;
		var v = jsonItem.desc;
		//依 itemOrder, 一個一個append, 把 clear 指為 false		
		var tdcol = {};
		tdcol['_a'] = "<label style='letter-spacing:0px;cursor:pointer;font-weight:normal;'><input value='"+k+"' id='"+assignId+"' name='"+assignId+"' type='checkbox'>"+v+"</label>";
		elmArr.push(tdcol);            		
	});
	
	//===
	//補empty col
	var addcnt = (col_cnt - (elmArr.length % col_cnt));
	if(addcnt==col_cnt){
		addcnt = 0;
	}
	for(var i=0;i<addcnt;i++){
		var tdcol = {};
		tdcol['_a'] = "&nbsp;";	            		
		elmArr.push(tdcol);  
	}
	
	var dyna = [];
	dyna.push("<table border='0' cellspacing='0' cellpadding='0'>");
	dyna.push("<tr>");
	for(var i=0;i<elmArr.length;i++){
		//若要對齊 width='"+(100/col_cnt)+"%'
		dyna.push("<td style='border:0' >"+elmArr[i]['_a']+"</td>");	            		
		if( (i+1) % col_cnt==0){
			dyna.push("</tr><tr>");
		}
	}
	dyna.push("</tr>");
	dyna.push("</table>");
	return dyna.join("\n");
}
