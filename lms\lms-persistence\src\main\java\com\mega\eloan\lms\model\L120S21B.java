/* 
 * L120S21B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** LGD額度EAD檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S21B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S21B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * 文件編號
	 * <p/>
	 * 簽報書MAINID
	 */
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 借款人統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID_S21B", length = 10, columnDefinition = "CHAR(10)")
	private String custId_s21b;

	/** 重複序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO_S21B", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo_s21b;

	/** 已分配額度序號 **/
	@Size(max = 12)
	@Column(name = "CNTRNO_S21B", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo_s21b;

	/**
	 * 額度最終EAD
	 * <p/>
	 * 系統TWD
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CNTREAD_S21B", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cntrEad_s21b;

	/** LGD模型版本_大版 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "LGDVER1", columnDefinition = "DECIMAL(4,0)")
	private Integer lgdVer1;

	/** LGD模型版本_小版 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "LGDVER2", columnDefinition = "DECIMAL(4,0)")
	private Integer lgdVer2;

	/** 額度預期LGD **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "EXPECTLGD", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal expectLgd;

	/** 借款人LGD **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CUSTLGD", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal custLgd;

	/**
	 * 有無額度明細表
	 * <p/>
	 * 系統
	 */
	@Size(max = 1)
	@Column(name = "HASCNTRDOC_S21B", length = 1, columnDefinition = "CHAR(1)")
	private String hasCntrDoc_s21b;

	/** 本額度有無送保 **/
	@Size(max = 1)
	@Column(name = "HEADITEM1_S21B", length = 1, columnDefinition = "CHAR(1)")
	private String headItem1_s21b;

	/** 信保保證成數 **/
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "GUTPERCENT_S21B", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal gutPercent_s21b;

	/** 有無公司保證人 **/
	@Size(max = 1)
	@Column(name = "HASGUARANTOR_S21B", length = 1, columnDefinition = "CHAR(1)")
	private String hasGuarantor_s21b;

	/**
	 * 聯貸案且兆豐為擔保品管理行
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "UNIONFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String unionFlag;

	/** 聯貸幣別 **/
	@Size(max = 3)
	@Column(name = "UNIONCURR", length = 3, columnDefinition = "CHAR(3)")
	private String unionCurr;

	/** 本行參貸額度 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "SYNDAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal syndAmt;

	/** 聯合授信案總金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "UNIONAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal unionAmt;

	/** 擔保品系統分配後擔保品回收小計 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COLLATERALRECOVERYCMS", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal collateralRecoveryCms;

	/** 未建檔擔保品小計 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COLLATERALRECOVERYOTH", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal collateralRecoveryOth;

	/**
	 * 信用保證回收
	 * <p/>
	 * TWD<br/>
	 * (額度EAD-擔保品合計) * 信保保證成數 * 95%(信保回收率 E33)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CREDITRECOVERY", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal creditRecovery;

	/**
	 * 預期擔保品回收
	 * <p/>
	 * TWD
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "EXPECTSECUREDRECOVERY", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal expectSecuredRecovery;

	/**
	 * 預期無擔保回收
	 * <p/>
	 * TWD
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "EXPECTUNSECUREDRECOVERY", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal expectUnsecuredRecovery;

	/**
	 * 分配後擔保品回收合計
	 * <p/>
	 * 系統TWD
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COLLATERALRECOVERY", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal collateralRecovery;

	/**
	 * 無擔保回收率有公司保證者
	 * <p/>
	 * E38 44%
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "UNSECUREDRECOVERYRATEY", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal unsecuredRecoveryRateY;

	/**
	 * 無擔保回收率無公司保證者
	 * <p/>
	 * E39 21%
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "UNSECUREDRECOVERYRATEN", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal unsecuredRecoveryRateN;

	/**
	 * 信保回收率
	 * <p/>
	 * E33 95%
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "GUTRECOVERYRATE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal gutRecoveryRate;

	/**
	 * 是否為產品自償回收額度
	 * <p/>
	 * Y/N<br/>
	 * 額度明細表科目為出口押匯或應收帳款時有值
	 */
	@Size(max = 1)
	@Column(name = "ISPRODRECV", length = 1, columnDefinition = "CHAR(1)")
	private String isProdRecv;

	/**
	 * 產品自償回收額度種類
	 * <p/>
	 * 01.出口押匯<br/>
	 * 02.應收帳款
	 */
	@Size(max = 2)
	@Column(name = "PRODRECVTYPE", length = 2, columnDefinition = "CHAR(2)")
	private String prodRecvType;

	/**
	 * 產品自償回收率
	 * <p/>
	 * E37 90%
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "PRODRECVRATE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal prodRecvRate;

	/**
	 * 帳款管理商類別
	 * <p/>
	 * 當prodRecvType為02時有值:<br/>
	 * 1.本行<br/>
	 * 2.IMPORT FACTOR<br/>
	 * 3.保險公司
	 */
	@Size(max = 1)
	@Column(name = "ARACCMANAGERTYPE_S21B", length = 1, columnDefinition = "CHAR(1)")
	private String arAccManagerType_s21b;

	/**
	 * 帳款管理商保證成數
	 * <p/>
	 * 帳款管理商類別為2或3有值
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "ARACCPERCENT_S21B", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal arAccPercent_s21b;

	/**
	 * 產品自償回收
	 * <p/>
	 * 若prodRecvType為01,02時=EAD*E37
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "PRODRECVTWD", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal prodRecvTwd;

	/**
	 * 清償損失率
	 * <p/>
	 * 計算結果
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "PAYOFFLOSSRATE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal payOffLossRate;

	/**
	 * 清償路徑比例
	 * <p/>
	 * E43 96%
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "PAYOFFPATH", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal payOffPath;

	/**
	 * 協商路徑比例
	 * <p/>
	 * E44 4%
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "NEGOTIATEPATH", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal negotiatePath;

	/**
	 * 轉正路徑比例
	 * <p/>
	 * E45 0%
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "TURNPOSITIVEPATH", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal turnPositivePath;

	/**
	 * 協商損失率
	 * <p/>
	 * E46 12%
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "NEGOTIATELOSSRATE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal negotiateLossRate;

	/**
	 * 轉正損失率
	 * <p/>
	 * E47 0%
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "TURNPOSITIVELOSSRATE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal turnPositiveLossRate;

	/**
	 * 間接成本
	 * <p/>
	 * E48 0.3%
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "INDIRECTCOST", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal indirectCost;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 其他保證成數 **/
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "OTHPERCENT_S21B", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal othPercent_s21b;

	/** 輸入資料檢誤完成(Y/N) **/
	@Size(max = 1)
	@Column(name = "CHKYN_S21B", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN_s21b;

	/** EAD模型版本_大版 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "EADVER1", columnDefinition = "DECIMAL(4,0)")
	private Integer eadVer1;

	/** EAD模型版本_小版 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "EADVER2", columnDefinition = "DECIMAL(4,0)")
	private Integer eadVer2;

	/** LGD業務種類 **/
	@Size(max = 2)
	@Column(name = "BUSSTYPE_S21B", length = 2, columnDefinition = "CHAR(2)")
	private String bussType_s21b;

	/** 借款人LGD **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "BUSSLGD", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal bussLgd;

	/** 擔保品系統分配後擔保品回收小計(含信保) **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COLLATERALRECOVERYCMSSME", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal collateralRecoveryCmsSme;

	/** 未建檔擔保品小計(含信保) **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COLLATERALRECOVERYOTHSME", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal collateralRecoveryOthSme;

	/**
	 * 屬本行授信業務授權準則得單獨劃分之業務類
	 */
	@Column(name = "ISSTANDALONEAUTH_S21B", length = 1, columnDefinition = "CHAR(1)")
	private String isStandAloneAuth_s21b;

	/** 現請額度幣別 **/
	@Column(name = "CURRENTAPPLYCURR_S21B", length = 3, columnDefinition = "CHAR(3)")
	private String currentApplyCurr_s21b;

	/** 現請額度幣別兌台幣匯率 **/
	@Column(name = "CURRENTRATE_S21B", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal currentRate_s21b;

	/**
	 * 本案是否有保證人共借人符合所有條件
	 */
	@Column(name = "ISGUARANTOREFFECT_S21B", length = 1, columnDefinition = "CHAR(1)")
	private String isGuarantorEffect_s21b;

	/**
	 * 統編
	 */
	@Column(name = "GUARANTORID_S21B", length = 10, columnDefinition = "VARCHAR(10)")
	private String guarantorId_s21b;

	/**
	 * 重覆序號
	 */
	@Column(name = "GUARANTORDUPNO_S21B", length = 1, columnDefinition = "CHAR(1)")
	private String guarantorDupNo_s21b;

	/**
	 * 戶名
	 */
	@Column(name = "GUARANTORRNAME_S21B", length = 150, columnDefinition = "VARCHAR(150)")
	private String guarantorRName_s21b;

	/**
	 * 評等種類
	 */
	@Column(name = "GUARANTORCRDTYPE_S21B", length = 2, columnDefinition = "CHAR(2)")
	private String guarantorCrdType_s21b;

	/**
	 * 評等日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "GUARANTORCRDTYEAR_S21B", columnDefinition = "DATE")
	private Date guarantorCrdTYear_s21b;

	/**
	 * 評等等級
	 */
	@Column(name = "GUARANTORGRADEORG_S21B", length = 6, columnDefinition = "CHAR(6)")
	private String guarantorGradeOrg_s21b;

	/**
	 * 評等等級-轉換後
	 */
	@Column(name = "GUARANTORGRADENEW_S21B", length = 6, columnDefinition = "CHAR(6)")
	private String guarantorGradeNew_s21b;

	/**
	 * 實收資本額-幣別
	 */
	@Column(name = "GUARANTORCPTLCURR_S21B", length = 3, columnDefinition = "CHAR(3)")
	private String guarantorCptlCurr_s21b;

	/**
	 * 實收資本額-金額
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "GUARANTORCPTLAMT_S21B", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal guarantorCptlAmt_s21b;

	/**
	 * 實收資本額-單位
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "GUARANTORCPTLUNIT_S21B", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal guarantorCptlUnit_s21b;

	/**
	 * 註冊地國別
	 */
	@Column(name = "GUARANTORNTCODE_S21B", length = 2, columnDefinition = "CHAR(2)")
	private String guarantorNtCode_s21b;

	/**
	 * 股票上市上櫃情形
	 */
	@Column(name = "GUARANTORSTOCKSTATUS_S21B", length = 1, columnDefinition = "VARCHAR(1)")
	private String guarantorStockStatus_s21b;

	/**
	 * 股票代號
	 */
	@Column(name = "GUARANTORSTOCKNUM_S21B", length = 6, columnDefinition = "VARCHAR(6)")
	private String guarantorStockNum_s21b;

	/**
	 * 評等種類
	 */
	@Column(name = "GUARANTORCRDTYPE2_S21B", length = 2, columnDefinition = "CHAR(2)")
	private String guarantorCrdType2_s21b;

	/**
	 * 評等日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "GUARANTORCRDTYEAR2_S21B", columnDefinition = "DATE")
	private Date guarantorCrdTYear2_s21b;

	/**
	 * 評等等級
	 */
	@Column(name = "GUARANTORGRADEORG2_S21B", length = 6, columnDefinition = "VARCHAR(20)")
	private String guarantorGradeOrg2_s21b;

	/**
	 * 評等等級-轉換後
	 */
	@Column(name = "GUARANTORGRADENEW2_S21B", length = 6, columnDefinition = "VARCHAR(20)")
	private String guarantorGradeNew2_s21b;

	/**
	 * 權益證券分類子目-本行所認可之海外交易所掛牌
	 */
	@Column(name = "GUARANTORSTKCATNM_S21B", length = 2, columnDefinition = "CHAR(02)")
	private String guarantorStkCatNm_s21b;

	/**
	 * 評等種類-異動前
	 */
	@Column(name = "GUARANTORCRDTYPE3_S21B", length = 2, columnDefinition = "CHAR(2)")
	private String guarantorCrdType3_s21b;

	/**
	 * 評等日期-異動前
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "GUARANTORCRDTYEAR3_S21B", columnDefinition = "DATE")
	private Date guarantorCrdTYear3_s21b;

	/**
	 * 評等等級-異動前
	 */
	@Column(name = "GUARANTORGRADEORG3_S21B", length = 6, columnDefinition = "VARCHAR(20)")
	private String guarantorGradeOrg3_s21b;

	/**
	 * 評等等級-轉換後-異動前
	 */
	@Column(name = "GUARANTORGRADENEW3_S21B", length = 6, columnDefinition = "VARCHAR(20)")
	private String guarantorGradeNew3_s21b;

	/**
	 * 權益證券分類子目-本行所認可之海外交易所掛牌-異動前
	 */
	@Column(name = "GUARANTORSTKCATNM3_S21B", length = 2, columnDefinition = "CHAR(02)")
	private String guarantorStkCatNm3_s21b;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得文件編號
	 * <p/>
	 * 簽報書MAINID
	 */
	public String getMainId() {
		return this.mainId;
	}

	/**
	 * 設定文件編號
	 * <p/>
	 * 簽報書MAINID
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得借款人統編 **/
	public String getCustId_s21b() {
		return this.custId_s21b;
	}

	/** 設定借款人統編 **/
	public void setCustId_s21b(String value) {
		this.custId_s21b = value;
	}

	/** 取得重複序號 **/
	public String getDupNo_s21b() {
		return this.dupNo_s21b;
	}

	/** 設定重複序號 **/
	public void setDupNo_s21b(String value) {
		this.dupNo_s21b = value;
	}

	/** 取得已分配額度序號 **/
	public String getCntrNo_s21b() {
		return this.cntrNo_s21b;
	}

	/** 設定已分配額度序號 **/
	public void setCntrNo_s21b(String value) {
		this.cntrNo_s21b = value;
	}

	/**
	 * 取得額度最終EAD
	 * <p/>
	 * 系統TWD
	 */
	public BigDecimal getCntrEad_s21b() {
		return this.cntrEad_s21b;
	}

	/**
	 * 設定額度最終EAD
	 * <p/>
	 * 系統TWD
	 **/
	public void setCntrEad_s21b(BigDecimal value) {
		this.cntrEad_s21b = value;
	}

	/** 取得LGD模型版本_大版 **/
	public Integer getLgdVer1() {
		return this.lgdVer1;
	}

	/** 設定LGD模型版本_大版 **/
	public void setLgdVer1(Integer value) {
		this.lgdVer1 = value;
	}

	/** 取得LGD模型版本_小版 **/
	public Integer getLgdVer2() {
		return this.lgdVer2;
	}

	/** 設定LGD模型版本_小版 **/
	public void setLgdVer2(Integer value) {
		this.lgdVer2 = value;
	}

	/** 取得額度預期LGD **/
	public BigDecimal getExpectLgd() {
		return this.expectLgd;
	}

	/** 設定額度預期LGD **/
	public void setExpectLgd(BigDecimal value) {
		this.expectLgd = value;
	}

	/** 取得借款人LGD **/
	public BigDecimal getCustLgd() {
		return this.custLgd;
	}

	/** 設定借款人LGD **/
	public void setCustLgd(BigDecimal value) {
		this.custLgd = value;
	}

	/**
	 * 取得有無額度明細表
	 * <p/>
	 * 系統
	 */
	public String getHasCntrDoc_s21b() {
		return this.hasCntrDoc_s21b;
	}

	/**
	 * 設定有無額度明細表
	 * <p/>
	 * 系統
	 **/
	public void setHasCntrDoc_s21b(String value) {
		this.hasCntrDoc_s21b = value;
	}

	/** 取得本額度有無送保 **/
	public String getHeadItem1_s21b() {
		return this.headItem1_s21b;
	}

	/** 設定本額度有無送保 **/
	public void setHeadItem1_s21b(String value) {
		this.headItem1_s21b = value;
	}

	/** 取得信保保證成數 **/
	public BigDecimal getGutPercent_s21b() {
		return this.gutPercent_s21b;
	}

	/** 設定信保保證成數 **/
	public void setGutPercent_s21b(BigDecimal value) {
		this.gutPercent_s21b = value;
	}

	/** 取得有無公司保證人 **/
	public String getHasGuarantor_s21b() {
		return this.hasGuarantor_s21b;
	}

	/** 設定有無公司保證人 **/
	public void setHasGuarantor_s21b(String value) {
		this.hasGuarantor_s21b = value;
	}

	/**
	 * 取得聯貸案且兆豐為擔保品管理行
	 * <p/>
	 * Y/N
	 */
	public String getUnionFlag() {
		return this.unionFlag;
	}

	/**
	 * 設定聯貸案且兆豐為擔保品管理行
	 * <p/>
	 * Y/N
	 **/
	public void setUnionFlag(String value) {
		this.unionFlag = value;
	}

	/** 取得聯貸幣別 **/
	public String getUnionCurr() {
		return this.unionCurr;
	}

	/** 設定聯貸幣別 **/
	public void setUnionCurr(String value) {
		this.unionCurr = value;
	}

	/** 取得本行參貸額度 **/
	public BigDecimal getSyndAmt() {
		return this.syndAmt;
	}

	/** 設定本行參貸額度 **/
	public void setSyndAmt(BigDecimal value) {
		this.syndAmt = value;
	}

	/** 取得聯合授信案總金額 **/
	public BigDecimal getUnionAmt() {
		return this.unionAmt;
	}

	/** 設定聯合授信案總金額 **/
	public void setUnionAmt(BigDecimal value) {
		this.unionAmt = value;
	}

	/** 取得擔保品系統分配後擔保品回收小計 **/
	public BigDecimal getCollateralRecoveryCms() {
		return this.collateralRecoveryCms;
	}

	/** 設定擔保品系統分配後擔保品回收小計 **/
	public void setCollateralRecoveryCms(BigDecimal value) {
		this.collateralRecoveryCms = value;
	}

	/** 取得未建檔擔保品小計 **/
	public BigDecimal getCollateralRecoveryOth() {
		return this.collateralRecoveryOth;
	}

	/** 設定未建檔擔保品小計 **/
	public void setCollateralRecoveryOth(BigDecimal value) {
		this.collateralRecoveryOth = value;
	}

	/**
	 * 取得信用保證回收
	 * <p/>
	 * TWD<br/>
	 * (額度EAD-擔保品合計) * 信保保證成數 * 95%(信保回收率 E33)
	 */
	public BigDecimal getCreditRecovery() {
		return this.creditRecovery;
	}

	/**
	 * 設定信用保證回收
	 * <p/>
	 * TWD<br/>
	 * (額度EAD-擔保品合計) * 信保保證成數 * 95%(信保回收率 E33)
	 **/
	public void setCreditRecovery(BigDecimal value) {
		this.creditRecovery = value;
	}

	/**
	 * 取得預期擔保品回收
	 * <p/>
	 * TWD
	 */
	public BigDecimal getExpectSecuredRecovery() {
		return this.expectSecuredRecovery;
	}

	/**
	 * 設定預期擔保品回收
	 * <p/>
	 * TWD
	 **/
	public void setExpectSecuredRecovery(BigDecimal value) {
		this.expectSecuredRecovery = value;
	}

	/**
	 * 取得預期無擔保回收
	 * <p/>
	 * TWD
	 */
	public BigDecimal getExpectUnsecuredRecovery() {
		return this.expectUnsecuredRecovery;
	}

	/**
	 * 設定預期無擔保回收
	 * <p/>
	 * TWD
	 **/
	public void setExpectUnsecuredRecovery(BigDecimal value) {
		this.expectUnsecuredRecovery = value;
	}

	/**
	 * 取得分配後擔保品回收合計
	 * <p/>
	 * 系統TWD
	 */
	public BigDecimal getCollateralRecovery() {
		return this.collateralRecovery;
	}

	/**
	 * 設定分配後擔保品回收合計
	 * <p/>
	 * 系統TWD
	 **/
	public void setCollateralRecovery(BigDecimal value) {
		this.collateralRecovery = value;
	}

	/**
	 * 取得無擔保回收率有公司保證者
	 * <p/>
	 * E38 44%
	 */
	public BigDecimal getUnsecuredRecoveryRateY() {
		return this.unsecuredRecoveryRateY;
	}

	/**
	 * 設定無擔保回收率有公司保證者
	 * <p/>
	 * E38 44%
	 **/
	public void setUnsecuredRecoveryRateY(BigDecimal value) {
		this.unsecuredRecoveryRateY = value;
	}

	/**
	 * 取得無擔保回收率無公司保證者
	 * <p/>
	 * E39 21%
	 */
	public BigDecimal getUnsecuredRecoveryRateN() {
		return this.unsecuredRecoveryRateN;
	}

	/**
	 * 設定無擔保回收率無公司保證者
	 * <p/>
	 * E39 21%
	 **/
	public void setUnsecuredRecoveryRateN(BigDecimal value) {
		this.unsecuredRecoveryRateN = value;
	}

	/**
	 * 取得信保回收率
	 * <p/>
	 * E33 95%
	 */
	public BigDecimal getGutRecoveryRate() {
		return this.gutRecoveryRate;
	}

	/**
	 * 設定信保回收率
	 * <p/>
	 * E33 95%
	 **/
	public void setGutRecoveryRate(BigDecimal value) {
		this.gutRecoveryRate = value;
	}

	/**
	 * 取得是否為產品自償回收額度
	 * <p/>
	 * Y/N<br/>
	 * 額度明細表科目為出口押匯或應收帳款時有值
	 */
	public String getIsProdRecv() {
		return this.isProdRecv;
	}

	/**
	 * 設定是否為產品自償回收額度
	 * <p/>
	 * Y/N<br/>
	 * 額度明細表科目為出口押匯或應收帳款時有值
	 **/
	public void setIsProdRecv(String value) {
		this.isProdRecv = value;
	}

	/**
	 * 取得產品自償回收額度種類
	 * <p/>
	 * 01.出口押匯<br/>
	 * 02.應收帳款
	 */
	public String getProdRecvType() {
		return this.prodRecvType;
	}

	/**
	 * 設定產品自償回收額度種類
	 * <p/>
	 * 01.出口押匯<br/>
	 * 02.應收帳款
	 **/
	public void setProdRecvType(String value) {
		this.prodRecvType = value;
	}

	/**
	 * 取得產品自償回收率
	 * <p/>
	 * E37 90%
	 */
	public BigDecimal getProdRecvRate() {
		return this.prodRecvRate;
	}

	/**
	 * 設定產品自償回收率
	 * <p/>
	 * E37 90%
	 **/
	public void setProdRecvRate(BigDecimal value) {
		this.prodRecvRate = value;
	}

	/**
	 * 取得帳款管理商類別
	 * <p/>
	 * 當prodRecvType為02時有值:<br/>
	 * 1.本行<br/>
	 * 2.IMPORT FACTOR<br/>
	 * 3.保險公司
	 */
	public String getArAccManagerType_s21b() {
		return this.arAccManagerType_s21b;
	}

	/**
	 * 設定帳款管理商類別
	 * <p/>
	 * 當prodRecvType為02時有值:<br/>
	 * 1.本行<br/>
	 * 2.IMPORT FACTOR<br/>
	 * 3.保險公司
	 **/
	public void setArAccManagerType_s21b(String value) {
		this.arAccManagerType_s21b = value;
	}

	/**
	 * 取得帳款管理商保證成數
	 * <p/>
	 * 帳款管理商類別為2或3有值
	 */
	public BigDecimal getArAccPercent_s21b() {
		return this.arAccPercent_s21b;
	}

	/**
	 * 設定帳款管理商保證成數
	 * <p/>
	 * 帳款管理商類別為2或3有值
	 **/
	public void setArAccPercent_s21b(BigDecimal value) {
		this.arAccPercent_s21b = value;
	}

	/**
	 * 取得產品自償回收
	 * <p/>
	 * 若prodRecvType為01,02時=EAD*E37
	 */
	public BigDecimal getProdRecvTwd() {
		return this.prodRecvTwd;
	}

	/**
	 * 設定產品自償回收
	 * <p/>
	 * 若prodRecvType為01,02時=EAD*E37
	 **/
	public void setProdRecvTwd(BigDecimal value) {
		this.prodRecvTwd = value;
	}

	/**
	 * 取得清償損失率
	 * <p/>
	 * 計算結果
	 */
	public BigDecimal getPayOffLossRate() {
		return this.payOffLossRate;
	}

	/**
	 * 設定清償損失率
	 * <p/>
	 * 計算結果
	 **/
	public void setPayOffLossRate(BigDecimal value) {
		this.payOffLossRate = value;
	}

	/**
	 * 取得清償路徑比例
	 * <p/>
	 * E43 96%
	 */
	public BigDecimal getPayOffPath() {
		return this.payOffPath;
	}

	/**
	 * 設定清償路徑比例
	 * <p/>
	 * E43 96%
	 **/
	public void setPayOffPath(BigDecimal value) {
		this.payOffPath = value;
	}

	/**
	 * 取得協商路徑比例
	 * <p/>
	 * E44 4%
	 */
	public BigDecimal getNegotiatePath() {
		return this.negotiatePath;
	}

	/**
	 * 設定協商路徑比例
	 * <p/>
	 * E44 4%
	 **/
	public void setNegotiatePath(BigDecimal value) {
		this.negotiatePath = value;
	}

	/**
	 * 取得轉正路徑比例
	 * <p/>
	 * E45 0%
	 */
	public BigDecimal getTurnPositivePath() {
		return this.turnPositivePath;
	}

	/**
	 * 設定轉正路徑比例
	 * <p/>
	 * E45 0%
	 **/
	public void setTurnPositivePath(BigDecimal value) {
		this.turnPositivePath = value;
	}

	/**
	 * 取得協商損失率
	 * <p/>
	 * E46 12%
	 */
	public BigDecimal getNegotiateLossRate() {
		return this.negotiateLossRate;
	}

	/**
	 * 設定協商損失率
	 * <p/>
	 * E46 12%
	 **/
	public void setNegotiateLossRate(BigDecimal value) {
		this.negotiateLossRate = value;
	}

	/**
	 * 取得轉正損失率
	 * <p/>
	 * E47 0%
	 */
	public BigDecimal getTurnPositiveLossRate() {
		return this.turnPositiveLossRate;
	}

	/**
	 * 設定轉正損失率
	 * <p/>
	 * E47 0%
	 **/
	public void setTurnPositiveLossRate(BigDecimal value) {
		this.turnPositiveLossRate = value;
	}

	/**
	 * 取得間接成本
	 * <p/>
	 * E48 0.3%
	 */
	public BigDecimal getIndirectCost() {
		return this.indirectCost;
	}

	/**
	 * 設定間接成本
	 * <p/>
	 * E48 0.3%
	 **/
	public void setIndirectCost(BigDecimal value) {
		this.indirectCost = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得其他保證成數 **/
	public BigDecimal getOthPercent_s21b() {
		return this.othPercent_s21b;
	}

	/** 設定其他保證成數 **/
	public void setOthPercent_s21b(BigDecimal value) {
		this.othPercent_s21b = value;
	}

	/** 取得輸入資料檢誤完成(Y/N) **/
	public String getChkYN_s21b() {
		return this.chkYN_s21b;
	}

	/** 設定輸入資料檢誤完成(Y/N) **/
	public void setChkYN_s21b(String value) {
		this.chkYN_s21b = value;
	}

	/** 取得EAD模型版本_大版 **/
	public Integer getEadVer1() {
		return this.eadVer1;
	}

	/** 設定EAD模型版本_大版 **/
	public void setEadVer1(Integer value) {
		this.eadVer1 = value;
	}

	/** 取得EAD模型版本_小版 **/
	public Integer getEadVer2() {
		return this.eadVer2;
	}

	/** 設定EAD模型版本_小版 **/
	public void setEadVer2(Integer value) {
		this.eadVer2 = value;
	}

	/** 設定LGD業務種類 **/
	public void setBussType_s21b(String bussType_s21b) {
		this.bussType_s21b = bussType_s21b;
	}

	/** 取得LGD業務種類 **/
	public String getBussType_s21b() {
		return bussType_s21b;
	}

	/** 設定業務種類LGD **/
	public void setBussLgd(BigDecimal bussLgd) {
		this.bussLgd = bussLgd;
	}

	/** 取得業務種類LGD **/
	public BigDecimal getBussLgd() {
		return bussLgd;
	}

	/** 設定擔保品系統分配後擔保品回收小計(含信保) **/
	public void setCollateralRecoveryCmsSme(BigDecimal collateralRecoveryCmsSme) {
		this.collateralRecoveryCmsSme = collateralRecoveryCmsSme;
	}

	/** 取得屬本行授信業務授權準則得單獨劃分之業務類 **/
	public BigDecimal getCollateralRecoveryCmsSme() {
		return collateralRecoveryCmsSme;
	}

	/** 設定未建檔擔保品小計(含信保) **/
	public void setCollateralRecoveryOthSme(BigDecimal collateralRecoveryOthSme) {
		this.collateralRecoveryOthSme = collateralRecoveryOthSme;
	}

	/** 取得未建檔擔保品小計(含信保) **/
	public BigDecimal getCollateralRecoveryOthSme() {
		return collateralRecoveryOthSme;
	}

	/** 設定屬本行授信業務授權準則得單獨劃分之業務類 **/
	public void setIsStandAloneAuth_s21b(String isStandAloneAuth_s21b) {
		this.isStandAloneAuth_s21b = isStandAloneAuth_s21b;
	}

	/** 取得屬本行授信業務授權準則得單獨劃分之業務類 **/
	public String getIsStandAloneAuth_s21b() {
		return isStandAloneAuth_s21b;
	}

	/** 設定現請額度幣別 **/
	public void setCurrentApplyCurr_s21b(String currentApplyCurr_s21b) {
		this.currentApplyCurr_s21b = currentApplyCurr_s21b;
	}

	/** 取得現請額度幣別 **/
	public String getCurrentApplyCurr_s21b() {
		return currentApplyCurr_s21b;
	}

	/** 設定現請額度兌台幣匯率 **/
	public void setCurrentRate_s21b(BigDecimal currentRate_s21b) {
		this.currentRate_s21b = currentRate_s21b;
	}

	/** 取得現請額度兌台幣匯率 **/
	public BigDecimal getCurrentRate_s21b() {
		return currentRate_s21b;
	}

	public void setIsGuarantorEffect_s21b(String isGuarantorEffect_s21b) {
		this.isGuarantorEffect_s21b = isGuarantorEffect_s21b;
	}

	public String getIsGuarantorEffect_s21b() {
		return isGuarantorEffect_s21b;
	}

	public void setGuarantorId_s21b(String guarantorId_s21b) {
		this.guarantorId_s21b = guarantorId_s21b;
	}

	public String getGuarantorId_s21b() {
		return guarantorId_s21b;
	}

	public void setGuarantorDupNo_s21b(String guarantorDupNo_s21b) {
		this.guarantorDupNo_s21b = guarantorDupNo_s21b;
	}

	public String getGuarantorDupNo_s21b() {
		return guarantorDupNo_s21b;
	}

	public void setGuarantorRName_s21b(String guarantorRName_s21b) {
		this.guarantorRName_s21b = guarantorRName_s21b;
	}

	public String getGuarantorRName_s21b() {
		return guarantorRName_s21b;
	}

	public void setGuarantorCrdType_s21b(String guarantorCrdType_s21b) {
		this.guarantorCrdType_s21b = guarantorCrdType_s21b;
	}

	public String getGuarantorCrdType_s21b() {
		return guarantorCrdType_s21b;
	}

	public void setGuarantorCrdTYear_s21b(Date guarantorCrdTYear_s21b) {
		this.guarantorCrdTYear_s21b = guarantorCrdTYear_s21b;
	}

	public Date getGuarantorCrdTYear_s21b() {
		return guarantorCrdTYear_s21b;
	}

	public void setGuarantorGradeOrg_s21b(String guarantorGradeOrg_s21b) {
		this.guarantorGradeOrg_s21b = guarantorGradeOrg_s21b;
	}

	public String getGuarantorGradeOrg_s21b() {
		return guarantorGradeOrg_s21b;
	}

	public void setGuarantorGradeNew_s21b(String guarantorGradeNew_s21b) {
		this.guarantorGradeNew_s21b = guarantorGradeNew_s21b;
	}

	public String getGuarantorGradeNew_s21b() {
		return guarantorGradeNew_s21b;
	}

	public void setGuarantorCptlCurr_s21b(String guarantorCptlCurr_s21b) {
		this.guarantorCptlCurr_s21b = guarantorCptlCurr_s21b;
	}

	public String getGuarantorCptlCurr_s21b() {
		return guarantorCptlCurr_s21b;
	}

	public void setGuarantorCptlAmt_s21b(BigDecimal guarantorCptlAmt_s21b) {
		this.guarantorCptlAmt_s21b = guarantorCptlAmt_s21b;
	}

	public BigDecimal getGuarantorCptlAmt_s21b() {
		return guarantorCptlAmt_s21b;
	}

	public void setGuarantorCptlUnit_s21b(BigDecimal guarantorCptlUnit_s21b) {
		this.guarantorCptlUnit_s21b = guarantorCptlUnit_s21b;
	}

	public BigDecimal getGuarantorCptlUnit_s21b() {
		return guarantorCptlUnit_s21b;
	}

	public void setGuarantorNtCode_s21b(String guarantorNtCode_s21b) {
		this.guarantorNtCode_s21b = guarantorNtCode_s21b;
	}

	public String getGuarantorNtCode_s21b() {
		return guarantorNtCode_s21b;
	}

	public void setGuarantorStockStatus_s21b(String guarantorStockStatus_s21b) {
		this.guarantorStockStatus_s21b = guarantorStockStatus_s21b;
	}

	public String getGuarantorStockStatus_s21b() {
		return guarantorStockStatus_s21b;
	}

	public void setGuarantorStockNum_s21b(String guarantorStockNum_s21b) {
		this.guarantorStockNum_s21b = guarantorStockNum_s21b;
	}

	public String getGuarantorStockNum_s21b() {
		return guarantorStockNum_s21b;
	}

	public void setGuarantorCrdType2_s21b(String guarantorCrdType2_s21b) {
		this.guarantorCrdType2_s21b = guarantorCrdType2_s21b;
	}

	public String getGuarantorCrdType2_s21b() {
		return guarantorCrdType2_s21b;
	}

	public void setGuarantorCrdTYear2_s21b(Date guarantorCrdTYear2_s21b) {
		this.guarantorCrdTYear2_s21b = guarantorCrdTYear2_s21b;
	}

	public Date getGuarantorCrdTYear2_s21b() {
		return guarantorCrdTYear2_s21b;
	}

	public void setGuarantorGradeOrg2_s21b(String guarantorGradeOrg2_s21b) {
		this.guarantorGradeOrg2_s21b = guarantorGradeOrg2_s21b;
	}

	public String getGuarantorGradeOrg2_s21b() {
		return guarantorGradeOrg2_s21b;
	}

	public void setGuarantorGradeNew2_s21b(String guarantorGradeNew2_s21b) {
		this.guarantorGradeNew2_s21b = guarantorGradeNew2_s21b;
	}

	public String getGuarantorGradeNew2_s21b() {
		return guarantorGradeNew2_s21b;
	}

	public void setGuarantorStkCatNm_s21b(String guarantorStkCatNm_s21b) {
		this.guarantorStkCatNm_s21b = guarantorStkCatNm_s21b;
	}

	public String getGuarantorStkCatNm_s21b() {
		return guarantorStkCatNm_s21b;
	}

	public void setGuarantorCrdType3_s21b(String guarantorCrdType3_s21b) {
		this.guarantorCrdType3_s21b = guarantorCrdType3_s21b;
	}

	public String getGuarantorCrdType3_s21b() {
		return guarantorCrdType3_s21b;
	}

	public void setGuarantorCrdTYear3_s21b(Date guarantorCrdTYear3_s21b) {
		this.guarantorCrdTYear3_s21b = guarantorCrdTYear3_s21b;
	}

	public Date getGuarantorCrdTYear3_s21b() {
		return guarantorCrdTYear3_s21b;
	}

	public void setGuarantorGradeOrg3_s21b(String guarantorGradeOrg3_s21b) {
		this.guarantorGradeOrg3_s21b = guarantorGradeOrg3_s21b;
	}

	public String getGuarantorGradeOrg3_s21b() {
		return guarantorGradeOrg3_s21b;
	}

	public void setGuarantorGradeNew3_s21b(String guarantorGradeNew3_s21b) {
		this.guarantorGradeNew3_s21b = guarantorGradeNew3_s21b;
	}

	public String getGuarantorGradeNew3_s21b() {
		return guarantorGradeNew3_s21b;
	}

	public void setGuarantorStkCatNm3_s21b(String guarantorStkCatNm3_s21b) {
		this.guarantorStkCatNm3_s21b = guarantorStkCatNm3_s21b;
	}

	public String getGuarantorStkCatNm3_s21b() {
		return guarantorStkCatNm3_s21b;
	}

}
