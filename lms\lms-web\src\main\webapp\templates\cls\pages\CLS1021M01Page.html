<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
				<script type="text/javascript">
					loadScript('pagejs/cls/CLS1021M01Page');
				</script>
			<div class="button-menu funcContainer" id="buttonPanel">
				
			<!--編製中 -->
			<th:block th:if="${_btnDOC_EDITING_visible}">
	        		<button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04" ></span>
	        			<th:block th:text="#{button.save}"><!--儲存--></th:block>
	        		</button>
					<button id="btnSend" >
	        			<span class="ui-icon ui-icon-jcs-02" ></span>
	        			<th:block th:text="#{button.send}" ><!--呈主管覆核--></th:block>
	        		</button>
		        </th:block>		
				
				<!--待覆核 -->
				<th:block th:if="${_btnWAIT_APPROVE_visible}">
	        		<button id="btnCheck" >
	        			<span class="ui-icon ui-icon-jcs-106" ></span>
	        			<th:block th:text="#{button.check}" ><!--覆核--></th:block>
	        		</button>
	        	<!--	<button id="btnTest"  class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					壓力測試用
					</button>-->
		        </th:block>				
		        
                <button id="btnPrint" class="forview">
                	<span class="ui-icon ui-icon-jcs-03"></span>
					<th:block th:text="#{button.print}"><!--列印--></th:block>
				</button>
                <button id="btnExit"  class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{button.exit}"><!--離開--></th:block>
				</button>
				
            </div>
			<div class="tit2 color-black">
				<span id="title" ></span>：
				(<span id="showTypCd" class="text-red"></span>)<span id="showCustId" class="color-blue" ></span>
			</div>
			<div class="tabs doc-tabs">
                <ul>
                	<li id="tabs_1" > <a href="#tab-01" goto="01"><b><th:block th:text="#{doc.docinfo}"><!--  文件資訊--></th:block></b></a></li>
				 </ul>
                <div class="tabCtx-warp">
                	
                		<div id="tabs-00" th:id="${_tabCtx}" th:insert="~{${panelName}::${panelFragmentName}}"></div>
				</div>
			</div>
			<div id="openCheckBox" style="display:none"> 
				<div>
				 <span id="check1" style="display:none">
				 	<label><input name="checkRadio" type="radio" value="3"><th:block th:text="#{C102M01A.bt12}"><!--  核准--></th:block></label><br/>
					<label><input name="checkRadio" type="radio" value="1"><th:block th:text="#{C102M01A.bt11}"><!--  退回經辦修改--></th:block></label>
				</span>
				</div>
			</div>
			<div id="openChecDatekBox" style="display:none"> 
				<div>
					<input id="forCheckDate" type="text" size="10" maxlength="10" class="date">	
				</div>
			</div>
			
			  <div id="selectBossBox"  style="display:none;">
			  <form id="selectBossForm">
	         	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                 <tr>
	            		<td class="hd1" width="60%"><th:block th:text="#{C102M01B.selectBoss}"><!--  授信主管人數--></th:block>&nbsp;&nbsp;</td>
	                    <td width="40%"><select id="numPerson" name="numPerson">
	                    		<option value="1">1</option>
	                    		<option value="2">2</option>
	                            <option value="3">3</option>
								<option value="4">4</option>
	                    		<option value="5">5</option>
	                            <option value="6">6</option>
								<option value="7">7</option>
	                    		<option value="8">8</option>
	                            <option value="9">9</option>
								<option value="10">10</option>
	                    	</select>
							</td>
	                 </tr>
	                 <tr >
	                 	<td class="hd1" ><th:block th:text="#{C102M01B.bossId}"><!--  授信主管--></th:block>&nbsp;&nbsp;</td>
	            		<td >
	            			<div id="bossItem"></div>
	                 	</td>
	                 </tr>
	                 <tr >
	            		<td class="hd1"><th:block th:text="#{C102M01B.managerId}"><!--經副襄理--></th:block>&nbsp;&nbsp;</td>
	                    <td><div id="managerItem"></div></td>
	                 </tr>
	           	 </table>
				</form>
  			</div>
		
		</th:block>
    </body>
</html>
