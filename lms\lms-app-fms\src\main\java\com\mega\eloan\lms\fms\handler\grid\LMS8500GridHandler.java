package com.mega.eloan.lms.fms.handler.grid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.service.LMS8500Service;
import com.mega.eloan.lms.model.L850M01A;
import com.mega.eloan.lms.model.L850M01C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 資料上傳作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms8500gridhandler")
public class LMS8500GridHandler extends AbstractGridHandler {

	@Resource
	LMSService lmsService;

	@Resource
	LMS8500Service lms8500Service;

	@Resource
	CodeTypeService codeTypeService;

	@SuppressWarnings("unchecked")
	public CapGridResult queryL850m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				UtilConstants.Field.目前編製行, user.getUnitNo());

		// 前端可篩選只顯示某上傳類別
		String appCodeFull = Util.nullToSpace(params.getString("appCode"));
		if (Util.isNotEmpty(appCodeFull)) {
			// 取大類appCode即可
			String appCode = appCodeFull.split("-")[0];
			// String version = appCodeFull.split("-").length > 1 ? appCodeFull
			// .split("-")[1] : "";
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "appCode",
					appCode);
		}

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");

		Page<? extends GenericBean> page = lms8500Service.findPage(
				L850M01A.class, pageSetting);

		List<L850M01A> l850m01aList = (List<L850M01A>) page.getContent();

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("appCode", new CodeTypeFormatter(codeTypeService,
				"L850M01A_APPCODE", CodeTypeFormatter.ShowTypeEnum.Desc));

		formatter.put("docStatusStr", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String docStatusStr = "";
				L850M01A l850m01a = (L850M01A) in;
				String docStatus = Util.trim(l850m01a.getDocStatus());
				if (Util.isNotEmpty(docStatus)) {
					docStatusStr = getMessage("docStatus."
							+ CreditDocStatusEnum.getEnum(docStatus).getCode());
				}

				return docStatusStr;
			}
		});

		formatter.put("updaterName", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String updaterName = "";
				L850M01A l850m01a = (L850M01A) in;
				String creator = Util.trim(l850m01a.getCreator());
				if (Util.isNotEmpty(creator)) {
					updaterName = lmsService.getUserName(creator);
				}

				return updaterName;
			}
		});

		formatter.put("approverName", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String approverName = "";
				L850M01A l850m01a = (L850M01A) in;
				String approver = Util.trim(l850m01a.getApprover());
				if (Util.isNotEmpty(approver)) {
					approverName = lmsService.getUserName(approver);
				}

				return approverName;
			}
		});

		return new CapGridResult(l850m01aList, page.getTotalRow(), formatter);

	}

	/**
	 * for各自交易會用到的查找L850m01c
	 * 
	 * 自己寫一隻自己的function吧，不需要共用
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL850m01cV090(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		// 撈出L850M01C
		List<? extends GenericBean> l850m01cList = lms8500Service
				.findListByMainId(L850M01C.class, mainId);

		//
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		for (GenericBean bean : l850m01cList) {
			L850M01C l850m01c = (L850M01C) bean;

			Map<String, Object> row = new HashMap<String, Object>();

			if (l850m01c != null) {
				// 1:CODETYPE
				// 2:CODEVALUE
				// 3:CODEDESC
				// 4:LOCALE
				// 5:CODEDESC2
				// 6:CODEDESC3
				// 7:CODEORDER

				String codeType = l850m01c.getDetail1();// 代碼類型
				String codeValue = l850m01c.getDetail2();// 代碼值
				String codeDesc = l850m01c.getDetail3();// 語言別
				String codeOrder = l850m01c.getDetail7();// 代碼順序
				String locale = l850m01c.getDetail4();// 語言別

				row.put("codeType", codeType);
				row.put("codeValue", codeValue);
				row.put("codeDesc", codeDesc);
				row.put("codeOrder", codeOrder);
				row.put("locale", locale);
				list.add(row);
			}

		}

		return new CapMapGridResult(list, list.size());
	}
}