/* 
 * C101S02S.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 聯徵T70證券暨期貨違約交割記錄資訊檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S02S", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo"}))
public class C101S02S extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;
	
	/** 分行別 **/
	@Size(max = 3)
	@Column(name = "BRANCHNO", length = 3, columnDefinition = "CHAR(3)")
	private String branchNo;

	/**
	 * A1:成功  
     * A2:待證交所回復  
     * A3:處理成功 
     * A4:處理失敗  
     * A5:查無資料或逾時未取回資料 
     * A6:未開放  
     * A7:重複查
	 */
	@Size(max = 2)
	@Column(name = "DATASTATUS", length = 2, columnDefinition = "CHAR(2)")
	private String dataStatus;
	
	/** 建立人員 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
//	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 是否更新查詢聯徵資料狀態
	 */
	@Column(name = "REFRESHSTATUS", length = 1, columnDefinition = "CHAR(1)")
	private String refreshStatus;

	/** 聯徵查詢成功日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "QSUCCESSDATE", columnDefinition = "DATE")
	private Date qSuccessDate;
	
	/** 查詢時間 **/
	@Column(name="QUERYTIME", columnDefinition="TIMESTAMP")
	private Date queryTime;
	
	@Size(max = 2)
	@Column(name = "NEGFLAG", length = 2, columnDefinition = "CHAR(2)")
	private String negFlag;
	
	@Digits(integer = 10, fraction = 0)
	@Column(name = "NEGAMT", length = 1, columnDefinition = "DECIMAL(10,0)")
	private BigDecimal negAmt;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	public String getDataStatus() {
		return this.dataStatus;
	}

	public void setDataStatus(String value) {
		this.dataStatus = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	public String getBranchNo() {
		return branchNo;
	}

	public void setBranchNo(String branchNo) {
		this.branchNo = branchNo;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getqSuccessDate() {
		return qSuccessDate;
	}

	public void setqSuccessDate(Date qSuccessDate) {
		this.qSuccessDate = qSuccessDate;
	}

	public String getNegFlag() {
		return negFlag;
	}

	public void setNegFlag(String negFlag) {
		this.negFlag = negFlag;
	}

	public BigDecimal getNegAmt() {
		return negAmt;
	}

	public void setNegAmt(BigDecimal negAmt) {
		this.negAmt = negAmt;
	}

	public Date getQueryTime() {
		return queryTime;
	}

	public void setQueryTime(Date queryTime) {
		this.queryTime = queryTime;
	}

	public String getRefreshStatus() {
		return refreshStatus;
	}

	public void setRefreshStatus(String refreshStatus) {
		this.refreshStatus = refreshStatus;
	}
}
