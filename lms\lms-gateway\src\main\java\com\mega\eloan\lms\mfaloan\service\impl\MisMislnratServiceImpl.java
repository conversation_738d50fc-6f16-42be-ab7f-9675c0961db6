/* 
 *MisMislnratServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.stereotype.Service;

import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;

import tw.com.jcs.common.Util;

@Service
public class MisMislnratServiceImpl extends AbstractMFAloanJdbc implements
		MisMislnratService {

	@Resource
	EloandbBASEService eloandbBASEService;

	public List<Map<String, Object>> findMislnratByLRRate(String type,
			String curr) {

		// SELECT
		// (CASE WHEN LR_CURR = 'CNY' AND LR_CODE IN
		// ('MI','MJ','MK','ML','MM','MN','MO') THEN 0 ELSE LR_RATE END) AS
		// LR_RATE,
		// LR_RATE AS ORG_LR_RATE FROM MIS.MISLNRAT WHERE LR_CODE = ? AND
		// LR_CURR = ?

		return this.getJdbc().queryForList("MIS.MISLNRAT.selByLR_RATE",
				new Object[] { type, curr });
	}

	@Override
	public HashMap<String, LinkedHashMap<String, String>> findBaseRateByCurrs(
			String[] currs) {
		HashMap<String, LinkedHashMap<String, String>> totalMap = new HashMap<String, LinkedHashMap<String, String>>();

		if (currs.length > 0) {
//			StringBuffer temp = new StringBuffer(0);
			String currParams = Util.genSqlParam(currs);
//			for (String curr : currs) {
//				temp.append(temp.length() > 0 ? "," : "");
//				temp.append("'").append(curr).append("'");
//			}

			// MIS.MISLNRAT利率更名LR_RATE_CNAME
			JSONObject jsonChgRateName = this
					.getSysParamJsonObject("LMS_MISLNRAT_CHG_LR_RATE_CNAME");

			List<Map<String, Object>> rowData = this.getJdbc()
					.queryForListByCustParam("MIS.MISLNRAT.selBaseRateByCurr",
							new Object[] { currParams }, currs);
			for (Map<String, Object> row : rowData) {
				String curr = Util.trim(row.get("LR_CURR"));
				String key = Util.trim(row.get("LR_CODE"));
				String value = Util.trim(row.get("LR_RATE_CNAME"));

				// 1.eloan額度明細表利率條件組字, 針對mb利率一律改為「本行計息基礎」一詞呈現
				// 2.eloan利率選單中mb利率之名稱改為「本行借入資金成本(本行計息基礎)」
				// 3.另btt 0191利率選單名稱亦請比照辦理!
				// MIS.MISLNRAT利率更名LR_RATE_CNAME
				if (jsonChgRateName.containsKey(key)) {
					value = Util.trim(jsonChgRateName.getString(key));
				}

				if (totalMap.containsKey(curr)) {
					totalMap.get(curr).put(key, value);
				} else {
					LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
					map.put(key, value);
					totalMap.put(curr, map);
				}
			}
		}
		return totalMap;
	}

	@Override
	public List<Map<String, Object>> findByCurrAndCodes(String curr,
			String... codes) {
		String codeParams = Util.genSqlParam(codes);
		List<Object> params = new ArrayList<Object>();
		params.add(curr);
		params.addAll(Arrays.asList(codes));


		// SELECT
		// LR_CODE,
		// (CASE WHEN LR_CURR = ''CNY'' AND LR_CODE IN
		// (''MI'',''MJ'',''MK'',''ML'',''MM'',''MN'',''MO'') THEN 0 ELSE
		// LR_RATE END) AS LR_RATE,
		// LR_RATE AS ORG_LR_RATE
		// FROM MIS.MISLNRAT where LR_CURR =? AND LR_CODE in ({0})

		return this.getJdbc().queryForListByCustParam(
				"MIS.MISLNRAT.selByCurrAndCodes", new Object[] { codeParams },
				params.toArray(new Object[0]));
	}

	@Override
	public List<Map<String, Object>> findByCurr(String curr) {

		// SELECT
		// LR_CODE,
		// (CASE WHEN LR_CURR = 'CNY' AND LR_CODE IN
		// ('MI','MJ','MK','ML','MM','MN','MO') THEN 0 ELSE LR_RATE END) AS
		// LR_RATE,
		// LR_RATE AS ORG_LR_RATE
		// FROM MIS.MISLNRAT where LR_CURR =?

		return this.getJdbc().queryForList("MIS.MISLNRAT.selByCurr",
				new Object[] { curr });
	}

	@Override
	public List<Map<String, Object>> findAllBaseRateByCurr(String curr) {

		// SELECT LR_CODE,LR_RATE_CNAME,
		// (CASE WHEN LR_CURR = 'CNY' AND LR_CODE IN
		// ('MI','MJ','MK','ML','MM','MN','MO') THEN 0 ELSE LR_RATE END) AS
		// LR_RATE,
		// LR_RATE AS ORG_LR_RATE
		// FROM MIS.MISLNRAT where LR_CURR =? order by LR_CODE ASC

		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.MISLNRAT.selAllBaseRateByCurr", new Object[] { curr });

		// MIS.MISLNRAT利率更名LR_RATE_CNAME
		JSONObject jsonChgRateName = this
				.getSysParamJsonObject("LMS_MISLNRAT_CHG_LR_RATE_CNAME");

		for (Map<String, Object> row : rowData) {

			String key = Util.trim(row.get("LR_CODE"));
			String value = Util.trim(row.get("LR_RATE_CNAME"));

			// 1.eloan額度明細表利率條件組字, 針對mb利率一律改為「本行計息基礎」一詞呈現
			// 2.eloan利率選單中mb利率之名稱改為「本行借入資金成本(本行計息基礎)」
			// 3.另btt 0191利率選單名稱亦請比照辦理!
			// MIS.MISLNRAT利率更名LR_RATE_CNAME
			if (jsonChgRateName.containsKey(key)) {
				value = Util.trim(jsonChgRateName.getString(key));
			}

			row.put("LR_RATE_CNAME", value);
		}

		return rowData;
	}

	@Override
	public List<Map<String, Object>> findMislnratAllRateCode() {

		// SELECT DISTINCT LR_CODE,LR_RATE_CNAME FROM MIS.MISLNRAT ORDER BY
		// LR_CODE ASC

		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.MISLNRAT.selAllBaseRateCode", new Object[] {});

		// MIS.MISLNRAT利率更名LR_RATE_CNAME
		JSONObject jsonChgRateName = this
				.getSysParamJsonObject("LMS_MISLNRAT_CHG_LR_RATE_CNAME");

		for (Map<String, Object> row : rowData) {

			String key = Util.trim(row.get("LR_CODE"));
			String value = Util.trim(row.get("LR_RATE_CNAME"));

			// 1.eloan額度明細表利率條件組字, 針對mb利率一律改為「本行計息基礎」一詞呈現
			// 2.eloan利率選單中mb利率之名稱改為「本行借入資金成本(本行計息基礎)」
			// 3.另btt 0191利率選單名稱亦請比照辦理!
			// MIS.MISLNRAT利率更名LR_RATE_CNAME
			if (jsonChgRateName.containsKey(key)) {
				value = Util.trim(jsonChgRateName.getString(key));
			}

			row.put("LR_RATE_CNAME", value);
		}

		return rowData;
	}

	@Override
	public List<Map<String, Object>> findLrCodeBySpecificCurrency(String curr,
			Map<String, String> currencyMap) {

		Set<String> currencySet = currencyMap.keySet();
		String[] paramArray = new String[currencySet.size() + 1];

		int i = 0;
		paramArray[i++] = curr;
		for (String key : currencySet) {
			paramArray[i++] = key;
		}

		String questionMark = StringUtils.repeat("?,", currencySet.size());
		questionMark = questionMark.substring(0, questionMark.lastIndexOf(','));

		// SELECT LR_CODE, LR_RATE_CNAME
		// FROM MIS.MISLNRAT
		// WHERE LR_CURR = ? and LR_CURR IN ({0})

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForAllListByCustParam(
						"MIS.MISLNRAT.selectLrCodeBySpecificCurrency",
						new Object[] { questionMark }, paramArray);

		// MIS.MISLNRAT利率更名LR_RATE_CNAME
		JSONObject jsonChgRateName = this
				.getSysParamJsonObject("LMS_MISLNRAT_CHG_LR_RATE_CNAME");

		for (Map<String, Object> row : rowData) {

			String key = Util.trim(row.get("LR_CODE"));
			String value = Util.trim(row.get("LR_RATE_CNAME"));

			// 1.eloan額度明細表利率條件組字, 針對mb利率一律改為「本行計息基礎」一詞呈現
			// 2.eloan利率選單中mb利率之名稱改為「本行借入資金成本(本行計息基礎)」
			// 3.另btt 0191利率選單名稱亦請比照辦理!
			// MIS.MISLNRAT利率更名LR_RATE_CNAME
			if (jsonChgRateName.containsKey(key)) {
				value = Util.trim(jsonChgRateName.getString(key));
			}

			row.put("LR_RATE_CNAME", value);
		}

		return rowData;

	}

	@Override
	public Map<String, Object> findLrCodeByCurr(String curr, String rateCode) {

		// SELECT LR_CODE, LR_RATE_CNAME
		// FROM MIS.MISLNRAT
		// WHERE LR_CURR = ? and LR_CODE = ?

		Map<String, Object> row = this.getJdbc().queryForMap(
				"MIS.MISLNRAT.selectLrCodeByCurr",
				new Object[] { curr, rateCode });
		
		if(row == null){
			return null;
		}

		// MIS.MISLNRAT利率更名LR_RATE_CNAME
		JSONObject jsonChgRateName = this
				.getSysParamJsonObject("LMS_MISLNRAT_CHG_LR_RATE_CNAME");

		String key = Util.trim(row.get("LR_CODE"));
		String value = Util.trim(row.get("LR_RATE_CNAME"));

		// 1.eloan額度明細表利率條件組字, 針對mb利率一律改為「本行計息基礎」一詞呈現
		// 2.eloan利率選單中mb利率之名稱改為「本行借入資金成本(本行計息基礎)」
		// 3.另btt 0191利率選單名稱亦請比照辦理!
		// MIS.MISLNRAT利率更名LR_RATE_CNAME
		 
		if (jsonChgRateName.containsKey(key)) {
			value = Util.trim(jsonChgRateName.getString(key));
		}

		row.put("LR_RATE_CNAME", value);

		return row;
	}

	@Override
	public JSONObject getSysParamJsonObject(String param) {
		JSONObject returnJson = null;
		String PARAMVALUE = "";

		Map<String, Object> onlineDateMap = eloandbBASEService
				.getSysParamData(param);
		if (onlineDateMap != null && !onlineDateMap.isEmpty()) {
			PARAMVALUE = Util.trim(onlineDateMap.get("PARAMVALUE"));
		} else {
			PARAMVALUE = "";
		}

		if (!Util.isEmpty(PARAMVALUE)) {
			returnJson = JSONObject.fromObject("{" + PARAMVALUE + "}");
		}

		return returnJson;
	}
	
	//查詢所有特定金錢信託
	@Override
	public List<Map<String, Object>> findAllLrCodeBySpecificCurrencies(Map<String, String> currencyMap) {

		Set<String> currencySet = currencyMap.keySet();
		String[] paramArray = new String[currencySet.size()];

		int i = 0;
		for (String key : currencySet) {
			paramArray[i++] = key;
		}

		String questionMark = StringUtils.repeat("?,", currencySet.size());
		questionMark = questionMark.substring(0, questionMark.lastIndexOf(','));
		
		/*
			SELECT LR_CODE, LR_RATE, LR_CURR
			FROM MIS.MISLNRAT
			WHERE LR_CURR IN ({0})
		*/
		
		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForAllListByCustParam(
						"MIS.MISLNRAT.selectAllLrCodeBySpecificCurrencies",
						new Object[] { questionMark }, paramArray);

		return rowData;

	}
}
