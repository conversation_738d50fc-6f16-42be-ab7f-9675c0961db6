---------------------------------------------------------
-- LMS.L999M01C 企金約據書連保人(保證人)檔
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L999M01C;
CREATE TABLE LMS.L999M01C (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	CUSTID        VARCHAR(10)   not null,
	<PERSON>UPNO         CHAR(1)       not null,
	CUSTPOS       CHAR(1)       not null,
	CUSTNAME      VARCHAR(120) ,
	ADDRZIP       DECIMAL(5,0) ,
	ADDRCITY      VARCHAR(12)  ,
	ADDRTOWN      VARCHAR(12)  ,
	ADDR          VARCHAR(300) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L999M01C PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL999M01C01;
CREATE UNIQUE INDEX LMS.XL999M01C01 ON LMS.L999M01C   (MAINID, CUSTID, DUPNO, CUSTPOS);
--DROP INDEX LMS.XL999M01C02;
CREATE INDEX LMS.XL999M01C02 ON LMS.L999M01C   (CUSTID, DUPNO);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L999M01C IS '企金約據書連保人(保證人)檔';
COMMENT ON LMS.L999M01C (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	CUSTID        IS '統一編號', 
	DUPNO         IS '重覆序號', 
	CUSTPOS       IS '性質(相關身份)', 
	CUSTNAME      IS '名稱', 
	ADDRZIP       IS '郵遞區號', 
	ADDRCITY      IS '地址(縣市)', 
	ADDRTOWN      IS '地址(區鄉鎮市)', 
	ADDR          IS '地址', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
