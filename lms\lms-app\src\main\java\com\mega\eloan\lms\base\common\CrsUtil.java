package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.mfaloan.bean.ELF516;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.C241M01C;
import com.mega.eloan.lms.model.C900M01A;
import com.mega.eloan.lms.model.C900S02F;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.sso.userdetails.MegaSSOUserDetails;

public class CrsUtil {
	// J-111-0636_05097_B1001 Web e-Loan企金授信配合私人銀行帳務與金控總部分行區隔解決方案討論會議結論辦理
	public static final int SUMMARY_PREFIX_LEN = 3;
	/**
	 * 純不動產、純有擔科目-全部短期
	 */
	public static final String PG_COLLNO_ONLY_01___LNAP_ONLY_2 = "S";
	/**
	 * 純不動產、純有擔科目-含中長期
	 */
	public static final String PG_COLLNO_ONLY_01___LNPA_46_OR_246 = "L";
	/**
	 * 純不動產含無擔科目(非十足擔保)
	 */
	public static final String PG_COLLNO_ONLY_01___LNAP_IN_135 = "N";
	/**
	 * 非純不動產
	 */
	public static final String PG_HAS_COLL___COLLNO_NOT_ONLY_01 = "X";

	public static final String BATCH_FUNC_NAME_CHKR1R2R4_GEN = "chkR1R2R4_gen";
	public static final String BATCH_FUNC_NAME_CHKR1R2R4_PROCESS = "chkR1R2R4_process";

	public static final String NO_LN_DATA = "no LN data";

	public static final String RULE_DELIM = ";";
	public static final String ELF498_DELIM = "、";
	public static final String R1 = "1";
	public static final String R2 = "2";

	// R1, R2 是針對「門檻以上」
	// 但2020-05後，增加「門檻以下」的抽樣
	/*
	 * 第十一條 (免辦理覆審之情事)
	 * 七、個人授信額度以不動產十足擔保，臺北市、新北市營業單位敘作額度新臺幣三千萬元(不含)以上，其餘地區營業單位敘作額度新臺幣ㄧ千五百萬元
	 * （不含）以上之中長期授信案件，除新做、增額案件依第六條第一項規定辦理一次一般覆審外，免再辦理覆審
	 * 
	 * 門檻以上的中長期，看一次 門檻以上的短期，舊案每年看一次
	 * 
	 * 門檻以下的{中長期，短期}都納入抽樣
	 */
	public static final String R1_1___R2_1 = "1~2";
	public static final String R1R2S = "R1R2S"; // J-109-0213, 申請單號 (109) 第 1603
												// 號 , 自 2020-05-22 修訂覆審作業須知

	/**
	 * J-111-0622<BR>
	 * (不含個人授信額度以不動產十足擔保之授信案件)
	 * 單一授信額度在新臺幣一千萬元以下
	 * 且為十足擔保授信或經信用保證基金保證成數七成以上者
	 * 有循環額度
	 * 之抽樣
	 */
	public static final String R14 = "14";

	public static final String R3 = "3";
	public static final String R4 = "4";
	public static final String R5 = "5";
	public static final String R6_1 = "6-1";
	public static final String R6_2 = "6-2";
	public static final String R7 = "7";
	public static final String R8_1 = "8-1";
	public static final String R8_2 = "8-2";
	public static final String R9 = "9";
	public static final String R10 = "10";
	public static final String R11 = "11";
	public static final String R12 = "12"; // 勞工紓困貸款，免覆審
	public static final String R13 = "13"; // 專案信貸，其產品 in ('08', '71')
	
	/**
	 * J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
	 * 利息或本金遲延61天(含)以上記錄, 但未達須註記逾期者
	 */
	public static final String R15 = "15"; 
	
	// 新增覆審種類 要在 key="ELF491.sel_gfnGetWillOverReviewData" 增加
	public static final String R95 = "95"; // J-109-0344, 申請單號 (109) 第 2561 號,
											// 將第1批名單寫入婉卻檔
	public static final String R95_1 = "95-1"; // J-109-0372, 申請單號 (109) 第 2669
												// 號,
												// 109.8.25兆銀總授審字第1090045985號函,疑似人頭戶專案覆審
												// (1)第一年全面辦理專案覆審
												// (2)嗣後逐年按受檢追蹤對象之10%範圍內

	// ============================================================================
	// J-109-0372 判斷 批次SLMS-00127 是否已完成(單筆)
	public static final String DONE_95_1_RULE_NO = "DN_95";
	public static final String DONE_95_1_BR = "900";
	public static final String DONE_95_1_CUSTID = "XXXXXXXXXX";
	public static final String DONE_95_1_DUPNO = "X";

	// ============================================================================
	// J-109-0372, 在6月底的有效件數(N筆明細)
	// 覆審 8-1 的明細，可用 ELF489_RULE_NO LIKE '%8-1%' 去抓出
	// 仿 8-1 的做法，把 95-1 的明細 留起來
	public static final String LIST_BASE_95_1_RULE_NO = "LS_95";

	public static final String R96 = "96";// J-107-0354 與 J-108-0076 防杜代辦覆審
	public static final String R97 = "97";// 價金履約保證
	public static final String R98 = "98";// 異常通報
	public static final String R99 = "99";
	public static final String R_COMBO_R2R4 = "2+4";

	public static final String[] RULE_ARR_R1_R2 = new String[] { CrsUtil.R1,
			CrsUtil.R2 };
	public static final String[] RULE_ARR_R1_R2_R4 = new String[] { CrsUtil.R1,
			CrsUtil.R2, CrsUtil.R4 };
	public static final String[] RULE_ARR_R12_R13_R8_1 = new String[] {
			CrsUtil.R12, CrsUtil.R13, CrsUtil.R8_1, CrsUtil.R14 };

	public static final String DOCKIND_S_R1 = "R1";
	public static final String DOCKIND_S_R2 = "R2";
	public static final String DOCKIND_S_R3 = "R3";
	public static final String DOCKIND_S_R4 = "R4";
	public static final String DOCKIND_S_R5 = "R5";
	public static final String DOCKIND_S_R6 = "R6";

	public static final String OLD_RULE = "O";
	public static final String NEW_RULE = "N";

	public static final String ELF491_NEWFLAG_Y = "Y";
	public static final String ELF491_NEWFLAG_N = "";

	public static final BigDecimal R1R2_IN_TPE_AMT_1000WAN = new BigDecimal(
			"10000000");// 一千萬
	public static final BigDecimal R1R2_NO_TPE_AMT_500WAN = new BigDecimal(
			"5000000");// 五百萬
	// J-109-0213 新的覆審規則(2020_05_22)之後，門檻由 1000/500 上調至 3000/1500
	public static final BigDecimal R1R2_IN_TPE_AMT_3000WAN = new BigDecimal(
			"30000000");// 三千萬
	public static final BigDecimal R1R2_NO_TPE_AMT_1500WAN = new BigDecimal(
			"15000000");// 一千五百萬

	public static final BigDecimal R9_AMT = new BigDecimal("50000000");// 五千萬
	public static final BigDecimal R8_1_8_2_SEP_AMT_300WAN = new BigDecimal(
			"3000000");// 三百萬
	public static final BigDecimal R8_1_8_2_SEP_AMT_500WAN = new BigDecimal(
			"5000000");// 五百萬

	public static final String DOCKIND_P = "P"; // 覆審_價金履保
	public static final String DOCKIND_N = "N"; // 覆審_一般
	public static final String DOCKIND_G = "G"; // 覆審_團貸母戶
	public static final String DOCKIND_H = "H"; // 覆審_小額/團體消費性貸款 =>
												// 應用場景(1)團貸裡的明細 (2)專案信貸 的抽樣
	public static final String DOCKIND_S = "S"; // 覆審_防杜代辦

	public static final String DOCFMT_一般 = "A";
	public static final String DOCFMT_土建融實地覆審 = "B";

	public static final String C241M01A_RETRIALYN_Y = "Y";
	public static final String C241M01A_RETRIALYN_N = "N";

	public static final String C241M01A_NCREATEDATA_SYS = "SYS";
	public static final String C241M01A_NCREATEDATA_USR = "PEO";

	public static final String C241M01B_GUARANTEEKIND_01_DESC = "不動產";

	public static final String NCKDFLAG_A = "A";// 改期覆審
	public static final String NCKDFLAG_O = "O";// 本案已存在於XXX/XX/XX覆審工作底稿中。

	public static final String ATTCH_C240M01A_0 = "listExcel";
	public static final String ATTCH_C240M01A_1 = "listPre";
	public static final String ATTCH_C240M01A_2 = "listChk";
	public static final String ATTCH_C240M01A_3A = "listEjcicA";// 個人戶
	public static final String ATTCH_C240M01A_3B = "listEjcicB";// 公司戶
	public static final String ATTCH_C241M01A_GRPDTL = "listGrpDetail";// 團貸明細

	public static final String V_N_20111201 = "Ver20111201";
	public static final String V_N_201412 = "Ver201412";
	public static final String V_N_201707A = "Ver201707A";
	public static final String V_N_201707B = "Ver201707B";
	public static final String V_N_201805A = "Ver201805A";
	public static final String V_N_201805B = "Ver201805B";
	public static final String V_N_201805A2 = "Ver201805A2";
	public static final String V_N_201805B2 = "Ver201805B2"; // C241M01A.docFmt
																// 覆審報告表格式{A一般/B實地覆審}第10條「實地覆審」的描述更多,第13條「實地覆審」多出NY20及[NY2A,NY2B,NY2C,NY2D]
	// C241M01A.docKind.N:一般
	// C241M01A.docKind.N_{yyyyMM}NA:一般(docFmt.A)
	// C241M01A.docKind.N_{yyyyMM}NB:一般(docFmt.B:土建融實地覆審)
	// C241M01A.docKind.N_{yyyyMM}GA:團貸子戶(docFmt.A)
	public static final String V_N_201907NA = "Ver201907NA";
	public static final String V_N_201907NB = "Ver201907NB";
	public static final String V_N_201907GA = "Ver201907GA"; // 團貸子戶
	public static final String V_N_201907GB = "Ver201907GB"; // 團貸子戶
	public static final String V_N_201909NA = "Ver201909NA"; // J-108-0226 修改Web
																// e-Loan系統企金戶授信覆審報告表「債權確保」第23項及消金戶授信覆審報告表「債權確保」第19項
																// (tfs 137094)
	public static final String V_N_201909NB = "Ver201909NB"; // J-108-0226
																// 擔保品估價是否按照規定？
																// 改為
																// 擔保品估價是否按照規定？是否有做合理性評估？
	public static final String V_N_202008NA = "Ver202008NA"; // J-109-0213
	public static final String V_N_202008NB = "Ver202008NB";
	// ======
	// 參考 LMS2401ServiceImpl :: decide_docKindN_or_H___for_grpDetail(String
	// brNo, String custId, String dupNo, Date elf491_crdate
	// 當團貸子戶，視情況決定是 docKind=H(小額/團體消費性貸款) 或 docKind=N(客戶有2個以上的額度，其中之一，屬於
	// "非"小額/團體消費性貸款 的需覆審額度)
	// select mainid, custid, custname, ownbrid, projectno, docKind, grpcntrno,
	// rptid from lms.c241m01a where createtime>='2022-08-01' and deletedtime is
	// null and grpcntrno> ' ' and docKind not in ('G','H')
	public static final String V_N_202008GA = "Ver202008GA"; // ===> GA
																// 已被｛docKind=H
																// 或 docKind=N
																// (rptId是NA/NB)
																// ｝整併/替代
	public static final String V_N_202008GB = "Ver202008GB";
	public static final String V_N_202105NA = "Ver202105NA"; // J-110-0070
	public static final String V_N_202105NB = "Ver202105NB";
	public static final String V_N_202204NA = "Ver202204NA"; // J-111-0031
	public static final String V_N_202204NB = "Ver202204NB";
	public static final String V_N_202209NA = "Ver202209NA"; // J-111-0405
	public static final String V_N_202209NB = "Ver202209NB";
	// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	// C241M01A.docKind.G:團貸母戶
	public static final String V_G_20111201 = "Ver20111201";
	public static final String V_G_201805 = "Ver201805";
	public static final String V_G_202008 = "Ver202008G"; // J-109-0213
															// 小額/團體消費性貸款(新案)授信案件覆審報告表。現在的
															// docKind=G
															// 與｛子戶若為docKind=H｝的項目是一致
															// =>
															// 以前的子戶docKind=N，簡化後｛單純=H，其它=N｝
	// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	// C241M01A.docKind.H:小額/團體消費性貸款
	public static final String V_H_202008 = "Ver202008H";
	// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	// C241M01A.docKind.P:價金履保
	public static final String V_P_20140101 = "Ver20140101";
	public static final String V_P_201809 = "Ver201809";
	public static final String V_P_202008 = "Ver202008P"; // J-109-0213
	// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	// C241M01A.docKind.S:防杜代辦覆審
	public static final String V_S_201902 = "Ver201902";
	public static final String V_S_202204 = "Ver202204";
	// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	public static final String V_201809 = "Ver201809"; // 009301海外改格式
	public static final String V_202209 = "Ver202209"; // J-111-0405,009763海外改格式

	public static final String R11_ON_DATE = "2017-07-01";
	public static final String VER202008_ON_DATE = "2020-08-01";
	public static final String VER202105_ON_DATE = "2021-05-01";
	public static final String VER202204_ON_DATE = "2022-04-01";
	public static final String VER202209_ON_DATE = "2022-08-26"; // J-111-0405
																	// 國內消金覆審格式改版

	public static final String PAVER_00010101 = "";	// 個金考評表初版
	public static final String PAVER_20230701 = "Ver20230701";	// 改版

	public static final String A_徵信事項 = "A";
	public static final String B_債權確保 = "B";
	public static final String C_其他 = "C";
	public static final String D_開辦徵信事項 = "D";
	public static final String E_專戶款項撥付作業 = "E";
	public static final String F_糾紛案件處理 = "F";
	public static final String Y_項目附屬選項 = "Y";
	public static final String Z_電腦建檔資料 = "Z";

	public static final String P001 = "P001";
	public static final String P002 = "P002";
	public static final String P003 = "P003";
	public static final String P004 = "P004";
	public static final String P005 = "P005";
	public static final String P006 = "P006";
	public static final String P007 = "P007";
	public static final String P008 = "P008";
	public static final String P009 = "P009";
	public static final String P010 = "P010";
	public static final String P011 = "P011";
	public static final String P012 = "P012";
	public static final String P013 = "P013";
	public static final String P014 = "P014";

	public static final String N001 = "N001";
	public static final String N002 = "N002";
	public static final String N003 = "N003";
	public static final String N004 = "N004";
	public static final String N005 = "N005";
	public static final String N006 = "N006";
	public static final String N007 = "N007";
	public static final String N008 = "N008";
	public static final String N009 = "N009";
	public static final String N010 = "N010";
	public static final String N011 = "N011";
	public static final String N012 = "N012";
	public static final String N013 = "N013";
	public static final String N014 = "N014";
	public static final String N015 = "N015";
	public static final String N016 = "N016";
	public static final String N017 = "N017";
	public static final String N018 = "N018";
	public static final String N019 = "N019";
	public static final String N020 = "N020";
	public static final String N021 = "N021";
	public static final String N022 = "N022";
	public static final String N023 = "N023";
	public static final String N024 = "N024";
	public static final String N025 = "N025";
	public static final String N026 = "N026";
	public static final String N027 = "N027";

	/*
	 * J-108-0128 , (108) 第 1278 號 (1)於「其他」項目下增訂第28項：「本案授信經辦與擔保品估價是否非同一人？」
	 * (2)「團體消費性貸款（新案）覆審報告表」及「對合作房仲業價金履約保證額度覆審報告表」，則均未做修正 ===> 另於 J-109-0213 ,
	 * (109) 第 1603 號 , 將原適用之覆審報告表名稱修改為「小額/團體消費性貸款（新案）授信案件覆審報告表」
	 */
	public static final String N028 = "N028";

	public static final String G001 = "G001";
	public static final String G002 = "G002";
	public static final String G003 = "G003";
	public static final String G004 = "G004";
	public static final String G005 = "G005";
	public static final String G006 = "G006";
	public static final String G007 = "G007";
	public static final String G008 = "G008";
	public static final String G009 = "G009";
	public static final String G010 = "G010";
	public static final String G011 = "G011";
	public static final String G012 = "G012";

	public static final String H001 = "H001";
	public static final String H002 = "H002";
	public static final String H003 = "H003";
	public static final String H004 = "H004";
	public static final String H005 = "H005";
	public static final String H006 = "H006";
	public static final String H007 = "H007";
	public static final String H008 = "H008";
	public static final String H009 = "H009";
	public static final String H010 = "H010";
	public static final String H011 = "H011";
	public static final String H012 = "H012";

	public static final String Y_DESC_NY20_SINCE_R11 = "土建融案件實地覆審結果，應勾選下列事項是否依核定條件辦理，若有不符者應說明情形。";
	public static final String Z_DESC_N007 = "註明調閱傳票、相關資料及影印資金流向詳情附於本報告表備查";
	public static final String Z_DESC_N007_SINCE_R11 = "應敘明撥貸入借戶指定存款帳戶後，該存款帳戶所提領(含轉出或匯出等)該筆撥貸金額之資金流向是否與申貸用途相符之詳情，並影印資金流向資料存覆審卷備查。";
	public static final String Z_DESC_N007_SINCE_R11_202204 = "應敘明撥貸入借戶指定存款帳戶後，該存款帳戶所提領(含轉出或匯出等)該筆撥貸金額之資金流向是否與申貸用途相符之詳情，並影印資金流向資料存覆審卷備查或確認E-LOAN授信管理系統貸後管理追蹤檢核表已存載。";
	public static final String Z_DESC_N007_2ND_SINCE_R11 = "如資金用途屬「週轉金」或「投資理財」之授信案，且用途係用於購買保險商品時，應由法令遵循主管或授信主管或非理財部門主管辦理電話查訪機制，與客戶確認無不當勸誘之情事，並取得「聲明書」。(國內營業單位適用)";
	public static final String Z_DESC_N013_Y = "已改善";
	public static final String Z_DESC_N013_N = "未改善";
	public static final String Z_DESC_N020 = "除須查核各項電腦檔案建檔作業是否正確外，應特別註明下列項目之建檔資料是否正確：";
	public static final String Z_DESC_ZB1A_Y = "擔保品，若勾「有」則應檢視下列資料建檔是否正確：";
	public static final String Z_DESC_ZB1A_N = "擔保品";
	public static final String Z_DESC_ZB2A_Y = "額度之融資業務分類A-LOAN註記為「#」或海外AS-400註記為「A0#」，若勾「有」則應檢視下列事項)";
	public static final String Z_DESC_ZB2A_N = "額度之融資業務分類A-LOAN註記為「#」或海外AS-400註記為「A0#」)";
	public static final String Z_DESC_ZB21_POSTFIX_1 = "維護正確*";
	public static final String Z_DESC_ZB21_POSTFIX_2 = "*因購置、興建廠房而排除列入72-2限額控管之案件，應於貸後管理檢核系統定期追蹤至廠房完工後，於e-loan「授信管理系統-建檔維護-額度相關資訊註記維護」完成建檔(國內廠房須含工廠登記/許可編號)為止。";
	public static final String Z_NOTE_ZA20 = "注意!!銀行法第72條之2管控項目:用途別、融資業務分類是否正確。";

	public static final String N016_J_110_0070 = "擔保品估價(含查詢不動產異動索引資料)及撥貸後回查實價登錄等作業是否符合本行規定？是否有做合理性評估？";
	public static final String N026_J_110_0070 = "若借戶、擔保品所有權人與不動產買賣契約書之買方不同，或不同借戶之保證人、擔保品提供人或聯絡資料相同等異常情事，是否詳予審核瞭解原因？如屬購置房屋貸款，對房屋買賣契約書是否依本行相關規定進行查驗？";
	public static final String S010 = "S010";
	public static final String S021 = "S021";
	public static final String S022 = "S022";
	public static final String S040 = "S040";
	public static final String S031 = "S031";
	public static final String S032 = "S032";
	public static final String S051 = "S051";
	public static final String S052 = "S052";
	public static final String S061 = "S061";
	public static final String S062 = "S062";
	public static final String S070 = "S070";
	public static final String S081 = "S081";
	public static final String S082 = "S082";
	public static final String S083 = "S083";
	public static final String S090 = "S090";
	public static final String S101 = "S101";
	public static final String S102 = "S102";
	public static final String S103 = "S103";
	public static final String S110 = "S110";
	public static final String S120 = "S120";
	public static final String S130 = "S130";
	public static final String S140 = "S140";
	// =========================================
	public static final String Y_DESC_NY10_Y = "約定事項，若有應註明下列事項：";
	public static final String Y_DESC_NY10_N = "約定事項";

	// 以下的項目，是 N012 借戶是否依照約定條件履行？借戶若有違反承諾或約定事項是否依核定條件處置？
	// 的下一層
	public static final String Y_NY10 = "NY10";
	public static final String Y_NY1A = "NY1A";
	public static final String Y_NY1B = "NY1B";
	// ~~~
	// 以下的項目，是 N010 中長期放款（含土建融案件）之申貸計劃與其自籌款及個人投資不動產融資達新台...
	// 的下一層
	public static final String Y_NY20 = "NY20";
	public static final String Y_NY2A = "NY2A";
	public static final String Y_NY2B = "NY2B";
	public static final String Y_NY2C = "NY2C";
	public static final String Y_NY2D = "NY2D";
	// ~~~
	// 以下的項目，是 N026 若借戶、擔保品所有權人與不動產買賣契約書之買方不同，或不同借戶之保證人、擔保品提供人或聯絡資料.....
	// 的下一層
	public static final String Y_NY3A = "NY3A";
	public static final String Y_NY3B = "NY3B";
	// =========================================
	public static final String ZA00 = "ZA00";
	public static final String ZA10 = "ZA10";
	public static final String ZA11 = "ZA11";
	public static final String ZA12 = "ZA12";

	public static final String ZA20 = "ZA20";
	public static final String ZA21 = "ZA21";
	public static final String ZA22 = "ZA22";
	public static final String ZA23 = "ZA23";
	public static final String ZA24 = "ZA24";
	public static final String ZA25 = "ZA25";

	public static final String ZB00 = "ZB00"; // E-LOAN擔保品管理系統：
	public static final String ZB10 = "ZB10"; // "授信擔保品不動產估價報告書
	public static final String ZB1A = "ZB1A";
	public static final String ZB11 = "ZB11";
	public static final String ZB12 = "ZB12";
	public static final String ZB13 = "ZB13";

	public static final String ZB20 = "ZB20"; // J-111-0405
												// 其他事項-授信管理系統-「建檔維護-額度相關資訊註記維護」之建檔
	public static final String ZB2A = "ZB2A";
	public static final String ZB21 = "ZB21";

	private static final String[] ZSYS_TITLE = { ZA00, ZA10, ZA20, ZB00, ZB10,
			ZB20 };
	private static final String[] YZ_SYS_ITEM = { ZA11, ZA12, ZA21, ZA22, ZA23,
			ZA24, ZA25, ZB1A, ZB11, ZB12, ZB13, ZB2A, ZB21, Y_NY10, Y_NY1A,
			Y_NY1B, Y_NY20, Y_NY2A, Y_NY2B, Y_NY2C, Y_NY2D, Y_NY3A, Y_NY3B };

	public static final String RPT_REMARK_BRTYPE = "brType";
	public static final String RPT_REMARK_BR = "br";
	public static final String RPT_REMARK_DT = "dt";
	public static final String RPT_REMARK_MCNT = "mCnt";

	/*
	 * 未來可能稱 '歡喜樂活貸' CLS1151S01Page_zh_TW.properties {page5.226, page5.227,
	 * page5.229, L140S02A.rmRctAmt} CLS1141R01RptServiceImpl_zh_TW.properties
	 * {CLS1151R03.rmRctAmt} CLS1161S02APage_zh_TW.properties {label.rmRctAmt}
	 */
	public static final String PROD_67_DESC = "以房養老";
	public static final String PROD_70_DESC = "以房養老-累積型";

	public static final String RATE_TYPE_01 = "01";
	public static final String RATE_TYPE_6R = "6R";
	public static final String RATE_TYPE_7C = "7C";
	public static final String RATE_TYPE_7D = "7D";
	public static final String RATE_TYPE_M2 = "M2";
	public static final String RATE_TYPE_M3 = "M3";
	public static final String RATE_TYPE_MD = "MD";
	public static final String RATE_TYPE_M1 = "M1";
	public static final String RATE_TYPE_MI = "MI"; // J-111-0204內政部貸款的政府補貼利率異動,
													// MI=１１１０３２３新國宅利率(原MK)
													// (MI=Z5+MJ , 參考MK=P7+MJ)
	public static final String RATE_TYPE_MK = "MK";
	public static final String RATE_TYPE_MR = "MR";//行員利率
	public static final String RATE_TYPE_N2 = "N2";
	public static final String RATE_TYPE_P7 = "P7";
	public static final String RATE_TYPE_Z5 = "Z5"; // J-111-0204內政部貸款的政府補貼利率異動,
													// Z5=１１１０３２３政府優惠房貸利率 (原P7)
	public static final String RATE_TYPE_Z6 = "Z6"; // J-111-0155 青安減碼利率Z6
	public static final String RATE_TYPE_Z7 = "Z7"; // J-111-0126
													// 勞工紓困A-LOAN的利率由P7改Z7

	// J-108-0277 一鍵查詢
	public static final String EJ_TXID_PREFIX = "聯徵";

	// ================================================================================
	// 已在 J-110-0088 , (110)0557 , 改以API連結內政部查身分證 => 不再查Z21
	public static final String EJ_TXID_Z21 = "Z21";
	public static final String EJ_TXID_Z21_DESC = "身分證領補換資料查詢驗證";

	// ================================================================================
	// 已在 J-110-0088 , (110)0557 , 受監護/輔助宣告 改以RPA查詢
	// 司法院網站（即時）LMS.C101S04W(C120S04W)的{status:A01-查詢中, A02-查詢完成,
	// A03-查詢失敗}{memo:有案件/查無資料}=> 不再查Z13（聯徵資料有1~3天時間遞延情形）
	// 另在 J-110-0133 , (110)0782 , Z13 補充註記╱消債條例信用註記資訊 已包含在
	// P7裡的【VAM106】消債條例信用註記資訊（含個別協商）【VAM107】銀行公會消金案件債務協商補充註記【VAM108】其他補充註記資訊
	public static final String EJ_TXID_Z13 = "Z13";
	public static final String EJ_TXID_Z13_DESC = "補充註記";

	public static final String EJ_TXID_B36 = "B36";
	public static final String EJ_TXID_B36_DESC = "新版授信(含票信)、擔保品、還款紀錄與保證資訊-行庫別";
	public static final String EJ_TXID_D10 = "D10";
	public static final String EJ_TXID_D10_DESC = " 50萬元以下退票紀錄資訊";
	public static final String EJ_TXID_R20 = "R20";
	public static final String EJ_TXID_R20_DESC = "勞工保險投保/勞工退休金提繳資訊";

	public static final String EJ_TXID_B68 = "B68";
	public static final String EJ_TXID_J10 = "J10";

	public static final String EJ_TXID_S11 = "S11";
	public static final String EJ_TXID_S11_DESC = "最近一年內查詢紀錄明細資訊";
	
	public static final String EJ_TXID_B95 = "B95"; //農安貸款審核資訊
	public static final String EJ_TXID_B98 = "B98"; //青安貸款審核資訊

	public static final String EJ_TXID_Q116 = "Q116";
	public static final String EJ_TXID_Q128 = "Q128"; // KRM040系列相關
	public static final String EJ_TXID_Q135 = "Q135"; // BAM095系列相關

	public static final String EJ_TXID_HZ13 = "HZ13";
	public static final String EJ_TXID_HZ21 = "HZ21";
	public static final String EJ_TXID_HD10 = "HD10";
	public static final String EJ_TXID_HR20 = "HR20";
	public static final String EJ_TXID_B29 = "B29";
	public static final String EJ_TXID_B33 = "B33";
	public static final String EJ_TXID_T70 = "T70";
	public static final String EJ_TXID_T70_DESC = "聯徵T70證券暨期貨違約交割記錄資訊";
	public static final String RPA_TXID_FA = "FA_QUERY";
	public static final String RPA_TXID_FA_DESC = "司法院受監護/輔助宣告資料";
	public static final String API_TXID_ID_CARD_CHECK = "ID_CARD_CHECK";
	public static final String API_TXID_ID_CARD_CHECK_DESC = "內政部國民身分證領換補資料";
	public static final String WiseNews = "WiseNews_QUERY";
	public static final String WiseNews_DESC = "負面新聞資料庫";
	public static final String Wealth = "Wealth";//財富管理查詢
	public static final String Wealth_DESC = "財富管理往來";

	/*
	 * EloandbBASEServiceImpl 需自行維護
	 */
	public static final String FN_IMWM0017_D = "IMWM0017.D";
	public static final String FN_IDWM0002_D = "IDWM0002.D";
	public static final String FN_KEY_C900S02E = "C900S02E";
	public static final String FN_KEY_C900S03C = "DWLNCUSTRE"; // Max=10碼, 有截掉
	public static final String FN_KEY_C900S03D = "DWLNCUSTBR";
	public static final String FN_KEY_C900S03E = "MISELF488";

	/*
	 * select * from DWADM.DW_LNCUSTREL where CUST_KEY like '###%'
	 */
	public static final String FN_KEY_C900S03C_DONE_CUST_KEY = "###########";

	public static final String C900S02F_REL_FLAG_SHOW = "◎";

	public static boolean isDwCustRelFirstTotalRun(String cyc_mn) {
		/*
		 * 因涉及產出 CLS180R22 消金借款人留存同一通訊處xx比對清單 若在上線後第2個月以後, 要再追溯產出第1個月的資料 需改成
		 * isFirstTotalRun = Util.equals("2019-xx-01", cyc_mn);
		 * 例如：DW在2019-02-25的批次執行, 會寫入 cyc_mn=2019-01-01
		 */
		// boolean isFirstTotalRun = true;
		// 調整 isFirstTotalRun 判斷
		boolean isFirstTotalRun = cyc_mn.startsWith("2019-01");
		return isFirstTotalRun;
	}

	public static String build_C900S02F_info(C900S02F c900s02f) {
		if (c900s02f == null) {
			return "";
		}
		return (Util.equals("N", c900s02f.getRel_flag()) ? CrsUtil.C900S02F_REL_FLAG_SHOW
				: "")
				+ Util.trim(c900s02f.getRel_custId())
				+ "-"
				+ c900s02f.getRel_dupNo()
				+ " "
				+ Util.trim(c900s02f.getRel_cname());
	}

	public static String get_custId_from_custKey(String cust_key) {
		return StringUtils.substring(cust_key, 0, 10);
	}

	public static String get_dupNo_from_custKey(String cust_key) {
		String dupNo = StringUtils.substring(cust_key, 10, 11);
		if (Util.equals("", dupNo)) {
			dupNo = "0";
		}
		return dupNo;
	}

	/**
	 * LMS.LMSBATCH 及 LMS.LMSRPT(歷史檔) 的 remark 格式為 key1=val1;key2=val2
	 */
	public static Map<String, String> parseRptRemark(String remark) {
		Map<String, String> r = new HashMap<String, String>();
		for (String entryStr : StringUtils.split(remark, ";")) {
			String[] entry = entryStr.split("=");
			if (entry.length == 2) {
				r.put(Util.trim(entry[0]), Util.trim(entry[1]));
			}
		}
		return r;
	}

	public static String cls180R14Name(String rptNo, String unitNo,
			String remark) {
		String desc = "";
		Map<String, String> remarkMap = CrsUtil.parseRptRemark(remark);
		String brType = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BRTYPE));
		String br = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BR));
		String dt = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_DT));
		String mCnt = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_MCNT));

		String prefixBr = Util.equals("S", brType) ? br : unitNo;
		if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月, rptNo)) {
			desc = prefixBr + "-至上個月止未於規定期限辦理覆審之名單";
		} else if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
				rptNo)) {
			desc = prefixBr + "-未於規定期限(" + dt + ")辦理覆審之名單";
		} else if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
				rptNo)) {
			desc = prefixBr + "-自本月起" + mCnt + "個月內即將到覆審期限未覆審之名單";
		} else if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
				rptNo)) {
			desc = prefixBr + "-逾覆審期限才覆審之名單(含未覆審)";
		}
		return desc;
	}

	public static String CLS250R01Name(String rptNo, String unitNo,
			String remark) {
		String desc = "";
		Map<String, String> remarkMap = CrsUtil.parseRptRemark(remark);
		String brType = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BRTYPE));
		String br = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BR));

		String prefixBr = Util.equals("S", brType) ? br : unitNo;
		if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表, rptNo)) {
			desc = prefixBr + "-疑似代辦案件訊息未註記明細表";
		} else if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
				rptNo)) {
			desc = prefixBr + "-疑似代辦案件訊息已註記明細表";
		} else if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,
				rptNo)) {
			desc = prefixBr + "-疑似代辦案件訊息全部明細表";
		}
		return desc;
	}

	public static String CLS180R21Name(String rptNo, String unitNo,
			String remark) {
		String desc = "";
		Map<String, String> remarkMap = CrsUtil.parseRptRemark(remark);
		String brType = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BRTYPE));
		String br = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BR));

		String prefixBr = Util.equals("S", brType) ? br : unitNo;
		if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
				rptNo)) {
			desc = prefixBr + "消金借款人留存同一通訊處未註記清單";
		} else if (Util.equals(
				UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單, rptNo)) {
			desc = prefixBr + "消金借款人留存同一通訊處已註記清單";
		} else if (Util.equals(
				UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單, rptNo)) {
			desc = prefixBr + "消金借款人留存同一通訊處全部清單";
		}
		return desc;
	}

	public static String cls180R15Name(String rptNo, String unitNo,
			String remark) {
		Map<String, String> remarkMap = CrsUtil.parseRptRemark(remark);
		String brType = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BRTYPE));
		String br = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BR));

		String prefixBr = Util.equals("S", brType) ? br : unitNo;
		return prefixBr + "-覆審類別8-1之進度表";
	}

	public static String cls180R15BName(String rptNo, String unitNo,
			String remark) {
		Map<String, String> remarkMap = CrsUtil.parseRptRemark(remark);
		String brType = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BRTYPE));
		String br = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BR));

		String prefixBr = Util.equals("S", brType) ? br : unitNo;
		return prefixBr + "-覆審類別「不動產十足擔保」抽樣之進度表";
	}

	public static String cls180R15CName(String rptNo, String unitNo,
			String remark) {
		Map<String, String> remarkMap = CrsUtil.parseRptRemark(remark);
		String brType = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BRTYPE));
		String br = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BR));

		String prefixBr = Util.equals("S", brType) ? br : unitNo;
		return prefixBr + "-覆審類別「專案信貸」抽樣之進度表";
	}

	public static String cls180R15DName(String rptNo, String unitNo,
			String remark) {
		Map<String, String> remarkMap = CrsUtil.parseRptRemark(remark);
		String brType = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BRTYPE));
		String br = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BR));

		String prefixBr = Util.equals("S", brType) ? br : unitNo;
		return prefixBr + "-覆審類別「疑似人頭戶追蹤對象逐年專案查核」抽樣之進度表";
	}

	public static String cls180R15EName(String rptNo, String unitNo,
			String remark) {
		Map<String, String> remarkMap = CrsUtil.parseRptRemark(remark);
		String brType = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BRTYPE));
		String br = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BR));

		String prefixBr = Util.equals("S", brType) ? br : unitNo;
		return prefixBr + "-覆審類別「額度一千萬元以下十足擔保信保七成循環動用」抽樣之進度表";
	}

	/**
	 * J-112-0465 新增「○○區營運中心覆審類別「額度一千萬元以下十足擔保信保七成循環動用」抽樣之授信戶明細表
	 * 
	 * @param rptNo
	 * @param unitNo
	 * @param remark
	 * @return
	 */
	public static String cls180R15FName(String rptNo, String unitNo,
			String remark) {
		Map<String, String> remarkMap = CrsUtil.parseRptRemark(remark);
		String brType = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BRTYPE));
		String br = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_BR));

		String prefixBr = Util.equals("S", brType) ? br : unitNo;
		return prefixBr + "-覆審類別「額度一千萬元以下十足擔保信保七成循環動用」抽樣之明細表";
	}

	public static String cls180R16Name(String rptName, Date startDate,
			Date endDate) {
		return rptName
				+ "("
				+ StringUtils.substring(Util.trim(TWNDate.toAD(startDate)), 0,
						7) + "~"
				+ StringUtils.substring(Util.trim(TWNDate.toAD(endDate)), 0, 7)
				+ ")";
	}

	public static String cls180R18Name(String rptName, Date endDate) {
		return rptName
				+ "(覆審基準期間:"
				+ StringUtils.join(
						CrsUtil.get_CLS180R18_period_byEndYM(
								TWNDate.toAD(endDate), 7), "~") + ")";
	}

	/**
	 * 回傳當月最後一天 ,傳入2013-04,取得2013-04-30
	 */
	public static String getDataEndDate(String yyyy_MM) {
		return Util.toAD(CapDate.shiftDays(
				CapDate.addMonth(CapDate.parseDate(yyyy_MM + "-01"), 1), -1));
	}

	/**
	 * 拆分 Rule ● 遇過 ELF491_REMOMO 為 6-1(08262168 -099-02) ● 遇過 ELF491_REMOMO 為
	 * 98(06)
	 * 
	 * 【key】val 【6-1】08262168 -099-02 【98】06
	 * 
	 * @param rule
	 * @return
	 */
	public static TreeMap<String, String> parseRule(String rule) {
		TreeMap<String, String> r = new TreeMap<String, String>();
		String[] arr = StringUtils.split(Util.trim(rule), RULE_DELIM);
		for (int i = 0; i < arr.length; i++) {
			String part = arr[i];
			String additional = "";
			if (part.indexOf("(") >= 0) {
				additional = Util.trim(part.substring(part.indexOf("(")));
				if (additional.startsWith("(")) {
					additional = additional.substring(1);// 去掉(
				}
				if (additional.endsWith(")")) {
					additional = additional.substring(0,
							additional.length() - 1);// 去掉)
				}
				part = part.substring(0, part.indexOf("("));
			}
			r.put(Util.trim(part), additional);
		}
		return r;
	}

	public static String combineRule(TreeMap<String, String> m) {
		ArrayList<String> list = new ArrayList<String>();
		for (String rule : m.keySet()) {
			String additional = Util.trim(m.get(rule));
			String s = "";
			if (Util.isNotEmpty(additional)) {
				s = "(" + additional + ")";
			}
			list.add(rule + s);
		}
		return StringUtils.join(list, RULE_DELIM);
	}

	/**
	 * 1~3：分行別、 4~4：區部代號、 5~5：期擔別 、 6~7：科目簡碼、 8~13：序號、 14~14：檢查碼 取得 3 碼的授信科目 EX:
	 * 0654112 0000311
	 */
	public static String getSubjCodeFromLNF030_LOAN_NO(String s) {
		return StringUtils.substring(s, 4, 7);
	}

	/**
	 * 由 lnf030 的帳號 取得 brNo
	 * 
	 * @param s
	 * @return
	 */
	public static String getBrNoFromLNF030_LOAN_NO(String s) {
		return StringUtils.substring(s, 0, 3);
	}

	/**
	 * 由 lnf020 的額度序號 取得 brNo
	 * 
	 * @param s
	 * @return
	 */
	public static String getBrNoFromLNF020_CONTRACT(String s) {
		return StringUtils.substring(s, 0, 3);
	}

	public static boolean isSkip_LNF020(String c240m01a_branchId,
			String cntrNo, String lnf020_ln_br_no, String lnf020_fact_type,
			BigDecimal factAmt) {
		/*
		 * 若 LNF020_CONTRACT LNF020_LN_BR_NO LNF020_FACT_TYPE ---------------
		 * --------------- ---------------- 046110100289 <USER> <GROUP> 046110100289 046
		 * 30 046110100289 046 31
		 */

		// 排除: 它行cntrNo,在上例,021分行無「需覆審的cntrNo」
		if (Util.notEquals(c240m01a_branchId,
				getBrNoFromLNF020_CONTRACT(cntrNo))) {
			return true;
		}
		return isSkip_LNF020_2(c240m01a_branchId, cntrNo, lnf020_ln_br_no,
				lnf020_fact_type, factAmt);
	}

	public static boolean isSkip_LNF020_2(String c240m01a_branchId,
			String cntrNo, String lnf020_ln_br_no, String lnf020_fact_type,
			BigDecimal factAmt) {
		// 排除: 它行的子戶,在上例,046分行排除[LNF020_LN_BR_NO==021]的記錄
		if (Util.notEquals(c240m01a_branchId, lnf020_ln_br_no)) {
			return true;
		}
		// 排除: 有些分行, 沒有把性質設取消,而是把額度設0
		if (BigDecimal.ZERO.compareTo(factAmt) == 0) {
			return true;
		}
		// 排除: 聯貸母戶
		if (Util.equals(UtilConstants.Cntrdoc.snoKind.聯貸, lnf020_fact_type)) {
			return true;
		}
		return false;
	}

	public static Date get_month_last_date(Date inputDt) {
		if (CrsUtil.isNull_or_ZeroDate(inputDt)) {
			return CapDate.parseDate(CapDate.ZERO_DATE);
		}

		return CapDate.parseDate(getDataEndDate(StringUtils.substring(
				TWNDate.toAD(inputDt), 0, 7)));
	}

	/**
	 * 每月1日,更新ELF490
	 * 
	 * @return
	 */
	public static Date get_sysMonth_1st() {
		Date dataDate = CapDate.parseDate(CapDate
				.getCurrentDate(UtilConstants.DateFormat.YYYY_MM) + "-01");
		return dataDate;
	}

	public static boolean inCollection(String s, String[] arr) {
		for (String item : arr) {
			if (Util.equals(item, s)) {
				return true;
			}
		}
		return false;
	}

	// XXX 用 subjectNo 來區分
	public static Set<String> match3digitSubject_R1R2() {
		Set<String> r = new HashSet<String>();
		r.add("403");// 一般中期擔保放款
		r.add("421");// 一般中期消費者擔保放款
		r.add("471");// 中期建築融資擔保放款
		r.add("473");// 中期房屋購置擔保放款
		r.add("474");// 中期房屋修繕擔保放款
		r.add("603");// 一般長期擔保放款
		r.add("671");// 長期建築融資擔保放款
		r.add("673");// 長期房屋購置擔保放款
		r.add("674");// 長期房屋修繕擔保放款
		r.add("202");// 擔保透支 or 短期擔保放款
		r.add("203");// 一般短期擔保放款
		r.add("204");// 短期存摺存款擔保透支
		/*
		 * 因 206,207 是股票, 不是不動產 r.add("206");//短期證券擔保放款
		 * r.add("207");//短期集保股票擔保放款
		 */
		r.add("404");// 中期擔保放款－存摺存款透支
		return r;
	}

	/**
	 * 在 jcs.common.Util.parseBigDecimal 裡 若傳入 123,456.78 因「,」不能正常判斷
	 * 
	 * 要先加工
	 * 
	 * @param s
	 * @return
	 */
	public static BigDecimal parseBigDecimal(Object s) {
		if (s == null) {
			return BigDecimal.ZERO;
		}
		if (s instanceof BigDecimal) {
			return (BigDecimal) s;
		}
		return Util.parseBigDecimal(StringUtils.replace(Util.trim(s), ",", ""));
	}

	public static String extractProjectNo(String s) {
		if (Util.isEmpty(s)) {
			return "";
		}
		int beg_idx = StringUtils.indexOf(s, "-");
		int end_idx = StringUtils.lastIndexOf(s, "-");
		return Util.trim(StringUtils.substring(s, beg_idx - 3, end_idx + 4));
	}

	/**
	 * C241M01A_ProjectNo 覆審序號 格式為：年度(YYYY) +分行簡稱(3碼)+(兆)+覆審字第+ 批號+ - + 序號 + 號
	 * 例：2011蘭雅(兆)覆審字第001-003號 例：2011蘭雅(兆)覆審字第001-003-004號 回傳 003
	 */
	public static String seqPart2nd_FromProjectNo(String s) {
		if (Util.isEmpty(s)) {
			return "";
		}
		String[] arr = StringUtils.split(s, "-");
		if (arr.length >= 2) {
			return StringUtils.left(arr[1], 3);
		} else {
			return s;
		}
	}

	/**
	 * C241M01A_ProjectNo 覆審序號 格式為：年度(YYYY) +分行簡稱(3碼)+(兆)+覆審字第+ 批號+ - + 序號 + 號
	 * 例：2011蘭雅(兆)覆審字第001-003號 回傳 003
	 * 
	 * 
	 * 例：2011蘭雅(兆)覆審字第001-003-004號 回傳 004
	 */
	public static String seqPartLast_FromProjectNo(String s) {
		if (Util.isEmpty(s)) {
			return "";
		}
		String[] arr = StringUtils.split(s, "-");
		if (arr.length >= 2) {
			return StringUtils.left(arr[arr.length - 1], 3);
		} else {
			return s;
		}
	}

	public static boolean isNOT_null_and_NOTZeroDate(Date d) {
		return !isNull_or_ZeroDate(d);
	}

	public static boolean isNull_or_ZeroDate(Date d) {
		if (d == null) {
			return true;
		}
		if (Util.equals(CapDate.ZERO_DATE, TWNDate.toAD(d))) {
			return true;
		}
		return false;
	}

	public static void trans99(Map<String, String> deriveMap,
			Set<String> deriveUnknownSet, String loanNo, String lnType,
			String actcd, String reVolve, BigDecimal quotaAmt) {
		// 海外的 loanNo 可能會是空的
		if (Util.isEmpty(loanNo)) {
			return;
		}
		String kind_R1 = "";
		String kind_R2 = "";
		String kind_R3 = "";
		String kind_R4 = "";
		String kind_R5 = "";
		String kind_R6_1 = "";
		String kind_R6_2 = "";
		String kind_R7 = "";
		String kind_R8_1 = "";
		String kind_R8_2 = "";
		String kind_R9 = "";
		String kind_R10 = "";
		String kind_R11 = "";
		/*
		 * select * from LMS.C900M01B where prodkind='08' 可以看{產品、科目}組合
		 */

		if (CrsUtil.inCollection(lnType, new String[] { "10", "11", "12", "13",
				"14", "15", "16", "17", "18", "19", "20", "21", "22", "23",
				"24", "25", "26", "27", "28", "30", "31", "35", "38", "39",
				"40", "41", "42", "43", "44", "45", "46", "47", "49", "55",
				"56", "57", "59", "63", "64", "65", "66", "67", "72" })) {
			// XXX ref SELECT * FROM LN.LNF07A where LNF07A_KEY_1 like 'CLS%'
			// and lnf07a_content_1 like '%67%'
			if (CrsUtil.inCollection(actcd,
					new String[] { "13506200", "13506300", "14501500",
							"14502000", "13500100", "14501000" })) {
				kind_R1 = "Y";
			}
		} else if (CrsUtil.is_03(lnType)) {
			if (CrsUtil.inCollection(actcd, new String[] { "13500100",
					"14501000" })) {
				kind_R1 = "Y";
			}
		} else if (CrsUtil.is_68(lnType)) {
			if (CrsUtil.inCollection(actcd, new String[] { "13500100",
					"13500200" })) {
				kind_R1 = "Y";
			}
		} else if (CrsUtil.is_02(lnType)) {
			if (CrsUtil.inCollection(actcd, new String[] { "12800000",
					"12600100", "12600200" })) {
				kind_R2 = "Y";
			} else if (CrsUtil.inCollection(actcd, new String[] { "12600100",
					"12600500", "12600400", "12600200", "12800000" })) {
				kind_R4 = "Y";
			} else if (CrsUtil.inCollection(actcd, new String[] { "12400000",
					"12100100", "12100200" })) {
				kind_R5 = "Y";
			}
		} else if (CrsUtil.inCollection(lnType, new String[] { "04" })) {
			if (CrsUtil.inCollection(actcd, new String[] { "12800000",
					"12600100", "12600200" })) {
				kind_R2 = "Y";
			}
		} else if (CrsUtil.inCollection(lnType, new String[] { "05" })) {
			if (CrsUtil.inCollection(actcd, new String[] { "13504000" })) {
				kind_R3 = "Y";
			}
		} else if (CrsUtil.inCollection(lnType, new String[] { "06" })) {
			if (CrsUtil.inCollection(actcd, new String[] { "13502000" })) {
				kind_R1 = "Y";
			}
		} else if (CrsUtil.is_07(lnType)) { // 產品種類07底下
			if (CrsUtil.inCollection(actcd, new String[] { "13502000" })) { // 科目421
				kind_R3 = "Y";
			} else if (CrsUtil.inCollection(actcd, new String[] { "12100200",
					"12100100" })) {
				kind_R5 = "Y";
			}
		} else if (CrsUtil.inCollection(lnType, new String[] { "09" })) {
			if (CrsUtil.inCollection(actcd, new String[] { "13500100",
					"14501000" })) {
				kind_R1 = "Y";
			} else if (CrsUtil.inCollection(actcd, new String[] { "12600100" })) {
				kind_R2 = "Y";
			}
		} else if (CrsUtil.inCollection(lnType, new String[] { "33" })) {
			if (CrsUtil.inCollection(actcd, new String[] { "13500100",
					"14501000" })) {
				kind_R1 = "Y";
			} else if (CrsUtil.inCollection(actcd, new String[] { "12600100" })) {
				kind_R2 = "Y";
			}
		} else if (CrsUtil.inCollection(lnType, new String[] { "34" })) {
			if (CrsUtil.inCollection(actcd, new String[] { "13505000",
					"14502500" })) {
				kind_R1 = "Y";
			} else if (CrsUtil.inCollection(actcd, new String[] { "12600100" })) {
				kind_R2 = "Y";
			}
		} else if (CrsUtil.inCollection(lnType, new String[] { "50", "54" })) {
			if (CrsUtil.inCollection(actcd, new String[] { "12100100" })) {
				kind_R5 = "Y";
			}
		} else if (CrsUtil.inCollection(lnType, new String[] { "51" })) {
			if (CrsUtil.inCollection(actcd, new String[] { "12600100" })) {
				kind_R2 = "Y";
			} else if (CrsUtil.inCollection(actcd, new String[] { "13500100",
					"14501000" })) {
				kind_R1 = "Y";
			}
		} else if (CrsUtil.inCollection(lnType, new String[] { "52" })) {
			if (CrsUtil.inCollection(actcd, new String[] { "13500100",
					"14501000" })) {
				kind_R3 = "Y";
			}
		} else if (CrsUtil.inCollection(lnType, new String[] { "58" })) {
			if (CrsUtil.inCollection(actcd, new String[] { "13500100",
					"14501000" })) {
				kind_R1 = "Y";
			} else if (CrsUtil.inCollection(actcd, new String[] { "13500100",
					"14501000" })) {
				kind_R3 = "Y";
			}
		} else if (CrsUtil.inCollection(lnType, new String[] { "60" })) {
			if (CrsUtil.inCollection(actcd, new String[] { "13500100" })) {
				kind_R3 = "Y";
			}
		}

		// 判斷 無擔保科目（註：371、571是 中期、長期建築融資 的科目）
		if (CrsUtil.is_not_guarantee_loan_exclude_371_571(actcd)) {
			if (Util.equals("Y", reVolve)) {
				kind_R5 = "Y";
			} else {
				if (quotaAmt != null
						&& quotaAmt.compareTo(CrsUtil.R8_1_8_2_SEP_AMT_500WAN) > 0) {
					kind_R8_2 = "Y"; // 無擔8-2金額＞{TWD500萬}
				} else {
					/*
					 * select b.*, d.subjcode2, subjnm from lms.c900m01b b left
					 * outer join lms.c900m01d d on b.subjcode=d.subjcode where
					 * prodkind='07'
					 */
					if (CrsUtil.isRule13_match(lnType, null)) {
						// 卡友信貸 由 8-1 移到 專案信貸
					} else {
						kind_R8_1 = "Y"; // 無擔8-1
					}
				}
			}
		}
		if (CrsUtil.inCollection(actcd, new String[] { "17620200", "17620400",
				"17620600", "17620800", "17621000", "17621200", "17621400",
				"17621600", "17621801", "17621802", "17622000", "17622200",
				"17622400", "17622800" })) {
			kind_R10 = "Y"; // 保證
		}

		if (CrsUtil.inCollection(lnType, new String[] { "33", "34" })) {
			kind_R11 = "Y"; // 土建融
		}
		// ========
		Map<String, String> tempMap = new HashMap<String, String>();
		if (Util.equals("Y", kind_R1)) {
			tempMap.put(CrsUtil.R1, "");
		}
		if (Util.equals("Y", kind_R2)) {
			tempMap.put(CrsUtil.R2, "");
		}
		if (Util.equals("Y", kind_R3)) {
			tempMap.put(CrsUtil.R3, "");
		}
		if (Util.equals("Y", kind_R4)) {
			tempMap.put(CrsUtil.R4, "");
		}
		if (Util.equals("Y", kind_R5)) {
			tempMap.put(CrsUtil.R5, "");
		}
		if (Util.equals("Y", kind_R6_1)) {
			tempMap.put(CrsUtil.R6_1, "");
		}
		if (Util.equals("Y", kind_R6_2)) {
			tempMap.put(CrsUtil.R6_2, "");
		}
		if (Util.equals("Y", kind_R7)) {
			tempMap.put(CrsUtil.R7, "");
		}
		if (Util.equals("Y", kind_R8_1)) {
			tempMap.put(CrsUtil.R8_1, "");
		}
		if (Util.equals("Y", kind_R8_2)) {
			tempMap.put(CrsUtil.R8_2, "");
		}
		if (Util.equals("Y", kind_R9)) {
			tempMap.put(CrsUtil.R9, "");
		}
		if (Util.equals("Y", kind_R10)) {
			tempMap.put(CrsUtil.R10, "");
		}
		if (Util.equals("Y", kind_R11)) {
			tempMap.put(CrsUtil.R11, "");
		}
		// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		if (MapUtils.isEmpty(tempMap)) {
			deriveUnknownSet.add(loanNo);
		} else {
			deriveMap.putAll(tempMap);
		}
	}

	public static boolean isCaseP(C241M01A c241m01a) {
		return Util.equals(CrsUtil.DOCKIND_P, c241m01a.getDocKind());
	}

	public static boolean isCaseN(C241M01A c241m01a) {
		return Util.isEmpty(c241m01a.getGrpCntrNo())
				&& Util.equals(CrsUtil.DOCKIND_N, c241m01a.getDocKind());
	}

	public static boolean isCaseG_Parent(C241M01A c241m01a) {
		return Util.isNotEmpty(c241m01a.getGrpCntrNo())
				&& Util.equals(CrsUtil.DOCKIND_G, c241m01a.getDocKind());
	}

	// 團貸裡的明細{docKind=H:小額/團體消費性貸款, docKind=N:除了「純信貸」之外還有需要覆審的其它產品、科目}
	public static boolean isCaseG___N_Detail(C241M01A c241m01a) {
		return Util.isNotEmpty(c241m01a.getGrpCntrNo())
				&& Util.equals(CrsUtil.DOCKIND_N, c241m01a.getDocKind());
		/*
		 * select * from lms.c241m01a where createtime>='2022-08-01' and
		 * grpcntrno like '918%' and dockind='N'
		 */
	}

	// 需一併考量 key="C241M01A.findGridListByC240M01AMainid" 裡的判斷
	// 團貸裡的明細{docKind=H:小額/團體消費性貸款, docKind=N:除了「純信貸」之外還有需要覆審的其它產品、科目}
	public static boolean isCaseG___H_Detail(C241M01A c241m01a) {
		return Util.isNotEmpty(c241m01a.getGrpCntrNo())
				&& Util.equals(CrsUtil.DOCKIND_H, c241m01a.getDocKind());
	}

	public static boolean isCaseG_Detail(C241M01A c241m01a) {
		if (isCaseG___N_Detail(c241m01a)) {
			return true;
		}
		if (isCaseG___H_Detail(c241m01a)) {
			return true;
		}
		return false;
	}

	public static boolean isCaseS(C241M01A c241m01a) {
		return Util.equals(CrsUtil.DOCKIND_S, c241m01a.getDocKind());
	}

	public static boolean isCaseH(C241M01A c241m01a) {
		return Util.isEmpty(c241m01a.getGrpCntrNo())
				&& Util.equals(CrsUtil.DOCKIND_H, c241m01a.getDocKind());
	}

	public static TreeMap<Integer, List<C241M01C>> caseS_to_seq(String rptId,
			Map<String, C241M01C> c241m01c_item_map) {
		TreeMap<Integer, List<C241M01C>> map = new TreeMap<Integer, List<C241M01C>>();
		if (Util.equals(CrsUtil.V_S_201902, rptId)
				|| Util.equals(CrsUtil.V_S_202204, rptId)) {
			_proc_caseS_to_seq(map, 1, c241m01c_item_map.get(CrsUtil.S010));
			_proc_caseS_to_seq(map, 2, c241m01c_item_map.get(CrsUtil.S021));
			_proc_caseS_to_seq(map, 2, c241m01c_item_map.get(CrsUtil.S022));
			_proc_caseS_to_seq(map, 3, c241m01c_item_map.get(CrsUtil.S031));
			_proc_caseS_to_seq(map, 3, c241m01c_item_map.get(CrsUtil.S032));
			_proc_caseS_to_seq(map, 4, c241m01c_item_map.get(CrsUtil.S040));
			_proc_caseS_to_seq(map, 5, c241m01c_item_map.get(CrsUtil.S051));
			_proc_caseS_to_seq(map, 5, c241m01c_item_map.get(CrsUtil.S052));
			_proc_caseS_to_seq(map, 6, c241m01c_item_map.get(CrsUtil.S061));
			_proc_caseS_to_seq(map, 6, c241m01c_item_map.get(CrsUtil.S062));
			_proc_caseS_to_seq(map, 7, c241m01c_item_map.get(CrsUtil.S070));
			_proc_caseS_to_seq(map, 8, c241m01c_item_map.get(CrsUtil.S081));
			_proc_caseS_to_seq(map, 8, c241m01c_item_map.get(CrsUtil.S082));
			_proc_caseS_to_seq(map, 8, c241m01c_item_map.get(CrsUtil.S083));
			_proc_caseS_to_seq(map, 9, c241m01c_item_map.get(CrsUtil.S090));
			_proc_caseS_to_seq(map, 10, c241m01c_item_map.get(CrsUtil.S101));
			_proc_caseS_to_seq(map, 10, c241m01c_item_map.get(CrsUtil.S102));
			_proc_caseS_to_seq(map, 10, c241m01c_item_map.get(CrsUtil.S103));
			_proc_caseS_to_seq(map, 11, c241m01c_item_map.get(CrsUtil.S110));
			_proc_caseS_to_seq(map, 12, c241m01c_item_map.get(CrsUtil.S120));
			_proc_caseS_to_seq(map, 13, c241m01c_item_map.get(CrsUtil.S130));
			_proc_caseS_to_seq(map, 14, c241m01c_item_map.get(CrsUtil.S140));
		}

		return map;
	}

	private static void _proc_caseS_to_seq(
			TreeMap<Integer, List<C241M01C>> map, Integer seq, C241M01C c241m01c) {
		if (c241m01c == null) {
			return;
		}
		if (!map.containsKey(seq)) {
			map.put(seq, new ArrayList<C241M01C>());
		}
		map.get(seq).add(c241m01c);
	}

	/**
	 * 當nckdFlag 為B, return true
	 * 
	 * @param param
	 * @return
	 */
	public static boolean isNckdFlag_EMPTY_A(String nckdFlag) {
		if (StringUtils.isBlank(nckdFlag)
				|| Util.equals(CrsUtil.NCKDFLAG_A, nckdFlag)) {
			return true;
		} else {
			return false;
		}
	}

	public static boolean isOnly8_1(C241M01A c241m01a) {
		Set<String> ruleSet = CrsUtil.parseRule(c241m01a.getRetrialKind())
				.keySet();
		ruleSet.remove(CrsUtil.R12);
		return (ruleSet.size() == 1 && ruleSet.contains(CrsUtil.R8_1));
	}

	public static boolean isOnly6_2(String remomo) {
		Set<String> ruleSet = CrsUtil.parseRule(Util.trim(remomo)).keySet();
		return (ruleSet.size() == 1 && ruleSet.contains(CrsUtil.R6_2));
	}

	public static Set<String> convert_arr_to_set(String[] src_arr) {
		Set<String> result = new HashSet<String>();
		for (String s : src_arr) {
			result.add(s);
		}
		return result;
	}

	public static Set<String> convert_arr_to_set(String[] src_arr,
			String[] src_arr2) {
		Set<String> result = new HashSet<String>();
		for (String s : src_arr) {
			result.add(s);
		}
		for (String s : src_arr2) {
			result.add(s);
		}
		return result;
	}

	public static boolean isInR1R2R4_noOthers(String remomo) {
		Set<String> ruleSet = CrsUtil.parseRule(Util.trim(remomo)).keySet();
		int mapR1R2R4_cnt = 0;
		int map_NotR1R2R4_cnt = 0;
		for (String rule : ruleSet) {
			if (Util.equals(CrsUtil.R1, rule) || Util.equals(CrsUtil.R2, rule)
					|| Util.equals(CrsUtil.R4, rule)) {
				mapR1R2R4_cnt++;
			} else {
				map_NotR1R2R4_cnt++;
			}
		}
		return (mapR1R2R4_cnt > 0 && map_NotR1R2R4_cnt == 0);
	}

	public static boolean isOnlyR2R4_noOthers(String remomo) {
		Set<String> ruleSet = CrsUtil.parseRule(Util.trim(remomo)).keySet();
		int matchRule_cnt = 0;
		int notMatch_cnt = 0;
		for (String rule : ruleSet) {
			if (Util.equals(CrsUtil.R2, rule) || Util.equals(CrsUtil.R4, rule)) {
				matchRule_cnt++;
			} else {
				notMatch_cnt++;
			}
		}
		return (matchRule_cnt > 0 && notMatch_cnt == 0);
	}

	public static boolean isExistBatchNo(C240M01A meta) {
		Integer batchNo = meta.getBatchNO();
		if (batchNo == null || batchNo == 0) {
			return false;
		} else {
			return true;
		}
	}

	public static String getExcelName(C240M01A c240m01a, String fieldId) {
		String tpName = "0000000";
		if (isNOT_null_and_NOTZeroDate(c240m01a.getExpectedRetrialDate())) {
			tpName = TWNDate.valueOf(c240m01a.getExpectedRetrialDate()).toTW();
		}

		String batchNO = "001";
		if (isExistBatchNo(c240m01a)) {
			batchNO = Util.addZeroWithValue(c240m01a.getBatchNO(), 3);
		}
		// ------
		if (Util.equals(ATTCH_C240M01A_0, fieldId)) {
			return c240m01a.getBranchId() + "-" + tpName + "-"
					+ "授信覆審名單工作底稿(消金)" + "-" + batchNO + ".xls";
		} else if (Util.equals(ATTCH_C240M01A_1, fieldId)) {
			String tStatus = "";
			RetrialDocStatusEnum docStatusEnum = RetrialDocStatusEnum
					.getEnum(c240m01a.getDocStatus());
			if (docStatusEnum == RetrialDocStatusEnum.編製中) {
				tStatus = "(編製中)";
			} else if (docStatusEnum == RetrialDocStatusEnum.待覆核) {
				tStatus = "(待覆核)";
			}
			return c240m01a.getBranchId() + "-" + tpName + "-" + "消金授信覆審名單"
					+ tStatus + "-" + batchNO + ".xls";
		} else if (Util.equals(ATTCH_C240M01A_2, fieldId)) {
			return c240m01a.getBranchId() + "-" + tpName + "-" + "消金授信覆審驗證名單"
					+ "-" + batchNO + ".xls";
		} else if (Util.equals(ATTCH_C240M01A_3A, fieldId)) {
			return "個人戶.csv";
		} else if (Util.equals(ATTCH_C240M01A_3B, fieldId)) {
			return "公司戶.csv";
		} else if (Util.equals(ATTCH_C241M01A_GRPDTL, fieldId)) {
			return c240m01a.getBranchId() + "-" + tpName + "-" + "消金授信團貸名單"
					+ batchNO + ".xls";
		}
		return "";
	}

	/**
	 * 在 client 呈現的順序及文字 LMS2411M01Page_zh_TW.properties
	 * <ul>
	 * <li>label.Y=是</li>
	 * <li>label.N=否</li>
	 * <li>label.K=一</li>
	 * <li>label.Y2=有</li>
	 * <li>label.N2=無</li>
	 * <li>label.Y3=已改善</li>
	 * <li>label.N3=未改善</li>
	 * <li>label.Y4=正確</li>
	 * <li>label.N4=不正確</li>
	 * </ul>
	 */
	public static String chkResult_fmt(C241M01C c241m01c) {

		// 前次覆審有無應行改善事項？【無|有|一】
		if (Util.equals(CrsUtil.N013, c241m01c.getItemNo())) {
			return "N2|Y2|K";
		}

		// 擔保物是否發生變化，致影響本行債權？ 【否|是|一】
		if (Util.equals(CrsUtil.N015, c241m01c.getItemNo())) {
			return "N|Y|K";
		}

		// 是否有其他未依相關規定辦理者？【否|是|一】
		if (Util.equals(CrsUtil.N023, c241m01c.getItemNo())) {
			return "N|Y|K";
		}

		// Z_電腦建檔
		{
			for (String itemNo : ZSYS_TITLE) {
				if (Util.equals(itemNo, c241m01c.getItemNo())) {
					return "";
				}
			}

			for (String itemNo : YZ_SYS_ITEM) {
				if (Util.equals(itemNo, c241m01c.getItemNo())) {
					if (Util.equals(CrsUtil.ZB1A, c241m01c.getItemNo())) {
						return "Y2|N2";// 【有|無 】
					}
					if (Util.equals(CrsUtil.Y_NY10, c241m01c.getItemNo())) {
						return "Y2|N2";// 【有|無 】
					}
					if (Util.equals(CrsUtil.ZA25, c241m01c.getItemNo())) {
						return "Y4|N4|K";// 【正確|不正確|一】
					}

					if (Util.equals(CrsUtil.ZB2A, c241m01c.getItemNo())) {
						return "Y2|N2";// 【有|無 】
					}

					return "Y|N|K";// 【是|否|一】
				}
			}
		}

		if (true) {
			// 防杜代辦覆審
			String[] s_arr = { CrsUtil.S082, CrsUtil.S090, CrsUtil.S101,
					CrsUtil.S102, CrsUtil.S140 };
			if (CrsUtil.inCollection(c241m01c.getItemNo(), s_arr)) {
				return "N|Y|K";
			}
		}
		// default【是|否|一】
		return "Y|N|K";
	}

	/**
	 * 四捨五入
	 */
	public static String amtDivide1000(BigDecimal src) {
		if (src == null) {
			return "0";
		}
		BigDecimal thr = new BigDecimal(1000);
		return NumConverter.addComma(src.divide(thr, 0,
				BigDecimal.ROUND_HALF_UP));
	}

	/**
	 * 無條件捨去
	 */
	public static String amtDivide1000_floor(BigDecimal src) {
		if (src == null) {
			return "0";
		}
		BigDecimal thr = new BigDecimal(1000);
		return NumConverter
				.addComma(src.divide(thr, 0, BigDecimal.ROUND_FLOOR));
	}

	/**
	 * 無條件進位
	 */
	public static String amtDivide1000_up(BigDecimal src) {
		if (src == null) {
			return "0";
		}
		BigDecimal thr = new BigDecimal(1000);
		return NumConverter.addComma(src.divide(thr, 0, BigDecimal.ROUND_UP));
	}

	public static Date get_Use_Loan_FDate(C241M01B c241m01b) {
		return get_Use_Loan_PeriodDate(c241m01b)[0];
	}

	/**
	 * 參考:gfnCreatNPrintData
	 * 
	 * LNF020_BEG_DATE 動用起日 ----------- c241m01b.UseFDate LNF020_DURATION_BG
	 * 中長期授信期間起日 -- c241m01b.LoanFDate
	 * '如果有中長期授信期間，且已用額度大於等於核准額度則填授信期間不然如果過動用迄日則填中長期授信期間，不然則填動用起迄日
	 * '如果沒有中長期授信期間，則填動用起迄日
	 * 
	 * 核准額度 LNF020_FACT_AMT 已用額度 LNF020_USED_AMT --- 在 C241M01B 無對應欄位
	 */
	public static Date[] get_Use_Loan_PeriodDate(C241M01B c241m01b) {
		Date[] arr = new Date[2];
		arr[0] = null;
		arr[1] = null;

		boolean fetch_useDate = true;// default 抓動用起迄日
		if (isNOT_null_and_NOTZeroDate(c241m01b.getLoanFDate())
				&& LMSUtil.cmpDate(c241m01b.getLnDataDate(), ">",
						c241m01b.getUseEDate())) {
			fetch_useDate = false;// 改抓 中長期授信期間起迄日
		}

		if (fetch_useDate) {
			arr[0] = c241m01b.getUseFDate();
			arr[1] = c241m01b.getUseEDate();
		} else {
			arr[0] = c241m01b.getLoanFDate();// 授信起日
			arr[1] = c241m01b.getLoanEDate();// 授信迄日
		}
		return arr;
	}

	/**
	 * 目前 007,201 既是覆審組, 也是受檢行
	 */
	public static boolean isRetrialTeam(MegaSSOUserDetails user) {
		boolean r = false;
		if (user != null) {
			r = _isRetrialTeam(user.getSsoUnitNo());
		}
		return r;
	}

	private static boolean _isRetrialTeam(String brId) {
		boolean r = false;

		HashSet<String> allowBrSet = new HashSet<String>();
		// J-111-0622，國外部、金控總部分行、私銀處作業組，由北區營運中心覆審
		// allowBrSet.add(UtilConstants.BankNo.國外部);// 007
		// allowBrSet.add(UtilConstants.BankNo.金控總部分行);// 201
		// 025也有企金覆審組的權限
		allowBrSet.add(UtilConstants.BankNo.國金部);
		// allowBrSet.add(UtilConstants.BankNo.私銀處作業組);
		if (brId.startsWith("9") || allowBrSet.contains(brId)) {
			r = true;
		}

		return r;
	}

	public static boolean haveBeenUpload(C241M01A c241m01a) {
		if (isNOT_null_and_NOTZeroDate(c241m01a.getUpDate())) {
			return true;
		}
		return false;
	}

	public static Date r98_1st(Date sDate, String brNo) {
		if (isNull_or_ZeroDate(sDate)) {
			return CapDate.parseDate(CapDate.ZERO_DATE);
		} else {
			// J-107-0223 異常通報由3個月改1個月
			// J-109-0213 金門分行異常通報案件之應辦理一般覆審期限，由批覆日之次月底前，修訂為三個月內。
			int addedMonth = 1;
			if (Util.equals("079", brNo)) {
				addedMonth = 3;
			}
			// J-110-0272 自110/05/03後，花蓮分行也納入，應於異常通報批覆日之三個月內辦理覆審
			if (Util.equals("023", brNo)) {
				addedMonth = 3;
			}
			String target = StringUtils.substring(
					TWNDate.toAD(CapDate.addMonth(sDate, addedMonth)), 0, 7)
					+ "-01";

			// 上面的變數 target 是 yyyy-MM-01
			// 因為有大月、小月、潤月 => 依 data 取該月的最後一天為覆審期限
			return CapDate.shiftDays(
					CapDate.addMonth(CapDate.parseDate(target), 1), -1);
		}
	}

	public static Date r98_after1st(Date lrDate) {
		return CapDate.addMonth(lrDate, 6);
	}

	public static void setR98Data(C241M01A c241m01a, String elf491_remomo,
			Date elf491_uckddt, String elf491_uckdline) {
		Map<String, String> map = CrsUtil.parseRule(elf491_remomo);
		if (CrsUtil.isNOT_null_and_NOTZeroDate(elf491_uckddt)
				&& map.keySet().contains(CrsUtil.R98)) {
			c241m01a.setUckdDt(elf491_uckddt);
			c241m01a.setUckdLine(Util.trim(elf491_uckdline));
			c241m01a.setMdFlag(Util.trim(map.get(CrsUtil.R98)));
		}
	}

	/**
	 * 由 2 → 02 <br/>
	 * 前補0到2碼 <br/>
	 * 在ELF491裡.98(02)
	 */
	public static String mdFlag_with_leadingZero(String raw_s) {
		String s = Util.trim(raw_s);
		if (Util.isNotEmpty(s)) {
			return Util.getRightStr("00" + s, 2);
		}
		return s;
	}

	/**
	 * 由 02 → 2 <br/>
	 * 在ELF412裡.沒有前補0
	 */
	public static String mdFlag_trim_leadingZero(String s) {
		if (s != null && s.startsWith("0")) {
			return String.valueOf(Util.parseInt(s));
		} else {
			return s;
		}
	}

	public static int getR8_1_total(String remomo) {
		return Util.parseInt(StringUtils.substring(remomo, 0, 13));
	}

	public static int getR8_1_already(String remomo) {
		return Util.parseInt(StringUtils.substring(remomo, 13, 26));
	}

	public static String buildR8_1_total(int cnt) {
		return Util.addZeroWithValue(cnt, 13);
	}

	public static String buildR8_1_already(int cnt) {
		return Util.addZeroWithValue(cnt, 13);
	}

	public static String build_P_Key(C241M01B c241m01b) {
		Map<String, Object> m = new HashMap<String, Object>();
		if (c241m01b != null) {
			m.put("LNF020_CONTRACT", c241m01b.getQuotaNo());
			m.put("LNF034_LC_NO", c241m01b.getLcNo());
		}
		return build_P_Key(m);
	}

	public static String build_P_Key(Map<String, Object> m) {
		return Util.trim(m.get("LNF020_CONTRACT")) + "^"
				+ Util.trim(m.get("LNF034_LC_NO"));
	}

	public static boolean is_flowClass_throughBr(SysParameterService sysParameterService,C240M01A meta) {
		boolean result = false;
		String moveToBr = Util.trim(sysParameterService.getParamValue("LMS_RETRIAL_MOVE_TO_BR"));
		if (Util.notEquals(moveToBr, "")) {
			for (String xx : moveToBr.split(",")) {
				boolean thisResult = xx.equals(meta.getOwnBrId());
				if (thisResult) {
					result = thisResult;
					break;
				}
			}
		}
		return result;
	}

	public static Map<String, String> parse_exeParam(String exeParam) {
		Map<String, String> r = new TreeMap<String, String>();
		if (StringUtils.isNotBlank(exeParam)) {
			String[] dataSplit = Util.trim(exeParam).split("\\|");
			for (String temp : dataSplit) {
				String brNo = Util.trim(temp.split("\\^")[0]);
				String yyyy_MM = Util.trim(temp.split("\\^")[1]);
				if (brNo.length() == 3 && yyyy_MM.length() == 7) {
					r.put(brNo, yyyy_MM);
				}
			}
		}
		return r;
	}

	/**
	 * @param adDate
	 *            來源格式 20141201
	 * @return 6碼民國年月010312(前4碼年, 後2碼月)
	 */
	public static String elf490YM_from_adDate(String adDate) {
		String yyyy = adDate.substring(0, 4);
		String mm = adDate.substring(4, 6);
		// elf490_data_ym 的格式: 009912
		return StringUtils.right("0000" + (Integer.parseInt(yyyy) - 1911) + mm,
				6);
	}

	public static String elf490YM_from_adDate(Date date) {
		return CrsUtil.elf490YM_from_adDate(TWNDate.valueOf(date).toAD());
	}

	/**
	 * @param elf490YM
	 *            來源格式 009912
	 * @param p_dd
	 *            01
	 * @return 西元年 20001201(中間無分隔符號)
	 */
	public static String elf490YM_to_adDate_s(String elf490YM, String p_dd) {
		String twyyyy = elf490YM.substring(0, 4);
		String mm = elf490YM.substring(4, 6);
		String dd = StringUtils.right("00" + p_dd, 2);
		return (Integer.parseInt(twyyyy) + 1911) + mm + dd;
	}

	public static Date elf490YM_to_adDate_d(String elf490YM) {
		return CapDate.parseDate(elf490YM_to_adDate_s(elf490YM, "01"));
	}

	public static boolean canSaveC241M01A(MegaSSOUserDetails user, C241M01A meta) {
		if (meta == null) {
			return false;
		}
		String docStatus = Util.trim(meta.getDocStatus());
		if (Util.equals(docStatus, RetrialDocStatusEnum.區中心_編製中.getCode())) {
			return true;
		} else if (Util.equals(docStatus, RetrialDocStatusEnum.編製中.getCode())
				|| Util.equals(docStatus, RetrialDocStatusEnum.待覆核.getCode())) {
			return false;
		} else if (Util.equals(docStatus,
				RetrialDocStatusEnum.區中心_待覆核.getCode())
				|| Util.equals(docStatus, RetrialDocStatusEnum.已覆核未核定.getCode())
				|| Util.equals(docStatus, RetrialDocStatusEnum.已覆核已核定.getCode())) {
			if (isRetrialTeam(user)) {
				String approver = Util.trim(meta.getApprover());

				if (Util.isEmpty(approver)) {
					return true;
				} else {
					// 上傳人員為 BATCH,任何總處人員,皆可修改
					if (approver.equalsIgnoreCase("BATCH")) {
						return true;
					}
					if (Util.equals(approver, user.getUserId())) {
						return true;
					}
				}
				return false;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}

	/**
	 * 在區中心待覆核，和消金的判斷不同
	 */
	public static boolean canSaveL170M01A(MegaSSOUserDetails user, L170M01A meta) {
		if (meta == null) {
			return false;
		}
		if (LrsUtil.isFromNotes(meta)) {
			return false;
		}
		String docStatus = Util.trim(meta.getDocStatus());
		if (Util.equals(docStatus, RetrialDocStatusEnum.區中心_編製中.getCode())) {
			return true;
		} else if (Util.equals(docStatus, RetrialDocStatusEnum.編製中.getCode())
				|| Util.equals(docStatus, RetrialDocStatusEnum.待覆核.getCode())) {
			return false;
		} else if (Util.equals(docStatus,
				RetrialDocStatusEnum.區中心_待覆核.getCode())) {
			return false;
		} else if (Util
				.equals(docStatus, RetrialDocStatusEnum.已覆核未核定.getCode())
				|| Util.equals(docStatus, RetrialDocStatusEnum.已覆核已核定.getCode())) {

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			boolean isRetrialTeam = false;
			if (Util.equals(Util.trim(meta.getCtlType()), LrsUtil.CTLTYPE_自辦覆審)) {
				isRetrialTeam = true;
			} else {
				isRetrialTeam = isRetrialTeam(user);
			}

			if (isRetrialTeam) {
				String approver = Util.trim(meta.getApprover());

				if (Util.isEmpty(approver)) {
					return true;
				} else {
					// 上傳人員為 BATCH,任何總處人員,皆可修改
					if (approver.equalsIgnoreCase("BATCH")) {
						return true;
					}
					if (Util.equals(approver, user.getUserId())) {
						return true;
					}
				}
				return false;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}

	public static String get_chkItem_valDesC(C241M01C c241m01c,
			Properties prop_lms2411m01) {
		String desc = "";
		if (Util.isNotEmpty(Util.trim(c241m01c.getChkResult()))) {
			String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c),
					"|");
			if (fmt != null && fmt.length > 0) {
				for (String fmt_str : fmt) {
					if (Util.equals(c241m01c.getChkResult(),
							Util.getLeftStr(fmt_str, 1))) {
						desc = prop_lms2411m01.getProperty("label." + fmt_str);
						break;
					}
				}
			}
		}
		return desc;

	}

	public static String get_chkItem_ZB1A(C241M01C c241m01c,
			Properties prop_lms2411m01, boolean forceShort) {
		String desc = "";
		String post = CrsUtil.Z_DESC_ZB1A_Y;
		if (Util.isNotEmpty(Util.trim(c241m01c.getChkResult()))) {
			String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c),
					"|");
			if (fmt != null && fmt.length > 0) {
				for (String fmt_str : fmt) {
					if (Util.equals(c241m01c.getChkResult(),
							Util.getLeftStr(fmt_str, 1))) {
						desc = prop_lms2411m01.getProperty("label." + fmt_str);
						break;
					}
				}
			}

			if (Util.equals("N", c241m01c.getChkResult())) {
				post = CrsUtil.Z_DESC_ZB1A_N;
			}
		}
		return c241m01c.getChkItem() + "「" + desc + "」"
				+ (forceShort ? CrsUtil.Z_DESC_ZB1A_N : post);

	}

	public static String get_chkItem_ZB2A(C241M01C c241m01c,
			Properties prop_lms2411m01, boolean forceShort) {
		String desc = "";
		String post = CrsUtil.Z_DESC_ZB2A_Y;
		if (Util.isNotEmpty(Util.trim(c241m01c.getChkResult()))) {
			String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c),
					"|");
			if (fmt != null && fmt.length > 0) {
				for (String fmt_str : fmt) {
					if (Util.equals(c241m01c.getChkResult(),
							Util.getLeftStr(fmt_str, 1))) {
						desc = prop_lms2411m01.getProperty("label." + fmt_str);
						break;
					}
				}
			}

			if (Util.equals("N", c241m01c.getChkResult())) {
				post = CrsUtil.Z_DESC_ZB2A_N;
			}
		}
		return c241m01c.getChkItem() + "「" + desc + "」"
				+ (forceShort ? CrsUtil.Z_DESC_ZB2A_N : post);

	}

	public static String get_chkItem_Y_NY10(C241M01C c241m01c_Y_NY10,
			Properties prop_lms2411m01, boolean forceShort) {
		if (c241m01c_Y_NY10 == null) {
			return "";
		}

		String desc = "";
		String post = CrsUtil.Y_DESC_NY10_Y;
		if (Util.isNotEmpty(Util.trim(c241m01c_Y_NY10.getChkResult()))) {
			String[] fmt = StringUtils.split(
					CrsUtil.chkResult_fmt(c241m01c_Y_NY10), "|");
			if (fmt != null && fmt.length > 0) {
				for (String fmt_str : fmt) {
					if (Util.equals(c241m01c_Y_NY10.getChkResult(),
							Util.getLeftStr(fmt_str, 1))) {
						desc = prop_lms2411m01.getProperty("label." + fmt_str);
						break;
					}
				}
			}

			if (Util.equals("N", c241m01c_Y_NY10.getChkResult())) {
				post = CrsUtil.Y_DESC_NY10_N;
			}
		}
		return c241m01c_Y_NY10.getChkItem() + "「" + desc + "」"
				+ (forceShort ? CrsUtil.Y_DESC_NY10_N : post);
	}

	public static boolean hasR99(C241M01A meta) {
		return CrsUtil.parseRule(meta.getRetrialKind()).keySet()
				.contains(CrsUtil.R99);
	}

	public static boolean hasR99_Cycle(C241M01A meta) {
		return Util.isNotEmpty(Util.trim(meta.getSpecifyCycle()));
	}

	public static boolean has_sBalCurrAmt(C241M01B c241m01b) {
		return (Util.isNotEmpty(c241m01b.getSBalCurr()) && c241m01b
				.getSBalAmt() != null);
	}

	public static void processSubjName(C241M01B c241m01b) {
		if (Util.equals("673", c241m01b.getSubjectNo())
				&& Util.equals("長期房屋擔保放款", c241m01b.getSubjectName())) {
			c241m01b.setSubjectName("長期房屋擔保放款－房屋購置");
		}
	}

	public static Set<String> grp_detail_allow_companyIdDup(String brNo) {
		String id_22099131_1 = LMSUtil.getCustKey_len10custId("22099131", "1");
		String id_47217677_0 = LMSUtil.getCustKey_len10custId("47217677", "0");

		Set<String> r = new HashSet<String>();
		if (Util.equals(brNo, "020")) {
			r.add(id_22099131_1);
			r.add(id_47217677_0);
		} else if (Util.equals(brNo, "026")) {
			r.add(id_22099131_1);
			r.add(id_47217677_0);
		} else if (Util.equals(brNo, "058")) {
			r.add(id_22099131_1);
			r.add(id_47217677_0);
		} else if (Util.equals(brNo, "065")) {
			r.add(id_22099131_1);
		} else if (Util.equals(brNo, "075")) {
			r.add(id_22099131_1);
			r.add(id_47217677_0);
		} else if (Util.equals(brNo, "076")) {
			r.add(id_22099131_1);
		} else if (Util.equals(brNo, "203")) {
			r.add(id_22099131_1);
			r.add(id_47217677_0);
		} else if (Util.equals(brNo, "208")) {
			r.add(id_22099131_1);
			r.add(id_47217677_0);
		} else if (Util.equals(brNo, "215")) {
			r.add(id_22099131_1);
			r.add(id_47217677_0);
		} else if (Util.equals(brNo, "242")) {
			r.add(id_22099131_1);
			r.add(id_47217677_0);
		} else if (Util.equals(brNo, "900")) {
			r.add(id_22099131_1);
			r.add(id_47217677_0);
		}
		return r;
	}

	/** 行家理財(短期) */
	public static boolean is_02(String prodKind) {
		return Util.equals("02", prodKind);
	}

	/** 行家理財(中長期) */
	public static boolean is_03(String prodKind) {
		return Util.equals("03", prodKind);
	}

	/** (綜合理財) 房貸還本週轉 */
	public static boolean is_04(String prodKind) {
		return Util.equals("04", prodKind);
	}

	/** 一般消貸含團體消貸 */
	public static boolean is_07(String prodKind) {
		return Util.equals("07", prodKind);
	}

	/** 卡友信貸 */
	public static boolean is_08(String prodKind) { // J-108-0187
		return Util.equals("08", prodKind);
	}

	/** 輔助勞工建購住宅貸款_一般建購 */
	public static boolean is_17(String prodKind) {
		return Util.equals("17", prodKind);
	}

	/** 輔助勞工建購住宅貸款_一般修繕 */
	public static boolean is_18(String prodKind) {
		return Util.equals("18", prodKind);
	}

	/** 輔助勞工建購住宅貸款_921建購 */
	public static boolean is_19(String prodKind) {
		return Util.equals("19", prodKind);
	}

	/** 921貸款專案(新購屋) */
	public static boolean is_22(String prodKind) {
		return Util.equals("22", prodKind);
	}

	/** 921貸款專案(重建) */
	public static boolean is_23(String prodKind) {
		return Util.equals("23", prodKind);
	}

	/** 921貸款專案(修繕) */
	public static boolean is_24(String prodKind) {
		return Util.equals("24", prodKind);
	}

	/** 8000億優惠購屋專案 */
	public static boolean is_28(String prodKind) {
		return Util.equals("28", prodKind);
	}

	/** 歡喜房貸 */
	public static boolean is_30(String prodKind) {
		return Util.equals("30", prodKind);
	}

	/** 歡喜房貸 */
	public static boolean is_31(String prodKind) {
		return Util.equals("31", prodKind);
	}

	/** 93年3000億優惠購屋專案 */
	public static boolean is_35(String prodKind) {
		return Util.equals("35", prodKind);
	}

	/** 政策性留學生貸款 */
	public static boolean is_36(String prodKind) {
		return Util.equals("36", prodKind);
	}

	/** 內政部整合住宅方案-弱勢戶(第一類) */
	public static boolean is_38(String prodKind) {
		return Util.equals("38", prodKind);
	}

	/** 內政部整合住宅方案-一般戶(第二類) */
	public static boolean is_39(String prodKind) {
		return Util.equals("39", prodKind);
	}

	/** 次順位房貸 */
	public static boolean is_49(String prodKind) {
		return Util.equals("49", prodKind);
	}

	/** 97年2000億優惠購屋專案 */
	public static boolean is_56(String prodKind) {
		return Util.equals("56", prodKind);
	}

	/** 青年安心成家(內政部) */
	public static boolean is_57(String prodKind) {
		return Util.equals("57", prodKind);
	}

	/** 青年創業貸款 */
	public static boolean is_58(String prodKind) {
		return Util.equals("58", prodKind);
	}

	/** 青年安心成家(財政部) */
	public static boolean is_59(String prodKind) {
		return Util.equals("59", prodKind);
	}

	/** 青年築夢創業啟動金貸款 */
	public static boolean is_60(String prodKind) {
		return Util.equals("60", prodKind);
	}

	/** 青年創業及啟動金貸款 */
	public static boolean is_61(String prodKind) {
		return Util.equals("61", prodKind);
	}
	
	/** 天然及重大災害受災戶-修繕 */
	public static boolean is_65(String prodKind) {
		return Util.equals("65", prodKind);
	}

	/** 以房養老 */
	public static boolean is_67(String prodKind) {
		return Util.equals("67", prodKind);
	}

	/** 行家理財貸款-中期循環 */
	public static boolean is_68(String prodKind) { // J-107-0008
		return Util.equals("68", prodKind);
	}

	/** 勞工紓困貸款 */
	public static boolean is_69(String prodKind) {
		return Util.equals("69", prodKind);
	}

	/** 以房養老(累積型) */
	public static boolean is_70(String prodKind) { // J-109-0271
		return Util.equals("70", prodKind);
	}

	/** 歡喜信貸 */
	public static boolean is_71(String prodKind) {
		return Util.equals("71", prodKind);
	}

	/** 地上權住宅貸款 */
	public static boolean is_72(String prodKind) {
		return Util.equals("72", prodKind);
	}

	/** 專案種類-菁英人員信貸 */
	public static boolean is_Y1(String projClass) {
		return Util.equals("Y1", projClass);
	}
	
	/** 專案種類-菁英人員理財型貸款(循環動用)*/
	public static boolean is_Y2(String projClass) {
		return Util.equals("Y2", projClass);
	}

	/** 專案種類-次順位房貸 */
	public static boolean is_Y3(String projClass) {
		return Util.equals("Y3", projClass);
	}

	/** 專案種類-尊榮方案 */
	public static boolean is_A1(String projClass) {
		return Util.equals("A1", projClass);
	}

	public static boolean is_tabDoc_only_apply_prodKind(
			List<L140S02A> l140s02a_list, String specificProdKind) {
		int[] cnt_arr = CrsUtil.count_prodKind_specific_vs_others(
				l140s02a_list, specificProdKind);
		int new_prod_specific = cnt_arr[0];
		int new_prod_oth = cnt_arr[1];

		if (new_prod_specific > 0 && new_prod_oth == 0) {
			return true;
		}
		return false;
	}

	public static int[] count_prodKind_specific_vs_others(
			List<L140S02A> l140s02a_list, String specificProdKind) {
		int cnt_specific = 0;
		int cnt_others = 0;

		for (L140S02A l140s02a : l140s02a_list) {
			if (Util.equals(specificProdKind, l140s02a.getProdKind())) {
				++cnt_specific;
			} else {
				++cnt_others;
			}
		}

		return new int[] { cnt_specific, cnt_others };
	}

	public static boolean is_prodKind_in_63_64_65(String prodKind) {
		if (Util.equals("63", prodKind)) {
			return true;
		}
		if (Util.equals("64", prodKind)) {
			return true;
		}
		if (Util.equals("65", prodKind)) {
			return true;
		}
		return false;
	}

	public static Set<String> getProdKind_68_block_rateType() {
		// J-107-0303 因行員為利害關係人，其授信條件不可優於XX對象
		Set<String> set = new HashSet<String>();
		set.add(CrsUtil.RATE_TYPE_M2);
		set.add(CrsUtil.RATE_TYPE_N2);
		set.add(CrsUtil.RATE_TYPE_M3);
		set.add(CrsUtil.RATE_TYPE_MR);
		set.add(CrsUtil.RATE_TYPE_7D);
		set.add(CrsUtil.RATE_TYPE_MR);
		return set;
	}

	public static String getProdKindName(C900M01A c900m01a) {
		if (c900m01a == null) {
			return "";
		} else {
			String name = "";
			if (Util.isNotEmpty(c900m01a.getProdNm2())) {
				name = Util.trim(c900m01a.getProdNm1()) + "-"
						+ Util.trim(c900m01a.getProdNm2());
			} else {
				name = Util.trim(c900m01a.getProdNm1());
			}
			// ref ProdServiceImpl :: sub_personalString
			int idx = name.lastIndexOf("-個人戶");
			if (idx > 0) {
				return name.substring(0, idx);
			}
			return name;
		}
	}

	public static boolean docKindG_since_V202008(C241M01A meta) {
		if (CrsUtil.isCaseG_Parent(meta)) {
			if (Util.equals(meta.getRptId(), CrsUtil.V_G_20111201)
					|| Util.equals(meta.getRptId(), CrsUtil.V_G_201805)) {
				// 之前格式
			} else {
				return true;
			}
		}
		return false;
	}

	public static boolean docKindN_since_V202008(C241M01A meta) {
		if (CrsUtil.isCaseN(meta) || CrsUtil.isCaseG___N_Detail(meta)) {
			if (Util.equals(meta.getRptId(), CrsUtil.V_N_20111201)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201412)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201707A)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201707B)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201805A)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201805B)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201805A2)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201805B2)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201907NA)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201907NB)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201907GA)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201907GB)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201909NA)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201909NB)) {
				// 之前格式
			} else {
				return true;
			}
		}
		return false;
	}

	public static boolean docKindN_since_R11(C241M01A meta) {
		if (CrsUtil.isCaseN(meta) || CrsUtil.isCaseG___N_Detail(meta)) {
			if (Util.equals(meta.getRptId(), CrsUtil.V_N_20111201)
					|| Util.equals(meta.getRptId(), CrsUtil.V_N_201412)) {
				// 之前格式
			} else {
				return true;
			}
		}
		return false;
	}

	/**
	 * @param c240m01a_branchId
	 * @param c241m01b_list
	 * @return {Y表示土建融同業聯貸主辦, O:只有土建融聯貸參貸(非主辦), N:無土建融額度}
	 */
	public static String match_R11_syndType(String c240m01a_branchId,
			List<C241M01B> c241m01b_list) {
		int cnt1 = 0;
		int cnt2 = 0;
		for (C241M01B c241m01b : c241m01b_list) {
			String lnType = c241m01b.getLnType();
			String quotaNo = c241m01b.getQuotaNo();
			String loanNo = c241m01b.getLoanNo();
			String syndType = c241m01b.getSyndType();
			if (Util.equals("33", lnType) || Util.equals("34", lnType)) {
				if (Util.equals(c240m01a_branchId,
						getBrNoFromLNF020_CONTRACT(quotaNo))
						&& Util.equals(getBrNoFromLNF030_LOAN_NO(loanNo),
								getBrNoFromLNF020_CONTRACT(c241m01b
										.getQuotaNo()))) {
					if (Util.equals(syndType, "2")) {
						cnt2++;
					} else {
						cnt1++;
					}
				} else {
					// 若非本分行
				}
			}
		}

		if (cnt1 == 0 && cnt2 > 0) {
			return "O";
		} else if (cnt1 > 0) {
			return "Y";
		}
		return "N";
	}

	public static Date match_R11_syndType_lnDate(String c240m01a_branchId,
			List<C241M01B> c241m01b_list) {
		Date r = null;
		int cnt1 = 0;
		int cnt2 = 0;
		for (C241M01B c241m01b : c241m01b_list) {
			String lnType = c241m01b.getLnType();
			String quotaNo = c241m01b.getQuotaNo();
			String loanNo = c241m01b.getLoanNo();
			String syndType = c241m01b.getSyndType();
			if (Util.equals("33", lnType) || Util.equals("34", lnType)) {
				if (Util.equals(c240m01a_branchId,
						getBrNoFromLNF020_CONTRACT(quotaNo))
						&& Util.equals(getBrNoFromLNF030_LOAN_NO(loanNo),
								getBrNoFromLNF020_CONTRACT(c241m01b
										.getQuotaNo()))) {
					if (Util.equals(syndType, "2")) {
						cnt2++;
					} else {
						cnt1++;
						Date lnDate = c241m01b.getLoanFDate();
						if (lnDate != null) {
							if (r == null) {
								r = lnDate;
							} else {
								if (LMSUtil.cmpDate(lnDate, ">", r)) {
									r = lnDate;
								}
							}
						}
					}
				} else {
					// 若非本分行
				}
			}
		}

		// if(cnt1==0 && cnt2>0){
		// return "O";
		// }else if(cnt1>0){
		// return "Y";
		// }
		return r;
	}

	private static String _build_catkey_Pteamapp_LNF(Pteamapp_LNF vo) {
		String _grpCntrNo = vo.getLnf020_grp_cntrno();
		String _brNo = vo.getBrno();
		return _build_catkey_Pteamapp_LNF(_grpCntrNo, _brNo);
	}

	private static String _build_catkey_Pteamapp_LNF(String _grpCntrNo,
			String _brNo) {
		return _grpCntrNo + "-" + _brNo;
	}

	public static Pteamapp_TOT sumAllBrNo(Map<String, Pteamapp_TOT> totBrNoMap) {
		Pteamapp_TOT r = new Pteamapp_TOT();

		int _CntById = 0;
		int _CntByCntrNo = 0;
		int _CntByLoanNo = 0;
		int _CntLnapEq321 = 0;
		int _CntLnapDiff321 = 0;
		BigDecimal _SumFirstFactAmt = BigDecimal.ZERO;
		BigDecimal _SumFactAmt = BigDecimal.ZERO;
		BigDecimal _SumBal = BigDecimal.ZERO;
		for (String brNo : totBrNoMap.keySet()) {
			Pteamapp_TOT tot = totBrNoMap.get(brNo);

			_CntById += tot.getCntById();
			_CntByCntrNo += tot.getCntByCntrNo();
			_CntByLoanNo += tot.getCntByLoanNo();
			_CntLnapEq321 += tot.getCntLnapEq321();
			_CntLnapDiff321 += tot.getCntLnapDiff321();
			_SumFirstFactAmt = _SumFirstFactAmt.add(tot.getSumFirstFactAmt());
			_SumFactAmt = _SumFactAmt.add(tot.getSumFactAmt());
			_SumBal = _SumBal.add(tot.getSumBal());
		}
		r.setCntById(_CntById);
		r.setCntByCntrNo(_CntByCntrNo);
		r.setCntByLoanNo(_CntByLoanNo);
		r.setCntLnapEq321(_CntLnapEq321);
		r.setCntLnapDiff321(_CntLnapDiff321);
		r.setSumFirstFactAmt(_SumFirstFactAmt);
		r.setSumFactAmt(_SumFactAmt);
		r.setSumBal(_SumBal);

		return r;
	}

	public static List<Pteamapp_LNF> convert_to_Pteamapp_LNF(
			List<Map<String, Object>> child_list) {
		List<Pteamapp_LNF> rows_list = new ArrayList<Pteamapp_LNF>();

		for (Map<String, Object> rowData_c : child_list) {
			Pteamapp_LNF vo = new Pteamapp_LNF();
			// ========
			vo.setLnf020_cust_id(Util.trim(MapUtils.getString(rowData_c,
					"LNF020_CUST_ID")));
			vo.setBrno(Util.trim(MapUtils.getString(rowData_c, "BRNO")));
			vo.setLnf020_contract(Util.trim(MapUtils.getString(rowData_c,
					"LNF020_CONTRACT")));
			vo.setLnf020_grp_cntrno(Util.trim(MapUtils.getString(rowData_c,
					"LNF020_GRP_CNTRNO")));
			vo.setRaw_lnf020_grp_cntrno(Util.trim(MapUtils.getString(rowData_c,
					"RAW_LNF020_GRP_CNTRNO")));
			vo.setFact_amt((BigDecimal) MapUtils.getObject(rowData_c,
					"FACT_AMT"));
			vo.setLnf030_loan_no(Util.trim(MapUtils.getString(rowData_c,
					"LNF030_LOAN_NO")));
			vo.setLnap(Util.trim(MapUtils.getString(rowData_c, "LNAP")));
			vo.setLnf030_charc_code(Util.trim(MapUtils.getString(rowData_c,
					"LNF030_CHARC_CODE")));
			vo.setLnf030_open_date((Date) MapUtils.getObject(rowData_c,
					"LNF030_OPEN_DATE"));
			vo.setLnf030_loan_date((Date) MapUtils.getObject(rowData_c,
					"LNF030_LOAN_DATE"));
			vo.setLoan_date_ym(Util.trim(MapUtils.getString(rowData_c,
					"LOAN_DATE_YM")));
			vo.setLnf030_1st_ln_amt((BigDecimal) MapUtils.getObject(rowData_c,
					"LNF030_1ST_LN_AMT"));
			vo.setLnf030_cancel_date((Date) MapUtils.getObject(rowData_c,
					"LNF030_CANCEL_DATE"));
			vo.setLoan_bal((BigDecimal) MapUtils.getObject(rowData_c,
					"LOAN_BAL"));
			vo.setLoan_bal_m((BigDecimal) MapUtils.getObject(rowData_c,
					"LOAN_BAL_M"));
			vo.setLoan_rate((BigDecimal) MapUtils.getObject(rowData_c,
					"LOAN_RATE"));
			vo.setLoan_rate_m((BigDecimal) MapUtils.getObject(rowData_c,
					"LOAN_RATE_M"));
			// LNF155_DATA_YM
			vo.setElf500_lotno(Util.trim(MapUtils.getString(rowData_c,
					"ELF500_LOTNO")));
			vo.setElf500_document_no(Util.trim(MapUtils.getString(rowData_c,
					"ELF500_DOCUMENT_NO")));
			vo.setElf447n_unid(Util.trim(MapUtils.getString(rowData_c,
					"ELF447N_UNID")));
			vo.setElf447n_project_no(Util.trim(MapUtils.getString(rowData_c,
					"ELF447N_PROJECT_NO")));
			// ELF447N_CURR
			vo.setFirst_fact_amt((BigDecimal) MapUtils.getObject(rowData_c,
					"FIRST_FACT_AMT"));
			// C_CUSTID
			// C_DUPNO
			vo.setCName(Util.trim(MapUtils.getString(rowData_c, "CNAME")));
			// ========
			vo.setElf506_722_flag(Util.trim(MapUtils.getString(rowData_c, "ELF506_722_FLAG")));
			rows_list.add(vo);
		}

		return rows_list;
	}

	public static Map<String, TreeMap<String, Pteamapp_TOT>> totGrpCntrNoMap(
			List<Pteamapp_LNF> rows_list, String dataPeriodFlag) {
		Map<String, TreeMap<String, Pteamapp_TOT>> totGrpCntrNoMap = new TreeMap<String, TreeMap<String, Pteamapp_TOT>>();

		Map<String, Set<String>> cat_grpcntrno_brno__custId = new HashMap<String, Set<String>>();
		Map<String, Set<String>> cat_grpcntrno_brno__cntrNo = new HashMap<String, Set<String>>();
		Map<String, Map<String, BigDecimal>> cat_grpcntrno_brno__cntrNo_firstFact = new HashMap<String, Map<String, BigDecimal>>();
		Map<String, Map<String, BigDecimal>> cat_grpcntrno_brno__cntrNo_Fact = new HashMap<String, Map<String, BigDecimal>>();
		for (Pteamapp_LNF vo : rows_list) {
			String vo_catkey = _build_catkey_Pteamapp_LNF(vo);
			if (true) {
				if (!cat_grpcntrno_brno__custId.containsKey(vo_catkey)) {
					cat_grpcntrno_brno__custId.put(vo_catkey,
							new HashSet<String>());
				}
				cat_grpcntrno_brno__custId.get(vo_catkey).add(
						vo.getLnf020_cust_id());
			}
			if (true) {
				if (!cat_grpcntrno_brno__cntrNo.containsKey(vo_catkey)) {
					cat_grpcntrno_brno__cntrNo.put(vo_catkey,
							new HashSet<String>());
				}
				cat_grpcntrno_brno__cntrNo.get(vo_catkey).add(
						vo.getLnf020_contract());
			}

			if (true) {
				if (!cat_grpcntrno_brno__cntrNo_firstFact
						.containsKey(vo_catkey)) {
					cat_grpcntrno_brno__cntrNo_firstFact.put(vo_catkey,
							new HashMap<String, BigDecimal>());
				}
				if (cat_grpcntrno_brno__cntrNo_firstFact.get(vo_catkey)
						.containsKey(vo.getLnf020_contract())) {
					// ok
				} else {
					cat_grpcntrno_brno__cntrNo_firstFact.get(vo_catkey).put(
							vo.getLnf020_contract(), vo.getFirst_fact_amt());
				}
			}
			if (true) {
				if (!cat_grpcntrno_brno__cntrNo_Fact.containsKey(vo_catkey)) {
					cat_grpcntrno_brno__cntrNo_Fact.put(vo_catkey,
							new HashMap<String, BigDecimal>());
				}
				if (cat_grpcntrno_brno__cntrNo_Fact.get(vo_catkey).containsKey(
						vo.getLnf020_contract())) {
					// ok
				} else {
					cat_grpcntrno_brno__cntrNo_Fact.get(vo_catkey).put(
							vo.getLnf020_contract(), vo.getFact_amt());
				}
			}
		}
		for (Pteamapp_LNF vo : rows_list) {
			String _grpCntrNo = vo.getLnf020_grp_cntrno();
			String _brNo = vo.getBrno();

			TreeMap<String, Pteamapp_TOT> totBrNoMap = null;
			if (totGrpCntrNoMap.containsKey(_grpCntrNo)) {
				totBrNoMap = totGrpCntrNoMap.get(_grpCntrNo);
			} else {
				totBrNoMap = new TreeMap<String, Pteamapp_TOT>();
			}
			if (true) {
				Pteamapp_TOT tot = null;
				if (totBrNoMap.containsKey(_brNo)) {
					tot = totBrNoMap.get(_brNo);
				} else {
					tot = new Pteamapp_TOT();
				}
				/*
				 * 未來, 若把 "已核准,未動用的資料" 加入 會有 custId, cntrNo, 但缺少 loanNo
				 */
				if (Util.isNotEmpty(Util.trim(vo.getLnf030_loan_no()))) {
					tot.setCntByLoanNo(tot.getCntByLoanNo() + 1);

					if (Util.equals("321", vo.getLnap())) {
						tot.setCntLnapEq321(tot.getCntLnapEq321() + 1);
					} else {
						tot.setCntLnapDiff321(tot.getCntLnapDiff321() + 1);
					}
				}

				// 後面再指定
				// this.sumFirstFactAmt = BigDecimal.ZERO;
				// this.sumFactAmt = BigDecimal.ZERO;
				BigDecimal bal = vo.fetch_bal(dataPeriodFlag);
				if (bal != null) {
					tot.setSumBal(tot.getSumBal().add(bal));
				}
				// ===================
				totBrNoMap.put(_brNo, tot);
			}
			totGrpCntrNoMap.put(_grpCntrNo, totBrNoMap);
		}

		for (String grpCntrNo : totGrpCntrNoMap.keySet()) {
			Map<String, Pteamapp_TOT> totBrNoMap = totGrpCntrNoMap
					.get(grpCntrNo);
			for (String brNo : totBrNoMap.keySet()) {
				Pteamapp_TOT tot = totBrNoMap.get(brNo);
				String vo_catkey = _build_catkey_Pteamapp_LNF(grpCntrNo, brNo);
				if (true) {
					Set<String> idSet = cat_grpcntrno_brno__custId
							.get(vo_catkey);
					tot.setCntById(idSet == null ? 0 : idSet.size());
				}

				if (true) {
					Set<String> cntrNoSet = cat_grpcntrno_brno__cntrNo
							.get(vo_catkey);
					tot.setCntByCntrNo(cntrNoSet == null ? 0 : cntrNoSet.size());
				}

				if (true) {
					Map<String, BigDecimal> cntrNo_firstFact_map = cat_grpcntrno_brno__cntrNo_firstFact
							.get(vo_catkey);
					Map<String, BigDecimal> cntrNo_fact_map = cat_grpcntrno_brno__cntrNo_Fact
							.get(vo_catkey);

					for (String cntrNo : cntrNo_firstFact_map.keySet()) {
						BigDecimal to_be_add = cntrNo_firstFact_map.get(cntrNo);
						if (to_be_add != null) {
							tot.setSumFirstFactAmt(tot.getSumFirstFactAmt()
									.add(to_be_add));
						}
					}
					for (String cntrNo : cntrNo_fact_map.keySet()) {
						BigDecimal to_be_add = cntrNo_fact_map.get(cntrNo);
						if (to_be_add != null) {
							tot.setSumFactAmt(tot.getSumFactAmt()
									.add(to_be_add));
						}
					}
				}
			}
		}

		return totGrpCntrNoMap;
	}

	public static boolean notAllowedNckdFlag_R98(String nckdflag) {
		// A-改期覆審
		if (inCollection(Util.trim(nckdflag), new String[] { "", "G", "H" })) {
			return false;
		}
		return true;
	}

	/**
	 * 若1個人有3個額度都有連續撥款, 只會覆審那1個人而已 => 用 id 去算「應覆審戶數」
	 */
	public static int cnt_CLS180R18_R0_byId(List<ELF516> elf516_list) {
		Set<String> set = new HashSet<String>();
		for (ELF516 elf516 : elf516_list) {
			set.add(LMSUtil.getCustKey_len10custId(
					Util.trim(elf516.getCustid()), Util.trim(elf516.getDupno())));
		}
		return set.size();
	}

	public static int get_ELF490B_cnt_for_top3() {
		return 999;
	}

	/** 傳入的參數應 in (6月,12月)， 再以此，去回傳「前一個週期」 */
	public static String get_previous_R96_base_ym(String yyyy_MM) {
		String tmp_dt = yyyy_MM + "-01";
		Date dt = CapDate.parseDate(tmp_dt);
		return StringUtils.substring(TWNDate.toAD(CapDate.addMonth(dt, -6)), 0,
				7);
	}

	/** 若 "傳入的參數年月" =2019-01或2019-02， 回傳2018-12 */
	public static String get_R96_base_ym(String yyyy_MM) {
		int mm = Util.parseInt(StringUtils.substring(yyyy_MM, 5, 7));

		String end_dataYM = "";
		if (mm >= 1 && mm <= 6) {
			int yyyy = Util.parseInt(StringUtils.substring(yyyy_MM, 0, 4)) - 1;
			end_dataYM = String.valueOf(yyyy) + "-12";
		} else {
			int yyyy = Util.parseInt(StringUtils.substring(yyyy_MM, 0, 4));
			end_dataYM = String.valueOf(yyyy) + "-06";
		}
		return end_dataYM;
	}

	/** 取得防杜代辦「覆審基準期間」的起迄 */
	public static String[] get_CLS180R18_period_byEndYM(String dataYM,
			int fmt_length) {
		// 半年
		String yyyy = StringUtils.substring(dataYM, 0, 4);
		String mm = StringUtils.substring(dataYM, 5, 7);

		String r_beg = dataYM;
		String r_end = dataYM;
		if (mm.equals("12")) {
			if (fmt_length == 7) {
				r_beg = yyyy + "-07";
				r_end = yyyy + "-12";
			} else {
				r_beg = yyyy + "-07-01";
				r_end = yyyy + "-12-31";
			}
		} else if (mm.equals("06")) {
			if (fmt_length == 7) {
				r_beg = yyyy + "-01";
				r_end = yyyy + "-06";
			} else {
				r_beg = yyyy + "-01-01";
				r_end = yyyy + "-06-30";
			}
		}
		return new String[] { r_beg, r_end };
	}

	public static String[] get_ELF490B_areaArr() {
		return new String[] { "931", "932", "933", "934", "935" };
	}

	public static String convert_ELF339_BRNO_AREA(String areaNo) {
		if (Util.equals("931", areaNo)) {
			return "1";
		} else if (Util.equals("932", areaNo)) {
			return "5";
		} else if (Util.equals("933", areaNo)) {
			return "6";
		} else if (Util.equals("934", areaNo)) {
			return "2";
		} else if (Util.equals("935", areaNo)) {
			return "3";
		}
		return areaNo;
	}

	public static boolean is_tp_brNo(String brNo) {
		boolean tpBr = false;
		if (true) {// 參考 gfnBranchOfTaipei
			if (CrsUtil.inCollection(brNo, new String[] { "005", "008", "010",
					"012", "015", "017", "019", "021", "027", "030", "031",
					"034", "036", "041", "042", "043" })) {
				tpBr = true;
			}
			// if(CrsUtil.inCollection(this.brNo, new
			// String[]{"046","048","051","055","056","057","067","069","070","074","201","007","202","206","208","210","216"})){
			// 208 是竹北
			if (CrsUtil.inCollection(brNo, new String[] { "046", "048", "051",
					"055", "056", "057", "067", "069", "070", "074", "149",
					"201", "007", "202", "206", "210", "216" })) {
				tpBr = true;
			}

			/*
			 * F-110-0156 , 業管處110/08/18電子簽核郵件(城北分行搬遷案帳務移轉辦理事項討論會會議紀錄)
			 * 城北(231)分行搬遷至林口。地址：由{台北市中山區}變更為{桃園市龜山區} 因「授信覆審作業須知」內的條文...台北市、
			 * 新北市營業單位敘作額度...以下，其餘地區營業單位敘作額度...以下 故將 231 的分行代碼，由{雙北地區}的清單排除
			 */
			// if(CrsUtil.inCollection(brNo, new
			// String[]{"219","226","229","231","234","235","237","238","240"})){
			if (CrsUtil.inCollection(brNo, new String[] { "219", "226", "229",
					"234", "235", "237", "238", "240" })) {
				tpBr = true;
			}
		}
		return tpBr;
	}

	public static boolean amt_over_threshold_3000WAN_1500WAN_by_brNo(
			boolean is_tp_brNo, BigDecimal sum) {
		if (is_tp_brNo) {
			return (sum.compareTo(CrsUtil.R1R2_IN_TPE_AMT_3000WAN) > 0);
		} else {
			return (sum.compareTo(CrsUtil.R1R2_NO_TPE_AMT_1500WAN) > 0);
		}
	}

	// ================================================
	/** 102:支存帳號 */
	public static boolean is_subj_in_102(String subjCode) {
		if (Util.equals("12400000", subjCode)) {
			return true;
		}
		return false;
	}

	/** 104:存摺存款帳號 */
	public static boolean is_subj_in_104(String subjCode) {
		if (Util.equals("12100200", subjCode)) {
			return true;
		}
		return false;
	}

	/**
	 * 102 12400000 透支［102,202：支存］　　　　　　　　　　　　　　　　　　 104 12100200 短期放款－存摺存款透支　　　
	 */
	public static boolean is_subj_in_102_104(String subjCode) {
		if (is_subj_in_102(subjCode)) {
			return true;
		}
		if (is_subj_in_104(subjCode)) {
			return true;
		}
		return false;
	}

	/**
	 * 　　　　　 202 12800000 擔保透支［102,202：支存］　　　　　　　　　　　　　　　　 204 12600200
	 * 短期擔保放款－存摺存款透支　　　
	 */
	public static boolean is_subj_in_202_204(String subjCode) {
		if (Util.equals("12800000", subjCode)) {
			return true;
		}
		if (Util.equals("12600200", subjCode)) {
			return true;
		}
		return false;
	}

	/**
	 * 　　 404 13500200 中期擔保放款－存摺存款透支　　　　
	 */
	public static boolean is_subj_in_404(String subjCode) {
		if (Util.equals("13500200", subjCode)) {
			return true;
		}
		return false;
	}
	
	/**
	 * 303 13100100 中期放款-一般中期放款
	 */
	public static boolean is_subj_in_303(String subjCode) {
		if (Util.equals("13100100", subjCode)) {
			return true;
		}
		return false;
	}

	// ================================================
	public static boolean is_subj_in_473_474_673_674(String subjCode) {
		if (is_subj_in_473_673(subjCode) || is_subj_in_474_674(subjCode)) {
			return true;
		}
		return false;
	}

	/**
	 * 473 13506200 中期房屋擔保放款－房屋購置　　　　　　　 673 14501500 長期房屋擔保放款　　　　　
	 */
	public static boolean is_subj_in_473_673(String subjCode) {
		if (Util.equals("13506200", subjCode)) {
			return true;
		}
		if (Util.equals("14501500", subjCode)) {
			return true;
		}
		return false;
	}

	/**
	 * 474 13506300 中期房屋擔保放款－房屋修繕　　　　　　　 674 14502000 長期房屋擔保放款－房屋修繕
	 */
	public static boolean is_subj_in_474_674(String subjCode) {
		if (Util.equals("13506300", subjCode)) {
			return true;
		}
		if (Util.equals("14502000", subjCode)) {
			return true;
		}
		return false;
	}

	/**
	 * 403 13500100 中期擔保放款　　　　　　　　　　　　　　 603 14501000 長期擔保放款　
	 */
	public static boolean is_subj_in_403_603(String subjCode) {
		if (Util.equals("13500100", subjCode)) {
			return true;
		}
		if (Util.equals("14501000", subjCode)) {
			return true;
		}
		return false;
	}

	// ================================================
	/**
	 * 203 12600100 短期擔保放款　　　　　　　　　　　　　　
	 */
	public static boolean is_subj_in_203(String subjCode) {
		if (Util.equals("12600100", subjCode)) {
			return true;
		}
		return false;
	}

	// ================================================
	/*
	 * 此 SQL 區分［純房貸］的［產品種類］有哪些 with t0 as ( select distinct prodkind, house_subj
	 * from (select tab.*, (case when subjcode in
	 * ('13506200','14501500','13506300', '14502000') then 'Y' else 'N' end) as
	 * house_subj from lms.c900m01b tab) t ) , t1 as (select distinct prodKind
	 * from t0) , t2 as (select t1.prodKind,t0_Y.house_subj as house_subj_Y
	 * ,t0_N.house_subj as house_subj_N from t1 left outer join t0 t0_Y on
	 * t1.prodKind=t0_Y.prodKind and t0_Y.house_subj='Y' left outer join t0 t0_N
	 * on t1.prodKind=t0_N.prodKind and t0_N.house_subj='N' ) select * from t2
	 * where house_subj_Y like '%' and prodKind like '%' order by prodKind
	 */
	public static boolean is_prodKind_justIn_473_474_673_674(String prodKind) {
		String[] rs_houseType = new String[] { "10", "11", "12", "13", "14",
				"15", "16", "17", "18", "19", "20", "21"
				// 921貸款專案含{503}, "22", "23", "24"
				, "25", "26"
				// 921貸款展延含{503}, "27"
				, "28"
				// 歡喜房貸{含403, 603的擔保放款}, "30", "31"
				, "35", "38", "39"
				// 已停用產品, "40", "41", "42", "43", "44", "45", "46", "47"
				// 已停用產品, "55"
				, "56", "57", "59", "63", "64", "65", "66" };
		return inCollection(prodKind, rs_houseType);
	}

	public static boolean is_house_loan(String subjCode, String prodKind,
			String l140s02a_modelKind) {
		if (is_subj_in_473_474_673_674(subjCode)) {
			return true;
		}
		// 921貸款專案(新購屋、重建、修繕) 及 歡喜房貸專案
		if (is_22(prodKind) || is_23(prodKind) || is_24(prodKind)
				|| is_30(prodKind) || is_31(prodKind)) {
			return true;
		}

		if (is_03(prodKind) && is_subj_in_403_603(subjCode)
				&& Util.equals("1", l140s02a_modelKind)) {
			return true;
		}
		// ~~~~~~~~~~~~~~
		return false;
	}

	/*
	 * select * from lms.c900m01d where subjcode in ('12400000', '12100100',
	 * '12100200', '13100100', '14100500', '13103000', '13101000') order by
	 * subjcode2
	 * 
	 * select * from lms.c900m01d where subjcode2 like '1%' or subjcode2 like
	 * '3%' or subjcode2 like '5%' order by subjcode2
	 */
	public static boolean is_not_guarantee_loan_exclude_371_571(String subjCode) {
		if (CrsUtil.inCollection(subjCode, new String[] { "12400000",
				"12100100", "12100200", "13100100", "14100500", "13103000",
				"13101000" })) {
			return true;
		}
		return false;
	}

	public static Set<String> get_prodKind_exclude_property(
			List<L140S02A> l140s02as, String[] arr_excludeProp) {
		Set<String> r = new HashSet<String>();
		for (L140S02A l140s02a : l140s02as) {
			if (CrsUtil.inCollection(l140s02a.getProperty(), arr_excludeProp)) {
				continue;
			}
			r.add(l140s02a.getProdKind());
		}
		return r;
	}

	public static Set<String> get_prodKind_exclude_property7_8(
			List<L140S02A> l140s02as) {
		return get_prodKind_exclude_property(l140s02as, new String[] {
				UtilConstants.Cntrdoc.Property.不變,
				UtilConstants.Cntrdoc.Property.取消 });
	}

	public static Set<String> get_prodKind_exclude_property8(
			List<L140S02A> l140s02as) {
		return get_prodKind_exclude_property(l140s02as,
				new String[] { UtilConstants.Cntrdoc.Property.取消 });
	}

	public static boolean hasLaaCertData(C101S01E c101s01e) {
		return hasLaaCertData(c101s01e.getLaaYear(), c101s01e.getLaaWord(),
				c101s01e.getLaaNo());
	}

	public static boolean hasLaaCertData(String laaYear, String laaWord,
			String laaNo) {
		if (Util.isNotEmpty(Util.trim(laaYear))
				&& Util.isNotEmpty(Util.trim(laaWord))
				&& Util.isNotEmpty(Util.trim(laaNo))) {
			return true;
		}
		return false;
	}

	public static boolean hasLaaOfficeData(C101S01E c101s01e) {
		return hasLaaOfficeData(c101s01e.getLaaOffice());
	}

	public static boolean hasLaaOfficeData(String laaOffice) {
		if (Util.isNotEmpty(Util.trim(laaOffice))) {
			return true;
		}
		return false;
	}

	public static boolean is_fix_rate(L140S02D l140s02d) {
		if (l140s02d != null && Util.equals(l140s02d.getIsUseBox(), "Y")) {
			if (CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType())
					&& Util.equals("C01", l140s02d.getRateUserType())) {
				return true;
			} else {
				// 機動 或 定期浮動
			}
		}
		return false;
	}

	// J-110-0308 覆審考核表
	public static final String[] c241m01gCol = { "paItem01", "paItem02",
			"paItem03", "paItem04", "paItem05", "paItem06", "paItem07",
			"paItem08" };
	public static final String[] c241m01gColYn = { "paItem01", "paItem02",
			"paItem06", "paItem07", "paItem08" };
	public static final String[] c241m01gColCnt = { "paItem03", "paItem04",
			"paItem05" };
	// J-110-0308 覆審考核表V11207
	public static final String[] c241m01gColV11207 = { "paItem01", "paItem02",
			"paItem03", "paItem04", "paItem05", "paItem06", "paItem07",
			"paItem08", "paItem09" };
	public static final String[] c241m01gColYnV11207 = { "paItem01", "paItem06",
			"paItem07", "paItem08", "paItem09" };
	public static final String[] c241m01gColCntV11207 = { "paItem02", "paItem03",
			"paItem04", "paItem05" };

	public static List<L140M01A> l140m01a_excludeDocStatus060(
			List<L140M01A> src_list) {
		List<L140M01A> list = new ArrayList<L140M01A>();
		for (L140M01A l140m01a : src_list) {
			if (Util.equals("060", l140m01a.getDocStatus())) {
				continue;
			}
			list.add(l140m01a);
		}
		return list;
	}

	public static List<L140M01A> l140m01a_in_idDup(List<L140M01A> src_list,
			String target_idDup) {
		List<L140M01A> list = new ArrayList<L140M01A>();
		for (L140M01A l140m01a : src_list) {
			if (Util.equals(target_idDup, LMSUtil.getCustKey_len10custId(
					l140m01a.getCustId(), l140m01a.getDupNo()))) {
				list.add(l140m01a);
			}
		}
		return list;
	}

	public static Map<String, List<L140M01A>> group_by_idDup_L140M01A(
			List<L140M01A> list) {
		Map<String, List<L140M01A>> idDup_L140M01A_map = new HashMap<String, List<L140M01A>>();
		for (L140M01A l140m01a : list) {
			String idDup = LMSUtil.getCustKey_len10custId(l140m01a.getCustId(),
					l140m01a.getDupNo());
			if (!idDup_L140M01A_map.containsKey(idDup)) {
				idDup_L140M01A_map.put(idDup, new ArrayList<L140M01A>());
			}
			idDup_L140M01A_map.get(idDup).add(l140m01a);
		}
		return idDup_L140M01A_map;
	}

	public static Map<String, List<C160M01B>> group_by_idDup_C160M01B(
			List<C160M01B> list) {
		Map<String, List<C160M01B>> idDup_C160M01B_map = new HashMap<String, List<C160M01B>>();
		for (C160M01B c160m01b : list) {
			String idDup = LMSUtil.getCustKey_len10custId(c160m01b.getCustId(),
					c160m01b.getDupNo());
			if (!idDup_C160M01B_map.containsKey(idDup)) {
				idDup_C160M01B_map.put(idDup, new ArrayList<C160M01B>());
			}
			idDup_C160M01B_map.get(idDup).add(c160m01b);
		}
		return idDup_C160M01B_map;
	}

	public static Set<String> get_s01q_out_authority_grade_6_to_10() {
		HashSet<String> set = new HashSet<String>();
		for (int i = 6; i <= 10; i++) {
			set.add(String.valueOf(i));
		}
		return set;
	}

	public static boolean isRule13_match(String prodKind, Date lnf030_loan_date) {
		// 108.12.30 兆銀消金字第1080000234號 開辦卡友信貸
		if (is_08(prodKind)) {
			if (isNull_or_ZeroDate(lnf030_loan_date)) {
				return true;
			}
			if (isNOT_null_and_NOTZeroDate(lnf030_loan_date)
					&& LMSUtil.cmpDate(lnf030_loan_date, ">=",
							CapDate.parseDate("2019-12-30"))) {
				return true;
			}
		}
		if (is_71(prodKind)) {
			return true;
		}
		return false;
	}

	/**
	 * 詢問 授審處 提案人，以{某年07-01 ~ 至 次年06-30} <br/>
	 * input[2021-01-01] => 2020-07-01 , 2021-06-30 <br/>
	 * input[2021-06-30] => 2020-07-01 , 2021-06-30 <br/>
	 * input[2021-07-01] => 2021-07-01 , 2022-06-30 <br/>
	 * input[2021-07-02] => 2021-07-01 , 2022-06-30 <br/>
	 * input[2021-12-31] => 2021-07-01 , 2022-06-30
	 */
	public static String[] get_R95_1_done_period(String retrial_dt) {

		int retrial_year = Util.parseInt(StringUtils
				.substring(retrial_dt, 0, 4));
		int rule95_beg0701_year = retrial_year;
		if (Util.parseInt(StringUtils.substring(retrial_dt, 5, 7)) <= 6) { // 若是1~6月
			rule95_beg0701_year = retrial_year - 1;
		}
		String donePeriod_beg = String.valueOf(rule95_beg0701_year) + "-07-01";
		String donePeriod_end = String.valueOf(rule95_beg0701_year + 1)
				+ "-06-30";

		return new String[] { donePeriod_beg, donePeriod_end };
	}

	/**
	 * input[2021-01-01] => 2020-06-30 <br/>
	 * input[2021-06-30] => 2020-06-30 <br/>
	 * input[2021-07-01] => 2021-06-30 <br/>
	 * input[2021-07-02] => 2021-06-30 <br/>
	 * input[2021-12-31] => 2021-06-30
	 */
	public static String get_R95_1_baseDate(String str_date) {
		int yyyy = Util.parseInt(StringUtils.substring(str_date, 0, 4));
		if (Util.parseInt(StringUtils.substring(str_date, 5, 7)) <= 6) { // 若是1~6月
			return (String.valueOf(yyyy - 1) + "-06-30");
		} else {
			return (String.valueOf(yyyy) + "-06-30");
		}
	}

	public static Date get_R13_begDate(Date dt) {
		// 讓 input=yyyy-12-31，回傳值是 yyyy-01-01
		return CapDate.shiftDays(CapDate.addMonth(dt, -12), 1);
	}

	public static Integer calc_c240m01c_doneRate(int denominatorCnt, int doneCnt) {
		int plotOfReview = denominatorCnt;
		int samplingCount = doneCnt;
		if (samplingCount > 0 && plotOfReview > 0) {
			// 先乘100再除分母, 不然會算出0
			String sRate = CapMath.divide(
					CapMath.multiply(String.valueOf(samplingCount), "100"),
					String.valueOf(plotOfReview));
			return (Util.parseInt(sRate));
		}
		return null;
	}

	public static Set<String> get_upElf491c_rule_no_set_simple() {
		Set<String> upElf491c_rule_no_set = new HashSet<String>();
		upElf491c_rule_no_set.add(CrsUtil.R8_1);
		upElf491c_rule_no_set.add(CrsUtil.R95_1);
		upElf491c_rule_no_set.add(CrsUtil.R13);
		return upElf491c_rule_no_set;
	}

	public static Set<String> get_upElf491c_rule_no_set_R1R2S() {
		Set<String> upElf491c_rule_no_set = new HashSet<String>();
		/*
		 * 若要分的很細，應針對{中長期額度、門檻以下}、{短期額度、門檻以下}有各自的 Rule。例如：1-1, 2-1 但時間太趕 先編一個
		 * Rule代號同時代表{長期、短期}
		 */
		upElf491c_rule_no_set.add(CrsUtil.R1_1___R2_1);
		return upElf491c_rule_no_set;
	}

	public static Set<String> get_upElf491c_rule_no_set_R14() {
		Set<String> upElf491c_rule_no_set = new HashSet<String>();
		upElf491c_rule_no_set.add(CrsUtil.R14);
		return upElf491c_rule_no_set;
	}

	public static Date get_J_110_0309_extendCrDate(String brNo, Date crDate) {
		if (CrsUtil.isNull_or_ZeroDate(crDate)) {
			return crDate;
		}
		// ==================
		if (LMSUtil.cmpDate(crDate, ">=", CapDate.parseDate("2022-08-01"))) {
			return crDate;
		} else if (LMSUtil.cmpDate(crDate, ">=",
				CapDate.parseDate("2022-07-01"))
				&& LMSUtil.cmpDate(crDate, "<=",
						CapDate.parseDate("2022-07-31"))) {
			// 延後1個月
			return CapDate.parseDate("2022-08-31");
		} else if (LMSUtil.cmpDate(crDate, ">=",
				CapDate.parseDate("2022-06-01"))
				&& LMSUtil.cmpDate(crDate, "<=",
						CapDate.parseDate("2022-06-30"))) {
			// 延後2個月
			return CapDate.parseDate("2022-08-31");
		} else if (LMSUtil.cmpDate(crDate, ">=",
				CapDate.parseDate("2022-01-01"))
				&& LMSUtil.cmpDate(crDate, "<=",
						CapDate.parseDate("2022-05-31"))) {
			// 延後3個月
			return get_month_last_date(CapDate.addMonth(crDate, 3));
		} else if (LMSUtil.cmpDate(crDate, ">=",
				CapDate.parseDate("2021-05-01"))
				&& LMSUtil.cmpDate(crDate, "<=",
						CapDate.parseDate("2021-12-31"))) {
			int addedMonth = 3;
			// if(Util.equals("079", brNo) || Util.equals("023", brNo)){
			// addedMonth = 6;
			// }
			return get_month_last_date(CapDate.addMonth(crDate, addedMonth));
		} else {
			return crDate;
		}
	}

	public static boolean showC241M01G(MegaSSOUserDetails user) {
		if (user.getSsoUnitNo().startsWith("9")) { // J-110-0308 分行及三大部隱藏「考評表」頁籤
			return true;
		}
		return false;
	}
}
