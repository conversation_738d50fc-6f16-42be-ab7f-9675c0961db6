/* 
 * L820M01S.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 以房養老綜合資訊檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L820M01S", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L820M01S extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 資料類型<p/>
	 * 5:行內_身分證驗證<br/>
	 *  6: RPA受監護輔助宣告查詢
	 */
	@Size(max=1)
	@Column(name="DATATYPE", length=1, columnDefinition="CHAR(1)")
	private String dataType;

	/** 
	 * 資料狀態<p/>
	 * 全部為0代表正常, 任一碼為1代表資料異常
	 */
	@Size(max=10)
	@Column(name="DATASTATUS", length=10, columnDefinition="VARCHAR(10)")
	private String dataStatus;

	/** 報表檔案 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="REPORTFILE", columnDefinition="BLOB")
	private byte[] reportFile;

	/** 檔案序號 **/
	@Size(max=1)
	@Column(name="FILESEQ", length=1, columnDefinition="CHAR(1)")
	private String fileSeq;

	/** 資料建立時間 **/
	@Column(name="DATACREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp dataCreateTime;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 報表檔案存檔類型<p/>
	 * J:JSON
	 */
	@Size(max=1)
	@Column(name="REPORTFILETYPE", length=1, columnDefinition="CHAR(1)")
	private String reportFileType;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得資料類型<p/>
	 * 5:行內_身分證驗證<br/>
	 *  6: RPA受監護輔助宣告查詢
	 */
	public String getDataType() {
		return this.dataType;
	}
	/**
	 *  設定資料類型<p/>
	 *  5:行內_身分證驗證<br/>
	 *  6: RPA受監護輔助宣告查詢
	 **/
	public void setDataType(String value) {
		this.dataType = value;
	}

	/** 
	 * 取得資料狀態<p/>
	 * 全部為0代表正常, 任一碼為1代表資料異常
	 */
	public String getDataStatus() {
		return this.dataStatus;
	}
	/**
	 *  設定資料狀態<p/>
	 *  全部為0代表正常, 任一碼為1代表資料異常
	 **/
	public void setDataStatus(String value) {
		this.dataStatus = value;
	}

	/** 取得報表檔案 **/
	public byte[] getReportFile() {
		return this.reportFile;
	}
	/** 設定報表檔案 **/
	public void setReportFile(byte[] value) {
		this.reportFile = value;
	}

	/** 取得檔案序號 **/
	public String getFileSeq() {
		return this.fileSeq;
	}
	/** 設定檔案序號 **/
	public void setFileSeq(String value) {
		this.fileSeq = value;
	}

	/** 取得資料建立時間 **/
	public Timestamp getDataCreateTime() {
		return this.dataCreateTime;
	}
	/** 設定資料建立時間 **/
	public void setDataCreateTime(Timestamp value) {
		this.dataCreateTime = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得報表檔案存檔類型<p/>
	 * J:JSON
	 */
	public String getReportFileType() {
		return this.reportFileType;
	}
	/**
	 *  設定報表檔案存檔類型<p/>
	 *  J:JSON
	 **/
	public void setReportFileType(String value) {
		this.reportFileType = value;
	}
}
