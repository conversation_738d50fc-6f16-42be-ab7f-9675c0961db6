/* 
 * LMS1115FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.lms.model.L120M01A;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 授信簽報書(個金授權內) FormHandler
 * </pre>
 * 
 * @since 2011/8/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/8/6,<PERSON>,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1115formhandler")
@DomainClass(L120M01A.class)
public class LMS1115M01FormHandler extends LMSM01FormHandler {
	/**
	 * <pre>
	 * 查詢銀行法及金控法44 45條(個金)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getRlt2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getRlt2(params);
	}

	/**
	 * 查詢主從債務人是否有資料(個金)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getSel(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getSel(params);
	}

	/**
	 * <pre>
	 * 新增借款人主檔(個金)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addBorrowMain2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_addBorrowMain2(params);
	}

	/**
	 * <pre>
	 * 刪除-(個金)(借款人資料)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteBorrowMain2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_deleteBorrowMain2(params);
	}

	/**
	 * 引進借款人資料(個金)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getCustData3(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getCustData3(params);
	}

	/**
	 * 引進借款人配偶資料(個金)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getCustData3m(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getCustData3m(params);
	}

	/**
	 * 設定使用者所選擇的申貸戶資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult setCustData3(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_setCustData3(params);
	}

	/**
	 * <pre>
	 * 查詢-個金(借款人標籤列)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBorrow2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_queryBorrow2(params);
	}

	/**
	 * <pre>
	 * 儲存-個金(全部借款人內容)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveBorrow2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_saveBorrow2(params);
	}

	/**
	 * 儲存配偶欄位(同本案借保人)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveCouple(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_saveCouple(params);
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult prepareImportL120S01m(PageParameters params)
		throws CapException {
		return super._LMSM01FormHandler_prepareImportL120S01m(params);
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120S01m_date(PageParameters params)
		throws CapException {
		return super._LMSM01FormHandler_queryL120S01m_date(params);
	}
	
	/**
	 * 儲存簽章欄(授管處)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContentF2(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_saveSignContentF2(params);
	}
	
	
	//取得模型版本
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getVarverFormC121M01A(PageParameters params)
			throws CapException {
		return super.getVarverFormC121M01A(params);
	}
	
	
	
}