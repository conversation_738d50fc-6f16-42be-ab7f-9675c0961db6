package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L170M01A;

/** 覆審報告表主檔 **/
@Repository
public class L170M01ADaoImpl extends LMSJpaDao<L170M01A, String> implements
		L170M01ADao {

	@Override
	public L170M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L170M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public L170M01A findByUniqueKey(String mainId, String custId, String dupNo,
			String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		return findUniqueOrNone(search);
	}

	@Override
	public L170M01A findByUniqueKey2(String custId, String dupNo,
			String branch, String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", branch);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		return findUniqueOrNone(search);
	}

	@Override
	public L170M01A findByUniqueKey3(String custId, String dupNo,
			String ownBrId, String docStatus, String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);

		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}

		// Query query = getEntityManager().createNamedQuery(

		// "L170M01A.selDocStatus");
		// query.setParameter("custId", custId);
		// query.setParameter("dupNo", dupNo);
		// query.setParameter("ownBrId", ownBrId);
		// query.setParameter("docStatus", docStatus);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L170M01A> findByCustIdDupId(String custId, String DupNo,
			String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		List<L170M01A> list = createQuery(L170M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L170M01A findByPidCustIdDup(String pid, String custId, String dupNo,
			String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		return findUniqueOrNone(search);
	}

	@Override
	public List<L170M01A> findByPid(String pid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	@Override
	public List<L170M01A> findByPid(String pid, String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

}