package com.mega.eloan.lms.lrs.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.dao.L170M01BDao;
import com.mega.eloan.lms.dao.L170M01CDao;
import com.mega.eloan.lms.dao.L170M01DDao;
import com.mega.eloan.lms.dao.L170M01EDao;
import com.mega.eloan.lms.dao.L170M01FDao;
import com.mega.eloan.lms.dao.L170M01GDao;
import com.mega.eloan.lms.dao.L170M01IDao;
import com.mega.eloan.lms.lrs.panels.LMS1705S03Panel;
import com.mega.eloan.lms.lrs.panels.LMS1705S04Panel;
import com.mega.eloan.lms.lrs.service.LMS1705Service;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01C;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L170M01G;
import com.mega.eloan.lms.model.L170M01I;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 產生簽報書PDF
 * 
 * <AUTHOR>
 * 
 */
@Service("lms1705r01rptservice")
public class LMS1705R01RptServiceImpl extends AbstractFormHandler implements
		FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1705R01RptServiceImpl.class);

	@Resource
	UserInfoService userInfoService;

	@Resource
	L170M01ADao l170m01aDao;

	@Resource
	L170M01BDao l170m01bDao;

	@Resource
	L170M01CDao l170m01cDao;

	@Resource
	L170M01DDao l170m01dDao;

	@Resource
	L170M01EDao l170m01eDao;

	@Resource
	L170M01FDao l170m01fDao;

	@Resource
	L170M01GDao l170m01gDao;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	BranchService branch;

	@Resource
	LMSService lmsService;

	@Resource
	RetrialService retrialService;

	@Resource
	LMS1705Service lms1705service;

	@Resource
	L170M01IDao l170m01iDao;

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 */
	public OutputStream generateReport(PageParameters params) throws Exception {
		String mainId = params.getString("mainId");
		String rptOid = Util.nullToSpace(params.getString("rptOid"));
		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		List<InputStream> list = new LinkedList<InputStream>();
		String[] dataSplit = rptOid.split("\\|");
		OutputStream outputStream = null;
		Locale locale = null;
		Properties propEloanPage = null;
		int subLine = 7;
		Properties rptProperties = null;
		locale = LMSUtil.getLocale();
		rptProperties = MessageBundleScriptCreator
				.getComponentResource(LMS1705R01RptServiceImpl.class);
		propEloanPage = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		for (String temp : dataSplit) {
			outputStream = null;
			String type = temp.split("\\^")[0];
			// String oid = temp.split("\\^")[1];

			String caseBrId = "";
			L170M01A l170m01a = l170m01aDao.findByMainId(mainId);
			if (l170m01a != null) {
				caseBrId = l170m01a.getOwnBrId();
			} else {
				caseBrId = user.getUnitNo();
			}

			String logoPath = lmsService.getLogoShowPath(
					UtilConstants.RPTPicType.兆豐LOGO, "00", caseBrId);

			rptProperties.setProperty("LOGOSHOW", logoPath);

			if ("R01".equals(type)) {
				if (Util.equals("", Util.nullToSpace(l170m01a.getRptId()))) {
					outputStream = this.genLMS1705R01(mainId, locale,
							rptProperties);
					subLine = 7;
					pdfNameMap.put(
							new ByteArrayInputStream(
									((ByteArrayOutputStream) outputStream)
											.toByteArray()), subLine);
				} else {
					subLine = 13; // J-107-0128海外改格式
					outputStream = this.genLMS1705R01V2(mainId, locale,
							rptProperties);
					if (outputStream != null) {
						pdfNameMap.put(
								new ByteArrayInputStream(
										((ByteArrayOutputStream) outputStream)
												.toByteArray()), subLine);
					} else {
						pdfNameMap.put(null, subLine);
					}

					outputStream = this.genLMS1705R03(mainId, locale,
							rptProperties);
					if (outputStream != null) {
						pdfNameMap.put(
								new ByteArrayInputStream(
										((ByteArrayOutputStream) outputStream)
												.toByteArray()), subLine);
					}

					// J-108-0260 海外覆審檢視表
					outputStream = this.genLMS1705R04(mainId, locale,
							rptProperties);
					if (outputStream != null) {
						pdfNameMap.put(
								new ByteArrayInputStream(
										((ByteArrayOutputStream) outputStream)
												.toByteArray()), subLine);
					}
				}
			} else if ("R02".equals(type)) {
				outputStream = this
						.genLMS1705R02(mainId, locale, rptProperties);
				subLine = 7;
				pdfNameMap.put(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()),
						subLine);
			}
		}
		if (pdfNameMap != null && pdfNameMap.size() > 0) {
			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
					propEloanPage.getProperty("PaginationText"), true, locale,
					subLine);
			list.add(new ByteArrayInputStream(
					((ByteArrayOutputStream) outputStream).toByteArray()));
		}
		outputStream = new ByteArrayOutputStream();
		PdfTools.mergeReWritePagePdf(list, outputStream);
		return outputStream;
	}

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(com.iisigroup.cap.component.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params) throws Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 產生LMS1205R01的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @return outputstream outputstream
	 * @throws Exception
	 */
	public OutputStream genLMS1705R01(String mainId, Locale locale,
			Properties rptProperties) throws Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		ReportGenerator generator = new ReportGenerator(
				"report/lrs/LMS1705R01_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;

		L170M01A l170m01a = null;
		L170M01C l170m01c = null;
		List<L170M01D> l170m01dList = null;
		List<L170M01E> l170m01eList = null;
		List<L170M01E> exl170m01eList = null;
		L170M01F l170m01f = null;
		List<L170M01B> l170m01bList = null;
		List<L170M01G> l170m01gList = null;
		String branchName = null;
		Map<String, String> crdTypeMap = null;
		Map<String, String> crdType2Map = null;
		Map<String, String> unitMap = null;
		Map<String, String> yesNoMap = null;
		Map<String, String> typCdMap = null;
		Map<String, String> subItemMap = null;
		try {
			l170m01a = l170m01aDao.findByMainId(mainId);
			l170m01c = l170m01cDao.findByMainId(mainId);
			l170m01dList = l170m01dDao.findByMainId(mainId);
			l170m01eList = l170m01eDao.findByMainId(mainId, "T");
			exl170m01eList = l170m01eDao.findByMainId(mainId, "L");
			l170m01f = l170m01fDao.findByMainId(mainId);
			l170m01bList = l170m01bDao.findByMainId(mainId);
			l170m01gList = l170m01gDao.findByMainId(mainId);
			// l170m01gList = new LinkedList<L170M01G>();
			crdTypeMap = codetypeservice.findByCodeType("CRDType",
					locale.toString());
			crdType2Map = codetypeservice.findByCodeType("lms1705s01_crdType2",
					locale.toString());
			unitMap = codetypeservice.findByCodeType("lms1205s01_Unit",
					locale.toString());

			unitMap = this.replaceDollar(unitMap);

			yesNoMap = codetypeservice.findByCodeType("Common_YesNo",
					locale.toString());
			typCdMap = codetypeservice.findByCodeType("TypCd",
					locale.toString());
			subItemMap = codetypeservice.findByCodeType("lms1405m01_SubItem",
					locale.toString());

			if (subItemMap == null)
				subItemMap = new LinkedHashMap<String, String>();
			if (crdType2Map == null)
				crdType2Map = new LinkedHashMap<String, String>();
			if (crdTypeMap == null)
				crdTypeMap = new LinkedHashMap<String, String>();
			if (unitMap == null)
				unitMap = new LinkedHashMap<String, String>();
			if (yesNoMap == null)
				yesNoMap = new LinkedHashMap<String, String>();
			if (typCdMap == null)
				typCdMap = new LinkedHashMap<String, String>();

			branchName = branch.getBranchName(Util.nullToSpace(l170m01a
					.getOwnBrId()));

			String logoPath = rptProperties.getProperty("LOGOSHOW");
			rptVariableMap.put("LOGOSHOW", logoPath);

			// 分行名稱
			rptVariableMap.put("BRANCHNAME", Util.nullToSpace(branchName));
			// J-107-0128海外改格式
			String ver = Util.nullToSpace(l170m01a.getRptId());
			rptVariableMap = this.setL170M01AData(rptVariableMap, l170m01a,
					yesNoMap, typCdMap, rptProperties);
			rptVariableMap = this
					.setL170M01EData(rptVariableMap, l170m01eList, crdTypeMap,
							crdType2Map, ver, rptProperties, exl170m01eList);
			rptVariableMap = this.setL170M01CData(rptVariableMap, l170m01c,
					unitMap, ver);

			// 列印走吃HTML檔案
			rptVariableMap = this.setL170M01DListDataHtml(l170m01a,
					rptVariableMap, l170m01dList, rptProperties);
			/*
			 * if (Util.equals(LMSUtil.getLocale(), "en")) {
			 * //英文版列印走吃HTML檔案，因為項目英文會太長造成疊字 rptVariableMap =
			 * this.setL170M01DListDataHtml(rptVariableMap, l170m01dList,
			 * rptProperties); }else{ rptVariableMap =
			 * this.setL170M01DListData(rptVariableMap, l170m01dList,
			 * rptProperties); }
			 */

			titleRows = this.setL170M01BListData(titleRows, l170m01bList, true,
					yesNoMap, subItemMap, rptProperties);
			rptVariableMap = this.setL170M01FData(rptVariableMap, l170m01f,
					yesNoMap, rptProperties, ver);
			rptVariableMap = this.setL170M01GData(rptVariableMap, l170m01gList,
					locale);
			// generator.setLang(java.util.Locale.TAIWAN);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			// generator.setReportFile("D:/test_Mega_Report/test/LMS1705R01_zh_TW.rpt");
			// generator.setTestMethod(true);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (crdTypeMap != null) {
				crdTypeMap.clear();
			}
			if (unitMap != null) {
				unitMap.clear();
			}
			if (crdType2Map != null) {
				crdType2Map.clear();
			}
			if (typCdMap != null) {
				typCdMap.clear();
			}
			if (subItemMap != null) {
				subItemMap.clear();
			}
		}

		return outputStream;
	}

	/**
	 * 產生LMS1705R02的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @return outputstream outputstream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public OutputStream genLMS1705R02(String mainId, Locale locale,
			Properties rptProperties) throws FileNotFoundException,
			IOException, Exception {
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		ReportGenerator generator = new ReportGenerator(
				"report/lrs/LMS1705R02_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;

		L170M01A l170m01a = null;
		List<L170M01B> l170m01bList = null;
		String branchName = null;
		Map<String, String> unitMap = null;
		Map<String, String> yesNoMap = null;
		Map<String, String> typCdMap = null;
		Map<String, String> subItemMap = null;

		try {
			l170m01a = l170m01aDao.findByMainId(mainId);
			l170m01bList = l170m01bDao.findByMainId(mainId);
			unitMap = codetypeservice.findByCodeType("lms1205s01_Unit");
			unitMap = this.replaceDollar(unitMap);
			yesNoMap = codetypeservice.findByCodeType("Common_YesNo");
			typCdMap = codetypeservice.findByCodeType("TypCd",
					locale.toString());
			subItemMap = codetypeservice.findByCodeType("lms1405m01_SubItem",
					locale.toString());

			if (subItemMap == null)
				subItemMap = new LinkedHashMap<String, String>();
			if (unitMap == null)
				unitMap = new LinkedHashMap<String, String>();
			if (yesNoMap == null)
				yesNoMap = new LinkedHashMap<String, String>();
			if (typCdMap == null)
				typCdMap = new LinkedHashMap<String, String>();

			branchName = branch.getBranchName(Util.nullToSpace(l170m01a
					.getOwnBrId()));

			String logoPath = rptProperties.getProperty("LOGOSHOW");
			rptVariableMap.put("LOGOSHOW", logoPath);

			// 分行名稱
			rptVariableMap.put("BRANCHNAME", Util.nullToSpace(branchName));
			rptVariableMap = this.setL170M01AData(rptVariableMap, l170m01a,
					yesNoMap, typCdMap, rptProperties);
			titleRows = this.setL170M01BListData(titleRows, l170m01bList,
					false, yesNoMap, subItemMap, rptProperties);
			// rptVariableMap = this.setL170M01CData(rptVariableMap,
			// l170m01c,unitMap);
			// generator.setLang(java.util.Locale.TAIWAN);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			// generator.setReportFile("report/lms/LMS1205R01_zh_TW.rpt");
			// generator.setTestMethod(true);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (unitMap != null) {
				unitMap.clear();
			}
			if (yesNoMap != null) {
				yesNoMap.clear();
			}
			if (subItemMap != null) {
				subItemMap.clear();
			}
			if (typCdMap != null) {
				typCdMap.clear();
			}

		}

		return outputStream;
	}

	/**
	 * 塞入變數MAP資料使用(L170M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param L170M01A
	 *            L170M01A資料資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL170M01AData(
			Map<String, String> rptVariableMap, L170M01A l170m01a,
			Map<String, String> yesNoMap, Map<String, String> typCdMap,
			Properties prop) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());

		BigDecimal thu = new BigDecimal(1000);
		String ver = Util.nullToSpace(l170m01a.getRptId()); // J-107-0128海外改格式
		rptVariableMap.put("L170M01A.RETRIALDATE",
				Util.nullToSpace(TWNDate.toAD(l170m01a.getRetrialDate())));
		rptVariableMap.put("L170M01A.PROJECTSEQ",
				Util.nullToSpace(l170m01a.getProjectSeq()));
		rptVariableMap.put("L170M01A.PROJECTNO",
				Util.nullToSpace(l170m01a.getProjectNo()));
		if (Util.equals("", ver)) {
			rptVariableMap.put("L170M01A.LASTRETRALDATE", Util
					.nullToSpace(TWNDate.toAD(l170m01a.getLastRetrialDate())));
		} else {
			if (Util.equals(CapDate.ZERO_DATE,
					TWNDate.toAD(l170m01a.getLastRetrialDate()))
					|| l170m01a.getLastRetrialDate() == null) {
				rptVariableMap.put("L170M01A.LASTRETRIALDATE",
						prop.getProperty("RPTSHOW.NO")); // 無
			} else {
				rptVariableMap.put("L170M01A.LASTRETRIALDATE",
						Util.nullToSpace(TWNDate.toAD(l170m01a
								.getLastRetrialDate())));
			}
		}
		rptVariableMap.put("L170M01A.CHAIRMAN",
				Util.nullToSpace(l170m01a.getChairman()));
		if (Util.equals("", ver)) {
			rptVariableMap.put("L170M01A.TRADETYPE",
					Util.nullToSpace(l170m01a.getTradeType()));
		} else {
			rptVariableMap.put("L170M01A.TRADETYPE",
					Util.nullToSpace(l170m01a.getTradeType()));
		}
		// J-111-0326 海外覆審作業系統改良第一階段： 10. 加行業別代碼
		String busCd_bussKind = LrsUtil.get_s01_busCd_bussKind(l170m01a);
		rptVariableMap.put("L170M01A.BUSCD", Util.nullToSpace(busCd_bussKind));

		rptVariableMap.put(
				"L170M01A.RLTGUARANTOR",
				Util.isNotEmpty(l170m01a.getRltGuarantor()) ? Util
						.nullToSpace(l170m01a.getRltGuarantor()) : prop
						.getProperty("RPTSHOW.NO"));
		rptVariableMap.put("L170M01A.CUSTID",
				Util.nullToSpace(l170m01a.getCustId()));
		rptVariableMap.put("L170M01A.DUPNO",
				Util.nullToSpace(l170m01a.getDupNo()));
		rptVariableMap.put("L170M01A.CUSTNAME",
				Util.nullToSpace(l170m01a.getCustName()));
		rptVariableMap.put("L170M01A.RANDOMCODE",
				Util.nullToSpace(l170m01a.getRandomCode()));
		rptVariableMap.put("L170M01A.TOTQUOTACURR",
				Util.nullToSpace(l170m01a.getTotQuotaCurr()));
		rptVariableMap.put("L170M01A.LNDATADATE",
				Util.nullToSpace(TWNDate.toAD(l170m01a.getLnDataDate())));
		rptVariableMap.put("L170M01A.TYPCD", Util.nullToSpace(typCdMap.get(Util
				.nullToSpace(l170m01a.getTypCd()))));
		StringBuffer str = new StringBuffer();
		if ("Y".equals(l170m01a.getMLoanPerson())) {
			str.append("■").append(yesNoMap.get("Y")).append("□")
					.append(yesNoMap.get("N"));
		} else if ("N".equals(l170m01a.getMLoanPerson())) {
			str.append("□").append(yesNoMap.get("Y")).append("■")
					.append(yesNoMap.get("N"));
		} else {
			str.append("□").append(yesNoMap.get("Y")).append("□")
					.append(yesNoMap.get("N"));
		}
		if (Util.equals("", ver)) {
			rptVariableMap.put("L170M01A.MLOANPERSONVALUE", str.toString());
		} else {
			if (Util.equals(LMSUtil.getLocale(), "en")
					&& Util.notEquals(UtilConstants.Country.加拿大,
							branchtype.getCountryType())) {
				rptVariableMap.put("L170M01A.MLOANPERSON",
						toBox("Y6|N6", prop, l170m01a.getMLoanPerson(), 0));
			}
			rptVariableMap.put("L170M01A.MLOANPERSON", str.toString());
		}

		rptVariableMap.put("L170M01A.QUOTAAMT", NumConverter.addComma(
				l170m01a.getTotQuota() == null ? BigDecimal.ZERO : l170m01a
						.getTotQuota().divide(thu)
						.setScale(2, BigDecimal.ROUND_HALF_UP), "#,##0.00"));
		rptVariableMap.put("L170M01A.BALAMT", NumConverter.addComma(
				l170m01a.getTotBal() == null ? BigDecimal.ZERO : l170m01a
						.getTotBal().divide(thu)
						.setScale(2, BigDecimal.ROUND_HALF_UP), "#,##0.00"));
		rptVariableMap.put("L170M01A.QUOTACURR",
				Util.trim(l170m01a.getTotQuotaCurr()));
		rptVariableMap.put("L170M01A.BALCURR",
				Util.trim(l170m01a.getTotBalCurr()));
		rptVariableMap.put("L170M01A.UPDATER",
				userInfoService.getUserName(Util.trim(l170m01a.getUpdater())));

		rptVariableMap.put("L170M01A.REALRPFG",
				Util.trim(l170m01a.getRealRpFg()));
		// （含土建融實地覆審）
		rptVariableMap.put(
				"L170M01A.REALRPFGSTR",
				(Util.equals("Y", Util.trim(l170m01a.getRealRpFg()))) ? prop
						.getProperty("L170M01A.realRpFgStrY") : prop
						.getProperty("L170M01A.realRpFgStrN"));
		rptVariableMap.put("L170M01A.REALDT", ""); // 無
		if (Util.equals(LMSUtil.getLocale(), "en")
				&& Util.notEquals(UtilConstants.Country.加拿大,
						branchtype.getCountryType())) {
			rptVariableMap.put("L170M01A.MLOANPERSONA",
					toBox("Y7|N7", prop, l170m01a.getMLoanPersonA(), 0));
		} else {
			rptVariableMap.put("L170M01A.MLOANPERSONA",
					toBox("Y|N", prop, l170m01a.getMLoanPersonA(), 0));
		}
		rptVariableMap.put("L170M01A.RLTCOBORROWER", ""); // 共同借款人

		// J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
		// rptVariableMap.put("L170M01A.APPENDIX",Util.trim(Util.trim(l170m01a.getProjectNo())
		// + " "
		// + Util.trim(l170m01a.getCustId()) + Util.trim(l170m01a.getDupNo()) +
		// " "
		// + Util.trim(l170m01a.getCustName())));

		String revisedDate = "";
		if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
				LrsUtil.V_O_202406)) {

			// J-113-0204 新增及修正說明文句
			revisedDate = prop
					.getProperty("revisedDate." + l170m01a.getRptId());
			if (Util.equals("Y", l170m01a.getRealRpFg())) {
				revisedDate = prop.getProperty("revisedDate.Ver202404");
			}

		} else if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
				LrsUtil.V_O_202210)) {
			// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
			revisedDate = prop
					.getProperty("revisedDate." + l170m01a.getRptId());
		} else {
			revisedDate = prop.getProperty("revisedDate");
		}
		rptVariableMap.put("revisedDateStr", revisedDate);

		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(L170M01B)
	 * 
	 * @param titleRows
	 *            存放變數list
	 * @param l170m01bList
	 *            L170M01B資料
	 * @param result
	 *            是否只顯示三筆
	 * @return List<Map<String, String>> titleRows
	 */
	private List<Map<String, String>> setL170M01BListData(
			List<Map<String, String>> titleRows, List<L170M01B> l170m01bList,
			boolean result, Map<String, String> yesNoMap,
			Map<String, String> subItemMap, Properties rptProperties) {
		int count = 0;
		List<String> cntrnoList = new LinkedList<String>();
		Map<String, String> map = null;
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "L");
		titleRows.add(map);
		BigDecimal thu = new BigDecimal(1000);
		for (L170M01B l170m01b : l170m01bList) {
			if (result && count >= 1) {
				count++;
			} else if (!result && count < 1) {
				count++;
			} else {
				map = Util.setColumnMap();
				map.put("ReportBean.column01", "Y");
				map.put("ReportBean.column02",
						Util.nullToSpace(l170m01b.getCntrNo()));
				boolean checkResult = false;
				for (String cntrno : cntrnoList) {
					if (cntrno.equals(Util.nullToSpace(l170m01b.getCntrNo()))) {
						checkResult = true;
					}
				}
				if (checkResult) {
					map.put("ReportBean.column08",
							rptProperties.getProperty("L170M01B.001"));
					map.put("ReportBean.column09",
							rptProperties.getProperty("L170M01B.001"));
					map.put("ReportBean.column10",
							rptProperties.getProperty("L170M01B.001"));
				} else {
					map.put("ReportBean.column08",
							Util.nullToSpace(l170m01b.getGuaranteeName()));
					map.put("ReportBean.column09",
							Util.nullToSpace(l170m01b.getEstCurr())
									+ NumConverter.addComma(
											l170m01b.getEstAmt() == null ? BigDecimal.ZERO
													: l170m01b
															.getEstAmt()
															.divide(thu)
															.setScale(
																	2,
																	BigDecimal.ROUND_HALF_UP),
											"#,##0.00"));
					map.put("ReportBean.column10",
							Util.nullToSpace(l170m01b.getLoanCurr())
									+ NumConverter.addComma(
											l170m01b.getLoanAmt() == null ? BigDecimal.ZERO
													: l170m01b
															.getLoanAmt()
															.divide(thu)
															.setScale(
																	2,
																	BigDecimal.ROUND_HALF_UP),
											"#,##0.00"));
				}
				map.put("ReportBean.column03",
						Util.nullToSpace(l170m01b.getQuotaCurr())
								+ NumConverter.addComma(
										l170m01b.getQuotaAmt() == null ? BigDecimal.ZERO
												: l170m01b
														.getQuotaAmt()
														.divide(thu)
														.setScale(
																2,
																BigDecimal.ROUND_HALF_UP),
										"#,##0.00"));
				map.put("ReportBean.column04",
						Util.nullToSpace(l170m01b.getBalCurr())
								+ NumConverter.addComma(
										l170m01b.getBalAmt() == null ? BigDecimal.ZERO
												: l170m01b
														.getBalAmt()
														.divide(thu)
														.setScale(
																2,
																BigDecimal.ROUND_HALF_UP),
										"#,##0.00"));
				map.put("ReportBean.column05",
						Util.nullToSpace(TWNDate.toAD(l170m01b.getFromDate()))
								+ "~"
								+ Util.nullToSpace(TWNDate.toAD(l170m01b
										.getEndDate())));
				map.put("ReportBean.column07", Util.nullToSpace(yesNoMap
						.get(Util.nullToSpace(l170m01b.getNewCase()))));
				map.put("ReportBean.column11", Util.trim(subItemMap.get(Util
						.nullToSpace(l170m01b.getLoanTP()))));
				if ("~".equals(Util.trim(map.get("ReportBean.column05")))) {
					map.put("ReportBean.column05", "");
				}
				titleRows.add(map);
				count++;
				cntrnoList.add(Util.nullToSpace(l170m01b.getCntrNo()));
			}
		}
		if (result) {
			for (int i = count; i < 1; i++) {
				map = Util.setColumnMap();
				map.put("ReportBean.column01", "Y");
				titleRows.add(map);
			}
		}
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "N");
		titleRows.add(map);
		return titleRows;
	}

	/**
	 * 塞入變數MAP資料使用(L170M01B)
	 * 
	 * @param titleRows
	 *            存放變數list
	 * @param l170m01bList
	 *            L170M01B資料
	 * @param result
	 *            是否只顯示三筆
	 * @return List<Map<String, String>> titleRows
	 */
	private Map<String, String> setL170M01FData(
			Map<String, String> rptVariableMap, L170M01F l170m01f,
			Map<String, String> yesNoMap, Properties rptProperties, String ver) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());

		if (l170m01f == null)
			l170m01f = new L170M01F();
		StringBuffer str = new StringBuffer();

		rptVariableMap.put(
				"L170M01F.CONFLAG",
				Util.nullToSpace(rptProperties.getProperty("L170M01F.conFlag"
						+ l170m01f.getConFlag())));
		if (Util.equals("", ver)) {
			rptVariableMap.put("L170M01F.CONDITION",
					Util.nullToSpace(l170m01f.getCondition()));
		} else {
			String Condition = "";
			if (UtilConstants.Country.加拿大.equals(branchtype.getCountryType())) {
				Condition = rptProperties.getProperty("label.C_Rating")
						+ (Util.equals("0",
								Util.nullToSpace(l170m01f.getCurRate())) ? ""
								: Util.nullToSpace(l170m01f.getCurRate()))
						+ "　　"
						+ rptProperties.getProperty("label.S_Rating")
						+ (Util.equals("0",
								Util.nullToSpace(l170m01f.getSugRate())) ? ""
								: Util.nullToSpace(l170m01f.getSugRate()))
						+ "\n";
			}
			if (Util.equals("1", l170m01f.getConFlag())) {
				rptVariableMap.put(
						"L170M01F.CONDITION",
						Condition
								+ rptProperties.getProperty("L170M01F.conFlag")
								+ (Util.isNotEmpty(Util.nullToSpace(l170m01f
										.getCondition())) ? "，" : "")
								+ Util.nullToSpace(l170m01f.getCondition()));
			} else {
				rptVariableMap.put("L170M01F.CONDITION",
						Condition + Util.nullToSpace(l170m01f.getCondition()));
			}
		}

		if ("Y".equals(l170m01f.getRetialComm())) {
			str.append("■ ");
		} else {
			str.append("□ ");
		}
		str.append(yesNoMap.get("Y"));
		if ("N".equals(l170m01f.getRetialComm())) {
			str.append(" ■ ");
		} else {
			str.append(" □ ");
		}
		str.append(yesNoMap.get("N"));
		rptVariableMap.put("L170M01F.RETIALCOMM", str.toString());
		rptVariableMap.put("L170M01F.BRANCHCOMM",
				Util.nullToSpace(l170m01f.getBranchComm()));

		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(L170M01C)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param L170M01C
	 *            L170M01C資料資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL170M01CData(
			Map<String, String> rptVariableMap, L170M01C l170m01c,
			Map<String, String> unitMap, String ver) {
		if (l170m01c == null)
			l170m01c = new L170M01C();
		String showNA1 = l170m01c.getEndDate1() == null ? "" : "N.A.";
		String showNA2 = l170m01c.getEndDate2() == null ? "" : "N.A.";
		String showNA3 = l170m01c.getEndDate3() == null ? "" : "N.A.";
		if (Util.equals("", ver)) {
			rptVariableMap.put("L170M01C.FORMDATE1",
					Util.nullToSpace(TWNDate.toAD(l170m01c.getFromDate1())));
			rptVariableMap.put("L170M01C.FORMDATE2",
					Util.nullToSpace(TWNDate.toAD(l170m01c.getFromDate2())));
			rptVariableMap.put("L170M01C.FORMDATE3",
					Util.nullToSpace(TWNDate.toAD(l170m01c.getFromDate3())));
		} else {
			rptVariableMap.put("L170M01C.FROMDATE1",
					Util.trim(TWNDate.toAD(l170m01c.getFromDate1())));
			rptVariableMap.put("L170M01C.FROMDATE2",
					Util.trim(TWNDate.toAD(l170m01c.getFromDate2())));
			rptVariableMap.put("L170M01C.FROMDATE3",
					Util.trim(TWNDate.toAD(l170m01c.getFromDate3())));
		}
		rptVariableMap.put("L170M01C.ENDDATE1",
				Util.nullToSpace(TWNDate.toAD(l170m01c.getEndDate1())));
		rptVariableMap.put("L170M01C.ENDDATE2",
				Util.nullToSpace(TWNDate.toAD(l170m01c.getEndDate2())));
		rptVariableMap.put("L170M01C.ENDDATE3",
				Util.nullToSpace(TWNDate.toAD(l170m01c.getEndDate3())));
		rptVariableMap.put("L170M01C.RATIONO1",
				this.getRatioNoStr(Util.nullToSpace(l170m01c.getRatioNo1())));
		rptVariableMap.put("L170M01C.RATIONO2",
				this.getRatioNoStr(Util.nullToSpace(l170m01c.getRatioNo2())));
		rptVariableMap.put("L170M01C.RATIONO3",
				this.getRatioNoStr(Util.nullToSpace(l170m01c.getRatioNo3())));
		rptVariableMap.put("L170M01C.RATIONO4",
				this.getRatioNoStr(Util.nullToSpace(l170m01c.getRatioNo4())));
		rptVariableMap.put("L170M01C.RATEDATE1",
				Util.nullToSpace(TWNDate.toAD(l170m01c.getRateDate1())));
		rptVariableMap.put("L170M01C.RATEDATE2",
				Util.nullToSpace(TWNDate.toAD(l170m01c.getRateDate2())));
		rptVariableMap.put("L170M01C.RATEDATE3",
				Util.nullToSpace(TWNDate.toAD(l170m01c.getRateDate3())));
		rptVariableMap.put(
				"L170M01C.RATE11",
				Util.isEmpty(l170m01c.getRate11()) ? showNA1 : NumConverter
						.addComma(l170m01c.getRate11(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.RATE12",
				Util.isEmpty(l170m01c.getRate12()) ? showNA1 : NumConverter
						.addComma(l170m01c.getRate12(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.RATE13",
				Util.isEmpty(l170m01c.getRate13()) ? showNA1 : NumConverter
						.addComma(l170m01c.getRate13(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.RATE14",
				Util.isEmpty(l170m01c.getRate14()) ? showNA1 : NumConverter
						.addComma(l170m01c.getRate14(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.RATE21",
				Util.isEmpty(l170m01c.getRate21()) ? showNA2 : NumConverter
						.addComma(l170m01c.getRate21(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.RATE22",
				Util.isEmpty(l170m01c.getRate22()) ? showNA2 : NumConverter
						.addComma(l170m01c.getRate22(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.RATE23",
				Util.isEmpty(l170m01c.getRate23()) ? showNA2 : NumConverter
						.addComma(l170m01c.getRate23(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.RATE24",
				Util.isEmpty(l170m01c.getRate24()) ? showNA2 : NumConverter
						.addComma(l170m01c.getRate24(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.RATE31",
				Util.isEmpty(l170m01c.getRate31()) ? showNA3 : NumConverter
						.addComma(l170m01c.getRate31(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.RATE32",
				Util.isEmpty(l170m01c.getRate32()) ? showNA3 : NumConverter
						.addComma(l170m01c.getRate32(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.RATE33",
				Util.isEmpty(l170m01c.getRate33()) ? showNA3 : NumConverter
						.addComma(l170m01c.getRate33(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.RATE34",
				Util.isEmpty(l170m01c.getRate34()) ? showNA3 : NumConverter
						.addComma(l170m01c.getRate34(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.AMT11",
				Util.isEmpty(l170m01c.getAmt11()) ? showNA1 : NumConverter
						.addComma(l170m01c.getAmt11(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.AMT12",
				Util.isEmpty(l170m01c.getAmt12()) ? showNA1 : NumConverter
						.addComma(l170m01c.getAmt12(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.AMT13",
				Util.isEmpty(l170m01c.getAmt13()) ? showNA1 : NumConverter
						.addComma(l170m01c.getAmt13(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.AMT21",
				Util.isEmpty(l170m01c.getAmt21()) ? showNA2 : NumConverter
						.addComma(l170m01c.getAmt21(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.AMT22",
				Util.isEmpty(l170m01c.getAmt22()) ? showNA2 : NumConverter
						.addComma(l170m01c.getAmt22(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.AMT23",
				Util.isEmpty(l170m01c.getAmt23()) ? showNA2 : NumConverter
						.addComma(l170m01c.getAmt23(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.AMT31",
				Util.isEmpty(l170m01c.getAmt31()) ? showNA3 : NumConverter
						.addComma(l170m01c.getAmt31(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.AMT32",
				Util.isEmpty(l170m01c.getAmt32()) ? showNA3 : NumConverter
						.addComma(l170m01c.getAmt32(), "#,##0.00"));
		rptVariableMap.put(
				"L170M01C.AMT33",
				Util.isEmpty(l170m01c.getAmt33()) ? showNA3 : NumConverter
						.addComma(l170m01c.getAmt33(), "#,##0.00"));
		rptVariableMap.put("L170M01C.CURR",
				Util.nullToSpace(l170m01c.getCurr()));
		rptVariableMap.put("L170M01C.UNIT",
				Util.nullToSpace(unitMap.get(Util.trim(l170m01c.getUnit()))));

		return rptVariableMap;
	}

	private String getRatioNoStr(String ratioNo) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1705S03Panel.class);
		if (!Util.isEmpty(ratioNo)) {
			int ratioNoInt = Util.parseInt(ratioNo);
			ratioNo = pop.getProperty("L170M01c.ratioNo" + ratioNoInt);
		} else {
			ratioNo = "";
		}
		return ratioNo;
	}

	/**
	 * 塞入變數MAP資料使用(L170M01C)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param L170M01C
	 *            L170M01C資料資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL170M01DListData(
			Map<String, String> rptVariableMap, List<L170M01D> L170m01dList,
			Properties rptProperties) {
		// A的項目
		// int countA = 5;
		// // B的項目
		// int countB = 21;
		// boolean result = false;

		// J-108-0888_05097_B1001
		// 此功能已經做廢，不會被呼叫到

		StringBuffer str = new StringBuffer();
		for (L170M01D l170m01d : L170m01dList) {
			rptVariableMap = this.getCHKYN(rptProperties,
					Util.nullToSpace(l170m01d.getItemContent()),
					Util.nullToSpace(l170m01d.getChkResult()),
					Util.nullToSpace(l170m01d.getItemNo()),
					l170m01d.getItemSeq(), l170m01d.getChkCheck(),
					rptVariableMap);
			if (("B015".equals(l170m01d.getItemNo()) && !"N".equals(l170m01d
					.getChkResult()))
					|| (!"B015".equals(l170m01d.getItemNo()) && Util
							.isNotEmpty(l170m01d.getChkText()))) {

				if ("B015".equals(l170m01d.getItemNo())
						&& (Util.isEmpty(l170m01d.getChkPreReview()) || "K"
								.equals(Util.trim(l170m01d.getChkPreReview())))) {

				} else {
					if (str.length() > 0) {
						str.append("\n");
					}
					str.append(rptProperties.getProperty("L170M01D.SPE"))
							.append(Util.trim(l170m01d.getItemSeq()))
							.append(rptProperties.getProperty("L170M01D.ITEM"));
					if ("B008".equals(l170m01d.getItemNo())) {
						str.append(".").append(
								Util.trim(rptProperties
										.get("L170M01D.ITEMB008")));
					} else if ("B014".equals(l170m01d.getItemNo())) {
						str.append(".").append(
								Util.trim(rptProperties
										.get("L170M01D.ITEMB014")));
					} else if ("B015".equals(l170m01d.getItemNo())) {
						str.append(".")
								.append(Util.trim(rptProperties
										.get("L170M01D.ITEMB015"
												+ Util.trim(l170m01d
														.getChkPreReview()))));
					}
					str.append(":").append(Util.trim(l170m01d.getChkText()));
				}
			}
		}
		rptVariableMap.put("L170M01D.ITEMCONTENTS", str.toString());

		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(L170M01C)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param L170M01C
	 *            L170M01C資料資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL170M01DListDataHtml(L170M01A l170m01a,
			Map<String, String> rptVariableMap, List<L170M01D> L170m01dList,
			Properties rptProperties) throws CapException {
		// A的項目
		// int countA = 5;
		// // B的項目
		// int countB = 21;
		// boolean result = false;

		// 讀檔
		String content = null;
		Map<String, String> map = new LinkedHashMap<String, String>();

		try {
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/"
					+ "LMSDoc19_"
					+ LMSUtil.getLocale() + ".htm");

		} catch (FileNotFoundException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (IOException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (Exception e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		}
		StringBuffer str = new StringBuffer();
		for (L170M01D l170m01d : L170m01dList) {
           		
			// J-108-0888_05097_B1001
			int itemSeq = this.getItemSeqForShow(l170m01a, l170m01d);

			map = this.getCHKYN(rptProperties,
					Util.nullToSpace(l170m01d.getItemContent()),
					Util.nullToSpace(l170m01d.getChkResult()),
					Util.nullToSpace(l170m01d.getItemNo()), itemSeq,
					l170m01d.getChkCheck(), map);

			if (("B015".equals(l170m01d.getItemNo()) && !"N".equals(l170m01d
					.getChkResult()))
					|| (!"B015".equals(l170m01d.getItemNo()) && Util
							.isNotEmpty(l170m01d.getChkText()))) {

				if ("B015".equals(l170m01d.getItemNo())
						&& (Util.isEmpty(l170m01d.getChkPreReview()) || "K"
								.equals(Util.trim(l170m01d.getChkPreReview())))) {

				} else {
					if (str.length() > 0) {
						str.append("<br/>");
					}
					str.append(rptProperties.getProperty("L170M01D.SPE"))
							.append(Util.trim(itemSeq))
							.append(rptProperties.getProperty("L170M01D.ITEM"));
					if ("B008".equals(l170m01d.getItemNo())) {
						str.append(".").append(
								Util.trim(rptProperties
										.get("L170M01D.ITEMB008")));
					} else if ("B014".equals(l170m01d.getItemNo())) {
						str.append(".").append(
								Util.trim(rptProperties
										.get("L170M01D.ITEMB014")));
					} else if ("B015".equals(l170m01d.getItemNo())) {
						str.append(".")
								.append(Util.trim(rptProperties
										.get("L170M01D.ITEMB015"
												+ Util.trim(l170m01d
														.getChkPreReview()))));
					}
					str.append(":").append(Util.trim(l170m01d.getChkText()));
				}
			}
		}

		map.put("L170M01D.ITEMCONTENTS", str.toString());

		content = Util.replaceWordContent(content, this.addMarkByMapKey(map));

		// //////////////////////////////////////////////////////////////////
		rptVariableMap.put("L170M01D.CHKITEMALL", content);
		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(L170M01E)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param L170M01A
	 *            L170M01A資料資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL170M01EData(
			Map<String, String> rptVariableMap, List<L170M01E> l170m01eList,
			Map<String, String> crdTypeMap, Map<String, String> crdType2Map,
			String ver, Properties prop, List<L170M01E> exl170m01eList) {
		StringBuffer str1 = new StringBuffer();
		StringBuffer str2 = new StringBuffer();
		StringBuffer str3 = new StringBuffer();

		for (L170M01E l170m01e : l170m01eList) {
			if (Util.nullToSpace(
					crdType2Map.get(Util.trim(l170m01e.getCrdType()))).length() > 0
					|| Util.trim(l170m01e.getGrade()).length() > 0) {
				if (UtilConstants.crdType.DBU大型企業.equals(Util.trim(l170m01e
						.getCrdType()))
						|| UtilConstants.crdType.DBU中小型企業.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdType.海外.equals(Util.trim(l170m01e
								.getCrdType()))
						|| UtilConstants.crdType.未評等.equals(Util.trim(l170m01e
								.getCrdType()))) {
					if (Util.isNotEmpty(crdTypeMap.get(Util.trim(l170m01e
							.getCrdType())))) {
						if (str1.length() > 0) {
							str1.append("、");
						}
						str1.append(
								Util.nullToSpace(crdTypeMap.get(Util
										.trim(l170m01e.getCrdType()))))
								.append(" ")
								.append(Util.trim(l170m01e.getGrade()));
					}

					// str1.append(l170m01e.getCrdTYear() != null ?
					// Util.nullToSpace(TWNDate.toAD(l170m01e.getCrdTYear())).split("-")[0]
					// : "").append(" ")

				} else if (Util.notEquals(Util.nullToSpace(crdType2Map.get(Util
						.trim(l170m01e.getCrdType()))), "")

				// UtilConstants.mowType.DBU大型企業.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU中型企業.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU中小型企業.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU不動產有建案規劃.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU專案融資.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU本國證券公司.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU投資公司一般情況.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU租賃公司.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU一案建商.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU非一案建商.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU非一案建商無擔.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.投資公司情況一.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.投資公司情況二.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.OBU境外船舶.equals(Util
				// .trim(l170m01e.getCrdType()))
				// || UtilConstants.mowType.免辦.equals(Util.trim(l170m01e
				// .getCrdType()))

				) {
					if (Util.isNotEmpty(crdType2Map.get(Util.trim(l170m01e
							.getCrdType())))) {
						if (str2.length() > 0) {
							str2.append("、");
						}
						str2.append(
								Util.nullToSpace(crdType2Map.get(Util
										.trim(l170m01e.getCrdType()))))
								.append(" ")
								.append(Util.trim(l170m01e.getGrade()));
					}

					// str3.append(l170m01e.getCrdTYear() != null ?
					// Util.nullToSpace(TWNDate.toAD(l170m01e.getCrdTYear())).split("-")[0]
					// : "").append(" ")

				} else if (UtilConstants.crdTypeC.泰國GroupA.equals(Util
						.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeC.泰國GroupB.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeC.自訂.equals(Util.trim(l170m01e
								.getCrdType()))
						|| UtilConstants.crdTypeC.消金評等.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.Moody.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.中華信評.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.SP.equals(Util.trim(l170m01e
								.getCrdType()))
						|| UtilConstants.crdTypeN.Fitch.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.FitchTW.equals(Util
								.trim(l170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.KBRA.equals(Util
								.trim(l170m01e.getCrdType()))) {
					// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等

					if (Util.isNotEmpty(crdTypeMap.get(Util.trim(l170m01e
							.getCrdType())))) {
						if (str3.length() > 0) {
							str3.append("、");
						}
						str3.append(
								Util.nullToSpace(crdTypeMap.get(Util
										.trim(l170m01e.getCrdType()))))
								.append(" ")
								.append(Util.trim(l170m01e.getGrade()));
					}

					// str2.append(l170m01e.getCrdTYear() != null ?
					// Util.nullToSpace(TWNDate.toAD(l170m01e.getCrdTYear())).split("-")[0]
					// : "").append(" ")

				}
			}

		}
		// 前次
		StringBuffer str4 = new StringBuffer();
		StringBuffer str5 = new StringBuffer();
		StringBuffer str6 = new StringBuffer();
		for (L170M01E exl170m01e : exl170m01eList) {
			if (Util.nullToSpace(
					crdType2Map.get(Util.trim(exl170m01e.getCrdType())))
					.length() > 0
					|| Util.trim(exl170m01e.getGrade()).length() > 0
					|| Util.equals(UtilConstants.Type.無資料_C,
							Util.trim(exl170m01e.getCrdType()))
					|| Util.equals(UtilConstants.Type.無資料_M,
							Util.trim(exl170m01e.getCrdType()))) {
				if (UtilConstants.crdType.DBU大型企業.equals(Util.trim(exl170m01e
						.getCrdType()))
						|| UtilConstants.crdType.DBU中小型企業.equals(Util
								.trim(exl170m01e.getCrdType()))
						|| UtilConstants.crdType.海外.equals(Util.trim(exl170m01e
								.getCrdType()))
						|| UtilConstants.crdType.未評等.equals(Util
								.trim(exl170m01e.getCrdType()))
						|| UtilConstants.Type.無資料_C.equals(Util.trim(exl170m01e
								.getCrdType()))) {
					if (Util.isNotEmpty(crdTypeMap.get(Util.trim(exl170m01e
							.getCrdType())))) {
						if (str4.length() > 0) {
							str4.append("、");
						}
						str4.append(
								Util.nullToSpace(crdTypeMap.get(Util
										.trim(exl170m01e.getCrdType()))))
								.append(" ")
								.append(Util.trim(exl170m01e.getGrade()));
					} else if (UtilConstants.Type.無資料_C.equals(Util
							.trim(exl170m01e.getCrdType()))) {
						if (str4.length() > 0) {
							str4.append("、");
						}
						str4.append("N.A.");
					}

				} else if (Util.notEquals(Util.nullToSpace(crdType2Map.get(Util
						.trim(exl170m01e.getCrdType()))), "")

				// } else if (UtilConstants.mowType.DBU大型企業.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU中型企業.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU中小型企業.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU不動產有建案規劃.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU專案融資.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU本國證券公司.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU投資公司一般情況.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU租賃公司.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU一案建商.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU非一案建商.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.DBU非一案建商無擔.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.投資公司情況一.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.投資公司情況二.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.OBU境外船舶.equals(Util
				// .trim(exl170m01e.getCrdType()))
				// || UtilConstants.mowType.免辦.equals(Util.trim(exl170m01e
				// .getCrdType()))
				// || UtilConstants.Type.無資料_M.equals(Util.trim(exl170m01e
				// .getCrdType()))

				) {
					if (Util.isNotEmpty(crdType2Map.get(Util.trim(exl170m01e
							.getCrdType())))) {
						if (str5.length() > 0) {
							str5.append("、");
						}
						str5.append(
								Util.nullToSpace(crdType2Map.get(Util
										.trim(exl170m01e.getCrdType()))))
								.append(" ")
								.append(Util.trim(exl170m01e.getGrade()));
					} else if (UtilConstants.Type.無資料_M.equals(Util
							.trim(exl170m01e.getCrdType()))) {
						if (str5.length() > 0) {
							str5.append("、");
						}
						str5.append("N.A.");
					}
				} else if (UtilConstants.crdTypeC.泰國GroupA.equals(Util
						.trim(exl170m01e.getCrdType()))
						|| UtilConstants.crdTypeC.泰國GroupB.equals(Util
								.trim(exl170m01e.getCrdType()))
						|| UtilConstants.crdTypeC.自訂.equals(Util
								.trim(exl170m01e.getCrdType()))
						|| UtilConstants.crdTypeC.消金評等.equals(Util
								.trim(exl170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.Moody.equals(Util
								.trim(exl170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.中華信評.equals(Util
								.trim(exl170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.SP.equals(Util
								.trim(exl170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.Fitch.equals(Util
								.trim(exl170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.FitchTW.equals(Util
								.trim(exl170m01e.getCrdType()))
						|| UtilConstants.crdTypeN.KBRA.equals(Util
								.trim(exl170m01e.getCrdType()))) {
					// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
					if (Util.isNotEmpty(crdTypeMap.get(Util.trim(exl170m01e
							.getCrdType())))) {
						if (str6.length() > 0) {
							str6.append("、");
						}
						str6.append(
								Util.nullToSpace(crdTypeMap.get(Util
										.trim(exl170m01e.getCrdType()))))
								.append(" ")
								.append(Util.trim(exl170m01e.getGrade()));
					}
				}
			}
		}
		if (Util.equals("", ver)) {
			rptVariableMap.put("L170M01E.CRD1",
					str1.toString() + "\n" + prop.getProperty("L170M01E.ex")
							+ prop.getProperty("L170M01E.C") + str4.toString());
			rptVariableMap.put("L170M01E.CRD2",
					str2.toString() + "\n" + prop.getProperty("L170M01E.ex")
							+ prop.getProperty("L170M01E.M") + str5.toString());
			rptVariableMap.put("L170M01E.CRD3",
					str3.toString() + "\n" + prop.getProperty("L170M01E.ex")
							+ prop.getProperty("L170M01E.F") + str6.toString());
		} else {
			rptVariableMap.put(
					"L170M01E.C_M",
					prop.getProperty("L170M01E.C") + str1.toString() + "   "
							+ prop.getProperty("L170M01E.M") + str2.toString()
							+ "\n" + prop.getProperty("L170M01E.ex")
							+ prop.getProperty("L170M01E.C") + str4.toString()
							+ "   " + prop.getProperty("L170M01E.ex")
							+ prop.getProperty("L170M01E.M") + str5.toString());
			rptVariableMap.put("L170M01E.F", prop.getProperty("L170M01E.F")
					+ str3.toString() + "\n" + prop.getProperty("L170M01E.ex")
					+ prop.getProperty("L170M01E.F") + str6.toString());
		}
		return rptVariableMap;
	}

	/**
	 * 取得複審項目的是否
	 * 
	 * @param chk
	 *            是否
	 * @param no
	 *            編號
	 * @param rptVariableMap
	 *            rptVariableMap
	 * @return Map<String,String>
	 */
	private Map<String, String> getCHKYN(Properties prop, String item,
			String chk, String no, int seq, String chkCheck,
			Map<String, String> rptVariableMap) {

		rptVariableMap
				.put("L170M01D.ITEM" + no.toUpperCase() + "SEQ", seq + "");
		if ("A002".equals(no)) {
			rptVariableMap.put(
					"L170M01D.ITEM" + no.toUpperCase() + "ITEM",
					item
							+ "("
							+ ("N".equals(chkCheck) ? prop
									.getProperty("RPTSHOW.NO") : "")
							+ prop.getProperty("L170M01D.CHKCHECKA002") + ")");
		} else {
			rptVariableMap.put("L170M01D.ITEM" + no.toUpperCase() + "ITEM",
					item);
		}
		if ("Y".equals(chk)) {
			rptVariableMap
					.put("L170M01D.ITEM" + no.toUpperCase() + "CHKY", "V");
			rptVariableMap.put("L170M01D.ITEM" + no.toUpperCase() + "CHKN", "");
		} else if ("N".equals(chk)) {
			rptVariableMap.put("L170M01D.ITEM" + no.toUpperCase() + "CHKY", "");
			rptVariableMap
					.put("L170M01D.ITEM" + no.toUpperCase() + "CHKN", "V");
		} else {
			rptVariableMap
					.put("L170M01D.ITEM" + no.toUpperCase() + "CHKY", "-");
			rptVariableMap
					.put("L170M01D.ITEM" + no.toUpperCase() + "CHKN", "-");
		}
		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(L120M01F)簽章欄
	 * 
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param L120M01F
	 *            L120M01F資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL170M01GData(
			Map<String, String> rptVariableMap, List<L170M01G> l170m01gList,
			Locale locale) {
		String[] jobs = { "L1", "L2", "L3", "L4", "L5", "L6" };
		for (int i = 1; i <= 2; i++) {
			for (String job : jobs) {
				rptVariableMap.put("L170M01G." + i + "STAFFJOB" + job, "");
			}
		}
		for (L170M01G l170m01g : l170m01gList) {
			rptVariableMap.put("L170M01G." + l170m01g.getBranchType()
					+ "STAFFJOB" + l170m01g.getStaffJob(),
					Util.nullToSpace(l170m01g.getStaffName()));
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());
		if (Util.equals("en", locale)) {
			if (UtilConstants.Country.加拿大.equals(branchtype.getCountryType())) {
				rptVariableMap.put("L170M01G.1STAFFJOBL5NAME", "S.V.P./ V.P");
			} else {
				rptVariableMap.put("L170M01G.1STAFFJOBL5NAME",
						"Manager/Deputy Manager/Assistant Manager");
			}
		} else {
			rptVariableMap.put("L170M01G.1STAFFJOBL5NAME", "經副襄理");
		}

		return rptVariableMap;
	}

	/**
	 * 為map的key 前後加上特殊符號
	 * 
	 * @param map
	 * @return
	 */
	private Map<String, String> addMarkByMapKey(Map<String, String> map) {
		Map<String, String> temp = new LinkedHashMap<String, String>();
		String tempKey = "";
		for (String key : map.keySet()) {
			tempKey = key;
			if (!key.endsWith("AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA")) {
				tempKey = "[" + tempKey + "]";
			}
			temp.put(tempKey, map.get(key));
		}
		return temp;
	}

	private String replaceUnit(String unit) {
		String newUnit = unit;

		if (Util.equals(LMSUtil.getLocale(), "en")) {
			newUnit = unit.replaceAll("dollars", "").replaceAll("dollar", "");
			newUnit = newUnit.replaceAll("Dollars", "")
					.replaceAll("Dollar", "");
			newUnit = newUnit.replaceAll("DOLLARS", "")
					.replaceAll("DOLLAR", "");
		}

		return newUnit;
	}

	private Map<String, String> replaceDollar(Map<String, String> unitMap) {

		if (Util.equals(LMSUtil.getLocale(), "en")) {
			String unitStr = "";
			for (Map.Entry<String, String> entry : unitMap.entrySet()) {
				unitStr = entry.getValue().replaceAll("dollars", "")
						.replaceAll("dollar", "");
				unitMap.put(entry.getKey(), unitStr);

			}
		}
		return unitMap;
	}

	/**
	 * J-107-0128海外改格式 產生LMS1705R01的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @return outputstream outputstream
	 * @throws Exception
	 */
	public OutputStream genLMS1705R01V2(String mainId, Locale locale,
			Properties rptProperties) throws Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());

		L170M01A l170m01a = l170m01aDao.findByMainId(mainId);
		L170M01C l170m01c = l170m01cDao.findByMainId(mainId);
		List<L170M01B> l170m01bList = l170m01bDao.findByMainId(mainId);
		List<L170M01D> l170m01dList = l170m01dDao.findByMainId(mainId);
		List<L170M01E> l170m01eList = l170m01eDao.findByMainId(mainId, "T");
		List<L170M01E> exl170m01eList = l170m01eDao.findByMainId(mainId, "L");
		L170M01F l170m01f = l170m01fDao.findByMainId(mainId);
		List<L170M01G> l170m01gList = l170m01gDao.findByMainId(mainId);

		String rtpFileVer = "201809";
		if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
				LrsUtil.V_O_202404)) {
			// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
			rtpFileVer = "202404";
		}

		ReportGenerator generator = new ReportGenerator(
				"report/lrs/LMS1705R01_" + rtpFileVer + "_" + locale.toString()
						+ ".rpt");
		if (Util.equals(LMSUtil.getLocale(), "en")
				&& Util.equals(UtilConstants.Country.加拿大,
						branchtype.getCountryType())) {
			generator = new ReportGenerator("report/lrs/LMS1705R01_"
					+ rtpFileVer + "_CA_" + locale.toString() + ".rpt");
		}
		OutputStream outputStream = null;

		String branchName = null;
		Map<String, String> crdTypeMap = null;
		Map<String, String> crdType2Map = null;
		Map<String, String> unitMap = null;
		Map<String, String> yesNoMap = null;
		Map<String, String> typCdMap = null;
		Map<String, String> subItemMap = null;
		// J-111-0326 海外覆審作業系統改良第一階段： 4-1. 顯示新貸/舊案
		Map<String, String> newCaseMap = null;
		try {
			crdTypeMap = codetypeservice.findByCodeType("CRDType",
					locale.toString());
			crdType2Map = codetypeservice.findByCodeType("lms1705s01_crdType2",
					locale.toString());
			unitMap = codetypeservice.findByCodeType("lms1205s01_Unit",
					locale.toString());

			unitMap = this.replaceDollar(unitMap);

			yesNoMap = codetypeservice.findByCodeType("Common_YesNo",
					locale.toString());
			typCdMap = codetypeservice.findByCodeType("TypCd",
					locale.toString());
			subItemMap = codetypeservice.findByCodeType("lms1405m01_SubItem",
					locale.toString());
			// J-111-0326 海外覆審作業系統改良第一階段： 4-1. 顯示新貸/舊案
			newCaseMap = codetypeservice.findByCodeType("lms1705s02_newCase",
					locale.toString());

			if (subItemMap == null)
				subItemMap = new LinkedHashMap<String, String>();
			if (crdType2Map == null)
				crdType2Map = new LinkedHashMap<String, String>();
			if (crdTypeMap == null)
				crdTypeMap = new LinkedHashMap<String, String>();
			if (unitMap == null)
				unitMap = new LinkedHashMap<String, String>();
			if (yesNoMap == null)
				yesNoMap = new LinkedHashMap<String, String>();
			if (typCdMap == null)
				typCdMap = new LinkedHashMap<String, String>();
			if (newCaseMap == null)
				newCaseMap = new LinkedHashMap<String, String>();

			branchName = branch.getBranchName(Util.nullToSpace(l170m01a
					.getOwnBrId()));

			String logoPath = rptProperties.getProperty("LOGOSHOW");
			rptVariableMap.put("LOGOSHOW", logoPath);

			// 分行名稱
			rptVariableMap.put("BRANCHNAME", Util.nullToSpace(branchName));
			rptVariableMap.put("COUNTRYTYPE", branchtype.getCountryType());
			// 報表版本
			String ver = Util.nullToSpace(l170m01a.getRptId());
			rptVariableMap = this.setL170M01AData(rptVariableMap, l170m01a,
					yesNoMap, typCdMap, rptProperties);
			rptVariableMap = this
					.setL170M01EData(rptVariableMap, l170m01eList, crdTypeMap,
							crdType2Map, ver, rptProperties, exl170m01eList);
			rptVariableMap = this.setL170M01CData(rptVariableMap, l170m01c,
					unitMap, ver);

			rptVariableMap = this.setL170M01DData(rptVariableMap, l170m01dList,
					rptProperties, ver, locale, l170m01a.getRealRpFg(),
					l170m01a);

			titleRows = this.setL170M01BData(titleRows, l170m01bList, true,
					yesNoMap, subItemMap, rptProperties, newCaseMap);
			rptVariableMap = this.setL170M01FData(rptVariableMap, l170m01f,
					yesNoMap, rptProperties, ver);
			rptVariableMap = this.setL170M01GData(rptVariableMap, l170m01gList,
					locale);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (crdTypeMap != null) {
				crdTypeMap.clear();
			}
			if (unitMap != null) {
				unitMap.clear();
			}
			if (crdType2Map != null) {
				crdType2Map.clear();
			}
			if (typCdMap != null) {
				typCdMap.clear();
			}
			if (subItemMap != null) {
				subItemMap.clear();
			}
			if (newCaseMap != null) {
				newCaseMap.clear();
			}
		}
		return outputStream;
	}

	/**
	 * 塞入變數MAP資料使用(L170M01D)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param L170M01D
	 *            L170M01D資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL170M01DData(
			Map<String, String> rptVariableMap, List<L170M01D> L170m01dList,
			Properties rptProperties, String ver, Locale locale,
			String realRpFg, L170M01A l170m01a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());
		StringBuilder stab1 = new StringBuilder();
		ArrayList<String> strList = new ArrayList<String>(); // ITEMCONTENTS
		JSONObject typeJson = new JSONObject();

		// J-108-0888_05097_B1001
		// ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ

		String rptId = Util.trim(l170m01a.getRptId());
		if (Util.notEquals(rptId, "")
				&& LrsUtil.compareRptVersion(rptId, ">=", LrsUtil.V_O_201907)) {
			if (LrsUtil.compareRptVersion(rptId, ">=", LrsUtil.V_O_202210)) {
				// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
				if (Util.equals("en", locale)
						&& Util.equals(UtilConstants.Country.加拿大,
								branchtype.getCountryType())) {
					strList.add(rptProperties
							.getProperty("L170M01D.v4ITEMB003_CA"));
				} else {
					strList.add(rptProperties
							.getProperty("L170M01D.v4ITEMB003"));
				}
			} else {
				if (Util.equals("en", locale)
						&& Util.equals(UtilConstants.Country.加拿大,
								branchtype.getCountryType())) {
					strList.add(rptProperties
							.getProperty("L170M01D.v2ITEMB003_CA"));
				} else {
					// J-111-0031 更動覆審系統內以下九式覆審報告表之文字內容。
					// 2022/02/15 授審連喬凱說 加拿大英文不改
					if (LrsUtil.compareRptVersion(rptId, ">=",
							LrsUtil.V_O_202204)) {
						strList.add(rptProperties
								.getProperty("L170M01D.v3ITEMB003"));
					} else {
						strList.add(rptProperties
								.getProperty("L170M01D.v2ITEMB003"));
					}
				}
			}
			if (Util.notEquals(UtilConstants.Country.加拿大,
					branchtype.getCountryType())
					&& Util.equals("Y", realRpFg)) {
				strList.add(rptProperties
						.getProperty("L170M01D.v2ITEMCONTENT01")
						+ "<br>"
						+ rptProperties.getProperty("L170M01D.v2ITEMCONTENT02"));
			}
		} else {
			if (Util.equals("en", locale)
					&& Util.equals(UtilConstants.Country.加拿大,
							branchtype.getCountryType())) {
				strList.add(rptProperties.getProperty("L170M01D.v2ITEMB003_CA"));
			} else {
				// J-111-0031 更動覆審系統內以下九式覆審報告表之文字內容。
				// 2022/02/15 授審連喬凱說 加拿大英文不改
				if (LrsUtil.compareRptVersion(rptId, ">=", LrsUtil.V_O_202204)) {
					strList.add(rptProperties
							.getProperty("L170M01D.v3ITEMB003"));
				} else {
					strList.add(rptProperties
							.getProperty("L170M01D.v2ITEMB003"));
				}
			}
			if (Util.notEquals(UtilConstants.Country.加拿大,
					branchtype.getCountryType())
					&& Util.equals("Y", realRpFg)) {
				strList.add(rptProperties
						.getProperty("L170M01D.v2ITEMCONTENT01")
						+ "<br>"
						+ rptProperties.getProperty("L170M01D.v2ITEMCONTENT02"));
			}
		}

		for (L170M01D l170m01d : L170m01dList) {
			String type = l170m01d.getItemType();
			if (Util.equals("Z", type) || Util.equals("Y", type)
					|| Util.equals("X", type)) {
				continue;
			}
			if (Util.equals("N", realRpFg)
					&& Util.equals("B008", l170m01d.getItemNo())) {
				continue;
			}
			
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202406)
					&& Util.equals(Util.trim(l170m01a.getRealRpFg()), "Y")
					&& Util.equals("A005", l170m01d.getItemNo())) {
				// J-113-0204 新增及修正說明文句
				// 實地覆審表，沒有該項目
				continue;
			}
			
			int count = Util.parseInt((String) typeJson.get(type));
			typeJson.put(type, String.valueOf(++count));

			// J-108-0888_05097_B1001
			// int itemSeq = l170m01d.getItemSeq();
			int itemSeq = this.getItemSeqForShow(l170m01a, l170m01d);
            if (Util.notEquals(rptId, "")
					&& LrsUtil.compareRptVersion(rptId, ">=",
							LrsUtil.V_O_201907)) {
				// itemSeq 已經變成 itemSeqShow,所以不用再特別處理
			} else {
				if (itemSeq >= 12) {
					itemSeq = itemSeq - 1;
				}
			}

			if (Util.isNotEmpty(l170m01d.getChkText())) {
				String item = "";
				if (Util.equals("en", locale)) {
					item = rptProperties.getProperty("L170M01D.ITEM") + " "
							+ itemSeq;
				} else {
					item = itemSeq + rptProperties.getProperty("L170M01D.ITEM");
				}

				if (Util.equals("B009", l170m01d.getItemNo())) {
					strList.add(item + "."
							+ rptProperties.getProperty("L170M01D.ITEMB008")
							+ "：" + l170m01d.getChkText());
				} else if (Util.equals("B015", l170m01d.getItemNo())) {
					strList.add(item + "."
							+ rptProperties.getProperty("L170M01D.ITEMB014")
							+ "：" + l170m01d.getChkText());
				} else if (Util.equals("B016", l170m01d.getItemNo())) {
					strList.add(item
							+ "."
							+ (Util.equals("Y", l170m01d.getChkPreReview()) ? rptProperties
									.getProperty("L170M01D.ITEMB015Y")
									: rptProperties
											.getProperty("L170M01D.ITEMB015N"))
							+ " " + l170m01d.getChkText());
				} else {
					strList.add(item + "." + l170m01d.getChkText());
				}
			}
		}
		int itemRows = typeJson.getInt("A") + typeJson.getInt("B")
				+ typeJson.getInt("C");

		// Creat stab1 HTML CODE
		stab1.append("<style> table,td { border: 1px solid black; border-collapse: collapse;} </style>");
		stab1.append("<table border=\"1px\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border: 1px #000000 solid; \">");
		stab1.append("<tbody>");
		stab1.append("<tr height=\"1px\">");
		stab1.append("<td cellpadding=\"0\" cellspacing=\"0\" colspan=\"3\" style=\"width: 80%; text-align: center;border: 1px solid rgb(0, 0, 0);\">");
		stab1.append("<span style=\"display: inline-block; width: 100%;\">"
				+ rptProperties.getProperty("TITLE.RPTTITLE01") + "</span>");
		stab1.append("</td>");
		stab1.append("<td colspan=\"5\" style=\"width: 20%; border: 1px solid rgb(0, 0, 0); text-align: center;\">");
		if (Util.equals("en", locale)
				&& Util.equals(UtilConstants.Country.加拿大,
						branchtype.getCountryType())) {
			stab1.append("<span style=\"display: inline-block; width: 100%;\">"
					+ rptProperties.getProperty("TITLE.RPTTITLE02_CA")
					+ "</span>");
		} else {
			stab1.append("<span style=\"display: inline-block; width: 100%;\">"
					+ rptProperties.getProperty("TITLE.RPTTITLE02") + "</span>");
		}
		stab1.append("</td>");
		stab1.append("</tr>");

		int countTable = 0, countType = 0;
		String nowType = "";
		for (L170M01D l170m01d : L170m01dList) {
			String ItemType = l170m01d.getItemType();
			if (Util.equals("Z", ItemType) || Util.equals("Y", ItemType)
					|| Util.equals("X", ItemType)) {
				continue;
			}
			if (Util.equals("N", realRpFg)
					&& Util.equals("B008", l170m01d.getItemNo())) {
				continue;
			}
			
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202406)
					&& Util.equals(Util.trim(l170m01a.getRealRpFg()), "Y")
					&& Util.equals("A005", l170m01d.getItemNo())) {
				// J-113-0204 新增及修正說明文句
				// 實地覆審表，沒有該項目
				continue;
			}

			// J-108-0888_05097_B1001
			// int itemSeq = l170m01d.getItemSeq();
			int itemSeq = this.getItemSeqForShow(l170m01a, l170m01d);

			if (Util.notEquals(rptId, "")
					&& LrsUtil.compareRptVersion(rptId, ">=",
							LrsUtil.V_O_201907)) {
				// itemSeq 已經變成 itemSeqShow,所以不用再特別處理
			} else {
				if (itemSeq >= 12) {
					itemSeq = itemSeq - 1;
				}
			}

			if (countTable == 0) {
				stab1.append("<tr height=\"1px\">");
				countTable++;
			} else {
				stab1.append("<tr>");
			}

			if (Util.equals("", nowType) || Util.notEquals(nowType, ItemType)) {
				int rowspan = (Util.equals("B", ItemType) ? typeJson
						.getInt(ItemType) + 1 : typeJson.getInt(ItemType));
				nowType = ItemType;
				stab1.append("<td rowspan=\""
						+ rowspan
						+ "\" style=\"text-align:center;border: 1px solid rgb(0, 0, 0); width: 2%; vertical-align: middle;text-align: center;\">");
				if (Util.equals("A", ItemType)) {
					if (Util.equals("en", locale)
							&& Util.equals(UtilConstants.Country.加拿大,
									branchtype.getCountryType())) {
						stab1.append("<span style=\"display: inline-block; width: 100%;\">"
								+ rptProperties
										.getProperty("TITLE.RPTTITLE03_CA")
								+ "</span>");
					} else {
						stab1.append("<span style=\"display: inline-block; width: 100%;\">"
								+ rptProperties.getProperty("TITLE.RPTTITLE03")
								+ "</span>");
					}
				} else if (Util.equals("B", ItemType)) {
					if (Util.equals("en", locale)
							&& Util.equals(UtilConstants.Country.加拿大,
									branchtype.getCountryType())) {
						stab1.append("<span style=\"display: inline-block; width: 100%;\">"
								+ rptProperties
										.getProperty("TITLE.RPTTITLE04_CA")
								+ "</span>");
					} else {
						stab1.append("<span style=\"display: inline-block; width: 100%;\">"
								+ rptProperties.getProperty("TITLE.RPTTITLE04")
								+ "</span>");
					}
				} else if (Util.equals("C", ItemType)) {
					stab1.append("<span style=\"display: inline-block; width: 100%;\">"
							+ rptProperties.getProperty("TITLE.RPTTITLE05")
							+ "</span>");
				}
				stab1.append("</td>");
			}

			// J-108-0888_05097_B1001
			if (Util.notEquals(rptId, "")
					&& LrsUtil.compareRptVersion(rptId, ">=",
							LrsUtil.V_O_201907)) {

				// B007-> 11-1.中長期放款（含土建融之案件）之申貸計劃及其自籌款是否照預定進度與核定條件執行？
				// B008->
				// 11-2.土地融資與建築融資案件實地覆審結果，其不動產開發與興建計畫、工程進度及建案銷售等情形是否與核定條件相符。（須另勾選附表）
				// B013-> 16.借戶是否依照約定條件履行？（核定條件若有「應檢視事項」或「承諾事項」須另勾選附表）
				// B009-> 12.工程預付款及/或履約保證其工程進度及履約情形是否正常？
				// B024-> 12-1.XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
				if (Util.equals("B007", l170m01d.getItemNo())
						|| Util.equals("B013", l170m01d.getItemNo())
						|| Util.equals("B009", l170m01d.getItemNo())
						|| Util.equals("B015", l170m01d.getItemNo())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 2%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals("B008", l170m01d.getItemNo())
						|| Util.equals("B024", l170m01d.getItemNo())
						|| Util.equals("B025", l170m01d.getItemNo())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 2%; vertical-align: top;text-align: center;border-top-style:none;\">");
				} else {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 2%; vertical-align: middle;text-align: center;\">");
				}
				String seq = "";
				seq = String.valueOf(itemSeq);
				
				if (LrsUtil.compareRptVersion(rptId, ">=", LrsUtil.V_O_202406)) {
					if (Util.equals("A005", l170m01d.getItemNo())) {
						seq = l170m01d.getItemSeqShow();
					}
				}
				
				// if (Util.equals(11, l170m01d.getItemSeq())) {
				// seq = (Util.equals("N", realRpFg) ? "11" : "11-1");
				// } else if (Util.equals(12, l170m01d.getItemSeq())) {
				// seq = "11-2";
				// } else {
				// seq = String.valueOf(itemSeq);
				// }

				if (Util.equals("B008", l170m01d.getItemNo())
						|| Util.equals("B024", l170m01d.getItemNo())
						|| Util.equals("B025", l170m01d.getItemNo())) {
					// 拆項不要列印項次
					stab1.append("<span style=\"display: inline-block; width: 100%;\">"
							+ "" + "</span>");
				} else {
					stab1.append("<span style=\"display: inline-block; width: 100%;\">"
							+ seq + "</span>");
				}

			} else {

				// 11->B007-> 11-1.中長期放款（含土建融之案件）之申貸計劃及其自籌款是否照預定進度與核定條件執行？
				// 12->B008->
				// 11-2.土地融資與建築融資案件實地覆審結果，其不動產開發與興建計畫、工程進度及建案銷售等情形是否與核定條件相符。（須另勾選附表）
				// 17->B013-> 16.借戶是否依照約定條件履行？（核定條件若有「應檢視事項」或「承諾事項」須另勾選附表）

				if (Util.equals(11, l170m01d.getItemSeq())
						|| Util.equals(17, l170m01d.getItemSeq())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 2%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals(12, l170m01d.getItemSeq())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 2%; vertical-align: middle;text-align: center;border-top-style:none;\">");
				} else {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 2%; vertical-align: middle;text-align: center;\">");
				}
				String seq = "";
				if (Util.equals(11, l170m01d.getItemSeq())) {
					seq = (Util.equals("N", realRpFg) ? "11" : "11-1");
				} else if (Util.equals(12, l170m01d.getItemSeq())) {
					seq = "11-2";
				} else {
					seq = String.valueOf(itemSeq);
				}
				stab1.append("<span style=\"display: inline-block; width: 100%;\">"
						+ seq + "</span>");
			}

			stab1.append("</td>");

			// J-108-0888_05097_B1001
			if (Util.notEquals(rptId, "")
					&& LrsUtil.compareRptVersion(rptId, ">=",
							LrsUtil.V_O_201907)) {

				// B007-> 11-1.中長期放款（含土建融之案件）之申貸計劃及其自籌款是否照預定進度與核定條件執行？
				// B008->
				// 11-2.土地融資與建築融資案件實地覆審結果，其不動產開發與興建計畫、工程進度及建案銷售等情形是否與核定條件相符。（須另勾選附表）
				// B013-> 16.借戶是否依照約定條件履行？（核定條件若有「應檢視事項」或「承諾事項」須另勾選附表）
				// B009-> 12.工程預付款及/或履約保證其工程進度及履約情形是否正常？
				// B024-> 12-1.XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

				if (Util.equals("B007", l170m01d.getItemNo())
						|| Util.equals("B013", l170m01d.getItemNo())
						|| Util.equals("B009", l170m01d.getItemNo())
						|| Util.equals("B015", l170m01d.getItemNo())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 76%; vertical-align: middle;border-bottom-style:none;\">");
				} else if (Util.equals("B008", l170m01d.getItemNo())
						|| Util.equals("B024", l170m01d.getItemNo())
						|| Util.equals("B025", l170m01d.getItemNo())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 76%; vertical-align: top;border-top-style:none;\">");
				} else {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 76%; vertical-align: middle;\">");
				}
			} else {

				// 11->B007-> 11-1.中長期放款（含土建融之案件）之申貸計劃及其自籌款是否照預定進度與核定條件執行？
				// 12->B008->
				// 11-2.土地融資與建築融資案件實地覆審結果，其不動產開發與興建計畫、工程進度及建案銷售等情形是否與核定條件相符。（須另勾選附表）
				// 17->B013-> 16.借戶是否依照約定條件履行？（核定條件若有「應檢視事項」或「承諾事項」須另勾選附表）

				if (Util.equals(11, l170m01d.getItemSeq())
						|| Util.equals(17, l170m01d.getItemSeq())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 76%; vertical-align: middle;border-bottom-style:none;\">");
				} else if (Util.equals(12, l170m01d.getItemSeq())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 76%; vertical-align: middle;border-top-style:none;\">");
				} else {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 76%; vertical-align: middle;\">");
				}
			}

			if (Util.equals("A002", l170m01d.getItemNo())) {
				if (LrsUtil.compareRptVersion(rptId, ">=", LrsUtil.V_O_202210)) {
					String noStr = rptProperties.getProperty("RPTSHOW.NO");
					String chkStr = rptProperties
							.getProperty("L170M01D.CHKCHECKA002");
					if (Util.equals("en", locale)) {
						if (Util.equals(UtilConstants.Country.加拿大,
								branchtype.getCountryType())) {
							chkStr = rptProperties
									.getProperty("L170M01D.v4CHKCHECKA002_CA");
							noStr = rptProperties.getProperty("label.N");
						} else {
							chkStr = rptProperties
									.getProperty("L170M01D.v4CHKCHECKA002");
						}
					}
					stab1.append("<span style=\"display: inline-block; width: 100%;\">"
							+ l170m01d.getItemContent()
							+ "("
							+ (Util.equals("N", l170m01d.getChkCheck()) ? noStr
									: "")
							+ (Util.equals("en", locale) ? "　" : "")
							+ chkStr
							+ ")" + "</span>");
				} else {
					stab1.append("<span style=\"display: inline-block; width: 100%;\">"
							+ l170m01d.getItemContent()
							+ "("
							+ (Util.equals("N", l170m01d.getChkCheck()) ? rptProperties
									.getProperty("label.N") : "")
							+ (Util.equals("en", locale) ? "　" : "")
							+ rptProperties
									.getProperty("L170M01D.CHKCHECKA002")
							+ ")"
							+ "</span>");
				}
			} else {
				stab1.append("<span style=\"display: inline-block; width: 100%;\">"
						+ l170m01d.getItemContent() + "</span>");
			}
			stab1.append("</td>");

			// J-108-0888_05097_B1001
			if (Util.notEquals(rptId, "")
					&& LrsUtil.compareRptVersion(rptId, ">=",
							LrsUtil.V_O_201907)) {
				// B007-> 11-1.中長期放款（含土建融之案件）之申貸計劃及其自籌款是否照預定進度與核定條件執行？
				// B008->
				// 11-2.土地融資與建築融資案件實地覆審結果，其不動產開發與興建計畫、工程進度及建案銷售等情形是否與核定條件相符。（須另勾選附表）
				// B013-> 16.借戶是否依照約定條件履行？（核定條件若有「應檢視事項」或「承諾事項」須另勾選附表）
				// B009-> 12.工程預付款及/或履約保證其工程進度及履約情形是否正常？
				// B024-> 12-1.XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

				if (Util.equals("B007", l170m01d.getItemNo())
						|| Util.equals("B013", l170m01d.getItemNo())
						|| Util.equals("B009", l170m01d.getItemNo())
						|| Util.equals("B015", l170m01d.getItemNo())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals("B008", l170m01d.getItemNo())
						|| Util.equals("B024", l170m01d.getItemNo())
						|| Util.equals("B025", l170m01d.getItemNo())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: top;text-align: center;border-top-style:none;\">");
				} else {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;\">");
				}

			} else {
				// 11->B007-> 11-1.中長期放款（含土建融之案件）之申貸計劃及其自籌款是否照預定進度與核定條件執行？
				// 12->B008->
				// 11-2.土地融資與建築融資案件實地覆審結果，其不動產開發與興建計畫、工程進度及建案銷售等情形是否與核定條件相符。（須另勾選附表）
				// 17->B013-> 16.借戶是否依照約定條件履行？（核定條件若有「應檢視事項」或「承諾事項」須另勾選附表）
				if (Util.equals(11, l170m01d.getItemSeq())
						|| Util.equals(17, l170m01d.getItemSeq())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals(12, l170m01d.getItemSeq())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-top-style:none;\">");
				} else {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;\">");
				}
			}

			String ra = "";
			String r1 = "";
			String rb = "";
			String r2 = "";
			if (Util.equals("B016", l170m01d.getItemNo())) { // 前次覆審有無應行改善事項？
																// 【無|有|－】
				if (Util.equals("en", locale)) {
					ra = rptProperties.getProperty("label.N");
					rb = rptProperties.getProperty("label.Y");
				} else {
					ra = rptProperties.getProperty("label.N2");
					rb = rptProperties.getProperty("label.Y2");
				}
				if (Util.equals("N", l170m01d.getChkResult())) {
					r1 = "V";
				} else if (Util.equals("Y", l170m01d.getChkResult())) {
					r2 = "V";
				} else if (Util.equals("K", l170m01d.getChkResult())) {
					r1 = "&ndash;"; // -
					r2 = "&ndash;";
				}
			} else if (Util.equals("B019", l170m01d.getItemNo())) { // 擔保物是否發生變化，致影響本行債權？【否|是|－】
				ra = rptProperties.getProperty("label.N");
				rb = rptProperties.getProperty("label.Y");
				if (Util.equals("N", l170m01d.getChkResult())) {
					r1 = "V";
				} else if (Util.equals("Y", l170m01d.getChkResult())) {
					r2 = "V";
				} else if (Util.equals("K", l170m01d.getChkResult())) {
					r1 = "&ndash;";
					r2 = "&ndash;";
				}
			} else {
				ra = rptProperties.getProperty("label.Y");
				rb = rptProperties.getProperty("label.N");
				if (Util.equals("Y", l170m01d.getChkResult())) {
					r1 = "V";
				} else if (Util.equals("N", l170m01d.getChkResult())) {
					r2 = "V";
				} else if (Util.equals("K", l170m01d.getChkResult())) {
					r1 = "&ndash;";
					r2 = "&ndash;";
				}
			}
			stab1.append("<span>" + ra + "</span>");
			stab1.append("</td>");

			// J-108-0888_05097_B1001
			if (Util.notEquals(rptId, "")
					&& LrsUtil.compareRptVersion(rptId, ">=",
							LrsUtil.V_O_201907)) {
				// B007-> 11-1.中長期放款（含土建融之案件）之申貸計劃及其自籌款是否照預定進度與核定條件執行？
				// B008->
				// 11-2.土地融資與建築融資案件實地覆審結果，其不動產開發與興建計畫、工程進度及建案銷售等情形是否與核定條件相符。（須另勾選附表）
				// B013-> 16.借戶是否依照約定條件履行？（核定條件若有「應檢視事項」或「承諾事項」須另勾選附表）
				// B009-> 12.工程預付款及/或履約保證其工程進度及履約情形是否正常？
				// B024-> 12-1.XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

				if (Util.equals("B007", l170m01d.getItemNo())
						|| Util.equals("B013", l170m01d.getItemNo())
						|| Util.equals("B009", l170m01d.getItemNo())
						|| Util.equals("B015", l170m01d.getItemNo())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals("B008", l170m01d.getItemNo())
						|| Util.equals("B024", l170m01d.getItemNo())
						|| Util.equals("B025", l170m01d.getItemNo())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: top;text-align: center;border-top-style:none;\">");
				} else {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;\">");
				}
				stab1.append("<span>" + r1 + "</span>");
				stab1.append("</td>");
				if (Util.equals("B007", l170m01d.getItemNo())
						|| Util.equals("B013", l170m01d.getItemNo())
						|| Util.equals("B009", l170m01d.getItemNo())
						|| Util.equals("B015", l170m01d.getItemNo())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals("B008", l170m01d.getItemNo())
						|| Util.equals("B024", l170m01d.getItemNo())
						|| Util.equals("B025", l170m01d.getItemNo())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: top;text-align: center;border-top-style:none;\">");
				} else {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;\">");
				}
				stab1.append("<span>" + rb + "</span>");
				stab1.append("</td>");
				if (Util.equals("B007", l170m01d.getItemNo())
						|| Util.equals("B013", l170m01d.getItemNo())
						|| Util.equals("B009", l170m01d.getItemNo())
						|| Util.equals("B015", l170m01d.getItemNo())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals("B008", l170m01d.getItemNo())
						|| Util.equals("B024", l170m01d.getItemNo())
						|| Util.equals("B025", l170m01d.getItemNo())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: top;text-align: center;border-top-style:none;\">");
				} else {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;\">");
				}

			} else {
				// 11->B007-> 11-1.中長期放款（含土建融之案件）之申貸計劃及其自籌款是否照預定進度與核定條件執行？
				// 12->B008->
				// 11-2.土地融資與建築融資案件實地覆審結果，其不動產開發與興建計畫、工程進度及建案銷售等情形是否與核定條件相符。（須另勾選附表）
				// 17->B013-> 16.借戶是否依照約定條件履行？（核定條件若有「應檢視事項」或「承諾事項」須另勾選附表）
				if (Util.equals(11, l170m01d.getItemSeq())
						|| Util.equals(17, l170m01d.getItemSeq())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals(12, l170m01d.getItemSeq())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-top-style:none;\">");
				} else {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;\">");
				}
				stab1.append("<span>" + r1 + "</span>");
				stab1.append("</td>");
				if (Util.equals(11, l170m01d.getItemSeq())
						|| Util.equals(17, l170m01d.getItemSeq())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals(12, l170m01d.getItemSeq())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-top-style:none;\">");
				} else {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;\">");
				}
				stab1.append("<span>" + rb + "</span>");
				stab1.append("</td>");
				if (Util.equals(11, l170m01d.getItemSeq())
						|| Util.equals(17, l170m01d.getItemSeq())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals(12, l170m01d.getItemSeq())) {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-top-style:none;\">");
				} else {
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;\">");
				}
			}

			stab1.append("<span>" + r2 + "</span>");
			stab1.append("</td>");

			// 覆審結果備註 itemRows
			if (Util.equals("A", ItemType) && countType == 0) {
				int totalItemRows = itemRows + 1;
				stab1.append("<td rowspan=\""
						+ totalItemRows
						+ "\" style=\"border: 1px solid rgb(0, 0, 0); width: 20%; vertical-align: top;\">");
				stab1.append("<span>" + StringUtils.join(strList, "<br><br>")
						+ "</span>");
				stab1.append("</td>");
				countType++;
			}

			if (Util.equals("B013", l170m01d.getItemNo())) { // 若有違反承諾或約定事項是否依核定條件處置？
				String R = "K";
				L170M01D X113 = l170m01dDao.findByUniqueKey(
						l170m01d.getMainId(), l170m01d.getCustId(),
						l170m01d.getDupNo(), "X113");
				L170M01D X213 = l170m01dDao.findByUniqueKey(
						l170m01d.getMainId(), l170m01d.getCustId(),
						l170m01d.getDupNo(), "X213");
				if (X113 != null && X213 != null) {
					String x113 = Util.trim(X113.getChkResult());
					String x213 = Util.trim(X213.getChkResult());

					if (Util.equals(x113, "N") || Util.equals(x213, "N")) {
						R = "N";
					} else if (Util.equals(x113, "Y") || Util.equals(x213, "Y")) {
						R = "Y";
					} else {
						R = "K";
					}
				}
				ra = "";
				r1 = "";
				rb = "";
				r2 = "";
				ra = rptProperties.getProperty("label.Y");
				rb = rptProperties.getProperty("label.N");
				if (Util.equals("Y", R)) {
					r1 = "V";
				} else if (Util.equals("N", R)) {
					r2 = "V";
				} else if (Util.equals("K", R)) {
					r1 = "&ndash;";
					r2 = "&ndash;";
				}
				String Con = (Util.equals("en", locale)
						&& Util.equals(UtilConstants.Country.加拿大,
								branchtype.getCountryType()) ? rptProperties
						.getProperty("L170M01D.ITEMB013_CA") : rptProperties
						.getProperty("L170M01D.ITEMB013"));
				stab1.append("<tr>");
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 2%; vertical-align: middle;text-align: center;border-top-style:none;\">");
				stab1.append("<span style=\"display: inline-block; width: 100%;\"></span>");
				stab1.append("</td>");
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 76%; vertical-align: middle;border-top-style:none;\">");
				stab1.append("<span style=\"display: inline-block; width: 100%;\">"
						+ Con + "</span>");
				stab1.append("</td>");
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-top-style:none;\">");
				stab1.append("<span>" + ra + "</span>");
				stab1.append("</td>");
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-top-style:none;\">");
				stab1.append("<span>" + r1 + "</span>");
				stab1.append("</td>");
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-top-style:none;\">");
				stab1.append("<span>" + rb + "</span>");
				stab1.append("</td>");
				stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: 3%; vertical-align: middle;text-align: center;border-top-style:none;\">");
				stab1.append("<span>" + r2 + "</span>");
				stab1.append("</td>");
				stab1.append("</tr>");
			}
		}

		stab1.append("</tr>");
		stab1.append("</tbody>");
		stab1.append("</table>");
		rptVariableMap.put("strTable", stab1.toString());
		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(L170M01B)
	 * 
	 * @param titleRows
	 *            存放變數list
	 * @param l170m01bList
	 *            L170M01B資料
	 * @param result
	 *            是否只顯示三筆
	 * @return List<Map<String, String>> titleRows
	 */
	private List<Map<String, String>> setL170M01BData(
			List<Map<String, String>> titleRows, List<L170M01B> l170m01bList,
			boolean result, Map<String, String> yesNoMap,
			Map<String, String> subItemMap, Properties rptProperties,
			Map<String, String> newCaseMap) {
		Map<String, String> map = Util.setColumnMap();
		map.put("ReportBean.column01", "L");// title 列
		titleRows.add(map);

		BigDecimal thu = new BigDecimal(1000);
		int count = 0;
		if (CollectionUtils.isNotEmpty(l170m01bList)) {
			for (L170M01B l170m01b : l170m01bList) {
				if (result && count >= 2) {
					count++;
				} else if (!result && count < 2) {
					count++;
				} else {
					map = Util.setColumnMap();
					map.put("ReportBean.column01", "Y");

					map.put("ReportBean.column02",
							l170m01b.getBalAmt() == null ? "" : Util
									.trim(l170m01b.getQuotaCurr()));
					map.put("ReportBean.column03", NumConverter.addComma(
							l170m01b.getQuotaAmt() == null ? BigDecimal.ZERO
									: l170m01b
											.getQuotaAmt()
											.divide(thu)
											.setScale(2,
													BigDecimal.ROUND_HALF_UP),
							"#,##0.00"));
					map.put("ReportBean.column04",
							l170m01b.getBalAmt() == null ? "" : Util
									.trim(l170m01b.getBalCurr()));
					map.put("ReportBean.column05", NumConverter.addComma(
							l170m01b.getBalAmt() == null ? BigDecimal.ZERO
									: l170m01b
											.getBalAmt()
											.divide(thu)
											.setScale(2,
													BigDecimal.ROUND_HALF_UP),
							"#,##0.00"));
					map.put("ReportBean.column06",
							Util.trim(l170m01b.getSubject()));
					map.put("ReportBean.column07",
							Util.trim(l170m01b.getCntrNo()));
					map.put("ReportBean.column08",
							Util.trim(TWNDate.toAD(l170m01b.getFromDate())));
					map.put("ReportBean.column09",
							Util.trim(TWNDate.toAD(l170m01b.getEndDate())));
					// J-111-0326 海外覆審作業系統改良第一階段： 4-1. 顯示新貸/舊案
					map.put("ReportBean.column10", Util.trim(newCaseMap
							.get(Util.nullToSpace(l170m01b.getNewCase()))));
					// Util.equals("Y", l170m01b.getNewCase()) ? "ｖ" : "");

					List<String> ls = new ArrayList<String>();
					if (Util.isNotEmpty(l170m01b.getGuaranteeName())) {
						ls.add(l170m01b.getGuaranteeName());
					}
					if (Util.isNotEmpty(l170m01b.getMajorMemo())) {
						ls.add(l170m01b.getMajorMemo());
					}
					map.put("ReportBean.column11", StringUtils.join(ls, "\n"));

					// 當為 無擔保品/同上, 不印出 估值/押值
					if (Util.notEquals(
							rptProperties.getProperty("L170M01B.v2001"),
							l170m01b.getGuaranteeName())
							&& Util.notEquals(
									rptProperties.getProperty("L170M01B.v2002"),
									l170m01b.getGuaranteeName())) {
						if (l170m01b.getEstAmt() != null
								&& Util.isNotEmpty(Util.trim(l170m01b
										.getEstCurr()))) {
							map.put("ReportBean.column13",
									Util.trim(l170m01b.getEstCurr()));
							map.put("14", NumConverter.addComma(
									l170m01b.getEstAmt()
											.divide(thu)
											.setScale(2,
													BigDecimal.ROUND_HALF_UP),
									"#,##0.00"));
						}
						if (l170m01b.getLoanAmt() != null
								&& Util.isNotEmpty(Util.trim(l170m01b
										.getLoanCurr()))) {
							map.put("ReportBean.column15",
									Util.trim(l170m01b.getLoanCurr()));
							map.put("ReportBean.column16",
									NumConverter
											.addComma(
													l170m01b.getLoanAmt()
															.divide(thu)
															.setScale(
																	2,
																	BigDecimal.ROUND_HALF_UP),
													"#,##0.00"));
						}
					}
					titleRows.add(map);
					count++;
				}
			}
		} else {
			if (result) {
				for (int i = count; i < 1; i++) {
					map = Util.setColumnMap();
					map.put("ReportBean.column01", "Y");
					titleRows.add(map);
				}
			}
		}
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "N");// 合計列
		titleRows.add(map);

		return titleRows;
	}

	/**
	 * 產生LMS1705R03的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @return outputstream outputstream
	 * @throws Exception
	 */
	public OutputStream genLMS1705R03(String mainId, Locale locale,
			Properties rptProperties) throws Exception {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();

		L170M01A l170m01a = l170m01aDao.findByMainId(mainId);
		List<L170M01D> l170m01dList = l170m01dDao.findByMainId(mainId);

		
		String rtpFileVer = "201809";
		if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
				LrsUtil.V_O_202404)) {
			// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
			rtpFileVer = "202404";
		}
		
		ReportGenerator generator = new ReportGenerator(
				"report/lrs/LMS1705R03_" + rtpFileVer +"_"+ locale.toString() + ".rpt");
		OutputStream outputStream = null;
		Map<String, String> typCdMap = null;

		try {
			typCdMap = codetypeservice.findByCodeType("TypCd",
					locale.toString());
			String revisedDate = "";

			 if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
						LrsUtil.V_O_202406)) {
				    // J-113-0204  新增及修正說明文句
					revisedDate = rptProperties.getProperty("revisedDateR03."
							+ l170m01a.getRptId());
					
					if(Util.equals("Y", l170m01a.getRealRpFg())){
						revisedDate = rptProperties.getProperty("revisedDateR03.Ver202404");
					}
					
			} else if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202404)) {
				// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
				revisedDate = rptProperties.getProperty("revisedDateR03."
						+ l170m01a.getRptId());
			}		
			// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
			else if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202210)) {
				revisedDate = rptProperties.getProperty("revisedDate."
						+ l170m01a.getRptId());
			} else {
				revisedDate = rptProperties.getProperty("revisedDate");
			}
			rptVariableMap.put("revisedDateStr", revisedDate);
			rptVariableMap.put("locale", locale.toString());
			rptVariableMap.put("COUNTRYTYPE", branchtype.getCountryType());
			rptVariableMap.put("L170M01A.RETRIALDATE",
					Util.trim(TWNDate.toAD(l170m01a.getRetrialDate())));
			rptVariableMap.put("L170M01A.PROJECTNO",
					Util.trim(l170m01a.getProjectNo()));
			rptVariableMap.put(
					"CUST_INFO",
					"("
							+ Util.nullToSpace(typCdMap.get(Util
									.nullToSpace(l170m01a.getTypCd()))) + ")"
							+ " " + Util.trim(l170m01a.getCustId()) + " "
							+ Util.trim(l170m01a.getCustName()));
			rptVariableMap.put("L170M01A.REALRPFG",
					Util.trim(l170m01a.getRealRpFg()));
					
			rptVariableMap.put("CHAPTER_NUM1",
					rptProperties.getProperty("label.num_1"));
			rptVariableMap.put("CHAPTER_NUM2",
					rptProperties.getProperty("label.num_2"));
			rptVariableMap.put("CHAPTER_NUM3",
					rptProperties.getProperty("label.num_3"));

			Map<String, L170M01D> map = new HashMap<String, L170M01D>();
			for (L170M01D i : l170m01dList)
				map.put(i.getItemNo(), i);

			ArrayList<String> arrayHide = new ArrayList<String>();
			ArrayList<String> chk_YN = new ArrayList<String>(Arrays.asList(
					"Y124", "Y210", "X110", "X210"));

			// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202210)) {
				// Y210改Y21A、新增Y22A
				chk_YN = new ArrayList<String>(Arrays.asList("Y124", "Y21A",
						"Y22A", "X110", "X210"));
			}
			// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202307)) {
				chk_YN = new ArrayList<String>(Arrays.asList("Y124", "Y21A",
						"Y22A", "X110", "X210"));
				chk_YN.add("Y236");
				chk_YN.add("Y238");
				chk_YN.add("Y239");
			}

			for (String itemNo : chk_YN) {
				L170M01D itemData = map.get(itemNo);
				if (Util.equals("X110", itemNo) && itemData != null) {
					if (Util.equals("N", itemData.getChkResult())) {
						arrayHide.add("X111");
						arrayHide.add("X112");
						arrayHide.add("X113");
					} else {
						L170M01D X111Data = map.get("X111");
						L170M01D X112Data = map.get("X112");
						if ((X111Data != null && Util.equals("Y",
								X111Data.getChkResult()))
								&& (X112Data != null && Util.equals("Y",
										X112Data.getChkResult()))) {
							arrayHide.add("X113");
						}
					}
				} else if (Util.equals("X210", itemNo) && itemData != null) {
					if (Util.equals("N", itemData.getChkResult())) {
						arrayHide.add("X211");
						arrayHide.add("X212");
						arrayHide.add("X213");
					} else {
						L170M01D X211Data = map.get("X211");
						L170M01D X212Data = map.get("X212");
						if ((X211Data != null && Util.equals("Y",
								X211Data.getChkResult()))
								&& (X212Data != null && Util.equals("Y",
										X212Data.getChkResult()))) {
							arrayHide.add("X213");
						}
					}
				} else if (Util.equals("Y124", itemNo) && itemData != null
						&& Util.equals("N", itemData.getChkResult())) {
					arrayHide.add("Y12A");
					arrayHide.add("Y12B");
					arrayHide.add("Y12C");
					arrayHide.add("Y12D");
					arrayHide.add("Y12E");
					arrayHide.add("Y12F");
				} else if ((Util.equals("Y210", itemNo) || Util.equals("Y21A",
						itemNo))
						&& itemData != null
						&& Util.equals("N", itemData.getChkResult())) {
					arrayHide.add("Y211");
					arrayHide.add("Y212");
					arrayHide.add("Y213");
					
					if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
							LrsUtil.V_O_202406)) {
						arrayHide.add("Y214");
						arrayHide.add("Y215");
					}
				} else if (Util.equals("Y22A", itemNo) && itemData != null
						&& Util.equals("N", itemData.getChkResult())) {
					arrayHide.add("Y221");
				}
				// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
				else if (Util.equals("Y236", itemNo) && itemData != null
						&& Util.equals("N", itemData.getChkResult())) {
					arrayHide.add("Y23I");
					arrayHide.add("Y23J");
					arrayHide.add("Y23K");
				} else if (Util.equals("Y238", itemNo) && itemData != null
						&& Util.equals("N", itemData.getChkResult())) {
					arrayHide.add("Y23L");
					arrayHide.add("Y23M");
				} else if (Util.equals("Y239", itemNo) && itemData != null
						&& Util.equals("N", itemData.getChkResult())) {
					arrayHide.add("Y23N");
					arrayHide.add("Y23O");
				}

			}

			// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202307)) {

				if (map.containsKey("Y234")) {
					L170M01D itemData = map.get("Y234");
					if (itemData != null
							&& Util.equals("Y", itemData.getChkResult())) {
						arrayHide.add("Y23F");
						arrayHide.add("Y23G");
						arrayHide.add("Y23H");
					} else if (itemData != null
							&& Util.equals("N", itemData.getChkResult())) {
						arrayHide.add("Y23E");

					} else if (itemData != null
							&& Util.equals("K", itemData.getChkResult())) {
						arrayHide.add("Y23E");
						arrayHide.add("Y23F");
						arrayHide.add("Y23G");
						arrayHide.add("Y23H");

					}
				}

				if (map.containsKey("Y230")) {
					L170M01D itemData = map.get("Y230");

					if (itemData != null
							&& Util.equals("K", itemData.getChkResult())) {
						arrayHide.add("Y231");
						arrayHide.add("Y23A");
						arrayHide.add("Y23B");
						arrayHide.add("Y232");
						arrayHide.add("Y23C");
						arrayHide.add("Y23D");
						arrayHide.add("Y233");
						arrayHide.add("Y234");
						arrayHide.add("Y23E");
						arrayHide.add("Y23F");
						arrayHide.add("Y23G");
						arrayHide.add("Y23H");
						arrayHide.add("Y235");
						arrayHide.add("Y236");
						arrayHide.add("Y23I");
						arrayHide.add("Y23J");
						arrayHide.add("Y23K");
						arrayHide.add("Y237");
						arrayHide.add("Y238");
						arrayHide.add("Y23L");
						arrayHide.add("Y23M");
						arrayHide.add("Y239");
						arrayHide.add("Y23N");
						arrayHide.add("Y23O");
					}
				}

			}
			
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202406)) {
				// J-113-0204 新增及修正說明文句
				
				if (map.containsKey("Y310")) {
					L170M01D itemData = map.get("Y310");

					if (itemData != null
							&& (Util.equals("K", itemData.getChkResult()) || Util
									.equals("Y", itemData.getChkResult()))) {
						arrayHide.add("Y31A");
					}
				}
				
				if (Util.equals("Y", l170m01a.getRealRpFg())) {
				    arrayHide.add("Y113");
					arrayHide.add("Y240");
					arrayHide.add("Y250");
					arrayHide.add("Y214");
					arrayHide.add("Y215");
					arrayHide.add("Y300");
					arrayHide.add("Y310");
					arrayHide.add("Y31A");
					arrayHide.add("Y320");
					arrayHide.add("Y330");
					arrayHide.add("Y33A");
					arrayHide.add("Y33B");
					arrayHide.add("Y33C");
					arrayHide.add("Y33D");
				}
			}
			
			ArrayList<String> TAB_0 = new ArrayList<String>(Arrays.asList(
					"Z000", "Y000", "X000"));
			ArrayList<String> TAB_1 = new ArrayList<String>(Arrays.asList(
					"Z100", "Z200", "Z300", "Z400", "Y100", "Y200", "X100",
					"X200"));
			ArrayList<String> TAB_2 = new ArrayList<String>(Arrays.asList(
					"Y110", "Y120", "Y130", "Y210", "X110", "X210"));
			ArrayList<String> TAB_3 = new ArrayList<String>(Arrays.asList(
					"Y111", "Y112", "Y121", "Y122", "Y123", "Y124", "Y131",
					"Y132", "Y133", "Y134", "Y135", "Y211", "Y212", "Y213",
					"X111", "X112", "X113", "X211", "X212", "X213"));
			ArrayList<String> TAB_4 = new ArrayList<String>(Arrays.asList(
					"Y12A", "Y12B", "Y12C", "Y12D", "Y12E", "Y12F"));

			// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
			ArrayList<String> TAB_5 = new ArrayList<String>();

			ArrayList<String> chk_3 = new ArrayList<String>(Arrays.asList(
					"Z100", "Z200", "Z300", "Z400", "Y211", "Y212", "Y213",
					"X113", "X211", "X212", "X213")); // 是 否 －
			
			ArrayList<String> chk_2 = new ArrayList<String>(Arrays.asList(
					"Y111", "Y112", "Y121", "Y122", "Y123", "Y12A", "Y12B",
					"Y12C", "Y12D", "Y12E", "Y12F", "Y131", "Y132", "Y133",
					"Y134", "Y135", "X111", "X112")); // 是 否

			// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
			ArrayList<String> chk_4 = new ArrayList<String>(); // 有 無

			// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202210)) {
				// Y121:chk_2改chk_3、Y210改Y21A、新增Y220.Y22A.Y221
				// J-111-0326 海外覆審作業系統改良第一階段： 7. Y135:chk_2改chk_3
				TAB_2 = new ArrayList<String>(Arrays.asList("Y110", "Y120",
						"Y130", "Y210", "Y21A", "Y220", "Y22A", "X110", "X210"));
				TAB_3 = new ArrayList<String>(Arrays.asList("Y111", "Y112",
						"Y121", "Y122", "Y123", "Y124", "Y131", "Y132", "Y133",
						"Y134", "Y135", "Y211", "Y212", "Y213", "Y221", "X111",
						"X112", "X113", "X211", "X212", "X213"));
				chk_3 = new ArrayList<String>(Arrays.asList("Z100", "Z200",
						"Z300", "Z400", "Y121", "Y211", "Y212", "Y213", "X113",
						"Y135", "X211", "X212", "X213")); // 是 否 －
				chk_2 = new ArrayList<String>(Arrays.asList("Y111", "Y112",
						"Y122", "Y123", "Y12A", "Y12B", "Y12C", "Y12D", "Y12E",
						"Y12F", "Y131", "Y132", "Y133", "Y134", "Y221", "X111",
						"X112")); // 是 否
			}

			// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202307)) {

				TAB_2 = new ArrayList<String>(Arrays.asList("Y110", "Y120",
						"Y130", "Y210", "Y21A", "Y220", "Y22A", "Y230", "X110",
						"X210"));
				TAB_3 = new ArrayList<String>(Arrays.asList("Y111", "Y112",
						"Y121", "Y122", "Y123", "Y124", "Y131", "Y132", "Y133",
						"Y134", "Y135", "Y211", "Y212", "Y213", "Y221", "Y231",
						"Y232", "Y233", "Y236", "Y237", "X111", "X112", "X113",
						"X211", "X212", "X213"));

				TAB_4 = new ArrayList<String>(Arrays.asList("Y12A", "Y12B",
						"Y12C", "Y12D", "Y12E", "Y12F", "Y23A", "Y23B", "Y23C",
						"Y23D", "Y234", "Y235", "Y23I", "Y23J", "Y23K", "Y238",
						"Y239"));

				TAB_5 = new ArrayList<String>(Arrays.asList("Y23E", "Y23F",
						"Y23G", "Y23H", "Y23L", "Y23M", "Y23N", "Y23O"));

				chk_3 = new ArrayList<String>(Arrays.asList("Z100", "Z200",
						"Z300", "Z400", "Y121", "Y211", "Y212", "Y213", "Y230",
						"Y234", "Y235", "Y23O", "X113", "Y135", "X211", "X212",
						"X213"));
				chk_2 = new ArrayList<String>(Arrays.asList("Y111", "Y112",
						"Y122", "Y123", "Y12A", "Y12B", "Y12C", "Y12D", "Y12E",
						"Y12F", "Y131", "Y132", "Y133", "Y134", "Y221", "Y23B",
						"Y23C", "Y23D", "Y23E", "Y23F", "Y23G", "Y23H", "Y236",
						"Y23I", "Y23J", "Y23K", "Y23L", "Y23M", "Y23N", "X111",
						"X112"));

				chk_4 = new ArrayList<String>(Arrays.asList("Y23A"));

			}
			
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202406)) {
				// J-113-0204  新增及修正說明文句
				TAB_1 = new ArrayList<String>(Arrays.asList(
						"Z100", "Z200", "Z300", "Z400", "Y100", "Y200","Y300", "X100",
						"X200"));								
				TAB_2 = new ArrayList<String>(Arrays.asList("Y110", "Y120",
						"Y130", "Y210", "Y21A", "Y220", "Y22A", "Y230",
						"Y240", "Y250", "Y310", "Y320", "Y330",
						"X110",
						"X210"));
				TAB_3 = new ArrayList<String>(Arrays.asList("Y111", "Y112","Y113",
						"Y121", "Y122", "Y123", "Y124", "Y131", "Y132", "Y133",
						"Y134", "Y135", "Y211", "Y212", "Y213", "Y214", "Y215",						
						"Y221", "Y231",
						"Y232", "Y233", "Y236", "Y237", 
						"Y33A", "Y33B", "Y33C", "Y33D",
						"X111", "X112", "X113",
						"X211", "X212", "X213"));

				TAB_4 = new ArrayList<String>(Arrays.asList("Y12A", "Y12B",
						"Y12C", "Y12D", "Y12E", "Y12F", "Y23A", "Y23B", "Y23C",
						"Y23D", "Y234", "Y235", "Y23I", "Y23J", "Y23K", "Y238",
						"Y239", "Y31A"));

				TAB_5 = new ArrayList<String>(Arrays.asList("Y23E", "Y23F",
						"Y23G", "Y23H", "Y23L", "Y23M", "Y23N", "Y23O"));

				chk_3 = new ArrayList<String>(Arrays.asList("Z100", "Z200",
						"Z300", "Z400", "Y121", "Y211", "Y212", "Y213",
						"Y214", "Y215",
						"Y230",
						"Y234", "Y235", "Y23O",
						"Y240", "Y250",
						"Y310", "Y31A", "Y320", "Y33A", "Y33B", "Y33C", "Y33D",
						"X113", "Y135", "X211", "X212",
						"X213"));
				chk_2 = new ArrayList<String>(Arrays.asList("Y111", "Y112", "Y113",
						"Y122", "Y123", "Y12A", "Y12B", "Y12C", "Y12D", "Y12E",
						"Y12F", "Y131", "Y132", "Y133", "Y134", "Y221", "Y23B",
						"Y23C", "Y23D", "Y23E", "Y23F", "Y23G", "Y23H", "Y236",
						"Y23I", "Y23J", "Y23K", "Y23L", "Y23M", "Y23N", "X111",
						"X112"));

				chk_4 = new ArrayList<String>(Arrays.asList("Y23A"));

			}

			List<String> rB008 = new ArrayList<String>();
			StringBuffer B008Memo = new StringBuffer("");
			List<String> rB011 = new ArrayList<String>();
			List<String> rB013 = new ArrayList<String>();
			for (L170M01D l170m01d : l170m01dList) {
				String itemType = l170m01d.getItemType();
				String itemNo = l170m01d.getItemNo();
				String itemContent = l170m01d.getItemContent();
				String chkText = l170m01d.getChkText();
				if (Util.notEquals("Z", itemType)
						&& Util.notEquals("Y", itemType)
						&& Util.notEquals("X", itemType)) {
					continue;
				}
				if (arrayHide.contains(itemNo)) {
					continue; // 隱藏項目
				}

				
				if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
						LrsUtil.V_O_202406)) {
					// J-113-0204 新增及修正說明文句
					if (Util.equals("Y", l170m01a.getRealRpFg())) {
						if (Util.equals("Y210", itemNo)) {
							itemContent = "1." + itemContent;
						}
						if (Util.equals("Y220", itemNo)) {
							itemContent = "2." + itemContent;
						}
						if (Util.equals("Y230", itemNo)) {
							itemContent = "3." + itemContent;
						}
					} else {
						if (Util.equals("Y240", l170m01d.getItemNo())) {
							itemContent = "1." + itemContent;
						}
						if (Util.equals("Y250", l170m01d.getItemNo())) {
							itemContent = "2." + itemContent;
						}
						if (Util.equals("Y210", l170m01d.getItemNo())) {
							itemContent = "3." + itemContent;
						}
						if (Util.equals("Y220", l170m01d.getItemNo())) {
							itemContent = "4." + itemContent;
						}
						if (Util.equals("Y230", l170m01d.getItemNo())) {
							itemContent = "5." + itemContent;
						}
					}
				}
				
				if (chk_YN.contains(itemNo)) {

					// J-112-0280
					// 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
					if (Util.equals(locale, "en")
							&& Util.equals("Y236", itemNo)) {
						itemContent = StringUtils.replace(
								itemContent,
								"有無",
								" "
										+ toBox("Y|N5", rptProperties,
												l170m01d.getChkResult(), 1)
										+ " ");
					} else if (Util.equals(locale, "en")
							&& Util.equals("Y124", itemNo)
							&& UtilConstants.Country.加拿大.equals(branchtype
									.getCountryType())) {
						itemContent = StringUtils.replace(
								itemContent,
								"有無",
								" "
										+ toBox("Y2_CA|N2_CA", rptProperties,
												l170m01d.getChkResult(), 1)
										+ " ");
					} else if (Util.equals(locale, "en")
							&& Util.equals("Y22A", itemNo)) {
						itemContent = StringUtils.replace(
								itemContent,
								"有無",
								" "
										+ toBox("Y7|N7", rptProperties,
												l170m01d.getChkResult(), 1)
										+ " ");
					} else {
						itemContent = StringUtils.replace(
								itemContent,
								"有無",
								" "
										+ toBox("Y2|N2", rptProperties,
												l170m01d.getChkResult(), 1)
										+ " ");
						if (UtilConstants.Country.加拿大.equals(branchtype
								.getCountryType())
								&& Util.equals(locale, "en")
								&& (Util.equals("X110", itemNo) || Util.equals(
										"X210", itemNo))) {
							itemContent = StringUtils.replace(itemContent,
									"1. ", " ( ") + " ):";
						}
					}
				} else if (chk_3.contains(itemNo)) {
					if (Util.equals("Z100", itemNo)
							|| Util.equals("Z200", itemNo)
							|| Util.equals("Z300", itemNo)
							|| Util.equals("Z400", itemNo)) {
						if (Util.equals(locale, "en")) {
							itemContent = itemContent
									+ " "
									+ toBox("Y8|N8|K5", rptProperties,
											l170m01d.getChkResult(), 1);
						} else {
							// J-111-0326 海外覆審作業系統改良第一階段： 3. 土建融實地覆審附表項目
							itemContent = itemContent
									+ " "
									+ toBox("Y|N|K5", rptProperties,
											l170m01d.getChkResult(), 1);
						}
					} else if (Util.equals("X113", itemNo)
							|| Util.equals("X211", itemNo)
							|| Util.equals("X212", itemNo)
							|| Util.equals("X213", itemNo)) {
						if (Util.equals("X211", itemNo)
								|| Util.equals("X212", itemNo)) {
							if (Util.equals(locale, "en")) {
								itemContent = itemContent
										+ " "
										+ StringUtils
												.replaceOnce(
														toBox("Y|N|K5",
																rptProperties,
																l170m01d.getChkResult(),
																1),
														"No",
														"No"
																+ rptProperties
																		.getProperty("label.chkText00"));
							} else {
								itemContent = itemContent
										+ " "
										+ StringUtils
												.replace(
														toBox("Y|N|K5",
																rptProperties,
																l170m01d.getChkResult(),
																1),
														"否",
														"否"
																+ rptProperties
																		.getProperty("label.chkText00"));
							}
						} else {
							itemContent = itemContent
									+ " "
									+ toBox("Y|N|K5", rptProperties,
											l170m01d.getChkResult(), 1);
						}
					} else if (Util.equals("Y135", itemNo)) {
						// J-111-0326 海外覆審作業系統改良第一階段： 7. Y135:chk_2改chk_3
						if (Util.equals(locale, "en")) {
							itemContent = itemContent
									+ " "
									+ toBox("Y4|N4|K5", rptProperties,
											l170m01d.getChkResult(), 1);
						} else {
							itemContent = StringUtils.replace(
									itemContent,
									"是否正確",
									" "
											+ toBox("Y4|N4|K5", rptProperties,
													l170m01d.getChkResult(), 1)
											+ " ");
						}
					}
					// J-112-0280
					// 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
					else if (Util.equals("Y230", itemNo)
							|| Util.equals("Y235", itemNo)
							|| Util.equals("Y23O", itemNo)) {
						if (Util.equals(locale, "en")) {
							itemContent = itemContent
									+ " "
									+ toBox("Y|N|K2", rptProperties,
											l170m01d.getChkResult(), 1);
						} else {
							itemContent = StringUtils.replace(
									itemContent,
									"是否",
									" "
											+ toBox("Y|N|K5", rptProperties,
													l170m01d.getChkResult(), 1)
											+ " ");

						}
					} else if (Util.equals("Y310", itemNo)
							|| Util.equals("Y31A", itemNo)
							|| Util.equals("Y320", itemNo)
							|| Util.equals("Y33A", itemNo)
							|| Util.equals("Y33B", itemNo)
							|| Util.equals("Y33C", itemNo)
							|| Util.equals("Y33D", itemNo)) {
						// J-113-0204 新增及修正說明文句
						if (Util.equals(locale, "en")) {
							itemContent = itemContent
									+ " "
									+ toBox("Y|N|K2", rptProperties,
											l170m01d.getChkResult(), 1);
						} else {
							itemContent = itemContent
							+ " "
							+ toBox("Y|N|K5", rptProperties,
									l170m01d.getChkResult(), 1);
						}
					} else {
						if (Util.equals(locale, "en")) {
							itemContent = itemContent
									+ " "
									+ toBox("Y|N|K5", rptProperties,
											l170m01d.getChkResult(), 1);
						} else {
							itemContent = StringUtils.replace(
									itemContent,
									"是否",
									" "
											+ toBox("Y|N|K5", rptProperties,
													l170m01d.getChkResult(), 1)
											+ " ");
						}
					}
				} else if (chk_2.contains(itemNo)) {
					if (Util.equals("X111", itemNo)
							|| Util.equals("X112", itemNo)) {
						itemContent = itemContent
								+ " "
								+ toBox("Y|N", rptProperties,
										l170m01d.getChkResult(), 1) + " "
								+ rptProperties.getProperty("label.chkText00");
					} else if (Util.equals("Y135", itemNo)) {
						if (Util.equals(locale, "en")) {
							itemContent = itemContent
									+ " "
									+ toBox("Y4|N4", rptProperties,
											l170m01d.getChkResult(), 1);
						} else {
							itemContent = StringUtils.replace(
									itemContent,
									"是否正確",
									" "
											+ toBox("Y4|N4", rptProperties,
													l170m01d.getChkResult(), 1)
											+ " ");
						}
					} else {
						if (Util.equals(locale, "en")) {
							itemContent = itemContent
									+ " "
									+ toBox("Y|N", rptProperties,
											l170m01d.getChkResult(), 1);
						} else {
							itemContent = StringUtils.replace(
									itemContent,
									"是否",
									" "
											+ toBox("Y|N", rptProperties,
													l170m01d.getChkResult(), 1)
											+ " ");
						}
					}
				}
				// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
				else if (chk_4.contains(itemNo)) {

					if (Util.equals(locale, "en")) {
						itemContent = itemContent
								+ " "
								+ toBox("Y3|N3", rptProperties,
										l170m01d.getChkResult(), 1);
					} else {
						itemContent = StringUtils.replace(
								itemContent,
								"有無",
								" "
										+ toBox("Y2|N2", rptProperties,
												l170m01d.getChkResult(), 1)
										+ " ");
					}

				}

				if (TAB_1.contains(itemNo)) {
					itemContent = "　" + itemContent;
					itemContent = "<div style='margin-left:3em; text-indent:-3em;'>"
							+ itemContent + "</div>";
				} else if (TAB_2.contains(itemNo)) {
					itemContent = "　　" + itemContent;
					if (Util.equals("Y130", itemNo)) {
						String Y130 = "";
						if (Util.equals(locale, "en")
								&& UtilConstants.Country.加拿大.equals(branchtype
										.getCountryType())) {
							Y130 = rptProperties
									.getProperty("L170M01D.v2ITEMY130_CA");
						} else {
							Y130 = rptProperties
									.getProperty("L170M01D.v2ITEMY130");
						}
						itemContent += ("<br>" + "　" + "<u>" + Y130 + "</u>");
					}
					itemContent = "<div style='margin-left:4em; text-indent:-4em;'>"
							+ itemContent + "</div>";
				} else if (TAB_3.contains(itemNo)) {
					itemContent = "　　　" + itemContent;
					if (Util.equals(locale, "en")
							&& Util.equals("Y124", itemNo)) {
						itemContent += "<br>"
								+ rptProperties
										.getProperty("L170M01D.v2ITEMY124");
					}
					// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
					if (Util.equals("Y221", itemNo)) {
						String Y22A = "";
						if (Util.equals(locale, "en")
								&& UtilConstants.Country.加拿大.equals(branchtype
										.getCountryType())) {
							Y22A = rptProperties
									.getProperty("L170M01D.v4ITEMY221_CA");
						} else {
							Y22A = rptProperties
									.getProperty("L170M01D.v4ITEMY221");
						}
						itemContent += ("<br>" + "　" + "<u>" + Y22A + "</u>");
					}
					itemContent = "<div style='margin-left:5em; text-indent:-5em;'>"
							+ itemContent + "</div>";
				} else if (TAB_4.contains(itemNo)) {
					itemContent = "　　　　" + itemContent;
					itemContent = "<div style='margin-left:6em; text-indent:-6em;'>"
							+ itemContent + "</div>";
				}
				// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
				else if (TAB_5.contains(itemNo)) {
					itemContent = "　　　　　" + itemContent;
					itemContent = "<div style='margin-left:7em; text-indent:-7em;'>"
							+ itemContent + "</div>";
				} else if (TAB_0.contains(itemNo)
						&& Util.equals("N", Util.trim(l170m01a.getRealRpFg()))) {
					if (Util.equals(locale, "en")) {

						int idx = itemContent.indexOf(".");

						int idx2 = itemContent.indexOf("：");

						itemContent = itemContent.substring(idx >= 0 ? idx
								: idx2);
					} else {

						int idx = itemContent.indexOf(".");

						int idx2 = itemContent.indexOf("：");

						itemContent = itemContent.substring(idx2 >= 0 ? idx2
								: idx);
					}
					if (Util.equals("Y000", itemNo)) {
						itemContent = rptProperties.getProperty("label.num_1")
								+ itemContent;
					} else if (Util.equals("X000", itemNo)) {
						itemContent = rptProperties.getProperty("label.num_2")
								+ itemContent;
					}
					itemContent = "<div style='margin-left:2em; text-indent:-2em;'>"
							+ itemContent + "</div>";
				}

				if (Util.equals("Z", itemType)) {
					if (Util.equals("Z400", itemNo)) {
						L170M01D B008Data = map.get("B008");
						if (Util.notEquals(Util.trim(B008Data.getChkText()), "")) {
							B008Memo.append("(");
							B008Memo.append(rptProperties
									.getProperty("label.chkText01"));
							B008Memo.append(Util.trim(B008Data.getChkText()));
							B008Memo.append(")");
						}
					}
					rB008.add("<tr><td>" + itemContent + "</td></tr>");
				} else if (Util.equals("Y", itemType)) {
					rB011.add("<tr><td>" + itemContent + "</td></tr>");
				} else if (Util.equals("X", itemType)) {
					String chkTexts = "";
					StringBuffer B013Memo = new StringBuffer("");
					if (Util.notEquals(Util.trim(chkText), "")) {
						if (Util.equals("X111", itemNo)
								|| Util.equals("X211", itemNo)
								|| Util.equals("X112", itemNo)
								|| Util.equals("X212", itemNo)) {
							chkTexts = rptProperties
									.getProperty("label.chkText02");
						} else if (Util.equals("X113", itemNo)
								|| Util.equals("X213", itemNo)) {
							chkTexts = rptProperties
									.getProperty("label.chkText04");
						}
						B013Memo.append("<p style ='margin:2px 0px 5px 50px'>");
						B013Memo.append("(");
						B013Memo.append(chkTexts);
						B013Memo.append(chkText);
						B013Memo.append(")");
						B013Memo.append("</p>");
					}
					rB013.add("<tr><td>" + itemContent + "</td></tr>");
					if (Util.notEquals(B013Memo.toString(), "")) {
						rB013.add("<tr><td>" + "" + B013Memo.toString()
								+ "</td></tr>");
					}
				}
			}
			if (Util.notEquals(B008Memo.toString(), "")) {
				rB008.add("<tr><td>" + "" + B008Memo.toString() + "</td></tr>");
			}
			rptVariableMap.put(
					"STR_B008",
					"<table style='margin:0px' border='0'>"
							+ StringUtils.join(rB008, "") + "</table>");
			rptVariableMap.put(
					"STR_B011",
					"<table style='margin:0px' border='0'>"
							+ StringUtils.join(rB011, "") + "</table>");
			rptVariableMap.put(
					"STR_B013",
					"<table style='margin:0px' border='0'>"
							+ StringUtils.join(rB013, "") + "</table>");

			if (rB008.size() == 0 && rB011.size() == 0 && rB013.size() == 0) {
				return outputStream;
			}

			rptVariableMap.put("L170M01A.RANDOMCODE",
					Util.nullToSpace(l170m01a.getRandomCode()));

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (typCdMap != null) {
				typCdMap.clear();
			}
		}
		return outputStream;
	}

	private String toBox(String result_fmt, Properties rptProperties, String s,
			int spaceLen) {
		String addStr = Util.addSpaceWithValue("", spaceLen);

		String[] valSplit = Util.trim(result_fmt).split("\\|");
		if (valSplit == null || valSplit.length == 0) {
			return s;
		} else {
			List<String> r = new ArrayList<String>();
			for (String val : valSplit) {
				String showStr = (Util.equals(s, Util.getLeftStr(val, 1)) ? "■"
						: "□")
						+ " "
						+ rptProperties.getProperty("label." + val);
				r.add(showStr);
			}
			return StringUtils.join(r, addStr);
		}
	}

	private int getItemSeqForShow(L170M01A l170m01a, L170M01D l170m01d) {

		// J-108-0888_05097_B1001
		int itemSeq = 0;
		String rptId = Util.trim(l170m01a.getRptId());
		String itemSeqShow = Util.trim(l170m01d.getItemSeqShow());

		if (Util.notEquals(rptId, "")) {
			if (LrsUtil.compareRptVersion(rptId, ">=", LrsUtil.V_O_201907)) {
				if (Util.notEquals(itemSeqShow, "")) {
					itemSeq = Util.parseInt(itemSeqShow);
				} else {
					itemSeq = l170m01d.getItemSeq();
				}
			} else {
				itemSeq = l170m01d.getItemSeq();
			}
		} else {
			itemSeq = l170m01d.getItemSeq();
		}

		return itemSeq;
	}

	/**
	 * 產生LMS1705R04的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param locale
	 *            語系
	 * @return outputstream outputstream
	 * @throws Exception
	 */
	public OutputStream genLMS1705R04(String mainId, Locale locale,
			Properties rptProperties) throws Exception {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();

		L170M01A l170m01a = l170m01aDao.findByMainId(mainId);
		if (l170m01a == null)
			l170m01a = new L170M01A();
		L170M01I l170m01i = l170m01iDao.findByMainId(mainId);
		if (l170m01i == null)
			l170m01i = new L170M01I();

		ReportGenerator generator = new ReportGenerator(
				"report/lrs/LMS1705R04_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;
		Map<String, String> typCdMap = null;

		try {
			typCdMap = codetypeservice.findByCodeType("TypCd",
					locale.toString());
			rptVariableMap.put("L170M01A.RETRIALDATE",
					Util.trim(TWNDate.toAD(l170m01a.getRetrialDate())));
			rptVariableMap.put("L170M01A.PROJECTNO",
					Util.trim(l170m01a.getProjectNo()));
			rptVariableMap.put(
					"CUST_INFO",
					"("
							+ Util.nullToSpace(typCdMap.get(Util
									.nullToSpace(l170m01a.getTypCd()))) + ")"
							+ " " + Util.trim(l170m01a.getCustId()) + " "
							+ Util.trim(l170m01a.getCustName()));

			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1705S04Panel.class);
			String reviewType = ""; // 取得該案複審內容版本
			reviewType = lms1705service.getReviewType(l170m01a);
			// 取得項目
			HashMap<String, String> chklist = new HashMap<String, String>();
			chklist = lms1705service.getChkList(reviewType);
			// 取得項目文字
			String[] spanContent = new String[6];
			spanContent = retrialService.getChkListSpan(reviewType, chklist,
					prop);

			rptVariableMap.put("tilteList", spanContent[5]);
			rptVariableMap.put("first", spanContent[0]);
			rptVariableMap.put("second", spanContent[1]);
			rptVariableMap.put("third", spanContent[2]);
			rptVariableMap.put("fourth", spanContent[3]);
			rptVariableMap.put("fifth", spanContent[4]);

			// J-111-0326 海外覆審作業系統改良第一階段： 8. 以下四項約據選擇「無」時，應列印顯示N/A
			if (Util.equals(Util.nullToSpace(l170m01i.getHasContract()), "N")) {
				rptVariableMap.put("contractDt", "N/A");
			} else {
				rptVariableMap.put("contractDt",
						Util.trim(TWNDate.toAD(l170m01i.getContractDt())));
			}
			if (Util.equals(Util.nullToSpace(l170m01i.getHasNote()), "N")) {
				rptVariableMap.put("noteDt", "N/A");
			} else {
				rptVariableMap.put("noteDt",
						Util.trim(TWNDate.toAD(l170m01i.getNoteDt())));
			}
			if (Util.equals(Util.nullToSpace(l170m01i.getHasAttorney()), "N")) {
				rptVariableMap.put("attorneyDt", "N/A");
			} else {
				rptVariableMap.put("attorneyDt",
						Util.trim(TWNDate.toAD(l170m01i.getAttorneyDt())));
			}
			if (Util.equals(Util.nullToSpace(l170m01i.getHasGuar()), "N")) {
				rptVariableMap.put("guarSignDt", "N/A");
				rptVariableMap.put("guarVerDt", "N/A");
			} else {
				rptVariableMap.put("guarSignDt",
						Util.trim(TWNDate.toAD(l170m01i.getGuarSignDt())));
				rptVariableMap.put("guarVerDt",
						Util.trim(TWNDate.toAD(l170m01i.getGuarVerDt())));
			}
			rptVariableMap.put("receAmt",
					NumConverter.addComma(Util.equals("",
							Util.nullToSpace(l170m01i.getReceAmt())) ? ""
							: l170m01i.getReceAmt(), "#,##0"));
			rptVariableMap.put("receNA",
					this.getYNPic(Util.trim(l170m01i.getReceNA())));
			rptVariableMap.put("valRepDt",
					Util.trim(TWNDate.toAD(l170m01i.getValRepDt())));
			rptVariableMap.put(
					"valRepAmt",
					NumConverter.addComma(
							Util.equals("",
									Util.nullToSpace(l170m01i.getValRepAmt())) ? // BigDecimal.ZERO
							""
									: l170m01i.getValRepAmt(), "#,##0"));
			rptVariableMap.put("valRepNA",
					this.getYNPic(Util.trim(l170m01i.getValRepNA())));
			rptVariableMap.put("mtgOrder", Util.trim(l170m01i.getMtgOrder()));
			rptVariableMap.put("mtgAmt",
					NumConverter.addComma(Util.equals("",
							Util.nullToSpace(l170m01i.getMtgAmt())) ? ""
							: l170m01i.getMtgAmt(), "#,##0"));
			rptVariableMap.put("mtgNA",
					this.getYNPic(Util.trim(l170m01i.getMtgNA())));
			rptVariableMap.put("insDt",
					Util.trim(TWNDate.toAD(l170m01i.getInsDt())));
			rptVariableMap.put("insAmt",
					NumConverter.addComma(Util.equals("",
							Util.nullToSpace(l170m01i.getInsAmt())) ? ""
							: l170m01i.getInsAmt(), "#,##0"));
			rptVariableMap.put("insNum", Util.trim(l170m01i.getInsNum()));
			rptVariableMap.put("insNA",
					this.getYNPic(Util.trim(l170m01i.getInsNA())));

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			outputStream = generator.generateReport();
		} finally {

		}
		return outputStream;
	}

	private String getYNPic(String value) {
		if ("Y".equals(value)) {
			return "√";
		} else {
			return "□";
		}
	}
}
