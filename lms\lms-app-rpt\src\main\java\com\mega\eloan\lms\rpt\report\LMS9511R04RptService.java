/* 
 * LMS1205R01RptService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.report;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.exception.CapException;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.LMSBATCH;

/**<pre>
 * rpt報表service程式
 * </pre>
 * @since  2013/01/17
 * <AUTHOR>
 * @version <ul>
 *           <li>
 *          </ul>
 */
public interface LMS9511R04RptService extends AbstractService{

	/**
	 * <pre>
	 * 產生PDF檔
	 * 1. 授信契約已逾期控制表
	 * 
	 * @param dataCollection
	 * @param i
	 * @param listName 
	 * @param ovUnitNo 
	 * @param ctype 
	 * @param caseDept 
	 * @param dataDate 
	 * @return
	 * </pre>
	 * @throws CapException 
	 * @throws IOException 
	 */
	
	String generateReport(List<Map<String, Object>> data, int action,
			LMSBATCH batchtbl) throws IOException;

}
