/* 
 * L120S01NDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01N;

/** 信用風險管理遵循明細檔 **/
public interface L120S01NDao extends IGenericDao<L120S01N> {

	L120S01N findByOid(String oid);

	List<L120S01N> findByMainId(String mainId);

	List<L120S01N> findByIndex01(String mainId, String custId, String dupNo);

	List<L120S01N> findByIndex02(String custId, String dupNo);

	public List<L120S01N> findByCustId(String mainId, String custId,
			String dupNo);
	int deleteByKey(String mainId, String custId, String dupNo);
}