/* 
 * C900M01O.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 歡喜信貸自動派案維護-被指派分行清單檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C900M01O", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900M01O extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 被分派分行代碼 **/
	@Size(max=3)
	@Column(name="ASSIGNEEBRCHID", length=3, columnDefinition="VARCHAR (3)")
	private String assigneeBrchId;

	/** 分派順序 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="ASSIGNORDER", columnDefinition="DECIMAL(3,0)")
	private Integer assignOrder;

	/** 建立人員代碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 分派日期 **/
	@Column(name="ASSIGNTIME", columnDefinition="TIMESTAMP")
	private Timestamp assignTime;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得被分派分行代碼 **/
	public String getAssigneeBrchId() {
		return this.assigneeBrchId;
	}
	/** 設定被分派分行代碼 **/
	public void setAssigneeBrchId(String value) {
		this.assigneeBrchId = value;
	}

	/** 取得分派順序 **/
	public Integer getAssignOrder() {
		return this.assignOrder;
	}
	/** 設定分派順序 **/
	public void setAssignOrder(Integer value) {
		this.assignOrder = value;
	}

	/** 取得建立人員代碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員代碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得分派日期 **/
	public Timestamp getAssignTime() {
		return this.assignTime;
	}
	/** 設定分派日期 **/
	public void setAssignTime(Timestamp value) {
		this.assignTime = value;
	}
}
