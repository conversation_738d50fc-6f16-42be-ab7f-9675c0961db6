package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S01Y;

/** 個金地政士名單 **/
public interface C101S01YDao extends IGenericDao<C101S01Y> {

    C101S01Y findByOid(String oid);

    List<C101S01Y> findByList(String mainId, String custId, String dupNo);

    C101S01Y findLaaByList(String mainId, String custId, String dupNo, String laaYear, String laaWord, String laaNo);

	List<C101S01Y> findByCustIdDupId(String custId, String DupNo);

	List<C101S01Y> findByMainId(String mainId);

	public int deleteByOid(String oid);
}