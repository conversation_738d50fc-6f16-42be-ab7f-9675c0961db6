package com.mega.eloan.lms.mfaloan.service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

public interface MisGrpdtlService {
	public List<?> findGrpdtlForGrpnm(String grpNo);
	public List<?> findGrpdtlForAllGrpid();
	
	/**
	 * <li>0-集團代號 <li>1-集團名稱 <li>2-資料彙整分行代號 <li>3-幣別 <li>4-集團額度限額 <li>
	 * 5-目前已核准額度 <li>6-資料修改人（行員代號） <li>7-資料修改日期 <li>8-額度中文說明 <li>
	 * 9-集團擔保授信總額度 <li>10-集團無擔保授信總額度 <li>11-集團無擔保限額 <li>12-年度 <li>13-評等 <li>
	 * 14-集團額度限額百分比% <li>15-集團無擔保限額百分比% <li>16-集團無擔保債券額度上限 <li>17-集團無擔保債券限額% <li>
	 * 18-列管註記
	 */
	final String[] DtlCols = { "GRPID", "GRPNM", "BRANCH", "CURR", "QUOTA",
			"APPR", "UPDATER", "TMESTAMP", "QUOTAMO", "NGTAPPR", "GTAPPR",
			"NGTQUOTA", "GRPYY", "GRPGRADE", "QUOTAPC", "NQUOTAPC", "BGTQUOTA",
			"BQUOTAPC", "BADFLAG" };

	/**
	 * <li>01-集團代號 <li>02-集團轄下公司之統一編號 <li>03-重複序號 <li>04-集團轄下公司名稱 <li>05-自然人或法人
	 * (1 法人 2 自然人) <li>06-職稱 <li>07-區部別 <li>08-是否編三書表 (Y/N) <li>
	 * 09-編製三書表核心公司統一編號 <li>10-與本行是否往來 (Y/N) <li>12-指定資料編製分行代號 <li>13-聯徵財務資料日期
	 * (YYYMMDD) <li>14-行業別名稱 <li>15-負責人姓名 <li>16-全體金融機構授信餘額 <li>17-全體金融機構授信額度
	 * <li>18-本行授信餘額 <li>19-本行授信額度 <li>20-資料修改人（行員代號） <li>21-資料修改日期 <li>
	 * 22-國內/海外集團 (國內:空白;海外:Y)
	 */
	final String[] CmpCols = { "GRPID", "BAN", "DUPNO", "CMPNM", "KIND",
			"POSCD", "TYPCD", "GBANYN", "GBAN", "ACUST", "BRANCH", "FINYMD",
			"BUSNM", "MANNM", "BAL1", "QUOTA1", "BAL2", "QUOTA2", "UPDATER",
			"TMESTAMP", "OVERSEAS" };

	/**
	 * 集團代號取得集團基本資料
	 * 
	 * @param groupId
	 *            集團代號
	 * @return Map<String, Object>
	 */
	public Map<String, Object> findDtlByGrpId(String groupId);

	/**
	 * 取得所有集團基本資料
	 * 
	 * @return List<Map<String,Object>>
	 */
	List<Map<String, Object>> findAllDtl();
	
//	/**
//	 * 依所選型態以及輸入的查詢字串，查詢集團資料。
//	 * 
//	 * @param type
//	 *            (grpId,ban,cmpNm,all)
//	 * @param condition
//	 *            query string
//	 * @return GrpDtl list
//	 */
//	List<Map<String, Object>> findDtlByType(String type, String condition);

	/**
	 * 客戶ID取得其集團代號及名稱
	 * 
	 * @param ban
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return Map<String, Object>
	 */
	public Map<String, Object> findCmpByBanDup(String ban, String dupNo);

	/**
	 * 集團代號取得集團轄下公司資料
	 * 
	 * @param grpid
	 *            集團代號
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> findCmpByGrpid(String grpid);

	/**
	 * 取得新集團代號
	 * 
	 * @return Map<String, Object>
	 */
	public Map<String, Object> findGrpdtlNextGrpid();

	/**
	 * 新增集團企業基本資料
	 * 
	 * @param grpid
	 *            集團代號
	 * @param grpnm
	 *            集團名稱
	 * @param branch
	 *            資料彙整分行代號
	 * @param updater
	 *            資料修改人
	 * @param updateTime
	 *            資料修改日期
	 * @param grpyy
	 *            年度(民國年)
	 * @return 是否成功
	 */
	public int addGrpdtl(String grpid, String grpnm, String branch,
			Timestamp updateTime, String updater, String grpyy);

	/**
	 * 依據集團代號刪除集團資料
	 * 
	 * @param grpid
	 *            集團代號
	 * @return 刪除筆數
	 */
	public int delGrpdtl(String grpid);
	
	/**
	 * 取得集團調整數
	 * 
	 * @param grpId
	 *            集團代號
	 * @return 集團調整數
	 */
	public Map<String, Object> findAdjustByGrpId(String grpId);
	
	/**
	 * 取得集團年度
	 * @return
	 */
	public Map<String, Object> findGrpdtl_selGrpyy();
}
