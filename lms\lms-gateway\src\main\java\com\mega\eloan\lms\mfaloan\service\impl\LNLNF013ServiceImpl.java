package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.LNLNF013Service;

/**
 * <pre>
 *  AO帳戶管理員對應服務顧客檔 LN.LNF013
 * </pre>
 * 
 * @since 2012/6/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/6/8,REX,new
 *          </ul>
 */
@Service
public class LNLNF013ServiceImpl extends AbstractMFAloanJdbc implements
		LNLNF013Service {
	@Override
	public List<Map<String, Object>> findByUnidAndCustId(String LNF013_BR_NO,
			String LNF013_CUST_ID, String LNF013_BUS_PER_FG) {
		return this.getJdbc().queryForListWithMax("LN.LNF013_findByUnidAndCustId",
				new Object[] { LNF013_BR_NO, LNF013_CUST_ID, LNF013_BUS_PER_FG });

	}
	
	public List<Map<String, Object>> findByUnidAndAOId(String LNF013_BR_NO, String LNF013_STAFF_NO, String LNF013_BUS_PER_FG) {
		List<Map<String, Object>> datas=this.getJdbc().queryForListWithMax(
				"LN.LNF013_findByUnidAndAOId",	new Object[] { LNF013_BR_NO, LNF013_STAFF_NO, LNF013_BUS_PER_FG });
		return datas;
	}
	
	
	
	@Override
	public void insertByCls(String LNF013_BR_NO, String LNF013_STAFF_NO,
			String LNF013_BEG_DATE, String LNF013_END_DATE,
			String LNF013_CUST_ID, String LNF013_CRE_DATE,
			String LNF013_CRE_TELLER, String LNF013_CRE_SUPVNO,
			String LNF013_UPD_DATE, String LNF013_UPD_TELLER,
			String LNF013_UPD_SUPVNO, String LNF013_DEL_DATE,
			String LNF013_DEL_TELLER, String LNF013_DEL_SUPVNO, String LNF013_BUS_PER_FG) {
		this.getJdbc().update(
				"LNLNF013.insertByCls",
				new Object[] { LNF013_BR_NO, LNF013_STAFF_NO,
						LNF013_BEG_DATE, LNF013_END_DATE, LNF013_CUST_ID, LNF013_CRE_DATE,
						LNF013_CRE_TELLER, LNF013_CRE_SUPVNO, LNF013_UPD_DATE, LNF013_UPD_TELLER,
						LNF013_UPD_SUPVNO, LNF013_DEL_DATE, LNF013_DEL_TELLER, LNF013_DEL_SUPVNO, LNF013_BUS_PER_FG});
	}
	
	@Override
	public int updateByBrnoAndCustId(String LNF013_UPD_DATE, String LNF013_UPD_TELLER, String LNF013_UPD_SUPVNO, String LNF013_STAFF_NO,
			String LNF013_BR_NO, String LNF013_CUST_ID, String LNF013_BUS_PER_FG) {
		int count = 0;
		count = this.getJdbc().update(
					"LNLNF013.updateByBrnoAndCustId",
					new Object[] { LNF013_UPD_DATE, LNF013_UPD_TELLER, LNF013_UPD_SUPVNO, LNF013_STAFF_NO,
							LNF013_BR_NO, LNF013_CUST_ID, LNF013_BUS_PER_FG });
		return count;
	}
	
	@Override
	public int delByCustKey(String LNF013_BR_NO, String LNF013_CUST_ID, String LNF013_BUS_PER_FG) {
		int count = 0;
		count = this.getJdbc().update("LNLNF013.delByCsutKey",
				new Object[] { LNF013_BR_NO, LNF013_CUST_ID, LNF013_BUS_PER_FG });
		return count;
	}
	

}
