var Action = {
    baseUrl: "../lms/lms1505m01/02",
    //1.海外 2.企金 3.個金
    createType: "2",
    i18nkey: i18n.lms1505m01
};
var newRowNum = 15;
$(function(){
	var viewstatus = window.viewstatus || "VIEW";
	var txCode = window.txCode || "TX01";
	
    var grid = $("#gridview").iGrid({
        handler: 'lms1505gridhandler',
        height: 347,
        sortname: 'meetingDate|oid',
        sortorder: 'desc|desc',
        postData: {
            formAction: "query",
            createType: Action.createType
        },
        rowNum: newRowNum,
        gridPage: 1,
        colModel: [{
            colHeader: Action.i18nkey['L150M01a.meetingDate'],//"會議日期",
            name: 'meetingDate',
            align: "center",
            width: 10,
            sortable: true,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            }
        
        }, {
            colHeader: Action.i18nkey['L150M01a.gist'],//"案由",
            name: 'gist',
            width: 100,
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({
            url: '..' + rowObject.docURL + '/02',//../lms/lms1505m01/02
            data: {
                formAction: "query",
                mainOid: rowObject.oid,
                mainId: rowObject.mainId,
                mainDocStatus: viewstatus,
                txCode: txCode
            
            },
            target: rowObject.oid
        });
    };
    
    
    $("#buttonPanel").find("#btnView").click(function(){
	        var id = $("#gridview").getGridParam('selrow');
	        if (!id) {
	            // action_004=請先選擇需「調閱」之資料列
	            return CommonAPI.showMessage(i18n.def["action_004"]);
	        }
	        
	        var result = $("#gridview").getRowData(id);
	        openDoc(null, null, result);
	
	
	}).end().find("#btnDelete").click(function(){
        var $gridview = $("#gridview");
        var ids = $gridview.getGridParam('selrow');
        
        if (!ids) {//TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        var oids = [];
        if (ids) {
			var rowData = $gridview.getRowData(ids);
			    oids.push(rowData.oid);
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "lms1505m01formhandler",
                    data: {
                        formAction: "deleteList",
                        oids: oids
                    }
                }).done(function(obj){
					 $("#gridview").trigger("reloadGrid");
				});
            }
        });
        
    }).end().find("#btnAdd").click(function(){
    
        //單筆
        var id = $("#gridview").getGridParam('selrow');
        if (id) {
            var result = $("#gridview").getRowData(id);
            $.ajax({
				url    : "../lms/lms1505m01/02",
                handler: "lms1505m01formhandler",
                formId: "filterForm",
                action: "copyCase",
                data: {
                    oid: result.oid	
                }
            }).done(function(obj){
				 openDoc(null, null, obj);
			});
        }
        else {
            $.form.submit({
                url: Action.baseUrl,//'../lms/lms1505m01/02',
                data: {
                    mainDocStatus: viewstatus,
                    createType: Action.createType
                },
                target: "_blank"
            });
        }
        
    }).end().find("#btnModify").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {//TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        var result = $("#gridview").getRowData(id);
        openDoc(null, null, result);
    }).end().find("#btnFilter").click(function(){
        openFilterBox();
    })
});
function dateObjtoStr(tDate){
    return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
}

// 篩選
function openFilterBox(){

    var $filterForm = $("#filterForm");
    // 初始化
	$filterForm[0].reset();
    //set default value
    var sysdate = CommonAPI.getToday().split("-");
    var endDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
    var fromDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
    fromDate.setMonth(fromDate.getMonth() - 12);
    
    $("#meetingDateS").val(dateObjtoStr(fromDate));
    $("#meetingDateE").val(dateObjtoStr(endDate));
    
    var thickTitle;
    thickTitle = i18n.lms1201v01['l120v05.title02'];
    
    $("#filterBox").thickbox({
        // l120v05.title02=請輸入欲查詢項目
        title: thickTitle,
        width: 500,
        height: 400,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                if (!$("#filterForm").valid()) {
                    return;
                }
                if ($("#meetingDateS").val() > $("#meetingDateE").val()) {
                    //起始日期不能大於結束日期
                    CommonAPI.showErrorMessage(i18n.lms1501v00['l150m01a.meetingDate'] + "：" + i18n.lms1501v00['l150m01a.error01']);
                    return false;
                }
                
                var mDateS = $("#meetingDateS").val().split("-");
                //var newMDateS = new Date(mDateS[0], mDateS[1] - 1, mDateS[2]);
                var mDateE = $("#meetingDateE").val().split("-");
                //var newMDateE = new Date(mDateE[0], mDateE[1] - 1, mDateE[2]);
                //var diffDay = ((newMDateE.getTime()/1000/60/60/24/31)-(newMDateS.getTime()/1000/60/60/24/31)) ;
                var diffDay = ((mDateE[0] - mDateS[0]) * 12) + (mDateE[1] - mDateS[1]);
                if (diffDay > 24) {
                    //查詢起迄日期區間不得相差兩年以上
                    CommonAPI.showErrorMessage(i18n.lms1501v00['l150m01a.meetingDate'] + "：" + i18n.lms1501v00['l150m01a.error02']);
                    return false;
                }
                grid("queryL150m01a1");
                
                $.thickbox.close();
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                    
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function grid(action){
    newRowNum = $("#rowNum").val();
    if (newRowNum <= 0) {
        newRowNum = 15;
    }
    $("#gridview").jqGrid("setGridParam", {
        postData: $.extend($("#filterForm").serializeData(), {
            handler: "lms1505gridhandler",
            formAction: action,
            docStatus: viewstatus,
            mainDocStatus: viewstatus,
            rowNum: newRowNum
        
        }),
        rowNum: newRowNum,
        page: 1
    
    }).trigger("reloadGrid");
    
}

