/* 
 *  LMS9515R01RptServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.annotation.Resource;

import jxl.SheetSettings;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.PaperSize;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.NumberFormats;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.XmlTool;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.jcs.common.report.SubReportParam;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.constants.UtilConstants.Casedoc;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.cls.report.impl.CLS1141R01RptServiceImpl;
import com.mega.eloan.lms.dao.L784S01ADao;
import com.mega.eloan.lms.dao.LMSRPTDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lns.report.impl.LMS1201R01RptServiceImpl;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L784S01A;
import com.mega.eloan.lms.model.LMSBATCH;
import com.mega.eloan.lms.model.LMSRPT;
import com.mega.eloan.lms.rpt.pages.LMS9515V01Page;
import com.mega.eloan.lms.rpt.report.LMS9511R01RptService;
import com.mega.eloan.lms.rpt.service.LMS9511Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS9511R01RptServiceImpl extends AbstractCapService implements
		LMS9511R01RptService {
	private static final Logger logger = LoggerFactory
			.getLogger(LMS9511R01RptServiceImpl.class);
	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	DocFileService fileService;

	@Resource
	LMS9511Service lms9511Service;

	@Resource
	L784S01ADao l784s01aDao;

	@Resource
	LMSRPTDao lmsrptDao;

	@Resource
	DocFileDao docFileDao;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	RetrialService retrialService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	LMSService lmsService;

	@Resource
	BranchService branch;

	@Resource
	LMS1401Service service1401;

	@Resource
	LMS1201Service service1201;

	@Resource
	LMS1405Service service1405;

	@Override
	public void save(GenericBean... entity) {

	}

	@Override
	public void delete(GenericBean... entity) {

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}

	@Override
	public String generateLMS180R01Report(String mainId) throws Exception {
		OutputStream outputStream = null;
		Locale locale = LMSUtil.getLocale();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		LMSRPT lmsRpt = lmsrptDao.findByIndex03(mainId);
		List<L784S01A> l784s01aList = l784s01aDao.findByMainId(mainId);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		titleRows = this.setL180R01TitleRows(titleRows, l784s01aList, prop,
				locale);
		rptVariableMap = this.setL180R01TitleRowsRptVariableMap(rptVariableMap,
				lmsRpt);
		ReportGenerator generator = new ReportGenerator(
				"report/rpt/LMS9511R01_" + locale.toString() + ".rpt");
		generator.setRowsData(titleRows);
		generator.setVariableData(rptVariableMap);
		outputStream = generator.generateReport();
		DocFile docFile = this.saveDocFile(outputStream, lmsRpt,
				UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單, 7, locale, false);

		return docFile.getOid();
	}

	@Override
	public String generateLMS180R05Report(String mainId) throws Exception {
		List<Map<String, Object>> list = null;
		OutputStream outputStream = null;
		Locale locale = LMSUtil.getLocale();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		LMSRPT lmsRpt = lmsrptDao.findByIndex03(mainId);
		list = lms9511Service.findType1ByBrNoAndDate(lmsRpt.getBranch(),
				lmsRpt.getBgnDate(), lmsRpt.getEndDate(),
				UtilConstants.Casedoc.DocType.個金, null);
		titleRows = this.setLMS180R05DataTitleRows(titleRows, list);
		rptVariableMap.put("LMS9515R01.dataDate",
				TWNDate.toAD(lmsRpt.getDataDate()));
		rptVariableMap.put("LMS9515R01.printDate",
				TWNDate.toAD(CapDate.getCurrentTimestamp()));
		rptVariableMap.put("L784M01A.RANDOMCODE", lmsRpt.getRandomCode());
		ReportGenerator generator = new ReportGenerator(
				"report/rpt/LMS9511R05_" + locale.toString() + ".rpt");
		generator.setRowsData(titleRows);
		generator.setVariableData(rptVariableMap);
		outputStream = generator.generateReport();
		DocFile docFile = this.saveDocFile(outputStream, lmsRpt,
				UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表, 7, locale, true);
		return docFile.getOid();
	}

	@Override
	public String generateLMS180R10Report(String mainId) throws Exception {
		List<Map<String, Object>> list = null;
		OutputStream outputStream = null;
		Locale locale = LMSUtil.getLocale();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		LMSRPT lmsRpt = lmsrptDao.findByIndex03(mainId);
		list = lms9511Service.findType3ByBrNoAndDate(lmsRpt.getBranch(),
				TWNDate.toAD(lmsRpt.getBgnDate()),
				TWNDate.toAD(lmsRpt.getEndDate()),
				UtilConstants.Casedoc.DocType.個金);
		titleRows = this.setLMS180R10DataTitleRows(titleRows,
				(List<Map<String, Object>>) list);
		// 資料基準日
		rptVariableMap.put("LMS9515R03.STARTDATE",
				TWNDate.toAD(lmsRpt.getBgnDate()));
		rptVariableMap.put("LMS9515R03.ENDDATE",
				TWNDate.toAD(lmsRpt.getEndDate()));
		// 資料產生日期
		rptVariableMap.put("LMS9515R03.createDate",
				TWNDate.toAD(lmsRpt.getDataDate()));
		// 印表日
		rptVariableMap.put("LMS9515R03.printDate",
				TWNDate.toAD(CapDate.getCurrentTimestamp()));
		rptVariableMap.put("L784M01A.RANDOMCODE",
				Util.trim(lmsRpt.getRandomCode()));
		ReportGenerator generator = new ReportGenerator(
				"report/rpt/LMS9511R10_" + locale.toString() + ".rpt");
		generator.setRowsData(titleRows);
		generator.setVariableData(rptVariableMap);
		outputStream = generator.generateReport();
		DocFile docFile = this.saveDocFile(outputStream, lmsRpt,
				UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單, 7, locale, true);
		return docFile.getOid();
	}

	@Override
	public String generateLMS180R15Report(String mainId) throws Exception {
		List<Map<String, Object>> list = null;
		OutputStream outputStream = null;
		Locale locale = LMSUtil.getLocale();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		Map<String, String> docTypeMap = null;
		docTypeMap = codetypeservice.findByCodeType("L120M01A_docType");
		if (docTypeMap == null) {
			docTypeMap = new LinkedHashMap<String, String>();
		}
		LMSRPT lmsRpt = lmsrptDao.findByIndex03(mainId);
		list = lms9511Service.findType6ByBrNoAndDate(lmsRpt.getBranch(),
				TWNDate.toAD(lmsRpt.getBgnDate()),
				UtilConstants.Casedoc.DocType.企金個金,
				UtilConstants.Casedoc.DocKind.授權外);
		titleRows = this.setLMS180R15DataTitleRows(titleRows,
				(List<Map<String, Object>>) list, docTypeMap);
		rptVariableMap.put("LMS9515R06.year", TWNDate.toAD(lmsRpt.getBgnDate())
				.split("-")[0]);
		rptVariableMap.put("LMS9515R06.month", TWNDate
				.toAD(lmsRpt.getBgnDate()).split("-")[1]);
		rptVariableMap.put("LMS9515R06.day", TWNDate.toAD(lmsRpt.getBgnDate())
				.split("-")[2]);
		rptVariableMap.put("L784M01A.RANDOMCODE",
				Util.trim(lmsRpt.getRandomCode()));
		ReportGenerator generator = new ReportGenerator(
				"report/rpt/LMS9511R15_" + locale.toString() + ".rpt");
		generator.setRowsData(titleRows);
		generator.setVariableData(rptVariableMap);
		outputStream = generator.generateReport();
		DocFile docFile = this.saveDocFile(outputStream, lmsRpt,
				UtilConstants.RPTREPORT.DOCTYPE1.營運中心每日授權外授信案件清單, 7, locale,
				true);
		return docFile.getOid();
	}

	@Override
	public String generateLMS180R17Report(String mainId) throws Exception {
		List<Map<String, Object>> list = null;
		OutputStream outputStream = null;
		Locale locale = LMSUtil.getLocale();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		LMSRPT lmsRpt = lmsrptDao.findByIndex03(mainId);
		list = lms9511Service.findType8ByBrNoAndDate(lmsRpt.getBranch(),
				lmsRpt.getDataDate(), UtilConstants.Casedoc.DocKind.授權內);
		titleRows = this.setL180R17TitleRows(titleRows, list);
		rptVariableMap = this.setTitleRowsDataRptVariableMap(rptVariableMap,
				(List<Map<String, Object>>) list);
		rptVariableMap.put("LMS9515R08.year", TWNDate.toAD(lmsRpt.getBgnDate())
				.split("-")[0]);
		rptVariableMap.put("LMS9515R08.month", TWNDate
				.toAD(lmsRpt.getBgnDate()).split("-")[1]);
		rptVariableMap.put("L784M01A.RANDOMCODE",
				Util.trim(lmsRpt.getRandomCode()));
		ReportGenerator generator = new ReportGenerator(
				"report/rpt/LMS9511R17_" + locale.toString() + ".rpt");
		generator.setRowsData(titleRows);
		generator.setVariableData(rptVariableMap);
		outputStream = generator.generateReport();
		DocFile docFile = this.saveDocFile(outputStream, lmsRpt,
				UtilConstants.RPTREPORT.DOCTYPE1.本行各營業單位各級授權範圍內承做授信案件統計表, 7,
				locale, true);
		return docFile.getOid();
	}

	/**
	 * 將產出的PDF 加上頁碼 再存入資料庫
	 * 
	 * @param outputStream
	 * @param lmsRpt
	 * @param docType1
	 * @param subLine
	 * @param locale
	 * @param vaPrintResult
	 * @return
	 * @throws Exception
	 */
	private DocFile saveDocFile(OutputStream outputStream, LMSRPT lmsRpt,
			String docType1, int subLine, Locale locale, boolean vaPrintResult)
			throws Exception {
		FileOutputStream fileOutputStream = null;
		OutputStream outputStreamToFile = null;
		Properties propEloanPage = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		if (outputStream != null) {
			pdfNameMap.put(new ByteArrayInputStream(
					((ByteArrayOutputStream) outputStream).toByteArray()),
					subLine);
		}
		if (pdfNameMap != null && pdfNameMap.size() > 0) {
			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
					propEloanPage.getProperty("PaginationText"), true, locale,
					subLine, vaPrintResult);
		}
		DocFile docFile = lms9511Service.saveDocFile(lmsRpt.getBranch(),
				lmsRpt.getMainId(), docType1, "pdf");

		String filename = LMSUtil.getUploadFilePath(lmsRpt.getBranch(),
				lmsRpt.getMainId(), docType1);
		File file2 = new File(filename + "/" + docFile.getOid() + ".pdf");
		if (file2 != null) {
			fileOutputStream = new FileOutputStream(file2);
		}
		if (fileOutputStream != null) {
			outputStreamToFile = new DataOutputStream(fileOutputStream);
			if (outputStreamToFile != null) {
				outputStreamToFile.write(((ByteArrayOutputStream) outputStream)
						.toByteArray());
				outputStreamToFile.flush();
				outputStreamToFile.close();
			}
		}
		return docFile;
	}

	/**
	 * 塞值 TitleRows
	 * 
	 * @param titleRows
	 * @param dataCollection
	 * @return
	 * @throws CapException
	 */
	private List<Map<String, String>> setLMS180R05DataTitleRows(
			List<Map<String, String>> titleRows, List<Map<String, Object>> list)
			throws CapException {
		Map<String, String> mapInTitleRows = null;
		try {
			mapInTitleRows = Util.setColumnMap();

			if (!list.isEmpty()) {
				// 總共有K筆資料
				int k = list.size();
				for (int j = 0; j < k; j++) {
					Map<String, Object> dataCollection = list.get(j);
					if (dataCollection != null) {
						mapInTitleRows = Util.setColumnMap();
						// 客戶統一編號
						String column01 = Util.trim(dataCollection
								.get("custId"));
						// 名稱
						String column02 = Util.trim(dataCollection
								.get("custName"));

						// 額度序號
						String column03 = Util.trim(dataCollection
								.get("contract"));
						// 幣別
						String column04 = Util.trim(dataCollection
								.get("factSwft"));
						// 核准額度
						String column05 = NumConverter.addComma(LMSUtil
								.toBigDecimal(dataCollection.get("factAmtNt")));
						// 動用起日
						String column06 = TWNDate.toAD(TWNDate.valueOf(Util
								.trim(dataCollection.get("begDate"))));
						// 動用止日
						String column07 = TWNDate.toAD(TWNDate.valueOf(Util
								.trim(dataCollection.get("endDate"))));

						mapInTitleRows.put("ReportBean.column01", column01);
						mapInTitleRows.put("ReportBean.column02", column02);
						mapInTitleRows.put("ReportBean.column03", column03);
						mapInTitleRows.put("ReportBean.column04", column04);
						mapInTitleRows.put("ReportBean.column05", column05);
						mapInTitleRows.put("ReportBean.column06", column06);
						mapInTitleRows.put("ReportBean.column07", column07);
					}
					titleRows.add(mapInTitleRows);
				}
			} else {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} catch (Exception e) {
			throw new CapException();
		}
		return titleRows;
	}

	/**
	 * 塞值 TitleRows
	 * 
	 * @param titleRows
	 * @param list
	 * @return
	 * @throws CapException
	 */
	private List<Map<String, String>> setLMS180R10DataTitleRows(
			List<Map<String, String>> titleRows, List<Map<String, Object>> list)
			throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9515V01Page.class);
		try {
			Map<String, String> mapInTitleRows = null;
			mapInTitleRows = Util.setColumnMap();

			if (!list.isEmpty()) {
				// 總共有K筆資料
				int k = list.size();
				for (int j = 0; j < k; j++) {
					Map<String, Object> dataCollection = list.get(j);
					if (dataCollection != null) {
						mapInTitleRows = Util.setColumnMap();
						// 客戶統一編號
						String column01 = (String) dataCollection.get("custId");
						// 客戶名稱
						String column02 = (String) dataCollection.get("cname");
						// 額度序號
						String column03 = (String) dataCollection.get("cntrno");
						// 性質
						String column04 = (String) dataCollection
								.get("property");
						if (!column04.isEmpty()) {
							column04 = pop.getProperty("L784M01a." + column04);
						}
						// 動用止日
						Date column05 = (Date) dataCollection.get("gutcdate");
						// 案號
						String column06 = (String) dataCollection.get("projno");
						mapInTitleRows.put("ReportBean.column01", column01);
						mapInTitleRows.put("ReportBean.column02", column02);
						mapInTitleRows.put("ReportBean.column03", column03);
						mapInTitleRows.put("ReportBean.column04", column04);
						mapInTitleRows.put("ReportBean.column05",
								Util.trim(TWNDate.toAD((column05))));
						mapInTitleRows.put("ReportBean.column06", column06);
					}
					titleRows.add(mapInTitleRows);
				}
			} else {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} catch (Exception e) {
			throw new CapException();
		}
		return titleRows;
	}

	/**
	 * 塞值LMS180R01 的欄位
	 * 
	 * @param titleRows
	 * @param l784s01aList
	 * @param prop
	 * @return
	 */
	private List<Map<String, String>> setL180R01TitleRows(
			List<Map<String, String>> titleRows, List<L784S01A> l784s01aList,
			Properties prop, Locale locale) {
		Map<String, String> mapInTitleRows = null;
		try {
			if (!l784s01aList.isEmpty()) {
				// 總共有K筆資料
				int k = l784s01aList.size();
				// 為了column11信評資料要接露PD+LGD，這個map幫忙存PD可以減少查過多重複資料
				Map<String, String> pdGroupMap = new HashMap<String,String>();
				for (int j = 0; j < k; j++) {
					L784S01A l784s01a = l784s01aList.get(j);
					mapInTitleRows = Util.setColumnMap();
					if (l784s01a != null) {
						// 核定日
						Date column01 = l784s01a.getEndDate();
						// 客戶統一編號
						String column02 = l784s01a.getCustId();
						// 戶名
						String column03 = l784s01a.getCustName();
						// 額度序號
						String column04 = l784s01a.getCntrNo();
						// 授信科目
						String column05 = Util.trim(l784s01a.getLnSubject());
						String curr = l784s01a.getCurrentApplyCurr();
						// 額度(元)
						BigDecimal column06 = LMSUtil.toBigDecimal(l784s01a
								.getCurrentApplyAmt());

						// 期間
						// String column07 = l784s01a.getDesp1();
						// 備註_敘作續約或變更條件 L140M01A.property
						String column08 = LMSUtil.getProPerty(
								l784s01a.getProperty(), prop);
						// 核准人 STAFFNO
						String column09 = Util.trim(userInfoService
								.getUserName(l784s01a.getStaffNo()));
						// 報表亂碼 randomCode
						String column10 = l784s01a.getRandomCode();

						// J-109-0092_10702_B1001 Web
						// e-Loan已敘做授信案件清單及區域授信管理中心授權內外已核准/已婉卻授信案件報表，增加企、消金信用評等資料修改
						String L120M01A_mainId = l784s01a.getL120M01A_MainId();
						String L140M01A_mainId = l784s01a.getL140M01A_MainId();
						String column11 = null;
						String column12 = null;
						if (Util.isNotEmpty(L120M01A_mainId)
								&& Util.isNotEmpty(L140M01A_mainId)) {
							L120M01A l120m01a = service1201
									.findL120m01aByMainId(L120M01A_mainId);
							String docType = l120m01a.getDocType();
							// 企金
							if (Util.nullToSpace(docType).equals("1")) {
								// L120S01A．借款人主檔
								List<L120S01A> l120s01aList = service1201
										.findL120s01aByMainIdForOrder(L120M01A_mainId);

								L140M01A l140m01a = service1405
										.findL140m01aByMainId(L140M01A_mainId);
								if (l140m01a == null) {
									l140m01a = new L140M01A();
								}
								List<L120S01C> l120s01cList = l120s01cList = service1201
										.findL120s01cByMainId(L120M01A_mainId);
								L120S01A l120s01aFor140 = null;
								for (L120S01A l120s01a : l120s01aList) {
									if (Util.nullToSpace(l120s01a.getCustId())
											.equals(Util.nullToSpace(l140m01a
													.getCustId()))
											&& Util.nullToSpace(
													l120s01a.getCustId())
													.equals(Util
															.nullToSpace(l140m01a
																	.getCustId()))) {
										l120s01aFor140 = l120s01a;
									}
								}
								if (l120s01aFor140 == null)
									l120s01aFor140 = new L120S01A();
								Map<String, String> crdTypeMap = codetypeservice
										.findByCodeType("CRDType",
												locale.toString());
								column11 = this.setL120S01CData(l120s01aFor140,
										l120s01cList, crdTypeMap, prop,
										l140m01a);

								// J-113-0233 配合PD/LGD授權架構之應用
								String LMS_L180R01_SHOW_PDLGD = Util
										.trim(lmsService
												.getSysParamDataValue("LMS_L180R01_SHOW_PDLGD"));
								if ("Y".equals(LMS_L180R01_SHOW_PDLGD)) {
									String pdLgdInfo = lmsService
											.getPdGroupAndLgd(l120m01a,
													l140m01a, pdGroupMap);
									column11 = column11 + "<BR/>" + pdLgdInfo;
								}
							}
							// 消金
							else {
								column11 = null;
							}

							// J-111-0283
							// E-LOAN簽報，關係企業額度已逾分行權限，在案件簽報畫面明顯位置處，加註「疑似逾越授權」之註記
							L120M01I l120m01i = service1201
									.findL120m01iByMainId(L120M01A_mainId);
							column12 = "";
							if (l120m01i != null) {
								if ("Y".equals(Util.trim(l120m01i
										.getOverAuthLoan()))
										|| "Y".equals(Util.trim(l120m01i
												.getOverAuthExperf()))
										|| "Y".equals(Util.trim(l120m01i
												.getOverAuthAloneLoan()))) {
									column12 = "Y";
								}
							}
						}
						mapInTitleRows.put("ReportBean.column01",
								Util.trim(TWNDate.toAD((column01))));
						mapInTitleRows.put("ReportBean.column02", column02);
						mapInTitleRows.put("ReportBean.column03", column03);
						mapInTitleRows.put("ReportBean.column04", column04);
						mapInTitleRows.put("ReportBean.column05", column05);
						mapInTitleRows.put("ReportBean.column06", curr + " "
								+ NumConverter.addComma(column06));
						mapInTitleRows
								.put("ReportBean.column07",
										LMSUtil.getUseDeadline(
												Util.trim(l784s01a
														.getUseDeadline()),
												Util.trim(l784s01a.getDesp1()),
												MessageBundleScriptCreator
														.getComponentResource(CLS1141R01RptServiceImpl.class)));
						mapInTitleRows.put("ReportBean.column08", column08);
						mapInTitleRows.put("ReportBean.column09", column09);
						mapInTitleRows.put("ReportBean.column10", column10);
						mapInTitleRows.put("ReportBean.column11", column11);
						mapInTitleRows.put("ReportBean.column12", column12);
					}
					titleRows.add(mapInTitleRows);
				}
			} else {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} finally {

		}
		return titleRows;
	}

	/**
	 * 塞值 TitleRows
	 * 
	 * @param titleRows
	 * @param dataCollection
	 * @return
	 * @throws CapException
	 */
	private List<Map<String, String>> setLMS180R15DataTitleRows(
			List<Map<String, String>> titleRows,
			List<Map<String, Object>> list, Map<String, String> docTypeMap)
			throws CapException {
		Map<String, String> mapInTitleRows = null;
		try {
			mapInTitleRows = Util.setColumnMap();

			if (!list.isEmpty()) {
				// 總共有K筆資料
				int k = list.size();
				for (int j = 0; j < k; j++) {
					Map<String, Object> dataCollection = list.get(j);
					if (dataCollection != null) {
						mapInTitleRows = Util.setColumnMap();

						String column01 = Util.trim(dataCollection
								.get("CASEBRID"));
						// 客戶統一編號
						String column02 = Util.trim(dataCollection
								.get("CUSTID"));
						// 戶名
						String column03 = Util.trim(dataCollection
								.get("CUSTNAME"));
						// 案號
						String column04 = Util.trim(dataCollection
								.get("CASENO"));
						// 分屬
						String column05 = Util.trim(docTypeMap.get(Util
								.trim(dataCollection.get("DOCTYPE"))));
						// 營運中心負責經辦
						String column06 = Util.trim(userInfoService
								.getUserName(Util.trim(dataCollection
										.get("AREAAPPRAISER"))));
						// 分行最後送件日
						String column07 = Util.trim(dataCollection
								.get("AREASENDINFO"));
						// 報表亂碼 randomCode
						String column08 = Util.trim(dataCollection
								.get("RANDOMCODE"));
						mapInTitleRows.put("ReportBean.column01", column01);
						mapInTitleRows.put("ReportBean.column02", column02);
						mapInTitleRows.put("ReportBean.column03", column03);
						mapInTitleRows.put("ReportBean.column04", column04);
						mapInTitleRows.put("ReportBean.column05", column05);
						mapInTitleRows.put("ReportBean.column06", column06);
						mapInTitleRows.put("ReportBean.column07", column07);
						mapInTitleRows.put("ReportBean.column08", column08);
					}
					titleRows.add(mapInTitleRows);
				}

			} else {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} catch (Exception e) {
			throw new CapException();
		}
		return titleRows;
	}

	/**
	 * 塞值LMS180R01 RptVariableMap
	 * 
	 * @param rptVariableMap
	 * @param list
	 * @return
	 * @throws CapException
	 */
	private Map<String, String> setL180R01TitleRowsRptVariableMap(
			Map<String, String> rptVariableMap, LMSRPT lmsrpt)
			throws CapException {
		try {
			rptVariableMap.put("LMS9515R02.year",
					TWNDate.toAD(lmsrpt.getDataDate()).split("-")[0]);
			rptVariableMap.put("LMS9515R02.month",
					TWNDate.toAD(lmsrpt.getDataDate()).split("-")[1]);
			rptVariableMap.put("L784M01A.RANDOMCODE",
					Util.trim(lmsrpt.getRandomCode()));
			rptVariableMap.put("L784M01A.BRNAME", Util.trim(branchService
					.getBranchName(Util.trim(lmsrpt.getBranch()))));

		} catch (Exception e) {
			throw new CapException();
		}
		return rptVariableMap;
	}

	/**
	 * 塞值LMS180R01 的欄位
	 * 
	 * @param titleRows
	 * @param l784s01aList
	 * @param prop
	 * @return
	 */
	@SuppressWarnings("unused")
	private List<Map<String, String>> setL180R17TitleRows(
			List<Map<String, String>> titleRows, List<Map<String, Object>> list) {
		try {
			Map<String, String> mapInTitleRows = null;
			mapInTitleRows = Util.setColumnMap();
			if (!list.isEmpty()) {
				for (Map<String, Object> dataCollection : list) {
					mapInTitleRows = Util.setColumnMap();

					String column01 = Util.trim((String) dataCollection
							.get("brNo"));
					// 新作
					BigDecimal column02 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM1"));
					BigDecimal column03 = Arithmetic.div(LMSUtil
							.toBigDecimal(dataCollection.get("tCITEM1AMT")),
							new BigDecimal(1000), 0);

					// 續約
					BigDecimal column04 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM2"));
					BigDecimal column05 = Arithmetic.div(LMSUtil
							.toBigDecimal(dataCollection.get("tCITEM2AMT")),
							new BigDecimal(1000), 0);

					// 變更條件
					BigDecimal column06 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM3"));
					BigDecimal column07 = Arithmetic.div(LMSUtil
							.toBigDecimal(dataCollection.get("tCITEM3AMT")),
							new BigDecimal(1000), 0);

					// 逾放展期轉正常
					BigDecimal column08 = null;
					BigDecimal column09 = null;

					// 合計
					BigDecimal column10 = column02.add(column04).add(column06);
					BigDecimal column11 = column03.add(column05).add(column07);
					// 無擔保授信
					BigDecimal column12 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM4"));
					BigDecimal column13 = Arithmetic.div(LMSUtil
							.toBigDecimal(dataCollection.get("tCITEM4AMT")),
							new BigDecimal(1000), 0);

					// 擔保授信
					BigDecimal column14 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM5"));
					BigDecimal column15 = Arithmetic.div(LMSUtil
							.toBigDecimal(dataCollection.get("tCITEM5AMT")),
							new BigDecimal(1000), 0);

					BigDecimal column16 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM6"));
					BigDecimal column17 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM7"));
					BigDecimal column18 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM8"));
					BigDecimal column19 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM9"));
					BigDecimal column20 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM10"));
					BigDecimal column21 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM11"));

					mapInTitleRows.put("ReportBean.column01",
							branchService.getBranchName(column01));
					mapInTitleRows.put("ReportBean.column02",
							this.formatBigDecimal(column02));
					mapInTitleRows.put("ReportBean.column03",
							this.formatBigDecimal(column03));
					mapInTitleRows.put("ReportBean.column04",
							this.formatBigDecimal(column04));
					mapInTitleRows.put("ReportBean.column05",
							this.formatBigDecimal(column05));
					mapInTitleRows.put("ReportBean.column06",
							this.formatBigDecimal(column06));
					mapInTitleRows.put("ReportBean.column07",
							this.formatBigDecimal(column07));
					mapInTitleRows.put("ReportBean.column08", "");
					mapInTitleRows.put("ReportBean.column09", "");
					mapInTitleRows.put("ReportBean.column10",
							this.formatBigDecimal(column10));
					mapInTitleRows.put("ReportBean.column11",
							this.formatBigDecimal(column11));
					titleRows.add(mapInTitleRows);
				}
			} else {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} finally {

		}

		return titleRows;
	}

	private Map<String, String> setTitleRowsDataRptVariableMap(
			Map<String, String> rptVariableMap, List<Map<String, Object>> list)
			throws CapException {
		try {
			BigDecimal CITEM1Count = new BigDecimal(0);
			BigDecimal CITEM2Count = new BigDecimal(0);
			BigDecimal CITEM3Count = new BigDecimal(0);
			BigDecimal CITEM4Count = new BigDecimal(0);// 無擔保授信
			BigDecimal CITEM5Count = new BigDecimal(0);// 擔保授信
			BigDecimal totalCount = new BigDecimal(0);
			BigDecimal CITEM1Amt = new BigDecimal(0);
			BigDecimal CITEM2Amt = new BigDecimal(0);
			BigDecimal CITEM3Amt = new BigDecimal(0);
			BigDecimal CITEM4Amt = new BigDecimal(0);
			BigDecimal CITEM5Amt = new BigDecimal(0);
			BigDecimal totalAmt = new BigDecimal(0);
			for (Map<String, Object> dataCollection : list) {
				// 新作
				BigDecimal column02 = LMSUtil.toBigDecimal(dataCollection
						.get("tCITEM1"));
				BigDecimal column03 = Arithmetic.div(
						LMSUtil.toBigDecimal(dataCollection.get("tCITEM1AMT")),
						new BigDecimal(1000), 0);

				// 續約
				BigDecimal column04 = LMSUtil.toBigDecimal(dataCollection
						.get("tCITEM2"));
				BigDecimal column05 = Arithmetic.div(
						LMSUtil.toBigDecimal(dataCollection.get("tCITEM2AMT")),
						new BigDecimal(1000), 0);

				// 變更條件
				BigDecimal column06 = LMSUtil.toBigDecimal(dataCollection
						.get("tCITEM3"));
				BigDecimal column07 = Arithmetic.div(
						LMSUtil.toBigDecimal(dataCollection.get("tCITEM3AMT")),
						new BigDecimal(1000), 0);

				// 逾放展期轉正常
				// BigDecimal column08 = null;
				// BigDecimal column09 = null;

				// 合計
				BigDecimal column10 = column02.add(column04).add(column06);
				BigDecimal column11 = column03.add(column05).add(column07);
				// 無擔保授信
				BigDecimal column12 = LMSUtil.toBigDecimal(dataCollection
						.get("tCITEM4"));
				BigDecimal column13 = Arithmetic.div(
						LMSUtil.toBigDecimal(dataCollection.get("tCITEM4AMT")),
						new BigDecimal(1000), 0);

				// 擔保授信
				BigDecimal column14 = LMSUtil.toBigDecimal(dataCollection
						.get("tCITEM5"));
				BigDecimal column15 = Arithmetic.div(
						LMSUtil.toBigDecimal(dataCollection.get("tCITEM5AMT")),
						new BigDecimal(1000), 0);

				CITEM1Count = CITEM1Count.add(column02);
				CITEM2Count = CITEM2Count.add(column04);
				CITEM3Count = CITEM3Count.add(column06);
				totalCount = totalCount.add(column10);
				CITEM4Count = CITEM4Count.add(column12);
				CITEM5Count = CITEM5Count.add(column14);
				CITEM1Amt = CITEM1Amt.add(column03);
				CITEM2Amt = CITEM2Amt.add(column05);
				CITEM3Amt = CITEM3Amt.add(column07);
				totalAmt = totalAmt.add(column11);
				CITEM4Amt = CITEM4Amt.add(column13);
				CITEM5Amt = CITEM5Amt.add(column15);
			}
			rptVariableMap.put("SUMCOLUMN02",
					NumConverter.addComma(CITEM1Count));
			rptVariableMap.put("SUMCOLUMN04",
					NumConverter.addComma(CITEM2Count));
			rptVariableMap.put("SUMCOLUMN06",
					NumConverter.addComma(CITEM3Count));
			rptVariableMap.put("SUMCOLUMN08", "0");
			rptVariableMap
					.put("SUMCOLUMN10", NumConverter.addComma(totalCount));
			rptVariableMap.put("SUMCOLUMN12",
					NumConverter.addComma(CITEM4Count));
			rptVariableMap.put("SUMCOLUMN14",
					NumConverter.addComma(CITEM5Count));
			rptVariableMap.put("SUMCOLUMN03", NumConverter.addComma(CITEM1Amt));
			rptVariableMap.put("SUMCOLUMN05", NumConverter.addComma(CITEM2Amt));
			rptVariableMap.put("SUMCOLUMN07", NumConverter.addComma(CITEM3Amt));
			rptVariableMap.put("SUMCOLUMN09", "0");
			rptVariableMap.put("SUMCOLUMN11", NumConverter.addComma(totalAmt));
			rptVariableMap.put("SUMCOLUMN13", NumConverter.addComma(CITEM4Amt));
			rptVariableMap.put("SUMCOLUMN15", NumConverter.addComma(CITEM5Amt));
		} catch (Exception e) {
			throw new CapException();
		}
		return rptVariableMap;
	}

	private String formatBigDecimal(BigDecimal number) {
		if (number == null) {
			return "";
		} else if ("0".equals(String.valueOf(number))) {
			return "";
		} else {
			return NumConverter.addComma(number);
		}
	}

	@Override
	public String generateLMS180R23Report(LMSBATCH batch) throws CapException,
			IOException, Exception {
		DocFile docFile = this.gfnCTLGenAssessBatch(batch,
				retrialService.get_lrs_rpt_brMap(batch.getRemarks()),
				LrsUtil.CTLTYPE_主辦覆審);
		return docFile.getOid();
	}

	@Override
	public String generateLMS180R23BReport(LMSBATCH batch) throws CapException,
			IOException, Exception {
		DocFile docFile = this.gfnCTLGenAssessBatch(batch,
				retrialService.get_lrs_rpt_brMap(batch.getRemarks()),
				LrsUtil.CTLTYPE_自辦覆審);
		return docFile.getOid();
	}

	@Override
	public String generateCLS180R17Report(LMSBATCH batch) throws CapException,
			IOException, Exception {
		DocFile docFile = null;
		String filename = LMSUtil.getUploadFilePath(batch.getBranch(),
				batch.getMainId(), batch.getRptNo());
		try {
			String fileExt = "zip";
			docFile = lms9511Service.saveDocFile(batch.getBranch(),
					batch.getMainId(), batch.getRptNo(), fileExt);
			// 設定下載的 xls 名稱
			docFile.setSrcFileName(batch.getRptName() + "." + fileExt);
			fileService.save(docFile, false);

			File zip_file = new File(filename);
			zip_file.mkdirs();
			zip_file = new File(filename + "/" + docFile.getOid() + "."
					+ fileExt);

			if (true) {
				ZipOutputStream zops = new ZipOutputStream(
						new FileOutputStream(zip_file));
				if (true) {
					ByteArrayOutputStream baos = new ByteArrayOutputStream();
					try {
						_CLS180R17(baos, batch.getBgnDate(),
								batch.getEndDate(), "M");
						if (true) {
							// java.util.zip.ZipOutputStream 的 entry 放中文會亂碼
							ZipEntry entry = new ZipEntry(
									StringUtils.substring(Util.trim(TWNDate
											.toAD(batch.getBgnDate())), 0, 7)
											+ "_singleMonth.xls");
							zops.putNextEntry(entry);

							if (true) {
								InputStream src = new ByteArrayInputStream(
										baos.toByteArray());
								IOUtils.copy(src, zops);
								IOUtils.closeQuietly(src);
							}
							zops.closeEntry();
						}
					} catch (Exception ex) {
						logger.error(StrUtils.getStackTrace(ex));
					} finally {
						IOUtils.closeQuietly(baos);
					}
				}
				if (true) {
					ByteArrayOutputStream baos = new ByteArrayOutputStream();
					try {
						_CLS180R17(baos, batch.getBgnDate(),
								batch.getEndDate(), "Y");
						if (true) {
							// java.util.zip.ZipOutputStream 的 entry 放中文會亂碼
							ZipEntry entry = new ZipEntry(
									StringUtils.substring(Util.trim(TWNDate
											.toAD(batch.getBgnDate())), 0, 4)
											+ "-01~"
											+ StringUtils.substring(Util
													.trim(TWNDate.toAD(batch
															.getEndDate())), 0,
													7) + "_accumulation.xls");
							zops.putNextEntry(entry);

							if (true) {
								InputStream src = new ByteArrayInputStream(
										baos.toByteArray());
								IOUtils.copy(src, zops);
								IOUtils.closeQuietly(src);
							}
							zops.closeEntry();
						}
					} catch (Exception ex) {
						logger.error(StrUtils.getStackTrace(ex));
					} finally {
						IOUtils.closeQuietly(baos);
					}
				}
				IOUtils.closeQuietly(zops);
			}

		} finally {

		}
		return docFile.getOid();
	}

	private void _CLS180R17(ByteArrayOutputStream baos, Date bgnDate,
			Date endDate, String type) throws WriteException, IOException {
		Label label = null;
		WritableFont font12 = null;
		WritableCellFormat fmt_header = null;
		WritableCellFormat fmt_content_str = null;
		WritableCellFormat fmt_content_str_sumArea = null;
		WritableCellFormat fmt_content_str_sumTot = null;
		WritableCellFormat amtTWDFormat = null;
		WritableCellFormat amtTWDFormat_sumArea = null;
		WritableCellFormat amtTWDFormat_sumTot = null;
		WritableWorkbook workbook = null;
		WritableSheet sheet = null;
		String logic = "v2";
		try {
			// 字型設定
			font12 = new WritableFont(WritableFont.createFont("標楷體"), 12);
			fmt_header = LMSUtil.setCellFormat(fmt_header, font12,
					Alignment.LEFT, false, false);
			fmt_content_str = LMSUtil.setCellFormat(fmt_content_str, font12,
					Alignment.LEFT, true, false);
			fmt_content_str_sumArea = LMSUtil.setCellFormat(fmt_content_str,
					font12, Alignment.LEFT, true, false);
			if (true) {
				fmt_content_str_sumArea.setBackground(jxl.format.Colour.YELLOW);
			}
			fmt_content_str_sumTot = LMSUtil.setCellFormat(fmt_content_str,
					font12, Alignment.LEFT, true, false);
			if (true) {
				fmt_content_str_sumTot.setBackground(jxl.format.Colour.ORANGE);
			}
			// =============
			amtTWDFormat = new WritableCellFormat(font12, NumberFormats.FORMAT1);
			if (true) {
				amtTWDFormat.setVerticalAlignment(VerticalAlignment.TOP);
				amtTWDFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
			}
			amtTWDFormat_sumArea = new WritableCellFormat(font12,
					NumberFormats.FORMAT1);
			if (true) {
				amtTWDFormat_sumArea
						.setVerticalAlignment(VerticalAlignment.TOP);
				amtTWDFormat_sumArea
						.setBorder(Border.ALL, BorderLineStyle.THIN);
				amtTWDFormat_sumArea.setBackground(jxl.format.Colour.YELLOW);
			}
			amtTWDFormat_sumTot = new WritableCellFormat(font12,
					NumberFormats.FORMAT1);
			if (true) {
				amtTWDFormat_sumTot.setVerticalAlignment(VerticalAlignment.TOP);
				amtTWDFormat_sumTot.setBorder(Border.ALL, BorderLineStyle.THIN);
				amtTWDFormat_sumTot.setBackground(jxl.format.Colour.ORANGE);
			}

			workbook = Workbook.createWorkbook(baos);
			String[] sheet_arr = { "H", "C", "O" };

			String str_bgnDate = Util.trim(TWNDate.toAD(bgnDate));
			String str_bgnYYY_roc = String.valueOf(Util.parseInt(StringUtils
					.substring(str_bgnDate, 0, 4)) - 1911);
			String str_endDate = Util.trim(TWNDate.toAD(endDate));
			String str_endMM = String.valueOf(Util.parseInt(StringUtils
					.substring(str_endDate, 5, 7)));
			String str_endDD = String.valueOf(Util.parseInt(StringUtils
					.substring(str_endDate, 8, 10)));
			String data_ym = StringUtils.substring(str_endDate, 0, 7);

			Map<String, String> brnGoupMap = new HashMap<String, String>();
			if (true) {
				String[] area_arr = { UtilConstants.BankNo.北一區營運中心,
						UtilConstants.BankNo.北二區營運中心,
						UtilConstants.BankNo.桃竹苗區營運中心,
						UtilConstants.BankNo.中區營運中心,
						UtilConstants.BankNo.南區營運中心 };
				for (String brNo : area_arr) {
					IBranch iBranch = branchService.getBranch(brNo);
					if (iBranch != null) {
						brnGoupMap.put(Util.trim(iBranch.getBrNo()),
								Util.trim(iBranch.getNameABBR()));
					}
				}
			}
			for (String sheet_flag : sheet_arr) {
				String sheetName = "";
				int sheet_idx = 0;
				if (Util.equals("H", sheet_flag)) {
					sheetName = "房貸";
					sheet_idx = 0;
				} else if (Util.equals("C", sheet_flag)) {
					sheetName = "消金貸款(扣除房貸)";
					sheet_idx = 1;
				} else if (Util.equals("O", sheet_flag)) {
					sheetName = "其他(同一簽報書含消貸及房貸)";
					sheet_idx = 2;
				} else {
					sheetName = sheet_flag;
				}

				sheet = workbook.createSheet(sheetName, sheet_idx);
				SheetSettings setting = sheet.getSettings();
				setting.setPaperSize(PaperSize.A4);

				int rowIdx = 0;
				if (true) {
					String desc_p1 = "";
					if (Util.equals("M", type)) {
						desc_p1 = str_bgnYYY_roc + "年" + str_endMM + "月";
					} else if (Util.equals("Y", type)) {
						desc_p1 = str_bgnYYY_roc + "年1-" + str_endMM + "月";
					} else {
						desc_p1 = "type=" + type;
					}
					String desc_p2 = "";
					if (Util.equals("H", sheet_flag)) {
						desc_p2 = "新作房貸簽案、核准情形";
					} else if (Util.equals("C", sheet_flag)) {
						desc_p2 = "新作消金案件(不含房貸)簽案、核准情形";
					} else if (Util.equals("O", sheet_flag)) {
						desc_p2 = "其他(同一簽報書含房貸及消貸)簽案、核准情形";
					}

					int colIdx = 0;
					sheet.mergeCells(colIdx, rowIdx, colIdx + 6, rowIdx);
					label = new Label(colIdx, rowIdx, desc_p1 + desc_p2,
							fmt_header);
					sheet.addCell(label);
				}
				if (true) {
					rowIdx = 1;
					// ===
					int colIdx = 6;
					label = new Label(colIdx, rowIdx, "單位：件數/TWD仟元", fmt_header);
					sheet.addCell(label);
				}

				if (true) {
					sheet.setColumnView(0, 10); // 營運中心
					sheet.setColumnView(1, 20); // 分行代號
					sheet.setColumnView(2, 35); // 分行名稱

					sheet.setColumnView(3, 15); // 件數
					sheet.setColumnView(4, 20); // 金額

					sheet.setColumnView(5, 15); // 件數
					sheet.setColumnView(6, 20); // 金額

					if (true) {
						rowIdx = 2;
						// ===
						int colIdx = 0;
						sheet.mergeCells(colIdx, rowIdx, colIdx, rowIdx + 1);
						label = new Label(colIdx, rowIdx, "", fmt_content_str);
						sheet.addCell(label);
						// ===
						++colIdx;
						sheet.mergeCells(colIdx, rowIdx, colIdx, rowIdx + 1);
						label = new Label(colIdx, rowIdx, "分行代號",
								fmt_content_str);
						sheet.addCell(label);
						// ===
						++colIdx;
						sheet.mergeCells(colIdx, rowIdx, colIdx, rowIdx + 1);
						label = new Label(colIdx, rowIdx, "分行名稱",
								fmt_content_str);
						sheet.addCell(label);
					}
					if (true) {
						int colIdx = 3;
						sheet.mergeCells(colIdx, rowIdx, colIdx + 1, rowIdx);
						label = new Label(colIdx, rowIdx, (Util.equals("M",
								type) ? "當月份簽案" : "累計簽案"), fmt_content_str);
						sheet.addCell(label);
						if (true) {
							label = new Label(colIdx, rowIdx + 1, "件數",
									fmt_content_str);
							sheet.addCell(label);
							// ===
							label = new Label(colIdx + 1, rowIdx + 1, "金額",
									fmt_content_str);
							sheet.addCell(label);
						}
					}
					if (true) {
						int colIdx = 5;
						sheet.mergeCells(colIdx, rowIdx, colIdx + 1, rowIdx);
						label = new Label(colIdx, rowIdx, (Util.equals("M",
								type) ? "當月份核准" : "累計核准"), fmt_content_str);
						sheet.addCell(label);
						if (true) {
							label = new Label(colIdx, rowIdx + 1, "件數",
									fmt_content_str);
							sheet.addCell(label);
							// ===
							label = new Label(colIdx + 1, rowIdx + 1, "金額",
									fmt_content_str);
							sheet.addCell(label);
						}
					}
					++rowIdx;
				}
				if (true) {
					List<Map<String, Object>> raw_data_list = new ArrayList<Map<String, Object>>();
					if (Util.equals("M", type)) {
						if (Util.equals(logic, "v1")) {
							raw_data_list = eloandbBASEService
									.findC900S02C_fetch1(sheet_flag, data_ym,
											str_bgnDate, str_endDate);
						} else if (Util.equals(logic, "v2")) {
							raw_data_list = eloandbBASEService
									.findC900S02C_fetch2(sheet_flag, data_ym,
											str_bgnDate, str_endDate);
						}
					} else if (Util.equals("Y", type)) {
						raw_data_list = eloandbBASEService.findC900S02C_fetch2(
								sheet_flag, data_ym,
								StringUtils.substring(str_endDate, 0, 4)
										+ "-01-01", str_endDate);
					}
					LinkedHashMap<String, List<Map<String, Object>>> group_list = new LinkedHashMap<String, List<Map<String, Object>>>();
					for (Map<String, Object> map : raw_data_list) {
						String BRNGROUP = MapUtils.getString(map, "BRNGROUP");

						if (!group_list.containsKey(BRNGROUP)) {
							group_list.put(BRNGROUP,
									new ArrayList<Map<String, Object>>());
						}
						group_list.get(BRNGROUP).add(map);
					}

					Integer tot_CNT_1 = 0;
					Integer tot_AMT_1 = 0;
					Integer tot_CNT_2 = 0;
					Integer tot_AMT_2 = 0;
					for (String BRNGROUP : group_list.keySet()) {
						Integer brnGroup_CNT_1 = 0;
						Integer brnGroup_AMT_1 = 0;
						Integer brnGroup_CNT_2 = 0;
						Integer brnGroup_AMT_2 = 0;
						for (Map<String, Object> map : group_list.get(BRNGROUP)) {
							Integer CNT_1 = MapUtils
									.getInteger(map, "CNT_1", 0);
							Integer AMT_1 = MapUtils
									.getInteger(map, "AMT_1", 0);
							Integer CNT_2 = MapUtils
									.getInteger(map, "CNT_2", 0);
							Integer AMT_2 = MapUtils
									.getInteger(map, "AMT_2", 0);
							String BRNO = MapUtils.getString(map, "BRNO");
							String BRNAME = MapUtils.getString(map, "BRNAME");
							// =========
							++rowIdx;
							if (brnGoupMap.containsKey(BRNGROUP)) {
								// 各營運中心
								label = new Label(0, rowIdx, LMSUtil.getDesc(
										brnGoupMap, BRNGROUP), fmt_content_str);
								sheet.addCell(label);
								label = new Label(1, rowIdx, BRNO,
										fmt_content_str);
								sheet.addCell(label);
								label = new Label(2, rowIdx, BRNAME,
										fmt_content_str);
								sheet.addCell(label);
							} else {
								// 007及201
								sheet.mergeCells(0, rowIdx, 2, rowIdx);
								label = new Label(0, rowIdx, BRNAME,
										fmt_content_str);
								sheet.addCell(label);
							}

							sheet.addCell(new jxl.write.Number(3, rowIdx,
									CNT_1, amtTWDFormat));
							sheet.addCell(new jxl.write.Number(4, rowIdx,
									AMT_1, amtTWDFormat));
							sheet.addCell(new jxl.write.Number(5, rowIdx,
									CNT_2, amtTWDFormat));
							sheet.addCell(new jxl.write.Number(6, rowIdx,
									AMT_2, amtTWDFormat));
							if (true) {
								brnGroup_CNT_1 += CNT_1;
								brnGroup_AMT_1 += AMT_1;
								brnGroup_CNT_2 += CNT_2;
								brnGroup_AMT_2 += AMT_2;
							}
							if (true) {
								tot_CNT_1 += CNT_1;
								tot_AMT_1 += AMT_1;
								tot_CNT_2 += CNT_2;
								tot_AMT_2 += AMT_2;
							}
						}
						if (brnGoupMap.containsKey(BRNGROUP)) {
							++rowIdx;
							// 各營運中心 的合計
							sheet.mergeCells(0, rowIdx, 2, rowIdx);
							label = new Label(0, rowIdx, "合計",
									fmt_content_str_sumArea);
							sheet.addCell(label);
							sheet.addCell(new jxl.write.Number(3, rowIdx,
									brnGroup_CNT_1, amtTWDFormat_sumArea));
							sheet.addCell(new jxl.write.Number(4, rowIdx,
									brnGroup_AMT_1, amtTWDFormat_sumArea));
							sheet.addCell(new jxl.write.Number(5, rowIdx,
									brnGroup_CNT_2, amtTWDFormat_sumArea));
							sheet.addCell(new jxl.write.Number(6, rowIdx,
									brnGroup_AMT_2, amtTWDFormat_sumArea));
						} else {
							// [007及201] 的合計
							// 不顯示
						}

					}
					if (true) {
						++rowIdx;
						// 總計=[各營運中心 的合計 + 007及201]
						sheet.mergeCells(0, rowIdx, 2, rowIdx);
						label = new Label(0, rowIdx, "總計",
								fmt_content_str_sumTot);
						sheet.addCell(label);
						sheet.addCell(new jxl.write.Number(3, rowIdx,
								tot_CNT_1, amtTWDFormat_sumTot));
						sheet.addCell(new jxl.write.Number(4, rowIdx,
								tot_AMT_1, amtTWDFormat_sumTot));
						sheet.addCell(new jxl.write.Number(5, rowIdx,
								tot_CNT_2, amtTWDFormat_sumTot));
						sheet.addCell(new jxl.write.Number(6, rowIdx,
								tot_AMT_2, amtTWDFormat_sumTot));
					}
				}

				if (true) {
					int colIdx = 0;
					// ===
					List<String> memo_list = new ArrayList<String>();
					if (true) {
						String rocM_period = str_bgnYYY_roc + "/" + str_endMM
								+ "/1~" + str_bgnYYY_roc + "/" + str_endMM
								+ "/" + str_endDD;
						String rocY_period = str_bgnYYY_roc + "/1/1~"
								+ str_bgnYYY_roc + "/" + str_endMM + "/"
								+ str_endDD;
						if (Util.equals("M", type)) {
							if (Util.equals(logic, "v1")) {
								memo_list.add("簽報：指E-LOAN系統" + rocM_period
										+ "簽報");
								memo_list.add("核准：指E-LOAN系統" + rocM_period
										+ "核准");
							} else if (Util.equals(logic, "v2")) {
								memo_list.add("簽報：指E-LOAN系統" + rocM_period
										+ "簽報");
								memo_list.add("核准：指E-LOAN系統" + rocM_period
										+ "簽報且" + rocM_period + "核准");
							}

						} else if (Util.equals("Y", type)) {
							memo_list.add("簽報：指E-LOAN系統" + rocY_period + "簽報");
							memo_list.add("核准：指E-LOAN系統" + rocY_period + "簽報且"
									+ rocY_period + "核准");
						} else {

						}
					}
					for (String memo : memo_list) {
						++rowIdx;
						// =========
						// sheet.mergeCells(colIdx, rowIdx, colIdx+ 6 , rowIdx);
						label = new Label(colIdx, rowIdx, memo, fmt_header);
						sheet.addCell(label);
					}
				}
			}
			workbook.write();
			workbook.close();
		} finally {

		}
	}

	private DocFile gfnCTLGenAssessBatch(LMSBATCH batch,
			Map<String, String> brInfoMap, String ctlType)
			throws WriteException, IOException, Exception {

		DocFile docFile = null;
		String filename = LMSUtil.getUploadFilePath(batch.getBranch(),
				batch.getMainId(), batch.getRptNo());
		try {
			String fileExt = "zip";
			docFile = lms9511Service.saveDocFile(batch.getBranch(),
					batch.getMainId(), batch.getRptNo(), fileExt);
			// 設定下載的 xls 名稱
			docFile.setSrcFileName(batch.getRptName() + "." + fileExt);
			fileService.save(docFile, false);

			File zip_file = new File(filename);
			zip_file.mkdirs();
			zip_file = new File(filename + "/" + docFile.getOid() + "."
					+ fileExt);

			if (Util.equals(ctlType, "")) {
				ctlType = LrsUtil.CTLTYPE_主辦覆審;
			}

			if (true) {
				String queryCondition = _period(batch.getBgnDate(),
						batch.getEndDate());
				ZipOutputStream zops = new ZipOutputStream(
						new FileOutputStream(zip_file));
				for (String brNo : brInfoMap.keySet()) {

					ByteArrayOutputStream baos = new ByteArrayOutputStream();
					try {
						List<String> matchExeDate = new ArrayList<String>();

						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能

						_LMS180R23(baos, brNo, brInfoMap.get(brNo),
								batch.getBgnDate(), batch.getEndDate(),
								matchExeDate, ctlType);

						if (true) {
							// java.util.zip.ZipOutputStream 的 entry 放中文會亂碼
							ZipEntry entry = new ZipEntry(brNo + "("
									+ queryCondition + ")" + ".doc");
							zops.putNextEntry(entry);

							if (true) {
								InputStream src = new ByteArrayInputStream(
										baos.toByteArray());
								IOUtils.copy(src, zops);
								IOUtils.closeQuietly(src);
							}
							zops.closeEntry();
						}
					} catch (Exception ex) {
						logger.error(StrUtils.getStackTrace(ex));
					} finally {
						IOUtils.closeQuietly(baos);
					}
				}
				if (true) {
					ByteArrayOutputStream baos = new ByteArrayOutputStream();
					try {
						_LMS180R23_SummaryXls(baos, brInfoMap,
								batch.getBgnDate(), batch.getEndDate(), ctlType);
						if (true) {
							// java.util.zip.ZipOutputStream 的 entry 放中文會亂碼
							ZipEntry entry = new ZipEntry("Summary.xls");
							zops.putNextEntry(entry);

							if (true) {
								InputStream src = new ByteArrayInputStream(
										baos.toByteArray());
								IOUtils.copy(src, zops);
								IOUtils.closeQuietly(src);
							}
							zops.closeEntry();
						}
					} catch (Exception ex) {
						logger.error(StrUtils.getStackTrace(ex));
					} finally {
						IOUtils.closeQuietly(baos);
					}
				}
				IOUtils.closeQuietly(zops);
			}

		} finally {

		}
		return docFile;
	}

	private String _period(Date d1, Date d2) {
		return StringUtils.substring(Util.trim(TWNDate.toAD(d1)), 0, 7) + "~"
				+ StringUtils.substring(Util.trim(TWNDate.toAD(d2)), 0, 7);
	}

	private void _addStr(StringBuffer sb, String s, String align) {
		String jc = align;
		if (Util.isEmpty(jc)) {
			jc = "left";
		}

		sb.append("<w:p><w:pPr><w:jc w:val='" + jc + "' /></w:pPr><w:r> <w:t>"
				+ s + "</w:t> </w:r> </w:p>");
	}

	private void _addTable(StringBuffer sb, String keyStr,
			List<String> itemList, String sys) {
		int detailCnt = itemList.size();
		int colCnt = 2;

		sb.append("<w:tbl>");
		if (true) {
			sb.append("<w:tblPr>");
			sb.append("<w:tblW w:w='5000' w:type='pct' />");
			sb.append("</w:tblPr>");
		}

		if (true) {
			Integer seq = NumberUtils.toInt(keyStr.substring(0,
					CrsUtil.SUMMARY_PREFIX_LEN));
			String seqStr = String.valueOf(seq);
			String content = keyStr.substring(CrsUtil.SUMMARY_PREFIX_LEN);
			if (Util.equals(sys, "lrs")) {
				if (seq == 12 || seq == 18) {// keyStr = 012N031..... //if (seq
												// == 0) {
					String itemNo = keyStr.substring(
							CrsUtil.SUMMARY_PREFIX_LEN,
							CrsUtil.SUMMARY_PREFIX_LEN + 4);
					if (Util.equals(itemNo, LrsUtil.N031)) {
						seqStr = "12-1";
						content = keyStr
								.substring(CrsUtil.SUMMARY_PREFIX_LEN + 4);
					} else if (Util.equals(itemNo, LrsUtil.N029)) {
						seqStr = "18-1";
						content = keyStr
								.substring(CrsUtil.SUMMARY_PREFIX_LEN + 4);
					}
				}
			}
			String itemTitle = "第" + seqStr + "項" + content + "共" + detailCnt
					+ "戶";
			sb.append("<w:tr>");
			if (true) {
				sb.append("<w:tc>");
				if (true) {
					sb.append("<w:tcPr><w:gridSpan w:val='" + colCnt
							+ "' /></w:tcPr>");
				}
				_addStr(sb, itemTitle, null);
				sb.append("</w:tc>");
			}
			sb.append("</w:tr>");
		}
		for (int i = 0; i < detailCnt; i++) {
			sb.append("<w:tr>");
			if (true) {
				String desc = String.valueOf("(" + (i + 1) + ")");

				sb.append("<w:tc>");
				if (true) {
					sb.append("<w:tcPr><w:tcW w:w='650' w:type='dxa' /><w:vAlign w:val='top' /></w:tcPr>");
				}
				_addStr(sb, desc, "right");
				sb.append("</w:tc>");
			}
			if (true) {
				String desc = XmlTool.replaceXMLReservedWord(itemList.get(i),
						true);

				sb.append("<w:tc>");
				if (true) {
					sb.append("<w:tcPr><w:vAlign w:val='top' /></w:tcPr>");
				}
				_addStr(sb, desc, null);
				sb.append("</w:tc>");

			}
			sb.append("</w:tr>");
		}
		sb.append("</w:tbl>");
	}

	private int _cnt_crs_retrial(List<List<C241M01A>> list) {
		int r = 0;
		for (List<C241M01A> l : list) {
			r += l.size();
		}
		return r;
	}

	private int _cnt_lrs_retrial(List<List<L170M01A>> list) {
		int r = 0;
		for (List<L170M01A> l : list) {
			r += l.size();
		}
		return r;
	}

	@SuppressWarnings("unchecked")
	private List<List<C241M01A>> _gen_crs_retrial_list(String brNo,
			String startDate, String endDate) {
		String dateCol = "expectedRetrialDate";
		List<List<C241M01A>> r = new ArrayList<List<C241M01A>>();
		ISearch search = retrialService.getMetaSearch();
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", brNo);
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS,
				RetrialDocStatusEnum.已產生覆審名單報告檔.getCode());
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS, dateCol,
				startDate);
		search.addSearchModeParameters(SearchMode.LESS_EQUALS, dateCol, endDate);
		search.addOrderBy(dateCol, false);

		List<C240M01A> c240m01a_list = (List<C240M01A>) retrialService
				.findPage(C240M01A.class, search).getContent();
		for (C240M01A meta : c240m01a_list) {
			List<C241M01A> detail_list = retrialService
					.getCrsRetrialSummaryList(meta);
			if (CollectionUtils.isNotEmpty(detail_list)) {
				r.add(detail_list);
			}
		}
		return r;
	}

	@SuppressWarnings("unchecked")
	private List<List<L170M01A>> _gen_lrs_retrial_list(String brNo,
			String startDate, String endDate, String ctlType) {
		String dateCol = "defaultCTLDate";
		List<List<L170M01A>> r = new ArrayList<List<L170M01A>>();
		ISearch search = retrialService.getMetaSearch();
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", brNo);
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS,
				RetrialDocStatusEnum.已產生覆審名單報告檔.getCode());
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS, dateCol,
				startDate);
		search.addSearchModeParameters(SearchMode.LESS_EQUALS, dateCol, endDate);
		search.addOrderBy(dateCol, false);

		List<L180M01A> l180m01a_list = (List<L180M01A>) retrialService
				.findPage(L180M01A.class, search).getContent();
		for (L180M01A meta : l180m01a_list) {
			List<L170M01A> detail_list = retrialService
					.getLrsRetrialSummaryList(meta, ctlType);
			if (CollectionUtils.isNotEmpty(detail_list)) {
				r.add(detail_list);
			}
		}
		return r;
	}

	private void _LMS180R23(ByteArrayOutputStream baos, String brNo,
			String brName, Date bgnDate, Date endDate,
			List<String> matchExeDate, String ctlType) throws Exception {
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = "LRSDOC1.xml";
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));

		// 開始讀取檔案
		URL urlRpt = Thread.currentThread().getContextClassLoader()
				.getResource(fileName.toString());
		File file = new File(urlRpt.toURI());

		String str_docx = FileUtils.readFileToString(file, "UTF-8");
		StringBuffer r = new StringBuffer();
		if (true) {
			String title = _period(bgnDate, endDate)
					+ " "
					+ brName
					+ " ("
					+ brNo
					+ ") 授信覆審考核表"
					+ (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審) ? "(董事會/常董會權限案件實地覆審)"
							: "(一般/土建融)");
			_addStr(r, title, "center");

			if (true) {
				LinkedHashSet<String> exeDateList = new LinkedHashSet<String>();
				if (true) {
					Date cmpDate = bgnDate;
					while (true) {
						if (LMSUtil.cmp_yyyyMM(cmpDate, ">", endDate)) {
							break;
						}
						exeDateList.add(TWNDate.toAD(cmpDate));
						// ---
						cmpDate = CapDate.addMonth(cmpDate, 1);
					}
				}

				int idx = 1;
				for (String strStartDate : exeDateList) {
					String yyyy_MM = StringUtils.substring(strStartDate, 0, 7);
					String strEndDate = CrsUtil.getDataEndDate(yyyy_MM);
					// ============================
					List<List<C241M01A>> crs_list = _gen_crs_retrial_list(brNo,
							strStartDate, strEndDate);
					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					List<List<L170M01A>> lrs_list = _gen_lrs_retrial_list(brNo,
							strStartDate, strEndDate, ctlType);
					// VLMS18137
					// VCLS24022
					// ============================
					int crsCnt = _cnt_crs_retrial(crs_list);
					int lrsCnt = _cnt_lrs_retrial(lrs_list);
					if (crsCnt == 0 && lrsCnt == 0) {
						continue;
					}

					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					// 個金沒有自辦覆審
					if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
						crsCnt = 0;
					} else {

					}

					matchExeDate.add(strStartDate);
					if (idx > 1) {
						// 不同年、月 中間的分隔行
						_addStr(r, "", null);
						_addStr(r, "", null);
					}

					String block_title = NumConverter
							.toChineseNumberFull(idx++) + "、覆審年月：" + yyyy_MM;
					_addStr(r, block_title, null);

					if (Util.notEquals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
						// 消金沒有自辦覆審
						if (true) {
							_addStr(r, "一、消金覆審戶數：" + crsCnt + "戶", null);
							if (crsCnt > 0) {
								for (List<C241M01A> c241m01a_list : crs_list) {
									boolean haveData = false;
									TreeMap<String, List<String>> map = retrialService
											.getCrsRetrialSummary(c241m01a_list);
									for (String keyStr : map.keySet()) {
										List<String> detailList = map
												.get(keyStr);
										if (CollectionUtils.isEmpty(detailList)) {
											continue;
										}
										haveData = true;

										_addTable(r, keyStr, detailList, "crs");
									}
									if (haveData == false) {
										_addStr(r, "本分行本次個金覆審無任何缺失。", null);
									}
								}
							} else {
								_addStr(r, "本次未覆審個金戶。", null);
							}
						}
					}
					// 消金、企金中間的分隔行
					_addStr(r, "", null);
					if (true) {
						if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
							_addStr(r, "一、企金覆審戶數：" + lrsCnt + "戶", null);
						} else {
							_addStr(r, "二、企金覆審戶數：" + lrsCnt + "戶", null);
						}

						if (lrsCnt > 0) {
							for (List<L170M01A> l170m01a_list : lrs_list) {
								boolean haveData = false;

								TreeMap<String, List<String>> map = null;
								if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
									map = retrialService
											.getLrsRetrialSummary_ctlTypeB(l170m01a_list);
								} else {
									map = retrialService.getLrsRetrialSummary(
											l170m01a_list, true);
								}

								for (String keyStr : map.keySet()) {
									List<String> detailList = map.get(keyStr);
									if (CollectionUtils.isEmpty(detailList)) {
										continue;
									}
									haveData = true;

									_addTable(r, keyStr, detailList, "lrs");
								}
								if (haveData == false) {
									_addStr(r, "本分行本次企金覆審無任何缺失。", null);
								}
							}
						} else {
							_addStr(r, "本次未覆審企金戶。", null);
						}
					}
				}
			}

			if (matchExeDate.size() == 0) {
				_addStr(r, "於 Web e-Loan 無覆審資料", null);
			}
		}

		String str = StringUtils.replace(str_docx, "%PARAM%", r.toString());

		OutputStreamWriter outWriter = new OutputStreamWriter(baos, "UTF-8");
		outWriter.write(str);
		outWriter.close();
	}

	private void _LMS180R23_SummaryXls(ByteArrayOutputStream baos,
			Map<String, String> brInfoMap, Date bgnDate, Date endDate,
			String ctlType) throws WriteException, IOException {
		Label label = null;
		WritableFont font12 = null;
		WritableCellFormat fmt_header = null;
		WritableCellFormat fmt_content_str = null;
		WritableCellFormat fmt_content_cnt = null;
		WritableWorkbook workbook = null;
		WritableSheet sheet1 = null;
		try {
			// 字型設定
			font12 = new WritableFont(WritableFont.createFont("標楷體"), 12);
			fmt_header = LMSUtil.setCellFormat(fmt_header, font12,
					Alignment.LEFT, false, false);
			fmt_content_str = LMSUtil.setCellFormat(fmt_content_str, font12,
					Alignment.LEFT, true, false);
			fmt_content_cnt = LMSUtil.setCellFormat(fmt_content_cnt, font12,
					Alignment.RIGHT, true, false);

			workbook = Workbook.createWorkbook(baos);
			sheet1 = workbook.createSheet("Sheet1", 0);
			SheetSettings setting = sheet1.getSettings();
			setting.setPaperSize(PaperSize.A4);

			// 103/01～103/12覆審國內營業單位授信貸後管理缺失考評彙總表_北區
			// 資料基準日：103/11/06
			int rowIdx = 0;
			if (true) {
				MegaSSOUserDetails user = MegaSSOSecurityContext
						.getUserDetails();
				IBranch ibranch = branchService.getBranch(user.getUnitNo());
				String desc_br = ibranch == null ? "" : ibranch.getNameABBR();
				int colIdx = 1;
				sheet1.mergeCells(colIdx, rowIdx, colIdx + 10, rowIdx);
				label = new Label(
						colIdx,
						rowIdx,
						LrsUtil.toStrYM(bgnDate)
								+ "～"
								+ LrsUtil.toStrYM(endDate)
								+ "覆審國內營業單位授信貸後管理缺失考評彙總表"
								+ (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審) ? "董事會(或常董會)權限案件實地覆審"
										: "(一般/土建融)") + "_"
								+ Util.trim(desc_br), fmt_header);
				sheet1.addCell(label);
			}
			if (true) {
				rowIdx = 1;
				// ===
				int colIdx = 1;
				sheet1.mergeCells(colIdx, rowIdx, colIdx + 1, rowIdx);
				label = new Label(1, rowIdx, "資料基準日："
						+ Util.trim(TWNDate.toAD(new Date())), fmt_header);
				sheet1.addCell(label);
			}

			if (true) {
				int lrs_item_cnt = 0;
				int crs_item_cnt = 0;

				if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
					lrs_item_cnt = 16;
					crs_item_cnt = 0;
				} else {
					lrs_item_cnt = 28;
					crs_item_cnt = 27;
					// J-108-0128 增加第28項
					if (LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=",
							CapDate.parseDate("2019-07-01"))) {
						crs_item_cnt = 28;
					}
				}

				Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
				headerMap.put("", 5);
				headerMap.put("分行代號", 10);
				headerMap.put("分行別", 20);
				for (int i = 1; i <= lrs_item_cnt; i++) {
					headerMap.put("企" + String.valueOf(i), 6);
					// N031 [12-1]、N029 [18-1] 拆項
					if (i == 12 || i == 18) {
						headerMap.put("企" + String.valueOf(i) + "-1", 7);
					}
				}
				for (int i = 1; i <= crs_item_cnt; i++) {
					headerMap.put("消" + String.valueOf(i), 6);
				}

				if (true) {
					rowIdx = 2;
					int colIdx = 0;
					for (String h : headerMap.keySet()) {
						int colWidth = headerMap.get(h);

						sheet1.setColumnView(colIdx, colWidth);
						sheet1.addCell(new Label(colIdx, rowIdx, h,
								fmt_content_str));
						// ---
						colIdx++;
					}
				}
				if (true) {
					rowIdx = 3;

					List<String[]> r = _LMS180R23_SummaryXls_content(brInfoMap,
							bgnDate, endDate, headerMap.size(), lrs_item_cnt,
							crs_item_cnt, ctlType);
					for (String[] arr : r) {
						int colLen = arr.length;
						for (int i_col = 0; i_col < colLen; i_col++) {
							sheet1.addCell(new Label(
									i_col,
									rowIdx,
									arr[i_col],
									(i_col == 1 || i_col == 2) ? fmt_content_str
											: fmt_content_cnt));
						}
						// ---
						rowIdx++;
					}
				}

			}

			workbook.write();
			workbook.close();
		} finally {

		}
	}

	/**
	 * 參考 gfnCTLGenAssessBatch
	 */
	private List<String[]> _LMS180R23_SummaryXls_content(
			Map<String, String> brInfoMap, Date bgnDate, Date endDate,
			int col_cnt, int lrs_item_cnt, int crs_item_cnt, String ctlType) {
		List<String[]> r = new ArrayList<String[]>();

		int cntBr = 1;
		int lrs_xls_beg_seq = 3;
		int crs_xls_beg_seq = 33;
		for (String brNo : brInfoMap.keySet()) {
			String brName = brInfoMap.get(brNo);
			// ========
			String[] row = new String[col_cnt];
			row[0] = String.valueOf(cntBr);
			row[1] = Util.trim(brNo);
			row[2] = Util.trim(brName);
			for (int i = 3; i < col_cnt; i++) {
				row[i] = "0";
			}
			// ========

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			// 個金沒有自辦覆審
			if (Util.notEquals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
				if (true) {
					List<C241M01A> c241m01a_list = new ArrayList<C241M01A>();
					if (true) {
						List<List<C241M01A>> crs_list = _gen_crs_retrial_list(
								brNo, TWNDate.toAD(bgnDate),
								TWNDate.toAD(endDate));
						for (List<C241M01A> paperList : crs_list) {
							if (CollectionUtils.isNotEmpty(paperList)) {
								c241m01a_list.addAll(paperList);
							}
						}
					}
					TreeMap<String, List<String>> crs_map = retrialService
							.getCrsRetrialSummary(c241m01a_list);
					for (String keyStr : crs_map.keySet()) {
						List<String> detailList = crs_map.get(keyStr);
						if (CollectionUtils.isEmpty(detailList)) {
							continue;
						}

						_set_item_seq(row, keyStr, detailList.size(),
								crs_xls_beg_seq, crs_item_cnt, "crs");
					}
				}
			}

			// ========
			if (true) {
				List<L170M01A> l170m01a_list = new ArrayList<L170M01A>();
				if (true) {
					List<List<L170M01A>> lrs_list = _gen_lrs_retrial_list(brNo,
							TWNDate.toAD(bgnDate), TWNDate.toAD(endDate),
							ctlType);
					for (List<L170M01A> paperList : lrs_list) {
						if (CollectionUtils.isNotEmpty(paperList)) {
							l170m01a_list.addAll(paperList);
						}
					}
				}

				TreeMap<String, List<String>> lrs_map = null;
				if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
					lrs_map = retrialService
							.getLrsRetrialSummary_ctlTypeB(l170m01a_list);
				} else {
					lrs_map = retrialService.getLrsRetrialSummary(
							l170m01a_list, true);
				}

				// TreeMap<String, List<String>> lrs_map = retrialService
				// .getLrsRetrialSummary(l170m01a_list);

				for (String keyStr : lrs_map.keySet()) {
					List<String> detailList = lrs_map.get(keyStr);
					if (CollectionUtils.isEmpty(detailList)) {
						continue;
					}

					_set_item_seq(row, keyStr, detailList.size(),
							lrs_xls_beg_seq, lrs_item_cnt, "lrs");
				}
			}
			// ---
			cntBr++;
			r.add(row);
		}
		return r;
	}

	private void _set_item_seq(String[] row, String keyStr, int val,
			int xls_beg_seq, int item_cnt, String sys) {
		int raw_seq = Util.parseInt(StringUtils.substring(keyStr, 0,
				CrsUtil.SUMMARY_PREFIX_LEN));
		if (Util.equals(sys, "lrs")) {
			if (raw_seq == 12 || raw_seq == 18) {// keyStr = 012N031..... //if
													// (raw_seq == 0) {
				String itemNo = keyStr.substring(CrsUtil.SUMMARY_PREFIX_LEN,
						CrsUtil.SUMMARY_PREFIX_LEN + 4);
				if (Util.equals(itemNo, LrsUtil.N031)) { // 12-1
					raw_seq = 13;
				} else if (Util.equals(itemNo, LrsUtil.N029)) { // 18-1
					raw_seq = 20;
				} else if (raw_seq == 18) { // 因為前面有加12-1項
					raw_seq += 1;
				}
			} else if (raw_seq > 13 && raw_seq < 19) {
				raw_seq += 1;
			} else if (raw_seq >= 19) {
				raw_seq += 2;
			}
		}
		int parse_seq = xls_beg_seq - 1 + raw_seq;

		int xls_end_seq = (xls_beg_seq - 1 + item_cnt);
		if (parse_seq >= xls_beg_seq && parse_seq <= xls_end_seq) {
			int r = Util.parseInt(row[parse_seq]) + val;
			row[parse_seq] = String.valueOf(r);
		} else {
			logger.error("異常[" + keyStr + " / " + val + "]" + xls_beg_seq + ","
					+ item_cnt + "," + parse_seq + "|" + xls_end_seq);
		}
	}

	/**
	 * 企金信用評等(L120S01C)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120s01a
	 *            L120S01A的資料
	 * @param l120s01cList
	 *            LIST<L120S01C>的資料
	 * @param crdTypeMap
	 *            bcodetype的crdType
	 * @return Map<String,String> rptVariableMap
	 */
	@SuppressWarnings("unused")
	public String setL120S01CData(L120S01A l120s01a,
			List<L120S01C> l120s01cList, Map<String, String> crdTypeMap,
			Properties prop, L140M01A l140m01a) {
		StringBuffer str1 = new StringBuffer();
		StringBuffer str2 = new StringBuffer();
		StringBuffer str3 = new StringBuffer();
		StringBuffer str4 = new StringBuffer();
		StringBuffer str5 = new StringBuffer();// 個人信用評等 J-105-0156-001 Web
												// e-Loan企金額度明細表增加得引入消金個人信用評等

		// 免辦
		boolean noResult = false;
		boolean naResult = false;
		StringBuffer tempGrade = new StringBuffer();
		for (L120S01C l120s01c : l120s01cList) {
			if (Util.nullToSpace(l120s01a.getCustId()).equals(
					Util.nullToSpace(l120s01c.getCustId()))
					&& Util.nullToSpace(l120s01a.getDupNo()).equals(
							Util.nullToSpace(l120s01c.getDupNo()))) {
				String crdType = Util.trim(l120s01c.getCrdType());
				String grade = Util.trim(l120s01c.getGrade());
				tempGrade.setLength(0);
				if ("NA".equals(crdType)) {
					naResult = true;
					// str.append(prop.getProperty("L120S01C.CRDTITLE01"))
					// .append(prop.getProperty("L120S05A.GRPGRRDN"))
					// .append("、");
				} else if ("DB".equals(crdType) || "DL".equals(crdType)
						|| "OU".equals(crdType) || "OB".equals(crdType)
						|| "A0".equals(crdType) || "A1".equals(crdType)
						|| "A2".equals(crdType)) {
					if (str3.length() != 0) {
						str3.append("、");
					}

					if ("NA".equals(grade)) {

					} else {

						if ("A0".equals(crdType) || "A1".equals(crdType)
								|| "A2".equals(crdType)) {

							if (Util.isNumeric(grade)) {
								tempGrade.append(grade)
										.append(prop.getProperty("tempGrade"))
										.append(" ");
							}

							// 取得MOW等級之說明
							tempGrade.append(lmsService.getMowGradeName(prop,
									crdType, grade));

							str3.append(
									Util.nullToSpace(crdTypeMap.get(crdType)))
									.append(" : ")
									.append(tempGrade.toString())
									.append("【")
									.append(prop
											.getProperty("L120S01C.CRDTITLE02"))
									.append(Util.nullToSpace(TWNDate
											.toAD(l120s01c.getCrdTYear())))
									.append(" ")
									.append(prop
											.getProperty("L120S01C.CRDTITLE03"))
									.append(" ")
									.append(l120s01c.getCrdTBR())
									.append(" ")
									.append(Util.nullToSpace(branch
											.getBranchName(Util
													.nullToSpace(l120s01c
															.getCrdTBR()))))
									.append("】");

						} else {
							str3.append(prop.getProperty("L120S01C.CRDTITLE01"))
									.append(grade)
									.append("【")
									.append(prop
											.getProperty("L120S01C.CRDTITLE02"))
									.append(Util.nullToSpace(TWNDate
											.toAD(l120s01c.getCrdTYear())))
									.append(" ")
									.append(prop
											.getProperty("L120S01C.CRDTITLE03"))
									.append(" ")
									.append(l120s01c.getCrdTBR())
									.append(" ")
									.append(Util.nullToSpace(branch
											.getBranchName(Util
													.nullToSpace(l120s01c
															.getCrdTBR()))))
									.append("】");
						}

					}

				} else if ("NO".equals(crdType)) {
					noResult = true;
					// str.append(prop.getProperty("L120S01C.CRDTITLE04"))
					// .append(prop.getProperty("L120S01C.NOCRD01"))
					// .append("、");
				} else if ("M".equals(Util.getLeftStr(crdType, 1))) {
					if (Util.isNumeric(grade)) {
						tempGrade.append(grade)
								.append(prop.getProperty("tempGrade"))
								.append(" ");
					}

					// 取得MOW等級之說明
					tempGrade.append(lmsService.getMowGradeName(prop, crdType,
							grade));

					if (str2.length() != 0) {
						str2.append("、");
					}
					str2.append(Util.nullToSpace(crdTypeMap.get(crdType)))
							.append(" : ")
							.append(tempGrade.toString())
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(Util.nullToSpace(branch.getBranchName(Util
									.nullToSpace(l120s01c.getCrdTBR()))))
							.append("】");
				} else if (Casedoc.CrdType.MOODY.equals(crdType)
						|| Casedoc.CrdType.SAndP.equals(crdType)
						|| Casedoc.CrdType.Fitch.equals(crdType)
						|| Casedoc.CrdType.中華信評.equals(crdType)
						|| Casedoc.CrdType.FitchTW.equals(crdType)
						|| Casedoc.CrdType.KBRA.equals(crdType)) {
					// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
					if (str1.length() != 0) {
						str1.append("、");
					}
					str1.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(Util.nullToSpace(crdTypeMap.get(l120s01c
									.getCrdType()))).append("】");
				} else if (crdType.startsWith("C")
						&& Util.notEquals(crdType, "CS")) {
					if (str4.length() != 0) {
						str4.append("、");
					}
					str4.append(Util.nullToSpace(crdTypeMap.get(crdType)))
							.append(" : ")
							.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(Util.nullToSpace(branch.getBranchName(Util
									.nullToSpace(l120s01c.getCrdTBR()))))
							.append("】");
				}
			}
		}

		// J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等
		String buscd = Util.trim(l120s01a.getBusCode());
		if (Util.equals(buscd, "130300") || Util.equals(buscd, "060000")) {
			// 個人戶
			str5.append(service1401.buildL140S03AStr(l140m01a.getMainId()));
		}

		/*
		 * 狀況1:MX+NA 狀況2:DX+NO 狀況3:NA+NO 狀況4:空 最後在加外部NM,NS,NP
		 */
		// 外部評等一定要串
		boolean result = false;
		// rptVariableMap.put("L120S01C.CRD", "");
		StringBuffer total = new StringBuffer();
		// L120S01C.CRDTITLE04=模型評等 :
		if (str2.length() > 0) {

			// MXXX+外部
			// rptVariableMap.put("L120S01C.CRD",str2.toString());
			total.append(prop.getProperty("L120S01C.CRDTITLE04") + " " + str2);
			result = true;
		}
		// L120S01C.CRDTITLE01=信用評等 :
		if (str3.length() > 0) {
			// DXXX+外部
			total.append(total.length() > 0 ? "\r" : "");
			total.append(str3.toString());
			// rptVariableMap.put("L120S01C.CRD",str3.toString() + " " +
			// prop.getProperty("L120S01C.CRDTITLE04"));
			result = true;
		}

		// L120S01C.CRDTITLE05=外部評等 :
		if (str1.length() > 0) {
			total.append(total.length() > 0 ? "\r" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE05")
					+ str1.toString());
		}

		// J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等
		// L120S01C.CRDTITLE07=個金評等 :
		if (str5.length() > 0) {
			total.append(total.length() > 0 ? "\r" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE07")
					+ str5.toString());
		}

		if (total.length() == 0) {
			// rptVariableMap.put("L120S01C.CRD",prop.getProperty("L120S01C.NOCRD01"));
			total.append(prop.getProperty("L120S01C.NOCRD01"));
			result = true;
		}

		// rptVariableMap.put("L120S01C.CRD",(!result ? "" :
		// (rptVariableMap.get("L120S01C.CRD") + "\n"))+crdtitle05 +
		// str1.toString());
		// rptVariableMap.put("L120S01C.CRD", total.toString());
		return total.toString();
	}
	
	// LMS9511R01XLSServiceImpl tranSportLMS180R50Excel
	@Override
	public String tranSportLMS180R50Pdf(String mainId) throws Exception {
		Locale locale = LMSUtil.getLocale();
		ReportGenerator generator = new ReportGenerator(
				"report/rpt/LMS9511R50_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		Map<String, String> crdTypeMap = codetypeservice.findByCodeType(
				"CRDType", LMSUtil.getLocale().toString());
		Map<String, String> projTW_useCodeMap = codetypeservice.findByCodeType(
				"projTW_use", LMSUtil.getLocale().toString());
		LMSRPT lmsRpt = lmsrptDao.findByIndex03(mainId);
		String bnDate = TWNDate.toAD(lmsRpt.getBgnDate());
		String endDate = TWNDate.toAD(lmsRpt.getEndDate());
		rptVariableMap.put("dataDate", bnDate + " ~ " + endDate);

		Boolean head = false; // 控制顯示頁籤種類 true:1.2 false:1.3
		// "940","918","931","932", "933", "934", "935"
		String[] headArr = new String[] { UtilConstants.BankNo.授信行銷處,
				UtilConstants.BankNo.授管處, UtilConstants.BankNo.北一區營運中心,
				UtilConstants.BankNo.北二區營運中心, UtilConstants.BankNo.桃竹苗區營運中心,
				UtilConstants.BankNo.中區營運中心, UtilConstants.BankNo.南區營運中心 };
		if (ArrayUtils.contains(headArr, Util.trim(lmsRpt.getBranch()))) {
			head = true;
		}
		rptVariableMap.put("head", head.toString());
		String brType = "";
		if (Util.equals(UtilConstants.BankNo.授信行銷處,
				Util.trim(lmsRpt.getBranch()))
				|| Util.equals(UtilConstants.BankNo.授管處,
						Util.trim(lmsRpt.getBranch()))) {
			brType = "1";
		} else if (Util.equals(UtilConstants.BankNo.北一區營運中心,
				Util.trim(lmsRpt.getBranch()))
				|| Util.equals(UtilConstants.BankNo.北二區營運中心,
						Util.trim(lmsRpt.getBranch()))
				|| Util.equals(UtilConstants.BankNo.桃竹苗區營運中心,
						Util.trim(lmsRpt.getBranch()))
				|| Util.equals(UtilConstants.BankNo.中區營運中心,
						Util.trim(lmsRpt.getBranch()))
				|| Util.equals(UtilConstants.BankNo.南區營運中心,
						Util.trim(lmsRpt.getBranch()))) {
			brType = "2";
		} else {
			brType = "3";
		}

		// + (總處) "明細" or (分行) "案例"
		rptVariableMap.put("s2", (head ? "明細" : "案例"));

		List<Map<String, String>> rowData_1 = new ArrayList<Map<String, String>>(); // 台商
																					// head
		List<Map<String, String>> rowData_2 = new ArrayList<Map<String, String>>(); // 台商
																					// NotHead
		List<Map<String, String>> rowData_3 = new ArrayList<Map<String, String>>(); // 中小企業
																					// head
		List<Map<String, String>> rowData_4 = new ArrayList<Map<String, String>>(); // 中小企業
																					// NotHead
		List<Map<String, String>> rowData_5 = new ArrayList<Map<String, String>>(); // 根留台灣企業
																					// head
		List<Map<String, String>> rowData_6 = new ArrayList<Map<String, String>>(); // 根留台灣企業
																					// NotHead
		for (int i = 1; i <= 3; i++) {
			String projClass = "";
			if (i == 1) {
				projClass = UtilConstants.projClass.歡迎台商回台投資專案貸款;
			} else if (i == 2) {
				projClass = UtilConstants.projClass.中小企業加速投資貸款;
			} else if (i == 3) {
				projClass = UtilConstants.projClass.根留台灣企業加速投資專案貸款;
			}

			// =========================統計=========================
			Map<String, Object> map1 = eloandbBASEService.listLMS180R50_1(
					bnDate, endDate, projClass, brType,
					Util.trim(lmsRpt.getBranch()));

			rptVariableMap.put("MONTH_COUNT_" + i, Integer.toString(MapUtils
					.getIntValue(map1, "MONTH_COUNT", 0)));
			rptVariableMap.put("TTL_COUNT_" + i, Integer.toString(MapUtils
					.getIntValue(map1, "TTL_COUNT", 0)));
			BigDecimal MONTH_AMT = BigDecimal.ZERO;
			BigDecimal TTL_AMT = BigDecimal.ZERO;
			MONTH_AMT = BigDecimal.valueOf(MapUtils.getDoubleValue(map1,
					"MONTH_AMT", 0));
			TTL_AMT = BigDecimal.valueOf(MapUtils.getDoubleValue(map1,
					"TTL_AMT", 0));
			rptVariableMap.put("MONTH_AMT_" + i, NumConverter.addComma(
					(MONTH_AMT.divide(BigDecimal.valueOf(1000)))
							.toPlainString(), "#,###,###,###,##0"));
			rptVariableMap.put("TTL_AMT_" + i, NumConverter.addComma(
					(TTL_AMT.divide(BigDecimal.valueOf(1000))).toPlainString(),
					"#,###,###,###,##0"));
			// =========================統計=========================

			// =========================明細or案例=========================
			List<Map<String, Object>> map2 = eloandbBASEService
					.listLMS180R50_2(bnDate, endDate, projClass, brType,
							Util.trim(lmsRpt.getBranch()));
			List<Map<String, String>> rowData_temp = new ArrayList<Map<String, String>>();
			rowData_temp = this.getColumnDataLMS180R50(map2, head, crdTypeMap,
					projTW_useCodeMap);
			if (head) {
				if (i == 1) {
					rowData_1 = rowData_temp;
				} else if (i == 2) {
					rowData_3 = rowData_temp;
				} else if (i == 3) {
					rowData_5 = rowData_temp;
				}
			} else {
				if (i == 1) {
					rowData_2 = rowData_temp;
				} else if (i == 2) {
					rowData_4 = rowData_temp;
				} else if (i == 3) {
					rowData_6 = rowData_temp;
				}
			}
			// =========================明細or案例=========================
		}

		// 子報表設定
		SubReportParam subReportParam = new SubReportParam();
		Map<String, String> map = new HashMap<String, String>();
		subReportParam.setData(0, map, rowData_1);
		subReportParam.setData(1, map, rowData_2);
		subReportParam.setData(2, map, rowData_3);
		subReportParam.setData(3, map, rowData_4);
		subReportParam.setData(4, map, rowData_5);
		subReportParam.setData(5, map, rowData_6);
		generator.setSubReportParam(subReportParam);

		generator.setVariableData(rptVariableMap);
		generator.setLang(locale);
		outputStream = generator.generateReport();

		FileOutputStream fileOutputStream = null;
		OutputStream outputStreamToFile = null;
		DocFile docFile = lms9511Service.saveDocFile(lmsRpt.getBranch(),
				lmsRpt.getMainId(),
				UtilConstants.RPTREPORT.DOCTYPE1.投資台灣三大方案專案貸款執行情形統計表, "pdf");
		String filename = LMSUtil.getUploadFilePath(lmsRpt.getBranch(),
				lmsRpt.getMainId(),
				UtilConstants.RPTREPORT.DOCTYPE1.投資台灣三大方案專案貸款執行情形統計表);
		File file2 = new File(filename + "/" + docFile.getOid() + ".pdf");
		if (file2 != null) {
			fileOutputStream = new FileOutputStream(file2);
		}
		if (fileOutputStream != null) {
			outputStreamToFile = new DataOutputStream(fileOutputStream);
			if (outputStreamToFile != null) {
				outputStreamToFile.write(((ByteArrayOutputStream) outputStream)
						.toByteArray());
				outputStreamToFile.flush();
				outputStreamToFile.close();
			}
		}

		return docFile.getOid();
	}

	// LMS9511R01XLSServiceImpl setColumnDataLMS180R50_2
	private List<Map<String, String>> getColumnDataLMS180R50(
			List<Map<String, Object>> ListMap, Boolean head,
			Map<String, String> crdTypeMap,
			Map<String, String> projTW_useCodeMap) {
		List<Map<String, String>> temp = new ArrayList<Map<String, String>>();
		Properties prop1201Rpt = MessageBundleScriptCreator
				.getComponentResource(LMS1201R01RptServiceImpl.class);
		for (Map<String, Object> map : ListMap) {
			Map<String, String> tempMap = new LinkedHashMap<String, String>();
			String L12_MAINID = Util.trim(map.get("L12_MAINID"));
			String L12_CUSTID = Util.trim(map.get("L12_CUSTID"));
			String L12_DUPNO = Util.trim(map.get("L12_DUPNO"));
			String L14_MAINID = Util.trim(map.get("L14_MAINID"));

			L120S01A l120s01a = service1201.findL120s01aByUniqueKey(L12_MAINID,
					L12_CUSTID, L12_DUPNO);
			L140M01A l140m01a = service1401.findL140m01aByMainId(L14_MAINID);
			List<L120S01C> l120s01cList = service1201.findL120s01cByCustId(
					L12_MAINID, L12_CUSTID, L12_DUPNO);
			// 取得信評
			String mowStr = ""; // 評等模型
			if (l140m01a != null) {
				if (l120s01a != null) {
					mowStr = this.setL120S01CData(l120s01a, l120s01cList,
							crdTypeMap, prop1201Rpt, l140m01a);
				}
			} else {
				mowStr = "";
			}

			String ENDDATE = Util.trim(map.get("ENDDATE")); // 核准日期
			String CASEBRID = Util.trim(map.get("CASEBRID")); // 分行別
			String BRNAME = Util.trim(branchService.getBranch(CASEBRID)
					.getBrName()); // 分行名稱
			String CUSTID = Util.trim(map.get("CUSTID")); // 統一編號
			String CUSTNAME = Util.trim(map.get("CUSTNAME")); // 戶名

			String CASENO = Util.trim(map.get("CASENO")); // 簽報書案號
			String CNTRNO = Util.trim(map.get("CNTRNO")); // 額度序號
			String CURR = Util.trim(map.get("CURRENTAPPLYCURR")); // 現請額度幣別
			BigDecimal AMT = Util.equals(Util.trim(map.get("CURRENTAPPLYAMT")),
					"") ? BigDecimal.ZERO : new BigDecimal(Util.trim(map
					.get("CURRENTAPPLYAMT"))); // 現請額度
			String PROJTW_USE = Util.trim(map.get("PROJTW_USE")); // 貸款用途
			PROJTW_USE = (Util.isInteger(PROJTW_USE) ? Util
					.nullToSpace(projTW_useCodeMap.get(PROJTW_USE))
					: PROJTW_USE);
			String LNSUBJECT = Util.trim(map.get("LNSUBJECT")); // 科目

			// 動用期限
			String USEDEADLINE = Util.trim(map.get("USEDEADLINE"));
			String DESP1 = Util.trim(map.get("DESP1"));
			String USEperiod = LMSUtil.getUseDeadline(Util
					.nullToSpace(USEDEADLINE), Util.nullToSpace(DESP1),
					MessageBundleScriptCreator
							.getComponentResource(LMS1405S02Panel.class));

			String GUARANTOR = Util.trim(map.get("GUARANTOR")); // 保證人
			String L14B3 = Util.trim(map.get("L14B3")); // 擔保品
			if (!head) {
				GUARANTOR = (Util.isNotEmpty(GUARANTOR) ? "有" : "無");
				L14B3 = (Util.isNotEmpty(L14B3) ? "有" : "無");
			}
			String L14B2 = Util.trim(map.get("L14B2")); // 利率
			String L14B4 = Util.trim(map.get("L14B4")); // 其他敘做條件

			tempMap.put("ReportBean.column01", ENDDATE); // 核准日期
			if (head) {
				// head
				tempMap.put("ReportBean.column02", CASEBRID); // 分行別
				tempMap.put("ReportBean.column03", BRNAME); // 分行名稱
				tempMap.put("ReportBean.column04", CUSTID); // 統一編號
				tempMap.put("ReportBean.column05", CUSTNAME); // 戶名
				// ===head

				tempMap.put("ReportBean.column06", mowStr); // 評等模型

				// head
				tempMap.put("ReportBean.column07", CASENO); // 簽報書案號
				tempMap.put("ReportBean.column08", CNTRNO); // 額度序號
				// ===head

				tempMap.put("ReportBean.column09", CURR); // 現請額度幣別
				tempMap.put("ReportBean.column10", NumConverter.addComma(
						AMT.toPlainString(), "#,###,###,###,###,###,##0")); // 現請額度
				tempMap.put("ReportBean.column11", PROJTW_USE); // 貸款用途
				tempMap.put("ReportBean.column12", LNSUBJECT); // 科目
				tempMap.put("ReportBean.column13", USEperiod); // 動用期限
				tempMap.put("ReportBean.column14", GUARANTOR); // 保證人
				tempMap.put("ReportBean.column15", L14B3); // 擔保品
				tempMap.put("ReportBean.column16", L14B2); // 利率
				tempMap.put("ReportBean.column17", L14B4); // 其他敘做條件
			} else {
				tempMap.put("ReportBean.column02", mowStr); // 評等模型
				tempMap.put("ReportBean.column03", CURR); // 現請額度幣別
				tempMap.put("ReportBean.column04", NumConverter.addComma(
						AMT.toPlainString(), "#,###,###,###,###,###,##0")); // 現請額度
				tempMap.put("ReportBean.column05", PROJTW_USE); // 貸款用途
				tempMap.put("ReportBean.column06", LNSUBJECT); // 科目
				tempMap.put("ReportBean.column07", USEperiod); // 動用期限
				tempMap.put("ReportBean.column08", GUARANTOR); // 保證人
				tempMap.put("ReportBean.column09", L14B3); // 擔保品
				tempMap.put("ReportBean.column10", L14B2); // 利率
				tempMap.put("ReportBean.column11", L14B4); // 其他敘做條件
			}

			temp.add(tempMap);
		}
		return temp;
	}
}