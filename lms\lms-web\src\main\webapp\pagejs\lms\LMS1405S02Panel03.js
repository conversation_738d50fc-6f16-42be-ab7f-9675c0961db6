var dfd31 = new $.Deferred(), dfd32 = new $.Deferred(), dfd33 = new $.Deferred(), dfd7 = new $.Deferred();

initAll.done(function(inits){
    dfd31.done(gridviewitemChildren);//科子目限額
    dfd32.done(gridviewitemChildren2);
    dfd33.done(gridviewitemChildren3);
    dfd7.done(queryItemSelect);//查詢科子目項目
    //====================button event ================================
    //新增科子目
    $("#newItemChildren1Bt").click(function(){
        newItemChildren1();
    });
    
    //新增科子目合併限額
    $("#newItemChildren2Bt").click(function(){
        newItemChildren2();
    });
    
    //新增攤待比率
    $("#newItemChildren3Bt").click(function(){
        //新增前先儲存額度序號與現請額度
        var currAmt = util.delComma($("#currentApplyAmt").val());
        if ($.trim(currAmt) == "" || !isNumber(currAmt)) {
            // L140M01a.message71=請先登錄現請額度與額度序號並儲存後，在執行此動作。
            return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.message71"]);
        }
        BranchAciton.query();
    });
    
    //變更分母
    $("#changesShareRate2").click(function(){
        BranchAciton.changesShareRate();
    });
    
    // 新號，舊號的隱藏條件
    $("input[name=numberType]").change(function(){
        var $cntrNoForm = $("#cntrNoBoxforItem3Form");
        var type = $cntrNoForm.find("#cntrNoType").val(type);
        $cntrNoForm.find(".ForOriginal,.ForInSide").hide();
        //海外不行選輸入分行 和 是否遠匯 
        var value = $(this).val();
        switch (value) {
            case "1":
                if (type != "5") {
                    $cntrNoForm.find(".ForInSide").show();
                }
                $("#showBrNoTr").show();
                break;
            case "2":
                $cntrNoForm.find(".ForOriginal").show();
                break;
        }
    });
    
    
    
    //變更攤貸行計算方式
    $("[name=shareFlag]").click(function(){
    
        BranchAciton.controlShow();
    });
    
    //變更科子目限額選擇的顏色
    $("[name=subject2]").on("click", function(){
        var $this = $(this);
        if ($this.prop("checked")) {
            $this.closest("td").css("background", "#C0C0C0");
        } else {
            $this.closest("td").css("background", "#FFFFFF");
        }
    });
    
    //刪除科子目
    $("#removeGridviewitemChildren").click(function(){
        var gridID = $("#gridviewitemChildren").getGridParam('selarrrow');
        if (gridID == "") {
            //TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var gridIDList = [];
                for (var i = 0; i < gridID.length; i++) {
                    gridIDList[i] = $("#gridviewitemChildren").getRowData(gridID[i]).oid;
                }
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteL140m01d",
                        tabFormMainId: $("#tabFormMainId").val(),
                        Idlist: gridIDList
                    },
                    success: function(obj){
                    
                        if (obj && obj.drc) {
                            $("#itemDscr1").val(obj.drc);
                        }
                        $("#gridviewitemChildren").trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
    //刪除科子目合併
    $("#removeGridviewitemChildren2").click(function(){
    
        var gridID = $("#gridviewitemChildren2").getGridParam('selarrrow');
        if (gridID == "") {
            //TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var gridIDList = [];
                for (var i = 0; i < gridID.length; i++) {
                    gridIDList[i] = $("#gridviewitemChildren2").getRowData(gridID[i]).oid;
                }
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteL140m01d",
                        tabFormMainId: $("#tabFormMainId").val(),
                        Idlist: gridIDList
                    },
                    success: function(obj){
                        if (obj && obj.drc) {
                            $("#itemDscr1").val(obj.drc);
                        }
                        $("#gridviewitemChildren2").trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
    //刪除攤待比率
    $("#removeGridviewitemChildren3").click(function(){
        BranchAciton.remove();
    });
    
    $("#lms140Tab03").click(function(){
        dfd31.resolve();
        dfd7.resolve();
        
        $("#gridviewitemChildren").jqGrid("setGridParam", {//當文件載完再將科子目的grid更新
            sortname: 'createTime',
            sortorder: 'asc',
            postData: {
                formAction: "queryL140m01d",
                tabFormMainId: $("#tabFormMainId").val(),
                lmtType: "1"
            },
            search: true
        }).trigger("reloadGrid");
        
    });
    $("#tab03_2,#tab03_3,#tab03_4").find("a").click(function(){
        var $thisId = $(this).parent("li").attr("id");
        switch ($thisId) {
            //科子目頁籤
            case "tab03_2":
                dfd32.resolve();
                $("#gridviewitemChildren2").jqGrid("setGridParam", {//當文件載完再將科子目的grid更新
                    sortname: 'createTime',
                    sortorder: 'asc',
                    postData: {
                        formAction: "queryL140m01d",
                        tabFormMainId: $("#tabFormMainId").val(),
                        lmtType: "2"
                    },
                    search: true
                }).trigger("reloadGrid");
                break;
            case "tab03_3":
                dfd33.resolve();
                $(BranchAciton.gridId).jqGrid("setGridParam", {//當文件載完再將科子目的grid更新
                    sortname: 'createTime',
                    sortorder: 'asc',
                    postData: {
                        formAction: "queryL140m01e",
                        tabFormMainId: $("#tabFormMainId").val()
                    },
                    search: true
                }).trigger("reloadGrid");
                break;
            case "tab03_4":
                break;
        }
    });
    
    
    $("#limitWordAll").click(function(){
        //組成字串從sql 裡面撈
        $("#itemDscr1").html("");
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "saveDscr1",
                tabFormMainId: $("#tabFormMainId").val(),
                type: "1",
                pageNum: $("#pageNum1").val()
            },
            success: function(obj){
                $("#itemDscr1").val(obj.drc);
            }//close success
        });//close ajax
    });
    
    //驗證是不是數字   
    function isNumber(val){
        return /\d/.test(val);
    }
    
    $("#shareMoneyCount1").click(function(){//計算攤貸金額
        BranchAciton.countByAMT();
    });
    
    
    $("#shareMoneyCount2").click(function(){//計算攤貸比率
        var shareAmt = $("#shareAmt").val().replace(/,/g, "");
        var totel = $("#totalAmt").val().replace(/,/g, "");
        var shareRate2 = $("#shareRate2").val().replace(/,/g, "");
        var gcd = getGCD(shareAmt, totel);
        var countGrid = $(BranchAciton.gridId).jqGrid('getGridParam', 'records');
        if (shareRate2 == "" || countGrid == 0) {
            $("#shareRate1").val(Math.round(shareAmt / gcd));
            $("#shareRate2").val(Math.round(totel / gcd));
        } else {
            $("#shareRate1").val((shareAmt * shareRate2) / totel);
        }
        
    });
    /**
     * 取得最大公因數
     * @param {Object} m 數字
     * @param {Object} n 數字
     */
    function getGCD(m, n){
        var num = 1;
        while (num > 0) {
            num = m % n;
            m = n;
            n = num;
        }
        return m;
    }
    
    //====================button event End================================
    
    //====================Grid Code ======================================
    
    /**  科子目限額grid  */
    function gridviewitemChildren(){
        $("#gridviewitemChildren").iGrid({
            handler: inits.ghandle,
            height: 170,
            rownumbers: true,
            multiselect: true,
            hideMultiselect: false,
            sortname: 'createTime',
            sortorder: 'asc',
            rowNum: 10,
            postData: {
                formAction: "queryL140m01d",
                tabFormMainId: $("#tabFormMainId").val(),
                lmtType: "1"
            },
            autowidth: true,
            colModel: [{
                colHeader: i18n.lms1405s02["L782M01A.loanTP"],//科目
                name: 'subject',
                align: "left",
                width: 350,
                sortable: true,
                type: "1",
                formatter: 'click',
                onclick: newItemChildren1
            }, {
                colHeader: i18n.lms1405s02["L782M01A.applyCurr"],//"幣別",
                name: 'lmtCurr',
                width: 40,
                align: "center",
                sortable: true
            }, {
                colHeader: i18n.lms1405s02["L140M01d.lmtMoney"],//"限額",
                width: 100,
                name: 'lmtAmt',
                align: "right",
                sortable: true,
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
					removeTrailingZero: true,
                    decimalPlaces: 2//小數點到第幾位
                }
            }, {
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewitemChildren").getRowData(rowid);
                newItemChildren1(null, null, data);
            }
        });
    }// close gridviewitemChildren fn(x)
    /**  科子目合併限額grid  */
    function gridviewitemChildren2(){
        $("#gridviewitemChildren2").iGrid({
            handler: inits.ghandle,
            height: 170,
            rowNum: 10,
            rownumbers: true,
            multiselect: true,
            hideMultiselect: false,
            sortname: 'createTime',
            sortorder: 'asc',
            postData: {
                formAction: "queryL140m01d",
                tabFormMainId: $("#tabFormMainId").val(),
                lmtType: "2"
            },
            autowidth: true,
            colModel: [{
                colHeader: i18n.lms1405s02["L782M01A.loanTP"],//"科目"
                name: 'subject',
                align: "left",
                width: 350,
                sortable: true,
                type: "2",
                //在LMS1405s02panel02.js的格式化function
                formatter: "click",
                onclick: newItemChildren2
            
            }, {
                colHeader: i18n.lms1405s02["L782M01A.applyCurr"],//幣別,
                name: 'lmtCurr',
                align: "center",
                width: 40,
                sortable: true
            }, {
                colHeader: i18n.lms1405s02["L140M01d.lmtMoney"],//"限額",
                width: 100,
                name: 'lmtAmt',
                align: "right",
                sortable: true,
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
					removeTrailingZero: true,
                    decimalPlaces: 2//小數點到第幾位
                }
            }, {
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewitemChildren2").getRowData(rowid);
                newItemChildren2(null, null, data);
            }
        });
    }//close gridviewitemChildren2
    /** 聯行攤貸比例grid  */
    function gridviewitemChildren3(){
        $(BranchAciton.gridId).iGrid({//
            handler: "lms1405gridhandler",
            height: 170,
            rowNum: 10,
            rownumbers: true,
            multiselect: true,
            hideMultiselect: false,
            sortname: 'createTime',
            sortorder: 'asc',
            postData: {
                formAction: "queryL140m01e",
                tabFormMainId: $("#tabFormMainId").val()
            },
            autowidth: true,
            colModel: [{
                name: 'shareRate2',
                hidden: true
            }, {
                colHeader: i18n.lms1405s02["L140M01e.shareBrId"],//"攤貸分行",
                name: 'shareBrId',
                align: "left",
                width: 110,
                sortable: true,
                formatter: 'click',
                onclick: BranchAciton.query
            }, {
                colHeader: i18n.lms1405s02["L140M01e.shareAmt"],//"攤貸金額",
                name: 'shareAmt',
                width: 160,
                sortable: true,
                align: "right",
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
					removeTrailingZero: true,
                    decimalPlaces: 2//小數點到第幾位
                }
            }, {
                colHeader: i18n.lms1405s02["L140M01e.shareRate1"],//"攤貸比例",
                width: 140,
                name: 'showRate',
                align: "right",
                sortable: true
            }, {
                colHeader: i18n.lms1405s02["L140M01a.cntrNo"],//"額度序號",
                width: 140,
                name: 'shareNo',
                sortable: true
            }, {
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $(BranchAciton.gridId).getRowData(rowid);
                BranchAciton.query(null, null, data);
            }
        });
    }//close gridviewitemChildren3 fn(x)
    //====================Grid Code End======================================
    
    //====================thickbox Code======================================
    function openNewItemChildrenBox1(){
        $("#newItemChildrenBox1").thickbox({
            // 科子目
            //L140S02Tab.3_01=科子目限額
            title: i18n.lms1405s02["L140S02Tab.3_01"],
            width: 550,
            height: 200,
            readOnly: _openerLockDoc == "1" || inits.toreadOnly,
            i18n: i18n.def,
            modal: true,
            open: function(){
                //鎖定box
                $("#L140M01DForm1").readOnlyChilds(_openerLockDoc == "1" || inits.toreadOnly);
            },
            buttons: {
                "saveData": function(){
                    if (!$("#L140M01DForm1").valid()) {
                        return false;
                    }
                    if ($("#subject1").val() == "") {
                        //L782M01A.loanTP = 科目
                        return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error07"] + i18n.lms1405s02["L782M01A.loanTP"]);
                    }
                    
                    if ($("#lmtCurr1").val() == "") {
                        //L782M01A.applyCurr=幣別
                        return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error07"] + i18n.lms1405s02["L782M01A.applyCurr"]);
                    }
                    
                    if (parseInt($("#lmtAmt1").val(), 10) == 0) {
                    
                        //L140M01a.error08=限額不得為
                        return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error08"] + "0");
                    }
                    
                    FormAction.open = true;
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "saveL140m01d",
                            lmtType: "1",
                            tabFormMainId: $("#tabFormMainId").val(),
                            lmtSeq: $("#lmtSeq1").val(),
                            itemList: $("#subject1").val()
                        
                        },
                        success: function(obj){
                            if (obj && obj.drc) {
                                $("#itemDscr1").val(obj.drc);
                            }
                            FormAction.open = false;
                            $.thickbox.close();
                            $("#gridviewitemChildren").trigger("reloadGrid");
                        }
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    function newItemChildren1(cellvalue, type, data){
        $("#L140M01DForm1").reset();
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "queryL140m01d",
                oid: data && data.oid,
                tabFormMainId: $("#tabFormMainId").val()
            },
            success: function(obj){
                if (!$.isEmptyObject(obj.item)) {
                    $("#subject1").setItems({
                        item: obj.item,
                        format: "{value} - {key}"
                    });
                }
                $("#L140M01DForm1").injectData(obj);
                $("#lmtAmt1").val(obj.lmtAmt);
                $("#lmtCurr1").val(obj.lmtCurr);
                $("#subject1").val(obj.subject);
                $("#lmtSeq1").val(obj.lmtSeq);
                openNewItemChildrenBox1();
            }
        });
        
        
    }
    function openNewItemChildrenBox2(){
        $("#newItemChildrenBox2").thickbox({ // 科子目合併
            title: i18n.lms1405s02["L140S02Tab.3_02"],
            width: 580,
            height: 400,
            readOnly: _openerLockDoc == "1" || inits.toreadOnly,
            i18n: i18n.def,
            modal: true,
            open: function(){
                //鎖定box
                $("#L140M01DForm2").readOnlyChilds(_openerLockDoc == "1" || inits.toreadOnly);
            },
            buttons: {
                "saveData": function(){
                    if (!$("#L140M01DForm2").valid()) {
                        return false;
                    }
                    
                    if ($("#lmtAmt2").val() == 0) {
                        $("#lmtAmt2").val("");
                        //L140M01a.error08=限額不得
                        return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error08"] + "0");
                    }
                    
                    if ($("#lmtCurr2").val() == "") {
                        //L782M01A.applyCurr=幣別
                        return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error07"] + i18n.lms1405s02["L782M01A.applyCurr"]);
                    }
                    var $item = $("[name=subject2]").filter(":checked");
                    if ($item.length == 0) {
                    
                        return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error07"] + i18n.lms1405s02["L140M01c.item"]);
                    }
                    var itemList = $item.map(function(){
                        return $(this).val()
                    }).toArray();
                    
                    
                    FormAction.open = true;
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "saveL140m01d",
                            lmtType: "2",
                            tabFormMainId: $("#tabFormMainId").val(),
                            lmtSeq: $("#lmtSeq2").val(),
                            subitem: $("#subject2").val(),
                            itemList: itemList.join("|")
                        
                        },
                        success: function(obj){
                            if (obj && obj.drc) {
                                $("#itemDscr1").val(obj.drc);
                            }
                            FormAction.open = false;
                            $.thickbox.close();
                            $("#gridviewitemChildren2").trigger("reloadGrid");
                        }
                    });
                    
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    
    function newItemChildren2(cellvalue, type, data){
        //初始化
        $("#L140M01DForm2").reset();
        $("[name=subject2]").closest("td").css("background", "#FFFFFF");
        if (!$.isEmptyObject(data)) {
            $.ajax({
                handler: inits.fhandle,
                data: {
                    formAction: "queryL140m01d",
                    oid: data.oid
                },
                success: function(obj){
                    $("#L140M01DForm2").injectData(obj);
                    var list = obj.subject.split("|");
                    for (var i = 0; i < list.length; i++) {//轉換科目代碼將值帶入
                        $("[name=subject2][value=" + list[i] + "]").prop("checked", true).trigger('click').prop("checked", true);
                    }//close for
                    $("#lmtAmt2").val(obj.lmtAmt);
                    $("#lmtCurr2").val(obj.lmtCurr);
                    $("#lmtSeq2").val(obj.lmtSeq);
                    
					$("[name=subject2]").filter(":checked").closest("td").css("background", "#C0C0C0");
					
                    openNewItemChildrenBox2();
                }
            });
        } else {
            openNewItemChildrenBox2();
        }
        
        
    }
    
    
    //登錄聯行攤貸比例
    var BranchAciton = {
        $form: $('#L140M01EForm'),
        gridId: "#gridviewitemChildren3",
        /**
         * 查詢
         * @param {Object} cellvalue 欄位顯示值
         * @param {Object} type  欄位選項
         * @param {Object} data  欄位資料
         */
        query: function(cellvalue, type, data){
            if (!data) {
                data = {
                    oid: ""
                };
            }
            util.init(BranchAciton.$form);
            BranchAciton.$form.reset();
            
            $.ajax({
                handler: inits.fhandle,
                data: {//把資料轉成json
                    formAction: "queryL140m01e",
                    tabFormMainId: $("#tabFormMainId").val(),
                    oid: data.oid
                },
                success: function(obj){
                    $("#shareBrId").setItems({
                        item: obj.item,
                        format: "{value} {key}",
                        space: false
                    });
                    BranchAciton.$form.injectData(obj.formData);
                    $("#totalAmt").val(util.addComma(DOMPurify.sanitize($("#currentApplyAmt").val())));
                    if (data.oid != "") {
                        $("#shareBrId").prop("disabled", true);
                    } else {
                        $("#shareBrId").prop("disabled", false);
                    }
                    $("#shareAmt,#shareRate1,#shareRate2").prop("readonly", true);
                    $("#shareMoneyCount1,#shareMoneyCount2").hide().parents(".fg-buttonset").hide();
                    if (obj.role != "0") {
                        $("#shareRate2").val(obj.role);
                        $("#shareRate2").prop("readonly", true);
                        $("[name=shareFlag]").prop("disabled", true);
						if (obj.shareFlag) {
							$("[name=shareFlag][value=" + obj.shareFlag + "]").prop("checked", true);
						}
                    } else {
                        $("[name=shareFlag]").removeAttr("disabled");
                    }//close if
                    BranchAciton.controlShow();
                    BranchAciton.openBox();
                }//close success
            }); //close ajax  
        },
        openBox: function(){
            $("#newItemChildrenBox3").thickbox({
                //title.15=登錄聯行攤貸比例
                title: i18n.lms1405s02["title.15"],
                width: 600,
                height: 300,
                modal: true,
                readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                i18n: i18n.def,
                open: function(){
                    if (_openerLockDoc == "1" || inits.toreadOnly) {
                        //鎖定box
                        BranchAciton.$form.readOnlyChilds(_openerLockDoc == "1" || inits.toreadOnly, "#totalAmt,#shareBrId");
                    }
                },
                buttons: {
                    "saveData": function(){
                        if ($("[name=shareFlag]:checked").val() == "1") {
                            BranchAciton.$form.find("#shareRate1,#shareRate2").removeClass("required");
                        } else {
                            BranchAciton.countByAMT();
                            BranchAciton.$form.find("#shareRate1,#shareRate2").addClass("required");
                            if ($("#shareRate2").val() == 0) {
                                //L140M01a.message81=分母不可為0
                                return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.message81"]);
                            }
                        }
                        
                        if (!BranchAciton.$form.valid()) {
                            return false;
                        }
                        var shareRate1 = parseInt($("#shareRate1").val(), 10), shareRate2 = parseInt($("#shareRate2").val(), 10);
                        if (shareRate1 > shareRate2) {
                            //L140M01e.lmterror=分子總和大於分母無法儲存
                            return CommonAPI.showMessage(i18n.lms1405s02["L140M01e.lmterror"]);
                        }
                        var shareBrId = $("#shareBrId").val();
                        $.ajax({
                            handler: inits.fhandle,
                            data: {
                                formAction: "queryShareBrIdType",//查詢目前分行為海外還是國內，若為海外第四碼為5，若為國內判斷
                                shareBrId: shareBrId,
                                tabFormMainId: $("#tabFormMainId").val()
                            },
                            success: function(obj){
                                //先檢查該間分行是否存在，若存在只做save的動作
                                if (obj.have) {
                                    //若該分行已存在只執行儲存
                                    BranchAciton.save("");
                                } else {
                                    //新增案件開起給號視窗
                                    BranchAciton.getCntrNo(obj.type, shareBrId);
                                }
                            }//close success
                        });
                    },
                    "close": function(){
                        $.thickbox.close();
                    }
                }
            });
        },
        /**儲存
         *
         * @param {Object} type 1.DBU,4.OBU,5.海外
         */
        save: function(type){
            $.ajax({
                handler: inits.fhandle,
                data: {
                    formAction: "saveL140m01e",
                    tabFormMainId: $("#tabFormMainId").val(),
                    L140M01EForm: JSON.stringify(BranchAciton.$form.serializeData()),
                    type: type
                },
                success: function(obj){
                    if (obj && obj.drc) {
                        $("#itemDscr1").val(obj.drc);
                    }
                    $.thickbox.close();
                    $(BranchAciton.gridId).trigger("reloadGrid");
                    
                }
            });
        },
        
        /**
         * 給號畫面
         * @param {String} type 5.海外
         * @param {String} brId 分行代號
         */
        getCntrNo: function(type, brId){
            var $cntrNoForm = $("#cntrNoBoxforItem3Form");
            $("#cntrNoBoxforItem3").thickbox({
                //btn.number=給號
                title: i18n.lms1405s02["btn.number"],
                width: 640,
                height: 320,
                modal: true,
                align: "center",
                readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                valign: "bottom",
                i18n: i18n.def,
                open: function(){
                    //初始化
                    $cntrNoForm.reset();
                    //帶入分行預設值
                    $cntrNoForm.find("#branchNoItem3").val(brId);
                    $cntrNoForm.find("#cntrNoType").val(type);
                    $cntrNoForm.find(".ForOriginal,.ForInSide").hide();
                },
                buttons: {
                    "sure": function(){
                        if (!$cntrNoForm.valid()) {
                            return false;
                        }
                        var numberType = $cntrNoForm.find("input[name=numberType]:checked").val();
                        var originalCntrNo = $cntrNoForm.find("#originalCntrNo").val();
                        brId = $cntrNoForm.find("#branchNoItem3").val();
                        //給新號
                        if (numberType == "1") {
                            if (type != "5") {
                                //國內的可以自己選分行和DBU or OBU
                                type = $cntrNoForm.find("input[name=typeCd]:checked").val();
                            }
                        } else {
                            //舊號
                            var cntrno = CntrNoAPI.queryOriginalCntrNo(originalCntrNo, "3");
                            if ($.isEmptyObject(cntrno) || !cntrno.cntrNo) {
                                return false;
                            }
                        }
                        $.ajax({
                            handler: inits.fhandle,
                            action: "saveL140m01e",
                            data: {
                                L140M01EForm: JSON.stringify(BranchAciton.$form.serializeData()),
                                tabFormMainId: $("#tabFormMainId").val(),
                                numberType: numberType,
                                originalCntrNo: originalCntrNo,
                                type: type,
                                classCD: "0",
                                selectBrNo: brId
                            },
                            success: function(obj){
                                if (obj && obj.drc) {
                                    $("#itemDscr1").val(obj.drc);
                                }
                                $.thickbox.close();
                                $.thickbox.close();
                                $(BranchAciton.gridId).trigger("reloadGrid");
                            }
                        });
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        },
        /**
         * 刪除
         */
        remove: function(){
            var gridData = $(BranchAciton.gridId);
            var gridID = gridData.getGridParam('selarrrow');
            if (gridID == "") {
                //TMMDeleteError=請先選擇需修改(刪除)之資料列
                return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            }
            //confirmDelete=是否確定刪除?
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    var gridIDList = [];
                    for (var i = 0; i < gridID.length; i++) {
                        gridIDList[i] = gridData.getRowData(gridID[i]).oid;
                    }
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "deleteL140m01e",
                            tabFormMainId: $("#tabFormMainId").val(),
                            Idlist: gridIDList
                        },
                        success: function(obj){
                            if (obj && obj.drc) {
                                $("#itemDscr1").val(obj.drc);
                            }
                            gridData.trigger("reloadGrid");
                        }
                    });
                }
            });
        },
        /**
         * 控制計算方式顯示
         */
        controlShow: function(){
            var value = $("[name=shareFlag]:checked").val();
            $("#shareAmt,#shareRate1,#shareRate2").prop("readonly", true);
            $("#shareMoneyCount1,#shareMoneyCount2").hide().parents(".fg-buttonset").hide();
            //顯示隱藏
            switch (value) {
                case "1":
                    // 1.依攤貸金額
                    $("#shareMoneyCount2").show().parents(".fg-buttonset").show();
                    $("#shareAmt").removeAttr("readonly");
                    $("#shareRate1,#shareRate2").val("");
                    break;
                case "2":
                    //2.依攤貸比例          
                    $("#shareMoneyCount1").show().parents(".fg-buttonset").show();
                    if (!$("[name=shareFlag]:checked").prop("disabled")) {
                        $("#shareAmt").val("");
                    }
                    $("#shareRate1").removeAttr("readonly");
                    var countGrid = $(BranchAciton.gridId).jqGrid('getGridParam', 'records');
                    if (countGrid == 0) {
                        $("#shareRate2").removeAttr("readonly");
                    }
                    break;
                default:
                    break;
            }
        },
        /**
         *以比例計算金額
         */
        countByAMT: function(){
            var totel = $("#totalAmt").val().replace(/,/g, ""), value1 = $("#shareRate1").val(), value2 = $("#shareRate2").val(), shareRate1 = parseInt(value1, 10), shareRate2 = parseInt(value2, 10), end = (totel * shareRate1) / shareRate2;
            if (!isNumber(end)) {
                end = "";
            } else {
                end = util.addComma(parseInt(end, 10));
            }
            $("#shareAmt").val(end);
        },
        /**
         *變更分母
         */
        changesShareRate: function(){
            $("#newSharteNew").val("");
            $.ajax({
                handler: inits.fhandle,
                data: {
                    formAction: "queryChangesShareRate",
                    tabFormMainId: $("#tabFormMainId").val()
                },
                success: function(obj){
                    $("#newSharteNewBox").thickbox({
                        title: i18n.lms1405s02["btn.changesShareRate2"],//變更分母
                        width: 200,
                        height: 100,
                        readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                        i18n: i18n.def,
                        modal: true,
                        align: "center",
                        valign: "bottom",
                        open: function(){
                        },
                        buttons: {
                            "sure": function(){
                                if ($("#newSharteNew").val() == 0) {
                                    //L140M01a.message81=分母不可為0
                                    return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.message81"]);
                                }
                                if (!$("#newSharteNewForm").valid()) {
                                    return false;
                                }
                                $.ajax({
                                    handler: inits.fhandle,
                                    data: {
                                        formAction: "saveChangesShareRate",
                                        tabFormMainId: $("#tabFormMainId").val(),
                                        shareRate: $("#newSharteNew").val()
                                    },
                                    success: function(obj){
                                        if (obj && obj.drc) {
                                            $("#itemDscr1").val(obj.drc);
                                        }
                                        $(BranchAciton.gridId).trigger("reloadGrid");
                                        $.thickbox.close();
                                    }
                                });
                            },
                            "cancel": function(){
                                $.thickbox.close();
                            }
                        }
                    });
                }
            });
        }
    };
    //====================thickbox Code End======================================	
    //查詢科子目項目
    function queryItemSelect(){
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "queryItemSelect"
            },
            success: function(obj){
				if (obj.stopUseSubject) {
				    obj.stopUseSubject = obj.stopUseSubject.split(",");
				}
                $("#subject2").setItems({
                    size: 1,
                    item: obj
                });
            }
        });
    }
});
