package com.mega.eloan.lms.cls.panels;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.service.CLSService;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金額度明細表 - 文件資訊
 * </pre>
 * 
 * @since 2012/12/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/7,REX,new
 *          </ul>
 */
public class CLS1151S01Panel extends Panel {

	private static final long serialVersionUID = 1L;

	private boolean show_change_L140M01A_ownBrId;

	private boolean showITWCODE;

	private boolean showCoreBussITWCODE;

	@Autowired
	CLSService clsService;
	
	/**
	 * @param id
	 */
	public CLS1151S01Panel(String id, boolean show_change_L140M01A_ownBrId, boolean showITWCODE, boolean showCoreBussITWCODE) {
		super(id);
		this.show_change_L140M01A_ownBrId = show_change_L140M01A_ownBrId;
		this.showITWCODE = showITWCODE;
		this.showCoreBussITWCODE = showCoreBussITWCODE;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		model.addAttribute("show_change_L140M01A_ownBrId_Y",
				show_change_L140M01A_ownBrId);
		
		model.addAttribute("showITWCODE_Y", showITWCODE);
		model.addAttribute("showITWCODE_N", !showITWCODE);
		model.addAttribute("showCoreBussITWCODE_Y", showCoreBussITWCODE);
		model.addAttribute("showCoreBussITWCODE_N", !showCoreBussITWCODE);
	
		if(true){
			List<String> tpc_321_list = new ArrayList<String>();
			Map<String, String> map = clsService.get_codeTypeWithOrder("grpCntrNo_TPC_321");
			for(String grpCntrNo : map.keySet()){
				tpc_321_list.add(Util.trim(grpCntrNo));
			}
			
			StringBuilder sb = new StringBuilder();
            sb.append("<input type='hidden' id='grpCntrNo_TPC_321_list' name='grpCntrNo_TPC_321_list' value='");
            sb.append(StringUtils.join(tpc_321_list, ","));
            sb.append("'  ");
            sb.append(">");
			model.addAttribute("grpCntrNo_TPC_321_list", sb.toString());
		}
		
	}
}
