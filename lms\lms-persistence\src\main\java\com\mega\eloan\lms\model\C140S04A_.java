package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

/**
 * <pre>
 * The persistent class for the C140S04A database table.
 * </pre>
 * @since  2011/9/20
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/20,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140S04A.class)
public class C140S04A_ {
	public static volatile SingularAttribute<C140S04A, BigDecimal> amtUnitST;
	public static volatile SingularAttribute<C140S04A, String> invBusna1;
	public static volatile SingularAttribute<C140S04A, String> invCap11;
	public static volatile SingularAttribute<C140S04A, BigDecimal> invCap21;
	public static volatile SingularAttribute<C140S04A, String> invKind1;
	public static volatile SingularAttribute<C140S04A, String> invTit1;
	public static volatile SingularAttribute<C140S04A, BigDecimal> invUnit11;
	public static volatile SingularAttribute<C140S04A, BigDecimal> invUnit21;
	public static volatile SingularAttribute<C140S04A, C140M04A> c140m04a;
}
