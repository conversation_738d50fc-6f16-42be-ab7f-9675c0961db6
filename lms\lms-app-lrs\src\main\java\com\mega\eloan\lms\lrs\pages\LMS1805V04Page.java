/* 
 * LMS1805V04Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.pages;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 覆審名單 - 已產生覆審名單報告檔
 * </pre>
 * 
 * @since 2011/9/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/6,irene,new
 *          </ul>
 */
@Controller
@RequestMapping("/lrs/lms1805v04")
public class LMS1805V04Page extends AbstractEloanInnerView {

	public LMS1805V04Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(RetrialDocStatusEnum.已產生覆審名單報告檔);
		// 加上Button
		List<EloanPageFragment> list = new ArrayList<>();
		if (isOverSea()) {
			// 海外
			list.addAll(Arrays.asList(LmsButtonEnum.Filter,
					LmsButtonEnum.ProduceExcel));
			if (this.getAuth(AuthType.Modify)) {
				list.add(LmsButtonEnum.ExceptRetrialDate);
			}
			list.add(LmsButtonEnum.View);
		} else {
			list.addAll(Arrays.asList(LmsButtonEnum.Filter,
					LmsButtonEnum.ReturnToCompiling, LmsButtonEnum.SendBtt,
					LmsButtonEnum.ProduceExcel, LmsButtonEnum.SendRetrialReport,
					LmsButtonEnum.ProduceEvaluateTbl,
					LmsButtonEnum.ExceptRetrialDate));
		}

		addToButtonPanel(model, list);
		renderJsI18N(LMS1805V01Page.class);
		renderJsI18N(AbstractEloanPage.class);
		renderJsI18N(LMS1800M01Page.class);
		renderJsI18N(LMS1805V04Page.class);
		
		loadJavaScript(model);
	}

	public void loadJavaScript(ModelMap model) {
		if (isOverSea()) {
			model.addAttribute("loadScript", "loadScript('pagejs/lrs/LMS1805V01Page');");
		} else {
			model.addAttribute("loadScript", "loadScript('pagejs/lrs/LMS1800V01Page');");
		}
	}
}
