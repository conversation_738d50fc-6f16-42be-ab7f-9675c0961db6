<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
      	<script type="text/javascript">
        	loadScript('pagejs/base/GridViewFilterPanel');
        </script>
            <div id="filterBox" style="display:none">
                <form id="filterForm">
                    <table class="tb2">
                        <tbody>
                        	<tr>
                                <td class="hd1">
                                    <th:block th:text="#{'typCd.title'}">區部別</th:block>：&nbsp;&nbsp;
                                </td>
                                <td>
                                	<select id="typCd" name="typCd" space="true" combokey="TypCd"></select>
                                </td>
                            </tr>
							<tr>
                                <td class="hd1">
                                    <th:block th:text="#{'l120m01a.doctype'}">企/個金</th:block>：&nbsp;&nbsp;
                                </td>
                                <td>
                                    <select id="docType" name="docType" space="true" combokey="L120M01A_docType"></select>
                                </td>
                            </tr>							
							<tr>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.id'}"><!-- 客戶統編--></th:block>：&nbsp;&nbsp;
                                </td>
                                <td>
                                    <input id="custId" name="custId" type="text" class="upText" size="10" maxlength="10" />
                                </td>
                            </tr>
							<tr>
                                <td class="hd1">
                                    <th:block th:text="#{'l120m01a.custName'}">申貸戶名稱</th:block>：&nbsp;&nbsp;
                                </td>
                                <td>
                                    <input id="custName" name="custName" type="text" size="20" maxlength="120" maxlengthC="40" />
                                </td>
                            </tr>							
                            <tr>
                                <td class="hd1" style="width:25%">
                                    <th:block th:text="#{'l120m01a.caseDate'}">簽案日期</th:block>：&nbsp;&nbsp;
                                </td>
                                <td>
                                    <input id="fromDate" name="fromDate"  class="date"  type="text"   size="8" maxlength="10" />
                                    ~
                                    <input id="endDate" name="endDate"  class="date"  type="text"   size="8" maxlength="10" />
                                    <span class="text-red">ex:YYYY-MM-DD</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1" style="width:25%">
                                    <th:block th:text="#{'l120m01a.approveTime'}">核准日期</th:block>：&nbsp;&nbsp;
                                </td>
                                <td>
                                    <input id="approveDateS" name="approveDateS"  class="date"  type="text"   size="8" maxlength="10" />
                                    ~
                                    <input id="approveDateE" name="approveDateE"  class="date"  type="text"   size="8" maxlength="10" />
                                    <span class="text-red">ex:YYYY-MM-DD</span>
                                </td>
                            </tr>
							
                        </tbody>
                    </table>
                </form>
            </div>
        </th:block>
    </body>
</html>