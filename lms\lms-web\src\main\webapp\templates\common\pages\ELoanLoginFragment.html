<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<div th:fragment="loginFragment" id="loginFragment">
    <form name='f' id="login-form" action="../app/ssoverify" method="post">
        <br />
        <br />
        <br />
        <br />
        <br />
        <!--以下為測試用ID-->
        <script>
        	loadScript('pagejs/base/ELoanLoginPage');
        </script>
		<select id="branchType" name="branchType" class="tit">
			<option value="0">--請選擇--</option>
			<option value="1">忠孝分行</option>
			<option value="2">國金部</option>
			<option value="3">香港</option>
			<option value="4">澳洲</option>
			<option value="5">泰國</option>
			<option value="6">加拿大</option>
			<option value="7">營運中心</option>
			<option value="8">授管處</option>
			<option value="9">稽核處</option>
			<option value="10">資訊處</option>
			<option value="11">國外部</option>
			<option value="12">金控總部</option>
			<option value="13">財務部</option>
			<option value="14">楠梓分行</option>
			<option value="15">仁武簡易型分行</option>
			<option value="16">004台中分行</option>
			<option value="17">202台北分行</option>
			<option value="18">008台北復興分行</option>
		</select>
		
		<select id="branchAccount0" >
			<option value="0">--請選擇--</option>
		</select>
		
		<select id="branchAccount1" style="display:none">
			<option value="0">--請選擇--</option>
			<!--<option value="000001">忠孝分行-全分行</option>
			<option value="010001">忠孝分行-分經辦</option>
			<option value="010002">忠孝分行-分主管</option>
			<option value="010003">忠孝分行-分內查</option>-->
			<!-- 新增 -->
			<option value="046001">新店分行-分經辦_046001</option>
			<option value="000987">忠孝分行-分主管_000987</option>
			<option value="002417">忠孝分行-分內查_002417</option>
			<option value="030433">忠孝分行-忠經辦_030433</option>
			<option value="007799">忠孝分行-忠經辦_007799</option>
			<option value="005116">忠孝分行-忠經辦_005116</option>
			<option value="006891">忠孝分行-忠經辦_006891</option>
			<option value="007091">忠孝分行-忠經辦_007091</option>
			<option value="030218">忠孝分行-忠經辦_030218</option>
			<option value="030305">忠孝分行-忠經辦_030305</option>
			<option value="007177">忠孝分行-忠經辦_007177</option>						
			<option value="000093">忠孝分行-忠主管_000093</option>
			<option value="001100">忠孝分行-忠主管_001100</option>
		</select>
		
		<select id="branchAccount2" style="display:none">
			<option value="0">--請選擇--</option>
			<!--<option value="010006">國金部-國金經辦</option>
			<option value="010007">國金部-國金主管</option>-->
			<!-- 新增 -->
			<option value="006545">國金部-國金經辦_06545</option>
			<option value="003333">國金部-國金主管_03333</option>
			<option value="005033">國金部-邱經辦_005033</option>
			<option value="005084">國金部-李經辦_005084</option>
			<option value="006427">國金部-曾經辦_006427</option>
			<option value="006453">國金部-洪經辦_006453</option>
			<option value="005507">國金部-國金主管_005507</option>
			<option value="006251">國金部-林主管_006251</option>
		</select>
		
		<select id="branchAccount3" style="display:none">
			<option value="0">--請選擇--</option>
			<!-- <option value="010009">香港分行-香經辦</option>
			<option value="010010">香港分行-香主管</option>-->
			<!-- 新增 -->
			<option value="006993">香港分行-香經辦_06993</option>
			<option value="002850">香港分行-香主管_02850</option>
			<option value="040888">香港分行-香港經辦_40888</option>
			<option value="006998">香港分行-陳經辦_006998</option>
			<option value="004889">香港分行-林經辦_004889</option>
			<option value="001685">香港分行-香港主管_001685</option>
			<option value="005075">香港分行-郭主管_005075</option>
		</select>
		
		<select id="branchAccount4" style="display:none">
			<option value="0">--請選擇--</option>
			<!--<option value="010011">墨爾本分行-墨經辦</option>
			<option value="010012">墨爾本分行-墨主管</option>-->
			<!-- 新增 -->
			<option value="00C5T1">墨爾本分行-墨櫃員_0C5T1</option>
			<option value="004390">墨爾本分行-墨經辦_04390</option>
			<option value="003835">墨爾本分行-墨主管_03835</option>
			<option value="006014">墨爾本分行-墨主管_06014</option>
			
			<!--<option value="010013">布里斯本分行-布經辦</option>
			<option value="010014">布里斯本分行-布主管</option>-->
			<!-- 新增 -->
			<option value="00C2T1">布里斯本分行-布櫃員_0C2T1</option>
			<option value="006167">布里斯本分行-布經辦_06167</option>
			<option value="002180">布里斯本分行-布主管_02180</option>
			<option value="000966">布里斯本分行-布主管_00966</option>
			
			<!--<option value="010015">雪梨總行-雪經辦</option>
			<option value="010016">雪梨總行-雪主管</option>-->
			<!-- 新增 -->
			<option value="041121">雪梨總行-雪經辦_41121</option>
			<option value="041210">雪梨總行-雪經辦_41210</option>
			<option value="041211">雪梨總行-雪經辦_41211</option>
			<option value="004634">雪梨總行-雪主管_04634</option>
			<option value="004801">雪梨總行-雪主管_04801</option>
		</select>
		
		<select id="branchAccount5" style="display:none">
			<option value="0">--請選擇--</option>
			<!--<option value="010017">春武里分行-春經辦</option>
			<option value="010018">春武里分行-春主管</option>-->
			<!-- 新增 -->
			<option value="00E3T2">春武里分行-春櫃員_0E3T2</option>
			<option value="005368">春武里分行-春經辦_05368</option>
			<option value="040488">春武里分行-春主管_40488</option>
			<option value="040905">春武里分行-林經辦_040905</option>
			<option value="040753">春武里分行-谷經辦_040753</option>
			<option value="040503">春武里分行-傅經辦_040503</option>
			<option value="040590">春武里分行-羅經辦_040590</option>
			<option value="002307">春武里分行-張主管_002307</option>
			<option value="040485">春武里分行-許主管_040485</option>
			
			<!--<option value="010019">曼谷總行-曼經辦</option>
			<option value="010020">曼谷總行-曼主管</option>-->
			<!-- 新增 -->
			<option value="006994">曼谷總行-曼經辦_06994</option>
			<option value="000434">曼谷總行-曼主管_00434</option>
			
			<option value="040305">曼谷總行-曼谷經辦_040305</option>
			<option value="040075">曼谷總行-巫經辦_040075</option>
			<option value="040316">曼谷總行-曼谷主管_040316</option>
			<option value="040074">曼谷總行-許經辦_040074</option>
			<option value="040073">曼谷總行-朱經辦_040073</option>
			<option value="040568">曼谷總行-許主管_040568</option>
		</select>
		
		<select id="branchAccount6" style="display:none">
			<option value="0">--請選擇--</option>
			<!--<option value="010021">溫哥華分行-溫經辦</option>
			<option value="010022">溫哥華分行-溫主管</option>-->
			<!-- 新增 -->
			<option value="00E1T2">溫哥華分行-溫櫃員_0E1T2</option>
			<option value="002435">溫哥華分行-溫經辦_02435</option>
			<option value="000964">溫哥華分行-溫主管_00964</option>
			
			<!--<option value="010023">多倫多總行-多經辦</option>
			<option value="010024">多倫多總行-多主管</option>-->
			<!-- 新增 -->
			<option value="006139">多倫多總行-多經辦_06139</option>
			<option value="006086">多倫多總行-多經辦_06086</option>
			<option value="000284">多倫多總行-多主管_00284</option>
			<option value="002435">多倫多總行-多主管_02435</option>
		</select>
		
		<select id="branchAccount7" style="display:none">
			<option value="0">--請選擇--</option>
			<!-- <option value="000002">營運中心-全營運</option>
			<option value="030001">營運中心-營經辦</option>
			<option value="030002">營運中心-營主管</option>-->
			<!-- 新增 -->
			<option value="002882">營運中心-營經辦_02882</option>
			<option value="004123">營運中心-營主管_04123</option>
			<option value="003137">營運中心-區域經辦_03137</option>
			<option value="002471">營運中心-區域經辦_02471</option>
			<option value="004224">營運中心-區域經辦_04224</option>
			<option value="004715">營運中心-區域經辦_04715</option>
			<option value="000268">營運中心-區域主管_00268</option>
			<option value="003867">營運中心-區域主管_03867</option>
			<option value="005400">營運中心932-區域經辦_005400</option>
			<option value="005539">營運中心932-區域主管_005539</option>
			<option value="002031">南區935-區域經辦_002031</option>
			<option value="000596">南區935-區域主管_000596</option>
			
			<option value="002546">中區934-區域經辦_002546</option>
			<option value="000532">中區934-區域主管_000532</option>
		</select>
		
		<select id="branchAccount8" style="display:none">
			<option value="0">--請選擇--</option>
			<!--<option value="000003">授管處-全授管</option>
			<option value="050001">授管處-授經辦</option>
			<option value="050002">授管處-授主管</option>
			<option value="050003">授管處-收件員</option>-->
			<!-- 新增 -->
			<option value="006081">授管處-授經辦_06081</option>
			<option value="006163">授管處-授主管_06163</option>
			<option value="005225">授管處-收件員_05225</option>
			
			<option value="000076">授管處-授經辦_000076</option>
			<option value="002494">授管處-林經辦_002494</option>
			<option value="003487">授管處-陳經辦_003487</option>
			<option value="003805">授管處-黃經辦_003805</option>
			<option value="000161">授管處-授主管_000161</option>
		</select>
		
		<select id="branchAccount9" style="display:none">
			<option value="0">--請選擇--</option>
			<!--<option value="000004">稽核處-全稽核</option>
			<option value="060001">稽核處-稽經辦</option>
			<option value="060002">稽核處-稽主管</option>-->
			<!-- 新增 -->
			<option value="006032">稽核處-稽經辦_06032</option>
			<option value="002211">稽核處-稽經辦_02211</option>
			<option value="003189">稽核處-稽經辦_03189</option>
			<option value="001033">稽核處-稽主管_01033</option>
			<option value="000231">稽核處-稽主管_00231</option>
		</select>
		
		<select id="branchAccount10" style="display:none">
			<option value="0">--請選擇--</option>
			<option value="005097">005097</option>
			<option value="006148">006148</option>
		</select>
		
		<select id="branchAccount11" style="display:none">
			<option value="0">--請選擇--</option>
			<option value="030082">國外部-國外經辦_030082</option>
			<option value="000240">國外部-國外主管_000240</option>
		</select>
		
		<select id="branchAccount12" style="display:none">
			<option value="0">--請選擇--</option>
			<option value="005372">金控總部-金控經辦_004526</option>
			<option value="005623">金控總部-金控主管_000987</option>
		</select>

		<select id="branchAccount13" style="display:none">
			<option value="0">--請選擇--</option>
			<option value="004526">財務部-財經辦_004526</option>
			<option value="000987">財務部-財主管_000987</option>
		</select>
		
		<select id="branchAccount14" style="display:none">
			<option value="0">--請選擇--</option>
			<option value="003727">楠梓分行-伍經辦_003727</option>
			<option value="001499">楠梓分行-張主管_001499</option>
		</select>
		
		<select id="branchAccount15" style="display:none">
			<option value="0">--請選擇--</option>
			<option value="004078">仁武簡易型分行-陳經辦_004078</option>
			<option value="002214">仁武簡易型分行-蔡主管_002214</option>
		</select>
		
		<select id="branchAccount16" style="display:none">
			<option value="0">--請選擇--</option>
			<option value="002225">李經辦_002225</option>
			<option value="000536">林主管_000536</option>
		</select>
		
		<select id="branchAccount17" style="display:none">
			<option value="0">--請選擇--</option>
			<option value="005381">李經辦_005381</option>
			<option value="005629">林主管_005629</option>
		</select>
		
		<select id="branchAccount18" style="display:none">
			<option value="0">--請選擇--</option>
			<option value="002864">李經辦_002864</option>
			<option value="001946">林主管_001946</option>
		</select>
		
		<table style="margin-left: 280px; padding: 1px; *margin-left: 0px;">
           	<input type="hidden" id="_csrf" name="_csrf" th:value="${_csrf.token}"></input>
            <tr>
                <!-- 
                <td style="padding-right: 15px;"><th:block th:text="#{'login.id'}">使用者帳號</th:block></td>
                 -->
                <td style="padding-right: 15px;"><span th:text="#{ELoanLoginPage.login.id}">使用者帳號</span></td>
				<td><input type="text" id="login" name="lightID" value="046001"/></td>
            </tr>
            <tr>
                <!-- 
                <td style="padding-right: 15px;"><th:block th:text="#{'login.password'}">密碼</th:block></td>
                 -->
                <td style="padding-right: 15px;"><span th:text="#{ELoanLoginPage.login.password}">密碼</span></td>
                <td><input type="password" id="password" name="j_password" /></td>
            </tr>
			<tr>
				<td style="padding-right:15px;">登入分行</td>
				<td><input type="text" id="loginUnit" name="loginUnit"/></td>
			</tr>
            <tr>
                <td colspan="2" style="text-align: left;"><input type="submit" class="button" name="commit" value="Log in" style="float: right;" /></td>
            </tr>
        </table>
    </form>
</div>

<div th:fragment="messageFragment" id="messageFragment">
    <center>
        <div class="ui-widget errormsg">
            <div class="ui-state-error ui-corner-all" style="padding: 10px;">[[${message]]</div>
        </div>
    </center>
</div>
<div th:fragment="userRelogin" id="userRelogin">
    <script type="text/javascript">
        $(function(){
            if (confirm(res.lightID+"使用者已登入，是否要繼續登入?")) {
                var url = '../app/ssoverify?lightID='+res.lightID+'&branch='+res.unitno+'&relogin=1';
                window.location=url;
            }
		});
	</script>
</div>
<div th:fragment="closeWindow" id="closeWindow">
    <script type="text/javascript">
    	$(function(){window.close();});
	</script>
</div>

</html>