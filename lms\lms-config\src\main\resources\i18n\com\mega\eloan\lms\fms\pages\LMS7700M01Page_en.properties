L140MM5A.title=The electronic data file archives overdue lists
L140MM5A.custId=Cust Id
L140MM5A.dupNo=DupNo
L140MM5A.custName=Cust Name
L140MM5A.ces=Credit Assessment
L140MM5A.cms=Collateral
L140MM5A.col=Overdue
L140MM5A.lms=Credit
L140MM5A.rps=Data Archiving
L140MM5A.isDelete=Delete or not
L140MM5A.reason=Reason for not deleting
L140MM5A.dataFrom=Data Source
L140MM5A.closeDate=Close Date

L140MM5A.sys=Data location

L140MM5B.title01=Business Unit
L140MM5B.selectBoss=Number Of Credit Supervisors
L140MM5B.bossId=Credit Supervisor
L140MM5B.managerId=Unit/Authorizer
L140MM5B.reCheckId=Approver
L140MM5B.apprId=Handling Officer

L140MM5B.message01=Whether to submit for supervisor's approval
L140MM5B.message02=Supervisor's name duplicated; please select again
L140MM5B.message03=Whether to return the case to the handling officer for amendments; to return, please press [OK], otherwise press [Cancel]
L140MM5B.message04=Proceed with confirmation?

accept=Accept
back=Return to handling officer for correction
approve=Approve

checkSelect=Please choose 
cantEmpty= can't be blank
notMatchFormat= Incorrect format

message01=Can't delete the list generated by the system
message02=There are still lists that are not maintained

button.addRow=Add
button.deleteRow=Delete
button.maintain=Maintain Detail

sys=system
user=Artificial

Y=Yes
N=No