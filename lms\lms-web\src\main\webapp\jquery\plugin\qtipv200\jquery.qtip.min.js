/*
* qTip - The jQuery tooltip plugin
* http://craigsworks.com/projects/qtip/
*
* Version: 2.0.0pre
* Copyright 2009 <PERSON> - http://craigsworks.com
*
* Dual licensed under MIT or GPL Version 2 licenses
*   http://en.wikipedia.org/wiki/MIT_License
*   http://en.wikipedia.org/wiki/GNU_General_Public_License
*
* Date: Thu Aug 19 19:28:14 2010 +0100
*/

"use strict"; // Enable ECMAScript "strict" operation for this function. See more: http://ejohn.org/blog/ecmascript-5-strict-mode-json-and-more/
/*jslint browser: true, onevar: true, undef: true, nomen: true, bitwise: true, regexp: true, newcap: true, immed: true, strict: true */
/*global window: false, jQuery: false */

var TRUE=true,FALSE=false,NULL=null;
(function(a,B,E){function K(b){if(!b)return FALSE;try{if("metadata"in b&&"object"!==typeof b.metadata)b.metadata={type:b.metadata};if("content"in b){if("object"!==typeof b.content||b.content.jquery)b.content={text:b.content};var c=b.content.text||FALSE;if(c.length<1||!c&&!c.attr||"object"===typeof c&&!c.jquery)b.content.text=FALSE;if("title"in b.content&&"object"!==typeof b.content.title)b.content.title={text:b.content.title}}if("position"in b){if("object"!==typeof b.position)b.position={my:b.position,
at:b.position};if("object"!==typeof b.position.adjust)b.position.adjust={};if("undefined"!==typeof b.position.adjust.screen)b.position.adjust.screen=!!b.position.adjust.screen}if("show"in b){if("object"!==typeof b.show)b.show={event:b.show};if("object"!==typeof b.show)b.show=b.show.jquery?{target:b.show}:{event:b.show}}if("hide"in b)if("object"!==typeof b.hide)b.hide=b.hide.jquery?{target:b.hide}:{event:b.hide};if("style"in b&&"object"!==typeof b.style)b.style={classes:b.style}}catch(k){}a.each(a.fn.qtip.plugins,
function(){this.sanitize&&this.sanitize(b)})}function L(b,c,k){function g(e){var h,n;e=e.split(".");h=c[e[0]];for(n=1;n<e.length;n+=1)if(typeof h[e[n]]==="object"&&!h[e[n]].jquery)h=h[e[n]];else break;return[h,e[n]]}function p(e){var h=d.elements.tooltip,n=!h.is(":visible")?TRUE:FALSE,j=FALSE;if(!d.rendered)return FALSE;n&&h.addClass("ui-tooltip-accessible");switch(e){case "dimensions":j={height:h.outerHeight(),width:h.outerWidth()};break;case "position":j=h.offset();break}n&&h.removeClass("ui-tooltip-accessible");
return j}function o(){var e=d.elements;if(e.title){e.titlebar.remove();e.titlebar=e.title=e.button=NULL;e.tooltip.removeAttr("aria-labelledby")}}function v(){var e=d.elements,h=c.content.title.button;e.titlebar&&o();e.titlebar=a("<div />",{"class":"ui-tooltip-titlebar "+(c.style.widget?"ui-widget-header":"")}).append(e.title=a("<div />",{id:"ui-tooltip-"+k+"-title","class":"ui-tooltip-title",html:c.content.title.text})).prependTo(e.wrapper);if(h){e.button=h.jquery?h:"string"===typeof h?a("<a />",
{html:h}):a("<a />",{"class":"ui-state-default"}).append(a("<span />",{"class":"ui-icon ui-icon-close"}));e.button.prependTo(e.titlebar).attr("role","button").addClass("ui-tooltip-"+(h===TRUE?"close":"button")).hover(function(n){a(this).toggleClass("ui-state-hover",n.type==="mouseenter")}).click(function(){e.tooltip.hasClass("ui-state-disabled")||d.hide();return FALSE}).bind("mousedown keydown mouseup keyup mouseout",function(n){a(this).toggleClass("ui-state-active ui-state-focus",/down$/i.test(n.type))})}}
function q(e){if(!d.rendered||!e)return FALSE;e.jquery&&e.length>0?d.elements.content.append(e.css({display:"block"})):d.elements.content.html(e);e=void 0;var h=d.elements.tooltip,n,j;if(d.rendered&&a.browser.msie){h.css({width:"auto",maxWidth:"none"});e=p("dimensions").width;h.css({maxWidth:""});n=parseInt(h.css("max-width"),10)||0;j=parseInt(h.css("min-width"),10)||0;e=Math.min(Math.max(e,j),n);h.width(e)}d.reposition(d.cache.event);if(d.rendered<0){if(c.show.ready||d.rendered===-2)d.show(d.cache.event);
d.rendered=TRUE}return d}function w(e,h,n,j){function f(y){if(r.tooltip.hasClass("ui-state-disabled"))return FALSE;r.show.trigger("qtip-"+k+"-inactive");clearTimeout(d.timers.show);clearTimeout(d.timers.hide);d.timers.show=setTimeout(function(){d.show(y)},c.show.delay)}function i(y){if(r.tooltip.hasClass("ui-state-disabled"))return FALSE;var z=a(y.relatedTarget).parents(".qtip.ui-tooltip")[0]==r.tooltip[0];clearTimeout(d.timers.show);clearTimeout(d.timers.hide);if(c.position.target==="mouse"&&z||
c.hide.fixed&&/mouse(out|leave|move)/.test(y.type)&&z){y.stopPropagation();y.preventDefault();return FALSE}r.tooltip.stop(TRUE,TRUE);d.timers.hide=setTimeout(function(){d.hide(y)},c.hide.delay)}function l(y){if(r.tooltip.hasClass("ui-state-disabled"))return FALSE;clearTimeout(d.timers.inactive);d.timers.inactive=setTimeout(function(){d.hide(y)},c.hide.inactive)}function m(y){d.elements.tooltip.is(":visible")&&d.reposition(y)}var t=".qtip-"+k,r={show:c.show.target,hide:c.hide.target,tooltip:d.elements.tooltip},
x={show:String(c.show.event).split(" "),hide:String(c.hide.event).split(" ")},s=a.browser.msie&&/^6\.[0-9]/.test(a.browser.version);if(n&&c.hide.fixed){r.hide=r.hide.add(r.tooltip);r.tooltip.bind("mouseover"+t,function(){r.tooltip.hasClass("ui-state-disabled")||clearTimeout(d.timers.hide)})}if(h){if("number"===typeof c.hide.inactive){r.show.bind("qtip-"+k+"-inactive",l);a.each(a.fn.qtip.inactiveEvents,function(y,z){r.hide.add(d.elements.tooltip).bind(z+t+"-inactive",l)})}a.each(x.hide,function(y,
z){var A=a.inArray(z,x.show);if(A>-1&&a(r.hide).add(r.show).length===a(r.hide).length||z==="unfocus"){r.show.bind(z+t,function(C){r.tooltip.is(":visible")?i(C):f(C)});delete x.show[A]}else r.hide.bind(z+t,i)})}if(e){a.each(x.show,function(y,z){r.show.bind(z+t,f)});r.tooltip.bind("mouseover"+t,function(){d.focus()})}if(j){if(c.position.adjust.resize||c.position.adjust.screen)a(B).bind("resize"+t,m);if(c.position.adjust.screen||s&&r.tooltip.css("position")==="fixed")a(document).bind("scroll"+t,m);/unfocus/i.test(c.hide.event)&&
a(document).bind("mousedown"+t,function(y){var z=d.elements.tooltip;a(y.target).parents(".qtip.ui-tooltip").length===0&&a(y.target).add(b).length>1&&z.is(":visible")&&!z.hasClass("ui-state-disabled")&&d.hide()});c.position.target==="mouse"&&a(document).bind("mousemove"+t,function(y){c.position.adjust.mouse&&!r.tooltip.hasClass("ui-state-disabled")&&r.tooltip.is(":visible")&&d.reposition(y)})}}function u(e,h,n,j){j=parseInt(j,10)!==0;var f=".qtip-"+k,i={show:e?c.show.target:a("<div/>"),hide:h?c.hide.target:
a("<div/>"),tooltip:n?d.elements.tooltip:a("<div/>")};h={show:String(c.show.event).split(" "),hide:String(c.hide.event).split(" ")};if(d.rendered){a.each(h.show,function(l,m){i.show.unbind(m+f)});i.show.unbind("mousemove"+f).unbind("mouseout"+f).unbind("qtip-"+k+"-inactive");a.each(h.hide,function(l,m){i.hide.add(i.tooltip).unbind(m+f)});a.each(a.fn.qtip.inactiveEvents,function(l,m){i.hide.add(n?d.elements.content:NULL).unbind(m+f+"-inactive")});i.hide.unbind("mouseout"+f);i.tooltip.unbind("mouseover"+
f);if(j){a(B).unbind("resize"+f);a(document).unbind("mousedown"+f+" mousemove"+f)}}else e&&i.show.unbind(h.show+f+"-create")}var d=this;d.id=k;d.rendered=FALSE;d.elements={target:b};d.cache={event:{},target:NULL,disabled:FALSE};d.timers={};d.options=c;d.plugins={};a.extend(d,{render:function(e){var h=d.elements;if(d.rendered)return FALSE;d.rendered=e?-2:-1;h.tooltip=a("<div/>").attr({id:"ui-tooltip-"+k,role:"tooltip"}).addClass("qtip ui-tooltip ui-helper-reset "+c.style.classes).toggleClass("ui-widget",
c.style.widget).toggleClass("ui-state-disabled",d.cache.disabled).css("z-index",a.fn.qtip.zindex+a("div.qtip.ui-tooltip").length).data("qtip",d).appendTo(c.position.container);h.wrapper=a("<div />").addClass("ui-tooltip-wrapper").appendTo(h.tooltip);h.content=a("<div />").addClass("ui-tooltip-content").attr("id","ui-tooltip-"+k+"-content").addClass("ui-tooltip-content").toggleClass("ui-widget-content",c.style.widget).appendTo(h.wrapper);c.content.title.text&&v();a.each(a.fn.qtip.plugins,function(){this.initialize===
"render"&&this(d)});w(1,1,1,1);a.each(c.events,function(n,j){h.tooltip.bind("tooltip"+n,j)});b.bind("remove.qtip",function(){d.destroy()});q(c.content.text);h.tooltip.trigger("tooltiprender",[d.hash()]);return d},get:function(e){switch(e.toLowerCase()){case "offset":e=p("position");break;case "dimensions":e=p("dimensions");break;default:e=g(e.toLowerCase());e=e[0].precedance?e[0].string():e[0].jquery?e[0]:e[0][e[1]];break}return e},set:function(e,h){var n=g(e.toLowerCase()),j,f,i,l={builtin:{"^content.text":function(){q(h)},
"^content.title.text":function(){if(d.rendered)if(!d.elements.title&&h){v();d.reposition()}else h?d.elements.title.html(h):o()},"^position.container$":function(){d.rendered&&d.elements.tooltip.appendTo(h)},"^position.(my|at)$":function(){var m=/my$/i.test(e)?"my":"at";if("string"===typeof h)c.position[m]=new a.fn.qtip.plugins.Corner(h)},"^position.(my|at|adjust|target)":function(){d.rendered&&d.reposition()},"^(show|hide).(event|target|fixed)":function(m,t,r,x){var s=e.search(/fixed/i)>-1?[0,[0,1,
1,1]]:e.search(/hide/i)<0?["show",[1,0,0,0]]:["hide",[0,1,0,0]];if(s[0])m[t]=x;u.apply(d,s[1]);if(s[0])m[t]=r;w.apply(d,s[1])}}};a.each(d.plugins,function(m){if("object"===typeof this.checks)l[m]=this.checks});j=n[0][n[1]];n[0][n[1]]=h;K(c);for(f in l)for(i in l[f])RegExp(i,"i").test(e)&&l[f][i].call(d,n[0],n[1],h,j);return d},toggle:function(e,h){function n(){var t=e?"attr":"removeAttr",r=/^1|0$/.test(a(this).css("opacity"));d.elements.title&&b[t]("aria-labelledby","ui-tooltip-"+k+"-title");b[t]("aria-describedby",
"ui-tooltip-"+k+"-content");if(e){if(a.browser.msie&&a(this).get(0).style&&r){m=a(this).get(0).style;m.removeAttribute("filter");m.removeAttribute("opacity")}}else r&&a(this).hide()}if(!d.rendered)return FALSE;var j=e?"show":"hide",f=d.elements.tooltip,i=c[j],l=f.is(":visible"),m;if((typeof e).search("boolean|number"))e=!f.is(":visible");if(!l&&!e||f.is(":animated"))return d;if(h){if(d.cache.event&&/over|enter/.test(h.type)&&/out|leave/.test(d.cache.event.type)&&a(h.target).add(c.show.target).length<
2&&a(h.relatedTarget).parents(".qtip.ui-tooltip").length>0)return d;d.cache.event=a.extend({},h)}l=a.Event("tooltip"+j);l.originalEvent=a.extend({},h);f.trigger(l,[d.hash(),90]);if(l.isDefaultPrevented())return d;if(e){if(d.rendered===TRUE){d.focus();d.reposition(h)}i.solo&&a(":not(.qtip.ui-tooltip)").qtip("hide")}else clearTimeout(d.timers.show);f.attr("aria-hidden",Boolean(!e));f.stop(TRUE,FALSE);if(a.isFunction(i.effect)){i.effect.call(f);f.queue(function(){n.call(this);a(this).dequeue()})}else if(i.effect===
FALSE){f[j]();n.call(f)}else f.fadeTo(90,e?100:0,n);e&&i.target.trigger("qtip-"+k+"-inactive");return d},show:function(e){d.toggle(TRUE,e)},hide:function(e){d.toggle(FALSE,e)},focus:function(e){if(!d.rendered)return FALSE;var h=d.elements.tooltip,n=parseInt(h.css("z-index"),10),j=a.fn.qtip.zindex+a(".qtip.ui-tooltip").length,f=a.extend({},e);if(!h.hasClass("ui-tooltip-focus")&&n!==j){a(".qtip.ui-tooltip").each(function(){var i=a(this).qtip(),l=a.Event("tooltipblur"),m,t;if(!i||!i.rendered)return TRUE;
m=i.elements.tooltip;t=parseInt(m.css("z-index"),10);isNaN(t)||m.css({zIndex:t-1});m.removeClass("ui-tooltip-focus");l.originalEvent=f;m.trigger(l,[i,j])});e=a.Event("tooltipfocus");e.originalEvent=f;h.trigger(e,[d.hash(),j]);e.isDefaultPrevented()||h.css({zIndex:j}).addClass("ui-tooltip-focus")}return d},reposition:function(e){if(d.rendered===FALSE)return FALSE;var h=c.position.target,n=d.elements.tooltip,j=c.position,f=j.my,i=j.at,l=d.elements.tooltip.width(),m=d.elements.tooltip.height(),t=j.container,
r=0,x=0,s={left:0,top:0},y=a.Event("tooltipmove"),z={left:function(A){var C=h==="mouse"?e.pageX:h.offset().left,G=a(B).scrollLeft(),F=a(B).width(),D=i.x==="left"?r:i.x==="right"?-r:r/2,H=D+(f.x==="left"?-l:f.x==="right"?l:l/2)+-2*j.adjust.x;F=A+l-F-G;if(G-A>0&&!(A>=C&&A<C+r))s.left+=H-D;else if(F>0&&A+l>C)s.left+=f.x==="center"?-H+D:H-D;return s.left-A},top:function(A){var C=a(B).scrollTop(),G=a(B).height(),F=i.y==="top"?x:i.y==="bottom"?-x:0,D=F+(f.y==="top"?-m:f.y==="bottom"?m:-m/2)+-2*j.adjust.y;
G=A+m-G-C;if(C-A>0)s.top+=f.y==="center"?-D+F:D;else if(G>0)s.top+=D-F;return s.top-A}};if(h==="mouse"){i={x:"left",y:"top"};if(!e)e=d.cache.event;s={top:e.pageY,left:e.pageX}}else{if(h==="event")h=e&&e.target&&e.type!=="scroll"&&e.type!=="resize"?d.cache.target=a(e.target):d.cache.target;if(h[0]===document||h[0]===B){r=h.width();x=h.height();s={top:n.css("position")==="fixed"?0:h.scrollTop(),left:h.scrollLeft()}}else if(h.is("area")&&a.fn.qtip.plugins.imagemap){s=a.fn.qtip.plugins.imagemap(h,i);
r=s.width;x=s.height;s=s.offset}else{r=h.outerWidth();x=h.outerHeight();if(!j.adjust.offset||t[0]===document.body)s=h.offset();else{s=h.position();s.top+=t.scrollTop()-t.offset().top;s.left+=t.scrollLeft()-t.offset().left}}s.left+=i.x==="right"?r:i.x==="center"?r/2:0;s.top+=i.y==="bottom"?x:i.y==="center"?x/2:0}s.left+=j.adjust.x+(f.x==="right"?-l:f.x==="center"?-l/2:0);s.top+=j.adjust.y+(f.y==="bottom"?-m:f.y==="center"?-m/2:0);s.adjusted=j.adjust.screen?{left:z.left(s.left),top:z.top(s.top)}:{left:0,
top:0};if(s.top<1)s.top=0;if(s.left<1)s.left=0;n.attr("class",function(){return a(this).attr("class").replace(/ui-tooltip-pos-\w+/i,"")}).addClass("ui-tooltip-pos-"+f.abbreviation());y.originalEvent=a.extend({},e);n.trigger(y,[d.hash(),s]);if(y.isDefaultPrevented())return d;delete s.adjust;if(n.is(":visible")&&a.isFunction(j.adjust.effect)){j.adjust.effect.call(n,s);n.queue(function(){a(this).css({opacity:"",height:""});a.browser.msie&&a(this).get(0).style&&a(this).get(0).style.removeAttribute("filter");
a(this).dequeue()})}else n.css(s);return d},disable:function(e){var h=d.elements.tooltip;if(d.rendered)h.toggleClass("ui-state-disabled",e);else d.cache.disabled=!!e;return d},destroy:function(){var e=d.elements,h=e.target.data("oldtitle");d.rendered&&a.each(d.plugins,function(){this.initialize==="render"&&this.destroy()});u(1,1,1,1);b.removeData("qtip");d.rendered&&e.tooltip.remove();h&&b.attr("title",h);return b},hash:function(){var e=a.extend({},d);delete e.cache;delete e.timers;delete e.options;
delete e.plugins;delete e.render;delete e.hash;return e}})}function M(b,c){var k;k=a(this).metadata?a(this).metadata(c.metadata):{};k=a.extend(TRUE,{},c,k);var g=k.position,p=a(this)[0]===document?a(document.body):a(this);if("boolean"===typeof k.content.text)if(k.content.attr!==FALSE&&a(this).attr(k.content.attr))k.content.text=a(this).attr(k.content.attr);else return FALSE;if(g.container===FALSE)g.container=a(document.body);if(g.target===FALSE)g.target=p;if(k.show.target===FALSE)k.show.target=p;
if(k.hide.target===FALSE)k.hide.target=p;g.at=new a.fn.qtip.plugins.Corner(g.at);g.my=new a.fn.qtip.plugins.Corner(g.my);if(a(this).data("qtip"))if(k.overwrite)a(this).qtip("destroy");else if(k.overwrite===FALSE)return FALSE;k=new L(a(this),k,b);a(this).data("qtip",k);return k}function N(b){var c=this;c.checks={"^content.ajax":function(){this.plugins.ajax.load(this.options.content.ajax)}};a.extend(c,{init:function(){var k=b.options.content.ajax;c.load(k);b.elements.tooltip.bind("tooltipshow.ajax",
function(){k.once===FALSE&&b.rendered===TRUE&&c.load(k)})},load:function(k){a.ajax(a.extend(TRUE,{},k,{success:function(g,p){if(a.isFunction(k.success))if(k.success.call(b.hash(),g,p)===FALSE)return;b.set("content.text",g);I.remove()},error:function(g,p,o){var v=p||o;if(a.isFunction(k.error)){g=k.error.call(b.hash(),g,p,o);if(g===FALSE)return}b.set("content.text",v)}}));return c},destroy:function(){b.elements.tooltip.unbind("tooltipshow.ajax")}});c.init()}function J(b,c,k){var g=Math.floor(c/2),p=
Math.floor(k/2);c={bottomright:[[0,0],[c,k],[c,0]],bottomleft:[[0,0],[c,0],[0,k]],topright:[[0,k],[c,0],[c,k]],topleft:[[0,0],[0,k],[c,k]],topcenter:[[0,k],[g,0],[c,k]],bottomcenter:[[0,0],[c,0],[g,k]],rightcenter:[[0,0],[c,p],[0,k]],leftcenter:[[c,0],[c,k],[0,p]]};c.lefttop=c.bottomright;c.righttop=c.bottomleft;c.leftbottom=c.topright;c.rightbottom=c.topleft;return c[b]}function O(b){function c(j){var f=o.tip,i=["left","right"],l={left:0,right:0,top:0,bottom:0},m=0;if(p.corner===FALSE||!f)return FALSE;
j=j||g.corner;f.css({top:"",bottom:"",left:"",right:"",margin:""});i[j.precedance==="y"?"push":"unshift"]("top","bottom");if(a.browser.msie)l={top:j.precedance==="y"?0:0,left:0,bottom:j.precedance==="y"?0:0,right:0};switch(j[j.precedance==="y"?"x":"y"]){case "center":f.css(i[0],"50%").css("margin-"+i[0],-(u[j.precedance==="y"?"width":"height"]/2));break;case i[0]:f.css(i[0],l[i[0]]+h);break;case i[1]:f.css(i[1],l[i[1]]+h);break}m=u[j.precedance==="x"?"width":"height"];if(e)m-=parseInt(q.css("border-"+
j[j.precedance]+"-width"),10);j[j.precedance]===i[2]?f.css(i[2],-l[i[2]]-m):f.css(i[3],l[i[3]]-m)}function k(j,f,i){if(o.tip){j=a.extend({},g.corner);f=g.mimic.adjust?a.extend({},g.mimic):null;var l=j.precedance==="y"?["y","top","left","height"]:["x","left","top","width"],m=i.adjusted,t=parseInt(q.css("border-"+j[l[0]]+"-width"),10);a.each([j,f],function(){if(m.left)this.x=this.x==="center"?m.left>0?"left":"right":this.x==="left"?"right":"left";if(m.top)this.y=this.y==="center"?m.top>0?"top":"bottom":
this.y==="top"?"bottom":"top"});i[l[1]]+=(j[l[0]]===l[1]?1:-1)*(u[l[3]]-t);i[l[2]]-=h;if(j.string()!==w.corner.string()&&(w.top!==m.top||w.left!==m.left))g.update(j,f);w.left=m.left;w.top=m.top;w.corner=j}}var g=this,p=b.options.style.tip,o=b.elements,v=o.tooltip,q=o.wrapper,w={top:0,left:0,corner:{string:function(){}}},u={width:p.width,height:p.height},d={},e=p.border||0,h=p.adjust||0,n=p.method||FALSE;g.corner=NULL;g.mimic=NULL;g.checks={"^position.my|style.tip.(corner|mimic|method|border)":function(){e=
p.border;if(g.detectCorner()){g.create();g.detectColours();g.update()}else g.tip.remove();this.get("position.target")!=="mouse"&&this.reposition()},"^style.tip.(height|width)":function(){u={width:p.width,height:p.height};g.create();g.update();b.reposition()}};a.extend(g,{init:function(){if(n===TRUE)n=a("<canvas />")[0].getContext?"canvas":a.browser.msie&&(g.mimic&&/center/i.test(g.mimic.string())||u.height!==u.width)?"vml":"polygon";else if(n==="canvas")n=a.browser.msie?"vml":!a("<canvas />")[0].getContext?
"polygon":"canvas";else if(n==="polygon")n=a.browser.msie&&/center/i.test(g.mimic.string())?"vml":n;if(g.detectCorner()){g.create();g.detectColours();g.update();v.bind("tooltipmove.tip",k)}return g},detectCorner:function(){var j=p.corner,f=p.mimic||j,i=b.options.position.at,l=b.options.position.my;if(l.string)l=l.string();if(j===FALSE||l===FALSE&&i===FALSE)return FALSE;else{if(j===TRUE)g.corner=new a.fn.qtip.plugins.Corner(l);else if(!j.string)g.corner=new a.fn.qtip.plugins.Corner(j);if(f===TRUE)g.mimic=
new a.fn.qtip.plugins.Corner(l);else if(!f.string){g.mimic=new a.fn.qtip.plugins.Corner(f);g.mimic.precedance=g.corner.precedance}}return g.corner.string()!=="centercenter"},detectColours:function(){var j=o.tip,f=g.mimic[g.mimic.precedance],i="border-"+f+"-color";d.fill=j.css("background-color","").css("border","").css("background-color")||"transparent";d.border=j.get(0).style?j.get(0).style["border"+f.charAt(0)+f.substr(1)+"Color"]:j.css(i)||"transparent";if(/rgba?\(0, 0, 0(, 0)?\)|transparent/i.test(d.fill))d.fill=
q.css(e?"background-color":i);if(!d.border||/rgba?\(0, 0, 0(, 0)?\)|transparent/i.test(d.border))d.border=q.css(i)||d.fill;a("*",j).add(j).css("background-color","transparent").css("border",0)},create:function(){var j=u.width,f=u.height;o.tip&&o.tip.remove();o.tip=a('<div class="ui-tooltip-tip ui-widget-content"></div>').css(u).prependTo(v);switch(n){case "canvas":o.tip.append('<canvas height="'+f+'" width="'+j+'" />');break;case "vml":o.tip.html('<vml:shape coordorigin="0 0" coordsize="'+j+" "+f+
'" stroked="'+!!e+'"  style="behavior:url(#default#VML); display:inline-block; antialias:TRUE; position: absolute;  top:0; left:0; width:'+j+"px; height:"+f+"px; vertical-align:"+g.corner.y+';"><vml:stroke weight="'+(e-2)+'px" joinstyle="miter" miterlimit="10"  style="behavior:url(#default#VML); display:inline-block;" /></vml:shape>');break;case "polygon":o.tip.append('<div class="ui-tooltip-tip-inner" />').append(e?'<div class="ui-tooltip-tip-border" />':"");break}return g},update:function(j,f){var i=
o.tip,l=u.width,m=u.height,t=e>0?0:1,r=Math.ceil(e/2+0.5),x;f||(f=j?j:g.mimic);if(!j)j=g.corner;if(f.x==="false")f.x=j.x;if(f.y==="false")f.y=j.y;i=i.children();switch(n){case "canvas":i=i.get(0).getContext("2d");i.clearRect(0,0,3E3,3E3);i.restore();if(e){x=J(f.string(),l*2,m*2);i.strokeStyle=d.border;i.lineWidth=e+1;i.lineJoin="miter";i.miterLimit=100;i.save();i.translate(f.x==="left"?0:f.x==="right"?-l:-l/2,f.y==="top"?0:f.y==="bottom"?-m:-m/2)}else x=J(f.string(),l,m);i.fillStyle=d.fill;for(i.miterLimit=
0;t<2;t++){i.globalCompositeOperation=t&&e?"destination-in":"source-over";i.beginPath();i.moveTo(x[0][0],x[0][1]);i.lineTo(x[1][0],x[1][1]);i.lineTo(x[2][0],x[2][1]);i.closePath();i.fill();t||i.stroke()}break;case "vml":x=J(f.string(),l,m);t="m"+x[0][0]+","+x[0][1]+" l"+x[1][0]+","+x[1][1]+" "+x[2][0]+","+x[2][1]+" xe";i.attr({path:t,fillcolor:d.fill});if(e){i.children().attr("color",d.border);if(f.precedance==="y"){i.css("top",(f.y==="top"?1:-1)*(e-2));i.css("left",f.x==="left"?1:-2)}else{i.css("left",
(f.x==="left"?1:-1)*(e-2));i.css("top",f.y==="top"?1:-2)}}break;case "polygon":if(f.precedance==="y"){t=l>m?1.5:l<m?5:2.2;r=[f.x==="left"?r:f.x==="right"?-r:0,Math.floor(t*r*(f.y==="bottom"?-1:1)*(f.x==="center"?0.8:1))]}else{t=l<m?1.5:l>m?5:2.2;r=[Math.floor(t*r*(f.x==="right"?-1:1)*(f.y==="center"?0.9:1)),f.y==="top"?r:f.y==="bottom"?-r:0]}i.removeAttr("style").each(function(s){var y={x:f.precedance==="x"?f.x==="left"?"right":"left":f.x,y:f.precedance==="y"?f.y==="top"?"bottom":"top":f.y},z=f.x===
"center"?["left","right",y.y,m,l]:["top","bottom",y.x,l,m],A=d[!s&&e?"border":"fill"];s&&a(this).css({position:"absolute","z-index":1,left:r[0],top:r[1]});f.x==="center"||f.y==="center"?a(this).css("border-"+z[2],z[3]+"px solid "+A).css("border-"+z[0],Math.floor(z[4]/2)+"px dashed transparent").css("border-"+z[1],Math.floor(z[4]/2)+"px dashed transparent"):a(this).css("border-width",Math.floor(m/2)+"px "+Math.floor(l/2)+"px").css("border-"+y.x,Math.floor(l/2)+"px solid "+A).css("border-"+y.y,Math.floor(m/
2)+"px solid "+A)});break}c(j);return g},destroy:function(){o.tip&&o.tip.remove();v.unbind("tooltipmove.tip")}})}function P(b,c){var k=this,g=b.elements,p=g.tooltip;g.blanket=a("#qtip-blanket");a.extend(k,{init:function(){c=a.extend(TRUE,a.fn.qtip.plugins.modal.defaults,c);p.bind("tooltipshow.qtipmodal tooltiphide.qtipmodal",function(o,v,q){o=o.type.replace("tooltip","");a.isFunction(c[o])?c[o].call(g.blanket,q,v):k[o](q)});g.blanket.length||k.create();c.blur===TRUE&&g.blanket.bind("click.qtipmodal"+
b.id,function(){b.hide.call(b)})},create:function(){g.blanket=a("<div />",{id:"qtip-blanket",css:{position:"absolute",top:0,left:0,display:"none"}}).appendTo(document.body);a(B).bind("resize.qtipmodal",function(){g.blanket.css({height:Math.max(a(B).height(),a(document).height()),width:Math.max(a(B).width(),a(document).width())})}).trigger("resize")},show:function(o){g.blanket.fadeIn(o)},hide:function(o){g.blanket.fadeOut(o)},destroy:function(){var o=TRUE;a("*").each(function(){var v=a(this).data("qtip");
if(v&&v.id!==b.id&&v.options.show.modal)return o=FALSE});if(o){g.blanket.remove();a(B).unbind("scroll.qtipmodal resize.qtipmodal")}else g.blanket.unbind("click.qtipmodal"+b.id);p.unbind("tooltipshow.qtipmodal tooltiphide.qtipmodal")}});k.init()}function Q(b){var c=this,k=b.elements,g=k.tooltip,p=".bgiframe-"+b.id,o="tooltipmove"+p+" tooltipshow"+p;a.extend(c,{init:function(){k.bgiframe=a('<iframe class="ui-tooltip-bgiframe" frameborder="0" tabindex="-1" src="javascript:\'\';"  style="display:block; position:absolute; z-index:-1; filter:alpha(opacity=0);"></iframe>');
k.bgiframe.appendTo(g);g.bind(o,c.adjust)},adjust:function(){var v=b.calculate("dimensions"),q=b.plugins.tip,w=b.elements.tip,u;u=parseInt(g.css("border-left-width"),10);u={left:-u,top:-u};if(q&&w){q=q.corner.precedance==="x"?["width","left"]:["height","top"];u[q[1]]-=w[q[0]]()}k.bgiframe.css(u).css(v)},destroy:function(){c.iframe.remove();g.unbind(o)}});c.init()}a.fn.qtip=function(b,c,k){var g=String(b).toLowerCase(),p=FALSE,o=g==="disable"?[TRUE]:a.makeArray(arguments).splice(1),v=o[o.length-1],
q;if(!b&&a(this).data("qtip")||g==="api")return(q=a(this).eq(0).data("qtip"))?q.hash():E;else if("string"===typeof b){a(this).each(function(){var w=a(this).data("qtip");if(!w)return TRUE;if(/option|set/.test(g)&&c)if(k!==E)w.set(c,k);else p=w.get(c);else{if(!w.rendered&&(g==="show"||g==="toggle")){if(v.timeStamp)w.cache.event=v;w.render()}else if(g==="enable"){g="disable";o=[FALSE]}w[g]&&w[g].apply(w[g],o)}});return p!==FALSE?p:a(this)}else if("object"===typeof b){K(b);q=a.extend(TRUE,{},a.fn.qtip.defaults,
b);return a.fn.qtip.bind.call(this,q)}};a.fn.qtip.bind=function(b){return a(this).each(function(){function c(w){function u(){g.render(typeof w==="object"||p.show.ready);o.show.unbind(v.show);o.hide.unbind(v.hide)}if(g.cache.disabled)return FALSE;g.cache.event=a.extend({},w);if(p.show.delay>0){g.timers.show=setTimeout(u,p.show.delay);v.show!==v.hide&&o.hide.bind(v.hide,function(){clearTimeout(g.timers.show)})}else u()}var k,g,p,o,v,q;k=b.id===FALSE||b.id.length<1||a("#ui-tooltip-"+b.id).length?a.fn.qtip.nextid++:
b.id;q=".qtip-"+k+"-create";g=M.call(a(this),k,b);if(g===FALSE)return TRUE;p=g.options;a(this).attr("title")&&a(this).data("oldtitle",a(this).attr("title")).removeAttr("title");a.each(a.fn.qtip.plugins,function(){this.initialize==="initialize"&&this(g)});o={show:p.show.target,hide:p.hide.target};v={show:String(p.show.event).replace(" ",q+" ")+q,hide:String(p.hide.event).replace(" ",q+" ")+q};p.show.ready||p.prerender||p.show.event===FALSE?c():o.show.bind(v.show,c)})};a.each({attr:function(b){var c=
a(this).data("qtip");return arguments.length===1&&b==="title"&&c&&c.rendered===TRUE?a(this).data("oldtitle"):NULL},remove:a.ui?NULL:function(b,c){this.each(function(){if(!c)if(!b||a.filter(b,[this]).length)a("*",this).add(this).each(function(){a(this).triggerHandler("remove")})})}},function(b,c){if(!c)return true;var k=a.fn[b];a.fn[b]=function(){return c.apply(this,arguments)||k.apply(this,arguments)}});a.fn.qtip.nextid=0;a.fn.qtip.inactiveEvents="click dblclick mousedown mouseup mousemove mouseleave mouseenter".split(" ");
a.fn.qtip.zindex=15E3;a.fn.qtip.plugins={Corner:function(b){this.x=(String(b).replace(/middle/i,"center").match(/left|right|center/i)||["false"])[0].toLowerCase();this.y=(String(b).replace(/middle/i,"center").match(/top|bottom|center/i)||["false"])[0].toLowerCase();this.precedance=b.charAt(0).search(/^(t|b)/)>-1?"y":"x";this.string=function(){return this.precedance==="y"?this.y+this.x:this.x+this.y};this.abbreviation=function(){var c=this.x.substr(0,1),k=this.y.substr(0,1);return c===k?c:c==="c"||
c!=="c"&&k!=="c"?k+c:c+k}}};a.fn.qtip.defaults={prerender:FALSE,id:FALSE,overwrite:TRUE,metadata:{type:"class"},content:{text:TRUE,attr:"title",title:{text:FALSE,button:FALSE}},position:{my:"top left",at:"bottom right",target:FALSE,container:FALSE,adjust:{x:0,y:0,mouse:TRUE,screen:FALSE,resize:TRUE,effect:TRUE,offset:FALSE}},show:{target:FALSE,event:"mouseenter",effect:TRUE,delay:90,solo:FALSE,ready:FALSE},hide:{target:FALSE,event:"mouseleave",effect:TRUE,delay:0,fixed:FALSE,inactive:FALSE},style:{classes:"",
widget:TRUE},events:{render:a.noop,move:a.noop,show:a.noop,hide:a.noop,focus:a.noop,blur:a.noop}};var I=a();a.fn.qtip.plugins.ajax=function(b){var c=b.plugins.ajax,k=b.options.content;if(k.ajax&&k.ajax.url)if(c)return c;else{b.plugins.ajax=new N(b);return b.plugins.ajax}};a.fn.qtip.plugins.ajax.initialize="render";a.fn.qtip.plugins.ajax.sanitize=function(b){if(b.content!==E)if(b.content.ajax!==E){if(typeof b.content.ajax!=="object")b.content.ajax={url:b.content.ajax};if(b.content.text===FALSE)b.content.text=
"Loading...";b.content.ajax.once=Boolean(b.content.ajax.once);b.content.ajax.preload=Boolean(b.content.ajax.preload);if(b.content.ajax.preload){b=b.content.ajax.url;a("#qtip-preload").length||a('<div id="qtip-preload" class="ui-tooltip-accessible" />').appendTo(document.body);I.length||(I=a("<div />").appendTo("#qtip-preload").load(b+" img"))}}};a.fn.qtip.plugins.tip=function(b){var c=b.plugins.tip,k=b.options.style.tip;if(k&&k.corner)if(c)return c;else{b.plugins.tip=new O(b);b.plugins.tip.init();
return b.plugins.tip}};a.fn.qtip.plugins.tip.initialize="render";a.fn.qtip.plugins.tip.sanitize=function(b){if(b.style!==E&&b.style.tip!==E){if(typeof b.style.tip!=="object")b.style.tip={corner:b.style.tip};if(typeof b.style.tip.method!=="string")b.style.tip.method=TRUE;if(!/canvas|polygon/i.test(b.style.tip.method))b.style.tip.method=TRUE;if(typeof b.style.tip.width!=="number")b.style.tip.width=14;if(typeof b.style.tip.height!=="number")b.style.tip.height=14;if(typeof b.style.tip.border!=="number")b.style.tip.border=
0}};a.fn.qtip.plugins.imagemap=function(b,c){function k(d,e){for(var h=0,n=1,j=1,f=0,i=0,l=d.width,m=d.height;l>0&&m>0&&n>0&&j>0;){l=Math.floor(l/2);m=Math.floor(m/2);if(c.x==="left")n=l;else if(c.x==="right")n=d.width-l;else n+=Math.floor(l/2);if(c.y==="top")j=m;else if(c.y==="bottom")j=d.height-m;else j+=Math.floor(m/2);for(h=e.length;h--;){if(e.length<2)break;f=e[h][0]-d.offset.left;i=e[h][1]-d.offset.top;if(c.x==="left"&&f>=n||c.x==="right"&&f<=n||c.x==="center"&&(f<n||f>d.width-n)||c.y==="top"&&
i>=j||c.y==="bottom"&&i<=j||c.y==="center"&&(i<j||i>d.height-j))e.splice(h,1)}}return{left:e[0][0],top:e[0][1]}}var g=b.attr("shape").toLowerCase(),p=b.attr("coords").split(","),o=[],v=a('img[usemap="#'+b.parent("map").attr("name")+'"]').offset(),q={width:0,height:0,offset:{top:1E10,right:0,bottom:0,left:1E10}},w=0,u=0;if(g==="poly")for(w=p.length;w--;){u=[parseInt(p[--w],10),parseInt(p[w+1],10)];if(u[0]>q.offset.right)q.offset.right=u[0];if(u[0]<q.offset.left)q.offset.left=u[0];if(u[1]>q.offset.bottom)q.offset.bottom=
u[1];if(u[1]<q.offset.top)q.offset.top=u[1];o.push(u)}else o=a.map(p,function(d){return parseInt(d,10)});switch(g){case "rect":q={width:Math.abs(o[2]-o[0]),height:Math.abs(o[3]-o[1]),offset:{left:o[0],top:o[1]}};break;case "circle":q={width:o[2]+2,height:o[2]+2,offset:{left:o[0],top:o[1]}};break;case "poly":a.extend(q,{width:Math.abs(q.offset.right-q.offset.left),height:Math.abs(q.offset.bottom-q.offset.top)});q.offset=c.string()==="centercenter"?{left:q.offset.left+q.width/2,top:q.offset.top+q.height/
2}:k(q,o.slice());q.width=q.height=0;break}q.offset.left+=v.left;q.offset.top+=v.top;return q};a.fn.qtip.plugins.modal=function(b){var c=b.plugins.modal,k=b.options.show.modal;if(c)return c;else if(typeof k==="object"){b.plugins.modal=new P(b,k);return b.plugins.modal}};a.fn.qtip.plugins.modal.initialize="render";a.fn.qtip.plugins.modal.sanitize=function(b){if(b.show&&b.show.modal!==E)if(typeof b.show.modal!=="object")b.show.modal={}};a.fn.qtip.plugins.modal.defaults={effects:{show:TRUE,hide:TRUE},
blur:TRUE};a.fn.qtip.plugins.bgiframe=function(b){if(!(a.browser.msie&&/^6\.[0-9]/.test(a.browser.version)&&a("select, object").length))return FALSE;var c=b.plugins.bgiframe;if(c)return c;else{b.plugins.bgiframe=new Q(b);return b.plugins.bgiframe}};a.fn.qtip.plugins.bgiframe.initialize="render"})(jQuery,window);
