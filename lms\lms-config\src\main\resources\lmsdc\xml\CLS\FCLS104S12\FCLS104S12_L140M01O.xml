<?xml version="1.0"?>
<MsgRq>
  <Header>
    <Title>擔保品資料明細檔</Title>
    <Form>FCLS104S12</Form>
    <OutFile>FCLS104S12_L140M01O.txt</OutFile>
  </Header>
  <ItemName> 
    <oid          name=""   null="N"   type=""   default=""   num=""   desc="*oid"/>
    <mainId       name="LinkMainId"    null="N"   type=""   default=""   num=""   desc="文件編號"/>
    <seqNo        name="Coll_Seq"      null="N"   type=""   default=""   num=""   desc="序號"/>
    <branch       name="Branch_ID"     null=""  type=""  default="" num="" desc="分行代碼"/>
    <estBrn       name="tCmsBranch"    null=""  type=""  default="" num="" desc="鑑價分行"/>
    <estDate      name=""   null=""    type=""   default=""   num=""   desc="鑑估日期"/>
    <custId       name="Borrower_ID"   null=""  type=""  default="" num="" desc="統一編號"/>
    <dupNo        name=""   null=""    type=""   default=""   num=""   desc="重覆序號"/>
    <custName     name=""   null=""    type=""   default=""   num=""   desc="客戶名稱"/>
    <collNo       name="rty_id"        null=""  type=""  default="" num="" desc="擔保品編號"/>
    <collTyp1     name=""   null=""    type=""   default="01"   num=""   desc="擔保品大類"/>
    <collTyp2     name=""   null=""    type=""   default=""   num=""   desc="擔保品小類"/>
    <docStatus    name="cmsDocStatus"  null=""   type=""   default=""   num=""   desc="擔保品文件狀態"/>
    <collName     name=""   null=""    type=""   default=""   num=""   desc="擔保品名稱"/>
    <loanTwd      name="TotalLoanAmt"  null=""  type=""  default="" num="" desc="放款值（TWD）"/>
    <ttlNum       name="cmsDoor_1"     null=""  type=""  default="" num="" desc="已敘做放款總戶數"/>
    <ttlBal       name="cmsAmt_1"      null=""  type=""  default="" num="" desc="已敘做放款總餘額"/>
    <set1         name="set_1"         null=""  type="setIns"  default="" num="" desc="土地及建物擬/已設定"/>
    <setOdr       name="order"         null=""  type="numC2E"  default="" num="" desc="順位"/>
    <setAmt       name="s_amt"         null=""  type=""  default="" num="" desc="抵押金額"/>
    <priAmt       name="b_s_amt"       null=""  type=""  default="" num="" desc="前順位抵押金額"/>
    <totalLoanAmt name="TotalLoanAmt_1" null="" type=""  default="" num="" desc="押值"/>
    <ins1         name="ins_1"          null=""  type="setIns"  default="" num="" desc="建物擬/已投保"/>
    <insAmt1      name="ins_amt_1"      null=""  type=""  default="" num="" desc="火險金額"/>
    <insAmt2      name="ins_amt_2"      null=""  type=""  default="" num="" desc="地震險金額"/>
    <insId        name="Insurance_Code" null=""  type=""  default="" num="" desc="保險公司"/>
    <fireIns      name="fireIns"   null=""  type=""  default="" num="" desc="火險金額計算方式"/>
    <inAppMoney   name="in_app_money" null=""  type=""  default="" num="" desc="借款金額"/>
    <assTot       name="AssTot"       null=""  type=""  default="" num="" desc="土地放款值"/>
    <rebuildCost  name="rebuildCost"  null=""  type=""  default="" num="" desc="重置成本"/>
    <term1        name="term_1"       null=""  type=""  default="" num="" desc="擔保品係"/>
    <term2        name="term_2"       null=""  type="yn"  default="" num="" desc="有無加建未辦保存登記之建物"/>
    <addrAreaNum  name="AddAreaP_1"   null=""  type=""  default="" num="" desc="面積"/>
    <build        name="LArea"        null=""  type="build"       default="" num="" desc="建物地址"/>
    <areaDetail   name="LnArea_1"     null=""  type="areaDetail"  default="" num="" desc="土地地段"/>
    <lnNo         name=""             null=""  type=""  default="" num="" desc="地號"/>
    <taxNo        name="TaxNo"        null=""  type=""  default="" num="" desc="稅籍編號"/>
    <taxAddr      name="LArea"        null=""  type=""  default="" num="" desc="稅籍地址"/>
    <payPercent   name=""   null=""   type=""   default=""   num=""   desc="核貸成數"/>
    <cmsDesc      name=""   null=""   type=""   default=""   num=""   desc="擔保品內容"/>
    <inAmt        name="houseAmt"   null=""   type=""   default=""   num=""   desc="購價或時價"/>
    <houseYear    name=""   null=""   type=""   default=""   num=""   desc="屋齡"/>
    <cmsOid       name=""   null=""   type=""   default=""   num=""   desc="擔保品Oid"/>
    <creator     name="" null=""  type="today"    default="" num="" desc="建立人員號碼"/>
    <createTime  name="" null=""  type="sysdate"  default="" num="" desc="建立日期"/>
    <updater     name="" null=""  type="today"    default="" num="" desc="異動人員號碼"/>
    <updateTime  name="" null=""  type="sysdate"  default="" num="" desc="異動日期"/>
    <setordStr   name="" null=""  type=""   default=""   num=""   desc="設定順位文字"/>
  </ItemName>
    <Occurs> 
        <OccursTimes>4</OccursTimes>
        <OccursFields>Sno_;fireIns</OccursFields>
        <OccursKeys>Sno_</OccursKeys>
        <OccursValues>notSpace</OccursValues>
    </Occurs>
</MsgRq>
