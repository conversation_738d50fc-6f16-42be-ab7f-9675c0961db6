package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Timestamp;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisELF447Service;
import com.mega.sso.model.IBranch;

@Service
public class MisELF447ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF447Service {

	@Override
	public List<Map<String, Object>> findElf447forNewReportType5ByBrNo(
			List<IBranch> ovUnitNo, String benDate, String endDate,
			String otherCondition) {
		List<Object> params = new ArrayList<Object>();
		params.add(benDate);
		params.add(endDate);

		String branchParams = "";
		if (ovUnitNo != null) {
			branchParams = Util.genSqlParam(ovUnitNo);
			for (IBranch iBranch : ovUnitNo) {
				params.add(Util.trim(iBranch.getBrNo()));
			}
		}

		return this.getJdbc().queryForListByCustParam("Elf447.selByUniqueKey",
				new Object[] { branchParams, otherCondition },
				params.toArray(new Object[0]));

	}

	@Override
	public void insertElf447(String staffNo, Date approveTime, String staffJob,
			String caseBrid, String caseBrid2, String custId, String dupNo,
			String cntrNo, String mainId, String caseLvl, String property,
			Timestamp upTime, double currentApplyAmt, String tCaseNo,
			String LoanTotCurr, double LoanTotAmt, String assureTotCurr,
			double assureTotAmt, String LoanTotZCurr, double LoanTotZAmt,
			String LoanTotLCurr, double LoanTotLAmt, String LVCurr,
			double LVAmt, String currentApplyCurr, String liHaiBank,
			String liHai44, String liHai45, String sysNo) {
		this.getJdbc().update(
				"ELF447.insert",
				new Object[] { staffNo, approveTime, staffJob, caseBrid2,
						custId, dupNo, cntrNo, caseBrid, sysNo, mainId,
						caseLvl, property, upTime, currentApplyAmt, tCaseNo,
						LoanTotCurr, LoanTotAmt, assureTotCurr, assureTotAmt,
						LoanTotZCurr, LoanTotZAmt, LoanTotLCurr, LoanTotLAmt,
						LVCurr, LVAmt, currentApplyCurr, liHaiBank, liHai44,
						liHai45 });
	}

	@Override
	public void insertELF4472(List<Object[]> dataList,List<Object[]> delteList) {
		this.getJdbc().batchUpdate("ELF447.delete",
				new int[] { Types.CHAR },
				delteList);
		this.getJdbc().batchUpdate(
				"ELF447.insert",
				new int[] { Types.CHAR, Types.DATE, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.TIMESTAMP,
						Types.DECIMAL, Types.CHAR, Types.CHAR, Types.DECIMAL,
						Types.CHAR, Types.DECIMAL, Types.CHAR, Types.DECIMAL,
						Types.CHAR, Types.DECIMAL, Types.CHAR, Types.DECIMAL,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR }, dataList);
	}

	@Override
	public void upDateElf447(String staffNo, Date approveTime, String staffJob,
			String caseBrid, String caseBrid2, String custId, String dupNo,
			String cntrNo, String mainId, String caseLvl, String property,
			double currentApplyAmt, String tCaseNo, String LoanTotCurr,
			double LoanTotAmt, String assureTotCurr, double assureTotAmt,
			String LoanTotZCurr, double LoanTotZAmt, String LoanTotLCurr,
			double LoanTotLAmt, String LVCurr, double LVAmt,
			String currentApplyCurr, String liHaiBank, String liHai44,
			String liHai45, String sysNo) {
		this.getJdbc().update(
				"ELF447.update",
				new Object[] { staffNo, approveTime, staffJob, caseBrid2,
						custId, dupNo, cntrNo, caseBrid, sysNo, mainId,
						caseLvl, property, currentApplyAmt, tCaseNo,
						LoanTotCurr, LoanTotAmt, assureTotCurr, assureTotAmt,
						LoanTotZCurr, LoanTotZAmt, LoanTotLCurr, LoanTotLAmt,
						LVCurr, LVAmt, currentApplyCurr, liHaiBank, liHai44,
						liHai45, staffNo, approveTime, staffJob, custId, dupNo,
						cntrNo, sysNo });
	}

	@Override
	public void delElf447(String mainId) {
		this.getJdbc().update("ELF447.delete", new Object[] { mainId });
	}

	public void delByUNID(String UNID, String BRNO) {
		this.getJdbc().update("ELF447.delByUNIDAndBranch",
				new Object[] { UNID, BRNO });
	}
	
	@Override
	public int findByUnid(String mainId){
		 Map<String, Object> data = this.getJdbc().queryForMap("ELF447.findByUnid", new String[]{mainId});
		 return (Integer) data.get("COUNT");
	}
	
	@Override
	public String findProperty1UnidByCntrNo(String cntrNo){
		Map<String, Object> data = this.getJdbc().queryForMap("ELF447.findProperty1UnidByCntrNo", new String[]{cntrNo});
		if(MapUtils.isNotEmpty(data)){
			return Util.trim(data.get("ELF447_UNID"));	
		}
		return "";
	}
	
	@Override
	public List<Map<String, Object>> findForC241M01A(String unid){
		return this.getJdbc().queryForListWithMax("ELF447.findForC241M01A", new String[]{unid});
	}
	
	@Override
	public Map<String, Object> findLandOrConstructionLoanContrNo(String cntrNo){
		return this.getJdbc().queryForMap("ELF447.findLandOrConstructionLoanContrNo", new String[]{ cntrNo });
	}
}
