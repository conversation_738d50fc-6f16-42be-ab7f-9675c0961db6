/**
 * 約據書 開發信用狀約定書
 * @since
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
var initDfd = $.Deferred();
var inits = {
    fhandle: "lms9990m01formhandler",
    queryAction: "queryL999m01aM09",
    saveAction: "saveL999m01aM09",
    contractType: "07"//約據書種類
};
var LMS999Action = {
    ActionSFormId: "#ActionSForm",
    ActionMFormId: "#ActionMForm",
    save: function(showMsg, tofn){
        var $ActionSForm = $(LMS999Action.ActionSFormId);
        var $ActionMForm = $(LMS999Action.ActionMFormId);
        if ($ActionSForm.valid() && $ActionMForm.valid()) {
            var startDate = "";
            var endDate = "";
			if ($("#useSDateY").val() && $("#useSDateM").val() && $("#useSDateD").val()) {
				if($("#useSDateY").val() == '' && $("#useSDateM").val() == '' && $("#useSDateD").val() == ''){
	            
				}else{
					startDate = $("#useSDateY").val() + "-" + $("#useSDateM").val() + "-" + $("#useSDateD").val();
					if (!API.twDate(startDate)) {
		                return CommonAPI.showErrorMessage(i18n.lms9990M09['L999M01AM09.message02']);
		            }
				}
			}
			if ($("#useEDateY").val() && $("#useEDateM").val() && $("#useEDateD").val()) {
				if($("#useEDateY").val() == '' && $("#useEDateM").val() == '' && $("#useEDateD").val() == ''){
	            
				}else{
					endDate = $("#useEDateY").val() + "-" + $("#useEDateM").val() + "-" + $("#useEDateD").val();
					if (!API.twDate(endDate)) {
		                return CommonAPI.showErrorMessage(i18n.lms9990M09['L999M01AM09.message03']);
		            }
				}
			}
			
            
            $.ajax({
                async: false,
                handler: inits.fhandle,
                action: inits.saveAction,//saveL999m01aM09
                data: {
                    showMsg: showMsg,
                    contractType: inits.contractType,//約據書種類
                    ActionMForm: JSON.stringify($ActionMForm.serializeData()),
                    ActionSForm: JSON.stringify($ActionSForm.serializeData())
                },
            }).done(function(responseData){
				// $ActionMForm.injectData(responseData);
				//$ActionSForm.injectData(responseData);
				if (tofn) {
				    tofn();
				}				
			});
        }
    },
    print: function(){
        $.capFileDownload({
            handler: "lmsdownloadformhandler",
            data: {
                mainId: responseJSON.mainId,
                contractType: responseJSON.contractType,
                fileDownloadName: "LMS9990W07.doc",
                serviceName: "lms9990doc01service"
            }
        });
    },
    //驗證readOnly狀態
    checkReadonly: function(){
        var auth = (responseJSON ? responseJSON.Auth : {}); //權限
        if (auth.readOnly) {
            return true;
        }
        return false;
    }
};

/**
 *tempSave
 */
$.extend(window.tempSave, {
    handler: inits.fhandle,
    action: "tempsaveL999m01aM09",
    beforeCheck: function(){
        return $(LMS999Action.ActionSFormId).valid() && $(LMS999Action.ActionMFormId).valid();
    },
    sendData: function(){
        var $ActionSForm = $(LMS999Action.ActionSFormId);
        var $ActionMForm = $(LMS999Action.ActionMFormId);
        return {
            contractType: inits.contractType,//約據書種類
            ActionMForm: JSON.stringify($ActionMForm.serializeData()),
            ActionSForm: JSON.stringify($ActionSForm.serializeData())
        }
    }
});

$(function() {
    $.form.init({
        formHandler: inits.fhandle,
        formPostData: {
            formAction: inits.queryAction,
            srcMainId: responseJSON.srcMainId
        },
        loadSuccess: function(json){
            initDfd.resolve(json);
        }
    });
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(){
        LMS999Action.save(true);
    }).end().find("#btnPrint").click(function(){
        if (LMS999Action.checkReadonly()) {
            LMS999Action.print();
        } else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    LMS999Action.save(false, LMS999Action.print);
                }
            });
        }
    });
});

function addommon(obj,len){
	if(obj.value != ''){
		obj.value = util.addZeroBefore(obj.value,len);
	}else{
		obj.value = '';
	}
//	if($("input[name=" + name + "]").val() != ''){
//		$("input[name=" + name + "]").val(util.addZeroBefore($("input[name=" + name + "]").val(), len));
//	}else{
//		$("input[name=" + name + "]").val('');
//	}
}



