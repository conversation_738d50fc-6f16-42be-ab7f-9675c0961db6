/* 
 *MisMISLN20ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.MISLN20;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;

/**
 * <pre>
 * 授信額度檔(昨日) MisMISLN20
 * </pre>
 * 
 * @since 2012/11/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/19,REX,new
 *          </ul>
 */
@Service
public class MisMISLN20ServiceImpl extends AbstractMFAloanJdbc implements
		MisMISLN20Service {

	@Override
	public String findFactType(String custId, String dupNo, String cntrNo) {
		String result = "";
		Map<String, Object> data = this.getJdbc().queryForMap(
				"MISMISLN20.getLNF020_FACT_TYPE",
				new Object[] { cntrNo,
						Util.addSpaceWithValue(custId, 10) + dupNo });

		if (data != null) {
			result = Util.trim(data.get("LNF020_FACT_TYPE"));
		}

		return result;
	}

	@Override
	public List<MISLN20> findByCustId(String custId, String dupNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"MIS.MISLN20_findByCustId",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo });
		List<MISLN20> list = new ArrayList<MISLN20>();
		for (Map<String, Object> row : rowData) {
			MISLN20 model = new MISLN20();
			DataParse.map2Bean(row, model);
			list.add(model);
		}

		return list;
	}

	@Override
	public List<MISLN20> findByCustIdIsCanCel(String custId, String dupNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"MIS.MISLN20_findByCustIdIsCanCel",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo });
		List<MISLN20> list = new ArrayList<MISLN20>();
		for (Map<String, Object> row : rowData) {
			MISLN20 model = new MISLN20();
			DataParse.map2Bean(row, model);
			list.add(model);
		}

		return list;
	}

	@Override
	public Map<String, Object> findSumBycntrNo(String cntrNo) {
		return this.getJdbc().queryForMap("LN.LNF022SumByCntrNo",
				new Object[] { cntrNo });
	}

	@Override
	public MISLN20 findByKey(String custId, String dupNo, String cntrNo) {
		Map<String, Object> rowData = this.getJdbc().queryForMap(
				"MIS.MISLN20_findByCustIdAndCntrNo",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo,
						cntrNo });
		if (rowData == null) {
			return null;
		} else {
			MISLN20 model = new MISLN20();
			DataParse.map2Bean(rowData, model);
			return model;
		}
	}

	@Override
	public Map<String, Object> findByCustIdCntrNo(String custId, String dupNo,
			String cntrNo) {
		return this.getJdbc().queryForMap(
				"MIS.MISLN20.getByCustIdDupNoCntrNo",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo,
						cntrNo });
	}

	@Override
	public List<MISLN20> findByCustIdCntrNoList(String custId, String dupNo,
			String cntrNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"MIS.MISLN20_findByCustIdAndCntrNo",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo,
						cntrNo });

		List<MISLN20> list = new ArrayList<MISLN20>();
		for (Map<String, Object> row : rowData) {
			MISLN20 model = new MISLN20();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public HashMap<String, String> findCancelCntrNoByCustId(
			HashSet<String> custIdSet) {
		String custIdParams = Util.genSqlParam(custIdSet.toArray(new String[0]));
//		StringBuffer custString = new StringBuffer();
//		for (String key : custIdSet) {
//			custString.append(custString.length() > 0 ? "," : "");
//			custString.append("'");
//			custString.append(key);
//			custString.append("'");
//		}

		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForAllListByCustParam(
						"LNF020.findCancelCntrNo",
						new Object[] { custIdParams }, custIdSet.toArray(new String[0]));
		HashMap<String, String> cancelCntrNo = new HashMap<String, String>();
		for (Map<String, Object> row : rowData) {
			String cntrNo = Util.trim(row.get("LNF020_CONTRACT"));
			cancelCntrNo.put(cntrNo, "");
		}
		return cancelCntrNo;
	}

	@Override
	public List<Map<String, Object>> findGetL140M01E(String cntrNo) {
		return this.getJdbc().queryForListWithMax("MIS.findL140M01EInfo",
				new Object[] { cntrNo });
	}

	@Override
	public List<MISLN20> findByGrpCntrNoIsCanCel(String grpCntrNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"MIS.MISLN20_findByGrpCntrNoIsCanCel",
				new Object[] { grpCntrNo });
		List<MISLN20> list = new ArrayList<MISLN20>();
		for (Map<String, Object> row : rowData) {
			MISLN20 model = new MISLN20();
			DataParse.map2Bean(row, model);
			list.add(model);
		}

		return list;
	}

	@Override
	public List<Map<String, Object>> findLNF660() {
		return this.getJdbc().queryForListWithMax("MIS.MISLN20_findLNF660",
				null);
	}

	@Override
	public List<Map<String, Object>> findLNF034_H(String contract_M,
			String lnf034_CP_BANK_CD, String sDate, String gDate) {
		if (sDate == null || gDate == null) {
			return new ArrayList<Map<String, Object>>();
		}
		return this.getJdbc().queryForListWithMax("MIS.MISLN20_findLNF034_H",
				new String[] { contract_M, lnf034_CP_BANK_CD, sDate, gDate });
	}

	@Override
	public List<Map<String, Object>> findLNF034_OTHER(String contract_M,
			String brNo, String sDate, String gDate) {
		if (sDate == null || gDate == null) {
			return new ArrayList<Map<String, Object>>();
		}
		return this.getJdbc().queryForListWithMax(
				"MIS.MISLN20_findLNF034_OTHER",
				new String[] { contract_M, brNo + "%", sDate, gDate });

	}

	@Override
	public List<Map<String, Object>> findLNF034_H_lcNo(String contract_M,
			String lnf034_CP_BANK_CD, String lcNo) {
		return this.getJdbc().queryForListWithMax(
				"MIS.MISLN20_findLNF034_H_LCNO",
				new String[] { contract_M, lnf034_CP_BANK_CD, lcNo });
	}

	@Override
	public List<Map<String, Object>> findLNF034_OTHER_lcNo(String contract_M,
			String brNo, String lcNo) {
		return this.getJdbc().queryForListWithMax(
				"MIS.MISLN20_findLNF034_OTHER_LCNO",
				new String[] { contract_M, brNo + "%", lcNo });

	}

	@Override
	public List<Map<String, Object>> findLNF034_H_CNT(String lnf660_m_contract,
			String date_s, String date_e) {
		return this.getJdbc().queryForListWithMax(
				"MIS.MISLN20_findLNF034_H_CNT",
				new String[] { lnf660_m_contract, date_s, date_e });
	}

	@Override
	public List<Map<String, Object>> findLNF034_OTHER_CNT(
			String lnf660_m_contract, String date_s, String date_e) {
		return this.getJdbc().queryForListWithMax(
				"MIS.MISLN20_findLNF034_OTHER_CNT",
				new String[] { lnf660_m_contract, date_s, date_e });
	}

	@Override
	/** 參考是否改用    public List<MISLN20> findByCustId(String custId, String dupNo) 會更合理
	 */
	public List<Map<String, Object>> findByCustIdDupNoIncludeCancel(
			String lnf020_cust_id) {
		return this.getJdbc().queryForListWithMax(
				"MIS.MISLN20_findByCustIdDupNoIncludeCancel",
				new String[] { lnf020_cust_id });
	}

	@Override
	public List<Map<String, Object>> findCancelCntrNoByCustId(String custId,
			String dupNo) {
		// 借款人=CUSTID ,帳號=LOANNO 利率= CODE 目前餘額：= BAL 額度=AMT:OLNAPPDATE:原貸放日期
		// :OLNENDDATE:原貸款到期日
		return this.getJdbc().queryForListWithMax(
				"MIS.MISLN20_findByCustIdIsCanCel",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo });
	}

	@Override
	public List<MISLN20> queryCancelCntrnoByCntrno(String cntrNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"LN.queryCancelCntrnoByCntrno", new Object[] { cntrNo });
		List<MISLN20> list = new ArrayList<MISLN20>();
		for (Map<String, Object> row : rowData) {
			MISLN20 model = new MISLN20();
			DataParse.map2Bean(row, model);
			list.add(model);
		}

		return list;
	}

	@Override
	public List<MISLN20> query_LNF020_DOCUMENT_NO(String lnf020_document_no) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"LNF020.findBy_LNF020_DOCUMENT_NO",
				new Object[] { lnf020_document_no });
		List<MISLN20> list = new ArrayList<MISLN20>();
		for (Map<String, Object> row : rowData) {
			MISLN20 model = new MISLN20();
			DataParse.map2Bean(row, model);
			list.add(model);
		}

		return list;
	}

	@Override
	public String findProjClass(String custId, String dupNo, String cntrNo) {
		String result = "";
		// LNF020_PROJ_CLASS
		Map<String, Object> data = this.getJdbc().queryForMap(
				"MISMISLN20.getLNF020_PROJ_CLASS",
				new Object[] { cntrNo,
						Util.addSpaceWithValue(custId, 10) + dupNo });

		if (data != null) {
			result = Util.trim(data.get("LNF020_PROJ_CLASS"));
		}

		return result;
	}

	@Override
	public Map<String, Object> findByCntrNo(String cntrNo) {
		return this.getJdbc().queryForMap("MIS.MISLN20.getByCntrNo",
				new Object[] { cntrNo });
	}

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 * 
	 * J-108-0028_05097_B1001 Web
	 * e-Loan國內企金授信覆審，價金履約保證額度序號為918起頭者，其覆審名單由敘做分行或管理行之所屬營運中心或自辦覆審分行辦理覆審。
	 * 
	 * @param brNo
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findCtlTypeByBrNoAndCustId(String brNo,
			String custId, String dupNo) {
		// 918 的 C類價金履約保證只判斷統編，不判斷分行
		String fullCustId = Util.addSpaceWithValue(custId, 10) + dupNo;
		return this.getJdbc().queryForMap(
				"LN.LNF020.GetCtlTypeByBrNoAndCustId",
				new Object[] { brNo, fullCustId, brNo, fullCustId, brNo,
						fullCustId, fullCustId, brNo });
	}

	/**
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 * 
	 * @param endDate
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findIsNewCust(String custId, String dupNo,
			String minDate, String maxDate) {
		String fullCustId = Util.addSpaceWithValue(custId, 10) + dupNo;

		// return this.getJdbc().queryForListWithMax(
		// "LNF020.selIsNewCust",
		// new Object[] { fullCustId, minDate, maxDate, minDate, maxDate,
		// minDate, maxDate, minDate, fullCustId, fullCustId });

		return this.getJdbc().queryForListWithMax(
				"LNF020.selIsNewCust",
				new Object[] { fullCustId, minDate, maxDate, minDate, maxDate,
						minDate, maxDate, minDate });

	}

	/**
	 * J-108-0078_05097_B1001
	 * 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
	 * 
	 * @param endDate
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findIsNewCustForCTL(String custId,
			String dupNo, String maxDate) {
		String fullCustId = Util.addSpaceWithValue(custId, 10) + dupNo;
		return this.getJdbc().queryForListWithMax("LNF020.selIsNewCustForCTL",
				new Object[] { fullCustId, maxDate, maxDate });

	}

	/**
	 * J-108-0217_05097_B1001 Web e-Loan國內企金授信額度明細表配合增加特定金錢信託受益權自行設質擔保授信專案種類
	 * 
	 * @param custId
	 * @param dupNo
	 * @param projClass
	 * @return
	 */
	public List<Map<String, Object>> findByProjClassAndCustId(String custId,
			String dupNo, String projClass) {
		String fullCustId = Util.addSpaceWithValue(custId, 10) + dupNo;
		return this.getJdbc().queryForListWithMax(
				"LNF020.selByProjClassAndCustId",
				new Object[] { fullCustId, projClass });
	}

//	@Override
//	public HashMap<String, String> findCntrNoByCustId(String custId) {
//		StringBuffer custString = new StringBuffer();
//		custString.append("'");
//		custString.append(custId);
//		custString.append("'");
//
//		List<Map<String, Object>> rowData = this.getJdbc()
//				.queryForAllListByCustParam(
//						"LNF020.findCntrNoByCustID",
//						new Object[] { custString.toString(),
//								custString.toString() }, new Object[] {});
//		HashMap<String, String> CntrNo = new HashMap<String, String>();
//		for (Map<String, Object> row : rowData) {
//			String cntrNo = Util.trim(row.get("LNF020_CONTRACT"));
//			CntrNo.put(cntrNo, "");
//		}
//		return CntrNo;
//	}

	/**
	 * J-110-0304_05097_B1007 Web e-Loan授信覆審配合RPA作業修改
	 * 
	 * 覆審RPA時判斷主借款人於該分行下有無有效額度
	 * 
	 * @param brNo
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findRetrialEffectCust(String brNo,
			String custId, String dupNo) {

		String fullCustId = Util.addSpaceWithValue(custId, 10) + dupNo;

		return this.getJdbc().queryForListWithMax("LNF020.isRetrialEffectCust",
				new Object[] { brNo, fullCustId, brNo, fullCustId });

	}

	@Override
	public List<MISLN20> findByCustId_R3R4(String custId, String dupNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"MIS.MISLN20_findByCustId_R3R4",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo });
		List<MISLN20> list = new ArrayList<MISLN20>();
		for (Map<String, Object> row : rowData) {
			MISLN20 model = new MISLN20();
			DataParse.map2Bean(row, model);
			list.add(model);
		}

		return list;
	}
	
	@Override
	public List<Map<String, Object>> findByUncanceledAccountCase(List<String> custIdDupNoList) {
		
		if(custIdDupNoList == null || custIdDupNoList.isEmpty()){
			return null;
		}
		
		int size = custIdDupNoList.size();
		String questionMarkStr = StringUtils.repeat("?,", size);
		questionMarkStr = questionMarkStr.substring(0, questionMarkStr.length()-1);
		Object[] params = new Object[size];
		
		for(int i=0; i<size; i++){
			params[i] = custIdDupNoList.get(i);
		}
		
		return this.getJdbc().queryForAllListByCustParam("LNF020.findByUncanceledAccountCase", new Object[] { questionMarkStr }, params);
	}

	@Override
	public List<MISLN20> findByCustIdAndContract(String custId, String dupNo, String cntrNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"MIS.LNF020.findByCustIdAndContract",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo,
						cntrNo });

		List<MISLN20> list = new ArrayList<MISLN20>();
		for (Map<String, Object> row : rowData) {
			MISLN20 model = new MISLN20();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
}
