/* 
 * PTEMAPPN.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;


import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.GenericBean;


/** 更新團貸年度總額度檔、上傳團貸額度明細檔 **/
public class PTEMAPPN extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Column(name="BRANCH", length=3, columnDefinition="CHAR(03)",unique = true)
	private String branch;

	/** 
	 * 公司統一編號<p/>
	 * 公司統一編號
	 */
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)",unique = true)
	private String custid;

	/** 重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(01)",unique = true)
	private String dupno;

	/** 
	 * 年度<p/>
	 * 年度
	 */
	@Column(name="YEAR", length=3, columnDefinition="CHAR(3)")
	private String year;

	/** 
	 * 額度申請批號<p/>
	 * 額度申請序號
	 */
	@Column(name="AMTAPPNO", length=4, columnDefinition="CHAR(4)")
	private String amtappno;

	/** 
	 * 資料修改人(行員代號)<p/>
	 * 資料修改人(行員代號)
	 */
	@Column(name="UPDATER", length=5, columnDefinition="CHAR(05)")
	private String updater;

	/** 
	 * 資料修改日期<p/>
	 * 資料修改日期
	 */
	@Column(name="TMESTAMP", columnDefinition="TIMESTAMP")
	private Date tmestamp;

	/** 取得分行別 **/
	public String getBranch() {
		return this.branch;
	}
	/** 設定分行別 **/
	public void setBranch(String value) {
		this.branch = value;
	}

	/** 
	 * 取得公司統一編號<p/>
	 * 公司統一編號
	 */
	public String getCustid() {
		return this.custid;
	}
	/**
	 *  設定公司統一編號<p/>
	 *  公司統一編號
	 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 
	 * 取得年度<p/>
	 * 年度
	 */
	public String getYear() {
		return this.year;
	}
	/**
	 *  設定年度<p/>
	 *  年度
	 **/
	public void setYear(String value) {
		this.year = value;
	}

	/** 
	 * 取得額度申請批號<p/>
	 * 額度申請序號
	 */
	public String getAmtappno() {
		return this.amtappno;
	}
	/**
	 *  設定額度申請批號<p/>
	 *  額度申請序號
	 **/
	public void setAmtappno(String value) {
		this.amtappno = value;
	}

	/** 
	 * 取得資料修改人(行員代號)<p/>
	 * 資料修改人(行員代號)
	 */
	public String getUpdater() {
		return this.updater;
	}
	/**
	 *  設定資料修改人(行員代號)<p/>
	 *  資料修改人(行員代號)
	 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 
	 * 取得資料修改日期<p/>
	 * 資料修改日期
	 */
	public Date getTmestamp() {
		return this.tmestamp;
	}
	/**
	 *  設定資料修改日期<p/>
	 *  資料修改日期
	 **/
	public void setTmestamp(Date value) {
		this.tmestamp = value;
	}
}
