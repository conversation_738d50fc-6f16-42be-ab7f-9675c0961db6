var oldOid = "";
var _handler = "";

initDfd.done(function() {	
	A_1_8_1();
	A_1_8_2();
	gridviewCust();
	$("#formL120s04b").find("#grpGrrd").change(function(i){
		var $formL120s04b = $("#formL120s04b");
		var grpNo = $("#formL120s04b").find("#grpNo").html();
		var grpGrrd = $(this).val();

		//J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。			
		var grpYear =  $formL120s04b.find("#grpYear").html();
		 
		if(grpYear){
			//判斷2017以後為新版，之前為舊版
			if(parseInt(grpYear, 10) >= 2017){
				// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
				if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5" || grpGrrd == "6" || grpGrrd == "7"){
					// 顯示屬主要集團企業...
					$formL120s04b.find(".spectialHide").show();
				}else{
					// 隱藏屬主要集團企業...
					$formL120s04b.find(".spectialHide").hide();
				}			
			}else{
				// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
				if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5"){
					// 顯示屬主要集團企業...
					$formL120s04b.find(".spectialHide").show();
				}else{
					// 隱藏屬主要集團企業...
					$formL120s04b.find(".spectialHide").hide();
				}			
			}
			
			
		}else{
			//如果沒有評等年度，以舊版執行
			// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
			if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5"){
				// 顯示屬主要集團企業...
				$formL120s04b.find(".spectialHide").show();
			}else{
				// 隱藏屬主要集團企業...
				$formL120s04b.find(".spectialHide").hide();
			}			
		}
		
	});	

	$("#buttonSearch03").click(function(){
		if($("#formAdd").valid()){
	        $.ajax({
	            type: "POST",
	            handler: responseJSON["handler"],
	            data: $.extend({}, getKeyCustIdDupNoItem(), {
	                formAction: "getCustData",
	                custId: $("#formAdd").find("#searchId03").val()
	            }),
	            success: function(responseData03){
	                // alert(JSON.stringify(responseData));
				      var selJson03 = {
					       		item : responseData03.selCus,
					       		format : "{value} - {key}",
					       		space: true
					       	};
				      $("#selCus03").setItems(selJson03);				  
				      $("#showSel03").show();
	            }
	        });						
		}
	});
	//上傳檔案按鈕
	$("#uploadComFile").click(function(){
		var limitFileSize=3145728;		
		MegaApi.uploadDialog({
			fieldId: get_uploadfile_fieldId(),
            fieldIdHtml:"size='30'",
            fileDescId:"fileDesc",
            fileDescHtml:"size='30' maxlength='30'",
			subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
			limitSize:limitFileSize,
            width:320,
            height:190,			
			data:$.extend({}, getKeyCustIdDupNoItem(), {
				mainId:responseJSON.mainId
			}),
			success : function(obj) {
				$("#gridviewPare").trigger("reloadGrid");
			}
	   });
	});	
	//刪除檔案按鈕
	$("#deleteComFile").click(function(){
		var select  = $("#gridviewPare").getGridParam('selrow');		
		// confirmDelete=是否確定刪除?
		CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
			if(b){				
				var data = $("#gridviewPare").getRowData(select);
				if(data.oid == "" || data.oid == undefined || data.oid == null){		
					// TMMDeleteError=請先選擇需修改(刪除)之資料列
					CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
					return;
				}				
				$.ajax({
					handler : "cls1141m01formhandler",
					type : "POST",
					dataType : "json",
					data : $.extend({}, getKeyCustIdDupNoItem(), {
						formAction : "deleteUploadFile",
						fileOid:data.oid
					}),
					success : function(obj) {
						$("#gridviewPare").trigger("reloadGrid");
					}
				});
			}else{
				return ;
			}
		});
	});		
	gridviewPare();
});

// 設定附加檔案Grid
function gridviewPare(){
	//檔案上傳grid
	$("#gridviewPare").iGrid({
		handler : 'cls1141gridhandler',
		height : 50,
		sortname : 'srcFileName',
		postData : $.extend({}, getKeyCustIdDupNoItem(), {
			formAction : "queryfile",
			fieldId:get_uploadfile_fieldId(),
			mainId:responseJSON.mainId,
			needCngName : true
		}),
		rowNum : 15,
		caption: "&nbsp;",
		hiddengrid : false,
		//expandOnLoad : true,	//只對subgrid有用
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lmscommom['other.msg152'],//other.msg152=報表名稱
			name : 'srcFileName',
			width : 120,
			align: "left",
			sortable : false,
			formatter : 'click',
			onclick : openFile
		}, {
			colHeader : i18n.lmscommom['other.msg176'],//other.msg176=上傳時間
			name : 'uploadTime',
			width : 140,
			sortable : false
		}, {
			name : 'oid',
			hidden : true
		}]
	});		
}

function openFile(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}

function gridviewCust(){
	var gridView = $("#gridviewCust").iGrid({
		handler: 'cls1141gridhandler',
		//height: 345, //for 15 筆
		height: "230px", //for 10 筆
		//autoHeight: true,
		width: "100%",
		sortname : 'custName',
		postData : $.extend({}, getKeyCustIdDupNoItem(), {
			formAction : "queryL120s01aById",
			custId : $("#custSearch").find("#searchId").val(),
			rowNum:10
		}),
		caption: "&nbsp;",
		hiddengrid : false,
		// autofit: false,
		autowidth:true,
		colModel: [{
			  colHeader: i18n.clss07a["L1205S07.grida"],//"借款人名稱"
			  name: 'custName',
			  width: 100,
			  sortable: true
		},{
		  name: 'oid',
		  hidden: true
		}],
			ondblClickRow: function(rowid){
		}
	});
}

function A_1_8_1() {
	var gridA181 = $("#gridview_A-1-8-1")
			.iGrid({
				needPager: false,
				handler : 'cls1141gridhandler',
				// height: 345, //for 15 筆
				height : "300px", // for 10 筆
				// autoHeight: true,
				width : "100%",
				postData : $.extend({}, getKeyCustIdDupNoItem(), {
					formAction : "queryL120s04a",
					mainId : responseJSON.mainid
					//rowNum:30
				}),
				//rowNum:30,
				caption: "&nbsp;",
				hiddengrid : false,
				sortname : 'custRelation|profit|custId',
				sortorder:'asc|desc|asc',
				//sortname : 'custRelation',
				multiselect: true,
				//rownumbers : true,
				hideMultiselect:false,
				// autofit: false,
				autowidth : true,
				colModel : [ {
					colHeader : i18n.clss07a["L1205S07.grid15"],//"需列印"
					name : 'prtFlag',
					align : "center",
					width : 40,
					sortable : false
				}, {
					colHeader : i18n.clss07a["L1205S07.grid16"],//"關係戶統編"
					name : 'custId',
					width : 80,
					sortable : false,
					formatter : 'click',
					onclick : openDoc3
				}, {
					colHeader : i18n.clss07a["L1205S07.grid17"],//"關係戶戶名"
					width : 160,
					name : 'custName',
					sortable : false
				}, {
					colHeader : i18n.clss07a["L1205S07.grid18"],//"與借戶關係"
					name : 'custRelationIndex',
					width : 80,
					sortable : false,
					align : "center"
				}, 
				{
					colHeader : i18n.clss07a["L1205S07.grid19"],//"貢獻度"
					name : 'profit',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data != null && data != undefined && data != ''){
							// 加入撇節符號
							return util.addComma(data);
						}else{
							return data;
						}
					}					
				}, 
				{
					colHeader : i18n.clss07a["L1205S07.grid20"],//"放款額度"
					name : 'loanQuota',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data != null && data != undefined && data != ''){
							// 加入撇節符號
							return util.addComma(data);
						}else{
							return data;
						}						
					}					
				}, {
					colHeader : i18n.clss07a["L1205S07.grid21"],//"放款餘額"
					name : 'loanAvgBal',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data != null && data != undefined && data != ''){
							// 加入撇節符號
							return util.addComma(data);
						}else{
							return data;
						}
					}					
				}, {
					colHeader : i18n.clss07a["L1205S07.grid22"],//"活期存款"
					name : 'depTime',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data != null && data != undefined && data != ''){
							// 加入撇節符號
							return util.addComma(data);
						}else{
							return data;
						}
					}					
				}, {
		        	 colHeader: "&nbsp",//"檢核欄位",
		             name: 'chkYN',
		             width: 20,
		             sortable: false,
					 align:"center"
		         },	{
					name : 'oid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) {
					var data = gridA181.getRowData(rowid);
					openDoc3(null, null, data);
				}
			});
}

/**
 * 往來彙總實績表
 */
function A_1_8_2() {
	var gridA182 = $("#gridview_A-1-8-2")
			.iGrid({
				handler : 'cls1141gridhandler',
				// height: 345, //for 15 筆
				height : "50", // for 10 筆
				// autoHeight: true,
				width : "100%",
				postData : $.extend({}, getKeyCustIdDupNoItem(), {
					formAction : "queryL120s04b",
					mainId : responseJSON.mainid,
					rowNum:30
				}),
				rowNum:30,
				caption: "&nbsp;",
				hiddengrid : false,
				//sortname : 'custRelation',
				//multiselect: true,
				//rownumbers : true,
				//hideMultiselect:false,
				// autofit: false,
				autowidth : true,
				colModel : [ {
					colHeader : i18n.clss07a["L1205S07.grid42"],//"報表名稱"
					width : 200,
					name : 'rptName',
					sortable : false,
					formatter : function(data) {
						// L1205S07.grid43=借戶暨關係戶與本行往來實績彙總表
						return i18n.clss07a["L1205S07.grid43"];
					}								
				}, {
					colHeader : i18n.clss07a["L1205S07.grid38"],//"建立日期"
					width : 200,
					name : 'createTime',
					sortable : false,
					formatter : 'click',
					onclick : tL120s04b					
				}, {
					name : 'oid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) {
					var data = gridA182.getRowData(rowid);
					tL120s04b(null, null, data);
				}
			});
}

/**
 * 產生主要關係戶與本行授信往來比較表(Excel)
 */
function creExcel(){
	API.confirmMessage(i18n.def["confirmRun"],function(b){
		if(b){
			
			$.ajax({
				handler : 'cls1141m01formhandler',
				type : "POST",
				dataType : "json",
				action : "creExcel",
				data : $.extend({}, getKeyCustIdDupNoItem(), {
					mainId : responseJSON.mainid,
					fieldId: get_uploadfile_fieldId()
				}),
				success : function(json) {
					$("#gridviewPare").trigger("reloadGrid");
				}
			});
				
		}
	})
}

function get_uploadfile_fieldId(){
	var r = "";
	var obj = getKeyCustIdDupNoItem();
	var keyCustId = obj.keyCustId;
	var keyDupNo = obj.keyDupNo;
	if($.trim(keyCustId).length>0){
		//fieldId 為VARCHAR(20)
		/*
		 * keyCustId 在個人戶會有10碼。 但在公司戶是8碼+2碼空白,EX【12345678  0】
		 * 在 MegaApi.uploadDialog 上傳時, 會出現 js error
		   *  　　原因：
		   * 　　　　在 common.js 內， extend :: capFileUpload
		 *　　 　　var telm = $("#" + s.fileElementId), val = telm.val();
		 *        會把傳入的 field 去 search  
		 *  
		 */
			
		var _keyCustId = $.trim(keyCustId)+"__________";		
		r = _keyCustId.slice(0,10)+keyDupNo+"_CLSNoLst";
	}
	return r;
		
}