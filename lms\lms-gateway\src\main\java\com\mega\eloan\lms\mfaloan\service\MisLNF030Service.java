/* 
 *MisLNF030Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.utils.ISearch;

import com.mega.eloan.lms.mfaloan.bean.MISLN30;

/**
 * <pre>
 * 放款帳號
 * </pre>
 * 
 * @since 2013/01/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/02,GaryChang,new
 *          </ul>
 */
public interface MisLNF030Service {
	/**
	 * 取得放款帳號
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param BranchId
	 *            分行代碼
	 * @param cnrtNo
	 *            額度序號
	 * @return
	 */
	List<Map<String, Object>> selaLoanAC(String custId, String dupNo,
			String BranchId, String cnrtNo);
	
	List<Map<String, Object>> selaLoanAC_allow_403_603(String custId, String dupNo,
			String branchId, String cnrtNo);
	/**
	 * 代償 取得放款帳號與金額
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 借款人=CUSTID ,帳號=LOANNO 利率= CODE 目前餘額：= BAL 額度=AMT:OLNAPPDATE:原貸放日期
	 *         :OLNENDDATE:原貸款到期日
	 */
	List<Map<String, Object>> selByCustId(ISearch search, String custId,
			String dupNo);

	/**
	 * 查詢 MISLN30
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	List<MISLN30> selBykey(String custId, String dupNo, String cntrNo);

	/**
	 * 查詢 MISLN30(不考慮放款帳戶銷戶與否)
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	List<MISLN30> selBykeyWithoutCancelDate(String custId, String dupNo, String cntrNo);
	
	BigDecimal selCrsR9_amt(String custIdDupNo, String brNo);
	/**
	 * 查詢個金覆審 Rule9 
	 * 
	 * @param custIdDupNo
	 *            客戶統編+重覆序號
	 * @param brNo
	 *            分行代碼
	 * @return
	 */
	BigDecimal selSumLoanBalByCustidBrNo_CrsR9(String custIdDupNo, String brNo);
	
	/**
	 * 查詢個金覆審 Rule9 的舊案，是否有在 建立帳號 後，隔一段時間才撥款的案件。<br/>
	 * 若有，則 把覆審時間提前(不要當成舊案)
	 */
	List<MISLN30> sel_CrsR9(String custIdDupNo, String brNo);
		
	/**
	 * 個金覆審-重新引入帳務資料 
	 * 
	 * @param custIdDupNo
	 *            客戶統編+重覆序號
	 * @return
	 */
	List<Map<String, Object>> selNewLnfData(String custIdDupNo);
	
	/**
	 * 個金覆審-取得團貸母戶的帳務資料  
	 * 
	 * @param custIdDupNo
	 *            客戶統編+重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	List<Map<String, Object>> selGrpData_FactTypeEQ30(String custIdDupNo, String cntrNo);
	
	/**
	 * 查詢個金覆審 Rule5 DBR22 個人無擔保且循環
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<Map<String, Object>> selByCustid_CrsR5(String custIdDupNo);
	
	MISLN30 selByPk(String loanNo);
	MISLN30 selByPk_cancelDateNull(String loanNo);
	List<MISLN30> selByCntrNo(String cntrNo);

	/**
	 * [個金額度明細表] 取得銷戶帳務檔資料
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	List<MISLN30> selLNF030_findCancelData(String custId, String dupNo,
			String cntrNo);

	List<Map<String, Object>> selaLoanNoByCntrno(String cnrtNo);

	Map<String, Object> findCreditDelayPaymentRecord(String custId, String dupNo);

	Map<String, Object> findLabourBailoutDataByCustIdAndLoanDate(String custId, String dupNo, String loanDate);
	
	/**
	 * [個金額度明細表]依條件取得帳務檔(不考慮消戶)
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @param loanClass
	 *            產品種類
	 * @return
	 */
	List<MISLN30> findByCustAndCutrnoAndLoanClass(String custId, String dupNo, String cntrNo, String loanClass);
}
