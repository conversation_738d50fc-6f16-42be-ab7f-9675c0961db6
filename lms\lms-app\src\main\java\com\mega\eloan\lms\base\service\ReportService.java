package com.mega.eloan.lms.base.service;

import java.io.OutputStream;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.exception.CapException;

public interface ReportService {
	/**
	 * 產生報表
	 * 
	 * @param params
	 *            PageParameters
	 * @return 報表內容
	 * @throws CapException
	 * @throws Exception 
	 */
	public OutputStream generateReport(PageParameters params) throws CapException, Exception;
}
