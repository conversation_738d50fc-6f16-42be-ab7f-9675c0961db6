function test888() {
	var value = $("#elfRCkdLine").val();
	if(value != ''){
		$("input[name=r888][value=" + value.substr(0,2).replace(".","") + "]").attr("checked",true);
	}
	$("#borrower-data888").thickbox({
		title : i18n.lms1815m01["L180M02B.elfRCkdLine"],
		width : 700,
		height : 330,
		modal : false,
		align : 'center',
		valign : 'bottom',
		i18n : i18n.lms1815m01,
		buttons : {
			"ok" : function() {
				var getvalue = $("input:checked[name=r888]").next('span').html();
				$("#elfRCkdLine").val(getvalue);
				$.thickbox.close();
			},
			"cancel" : function() {
				$.thickbox.close();
			}
		}
	});
}
function test2() {
	var value = $("#elfNCkdFlag").val();
	if(value != ''){
		$("input[name=r8888][value=" + value.substr(0,2).replace(".","") + "]").attr("checked",true);
	}
	$("#borrower-data2").thickbox({
		title : i18n.lms1815m01["L180M02B.elfNCkdFlag"],
		width : 570,
		height : 350,
		modal : false,
		align : 'center',
		valign : 'bottom',
		i18n : i18n.lms1815m01,
		buttons : {
			"ok" : function() {
				var getvalue = $("input:checked[name=r8888]").next('span').html();
				if("N.A." == getvalue){
					getvalue = "";
				}
				$("#elfNCkdFlag").val(getvalue);
				if($("[name=r8888]:checked").val() == "8"){
					$("#nextNwDt").show();
				}else{
					$("#elfNextNwDt").val("");
					$("#nextNwDt").hide();
				}
				$.thickbox.close();
			},
			"cancel" : function() {
				$.thickbox.close();
			}
		}
	});
}
function test8882() {
	var value = $("#elfMDFlag").val();
	if(value != ''){
		$("input[name=r88888][value=" + value.substr(0,2).replace(".","") + "]").attr("checked",true);
	}
	$("#borrower-data8883").thickbox({
		title : i18n.lms1815m01["L180M02B.elfMDFlag"],
		width : 640,
		height : 400,
		modal : false,
		align : 'center',
		valign : 'bottom',
		i18n:i18n.lms1815m01,
		buttons : {
			"ok" : function() {
				var getvalue = $("input:checked[name=r88888]").next('span').html();
				if("N.A." == getvalue){
					getvalue = "";
				}
				$("#elfMDFlag").val(getvalue);
				$.thickbox.close();
			},
			"cancel" : function() {
				$.thickbox.close();
			}
		}
	});
}