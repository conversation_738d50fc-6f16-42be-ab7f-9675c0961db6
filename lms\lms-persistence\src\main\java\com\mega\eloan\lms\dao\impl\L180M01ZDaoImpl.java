/* 
 * L180M01ZDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L180M01ZDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L180M01Z;

/** 覆審名單主檔 **/
@Repository
public class L180M01ZDaoImpl extends LMSJpaDao<L180M01Z, String>
	implements L180M01ZDao {

	@Override
	public L180M01Z findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180M01Z> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L180M01Z> list = createQuery(L180M01Z.class,search).getResultList();
		return list;
	}
	
	@Override
	public L180M01Z findByUniqueKey(Date dataDate, String branchId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "dataDate", dataDate);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180M01Z> findByIndex1Z(Date dataDate, String branchId){
		ISearch search = createSearchTemplete();
		List<L180M01Z> list = null;
		if (dataDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate", dataDate);
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(L180M01Z.class,search).getResultList();
		}
		return list;
	}
}