package com.mega.eloan.lms.base.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractOutputPage;

/**
 * <pre>
 * 共用功能頁面
 * </pre>
 * 
 * @since 2013/1/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/7,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/commompage")
public class LMSCommomPage extends AbstractOutputPage {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public void execute(ModelMap model, PageParameters params) {
		renderJsI18N(LMSCommomPage.class);
	}

	@Override
	protected String getViewName() {
		return getEloanPagePathByClass(getClass());
	}

}
