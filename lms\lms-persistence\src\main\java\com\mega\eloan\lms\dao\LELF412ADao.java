/* 
 * LELF412ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.LELF412A;

/** 逾期未覆審名單主辦覆審控制歷史檔 **/
public interface LELF412ADao extends IGenericDao<LELF412A> {

	LELF412A findByOid(String oid);

	List<LELF412A> findByMainId(String mainId);

	List<LELF412A> findByMainIdAndDataDate(String mainId, Date dataDate);

	List<LELF412A> findByMainIdDataDateAndBranch(String mainId, Date dataDate,
			String branch);

	List<LELF412A> findByMainIdDataDateBranchAndCustId(String mainId,
			Date dataDate, String custId, String dupNo);

	List<LELF412A> findByDataDateAndBranch(Date dataDate, String branch);

}