initDfd.done(function(json){

	ilog.debug("<EMAIL>"); 

	$("#updateLaonQuota").click(function(){
		PaperlessCreditCondition.openBox(PaperlessCreditCondition.QUOTA)
	});
	
	$("#updateLoanPeriod").click(function(){
		PaperlessCreditCondition.openBox(PaperlessCreditCondition.TERM)
	});
	
	$("#updateLoanFee").click(function(){
		PaperlessCreditCondition.openBox(PaperlessCreditCondition.FEE)
	});
	
	$("#updateLoanRate").click(function(){
		L140S02Action.openRateBox();
	});

	$("#docLogLaonQuota").click(function(){
		PaperlessCreditCondition.showDocLogGrid(PaperlessCreditCondition.QUOTA);
	});
		
	$("#docLogLoanPeriod").click(function(){
		PaperlessCreditCondition.showDocLogGrid(PaperlessCreditCondition.TERM);
	});
	
	$("#docLogLoanRate").click(function(){
		PaperlessCreditCondition.showDocLogGrid(PaperlessCreditCondition.RATE);
	});
				
	$("#docLogLoanFee").click(function(){
		PaperlessCreditCondition.showDocLogGrid(PaperlessCreditCondition.FEE);
	});
				
	PaperlessCreditCondition.hide_show_docLogBtn();
	
});

//initDfdPanel27.done(function(json){
	
//	if(json.docStatus == '02O'){
//		$("#updateLaonQuota").show();
//		$("#updateLoanPeriod").show();
//		$("#updateLoanRate").show();
//		$("#updateLoanFee").show();
//
//		$("#openBox_creidtConditionModifying input").each(function(){
//			$(this).readOnly(false);
//		});
//		
//		$("#openBox_creidtConditionModifying textarea").each(function(){
//			$(this).readOnly(false);
//		});
//		
//		$("#openBox_creidtConditionModifying select").each(function(){
//			$(this).readOnly(false);
//		});
//		
//		alert($("#L140S02CBox select").length)
//		
//		$("#L140S02CBox select").each(function(){
//			$(this).readOnly(false);
//			$(this).attr("disabled", false);
//		});
//		
//		$("#L140S02CBox input").each(function(){
//			$(this).readOnly(false);
//			$(this).attr("disabled", false);
//		});
//		
//		$("#L140S02CBox :checkbox").each(function(){
//			$(this).attr("disabled", false);
//		});
//	}
//});

var PaperlessCreditCondition = {
	FEE: 'fee',
	QUOTA: 'quota',
	RATE: 'rate',
	TERM: 'term',
	openBoxTitleName: '',
	contextUpdHtmlId: '',
	remarkUpdHtmlId: '',
	
	processDisplayItem: function(code){
		
		$(".tr_quotaModifying").hide();
		$(".tr_termModifying").hide();
		$(".tr_feeModifying").hide();
		$("#remarkModifying").val('');
		
		var role = responseJSON.mainDocStatus == '01O' ? 'ao' : 'rv';
		var titleName, contextUpdHtmlId, remarkUpdHtmlId;
		
		switch (code) {
			case PaperlessCreditCondition.QUOTA:
				$(".tr_quotaModifying").show();
				titleName = '系統計算額度修改';
				contextUpdHtmlId = role + 'ModifyAmount';
				remarkUpdHtmlId = role + 'UpdAmountRemark';
				break;
			case PaperlessCreditCondition.FEE:
				$(".tr_feeModifying").show();
				titleName = '系統計算費用修改';
				contextUpdHtmlId = role + 'ModifyFee';
				remarkUpdHtmlId = role + 'UpdFeeRemark';
				break;
			case PaperlessCreditCondition.RATE:
				titleName = '系統計算利率修改';
				break;
			case PaperlessCreditCondition.TERM:
				$(".tr_termModifying").show();
				titleName = '系統計算期間修改';
				contextUpdHtmlId = role + 'ModifyPeriod';
				remarkUpdHtmlId = role + 'UpdPeriodRemark';
				break;
		}
		
		PaperlessCreditCondition.openBoxTitleName = titleName;
		PaperlessCreditCondition.contextUpdHtmlId = contextUpdHtmlId;
		PaperlessCreditCondition.remarkUpdHtmlId = remarkUpdHtmlId;
	},
	
	openBox: function(type){
		
		PaperlessCreditCondition.processDisplayItem(type);

		var buttons = {};
		buttons[i18n.def.sure] = function(){

			if($("#creidtConditionModifyingForm").valid()){
				var content;
				if(type == PaperlessCreditCondition.TERM){
					content = $("#yearModifying").val() + '-' + $("#monthModifying").val();
				}
				else if(type == PaperlessCreditCondition.FEE){
					content = $("#feeCode").val() + '-' + $("#feeAmount").val();
				}
				else{
					content = $("#" + type + 'Modifying').val() ;
				}	
							
		        $.ajax({
					handler : 'cls1141formhandler',
					type : "POST", 
					dataType : "json",
					data : {
						formAction : "updateCreditCondition",
						mainId : responseJSON.mainid,
						type: type,
						content: content,
						remark:  $("#remarkModifying").val()
					},
					success : function(json) {
						$("#" + PaperlessCreditCondition.contextUpdHtmlId).val(json.content);
						$("#" + PaperlessCreditCondition.remarkUpdHtmlId).val(json.remark);
						PaperlessCreditCondition.hide_show_docLogBtn();
					}
				});
				$.thickbox.close();
			}
        };
		
		buttons[i18n.def.close] = function(){
			$.thickbox.close();
		}
		
		$("#openBox_creidtConditionModifying").thickbox({
	        title: PaperlessCreditCondition.openBoxTitleName,
	        width: 350,
	        height: 300,
	        modal: true,
			align: "center",
			valign: 'bottom',
	        i18n: i18n.def,
	        buttons: buttons
    	});
	}, 
	hide_show_docLogBtn:function(){

		var _isEnable_QUOTA = $("#aoModifyAmount").val()!="" || $("#rvModifyAmount").val()!="";
		var _isEnable_TERM = $("#aoModifyPeriod").val()!="" || $("#rvModifyPeriod").val()!="";
		var _isEnable_RATE = $("#aoModifyRate").val()!="" || $("#rvModifyRate").val()!="";
		var _isEnable_FEE =  $("#aoModifyFee").val()!="" || $("#rvModifyFee").val()!="";
		
		PaperlessCreditCondition.hide_show_docLogBtn_byElm($("#docLogLaonQuota"), _isEnable_QUOTA);
		PaperlessCreditCondition.hide_show_docLogBtn_byElm($("#docLogLoanPeriod"), _isEnable_TERM);
		PaperlessCreditCondition.hide_show_docLogBtn_byElm($("#docLogLoanRate"), _isEnable_RATE);
		PaperlessCreditCondition.hide_show_docLogBtn_byElm($("#docLogLoanFee"), _isEnable_FEE);
	},
	hide_show_docLogBtn_byElm:function($elm, isEnable){
		if(isEnable){
			$elm.removeClass(" ui-state-disabled ").removeAttr("disabled");
		}else{
			$elm.addClass(" ui-state-disabled ").attr("disabled", "true");
		}	
	}
	, 
	showDocLogGrid:function(type){

		if($("#cls_L120S19BGrid.ui-jqgrid-btable").length >0){
			$("#cls_L120S19BGrid").jqGrid("setGridParam", {
				postData : {"type":type,
		            "mainId": responseJSON.mainId,
		            "formAction": "queryL120S19B"},
				search : true
			}).trigger("reloadGrid");
			
			var cls_L120S19BGridBox_title = "";
			if(type==PaperlessCreditCondition.QUOTA){
				cls_L120S19BGridBox_title = i18n.cls1201s27['brmp.loanQuota'];
			}else if(type==PaperlessCreditCondition.TERM){
				cls_L120S19BGridBox_title = i18n.cls1201s27['brmp.loanPeriod'];
			}else if(type==PaperlessCreditCondition.RATE){
				cls_L120S19BGridBox_title = i18n.cls1201s27['brmp.loanRate'];
			}else if(type==PaperlessCreditCondition.FEE){
				cls_L120S19BGridBox_title = i18n.cls1201s27['brmp.loanFee'];
			}
		
			$("#cls_L120S19BGridBox").thickbox({
		       title: cls_L120S19BGridBox_title,
		       width: 750,
	           height: 500,
	           align: "center",
	           valign: "bottom",
	           modal: false,
	           i18n: i18n.def,
	           buttons: {
	               "cancel": function(){
	            	   $.thickbox.close();
	               }
	           }
			});
		}else{
			
			var $cls_L120S19BGrid = $("#cls_L120S19BGrid").iGrid({
		        handler: 'cls1141gridhandler',        
		        height: 370,
				sortname: "createTime",
		        sortorder: "desc",
		        postData: {
		            mainId: responseJSON.mainId,
		            formAction: "queryL120S19B"
		        },
		        needPager: false,        
		        shrinkToFit: false,
		        multiselect: false,       
		        colModel: [
		          {  colHeader: i18n.cls1201s27['L120S19B.createTime'], name: 'createTime',width: 120, sortable: true,align: "left"	        		  
		        }, {
		        	colHeader: i18n.cls1201s27['L120S19B.creator'], name: 'creator', width: 65, sortable: true, align: "left"
		        }, {
		            colHeader: i18n.cls1201s27['L120S19B.role'], name: 'role', width: 65, sortable: true, align: "left"          
		        }, {
		            colHeader: i18n.cls1201s27['L120S19B.content'], name: 'content',width: 220, sortable: true,align: "left"            
		        }, {
		        	colHeader: i18n.cls1201s27['L120S19B.remark'], name: 'remark',width: 180, sortable: true,align: "left"
		        }, { name: 'oid', hidden: true }
		        ],        
		        ondblClickRow: function(rowid){
		        	
		        }        
		    });
		}
	}
}

var _M = {
		// 自己捕的!!
		tabMainId : "",//額度明細表mainId
		CaseMainId : responseJSON.mainId,//簽報書mainId
		// end
		
	    fhandle: "cls1151m01formhandler",
	    itemType: "",
	    AllFormData: {
	        "04": {}
	    },
	    /**
	     * 清空欄位值並隱藏
	     * @param {Object} $obj jquery 物件
	     */
	    cleanTrHideInput: function($obj){
	        $obj.hide();
	        $(["input", "select", "textarea", "span.field"]).each(function(i, v){
	            $obj.find(v).each(function(){
	                var $this = $(this);
	                $this.each(function(){
	                    switch (this.nodeName.toLowerCase()) {
	                        case 'input':
	                            switch (this.type.toLowerCase()) {
	                                case "text":
	                                case "hidden":
	                                case "password":
	                                    $this.val("");
	                                    break;
	                                case "radio":
	                                case "checkbox":
	                                    $this.removeAttr("checked");
	                                    break;
	                                default:
	                                    $this.val("");
	                                    break;
	                            }
	                            break
	                        case 'select':
	                            $this.val("").trigger("change");
	                            break;
	                        case "span":
	                            $this.html("");
	                            break;
	                    }
	                });
	            });
	        });
	    },
	    codetypeItem: {},
	    /**
	     *執行ajax的動作
	     * @param {String} action 要執行的動作
	     * @param {Array} formId
	     * @param {Object} data 要傳送的資料
	     * @param {Function} success 執行後的動作
	     *
	     * ex1: _M.doAjax(action:queryL140M01A);
	     *
	     * ex2:_M.doAjax(action:"queryL140M01A",success:function(obj){
	     *      doSomething
	     *     });
	     * ex3:_M.doAjax(action:"queryL140M01A",formId:"xxxform",success:function(obj){
	     *      doSomething
	     *     });
	     * ex4:_M.doAjax(action:"queryL140M01A",formId:"xxxform",data:obj,success:function(obj){
	     *      doSomething
	     *     });
	     */
	    doAjax: function(obj){
	        $.ajax({
	            async: obj.async == undefined ? true : obj.async,
	            handler: _M.fhandle,
	            action: obj.action || alert("error not have Action"),
	            formId: obj.formId || 'empty',
	            
	            data: $.extend({
	                noOpenDoc: true,
	                caseType: _M.itemType || "1",
	                mainId: _M.CaseMainId,
	                tabFormMainId: _M.tabMainId
	            }, obj.data || {}),
	            success: function(responseData){
	                if (obj.success) {
	                    obj.success(responseData);
	                }
	                
	            }
	        });
	    },
	    
	    /**
	     * 表單是否出初始化
	     */
	    isInit: false,
	    isReadOnly: false
	};
/**
* 利率方式
*/
var L140S02CAction = {
   isInit: false,
   formData: null,
   //產品種類
   prodKind: "",
   /**
    * 判斷是否為產品種類02 或04
    */
   isProdKind02And04: function(){
       var result = false;
       if (this.prodKind == "02" || this.prodKind == "04") {
           result = true;
       }
       return result;
   },
   formId: "L140S02CForm",
   /**
    * rateType :利率基礎
    * rateUserType: 自訂利率
    * rate:利率對應
    * */
   AllItem: null,
   init: function(data){
       if (!this.isInit) {
           TEST.start("createTr");
           this.createTr();
           TEST.end("createTr");
           TEST.start("initItem");
           L140S02Action.initItem(this.formId);
           TEST.end("initItem");
           TEST.start("initEvent");
           this.initEvent();
           TEST.end("initEvent");
           this.isInit = true;
       }
       TEST.start("initAllItem");
       this.initAllItem();
       TEST.end("initAllItem");
       
       var $form = $("#" + this.formId);
       if (data.isInputDesc == "Y") {
           $form.find("#desc").show();
       }
       else {
           $form.find("#desc").hide();
       }
       
       $form.reset();
       $form.find("[id^=bgnNum_],[id^=endNum_]").each(function(){
           var $this = $(this);
           $this.attr("readOnly", "readOnly").removeClass("required");
       });
       $form.find("[class^=isUseBox_]").hide();
       if (data) {
           L140S02CAction.formData = data;
           TEST.start("injectData");
           $form.injectData(data);
           TEST.end("injectData");
           //判斷有勾選的才進行觸發
           var target = "";
           for (var keyName in data) {
               if (keyName.match("isUseBox_") && data[keyName] == "Y") {
                   if (target) {
                       target += ",";
                   }
                   target += "." + keyName;
               }
           }
           if (target) {
               TEST.start("selectALLtrigger");
               $form.find(target).find("select.haveEven").trigger("change", "init");
               TEST.end("selectALLtrigger");
           }
           
       }
       var $payNumOldTr = $form.find("#payNumOldTr");
       if (data.payNum) {
           //            if ($("#payNum").val != "0") {
           if (data.payNum != "0") {
               $payNumOldTr.show();
               
               //開放可自行修改前期利率欄位值。
               $("#payNum").dblclick(function(){
                   if ($("#payNum").val() != "0") {
                       $("#preDscr").removeAttr("readonly").addClass("required");
                   }
               });
           }
           else {
               $payNumOldTr.hide();
           }
       }
       else {
           $payNumOldTr.hide();
       }
       //取得產品對應計息方式
       var rIntWay = L140S02Action.subjCodeAttr['rIntWay'];
       if (rIntWay) {
           if (rIntWay.split("|").length > 1) {
               $form.find("#intWay").removeAttr("disabled");
           }
           else {
               $form.find("#intWay").val(rIntWay).attr("disabled", "disabled");
           }
           $form.find("#intWay").trigger("change", "init");
       }
       
       //計息方式─透支
       if(true){
       	var subjCode2 = L140S02Action.subjCodeAttr['subjCode2'];
           //102,202 透支
           //104,204,404 存摺存款透支(ex:金融卡)
           if(        (subjCode2=="102"||subjCode2=="202")
           		|| (subjCode2=="104"||subjCode2=="204")
             ){
           	$form.find("#intWay option[value=1]").removeAttr("disabled"); // 一開始只有{1,2}, 後來才變{1,2,P,Q}, 要能輸入舊資料
           	$form.find("#intWay option[value=2]").attr("disabled", "disabled");
           	$form.find("#intWay option[value=P]").removeAttr("disabled");
           	$form.find("#intWay option[value=Q]").removeAttr("disabled");
           }else if(subjCode2=="404"){
           	$form.find("#intWay option[value=1]").attr("disabled", "disabled");
           	$form.find("#intWay option[value=2]").attr("disabled", "disabled");
           	$form.find("#intWay option[value=P]").attr("disabled", "disabled");
           	$form.find("#intWay option[value=Q]").removeAttr("disabled");
           }else{
           	$form.find("#intWay option[value=1]").removeAttr("disabled"); // 一開始只有{1,2}, 後來才變{1,2,P,Q}, 要能輸入舊資料
           	$form.find("#intWay option[value=2]").removeAttr("disabled");
           	$form.find("#intWay option[value=P]").attr("disabled", "disabled");
           	$form.find("#intWay option[value=Q]").attr("disabled", "disabled");
           }	
       }        
       
       //當7.登錄利率的時候如果該案有已還期數時，其第一段的起期鎖住不要再讓user修改。
       if ($.trim(data.payNum) != '' && data.payNum != 0) {
           $form.find("#isUseBox_1,#bgnNum_1").attr("disabled", "disabled");
       }
       else {
           if (!_M.isReadOnly) {
               $form.find("#isUseBox_1,#bgnNum_1").removeAttr("disabled");
           }
       }
   },
   
   
   /**
    * 產生重覆的tr內容
    */
   createTr: function(){
       var tempTr = $("#L140S02C_temp_Div").html();
       var prefix = "#isTempTr_";
       var $form = $("#" + this.formId);
       for (var i = 0; i <= 10; i++) {
           $form.find(prefix + i).html(tempTr.replace(/{seq}/g, i));
       }
       
   },
   /**
    * 取得所有項目
    */
   initAllItem: function(){
       
       var $form = $("#" + this.formId);
       _M.doAjax({
           action: "getAllItem",
           async: false,
			data:
				$.extend({}, {currentApplyCurr: _M.AllFormData["04"]['currentApplyCurr']}
			),
           success: function(obj){

				$form.find(".rateType").empty();
               $form.find(".rateType").setItems({
                   item: obj.rateType,
                   format: "{value} - {key}",
                   sort: "asc",
                   value: ""
               });
				
				if (!this.AllItem) {
					L140S02CAction.AllItem = obj;
					$form.find(".rateUserType").setItems({
						item: obj.rateUserType,
						format: "{value} - {key}",
						sort: "asc",
						value: ""
					});
				}
           }
       });
   },
   /**
    * 新增事件
    */
   initEvent: function(){
       var $form = $("#" + this.formId);
       /**
        * 是否使用這段利率
        */
       $form.find("[name^=isUseBox_]").click(function(){
           var prefix = ""
           var id = $(this).attr("id");
           var id_Seq = parseInt(id.replace("isUseBox_", ""), 10);
           var $obj = $form.find("." + id);
           var $bgnNum = $form.find("#bgnNum_" + id_Seq);
           var $endNum = $form.find("#endNum_" + id_Seq);
           if (this.checked) {
               $obj.show();
               if (id_Seq != "1") {
                   var beforeVal = $form.find("#endNum_" + (id_Seq - 1)).val();
                   if (beforeVal != "") {
                       $bgnNum.val($.trim(parseInt(beforeVal, 10) + 1));
                   }
               }
               var intWaycheck = !($form.find("#intWay").val() == "1" || $form.find("#intWay").val() == "P" || $form.find("#intWay").val() == "Q");
               if (intWaycheck) {
                   $bgnNum.removeAttr("readonly");
                   $endNum.removeAttr("readonly");
               }
               if (!L140S02CAction.isProdKind02And04() && intWaycheck) {
                   $bgnNum.addClass("required");
                   $endNum.addClass("required");
               }
           }
           else {
               var preDscr = $.trim($form.find("#payNum").html());
               //當無前期記錄才清空首段利率
               if (id_Seq == "1" && preDscr == "") {
                   $form.find("#submitRate").html("");
               }
               $bgnNum.val("").removeClass("required").attr("readonly", "readonly");
               $endNum.val("").removeClass("required").attr("readonly", "readonly");
               _M.cleanTrHideInput($obj);
           }
       });
       //其他說明欄位
       var $desc = $form.find("#desc");
       /**
        * 判斷是否勾選其他欄位
        */
       $form.find("#isInputDesc").click(function(){
           if (this.checked) {
               $desc.show();
           }
           else {
               $desc.hide();
               $desc.val("");
           }
       });
       /**
        * 利率基礎欄位切換
        */
       $form.find(".rateType").change(function(k, v){
           TEST.start("rateType-change");
           var id = $(this).attr("id");
           var id_seq = id.replace("rateType_", "");
           var value = $(this).val();
           //自訂利率相關
           var $othDivId = $form.find("#rateUserDiv_" + id_seq);
           //指標利率相關
           var $rateTypeDiv = $form.find("#rateTypeDiv_" + id_seq);
           //基礎利率相關
           var $baseRate = $rateTypeDiv.find("#baseRate_" + id_seq);
           //加減碼
           var $pmFlagDiv = $rateTypeDiv.find("#pmFlagDiv_" + id_seq);
           //利率方式 
           var $rateFlag = $form.find("#rateFlag_" + id_seq);
           //利率方式變動方式
           var $rateFlagDiv = $form.find("#rateFlagDiv_" + id_seq);
           //利率變動方式
           var $rateChgWay = $form.find("#rateChgWay_" + id_seq);
           //變動周期
           var $rateChgWay2 = $form.find("#rateChgWay2_" + id_seq);
           if (value) {
               if (value == "01") {
                   //利率代碼01時利率方式不能選2。
                   $othDivId.show();
                   $rateFlag.find("option[value=2]").attr("disabled", "disabled");
                   $rateFlag.removeAttr("disabled", "disabled");
                   _M.cleanTrHideInput($rateTypeDiv);
                   _M.cleanTrHideInput($pmFlagDiv);
                   _M.cleanTrHideInput($rateFlagDiv);
               }
               else {
               
                   _M.cleanTrHideInput($othDivId);
                   $rateFlag.setItems({
                       item: API.loadCombos("lms1405s0204_rateKind")["lms1405s0204_rateKind"],
                       format: "{value} - {key}",
                       sort: "asc",
                       value: L140S02CAction.formData["rateFlag_" + id_seq] || ""
                   });
                   $rateTypeDiv.show();
                   //當欄位非初始動作才需抓DB值
                   if (v != "init") {
                       $baseRate.html(L140S02CAction.AllItem.rate[value]);
                   }
                   //當非文件鎖定時才需要移除欄位的屬性
                   if (!_M.isReadOnly) {
                       $rateFlag.removeAttr("disabled", "disabled");
                       $rateChgWay.removeAttr("disabled", "disabled");
                       $rateChgWay2.removeAttr("disabled", "disabled");
                   }
                   switch (value) {
                       case "ML":
                       case "MM":
                       case "MN":
                       case "M2":
                       case "M3":
                       case "I7":
                       case "P6":
                       //2013/08/02,Rex,明澤說p7改成不要鎖定在機動
                       //case "P7":
                       case "20":
                       case "24":
                       case "31":
                       case "33":
                       case "35":
                       case "77":
                       case "79":
                       case "N2":
                           $rateFlag.val("2").trigger("change");
                           $rateFlag.attr("disabled", "disabled");
                           break;
                       case "23":
                       case "30":
                       case "32":
                       case "34":
                       case "76":
                       case "78":
                           $rateFlag.val("1").trigger("change");
                           $rateFlag.attr("disabled", "disabled");
                           break;
                       case "6R":
                           var subcode = L140S02Action.subjCodeAttr['subjCode'];
                           //[J-098-0065] " 6R"時, 利率方式須預設為”3”(定期浮動)、利率變動週期須預設為”1”、利率變動方式須預設為”M”(月)
                           //[J-098-0156]只限13506200(授信科目:473)、14501500(授信科目:673)、13506300(授信科目:474)、14502000(授信科目:674)
                           if (subcode == "13506200" || subcode == "14501500" || subcode == "13506300" || subcode == "14502000") {
                               $rateFlag.val("3").trigger("change");
                               $rateFlag.attr("disabled", "disabled");
                               $rateChgWay.val("1").trigger("change"); //1 - 每『月/三個月/半年/九個月/年』調整乙次
                               $rateChgWay.attr("disabled", "disabled");
                               $rateChgWay2.val("1"); //1 - 月, 3 - 三個月
                               $rateChgWay2.attr("disabled", "disabled");
                           }
                           /*
                                                                          Ｑ：原本的簽案內容是 2.機動利率。但若依上述的邏輯，會強迫轉 3.定期浮動
                                                                                    造成明明是「不變」，印出的內容卻被改到了
                                 
                           Workaround: 
                                                                                   若要呈現原始資料(舊案)，於 browser 的 console 取消 disable，再更正 	
                               $("#rateFlag_1").attr("disabled", "");
                               $("#rateChgWay_1").attr("disabled", "");
                               $("#rateChgWay2_1").attr("disabled", "");
                           */
                           break;
                       default:
                           break;
                   }
                   $pmFlagDiv.find("option[value=M]").removeAttr("disabled");
                   //加減年利率呈現條件
                   switch (value) {
                       //case "M2":
                       case "M3":
                       case "M8":
                       case "M9":
                       case "MK":
                       case "ML":
                       case "MM":
                       case "MN":
                       case "MO":
                           _M.cleanTrHideInput($pmFlagDiv);
                           break;
                       case "6C":
                           $pmFlagDiv.show();
                           $pmFlagDiv.find("option[value=M]").attr("disabled", "disabled");
                           break;
                       default:
                           $pmFlagDiv.show();
                           break;
                   }
               }
           }
           else {
               _M.cleanTrHideInput($othDivId);
               _M.cleanTrHideInput($rateTypeDiv);
               _M.cleanTrHideInput($pmFlagDiv);
               _M.cleanTrHideInput($rateFlagDiv);
           }
           TEST.end("rateType-change");
       });
       /**
        * 加減碼欄位切換
        */
       $form.find(".pmFlag").change(function(){
           TEST.start("pmFlag-change");
           var id = $(this).attr("id");
           var id_seq = id.replace("pmFlag_", "");
           var $pmRateSpan = $("#pmRateSpan_" + id_seq);
           if ($(this).val() == "") {
               $("#pmRate_" + id_seq).val("");
               $pmRateSpan.hide();
           }
           else {
               $pmRateSpan.show();
           }
           L140S02CAction.countNowRate(id_seq, $form);
           TEST.end("pmFlag-change");
       });
       
       
       /**
        * 利率方式欄位切換
        */
       $form.find(".rateFlag").change(function(k, v){
           TEST.start("rateFlag-change");
           var id = $(this).attr("id");
           var id_seq = id.replace("rateFlag_", "");
           var value = $(this).val();
           var $rateFlagDiv = $form.find("#rateFlagDiv_" + id_seq);
           if (value == "3") {
               $rateFlagDiv.show();
               if (v) {
                   $form.find("#rateChgWay_" + id_seq).val(L140S02CAction.formData["rateChgWay_" + id_seq] || "");
               }
           }
           else {
               _M.cleanTrHideInput($rateFlagDiv);
           }
           TEST.end("rateFlag-change");
       });
       
       
       /**
        * 利率變動方式切換
        */
       $form.find(".rateChgWay").change(function(k, v){
           TEST.start("rateChgWay-change");
           var id = $(this).attr("id");
           var id_seq = id.replace("rateChgWay_", "");
           var value = $(this).val();
           var $rateChgWay2Div = $form.find("#rateChgWay2Div_" + id_seq);
           if (value == "1") {
               $rateChgWay2Div.show();
               if (v) {
                   $form.find("#rateChgWay2_" + id_seq).val(L140S02CAction.formData["rateChgWay2_" + id_seq] || "");
               }
           }
           else {
               _M.cleanTrHideInput($rateChgWay2Div);
           }
           TEST.end("rateChgWay-change");
       });
       
       /**
        *重新引進匯率 按鈕
        */
       $form.find("[id^=reloadBaseRate_]").click(function(){
           var id = $(this).attr("id");
           var id_seq = id.replace("reloadBaseRate_", "");
           var value = $("#rateType_" + id_seq).val();
           $("#baseRate_" + id_seq).html(L140S02CAction.AllItem.rate[value]);
           L140S02CAction.countNowRate(id_seq, $form);
       });
       /**
        *調整匯率 按鈕
        */
       $form.find("[id^=modfixBaseRate_]").click(function(){
           var id = $(this).attr("id");
           var id_seq = id.replace("modfixBaseRate_", "");
           L140S02CAction.enterRateBox(id_seq);
       });
       /**
        *  當加減年率欄位失去焦點後 要算出其目前利率
        */
       $form.find("[name^=pmRate_]").blur(function(){
           var id = $(this).attr("id");
           var id_seq = id.replace("pmRate_", "");
           L140S02CAction.countNowRate(id_seq, $form);
       });
       /**
        *  當扣稅負擔值欄位失去焦點後 要算出其目前利率
        */
       $form.find("#taxRate").blur(function(){
           L140S02CAction.countNowRate(1, $form);
       });
       
       //扣稅負擔值
       var $taxRateSpan = $form.find("#taxRateSpan");
       //收息方式
       var $rIntWay = $form.find("#rIntWay");
       var $bgnNum1 = $form.find("#bgnNum_1");
       var $endNum1 = $form.find("#endNum_1");
       $rIntWay.change(function(){
           var value = $(this).val();
           var intWaycheck = ($form.find("#intWay").val() == "1" || $form.find("#intWay").val() == "P" || $form.find("#intWay").val() == "Q");
           if (intWaycheck) {
               $bgnNum1.val("").removeClass("required").attr("readonly", "readonly");
               $endNum1.val("").removeClass("required").attr("readonly", "readonly");
           }
       });
       
       //所有非第一段的box內容
       var $notIsOne = $form.find("[class^=isUseBox_]:not(.isUseBox_1)");
       var $notIsOneNum = $form.find(".notIsOneNum");
       //計息方式
       $form.find("#intWay").change(function(k, v){
           TEST.start("intWayChange");
           var value = $(this).val();
           // 計息方式如果選2-期付金時，收息方式請直接帶出6-期付金。也就是說計息方式為2時收息方式一定為6。
           if (value == "2") {
               //J-102-0226>e-Loan個金業務開放個人戶期付金可由客戶自行負擔稅負修改。
               //                $taxRateSpan.hide().find("input").val("");
               var taxRateValue = $form.find("#taxRate").val();
               if (taxRateValue == '') {
                   //帶入預設值
                   $form.find("#taxRate").val("1");
               }
               //扣稅負擔值
               $taxRateSpan.show();
               
               $rIntWay.val("6");
               $rIntWay.attr("disabled", "disabled");
               $form.find("[name^=isUseBox_]").show();
               //扣稅負擔值
               //                $taxRateSpan.hide().find("input").val("");
               // 計息方式如果選1-按月計息時，僅能選擇第一段利率，且收息方式選1-按月收息時，關閉期間起迄期輸入。
               $bgnNum1.removeAttr("readonly");
               $endNum1.removeAttr("readonly");
           }
           else {
               if (value == "1" || value == "P" || value == "Q") {
                   if (!v) {
                       $rIntWay.val("");
                   }
                   var taxRateValue = $form.find("#taxRate").val();
                   if (taxRateValue == '') {
                       //帶入預設值
                       $form.find("#taxRate").val("1");
                   }
                   //扣稅負擔值
                   $taxRateSpan.show();
                   $rIntWay.find("option[value=6]").attr("disabled", "disabled");
                   TEST.start("cleanTrHideInput");
                   $notIsOneNum.val("").removeClass("required").attr("readonly", "readonly");
                   $notIsOne.hide();
                   //_M.cleanTrHideInput($notIsOne);
                   $form.find("input.notIsOne:checkbox").removeAttr("checked").hide();
                   TEST.end("cleanTrHideInput");
                   $rIntWay.removeAttr("disabled");
                   $bgnNum1.val("").removeClass("required").attr("readonly", "readonly");
                   $endNum1.val("").removeClass("required").attr("readonly", "readonly");
               }
               else {
               //扣稅負擔值
               //                    $taxRateSpan.hide().find("input").val("");
               }
           }
           TEST.end("intWayChange");
       });
   },
   /**
    * 計算目前利率
    * @param {Object} seq 欄位序列號
    */
   countNowRate: function(seq, $form){
       if (!$form) {
           $form = $("#" + this.formId);
       }
       var rateType = $form.find("#rateType_" + seq).val();
       //指標利率
       var baseRate = parseFloat($form.find("#baseRate_" + seq).html(), 10);
       //當為自訂利率抓的欄位不一樣
       if (rateType == "01") {
           baseRate = parseFloat($form.find("#rateUser_" + seq).val(), 10);
       }
       //加減種類
       var pmFlag = $form.find("#pmFlag_" + seq).val();
       //加減碼
       var pmRateValue = $form.find("#pmRate_" + seq).val();
       var pmRate = pmRateValue ? parseFloat(pmRateValue, 10) : 0;
       if (pmFlag == "P") {
           baseRate += pmRate;
       }
       else 
           if (pmFlag == "M") {
               baseRate -= pmRate;
           }
       if (isNaN(baseRate)) {
           baseRate = "";
       }
       else {
           baseRate = Math.round(parseFloat(baseRate, 10).toFixed(4) * 10000) / 10000;
       }
       var taxRate = $form.find("#taxRate").val();
       
       //當有扣稅負擔值才要算值
       //        if ($form.find("#intWay").val() == "1") {
       if (taxRate != '' && taxRate != '0' && baseRate != '') {
           baseRate = Math.round(parseFloat(baseRate / taxRate, 10).toFixed(4) * 10000) / 10000;
       }
       //        }
       $form.find("#nowRate_" + seq).html(baseRate);
       var payNum = $.trim($form.find("#payNum").html());
       //帶入首段利率
       if (seq == "1" && baseRate != "" && (payNum == '' || payNum == "0")) {
           $form.find("#submitRate").html(baseRate);
       }
   },
   /**
    *輸入修改利率thickBox
    * @param {Object} seq 欄位序列號
    */
   enterRateBox: function(seq){
       var $form = $("#" + this.formId);
       var $baseRate = $form.find("#baseRate_" + seq);
       var srcValue = $.trim($baseRate.html());
       if (!srcValue) {
           //grid_selector=請選擇資料
           return API.showMessage(i18n.def.grid_selector);
       }
       var $inputform = $("#cls_enterRateForm");
       $inputform.reset();
       $inputform.find("#cls_enterRateInupt").val(srcValue);
       $("#cls_enterRateBox").thickbox({
           title: i18n.def.confirmTitle,
           width: 200,
           height: 200,
           modal: true,
           align: "center",
           valign: "bottom",
           readOnly: _openerLockDoc == "1",
           i18n: i18n.def,
           buttons: {
               "sure": function(){
                   if (!$inputform.valid()) {
                       return false;
                   }
                   var targetValue = $.trim($inputform.find("#cls_enterRateInupt").val());
                   $baseRate.html(targetValue);
                   L140S02CAction.countNowRate(seq, $form);
                   $.thickbox.close();
               },
               "cancel": function(){
                   $.thickbox.close();
               }
           }
       });
   },
   /**
    * 開啟利率視窗
    */
   openL140S02C: function(){
       //請先登錄產品
       var prodKind = L140S02Action.subjCodeAttr['prodKind'];
       if (prodKind == "") {
           //L140M01A.msg010=尚未登錄產品種類
           API.showErrorMessage(i18n.cls1201s27["L140M01A.msg010"]);
           return false;
       }
       
       var subjCode = L140S02Action.subjCodeAttr['subjCode'];
       if (subjCode == "") {
           //L140M01A.msg047=尚未登錄授信科目
           API.showErrorMessage(i18n.cls1201s27["L140M01A.msg047"]);
           return false;
       }
       
       L140S02CAction.prodKind = prodKind;
       var initJson = {};
       // 為了撈出利率thickbox裡的一個備註欄位而已，先裝到jsonArray裡為了inject進欄位
       _M.doAjax({
           action: "queryL120S19A",
           success: function(obj){
        	   initJson = obj;
        	   _M.doAjax({
        		   action: "queryL140S02C",
        		   data: {
        			   L140S02ASeq: L140S02Action.L140S02ASeq
        		   },
        		   success: function(obj){
        			   TEST.start("L140S02CAction.init");
        			   obj = $.extend(initJson, obj);
        			   L140S02CAction.init(obj);
        			   TEST.end("L140S02CAction.init");
        			   TEST.log();
        			   
        		   }
        	   });
           }
       });
       
       var $form = $("#" + L140S02CAction.formId);
       
       $("#L140S02CBox").thickbox({
           //L140S02A.rateDesc=利率
           title: i18n.cls1201s27["L140S02A.rateDesc"],
           width: 800,
           height: 500,
           modal: true,
           readOnly: _openerLockDoc == "1",
           i18n: i18n.def,
           buttons: {
               "saveData": function(){
               
                   if (!$form.valid()) {
                       //page5.151=尚有欄位未填妥，請完成後再執行此動作。
                       API.showErrorMessage(i18n.cls1201s27["page5.151"]);
                       return false;
                   }
                   
                   var haveError = false;
                   var saveData = {};
                   var errorMsg1 = "";
                   var errorMsg2 = "";
                   var count = 0;
                   var endNum;
                   var lnyear = L140S02Action.subjCodeAttr['lnYear'] * 12;
                   var lnmonth = L140S02Action.subjCodeAttr['lnMonth'] * 1;
                   var sumNum = lnyear + lnmonth;
                   //計息方式
                   var intWay = $form.find("#intWay").val();
                   $form.find("[class^=isUseBox_]").each(function(){
                       var $this = $(this);
                       var className = $this.attr("class");
                       var className_seq = className.replace("isUseBox_", "");
                       var allData = $this.serializeData();
                       var isUse = $form.find("#isUseBox_" + className_seq).is(":checked");
                       
                       allData["isUseBox"] = $form.find("#isUseBox_" + className_seq + ":checked").val() || "N";
                       var bgnNum_Val = parseInt($form.find("#bgnNum_" + className_seq).val(), 10);
                       var endNum_Val = parseInt($form.find("#endNum_" + className_seq).val(), 10);
                       
                       if (isUse) {
                           endNum = endNum_Val;
                           count++
                           if (className_seq != count) {
                               //page5.163=利率不能跳段登打！
                               errorMsg1 = i18n.cls1201s27["page5.163"];
                               haveError = true;
                           }
                           if (intWay == "2" && ((bgnNum_Val == 0 || endNum_Val == 0) || ($.isNaN(bgnNum_Val) || $.isNaN(endNum_Val)))) {
                           
                               //page5.209=計息方式為[期付金]且第{0}段有勾時，期數不可空白！
                               errorMsg1 += "<br>" + i18n.cls1201s27["page5.209"].replace("{0}", className_seq);
                               haveError = true;
                           }
                       }
                       //若利率登錄時如果第一段為空值，要檢查至少要勾選其他不能是空白
                       
                       if (className_seq == 1 && !isUse) {
                           if (!$form.find("#isInputDesc").is(":checked")) {
                               //page5.200=請至少勾選第一段利率或勾選其他輸入內容！
                               errorMsg1 += "<br>" + i18n.cls1201s27["page5.200"];
                               haveError = true;
                           }
                       }
                       
                       //檢查起期不可為0
                       if (className_seq == 1 && bgnNum_Val == 0 && isUse) {
                           //page5.207=第一段起期不可為0
                           errorMsg1 += "<br>" + i18n.cls1201s27["page5.207"];
                           haveError = true;
                       }
                       //檢查起訖期間不可顛倒
                       if (endNum_Val < bgnNum_Val) {
                           //page5.087=第{0}段利率迄期不可小於起期！
                           errorMsg1 += "<br>" + i18n.cls1201s27["page5.087"].replace("{0}", className_seq);
                           haveError = true;
                       }
                       
                       if (className_seq != "1" && isUse) {
                           //前一期迄期
                           var exEndNum_Val = parseInt($form.find("#endNum_" + (className_seq - 1)).val(), 10);
                           //page5.152=第{0}段利率起期需接續前期迄期！
                           if ((exEndNum_Val + 1) != bgnNum_Val) {
                               errorMsg2 += "<br>" + i18n.cls1201s27["page5.152"].replace("{0}", className_seq);
                               haveError = true;
                           }
                       }
                       allData["bgnNum"] = bgnNum_Val;
                       allData["endNum"] = endNum_Val;
                       saveData[className_seq] = allData;
                       L140S02CAction.countNowRate(className_seq, $form);
                   });
                   //檢查最後一期是否大於總期數
                   if (endNum < sumNum && $form.find("#isUseBox_1").is(":checked")) {
                       errorMsg1 = i18n.cls1201s27["page5.202"] + sumNum;
                       haveError = true;
                   }
                   if (haveError) {
                       return API.showErrorMessage(errorMsg1 + "<br/>" + errorMsg2);
                   }
                   var taxRate = $form.find("#taxRate").val();
                   
                   //                    if (intWay == "1") {
                   if (parseFloat(taxRate, 10) <= 0 || parseFloat(taxRate, 10) > 1) {
                       //page5.198=扣稅負擔值必須小於1，但不可為0！！
                       return API.showErrorMessage(i18n.cls1201s27["page5.198"]);
                   }
                   //                    }
                   if (!$form.find("#isUseBox_1").is(":checked") && $form.find("#isInputDesc").is(":checked")) {
                       var $formSR = $("#L140S02CsubmitRate");
                       $("#submitRateBox").thickbox({
                           //L140S02A.rateDesc=利率
                           title: i18n.cls1201s27["L140S02C.submitRate"],
                           width: 350,
                           height: 150,
                           modal: true,
                           readOnly: _openerLockDoc == "1",
                           i18n: i18n.def,
                           buttons: {
                               "saveData": function(){
                                   if ($formSR.valid()) {
                                       $form.find("#submitRate").html($formSR.find("#tmpsubmitRate").val());
                                       _M.doAjax({
                                           action: "saveL140S02C",
                                           formId: L140S02CAction.formId,
                                           data: {
                                               L140S02ASeq: L140S02Action.L140S02ASeq,
                                               L140S02ASubjCode: L140S02Action.subjCodeAttr['subjCode'] ,
                                               L140S02AProdKind: L140S02Action.subjCodeAttr['prodKind'] ,
                                               saveFormData: JSON.stringify(saveData),
												currentApplyCurr: _M.AllFormData["04"]['currentApplyCurr']
                                           },
                                           success: function(obj){
                                               //儲存過清空期付金欄位
                                               L140S02Action.cleanPeriodAmt();
                                               //L140S02Action.formId= "L140S02AForm"
                                               // 沒有期付金相關的畫面，先註解起來
                                               /*
                                               var $periodSTr = $l140s02a.find("#periodSTr");
                                               var $for07Tr = $l140s02a.find("#for07Tr");
                                               
                                               $periodSTr.show();
                                               //當為按月計息或 舊案 無產生期付金對照表
                                               if ((obj.L140S02C_IntWay == "1" || obj.L140S02C_IntWay == "P" || obj.L140S02C_IntWay == "Q") 
                                               		|| L140S02Action._L140S02AData.creatSrc == "0") {
                                                   $periodSTr.hide();
                                               }
                                               
                                               if ($periodSTr.is(":hidden") && $for07Tr.is(":hidden")) {
                                                   $("#periodDiv").hide();
                                               }
                                               */
                                               
                                               //開放可自行修改前期利率欄位值。
                                               //$l140s02a.find("#L140S02C_PreDscr").html(obj.preDscr);//????
                                             
                                               // thickbox裡的span更新
                                               $form.find("#L140S02CDesc").html(obj.L140S02CDesc);
                                               $form.find("#desc").val(obj.desc);
                                               
                                               $("#preDscr").attr("readonly", "readonly");

                                               // 儲存L120S19A的利率、備註欄位，寫一筆L120S19B修改歷程
                                               var $l140s02a = $("#" + L140S02Action.formId);
                                               $.ajax({
                                            	   handler : 'cls1141m01formhandler',type : "POST", dataType : "json",
                                            	   data : {
                                            		   formAction : "saveL120S19AForRate",
                                            		   rateDesc : obj.L140S02CDesc,
                                            		   rateRemark : $form.find("#rateRemark").val()
                                            	   },
                                            	   success : function(json) {
                                            		   if(json.mainDocStatus == '01O'){
                                            			   // ao
                                            			   $l140s02a.find("#aoModifyRate").html(DOMPurify.sanitize(json.rateDesc));
                                            			   $l140s02a.find("#aoUpdRateRemark").html(DOMPurify.sanitize(json.rateRemark));
                                                       }else{
                                                    	   // rv
                                                    	   $l140s02a.find("#rvModifyRate").html(DOMPurify.sanitize(json.rateDesc));
                                                    	   $l140s02a.find("#rvUpdRateRemark").html(DOMPurify.sanitize(json.rateRemark));
                                                       }
													   
													   PaperlessCreditCondition.hide_show_docLogBtn();
                                            	   }
                                               });		
                                           	
                                           }
                                       });
                                       $.thickbox.close();
                                   }
                               },
                               "close": function(){
                                   $.thickbox.close();
                               }
                           }
                       });
                   }
                   else {
                       _M.doAjax({
                           action: "saveL140S02C",
                           formId: L140S02CAction.formId,
                           data: {
                               L140S02ASeq: L140S02Action.L140S02ASeq,
                               L140S02ASubjCode: L140S02Action.subjCodeAttr['subjCode'] ,
                               L140S02AProdKind: L140S02Action.subjCodeAttr['prodKind'] ,
                               saveFormData: JSON.stringify(saveData),
								currentApplyCurr: _M.AllFormData["04"]['currentApplyCurr']
                           },
                           success: function(obj){
                               //儲存過清空期付金欄位
                               L140S02Action.cleanPeriodAmt();
                               //L140S02Action.formId= "L140S02AForm"	
                               
                               //開放可自行修改前期利率欄位值。
                               //$("#" + L140S02Action.formId).find("#L140S02C_PreDscr").html(obj.preDscr);//????
                               // thickbox裡的span更新
                               $form.find("#L140S02CDesc").html(obj.L140S02CDesc);
                               $form.find("#desc").val(obj.desc);
                               
                               //開放可自行修改前期利率欄位值。
                               $("#preDscr").attr("readonly", "readonly");
                               
                               // 儲存L120S19A的利率、備註欄位，寫一筆L120S19B修改歷程
                               var $l140s02a = $("#" + L140S02Action.formId);
                               $.ajax({
                            	   handler : 'cls1141m01formhandler',type : "POST", dataType : "json",
                            	   data : {
                            		   formAction : "saveL120S19AForRate",
                            		   rateDesc : obj.L140S02CDesc,
                            		   rateRemark : $form.find("#rateRemark").val()
                            	   },
                            	   success : function(json) {
                            		   if(json.mainDocStatus == '01O'){
                            			   // ao                                 
                            			   $l140s02a.find("#aoModifyRate").html(DOMPurify.sanitize(json.rateDesc));
                            			   $l140s02a.find("#aoUpdRateRemark").html(DOMPurify.sanitize(json.rateRemark));
                                       }else{
                                    	   // rv
                                    	   $l140s02a.find("#rvModifyRate").html(DOMPurify.sanitize(json.rateDesc));
                                    	   $l140s02a.find("#rvUpdRateRemark").html(DOMPurify.sanitize(json.rateRemark));
                                       }
									   
									   PaperlessCreditCondition.hide_show_docLogBtn();
                            	   }
                               });		
                               
                           }
                       });
                   }
                   
               },
               "close": function(){
                   //開放可自行修改前期利率欄位值。
                   $("#preDscr").attr("readonly", "readonly");
                   $.thickbox.close();
               }
           }
       });
   }
   
};

L140S02Action = {
		L140S02ASeq:1, // 只有一筆產品!!
		itemCode: {},
		subjCodeAttr:{},// 放本來存在subjCode Option裡的一些資訊
		formId: "CLS1201S27Form",//最外層的畫面form
	    //清除期付金欄位
	    cleanPeriodAmt: function(){
	    	// 沒有期付金相關的畫面，先註解起來
	    	/*
	        var $form = $("#" + L140S02Action.formId);
	        $form.find("#periodAmt").val("");
	        $form.find("[name=periodChk]:checked").removeAttr("checked");
	        */
	    },
		/**
	     * 初始化下拉選單
	     * @param {Object} formId
	     */
	    initItem: function(formId){
	    	ilog.debug("@PanelAction05 :: initItem(...) > formId="+(formId||'') +"" );
	    	if(true){
	    		if(formId=="L140S02AForm"){
	    			// 沒有抵利型房貸說明的畫面，先註解起來
	    			/*
	    			$("#" + formId+" #isCredit").setItems({
	        			item: API.loadOrderCombosAsList("L140S02F_isCredit")["L140S02F_isCredit"],
	                    format: "{key}"
	    		    });
	    		    */	
	    		}else{
	    			//...
	    		}    		    		
	    	}
	        //產生下拉選單
	        var $div = $("#" + formId).find("[itemType]");
	        var allKey = [];
	        $div.each(function(){
	            allKey.push($(this).attr("itemType"));
	        });
	        L140S02Action.itemCode = API.loadCombos(allKey);
	        $div.each(function(){
	            var $obj = $(this);
	            var itemType = $obj.attr("itemType");
	            if (itemType) {
	                var format = $obj.attr("itemFormat") || "{value} - {key}";
	                $obj.setItems({
	                    space: $obj.attr("space") || true,
	                    item: L140S02Action.itemCode[itemType],
	                    format: format,
	                    //sort: "asc",
	                    size: $obj.attr("itemSize")
	                });
	            }
	        });
	    },
	    openRateBox : function(){
	    	if(_M.tabMainId == ''){
	    		$.ajax({
	    			handler : 'cls1141m01formhandler',type : "POST", dataType : "json",
	    			data : {
	    				formAction : "getTabMainIdFromNoPaper",
	    				mainId : responseJSON.mainid,
	    				L140S02ASeq : L140S02Action.L140S02ASeq
	    			},
	    			success : function(json) {
	    				_M.tabMainId = json.tabMainId;
	    				_M.AllFormData["04"]['currentApplyCurr'] = json.currentApplyCurr;
	    				L140S02Action.subjCodeAttr['prodKind'] = json.prodKind;
	    				L140S02Action.subjCodeAttr['subjCode'] = json.subjCode;
	    				L140S02Action.subjCodeAttr['lnYear'] = json.lnYear;
	    				L140S02Action.subjCodeAttr['lnMonth'] = json.lnMonth;
	    				L140S02Action.subjCodeAttr['rIntWay'] = json.rIntWay;
	    				L140S02Action.subjCodeAttr['subjCode2'] = json.subjCode2;
	    				L140S02CAction.openL140S02C();
	    			}
	    		});		
	    	}else{
	    		L140S02CAction.openL140S02C();
	    	}
	    } 
}