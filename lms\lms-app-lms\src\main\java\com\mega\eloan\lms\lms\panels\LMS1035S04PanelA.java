package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 消金信用評等模型(擔保品)
 * 此 panel 為純 html(不含 js)，被以下 panel include
 * ● LMS1035S04Panel
 * ● LMS1405S02Panel05
 * 
 * 若直接在 LMS1405S02Panel05 去 add LMS1035S04Panel(沒有A)
 * 因 LMS1035S04Panel 裡面有 js
 * 會抓不到 js 的 path,出現 HTTP 404
 * </pre>
 * 
 * @since 2015/3/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/3/1,EL08034,new
 *          </ul>
 */
public class LMS1035S04PanelA extends Panel {
	private static final long serialVersionUID = 1L;
	private boolean cmsType_withItem;
	/*
	 直接在 html 寫 combokey="c121s01a_cmsType_TH"  仍不會排序
	 ●評等文件的 panel 由 LMS1035M01.js 來 init
	 ●額度明細表內的 panel, 則帶 combokey(因為在評等文件，已選擇了)
	 */
	// public LMS1035S04PanelA(String id, boolean cmsType_withItem) {
	// super(id);
	// add(LMSUtil.genHtmlComponent("_cmsType_withItem", cmsType_withItem));
	// add(LMSUtil.genHtmlComponent("_cmsType_noItem", !cmsType_withItem));
	// }

	public LMS1035S04PanelA(String id, boolean cmsType_withItem) {
		super(id);
		this.cmsType_withItem = cmsType_withItem;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		model.addAttribute("_cmsType_withItem", cmsType_withItem);
		model.addAttribute("_cmsType_noItem", !cmsType_withItem);
	}
}
