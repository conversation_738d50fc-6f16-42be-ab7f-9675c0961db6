---------------------------------------------------------
-- LMS.C999S01A 個金約據書產品種類檔
---------------------------------------------------------
--DROP TABLE LMS.C999S01A;
CREATE TABLE LMS.C999S01A (
	OID           CHAR(32)      not null,
	<PERSON>IN<PERSON>        CHAR(32)     ,
	UID           CHAR(32)     ,
	ITEMNO        DECIMAL(3,0) ,
	<PERSON>LETEDTIME   TIMESTAMP    ,
	CNTRN<PERSON>        CHAR(12)     ,
	<PERSON><PERSON><PERSON>IN<PERSON>      CHAR(2)      ,
	<PERSON>UBJCODE      VARCHAR(8)   ,
	LOA<PERSON>UR<PERSON>      CHAR(3)      ,
	<PERSON><PERSON>NAMT       DECIMAL(15,0),
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATE<PERSON>ME    TIMESTAMP    ,

	constraint P_C999S01A PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC999S01A01;
CREATE UNIQUE INDEX LMS.XC999S01A01 ON LMS.C999S01A   (MAINID, UID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C999S01A IS '個金約據書產品種類檔';
COMMENT ON LMS.C999S01A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	UID           IS 'UID', 
	ITEMNO        IS '項次', 
	DELETEDTIME   IS '刪除註記', 
	CNTRNO        IS '額度序號', 
	PRODKIND      IS '產品種類', 
	SUBJCODE      IS '科目', 
	LOANCURR      IS '幣別', 
	LOANAMT       IS '金額', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
