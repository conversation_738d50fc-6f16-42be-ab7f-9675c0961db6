package com.mega.eloan.lms.dao.impl;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L140M01QDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01Q;

/**
 * <pre>
 * 大陸地區授信業務控管註記 
 * </pre>
 * @since  2013/7/15
 * <AUTHOR>
 * @version <ul>
 *           <li>2013/7/15,007625,new
 *          </ul>
 */
@Repository
public class L140M01QDaoImpl extends LMSJpaDao<L140M01Q, String> implements
		L140M01QDao {

	@Override
	public L140M01Q findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

}