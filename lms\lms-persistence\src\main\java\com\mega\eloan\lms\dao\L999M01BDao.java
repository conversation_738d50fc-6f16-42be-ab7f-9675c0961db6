/* 
 * L999M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L999M01B;


/** 企金約據書立約人檔 **/
public interface L999M01BDao extends IGenericDao<L999M01B> {

	L999M01B findByOid(String oid);
	
	List<L999M01B> findByMainId(String mainId);
	
	L999M01B findByUniqueKey(String mainId, String type);
	
	L999M01B findByUniqueKey(String mainId, String custId, String dupNo, String type);

	List<L999M01B> findByIndex01(String mainId, String custId, String dupNo, String type);

	List<L999M01B> findByCustIdDupId(String custId,String DupNo);
}