var initDfd = initDfd || $.Deferred();
initDfd.done(function(json){
	excuteQuery(); 
	
	
	var gridViewBox=$("#gridViewBox").iGrid({
		handler : 'lms9990gridhandler',
		height : 225,
/*
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
*/		
		sortname : 'createTime|contractType',
		sortorder: 'desc|asc',
		postData : {
			formAction : "queryC999M01AList",
			srcMainId:json.srcMainId
		},
		multiselect : false,
		rowNum : 15,
		colModel : [  {
			colHeader :i18n.lms9990m06['C999M01AM06.createTime'],// "建立日期",
			name : 'createTime',
			width : 120,
			align: "center",
			sortable : true
		}, {
			colHeader : i18n.lms9990m06['C999M01AM06.contractKind'],// "約據書類型",
			name : 'contractKind',
			width : 200,
			align: "center",
			sortable : true,
            formatter: 'click',
            onclick: openDoc			
		}, {
			colHeader : i18n.lms9990m06['C999M01AM06.contractType'],// "約據書種類",
			name : 'contractType',
			width : 140,
			align: "center",
			sortable : true
		}, {
			colHeader : i18n.lms9990m06['C999M01AM06.updateTime'],// "異動日期",
			name : 'updateTime',
			width : 140,
			align: "center",
			sortable : true
		}, {
			colHeader : i18n.lms9990m06['C999M01AM06.updater'],// "異動人員",
			name : 'updater',
			width : 140,
			align: "center",
			sortable : true
		}, {
			name : 'mainId',
			hidden : true
		}, {
			name : 'oid',
			hidden : true
		}, {
			name : 'docStatus',
			hidden : true
		}, {
			name : 'uid',
			hidden : true
		}, {
			name : 'contractType2',
			hidden : true
		}],ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = gridViewBox.getRowData(rowid);
			openDoc(null, null, data);
		}
	});

	/***輸入查詢ID視窗***/
	function excuteQuery() {
		filterGrid({
			srcMainId : json.srcMainId
   	 	});
	}
	//grid資料篩選
	function filterGrid(sendData){
		$("#gridViewBox").jqGrid("setGridParam", {
			 postData : $.extend({
			},sendData), 
			search: true
		}).trigger("reloadGrid");
	}
	//依照種類開啟個金約據書子畫面
	function openDoc(cellvalue, options, rowObject) {
		var actionUrl;
		var isNew = false;
		if(rowObject.contractType2 && rowObject.contractType2.indexOf('W') != -1){
			$.capFileDownload({					     
				handler:"lmsdownloadformhandler",
	            data: {
					oidFor140 : rowObject.oid,
		            mainId: rowObject.mainId,
		            contractType: rowObject.uid,
					//contractKind : $("#contractKind").val(),
					chkColIdVal : rowObject.contractType2,
		            fileDownloadName: "LMS9990" + rowObject.contractType2 + "C.doc",
		            serviceName: "lms9990doc02service"
	            }
	        });			
		}else{
			if(rowObject.uid == 'A'){
				// 一般
				actionUrl = '../lms9990m07/01';
				isNew = true;
			}else if(rowObject.uid == 'B'){
				// 政策性留學貸款
				actionUrl = '../lms9990m08/01';
				isNew = true;
			}			
		}
		if(isNew){
			$.form.submit({
				url : actionUrl,
				data : {
					mainId : rowObject.mainId,
					mainDocStatus : rowObject.docStatus,
					mainOid : rowObject.oid,
					//由於c999m01a的contractType帶為中文資訊 uid又一定無值 所以用contractType的英文代號存入uid帶入
					contractType : rowObject.uid,
					txCode : $("#txCode").val()
				},
				target : "_blank"
			});			
		}
	}
});