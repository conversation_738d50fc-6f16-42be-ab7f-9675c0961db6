package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.sql.Types;
import java.util.Date;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.SqlOutParameter;
import org.springframework.jdbc.core.SqlParameter;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;

@Service
public class MisStoredProcServiceImpl extends AbstractMFAloanJdbc implements
		MisStoredProcService {

	private static Logger logger = LoggerFactory
			.getLogger(MisStoredProcServiceImpl.class);

	public Map<String, Object> callLNSP0050(String ownBrId, String unitCode,
			String classCD) {
		String spName = "LN.LNSP0050";
		Object[] inParamsVal = { ownBrId, unitCode, classCD };
		SqlParameter[] params = { new SqlParameter("SP_BR_NO", Types.VARCHAR),
				new SqlParameter("SP_UNIT_CODE", Types.VARCHAR),
				new SqlParameter("SP_CLASS_CD", Types.VARCHAR),
				new SqlOutParameter("cntrNo", Types.VARCHAR),
				new SqlOutParameter("chkFlag", Types.VARCHAR),
				new SqlOutParameter("errMsg", Types.VARCHAR) };

		return this.getJdbc().callSPForMap(spName, params, inParamsVal);

	}
	
	public Map<String, Object> callLNSP0600(String cntrNo, String esgFlag) {
		String spName = "LN.LNSP0600";
		Object[] inParamsVal = { cntrNo, esgFlag };
		SqlParameter[] params = {
				new SqlParameter("SP_CONTRACT", Types.VARCHAR),
				new SqlParameter("SP_ESGSUNRE", Types.VARCHAR),
				new SqlOutParameter("SP_RETURN", Types.VARCHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.VARCHAR) };

		return this.getJdbc().callSPForMap(spName, params, inParamsVal);

	}

	@Override
	public Map<String, Object> callLNSP0130(String ownBrId, String eName) {
		String spName = "LN.LNSP0130";
		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		if (Util.isNotEmpty(ownBrId) && Util.isNotEmpty(eName)) {
			sb.append("01").append(ownBrId).append("TWUSY").append(eName);
		}
		Object[] inParamsVal = { "BLACKLST", sb.toString() };
		SqlParameter[] params = {
				new SqlParameter("SP_TXN_CODE", Types.VARCHAR),
				new SqlParameter("SP_INPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_OUTPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_RETURN", Types.VARCHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.VARCHAR) };

		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	@Override
	public Map<String, Object> callLNSP0270(String ownBrId, String idType,
			String custID, String companyCountry, String localCountry,
			String licenseType, String licenseNO, String business_CD,
			String cName, String lName, String codePage, String eName,
			String birthday, String localID, String busSubCD, String email) {
		String spName = "LN.LNSP0270";
		Object[] inParamsVal = { ownBrId, idType, custID, companyCountry,
				localCountry, licenseType, licenseNO, business_CD, cName,
				lName, codePage, eName, birthday, localID, busSubCD, email };
		SqlParameter[] params = { new SqlParameter("SP_BR_NO", Types.CHAR),
				new SqlParameter("SP_ID_TYPE", Types.CHAR),
				new SqlParameter("SP_TW_SWIFT_ID", Types.CHAR),
				new SqlParameter("SP_HEAD_NATION", Types.CHAR),
				new SqlParameter("SP_REG_NATION", Types.CHAR),
				new SqlParameter("SP_LICENSE_TYPE", Types.CHAR),
				new SqlParameter("SP_LICENSE_NO", Types.VARCHAR),
				new SqlParameter("SP_BUSINESS_CD", Types.CHAR),
				new SqlParameter("SP_CNAME", Types.VARCHAR),
				new SqlParameter("SP_LNAME", Types.VARCHAR),
				new SqlParameter("SP_CODE_PAGE", Types.CHAR),
				new SqlParameter("SP_ENAME", Types.VARCHAR),
				new SqlParameter("SP_BIRTHDAY", Types.CHAR),
				new SqlParameter("SP_LOCAL_ID", Types.CHAR),
				new SqlParameter("SP_BUS_SUB_CD", Types.CHAR),
				new SqlParameter("SP_EMAIL", Types.CHAR),
				new SqlOutParameter("MEGA_ID", Types.CHAR),
				new SqlOutParameter("CNAME", Types.CHAR),
				new SqlOutParameter("LNAME", Types.CHAR),
				new SqlOutParameter("ENAME", Types.CHAR),
				new SqlOutParameter("RETURN", Types.CHAR),
				new SqlOutParameter("ERROR_MSG", Types.CHAR) };

		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	@Override
	public Map<String, Object> callLNSP0271(String eName) {
		String spName = "LN.LNSP0271";
		Object[] inParamsVal = { eName };
		SqlParameter[] params = { new SqlParameter("SP_ENAME", Types.VARCHAR) };

		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	@Override
	public Map<String, Object> callLNSP0290(String caseBrid, String caseNo,
			String custId, String suspendCode, String suspendMons,
			String loanDate, String ovDate, String setBrno, String setUser,
			String mainId) {
		String spName = "LN.LNSP0290";
		Object[] inParamsVal = { caseBrid, caseNo, custId, suspendCode,
				Util.parseInt(suspendMons), loanDate, ovDate, setBrno, setUser,
				mainId };
		SqlParameter[] params = { new SqlParameter("SP_BRANCH", Types.CHAR),
				new SqlParameter("SP_SET_DOC_NO", Types.CHAR),
				new SqlParameter("SP_CUST_ID", Types.CHAR),
				new SqlParameter("SP_SUSPEND_CODE", Types.CHAR),
				new SqlParameter("SP_SUSPEND_MONS", Types.DECIMAL),
				new SqlParameter("SP_LOAN_DATE", Types.CHAR),
				new SqlParameter("SP_OV_DATE", Types.CHAR),
				new SqlParameter("SP_SET_DEPART", Types.CHAR),
				new SqlParameter("SP_SET_EMP_NO", Types.CHAR),
				new SqlParameter("SP_UNIVERSAL_ID", Types.CHAR),
				new SqlOutParameter("SP_RETURN", Types.CHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.CHAR) };
		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	@Override
	public Map<String, Object> callLNSP0291(String caseBrid, String caseNo,
			String custId, String suspendCode, String suspendMons,
			String setBrno, String setUser, String mainId) {
		String spName = "LN.LNSP0291";
		Object[] inParamsVal = { caseBrid, caseNo, custId, suspendCode,
				suspendMons, setBrno, setUser, caseNo, mainId };
		SqlParameter[] params = { new SqlParameter("SP_BRANCH", Types.CHAR),
				new SqlParameter("SP_SET_DOC_NO", Types.CHAR),
				new SqlParameter("SP_CUST_ID", Types.CHAR),
				new SqlParameter("SP_SUSPEND_CODE", Types.CHAR),
				new SqlParameter("SP_SUSPEND_MONS", Types.DECIMAL),
				new SqlParameter("SP_SET_DEPART", Types.CHAR),
				new SqlParameter("SP_SET_EMP_NO", Types.CHAR),
				new SqlParameter("SP_MOD_DOC_NO", Types.CHAR),
				new SqlParameter("SP_UNIVERSAL_ID", Types.CHAR),
				new SqlOutParameter("SP_RETURN", Types.CHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.CHAR) };
		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	@Override
	public Map<String, Object> addStop(String caseBrid, String caseNo,
			String custId, String suspendCode, String suspendMons,
			String setBrno, String setUser, String oid) {
		String spName = "LN.LNSP0290";
		Object[] inParamsVal = { caseBrid, caseNo, custId, suspendCode,
				suspendMons, "0001-01-01", "0001-01-01", setBrno, setUser, oid };
		SqlParameter[] params = { new SqlParameter("SP_BRANCH", Types.CHAR),
				new SqlParameter("SP_SET_DOC_NO", Types.CHAR),
				new SqlParameter("SP_CUST_ID", Types.CHAR),
				new SqlParameter("SP_SUSPEND_CODE", Types.CHAR),
				new SqlParameter("SP_SUSPEND_MONS", Types.DECIMAL),
				new SqlParameter("SP_LOAN_DATE", Types.CHAR),
				new SqlParameter("SP_OV_DATE", Types.CHAR),
				new SqlParameter("SP_SET_DEPART", Types.CHAR),
				new SqlParameter("SP_SET_EMP_NO", Types.CHAR),
				new SqlParameter("SP_UNIVERSAL_ID", Types.CHAR),
				new SqlOutParameter("SP_RETURN", Types.CHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.CHAR) };
		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	@Override
	public Map<String, Object> modifyStop(String caseBrid, String caseNo,
			String custId, String suspendCode, String suspendMons,
			String setBrno, String setUser, String newCaseNo, String oid) {
		String spName = "LN.LNSP0291";
		Object[] inParamsVal = { caseBrid, caseNo, custId, suspendCode,
				suspendMons, setBrno, setUser, newCaseNo, oid };
		SqlParameter[] params = { new SqlParameter("SP_BRANCH", Types.CHAR),
				new SqlParameter("SP_SET_DOC_NO", Types.CHAR),
				new SqlParameter("SP_CUST_ID", Types.CHAR),
				new SqlParameter("SP_SUSPEND_CODE", Types.CHAR),
				new SqlParameter("SP_SUSPEND_MONS", Types.DECIMAL),
				new SqlParameter("SP_SET_DEPART", Types.CHAR),
				new SqlParameter("SP_SET_EMP_NO", Types.CHAR),
				new SqlParameter("SP_MOD_DOC_NO", Types.CHAR),
				new SqlParameter("SP_UNIVERSAL_ID", Types.CHAR),
				new SqlOutParameter("SP_RETURN", Types.CHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.CHAR) };
		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	@Override
	public Map<String, Object> delStop(String caseBrid, String caseNo,
			String custId, String suspendCode, String suspendMons,
			String setBrno, String setUser, String newCaseNo, String oid) {
		String spName = "LN.LNSP0291";
		Object[] inParamsVal = { caseBrid, caseNo, custId, suspendCode,
				suspendMons, setBrno, setUser, newCaseNo, oid };
		SqlParameter[] params = { new SqlParameter("SP_BRANCH", Types.CHAR),
				new SqlParameter("SP_SET_DOC_NO", Types.CHAR),
				new SqlParameter("SP_CUST_ID", Types.CHAR),
				new SqlParameter("SP_SUSPEND_CODE", Types.CHAR),
				new SqlParameter("SP_SUSPEND_MONS", Types.DECIMAL),
				new SqlParameter("SP_SET_DEPART", Types.CHAR),
				new SqlParameter("SP_SET_EMP_NO", Types.CHAR),
				new SqlParameter("SP_MOD_DOC_NO", Types.CHAR),
				new SqlParameter("SP_UNIVERSAL_ID", Types.CHAR),
				new SqlOutParameter("SP_RETURN", Types.CHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.CHAR) };
		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	@Override
	public Map<String, Object> callLNSP0150(String branch, String custId,
			String dupNo) {
		String spName = "LN.LNSP0150";
		Object[] inParamsVal = { CapString.fillBlankTail(custId, 10) + dupNo,
				branch };

		SqlParameter[] params = { new SqlParameter("SP_CUST_ID", Types.CHAR),
				new SqlParameter("SP_BR_NO", Types.CHAR),
				new SqlOutParameter("SP_RETURN", Types.CHAR),
				new SqlOutParameter("SP_MAIN_CUST_FLAG", Types.CHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.CHAR) };
		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	public Map<String, Object> callLNSP0330(String cntrNo, String begDate,
			String endDate) {
		String spName = "LN.LNSP0330";
		Object[] inParamsVal = { cntrNo, begDate, endDate };
		SqlParameter[] params = {
				new SqlParameter("SP_I_CONTRACT", Types.CHAR),
				new SqlParameter("SP_I_BEG_DATE", Types.CHAR),
				new SqlParameter("SP_I_END_DATE", Types.CHAR),
				new SqlOutParameter("SP_O_RETURN", Types.CHAR),
				new SqlOutParameter("SP_O_ERROR_MSG", Types.CHAR),
				new SqlOutParameter("SP_O_AREA", Types.VARCHAR) };

		return this.getJdbc().callSPForMap(spName, params, inParamsVal);

	}

	@Override
	public Map<String, Object> callSCMLUINQ(String idDup) {
		String spName = "LN.LNSP0130";

		Object[] inParamsVal = { "SCMLUINQ",
				"01" + (Util.addSpaceWithValue(idDup, 11)) + "02" };
		SqlParameter[] params = {
				new SqlParameter("SP_TXN_CODE", Types.VARCHAR),
				new SqlParameter("SP_INPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_OUTPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_RETURN", Types.VARCHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.VARCHAR) };

		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	@Override
	public Map<String, Object> callTRPAYSQ1(String idDup) {
		String spName = "LN.LNSP0130";

		Object[] inParamsVal = { "TRPAYSQ1",
				("    " + Util.addSpaceWithValue(idDup, 11)) };
		SqlParameter[] params = {
				new SqlParameter("SP_TXN_CODE", Types.VARCHAR),
				new SqlParameter("SP_INPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_OUTPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_RETURN", Types.VARCHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.VARCHAR) };

		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	@Override
	public Map<String, Object> callLNPS188(String accNo) {
		return callLNPS188("5", accNo, "TWD");
	}

	@Override
	public Map<String, Object> callLNPS188(String ac_type, String acctNo,
			String curr) {
		String spName = "LN.LNSP0130";

		Object[] inParamsVal = { "LNPS188", ac_type + acctNo + curr };
		SqlParameter[] params = {
				new SqlParameter("SP_TXN_CODE", Types.VARCHAR),
				new SqlParameter("SP_INPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_OUTPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_RETURN", Types.VARCHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.VARCHAR) };

		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	/**
	 * I-107-0260_05097_B1001 Web e-Loan企金授信系統增加提示訊息【該客戶屬於巴拿馬文件名單】
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> callCMPEPUPD(String custId, String dupNo) {
		String spName = "LN.LNSP0130";
		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		sb.append("03").append(CapString.fillBlankTail(custId, 10) + dupNo)
				.append("01").append("101").append(" ");
		String channel = "CMPEPUPD";
		Object[] inParamsVal = { "CMPEPUPD", sb.toString() };
		SqlParameter[] params = {
				new SqlParameter("SP_TXN_CODE", Types.VARCHAR),
				new SqlParameter("SP_INPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_OUTPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_RETURN", Types.VARCHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.VARCHAR) };

		return this.getJdbc().callSPForMap(spName, params, inParamsVal);

	}

	/**
	 * I-110-0329_11557_B1001
	 * 針對既有自然人客戶增加KYC系統異常機制，KYC異常時可BYPASS
	 * 
	 * @param custId 查詢客戶之身分證號 / 統編
	 * @param dupNo 重複序號
	 * @param dept 查詢客戶之業務往來項目   
	 * @param brId 交易分行   
	 * @param userId 交易行員
	 * @return
	 */
	@Override
	public Map<String, Object> callSCMLUINQ(String custId, String dupNo, String dept,
			 String brId, String userId) {
		String spName = "LN.LNSP0130";
		String txnCode = "ELOAN"; //出報表用的字串
		String userIdFive = StringUtils.right(userId, 5);
		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		sb.append("01").append(CapString.fillBlankTail(custId, 10) + dupNo)
				.append(CapString.fillZeroHead(dept, 2)).append(brId).append(CapString.fillBlankHead(userIdFive, 5))
				.append(CapString.fillBlankTail(txnCode, 10));
		StringUtils.right(userId, 5);
		Object[] inParamsVal = { "SCMLUINQ", sb.toString() };
		logger.info("CALL LN.LNSP0130 SCMLUINQ SP_INPUT_AREA:" + sb.toString());
		
		SqlParameter[] params = {
				new SqlParameter("SP_TXN_CODE", Types.VARCHAR),
				new SqlParameter("SP_INPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_OUTPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_RETURN", Types.VARCHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.VARCHAR) };

		return this.getJdbc().callSPForMap(spName, params, inParamsVal);

	}
	
	/**
	 * 
	 * 取得票據退票及拒往資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @param ejcicVirtualId
	 * @return
	 */
	@Override
	public Map<String, Object> getRefundAndRejectedInfo(String custId,
			String dupno, String ejcicVirtualId) {

		String spName = "LN.LNSP0130";

		Object[] inParamsVal = {
				"LNPS166",
				"2              " + CapString.fillBlankTail(custId, 10) + dupno
						+ ejcicVirtualId };// 2:拒往戶

		SqlParameter[] params = { new SqlParameter("SP_TXN_CODE", Types.CHAR),
				new SqlParameter("SP_INPUT_AREA", Types.CHAR),
				new SqlOutParameter("SP_OUTPUT_AREA", Types.CHAR),
				new SqlOutParameter("SP_RETURN", Types.CHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.CHAR) };

		Map<String, Object> rtnMap = this.getJdbc().callSPForMap(spName,
				params, inParamsVal);

		logger.info("SP_RETURN = {" + rtnMap.get("SP_RETURN")
				+ "}, SP_OUTPUT_AREA = {" + rtnMap.get("SP_OUTPUT_AREA")
				+ "}, SP_ERROR_MSG = {" + rtnMap.get("SP_ERROR_MSG") + "}");

		if ("YES".equals(rtnMap.get("SP_RETURN"))) {

			String result = String.valueOf(rtnMap.get("SP_OUTPUT_AREA"));

			if ("0000".equals(result.substring(51, 55))) {
				rtnMap.put("DNGER-YN", result.substring(55, 56));
				rtnMap.put("DNGER-DATE", result.substring(56, 64));
				rtnMap.put("DNGER-D-DATE", result.substring(64, 72));
				rtnMap.put("REJ-YN", result.substring(72, 73));
				rtnMap.put("REJ-DATE", result.substring(73, 81));
				rtnMap.put("REJ-CNT", result.substring(81, 89));
				rtnMap.put("REJ-SWFT", result.substring(89, 92));
				rtnMap.put("REJ-AMT", result.substring(92, 104));
			} else {
				rtnMap.put("ERROR",
						"拒往戶資料查詢出錯，請聯絡資訊處!!" + result.substring(4, 44));
			}
		} else {
			rtnMap.put("ERROR", "拒往戶資料查詢出錯，請聯絡資訊處!!");
		}

		return rtnMap;
	}

	/**
	 * J-110-0182_05097_B1002 Web e-Loan國內企金授信配合經濟部紓困貸款更改為非紓困案仍需持續補貼，修改相關程式。
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param rescueItem
	 * @param rescueRate
	 * @return
	 */
	@Override
	public Map<String, Object> callLNSP0141(String custId, String dupNo,
			String cntrNo, String rescueItem, BigDecimal rescueRate) {
		String spName = "LN.LNSP0141";
		Object[] inParamsVal = { CapString.fillBlankTail(custId, 10) + dupNo,
				cntrNo, rescueItem, rescueRate };

		// CREATE PROCEDURE LN.LNSP0141
		// (IN SP_CUST_ID CHAR(11),
		// IN SP_CONTRACT CHAR(12),
		// IN SP_RESCUEITEM CHAR(03),
		// IN SP_RESCUERATE DEC(7,5))
		// DYNAMIC RESULT SETS 1
		// EXTERNAL NAME LNSP0141
		// LANGUAGE COBOL
		// COLLID STPLN
		// WLM ENVIRONMENT DB2PSQL
		// PARAMETER STYLE GENERAL
		// COMMIT ON RETURN YES;
		// GRANT EXECUTE ON PROCEDURE LN.LNSP0141 TO EJCIC, ELOANWEB;

		SqlParameter[] params = { new SqlParameter("SP_CUST_ID", Types.CHAR),
				new SqlParameter("SP_CONTRACT", Types.CHAR),
				new SqlParameter("SP_RESCUEITEM", Types.CHAR),
				new SqlParameter("SP_RESCUERATE", Types.DECIMAL) };
		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}
	
	@Override
	public Map<String, Object> insertLNF087(String contract, String controlCd, BigDecimal ltvRate, 
											String locationCd, BigDecimal appAmt, String plusReason, 
											String jcicMark, String cocollFg, BigDecimal sumFactamt,
											String partFund, String plusMemo, String regPurpose, 
											BigDecimal estAmt, BigDecimal lawval, String restrict, 
											String hpHouse, String planArea, String pUsetype, 
											String pLoanuse, String collChar, String keepLawval, 
											BigDecimal site3no, String site4no, String collCharM, 
											BigDecimal houseAge, BigDecimal loanamt, String version, 
											String hloanlimit, String hloanlimit2, Date enddate, 
											Date lstdate, BigDecimal timeval, String custId, String dupNo) {
		
		String spName = "LN.LNSP0400";
		Object[] inParamsVal = { contract, controlCd, ltvRate, locationCd, appAmt, plusReason,
								 jcicMark, cocollFg, sumFactamt, partFund, plusMemo, regPurpose, estAmt,
								 lawval, restrict, hpHouse, planArea, pUsetype, pLoanuse, collChar, 
								 keepLawval, site3no, site4no, collCharM, houseAge, loanamt, version, hloanlimit, 
								 hloanlimit2, enddate, lstdate, timeval, custId, dupNo};
		
		String str = "Insert Into LN.LNF087(LNF087_CONTRACT, LNF087_CONTROL_CD, LNF087_LTV_RATE, LNF087_LOCATION_CD, LNF087_APP_AMT," +
						"LNF087_PLUS_REASON, LNF087_JCIC_MARK, LNF087_COCOLL_FG, LNF087_SUM_FACTAMT, LNF087_PART_FUND, "+
						"LNF087_PLUS_MEMO, LNF087_REG_PURPOSE, LNF087_EST_AMT, LNF087_LAWVAL, LNF087_RESTRICT, "+
						"LNF087_HP_HOUSE, LNF087_PLAN_AREA, LNF087_P_USETYPE, LNF087_P_LOANUSE, LNF087_COLL_CHAR, "+ 
						"LNF087_KEEP_LAWVAL, LNF087_SITE3NO, LNF087_SITE4NO, LNF087_COLL_CHAR_M, LNF087_HOUSE_AGE, "+
						"LNF087_LOANAMT, LNF087_VERSION, LNF087_HLOANLIMIT, LNF087_HLOANLIMIT_2, LNF087_ENDDATE, "+
						"LNF087_LSTDATE, LNF087_TIMEVAL, LNF087_CUST_ID, LNF087_DUP_NO) ";
		String values = "values ('" + contract + "','" + controlCd + "'," + ltvRate + ",'" + locationCd + "'," + appAmt + ",'" + plusReason + "','" + 
		 				jcicMark + "','" + cocollFg + "'," + sumFactamt + ",'" + partFund + "','" + plusMemo + "','" + regPurpose + "'," + 
		 				estAmt + "," + lawval + ",'" + restrict + "','" + hpHouse + "','" + planArea + "','" + pUsetype + "','" + pLoanuse + "','" + 
		 				collChar + "','" + keepLawval + "'," + site3no + ",'" + site4no + "','" + collCharM + "'," + houseAge + "," + loanamt + ",'" + 
		 				version + "','" + hloanlimit + "','" + hloanlimit2 + "'," + enddate + "," + lstdate + "," + timeval + ",'" + custId + "','" + dupNo + ");";
		
		logger.debug(str + values);
		
		SqlParameter[] params = { 
				new SqlParameter("LNF087_CONTRACT", Types.CHAR),
				new SqlParameter("LNF087_CONTROL_CD", Types.CHAR),
				new SqlParameter("LNF087_LTV_RATE", Types.DECIMAL),
				new SqlParameter("LNF087_LOCATION_CD", Types.CHAR),
				new SqlParameter("LNF087_APP_AMT", Types.DECIMAL),
				new SqlParameter("LNF087_PLUS_REASON", Types.CHAR),
				new SqlParameter("LNF087_JCIC_MARK", Types.CHAR),
				new SqlParameter("LNF087_COCOLL_FG", Types.CHAR),
				new SqlParameter("LNF087_SUM_FACTAMT", Types.DECIMAL),
				new SqlParameter("LNF087_PART_FUND", Types.CHAR),
				new SqlParameter("LNF087_PLUS_MEMO", Types.CHAR),
				new SqlParameter("LNF087_REG_PURPOSE", Types.CHAR),
				new SqlParameter("LNF087_EST_AMT", Types.DECIMAL),
				new SqlParameter("LNF087_LAWVAL", Types.DECIMAL),
				new SqlParameter("LNF087_RESTRICT", Types.CHAR),
				new SqlParameter("LNF087_HP_HOUSE", Types.CHAR),
				new SqlParameter("LNF087_PLAN_AREA", Types.CHAR),
				new SqlParameter("LNF087_P_USETYPE", Types.CHAR),
				new SqlParameter("LNF087_P_LOANUSE", Types.CHAR),
				new SqlParameter("LNF087_COLL_CHAR", Types.CHAR),
				new SqlParameter("LNF087_KEEP_LAWVAL", Types.CHAR),
				new SqlParameter("LNF087_SITE3NO", Types.DECIMAL),
				new SqlParameter("LNF087_SITE4NO", Types.CHAR),
				new SqlParameter("LNF087_COLL_CHAR_M", Types.CHAR),
				new SqlParameter("LNF087_HOUSE_AGE", Types.DECIMAL),
				new SqlParameter("LNF087_LOANAMT", Types.DECIMAL),
				new SqlParameter("LNF087_VERSION", Types.VARCHAR),
				new SqlParameter("LNF087_HLOANLIMIT", Types.CHAR),
				new SqlParameter("LNF087_HLOANLIMIT_2", Types.CHAR),
				new SqlParameter("LNF087_ENDDATE", Types.DATE),
				new SqlParameter("LNF087_LSTDATE", Types.DATE),
				new SqlParameter("LNF087_TIMEVAL", Types.DECIMAL),
				new SqlParameter("LNF087_CUST_ID", Types.CHAR),
				new SqlParameter("LNF087_DUP_NO", Types.CHAR)};
		
		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

	@Override
	public Map<String, Object> callLNSP0130ForUpLnFlag(String custId,
			String dupNo, String empId, String ownBrId) {
		String spName = "LN.LNSP0130";
		//sample
		//call ln.lnsp0130('UPDLNFLG','0273251209  0011850005',?,?,?)
		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		//02
		//+73251209  0
		//+011850
		//+005
		sb.append("02").append(CapString.fillBlankTail(Util.trim(custId), 10)).append(dupNo)
		.append(empId).append(ownBrId);
		
		Object[] inParamsVal = { "UPDLNFLG", sb.toString() };
		SqlParameter[] params = {
				new SqlParameter("SP_TXN_CODE", Types.VARCHAR),
				new SqlParameter("SP_INPUT_AREA", Types.VARCHAR),
				
				new SqlOutParameter("SP_OUTPUT_AREA", Types.VARCHAR),
				new SqlOutParameter("SP_RETURN", Types.VARCHAR),
				new SqlOutParameter("SP_ERROR_MSG", Types.VARCHAR) };

		return this.getJdbc().callSPForMap(spName, params, inParamsVal);
	}

}
