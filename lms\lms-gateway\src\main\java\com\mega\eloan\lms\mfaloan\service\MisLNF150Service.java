package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

public interface MisLNF150Service {
	/**
	 * 取得授信明細檔資料(有帳戶)，無聯貸的資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	List<Map<String, Object>> findByCustIdDup(String custId, String dupNo,
			String brNo);

	/**
	 * 取得授信明細檔資料(有帳戶)，有聯貸母戶的資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	List<Map<String, Object>> findByCustIdDup2(String custId, String dupNo,
			String brNo);

	/**
	 * 取得房貸明細檔資料(有帳戶)，無聯貸的資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	List<Map<String, Object>> findByCustIdDupOnlyHouse(String custId,
			String dupNo, String brNo);

	/**
	 * 取得房貸明細檔資料(有帳戶)，有聯貸母戶的資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	List<Map<String, Object>> findByCustIdDupOnlyHouse2(String custId,
			String dupNo, String brNo);

	/**
	 * * 額度明細表 引進帳務資料的餘額
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param brNo
	 *            分行代號
	 * @param cntrNo
	 *            額度序號
	 * @return <pre>
	 * NTDLBAL  放款等值台幣
	 * LOANBAL 放款餘額
	 * CURR 放款幣別
	 * </pre>
	 */
	List<Map<String, Object>> findByContractAndCustId(String custId,
			String dupNo, String cntrNo);

	/**
	 * 查詢 申貸人、連保人、配偶放款檔
	 * 
	 * @param custId
	 * @return
	 */
	List<Map<String, Object>> gfnDB2GetELLNF(String custId);
	
	/**
	 * 查詢一般放款餘額（千元）(含進出口)  扣掉應收帳款餘額
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> selFactAmt3(String brno, String custId, String dupNo);
	
	/**
	 * 查詢該戶在聯行之餘額（千元）應收帳款餘額-有追索
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> selFactAmt4(String brno, String custId, String dupNo);
	
	/**
	 * 查詢該戶在聯行之餘額（千元）應收帳款餘額-無追索
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> selFactAmt5(String brno, String custId, String dupNo);
	
	/**
	 * 查詢該戶在聯行之餘額（千元），一般放款(含進出口)  扣掉應收帳款餘額
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> selFactAmt6(String brno, String custId, String dupNo);
	
	/**
	 * 查詢該戶在聯行之餘額（千元），應收帳款餘額-有追索(part2)
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> selFactAmt7(String brno, String custId, String dupNo);
	
	/**
	 * 查詢該戶在聯行之餘額（千元），應收帳款餘額-無追索(part2)
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> selFactAmt8(String brno, String custId, String dupNo);
}
