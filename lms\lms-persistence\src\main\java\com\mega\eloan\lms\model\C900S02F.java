package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** <pre>同一通訊指標相同借戶明細檔 
 * </pre>
 * 
 * @since 2019/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/02, J-107-0129 , 在 SLMS-00074 先把 DW、MIS的資料抄寫到ELOANDB後，結合 C900S03C、C900S03D、C900S03E 這三個Table，所產出的控制檔 <br/>
 *          	因為不能跨DB去join資料 , 所以把資料先下到 ELOANDB。在ELOAN 去 join 之後，把結果（亦即，需註記的資料）留存在 ELOAN
 *          </li>
 *          <li>出表的條件，是N個人的某一項「通訊指標」是相同的。 <br/>
 *          	客戶甲、客戶乙的電話相同，已經過查證。後來 客戶丙 撥款，但若 客戶丙 的電話 與［客戶甲、 客戶乙］相同，會導致 3 個人都被出表 <br/>
 *          	藉由欄位 rel_flag 去標註，是因為「哪一個ID」被出表 （透過 LMS.C900S03E 去抓新撥款的案件） 
 *          </li>
 *          </ul>
 */
/*
 delete from lms.c900s02f where mainid not in (select mainid from lms.c900s02e)
*/
@Entity
@Table(name="C900S02F", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900S02F extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;
	
	/** 文件編號 */
	@Column(length = 32, columnDefinition = "CHAR(32)")
	private String mainId;
	
	@Column(name="REL_CUSTID", length=10, columnDefinition="CHAR(10)")
	private String rel_custId;
	
	@Column(name="REL_DUPNO", length=1, columnDefinition="CHAR(1)")
	private String rel_dupNo;
	
	@Column(name="REL_CNAME", length=171, columnDefinition="CHAR(171)")
	private String rel_cname;
	
	@Column(name="REL_FLAG", length=1, columnDefinition="CHAR(1)")
	private String rel_flag;
	
	public String getOid() {
		return this.oid;
	}
	
	public void setOid(String value) {
		this.oid = value;
	}
	
	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getRel_custId() {
		return rel_custId;
	}

	public void setRel_custId(String s) {
		this.rel_custId = s;
	}
	

	public String getRel_dupNo() {
		return rel_dupNo;
	}

	public void setRel_dupNo(String rel_dupNo) {
		this.rel_dupNo = rel_dupNo;
	}

	public String getRel_cname() {
		return rel_cname;
	}

	public void setRel_cname(String rel_cname) {
		this.rel_cname = rel_cname;
	}

	public String getRel_flag() {
		return rel_flag;
	}

	public void setRel_flag(String rel_flag) {
		this.rel_flag = rel_flag;
	}
}