<html xmlns="http://www.w3.org/1999/xhtml"  xmlns:th="http://www.thymeleaf.org">
<body>
<th:block th:fragment="lms1405s07_panel">
	<script type="text/javascript">
		loadScript('pagejs/lms/LMS1405S07Panel');
	</script>
	<!-- pop up screen -->
	<div id="borrower-data921" style="display: none;">
		<div>
			<form id="tLMS1401S07Form01">
				
				<input type="hidden" id="oldOid" name="oldOid" />
				<!--
				<input type="text" id="property" name="property" style="display:none;"/>
				<input type="text" id="currentApplyCurr" name="currentApplyCurr" style="display:none;"/>
				<input type="text" id="currentApplyAmt" name="currentApplyAmt" style="display:none;"/>
				<input type="text" class="numeric" positiveonly="false" integer="3" fraction="2" id="gutPercent" name="gutPercent" style="display:none;"/>
				<input type="text" id="guarantorMemo" name="guarantorMemo" style="display:none;"/>
				<input type="text" id="property2" name="property2" style="display:none;"/>
				<input type="text" id="currentApplyCurr2" name="currentApplyCurr2" style="display:none;"/>
				<input type="text" id="currentApplyAmt2" name="currentApplyAmt2" style="display:none;"/>
				<input type="text" class="numeric" positiveonly="false" integer="3" fraction="2" id="gutPercent2" name="gutPercent2" style="display:none;"/>
				<input type="text" id="guarantorMemo2" name="guarantorMemo2" style="display:none;"/>
                -->
				<table class="tb2" width="600" border="0" cellspacing="0"
					   cellpadding="0">
					<tbody>
					<th:block th:text="#{'L140S16A.other2'}">單位：仟元</th:block>
					<tr>
						<td class="hd2"></td>
						<td><button type="button" onclick="getLocalData()" class="noHideBt">
							<span class="text-only"><th:block th:text="#{'L140S16A.btn14'}">重新引進</th:block></span>
						</button>
						</td>

					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'L140S16A.index25'}">戶名</th:block></td>
						<td >(<span class="text-red" id="typCd_s16a" name="typCd_s16a" ></span>)
							<span class="text-red field required max" id="custId_s16a" name="custId_s16a" maxlength="10"></span>
							<th:block th:text="#{'L120S04A.dupNo'}">重覆序號</th:block>：<span class="text-red field required max" id="dupNo_s16a" name="dupNo_s16a" maxlength="1"></span>
							<br /><span id="custName_s16a" name="custName_s16a" class="max" maxlength="150"></span></td>

					</tr>
					<tr>
						<td class="hd2" ><th:block th:text="#{'L120S03A.cntrNo'}">額度序號</th:block></td>
						<td><span class="field required max" id="cntrNo_s16a" name="cntrNo_s16a" maxlength="12"></span></td>
					</tr>
					<!--J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表-->
					<tr>
						<td class="hd2" ><th:block th:text="#{'L120S16A.proPerty'}">性質</th:block></td>
						<td> 
							<span id="propertyShow_s16a" name="propertyShow_s16a"></span>
						    <span class="field" style="display: none;" id="property_s16a" name="property_s16a"></span>
					    </td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'L120S16A.lnSubject'}">授信科目</th:block></td>
						<td>
							<textarea id="lnSubject_s16a" name="lnSubject_s16a" class="max" rows="5" cols="40" maxlengthC="512"></textarea>
						</td>

					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'L120S16B.CurrentApplyCurrMsg'}">現請額度</th:block></td>
						<td><textarea id="CurrentApplyCurrMsg_s16a" name="CurrentApplyCurrMsg_s16a" class="max" rows="5" cols="40" maxlengthC="200"></textarea></td>

					</tr>
					<tr>
						<td class="hd2"  ><th:block th:text="#{'L140S16A.index29'}">授信期間</th:block></td>
						<td>
							<textarea class="max" id="payDeadline_s16a" name="payDeadline_s16a" rows="5" cols="40" maxlengthC="200"></textarea>

                        </td>

                    </tr>

					<tr>
						<td class="hd2"  ><th:block th:text="#{'L140S16A.index32'}">其他敘作條件</th:block></td>
						<td>
							<span class="text-red"><th:block th:text="#{'L140S16A.other12'}">其他敘作條件僅為參考，列印時不顯示。</th:block></span>
							<div class="showItem" width="40%">
								<textarea class="ickeditor" id="itemDscr4_s16a" name="itemDscr4_s16a" rows="5" cols="40"></textarea>
							</div>
						</td>

                    </tr>

					<tr>
						<td class="hd2"><th:block th:text="#{'L140S16A.index28'}">利(費)率</th:block></td>
						<td><textarea id="itemDscr2_s16a" name="itemDscr2_s16a" rows="10" cols="40"></textarea>
						</td>

                    </tr>
                    <tr>
                        <td class="hd2"><th:block th:text="#{'L140S16A.index27'}">擔保品</th:block></td>

						<td>
							<textarea id="collateral_s16a" name="collateral_s16a" class="max" rows="5" cols="40" maxlengthC="512"></textarea>
							<br>
							<span class="text-red"><th:block th:text="#{'L140S16A.other15'}">以下擔保品資訊僅為引進額度明細表資料供參考，列印時不顯示。</th:block></span>
							<br>
							<textarea class="ickeditor" id="itemDscr3_s16a" name="itemDscr3_s16a" rows="10" cols="40"></textarea>
						</td>
					</tr>

					<tr>
						<td class="hd2"><th:block th:text="#{'L120S16A.guarantor'}">保證人</th:block></td>
						<td>
							<textarea id="guarantor_s16a" name="guarantor_s16a" class="max" rows="5" cols="40" maxlengthC="600"></textarea>
						</td>

					</tr>
                    <!--J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表-->
                    <tr>
						<td class="hd2"><th:block th:text="#{'L120S16A.description'}">說明</th:block></td>
						<td>
							<textarea id="itemDscrC_s16a" name="itemDscrC_s16a" rows="10" cols="40"></textarea>
						    <br>
						    <span class="text-red">1.<th:block th:text="#{'L140S16A.other16'}">本欄位係呈現於額度檢視表。</th:block></span>
							<br>
							<!--<span class="text-red">2.<th:block th:text="#{'L140S16A.other17'}">針對性質為「不變」之額度序號，僅維護說明欄位即可。</th:block></span>-->
							
						</td>
					</tr>

                    </tbody>
                </table>
            </form>
        </div>
    </div>
    <form id="LMS1401S07Form01">
        <div class="content">
            <fieldset>
                <legend>
                    <b>(一)、<th:block th:text="#{'L140S16A.subindex6'}">主要敘作條件</th:block></b>
                </legend>
                <div class="funcContainer">

                    <button type="button" onClick="openbox222();" class="noHideBt">
                        <span class="text-only"><th:block th:text="#{'L140S16A.btn17'}">產生敘作內容</th:block></span>
                    </button>
                    <button type="button" onClick="deleteL120s16a();" class="noHideBt">
                        <span class="text-only"><th:block th:text="#{'L140S16A.btn18'}">刪除敘作內容</th:block></span>
                    </button>
                    <button type="button" onClick="resetL120s16aPrintSeq();" class="noHideBt">
                        <span class="text-only"><th:block th:text="#{'L140S16A.btn21'}">同步額度明細表列印順序</th:block></span>
                    </button>
                    <!-- <button type="button" onClick="void(0);"><span class="text-only">回復全部對照表為可編輯狀態</span></button>-->
                </div>
                <div id="gridviewShow"
                     style="margin-left: 10px; margin-right: 10px"></div>
            </fieldset>
            <div id="openbox222" style="display: none;">
                <span class="text-red"><th:block th:text="#{'L140S16A.other10'}">請選擇本次欲產生主要申請敘作內容之額度明細表</th:block></span>
                <div id="gridviewCC" ></div>
            </div>
        </div>
    </form>

    <form id="LMS1401S07Form02">
        <div class="content">
            <fieldset>
                <legend>
                    <b>(二)、<th:block th:text="#{'L140S16A.subindex7'}">授信額度異動情形</th:block></b>
                </legend>
                <div class="funcContainer">

                    <button type="button" onClick="openbox333();" class="noHideBt">
                        <span class="text-only"><th:block th:text="#{'L140S16A.btn19'}">產生異動情形</th:block></span>
                    </button>
                    <button type="button" onClick="deleteL120s16c();" class="noHideBt">
                        <span class="text-only"><th:block th:text="#{'L140S16A.btn20'}">刪除異動情形</th:block></span>
                    </button>
                </div>
                <span class="text-red"><th:block th:text="#{'L140S16A.other14'}">請於額度明細表完成計算授信額度合計後，再執行本功能</th:block></span>
                <br>
                <th:block th:text="#{'L140S16A.other2'}">單位：仟元</th:block>
                <br>
                <div id="gridviewShow2"
                     style="margin-left: 10px; margin-right: 10px"></div>
            </fieldset>
            <div id="openbox333" style="display: none;">
                <span><th:block th:text="#{'L140S16A.other13'}">請選擇本次欲產生授信額度異動情形之借款人</th:block></span>
                <div id="gridviewDD" ></div>
            </div>
        </div>
    </form>


    <div id="borrower-data888" style="display: none;">
        <div>
            <form id="tLMS1401S07Form02">
                <input type="hidden" id="oldOid" name="oldOid" />
                <table class="tb2" width="750" border="0" cellspacing="0"
                       cellpadding="0">
                    <tbody>
                    <th:block th:text="#{'L140S16A.other2'}">單位：仟元</th:block>
                    <tr>
                        <td class="hd2"></td>
                        <td colspan = 3><button type="button" onclick="getLocalData2()" class="noHideBt">
                            <span class="text-only"><th:block th:text="#{'L140S16A.btn14'}">重新引進</th:block></span>
                        </button>
                        </td>

                    </tr>
                    <tr>

						<td class="hd2"><th:block th:text="#{'L140S16A.index25'}">戶名</th:block></td>
						<td colspan= 3>(<span class="text-red" id="typCd_s16c" name="typCd_s16c" ></span>)
							<span class="text-red field required max" id="custId_s16c" name="custId_s16c" maxlength="10"></span>
							<th:block th:text="#{'L120S04A.dupNo'}">重覆序號</th:block>：<span class="text-red field required max" id="dupNo_s16c" name="dupNo_s16c" maxlength="1"></span>
							<br /><span id="custName_s16c" name="custName_s16c" class="max" maxlength="150"></span></td>

                    </tr>

					<tr>
						<td class="hd2"><th:block th:text="#{'L120S16C.lvTotAmt'}">前准額度</th:block></td>
						<td>
							<textarea id="lvTotAmt_s16c" name="lvTotAmt_s16c" class="max" rows="3" cols="30" maxlengthC="600"></textarea>
						</td>
						<td class="hd2"><th:block th:text="#{'L120S16C.blTotAmt'}">餘額</th:block></td>
						<td>
							<textarea id="blTotAmt_s16c" name="blTotAmt_s16c" class="max" rows="3" cols="30" maxlengthC="600"></textarea>
						</td>
					</tr>




					<tr>
						<td class="hd2"><th:block th:text="#{'L120S16C.incApplyTotAmt'}">增加/減少</th:block></td>
						<td>
							<th:block th:text="#{'L120S16C.other'}"></th:block><br/>
							<textarea id="incApplyTotAmt_s16c" name="incApplyTotAmt_s16c" class="max" rows="3" cols="30" maxlengthC="600"></textarea>
						</td>
						<td class="hd2"><th:block th:text="#{'L120S16C.incApplyMemo'}">額度增減說明</th:block></td>
						<td>
							<textarea id="incApplyMemo_s16c" name="incApplyMemo_s16c" class="max" rows="3" cols="30" maxlengthC="600"></textarea>
						</td>
					</tr>



					<tr>
						<td class="hd2"><th:block th:text="#{'L120S16C.loanTotAmt'}">授信總額度</th:block></td>
						<td>
							<textarea id="loanTotAmt_s16c" name="loanTotAmt_s16c" class="max" rows="3" cols="30" maxlengthC="600"></textarea>
						</td>
						<td class="hd2"><th:block th:text="#{'L120S16C.assureTotAmt'}">其中擔保</th:block></td>
						<td>
							<textarea id="assureTotAmt_s16c" name="assureTotAmt_s16c" class="max" rows="3" cols="30" maxlengthC="600"></textarea>
						</td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'L120S16C.expMemo'}"></th:block></td>
						<td colspan= 3>
							<span class="ps1"><th:block th:text="#{'L120S16C.other1'}"></th:block></span><br/>
							<span class="ps1"><th:block th:text="#{'L120S16C.other2'}"></th:block></span><br/>
							<span class="ps1"><th:block th:text="#{'L120S16C.other3'}"></th:block></span><br/>
							<span><th:block th:text="#{'L120S16C.other4'}"></th:block></span><br/>
							<span><th:block th:text="#{'L120S16C.other5'}"></th:block></span><br/>
							<span><th:block th:text="#{'L120S16C.other6'}"></th:block></span><br/>
							<span><th:block th:text="#{'L120S16C.other7'}"></th:block></span><br/>
							<span><th:block th:text="#{'L120S16C.other8'}"></th:block></span><br/>
							<span><th:block th:text="#{'L120S16C.other9'}"></th:block></span><br/>
							<span><th:block th:text="#{'L120S16C.other10'}"></th:block></span><br/>
							<textarea rows="4" cols="80" class="max" maxlength="600" maxlengthC="200" id="expMemo_s16c" name="expMemo_s16c"></textarea>
						</td>
					</tr>




                    </tbody>
                </table>
            </form>
        </div>
    </div>


    <div id="openbox" style="display: none;">
        <form id="formSearch">
            <table class="tb2" width="100%">
                <!--<tr>
                        <td class="hd1"><th:block th:text="#{'L140S16A.index16'}">客戶型態</th:block>&nbsp;&nbsp;</td>
                        <td><input type="radio" name="rr"><th:block th:text="#{'L140S16A.radio1'}">DBU客戶</th:block><input
                            type="radio" name="rr"><th:block th:text="#{'L140S16A.radio2'}">OBU客戶</th:block><input type="radio" name="rr">
                            <th:block th:text="#{'L140S16A.radio3'}">海外同業</th:block><input
                            type="radio" name="rr"><th:block th:text="#{'L140S16A.radio4'}">海外客戶</th:block></td>
                    </tr>-->
                <tr>
                    <td class="hd1" width="30%"><th:block th:text="#{'L140S16A.index15'}">統一編號<br />(不含重覆序號)</th:block>&nbsp;&nbsp;</td>
                    <td width="70%"><input type="text" class="max required upText" id="searchId04" name="searchId04" maxlength="10" />
                        <button type="button" id="buttonSearch04" class="noHideBt" >
                            <span class="text-only"><th:block th:text="#{'L140S16A.btn13'}">查詢</th:block></span>
                        </button>
                    </td>
                </tr>
                <tr id="showSel04" class="hide">
                    <td class="hd1" width="30%"><th:block th:text="#{'L140S16A.thickbox6'}">借款人選擇</th:block>&nbsp;&nbsp;</td>
                    <td>
                        <select id="selCus04" name="selCus04"></select>
                    </td>
                </tr>
                <tr id="showBrid" class="hide">
                    <td class="hd1" width="30%"><th:block th:text="#{'L140S16A.thickbox18'}">查詢分行</th:block>&nbsp;&nbsp;</td>
                    <td>
                        <th:block th:text="#{'L140S16A.thickbox19'}">分行代碼</th:block>：<input type="text" class="max obuText upText required" id="textBrid" name="textBrid" size="5" maxlength="3" />
                        <button type="button" id="buttonSearch05" class="noHideBt">
                            <span class="text-only"><th:block th:text="#{'L140S16A.btn13'}">查詢</th:block></span>
                        </button>
                    </td>
                </tr>
                <tr id="showGrid" class="hide">
                    <td colspan="2"><div id="gridviewAA">
							<span id="bb" style="display: none" class="text-red">
								<th:block th:text="#{'L140S16A.other11'}">請選擇一份額度明細表</th:block></span>
                    </div>
                    </td>
                </tr>
                <tr></tr>
            </table>
        </form>
    </div>
</th:block>
</body>
</html>