package tw.com.jcs.flow.core;

/**
 * <h1>流程引擎元件</h1> 定義為流程引擎的元件，可取得流程引擎的實體參考
 * 
 * <AUTHOR> Software Inc.
 */
public abstract class FlowEngineUnit {

    /**
     * 綁定的流程引擎
     * 
     */
    protected FlowEngineImpl engine;

    /**
     * 取得綁定的流程引擎
     * 
     * @return
     */
    public FlowEngineImpl getEngine() {
        return engine;
    }

    /**
     * constructor
     * 
     * @param engine
     */
    public FlowEngineUnit(FlowEngineImpl engine) {
        this.engine = engine;
    }

}
