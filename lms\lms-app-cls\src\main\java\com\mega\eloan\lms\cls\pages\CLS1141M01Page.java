/* 
 *CLS1141M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import java.util.Arrays;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.LMS7301M01Panel;
import com.mega.eloan.lms.base.panels.LMSM02BPanel;
import com.mega.eloan.lms.base.panels.LMSM02Panel;
import com.mega.eloan.lms.base.panels.LMSM03Panel;
import com.mega.eloan.lms.base.panels.LMSM04Panel;
import com.mega.eloan.lms.base.panels.LMSS08COMPanel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.panels.CLS1141S01Panel;
import com.mega.eloan.lms.cls.panels.CLS1141S03Panel;
import com.mega.eloan.lms.cls.panels.CLS1141S04Panel;
import com.mega.eloan.lms.cls.panels.CLS1141S05Panel;
import com.mega.eloan.lms.cls.panels.CLS1141S11Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S12Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S13Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S14Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S15Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S17Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S18Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S20Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S21Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S22Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S23Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S24BPanel;
import com.mega.eloan.lms.cls.panels.CLS1201S24Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S25BPanel;
import com.mega.eloan.lms.cls.panels.CLS1201S25Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S26Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S27Panel;
import com.mega.eloan.lms.cls.panels.CLS1301S05Panel;
import com.mega.eloan.lms.cls.panels.CLSM01Panel;
import com.mega.eloan.lms.cls.panels.CLSS02CPanel;
import com.mega.eloan.lms.cls.panels.CLSS07APanel;
import com.mega.eloan.lms.cls.panels.CLSS08APanel;
import com.mega.eloan.lms.cls.panels.CLSS09APanel;
import com.mega.eloan.lms.cls.panels.CLSS10APanel;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金 - 案件簽報書
 * </pre>
 * 
 * @since 2012/10/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/2,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1141m01/{page}")
public class CLS1141M01Page extends AbstractEloanForm {

	@Autowired
	DocCheckService docCheckService;

	@Autowired
	CLS1141Service service1141;

	@Autowired
	CLSService clsService;
	
	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		// 案件考核表分頁
		new LMS7301M01Panel("lmss7305_panel").processPanelData(model, params);
		// 常用主管名單共用元件
		new LMSM03Panel("lmsm03_panel").processPanelData(model, params);
		new LMSM02BPanel("lmsm02b_panel").processPanelData(model, params);
		if(true){
			renderJsI18N(LMSM02BPanel.class);	
		}		
		new LMSM04Panel("lmsm04_panel").processPanelData(model, params);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 如果存在海外聯貸案文件狀態則新增此狀態的按鈕
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		Boolean isC122M01A = params.getBoolean("isC122M01A");
		L120M01A l120m01a = service1141.findModelByOid(L120M01A.class, mainOid);
		boolean isShow = false;
		boolean show_btn4AreaAuthLvl3Hide = false;

        boolean active_brmp = false;
        String ssoUnitNo = user.getSsoUnitNo();
        String unitNo = user.getUnitNo();
        if(Util.equals("900", ssoUnitNo) || Util.equals("943", ssoUnitNo) || isC122M01A || clsService.is_function_on_codetype("J-111-0160_sso_authChk")){
            active_brmp = true;
        }
		Map<String, String> map= clsService.get_codeType_codeDesc2WithOrder("LMS_FUNC_ON_FLAG","zh_TW");
		if(clsService.is_function_on_codetype("cls_brmp_pilot")){
			if (map.containsKey("cls_brmp_pilot")) {
				String val = Util.trim(map.get("cls_brmp_pilot"));
				if(val.contains(ssoUnitNo)){
					active_brmp = true;
				}
			}
		}

		// 2012/11/26, Rex add
		String docStatus = l120m01a.getDocStatus();
		UnitTypeEnum unitType = UnitTypeEnum.convertToUnitType(user
				.getUnitType());
		if (UnitTypeEnum.營運中心.equals(unitType)) {
			if (CreditDocStatusEnum.營運中心_審查中.getCode().equals(docStatus)
					&& UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(l120m01a
							.getAuthLvl())) {
				if (UtilConstants.Casedoc.DocCode.陳復陳述案.equals(l120m01a
						.getDocCode())) {
					isShow = true;
				} else {
					show_btn4AreaAuthLvl3Hide = true;
				}
			} else if (CreditDocStatusEnum.營運中心_審查中.getCode().equals(docStatus)
					&& LMSUtil.isSpecialBranch(l120m01a.getCaseBrId())
					&& (UtilConstants.Casedoc.DocCode.其他.equals(l120m01a
							.getDocCode())
							|| UtilConstants.Casedoc.DocCode.陳復陳述案.equals(l120m01a
									.getDocCode()) || UtilConstants.Casedoc.DocCode.異常通報
							.equals(l120m01a.getDocCode()))) {
				show_btn4AreaAuthLvl3Hide = true;
			} else if (CreditDocStatusEnum.營運中心_審查中.getCode().equals(docStatus)
					&& (UtilConstants.Casedoc.DocKind.授權外.equals(l120m01a
							.getDocKind()))) {
				show_btn4AreaAuthLvl3Hide = true;
			}
		}
		if (UnitTypeEnum.授管處.equals(unitType)) {
			// 2013/03/19 Miller add
			if (CreditDocStatusEnum.授管處_審查中.getCode().equals(docStatus)
					&& UtilConstants.Casedoc.DocCode.異常通報.equals(l120m01a
							.getDocCode())) {
				isShow = true;
			}else if (CreditDocStatusEnum.授管處_審查中.getCode().equals(docStatus)
					&& UtilConstants.Casedoc.DocCode.陳復陳述案.equals(l120m01a
							.getDocCode())) {
				isShow = true;
			}
		}
		// 免批覆按鈕
		addAclLabel(model, new AclLabel("_btn4AreaAuthLvl3", AuthType.Modify, params, isShow));

		// 呈主管覆核
		addAclLabel(model, new AclLabel("_btn4AreaAuthLvl3Hide", AuthType.Modify, params, show_btn4AreaAuthLvl3Hide));
		if (LMSUtil.isSpecialBranch(Util.trim(l120m01a.getCaseBrId()))) {
			// 控制呈授管處/營運中心時隱藏預設按鈕
			if (docStatus.indexOf("C") != -1 || docStatus.indexOf("H") != -1) {
				if (!user.getUnitNo().equals(UtilConstants.BankNo.授管處)
						&& !user.getUnitNo()
								.equals(UtilConstants.BankNo.中區營運中心)
						&& !user.getUnitNo().equals(
								UtilConstants.BankNo.中部區域授信中心)
						&& !user.getUnitNo().equals(
								UtilConstants.BankNo.北一區營運中心)
						&& !user.getUnitNo().equals(
								UtilConstants.BankNo.北二區營運中心)
						&& !user.getUnitNo()
								.equals(UtilConstants.BankNo.南區營運中心)
						&& !user.getUnitNo().equals(
								UtilConstants.BankNo.南部區域授信中心)
						&& !user.getUnitNo().equals(
								UtilConstants.BankNo.桃竹苗區營運中心)) {
					hdefaultBtn(params, model);
				} else {
					defaultBtn(params, l120m01a, model);
				}
			} else {
				defaultBtn(params, l120m01a, model);
			}
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING2", AuthType.Accept, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING3", AuthType.Query, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO2", AuthType.Accept, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO3", AuthType.Query, params, false));
		} else if (CreditDocStatusEnum.營運中心_海外聯貸案_會簽中.getCode().equals(
				l120m01a.getAreaDocstatus())
				&& user.getUnitNo().equals(l120m01a.getAreaBrId())
				&& !LMSUtil.isSpecialBranch(user.getUnitNo())) {
			hdefaultBtn(params, model);
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING", AuthType.Modify, params, true));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING3", AuthType.Query, params, true));
			
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO2", AuthType.Accept, params, false));
		} else if (CreditDocStatusEnum.營運中心_海外聯貸案_待放行.getCode().equals(
				l120m01a.getAreaDocstatus())
				&& user.getUnitNo().equals(l120m01a.getAreaBrId())) {
			hdefaultBtn(params, model);

			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING3", AuthType.Query, params, false));

			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO2", AuthType.Accept, params, true));
		} else if (CreditDocStatusEnum.營運中心_海外聯貸案_已會簽.getCode().equals(
				l120m01a.getAreaDocstatus())
				&& user.getUnitNo().equals(l120m01a.getAreaBrId())) {
			hdefaultBtn(params, model);
			if ("S".equals(user.getUnitType())) {
				addAclLabel(model, new AclLabel("_btnDOC_HEDITING0", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.授管處_審查中));
			}

			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING3", AuthType.Query, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO2", AuthType.Accept, params, false));
		} else {
			if (user.getUnitNo().equals(Util.trim(l120m01a.getOwnBrId()))) {
				defaultBtn(params, l120m01a, model);
			} else {
				if (CreditDocStatusEnum.海外_已核准.getCode().equals(docStatus)
						|| CreditDocStatusEnum.海外_婉卻.getCode()
								.equals(docStatus)
						||
						// CreditDocStatusEnum.海外_免批覆.getCode().equals(docStatus)
						// ||
						CreditDocStatusEnum.泰國_提會待登錄.getCode()
								.equals(docStatus)
						|| CreditDocStatusEnum.泰國_提會待覆核.getCode().equals(
								docStatus)
						|| (CreditDocStatusEnum.會簽後修改待覆核.getCode().equals(
								Util.trim(docStatus)) || CreditDocStatusEnum.總處營業單位待覆核
								.getCode().equals(Util.trim(docStatus)))) {
					defaultBtn(params, l120m01a, model);
				} else {
					hdefaultBtn(params, model);
				}
			}
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING", AuthType.Modify, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_SIGNING3", AuthType.Query, params, false));
			addAclLabel(model, new AclLabel("_btnDOC_SEA_CWAITGO2", AuthType.Accept, params, false));
		}
		
		if(true){
			boolean _btnSAVE = true;
			if(Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), docStatus)
				|| Util.equals(CreditDocStatusEnum.營運中心_待放行.getCode(), docStatus)
				|| Util.equals(CreditDocStatusEnum.營運中心_待核定.getCode(), docStatus)
				|| Util.equals(CreditDocStatusEnum.海外_已核准.getCode(), docStatus)){
				_btnSAVE = false;
			}
			model.addAttribute("_btnSAVE", _btnSAVE);
		}		

		renderJsI18N(CLS1141M01Page.class);
		renderJsI18N(CLSM01Panel.class);
		renderJsI18N(LMSCommomPage.class);
		renderRespMsgJsI18N(UtilConstants.AJAX_RSP_MSG.ERR_MSG); // 多render一個msgi18n

		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(l120m01a, page);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
		
		model.addAttribute("tab_l120m01a_simplifyFlagB",
				(l120m01a != null
						&& Util.equals(UtilConstants.Casedoc.SimplifyFlag.卡友信貸,
								l120m01a.getSimplifyFlag())));
		model.addAttribute("tab_l120m01a_simplifyFlagA",
				(l120m01a != null
						&& Util.equals(UtilConstants.Casedoc.SimplifyFlag.一般消金,
								l120m01a.getSimplifyFlag())));
		model.addAttribute("tab_l120m01a_simplifyFlagC",
				(l120m01a != null
						&& Util.equals(UtilConstants.Casedoc.SimplifyFlag.勞工紓困,
								l120m01a.getSimplifyFlag())));
		model.addAttribute("tab_l120m01a_simplifyFlagD",
				(l120m01a != null
						&& Util.equals(UtilConstants.Casedoc.SimplifyFlag.歡喜信貸,
								l120m01a.getSimplifyFlag())));
		model.addAttribute("tab_l120m01a_paperlessFlagE",
				(l120m01a != null && Util.equals(
						UtilConstants.Casedoc.SimplifyFlag.快速審核信貸,
						l120m01a.getSimplifyFlag())));
		
		model.addAttribute("isShowPrintButton_paperlessFlagE",
				!(l120m01a != null && Util.equals(
						UtilConstants.Casedoc.SimplifyFlag.快速審核信貸,
						l120m01a.getSimplifyFlag())));
		model.addAttribute("isShowReSendBRMPButton_paperlessFlagE",
				(l120m01a != null && Util.equals(
						UtilConstants.Casedoc.SimplifyFlag.快速審核信貸,
						l120m01a.getSimplifyFlag()) && active_brmp));
		
		//J-109-0178_10702_B1001 Web e-Loan 消金簽報書新增申貸資料核對表頁籤及列印功能
		model.addAttribute("show_checkList_panel", Util
				.equals(l120m01a.getCheckListFlag(), UtilConstants.DEFAULT.是));

		boolean showBrmpPanel = clsService.is_L120M01A_contains_brmpJsonData(l120m01a);
		model.addAttribute("tab_brmpPanel", showBrmpPanel);

		//J-111-0227
		if( clsService.is_function_on_codetype("CtrTypeA_V202209")){
			model.addAttribute("trPurpose202209",
					clsService.is_function_on_codetype("CtrTypeA_V202209"));
			model.addAttribute("trPurpose", false);
		} else if (clsService.is_function_on_codetype("CtrTypeA_V202206")) {
			model.addAttribute("trPurpose202209", false);
			model.addAttribute("trPurpose",
					clsService.is_function_on_codetype("CtrTypeA_V202206"));
		}
	}// ;

	private void defaultBtn(PageParameters params, L120M01A l120m01a,
			ModelMap model) {
		// 海外
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params,
				getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));

		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE2", params,
				getDomainClass(), AuthType.Accept, CreditDocStatusEnum.海外_待覆核,
				CreditDocStatusEnum.國內簡易行待覆核, CreditDocStatusEnum.會簽後修改待覆核,
				CreditDocStatusEnum.總處營業單位待覆核));

		addAclLabel(model, new AclLabel("_btnSEND_WAITLOGIN", params,
				getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_提會待登錄,
				CreditDocStatusEnum.海外_總行提會待登錄, CreditDocStatusEnum.泰國_提會待登錄));
		addAclLabel(model, new AclLabel("_btnSEND_WAITLOGIN3", params,
				getDomainClass(), AuthType.Query, CreditDocStatusEnum.海外_提會待登錄,
				CreditDocStatusEnum.海外_總行提會待登錄, CreditDocStatusEnum.泰國_提會待登錄));

		addAclLabel(model, new AclLabel("_btnSEND_WAITAPPROVE2", params,
				getDomainClass(), AuthType.Accept, CreditDocStatusEnum.海外_提會待覆核,
				CreditDocStatusEnum.海外_總行提會待覆核, CreditDocStatusEnum.泰國_提會待覆核));
		addAclLabel(model, new AclLabel("_btnSEND_WAITAPPROVE3", params,
				getDomainClass(), AuthType.Query, CreditDocStatusEnum.海外_提會待覆核,
				CreditDocStatusEnum.海外_總行提會待覆核, CreditDocStatusEnum.泰國_提會待覆核));

		addAclLabel(model,
				new AclLabel("_btnWAIT_GETORBACK", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.海外_待補件,
						CreditDocStatusEnum.海外_待撤件));

		boolean _btnALREADY_OK3show = false;
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(
				params.getString(EloanConstants.MAIN_DOC_STATUS))) {
			String authLvl = l120m01a.getAuthLvl();
			if (UtilConstants.Casedoc.AuthLvl.分行授權內.equals(authLvl)
					|| UtilConstants.Casedoc.AuthLvl.總行授權內.equals(authLvl)) {
				_btnALREADY_OK3show = false;
			} else {
				_btnALREADY_OK3show = true;
			}
		}

		addAclLabel(model, new AclLabel("_btnALREADY_OK3", AuthType.Query,
				params, _btnALREADY_OK3show));

		addAclLabel(model, new AclLabel("_btnALREADY_REJECT3", params,
				getDomainClass(), AuthType.Query, CreditDocStatusEnum.海外_婉卻));

		addAclLabel(model,
				new AclLabel("_btnSAY_CASE", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.海外_陳復案_陳述案));

		// 營運中心
		addAclLabel(model,
				new AclLabel("_btnDOC_CEDITING", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.營運中心_審查中));

		addAclLabel(model,
				new AclLabel("_btnDOC_CEDITING3", params, getDomainClass(),
						AuthType.Query, CreditDocStatusEnum.營運中心_審查中));
		addAclLabel(model,
				new AclLabel("_btnDOC_CWAITCHECK2", params, getDomainClass(),
						AuthType.Accept, CreditDocStatusEnum.營運中心_待放行,
						CreditDocStatusEnum.營運中心_待核定));

		addAclLabel(model,
				new AclLabel("_btnDOC_CTOADMIN", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.營運中心_呈總處));
		addAclLabel(model,
				new AclLabel("_btnDOC_CTOADMIN2", params, getDomainClass(),
						AuthType.Accept, CreditDocStatusEnum.營運中心_呈總處));

		addAclLabel(model,
				new AclLabel("_btnDOC_CALREADYOK3", params, getDomainClass(),
						AuthType.Query, CreditDocStatusEnum.營運中心_已核准));

		addAclLabel(model,
				new AclLabel("_btnDOC_CALREADYREJECT", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.營運中心_已婉卻,
						CreditDocStatusEnum.營運中心_待陳復));

		addAclLabel(model,
				new AclLabel("_btnDOC_CALLSEND", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.營運中心_所有提會案件));

		addAclLabel(model,
				new AclLabel("_btnDOC_CSAY_CASE", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.營運中心_陳復案_陳述案));

		// 授管處
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING0", AuthType.Modify,
				params, false));
		addAclLabel(model,
				new AclLabel("_btnDOC_HEDITING", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.授管處_審查中));
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING3", params,
				getDomainClass(), AuthType.Query, CreditDocStatusEnum.授管處_審查中));

		addAclLabel(model,
				new AclLabel("_btnDOC_HALREADY", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.授管處_已會簽));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADY3", params,
				getDomainClass(), AuthType.Query, CreditDocStatusEnum.授管處_已會簽));

		addAclLabel(model,
				new AclLabel("_btnDOC_HSEND1", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.授管處_提授審會));
		addAclLabel(model,
				new AclLabel("_btnDOC_HSEND1b", params, getDomainClass(),
						AuthType.Query, CreditDocStatusEnum.授管處_提授審會));

		addAclLabel(model,
				new AclLabel("_btnDOC_HSEND2", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.授管處_提催收會));
		addAclLabel(model,
				new AclLabel("_btnDOC_HSEND2b", params, getDomainClass(),
						AuthType.Query, CreditDocStatusEnum.授管處_提催收會));

		addAclLabel(model,
				new AclLabel("_btnDOC_HSEND3", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.授管處_提常董會));
		addAclLabel(model,
				new AclLabel("_btnDOC_HSEND3b", params, getDomainClass(),
						AuthType.Query, CreditDocStatusEnum.授管處_提常董會));

		addAclLabel(model,
				new AclLabel("_btnDOC_HWAITCHECK", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.授管處_待放行,
						CreditDocStatusEnum.授管處_待核定));
		addAclLabel(model,
				new AclLabel("_btnDOC_HWAITCHECK2", params, getDomainClass(),
						AuthType.Accept, CreditDocStatusEnum.授管處_待放行,
						CreditDocStatusEnum.授管處_待核定));
		addAclLabel(model,
				new AclLabel("_btnDOC_HWAITCHECK3", params, getDomainClass(),
						AuthType.Query, CreditDocStatusEnum.授管處_待放行,
						CreditDocStatusEnum.授管處_待核定));
		addAclLabel(model,
				new AclLabel("_btnDOC_HWAITCHANGE3", params, getDomainClass(),
						AuthType.Query, CreditDocStatusEnum.授管處_待更正,
						CreditDocStatusEnum.授管處_待分行撤件));

		addAclLabel(model, new AclLabel("_btnDOC_HALREADYOK3", params,
				getDomainClass(), AuthType.Query, CreditDocStatusEnum.授管處_已核准));

		addAclLabel(model,
				new AclLabel("_btnDOC_HALREADYREJECT", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.授管處_已婉卻,
						CreditDocStatusEnum.授管處_待陳復));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYREJECT3", params,
				getDomainClass(), AuthType.Query, CreditDocStatusEnum.授管處_已婉卻,
				CreditDocStatusEnum.授管處_待陳復));
	}

	private void hdefaultBtn(PageParameters params, ModelMap model) {
		// 海外
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", AuthType.Modify,
				params, false));

		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE2", AuthType.Accept,
				params, false));

		addAclLabel(model, new AclLabel("_btnSEND_WAITLOGIN", AuthType.Modify,
				params, false));
		addAclLabel(model, new AclLabel("_btnSEND_WAITLOGIN3", AuthType.Query,
				params, false));

		addAclLabel(model, new AclLabel("_btnSEND_WAITAPPROVE2",
				AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnSEND_WAITAPPROVE3", AuthType.Query,
				params, false));

		addAclLabel(model, new AclLabel("_btnWAIT_GETORBACK", AuthType.Modify,
				params, false));

		addAclLabel(model,
				new AclLabel("_btnALREADY_OK3", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnALREADY_REJECT3", AuthType.Query,
				params, false));

		addAclLabel(model,
				new AclLabel("_btnSAY_CASE", AuthType.Modify, params, false));

		// 授管處
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING0", AuthType.Modify,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING", AuthType.Modify,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING2", AuthType.Accept,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HEDITING3", AuthType.Query,
				params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HALREADY", AuthType.Modify,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADY2", AuthType.Accept,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADY3", AuthType.Query,
				params, false));

		addAclLabel(model,
				new AclLabel("_btnDOC_HSEND1", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND1a", AuthType.Accept,
				params, false));
		addAclLabel(model,
				new AclLabel("_btnDOC_HSEND1b", AuthType.Query, params, false));

		addAclLabel(model,
				new AclLabel("_btnDOC_HSEND2", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND2a", AuthType.Accept,
				params, false));
		addAclLabel(model,
				new AclLabel("_btnDOC_HSEND2b", AuthType.Query, params, false));

		addAclLabel(model,
				new AclLabel("_btnDOC_HSEND3", AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HSEND3a", AuthType.Accept,
				params, false));
		addAclLabel(model,
				new AclLabel("_btnDOC_HSEND3b", AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHECK", AuthType.Modify,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHECK2", AuthType.Accept,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHECK3", AuthType.Query,
				params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHANGE", AuthType.Modify,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHANGE2", AuthType.Accept,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HWAITCHANGE3", AuthType.Query,
				params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HALREADYOK", AuthType.Modify,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYOK2", AuthType.Accept,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYOK3", AuthType.Query,
				params, false));

		addAclLabel(model, new AclLabel("_btnDOC_HALREADYREJECT",
				AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYREJECT2",
				AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_HALREADYREJECT3",
				AuthType.Query, params, false));

		// 營運中心

		addAclLabel(model, new AclLabel("_btnDOC_CEDITING", AuthType.Modify,
				params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CEDITING3", AuthType.Query,
				params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHECK2", AuthType.Accept,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHECK3", AuthType.Query,
				params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CTOADMIN", AuthType.Modify,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CTOADMIN2", AuthType.Accept,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CTOADMIN3", AuthType.Query,
				params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHANGE", AuthType.Modify,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHANGE2", AuthType.Accept,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CWAITCHANGE3", AuthType.Query,
				params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CALREADYOK", AuthType.Modify,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CALREADYOK2", AuthType.Accept,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CALREADYOK3", AuthType.Query,
				params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CALREADYREJECT",
				AuthType.Modify, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CALREADYREJECT2",
				AuthType.Accept, params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CALREADYREJECT3",
				AuthType.Query, params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CALLSEND", AuthType.Modify,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CALLSEND2", AuthType.Accept,
				params, false));
		addAclLabel(model, new AclLabel("_btnDOC_CALLSEND3", AuthType.Query,
				params, false));

		addAclLabel(model, new AclLabel("_btnDOC_CSAY_CASE", AuthType.Modify,
				params, false));

	}

	// 頁籤
	public Panel getPanel(L120M01A l120m01a, int index) {
		String active_SAS_AML = "0";
		if(l120m01a!=null && clsService.active_SAS_AML(l120m01a)){
			active_SAS_AML = clsService.is_aml_lockEdit(l120m01a.getMainId())?"2":"1";
		}
		//=======================
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new CLS1141S01Panel(TAB_CTX, true);
			break;
		case 2:
			renderRespMsgJsI18N("EFD3026"); // 多render一個msgi18n
			panel = new CLSS02CPanel(TAB_CTX, true);
			renderJsI18N(CLSS02CPanel.class);
			break;
		case 3:
			renderRespMsgJsI18N("EFD0002"); // 多render一個msgi18n
			panel = new CLS1141S03Panel(TAB_CTX, true, l120m01a);
			break;
		case 4:
			renderJsI18N(CLS1141S04Panel.class);
			panel = new CLS1141S04Panel(TAB_CTX, true);
			break;
		case 5:
			renderJsI18N(CLS1141S05Panel.class);
			panel = new CLS1141S05Panel(TAB_CTX, true);
			break;
		case 7:
			renderJsI18N(CLSS07APanel.class);
			renderJsI18N(CLS1301S05Panel.class);
			renderJsI18N(CLS1151S01Page.class);
			panel = new CLSS07APanel(TAB_CTX, true);
			break;
		case 8:
			renderJsI18N(CLSS08APanel.class);
			renderJsI18N(LMSS08COMPanel.class);
			renderJsI18N(AbstractEloanPage.class,
					new String[] { "docStatus.230" });
			panel = new CLSS08APanel(TAB_CTX, true, l120m01a);
			break;
		case 9:
			renderJsI18N(CLSS09APanel.class);
			panel = new CLSS09APanel(TAB_CTX, true);
			break;
		case 10:
			renderJsI18N(CLSS10APanel.class);
			panel = new CLSS10APanel(TAB_CTX, true);
			break;
		case 11:
			panel = new CLS1141S11Panel(TAB_CTX, true);
			break;
		case 12:
			renderJsI18N(CLSS10APanel.class);
			panel = new CLS1201S12Panel(TAB_CTX, true);
			break;
		case 13:
			renderJsI18N(CLSS10APanel.class);
			panel = new CLS1201S13Panel(TAB_CTX, true);
			break;
		case 14:
			renderJsI18N(CLSS10APanel.class);
			panel = new CLS1201S14Panel(TAB_CTX, true);
			break;
		case 15:
			renderJsI18N(CLSS10APanel.class);
			panel = new CLS1201S15Panel(TAB_CTX, true);
			break;
		case 17:
			panel = new CLS1201S17Panel(TAB_CTX, true);
			renderJsI18N(CLS1201S17Panel.class);
			break;
		case 18:
			panel = new CLS1201S18Panel(TAB_CTX, true);
			renderJsI18N(CLS1201S18Panel.class);
			break;
		case 19:
			panel = new LMSM02Panel(TAB_CTX, true);
			break;
		case 20:
			panel = new CLS1201S20Panel(TAB_CTX, true, active_SAS_AML);
			renderJsI18N(CLS1201S20Panel.class);
			break;
		case 21:
			panel = new CLS1201S21Panel(TAB_CTX, true);
			renderJsI18N(CLS1201S21Panel.class);
			break;
		case 22:
			panel = new CLS1201S22Panel(TAB_CTX, true);
			renderJsI18N(CLS1201S22Panel.class);
			renderJsI18N(CLSS02CPanel.class);
			renderJsI18N(CLS1141S05Panel.class);
			break;
		case 23:
			panel = new CLS1201S23Panel(TAB_CTX);
			renderJsI18N(CLS1201S23Panel.class);
			break;
		case 24:
			String checkListVersion=clsService.findCheckListVersion(l120m01a);
			if(Util.isEmpty(checkListVersion)){
				panel = new CLS1201S24Panel(TAB_CTX, true);
				renderJsI18N(CLS1201S24Panel.class);
			}
			else{
				panel = new CLS1201S24BPanel(TAB_CTX);
				renderJsI18N(CLS1201S24BPanel.class);
			}
			
			break;
		case 25:
			//根據版本好帶出不同的HTML
			String checkVersion=clsService.findCheckL120S15AVersion(l120m01a);
			
			if(Arrays.asList(ClsUtil.get_newer_version_L120S15A_rptId()).contains(checkVersion) || Util.equals(checkVersion, "")){ //判斷版本
				panel = new CLS1201S25BPanel(TAB_CTX, true);
				renderJsI18N(CLS1201S25BPanel.class);
			}else{
				panel = new CLS1201S25Panel(TAB_CTX, true);
				renderJsI18N(CLS1201S25Panel.class);
			}
			
			break;
		case 26:
			panel = new CLS1201S26Panel(TAB_CTX, true, l120m01a.getMainId());
			break;
		case 27:
			panel = new CLS1201S27Panel(TAB_CTX, true, l120m01a.getMainId());
			renderJsI18N(CLS1201S27Panel.class);
			break;
		default:
			panel = new CLS1141S01Panel(TAB_CTX);
			break;
		}
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L120M01A.class;
	}
}
