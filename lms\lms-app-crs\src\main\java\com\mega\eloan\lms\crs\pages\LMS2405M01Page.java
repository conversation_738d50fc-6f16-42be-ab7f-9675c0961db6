/* 
 * LMS2405M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.pages;


import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;




import tw.com.jcs.auth.AuthType;
import tw.com.jcs.auth.CodeItemService;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.crs.panels.LMS2405S01Panel;
import com.mega.eloan.lms.crs.panels.LMS2405S02Panel;
import com.mega.eloan.lms.crs.panels.LMS2405S03Panel;
import com.mega.eloan.lms.crs.panels.LMS2415S02Panel;
import com.mega.eloan.lms.crs.panels.LMS2415S05Panel;
import com.mega.eloan.lms.model.C240M01A;

/**
 * <pre>
 * 覆審名單
 * </pre>
 * 
 * @since 2011/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/6,irene
 *          </ul>
 */
@Controller
@RequestMapping("/crs/lms2405m01/{page}")

public class LMS2405M01Page extends AbstractEloanForm {
	@Autowired
	CodeItemService cis;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS2405M01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		// 依權限設定button

		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.編製中));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
				AuthType.Accept, RetrialDocStatusEnum.待覆核));
		addAclLabel(model, new AclLabel("_btnApply", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.已核准));

		renderJsI18N(LMS2405M01Page.class);

		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2);
		Panel panel = getPanel(page, params);
		model.addAttribute("tabID", tabID);
		panel.processPanelData(model, params);
		model.addAttribute("panelName", panel.getPanelName());
		model.addAttribute("panelFragmentName", panel.getPanelFragmentName());
	}

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index, PageParameters params) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS2405S01Panel(TAB_CTX,true);
			renderJsI18N(LMS2405S01Panel.class);
			break;
		case 2:
			panel = new LMS2405S02Panel(TAB_CTX,params,true);
			renderJsI18N(LMS2405S02Panel.class);
			renderJsI18N(LMS2415S02Panel.class);
			renderJsI18N(LMS2415S05Panel.class);
			break;
		case 3:
			panel = new LMS2405S03Panel(TAB_CTX,true);
			break;
		default:
			panel = new LMS2405S01Panel(TAB_CTX,true);
			break;
		}
		if (panel == null)
			panel = new Panel(TAB_CTX,true);
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C240M01A.class;
	}

}
