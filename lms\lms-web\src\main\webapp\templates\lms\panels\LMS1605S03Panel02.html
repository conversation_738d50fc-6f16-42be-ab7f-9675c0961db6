<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        	<form>
	            <fieldset>
	                <legend>
	                    <strong><th:block th:text="#{'L160M01A.title11'}"><!-- 主從債務人資料表--></th:block></strong>
	                </legend>
					<button id="includePeople" type="button" >
	                    <span class="text-only"><th:block th:text="#{'L160M01A.bt10'}"><!-- 引進連保人--></th:block></span>
	                </button>
	                <button id="openPeopleBox" type="button" >
	                    <span class="text-only"><th:block th:text="#{'L160M01A.bt08'}"><!-- 新增主從債務人資料表--></th:block></span>
	                </button>
					<!--J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定-->
					<button id="setGuarantorCreditPriority" type="button">
                        <span class="text-only"><th:block th:text="#{'btn.setGuarantorCreditPriority'}">信用品質順序設定</th:block></span>
                    </button>
					<!--J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記-->
					<button id="btnEntireApply" type="button">
                        <span class="text-only"><th:block th:text="#{'btn.btnEntireApply'}">整批作業</th:block></span>
                    </button>
					<button id="deletePeople" type="button" >
	                    <span class="text-only"><th:block th:text="#{'L160M01A.bt06'}"><!--刪除--></th:block></span>
	                </button>
					<button id="printPeople" type="button" class="forview">
	                    <span class="text-only"><th:block th:text="#{'button.print'}"><!--列印--></th:block></span>
	                </button>
					<!--G-113-0036_11850_B1001 主從債務人新增得用EXCEL Import方式整批匯入-->
					<br>
					<button id="ExcelDownload" type="button">
						<span class="text-only"><th:block th:text="#{'L162S02A.btn01'}"><!-- Excel範本下載--></th:block></span>
					</button>
					<button id="ExcelImport" type="button">
						<span class="text-only"><th:block th:text="#{'L162S02A.btn02'}"><!-- Excel匯入主從債務人--></th:block></span>
					</button>
	                <div id="gridviewPeople" ></div>
	            </fieldset>
			</form>
            <!-- thickbox -->
            <div id="peopleBox" style="display:none">
            	
                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-top:10px;">
                    <tr>
                        <td>
                            <span class="text-red">※1：
							<th:block th:text="#{'L160M01A.message18'}"><!-- 只有主債務人統編與從債務人統編相同者，其關係可為空白，其餘欄位皆不可空白--></th:block>
							</span>
                        </td>
                    </tr>
                </table>
				<form id="L162M01AForm">
                <table class="tb2" width="100%" border="1" cellspacing="0" cellpadding="0" style="margin-top:5px;">
                    <tr>
                        <td class="hd1" width="20%">
                        	<span class="text-red">＊</span>
                            <th:block th:text="#{'L162M01A.custId'}"><!-- 主債務人統編--></th:block>&nbsp;&nbsp;
                        </td>
                        <td width="30%">
                        	 <select id="custIdSelect" name="custIdSelect"  class="required"></select>&nbsp;
                        	 <input type="hidden" id="custId" name="custId" size="10" maxlength="10" class="required"/>&nbsp;
							 <input type="hidden" id="dupNo"  name="dupNo"  size="1" maxlength="1"  class="required" />
                        </td>
                        <td class="hd1" width="20%">
                          <th:block th:text="#{'L160M01A.cntrNo'}"><!-- 額度序號--></th:block>&nbsp;&nbsp;
                        </td>
                        <td width="30%">
                            <select type="text" id="cntrNo" name="cntrNo"  class="required"></select>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                        	<span class="text-red">＊</span>
                            <th:block th:text="#{'L162M01A.rId'}"><!-- 從債務人統編--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                            <input type="text" id="rId" name="rId" size="9" maxlength="10"  class="required upText"/>
							&nbsp;<input type="text" id="rDupNo" name="rDupNo" size="1" maxlength="1"  class="required upText"/>
                        </td>
                        <td class="hd1">
                             <span class="text-red">＊</span>
							 <th:block th:text="#{'L162M01A.rName'}"><!-- 從債務人名稱--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                            <input type="text" id="rName" name="rName" size="30" maxlength="70" maxlengthC="40" class="required"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                              <th:block th:text="#{'L162M01A.rKindM'}"><!-- 關　　係--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                            <select id="rKindM" name="rKindM" combokey="lms1205s01_RelClass" ></select>
                                
                            <br/>
                         
                            <br/>
                           <span id="the1">
              					  <select id="rationSelect1" name="rationSelect1" combokey="Relation_type1"  space="true" ></select>
            			   </span>
              				<span id="the2" style="display:none;">
                				<select id="rationSelect2" name="rationSelect2" combokey="Relation_type2"  space="true" ></select>
          					</span>
          					<span id="the3" style="display:none;">
                				 <select id="rationSelect31" name="rationSelect31" combokey="Relation_type31"  space="true" ></select>
								 <br/>
               				     <select id="rationSelect32" name ="rationSelect32" combokey="Relation_type32" space="true" ></select>  
          					</span>
                        </td>
                        <td class="hd1">
                        	<span class="text-red">＊</span>
                              <th:block th:text="#{'L162M01A.rCountry'}"><!-- 國別--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                        	
                            <select id="rCountry" name="rCountry" combokey="CountryCode" space="true" class="required" ></select>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                        	<span class="text-red">＊</span>
                              <th:block th:text="#{'L162M01A.rType'}"><!-- 相關身份--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                            <select id="rType" name="rType" combokey="lms1605s03_rType" ></select>
                        </td>
                        <td class="hd1">
                             <th:block th:text="#{'L162M01A.dueDate'}"><!--  董監事任期止日--></th:block>&nbsp;&nbsp;
                            <br/>
                            (  <th:block th:text="#{'L162M01A.dueDate2'}"><!-- 保證人保證迄日--></th:block>)&nbsp;&nbsp;
                        </td>
                        <td>
                            <input type="text"  id="dueDate" name="dueDate" size="12" maxlength="10" class="date" />
                        </td>
                    </tr>
					
					<tr class="showGuaPercent">
                        <td class="hd1" >
                        	<div >
								<span class="text-red">＊</span>
                                <th:block th:text="#{'L162M01A.guaPercent'}"><!-- 保證人負担保證責任比率--></th:block>&nbsp;&nbsp;
							</div>                               
                        </td>
                        <td >
                        	<div >
							    <input type="text" size="18" id="guaPercent" name="guaPercent"   integer="3" fraction="2" class="numeric"/>％
							</div>
                        </td>
						<!--J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記-->
						<!--J-110-0040-TODO-->
						<td class="hd1" >
                        	<div >
								<span class="text-red">＊</span>
                                <th:block th:text="#{'L162M01A.guaNaExposure'}">本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)</th:block>&nbsp;&nbsp;
							</div>                               
                        </td>
                        <td >
                        	<div >
							    <label>
	                              <input id="guaNaExposure" name="guaNaExposure" type="radio"  value="Y" class="nodisabled" />
	                              <th:block th:text="#{'yes'}"><!-- 是--></th:block>
	                            </label>
	                            <label>
	                              <input name="guaNaExposure" type="radio"  value="N" class="nodisabled"/>
	                              <th:block th:text="#{'no'}"><!-- 否--></th:block>
	                            </label>
							</div>
                        </td>
                    </tr>
					<tr >
			                            
						<!--J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定-->
                        <td class="hd1">
                            <th:block th:text="#{'L162M01A.priority'}">信用品質順序</th:block>&nbsp;&nbsp;
                        </td>
                        <td>
							<span id="priority" class="field"></span>
                        </td>
						<td class="hd1">
                            
                        </td>
                        <td>
							 
                        </td>
                    </tr>
					<tr class="hBorrowData">  
						<td class="hd1"> 
						    <span class="text-red">＊</span>
							<th:block th:text="#{'l120s01a.chairman'}">負責人</th:block>&nbsp;&nbsp;
							<!--
							<button type="button" id="applyChairman">
	                            <span class="text-only"><th:block th:text="#{'L120S01p.btnApply'}">引進</th:block></span>
	                        </button>
							-->
						</td>
						<td>
							<th:block th:text="#{'l120s01a.custid'}">統一編號</th:block>
							<input type="text" class="alphanum trim upText" maxlength="10" name="chairmanId" id="chairmanId" size="10" />
							
							<th:block th:text="#{'l120s01a.dupno'}">重覆序號</th:block>：											
							<input type="text" class="alphanum trim upText" size="1" maxlength="1" name="chairmanDupNo" id="chairmanDupNo" />
							<br/>						
							<th:block th:text="#{'l120s01a.custname'}">名　　稱</th:block>  
							<input type="text" class="halfText trim" name="chairman" id="chairman" maxlengthC="20" />							
						</td>
						<td class="hd1">
							<!--J-106-0029-003  洗錢防制-新增實質受益人-->
							<span class="text-red">＊</span>
							<th:block th:text="#{'L120S01p.beneficiary'}">實質受益人</th:block>&nbsp;&nbsp;
							<br/>
	                        <button type="button" id="toglePersonBT" class="noHideBt">
	                            <span class="text-only"><th:block th:text="#{'other.login'}"><!--other.login --></th:block></span>
	                        </button>
						</td>
						<td>
							<!--J-106-0029-003  洗錢防制-新增實質受益人-->
							<textarea id="beneficiary" name="beneficiary" class="caseReadOnly" rows="7" maxlength="900" maxlengthC="300" readonly></textarea>	 
						</td> 
					</tr>
					<tr class="trGrtAmt" style="display:none;">
						<td colspan="4">
							<span class="text-red">
								<th:block th:text="#{'L162S02A.message01'}"><!-- 擔保限額與當地客戶識別ID僅需海外分行填寫--></th:block>
							</span>
						</td>
					</tr>
					<tr class="trGrtAmt" style="display:none;">
			        	<td class="hd1">
			            	<th:block th:text="#{'L162S02A.grtAmt'}"><!-- 擔保限額--></th:block>&nbsp;&nbsp;
			            </td>
			            <td>
			            	<input type="text" id="grtAmt" name="grtAmt" size="18" maxlength="22" integer="17" fraction="2" class="numeric"/>
			            </td>
			            <td class="hd1">
			            	<th:block th:text="#{'L162S02A.localId'}"><!-- 當地客戶識別ID--></th:block>&nbsp;&nbsp;
			            </td>
			            <td>
			            	<input type="text" id="localId" name="localId" size="30" maxlength="30"/>
			           	</td>
			        </tr>
                </table>
				</form>
            </div>
			
			<!--J-106-0029-003  洗錢防制-新增實質受益人-->
			<div id="toglePersonBox" style="display:none;">
	            <!--連保人 thickbox --><span class="text-red"><th:block th:text="#{'L120S01p.message01'}"><!-- ※請按[關閉]將資料寫回到前一頁。--></th:block></span>
	            <div id="toglePersonTabs" class="tabs">
	                <ul>
	                    <li><a href="#tabs-f_1"><b><th:block th:text="#{'L120S01p.type_01'}"><!-- 自然人--></th:block></b></a></li>
	                    <li><a href="#tabs-f_2"><b><th:block th:text="#{'L120S01p.type_02'}"><!-- 法人--></th:block></b></a></li>
	                </ul>
	                <div class="tabCtx-warp">
	                    <div id="tabs-f_1" class="content">
	                        <div id="gridviewNatural" ></div>
	                    </div><!--end tabs-f_1-->
	                    <div id="tabs-f_2" class="content">
	                        <div id="gridviewCorporate" ></div>
	                    </div><!--end tabs-f_2-->
	                </div>
	                <!--end tabCtx-warp-->
	            </div>
	        </div>
	        
			<div id="newToglePersonBox" style="display:none;">
	            <!--新增相關人/實質受益人 thickbox -->
	            <form id="L120S01PForm">
	                <table class="tb2" width="100%" border="0" cellpadding="0" cellspacing="0" id="newToglePersonTable">
	                    <tr>
	                        <td width="70%" class="hd1">
	                            <th:block th:text="#{'L120S01p.type'}"><!-- 相關人類型--></th:block>&nbsp;&nbsp;
	                        </td>
	                        <td width="30%">
	                            <label>
	                                <input type="radio" id="toglePersonType" name="toglePersonType" value="1" class="required"/>
	                                <th:block th:text="#{'L120S01p.type_01'}"><!-- 自然人--></th:block>
	                            </label>
	                            <label>
	                                <input type="radio" id="toglePersonType" name="toglePersonType" value="2" class="required"/>
	                                <th:block th:text="#{'L120S01p.type_02'}"><!-- 法人--></th:block>
	                            </label>
	                        </td>
	                    </tr>
	                    <tr>
	                        <td width="70%" class="hd1" style="width:25%">
	                            <th:block th:text="#{'L120S01p.custIdAndDupNo'}"><!-- 統一編號和重覆序號--></th:block>&nbsp;&nbsp;
	                        </td>
	                        <td width="30%">
	                            <input type="text" id="rId" name="rId" size="10" maxlength="10" class="upText"/>
	                            <input type="text" id="rDupNo" name="rDupNo" size="1" maxlength="1" class="upText obuText" />
	                        </td>
	                    </tr>
	                    <tr>
	                        <td class="hd1">
	                            <th:block th:text="#{'L120S01p.conPersonName'}"><!-- 名稱--></th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <input type="text" size="30" id="rName" name="rName" maxlength="120" maxlengthC="40" class="required halfText"/>
	                            <br/>
	                            &nbsp;
	                        </td>
	                    </tr>
						<tr>
	                        <td class="hd1">
	                            <th:block th:text="#{'L120S01p.conPersonEName'}"><!-- 英文名稱--></th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <input type="text" size="30" id="rEName" name="rEName" maxlength="120" maxlengthC="40" class="halfText halfword" />
	                            <br/>
	                            &nbsp;
	                        </td>
	                    </tr>
						<!--J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
						<tr>
	                        <td class="hd1">
	                            <th:block th:text="#{'L120S01p.nation'}">國別</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <select id="nation" name="nation" combokey="CountryCode" combotype="4" space="true"></select>
	                            <br/>
	                            &nbsp;
	                        </td>
	                    </tr>
						-->
						<tr>
	                        <td class="hd1">
	                            <th:block th:text="#{'L120S01p.peps'}">PEPS</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <span id="peps" class="field"></span>
	                            <br/>
	                            &nbsp;
	                        </td>
	                    </tr>
	                </table>
	                <input type="text" id="l120s01pFormOid" name="l120s01pFormOid" style="display:none"/>
					<input type="text" id="rType" name="rType" style="display:none"/>
	            </form>
	        </div>
			<!--J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定-->
			<div id="setGuarantorSeqThickBox" style="display:none;">
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td class="hd1">
                            <th:block th:text="#{'L162M01A.message7'}"><!-- 注意事項--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                            <span class="text-red">(1)<th:block th:text="#{'L162M01A.message4'}">借款人為企業戶且保證人(一般/連帶)亦為企業戶時，需要填列保證人信用品質順序。</th:block></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <th:block th:text="#{'L162M01A.message8'}"><!--操作說明--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                            <span class="text-red">(1)<th:block th:text="#{'L162M01A.message5'}">設定各保證人之優先順序，由1開始依序編排，不得跳號。</th:block></span>
                            <br/>
                            <span class="text-red">(2)<th:block th:text="#{'L162M01A.message2'}">保證人必需設定信用品質順序，直到該額度之保證人之負担保證責任比率合計達100%。</th:block></span>
                            <br/>
                            <span class="text-red">(3)<th:block th:text="#{'L162M01A.message6'}">執行寫回額度明細表。</th:block></span>
                        </td>
                    </tr>
                </table>
                
                <div id="gridViewGuarantorSeq" ></div>
            </div>
			
			<!--J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記-->
			<div id="choiceEntireApply" style="display:none; margin-top:5px;">
			    <!--整批引進最新資料 thickbox 項目從CODETYPE lms1401s02_EntireApplyNew(海外是從lms1405s02_EntireApplyNew) 來-->
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
                            <input type="checkbox" name="entireApply" id="entireApply" />
                        </td>
					</tr>
					
					<tr id="showGuaNaExposure"  >
						<td>
                           <span class="text-red">＊</span>
				           <th:block th:text="#{'L162M01A.guaNaExposure'}">本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)</th:block>&nbsp;&nbsp;
						   <div >
							    <label>
	                              <input id="guaNaExposureTmp" name="guaNaExposureTmp" type="radio"  value="Y" class="nodisabled"   />
	                              <th:block th:text="#{'yes'}"><!-- 是--></th:block>
	                            </label>
	                            <label>
	                              <input name="guaNaExposureTmp" type="radio"  value="N" class="nodisabled"/>
	                              <th:block th:text="#{'no'}"><!-- 否--></th:block>
	                            </label>
							</div>
                        </td>
					</tr>
					 
				</table>	
			</div>
			<!-- G-113-0036 連保人excel匯入 -->
			<div style="display:none;">
			    <!-- dialog start-->
			    <div id="uploadDialog" class="popup_cont" style="display:none;" title="">
			        <form id="filterForm">
			            <!-- onsubmit="return false" -->
			            <fieldset>
			                <legend>
			                	<th:block th:text="#{'L162S02A.FileUpload'}">檔案上傳</th:block>
							</legend>
			                <table class="tL tb2" border="0" cellpadding="0" cellspacing="0" width="100%">
			                    <tbody><tr>
			                        <td>
			                            <input name="LMS1602S01" id="LMS1602S01" type="file" class="required">
			                        </td>
			                    </tr>
			                </tbody></table>
			            </fieldset>
			        </form>
			    </div>
			    <!-- dialog end -->
			</div>
        </th:block>
    </body>
</html>