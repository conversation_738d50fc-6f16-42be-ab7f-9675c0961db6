/* 
 *  lms0005GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.handler.grid;

import java.util.Calendar;
import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.cls.pages.CLS0001V00Page;
import com.mega.eloan.lms.cls.pages.CLS1141M01Page;
import com.mega.eloan.lms.cls.service.CLS0001Service;
import com.mega.eloan.lms.model.L000M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 近期已收案件
 * </pre>
 * 
 * @since 2012/1/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/18,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("cls0001gridhandler")
public class CLS0001GridHandler extends AbstractGridHandler {

	@Resource
	CLS0001Service cls0001Service;

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branchService;

	/**
	 * 近期已收案件的grid
	 * 
	 * @param pageSetting
	 *            頁面相關設定
	 * @param params
	 *            前端資料
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult query(ISearch pageSetting, PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				user.getUnitNo());

		// 排除掉海外授信案件
//		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
//				UtilConstants.Casedoc.typCd.海外);
		// 限定只顯示個金案件
//		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
//				UtilConstants.Casedoc.DocType.個金);			
		
		// 14天以內的案件
		TWNDate date = new TWNDate();
		date.add(Calendar.DATE, -14);
		pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "sendInfo",
				new Object[] { date, CapDate.getCurrentTimestamp() });
		// 限定只顯示個金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.個金);			
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS0001V00Page.class);
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(CLS1141M01Page.class);
		Page<L000M01A> page = cls0001Service.findPage(pageSetting);
		List<L000M01A> l000m01as = page.getContent();
		StringBuffer temp = new StringBuffer();
		for (L000M01A l000m01a : l000m01as) {
			if ("L120M01A".equals(l000m01a.getCaseType())) {
				l000m01a.setCaseType(this.getCaseType(l000m01a, pop2, temp));
			} else {
				l000m01a.setCaseType(pop.getProperty(StrUtils.concat("model.",
						l000m01a.getCaseType())));
			}
			l000m01a.setDocStatus(getMessage(StrUtils.concat("docStatus.",
					l000m01a.getDocStatus())));
			l000m01a.setCustId(StrUtils.concat(l000m01a.getCustId(), " ",
					l000m01a.getDupNo()));
			l000m01a.setSendBrid(branchService.getBranchName(l000m01a
					.getSendBrid()));
			l000m01a.setCaseNo(Util.toSemiCharString(l000m01a.getCaseNo()));

		}
		return new CapGridResult(l000m01as, page.getTotalRow());
	}

	/**
	 * 取得 案件類別
	 * 
	 * @param model
	 *            近期已收檔
	 * @param pop
	 *            語系檔
	 * @param temp
	 *            暫存的stringBuffer
	 * 
	 * @return
	 */
	private String getCaseType(L000M01A model, Properties pop, StringBuffer temp) {
		temp.setLength(0);
		if (UtilConstants.Casedoc.DocType.企金.equals(model.getDocType())) {
			if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {
				temp.append(pop.getProperty("L1205G.grid1"));
			} else {
				temp.append(pop.getProperty("L1205G.grid2"));
			}
		} else {
			if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {
				temp.append(pop.getProperty("L1205G.grid12"));
			} else {
				temp.append(pop.getProperty("L1205G.grid13"));
			}
		}
		// L1205G.grid9=一般
		// L1205G.grid10=其他
		// L1205G.grid11=陳復/陳述案
		temp.append("(");
		if (UtilConstants.Casedoc.DocCode.一般.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid9"));
		} else if (UtilConstants.Casedoc.DocCode.其他.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid10"));
		} else {
			temp.append(pop.getProperty("L1205G.grid11"));
		}
		temp.append(")");
		return temp.toString();
	}
	
	

}
