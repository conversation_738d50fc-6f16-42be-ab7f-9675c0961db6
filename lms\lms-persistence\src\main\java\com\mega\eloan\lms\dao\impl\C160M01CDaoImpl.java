/* 
 * C160M01CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C160M01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C160M01C;

/** 檢附資訊檔明細檔 **/
@Repository
public class C160M01CDaoImpl extends LMSJpaDao<C160M01C, String>
	implements C160M01CDao {

	@Override
	public C160M01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C160M01C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		LinkedHashMap<String, Boolean> orderBy = new LinkedHashMap<String, Boolean>();
		orderBy.put("itemType", false);
		orderBy.put("itemSeq", false);
		search.setOrderBy(orderBy);
		List<C160M01C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C160M01C findByUniqueKey(String oid){
		ISearch search = createSearchTemplete();
		if (oid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C160M01C> findByIndex01(String oid){
		ISearch search = createSearchTemplete();
		List<C160M01C> list = null;
		if (oid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public C160M01C findByUniqueKey(String mainId, String itemCode, String itemType) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (itemCode != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemCode", itemCode);
		if (itemType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}
}