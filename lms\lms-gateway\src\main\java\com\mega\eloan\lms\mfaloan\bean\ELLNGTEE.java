/* 
 * ELLNGTEE .java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;


import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;


/** 主從債務人資料檔 **/
public class ELLNGTEE  extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Column(name="BRNO", length=3, columnDefinition="CHAR(03)",unique = true)
	private String brno;

	/** 客戶統一編號 **/
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10) ",unique = true)
	private String custid;

	/** 重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(01)",unique = true)
	private String dupno;

	/** 額度序號 **/
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)",unique = true)
	private String cntrno;

	/** 
	 * 相關身分<p/>
	 * C: 共同借款人<br/>
	 *  D: 共同發票人　                         <br/>
	 *  E: 票據債務人（指金融交易之擔保背書）               <br/>
	 *  G: 連帶保證人，擔保品提供人兼連帶保證人             <br/>
	 *  L: 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人 <br/>
	 *  S: 擔保品提供人  <br/>
	 *  N: ㄧ般保證人
	 */
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(01)",unique = true)
	private String lngeflag;

	/** 身份證號 **/
	@Column(name="LNGEID", length=10, columnDefinition="CHAR(10)",unique = true)
	private String lngeid;

	/** 重複序號 **/
	@Column(name="DUPNO1", length=1, columnDefinition="CHAR(01)",unique = true)
	private String dupno1;

	/** 名稱 **/
	@Column(name="LNGENM", length=40, columnDefinition="CHAR(40)")
	private String lngenm;

	/** 
	 * 從債務人類別<p/>
	 * A.中央政府 B.地方政府 <br/>
	 *  C.銀行 D.保證機關,空白
	 */
	@Column(name="LNGEKIND", length=1, columnDefinition="CHAR(01)")
	private String lngekind;

	/** 國家別 **/
	@Column(name="NTCODE", length=2, columnDefinition="CHAR(02)")
	private String ntcode;

	/** 
	 * 與主債務人關係<p/>
	 * 自然人第一碼為    X<br/>
	 *  第二碼為    A:配偶 B:父母 C:子女 D:兄弟姐妹 E:祖父母 F:外祖父母 G:孫子女 H:外孫子女  I:配偶之父母 J:配偶之兄弟姐妹 K其他親屬 L:其他非親屬自然人
	 */
	@Column(name="LNGERE", length=2, columnDefinition="CHAR(02)")
	private String lngere;

	/** 更新日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="UPDDT", columnDefinition="DATE")
	private Date upddt;

	/** 
	 * 是否對外報送<p/>
	 * 預設為Y
	 */
	@Column(name="LNGEOUT", length=1, columnDefinition="CHAR(01)")
	private String lngeout;

	/** 資料修改人（行員代號） **/
	@Column(name="UPDATER", length=8, columnDefinition="CHAR(08)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="TMESTAMP", columnDefinition="TIMESTAMP")
	private Date tmestamp;

	/** 董監事註記 **/
	@Column(name="LNGEPOS", length=4, columnDefinition="CHAR(04)")
	private String lngepos;

	/** 
	 * 徵提保證人原因代碼<p/>
	 * 01)借款人薪資收入條件不足。<br/>
	 *  02)借款人年齡較大致使可工作年限短於借款期限。<br/>
	 *  03)借款人有信用不良紀錄。<br/>
	 *  04)借款人所提供之擔保品非屬自己所有。<br/>
	 *  99)無法歸類於前項各款者。(請說明原因，限20個字以內)
	 */
	@Column(name="REASONCD", length=2, columnDefinition="CHAR(02)")
	private String reasoncd;

	/** 徵提保證人理由說明 **/
	@Column(name="REASONMEMO", length=202, columnDefinition="VARCHAR(202)")
	private String reasonmemo;
	
	/**
	 * 保證人按一定比率負担保證責任成數
	 */
	@Column(name="GRTRT", columnDefinition="DECIMAL(5,2)")
	private BigDecimal grtrt;

	/** 取得分行別 **/
	public String getBrno() {
		return this.brno;
	}
	/** 設定分行別 **/
	public void setBrno(String value) {
		this.brno = value;
	}

	/** 取得客戶統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定客戶統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得額度序號 **/
	public String getCntrno() {
		return this.cntrno;
	}
	/** 設定額度序號 **/
	public void setCntrno(String value) {
		this.cntrno = value;
	}

	/** 
	 * 取得相關身分<p/>
	 * C: 共同借款人<br/>
	 *  D: 共同發票人　                         <br/>
	 *  E: 票據債務人（指金融交易之擔保背書）               <br/>
	 *  G: 連帶保證人，擔保品提供人兼連帶保證人             <br/>
	 *  L: 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人 <br/>
	 *  S: 擔保品提供人  <br/>
	 *  N: ㄧ般保證人
	 */
	public String getLngeflag() {
		return this.lngeflag;
	}
	/**
	 *  設定相關身分<p/>
	 *  C: 共同借款人<br/>
	 *  D: 共同發票人　                         <br/>
	 *  E: 票據債務人（指金融交易之擔保背書）               <br/>
	 *  G: 連帶保證人，擔保品提供人兼連帶保證人             <br/>
	 *  L: 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人 <br/>
	 *  S: 擔保品提供人  <br/>
	 *  N: ㄧ般保證人
	 **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 取得身份證號 **/
	public String getLngeid() {
		return this.lngeid;
	}
	/** 設定身份證號 **/
	public void setLngeid(String value) {
		this.lngeid = value;
	}

	/** 取得重複序號 **/
	public String getDupno1() {
		return this.dupno1;
	}
	/** 設定重複序號 **/
	public void setDupno1(String value) {
		this.dupno1 = value;
	}

	/** 取得名稱 **/
	public String getLngenm() {
		return this.lngenm;
	}
	/** 設定名稱 **/
	public void setLngenm(String value) {
		this.lngenm = value;
	}

	/** 
	 * 取得從債務人類別<p/>
	 * A.中央政府 B.地方政府 <br/>
	 *  C.銀行 D.保證機關,空白
	 */
	public String getLngekind() {
		return this.lngekind;
	}
	/**
	 *  設定從債務人類別<p/>
	 *  A.中央政府 B.地方政府 <br/>
	 *  C.銀行 D.保證機關,空白
	 **/
	public void setLngekind(String value) {
		this.lngekind = value;
	}

	/** 取得國家別 **/
	public String getNtcode() {
		return this.ntcode;
	}
	/** 設定國家別 **/
	public void setNtcode(String value) {
		this.ntcode = value;
	}

	/** 
	 * 取得與主債務人關係<p/>
	 * 自然人第一碼為    X<br/>
	 *  第二碼為    A:配偶 B:父母 C:子女 D:兄弟姐妹 E:祖父母 F:外祖父母 G:孫子女 H:外孫子女  I:配偶之父母 J:配偶之兄弟姐妹 K其他親屬 L:其他非親屬自然人
	 */
	public String getLngere() {
		return this.lngere;
	}
	/**
	 *  設定與主債務人關係<p/>
	 *  自然人第一碼為    X<br/>
	 *  第二碼為    A:配偶 B:父母 C:子女 D:兄弟姐妹 E:祖父母 F:外祖父母 G:孫子女 H:外孫子女  I:配偶之父母 J:配偶之兄弟姐妹 K其他親屬 L:其他非親屬自然人
	 **/
	public void setLngere(String value) {
		this.lngere = value;
	}

	/** 取得更新日 **/
	public Date getUpddt() {
		return this.upddt;
	}
	/** 設定更新日 **/
	public void setUpddt(Date value) {
		this.upddt = value;
	}

	/** 
	 * 取得是否對外報送<p/>
	 * 預設為Y
	 */
	public String getLngeout() {
		return this.lngeout;
	}
	/**
	 *  設定是否對外報送<p/>
	 *  預設為Y
	 **/
	public void setLngeout(String value) {
		this.lngeout = value;
	}

	/** 取得資料修改人（行員代號） **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改人（行員代號） **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Date getTmestamp() {
		return this.tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setTmestamp(Date value) {
		this.tmestamp = value;
	}

	/** 取得董監事註記 **/
	public String getLngepos() {
		return this.lngepos;
	}
	/** 設定董監事註記 **/
	public void setLngepos(String value) {
		this.lngepos = value;
	}


	/** 
	 * 取得徵提保證人原因代碼<p/>
	 * 01)借款人薪資收入條件不足。<br/>
	 *  02)借款人年齡較大致使可工作年限短於借款期限。<br/>
	 *  03)借款人有信用不良紀錄。<br/>
	 *  04)借款人所提供之擔保品非屬自己所有。<br/>
	 *  99)無法歸類於前項各款者。(請說明原因，限20個字以內)
	 */
	public String getReasoncd() {
		return this.reasoncd;
	}
	/**
	 *  設定徵提保證人原因代碼<p/>
	 *  01)借款人薪資收入條件不足。<br/>
	 *  02)借款人年齡較大致使可工作年限短於借款期限。<br/>
	 *  03)借款人有信用不良紀錄。<br/>
	 *  04)借款人所提供之擔保品非屬自己所有。<br/>
	 *  99)無法歸類於前項各款者。(請說明原因，限20個字以內)
	 **/
	public void setReasoncd(String value) {
		this.reasoncd = value;
	}

	/** 取得徵提保證人理由說明 **/
	public String getReasonmemo() {
		return this.reasonmemo;
	}
	/** 設定徵提保證人理由說明 **/
	public void setReasonmemo(String value) {
		this.reasonmemo = value;
	}
	
	/**
	 * 設定保證人按一定比率負担保證責任成數
	 */
	public void setGrtrt(BigDecimal value) {
		this.grtrt = value;
	}
	
	/**
	 * 取得保證人按一定比率負担保證責任成數
	 */
	public BigDecimal getGrtrt() {
		return this.grtrt;
	}
}
