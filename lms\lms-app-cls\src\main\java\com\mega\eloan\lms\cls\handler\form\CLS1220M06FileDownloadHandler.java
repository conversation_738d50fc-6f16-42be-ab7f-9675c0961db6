package com.mega.eloan.lms.cls.handler.form;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.FileDownLoadConstant;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122S01C;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FileDownloadHandler;
import tw.com.iisi.cap.response.CapByteArrayDownloadResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 經濟部中小企業處-青創貸款線上申貸案 WORD 檔案下載
 * </pre>
 * 
 * @since 2022/03/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/03/16,011879,new
 *          </ul>
 */
@Scope("request")
@Controller("cls1220m06filedownloadhandler")
public class CLS1220M06FileDownloadHandler extends FileDownloadHandler {

	@Resource
	CLS1220Service service;

	@Override
	public IResult beforeDownload(PageParameters params) throws CapException {
		IResult result = null;
		String oid = params.getString(EloanConstants.OID);
		C122M01A meta = service.getC122M01A_byOid(oid);

		if (meta != null) {
			C122S01C c122s01c = service.getC122S01C(meta.getMainId());
			if (c122s01c == null) {
				c122s01c = new C122S01C();
			}
			// I:100萬以下,J:100萬以上
			String fileName = Util.equals(UtilConstants.C122_ApplyKind.I,meta.getApplyKind()) ? 
					"青創貸款線上申貸案-100萬以下.doc" : "青創貸款線上申貸案-100萬以上.doc";
			byte[] content = service.getWordContent(meta, c122s01c);
			if (content != null) {
				result = new CapByteArrayDownloadResult(content, FileDownLoadConstant.CONTENT_TTYPE_WORD, fileName);
			} else {
				// 無法下載此文件，請洽資訊室。
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0061"), null);
			}
		} else {
			// 無法下載此文件，請洽資訊室。
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0061"), null);
		}

		return result;
	}

	@Override
	public String getOperationName() {
		return "fileDownloadOperation";
	}

}
