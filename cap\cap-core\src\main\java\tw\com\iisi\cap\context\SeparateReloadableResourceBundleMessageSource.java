/* 
 * SeparateReloadableResourceBundleMessageSource.java
 * 
 * Copyright (c) 2021 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package tw.com.iisi.cap.context;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import javax.annotation.PostConstruct;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.lang.Nullable;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * <pre>
 * 配置i18n訊息
 * </pre>
 * 
 * @since 2021年9月26日
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2021年9月26日,1104263,new
 *          </ul>
 */
public class SeparateReloadableResourceBundleMessageSource extends ReloadableResourceBundleMessageSource {

    /**
     * 語言
     */
    private String[] languages;

    /**
     * Class路徑
     */
    private String basePath;

    /**
     * 存放Class路徑和對應語言檔案
     */
    private final ConcurrentMap<String, String> cachedBasenames = new ConcurrentHashMap<>();

    /**
     * 初始化賦值, 設置對應屬性文件位置
     */
    @PostConstruct
    public void init() {
        Set<String> basenameSet = new HashSet<String>();
        String i18nPattern = getBasePath() + "/**/*.properties";
        String i18nFileREG = "(" + StringUtils.join(languages, '|') + ").properties$";
        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

            Resource[] resources = resolver.getResources(i18nPattern);
            for (Resource resource : resources) {
                String path = resource.getURI().toString();
                String fileName = FileUtils.getFile(path).getName();
                String basename = "classpath:/i18n/" + path.replaceAll(i18nFileREG, "").substring(path.indexOf("i18n/") + 5)
                        // for windows
                        .replaceAll(".*\\\\i18n\\\\", "classpath:\\\\i18n\\\\").replaceAll("\\\\", "/");
                String basenameKey = fileName.replaceAll(i18nFileREG, "");
                basenameKey = StringUtils.removeEnd(basenameKey, "Page");
                basenameKey = StringUtils.removeEnd(basenameKey, "Panel");
                cachedBasenames.put(basenameKey, basename);
                basenameSet.add(basename);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        super.setBasenames(basenameSet.toArray(new String[basenameSet.size()]));
    }

    /*
     * 根據地區返回解析代碼, 若無則返回原代碼
     * 
     * @see org.springframework.context.support.ReloadableResourceBundleMessageSource#resolveCodeWithoutArguments(java.lang.String, java.util.Locale)
     */
    @Override
    protected String resolveCodeWithoutArguments(String code, Locale locale) {
        try {
            LinkedHashSet keySet = (LinkedHashSet) RequestContextHolder.getRequestAttributes().getAttribute("basenameKey", RequestAttributes.SCOPE_REQUEST);
            if (keySet == null) {
                keySet = new LinkedHashSet();
            }
            keySet.add("AbstractEloan");

            // 先找有加 i18n key 的
            for (Object key : keySet) {
                String basenameKey = (String) key;
                basenameKey = StringUtils.removeEnd(basenameKey, "Page");
                basenameKey = StringUtils.removeEnd(basenameKey, "Panel");
                String basename = cachedBasenames.get(basenameKey);
                String propKey = code;
                List<String> filenames = calculateAllFilenames(basename, locale);
                for (String filename : filenames) {
                    PropertiesHolder propHolder = getProperties(filename);
                    String result = propHolder.getProperty(propKey);
                    if (result != null) {
                        return result;
                    }
                }
            }

            // 沒有的話以第一節為 key 再找找
            int idx = code.indexOf('.');
            String basenameKey = idx > 0 ? code.substring(0, idx) : code;
            basenameKey = StringUtils.removeEnd(basenameKey, "Page");
            basenameKey = StringUtils.removeEnd(basenameKey, "Panel");
            if (logger.isTraceEnabled())
                logger.trace(basenameKey + ": " + code);

            String basename = cachedBasenames.get(basenameKey);
            if (basename != null) {
                String propKey = code.substring(idx + 1);
                List<String> filenames = calculateAllFilenames(basename, locale);
                for (String filename : filenames) {
                    PropertiesHolder propHolder = getProperties(filename);
                    String result = propHolder.getProperty(propKey);
                    if (result != null) {
                        return result;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("can't find " + code + " with locale " + locale.toString(), e);
        }
        return code;
    }

    /*
     * 解析代碼, 若無對應代碼則返回空值
     * 
     * @see org.springframework.context.support.ReloadableResourceBundleMessageSource#resolveCode(java.lang.String, java.util.Locale)
     */
    @Override
    @Nullable
    protected MessageFormat resolveCode(String code, Locale locale) {
        for (String basename : getBasenameSet()) {
            List<String> filenames = calculateAllFilenames(basename, locale);
            for (String filename : filenames) {
                PropertiesHolder propHolder = getProperties(filename);
                MessageFormat result = propHolder.getMessageFormat(code, locale);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }

    /**
     * 取得Class路徑
     * 
     * @return the basePath
     */
    public String getBasePath() {
        return basePath;
    }

    /**
     * 設置Class路徑
     * 
     * @param basePath
     *            the basePath to set
     */
    public void setBasePath(String basePath) {
        this.basePath = basePath;
    }

    /**
     * 取得語言
     * 
     * @return the languages
     */
    public String[] getLanguages() {
        return languages;
    }

    /**
     * 設置語言
     * 
     * @param languages
     *            the languages to set
     */
    public void setLanguages(String[] languages) {
        this.languages = languages;
    }

    /**
     * 解析綴詞
     * 
     * @param prefix
     * @param locale
     * @return
     */
    public Map<String, String> resolvePrefix(String prefix, Locale locale) {
        Map<String, String> result = new HashMap<>();
        prefix = StringUtils.removeEnd(prefix, "Page");
        prefix = StringUtils.removeEnd(prefix, "Panel");
        String basename = cachedBasenames.get(prefix);
        List<String> filenames = calculateAllFilenames(basename, locale);
        for (String filename : filenames) {
            PropertiesHolder propHolder = getProperties(filename);
            Properties properties = propHolder.getProperties();
            if (properties != null) {
                for (Entry<Object, Object> entry : properties.entrySet()) {
                    // 選擇不同語系時，仍可能會對應到多個 properties，第一個是選擇的語系、第二個是預設語系，所以應判斷 key 是否已經存在，否則會被預設語系蓋掉。
                    if (!result.containsKey(entry.getKey())) {
                        result.put((String) entry.getKey(), (String) entry.getValue());
                    }
                }
            }
        }
        return result;
    }

    /*
     * 計算群組檔案數量
     * 
     * @see org.springframework.context.support.ReloadableResourceBundleMessageSource#calculateAllFilenames(java.lang.String, java.util.Locale)
     */
    @Override
    public List<String> calculateAllFilenames(String basename, Locale locale) {
        if (basename == null) {
            basename = cachedBasenames.get("AbstractEloan");
        }
        return super.calculateAllFilenames(basename, locale);
    }
}
