<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        	 
	            <tr class="lmss08jk_button show_lmss08o_panel">
	                <th class="hd2">
	                    <th:block th:text="#{'L120S08O.title'}">授審會/常董會提案稿</th:block>
						<br>
						<a href="img/lms/J-112-0508.pdf" target="_blank" ><th:block th:text="#{'L120S08O.OpenDescription'}">開啟說明</th:block></a>
	                </th>
	                <td class="left2">
	                    <button id="lmss08o_generate" type="button">
	                        <span class="text-only"><span><th:block th:text="#{'L120S08O.bt04'}">匯入</th:block></span></span>
	                    </button>
	                </td>
	                <td>
	                    <form id="formfile">
	                    	<div class="funcContainer">
								<button id="lmss08o_choiceFile1" type="button">
			                        <span class="text-only"><span><th:block th:text="#{'L120S08O.choiceFile1'}">勾選(授審會)</th:block></span></span>
			                    </button>
								<button id="lmss08o_choiceFile2" type="button">
			                        <span class="text-only"><span><th:block th:text="#{'L120S08O.choiceFile2'}">勾選(常董會_個案)</th:block></span></span>
			                    </button>
								<button id="lmss08o_choiceFile3" type="button">
			                        <span class="text-only"><span><th:block th:text="#{'L120S08O.choiceFile3'}">勾選(常董會_彙總)</th:block></span></span>
			                    </button>
								<button id="lmss08o_updateFileName" type="button">
			                        <span class="text-only"><span><th:block th:text="#{'L120S08O.updateFileName'}">修改檔案資訊)</th:block></span></span>
			                    </button>
								<button id="lmss08o_clearRpaCombine" type="button">
			                        <span class="text-only"><span><th:block th:text="#{'L120S08O.clearRpaCombine'}">清除列印紀錄(管理用)</th:block></span></span>
			                    </button>
								<br>
								<button id="lmss08o_sendRpaCombine" type="button">
			                        <span class="text-only"><span><th:block th:text="#{'L120S08O.sendRpaCombine'}">合併列印</th:block></span></span>
			                    </button>
								<br>
		                        
								<div>
								        <th:block th:text="#{'L120S08O.combinePrint_time'}">送出時間</th:block>：<span id="combinePrint_time" class="field"></span>
								   <br>
								        <th:block th:text="#{'L120S08O.combinePrint_unid'}">送出序號</th:block>：<span id="combinePrint_unid" class="field"></span>
								   <br>
								        <th:block th:text="#{'L120S08O.combinePrint_status'}">列印狀態</th:block>：<select id="combinePrint_status" name="combinePrint_status" comboKey="lms120_combinePrintStatus" space="true" disabled="disabled"></select><br>  
								   <br>
								        <th:block th:text="#{'L120S08O.combinePrint_errMsg'}">錯誤訊息</th:block>：<span id="combinePrint_errMsg" class="field"></span>
								</div>	
								<br>
								<button type="button" id="lmss08o_uploadFile">
		                            <span class="text-only"><th:block th:text="#{'L120S08O.bt01'}"><!-- 選擇附加檔案--></th:block></span>
		                        </button>
		                        <button type="button" id="lmss08o_deleteFile">
		                            <span class="text-only"><th:block th:text="#{'L120S08O.bt02'}"><!-- 刪除--></th:block></span>
		                        </button>
								<br>
								<div id="lmss08o_gridfile"></div>
		                    </div>
						</form>
	                </td>
	            </tr>
			 
			
        </th:block>
    </body>
</html>
