package tw.com.jcs.common;

import java.math.BigDecimal;

/**
 * <pre>
 * 由於Java的簡單類型(float,double)不能夠精確的對浮點數進行運算， 這個類別提供精確的浮點數運算，包括加減乘除和四捨五入。
 * </pre>
 * 
 * @since 2008年06月18日
 * <AUTHOR>
 * @version 1.0
 *          <ul>
 *          <li>2008年06月18日
 *          </ul>
 */
public final class Arithmetic {
    // 默認除法運算精度
    private static final int DEF_DIV_SCALE = 10;
    private static final int DEF_SCALE = 2;

    // 這個類不能實例化
    private Arithmetic() {
    }

    /**
     * 提供精確的加法運算。
     * 
     * @param v1
     *            被加數
     * @param v2
     *            加數
     * @return 兩個參數的和
     */
    public static double add(String v1, String v2) {
        return add(parseDouble(v1), parseDouble(v2));
    }

    /**
     * 提供精確的加法運算。
     * 
     * @param v1
     *            被加數
     * @param v2
     *            加數
     * @return 兩個參數的和
     */
    public static double add(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));

        return b1.add(b2).doubleValue();
    }

    /**
     * 提供精確的減法運算。
     * 
     * @param v1
     *            被減數
     * @param v2
     *            減數
     * @return 兩個參數的差
     */
    public static double sub(String v1, String v2) {
        return sub(parseDouble(v1), parseDouble(v2));
    }

    /**
     * 提供精確的減法運算。
     * 
     * @param v1
     *            被減數
     * @param v2
     *            減數
     * @return 兩個參數的差
     */
    public static double sub(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return Arithmetic.sub(b1, b2).doubleValue();
    }

    /**
     * 提供精確的減法運算。
     * 
     * @param v1
     *            被減數
     * @param v2
     *            減數
     * @return 兩個參數的差
     */
    public static BigDecimal sub(BigDecimal v1, BigDecimal v2) {
        BigDecimal b1 = Util.parseToBigDecimal(v1);
        BigDecimal b2 = Util.parseToBigDecimal(v2);
        return b1.subtract(b2);
    }

    /**
     * 提供精確的乘法運算。
     * 
     * @param v1
     *            被乘數
     * @param v2
     *            乘數
     * @return 兩個參數的積
     */
    public static double mul(String v1, String v2) {
        return mul(parseDouble(v1), parseDouble(v2));
    }

    /**
     * 提供精確的乘法運算。
     * 
     * @param v1
     *            被乘數
     * @param v2
     *            乘數
     * @return 兩個參數的積
     */
    public static double mul(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));

        return b1.multiply(b2).doubleValue();
    }

    /**
     * 提供精確的乘法運算。
     * 
     * @param v1
     *            被乘數
     * @param v2
     *            乘數
     * @return 兩個參數的積
     */
    public static BigDecimal mul(BigDecimal v1, BigDecimal v2) {
        BigDecimal b1 = (v1 == null ? BigDecimal.ZERO : v1);
        BigDecimal b2 = (v2 == null ? BigDecimal.ZERO : v2);
        return b1.multiply(b2);
    }

    /**
     * 提供（相對）精確的除法運算，當發生除不盡的情況時， 精確到小數點以後10位，以後的數字四捨五入。
     * 
     * @param v1
     *            被除數
     * @param v2
     *            除數
     * @return 兩個參數的商
     */
    public static double div(String v1, String v2) {
        return div(parseDouble(v1), parseDouble(v2));
    }

    /**
     * 提供（相對）精確的除法運算，當發生除不盡的情況時， 精確到小數點以後10位，以後的數字四捨五入。
     * 
     * @param v1
     *            被除數
     * @param v2
     *            除數
     * @return 兩個參數的商
     */
    public static double div(double v1, double v2) {
        return div(v1, v2, DEF_DIV_SCALE);
    }

    /**
     * 提供（相對）精確的除法運算。當發生除不盡的情況時， 由scale參數指定精度，以後的數字四捨五入。
     * 
     * @param v1
     *            被除數
     * @param v2
     *            除數
     * @param scale
     *            表示表示需要精確到小數點以後幾位。
     * @return 兩個參數的商
     */
    public static double div(String v1, String v2, int scale) {
        return div(parseDouble(v1), parseDouble(v2), scale);
    }

    /**
     * 提供（相對）精確的除法運算。當發生除不盡的情況時， 由scale參數指定精度，以後的數字四捨五入。
     * 
     * @param v1
     *            被除數
     * @param v2
     *            除數
     * @param scale
     *            表示表示需要精確到小數點以後幾位。
     * @return 兩個參數的商
     */
    public static double div(double v1, double v2, int scale) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return div(b1, b2, scale).doubleValue();
    }

    /**
     * 提供（相對）精確的除法運算。當發生除不盡的情況時， 由scale參數指定精度，以後的數字四捨五入。
     * 
     * @param v1
     *            被除數
     * @param v2
     *            除數
     * @param scale
     *            表示表示需要精確到小數點以後幾位。 {@link tw.com.jcs.common.Arithmetic#DEF_DIV_SCALE}
     * @return 兩個參數的商
     */
    public static BigDecimal div(BigDecimal v1, BigDecimal v2) {
        return div(v1, v2, DEF_DIV_SCALE);
    }

    /**
     * 提供（相對）精確的除法運算。當發生除不盡的情況時， 由scale參數指定精度，以後的數字四捨五入。
     * 
     * @param v1
     *            被除數
     * @param v2
     *            除數
     * @param scale
     *            表示表示需要精確到小數點以後幾位。
     * @return 兩個參數的商
     */
    public static BigDecimal div(BigDecimal v1, BigDecimal v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        if (Util.isEmpty(v1) || Util.isEmpty(v2) || v1.compareTo(BigDecimal.ZERO) == 0 || v2.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return v1.divide(v2, scale, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 提供（相對）精確的除法運算。當發生除不盡的情況時， 由scale參數指定精度，以後的數字無條件捨去。
     * 
     * @param v1
     *            被除數
     * @param v2
     *            除數
     * @param scale
     *            表示表示需要精確到小數點以後幾位。
     * @return 兩個參數的商
     * @deprecated 希望全部改用 public static <T extends Number> BigDecimal div_floor(T t1, T t2, int scale)
     */
    public static double div_floor(double v1, double v2, int scale) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));

        return div_floor(b1, b2, scale).doubleValue();
    }

    /**
     * 提供（相對）精確的除法運算。當發生除不盡的情況時， 由scale參數指定精度，以後的數字無條件捨去。
     * 
     * @param v1
     *            被除數
     * @param v2
     *            除數
     * @param scale
     *            表示表示需要精確到小數點以後幾位。
     * @return 兩個參數的商
     */
    public static BigDecimal div_floor(BigDecimal b1, BigDecimal b2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        return b1.divide(b2, scale, BigDecimal.ROUND_FLOOR);
    }

    /**
     * 提供精確的小數位四捨五入處理。
     * 
     * @param v
     *            需要四捨五入的數字
     * @param scale
     *            小數點後保留幾位
     * @return 四捨五入後的結果
     */
    public static double round(String v, int scale) {
        return round(parseDouble(v), scale);
    }

    /**
     * 提供精確的小數位四捨五入處理。
     * 
     * @param v
     *            需要四捨五入的數字
     * @param scale
     *            小數點後保留幾位
     * @return 四捨五入後的結果
     */
    public static double round(double v, int scale) {
        return round(new BigDecimal(Double.toString(v)), scale).doubleValue();
    }

    /**
     * 四捨五入(小數點第二位)
     * 
     * @param v
     *            需要四捨五入的數值
     * @return 四捨五入後的結果
     */
    public static BigDecimal round(BigDecimal v) {
        return round(v, DEF_SCALE);
    }

    /**
     * 四捨五入
     * 
     * @param v
     *            需要四捨五入的數值
     * @param scale
     *            小數點後保留幾位
     * @return 四捨五入後的結果
     */
    public static BigDecimal round(BigDecimal v, int scale) {
        return math(v, scale, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 提供精確的小數位無條件捨去處理。
     * 
     * @param v
     * @param scale
     * @param mode
     * @return
     */
    public static BigDecimal math(BigDecimal v, int scale, int mode) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        return v.divide(BigDecimal.ONE, scale, mode);
    }

    /**
     * 提供精確的小數位無條件捨去處理。
     * 
     * @param v
     *            需要無條件捨去的數字
     * @param scale
     *            小數點後保留幾位
     * @return 無條件捨去後的結果
     */
    public static double floor(double v, int scale) {
        BigDecimal b = new BigDecimal(Double.toString(v));
        return math(b, scale, BigDecimal.ROUND_FLOOR).doubleValue();
    }

    /**
     * 提供精確的小數位無條件捨去處理。
     * 
     * @param v
     *            需要無條件捨去的數字
     * @param scale
     *            小數點後保留幾位
     * @return 無條件捨去後的結果
     */
    public static BigDecimal floor(BigDecimal v, int scale) {
        return math(v, scale, BigDecimal.ROUND_FLOOR);
    }

    /**
     * 無條件進位
     * 
     * @param v
     *            數字
     * @param scale
     *            小數點後保留幾位
     * @return 無條件進位後的結果
     */
    public static BigDecimal ceil(BigDecimal v, int scale) {
        return math(v, scale, BigDecimal.ROUND_UP);
    }

    /**
     * 字串轉數值
     * 
     * @param value
     *            欲轉換的字串
     * @return 數值
     */
    public static double parseDouble(String value) {
        try {
            return Double.parseDouble(value);
        } catch (Exception e) {
            return 0.0;
        }
    }
}
