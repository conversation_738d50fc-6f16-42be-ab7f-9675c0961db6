/* 
 * SourceEnum.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.enums;

/**
 * <pre>
 * 財報資料來源enum。
 * </pre>
 * 
 * @since 2011/8/4
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/8/4,Sunkist Wang,new</li>
 *          </ul>
 */
public enum FssSourceEnum {
    /**
     * 0 無
     */
    NONE("0"),
    /**
     * 1 會計師查核報告書
     */
    INQUIRY("1"),
    /**
     * 2 會計師核閱報告書
     */
    REVIEW("2"),
    /**
     * 3 會計師稅務簽證
     */
    TAX("3"),
    /**
     * 4 自編報表
     */
    CUSTOM("4"),
    /**
     * 5 營利事業所得稅結算申報書
     */
    SETTLEMENT("5"),
    /**
     * 6 借戶出具聲明書之稅務帳冊
     */
    STATEMENT("6"),
    /**
     * 7 借戶電子計算機直接列印之財務報表
     */
    ELECTRONIC("7"),
    /**
     * 8 其他
     */
    OTHER("8");

    private String code;

    FssSourceEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public boolean isEquals(Object other) {
        if (other instanceof String) {
            return code.equals(other);
        } else {
            return super.equals(other);
        }
    }

    public static FssSourceEnum getEnum(String code) {
        for (FssSourceEnum enums : FssSourceEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
