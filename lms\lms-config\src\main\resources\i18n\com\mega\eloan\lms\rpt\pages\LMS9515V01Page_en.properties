#==================================================
#\u6388\u4fe1\u7ba1\u7406\u5831\u8868\u6a94 -\u5916 Grid
#==================================================

L784M01a.ownBrId=Branch
L784M01a.ownBrId2=Branch name

L784M01a.areaUserNo1=Headquarter's Accountable Officer
L784M01a.areaUserNo2=Credit Administration Division's Handling Officer
L784M01a.areaUserNo3=Corporate Banking Division's Handling Officer


L784M01a.sendFirstTime=Time Sent To Branch
L784M01a.approveTime=approve date
L784M01a.rptType1=Report Category
L784M01a.createTime=Data Generation Date
L784M01a.createTime2=Date of date
L784M01a.dataDate=Year/Month Of Data
L784M01a.branchId=Unit
L784M01a.recCount=Data Count
L784M01a.checkTime=Approval Remarks
L784M01a.yearDate=Year Of Data
L784M01a.rptType2=Data Attribute
L784M01a.rptType3=Report Items
L784M01a.page=Page
L784M01a.transportEnd=Sent
L784M01a.testDate=Evaluation Year/Month

L784M01a.totalBrNo=Bank-wide
L784M01a.singleBrNo=Branch
L784M01a.totalBrSingleMon=All Branch Single Month

L784M01a.docType1=Corporate Banking Department
L784M01a.docType2=Personal Banking Department
L784M01a.docType3=Corporate Banking + Personal Banking
#==================================================
#\u6388\u4fe1\u7ba1\u7406\u5831\u8868\u6a94 -thickbox
#==================================================
newInfo1=Please input data year/month
newInfo=Latest Management Report Data
newInfo2=Data Import
newInfo2a=Credit Management <br/> System
newInfo2b=Imput/Retrieve Approval Remarks
newInfo3=Credit Management <br/> System
newInfo3a=Please input the start and end dates for "List Of Unutilized Credit-guaranteed Cases"
newInfo5a=Please input the inquiry starting and ending year/month (YYY/MM) for the  "Credit Case Statistics Report - Headquarter, Foreign Department, Financial Headquarter"

L784M01a.ManageReport=Management Report
L784M01a.dataDate=Year/Month Of Data
L784M01a.projectBranchId=Branch name

L784M01a.search1=Expired Credit Agreement Control Sheet
L784M01a.search2=Approved Credit Case Listing
L784M01a.search3=List Of Unutilized Credit-guaranteed Cases
L784M01a.search4=List of syndication loan lead arrangers
L784M01a.search5=Credit Case statistics
L784M01a.search6=Headquarter's Daily Outside-authority Credit Case List
L784M01a.search7=Board Of Managing Directors Reports & Reported Case Statistics
L784M01a.search8=Credit Case Approval Statistics - By Approval Authority

L784M01a.error3=The system is about to delete the existing [List Of Unutilized Credit-guaranteed Cases]; are you sure to proceed?
L784M01a.error7=By performing this function, the system will import all current month data of "All Branches" from the server, and may take a longer time to process; are you sure to continue?
L784M01a.error8=By performing this function, the system will import all current month's within-authority data of "All Branches" from the server, and may take a longer time to process; are you sure to continue?
L784M01a.error9=System will delete the existing Credit Evaluation Summary of  same base period; are you sure to continue?
L784M01a.error10=Please select at least one report types

L784M01a.startDate=Start Date
L784M01a.endDate=End Date
L784M01a.startYearMonth=Year/Month Of Data

L784M01a.byAccGroup=Contains Data on all branches of subsidiaries
#J-112-0JJJ_05097_B1001 Web e-Loan\u65e5\u672c\u5730\u5340\u5206\u884c\u7c3d\u5831\u66f8\u65b0\u589e\u7ba1\u7406\u884c\u6388\u6b0a\u5167\u6848\u4ef6\u6b0a\u9650\u53ca\u76f8\u95dc\u4fee\u6539
L784M01a.byCountryHead=\u5305\u542b\u7576\u5730\u6240\u6709\u7ba1\u7406\u884c\u6388\u6b0a\u5167\u6848\u4ef6
L784M01a.msg=By performing this function, may take a longer time to process; are you sure to continue?
#==================================================
# \u5df2\u6558\u505a\u6388\u4fe1\u6848\u4ef6\u660e\u7d30
#==================================================
L784S01A.custId=Unified Business Number
L784S01A.cntrNo=Credit Limit Serial Number
L784S01A.custName=Account name please select any Language input
L784S01A.endDate=Approval Date
L784S01A.currentApplyAmt=Credit Limit (amount)
L784S01A.hqCheckDate=Head Office's Approval Date
L784S01A.hqCheckMemo=Remarks
L784S01A.hqCheckDateError=The Report has already completed reference by Credit Administration Division
L784S01A.haveSended=The Report has already been sent
L784S01A.error1=No data selected
L784M01a.error2=Can not be greater than up to the date
L784M01a.less=\u3000etc.
#==================================================
#\u6388\u4fe1\u7ba1\u7406\u5831\u8868\u6a94 -thickbox
#==================================================
L784M01a.1=New
L784M01a.2=Renewal
L784M01a.3=Condition Change
L784M01a.4=Continued
L784M01a.5=Increase
L784M01a.6=Decrease
L784M01a.7=Unchanged
L784M01a.8=Cancel
L784M01a.9=Extension
L784M01a.10=Distress Support
L784M01a.11=Advance Renewal
L784M01a.12=Negotiated Settlement
L784M01a.13=Quotation

#==================================================
#\u6708\u4efd
#==================================================
month.01=Jan
month.02=Feb
month.03=Mar
month.04=Apr
month.05=May
month.06=Jun
month.07=Jul
month.08=Aug
month.09=Sep
month.10=Oct
month.11=Nov
month.12=Dec

month.total=Total

L784S07A.apprYY=Approval Year
L784S07A.apprMM=Approval Month
L784S07A.cItem12Rec=Overdue Loan Extension, Normalization(Records)
L784S07A.cItem12Amt=Overdue Loan Extension, Normalization(Amount)
L784S07A.updater=Personnel Change

branchGroup=Headquarter
hqCheckMemoDefault=Nuclear Remarks remember



#==================================================
#\u5e38\u8463\u6703XLS   prop.getProperty("LMS9515X09.number1")
#==================================================
LMS9515X09.number01=Original
LMS9515X09.number02=Business Unit Credit Evaluation Summary
LMS9515X09.number03=Year
LMS9515X09.number04=Months
LMS9515X09.number05=Average demerit points
LMS9515X09.number06=Business Unit Credit Evaluation Summary (Cumulative Average Month Deduct points)
LMS9515X09.number07=Total Deduct points
LMS9515X09.number08=Business Unit Credit Evaluation Summary (Cumulative Total Month Deduct points)
LMS9515X09.number09=Note: 1. Each Reporting Case Total deduct 89 points.
LMS9515X09.number10=2. Business Center not Approved Summary Information Sheet and Valuation Report, Each case Total deductions 61 points.
LMS9515X09.number11=3. Personal Banking cases not Approved Summary Information Sheet.
LMS9515X09.number12=\uff0a\uff0a   This table partition arranged by group
LMS9515X09.number13=Groups
LMS9515X09.number14=Review Unit
LMS9515X09.number15=Offshore Unit
LMS9515X09.number16=Domestic Unit
LMS9515X09.number17=Branc\nCode
LMS9515X09.number18=No.
LMS9515X09.number19=Cumulative month \n Total deduct points
LMS9515X09.number20=Cumulative month \n Average deduct points
