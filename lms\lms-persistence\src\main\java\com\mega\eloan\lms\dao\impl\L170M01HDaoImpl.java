package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L170M01HDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L170M01H;

@Repository
public class L170M01HDaoImpl extends LMSJpaDao<L170M01H, String> implements
		L170M01HDao {

	@Override
	public List<L170M01H> findByMainIdCustIdDupNo_fmtSYS(String[] exclude_debIdArr, String mainId, String custId, String dupNo) {
		return _findByMainIdCustIdDupNo_fmtSYS_debType(exclude_debIdArr, mainId, custId, dupNo, null);
	}
	
	@Override
	public List<L170M01H> findByMainIdCustIdDupNo_fmtSYS_debTypeC(String[] exclude_debIdArr, String mainId, String custId, String dupNo) {
		String[] debTypeArr = {"C"};
		return _findByMainIdCustIdDupNo_fmtSYS_debType(exclude_debIdArr, mainId, custId, dupNo, debTypeArr);
	}
	
	@Override
	public List<L170M01H> findByMainIdCustIdDupNo_fmtSYS_debTypeGN(String[] exclude_debIdArr, String mainId, String custId, String dupNo){
		String[] debTypeArr = {"G", "N"};
		return _findByMainIdCustIdDupNo_fmtSYS_debType(exclude_debIdArr, mainId, custId, dupNo, debTypeArr);
	}
	
	private List<L170M01H> _findByMainIdCustIdDupNo_fmtSYS_debType(String[] exclude_debIdArr
			, String mainId, String custId, String dupNo
			, String[] debTypeArr) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if(debTypeArr!=null &&  debTypeArr.length>0){
			search.addSearchModeParameters(SearchMode.IN, "debType", debTypeArr);	
		}
		if(exclude_debIdArr!=null &&  exclude_debIdArr.length>0){
			for(String ex_debId : exclude_debIdArr){
				search.addSearchModeParameters(SearchMode.NOT_EQUALS, "debId", ex_debId);	
			}
		}		
		
		search.addOrderBy("debType", false);
		search.setMaxResults(Integer.MAX_VALUE);
		
		List<L170M01H> list = createQuery(L170M01H.class, search).getResultList();		
		return list;
	}

	@Override
	public L170M01H findByMainIdCustIdDupNo_fmtFree_debTypeG(String _debId, String mainId, String custId, String dupNo){
		String[] debTypeArr = {"G", "N"};
		return _findByMainIdCustIdDupNo_fmtFree_debType(_debId, mainId, custId, dupNo, debTypeArr);
	}

	@Override
	public L170M01H findByMainIdCustIdDupNo_fmtFree_debTypeC(String _debId, String mainId, String custId, String dupNo){
		String[] debTypeArr = {"C"};
		return _findByMainIdCustIdDupNo_fmtFree_debType(_debId, mainId, custId, dupNo, debTypeArr);
	}
	
	private L170M01H _findByMainIdCustIdDupNo_fmtFree_debType(String in_debId
			, String mainId, String custId, String dupNo
			, String[] debTypeArr) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if(debTypeArr!=null &&  debTypeArr.length>0){
			search.addSearchModeParameters(SearchMode.IN, "debType", debTypeArr);	
		}
		search.addSearchModeParameters(SearchMode.EQUALS, "debId", in_debId);		
		
		search.addOrderBy("debType", false);
		search.setMaxResults(1);
		
		List<L170M01H> list = createQuery(L170M01H.class, search).getResultList();
		if(list==null || list.size()==0){
			return null;
		}
		return list.get(0);
	}
}
