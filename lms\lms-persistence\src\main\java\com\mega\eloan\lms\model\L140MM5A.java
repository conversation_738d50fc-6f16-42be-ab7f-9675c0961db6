/* 
 * L140MM5A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** eLoan各系統客戶ID欄位檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140MM5A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140MM5A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 資料年月 **/
	@Size(max=7)
	@Column(name="DATAYM", length=7, columnDefinition="VARCHAR(7)")
	private String dataYM;

	/** 資料筆數 **/
	@Column(length = 5)
	private Integer cnt;


	/** 取得資料年月 **/
	public String getDataYM() {
		return this.dataYM;
	}
	/** 設定資料年月 **/
	public void setDataYM(String value) {
		this.dataYM = value;
	}

	/** 取得資料筆數 **/
	public Integer getCnt() {
		return this.cnt;
	}
	/** 設定資料筆數 **/
	public void setCnt(Integer value) {
		this.cnt = value;
	}
}
