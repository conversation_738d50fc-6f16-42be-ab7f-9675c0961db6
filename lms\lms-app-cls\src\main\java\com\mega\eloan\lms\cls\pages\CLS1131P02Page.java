package com.mega.eloan.lms.cls.pages;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01U;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01U;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 聯徵 ST查詢結果列印(自C120S01E/C101S01E) 
 * </pre>
 * 
 * @since 2019/12/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/12/4,EL08034
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1131p02")
public class CLS1131P02Page extends AbstractOutputPage {

	@Autowired
	CLS1131Service cms1131Service;
	
	@Autowired
	CLSService clsService;
	
	@Autowired
	EjcicService ejcicService;
	
	/* TODO 在取得 10組傳回的HTML時，裡面有包含Button、浮水印、js檔路徑、css檔路徑
		在存入LMS時，不去【加工/截字串】，避免之後因發生不可預期的狀況，導致資料遺失
		可能情況1)若10組的程式改版，改變它們加工的 html code
		可能情況2)若出現某種錯誤訊息，例如：(A)逾時未回應 (B)無權限。 可能交易 Fail時，傳回來的 html 內容，會和正常回應的內容不同
		
		結論：
		(1)在儲存時，應放 10組 回傳的 Raw Data
		(2)在 LMS 呈現時，再去把10組添加的 Button、浮水印拿掉。當遇到異常時，依遇到的情形，用不同 version 的截字串logic 去處理
		
		
		例如：
		<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" /> <html> <head> <title>兆豐國際商銀 金融聯合徵信中心專線查詢系統</title> <script src="../JCIC.js"></script> <LINK href="../print.css" rel="stylesheet" media="print" type="text/css" /> </head> 0001-No Permission==229001
	*/

	// UPGRADETODO: 不確定Header是否還需要
	// @Override
	// protected void setHeaders(WebResponse response){
	// if(true){
	// Map<String, String> map =
	// clsService.get_codeTypeWithOrder("LMS_FUNC_ON_FLAG");
	// String key = "cls1131p01_X-UA-Compatible";
	// if(map.containsKey(key)){
	// String configStr = Util.trim(map.get(key));
	// if(Util.isNotEmpty(configStr) && configStr.indexOf("IE")>=0){
	// response.setHeader("X-UA-Compatible", configStr);
	// }
	// }
	// }
	// }
	
	/*
	 	$.form.submit({
            url: webroot + '/app/cls/cls1131p02',
            target: 'cls1131p02',
            data: $.extend({isC120M01A: CLS1131S01.isC120M01A}, CLS1131S01.data)
        }); 
	*/
	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String txId = Util.trim(params.getString("txId"));
		StringBuilder sb = new StringBuilder();
		
		model.addAttribute("cls1131p01_allow_print",
				clsService.is_function_on_codetype("cls1131p01_allow_print"));

		boolean isC120M01A = params.getBoolean("isC120M01A");
		boolean isC160M01A = params.getBoolean("isC160M01A");
		
		String[] txid_arr = new String[]{CrsUtil.EJ_TXID_B36, CrsUtil.EJ_TXID_D10, CrsUtil.EJ_TXID_R20, CrsUtil.EJ_TXID_S11};
		String[] txid_arr_C160M01A = new String[]{CrsUtil.EJ_TXID_B29, CrsUtil.EJ_TXID_B33, CrsUtil.EJ_TXID_B68};
		
		if(isC120M01A){
			C120S01E c120S01e = clsService.findC120S01E(mainId, custId, dupNo);
			if (c120S01e != null) {
				if (txId.indexOf(CrsUtil.EJ_TXID_Z13) >= 0
						&& Util.isNotEmpty(Util.trim(c120S01e.getZ13_html()))) {
					String extract_html = extract_data(c120S01e.getZ13_html());
					sb.append(extract_html);	
				}
				if (txId.indexOf(CrsUtil.EJ_TXID_Z21) >= 0
						&& Util.isNotEmpty(Util.trim(c120S01e.getZ21_html()))) {
					String extract_html = extract_data(c120S01e.getZ21_html());
					sb.append(extract_html);	
				}
				
				for(String item_in_txid : txid_arr){
					if(txId.indexOf(item_in_txid)>=0){
						for (C120S01U s01u : clsService.findC120S01U_txid(
								c120S01e.getMainId(), c120S01e.getCustId(),
								c120S01e.getDupNo(), item_in_txid)) {
							String extract_html = extract_data(s01u.getHtmlData());
							sb.append(extract_html);		
						}					
					}	
				}
			}
		} else if(isC160M01A){
			
			List<C101S01U> c101s01uList = clsService.findC101S01U(mainId, custId, dupNo);
			for(C101S01U c101s01u : c101s01uList){

				if(Arrays.asList(txid_arr_C160M01A).contains(Util.trim(c101s01u.getTxid()))){
					String extract_html = extract_data(c101s01u.getHtmlData());
					sb.append(extract_html);
				}
			}
			
		}
		else{
			C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
			C101S01E c101S01e = clsService.findC101S01E(c101m01a);
			if (c101S01e != null) {
				if (txId.indexOf(CrsUtil.EJ_TXID_Z13) >= 0
						&& Util.isNotEmpty(Util.trim(c101S01e.getZ13_html()))) {
					String extract_html = extract_data(c101S01e.getZ13_html());
					sb.append(extract_html);	
				}
				if (txId.indexOf(CrsUtil.EJ_TXID_Z21) >= 0
						&& Util.isNotEmpty(Util.trim(c101S01e.getZ21_html()))) {
					String extract_html = extract_data(c101S01e.getZ21_html());
					sb.append(extract_html);	
				}
				
				for(String item_in_txid : txid_arr){
					if(txId.indexOf(item_in_txid)>=0){
						for (C101S01U s01u : clsService.findC101S01U_txid(
								c101S01e.getMainId(), c101S01e.getCustId(),
								c101S01e.getDupNo(), item_in_txid)) {
							String extract_html = extract_data(s01u.getHtmlData());
							sb.append(extract_html);		
						}					
					}	
				}
			}
		}
		
		if (sb.length() > 0) {
			setNeedHtml(true);
			// 執行浮水印
			// O-112-0233 調整浮水印，UnitNo若在EJF369有對應的VDEPTID就改用VDEPTID
			String wm_msg = UtilConstants.兆豐銀行代碼 + ejcicService.findEJF369VDEPTID(user.getUnitNo()) 
					+" "+user.getUserId()+" "+user.getLoginIP();
			sb.append("<script>window.onload = function(){");
			sb.append("watermark('").append(wm_msg).append("');");
			sb.append("};</script>");
		}

		return sb.toString();
	}
	
	/** 目前去 parse Z13及Z21 的 HtmlResponse
	 */
	private String extract_data(String raw){
		String lower_str = raw.toLowerCase();
		int idx_charset = lower_str.indexOf("charset");
		if(idx_charset>1){
			//在正常回覆的 html，先以 charset 去拆分字串
			int beg_idx = lower_str.substring(0, idx_charset).lastIndexOf("<html>");
			String end_tag = "</html>";
			int end_idx = lower_str.indexOf(end_tag, idx_charset);
			if(beg_idx>1 && end_idx>1 && beg_idx<end_idx){
				return StringUtils.substring(raw, beg_idx, end_idx+"</html>".length());
			}
		}
		return raw;
	}

	// UPGRADE: 待確認是否需要ViewName
	@Override
	protected String getViewName() {
		return null;
	}

}
