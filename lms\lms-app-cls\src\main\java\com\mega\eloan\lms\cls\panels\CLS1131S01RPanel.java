package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.ClsScoreUtil;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金非房貸-卡友評等
 * </pre>
 * 
 * @since 2019/05/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/05/13,EL08034,new
 *          </ul>
 */
public class CLS1131S01RPanel extends Panel {

	private static final long serialVersionUID = 1L;

	private String varVer;

	private boolean showSDT;

	/**
	 * @param id
	 */
	public CLS1131S01RPanel(String id) {
		super(id);
	}

	/**
	 * @param id
	 * @param varVer
	 * @param showSDT
	 */
	public CLS1131S01RPanel(String id, String varVer, boolean showSDT) {
		super(id);
		this.varVer = varVer;
		this.showSDT = showSDT;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		//TITLE
		boolean t_defaule = false;
		boolean t_v4_0 = false;
		if(Util.equals(varVer, ClsScoreUtil.V4_0_NOT_HOUSE_LOAN)){
			t_v4_0 = true;
		}else{
			t_defaule = true;
		}
		model.addAttribute("FACTOR_TITLE_DEFAULT", t_defaule);
		model.addAttribute("FACTOR_TITLE_V4_0", t_v4_0);
		model.addAttribute("FACTOR_TOTAL_DEFAULT", t_defaule);
		model.addAttribute("FACTOR_TOTAL_V4_0", t_v4_0);
		
		//內容
		boolean v2_1 = false;
		boolean v3_0 = false;
		boolean v4_0 = false;
		boolean v_unknown = false;
		if(Util.equals(varVer, ClsScoreUtil.V2_1_CARD_LOAN)){
			v2_1 = true;
		}else if(Util.equals(varVer, ClsScoreUtil.V3_0_CARD_LOAN) || Util.equals(varVer, ClsScoreUtil.V3_1_CARD_LOAN)){
			//3.0跟3.1用同一版本
			v3_0 = true;
		}else if(Util.equals(varVer, ClsScoreUtil.V4_0_CARD_LOAN)){
			v4_0 = true;
		}else{
			v_unknown = true;
		}
		model.addAttribute("FACTOR_V2_1", v2_1);
		model.addAttribute("FACTOR_V3_0", v3_0);
		model.addAttribute("FACTOR_V4_0", v4_0);
		model.addAttribute("FACTOR_V_UNKNOWN", v_unknown);
		model.addAttribute("DOUBLETRACK", showSDT);
		model.addAttribute("DOUBLETRACK2", showSDT);
	}
}
