/* 
 * L120S07A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 土建融案檢視清單主檔 **/
@Entity
//@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S07A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class L120S07A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 推案公司 **/
	@Column(name="COMNAME", length=360, columnDefinition="VARCHAR(360)")
	private String comName;

	/** 案名 **/
	@Column(name="CASENAME", length=360, columnDefinition="VARCHAR(360)")
	private String caseName;

	/** 案址 **/
	@Column(name="CASEADDR", length=360, columnDefinition="VARCHAR(360)")
	private String caseAddr;

	/** 使用分區 **/
	@Column(name="USEPLACE", length=128, columnDefinition="VARCHAR(128)")
	private String usePlace;

	/** 建蔽率 **/
	@Column(name="BUILDRATIO", columnDefinition="DECIMAL(5,2)")
	private BigDecimal buildRatio;

	/** 容積率 **/
	@Column(name="INSIDERATIO", columnDefinition="DECIMAL(5,2)")
	private BigDecimal InsideRatio;

	/** 
	 * 基地面積<p/>
	 * 單位：仟元
	 */
	@Column(name="BASEWIDE", columnDefinition="DECIMAL(15,2)")
	private BigDecimal baseWide;

	/** 
	 * 總營建面積<p/>
	 * 單位：仟元
	 */
	@Column(name="TOTALWIDE", columnDefinition="DECIMAL(15,2)")
	private BigDecimal totalWide;

	/** 樓層地下(層) **/
	@Column(name="UNDERF", length=3, columnDefinition="VARCHAR(3)")
	private String underF;

	/** 樓層地上(層) **/
	@Column(name="GROUNDF", length=3, columnDefinition="VARCHAR(3)")
	private String groundF;

	/** 
	 * 土地買價(每坪)<p/>
	 * 單位：仟元
	 */
	@Column(name="LANDBUYAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal landBuyAmt;

	/** 
	 * 地鑑(初)估價(每坪)未含容積移轉<p/>
	 * 單位：仟元
	 */
	@Column(name="LANDAMT1", columnDefinition="DECIMAL(15,0)")
	private BigDecimal landAmt1;

	/** 
	 * 地鑑(初)估價(每坪)已含容積移轉<p/>
	 * 單位：仟元
	 */
	@Column(name="LANDAMT2", columnDefinition="DECIMAL(15,0)")
	private BigDecimal landAmt2;

	/** 
	 * 容積移轉總金額<p/>
	 * 單位：仟元
	 */
	@Column(name="ISDMOVTOTAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal isdMovTotAmt;

	/** 
	 * 附近行情價(每坪)<p/>
	 * 單位：仟元
	 */
	@Column(name="NEARAMTLAND", columnDefinition="DECIMAL(15,0)")
	private BigDecimal nearAmtLand;

	/** 
	 * 是否為辦理信託者<p/>
	 * Y/N
	 */
	@Column(name="ISTRUSTLAND", length=1, columnDefinition="CHAR(1)")
	private String isTrustLand;

	/** 
	 * 土地貸款總金額/融資成數<p/>
	 * 單位：仟元
	 */
	@Column(name="LANDBORTOTAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal landBorTotAmt;

	/** 土地貸款融資成數 **/
	@Column(name="LANDBORPERCENT", columnDefinition="DECIMAL(5,2)")
	private BigDecimal landBorPercent;

	/** 
	 * 建築成本(每坪)<p/>
	 * 單位：仟元
	 */
	@Column(name="BUILDCOST", columnDefinition="DECIMAL(15,0)")
	private BigDecimal buildCost;

	/** 
	 * 是否為辦理信託者<p/>
	 * Y/N
	 */
	@Column(name="ISTRUSTBUILD", length=1, columnDefinition="CHAR(1)")
	private String isTrustBuild;

	/** 
	 * 建築融資總金額/融資成數<p/>
	 * 單位：仟元
	 */
	@Column(name="BUILDTOTAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal buildTotAmt;

	/** 建築融資成數 **/
	@Column(name="BUILDBORPERCENT", columnDefinition="DECIMAL(5,2)")
	private BigDecimal buildBorPercent;

	/** 
	 * 本案擬銷售價格(每坪)<p/>
	 * 單位：仟元
	 */
	@Column(name="CASEWILLSALEAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal caseWillSaleAmt;

	/** 
	 * 附近行情價(每坪)<p/>
	 * 單位：仟元
	 */
	@Column(name="NEARAMTALL", columnDefinition="DECIMAL(15,0)")
	private BigDecimal nearAmtAll;

	/** 損益兩平比率 **/
	@Column(name="PROFITLOSTRATIO", columnDefinition="DECIMAL(5,2)")
	private BigDecimal ProfitLostRatio;

	/** 土建融每坪借款佔本案售價比率 **/
	@Column(name="BORROWRATIO", columnDefinition="DECIMAL(5,2)")
	private BigDecimal borrowRatio;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得推案公司 **/
	public String getComName() {
		return this.comName;
	}
	/** 設定推案公司 **/
	public void setComName(String value) {
		this.comName = value;
	}

	/** 取得案名 **/
	public String getCaseName() {
		return this.caseName;
	}
	/** 設定案名 **/
	public void setCaseName(String value) {
		this.caseName = value;
	}

	/** 取得案址 **/
	public String getCaseAddr() {
		return this.caseAddr;
	}
	/** 設定案址 **/
	public void setCaseAddr(String value) {
		this.caseAddr = value;
	}

	/** 取得使用分區 **/
	public String getUsePlace() {
		return this.usePlace;
	}
	/** 設定使用分區 **/
	public void setUsePlace(String value) {
		this.usePlace = value;
	}

	/** 取得建蔽率 **/
	public BigDecimal getBuildRatio() {
		return this.buildRatio;
	}
	/** 設定建蔽率 **/
	public void setBuildRatio(BigDecimal value) {
		this.buildRatio = value;
	}

	/** 取得容積率 **/
	public BigDecimal getInsideRatio() {
		return this.InsideRatio;
	}
	/** 設定容積率 **/
	public void setInsideRatio(BigDecimal value) {
		this.InsideRatio = value;
	}

	/** 
	 * 取得基地面積<p/>
	 * 單位：仟元
	 */
	public BigDecimal getBaseWide() {
		return this.baseWide;
	}
	/**
	 *  設定基地面積<p/>
	 *  單位：仟元
	 **/
	public void setBaseWide(BigDecimal value) {
		this.baseWide = value;
	}

	/** 
	 * 取得總營建面積<p/>
	 * 單位：仟元
	 */
	public BigDecimal getTotalWide() {
		return this.totalWide;
	}
	/**
	 *  設定總營建面積<p/>
	 *  單位：仟元
	 **/
	public void setTotalWide(BigDecimal value) {
		this.totalWide = value;
	}

	/** 取得樓層地下(層) **/
	public String getUnderF() {
		return this.underF;
	}
	/** 設定樓層地下(層) **/
	public void setUnderF(String value) {
		this.underF = value;
	}

	/** 取得樓層地上(層) **/
	public String getGroundF() {
		return this.groundF;
	}
	/** 設定樓層地上(層) **/
	public void setGroundF(String value) {
		this.groundF = value;
	}

	/** 
	 * 取得土地買價(每坪)<p/>
	 * 單位：仟元
	 */
	public BigDecimal getLandBuyAmt() {
		return this.landBuyAmt;
	}
	/**
	 *  設定土地買價(每坪)<p/>
	 *  單位：仟元
	 **/
	public void setLandBuyAmt(BigDecimal value) {
		this.landBuyAmt = value;
	}

	/** 
	 * 取得地鑑(初)估價(每坪)未含容積移轉<p/>
	 * 單位：仟元
	 */
	public BigDecimal getLandAmt1() {
		return this.landAmt1;
	}
	/**
	 *  設定地鑑(初)估價(每坪)未含容積移轉<p/>
	 *  單位：仟元
	 **/
	public void setLandAmt1(BigDecimal value) {
		this.landAmt1 = value;
	}

	/** 
	 * 取得地鑑(初)估價(每坪)已含容積移轉<p/>
	 * 單位：仟元
	 */
	public BigDecimal getLandAmt2() {
		return this.landAmt2;
	}
	/**
	 *  設定地鑑(初)估價(每坪)已含容積移轉<p/>
	 *  單位：仟元
	 **/
	public void setLandAmt2(BigDecimal value) {
		this.landAmt2 = value;
	}

	/** 
	 * 取得容積移轉總金額<p/>
	 * 單位：仟元
	 */
	public BigDecimal getIsdMovTotAmt() {
		return this.isdMovTotAmt;
	}
	/**
	 *  設定容積移轉總金額<p/>
	 *  單位：仟元
	 **/
	public void setIsdMovTotAmt(BigDecimal value) {
		this.isdMovTotAmt = value;
	}

	/** 
	 * 取得附近行情價(每坪)<p/>
	 * 單位：仟元
	 */
	public BigDecimal getNearAmtLand() {
		return this.nearAmtLand;
	}
	/**
	 *  設定附近行情價(每坪)<p/>
	 *  單位：仟元
	 **/
	public void setNearAmtLand(BigDecimal value) {
		this.nearAmtLand = value;
	}

	/** 
	 * 取得是否為辦理信託者<p/>
	 * Y/N
	 */
	public String getIsTrustLand() {
		return this.isTrustLand;
	}
	/**
	 *  設定是否為辦理信託者<p/>
	 *  Y/N
	 **/
	public void setIsTrustLand(String value) {
		this.isTrustLand = value;
	}

	/** 
	 * 取得土地貸款總金額/融資成數<p/>
	 * 單位：仟元
	 */
	public BigDecimal getLandBorTotAmt() {
		return this.landBorTotAmt;
	}
	/**
	 *  設定土地貸款總金額/融資成數<p/>
	 *  單位：仟元
	 **/
	public void setLandBorTotAmt(BigDecimal value) {
		this.landBorTotAmt = value;
	}

	/** 取得土地貸款融資成數 **/
	public BigDecimal getLandBorPercent() {
		return this.landBorPercent;
	}
	/** 設定土地貸款融資成數 **/
	public void setLandBorPercent(BigDecimal value) {
		this.landBorPercent = value;
	}

	/** 
	 * 取得建築成本(每坪)<p/>
	 * 單位：仟元
	 */
	public BigDecimal getBuildCost() {
		return this.buildCost;
	}
	/**
	 *  設定建築成本(每坪)<p/>
	 *  單位：仟元
	 **/
	public void setBuildCost(BigDecimal value) {
		this.buildCost = value;
	}

	/** 
	 * 取得是否為辦理信託者<p/>
	 * Y/N
	 */
	public String getIsTrustBuild() {
		return this.isTrustBuild;
	}
	/**
	 *  設定是否為辦理信託者<p/>
	 *  Y/N
	 **/
	public void setIsTrustBuild(String value) {
		this.isTrustBuild = value;
	}

	/** 
	 * 取得建築融資總金額/融資成數<p/>
	 * 單位：仟元
	 */
	public BigDecimal getBuildTotAmt() {
		return this.buildTotAmt;
	}
	/**
	 *  設定建築融資總金額/融資成數<p/>
	 *  單位：仟元
	 **/
	public void setBuildTotAmt(BigDecimal value) {
		this.buildTotAmt = value;
	}

	/** 取得建築融資成數 **/
	public BigDecimal getBuildBorPercent() {
		return this.buildBorPercent;
	}
	/** 設定建築融資成數 **/
	public void setBuildBorPercent(BigDecimal value) {
		this.buildBorPercent = value;
	}

	/** 
	 * 取得本案擬銷售價格(每坪)<p/>
	 * 單位：仟元
	 */
	public BigDecimal getCaseWillSaleAmt() {
		return this.caseWillSaleAmt;
	}
	/**
	 *  設定本案擬銷售價格(每坪)<p/>
	 *  單位：仟元
	 **/
	public void setCaseWillSaleAmt(BigDecimal value) {
		this.caseWillSaleAmt = value;
	}

	/** 
	 * 取得附近行情價(每坪)<p/>
	 * 單位：仟元
	 */
	public BigDecimal getNearAmtAll() {
		return this.nearAmtAll;
	}
	/**
	 *  設定附近行情價(每坪)<p/>
	 *  單位：仟元
	 **/
	public void setNearAmtAll(BigDecimal value) {
		this.nearAmtAll = value;
	}

	/** 取得損益兩平比率 **/
	public BigDecimal getProfitLostRatio() {
		return this.ProfitLostRatio;
	}
	/** 設定損益兩平比率 **/
	public void setProfitLostRatio(BigDecimal value) {
		this.ProfitLostRatio = value;
	}

	/** 取得土建融每坪借款佔本案售價比率 **/
	public BigDecimal getBorrowRatio() {
		return this.borrowRatio;
	}
	/** 設定土建融每坪借款佔本案售價比率 **/
	public void setBorrowRatio(BigDecimal value) {
		this.borrowRatio = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
