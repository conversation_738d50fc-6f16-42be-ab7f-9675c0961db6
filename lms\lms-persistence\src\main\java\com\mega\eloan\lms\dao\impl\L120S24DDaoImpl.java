/* 
 * L120S24DDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S24DDao;
import com.mega.eloan.lms.model.L120S24D;

/** LTV風險權數計算對照表 **/
@Repository
public class L120S24DDaoImpl extends LMSJpaDao<L120S24D, String>
	implements L120S24DDao {

	@Override
	public L120S24D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L120S24D> findByCBControlAndLTVClassAndLTVType(String isCBControl, String LTVClass, String LTVType){
		ISearch search = createSearchTemplete();
		search.setMaxResults(Integer.MAX_VALUE);
		if(Util.isNotEmpty(isCBControl)){
			search.addSearchModeParameters(SearchMode.EQUALS, "isCBControl", isCBControl);
		}
		if(Util.isNotEmpty(LTVClass)){
			search.addSearchModeParameters(SearchMode.EQUALS, "LTVClass", LTVClass);
		}
		if(Util.isNotEmpty(LTVType)){
			search.addSearchModeParameters(SearchMode.EQUALS, "LTVType", LTVType);
		}
		List<L120S24D> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L120S24D findIsCBControlYByAndLTVClass(String LTVClass) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "isCBControl", "Y");
		search.addSearchModeParameters(SearchMode.EQUALS, "LTVClass", LTVClass);
		return findUniqueOrNone(search);
	}
}