package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/**
 * <pre>
 * 財務比率 - The persistent class for the F101S01B database table.
 * </pre>
 * 
 * @since 2011/7/26
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/26,Sunkist Wang,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "F101S01B-entity-graph", attributeNodes = { @NamedAttributeNode("f101m01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "F101S01B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class F101S01B extends com.mega.eloan.common.model.RelativeMeta
		implements Serializable, tw.com.iisi.cap.model.IDataObject, IDocObject{
	private static final long serialVersionUID = 1L;

	@Column(precision = 11, scale = 2)
	private BigDecimal ratio;

	@Column(length = 2)
	private String ratioNo;

	// bi-directional many-to-one association to F101M01A
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false)
		})
	private F101M01A f101m01a;

	public F101S01B() {
	}

	public BigDecimal getRatio() {
		return this.ratio;
	}

	public void setRatio(BigDecimal ratio) {
		this.ratio = ratio;
	}

	public String getRatioNo() {
		return this.ratioNo;
	}

	public void setRatioNo(String ratioNo) {
		this.ratioNo = ratioNo;
	}

	public F101M01A getF101m01a() {
		return f101m01a;
	}

	public void setF101m01a(F101M01A f101m01a) {
		this.f101m01a = f101m01a;
	}

}