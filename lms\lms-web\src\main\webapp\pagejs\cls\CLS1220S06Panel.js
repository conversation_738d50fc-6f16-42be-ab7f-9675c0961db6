//$(document).ready(function(json){
var initDfd = window.initDfd || $.Deferred();
initDfd.done(function(json){

	$("#reEstView").hide();
//	$("#bntEva").hide();
	$("#estUnitName").prop("disabled",true);
	var docStatus = responseJSON.mainDocStatus;
	$.ajax({
        handler: 'cls1220m10formhandler',
        action: 'check_only_expermission',
    }).done(function(responseData){
		if(responseData.only_ex_permission){//僅有電銷權限, 無其他EL相關權限 true=是, false=否
		            	
    	}else{
    		//信貸案件-有無擔保品欄位鎖定
    		var applyKind = responseJSON.mainApplyKind;
    		if(applyKind == "P" || applyKind == "Q"){
    			$("#estFlag").prop("disabled",true);
    		}
    		//案件狀態=審核中，不開放修改[有無擔保品欄位]
    		//擔保品欄位原則上由主管於派案時填寫，(新增其他案件)
    		if(responseData.masterRoles && (applyKind == "E" || applyKind == "F"
    								|| applyKind == "O"|| applyKind == "R")){
			//$("#bntEva").show();
    			$("#evaMegaEmp").prop("disabled",false);
    			$("#estUnitName").prop("disabled",false);
    			$("#estUnitName").prop("readonly",false);
    			$("#estFlag").prop("disabled",false);
    		}
    		
    	}
	});
	
	if(docStatus == "03O"){
		$("input[type=text]").readOnly();
		$("input[type=radio]").readOnly();
		$("select").readOnly();
		$("input[type=checkbox]").readOnly();
	}
	

	

var _M = {
	tabMainId: ""	
}

var IntroductionSource = {
	show: function(introduceSrc){
		switch (introduceSrc) {
		    case '1':
		        $("#employeeDiv").show();
				$("#realEstateAgentDiv").find("input:text").val("");
				$("#megaSubCompanyDiv").find("select").val("");
				$("#megaSubCompanyDiv").find("input:text").val("");
				$("#customerOrCompanyDiv").find("input:text").val("");
				$("#batchCodeSbrDiv").find("input:text").val("");
				$("#buildCaseDiv").find("input:text").val("");
				cleanLaa();
		        break;
			case '2':
		        $("#realEstateAgentDiv").show();
				$("#employeeDiv").find("input:text").val("");
				$("#megaSubCompanyDiv").find("select").val("");
				$("#megaSubCompanyDiv").find("input:text").val("");
				$("#customerOrCompanyDiv").find("input:text").val("");
				$("#batchCodeSbrDiv").find("input:text").val("");
				$("#buildCaseDiv").find("input:text").val("");
				cleanLaa();
		        break;
		    case '3':
		        $("#megaSubCompanyDiv").show();
				$("#employeeDiv").find("input:text").val("");
				$("#realEstateAgentDiv").find("input:text").val("");
				$("#customerOrCompanyDiv").find("input:text").val("");
				$("#batchCodeSbrDiv").find("input:text").val("");
				$("#buildCaseDiv").find("input:text").val("");
				cleanLaa();
		        break;
		    case '4':
		    	$("#laaDiv").show();
		    	$("#employeeDiv").find("input:text").val("");
		    	$("#realEstateAgentDiv").find("input:text").val("");
				$("#megaSubCompanyDiv").find("select").val("");
				$("#megaSubCompanyDiv").find("input:text").val("");
				$("#customerOrCompanyDiv").find("input:text").val("");
				$("#batchCodeSbrDiv").find("input:text").val("");
				$("#buildCaseDiv").find("input:text").val("");
		    	break
		    case '5':
				$("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				$("#employeeDiv").find("input:text").val("");
				$("#realEstateAgentDiv").find("input:text").val("");
				$("#megaSubCompanyDiv").find("select").val("");
				$("#megaSubCompanyDiv").find("input:text").val("");
				$("#batchCodeSbrDiv").find("input:text").val("");
				$("#buildCaseDiv").find("input:text").val("");
				cleanLaa();
		        break;
		    case '6':
		        $("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				$("#employeeDiv").find("input:text").val("");
				$("#realEstateAgentDiv").find("input:text").val("");
				$("#megaSubCompanyDiv").find("select").val("");
				$("#megaSubCompanyDiv").find("input:text").val("");
				$("#batchCodeSbrDiv").find("input:text").val("");
				$("#buildCaseDiv").find("input:text").val("");
				cleanLaa();
				break;
		    case '7':
		    	$("#batchCodeSbrDiv").show();
				$("#employeeDiv").find("input:text").val("");
				$("#realEstateAgentDiv").find("input:text").val("");
				$("#megaSubCompanyDiv").find("select").val("");
				$("#megaSubCompanyDiv").find("input:text").val("");
				$("#customerOrCompanyDiv").find("input:text").val("");
				$("#buildCaseDiv").find("input:text").val("");
				cleanLaa();
		    	break;
			case '8':
		        $("#buildCaseDiv").show();
				$("#employeeDiv").find("input:text").val("");
				$("#realEstateAgentDiv").find("input:text").val("");
				$("#megaSubCompanyDiv").find("select").val("");
				$("#megaSubCompanyDiv").find("input:text").val("");
				$("#customerOrCompanyDiv").find("input:text").val("");
				$("#batchCodeSbrDiv").find("input:text").val("");("");
				cleanLaa();
		        break;
		}
	},	
	change: function(){
		$("#employeeDiv").hide();
		$("#realEstateAgentDiv").hide();
		$("#megaSubCompanyDiv").hide();
		$("#laaDiv").hide();
		$("#customerOrCompanyDiv").hide();
		$("#importCustOrComSpan").hide();
		$("#batchCodeSbrDiv").hide();
		$("#buildCaseDiv").hide();
		
		IntroductionSource.show($("#introduceSrc").val());
	},
	loadGridInfo: function(){
		$("#realEstateGrid").iGrid({
			height: 100,
			handler: "cls1222gridhandler",
			sortname: 'estateType|estateSubType',
			sortorder: 'asc|asc',
			action: "importRealEstateAgentInfo",
			postData: {
				tabFormMainId: _M.tabMainId,
				flag: "N"
			},
			loadComplete : function() {    				
				$('#realEstateGrid tr').click(function(e) {
					//click 帶入資料至畫面後 go to top
					e.preventDefault();
				});
			},
			colModel: [{
				colHeader: i18n.cls1220s06["C122M01A.agntNo.02"],//房仲代號
				width: 20,
				name: 'AGNTNO',
				sortable: true,
				formatter: 'click',
				onclick: function(cellValue, options, rowObject){
					$("#agntNo").val(rowObject.AGNTNO);//引介房仲代號
					$("#agntChain").val(rowObject.AGNTCHAIN);//引介房仲連鎖店類型
					$("#dealContractNo").val(rowObject.CONTRACTNO);//買賣合約書編號
					$.thickbox.close();
				}
			}, {
				colHeader: i18n.cls1220s06["C122M01A.agntName"],//房仲名稱
				width: 40,
				name: 'AGENTNAME',
				sortable: true
			}, {
				colHeader: i18n.cls1220s06["C122M01A.agntChain"],//連鎖店類型
				name: 'AGNTCHAIN',
				align: 'center',
				width: 30
			}, {
				colHeader: i18n.cls1220s06["C122M01A.agntChainName"],//連鎖店類型名稱
				name: 'CHAINNAME',
				align: 'center',
				width: 35
			}, {
				colHeader: i18n.cls1220s06["C122M01A.dealContractNo"],//買賣合約書編號
				name: 'CONTRACTNO',
				width: 35
			}]
		});
	},
	openBox: function(){
		var buttons = {};
		buttons[i18n.def.close] = function(){				
			$.thickbox.close();
        };
       	$("#openBox_realEstateIntroduction").thickbox({
       		title: i18n.cls1220s06['page01.title.introductionRealEstateAgentInfo'],
            width: 550,
            height: 250,
            modal: true,
			align: "center",
			valign: 'bottom',
            i18n: i18n.def,
            buttons: buttons
        });
	},
	importCustomerOrCompanyInfo: function(){
		AddCustAction.open({
    		handler: 'cls1220m10formhandler',
			action : 'importCustomerOrCompanyInfo',
			data : {
            },
			callback : function(json){					
            	// 關掉 AddCustAction 的 
            	$.thickbox.close();					
				$("#introCustId").val(json.introCustId);
				$("#introDupNo").val(json.introDupNo);
				$("#introCustName").val(json.introCustName);
			}
		});
	},
	processBatchHouseholdLoan: function(isBatchLoan){
		$("#introduceSrc").prop("disabled", false);
		if(isBatchLoan == "Y"){
			$("#introduceSrc").val("7").trigger('change');
			$("#introduceSrc").prop("disabled", true);
		}
	},
	openLaa: function(cellvalue, options, rowObject){
		var openBts;
		openBts = API.createJSON([{
			key: i18n.def['sure'],
			value: function(){
				if ($("#laaName").val() != "" ) {
					$.ajax({
						type : "POST",
			            handler: "cls1220m10formhandler",
			            action: 'checkLaa',
			            data: {
			            	mainId: responseJSON.mainId,
			            	laaName : $("#laaName").val()
			            },
			        }).done(function(responseData){
						if (responseData.msg) {
							API.showPopMessage(responseData.msg);
							return;
						} else {
							$.ajax({
								type : "POST",
					            handler: "cls1220m10formhandler",
					            action: "saveC122S01Y",
					            data: {
									mainId: responseJSON.mainId,
									sOid: $("#C122S01YOid").val(),
									laaName : $("#laaName").val()
				                },
					        }).done(function(responseData){
								if (!responseData.NOTIFY_MESSAGE) {
				            		$("#laaListGrid").trigger("reloadGrid");
				            		API.triggerOpener();
				            		//執行成功
				            		CommonAPI.showPopMessage(i18n.def["runSuccess"], function(){
				            			$.thickbox.close();
				            		});
				            	}
							});
						}
					});
				}
			}
		}, {
			key: i18n.def['cancel'],
			value: function(){
				$("#laaNum").val('');
				$.thickbox.close();
			}
		}]);
		$("#laaDetail").thickbox({
			title : '地政士維護',
			height : 100,
			weight : 100,
			align : "center",
			valign : "bottom",
			model : true,
			i18n : i18n.def,
			open: function(){
				$("#C122S01YOid").val('');
				$("#laaName").val('');
				$.ajax({
					type : "POST",
					handler: "cls1220m10formhandler",
					data : {
						formAction : "getC122S01Y",
						sOid : rowObject.oid
					},
				}).done(function(responseData){
					$("#C122S01YOid").val(responseData.oid);
					$("#laaName").val(responseData.laaName);
				});
            },
			buttons : openBts
		});
	},
	deleteLaa: function(){
		var rows = $("#laaListGrid").getGridParam('selrow');
		var sOid = "";
		if (rows != 'undefined' && rows != null && rows != 0) {
			var data = $("#laaListGrid").getRowData(rows);
			sOid = data.oid;
		}
		if (sOid == "") {
			CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
			return;
		}
		CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
			if(b){
				$.ajax({
					type : "POST",
					handler: "cls1220m10formhandler",
					data : {
						formAction : "deleteC122S01Y",
						sOid : sOid,
						mainId: responseJSON.mainId
					},
				}).done(function(responseData){
					$("#laaListGrid").trigger("reloadGrid");
						API.triggerOpener();
						CommonAPI.showPopMessage(i18n.def["runSuccess"], function(){
							
						});
				});
			}
		});
	}
}

var setEstData = function(){
	//設定經辦下拉選單(有無擔保品-指定收件行員)
	return $.ajax({
		type : "POST",
		handler: "cls1220m10formhandler",
	    //action: "getMegaEmpInfo",
	    data:$.extend( {
        	formAction: "getMegaEmpInfo",
        	isEvaMegaEmp:"Y"
            }, {}
        ), 
	}).done(function(responseData){
		if(responseData.Success){ //成功
			var evaMegaEmp = $("#evaMegaEmp");
			evaMegaEmp.setItems({
	            item: responseData.bossListAO,
	            space: true,
	            format: "{value} {key}",
				disabled: evaMegaEmp.prop("disabled")
	        });
		}
	});
}

var initData = function(){
	$("#introduceSrc").trigger("change");
	var introduceSrc = $("#introduceSrc").val();
	switch (introduceSrc) {
    case '3':
    	$("select#megaCode").trigger("change");
    	$("select#subUnitNo").val(json.subUnitNo);
        break;
	}
	
	$("#estFlag").trigger("change");
	var estFlag = $("#estFlag").val();
	switch (estFlag) {
	case '1':
    	$("select#evaMegaEmp").val(json.evaMegaEmpNo);
//		$("select#evaMegaEmp").find("option[value='"+json.evaMegaEmpNo+"']").attr("selected",true);
        break;
    case '4':
    	console.log($("select#estAddressArea").val());
    	$("select#estAddressCity").trigger("change");
    	$("select#estAddressArea").val(json.estAddressArea);
    	console.log($("select#estAddressArea").val());
        break;
	}
	
}

//地政士
if ($("#laaGrid").length > 0) {
	$("#laaGrid").iGrid({
		handler: 'cls1222gridhandler',
		height: 100,
		autowidth: false,
		needPager: false,
		action: "",
	    postData: {
	    	formAction: "queryC122S01Y",
			mainId : json.mainId
	    },
		colModel : [ {
			colHeader : '黑名單',
			name : 'laaCtlFlagType',
			width : 50,
			sortable : false,
			align : "center"
		}, {
			colHeader : '地政士姓名',
			name : 'laaName',
			width : 100,
			sortable : false,
			formatter : 'click',
	//		onclick : openLaa,
			align : "left"
		}, {
			colHeader : '地政士證書字號',
			name : '',
			width : 180,
			sortable : false,
			align : "left",
			formatter: function enterNumer (cellvalue , options, rowObject) {
	            return '<span>' 
						+ '(' + rowObject[6] + ')'
						+ rowObject[7] + '字'
						+ '第' + rowObject[8] + '號'
						+ '<span/>';
	        }
		}, {
			colHeader : '地政士事務所統編',
			name : 'laaOfficeId',
			width : 120,
			sortable : false,
			align : "left"
		}, {
			colHeader : '地政士事務所名稱',
			name : 'laaOffice',
			width : 130,
			sortable : false,
			align : "left"
		}, {
			colHeader : '地政士黑名單<br/>警示名單敘述',
			name : 'laaDesc',
			width : 150,
			sortable : false,
			align : "left"
		}, {
			colHeader : "laaYear",
			name : 'laaYear',
			hidden : true
		}, {
			colHeader : "laaWord",
			name : 'laaWord',
			hidden : true
		}, {
			colHeader : "laaNo",
			name : 'laaNo',
			hidden : true
		}, {
			colHeader : "laaCtlFlag",
			name : 'laaCtlFlag',
			hidden : true
		}, {
			colHeader : "laaMatchRuleFlag",
			name : 'laaMatchRuleFlag',
			hidden : true
		}, {
			colHeader : "laaQueryDate",
			name : 'laaQueryDate',
			hidden : true
		}, {
			colHeader : "oid",
			name : 'oid',
			hidden : true
		}]
	});
}

if ($("#laaListGrid").length > 0){
	$("#laaListGrid").iGrid({
		handler: 'cls1222gridhandler',
		height: 100,
		autowidth: false,
		needPager: false,
	    postData: {
	    	formAction: "getC122S01YList",
			mainId : json.mainId
	    },
		colModel : [ {
			colHeader : '地政士姓名',
			name : 'laaName',
			width : 300,
			sortable : false,
			formatter : 'click',
			onclick : IntroductionSource.openLaa,
			align : "left"
		}, {
			colHeader : 'oid',
			name : 'oid',
			hidden : true
		}]
	});
}

//金控公司員工引介，根據引介子公司代號，自動產生引介公司分支代號
if(true){ 
	$("select#megaCode").change(function(){
		var val_mega_code = $(this).val();
		if(val_mega_code==''){
//			ilog.debug("call[megaCode='']change");
			$("select#subUnitNo").setItems({});
			$("#subUnitNo").val("");
			$("#subEmpNo").val("");
			$("#subEmpNm").val("");
		}else{
//			ilog.debug("call[megaCode="+val_mega_code+"]change");
    		var key_sub_unitNo = ('LNF13E_SUB_UNITNO_' + val_mega_code);
    		var item = CommonAPI.loadCombos(key_sub_unitNo);
    		$("select#subUnitNo").setItems({ item: item[key_sub_unitNo] , format: "{value} - {key}" });
		}
	});	
	
	$("#agntNo").change(function(){
		var val_agntNo = $(this).val();
		if(val_agntNo==''){
			$("#agntChain").val("");
		}
	});	
	
	$("select#estAddressCity").change(function(){
		var val_estateCityId = $(this).val();
		if(val_estateCityId==''){
//			ilog.debug("call[megaCode='']change");
			$("select#estAddressArea").setItems({});
		}else{
//			ilog.debug("call[megaCode="+val_mega_code+"]change");
    		var key_sub_unitNo = ('counties' + val_estateCityId);
    		var item = CommonAPI.loadCombos(key_sub_unitNo);
    		$("select#estAddressArea").setItems({ item: item[key_sub_unitNo] , format: "{value} - {key}" });
		}
	});
	
}


//初始化設定
IntroductionSource.loadGridInfo();



//按鈕功能
$("#introduceSrc").change(function(){
	IntroductionSource.change();
});	
$("#importRealEstateAgentInfo").click(function(){ 
	IntroductionSource.openBox();
});
$("#importCustOrComButton").click(function(){
	IntroductionSource.importCustomerOrCompanyInfo();
})
$("#addLaa").click(function(){
	IntroductionSource.openLaa();
})
$("#deleteLaa").click(function(){
	IntroductionSource.deleteLaa();
})
$("#estFlag").change(function(){
	var estFlag = $("#estFlag").val();	
	if(estFlag == "4"){ //擔保品座落位置
		$("#reEstView").show();
		$("#evaMegaEmpView").hide();
		initEvaMegaEmpView();
		$("#estUnitView").hide();
		initEstUnitView();
	}else if(estFlag == "1"){ //指定收件行行員
		$("#evaMegaEmpView").show();
		$("#reEstView").hide();
		initReEstView();
		$("#estUnitView").hide();
		initEstUnitView();
	}else if(estFlag == "3"){ //委外估價
		$("#estUnitView").show();
		$("#evaMegaEmpView").hide();
		initEvaMegaEmpView();
		$("#reEstView").hide();
		initReEstView();
	}else{
		$("#reEstView").hide();
		initReEstView();
		$("#evaMegaEmpView").hide();
		initEvaMegaEmpView();
		$("#estUnitView").hide();
		initEstUnitView();
	}
});		

	
	var initReEstView = function(){
		$("#estAddressCity").val("");
		$("#estAddressArea").val("");
		$("#estAddressStreet").val("");
		$("#estAddressVillage").val("");
		$("#estAddressSection").val("");
		$("#estAddressLane").val("");
		$("#estAddressAlley").val("");
		$("#estAddressNo").val("");
		$("#estAddressFloor").val("");
		$("#estAddressLastNo").val("");
		$("#estAddressRoom").val("");	
	}
	var initEvaMegaEmpView = function(){
		$("select#evaMegaEmp").val("");
//		$("#evaMegaEmpNo").val("");
//		$("#evaMegaEmpName").val("");
	}
	var initEstUnitView = function(){
		$("#estUnitName").val("");
	}
	
	var cleanLaa = function(){
		$.ajax({
			type : "POST",
			handler: "cls1220m10formhandler",
			data : {
				formAction : "cleanC122S01Y",
				mainId : responseJSON.mainId
			},
		}).done(function(responseData){
			$("#laaListGrid").trigger("reloadGrid");
			API.triggerOpener();
		});
	}
	
	$(function(){
		setEstData().done(function(){
			initData();
		});
	});
	
});

