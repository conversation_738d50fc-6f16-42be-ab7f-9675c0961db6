package com.mega.eloan.lms.ods.service.impl;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.EloanJdbcTemplate;
import tw.com.iisi.cap.context.CapParameter;

import javax.annotation.Resource;
import javax.sql.DataSource;

public class AbstractODSJdbc {

	private EloanJdbcTemplate jdbc;

	@Resource(name = "odsSqlStatement")
	CapParameter odsSQL;

	@Resource(name = "ods-db")
	public void setDataSource(DataSource dataSource) {
		jdbc = new EloanJdbcTemplate(dataSource, GWException.GWTYPE_ODSDB);
		jdbc.setSqlProvider(odsSQL);
		jdbc.setCauseClass(this.getClass());
	}

	/**
	 * get the the jdbc
	 * 
	 * @return the jdbc
	 */
	public EloanJdbcTemplate getJdbc() {
		return jdbc;
	}
	
	/**
	  * 取得Sql
	  * 
	  * @param sqlId
	  *            sqlId
	  * @return sqlString
	  */
	 public String getSqlBySqlId(String sqlId) {
	  return odsSQL.getValue(sqlId);
	 }
}
