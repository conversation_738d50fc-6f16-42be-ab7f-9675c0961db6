var _handler = "lms2430m01formhandler";

$(function(){	
	
	//default div
	var $gridview = $("#gridview").iGrid({
        handler: "lms2430gridhandler",
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        multiselect: false,  
        sortname: 'approveTime|elfBranch' ,
       	sortorder: 'desc|asc' ,
        postData: {
            formAction: "queryMain",
            docStatus : viewstatus	
        },
        colModel: [
            {
            	colHeader: "",name: 'oid', hidden: true
	        }, {
	        	colHeader: "",name: 'mainId', hidden: true
	        }, {
	        	colHeader: i18n.lms2430v01["C243M01A.elfBranch"],
	            align: "left", width: 80, sortable: true, name: 'elfBranch'
	        }, {
	            colHeader: i18n.lms2430v01["C243M01A.custId"], 
	            align: "left", width: 85, sortable: true, name: 'custId',
				onclick : openDoc, formatter : 'click'
	        }, {
	            colHeader: i18n.lms2430v01["C243M01A.custName"], 
	            align: "left", width: 100, sortable: true, name: 'custName'
	        }, {
	            colHeader: i18n.lms2430v01["C243M01A.elfCrDate"], 
	            align: "left", width: 100, sortable: true, name: 'elfCrDate'
	        }, {
	            colHeader: i18n.lms2430v01["C243M01A.chgCrDate"], 
	            align: "left", width: 100, sortable: true, name: 'chgCrDate'
	        }, {
	            colHeader: i18n.lms2430v01["C243M01A.chgReason"], 
	            align: "left", width: 110, sortable: true, name: 'chgReason'
	        }, {
	        	colHeader: i18n.lms2430v01["C243M01A.updater"], //異動人員
	            align: "left", width: 80, sortable: true, name: 'updater'
	        }, {
	        	colHeader: i18n.lms2430v01["C243M01A.approver"], //核准人員
	            align: "left", width: 80, sortable: true, name: 'approver'
	        }, {
	        	colHeader: i18n.lms2430v01["C243M01A.approveTime"], //核准日期
	            align: "left", width: 80, sortable: true, name: 'approveTime'
	        }
	     ],
		ondblClickRow : function(rowid){
			openDoc(null, null, $gridview.getRowData(rowid));
		}
    });
		
	function openDoc(cellvalue, options, rowObject) {
		console.log("openDoc 被調用，參數:", {cellvalue: cellvalue, options: options, rowObject: rowObject});
		
		if (!rowObject || !rowObject.oid) {
			console.error("rowObject 或 oid 為空:", rowObject);
			return;
		}
		
		console.log("準備開啟文件，oid:", rowObject.oid, "mainId:", rowObject.mainId);
		
		$.form.submit({
			url : '../crs/lms2430m01/01',
			data : {
				'oid' : rowObject.oid,
				'mainOid' : rowObject.oid,
				'mainId' : rowObject.mainId,
				'mainDocStatus' : viewstatus,
				'page' : '01' 
			},
			target : rowObject.oid
		});					
	};
	
	var branchData = null;
	var branchDataLoaded = false;
	$.ajax({
		type : "POST",
		handler : _handler,
		data : {
			formAction : "queryBranch"
		}
	}).done(function(obj){
		branchData = obj;
		branchDataLoaded = true;
		console.log("分行資料載入成功:", branchData);
	}).fail(function(xhr, status, error) {
		console.error("分行資料載入失敗:", error);
	});
	
    // 借款人統編篩選
    $("#buttonPanel").find("#btnFilter").click(function(){
    	var _id = "_div_lms2430v01_filter";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' >");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.lms2430v01["C243M01A.custId"]+"</td><td>"
					+"<input type='text' id='search_custId' name='search_custId' maxlength='10'>"
					+"</td></tr>");
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ 
			title: '借款人統編篩選',
	       width: 400,
           height: 120,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
                  var custId = $("#search_custId").val().trim();
                  if (custId === '') {
                  	API.showMessage("請輸入借款人統編");
                  	return;
                  }
                  
                  $.thickbox.close();
                  $gridview.setGridParam({
  	                postData: {
  	                	formAction: "queryMain",
  	                	docStatus: viewstatus,
  	                	search_custId: custId,
  	                	searchMode: "contains"  // 前後模糊查詢
  	                },
  	                search: true
                  }).trigger("reloadGrid");
               },
               "cancel": function(){
            	   // 清除篩選條件
            	   $gridview.setGridParam({
            		   postData: {
            			   formAction: "queryMain",
            			   docStatus: viewstatus
            		   },
            		   search: true
            	   }).trigger("reloadGrid");
            	   $.thickbox.close();
               }
           }
		});
    }).end().find("#btnAdd").click(function(){
    	console.log("開始處理新增按鈕點擊");
    	
    	// 動態創建對話框 HTML（如果不存在）
    	var dialogId = "_div_lms2430v01_add";
    	var formId = dialogId + "_form";
    	
    	if ($("#" + dialogId).length == 0) {
    		var html = [];
    		html.push("<div id='" + dialogId + "' style='display:none;'>");
    		html.push("<form id='" + formId + "'>");
    		html.push("<table width='100%' border='0' cellpadding='0' cellspacing='0'>");
    		html.push("<tr>");
    		html.push("<td>" + i18n.lms2430v01["C243M01A.elfBranch"] + "</td>");
    		html.push("<td><select id='select_ins_branch' name='branch'></select></td>");
    		html.push("</tr>");
    		html.push("<tr>");
    		html.push("<td>" + i18n.lms2430v01["C243M01A.custId"] + "</td>");
    		html.push("<td>");
    		html.push("<input type='text' id='ins_custId' name='custId' maxlength='10' size='10'>-");
    		html.push("<input type='text' id='ins_dupNo' name='dupNo' value='0' maxlength='1' size='1'>");
    		html.push("</td>");
    		html.push("</tr>");
    		html.push("</table>");
    		html.push("</form>");
    		html.push("</div>");
    		
    		$('body').append(html.join(""));
    	}
    	
    	//清空
    	$("#ins_custId").val("");
    	$("#ins_dupNo").val("0");
    	
    	// 填充分行資料到下拉選單
    	var selectBranch = $("#select_ins_branch");
    	selectBranch.empty();
    	selectBranch.append('<option value="">請選擇分行</option>');
    	
    	if (branchDataLoaded && branchData && branchData.item && branchData.itemOrder) {
    		$.each(branchData.itemOrder, function(index, branchCode) {
    			var branchName = branchData.item[branchCode];
    			selectBranch.append('<option value="' + branchCode + '">' + branchCode + ' - ' + branchName + '</option>');
    		});
    	}
    	
    	$("#" + dialogId).thickbox({
			title : '新增個金覆審控制檔', 
			width : 480, 
			height : 160,
			modal : true, 
			align : 'center', 
			valign: 'bottom', 
			i18n: i18n.def,
			buttons : {
				'sure' : function(){
					var data = {};
					$.each($('#' + formId).serializeArray(), function(i, field) {
						data[field.name] = field.value;
					});
					data.formAction = 'newC243M01A';

					$.ajax({
						type: "POST",
						handler: _handler,
						data: data
					}).done(function(json){
						$gridview.trigger("reloadGrid");	
						$.thickbox.close();	
						
						if (json && json.custNameStatus === "empty") {
							API.showMessage("記錄已成功建立，但客戶姓名暫時空白，請於編輯時補正");
						} else {
							API.showMessage(i18n.def.runSuccess);
						}
					}).fail(function(xhr, status, error) {
						console.error("新增資料失敗:", error);
						API.showMessage("新增資料失敗，請稍後再試");
					});
				},
				'close' : function(){	
					$.thickbox.close();
				}
			}
		});
    }).end().find("#btnDelete").click(function(){ //編製中-刪除
    	var row = $gridview.getGridParam('selrow');
		if(row){
			var data = $gridview.getRowData(row);
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){
					$.ajax({
						handler : _handler,
						type : "POST",
						dataType : "json",
						data :{
							'formAction' : 'delC243M01A',
							'oid' : data.oid,
							'mainOid' : data.oid,
							'mainId' : data.mainId,
							'mainDocStatus' : viewstatus
						}
					}).done(function(obj) {
						$gridview.trigger("reloadGrid");
						API.showMessage(i18n.def.runSuccess);      
					}).fail(function(xhr, status, error) {
						console.error("刪除資料失敗:", error);
						API.showMessage("刪除資料失敗，請稍後再試");
					});
				}
			})				
		}else{
			API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
			return;
		}
    });
});
