<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        	<div id="showL161M01CForm" style="display: none;">
	        	<form id="L161M01CForm">
		            <fieldset>
		                <legend>
		                    <strong>	                                                   
							<th:block th:text="#{'L160M01A.cntrInfo'}"><!--額度動用資訊 --></th:block>
							</strong>
		                </legend>
		                <button id="openCntrnoBox" type="button">
		                	
		                    <span class="text-only"><th:block th:text="#{'L160M01A.edit'}"><!--調閱 --></th:block></span>
		                </button>
						
		                <button id="printBranchAll" type="button" class="forview">
		                	<span class="text-only">
	                          <th:block th:text="#{'L160M01A.printAll'}"><!--整批列印 --></th:block>
							  <th:block th:text="#{'L160M01A.title10'}"><!--聯貸案參貸比率一覽表 --></th:block>
	                        </span>
		                </button>
		                <div id="gridviewCntrnoInfo" ></div>
		            </fieldset>
				</form>
			</div>
            <div id="branchBox" style="display: none;"><!--聯貸案參貸比率一覽表 thinckBox-->
			<form id="L161M01BForm">
                <table class="tb2" width="100%" border="1" cellspacing="0" cellpadding="0" style="margin-top:10px;">
                    <tr>
                        <td width="30%" class="hd1">
                            <th:block th:text="#{'L160M01A.slBank'}"><!-- 參貸行庫/分行--></th:block>
							<button id="includeBranch" type="button" >
                			    <span class="text-only"><th:block th:text="#{'L160M01A.bt09'}"><!--登錄分行--></th:block></span>
              			  </button>
                        </td>
                        <td width="60%">         
                            <span class="color-blue">
                            	<!-- 用來放種類代碼-->
                            	<input id="slBankType" name="slBankType"  class="required" style="display:none"/>
								<input id="slBank" name="slBank" size="1" class="required" style="display:none" />
								<input id="slBankCN" name="slBankCN" class="required"  style="display:none" /><!-- 參貸行庫名稱 -->
								<input id="slBranch" name="slBranch"  style="display:none"  /><!--參貸行庫分行代碼 -->
								<input id="slBranchCN" name="slBranchCN"  style="display:none"  /> <!--參貸行庫分行名稱-->
								<span id="showBranch" ></span>
								<br />
								<span id="showBranchCn" ></span>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <th:block th:text="#{'L160M01A.slMaster'}"><!-- 共同主辦行--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                            <label><input type="checkbox" id="slMaster" name="slMaster" value="Y" /><th:block th:text="#{'L160M01A.yes'}"><!--是--></th:block></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                           <th:block th:text="#{'L160M01A.slAccNo'}"><!-- 同業帳號--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                            <input type="text" id="slAccNo" name="slAccNo" size="14" maxlength="14"   />
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                          <th:block th:text="#{'L160M01A.slAmt'}"><!-- 參貸金額--></th:block>  &nbsp;&nbsp;
                        </td>
                        <td>
                            <input type="text" id="slAmt" name="slAmt" size="15"  maxlength="22" integer="13" fraction="2"  class="required numeric" />
							<th:block th:text="#{'L160M01A.money'}"><!-- 元--></th:block>
                        </td>
                    </tr>
                </table>
				</form>
            </div><!-- end 聯貸案參貸比率一覽表 thinckBox-->
			 
			 <div id="cntrnoInfoBox" style="display: none;"><!--額度動用資訊一覽表 thinckBox-->
			 <form id="L161M01AForm">
			 	 <table class="tb2" width="100%" border="1" cellspacing="0" cellpadding="0"  style="margin-top:5px;">
			    	<tr>
		                <td class="hd1" width="20%">
	                    	<th:block th:text="#{'L160M01A.cntrNum'}"><!--  額度序號--></th:block>&nbsp;&nbsp;
	                    </td>
	                    <td width="30%">
	                        <input type="text" id="cntrNo" name="cntrNo" size="11" readOnly="readonly" />
	                    </td>
					    <td class="hd1" width="20%">
					    	<th:block th:text="#{'L140M01a.type'}"><!-- 性質--></th:block>&nbsp;&nbsp;
						</td>
						<td width="30%">
							<input type="text" id="proPerty" name="proPerty" style="display:none"/>
                        	<input type="text"  id="proPertyShowMsg" name="proPertyShowMsg" class="caseReadOnly" readonly/>
						</td>
					</tr>
				</table>
				 <div class="tabs">
	                <ul>
	                    <li><a id="lms160s0301A" href="#lms160s0301Tab01"><b><th:block th:text="#{'L160M01A.baseInfo'}"><!-- 基本資訊--></th:block></b></a></li>
						<li><a id="lms160s0301B" href="#lms160s0301Tab02" style="display:none" ><b><th:block th:text="#{'L160M01A.unitLoanInfo'}"><!-- 聯貸比率資訊--></th:block></b></a></li>
						<li><a id="lms160s0301C" href="#lms160s0301Tab03"><b><th:block th:text="#{'L160M01A.ApprovalSheetAllocationInfo'}"><!-- 額度明細表聯行攤貸比率--></th:block></b></a></li>
	                     	
	                </ul>
	                <div class="tabCtx-warp">
	                    <div id="lms160s0301Tab01">
		                    				
					  		<ul>
					  		<li>
					  			<span class="text-red">＊</span>
		                        <b><th:block th:text="#{'L160M01A.useSpecialReason'}"><!--修改資料特殊原因 --></th:block>：</b>&nbsp;&nbsp;
								<select id="useSpecialReason" name="useSpecialReason" combokey="lms1605m01_useSpecialReason" class="required"></select>
					  		</li>	
					  		<li>
					  			<span class="text-red">＊</span>
		                        <b><th:block th:text="#{'L140M01a.moneyAmt'}"><!-- 現請額度--></th:block>：</b>&nbsp;&nbsp;	
								<select id="currentApplyCurr" name="currentApplyCurr" class="money" combokey="Common_Currcy" disabled="true"></select>
	                            <input type="text" id="currentApplyAmt" name="currentApplyAmt" size="18" maxlength="22" integer="13" fraction="2" class="numeric required"/>
	                            <th:block th:text="#{'other.money'}"><!-- 元--></th:block>
					  		</li>
							<li id="showDervApplyAmtType">
								<span class="text-red">＊</span><b><th:block th:text="#{'L160M01A.dervApplyAmtType'}"><!-- 衍生性商品現請額度種類--></th:block>：</b>&nbsp;&nbsp;
								<label><input id="dervApplyAmtType" name="dervApplyAmtType" type="radio" value="1"  /><th:block th:text="#{'L160M01A.dervApplyAmtType1'}"><!--授權額度/交易額度(已乘上信用轉換係數/風險係數)--></th:block></label>
								<label><input id="dervApplyAmtType" name="dervApplyAmtType" type="radio" value="2"  /><th:block th:text="#{'L160M01A.dervApplyAmtType2'}"><!-- 名目額度(名目本金)--></th:block></label>
							</li>
					  		<li>
					  			<span class="text-red">＊</span>
		                        <b><th:block th:text="#{'L140M01a.snoKind'}"><!--額度控管種類 --></th:block>：</b>&nbsp;&nbsp;
								<select id="snoKind" name="snoKind"  combokey="lms1405m01_snoKind" space="true" class="required"></select>
					  		</li>
							<li>
					  			<span class="text-red">＊</span>
		                        <b><th:block th:text="#{'L160M01A.caseType'}"><!--案件性質 --></th:block>：</b>&nbsp;&nbsp;
								<select id="caseType" name="caseType" combokey="lms1605m01_caseType" space="true" class="required"></select>
					  		</li>	
								
							<li>
								<span class="text-red">＊</span><b><th:block th:text="#{'L120M01b.unitCase'}"><!-- 本案是否有同業聯貸案額度--></th:block>：</b>
								<label><input id="unitCase" name="unitCase" type="radio" value="Y" onClick="changeSyndicationLoan(this);" class="required"  /><th:block th:text="#{'yes'}"><!-- 是--></th:block></label>
								<label><input id="unitCase" name="unitCase" type="radio" value="N" onClick="changeSyndicationLoan(this);" class="required" checked/><th:block th:text="#{'no'}"><!-- 否--></th:block></label>
							</li>
						 	<fieldset id="SyndicationLoanAddDate" style="display:none">
								<legend>
									<b><th:block th:text="#{'L120M01b.unitInfo'}"><!-- 同業聯貸資訊--></th:block></b>
								</legend>
								<ul id="SyndicationLoan1" style="display:none">
								<li >  <!--id="SyndicationLoan1" style="display:none"-->
								  	<!--&nbsp;&nbsp; -->
								  	<span class="text-red">＊</span><th:block th:text="#{'L120M01b.uCMainBranch'}"><!-- 本行是否為主辦行--></th:block>：
								  	<label><input id="uCMainBranch" name="uCMainBranch" type="radio" value="Y" onClick="changeSyndicationLoan(this);" class="required" /><th:block th:text="#{'yes'}"><!-- 是--></th:block></label>
								  	<label><input id="uCMainBranch" name="uCMainBranch" type="radio" value="N" onClick="changeSyndicationLoan(this);" class="required" /><th:block th:text="#{'no'}"><!-- 否--></th:block></label>
								  	
								 </li>
								 
								<!--<ul id="SyndicationLoan2" style="display:none">-->
								  <li>
								  	<!--&nbsp;&nbsp; -->
								  	<span class="text-red">＊</span><th:block th:text="#{'L120M01b.uCntBranch'}"><!-- 本分行是否為額度管理行--></th:block>：
								  	<label><input id="uCntBranch" name="uCntBranch" type="radio" value="Y" onClick="changeSyndicationLoan(this);" class="required" /><th:block th:text="#{'yes'}"><!-- 是--></th:block></label>
								  	<label><input id="uCntBranch" name="uCntBranch" type="radio" value="N" onClick="changeSyndicationLoan(this);" class="required"/><th:block th:text="#{'no'}"><!-- 否--></th:block></label>
									<span id="SyndicationLoan2_mgr" class="ps1 hide">
								  	  <br />
								  	  &nbsp;&nbsp;
								  	 <span class="text-red"> ※<th:block th:text="#{'L120M01b.uCMainBranchdrc'}"><!-- 若為「管理行」請於動用審核表之「聯貸案參貸比率附表」中建檔--> </th:block>。</span>
								  	</span>
								  </li>
								  <li>
								  	<!--&nbsp;&nbsp; -->
								  	<span class="text-red">＊</span><th:block th:text="#{'L120M01b.uCMSBranch'}"><!-- 本分行是否為擔保品管理行--></th:block>：
								  	<label><input id="uCMSBranch" name="uCMSBranch" type="radio" value="Y" class="required" /><th:block th:text="#{'yes'}"><!-- 是--></th:block></label>
								  	<label><input id="uCMSBranch" name="uCMSBranch" type="radio" value="N" class="required" /><th:block th:text="#{'no'}"><!-- 否--></th:block></label>
								  </li>
								  <li>
								  	<!--&nbsp;&nbsp; -->
								  	<span class="text-red">＊</span><th:block th:text="#{'L120M01b.uHideName'}"><!-- 本案是否為隱名參貸--></th:block>：
								  	<label><input id="uHideName" name="uHideName" type="radio" value="Y" class="required" /><th:block th:text="#{'yes'}"><!-- 是--></th:block></label>
								  	<label><input id="uHideName" name="uHideName" type="radio" value="N" class="required" /><th:block th:text="#{'no'}"><!-- 否--></th:block></label>
								  </li>
								  <li>
								  	<!--&nbsp;&nbsp; -->
								  	<span class="text-red">＊</span><th:block th:text="#{'L120M01b.uArea'}"><!-- 本案為國內聯貸/國際聯貸--></th:block>：
								  	<label><input id="uArea" name="uArea" type="radio" value="A" class="required" /><th:block th:text="#{'L120M01b.uArea1'}"><!-- 國內聯貸--></th:block></label>
								  	<label><input id="uArea" name="uArea" type="radio" value="B" class="required" /><th:block th:text="#{'L120M01b.uArea2'}"><!-- 國際聯貸--></th:block></label>
					
								  </li>
								  <li id="branchShow025_2" class="hide">
								  	<!--&nbsp;&nbsp; -->
								  	<span class="text-red">＊</span><th:block th:text="#{'L120M01b.uRP'}"><!-- 報核方式--></th:block>：
								  	<div >
								  		<ul>
								  			<li >
								  				&nbsp;&nbsp;<!--&nbsp;&nbsp; -->
								  				<label><input id="uRP1" name="uRP1" type="radio" value="A"  class="required" /><th:block th:text="#{'L120M01b.uRP1A'}"><!-- 國金部報核--></th:block></label>
								  				<label><input id="uRP1" name="uRP1" type="radio" value="B" class="required" /><th:block th:text="#{'L120M01b.uRP1B'}"><!-- 分行報核--></th:block></label>
								  			</li>
								  			<li >
								  				&nbsp;&nbsp;<!--&nbsp;&nbsp; -->
								  				<label><input id="uRP2"  name="uRP2" type="radio" value="A"  class="required" /><th:block th:text="#{'L120M01b.uRP2A'}"><!-- 國金部對外--></th:block></label>
								  				<label><input id="uRP2" name="uRP2" type="radio" value="B" class="required" /><th:block th:text="#{'L120M01b.uRP2B'}"><!-- 分行對外--></th:block></label>
								  			</li>
								  			<li>
								  				&nbsp;&nbsp;<!--&nbsp;&nbsp; -->
								  				<label><input id="uRP3"  name="uRP3" type="radio" value="A"  class="required" /><th:block th:text="#{'L120M01b.uRP3A'}"><!-- 國金部掛帳--></th:block></label>
								  				<label><input id="uRP3" name="uRP3" type="radio" value="B" class="required" /><th:block th:text="#{'L120M01b.uRP3B'}"><!-- 分行掛帳--></th:block></label>
								  			</li>
								  		</ul>
								  	</div>
								  </li>
							   </ul>
							<!-- SyndicationLoan2 -->
							</fieldset>
							
							<!-- SyndicationLoan1 -->
							<li id="showforDocCode3" style="display:none">
								<th:block th:text="#{'L120M01b.message01'}"><!-- 陳復/陳述--> </th:block>
							<span class="text-red"> ※<th:block th:text="#{'L120M01b.message02'}"><!-- 陳復(述)案、授信異常通報案免附額度明細表--> </th:block>。</span>
							</li>	
							
							<li>
								<span class="text-red">＊</span><b><th:block th:text="#{'L120M01b.unitMega'}"><!-- 本案是否有同行(本行)聯貸案額度行--></th:block>：</b>
								<label><input id="unitMega" name="unitMega" type="radio" value="Y"  onClick="changeSyndicationLoan(this);" class="required" /><th:block th:text="#{'yes'}"><!-- 是--></th:block></label>
								<label><input id="unitMega" name="unitMega" type="radio" value="N"  onClick="changeSyndicationLoan(this);" class="required" checked/><th:block th:text="#{'no'}"><!-- 否--></th:block></label>
							</li>
							
							
							<li>
								<span class="text-red">＊</span><b><th:block th:text="#{'L120M01b.coKind'}"><!-- 合作業務種類--></th:block>：</b>
								<select id="coKind" name="coKind" onChange="changeItem(this);"  combokey="lms1405s01_coKind" combotype="2" class="required">
									<!-- <option value="0" selected><th:block th:text="#{'L120M01b.coKind0'}">非合作業務</th:block></option>
									<option value="1"><th:block th:text="#{'L120M01b.coKind1'}"> 價金履約保證</th:block></option>
									<option value="2"><th:block th:text="#{'L120M01b.coKind2'}"> 合作外匯</th:block></option>
									<option value="Z"><th:block th:text="#{'L120M01b.coKindZ'}">其他合作業務</th:block></option>-->
								</select>
								
									<ul id="coKindSpan" class="hide" >
										<li>
											<span class="text-red">＊</span>
											<span id="coKind_1" class="color-blue"><th:block th:text="#{'L120M01b.coKind0'}"><!-- 非合作業務--></th:block></span><th:block th:text="#{'L120M01b.mCntrt'}"><!-- 母戶--></th:block>：
											<label><input id="mCntrt" name="mCntrt" type="radio" value="Y" onClick="changeItem(this);" class="required" /><th:block th:text="#{'yes'}"><!-- 是--></th:block></label>
											<label><input id="mCntrt"name="mCntrt" type="radio" value="N" onClick="changeItem(this);"  class="required" /><th:block th:text="#{'no'}"><!-- 否--></th:block></label>
										</li>
										<div id="coKind_parent">							 
										  	<span class="text-red">＊</span>
										  	<span id="coKind_2" class="color-blue"><th:block th:text="#{'L120M01b.coKind0'}"><!-- 非合作業務--></th:block></span><th:block th:text="#{'L120M01b.sCntrt'}"><!-- 子戶--></th:block>：
										  	<label><input id="sCntrt" name="sCntrt" type="radio" value="Y" onClick="changeItem(this);" class="required" /><th:block th:text="#{'yes'}"><!-- 是--></th:block></label>
										  	<label><input id="sCntrt" name="sCntrt" type="radio" value="N" onClick="changeItem(this);" class="required"/><th:block th:text="#{'no'}"><!-- 否--></th:block></label>
										
										  <div id="coKind_son" class="hide">
										  	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
										  	<span id="coKind_3" class="color-blue"><th:block th:text="#{'L120M01b.coKind0'}"><!-- 非合作業務--></th:block></span><th:block th:text="#{'L120M01b.mScntrt'}"><!-- 母戶之額度序號--></th:block>：
										  	<input id="mScntrt" name="mScntrt" type="text" value=""  maxlength="12" size="13" class="obuText required"/>
										  	<br />
										  	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
										  	<span id="coKind_4" class="color-blue"><th:block th:text="#{'L120M01b.coKind0'}"><!-- 非合作業務--></th:block></span><th:block th:text="#{'L120M01b.mSAcc'}"><!-- 子戶之代收帳號--></th:block>：
										  	<input id="mSAcc" name="mSAcc" type="text" value="" maxlength="14" size="15" class="obuText required"/>
									    </div>
									  </div>
									</ul>
							</li>
						</ul>
	                    </div>
	                    <div id="lms160s0301Tab02">
		                   <div id="SyndicationLoanAddDate1" style="display:none">
					            <fieldset>
					                <legend>
					                    <strong><th:block th:text="#{'L160M01A.title15'}"><!--同業聯貸案帳務管理行必要資訊--></th:block></strong>
					                </legend>
						            <table class="tb2" width="100%" border="1" cellspacing="0" cellpadding="0" bordercolor="#000000">
						                <tr>
						                    <td class="hd1" width="20%">
						 							<th:block th:text="#{'L160M01A.caseNo'}"><!--  案 號--></th:block>&nbsp;&nbsp;
						                    </td>
						                    <td colspan="3" width="80%">
												<span id="caseNo" ></span>
						                    </td>
						                </tr>
						                <tr>
						                    <td class="hd1" width="20%">
						                        <span class="text-red">＊</span><th:block th:text="#{'L160M01A.caseGist'}"><!--案 由--></th:block>&nbsp;&nbsp;<br/>
												<button id="applyGist" type="button" class="forview">
								                	<span class="text-only">
							                          <th:block th:text="#{'L160M01A.bt20'}"><!--引進案由 --></th:block>
							                        </span>
								                </button>
						                    </td>
						                    <td colspan="3" width="80%">
						                        <textarea id="gist" name="gist" cols="70" rows="6" maxlengthC="1365"></textarea>
						                    </td>
						                </tr>
						                <tr>
						                    <td class="hd1" width="20%">
						 						<th:block th:text="#{'L160M01A.curr'}"><!--  幣 別--></th:block>&nbsp;&nbsp;
						                    </td>
						                    <td width="30%">
						                        <select id="quotaCurr" name="quotaCurr"  combokey="Common_Currcy" space="true"></select>
						                    </td>
						                    <td class="hd1" width="20%">
						 							<th:block th:text="#{'L160M01A.allMoney'}"><!--  總　額　度--></th:block>&nbsp;&nbsp;
						                    </td>
						                    <td width="30%">
						                        <input type="text" id="quotaAmt" name="quotaAmt" size="16" maxlength="22" integer="13" fraction="2"  class="numeric" /><!--class="required numeric" --><th:block th:text="#{'L160M01A.money'}"><!-- 元--></th:block>
						                    </td>
						                </tr>
						                <tr>
						                    <td class="hd1">
						                      <th:block th:text="#{'L160M01A.signDate'}"><!--簽約日期--></th:block>&nbsp;&nbsp;
						                    </td>
						                    <td>
						                        <input type="text" id="signDate" name="signDate" class="date" size="11" maxlength="10"  /> <!--required-->
						                    </td>
						                    
						                </tr>
						            </table>
								</fieldset>	
						    </div>	
					            
							
				            <fieldset>
				                <legend>
				                    <strong><th:block th:text="#{'L160M01A.title10'}"><!--聯貸案參貸比率一覽表--></th:block></strong>
				                </legend>
				                <button id="openBranchBox" type="button">
				                    <span class="text-only"><th:block th:text="#{'L160M01A.bt07'}"><!--新增聯貸參貸比率--></th:block></span>
				                </button>
								<button id="copyBranchBox" type="button">
				                    <span class="text-only"><th:block th:text="#{'L160M01A.bt19'}"><!--複製同業參貸比率--></th:block></span>
				                </button>
				                <button id="deleteBranch" type="button" >
				                    <span class="text-only"><th:block th:text="#{'L160M01A.bt06'}"><!--刪除--></th:block></span>
				                </button>
								<button id="printBranch" type="button" class="forview">
				                    <span class="text-only"><th:block th:text="#{'button.print'}"><!--列印--></th:block></span>
				                </button>
				                <div id="gridviewBranch" ></div>
				            </fieldset>
						</div>	
	                    <div id="lms160s0301Tab03">
						  <table class="tb2" width="800px" border="0" cellpadding="0" cellspacing="0">
						    <tr class="hd1" style="text-align:left">
						      <td>
						        <button type="button" id="newItemChildren3Bt"  class="noHideBt"><span class="text-only"><th:block th:text="#{'button.add'}"><!-- 新增--></th:block></span></button> 
						        <button type="button" id="removeGridviewitemChildren3"  class="noHideBt"><span class="text-only"><th:block th:text="#{'button.delete'}"><!-- 刪除--></th:block></span></button>
						        <button type="button" id="changesShareRate2" class="noHideBt"><span class="text-only"><th:block th:text="#{'btn.changesShareRate2'}"><!--變更分母--></th:block></span></button>
						      </td>
						    </tr>
						    <tr>
						      <td> 					
						          <div id="gridviewL140M01E_AF" ></div>
						      </td>
						    </tr>
						  </table>
						</div><!--end tabs-c01_3-->
	                </div>
	            </div>
				<th:block th:text="#{'L160M01A.randomcode'}"><!--報表亂碼--></th:block>UID：<input type="text" id="uid" name="uid" size="40" readonly="readonly"/>&nbsp;&nbsp;
			</form>
			
			   
                
            </div><!-- end 聯貸案參貸比率一覽表 thinckBox-->
			
			 <div id="includeBranchBox" style="display: none;"><!--登錄銀行 thinckBox-->
				<form id="includeBranchForm" name="includeBranchForm" >
                <table class="tb2" width="100%" border="1" cellspacing="0" cellpadding="0" style="margin-top:10px;">
                    <tr>
                        <td width="40%" class="hd1">
                            <th:block th:text="#{'L162M01A.breanch'}"><!-- 金融機構種類--></th:block>&nbsp;&nbsp;
                        </td>
                        <td width="60%">         
                          <select id="selBank" combokey="lms1605s03_slBankType" space="true" ></select>
		                    
                        </td>
                    </tr>
                    <tr >
                        <td class="hd1">
                            <th:block th:text="#{'L160M01A.slBank'}"><!-- 參貸行庫/分行--></th:block>&nbsp;&nbsp;
                        </td>
                        <td id="tdSelBank">
                             <select id="selBank01" combokey="BankCode01" space="true" style="display:none"></select>
							 <select id="selBankOther01" style="display:none"></select>
						     <select id="selBank02" style="display:none" combokey="BankCode02" space="true"></select>
							 <select id="selBankOther02" style="display:none"></select>
						     <select id="selBank03" style="display:none" combokey="BankCode03" space="true"></select>
							 <select id="selBank04" style="display:none" combokey="BankCode04" space="true"></select>
							 <select id="selBank05" style="display:none" combokey="BankCode05" space="true"></select>
							 <select id="selBank06" style="display:none" combokey="BankCode06" space="true"></select>
							 <select id="selBank07" style="display:none" combokey="BankCode07" space="true"></select>
							 <select id="selBank08" style="display:none" combokey="BankCode08" space="true"></select>
							 <select id="selBank09" style="display:none" combokey="BankCode09" space="true"></select>
							 <select id="selBank10" style="display:none" combokey="BankCode10" space="true"></select>
							 <select id="selBank11" style="display:none" combokey="BankCode11" space="true"></select>
							 <select id="selBank12" style="display:none" ></select>
							 <select id="selBank99" style="display:none" ></select>
                        </td>
                    </tr>                  
                </table>
				</form>
            </div><!-- end 登錄銀行 thinckBox--> 
			<!-- 登錄動審聯行攤貸比例 thinkBox -->
			<div id="newItemChildrenBox3" style="display:none;">
				<form id="L140M01E_AFForm" name="L140M01E_AFForm" >
					<table width="100%" class="tb2" border="0" cellpadding="0" cellspacing="0">
						<tr>
						  <td width="60%" class="hd1"><th:block th:text="#{'L140M01a.moneyAmt'}"><!-- 現請額度--></th:block>&nbsp;&nbsp;</td>
						  <td width="40%">
							   <input id="totalAmt" name="totalAmt" readonly="readonly"  class="caseReadOnly"/>
						  </td>
						</tr>
						<tr>
						  <td  class="hd1"><th:block th:text="#{'L140M01e.shareBrId'}"><!-- 攤貸分行--></th:block>&nbsp;&nbsp;</td>
						  <td >
							    <select id="shareBrId" name="shareBrId" ></select><!--分行位置 -->
						  </td>
						</tr>
						<tr>
						  <td class="hd1">
						  	<label><input type="radio" id="shareFlag" name="shareFlag" value="1" class="required"/><th:block th:text="#{'L140M01e.shareAmt'}"><!-- 攤貸金額--></th:block></label>&nbsp;&nbsp;
							</td>
						  <td>	
						    <input type="text" id="shareAmt" name="shareAmt" size="13" maxlength="22" integer="13" fraction="2"  class="required numeric" />
						    <!--<button type="button" id="shareMoneyCount2" class="noHideBt" ><span class="text-only"><th:block th:text="#{'L140M01e.count2'}">計算比率</th:block></span></button>-->
						    <span id="tips2" class="text-red"></span>
						  </td>
						</tr>
						<tr>
						  <td class="hd1">
						  	<label><input type="radio" id="shareFlag" name="shareFlag" value="2" class="required"/><th:block th:text="#{'L140M01e.shareRate1'}"><!-- 攤貸比例--></th:block></label>&nbsp;&nbsp;
						  </td>
						  <td>
						    <input type="text" id="shareRate1" name="shareRate1" size="5" maxlength="5"  integer="5" fraction="0" class="numeric required"/>/
						    <input type="text" id="shareRate2" name="shareRate2" size="5" maxlength="5" integer="5" fraction="0" class="numeric required"/>
						    <!--<button type="button" id="shareMoneyCount1" class="noHideBt"><span class="text-only"><th:block th:text="#{'L140M01e.count'}"> 計算金額</th:block></span></button>-->
						    <span id="tips" class="text-red"></span>
						  </td>
						</tr>
					</table>
				</form>
			</div>
			<!-- 登錄動審聯行攤貸比例 thinkBox END-->
			<!-- 登錄聯行攤貸比例 國內額度序號給號 thinkBox -->
			<div id="cntrNoBoxforItem3" style="display:none;">
				<form id="cntrNoBoxforItem3Form" name="cntrNoBoxforItem3Form">
					<table width="100%" class="tb2" border="1" >
						<tr>
							<!-- 用來放額度序號類型 -->
							<input type="text" id="cntrNoType" style="display:none;" />
						</tr>	
						<tr>
							<td width="80%" class="hd1"><th:block th:text="#{'L140M01a.message29'}"><!-- 請選擇額度序號來源--></th:block>&nbsp;&nbsp;</td>
							<td width="20%">
								<label><input type="radio"  name="numberType" value="1" class="required" /><th:block th:text="#{'L140M01a.message30'}"><!-- 產生新號(適用於「新做」案件)--></th:block></label>
								<br/>
								<label><input type="radio"  name="numberType" value="2" class="required" /><th:block th:text="#{'L140M01a.message32'}"><!-- 登錄原案額度序號(適用於舊案續約及條件變更)--></th:block></label>
							</td>
						</tr>
						<tr class="ForInSide">
							<td class="hd1"><th:block th:text="#{'typCd.title'}"><!-- 類別--></th:block>&nbsp;&nbsp;</td>
							<td>
								<label><input type="radio" name="typeCd" value="1" class="required"/>DBU</label> &nbsp;&nbsp;
								<label><input type="radio" name="typeCd" value="4" class="required"/>OBU</label>
							</td>
						</tr>
						<tr id="showBrNoTr" class="ForInSide">
							<td  class="hd1"><th:block th:text="#{'L140M01a.message28'}"><!-- 請輸入欲產生額度序號之作帳行分行代碼(三碼)--></th:block>&nbsp;&nbsp;</td>
							<td >
								<input type="text"  id="branchNoItem3" maxlength="3" minlength="3" size="3" class="required upText" />
							</td>
						</tr>
						<tr class="ForOriginal">
							<td class="hd1">&nbsp;&nbsp;</td>
							<td>	
								<b><th:block th:text="#{'L140M01a.message33'}"><!-- 請輸入原額度序號: 該舊額度序號須已執行轉換，轉換後新編碼之額度序號--></th:block><br/>
									【<th:block th:text="#{'L140M01a.message68'}"><!-- 額度序號長度應為12碼，編碼原則:XXX(分行代號)+X(1:DBU,4:OBU,5:海外)+YYY(年度)+99999(流水號) --></th:block>】
								</b><br/>
								<input type="text"  id="originalCntrNo" size="11" minlength="12" maxlength="12"  class="upText required" />
							</td>
						</tr>
					</table>
				</form>
			</div>
			<!-- 登錄動審聯行選擇分母 thinkBox -->
			<div id="newSharteNewBox" style="display:none;">
				<span>
					<form id="newSharteNewForm" name="newSharteNewForm" >
						<input type="text" id="newSharteNew" name="newSharteNew" size="5" maxlength="5" integer="5" fraction="0" class="numeric required" />
					</form>	
				</span>
			</div><!-- 登錄動審聯行選擇分母 thinkBox END-->
			<!--儲存動審聯行攤貸比例 比例金額檢核-->
			<div id="l140m01eAmtBox" style="display:none;">
                <table border="0" cellpadding="0" cellspacing="0">
                    <tr>
                        <td>
                            <span id="l140m01eAmtMsg" ></span>
                            <br/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div id="gridviewL140m01eAmt" ></div>
                        </td>
                    </tr>
                </table>
            </div>
			<!--儲存動審聯行攤貸比例 比例金額檢核 END-->
			<div id="selectCntrnoInfoBox" style="display:none;">
                <table border="0" cellpadding="0" cellspacing="0">
                    <tr>
                        <td>
                            <th:block th:text="#{'L160M01A.message80'}"><!--請選擇一筆欲複製同業參貸比率的額度動用資訊(系統會清除目前已存在參貸比率中同業參貸資料再複製)--></th:block>&nbsp;
                        </td>
                    </tr>
                </table>
                <div id="copyL161S01BGrid" ></div>
            </div>
            
			<!-- 舊案顯示模式 -->
			<div id="showOldL161M01AForm" style="display:none;">
				<fieldset>
	                <legend>
	                    <strong>	                                                   
						<th:block th:text="#{'L160M01A.cntrInfo'}"><!--額度動用資訊 --></th:block>
						</strong>
	                </legend>
					<form id="L161M01AFormOld">
			            <br/>
						 <b><th:block th:text="#{'L160M01A.unitLoanCase'}"><!-- 本案是否有同業聯貸案額度--></th:block>：</b>
						 <span class="color-blue"><span id="unitLoanCaseShowOld" ></span></span>
						 <br/>
			            <b><th:block th:text="#{'L160M01A.uCMainBranch'}"><!-- 本分行是否為管理行--></th:block>：</b>
			            <span class="color-blue"><span id="uCMainBranchShowOld" ></span></span>
			            <br/>
			            <br/>
				            <table class="tb2" width="100%" border="1" cellspacing="0" cellpadding="0" bordercolor="#000000">
				                <tr>
				                    <td class="hd1" width="20%">
				 							<th:block th:text="#{'L160M01A.caseNo'}"><!--  案 號--></th:block>&nbsp;&nbsp;
				                    </td>
				                    <td colspan="3" width="80%">
										<span id="caseNoOld" ></span>
				                    </td>
				                </tr>
				                <tr>
				                    <td class="hd1" width="20%">
				                        <span class="text-red">＊</span><th:block th:text="#{'L160M01A.caseGist'}"><!--案 由--></th:block>&nbsp;&nbsp;
				                    </td>
				                    <td colspan="3" width="80%">
				                        <textarea id="gistOld" name="gistOld" cols="92" rows="6" maxlengthC="1365"></textarea>
				                    </td>
				                </tr>
				                <tr>
				                    <td class="hd1" width="20%">
				 						<th:block th:text="#{'L160M01A.curr'}"><!--  幣 別--></th:block>&nbsp;&nbsp;
				                    </td>
				                    <td width="30%">
				                        <select id="quotaCurrOld" name="quotaCurrOld"  combokey="Common_Currcy" space="true"></select>
				                    </td>
				                    <td class="hd1" width="20%">
				 							<th:block th:text="#{'L160M01A.allMoney'}"><!--  總　額　度--></th:block>&nbsp;&nbsp;
				                    </td>
				                    <td width="30%">
				                        <input type="text" id="quotaAmtOld" name="quotaAmtOld" size="16" maxlength="22" integer="13" fraction="2"  class="numeric" /><!--class="required numeric" --><th:block th:text="#{'L160M01A.money'}"><!-- 元--></th:block>
				                    </td>
				                </tr>
				                <tr>
				                    <td class="hd1">
				                      <th:block th:text="#{'L160M01A.signDate'}"><!--簽約日期--></th:block>&nbsp;&nbsp;
				                    </td>
				                    <td>
				                        <input type="text" id="signDateOld" name="signDateOld" class="date required" size="11" maxlength="10"  />
				                    </td>
				                    <td class="hd1">
				                    <th:block th:text="#{'L160M01A.cntrNum'}"><!--  額度序號--></th:block>&nbsp;&nbsp;
				                    </td>
				                    <td>
				                        <!--<select id="cntrNoOld" name="cntrNoOld" class="cntrNo" space="true" ></select>-->
				                         <input type="text" id="cntrNoOld" name="cntrNoOld" size="11" readOnly="readonly" />
				                    </td>
				                </tr>
				            </table>
					
			            <fieldset>
			                <legend>
			                    <strong><th:block th:text="#{'L160M01A.title16'}"><!--聯貸案參貸比率一覽表--></th:block></strong>
			                </legend>
							<button id="printBranchOld" type="button" class="forview">
			                    <span class="text-only"><th:block th:text="#{'button.print'}"><!--列印--></th:block></span>
			                </button>
			                <div id="gridviewBranchOld" ></div>
			            </fieldset>
					</form>
				</fieldset>
			</div>
			
			<!-- 共用參數，用來判斷要顯示新模式還是舊模式 -->
			<div id="commUseParam" style="display:none;">
			   <form id="commUseParamForm">
			   		<input type="text" id="showVersion" name="showVersion" size="40" readonly="readonly"/>
			   </form>
			</div>

        </th:block>
    </body>
</html>