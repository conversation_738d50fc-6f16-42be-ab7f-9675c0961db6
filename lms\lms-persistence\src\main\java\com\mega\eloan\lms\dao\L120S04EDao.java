/* 
 * L120S04EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S04E;

/** 往來實績彙總表利潤貢獻度業務別占比檔 **/
public interface L120S04EDao extends IGenericDao<L120S04E> {

	L120S04E findByOid(String oid);
	
	List<L120S04E> findByMainId(String mainId);

	List<L120S04E> findByIndex01(String mainId, String keyCustId, String keyDupNo, String docKind);

	List<L120S04E> findByMainIdDocKind(String mainId, String[] docKind);

	List<L120S04E> findByMainIdKeyCustIdDupNo(String mainId, String keyCustId, String keyDupNo);

	List<L120S04E> findByMainIdKeyCustIdDupNoDocKind(String mainId, String keyCustId, String keyDupNo, String[] docKind);
}