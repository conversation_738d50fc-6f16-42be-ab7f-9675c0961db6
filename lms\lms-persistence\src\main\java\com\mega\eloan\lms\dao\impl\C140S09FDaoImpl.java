/* 
 * C140S09FDaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C140S09FDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C140M01A;
import com.mega.eloan.lms.model.C140S09F;
import com.mega.eloan.lms.model.C140S09F_;

/**
 * <pre>
 * 徵信調查報告書第九章 董監事(子)同一關係人明細
 * </pre>
 * 
 * @since 2011/10/27
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
@Repository
public class C140S09FDaoImpl extends LMSJpaDao<C140S09F, String> implements
		C140S09FDao {

	@Override
	public int deleteByMeta(C140M01A meta) {
		Query query = entityManager
				.createNamedQuery("ces140s09f.deleteByMainIdAndPid");

		query.setParameter("mainId", meta.getMainId());
		query.setParameter("pid", meta.getUid());
		return query.executeUpdate();
	}
	
	@Override
	public List<C140S09F> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,C140S09F_.mainId.getName(), mainId);
		return find(search);
	}	
}// ;
