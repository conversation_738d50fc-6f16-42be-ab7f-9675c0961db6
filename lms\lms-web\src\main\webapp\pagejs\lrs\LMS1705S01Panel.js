//alert(JSON.stringify(responseJSON)); 
$(function(){
    /*------------------------------------------ 引進基本資料----------------------------------------*/
    $("#_lms1705s01Button1").click(function(){
        $.ajax({
            handler: "lms1705m01formhandler",
            type: "POST",
            action: "queryCreditBase",
            data: {
                mainOid: responseJSON.oid,
                mainId: responseJSON.mainId,
                dupNo: responseJSON.dupNo,
                showMsg: true,
                txCode: responseJSON.txCode
            },
		}).done(function(responseData){
			  //  alert(JSON.stringify(responseJSON));
			  $("#L170M01aForm").injectData(responseData);
			jsSelectIsExitItem($("#excrdType"),"XC");
			jsSelectIsExitItem($("#excrdTypeMow"),"XM");
			//信用評等 
			  $("#crdType").val(responseData.crdType);
			  //信用等級
			  $("#grade").val(responseData.grade);
			  //信用風險內部評等  
			  $("#crdTypeMow").val(responseData.mowtype);
			  //信用風險內部評等等級
			  $("#mow").val(responseData.mowtbl1);
			  //未評等
			  typeCheck();
			  //免辦
			  mowCheck();
			  //信用風險內部評等 
			  //當 NM=Moody,NS=S&P,NF=Fitch,NC=中華信評 有資料才需要顯示
			  if (responseData.NMSFC == "Y") {
			      //                    var temp = "";
			      //                    for (var key in responseData.crdTypeN) {
			      //                        temp += temp.length > 0 ? ",</br>" : "";
			      //                        temp += responseData.crdTypeN[key];
			      //                    }
			      //                    
			      //                    $("#crdTypeN").html(temp);
			  }
			  //前次評等資料
			$("#excrdType").val(responseData.excrdType);
			$("#exgrade").val(responseData.exgrade);
			$("#excrdTypeMow").val(responseData.exmowtype);
			$("#exmow").val(responseData.exmowtbl1);
			$("#excrdTypeCN").html(DOMPurify.sanitize(responseData.excrdTypeCN));
			  $("#excrdTypeHiddenCN").val(responseData.excrdTypeHiddenCN);
			exCheck($("#excrdType"),$("#exgrade111"),$("#exgrade"));
			exCheck($("#excrdTypeMow"),$("#exmow111"),$("#exmow"));
			  
			  var i = responseData.error;
			  switch (i) {
			      case '1':
			          return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message1"]);
			          break;
			      case '2':
			          return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message2"]);
			          break;
			      case '3':
			          return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message3"]);
			          break;
			      case '4':
			          return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message4"]);
			          break;
			      case '5':
			          return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message5"]);
			          break;
			      case '6':
			          return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message6"]);
			          break;
			      case '7':
			          return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message7"]);
			          break;
			      case '8':
			          return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message8"]);
			          break;
			      case '9':
			          return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message9"]);
			          break;
			      case '10':
			          return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message10"]);
			          break;
			      case '11':
			          return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message11"]);
			          break;
			  }
		})
    })
    //-------------------------------------------- 引信用信用風險 部評等----------------------------------------  
    $("#_lms1705s01Button6").click(function(){
        $("#creditBom2").thickbox({
            //L170M01a.insertData=請選擇信用評等種類                                                                            
            title: i18n.lms1705s01['L170M01a.title'],
            width: 200,
            height: 150,
            align: 'center',
            valign: 'bottom',
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(showMsg){
                    var i = $("[name=gradeType2]:checked").val();
                    //L170M01a.message12=請至少選擇一項信用評等種類!!
                    //alert(i);
                    if (!i || i == "") {
                        return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message12"]);
                    }
                    $.ajax({
                        handler: "lms1705m01formhandler",
                        type: "POST",
                        dataType: "json",
                        data: $.extend($("#creditDataForm2").serializeData(), {
                            formAction: "queryCreditData",
                            mainId: responseJSON.mainId,
                            oid: responseJSON.oid,
                            showMsg: true,
                            gradeType: i,
                            txCode: responseJSON.txCode
                        }),
					}).done(function(responseData){
						$("#L170M01aForm").injectData(responseData);
						$("#crdTypeCN").html(DOMPurify.sanitize(responseData.crdTypeCN));
						$("#crdTypeHiddenCN").val(responseData.crdTypeHiddenCN);
						//信用風險內部評等 
						//當 NM=Moody,NS=S&P,NF=Fitch,NC=中華信評 有資料才需要顯示
						typeCheck();
						mowCheck();
						//前次評等資料
						$("#excrdType").val(responseData.excrdType);
						$("#exgrade").val(responseData.exgrade);
						$("#excrdTypeMow").val(responseData.exmowtype);
						$("#exmow").val(responseData.exmowtbl1);
						$("#excrdTypeCN").html(DOMPurify.sanitize(responseData.excrdTypeCN));
						$("#excrdTypeHiddenCN").val(responseData.excrdTypeHiddenCN);
						exCheck($("#excrdType"),$("#exgrade111"),$("#exgrade"));
						exCheck($("#excrdTypeMow"),$("#exmow111"),$("#exmow"));
					})
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        })
    })
    //-------------------------------------------- 引信用評等資料----------------------------------------          
    $("#_lms1705s01Button3").click(function(){
        $("#creditBom").thickbox({
            //L170M01a.insertData=請選擇信用評等種類                                                                            
            title: i18n.lms1705s01['L170M01a.title'],
            width: 200,
            height: 200,
            align: 'center',
            valign: 'bottom',
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(showMsg){
                
                    //L170M01a.message12=請至少選擇一項信用評等種類!!
                    //alert(i);
                    if ($("[name=gradeType]:checked").val() == "") {
                        return CommonAPI.showErrorMessage(i18n.lms1705s01["L170M01a.message12"]);
                    }
                    $.ajax({
                        handler: "lms1705m01formhandler",
                        type: "POST",
                        dataType: "json",
                        data: $.extend($("#creditDataForm").serializeData(), {
                            formAction: "queryCreditData",
                            mainId: responseJSON.mainId,
                            oid: responseJSON.oid,
                            showMsg: true,
                            gradeType: $("gradeType").val(),
                            txCode: responseJSON.txCode
                        }),
					}).done(function(responseData){
						$("#L170M01aForm").injectData(responseData);
						jsSelectIsExitItem($("#excrdType"),"XC");
						jsSelectIsExitItem($("#excrdTypeMow"),"XM");

						//信用評等   
						$("#crdType").val(responseData.crdType);
						//信用等級
						$("#grade").val(responseData.grade);

						//信用風險內部評等  
						$("#crdTypeMow").val(responseData.crdTypeMow);

						//信用風險內部評等 等級
						$("#mow").val(responseData.mow);

						typeCheck();
						mowCheck();
						//前次評等資料
						$("#excrdType").val(responseData.excrdType);
						$("#exgrade").val(responseData.exgrade);
						$("#excrdTypeMow").val(responseData.exmowtype);
						$("#exmow").val(responseData.exmowtbl1);
						$("#excrdTypeCN").html(DOMPurify.sanitize(responseData.excrdTypeCN));
						$("#excrdTypeHiddenCN").val(responseData.excrdTypeHiddenCN);
						exCheck($("#excrdType"),$("#exgrade111"),$("#exgrade"));
						exCheck($("#excrdTypeMow"),$("#exmow111"),$("#exmow"));
					})
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        })
    })
    /*-----------------------------------------引進信評資料畫面動作-------------------------------------*/
    $("#crdType").change(function(){
    	typeCheck();
    });//  END
    $("#crdTypeMow").change(function(){
    	mowCheck();
    });//  END
    
    function typeCheck(){
    	if ($("#crdType").val() == "NA" || $("#crdType").val() == "") {
            $("#grade111").hide();
        }
        else {
            $("#grade111").show();
        }
    }
    function mowCheck(){
    	if ($("#crdTypeMow").val() == "G" || $("#crdTypeMow").val() == "") {
            $("#mow111").hide();
        }
        else {
            $("#mow111").show();
        }
    }
	$("#excrdType").change(function(){
    	exCheck($("#excrdType"),$("#exgrade111"),$("#exgrade"));
    });
    $("#excrdTypeMow").change(function(){
    	exCheck($("#excrdTypeMow"),$("#exmow111"),$("#exmow"));
    });
    function exCheck(v,t,s){
		var val = v.val();
		var $target = t;
		var $select = s;
    	if (val == "NA" || val == "" || val == "G" 
				|| val == "XC" || val == "XM") {
            $target.hide();
			$select.val("");
        } else {
            $target.show();
        }
    }
	
	function jsSelectIsExitItem(objSelect, objItemValue) { 
		var count = 0;
		for (var i = 0; i < objSelect.find('option').length; i++) { 
			if (objSelect.get(0).options[i].value == objItemValue) { 
				count += 1;
				if(count > 1 ){
					objSelect.find('option').remove(i); 
				}
			} 
		} 
		if(count == 0){			
			if(objItemValue == "XC"){
				
				objItemValue =  DOMPurify.sanitize(objItemValue);

				let htmlStr = $("<option></option>")
				.attr("value", objItemValue)
				.text("N.A.")[0]
				.outerHTML;
				htmlStr = DOMPurify.sanitize(htmlStr);
				$("#excrdType").append(htmlStr);
			
				$("#excrdType [value='"+objItemValue+"']")				
				.attr("key", objItemValue)
				.attr("showvalue", "N.A.");		
				
			} else if(objItemValue == "XM"){

			    objItemValue =  DOMPurify.sanitize(objItemValue);

				let htmlStr = $("<option></option>")
				.attr("value", objItemValue)
				.text("N.A.")[0]
				.outerHTML;
				htmlStr = DOMPurify.sanitize(htmlStr);
				$("#excrdTypeMow").append(htmlStr);
				
				$("#excrdTypeMow [value='"+objItemValue+"']")
				.attr("key", objItemValue)
				.attr("showvalue", "N.A.");		
				
			}
		}
	} 
    /*----------------------------------------- (重新引進)負責人-------------------------------------*/
    $("#_lms1705s01Button2").click(function(){
        $.ajax({
            handler: "lms1705m01formhandler",
            type: "POST",
            action: "queryCharmin",
            data: {
                mainOid: responseJSON.oid,
                mainId: responseJSON.mainId,
                dupNo: responseJSON.dupNo,
                showMsg: true,
                txCode: responseJSON.txCode
            },
		}).done(function(responseData){
			$("#L170M01aForm").injectData(responseData);
		});
    })
    
    
    /*------------------------------------------- 引主要授信戶------------------------------------------*/
    $("#_lms1705s01Button4").click(function(){
        $.ajax({
            handler: "lms1705m01formhandler",
            type: "POST",
            action: "queryMaincust",
            data: {
                mainOid: responseJSON.oid,
                mainId: responseJSON.mainId,
                dupNo: responseJSON.dupNo,
                showMsg: true,
                txCode: responseJSON.txCode
            },
		}).done(function(responseData){
			// J-111-0326 海外覆審作業系統改良第一階段：
			// 4-2. 新增 符合授信額度標準
			// 原 mLoanPerson主要戶 改 mLoanPerson符合授信額度標準、mLoanPersonA主要戶
			if (responseData.mLoanPersonA) {
			    $("input[name='mLoanPersonA'][value=" + responseData.mLoanPersonA + "]").prop("checked", true);
			}
			else {
			    $("input[name='mLoanPersonA']").removeAttr("checked");
			}
			$("#L170M01aForm").injectData(responseData);
		});
    })
    /*------------------------------------------- (重新引進)保證人-------------------------------------*/
    $("#_lms1705s01Button5").click(function(){
        $.ajax({
            handler: "lms1705m01formhandler",
            type: "POST",
            action: "queryRltGuarantor",
            data: {
                mainOid: responseJSON.oid,
                mainId: responseJSON.mainId,
                dupNo: responseJSON.dupNo,
                showMsg: true,
                txCode: responseJSON.txCode
            },
		}).done(function(responseData){
			$("#L170M01aForm").injectData(responseData);
		});	
    })
    
});//最外層;
