package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.mega.eloan.lms.mfaloan.bean.ELF508;

/**
 * <pre>
 * 天然及重大災害受災戶住宅補貼控制檔
 * </pre>
 * 
 * @since 2017/8/24
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/8/24,EL09301,new
 *          </ul>
 */
public interface MisELF508Service {
	public ELF508 findByPk(String ELF508_CUST_ID, String ELF508_DISAS_TYPE,String ELF508_HOLD_NO,String ELF508_OWNER_ID);
	
	public List<ELF508> findByCustidDupno(String ELF508_CUST_ID, String dupNo);
	public List<ELF508> findByCntrNo(String cntrNo);

	public void deleteELF508ByPk(String ELF508_CUST_ID, String ELF508_DISAS_TYPE,String ELF508_HOLD_NO,String ELF508_OWNER_ID);
	public void insertELF508(String ELF508_BRNO, String ELF508_CUST_ID, String ELF508_DISAS_TYPE, String ELF508_HOLD_NO, String ELF508_OWNER_ID, 
			String ELF508_CNTRNO, String ELF508_LOAN_TYPE, Date ELF508_APP_DATE, BigDecimal ELF508_APP_AMT, String ELF508_HOUSE_ADR, String ELF508_OWNER_NM,
			String ELF508_OWNSP_ID, String ELF508_OWNSP_NM, String ELF508_SPOUSE_ID, String ELF508_SPOUSE_NM, String ELF508_COLL_LN, String ELF508_COLL_BN, 
			String ELF508_COLL_ADDR, String ELF508_SET_HOLD, Date ELF508_CANCEL_DATE, Timestamp ELF508_ELOAN_DATE, Timestamp ELF508_ALOAN_DATE);
}
