package com.mega.eloan.lms.batch.service.impl;

import java.io.File;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.WiseNewsService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.report.CLS1131R05RptService;
import com.mega.eloan.lms.cls.report.CLS1131R06RptService;
import com.mega.eloan.lms.cls.report.CLS1131R10RptService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.dao.C120S01SDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120S01S;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C160M02A;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Workbook;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * J-112-0390 Web e-Loan中鋼集團消貸程式優化(批次)
 * 
 */
@Service("clsBatchChinaSteelCreditServiceImpl")
public class ClsBatchChinaSteelCreditServiceImpl extends AbstractCapService
		implements WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	CLS1161Service cls1161Service;

	@Resource
	CLS1131Service cls1131Service;

	@Resource
	DocFileService docFileService;

	@Resource
	CLSService clsService;

	@Resource
	C120S01SDao c120s01sDao;

	@Resource
	CLS1220Service cls1220Service;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	LMSService lmsService;

	@Resource
	RPAProcessService rpaProcessService;

	@Resource
	CLS1131R05RptService cls1131R05RptService;

	@Resource
	CLS1131R06RptService cls1131R06RptService;
	
	@Resource
	CLS1131R10RptService cls1131R10RptService;
	
	@Resource
	CLS3401Service cls3401Service;
	
	@Resource
	WiseNewsService wiseNewsService;
	

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		boolean isSuccess = false;
		JSONObject result = null;
		JSONObject rq = json.getJSONObject("request");

		String act = rq.optString("act");
		String msg = "";

		try {
			if (Util.equals("gotoRPAJobs_FamilyAnnouncement", act)) { // SLMS-00157
				// J-112-0390 中鋼消貸自動發查家事RPA
				String ploanPlan = Util.trim(rq.optString("ploanPlan"));
				String begTs = Util.trim(rq.optString("begTs"));//
				ploan_gotoRPAJobs_FamilyAnnouncement(ploanPlan, begTs);
				
				//多查負面資訊
				queryWiseNews(ploanPlan, begTs);
			} else if (Util.equals("cls_import_CSGExcel_step1", act)) { // SLMS-00186
				// 匯入中鋼產信評的EXCEL
				String c160m02a_oid = Util.trim(rq.optString("c160m02a_oid"));
				import_CSGExcel_step1(c160m02a_oid);
			} else if (Util.equals("cls_CSG_CallOuterSys", act)) { // SLMS-00187
				// 1.中鋼消貸發查外部系統
				// 2.中鋼消貸徵信整批更新外部系統查詢結果
				String begTs = Util.trim(rq.optString("begTs"));//
				if (Util.isEmpty(begTs)) {
					begTs = "2024-10-01";
				}
				int batch_cnt = Util.parseInt(rq.optString("batch_cnt"));
				if (batch_cnt == 0) {
					batch_cnt = 150;
				}
				String outerSys = Util.trim(rq.optString("outerSys"));
				// J-113-0083 改成查完直接可以直接跑更新EXCEL(本來是SLMS-00188)
				String act_step = Util.trim(rq.optString("act_step"));
				String c160m02a_oid = Util.trim(rq.optString("c160m02a_oid"));

				// J-113-0083 改成查完直接可以直接跑更新EXCEL(本來是SLMS-00188)
				boolean run_act01 = false;
				boolean run_act02 = false;
				if(Util.equals(act_step, "act01")){
					run_act01 = true;
				}else if(Util.equals(act_step, "act02")){
					run_act02 = true;
				}else if(Util.equals(act_step, "act0102")){
					run_act01 = true;
					run_act02 = true;
				}
				//===========
				if(run_act01){ //中鋼消貸發查外部系統
					call_outerSys(begTs, batch_cnt, outerSys);
				}
				if(run_act02){ // 中鋼消貸徵信整批更新外部系統查詢結果
					update_CSGExcel_outerSys_Result(begTs, c160m02a_oid);
				}
				//
//			} else if (Util.equals("cls_CSG_UpdateCredit_OuterSysResult", act)) {// SLMS-00188
//																					// 中鋼消貸徵信整批更新外部系統查詢結果
//				String begTs = Util.trim(rq.optString("begTs"));//
//				String c160m02a_oid = Util.trim(rq.optString("c160m02a_oid"));
//				update_CSGExcel_outerSys_Result(begTs, c160m02a_oid);
			} else if (Util.equals("ctrBegDateGotoRPAJobs_FamilyAnnouncement", act)){// SLMS-00188 (本來是用來 [中鋼消貸徵信整批更新外部系統查詢結果]) 
				// J-113-0083 中鋼消貸於撥款日自動發查家事RPA
				String ctrBegDate = Util.trim(rq.optString("ctrBegDate"));//授信起日(指定重查家事才需要給)
				if(Util.isEmpty(ctrBegDate)){
					ctrBegDate = CapDate.getCurrentDate("yyyy-MM-dd");
				}
				ctrBegDate_gotoRPAJobs_FamilyAnnouncement(ctrBegDate);
			}
			isSuccess = true;
		} catch (Throwable e) {
			msg = e.getMessage();
			logger.error(msg, e);
			isSuccess = false;
		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;// 此json object 內已包含 SUCCESS
			result.element(WebBatchCode.P_RESPONSE,
					result.get(WebBatchCode.P_RC_MSG));
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, msg);
			result.element(WebBatchCode.P_RESPONSE, msg);
		}

		return result;
	}

	private void ploan_gotoRPAJobs_FamilyAnnouncement(String ploanPlan,
			String begTs) throws CapException {
		String startTime = "2024-10-01 00:00:00";
		String[] planArr = null;
		if (Util.isEmpty(ploanPlan)) {
			logger.info("SLMS-00157 ploanPlan is empty");
			return;
		} else {
			planArr = ploanPlan.split(",");
		}
		List<C122M01A> needSendFaRpaList = cls1220Service
				.findPloanCSCNeedSendFARpaList(Util.isEmpty(begTs) ? startTime
						: begTs, planArr);

		for (C122M01A c122m01a : needSendFaRpaList) {
			// 查詢前先刪除
			rpaProcessService.deleteBeforeQueryData(c122m01a.getMainId(),
					c122m01a.getCustId());
			try {
				rpaProcessService.gotoRPAJobs(new C101S04W(
						c122m01a.getMainId(), c122m01a.getCustId()));
				// 更新RPA發查時間
				c122m01a.setFaRpaJobTs(CapDate.getCurrentTimestamp());
				clsService.save(c122m01a);
			} catch (CapMessageException e) {
				logger.error("ploan_gotoRPAJobs_FamilyAnnouncement | CustId=["
						+ c122m01a.getCustId() + "]RPA Job建立失敗!", e);
			} catch (Exception e){
				logger.error("ploan_gotoRPAJobs_FamilyAnnouncement | CustId=["
						+ c122m01a.getCustId() + "]Exception=", e);
			}
		}
	}
	
	// J-113-0083 中鋼集團消貸程式優化(e-Loan)
	// 系統自動發查借款人動撥當日(即契約起日)之家事事件公告查詢(監護輔助宣告)
	private void ctrBegDate_gotoRPAJobs_FamilyAnnouncement(String ctrBegDate
			 ) throws CapException {
		String ploanCtrBegDate = "2024-10-01";
		
		List<C340M01A> needSendFaRpaList = 
			cls3401Service.findByPloanCtrBegDate_ctrTypeC(Util.isEmpty(ctrBegDate) ?
					ploanCtrBegDate	: ctrBegDate);

		for (C340M01A c340m01a : needSendFaRpaList) {
			// 查詢前先刪除
			rpaProcessService.deleteBeforeQueryData(c340m01a.getMainId(),
					c340m01a.getCustId());
			try {
				rpaProcessService.gotoRPAJobs(new C101S04W(
						c340m01a.getMainId(), c340m01a.getCustId()));
				// 更新RPA發查時間
				c340m01a.setFaRpaJobTs(CapDate.getCurrentTimestamp());
				clsService.save(c340m01a);
			} catch (CapMessageException e) {
				logger.error("ctrBegDate_gotoRPAJobs_FamilyAnnouncement | CustId=["
						+ c340m01a.getCustId() + "]，契約書編號=[" 
						+ c340m01a.getContrNumber() + "]，" 
						+ "RPA Job建立失敗!", e);
			} catch (Exception e){
				logger.error("ctrBegDate_gotoRPAJobs_FamilyAnnouncement | CustId=["
						+ c340m01a.getCustId() + "]，契約書編號=[" 
						+ c340m01a.getContrNumber() + "]，" 
						+ "Exception=", e);
			}
		}
	}
	
	private void queryWiseNews(String ploanPlan, String begTs){
		String startTime = "2024-10-01 00:00:00";
		String[] planArr = null;
		if (Util.isEmpty(ploanPlan)) {
			logger.info("SLMS-00157 queryWiseNews ploanPlan is empty");
			return;
		} else {
			planArr = ploanPlan.split(",");
		}
		List<C122M01A> needQueryWiseNewsList = cls1220Service
				.findPloanCSCNeedQueryWiseNewsList(Util.isEmpty(begTs) ? startTime
				: begTs, planArr);
		
		for (C122M01A c122m01a : needQueryWiseNewsList) {
			String custId = Util.trimSpace(c122m01a.getCustId());
			String custName =  Util.trimSpace(c122m01a.getCustName());
			String mainId =  Util.trimSpace(c122m01a.getMainId());
			try {
				JSONObject wiseNewsJson = wiseNewsService.getWiseNewsData(custName, custId, mainId);
				logger.info("queryWiseNews | CustId=["
						+ c122m01a.getCustId() + "]，[MainId]="+c122m01a.getMainId());
				byte[] pdfReport = null;
				if(wiseNewsJson != null){
					PageParameters params = new CapMvcParameters();
					params.put("isChinaSteel", "Y");
					params.put("custId", custId);
					params.put("custName", custName);
					params.put("json", wiseNewsJson.toString());
					
					logger.info("queryWiseNews | CustId=["
							+ c122m01a.getCustId() + "]，[wiseNewsJson]=" + wiseNewsJson.toString());
					
					pdfReport = cls1131R10RptService.getContent(params);
					this.saveDocFile("002", "pplication/pdf", mainId, "wiseNews",
							"wiseNews", "pdf", pdfReport, "負面新聞查詢", "9");
				}
				c122m01a.setQueryWiseNewsTs(CapDate.getCurrentTimestamp());
				clsService.save(c122m01a);
			} catch (CapMessageException e) {
				logger.error("queryWiseNews | CustId=["
						+ c122m01a.getCustId() + "]CapMessageException=", e);
			} catch (Exception e) {
				logger.error("queryWiseNews | CustId=["
						+ c122m01a.getCustId() + "]Exception=", e);
			}
		}
		
	}

	private void import_CSGExcel_step1(String c160m02a_oid)
			throws CapMessageException {
		logger.info("import_CSGExcel_step1 start|c160m02a_oid=[" + c160m02a_oid
				+ "] ");
		C160M02A c160m02a = cls1161Service.findModelByOid(C160M02A.class,
				c160m02a_oid);
		if (c160m02a == null) {
			logger.error("import_CSGExcel_step1 | c160m02a c160m02a_oid=["
					+ c160m02a_oid + "]is null ");
		} else {
			String excelId = Util.trim(c160m02a.getExcelId());
			DocFile docFile = docFileService.read(excelId);
			logger.info("import_CSGExcel_step1 | excelId=[" + excelId + "] ");
			if (docFile != null) {
				cls1161Service.setC160M02AExcelStatus(c160m02a, "01");
				WritableWorkbook workbook = null;
				String filePath = docFileService.getFilePath(docFile);

				try {
					logger.info("import_CSGExcel_step1 | filePath=[" + filePath
							+ "] ");
					workbook = Workbook.createWorkbook(new File(filePath),
							Workbook.getWorkbook(new File(filePath)));
					WritableSheet sheet = workbook.getSheet(0);
					logger.info("import_CSGExcel_step1 | send sheet to sheetcls1161Service.parseExcel] ");
					cls1161Service.parseExcel(sheet, c160m02a);
					workbook.write();
					workbook.close();
					cls1161Service.setC160M02AExcelStatus(c160m02a, "02");
				} catch (Exception e) {
					cls1161Service.setC160M02AExcelStatus(c160m02a, "03");
					logger.error(
							"cls_import_CSGExcel_step1 | parseExcel EXCEPTION!!",
							e);
					throw new CapMessageException(e, getClass());
				} finally {
					try {
						if (workbook != null) {
							workbook.close();
							workbook = null;
						}
					} catch (Exception e) {
						logger.debug("import_CSGExcel_step1 | Workbook close EXCEPTION!", getClass());
					}
				}
			} else {
				logger.error("import_CSCExcel_step1 | c160m02a c160m02a_oid=["
						+ c160m02a_oid + "]請先上傳EXCEL");
			}

		}

	}

	private void call_outerSys(String begTs, int batch_cnt, String outerSys)
			throws CapMessageException {

		int runCount = 0;
		int outerSysDataCount = 0;
		boolean runRPS = false;
		boolean runFH_EAI = false;
		
		if(Util.isEmpty(outerSys)){
			logger.info("call_outerSys--info| outerSys is empty ");
			return;
		}
		
		// 檢核是否 stop 連至外部系統
		//List<String> checkStoppedOuterSys= _verify_call_outerSys(outerSys);
		for (String sys : outerSys.split("\\|")) {
			if(Util.equals(sys, "RPS") && _verify_call_outerSys(sys)){
				runRPS = true;
				outerSysDataCount = outerSysDataCount+2;
			}
			if(Util.equals(sys, "FH_EAI") && _verify_call_outerSys(sys)){
				runFH_EAI = true;
				outerSysDataCount = outerSysDataCount+1;
			}
			
		}
			
		if(outerSysDataCount > 0){
			// 取得客戶清單以及送外部查詢紀錄
			List<Map<String, Object>> needSendOuterSysList = eloandbBASEService
					.findCSGNeedSendOuterSysData(begTs, outerSysDataCount);

			for (Map<String, Object> itemMap : needSendOuterSysList) {

				// 發查外部系統的結果如果筆對不上，就先全部刪掉之後重新查
				// type1-往來客戶信用異常資料
				// type3-婉卻紀錄資料
				// type4-證券暨期貨違約交割查詢
				List<GenericBean> deleteList = new ArrayList<GenericBean>();
				List<GenericBean> c120s01s_list = new ArrayList<GenericBean>();
				String mainId = Util
						.trim(MapUtils.getString(itemMap, "MAINID"));
				String custId = Util
						.trim(MapUtils.getString(itemMap, "CUSTID"));
				String dupNo = Util.trim(MapUtils.getString(itemMap, "DUPNO"));
				String custName = Util.trim(MapUtils.getString(itemMap,
						"CUSTNAME"));
				try {
					// 取得-往來客戶信用異常、婉卻紀錄、證券暨期貨違約交割 ---start---
					if (runRPS) {
						// 往來客戶信用異常資料 dataType=1
						Map<String, Object> map1 = cls1131Service
								.getCreditAnomalyData(custId, dupNo, custName);
						C120S01S c120s01s = new C120S01S();
						c120s01s.setMainId(mainId);
						c120s01s.setCustId(custId);
						c120s01s.setDupNo(dupNo);

						if (Util.isNotEmpty(map1)) {
							byte[] report1 = cls1131Service
									.getReportOfCreditAnomalyData(map1);
							String dataStatusType1 = (String) map1.get("dataStatus");
							setC120S01SData(c120s01s,
										ClsConstants.C101S01S_dataType.往來客戶信用異常資料,
										dataStatusType1, report1);

							c120s01s_list.add(c120s01s);
						}
					}

					if (runRPS) {
						// 婉卻紀錄資料 dataType=3
						Map<String, Object> map3 = cls1131Service
								.getRefusedData(custId, dupNo, custName);
						C120S01S c120s01s = new C120S01S();
						c120s01s.setMainId(mainId);
						c120s01s.setCustId(custId);
						c120s01s.setDupNo(dupNo);

						if (Util.isNotEmpty(map3)) {
							MegaSSOUserDetails user = MegaSSOSecurityContext
									.getUserDetails();
							PageParameters params = new CapMvcParameters();

							params.put("queryMan", user.getUserId() + " "
									+ user.getUserName());
							params.put("refuseList", map3.get("refuseList"));

							byte[] pdfReport = cls1131R05RptService
									.getContent(params);
							String dataStatusType3 = (String) map3.get("dataStatus");
							setC120S01SData(c120s01s,
										ClsConstants.C101S01S_dataType.婉卻紀錄資料,
										dataStatusType3, pdfReport);
							c120s01s_list.add(c120s01s);
						}
					}

					if (runFH_EAI) {
						// 證券暨期貨違約交割查詢 dataType=4
						Map<String, Object> map4 = cls1131Service.getCURIQ01(
								mainId, custId);
						C120S01S c120s01s = new C120S01S();
						c120s01s.setMainId(mainId);
						c120s01s.setCustId(custId);
						c120s01s.setDupNo(dupNo);
						if (Util.isNotEmpty(map4)) {
							Timestamp nowTS = CapDate.getCurrentTimestamp();
							PageParameters params = new CapMvcParameters();

							params.put("eaiCURIQ01_list", map4.get("eaiCURIQ01_list"));
							params.put(	"eaiCURIQ01_ts",
									CapDate.convertTimestampToString(nowTS,
											UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS));
							String dataStatusType4 = (String) map4.get("dataStatus");

							byte[] pdfReport = cls1131R06RptService.getContent(params);
							setC120S01SData(c120s01s,
									ClsConstants.C101S01S_dataType.證券暨期貨違約交割紀錄,
									dataStatusType4, pdfReport);

							c120s01s_list.add(c120s01s);
						}
					}
				} catch (Exception e) {
					logger.error("call_outerSys error| mainId=[" + mainId
							+ "],custId=[" + custId + "]");
					logger.error(e.getMessage(), e);
				}
				// 取得-往來客戶信用異常、婉卻紀錄、證券暨期貨違約交割 ---end---
				deleteList.addAll(c120s01sDao.findByMainIdAndCustIdAndDupNo(
						mainId, custId, dupNo));
				cls1131Service.deleteByJPQL(deleteList);
				cls1131Service.save(c120s01s_list);
				runCount++;
				if (runCount == batch_cnt) {
					break;
				}
			}
		}

	}

	private void update_CSGExcel_outerSys_Result(String begTs,
			String c160m02a_oid) throws CapMessageException {
		logger.info("update_CSGExcel_outerSys_Result start");
		List<C160M02A> c160m02aList = new ArrayList<C160M02A>();

		if (Util.isNotEmpty(c160m02a_oid)) {// 指定執行案件
			logger.info("update_CSGExcel_outerSys_Result | 更新指定案件 ");
			C160M02A c160m02a = cls1161Service.findModelByOid(C160M02A.class,
					c160m02a_oid);
			if (c160m02a == null) {
				logger.error("update_CSGExcel_outerSys_Result | c160m02a c160m02a_oid=["
						+ c160m02a_oid + "]is null ");
			} else if (Util.equals(c160m02a.getImportExcelStatus(), "2")) {
				c160m02aList.add(c160m02a);
			}
		} else {
			if (Util.isEmpty(begTs)) {
				begTs = "2024-10-01";
			}
			// 取得尚未完成更新的案件
			c160m02aList = cls1161Service
					.findC160m02aByCreateTimeAndImportStatus(begTs, "02");
		}

		for (C160M02A c160m02a : c160m02aList) {
			logger.info("update_CSGExcel_outerSys_Result | c160m02a_oid=["
					+ c160m02a.getOid() + "] ");
			String excelId = Util.trim(c160m02a.getExcelId());
			DocFile docFile = docFileService.read(excelId);
			if (docFile != null) {
				WritableWorkbook workbook = null;
				String filePath = docFileService.getFilePath(docFile);
				logger.info("update_CSGExcel_outerSys_Result | filePath=["
						+ filePath + "] ");
				try {

					workbook = Workbook.createWorkbook(new File(filePath),
							Workbook.getWorkbook(new File(filePath)));
					WritableSheet sheet = workbook.getSheet(0);
					logger.info("update_CSGExcel_outerSys_Result | send to cls1161Service.updateExcel");
					cls1161Service.updateExcel(sheet, c160m02a);
					workbook.write();
					workbook.close();
				} catch (Exception e) {
					cls1161Service.setC160M02AExcelStatus(c160m02a, "05");
					logger.error("update_CSGExcel_outerSys_Result | updateExcel EXCEPTION!!",
							e);
					throw new CapMessageException(e, getClass());
				} finally {
					try {
						if (workbook != null) {
							workbook.close();
							workbook = null;
						}
					} catch (Exception e) {
						logger.debug("update_CSGExcel_outerSys_Result | Workbook close EXCEPTION!", getClass());
					}
				}
			} else {
				logger.error("update_CSGExcel_outerSys_Result | c160m02a c160m02a_oid=["
						+ c160m02a.getOid() + "]尚未上傳EXCEL");
			}
		}
	}

	private boolean _verify_call_outerSys(String paramArr)
			throws CapMessageException {

		Map<String, String> map = clsService
				.get_codeTypeWithOrder("J-108-0277_stop_call");
		List<String> errMsg = new ArrayList<String>();

		for (String param : paramArr.split("\\|")) {
			if (!map.containsKey(param)) {
				continue;
			}
			String desc = Util.trim(map.get(param));
			if (Util.isNotEmpty(desc)) {
				errMsg.add(desc);
			}
		}

		if (errMsg.size() > 0) {
			return false;	
		}
		return true;
	}

	private void setC120S01SData(C120S01S c120s01s, String dataType,
			String dataStatus, byte[] loanCreditDesFile) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp currentDateTime = CapDate.getCurrentTimestamp();

		c120s01s.setDataType(dataType);
		c120s01s.setDataStatus(dataStatus);
		c120s01s.setReportFile(loanCreditDesFile);
		c120s01s.setFileSeq("1");
		c120s01s.setCreateTime(currentDateTime);
		c120s01s.setUpdater(user.getUserId());
		c120s01s.setUpdateTime(currentDateTime);
		c120s01s.setDataCreateTime(currentDateTime);
		c120s01s.setReportFileType("J");

	}
	
	/**
	 * DocFile存檔
	 * @param meta
	 * @param bytes
	 * @param fieldId
	 * @param contentType
	 * @param attchFileName
	 * @param attchFileExt
	 * @param attchMemo
	 * @param flag
	 * @return
	 */
	private DocFile saveDocFile(String branchId,String contentType,String mainId,
			String fieldId, String attchFileName, String attchFileExt, byte[] bytes,
			String attchMemo, String flag) {
		DocFile docFile = new DocFile();
		docFile.setBranchId(branchId);
		docFile.setContentType(contentType);
		docFile.setMainId(mainId);
		docFile.setPid(null);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(fieldId);
		docFile.setDeletedTime(null);
		docFile.setSrcFileName(attchFileName + "." + attchFileExt);
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId("LMS");
		docFile.setFileSize(bytes.length);
		docFile.setData(bytes);
		docFile.setFileDesc(Util.isNotEmpty(attchMemo) ? attchMemo: attchFileName);
		docFile.setFlag(flag);
		docFileService.save(docFile);
		return docFile;
	}

}
