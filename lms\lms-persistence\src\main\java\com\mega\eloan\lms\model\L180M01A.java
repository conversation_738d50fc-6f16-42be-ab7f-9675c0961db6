/* 
 * L180M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.model;

import java.util.Date;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 覆審名單主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L180M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L180M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l180m01a", fetch = FetchType.LAZY)
	private Set<L180A01A> l180a01a;

	public Set<L180A01A> getL180a01a() {
		return l180a01a;
	}

	public void setL180a01a(Set<L180A01A> l180m01a) {
		this.l180a01a = l180m01a;
	}

	/**
	 * 資料年月
	 * <p/>
	 * YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "DATADATE", columnDefinition = "DATE")
	private Date dataDate;

	/** 分行 **/
	@Column(name = "BRANCHID", length = 3, columnDefinition = "CHAR(3)")
	private String branchId;

	/**
	 * 覆審批號
	 * <p/>
	 * ※預設值「null」，於執行「重新產生批號」時與「明細檔_覆審序號」一起給號，依「資料年月(dataDate)的年度」與「分行(branchId)
	 * 」<br/>
	 * max(batchNO)+1<br/>
	 * 顯示格式：001
	 */
	@Column(name = "BATCHNO", columnDefinition = "DECIMAL(3,0)")
	private Integer batchNO;

	/** 名單產生日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "GENERATEDATE", columnDefinition = "DATE")
	private Date generateDate;

	/**
	 * 產生方式
	 * <p/>
	 * 1.每月產生(預約單)<br/>
	 * 2.指定年月查詢(手動)
	 */
	@Column(name = "CREATEBY", length = 1, columnDefinition = "CHAR(1)")
	private String createBy;

	/** 預計覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DEFAULTCTLDATE", columnDefinition = "DATE")
	private Date defaultCTLDate;

	/** 覆審名單EXCEL檔檔案位置 **/
	@Column(name = "EXCELFILE", length = 40, columnDefinition = "VARCHAR(40)")
	private String excelFile;

	/** 覆審名單驗證EXCEL檔檔案位置 **/
	@Column(name = "CHKEXCELFILE", length = 40, columnDefinition = "VARCHAR(40)")
	private String chkExcelFile;

	/** 經辦 **/
	@Column(name = "APPRID", length = 6, columnDefinition = "CHAR(6)")
	private String apprId;

	/** 授信主管 **/
	@Column(name = "BOSSID", length = 6, columnDefinition = "CHAR(6)")
	private String bossId;

	/**
	 * RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;

	/** RPA執行日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "RPADATE", columnDefinition = "DATE")
	private Date rpaDate;

	/**
	 * 處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 */
	@Size(max = 3)
	@Column(name = "STATUS", length = 3, columnDefinition = "CHAR(3)")
	private String status;

	public L180M01A(String branchId, Integer batchNO) {
		this.branchId = branchId;
		this.batchNO = batchNO;
	}

	public L180M01A() {
	}

	/**
	 * 取得資料年月
	 * <p/>
	 * YYYY-MM-01
	 */
	public Date getDataDate() {
		return this.dataDate;
	}

	/**
	 * 設定資料年月
	 * <p/>
	 * YYYY-MM-01
	 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 取得分行 **/
	public String getBranchId() {
		return this.branchId;
	}

	/** 設定分行 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/**
	 * 取得覆審批號
	 * <p/>
	 * ※預設值「null」，於執行「重新產生批號」時與「明細檔_覆審序號」一起給號，依「資料年月(dataDate)的年度」與「分行(branchId)
	 * 」<br/>
	 * max(batchNO)+1<br/>
	 * 顯示格式：001
	 */
	public Integer getBatchNO() {
		return this.batchNO;
	}

	/**
	 * 設定覆審批號
	 * <p/>
	 * ※預設值「null」，於執行「重新產生批號」時與「明細檔_覆審序號」一起給號，依「資料年月(dataDate)的年度」與「分行(branchId)
	 * 」<br/>
	 * max(batchNO)+1<br/>
	 * 顯示格式：001
	 **/
	public void setBatchNO(Integer value) {
		this.batchNO = value;
	}

	/** 取得名單產生日期 **/
	public Date getGenerateDate() {
		return this.generateDate;
	}

	/** 設定名單產生日期 **/
	public void setGenerateDate(Date value) {
		this.generateDate = value;
	}

	/**
	 * 取得產生方式
	 * <p/>
	 * 1.每月產生(預約單)<br/>
	 * 2.指定年月查詢(手動)
	 */
	public String getCreateBy() {
		return this.createBy;
	}

	/**
	 * 設定產生方式
	 * <p/>
	 * 1.每月產生(預約單)<br/>
	 * 2.指定年月查詢(手動)
	 **/
	public void setCreateBy(String value) {
		this.createBy = value;
	}

	/** 取得預計覆審日 **/
	public Date getDefaultCTLDate() {
		return this.defaultCTLDate;
	}

	/** 設定預計覆審日 **/
	public void setDefaultCTLDate(Date value) {
		this.defaultCTLDate = value;
	}

	/** 取得覆審名單EXCEL檔檔案位置 **/
	public String getExcelFile() {
		return this.excelFile;
	}

	/** 設定覆審名單EXCEL檔檔案位置 **/
	public void setExcelFile(String value) {
		this.excelFile = value;
	}

	/** 取得覆審名單驗證EXCEL檔檔案位置 **/
	public String getChkExcelFile() {
		return this.chkExcelFile;
	}

	/** 設定覆審名單驗證EXCEL檔檔案位置 **/
	public void setChkExcelFile(String value) {
		this.chkExcelFile = value;
	}

	/** 取得經辦 **/
	public String getApprId() {
		return this.apprId;
	}

	/** 設定經辦 **/
	public void setApprId(String value) {
		this.apprId = value;
	}

	/** 取得授信主管 **/
	public String getBossId() {
		return this.bossId;
	}

	/** 設定授信主管 **/
	public void setBossId(String value) {
		this.bossId = value;
	}

	/**
	 * 取得RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}

	/**
	 * 設定RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}

	/**
	 * 設定RPADATE
	 * <p/>
	 * RPA執行日期
	 **/
	public void setRpaDate(Date rpaDate) {
		this.rpaDate = rpaDate;
	}

	/**
	 * 取得RPADATE
	 * <p/>
	 * RPA執行日期
	 */
	public Date getRpaDate() {
		return rpaDate;
	}

	/**
	 * 取得處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 */
	public String getStatus() {
		return this.status;
	}

	/**
	 * 設定處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 **/
	public void setStatus(String value) {
		this.status = value;
	}
}
