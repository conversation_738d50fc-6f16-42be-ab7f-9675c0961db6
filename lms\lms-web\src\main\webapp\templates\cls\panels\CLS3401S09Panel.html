<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        		<script type="text/javascript">
					loadScript('pagejs/cls/CLS3401S09Panel');
				</script>
	        <fieldset>
				<legend id="leg1"><th:block th:text="#{'tab.09'}"><!-- 附加檔案--></th:block></legend>
				<table width="100%" border="0">
	            <tr>
	                <td colspan="2">
	                    <div class="funcContainer">
							<div id="filehide0">
	                        <button type="button" id="uploadFile">
	                            <span class="text-only"><th:block th:text="#{'tab09.bt01'}"><!-- 選擇附加檔案--></th:block></span>
	                        </button>
	                        <button type="button" id="deleteFile">
	                            <span class="text-only"><th:block th:text="#{'tab09.bt02'}"><!-- 刪除--></th:block></span>
	                        </button>
							</div>
							<div id="gridfile"></div>
	                    </div>
	                </td>
	            </tr>
	        </table>
		</fieldset>	
        </th:block>
    </body>
</html>