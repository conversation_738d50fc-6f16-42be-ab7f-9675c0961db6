package com.mega.eloan.lms.lms.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 配偶資料
 * ⇒ 原始參照 LMS1115S02PanelC4
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
public class LMS1035S02PanelC4 extends Panel {
	private static final long serialVersionUID = 1L;

	public LMS1035S02PanelC4(String id) {
		super(id);
	}

	public LMS1035S02PanelC4(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}
	
	@Override
	protected String getViewName() {
		return getEloanPagePathByClass(getClass());
	}
}
