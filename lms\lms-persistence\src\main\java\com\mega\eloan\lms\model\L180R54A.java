/* 
 * L180R54A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 企金兆元振興融資方案月檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L180R54A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L180R54A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 額度明細表MAINID **/
	@Size(max = 32)
	@Column(name = "CNTRMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String cntrMainId;

	/** 簽案日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "Date")
	private Date caseDate;

	/**
	 * 核准日期
	 * <p/>
	 * NOT NULL
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE", columnDefinition = "Date")
	private Date endDate;

	/**
	 * 企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	@Size(max = 1)
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String docType;

	/**
	 * 授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 */
	@Size(max = 1)
	@Column(name = "DOCKIND", length = 1, columnDefinition = "CHAR(1)")
	private String docKind;

	/**
	 * 案件別
	 * <p/>
	 * 1一般<br/>
	 * 2其他<br/>
	 * 3陳復/陳述案<br/>
	 * 4異常通報<br/>
	 * 5團貸案件 (※國內個金案件)
	 */
	@Size(max = 1)
	@Column(name = "DOCCODE", length = 1, columnDefinition = "CHAR(1)")
	private String docCode;

	/**
	 * 區部別
	 * <p/>
	 * 無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)<br/>
	 * (額度明細表)
	 */
	@Size(max = 1)
	@Column(name = "TYPCD", length = 1, columnDefinition = "CHAR(1)")
	private String typCd;

	/**
	 * 客戶統編
	 * <p/>
	 * (額度明細表)
	 */
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/**
	 * 重覆序號
	 * <p/>
	 * (額度明細表)
	 */
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 客戶名稱
	 * <p/>
	 * (額度明細表)
	 */
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/** 簽案分行 **/
	@Size(max = 3)
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/** 案件號碼 **/
	@Size(max = 62)
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/**
	 * 案件審核層級
	 * <p/>
	 * 1　常董會權限<br/>
	 * 2　常董會權限簽奉總經理核批<br/>
	 * 3　常董會權限簽准由副總經理核批<br/>
	 * 4　利費率變更案件由總處經理核定<br/>
	 * 5　屬常董會授權總經理逕核案件<br/>
	 * 6　總經理權限內<br/>
	 * 7　副總經理權限<br/>
	 * 8　授管處處長權限<br/>
	 * 9　其他<br/>
	 * A　董事會權限<br/>
	 * B　區域營運中心營運長/副營運長權限<br/>
	 * C　利費率變更案件由董事長核定<br/>
	 * D　個金處經理權
	 */
	@Size(max = 2)
	@Column(name = "CASELVL", length = 2, columnDefinition = "CHAR(2)")
	private String caseLvl;

	/**
	 * 額度序號
	 * <p/>
	 * (額度明細表)
	 */
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/**
	 * 額度分行
	 * <p/>
	 * (額度明細表)
	 */
	@Size(max = 3)
	@Column(name = "CNTRBRID", length = 3, columnDefinition = "CHAR(3)")
	private String cntrBrId;

	/**
	 * 性質
	 * <p/>
	 * 新做|1<br/>
	 * 續約|2<br/>
	 * 變更條件|3<br/>
	 * 流用|4<br/>
	 * 增額|5<br/>
	 * 減額|6<br/>
	 * 不變|7<br/>
	 * 取消|8<br/>
	 * 展期(不良授信案)|9<br/>
	 * 紓困|10<br/>
	 * 提前續約|11<br/>
	 * 協議清償|12<br/>
	 * 報價 | 13
	 */
	@Size(max = 30)
	@Column(name = "PROPERTY", length = 30, columnDefinition = "VARCHAR(30)")
	private String property;

	/**
	 * 額度性質
	 * <p/>
	 * S:擔保<br/>
	 * N:無擔保/信保
	 */
	@Size(max = 1)
	@Column(name = "SBJPROPERTY", length = 1, columnDefinition = "CHAR(1)")
	private String sbjProperty;

	/**
	 * 本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 */
	@Size(max = 1)
	@Column(name = "HEADITEM1", length = 1, columnDefinition = "CHAR(1)")
	private String headItem1;

	/**
	 * 借款人是否為中小企業
	 * <p/>
	 * Y/N（是/否）
	 */
	@Size(max = 1)
	@Column(name = "HEADITEM2", length = 1, columnDefinition = "CHAR(1)")
	private String headItem2;

	/**
	 * 是否符合送信保基金保證條件
	 * <p/>
	 * Y/N（是/否）
	 */
	@Size(max = 1)
	@Column(name = "HEADITEM3", length = 1, columnDefinition = "CHAR(1)")
	private String headItem3;

	/** 前准額度－幣別 **/
	@Size(max = 3)
	@Column(name = "LV2CURR", length = 3, columnDefinition = "CHAR(3)")
	private String LV2Curr;

	/** 前准額度－金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LV2AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LV2Amt;

	/** 現請額度－幣別 **/
	@Size(max = 3)
	@Column(name = "CURRENTAPPLYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String currentApplyCurr;

	/** 現請額度－金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CURRENTAPPLYAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal currentApplyAmt;

	/** 新作、增額合計幣別 **/
	@Size(max = 3)
	@Column(name = "INCAPPLYTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String incApplyTotCurr;

	/** 新作、增額合計金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "INCAPPLYTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal incApplyTotAmt;

	/** 新作、增額擔保額度合計幣別 **/
	@Size(max = 3)
	@Column(name = "INCASSTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String incAssTotCurr;

	/** 新作、增額擔保額度合計金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "INCASSTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal incAssTotAmt;

	/** 授信額度合計幣別 **/
	@Size(max = 3)
	@Column(name = "LOANTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LoanTotCurr;

	/** 授信額度合計金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LOANTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LoanTotAmt;

	/** 擔保授信額度合計幣別 **/
	@Size(max = 3)
	@Column(name = "ASSURETOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String assureTotCurr;

	/** 擔保授信額度合計金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "ASSURETOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal assureTotAmt;

	/**
	 * 企業規模
	 * <p/>
	 * 民營大企業<br/>
	 * 私人<br/>
	 * --<br/>
	 * 民營中小企業<br/>
	 * 公營事業<br/>
	 * 政府機關<br/>
	 * 非營利團體<br/>
	 * 視同中小企業
	 */
	@Size(max = 1)
	@Column(name = "CLTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String cltType;

	/** 行業對象別 **/
	@Size(max = 6)
	@Column(name = "BUSCODE", length = 6, columnDefinition = "VARCHAR(6)")
	private String busCode;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 刪除註記 **/
	@Column(name = "DELETEDTIME", columnDefinition = "TIMESTAMP")
	private Timestamp deletedTime;

	/**
	 * 匯率
	 * <p/>
	 * 新作、增額合計幣別的匯率
	 */
	@Digits(integer = 9, fraction = 5, groups = Check.class)
	@Column(name = "ENDRATE", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal endRate;

	/**
	 * 匯率日期
	 * <p/>
	 * 產生資料的當天
	 */
	@Size(max = 10)
	@Column(name = "RATEYMD", length = 10, columnDefinition = "CHAR(10)")
	private String rateYmd;

	/**
	 * 是否為新客戶
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "ISNEW", length = 1, columnDefinition = "CHAR(1)")
	private String isNew;

	/** 產品種類 **/
	@Size(max = 2)
	@Column(name = "LNTYPE", length = 2, columnDefinition = "CHAR(2)")
	private String lnType;

	/** 專案種類 **/
	@Size(max = 2)
	@Column(name = "PROJCLASS", length = 2, columnDefinition = "CHAR(2)")
	private String projClass;

	/** 授信科目 **/
	@Size(max = 1536)
	@Column(name = "LNSUBJECT", length = 1536, columnDefinition = "VARCHAR(1536)")
	private String lnSubject;

	/** 本案是否屬兆元振興融資方案 **/
	@Size(max = 1)
	@Column(name = "ISREVIVE", length = 1, columnDefinition = "CHAR(1)")
	private String isRevive;

	/** 貸款對象類別 **/
	@Size(max = 2)
	@Column(name = "REVIVETARGET", length = 2, columnDefinition = "CHAR(2)")
	private String reviveTarget;

	/** 六大核心產業 **/
	@Size(max = 2)
	@Column(name = "REVIVECOREINDUSTRY", length = 2, columnDefinition = "CHAR(2)")
	private String reviveCoreIndustry;

	/** 國際鏈結布局 **/
	@Size(max = 2)
	@Column(name = "REVIVECHAIN", length = 2, columnDefinition = "VARCHAR(2)")
	private String reviveChain;

	/** 貸款用途 **/
	@Size(max = 2)
	@Column(name = "REVIVELOANPURPOSE", length = 2, columnDefinition = "VARCHAR(2)")
	private String reviveLoanPurpose;

	/**
	 * 現請額度幣別匯率
	 * <p/>
	 * 產生資料的當天
	 */
	@Digits(integer = 9, fraction = 5, groups = Check.class)
	@Column(name = "CURRENDRATE", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal currEndRate;

	/** 前准額度幣別匯率 **/
	@Digits(integer = 9, fraction = 5, groups = Check.class)
	@Column(name = "LV2RATE", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal lv2Rate;

	/** 增額註記 **/
	@Size(max = 1)
	@Column(name = "INCREASEFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String increaseFlag;

	/** 符合報送註記 **/
	@Size(max = 1)
	@Column(name = "REPORTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String reportFlag;

	/**
	 * 報送金額
	 * <p/>
	 * increaseFlag = Y則現請額度-前准額度，否則為現請額度
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "REPORTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal reportAmt;

	/** 是否有共用 **/
	@Size(max = 1)
	@Column(name = "HASCOMM", length = 1, columnDefinition = "CHAR(1)")
	private String hasComm;

	/** 共用額度序號 **/
	@Size(max = 12)
	@Column(name = "COMMSNO", length = 1, columnDefinition = "CHAR(12)")
	private String commSno;

	/**
	 * 計入兆元振興額度
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "REVIVEAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal reviveAmt;

	/** 額度控管種類 **/
	@Size(max = 2)
	@Column(name = "SNOKIND", length = 1, columnDefinition = "CHAR(2)")
	private String snoKind;

	/** 循環註記 **/
	@Size(max = 1)
	@Column(name = "REUSE", length = 1, columnDefinition = "CHAR(1)")
	private String reUse;

	/** 符合報送註記113項 **/
	@Size(max = 1)
	@Column(name = "REPORTFLAG_113", length = 1, columnDefinition = "CHAR(1)")
	private String reportFlag_113;

	/** 符合報送註記113項不循環 **/
	@Size(max = 1)
	@Column(name = "REPORTFLAG_113_REUSE", length = 1, columnDefinition = "CHAR(1)")
	private String reportFlag_113_reUse;

	/** 符合報送註記517項 **/
	@Size(max = 1)
	@Column(name = "REPORTFLAG_517", length = 1, columnDefinition = "CHAR(1)")
	private String reportFlag_517;

	/** 符合報送註記517項不循環 **/
	@Size(max = 1)
	@Column(name = "REPORTFLAG_517_REUSE", length = 1, columnDefinition = "CHAR(1)")
	private String reportFlag_517_reUse;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得額度明細表MAINID **/
	public String getCntrMainId() {
		return this.cntrMainId;
	}

	/** 設定額度明細表MAINID **/
	public void setCntrMainId(String value) {
		this.cntrMainId = value;
	}

	/** 取得簽案日期 **/
	public Date getCaseDate() {
		return this.caseDate;
	}

	/** 設定簽案日期 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/**
	 * 取得核准日期
	 * <p/>
	 * NOT NULL
	 */
	public Date getEndDate() {
		return this.endDate;
	}

	/**
	 * 設定核准日期
	 * <p/>
	 * NOT NULL
	 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/**
	 * 取得企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	public String getDocType() {
		return this.docType;
	}

	/**
	 * 設定企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/**
	 * 取得授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 */
	public String getDocKind() {
		return this.docKind;
	}

	/**
	 * 設定授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 **/
	public void setDocKind(String value) {
		this.docKind = value;
	}

	/**
	 * 取得案件別
	 * <p/>
	 * 1一般<br/>
	 * 2其他<br/>
	 * 3陳復/陳述案<br/>
	 * 4異常通報<br/>
	 * 5團貸案件 (※國內個金案件)
	 */
	public String getDocCode() {
		return this.docCode;
	}

	/**
	 * 設定案件別
	 * <p/>
	 * 1一般<br/>
	 * 2其他<br/>
	 * 3陳復/陳述案<br/>
	 * 4異常通報<br/>
	 * 5團貸案件 (※國內個金案件)
	 **/
	public void setDocCode(String value) {
		this.docCode = value;
	}

	/**
	 * 取得區部別
	 * <p/>
	 * 無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)<br/>
	 * (額度明細表)
	 */
	public String getTypCd() {
		return this.typCd;
	}

	/**
	 * 設定區部別
	 * <p/>
	 * 無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)<br/>
	 * (額度明細表)
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/**
	 * 取得客戶統編
	 * <p/>
	 * (額度明細表)
	 */
	public String getCustId() {
		return this.custId;
	}

	/**
	 * 設定客戶統編
	 * <p/>
	 * (額度明細表)
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/**
	 * 取得重覆序號
	 * <p/>
	 * (額度明細表)
	 */
	public String getDupNo() {
		return this.dupNo;
	}

	/**
	 * 設定重覆序號
	 * <p/>
	 * (額度明細表)
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得客戶名稱
	 * <p/>
	 * (額度明細表)
	 */
	public String getCustName() {
		return this.custName;
	}

	/**
	 * 設定客戶名稱
	 * <p/>
	 * (額度明細表)
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得簽案分行 **/
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/** 設定簽案分行 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/** 取得案件號碼 **/
	public String getCaseNo() {
		return this.caseNo;
	}

	/** 設定案件號碼 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/**
	 * 取得案件審核層級
	 * <p/>
	 * 1　常董會權限<br/>
	 * 2　常董會權限簽奉總經理核批<br/>
	 * 3　常董會權限簽准由副總經理核批<br/>
	 * 4　利費率變更案件由總處經理核定<br/>
	 * 5　屬常董會授權總經理逕核案件<br/>
	 * 6　總經理權限內<br/>
	 * 7　副總經理權限<br/>
	 * 8　授管處處長權限<br/>
	 * 9　其他<br/>
	 * A　董事會權限<br/>
	 * B　區域營運中心營運長/副營運長權限<br/>
	 * C　利費率變更案件由董事長核定<br/>
	 * D　個金處經理權
	 */
	public String getCaseLvl() {
		return this.caseLvl;
	}

	/**
	 * 設定案件審核層級
	 * <p/>
	 * 1　常董會權限<br/>
	 * 2　常董會權限簽奉總經理核批<br/>
	 * 3　常董會權限簽准由副總經理核批<br/>
	 * 4　利費率變更案件由總處經理核定<br/>
	 * 5　屬常董會授權總經理逕核案件<br/>
	 * 6　總經理權限內<br/>
	 * 7　副總經理權限<br/>
	 * 8　授管處處長權限<br/>
	 * 9　其他<br/>
	 * A　董事會權限<br/>
	 * B　區域營運中心營運長/副營運長權限<br/>
	 * C　利費率變更案件由董事長核定<br/>
	 * D　個金處經理權
	 **/
	public void setCaseLvl(String value) {
		this.caseLvl = value;
	}

	/**
	 * 取得額度序號
	 * <p/>
	 * (額度明細表)
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}

	/**
	 * 設定額度序號
	 * <p/>
	 * (額度明細表)
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/**
	 * 取得額度分行
	 * <p/>
	 * (額度明細表)
	 */
	public String getCntrBrId() {
		return this.cntrBrId;
	}

	/**
	 * 設定額度分行
	 * <p/>
	 * (額度明細表)
	 **/
	public void setCntrBrId(String value) {
		this.cntrBrId = value;
	}

	/**
	 * 取得性質
	 * <p/>
	 * 新做|1<br/>
	 * 續約|2<br/>
	 * 變更條件|3<br/>
	 * 流用|4<br/>
	 * 增額|5<br/>
	 * 減額|6<br/>
	 * 不變|7<br/>
	 * 取消|8<br/>
	 * 展期(不良授信案)|9<br/>
	 * 紓困|10<br/>
	 * 提前續約|11<br/>
	 * 協議清償|12<br/>
	 * 報價 | 13
	 */
	public String getProperty() {
		return this.property;
	}

	/**
	 * 設定性質
	 * <p/>
	 * 新做|1<br/>
	 * 續約|2<br/>
	 * 變更條件|3<br/>
	 * 流用|4<br/>
	 * 增額|5<br/>
	 * 減額|6<br/>
	 * 不變|7<br/>
	 * 取消|8<br/>
	 * 展期(不良授信案)|9<br/>
	 * 紓困|10<br/>
	 * 提前續約|11<br/>
	 * 協議清償|12<br/>
	 * 報價 | 13
	 **/
	public void setProperty(String value) {
		this.property = value;
	}

	/**
	 * 取得額度性質
	 * <p/>
	 * S:擔保<br/>
	 * N:無擔保/信保
	 */
	public String getSbjProperty() {
		return this.sbjProperty;
	}

	/**
	 * 設定額度性質
	 * <p/>
	 * S:擔保<br/>
	 * N:無擔保/信保
	 **/
	public void setSbjProperty(String value) {
		this.sbjProperty = value;
	}

	/**
	 * 取得本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 */
	public String getHeadItem1() {
		return this.headItem1;
	}

	/**
	 * 設定本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 **/
	public void setHeadItem1(String value) {
		this.headItem1 = value;
	}

	/**
	 * 取得借款人是否為中小企業
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getHeadItem2() {
		return this.headItem2;
	}

	/**
	 * 設定借款人是否為中小企業
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setHeadItem2(String value) {
		this.headItem2 = value;
	}

	/**
	 * 取得是否符合送信保基金保證條件
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getHeadItem3() {
		return this.headItem3;
	}

	/**
	 * 設定是否符合送信保基金保證條件
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setHeadItem3(String value) {
		this.headItem3 = value;
	}

	/** 取得前准額度－幣別 **/
	public String getLV2Curr() {
		return this.LV2Curr;
	}

	/** 設定前准額度－幣別 **/
	public void setLV2Curr(String value) {
		this.LV2Curr = value;
	}

	/** 取得前准額度－金額 **/
	public BigDecimal getLV2Amt() {
		return this.LV2Amt;
	}

	/** 設定前准額度－金額 **/
	public void setLV2Amt(BigDecimal value) {
		this.LV2Amt = value;
	}

	/** 取得現請額度－幣別 **/
	public String getCurrentApplyCurr() {
		return this.currentApplyCurr;
	}

	/** 設定現請額度－幣別 **/
	public void setCurrentApplyCurr(String value) {
		this.currentApplyCurr = value;
	}

	/** 取得現請額度－金額 **/
	public BigDecimal getCurrentApplyAmt() {
		return this.currentApplyAmt;
	}

	/** 設定現請額度－金額 **/
	public void setCurrentApplyAmt(BigDecimal value) {
		this.currentApplyAmt = value;
	}

	/** 取得新作、增額合計幣別 **/
	public String getIncApplyTotCurr() {
		return this.incApplyTotCurr;
	}

	/** 設定新作、增額合計幣別 **/
	public void setIncApplyTotCurr(String value) {
		this.incApplyTotCurr = value;
	}

	/** 取得新作、增額合計金額 **/
	public BigDecimal getIncApplyTotAmt() {
		return this.incApplyTotAmt;
	}

	/** 設定新作、增額合計金額 **/
	public void setIncApplyTotAmt(BigDecimal value) {
		this.incApplyTotAmt = value;
	}

	/** 取得新作、增額擔保額度合計幣別 **/
	public String getIncAssTotCurr() {
		return this.incAssTotCurr;
	}

	/** 設定新作、增額擔保額度合計幣別 **/
	public void setIncAssTotCurr(String value) {
		this.incAssTotCurr = value;
	}

	/** 取得新作、增額擔保額度合計金額 **/
	public BigDecimal getIncAssTotAmt() {
		return this.incAssTotAmt;
	}

	/** 設定新作、增額擔保額度合計金額 **/
	public void setIncAssTotAmt(BigDecimal value) {
		this.incAssTotAmt = value;
	}

	/** 取得授信額度合計幣別 **/
	public String getLoanTotCurr() {
		return this.LoanTotCurr;
	}

	/** 設定授信額度合計幣別 **/
	public void setLoanTotCurr(String value) {
		this.LoanTotCurr = value;
	}

	/** 取得授信額度合計金額 **/
	public BigDecimal getLoanTotAmt() {
		return this.LoanTotAmt;
	}

	/** 設定授信額度合計金額 **/
	public void setLoanTotAmt(BigDecimal value) {
		this.LoanTotAmt = value;
	}

	/** 取得擔保授信額度合計幣別 **/
	public String getAssureTotCurr() {
		return this.assureTotCurr;
	}

	/** 設定擔保授信額度合計幣別 **/
	public void setAssureTotCurr(String value) {
		this.assureTotCurr = value;
	}

	/** 取得擔保授信額度合計金額 **/
	public BigDecimal getAssureTotAmt() {
		return this.assureTotAmt;
	}

	/** 設定擔保授信額度合計金額 **/
	public void setAssureTotAmt(BigDecimal value) {
		this.assureTotAmt = value;
	}

	/**
	 * 取得企業規模
	 * <p/>
	 * 民營大企業<br/>
	 * 私人<br/>
	 * --<br/>
	 * 民營中小企業<br/>
	 * 公營事業<br/>
	 * 政府機關<br/>
	 * 非營利團體<br/>
	 * 視同中小企業
	 */
	public String getCltType() {
		return this.cltType;
	}

	/**
	 * 設定企業規模
	 * <p/>
	 * 民營大企業<br/>
	 * 私人<br/>
	 * --<br/>
	 * 民營中小企業<br/>
	 * 公營事業<br/>
	 * 政府機關<br/>
	 * 非營利團體<br/>
	 * 視同中小企業
	 **/
	public void setCltType(String value) {
		this.cltType = value;
	}

	/** 取得行業對象別 **/
	public String getBusCode() {
		return this.busCode;
	}

	/** 設定行業對象別 **/
	public void setBusCode(String value) {
		this.busCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得刪除註記 **/
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}

	/** 設定刪除註記 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/**
	 * 取得匯率
	 * <p/>
	 * 新作、增額合計幣別的匯率
	 */
	public BigDecimal getEndRate() {
		return this.endRate;
	}

	/**
	 * 設定匯率
	 * <p/>
	 * 新作、增額合計幣別的匯率
	 **/
	public void setEndRate(BigDecimal value) {
		this.endRate = value;
	}

	/**
	 * 取得匯率日期
	 * <p/>
	 * 產生資料的當天
	 */
	public String getRateYmd() {
		return this.rateYmd;
	}

	/**
	 * 設定匯率日期
	 * <p/>
	 * 產生資料的當天
	 **/
	public void setRateYmd(String value) {
		this.rateYmd = value;
	}

	/**
	 * 取得是否為新客戶
	 * <p/>
	 * Y/N
	 */
	public String getIsNew() {
		return this.isNew;
	}

	/**
	 * 設定是否為新客戶
	 * <p/>
	 * Y/N
	 **/
	public void setIsNew(String value) {
		this.isNew = value;
	}

	/** 取得產品種類 **/
	public String getLnType() {
		return this.lnType;
	}

	/** 設定產品種類 **/
	public void setLnType(String value) {
		this.lnType = value;
	}

	/** 取得專案種類 **/
	public String getProjClass() {
		return this.projClass;
	}

	/** 設定專案種類 **/
	public void setProjClass(String value) {
		this.projClass = value;
	}

	/** 取得授信科目 **/
	public String getLnSubject() {
		return this.lnSubject;
	}

	/** 設定授信科目 **/
	public void setLnSubject(String value) {
		this.lnSubject = value;
	}

	/** 取得本案是否屬兆元振興融資方案 **/
	public String getIsRevive() {
		return this.isRevive;
	}

	/** 設定本案是否屬兆元振興融資方案 **/
	public void setIsRevive(String value) {
		this.isRevive = value;
	}

	/** 取得貸款對象類別 **/
	public String getReviveTarget() {
		return this.reviveTarget;
	}

	/** 設定貸款對象類別 **/
	public void setReviveTarget(String value) {
		this.reviveTarget = value;
	}

	/** 取得六大核心產業 **/
	public String getReviveCoreIndustry() {
		return this.reviveCoreIndustry;
	}

	/** 設定六大核心產業 **/
	public void setReviveCoreIndustry(String value) {
		this.reviveCoreIndustry = value;
	}

	/** 取得國際鏈結布局 **/
	public String getReviveChain() {
		return this.reviveChain;
	}

	/** 設定國際鏈結布局 **/
	public void setReviveChain(String value) {
		this.reviveChain = value;
	}

	/** 取得貸款用途 **/
	public String getReviveLoanPurpose() {
		return this.reviveLoanPurpose;
	}

	/** 設定貸款用途 **/
	public void setReviveLoanPurpose(String value) {
		this.reviveLoanPurpose = value;
	}

	/**
	 * 取得現請額度幣別匯率
	 * <p/>
	 * 產生資料的當天
	 */
	public BigDecimal getCurrEndRate() {
		return this.currEndRate;
	}

	/**
	 * 設定現請額度幣別匯率
	 * <p/>
	 * 產生資料的當天
	 **/
	public void setCurrEndRate(BigDecimal value) {
		this.currEndRate = value;
	}

	/** 取得前准額度幣別匯率 **/
	public BigDecimal getLv2Rate() {
		return this.lv2Rate;
	}

	/** 設定前准額度幣別匯率 **/
	public void setLv2Rate(BigDecimal value) {
		this.lv2Rate = value;
	}

	/** 取得增額註記 **/
	public String getIncreaseFlag() {
		return this.increaseFlag;
	}

	/** 設定增額註記 **/
	public void setIncreaseFlag(String value) {
		this.increaseFlag = value;
	}

	/** 取得符合報送註記 **/
	public String getReportFlag() {
		return this.reportFlag;
	}

	/** 設定符合報送註記 **/
	public void setReportFlag(String value) {
		this.reportFlag = value;
	}

	/**
	 * 取得報送金額
	 * <p/>
	 * increaseFlag = Y則現請額度-前准額度，否則為現請額度
	 */
	public BigDecimal getReportAmt() {
		return this.reportAmt;
	}

	/**
	 * 設定報送金額
	 * <p/>
	 * increaseFlag = Y則現請額度-前准額度，否則為現請額度
	 **/
	public void setReportAmt(BigDecimal value) {
		this.reportAmt = value;
	}

	public void setHasComm(String hasComm) {
		this.hasComm = hasComm;
	}

	public String getHasComm() {
		return hasComm;
	}

	public void setCommSno(String commSno) {
		this.commSno = commSno;
	}

	public String getCommSno() {
		return commSno;
	}

	public void setReviveAmt(BigDecimal reviveAmt) {
		this.reviveAmt = reviveAmt;
	}

	public BigDecimal getReviveAmt() {
		return reviveAmt;
	}

	public void setSnoKind(String snoKind) {
		this.snoKind = snoKind;
	}

	public String getSnoKind() {
		return snoKind;
	}

	public void setReUse(String reUse) {
		this.reUse = reUse;
	}

	public String getReUse() {
		return reUse;
	}

	public void setReportFlag_113(String reportFlag_113) {
		this.reportFlag_113 = reportFlag_113;
	}

	public String getReportFlag_113() {
		return reportFlag_113;
	}

	public void setReportFlag_113_reUse(String reportFlag_113_reUse) {
		this.reportFlag_113_reUse = reportFlag_113_reUse;
	}

	public String getReportFlag_113_reUse() {
		return reportFlag_113_reUse;
	}

	public void setReportFlag_517(String reportFlag_517) {
		this.reportFlag_517 = reportFlag_517;
	}

	public String getReportFlag_517() {
		return reportFlag_517;
	}

	public void setReportFlag_517_reUse(String reportFlag_517_reUse) {
		this.reportFlag_517_reUse = reportFlag_517_reUse;
	}

	public String getReportFlag_517_reUse() {
		return reportFlag_517_reUse;
	}
}
