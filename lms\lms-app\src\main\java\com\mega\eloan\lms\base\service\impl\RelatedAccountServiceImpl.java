package com.mega.eloan.lms.base.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.RelatedAccountPanel;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RelatedAccountService;
import com.mega.eloan.lms.dao.C120S01GDao;
import com.mega.eloan.lms.dao.C120S01QDao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01CDao;
import com.mega.eloan.lms.dao.L120S04ADao;
import com.mega.eloan.lms.dao.L120S04BDao;
import com.mega.eloan.lms.dao.L120S04CDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01BDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisElcrcoService;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.mfaloan.service.MisGrpdtlService;
import com.mega.eloan.lms.model.C120S01G;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S04C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.obsdb.service.MisELF001Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.SheetSettings;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.PageOrientation;
import jxl.write.Label;
import jxl.write.NumberFormats;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Service("RelatedAccountServiceImpl")
public class RelatedAccountServiceImpl extends AbstractCapService implements
		RelatedAccountService {

	@Resource
	MisELF001Service elf001Srv; // 海外存款

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(RelatedAccountServiceImpl.class);

	// 過濾所有以<開頭以>結尾的標籤
	private final static String regxpForHtml = "<([^>]*)>";

	/**
	 * 
	 * 基本功能：過濾所有以"<"開頭以">"結尾的標籤
	 * <p>
	 * 
	 * @param str
	 *            String
	 * @return String
	 */
	private static String filterHtml(String str) {
		Pattern pattern = Pattern.compile(regxpForHtml);
		Matcher matcher = pattern.matcher(str);
		StringBuffer sb = new StringBuffer();
		boolean result1 = matcher.find();
		while (result1) {
			matcher.appendReplacement(sb, "");
			result1 = matcher.find();
		}
		matcher.appendTail(sb);
		return sb.toString();
	}

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	DocFileService docFileService;;

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	EloandbBASEService eloanDBService;

	@Resource
	LMSService lmsService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisELF001Service misELF001Service;

	@Resource
	MisElcrcoService misElcrcoService;

	@Resource
	MisGrpcmpService misGrpcmpService;

	@Resource
	MisGrpdtlService misGrpdtlService;

	@Resource
	TempDataService tempDataService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L120S01CDao l120s01cDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L120S01ADao l120s01aDao;

	@Resource
	L120S04ADao l120s04aDao;

	@Resource
	L120S04BDao l120s04bDao;

	@Resource
	L120S04CDao l120s04cDao;

	@Resource
	L140M01BDao l140m01bDao;

	@Resource
	C120S01GDao c120s01gDao;

	@Resource
	C120S01QDao c120s01qDao;

	// =============================================
	@Override
	public void save(GenericBean... entity) {
		// 進行無限多筆儲存
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L120M01A) {
					if (((L120M01A) model).getDocStatus().equals(
							CreditDocStatusEnum.海外_編製中.getCode())
							|| ((L120M01A) model).getDocStatus().equals(
									CreditDocStatusEnum.海外_待補件.getCode())) {
						if (!Util.trim(SimpleContextHolder.get("runTempSave"))
								.equals("true")) {
							String randomCode = IDGenerator.getRandomCode();
							((L120M01A) model).setRandomCode(randomCode);
						}
						((L120M01A) model).setUpdater(user.getUserId());
						((L120M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
					}
					l120m01aDao.save((L120M01A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120M01A) model)
								.getMainId());
					}
				} else if (model instanceof L120S04A) {
					((L120S04A) model).setUpdater(user.getUserId());
					((L120S04A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120s04aDao.save((L120S04A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S04A) model)
								.getMainId());
						// // 記錄文件異動記錄
						// docLogService.record(((L120M01D) model).getOid(),
						// DocLogEnum.SAVE);
					}
				} else if (model instanceof L120S04B) {
					((L120S04B) model).setUpdater(user.getUserId());
					((L120S04B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120s04bDao.save((L120S04B) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S04B) model)
								.getMainId());
						// // 記錄文件異動記錄
						// docLogService.record(((L120M01D) model).getOid(),
						// DocLogEnum.SAVE);
					}
				} else if (model instanceof L120S04C) {
					((L120S04C) model).setUpdater(user.getUserId());
					((L120S04C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120s04cDao.save((L120S04C) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S04C) model)
								.getMainId());
						// // 記錄文件異動記錄
						// docLogService.record(((L120M01D) model).getOid(),
						// DocLogEnum.SAVE);
					}
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L120S04A) {
					l120s04aDao.delete((L120S04A) model);
				} else if (model instanceof L120S04B) {
					l120s04bDao.delete((L120S04B) model);
				} else if (model instanceof L120S04C) {
					l120s04cDao.delete((L120S04C) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L120S01A.class) {
			return l120s01aDao.findPage(search);
		} else if (clazz == L120S04A.class) {
			return l120s04aDao.findPage(search);
		} else if (clazz == L120S04B.class) {
			return l120s04bDao.findPage(search);
		} else if (clazz == L120S04C.class) {
			return l120s04cDao.findPage(search);
		}

		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}

	@Override
	public Page<? extends GenericBean> queryL120s01aById(ISearch pageSetting) {
		return findPage(L120S01A.class, pageSetting);
	}

	@Override
	public Page<DocFile> queryfile(ISearch pageSetting, boolean needCngName,
			boolean needBranch) {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		Page<DocFile> page = docFileService.readToGrid(pageSetting);
		List<DocFile> list = (List<DocFile>) page.getContent();
		if (needCngName) {
			for (DocFile file : list) {
				// other.msg61=借戶暨關係戶與本行授信往來比較表
				file.setSrcFileName(pop.getProperty("other.msg61") + ".xls");
			}
		}
		if (needBranch) {
			// 需要分行名稱(透過CRYEAR欄位顯示)
			for (DocFile file : list) {
				file.setCrYear(Util.trim(file.getBranchId())
						+ " "
						+ branchService.getBranchName(Util.trim(file
								.getBranchId())));
			}
		}
		return page;

	}

	@Override
	public Page<? extends GenericBean> queryL120s04a(ISearch pageSetting) {
		Page<? extends GenericBean> page = findPage(L120S04A.class, pageSetting);
		return page;
	}

	@Override
	public Page<? extends GenericBean> queryL120s04b(ISearch pageSetting) {
		Page<? extends GenericBean> page = findPage(L120S04B.class, pageSetting);
		return page;
	}

	@Override
	public boolean delfile(String fileOid) {
		return docFileService.clean(fileOid);
	}

	@Override
	public void genfile(String mainId, String fieldId, String keyCustId,
			String keyDupNo) throws CapException, WriteException, IOException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		ByteArrayOutputStream baos = null;
		List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();
		WritableFont font12 = null;
		WritableCellFormat format12Center = null;
		WritableCellFormat format12Left = null;
		WritableCellFormat format12Right = null;
		WritableCellFormat format12LeftNO = null;
		WritableCellFormat format12RightNO = null;
		L120M01A meta = findL120m01aByMainId(mainId);
		List<L120S04A> tempNew = l120s04aDao.findByMainIdKeyCustIdDupNo(mainId,
				keyCustId, keyDupNo);

		List<L120S04A> list = new ArrayList<L120S04A>(tempNew);

		if (list != null && !list.isEmpty()) {

			Collections.sort(list, new Comparator<L120S04A>() {

				@Override
				public int compare(L120S04A object1, L120S04A object2) {
					// TODO Auto-generated method stub
					int cr = 0;
					String[] resStr1 = Util.trim(object1.getCustRelation())
							.split(",");
					Arrays.sort(resStr1);
					String[] resStr2 = Util.trim(object2.getCustRelation())
							.split(",");
					Arrays.sort(resStr2);

					int a = resStr2[0].compareTo(resStr1[0]);

					String prtFlag1 = object1.getPrtFlag();
					String prtFlag2 = object2.getPrtFlag();
					int prtFlag = prtFlag2.compareTo(prtFlag1);

					if (prtFlag != 0) {
						cr = (prtFlag > 0) ? -1 : 5;
					} else if (a != 0) {
						cr = (a > 0) ? -2 : 4;
					} else {
						long b = (object2.getProfit() == null ? 0 : object2
								.getProfit())
								- (object1.getProfit() == null ? 0 : object1
										.getProfit());
						if (b != 0) {
							cr = (b > 0) ? 3 : -3;
						} else {
							int c = object2.getCustId().compareTo(
									object1.getCustId());
							if (c != 0) {
								cr = (c > 0) ? -4 : 2;
							} else {
								// String oid1 = object1.getOid();
								// String oid2 = object2.getOid();
								// int oidFlag = oid2.compareTo(oid2);
								// if(oidFlag != 0){
								// cr = (oidFlag > 0)? -5:1;
								// }
							}
						}
					}

					return cr;
				}
			});
		}

		String brno = null;
		String custId = null;
		String dupNo = null;
		String custName = null;
		String caseNo = null;

		// String ranMainId = IDGenerator.getRandomCode();
		DocFile docFile = new DocFile();
		docFile.setBranchId((meta != null) ? Util.trim(meta.getCaseBrId()) : "");
		docFile.setContentType("application/msexcel");
		docFile.setMainId(mainId);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(fieldId);
		// other.msg61=借戶暨關係戶與本行授信往來比較表
		// LMSNoList.xls請顯示為 主要關係戶與本行授信往來比較表.xls
		docFile.setSrcFileName("LMS1205R24A.xls");
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(docFileService.getSysId());
		docFile.setData(new byte[] {});
		docFileService.save(docFile, false);
		File file = null;

		String filename = null;
		String xlsOid = null;

		xlsOid = docFile.getOid();
		filename = LMSUtil.getUploadFilePath(docFile.getBranchId(), mainId,
				docFile.getFieldId());
		file = new File(filename);
		file.mkdirs();

		try {
			file = new File(filename + "/" + xlsOid + ".xls");

			// baos = new ByteArrayOutputStream();
			// WritableWorkbook book = Workbook.createWorkbook(baos);
			WritableWorkbook book = Workbook.createWorkbook(file);
			WritableSheet sheet = book.createSheet("1", 0);

			/*
			 * 1.1方向 SheetSetting#setOrientation(PageOrientation po)； 參數：
			 * PageOrientation#LANDSCAPE 橫向打印 PageOrientation# PORTRAIT 縱向打印 (A)
			 * SheetSetting #setScaleFactor (int);百分比形式
			 */
			SheetSettings settings = sheet.getSettings();
			settings.setOrientation(PageOrientation.LANDSCAPE);
			// EXCEL 請設定預設為橫印、預設為1頁寬
			// // 縮放比例
			// settings.setScaleFactor(100);
			// 縮放比例頁寬
			settings.setFitWidth(1);
			// 縮放比例頁高
			settings.setFitHeight(5000);
			// 頁面距(英吋)
			// 上
			settings.setTopMargin((double) 0.394);
			// 下
			settings.setBottomMargin((double) 0.394);
			// 左
			settings.setLeftMargin((double) 0.197);
			// 右
			settings.setRightMargin((double) 0.197);
			// 頁首
			settings.setHeaderMargin((double) 0.5);
			// 頁尾
			settings.setFooterMargin((double) 0.5);

			// 設定字型與格式
			// other.msg60=新細明體
			font12 = new WritableFont(WritableFont.createFont(pop
					.getProperty("other.msg60")), 12, WritableFont.NO_BOLD);
			// 將某欄位強制轉成文字欄位顯示
			WritableCellFormat contentFromart = new WritableCellFormat(
					NumberFormats.TEXT);
			format12Center = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE);
			format12Left = LMSUtil.setCellFormat(contentFromart, font12,
					Alignment.LEFT);
			format12Right = LMSUtil.setCellFormat(format12Right, font12,
					Alignment.RIGHT);
			format12LeftNO = LMSUtil.setCellFormat(format12LeftNO, font12,
					Alignment.LEFT, false, false);
			format12RightNO = LMSUtil.setCellFormat(format12RightNO, font12,
					Alignment.RIGHT, false, false);
			sheet.mergeCells(0, 0, 12, 0);
			if (meta != null) {
				custId = keyCustId;
				dupNo = keyDupNo;
				brno = Util.trim(meta.getCaseBrId());
				custName = getKeyCustName(mainId, custId, dupNo);
			}
			StringBuilder sb = new StringBuilder();
			// other.msg61=借戶暨關係戶與本行授信往來比較表
			sb.append(Util.trim(custId)).append(" ")
					.append(Util.trim(custName))
					.append(pop.getProperty("other.msg61")).append(" ")
					.append(TWNDate.toTW(new Date()));
			Label labelT1 = new Label(0, 0, sb.toString(), format12Left);
			sheet.addCell(labelT1);

			// other.msg62=公司名稱
			Label labelT2 = new Label(0, 1, pop.getProperty("other.msg62"),
					format12Center);
			sheet.addCell(labelT2);
			// other.msg136=分行
			Label labelT15 = new Label(1, 1, pop.getProperty("other.msg136"),
					format12Center);
			sheet.addCell(labelT15);
			// other.msg63=信用評等
			Label labelT3 = new Label(2, 1, pop.getProperty("other.msg63"),
					format12Center);
			sheet.addCell(labelT3);
			// other.msg64=關係
			Label labelT4 = new Label(3, 1, pop.getProperty("other.msg64"),
					format12Center);
			sheet.addCell(labelT4);
			// other.msg65=核准日期
			Label labelT5 = new Label(4, 1, pop.getProperty("other.msg65"),
					format12Center);
			sheet.addCell(labelT5);
			// other.msg137=額度序號
			Label labelT16 = new Label(5, 1, pop.getProperty("other.msg137"),
					format12Center);
			sheet.addCell(labelT16);
			// other.msg66=額度（仟元）
			Label labelT6 = new Label(6, 1, pop.getProperty("other.msg66"),
					format12Center);
			sheet.addCell(labelT6);
			// other.msg67=性質
			Label labelT7 = new Label(7, 1, pop.getProperty("other.msg67"),
					format12Center);
			sheet.addCell(labelT7);
			// other.msg68=科目
			Label labelT8 = new Label(8, 1, pop.getProperty("other.msg68"),
					format12Center);
			sheet.addCell(labelT8);
			// other.msg69=清償期限
			Label labelT9 = new Label(9, 1, pop.getProperty("other.msg69"),
					format12Center);
			sheet.addCell(labelT9);
			// other.msg70=保證人
			Label labelT10 = new Label(10, 1, pop.getProperty("other.msg70"),
					format12Center);
			sheet.addCell(labelT10);
			// other.msg71=擔保品
			Label labelT11 = new Label(11, 1, pop.getProperty("other.msg71"),
					format12Center);
			sheet.addCell(labelT11);
			// other.msg72=利率
			Label labelT12 = new Label(12, 1, pop.getProperty("other.msg72"),
					format12Center);
			sheet.addCell(labelT12);
			// other.msg73=其他敘做條件
			Label labelT13 = new Label(13, 1, pop.getProperty("other.msg73"),
					format12Center);
			sheet.addCell(labelT13);
			// other.msg74=備註
			Label labelT14 = new Label(14, 1, pop.getProperty("other.msg74"),
					format12Center);
			sheet.addCell(labelT14);
			// 設定行寬
			sheet.setColumnView(0, 15);
			sheet.setColumnView(1, 15);
			sheet.setColumnView(2, 15);
			sheet.setColumnView(3, 10);
			sheet.setColumnView(4, 15);
			sheet.setColumnView(5, 15);
			sheet.setColumnView(6, 15);
			sheet.setColumnView(7, 10);
			sheet.setColumnView(8, 15);
			sheet.setColumnView(9, 10);
			sheet.setColumnView(10, 10);
			sheet.setColumnView(11, 25);
			sheet.setColumnView(12, 25);
			sheet.setColumnView(13, 25);
			sheet.setColumnView(14, 25);

			for (L120S04A model : list) {
				// 集團合計、關係企業合計就不用顯示於EXCEL
				if (!UtilConstants.Casedoc.L120s04aCustRelation.集團企業合計
						.equals(Util.trim(model.getCustRelation()))
						&& !UtilConstants.Casedoc.L120s04aCustRelation.關係企業合計
								.equals(Util.trim(model.getCustRelation()))) {
					String tCustId = Util.trim(model.getCustId());
					String tDupNo = Util.trim(model.getDupNo());
					String tCustName = Util.trim(model.getCustName());
					List<Map<String, Object>> tempListMap = eloanDBService
							.selExcel(tCustId, tDupNo);
					listMap.add(getFullContent(tempListMap, tCustId, tDupNo,
							tCustName, model));
				}
			}

			if (!listMap.isEmpty()) {
				for (int i = 0, k = 2; i < listMap.size(); i++, k++) {
					// 公司名稱
					Label labelCustName = new Label(0, k, listMap.get(i)
							.get("custName").toString(), format12Left);
					sheet.addCell(labelCustName);
					// 分行
					Label labelOwnBrId = new Label(1, k, listMap.get(i)
							.get("ownBrId").toString(), format12Left);
					sheet.addCell(labelOwnBrId);
					// 信用評等
					Label labelGrade = new Label(2, k, listMap.get(i)
							.get("grade").toString(), format12Left);
					sheet.addCell(labelGrade);
					// 關係
					Label labelCustRelation = new Label(3, k, listMap.get(i)
							.get("custRelation").toString(), format12Left);
					sheet.addCell(labelCustRelation);
					// 核准日期
					Label labelApproveTime = new Label(4, k, listMap.get(i)
							.get("approveTime").toString(), format12Center);
					sheet.addCell(labelApproveTime);
					// 額度序號
					Label labelCntrNo = new Label(5, k, listMap.get(i)
							.get("cntrNo").toString(), format12Left);
					sheet.addCell(labelCntrNo);
					// 額度(仟元)
					Label labelCurrentApplyAmt = new Label(6, k, listMap.get(i)
							.get("currentApplyAmt").toString(), format12Right);
					sheet.addCell(labelCurrentApplyAmt);
					// 性質
					Label labelProperty = new Label(7, k, listMap.get(i)
							.get("property").toString(), format12Left);
					sheet.addCell(labelProperty);
					// 科目
					Label labelLnSubject = new Label(8, k, listMap.get(i)
							.get("lnSubject").toString(), format12Left);
					sheet.addCell(labelLnSubject);
					// 清償期限
					Label labelPayDeadline = new Label(9, k, listMap.get(i)
							.get("payDeadline").toString(), format12Left);
					sheet.addCell(labelPayDeadline);
					// 保證人
					Label labelGuarantor = new Label(10, k, listMap.get(i)
							.get("guarantor").toString(), format12Left);
					sheet.addCell(labelGuarantor);
					// 擔保品
					Label labelDanbowDscr = new Label(11, k, listMap.get(i)
							.get("danbowDscr").toString(), format12Left);
					sheet.addCell(labelDanbowDscr);
					// 利率
					Label labelRateDscr = new Label(12, k, listMap.get(i)
							.get("rateDscr").toString(), format12Left);
					sheet.addCell(labelRateDscr);
					// 其他敘做條件
					Label labelOtherDscr = new Label(13, k, listMap.get(i)
							.get("otherDscr").toString(), format12Left);
					sheet.addCell(labelOtherDscr);
					// 備註
					Label labelMemoDscr = new Label(14, k, listMap.get(i)
							.get("memoDscr").toString(), format12Left);
					sheet.addCell(labelMemoDscr);
				}
			}

			book.write();
			book.close();
			// return baos.toByteArray();
			// return null;
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
		// return null;

	}

	@Override
	public L120M01A findL120m01aByMainId(String mainId) {
		// 透過MainId取得資料
		return l120m01aDao.findByMainId(mainId);
	}

	private List<L120S01C> findL120s01cByCustId(String mainId, String custId,
			String dupNo) {
		return l120s01cDao.findByCustId(mainId, custId, dupNo);
	}

	private L140M01A findL140m01aByMainId(String mainId) {
		return l140m01aDao.findByUniqueKey(mainId);
	}

	private L140M01B findL140m01bUniqueKey(String mainId, String itemType) {
		return l140m01bDao.findByUniqueKey(mainId, itemType);
	}

	/**
	 * 取得完整資料(Excel所要塞的內容)
	 * 
	 * @param tempListMap
	 * @param custId
	 *            往來彙總明細統編
	 * @param dupNo
	 *            往來彙總明細重覆序號
	 * @param custName
	 *            公司名稱
	 * @return
	 */
	private Map<String, Object> getFullContent(
			List<Map<String, Object>> tempListMap, String custId, String dupNo,
			String custName, L120S04A model) {
		Map<String, String> proPertyMap = codetypeService.findByCodeType(
				"lms1405s02_proPerty", "zh_TW");
		Map<String, Object> map = new HashMap<String, Object>();
		/*
		 * 信用評等(用L120M01A.docType判斷企金/個金) 企金：L120S01C.crdType + L120S01C.grade
		 * 個金：C120S01G.grade3
		 */
		String grade = null;
		// 關係(L120S04A.custRelation)
		String custRelation = null;
		// 核准日期(L120M01A.approveTime)
		String approveTime = null;
		// 編製單位(L140M01A.ownBrId)
		String ownBrId = null;
		// 額度序號(L140M01A.cntrNo)
		String cntrNo = null;
		// 額度(仟元)(L140M01A.currentApplyCurr + L140M01A.currentApplyAmt)
		String currentApplyAmt = null;
		// 性質(L140M01A.property)
		String property = null;
		// 科目(L140M01A.lnSubject)
		String lnSubject = null;
		// 清償期限(L140M01A.payDeadline)
		String payDeadline = null;
		// 保證人(L140M01A.guarantorType=="2" + L140M01A.guarantor)
		String guarantor = null;
		// 擔保品(L140M01B.itemType=="3" + L140M01B.itemDscr)
		String danbowDscr = null;
		// 利率(L140M01B.itemType=="2" + L140M01B.itemDscr)
		String rateDscr = null;
		// 其他敘作條件(L140M01B.itemType=="4" + L140M01B.itemDscr)
		String otherDscr = null;
		// 備註
		String memoDscr = null;
		// 額度明細表文件編號
		String cntrMainId = null;
		// 簽報書文件編號
		String mainId = null;

		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		for (Map<String, Object> tempMap : tempListMap) {
			// 編製單位
			ownBrId = Util.trim(tempMap.get("OWNBRID"));
			// 額度序號
			cntrNo = Util.trim(tempMap.get("CNTRNO"));
			// 額度明細表文件編號
			cntrMainId = Util.trim(tempMap.get("CNTRMAINID"));
			sb.append(Util.trim(tempMap.get("CURRENTAPPLYCURR")))
					.append(NumConverter.addComma((LMSUtil.toBigDecimal(Util
							.trim(tempMap.get("currentApplyAmt"))) == null) ? LMSUtil
							.toBigDecimal(Util.trim(tempMap
									.get("currentApplyAmt"))) : LMSUtil
							.toBigDecimal(
									Util.trim(tempMap.get("currentApplyAmt")))
							.setScale(0)));
			// 額度(仟元)
			currentApplyAmt = sb.toString();
			// 性質
			property = this.getProPerty(Util.trim(tempMap.get("PROPERTY")),
					proPertyMap);
			// 科目
			lnSubject = Util.trim(tempMap.get("LNSUBJECT"));
			// 清償期限
			payDeadline = Util.trim(tempMap.get("PAYDEADLINE"));
			// 核准日期
			approveTime = TWNDate.toTW(
					CapDate.getDate(Util.trim(tempMap.get("APPROVETIME")),
							UtilConstants.DateFormat.YYYY_MM_DD)).replace("/",
					".");
			break;
		}
		L140M01A cntrModel = findL140m01aByMainId(cntrMainId);
		L140M01B danBowModel = findL140m01bUniqueKey(cntrMainId,
				UtilConstants.Cntrdoc.l140m01bItemType.擔保品);
		L140M01B rateModel = findL140m01bUniqueKey(cntrMainId,
				UtilConstants.Cntrdoc.l140m01bItemType.利費率);
		L140M01B otherModel = findL140m01bUniqueKey(cntrMainId,
				UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件);
		if (cntrModel != null) {
			L120M01C relateModel = cntrModel.getL120m01c();
			if ("2".equals(Util.trim(cntrModel.getGuarantorType()))) {
				// 保證人內容
				guarantor = Util.trim(cntrModel.getGuarantor());
			} else if ("1".equals(Util.trim(cntrModel.getGuarantorType()))) {
				// 連保人內容
				guarantor = Util.trim(cntrModel.getGuarantor());
			}
			if (relateModel != null) {
				// 簽報書文件編號
				mainId = Util.trim(relateModel.getMainId());
			}
		}
		if (danBowModel != null) {
			// 擔保品內容
			danbowDscr = filterHtml(Util.trim(danBowModel.getItemDscr()))
					.replace("&nbsp;", UtilConstants.Mark.SPACE);
		}
		if (rateModel != null) {
			// 利費率內容
			rateDscr = filterHtml(Util.trim(rateModel.getItemDscr())).replace(
					"&nbsp;", UtilConstants.Mark.SPACE);
		}
		if (otherModel != null) {
			// 其他敘做條件內容
			otherDscr = filterHtml(Util.trim(otherModel.getItemDscr()))
					.replace("&nbsp;", UtilConstants.Mark.SPACE);
		}
		// L120S04A custRelModel = service1201.findL120s04aByUniqueKey(mainId,
		// custId, dupNo, custName);
		if (model != null) {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(RelatedAccountPanel.class);
			StringBuilder relSb = new StringBuilder();
			relSb.setLength(0);
			String[] strs = Util.trim(model.getCustRelation()).split(",");
			// 對陣列進行排序
			Arrays.sort(strs);
			for (String s : strs) {
				if (relSb.length() > 0)
					relSb.append("/");
				relSb.append(Util.trim(pop.getProperty("L1205S07.checkbox" + s)));
			}
			// 關係
			custRelation = relSb.toString();
		}
		L120M01A meta = null;
		if (Util.isNotEmpty(mainId)) {
			// 避開不合理的資料 l120m01a.mainId is null
			meta = findL120m01aByMainId(mainId);
		}
		if (meta != null) {
			String docType = Util.trim(meta.getDocType());
			if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {
				// 企金信評設定
				StringBuilder trustSb = new StringBuilder();
				trustSb.setLength(0);
				List<L120S01C> busList = findL120s01cByCustId(mainId, custId,
						dupNo);
				// for (L120S01C busModel : busList) {
				// String crdType = Util.trim(busModel.getCrdType());
				// trustSb.append(
				// (trustSb.length() > 0) ? "\n"
				// : UtilConstants.Mark.SPACE)
				// .append(getTrustName(crdType))
				// .append(("NA".equals(crdType) || "NO"
				// .equals(crdType)) ? UtilConstants.Mark.SPACE
				// : "：")
				// .append(Util.trim(busModel.getGrade()));
				// }
				// grade = trustSb.toString();
				grade = getL120S01CData(busList);
			} else if (UtilConstants.Casedoc.DocType.個金.equals(docType)) {
				// 個金信評設定
				/*
				 * 房貸評等:1, 非房貸評等:3 顯示「特A/3」
				 */
				C120S01G g_model = c120s01gDao.findByUniqueKey(mainId,
						Util.trim(meta.getOwnBrId()), custId, dupNo);
				C120S01Q q_model = c120s01qDao.findByUniqueKey(mainId,
						Util.trim(meta.getOwnBrId()), custId, dupNo);
				String g_grade = "";
				String q_grade = "";
				if (g_model != null && Util.isNotEmpty(g_model.getGrade3())) {
					g_grade = LMSUtil.getFinalGrade(
							UtilConstants.L140S02AModelKind.房貸,
							g_model.getGrade3());
				}
				if (q_model != null && Util.isNotEmpty(q_model.getGrade3())) {
					q_grade = LMSUtil.getFinalGrade(
							UtilConstants.L140S02AModelKind.非房貸,
							q_model.getGrade3());
				}
				if (Util.isEmpty(g_grade) && Util.isEmpty(q_grade)) {
					grade = "";
				} else if (Util.isNotEmpty(g_grade) && Util.isEmpty(q_grade)) {
					grade = g_grade;
				} else if (Util.isEmpty(g_grade) && Util.isNotEmpty(q_grade)) {
					grade = q_grade;
				} else {
					grade = g_grade + " / " + q_grade;
				}
			}
		}

		// 開始設定值
		map.put("ownBrId", Util.trim(ownBrId));
		map.put("cntrNo", Util.trim(cntrNo));
		map.put("custName", Util.trim(custName));
		map.put("grade", Util.trim(grade));
		map.put("custRelation", Util.trim(custRelation));
		map.put("approveTime", Util.trim(approveTime));
		map.put("currentApplyAmt", Util.trim(currentApplyAmt));
		map.put("property", Util.trim(property));
		map.put("lnSubject", Util.trim(lnSubject));
		map.put("payDeadline", Util.trim(payDeadline));
		map.put("guarantor", Util.trim(guarantor));
		map.put("danbowDscr", Util.trim(danbowDscr));
		map.put("rateDscr", Util.trim(rateDscr));
		map.put("otherDscr", Util.trim(otherDscr));
		map.put("memoDscr", Util.trim(memoDscr));

		return map;
	}

	/**
	 * 取得property對應
	 * 
	 * @param proPerty
	 *            property
	 * @return property對應
	 */
	private String getProPerty(String proPerty, Map<String, String> proPertyMap) {
		StringBuffer str = new StringBuffer();
		String[] temp = proPerty.split("\\|");
		for (String perty : temp) {
			str.append(Util.nullToSpace(proPertyMap.get(perty))).append("、");
		}
		if (str.length() == 0) {
			str.append("、");
		}

		return str.toString().substring(0, str.length() - 1);
	}

	/**
	 * 取得信評資料
	 * 
	 * @param l120s01a
	 *            L120S01A的資料
	 * @param l120s01cList
	 *            LIST<L120S01C>的資料
	 * @return 信評資料
	 */
	@SuppressWarnings("unused")
	private String getL120S01CData(List<L120S01C> l120s01cList) {
		String resultData = "";
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(RelatedAccountPanel.class);
		Map<String, String> crdTypeMap = codetypeService.findByCodeType(
				"CRDType", LMSUtil.getLocale().toString());
		StringBuffer str1 = new StringBuffer();
		StringBuffer str2 = new StringBuffer();
		StringBuffer str3 = new StringBuffer();
		// 免辦
		boolean noResult = false;
		boolean naResult = false;
		StringBuffer tempGrade = new StringBuffer();
		for (L120S01C l120s01c : l120s01cList) {
			// if (Util.nullToSpace(l120s01a.getCustId()).equals(
			// Util.nullToSpace(l120s01c.getCustId()))
			// && Util.nullToSpace(l120s01a.getDupNo()).equals(
			// Util.nullToSpace(l120s01c.getDupNo()))) {
			String crdType = Util.trim(l120s01c.getCrdType());
			String grade = Util.trim(l120s01c.getGrade());
			tempGrade.setLength(0);
			if ("NA".equals(crdType)) {
				naResult = true;
				// str.append(prop.getProperty("L120S01C.CRDTITLE01"))
				// .append(prop.getProperty("L120S05A.GRPGRRDN"))
				// .append("、");
			} else if ("DB".equals(crdType) || "DL".equals(crdType)
					|| "OU".equals(crdType)) {
				if (str3.length() != 0) {
					str3.append("、");
				}
				str3.append(prop.getProperty("L120S01C.CRDTITLE01"))
						.append(grade)
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(Util.nullToSpace(branchService
								.getBranchName(Util.nullToSpace(l120s01c
										.getCrdTBR())))).append("】");
			} else if ("NO".equals(crdType)) {
				noResult = true;
				// str.append(prop.getProperty("L120S01C.CRDTITLE04"))
				// .append(prop.getProperty("L120S01C.NOCRD01"))
				// .append("、");
			} else if ("M".equals(Util.getLeftStr(crdType, 1))) {
				if (Util.isNumeric(grade)) {
					tempGrade.append(grade)
							.append(prop.getProperty("tempGrade")).append(" ");
				}
				// 取得MOW等級之說明
				tempGrade.append(lmsService.getMowGradeName(prop, crdType,
						grade));
				if (str2.length() != 0) {
					str2.append("、");
				}
				str2.append(Util.nullToSpace(crdTypeMap.get(crdType)))
						.append(" : ")
						.append(tempGrade.toString())
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(Util.nullToSpace(branchService
								.getBranchName(Util.nullToSpace(l120s01c
										.getCrdTBR())))).append("】");
			} else if (UtilConstants.Casedoc.CrdType.MOODY.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.SAndP.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.Fitch.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.FitchTW.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.KBRA.equals(crdType)) {
				// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
				if (str1.length() != 0) {
					str1.append("、");
				}
				str1.append(grade)
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(Util.nullToSpace(crdTypeMap.get(l120s01c
								.getCrdType()))).append("】");
			}
			// }
		}

		/*
		 * 狀況1:MX+NA 狀況2:DX+NO 狀況3:NA+NO 狀況4:空 最後在加外部NM,NS,NP
		 */
		// 外部平等一定要串
		boolean result = false;
		StringBuffer total = new StringBuffer();
		// L120S01C.CRDTITLE04=模型評等 :
		if (str2.length() > 0) {

			// MXXX+外部
			// rptVariableMap.put("L120S01C.CRD",str2.toString());
			total.append(prop.getProperty("L120S01C.CRDTITLE04") + " " + str2);
			result = true;
		}
		// L120S01C.CRDTITLE01=信用評等 :
		if (str3.length() > 0) {
			// DXXX+外部
			total.append(total.length() > 0 ? "<br/>" : "");
			total.append(str3.toString());
			// rptVariableMap.put("L120S01C.CRD",str3.toString() + " " +
			// prop.getProperty("L120S01C.CRDTITLE04"));
			result = true;
		}

		// L120S01C.CRDTITLE05=外部評等 :
		if (str1.length() > 0) {
			total.append(total.length() > 0 ? "<br/>" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE05")
					+ str1.toString());
		}
		if (total.length() == 0) {
			// rptVariableMap.put("L120S01C.CRD",prop.getProperty("L120S01C.NOCRD01"));
			total.append(prop.getProperty("L120S01C.NOCRD01"));
			result = true;
		}
		// rptVariableMap.put("L120S01C.CRD",(!result ? "" :
		// (rptVariableMap.get("L120S01C.CRD") + "\n"))+crdtitle05 +
		// str1.toString());
		resultData = total.toString();
		return resultData;
	}

	@Override
	public String checkDWDate(String queryDateS, String queryDateE,
			String str_LMS1205S07Form03) {
		JSONObject formJson;
		{// check DW date
			if (str_LMS1205S07Form03 != null) {
				formJson = JSONObject.fromObject(str_LMS1205S07Form03);
			} else {
				formJson = new JSONObject();
			}

			// 設定日期
			formJson.put("queryDateS0", queryDateS);
			formJson.put("queryDateE0", queryDateE);
		}
		int getKind = checkDate(queryDateS, queryDateE, formJson);
		return showError(getKind, queryDateS, queryDateE, formJson);
	}

	@Override
	public List<L120S04A> saveL120s04a2(String mainId, String queryDateS,
			String queryDateE, String keyCustId, String keyDupNo,
			String keyCustName) {

		List<L120S04A> exist_list = l120s04aDao.findByMainIdKeyCustIdDupNo(
				mainId, keyCustId, keyDupNo);
		if (exist_list != null && exist_list.size() > 0) {
			// 如果有資料就刪除再引進
			deleteListL120s04a(exist_list);
		}

		List<L120S04A> list = findL120s04a(mainId, keyCustId, keyDupNo,
				keyCustName, queryDateS, queryDateE);

		// 設定文件檢核狀態
		for (L120S04A l120s04a : list) {
			l120s04a.setChkYN(UtilConstants.DEFAULT.是);
			l120s04a.setKeyCustId(keyCustId);
			l120s04a.setKeyDupNo(keyDupNo);
		}
		// 儲存
		if (!list.isEmpty()) {
			l120s04aDao.save(list);
		}
		return list;
	}

	private void deleteListL120s04a(List<L120S04A> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S04A model : list) {
			listOid.add(model.getOid());
		}
		l120s04aDao.delete(list);
	}

	private String showError(int getKind, String queryDateS, String queryDateE,
			JSONObject formJson) {
		StringBuilder sbMsg = new StringBuilder();
		StringBuilder sbDate = new StringBuilder();
		StringBuilder sbDbDate = new StringBuilder();
		String popMsg1 = UtilConstants.Mark.SPACE;
		String popMsg2 = UtilConstants.Mark.SPACE;

		String SIGN = "/";

		if (getKind == 0) {
			return "";
		} else if (getKind == 1 || getKind == 2 || getKind == 3) {
			popMsg1 = "L1205S07.error1";// L1205S07.error1 = 查詢起始年月
			sbDate.append(queryDateS.substring(0, queryDateS.length() - 3)
					.replaceAll("-", SIGN));
			if (getKind == 3) {
				popMsg2 = "L1205S07.error3";
			} else {
				sbDbDate.append(
						getYMDofDate(Util.trim(formJson.get("MIN_CYC_MN")),
								UtilConstants.DEFAULT.是))
						.append(SIGN)
						.append(getYMDofDate(
								Util.trim(formJson.get("MIN_CYC_MN")), "M"));
				if (getKind == 1) {
					popMsg2 = "L1205S07.error8";
				} else if (getKind == 2) {
					popMsg2 = "L1205S07.error4";
				}
			}
		} else if (getKind == 4 || getKind == 5 || getKind == 6) {
			popMsg1 = "L1205S07.error2";// L1205S07.error2 = 查詢迄至年月
			sbDate.append(queryDateE.substring(0, queryDateE.length() - 3)
					.replaceAll("-", SIGN));
			if (getKind == 6) {
				popMsg2 = "L1205S07.error5";
			} else {
				sbDbDate.append(
						getYMDofDate(Util.trim(formJson.get("MAX_CYC_MN")),
								UtilConstants.DEFAULT.是))
						.append(SIGN)
						.append(getYMDofDate(
								Util.trim(formJson.get("MAX_CYC_MN")), "M"));
				if (getKind == 4) {
					popMsg2 = "L1205S07.error6";
				} else if (getKind == 5) {
					popMsg2 = "L1205S07.error10";
				}
			}
		}
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(RelatedAccountPanel.class);
		sbMsg.append(pop.getProperty(popMsg1)).append(sbDate.toString())
				.append(pop.getProperty(popMsg2)).append(sbDbDate.toString())
				.append("\t").append(pop.getProperty("L1205S07.error7"));
		return sbMsg.toString();
	}

	private String getYMDofDate(String date, String dateFormate) {
		String dateArray[] = date.split("-");
		dateFormate = Util.trim(dateFormate).toUpperCase();
		if (dateArray.length >= 3) {
			if ("Y".equals(dateFormate)) {
				// Year
				return dateArray[0];
			} else if ("M".equals(dateFormate)) {
				// Month
				return dateArray[1];
			} else if ("D".equals(dateFormate)) {
				// Day
				return dateArray[2];
			}
		}
		return UtilConstants.Mark.SPACE;
	}

	private int checkDate(String queryDateS, String queryDateE, JSONObject json) {
		List<?> rows = dwdbBASEService.findDWADM_MD_CUPFM_OTS_selDate();
		Iterator<?> it = rows.iterator();
		String MAX_CYC_MN = "";
		String MIN_CYC_MN = "";
		if (it.hasNext()) {
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			MAX_CYC_MN = Util.trim(Util.nullToSpace(dataMap.get("MAX_CYC_MN")));
			MIN_CYC_MN = Util.trim(Util.nullToSpace(dataMap.get("MIN_CYC_MN")));
			json.put("MAX_CYC_MN", MAX_CYC_MN);
			json.put("MIN_CYC_MN", MIN_CYC_MN);
			// compareTo用法
			// 使用者輸入資料小於資料查詢-> >0
			// 使用者輸入資料大於資料查詢-> <0
			// 使用者輸入資料等於資料查詢-> =0
			if (CapDate.parseDate(MIN_CYC_MN).compareTo(
					CapDate.parseDate(queryDateS)) > 0
					// || CapDate.parseDate(MIN_CYC_MN).compareTo(
					// CapDate.parseDate(queryDateS)) < 0
					|| "".equals(MIN_CYC_MN)) {
				// if (CapDate.parseDate(MIN_CYC_MN).compareTo(
				// CapDate.parseDate(queryDateS)) < 0) {
				// return 1;
				// }
				// else
				// 當使用者輸入起日小於資料查詢起日
				if (CapDate.parseDate(MIN_CYC_MN).compareTo(
						CapDate.parseDate(queryDateS)) > 0) {
					return 2;
				} else {
					return 3;
				}
			}
			if (CapDate.parseDate(MAX_CYC_MN).compareTo(
					CapDate.parseDate(queryDateE)) < 0
					// || CapDate.parseDate(MAX_CYC_MN).compareTo(
					// CapDate.parseDate(queryDateE)) > 0
					|| "".equals(MAX_CYC_MN)) {
				// 當使用者輸入迄日大於資料查詢迄日
				if (CapDate.parseDate(MAX_CYC_MN).compareTo(
						CapDate.parseDate(queryDateE)) < 0) {
					return 4;
				}
				// if (CapDate.parseDate(MAX_CYC_MN).compareTo(
				// CapDate.parseDate(queryDateE)) > 0) {
				// return 5;
				// }
				else {
					return 6;
				}
			}
		}
		return 0;
	}

	private void defaultL120s04a(L120S04A l120s04a, String MainId,
			String queryDateS, String queryDateE) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		l120s04a.setMainId(MainId);
		l120s04a.setCreator(user.getUserId());
		l120s04a.setCreateTime(CapDate.getCurrentTimestamp());
		l120s04a.setQueryDateS(Util.parseDate(queryDateS));
		l120s04a.setQueryDateE(Util.parseDate(queryDateE));
		l120s04a.setCreateBY(UtilConstants.Casedoc.L120s04aCreateBY.系統產生);
		l120s04a.setPrtFlag(UtilConstants.Casedoc.L120s04aPrtFlag.要列印);
	}

	private String getAllCust(String custid, String dupNo) {
		StringBuilder strb = new StringBuilder();
		if ("0".equals(dupNo)) {
			dupNo = "";
		}
		return strb.append(custid).append(dupNo).toString();
	}

	private List<L120S04A> findL120s04a(String mainId, String custId,
			String dupNo, String custName, String queryDateS, String queryDateE) {
		L120M01A meta = this.findL120m01aByMainId(mainId);
		// 依目前簽案行做計算幣別
		BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());
		List<L120S04A> list = new ArrayList<L120S04A>();
		Map<String, L120S04A> map = new HashMap<String, L120S04A>();
		List<String> listCname = new ArrayList<String>();
		L120S04A l120s04a = new L120S04A();
		defaultL120s04a(l120s04a, mainId, queryDateS, queryDateE);
		List<?> rows1 = this.misCustdataService.findCustdataForList(custId,
				dupNo);
		Iterator<?> it1 = rows1.iterator();
		// 負責人統編
		String manageId = custId;
		// 負責人重覆序號
		String manageDup = dupNo;
		// 負責人名稱
		String manageName = custName;

		l120s04a.setCustId(manageId);
		l120s04a.setDupNo(manageDup);
		l120s04a.setCustName(manageName);
		l120s04a.setCustRelation("1");
		map.put(getAllCust(manageId, manageDup), l120s04a);
		listCname.add(getAllCust(manageId, manageDup));
		if (it1.hasNext()) {
			l120s04a = new L120S04A();
			defaultL120s04a(l120s04a, mainId, queryDateS, queryDateE);
			Map<?, ?> dataMap1 = (Map<?, ?>) it1.next();
			manageId = Util.trim(Util.nullToSpace(dataMap1.get("SUP1ID")));
			manageDup = Util.trim(Util.nullToSpace(dataMap1.get("SUP1DUPNO")));
			if ("".equals(manageDup)) {
				// 重覆序號為空則設為0
				manageDup = "0";
			}
			manageName = Util.trim(Util.nullToSpace(dataMap1.get("SUP1CNM")));
			if (!Util.isEmpty(manageId) && !Util.isEmpty(manageDup)) {
				l120s04a.setCustId(manageId);
				l120s04a.setDupNo(manageDup);
				l120s04a.setCustName(manageName);
				l120s04a.setCustRelation("2");
				map.put(getAllCust(manageId, manageDup), l120s04a);
				listCname.add(getAllCust(manageId, manageDup));
			}
		}
		// 集團代號
		String gid = "";
		// 集團名稱
		String gname = "";
		List<?> rows2 = this.misGrpcmpService.findGrpcmpForGrpid(custId, dupNo);
		Iterator<?> it2 = rows2.iterator();
		while (it2.hasNext()) {
			Map<?, ?> dataMap2 = (Map<?, ?>) it2.next();
			gid = Util.trim(Util.nullToSpace(dataMap2.get("GRPID")));
		}

		if (!"".equals(gid)) {
			List<?> rows3 = this.misGrpdtlService.findGrpdtlForGrpnm(gid);
			Iterator<?> it3 = rows3.iterator();
			while (it3.hasNext()) {
				Map<?, ?> dataMap3 = (Map<?, ?>) it3.next();
				gname = Util.trim(Util.nullToSpace(dataMap3.get("GRPNM")));
			}

			// 讀取集團明細 .......
			List<?> rows4 = this.misGrpcmpService.findGrpcmpForCmpnm(gid);
			Iterator<?> it4 = rows4.iterator();
			while (it4.hasNext()) {
				Map<?, ?> dataMap4 = (Map<?, ?>) it4.next();
				manageId = Util.trim(Util.nullToSpace(dataMap4.get("BAN")));
				manageDup = Util.trim(Util.nullToSpace(dataMap4.get("DUPNO")));
				manageName = Util.toSemiCharString(Util.trim(String
						.valueOf(dataMap4.get("CMPNM"))));
				l120s04a = map.get(getAllCust(manageId, manageDup));
				// l120s04a = this.findL120s04aByUniqueKey(mainId, manageId,
				// manageDup, manageName);
				if (l120s04a == null) {
					l120s04a = new L120S04A();
					defaultL120s04a(l120s04a, mainId, queryDateS, queryDateE);
				}
				l120s04a.setCustId(manageId);
				l120s04a.setDupNo(manageDup);
				l120s04a.setCustName(manageName);
				if (Util.isEmpty(Util.trim(l120s04a.getCustRelation()))) {
					l120s04a.setCustRelation("5");
				} else {
					if (l120s04a.getCustRelation().indexOf("5") == -1) {
						l120s04a.setCustRelation(l120s04a.getCustRelation()
								+ ",5");
					}
				}
				map.put(getAllCust(manageId, manageDup), l120s04a);
				listCname.add(getAllCust(manageId, manageDup));
			}
		}
		List<?> rows5 = this.misElcrcoService.findElcrecomByIdDupno2(custId,
				dupNo);
		Iterator<?> it5 = rows5.iterator();
		while (it5.hasNext()) {
			Map<?, ?> dataMap5 = (Map<?, ?>) it5.next();
			manageId = Util.trim(Util.nullToSpace(dataMap5.get("BAN")));
			manageDup = Util.trim(Util.nullToSpace(dataMap5.get("DUPNO")));
			manageName = Util.toSemiCharString(Util.trim(String
					.valueOf(dataMap5.get("CNAME"))));
			l120s04a = map.get(getAllCust(manageId, manageDup));
			// l120s04a = this.findL120s04aByUniqueKey(mainId, manageId,
			// manageDup, manageName);
			if (l120s04a == null) {
				l120s04a = new L120S04A();
				defaultL120s04a(l120s04a, mainId, queryDateS, queryDateE);
			}
			l120s04a.setCustId(manageId);
			l120s04a.setDupNo(manageDup);
			l120s04a.setCustName(manageName);
			if (Util.isEmpty(Util.trim(l120s04a.getCustRelation()))) {
				l120s04a.setCustRelation("6");
			} else {
				if (l120s04a.getCustRelation().indexOf("6") == -1) {
					l120s04a.setCustRelation(l120s04a.getCustRelation() + ",6");
				}
			}
			// l120s04a.setCustRelation("6");
			map.put(getAllCust(manageId, manageDup), l120s04a);
			listCname.add(getAllCust(manageId, manageDup));
		}
		for (String x : listCname) {
			L120S04A model = map.get(x);
			list.add(queryL120s04a_reQuery_single(model));
		}
		return list;
	}

	@Override
	public List<Map<String, String>> getKeyCustId(String l120m01a_mainId) {
		List<Map<String, String>> r = new ArrayList<Map<String, String>>();

		List<L140M01A> list = l140m01aDao
				.findL140m01aListByL120m01cMainId(l120m01a_mainId);
		HashMap<String, String> m = new HashMap<String, String>();
		for (L140M01A l140m01a : list) {
			m.put(LMSUtil.getCustKey_len10custId(l140m01a.getCustId(),
					l140m01a.getDupNo()), Util.trim(l140m01a.getCustName()));
		}

		for (String custKey : m.keySet()) {
			HashMap<String, String> o = new HashMap<String, String>();
			o.put("CUSTID", custKey.substring(0, 10));
			o.put("DUPNO", custKey.substring(10, custKey.length()));
			o.put("CUSTNAME", m.get(custKey));
			r.add(o);
		}
		return r;

	}

	@Override
	public String getKeyCustName(String l120m01a_mainId,
			String choose_keyCustId, String choose_keyDupNo) {
		String r = "";
		for (Map<String, String> map : getKeyCustId(l120m01a_mainId)) {
			String keyCustId = Util.trim(map.get("CUSTID"));
			String keyDupNo = Util.trim(map.get("DUPNO"));
			String keyCustName = Util.trim(map.get("CUSTNAME"));
			if (Util.equals(choose_keyCustId, keyCustId)
					&& Util.equals(choose_keyDupNo, keyDupNo)) {
				r = keyCustName;
				break;
			}
		}
		return r;
	}

	@Override
	public L120S04A findL120s04aByOid(String oid) {
		return l120s04aDao.findByOid(oid);
	}

	@Override
	public L120S04B findL120s04bByOid(String oid) {
		return l120s04bDao.findByOid(oid);
	}

	@Override
	public L120S04C findL120s04cByOid(String oid) {
		return l120s04cDao.findByOid(oid);
	}

	@Override
	public CapAjaxFormResult queryL120s04a_detail(L120S04A l120s04a)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(RelatedAccountPanel.class);

		CapAjaxFormResult myForm2Result = DataParse.toResult(l120s04a);
		String[] strs = l120s04a.getCustRelation().split(",");
		List<String> list = new ArrayList<String>();
		for (int i = 0; i < strs.length; i++) {
			list.add(strs[i]);
		}

		StringBuilder relDateS = new StringBuilder();
		StringBuilder relDateE = new StringBuilder();
		StringBuilder depMemo = new StringBuilder();
		StringBuilder loanQMemo = new StringBuilder();
		StringBuilder loanABMemo = new StringBuilder();
		StringBuilder exchgMemo = new StringBuilder();
		StringBuilder derMemo = new StringBuilder();
		StringBuilder trustMemo = new StringBuilder();
		StringBuilder wealthMemo = new StringBuilder();
		StringBuilder salaryMemo = new StringBuilder();
		StringBuilder cardComMemo = new StringBuilder();
		StringBuilder cardBrnMemo = new StringBuilder();
		StringBuilder GEBMemo = new StringBuilder();
		StringBuilder GEBLCMemo = new StringBuilder();
		StringBuilder profitMemo = new StringBuilder();

		if (Util.isEmpty(l120s04a.getQueryDateS())) {
			relDateS.append(UtilConstants.Mark.SPACE);
		} else {
			relDateS.append(Util.isEmpty(CapDate.formatDate(
					l120s04a.getQueryDateS(),
					UtilConstants.DateFormat.YYYY_MM_DD)) ? UtilConstants.Mark.SPACE
					: CapDate.formatDate(l120s04a.getQueryDateS(),
							UtilConstants.DateFormat.YYYY_MM_DD)
							.substring(0, 7));
		}
		if (Util.isEmpty(l120s04a.getQueryDateE())) {
			relDateE.append(UtilConstants.Mark.SPACE);
		} else {
			relDateE.append(Util.isEmpty(CapDate.formatDate(
					l120s04a.getQueryDateE(),
					UtilConstants.DateFormat.YYYY_MM_DD)) ? UtilConstants.Mark.SPACE
					: CapDate.formatDate(l120s04a.getQueryDateE(),
							UtilConstants.DateFormat.YYYY_MM_DD)
							.substring(0, 7));
		}

		// 開始設定值到前端欄位
		if (Util.isNotEmpty(l120s04a.getQueryDateS())
				&& Util.isNotEmpty(l120s04a.getQueryDateS())) {
			myForm2Result
					.set("queryDateS_",
							Util.isEmpty(CapDate.formatDate(
									l120s04a.getQueryDateS(),
									UtilConstants.DateFormat.YYYY_MM_DD)) ? UtilConstants.Mark.SPACE
									: CapDate
											.formatDate(
													l120s04a.getQueryDateS(),
													UtilConstants.DateFormat.YYYY_MM_DD)
											.substring(0, 7));
			myForm2Result
					.set("queryDateE_",
							Util.isEmpty(CapDate.formatDate(
									l120s04a.getQueryDateE(),
									UtilConstants.DateFormat.YYYY_MM_DD)) ? UtilConstants.Mark.SPACE
									: CapDate
											.formatDate(
													l120s04a.getQueryDateE(),
													UtilConstants.DateFormat.YYYY_MM_DD)
											.substring(0, 7));

			depMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(prop.getProperty("L1205S07.form1"));
			loanQMemo.append(relDateE).append(UtilConstants.Mark.HTMLSPACE)
					.append(prop.getProperty("L1205S07.form2"));
			loanABMemo.append(relDateS).append("~").append(relDateE);
			exchgMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(prop.getProperty("L1205S07.form3"));
			derMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(prop.getProperty("L1205S07.form3"));
			trustMemo.append(relDateE).append(UtilConstants.Mark.HTMLSPACE)
					.append(prop.getProperty("L1205S07.form4"));
			wealthMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(prop.getProperty("L1205S07.form5"));
			salaryMemo.append(relDateE).append(UtilConstants.Mark.HTMLSPACE)
					.append(prop.getProperty("L1205S07.form6"));
			cardComMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(prop.getProperty("L1205S07.form7"));
			cardBrnMemo.append(relDateE).append(UtilConstants.Mark.HTMLSPACE)
					.append(prop.getProperty("L1205S07.form8"));
			GEBMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(prop.getProperty("L1205S07.form9"));
			GEBLCMemo.append(relDateS).append("~").append(relDateE);
			profitMemo.append(relDateS).append("~").append(relDateE);

			// 設定資料基期
			myForm2Result.set("depMemo", depMemo.toString());
			myForm2Result.set("loanQMemo", loanQMemo.toString());
			myForm2Result.set("loanABMemo", loanABMemo.toString());
			myForm2Result.set("exchgMemo", exchgMemo.toString());
			myForm2Result.set("derMemo", derMemo.toString());
			myForm2Result.set("trustMemo", trustMemo.toString());
			myForm2Result.set("wealthMemo", wealthMemo.toString());
			myForm2Result.set("salaryMemo", salaryMemo.toString());
			myForm2Result.set("cardComMemo", cardComMemo.toString());
			myForm2Result.set("cardComMemo1", cardComMemo.toString());
			myForm2Result.set("cardBrnMemo", cardBrnMemo.toString());
			myForm2Result.set("GEBMemo", GEBMemo.toString());
			myForm2Result.set("GEBLCMemo", GEBLCMemo.toString());
			myForm2Result.set("profitMemo", profitMemo.toString());
		}
		myForm2Result.set(
				"createBY2",
				UtilConstants.Casedoc.L120s04aCreateBY.系統產生.equals(l120s04a
						.getCreateBY()) ? prop
						.getProperty("L1205S07.createBY1") : prop
						.getProperty("L1205S07.createBY2"));
		myForm2Result.set("prtFlag2",
				UtilConstants.Casedoc.L120s04aPrtFlag.要列印.equals(l120s04a
						.getPrtFlag()) ? prop.getProperty("L1205S07.prtFlag1")
						: prop.getProperty("L1205S07.prtFlag2"));
		myForm2Result.set("custRelation", list);
		JSONObject json = new JSONObject();
		if (Util.isNotEmpty(l120s04a.getQueryDateS())) {
			json.put(
					"queryDateS",
					Util.isEmpty(CapDate.formatDate(l120s04a.getQueryDateS(),
							UtilConstants.DateFormat.YYYY_MM_DD)) ? UtilConstants.Mark.SPACE
							: CapDate.formatDate(l120s04a.getQueryDateS(),
									UtilConstants.DateFormat.YYYY_MM_DD)
									.substring(0, 7));
		}
		if (Util.isNotEmpty(l120s04a.getQueryDateE())) {
			json.put(
					"queryDateE",
					Util.isEmpty(CapDate.formatDate(l120s04a.getQueryDateE(),
							UtilConstants.DateFormat.YYYY_MM_DD)) ? UtilConstants.Mark.SPACE
							: CapDate.formatDate(l120s04a.getQueryDateE(),
									UtilConstants.DateFormat.YYYY_MM_DD)
									.substring(0, 7));
		}
		result.set("LMS1205S07Form03", json.toString());
		result.set("tLMS1205S07Form03", myForm2Result);
		return result;
	}

	private String getProfitRate(String l120s04cCol, int index, JSONObject json) {
		if ("profitRate".equals(l120s04cCol)) {
			// 若A-B+C = 0，則報酬率分母為零傳回N.A.
			if (LMSUtil
					.toBigDecimal(
							NumConverter.delCommaString(Util.trim(json
									.get("avgLoanAmt"))))
					.subtract(
							LMSUtil.toBigDecimal(NumConverter
									.delCommaString(Util.trim(json
											.get("rcvBuyAvgAmt")))))
					.add(LMSUtil.toBigDecimal(NumConverter.delCommaString(Util
							.trim(json.get("rcvSellAvgAmt")))))
					.compareTo(BigDecimal.ZERO) == 0) {
				return "N.A.";
			} else {
				return Util.trim(json.get(l120s04cCol));
			}
		} else {
			return Util.trim(json.get(l120s04cCol));
		}
	}

	private String setDocDate(String l120s04cCol, int index, JSONObject json) {
		// 第一欄、第二欄日期格式為yyyy顯示，第三欄日期格式為yyyy-MM顯示
		if ("docDate1".equals(l120s04cCol + index)
				|| "docDate2".equals(l120s04cCol + index)
				|| "docDate4".equals(l120s04cCol + index)
				|| "docDate5".equals(l120s04cCol + index)
				|| "docDate3".equals(l120s04cCol + index)
				|| "docDate6".equals(l120s04cCol + index)) {
			return Util.trim(json.get(l120s04cCol));
		} else {
			return getProfitRate(l120s04cCol, index, json);
		}
	}

	private IResult queryL120S04c(List<L120S04C> listKind,
			List<String> dateKind, String type) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] l120S04cCol = new String[] { "docDate", "docDateE",
				"avgDepositAmt", "avgLoanAmt", "rcvBuyAvgAmt", "rcvSellAvgAmt",
				"exportAmt", "importAmt", "profitAmt", "profitSalaryAmt",
				"profitTrustFdtaAmt", "profitRate", "wmAmt", "oid" };
		int index = 0;
		if (!dateKind.isEmpty() && !listKind.isEmpty()) {
			if ("1".equals(type)) {
				// 借款人與本行往來實績彙總表
				index = 1;
			} else if ("2".equals(type)) {
				// 借款人暨關係戶與本行往來實績彙總表
				index = 4;
			}
			for (String docDateS : dateKind) {
				for (L120S04C l120s04c : listKind) {
					JSONObject json = DataParse.toJSON(l120s04c);
					if (docDateS.equals(Util.trim(l120s04c.getDocDate()))) {
						for (String l120s04cCol : l120S04cCol) {
							result.set(l120s04cCol + index,
									setDocDate(l120s04cCol, index, json));
						}
						index++;
						break;
					}
				}
			}
		}
		return result;
	}

	@Override
	public CapAjaxFormResult queryL120s04b_detail(L120S04B l120s04b,
			String keyCustName) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		CapAjaxFormResult formL120s04b = new CapAjaxFormResult();
		if (l120s04b != null) {
			formL120s04b.add(DataParse.toResult(l120s04b));
			formL120s04b.set("grpYear", NumConverter.delCommaString(Util
					.trim(formL120s04b.get("grpYear"))));
			formL120s04b
					.set("mainGrpAvgRate",
							(Util.isEmpty(Util.trim(l120s04b
									.getMainGrpAvgRate()))) ? "N.A."
									: NumConverter.addComma(l120s04b
											.getMainGrpAvgRate()));
			formL120s04b
					.set("demandAvgRate",
							(Util.isEmpty(Util.trim(l120s04b.getDemandAvgRate()))) ? "N.A."
									: NumConverter.addComma(l120s04b
											.getDemandAvgRate()));

			formL120s04b.set("mainGrpAvgRateM", (Util.isEmpty(Util
					.trim(l120s04b.getMainGrpAvgRateM()))) ? "N.A."
					: NumConverter.addComma(l120s04b.getMainGrpAvgRateM()));

			formL120s04b.set("mainGrpAvgRateR", (Util.isEmpty(Util
					.trim(l120s04b.getMainGrpAvgRateR()))) ? "N.A."
					: NumConverter.addComma(l120s04b.getMainGrpAvgRateR()));

			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			boolean newGrpGrade = lmsService.isNewGrpGrade(
					Util.trim(l120s04b.getGrpYear()), true);
			String defultNoGrade = lmsService.getGrpNoGrade(newGrpGrade);

			if (newGrpGrade) {
				// (大A)、(中B)....
				formL120s04b.set(
						"grpSizeLvlShow",
						lmsService.getGrpSizeLvlShow(
								Util.trim(l120s04b.getGrpSize()),
								Util.trim(l120s04b.getGrpLevel())));
			} else {
				formL120s04b.set("grpSizeLvlShow", "");
			}

		}

		List<L120S04C> list = new ArrayList<L120S04C>();
		if (l120s04b != null) {
			list = l120s04cDao.findByMainIdKeyCustIdDupNo(l120s04b.getMainId(),
					l120s04b.getKeyCustId(), l120s04b.getKeyDupNo());
		}
		// 借款人與本行往來實績彙總表list
		List<L120S04C> listKind1 = new ArrayList<L120S04C>();
		// 借款人與本行往來實績彙總表資料年月(起)list
		List<String> dateKind1 = new ArrayList<String>();
		// 借款人暨關係戶與本行往來實績彙總表list
		List<L120S04C> listKind2 = new ArrayList<L120S04C>();
		// 借款人暨關係戶與本行往來實績彙總表資料年月(起)list
		List<String> dateKind2 = new ArrayList<String>();
		if (!list.isEmpty()) {
			for (L120S04C l120s04c : list) {
				if ("1".equals(Util.trim(l120s04c.getDocKind()))) {
					// 借款人與本行往來實績彙總表
					listKind1.add(l120s04c);
					String docDate = Util.trim(l120s04c.getDocDate());
					if (Util.isNotEmpty(docDate)) {
						dateKind1.add(docDate);
					}
				} else if ("2".equals(Util.trim(l120s04c.getDocKind()))) {
					// 借款人暨關係戶與本行往來實績彙總表
					listKind2.add(l120s04c);
					String docDate = l120s04c.getDocDate();
					if (Util.isNotEmpty(docDate)) {
						dateKind2.add(docDate);
					}
				}
			}
		}

		formL120s04b.set("custName04C", Util.trim(keyCustName));
		// 進行排序
		Collections.sort(dateKind1);
		Collections.sort(dateKind2);
		formL120s04b.add(queryL120S04c(listKind1, dateKind1, "1"));
		formL120s04b.add(queryL120S04c(listKind2, dateKind2, "2"));
		result.set("formL120s04b", formL120s04b);

		return result;
	}

	@Override
	public List<L120S04A> findL120s04aByMainIdKeyCustIdDupNodPrtFlag(
			String mainId, String keyCustId, String keyDupNo, String prtFlag) {
		return l120s04aDao.findByMainIdKeyCustIdDupNoPrtFlag(mainId, keyCustId,
				keyDupNo, prtFlag);
	}

	@Override
	public L120S04A rQueryL120s04a(L120S04A l120s04a) {
		L120S04A model = queryL120s04a_reQuery_single(l120s04a);
		l120s04aDao.save(model);
		return model;
	}

	/**
	 * LMS1201ServiceImpl::findL120s04a 有用到 l120s04a.setCardNoneCommercial 但在
	 * LMSM02FormHandler::rQueryL120s04a 未呼叫 setCardNoneCommercial
	 * 
	 * @param model
	 * @return
	 */
	private L120S04A queryL120s04a_reQuery_single(L120S04A model) {
		// 活期存款
		long depTime = 0;
		// 定期存款
		long depFixed = 0;
		// 額度
		long loanQuota = 0;
		// 平均餘額
		long loanAvgBal = 0;
		// 平均動用率
		BigDecimal loanAvgRate = new BigDecimal("0");
		// 進口筆數
		long exchgImpRec = 0;
		// 進口金額
		long exchgImpAmt = 0;
		// 出口筆數
		long exchgExpRec = 0;
		// 出口金額
		long exchgExpAmt = 0;
		// 匯出筆數
		long exchgOutRec = 0;
		// 匯出金額
		long exchgOutAmt = 0;
		// 匯入筆數
		long exchgInRec = 0;
		// 匯入金額
		long exchgInAmt = 0;
		// 選擇權
		long derOption = 0;
		// 利率交換
		long derRateExchg = 0;
		// 換匯換利
		long derCCS = 0;
		// 遠匯
		// String derDraft = "0";
		// 遠匯(含SWAP)
		long derSWAP = 0;
		// 國內外基金債券
		long trustBond = 0;
		// 基金保管
		long trustFund = 0;
		// 集管
		long trustSetAcct = 0;
		// 有價證券信託
		// String trustSecurities = "0";
		// 不動產信託
		// String trustREITs = "0";
		// 福儲信託
		// String trustWelDep = "0";
		// 其他信託
		long trustOther = 0;
		// 信託
		long wealthTrust = 0;
		// 保險佣金
		long wealthInsCom = 0;
		// 雙元投資
		long wealthInvest = 0;
		// 薪轉戶數
		long salaryRec = 0;
		// 定期定額戶數
		long salaryFixed = 0;
		// 房貸戶數
		long salaryMortgage = 0;
		// 消貸戶數
		long salaryConsumption = 0;
		// 信用卡持卡人數
		long salaryCard = 0;
		// 個人網銀戶數
		long salaryNetwork = 0;
		// 商務卡
		long cardCommercial = 0;
		// 非商務卡
		long cardNoneCommercial = 0;
		// 聯名卡
		String cardCoBranded = "N";
		// 台幣交易筆數
		long GEBTWDRec = 0;
		// 外幣交易筆數
		long GEBOTHRec = 0;
		// 信用狀交易筆數
		long GEBLCRec = 0;
		// 利潤貢獻度
		// String profit = "0";
		// 活期存款
		String depTime1 = "0";
		// 定期存款
		String depFixed1 = "0";
		// 額度
		String loanQuota1 = "0";
		// 平均餘額
		String loanAvgBal1 = "0";
		// 平均動用率
		String loanAvgRate1 = "0";
		// 進口筆數
		String exchgImpRec1 = "0";
		// 進口金額
		String exchgImpAmt1 = "0";
		// 出口筆數
		String exchgExpRec1 = "0";
		// 出口金額
		String exchgExpAmt1 = "0";
		// 匯出筆數
		String exchgOutRec1 = "0";
		// 匯出金額
		String exchgOutAmt1 = "0";
		// 匯入筆數
		String exchgInRec1 = "0";
		// 匯入金額
		String exchgInAmt1 = "0";
		// 選擇權
		String derOption1 = "0";
		// 利率交換
		String derRateExchg1 = "0";
		// 換匯換利
		String derCCS1 = "0";
		// 遠匯
		// String derDraft1 = "0";
		// 遠匯(含SWAP)
		String derSWAP1 = "0";
		// 國內外基金債券
		String trustBond1 = "0";
		// 基金保管
		String trustFund1 = "0";
		// 集管
		String trustSetAcct1 = "0";
		// 有價證券信託
		// String trustSecurities1 = "0";
		// 不動產信託
		// String trustREITs1 = "0";
		// 福儲信託
		// String trustWelDep1 = "0";
		// 其他信託
		String trustOther1 = "0";
		// 信託
		String wealthTrust1 = "0";
		// 保險佣金
		String wealthInsCom1 = "0";
		// 雙元投資
		String wealthInvest1 = "0";
		// 薪轉戶數
		String salaryRec1 = "0";
		// 定期定額戶數
		String salaryFixed1 = "0";
		// 房貸戶數
		String salaryMortgage1 = "0";
		// 消貸戶數
		String salaryConsumption1 = "0";
		// 信用卡持卡人數
		String salaryCard1 = "0";
		// 個人網銀戶數
		String salaryNetwork1 = "0";
		// 商務卡
		String cardCommercial1 = "0";
		// 非商務卡
		String cardNoneCommercial1 = "0";
		// 聯名卡
		String cardCoBranded1 = "N";
		// 台幣交易筆數
		String GEBTWDRec1 = "0";
		// 外幣交易筆數
		String GEBOTHRec1 = "0";
		// 信用狀交易筆數
		String GEBLCRec1 = "0";
		// 利潤貢獻度
		String profit1 = "0";
		// 薪轉戶貢獻度
		String profitSalary1 = "0";

		// M-104-0172-001 二維表收信新增AR買方額度餘額
		String IN_LN_FA_B = "0";
		String IN_LN_FA_S = "0";
		long in_ln_fa_b = 0;
		long in_ln_fa_s = 0;
		String IN_LN_FACT_AMT_FA_S = "0";
		String IN_LN_FACT_AMT_FA_B = "0";
		long in_ln_fact_amt_fa_b = 0;
		long in_ln_fact_amt_fa_s = 0;

		String IN_CC_CC_ACT = UtilConstants.Mark.SPACE;
		String BR_CD = UtilConstants.Mark.SPACE;
		// =====================
		String queryDateS = TWNDate.toAD(model.getQueryDateS());
		String queryDateE = TWNDate.toAD(model.getQueryDateE());
		List<?> rows6 = this.dwdbBASEService.findDW_MD_CUPFM_OTS_selCYC_MN(
				model.getCustId(), model.getDupNo(), queryDateS, queryDateE);
		Iterator<?> it6 = rows6.iterator();
		// 聯名卡
		String _cardCoBranded = "N";
		while (it6.hasNext()) {
			Map<?, ?> dataMap6 = (Map<?, ?>) it6.next();
			String date = CapDate.formatDate(
					CapDate.parseDate(String.valueOf(dataMap6.get("CYC_MN"))),
					"yyyy-MM-dd");
			dataMap6.get("CUST_KEY");
			BR_CD = Util.nullToSpace(dataMap6.get("BR_CD")); // 999代表全行
			depTime1 = Util.nullToSpace(dataMap6.get("IN_DP"));
			dataMap6.get("IN_DP_G");
			depFixed1 = Util.nullToSpace(dataMap6.get("IN_CT"));
			loanAvgBal1 = Util.nullToSpace(dataMap6.get("IN_LN_USE"));
			loanAvgRate1 = Util.nullToSpace(dataMap6.get("IN_LN_AVGRT"));
			loanQuota1 = Util.nullToSpace(dataMap6.get("IN_LN_FACT_AMT"));
			exchgImpAmt1 = Util.nullToSpace(dataMap6.get("IN_IM"));
			exchgExpAmt1 = Util.nullToSpace(dataMap6.get("IN_EX_BP"));
			exchgImpRec1 = Util.nullToSpace(dataMap6.get("IN_IM_TXN"));
			exchgExpRec1 = Util.nullToSpace(dataMap6.get("IN_EX_TXN"));
			exchgOutAmt1 = Util.nullToSpace(dataMap6.get("IN_OR"));
			exchgInAmt1 = Util.nullToSpace(dataMap6.get("IN_IR"));
			exchgOutRec1 = Util.nullToSpace(dataMap6.get("IN_OR_TXN"));
			exchgInRec1 = Util.nullToSpace(dataMap6.get("IN_IR_TXN"));
			derOption1 = Util.nullToSpace(dataMap6.get("IN_DV_OP"));
			derRateExchg1 = Util.nullToSpace(dataMap6.get("IN_DV_RE"));
			derCCS1 = Util.nullToSpace(dataMap6.get("IN_DV_ER"));
			derSWAP1 = Util.nullToSpace(dataMap6.get("IN_DV_FR"));
			wealthTrust1 = Util.nullToSpace(dataMap6.get("IN_WM_F_FEE"));
			wealthInsCom1 = Util.nullToSpace(dataMap6.get("IN_WM_I_FEE"));
			wealthInvest1 = Util.nullToSpace(dataMap6.get("IN_WM_S_FEE"));
			trustFund1 = Util.nullToSpace(dataMap6.get("IN_TR_FU"));
			trustSetAcct1 = Util.nullToSpace(dataMap6.get("IN_TR_CF"));
			trustBond1 = Util.nullToSpace(dataMap6.get("IN_TR_SC"));
			trustOther1 = Util.nullToSpace(dataMap6.get("IN_TR_OTS"));
			cardCommercial1 = Util.nullToSpace(dataMap6.get("IN_CC_CC"));
			cardNoneCommercial1 = Util.nullToSpace(dataMap6.get("IN_CC_IV"));
			IN_CC_CC_ACT = Util.nullToSpace(dataMap6.get("IN_CC_CC_ACT"));
			cardCoBranded1 = Util.nullToSpace(dataMap6.get("IN_CC_JC_ACT"));
			salaryRec1 = Util.nullToSpace(dataMap6.get("IN_ST"));
			salaryFixed1 = Util.nullToSpace(dataMap6.get("IN_ST_FD"));
			salaryMortgage1 = Util.nullToSpace(dataMap6.get("IN_ST_LN_1"));
			salaryConsumption1 = Util.nullToSpace(dataMap6.get("IN_ST_LN_2"));
			salaryCard1 = Util.nullToSpace(dataMap6.get("IN_ST_CC"));
			salaryNetwork1 = Util.nullToSpace(dataMap6.get("IN_ST_NB"));
			GEBTWDRec1 = Util.nullToSpace(dataMap6.get("IN_GEB_NTD_TXN"));
			GEBOTHRec1 = Util.nullToSpace(dataMap6.get("IN_GEB_NTD_N_TXN"));
			GEBLCRec1 = Util.nullToSpace(dataMap6.get("IN_GEB_LC_TXN"));
			dataMap6.get("DW_CR_DT");
			dataMap6.get("DW_LST_MNT_DT");

			// M-104-0172-001 二維表收信新增AR買方額度餘額
			// 應收帳款無追索買方承購平均餘額(IN_LN_FA_B)
			IN_LN_FA_B = Util.nullToSpace(dataMap6.get("IN_LN_FA_B"));
			// 應收帳款無追索權賣方融資平均餘額(IN_LN_FA_S)
			IN_LN_FA_S = Util.nullToSpace(dataMap6.get("IN_LN_FA_S"));
			// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
			IN_LN_FACT_AMT_FA_B = Util.nullToSpace(dataMap6
					.get("IN_LN_FACT_AMT_FA_B"));
			// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
			IN_LN_FACT_AMT_FA_S = Util.nullToSpace(dataMap6
					.get("IN_LN_FACT_AMT_FA_S"));

			if (date.equals(queryDateE)) {
				loanQuota += Util.parseLong(loanQuota1);
				trustFund += Util.parseLong(trustFund1);
				trustSetAcct += Util.parseLong(trustSetAcct1);
				trustBond += Util.parseLong(trustBond1);
				trustOther += Util.parseLong(trustOther1);

				salaryRec += Util.parseLong(salaryRec1);
				salaryFixed += Util.parseLong(salaryFixed1);
				salaryMortgage += Util.parseLong(salaryMortgage1);
				salaryConsumption += Util.parseLong(salaryConsumption1);
				salaryCard += Util.parseLong(salaryCard1);
				salaryNetwork += Util.parseLong(salaryNetwork1);

				if (UtilConstants.DEFAULT.是.equals(cardCoBranded1)) {
					cardCoBranded = UtilConstants.DEFAULT.是;
				} else {
					cardCoBranded = UtilConstants.DEFAULT.否;
				}

				// M-104-0172-001 二維表收信新增AR買方額度餘額
				// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
				in_ln_fact_amt_fa_b += Util.parseLong(IN_LN_FACT_AMT_FA_B);
				// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
				in_ln_fact_amt_fa_s += Util.parseLong(IN_LN_FACT_AMT_FA_S);

			}
			if (UtilConstants.Casedoc.往來彙總用全行.equals(BR_CD)) {
				// 全行、用來計算平均動用率
				loanAvgRate = loanAvgRate.add(new BigDecimal(loanAvgRate1));
			}
			depTime += Util.parseLong(depTime1);
			depFixed += Util.parseLong(depFixed1);
			loanAvgBal += Util.parseLong(loanAvgBal1);
			exchgImpAmt += Util.parseLong(exchgImpAmt1);
			exchgExpAmt += Util.parseLong(exchgExpAmt1);
			exchgImpRec += Util.parseLong(exchgImpRec1);
			exchgExpRec += Util.parseLong(exchgExpRec1);
			exchgOutAmt += Util.parseLong(exchgOutAmt1);
			exchgInAmt += Util.parseLong(exchgInAmt1);
			exchgOutRec += Util.parseLong(exchgOutRec1);
			exchgInRec += Util.parseLong(exchgInRec1);
			derOption += Util.parseLong(derOption1);
			derRateExchg += Util.parseLong(derRateExchg1);
			derCCS += Util.parseLong(derCCS1);
			derSWAP += Util.parseLong(derSWAP1);
			wealthTrust += Util.parseLong(wealthTrust1);
			wealthInsCom += Util.parseLong(wealthInsCom1);
			wealthInvest += Util.parseLong(wealthInvest1);
			cardCommercial += Util.parseLong(cardCommercial1);
			cardNoneCommercial += Util.parseLong(cardNoneCommercial1);
			GEBTWDRec += Util.parseLong(GEBTWDRec1);
			GEBOTHRec += Util.parseLong(GEBOTHRec1);
			GEBLCRec += Util.parseLong(GEBLCRec1);

			// M-104-0172-001 二維表收信新增AR買方額度餘額
			// 應收帳款無追索買方承購平均餘額(IN_LN_FA_B)
			in_ln_fa_b += Util.parseLong(IN_LN_FA_B);
			// 應收帳款無追索權賣方融資平均餘額(IN_LN_FA_S)
			in_ln_fa_s += Util.parseLong(IN_LN_FA_S);

		}
		// 算起迄年月的差異月數
		int monDiff = (Util.parseInt(queryDateE.substring(0, 4)) * 12 + Util
				.parseInt(queryDateE.substring(5, 7)))
				- (Util.parseInt(queryDateS.substring(0, 4)) * 12 + Util
						.parseInt(queryDateS.substring(5, 7))) + 1;
		// 換算仟元
		model.setLoanQuota((long) Math.round((double) loanQuota / 1000));
		model.setTrustFund((long) Math.round((double) trustFund / 1000));
		model.setTrustSetAcct((long) Math.round((double) trustSetAcct / 1000));
		model.setTrustBond((long) Math.round((double) trustBond / 1000));
		model.setTrustOther((long) Math.round((double) trustOther / 1000));
		model.setExchgImpAmt((long) Math.round((double) exchgImpAmt / 1000));
		model.setExchgExpAmt((long) Math.round((double) exchgExpAmt / 1000));
		model.setExchgOutAmt((long) Math.round((double) exchgOutAmt / 1000));
		model.setExchgInAmt((long) Math.round((double) exchgInAmt / 1000));
		model.setDerOption((long) Math.round((double) derOption / 1000));
		model.setDerRateExchg((long) Math.round((double) derRateExchg / 1000));
		model.setDerCCS((long) Math.round((double) derCCS / 1000));
		model.setDerSWAP((long) Math.round((double) derSWAP / 1000));
		model.setWealthTrust((long) Math.round((double) wealthTrust / 1000));
		model.setWealthInsCom((long) Math.round((double) wealthInsCom / 1000));
		model.setWealthInvest((long) Math.round((double) wealthInvest / 1000));
		model.setCardCommercial((long) Math
				.round((double) cardCommercial / 1000));
		model.setCardNoneCommercial((long) Math
				.round((double) cardNoneCommercial / 1000));
		// 交易筆數不須處理
		model.setExchgImpRec((int) exchgImpRec);
		model.setExchgExpRec((int) exchgExpRec);
		model.setExchgOutRec((int) exchgOutRec);
		model.setExchgInRec((int) exchgInRec);
		model.setSalaryRec(salaryRec);
		model.setSalaryFixed(salaryFixed);
		model.setSalaryMortgage(salaryMortgage);
		model.setSalaryConsumption(salaryConsumption);
		model.setSalaryCard(salaryCard);
		model.setSalaryNetwork(salaryNetwork);
		model.setGEBTWDRec(GEBTWDRec);
		model.setGEBOTHRec(GEBOTHRec);
		model.setGEBLCRec(GEBLCRec);
		model.setCardCoBranded(cardCoBranded);
		// 算月平均【不】換算仟元
		model.setLoanAvgRate(loanAvgRate.divide(BigDecimal.valueOf(monDiff), 0,
				BigDecimal.ROUND_HALF_UP).doubleValue());
		// 算月平均並換算仟元
		model.setDepTime((long) Math.round((depTime / (double) monDiff) / 1000));
		model.setDepFixed((long) Math
				.round((depFixed / (double) monDiff) / 1000));
		model.setLoanAvgBal((long) Math
				.round((loanAvgBal / (double) monDiff) / 1000));
		// 設定其他欄位
		// 有價證券信託
		model.setTrustSecurities((long) 0);
		// 不動產信託
		model.setTrustREITs((long) 0);
		// 福儲信託
		model.setTrustWelDep((long) 0);

		// M-104-0172-001 二維表收信新增AR買方額度餘額
		// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
		model.setRcvBuyFactAmt((long) Math
				.round((double) in_ln_fact_amt_fa_b / 1000));
		// 授信-應收帳款無追索權買方承購月平均餘額(等值台幣)(資料來源LNF150)
		model.setRcvBuyAvgBal((long) Math
				.round((in_ln_fa_b / (double) monDiff) / 1000));

		// 讀取並計算貢獻度
		List<?> rows7 = this.dwdbBASEService.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
				model.getCustId(), model.getDupNo(), queryDateS, queryDateE);
		List<?> rows8 = this.dwdbBASEService
				.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(model.getCustId(),
						model.getDupNo(), queryDateS, queryDateE);
		Iterator<?> it7 = rows7.iterator();
		Iterator<?> it8 = rows8.iterator();
		double conTri = 0;
		double conSalaryTri = 0;
		double conTrustFdtaTri = 0;
		// 國內貢獻度(存款,非存款)
		if (it7.hasNext()) {
			Map<?, ?> dataMap7 = (Map<?, ?>) it7.next();
			conTri += Util.parseDouble(Util.trim(dataMap7
					.get("TOTAL_ATTRIBUTE")));
			conSalaryTri += Util.parseDouble(Util.trim(dataMap7
					.get("SLDP_TOTAL")));
			conTrustFdtaTri += Util.parseDouble(Util.trim(dataMap7
					.get("FDTA_T_TOTAL")));
		}

		L120M01A meta = findL120m01aByMainId(model.getMainId());
		// 依目前簽案行做計算幣別
		BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());

		// 海外貢獻度(非存款) Miller added at 2012/07/27
		while (it8.hasNext()) {
			Map<?, ?> dataMap8 = (Map<?, ?>) it8.next();
			double seaBal = Util.parseDouble(Util.trim(dataMap8
					.get("TOTAL_ATTRIBUTE")));
			double seaSalaryBal = Util.parseDouble(Util.trim(dataMap8
					.get("SLDP_TOTAL")));
			double seaTrustFdtaBal = Util.parseDouble(Util.trim(dataMap8
					.get("FDTA_T_TOTAL")));
			String curr = Util.trim(dataMap8.get("CURR"));
			if (seaBal != 0) {
				seaBal = branchRate.toTWDAmt(
						(Util.isEmpty(curr)) ? "TWD" : curr,
						LMSUtil.toBigDecimal(seaBal)).doubleValue();
			}
			if (seaSalaryBal != 0) {
				seaSalaryBal = branchRate.toTWDAmt(
						(Util.isEmpty(curr)) ? "TWD" : curr,
						LMSUtil.toBigDecimal(seaSalaryBal)).doubleValue();
			}
			if (seaTrustFdtaBal != 0) {
				seaTrustFdtaBal = branchRate.toTWDAmt(
						(Util.isEmpty(curr)) ? "TWD" : curr,
						LMSUtil.toBigDecimal(seaTrustFdtaBal)).doubleValue();
			}
			conTri += seaBal;
			conSalaryTri += seaSalaryBal;
			conTrustFdtaTri += seaTrustFdtaBal;
		}

		// 開始透過AS400 取得海外存款貢獻度 Miller added at 2012/07/20
		Map mapAs400 = null;
		if (meta != null) {
			String typCd = Util.trim(meta.getTypCd());
			if (UtilConstants.Casedoc.typCd.海外.equals(typCd)) {
				try {
					Date dQueryDateS = CapDate.getDate(queryDateS,
							UtilConstants.DateFormat.YYYY_MM_DD);
					Date dQueryDateE = CapDate.getDate(queryDateE,
							UtilConstants.DateFormat.YYYY_MM_DD);
					String elf003SDate = CapDate.formatDate(dQueryDateS,
							"yyyyMM");
					String elf003EDate = CapDate.formatDate(dQueryDateE,
							"yyyyMM");
					mapAs400 = elf001Srv.findELF003ProfitContributeByIdDate(
							Util.trim(model.getCustId()),
							Util.trim(model.getDupNo()),
							Util.trim(meta.getCaseBrId()), elf003SDate,
							elf003EDate);
				} catch (GWException gw) {
					throw gw;
				} catch (Exception e) {
					LOGGER.error("[getContent] Exception!!", e.getMessage());
				}
			}
		}

		BigDecimal exchangeRate = null;

		if (!CollectionUtils.isEmpty(mapAs400)) {
			String tmpStr = MapUtils.getString(mapAs400, "ELF003_LOC_CURR");
			if (!CapString.isEmpty(tmpStr)) {
				exchangeRate = branchRate.toTWDRate(tmpStr);
			} else {
				exchangeRate = BigDecimal.ONE;
			}
			tmpStr = MapUtils.getString(mapAs400, "ELF003_AMT", "0");
			BigDecimal tmpBD = new BigDecimal(tmpStr);
			conTri += tmpBD.multiply(exchangeRate).longValue();
		}

		conTri = Util.parseDouble(CapMath.round(Util.trim(conTri / 1000), 0));
		model.setProfit((long) conTri);
		conSalaryTri = Util.parseDouble(CapMath.round(
				Util.trim(conSalaryTri / 1000), 0));
		model.setProfitSalary((long) conSalaryTri);
		conTrustFdtaTri = Util.parseDouble(CapMath.round(
				Util.trim(conTrustFdtaTri / 1000), 0));
		model.setProfitTrustFdta((long) conTrustFdtaTri);

		// 起始日(民國年)
		StringBuilder relDateS = new StringBuilder();
		String dateS = Util.addZeroWithValue(
				CapDate.convertDateToTaiwanYear(queryDateS.substring(0, 4)), 3);
		String dateE = Util.addZeroWithValue(
				CapDate.convertDateToTaiwanYear(queryDateE.substring(0, 4)), 3);
		relDateS.append(dateS).append("/").append(queryDateS.substring(5, 7));
		// 迄日(民國年)
		StringBuilder relDateE = new StringBuilder();
		relDateE.append(dateE).append("/").append(queryDateE.substring(5, 7));

		// 設定資料基期
		return model;
	}

	@Override
	public List<L120S04A> findL120s04aByMainIdKeyCustIdDupNo(String mainId,
			String keyCustId, String keyDupNo) {
		return l120s04aDao.findByMainIdKeyCustIdDupNo(mainId, keyCustId,
				keyDupNo);

	}

	@Override
	public void setTotal(String mainId, String keyCustId, String keyDupNo,
			Map<String, Long> map, String queryDateS, String queryDateE) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(RelatedAccountPanel.class);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L120S04A> listL120s04a = this.findL120s04aByMainIdKeyCustIdDupNo(
				mainId, keyCustId, keyDupNo);
		// 檢查是否已有計算好的集團合計或關係合計
		if (listL120s04a != null) {
			for (L120S04A model : listL120s04a) {
				if ("3".equals(model.getCustRelation())
						|| "4".equals(model.getCustRelation())) {
					// 如果有則刪除
					this.delete(model);
				}
			}
		}
		List<Object[]> list1 = l120s04aDao.findL120s04a_keyCustIdDupNo(mainId,
				keyCustId, keyDupNo);
		List<Object[]> list2 = l120s04aDao.findL120s04a2_keyCustIdDupNo(mainId,
				keyCustId, keyDupNo);
		// 集團合計
		L120S04A l120s04a1 = new L120S04A();
		l120s04a1.setMainId(mainId);
		l120s04a1.setCreateTime(CapDate.getCurrentTimestamp());
		l120s04a1.setCreator(user.getUserId());
		l120s04a1.setCustRelation("3");
		l120s04a1.setPrtFlag("1");
		l120s04a1.setCustId("");
		l120s04a1.setCustName(pop.getProperty("L1205S07.index5"));
		l120s04a1.setCreateBY(UtilConstants.Casedoc.L120s04aCreateBY.人工產生);
		l120s04a1.setKeyCustId(keyCustId);
		l120s04a1.setKeyDupNo(keyDupNo);
		// 關係合計
		L120S04A l120s04a2 = new L120S04A();
		l120s04a2.setMainId(mainId);
		l120s04a2.setCreateTime(CapDate.getCurrentTimestamp());
		l120s04a2.setCreator(user.getUserId());
		l120s04a2.setCustRelation("4");
		l120s04a2.setPrtFlag("1");
		l120s04a2.setCustId("");
		l120s04a2.setCustName(pop.getProperty("L1205S07.index4"));
		l120s04a2.setCreateBY(UtilConstants.Casedoc.L120s04aCreateBY.人工產生);
		l120s04a2.setKeyCustId(keyCustId);
		l120s04a2.setKeyDupNo(keyDupNo);
		// ======================
		procTotal(list1, l120s04a1, map, "profit01", "loanQuota01",
				"loanAvgBal01", "depTime01", queryDateS, queryDateE);
		procTotal(list2, l120s04a2, map, "profit02", "loanQuota02",
				"loanAvgBal02", "depTime02", queryDateS, queryDateE);
		// ======================
		L120M01A model = this.findL120m01aByMainId(mainId);

		this.save(model, l120s04a1, l120s04a2);

	}

	private void procTotal(List<Object[]> list, L120S04A l120s04a1,
			Map<String, Long> map, String key_profit, String key_loanQuota,
			String key_loanAvgBal, String key_depTime, String queryDateS,
			String queryDateE) {
		if (list != null) {
			// 找到資料時(計算後有值)
			for (Object[] row : list) {
				// 貢獻度(集團)
				Long tprofit = (Long) row[34];
				// 放款額度(集團)
				Long tloanQuota = (Long) row[2];
				// 放款餘額(集團)
				Long tloanAvgBal = (Long) row[3];
				// 活期存款(集團)
				Long tdepTime = (Long) row[0];

				// 開始設定集團合計

				l120s04a1.setDepTime((Long) row[0]);
				l120s04a1.setDepFixed((Long) row[1]);
				l120s04a1.setLoanQuota((Long) row[2]);
				l120s04a1.setLoanAvgBal((Long) row[3]);
				// l120s04a1.setLoanAvgRate(0.0);
				l120s04a1
						.setExchgImpRec(new Integer(((Long) row[4]).toString()));
				l120s04a1.setExchgImpAmt((Long) row[5]);
				l120s04a1
						.setExchgExpRec(new Integer(((Long) row[6]).toString()));
				l120s04a1.setExchgExpAmt((Long) row[7]);
				l120s04a1
						.setExchgOutRec(new Integer(((Long) row[8]).toString()));
				l120s04a1.setExchgOutAmt((Long) row[9]);
				l120s04a1
						.setExchgInRec(new Integer(((Long) row[10]).toString()));
				l120s04a1.setExchgInAmt((Long) row[11]);
				l120s04a1.setDerOption((Long) row[12]);
				l120s04a1.setDerRateExchg((Long) row[13]);
				l120s04a1.setDerCCS((Long) row[14]);
				l120s04a1.setDerSWAP((Long) row[15]);
				l120s04a1.setTrustBond((Long) row[16]);
				l120s04a1.setTrustFund((Long) row[17]);
				l120s04a1.setTrustSetAcct((Long) row[18]);
				l120s04a1.setTrustOther((Long) row[19]);
				l120s04a1.setWealthTrust((Long) row[20]);
				l120s04a1.setWealthInsCom((Long) row[21]);
				l120s04a1.setWealthInvest((Long) row[22]);
				l120s04a1.setSalaryRec((Long) row[23]);
				l120s04a1.setSalaryFixed((Long) row[24]);
				l120s04a1.setSalaryMortgage((Long) row[25]);
				l120s04a1.setSalaryConsumption((Long) row[26]);
				l120s04a1.setSalaryCard((Long) row[27]);
				l120s04a1.setSalaryNetwork((Long) row[28]);
				l120s04a1.setCardCommercial((Long) row[29]);
				l120s04a1.setCardCoBranded((String) row[30]);
				if (Util.isEmpty(l120s04a1.getCardCoBranded())) {
					l120s04a1.setCardCoBranded("N");// default N
				}
				l120s04a1.setGEBTWDRec((Long) row[31]);
				l120s04a1.setGEBOTHRec((Long) row[32]);
				l120s04a1.setGEBLCRec((Long) row[33]);
				l120s04a1.setProfit((Long) row[34]);
				l120s04a1.setCardNoneCommercial((Long) row[35]);
				l120s04a1.setProfitSalary((Long) row[36]);
				l120s04a1.setProfitTrustFdta((Long) row[37]);
				// M-104-0172-001 二維表收信新增AR買方額度餘額
				l120s04a1.setRcvBuyFactAmt((Long) row[38]);
				l120s04a1.setRcvBuyAvgBal((Long) row[39]);

				// 將計算好結果存到Map裡以傳到前端
				map.put(key_profit, tprofit);
				map.put(key_loanQuota, tloanQuota);
				map.put(key_loanAvgBal, tloanAvgBal);
				map.put(key_depTime, tdepTime);
			}
		} else {
			// 找不到資料時(即計算後為空值)
			// 開始設定集團合計
			l120s04a1.setDepTime(new Long("0"));
			l120s04a1.setDepFixed(new Long("0"));
			l120s04a1.setLoanQuota(new Long("0"));
			l120s04a1.setLoanAvgBal(new Long("0"));
			// l120s04a1.setLoanAvgRate(0.0);
			l120s04a1.setExchgImpRec(new Integer("0"));
			l120s04a1.setExchgImpAmt(new Long("0"));
			l120s04a1.setExchgExpRec(new Integer("0"));
			l120s04a1.setExchgExpAmt(new Long("0"));
			l120s04a1.setExchgOutRec(new Integer("0"));
			l120s04a1.setExchgOutAmt(new Long("0"));
			l120s04a1.setExchgInRec(new Integer("0"));
			l120s04a1.setExchgInAmt(new Long("0"));
			l120s04a1.setDerOption(new Long("0"));
			l120s04a1.setDerRateExchg(new Long("0"));
			l120s04a1.setDerCCS(new Long("0"));
			l120s04a1.setDerSWAP(new Long("0"));
			l120s04a1.setTrustBond(new Long("0"));
			l120s04a1.setTrustFund(new Long("0"));
			l120s04a1.setTrustSetAcct(new Long("0"));
			l120s04a1.setTrustOther(new Long("0"));
			l120s04a1.setWealthTrust(new Long("0"));
			l120s04a1.setWealthInsCom(new Long("0"));
			l120s04a1.setWealthInvest(new Long("0"));
			l120s04a1.setSalaryRec(new Long("0"));
			l120s04a1.setSalaryFixed(new Long("0"));
			l120s04a1.setSalaryMortgage(new Long("0"));
			l120s04a1.setSalaryConsumption(new Long("0"));
			l120s04a1.setSalaryCard(new Long("0"));
			l120s04a1.setSalaryNetwork(new Long("0"));
			l120s04a1.setCardCommercial(new Long("0"));
			l120s04a1.setCardCoBranded(new String("N"));
			l120s04a1.setGEBTWDRec(new Long("0"));
			l120s04a1.setGEBOTHRec(new Long("0"));
			l120s04a1.setGEBLCRec(new Long("0"));
			l120s04a1.setProfit(new Long("0"));
			l120s04a1.setCardNoneCommercial(new Long("0"));
			l120s04a1.setProfitSalary(new Long("0"));
			l120s04a1.setProfitTrustFdta(new Long("0"));
			// M-104-0172-001 二維表收信新增AR買方額度餘額
			l120s04a1.setRcvBuyFactAmt(new Long("0"));
			l120s04a1.setRcvBuyAvgBal(new Long("0"));

			// 將計算好結果存到Map裡以傳到前端
			map.put(key_profit, new Long("0"));
			map.put(key_loanQuota, new Long("0"));
			map.put(key_loanAvgBal, new Long("0"));
			map.put(key_depTime, new Long("0"));
		}

		if (l120s04a1 != null) {
			l120s04a1.setChkYN("Y");
			if (!Util.isEmpty(queryDateS)) {
				if (queryDateS.length() == 7) {
					queryDateS = queryDateS + "-01";
				}
				l120s04a1.setQueryDateS(Util.parseDate(Util.trim(queryDateS)));
			} else {
				l120s04a1.setQueryDateS(null);
			}
			if (!Util.isEmpty(queryDateE)) {
				if (queryDateE.length() == 7) {
					queryDateE = queryDateE + "-01";
				}
				l120s04a1.setQueryDateE(Util.parseDate(Util.trim(queryDateE)));
			} else {
				l120s04a1.setQueryDateE(null);
			}
		}
	}

	@Override
	public void deleteL120S04_A(String mainId, String keyCustId, String keyDupNo) {
		List<L120S04A> list = findL120s04aByMainIdKeyCustIdDupNo(mainId,
				keyCustId, keyDupNo);
		if (list != null) {
			// 有資料就刪除
			l120s04aDao.delete(list);
		}
	}

	@Override
	public void deleteL120S04_BC(String mainId, String keyCustId,
			String keyDupNo) {

		List<L120S04B> listS04b = l120s04bDao.findByMainIdKeyCustIdDupNo(
				mainId, keyCustId, keyDupNo);
		List<L120S04C> listS04c = l120s04cDao.findByMainIdKeyCustIdDupNo(
				mainId, keyCustId, keyDupNo);
		if (listS04b != null && !listS04b.isEmpty()) {
			l120s04bDao.delete(listS04b);
		}

		if (listS04c != null && !listS04c.isEmpty()) {
			l120s04cDao.delete(listS04c);
		}
	}

	/**
	 * 取得集團信用評等及年度
	 * 
	 * @param gpId
	 *            集團代號
	 * @return
	 */
	public Map<String, Object> getMainGrpData(String gpId) {
		Map<String, Object> map = new HashMap<String, Object>();
		Map<String, Object> mapGrpYY = misGrpdtlService.findGrpdtl_selGrpyy();
		String grpYY = null;
		if (!mapGrpYY.isEmpty()) {
			grpYY = Util.trim(mapGrpYY.get("GRPYY"));
			map.put("tDATAYY", grpYY);
		}

		// J-107-0087-001 Web
		// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
		boolean newGrpGrade = lmsService.isNewGrpGrade(grpYY, false);
		String defultNoGrade = lmsService.getGrpNoGrade(newGrpGrade);

		if (Util.isEmpty(grpYY)) {
			map.put("tDATAYY", "");
			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			map.put("tGRADE", defultNoGrade);
			map.put("tGRPSIZE", "");
			map.put("tGRPLEVEL", "");
		}

		List<Map<String, Object>> mapGrpGrades = misGrpcmpService
				.findGrpcmpSelGrpGrade(gpId, grpYY);
		if (!mapGrpGrades.isEmpty()) {
			for (Map<String, Object> mapGrpGrade : mapGrpGrades) {
				String value = Util.trim(mapGrpGrade.get("GRADE"));

				// J-107-0087-001 Web
				// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
				String size = MapUtils.getString(mapGrpGrade, "GRPSIZE", "");
				String level = MapUtils.getString(mapGrpGrade, "GRPLEVEL", "");
				if (Util.isEmpty(value)) {
					value = defultNoGrade;
				}
				map.put("tGRADE", value);

				// J-107-0087-001 Web
				// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
				map.put("tGRPSIZE", size);
				map.put("tGRPLEVEL", level);
			}
		} else {
			map.put("tGRADE", defultNoGrade);

			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			map.put("tGRPSIZE", "");
			map.put("tGRPLEVEL", "");
		}
		return map;
	}

	private JSONObject getL120S04B(String mainId, String custId, String dupNo,
			String queryDateS, String queryDateE) {
		JSONObject jsonData = new JSONObject();
		L120M01A meta = findL120m01aByMainId(mainId);
		// 活期存款
		long depTime = 0;
		// 定期存款
		long depFixed = 0;
		// 額度
		long loanQuota = 0;
		// 平均餘額
		long loanAvgBal = 0;
		// 平均動用率
		BigDecimal loanAvgRate = new BigDecimal("0");
		// 進口筆數
		long exchgImpRec = 0;
		// 進口金額
		long exchgImpAmt = 0;
		// 出口筆數
		long exchgExpRec = 0;
		// 出口金額
		long exchgExpAmt = 0;
		// 匯出筆數
		long exchgOutRec = 0;
		// 匯出金額
		long exchgOutAmt = 0;
		// 匯入筆數
		long exchgInRec = 0;
		// 匯入金額
		long exchgInAmt = 0;
		// 選擇權
		long derOption = 0;
		// 利率交換
		long derRateExchg = 0;
		// 換匯換利
		long derCCS = 0;
		// 遠匯
		String derDraft = "0";
		// 遠匯(含SWAP)
		long derSWAP = 0;
		// 國內外基金債券
		long trustBond = 0;
		// 基金保管
		long trustFund = 0;
		// 集管
		long trustSetAcct = 0;
		// 有價證券信託
		String trustSecurities = "0";
		// 不動產信託
		String trustREITs = "0";
		// 福儲信託
		String trustWelDep = "0";
		// 其他信託
		long trustOther = 0;
		// 信託
		long wealthTrust = 0;
		// 保險佣金
		long wealthInsCom = 0;
		// 雙元投資
		long wealthInvest = 0;
		// 薪轉戶數
		long salaryRec = 0;
		// 定期定額戶數
		long salaryFixed = 0;
		// 房貸戶數
		long salaryMortgage = 0;
		// 消貸戶數
		long salaryConsumption = 0;
		// 信用卡持卡人數
		long salaryCard = 0;
		// 個人網銀戶數
		long salaryNetwork = 0;
		// 商務卡
		long cardCommercial = 0;
		// 非商務卡
		long cardNoneCommercial = 0;
		// 聯名卡
		String cardCoBranded = "N";
		// 台幣交易筆數
		long GEBTWDRec = 0;
		// 外幣交易筆數
		long GEBOTHRec = 0;
		// 信用狀交易筆數
		long GEBLCRec = 0;
		// 利潤貢獻度
		String profit = "0";
		// 活期存款
		String depTime1 = "0";
		// 定期存款
		String depFixed1 = "0";
		// 額度
		String loanQuota1 = "0";
		// 平均餘額
		String loanAvgBal1 = "0";
		// 平均動用率
		String loanAvgRate1 = "0";
		// 進口筆數
		String exchgImpRec1 = "0";
		// 進口金額
		String exchgImpAmt1 = "0";
		// 出口筆數
		String exchgExpRec1 = "0";
		// 出口金額
		String exchgExpAmt1 = "0";
		// 匯出筆數
		String exchgOutRec1 = "0";
		// 匯出金額
		String exchgOutAmt1 = "0";
		// 匯入筆數
		String exchgInRec1 = "0";
		// 匯入金額
		String exchgInAmt1 = "0";
		// 選擇權
		String derOption1 = "0";
		// 利率交換
		String derRateExchg1 = "0";
		// 換匯換利
		String derCCS1 = "0";
		// 遠匯
		String derDraft1 = "0";
		// 遠匯(含SWAP)
		String derSWAP1 = "0";
		// 國內外基金債券
		String trustBond1 = "0";
		// 基金保管
		String trustFund1 = "0";
		// 集管
		String trustSetAcct1 = "0";
		// 有價證券信託
		String trustSecurities1 = "0";
		// 不動產信託
		String trustREITs1 = "0";
		// 福儲信託
		String trustWelDep1 = "0";
		// 其他信託
		String trustOther1 = "0";
		// 信託
		String wealthTrust1 = "0";
		// 保險佣金
		String wealthInsCom1 = "0";
		// 雙元投資
		String wealthInvest1 = "0";
		// 薪轉戶數
		String salaryRec1 = "0";
		// 定期定額戶數
		String salaryFixed1 = "0";
		// 房貸戶數
		String salaryMortgage1 = "0";
		// 消貸戶數
		String salaryConsumption1 = "0";
		// 信用卡持卡人數
		String salaryCard1 = "0";
		// 個人網銀戶數
		String salaryNetwork1 = "0";
		// 商務卡
		String cardCommercial1 = "0";
		// 非商務卡
		String cardNoneCommercial1 = "0";
		// 聯名卡
		String cardCoBranded1 = "N";
		// 台幣交易筆數
		String GEBTWDRec1 = "0";
		// 外幣交易筆數
		String GEBOTHRec1 = "0";
		// 信用狀交易筆數
		String GEBLCRec1 = "0";
		// 利潤貢獻度
		String profit1 = "0";

		String IN_CC_CC_ACT = "";
		String BR_CD = "";
		String DW_CR_DT = "0";
		String DW_LST_MNT_DT = "0";
		String IN_LN_FA_B = "0";
		String IN_LN_FA_S = "0";
		long in_ln_fa_b = 0;
		long in_ln_fa_s = 0;

		// M-104-0172-001 二維表收信新增AR買方額度餘額
		String IN_LN_FACT_AMT_FA_S = "0";
		String IN_LN_FACT_AMT_FA_B = "0";
		long in_ln_fact_amt_fa_b = 0;
		long in_ln_fact_amt_fa_s = 0;

		custId = (custId + "          ").substring(0, 10);
		if ("0".equals(dupNo)) {
			dupNo = " ";
		}

		List<?> rows6 = this.dwdbBASEService.findDW_MD_CUPFM_OTS_selCYC_MN(
				custId, dupNo, queryDateS, queryDateE);
		Iterator<?> it6 = rows6.iterator();
		while (it6.hasNext()) {
			Map<?, ?> dataMap6 = (Map<?, ?>) it6.next();
			String date = CapDate.formatDate(
					CapDate.parseDate(String.valueOf(dataMap6.get("CYC_MN"))),
					"yyyy-MM-dd");
			dataMap6.get("CUST_KEY");
			BR_CD = Util.nullToSpace(dataMap6.get("BR_CD")); // 999代表全行
			depTime1 = Util.nullToSpace(dataMap6.get("IN_DP"));
			dataMap6.get("IN_DP_G");
			depFixed1 = Util.nullToSpace(dataMap6.get("IN_CT"));
			loanAvgBal1 = Util.nullToSpace(dataMap6.get("IN_LN_USE"));
			loanAvgRate1 = Util.nullToSpace(dataMap6.get("IN_LN_AVGRT"));
			loanQuota1 = Util.nullToSpace(dataMap6.get("IN_LN_FACT_AMT"));
			exchgImpAmt1 = Util.nullToSpace(dataMap6.get("IN_IM"));
			exchgExpAmt1 = Util.nullToSpace(dataMap6.get("IN_EX_BP"));
			exchgImpRec1 = Util.nullToSpace(dataMap6.get("IN_IM_TXN"));
			exchgExpRec1 = Util.nullToSpace(dataMap6.get("IN_EX_TXN"));
			exchgOutAmt1 = Util.nullToSpace(dataMap6.get("IN_OR"));
			exchgInAmt1 = Util.nullToSpace(dataMap6.get("IN_IR"));
			exchgOutRec1 = Util.nullToSpace(dataMap6.get("IN_OR_TXN"));
			exchgInRec1 = Util.nullToSpace(dataMap6.get("IN_IR_TXN"));
			derOption1 = Util.nullToSpace(dataMap6.get("IN_DV_OP"));
			derRateExchg1 = Util.nullToSpace(dataMap6.get("IN_DV_RE"));
			derCCS1 = Util.nullToSpace(dataMap6.get("IN_DV_ER"));
			derSWAP1 = Util.nullToSpace(dataMap6.get("IN_DV_FR"));
			wealthTrust1 = Util.nullToSpace(dataMap6.get("IN_WM_F_FEE"));
			wealthInsCom1 = Util.nullToSpace(dataMap6.get("IN_WM_I_FEE"));
			wealthInvest1 = Util.nullToSpace(dataMap6.get("IN_WM_S_FEE"));
			trustFund1 = Util.nullToSpace(dataMap6.get("IN_TR_FU"));
			trustSetAcct1 = Util.nullToSpace(dataMap6.get("IN_TR_CF"));
			trustBond1 = Util.nullToSpace(dataMap6.get("IN_TR_SC"));
			trustOther1 = Util.nullToSpace(dataMap6.get("IN_TR_OTS"));
			cardCommercial1 = Util.nullToSpace(dataMap6.get("IN_CC_CC"));
			cardNoneCommercial1 = Util.nullToSpace(dataMap6.get("IN_CC_IV"));
			IN_CC_CC_ACT = Util.nullToSpace(dataMap6.get("IN_CC_CC_ACT"));
			cardCoBranded1 = Util.nullToSpace(dataMap6.get("IN_CC_JC_ACT"));
			salaryRec1 = Util.nullToSpace(dataMap6.get("IN_ST"));
			salaryFixed1 = Util.nullToSpace(dataMap6.get("IN_ST_FD"));
			salaryMortgage1 = Util.nullToSpace(dataMap6.get("IN_ST_LN_1"));
			salaryConsumption1 = Util.nullToSpace(dataMap6.get("IN_ST_LN_2"));
			salaryCard1 = Util.nullToSpace(dataMap6.get("IN_ST_CC"));
			salaryNetwork1 = Util.nullToSpace(dataMap6.get("IN_ST_NB"));
			GEBTWDRec1 = Util.nullToSpace(dataMap6.get("IN_GEB_NTD_TXN"));
			GEBOTHRec1 = Util.nullToSpace(dataMap6.get("IN_GEB_NTD_N_TXN"));
			GEBLCRec1 = Util.nullToSpace(dataMap6.get("IN_GEB_LC_TXN"));
			DW_CR_DT = Util.nullToSpace(dataMap6.get("DW_CR_DT"));
			DW_LST_MNT_DT = Util.nullToSpace(dataMap6.get("DW_LST_MNT_DT"));

			// 應收帳款無追索買方承購平均餘額(IN_LN_FA_B)
			IN_LN_FA_B = Util.nullToSpace(dataMap6.get("IN_LN_FA_B"));
			// 應收帳款無追索權賣方融資平均餘額(IN_LN_FA_S)
			IN_LN_FA_S = Util.nullToSpace(dataMap6.get("IN_LN_FA_S"));

			// M-104-0172-001 二維表收信新增AR買方額度餘額
			// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
			IN_LN_FACT_AMT_FA_B = Util.nullToSpace(dataMap6
					.get("IN_LN_FACT_AMT_FA_B"));
			// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
			IN_LN_FACT_AMT_FA_S = Util.nullToSpace(dataMap6
					.get("IN_LN_FACT_AMT_FA_S"));

			dataMap6.get("DW_CR_DT");
			dataMap6.get("DW_LST_MNT_DT");
			if (date.equals(queryDateE)) {
				loanQuota += Util.parseLong(loanQuota1);
				trustFund += Util.parseLong(trustFund1);
				trustSetAcct += Util.parseLong(trustSetAcct1);
				trustBond += Util.parseLong(trustBond1);
				trustOther += Util.parseLong(trustOther1);

				salaryRec += Util.parseLong(salaryRec1);
				salaryFixed += Util.parseLong(salaryFixed1);
				salaryMortgage += Util.parseLong(salaryMortgage1);
				salaryConsumption += Util.parseLong(salaryConsumption1);
				salaryCard += Util.parseLong(salaryCard1);
				salaryNetwork += Util.parseLong(salaryNetwork1);

				if ("Y".equals(cardCoBranded1)) {
					cardCoBranded = "Y";
				} else {
					cardCoBranded = "Y";
				}

				// M-104-0172-001 二維表收信新增AR買方額度餘額
				// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
				in_ln_fact_amt_fa_b += Util.parseLong(IN_LN_FACT_AMT_FA_B);
				// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
				in_ln_fact_amt_fa_s += Util.parseLong(IN_LN_FACT_AMT_FA_S);

				/*
				 * if ("Y".equals(cardCoBranded1)) { cardCoBranded = "Y"; } else
				 * { cardCoBranded = "N"; }
				 */

			}
			if ("999".equals(BR_CD)) {
				// 全行、用來計算平均動用率
				loanAvgRate = loanAvgRate.add(new BigDecimal(loanAvgRate1));
			}
			depTime += Util.parseLong(depTime1);
			depFixed += Util.parseLong(depFixed1);
			loanAvgBal += Util.parseLong(loanAvgBal1);
			exchgImpAmt += Util.parseLong(exchgImpAmt1);
			exchgExpAmt += Util.parseLong(exchgExpAmt1);
			exchgImpRec += Util.parseLong(exchgImpRec1);
			exchgExpRec += Util.parseLong(exchgExpRec1);
			exchgOutAmt += Util.parseLong(exchgOutAmt1);
			exchgInAmt += Util.parseLong(exchgInAmt1);
			exchgOutRec += Util.parseLong(exchgOutRec1);
			exchgInRec += Util.parseLong(exchgInRec1);
			derOption += Util.parseLong(derOption1);
			derRateExchg += Util.parseLong(derRateExchg1);
			derCCS += Util.parseLong(derCCS1);
			derSWAP += Util.parseLong(derSWAP1);
			wealthTrust += Util.parseLong(wealthTrust1);
			wealthInsCom += Util.parseLong(wealthInsCom1);
			wealthInvest += Util.parseLong(wealthInvest1);
			cardCommercial += Util.parseLong(cardCommercial1);
			cardNoneCommercial += Util.parseLong(cardNoneCommercial1);
			GEBTWDRec += Util.parseLong(GEBTWDRec1);
			GEBOTHRec += Util.parseLong(GEBOTHRec1);
			GEBLCRec += Util.parseLong(GEBLCRec1);

			in_ln_fa_b += Util.parseLong(IN_LN_FA_B);
			in_ln_fa_s += Util.parseLong(IN_LN_FA_S);
		}
		// 算起迄年月的差異月數
		int monDiff = (Util.parseInt(queryDateE.substring(0, 4)) * 12 + Util
				.parseInt(queryDateE.substring(5, 7)))
				- (Util.parseInt(queryDateS.substring(0, 4)) * 12 + Util
						.parseInt(queryDateS.substring(5, 7))) + 1;
		// 換算仟元
		jsonData.put("loanQuota", (long) Math.round((double) loanQuota / 1000));
		jsonData.put("trustFund", (long) Math.round((double) trustFund / 1000));
		jsonData.put("trustSetAcct",
				(long) Math.round((double) trustSetAcct / 1000));
		jsonData.put("trustBond", (long) Math.round((double) trustBond / 1000));
		jsonData.put("trustOther",
				(long) Math.round((double) trustOther / 1000));
		jsonData.put("exchgImpAmt",
				(long) Math.round((double) exchgImpAmt / 1000));
		jsonData.put("exchgExpAmt",
				(long) Math.round((double) exchgExpAmt / 1000));
		jsonData.put("exchgOutAmt",
				(long) Math.round((double) exchgOutAmt / 1000));
		jsonData.put("exchgInAmt",
				(long) Math.round((double) exchgInAmt / 1000));
		jsonData.put("derOption", (long) Math.round((double) derOption / 1000));
		jsonData.put("derRateExchg",
				(long) Math.round((double) derRateExchg / 1000));
		jsonData.put("derCCS", (long) Math.round((double) derCCS / 1000));
		jsonData.put("derSWAP", (long) Math.round((double) derSWAP / 1000));
		jsonData.put("wealthTrust",
				(long) Math.round((double) wealthTrust / 1000));
		jsonData.put("wealthInsCom",
				(long) Math.round((double) wealthInsCom / 1000));
		jsonData.put("wealthInvest",
				(long) Math.round((double) wealthInvest / 1000));
		jsonData.put("cardCommercial",
				(long) Math.round((double) cardCommercial / 1000));
		jsonData.put("cardNoneCommercial",
				(long) Math.round((double) cardNoneCommercial / 1000));
		// 交易筆數不須處理
		jsonData.put("exchgImpRec", (int) exchgImpRec);
		jsonData.put("exchgExpRec", (int) exchgExpRec);
		jsonData.put("exchgOutRec", (int) exchgOutRec);
		jsonData.put("exchgInRec", (int) exchgInRec);
		jsonData.put("salaryRec", salaryRec);
		jsonData.put("salaryFixed", salaryFixed);
		jsonData.put("salaryMortgage", salaryMortgage);
		jsonData.put("salaryConsumption", salaryConsumption);
		jsonData.put("salaryCard", salaryCard);
		jsonData.put("salaryNetwork", salaryNetwork);
		jsonData.put("gEBTWDRec", GEBTWDRec);
		jsonData.put("gEBOTHRec", GEBOTHRec);
		jsonData.put("gEBLCRec", GEBLCRec);
		jsonData.put("cardCoBranded", cardCoBranded);
		// 算月平均【不】換算仟元
		jsonData.put(
				"loanAvgRate",
				loanAvgRate.divide(BigDecimal.valueOf(monDiff), 0,
						BigDecimal.ROUND_HALF_UP).doubleValue());
		// 算月平均並換算仟元
		jsonData.put("depTime",
				(long) Math.round((depTime / (double) monDiff) / 1000));
		jsonData.put("depFixed",
				(long) Math.round((depFixed / (double) monDiff) / 1000));
		jsonData.put("loanAvgBal",
				(long) Math.round((loanAvgBal / (double) monDiff) / 1000));
		// 設定其他欄位
		// 有價證券信託
		jsonData.put("trustSecurities", (long) 0);
		// 不動產信託
		jsonData.put("trustREITs", (long) 0);
		// 福儲信託
		jsonData.put("trustWelDep", (long) 0);

		// 應收帳款無追索買方承購平均餘額(IN_LN_FA_B)
		jsonData.put("IN_LN_FA_B",
				(long) Math.round((in_ln_fa_b / (double) monDiff) / 1000));
		// 應收帳款無追索權賣方融資平均餘額(IN_LN_FA_S)
		jsonData.put("IN_LN_FA_S",
				(long) Math.round((in_ln_fa_s / (double) monDiff) / 1000));

		// M-104-0172-001 二維表收信新增AR買方額度餘額
		// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
		jsonData.put("IN_LN_FACT_AMT_FA_B",
				(long) Math.round((double) in_ln_fact_amt_fa_b / 1000));
		// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
		jsonData.put("IN_LN_FACT_AMT_FA_S",
				(long) Math.round((double) in_ln_fact_amt_fa_s / 1000));

		// 讀取並計算貢獻度
		// List<?> rows7 =
		// this.dwdbBASEService.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
		// custId + dupNo, queryDateS, queryDateE);
		// List<?> rows8 =
		// this.dwdbService.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(
		// custId + dupNo, queryDateS, queryDateE);
		// Iterator<?> it7 = rows7.iterator();
		// Iterator<?> it8 = rows8.iterator();
		long conTri = 0;
		// 國內貢獻度(存款,非存款)
		// if (it7.hasNext()) {
		// Map<?, ?> dataMap7 = (Map<?, ?>) it7.next();
		// conTri = Util.parseLong(Util.trim(Util.nullToSpace(dataMap7
		// .get("TOTAL_ATTRIBUTE"))));
		// if (conTri != 0) {
		// conTri = Util.parseLong(CapMath.round(Util.trim(conTri / 1000),
		// 0));
		// }
		// }
		// // 海外貢獻度(非存款) Miller added at 2012/07/27
		// if (it8.hasNext()) {
		// Map<?, ?> dataMap8 = (Map<?, ?>) it8.next();
		// conTri += Util.parseLong(Util.trim(Util.nullToSpace(dataMap8
		// .get("TOTAL_ATTRIBUTE"))));
		// }
		// // 開始透過AS400 取得海外存款貢獻度 Miller added at 2012/07/20
		// Map mapAs400 = null;
		// try {
		// mapAs400 = misELF003Service.findELF003ProfitContributeById(
		// Util.trim(custId), Util.trim(dupNo),
		// Util.trim(meta.getCaseBrId()));
		// } catch (GWException gw) {
		// throw gw;
		// } catch (Exception e) {
		// logger.error(e.getMessage());
		// }
		//
		// // 依目前簽案行做計算幣別
		// BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());
		//
		// BigDecimal exchangeRate = null;
		//
		// if (!CollectionUtils.isEmpty(mapAs400)) {
		// String tmpStr = MapUtils.getString(mapAs400, "ELF003_LOC_CURR");
		// if (!CapString.isEmpty(tmpStr)) {
		// exchangeRate = branchRate.toTWDRate(tmpStr);
		// } else {
		// exchangeRate = BigDecimal.ONE;
		// }
		// tmpStr = MapUtils.getString(mapAs400, "ELF003_AMT", "0");
		// BigDecimal tmpBD = new BigDecimal(tmpStr);
		// conTri += tmpBD.multiply(exchangeRate).longValue();
		// }
		// conTri = (long) Math.round(conTri / 1000);
		jsonData.put("CUBCPCM", conTri);
		return jsonData;
	}

	@Override
	public String importL120s04b(String mainId, String keyCustId,
			String keyDupNo, String queryDateS, String queryDateE) {

		L120M01A meta = this.findL120m01aByMainId(mainId);
		BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());
		List<L120S04A> listL120s04a = this.findL120s04aByMainIdKeyCustIdDupNo(
				mainId, keyCustId, keyDupNo);
		// L120S04B l120s04b = new L120S04B();
		JSONObject json = new JSONObject();
		JSONObject jsonData = new JSONObject();
		Date dQueryDateS = CapDate.getDate(queryDateS,
				UtilConstants.DateFormat.YYYY_MM_DD);
		Date dQueryDateE = CapDate.getDate(queryDateE,
				UtilConstants.DateFormat.YYYY_MM_DD);
		String MAX_CYC_MN = null;
		String MIN_CYC_MN = null;
		// ====
		// 原本用 meta.custId, 但是用在1份簽報書,只有主借款人, 才能做往來實績彙總表
		// 在加上欄位 keyCustId 後, 應該用 keyCustId
		String mCustId = keyCustId;
		String mDupNo = keyDupNo;
		// ====
		String _mDupNo = "0".equals(Util.trim(mDupNo)) ? "" : Util.trim(mDupNo);
		String tGPID = null;
		String tGPName = null;
		String tID = null;
		String tDUPNO = null;
		String tCUSTNAME = null;
		String tGRADE = null;
		String end3Mon = null;
		double bal = 0;
		double balSalary = 0;
		double balTrustFdta = 0;

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(RelatedAccountPanel.class);

		if (listL120s04a.isEmpty()) {
			// L1205S07.error14 = 必須先執行【引進各關係戶往來彙總】才能執行本功能！
			return prop.getProperty("L1205S07.error14");
		} else {
			// 將除了集團、與關係企業計算合計以外的借款人資料記錄下來
			// a Method
			for (L120S04A model : listL120s04a) {
				String custRel = Util.trim(model.getCustRelation());
				if (!custRel.contains("3") && !custRel.contains("4")) {
					String dupNo = Util.trim(model.getDupNo());
					StringBuilder sbFullId = new StringBuilder();
					sbFullId.append(Util.trim(model.getCustId())).append(
							Util.trim(dupNo).equals("0") ? " " : Util
									.trim(dupNo));
					json.put(sbFullId.toString(),
							Util.trim(model.getCustName()));
				}
			}
			// a Method

			Date max_cyc_mn = dwdbBASEService.getMD_CUPFM_OTS_max_cyc_mn();
			if (max_cyc_mn == null) {
				// 資料倉儲無最近三個月("+Left(c_bgn_date,7)+"～"+Left(c_end_date,7) +")之資料
				return MessageFormat.format(
						prop.getProperty("L1205S07.error20"),
						CapDate.formatDate(dQueryDateS,
								UtilConstants.DateFormat.YYYY_MM_DD).substring(
								7),
						CapDate.formatDate(dQueryDateE,
								UtilConstants.DateFormat.YYYY_MM_DD).substring(
								7));
			}

			MAX_CYC_MN = CapDate.formatDate(max_cyc_mn,
					UtilConstants.DateFormat.YYYY_MM_DD);
			MIN_CYC_MN = Util
					.trim(Util.parseInt(MAX_CYC_MN.substring(0, 4)) - 2)
					+ "-01-01";
			try {
				List<Map<String, Object>> listGrpMap = misGrpcmpService
						.findGrpcmpSelGrpdtl(mCustId, mDupNo);
				for (Map<String, Object> map : listGrpMap) {
					// 隸屬集團代號
					tGPID = Util.trim(map.get("GRPID"));
					jsonData.put("GpID", tGPID);
					// l120s04b.setGrpNo(tGPID);
					// 隸屬集團名稱
					tGPName = Util.trim(map.get("GRPNM"));
					jsonData.put("GpName", tGPName);
					// l120s04b.setGrpName(tGPName);
					break;
				}
			} catch (GWException e) {
				// 引進借款人"+rptDoc.borrower_id(0)+"
				// " + rptDoc.borrower(0)+"集團資訊錯誤
				return MessageFormat.format(
						prop.getProperty("L1205S07.error21"), mCustId, mDupNo);
			}

			try {
				Map<String, Object> mapGrpData = getMainGrpData(tGPID);
				// 集團評等年度
				jsonData.put("GpDATAYY",
						Util.parseInt(mapGrpData.get("tDATAYY")) + 1911);
				// l120s04b.setGrpYear(Util.parseInt(mapGrpData.get("tDATAYY"))
				// + 1911);
				// 集團評等
				tGRADE = Util.trim(mapGrpData.get("tGRADE"));
				jsonData.put("GpGRADE", tGRADE);
				// J-107-0087-001 Web
				// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
				jsonData.put("GRPSIZE", Util.trim(MapUtils.getObject(
						mapGrpData, "tGRPSIZE", "")));
				jsonData.put("GRPLEVEL", Util.trim(MapUtils.getObject(
						mapGrpData, "tGRPLEVEL", "")));

				// l120s04b.setGrpGrrd(tGRADE);
			} catch (GWException e) {
				// "引進借款人"+rptDoc.borrower_id(0)+" " +
				// rptDoc.borrower(0)+"集團評等資訊錯誤"
				return MessageFormat.format(
						prop.getProperty("L1205S07.error22"), mCustId, mDupNo);
			}

			// a Method
			if (!json.isEmpty()) {
				for (Object jsonKey : json.keySet()) {
					String sJsonKey = (String) jsonKey;
					String jsonVal = Util.trim(json.get(sJsonKey));
					// 擷取 "+tID+" "+ xr + " 與本行往來實績資料
					tID = (Util.isEmpty(sJsonKey)) ? null : sJsonKey.substring(
							0, sJsonKey.length() - 1);
					tDUPNO = (Util.isEmpty(sJsonKey)) ? null : sJsonKey
							.substring(sJsonKey.length() - 1);
					tCUSTNAME = jsonVal;
					String _MAX_CYC_MN = MAX_CYC_MN.replace("-", "");
					dQueryDateE = CapDate.getDate(MAX_CYC_MN,
							UtilConstants.DateFormat.YYYY_MM_DD);
					dQueryDateS = CapDate.getDate(CapDate
							.formatyyyyMMddToDateFormat(
									CapDate.addMonth(_MAX_CYC_MN, -5),
									UtilConstants.DateFormat.YYYY_MM_DD),
							UtilConstants.DateFormat.YYYY_MM_DD);
					JSONObject tempJson = getL120S04B(mainId, tID, tDUPNO,
							CapDate.formatDate(dQueryDateS,
									UtilConstants.DateFormat.YYYY_MM_DD),
							CapDate.formatDate(dQueryDateE,
									UtilConstants.DateFormat.YYYY_MM_DD));
					if (tempJson == null) {
						// "引進"+tID+" " + xr+"業務往來資訊錯誤"
						return MessageFormat.format(
								prop.getProperty("L1205S07.error23"), tID,
								tDUPNO);
					}
					jsonData.put("Rate3_1",
							CapDate.formatDate(dQueryDateS, "yyyy") + "/"
									+ CapDate.formatDate(dQueryDateS, "MM"));

					jsonData.put("Rate3_2",
							CapDate.formatDate(dQueryDateE, "yyyy") + "/"
									+ CapDate.formatDate(dQueryDateE, "MM"));

					jsonData.put(
							"Rate3_3",
							Util.trim(Util.parseLong(jsonData.get("Rate3_3"))
									+ tempJson.getLong("depTime")
									+ tempJson.getLong("depFixed")));
					jsonData.put(
							"Rate3_4",
							Util.trim(Util.parseLong(jsonData.get("Rate3_4"))
									+ tempJson.getLong("depTime")));

					// // 設定主要集團企業最近一年起日
					// l120s04b.setMainGrpDateS(dQueryDateS);
					// // 設定主要集團企業最近一年迄日
					// l120s04b.setMainGrpDateE(dQueryDateE);
					// // 本行平均存款合計－金額
					// l120s04b.setMegaAvgAmt(new BigDecimal(tempJson
					// .getLong("depTime")
					// + tempJson.getLong("depFixed")));
					// // 活期性存款－金額
					// l120s04b.setDemandAmt(new BigDecimal(tempJson
					// .getLong("depTime")));

					// a Method
					// 101/1-3月 平均存款 平均授信 開狀及匯出 出押及匯入
					// L120S04C
					// L120S04C l120s04c = new L120S04C();
					dQueryDateS = (MAX_CYC_MN.length() < 4) ? null : CapDate
							.getDate(MAX_CYC_MN.substring(0, 4) + "-01-01",
									UtilConstants.DateFormat.YYYY_MM_DD);
					dQueryDateE = (MAX_CYC_MN.length() < 7) ? null : CapDate
							.getDate(MAX_CYC_MN.substring(0, 7) + "-01",
									UtilConstants.DateFormat.YYYY_MM_DD);
					// l120s04c.setDocDateS(dQueryDateS);
					// l120s04c.setDocDateE(dQueryDateE);
					jsonData.put("field1_3",
							CapDate.formatDate(dQueryDateS, "yyyy") + "/1~"
									+ CapDate.formatDate(dQueryDateE, "M")
									+ "月");
					jsonData.put("field1_3_1",
							CapDate.formatDate(dQueryDateS, "yyyy") + "/1~"
									+ CapDate.formatDate(dQueryDateE, "M")
									+ "月");

					JSONObject tempJson3 = getL120S04B(mainId, tID, tDUPNO,
							CapDate.formatDate(dQueryDateS,
									UtilConstants.DateFormat.YYYY_MM_DD),
							CapDate.formatDate(dQueryDateE,
									UtilConstants.DateFormat.YYYY_MM_DD));
					List<Map<String, Object>> mapBal = this.dwdbBASEService
							.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					List<?> rows8 = this.dwdbBASEService
							.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					Iterator<?> it8 = rows8.iterator();
					if (tempJson3 == null) {
						// "引進"+tID+" " + xr+"業務往來資訊錯誤"
						return MessageFormat.format(
								prop.getProperty("L1205S07.error23"), tID,
								tDUPNO);
					}
					if (mapBal.isEmpty()) {
						// "引進"+tID+" " + xr+"利潤貢獻度錯誤"
						return MessageFormat.format(
								prop.getProperty("L1205S07.error24"), tID,
								tDUPNO);
					}
					for (Map<String, Object> mapAttri : mapBal) {
						bal = Util.parseDouble(Util.trim(mapAttri
								.get("TOTAL_ATTRIBUTE")));
						balSalary = Util.parseDouble(Util.trim(mapAttri
								.get("SLDP_TOTAL")));
						balTrustFdta = Util.parseDouble(Util.trim(mapAttri
								.get("FDTA_T_TOTAL")));
						if (bal != 0) {
							bal = Util.parseDouble(CapMath.round(
									Util.trim(bal / 1000), 0));
						}
						if (balSalary != 0) {
							balSalary = Util.parseDouble(CapMath.round(
									Util.trim(balSalary / 1000), 0));
						}
						if (balTrustFdta != 0) {
							balTrustFdta = Util.parseDouble(CapMath.round(
									Util.trim(balTrustFdta / 1000), 0));
						}
						break;
					}
					// 海外貢獻度(非存款) Miller added at 2012/07/27
					while (it8.hasNext()) {
						Map<?, ?> dataMap8 = (Map<?, ?>) it8.next();
						double seaBal = Util.parseDouble(Util.trim(dataMap8
								.get("TOTAL_ATTRIBUTE")));
						double seaSalaryBal = Util.parseDouble(Util
								.trim(dataMap8.get("SLDP_TOTAL")));
						double seaTrustFdtaBal = Util.parseDouble(Util
								.trim(dataMap8.get("FDTA_T_TOTAL")));
						String curr = Util.trim(dataMap8.get("CURR"));
						if (seaBal != 0) {
							seaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaBal)).doubleValue();
							seaBal = Util.parseDouble(CapMath.round(
									Util.trim(seaBal / 1000), 0));
						}
						if (seaSalaryBal != 0) {
							seaSalaryBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaSalaryBal))
									.doubleValue();
							seaSalaryBal = Util.parseDouble(CapMath.round(
									Util.trim(seaSalaryBal / 1000), 0));
						}
						if (seaTrustFdtaBal != 0) {
							seaTrustFdtaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaTrustFdtaBal))
									.doubleValue();
							seaTrustFdtaBal = Util.parseDouble(CapMath.round(
									Util.trim(seaTrustFdtaBal / 1000), 0));
						}
						bal += seaBal;
						balSalary += seaSalaryBal;
						balTrustFdta += seaTrustFdtaBal;
					}
					end3Mon = CapDate.formatDate(dQueryDateE, "MM");

					if (Util.trim(tID).equals(mCustId)
							&& Util.trim(tDUPNO).equals(_mDupNo)) {
						jsonData.put("field2_3", Util.trim(Util
								.parseLong(tempJson3.get("depTime"))
								+ Util.parseLong(tempJson3.get("depFixed"))));
						jsonData.put("field3_3",
								Util.trim(tempJson3.get("loanAvgBal")));
						jsonData.put("field4_3", Util.trim(Util
								.parseLong(tempJson3.get("exchgImpAmt"))
								+ Util.parseLong(tempJson3.get("exchgOutAmt"))));
						jsonData.put("field5_3", Util.trim(Util
								.parseLong(tempJson3.get("exchgExpAmt"))
								+ Util.parseLong(tempJson3.get("exchgInAmt"))));
						jsonData.put("field6_3", Util.trim(bal));
						jsonData.put("field8_3",
								Util.trim(tempJson3.get("IN_LN_FA_B")));
						jsonData.put("field9_3",
								Util.trim(tempJson3.get("IN_LN_FA_S")));
						jsonData.put("fieldB_3", Util.trim(balSalary));
						jsonData.put("fieldC_3", Util.trim(balTrustFdta));
					}
					jsonData.put("field2_3_1", Util.trim(Util
							.parseLong(jsonData.get("field2_3_1"))
							+ Util.parseLong(tempJson3.get("depTime"))
							+ Util.parseLong(tempJson3.get("depFixed"))));
					jsonData.put("field3_3_1", Util.trim(Util
							.parseLong(jsonData.get("field3_3_1"))
							+ Util.parseLong(tempJson3.get("loanAvgBal"))));
					jsonData.put("field4_3_1", Util.trim(Util
							.parseLong(jsonData.get("field4_3_1"))
							+ Util.parseLong(tempJson3.get("exchgImpAmt"))
							+ Util.parseLong(tempJson3.get("exchgOutAmt"))));
					jsonData.put("field5_3_1", Util.trim(Util
							.parseLong(jsonData.get("field5_3_1"))
							+ Util.parseLong(tempJson3.get("exchgExpAmt"))
							+ Util.parseLong(tempJson3.get("exchgInAmt"))));
					jsonData.put("field6_3_1", Util.trim(Util
							.parseLong(jsonData.get("field6_3_1"))
							+ Util.parseLong(bal)));
					jsonData.put("field8_3_1", Util.trim(Util
							.parseLong(jsonData.get("field8_3_1"))
							+ Util.parseLong(tempJson3.get("IN_LN_FA_B"))));
					jsonData.put("field9_3_1", Util.trim(Util
							.parseLong(jsonData.get("field9_3_1"))
							+ Util.parseLong(tempJson3.get("IN_LN_FA_S"))));
					jsonData.put("fieldB_3_1", Util.trim(Util
							.parseLong(jsonData.get("fieldB_3_1"))
							+ Util.parseLong(balSalary)));
					jsonData.put("fieldC_3_1", Util.trim(Util
							.parseLong(jsonData.get("fieldC_3_1"))
							+ Util.parseLong(balTrustFdta)));

					// a Method
					// 100年 平均存款 平均授信 開狀及匯出 出押及匯入
					String year1 = (MAX_CYC_MN.length() < 4) ? null
							: MAX_CYC_MN.substring(0, 4);
					if (Util.isNotEmpty(year1)) {
						dQueryDateS = CapDate.getDate(
								Util.trim(Util.parseInt(year1) - 1) + "-01-01",
								UtilConstants.DateFormat.YYYY_MM_DD);
						dQueryDateE = CapDate.getDate(
								Util.trim(Util.parseInt(year1) - 1) + "-12-01",
								UtilConstants.DateFormat.YYYY_MM_DD);
					}
					JSONObject tempJson4 = getL120S04B(mainId, tID, tDUPNO,
							CapDate.formatDate(dQueryDateS,
									UtilConstants.DateFormat.YYYY_MM_DD),
							CapDate.formatDate(dQueryDateE,
									UtilConstants.DateFormat.YYYY_MM_DD));
					List<Map<String, Object>> mapBal2 = this.dwdbBASEService
							.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					List<?> rows82 = this.dwdbBASEService
							.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					Iterator<?> it82 = rows82.iterator();
					if (tempJson4 == null) {
						// "引進"+tID+" " + xr+"業務往來資訊錯誤"
						return MessageFormat.format(
								prop.getProperty("L1205S07.error23"), tID,
								tDUPNO);
					}
					if (mapBal2.isEmpty()) {
						// "引進"+tID+" " + xr+"利潤貢獻度錯誤"
						return MessageFormat.format(
								prop.getProperty("L1205S07.error24"), tID,
								tDUPNO);
					}
					for (Map<String, Object> mapAttri : mapBal2) {
						bal = Util.parseDouble(Util.trim(mapAttri
								.get("TOTAL_ATTRIBUTE")));
						balSalary = Util.parseDouble(Util.trim(mapAttri
								.get("SLDP_TOTAL")));
						balTrustFdta = Util.parseDouble(Util.trim(mapAttri
								.get("FDTA_T_TOTAL")));
						if (bal != 0) {
							bal = Util.parseDouble(CapMath.round(
									Util.trim(bal / 1000), 0));
						}
						if (balSalary != 0) {
							balSalary = Util.parseDouble(CapMath.round(
									Util.trim(balSalary / 1000), 0));
						}
						if (balTrustFdta != 0) {
							balTrustFdta = Util.parseDouble(CapMath.round(
									Util.trim(balTrustFdta / 1000), 0));
						}
						break;
					}
					// 海外貢獻度(非存款) Miller added at 2012/07/27
					while (it82.hasNext()) {
						Map<?, ?> dataMap8 = (Map<?, ?>) it82.next();
						double seaBal = Util.parseDouble(Util.trim(dataMap8
								.get("TOTAL_ATTRIBUTE")));
						double seaSalaryBal = Util.parseDouble(Util
								.trim(dataMap8.get("SLDP_TOTAL")));
						double seaTrustFdtaBal = Util.parseDouble(Util
								.trim(dataMap8.get("FDTA_T_TOTAL")));
						String curr = Util.trim(dataMap8.get("CURR"));
						if (seaBal != 0) {
							seaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaBal)).doubleValue();
							seaBal = Util.parseDouble(CapMath.round(
									Util.trim(seaBal / 1000), 0));
						}
						if (seaSalaryBal != 0) {
							seaSalaryBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaSalaryBal))
									.doubleValue();
							seaSalaryBal = Util.parseDouble(CapMath.round(
									Util.trim(seaSalaryBal / 1000), 0));
						}
						if (seaTrustFdtaBal != 0) {
							seaTrustFdtaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaTrustFdtaBal))
									.doubleValue();
							seaTrustFdtaBal = Util.parseDouble(CapMath.round(
									Util.trim(seaTrustFdtaBal / 1000), 0));
						}

						bal += seaBal;
						balSalary += seaSalaryBal;
						balTrustFdta += seaTrustFdtaBal;
					}
					jsonData.put("field1_2",
							CapDate.formatDate(dQueryDateS, "yyyy") + "年");
					jsonData.put("field1_2_1",
							CapDate.formatDate(dQueryDateS, "yyyy") + "年");

					if (Util.trim(tID).equals(mCustId)
							&& Util.trim(tDUPNO).equals(_mDupNo)) {
						jsonData.put("field2_2", Util.trim(Util
								.parseLong(tempJson4.get("depTime"))
								+ Util.parseLong(tempJson4.get("depFixed"))));
						jsonData.put("field3_2",
								Util.trim(tempJson4.get("loanAvgBal")));
						jsonData.put("field4_2", Util.trim(Util
								.parseLong(tempJson4.get("exchgImpAmt"))
								+ Util.parseLong(tempJson4.get("exchgOutAmt"))));
						jsonData.put("field5_2", Util.trim(Util
								.parseLong(tempJson4.get("exchgExpAmt"))
								+ Util.parseLong(tempJson4.get("exchgInAmt"))));
						jsonData.put("field6_2", Util.trim(bal));
						jsonData.put("field8_2",
								Util.trim(tempJson4.get("IN_LN_FA_B")));
						jsonData.put("field9_2",
								Util.trim(tempJson4.get("IN_LN_FA_S")));
						jsonData.put("fieldB_2", Util.trim(balSalary));
						jsonData.put("fieldC_2", Util.trim(balTrustFdta));
					}
					jsonData.put("field2_2_1", Util.trim(Util
							.parseLong(jsonData.get("field2_2_1"))
							+ Util.parseLong(tempJson4.get("depTime"))
							+ Util.parseLong(tempJson4.get("depFixed"))));
					jsonData.put("field3_2_1", Util.trim(Util
							.parseLong(jsonData.get("field3_2_1"))
							+ Util.parseLong(tempJson4.get("loanAvgBal"))));
					jsonData.put("field4_2_1", Util.trim(Util
							.parseLong(jsonData.get("field4_2_1"))
							+ Util.parseLong(tempJson4.get("exchgImpAmt"))
							+ Util.parseLong(tempJson4.get("exchgOutAmt"))));
					jsonData.put("field5_2_1", Util.trim(Util
							.parseLong(jsonData.get("field5_2_1"))
							+ Util.parseLong(tempJson4.get("exchgExpAmt"))
							+ Util.parseLong(tempJson4.get("exchgInAmt"))));
					jsonData.put("field6_2_1", Util.trim(Util
							.parseLong(jsonData.get("field6_2_1"))
							+ Util.parseLong(bal)));
					jsonData.put("field8_2_1", Util.trim(Util
							.parseLong(jsonData.get("field8_2_1"))
							+ Util.parseLong(tempJson4.get("IN_LN_FA_B"))));
					jsonData.put("field9_2_1", Util.trim(Util
							.parseLong(jsonData.get("field9_2_1"))
							+ Util.parseLong(tempJson4.get("IN_LN_FA_S"))));
					jsonData.put("fieldB_2_1", Util.trim(Util
							.parseLong(jsonData.get("fieldB_2_1"))
							+ Util.parseLong(balSalary)));
					jsonData.put("fieldC_2_1", Util.trim(Util
							.parseLong(jsonData.get("fieldC_2_1"))
							+ Util.parseLong(balTrustFdta)));

					// a Method
					// 99年 平均存款 平均授信 開狀及匯出 出押及匯入
					String year2 = (MAX_CYC_MN.length() < 4) ? null
							: MAX_CYC_MN.substring(0, 4);
					if (Util.isNotEmpty(year2)) {
						dQueryDateS = CapDate.getDate(
								Util.trim(Util.parseInt(year2) - 2) + "-01-01",
								UtilConstants.DateFormat.YYYY_MM_DD);
						dQueryDateE = CapDate.getDate(
								Util.trim(Util.parseInt(year2) - 2) + "-12-01",
								UtilConstants.DateFormat.YYYY_MM_DD);
					}
					JSONObject tempJson5 = getL120S04B(mainId, tID, tDUPNO,
							CapDate.formatDate(dQueryDateS,
									UtilConstants.DateFormat.YYYY_MM_DD),
							CapDate.formatDate(dQueryDateE,
									UtilConstants.DateFormat.YYYY_MM_DD));
					List<Map<String, Object>> mapBal3 = this.dwdbBASEService
							.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					List<?> rows83 = this.dwdbBASEService
							.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					Iterator<?> it83 = rows83.iterator();
					if (tempJson5 == null) {
						// "引進"+tID+" " + xr+"業務往來資訊錯誤"
						return MessageFormat.format(
								prop.getProperty("L1205S07.error23"), tID,
								tDUPNO);
					}
					if (mapBal3.isEmpty()) {
						// "引進"+tID+" " + xr+"利潤貢獻度錯誤"
						return MessageFormat.format(
								prop.getProperty("L1205S07.error24"), tID,
								tDUPNO);
					}
					for (Map<String, Object> mapAttri : mapBal3) {
						bal = Util.parseDouble(Util.trim(mapAttri
								.get("TOTAL_ATTRIBUTE")));
						balSalary = Util.parseDouble(Util.trim(mapAttri
								.get("SLDP_TOTAL")));
						balTrustFdta = Util.parseDouble(Util.trim(mapAttri
								.get("FDTA_T_TOTAL")));
						if (bal != 0) {
							bal = Util.parseDouble(CapMath.round(
									Util.trim(bal / 1000), 0));
						}
						if (balSalary != 0) {
							balSalary = Util.parseDouble(CapMath.round(
									Util.trim(balSalary / 1000), 0));
						}
						if (balTrustFdta != 0) {
							balTrustFdta = Util.parseDouble(CapMath.round(
									Util.trim(balTrustFdta / 1000), 0));
						}
						break;
					}
					// 海外貢獻度(非存款) Miller added at 2012/07/27
					while (it83.hasNext()) {
						Map<?, ?> dataMap8 = (Map<?, ?>) it83.next();
						double seaBal = Util.parseDouble(Util.trim(dataMap8
								.get("TOTAL_ATTRIBUTE")));
						double seaSalaryBal = Util.parseDouble(Util
								.trim(dataMap8.get("SLDP_TOTAL")));
						double seaTrustFdtaBal = Util.parseDouble(Util
								.trim(dataMap8.get("FDTA_T_TOTAL")));
						String curr = Util.trim(dataMap8.get("CURR"));
						if (seaBal != 0) {
							seaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaBal)).doubleValue();
							seaBal = Util.parseDouble(CapMath.round(
									Util.trim(seaBal / 1000), 0));
						}
						if (seaSalaryBal != 0) {
							seaSalaryBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaSalaryBal))
									.doubleValue();
							seaSalaryBal = Util.parseDouble(CapMath.round(
									Util.trim(seaSalaryBal / 1000), 0));
						}
						if (seaTrustFdtaBal != 0) {
							seaTrustFdtaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaTrustFdtaBal))
									.doubleValue();
							seaTrustFdtaBal = Util.parseDouble(CapMath.round(
									Util.trim(seaTrustFdtaBal / 1000), 0));
						}
						bal += seaBal;
						balSalary += seaSalaryBal;
						balTrustFdta += seaTrustFdtaBal;
					}
					jsonData.put("field1_1",
							CapDate.formatDate(dQueryDateS, "yyyy") + "年");
					jsonData.put("field1_1_1",
							CapDate.formatDate(dQueryDateS, "yyyy") + "年");

					if (Util.trim(tID).equals(mCustId)
							&& Util.trim(tDUPNO).equals(_mDupNo)) {
						jsonData.put("field2_1", Util.trim(Util
								.parseLong(tempJson5.get("depTime"))
								+ Util.parseLong(tempJson5.get("depFixed"))));
						jsonData.put("field3_1",
								Util.trim(tempJson5.get("loanAvgBal")));
						jsonData.put("field4_1", Util.trim(Util
								.parseLong(tempJson5.get("exchgImpAmt"))
								+ Util.parseLong(tempJson5.get("exchgOutAmt"))));
						jsonData.put("field5_1", Util.trim(Util
								.parseLong(tempJson5.get("exchgExpAmt"))
								+ Util.parseLong(tempJson5.get("exchgInAmt"))));
						jsonData.put("field6_1", Util.trim(bal));
						jsonData.put("field8_1",
								Util.trim(tempJson5.get("IN_LN_FA_B")));
						jsonData.put("field9_1",
								Util.trim(tempJson5.get("IN_LN_FA_S")));
						jsonData.put("fieldB_1", Util.trim(balSalary));
						jsonData.put("fieldC_1", Util.trim(balTrustFdta));
					}
					jsonData.put("field2_1_1", Util.trim(Util
							.parseLong(jsonData.get("field2_1_1"))
							+ Util.parseLong(tempJson5.get("depTime"))
							+ Util.parseLong(tempJson5.get("depFixed"))));
					jsonData.put("field3_1_1", Util.trim(Util
							.parseLong(jsonData.get("field3_1_1"))
							+ Util.parseLong(tempJson5.get("loanAvgBal"))));
					jsonData.put("field4_1_1", Util.trim(Util
							.parseLong(jsonData.get("field4_1_1"))
							+ Util.parseLong(tempJson5.get("exchgImpAmt"))
							+ Util.parseLong(tempJson5.get("exchgOutAmt"))));
					jsonData.put("field5_1_1", Util.trim(Util
							.parseLong(jsonData.get("field5_1_1"))
							+ Util.parseLong(tempJson5.get("exchgExpAmt"))
							+ Util.parseLong(tempJson5.get("exchgInAmt"))));
					jsonData.put("field6_1_1", Util.trim(Util
							.parseLong(jsonData.get("field6_1_1"))
							+ Util.parseLong(bal)));
					jsonData.put("field8_1_1", Util.trim(Util
							.parseLong(jsonData.get("field8_1_1"))
							+ Util.parseLong(tempJson5.get("IN_LN_FA_B"))));
					jsonData.put("field9_1_1", Util.trim(Util
							.parseLong(jsonData.get("field9_1_1"))
							+ Util.parseLong(tempJson5.get("IN_LN_FA_S"))));
					jsonData.put("fieldB_1_1", Util.trim(Util
							.parseLong(jsonData.get("fieldB_1_1"))
							+ Util.parseLong(balSalary)));
					jsonData.put("fieldC_1_1", Util.trim(Util
							.parseLong(jsonData.get("fieldC_1_1"))
							+ Util.parseLong(balTrustFdta)));
				}

				// a Method
				for (int i = 1; i <= 3; i++) {
					if (i == 3) {
						// 因資料未滿一年，所以報酬率要年化
						if (Util.parseDouble(jsonData.get("field3_"
								+ Util.trim(i)))
								- Util.parseDouble(jsonData.get("field8_"
										+ Util.trim(i)))
								+ Util.parseDouble(jsonData.get("field9_"
										+ Util.trim(i))) > 0) {
							jsonData.put(
									"field7_" + Util.trim(i),
									Util.trim(Util.parseDouble(

									CapMath.round(
											Util.trim((Util.parseDouble(Util.trim(jsonData
													.get("field6_"
															+ Util.trim(i)))) / (Util.parseDouble(Util.trim(jsonData
													.get("field3_"
															+ Util.trim(i))))
													- Util.parseDouble(Util.trim(jsonData.get("field8_"
															+ Util.trim(i)))) + Util.parseDouble(Util.trim(jsonData
													.get("field9_"
															+ Util.trim(i))))))
													/ Util.parseDouble(end3Mon)
													* 12), 4)) * 100));
						} else {
							jsonData.put("field7_" + Util.trim(i), "N.A.");
						}
						if (Util.parseDouble(jsonData.get("field3_"
								+ Util.trim(i) + "_1"))
								- Util.parseDouble(jsonData.get("field8_"
										+ Util.trim(i) + "_1"))
								+ Util.parseDouble(jsonData.get("field9_"
										+ Util.trim(i) + "_1")) > 0) {
							jsonData.put(
									"field7_" + Util.trim(i) + "_1",
									Util.trim(Util.parseDouble(CapMath.round(
											Util.trim((Util.parseDouble(Util.trim(jsonData
													.get("field6_"
															+ Util.trim(i)
															+ "_1"))) / (Util.parseDouble(Util.trim(jsonData
													.get("field3_"
															+ Util.trim(i)
															+ "_1")))
													- Util.parseDouble(Util.trim(jsonData.get("field8_"
															+ Util.trim(i)
															+ "_1"))) + Util.parseDouble(Util.trim(jsonData
													.get("field9_"
															+ Util.trim(i)
															+ "_1")))))
													/ Util.parseDouble(end3Mon)
													* 12), 4)) * 100));
						} else {
							jsonData.put("field7_" + Util.trim(i) + "_1",
									"N.A.");
						}
					} else {
						if (Util.parseDouble(jsonData.get("field3_"
								+ Util.trim(i)))
								- Util.parseDouble(jsonData.get("field8_"
										+ Util.trim(i)))
								+ Util.parseDouble(jsonData.get("field9_"
										+ Util.trim(i))) > 0) {
							jsonData.put(
									"field7_" + Util.trim(i),
									Util.trim(Util.parseDouble(

									CapMath.round(
											Util.trim((Util.parseDouble(Util.trim(jsonData
													.get("field6_"
															+ Util.trim(i)))) / (Util.parseDouble(Util.trim(jsonData
													.get("field3_"
															+ Util.trim(i))))
													- Util.parseDouble(Util.trim(jsonData.get("field8_"
															+ Util.trim(i)))) + Util.parseDouble(Util.trim(jsonData
													.get("field9_"
															+ Util.trim(i))))))),
											4)) * 100));
						} else {
							jsonData.put("field7_" + Util.trim(i), "N.A.");
						}
						if (Util.parseDouble(jsonData.get("field3_"
								+ Util.trim(i) + "_1"))
								- Util.parseDouble(jsonData.get("field8_"
										+ Util.trim(i) + "_1"))
								+ Util.parseDouble(jsonData.get("field9_"
										+ Util.trim(i) + "_1")) > 0) {
							jsonData.put(
									"field7_" + Util.trim(i) + "_1",
									Util.trim(Util.parseDouble(CapMath.round(
											Util.trim((Util.parseDouble(Util.trim(jsonData
													.get("field6_"
															+ Util.trim(i)
															+ "_1"))) / (Util.parseDouble(Util.trim(jsonData
													.get("field3_"
															+ Util.trim(i)
															+ "_1")))
													- Util.parseDouble(Util.trim(jsonData.get("field8_"
															+ Util.trim(i)
															+ "_1"))) + Util.parseDouble(Util.trim(jsonData
													.get("field9_"
															+ Util.trim(i)
															+ "_1")))))), 4)) * 100));
						} else {
							jsonData.put("field7_" + Util.trim(i) + "_1",
									"N.A.");
						}
					}
				}

				if (Util.isNotEmpty(Util.trim(jsonData.get("Rate3_3")))
						&& !"0".equals(Util.trim(jsonData.get("Rate3_3")))) {
					jsonData.put("Rate3_5", Util.trim(Util.parseDouble(CapMath
							.round(Util.trim(Util.parseDouble(Util
									.trim(jsonData.get("Rate3_4")))
									/ Util.parseDouble(Util.trim(jsonData
											.get("Rate3_3")))), 4)) * 100));
				} else {
					jsonData.put("Rate3_5", "N.A.");
				}
			}
			// }
		}
		// result.set("l120s04b", DataParse.toResult(l120s04b));

		// 開始匯率轉換
		String[] jsonKeys = Util.getMapKey(jsonData);
		for (String jsonKey : jsonKeys) {
			if (jsonKey.contains("field4") || jsonKey.contains("field5")) {
				jsonData.put(jsonKey, CapMath.round(
						branchRate
								.toUSDAmt(
										"TWD",
										new BigDecimal(Util.trim(jsonData
												.get(jsonKey)))).toString(), 0));
			}
		}

		// // J-103-0419-001 Web e-Loan授信管理系統關係戶往來實績彙總表(ROA表)新增引進風險性資產平均餘額報酬率
		String raReturn = importL120s04b_Risk_weighted_Assets(mainId,
				keyCustId, keyDupNo, queryDateS, queryDateE, jsonData,
				branchRate);
		if (Util.notEquals(raReturn, "")) {
			return raReturn;
		}

		// result.set("jsonData", DataParse.toResult(jsonData));
		this.saveL120S04BC(mainId, jsonData, keyCustId, keyDupNo);

		return "";
	}

	private L120S04B setL120s04b(String mainId, JSONObject jsonData,
			String[] cols) {
		L120S04B l120s04b = initL120s04b(mainId);
		for (String col : cols) {
			if (jsonData.containsKey(col)) {
				l120s04b = setColToModel(l120s04b, col, jsonData);
			}
		}
		return l120s04b;
	}

	private L120S04C setL120s04c(String mainId, String docKind,
			JSONObject jsonData, String[] cols) {
		L120S04C l120s04c = initL120s04c(mainId, docKind);
		for (String col : cols) {
			if (jsonData.containsKey(col)) {
				l120s04c = setColToModel(l120s04c, col, jsonData);
			}
		}
		return l120s04c;
	}

	private L120S04B setColToModel(L120S04B model, String col,
			JSONObject jsonData) {
		if (("GpID").equals(col)) {
			// 隸屬集團代號 grpNo
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setGrpNo(Util.trim(jsonData.get(col)));
			}
		} else if (("GpName").equals(col)) {
			// 隸屬集團 grpName
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setGrpName(Util.trim(jsonData.get(col)));
			}
		} else if (("GpDATAYY").equals(col)) {
			// 集團評等年度 grpYear
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setGrpYear(Util.parseInt(NumConverter.delCommaString(Util
						.trim(jsonData.get(col)))));
			}
		} else if (("GpGRADE").equals(col)) {
			// 集團評等 grpGrrd
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setGrpGrrd(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate1_2").equals(col)) {
			// 主要集團企業最近一年起日 mainGrpDateS
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpDateS(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate1_3").equals(col)) {
			// 主要集團企業最近一年迄日 mainGrpDateE
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpDateE(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate1").equals(col)) {
			// 主要集團企業-平均餘額報酬率 mainGrpAvgRate
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpAvgRate(("N.A.".equals(Util.trim(jsonData
						.get(col)))) ? null : new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (("Rate3_1").equals(col)) {
			// 借戶暨關係戶近半年起日 depositDateS
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setDepositDateS(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate3_2").equals(col)) {
			// 借戶暨關係戶近半年迄日 depositDateE
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setDepositDateE(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate3_3").equals(col)) {
			// 本行平均存款合計－金額 megaAvgAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMegaAvgAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (("Rate3_4").equals(col)) {
			// 活期性存款－金額 demandAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setDemandAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (("Rate3_5").equals(col)) {
			// 活期性存款所占比率 demandAvgRate
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setDemandAvgRate(("N.A.".equals(Util.trim(jsonData
						.get(col)))) ? null : new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (("Rate4_2").equals(col)) {
			// 主借款人最近一年起日 mainGrpDateS
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpDateMS(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate4_3").equals(col)) {
			// 主借款人最近一年迄日 mainGrpDateE
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpDateME(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate4").equals(col)) {
			// 主借款人-平均餘額報酬率 mainGrpAvgRate
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpAvgRateM(("N.A.".equals(Util.trim(jsonData
						.get(col)))) ? null : new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (("Rate5_2").equals(col)) {
			// 主借款人最近一年起日 mainGrpDateS
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpDateRS(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate5_3").equals(col)) {
			// 主借款人最近一年迄日 mainGrpDateE
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpDateRE(Util.trim(jsonData.get(col)));
			}
		} else if (("Rate5").equals(col)) {
			// 主借款人-平均餘額報酬率 mainGrpAvgRate
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setMainGrpAvgRateR(("N.A.".equals(Util.trim(jsonData
						.get(col)))) ? null : new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級
		} else if (("GRPSIZE").equals(col)) {
			// 集團企業規模 grpSize
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setGrpSize(Util.trim(jsonData.get(col)));
			}
		} else if (("GRPLEVEL").equals(col)) {
			// 集團企業規模級別 grpLevel
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setGrpLevel(Util.trim(jsonData.get(col)));
			}
		}
		return model;
	}

	private L120S04C setColToModel(L120S04C model, String col,
			JSONObject jsonData) {
		if (col.contains("field1")) {
			// 資料年月(起) docDateS 資料年月(迄) docDateE
			if (col.equals("field1_3") || col.equals("field1_3_1")) {
				// 101/1-7月
				if (Util.isNotEmpty(jsonData.get(col))) {
					String colDate = Util.trim(jsonData.get(col));
					model.setDocDate(colDate);
					// model.setDocDateE(CapDate.getDate(
					// colDate.substring(0, colDate.length() - 1) + "-01",
					// UtilConstants.DateFormat.YYYY_MM_DD));
				}
			} else {
				// YYYY 年
				if (Util.isNotEmpty(jsonData.get(col))) {
					String colDate = Util.trim(jsonData.get(col));
					model.setDocDate(colDate);
				}
			}
		} else if (col.contains("field2")) {
			// 平均存款－金額 avgDepositAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setAvgDepositAmt(new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (col.contains("field3")) {
			// A平均授信－金額 avgLoanAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setAvgLoanAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (col.contains("field8")) {
			// Ｂ應收帳款無追索買方承購平均餘額－金額 rcvBuyAvgAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setRcvBuyAvgAmt(new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (col.contains("field9")) {
			// Ｃ應收帳款無追索權賣方融資平均餘額－金額 rcvSellAvgAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setRcvSellAvgAmt(new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (col.contains("field4")) {
			// 進押及匯出－金額 exportAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setExportAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (col.contains("field5")) {
			// 出押及匯入－金額 importAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setImportAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (col.contains("field6")) {
			// Ｄ利潤貢獻－金額 profitAmt
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setProfitAmt(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (col.contains("field7")) {
			// 報酬率 profitRate
			if (Util.isNotEmpty(jsonData.get(col))
					&& !"N.A.".equals(Util.trim(jsonData.get(col)))) {
				model.setProfitRate(new BigDecimal(Util.trim(jsonData.get(col))));
			}
		} else if (col.contains("fieldB")) {
			// 企業戶員工薪轉貢獻度
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setProfitSalaryAmt(new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		} else if (col.contains("fieldC")) {
			// 信託專戶利差
			if (Util.isNotEmpty(jsonData.get(col))) {
				model.setProfitTrustFdtaAmt(new BigDecimal(Util.trim(jsonData
						.get(col))));
			}
		}
		return model;
	}

	private L120S04B initL120s04b(String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp timeNow = CapDate.getCurrentTimestamp();
		L120S04B l120s04b = new L120S04B();
		l120s04b.setMainId(mainId);
		l120s04b.setGrpNo(null);
		l120s04b.setGrpName(null);
		l120s04b.setGrpYear(null);
		l120s04b.setGrpGrrd(null);
		l120s04b.setMainGrpDateS(null);
		l120s04b.setMainGrpDateE(null);
		l120s04b.setMainGrpAvgRate(BigDecimal.ZERO);
		l120s04b.setDepositDateS(null);
		l120s04b.setDepositDateE(null);
		l120s04b.setMegaAvgAmt(BigDecimal.ZERO);
		l120s04b.setDemandAmt(BigDecimal.ZERO);
		l120s04b.setDemandAvgRate(BigDecimal.ZERO);
		l120s04b.setCreateTime(timeNow);
		l120s04b.setCreator(user.getUserId());
		l120s04b.setUpdateTime(timeNow);
		l120s04b.setUpdater(user.getUserId());
		// J-107-0087-001 Web
		// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
		l120s04b.setGrpSize(null);
		l120s04b.setGrpLevel(null);
		return l120s04b;
	}

	private L120S04C initL120s04c(String mainId, String docKind) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp timeNow = CapDate.getCurrentTimestamp();
		L120S04C l120s04c = new L120S04C();
		l120s04c.setMainId(mainId);
		l120s04c.setDocKind(docKind);
		l120s04c.setDocDate(null);
		l120s04c.setDocDateE(null);
		l120s04c.setAvgDepositAmt(BigDecimal.ZERO);
		l120s04c.setAvgLoanAmt(BigDecimal.ZERO);
		l120s04c.setRcvBuyAvgAmt(BigDecimal.ZERO);
		l120s04c.setRcvSellAvgAmt(BigDecimal.ZERO);
		l120s04c.setExportAmt(BigDecimal.ZERO);
		l120s04c.setImportAmt(BigDecimal.ZERO);
		l120s04c.setProfitAmt(BigDecimal.ZERO);
		l120s04c.setProfitSalaryAmt(BigDecimal.ZERO);
		l120s04c.setProfitTrustFdtaAmt(BigDecimal.ZERO);
		l120s04c.setProfitRate(BigDecimal.ZERO);
		l120s04c.setCreateTime(timeNow);
		l120s04c.setCreator(user.getUserId());
		l120s04c.setUpdateTime(timeNow);
		l120s04c.setUpdater(user.getUserId());
		return l120s04c;
	}

	/**
	 * 將JsonData資料儲存到L120S04B,L120S04C Model裡
	 * 
	 * @param mainId
	 * @param jsonData
	 */
	private void saveL120S04BC(String mainId, JSONObject jsonData,
			String keyCustId, String keyDupNo) {
		// J-107-0087-001 Web
		// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
		String[] s4aCol = new String[] { "GpID", "GpName", "GpDATAYY",
				"GpGRADE", "Rate1_2", "Rate1_3", "Rate1", "Rate3_1", "Rate3_2",
				"Rate3_3", "Rate3_4", "Rate3_5", "Rate4_2", "Rate4_3", "Rate4",
				"Rate5_2", "Rate5_3", "Rate5", "GRPSIZE", "GRPLEVEL" };
		String[] s04bCola1 = new String[] { "field1", "field2", "field3",
				"field8", "field9", "field4", "field5", "field6_1", "field7_1",
				"fieldB_1", "fieldC_1" };
		String[] s04bCola2 = new String[] { "field1_1", "field2_1", "field3_1",
				"field8_1", "field9_1", "field4_1", "field5_1", "field6_1",
				"field7_1", "fieldB_1", "fieldC_1" };
		String[] s04bCola3 = new String[] { "field1_2", "field2_2", "field3_2",
				"field8_2", "field9_2", "field4_2", "field5_2", "field6_2",
				"field7_2", "fieldB_2", "fieldC_2" };
		String[] s04bCola4 = new String[] { "field1_3", "field2_3", "field3_3",
				"field8_3", "field9_3", "field4_3", "field5_3", "field6_3",
				"field7_3", "fieldB_3", "fieldC_3" };
		String[] s04bColb1 = new String[] { "field1_1_1", "field2_1_1",
				"field3_1_1", "field8_1_1", "field9_1_1", "field4_1_1",
				"field5_1_1", "field6_1_1", "field7_1_1", "fieldB_1_1",
				"fieldC_1_1" };
		String[] s04bColb2 = new String[] { "field1_2_1", "field2_2_1",
				"field3_2_1", "field8_2_1", "field9_2_1", "field4_2_1",
				"field5_2_1", "field6_2_1", "field7_2_1", "fieldB_2_1",
				"fieldC_2_1" };
		String[] s04bColb3 = new String[] { "field1_3_1", "field2_3_1",
				"field3_3_1", "field8_3_1", "field9_3_1", "field4_3_1",
				"field5_3_1", "field6_3_1", "field7_3_1", "fieldB_3_1",
				"fieldC_3_1" };
		L120S04B l120s04b = new L120S04B();
		// 進行設值關係戶於本行往來實績彙總表主檔
		l120s04b = setL120s04b(mainId, jsonData, s4aCol);
		l120s04b.setKeyCustId(keyCustId);
		l120s04b.setKeyDupNo(keyDupNo);

		List<L120S04C> listL120s04c = new ArrayList<L120S04C>();
		// 進行設值關係戶於本行往來實績彙總表明細檔
		listL120s04c.add(setL120s04c(mainId, "1", jsonData, s04bCola1));
		listL120s04c.add(setL120s04c(mainId, "1", jsonData, s04bCola2));
		listL120s04c.add(setL120s04c(mainId, "1", jsonData, s04bCola3));
		listL120s04c.add(setL120s04c(mainId, "1", jsonData, s04bCola4));
		listL120s04c.add(setL120s04c(mainId, "2", jsonData, s04bColb1));
		listL120s04c.add(setL120s04c(mainId, "2", jsonData, s04bColb2));
		listL120s04c.add(setL120s04c(mainId, "2", jsonData, s04bColb3));

		for (L120S04C l120s04c : listL120s04c) {
			l120s04c.setKeyCustId(keyCustId);
			l120s04c.setKeyDupNo(keyDupNo);
		}
		// 進行儲存
		l120s04b.setDocKind("0");
		this.save(l120s04b);
		if (listL120s04c.size() > 0) {
			l120s04cDao.save(listL120s04c);
		}

	}

	@Override
	public void cancelPrint(String mainId, String[] oidArray) {
		List<L120S04A> list = new ArrayList<L120S04A>();
		List<L120S04A> listDel = new ArrayList<L120S04A>();
		for (String oid : oidArray) {
			L120S04A l120s04a = findL120s04aByOid(oid);
			if (l120s04a != null) {
				// 有資料(更新儲存)
				if (UtilConstants.Casedoc.L120s04aCreateBY.系統產生.equals(Util
						.trim(l120s04a.getCreateBY()))) {
					l120s04a.setPrtFlag(UtilConstants.Casedoc.L120s04aPrtFlag.不列印);
					list.add(l120s04a);
				} else if (UtilConstants.Casedoc.L120s04aCreateBY.人工產生
						.equals(Util.trim(l120s04a.getCreateBY()))) {
					listDel.add(l120s04a);
				}
			}
		}
		// ==================
		// 儲存
		// l120m01a
		L120M01A meta = findL120m01aByMainId(mainId);
		save(meta);
		// l120s04a-update
		if (!list.isEmpty()) {
			l120s04aDao.save(list);
		}
		// l120s04a-delete刪除人工資料
		if (!listDel.isEmpty()) {
			l120s04aDao.delete(listDel);
		}
	}

	@Override
	public void undoPrint(String mainId, String[] oidArray) {
		List<L120S04A> list = new ArrayList<L120S04A>();
		for (String oid : oidArray) {
			L120S04A l120s04a = findL120s04aByOid(oid);
			if (l120s04a != null) {
				// 有資料(更新儲存)
				l120s04a.setPrtFlag(UtilConstants.Casedoc.L120s04aPrtFlag.要列印);
				list.add(l120s04a);
			}
		}
		// ==================
		// 儲存
		// l120m01a
		L120M01A meta = findL120m01aByMainId(mainId);
		save(meta);
		// l120s04a-update
		if (!list.isEmpty()) {
			l120s04aDao.save(list);
		}
	}

	@Override
	public void saveL120s04bc(L120S04B model, List<L120S04C> list) {
		if (!list.isEmpty()) {
			l120s04cDao.save(list);
		}
		this.save(model);
	}

	// J-103-0419-001 Web e-Loan授信管理系統關係戶往來實績彙總表(ROA表)新增引進風險性資產平均餘額報酬率
	@SuppressWarnings("unused")
	@Override
	public String importL120s04b_Risk_weighted_Assets(String mainId,
			String keyCustId, String keyDupNo, String queryDateS,
			String queryDateE, JSONObject jsonData, BranchRate branchRate) {

		/*
		 * mode1:集團企業2.主借款人3.....
		 */

		L120M01A meta = this.findL120m01aByMainId(mainId);
		List<L120S04A> listL120s04a = this.findL120s04aByMainIdKeyCustIdDupNo(
				mainId, keyCustId, keyDupNo);
		// L120S04B l120s04b = new L120S04B();
		JSONObject json = new JSONObject();
		JSONObject jsonM = new JSONObject();
		JSONObject jsonG = new JSONObject();

		// JSONObject jsonData = new JSONObject();
		Date dQueryDateS = CapDate.getDate(queryDateS,
				UtilConstants.DateFormat.YYYY_MM_DD);
		Date dQueryDateE = CapDate.getDate(queryDateE,
				UtilConstants.DateFormat.YYYY_MM_DD);
		String MAX_CYC_MN = null;
		String MIN_CYC_MN = null;
		// ====
		// 原本用 meta.custId, 但是用在1份簽報書,只有主借款人, 才能做往來實績彙總表
		// 在加上欄位 keyCustId 後, 應該用 keyCustId
		String mCustId = keyCustId;
		String mDupNo = keyDupNo;
		// ====
		String _mDupNo = "0".equals(Util.trim(mDupNo)) ? "" : Util.trim(mDupNo);
		String tGPID = null;
		String tGPName = null;
		String tID = null;
		String tDUPNO = null;
		String tCUSTNAME = null;
		String tGRADE = null;
		String end3Mon = null;
		double bal = 0;

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(RelatedAccountPanel.class);

		if (listL120s04a.isEmpty()) {
			// L1205S07.error14 = 必須先執行【引進各關係戶往來彙總】才能執行本功能！
			return prop.getProperty("L1205S07.error14");
		} else {
			// 將除了集團、與關係企業計算合計以外的借款人資料記錄下來
			// a Method
			for (L120S04A model : listL120s04a) {
				String custRel = Util.trim(model.getCustRelation());
				String dupNo = Util.trim(model.getDupNo());
				StringBuilder sbFullId = new StringBuilder();
				sbFullId.append(Util.trim(model.getCustId())).append(
						Util.trim(dupNo).equals("0") ? " " : Util.trim(dupNo));

				if (!custRel.contains("3") && !custRel.contains("4")) {
					// 全部名單
					json.put(sbFullId.toString(),
							Util.trim(model.getCustName()));

					// 集團企業
					if (custRel.contains("5")) {
						jsonG.put(sbFullId.toString(),
								Util.trim(model.getCustName()));
					}

					// 主借戶
					if (custRel.contains("1")) {
						if (Util.equals(Util.trim(keyCustId),
								Util.trim(model.getCustId()))) {
							jsonM.put(sbFullId.toString(),
									Util.trim(model.getCustName()));
						}
					}
				}

			}
			// a Method

			Date max_cyc_mn = dwdbBASEService.getOTS_BSL2CSNET_AVG_max_cyc_mn();
			if (max_cyc_mn == null) {
				// L1205S07.error26=資料倉儲無風險性資產平均餘額之資料(DWADM.OTS_BSL2CSNET_AVG)
				return prop.getProperty("L1205S07.error26");
			}

			MAX_CYC_MN = CapDate.formatDate(max_cyc_mn,
					UtilConstants.DateFormat.YYYY_MM_DD);
			String _MAX_CYC_MN = MAX_CYC_MN.replace("-", "");
			// 依照風險性資產平均餘額(DWADM.OTS_BSL2CSNET_AVG)最大資料日往前推一年
			dQueryDateE = CapDate.getDate(MAX_CYC_MN,
					UtilConstants.DateFormat.YYYY_MM_DD);
			dQueryDateS = CapDate.getDate(CapDate.formatyyyyMMddToDateFormat(
					CapDate.addMonth(_MAX_CYC_MN, -11),
					UtilConstants.DateFormat.YYYY_MM_DD),
					UtilConstants.DateFormat.YYYY_MM_DD);

			// 主借款人與關係戶
			BigDecimal totalRaBal = BigDecimal.ZERO;
			BigDecimal totalAttrOTSBal = BigDecimal.ZERO;
			BigDecimal totalAttrOVSBal = BigDecimal.ZERO;
			// 主借款人
			BigDecimal totalRaBalM = BigDecimal.ZERO;
			BigDecimal totalAttrOTSBalM = BigDecimal.ZERO;
			BigDecimal totalAttrOVSBalM = BigDecimal.ZERO;
			// 主要集團
			BigDecimal totalRaBalG = BigDecimal.ZERO;
			BigDecimal totalAttrOTSBalG = BigDecimal.ZERO;
			BigDecimal totalAttrOVSBalG = BigDecimal.ZERO;

			// a Method
			if (!json.isEmpty()) {
				for (Object jsonKey : json.keySet()) {
					String sJsonKey = (String) jsonKey;
					String jsonVal = Util.trim(json.get(sJsonKey));
					// 擷取 "+tID+" "+ xr + " 與本行往來實績資料
					tID = (Util.isEmpty(sJsonKey)) ? null : sJsonKey.substring(
							0, sJsonKey.length() - 1);
					tDUPNO = (Util.isEmpty(sJsonKey)) ? null : sJsonKey
							.substring(sJsonKey.length() - 1);
					tCUSTNAME = jsonVal;

					// 風險性資產平均餘額
					List<Map<String, Object>> raBal = this.dwdbBASEService
							.findOTS_BSL2CSNET_AVG_TOTAL_RA_AMT(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));

					// 國內貢獻度
					List<Map<String, Object>> attrOTSBal = this.dwdbBASEService
							.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));

					// 海外貢獻度
					List<?> rows8 = this.dwdbBASEService
							.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(
									tID,
									tDUPNO,
									CapDate.formatDate(dQueryDateS,
											UtilConstants.DateFormat.YYYY_MM_DD),
									CapDate.formatDate(dQueryDateE,
											UtilConstants.DateFormat.YYYY_MM_DD));
					Iterator<?> it8 = rows8.iterator();

					if (raBal.isEmpty()) {
						// L1205S07.error27 = 引進{0}&nbsp;{1}風險性資產平均餘額錯誤
						return MessageFormat.format(
								prop.getProperty("L1205S07.error27"), tID,
								tDUPNO);
					}
					if (attrOTSBal.isEmpty()) {
						// L1205S07.error24 = 引進{0}&nbsp;{1}利潤貢獻度錯誤
						return MessageFormat.format(
								prop.getProperty("L1205S07.error24"), tID,
								tDUPNO);
					}

					// 合計*******************************************************
					for (Map<String, Object> ra : raBal) {
						bal = Util
								.parseDouble(Util.trim(ra.get("TOTAL_RA_AMT")));
						totalRaBal = totalRaBal.add(new BigDecimal(bal));

						if (Util.notEquals(Util.trim(jsonG.get(sJsonKey)), "")) {
							totalRaBalG = totalRaBalG.add(new BigDecimal(bal));
						}

						if (Util.notEquals(Util.trim(jsonM.get(sJsonKey)), "")) {
							totalRaBalM = totalRaBalM.add(new BigDecimal(bal));
						}

						break;
					}

					for (Map<String, Object> attrOTS : attrOTSBal) {
						bal = Util.parseDouble(Util.trim(attrOTS
								.get("TOTAL_ATTRIBUTE")));
						totalAttrOTSBal = totalAttrOTSBal.add(BigDecimal
								.valueOf(bal));

						if (Util.notEquals(Util.trim(jsonG.get(sJsonKey)), "")) {
							totalAttrOTSBalG = totalAttrOTSBalG.add(BigDecimal
									.valueOf(bal));
						}

						if (Util.notEquals(Util.trim(jsonM.get(sJsonKey)), "")) {
							totalAttrOTSBalM = totalAttrOTSBalM.add(BigDecimal
									.valueOf(bal));
						}

						break;
					}

					// 海外貢獻度(非存款) Miller added at 2012/07/27
					while (it8.hasNext()) {
						Map<?, ?> dataMap8 = (Map<?, ?>) it8.next();
						double seaBal = Util.parseDouble(Util.trim(dataMap8
								.get("TOTAL_ATTRIBUTE")));
						String curr = Util.trim(dataMap8.get("CURR"));
						if (seaBal != 0) {
							seaBal = branchRate.toTWDAmt(
									(Util.isEmpty(curr)) ? "TWD" : curr,
									LMSUtil.toBigDecimal(seaBal)).doubleValue();
						}
						totalAttrOVSBal = totalAttrOVSBal.add(BigDecimal
								.valueOf(seaBal));

						if (Util.notEquals(Util.trim(jsonG.get(sJsonKey)), "")) {
							totalAttrOVSBalG = totalAttrOVSBalG.add(BigDecimal
									.valueOf(seaBal));
						}

						if (Util.notEquals(Util.trim(jsonM.get(sJsonKey)), "")) {
							totalAttrOVSBalM = totalAttrOVSBalM.add(BigDecimal
									.valueOf(seaBal));
						}

					}

					// ************************************************************************
				}

				// 計算 集團風險性資產平均餘額報酬率

				// 主借款人及關係戶
				jsonData.put("Rate5_2", CapDate.formatDate(dQueryDateS, "yyyy")
						+ "/" + CapDate.formatDate(dQueryDateS, "MM"));

				jsonData.put("Rate5_3", CapDate.formatDate(dQueryDateE, "yyyy")
						+ "/" + CapDate.formatDate(dQueryDateE, "MM"));

				if (BigDecimal.ZERO.compareTo(totalRaBal) == 0) {
					jsonData.put("Rate5", "N.A.");
				} else {
					BigDecimal totalAttr = totalAttrOTSBal.add(totalAttrOVSBal);
					if (BigDecimal.ZERO.compareTo(totalAttr) == 0) {
						jsonData.put("Rate5", "0");
					} else {
						BigDecimal mainGrpAvgRate = totalAttr.multiply(
								BigDecimal.valueOf(100)).divide(
								totalRaBal.divide(BigDecimal.valueOf(12), 4,
										BigDecimal.ROUND_HALF_UP), 2,
								BigDecimal.ROUND_HALF_UP);
						jsonData.put("Rate5", mainGrpAvgRate.toString());
					}

				}

				// 主借款人--只有一筆
				jsonData.put("Rate4_2", CapDate.formatDate(dQueryDateS, "yyyy")
						+ "/" + CapDate.formatDate(dQueryDateS, "MM"));

				jsonData.put("Rate4_3", CapDate.formatDate(dQueryDateE, "yyyy")
						+ "/" + CapDate.formatDate(dQueryDateE, "MM"));

				if (BigDecimal.ZERO.compareTo(totalRaBalM) == 0) {
					jsonData.put("Rate4", "N.A.");
				} else {
					BigDecimal totalAttr = totalAttrOTSBalM
							.add(totalAttrOVSBalM);
					if (BigDecimal.ZERO.compareTo(totalAttr) == 0) {
						jsonData.put("Rate4", "0");
					} else {
						BigDecimal mainGrpAvgRate = totalAttr.multiply(
								BigDecimal.valueOf(100)).divide(
								totalRaBalM.divide(BigDecimal.valueOf(12), 4,
										BigDecimal.ROUND_HALF_UP), 2,
								BigDecimal.ROUND_HALF_UP);
						jsonData.put("Rate4", mainGrpAvgRate.toString());
					}

				}

				// 集團企業
				jsonData.put("Rate1_2", CapDate.formatDate(dQueryDateS, "yyyy")
						+ "/" + CapDate.formatDate(dQueryDateS, "MM"));

				jsonData.put("Rate1_3", CapDate.formatDate(dQueryDateE, "yyyy")
						+ "/" + CapDate.formatDate(dQueryDateE, "MM"));

				if (BigDecimal.ZERO.compareTo(totalRaBalG) == 0) {
					jsonData.put("Rate1", "N.A.");
				} else {
					BigDecimal totalAttr = totalAttrOTSBalG
							.add(totalAttrOVSBalG);
					if (BigDecimal.ZERO.compareTo(totalAttr) == 0) {
						jsonData.put("Rate1", "0");
					} else {
						BigDecimal mainGrpAvgRate = totalAttr.multiply(
								BigDecimal.valueOf(100)).divide(
								totalRaBalG.divide(BigDecimal.valueOf(12), 4,
										BigDecimal.ROUND_HALF_UP), 2,
								BigDecimal.ROUND_HALF_UP);
						jsonData.put("Rate1", mainGrpAvgRate.toString());
					}

				}

			}
			// }
		}
		return "";
	}
}
