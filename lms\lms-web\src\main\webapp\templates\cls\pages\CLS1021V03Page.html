<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
	      <div id="filterBox" style="display:none">
	      	<form id="filterForm" name="filterForm">
	      	<table>
		      <label><b><th:block th:text="#{C102M01A.message01}"><!-- 請輸入欲查詢紀錄--></th:block>：</b></label>
		      <tbody>
		        <tr>
		          <td>
		          	<label><input type="radio" name="queryData" value="1" checked="checked"/>
		            <th:block th:text="#{C102M01A.custId}"><!--借款人統編--></th:block></label>&nbsp;&nbsp;&nbsp;&nbsp;
		            <label><input type="radio" name="queryData" value="2"/>
		         	<th:block th:text="#{C102M01A.cntrNo}"><!--額度序號--></th:block></label>
					<label><input type="radio" name="queryData" value="3"/>
		         	<th:block th:text="#{C102M01A.approveTime}"><!--核准日期--></th:block></label>
				  </td>
		        </tr>
		        <tr id="queryDataTr1" class="select">
		        <td >
		        	<input id="custId" name="custId" type="text" size="14" maxlength="11"  class="upText required"/>
		            <span class="text-red">ex:A1234567890</span>
				</td>
		        </tr>
		        <tr id="queryDataTr2" class="select" style="display:none">
		          <td >
		          	  <input id="cntrNo" name="cntrNo" type="text" class="required" size="12" maxlength="12" />
		             <span class="text-red">ex:001234567890</span> 
				  </td>
		        </tr>
				<tr id="queryDataTr3" class="select" style="display:none">
		          <td >
		          	  <input id="approveTime" name="approveTime" type="text" class="date required" size="12" maxlength="12" />
				  </td>
		        </tr>
		      </tbody>
		    </table>
			</form>
		  </div>
	</th:block>
</body>
</html>
