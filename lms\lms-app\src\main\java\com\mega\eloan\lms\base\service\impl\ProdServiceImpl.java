/* 
 *ProdServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.dao.C160S01CDao;
import com.mega.eloan.lms.dao.C900M01ADao;
import com.mega.eloan.lms.dao.C900M01BDao;
import com.mega.eloan.lms.dao.C900M01CDao;
import com.mega.eloan.lms.dao.C900M01DDao;
import com.mega.eloan.lms.dao.C900S01ADao;
import com.mega.eloan.lms.dao.C900S01BDao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01CDao;
import com.mega.eloan.lms.dao.L140M01ODao;
import com.mega.eloan.lms.dao.L140M04ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.impl.AbstractEloandbJdbc;
import com.mega.eloan.lms.model.C160M01C;
import com.mega.eloan.lms.model.C160S01C;
import com.mega.eloan.lms.model.C900M01A;
import com.mega.eloan.lms.model.C900M01B;
import com.mega.eloan.lms.model.C900M01C;
import com.mega.eloan.lms.model.C900M01D;
import com.mega.eloan.lms.model.C900S01A;
import com.mega.eloan.lms.model.C900S01B;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M04A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 產品種類相關 ServiceImpl
 * </pre>
 * 
 * @since 2012/12/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/26,REX,new
 *          </ul>
 */
@Service("ProdService")
public class ProdServiceImpl extends AbstractEloandbJdbc implements ProdService {

	private static final Logger logger = LoggerFactory
			.getLogger(ProdServiceImpl.class);

	private HashMap<String, Object> container = new LinkedHashMap<String, Object>();

	private List<C900M01A> c900m01as = new ArrayList<C900M01A>();
	//J-113-0036 新增新產品資訊時，將產品種類下拉選單選項做細部分類
	private List<C900M01A> c900m01as_houseLoan = new ArrayList<C900M01A>();
	private List<C900M01A> c900m01as_creditLoan = new ArrayList<C900M01A>();
	private List<C900M01A> c900m01as_otherLoan = new ArrayList<C900M01A>();
	@SuppressWarnings("unused")
	private List<C900M01B> c900m01bs = new ArrayList<C900M01B>();
	private List<C900M01C> c900m01cs = new ArrayList<C900M01C>();
	private List<C900M01D> c900m01ds = new ArrayList<C900M01D>();
	private List<C900S01A> c900S01as = new ArrayList<C900S01A>();
	private List<C900S01B> c900S01bs = new ArrayList<C900S01B>();
	private HashMap<String, List<C900S01A>> c900s01aData = new HashMap<String, List<C900S01A>>();
	private HashMap<String, List<C900S01B>> c900s01bData = new HashMap<String, List<C900S01B>>();
	private List<C900S01B> 全產品共用項目 = new ArrayList<C900S01B>();
	private List<C900S01B> 自行輸入 = new ArrayList<C900S01B>();

	private String 產品下拉選單 = "ProdKind";
	//J-113-0036 新增新產品資訊時，將產品種類下拉選單選項做細部分類
	private String 產品下拉選單_房貸 = "ProdKind_1";
	private String 產品下拉選單_信貸 = "ProdKind_2";
	private String 產品下拉選單_其他 = "ProdKind_3";
	private String 科目對應名稱 = "SubCodeName";
	private String 產品對應名稱 = "ProdKindName";
	private String 查核事項 = "1";
	private String 檢附文件 = "2";

	@Resource
	C900M01ADao c900m01aDao;
	@Resource
	C900M01BDao c900m01bDao;
	@Resource
	C900M01CDao c900m01cDao;
	@Resource
	C900M01DDao c900m01dDao;
	@Resource
	C900S01ADao c900s01aDao;
	@Resource
	C900S01BDao c900s01bDao;
	@Resource
	C160S01CDao c160s01CDao;
	@Resource
	L120M01ADao l120m01aDao;
	@Resource
	L120M01CDao l120m01cDao;
	@Resource
	L140M01ODao l140m01oDao;
	@Resource
	L140M04ADao l140m04aDao;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	EloandbBASEService eloandbBASEService;

	private static String[] oldProdKind = new String[] { "01", "05", 
			// "08",
			"11", "29", "30", "37", "40", "41", "42", "43", "44", "45", "46",
			"47", "48", 
			// "49", 
			"50", "51", "52", "53", "54", "55" };
	
	/**
	 * 設定產品下拉選單
	 * 
	 * @return
	 */
	private JSONArray setProdKind() {
		// 同時要把會計科目帶下來
		JSONArray total = new JSONArray();
		String name = "";
		List<String> isHandle = new ArrayList<String>();
		// 將已經停用的產品放入，則不會產出在下拉選單中
		for (String key : oldProdKind) {
			isHandle.add(key);
		}
		for (C900M01A c900m01a : c900m01as) {
			String prodKind = c900m01a.getProdKind();
			JSONObject result = new JSONObject();
			if (isHandle.contains(prodKind)) {
				result.put("isCanCel", "Y");
			} else {
				isHandle.add(prodKind);
			}

			if (Util.isNotEmpty(c900m01a.getProdNm2())) {
				name = StrUtils.concat(c900m01a.getProdNm1(), "-",
						c900m01a.getProdNm2());
			} else {
				name = c900m01a.getProdNm1();
			}
			result.put("key", prodKind);
			result.put("name", name);
			JSONArray subjectDataArray = new JSONArray();
			for (C900M01B c900m01b : c900m01a.getC900M01B()) {
				String isCanCel = "N";
				if ("Y".equals(c900m01b.getIsCancel())) {
					isCanCel = "Y";
				}
				C900M01D c900m01d = c900m01b.getC900M01D();
				if (c900m01d != null) {
					String subJcode2 = c900m01d.getSubjCode2();
					if ("104".equals(subJcode2)) {
						isCanCel = "Y";
					}
					
					if (Util.equals("N", c900m01b.getIsCancel()) && CrsUtil.is_subj_in_104(c900m01d.getSubjCode())) {
						isCanCel = "N";//J-111-0284 授信科目 104 在產品種類 [02, 04, 07] 都有 => 依 c900m01b.isCancel 決定是否可承做
					}
					
					JSONObject subjectData = new JSONObject();
					subjectData.put("subjCode", c900m01d.getSubjCode());
					subjectData.put("subjNm", c900m01d.getSubjNm());
					subjectData.put("subjCode2", c900m01d.getSubjCode2());
					subjectData.put("rIntWay", c900m01b.getRIntWay());
					subjectData.put("isCanCel", isCanCel);
					subjectDataArray.add(subjectData);
				}

			}
			result.put("subjectData", subjectDataArray.toString());
			total.add(result);
		}
		return total;
	}
	
	/**
	 * J-113-0036 新增新產品資訊時，將產品種類下拉選單選項做細部分類
	 * 以類型設定產品下拉選單
	 * 1：房貸
	 * 2：信貸
	 * 3：其他
	 * @return
	 */
	private JSONArray setProdKindByLoanType(String loanType) {
		// 同時要把會計科目帶下來
		JSONArray total = new JSONArray();
		String name = "";
		List<String> isHandle = new ArrayList<String>();
		// 將已經停用的產品放入，則不會產出在下拉選單中
		for (String key : oldProdKind) {
			isHandle.add(key);
		}
		List<C900M01A> c900m01aList = new ArrayList<C900M01A>();
		
		if(Util.equals("1", loanType)){
			c900m01aList = c900m01as_houseLoan;
		}else if(Util.equals("2", loanType)){
			c900m01aList = c900m01as_creditLoan;
		}else if(Util.equals("3", loanType)){
			c900m01aList = c900m01as_otherLoan;
		}
		
		for (C900M01A c900m01a : c900m01aList) {
			String prodKind = c900m01a.getProdKind();
			JSONObject result = new JSONObject();
			if (isHandle.contains(prodKind)) {
				result.put("isCanCel", "Y");
			} else {
				isHandle.add(prodKind);
			}

			if (Util.isNotEmpty(c900m01a.getProdNm2())) {
				name = StrUtils.concat(c900m01a.getProdNm1(), "-",
						c900m01a.getProdNm2());
			} else {
				name = c900m01a.getProdNm1();
			}
			result.put("key", prodKind);
			result.put("name", name);
			JSONArray subjectDataArray = new JSONArray();
			for (C900M01B c900m01b : c900m01a.getC900M01B()) {
				String isCanCel = "N";
				if ("Y".equals(c900m01b.getIsCancel())) {
					isCanCel = "Y";
				}
				C900M01D c900m01d = c900m01b.getC900M01D();
				if (c900m01d != null) {
					String subJcode2 = c900m01d.getSubjCode2();
					if ("104".equals(subJcode2)) {
						isCanCel = "Y";
					}
					
					if (Util.equals("N", c900m01b.getIsCancel()) && CrsUtil.is_subj_in_104(c900m01d.getSubjCode())) {
						isCanCel = "N";//J-111-0284 授信科目 104 在產品種類 [02, 04, 07] 都有 => 依 c900m01b.isCancel 決定是否可承做
					}
					
					JSONObject subjectData = new JSONObject();
					subjectData.put("subjCode", c900m01d.getSubjCode());
					subjectData.put("subjNm", c900m01d.getSubjNm());
					subjectData.put("subjCode2", c900m01d.getSubjCode2());
					subjectData.put("rIntWay", c900m01b.getRIntWay());
					subjectData.put("isCanCel", isCanCel);
					subjectDataArray.add(subjectData);
				}

			}
			result.put("subjectData", subjectDataArray.toString());
			total.add(result);
		}
		return total;
	}
	
	@Override
	public JSONArray getProdKind() {
		this.reload(false);
		return (JSONArray) container.get(產品下拉選單);
	}
	
	/**
	 * J-113-0036 新增新產品資訊時，將產品種類下拉選單選項做細部分類
	 * 以貸款類型取得產品種類
	 */
	@Override
	public JSONArray getProdKindByLoanType(String loanType) {
		this.reload(false);
		if(Util.equals("1", loanType)){
			return (JSONArray) container.get(產品下拉選單_房貸);
		}else if(Util.equals("2", loanType)){
			return (JSONArray) container.get(產品下拉選單_信貸);
		}else if(Util.equals("3", loanType)){
			return (JSONArray) container.get(產品下拉選單_其他);
		}else{
			return new JSONArray();
		}
	}

	private String[] get_lmsprodkind(){
		String[] procArr = { 
				ProdService.ProdKindEnum.土地融資_個人戶_33.getCode()
				, ProdService.ProdKindEnum.建築融資_個人戶_34.getCode()};
		return procArr;
	}
	
	private String sub_personalString(String orgName){
		/*
		 33       土地融資-個人戶 
		 34       建築融資-個人戶  
		 */
		int idx = orgName.lastIndexOf("-個人戶");
		if(idx>0){
			return orgName.substring(0, idx);
		}
		return orgName;
	}
	
	@Override
	public JSONArray getProdKind(String busCode) {
		if(Util.isEmpty(Util.trim(busCode))|| LMSUtil.isBusCode_060000_130300(busCode)){
			return getProdKind();
		}
		
		JSONArray src = getProdKind();
		JSONArray r = new JSONArray(); 
		int cnt = src.size();
		String[] lmsprodkind = get_lmsprodkind();
		for(int i=0;i<cnt;i++){
			JSONObject obj = src.getJSONObject(i);
			
			String key = obj.optString("key");
			String name = obj.optString("name");
			if(CrsUtil.inCollection(key, lmsprodkind)){
				String decoName = sub_personalString(name);
				if(Util.notEquals(decoName, name)){
					obj.put("name", decoName); 
				}					
			}
			
			r.add(obj);
		}
		return r;
	}
	
	/**
	 * J-113-0036 新增新產品資訊時，將產品種類下拉選單選項做細部分類
	 * 以貸款類型取得產品種類
	 */
	@Override
	public JSONArray getProdKind(String busCode,String loanType) {
		if(Util.isEmpty(Util.trim(busCode))|| LMSUtil.isBusCode_060000_130300(busCode)){
			return getProdKindByLoanType(loanType);
		}
		
		JSONArray src = getProdKindByLoanType(loanType);
		JSONArray r = new JSONArray(); 
		int cnt = src.size();
		String[] lmsprodkind = get_lmsprodkind();
		for(int i=0;i<cnt;i++){
			JSONObject obj = src.getJSONObject(i);
			
			String key = obj.optString("key");
			String name = obj.optString("name");
			if(CrsUtil.inCollection(key, lmsprodkind)){
				String decoName = sub_personalString(name);
				if(Util.notEquals(decoName, name)){
					obj.put("name", decoName); 
				}					
			}
			
			r.add(obj);
		}
		return r;
	}
		
	@SuppressWarnings("unchecked")
	@Override
	public HashMap<String, String> getSubCode() {
		this.reload(false);
		return (HashMap<String, String>) container.get(科目對應名稱);
	}

	@SuppressWarnings("unchecked")
	@Override
	public HashMap<String, String> getProdKindName() {
		this.reload(false);
		return (HashMap<String, String>) container.get(產品對應名稱);
	}

	public HashMap<String, String> getProdKindName(String busCode){
		if(Util.isEmpty(Util.trim(busCode))|| LMSUtil.isBusCode_060000_130300(busCode)){
			return getProdKindName();
		}

		HashMap<String, String> r = new HashMap<String, String>();
		r.putAll(getProdKindName());
	
		for(String prodKind: get_lmsprodkind()){
			String prodName = Util.trim(r.get(prodKind));			
			String decoName = sub_personalString(prodName);
			if(Util.notEquals(decoName, prodName)){
				r.put(prodKind, decoName); 
			}
		}				
		return r;
	}
	
	@Override
	public synchronized void reload(boolean cleanAll) {
		if (cleanAll) {
			container = null;
		}
		if (container == null || container.isEmpty()) {
			logger.info("ProdServiceImpl reload ");
			全產品共用項目.clear();
			自行輸入.clear();
			container = new LinkedHashMap<String, Object>();
			this.queryDbData(cleanAll);
			container.put(產品下拉選單, this.setProdKind());
			container.put(科目對應名稱, this.setSubCode());
			container.put(產品對應名稱, this.setProdKindName());
			//J-113-0036 新增新產品資訊時，將產品種類下拉選單選項做細部分類
			container.put(產品下拉選單_房貸, this.setProdKindByLoanType("1"));
			container.put(產品下拉選單_信貸, this.setProdKindByLoanType("2"));
			container.put(產品下拉選單_其他, this.setProdKindByLoanType("3"));

		}

	}

	/**
	 * 設定科目對應名稱
	 * 
	 * @return
	 */
	private HashMap<String, String> setSubCode() {
		// 同時要把會計科目帶下來
		HashMap<String, String> total = new HashMap<String, String>();
		for (C900M01D c900m01d : c900m01ds) {
			total.put(c900m01d.getSubjCode(), c900m01d.getSubjNm());
		}
		return total;
	}

	/**
	 * 設定產品對應名稱
	 * 
	 * @return
	 */
	private HashMap<String, String> setProdKindName() {
		HashMap<String, String> total = new HashMap<String, String>();
		String name = "";
		for (C900M01A c900m01a : c900m01as) {
			String prodKind = c900m01a.getProdKind();
			if (Util.isNotEmpty(c900m01a.getProdNm2())) {
				name = StrUtils.concat(c900m01a.getProdNm1(), "-",
						c900m01a.getProdNm2());
			} else {
				name = c900m01a.getProdNm1();
			}
			total.put(prodKind, name);
		}
		return total;
	}

	@Override
	public List<L140M04A> getL140M04A(String mainId) {
		this.reload(false);
		List<Map<String, Object>> rows = eloandbBASEService
				.findL140S02ADistinctByKey(mainId);
		logger.info("[L120M01A] mainId==>" + mainId);
		Set<String> prodKinds = new HashSet<String>();
		for (Map<String, Object> dataRow : rows) {
			String key = StrUtils.concat(Util.trim(dataRow.get("PRODKIND")),
					Util.trim(dataRow.get("SUBJCODE")));
			prodKinds.add(key);
		}
		HashMap<String, HashSet<String>> temp = new HashMap<String, HashSet<String>>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		ArrayList<String> allcode = new ArrayList<String>();
		List<L140M04A> l140m04as = new ArrayList<L140M04A>();
		int seq = 0;
		for (String key : prodKinds) {
			List<C900S01A> c900s01as = c900s01aData.get(key);
			if (c900s01as == null) {
				continue;
			}

			for (C900S01A c900s01a : c900s01as) {
				if (c900s01a == null) {
					continue;
				}
				String itemCode = c900s01a.getCheckCode();
				
				if (!"".equals(itemCode)) {
					if (temp.containsKey(itemCode)) {
						HashSet<String> setTemp = temp.get(itemCode);
						setTemp.add(Util.getLeftStr(key, 2));
						temp.put(itemCode, setTemp);
					} else {
						HashSet<String> setTemp = new HashSet<String>();
						setTemp.add(Util.getLeftStr(key, 2));
						temp.put(itemCode, setTemp);
					}
					if (!allcode.contains(itemCode)) {
						L140M04A l140m04a = new L140M04A();
						try {
							DataParse.copy(c900s01a, l140m04a);
						} catch (CapException e) {
							logger.error(
									"[getL140M04A] DataParse.copy Exception", e);
						}
						l140m04a.setMainId(mainId);

						l140m04a.setOid(null);
						// l140m04a.setCheckseq(++seq);
						l140m04a.setCreateTime(CapDate.getCurrentTimestamp());
						l140m04a.setCreator(user.getUserId());
						l140m04a.setUpdater(user.getUserId());
						l140m04a.setUpdateTime(CapDate.getCurrentTimestamp());
						l140m04as.add(l140m04a);
						allcode.add(itemCode);
					}
				}

			}
		}

		//XXX 在此將 l140m04a 依 checkType 排序
		Collections.sort(l140m04as, new Comparator<L140M04A>() {

			@Override
			public int compare(L140M04A object1, L140M04A object2) {
				int r = Util.parseInt(object1.getCheckType()) - Util.parseInt(object2.getCheckType());
				if(r==0 && object1.getCheckseq()!=null && object2.getCheckseq()!=null){
					r = object1.getCheckseq() - object2.getCheckseq();
				}
				return r;
			}
		});

		for (L140M04A l140m04a : l140m04as) {
			l140m04a.setCheckseq(++seq);
			
			StringBuffer str = new StringBuffer();
			HashSet<String> hashSet = temp.get(l140m04a.getCheckCode());
			for (String key : hashSet) {
				str.append(str.length() > 0 ? "," : "");
				str.append(key);
			}
			l140m04a.setProdType(str.toString());
		}

		return l140m04as;
	}

	@Override
	public List<C160M01C> getC160M01C(String mainId) {
		this.reload(false);
		logger.info("[C160M01C] mainId==>" + mainId);
		Set<String> prodKinds = new HashSet<String>();
		List<C160S01C> c160s01cs = c160s01CDao.findByMainId(mainId);
		boolean has_67_or_70 = false;
		for (C160S01C c160s01c : c160s01cs) {
			String key = StrUtils.concat(c160s01c.getProdKind(),
					c160s01c.getSubjCode());
			prodKinds.add(key);
			//=======
			if(CrsUtil.is_67(c160s01c.getProdKind()) || CrsUtil.is_70(c160s01c.getProdKind())){
				has_67_or_70 = true;
			}
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 產品 + 授信科目 XX+XXXXXXXX
		List<C160M01C> c160m01cs = new ArrayList<C160M01C>();
		ArrayList<String> allcode = new ArrayList<String>();
		for (String key : prodKinds) {
			List<C900S01B> c900s01bs = c900s01bData.get(key);
			if (c900s01bs == null) {
				continue;
			}
			for (C900S01B c900s01b : c900s01bs) {
				if (c900s01b == null || c900s01bs.isEmpty()) {
					continue;
				}
				String itemCode = c900s01b.getItemCode();
				if (!allcode.contains(itemCode)) {
					C160M01C c160m01c = new C160M01C();
					try {
						DataParse.copy(c900s01b, c160m01c);
						c160m01c.setItemDscr(c900s01b.getItemContent());
					} catch (CapException e) {
						logger.error("[getC160M01C] DataParse.copy Exception",
								e);
					}
					c160m01c.setItemType(c900s01b.getItemType());
					c160m01c.setMainId(mainId);
					c160m01c.setOid(null);
					c160m01c.setCreateTime(CapDate.getCurrentTimestamp());
					c160m01c.setCreator(user.getUserId());
					c160m01c.setUpdater(user.getUserId());
					c160m01c.setUpdateTime(CapDate.getCurrentTimestamp());
					c160m01cs.add(c160m01c);
					allcode.add(itemCode);
				}
			}
		}

		for (C900S01B c900s01b : 全產品共用項目) {
			C160M01C c160m01c = new C160M01C();
			try {
				DataParse.copy(c900s01b, c160m01c);
				if(has_67_or_70 && Util.equals(c160m01c.getItemCode(), "5")){
					//曾提到，在產品 67 時，要出現 "他項權利證明書及設定契約書(以房養老業務需有流抵契約約定)"					 
//					c160m01c.setItemContent(c160m01c.getItemContent()+"(以房養老業務需有流抵契約約定)");
				}

				//=======================================================
				//由 c900s01b.itemContent 複製到 c160m01c.itemContent
				//
				c160m01c.setItemDscr(c160m01c.getItemContent());
			} catch (CapException e) {
				logger.error("[getC160M01C] DataParse.copy Exception", e);
			}
			c160m01c.setItemType(c900s01b.getItemType());
			c160m01c.setMainId(mainId);
			c160m01c.setOid(null);
			c160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			c160m01c.setCreator(user.getUserId());
			c160m01c.setUpdater(user.getUserId());
			c160m01c.setUpdateTime(CapDate.getCurrentTimestamp());
			c160m01cs.add(c160m01c);
		}

		int seq = 90001;
		// 自訂項目
		for (int i = 0; i < 6; i++) {
			C160M01C c160m01c = new C160M01C();
			c160m01c.setItemType(自行輸入項目);
			c160m01c.setMainId(mainId);
			c160m01c.setOid(null);
			c160m01c.setItemSeq(seq + i);
			c160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			c160m01c.setCreator(user.getUserId());
			c160m01c.setUpdater(user.getUserId());
			c160m01c.setUpdateTime(CapDate.getCurrentTimestamp());
			c160m01cs.add(c160m01c);
		}
		return c160m01cs;

	}

	private String 共用項目 = "1";
	// private String 依產品自訂 = "2";
	private String 自行輸入項目 = "3";
	private String 已刪除項目 = "D";

	/**
	 * 查詢DB資料
	 */
	private void queryDbData(boolean cleanAll) {

		c900m01as = c900m01aDao.getAll(true);
		c900m01bs = c900m01bDao.getAll();
		c900m01cs = c900m01cDao.getAll();
		c900m01ds = c900m01dDao.getAll();
		c900S01as = c900s01aDao.getAll();
		c900S01bs = c900s01bDao.getAll();
		//J-113-0036 新增新產品資訊時，將產品種類下拉選單選項做細部分類
		c900m01as_houseLoan = c900m01aDao.getAllByLoanType("1");
		c900m01as_creditLoan = c900m01aDao.getAllByLoanType("2");
		c900m01as_otherLoan = c900m01aDao.getAllByLoanType("3");
		
		if(cleanAll){
			c900s01aData.clear();
		}
		c900s01bData.clear();
		LinkedHashMap<String, C900S01A> c900s01aMap = new LinkedHashMap<String, C900S01A>();
		for (C900S01A c900s01a : c900S01as) {
			c900s01aMap.put(c900s01a.getCheckCode(), c900s01a);
		}

		LinkedHashMap<String, C900S01B> c900s01bMap = new LinkedHashMap<String, C900S01B>();
		for (C900S01B c900s01b : c900S01bs) {
			String itemType = Util.trim(c900s01b.getItemType());
			if (共用項目.equals(itemType)) {
				全產品共用項目.add(c900s01b);
			} else if (自行輸入項目.equals(itemType)) {
				自行輸入.add(c900s01b);
			} else if (已刪除項目.equals(itemType)){
				// J-111-0267  修改國內企金、消金動用審核表檢核項目內容文字
				// 不做任何事，讓itemType為D的資料不落入任何一List、Map
			} else {
				c900s01bMap.put(c900s01b.getItemCode(), c900s01b);
			}
		}
		for (C900M01C c900m01c : c900m01cs) {
			String type = c900m01c.getType();
			String souseCode = c900m01c.getSouseCode();
			String key = StrUtils.concat(c900m01c.getProdKind(),
					c900m01c.getSubjCode());
			if (查核事項.equals(type)) {
				if (!c900s01aData.containsKey(key)) {
					c900s01aData.put(key, new ArrayList<C900S01A>());
				}
				c900s01aData.get(key).add(c900s01aMap.get(souseCode));
			} else if (檢附文件.equals(type)) {
				if (!c900s01bData.containsKey(key)) {
					c900s01bData.put(key, new ArrayList<C900S01B>());
				}
				c900s01bData.get(key).add(c900s01bMap.get(souseCode));
			}
		}

	}

	@Override
	public boolean isGuarantee(String subjectCode) {
		this.reload(false);
		for (C900M01D c900m01d : c900m01ds) {
			if (c900m01d.getSubjCode().equals(subjectCode)) {
				// 如果三碼的授信科目第一碼為單數，為無擔科目
				if (Integer.valueOf(c900m01d.getSubjCode2().substring(0, 1)) % 2 == 1) {
					return false;
				} else {
					return true;
				}
			}
		}
		return false;
	}

	@Override
	public String getSubject3to8(String subjectCode) {
		String result = "";
		this.reload(false);
		for (C900M01D c900m01d : c900m01ds) {
			if (c900m01d.getSubjCode2().equals(subjectCode)) {
				result = c900m01d.getSubjCode();
				break;
			}
		}
		return result;
	}

	@Override
	public String getSubject8to3(String subjectCode2) {
		String result = "";
		this.reload(false);
		for (C900M01D c900m01d : c900m01ds) {
			if (c900m01d.getSubjCode().equals(subjectCode2)) {
				result = c900m01d.getSubjCode2();
				break;
			}
		}
		return result;
	}

	@Override
	public boolean is_policy_house_loan(ProdKindEnum prodKindEnumVal) {
		boolean result = false;
		switch (prodKindEnumVal) {
		case 優惠購屋專案_8000億_28:
		case 優惠購屋專案_93年3000億_35:
		case 內政部整合住宅方案_弱勢戶_第一類_38:
		case 內政部整合住宅方案_一般戶_第二類_39:
		case 優惠購屋專案_97年_2000億_56:
		case 青年安心成家貸款_57:
		case 青年安心成家貸款_第二案_59:
		case 金門青年安心成家_66:
			result = true;
			break;
		}
		return result;
	}

	@Override
	public String getMatchModelKind(L140S02A l140s02a) {
		return getMatchModelKind(l140s02a.getMainId(), l140s02a.getProdKind(),
				l140s02a.getSubjCode());
	}

	@Override
	public String getMatchModelKind(String mainId, String prodKind,
			String subjCode) {
		L120M01C l120m01c = l120m01cDao.findoneByRefMainId(mainId);
		L120M01A meta = l120m01aDao.findByMainId(l120m01c == null ? ""
				: l120m01c.getMainId());
		if (meta != null && LMSUtil.isParentCase(meta)) {
			return "0";// 團貸案-固定「免辦」
		} else {
			return getClsCreditType(prodKind, getSubject8to3(subjCode),
					checkhasCMS0102(mainId));
		}
	}

	/**
	 * 帶入產品種類/會計科目後回傳個金信評種類
	 */
	private String getClsCreditType(String prodKind, String subJcode,
			String collKind) {

		String returnValue = "";
		if (Util.isNotEmpty(prodKind)) {
			HashSet<String> prd_03_set = new HashSet<String>();
			prd_03_set.add("403");
			prd_03_set.add("603");

			
			ProdKindEnum valEnum = ProdService.ProdKindEnum.getEnum(prodKind);
			//企金戶的額度,其 prodKind(lnf030_loan_class) 可能是 ['A1','','34']
			if(valEnum==null){
				return "0";
			}
			
			switch (valEnum) {
			case 信用卡卡友信用貸款_08:
			case 歡喜信貸_71:
				returnValue = "3";

				break;
			case 協助在學學生購置電腦設備_01:
			case 行家理財貸款_短期_02:
			case 綜合理財_04:
			case 汽車貸款GECT及AIG_05:
			case 一般消貸含團體消貸_07:
			case 停車位貸款_09:
			case 合作車貸GECT及AIG_29:
			case 土地融資_個人戶_33:
			case 建築融資_個人戶_34:
			case 個人通信貸款_37:
			case 法拍屋信用貸款修繕住宅貸款_48:
			case 次順位房貸_49:
			case 自營業主貸款住宅者優惠貸款_50:
			case 消費者個人擔保貸款_土地住宅者優惠貸款_51:
			case 消費者個人擔保貸款_各類債券及有價證券置新屋_無補貼息_52:
			case 軍人信用貸款專案房貸_53:
			case 短期個人信貸專案_54:
			case 一０四年兆豐金控集團現職員工增資認股消貸專案_62:
			case 勞工紓困貸款_69:
				returnValue = "2";

				break;
			case 行家理財貸款_中長期_03:
				if (prd_03_set.contains(subJcode) && "Y".equals(collKind)) {
					returnValue = "1";
				} else {
					returnValue = "2";
				}
				break;
			case 留學貸款_06:
			case 外勞貸款_32:
			case 政策性留學生貸款_36:
			case 青年創業貸款_58:
			case 青年築夢創業啟動金貸款_60:
			case 青年創業及啟動金貸款_61:
			case 以房養老_67:
			case 以房養老累積型_70:
			case 企金科目:
				returnValue = "0";
				break;
			case 一般房貸_購置住宅_10:
			case 一般房貸_購屋儲蓄_11:
			case 一般房貸_房屋修繕_12:
			case 優惠購屋專案_3200億_13:
			case 青年優惠房貸暨信用保證專案_3200億_14:
			case 青年購屋低利貸款_15:
			case 輔助人民自購住宅貸款_16:
			case 輔助勞工建購住宅貸款_一般建購_17:
			case 輔助勞工建購住宅貸款_一般修繕_18:
			case 輔助勞工建購住宅貸款_921建購_19:
			case 購置新屋_1500億_20:
			case 無自用住宅購屋_1500億_21:
			case 新購屋_921貸款專案_22:
			case 重建_921貸款專案_23:
			case 貸款專案_921_24:
			case 修繕_優惠購屋專案_6000億_25:
			case 輔助原助民建購_修繕住宅貸款_26:
			case 優惠購屋專案_8000億_28:
			case 房貸專案_A案_100億_30:
			case 房貸專案_B案_100億_31:
			case 優惠購屋專案_93年3000億_35:
			case 內政部整合住宅方案_弱勢戶_第一類_38:
			case 內政部整合住宅方案_一般戶_第二類_39:
			case 協助震災災區民眾建購或修繕住宅貸款_40:
			case 郵儲金辦理無自用住宅者優惠貸款_41:
			case 郵儲金辦理無自用住宅者優惠貸款_42:
			case 無自用住宅者購置新屋_無補貼息_1500億_43:
			case 一年固定利率優惠房貸_44:
			case 兆豐一生康健安家專案_45:
			case 五年固定利率優惠房貸專案_46:
			case 永慶房屋履約保證優惠房貸專案_47:
			case 兆豐傳家保_優惠房貸加壽險專案_55:
			case 優惠購屋專案_97年_2000億_56:
			case 青年安心成家貸款_57:
			case 青年安心成家貸款_第二案_59:
			case 天然及重大災害受災戶購屋_63:
			case 天然及重大災害受災戶重建_64:
			case 天然及重大災害受災戶修繕_65:
			case 金門青年安心成家_66:		
			case 地上權住宅貸款_72:	
				returnValue = "1";
				break;
			/*
			 	產品68：行家理財-中期循環
			 	在剛推出上線時，採用「房貸申請信用評等」
			 	在 2019-08 消金處 提案 （J-108-0229 ）調整為【擔保品為房屋者適用「房貸」模型，若案件之擔保品僅為素地，應適用「非房貸」模型】
			 */
			case 行家理財中期循環_68:
				if ("Y".equals(collKind)) {
					returnValue = "1";
				} else {
					returnValue = "2";
				}
				break;
			case 購置住宅_921原貸展延戶_27:
				if (Util.equals(subJcode, "503")) {
					returnValue = "2";
				} else {
					returnValue = "1";
				}
				break;
			}
		}
		return returnValue;
	}

	/**
	 * 確認擔保品內容是否有不動產-建物
	 * 
	 * @param l140m01a_mainId
	 *            額度明細表 mainId
	 * @return Y 有|N 無
	 */
	private String checkhasCMS0102(String l140m01a_mainId) {
		List<L140M01O> l140m01os = l140m01oDao.findByMainId(l140m01a_mainId);

		if(checkhasCMS01_build(l140m01os)){
			return UtilConstants.DEFAULT.是;
		}
		return UtilConstants.DEFAULT.否;
	}

	@Override
	public boolean checkhasCMS01_build(List<L140M01O> l140m01os){
		for (L140M01O l140m01o : l140m01os) {
			if (UtilConstants.CollTyp1.不動產.equals(l140m01o.getCollTyp1())
					&& !Util.isEmpty(l140m01o.getBuild())) {			
				return true;
			}
		}
		return false;
	}

	@Override
	public String[] get_33or34(){
		return new String[] { 
				  ProdKindEnum.土地融資_個人戶_33.getCode()
				, ProdKindEnum.建築融資_個人戶_34.getCode() 
			};
	}

	@Override
	public String get_brmp_target_prodKind(String isQdata2, String isQdata3, boolean isBankMan_borrower){
		if(Util.equals("1", isQdata2) || Util.equals("1", isQdata3) ){
			if(isBankMan_borrower){
				return ProdKindEnum.一般消貸含團體消貸_07.getCode();
			}else{
				//
			}
		}else{
			//
		}
		return ProdKindEnum.歡喜信貸_71.getCode();
	}
	
//	@Override
//	public boolean is_63_64_65(String prodKind){
//		if(Util.equals(ProdKindEnum.天然及重大災害受災戶購屋_63.getCode(), prodKind)
//				|| Util.equals(ProdKindEnum.天然及重大災害受災戶重建_64.getCode(), prodKind)
//				|| Util.equals(ProdKindEnum.天然及重大災害受災戶修繕_65.getCode(), prodKind)){
//			return true;
//		}
//		return false;
//	}
}
