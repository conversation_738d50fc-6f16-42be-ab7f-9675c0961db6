package com.mega.eloan.lms.base.common;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JSONUtil {

	private static final Logger LOGGER = LoggerFactory.getLogger(JSONUtil.class);

	@SuppressWarnings("unchecked")
	public static Map<String, String> parseJsonToStringMap(JSONObject data) {
		Map<String, String> map = new HashMap<String, String>();
		if (data == null) {
			LOGGER.warn("parseJsonToStringMap() Json data is null!");
		} else {
			Iterator<String> keys = data.keys();
			while (keys.hasNext()) {
				String key = keys.next();
				map.put(key, data.getString(key));
			}
		}

		return map;
	}

}
