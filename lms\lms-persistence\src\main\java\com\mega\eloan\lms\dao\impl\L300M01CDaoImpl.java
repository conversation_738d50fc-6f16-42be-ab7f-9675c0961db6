/* 
 * L300M01CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;

import com.mega.eloan.lms.dao.L300M01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L300M01C;

/** 覆審排名表檔 **/
@Repository
public class L300M01CDaoImpl extends LMSJpaDao<L300M01C, String>
	implements L300M01CDao {

	@Override
	public L300M01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L300M01C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L300M01C> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L300M01C> findByIndex01(String mainId, String ownBrId, Date creatDate){
		ISearch search = createSearchTemplete();
		List<L300M01C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if (creatDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "creatDate", creatDate);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L300M01C> findByIndex02(String ownBrId, Date creatDate, Date bgnDate, Date endDate){
		ISearch search = createSearchTemplete();
		List<L300M01C> list = null;
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if (creatDate != null)
			search.addSearchModeParameters(SearchMode.BETWEEN, "creatDate", new Object[]{bgnDate, endDate});
		if (bgnDate != null)
			search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "bgnDate", 
					(bgnDate==null ? CapDate.ZERO_DATE : TWNDate.toAD(bgnDate)));
		if (endDate != null)
			search.addSearchModeParameters(SearchMode.LESS_EQUALS, "endDate", 
					(endDate==null ? CapDate.ZERO_DATE : TWNDate.toAD(endDate)));
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}