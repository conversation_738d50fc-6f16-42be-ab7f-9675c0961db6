/* 
 * L140M01EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01E;


/** 額度聯行攤貸比例檔 **/
public interface L140M01EDao extends IGenericDao<L140M01E> {

	L140M01E findByOid(String oid);

	List<L140M01E> findByOids(String[] oids);

	List<L140M01E> findByMainId(String mainId);

	L140M01E findByUniqueKey(String mainId, String shareBrId);

	List<L140M01E> findByMainIdAndFlag(String mainId, String[] Flag);
	
	List<L140M01E> findByCntrNo(String CntrNo);
	
	List<L140M01E> findByCustIdDupId(String custId,String DupNo);
}