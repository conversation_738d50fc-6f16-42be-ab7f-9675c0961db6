package com.mega.eloan.lms.fms.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.mfaloan.bean.ELF506;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140MM3A;
import com.mega.eloan.lms.model.L140MM3B;
import com.mega.eloan.lms.model.L140MM3C;

import tw.com.iisi.cap.exception.CapException;

/**
 * <pre>
 * 企個金相關註記維護作業
 * </pre>
 * 
 * @since 2018
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface LMS7500Service extends AbstractService {

	public L140MM3A findL140mm3aByUniqueKey(String mainId);

	public Map<String, String> getElf515Data(L140MM3A l140mm3a)
			throws CapException;

	String checkCaseIs72_2(L140MM3A meta, boolean isPrevious);

	/**
	 * 刪除本案72-2註記資料
	 * 
	 * @param mainId
	 */
	void deleteCurrentL140mm3cs(String mainId);

	/**
	 * 刪除72-2註記資料和附件
	 * 
	 * @param oid
	 */
	void deleteL140mm3cAndFile(String oid);

	/**
	 * 本案72-2註記資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<L140MM3C> findCurrentL140mm3cs(String mainId);

	/**
	 * 前案72-2註記資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<L140MM3C> findLastL140mm3cs(String mainId);

	/**
	 * 儲存72-2註記資料
	 * 
	 * @param list
	 */
	void saveL140mm3cList(List<L140MM3C> list);

	List<L140MM3C> findL140mm3csByMainId(String mainId);

	public Map<String, String> getData(L140MM3A l140mm3a) throws CapException;

	/**
	 * 刪除動審表主檔資料 根據所選的oid
	 * 
	 * @param oids
	 *            文件編號陣列
	 * @return boolean
	 */
	boolean deleteL140mm3as(String[] oids);

	/**
	 * 其它到結案所用的flow
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            資料表
	 * @param setResult
	 *            boolean
	 * @param resultType
	 *            boolean
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, L140MM3A model, boolean setResult,
			boolean resultType, boolean upMis) throws Throwable;

	public void deleteL140mm3bs(List<L140MM3B> l140mm3bs, boolean isAll);

	/**
	 * 儲存案件簽章欄檔
	 */
	public void saveL140mm3bList(List<L140MM3B> list);

	/**
	 * 查詢案件簽章欄檔
	 * 
	 * @param mainId
	 *            案件編號
	 * @param staffNo
	 *            員編
	 * @param staffjob
	 *            人員職稱
	 */
	public L140MM3B findL140mm3b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob);

	/**
	 * 取得都更危老母戶預約資料
	 * 
	 * @param mCntrNo
	 * @return
	 */
	L140MM3C getBuildInfoByMcntrNo(String mCntrNo);

	/**
	 * J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
	 * 
	 * 暴險註記
	 * 
	 * @param l140mm3a
	 * @return
	 * @throws CapException
	 */
	Map<String, String> getEllnseekData(L140MM3A l140mm3a, boolean needSave)
			throws CapException;

	/**
	 * J-111-0633_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
	 * 
	 * ADC號碼補建或變更
	 * 
	 * @param l140mm3a
	 * @return
	 * @throws CapException
	 */
	Map<String, String> getAdcInfo(L140MM3A l140mm3a, boolean needSave)
			throws CapException;

	/**
	 * J-109-0239_05097_B1001 Web
	 * e-Loane-Loan授信管理系統案件簽報書之額度明細表新增「特殊融資或不動產ADC融資暴險註記」
	 * 
	 * 判斷是否要顯示本案是否屬特殊融資暴險
	 * 
	 * @param l120m01a
	 * @param l140m01a
	 * @return
	 * @throws CapException
	 */
	public boolean needShowIsSpecialFinRisk(L140MM3A l140mm3a)
			throws CapException;

	public L140MM3C findLastestL140mm3cByMainIdEstateType(String mainId);

	public ELF506 findELF506ByCntrNo(String cntrNo);

	public L120M01A findL120m01aByMainId(String mainId);

	public boolean isShowQ2Q7AttachedItem(String cntrNo);

	public String checkFinancingNotesAgreed(L140MM3A l140mm3a) throws CapException;

	public boolean processIsDerivatives(String l140m01a_mainId);
}
