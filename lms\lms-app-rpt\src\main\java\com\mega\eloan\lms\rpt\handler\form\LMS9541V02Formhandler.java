/* 
 * LMS9541v02Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.form;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.L810M01A;
import com.mega.eloan.lms.rpt.pages.LMS9541V02Page;
import com.mega.eloan.lms.rpt.service.LMS9541V02Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 優惠房貸報表 - 統計表
 * </pre>
 * 
 * @since 2012/12/06
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/06,Vector,new
 *          </ul>
 */

@Scope("request")
@Controller("lms9541v02formhandler")
public class LMS9541V02Formhandler extends AbstractFormHandler {

	@Resource
	LMS9541V02Service service;

	@Resource
	UserInfoService userinfoservice;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	EmailClient email;

	/**
	 * 建立 優惠貸款相關控制表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("static-access")
	public IResult add(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int type = params.getInt("type"), useType = params.getInt("mainType");
		L810M01A data = new L810M01A();
		data.setBrno(user.getUnitNo());
		data.setEndDate(Util.parseDate(params.getString("endDate")));
		data.setRptType(String.valueOf(type));
		data.setRptName(service.RPTNAMES[type]);
		data.setUseType(String.valueOf(useType));
		if (service.isRepeat(data)) {
			result.set("exist", true);
		} else {
			service.save(data);
		}

		return result;
	}

	/**
	 * 代入總表資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult queryTot(PageParameters params)
			throws CapException {
		String kindNo = params.getString("kindNo");
		JSONObject jsonData = new JSONObject();

		String totapp = service.findElghtappByKindno(kindNo);
		jsonData.put("totapp", NumConverter.addComma(totapp));

		Map<String, Object> totap = service.findTot(kindNo, "ACCEPT");

		BigDecimal base = Util.parseBigDecimal("10000");// 萬元

		BigDecimal allNum = Util.parseBigDecimal(totap.get("COUNT"));
		BigDecimal allAmt = Util.parseBigDecimal(totap.get("LOAN"))
				.divide(base).setScale(0, RoundingMode.HALF_UP);
		jsonData.put("allNum", NumConverter.addComma(allNum));
		jsonData.put("allAmt", NumConverter.addComma(allAmt));

		Map<String, Object> totac = service.findTot(kindNo, "ALLOCATE");
		BigDecimal acNum = Util.parseBigDecimal(totac.get("COUNT"));
		BigDecimal acAmt = Util.parseBigDecimal(totac.get("LOAN")).divide(base)
				.setScale(0, RoundingMode.HALF_UP);
		jsonData.put("acNum", NumConverter.addComma(acNum));
		jsonData.put("acAmt", NumConverter.addComma(acAmt));

		BigDecimal unAcNum = Util.parseBigDecimal(allNum).subtract(
				Util.parseBigDecimal(acNum));
		BigDecimal unAcAmt = Util.parseBigDecimal(allAmt).subtract(
				Util.parseBigDecimal(acAmt));
		jsonData.put("unacNum", NumConverter.addComma(unAcNum));
		jsonData.put("unacAmt", NumConverter.addComma(unAcAmt));

		Map<String, Object> prepare = eloandbBaseService
				.findTotPreparation(kindNo);
		String prepareNum = Util.trim(prepare.get("num"));
		String prepareAmt = (prepare.get("amt") == null) ? "0" : Util
				.trim(prepare.get("amt"));
		jsonData.put("prepareNum", NumConverter.addComma(prepareNum));
		jsonData.put("prepareAmt", NumConverter.addComma(prepareAmt));

		jsonData.put("totNum", allNum.add(Util.parseBigDecimal(prepareNum)));
		jsonData.put("totAmt", allAmt.add(Util.parseBigDecimal(prepareAmt)));

		return new CapAjaxFormResult(jsonData);
	}

	/**
	 * 寄信
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult sendMail(PageParameters params)
			throws CapException {
		String totalForm = params.getString("totalForm");
		JSONObject json = new JSONObject();
		if (Util.isNotEmpty(totalForm)) {
			json = DataParse.toJSON(totalForm);
			L810M01A record = service.findModelByOid(L810M01A.class,
					params.getString("oid"));
			if (Util.isEmpty(record.getSended())) {
				List<CodeType> mailList = codetypeService
						.findByCodeTypeList("lms9541v02_mailSites");
				String[] site = new String[mailList.size()];
				for (int i = 0; i < mailList.size(); i++) {
					CodeType sendData = mailList.get(i);
					site[i] = sendData.getCodeDesc2() + " "
							+ sendData.getCodeDesc();
				}

				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS9541V02Page.class);
				StringBuffer strBuf = new StringBuffer();

				// XXX承做數
				strBuf.append(pop.getProperty("title_b1") + record.getRptName()
						+ pop.getProperty("title_b2"));

				// 截至_______為止
				strBuf.append(pop.getProperty("date_b1")
						+ Util.getDate(record.getEndDate())
						+ pop.getProperty("date_b2"));

				// 單位:新台幣萬元
				strBuf.append(pop.getProperty("unit_b1"));

				// 開始畫table
				// 筆數 金額
				strBuf.append(pop.getProperty("table_r1_b1"));

				// a-Loan已撥款
				strBuf.append(pop.getProperty("table_r2_b1")
						+ json.getString("acNum")
						+ pop.getProperty("table_r_mid")
						+ json.getString("acAmt")
						+ pop.getProperty("table_r_end"));

				// e-Loan已核准未撥款
				strBuf.append(pop.getProperty("table_r3_b1")
						+ json.getString("unacNum")
						+ pop.getProperty("table_r_mid")
						+ json.getString("unacAmt")
						+ pop.getProperty("table_r_end"));

				// e-Loan簽核中
				strBuf.append(pop.getProperty("table_r4_b1")
						+ json.getString("prepareNum")
						+ pop.getProperty("table_r_mid")
						+ json.getString("prepareAmt")
						+ pop.getProperty("table_r_end"));

				// 合計
				strBuf.append(pop.getProperty("table_r5_b1")
						+ NumConverter.addComma(json.getString("totNum"))
						+ pop.getProperty("table_r_mid")
						+ NumConverter.addComma(json.getString("totAmt"))
						+ pop.getProperty("table_r_end")
						+ pop.getProperty("table_end"));

				email.send(site,
						record.getRptName() + pop.getProperty("subject"),
						strBuf.toString());
				// 更改資料庫報送狀態
				record.setSended("Y");
				service.save(record);
			} else {
				json.put("errorMsg", "isSended");
			}
		} else {
			json.put("errorMsg", "dataIncurrect");
		}

		return new CapAjaxFormResult(json);
	}
}
