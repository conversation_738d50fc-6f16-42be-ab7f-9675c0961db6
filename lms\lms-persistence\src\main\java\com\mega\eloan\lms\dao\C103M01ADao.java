/* 
 * C103M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C103M01A;

/** 訪談紀錄表資料檔 **/
public interface C103M01ADao extends IGenericDao<C103M01A> {

	C103M01A findByOid(String oid);

	List<C103M01A> findByMainId(String mainId);

	C103M01A findByUniqueKey(String mainId);

	List<C103M01A> findByIndex01(String mainId);

	List<C103M01A> findRptCls180r58(String ownBrId, Date rptStartDate,
			Date rptEndDate);
}