/* 
 * CLS1130Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service;

import java.util.List;

import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.C120M01A;

/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          </ul>
 */
public interface CLS1130Service extends AbstractService {

	/**
	 * save
	 * 
	 * @param list
	 */
	void save(List<GenericBean> list);

	/**
	 * delete
	 * 
	 * @param list
	 */
	void delete(List<GenericBean> list);

	/**
	 * 依oid取得model
	 * 
	 * @param <T>
	 * @param clazz
	 * @param oid
	 * @param create
	 * @return
	 */
	<T extends GenericBean> T findModelByOid(Class<?> clazz, String oid,
			boolean create);

	/**
	 * 依key取得model
	 * 
	 * @param <T>
	 * @param clazz
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	<T extends GenericBean> T findModelByKey(Class<?> clazz, String mainId,
			String custId, String dupNo);

	/**
	 * 依key取得model
	 * 
	 * @param clazz
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	<T extends GenericBean> T findModelByKey(Class<?> clazz, String mainId,
			String custId, String dupNo, boolean create);

	/**
	 * 依關聯鍵值取得LIST
	 * 
	 * @param clazz
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<? extends GenericBean> findListByRelationKey(Class<?> clazz,
			String mainId, String custId, String dupNo);

	/**
	 * 複製
	 * 
	 * @param targetMainId
	 * @param originMainIds
	 */
	void copy(String targetMainId, String[] originMainIds);

	void deleteByJPQL(GenericBean... entity);

	void deleteByJPQL(List<GenericBean> list);
	
	/**
	 * 儲存C120M01A/因借款人資料表(C120M01A)的姓名異動而連動修改簽報書及相關額度明細表
	 * @param c120m01a
	 */
	void saveC120M01A(C120M01A c120m01a);
}
