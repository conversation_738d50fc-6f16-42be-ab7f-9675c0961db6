package com.mega.eloan.lms.eloandb.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.eloandb.service.Dw_elf490ovsService;

@Service
public class Dw_elf490ovsServiceImpl extends AbstractEloandbJdbc implements 
		Dw_elf490ovsService {

	@Override
	public List<?> findELF490ovsSelNewRule(String dataYm,String branch){
		return this.getJdbc().queryForList("DW_ELF490OVS.selNewRule", new Object[] { dataYm,branch });
	}
	
	@Override
	public List<?> findELF490ovsSelOldRule(String dataYm,String branch){
		return this.getJdbc().queryForList("DW_ELF490OVS.selOldRule", new Object[] { dataYm,branch });
	}
}
