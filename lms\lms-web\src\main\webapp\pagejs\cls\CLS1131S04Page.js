var C101S01OAction = {
	handler : 'cls1131formhandler',
	data : {},
	ready : false,
	/**
	 * 初始化
	 */
	init : function() {
		var $div = $('#C101S01ODiv');
		$div.find('.dataTr').remove();
		$div.find('#queryDate').html('');
		$div.find('#relationSource').html('');
	},
	/**
	 * 建置
	 */
	build : function() {
		return true;
	},
	/**
	 * 開啟
	 */
	open : function(data) {
		if (!C101S01OAction.ready)
			C101S01OAction.ready = C101S01OAction.build();
		// 初始化
		C101S01OAction.init();
		// set data
		C101S01OAction.data = $.extend(data || {}, {
			noOpenDoc : true
		});
		// load data
		C101S01OAction.load();
	},
	openThinkBox : function() {
		$('#C101S01OThickBox').thickbox({
			title : '關聯戶貸款明細',
			width : 900,
			height : 450,
			modal : true,
			align : 'center',
			valign : 'bottom',
			buttons : {
				'close' : function() {
					$.thickbox.close();
				}
			}
		});
	},
	/**
	 * 讀取資料
	 */
	load : function() {
		if (!$.isEmptyObject(C101S01OAction.data)) {
			$.ajax({
				handler : C101S01OAction.handler,
				action : 'loadRelation',
				data : C101S01OAction.data,
				}).done(function(response) {
					var $div = $('#C101S01ODiv');

					if (response.queryDate){
						$div.find('#queryDate').html(DOMPurify.sanitize(response.queryDate));
					}
					//if (response.detials && response.detials.length >= 3) {
					if (response.detials) {
						$div.find('#C101S01OTableTr').after(C101S01OAction.parseTr(response.detials));
						$div.find('#relationSource').html(C101S01OAction.parseRelationSource(response.detials));
						C101S01OAction.openThinkBox();
					}else{
						//MegaApi.showPopMessage(i18n.def["confirmTitle"], "查無關聯戶貸款明細資料或不足3筆資料");
						MegaApi.showPopMessage(i18n.def["confirmTitle"], "查無關聯戶貸款明細資料。");
					}
			});
		}
	},
	/**
	 * 解析資料
	 */
	parseTr : function(args){
		var result = '';
		var list = typeof args === 'string' ? JSON.parse(args) : args;

		if (!Array.isArray(list) || list.length === 0){
			result += '<tr class="dataTr blueColor" >';
			result += ' <td colspan="7" class="blueColor" align="center" >無</td>';
			result += '</tr>';
		}else{
			for (var i in list){
				var json = list[i];
				result += '<tr class="dataTr blueColor" >';
				result += ' <td class="blueColor" align="center">'+DOMPurify.sanitize(json.cntrNo)+'</td>';
				result += ' <td class="blueColor" align="center">'+DOMPurify.sanitize(json.loanNo)+'</td>';
				result += ' <td class="blueColor" align="center">'+DOMPurify.sanitize(json.cancelFlag)+'</td>';
				result += ' <td class="blueColor" >'+DOMPurify.sanitize(json.custId1)+' '+DOMPurify.sanitize(json.dupNo1)+' '+DOMPurify.sanitize(json.custNm1)+'</td>';
				result += ' <td class="blueColor" >'+DOMPurify.sanitize(json.custId2)+' '+DOMPurify.sanitize(json.dupNo2)+' '+DOMPurify.sanitize(json.custNm2)+'</td>';
				result += ' <td class="blueColor" align="center">'+DOMPurify.sanitize(json.lngeFlag)+'</td>';
				result += ' <td class="blueColor" align="center">'+DOMPurify.sanitize(json.lngere)+'</td>';
				result += '</tr>';
			}
		}
		return result;
	},
	/**
	 * 解析資料來源
	 */
	parseRelationSource : function(args){
		var data = {};
		var list = typeof args === 'string' ? JSON.parse(args) : args;

		if (!Array.isArray(list) || list.length === 0){
			return '無';
		}

		for (var i in list){
			var json = list[i];
			var key = (DOMPurify.sanitize(json.custId2)+' '+DOMPurify.sanitize(json.dupNo2)) || '';
			var value = (DOMPurify.sanitize(json.custId1)+' '+DOMPurify.sanitize(json.dupNo1)) || '';
			var m = data[key] || {};
			m[value] = value;
			data[key] = m;
		}
		
		var result = '';
		for (var i in data){
			var s = '';
			for (var j in data[i]){
				s += (s.length > 0 ? '、' :  '') + j;
			}
			result += (result.length > 0 ? '<br/>' :  '') + i + ' -> ' + s;
		}
		return result;
	}
}