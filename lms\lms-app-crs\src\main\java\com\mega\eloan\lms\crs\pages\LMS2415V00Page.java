package com.mega.eloan.lms.crs.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.panels.LMS2415FilterPanel;

/**
 * <pre>
 * [個金]覆審報告表 覆審組處理中
 * </pre>
 */
@Controller
@RequestMapping("/crs/lms2415v00")
public class LMS2415V00Page extends AbstractEloanInnerView {
	@Autowired
	RetrialService retrialService;
	
	public LMS2415V00Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {

		setGridViewStatus(RetrialDocStatusEnum.區中心_編製中, RetrialDocStatusEnum.區中心_待覆核);
		if(retrialService.overSeaProgram()){
			addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Filter);
		}else{
			addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Filter,
					LmsButtonEnum.Print);
		}
		
		renderJsI18N(LMS2415V01Page.class);
		renderRespMsgJsI18N("EFD3026"); // 多render一個msgi18n
		
		setupIPanel(new LMS2415FilterPanel(PANEL_ID), model, params);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript","loadScript('pagejs/crs/LMS2415V01Page');");
	}
}
