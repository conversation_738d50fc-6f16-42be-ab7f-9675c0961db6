/* 
 *ObsdbELF388 Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 核准額度資料檔  ELF388
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
public interface ObsdbELF388Service {
	/**
	 * 新增
	 * 
	 * @param BRNID
	 *            上傳銀行ID
	 * @param dataList
	 *            sql陣列
	 * 
	 *            <pre>
	 *  quotaNo 額度序號
	 *  custId    CUSTID
	 *  dupNo    重複序號
	 *  icbcNo 授信經辦行員代號
	 *  cName  授信經辦姓名
	 *  omgrName  初放主管姓名
	 *  fmgrName 敘作主管姓名
	 *  approLvl  授權等級
	 *  updater  資料修改人
	 * </pre>
	 */
	void insert(String BRNID, Object[] dataList);

	/**
	 * 更新
	 * 
	 * @param BRNID
	 *            上傳銀行ID
	 * @param dataList
	 *            sql陣列
	 * 
	 *            <pre>
	 *  quotaNo 額度序號
	 *  custId  CUSTID
	 *  dupNo  重複序號
	 *  icbcNo 授信經辦行員代號
	 *  cName 授信經辦姓名
	 *  fmgrName 敘作主管姓名
	 *  approLvl 授權等級
	 *  updater  資料修改人
	 * </pre>
	 */
	void update(String BRNID, Object[] dataList);

	/**
	 * 查詢
	 * 
	 * @param BRNID
	 *            上傳銀行
	 * @param args
	 *            額度序號
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> selByQuotano(String BRNID, Object[] args);
}
