package com.mega.eloan.lms.cls.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.cls.panels.CLS1131S01RPanel;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C101S01R_N;
import com.mega.eloan.lms.model.C120S01R;

import tw.com.jcs.common.Util;

@Controller
@RequestMapping("/cls/cls1131s07")
public class CLS1131S07Page extends AbstractOutputPage {

	@Autowired
	CLS1131Service cls1131Service;
	
	@Autowired
	ScoreService scoreService;

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {

		setNeedHtml(true); // need html

		String mainId = Util.trim(params.getString("mainId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		boolean isC120M01A = Util.equals("Y", params.getString("isC120M01A"));
		String varVer = "";
		if(isC120M01A){
			C120S01R c120s01r = cls1131Service.findModelByKey(C120S01R.class, mainId, custId,dupNo);
			if(c120s01r!=null){
				varVer = Util.trim(c120s01r.getVarVer());
			}
		}else{
			C101S01R c101s01r = cls1131Service.findModelByKey(C101S01R.class, mainId, custId,dupNo);
			if(c101s01r!=null){
				varVer = Util.trim(c101s01r.getVarVer());
			}	
		}
		
		if(Util.isEmpty(Util.trim(varVer))){
			varVer = scoreService.get_Version_CardLoan();
		}
		
		//雙軌模式運行
		boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
		boolean showSDT = false;
		if(scoreDoubleTrack){
			C101S01R_N model_RN = cls1131Service.findModelByKey(C101S01R_N.class, mainId, custId, dupNo);
			if(model_RN != null){
				showSDT = true;
			}
		}
		
		new CLS1131S01RPanel("CLS1131S01_R", varVer, showSDT)
				.processPanelData(model, params); // add panel

		renderJsI18N(CLS1131S01RPanel.class); // render i18n

		setJavascript(new String[] { "pagejs/cls/CLS1131S07Page.js" });

		return "&nbsp;";
	}

	// UPGRADE: 待確認是否需要ViewName
	@Override
	protected String getViewName() {
		return null;
	}

}
