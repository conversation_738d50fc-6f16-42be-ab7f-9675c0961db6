package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L250S02ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L250S02A;

@Repository
public class L250S02ADaoImpl extends LMSJpaDao<L250S02A, String> implements
		L250S02ADao {
	@Override
	public List<L250S02A> getAll() {

		ISearch search = createSearchTemplete();
		search.addOrderBy("group");
		search.addOrderBy("sub1Order");
		return find(search);

	}

	@Override
	public List<L250S02A> getByGroup(Integer group) {

		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "group", group);
		search.addOrderBy("sub1Order");
		return find(search);

	}

}