<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <form id="C102M01AForm" name="C102M01AForm">
                <fieldset>
                    <legend>
                        <b>
                            <th:block th:text="#{doc.baseInfo}">
                                <!--基本資訊-->
                            </th:block>
                        </b>
                    </legend>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="20%" class="hd1">
                                    <th:block th:text="#{doc.branchName}">
                                        <!--  分行名稱-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="ownBrId" ></span><span id="ownBrName" ></span>
                                </td>
                                <td width="20%" class="hd1">
                                    <th:block th:text="#{doc.docStatus}">
                                        <!--文件狀態-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span class="color-red"><b><span id="docStatus" ></span></b></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <th:block th:text="#{C102M01A.custId}">
                                        <!--借款人統編-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <input id="custId" name="custId" type="text" size="10" class="required" maxlength="10">
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{C102M01A.DupNo}">
                                        <!--重複序號-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <input id="dupNo" name="dupNo" type="text" size="1" class="required" _requiredLength="1" maxlength="1">
                                	
                                    <button id="getcustName" type="button">
                                        <span class="text-only">
                                            <th:block th:text="#{C102M01A.getcustName}">
                                                <!--引進客戶姓名-->
                                            </th:block>
                                        </span>
                                    </button>
								</td>
								
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <th:block th:text="#{C102M01A.custName}">
                                        借款人姓名
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="custName" name="custName" class="required"></span>
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{C102M01A.cntrNo}">
                                        額度序號
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <input id="cntrNo" name="cntrNo" type="text" size="12" class="required" _requiredLength="12" maxlength="12">
                                    <button id="getaLoanAC" type="button">
                                        <span class="text-only">
                                            <th:block th:text="#{C102M01A.getaLoanAC}">
                                                <!--引進放款帳號-->
                                            </th:block>
                                        </span>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <th:block th:text="#{C102M01A.aLoanAC}">
                                        放款帳號
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    
									<span id="aLoanAC" name="aLoanAC"></span>
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{C102M01A.aLoanDate}">
                                        首撥日期
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="aLoanDate" name="aLoanDate" ></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
					<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
                                <td class="hd1" width="20%">
                                    <th:block th:text="#{C102M01A.Explain}">
                                       說明
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="75%">
                                    <th:block th:text="#{C102M01A.Explain1}">
                                        內容1
                                    </th:block>&nbsp;&nbsp;
									<br/>
									<th:block th:text="#{C102M01A.Explain2}">
                                        內容2
                                    </th:block>&nbsp;&nbsp;
                                </td>
                               
                            </tr>
					</table>		
                </fieldset>
                <fieldset>
                    <legend>
                        <b>
                            <th:block th:text="#{C102M01A.title1}">
                                <!--基本條件檢核-->
                            </th:block>
                        </b>
                    </legend>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr align="center">
                                <td class="hd1" style='text-align:center;width:5%'>
                                    <th:block th:text="#{C102M01A.titleseq}">
                                        <!--項次-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="25%" class="hd1" style='text-align:center'>
                                    <th:block th:text="#{C102M01A.titlecheck}">
                                        <!--符合/不符合-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="70%" class="hd1" style='text-align:center'>
                                    <th:block th:text="#{C102M01A.Content}">
                                        <!--內容-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td width="3%">
                                    <th:block th:text="#{C102M01A.titleseq1}">
                                        <!--1-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="25%">
                                	<label>
                                    <input id="chk11" name="chk11" value="Y" type="radio" class="required" />
                                    <th:block th:text="#{C102M01A.checkY}">
                                        符合
                                    </th:block>&nbsp;&nbsp;
									</label>
									<label>	
									<input id="chk11" name="chk11" value="N" type="radio" class="required" />
                                    <th:block th:text="#{C102M01A.checkN}">
                                        不符合
                                    </th:block>&nbsp;&nbsp;
									</label>
                                </td>
                                <td width="70%">
                                    <th:block th:text="#{C102M01A.Content1}">
                                        <!--內容-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td width="3%">
                                    <th:block th:text="#{C102M01A.titleseq2}">
                                        <!--2-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="25%">
                                	<label>
                                    <input id="chk12" name="chk12" value="Y" type="radio" class="required" />
                                    <th:block th:text="#{C102M01A.checkY}">
                                        符合
                                    </th:block>&nbsp;&nbsp;
									</label>
									<label>	
									<input id="chk12" name="chk12" value="N" type="radio" class="required" />
                                    <th:block th:text="#{C102M01A.checkN}">
                                        不符合
                                    </th:block>&nbsp;&nbsp;
									</label>
                                </td>
                                <td width="70%">
                                    <th:block th:text="#{C102M01A.Content2}">
                                        <!--內容-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td width="3%">
                                    <th:block th:text="#{C102M01A.titleseq3}">
                                        <!--2-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="25%">
                                	<label>
                                    <input id="chk13" name="chk13" value="Y" type="radio" class="required" />
                                    <th:block th:text="#{C102M01A.checkY}">
                                        符合
                                    </th:block>&nbsp;&nbsp;
									</label>
									<label>
									<input id="chk13" name="chk13" value="N" type="radio" class="required" />
                                    
									<th:block th:text="#{C102M01A.checkN}">
                                        不符合
                                    </th:block>&nbsp;&nbsp;
									</label>
                                </td>
                                <td width="70%">
                                    <th:block th:text="#{C102M01A.Content3}">
                                        <!--內容-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td width="3%">
                                    <th:block th:text="#{C102M01A.titleseq4}">
                                        <!--2-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="25%">
                                	<label>
                                    <input id="chk14" name="chk14" value="Y" type="radio" class="required" />
                                    <th:block th:text="#{C102M01A.checkY}">
                                        符合
                                    </th:block>&nbsp;&nbsp;
									</label>
									<label>
									<input id="chk14" name="chk14" value="N" type="radio" class="required" />
                                    <th:block th:text="#{C102M01A.checkN}">
                                        不符合
                                    </th:block>&nbsp;&nbsp;
									</label>
                                </td>
                                <td width="70%">
                                    <th:block th:text="#{C102M01A.Content4}">
                                        <!--內容-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
				
				
						<th:block th:if="${showRptId_V20171231}">
						<!-- 版本 V20171231  -->
						<fieldset id="fieldset_C102M01A_showRptId_V20171231">
						<legend id="legend_C102M01A_showRptId_V20171231"><b><th:block th:text="#{C102M01A.title2}"><!-- 本案適用風險權數 --></th:block></b>
						</legend>
						<div>
							<th:block th:text="#{C102M01A.rskFlagStart}">本案適用於風險權數</th:block>&nbsp;&nbsp;
							<label>
								<input id="rskFlag" name="rskFlag" value="3" type="radio" class="required" />				
			                	<th:block th:text="#{C102M01A.rskFlag3}">35%</th:block>&nbsp;&nbsp;
			               	</label>
			               	<label>
			               		<input id="rskFlag" name="rskFlag" value="4" type="radio" class="required" />
						    	<th:block th:text="#{C102M01A.rskFlag4}">75%</th:block>&nbsp;&nbsp;
							</label>
							<label>
			               		<input id="rskFlag" name="rskFlag" value="2" type="radio" class="required" />
						    	<th:block th:text="#{C102M01A.rskFlag2}">100%</th:block>&nbsp;&nbsp;
							</label>
							<span ><th:block th:text="#{C102M01A.rskFlagEnd2.V20171231}">(非住宅用途之房貸案件，風險權數仍為100%)</th:block>
								<br/>
							</span>
							<label>
			               		<input id="rskFlag" name="rskFlag" value="1" type="radio" class="required" />
						    	<th:block th:text="#{C102M01A.rskFlag1}">45%</th:block>
							</label>			                
							<th:block th:text="#{C102M01A.rskFlagEnd1.V20171231}">(首撥日在2011-04-21以前案件未符合自用住宅定義者為45%)</th:block>&nbsp;&nbsp;
							
							<br/>
							<th:block th:text="#{C102M01A.Content5.V20171231}">
			                    (請依103年9月22日金管銀法字第10310004912號函附件之問與答、及106年11月16日金管銀法字第10610005770號令修正「以住宅不動產為擔保之債權」適用之風險權數，點選適用之風險權數)
			                </th:block>&nbsp;&nbsp;
							<button id="getQuest" type="button">
			                      <span class="text-only">
			                             <th:block th:text="#{C102M01A.getQuest}">
			                                  <!--開啟 問與答-->
			                             </th:block>
			                       </span>
			                </button>
						</div>
						</fieldset>	
						</th:block>
						
						<th:block th:if="${showRptId_default}">
						<!-- 舊版本  -->
						<fieldset id="fieldset_C102M01A_showRptId_default">
						<legend id="legend_C102M01A_showRptId_default"><b><th:block th:text="#{C102M01A.title2}"><!-- 本案適用風險權數 --></th:block></b>
						</legend>
						<div>
							<th:block th:text="#{C102M01A.rskFlagStart}">本案適用於風險權數</th:block>&nbsp;&nbsp;
							<label>
								<input id="rskFlag" name="rskFlag" value="1" type="radio" class="required" />				
			                	<th:block th:text="#{C102M01A.rskFlag1}">45%</th:block>&nbsp;&nbsp;
			               	</label>
			               	<label>
			               		<input id="rskFlag" name="rskFlag" value="2" type="radio" class="required" />
						    	<th:block th:text="#{C102M01A.rskFlag2}">100%</th:block>&nbsp;&nbsp;
							</label>
			                <th:block th:text="#{C102M01A.rskFlagEnd}">(首撥日在100/04/21以前案件皆為45%)</th:block>&nbsp;&nbsp;
							<br/>
							 <th:block th:text="#{C102M01A.Content5}">
			                    (請依103年7月17日金管銀法字第10300198760號函附件之問與答，點選適用之風險權數)
			                </th:block>&nbsp;&nbsp;
							<button id="getQuest" type="button">
			                      <span class="text-only">
			                             <th:block th:text="#{C102M01A.getQuest}">
			                                  <!--開啟 問與答-->
			                             </th:block>
			                       </span>
			                </button>
						</div>
						</fieldset>	
						</th:block>	
								
				
				<fieldset>
                    <legend>
                        <b>
                            <th:block th:text="#{doc.docUpdateLog}">
                                <!-- 文件異動紀錄 -->
                            </th:block>
                        </b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer">
                            <!-- 文件異動紀錄 --><div id="_docLog" class="forview" th:insert="~{common/panels/DocLogPanel :: DocLogPanel}"></div>
                        </div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="35%" class="hd1">
                                    <th:block th:text="#{doc.creator}">
                                        <!--  文件建立者-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="15%">
                                    <span id='creator'></span>(<span id='createTime'></span>)
                                </td>
                                <td width="30%" class="hd1">
                                    <th:block th:text="#{doc.lastUpdater}">
                                        <!--  最後異動者-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="20%">
                                    <span id='updater'></span>(<span id='updateTime'></span>)
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                	<th:block th:text="#{C102M01A.rptId}">表單版本</th:block>&nbsp;
                                		
                                </td>
                                <td>
                                	<span id="rptId" ></span>&nbsp;
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{doc.docCode}">
                                        <!--文件亂碼-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="randomCode" ></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
            </form><!------------------------------批核欄-------------------------------------------------->
            <fieldset>
                <div id="tabs-appr" class="tabs">
                    <ul>
                        <li>
                            <a href="#tabs-appr01"><b>
                                    <th:block th:text="#{C102M01B.title01}">
                                        <!--營業單位-->
                                    </th:block>
                                </b></a>
                        </li>
                    </ul>
                    <div class="tabCtx-warp">
                        <div id="tabs-appr01" class="content">
                            <p>
                            <table width="100%">
                                <tr>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{C102M01B.managerId}">
                                                <!--經副襄理-->
                                            </th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="managerId" ></span>
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{C102M01B.bossId}">
                                                <!-- 授信主管-->
                                            </th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="bossId" ></span>
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{C102M01B.reCheckId}">
                                                <!--覆核主管-->
                                            </th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="reCheckId"></span>
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{C102M01B.apprId}">
                                                <!--  經辦-->
                                            </th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="showApprId"></span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </fieldset><!------------------------------批核欄----END---------------------------------------------->
            <div id="openCustNameBox" style="display: none;">
                <div id="openCustNameGrid">
                </div>
            </div>
            <div id="openaLoanACBox" style="display: none;">
                <div id="openaLoanACGrid">
                </div>
            </div>
            <script type="text/javascript">
            	loadScript('pagejs/cls/CLS1021S01Panel');
            </script>
        </th:block>
    </body>
</html>
