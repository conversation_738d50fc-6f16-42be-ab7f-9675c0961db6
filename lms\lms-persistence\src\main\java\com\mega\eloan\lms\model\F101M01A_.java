package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.annotation.Generated;
import javax.persistence.metamodel.SetAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.Meta_;

/**
 * <pre>
 * 財報主檔。The persistent class for the F101M01A database table.
 * </pre>
 * 
 * @since 2011/7/25
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/25,Sunkist Wang,new</li>
 *          </ul>
 */
@Generated(value = "Dali", date = "2011-07-25T17:22:12.390+0800")
@StaticMetamodel(F101M01A.class)
public class F101M01A_ extends Meta_ {
	public static volatile SingularAttribute<F101M01A, String> tradeType;
	public static volatile SingularAttribute<F101M01A, BigDecimal> amtUnit;
	public static volatile SingularAttribute<F101M01A, String> conso;
	public static volatile SingularAttribute<F101M01A, String> curr;
	public static volatile SingularAttribute<F101M01A, Date> eDate;
	public static volatile SingularAttribute<F101M01A, Date> sDate;
	public static volatile SingularAttribute<F101M01A, String> gaapFlag;
	public static volatile SingularAttribute<F101M01A, String> inFlag;
	public static volatile SingularAttribute<F101M01A, String> ncp;
	public static volatile SingularAttribute<F101M01A, String> othType;
	public static volatile SingularAttribute<F101M01A, String> periodType;
	public static volatile SingularAttribute<F101M01A, String> publicFlag;
	public static volatile SingularAttribute<F101M01A, String> remark;
	public static volatile SingularAttribute<F101M01A, String> source;
	public static volatile SingularAttribute<F101M01A, String> type;
	public static volatile SingularAttribute<F101M01A, String> year;
	public static volatile SetAttribute<F101M01A, F101S01A> f101s01as;
	public static volatile SetAttribute<F101M01A, F101S01B> f101s01bs;
}
