/* 
 * L140M01G.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 額度利率明細檔 **/
@NamedEntityGraph(name = "L140M01G-entity-graph", attributeNodes = { @NamedAttributeNode("l140m01f") })
@Entity
@Table(name = "L140M01G", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "rateSeq", "rateType" }))
public class L140M01G extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "RATESEQ", referencedColumnName = "RATESEQ", nullable = false, insertable = false, updatable = false)
		})
	private L140M01F l140m01f;

	public L140M01F getL140m01f() {
		return l140m01f;
	}

	public void setL140m01f(L140M01F l140m01f) {
		this.l140m01f = l140m01f;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)", insertable = false, updatable = false)
	private String mainId;

	/**
	 * 序號
	 * <p/>
	 * L140M01F_rateSeq
	 */
	@Column(name = "RATESEQ", columnDefinition = "DECIMAL(5,0)", insertable = false, updatable = false)
	private Integer rateSeq;

	/**
	 * 類別
	 * <p/>
	 * 1新台幣、2美金、3日幣、4歐元、5雜幣、6費率、7泰銖
	 */
	@Column(name = "RATETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String rateType;

	/**
	 * 加碼基礎選項
	 * <p/>
	 * 詳【註1】<br/>
	 * (若為新台幣且選擇「1.本行基準利率」、「17.6S基準利率月指標利率」為空白則不組入文字串中)
	 */
	@Column(name = "RATEBASE", length = 2, columnDefinition = "CHAR(2)")
	private String rateBase;

	/**
	 * 加碼基礎組成文字
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * ※動審表上傳用(INT_BASE)<br/>
	 * 配合動審表上傳LN.LNF164<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(100)+0F<br/>
	 * 100/2*3+2=152<br/>
	 * 101/05/25調整，<br/>
	 * for「其他自行輸入」 VARCHAR(152)VARCHAR(1500)
	 */
	@Column(name = "RATEINPUT", length = 1500, columnDefinition = "VARCHAR(1500)")
	private String rateInput;

	/**
	 * 加碼基礎加減碼
	 * <p/>
	 * ※動審表上傳用(INT_SPREAD)<br/>
	 * 配合動審表上傳LN.LNF164
	 */
	@Column(name = "RATEVALUE", columnDefinition = "DECIMAL(7,5)")
	private Double rateValue;

	/**
	 * 輸入欄位1
	 * <p/>
	 * 依所選擇的「加碼基礎選項」，可依序填入「輸入欄位1~5」，於前端檢核各欄可輸入之型態與數值正確性，後端則皆以文字型態儲存。
	 * 101/05/25調整，for「其他自行輸入」 VARCHAR(30)  VARCHAR(1024)
	 * 
	 * 
	 */
	@Column(name = "RATEINPUT1", length = 1024, columnDefinition = "VARCHAR(1024)")
	private String rateInput1;

	/** 輸入欄位2 **/
	@Column(name = "RATEINPUT2", length = 30, columnDefinition = "VARCHAR(30)")
	private String rateInput2;

	/** 輸入欄位3 **/
	@Column(name = "RATEINPUT3", length = 30, columnDefinition = "VARCHAR(30)")
	private String rateInput3;

	/** 輸入欄位4 **/
	@Column(name = "RATEINPUT4", length = 30, columnDefinition = "VARCHAR(30)")
	private String rateInput4;

	/** 輸入欄位5 **/
	@Column(name = "RATEINPUT5", length = 30, columnDefinition = "VARCHAR(30)")
	private String rateInput5;

	/**
	 * 利率方式
	 * <p/>
	 * 1固定利率<br/>
	 * 2機動利率<br/>
	 * 3定期浮動<br/>
	 * ※若選擇「機動利率」則不組入文字串中
	 */
	@Column(name = "RATEKIND", length = 1, columnDefinition = "CHAR(1)")
	private String rateKind;

	/**
	 * 收息方式
	 * <p/>
	 * 1按月收息<br/>
	 * 2每三個月收息乙次<br/>
	 * 3每半年收息乙次<br/>
	 * 4按年收息<br/>
	 * 5本息併付<br/>
	 * 6預收利息<br/>
	 * ※若選擇「按月收息」則不組入文字串中
	 */
	@Column(name = "RATEGETINT", length = 1, columnDefinition = "CHAR(1)")
	private String rateGetInt;

	/**
	 * 利率變動方式
	 * <p/>
	 * 1中長期<br/>
	 * 2短期
	 */
	@Column(name = "RATECHGKIND", length = 1, columnDefinition = "CHAR(1)")
	private String rateChgKind;

	/**
	 * 中長期調整方式1
	 * <p/>
	 * 1星期<br/>
	 * 2月<br/>
	 * 3季<br/>
	 * 4半年<br/>
	 * 5九個月<br/>
	 * 6年
	 */
	@Column(name = "RATECHG1", length = 1, columnDefinition = "CHAR(1)")
	private String rateChg1;

	/**
	 * 中長期調整方式2
	 * <p/>
	 * ※可複選1|2|3…<br/>
	 * 1一個月<br/>
	 * 2二個月<br/>
	 * 3三個月<br/>
	 * 4四個月<br/>
	 * 5五個月<br/>
	 * 6六個月
	 */
	@Column(name = "RATECHG2", length = 12, columnDefinition = "VARCHAR(12)")
	private String rateChg2;

	/**
	 * 短期調整方式
	 * <p/>
	 * ※可空白<br/>
	 * 1.逐筆議定
	 */
	@Column(name = "RATECHG3", length = 1, columnDefinition = "CHAR(1)")
	private String rateChg3;

	/**
	 * 稅負洽收
	 * <p/>
	 * 1借款人<br/>
	 * 2銀行
	 */
	@Column(name = "RATETAX", length = 1, columnDefinition = "CHAR(1)")
	private String rateTax;

	/**
	 * 組成說明字串
	 * <p/>
	 * ※動審表上傳用(INT_MEMO)<br/>
	 * 配合動審表上傳LN.LNF164<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(200)+0F<br/>
	 * 200/2*3+2=302 <br/>
	 * 101/05/25調整，for「其他自行輸入」 VARCHAR(302)VARCHAR(2048)
	 */
	@Column(name = "RATEDSCR", length = 2048, columnDefinition = "VARCHAR(2048)")
	private String rateDscr;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得序號
	 * <p/>
	 * L140M01F_rateSeq
	 */
	public Integer getRateSeq() {
		return this.rateSeq;
	}

	/**
	 * 設定序號
	 * <p/>
	 * L140M01F_rateSeq
	 **/
	public void setRateSeq(Integer value) {
		this.rateSeq = value;
	}

	/**
	 * 取得類別
	 * <p/>
	 * 1新台幣、2美金、3日幣、4歐元、5雜幣、6費率
	 */
	public String getRateType() {
		return this.rateType;
	}

	/**
	 * 設定類別
	 * <p/>
	 * 1新台幣、2美金、3日幣、4歐元、5雜幣、6費率
	 **/
	public void setRateType(String value) {
		this.rateType = value;
	}

	/**
	 * 取得加碼基礎選項
	 * <p/>
	 * 詳【註1】<br/>
	 * (若為新台幣且選擇「1.本行基準利率」、「17.6S基準利率月指標利率」為空白則不組入文字串中)
	 */
	public String getRateBase() {
		return this.rateBase;
	}

	/**
	 * 設定加碼基礎選項
	 * <p/>
	 * 詳【註1】<br/>
	 * (若為新台幣且選擇「1.本行基準利率」、「17.6S基準利率月指標利率」為空白則不組入文字串中)
	 **/
	public void setRateBase(String value) {
		this.rateBase = value;
	}

	/**
	 * 取得加碼基礎組成文字
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * ※動審表上傳用(INT_BASE)<br/>
	 * 配合動審表上傳LN.LNF164<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(100)+0F<br/>
	 * 100/2*3+2=152 101/05/25調整，<br/>
	 * for「其他自行輸入」 VARCHAR(152)VARCHAR(1500)
	 */
	public String getRateInput() {
		return this.rateInput;
	}

	/**
	 * 設定加碼基礎組成文字
	 * <p/>
	 * 組合文字或其他自行輸入<br/>
	 * ※動審表上傳用(INT_BASE)<br/>
	 * 配合動審表上傳LN.LNF164<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(100)+0F<br/>
	 * 100/2*3+2=152 101/05/25調整，<br/>
	 * for「其他自行輸入」 VARCHAR(152)VARCHAR(1500)
	 **/
	public void setRateInput(String value) {
		this.rateInput = value;
	}

	/**
	 * 取得加碼基礎加減碼
	 * <p/>
	 * ※動審表上傳用(INT_SPREAD)<br/>
	 * 配合動審表上傳LN.LNF164
	 */
	public Double getRateValue() {
		return this.rateValue;
	}

	/**
	 * 設定加碼基礎加減碼
	 * <p/>
	 * ※動審表上傳用(INT_SPREAD)<br/>
	 * 配合動審表上傳LN.LNF164
	 **/
	public void setRateValue(Double value) {
		this.rateValue = value;
	}

	/**
	 * 取得輸入欄位1
	 * <p/>
	 * 依所選擇的「加碼基礎選項」，可依序填入「輸入欄位1~5」，於前端檢核各欄可輸入之型態與數值正確性，後端則皆以文字型態儲存。
	 * 101/05/25調整，for「其他自行輸入」 VARCHAR(30)  VARCHAR(1024)
	 */
	public String getRateInput1() {
		return this.rateInput1;
	}

	/**
	 * 設定輸入欄位1
	 * <p/>
	 * 依所選擇的「加碼基礎選項」，可依序填入「輸入欄位1~5」，於前端檢核各欄可輸入之型態與數值正確性，後端則皆以文字型態儲存。
	 * 101/05/25調整，for「其他自行輸入」 VARCHAR(30)  VARCHAR(1024)
	 **/
	public void setRateInput1(String value) {
		this.rateInput1 = value;
	}

	/** 取得輸入欄位2 **/
	public String getRateInput2() {
		return this.rateInput2;
	}

	/** 設定輸入欄位2 **/
	public void setRateInput2(String value) {
		this.rateInput2 = value;
	}

	/** 取得輸入欄位3 **/
	public String getRateInput3() {
		return this.rateInput3;
	}

	/** 設定輸入欄位3 **/
	public void setRateInput3(String value) {
		this.rateInput3 = value;
	}

	/** 取得輸入欄位4 **/
	public String getRateInput4() {
		return this.rateInput4;
	}

	/** 設定輸入欄位4 **/
	public void setRateInput4(String value) {
		this.rateInput4 = value;
	}

	/** 取得輸入欄位5 **/
	public String getRateInput5() {
		return this.rateInput5;
	}

	/** 設定輸入欄位5 **/
	public void setRateInput5(String value) {
		this.rateInput5 = value;
	}

	/**
	 * 取得利率方式
	 * <p/>
	 * 1固定利率<br/>
	 * 2機動利率<br/>
	 * 3定期浮動<br/>
	 * ※若選擇「機動利率」則不組入文字串中
	 */
	public String getRateKind() {
		return this.rateKind;
	}

	/**
	 * 設定利率方式
	 * <p/>
	 * 1固定利率<br/>
	 * 2機動利率<br/>
	 * 3定期浮動<br/>
	 * ※若選擇「機動利率」則不組入文字串中
	 **/
	public void setRateKind(String value) {
		this.rateKind = value;
	}

	/**
	 * 取得收息方式
	 * <p/>
	 * 1按月收息<br/>
	 * 2每三個月收息乙次<br/>
	 * 3每半年收息乙次<br/>
	 * 4按年收息<br/>
	 * 5本息併付<br/>
	 * 6預收利息<br/>
	 * ※若選擇「按月收息」則不組入文字串中
	 */
	public String getRateGetInt() {
		return this.rateGetInt;
	}

	/**
	 * 設定收息方式
	 * <p/>
	 * 1按月收息<br/>
	 * 2每三個月收息乙次<br/>
	 * 3每半年收息乙次<br/>
	 * 4按年收息<br/>
	 * 5本息併付<br/>
	 * 6預收利息<br/>
	 * ※若選擇「按月收息」則不組入文字串中
	 **/
	public void setRateGetInt(String value) {
		this.rateGetInt = value;
	}

	/**
	 * 取得利率變動方式
	 * <p/>
	 * 1中長期<br/>
	 * 2短期
	 */
	public String getRateChgKind() {
		return this.rateChgKind;
	}

	/**
	 * 設定利率變動方式
	 * <p/>
	 * 1中長期<br/>
	 * 2短期
	 **/
	public void setRateChgKind(String value) {
		this.rateChgKind = value;
	}

	/**
	 * 取得中長期調整方式1
	 * <p/>
	 * 1星期<br/>
	 * 2月<br/>
	 * 3季<br/>
	 * 4半年<br/>
	 * 5九個月<br/>
	 * 6年
	 */
	public String getRateChg1() {
		return this.rateChg1;
	}

	/**
	 * 設定中長期調整方式1
	 * <p/>
	 * 1星期<br/>
	 * 2月<br/>
	 * 3季<br/>
	 * 4半年<br/>
	 * 5九個月<br/>
	 * 6年
	 **/
	public void setRateChg1(String value) {
		this.rateChg1 = value;
	}

	/**
	 * 取得中長期調整方式2
	 * <p/>
	 * ※可複選1|2|3…<br/>
	 * 1一個月<br/>
	 * 2二個月<br/>
	 * 3三個月<br/>
	 * 4四個月<br/>
	 * 5五個月<br/>
	 * 6六個月
	 */
	public String getRateChg2() {
		return this.rateChg2;
	}

	/**
	 * 設定中長期調整方式2
	 * <p/>
	 * ※可複選1|2|3…<br/>
	 * 1一個月<br/>
	 * 2二個月<br/>
	 * 3三個月<br/>
	 * 4四個月<br/>
	 * 5五個月<br/>
	 * 6六個月
	 **/
	public void setRateChg2(String value) {
		this.rateChg2 = value;
	}

	/**
	 * 取得短期調整方式
	 * <p/>
	 * ※可空白<br/>
	 * 1.逐筆議定
	 */
	public String getRateChg3() {
		return this.rateChg3;
	}

	/**
	 * 設定短期調整方式
	 * <p/>
	 * ※可空白<br/>
	 * 1.逐筆議定
	 **/
	public void setRateChg3(String value) {
		this.rateChg3 = value;
	}

	/**
	 * 取得稅負洽收
	 * <p/>
	 * 1借款人<br/>
	 * 2銀行
	 */
	public String getRateTax() {
		return this.rateTax;
	}

	/**
	 * 設定稅負洽收
	 * <p/>
	 * 1借款人<br/>
	 * 2銀行
	 **/
	public void setRateTax(String value) {
		this.rateTax = value;
	}

	/**
	 * 取得組成說明字串
	 * <p/>
	 * ※動審表上傳用(INT_MEMO)<br/>
	 * 配合動審表上傳LN.LNF164<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(200)+0F<br/>
	 * 200/2*3+2=302 101/05/25調整，for「其他自行輸入」 VARCHAR(302)VARCHAR(2048)
	 */
	public String getRateDscr() {
		return this.rateDscr;
	}

	/**
	 * 設定組成說明字串
	 * <p/>
	 * ※動審表上傳用(INT_MEMO)<br/>
	 * 配合動審表上傳LN.LNF164<br/>
	 * (100/08/29調整)配合上傳主機(MIS與AS400)調整欄位長度內容均需轉換為全型字並額外保留0E0F長度。<br/>
	 * 如：0E+CHAR(200)+0F<br/>
	 * 200/2*3+2=302 101/05/25調整，for「其他自行輸入」 VARCHAR(302)VARCHAR(2048)
	 **/
	public void setRateDscr(String value) {
		this.rateDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
