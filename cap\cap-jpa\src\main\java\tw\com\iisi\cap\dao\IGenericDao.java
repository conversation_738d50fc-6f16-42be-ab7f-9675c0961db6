/*
* IGenericDao.java
*
* Copyright (c) 2009-2011 International Integrated System, Inc.
* 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
* All Rights Reserved.
*
* Licensed Materials - Property of International Integrated System,Inc.
*
* This software is confidential and proprietary information of
* International Integrated System, Inc. ("Confidential Information").
*/
package tw.com.iisi.cap.dao;

import java.io.Serializable;
import java.util.Iterator;
import java.util.List;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

/**
 * <pre>
 * interface IGenericDao. 
 * 共用Dao接口
 * </pre>
 *
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/7,iristu,new
 *          <li>2011/6/20,iristu,增加findPage
 *          <li>2015/10/06,<PERSON>,增加distinct query
 *          </ul>
 * @param <T>
 *            the model
 */
public interface IGenericDao<T> {

    /**
     * Insert.
     * 
     * @param entity
     *            the entity
     */
    void save(T entity);

    /**
     * Multiple Insert.
     * 
     * @param entries
     */
    void save(List<T> entries);

    /**
     * Delete.
     * 
     * @param entity
     *            the entity
     */
    void delete(T entity);

    /**
     * Multiple delete
     * 
     * @param entries
     */
    void delete(List<T> entries);

    /**
     * Find.
     * 
     * @param pk
     *            the oid
     * 
     * @return the t
     */
    T find(Serializable pk);

    /**
     * Find.
     * 
     * @param entity
     * @return
     */
    T find(T entity);

    /**
     * find Unique or none
     * 
     * @param search
     *            SearchSetting
     * @return
     */
    T findUniqueOrNone(ISearch search);

    /**
     * find
     * 
     * @param search
     *            SearchSetting
     * @return
     */
    List<T> find(ISearch search);

    /**
     * 取得資料筆數
     * 
     * @param search
     *            SearchSetting
     * @return the int
     */
    int count(ISearch search);

    /**
     * 多筆搜尋清單
     * 
     * @param first
     *            首行
     * @param count
     *            總筆數
     * @return
     */
    Iterator<T> list(int first, int count);

    /**
     * 尋找頁面
     * 
     * @param search
     *            SearchSetting
     * @return
     */
    Page<T> findPage(ISearch search);

    /**
     * 尋找多個頁面
     * 
     * @param <S>
     * @param clazz
     *            類別
     * @param search
     *            SearchSetting
     * @return
     */
    <S> Page<S> findPage(Class<S> clazz, ISearch search);

    /**
     * create new search requirement
     * 
     * @return ISearch
     */
    ISearch createSearchTemplete();

    /**
     * flush
     */
    void flush();
    
    /**
     * clear
     */
    void clear();
}
