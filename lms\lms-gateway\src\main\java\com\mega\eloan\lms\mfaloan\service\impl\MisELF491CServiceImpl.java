
package com.mega.eloan.lms.mfaloan.service.impl;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF491C;
import com.mega.eloan.lms.mfaloan.service.MisELF491CService;

/**
 * <pre>
 * 消金抽樣覆審控制檔
 * </pre>
 * 
 * @since 2020/11/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/11/16,EL08034,new
 *          </ul>
 */
@Service
public class MisELF491CServiceImpl extends AbstractMFAloanJdbc implements MisELF491CService {

	@Override
	public int countByBrNoRuleNoLrDateBegEnd(String brNo, String ruleNo, String lrDate_beg, String lrDate_end){
		return this.getJdbc().queryForInt( "ELF491C.countByBrNoRuleNoLrDateBegEnd", new Object[] { brNo, ruleNo, lrDate_beg, lrDate_end });
	}
	
	@Override
	public int countRule95_all(String brNo){
		return this.getJdbc().queryForInt( "ELF491C.countRule95_all", new String[] { brNo+"%" });
	}
	
	@Override
	public int countRule95_active(String brNo){
		return this.getJdbc().queryForInt( "ELF491C.countRule95_active", new String[] { brNo+"%" });
	}
		
	@Override
	public List<Map<String, Object>> selByBrno_ELF491C_RULE_NO_95_1(String brNo, String period_beg, String period_end, String ruleNo_95_1){
		return this.getJdbc().queryForListWithMax("ELF491C.selByBrno_ELF491C_RULE_NO_95_1", new String[]{period_beg, period_end, brNo+"%", ruleNo_95_1, brNo}); 
	}
	
	@Override
	public List<Map<String, Object>> selByBrno_R1R2S(String brNo, BigDecimal threshold_amt, int fetch_size){
		Date currentDate = CapDate.getCurrentTimestamp();
		String dt_A = TWNDate.toAD(CapDate.addMonth(currentDate, -12));
		int split_month_cnt = 5; //前1~2個月的撥款案件，客戶還款次數太少 => 優先順序較後面
		String dt_split = StringUtils.substring(TWNDate.toAD(CapDate.addMonth(currentDate, -1 * split_month_cnt)), 0, 7)+"-01" ;		
		return this.getJdbc().queryForList("ELF491C.selByBrno_R1R2S", new Object[]{brNo, dt_A, threshold_amt, dt_A, dt_split}, 0, fetch_size);
	}
	
	@Override
	public List<Map<String, Object>> selAllBrnoCount_R14(String twYYYYMM){
		return this.getJdbc().queryForList("ELF491C.selAllBrnoCount_R14", new Object[]{ twYYYYMM, twYYYYMM }, 0, 500);
	}
	
	@Override
	public List<Map<String, Object>> selByBrno_R14(String brNo, int fetch_size){
		return this.getJdbc().queryForList("ELF491C.selByBrno_R14", new Object[]{ brNo }, 0, fetch_size);
	}
	
	@Override
	public int countRule_projectCreditLoan_all(String brNo, String loanDate_beg, String loanDate_end){
		return this.getJdbc().queryForInt( "ELF491C.countRule_projectCreditLoan_all", new String[]{loanDate_beg, loanDate_end, brNo+"%" });
	}
	
	@Override
	public int countRule_projectCreditLoan_active(String brNo, String loanDate_beg, String loanDate_end){
		return this.getJdbc().queryForInt( "ELF491C.countRule_projectCreditLoan_active", new String[]{loanDate_beg, loanDate_end, brNo+"%" });
	}
	
	@Override
	public List<Map<String, Object>> selByBrno_Rule_projectCreditLoan(String brNo, String loanDate_beg, String loanDate_end, int fetch_size){
		return this.getJdbc().queryForList("ELF491C.selByBrno_Rule_projectCreditLoan", new String[]{loanDate_beg, loanDate_end, brNo+"%" }, 0, fetch_size);
	}
	
	@Override
	public List<Map<String, Object>> list_baseCnt_R95_1(){
		return this.getJdbc().queryForListWithMax( "ELF491C.list_baseCnt_R95_1", new String[] { });
	}

	@Override
	public List<ELF491C> find(String brNo, String custid, String dupNo, String ruleNo, String lrDate){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF491C.find", new String[]{brNo, custid, dupNo, ruleNo, lrDate});
		return toELF491C(rowData);
	}
	
	@Override
	public Date findLatest_lrDate(String brNo, String custid, String dupNo, String ruleNo){
		int fetch_size = 1; 
		List<Map<String, Object>> rowData = this.getJdbc().queryForList("ELF491C.find_orderByLrDateDesc", new String[]{brNo, custid, dupNo, ruleNo}, 0, fetch_size);
		if(rowData.size()==0){
			return null;
		}else{
			return toELF491C(rowData).get(0).getElf491c_lrdate();	
		}		
	}
	
	private List<ELF491C> toELF491C(List<Map<String, Object>> rowData){
		List<ELF491C> list = new ArrayList<ELF491C>();
		for (Map<String, Object> row : rowData) {
			ELF491C model = new ELF491C();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}	
}
