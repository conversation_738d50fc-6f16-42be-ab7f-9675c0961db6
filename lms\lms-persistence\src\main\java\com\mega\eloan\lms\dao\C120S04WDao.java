/* 
 * C120S04WDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S04W;

/** RPA發查明家事公告細檔 **/
public interface C120S04WDao extends IGenericDao<C120S04W> {

	C120S04W findByOid(String oid);
	
	List<C120S04W> findByMainId(String mainId);
	
	C120S04W findByUniqueKey(String MainId, String branchNo, String empNo, String dataCustomerNo);
	
	public List<C120S04W> findBy(String mainId, String custId);
}