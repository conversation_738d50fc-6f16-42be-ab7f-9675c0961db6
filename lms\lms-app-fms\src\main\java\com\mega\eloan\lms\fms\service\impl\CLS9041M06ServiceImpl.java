/* 
 *CLS9041M04ServiceImp.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.lms.dao.C004M01ADao;
import com.mega.eloan.lms.dao.C004S01ADao;
import com.mega.eloan.lms.fms.service.CLS9041M06Service;
import com.mega.eloan.lms.mfaloan.service.impl.AbstractMFAloanJdbc;
import com.mega.eloan.lms.model.C004M01A;
import com.mega.eloan.lms.model.C004S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 辦理留學生貸款申請補貼利息明細表
 * </pre>
 * 
 * @since 2012/11/01
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/01,Vector Lo,new
 *          </ul>
 */
/* Use MIS-RDB */
@Service
public class CLS9041M06ServiceImpl extends AbstractMFAloanJdbc implements
		CLS9041M06Service {
	private static final Logger logger = LoggerFactory
			.getLogger(CLS9041M06ServiceImpl.class);

	@Autowired
	private DocFileDao docFileDao;

	@Resource
	C004M01ADao c004m01aDao;

	@Resource
	C004S01ADao c004s01aDao;

	/**
	 * 取得資料
	 * 
	 * @param String起始年度
	 * @param String起始月份
	 * @param String結束月份
	 * @return
	 * @throws CapException
	 * @throws IOException
	 */
	@Override
	public List<Map<String, Object>> getMisData(String beginDate, String endDate){
		return getJdbc().queryForList("MIS.LNF193.SEARCH",
				new String[] { _yyyyMM(beginDate), _yyyyMM(endDate) });
	}

	@Override
	public List<Map<String, Object>> getStuData(String beginDate, String endDate){
		return getJdbc().queryForList("MIS.STUDATA.REFLECT",
				new String[] { _yyyyMM(beginDate), _yyyyMM(endDate) });
	}
	
	private String _yyyyMM(String d){
		//2015-05-06
		return StringUtils.substring(d, 0, 4)+StringUtils.substring(d, 5, 7);
	}
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("CapException!!", e);
				}

				if (model instanceof C004M01A) {
					c004m01aDao.save(((C004M01A) model));
				} else if (model instanceof C004S01A) {
					c004s01aDao.save(((C004S01A) model));
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model instanceof C004M01A) {
				c004m01aDao.delete(((C004M01A) model));
			} else if (model instanceof C004S01A) {
				c004s01aDao.delete(((C004S01A) model));
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch pageSetting) {
		if (clazz == C004M01A.class) {
			return c004m01aDao.findPage(pageSetting);
		} else if (clazz == C004S01A.class) {
			return c004s01aDao.findPage(pageSetting);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C004M01A.class) {
			C004M01A model = Util.isEmpty(oid) ? null : c004m01aDao
					.findByOid(oid);
			return (T) (model == null ? null : model);
		} else if (clazz == C004S01A.class) {
			C004S01A model = Util.isEmpty(oid) ? null : c004s01aDao
					.findByOid(oid);
			return (T) (model == null ? null : model);
		}
		return null;
	}


	@SuppressWarnings("rawtypes")
	@Override
	public Page findFile(String mainId) {
		ISearch search = docFileDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		return docFileDao.findPage(search);
	}
	@Override
	public void deleteFile(String oid) {
		ISearch search = docFileDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		List<DocFile> docfiles = docFileDao.find(search);
		for (DocFile docfile : docfiles) {
			docfile.setDeletedTime(CapDate.getCurrentTimestamp());
		}
		docFileDao.save(docfiles);
	}

	@Override
	public Map<String, Object> getLNN192(String LNF192_CUST_ID,String LNF192_CUST_ID_DUP
			,String LNF192_STUDENT_ID,String LNF192_BEG_DATE){
		return getJdbc().queryForMap("MIS.LNF192.SEARCH_byId_begDate",
				new String[] { LNF192_CUST_ID, LNF192_CUST_ID_DUP,LNF192_STUDENT_ID,LNF192_BEG_DATE });
	}
}
