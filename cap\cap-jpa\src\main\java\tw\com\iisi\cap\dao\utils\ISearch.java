/*
 * Search.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.dao.utils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * ISearch for dao use
 * </pre>
 * 
 * @since 2011/3/28
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/3/28,iristu,new
 *          <li>2011/6/10,Rod<PERSON><PERSON><PERSON>, add method
 *          <li>2015/10/6,<PERSON>, 增加distinct設定欄位
 *          </ul>
 * @param <T>
 */
public interface ISearch extends Serializable {

    /**
     * 取得實體名
     * 
     * @return
     */
    String getEntityName();

    /**
     * 設置實體名
     * 
     * @param entityName
     *            實體名
     * @return
     */
    ISearch setEntityName(String entityName);

    /**
     * <pre>
     * 是否distinct result
     * </pre>
     * 
     * @param isDistinct
     *            true/false
     */
    void setDistinct(boolean isDistinct);

    /**
     * <pre>
     * 是否distinct result
     * </pre>
     * 
     * @return boolean
     */
    boolean isDistinct();

    /**
     * 是否排序
     * 
     * @return boolean
     */
    boolean hasOrderBy();

    /**
     * Specify that results must be ordered by the passed column Null by default. 預設為升羃排序
     * 
     * @param orderBy
     *            the order by
     * @return T extends ISearch
     */
    ISearch addOrderBy(String orderBy);

    /**
     * Specify that results must be ordered by the passed column Null by default.
     * 
     * @param orderBy
     *            orderBy
     * @param orderDesc
     *            是否要降羃排序
     * @return T extends ISearch
     */
    ISearch addOrderBy(String orderBy, boolean orderDesc);

    /**
     * 設定排序形式
     * 
     * @param orderBy
     * @return
     */
    ISearch setOrderBy(Map<String, Boolean> orderBy);

    /**
     * 取得排序形式
     * 
     * @return
     */
    Map<String, Boolean> getOrderBy();

    /**
     * 設定查詢的筆數
     * 
     * @param maxResults
     * @return T extends ISearch
     */
    ISearch setMaxResults(int maxResults);

    /**
     * 取得查詢的筆數
     * 
     * @return
     */
    int getMaxResults();

    /**
     * 設定查詢的頁碼
     * 
     * @param firstResult
     * @return T extends ISearch
     */
    ISearch setFirstResult(int firstResult);

    /**
     * 取得查詢的頁碼
     * 
     * @return
     */
    int getFirstResult();

    /**
     * 取得已設置查詢模式
     * 
     * @return
     */
    List<SearchModeParameter> getSearchModeParameters();

    /**
     * 設定查詢模式
     *
     * @param searchMode
     * @param key
     * @param value
     * @return
     */
    ISearch addSearchModeParameters(SearchMode searchMode, Object key, Object value);

    /**
     * 設定查詢模式
     * 
     * @param search
     * @return
     */
    ISearch addSearchModeParameters(ISearch search);

    /**
     * 設定Distinct欄位
     * 
     * @param distinctColumns
     * @return
     */
    ISearch setDistinctColumn(String[] distinctColumns);

    /**
     * 取得已設定的Distinct欄位
     * 
     * @return
     */
    String[] getDistinctColumn();

}
