/* 
 * CLS101S01S2Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.DataView;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.model.C101S01S;
import com.mega.eloan.lms.model.C120S01S;

import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金徵信作業-銀行法及金控法第44/45條HTML
 * </pre>
 * 
 * @since 2020/05/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/05/25,09763,new
 *          </ul>
 */
public class CLS101S01S2Panel extends Panel {

	@Autowired
	CLSService clsService;

	@Autowired
	CodeTypeService codeTypeService;

	private static final long serialVersionUID = 1L;

	/**
	 * @param id
	 */
	public CLS101S01S2Panel(String id) {
		super(id);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		JSONObject json = new JSONObject();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");
		boolean isC120M01A = Util.equals("Y",
				Util.trim(params.getString("isC120M01A")));
		if (isC120M01A) {
			// 簽報書
			// 銀行法及金控法第44/45條
			List<C120S01S> type1List = clsService.findC120S01S_byIdDupDataType(
					mainId, custId, dupNo, "2");
			byte[] byteArr = type1List.get(0).getReportFile();
			String str = new String(byteArr);
			json = JSONObject.fromObject(str);
		} else {
			// 個金徵信
			// 銀行法及金控法第44/45條
			List<C101S01S> type1List = clsService.findC101S01S_byIdDupDataType(
					mainId, custId, dupNo, "2");
			byte[] byteArr = type1List.get(0).getReportFile();
			String str = new String(byteArr);
			json = JSONObject.fromObject(str);
		}
		// 查詢人員
		model.addAttribute("queryPerson", json.optString("queryPerson"));
		// 日期
		model.addAttribute("date", json.optString("date"));
		// 銀行法及金控法第44/45條
		JSONArray list = json.getJSONArray("list");
		List<JSONObject> table1 = convertJSONArrayToList(list);
		DataView<JSONObject> dataView1 = getGridDataView("_table1", table1);
		dataView1.processData(null, params);
		// 是否在頁面上加入列印換頁DIV
		boolean addPageBreak = params.getAsBoolean("addPageBreak", false);
		model.addAttribute("addPageBreak", addPageBreak);
	}

	private DataView<JSONObject> getGridDataView(final String id,
			List<JSONObject> list) {
		final int col = list.get(0).size();
		DataView<JSONObject> dataView = new DataView<JSONObject>(id, list) {
			@Override
			protected Map<String, Object> populateItem(JSONObject item) {
				Map<String, Object> result = new HashMap<String, Object>();
				for (int i = 0; i < col; i++) {
					result.put("_td" + i,
							CapString.trimNull(item.optString("td" + i)));
				}
				return result;
			}
		};
		return dataView;
	}

	private List<JSONObject> convertJSONArrayToList(JSONArray array) {
		List<JSONObject> list = new ArrayList<JSONObject>();
		for (int i = 0; i < array.size(); i++) {
			list.add(array.getJSONObject(i));
		}
		return list;
	}
}
