
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L120S18ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S18A;

/** 同一經濟關係人授信額度歸戶檔 **/
@Repository
public class L120S18ADaoImpl extends LMSJpaDao<L120S18A, String> implements
		L120S18ADao {

	@Override
	public L120S18A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S18A> findByMainId_orderBySeq(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//~~~
		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		
		List<L120S18A> list = createQuery(search).getResultList();		

		return list;
	}
	
	@Override
	public List<L120S18A> findByMainIdAndCustId(String mainId, String custId, String dupNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		//~~~
		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		
		List<L120S18A> list = createQuery(search).getResultList();		

		return list;
		
	}
	
	@Override
	public List<L120S18A> findByMainIdAndCustId2(String mainId, String custId2, String dupNo2){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId2", custId2);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", dupNo2);
		//~~~
		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		
		List<L120S18A> list = createQuery(search).getResultList();		

		return list;		
	}
}