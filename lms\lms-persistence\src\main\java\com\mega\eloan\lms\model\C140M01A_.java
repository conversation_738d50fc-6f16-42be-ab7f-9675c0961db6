package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.metamodel.ListAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.Meta_;

/**
 * <pre>
 * The persistent class for the C140M01A database table.
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/20,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140M01A.class)
public class C140M01A_ extends Meta_{
	public static volatile SingularAttribute<C140M01A, String> cesId;
	public static volatile SingularAttribute<C140M01A, String> cesMode;
	public static volatile SingularAttribute<C140M01A, String> ch9LlandInv1;
	public static volatile SingularAttribute<C140M01A, String> techSelect;
	public static volatile SingularAttribute<C140M01A, String> equipSelect;
	public static volatile SingularAttribute<C140M01A, String> chp12Select;
	public static volatile SingularAttribute<C140M01A, String> chp13Select;
	public static volatile SingularAttribute<C140M01A, String> chp5Select;
	public static volatile SingularAttribute<C140M01A, String> ch10Se4Select;
	public static volatile SingularAttribute<C140M01A, String> ch6LstockInv;
	public static volatile SingularAttribute<C140M01A, String> ch61Title;
	public static volatile SingularAttribute<C140M01A, String> ch62Title;
	public static volatile SingularAttribute<C140M01A, String> comId;
	public static volatile SingularAttribute<C140M01A, String> comId1;
	public static volatile SingularAttribute<C140M01A, String> edit;
	public static volatile SingularAttribute<C140M01A, String> editMode1;
	public static volatile SingularAttribute<C140M01A, String> editMode2;
	public static volatile SingularAttribute<C140M01A, String> editMode3;
	public static volatile SingularAttribute<C140M01A, String> toM1;
	public static volatile SingularAttribute<C140M01A, String> toM2;
	public static volatile SingularAttribute<C140M01A, String> toM3;
	public static volatile SingularAttribute<C140M01A, String> toM4;
	public static volatile SingularAttribute<C140M01A, String> toM5;
	public static volatile SingularAttribute<C140M01A, String> typem1;
	public static volatile SingularAttribute<C140M01A, String> typem2;
	public static volatile SingularAttribute<C140M01A, String> typem3;
	public static volatile SingularAttribute<C140M01A, String> typem4;
	public static volatile SingularAttribute<C140M01A, String> typem5;
	public static volatile SingularAttribute<C140M01A, String> sourceRt;
	public static volatile SingularAttribute<C140M01A, Date> cesFDate;
	public static volatile SingularAttribute<C140M01A, String> normal;
	public static volatile SingularAttribute<C140M01A, String> se3Select;
	public static volatile SingularAttribute<C140M01A, String> se4Select;
	public static volatile SingularAttribute<C140M01A, String> se5Select;
	public static volatile SingularAttribute<C140M01A, String> isGroupCompany1;
	public static volatile SingularAttribute<C140M01A, String> returnMode;
	public static volatile SingularAttribute<C140M01A, String> returnMainId;
//	public static volatile ListAttribute<C140M01A, C140A01A> c140a01as;
//	public static volatile ListAttribute<C140M01A, C140A01B> c140a01bs;
	public static volatile ListAttribute<C140M01A, C140JSON> c140jsons;
	public static volatile ListAttribute<C140M01A, C140M04A> c140m04as;
	public static volatile ListAttribute<C140M01A, C140M04B> c140m04bs;
	public static volatile ListAttribute<C140M01A, C140M07A> c140m07as;
	public static volatile ListAttribute<C140M01A, C140SDSC> c140sdscs;
//	public static volatile ListAttribute<C140M01A, C140S03A> c140s03as;
//	public static volatile ListAttribute<C140M01A, C140S03B> c140s03bs;
//	public static volatile ListAttribute<C140M01A, C140S03C> c140s03cs;
//	public static volatile ListAttribute<C140M01A, C140S03D> c140s03ds;
//	public static volatile ListAttribute<C140M01A, C140S03E> c140s03es;
//	public static volatile ListAttribute<C140M01A, C140S07B> c140s07bs;
//	public static volatile ListAttribute<C140M01A, C140S07C> c140s07cs;
//	public static volatile ListAttribute<C140M01A, C140S09A> c140s09as;
//	public static volatile ListAttribute<C140M01A, C140S09B> c140s09bs;
//	public static volatile ListAttribute<C140M01A, C140S09C> c140s09cs;
//	public static volatile ListAttribute<C140M01A, C140S09D> c140s09ds;
//	public static volatile ListAttribute<C140M01A, C140S09E> c140s09es;
//	public static volatile ListAttribute<C140M01A, C140S09E> c140s09fs;
	//public static volatile ListAttribute<C140M01A, C140SFFF> c140sfffs;	
	
}
