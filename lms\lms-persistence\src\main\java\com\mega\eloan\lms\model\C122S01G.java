/* 
 * C122S01G.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 不承作項目明系檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C122S01G", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C122S01G extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * uid<p/>
	 * not null (先放mainid，未來有複製功能的時候在改成複製的mainid)
	 */
	@Size(max=32)
	@Column(name="UID", length=32, columnDefinition="CHAR(32)")
	private String uid;

	/** 主文件狀態 **/
	@Size(max=3)
	@Column(name="DOCSTATUS", length=3, columnDefinition="VARCHAR(3)")
	private String docStatus;

	/** 項目代號 **/
	@Size(max=32)
	@Column(name="CODEVALUE", length=32, columnDefinition="VARCHAR(32)")
	private String codeValue;

	/** 項目說明 **/
	@Size(max=300)
	@Column(name="CODEDESC", length=300, columnDefinition="VARCHAR(300)")
	private String codeDesc;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得uid<p/>
	 * not null (先放mainid，未來有複製功能的時候在改成複製的mainid)
	 */
	public String getUid() {
		return this.uid;
	}
	/**
	 *  設定uid<p/>
	 *  not null (先放mainid，未來有複製功能的時候在改成複製的mainid)
	 **/
	public void setUid(String value) {
		this.uid = value;
	}

	/** 取得主文件狀態 **/
	public String getDocStatus() {
		return this.docStatus;
	}
	/** 設定主文件狀態 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}

	/** 取得項目代號 **/
	public String getCodeValue() {
		return this.codeValue;
	}
	/** 設定項目代號 **/
	public void setCodeValue(String value) {
		this.codeValue = value;
	}

	/** 取得項目說明 **/
	public String getCodeDesc() {
		return this.codeDesc;
	}
	/** 設定項目說明 **/
	public void setCodeDesc(String value) {
		this.codeDesc = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}
}
