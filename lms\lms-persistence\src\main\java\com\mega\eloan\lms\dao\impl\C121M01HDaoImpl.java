/* 
 * C121M01HDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.C121M01HDao;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01D;
import com.mega.eloan.lms.model.C121M01G;
import com.mega.eloan.lms.model.C121M01H;

/** 泰國消金評等表 **/
@Repository
public class C121M01HDaoImpl extends LMSJpaDao<C121M01H, String>
	implements C121M01HDao {

	@Override
	public C121M01H findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C121M01H> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C121M01H> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C121M01H> findByC121M01A(C121M01A meta) {
		return findByMainId(meta.getMainId());
	}
	
	@Override
	public C121M01H findByUk(String mainId, String custId, String dupNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		return findUniqueOrNone(search);
	}
	
	@Override
	public C121M01H findByC120M01A(C120M01A c120m01a){
		return findByUk(c120m01a.getMainId(), c120m01a.getCustId(), c120m01a.getDupNo());
	}

}