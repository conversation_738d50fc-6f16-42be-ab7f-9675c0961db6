/* 
 * LMS9515M01Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.form;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.BrNoTypeEnum;
import com.mega.eloan.common.gwclient.EloanBatchClient;
import com.mega.eloan.common.gwclient.EloanServerBatReqMessage;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.bean.ELF491C;
import com.mega.eloan.lms.mfaloan.service.MisELF491CService;
import com.mega.eloan.lms.model.C900M03A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180R02A;
import com.mega.eloan.lms.model.L784S01A;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.eloan.lms.model.LMSBATCH;
import com.mega.eloan.lms.model.LMSRPT;
import com.mega.eloan.lms.rpt.pages.LMS9511V01Page;
import com.mega.eloan.lms.rpt.pages.LMS9515V01Page;
import com.mega.eloan.lms.rpt.service.LMS9511Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.read.biff.BiffException;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;
import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 管理報表 - 國內
 * </pre>
 * 
 * @since 2013/01/03
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/03,Ice,new
 *          <li>2013/01/10,Vector,加入個金報表
 *          </ul>
 */

@Scope("request")
@Controller("lms9511m01formhandler")
public class LMS9511M01Formhandler extends AbstractFormHandler {
	// 復原TFS J-111-0636_05097_B100X
	protected final Logger logger = LoggerFactory.getLogger(getClass());

	@Resource
	EloanBatchClient eloanBatClient;

	@Resource
	CodeTypeService codetypeService;// com.bcodetype

	@Resource
	BranchService branchService;

	@Resource
	DocFileService fileService;

	@Resource
	LMS9511Service lms9511Service;

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	RetrialService retrialService;

	@Resource
	MisELF491CService misELF491CService;

	/**
	 * 搜尋分行
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBranch(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String areaBranchId = Util.trim(params.getString("areaBranchId"));
		Map<String, String> m = new TreeMap<String, String>();
		List<IBranch> bankList1 = branchService.getBranchOfGroup(areaBranchId);
		for (IBranch b : bankList1) {
			String brName = Util.trim(b.getBrName());
			String brCode = b.getBrNo();
			m.put(brCode, brCode + brName);
		}

		CapAjaxFormResult bankList2 = new CapAjaxFormResult(m);
		result.set("item", bankList2);
		return result;
	}

	/**
	 * 搜尋分行
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryApprove(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		List<Map<String, Object>> list = lms9511Service
				.findApproverForL180R02A(mainId);
		Map<String, String> approverMap = new TreeMap<String, String>();
		for (Map<String, Object> map : list) {
			String approver = Util.trim(map.get("APPROVER"));
			approverMap.put(approver, approver);
		}

		CapAjaxFormResult approverList2 = new CapAjaxFormResult(approverMap);
		result.set("approvers", approverList2);
		return result;
	}

	/**
	 * 搜尋 報表類型
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryReportType(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// String brNo = user.getUnitNo();
		// result.set("UNITTYPE", branchService.getBranch(brNo).getUnitType());
		// result.set("UNITNO", brNo);

		Date dataStartDate = LMSUtil.getExMonthFirstDay(-1);
		Date dataEndDate = LMSUtil.getExMonthLastDay(-1);
		result.set("DATASTARTDATE1", TWNDate.toAD(dataStartDate));
		result.set("DATASTARTDATE2", TWNDate.toAD(dataStartDate)
				.substring(0, 7));
		result.set("DATAENDDATE1", TWNDate.toAD(dataEndDate));
		result.set("DATAENDDATE2", TWNDate.toAD(dataEndDate).substring(0, 7));
		result.set("YEAR", CapDate.getCurrentDate("yyyy"));
		result.set("TODAYYEARMONTH", CapDate.getCurrentDate("yyyy-MM"));
		result.set("TODAY", CapDate.getCurrentDate("yyyy-MM-dd"));
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DATE, -1);
		result.set("YESDAY", TWNDate.toAD(cal.getTime()));
		dataStartDate = LMSUtil.getExMonthFirstDay(-2);
		result.set("DATASTARTDATE3", TWNDate.toAD(dataStartDate));
		dataStartDate = LMSUtil.getExMonthFirstDay(1);
		dataEndDate = LMSUtil.getExMonthLastDay(1);
		result.set("NEXTDATASTARTDATE1", TWNDate.toAD(dataStartDate));
		result.set("NEXTDATAENDDATE1", TWNDate.toAD(dataEndDate));
		cal = Calendar.getInstance();
		cal.add(Calendar.DATE, -1 * 7);
		cal.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
		result.set("DATAENDDATE4", TWNDate.toAD(cal.getTime()));
		cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
		result.set("DATASTARTDATE4", TWNDate.toAD(cal.getTime()));
		dataStartDate = LMSUtil.getExMonthFirstDay(-3);
		result.set("DATASTARTDATE5", TWNDate.toAD(dataStartDate)
				.substring(0, 7));
		cal = Calendar.getInstance();
		cal.add(Calendar.MONTH, -2);
		result.set("BEFORELASTTODAY", TWNDate.toAD(cal.getTime()));
		// J-107-0342_05097_B1003 Web e-Loan授信系統新增覆審考核相關報表
		dataStartDate = LMSUtil.getExMonthFirstDay(-12);
		result.set("DATASTARTDATE6", TWNDate.toAD(dataStartDate)
				.substring(0, 7));

		// J-109-0132_05097_B1001 e-Loan授信系統新增「法令遵循自評檢核表」之抽測筆數所需之各檢核項目授信案件明細報表。
		dataStartDate = LMSUtil.getExMonthFirstDay(-6);
		result.set("DATASTARTDATE7", TWNDate.toAD(dataStartDate)
				.substring(0, 7));

		// J-110-0049_05097_B1001 Web
		// e-Loan企金授信增加「境內法人於國際金融業務分行辦理外幣授信業務報表」
		String lms180R63_bgnDate = Util.trim(lmsService
				.getSysParamDataValue("LMS_LMS180R63_BGNDATE"));
		if (Util.notEquals(lms180R63_bgnDate, "")) {
			result.set("LMS180R63_BGNDATE", lms180R63_bgnDate);
		} else {
			result.set("LMS180R63_BGNDATE", "2020-01-01");
		}

		result.set("baseDate_CLS180R15D", CrsUtil.get_R95_1_baseDate(TWNDate
				.toAD(CapDate.getCurrentTimestamp())));

		// TreeMap排序
		// lms9511v01_docType1 管理報表種類-企金
		CapAjaxFormResult type = codetypeService
				.findByCodeTypeWithOrderBy("lms9511v01_docType1");
		if (type == null) {
			type = new CapAjaxFormResult();
		}
		if (true) {
			HashSet<String> allowLrsBrSet = new HashSet<String>();
			allowLrsBrSet.add(UtilConstants.BankNo.資訊處);
			// allowCrsBrSet.add(UtilConstants.BankNo.授管處);
			// ---
			allowLrsBrSet.add(UtilConstants.BankNo.國外部);
			allowLrsBrSet.add(UtilConstants.BankNo.金控總部分行);
			allowLrsBrSet.add(UtilConstants.BankNo.國金部);
			allowLrsBrSet.add(UtilConstants.BankNo.私銀處作業組);
			// ---
			allowLrsBrSet.add(UtilConstants.BankNo.北一區營運中心);
			allowLrsBrSet.add(UtilConstants.BankNo.北二區營運中心);
			allowLrsBrSet.add(UtilConstants.BankNo.桃竹苗區營運中心);
			allowLrsBrSet.add(UtilConstants.BankNo.中區營運中心);
			allowLrsBrSet.add(UtilConstants.BankNo.南區營運中心);

			if (allowLrsBrSet.contains(user.getUnitNo())) {
				// ok
			} else {
				type.removeField(UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單);
				type.removeField(UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單);
				type.removeField(UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表);
				;
				type.removeField(UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表);
				type.removeField(UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表);
				type.removeField(UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表);
			}
		}
		if (true) {
			Set<String> allowLMS180R28 = new HashSet<String>();
			allowLMS180R28.add(UtilConstants.BankNo.資訊處);
			allowLMS180R28.add(UtilConstants.BankNo.授管處);
			allowLMS180R28.add(UtilConstants.BankNo.北一區營運中心);
			allowLMS180R28.add(UtilConstants.BankNo.北二區營運中心);
			allowLMS180R28.add(UtilConstants.BankNo.桃竹苗區營運中心);
			allowLMS180R28.add(UtilConstants.BankNo.中區營運中心);
			allowLMS180R28.add(UtilConstants.BankNo.南區營運中心);
			if (allowLMS180R28.contains(user.getUnitNo())) {

			} else {
				type.removeField(UtilConstants.RPTREPORT.DOCTYPE1.覆審經理記點統計明細表);
			}
		}
        if (true) { //J-113-0237 配合消金處，於ELOAN端消金業務處及企金業務處新增青創追蹤報表
            Set<String> allowLMS180R77 = new HashSet<String>();
            allowLMS180R77.add(UtilConstants.BankNo.資訊處);
            allowLMS180R77.add(UtilConstants.BankNo.授信行銷處);
            if (!allowLMS180R77.contains(user.getUnitNo())) {
                type.removeField(UtilConstants.RPTREPORT.DOCTYPE1.青創追蹤報表);
            }
        }
		result.set("item", type);
		// Vector done
		// lms9511v01_docType2 管理報表種類-個金
		CapAjaxFormResult clsType = codetypeService
				.findByCodeTypeWithOrderBy("lms9511v01_docType2");
		if (clsType == null) {
			clsType = new CapAjaxFormResult();
		}

		{// 只有 900,918,007,201,[各營運中心] 可產生消金覆審的報表
			HashSet<String> allowCrsBrSet = new HashSet<String>();
			allowCrsBrSet.add(UtilConstants.BankNo.資訊處);
			// allowCrsBrSet.add(UtilConstants.BankNo.授管處);
			// ---
			allowCrsBrSet.add(UtilConstants.BankNo.國外部);
			allowCrsBrSet.add(UtilConstants.BankNo.金控總部分行);
			allowCrsBrSet.add(UtilConstants.BankNo.私銀處作業組);
			// ---
			allowCrsBrSet.add(UtilConstants.BankNo.北一區營運中心);
			allowCrsBrSet.add(UtilConstants.BankNo.北二區營運中心);
			allowCrsBrSet.add(UtilConstants.BankNo.桃竹苗區營運中心);
			allowCrsBrSet.add(UtilConstants.BankNo.中區營運中心);
			allowCrsBrSet.add(UtilConstants.BankNo.南區營運中心);

			if (allowCrsBrSet.contains(user.getUnitNo())) {
				// ok
			} else {
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表);
			}
		}

		if (true) { // J-107-0286 消金案件(當月、當年度累計)統計表
			// XXX 在 Formhandler 去控制[總處、營運中心、分行]能看到哪些報表, 不要在 LMS9511V01Page.js 寫
			HashSet<String> allowBrSet = new HashSet<String>();
			allowBrSet.add(UtilConstants.BankNo.資訊處);
			allowBrSet.add(UtilConstants.BankNo.授管處);
			allowBrSet.add(UtilConstants.BankNo.消金業務處);
			if (!allowBrSet.contains(user.getUnitNo())) {
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金處新做案件_當月_當年度累計_統計表);
			}
		}

		if (true) { // J-107-0354 防杜代辦-消金專案覆審機制
			HashSet<String> allowBrSet = new HashSet<String>();
			allowBrSet.add(UtilConstants.BankNo.資訊處);
			if (clsService.is_function_on_codetype("918_rm_CLS180R18")) {
				allowBrSet.remove(UtilConstants.BankNo.授管處);
			} else {
				allowBrSet.add(UtilConstants.BankNo.授管處);
			}

			for (String allowBrNo : CrsUtil.get_ELF490B_areaArr()) {
				allowBrSet.add(allowBrNo);
			}

			if (!allowBrSet.contains(user.getUnitNo())) {
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審名單);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審件數控管表);
			}
		}

		if (true) { // J-107-0355
			// 在 Formhandler 去控制[總處、營運中心、分行]能看到哪些報表, 不要在 LMS9511V01Page.js 寫
			HashSet<String> allowBrSet = new HashSet<String>();
			allowBrSet.add(UtilConstants.BankNo.資訊處);
			allowBrSet.add(UtilConstants.BankNo.消金業務處);
			if (!allowBrSet.contains(user.getUnitNo())) {
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金整批房貸消貸敘做明細);
			}
		}

		if (true) { // J-108-0046
			// 在 Formhandler 去控制[總處、營運中心、分行]能看到哪些報表, 不要在 LMS9511V01Page.js 寫
			HashSet<String> allowBrSet = new HashSet<String>();
			allowBrSet.add(UtilConstants.BankNo.資訊處);
			allowBrSet.add(UtilConstants.BankNo.授管處);
			allowBrSet.add(UtilConstants.BankNo.消金業務處);
			if (!allowBrSet.contains(user.getUnitNo())) {
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金線上貸款餘額表);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.台電員工貸款明細表);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金自住型房貸成長方案統計報表);
			}
		}

		if (true) { // J-109-0090
			// HashSet<String> allowBrSet = new HashSet<String>();
			// allowBrSet.add(UtilConstants.BankNo.資訊處);
			// allowBrSet.add(UtilConstants.BankNo.授管處);
			// allowBrSet.add(UtilConstants.BankNo.消金業務處);
			// allowBrSet.addAll(clsService.getCardLoanBrNo());
			// if (!allowBrSet.contains(user.getUnitNo())) {
			// clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金線上信貸申貸清單);
			// }
			if (true) {
				boolean isOverSea = BrNoTypeEnum.國外.getCode()
						.equals(branchService.getBranch(user.getUnitNo())
								.getBrNoFlag());
				if (isOverSea) { // 海外
					clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金線上信貸申貸清單);
				}
			}
		}

		if (clsService.is_function_on_codetype("RPTNO_CLS180R21A_B_C")) { // 消金借款人留存同一通訊處[未註記清單,已註記清單,全部清單]
			if (true) {
				if (user.getUnitNo().startsWith("9")) { // 9開頭的總處單位
					// ==========
					HashSet<String> allowBrSet = new HashSet<String>();
					allowBrSet.add(UtilConstants.BankNo.資訊處);
					allowBrSet.add(UtilConstants.BankNo.授管處);
					allowBrSet.add(UtilConstants.BankNo.消金業務處);
					if (allowBrSet.contains(user.getUnitNo())) {

					} else {
						clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處註記清單);
					}
				}
			}
			if (true) {
				boolean isOverSea = BrNoTypeEnum.國外.getCode()
						.equals(branchService.getBranch(user.getUnitNo())
								.getBrNoFlag());
				if (isOverSea) { // 海外
					clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處註記清單);
				}
			}
			if (true) {
				// 國內分行
			}
		} else {
			HashSet<String> allowBrSet = new HashSet<String>();
			allowBrSet.add(UtilConstants.BankNo.資訊處);
			allowBrSet.add(UtilConstants.BankNo.授管處);
			allowBrSet.add(UtilConstants.BankNo.消金業務處);
			if (allowBrSet.contains(user.getSsoUnitNo())) { // 總處可登入各分行, 檢視清單

			} else {
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處註記清單);
			}
		}

		if (clsService.is_function_on_codetype("RPTNO_CLS180R22")) {
			if (true) {
				if (user.getUnitNo().startsWith("9")) { // 9開頭的總處單位
					// ==========
					HashSet<String> allowBrSet = new HashSet<String>();
					allowBrSet.add(UtilConstants.BankNo.資訊處);
					allowBrSet.add(UtilConstants.BankNo.授管處);
					allowBrSet.add(UtilConstants.BankNo.消金業務處);
					if (allowBrSet.contains(user.getUnitNo())) {

					} else {
						clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單);
						clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單);
						clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單);
					}
				}
			}
			if (true) {
				boolean isOverSea = BrNoTypeEnum.國外.getCode()
						.equals(branchService.getBranch(user.getUnitNo())
								.getBrNoFlag());
				if (isOverSea) { // 海外
					clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單);
					clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單);
					clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單);
				}
			}
			if (true) {
				// 國內分行
			}
		} else {
			HashSet<String> allowBrSet = new HashSet<String>();
			allowBrSet.add(UtilConstants.BankNo.資訊處);
			allowBrSet.add(UtilConstants.BankNo.授管處);
			allowBrSet.add(UtilConstants.BankNo.消金業務處);
			if (allowBrSet.contains(user.getSsoUnitNo())) { // 總處可登入各分行, 檢視清單

			} else {
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單);
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單);
			}
		}

		if (true) { //
			HashSet<String> allowBrSet = new HashSet<String>();
			allowBrSet.add(UtilConstants.BankNo.資訊處);
			allowBrSet.add(UtilConstants.BankNo.稽核處);
			if (!allowBrSet.contains(user.getUnitNo())) {
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報_稽核處);
			}
		}

		if (true) { // J-111-0178 客戶訪談紀錄表
			boolean isOverSea = BrNoTypeEnum.國外.getCode().equals(
					branchService.getBranch(user.getUnitNo()).getBrNoFlag());
			if (isOverSea) { // 海外
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.客戶訪談紀錄表);
			}
		}

		if (true) { // J-112-0399 分行承作購置住宅貸款年限40年統計表
			// 雪菱確認只有消金可以查
			HashSet<String> allowBrSet = new HashSet<String>();
			allowBrSet.add(UtilConstants.BankNo.資訊處);
			allowBrSet.add(UtilConstants.BankNo.消金業務處);
			if (!allowBrSet.contains(user.getUnitNo())) {
				clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE2.分行承作購置住宅貸款年限逾30年統計表);
			}
		}
        if(true) { // J-113-0237 配合消金處，於ELOAN端消金業務處及企金業務處新增青創追蹤報表
            Set<String> allowCLS180R62 = new HashSet<String>();
            allowCLS180R62.add(UtilConstants.BankNo.資訊處);
            allowCLS180R62.add(UtilConstants.BankNo.消金業務處);
            if (!allowCLS180R62.contains(user.getUnitNo())) {
                clsType.removeField(UtilConstants.RPTREPORT.DOCTYPE1.青創追蹤報表);
            }
        }
		result.set("clsItem", clsType);

		CapAjaxFormResult prod = lms9511Service.getProduct();
		if (prod == null) {
			prod = new CapAjaxFormResult();
		}
		result.set("prodItem", prod);

		// J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
		CapAjaxFormResult brStaffNo = lms9511Service.getStaffNo(user
				.getUnitNo());
		if (brStaffNo == null) {
			brStaffNo = new CapAjaxFormResult();
		}
		result.set("brStaffNo", brStaffNo);

		{// 加入「覆審」可選擇分行
			TreeMap<String, String> tm = retrialService.getBranch(user
					.getUnitNo());
			result.set("branchItem", new CapAjaxFormResult(tm));
			result.set("branchItemOrder", new ArrayList<String>(tm.keySet()));
		}

		// J-113-0125_05097_B1001
		// 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
		String LMS_LMS180R74_PROPERTY = Util.trim(lmsService
				.getSysParamDataValue("LMS_LMS180R74_PROPERTY"));
		if (Util.notEquals(LMS_LMS180R74_PROPERTY, "")) {
			result.set("proPerty_LMS180R74", LMS_LMS180R74_PROPERTY);
		}

		return result;
	}

	/**
	 * <pre>
	 * 檢查是否可以跑報表批次
	 * 
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws Exception 
	 * @throws URISyntaxException 
	 * @throws WriteException 
	 * @throws BiffException
	 * @throws RowsExceededException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkBatchData(PageParameters params)
			throws RowsExceededException, BiffException, WriteException,
			URISyntaxException, Exception {
		CapWebUtil.showParams(params);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String rptNo = Util.trim(params.getString("rptNo"));// 報表代碼-企金
		String dataStartDate = Util.trim(params.getString("dataStartDate"));// 起日
		String dataEndDate = Util.trim(params.getString("dataEndDate"));// 迄日
		String remark = Util.trim(params.getString("remark"));
		Date bngDate = null;
		Date endDate = null;
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		bngDate = lms9511Service.getStartDateForLMSRPT(dataStartDate);
		endDate = lms9511Service.getEndDateForLMSRPT(dataEndDate);
		result = lms9511Service.checkBatchData(result, rptNo, remark, bngDate,
				endDate, prop);
		return result;
	}

	/**
	 * <pre>
	 * 啟動報表批次
	 * 
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws Exception 
	 * @throws URISyntaxException 
	 * @throws WriteException 
	 * @throws BiffException
	 * @throws RowsExceededException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult callBatch(PageParameters params)
			throws Exception {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String rptNo = Util.trim(params.getString("rptNo"));// 報表代碼-企金
		String dataStartDate = Util.trim(params.getString("dataStartDate"));// 起始時間
		String dataEndDate = Util.trim(params.getString("dataEndDate"));// 結束時間

		// J-105-0065-001 Web e-Loan 授信管理系統修改異常通報副知風控處說明文字及新增統計報表
		// String remark = Util.trim(params.getString("remark"));
		// String selectBrNo = "";
		// String custId = "";

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, String> LMSBatchMap = codetypeService
				.findByCodeType("LMSBatch");
		if (LMSBatchMap == null) {
			LMSBatchMap = new LinkedHashMap<String, String>();
		}
		/*
		 * //直接線上呼叫BATCH執行 EloanSubsysBatReqMessage reqs = new
		 * EloanSubsysBatReqMessage(); reqs.setUrl("SYS_URL_LMS");
		 * //"http://127.0.0.1:8080/lms-web"
		 * 
		 * reqs.setLocalUrl(true);
		 * reqs.setReqFormat(EloanSubsysBatReqMessage.REQ_FMT_JSON);
		 * reqs.setServiceId("RptLMSBatch2ServiceImpl"); reqs.setTimeout(1200);
		 * 
		 * JSONObject requestJSON = new JSONObject();
		 * requestJSON.element("rptNo", rptNo); requestJSON.element("docType",
		 * "1"); requestJSON.element("unitNo", user.getUnitNo());
		 * requestJSON.element("userId", user.getUserId());
		 * requestJSON.element("dataStartDate", dataStartDate);
		 * requestJSON.element("dataEndDate", dataEndDate);
		 * reqs.setRequestJSON(requestJSON); eloanBatClient.send(reqs);
		 */

		// 線上啟動排程執行
		EloanServerBatReqMessage req = new EloanServerBatReqMessage();
		req.setUserId(user.getUserId());
		req.setRunType(EloanServerBatReqMessage.RUN_TYPE_QUEUE);
		logger.info(LMSBatchMap.get("8"));
		logger.info(LMSBatchMap.get("9"));
		logger.info(LMSBatchMap.get("10"));
		logger.info(LMSBatchMap.get("15"));
		logger.info(LMSBatchMap.get("35"));
		logger.info(LMSBatchMap.get("38"));
		logger.info(LMSBatchMap.get("55"));
		logger.info(LMSBatchMap.get("57"));
		logger.info(LMSBatchMap.get("58"));
		logger.info(LMSBatchMap.get("59"));
		logger.info(LMSBatchMap.get("62"));
		if (rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信案件統計表)) {
			req.setSchId(LMSBatchMap.get("8"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.金融機構辦理振興經濟非中小企業專案貸款)) {
			req.setSchId(LMSBatchMap.get("9"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企業自行申請展延案件案件統計表)) {
			req.setSchId(LMSBatchMap.get("10"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.分行授信淨增加額度統計表)) {
			req.setSchId(LMSBatchMap.get("15"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報)) {
			req.setSchId(LMSBatchMap.get("27"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.振興經濟非中小企業專案貸款暨信用保證要點執行情形調查表)) {
			req.setSchId(LMSBatchMap.get("35"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表)) {
			req.setSchId(LMSBatchMap.get("38"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金聯貸新作案件統計表)) {
			req.setSchId(LMSBatchMap.get("55"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授信案件敘做情形及比較表)) {
			req.setSchId(LMSBatchMap.get("57"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表)) {
			req.setSchId(LMSBatchMap.get("58"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金已核准授信額度辦理狀態通報彙總表)) {
			req.setSchId(LMSBatchMap.get("59"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金共同行銷報表)) {
			req.setSchId(LMSBatchMap.get("62"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.簽報階段都更危老業務統計表)) {
			req.setSchId(LMSBatchMap.get("67")); // SLMS-00067
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.法令遵循自評授信案件明細報表)) {
			req.setSchId(LMSBatchMap.get("94"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案辦理情形總表)) {
			req.setSchId(LMSBatchMap.get("96"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人授信異常通報表)) {
			// J-109-0315_05097_B1001 Web
			// e-loan企金授信新增小規模營業人央行C方案授信案件之異常通報上一個月獲核定之異動名單
			req.setSchId(LMSBatchMap.get("98"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表)) {
			// J-110-0049_05097_B1001 Web e-Loan企金授信增加「境內法人於國際金融業務分行辦理外幣授信業務報表」
			req.setSchId(LMSBatchMap.get("100"));
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授信簽報案件明細檔)) {
			// J-112-0342 新增產生企金授信簽報案件明細檔
			req.setSchId(LMSBatchMap.get("101"));
		} else {

		}

		JSONObject paraJson = new JSONObject();
		paraJson.put("rptNo", rptNo);
		paraJson.put("docType", "1");
		paraJson.put("unitNo", user.getUnitNo());
		paraJson.put("userId", user.getUserId());
		paraJson.put("dataStartDate", dataStartDate);
		paraJson.put("dataEndDate", dataEndDate);

		// paraJson.put("remark", remark);

		StringBuffer batchParams = new StringBuffer();
		batchParams.append("REQUEST=").append(paraJson.toString());

		// req.setParams("rptNo=" + rptNo + ",docType=1,unitNo=" +
		// user.getUnitNo() + ",userId=" + user.getUserId() + ",dataStartDate="
		// + dataStartDate + ",dataEndDate=" + dataEndDate);

		// req.setParams(reqJson.toString());

		req.setParams(batchParams.toString());

		req.setDupeId("119");
		// eloanBatClient.setRserviceClient(rserviceClient);
		eloanBatClient.send(req);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, "已加入排程");
		return result;
	}

	/**
	 * <pre>
	 * 新增報表批次  select * from LMS.LMSRPT where rptno='XXX' order by updateTime desc
	 * 
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws Exception 
	 * @throws URISyntaxException 
	 * @throws WriteException 
	 * @throws BiffException
	 * @throws RowsExceededException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult addBatchData(PageParameters params)
			throws RowsExceededException, BiffException, WriteException,
			URISyntaxException, Exception {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapWebUtil.showParams(params);
		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean execResult = false;
		boolean fileResult = false;
		LMSBATCH batch = null;
		String mainId = null;
		// String docType = Util.trim(params.getString("docType"));// 報表類別
		String rptNo = Util.trim(params.getString("rptNo"));// 報表代碼-企金
		String dataStartDate = Util.trim(params.getString("dataStartDate"));// 起日
		String dataEndDate = Util.trim(params.getString("dataEndDate"));// 迄日
		String remark = Util.trim(params.getString("remark"));
		Date bngDate = null;
		Date endDate = null;
		Map<String, String> clsRptName = codetypeService
				.findByCodeType("lms9511v01_docType2");
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		/*
		 * 若傳入的 dataEndDate 2014-05 dataStartDate 2014-05
		 * 
		 * 經過 lms9511Service.getStartDateForLMSRPT,
		 * lms9511Service.getEndDateForLMSRPT bngDate = 2014-05-01 endDate =
		 * 2014-05-31
		 */
		bngDate = lms9511Service.getStartDateForLMSRPT(dataStartDate);
		endDate = lms9511Service.getEndDateForLMSRPT(dataEndDate);
		String sysDate = TWNDate.toAD(CapDate.getCurrentTimestamp());
		if (rptNo.startsWith(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料)) {
			Map<String, String> remarkMap = CrsUtil.parseRptRemark(remark);
			String dt = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_DT));
			String mCnt = Util.trim(remarkMap.get(CrsUtil.RPT_REMARK_MCNT));

			if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
					rptNo)) {
				bngDate = CapDate.parseDate("1911-01-01");
				endDate = CapDate.shiftDays(
						CapDate.parseDate(StringUtils.substring(sysDate, 0, 7)
								+ "-01"), -1);
			} else if (Util.equals(
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂, rptNo)) {
				bngDate = CapDate.parseDate("1911-01-01");
				endDate = CapDate.parseDate(CrsUtil.getDataEndDate(dt));
			} else if (Util.equals(
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起, rptNo)) {
				bngDate = CapDate.parseDate((StringUtils.substring(sysDate, 0,
						7) + "-01"));
				String targetyyyy_MM = StringUtils.substring(
						TWNDate.toAD(CapDate.addMonth(bngDate,
								Util.parseInt(mCnt))), 0, 7);
				endDate = CapDate.parseDate(CrsUtil
						.getDataEndDate(targetyyyy_MM));
			} else if (Util.equals(
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限, rptNo)) {
				bngDate = CapDate.parseDate("1911-01-01");
				endDate = CapDate.parseDate(sysDate);
			}
		} else if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
				rptNo)) {
			String lastYear = String.valueOf(Util.parseInt(StringUtils
					.substring(sysDate, 0, 4)) - 1);
			bngDate = CapDate.parseDate(lastYear + "-01-01");
			endDate = CapDate.parseDate(lastYear + "-12-31");
		} else if (Util.equals(
				UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表, rptNo)) {
			String lastYear = String.valueOf(Util.parseInt(StringUtils
					.substring(sysDate, 0, 4)) - 1);
			bngDate = CapDate.parseDate(lastYear + "-01-01");
			endDate = CapDate.parseDate(lastYear + "-12-31");
		} else if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
				rptNo)) {
			String lastYear = String.valueOf(Util.parseInt(StringUtils
					.substring(sysDate, 0, 4)) - 1);
			bngDate = CapDate.parseDate(lastYear + "-01-01");
			endDate = CapDate.parseDate(lastYear + "-12-31");
		} else if (Util.equals(
				UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表, rptNo)) {
			String thisYear = String.valueOf(Util.parseInt(StringUtils
					.substring(sysDate, 0, 4)));
			bngDate = CapDate.parseDate(thisYear + "-01-01");
			endDate = CapDate.parseDate(thisYear + "-12-31");
		} else if (Util.equals(
				UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表, rptNo)) {
			bngDate = endDate;
			if (dataEndDate.endsWith("-06-30")) {
				List<ELF491C> elf491c_list = misELF491CService.find(
						CrsUtil.DONE_95_1_BR, CrsUtil.DONE_95_1_CUSTID,
						CrsUtil.DONE_95_1_DUPNO, CrsUtil.DONE_95_1_RULE_NO,
						TWNDate.toAD(endDate));
				if (elf491c_list.size() == 0) {
					throw new CapMessageException("疑似人頭戶追蹤對象逐年專案查核「年度基準日= "
							+ dataEndDate + "」查無資料", getClass());

				}
			} else {
				throw new CapMessageException(
						"疑似人頭戶追蹤對象逐年專案查核，以每年6月底為年度基準日。年度基準日的「月日」限制必須為06-30。",
						getClass());
			}
		} else if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
				rptNo)) {
			endDate = CapDate.parseDate(sysDate);
		} else if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
				rptNo)) {
			endDate = CapDate.parseDate(sysDate);
		} else if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審名單,
				rptNo)
				|| Util.equals(
						UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審件數控管表, rptNo)) {
			/*
			 * dataEndDate 2018-06 dataStartDate 2018-06 => 都轉為 2018-06-30
			 */
			bngDate = endDate;
			if (dataEndDate.endsWith("06") || dataEndDate.endsWith("12")) {
				if (LMSUtil.cmp_yyyyMM(endDate, ">=",
						CapDate.getCurrentTimestamp())) {
					if (dataEndDate.equals("2019-06")) {
						// 2019-07開始防杜代辦覆審, 可能2019-06就要開始準備工作
					} else {
						throw new CapMessageException("輸入資料 " + dataEndDate
								+ " 應小於目前年月", getClass());
					}
				}
			} else {
				String str_title = "消金防杜代辦專案覆審名單";
				if (Util.equals(
						UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審件數控管表, rptNo)) {
					str_title = "消金防杜代辦專案覆審件數控管表";
				}
				throw new CapMessageException(
						str_title
								+ "，資料年月的月份限制為06或12。"
								+ "<br/>"
								+ "如要檢視「覆審基準期間」 前一年度下半年(例：2018-07 ~ 2018-12)資料，請輸入2018-12。"
								+ "<br/>"
								+ "如要檢視「覆審基準期間」 　當年度上半年(例：2019-01 ~ 2019-06)資料，請輸入2019-06。",
						getClass());
			}
		} else if (Util.equals(
				UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單, rptNo)) {
			// 若在每月2號, DW仍未產出前1個月的比對結果
			// 仍然要讓分行能產出「未註記清單」
		} else if (Util.equals(
				UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單, rptNo)
				|| Util.equals(
						UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
						rptNo)) {
			// 可輸入 2018-09 ~ 2019-02

			Map<String, Object> latest_done_map = dwdbBASEService
					.findLnCustRel_done_history(CrsUtil.FN_KEY_C900S03C_DONE_CUST_KEY);
			Date done_cyc_mn = (Date) MapUtils.getObject(latest_done_map,
					"CYC_MN");

			if (MapUtils.isEmpty(latest_done_map) || done_cyc_mn == null) {
				// 從未產出
				throw new CapMessageException("e-Loan批次尚未產生同一通訊處註記清單。請稍後執行。",
						getClass());
			} else {
				if (LMSUtil.cmp_yyyyMM(done_cyc_mn, "<", endDate)) {
					// ok
					String show_cyc_mn_YYYYMM = StringUtils.substring(
							TWNDate.toAD(done_cyc_mn), 0, 7);
					throw new CapMessageException("資料倉儲最新的同一通訊處資料年月為["
							+ show_cyc_mn_YYYYMM + "]，資料區間請勿超過["
							+ show_cyc_mn_YYYYMM + "]", getClass());
				}

				if (true) {
					// 檢核 C900S02E 控制檔是否已產生
					String cyc_mn = StringUtils.substring(
							Util.trim(TWNDate.toAD(endDate)), 0, 7)
							+ "-01";
					String fn = CrsUtil.FN_KEY_C900S02E;
					String genDate = StringUtils.replace(cyc_mn, "-", "");
					String genTime = "000000";
					C900M03A m03a_for_C900S02E = clsService
							.findC900M03A_fnGenDateGenTime(fn, genDate, genTime);
					if (m03a_for_C900S02E == null) {
						throw new CapMessageException("e-Loan批次尚未產生"
								+ StringUtils.substring(cyc_mn, 0, 7)
								+ "同一通訊處註記清單。請稍後執行。", getClass());
					}
				}
			}

		} else if (Util.equals(
				UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單, rptNo)
				|| Util.equals(
						UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單,
						rptNo)
				|| Util.equals(
						UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單,
						rptNo)) {
			String cyc_mn = TWNDate.toAD(bngDate); // 　cyc_mn = ？
			String fn = CrsUtil.FN_KEY_C900S03C;
			String genDate = StringUtils.replace(cyc_mn, "-", "");
			String genTime = "000000";

			C900M03A m03a_for_C900S03C = clsService
					.findC900M03A_fnGenDateGenTime(fn, genDate, genTime);
			if (m03a_for_C900S03C == null) {
				boolean isFinish = dwdbBASEService.isLnCustRel_done(cyc_mn,
						CrsUtil.FN_KEY_C900S03C_DONE_CUST_KEY);
				/*
				 * DB中的 cyc_mn = 2019-01-01 表示在2月份, 已產生1月底的資料
				 */
				String show_cyc_mn_YYYYMM = StringUtils.substring(cyc_mn, 0, 7);
				if (isFinish) {
					throw new CapMessageException("資料倉儲已產生["
							+ show_cyc_mn_YYYYMM
							+ "]同一通訊處比對資料，e-Loan批次尚未引入比對資料。請稍後執行。", getClass());
				} else {
					throw new CapMessageException("資料倉儲尚未產生["
							+ show_cyc_mn_YYYYMM + "]同一通訊處比對資料。請稍後執行。",
							getClass());
				}
			}
		}
		result = lms9511Service.checkBatchData(result, rptNo, remark, bngDate,
				endDate, prop);
		if ("N".equals(Util.trim(result.get("SUCCESS")))) {
			return result;
		}

		String promptMsg = "";
		if (true) {
			if (Util.equals(rptNo,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單)) {
				String cyc_mn = StringUtils.substring(
						Util.trim(TWNDate.toAD(CapDate.addMonth(
								CrsUtil.get_sysMonth_1st(), -1))), 0, 7)
						+ "-01";
				String fn = CrsUtil.FN_KEY_C900S02E;
				String genDate = StringUtils.replace(cyc_mn, "-", "");
				String genTime = "000000";
				C900M03A m03a_for_C900S02E = clsService
						.findC900M03A_fnGenDateGenTime(fn, genDate, genTime);
				if (m03a_for_C900S02E == null) {
					promptMsg = "e-Loan批次尚未產生"
							+ StringUtils.substring(cyc_mn, 0, 7)
							+ "同一通訊處註記清單。產出的未註記清單資料範圍【尚未包含"
							+ StringUtils.substring(cyc_mn, 0, 7) + "】";
				}
			}
		}

		// ===============================
		// 於特定時段，暫不產出企金覆審報表
		if (true) {
			Set<String> lrsPauseRpt = new HashSet<String>();
			lrsPauseRpt.add(UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單);
			lrsPauseRpt.add(UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單);
			lrsPauseRpt.add(UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表);
			;
			lrsPauseRpt.add(UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表);
			// 可由 900 人員登入 931 去產生
			if (Util.notEquals("900", user.getSsoUnitNo())
					&& lrsPauseRpt.contains(rptNo)) {
				String errMsg = retrialService.lrsInExePeriod();
				if (Util.isNotEmpty(errMsg)) {
					throw new CapMessageException(errMsg, getClass());
				}
			}
		}

		if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.消金處新做案件_當月_當年度累計_統計表,
				rptNo)) {
			lms9511Service.sync_CLS180R17_data(endDate);
		}

		String lmsbatch_branch = user.getUnitNo();

		// 確認是否有重複日期的報表
		// 將LMSRPT(歷史檔 )的該筆資料複製回LMSBATCH 再將LMSRPT(歷史檔 )資料刪除 並且將相關的子TABLE資料移除
		mainId = lms9511Service.checkAndReplaceBatchData(getClass()
				.getSimpleName(), remark, lmsbatch_branch, user.getUserId(),
				rptNo, bngDate, endDate);

		// 新增報表
		batch = lms9511Service.addbatchData(remark, lmsbatch_branch, rptNo,
				bngDate, endDate, mainId);
		if (batch != null) {
			boolean checkResult = false;
			if (Util.isNotEmpty(mainId)) {
				checkResult = true;
			}
			execResult = lms9511Service.execBatchData(batch.getMainId(),
					checkResult);
			if (execResult) {
				fileResult = lms9511Service.createFileForAddBatch(batch,
						clsRptName, ""); // 產生檔案
				if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.常董會報告事項彙總及申報案件數統計表)) {
					List<LMSRPT> lmsRptList = lms9511Service
							.findLMSRptForRPTNO(rptNo);
					int year = 0;
					String mainIdR16 = null;
					for (LMSRPT lmsRpt : lmsRptList) {
						int year2 = Integer.parseInt(TWNDate.toAD(
								lmsRpt.getDataDate()).split("-")[0]);
						if (year2 > year) {
							year = year2;
							mainIdR16 = lmsRpt.getMainId();
						}
					}
					for (LMSRPT lmsRpt : lmsRptList) {
						if (Util.trim(mainIdR16).equals(lmsRpt.getMainId())) {
							lmsRpt.setNowRpt("Y");
						} else {
							lmsRpt.setNowRpt("N");
						}
					}
					lms9511Service.saveLmsRptList(lmsRptList);
				}
			}
		}
		if (fileResult) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.新增成功));
		}
		result.set("rptNo", batch.getRptNo());
		result.set("dataDate", TWNDate.toAD(batch.getDataDate())
				.substring(0, 7));
		result.set("brNo", batch.getBranch());
		result.set(EloanConstants.MAIN_ID, batch.getMainId());
		if (Util.isNotEmpty(promptMsg)) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, promptMsg);
		}
		/*
		 * // 印出新增成功訊息!
		 * result.set(CapConstants.AJAX_NOTIFY_MESSAGE,RespMsgHelper
		 * .getMainMessage(this.getComponent(),
		 * SystemConstant.AJAX_RSP_MSG.新增成功));
		 */
		return result;
	}

	/**
	 * 傳送授管處
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult sendDocTypeReport(PageParameters params)
			throws CapException {

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		LMSRPT lmsRpt = null;
		int check = 0;
		lmsRpt = lms9511Service.findModelByMainId(LMSRPT.class, mainId);
		check = lms9511Service.cheaksendTime(lmsRpt);
		if (check == 1) {
			// 此報表已傳送過
			result.set("sendTime", true);
			result.set("execSendTime", true);
		} else if (check == 2) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.報表無資料));
		} else {

			result.set("execSendTime", true);
			// 傳送成功
			if (!UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單.equals(lmsRpt
					.getRptNo())) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			} else {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						prop.getProperty("responseData.number01"));
			}
		}

		return result;
	}

	/**
	 * 2.已敘做授信案件清單-授管處儲存資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveLogError(PageParameters params)
			throws CapException {
		String[] oids = params.getStringArray("oids");
		CapAjaxFormResult result = new CapAjaxFormResult();
		L180R02A l180r02a = null;
		List<L180R02A> l180r02aList = new LinkedList<L180R02A>();
		if (oids.length > 0) {
			for (String oid : oids) {
				l180r02a = lms9511Service.findModelByOid(L180R02A.class, oid);
				if (l180r02a != null) {
					l180r02a.setAudit("Y");
					l180r02a.setHqCheckDate(null);
					l180r02a.setHqCheckMemo(null);
					l180r02aList.add(l180r02a);
				}
			}
		}
		if (l180r02aList.size() > 0) {
			lms9511Service.savel180r02aList(l180r02aList);
		}
		// EFD0017 儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * 2.已敘做授信案件清單-授管處儲存資料&個金報表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveLogError2(PageParameters params)
			throws CapException {
		String mainIdTemp = params.getString("mainIdTemp");
		CapAjaxFormResult result = new CapAjaxFormResult();
		LMSRPT lmsRpt = lms9511Service.findByLMSRPT(mainIdTemp);
		if (lmsRpt != null) {
			if ("N".equals(lmsRpt.getCfrmFlag())) {
				lmsRpt.setCfrmFlag("");
			} else {
				lmsRpt.setCfrmFlag("N");
			}
			lms9511Service.save(lmsRpt);
		}
		// EFD0017 儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * 取消報送
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult dissend(PageParameters params)
			throws CapException {
		String mainId = params.getString("mainIdTmp");
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<LMSRPT> lmsRpt = (List<LMSRPT>) lms9511Service.findListByMainId(
				LMSRPT.class, mainId);
		for (LMSRPT record : lmsRpt) {
			if (record.getBranch().equals(UtilConstants.BankNo.授管處)) {
				lms9511Service.delete(record);
			} else {
				record.setSender(null);
				record.setSendTime(null);
				lms9511Service.save(record);
			}
		}

		return result;
	}

	/**
	 * 核准
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public IResult clsAccept(PageParameters params)
			throws CapException {
		String mainId = params.getString("mainIdTmp");
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<LMSRPT> lmsRpt = (List<LMSRPT>) lms9511Service.findListByMainId(
				LMSRPT.class, mainId);
		for (LMSRPT record : lmsRpt) {
			record.setCfrmFlag("Y");
			record.setCfrmTime(CapDate.getCurrentTimestamp());
			lms9511Service.save(record);
		}
		return result;
	}

	/**
	 * 核准未通過
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public IResult clsUnaccept(PageParameters params)
			throws CapException {
		String mainId = params.getString("mainIdTmp");
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<LMSRPT> lmsRpt = (List<LMSRPT>) lms9511Service.findListByMainId(
				LMSRPT.class, mainId);
		for (LMSRPT record : lmsRpt) {
			record.setCfrmFlag("N");
			record.setCfrmTime(null);
			lms9511Service.save(record);
		}
		return result;
	}

	/**
	 * 2.已敘做授信案件清單-授管處儲存資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult remarkMemo(PageParameters params)
			throws CapException {
		String[] oids = params.getStringArray("oids");
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<LMSRPT> lmsRptList = new LinkedList<LMSRPT>();
		List<L180R02A> l180r02aList = null;
		List<L180R02A> l180r02aList2 = new LinkedList<L180R02A>();
		LMSRPT lmsRpt = null;
		if (oids.length > 0) {
			for (String oid : oids) {
				lmsRpt = lms9511Service.findModelByOid(LMSRPT.class, oid);
				if (lmsRpt != null) {
					l180r02aList = lms9511Service
							.findL180R02AListByMainId(lmsRpt.getMainId());
					for (GenericBean l180r02a : l180r02aList) {
						l180r02a.set("hqCheckDate",
								CapDate.getCurrentTimestamp());
					}
					l180r02aList2.addAll(l180r02aList);
					lmsRpt.setCfrmTime(CapDate.getCurrentTimestamp());
					lmsRpt.setCfrmFlag("Y");
					lmsRptList.add(lmsRpt);
				}
			}
		}
		if (lmsRptList.size() > 0) {
			lms9511Service.saveLmsRptList(lmsRptList);
			lms9511Service.savel180r02aList(l180r02aList2);
		}
		// EFD0017 儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 2.已敘做授信案件清單(搜尋此筆資料)
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryLMSRPT(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		String mainId = params.getString("mainIdTemp");
		LMSRPT lmsRpt = lms9511Service.findModelByMainId(LMSRPT.class, mainId);
		if (lmsRpt != null) {
			result.set("hqCheckDate", TWNDate.toAD(lmsRpt.getCfrmTime()));
			result.set("hqCheckMemo", pop.getProperty("hqCheckMemoDefault"));
		}
		return result;
	}

	/**
	 * 2.已敘做授信案件清單-授管處儲存資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveL180R02ARemarkNotes(PageParameters params) throws CapException {
		String[] oids = params.getStringArray("oids");
		CapAjaxFormResult result = new CapAjaxFormResult();
		L180R02A l180r02a = null;
		List<L180R02A> l180r02aList = new LinkedList<L180R02A>();
		String lms9511v01From2 = params.getString("lms9511v01From2");
		JSONObject jobject2 = JSONObject.fromObject(lms9511v01From2);
		String hqCheckMemo = jobject2.getString("hqCheckMemo");
		String hqCheckDate = jobject2.getString("hqCheckDate");
		for (String oid : oids) {
			l180r02a = lms9511Service.findModelByOid(L180R02A.class, oid);
			l180r02a.setAudit("");
			l180r02a.setHqCheckDate(CapDate.parseDate(hqCheckDate));
			l180r02a.setHqCheckMemo(hqCheckMemo);
			l180r02aList.add(l180r02a);
		}
		lms9511Service.savel180r02aList(l180r02aList);
		// EFD0017 儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 2.已敘做授信案件清單(搜尋此筆資料)
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL784S01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9515V01Page.class);
		String oids = params.getString("mainOids");
		L784S01A l784s01a = null;
		if (oids.indexOf("|") == -1) {
			l784s01a = lms9511Service.findModelByOid(L784S01A.class, oids);
			if ((l784s01a.getHqCheckDate() == null)) {
				result.set("hqCheckDate",
						TWNDate.toAD(CapDate.getCurrentTimestamp()));
			} else {
				result.set("hqCheckDate",
						TWNDate.toAD(l784s01a.getHqCheckDate()));
			}
			if (!"".equals(Util.trim(l784s01a.getHqCheckMemo()))) {
				result.set("hqCheckMemo", Util.trim(l784s01a.getHqCheckMemo()));
			} else {
				result.set("hqCheckMemo", pop.getProperty("hqCheckMemoDefault"));
			}
			result.set("oid", l784s01a.getOid());
		} else {
			result.set("hqCheckDate",
					TWNDate.toAD(CapDate.getCurrentTimestamp()));
			result.set("hqCheckMemo", pop.getProperty("hqCheckMemoDefault"));
			result.set("oids", oids);
		}
		return result;
	}

	/**
	 * 2.已敘做授信案件清單-授管處儲存資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveRemarkNotes(PageParameters params)
			throws CapException {
		String oids = params.getString("oids", "");
		String mainIdTemp = params.getString("mainIdTemp", "");
		CapAjaxFormResult result = new CapAjaxFormResult();
		L784S01A l784s01a = null;
		String lms9511v01From2 = params.getString("lms9511v01From2");
		JSONObject jobject2 = JSONObject.fromObject(lms9511v01From2);
		String hqCheckMemo = jobject2.getString("hqCheckMemo");
		String hqCheckDate = jobject2.getString("hqCheckDate");
		try {
			if (oids.indexOf("|") == -1) {
				l784s01a = lms9511Service.findModelByOid(L784S01A.class, oids);
				l784s01a.setHqCheckDate(CapDate.parseDate(hqCheckDate));
				l784s01a.setHqCheckMemo(hqCheckMemo);
				lms9511Service.save(l784s01a);
			} else {
				String temp[] = oids.split("\\|");
				for (String oid : temp) {
					l784s01a = lms9511Service.findModelByOid(L784S01A.class,
							oid);
					l784s01a.setHqCheckDate(CapDate.parseDate(hqCheckDate));
					l784s01a.setHqCheckMemo(hqCheckMemo);
					lms9511Service.save(l784s01a);
				}
			}
			LMSRPT lmsRpt = lms9511Service.findByLMSRPT(mainIdTemp);
			if (lmsRpt != null) {
				lmsRpt.setCfrmFlag("Y");
				lmsRpt.setCfrmTime(CapDate.parseDate(hqCheckDate));
				lms9511Service.save(lmsRpt);
			}
		} finally {

		}
		// EFD0017 儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveL784S07AData(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("mainIdTemp");
		String data = params.getString("data");
		JSONObject jsonData = JSONObject.fromObject(data);

		List<L784S07A> l784s07aList = lms9511Service
				.findL784S07AByMainId(mainId);
		List<L784S07A> list = new ArrayList<L784S07A>();
		for (L784S07A l784s07a : l784s07aList) {
			String oid = l784s07a.getOid();
			if (jsonData.containsKey(oid)) {
				String dataInsert = jsonData.getString(oid);
				if (!"|".equals(dataInsert)) {
					String temp[] = dataInsert.split("\\|");
					l784s07a.setCItem12Rec(Util.parseInt(temp[0].replace(",",
							"").replace(".", "")));
					l784s07a.setCItem12Amt(temp.length > 1 ? LMSUtil
							.toBigDecimal(temp[1].replace(",", "").replace(".",
									"")) : null);
					l784s07a.setUpdateTime(CapDate.getCurrentTimestamp());
					l784s07a.setUpdater(user.getUserId());
					list.add(l784s07a);
				}

			}
		}
		lms9511Service.savel784s07aList(list);
		// EFD0017 儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 引進當期資料-常董會
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	public IResult importLMS180R16Data(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String startDate = Util.trim(params.getString("startDate"));
		Date dataDate = null;
		Date dataStartDate = null;
		String[] temp = startDate.split("-");
		if (temp.length >= 1) {
			dataDate = lmsService.getDayOfMonth(temp[0], "1", true);
		} else {
			dataDate = lmsService.getDayOfMonth(CapDate.getCurrentDate("YYYY"),
					"1", true);
		}
		if (temp.length >= 2) {
			dataStartDate = lmsService.getDayOfMonth(temp[0], temp[1], true);
		}
		lms9511Service.importLMS180R16Data(dataDate, dataStartDate);

		// EFD0017 儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryLrsBrNo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String yyyy_MM = params.getString("lrsByDefaultCTLDate");
		List<String> r = new ArrayList<String>();

		if (CapDate.parseDate(yyyy_MM + "-01") == null) {
			throw new CapMessageException("傳入參數格式錯誤[" + yyyy_MM + "-01" + "]",
					getClass());
		}
		// 建立主要Search 條件
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		ISearch pageSetting = retrialService.getMetaSearch();
		List<String> ownBrIdList = new ArrayList<String>();
		if (true) {
			String logonBr = user.getUnitNo();
			ownBrIdList.add(logonBr);
			if (Util.equals(UtilConstants.BankNo.資訊處, logonBr)
					|| Util.equals(UtilConstants.BankNo.授管處, logonBr)) {

				ownBrIdList.add(UtilConstants.BankNo.北一區營運中心);
				ownBrIdList.add(UtilConstants.BankNo.北二區營運中心);
				ownBrIdList.add(UtilConstants.BankNo.桃竹苗區營運中心);
				ownBrIdList.add(UtilConstants.BankNo.中區營運中心);
				ownBrIdList.add(UtilConstants.BankNo.南區營運中心);
				// ---
				ownBrIdList.add(UtilConstants.BankNo.國外部);
				ownBrIdList.add(UtilConstants.BankNo.金控總部分行);
				ownBrIdList.add(UtilConstants.BankNo.國金部);
				ownBrIdList.add(UtilConstants.BankNo.私銀處作業組);
			}
		}
		pageSetting.addSearchModeParameters(SearchMode.IN, "ownBrId",
				(String[]) ownBrIdList.toArray(new String[ownBrIdList.size()]));
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (true) {
			pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
					"defaultCTLDate", yyyy_MM + "-01");
			pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
					"defaultCTLDate", CrsUtil.getDataEndDate(yyyy_MM));
		}

		Page<?> page = retrialService.findPage(L180M01A.class, pageSetting);
		List<L180M01A> l180m01a_list = (List<L180M01A>) page.getContent();
		for (L180M01A model : l180m01a_list) {
			r.add(model.getBranchId());
		}
		result.set("matchBr", r);
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult send_CLS180R03B_to_Audit(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String lmsRpt_mainId = params.getString("lmsRpt_mainId");
		int rtn = lms9511Service.send_CLS180R03B_to_Audit(lmsRpt_mainId);
		result.set("lmsRpt_mainId", lmsRpt_mainId);
		result.set("rtn", rtn);
		return result;
	}
}
