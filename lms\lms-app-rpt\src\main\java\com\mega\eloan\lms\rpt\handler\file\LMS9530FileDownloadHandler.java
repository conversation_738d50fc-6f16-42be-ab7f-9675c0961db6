/* 
 * LMS9530FileDownloadHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.file;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.report.PdfTools;

/**
 * <pre>
 * 舊案轉檔
 * </pre>
 * 
 * @since 2013/02/01
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/02/01,Vector,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9530filedownloadhandler")
public class LMS9530FileDownloadHandler implements FileDownloadService {
	private final String FILE_ROOT="C:/Users/<USER>/Desktop/Vector/ExportPDF";
	
	
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS9530FileDownloadHandler.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		String[] filePath = params.getStringArray("rptFile");
		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		FileInputStream fis = null;
		OutputStream outputStream = null;
		ByteArrayOutputStream baos = null,result= null;
		Locale locale = null;
		Properties propEloanPage = null;
		try {
			propEloanPage = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
			locale = LMSUtil.getLocale();
			result =new ByteArrayOutputStream();
			
			for(int i=0 ; i<filePath.length ; i++){
				filePath[i] =  java.net.URLDecoder.decode(filePath[i], "utf-8");
				fis = new FileInputStream(FILE_ROOT+filePath[i]);
				byte[] buf = new byte[4 * 1024];  // 4K buffer
				int bytesRead;
				outputStream = new ByteArrayOutputStream();
				while ((bytesRead = fis.read(buf)) != -1) {
					outputStream.write(buf, 0, bytesRead);
				}
				outputStream.flush();
				baos = (ByteArrayOutputStream) outputStream;
				if("pdf".equals(filePath[i].substring(filePath[i].lastIndexOf("."))))
					pdfNameMap.put(new ByteArrayInputStream(baos.toByteArray()), i);
			}
			if("pdf".equals(filePath[0].substring(filePath[0].lastIndexOf("."))))
			PdfTools.mergeReWritePagePdf(pdfNameMap, result,
					propEloanPage.getProperty("PaginationText"), true,
					locale, filePath.length);
			return result.toByteArray();
		} catch (Exception ex) {
			LOGGER.error(ex.toString());
		} finally {
			if (fis != null) {
				try {
					fis.close();
				} catch (IOException ex) {
				}
			}
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
				}
			}
			if (outputStream != null) {
				try {
					outputStream.close();
				} catch (IOException ex) {
				}
			}

		}
		return null;
	}
}
