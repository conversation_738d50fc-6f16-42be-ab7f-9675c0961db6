/* 
 * ELF461.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;


/** 授信消金狀態報送 **/
public class ELF461 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 借款人統編 **/
	@Column(name="ELF461_CUSTID", length=10, columnDefinition="CHAR(10)")
	private String elf461_custid;

	/** 重複序號 **/
	@Column(name="ELF461_DUPNO", length=1, columnDefinition="CHAR(01)")
	private String ELF461_DupNo;

	/** 額度序號 **/
	@Column(name="ELF461_CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String elf461_cntrno;

	/** 分行代碼 **/
	@Column(name="ELF461_BRNO", length=3, columnDefinition="CHAR(03)")
	private String elf461_brno;

	/** 
	 * 狀態<p/>
	 * 編製中|1<br/>
	 *  待覆核|2<br/>
	 *  核定|3<br/>
	 *  婉卻|4<br/>
	 *  結案|9
	 */
	@Column(name="ELF461_STATUS", length=1, columnDefinition="CHAR(01)")
	private String elf461_status;

	/** 
	 * 年度<p/>
	 * 案件更新狀態年度(民國年)
	 */
	@Column(name="ELF461_APPRYY", length=4, columnDefinition="CHAR(04)")
	private String elf461_appryy;

	/** 
	 * 月份<p/>
	 * 案件更新狀態月份
	 */
	@Column(name="ELF461_APPRMM", length=2, columnDefinition="CHAR(02)")
	private String elf461_apprmm;

	/** 
	 * 授權內/授權外<p/>
	 * 1 -分行授權內  2 -區域中心授權內<br/>
	 *  3 -分行授權外  4 -區域中心授權外
	 */
	@Column(name="ELF461_CTYPE", length=1, columnDefinition="CHAR(01)")
	private String elf461_ctype;

	/** 資料修改人(行員代號) **/
	@Column(name="ELF461_UPDATER", length=8, columnDefinition="CHAR(08)")
	private String elf461_updater;

	/** 資料修改日期 **/
	@Column(name="ELF461_TMESTAMP", columnDefinition="TIMESTAMP")
	private Date elf461_tmestamp;

	/** 信保動用截止日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF461_GUTCDATE", columnDefinition="DATE")
	private Date elf461_gutcdate;

	/** 簽報書案號 **/
	@Column(name="ELF461_PROJNO", length=40, columnDefinition="CHAR(40)")
	private String elf461_projno;

	/** 額度性質 **/
	@Column(name="ELF461_PROPERTY", length=2, columnDefinition="CHAR(02)")
	private String elf461_property;

    // J-108-0302 是否符合出口實績規範
    /** 是否符合出口實績規範     Y/N/ 空白，空白：上線前既有案件 **/
    @Column(name="ELF461_EXPERF_FG", length=1, columnDefinition="CHAR(01)")
    private String elf461_experf_fg;
    /** 瑕疵額度控管方式    1: 同出口押匯額度  2: 另訂限額 3: 不得瑕疵單據押匯 **/
    @Column(name="ELF461_FLAW_FG", length=1, columnDefinition="CHAR(01)")
    private String elf461_flaw_fg;
    /** 瑕疵額度限額 **/
    @Column(name="ELF461_FLAW_AMT", length=1, columnDefinition="DECIMAL(15,2)")
    private BigDecimal elf461_flaw_amt;

	/** 取得借款人統編 **/
	public String getElf461_custid() {
		return this.elf461_custid;
	}
	/** 設定借款人統編 **/
	public void setElf461_custid(String value) {
		this.elf461_custid = value;
	}

	/** 取得重複序號 **/
	public String getELF461_DupNo() {
		return this.ELF461_DupNo;
	}
	/** 設定重複序號 **/
	public void setELF461_DupNo(String value) {
		this.ELF461_DupNo = value;
	}

	/** 取得額度序號 **/
	public String getElf461_cntrno() {
		return this.elf461_cntrno;
	}
	/** 設定額度序號 **/
	public void setElf461_cntrno(String value) {
		this.elf461_cntrno = value;
	}

	/** 取得分行代碼 **/
	public String getElf461_brno() {
		return this.elf461_brno;
	}
	/** 設定分行代碼 **/
	public void setElf461_brno(String value) {
		this.elf461_brno = value;
	}

	/** 
	 * 取得狀態<p/>
	 * 編製中|1<br/>
	 *  待覆核|2<br/>
	 *  核定|3<br/>
	 *  婉卻|4<br/>
	 *  結案|9
	 */
	public String getElf461_status() {
		return this.elf461_status;
	}
	/**
	 *  設定狀態<p/>
	 *  編製中|1<br/>
	 *  待覆核|2<br/>
	 *  核定|3<br/>
	 *  婉卻|4<br/>
	 *  結案|9
	 **/
	public void setElf461_status(String value) {
		this.elf461_status = value;
	}

	/** 
	 * 取得年度<p/>
	 * 案件更新狀態年度(民國年)
	 */
	public String getElf461_appryy() {
		return this.elf461_appryy;
	}
	/**
	 *  設定年度<p/>
	 *  案件更新狀態年度(民國年)
	 **/
	public void setElf461_appryy(String value) {
		this.elf461_appryy = value;
	}

	/** 
	 * 取得月份<p/>
	 * 案件更新狀態月份
	 */
	public String getElf461_apprmm() {
		return this.elf461_apprmm;
	}
	/**
	 *  設定月份<p/>
	 *  案件更新狀態月份
	 **/
	public void setElf461_apprmm(String value) {
		this.elf461_apprmm = value;
	}

	/** 
	 * 取得授權內/授權外<p/>
	 * 1 -分行授權內  2 -區域中心授權內<br/>
	 *  3 -分行授權外  4 -區域中心授權外
	 */
	public String getElf461_ctype() {
		return this.elf461_ctype;
	}
	/**
	 *  設定授權內/授權外<p/>
	 *  1 -分行授權內  2 -區域中心授權內<br/>
	 *  3 -分行授權外  4 -區域中心授權外
	 **/
	public void setElf461_ctype(String value) {
		this.elf461_ctype = value;
	}

	/** 取得資料修改人(行員代號) **/
	public String getElf461_updater() {
		return this.elf461_updater;
	}
	/** 設定資料修改人(行員代號) **/
	public void setElf461_updater(String value) {
		this.elf461_updater = value;
	}

	/** 取得資料修改日期 **/
	public Date getElf461_tmestamp() {
		return this.elf461_tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setElf461_tmestamp(Date value) {
		this.elf461_tmestamp = value;
	}

	/** 取得信保動用截止日 **/
	public Date getElf461_gutcdate() {
		return this.elf461_gutcdate;
	}
	/** 設定信保動用截止日 **/
	public void setElf461_gutcdate(Date value) {
		this.elf461_gutcdate = value;
	}

	/** 取得簽報書案號 **/
	public String getElf461_projno() {
		return this.elf461_projno;
	}
	/** 設定簽報書案號 **/
	public void setElf461_projno(String value) {
		this.elf461_projno = value;
	}

	/** 取得額度性質 **/
    public String getElf461_property() {
        return this.elf461_property;
    }
    /** 設定額度性質 **/
    public void setElf461_property(String value) {
        this.elf461_property = value;
    }

    /** 取得是否符合出口實績規範 **/
    public String getElf461_experf_fg() {
        return this.elf461_experf_fg;
    }
    /** 設定是否符合出口實績規範 **/
    public void setElf461_experf_fg(String value) {
        this.elf461_experf_fg = value;
    }

    /** 取得瑕疵額度控管方式 **/
    public String getElf461_flaw_fg() {
        return this.elf461_flaw_fg;
    }
    /** 設定瑕疵額度控管方式 **/
    public void setElf461_flaw_fg(String value) {
        this.elf461_flaw_fg = value;
    }

    /** 取得瑕疵額度限額 **/
    public BigDecimal getElf461_flaw_amt() {
        return this.elf461_flaw_amt;
    }
    /** 設定瑕疵額度限額 **/
    public void setElf461_flaw_amt(BigDecimal value) {
        this.elf461_flaw_amt = value;
    }
}
