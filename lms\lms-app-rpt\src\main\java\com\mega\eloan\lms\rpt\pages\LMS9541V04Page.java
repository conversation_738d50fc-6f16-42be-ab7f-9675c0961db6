package com.mega.eloan.lms.rpt.pages;


import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
/**
 * <pre>
 * 查詢優惠額度資訊
 * </pre>
 * 
 * @since 2012/12/06
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/06,Vector,new
 *          </ul>
 */

@Controller
@RequestMapping(path = "/rpt/lms9541v04")
public class LMS9541V04Page extends AbstractEloanInnerView {

	public LMS9541V04Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {

		renderJsI18N(LMS9541V04Page.class);
		model.addAttribute("loadScript", "loadScript('pagejs/rpt/LMS9541V04Page');");
	}
}
