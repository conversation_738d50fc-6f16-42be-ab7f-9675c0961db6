package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang.builder.EqualsBuilder;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/**
 * 覆審借款人及連保人基本資料檔
 * 
 * <AUTHOR>
 * 
 */
@Entity
@Table(uniqueConstraints = @UniqueConstraint(columnNames = { "mainId",
		"custId", "dupNo", "debType", "debId", "debDupNo" }))
public class L170M01H extends GenericBean implements IDataObject, IDocObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5329556350961612262L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	@Column(length = 32, nullable = false, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 */
	@Column(length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 */
	@Column(length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 債務人種類    <br/>C.(共同)借款人    <br/>G(連帶)保證人    <br/>N(一般)保證人 */
	@Column(length = 1, columnDefinition = "CHAR(1)")
	private String debType;     

	/** 債務人id */
	@Column(length = 10, columnDefinition = "VARCHAR(10)")
	private String debId;

	/** 債務人重複碼 */
	@Column(length = 1, columnDefinition = "CHAR(1)")
	private String debDupNo;

	/** 債務人姓名 */
	@Column(length = 900, columnDefinition = "VARCHAR(900)")
	private String custName;

	/** 建立人員號碼 */
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 */
	@Temporal(TemporalType.TIMESTAMP)
	private Date createTime;

	/** 異動人員號碼 */
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 */
	@Temporal(TemporalType.TIMESTAMP)
	private Date updateTime;

	@Override
	public String getOid() {
		return this.oid;
	}

	@Override
	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	/** 身分證統編 */
	public String getCustId() {
		return custId;
	}

	/** 身分證統編 */
	public void setCustId(String custId) {
		this.custId = custId;
	}

	/** 身分證統編重複碼 */
	public String getDupNo() {
		return dupNo;
	}

	/** 身分證統編重複碼 */
	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	/** 借款人姓名 */
	public String getCustName() {
		return custName;
	}

	/** 借款人姓名 */
	public void setCustName(String custName) {
		this.custName = custName;
	}

	/** 建立人員號碼 */
	public String getCreator() {
		return creator;
	}

	/** 建立人員號碼 */
	public void setCreator(String creator) {
		this.creator = creator;
	}

	/** 建立日期 */
	public Date getCreateTime() {
		return createTime;
	}

	/** 建立日期 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/** 異動人員號碼 */
	public String getUpdater() {
		return updater;
	}

	/** 異動人員號碼 */
	public void setUpdater(String updater) {
		this.updater = updater;
	}

	/** 異動日期 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/** 異動日期 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public boolean equals(Object o) {
		boolean equals = false;
		if (o instanceof L170M01H) {
			L170M01H bean = (L170M01H) o;
			equals = (new EqualsBuilder().append(custId, bean.custId)
					.append(dupNo, bean.dupNo).append(debType, bean.debType)
					.append(debId, bean.debId).append(debDupNo, bean.debDupNo)
					.append(mainId, bean.mainId)).isEquals();
		}
		return equals;
	}

	public void setDebType(String debType) {
		this.debType = debType;
	}

	public String getDebType() {
		return debType;
	}

	public void setDebId(String debId) {
		this.debId = debId;
	}

	public String getDebId() {
		return debId;
	}

	public void setDebDupNo(String debDupNo) {
		this.debDupNo = debDupNo;
	}

	public String getDebDupNo() {
		return debDupNo;
	}

}
