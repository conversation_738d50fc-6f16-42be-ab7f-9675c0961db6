/* 
 * L140M01LDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01L;

/** 團貸案資料檔 **/
public interface L140M01LDao extends IGenericDao<L140M01L> {

	L140M01L findByOid(String oid);

	List<L140M01L> findByMainId(String mainId);
	
	
	List<L140M01L> findByMainIds(String[] mainIds);

	L140M01L findByUniqueKey(String mainId);

	List<L140M01L> findByIndex01(String mainId);
}