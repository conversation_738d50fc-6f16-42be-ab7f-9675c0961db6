/* 
 * L140M01DDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L140M01DDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01D;

/** 額度授信科目限額檔 **/
@Repository
public class L140M01DDaoImpl extends LMSJpaDao<L140M01D, String> implements
		L140M01DDao {

	@Override
	public L140M01D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M01D> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("createTime", false);
		printSeqMap.put("lmtSeq", false);
		search.setOrderBy(printSeqMap);
		List<L140M01D> list = createQuery(L140M01D.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L140M01D findByUniqueKey(String mainId, String lmtType,
			Integer lmtSeq) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "lmtType", lmtType);
		search.addSearchModeParameters(SearchMode.EQUALS, "lmtSeq", lmtSeq);

		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M01D> findByMainIdAndlmtType(String mainId, String lmtType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "lmtType", lmtType);
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("createTime", false);
		printSeqMap.put("lmtSeq", false);
		search.setOrderBy(printSeqMap);
		List<L140M01D> list = createQuery(L140M01D.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L140M01D> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L140M01D> list = createQuery(L140M01D.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<L140M01D> findByMainIdAndLmtTypeAndSubject(String mainId, String lmtType, String subject) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "lmtType", lmtType);
		search.addSearchModeParameters(SearchMode.EQUALS, "subject", subject);
		search.setMaxResults(Integer.MAX_VALUE);
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("lmtSeq", false);
		printSeqMap.put("createTime", false);
		search.setOrderBy(printSeqMap);
		List<L140M01D> list = createQuery(L140M01D.class, search)
				.getResultList();
		return list;
	}
}