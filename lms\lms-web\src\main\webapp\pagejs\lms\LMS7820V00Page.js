pageJsInit(function() {
  $(function() {
    var grid = $("#gridview").iGrid({
        handler: 'lms7820gridhandler',
        height: 350,
        postData: {
            formAction: "queryL782m01a"
        },
        rowNum: 15,
        sortname: 'dispatchDate',
        colModel: [{
            colHeader: i18n.lms7820m01['L782M01A.dispatchDate'],// 發文日期,
            name: 'dispatchDate',
            width: 120,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.lms7820m01['L782M01A.loanTP'],// 科目
            name: 'loanTP',
            width: 140,
            sortable: true
        }, {
            colHeader: i18n.lms7820m01['L782M01A.inteRate'],// 利率
            name: 'inteRate',
            width: 140,
            sortable: true
        }, {
            colHeader: i18n.lms7820m01['L782M01A.custName'],// 主要借款人名稱
            name: 'custName',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms7820m01['L782M01A.disp1'],// 備註
            name: 'disp1',
            width: 80,
            sortable: true,
            align: "center"
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $.form.submit({
            url: '../lms/lms7820m01/',
            data: {
                formAction: "queryL782m01a",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
    
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selrow');
        
        if (!rows) {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def['confirmDelete'], function(b){
            if (b) {
                $.ajax({
                    handler: "lms7820m01formhandler",
                    data: {
                        formAction: "deleteL782m01a",
                        oid: $("#gridview").getRowData(rows).oid
                    }
				}).done(function(obj) {
					$("#gridview").trigger("reloadGrid");
				});
            }
            else {
                return;
            }
        });
        
    }).end().find("#btnModify").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
        
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        
        var result = $("#gridview").getRowData(id);
        openDoc(null, null, result);
        
    }).end().find("#btnPrint").click(function(){
    
        var $filterForm = $("#filterForm");
        // 初始化
        $filterForm.reset();
        //set default value
        var sysdate = CommonAPI.getToday().split("-");
        var endDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
        var fromDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
        fromDate.setMonth(fromDate.getMonth() - 12);
        
        $filterForm.find("#releaseDateS").val(dateObjtoStr(fromDate));
        $filterForm.find("#releaseDateE").val(dateObjtoStr(endDate));
        
		 
        var thickTitle;
        thickTitle = i18n.lms7820m01['L782M01A.title01'];
         
        $("#filterBox").thickbox({
            // l7820v00.title01=請輸入篩選條件
            title: thickTitle,
            width: 500,
            height: 100,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
					
                    if (!$("#filterForm").valid()) {
                        return;
                    }
					
                    if ($("#releaseDateS").val() > $("#releaseDateE").val()) {
                        //起始日期不能大於結束日期
                        CommonAPI.showErrorMessage(i18n.lms7820m01['L782M01A.releaseDateS'] + "：" + i18n.lms7820m01['L782M01A.error01']);
                        return false;
                    }
                   
                    // L782M01A.printMess=此報表係列印【備註】欄位有資料之特殊案件﹝備註欄位為空白之案件，\n不列入﹞，是否執行列印動作
                    CommonAPI.confirmMessage(i18n.lms7820m01['L782M01A.printMess'], function(b){
                        if (b) {
                            $.form.submit({
                                url: "../simple/FileProcessingService",
                                target: "_blank",
                                data: {
                                    fileDownloadName: "lms7820r01.pdf",
                                    serviceName: "lms7820r01rptservice",
									releaseDateS:$filterForm.find("#releaseDateS").val() ,
									releaseDateE:$filterForm.find("#releaseDateE").val()
                                }
                            });
							
							$.thickbox.close();
                        }
                    });
                    
                    
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                        
                            $.thickbox.close();
                        }
                    });
                }
            }
         });
  	  });
	  
	  function dateObjtoStr(tDate) {
	    return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
	  }
   });
});

