package com.mega.eloan.lms.fms.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.Format;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.fms.pages.LMS9091V02Page;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 試算期付金日期對照表
 * 
 * <AUTHOR> 2012/12/07
 * 
 */
@Service("lms9091r02rptservice")
public class LMS9091R02RptServiceImpl extends AbstractReportService {

	@Resource
	BranchService branch;

	@Override
	public String getReportTemplateFileName() {
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		return "report/lms/LMS9091R01_" + LMSUtil.getLocale().toString()
				+ ".rpt";
	}
	Format fm1 = new DecimalFormat("#,###");// 貨幣格式

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 * org.apache.wicket.PageParameters)
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9091V02Page.class);
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = null;
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();

		try {
			locale = LMSUtil.getLocale();
			List<Map<String, String>> list = new ArrayList<Map<String, String>>();
			BigDecimal balance = Util.parseBigDecimal(Util.trim(params.getString("balance")));
			Integer years = Util.parseInt(Util.trim(params.getString("years")));
			Integer month = Util.parseInt(Util.trim(params.getString("month")));
			Double cycle = Util.parseDouble(params.getString("cycle"));// 繳款週期
			String repaymod = Util.trim(params.getString("repaymod"));// 攤還方式
			int NowEnd = Util.parseInt(params.getString("NowEnd"));// 寬限期結束期
			int NowFrom = Util.parseInt(params.getString("NowFrom"));// 寬限期起期
			Date date = Util
					.parseDate(Util.trim(params.getString("startdate")));// 起始日期
			Double PayWayAmt = Util.parseDouble(params.getString("PayWayAmt"));// 期付金額
			// --------------取得分段利率--------------------------
			Double rates[][] = new Double[5][3];
			for (int i = 0; i < rates.length; i++) {
				rates[i][0] = Util.parseDouble(Util.trim(params
						.getString("start" + (i + 1))));// 期間起始期數
				rates[i][1] = Util.parseDouble(Util.trim(params.getString("end"
						+ (i + 1))));// 期間結束期數
				rates[i][2] = Util.parseDouble(Util.trim(params
						.getString("rate" + (i + 1))));// 期間利率
			}
			String cyclename=new String();
			if (cycle == 12.0) {
				cyclename = pop.getProperty("L909V02.cycle0");
			} else {
				cyclename = pop.getProperty("L909V02.cycle1");
			}
			rptVariableMap.put("CYCLE", cyclename);
			list=LMSUtil.getRateList(balance, years, month, cycle, repaymod, NowEnd, NowFrom, date, PayWayAmt, rates);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			reportTools.setRowsData(list);
		} finally {

		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.ReportService#generateReport(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public OutputStream generateReport(PageParameters params)
			throws CapException {

		ReportGenerator rptGenerator = new ReportGenerator(
				this.getReportTemplateFileName());
		setReportData(rptGenerator, params);
		OutputStream outputStream = null;
		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		int subLine = 7;
		Properties propEloanPage = null;
		try {
			propEloanPage = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);
			outputStream = rptGenerator.generateReport();
			pdfNameMap.put(new ByteArrayInputStream(
					((ByteArrayOutputStream) outputStream).toByteArray()),
					subLine);
			if (pdfNameMap != null && pdfNameMap.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						LMSUtil.getLocale(), subLine, true);
			}
		} catch (Exception ex) {
			throw new CapException(ex.getCause(), ex.getClass());
		} finally {
			if (outputStream != null) {
				try {
					outputStream.close();
				} catch (IOException ex) {
					LOGGER.error("[generateReport]close() Exception!!", ex);
				}
			}
		}
		return outputStream;
	}


	
	
	

	
}
