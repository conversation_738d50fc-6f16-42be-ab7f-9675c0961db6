package com.mega.eloan.lms.cls.handler.form;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.cls.pages.CLS3401M05Page;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C340M01B;
import com.mega.eloan.lms.model.C340M01C;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上對保契約書 => 對應 ContractDocConstants.C340M01A_CtrType.Type_S 線上對保擔保品提供人契約
 * </pre>
 *
 * <AUTHOR>
 * @version <ul>
 * <li>2020/06/20,EL07625,new
 * </ul>
 * @since 2020/06/20
 */
@Scope("request")
@Controller("cls3401m05formhandler")
@DomainClass(C340M01A.class)
public class CLS3401M05FormHandler extends AbstractFormHandler {
	private static final DateFormat S_FORMAT = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
	
	@Resource
	BranchService branchService;

	@Resource
	CLSService clsService;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	TempDataService tempDataService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	CLS3401Service cls3401Service;

	@Resource
	ContractDocService contractDocService;
	
	Properties prop_cls3401m05 = MessageBundleScriptCreator.getComponentResource(CLS3401M05Page.class);
	Properties prop_abstractEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newC340M01A(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String ctrType = Util.trim(params.getString("ctrType", ContractDocConstants.C340M01A_CtrType.Type_S));
		String caseMainId = Util.trim(params.getString("caseMainId"));
		String tabMainId = Util.trim(params.getString("tabMainId"));

		L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);


		C340M01A c340m01a = new C340M01A();
		c340m01a.setMainId(IDGenerator.getUUID());
		c340m01a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		c340m01a.setOwnBrId(user.getUnitNo());
		c340m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());

		String txCode = Util.trim(params.getString(EloanConstants.TRANSACTION_CODE));
		c340m01a.setTxCode(txCode);
		c340m01a.setDocURL(CapWebUtil.getDocUrl(CLS3401M05Page.class));
		c340m01a.setDeletedTime(CapDate.getCurrentTimestamp());

		c340m01a.setCustId(custId);
		c340m01a.setDupNo(dupNo);
		c340m01a.setCreator(user.getUserId());
		c340m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c340m01a.setCtrType(ctrType);
		c340m01a.setRptId(get_latest_rptId(ctrType));
		c340m01a.setContrNumber("");
		c340m01a.setContrPartyNm("");


		if (l120m01a != null) {
			c340m01a.setCaseMainId(caseMainId);
			c340m01a.setCaseNo(Util.toSemiCharString(l120m01a.getCaseNo()));
		}

		cls3401Service.init_C340RelateCtrTypeS(c340m01a, caseMainId, tabMainId);

		return defaultResult(params, c340m01a, result);
	}

	private String get_latest_rptId(String ctrType){
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_S)){
			
		}
		return "";		
	}
	
	@DomainAuth(value = AuthType.Query)
	public IResult queryC340M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC340M01A_oid(mainOid);

			String page = params.getString(EloanConstants.PAGE);

			if ("01".equals(page)) {
				LMSUtil.addMetaToResult(result, meta,
						new String[] { "custId", "dupNo", "custName", "caseNo", "contrNumber", "contrPartyNm", "ploanCtrNo"
							, "ploanCtrExprDate", "ploanPosSId", "ploanPosSFlag", "ploanCtrSignTimeS"});
				String ownBrId = meta.getOwnBrId();
				result.set("ownBrId", StrUtils.concat(ownBrId, " ", branchService.getBranchName(ownBrId)));
				result.set("docStatus", getMessage("docStatus." + meta.getDocStatus()));
				result.set("ploanCtrStatus", getMessage("ploanCtrStatus." + Util.trim(meta.getPloanCtrStatus())));
				result.set("typCd", getMessage("typCd." + meta.getTypCd()));
				result.set("ctrTypeMapDesc", get_ctrType_desc(meta.getCtrType()));
				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime", Util.trim(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime", Util.trim(TWNDate.valueOf(meta.getUpdateTime())));
				result.set("rptId_desc", "");
				result.set("ploanCtrDcTime", meta.getPloanCtrDcTime()==null?"":S_FORMAT.format(meta.getPloanCtrDcTime()));
				result.set("ploanCtrDcUser", _id_name(meta.getPloanCtrDcUser()));
				
				List<C340M01B> c340m01b_list = clsService.findC340M01B(meta.getMainId());
				result.set("cntrNo", get_c340m01b_cntrNo(c340m01b_list));

				String ploanPosSId_name = cls3401Service.get_ploanPosSId_name(meta);
				result.set("ploanPosSId_name", ploanPosSId_name);
			}
		}

		//在 defaultResult(...) 去讀  c340m01c
		return defaultResult(params, meta, result);
	}

	private String get_c340m01b_cntrNo(List<C340M01B> c340m01b_list) {
		List<String> cntrNo_list = new ArrayList<String>();
		for (C340M01B c340m01b : c340m01b_list) {
			cntrNo_list.add(c340m01b.getCntrNo());
		}
		return StringUtils.join(cntrNo_list, "、");
	}


	private String _id_name(String raw_id) {
		String id = Util.trim(raw_id);
		return Util.trim(id + " " + Util.trim(userInfoService.getUserName(id)));
	}

	/**
	 * 儲存
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		return _saveAction(params, "Y");
	}

	private CapAjaxFormResult _saveAction(PageParameters params, String tempSave) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		//===
		String KEY = "saveOkFlag";


		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try {
				meta = clsService.findC340M01A_oid(mainOid);				
				//~~~~~~
				String page = params.getString(EloanConstants.PAGE);

				if ("01".equals(page)) {
				}


				meta.setDeletedTime(null);
				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(CapDate.getCurrentTimestamp());
				meta.setRandomCode(IDGenerator.getRandomCode());

				if ("Y".equals(tempSave)) {
					cls3401Service.saveTemporaryMeta(meta);
				} else {
					cls3401Service.saveMeta(meta);
				}
				// ===
				if (Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					// 在tempSave<>Y,若有未填欄位,丟 CapMessageException, 讓
					// saveOkFlag==false

					String err_msg = check_data_relation(meta);
					String unKeyIn_column = ""; // unKeyIn_column(meta);
					if (allowIncomplete == false && Util.isNotEmpty(err_msg)) {
						throw new CapMessageException(err_msg, getClass());
					}

					if (allowIncomplete && (Util.isNotEmpty(err_msg) || Util.isNotEmpty(unKeyIn_column))) {
						result.set("IncompleteMsg", err_msg + ((Util.isNotEmpty(err_msg) && Util.isNotEmpty(
								unKeyIn_column)) ? "<br/>" : "") + unKeyIn_column);
					}
				}
				result.set(KEY, true);
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}
		}

		return defaultResult(params, meta, result);
	}

	/**
	 * 呈主管覆核
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params) throws Throwable {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));

		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC340M01A_oid(mainOid);

			if (CreditDocStatusEnum.海外_編製中.getCode().equals(meta.getDocStatus())) {					
				if(true){
					String ploanCtrNo = Util.trim(meta.getPloanCtrNo());
					if(clsService.findC340M01A_ploanCtrNo(ploanCtrNo).size()>1){
						throw new CapMessageException(prop_cls3401m05.getProperty("C340M01A.ploanCtrNo")+"(" + ploanCtrNo + ")重複，請刪除此份契約，重新產出。", this.getClass());
					}
				}
				//~~~~~~~~~~
				String cntrNo = meta.getC340m01bs().get(0).getCntrNo();
				List<C340M01A> flowingC340M01A = cls3401Service.findFlowingC340M01A(cntrNo, meta.getCtrType());
				if (flowingC340M01A.size() > 0) {
					C340M01A pendingCase = flowingC340M01A.get(0);
					throw new CapMessageException("額度序號" + cntrNo 
							+ "（"+prop_cls3401m05.getProperty("C340M01A.ploanCtrNo")+"：" +pendingCase.getPloanCtrNo()
							+ (Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), pendingCase.getDocStatus())?"，e-Loan文件狀態："+prop_abstractEloanPage.getProperty("docStatus." + pendingCase.getDocStatus()):"")
							+"）"							
							+"尚未完成線上對保程序。"+"<br/>"
							+"因此本案契約（" +meta.getPloanCtrNo()+"）無法呈主管覆核", this.getClass());
				}
			}

			if (params.containsKey("decisionExpr")) {
				if ("ok".equals(decisionExpr)) {
					if (Util.equals(user.getUserId(), meta.getUpdater())) {
						// EFD0053' 覆核人員不可與「經辦人員或其它覆核人員」為同一人
						throw new CapMessageException(
								RespMsgHelper.getMessage("EFD0053"),
								getClass());
					}
				}
			}

			cls3401Service.flowAction(meta, params.containsKey("decisionExpr"), decisionExpr);

		}
		return defaultResult(params, meta, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult delC340M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A c340m01a = clsService.findC340M01A_oid(mainOid);
		if (c340m01a != null) {
			c340m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			clsService.save(c340m01a);
		}
		return result;
	}

	private String check_data_relation(C340M01A c340m01a) {
		//===========================
		// 若簽報書核准後，有被退回修改
		if (c340m01a == null) {
			return "查無 契約書資料";
		}
		L120M01A l120m01a = clsService.findL120M01A_mainId(c340m01a.getCaseMainId());
		if (l120m01a == null) {
			return "查無 簽報書資料" + "(" + c340m01a.getCaseMainId() + ")。" + "若退回且修改已核准簽報書，請重新產製契約書。";
		}
		for (C340M01B c340m01b : clsService.findC340M01B(c340m01a.getMainId())) {
			L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
			if (l140m01a == null) {
				return "查無 額度明細表資料(" + c340m01b.getTabMainId() + ")。" + "若退回且修改已核准簽報書，請重新產製契約書。";
			}
		}
		
		String ploanCtrNo = Util.trim(c340m01a.getPloanCtrNo());
		if(clsService.findC340M01A_ploanCtrNo(ploanCtrNo).size()>1){
			return prop_cls3401m05.getProperty("C340M01A.ploanCtrNo")+"(" + ploanCtrNo + ")重複，請刪除此份契約，重新產出。";
		}
		return "";
	}

	@DomainAuth(AuthType.Modify)
	public IResult check_C340(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = clsService.findC340M01A_oid(mainOid);
		if (true) {
			String err_msg = check_data_relation(meta);
			if (Util.isNotEmpty(err_msg)) {
				throw new CapMessageException(err_msg, getClass());
			}
		}
		
		result.set("model_custId", Util.trim(meta.getCustId()));
		result.set("model_custName", Util.trim(meta.getCustName()));
		result.set("model_caseNo", Util.trim(meta.getCaseNo()));
		result.set("model_ctrTypeDesc", get_ctrType_desc(meta.getCtrType()));
		return result;
	}


	private String get_ctrType_desc(String ctrType) {
		if (Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_S)) {
			return prop_cls3401m05.getProperty("C340M01A.ctrType.S");
		}
		return "";
	}
	
	private CapAjaxFormResult defaultResult(PageParameters params, C340M01A meta,
			CapAjaxFormResult result) throws CapException {

		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		result.set("custId", meta.getCustId());
		result.set("ctrType", meta.getCtrType());
		result.set("custInfo", Util.trim(meta.getCustId()) + " " + Util.trim(meta.getDupNo()) + " " + Util.trim(meta.getCustName()));
		result.set("ctrTypeHeaderDesc", get_ctrType_desc(meta.getCtrType()));
		return result;
	}


	/**
	 * 線上對保作廢
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Accept)
	public IResult inValidC340M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A c340m01a = clsService.findC340M01A_oid(mainOid);
		if (c340m01a != null) {
			cls3401Service.inValidOnlineCtr(c340m01a);
			result.set("ploanCtrStatus", getMessage("ploanCtrStatus." + Util.trim(c340m01a.getPloanCtrStatus())));
			result.set("ploanCtrDcTime", c340m01a.getPloanCtrDcTime()==null?"":S_FORMAT.format(c340m01a.getPloanCtrDcTime()));
			result.set("ploanCtrDcUser", _id_name(c340m01a.getPloanCtrDcUser()));
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult showJSON(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C340M01A meta = clsService.findC340M01A_mainId(mainId);
		if(meta==null){
			throw new CapMessageException("mainId="+mainId+" not found", getClass());
		}	
		if(true){	
			List<C340M01C> c340m01c_list = clsService.findC340M01C(meta.getMainId());
			for(C340M01C c340m01c : c340m01c_list){
				result.set("c340m01c_"+c340m01c.getItemType(), c340m01c.getJsonData());
			}
			result.set("mainId", meta.getMainId());
		}
		return result;
	}

	/**
	 * 判斷登入者是否僅有EL電銷權限
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult check_only_expermission(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		result.set("only_ex_permission", user.isEXAuth());//是否僅有電銷權限	
		return result;
	}
}
