package com.mega.eloan.lms.cls.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LmsExcelUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.pages.CLS3701M01Page;
import com.mega.eloan.lms.cls.service.CLS3701Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C126M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFPrintSetup;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 房仲產Excel
 * </pre>
 * 
 */
@Service("cls3701xlsservice")
public class CLS3701XLSServiceImpl implements FileDownloadService {

	@Resource
	EloandbBASEService eloanDbBaseService;

	@Resource
	BranchService branch;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	UserInfoService userInfoService;
	
	@Resource
    CLS3701Service cls3701Service;

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS3701XLSServiceImpl.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException {

		ByteArrayOutputStream baos = null;

		try {
			baos = this.genExcel_1(params);

			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}

	/**
	 * 房仲引介案件匯出
	 * 
	 * @param params
	 * @return
	 */
	private ByteArrayOutputStream genExcel_1(PageParameters params) {
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS3701M01Page.class);
		ByteArrayOutputStream baos = null;
		List<Map<String, Object>> listMap = null;
		List<C126M01A> listMap2 = null;

		HSSFWorkbook book = null;
		HSSFFont font12 = null;
		HSSFCellStyle format12Center = null;
		HSSFCellStyle format12CenterNO = null;
		HSSFCellStyle format12Left = null;
		HSSFCellStyle format12Right = null;
		HSSFSheet sheet = null;
		
		String agntNo = null;
		String ownBrId = null;
		String applyTS_beg = null;
		String applyTS_end = null;
		
		String isFCheck= null;
		String caseBrId= null;

		Map<String, String> returnCodeMap = null;
		Map<String, String> agntNoCodeMap = null;
		Locale locale = null;

		try {
			baos = new ByteArrayOutputStream();
			book = new HSSFWorkbook();
			sheet = book.createSheet("查詢結果");
			
			/*
			 * 1.1方向 SheetSetting#setOrientation(PageOrientation po)； 參數：
			 * PageOrientation#LANDSCAPE 橫向打印 PageOrientation# PORTRAIT 縱向打印 (A)
			 * SheetSetting #setScaleFactor (int);百分比形式
			 */
			HSSFPrintSetup printSetUp = sheet.getPrintSetup();
			//設定橫向列印
			printSetUp.setLandscape(true);
			printSetUp.setPaperSize(PrintSetup.A4_PAPERSIZE);
			sheet.setFitToPage(true);
			// 縮放比例頁寬一頁
			printSetUp.setFitWidth((short)1);
			// 縮放比例頁高(5000頁)
			printSetUp.setFitHeight((short)5000);
			// 設定字型與格式、other.msg60=新細明體
			font12 = book.createFont();
			// 設定字體名稱
			font12.setFontName(pop2.getProperty("other.msg60")); 
			// 設定字體大小
			font12.setFontHeightInPoints((short) 12);
			// 設定非粗體
			font12.setBold(false);                               
			
			format12Center = LmsExcelUtil.setCellFormat(book, font12,
					HorizontalAlignment.CENTER);
			format12Left = LmsExcelUtil.setCellFormat(book, font12,
					HorizontalAlignment.LEFT);
			format12Right = LmsExcelUtil.setCellFormat(book, font12,
					HorizontalAlignment.RIGHT);
			format12CenterNO = LmsExcelUtil.setCellFormat(book, font12,
					HorizontalAlignment.CENTER, false, false);
			
			// 取出 Workbook 的自訂調色盤
			HSSFPalette palette = book.getCustomPalette();

			// 覆寫 Excel 調色盤中的索引色
			palette.setColorAtIndex(
			    IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex(),  // 對應原始 JXL Colour.BLUE2
			    (byte)0xB3, (byte)0xCB, (byte)0xE2
			);
			palette.setColorAtIndex(
			    IndexedColors.LIGHT_YELLOW.getIndex(),           // 對應原始 JXL Colour.YELLOW2
			    (byte)255, (byte)242, (byte)204
			);

			// 3. 建立新的 CellStyle，並複製原有的文字格式與對齊設定
			HSSFCellStyle yellowStyle = book.createCellStyle();
			yellowStyle.cloneStyleFrom(format12Left);  
			yellowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			yellowStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());

			HSSFCellStyle blueStyle = book.createCellStyle();
			blueStyle.cloneStyleFrom(format12Left);
			blueStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			blueStyle.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());

            
			ownBrId = MegaSSOSecurityContext.getUnitNo();
			agntNo = Util.trim(params.getString("agntNo"));
			applyTS_beg = Util.trim(params.getString("applyTS_beg"));
			applyTS_end = Util.trim(params.getString("applyTS_end"));
			isFCheck=Util.trim(params.getString("isFCheck"));
			caseBrId=params.getString("caseBrId");
			//015分行匯出
			if(Util.isEmpty(isFCheck)){
				
				// 總處單位可以看全部，分行單位只能看自己
				if (Util.notEquals(ownBrId, "")) {
					if (cls3701Service.checkSpecialBank(ownBrId)) {
						ownBrId = caseBrId;
					}
				}
				
				listMap =eloanDbBaseService.findC126m01aByAgntNo(ownBrId,agntNo,applyTS_beg,applyTS_end,CreditDocStatusEnum.先行動用_已覆核.getCode());
			}
			//待覆核各別分行匯出
			else{
				listMap =eloanDbBaseService.findC126m01aByAgntNo(ownBrId,"","","",CreditDocStatusEnum.海外_編製中.getCode());
			}
			
			locale = LMSUtil.getLocale();
			agntNoCodeMap = codetypeservice.findByCodeType(
					"L140M01A_agntNo", locale.toString());
			returnCodeMap = codetypeservice.findByCodeType(
					"L140M01A_agntNo", locale.toString());
			
			String execelTitle=Util.trim(agntNoCodeMap.get(agntNo));

			sheet.addMergedRegion(new CellRangeAddress(
			    /*firstRow*/0, /*lastRow*/0,
			    /*firstCol*/0, /*lastCol*/20
			));
			HSSFRow row0 = sheet.createRow(0);
		    LmsExcelUtil.addCell(row0, 0, execelTitle + "案件追蹤表", format12CenterNO);

			// 設定行寬
			int[] colWidths = {6, 6, 12, 12, 14, 14, 14, 14, 20, 12, 12, 12, 12, 12, 20, 12, 12, 6, 10, 8};
			for(int i = 0; i < colWidths.length; i++) {
				sheet.setColumnWidth(i, colWidths[i] * 256);
			}

			String[] title = { "項次","分行名稱","收件日期","買賣合約書簽約日期",	"買賣合約書編號","送件代書","借戶姓名"
					,"借戶ID","物件門牌","成交金額 (萬元)","預貸金額(萬元)","可貸金額(萬元)","鑑價完成日期","徵審完成日期","撥款日期"
					,"承作情形\n"+
						"(請填代碼)\n"+
						"1.承作\n"+
						"2.未承作(客戶自找其他家)\n"+
						"3.未承作(因個人徵信婉拒客戶)\n"+
						"4.未承作(估值不到客戶欲貸金額)\n"+
						"5.未承作(擔保品不符內規條件，例:雙D房屋，小套房…等.)\n"
					,"經辦","撥款金額(萬元)","利率方案","回饋金比率(%)","回饋金(元)"};

			HSSFRow header = sheet.createRow(1);
	        for (int j = 0; j < title.length; j++) {
	            HSSFCell c = header.createCell(j);
	            c.setCellValue(title[j]);
	            c.setCellStyle(title[j].contains("承作情形") ? format12Left : format12Center);
	        }

			if (!listMap.isEmpty()) {
				for (int i = 0, k = 2 , j = 1; i < listMap.size(); i++, k++, j++) {
					HSSFRow dataRow = sheet.createRow(k);
					
					// 項次
					LmsExcelUtil.addCell(dataRow, 0, Integer.toString(j), format12Center);
					
					// 分行名稱
					String OWNBRID = "";
					if (listMap.get(i).get("OWNBRID") != null) {
						OWNBRID = Util.trim(listMap.get(i).get("OWNBRID"));
					}

					// 收件日期
					String RECEIVEDATE = "";
					if (listMap.get(i).get("RECEIVEDATE") != null) {
						RECEIVEDATE = Util.trim(listMap.get(i).get("RECEIVEDATE"));

					}

					// 買賣合約書簽約日期
					String CONTRACTDATE = "";
					if (listMap.get(i).get("CONTRACTDATE") != null) {
						CONTRACTDATE = Util.trim(listMap.get(i).get("CONTRACTDATE"));
					}

					// 買賣合約書編號
					String CONTRACTNO = "";
					if (listMap.get(i).get("CONTRACTNO") != null) {
						CONTRACTNO = Util.trim(listMap.get(i).get("CONTRACTNO"));
					}

					// 送件代書
					String ESSAYNAME = "";
					if (listMap.get(i).get("ESSAYNAME") != null) {
						ESSAYNAME = Util.trim(listMap.get(i).get("ESSAYNAME"));

					}

					// 借戶姓名
					String CUSTNAME = "";
					if (listMap.get(i).get("CUSTNAME") != null) {
						CUSTNAME = Util.trim(listMap.get(i).get("CUSTNAME"));
					}

					// 借戶ID
					String CUSTID = "";
					if (listMap.get(i).get("CUSTID") != null) {
						CUSTID = Util.trim(listMap.get(i).get("CUSTID"));
					}

					// 物件門牌
					String address = "";
					
					if (listMap.get(i).get("CITY") != null) {
						Map<String, CapAjaxFormResult> codeTypes = codetypeservice
						.findByCodeType(new String[] {"HaveNo","YesNo"});
			        	CodeType counties = codetypeservice.findByCodeTypeAndCodeValue(
								"counties", Util.trim(listMap.get(i).get("CITY")));
						CodeType counties2 = codetypeservice.findByCodeTypeAndCodeValue(
								"counties" + Util.trim(listMap.get(i).get("CITY")),
								Util.trim(listMap.get(i).get("DIST")));
						
						address = (counties == null ? "" : counties.getCodeDesc()) +
						(counties2 == null ? "" : counties2.getCodeDesc()) +
						Util.trim(listMap.get(i).get("VILLAGENAME")) +
						(Util.isEmpty(Util.trim(listMap.get(i).get("VILLAGENAME"))) ? "" :
								this.showPic(Util.trim(listMap.get(i).get("VILLAGE")), "5", codeTypes, locale)) +
						Util.trim(listMap.get(i).get("NEIGHBORHOOD")) +
						(Util.isEmpty(Util.trim(listMap.get(i).get("NEIGHBORHOOD"))) ? "" : "鄰") +
						Util.trim(listMap.get(i).get("ROADNAME")) + this.showPic(
								Util.trim(listMap.get(i).get("ROAD")), "6", codeTypes, locale) +
						Util.trim(listMap.get(i).get("SEC")) +
						(Util.isEmpty(Util.trim(listMap.get(i).get("SEC"))) ? "" : "段") +
						Util.trim(listMap.get(i).get("LANE")) +
						(Util.isEmpty(Util.trim(listMap.get(i).get("LANE"))) ? "" : "巷") +
						Util.trim(listMap.get(i).get("ALLEY")) +
						(Util.isEmpty(Util.trim(listMap.get(i).get("ALLEY"))) ? "" : "弄") +
						Util.trim(listMap.get(i).get("NO1")) +
						((Util.isEmpty(Util.trim(listMap.get(i).get("NO1"))) &&
								Util.isEmpty(Util.trim(listMap.get(i).get("NO2")))) ? "" :
								(Util.isEmpty(Util.trim(listMap.get(i).get("NO2"))) ? "號" : "之")) +
			            Util.trim(listMap.get(i).get("NO2")) +
						(Util.isEmpty(Util.trim(listMap.get(i).get("NO2"))) ? "" : "號") +
						Util.trim(listMap.get(i).get("FLOOR1")) +
						(Util.isEmpty(Util.trim(listMap.get(i).get("FLOOR1"))) ? "" : "樓") +
						(Util.isEmpty(Util.trim(listMap.get(i).get("FLOOR2"))) ? "" : "之") +
						Util.trim(listMap.get(i).get("FLOOR2")) +
						(Util.isEmpty(Util.trim(listMap.get(i).get("ROOM"))) ? "" : "(") +
						Util.trim(listMap.get(i).get("ROOM")) +
						(Util.isEmpty(Util.trim(listMap.get(i).get("ROOM"))) ? "" : "室)");;

					}
					
					// 成交金額
					String TTLAMT = "";
					if (listMap.get(i).get("TTLAMT") != null) {
						TTLAMT = LMSUtil.calcZero(Util.parseBigDecimal(Util.trim((listMap.get(i).get("TTLAMT")))).divide(new BigDecimal("10000")));

					}

					// 預貸金額
					String PRELOANAMT = "";
					if (listMap.get(i).get("PRELOANAMT") != null) {
						PRELOANAMT = LMSUtil.calcZero(Util.parseBigDecimal(Util.trim((listMap.get(i).get("PRELOANAMT")))).divide(new BigDecimal("10000")));

					}

					// 可貸金額
					String APPLYAMT = "";
					if (listMap.get(i).get("APPLYAMT") != null) {
						APPLYAMT = LMSUtil.calcZero(Util.parseBigDecimal(Util.trim((listMap.get(i).get("APPLYAMT")))).divide(new BigDecimal("10000")));
					}

					// 鑑價完成日期
					String ESTDATE = "";
					if (listMap.get(i).get("ESTDATE") != null) {
						ESTDATE = Util.trim(listMap.get(i).get("ESTDATE"));

					}
					
					// 徵審完成日期
					String CREDITREVIEWDATE = "";
					if (listMap.get(i).get("CREDITREVIEWDATE") != null) {
						CREDITREVIEWDATE = Util.trim(listMap.get(i).get("CREDITREVIEWDATE"));

					}
					
					// 撥款日期
					String LNDATE = "";
					if (listMap.get(i).get("LNDATE") != null) {
						LNDATE = Util.trim(listMap.get(i).get("LNDATE"));

					}
					
					// 承作情形
					String STATFLAG = "";
					if (listMap.get(i).get("STATFLAG") != null) {
						STATFLAG = Util.trim(listMap.get(i).get("STATFLAG"));

					}
					
					// 經辦
					String UPDATER = "";
					if (listMap.get(i).get("UPDATER") != null) {
						UPDATER = Util.trim(listMap.get(i).get("UPDATER"));

					}
					
					// 撥款金額
					String LNAMT = "";
					if (listMap.get(i).get("LNAMT") != null) {
						LNAMT = LMSUtil.calcZero(Util.parseBigDecimal(Util.trim((listMap.get(i).get("LNAMT")))).divide(new BigDecimal("10000")));
					}
					
					// 利率方案
					String RATEKIND = "";
					if (listMap.get(i).get("RATEKIND") != null) {
						RATEKIND = Util.trim(listMap.get(i).get("RATEKIND"));

					}
					
					// 回饋金比率
					String REBATERATIO = "";
					if (listMap.get(i).get("REBATERATIO") != null) {
						REBATERATIO = Util.parseBigDecimal(Util.trim((listMap.get(i).get("REBATERATIO")))).setScale(3, BigDecimal.ROUND_HALF_DOWN).toString();

					}
					
					// 回饋金
					String REBATE = "";
					if (listMap.get(i).get("REBATE") != null) {
						REBATE = LMSUtil.calcZero(Util.parseBigDecimal(Util.trim((listMap.get(i).get("REBATE")))));

					}

	                LmsExcelUtil.addCell(dataRow, 1, OWNBRID,     yellowStyle);
	                LmsExcelUtil.addCell(dataRow, 2, RECEIVEDATE, yellowStyle);
	                LmsExcelUtil.addCell(dataRow, 3, CONTRACTDATE,yellowStyle);
	                LmsExcelUtil.addCell(dataRow, 4, CONTRACTNO,  yellowStyle);
	                LmsExcelUtil.addCell(dataRow, 5, ESSAYNAME,   yellowStyle);
	                LmsExcelUtil.addCell(dataRow, 6, CUSTNAME,    yellowStyle);
	                LmsExcelUtil.addCell(dataRow, 7, CUSTID,      yellowStyle);
	                LmsExcelUtil.addCell(dataRow, 8, address,     yellowStyle);
	                LmsExcelUtil.addCell(dataRow, 9, TTLAMT,      yellowStyle);
	                LmsExcelUtil.addCell(dataRow, 10, PRELOANAMT, yellowStyle);
	                LmsExcelUtil.addCell(dataRow, 11, APPLYAMT,   blueStyle);
	                LmsExcelUtil.addCell(dataRow, 12, ESTDATE,    blueStyle);
	                LmsExcelUtil.addCell(dataRow, 13, CREDITREVIEWDATE, blueStyle);
	                LmsExcelUtil.addCell(dataRow, 14, LNDATE,     blueStyle);
	                LmsExcelUtil.addCell(dataRow, 15, STATFLAG,   yellowStyle);
	                LmsExcelUtil.addCell(dataRow, 16, UPDATER,    yellowStyle);
	                LmsExcelUtil.addCell(dataRow, 17, LNAMT,      blueStyle);
	                LmsExcelUtil.addCell(dataRow, 18, RATEKIND,   blueStyle);
	                LmsExcelUtil.addCell(dataRow, 19, REBATERATIO, blueStyle);
	                LmsExcelUtil.addCell(dataRow, 20, REBATE,     blueStyle);

				}
				Integer ps1=listMap.size()+3;
				Integer ps2=listMap.size()+4;
				Integer ps3=listMap.size()+8;
				Integer ps4=listMap.size()+9;
				
//				sheet.mergeCells(1, ps1, 7, ps1);
//				labelContent = new Label(1, ps1, "所有案件填寫資料",yellow);
//				sheet.addCell(labelContent);
//				
//				sheet.mergeCells(1, ps2, 7, ps2);
//				labelContent = new Label(1, ps2, "成案者填寫資料",bule);
//				sheet.addCell(labelContent);
//				
//				sheet.mergeCells(1, ps3, 7, ps3);
//				labelContent = new Label(1, ps3, "(一)一般房貸、小套房房貸案件(有搭配房貸壽險專案)：轉介服務費0.1%(含稅)",format12Left);
//				sheet.addCell(labelContent);
//				
//				sheet.mergeCells(1, ps4, 7, ps4);
//				labelContent = new Label(1, ps4, "(二)政策性房貸、小套房房貸案件(未搭配房貸壽險專案)：轉介服務費0.075%(含稅)",format12Left);
//				sheet.addCell(labelContent);
				
				 CellRangeAddress region = new CellRangeAddress(ps1, ps1, 1, 7);
		            sheet.addMergedRegion(region);
		            setMergeCellBorder(region, sheet);
		            HSSFRow rowPs1 = sheet.createRow(ps1);
		            LmsExcelUtil.addCell(rowPs1, 1, "所有案件填寫資料", yellowStyle);

		            region = new CellRangeAddress(ps2, ps2, 1, 7);
		            sheet.addMergedRegion(region);
		            setMergeCellBorder(region, sheet);
		            HSSFRow rowPs2 = sheet.createRow(ps2);
		            LmsExcelUtil.addCell(rowPs2, 1, "成案者填寫資料", blueStyle);

		            region = new CellRangeAddress(ps3, ps3, 1, 7);
		            sheet.addMergedRegion(region);
		            setMergeCellBorder(region, sheet);
		            HSSFRow rowPs3 = sheet.createRow(ps3);
		            LmsExcelUtil.addCell(rowPs3, 1,
		                "(一)一般房貸、小套房房貸案件(有搭配房貸壽險專案)：轉介服務費0.1%(含稅)",
		                format12Left
		            );

		            region = new CellRangeAddress(ps4, ps4, 1, 7);
		            sheet.addMergedRegion(region);
		            setMergeCellBorder(region, sheet);
		            HSSFRow rowPs4 = sheet.createRow(ps4);
		            LmsExcelUtil.addCell(rowPs4, 1,
		                "(二)政策性房貸、小套房房貸案件(未搭配房貸壽險專案)：轉介服務費0.075%(含稅)",
		                format12Left
		            );
				
			}
			book.write(baos);
			book.close();
			if(listMap.size()>0){
				listMap2 = cls3701Service.findAgntNo(ownBrId,agntNo,applyTS_beg,applyTS_end,CreditDocStatusEnum.先行動用_已覆核.getCode());
				for(C126M01A c1260m01a :listMap2){
					if(c1260m01a.getIsFinished().equals(UtilConstants.DEFAULT.是)){
						c1260m01a.setIsClosed(UtilConstants.DEFAULT.是);
						c1260m01a.setDocStatus(CreditDocStatusEnum.海外_已核准);
						cls3701Service.save(c1260m01a);
					}
				}
			}
			return baos;
		} catch (Exception ex) {
			LOGGER.error("[genExcel_1] Exception!!", ex.getMessage());
		} finally {

			if (returnCodeMap != null) {
				returnCodeMap.clear();
			}

			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[genExcel_1] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}
	

    /**
	 * kind=1, 寫死 "是 or 否"
	 * kind=2, 寫死 "有 or 無"
	 * kind=3, 1:是. 2:否
	 * kind=4, 1:有. 2:無
	 * kind=5, 1:里. 2:村
	 * kind=6, 1:"". 2:路. 3:街
	 **/
	private String showPic(String value, String kind,
						   Map<String, CapAjaxFormResult> codeTypes, Locale locale) {
		Map<String, String> yesNoMap = null;
		StringBuffer str = new StringBuffer();
		try {
			String code = "";
			if(Util.equals(kind, "1") || Util.equals(kind, "3")){
				code = "YesNo";
			} else if(Util.equals(kind, "2") || Util.equals(kind, "4")){
				code = "HaveNo";
			}

			if(Util.equals(kind, "1") || Util.equals(kind, "2")){
				str.append(codeTypes.get(code).get(value));
			} else if(Util.equals(kind, "3") || Util.equals(kind, "4")){
				str.append((Util.equals(value, "1") ? "■" : "□"));
				str.append(codeTypes.get(code).get("1"));
				str.append((Util.equals(value, "2") ? "■" : "□"));
				str.append(codeTypes.get(code).get("2"));
			} else if(Util.equals(kind, "5")){
				Map<String, String> k4Map = new HashMap<String, String>();
				k4Map.put("1", "里");
				k4Map.put("2", "村");
				str.append(k4Map.get(value));
			} else if(Util.equals(kind, "6")){
				Map<String, String> k5Map = new HashMap<String, String>();
				k5Map.put("1", "");
				k5Map.put("2", "路");
				k5Map.put("3", "街");
				str.append(k5Map.get(value));
			}
		} catch (Exception e) {
			//logger.error(e.getMessage(), e);
		} finally {
			if (yesNoMap != null) {
				yesNoMap.clear();
			}
		}
		return str.toString();
	}
	
	/**
	 * poi升級，設定合併儲存格邊框顯示
	 * 
	 * @param region 
	 * 			合併區域
	 * @param sheet 

	 * @return
	 */
	private void setMergeCellBorder(CellRangeAddress region, HSSFSheet sheet) {
		RegionUtil.setBorderTop(BorderStyle.THIN, region, sheet);
		RegionUtil.setBorderBottom(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, region, sheet);
	}

}
