/* 
 * LELF412CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.LELF412C;

/** 逾期未覆審名單自辦覆審控制歷史檔 **/
public interface LELF412CDao extends IGenericDao<LELF412C> {

	LELF412C findByOid(String oid);

	List<LELF412C> findByMainId(String mainId);

	List<LELF412C> findByMainIdAndDataDate(String mainId, Date dataDate);

	List<LELF412C> findByMainIdDataDateAndBranch(String mainId, Date dataDate,
			String branch);

	List<LELF412C> findByMainIdDataDateBranchAndCustId(String mainId,
			Date dataDate, String custId, String dupNo);
}