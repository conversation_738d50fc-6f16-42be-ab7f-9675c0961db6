package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF412C;
import com.mega.eloan.lms.mfaloan.service.MisELF412CService;

@Service
public class MisELF412CServiceImpl extends AbstractMFAloanJdbc implements
		MisELF412CService {

	@Override
	public List<Map<String, Object>> getByKeyWithBasicData(String branch) {

		return getJdbc().queryForListWithMax(
				"MIS.ELF412C.GetByKeyWithBasicData",
				new String[] { branch, "%", "%", "SYS", "SYS" });

	}

	@Override
	public Map<String, Object> getByKeyWithBasicData(String branch,
			String custId, String dupNo) {

		return getJdbc()
				.queryForMap(
						"MIS.ELF412C.GetByKeyWithBasicData",
						new Object[] { branch, custId + "%", dupNo + "%",
								"SYS", "SYS" });

	}

	@Override
	public Map<String, Object> getDataWithPEO(String branch, String custId,
			String dupNo) {
		return getJdbc()
				.queryForMap(
						"MIS.ELF412C.GetByKeyWithBasicData",
						new Object[] { branch, custId + "%", dupNo + "%",
								"PEO", "PEO" });
	}

	private List<ELF412C> toELF412C(List<Map<String, Object>> rowData) {
		List<ELF412C> list = new ArrayList<ELF412C>();
		for (Map<String, Object> row : rowData) {
			ELF412C model = new ELF412C();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public ELF412C findByPk(String branch, String custId, String dupNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF412C.GetByKey",
				new String[] { branch, custId + "%", dupNo + "%" });
		List<ELF412C> list = toELF412C(rowData);
		if (list.size() == 1) {
			return list.get(0);
		} else {
			return null;
		}
	}

	@Override
	public List<ELF412C> findByBranch(String branch) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"MIS.ELF412C.GetByKey", new String[] { branch, "%", "%" });
		return toELF412C(rowData);
	}

	@Override
	public int updateELF412CNckdFlag(Date ELF412C_LRDATE,
			String ELF412C_NEWADD, String ELF412C_NEWDATE,
			String ELF412C_NCKDFLAG, Date ELF412C_NCKDDATE,
			String ELF412C_NCKDMEMO, Date ELF412C_NEXTNWDT,
			Date ELF412C_NEXTLTDT, Timestamp ELF412C_TMESTAMP,
			Date ELF412C_UPDDATE, String ELF412C_UPDATER,
			String ELF412C_BRANCH, String ELF412C_CUSTID, String ELF412C_DUPNO) {
		int count = this.getJdbc().update(
				"lms1800flow.update.elf412c.nckdflag",
				new Object[] { ELF412C_LRDATE, ELF412C_NEWADD, ELF412C_NEWDATE,
						ELF412C_NCKDFLAG, ELF412C_NCKDDATE,
						Util.trimSizeInOS390(ELF412C_NCKDMEMO, 200),
						ELF412C_NEXTNWDT, ELF412C_NEXTLTDT, ELF412C_TMESTAMP,
						ELF412C_UPDDATE, ELF412C_UPDATER, ELF412C_BRANCH,
						ELF412C_CUSTID, ELF412C_DUPNO });
		return count;
	}

}
