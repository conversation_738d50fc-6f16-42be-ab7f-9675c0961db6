/* 
 * CMS1301S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.utils.CapAppContext;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          </ul>
 */
public class CLS1131S01EPanel extends Panel {

	
	private CLSService clsService;

	private CodeTypeService codeTypeService;
    
	private SysParameterService sysparamService;
	
	private static final long serialVersionUID = 1L;

	/**
	 * @param id
	 */
	public CLS1131S01EPanel(String id) {
		super(id);
		this.clsService = CapAppContext.getBean("CLSService");
		this.codeTypeService = CapAppContext.getBean("CodeService");
		this.sysparamService = CapAppContext.getBean("sysParameterServiceImpl");
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean on_J_108_0277_V2 = clsService.is_function_on_codetype("J-108-0277_V2");
		model.addAttribute("DivOneBtnQueryEJ_ETCH_V0", false);
		model.addAttribute("DivOneBtnQueryEJ_ETCH_V1", !on_J_108_0277_V2);
		model.addAttribute("DivOneBtnQueryEJ_ETCH_V2", on_J_108_0277_V2);
		
        List<CodeType> amlBadNewsList = codeTypeService
                .findByCodeTypeList("amlBadNewsCode");
        StringBuilder amlBadNewsSB = new StringBuilder();
        for (CodeType codeType : amlBadNewsList) {
            amlBadNewsSB.append("<label>")
                    .append("<input type='checkbox' name='amlBadNews' id='amlBadNews' value='")
                    .append(codeType.getCodeValue()).append("' />")
                    .append(codeType.getCodeDesc()).append("</label>")
                    .append("<br/>");
        }
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// Label amlBadNewsLabel = new Label("amlBadNewsLabel",
		// amlBadNewsSB.toString());
		// amlBadNewsLabel.setEscapeModelStrings(false);
		// add(amlBadNewsLabel);
		model.addAttribute("amlBadNewsLabel", amlBadNewsSB.toString());
        //======
        if(true){
            String link_laaWord_url = "http://pip.moi.gov.tw/V2/G/SCRG0404.aspx";
            Map<String, String> url_map = clsService.get_codeTypeWithOrder("C101S01E_laaWord_url");
            if(url_map.containsKey("url")){
            	link_laaWord_url = url_map.get("url");
            }
            StringBuilder sbLaa = new StringBuilder();
            sbLaa.append("<a ");
            sbLaa.append(" href=\""+link_laaWord_url+"\" ");
            sbLaa.append(" target=\"_blank\" ");
            sbLaa.append(">");
            sbLaa.append("內政部‧開業地政士查詢");
            sbLaa.append("</a>");
			// UPGRADE: 前端須配合改Thymeleaf的樣式
			// Label laaLabel = new Label("C101S01E_laaWord_url",
			// sbLaa.toString());
			// laaLabel.setEscapeModelStrings(false);
			// add(laaLabel);
			model.addAttribute("C101S01E_laaWord_url", sbLaa.toString());
        }
        // 判斷查詢WITCHER_FIN是否為已開放分行，ALL代表全開放
        boolean showDivWitcherFin = false;
        String branchs = sysparamService.getParamValue("WITCHER_FIN_BRANCHS");
        if ("ALL".equals(branchs) || Arrays.asList(branchs.split(",")).contains(user.getUnitNo())) {
        	showDivWitcherFin = true;
        }
		model.addAttribute("DivWitcherFin", showDivWitcherFin);
	}
}
