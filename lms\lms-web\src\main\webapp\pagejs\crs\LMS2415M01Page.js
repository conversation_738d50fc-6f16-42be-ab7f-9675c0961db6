$(function(){
	// 權限
    var auth = (responseJSON ? responseJSON.Auth : {});
    // $("#L170M01aForm").readOnlyChilds(auth.readOnly);
    if (auth.readOnly || responseJSON.mainDocStatus != "010") {
    	$("#btnUpdate491").hide();
    }
$(function(){    
    setCloseConfirm(true);/* 設定關閉此畫面時要詢問 */
    $.form.init({
        formHandler: "lms2415m01formhandler",
        formPostData: {
            formAction: "queryC2415m01a",
            mainId: responseJSON.mainId,
            custId: responseJSON.custId,
            dupNo: responseJSON.dupNo
        },
        loadSuccess: function(json){
        	if (responseJSON.page == "01") {
        		$("#btnSave").hide();
        	}else if (responseJSON.page == "02") {
//            	$("#retrialDate").val(json.c241m01a.retrialDate);
            	$("#show_totBal").val(json.show_totBal);
            	$("#show_totQuota").val(json.show_totQuota);
            	$("#show_specifyCycle").val(json.show_specifyCycle);
            	$("#show_nCkdFlag").val(json.show_nCkdFlag);
            	$("#show_nCkdMemo").val(json.show_nCkdMemo);
            	$("#show_lastRetrialDate").val(json.show_lastRetrialDate);
            	$("#show_retrialKind").val(json.show_retrialKind);
            	$("#show_shouldReviewDate").val(json.show_shouldReviewDate);
            	
            	
//            	$("#lnDataDate").val(json.show_lnDataDate);
            	
            	
        	}else if (responseJSON.page == "03") {
                $("#curapplyfor").localSave();
                $.ajax({
                    type: "POST",
                    handler: "lms2415m01formhandler",
                    action: "queryC2415m01c",
                    data: {
                        mainId: responseJSON.mainId,
                        custId: responseJSON.custId,
                        dupNo: responseJSON.dupNo,
                        page: responseJSON.page,
                        txCode: responseJSON.txCode
                    }
                    }).done(function(responseData){
                    	if(json.rptId == ""){	//舊版
                    		createC241M01CData(responseData);
                        } else if(json.rptId == "Ver201809"){	//J-107-0128海外改格式
                        	createC241M01CDataV2(responseData);
                        } else if(json.rptId == "Ver202209"){	//J-107-0128海外改格式	
                        	createC241M01CDataV3(responseData);
                        }

                        if (responseJSON.mainDocStatus != "010" || auth.readOnly) {
                            $(".doc-tabs").lockDoc();
                            // $("#tabForm").readOnlyChilds(true)//.find("button").remove();
                        }
                });// 第二個AJAX結束
            }else if (responseJSON.page == "04") {
            	$("#C241M01dForm").setData(json.c241m01d,false);
            }else if (responseJSON.page == "06") {
                if ($("#id").val() != "") 
                    $("#id").readOnly(true);
                else 
                    $("#id").readOnly(false);
            }

            if (responseJSON.mainDocStatus != "010" || auth.readOnly) {
            	if (responseJSON.page == "02") {
                	$("#retrialDate").readOnly(true);
            	}else{
                    $(".doc-tabs").lockDoc();
            	}
            }
        }
    });
    
    var $gridAddToC240M01A = $("#gridAddToC240M01A").iGrid({
        handler: 'lms2405gridhandler',        
        height: 200,
        shrinkToFit: true,
        sortname: "expectedRetrialDate|branchId",
        sortorder: "desc|asc",
        postData: {
        	docStatus: "0A0",
        	formAction: "queryMain"
        },
        colModel: [ {
            colHeader: i18n.lms2405v01['C240M01A.expectedRetrialDate'],//"預計複審日",
            name: 'expectedRetrialDate',
            width: 60,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.abstracteloan['doc.branchName'],//"分行名稱",
            name: 'uid',
            width: 80,
            sortable: true
        }, {
            colHeader: i18n.lms2405v01['C240M01A.reQuantity'],//"覆審筆數",
            name: 'reQuantity',
            width: 40,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms2405v01['C240M01A.dataEndDate'],//"覆審名單截至月",
            name: 'dataEndDate',
            align: "center",
            width: 70,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            },
            sortable: true
        }, {
            colHeader: i18n.lms2405v01['C240M01A.updater'],//"最後異動人員",
            name: 'updater',
            align: "center",
            width: 60,
            sortable: true,
            hidden: true
        }, {
            colHeader: i18n.lms2405v01['C240M01A.updateTime'],//"最後異動日期",
            name: 'updateTime',
            align: "center",
            width: 60,
            sortable: true,
            hidden: true
        }, {
        	//J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
            colHeader: i18n.lms2405v01['C240M01A.status'],//"處理狀態",
            name: 'status',
            align: "center",
            width: 40,
            sortable: true,
            hidden: true
        }, {
        	//J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
            colHeader: i18n.lms2405v01['C240M01A.rpaDate'],//"RPA執行日期",
            name: 'rpaDate',
            align: "center",
            width: 40,
            sortable: true,
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "branchId",
            name: 'branchId',
            hidden: true
        }]        
    });
    
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
    	saveData(true);
    }).end().find("#btnSend").click(function(){
    	if (checkReadonly()) {
    		btnSend();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforeSend"], function(b){
                if (b) {
                 	saveData(false,btnSend);
                }
            });
        }
        
    }).end().find("#btnAccept").click(function(){
        flowAction({
        	flowActionResult: "Y"
        });
    }).end().find("#btnReturn").click(function(){
    	flowAction({
        	flowSeq : "2",
        	flowActionResult: "N"
        });
    }).end().find("#btnPrint").click(function(){
    	if (checkReadonly()) {
    		btnPrint();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                 	saveData(false,btnPrint);
                }
            });
        }
        
    }).end().find("#btnCheck").click(function(){
        openCheck();
    }).end().find("#btnUpdate491").click(function(){
    	if (checkReadonly()) {
		   update491Now();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforeSend"], function(b){
                if (b) {
                 	saveData(false,update491Now);
                }
            });
        }
    }).end().find("#btnSend4").click(function(){
		$.ajax({
			handler : "lms2415m01formhandler",
			type : "POST",
			dataType : "json",
			data : $.extend($("#lms2415s05BOX").serializeData(),
				{
					formAction : "queryC2415m01d",
					mainId : responseJSON.mainId,
					txCode : responseJSON.txCode
				})
			}).done(function(responseData) {
				$("#lms2415s05BOXtext").val(responseData.branchComm);
				$("#lms2415SENT").thickbox({
					// 登錄受檢單位洽辦情形，登錄完成後請按編製完成
					title : i18n.lms2415m01['lms2415.tit06'],
					width : 500,
					height : 300,
					align : 'center',
					valign : 'bottom',
					modal : false,
					i18n : i18n.def,
					buttons : {
						"sure" : function(showMsg) {
							if($("#lms2415s05BOX").valid()){
								$.ajax({
    								handler : "lms2415m01formhandler",
    								type : "POST",
    								dataType : "json",
    								data : $.extend($("#lms2415s05BOX").serializeData(),
    									{
    										formAction : "save2415M5",
    										mainId : responseJSON.mainId,
    										dupNo : responseJSON.dupNo,
    										mainOid : responseJSON.oid,
    										page : 5,
    										showMsg : true,
    										txCode : responseJSON.txCode
    									})
    								}).done(function(responseData) {
    									$("#lms2415s05BOX").val(responseData.branchComm);
    	    							if (responseJSON.page == "04") {
    	        							$("#branchComm").val(responseData.branchComm);
    	    							}
    							});
    							$.thickbox.close();
							}
							

						},
						"cancel" : function() {
							$.thickbox.close();
						}
					}

				});
		});
    }).end().find("#btnAddToC240M01A").click(function(){
    	// 連結至覆審工作底稿
    	$gridAddToC240M01A.trigger("reloadGrid");
		$("#div_AddToC240M01A").thickbox({ // 使用選取的內容進行彈窗
	        title: '', width: 410, height: 340, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                	var data = $gridAddToC240M01A.getSingleData();
                    if (data) {
                    	API.confirmMessage("是否指定為【"+i18n.lms2405v01['C240M01A.expectedRetrialDate']
                    		+"："+data.expectedRetrialDate+"】之覆審名單？", function(result){
        		            if (result) {
        		            	$.ajax({
        		            		handler: "lms2415m01formhandler", 
        		            		data: {
        		            			formAction: "saveC240M01B",
        		            			c240MainId: data.mainId,
        		            			c241MainId: responseJSON.mainId
        		            		}
                            		}).done(function(json){
                            			$.thickbox.close();
                            			$.ajax({
                            	            type: "POST",
                            	            handler: "lms2405m01formhandler",
                            	            data: {
                            	                formAction: "number",
                            	                mainOid: data.oid
                            	            }
                            	            }).done(function(){
                            	            	$("#btnAddToC240M01A").hide();
                            	        });
    		            		});
        		        	}
        		    	});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	});
    // ==============================================================================================================
    
    
    
 
    
    // 變更為最新版本
    $('#svnToLatest').click(function(){
    	$.ajax({
            type: "POST",
            handler: "lms2415m01formhandler",
            action: "queryC2415m01c",
            data: {
                mainId: responseJSON.mainId,
                custId: responseJSON.custId,
                dupNo: responseJSON.dupNo
            }
            }).done(function(responseData){
            	var array = responseData.C241M01CArray;
                for (var i = 0; i < array.length; i++) {
                    var json = array[i];
                    $("#" + encodeHTML(json.itemNo)).remove();
           	 	}
            	$.ajax({
                    type: "POST",
                    handler: "lms2415m01formhandler",
                    action: "queryC2415m01c",
                    data: {
                        mainId: responseJSON.mainId,
                        custId: responseJSON.custId,
                        dupNo: responseJSON.dupNo,
                        deleteData: "Y"
                    }
                    }).done(function(responseData2){
                    	createC241M01CDataV3(responseData2);
            	})
    	})
    })
    
  //------------------------------------------ 更新覆審控制檔 ----------------------------------------
   $("#_lms2415s05ButtonUPDATE").click(function update491(){
	   if (checkReadonly()) {
		   update491Now();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforeSend"], function(b){
                if (b) {
                 	saveData(false,update491Now);
                }
            });
        }
    });
    
    
});

function doFixCheck(){
	if($("input[name=doFix]:checked").val() == 'N'){
		$("input[name=specifyCycle][value='1']").prop('checked',true); 
		$("#showCycle").hide();
	}else{
		$("input[name=specifyCycle][value='0']").prop('checked',true); 
		$("#showCycle").show();
	}
}

//=================================================================================
//畫面切換table 所需設定之資料 如無設定 則直接切換
$.extend(window.tempSave, {
    handler: "lms2415m01formhandler",
    action: "tempSave",
    sendData: function(){
    	if (responseJSON.page == "02") {
            return $("#C241M01aForm_s02").serializeData();
        }else if (responseJSON.page == "03") {
            return $("#C241M01cForm").serializeData();
        }else if (responseJSON.page == "04") {
                var json = {};
                var array = {};
                array["LMS2415S04Form01"] = $("#C241M01dForm").serializeData();
                mergeJSON(json, array.LMS2415S04Form01);
                return json;
       }
    }
});

//合併多個serializeData JSON 成一個serializeData JSON
function mergeJSON(json, array){
    for (var data in array) {
        json[data] = array[data];
    }
}

//驗證readOnly狀態
function checkReadonly(){
	
	var auth = (responseJSON ? responseJSON.Auth : {}); //權限
	//帳務不管狀態都不鎖
    if (auth.readOnly || responseJSON.mainDocStatus != "010") {
    	if (responseJSON.page == "02") {
        	$("#retrialDate").readOnly(true);
    		return false ;
    	}else{
			return true ;
    	}
    	return false ;
	}
}

function createC241M01CData(responseData){
	//typeJson.A typeJson.B typeJson.C
    var typeJson = responseData.typeJson;
    var array = responseData.C241M01CArray;
    //後端塞typeJson的值有哪幾種
    var table = $("#C241M01cForm").find("#tableXXX");
    for (var type in typeJson) {
    	var text;
        var rowspanCount = 0;
        if (type == "A") {
            text = i18n.lms2415m01["C241M01a.titleA"];
            rowspanCount = typeJson.A ;
        }else if (type == "B") {
            text = i18n.lms2415m01["C241M01a.titleB"];
            rowspanCount = typeJson.B ;
        }else {
            text = i18n.lms2415m01["C241M01a.titleC"];
            rowspanCount = typeJson.C ;
        }
        showItemTable(array,rowspanCount,text,type,table)
        //覆審項次(itemNo)有幾筆
    }//END ARRAY
    // 前次覆審有無應行改善事項？ 
    $("input[name=chkResult14]").click(function(){
        //alert("!!!!");
        if ($(this).val() == "N") {
            $("#show1").hide();
            $("#show2").hide();
        }
        else {
            $("#show1").show();
            $("#show2").show();
        }
    });// input[name=chkResult19] END
    
    if ($("input[name=chkResult14]:checked").val() == "N") {
        $("#show1").hide();
        $("#show2").hide();
    }
    else {
        $("#show1").show();
        $("#show2").show();
    }
}

function btnPrint(){
	gridPrint.trigger("reloadGrid");
    $("#printView").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.def['print'],
        width: 700,
        height: 400,
        modal: false,
        buttons: (function(){
            var btn = {};
            btn[i18n.def['print']] = function(){
                var count = 0;
                var content = "";
                var id = gridPrint.getGridParam('selarrrow');
                for (var i = 0; i < id.length; i++) {
                    if (id[i] != "") {
                        var datas = gridPrint.getRowData(id[i]);
                        content = content +
                        datas.rpt +
                        "^" +
                        datas.oid +
                        "|";
                        count++;
                    }
                }
                
                if (content.length != 0) {
                    content = content.substring(0, content.length - 1);
                }
                if (count == 0) {
                    CommonAPI.showMessage(i18n.def['grid.selrow']);
                }
                else {
                    $.form.submit({
                        url: "../../simple/FileProcessingService",
                        target: "_blank",
                        data: {
                            mainId: responseJSON.mainId,
                            rptOid: content,
                            fileDownloadName: "lms2415r01.pdf",
                            serviceName: "lms2415r01rptservice"
                        }
                    });
                }
            }
            btn[i18n.def['close']] = function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
            return btn;
        })()
    });
}

function saveData(showMsgs,tofn){
    var auth = (responseJSON ? responseJSON.Auth : {});
    if (auth.readOnly || responseJSON.mainDocStatus != "010") {
    	tofn();
    }else{
    	var validResult = true;
    	if (responseJSON.page == "02") {
    		if(!$("#C241M01aForm_s02").valid()){
    			validResult = false;
    		}
    	}else if (responseJSON.page == "03") {
    		if(!$("#C241M01cForm").valid()){
    			validResult = false;
    		}
    	}else if (responseJSON.page == "04") {
    		if(!$("#C241M01dForm").valid()){
    			validResult = false;
    		}
    	}
    	if(validResult){
    		$.ajax({
                type: "POST",
                handler: "lms2415m01formhandler",
                action: "saveAll",
                data: {
                    custId: responseJSON.custId,
                    dupNo: responseJSON.dupNo,
                    mainId: responseJSON.oid,
                    page: responseJSON.page,
                    retrialDate: $("#retrialDate").val(),
                    showMsg: showMsgs,
                    txCode: responseJSON.txCode,
                    C241M01cForm: JSON.stringify($("#C241M01cForm").serializeData()),
                    C241M01dForm: JSON.stringify($("#C241M01dForm").serializeData())
                    
                }
                }).done(function(responseData){
                    $('#C241M01aForm').injectData(responseData.C241M01aForm);
                	 if (responseJSON.page == "03") {
                         $('#C241M01cForm').injectData(responseData.C241M01cForm);
                	 }else if (responseJSON.page == "04") {
                         $('#C241M01dForm').injectData(responseData.C241M01dForm);
                	 }
         	        //執行列印
         	        if (!showMsgs && tofn) {
         	             tofn();
         	        }
                    
            });// 第二個AJAX結束
            // }//驗證的括弧
    	}
    }
	
}

function update491Now(){
	$.ajax({
        handler: "lms2415m01formhandler",
        type: "POST",
        dataType: "json",
        data: {
            formAction: "checkC2415Data",
            mainOid: responseJSON.oid,
            mainId: responseJSON.mainId,
            txCode: responseJSON.txCode,
            checkPoint : "N"
        }
        }).done(function(responseData){
        	if (responseData.check) {
        		if($("#upDateCheck").val() != ''){
        			CommonAPI.confirmMessage(i18n.lms2415m01["C241M01A.check01"], function(b){
                        if (b) {
                        	updateElf491Now(responseData.retrialKind);
                        }
                    })
        		}else{
        			updateElf491Now(responseData.retrialKind);
        		}
            }// CHECK END
    });
}


function updateElf491Now(retrialKind){
	if(retrialKind.indexOf('99') != '-1'){
		$("#update491Box").thickbox({
			// 登錄受檢單位洽辦情形，登錄完成後請按編製完成
			title : i18n.lms2415m01['C241M01A.check02title'],
			width : 700,
			height : 300,
			align : 'center',
			valign : 'bottom',
			modal : false,
			i18n : i18n.def,
			buttons : {
				"sure" : function(showMsg) {
					$.thickbox.close();
					// 更新覆審控制檔 
			        $.ajax({
			            handler: "lms2415m01formhandler",
			            type: "POST",
			            dataType: "json",
			            data: {
			                formAction: "updateElf491",
			                mainOid: responseJSON.oid,
			                mainId: responseJSON.mainId,
			                dupNo: responseJSON.dupNo,
			                custId: responseJSON.custId,
			                doFix: $("input[name=doFix]:checked").val(),
			                specifyCycle: $("input[name=specifyCycle]:checked").val(),
			                showMsg: true,
			                txCode: responseJSON.txCode
			            }
			           // target: mainOid,
			            }).done(function(responseData){
				             var st =responseData.st;
				             if(st){
				            	 if (responseJSON.page == "02") {
				            		 $("#show_specifyCycle").val(responseData.show_specifyCycle);
				            	 }
				            	 $("#upDate").val(responseData.upDate);
				            	 $("#upDateCheck").val(responseData.upDate);
				             }
			        });
				},
				"cancel" : function() {
					$.thickbox.close();
				}
			}

		});
	}else{
		// 更新覆審控制檔 
        $.ajax({
            handler: "lms2415m01formhandler",
            type: "POST",
            dataType: "json",
            data: {
                formAction: "updateElf491",
                mainOid: responseJSON.oid,
                mainId: responseJSON.mainId,
                dupNo: responseJSON.dupNo,
                custId: responseJSON.custId,
                specifyCycle: $("#specifyCycle:checked").val(),
                showMsg: true,
                txCode: responseJSON.txCode
            }
           // target: mainOid,
            }).done(function(responseData){
		             var st =responseData.st;
		             if(st){
		            	 $("#upDate").val(responseData.upDate);
		            	 CommonAPI.triggerOpener("gridview", "reloadGrid");
		             }
        });
	}
}

function btnSend(){
	$.ajax({
        handler: "lms2415m01formhandler",
        type: "POST",
        dataType: "json",
        data: {
            formAction: "checkC2415Data",
            mainOid: responseJSON.oid,
            mainId: responseJSON.mainId,
            txCode: responseJSON.txCode
        }
        }).done(function(responseData){
        	if(responseData.check){
            	flowAction({
                	flowActionResult: "Y"
                });
        	}
    });
}

//待覆核 - 覆核
function openCheck(){
    $("#openCheckBox").thickbox({
        // lms2415.tit07=覆核
        title: i18n.lms2415m01['lms2415.tit07'],
        width: 100,
        height: 150,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
            
                var val = $("[name=checkRadio]:checked").val();
                if (!val) {
                    // lms2415.check=請選擇
                    return CommonAPI.showMessage(i18n.lms2415m01['lms2415.check']);
                }
                $.thickbox.close();
                switch (val) {
                    case "1":
                        // 一般退回到編製中01O
                        // lms2415.check1=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                        CommonAPI.confirmMessage(i18n.lms2415m01['lms2415.check1'], function(b){
                            if (b) {
                                flowAction({
                                	flowSeq : "2",
                                	flowActionResult: "N"
                                });
                            }
                        });
                        
                        break;
                    case "2":
                        // 核定
                        // lms2415.check2=該案件是否核准？確定請按【確定】，否則請按【取消】離開
                        CommonAPI.confirmMessage(i18n.lms2415m01["lms2415.check2"], function(b){
                            if (b) {
                                flowAction({
                                	flowSeq : "2",
                                	flowActionResult: "Y",
                                	flowAction: true
                                });
                                $.thickbox.close();
                            }
                        });
                        break;
                }
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}
// ==============================================================================================================
function flowAction(sendData){
    $.ajax({
        type: "POST",
        handler: "lms2415m01formhandler",
        data: $.extend({
            formAction: "flowAction",
            mainOid: responseJSON.oid,
            oid: responseJSON.oid,
            txCode: responseJSON.txCode
        }, (sendData || {}))
        }).done(function(){
            CommonAPI.triggerOpener("gridview", "reloadGrid");
            setCloseConfirm(false);
            window.close();
    });
}


function showItemTable(array,rowspanCount,text,type,table){
	rowspanCount = Number(encodeHTML(rowspanCount));
	for (var i = 0; i < array.length; i++) {
        var json = array[i];
		if(type == json.itemType){
			var itemSeq = encodeHTML(json.itemSeq);
			var itemNo = encodeHTML(json.itemNo)
			var str = "";
        	str = "<tr valign='top' " + " id='" + itemNo + "'>";
            if (rowspanCount != 0) {
            	str += "<td width='15%' rowspan='" + rowspanCount + "' align='center' valign='middle' class='hd1 style42' style='text-align:center' >";
                str += text;
                str += "</td>";
                rowspanCount = 0 ;
            }
            str += "<td width='5%' ><input id='itemNo' name='itemNo' type='hidden'class='style999'value='" + itemSeq + "' />" + itemSeq + "</td>";
            str += " <td width='55%'>" + encodeHTML(json.chkItem) + "</td>";
            str += " <td width='55%'>";
            if (json.itemNo == "B009") {
                str += " <label><input id='chkResult" + itemSeq + "' name='chkResult" + itemSeq + "' type='radio' value='N' />";
                str += i18n.lms2415m01["C241M01a.haveNO"];
                str += "</label> <label><input id='chkResult" + itemSeq + "' name='chkResult" + itemSeq + "'type='radio' value='Y' />";
                str += i18n.lms2415m01["C241M01a.haved"];
                str += "</label> <label><input id='chkResult" + itemSeq + "' name='chkResult" + itemSeq + "' type='radio' value='K' />";
                str += i18n.lms2415m01["C241M01a.an"];
                str += "</label>";
            }else if (json.itemNo == "C004") {
                    str += " <label><input id='chkResult" + itemSeq + "' name='chkResult" + itemSeq + "' type='radio' value='N' />";
                    str += i18n.lms2415m01["C241M01a.n"];
                    str += "</label> <label><input id='chkResult" + itemSeq + "' name='chkResult" + itemSeq + "'type='radio' value='Y' />";
                    str += i18n.lms2415m01["C241M01a.y"];
                    str += "</label> <label><input id='chkResult" + itemSeq + "' name='chkResult" + itemSeq + "' type='radio' value='K' />";
                    str += i18n.lms2415m01["C241M01a.an"];
                    str += "</label>";
           } else {
                    str += " <label><input id='chkResult" + itemSeq + "' name='chkResult" + itemSeq + "' type='radio' value='Y' />";
                    str += i18n.lms2415m01["C241M01a.y"];
                    str += "</label> <label><input id='chkResult" + itemSeq + "' name='chkResult" + itemSeq + "'type='radio' value='N' />";
                    str += i18n.lms2415m01["C241M01a.n"];
                    str += "</label> <label><input id='chkResult" + itemSeq + "' name='chkResult" + itemSeq + "' type='radio' value='K' />";
                    str += i18n.lms2415m01["C241M01a.an"];
                    str += "</label>";
           }
            str += " </td>";
            str += " <td width='27%' >";
            // 前次覆審有無應行改善事項？(引藏)
            if (json.itemNo == "B009") {
            	str += "<div id='show1'><label><input type='radio' id='chkPreReview' name='chkPreReview' value='Y'/>";
            	str += i18n.lms2415m01["C241M01a.cy"];
                str += "</label> <label><input type='radio' id='chkPreReview' name='chkPreReview' value='N'/>";
                str += i18n.lms2415m01["C241M01a.cn"];
                str += "</label> <label><input type='radio' id='chkPreReview' name='chkPreReview' value='K'/>";
                str += i18n.lms2415m01["C241M01a.an"];
                str += "</label> </div>";
                str += "<div id='show2'><input type='text' id='chkText" + itemSeq + "' name='chkText" + itemSeq + "' size='30' maxlength='60' maxlengthC='20' /> </div>";
            }
            else {
                str += "<input type='text' id='chkText" + itemSeq + "' name='chkText" + itemSeq + "' size='30'  maxlength='60' maxlengthC='20' /> ";
            }
            str += " </td>";
            str += " </tr>";
            table.append(str);
            
            $("input[type='radio'][name='chkResult" + itemSeq + "'][value=" + json.chkResult + "]").prop("checked", true);
            $("input[type='checkbox'][name='chkCheck'][value=" + json.chkCheck + "]").prop("checked", true);
            //$("[name='chkText" + itemSeq + "']").val(json.chkText);
            var chkTextInput = $("[name='chkText" + itemSeq + "']");
            chkTextInput.val(json.chkText); 
            if (json.itemNo == "B009") {
            	$("input[name=chkPreReview][value="+json.chkPreReview+"]").prop("checked",true);
            }
		}
        //$("body").append(str);
    }//END len的if
}

//=======================================================================================
////J-107-0128海外改格式  新增版本資訊 	2018海外新版覆審報告表
function createC241M01CDataV2(responseData){
    var typeJson = responseData.typeJson;
    var table = $("#C241M01cForm").find("#tableXXX");
    var array = responseData.C241M01CArray;
	var rptid = responseData.rptId;		//代入報表版本
	$("input[type='hidden'][name='rptid']").val(rptid);
	var country = responseData.Country;		//國別	CA英文與他國不同
	var locale = userInfo.userLocale;		//語系
	var docFmt = responseData.docFmt;	//是否為實際覆審報告(土建融)

	var arrayZ = new Array();
    var arrayY = new Array();
	for (var i = 0; i < array.length; i++) {
		if(array[i].itemType == "Z"){
			arrayZ.push(array[i]);
		} else if(array[i].itemType == "Y"){
			arrayY.push(array[i]);
		}
	}
	
    //後端塞typeJson的值有哪幾種
    for (var type in typeJson) {
    	var text;
        var rowspanCount = 0;
        if (type == "A") {
        	if(locale == "en"){
        		text = (country == "CA") ? i18n.lms2415m01["C241M01aV2_CA.titleA"] : i18n.lms2415m01["C241M01aV2.titleA"];
        	} else {
        		text = i18n.lms2415m01["C241M01a.titleA"];
        	}
            rowspanCount = typeJson.A ;
        }else if (type == "B") {
        	if(locale == "en"){
        		text = (country == "CA") ? i18n.lms2415m01["C241M01aV2_CA.titleB"] : i18n.lms2415m01["C241M01aV2.titleB"];
        	} else {
        		text = i18n.lms2415m01["C241M01a.titleB"];
        	}
            rowspanCount = Number(typeJson.B) + Number((typeJson.Z != undefined) ? typeJson.Z : 0);
            if(docFmt == "A"){	//土建融	否-->不用輸入B007
            	rowspanCount = Number(rowspanCount)-1;
            }
        }else {
            text = i18n.lms2415m01["C241M01a.titleC"];
            rowspanCount = Number(typeJson.C) + Number((typeJson.Y != undefined) ? typeJson.Y : 0);
        }
        rowspanCount = Number(encodeHTML(rowspanCount));
        for (var i = 0; i < array.length; i++) {
        	var json = array[i];
			var itemNo = encodeHTML(json.itemNo);
        	if (json.itemType == type) {
        		if (type == "Z" || type == "Y") {
            		continue;
            	}
        		if(docFmt == "A" && itemNo == "B007"){
            		continue;	//是否為土建融	否-->不用輸入B007
            	}
        		var str = "";
                str = "<tr valign='top' " + " id='" + itemNo + "'>";
                if (rowspanCount != 0) {
                	str += "<td width='3%' rowspan='" + rowspanCount + "' align='center' valign='middle' class='hd1 style42' style='text-align:center' >";
                    str += text;
                    str += "</td>";
                    rowspanCount = 0 ;
                }
                
                //附表
                if(itemNo == "B011" && arrayZ != undefined){	//B010 -> B011前在插入附表
                	table.append(createAttachedTable("Z",arrayZ,country,locale));
                } else if(itemNo == "C002" && arrayY != undefined){	//C001 -> C002前在插入附表
                	table.append(createAttachedTable("Y",arrayY,country,locale));
                }
                
                if (json.itemSeq == 14 || json.itemSeq == 17) {	//B007	B010
                	str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + itemNo + "' /></td>";
                } else if (json.itemSeq > 14 && json.itemSeq < 17) {
                	var itemSeq = json.itemSeq-1;
                	str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + itemNo + "' />" + itemSeq + "</td>";
                } else if (json.itemSeq > 17) {
                	var itemSeq = json.itemSeq-2;
                	str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + itemNo + "' />" + itemSeq + "</td>";
                } else {
                	str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + itemNo + "' />" + encodeHTML(json.itemSeq) + "</td>";
                }
                
                str += " <td width='45%'>" + encodeHTML(json.chkItem) + "</td>";
                str += " <td width='22%'>";
                if (itemNo == "B011") {	//前次覆審有無應行改善事項？
                    str += "<label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='N' /><span class='style999'>";
                    if(locale == "en"){
                    	str += i18n.lms2415m01["C241M01a.n"];
                    } else {
                    	str += i18n.lms2415m01["C241M01a.haveNO"];
                    }
                    str += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='Y' /><span class='style999'>";
                    if(locale == "en"){
                    	str += i18n.lms2415m01["C241M01a.y"];
                    } else {
                    	str += i18n.lms2415m01["C241M01a.haved"];
                    }
                    str += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='K' /><span class='style999'>";
                    str += i18n.lms2415m01["C241M01a.an"];
                    str += "</label></td>";
                } else if (itemNo == "B013" || itemNo == "C005") {
                	str += " <label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='N' /><span class='style999'>";
                	str += i18n.lms2415m01["C241M01a.n"];
                	str += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='Y' /><span class='style999'>";
                	str += i18n.lms2415m01["C241M01a.y"];
                	str += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='K' /><span class='style999'>";
                	str += i18n.lms2415m01["C241M01a.an"];
                	str += "</label></td>";
                } else {
                    str += " <label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='Y' /><span class='style999'>";
                    str += i18n.lms2415m01["C241M01a.y"];
                    str += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='N' /><span class='style999'>";
                    str += i18n.lms2415m01["C241M01a.n"];
                    str += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='K' /><span class='style999'>";
                    str += i18n.lms2415m01["C241M01a.an"];
                    str += "</label></td>";
                }
                str += " </td>";
                str += " <td width='31%' >";
                if (itemNo == "B011") {	// 前次覆審有無應行改善事項？(引藏)
                	str += "<div id='show1'><label><input type='radio' id='chkPreReview' name='chkPreReview' value='Y'/>";
                	str += i18n.lms2415m01["C241M01a.cy"];
                    str += "</label> <label><input type='radio' id='chkPreReview' name='chkPreReview' value='N'/>";
                    str += i18n.lms2415m01["C241M01a.cn"];
                    str += "</label> <label><input type='radio' id='chkPreReview' name='chkPreReview' value='K'/>";
                    str += i18n.lms2415m01["C241M01a.an"];
                    str += "</label> </div>";
                    str += "<div id='show2'><input type='text' id='chkText" + itemNo + "' name='chkText" + itemNo + "' size='30' maxlength='60' maxlengthC='20' /> </div>";
                }
                else {
                    str += "<input type='text' id='chkText" + itemNo + "' name='chkText" + itemNo + "' size='30'  maxlength='60' maxlengthC='20' /> ";
                }
                str += " </td>";
                str += " </tr>";
                table.append(str);
                
                $("input[type='radio'][name='chkResult" + itemNo + "'][value=" + json.chkResult + "]").prop("checked", true);
                $("[name='chkText" + encodeHTML(itemNo) + "']").val(json.chkText);  
                if (itemNo == "B011") {
                	$("input[type='radio'][name=chkPreReview][value="+json.chkPreReview+"]").prop("checked",true);
                }
        	}
        }
        //覆審項次(itemNo)有幾筆
    }//END ARRAY
    // 前次覆審有無應行改善事項？ 
    $("input[name=chkResultB011]").click(function(){
        if ($(this).val() == "N") {
            $("#show1").hide();
            $("#show2").hide();
        } else {
            $("#show1").show();
            $("#show2").show();
        }
    });
    
    if ($("input[name=chkResultB011]:checked").val() == "N") {
        $("#show1").hide();
        $("#show2").hide();
    } else {
        $("#show1").show();
        $("#show2").show();
    }
    
    //塞附表值
	for (var i = 0; i < array.length; i++) {
		var itemNo = encodeHTML(array[i].itemNo);
		if(array[i].itemType == "Z" || array[i].itemType == "Y" ){
			$("input[type='radio'][name='chkResult" + array[i].itemNo + "'][value=" + array[i].chkResult + "]").prop("checked", true);
            $("[name='chkText" + itemNo + "']").val(array[i].chkText);
		}
	}
}

//=======================================================================================
////J-111-0405海外改格式  新增版本資訊 	2022海外新版覆審報告表
function createC241M01CDataV3(responseData){
    var typeJson = responseData.typeJson;
    var table = $("#C241M01cForm").find("#tableXXX");
    var array = responseData.C241M01CArray;
	var rptid = responseData.rptId;		//代入報表版本
	$("input[type='hidden'][name='rptid']").val(rptid);
	var country = responseData.Country;		//國別	CA英文與他國不同
	var locale = userInfo.userLocale;		//語系
	var docFmt = responseData.docFmt;	//是否為實際覆審報告(土建融)

	var arrayZ = new Array();
    var arrayY = new Array();
    var arrayX = new Array();
	for (var i = 0; i < array.length; i++) {
		if(array[i].itemType == "Z"){
			arrayZ.push(array[i]);
		} else if(array[i].itemType == "Y"){
			arrayY.push(array[i]);
		} else if(docFmt == "B" && array[i].itemType == "X"){
			arrayX.push(array[i]);
		}
	}
	
    //後端塞typeJson的值有哪幾種
    for (var type in typeJson) {
    	var text;
        var rowspanCount = 0;
        if (type == "A") {
        	if(locale == "en"){
        		text = (country == "CA") ? i18n.lms2415m01["C241M01aV2_CA.titleA"] : i18n.lms2415m01["C241M01aV2.titleA"];
        	} else {
        		text = i18n.lms2415m01["C241M01a.titleA"];
        	}
            rowspanCount = typeJson.A ;
        }else if (type == "B") {
        	if(locale == "en"){
        		text = (country == "CA") ? i18n.lms2415m01["C241M01aV2_CA.titleB"] : i18n.lms2415m01["C241M01aV2.titleB"];
        	} else {
        		text = i18n.lms2415m01["C241M01a.titleB"];
        	}
            rowspanCount = Number(typeJson.B) + Number((typeJson.Z != undefined) ? typeJson.Z : 0) + Number((typeJson.X != undefined) ? typeJson.X : 0);
            if(docFmt == "A"){	//土建融	否-->不用輸入B007 不用輸入X000 X100 X200 X300
            	rowspanCount = Number(rowspanCount)-1 - Number((typeJson.X != undefined) ? typeJson.X : 0);
            }
        }else {
            text = i18n.lms2415m01["C241M01a.titleC"];
            rowspanCount = Number(typeJson.C) + Number((typeJson.Y != undefined) ? typeJson.Y : 0);
        }
        rowspanCount = Number(encodeHTML(rowspanCount));
        for (var i = 0; i < array.length; i++) {
        	var json = array[i];
			var itemNo = encodeHTML(json.itemNo);
        	if (json.itemType == type) {
        		if (type == "Z" || type == "Y" || type == "X") {
            		continue;
            	}
        		if((docFmt == "A" && itemNo == "B007")){
            		continue;	//是否為土建融	否-->不用輸入B007
            	}
        		var str = "";
                str = "<tr valign='top' " + " id='" + itemNo + "'>";
                if (rowspanCount != 0) {
                	str += "<td width='3%' rowspan='" + rowspanCount + "' align='center' valign='middle' class='hd1 style42' style='text-align:center' >";
                    str += text;
                    str += "</td>";
                    rowspanCount = 0 ;
                }
                
                //附表
                if(itemNo == "B011" && arrayZ != undefined){	//B010 -> B011前在插入附表
                	table.append(createAttachedTable("Z",arrayZ,country,locale));
                } else if(itemNo == "C002" && arrayY != undefined){	//C001 -> C002前在插入附表
                	table.append(createAttachedTable("Y",arrayY,country,locale));
                } else if(itemNo == "B008" && arrayX != undefined){	//B007 -> B008前在插入附表
                	table.append(createAttachedTable("X",arrayX,country,locale));
                }
                
                if (json.itemSeq == 14 || json.itemSeq == 17) {	//B007	B010
                	str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + itemNo + "' /></td>";
                } else if (json.itemSeq > 14 && json.itemSeq < 17) {
                	var itemSeq = json.itemSeq-1;
                	str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + itemNo + "' />" + itemSeq + "</td>";
                } else if (json.itemSeq > 17) {
                	var itemSeq = json.itemSeq-2;
                	str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + itemNo + "' />" + itemSeq + "</td>";
                } else {
                	str += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + itemNo + "' />" + encodeHTML(json.itemSeq) + "</td>";
                }
                
                str += " <td width='45%'>" + encodeHTML(json.chkItem) + "</td>";
                str += " <td width='22%'>";
                if (itemNo == "B011") {	//前次覆審有無應行改善事項？
                    str += "<label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='N' /><span class='style999'>";
                    if(locale == "en"){
                    	str += i18n.lms2415m01["C241M01a.n"];
                    } else {
                    	str += i18n.lms2415m01["C241M01a.haveNO"];
                    }
                    str += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='Y' /><span class='style999'>";
                    if(locale == "en"){
                    	str += i18n.lms2415m01["C241M01a.y"];
                    } else {
                    	str += i18n.lms2415m01["C241M01a.haved"];
                    }
                    str += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='K' /><span class='style999'>";
                    str += i18n.lms2415m01["C241M01a.an"];
                    str += "</label></td>";
                } else if (itemNo == "B013" || itemNo == "C005") {
                	str += " <label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='N' /><span class='style999'>";
                	str += i18n.lms2415m01["C241M01a.n"];
                	str += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='Y' /><span class='style999'>";
                	str += i18n.lms2415m01["C241M01a.y"];
                	str += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='K' /><span class='style999'>";
                	str += i18n.lms2415m01["C241M01a.an"];
                	str += "</label></td>";
                } else {
                    str += " <label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='Y' /><span class='style999'>";
                    str += i18n.lms2415m01["C241M01a.y"];
                    str += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='N' /><span class='style999'>";
                    str += i18n.lms2415m01["C241M01a.n"];
                    str += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='K' /><span class='style999'>";
                    str += i18n.lms2415m01["C241M01a.an"];
                    str += "</label></td>";
                }
                str += " </td>";
                str += " <td width='31%' >";
                if (itemNo == "B011") {	// 前次覆審有無應行改善事項？(引藏)
                	str += "<div id='show1'><label><input type='radio' id='chkPreReview' name='chkPreReview' value='Y'/>";
                	str += i18n.lms2415m01["C241M01a.cy"];
                    str += "</label> <label><input type='radio' id='chkPreReview' name='chkPreReview' value='N'/>";
                    str += i18n.lms2415m01["C241M01a.cn"];
                    str += "</label> <label><input type='radio' id='chkPreReview' name='chkPreReview' value='K'/>";
                    str += i18n.lms2415m01["C241M01a.an"];
                    str += "</label> </div>";
                    str += "<div id='show2'><input type='text' id='chkText" + itemNo + "' name='chkText" + itemNo + "' size='30' maxlength='60' maxlengthC='20' /> </div>";
                }
                else {
                    str += "<input type='text' id='chkText" + itemNo + "' name='chkText" + itemNo + "' size='30'  maxlength='60' maxlengthC='20' /> ";
                }
                str += " </td>";
                str += " </tr>";
                table.append(str);
                
                $("input[type='radio'][name='chkResult" + itemNo + "'][value=" + json.chkResult + "]").prop("checked", true);
                //$("[name='chkText" + itemNo + "']").val(json.chkText);  
                var chkTextInput = $("[name='chkText" + itemNo + "']");
                chkTextInput.val(json.chkText);
                if (itemNo == "B011") {
                	$("input[type='radio'][name=chkPreReview][value="+json.chkPreReview+"]").prop("checked",true);
                }
        	}
        }
        //覆審項次(itemNo)有幾筆
    }//END ARRAY
    // 前次覆審有無應行改善事項？ 
    $("input[name=chkResultB011]").click(function(){
        if ($(this).val() == "N") {
            $("#show1").hide();
            $("#show2").hide();
        } else {
            $("#show1").show();
            $("#show2").show();
        }
    });
    
    if ($("input[name=chkResultB011]:checked").val() == "N") {
        $("#show1").hide();
        $("#show2").hide();
    } else {
        $("#show1").show();
        $("#show2").show();
    }
    
    //塞附表值
	for (var i = 0; i < array.length; i++) {
		var itemNo = encodeHTML(array[i].itemNo);
		if(array[i].itemType == "Z" || array[i].itemType == "Y" || array[i].itemType == "X"){
			$("input[type='radio'][name='chkResult" + itemNo + "'][value=" + array[i].chkResult + "]").prop("checked", true);
            $("[name='chkText" + itemNo + "']").val(array[i].chkText);
		}
	}
}

function createAttachedTable(arrayType, array, country, locale){
	var ATstr = "";
	var TAB_0 =  ['Z000', 'Y000', 'Y100', 'Y110', 'Y120', 'Y200', 'Y210', 'Y220', 'Y22A', 'Y222'];
	var TAB_1 =  ['Z100', 'Y111', 'Y112', 'Y121', 'Y122', 'Y123', 'Y124',
	              'Y125', 'Y211', 'Y212',  'Y213', 'Y221',
	              'X000', 'X100', 'X200', 'X300'];
	var noChk =  ['Y000', 'Y100', 'Y110', 'Y120', 'Y200', 'Y210'];
	var chk_3 =  ['Y125', 'Y211', 'Y212', 'Y213', 'X000', 'X100', 'X200', 'X300'];	//是 否 －
	var chk_2 =  ['Z100', 'Y111', 'Y112', 'Y121', 'Y122', 'Y123', 'Y124', 'Y221'];	//是 否
	var chk_YN = ['Z000', 'Y22A'];	//有無
	for(var i = 0; i < array.length; i++){
		var itemNo = encodeHTML(array[i].itemNo);
		var chkItem = encodeHTML(array[i].chkItem);
		ATstr += "<tr valign='top' " + " id='" + itemNo + "'>";
		ATstr += " <td width='2%'><input id='itemNo' name='itemNo' type='hidden'class='style999' value='" + itemNo + "' /></td>";
		
		if(jQuery.inArray(itemNo,TAB_0) >= 0){
			if(jQuery.inArray(itemNo,chk_YN) >= 0){
				var point = chkItem.indexOf("有無");
				ATstr += " <td width='45%' colspan='3'>" + chkItem.slice(0,point)+" ";
				ATstr += "<label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='Y' /><span class='style999'>";
				if(locale == "en"){
					ATstr += i18n.lms2415m01["C241M01a.y_i"];
                } else {
                	ATstr += i18n.lms2415m01["C241M01a.haved"];
                }
				ATstr += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='N' /><span class='style999'>";
				if(locale == "en"){
					ATstr += i18n.lms2415m01["C241M01a.n_d"];
                } else {
                	ATstr += i18n.lms2415m01["C241M01a.haveNO"];
                }
				ATstr += "</label>" + " " + chkItem.slice(point+2) +"</td>";
			} else {
				if (itemNo == "Y120") {
					ATstr += " <td colspan='3'>" + chkItem + "　　" + "<span class='text-red'>＊";
    				ATstr += i18n.lms2415m01["C241M01a.info02"];
    				ATstr += "</span></td>";
				} else {
					ATstr += " <td colspan='3'>" + chkItem + "</td>";	
				}
			}
		} else if(jQuery.inArray(itemNo,TAB_1) >= 0){
			ATstr += " <td width='45%'>" + "　"+ chkItem + "</td>";
		}
    	
		if(jQuery.inArray(itemNo,noChk) >= 0){
		} else if(jQuery.inArray(itemNo,chk_3) >= 0){
			ATstr += " <td width='22%'>";
    		ATstr += " <label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='Y' /><span class='style999'>";
    		ATstr += i18n.lms2415m01["C241M01a.y"];
    		ATstr += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='N' /><span class='style999'>";
    		ATstr += i18n.lms2415m01["C241M01a.n"];
    		ATstr += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='K' /><span class='style999'>";
    		ATstr += i18n.lms2415m01["C241M01a.an"];
    		ATstr += "</label></td>";
    	} else if(jQuery.inArray(itemNo,chk_2) >= 0){
			ATstr += " <td width='22%'>";
    		ATstr += " <label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='Y' /><span class='style999'>";
    		ATstr += i18n.lms2415m01["C241M01a.y"];
    		ATstr += "</label><label><input id='chkResult" + itemNo + "' name='chkResult" + itemNo + "' type='radio' value='N' /><span class='style999'>";
    		ATstr += i18n.lms2415m01["C241M01a.n"];
    		ATstr += "</label></td>";
    	}
		
		var chkText = ['Z100']
		if(jQuery.inArray(itemNo,chkText) >= 0){
			ATstr += "<td width='31%' class='text-red'>";
			ATstr += i18n.lms2415m01["C241M01a.info01"]+"<br>";
			ATstr += "<span class='style44'><input type='text' id='chkText" + itemNo + "' name='chkText" + itemNo + "' size='30' maxlength='60' maxlengthC='20' /></span>";
			ATstr += " </td>";
			ATstr += " </tr>";
		}
		ATstr += " </tr>";
	}
	return ATstr;
}

var gridPrint = $("#printGrid").iGrid({
    handler: 'lms2415gridhandler',
    height: 270,
    rownumbers: true,
    multiselect: true,
    hideMultiselect: false,
    
    postData: {
        formAction: "queryPrint"
    },
    colModel: [{
        colHeader: i18n.lms2415m01['print.custName'],// "借款人名稱",
        name: 'custName',
        width: 120,
        sortable: true
    }, {
        colHeader: i18n.lms2415m01['print.rptNo'],// "報表編號",
        name: 'rptNo',
        align: "center",
        width: 40,
        sortable: true
    }, {
        colHeader: i18n.lms2415m01['print.rptName'],// "報表名稱",
        name: 'rptName',
        width: 70,
        sortable: true
    }, {
        colHeader: "oid",
        name: 'oid',
        hidden: true
    }, {
        colHeader: "rpt",
        name: 'rpt',
        hidden: true
    }]
});

//將特殊字元轉換成 HTML 實體，例如 < 轉換成 &lt;
//避免在網頁中執行不受信任的 JavaScript 代碼，從而保護使用者的資訊安全。
function encodeHTML(str) {
	var isNumber = /^\d+$/.test(str);
	if (isNumber) {
		str = encodeURI(str);
	}
	if (str == null) return '';
	//強轉
	str = String(str);
	str = str.replace(/[\&\<\>\"\'\/]/g, function(match) {
		switch (match) {
		case '&':
			return '&amp;';
		case '<':
			return '&lt;';
		case '>':
			return '&gt;';
		case '"':
			return '&quot;';
		case '\'':
			return '&#039;';
		default:
			return match;
		}
	});
	if (isNumber) {
		str = Number(encodeURI(str));
	}
	return str;
}
});
