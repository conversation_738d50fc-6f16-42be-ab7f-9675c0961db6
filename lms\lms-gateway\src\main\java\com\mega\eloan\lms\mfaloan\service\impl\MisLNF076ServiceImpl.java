/* 
 * MisLNF076ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisLNF076Service;

/**<pre>
 * 移轉資料檔
 * </pre>
 * @since  2013/1/25
 * <AUTHOR>
 * @version <ul>
 *           <li>2013/1/25,<PERSON>,new
 *          </ul>
 */
@Service
public class MisLNF076ServiceImpl extends AbstractMFAloanJdbc implements
		MisLNF076Service {

	@Override
	public List<Map<String, Object>> selMoveUnit() {
		return this.getJdbc().queryForList("LNF076.selMoveUnit",
				new String[] {});
	}

}
