/* 
 * L140S10B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 其他敘作條件樣版項目資訊檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S10B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140S10B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 簽報書MAINID
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updatetime;

	/** 簽報樣板類別 **/
	@Size(max=1)
	@Column(name="BIZCAT", length=1, columnDefinition="CHAR(1)")
	private String bizCat;

	/** 項目代號 **/
	@Size(max=2)
	@Column(name="BIZITEM", length=2, columnDefinition="CHAR(2)")
	private String bizItem;

	/** 項目內容 **/
	@Size(max=2048)
	@Column(name="BIZITEMDESC", length=2048, columnDefinition="VARCHAR(2048)")
	private String bizItemDesc;

	/** 順序 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SEQ", columnDefinition="DECIMAL(3,0)")
	private Integer seq;

	/** 是否為預設樣板項目 **/
	@Size(max=1)
	@Column(name="ISDEFAULTITEM", length=1, columnDefinition="CHAR(1)")
	private String isDefaultItem;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 簽報書MAINID
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  簽報書MAINID
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdatetime() {
		return this.updatetime;
	}
	/** 設定異動日期 **/
	public void setUpdatetime(Timestamp value) {
		this.updatetime = value;
	}

	/** 取得簽報樣板類別 **/
	public String getBizCat() {
		return this.bizCat;
	}
	/** 設定簽報樣板類別 **/
	public void setBizCat(String value) {
		this.bizCat = value;
	}

	/** 取得項目代號 **/
	public String getBizItem() {
		return this.bizItem;
	}
	/** 設定項目代號 **/
	public void setBizItem(String value) {
		this.bizItem = value;
	}

	/** 取得項目內容 **/
	public String getBizItemDesc() {
		return this.bizItemDesc;
	}
	/** 設定項目內容 **/
	public void setBizItemDesc(String value) {
		this.bizItemDesc = value;
	}

	/** 取得順序 **/
	public Integer getSeq() {
		return this.seq;
	}
	/** 設定順序 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 取得是否為預設樣板項目 **/
	public String getIsDefaultItem() {
		return this.isDefaultItem;
	}
	/** 設定是否為預設樣板項目 **/
	public void setIsDefaultItem(String value) {
		this.isDefaultItem = value;
	}
}
