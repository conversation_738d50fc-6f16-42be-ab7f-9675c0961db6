/* 
 * C120S01KDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C120S01KDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C120S01K;


/** 個金相關查詢主從債務人檔 **/
@Repository
public class C120S01KDaoImpl extends LMSJpaDao<C120S01K, String>
	implements C120S01KDao {

	@Override
	public C120S01K findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120S01K> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C120S01K> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C120S01K findByUniqueKey(String mainId, String custId, String dupNo, String lnGeId, String lnGeDupNo){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (lnGeId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "lnGeId", lnGeId);
		if (lnGeDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "lnGeDupNo", lnGeDupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C120S01K> findByIndex01(String mainId, String custId, String dupNo, String lnGeId, String lnGeDupNo){
		ISearch search = createSearchTemplete();
		List<C120S01K> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (lnGeId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "lnGeId", lnGeId);
		if (lnGeDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "lnGeDupNo", lnGeDupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<C120S01K> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<C120S01K> list = createQuery(C120S01K.class,search).getResultList();
		return list;
	}
	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C120S01K.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
}