/* 
 * L140S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S01A;

/** 個金借保人檔 **/
public interface L140S01ADao extends IGenericDao<L140S01A> {

	L140S01A findByOid(String oid);

	List<L140S01A> findByOids(String[] oids);

	List<L140S01A> findByMainId(String mainId);

	L140S01A findByUniqueKey(String mainId, String custId, String dupNo, String custPos);

	List<L140S01A> findByCustIdDupId(String custId, String DupNo);
	
	List<L140S01A> findByMainIdCustPos(String mainId, String custPos);
	
	List<L140S01A> findByMainId_excludeS(String mainId);
}