var lms140_loanTarget = null;
var gSaveOkCanSend = "N";
var pageAction = {

    handler: 'lms7120formhandler',
    isChinaLoanInit: false,
    grid: null,
    build: function(){
        //build buttons
        //呈主管覆核
        $("#buttonPanel").find("#btnSend").click(function(){
            // confirmRun=是否確定執行此功能?
            CommonAPI.confirmMessage(i18n.def["confirmRun"], function(b){
                if (b) {
                    // 停權待覆核
                    pageAction.save();
                    if (gSaveOkCanSend == "Y") {
                        pageAction.flowAction({
                            flowAction: "sendStop"
                        });
                    }
                    gSaveOkCanSend = "N";
                }
            });
        }).end().find("#btnPullin").click(function(){
            //引進
            pageAction.pullin();
        }).end().find("#btnSave").click(function(){
            //儲存
            pageAction.save();
            
        }).end().find("#btnPrint").click(function(){
            //列印
            if (pageAction.save()) {
                pageAction.print();
            }
            
            
        })
    },
    /**
     * 流程
     */
    flowAction: function(sendData){
        $.ajax({
            handler: pageAction.handler,
            data: $.extend({
                formAction: "flowAction",
                mainOid: responseJSON.mainOid
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                API.showPopMessage(i18n.def["runSuccess"], window.close);
            }
        });
    },
    /**
     * 查詢detail
     */
    init: function(){
        var $form = $("#LMS140M01QForm");
        $.form.init({
            formHandler: pageAction.handler,
            formPostData: {
                formAction: "queryL712m01a",
                mainId: responseJSON.mainId
            },
            loadSuccess: function(obj){
            
                //J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
                pageAction.initLoanTarget();
                
                //$form.injectData(obj);
                
                $("#formStopDetail1").setData(obj.formStopDetail1);
                $("#LMS140M01QForm").setData(obj.lms140m01qform);
                
                //BGN J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
                var loanTarget = $("#loanTarget").val();
                if (loanTarget) {
                    $("#loanTargetDscr").val(buildLoanTargetDscr(loanTarget));
                }
                $("#loanTarget").triggerHandler("change");
                $("#grntClass").triggerHandler("change");
                
                var bloanTarget = $("#bloanTarget").val();
                if (bloanTarget) {
                    $("#bloanTargetDscr").val(buildLoanTargetDscr(bloanTarget));
                }
                $("#bloanTarget").triggerHandler("change");
                $("#bgrntClass").triggerHandler("change");
                
                //END J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
                
                
                
                if (obj.showBefore) {
                    $form.find(".showBefore").show();
                }
                else {
                    $form.find(".showBefore").hide();
                }
                if (obj.showDerv) {
                    $form.find(".showDerv").show();
                }
                else {
                    $form.find(".showDerv").hide();
                }
                
                //J-105-0074-001 Web e-Loan 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
                if (obj.showForCNCntrno) {
                    $form.find(".showForCNCntrno").show();
                }
                else {
                    $form.find(".showForCNCntrno").hide();
                }
                
                /*
                 $("#cnTMUFg option[value='-']").attr('disabled', true);
                 
                 $('#cnTMUFg').each(function(){
                 this.rejectDisabled = function(){
                 if (this.options[this.selectedIndex].disabled) {
                 if (this.lastSelectedIndex) {
                 this.selectedIndex = this.lastSelectedIndex;
                 }
                 else {
                 var first_enabled = $(this).children('option:not(:disabled)').get(0);
                 this.selectedIndex = first_enabled ? first_enabled.index : 0;
                 }
                 }
                 else {
                 this.lastSelectedIndex = this.selectedIndex;
                 }
                 };
                 this.rejectDisabled();
                 this.lastSelectedIndex = this.selectedIndex;
                 $(this).children('option[disabled]').each(function(){
                 $(this).css('color', '#CCC');
                 });
                 $(this).change(function(){
                 this.rejectDisabled();
                 });
                 });
                 */
                //J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
                $('#directFg').change();
                $('#bdirectFg').change();
                //$form.readOnlyChilds(this.isforQuery, ".noEdit");
            
            }
        });
    },
    /**
     * 開啟detail
     */
    openDetail: function(data){
        var $formStopDetail2 = $("#formStopDetail2");
        // 初始化原始停權月
        $formStopDetail2.find("#suspendMons").val("");
        if (data) {
            // btn.index1=新增
            if (data.statusFlag != i18n.lms7120m01["btn.index1"]) {
                $formStopDetail2.find(".addNeed").attr("disabled", true);
            }
        }
        else {
            $formStopDetail2.find(".addNeed").attr("disabled", false);
            $formStopDetail2.find("#modlifyMons").attr("disabled", false);
        }
        $.ajax({
            handler: pageAction.handler,
            action: 'setAllBranch',
            success: function(response){
                // 設定所有分行代碼Map
                $formStopDetail2.find("#branchNo").setItems({
                    clear: false,
                    item: response.caseBrId,
                    format: "{value} - {key}",
                    space: true
                });
                // 開始進行查詢明細
                $.ajax({
                    handler: pageAction.handler,
                    action: 'queryL918s01a',
                    data: {
                        oid: (data) ? data.oid : ""
                    },
                    success: function(result){
                        $formStopDetail2.setData(result.formStopDetail2, true);
                        if ($formStopDetail2.find("#modlifyMons").val() == "0") {
                            // 若為刪除則停權月設成唯讀
                            $formStopDetail2.find("#modlifyMons").attr("disabled", true);
                        }
                        else {
                            $formStopDetail2.find("#modlifyMons").attr("disabled", false);
                        }
                        // html.index15=停權明細
                        //開視窗
                        $("#stopDetail2").thickbox({
                            title: i18n.lms7120m01["html.index15"],
                            width: 800,
                            height: 200,
                            modal: true,
                            //align : 'center',
                            //valign: 'bottom',
                            i18n: i18n.def,
                            buttons: {
                                'saveData': function(){
                                    if ($formStopDetail2.valid()) {
                                        var modlifyMons = $formStopDetail2.find("#modlifyMons").val();
                                        if ($formStopDetail2.find("#modlifyMons").attr("disabled") == false) {
                                            if (modlifyMons < 1 || modlifyMons > 12) {
                                                // msg.alert2=輸入不合法的月份!
                                                CommonAPI.showMessage(i18n.lms7120m01["msg.alert2"]);
                                                return;
                                            }
                                        }
                                        pageAction.saveDetail(data);
                                    }
                                },
                                'del': function(){
                                    if (data) {
                                        pageAction.delDetail(data.oid);
                                    }
                                    else {
                                        // msg.alert3=資料尚未儲存，請執行「儲存」後再執行本功能。
                                        CommonAPI.showMessage(i18n.lms7120m01["msg.alert3"]);
                                        return;
                                    }
                                },
                                'close': function(){
                                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                                        if (res) {
                                            $.thickbox.close();
                                        }
                                    });
                                }
                            }
                        });
                    }
                });
            }
        });
    },
    /**
     * 儲存detail
     */
    saveDetail: function(data){
        $.ajax({
            handler: pageAction.handler,
            action: 'saveL918s01a',
            data: {
                oid: (data) ? data.oid : "",
                mainId: responseJSON.mainId,
                formStopDetail2: JSON.stringify($("#formStopDetail2").serializeData())
            },
            success: function(result){
                pageAction.reloadGrid();
                $.thickbox.close();
                $.thickbox.close();
                CommonAPI.showMessage(result.NOTIFY_MESSAGE);
            }
        });
    },
    //依照不同文件狀態控制唯讀
    setReadOnly: function(auth){
        //停權編製中可編輯，其他都不可編輯
        if (responseJSON.mainDocStatus == "LEH") {
            //編製中且沒被鎖定
            if (auth.Modify && !thickboxOptions.readOnly) {
                responseJSON["readOnly"] = false;
                $(this).find("button").show();
            }
            else {
                responseJSON["readOnly"] = true;
                var $formStopDetail2 = $("#formStopDetail2");
                $formStopDetail2.readOnlyChilds(true);
                
                var $lms140m01qform = $("#LMS140M01QForm");
                $lms140m01qform.readOnlyChilds(true);
                
                $("#formStopBtn").find("button").hide();
                thickboxOptions.readOnly = true;
                //顯示上方主要標題按鈕
                $("#buttonPanel :button").show();
                $("#buttonPanel").find("#btnSend").hide();
                $("#buttonPanel").find("#btnPullin").hide();
                $("#buttonPanel").find("#btnSave").hide();
            }
        }
        else {
            //非編製中
            responseJSON["readOnly"] = true;
            var $formStopDetail2 = $("#formStopDetail2");
            $formStopDetail2.readOnlyChilds(true);
            
            var $lms140m01qform = $("#LMS140M01QForm");
            $lms140m01qform.readOnlyChilds(true);
            
            $("#formStopBtn").find("button").hide();
            thickboxOptions.readOnly = true;
            //顯示上方主要標題按鈕
            $("#buttonPanel :button").show();
            $("#buttonPanel").find("#btnSend").hide();
            $("#buttonPanel").find("#btnPullin").hide();
            $("#buttonPanel").find("#btnSave").hide();
        }
    },
    /**
     * 取得資料表之選擇列
     */
    getRowData: function(){
        var rows = pageAction.grid.getGridParam('selarrrow');
        var list = "";
        var sign = ",";
        for (var i = 0; i < rows.length; i++) {
            //將所有已選擇的資料存進變數list裡面
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = pageAction.grid.getRowData(rows[i]);
                list += ((list == "") ? "" : sign) + data.oid;
            }
        }
        return list;
    },
    /**
     * 重整資料表
     */
    reloadGrid: function(data){
        if (data) {
            pageAction.grid.jqGrid("setGridParam", {
                postData: data,
                page: 1,
                search: true
            }).trigger("reloadGrid");
        }
        else {
            pageAction.grid.trigger('reloadGrid');
        }
    },
    /**
     * 重新引進
     * @param {Object} data
     */
    pullin: function(data){
        var $form = $("#LMS140M01QForm");
        var $formStopDetail1 = $("#formStopDetail1");
        $.ajax({
            handler: pageAction.handler,
            action: 'reQueryElf506',
            data: {
                oid: (data) ? data.oid : "",
                mainId: responseJSON.mainId,
                cntrNo: $formStopDetail1.find("#cntrNo").val(),
                formStopDetail1: JSON.stringify($formStopDetail1.serializeData()),
                lms140m01qform: JSON.stringify($form.serializeData()),
                noOpenDoc: true
            },
            success: function(obj){
                //J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
				//J-105-0074-001 Web e-Loan 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
                $form.find("input[name='bcnLoanFg'],input[name='biGolFlag'],input[name='bsTradeFg'],input[name='brickTrFg'],input[name='bunionArea3'],input[name='bnCnSblcFg']").attr("checked", false);
                $form.find("#bdirectFg").val('');
                
                //J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
                $form.find("#bcnBusKind").val('');
                $form.find("#bguar1Rate,#bguar2Rate,#bguar3Rate,#bcoll1Rate,#bcoll2Rate,#bcoll3Rate,#bcoll4Rate,#bcoll5Rate").val('');
                
                //BGN J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
                $form.find("#bisType").val('');
                $form.find("#bgrntType").val('');
                $form.find("#bgrntClass").val('');
                //$form.find("#bothCrdType").val('');
				$( "[name=bothCrdType]"). removeAttr("checked" );
                $form.find("#bloanTarget").val('');
                $form.find("#bloanTargetDscr").val('');
                //END J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別

                pageAction.initChinaLoan($form);
                
                //J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
                pageAction.initLoanTarget();
                
                $form.injectData(obj);
                
                //BGN J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
                var loanTarget = $("#loanTarget").val();
                if (loanTarget) {
                    $("#loanTargetDscr").val(buildLoanTargetDscr(loanTarget));
                }
                $("#loanTarget").triggerHandler("change");
                $("#grntClass").triggerHandler("change");
                
                var bloanTarget = $("#bloanTarget").val();
                if (bloanTarget) {
                    $("#bloanTargetDscr").val(buildLoanTargetDscr(bloanTarget));
                }
                $("#bloanTarget").triggerHandler("change");
                $("#bgrntClass").triggerHandler("change");
                
                //END J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
                
                
                if (obj.showBefore) {
                    $form.find(".showBefore").show();
                }
                else {
                    $form.find(".showBefore").hide();
                }
                if (obj.showDerv) {
                    $form.find(".showDerv").show();
                }
                else {
                    $form.find(".showDerv").hide();
                }
				
				//J-105-0074-001 Web e-Loan 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
				if(obj.showForCNCntrno){
					$form.find(".showForCNCntrno").show();
				}else{
					$form.find(".showForCNCntrno").hide();
				}
							
                //J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
                $('#directFg').change();
                $('#bdirectFg').change();
                //CommonAPI.showMessage(result.NOTIFY_MESSAGE);
            },
            error: function(obj){
                $.thickbox.close();
            }
        });
    },
    /**
     * 儲存
     * @param {Object} data
     */
    save: function(data){
        var $form = $("#LMS140M01QForm");
        var $formStopDetail1 = $("#formStopDetail1");
        gSaveOkCanSend = "N";
        var $rickTrFg = $form.find("input[name='rickTrFg']");
        if ($form.find("input[name='rickTrFg']:checked").val() == "Y") {
            var guar1Rate = $form.find("#guar1Rate").val();
            guar1Rate = isNaN(guar1Rate) || guar1Rate == "" ? 0 : parseInt(guar1Rate, 10);
            var guar2Rate = $form.find("#guar2Rate").val();
            guar2Rate = isNaN(guar2Rate) || guar2Rate == "" ? 0 : parseInt(guar2Rate, 10);
            var guar3Rate = $form.find("#guar3Rate").val();
            guar3Rate = isNaN(guar3Rate) || guar3Rate == "" ? 0 : parseInt(guar3Rate, 10);
            var coll1Rate = $form.find("#coll1Rate").val();
            coll1Rate = isNaN(coll1Rate) || coll1Rate == "" ? 0 : parseInt(coll1Rate, 10);
            var coll2Rate = $form.find("#coll2Rate").val();
            coll2Rate = isNaN(coll2Rate) || coll2Rate == "" ? 0 : parseInt(coll2Rate, 10);
            var coll3Rate = $form.find("#coll3Rate").val();
            coll3Rate = isNaN(coll3Rate) || coll3Rate == "" ? 0 : parseInt(coll3Rate, 10);
            var coll4Rate = $form.find("#coll4Rate").val();
            coll4Rate = isNaN(coll4Rate) || coll4Rate == "" ? 0 : parseInt(coll4Rate, 10);
            var coll5Rate = $form.find("#coll5Rate").val();
            coll5Rate = isNaN(coll5Rate) || coll5Rate == "" ? 0 : parseInt(coll5Rate, 10);
            
            var total = (guar1Rate + guar2Rate + guar3Rate +
            coll1Rate +
            coll2Rate +
            coll3Rate +
            coll4Rate +
            coll5Rate);
            
            if (!(total == 100)) {
                API.showMessage(i18n.lmsl140m01m['L140M01Q.MSG001']);
                return false;
            }
        }
        
        var directFg = $form.find("#directFg").val();
        if (directFg == "12" || directFg == "14") {
            //J-104-0073-001 Web e-Loan授信系統修改大陸地區控管註記授信對象別檢核。	
            if (directFg == "14") {
                var cnBusKindValue = $form.find("#cnBusKind").val();
                switch (cnBusKindValue) {
                    case "A":
                    case "B":
                        $("#cnBusKind").val("");
                        break;
                    default:
                        break;
                }
            }
            
            var cnBusKind = $form.find("#cnBusKind").val();
            if (cnBusKind == "" || cnBusKind == "undefined") {
                API.showMessage("「" + i18n.lmsl140m01m['L140M01Q.CNBUSKIND'] + "」" + i18n.def['val.required']);
                return false;
            }
        }
        
        if ($form.valid()) {
            $.ajax({
                async: false,
                handler: pageAction.handler,
                action: 'saveL712m01a',
                data: {
                    oid: responseJSON.oid,
                    mainId: responseJSON.mainId,
                    cntrNo: $formStopDetail1.find("#cntrNo").val(),
                    formStopDetail1: JSON.stringify($formStopDetail1.serializeData()),
                    lms140m01qform: JSON.stringify($form.serializeData())
                },
                success: function(result){
                    $formStopDetail1.find("#caseNo").val(result.caseNo);
                    gSaveOkCanSend = "Y";
                    // CommonAPI.showMessage(result.NOTIFY_MESSAGE);
                }
            });
            
            
        }
        
        return true;
        
    },
    /**
     * 列印
     * @param {Object} data
     */
    print: function(data){
        var $form = $("#LMS140M01QForm");
        var $formStopDetail1 = $("#formStopDetail1");
        var pdfName = "LMS1401R03.pdf";
        var rType = "R29A"
        
        var content = ""; //R12^10C4E73B266C11E3B4D7126EC0A83B82^73251209^0^006109290001^
        content = rType + "^" + responseJSON.oid + "^" + $("#formStopDetail1").find("#custId").val() + "^" + $("#formStopDetail1").find("#dupNo").val() + "^" + $("#formStopDetail1").find("#cntrNo").val() + "^" + responseJSON.mainId + "^";
        pdfName = pdfName;
        
        //$.thickbox.close();
        $.form.submit({
            url: "../../app/simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                fileDownloadName: pdfName,
                serviceName: "lms1201r01rptservice"
            }
        });
        
    },
    initChinaLoan: function($formObject){
        if (!this.isChinaLoanInit) {
            this.initChinaLoanEvent($formObject);
        }
        $formObject.reset();
    },
    initLoanTarget: function(){
        //J-104-0XXX-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
        var nitem = API.loadCombos('lms140_loanTarget');
        lms140_loanTarget = nitem.lms140_loanTarget;
        var code = [];
        var codeDesc = [];
        for (var prop in lms140_loanTarget) {
            if (lms140_loanTarget.hasOwnProperty(prop)) {
                code.push(prop);
                codeDesc.push(lms140_loanTarget[prop]);
            }
        }
        
        //var code = ['10000','11000','11100','11200','11210','11220','11230','11240','11241','11242','11243','11250','11300','11310'];	
        //var codeDesc = ['直接往來授信','債務人','大陸地區人民','大陸地區法人','大陸地區金融機構','大陸地區外資企業','大陸地區臺資企業','大陸地區陸資企業','中央企業','國營企業','民營企業','其他','大陸地區人民，法人在第三地區','人民在第三地區設立之企業'];			
        // $("#loanTarget").append("<option value=''>請選擇</option>");
        $("#_loanTarget").empty();
        $("#_loanTarget").append("<option value=''>請選擇</option>");
        
        for (i = 0; i < code.length; i++) {
            var haveChildren = haveChild(code[i], code[i + 1]);
            var zeroCount = countTailZero(code[i]);
            var spaceCount = 4 - zeroCount;
            
            var nbsp = "&nbsp;&nbsp;";
            for (var tmp = 0; tmp < spaceCount; tmp++) {
                nbsp = nbsp + "&nbsp;";
                nbsp = nbsp + "&nbsp;";
                nbsp = nbsp + "&nbsp;";
                nbsp = nbsp + "&nbsp;";
            }
            var option = $("<option>" + code[i] + nbsp + codeDesc[i] + "</option>");
            if (haveChildren) {
                option.attr("disabled", true);
            }
            option.attr("value", code[i]);
            $("#_loanTarget").append(option);
            
        }
    },
    initChinaLoanEvent: function($formObject){
    
        //J-104-00279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
        var item = API.loadOrderCombosAsList("L140M01Q_othCrdType")["L140M01Q_othCrdType"];
        $("#othCrdType").setItems({
            size: "1",
            item: item,
            clear: true,
            itemType: 'checkbox'
        });
        $("#bothCrdType").setItems({
            size: "1",
            item: item,
            clear: true,
            itemType: 'checkbox'
        });
        
        var $cnLoanFg = $formObject.find("input[name='cnLoanFg']");
        $cnLoanFg.click(function(){
            var cnLoanFg = $(this).val();
            if (cnLoanFg == "Y") {
                $formObject.find(".chinaLoan").show();
                //J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
                $('#directFg').change();
                $('#bdirectFg').change();
            }
            else {
                $formObject.find(".chinaLoan").hide();
                $formObject.find("input[name='iGolFlag']").attr("checked", false);
                $formObject.find("#directFg").val('');
                //J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
                $formObject.find("#cnBusKind").val('');
                $formObject.find("input[name='sTradeFg']").attr("checked", false);
                $formObject.find("input[name='rickTrFg']").attr("checked", false);
                $formObject.find("#guar1Rate,#guar2Rate,#guar3Rate,#coll1Rate,#coll2Rate,#coll3Rate,#coll4Rate,#coll5Rate").val('0');
				
				//J-105-0074-001 Web e-Loan 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
				$formObject.find("input[name='nCnSblcFg']").attr("checked", false);
            }
        });
        
        var $sTradeFg = $formObject.find("input[name='sTradeFg']");
        $sTradeFg.click(function(){
            var sTradeFg = $(this).val();
            if (sTradeFg == "N") {
                $formObject.find(".sTrade").show();
            }
            else {
                $formObject.find(".sTrade").hide();
                $formObject.find("input[name='rickTrFg']").attr("checked", false);
                $formObject.find("#guar1Rate,#guar2Rate,#guar3Rate,#coll1Rate,#coll2Rate,#coll3Rate,#coll4Rate,#coll5Rate").val('0');
            }
        });
        
        var $rickTrFg = $formObject.find("input[name='rickTrFg']");
        $rickTrFg.click(function(){
            var rickTrFg = $(this).val();
            if (rickTrFg == "Y") {
                $formObject.find(".rickTr").show();
            }
            else {
                $formObject.find(".rickTr").hide();
                $formObject.find("#guar1Rate,#guar2Rate,#guar3Rate,#coll1Rate,#coll2Rate,#coll3Rate,#coll4Rate,#coll5Rate").val('0');
            }
        });
        
        //J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
        //授信對象別代碼切換
        $('#directFg').change(function(){
            var value = $(this).val();
            switch (value) {
                case "12":
                case "14":
                    $("#showCnBusKind").show();
                    //$("#showBcnBusKind").show();
                    break;
                default:
                    $("#showCnBusKind").hide();
                    //$("#showBcnBusKind").hide();
                    break;
            }
        });
        $('#bdirectFg').change(function(){
            var value = $(this).val();
            switch (value) {
                case "12":
                case "14":
                    //$("#showCnBusKind").show();
                    $("#showBcnBusKind").show();
                    break;
                default:
                    //$("#showCnBusKind").hide();
                    $("#showBcnBusKind").hide();
                    break;
            }
        });
        
        //J-104-0073-001 Web e-Loan授信系統修改大陸地區控管註記授信對象別檢核。			
        $('#cnBusKind').click(function(){
            var value = $("#directFg").val();
            switch (value) {
                case "12":
                    $("#cnBusKind option[value='A']").attr('disabled', false);
                    $("#cnBusKind option[value='B']").attr('disabled', false);
                    break;
                case "14":
                    $("#cnBusKind option[value='A']").attr('disabled', true);
                    $("#cnBusKind option[value='B']").attr('disabled', true);
                    break;
                default:
                    $("#cnBusKind option[value='A']").attr('disabled', false);
                    $("#cnBusKind option[value='B']").attr('disabled', false);
                    break;
            }
            
            
        });
        //J-104-0073-001 Web e-Loan授信系統修改大陸地區控管註記授信對象別檢核。
        $('#directFg').click(function(){
            var value = $("#directFg").val();
            switch (value) {
                case "12":
                    $("#cnBusKind option[value='A']").attr('disabled', false);
                    $("#cnBusKind option[value='B']").attr('disabled', false);
                    break;
                case "14":
                    $("#cnBusKind option[value='A']").attr('disabled', true);
                    $("#cnBusKind option[value='B']").attr('disabled', true);
                    break;
                default:
                    $("#cnBusKind option[value='A']").attr('disabled', false);
                    $("#cnBusKind option[value='B']").attr('disabled', false);
                    break;
            }
            
        });
        
        //BGN J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別 center 
        $('#btnChoiceLoanTarget').click(function(){
        
            if (!lms140_loanTarget) {
                pageAction.initLoanTarget();
            }
            
            $('#choiceLoanTarget').thickbox({
                title: i18n.lmsl140m01m['L140M01Q.login'] + i18n.lmsl140m01m['L140M01Q.loanTarget'],//登錄+授信對象別
                width: 750,
                height: 100,
                align: 'center',
                valign: 'bottom',
                i18n: i18n.def,
                buttons: {
                    'sure': function(){
                        var _loanTarget = $("#_loanTarget").val();
                        $("#loanTarget").val(_loanTarget);
                        var _loanTargetDscr = buildLoanTargetDscr(_loanTarget);
                        $("#loanTargetDscr").val(_loanTargetDscr);
                        $("#loanTarget").triggerHandler("change")
                        $.thickbox.close();
                    },
                    'close': function(){
                        $.thickbox.close();
                    }
                }
            });
            
        });
        
        
        
        var $iGolFlag = $formObject.find("input[name='iGolFlag']");
        $iGolFlag.click(function(){
            var iGolFlag = $(this).val();
            switch (iGolFlag) {
                case "Y":
                    $("#isType").show();
                    break;
                default:
                    $("#isType").hide();
                    break;
            }
        });
        
        
        var $loanTarget = $formObject.find("#loanTarget");
        $loanTarget.change(function(){
            var loanTarget = $(this).val();
            if (loanTarget) {
                if (loanTarget.substring(0, 2) == "12") {
                    $("#grntType").show();
                }
                else {
                    $("#grntType").hide();
                }
            }
            else {
                $("#grntType").hide();
            }
            
        });
        
        
        var $grntClass = $formObject.find("#grntClass");
        $grntClass.change(function(){
            var grntClass = $(this).val();
            if (grntClass) {
                if (grntClass == "2") {
                    $("#showOthCrdType").show();
                }
                else {
                    $("#showOthCrdType").hide();
                }
            }
            else {
                $("#showOthCrdType").hide();
            }
            
        });
        
        var $bgrntClass = $formObject.find("#bgrntClass");
        $bgrntClass.change(function(){
            var bgrntClass = $(this).val();
            if (bgrntClass) {
                if (bgrntClass == "2") {
                    $("#showBOthCrdType").show();
                }
                else {
                    $("#showBOthCrdType").hide();
                }
            }
            else {
                $("#showBOthCrdType").hide();
            }
            
        });
        //END J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別 center
    
    },
    /* 
     查詢還是已修改
     */
    isforQuery: false
}
$(document).ready(function(){
    var $formObject = $("#LMS140M01QForm");
    var auth = (responseJSON ? responseJSON.Auth : {}); //權限
    gSaveOkCanSend = "N";
    pageAction.build();
    pageAction.init();
    pageAction.initChinaLoan($formObject);
    //J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
    $('#directFg').change();
    $('#bdirectFg').change();
    pageAction.setReadOnly(auth);
});

function countTailZero(str){
    //J-104-0XXX-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
    //var end = 4;
    var count = 0;
    for (var end = 4; end > 0; end--) {
        if (str.charAt(end) == "0") {
            count = count + 1;
        }
    }
    return count;
}

function haveChild(base, compre){
    //J-104-0XXX-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
    base = base + "";
    var maxLength = 5;
    var zeroCount = countTailZero(base);
    var maxBase = base.substring(0, maxLength - zeroCount) + "999999999".substring(0, zeroCount);
    if (maxBase >= compre) {
        return true;
    }
    else {
        return false;
    }
}

function buildLoanTargetDscr(tloanTarget){
    //J-104-0XXX-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
    var loanTargetDscr = "";
    
    if (!tloanTarget) {
        return loanTargetDscr;
    }
    
    if (lms140_loanTarget == null) {
        var nitem = API.loadCombos('lms140_loanTarget');
        lms140_loanTarget = nitem.lms140_loanTarget;
    }
    
    if (lms140_loanTarget.hasOwnProperty(tloanTarget)) {
        loanTargetDscr = $.trim(lms140_loanTarget[tloanTarget]);
    }
    
    
    for (var end = 5; end > 0; end--) {
        var newLoanTarget = ((tloanTarget).substring(0, end) + "00000").substring(0, 5);
        if (newLoanTarget != tloanTarget) {
            if (lms140_loanTarget.hasOwnProperty(newLoanTarget)) {
                loanTargetDscr = $.trim(lms140_loanTarget[newLoanTarget]) + "／" + loanTargetDscr;
            }
        }
        
    }
    
    return loanTargetDscr;
}


