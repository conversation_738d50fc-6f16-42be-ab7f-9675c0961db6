/* 
 * C140S07BDao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C140M04B;
import com.mega.eloan.lms.model.C140M07A;
import com.mega.eloan.lms.model.C140S07B;

/**
 * <pre>
 * 徵信調查報告書第柒章 授信明細(子) 格式1,2共用
 * </pre>
 * 
 * @since 2011/10/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/05,<PERSON><PERSON>hian<PERSON>, new
 *          </ul>
 */
public interface C140S07BDao extends IGenericDao<C140S07B> {
	
	/**
	 * 刪除所有資料
	 * @param meta C140M07A
	 * @return Integer
	 */
	int deleteByMeta(C140M07A meta);
	
	/**
	 * 刪除所有資料
	 * @param meta C140M04B
	 * @return Integer
	 */
	int deleteByMeta(C140M04B meta);
}
