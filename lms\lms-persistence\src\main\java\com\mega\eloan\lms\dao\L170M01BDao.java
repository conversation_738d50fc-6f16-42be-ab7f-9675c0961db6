package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;


import com.mega.eloan.lms.model.L170M01B;


/** 一般授信資料檔 **/
public interface L170M01BDao extends IGenericDao<L170M01B> {

	L170M01B findByOid(String oid);

	List<L170M01B>  findByMainId(String mainId);

	L170M01B findByUniqueKey(String mainId, String custId, String dupNo,
			String cntrNo);

	//boolean deleteL170m01bList(String mainId);

	boolean deleteL170m01bListNotLnDataDate(String mainId);

	List<Object[]> findCntrNo(String cntrNo);
	List<L170M01B> findByCntrNo(String CntrNo);
	List<L170M01B> findByCustIdDupId(String custId,String DupNo);
	
}