<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:panel>
            <div id="filterBox" style="display:none">
                <form id="filterForm">
                    <div class="" id="">
                        <table class="tb2" border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tbody>
                            	<tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="C900M01E.custId">統一編號</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<input type="text" size="10" maxlength="10" id="custId" name="custId" class="upText" />
                                    </td>
                                </tr>
								<tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="doc.custName">姓名/名稱</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<input type="text" id="custName" name="custName" class="" maxlength="120" size="30" />(<wicket:message key="label.compareLike" />)
                                    </td>
                                </tr>    
                                <tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="C900M01E.comId">任職公司統編</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<input type="text" size="10" maxlength="10" id="comId" name="comId" />
                                    </td>
                                </tr> 
								<tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="C900M01E.comName">任職公司名稱</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<input type="text" id="comName" name="comName" class="" maxlength="250" size="30" />(<wicket:message key="label.compareLike" />)
                                    </td>
                                </tr>
								<tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="C900M01E.comTarget">服務單位地址</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<input type="text" id="comTarget" name="comTarget" class="" size="30" />(<wicket:message key="label.compareLike" />)
                                    </td>
                                </tr>
								<tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="C900M01E.sourceNo">來源文號</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<input type="text" id="sourceNo" name="sourceNo" class="" maxlength="128" maxlengthC="42" size="30" />(<wicket:message key="label.compareLike" />)
                                    </td>
                                </tr>                                
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <script>
                var FilterAction = {
                    formId: "#filterForm",
                    gridId: "#gridview",
                    openBox: function(){
                        var $form = $(this.formId).reset();
                        
                        $("#filterBox").thickbox({
                            //query=查詢
                            title: i18n.def["query"],
                            width: 550,
                            height: 320,
                            modal: true,
                            i18n: i18n.def,
                            readOnly: false,
                            align: "center",
                            valign: "bottom",
                            buttons: {
                                "sure": function(){
                                    if (!$form.valid()) {
                                        return false;
                                    }
                                    FilterAction.reloadGrid(JSON.stringify($form.serializeData()));
                                    $.thickbox.close();
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    },
                    /**更新grid
                     *
                     * @param {Object} data 查詢條件
                     */
                    reloadGrid: function(data){
                        $(this.gridId).jqGrid("setGridParam", {
                            postData: {
                                formAction: "queryMain",
                                filetData: data
                            },
                            page: 1,
                            search: true
                        }).trigger("reloadGrid");
                    }
                };
            </script>
        </wicket:panel>
    </body>
</html>
