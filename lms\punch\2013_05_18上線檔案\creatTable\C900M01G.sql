---------------------------------------------------------
-- LMS.C900M01G 團貸分戶明細檔
---------------------------------------------------------

--DROP TABLE LMS.C900M01G;
CREATE TABLE LMS.C900M01G (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	CUSTID        VARCHAR(10)  ,
	<PERSON>UPNO         CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>        CHAR(12)      not null,
	<PERSON>RP<PERSON><PERSON><PERSON><PERSON><PERSON>     CHAR(12)      not null,
	STAT<PERSON>		  CHAR(1)      ,
	APPLYCURR     CHAR(3)      ,
	APPLYAMT      DECIMAL(17,2),
	APPROVECURR   CHAR(3)      ,
	APPROVEAMT    DECIMAL(17,2),
	<PERSON><PERSON><PERSON><PERSON><PERSON>      CHAR(3)      ,
	<PERSON><PERSON><PERSON><PERSON>       DECIMAL(17,2),
	AP<PERSON>O<PERSON>DATE   DATE         ,
	<PERSON><PERSON><PERSON>ATE      DATE         ,
	USEFLAG       CHAR(1)      ,
	CREATO<PERSON>       CHAR(6)      ,
	CREATE<PERSON><PERSON>    TIMESTAMP    ,
	<PERSON><PERSON><PERSON><PERSON>       CHAR(6)      ,
	<PERSON>DATETIME    TIMESTAMP    ,

	constraint P_C900M01G PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC900M01G01;
CREATE UNIQUE INDEX LMS.XC900M01G01 ON LMS.C900M01G   (CNTRNO, GRPCNTRNO);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C900M01G IS '團貸分戶明細檔';
COMMENT ON LMS.C900M01G (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	CUSTID        IS '身分證統編', 
	DUPNO         IS '身分證統編重複碼', 
	CNTRNO        IS '分戶額度序號', 
	GRPCNTRNO     IS '團貸總戶序號', 
	STATUS		  IS '狀態'      ,
	APPLYCURR     IS '申請幣別', 
	APPLYAMT      IS '申請金額', 
	APPROVECURR   IS '核准幣別', 
	APPROVEAMT    IS '核准金額', 
	LOANCURR      IS '動用幣別', 
	LOANAMT       IS '動用金額', 
	APPROVEDATE   IS '核定日期', 
	LOANDATE      IS '動用日期', 
	USEFLAG       IS '不再動用', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
