<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        <!-- 個金基本資料檔 -->
            <div id="C101S01ADiv" name="C101S01ADiv" class="naturalClass">
                <form id="C101S01AForm" name="C101S01AForm">
                    <table class="tb2" width="100%">
                        <tr>
                            <td width="18%" class="hd2" align="right">
                                <span class="text-red">＊</span>
                                <th:block th:text="#{'C101S01A.birthday'}">出生日期</th:block>&nbsp;&nbsp;
                            </td>
                            <td width="32%">
                                <input type="text" id="birthday" name="birthday" class="required date" />
                            </td><!-- <td class="hd2" align="right" ><wicket:message key="C101M01A.custNo">客戶編號</wicket:message>&nbsp;&nbsp;</td> --><!-- <td ><input type="text" id="custNo" name="custNo" class="max" maxlength="7" /></td> -->
                            <td width="18%" class="hd2" align="right">
                                <!--<span class="text-red">＊</span>-->
                                <th:block th:text="#{'label.idCardInfo'}">身分證欄位</th:block>&nbsp;&nbsp;
                            </td>
                            <td width="32%">
                                <table class="tb2">
                                    <tr>
                                        <td class="noborder">
                                            <th:block th:text="#{'label.idCardIssueDate'}">發證日期</th:block>
                                        </td>
                                        <td class="noborder">
                                            <input type="text" id="idCardIssueDate" name="idCardIssueDate" class="date" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="noborder">
                                            <th:block th:text="#{'label.idCard_siteId'}">發證地點</th:block>
                                        </td>
                                        <td class="noborder">
                                            <select id="idCard_siteId" name="idCard_siteId" class="" codeType="C101S01A_idCard_siteId" ></select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="noborder">
                                            <th:block th:text="#{'label.idCardChgFlag'}">領補換類別</th:block>
                                        </td>
                                        <td class="noborder">
                                            <select id="idCardChgFlag" name="idCardChgFlag" class="" codeType="C101S01A_idCardChgFlag" ></select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="noborder">
                                            <th:block th:text="#{'label.idCardPhoto'}">有無照片</th:block>
                                        </td>
                                        <td class="noborder">
                                            <select id="idCardPhoto" name="idCardPhoto" class="" codeType="C101S01A_idCardPhoto" ></select>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td width="18%" class="hd2" align="right">
                                <span class="text-red">＊</span>
                                <th:block th:text="#{'C101S01A.edu'}">學歷</th:block>&nbsp;&nbsp;
                            </td>
                            <td width="32%">
                                <select id="edu" name="edu" class="required" codeType="cls1131m01_edu" ></select>
                            </td>
                            <td width="18%" class="hd2" align="right">
                                <!--<span class="text-red">＊</span>-->
                                <th:block th:text="#{'C101S01A.sex'}">性別</th:block>&nbsp;&nbsp;
                            </td>
                            <td width="32%">
                                <input type="radio" id="sex" name="sex" codeType="sex" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2" align="right">
                                <span class="text-red">＊</span>
                                <th:block th:text="#{'C101S01A.marry'}">婚姻狀況</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <input type="radio" id="marry" name="marry" class="required" codeType="marry" itemStyle="size:3" />
                                <br/>
                                <th:block th:text="#{'C101S01A.child'}">子女數</th:block>
                                <input type="text" id="child" name="child" class="max number" maxlength="2" size="2"/>
                            </td>
                            <td class="hd2" align="right">
                                <th:block th:text="#{'C101S01A.foreginal'}">外國人無特定住所</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <input type="checkbox" id="foreginal" name="foreginal" value="Y" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2" align="right">
                                <th:block th:text="#{'C101S01A.coTel'}">通訊電話</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <input type="text" id="coTel" name="coTel" class="max" maxlength="150" />
                            </td>
                            <td class="hd2" align="right">
                                <th:block th:text="#{'C101S01A.fTel'}">戶籍電話</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <input type="text" id="fTel" name="fTel" class="max" maxlength="150" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2" align="right">
                                <th:block th:text="#{'C101S01A.mTel'}">行動電話</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <input type="text" id="mTel" name="mTel" class="max" maxlength="150" />
                            </td>
                            <td class="hd2" align="right">
                                <th:block th:text="#{'C101S01A.email'}">email</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <input type="text" id="email" name="email" class="max email" maxlength="120" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2" align="right">
                                <span class="text-red">＊</span>
                                <th:block th:text="#{'C101S01A.fAddr'}">戶籍地址</th:block>&nbsp;&nbsp;
                            </td>
                            <td colspan="3">
                                <input type="text" id="fCity" name="fCity" style="display:none;"/><input type="text" id="fZip" name="fZip" style="display:none;"/><input type="text" id="fAddr" name="fAddr" style="display:none;"/><a href="#" id="fTargetLink" class="readOnly"><span id="fTarget" class="field comboSpace" ></span></a>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2" align="right">
                                <span class="text-red">＊</span>
                                <th:block th:text="#{'C101S01A.coAddr'}">通訊地址</th:block>&nbsp;&nbsp;
                                <br/>
                                <button type="button" id="btSameAddr" name="btSameAddr">
                                    <span class="text-only">
                                        <th:block th:text="#{'C101S01A.btSameAddr'}">同戶籍地址</th:block>
                                    </span>
                                </button>
                            </td>
                            <td colspan="3">
                                <input type="text" id="coCity" name="coCity" style="display:none;"/><input type="text" id="coZip" name="coZip" style="display:none;"/><input type="text" id="coAddr" name="coAddr" style="display:none;"/><a href="#" id="coTargetLink" class="readOnly"><span id="coTarget" class="field comboSpace" ></span></a>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2" align="right">
                                <span class="text-red">＊</span>
                                <th:block th:text="#{'C101S01A.houseStatus'}">現住房屋</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <select id="houseStatus" name="houseStatus" class="setItem" codeType="lms1205s01_houseStatus" itemStyle="format:{value}-{key}" ></select>
                            </td>
                            <td class="hd2" align="right">
                                <!--<span class="text-red">＊</span>-->
                                <th:block th:text="#{'C101S01A.cmsStatus'}">不動產狀況</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <select id="cmsStatus" name="cmsStatus" class=" setItem" codeType="lms1205s01_cmsStatus" ></select>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2" align="right">
                                <th:block th:text="#{'C101S01A.dpBank'}">存款往來銀行</th:block>&nbsp;&nbsp;
                                <br/>
                                <button type="button" id="btDpBankAsUserInfoUnitNo" name="btDpBankAsUserInfoUnitNo">
                                    <span class="text-only">
                                        <th:block th:text="#{'C101S01A.btDpBankAsUserInfoUnitNo'}">預設本分行</th:block>
                                    </span>
                                </button>
                            </td>
                            <td>
                                <!-- 存款往來銀行 --><input type="text" id="dpBank" name="dpBank" style="display:none;"/><!-- 存款往來分行代碼 --><input type="text" id="dpBrno" name="dpBrno" style="display:none;"/><!-- 存款往來銀行(海外用) --><a href="#" id="dpBankNameLink" class="readOnly"><span id="dpBankName" class="field comboSpace" ></span></a>
                            </td>
                            <td class="hd2" align="right">
                                <th:block th:text="#{'C101S01A.dpAcct'}">存款帳戶</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <input type="text" id="dpAcct" name="dpAcct" class="obuText max" maxlength="16" />
                            </td>
                        </tr>
                        <tr id="CheckListFlag">
                            <td class="hd2">
                                <span class="text-red">＊</span>
                                <th:block th:text="#{'C101S01V.isCheckListFlag'}">是否填寫申請資料核對表</th:block>&nbsp;
                            </td>
                            <td colspan="3">
                                <label>
                                    <input type="radio" id="checkListFlag" name="checkListFlag" value="Y"/>
                                    <th:block th:text="#{'cls1201s24.th3'}">是</th:block>
                                </label>
                                <label>
                                    <input type="radio" id="checkListFlag" name="checkListFlag" value="N"/>
                                    <th:block th:text="#{'cls1201s24.th4'}">否</th:block>
                                </label>
                                <br>
                                <label class="text-red">
                                    <th:block th:text="#{'C101S01V.CheckListFlagDesc'}">案件為「新做」(排除勞工紓困、團體消貸、卡友信貸)需填是，其他則填否。</th:block>
                                </label>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
        </th:block>
    </body>
</html>
