/* 
 * LMS9515GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.grid;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.I18NFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.model.L784M01A;
import com.mega.eloan.lms.model.L784S01A;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.eloan.lms.model.VL784S07A01;
import com.mega.eloan.lms.rpt.service.LMS9515Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 最新管理報表
 * </pre>
 * 
 * @since 2012/2/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/15,jessica,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9515gridhandler")
public class LMS9515GridHandler extends AbstractGridHandler {

	@Resource
	LMS9515Service service;

	@Resource
	BranchService branch;

	@Resource
	UserInfoService userInfoService;

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	@SuppressWarnings({ "rawtypes", "unchecked", "deprecation" })
	public CapGridResult queryL951m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		String searchAction = Util
				.nullToSpace(params.getString("searchAction"));
		Date searchDate = CapDate.getCurrentTimestamp();
		// searchDate.setMonth(searchDate.getMonth() - 1 );
		searchDate.setDate(1);
		Date searchEnDate = CapDate.getCurrentTimestamp();
		// searchEnDate.setMonth(searchEnDate.getMonth() - 1 );
		searchEnDate.setDate(CapDate.getDayOfMonth(TWNDate.toAD(searchEnDate)
				.split("-")[0], TWNDate.toAD(searchEnDate).split("-")[1]));
		// searchEnDate =
		// CapDate.formatyyyyMMddToDateFormat(CapDate.addMonth(searchDate.replace("-",
		// ""), 1),"yyyy-MM-dd");
		int actionCode = Util.parseInt(searchAction);

		searchDate = Util.parseDate(CapDate
				.formatDate(searchDate, "yyyy-MM-dd"));
		searchEnDate = Util.parseDate(CapDate.formatDate(searchEnDate,
				"yyyy-MM-dd"));

		String searchDateX = CapDate.formatDate(searchDate, "yyyy-MM-dd");
		String searchEnDateX = CapDate.formatDate(searchEnDate, "yyyy-MM-dd");

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l784a01a.authUnit", user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptType",
				String.valueOf(actionCode));

		Object[] reason = { searchDateX, searchEnDateX };
		pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "creatDate",
				reason);
		pageSetting.addOrderBy("createTime", true);
		pageSetting.setMaxResults(1);
		// pageSetting.addOrderBy("createTime");
		Page page = service.findPage(L784M01A.class, pageSetting);
		List<L784M01A> list = page.getContent();
		for (L784M01A model : list) {
			model.setOwnBrId(model.getOwnBrId()
					+ " "
					+ Util.nullToSpace(branch.getBranchName(model.getOwnBrId())));
			model.setBranchId(model.getBranchId()
					+ Util.trim(branch.getBranchName(model.getBranchId())));
		}
		return new CapGridResult(list, page.getTotalRow());

	}

	/**
	 * 查詢Grid 資料(L784s07a)
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	public CapGridResult queryL784s07aForTotalMonth(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		String brNo = Util.nullToSpace(params.getString("brNo"));

		// 判定是否已註記被刪除
		// pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
		// null);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "brNo", brNo);
		// pageSetting.setDistinct(true);
		Page<? extends GenericBean> page = service.findPage(L784S07A.class,
				pageSetting);
		// 加入格式化
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();

		dataReformatter.put("caseDept", new I18NFormatter("caseDept."));
		dataReformatter.put("updater", new UserNameFormatter(userInfoService)); // codeType格式化
		result.setDataReformatter(dataReformatter);

		return result;
	}

	public CapMapGridResult queryL784s07a(ISearch pageSetting,
			PageParameters params) throws CapException {
		int startYear = params.getInt("startYear", 0);
		String year = startYear == 0 ? String.valueOf(
				TWNDate.toTW(CapDate.getCurrentTimestamp())).split("/")[0]
				: ((startYear - 1911) + "");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "apprYY", year);
		// 判定是否已註記被刪除
		// pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
		// null);
		// pageSetting.setDistinct(true);
		Page<? extends GenericBean> page = service.findPage(VL784S07A01.class,
				pageSetting);
		List<? extends GenericBean> list = page.getContent();
		String mainId = null;
		if (list.size() > 0) {
			mainId = Util.trim(list.get(0).get("mainId"));
		}
		List<IBranch> branchList = branch.getAllBranch();
		List<Map<String, Object>> branchMapList = new LinkedList<Map<String, Object>>();
		for (IBranch ibranch : branchList) {
			Map<String, Object> map = new LinkedHashMap<String, Object>();
			map.put("oid", ibranch.getBrNo());
			map.put("mainId", mainId);
			map.put("apprYY", year);
			map.put("brNo", ibranch.getBrName());
			map.put("caseDept", "3");
			branchMapList.add(map);
		}
		Page<Map<String, Object>> pages = LMSUtil.setPageMap(branchMapList,
				pageSetting);

		// 加入格式化
		CapMapGridResult result = new CapMapGridResult(pages.getContent(),
				branchMapList.size());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("caseDept", new I18NFormatter("caseDept."));
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 查詢Grid 資料(L784s01a)
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	public CapGridResult queryL784s01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 判定是否已註記被刪除
		// pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
		// null);
		Page<? extends GenericBean> page = service.findPage(L784S01A.class,
				pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());

	}
}
