/*
 * LMSService.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S20A;
import com.mega.eloan.lms.model.L120S20B;
import com.mega.eloan.lms.model.L120S21A;
import com.mega.eloan.lms.model.L120S21B;
import com.mega.eloan.lms.model.L120S21C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01O;

/**
 * <pre>
 * J-110-0986_05097_B1001 於簽報書新增LGD欄位
 * </pre>
 *
 * @since 2012/1/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/13,REX,new
 *          </ul>
 */
/**
 * <AUTHOR>
 * 
 */
public interface LMSLgdService extends ICapService {

	/**
	 * save
	 * 
	 * @param entity
	 */
	void save(GenericBean... entity);

	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * delete
	 * 
	 * @param entity
	 */
	void delete(GenericBean... entity);

	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId);

	void deleteListL120s20a(List<L120S20A> list);

	void deleteListL120s20b(List<L120S20B> list);

	/**
	 * 利用mainId取得異常通報表事項檔群組
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L120S20A> findL120s20aByMainId(String mainId);

	List<L120S20A> findL120s20aByMainIdCntrNoCo(String mainId, String cntrNoCo);

	List<L120S20A> findL120s20aByMainIdCntrNo(String mainId, String cntrNo);

	List<L120S20A> findL120s20aByMainIdCntrNoCoAndCntrNo(String mainId,
			String cntrNoCo, String cntrNo);

	List<Object[]> findL120s20aMinAllocate(String mainId);

	List<L120S20B> findL120s20bByMainId(String mainId);

	L120S20B findL120s20bByMainIdCntrNo(String mainId, String cntrNo);

	public List<L120S21A> findL120s21aByMainIdAndCntrNo(String mainId,
			String cntrNo);

	public List<L120S21A> findL120s21aByMainIdAndCntrNoCo(String mainId,
			String cntrNoCo);

	public List<L120S21A> findL120s21aByMainIdAndCntrNoCoAndCntrNo(
			String mainId, String cntrNoCo, String cntrNo);

	public void saveL120s21aList(List<L120S21A> list);

	public L120S21A findL120s21aByOid(String oid);

	public List<L120S21A> findL120s21aByMainId(String mainId);

	List<Object[]> findL120s21aMinAllocate(String mainId);

	public void deleteListL120s21a(List<L120S21A> list);

	public void deleteListL120s21aByOid(String[] oidArray);

	public L120S21B findL120s21bByMainIdAndCntrNo(String mainId, String cntrNo);

	public List<L120S21B> findL120s21bByCustId(String mainId, String custId,
			String dupNo);

	public L120S21B findL120s21bByOid(String oid);

	public List<L120S21B> findL120s21bByMainId(String mainId);

	public void deleteListL120s21b(List<L120S21B> list);

	public L120S21C findL120s21cByOid(String oid);

	public List<L120S21C> findL120s21cByMainId(String mainId);

	public void deleteListL120s21c(List<L120S21C> list);

	public void deleteListL120s21cByOid(String[] oidArray);

	List<L120S21C> findL120s21cByMainIdAndCollType(String mainId,
			String cntrNo_s21c, String collType_s21c);

	List<L120S21C> findL120s21cByMainIdAndCollKind(String mainId,
			String cntrNo_s21c, String colKind_s21c);

	public List<L120S21B> findL120s21bByMainIdCustIdAndBussType(String mainId,
			String custId_s21b, String dupNo_s21b, String bussType_s21b);

	/**
	 * 取得額度明細表LGD業務種類
	 * 
	 * J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	 * 
	 * @param l140m01a
	 * @return
	 */
	String getCntrBussType(L140M01A l140m01a);

	/**
	 * 判斷要計算LGD之額度明細表
	 * 
	 * J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	 * 
	 * @param l140m01a
	 * @param chkType
	 *            1:授權額度合計判斷用 (授審處金至忠) 2:引入計算LGD用(風控處邱煥翔)
	 * @return
	 */
	public boolean isL140m01aNeedLgd(L140M01A l140m01a, String chkType);

	/**
	 * 該額度是否需要計算授信額度合計
	 * 
	 * @param l140m01a
	 * @param chkType
	 *            1:授權額度合計判斷用 (授審處金至忠) - 非單獨另計授權額度(納入授權額度限額計算註記=1) 3:授權額度合計判斷用
	 *            (授審處金至忠) - 單獨另計授權額度(納入授權額度限額計算註記=2-8)
	 * @return
	 */
	public boolean isL140m01aNeedCountLgdTotal(L140M01A l140m01a, String chkType);

	/**
	 * J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
	 * 
	 * 擔保品建擋隱藏順位或前順位
	 * 
	 * @param collKind
	 * @return
	 */
	public boolean hideRgstInfoForL120s21c(String collKind);

	/**
	 * J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
	 * 
	 * 取得LGD參數(擔保品回收率只有zh_TW，所以英文版會抓不到)
	 * 
	 * @param l120m01a
	 * @param cntrNo
	 * @param codeTypePrefix
	 * @return
	 */
	public Map<String, String> findLgdCollKindCodeTypeMap(L120M01A l120m01a,
			String cntrNo, String codeTypePrefix);

	/**
	 * J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
	 * 
	 * 計算LGD前清除前次LGD計算相關資料
	 * 
	 * @param l120s21b
	 */
	public void clearL120s21bLgdData(L120S21B l120s21b);

	/**
	 * J-111-0572_05097_B1001 Web e-Loan企金授信配合e-Loan LGD擔保品分配規則修改
	 * 
	 * @param l120s21b
	 * @return
	 */
	public String getCollateralRecovery(L120S21B l120s21b);

	/**
	 * J-111-0572_05097_B1001 Web e-Loan企金授信配合e-Loan LGD擔保品分配規則修改
	 */
	public List<L140M01O> findL140m01oByMainIdOrderForLgd(String mainId);

	/**
	 * 依借款人行業對象別判斷是否需要計算授信額度合計 FOR 雙軌判斷
	 * 
	 * @param l140m01a
	 * @return
	 */
	public boolean isCustNeedCountLgdTotal(L140M01A l140m01a);

	/**
	 * 取得借款人行業對象別BY額度明細表
	 * 
	 * @param l140m01a
	 * @return
	 */
	public String getCustBusCodeByL140m01a(L140M01A l140m01a);

	/**
	 * 依借款人行業對象別判斷是否需要計算授信額度合計 FOR 雙軌判斷
	 * 
	 * 雙軌期間LGD不要印在額度明細表
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean isCountLgdTotalOnTestPeriod(L120M01A l120m01a);

	/**
	 * 依借款人行業對象別判斷是否需要計算授信額度合計 FOR 雙軌判斷
	 * 
	 * 雙軌期間LGD不要印在額度明細表
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean isCountLgdTotalOnTestPeriod_2(L120M01A l120m01a);

	/**
	 * 取得額度明細表LGD業務種類Map型態
	 * 
	 * J-111-0461_05097_B1001 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
	 * 
	 * @param l140m01a
	 * @return
	 */
	public Map<String, String> getCntrBussTypeMap(L140M01A l140m01a);

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21b
	 * @return
	 * @throws Exception
	 */
	public String chkIsGuarantorEffect_s21b(L120S21B l120s21b);

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param crdType
	 * @param gradeOrg
	 * @return
	 */
	public String convertMowGrade(String crdType, String gradeOrg);

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21b
	 * @return
	 */
	public void chkL120s21b(L120S21B l120s21b) throws CapException;

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21b
	 * @return
	 */
	public BigDecimal getUnsecuredRecoveryRate(L120S21B l120s21b);

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21b
	 * @return
	 */
	public String caculateLgd(L120S21B l120s21b) throws CapMessageException;

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21b
	 * @return
	 */
	public String caculateLgd_1(L120S21B l120s21b) throws CapMessageException;

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21c
	 * @return
	 */
	public String calcL120s21c(L120S21C l120s21c) throws CapMessageException;

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21b
	 * @return
	 */
	public String caculateLgd_2(L120S21B l120s21b) throws CapMessageException;

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param mainId
	 * @return
	 */
	public String calcCustLgd(String mainId) throws CapException;

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等 補齊 L120S21B
	 * 
	 * @param mainId
	 * @return
	 */
	public void furtherL120s21b(String mainId);

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等 清查 S21B -
	 * 只留共用及額度明細表
	 * 
	 * @param mainId
	 * @return
	 */
	public void inventoryL120s21b(String mainId);

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param mainId
	 * @return
	 */
	public void syncL120s21bInfo(String mainId, boolean reImpl);

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param mainId
	 * @return
	 */
	public void importL120s21bLookupsRate(String mainId) throws CapException;

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等 初始化 L120S21B
	 * 
	 * @param mainId
	 * @return
	 */
	public void initialL120s21b(String mainId);

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等 更新 S21B 資訊 -
	 * 信保、信保保證成數；有無公司保證人
	 * 
	 * @param mainId
	 * @return
	 */
	public void getL120s21bInfo(L120S21B l120s21b, String cntrNo,
			boolean hasCntrDoc, BranchRate branchRate);

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param mainId
	 * @return
	 */
	public void calcTWD(String mainId, BranchRate branchRate);

	/**
	 * J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等 更新 S21B 資訊 -
	 * 信保、信保保證成數；有無公司保證人
	 * 
	 * @param mainId
	 * @return
	 */
	public String calcLGDInner(String mainId)
			throws CapException;

	/**
	 * J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 */
	public String calcBussLgd(String mainId) throws CapException;

	/**
	 * 
	 * @param mainId
	 * @param cntrNo
	 * @return
	 */
	public Boolean isHasCntrDoc(String mainId, String cntrNo);

	/**
	 * 取得此份簽報書之額度明細表序號
	 */
	public List<String> getCntrNoList(String mainId);

	/**
	 * 共用資訊重新引進額度明細表現請額度
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 */
	public String reNewCntrDocCreditData(String mainId) throws CapException;

	/**
	 * 
	 * @param mainId
	 * @param cntrNo
	 * @param l120s21a
	 * @param setOnlyFromL140m01a
	 * @return
	 */
	public boolean setLatestLoanDataByCntrNo(String mainId, String cntrNo,
			L120S21A l120s21a, boolean setOnlyFromL140m01a);

	/**
	 * 
	 * @param mainId
	 */
	public void reAllocate(String mainId);

	/**
	 * 
	 * @param mainId
	 */
	public void initialAllocate(String mainId);

	public String chkL120s21aData(L120S21A l120s21a);

	public String getFullCustIdByCntrNo(String cntrNo, String mainId);

	public String calcEADInner(String mainId)
			throws CapException;

	/**
	 * J-112-0210_05097_B1003 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
	 * 
	 * @param l120s21c
	 * @return
	 */
	public void chkL120s21c(L120S21C l120s21c) throws CapException;

	/**
	 * J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 * 
	 * 回傳額度明細表顯示建議授權
	 * 
	 * @param l120m01a
	 * @param l140m01a
	 * @param itemType
	 * @return
	 */
	public String getLgdSuggestMsg(L120M01A l120m01a, L140M01A l140m01a,
			String itemType);

	/**
	 * J-114-XXX1 LGD合格保證人納入境外保證人
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, String> getCustBestElf338nGrade(String custId,
			String dupNo);

	/**
	 * 借款人LGD計算範圍請排除
	 * 
	 * J-112-0278_05097_B1001 Web
	 * e-Loan企金授信借款人LGD計算範圍排除進出口額度，並於試算頁簽內加註：借款人違約損失率不含進出口額度。
	 * 
	 * 簽案LGD試算頁籤的「借款人LGD」係作為授信利率合理性分析之用。考量進出口額度相關利費率已另有進出口作業規範，
	 * 故借款人LGD計算範圍請排除進出口額度。並於試算頁簽內加註：借款人違約損失率不含進出口額度。
	 * 
	 * @param l140m01a
	 * @return
	 */
	public boolean isCustLgdNeed(L140M01A l140m01a);

}