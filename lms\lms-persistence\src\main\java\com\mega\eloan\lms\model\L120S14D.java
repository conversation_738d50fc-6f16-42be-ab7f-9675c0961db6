/* 
 * L120S14D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 扣帳授權書資料檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S14D", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S14D extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 新產生時：getUUID()
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	@Size(max=32)
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 
	 * 被授權行<p/>
	 * 預設 分行ID
	 */
	@Size(max=3)
	@Column(name="AGENTBRID", length=3, columnDefinition="CHAR(3)")
	private String agentBrId;

	/** 
	 * 存款種類<p/>
	 * 自動引入
	 */
	@Size(max=30)
	@Column(name="DEPOSITTYPE", length=30, columnDefinition="VARCHAR(30)")
	private String depositType;

	/** 
	 * 存款帳戶<p/>
	 * 自動引入
	 */
	@Size(max=14)
	@Column(name="DEPOSITACCT", length=14, columnDefinition="VARCHAR(14)")
	private String depositAcct;

	/** 借款人 **/
	@Size(max=150)
	@Column(name="BORROWER", length=150, columnDefinition="VARCHAR(150)")
	private String borrower;

	/** 
	 * 存款人<p/>
	 * 自動引入
	 */
	@Size(max=150)
	@Column(name="DEPOSITOR", length=150, columnDefinition="VARCHAR(150)")
	private String depositor;

	/** 存款人統一編號 **/
	@Size(max=10)
	@Column(name="DEPOSITORID", length=10, columnDefinition="VARCHAR(10)")
	private String depositorId;

	/** 
	 * 存款人地址<p/>
	 * 自動引入
	 */
	@Size(max=192)
	@Column(name="DEPOSITORADDR", length=192, columnDefinition="VARCHAR(192)")
	private String depositorAddr;

	/** 
	 * 負責人<p/>
	 * 自動引入
	 */
	@Size(max=150)
	@Column(name="DIRECTOR", length=150, columnDefinition="VARCHAR(150)")
	private String director;

	/** 
	 * 負責人統一編號<p/>
	 * 自動引入
	 */
	@Size(max=10)
	@Column(name="DIRECTORID", length=10, columnDefinition="VARCHAR(10)")
	private String directorId;

    /** 額度序號 **/
    @Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
    private String cntrNo;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 新產生時：getUUID()
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  新產生時：getUUID()
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	public String getRandomCode() {
		return this.randomCode;
	}
	/**
	 *  設定文件亂碼<p/>
	 *  每次儲存：getRandomCode()
	 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 
	 * 取得被授權行<p/>
	 * 預設 分行ID
	 */
	public String getAgentBrId() {
		return this.agentBrId;
	}
	/**
	 *  設定被授權行<p/>
	 *  預設 分行ID
	 **/
	public void setAgentBrId(String value) {
		this.agentBrId = value;
	}

	/** 
	 * 取得存款種類<p/>
	 * 自動引入
	 */
	public String getDepositType() {
		return this.depositType;
	}
	/**
	 *  設定存款種類<p/>
	 *  自動引入
	 **/
	public void setDepositType(String value) {
		this.depositType = value;
	}

	/** 
	 * 取得存款帳戶<p/>
	 * 自動引入
	 */
	public String getDepositAcct() {
		return this.depositAcct;
	}
	/**
	 *  設定存款帳戶<p/>
	 *  自動引入
	 **/
	public void setDepositAcct(String value) {
		this.depositAcct = value;
	}

	/** 取得借款人 **/
	public String getBorrower() {
		return this.borrower;
	}
	/** 設定借款人 **/
	public void setBorrower(String value) {
		this.borrower = value;
	}

	/** 
	 * 取得存款人<p/>
	 * 自動引入
	 */
	public String getDepositor() {
		return this.depositor;
	}
	/**
	 *  設定存款人<p/>
	 *  自動引入
	 **/
	public void setDepositor(String value) {
		this.depositor = value;
	}

	/** 取得存款人統一編號 **/
	public String getDepositorId() {
		return this.depositorId;
	}
	/** 設定存款人統一編號 **/
	public void setDepositorId(String value) {
		this.depositorId = value;
	}

	/** 
	 * 取得存款人地址<p/>
	 * 自動引入
	 */
	public String getDepositorAddr() {
		return this.depositorAddr;
	}
	/**
	 *  設定存款人地址<p/>
	 *  自動引入
	 **/
	public void setDepositorAddr(String value) {
		this.depositorAddr = value;
	}

	/** 
	 * 取得負責人<p/>
	 * 自動引入
	 */
	public String getDirector() {
		return this.director;
	}
	/**
	 *  設定負責人<p/>
	 *  自動引入
	 **/
	public void setDirector(String value) {
		this.director = value;
	}

	/** 
	 * 取得負責人統一編號<p/>
	 * 自動引入
	 */
	public String getDirectorId() {
		return this.directorId;
	}
	/**
	 *  設定負責人統一編號<p/>
	 *  自動引入
	 **/
	public void setDirectorId(String value) {
		this.directorId = value;
	}

    /** 取得額度序號 **/
    public String getCntrNo() {
        return this.cntrNo;
    }

    /** 設定額度序號 **/
    public void setCntrNo(String value) {
        if (value != null) {
            // 一率轉大寫額度序號
            value = value.toUpperCase();
        }
        this.cntrNo = value;
    }
}
