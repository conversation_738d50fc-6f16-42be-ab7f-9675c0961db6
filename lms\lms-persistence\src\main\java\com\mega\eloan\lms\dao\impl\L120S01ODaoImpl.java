/* 
 * L120S01ODaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L120S01ODao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S01O;

/** 信用風險管理關係戶帳務明細檔
 * 
 * @version <ul>
 *          <li>2014/05/19,EL08034,指定MaxResult,避免只抓前100筆
 *          </ul>
 */
@Repository
public class L120S01ODaoImpl extends LMSJpaDao<L120S01O, String> implements
		L120S01ODao {

	@Override
	public L120S01O findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		search.setMaxResults(Integer.MAX_VALUE);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S01O> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S01O> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S01O> findByIndex01(String mainId, String custId,
			String dupNo, String relType) {
		ISearch search = createSearchTemplete();
		List<L120S01O> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (relType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "relType",
					relType);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S01O> findByIndex02(String mainId, String custId,
			String dupNo) {
		ISearch search = createSearchTemplete();
		List<L120S01O> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S01O> findByCustId(String mainId, String custId,
			String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S01O> list = createQuery(L120S01O.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<L120S01O> findByCustIdRelType(String mainId, String custId,
			String dupNo,String relType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "relType", relType);
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("relType");
		search.addOrderBy("custId");
		search.addOrderBy("dupNo");
		List<L120S01O> list = createQuery(L120S01O.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public int deleteByKey(String mainId, String custId, String dupNo) {
		Query query = entityManager.createNamedQuery("L120S01O.deleteByKey");
		query.setParameter("MAINID", mainId);
		query.setParameter("CUSTID", custId);
		query.setParameter("DUPNO", dupNo);
		return query.executeUpdate();
		
	}
}