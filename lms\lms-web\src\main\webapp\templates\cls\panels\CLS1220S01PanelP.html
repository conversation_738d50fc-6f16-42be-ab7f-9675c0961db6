<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <script type="text/javascript">loadScript('pagejs/cls/CLS1220S01PanelP');</script>
			 <script type="text/javascript">loadScript('pagejs/cls/CLS1220S06Panel01');</script>
            <!--======================================================-->
                <fieldset>
                    <legend>
                        <th:block th:text="#{'doc.docinfo'}"></th:block>
                    </legend>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr class='align_top'>      	
								<td class="hd2 rt" nowrap>
                                	<span class="color-red">＊</span>
                                    <th:block th:text="#{'C122M01A.ploanCaseNo'}">案件編號</th:block>
                                </td>
                                <td >
                                    <span id="ploanCaseNo" name="ploanCaseNo" ></span>
									<button type="button" id="btnPrintBarcode" class="forview">
			                            <span class="text-only">產生文件掃描封面條碼</span>
			                        </button>
                                </td>	                         
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'doc.attchGrid'}">上傳檔案</th:block>
									<!--
									<br/>
									<span class='ploan_attch_downloadZIP' style='color:#5291EF; text-decoration:underline;'><th:block th:text="#{'label.ploan_attch_downloadZIP'}">整批下載</th:block></span>
									-->
									<br/>
									<button type="button" id="uploadFile">
			                            <span class="text-only"><th:block th:text="#{'button.uploadFile'}"><!-- 選擇附加檔案--></th:block></span>
			                        </button>
									<br/>
			                        <button type="button" id="deleteFile">
			                            <span class="text-only"><th:block th:text="#{'button.deleteFile'}"><!-- 刪除--></th:block></span>
			                        </button>
									<br/>
									<div id = "uploadFileDiv_temp" class="hide">
										<span class="text-only">文件種類</span><br/>
										<select id="uploadFormId_temp" name="uploadFormId_temp" class="required" codeType="MEGAIMAGE_FormId" ></select><br/>
										<span class="text-only">對應客戶</span><br/>
										<select id="uploadFileRelationship_temp" name="uploadFileRelationship_temp" class="required"></select>
										<input type="hidden" id="formId_temp"></input>
										<input type="hidden" id="stakeholderID_temp"></input>
									</div>
                                </td>
                                <td>
                                    <div id='attchGrid' style='margin-left:0px;'>
			   						</div>
                                </td>						
                            </tr>
							<tr class='align_top'>      	
								<td class="hd2 rt" nowrap>
                                    使用專案
                                </td>
                                <td >
                                    <select name="usePlan" id="usePlan" class="required"></select>
                                </td>	                         
                                <td class="hd2 rt">                                   
                                    <span class="text-only"><th:block th:text="#{'label.megaImage'}">已電子化保存檔案</th:block></span>
			                        <button type="button" id="deleteFileMegaImage">
			                            <span class="text-only"><th:block th:text="#{'button.deleteFile'}"><!-- 刪除--></th:block></span>
			                        </button>
									<br/>
									<!-- 同步檔案清單-->
									<button type="button" id="btnRefreshAttchGrid" class="forview">
			                            <span class="text-only"><th:block th:text="#{'button.btnRefreshAttchGrid'}">同步檔案清單</th:block></span>
			                        </button>
									<!-- 開啟文件數位化系統-->
									<button type="button" id="btnCLSQuery" class="forview">
			                            <span class="text-only"><th:block th:text="#{'button.btnCLSQuery'}">開啟文件數位化系統</th:block></span>
			                        </button>
									<!-- 下載-->
									<div class="hide">
										<button type="button" id="btnRPAQueryCLImage">
				                            <span class="text-only"><th:block th:text="#{'button.btnRPAQueryCLImage'}">下載已電子化保存檔案</th:block></span>
				                        </button>
									</div>
                                </td>
                                <td>
									<div id='mEGAImageGrid' style='margin-left:0px;'>
			   						</div>	
                                </td>						
                            </tr>	
							<tr class="ploan_ixml_allowed">
								<td class="hd2 rt">
									<span class="text-only"><th:block th:text="#{'label.iXMLStatus'}">iXML查詢進度</th:block></span>
                                </td>
                                <td colspan="3">
                                	<div id='iXMLStatusGrid' style='margin-left:0px;'>
			   						</div>
									<th:block th:text="#{'Message.dataNotice'}"><!--財稅資料係為課稅目的的蒐集，僅供金融徵信案件之參考；若有資料內容疑問時，應向資料主管機關洽詢--></th:block>
                                </td>	
							</tr>	
							<tr class="ploan_decide_by_main_borrowser">
								<td width="20%" class="hd1">
                                    <th:block th:text="#{'title.caseprocess'}"><!--案件流程管理--></th:block>&nbsp;&nbsp;
									<br/>
									<button type="button" id="button_caseClosed">
			                            <span class="text-only"><th:block th:text="#{'button.caseClosed'}"><!--結案--></th:block></span>
			                        </button>
                                </td>
								 <td width="25%">
                                     <span id="docStatusDesc" name="docStatusDesc" ></span>
									 <br/><br/>
									 <span id="message" name="message" ></span>
									 
                                </td>
                                <td class="hd2 rt ploan_decide_by_main_borrowser" id="CaseProcess">
                                    <button type="button" id="buttonB00">
			                            <span class="text-only"><th:block th:text="#{'button.stateB00'}"><!--徵信--></th:block></span>
			                        </button>
									<button type="button" id="buttonA02">
			                            <span class="text-only"><th:block th:text="#{'button.stateA02'}"><!--補件通知--></th:block></span>
			                        </button>
                                    <button type="button" id="buttonB0304">
                                        <span class="text-only"><th:block th:text="#{'button.stateB0304'}"><!--新增簽報書--></th:block></span>
                                    </button>
                                </td>
                                <td width="35%" >  
									<button type="button" id="deleteDocStatusGrid">
			                            <span class="text-only"><th:block th:text="#{'button.delete'}"><!--移除綁定--></th:block></span>
			                        </button>                                 
                                	<div id='docStatusGrid' style='margin-left:0px;'>
			   						</div>	
								</td>								 
							</tr>
                        	<tr>
							 	<td width="20%" class="hd1">
                                    <th:block th:text="#{'doc.branchName'}">
                                        <!--  分行名稱-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="25%">
                                    <span id="ownBrId" ></span><span id="ownBrName" ></span>
                                </td>
                                <td class="hd2 rt">
                                    <th:block th:text="#{'C122M01A.orgBrId'}">原始申貸分行</th:block>
									<span id='changeOrgBrId' class='changeOrgBrId' style='color:#5291EF; text-decoration:underline; display:none;'>
										<th:block th:text="#{'spanbutton.changeOrgBrId'}">變更</th:block>
									</span>
									
                                </td>
                                <td width="35%" >                                    
                                	<span id="orgBrId" ></span><span id="orgBrName" ></span>
								</td>								 	
							</tr>
							<tr class='align_top'>     
                                <td class="hd1">
                                    <th:block th:text="#{'C122M01A.custId'}">身分證統編 </th:block>&nbsp;
                                </td>
                                <td>
                                    <span id="custId" name="custId" ></span>&nbsp;&nbsp;<span id="dupNo" name="dupNo" ></span>
                                </td>
                                <td class="hd1">
                                    <span id="custNameLabel" name="custNameLabel" ></span>&nbsp;
                                </td>
                                <td>
                                    <span id="custName" name="custName" ></span>
									<div class='ploan_relateWithMainBorrower'><!--與主借人關係-->
									 	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
										<tr>
									 		<td class='hd2'><span class="color-red">＊</span>
                                    						<th:block th:text="#{'label.ploanRelateCase.custPos'}">身分別</th:block>
									 		</td>
									 		<td class=''><select id="ploanCasePos" name="ploanCasePos" class="" codeType="ploan_casePos" ></select>&nbsp;
									 		</td>
									 	</tr>
									 	<tr>
									 		<td class='hd2'><th:block th:text="#{'ploanObj.relationWithBorrower'}">關係類別</th:block>
									 		</td>
									 		<td class=''><select id="ploan_basicInfo_relationWithBorrower" name="ploan_basicInfo_relationWithBorrower" class="" codeType="Relation_type2" ></select>&nbsp;
									 		</td>
									 	</tr>	
									 	<tr>
									 		<td class='hd2' nowrap><th:block th:text="#{'ploanObj.liveWithBorrower'}">與主借人同住</th:block>
									 		</td>
									 		<td class=''><select id="ploan_basicInfo_liveWithBorrower" name="ploan_basicInfo_liveWithBorrower" class="" codeType="ploan_liveWithBorrower" ></select>&nbsp;
									 		</td>
									 	</tr>
									 	<tr class='align_top'>    
									 		<td class='hd2'><th:block th:text="#{'ploanObj.guarantyReason'}">借保原因 </th:block>
									 		</td>
									 		<td class=''><select id="ploan_basicInfo_guarantyReason" name="ploan_basicInfo_guarantyReason" class="" codeType="cls1161m01_reson" ></select>&nbsp;
												<div id='ploan_basicInfo_otherGuarantyReason'>
												</div>
									 		</td>
									 	</tr>
										</table>
									</div>
                                </td>
                            </tr>
							
							
							<!-- 中鋼特有欄位 Start -->
						<th:block th:if="${show_C_visible}">
							<tr>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'C122M01A.stkhQueryEJTs'}">查利害關係人時間</th:block>
                                </td>
                                <td>
                                    <span id="stkhQueryEJTs" name="stkhQueryEJTs"></span>
                                </td>	
								<td class="hd2 rt" >                             
                                    <th:block th:text="#{'C122M01A.stkhBank33'}">銀行法利害關係人</th:block>
                                </td>
                                <td >
                                    <span id="stkhBank33" name="stkhBank33" ></span>&nbsp;
                                </td>							
                            </tr>	
							<tr>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'C122M01A.stkhFh44'}">金控法第44條利害關係人</th:block>
                                </td>
                                <td>
                                    <span id="stkhFh44" name="stkhFh44"></span>
                                </td>	
								<td class="hd2 rt" >                             
                                    <th:block th:text="#{'C122M01A.stkhFh45'}">金控法第45條利害關係人</th:block>
                                </td>
                                <td >
                                    <span id="stkhFh45" name="stkhFh45" ></span>&nbsp;
                                </td>							
                            </tr>	
							
							<tr>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'C122M01A.stkhRelFg'}">實質關係人(授信以外交易)</th:block>
                                </td>
                                <td>
                                    <span id="stkhRelFg" name="stkhRelFg"></span>
                                </td>	
								<td class="hd2 rt" >                             
                                    <th:block th:text="#{'C122M01A.stkhCoFg'}">公司法與本行董事具有控制從屬關係公司</th:block>
                                </td>
                                <td >
                                    <span id="stkhCoFg" name="stkhCoFg" ></span>&nbsp;
                                </td>							
                            </tr>	
							
							<tr>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'C122M01A.needW8BEN'}">是否具有美國納稅義務人身分</th:block>
                                </td>
                                <td>
                                    <span id="needW8BEN" name="needW8BEN"></span>
                                </td>	
								<td class="hd2 rt" >                             
                                    <th:block th:text="#{'C122M01A.needCRS'}">是否具有中華民國以外之納稅義務人身分</th:block>
                                </td>
                                <td >
                                    <span id="needCRS" name="needCRS" ></span>&nbsp;
                                </td>							
                            </tr>	
							
							<tr>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'C122M01A.rateAdjNotify'}">利率變動通知方式</th:block>
                                </td>
                                <td>
                                    <span id="rateAdjNotify" name="rateAdjNotify"></span>
                                </td>	
												
                            </tr>	
						</th:block>
							<!-- 中鋼特有欄位 End -->			
                            <tr>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'C122M01A.applyDateTime'}">進件日期
                                    </th:block>
                                </td>
                                <td>
                                    <span id="applyTS" name="applyTS"></span>
                                </td>	
								<td class="hd2 rt" nowrap>                             
                                    <th:block th:text="#{'ploanObj.ipAddr'}">來源IP
                                    </th:block>
                                </td>
                                <td >
                                    <span id="applyIPAddr" name="applyIPAddr" ></span>&nbsp;
									<button type="button" id="sameIpImportData">
			                            <span class="text-only"><th:block th:text="#{'button.sameIpImportData'}"><!--調閱30日內同IP進件資料--></th:block></span>
			                        </button>
                                </td>							
                            </tr>				
                            <tr>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'C122M01A.agreeQueryEJTs'}">同意聯徵時間
                                    </th:block>
                                </td>
                                <td>
                                    <span id="agreeQueryEJTs" name="agreeQueryEJTs"></span>
                                </td>	
								<td class="hd2 rt" nowrap>
                                    <th:block th:text="#{'C122M01A.agreeQueryEJVer'}">同意事項版本</th:block>
                                </td>
                                <td >
                                    <span id="agreeQueryEJVer" name="agreeQueryEJVer" ></span>
                                </td>							
                            </tr>		
                            <tr style='vertical-align:top;'>                             
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'ploanObj.avgTransactionAmt'}">預期月平均交易金額
                                    </th:block>
                                </td>
                                <td>
                                    <select id="ploan_basicInfo_avgTransactionAmt" name="ploan_basicInfo_avgTransactionAmt" class="" codeType="ploan_avgTransactionAmt" ></select>&nbsp;
                                </td>	
								<td class="hd2 rt" nowrap>                                 
                                    <th:block th:text="#{'ploanObj.serviceAssociateData'}">服務專員
                                    </th:block>
                                </td>
                                <td>
                                    <span id="ploan_basicInfo_serviceAssociateDeptCode" name="ploan_basicInfo_serviceAssociateDeptCode" ></span>&nbsp; <br/>
									<span id="ploan_basicInfo_serviceAssociateCode" name="ploan_basicInfo_serviceAssociateCode" ></span>&nbsp;
                                </td>							
                            </tr>		
							<tr style='vertical-align:top;'  class='ploan_decide_by_main_borrowser'>  		
                                <td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.applyAmt'}">申請金額</th:block>
                                </td>
                                <td>
                                    <span id="applyCurr" name="applyCurr" class='color-red'></span>&nbsp;&nbsp;
									<span id="applyAmt" name="applyAmt" class='rt color-red'></span><span class='color-red'>萬</span>
                                </td>                            
                                <td class="hd2 rt" rowspan='4'>                                   
                                    <th:block th:text="#{'label.ploanRelateCase'}">從債務人
                                    </th:block>
									<br/>
									<div id="relManageButtons" class="hide">
										<button type="button" id="addPloanRelateCase">
				                            <span class="text-only"><th:block th:text="#{'button.add'}">新增</th:block></span>
				                        </button>
										<br/>
										<button type="button" id="editPloanRelateCase">
				                            <span class="text-only"><th:block th:text="#{'button.edit'}">修改</th:block></span>
				                        </button>
										<br/>
										<button type="button" id="deletePloanRelateCase">
				                            <span class="text-only"><th:block th:text="#{'button.delete2'}">刪除</th:block></span>
				                        </button>
									</div>
                                </td>
                               <td rowspan='4'>
                                    <div id='ploanGridRelateCase' style='margin-left:0px;'>
									</div>
                                </td>
							</tr>	
							<tr class='ploan_decide_by_main_borrowser'>  									
                                <td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.maturity'}">借款年限</th:block>
                                </td>
                                <td >
                                    <span id="maturity" name="maturity"  class='color-red'></span><span class='color-red'><th:block th:text="#{'label.year'}">年</th:block></span>
                                	<span id="maturityM" name="maturityM"  class='color-red'></span><span class='color-red'><th:block th:text="#{'label.month'}">月</th:block></span>
								</td>																
							</tr>	
							<tr class='ploan_decide_by_main_borrowser'>  							
                                <td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.extYear'}">寬限期</th:block>
                                </td>
                                <td>
									無
                                </td>
							</tr> 
                            <tr class='ploan_decide_by_main_borrowser'>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'ploanObj.notificationMethod'}">利息收據通知方式
                                    </th:block>
                                </td>
                                <td>
                                    <select id="ploan_loanInfo_notificationMethod" name="ploan_loanInfo_notificationMethod" class="" codeType="ploan_notificationMethod" ></select>&nbsp;
                                </td>								
                            </tr>    	
							<tr class='align_top ploan_decide_by_main_borrowser'>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'label.ploanObj.relate'}">同一關係人資料</th:block>
                                </td>
                                <td colspan='3' >
                                    <div id='ploanGridRelationData' style='margin-left:7px;'>
			   						</div>	
                                    <div id='ploanGridServedData' style='margin:12px 0 0 7px ;'>
			   						</div>	
                                </td>							
                            </tr>	
                            <tr class='align_top ploan_decide_by_main_borrowser'>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'label.loanBrNo'}">目前貸款往來分行</th:block>
                                </td>
                                <td>
                                    <span id='loanBrNo' ></span>&nbsp;	
                                </td>							                             
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'label.payrollTransfersBrNo'}">目前薪轉往來分行</th:block>
                                </td>
                                <td>
                                    <span id='payrollTransfersBrNo' ></span>&nbsp;
                                </td>							
                            </tr>
							<tr class='align_top ploan_decide_by_main_borrowser'>    
								<td class='hd2' nowrap><th:block th:text="#{'C122M01A.statFlag'}">申貸案件狀態</th:block></td>
								<td colspan='5'>
										<table class='tb2'>
											<tr class='align_top '>
											<td class='noborder' >
												<label style="letter-spacing:0px;cursor:pointer;">
			                                		<input type="radio" id="statFlag" name="statFlag" value='0' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindP.0'}">受理中</th:block>
												</label>
												&nbsp;&nbsp;
												<label style="letter-spacing:0px;cursor:pointer;">
													<input type="radio" id="statFlag" name="statFlag" value='1' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindP.1'}">審核中</th:block>
												</label>
												&nbsp;&nbsp;
                                                <label style="letter-spacing:0px;cursor:pointer;">
                                                    <input type="radio" id="statFlag" name="statFlag" value='5' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindP.5'}">待補件</th:block>
                                                </label>
                                                &nbsp;&nbsp;
												<label style="letter-spacing:0px;cursor:pointer;">
													<input type="radio" id="statFlag" name="statFlag" value='2' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindP.2'}">已核准</th:block>
												</label>
												&nbsp;&nbsp;
											</td>
											<td class='noborder'>
												<label style="letter-spacing:0px;cursor:pointer;">
													<input type="radio" id="statFlag" name="statFlag" value='A' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindP.A'}">不承做-票債信不良</th:block>
												</label>
												<br/>
												<label style="letter-spacing:0px;cursor:pointer;">
													<input type="radio" id="statFlag" name="statFlag" value='E' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindP.E'}">不承做-申請信用評等未達標準</th:block>
												</label>
												<br/>
												<label style="letter-spacing:0px;cursor:pointer;">
													<input type="radio" id="statFlag" name="statFlag" value='D' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindP.D'}">不承做-客戶撤件</th:block>
												</label>
											</td>
											<td class='noborder' >
												<label style="letter-spacing:0px;cursor:pointer;">
			                                		<input type="radio" id="statFlag" name="statFlag" value='X' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindP.X'}">已作廢</th:block>
												</label>
											</td>
                                            <td class='noborder' >
                                                <label style="letter-spacing:0px;cursor:pointer;">
                                                    <input type="radio" id="statFlag" name="statFlag" value='6' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindP.6'}">動審表已覆核</th:block>
                                                </label>
                                            </td>
											</tr>
										</table>	
								</td>
							</tr>
							<tr class='align_top ploan_decide_by_main_borrowser'>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'label.notifyMemo'}">備註</th:block>
                                </td>
                                <td colspan='3' >
                                    <textarea name="notifyMemo" id="notifyMemo" maxlengthC='90' class="txt_mult" style="width:760px;height:60px;"></textarea>
                                </td>							
                            </tr>
							<tr class='align_top ploan_decide_by_main_borrowser'>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'label.gridCntrInfo'}">額度資料</th:block>
                                </td>
                                <td colspan='3' >
                                    <div id='gridCntrInfo'>
                                    </div>
                                </td>							
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
				<input type="hidden" class="hidden" id="queryReasonIsRecorded" name="queryReasonIsRecorded"/>
				
				<!--=======================-->
            
                <fieldset>
                    <legend>
                        <th:block th:text="#{'doc.docUpdateLog'}">文件異動紀錄
                        </th:block>
                    </legend>
                    <div class="funcContainer">
                        <div th:include="common/panels/DocLogPanel :: DocLogPanel"></div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.creator'}">文件建立者
                                    </th:block>
                                </td>
                                <td width="30%">
                                    <span id="creator"></span>&nbsp;(<span id="createTime"></span>)
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.lastUpdater'}">最後異動者
                                    </th:block>
                                </td>
                                <td>
                                    <span id="updater"></span>&nbsp;(<span id="updateTime"></span>)
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
				
				<!--=======================-->
            
                <fieldset class='ploan_decide_by_main_borrowser'>
                    <legend>
                        <th:block th:text="#{'label.changeBrNo'}">改分派異動記錄
                        </th:block>
                    </legend>
                    <div>
                        <div id='gridC122M01C' style='margin-left:0px;'>
						</div>
                    </div>
                </fieldset>
				
			<!-- 結案、產生個金徵信資料 引入CLS1220S06Panel01.html-->
			<div th:include="cls/panels/CLS1220S06Panel01 :: CLS1220S06Panel01" class="clear" style="display:none;" id="iPanelS"></div>
			
			<!-- 線下從債務人維護 -->
			<div id="ploanRelateCaseDiv" class="content" style="display:none">
				<form id='ploanRelateCaseDialogForm'>
					<span class="color-red" id="mTypeC">＊</span>
					<br/>
					<input type="text" class="hide" id="rel_mainId" name="rel_mainId"/>
					<input type="text" class="hide" id="rel_remainId" name="rel_remainId"/>
					<input type="text" class="hide" id="rel_flowId" name="rel_flowId"/>
					<table id="relDataTable" width="100%" class="tb2">
						<tr>
							<td class="hd2">
		                        <th:block th:text="#{'C122M01A.custId'}">身分證統編 </th:block>&nbsp;
		                    </td>
		                    <td>
								<input type="text" id="rel_custId" name="rel_custId" size="13" maxlength="10" class="required alphanum" readonly="readonly"/>
								<th:block th:text="#{'C122M01A.dupNo'}">重覆序號：</th:block>
		                        <input type="text" id="rel_dupNo" name="rel_dupNo" size="2" maxlength="1" class="alphanum" readonly="readonly"/>
								<br/>
								<button type="button" id="getCustName" >
		                			<span class="text-only"><th:block th:text="#{'button.importCust'}">引進客戶</th:block></span>
		            			</button>
		                    </td>
						</tr>
						<tr>
							<td class="hd2">
		                        <th:block th:text="#{'ploanObj.relationData.relationName'}">姓名 </th:block>&nbsp;
		                    </td>
		                    <td>
		                    	<u style="cursor:pointer;">
									<span id="rel_custName" name="rel_custName" class="field required"></span>
								</u>
		                    </td>
						</tr>
						<tr>
							<td class='hd2'>
								<span class="color-red">＊</span>
								<th:block th:text="#{'label.ploanRelateCase.custPos'}">身分別</th:block>
					 		</td>
		                    <td>
		                        <select id="rel_ploanCasePosCode" name="rel_ploanCasePosCode" class="required" codeType="ploan_casePos" ></select>
		                    </td>
						</tr>
					</table>
					<span class="color-red">＊從債務人存在文件，刪除從債務人檔案會同時刪除</span>
					<div id='relDocFileGrid' style='margin-left:0px;'>
					</div>
				</form>
			</div>
            <div id="createCntrNo_brmp_creditCheckDiv" style="display:none">
                <form id="createCntrNo_brmp_creditCheckForm">
                    <table class="tb2"  style="width:800px;">
                        <tr>
                            <td class="hd2 rt" style="width:200px;"><!-- hd1的 width 15% -->
                                借款人
                            </td>
                            <td>
                                <input type="text" id="createCntrNo_brmp_creditCheck_custId" name="createCntrNo_brmp_creditCheck_custId"  class="required" size='12' maxlength='10' readonly="readonly">
                                -
                                <input type="text" id="createCntrNo_brmp_creditCheck_dupNo" name="createCntrNo_brmp_creditCheck_dupNo"  class="required" size='1' maxlength='1' readonly="readonly">
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt">
                                現請額度
                            </td>
                            <td>
                                TWD
                                <input type="text" id="createCntrNo_brmp_creditCheck_amt" name="createCntrNo_brmp_creditCheck_amt" size="4" maxlength="4" integer="4" class="numeric required" />
                                萬
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt">
                                清償期限
                            </td>
                            <td>
                                <input type="text" id="createCntrNo_brmp_creditCheck_lnYear" name="createCntrNo_brmp_creditCheck_lnYear" size="2" maxlength="2" integer="2" class="numeric required" />年
                                <input type="text" id="createCntrNo_brmp_creditCheck_lnMonth" name="createCntrNo_brmp_creditCheck_lnMonth" size="2" maxlength="2" integer="2" class="numeric required" />月
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt">
                                利率分段數
                            </td>
                            <td>
                                <label style="white-space:nowrap">
                                    <input type="radio" name="createCntrNo_brmp_creditCheck_stageCount" id="createCntrNo_brmp_creditCheck_stageCount" value="1" class="required" checked="true" />
                                    一段
                                </label>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <label style="white-space:nowrap">
                                    <input type="radio" name="createCntrNo_brmp_creditCheck_stageCount" id="createCntrNo_brmp_creditCheck_stageCount" value="2" class="required"/>
                                    二段
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt">
                                綁約期
                            </td>
                            <td>
                                <!--<label style="white-space:nowrap">-->
                                <!--<input type="radio" name="createCntrNo_brmp_creditCheck_pConBegEnd_fg" id="createCntrNo_brmp_creditCheck_pConBegEnd_fg" value="Y" class="required" />是-->
                                <!--</label>-->
                                <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-->
                                <!--<label style="white-space:nowrap">-->
                                <!--<input type="radio" name="createCntrNo_brmp_creditCheck_pConBegEnd_fg" id="createCntrNo_brmp_creditCheck_pConBegEnd_fg" value="N" class="required" />否-->
                                <!--</label>									-->
                                <select id="createCntrNo_brmp_creditCheck_pConBegEnd_fg" name="createCntrNo_brmp_creditCheck_pConBegEnd_fg" space="true" combokey="pConBegEnd_fg" comboType="2"  class="required" ></select>
                                期
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt">
                                信貸客群 / 行銷代碼
                            </td>
                            <td>
                                <div style="font-weight:bold;font-size: 13px;"><span id="span_brmp_creditCheck_termGroupRuleResultText">--請選擇--</span></div>
                                <select id="createCntrNo_brmp_creditCheck_termGroup" name="createCntrNo_brmp_creditCheck_termGroup" space="true" combokey="L140S02A_termGroup_brmp" comboType="2" class="required" style="display:none;" ></select>
                                <span class="text-red">此處客群依個金徵信所選資料代入，如需調整客群請至個金徵信修改。</span>
                                <input type="hidden" id="createCntrNo_brmp_creditCheck_termGroupSub" name="createCntrNo_brmp_creditCheck_termGroupSub" value="" />
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt" style='vertical-align:top;'>
                                借款用途
                            </td>
                            <td>
                                <table border='0' class='tb2'>
                                    <tr>
                                        <td class='noborder' style='padding:0px;'>
                                            <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="N" class="required" ><th:block th:text="#{'l120m01i.creditLoanPurpose.N'}">個人週轉金</th:block>
                                            </label>
                                        </td>
                                        <td class='noborder' style='padding:0px;' colspan='3'>
                                            <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="O" class="required" ><th:block th:text="#{'l120m01i.creditLoanPurpose.O'}">個人消費性用途</th:block>
                                            </label>
                                        </td>
                                        <td class='noborder' style='padding:0px;'>
                                            <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="G" class="required" ><th:block th:text="#{'l120m01i.creditLoanPurpose.G'}">投資理財</th:block>
                                            </label>
                                        </td>
                                        <td class='noborder' style='padding:0px;' colspan='3'>
                                            <label><input type="radio" id="createCntrNo_brmp_creditCheck_creditLoanPurpose"  name="createCntrNo_brmp_creditCheck_creditLoanPurpose"  value="P" class="required" ><th:block th:text="#{'l120m01i.creditLoanPurpose.P'}">企業員工認購股票</th:block>
                                            </label>
                                        </td>
                                    </tr>
                                </table>
                                <div id="div_createCntrNo_brmp_creditCheck_induceFlagOrNot">
                                    <label style='padding:0px;'><input type='checkbox' id="createCntrNo_brmp_creditCheck_induceFlagOrNot" name="createCntrNo_brmp_creditCheck_induceFlagOrNot" value="T" class="">是否購買本行理財產品
                                    </label>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt" style='vertical-align:top;'>
                                房貸戶身分
                            </td>
                            <td>
                                <table border='0' class='tb2'>
                                    <tr>
                                        <td class='noborder' style='padding:0px;'>
                                            <label><input type="radio" id="createCntrNo_brmp_creditCheck_houseLoanFlag"  name="createCntrNo_brmp_creditCheck_houseLoanFlag"  value="0" class="required" checked="true"><th:block th:text="#{'l120m01i.houseLoanFlag.0'}">無</th:block>
                                            </label>
                                        </td>
                                        <td class='noborder' style='padding:0px;'>
                                            <label><input type="radio" id="createCntrNo_brmp_creditCheck_houseLoanFlag"  name="createCntrNo_brmp_creditCheck_houseLoanFlag"  value="1" class="required" ><th:block th:text="#{'l120m01i.houseLoanFlag.1'}">本行房貸戶</th:block>
                                            </label>
                                        </td>
                                        <td class='noborder' style='padding:0px;'>
                                            <label><input type="radio" id="createCntrNo_brmp_creditCheck_houseLoanFlag"  name="createCntrNo_brmp_creditCheck_houseLoanFlag"  value="2" class="required" ><th:block th:text="#{'l120m01i.houseLoanFlag.2'}">他行房貸戶</th:block>
                                            </label>
                                        </td>
                                        <td class='noborder' style='padding:0px;width:60%'>
                                            <table style='margin-left:5px;' border='0'>
                                                <tr style='vertical-align:top;'>
                                                    <td class='text-red' style='border: 0px;padding:0px;' rowspan='2'>＊</td>
                                                    <td class='text-red' style='border: 0px;padding:0px;' >房貸戶身分影響負債比上限，惟須符合本人及配偶持有且市值大於授信餘額。</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr id='tr_createCntrNo_brmp_creditCheck_induceFlagXV' style=''>
                            <td class="hd2 rt">
                                本案購買本行理財商品無徵提<br/>聲明書或有勸誘事宜
                            </td>
                            <td>
                                <label style="white-space:nowrap">
                                    <input type="radio" name="createCntrNo_brmp_creditCheck_induceFlagXV" id="createCntrNo_brmp_creditCheck_induceFlagXV" value="X" class="required" />
                                    是
                                </label>
                                &nbsp;&nbsp;
                                <label style="white-space:nowrap">
                                    <input type="radio" name="createCntrNo_brmp_creditCheck_induceFlagXV" id="createCntrNo_brmp_creditCheck_induceFlagXV" value="V" class="required" />
                                    否
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt">
                                線上進件案件編號
                            </td>
                            <td>
                                <input type="text" id="createCntrNo_brmp_creditCheck_onlineCaseNo" name="createCntrNo_brmp_creditCheck_onlineCaseNo" class="required" size='18' maxlength='30' readonly="readonly">
                                <!--<u id='createCntrNo_brmp_imp_onlineCaseNo' style='margin-left:16px; CURSOR: pointer'>引進</u>-->
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt">
                                引介來源
                            </td>
                            <td>
                                <select name="createCntrNo_brmp_creditCheck_introduceSrc" id="createCntrNo_brmp_creditCheck_introduceSrc" space="true" combokey="L140M01A_introductionSource" comboType="4" class="" ></select>
                                <div id="employeeDiv" style="display:none;">
                                    <table>
                                        <tr>
                                            <td class="hd1">
                                                <th:block th:text="#{'L140M01a.megaEmpno'}">引介行員代號</th:block>
                                            </td>
                                            <td width="40%">
                                                <input type="text" id="megaEmpNo" name="megaEmpNo" maxlength="6" size="6" class='numText required'
                                                       onblur="if($.trim($(this).val()).length > 0){var var_megaEmpNo = '000000'+$(this).val();$(this).val(var_megaEmpNo.substr(var_megaEmpNo.length-6, 6));}" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="hd1">
                                                <th:block th:text="#{'L140M01a.megaEmpBrNo'}">引介分行</th:block>
                                                <br/>
                                                <span id='selectBranchLink' style='color:#5291EF; text-decoration:underline;'>
														<th:block th:text="#{'L140M01a.selectBranch'}">選擇分行</th:block>
													</span>
                                            </td>
                                            <td width="40%">
                                                <input type="text" id="megaEmpBrNo" name="megaEmpBrNo" size="1" readonly class='required'/>
                                                <input id="megaEmpBrNoName" name="megaEmpBrNoName" readonly="readonly"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div id="megaSubCompanyDiv" style="display:none;">
                                    <table>
                                        <tr class="docCode5Hide" >
                                            <td class="hd1" nowrap><th:block th:text="#{'L140M01a.megaCode'}">引介子公司代號</th:block>
                                            </td>
                                            <td><select id="megaCode" name="megaCode" itemType="L140S02A_megaCode" class="required"></select>
                                            </td>
                                        </tr>
                                        <tr class="docCode5Hide" >
                                            <td class="hd1"><th:block th:text="#{'L140M01a.subUnitNo'}">引介子公司分支代號</th:block>
                                            </td>
                                            <td>
                                                <select id="subUnitNo" name="subUnitNo" class="required"></select> <!-- J-107-0136 -->
                                            </td>
                                        </tr>
                                        <tr class="docCode5Hide" >
                                            <td class="hd1"  ><th:block th:text="#{'L140M01a.subEmpNo'}">引介子公司員工編號</th:block>
                                            </td>
                                            <td><input type="text" id="subEmpNo" name="subEmpNo" maxlength="6" size="6" class='required'
                                                       onblur="if($.trim($(this).val()).length > 0 && (new RegExp('[a-zA-Z]').test($(this).val())==false)){var var_subEmpNo = '000000'+$(this).val();$(this).val(var_subEmpNo.substr(var_subEmpNo.length-6, 6));}" />
                                            </td>
                                        </tr>
                                        <tr class="docCode5Hide" >
                                            <td class="hd1"  ><th:block th:text="#{'L140M01a.subEmpNm'}">引介子公司員工姓名</th:block>
                                            </td>
                                            <td><input type="text" id="subEmpNm" name="subEmpNm"  maxlength="10" maxlengthC="10" size="18" class='required'/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div id="customerOrCompanyDiv" style="display:none;">
                                    <button type="button" id="importCustOrComButton"><th:block th:text="#{'button.importCustomerOrCompanyInfo'}">引進客戶/企業資訊</th:block></button>
                                    <table>
                                        <tr>
                                            <td class="hd1" nowrap><th:block th:text="#{'L140M01A.introCustId'}">客戶ID/企業統編</th:block></td>
                                            <td>
                                                <input type="text" id="introCustId" name="introCustId" readonly size="8" class='required'/>
                                                <input type="text" id="introDupNo" name="introDupNo" readonly size="2"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="hd1" ><th:block th:text="#{'L140M01A.introCustName'}">客戶名稱/企業名稱</th:block></td>
                                            <td><input type="text" id="introCustName" name="introCustName" class='required' readonly /></td>
                                        </tr>
                                    </table>
                                </div>
                                <div id="introducerNameDiv" style="display:none;margin-top:5px;" >
                                    <table>
                                        <tr class="docCode5Hide" >
                                            <td class="hd1"><th:block th:text="#{'L140M01A.introducerName'}">引介人姓名</th:block></td>
                                            <td width="50%">
                                                <input type="text" id="introducerName" name="introducerName" maxlength="90" maxlengthC="30" size="20"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>

                        <!--tr>
                            <td class="hd2 rt">
                                    科目
                            </td>
                            <td>
                                <label style="white-space:nowrap">
                                    <input type="radio" name="createCntrNo_brmp_creditCheck_lnap" id="createCntrNo_brmp_creditCheck_lnap" value="303" class="required" />
                                        一般中期放款(303)
                                </label>
                                <br/>
                                <label style="white-space:nowrap">
                                    <input type="radio" name="createCntrNo_brmp_creditCheck_lnap" id="createCntrNo_brmp_creditCheck_lnap" value="321" class="required" checked="true"/>
                                        一般中期消費者放款(321)
                                </label>
                            </td>
                        </tr-->
                        <tr>
                            <td class="hd2 rt">
                                海悅卡友專案
                            </td>
                            <td>
                                <label style="white-space:nowrap">
                                    <input type="radio" name="createCntrNo_brmp_creditCheck_coBrandedCardUserType01" id="createCntrNo_brmp_creditCheck_coBrandedCardUserType01" value="Y" class="required"/>
                                    是
                                </label>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <label style="white-space:nowrap">
                                    <input type="radio" name="createCntrNo_brmp_creditCheck_coBrandedCardUserType01" id="createCntrNo_brmp_creditCheck_coBrandedCardUserType01" value="N" class="required" checked="true" />
                                    否
                                </label>
                                &nbsp;&nbsp;
                                需有海悅推薦同意書
                            </td>
                        </tr>
                        <tr style='vertical-align:top;'>
                            <td class="hd2 rt">
                                代償同業金額
                            </td>
                            <td>
                                <table border='0'>
                                    <tr style='vertical-align:top;'>
                                        <td style='border: 0px;' nowrap>TWD <input type="text" id="createCntrNo_brmp_creditCheck_compensationAmt" name="createCntrNo_brmp_creditCheck_compensationAmt"  class="required numeric" size='13' maxlength='16' disabled="true">元
                                        </td>
                                        <td style='border: 0px;padding:0px;'>
                                            <table style='margin-left:18px;' border='0'>
                                                <tr style='vertical-align:top;'>
                                                    <td class='text-red' style='border: 0px;padding:0px;' rowspan='2'>＊</td>
                                                    <td class='text-red' style='border: 0px;padding:0px;' >代償同業金額為可貸額度上限之加項。</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr style='vertical-align:top;'>
                            <td class="hd2 rt">
                                其他調整金額
                            </td>
                            <td>
                                <table border='0'>
                                    <tr style='vertical-align:top;'>
                                        <td style='border: 0px;' nowrap>TWD <input type="text" id="createCntrNo_brmp_creditCheck_otherAdjustAmt" name="createCntrNo_brmp_creditCheck_otherAdjustAmt"  class="required numeric" size='13' maxlength='16'>元
                                        </td>
                                        <td style='border: 0px;padding:0px;'>
                                            <table style='margin-left:18px;' border='0'>
                                                <tr style='vertical-align:top;'>
                                                    <td class='text-red' style='border: 0px;padding:0px;' rowspan='2' >＊</td>
                                                    <td class='text-red' style='border: 0px;padding:0px;' >如了解有聯徵尚未揭露或其他負債，可於此欄位輸入，將</td>
                                                </tr>
                                                <tr>
                                                    <td class='text-red' style='border: 0px;padding:0px;' >做為可貸額度上限之減項。(如輸入負值則為加項)</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>

                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt" style='vertical-align:top;'>
                                一般保證人
                            </td>
                            <td>
                                <input type="text" id="createCntrNo_brmp_creditCheck_custPos_custId" name="createCntrNo_brmp_creditCheck_custPos_custId"  class=" " size='12' maxlength='10'>
                                -
                                <input type="text" id="createCntrNo_brmp_creditCheck_custPos_dupNo" name="createCntrNo_brmp_creditCheck_custPos_dupNo"  class=" " size='1' maxlength='1'>
                                <div>
                                    <table >
                                        <tr>
                                            <td style='border:0'>關係類別
                                            </td>
                                            <td style='border:0'>
                                                <select id="createCntrNo_brmp_creditCheck_custPos_rKindM" name="createCntrNo_brmp_creditCheck_custPos_rKindM" class=" " codeType="cls_RelClass" itemStyle="format:{value}-{key}" ></select>
                                            </td>
                                            <td style='border:0'>
                                                <select id="createCntrNo_brmp_creditCheck_custPos_rKindD1" name="createCntrNo_brmp_creditCheck_custPos_rKindD1" class="brmp_creditCheck_custPos_rKindD required" codeType="Relation_type1" itemStyle="format:{value}-{key}" ></select>
                                                <select id="createCntrNo_brmp_creditCheck_custPos_rKindD2" name="createCntrNo_brmp_creditCheck_custPos_rKindD2" class="brmp_creditCheck_custPos_rKindD required" codeType="Relation_type2" itemStyle="format:{value}-{key}" ></select>
                                                <select id="createCntrNo_brmp_creditCheck_custPos_rKindD31" name="createCntrNo_brmp_creditCheck_custPos_rKindD31" class="brmp_creditCheck_custPos_rKindD required" codeType="Relation_type31" itemStyle="format:{value}-{key}" ></select>
                                                <select id="createCntrNo_brmp_creditCheck_custPos_rKindD32" name="createCntrNo_brmp_creditCheck_custPos_rKindD32" class="brmp_creditCheck_custPos_rKindD required" codeType="Relation_type32" itemStyle="format:{value}-{key}" ></select>
                                            </td>
                                        </tr>
                                    </table>
                                    <input type="hidden" id="createCntrNo_brmp_creditCheck_custPos_rKindD" name="createCntrNo_brmp_creditCheck_custPos_rKindD" />
                                    <span class="brmp_creditCheck_custPos_isLiveWithBorrowerSpan" style="display:none;">
											&nbsp;&nbsp;&nbsp;
											是否與借款人同住
											<label><input name="createCntrNo_brmp_creditCheck_custPos_isLiveWithBorrower" type="radio" value="Y" class="required"/>是</label>
											<label><input name="createCntrNo_brmp_creditCheck_custPos_isLiveWithBorrower" type="radio" value="N" class="required"/>否</label>
										</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1"><span class="text-red">＊</span>綠色授信<br/>
                                <span id="EsgGtypeClsDescPdf_brmp" class="text-red"><u>說明</u></span>&nbsp;&nbsp;
                            </td>
                            <td>
                                <table>
                                    <tr>
                                        <td class="">
                                            <span class="text-red">＊</span>綠色授信註記
                                        </td>
                                        <td>
                                            <label style="white-space:nowrap">
                                                <input type="radio" name="createCntrNo_brmp_creditCheck_esggnLoanFg" id="createCntrNo_brmp_creditCheck_esggnLoanFg" value="Y" class="required" />是
                                            </label>
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <label style="white-space:nowrap">
                                                <input type="radio" name="createCntrNo_brmp_creditCheck_esggnLoanFg" id="createCntrNo_brmp_creditCheck_esggnLoanFg" value="N" class="required" />否
                                            </label>
                                        </td>
                                    </tr>
                                    <tr id="tr_EsgGtype_brmp" style="display:none;">
                                        <td class="">
                                            <span class="text-red">＊</span>綠色支出類型
                                        </td>
                                        <td>
                                            <select id="createCntrNo_brmp_creditCheck_esggtype" name="createCntrNo_brmp_creditCheck_esggtype" space="true" combokey="L140S02A_esggtype" comboType="4" class=" " ></select>
                                            <div id="div_createCntrNo_brmp_creditCheck_esggtypeZ" style="display:none;">
                                                <input type="text"  id="createCntrNo_brmp_creditCheck_esggtypeZMemo" name="createCntrNo_brmp_creditCheck_esggtypeZMemo" size="30">
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt">
                                使用專案
                            </td>
                            <td>
                                <select name="createCntrNo_brmp_creditCheck_usePlan" id="createCntrNo_brmp_creditCheck_usePlan" class="required readonly" disabled="true"></select>
                            </td>
                        </tr>
                    </table>
                    <!-- 引介來源 選擇分行 -->
                    <div id="selectBranchDiv" class="content" style="display:none">
                        <table width="100%" class="tb2">
                            <tr>
                                <td class="hd2" nowrap><th:block th:text="#{'branchType'}"><!--分行別--></th:block>
                                </td>
                                <td colspan="3">
                                    <select id="selectBranch" name="selectBranch" class="boss"></select>
                                </td>
                            </tr>
                        </table>
                    </div>
                </form>
            </div>
            <div id="LMS1205V01Thickbox" style="display:none">
                <form id="LMS1205V01Form">
                    <input name="docKind" type="radio" value="1" class="required"/>
                    <input name="docCode" type="radio" value="1" class="required"/>
                    <input id="authLvl" name="authLvl" type="radio" value="1" class="required group02 clearAuthLvl"/>
                    <input class="max" maxlength="1" name="ngFlag" type="radio" value="0" />
                    <input id="docStatus" name="docStatus" class="required"/>
                </form>
            </div>
        </th:block>
    </body>
</html>