/* 
 * L120M01CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120M01CDao;
import com.mega.eloan.lms.model.L120M01C;

/** 簽報書額度明細關聯檔 **/
@Repository
public class L120M01CDaoImpl extends LMSJpaDao<L120M01C, String>
	implements L120M01CDao {

	@Override
	public L120M01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	@Override
	public List<L120M01C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120M01C> list = createQuery(L120M01C.class,search).getResultList();
		return list;
	}
	@Override
	public L120M01C findByUniqueKey(String mainId,String itemType,String refMainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		search.addSearchModeParameters(SearchMode.EQUALS, "refMainId", refMainId);
	
		return findUniqueOrNone(search);
	}
	@Override
	public List<L120M01C> findByMainIdandItemType(String mainId, String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		List<L120M01C> list = createQuery(L120M01C.class,search).getResultList();
		return list;
	}
	@Override
	public L120M01C findoneByRefMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "refMainId", mainId);
		
		return findUniqueOrNone(search);
	}
}