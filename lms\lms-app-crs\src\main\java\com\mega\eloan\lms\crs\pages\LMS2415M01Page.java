/* 
 * LMS2415M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.pages;


import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.auth.CodeItemService;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.crs.panels.LMS2415S01Panel;
import com.mega.eloan.lms.crs.panels.LMS2415S02Panel;
import com.mega.eloan.lms.crs.panels.LMS2415S03Panel;
import com.mega.eloan.lms.crs.panels.LMS2415S04Panel;
import com.mega.eloan.lms.crs.panels.LMS2415S05Panel;
import com.mega.eloan.lms.crs.service.LMS2415Service;
import com.mega.eloan.lms.model.C241M01A;

/**
 * <pre>
 * [個金]覆審報告表
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/29,jessica,new
 *          </ul>
 */
@Controller
@RequestMapping("/crs/LMS2415M01Page/{page}")

public class LMS2415M01Page extends AbstractEloanForm {

	@Autowired
	CodeItemService cis;
	@Autowired
	LMS2415Service service;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS2415M01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);

		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.編製中));

		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
				AuthType.Accept, RetrialDocStatusEnum.待覆核));
		
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE2", params, getDomainClass(),
				AuthType.Accept, RetrialDocStatusEnum.已覆核未核定));
		
		addAclLabel(model, new AclLabel("_btn_COMPLETE", params, getDomainClass(),
				AuthType.Accept, RetrialDocStatusEnum.已覆核已核定));

		// 要加GRID才能載入欄位名稱
		renderJsI18N(LMS2415M01Page.class);
		renderJsI18N(LMS2405V01Page.class);
		renderJsI18N(AbstractEloanPage.class);

		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page);
		model.addAttribute("tabID", tabID);
		panel.processPanelData(model, params);
		
		boolean _btnAddToC240M01A = false;
		String mainId = params.getString(EloanConstants.MAIN_ID, "");
		C241M01A c241m01a = service.findModelByMainId(C241M01A.class,mainId);
		if (c241m01a != null && c241m01a.getC240m01b() == null) {
			_btnAddToC240M01A = true;
		}
		model.addAttribute("_btnAddToC240M01A", _btnAddToC240M01A);

	}// ;

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS2415S01Panel(TAB_CTX,true);
			renderJsI18N(LMS2415S01Panel.class);
			break;
		case 2:
			panel = new LMS2415S02Panel(TAB_CTX,true);
			renderJsI18N(LMS2415S02Panel.class);

			break;
		case 3:
			panel = new LMS2415S03Panel(TAB_CTX,true);
			renderJsI18N(LMS2415S03Panel.class);

			break;
		case 4:
			panel = new LMS2415S04Panel(TAB_CTX,true);
			renderJsI18N(LMS2415S04Panel.class);
			break;
		case 5:
			panel = new LMS2415S05Panel(TAB_CTX,true);
			renderJsI18N(LMS2415S05Panel.class);
			break;

		default:
			panel = new LMS2415S01Panel(TAB_CTX,true);
			break;
		}
		if (panel == null)
			panel = new Panel(TAB_CTX,true);
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C241M01A.class;
	}

}//
