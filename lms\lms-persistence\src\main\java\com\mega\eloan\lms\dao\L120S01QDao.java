/* 
 * L120S01QDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01Q;

/** 簽報書主檔資料檔 **/
public interface L120S01QDao extends IGenericDao<L120S01Q> {

	L120S01Q findByOid(String oid);
	
	List<L120S01Q> findByMainId(String mainId);
	
	L120S01Q findByUniqueKey(String mainId, String custId, String dupNo);

	List<L120S01Q> findByIndex01(String mainId, String custId, String dupNo);

	List<L120S01Q> findByIndex02(String custId, String dupNo);
}