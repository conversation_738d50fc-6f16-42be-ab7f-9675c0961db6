/* 
 * MisFintblService.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 財務資料檔
 * 主         鍵：	客戶統一編號＋重複序號＋起始日期＋截止日期＋合併報表別
 * </pre>
 * 
 * @since 2011/8/31
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/8/31,iristu,new
 *          <li>2011/1/31,yunglinliu,update 取得財務資料檔(G112S01A)
 *          <li>2013/06/17,EL07623,J-102-0112,add columns
 *          </ul>
 */
public interface MisFintblService {

	/**
	 * <li>00-客戶統一編號-CUSTID <li>01-重複序號-DUPNO <li>02-起始日期-BEGDT <li>
	 * 03-截止日期-ENDDT <li>04-合併報表別-FINFLAG <li>05-編製分行-BRNO <li>06-最後異動單位-TXDEPT
	 * <li>07-總資產-TASTAMT <li>08-流動資產-CASTAMT <li>09-速動資產-RASTAMT <li>
	 * 10-應收款項-ARAMT <li>11-存貨-INAMT <li>12-固定資產-FIXAMT <li>13-負債-TLBTAMT <li>
	 * 14-流動負債-CLBTAMT <li>15-淨值-NETAMT <li>16-資本化利息-INTAMT <li>17-短期投資-SIVSAMT
	 * <li>18-長期投資-LIVSAMT <li>19-短期借款-SLNAMT <li>20-長期銀行借款-LLNAMT <li>
	 * 21-銀行借款-BLNAMT <li>22-應付公司債-CMPAMT <li>23-股東往來-OWNERAMT <li>
	 * 24-資本額-CPTLAMT <li>25-營業收入-RCV1AMT <li>26-營業外收入-RCV2AMT <li>
	 * 27-營業毛利-GPRFAMT <li>28-營業利益-NPRFAMT <li>29-利息支出-INTCOST <li>
	 * 30-稅前淨利-PREPRF <li>31-營業活動之淨現金流入（出）-CASHFLW1 <li>
	 * 32-投資活動之淨現金流入（出）-CASHFLW2 <li>33-融資活動之淨現金流入（出）-CASHFLW3 <li>
	 * 34-流動比率-CARATIO1 <li>35-速動比率-CARATIO2 <li>36-利息保障倍數-INTMLTP <li>
	 * 37-LPAYMENT <li>38-應收款項週轉率（次）-ARCYCLE1 <li>39-應收款項週轉率（日）-ARCYCLE2 <li>
	 * 40-存貨週轉率（次）-INCYCLE1 <li>41-存貨週轉率（日-INCYCLE2 <li>42-固定資產週轉率（次）-FACYCLE1
	 * <li>43-固定資產週轉率（日-FACYCLE2 <li>44-總資產週轉率（次）-TACYCLE1 <li>
	 * 45-總資產週轉率（日）-TACYCLE2 <li>46-固定長期適合率-FLRATIO <li>47-負債比率-TLRATIO <li>
	 * 48-營收成長率-RCVRATIO <li>49-營業毛利率-GRSRATIO <li>50-營業利益率-PRFRATIO <li>
	 * 51-稅前淨利率-TAXRATIO <li>52-財務費用率-FINRATIO <li>53-應付款項週轉率-APRATIO <li>
	 * 54-前一期營業收入-PRCVAMT <li>55-前一期營業利益-PPRFAMT <li>56-現金流量比率-CHFRATIO <li>
	 * 57-資料修改人（行員代號）-UPDATER <li>58-資料修改日期-TMESTAMP <li>59-幣別-CURR <li>
	 * 60-金額單位-AMTUNIT <li>61-現金及約當現金-CASH <li>62-一年內到期之長期負債-ALBTAMT <li>
	 * 63-應付款項-APAMT <li>64-利息收入-INTRCV <li>65-稅後淨利-AFTERPRF <li>
	 * 66-財務報表資料來源-FINSOURCE <li>67-營業成本-BUSICOST <li>68-銷貨成本-SALESCOST <li>
	 * 69-應付款項週轉率(日)-APCYCLE2 <li>70-應收票據及帳款-關係人-ACRCVR <li>
	 * 71-應付票據及帳款-關係人-ACPAYR <li>72-銷貨收入淨額-NETSALE <li>73-營業費用-OPEEXP <li>
	 * 74-營業外支出-OPEOEXP <li>75-前一期財報起日-PBEGDT <li>76-前一期財報迄日-PENDDT <li>
	 * 77-前一期資產總額-PTASTAMT <li>78-前一期資本額-PCPTLAMT <li>79-前一期應收款項-PARAMT <li>
	 * 80-前一期應付款項-PAPAMT <li>81-前一期存貨-PINAMT <li>82-前一期固定資產-PFIXAMT <li>
	 * 83-前一期淨值-PNETAMT <li>84-前一期營業活動現金流量-PCASHFLW1 <li>85-行業別-TRADTYPE <li>
	 * 86-央行同業存款-CBDEPOSIT <li>87-存款及匯款-DEPOSIT <li>88-附買回票券及債券-RPBOND <li>
	 * 89-逾期放款及催收款-NPL <li>90-備抵呆帳-ALLOWANCE <li>91-呆帳費用-BADDEBT <li>
	 * 92-存放比率-DLRATIO <li>93-放款/資產-LARATIO <li>94-提存比率-ALRATIO <li>
	 * 95-呆帳覆蓋率-COVERAGE <li>96-逾放比率-NPLRATIO <li>97-資本適足率-ADEQUACY <li>
	 * 98-第一類資本適足率-TIER1 <li>99-第二類資本適足率-TIER2 <li>100-資產報酬率 ROA-ROA <li>
	 * 101-股東權益報酬率 ROE-ROE <li>102-商譽-COODWILL <li>103-其他無形資產-INTANGIBLE <li>
	 * 104-財報類型-PERIODTYPE <li>105-會計準則-PRINCIPLE 0-GAPP/1-IFRS <li>
	 * 106-無形資產-INTANGIBAST <li>107-長期負債-LONGLIAB <li>108-金融資產－非流動-NONCURAST <li>
	 * 109-投資性不動產-INVRELEST <li>110-其他應付款項－非流動-NONCURPAY <li>111-金融負債－流動-CURLIAB
	 * <li>112-保留盈餘-RETEARNING <li>113-其他綜合損益-OTHCOMINCME <li>
	 * 114-本期綜合損益總額-CURCOMINCME <li>115-折舊及各項攤銷-DEPRECAMORT
	 */
	public String[] finCols = { "CUSTID", "DUPNO", "BEGDT", "ENDDT", "FINFLAG",
			"BRNO", "TXDEPT", "TASTAMT", "CASTAMT", "RASTAMT", "ARAMT",
			"INAMT", "FIXAMT", "TLBTAMT", "CLBTAMT", "NETAMT", "INTAMT",
			"SIVSAMT", "LIVSAMT", "SLNAMT", "LLNAMT", "BLNAMT", "CMPAMT",
			"OWNERAMT", "CPTLAMT", "RCV1AMT", "RCV2AMT", "GPRFAMT", "NPRFAMT",
			"INTCOST", "PREPRF", "CASHFLW1", "CASHFLW2", "CASHFLW3",
			"CARATIO1", "CARATIO2", "INTMLTP", "LPAYMENT", "ARCYCLE1",
			"ARCYCLE2", "INCYCLE1", "INCYCLE2", "FACYCLE1", "FACYCLE2",
			"TACYCLE1", "TACYCLE2", "FLRATIO", "TLRATIO", "RCVRATIO",
			"GRSRATIO", "PRFRATIO", "TAXRATIO", "FINRATIO", "APRATIO",
			"PRCVAMT", "PPRFAMT", "CHFRATIO", "UPDATER", "TMESTAMP", "CURR",
			"AMTUNIT", "CASH", "ALBTAMT", "APAMT", "INTRCV", "AFTERPRF",
			"FINSOURCE", "BUSICOST", "SALESCOST", "APCYCLE2", "ACRCVR",
			"ACPAYR", "NETSALE", "OPEEXP", "OPEOEXP", "PBEGDT", "PENDDT",
			"PTASTAMT", "PCPTLAMT", "PARAMT", "PAPAMT", "PINAMT", "PFIXAMT",
			"PNETAMT", "PCASHFLW1", "TRADTYPE", "CBDEPOSIT", "DEPOSIT",
			"RPBOND", "NPL", "ALLOWANCE", "BADDEBT", "DLRATIO", "LARATIO",
			"ALRATIO", "COVERAGE", "NPLRATIO", "ADEQUACY", "TIER1", "TIER2",
			"ROA", "ROE", "COODWILL", "INTANGIBLE", "PERIODTYPE", "PRINCIPLE",
			"INTANGIBAST", "LONGLIAB", "NONCURAST", "INVRELEST", "NONCURPAY",
			"CURLIAB", "RETEARNING", "OTHCOMINCME", "CURCOMINCME",
			"DEPRECAMORT" };

	/**
	 * 上傳財務資料
	 * 
	 * @param inputData
	 *            其欄位應包含<code>finCols</code>所有的欄位資料
	 * @return int int
	 */
	public int addFintbl(Map<String, Object> inputData);

	/**
	 * 取得財務資料檔(G112S01A)
	 * 
	 * @param year
	 *            資料年度 yyy
	 * @param season
	 *            季
	 * @param custId
	 *            統一編號
	 * @param dupNo
	 *            重複序號
	 * @return List
	 */
	// public List<Map<String, Object>> getFinancialData(int year, int season,
	// String custId, String dupNo, String conso);

	public List<Map<String, Object>> getFinancialData(int year, int season,
	String custId, String dupNo);

	/**
	 * 這隻是對應舊notes資料的撈法。
	 * 
	 * @param year
	 *            資料年度 yyy
	 * @param season
	 *            季
	 * @param custId
	 *            統一編號
	 * @param dupNo
	 *            重複序號
	 * @param conso
	 *            合併報表別
	 * 
	 * @return List
	 */
	public List<Map<String, Object>> getFinancialData2(int year, int season,
			String custId, String dupNo, String conso);

	/**
	 * 取得查詢集團轄下公司財務資料
	 * 
	 * @param grpId
	 *            集團代號
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> findByGrpId(String grpId);

	/**
	 * IFRS取得查詢集團轄下公司財務資料
	 * 
	 * @param grpId
	 *            集團代號
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> findByGrpIdAndFingFlagBCN(String grpId);
}
