pageJsInit(function(){
    $(function(){
        // 判斷查詢為何種形式
    $("[name=queryData]").click(function(){
        $(".select").hide()
        //J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        $("#queryDataTr" + DOMPurify.sanitize($(this).val())).show();
    });
    $("#managerId").change(function(){
        if ($(this).val() == "0") {
            $("#managerNm").show();
        }
        else {
            $("#managerNm").hide();
        }
    });
    openFilterBox();
    var grid = $("#gridview").iGrid({
        handler: 'cls1021gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        // action: "queryC102m01a",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "custId",
        sortorder: "desc|desc",
        multiselect: true,
        colModel: [{
            colHeader: i18n.cls1021v03['C102M01A.custId'],// "主要借款人統編",
            name: 'custId',
            width: 80,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.cls1021v03['C102M01A.custName'],// "主要借款人",
            name: 'custName',
            width: 80,
            sortable: true
        }, {
            colHeader: i18n.cls1021v03['C102M01A.cntrNo'],// "額度序號",
            name: 'cntrNo',
            width: 150,
            sortable: true
        }, {
            colHeader: i18n.cls1021v03['C102M01A.aLoanAC'],// "放款帳號",
            name: 'aLoanAC',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.cls1021v03['C102M01A.aLoanDate'],// "首撥日期",
            name: 'aLoanDate',
            width: 70,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d'
            },
            align: "left",
            sortable: true
        }, {
            colHeader: i18n.cls1021v03['C102M01A.approveTime'],// "核准日期",
            name: 'approveTime',
            width: 70,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d'
            },
            align: "left",
            sortable: true
        }, {
            colHeader: i18n.cls1021v03['C102M01A.creator'],// "分行經辦",
            name: 'creator',
            width: 80,
            sortable: true,
            align: "center"
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    // 篩選
    function openFilterBox(){
        var $filterForm = $("#filterForm");
        // 初始化
        $filterForm.reset();
        $("[name=queryData][value=1]").prop("checked", "true");
        $("#queryDataTr1").show().siblings("#queryDataTr2").hide().siblings("#queryDataTr3");
        $("#filterBox").thickbox({
            title: i18n.cls1021m01['C102M01A.title01.V202208'],
            width: 500,
            height: 150,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$filterForm.valid()) {
                        return false;
                    }
                    if ($("[name=queryData]:checked").val() ==
                    '1') {
                        var cust = $filterForm.find("#custId").val();
                        
                        if ($.trim(cust) == "") {
                            // C102M01A.message07=借款人統編尚未輸入
                            return CommonAPI.showErrorMessage(i18n.cls1021v03["C102M01A.message07"]);
                        }
                        
                        filterGrid({
                            custId: cust.slice(0, cust.length - 1),
                            dupNo: cust.slice(cust.length - 1)
                        });
                    }
                    else 
                        if ($("[name=queryData]:checked").val() ==
                        '2') {
                            if ($.trim($("#cntrNo").val()) ==
                            "") {
                                // C102M01A.message08請輸入額度序號
                                return CommonAPI.showErrorMessage(i18n.cls1021v03["C102M01A.message08"]);
                            }
                            
                            filterGrid({
                                cntrNo: $("#cntrNo").val()
                            });
                        }
                        else 
                            if ($("[name=queryData]:checked").val() ==
                            '3') {
                                if ($.trim($("#approveTime").val()) ==
                                "") {
                                    // C102M01A.message08請輸入核准日期
                                    return CommonAPI.showErrorMessage(i18n.cls1021v03["C102M01A.message09"]);
                                }
                                
                                filterGrid({
                                    approveTime: $("#approveTime").val()
                                });
                            }
                    
                    
                    $.thickbox.close();
                },
                "cancel": function(){
                    filterGrid({
                        type: ""
                    });
                    $.thickbox.close();
                }
            }
        });
    }
    
    // grid資料篩選
    function filterGrid(sendData){
        $("#gridview").jqGrid("setGridParam", {
            postData: $.extend({
                formAction: "queryC102m01a3",
                docStatus: viewstatus,
                type: $("[name=queryData]:checked").val()
            }, sendData || {}),
            search: true
        }).trigger("reloadGrid");
    }
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',// '../lms/cls1021m01/01',
            data: {
                formAction: "queryC102m01a",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
    
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];
        
        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($("#gridview").getRowData(rows[i]).oid);
                }
                
				$.ajax({
                   handler: "cls1021m01formhandler",
                   data: {
                       formAction: "deleteC102m01a",
                       oids: data
                   },
               }).done(function(){
                   $("#gridview").trigger("reloadGrid");
               });     
            }
        });
        
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
            CommonAPI.showMessage(i18n.cls1021m01["C102M01A.error1"]);
        }
        else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
        openFilterBox();
    }).end().find("#btnLogeIN").click(function(){
        openLogeIN();
    }).end().find("#btnDataFix").click(function(){
        var id = $("#gridview").getGridParam('selarrrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        
        openCntrCaseBox(result.oid);
        
    });
    });
});

    

