/* 
 *  LMS7840M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.pages.LMS7840M01Page;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lms.service.LMS7840Service;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.model.C900M01D;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L999LOG01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 簽報案件查詢
 * </pre>
 * 
 * @since 2011/12/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/12,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7840m01formhandler")
public class LMS7840M01FormHandler extends AbstractFormHandler {

	@Resource
	LMS7840Service lms7840Service;
	@Resource
	LMSService lmsservice;

	@Resource
	MisMislnratService misMislnratService;

	@Resource
	LMS1405Service lms1405Service;

	/**
	 * 取得 申貸戶 異常通報戶紀錄 J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult openCntrDocPdf(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String tabFormMainId = Util.trim(params.getString("tabFormMainId"));

		L140M01A l140m01a = lmsservice.findModelByMainId(L140M01A.class,
				tabFormMainId);
		L120M01C l120m01c = l140m01a.getL120m01c();
		String mainId = l120m01c.getMainId();
		L120M01A l120m01a = lmsservice
				.findModelByMainId(L120M01A.class, mainId);

		String isCls = LMSUtil.isClsCase(l120m01a) ? "Y" : "N";

		result.set("itemType", l120m01c.getItemType());
		result.set("rptMainId", l120m01a.getMainId());
		result.set("typCd", l120m01a.getTypCd());
		result.set("tabFormOid", l140m01a.getOid());
		result.set("isCls", isCls);

		return result;
	}

	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryFxRateText(PageParameters params)
			throws CapException {

		List<Map<String, Object>> rateList = misMislnratService
				.findMislnratAllRateCode();

		TreeMap<String, String> itemList = new TreeMap<String, String>();

		for (Map<String, Object> rateMap : rateList) {

			itemList.put(Util.trim(MapUtils.getString(rateMap, "LR_CODE")),
					Util.trim(MapUtils.getString(rateMap, "LR_RATE_CNAME")));
		}

		CapAjaxFormResult result = new CapAjaxFormResult(itemList);

		return result;
	}// close queryItemList

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getBatchRunResult(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7840M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		L999LOG01A logDoc = lms1405Service.findLatestL999log01a(
				user.getUserId(), "2");

		if (logDoc != null) {
			result = DataParse.toResult(logDoc, DataParse.Delete, new String[] {
					EloanConstants.MAIN_ID, EloanConstants.OID });

			// 組查詢條件字串
			StringBuffer itemDscrBrff = new StringBuffer("");
			if (Util.notEquals(Util.trim(logDoc.getItemDscr()), "")) {
				JSONObject filterForm = JSONObject.fromObject(Util.trim(logDoc
						.getItemDscr()));

				Iterator keys = filterForm.keys();

				while (keys.hasNext()) {
					String strKey = (String) keys.next();
					if (Util.equals(strKey, "fxCurr")
							|| Util.equals(strKey, "fxLnSubject")
							|| Util.equals(strKey, "fxCollateral1")
							|| Util.equals(strKey, "fxRateText1")
							// J-112-0449_05097_B1001 Web
							// e-Loan企金額度明細表新增主要用途查詢條件
							|| Util.equals(strKey, "fxBldUse")
							// 以下為個金
							|| Util.equals(strKey, "fxProdKind")
							|| Util.equals(strKey, "fxLnSubjectCls")
							|| Util.equals(strKey, "fxRateTextCls")) {
						// 不印
					} else {
						String strVal = Util.trim(filterForm.optString(strKey,
								""));
						if (Util.notEquals(strVal, "")) {

							if (Util.equals(strKey, "fxCrKind")) {
								strVal = prop.getProperty("L784M01A.fxCrKind_"
										+ strVal);
							}

							itemDscrBrff.append(prop.getProperty("L784M01A."
									+ strKey)
									+ ":" + strVal);
							itemDscrBrff.append("<br>");
						}
					}

				}
			}

			result.set("itemDscr", itemDscrBrff.toString());
			result.set("logMainId", logDoc.getMainId());
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getExecMsg(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7840M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("logMainId"));
		L999LOG01A logDoc = lms1405Service.findL999log01aByMainId(mainId);

		if (logDoc != null) {
			if (Util.equals(Util.trim(logDoc.getExecMsg()), "")) {

				// L784M01A.noExecMsg=無執行訊息！
				result.set("execMsg", prop.getProperty("L784M01A.noExecMsg"));
			} else {
				result.set("execMsg", Util.trim(logDoc.getExecMsg()));
			}

		} else {
			// L784M01A.noLogDoc=無查詢紀錄檔L999LOG01A！
			result.set("execMsg", prop.getProperty("L784M01A.noLogDoc"));
		}

		return result;
	}

	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryClsSubject(PageParameters params)
			throws CapException {

		List<C900M01D> c900m01ds = lms7840Service.queryC900m01ds();

		TreeMap<String, String> itemList = new TreeMap<String, String>();

		for (C900M01D c900m01d : c900m01ds) {

			itemList.put(Util.trim(c900m01d.getSubjCode()),
					Util.trim(c900m01d.getSubjNm()));
		}

		CapAjaxFormResult result = new CapAjaxFormResult(itemList);

		return result;
	}// close queryItemList

}
