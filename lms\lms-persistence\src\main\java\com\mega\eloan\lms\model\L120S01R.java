/* 
 * L120S01R.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 高齡客戶關懷檢核表主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S01R", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S01R extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 客戶姓名 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 填表日期 **/
	@Column(name="WRITETIME", columnDefinition="TIMESTAMP")
	private Date writeTime;

	/** 版本日期 **/
	@Size(max=10)
	@Column(name="VERSIONDATE", length=10, columnDefinition="VARCHAR(10)")
	private String versionDate;

	/** 
	 * 評估結果<p/>
	 * 0可依徵審流程辦理<br/>
	 *  1應予婉拒
	 */
	@Size(max=2)
	@Column(name="RESULT", length=2, columnDefinition="VARCHAR(2)")
	private String result;

	/** 經辦 **/
	@Size(max=6)
	@Column(name="APPRID", length=6, columnDefinition="CHAR(6)")
	private String apprId;

	/** 覆核主管 **/
	@Size(max=6)
	@Column(name="RECHECKID", length=6, columnDefinition="CHAR(6)")
	private String reCheckId;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得客戶姓名 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定客戶姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得填表日期 **/
	public Date getWriteTime() {
		return this.writeTime;
	}
	/** 設定填表日期 **/
	public void setWriteTime(Date value) {
		this.writeTime = value;
	}

	/** 取得版本日期 **/
	public String getVersionDate() {
		return this.versionDate;
	}
	/** 設定版本日期 **/
	public void setVersionDate(String value) {
		this.versionDate = value;
	}

	/** 
	 * 取得評估結果<p/>
	 * 0可依徵審流程辦理<br/>
	 *  1應予婉拒
	 */
	public String getResult() {
		return this.result;
	}
	/**
	 *  設定評估結果<p/>
	 *  0可依徵審流程辦理<br/>
	 *  1應予婉拒
	 **/
	public void setResult(String value) {
		this.result = value;
	}

	/** 取得經辦 **/
	public String getApprId() {
		return this.apprId;
	}
	/** 設定經辦 **/
	public void setApprId(String value) {
		this.apprId = value;
	}

	/** 取得覆核主管 **/
	public String getReCheckId() {
		return this.reCheckId;
	}
	/** 設定覆核主管 **/
	public void setReCheckId(String value) {
		this.reCheckId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
