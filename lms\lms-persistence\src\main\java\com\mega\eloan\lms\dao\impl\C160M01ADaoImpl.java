/* 
 * C160M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C160M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C160M01A;

/** 動用審核表主檔 **/
@Repository
public class C160M01ADaoImpl extends LMSJpaDao<C160M01A, String> implements
		C160M01ADao {

	@Override
	public C160M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C160M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C160M01A> findByDocStatus(String docStatus) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		List<C160M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C160M01A> findByFitstUse(String[] docStatus, String ownBrId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "docStatus", docStatus);
		search.addSearchModeParameters(SearchMode.EQUALS, "c160a01a.authUnit",
				ownBrId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL,
				"c160m01d.waitingItem", "");
		search.addSearchModeParameters(SearchMode.IS_NULL,
				"c160m01d.finishDate", "");
		search.addSearchModeParameters(SearchMode.EQUALS, "useType", "Y");
		return createQuery(C160M01A.class, search).getResultList();
	}

	@Override
	public List<C160M01A> findBySrcMainId(String srcMainId, String[] docStatus) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "srcMainId", srcMainId);
		search.addSearchModeParameters(SearchMode.IN, "docStatus", docStatus);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		return createQuery(C160M01A.class, search).getResultList();
	}

	@Override
	public List<C160M01A> findLastBySrcMainId(String srcMainId, String[] docStatus) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "srcMainId", srcMainId);
		search.addSearchModeParameters(SearchMode.IN, "docStatus", docStatus);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addOrderBy("approveTime", true);
		search.setFirstResult(0).setMaxResults(1);
		return createQuery(C160M01A.class, search).getResultList();
	}
}