/* 
 * LMS1301M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.form;

import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.lns.report.LMS1201R01RptService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書(陳復(述)案) FormHandler
 * </pre>
 * 
 * @since 2011/8/6
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/8/6,Miller Lin,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1301formhandler")
@DomainClass(L120M01A.class)
public class LMS1301M01FormHandler extends LMSM01CFormHandler {

	@Resource
	LMS1201R01RptService lms1201r01RptService;

	@Resource
	LMS1201Service lms1201Service;

	@Autowired
	DocFileService docFileService;

	@Resource
	MisdbBASEService misdbBaseService;

	@Resource
	BranchService branchService;

	protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

	/**
	 * 異常通報呈主管覆核前，一定要產生PDF
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult genlms1201r26(PageParameters params)	throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String rptOid = "R26^^^";
		params.put("rptOid", rptOid);
		params.put("createFile", true);

		try {
			lms1201r01RptService.generateReport(params);
		} catch (Exception e) {
			logger.debug("LMS1301M01FormHandler.genlms1201r26 error  : ", e);
		}

		return result;
	}

	/**
	 * 產製異常通報表-關係戶清單
	 * 
	 * @param params
	 * @param parent
	 * @return IResult
	 * @throws CapException
	 */
	public IResult genLms1201r49(PageParameters params)	throws CapException {
		String success = "N";
		String oid = Util.trim(params.getString("oid"));
		L120M01A l120m01a = lms1201Service.findL120m01aByOid(oid);
		CapAjaxFormResult result = new CapAjaxFormResult();
		HashMap<String, JSONArray> attchFileMap = new HashMap<String, JSONArray>();

		if ("Y".equals(Util.trim(params.getString("genAction")))) {
			try {
				PageParameters pageParams = new CapMvcParameters();
				pageParams.add("rptOid", "R49" + "^^^^^");
				pageParams.add("mainId", l120m01a.getMainId());
				pageParams.add("fileDownloadName", "lms1201r49.pdf");
				pageParams.add("serviceName", "lms1201r01rptservice");
				lms1201r01RptService.generateReport(pageParams);

				success = "Y";
			} catch (Exception e) {
				LOGGER.error(StrUtils.getStackTrace(e));
			}
		}
		setAttchFile(attchFileMap, "divPdfFile", l120m01a.getMainId(), "relativePartyListPdf");
		result.set("attch", new CapAjaxFormResult(attchFileMap));
		result.set("success", success);
		return result;
	}

	/**
	 * 於畫面上渲染關係戶清單下載用tag
	 * 
	 * @param map
	 * @param ui_id html標籤id
	 * @param mainId
	 * @param fieldId
	 * @return
	 * @throws CapException
	 */
	private void setAttchFile(HashMap<String, JSONArray> map, String ui_id,
			String mainId, String fieldId) throws CapException {
		JSONArray jsonAraay = null;
		if (map.containsKey(ui_id)) {
			jsonAraay = map.get(ui_id);
		} else {
			jsonAraay = new JSONArray();
		}
		List<DocFile> docFileList = docFileService.findByIDAndName(mainId, fieldId, "");
		for (DocFile docFile : docFileList) {
			if (Util.equals(fieldId, docFile.getFieldId())) {
				JSONObject o = new JSONObject();
				o.putAll(DataParse.toJSON(docFile, true));
				o.put("uploadTime", TWNDate.toFullTW(docFile.getUploadTime()));
				jsonAraay.add(o);
			}
		}
		map.put(ui_id, jsonAraay);
	}
}