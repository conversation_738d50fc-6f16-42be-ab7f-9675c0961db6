package tw.com.jcs.flow.core;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.node.DecisionNode;
import tw.com.jcs.flow.node.FlowNode;
import tw.com.jcs.flow.node.StateNode;
import tw.com.jcs.flow.provider.FlowHandler;

/**
 * <pre>
 * <h1>流程執行實體</h1> 流程的實體，紀錄流程目前的執行狀態與資料，並實際負責流程的執行
 * </pre>
 * 
 * @since 2023年1月9日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月9日
 *          </ul>
 */
public class FlowInstanceImpl extends FlowEngineUnit implements FlowInstance {

    private static final Logger logger = LoggerFactory.getLogger(FlowInstanceImpl.class);

    /**
     * 流程定義資訊{@link tw.com.jcs.flow.core.FlowDefinitionImpl}
     */
    FlowDefinitionImpl definition;

    /**
     * 流程的ID
     */
    Object id;

    /**
     * 流程目前的執行序列
     */
    int seq;

    /**
     * 流程目前所在的狀態(節點名)
     */
    String state;

    /**
     * 
     */
    Map<String, Object> data;

    /**
     * 流程目前所屬的成員
     */
    String userId;

    /**
     * 流程目前所屬的組別
     */
    String roleId;

    /**
     * 流程目前所屬的部門
     */
    String deptId;

    /**
     * 流程目前執行序列的開始時間
     */
    Date beginTime;

    /**
     * 流程目前執行序列的結束時間
     */
    Date endTime;

    /**
     * 父流程編號(無父流程時為-1)
     */
    Object parentInstanceId;

    /**
     * 父流程建立目前流程時的執行狀態
     */
    String parentInstanceState;

    // Key為產生子流程的節點名(Fork時存Join節點名，sub-process存SUB節點名)
    Map<String, List<Object>> subInstanceList;

    public FlowInstanceImpl(FlowEngineImpl flowEngine) {
        super(flowEngine);
        data = new ConcurrentHashMap<String, Object>();
        subInstanceList = new ConcurrentHashMap<String, List<Object>>();
    }

    public FlowDefinitionImpl getDefinition() {
        return definition;
    }

    public void setDefinition(FlowDefinitionImpl definition) {
        this.definition = definition;
    }

    public Object getId() {
        return id;
    }

    public void setId(Object id) {
        this.id = id;
    }

    public int getSeq() {
        return seq;
    }

    public void setSeq(int seq) {
        this.seq = seq;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Object getParentInstanceId() {
        return parentInstanceId;
    }

    public void setParentInstanceId(Object parentInstanceId) {
        this.parentInstanceId = parentInstanceId;
    }

    public Map<String, List<Object>> getSubInstanceList() {
        return subInstanceList;
    }

    public void setSubInstanceList(Map<String, List<Object>> subInstanceList) {
        this.subInstanceList = subInstanceList;
    }

    public String getParentInstanceState() {
        return parentInstanceState;
    }

    public void setParentInstanceState(String parentInstanceState) {
        this.parentInstanceState = parentInstanceState;
    }

    /**
     * 返回指定key值所映射的value值
     */
    public Object getAttribute(String name) {
        return data.get(name);
    }

    /**
     * 移除指定key值所映射的value值
     */
    public void removeAttribute(String name) {
        data.remove(name);
    }

    /**
     * 設定指定key值所映射的value值
     */
    public void setAttribute(String name, Object value) {
        data.put(name, value);
    }

    /**
     * 取得目前節點
     * 
     * @return
     */
    FlowNode getCurrentNode() {
        return definition.getNodes().get(state);
    }

    /**
     * Handle指定的節點與路徑
     * 
     * @param nodeName
     *            節點
     * @param transition
     *            流程路徑名稱
     */
    public void handle(String nodeName, String transition) {
        FlowHandler handler = definition.getHandler();
        // 2022/08/28 若 handler 尚未與 definition 產生關聯，先透過 spring 取得 handler bean
        if (handler == null) {
            String className = definition.getHandlerClass();
            handler = engine.getObjectFactory().create(className); // 統一改用 SpringObjectFactory，會直接從 application context 取 bean，而不是此時才建立。
            definition.setHandler(handler);
        }
        if (handler != null) {
            handler.handle(this, nodeName, transition);
        }
    }

    /**
     * Handle目前節點指定的路徑
     * 
     * @param transition
     *            流程路徑名稱
     */
    public void handle(String transition) {
        handle(state, transition);
    }

    /**
     * Handle目前節點預設的路徑
     * 
     */
    public void handle() {
        Object value = getAttribute(DecisionNode.DECIDED_NEXT_NODE);
        if (value != null) {
            removeAttribute(DecisionNode.DECIDED_NEXT_NODE);
            handle(state, value.toString());
        } else {
            handle(state, getCurrentNode().getTransitions().keySet().iterator().next());
        }
    }

    /**
     * 儲存流程實體，含實體檔與序列檔，如DB不存在該實體，則建立
     */
    public void save() {
        engine.getPersistence().saveFlowInstance(this);
    }

    /**
     * 儲存流程實體目前的處理序列，如DB不存在該實體，則建立
     */
    public void saveSequence() {
        engine.getPersistence().saveSequence(this);
    }

    /**
     * 取得下一個流程並執行
     */
    public void next() {
        try {
            FlowNode currNode = getCurrentNode();
            FlowNode nextNode = definition.getNodes().get(currNode.getTransitions().values().iterator().next());
            logger.debug("[{}#{}] current node: {}, next node: {}", new Object[] { id, seq, state, nextNode.getName() });
            nextNode.next(this);
        } catch (Throwable e) {
            logger.error("[{}#{}] Occurs errors on going next node.[current state: {}]", new Object[] { id, seq, state });
            throw new FlowException(e);
        }
    }

    /**
     * 設定next_user的key值及value值
     */
    public void setNextUser(String userId) {
        setAttribute(StateNode.NEXT_USER_KEY, userId);
    }

    /**
     * 設定next_dept的key值及value值
     */
    public void setNextDept(String deptId) {
        setAttribute(StateNode.NEXT_DEPT_KEY, deptId);
    }

    /**
     * 從子流程清單中移除子流程
     * 
     * @param id
     *            子流程ID
     * @return 子流程對映的父流程節點名
     */
    public String removeSubInstance(Object id) {
        for (Entry<String, List<Object>> entry : subInstanceList.entrySet()) {
            List<Object> list = entry.getValue();
            for (Object subId : list) {
                if (id.equals(subId)) {
                    list.remove(subId);
                    return entry.getKey();
                }
            }
        }
        return null;
    }

    /**
     * 取得下一個State節點<br/>
     * 如回傳null，則代表找不到State，或下一節點無法判斷
     */
    public FlowNode getNextNode() {

        Map<String, FlowNode> nodes = this.getDefinition().getNodes();

        FlowNode node = nodes.get(this.getState());
        if (node instanceof DecisionNode) {
            // 如果是DECISION節點，則以目前的資料執行表達式，取得結果
            String value = ((DecisionNode) node).execute((FlowInstanceImpl) this);
            node = nodes.get(node.getTransitions().get(value));
        } else {
            node = nodes.get(node.getTransitions().values().iterator().next());
        }

        return node;
    }

    /**
     * 取得下一個節點名稱 如回傳null，則代表找不到，或下一個節點無法判斷
     */
    public String getNextNodeName() {
        FlowNode node = getNextNode();
        if (node != null) {
            return node.getName();
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowInstance#getNode()
     */
    @Override
    public FlowNode getNode() {
        Map<String, FlowNode> nodes = this.getDefinition().getNodes();
        FlowNode node = nodes.get(this.getState());
        return node;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowInstance#getStatus()
     */
    @Override
    public String getStatus() {
        FlowNode node = getNode();
        if (node != null) {
            return node.getStatus();
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowInstance#getNextStatus()
     */
    @Override
    public String getNextStatus() {
        FlowNode node = getNextNode();
        if (node != null) {
            return node.getStatus();
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowInstance#getLog()
     */
    @Override
    public String getLog() {
        FlowNode node = getNode();
        if (node != null) {
            return node.getLog();
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowInstance#getAttr()
     */
    @Override
    public Map<String, String> getAttr() {
        FlowNode node = getNode();
        if (node != null) {
            return node.getAttr();
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowInstance#getNextLog()
     */
    @Override
    public String getNextLog() {
        FlowNode node = getNextNode();
        if (node != null) {
            return node.getLog();
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowInstance#getNextAttr()
     */
    @Override
    public Map<String, String> getNextAttr() {
        FlowNode node = getNextNode();
        if (node != null) {
            return node.getAttr();
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowInstance#getTagName()
     */
    @Override
    public String getTagName() {
        FlowNode node = getNode();
        if (node != null) {
            return node.getTagName();
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowInstance#getNextTagName()
     */
    @Override
    public String getNextTagName() {
        FlowNode node = getNextNode();
        if (node != null) {
            return node.getTagName();
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.FlowInstance#getTransitionAttr()
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Override
    public Map<String, String> getTransitionAttr() {
        FlowNode node = getNode();
        FlowNode nextNode = getNextNode();
        if (node != null && nextNode != null) {
            Map<String, Map> map = node.getTransitionAttr();
            return map.get(nextNode.getName());
        }
        return new HashMap<String, String>();
    }

}
