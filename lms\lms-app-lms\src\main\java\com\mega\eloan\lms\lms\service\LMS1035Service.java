package com.mega.eloan.lms.lms.service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C121M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

public interface LMS1035Service extends ICapService {	
	public String checkIncompleteMsg(C121M01A meta, List<String> adjReasonCnt, LinkedHashMap<String, String> adjReasonCfmMap);
	public void delRatingDocCust(C121M01A c121m01a, C120M01A c120m01a);
	
	public void calc_C121_score(C121M01A meta) throws CapException;
	public void calc_C121_score_V2_0(C121M01A meta) throws CapException;
	public void del_noneRating_score(C121M01A meta);
	public boolean should_calc_C121_score(int page, C121M01A meta);
	public Page<Map<String, Object>> queryPrint(String mainId, ISearch search) throws CapException;	
}