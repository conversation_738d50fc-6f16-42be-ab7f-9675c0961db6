/* 
 * C102A01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C102A01A;

/** 購置房屋擔保放款風險權數檢核表授權檔 **/
public interface C102A01ADao extends IGenericDao<C102A01A> {

	C102A01A findByOid(String oid);
	
	C102A01A findByMainId(String mainId);
	
	List<C102A01A> findByDocStatus(String docStatus);
	
	C102A01A findByUniqueKey(String mainId, String ownUnit, String authType, String authUnit);

	List<C102A01A> findByIndex01(String mainId, String ownUnit, String authType, String authUnit);
}