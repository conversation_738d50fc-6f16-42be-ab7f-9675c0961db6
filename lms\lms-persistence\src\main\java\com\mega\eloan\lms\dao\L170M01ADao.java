package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L170M01A;

/** 覆審報告表主檔 **/
public interface L170M01ADao extends IGenericDao<L170M01A> {

	L170M01A findByOid(String oid);

	L170M01A findByMainId(String mainId);

	L170M01A findByUniqueKey(String mainId, String custId, String dupNo,
			String ctlType);

	L170M01A findByUniqueKey2(String custId, String dupNo, String branch,
			String ctlType);

	L170M01A findByUniqueKey3(String custId, String dupNo, String ownBrId,
			String docStatus, String ctlType);

	List<L170M01A> findByCustIdDupId(String custId, String DupNo, String ctlType);

	L170M01A findByPidCustIdDup(String pid, String custId, String dupNo,
			String ctlType);

	List<L170M01A> findByPid(String pid);

	List<L170M01A> findByPid(String pid, String ctlType);

}