/* 
 * LMS9541V01Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.form;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.map.LinkedMap;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.lms.model.L810M01A;
import com.mega.eloan.lms.rpt.service.LMS9541V01Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 優惠房貸報表 - 明細表
 * </pre>
 * 
 * @since 2012/12/06
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/06,Vector,new
 *          </ul>
 */

@Scope("request")
@Controller("lms9541v01formhandler")
public class LMS9541V01Formhandler extends AbstractFormHandler {

	@Resource
	LMS9541V01Service service;

	@Resource
	BranchService branchService;

	/**
	 * 建立 優惠貸款相關控制表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("static-access")
	public IResult add(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int type = params.getInt("type");
		L810M01A data = new L810M01A();
		data.setBrno(user.getUnitNo());
		data.setEndDate(CapDate.getCurrentTimestamp());
		data.setRptType(String.valueOf(type));
		data.setRptName(service.RPTNAMES[type]);
		data.setUseType("4");
		if (service.isRepeat(data)) {
			result.set("exist", true);
		} else {
			service.save(data);
		}

		return result;
	}

	/**
	 * 取得加總資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult getTotal(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<Map<String, Object>> data = service.findMisData(
				params.getString("rptType"), true, false,
				Util.nullToSpace(params.getString("brno")));
		if (Util.isNotEmpty(data)) {
			Map<String, Object> record = data.get(0);
			result.set("totalNum", Util.trim(record.get("TOT_CASE")));
			result.set("total", NumConverter.addComma(record.get("TOT_CASE")));
			result.set("totAppMoney",
					NumConverter.addComma(record.get("APP_MONEY")));
			result.set("totFavloan",
					NumConverter.addComma(record.get("FAV_LOAN")));
		}
		return result;
	}

	/**
	 * 取得分行清單
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public IResult getItem(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<Map<String, Object>> data = service.getDataBrno(params
				.getString("rptType"));
		Map<String, String> brnos = new LinkedMap();
		if (data != null) {
			for (int i = 0; i < data.size(); i++) {
				String brno = Util.trim(data.get(i).get("brno"));
				brnos.put(brno, branchService.getBranchName(brno));
			}
		}
		result.set("brno", new CapAjaxFormResult(brnos));
		return result;
	}

	/**
	 * 取得分行清單
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult getRptFile(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		L810M01A data = service.findModelByOid(L810M01A.class,
				params.getString("oid"));
		result.set("rptOid", data.getRptOid());
		return result;
	}
}
