package com.mega.eloan.lms.dw.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dw.service.DwdbBASEService;

@Service("dwdbBASEService")
public class DwdbBASEServiceImpl extends AbstractDWJdbc implements
		DwdbBASEService {

	private static final Logger logger = LoggerFactory.getLogger(DwdbBASEService.class);
	public static final char[] CashCardChars = { 'X' };

	public static final char[] DelayPayLoanChars = { 'A', 'B', '1', '2', '3',
			'4', '5', '6' };

	public List<Map<String, Object>> findDW_LNCNTROVS_TOT(String custId,
			String cntrNo) {
		return this.getJdbc().queryForList("DWLNCNTROVS.seltot",
				new Object[] { custId, cntrNo });
	}

	public List<Map<String, Object>> findDW_ASLNDAVGOVSJOIN_ByBrNoCustId(
			String brNo, String custId, String dupNo) {
		String key = "";
		if ("0".equals(Util.trim(dupNo))) {
			key = custId;
		} else {
			key = custId + dupNo;
		}
		return this.getJdbc().queryForList(
				// DW_ASLNDAVGOVSJOIN.selectByBrNoCustId2(用CUST_KEY)
				"DW_ASLNDAVGOVSJOIN.selectByBrNoCustId2",
				new Object[] { brNo, key, brNo, key, brNo, key });
	}

	// public List<?> findDW_ELF411OVS_ByCntr(String custId, String dupNo,
	// String branch) {
	// return this.getJdbc().queryForList("DWADM.DW_ELF411OVS.selCntr",
	// new Object[] { custId, dupNo, branch });
	// }

	public List<Map<String, Object>> findDW_DWFXRTHOVS_RATE(String brNo) {
		return this.getJdbc().queryForList("DW_FXRTHOVS.selByBrNoRate",
				new Object[] { brNo });
	}

	public List<Map<String, Object>> findDW_DWFXRTHOVS_MainCurr(String brNo) {
		return this.getJdbc().queryForList("DW_FXRTHOVS.selByBrNoMainCurr",
				new Object[] { brNo });
	}

	@Override
	public List<Map<String, Object>> findDW_MD_CUPFM_OTS_selCYC_MN(
			String custKey, String dupNo, String dateS, String dateE) {

		String likeCustKey = Util.getLeftStr(custKey + "          ", 10) + "%";

		return this.getJdbc().queryForList(
				"MD_CUPFM_OTS.selCYC_MN",
				new Object[] { likeCustKey, dateS, dateE, likeCustKey, dateS,
						dateE, likeCustKey, likeCustKey });
	}

	@Override
	public List<Map<String, Object>> findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
			String custKey, String dupNo, String dateS, String dateE) {
		// String likeCustKey = Util.getLeftStr(custKey, 10) + "%";
		String likeCustKey = Util.getLeftStr(custKey + "          ", 10) + "%";
		/*
		 * String likeCustKey = null; // 企業戶統編(第三碼為Z 或 非台灣自然人統編) if
		 * ("Z".equals(custKey.substring(2, 3)) ||
		 * !(custKey.matches("^[a-zA-Z](1|2)\\d{8}$") || custKey
		 * .matches("^[a-zA-Z]{2}\\d{8}$"))) { likeCustKey =
		 * Util.getLeftStr(custKey, 10) + "%"; }else{ likeCustKey =
		 * Util.getLeftStr(custKey+"           ", 11) + "%"; }
		 */
		return this.getJdbc().queryForList("DM_CUBCPCM.selTOTAL_ATTRIBUTE",
				new Object[] { likeCustKey, dateS, dateE });
	}

	@Override
	public List<?> findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(String custKey,
			String dupNo, String dateS, String dateE) {
		// String likeCustKey = Util.getLeftStr(custKey, 10) + "%";
		String likeCustKey = Util.getLeftStr(custKey + "          ", 10) + "%";
		/*
		 * String likeCustKey = null; // 企業戶統編(第三碼為Z 或 非台灣自然人統編) if
		 * ("Z".equals(custKey.substring(2, 3)) ||
		 * !(custKey.matches("^[a-zA-Z](1|2)\\d{8}$") || custKey
		 * .matches("^[a-zA-Z]{2}\\d{8}$"))) { likeCustKey =
		 * Util.getLeftStr(custKey, 10) + "%"; }else{ likeCustKey =
		 * Util.getLeftStr(custKey+"           ", 11) + "%"; }
		 */
		return this.getJdbc().queryForList("DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE",
				new Object[] { likeCustKey, dateS, dateE });
	}

	@Override
	public List<Map<String, Object>> findDW_ASLNDAVGOVSJOIN_ByCustIdData(
			String brNo, String custId, String dupNo) {
		String key = "";
		if ("0".equals(Util.trim(dupNo))) {
			key = custId;
		} else {
			key = custId + dupNo;
		}
		return this.getJdbc().queryForList("DW_ASLNOVEROVS.selCustIdData",
				new Object[] { brNo, key, brNo, key, brNo, key });
	}

	@Override
	public Map<String, Object> findDW_ASLNDAVGOVSJOIN_ByContractData(
			String custId, String contract) {

		return this.getJdbc().queryForMap("DW_ASLNOVEROVS.selContractData",
				new Object[] {});
	}

	@Override
	public List<Map<String, Object>> findDW_LNQUOTOV_selBLAmt(String custId,
			String brNo, String contract) {

		return this.getJdbc().queryForList("DW_LNQUOTOV.selBLAmt",
				new Object[] { custId, brNo, contract });
	}

	@Override
	public Map<String, Object> findDwaslnquotByBranchForReportType3(
			String custId411, String dupNo411, String brNo) {
		return this.getJdbc().queryForMap(
				"DW_ASLNOVEROVS.selByBrnoForReportType3",
				new Object[] { custId411, dupNo411, brNo, });
	}

	@Override
	public List<Map<String, Object>> findDWADM_MD_CUPFM_OTS_selDate() {

		return this.getJdbc().queryForList("DWADM_MD_CUPFM_OTS.selDate",
				new Object[] {});
	}

	@Override
	public List<Map<String, Object>> findDWADM_MD_CUPFM_OTS_selDate2(
			Date queryDateS, Date queryDateE) {

		return this.getJdbc().queryForList("DWADM_MD_CUPFM_OTS.selDate2",
				new Object[] { queryDateS, queryDateE });
	}

	@Override
	public List<Map<String, Object>> findDW_LNQUOTOVforNewReportType1ByBrNo(
			String brNo, String dateStartDate, String dateEndDate) {
		return this.getJdbc().queryForList(
				"DW_LNQUOTOV.selForNewReportType1ByBrNo",
				new Object[] { brNo, dateStartDate, dateEndDate });
	}

	@Override
	public List<Map<String, Object>> findDW_ELINSPDT_sel9ByBRANCHIDCHKYMTYPCDYM(
			String startTWYM, String endTWYM, String TWYM_START,
			String TWYM_END, String type) {
		StringBuffer str = new StringBuffer();
		if ("1".equals(type)) {
			// 照平均扣分排
			str.append(" order by M1.AVGT DESC,M1.BRANCHID ");
		}
		if ("2".equals(type)) {
			// 照總扣分排
			str.append(" order by M1.TITEMALL DESC,M1.BRANCHID ");
		}
		return this.getJdbc().queryForAllListByCustParam(
				"DW_ELINSPDT.sel9ByBRANCHIDCHKYMTYPCDYM",
				new Object[] { str.toString() },
				new Object[] { startTWYM, endTWYM, startTWYM, endTWYM,
						TWYM_START, TWYM_END, TWYM_START, TWYM_END });
	}

	@Override
	public List<Map<String, Object>> findDWADMSselDistinctByCust_Key(
			String custId) {
		return this.getJdbc().queryForList(
				"DWADM.DW_ASLNDAVGOVS.selDistinctByCust_Key",
				new Object[] { custId });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.dw.service.DwdbBASEService#findDW_CNTRYAREAByCountryCode
	 * (java.lang.String)
	 */
	@Override
	public List<Map<String, Object>> findDW_CNTRYAREAByCountryCode(
			String countryCode) {
		return this.getJdbc().queryForList("DWCNTRYAREA.selByCountryCode",
				new Object[] { countryCode });
	}

	@Override
	public void DW_ELINSPSC_delete(String mainId) {
		this.getJdbc().update("DWADM.DW_ELINSPSC.delete",
				new Object[] { mainId });
	}

	@Override
	public void DW_ELINSPSC_insert(String typCd, String branchId, Date chkm,
			String mainId, String apprId, String chkId, String chkUnit,
			String custId, String dupNo, String projNo, String curr,
			double loanAmt, double caseAmt, double tmGrade, String sysType) {
		StringBuilder sChkym = new StringBuilder();
		if (chkm != null) {
			String twChkym = TWNDate.toTW(chkm);
			sChkym.append(twChkym.substring(0, 3)).append(
					twChkym.substring(4, 6));
		}
		this.getJdbc().update(
				"DWADM.DW_ELINSPSC.insert",
				new Object[] { typCd, branchId, sChkym.toString(), mainId,
						apprId, chkId, chkUnit, custId, dupNo, projNo, curr,
						loanAmt, caseAmt, tmGrade, sysType });
	}

	@Override
	public void DW_ELINSPDT_delete(String mainId) {
		this.getJdbc().update("DWADM.DW_ELINSPDT.delete",
				new Object[] { mainId });
	}

	@Override
	public void DW_ELINSPDT_insert(List<Object[]> dataList) {
		this.getJdbc().batchUpdate(
				"DWADM.DW_ELINSPDT.insert",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.DECIMAL,
						Types.DECIMAL, Types.DECIMAL, Types.CHAR, Types.CHAR },
				dataList);
	}

	@Override
	public int delete(Object[] msgFmtParam, Object[] data) {
		return this.getJdbc().updateByCustParam("delete", msgFmtParam, data);
	}

	@Override
	public void insert(Object[] msgFmtParam, int[] type, List<Object[]> lst) {
		this.getJdbc().batchUpdateByCustParam("insert", msgFmtParam, type, lst);
	}

	@Override
	public void update(Object[] msgFmtParam, int[] type, List<Object[]> lst) {
		this.getJdbc().batchUpdateByCustParam("update", msgFmtParam, type, lst);
	}

	@Override
	public Date getMD_CUPFM_OTS_max_cyc_mn() {
		Map<String, Object> data = this.getJdbc().queryForMap(
				"DWADM_MD_CUPFM_OTS.getMaxCycMn", new Object[] {});
		return (Date) data.get("CYC_MN");
	}

	@Override
	public List<Map<String, Object>> findDW_LNCNTROVS_L120S05B1(
			String allCustId, String custId, String dupNo, String custName) {
		return this.getJdbc().queryForListByCustParam(
				"DWADM.DW_LNCNTROVS.selL120s05b1",
				new Object[] { "'" + allCustId + "'", "'" + custId + "'",
						"'" + dupNo + "'", "'" + custName + "'" },
				new Object[] {});
	}

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
	 */
	@Override
	public List<Map<String, Object>> findDW_LNCNTROVS_L120S05B1_ForLocal(
			String allCustId, String custId, String dupNo, String custName,
			String[] brNos) {
		List<Object> params = new ArrayList<Object>();
		String brNoParams = Util.genSqlParam(brNos);

		params.addAll(Arrays.asList(brNos));
		params.addAll(Arrays.asList(brNos));
		params.addAll(Arrays.asList(brNos));
		params.addAll(Arrays.asList(brNos));
		params.addAll(Arrays.asList(brNos));

		return this.getJdbc().queryForListByCustParam(
				"DWADM.DW_LNCNTROVS.selL120s05b1ForLocal",
				new Object[] { "'" + allCustId + "'", "'" + custId + "'",
						"'" + dupNo + "'", "'" + custName + "'", brNoParams,
						brNoParams, brNoParams, brNoParams, brNoParams },
				params.toArray(new Object[0]));
	}

	@Override
	public Map<String, Object> findDW_OTS_ROCLIST_ECUSD_ByCustId(String custId,
			String dupNo) {

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}
		return this.getJdbc().queryForMap(
				"DWADM.OTS_ROCLIST_ECUSD.selByCustId",
				new Object[] { custId, dupNo });
	}

	@Override
	public Map<String, Object> findDW_GRP_LIMIT_ByGrade(String grade) {

		// 如果grade是空的，當他是未評等0
		if (Util.isEmpty(grade)) {
			grade = "0";
		}
		return this.getJdbc().queryForMap("DWADM.findDW_GRP_LIMIT_ByGrade",
				new Object[] { grade });
	}
	
	/**
	 * 取得前一日客戶存款資料 原 MISDPF.findByCustId(MisEldpfServiceImpl.java\findByCustId)
	 */
	@Override
	public List<Map<String, Object>> findDW_IDDP_DPF_ByCustId(String custId) {

		String[][] APCDCode = new String[][] {// 與現行存款種類對應
		{ "01", "01" }, { "06", "01" }, { "09", "01" }, { "02", "02" },
				{ "07", "02" }, { "08", "02" }, { "10", "02" }, { "12", "02" },
				{ "13", "02" }, { "14", "02" }, { "60", "02" }, { "61", "02" },
				{ "62", "02" }, { "63", "02" }, { "64", "02" }, { "65", "02" },
				{ "20", "03" }, { "21", "03" }, { "22", "03" }, { "27", "03" },
				{ "05", "08" }, { "15", "08" }, { "16", "08" }, { "17", "08" },
				{ "18", "08" }, { "53", "08" }, { "57", "08" }, { "30", "05" },
				{ "38", "05" }, { "42", "05" }, { "31", "05" }, { "39", "05" },
				{ "45", "05" }, { "46", "05" }, { "47", "05" }, { "48", "05" },
				{ "11", "09" }, { "19", "09" }, { "33", "10" }, { "43", "10" },
				{ "34", "11" }, { "44", "11" }, { "35", "12" }, { "36", "13" } };

		String tCustId = Util.trim(custId);
		String tCustId8 = Util.getLeftStr(tCustId, 8);
		List<Map<String, Object>> result = getJdbc().queryForList(
				"DWADM.IDDP.findDpfByCustId",
				new String[] { tCustId, tCustId8, tCustId, tCustId8, tCustId,
						tCustId8, tCustId });

		if (!CollectionUtils.isEmpty(result)) {
			HashMap<String, String> hm = new HashMap<String, String>();
			for (String[] mapping : APCDCode) {
				hm.put(mapping[0], mapping[1]);
			}

			for (Map<String, Object> data : result) {
				if (hm.containsKey(data.get("DP_AP_CODE"))) {
					data.put("depType", hm.get(data.get("DP_AP_CODE")));
				} else {
					data.put("depType", "");
				}
			}
		}

		return result;
	}

	/**
	 * 查詢本行帳戶 By brno,custid,dupNo
	 * 原MIS.ELDPF.findByCustid(MisdbBASEServiceImpl.java\findMisEldpfByCustid)
	 */
	@Override
	public List<Map<String, Object>> findDW_IDDP_DPF_Seqno_ByCustIdBrno(
			String brno, String custId, String dupNo) {

		String tCustId = Util.trim(custId);
		String tCustId8 = Util.getLeftStr(tCustId, 8);

		return this.getJdbc().queryForList(
				"DWADM.IDDP.findDpfSeqnoByCustidBrno",
				new Object[] { custId, tCustId8, custId, tCustId8, custId,
						tCustId8, custId, dupNo, brno });
	}

	/**
	 * 查詢本行帳戶 By custid,dupNo
	 * 原MIS.ELDPF.findByCustid2(MisdbBASEServiceImpl.java\findMisEldpfByCustid)
	 */
	@Override
	public List<Map<String, Object>> findDW_IDDP_DPF_Seqno_ByCustId(
			String custId, String dupNo) {

		String tCustId = Util.trim(custId);
		String tCustId8 = Util.getLeftStr(tCustId, 8);

		return this.getJdbc().queryForList(
				"DWADM.IDDP.findDpfSeqnoByCustid",
				new Object[] { custId, tCustId8, custId, tCustId8, custId,
						tCustId8, custId, dupNo });
	}

	/**
	 * 查詢本行帳戶 By brno,apcd,seqno
	 * 原MIS.ELDPF.findByAccount(MisdbBASEServiceImpl.java\findMisEldpfByAccount)
	 */
	@Override
	public List<Map<String, Object>> findDW_IDDP_DPF_CustData_ByAccount(
			String brno, String apcd, String seqno) {
		String accNo = brno + apcd + seqno;
		return this.getJdbc().queryForList("DWADM.IDDP.findDpfCustByAccount",
				new Object[] { accNo, accNo, accNo });
	}

	/**
	 * 查詢本行帳戶 By brno,apcd,seqno,custid
	 * 原MIS.ELDPF.findByAccount2(MisdbBASEServiceImpl
	 * .java\findMisEldpfByAccount)
	 **/
	@Override
	public List<Map<String, Object>> findDW_IDDP_DPF_CustData_ByCustIdAccount(
			String[] custIds, String brno, String apcd, String seqno) {
		String accNo = brno + apcd + seqno;
		// StringBuilder sb = new StringBuilder("'");
		String custIdParams = Util.genSqlParam(custIds);
		List<Object> params = new ArrayList<Object>();
		params.add(accNo);
		params.add(accNo);
		params.add(accNo);
		params.addAll(Arrays.asList(custIds));
		// for (String custId : custIds) {
		// sb.append(sb.length() >= 1 ? "','" : "").append(custId);
		// }
		// sb.append("'");
		return this.getJdbc().queryForListByCustParam(
				"DWADM.IDDP.findDpfCustByCustIdAccount",
				new Object[] { custIdParams }, params.toArray(new Object[0]));
	}

	@Override
	public Date getOTS_BSL2CSNET_AVG_max_cyc_mn() {
		Map<String, Object> data = this.getJdbc().queryForMap(
				"DWADM_OTS_BSL2CSNET_AVG.getMaxCycMn", new Object[] {});
		return (Date) data.get("CYC_MN");
	}

	@Override
	public List<Map<String, Object>> findOTS_BSL2CSNET_AVG_TOTAL_RA_AMT(
			String custKey, String dupNo, String dateS, String dateE) {
		// String likeCustKey = Util.getLeftStr(custKey, 10) + "%";
		String likeCustKey = Util.getLeftStr(custKey + "          ", 10) + "%";
		/*
		 * String likeCustKey = null; // 企業戶統編(第三碼為Z 或 非台灣自然人統編) if
		 * ("Z".equals(custKey.substring(2, 3)) ||
		 * !(custKey.matches("^[a-zA-Z](1|2)\\d{8}$") || custKey
		 * .matches("^[a-zA-Z]{2}\\d{8}$"))) { likeCustKey =
		 * Util.getLeftStr(custKey, 10) + "%"; }else{ likeCustKey =
		 * Util.getLeftStr(custKey+"           ", 11) + "%"; }
		 */
		return this.getJdbc().queryForList("OTS_BSL2CSNET_AVG.selCYC_MN",
				new Object[] { likeCustKey, dateS, dateE });
	}

	@Override
	public List<Map<String, Object>> findDW_LNMRATEOVS_USED_RATEY(
			String cntrNo, String dateS, String dateE) {
		return this.getJdbc().queryForList(
				"DW_LNMRATEOVS.find_USED_RATEY_By_LINENO",
				new Object[] { cntrNo, dateS, dateE }, 0, 1);
	}

	@Override
	public void updateDR__DR_1YR(String mowType, int mower1, int mower2,
			int finalRating, BigDecimal dr, BigDecimal dr_1yr) {
		this.getJdbc()
				.update("DWADM.DW_RKCREDIT.updDR__DR_1YR",
						new Object[] { dr, dr_1yr, mowType, mower1, mower2,
								finalRating });
	}

	@Override
	public void delL140M01A(String BR_CD, String NOTEID, String CUSTID,
			String DUPNO, String MOWTYPE, String MOWVER1, String MOWVER2,
			String JCIC_DATE, String ACCT_KEY, String DELETE_REASON,
			String REJECT_OTHEREASON_TEXT) {
		String[] paramsArr = { "DD", BR_CD, NOTEID, CUSTID, DUPNO, MOWTYPE,
				MOWVER1, MOWVER2, JCIC_DATE, ACCT_KEY };
		String[] paramsCreditArr = { "DD", DELETE_REASON,
				REJECT_OTHEREASON_TEXT, BR_CD, NOTEID, CUSTID, DUPNO, MOWTYPE,
				MOWVER1, MOWVER2, JCIC_DATE, ACCT_KEY };

		this.getJdbc().update("DWADM.DW_RKSCORE.delL140M01A", paramsArr);
		this.getJdbc().update("DWADM.DW_RKCREDIT.delL140M01A", paramsCreditArr);
		this.getJdbc().update("DWADM.DW_RKJCIC.delL140M01A", paramsArr);
		this.getJdbc().update("DWADM.DW_RKPROJECT.delL140M01A", paramsArr);
		this.getJdbc().update("DWADM.DW_RKADJUST.delL140M01A", paramsArr);
		this.getJdbc().update("DWADM.DW_RKCNTRNO.delL140M01A", paramsArr);
		this.getJdbc().update("DWADM.DW_RKAPPLICANT.delL140M01A", paramsArr);
	}

	@Override
	public void delL140M01A_OVS(String BR_CD, String NOTEID,
			String RATING_DATE, String RATING_ID, String CUSTID, String DUPNO,
			String CUST_KEY, String LOAN_CODE, String MOWTYPE, String MOWVER1,
			String MOWVER2) {
		int[] type = { java.sql.Types.INTEGER, java.sql.Types.INTEGER };
		List<Object[]> lst = new ArrayList<Object[]>();
		lst.add(new Object[] { "DD", BR_CD, NOTEID, RATING_DATE, RATING_ID,
				CUSTID, DUPNO, CUST_KEY, LOAN_CODE, MOWTYPE, MOWVER1, MOWVER2 });

		List<String> table_list = new ArrayList<String>();
		table_list.add("DWADM.OTS_RKADJUSTOVS");
		table_list.add("DWADM.OTS_RKAPPLICANTOVS");
		table_list.add("DWADM.OTS_RKCNTRNOOVS");
		table_list.add("DWADM.OTS_RKCOLLOVS");
		table_list.add("DWADM.OTS_RKCREDITOVS");
		table_list.add("DWADM.OTS_RKJCICOVS");
		table_list.add("DWADM.OTS_RKPROJECTOVS");
		table_list.add("DWADM.OTS_RKSCOREOVS");
		for (String table : table_list) {
			update(new Object[] {
					table,
					"DOCSTATUS=? ",
					"BR_CD=? and NOTEID=? and RATING_DATE=? and RATING_ID=? and CUSTID=? and DUPNO=? and CUST_KEY=? and LOAN_CODE=? and MOWTYPE=? and MOWVER1=? and MOWVER2=?" },
					type, lst);
		}
	}

	/**
	 * 取得客戶項下DW未銷戶額務序號(企金用)
	 */
	@Override
	public List<Map<String, Object>> findDWCntrnoByCustId(String custId) {
		String likeCustKey = Util.getLeftStr(custId + "          ", 10) + "%";
		return this.getJdbc().queryForList("DWADM.LNCNTROVS.selByCustId",
				new Object[] { likeCustKey });
	}

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 取得客戶之海外額度序號資訊BY額度序號
	 */
	@Override
	public Map<String, Object> findDWCntrnoByCntrNo(String custId,
			String dupNo, String cntrNo) {
		// String newCustKey = Util.getLeftStr(custId+"          ", 10);
		String key = "";
		if ("0".equals(Util.trim(dupNo))) {
			key = custId;
		} else {
			key = custId + dupNo;
		}
		return this.getJdbc().queryForMap(
				"DDWADM.LNCNTROVS.selByCustIdAndCntrNo",
				new Object[] { key, cntrNo });
	}

	@Override
	public List<Map<String, Object>> findDW_FXRTH_LatestRate() {
		return this.getJdbc().queryForListWithMax("DW_FXRTH.selLatestRate",
				new Object[] {});
	}

	public Map<String, Object> findDBUEstAmtAndBal(String grpid,
			String[] banDupNoAry) {

		if (banDupNoAry.length <= 0) {
			return new HashMap<String, Object>();
		}

		String[] params = (String[]) ArrayUtils.addAll(new String[] { grpid },
				banDupNoAry);

		StringBuffer appendDynamicSql = getAppendDynamicSql(banDupNoAry);
		String sql = getSqlBySqlId("DW_ROCGRPBLD1_E.getDBUEstAmtAndBal");
		sql = MessageFormat.format(sql,
				new Object[] { appendDynamicSql.toString() });
		Map<String, Object> map = getJdbc().queryForMap(sql, params);
		return map == null ? new HashMap<String, Object>() : map;
	}

	public Map<String, Object> findOCEEstAmtAndBal(String grpid,
			String[] banDupNoAry) {

		if (banDupNoAry.length <= 0) {
			return new HashMap<String, Object>();
		}
		String[] params = (String[]) ArrayUtils.addAll(new String[] { grpid },
				banDupNoAry);

		StringBuffer appendDynamicSql = getAppendDynamicSql(banDupNoAry);
		String sql = getSqlBySqlId("DW_ROCGRPBLD1_E.getOCEEstAmtAndBal");
		sql = MessageFormat.format(sql,
				new Object[] { appendDynamicSql.toString() });
		Map<String, Object> map = getJdbc().queryForMap(sql, params);
		return map == null ? new HashMap<String, Object>() : map;
	}

	/**
	 * 組成多個?,?的查?字串。
	 * 
	 * @param banDupNoAry
	 *            ban code array
	 * @return StringBuffer query String
	 */
	private StringBuffer getAppendDynamicSql(String[] banDupNoAry) {
		StringBuffer appendDynamicSql = new StringBuffer();
		for (int i = 0; i < banDupNoAry.length; i++) {
			appendDynamicSql.append("?,");
		}
		if (banDupNoAry.length > 0) {
			appendDynamicSql.deleteCharAt(appendDynamicSql.length() - 1);
		}
		return appendDynamicSql;
	}

	/**
	 * J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
	 * 
	 * @param brNo
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findDW_AS400_groupMember(String brNo,
			String custId, String dupNo) {
		String key = "";
		if ("0".equals(Util.trim(dupNo))) {
			key = custId;
		} else {
			key = custId + dupNo;
		}
		return this.getJdbc().queryForList(
				// DW_ASLNDAVGOVSJOIN.selectByBrNoCustId2(用CUST_KEY)
				"DW_CSLNOVS.selectByBrNoCustId",
				new Object[] { brNo, key, brNo, key });
	}

	/**
	 * 修改ELCSECNT 統編 J-105-0202-001 Web e-Loan企金授信修改客戶統編。
	 * 
	 * @param orgCustId
	 * @param orgDupNo
	 * @param newCustId
	 * @param newDupNo
	 * @param cntrNo
	 * @param rptMainId
	 */
	@Override
	public void updateJ1050202_by_custIdAndDupNo(String orgCustId,
			String orgDupNo, String newCustId, String newDupNo,
			String documentNo, String cntrNo, String rptMainId) {

		String fullCustKeyOrg = Util.getLeftStr(orgCustId + "          ", 10)
				+ orgDupNo;
		String fullCustKeyNew = Util.getLeftStr(newCustId + "          ", 10)
				+ newDupNo;

		// 上傳MIS、AS400********************************************************************************

		// 依照簽報書
		// DW_ELINSPSC 授信考核表主檔
		this.getJdbc().update(
				"J1050202.update_DW_ELINSPSC_01",
				new Object[] { newCustId, newDupNo, orgCustId, orgDupNo,
						rptMainId });

		// DW_ELINSPDT 授信考核表子檔--無ID不需改

	}

	/**
	 * 取得客戶海外額度 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findDW_LNCNTROVS_By_CustId(String custId,
			String dupNo) {

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}

		String fullCustKeyNew = Util.getLeftStr(Util.trim(custId)
				+ "          ", 10)
				+ dupNo;
		return this.getJdbc().queryForList("DWLNCNTROVS.selByCustId",
				new Object[] { fullCustKeyNew });
	}

	/**
	 * 取得客戶海外額度 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findDW_LNCNTROVS_By_CntrNo(String cntrNo) {

		return this.getJdbc().queryForList("DWLNCNTROVS.selByCntrNo",
				new Object[] { cntrNo });
	}

	/**
	 * J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findDW_LNCNTROVS_By_CustId_And_CntrNo(
			String custId, String dupNo, String cntrNo) {

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}

		String fullCustKeyNew = Util.getLeftStr(Util.trim(custId)
				+ "          ", 10)
				+ dupNo;
		return this.getJdbc().queryForList("DWLNCNTROVS.selByCustIdAndCntrNo",
				new Object[] { fullCustKeyNew, cntrNo });
	}

	@Override
	public List<Map<String, Object>> findDW_LNCNTROVS_By_CustId_And_CntrNo_WithOriginal(
			String custId, String dupNo, String cntrNo) {

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}

		String fullCustKeyNew = Util.getLeftStr(Util.trim(custId)
				+ "          ", 10)
				+ dupNo;
		return this.getJdbc().queryForList(
				"DWLNCNTROVS.selByCustIdAndCntrNo_withOriginal",
				new Object[] { fullCustKeyNew, cntrNo });
	}

	/**
	 * J-106-0029-002/003/004 洗錢防制-新增洗錢防制頁籤 新增實質受益人 動審表新增洗錢防制頁籤
	 * 
	 * @param brNo
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findDW_OTS_EFFECTIVE_OVS_By_CustId_And_BrNo(
			String brNo, String custId, String dupNo) {

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}

		String fullCustKeyNew = Util.getLeftStr(Util.trim(custId)
				+ "          ", 10)
				+ dupNo;
		return this.getJdbc().queryForList(
				"OTS_EFFECTIVE_OVS.selByCustIdAndBrNo",
				new Object[] { brNo, fullCustKeyNew });
	}

	/**
	 * J-106-0029-002/003/004 洗錢防制-新增洗錢防制頁籤 新增實質受益人 動審表新增洗錢防制頁籤
	 *
	 * @param brNo
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findDW_OTS_CSOVS_By_CustId_And_BrNo(String brNo,
			String custId, String dupNo) {

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}

		String fullCustKeyNew = Util.getLeftStr(Util.trim(custId)
				+ "          ", 10)
				+ dupNo;
		return this.getJdbc().queryForMap("OTS_CSOVS.selByCustIdAndBrNo",
				new Object[] { brNo, fullCustKeyNew });
	}

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0012() {
		this.getJdbc()
				.update("UPDATE DWADM.DW_ELINSPSC SET BRANCHID = (CASE BRANCHID WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE BRANCHID END) WHERE BRANCHID IN ('Z01','Z03')",
						new Object[] {});

	}

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0012_1() {
		this.getJdbc()
				.update("UPDATE DWADM.DW_FXRTHOVS SET  BR_CD= (CASE BR_CD WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_CD IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_ASLNDAVGOVS SET  BR_CD= (CASE BR_CD WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_CD IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_LNQUOTOV SET  BR_NO= (CASE BR_NO WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_NO IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_LNCNTROVS SET  BR_CD= (CASE BR_CD WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_CD IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_CSLNOVS SET  BR_CD= (CASE BR_CD WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_CD IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.OTS_CSOVS SET  BR_CD= (CASE BR_CD WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_CD IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_FXRTHOVS_SS SET  BR_CD= (CASE BR_CD WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_CD IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_ASLNOVEROVS SET  BR_NO= (CASE BR_NO WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_NO IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.OTS_EFFECTIVE_OVS SET  BR_CD= (CASE BR_CD WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_CD IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_ELF411OVS SET  BR_NO= (CASE BR_NO WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_NO IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_ELF412OVS SET  BR_NO= (CASE BR_NO WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_NO IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_ELF490OVS SET  BR_NO= (CASE BR_NO WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_NO IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_ROCGRPBLD1_E SET  BR_CD= (CASE BR_CD WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_CD IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_ROCGRPBDLND_ELN SET  BR_CD= (CASE BR_CD WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_CD IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.OTS_ASLNDNEWOVS SET  BR_CD= (CASE BR_CD WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_CD IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.OTS_ASLNDNEWOVS SET  LN_BR_NO= (CASE LN_BR_NO WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE LN_BR_NO IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.OTS_ASLNDNEWOVS SET  FACT_OPEN_BRNO= (CASE FACT_OPEN_BRNO WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE FACT_OPEN_BRNO IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_ASLNDAVGOVS SET  FACT_CONTR= (CASE LEFT(FACT_CONTR,3)  WHEN 'Z03' THEN '0D8' ELSE '0D7' END) || RIGHT(FACT_CONTR,9) WHERE LEFT(FACT_CONTR,3) IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_LNQUOTOV SET  CONTRACT= (CASE LEFT(CONTRACT,3)  WHEN 'Z03' THEN '0D8' ELSE '0D7' END) || RIGHT(CONTRACT,9) WHERE LEFT(CONTRACT,3) IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_LNCNTROVS SET  LINE_NO= (CASE LEFT(LINE_NO,3)  WHEN 'Z03' THEN '0D8' ELSE '0D7' END) || RIGHT(LINE_NO,9) WHERE LEFT(LINE_NO,3) IN ('Z01','Z03')",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_LNMRATEOVS SET  LINE_NO= (CASE LEFT(LINE_NO,3)  WHEN 'Z03' THEN '0D8' ELSE '0D7' END) || RIGHT(LINE_NO,9) WHERE LEFT(LINE_NO,3) IN ('Z01','Z03')",
						new Object[] {});

		this.getJdbc()
				.update("INSERT INTO DWADM.DW_ASLNOVEROVS SELECT CYC_MN,'0D7',OV_CNT_A1,OV_AMT_A1,OV_INT_A1,OV_CNT_A2,OV_AMT_A2,OV_CNT_A,OV_AMT_A,OV_RATE_A,OV_CNT_B1,OV_AMT_B1,OV_INT_B1,OV_CNT_B2,OV_AMT_B2,OV_CNT_B,OV_AMT_B,OV_RATE_B,OV_CNT_1,OV_AMT_1,OV_INT_1,OV_CNT_2,OV_AMT_2,OV_CNT,OV_AMT,OV_RATE,LN_BAL,LN_AMT,TYPEAMT_2,TYPEAMT_3,TYPEAMT_4,TYPEAMT_5,DW_LST_DATA_SRC,DW_DATA_SRC_DT,DW_LST_MNT_DT FROM DWADM.DW_ASLNOVEROVS WHERE CYC_MN BETWEEN '2018-02-01' AND '2018-03-01' AND BR_NO = '0E1'",
						new Object[] {});
		this.getJdbc()
				.update("INSERT INTO DWADM.DW_ASLNOVEROVS SELECT CYC_MN,'0D8',OV_CNT_A1,OV_AMT_A1,OV_INT_A1,OV_CNT_A2,OV_AMT_A2,OV_CNT_A,OV_AMT_A,OV_RATE_A,OV_CNT_B1,OV_AMT_B1,OV_INT_B1,OV_CNT_B2,OV_AMT_B2,OV_CNT_B,OV_AMT_B,OV_RATE_B,OV_CNT_1,OV_AMT_1,OV_INT_1,OV_CNT_2,OV_AMT_2,OV_CNT,OV_AMT,OV_RATE,LN_BAL,LN_AMT,TYPEAMT_2,TYPEAMT_3,TYPEAMT_4,TYPEAMT_5,DW_LST_DATA_SRC,DW_DATA_SRC_DT,DW_LST_MNT_DT FROM DWADM.DW_ASLNOVEROVS WHERE CYC_MN BETWEEN '2018-02-01' AND '2018-03-01' AND BR_NO = '0E1'",
						new Object[] {});

	}

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0012_2() {
		this.getJdbc()
				.update("UPDATE DWADM.MD_CUPFM_OTS SET  BR_CD= (CASE BR_CD WHEN 'Z03' THEN '0D8' ELSE '0D7' END) WHERE BR_CD IN ('Z01','Z03')",
						new Object[] {});

	}

	/**
	 * J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
	 * 
	 * @param badFg
	 * @param grpGrade
	 * @return
	 */
	@Override
	public Map<String, Object> findDW_DW_ROCGRPRT_By_BadFg_And_GrpGrade(
			String badFg, String grpGrade) {
		return this.getJdbc().queryForMap("DW_ROCGRPRT.selByBadFgAndGrpGrade",
				new Object[] { badFg, grpGrade });
	}

	/**
	 * J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
	 * 
	 * @param badFg
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findDW_DW_ROCGRPRT_By_BadFg_And_GrpYy(
			String badFg, String grpYy, String grpCycMn) {
		return this.getJdbc().queryForList("DW_ROCGRPRT.selByBadFgAndGrpYy",
				new Object[] { badFg, grpCycMn, grpCycMn });
	}

	/**
	 * J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
	 * 
	 * @param badFg
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findDW_DW_ROCGRPRT_By_BadFg_With_MaxCycMn(
			String badFg) {
		return this.getJdbc().queryForList(
				"DW_ROCGRPRT.selByBadFgWithMaxCycMn", new Object[] { badFg });
	}

	/**
	 * G-107-0115-001 Web e-Loan 授信系統配合巴箇行整併調整簽報書相關資料
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0013() {
		this.getJdbc()
				.update("UPDATE DWADM.DW_ELINSPSC SET BRANCHID = '0A5' WHERE BRANCHID IN ('0A6')",
						new Object[] {});

	}

	/**
	 * G-107-0115-001 Web e-Loan 授信系統配合巴箇行整併調整簽報書相關資料
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0013_1() {

		this.getJdbc()
				.update("UPDATE DWADM.DW_CSLNOVS SET  BR_CD= '0A5' WHERE BR_CD IN ('0A6')  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.OTS_CSOVS SET  BR_CD= '0A5' WHERE BR_CD IN ('0A6')  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.OTS_EFFECTIVE_OVS SET  BR_CD= '0A5' WHERE BR_CD IN ('0A6') ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_ASLNDAVGOVS SET  BR_CD= '0A5' WHERE BR_CD IN ('0A6')  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_LNQUOTOV SET  BR_NO= '0A5' WHERE BR_NO IN ('0A6')   ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_ASLNDAVGOVS SET  FACT_CONTR = '0A5'||SUBSTR(FACT_CONTR,4,4)||( CASE SUBSTR(FACT_CONTR,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(FACT_CONTR,8,1) END)||SUBSTR(FACT_CONTR,9,4) WHERE LEFT(FACT_CONTR,3) IN ('0A6') AND SUBSTR(FACT_CONTR,8,1) IN ('0','5') AND FACT_CONTR IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_LNQUOTOV SET  CONTRACT = '0A5'||SUBSTR(CONTRACT,4,4)||( CASE SUBSTR(CONTRACT,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(CONTRACT,8,1) END)||SUBSTR(CONTRACT,9,4) WHERE LEFT(CONTRACT,3) IN ('0A6') AND SUBSTR(CONTRACT,8,1) IN ('0','5') AND CONTRACT IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_LNCNTROVS SET  LINE_NO = '0A5'||SUBSTR(LINE_NO,4,4)||( CASE SUBSTR(LINE_NO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(LINE_NO,8,1) END)||SUBSTR(LINE_NO,9,4) WHERE LEFT(LINE_NO,3) IN ('0A6') AND SUBSTR(LINE_NO,8,1) IN ('0','5') AND LINE_NO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc()
				.update("UPDATE DWADM.DW_LNMRATEOVS SET LINE_NO = '0A5'||SUBSTR(LINE_NO,4,4)||( CASE SUBSTR(LINE_NO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(LINE_NO,8,1) END)||SUBSTR(LINE_NO,9,4) WHERE LEFT(LINE_NO,3) IN ('0A6') AND SUBSTR(LINE_NO,8,1) IN ('0','5') AND LINE_NO IS NOT NULL  ",
						new Object[] {});

	}

	/**
	 * G-107-0115-001 Web e-Loan 授信系統配合巴箇行整併調整簽報書相關資料
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0013_2() {
		this.getJdbc()
				.update("UPDATE DWADM.MD_CUPFM_OTS SET  BR_CD= '0A5' WHERE BR_CD IN ('0A6')",
						new Object[] {});

	}

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findDW_LNCNTROVS_Sum_By_CustId(String custId,
			String dupNo) {

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}

		String fullCustKeyNew = Util.getLeftStr(Util.trim(custId)
				+ "          ", 10)
				+ dupNo;
		return this.getJdbc().queryForMap("DW_LNCNTROVS.sumFamtByCustKey",
				new Object[] { fullCustKeyNew });
	}

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findDW_LNCNTROVS_Sum_By_CustId_BrIds(
			String custId, String dupNo, List<String> brIds) {

		String brIdParams = Util.genSqlParam(brIds);
		List<Object> params = new ArrayList<Object>();

		// StringBuilder brIdsSb = new StringBuilder();
		// for (String branch : brIds) {
		// if (Util.isEmpty(brIdsSb)) {
		// brIdsSb.append("'" + branch + "'");
		// } else {
		// brIdsSb.append(",'" + branch + "'");
		// }
		// }
		// if (Util.isEmpty(brIdsSb)) {
		// brIdsSb.append("''");
		// }

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}

		String fullCustKeyNew = Util.getLeftStr(Util.trim(custId)
				+ "          ", 10)
				+ dupNo;

		params.add(fullCustKeyNew);
		params.addAll(brIds);
		return this.getJdbc().queryForListByCustParam(
				"DW_LNCNTROVS.sumFamtByCustKeyBrIds",
				new Object[] { brIdParams }, params.toArray(new Object[0]));
	}

	@Override
	public List<Map<String, Object>> findDW_OTS_ASFDAC2_J_107_0073(
			String cust_key) {
		String cmp_date = TWNDate.toAD(CapDate.getCurrentTimestamp());
		return this.getJdbc().queryForListWithMax(
				"DW_OTS_ASFDAC2.sel_J_107_0073",
				new Object[] { cust_key, cmp_date });
	}

	@Override
	public List<Map<String, Object>> findDW_OTS_WM_CUST_SUMMARY_J_107_0073(
			String cusCode, String cyc_mn_beg) {
		return this.getJdbc().queryForListWithMax(
				"DW_OTS_WM_CUST_SUMMARY.sel_J_107_0073",
				new Object[] { cusCode, cyc_mn_beg });
	}

	/**
	 * J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。
	 * 
	 * @param custKey
	 * @param dupNo
	 * @param dateS
	 * @param dateE
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE_exclude_interest(
			String custKey, String dupNo, String dateS, String dateE) {
		// String likeCustKey = Util.getLeftStr(custKey, 10) + "%";
		String likeCustKey = Util.getLeftStr(custKey + "          ", 10) + "%";
		/*
		 * String likeCustKey = null; // 企業戶統編(第三碼為Z 或 非台灣自然人統編) if
		 * ("Z".equals(custKey.substring(2, 3)) ||
		 * !(custKey.matches("^[a-zA-Z](1|2)\\d{8}$") || custKey
		 * .matches("^[a-zA-Z]{2}\\d{8}$"))) { likeCustKey =
		 * Util.getLeftStr(custKey, 10) + "%"; }else{ likeCustKey =
		 * Util.getLeftStr(custKey+"           ", 11) + "%"; }
		 */
		return this.getJdbc().queryForListWithMax(
				"DM_CUBCPCM.selTOTAL_ATTRIBUTE_exclude_interest",
				new Object[] { likeCustKey, dateS, dateE });
	}

	/**
	 * J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。
	 * 
	 * @param custKey
	 * @param dupNo
	 * @param dateS
	 * @param dateE
	 * @return
	 */
	@Override
	public List<?> findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE_exclude_interest(
			String custKey, String dupNo, String dateS, String dateE) {
		// String likeCustKey = Util.getLeftStr(custKey, 10) + "%";
		String likeCustKey = Util.getLeftStr(custKey + "          ", 10) + "%";
		/*
		 * String likeCustKey = null; // 企業戶統編(第三碼為Z 或 非台灣自然人統編) if
		 * ("Z".equals(custKey.substring(2, 3)) ||
		 * !(custKey.matches("^[a-zA-Z](1|2)\\d{8}$") || custKey
		 * .matches("^[a-zA-Z]{2}\\d{8}$"))) { likeCustKey =
		 * Util.getLeftStr(custKey, 10) + "%"; }else{ likeCustKey =
		 * Util.getLeftStr(custKey+"           ", 11) + "%"; }
		 */
		return this.getJdbc().queryForListWithMax(
				"DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE_exclude_interest",
				new Object[] { likeCustKey, dateS, dateE });
	}

	/**
	 * J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」
	 */
	@Override
	public List<Map<String, Object>> findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE_ByBC3_CD(
			String custKey, String dupNo, String dateS, String dateE,
			String type) {
		String likeCustKey = Util.getLeftStr(custKey + "          ", 10) + "%";
		List<Object> params = new ArrayList<Object>();
		// 授信
		String[] type1 = new String[] { "NTLN", "FXLN", "NTGA", "FXGA", "NTBA",
				"FXBA", "NTFA", "NTFB", "FXFA", "FXFB", "NTFI", "NTFJ", "FXFJ",
				"FXFI" };

		// 存款
		String[] type2 = new String[] { "NTDP", "FXDP", "NTRT" };

		// 外匯
		String[] type3 = new String[] { "FXRT", "FXIX", "FXEX" };

		// 財管
		String[] type4 = new String[] { "FDFU", "FDTB", "FDCF", "STD", "IA" };

		// 衍生性商品
		String[] type5 = new String[] { "FXOPT", "IRS", "CCS" };

		// 信託
		String[] type6 = new String[] { "FDTA", "FDTA_T", "FDTC", "FDTD",
				"FDFM", "FDCD" };

		// 薪轉戶
		String[] type7 = new String[] { "SLDP" };

		// 票債券
		String[] type8 = new String[] { "BDRP", "BDRS", "BLRP", "BLRS" };

		// 卡務
		String[] type9 = new String[] { "CCCC" };

		StringBuffer temp = new StringBuffer(0);
		if (Util.equals(type, "1")) { // 一年內授信業務
			// for (String t : type1) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp = new StringBuffer(Util.genSqlParam(type1));
			params.addAll(Arrays.asList(type1));
		} else if (Util.equals(type, "2")) { // 一年內存款及外匯
			temp = new StringBuffer(Util.genSqlParam(ArrayUtils.addAll(type2,
					type3)));
			// for (String t : type2) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			params.addAll(Arrays.asList(type2));
			// for (String t : type3) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			params.addAll(Arrays.asList(type3));
		} else if (Util.equals(type, "3")) { // 一年內財管及其他
			// for (String t : type4) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp = new StringBuffer(Util.genSqlParam(type4));
			params.addAll(Arrays.asList(type4));
			// for (String t : type5) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp.append(",").append(Util.genSqlParam(type5));
			params.addAll(Arrays.asList(type5));
			// for (String t : type6) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp.append(",").append(Util.genSqlParam(type6));
			params.addAll(Arrays.asList(type6));
			// for (String t : type7) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp.append(",").append(Util.genSqlParam(type7));
			params.addAll(Arrays.asList(type7));
			// for (String t : type8) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp.append(",").append(Util.genSqlParam(type8));
			params.addAll(Arrays.asList(type8));
			// for (String t : type9) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp.append(",").append(Util.genSqlParam(type9));
			params.addAll(Arrays.asList(type9));
		}

		if (temp.length() > 0) {
			temp.insert(0, "BC3_CD IN (");
			temp.append(") AND ");
		}

		params.add(likeCustKey);
		params.add(dateS);
		params.add(dateE);

		return this.getJdbc()
				.queryForListByCustParam(
						"DM_CUBCPCM.selTOTAL_ATTRIBUTE_ByBC3_CD",
						new Object[] { temp.toString() },
						params.toArray(new Object[0]));

		// return this.getJdbc().queryForListWithMax(
		// "DM_CUBCPCM.selTOTAL_ATTRIBUTE",
		// new Object[] { likeCustKey, dateS, dateE });
	}

	/**
	 * J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」
	 */
	@Override
	public List<?> findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE_ByBC3_CD(
			String custKey, String dupNo, String dateS, String dateE,
			String type) {
		String likeCustKey = Util.getLeftStr(custKey + "          ", 10) + "%";
		List<Object> params = new ArrayList<Object>();

		// 授信
		String[] type1 = new String[] { "NTLN", "FXLN", "NTGA", "FXGA", "NTBA",
				"FXBA", "NTFA", "NTFB", "FXFA", "FXFB", "NTFI", "NTFJ", "FXFJ",
				"FXFI" };

		// 存款
		String[] type2 = new String[] { "NTDP", "FXDP", "NTRT" };

		// 外匯
		String[] type3 = new String[] { "FXRT", "FXIX", "FXEX" };

		// 財管
		String[] type4 = new String[] { "FDFU", "FDTB", "FDCF", "STD", "IA" };

		// 衍生性商品
		String[] type5 = new String[] { "FXOPT", "IRS", "CCS" };

		// 信託
		String[] type6 = new String[] { "FDTA", "FDTA_T", "FDTC", "FDTD",
				"FDFM", "FDCD" };

		// 薪轉戶
		String[] type7 = new String[] { "SLDP" };

		// 票債券
		String[] type8 = new String[] { "BDRP", "BDRS", "BLRP", "BLRS" };

		// 卡務
		String[] type9 = new String[] { "CCCC" };

		StringBuffer temp = new StringBuffer(0);
		if (Util.equals(type, "1")) { // 一年內授信業務
			// for (String t : type1) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp = new StringBuffer(Util.genSqlParam(type1));
			params.addAll(Arrays.asList(type1));
		} else if (Util.equals(type, "2")) { // 一年內存款及外匯
			temp = new StringBuffer(Util.genSqlParam(ArrayUtils.addAll(type2,
					type3)));
			// for (String t : type2) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			params.addAll(Arrays.asList(type2));
			// for (String t : type3) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			params.addAll(Arrays.asList(type3));
		} else if (Util.equals(type, "3")) { // 一年內財管及其他
			// for (String t : type4) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp = new StringBuffer(Util.genSqlParam(type4));
			params.addAll(Arrays.asList(type4));
			// for (String t : type5) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp.append(",").append(Util.genSqlParam(type5));
			params.addAll(Arrays.asList(type5));
			// for (String t : type6) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp.append(",").append(Util.genSqlParam(type6));
			params.addAll(Arrays.asList(type6));
			// for (String t : type7) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp.append(",").append(Util.genSqlParam(type7));
			params.addAll(Arrays.asList(type7));
			// for (String t : type8) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp.append(",").append(Util.genSqlParam(type8));
			params.addAll(Arrays.asList(type8));
			// for (String t : type9) {
			// temp.append(temp.length() > 0 ? "," : "");
			// temp.append("'");
			// temp.append(t);
			// temp.append("'");
			// }
			temp.append(",").append(Util.genSqlParam(type9));
			params.addAll(Arrays.asList(type9));
		}

		if (temp.length() > 0) {
			temp.insert(0, "BC3_CD IN (");
			temp.append(") AND ");
		}

		params.add(likeCustKey);
		params.add(dateS);
		params.add(dateE);

		return this.getJdbc()
				.queryForListByCustParam(
						"DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE_ByBC3_CD",
						new Object[] { temp.toString() },
						params.toArray(new Object[0]));

		// return this.getJdbc().queryForListWithMax(
		// "DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE_ByBC3_CD",
		// new Object[] { likeCustKey, dateS, dateE });
	}

	/**
	 * J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
	 */
	@Override
	public List<Map<String, Object>> findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE_GroupByBC3_CD(
			String custKey, String dupNo, String dateS, String dateE) {
		String likeCustKey = Util.getLeftStr(custKey + "          ", 10) + "%";

		return this.getJdbc().queryForList(
				"DM_CUBCPCM.selTOTAL_ATTRIBUTE_GroupByBC3_CD",
				new Object[] { likeCustKey, dateS, dateE });
	}

	@Override
	public List<?> findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE_GroupByBC3_CD(
			String custKey, String dupNo, String dateS, String dateE) {
		String likeCustKey = Util.getLeftStr(custKey + "          ", 10) + "%";

		return this.getJdbc().queryForList(
				"DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE_GroupByBC3_CD",
				new Object[] { likeCustKey, dateS, dateE });
	}

	/**
	 * J-107-0224_05097_B1001 Web e-Loan企金處新增企金授信案件敘做情形及比較表
	 * 
	 * @param dateS
	 * @param dateE
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findOTS_RPC1A4ByRptDate(String dateS,
			String dateE) {
		return this.getJdbc().queryForListWithMax("OTS_RPC1A4.selbyRPT_DT",
				new Object[] { dateS, dateE });
	}

	@Override
	public List<Map<String, Object>> findLnCntrovsExitem() {
		return this.getJdbc().queryForListWithMax("DW_LNCNTROVS.findAll",
				new Object[] {});
	}

	/**
	 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findDW_LNCNTROVSByCustIdForCreExcel2(
			String custId, String dupNo) {

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}

		String fullCustKeyNew = Util.getLeftStr(Util.trim(custId)
				+ "          ", 10)
				+ dupNo;

		return this.getJdbc().queryForListWithMax(
				"DWLNCNTROVS.selByCustIdForCreExcel2",
				new Object[] { fullCustKeyNew });
	}

	@Override
	public List<Map<String, Object>> findDW_RKAPPLICANT_byNoteIdCustIdDupNo(
			String noteId1, String noteId2, String custId, String dupNo) {
		return this.getJdbc().queryForListWithMax(
				"DWADM.DW_RKAPPLICANT.SelectByNoteIdCustIdDupNo",
				new String[] { noteId1, noteId2, custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findDW_RKJCIC_byNoteIdCustIdDupNo(
			String noteId1, String noteId2, String custId, String dupNo) {
		return this.getJdbc().queryForListWithMax(
				"DWADM.DW_RKJCIC.SelectByNoteIdCustIdDupNo",
				new String[] { noteId1, noteId2, custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> find_fixData(String br_cd, String noteId,
			String cntrNo) {

		return this.getJdbc().queryForListWithMax("DW_RKCNTRNO.fixLnmonth",
				new String[] { br_cd + "%", noteId + "%", cntrNo + "%" });
	}

	@Override
	public void run_fixData(String BR_CD, String NOTEID, String CUSTID,
			String DUPNO, String MOWTYPE, String MOWVER1, String MOWVER2,
			String JCIC_DATE, String CNTRNO, String ACCT_KEY, Integer LNMONTH) {
		int[] type = { java.sql.Types.INTEGER, java.sql.Types.INTEGER };
		List<Object[]> lst = new ArrayList<Object[]>();
		lst.add(new Object[] { LNMONTH, BR_CD, NOTEID, CUSTID, DUPNO, MOWTYPE,
				MOWVER1, MOWVER2, JCIC_DATE, CNTRNO, ACCT_KEY });

		List<String> table_list = new ArrayList<String>();
		table_list.add("DWADM.DW_RKCNTRNO");
		for (String table : table_list) {
			update(new Object[] {
					table,
					"LNMONTH=? ",
					"BR_CD=? and NOTEID=? and CUSTID=? and DUPNO=? and MOWTYPE=? and MOWVER1=? and MOWVER2=? and JCIC_DATE=? and CNTRNO=? and ACCT_KEY=?" },
					type, lst);
		}
	}

	@Override
	public List<Map<String, Object>> find_clsScoreData_by_noteId(
			String tableName, String noteId) {
		return this.getJdbc().queryForAllListByCustParam(
				"DW.find_clsScoreData_by_noteId", new String[] { tableName },
				new String[] { noteId });
	}

	@Override
	public void dw_fixData_DATA_SRC_DT__null(String tableName, String noteId,
			String data_src_dt) {
		int[] type = { java.sql.Types.INTEGER, java.sql.Types.INTEGER };
		List<Object[]> lst = new ArrayList<Object[]>();
		lst.add(new Object[] { data_src_dt, noteId });

		List<String> table_list = new ArrayList<String>();
		table_list.add(tableName);
		for (String table : table_list) {
			update(new Object[] { table, "DATA_SRC_DT=? ",
					"NOTEID=? AND DATA_SRC_DT IS NULL" }, type, lst);
		}
	}

	@Override
	public List<Map<String, Object>> find_fixData_OTS_RKCREDITOVS_CHKDATE_CHKEMPNO(
			String br_cd, String noteId, String cntrNo) {
		return this.getJdbc().queryForListWithMax(
				"OTS_RKCREDITOVS.fixCHKDATE_CHKEMPNO",
				new String[] { br_cd + "%", noteId + "%", cntrNo + "%" });
	}

	@Override
	public void run_fixData_OTS_RKCREDITOVS_CHKDATE_CHKEMPNO(String BR_CD,
			String NOTEID, String RATING_DATE, String RATING_ID, String CUSTID,
			String DUPNO, String CUST_KEY, String LOAN_CODE, String MOWTYPE,
			String MOWVER1, String MOWVER2, String CHKDATE, String CHKEMPNO) {
		int[] type = { java.sql.Types.INTEGER, java.sql.Types.INTEGER };
		List<Object[]> lst = new ArrayList<Object[]>();
		lst.add(new Object[] { CHKDATE, CHKEMPNO, BR_CD, NOTEID, RATING_DATE,
				RATING_ID, CUSTID, DUPNO, CUST_KEY, LOAN_CODE, MOWTYPE,
				MOWVER1, MOWVER2 });

		List<String> table_list = new ArrayList<String>();
		table_list.add("DWADM.OTS_RKCREDITOVS");
		for (String table : table_list) {
			update(new Object[] {
					table,
					"CHKDATE=? , CHKEMPNO=? ",
					"BR_CD=? and NOTEID=? and RATING_DATE=? and RATING_ID=? and CUSTID=? and DUPNO=? and CUST_KEY=? and LOAN_CODE=? and MOWTYPE=? and MOWVER1=? and MOWVER2=?" },
					type, lst);
		}
	}

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0015_5(String fBranch, String tBranch, String custId,
			String dupNo) {

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}

		String fullCustKeyNew = Util.getLeftStr(Util.trim(custId)
				+ "          ", 10)
				+ dupNo;

		try {
			this.getJdbc().update("DW_LNCNTROVS.updateLineNoForBt15",
					new Object[] { tBranch, fBranch, fullCustKeyNew });
		} catch (Exception e) {
			logger.error(e.getMessage());
			// 若無資料不處理
		}

		try {
			this.getJdbc().update("DW_LNQUOTOV.updateLineNoForBt15",
					new Object[] { tBranch, fBranch, fullCustKeyNew });
		} catch (Exception e) {
			logger.error(e.getMessage());
			// 若無資料不處理
		}

		// try {
		// this.getJdbc().update("OTS_EFFECTIVE_OVS.updateBrCdForBt15",
		// new Object[] { tBranch, fBranch, fullCustKeyNew });
		// } catch (Exception e) {
		// logger.error(e.getMessage());
		// // 若無資料不處理
		// }
		//
		// try {
		// this.getJdbc().update("OTS_CSOVS.updateBrCdForBt15",
		// new Object[] { tBranch, fBranch, fullCustKeyNew });
		// } catch (Exception e) {
		// logger.error(e.getMessage());
		// // 若無資料不處理
		// }

		try {
			this.getJdbc().update("DW_CSLNOVS.updateBrCdForBt15",
					new Object[] { tBranch, fBranch, fullCustKeyNew });
		} catch (Exception e) {
			logger.error(e.getMessage());
			// 若無資料不處理
		}

	}

	@Override
	public boolean isLnCustRel_done(String cyc_mn, String done_cust_key) {
		return this
				.getJdbc()
				.queryForList("DW_LNCUSTREL.find_by_cyc_mn_cust_key",
						new String[] { cyc_mn, done_cust_key }).size() > 0;
	}

	@Override
	public Map<String, Object> findLnCustRel_done_history(String done_cust_key) {
		return this.getJdbc().queryForMap("DW_LNCUSTREL.list_by_cust_key",
				new String[] { done_cust_key });
	}

	@Override
	public List<Map<String, Object>> findLnCustRel_by_rel_flag(String cyc_mn,
			String rel_flag) {
		return this.getJdbc().queryForListWithMax("DW_LNCUSTREL.find",
				new String[] { cyc_mn, "%", rel_flag + "%" });
	}

	@Override
	public BigDecimal countLnCustRel_done_cust_key(String cyc_mn,
			String done_cust_key) {
		List<Map<String, Object>> list = this.getJdbc().queryForList(
				"DW_LNCUSTREL.count",
				new String[] { cyc_mn, done_cust_key, "%" });
		if (list.size() > 0) {
			return BigDecimal.valueOf(MapUtils.getIntValue(list.get(0), "CNT",
					0));
		} else {
			return BigDecimal.ZERO;
		}
	}

	@Override
	public BigDecimal countLnCustRel_rel_type(String cyc_mn, String rel_type) {
		List<Map<String, Object>> list = this.getJdbc().queryForList(
				"DW_LNCUSTREL.count",
				new String[] { cyc_mn, "", rel_type + "%" });
		if (list.size() > 0) {
			return BigDecimal.valueOf(MapUtils.getIntValue(list.get(0), "CNT",
					0));
		} else {
			return BigDecimal.ZERO;
		}
	}

	@Override
	public List<Map<String, Object>> findLnCustRel_top_dup_cnt(String cyc_mn,
			String rel_flag, Integer val) {
		return this.getJdbc().queryForList("DW_LNCUSTREL.top_dup_cnt",
				new Object[] { cyc_mn, rel_flag, val });
	}

	@Override
	public List<Map<String, Object>> findLnCustBr(String cyc_mn) {
		return this.getJdbc().queryForListWithMax("DW_LNCUSTBR.find",
				new String[] { cyc_mn });
	}

	@Override
	public BigDecimal countLnCustBr(String cyc_mn) {
		List<Map<String, Object>> list = this.getJdbc().queryForList(
				"DW_LNCUSTBR.count", new String[] { cyc_mn });
		if (list.size() > 0) {
			return BigDecimal.valueOf(MapUtils.getIntValue(list.get(0), "CNT",
					0));
		} else {
			return BigDecimal.ZERO;
		}
	}

	@Override
	public Map<String, Object> test_DwVam106Cc_getVAM106Data(String id) {
		return this.getJdbc().queryForMap("DW_VAM106_CC.getVAM106Data",
				new Object[] { id });
	}

	@Override
	public Map<String, Object> test_DwVam107Cc_getVAM107Data(String id) {
		return this.getJdbc().queryForMap("DW_VAM107_CC.getVAM107Data",
				new Object[] { id });
	}

	@Override
	public Map<String, Object> test_DwVam108Cc_getVAM108Data(String id) {
		return this.getJdbc().queryForMap("DW_VAM108_CC.getVAM108Data",
				new Object[] { id });
	}

	@Override
	public Map<String, Object> test_DwBam095Cc_getBAM087DelayPayLoan(String id) {

		Map<String, Object> result = new HashMap<String, Object>();
		int count = 0;
		List<Map<String, Object>> list = this.getJdbc().queryForList(
				"DW_BAM095_CC.getBAM087DelayPayLoan", new Object[] { id });
		for (Map<String, Object> map : list) {
			String PAY_CODE_12 = Util.trim(map.get("PAY_CODE_12"));
			if (Util.isNotEmpty(PAY_CODE_12)) {
				char[] chars = PAY_CODE_12.toCharArray();
				for (char c : chars) {
					if (ArrayUtils.indexOf(DelayPayLoanChars, c) != -1)
						count++;
				}
			}
		}

		result.put("Counts", count);
		return result;
	}

	@Override
	public Map<String, Object> test_DwKrm040Cc_getKRM040CardPayCode2(
			String custId) {
		return this.getJdbc().queryForMap("DW_KRM040_CC.getKRM040CardPayCode2",
				new Object[] { custId });
	}

	@Override
	public Map<String, Object> test_DwKrm040Cc_getKRM040CardPayCode3(
			String custId) {
		return this.getJdbc().queryForMap("DW_KRM040_CC.getKRM040CardPayCode3",
				new Object[] { custId });
	}

	@Override
	public List<Map<String, Object>> test_DwKrm040Cc_getKRM040CardPayCode4(
			String custId) {
		return this.getJdbc().queryForList(
				"DW_KRM040_CC.getKRM040CardPayCode4", new Object[] { custId });
	}

	@Override
	public Map<String, Object> test_DwKrm040Cc_getKRM040CashAdvance(
			String custId) {
		return this.getJdbc().queryForMap("DW_KRM040_CC.getKRM040CashAdvance",
				new Object[] { custId });
	}

	@Override
	public Map<String, Object> test_DwBam095Cc_getBAM087CashCard(String id) {

		Map<String, Object> result = null;
		List<Map<String, Object>> list = this.getJdbc().queryForList(
				"DW_BAM095_CC.getBAM087CashCard", new Object[] { id });
		for (Map<String, Object> map : list) {
			/*
			 * 問處內負責JCIC的同仁，欄位 PAY_CODE_12 的內容 0-無延遲 X-不需還款
			 */
			String PAY_CODE_12 = Util.trim(map.get("PAY_CODE_12"));
			if (Util.isNotEmpty(PAY_CODE_12)) {
				char[] chars = PAY_CODE_12.toCharArray();
				for (char c : chars) {
					if (ArrayUtils.indexOf(CashCardChars, c) == -1) {
						if (result == null)
							result = new HashMap<String, Object>();
						result.put("PCODE", "D");
					}
				}
			}
		}

		return result;
	}

	@Override
	public Map<String, Object> test_DwBam095Cc_getD63LnNosBank(String id) {
		return this.getJdbc().queryForMap("DW_BAM095_CC.getD63LnNosBank",
				new Object[] { id });
	}

	@Override
	public Map<String, Object> test_DwKrm040Cc_getAllCc6RcUseBank(String id) {
		return this.getJdbc().queryForMap("DW_KRM040_CC.getAllCc6RcUseBank",
				new Object[] { id });
	}

	@Override
	public Map<String, Object> test_DwBam095Cc_getD07_G_V_1_3(String id) {
		return this.getJdbc().queryForMap("DW_BAM095_CC.getD07_G_V_1_3",
				new Object[] { id });
	}

	@Override
	public Map<String, Object> test_DwKrm040Cc_getP68P19_Q(String id) {
		return this.getJdbc().queryForMap("DW_KRM040_CC.getP68P19_Q",
				new Object[] { id });
	}

	@Override
	public List<Map<String, Object>> getElDeleteFileList(String time,
			String brs, int count) {
		String where = "";
		List<String> list = new ArrayList<String>();

		where = " AND DATE(CMST_CLOSE_DATE)+" + time
				+ " year < DATE(CURRENT TIMESTAMP)";
		if (Util.isNotEmpty(brs)) {
			where += " AND BR_CD IN (";
			for (int i = 0; i < count; i++) {
				where += (i == 0 ? "?" : ",?");
			}
			where += ")";
		}
		for (String b : brs.split(",")) {
			list.add(b);
		}

		return this.getJdbc().queryForAllListByCustParam(
				"OTS_CSOVS.getElDeleteFileList",
				new Object[] { where.toString() }, list.toArray());
	}

	@Override
	public List<Map<String, Object>> getElDeleteFileListSingle(String time,
			String brs) {
		String where = "";
		List<String> list = new ArrayList<String>();

		where = " AND DATE(CMST_CLOSE_DATE)+" + time
				+ " year < DATE(CURRENT TIMESTAMP)";
		if (Util.isNotEmpty(brs)) {
			where += " AND BR_CD = ?";
			list.add(brs);
		}

		return this.getJdbc().queryForAllListByCustParam(
				"OTS_CSOVS.getElDeleteFileList",
				new Object[] { where.toString() }, list.toArray());
	}

	/**
	 * J-108-0143_05097_B1001 Web e-Loan國內外企金額度明細表簽報性質新作時加註(新客戶往來原有客戶往來)
	 * 
	 * @return
	 */
	@Override
	public Map<String, Object> findOTS_CSFACT_LIST_maxCynMn() {
		return this.getJdbc().queryForMap("OTS_CSFACT_LIST.getMaxCynMn",
				new Object[] {});
	}

	/**
	 * J-108-0143_05097_B1001 Web e-Loan國內外企金額度明細表簽報性質新作時加註(新客戶往來原有客戶往來)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cycMn
	 * @return
	 */
	@Override
	public Map<String, Object> findOTS_CSFACT_LIST_by_custKey(String custId,
			String dupNo, String cycMn) {

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}

		String fullCustKeyNew = Util.getLeftStr(Util.trim(custId)
				+ "          ", 10)
				+ dupNo;

		return this.getJdbc().queryForMap(
				"OTS_CSFACT_LIST.getByCustKeyAndCycMn",
				new Object[] { fullCustKeyNew, cycMn });
	}

	/**
	 * J-108-0268 覆審案件 客戶逾期情形
	 */
	@Override
	public Map<String, Object> findOverDue(Date qryDate, String custId,
			String dupNo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<Object> where = new ArrayList<Object>();
		if (qryDate == null || custId == null || custId.length() <= 1) {
			return null;
		}
		Calendar c;
		c = Calendar.getInstance();
		c.setTime(qryDate);
		c.add(Calendar.MONTH, -12);
		where.add(c.getTime());
		c.setTime(qryDate);
		c.add(Calendar.DATE, -1);
		where.add(c.getTime());
		where.add(custId);
		where.add(dupNo);

		// 本金
		Map<String, Object> CapDays = this.getJdbc().queryForMap(
				"DW_LNF253_DFTOTS.getCapOvDaysByDatadate", where.toArray());
		if (CapDays == null || CapDays.get("CAP_OV_DAYS") == null) {
			resultMap.put("CapDays", null);
		} else {
			resultMap.put("CapDays", (Integer) (CapDays.get("CAP_OV_DAYS")));
			resultMap.put("CapOvDate", CapDate.formatDate(
					(Date) (CapDays.get("CAP_OV_DATE")), "yyyy-MM-dd"));
			resultMap.put("CapDataDate", CapDate.formatDate(
					(Date) (CapDays.get("DATA_DATE")), "yyyy-MM-dd"));
		}
		// 利息
		Map<String, Object> IntDays = this.getJdbc().queryForMap(
				"DW_LNF253_DFTOTS.getIntOvDaysByDatadate", where.toArray());
		if (IntDays == null || IntDays.get("INT_OV_DAYS") == null) {
			resultMap.put("IntDays", null);
		} else {
			resultMap.put("IntDays", (Integer) (IntDays.get("INT_OV_DAYS")));
			resultMap.put("IntOvDate", CapDate.formatDate(
					(Date) (IntDays.get("INT_OV_DATE")), "yyyy-MM-dd"));
			resultMap.put("IntDataDate", CapDate.formatDate(
					(Date) (IntDays.get("DATA_DATE")), "yyyy-MM-dd"));
		}

		resultMap.put("qryDate", CapDate.formatDate(qryDate, "yyyy-MM-dd"));
		return resultMap;
	}

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param mainId
	 */
	@Override
	public void delete_DW_COSTSHARE_LMS01(String mainId) {
		this.getJdbc().update("DW_COSTSHARE_LMS01.deleteByMainId",
				new Object[] { mainId });

	}

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param mainId
	 */
	@Override
	public void delete_DW_COSTSHARE_LMS02(String mainId) {
		this.getJdbc().update("DW_COSTSHARE_LMS02.deleteByMainId",
				new Object[] { mainId });

	}

	/**
	 * M-108-0296_05097_B1003 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param mainId
	 */
	@Override
	public void delete_DW_COSTSHARE_LMS03(String mainId) {
		this.getJdbc().update("DW_COSTSHARE_LMS03.deleteByMainId",
				new Object[] { mainId });

	}

	/**
	 * M-108-0296_05097_B1004 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param mainId
	 */
	@Override
	public void delete_DW_COSTSHARE_LMS04(String mainId) {
		this.getJdbc().update("DW_COSTSHARE_LMS04.deleteByMainId",
				new Object[] { mainId });

	}

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param CNTRNO
	 * @param CASEBRID
	 * @param CASETYPE
	 * @param CASELVL
	 * @param COLLTYPE
	 * @param CASENO
	 * @param ENDDATE
	 * @param MAINID
	 * @param CNTRMAINID
	 * @param UPDATER
	 */
	@Override
	public void DW_COSTSHARE_LMS01_INSERT(String CNTRNO, String CASEBRID,
			String CASETYPE, String CASELVL, String COLLTYPE, String CASENO,
			String ENDDATE, String MAINID, String CNTRMAINID, String AREABRID,
			String APPROVEDATE, String UPDATER, Timestamp DELETETIME) {
		this.getJdbc().update(
				"DW_COSTSHARE_LMS01.insert",
				new Object[] { CNTRNO, CASEBRID, CASETYPE, CASELVL, COLLTYPE,
						CASENO, ENDDATE, MAINID, CNTRMAINID, AREABRID,
						APPROVEDATE, UPDATER, DELETETIME });
	}

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param CNTRNO
	 * @param OWNBRID
	 * @param CASETYPE
	 * @param CASELVL
	 * @param COLLTYPE
	 * @param RETRIALDATE
	 * @param PROJECTNO
	 * @param ENDDATE
	 * @param CASENO
	 * @param MAINID
	 * @param RPTMAINID
	 * @param UPDATER
	 */
	public void DW_COSTSHARE_LMS02_INSERT(String CNTRNO, String OWNBRID,
			String CASETYPE, String CASELVL, String COLLTYPE,
			String RETRIALDATE, String PROJECTNO, String ENDDATE,
			String CASENO, String MAINID, String RPTMAINID, String UPDATER,
			Timestamp DELETETIME) {
		this.getJdbc().update(
				"DW_COSTSHARE_LMS02.insert",
				new Object[] { CNTRNO, OWNBRID, CASETYPE, CASELVL, COLLTYPE,
						RETRIALDATE, PROJECTNO, ENDDATE, CASENO, MAINID,
						RPTMAINID, UPDATER, DELETETIME });
	}

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findOTS_SMTLMTByCustId(String custId,
			String dupNo) {

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}

		String fullCustKeyNew = Util.getLeftStr(Util.trim(custId)
				+ "          ", 10)
				+ dupNo;

		return this.getJdbc().queryForListWithMax("OTS_SMTLMT.selectByCustKey",
				new Object[] { fullCustKeyNew });
	}

	/**
	 * M-108-0296_05097_B1003 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param CASENO
	 * @param CASEBRID
	 * @param CASETYPE
	 * @param CASELVL
	 * @param DOCCODE
	 * @param ENDDATE
	 * @param MAINID
	 * @param AREABRID
	 * @param APPROVEDATE
	 * @param UPDATER
	 */
	@Override
	public void DW_COSTSHARE_LMS03_INSERT(String CASENO, String CASEBRID,
			String CASETYPE, String CASELVL, String DOCCODE, String ENDDATE,
			String MAINID, String AREABRID, String APPROVEDATE, String UPDATER,
			Timestamp DELETETIME) {
		this.getJdbc().update(
				"DW_COSTSHARE_LMS03.insert",
				new Object[] { CASENO, CASEBRID, CASETYPE, CASELVL, DOCCODE,
						ENDDATE, MAINID, AREABRID, APPROVEDATE, UPDATER,
						DELETETIME });
	}

	/**
	 * M-108-0296_05097_B1004 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param CNTRMAINID
	 * @param CNTRNO
	 * @param SHAREBRID
	 * @param SHAREFLAG
	 * @param SHARERATE1
	 * @param SHARERATE2
	 * @param SHAREAMT
	 * @param TOTALAMT
	 * @param SHARENO
	 * @param SNOKIND
	 * @param CURRENTAPPLYCURR
	 * @param CURRENTAPPLYAMT
	 * @param UPDATER
	 * @param DELETETIME
	 */
	@Override
	public void DW_COSTSHARE_LMS04_INSERT(String CNTRMAINID, String CNTRNO,
			String SHAREBRID, String SHAREFLAG, BigDecimal SHARERATE1,
			BigDecimal SHARERATE2, BigDecimal SHAREAMT, BigDecimal TOTALAMT,
			String SHARENO, String SNOKIND, String CURRENTAPPLYCURR,
			BigDecimal CURRENTAPPLYAMT, String UPDATER, Timestamp DELETETIME) {
		this.getJdbc().update(
				"DW_COSTSHARE_LMS04.insert",
				new Object[] { CNTRMAINID, CNTRNO, SHAREBRID, SHAREFLAG,
						SHARERATE1, SHARERATE2, SHAREAMT, TOTALAMT, SHARENO,
						SNOKIND, CURRENTAPPLYCURR, CURRENTAPPLYAMT, UPDATER,
						DELETETIME, });
	}

	/**
	 * J-108-0288_05097_B1003 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * 其他具相同地址、電話等原因之關係企業
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findRelatedCompanyWithSameTELAndAddr(
			String custId, String dupNo) {

		return this.getJdbc().queryForListWithMax(
				"findRelatedCompanyWithSameTELAndAddr",
				new Object[] { custId, dupNo, custId, dupNo, custId, dupNo,
						custId, dupNo, custId, dupNo, custId, dupNo });
	}

	/**
	 * J-109-0115_10702_B1001 Web e-Loan管理報表新增海外分行「授信業務異常通報月報表」
	 *
	 * @param brnNo
	 * @param custId
	 * @return
	 */
	@Override
	public List<Map<String, Object>> listLMS180R25_2(String brnNo, String custId) {

		return this.getJdbc().queryForListWithMax(
				"OTS_CSOVS.selByCustIdAndBrNoWithoutClose",
				new String[] { brnNo, custId }); //

		// /*
		// TEST
		// return this.getJdbc().queryForList("rpt.LMS180R18T",
		// new String[] { },0,Integer.MAX_VALUE); //bgnDate, endDate, areaNo
		// */
	}

	@Override
	public List<Map<String, Object>> getGuarOnLineAccount(String custId) {
		return this.getJdbc().queryForListWithMax("IDDP0001.getAccount",
				new Object[] { custId });
	}

	@Override
	public List<Map<String, Object>> getAccount_laborContract(String custId) {
		return this.getJdbc().queryForListWithMax(
				"IDDP0001.getAccount_laborContract", new Object[] { custId });
	}

	@Override
	public List<Map<String, Object>> getAccount_laborContract(String custId,
			String dupNo) {
		return this.getJdbc().queryForListWithMax(
				"IDDP0001.getAccount_laborContract_2",
				new Object[] { custId, dupNo });
	}

	/**
	 * 
	 * 
	 * 抓是否屬凍結額度國家
	 *
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findDW_CTYRKANYByType2() {

		return this.getJdbc().queryForListWithMax("DW_CTYRKANY.selLastType2",
				new Object[] {});
	}

	/**
	 * J-109-0328_05097_B1001 Web e-loan企金授信額度明細表中小信保增加借款人非屬獨資或合夥之徵提連帶保證人檢核。
	 * 
	 * @param custId
	 * @return
	 */
	@Override
	public Map<String, Object> findOTS_DW_BGMOPENByIdNo(String custId) {
		return this.getJdbc().queryForMap("OTS_DW_BGMOPEN.selByIdNo",
				new Object[] { custId });
	}

	/**
	 * J-109-0369_05097_B1001 e-Loan企金授權外簽報書引進國家風險限額資料
	 * 
	 * @param cntryCdStr
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findOTS_DW_CNTRY_C1ByCntryCd(
			String... cntryCdStr) {

		String cntryParam = Util.genSqlParam(cntryCdStr);
		return this.getJdbc().queryForListByCustParam(
				"OTS_DW_CNTRY_C1.selByCntryCd", new Object[] { cntryParam },
				cntryCdStr);
	}

	/**
	 * M-110-0227 資料倉儲提供fptinsrt table至DWOTS供eloan授信簽報利率合理性分析引用
	 */
	public List<Map<String, Object>> findOTS_FTPINSRTByCurr(String currs) {

		return this.getJdbc().queryForList("OTS_FTPINSRT.selByCurr",
				new String[] { currs, currs }, 0, Integer.MAX_VALUE);
	}

	/**
	 * J-112-0281_05097_B1001 Web
	 * e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
	 */
	public List<Map<String, Object>> findOTS_FTPINSRTByCurr_lastMonth(
			String currs) {
		return this.getJdbc().queryForList("OTS_FTPINSRT.selByCurr.lastMonth",
				new String[] { currs, currs }, 0, Integer.MAX_VALUE);
	}

	/**
	 * 抓外部信評最新資料日期
	 * 
	 * @return
	 */
	@Override
	public Map<String, Object> findDW_BSL2ECRSCRMaxCycMn() {
		return this.getJdbc().queryForMap("DW_BSL2ECRSCR.selMaxCycMn",
				new Object[] {});
	}

	/**
	 * 抓外部信評資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cycMn
	 * @return
	 */
	@Override
	public Map<String, Object> findDW_BSL2ECRSCRByCustKeyAndCycMn(
			String custId, String dupNo, String cycMn) {

		if ("0".equals(Util.trim(dupNo))) {
			dupNo = "";
		}

		String fullCustKeyNew = Util.getLeftStr(Util.trim(custId)
				+ "          ", 10)
				+ dupNo;

		return this.getJdbc().queryForMap("DW_BSL2ECRSCR.selByCustKeyAndCycMn",
				new Object[] { fullCustKeyNew, cycMn });
	}

	/**
	 * 抓理財商品資料
	 */
	@Override
	public List<Map<String, Object>> findOTS_DW_LNWM_MNT_ById(String custId,
			String dupNo, String bgnDate) {
		if (Util.isEmpty(Util.nullToSpace(bgnDate))) {
			bgnDate = CapDate.getCurrentDate("yyyy-MM-dd");
		}
		return this.getJdbc().queryForList("OTS_DW_LNWM_MNT.selById",
				new Object[] { custId, dupNo, bgnDate }, 0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> findOTS_DW_LNWM_MNT_ByKey(String custId,
			String dupNo, String proType, String bankProCode, String accNo) {
		return this.getJdbc().queryForList("OTS_DW_LNWM_MNT.selByKey",
				new Object[] { custId, dupNo, proType, bankProCode, accNo }, 0,
				Integer.MAX_VALUE);
	}

	/**
	 * 抓已確認的理財商品資料
	 */
	@Override
	public List<Map<String, Object>> findOTS_DW_LNWM_CFM_ByUnid(String unid) {
		return this.getJdbc().queryForList("OTS_DW_LNWM_CFM.selByUnid",
				new Object[] { unid }, 0, Integer.MAX_VALUE);
	}

	@Override
	public List<Map<String, Object>> findOTS_DW_LNWM_CFM_ByKey(String unid,
			String custId, String dupNo, String proType, String bankProCode,
			String accNo) {
		return this.getJdbc()
				.queryForList(
						"OTS_DW_LNWM_CFM.selByKey",
						new Object[] { unid, custId, dupNo, proType,
								bankProCode, accNo }, 0, Integer.MAX_VALUE);
	}

	@Override
	public void OTS_DW_LNWM_CFM_delete(List<Object[]> dataList) {
		this.getJdbc().batchUpdate(
				"DWADM.OTS_DW_LNWM_CFM.delete",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR }, dataList);
	}

	@Override
	public void OTS_DW_LNWM_CFM_insert(List<Object[]> dataList) {
		this.getJdbc().batchUpdate(
				"DWADM.OTS_DW_LNWM_CFM.insert",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.DATE,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.DATE,
						Types.CHAR, Types.NUMERIC, Types.CHAR, Types.DATE,
						Types.CHAR, Types.NUMERIC, Types.CHAR, Types.CHAR,
						Types.NUMERIC, Types.DATE, Types.DATE }, dataList);
	}

	/**
	 * J-109-0496 貸後管理 理財商品贖回追蹤
	 */
	@Override
	public List<Map<String, Object>> queryDWLNWMCFMList() {
		return this.getJdbc().queryForListWithMax("DW_LNWM_CFM.selCFM",
				new Object[] {});
	}

	@Override
	public List<Map<String, Object>> queryDWLNWMCFMUNID(String CUST_ID,
			String CUST_DUP_NO, String PRO_TYPE, String BANK_PRO_CODE,
			String ACC_NO) {
		return this.getJdbc().queryForListWithMax(
				"DW_LNWM_CFM.selUnid",
				new Object[] { CUST_ID, CUST_DUP_NO, PRO_TYPE, BANK_PRO_CODE,
						ACC_NO });
	}

	@Override
	public void DW_LNWM_CFM_Update(String lstSellDt, String lstSellCurCd,
			BigDecimal lstSellAmt, String lstSellBrCd, String tranType,
			String dataDt, String bachDt, String unid, String custID,
			String custDupNo, String proType, String bankProCode, String accNo) {
		this.getJdbc().update(
				"DW_LNWM_CFM.Update",
				new Object[] { lstSellDt, lstSellCurCd, lstSellAmt,
						lstSellBrCd, tranType, dataDt, bachDt, unid, custID,
						custDupNo, proType, bankProCode, accNo });
	}

	@Override
	public List<Map<String, Object>> findDW_RKCREDIT_by_acct_key(String acct_key) {
		return this.getJdbc().queryForList(
				"DWADM.DW_RKCREDIT.select_by_acct_key",
				new String[] { acct_key });
	}

	@Override
	public List<Map<String, Object>> findDW_ASLNDNEWOVS_ByBrNoCustId(
			String brNo, String custId, String dupNo, String cntrNo,
			String loanNo) {
		List<Object> params = new ArrayList<Object>();
		String key = "";
		if ("0".equals(Util.trim(dupNo))) {
			key = custId;
		} else {
			key = custId + dupNo;
		}
		params.add(brNo);
		params.add(key);
		params.add(brNo);
		params.add(key);

		StringBuffer sql = new StringBuffer();
		if (Util.isNotEmpty(cntrNo)) {
			sql.append("  WHERE CONTRACT = ? ");
			params.add(cntrNo);
			if (Util.isNotEmpty(loanNo)) {
				sql.append(" AND LOAN_NO = ? ");
				params.add(loanNo);
			}
		}

		return this.getJdbc().queryForAllListByCustParam(
				"DWADM.OTS_ASLNDNEWOVS.selectByBrNoCustId",
				new Object[] { sql.toString() }, params.toArray(new Object[0]));
	}

	@Override
	public int trans_DW_RKtbl_old_to_new_cntrNo(String old_cntrNo,
			String new_cntrNo) {
		return this.getJdbc().update("DWADM.DW_RKCNTRNO.Update",
				new String[] { new_cntrNo, old_cntrNo });
	}

	@Override
	public void trans_DW_RKtbl_old_to_new_loanNo(String old_loanNo,
			String new_loanNo) {
		this.getJdbc().update("DWADM.DW_RKCNTRNO.convert_acct_key_old_to_new",
				new String[] { new_loanNo, old_loanNo });
		this.getJdbc().update("DWADM.DW_RKADJUST.convert_acct_key_old_to_new",
				new String[] { new_loanNo, old_loanNo });
		this.getJdbc().update(
				"DWADM.DW_RKAPPLICANT.convert_acct_key_old_to_new",
				new String[] { new_loanNo, old_loanNo });
		this.getJdbc().update("DWADM.DW_RKCREDIT.convert_acct_key_old_to_new",
				new String[] { new_loanNo, old_loanNo });
		this.getJdbc().update("DWADM.DW_RKJCIC.convert_acct_key_old_to_new",
				new String[] { new_loanNo, old_loanNo });
		this.getJdbc().update("DWADM.DW_RKPROJECT.convert_acct_key_old_to_new",
				new String[] { new_loanNo, old_loanNo });
		this.getJdbc().update("DWADM.DW_RKSCORE.convert_acct_key_old_to_new",
				new String[] { new_loanNo, old_loanNo });
	}

	@Override
	public List<Map<String, Object>> findDW_LNF273_by_cntrNo(String cntrNo) {
		return this.getJdbc().queryForList("DWADM.DW_LNF273.selectByCntrNo",
				new String[] { cntrNo, cntrNo });
	}

	@Override
	public void trans_OTS_CRDLN031_old_to_new_AREA_CD(String brNo,
			String old_areaNo, String new_areaNo) {
		this.getJdbc().update("OTS_CRDLN031.change_AREA_CD",
				new Object[] { new_areaNo, old_areaNo, brNo });
	}

	/**
	 * J-111-0052 修改借戶暨關係戶與本行往來實績彙總表 取得 OTS_HINS_ASCT 資料月數、最大資料日期、及半年前日期
	 */
	@Override
	public Map<String, Object> getOTS_HINS_ASCT_maxCycMn() {
		return this.getJdbc().queryForMap("DWADM.OTS_HINS_ASCT.selMaxMN",
				new Object[] {});
	}

	/**
	 * J-111-0052 修改借戶暨關係戶與本行往來實績彙總表 取得 OTS_HINS_ASCT 最新統計資料
	 */
	@Override
	public Map<String, Object> getOTS_HINS_ASCT_data(String fullCustId,
			Date queryDate) {
		return this.getJdbc().queryForMap("DWADM.OTS_HINS_ASCT.selByCustId",
				new Object[] { fullCustId, queryDate });
	}

	/**
	 * J-111-0052 修改借戶暨關係戶與本行往來實績彙總表 取得 OTS_HINS_ASCT 近半年總數
	 */
	@Override
	public Map<String, Object> getOTS_HINS_ASCT_sumData(String fullCustId,
			Date beginDate, Date endDate) {
		return this.getJdbc().queryForMap("DWADM.OTS_HINS_ASCT.selSumByCustId",
				new Object[] { fullCustId, beginDate, endDate });
	}

	@Override
	public void DW_L120S21A_delete(String mainId) {
		this.getJdbc().update("DWADM.DW_L120S21A.delete",
				new Object[] { mainId });
	}

	@Override
	public void DW_L120S21A_insert(String UNID, String PROJECT_NO,
			String DATA_DATE, String STATUS, String STATUS_DT, String END_DT,
			String BR_NO, String CONTRACT_CO, String SWFT,
			BigDecimal FACT_AMT_CO, BigDecimal FACT_AMT_CO_NT, String CONTRACT,
			BigDecimal FACT_AMT_T, BigDecimal BAL_T, BigDecimal INT_AMT_T,
			BigDecimal FINAL_EAD, String REUSE) {

		this.getJdbc().update(
				"DWADM.DW_L120S21A.insert",
				new Object[] { UNID, PROJECT_NO, DATA_DATE, STATUS, STATUS_DT,
						END_DT, BR_NO, CONTRACT_CO, SWFT, FACT_AMT_CO,
						FACT_AMT_CO_NT, CONTRACT, FACT_AMT_T, BAL_T, INT_AMT_T,
						FINAL_EAD, REUSE });
	}

	@Override
	public void DW_L120S21B_delete(String mainId) {
		this.getJdbc().update("DWADM.DW_L120S21B.delete",
				new Object[] { mainId });
	}

	@Override
	public void DW_L120S21B_insert(String UNID, String PROJECT_NO,
			String CUSTID, String DATA_DATE, String STATUS, String STATUS_DT,
			String END_DT, BigDecimal LGD_MOWVER1, BigDecimal LGD_MOWVER2,
			BigDecimal EAD_MOWVER1, BigDecimal EAD_MOWVER2, String BR_NO,
			String CONTRACT, String CREDIT_FLAG, String GUR_FLAG,
			String UNION_FLAG, BigDecimal SYND_AMT_NT, BigDecimal UNION_AMT_NT,
			String FACT_CURR, BigDecimal FACT_AMT, BigDecimal FACT_AMT_T,
			BigDecimal BAL_T, BigDecimal INT_AMT_T, BigDecimal FINAL_EAD,
			BigDecimal PRO_R, BigDecimal COLL_R, BigDecimal CREDIT_R,
			BigDecimal UNSEC_R, BigDecimal EXP_R, BigDecimal UNDIR_COST,
			BigDecimal WORKOUT_LGD, BigDecimal WORKOUT,
			BigDecimal RESTRUCTURE_LGD, BigDecimal RESTRUCTURE,
			BigDecimal CURE_LGD, BigDecimal CURE, BigDecimal FACT_LGD,
			BigDecimal CUST_LGD, BigDecimal CREDIT_PT, String BUSS_TYPE,
			BigDecimal BUSS_LGD, String AUTH_TYPE,
			String ISGUARANTOREFFECT_S21B, String GUARANTORID_S21B,
			String GUARANTORDUPNO_S21B, String GUARANTORRNAME_S21B,
			String GUARANTORCRDTYPE_S21B, String GUARANTORCRDTYEAR_S21B,
			String GUARANTORGRADEORG_S21B, String GUARANTORGRADENEW_S21B,
			String GUARANTORCPTLCURR_S21B, BigDecimal GUARANTORCPTLAMT_S21B,
			BigDecimal GUARANTORCPTLUNIT_S21B, String GUARANTORNTCODE_S21B,
			String GUARANTORSTOCKSTATUS_S21B, String GUARANTORSTOCKNUM_S21B,
			String GUARANTORCRDTYPE2_S21B, String GUARANTORCRDTYEAR2_S21B,
			String GUARANTORGRADEORG2_S21B, String GUARANTORGRADENEW2_S21B,
			String GUARANTORSTKCATNM_S21B, String GUARANTORCRDTYPE3_S21B,
			String GUARANTORCRDTYEAR3_S21B, String GUARANTORGRADEORG3_S21B,
			String GUARANTORGRADENEW3_S21B, String GUARANTORSTKCATNM3_S21B) {

		// J-111-0083_05097_B1002 Web
		// e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類

		// J-113-0102_05097_B1001 修改e-Loan LGD之公司保證回收率估算規則

		this.getJdbc().update(
				"DWADM.DW_L120S21B.insert",
				new Object[] { UNID, PROJECT_NO, CUSTID, DATA_DATE, STATUS,
						STATUS_DT, END_DT, LGD_MOWVER1, LGD_MOWVER2,
						EAD_MOWVER1, EAD_MOWVER2, BR_NO, CONTRACT, CREDIT_FLAG,
						GUR_FLAG, UNION_FLAG, SYND_AMT_NT, UNION_AMT_NT,
						FACT_CURR, FACT_AMT, FACT_AMT_T, BAL_T, INT_AMT_T,
						FINAL_EAD, PRO_R, COLL_R, CREDIT_R, UNSEC_R, EXP_R,
						UNDIR_COST, WORKOUT_LGD, WORKOUT, RESTRUCTURE_LGD,
						RESTRUCTURE, CURE_LGD, CURE, FACT_LGD, CUST_LGD,
						CREDIT_PT, BUSS_TYPE, BUSS_LGD, AUTH_TYPE,
						ISGUARANTOREFFECT_S21B, GUARANTORID_S21B,
						GUARANTORDUPNO_S21B, GUARANTORRNAME_S21B,
						GUARANTORCRDTYPE_S21B, GUARANTORCRDTYEAR_S21B,
						GUARANTORGRADEORG_S21B, GUARANTORGRADENEW_S21B,
						GUARANTORCPTLCURR_S21B, GUARANTORCPTLAMT_S21B,
						GUARANTORCPTLUNIT_S21B, GUARANTORNTCODE_S21B,
						GUARANTORSTOCKSTATUS_S21B, GUARANTORSTOCKNUM_S21B,
						GUARANTORCRDTYPE2_S21B, GUARANTORCRDTYEAR2_S21B,
						GUARANTORGRADEORG2_S21B, GUARANTORGRADENEW2_S21B,
						GUARANTORSTKCATNM_S21B, GUARANTORCRDTYPE3_S21B,
						GUARANTORCRDTYEAR3_S21B, GUARANTORGRADEORG3_S21B,
						GUARANTORGRADENEW3_S21B, GUARANTORSTKCATNM3_S21B });
	}

	@Override
	public void DW_L120S21C_delete(String mainId) {
		this.getJdbc().update("DWADM.DW_L120S21C.delete",
				new Object[] { mainId });
	}

	@Override
	public void DW_L120S21C_insert(String UNID, String PROJECT_NO,
			String DATA_DATE, String STATUS, String STATUS_DT, String END_DT,
			String BR_NO, String CONTRACT, String COLKIND, String COLCURR,
			BigDecimal COLTIMEVALUE, BigDecimal COLPRERGSTAMT,
			BigDecimal COLRGSTAMT, String COLCOUSEFLAG,
			BigDecimal COLSHARERATE, BigDecimal COLRATE,
			BigDecimal COLESTRECOVERY, BigDecimal COLRGSTRECOVERY,
			BigDecimal COLRECOVERY, BigDecimal COLRECOVERYTWD,
			String CMSBRANCH, String CMSTYPECD, String CMSCUSTID,
			String CMSDUPNO, String CMSCOLLNO, String CMSOID,
			String CMSCOLLKEY, String COLLTYPE, BigDecimal CMSGRTRT,
			String UNIONFLAG_S21C, String UNIONCURR_S21C,
			BigDecimal SYNDAMT_S21C, BigDecimal UNIONAMT_S21C) {

		this.getJdbc().update(
				"DWADM.DW_L120S21C.insert",
				new Object[] { UNID, PROJECT_NO, DATA_DATE, STATUS, STATUS_DT,
						END_DT, BR_NO, CONTRACT, COLKIND, COLCURR,
						COLTIMEVALUE, COLPRERGSTAMT, COLRGSTAMT, COLCOUSEFLAG,
						COLSHARERATE, COLRATE, COLESTRECOVERY, COLRGSTRECOVERY,
						COLRECOVERY, COLRECOVERYTWD, CMSBRANCH, CMSTYPECD,
						CMSCUSTID, CMSDUPNO, CMSCOLLNO, CMSOID, CMSCOLLKEY,
						COLLTYPE, CMSGRTRT, UNIONFLAG_S21C, UNIONCURR_S21C,
						SYNDAMT_S21C, UNIONAMT_S21C });
	}

	@Override
	public List<Map<String, Object>> findlnf025OvsCoByCntrNoForLgd(
			List<String> cntrNos) {
		String cntrNosParams = Util.genSqlParam(cntrNos);
		// StringBuilder sb = new StringBuilder("'");
		// for (String cntrNo : cntrNos) {
		// sb.append(sb.length() > 1 ? "','" : "").append(cntrNo);
		// }
		// sb.append("'");
		// ~~~
		return this.getJdbc().queryForAllListByCustParam(
				"DW_LNF025OVS.selCoByCntrNoForLgd",
				new String[] { cntrNosParams }, cntrNos.toArray(new String[0]));

	}

	@Override
	public List<Map<String, Object>> findSumDW_ASLNDAVGOVSByCntrNo(String cntrNo) {
		return this.getJdbc().queryForListWithMax(
				"DW_ASLNDAVGOVS.SUM.INT_AMT_T", new Object[] { cntrNo });
	}

	@Override
	public List<Map<String, Object>> find_OTS_TRPAYLG(String custId,
			String dupNo, String begDate, String endDate) {
		return this.getJdbc().queryForListWithMax(
				"DW_OTS_TRPAYLG.QUERY",
				new String[] { Util.addSpaceWithValue(custId, 10) + "%",
						begDate, endDate });
	}

	@Override
	public List<Map<String, Object>> findlnf025OvsByCntrNoCo(String cntrNoCo) {
		return this.getJdbc().queryForListWithMax("DW_LNF025OVS.selByCntrNoCo",
				new String[] { cntrNoCo });
	}

	@Override
	public List<Map<String, Object>> findOTS_ACINSRTByColumn(String CYC_DT,
			String BR_CD, String DATA_DEF, String CUR_CD, String DW_DEP_CD,
			String DATA_TYP) {
		return this.getJdbc().queryForListWithMax(
				"OTS_ACINSRT.findOTS_ACINSRTByColumn",
				new String[] { CYC_DT, BR_CD, DATA_DEF, CUR_CD, DW_DEP_CD,
						DATA_TYP });

	}

	/**
	 * J-111-0551 取得客戶之海外額度序號(含已銷戶)
	 */
	@Override
	public List<String> findDWLnquotovCntrnoByCustId(String custId) {
		List<Map<String, Object>> rows = this.getJdbc().queryForListWithMax(
				"DWADM.DW_LNQUOTOV.DW_LNQUOTOV_C.selByCustId",
				new String[] { custId, custId, custId, custId });

		List<String> list = new ArrayList<String>();
		for (Map<String, Object> data : rows) {
			String LINE_NO = Util.trim(MapUtils.getString(data, "LINE_NO"));
			if (Util.isNotEmpty(LINE_NO)) {
				list.add(LINE_NO);
			}
		}
		return list;
	}

	/**
	 * J-111-0551 確認額度序號不存在於海外額度檔
	 */
	@Override
	public List<String> findDWLnquotovWithoutCntrno(List<String> cntrnoList) {
		List<String> sql = new ArrayList<String>();

		List<String> custQuery = new ArrayList<String>();
		List<String> custParam = new ArrayList<String>();

		if (cntrnoList != null && cntrnoList.size() > 0) {
			for (String cntrNo : cntrnoList) {
				sql.add(" SELECT ? FROM SYSIBM.SYSDUMMY1 ");
				custParam.add(cntrNo);
			}
		} else {
			return new ArrayList<String>();
		}

		custQuery.add(StringUtils.join(sql, " UNION ALL "));

		List<Map<String, Object>> rows = this.getJdbc()
				.queryForAllListByCustParam(
						"DW_LNCNTROVS.selByCntrNoAndElLineNo",
						custQuery.toArray(), custParam.toArray());

		List<String> list = new ArrayList<String>();
		for (Map<String, Object> data : rows) {
			String LINE_NO = Util.trim(MapUtils.getString(data, "CNTRNO"));
			if (Util.isNotEmpty(LINE_NO)) {
				list.add(LINE_NO);
			}
		}
		return list;
	}

	/**
	 * J-111-0443_05097_B1002 Web e-Loan企金授信開發授信BIS評估表
	 */
	@Override
	public List<Map<String, Object>> findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE_None_Loan(
			String custKey, String dupNo, String dateS, String dateE) {
		String likeCustKey = Util.getLeftStr(custKey + "          ", 10) + "%";

		// 授信
		String[] type1 = new String[] { "NTLN", "FXLN", "NTGA", "FXGA", "NTBA",
				"FXBA", "NTFA", "NTFB", "FXFA", "FXFB", "NTFI", "NTFJ", "FXFJ",
				"FXFI" };

		String sqlParams = Util.genSqlParam(type1);
		List<Object> params = new ArrayList<Object>();
		params.addAll(Arrays.asList(type1));
		params.add(likeCustKey);
		params.add(dateS);
		params.add(dateE);

		return this.getJdbc().queryForListByCustParam(
				"DM_CUBCPCM.selTOTAL_ATTRIBUTE_None_Loan",
				new Object[] { sqlParams }, params.toArray(new Object[0]));

	}

	/**
	 * J-111-0443_05097_B1002 Web e-Loan企金授信開發授信BIS評估表
	 */
	@Override
	public List<Map<String, Object>> findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE_None_Loan(
			String custKey, String dupNo, String dateS, String dateE) {
		String likeCustKey = Util.getLeftStr(custKey + "          ", 10) + "%";

		// 授信
		String[] type1 = new String[] { "NTLN", "FXLN", "NTGA", "FXGA", "NTBA",
				"FXBA", "NTFA", "NTFB", "FXFA", "FXFB", "NTFI", "NTFJ", "FXFJ",
				"FXFI" };

		String sqlParams = Util.genSqlParam(type1);
		List<Object> params = new ArrayList<Object>();
		params.addAll(Arrays.asList(type1));
		params.add(likeCustKey);
		params.add(dateS);
		params.add(dateE);

		return this.getJdbc().queryForListByCustParam(
				"DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE_None_Loan",
				new Object[] { sqlParams }, params.toArray(new Object[0]));
	}

	/**
	 * 取得表外科目CCF
	 * 
	 * @param acKey
	 * @return
	 */
	@Override
	public List<Map<String, Object>> find_OTS_BSL2ACRULE_byAcKey(String acKey) {
		return this.getJdbc().queryForListWithMax("OTS_BSL2ACRULE.selByAcKey",
				new String[] { acKey });
	}

	@Override
	public List<Map<String, Object>> findOtsCustInfoBy(
			List<String> contentList, String custId, String dupNo) {

		if (contentList == null || contentList.isEmpty()) {
			return null;
		}

		int size = contentList.size();
		String questionMarkStr = Util.genSqlParam(contentList);
		Object[] params = new Object[size + 1];

		int i = 0;
		for (String content : contentList) {
			params[i] = content;
			i++;
		}

		params[i++] = custId + dupNo;

		return this.getJdbc().queryForListByCustParam(
				"DW_OTS_CUST_INFO.selectByDataTypeAndContent",
				new Object[] { questionMarkStr }, params);
	}

	@Override
	public void DW_ELOAN_APPLY_DELETE(String[] c122m01aOidArry_toDelete) {
		if (c122m01aOidArry_toDelete == null
				|| c122m01aOidArry_toDelete.length == 0) {
			return;
		} else {
			int limit = 10000;// 如果筆數太多，每次只跑一萬筆
			int tmp = 0;

			ArrayList<List<String>> all = new ArrayList<List<String>>();
			List<String> sub = null;
			// 將傳進來的oid分批到不同陣列存起來
			while (tmp < c122m01aOidArry_toDelete.length) {
				if (tmp % limit == 0) {
					sub = new ArrayList<String>();
					all.add(sub);
				}
				sub.add(c122m01aOidArry_toDelete[tmp]);
				tmp++;
			}

			for (List<String> oids : all) {
				String sqlParam = Util.genSqlParam(oids);// 產生?號字串
				this.getJdbc().updateByCustParam("DWADM.DW_ELOAN_APPLY.delete",
						new Object[] { sqlParam }, oids.toArray(new String[0]));
			}
		}
	}

	@Override
	public void DW_ELOAN_APPLY_INSERT(List<Object[]> valuesList) {
		if (valuesList == null || valuesList.size() == 0) {
			return;
		} else {
			int limit = 10000;// 如果筆數太多，每次只跑一萬筆
			int tmp = 0;

			ArrayList<List<Object[]>> all = new ArrayList<List<Object[]>>();
			List<Object[]> sub = null;
			while (tmp < valuesList.size()) {
				if (tmp % limit == 0) {
					sub = new ArrayList<Object[]>();
					all.add(sub);
				}
				sub.add(valuesList.get(tmp));
				tmp++;
			}

			for (List<Object[]> valuesSubList : all) {
				this.getJdbc().batchUpdate("DWADM.DW_ELOAN_APPLY.insert", new int[] {}, valuesSubList);
			}
		}
	}

	@Override
	public void DW_ELOAN_REQUEST_DELETE(String[] l120m01aOidArry_toDelete) {
		if (l120m01aOidArry_toDelete == null
				|| l120m01aOidArry_toDelete.length == 0) {
			return;
		} else {
			int limit = 10000;// 如果筆數太多，每次只跑一萬筆
			int tmp = 0;

			ArrayList<List<String>> all = new ArrayList<List<String>>();
			List<String> sub = null;
			// 將傳進來的oid分批到不同陣列存起來
			while (tmp < l120m01aOidArry_toDelete.length) {
				if (tmp % limit == 0) {
					sub = new ArrayList<String>();
					all.add(sub);
				}
				sub.add(l120m01aOidArry_toDelete[tmp]);
				tmp++;
			}

			for (List<String> oids : all) {
				String sqlParam = Util.genSqlParam(oids);// 產生?號字串
				this.getJdbc().updateByCustParam(
						"DWADM.DW_ELOAN_REQUEST.delete",
						new Object[] { sqlParam }, oids.toArray(new String[0]));
			}
		}
	}

	@Override
	public void DW_ELOAN_REQUEST_INSERT(List<Object[]> valuesList) {
		if (valuesList == null || valuesList.size() == 0) {
			return;
		} else {
			int limit = 10000;// 如果筆數太多，每次只跑一萬筆
			int tmp = 0;

			ArrayList<List<Object[]>> all = new ArrayList<List<Object[]>>();
			List<Object[]> sub = null;
			while (tmp < valuesList.size()) {
				if (tmp % limit == 0) {
					sub = new ArrayList<Object[]>();
					all.add(sub);
				}
				sub.add(valuesList.get(tmp));
				tmp++;
			}

			for (List<Object[]> valuesSubList : all) {
				this.getJdbc().batchUpdate("DWADM.DW_ELOAN_REQUEST.insert", new int[] {}, valuesSubList);
			}
		}
	}
	
	@Override
	public Map<String, Object> findDwLncntrovsByCntrNo(String cntrNo) {
		
		return this.getJdbc().queryForMap(
				"DWADM.DW_LNCNTROVS.selectByCntrNo", new Object[] { cntrNo });
	}

    @Override
    public List<Map<String, Object>> findDW_ESGLOANLIST_SSByType(String type) {
		return this.getJdbc().queryForList("DW_ESGLOANLIST_SS_"+type, null);
    }

    /**
     * J-113-0402 依本行「企業授信ESG風險評級作業須知」第七條，為利追蹤ESG風險評級辦理頻率，新增貸後通知及報表(LLDLN350.LLWLN350)內容如下：
     * 1.請每月於授信管理系統「貸後管理追蹤檢核表」，針對ESG風險評級即將屆期之案件進行通知(中、低ESG風險至少每五年辦理一次、高ESG風險至少每年辦理一次，例如中風險最近一次評等日期112.12.10，屆期日為117.12.10，貸後通知117.11月)，且於本行尚有有效額度者，發送額度最大分行，通知分行重新辦理評級。
     * 2.追蹤事項通知內容提示「ESG風險評級即將屆期，請重新辦理ESG風險評級」。
     *
     * @param foDate
     * @return
     */
    @Override
    public List<Map<String, Object>> findDW_ESGLOANLIST_SSByRiskRating(String foDate) {
        java.sql.Date date = null;
        try {
            date = new java.sql.Date(new SimpleDateFormat("yyyy-MM-dd").parse(foDate).getTime());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return this.getJdbc().queryForList( "DW_ESGLOANLIST_SSByRiskRating", new Object[] { date, date });
    }
}
