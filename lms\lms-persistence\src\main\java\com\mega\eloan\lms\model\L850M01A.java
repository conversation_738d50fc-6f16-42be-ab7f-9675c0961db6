/* 
 * L850M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 資料上傳作業主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L850M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L850M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * 申請類別
	 * <p/>
	 * 風控處_自有資本與風險性資產預估表
	 */
	@Size(max = 3)
	@Column(name = "APPCODE", length = 3, columnDefinition = "VARCHAR(3)")
	private String appCode;

	/**
	 * 版本
	 * <p/>
	 * 配合appCode
	 */
	@Size(max = 3)
	@Column(name = "VERSION", length = 3, columnDefinition = "VARCHAR(3)")
	private String version;

	/**
	 * 上傳檔案mainId
	 * <p/>
	 * 可以透過excel上傳，或是輸入資料上傳，此欄位為非必填
	 */
	@Size(max = 32)
	@Column(name = "DOCFILEMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String docFileMainId;

	/**
	 * 取得申請類別
	 * <p/>
	 * 風控處_自有資本與風險性資產預估表
	 */
	public String getAppCode() {
		return this.appCode;
	}

	/**
	 * 設定申請類別
	 * <p/>
	 * 風控處_自有資本與風險性資產預估表
	 **/
	public void setAppCode(String value) {
		this.appCode = value;
	}

	/** 取得版本 */
	public String getVersion() {
		return this.version;
	}

	/** 設定版本 **/
	public void setVersion(String value) {
		this.version = value;
	}

	/**
	 * 取得上傳檔案mainId
	 * <p/>
	 * 可以透過excel上傳，或是輸入資料上傳，此欄位為非必填
	 */
	public String getDocFileMainId() {
		return this.docFileMainId;
	}

	/**
	 * 設定上傳檔案mainId
	 * <p/>
	 * 可以透過excel上傳，或是輸入資料上傳，此欄位為非必填
	 **/
	public void setDocFileMainId(String value) {
		this.docFileMainId = value;
	}
}
