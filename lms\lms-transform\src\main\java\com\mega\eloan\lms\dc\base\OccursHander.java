package com.mega.eloan.lms.dc.base;

import java.math.BigDecimal;

import com.mega.eloan.lms.dc.util.Util;

public class OccursHander {
	private String occursFields;
	private String occursKeys;
	private boolean keyOccurs;
	private int occursTimes = 1;
	private int occursTimesDef = 1;

	public OccursHander(String occursFields, String occursKeys,
			int occursTimesDef) {
		this.occursFields = occursFields;
		this.occursKeys = occursKeys;
		this.occursTimes = occursTimesDef;
		this.occursTimesDef = occursTimesDef;
	}

	public int getOccursValue(String times) {
		BigDecimal ts = Util.parseToBigDecimal(times);
		if (ts.compareTo(BigDecimal.ZERO) > 0) {
			this.occursTimes = ts.intValue();
		} else {
			this.occursTimes = this.occursTimesDef;
		}
		return this.occursTimes;
	}

	public String chkOccurs(String strName, int occurs) {
		this.keyOccurs = false;
		if (this.occursFields == null || this.occursFields.length() == 0) {
			return strName;
		}
		if (strName == null || strName.length() == 0) {
			return strName;
		}
		String str = strName.trim() + ";";
		int idx = this.occursFields.indexOf(str);
		if (idx >= 0) {
			idx = this.occursKeys.indexOf(str);
			if (idx >= 0) {
				this.keyOccurs = true;
			}
			if (strName.trim().indexOf("?") > -1) {
				return strName.trim().replaceAll("\\?", String.valueOf(occurs));
			}
			return strName.trim() + occurs;
		}
		return strName;
	}

	public boolean isKeyOccurs() {
		return keyOccurs;
	}

}
