<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="
http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-2.0.xsd">

	<util:map id="tejSql" map-class="java.util.HashMap" key-type="java.lang.String">
		<entry key="crmtab.byStockNo"><!--20180810,增加tcri<>''條件，因空白為尚未評等-->
			<value><![CDATA[select * from dbo.CRMTAB where co_id=? and tcri<>'' order by yymm desc]]></value>
		</entry>
		<entry key="crmstd.byInvoice">
			<value>select * from dbo.CRMSTD where invoice=?</value>
		</entry>
		<entry key="usageLog.byUserIdRemark">
			<value><![CDATA[select * from dbo.meb_usage_log where USER_ID=? AND REMARK=? AND DATEDIFF(day,ACTIVITY_DT, GETDATE()) < 30 AND PAGE_URL='tcriSummary.jsp' AND ACTIVITY_TYPE='C']]></value>
		</entry>
		<entry key="crmstd.findAllTCRIRatingDataByCoId">
			<value><![CDATA[SELECT emm, invoice, co_id, jcic, name8, name_f_chi, mkt, yymm_tcri, ad_tcri, crmtcri, ind, indnm, tejind, tejindnm, tseind, tseindnm, sics, sicsnm 
						    FROM crmstd
						    where co_id > ?
						    Order by co_id]]>
			</value>
		</entry>
		<entry key="crmstd.findCompanyGreatEventDataByCoIdAndYymmddAndSeq">
			<value><![CDATA[SELECT b.invoice, a.co_id, a.yymmdd, a.xno, a.seq, a.rmk FROM crqtrmk a join crmstd b on a.co_id = b.co_id
									where  a.co_id = ? and a.yymmdd = ? and cast(a.seq as int) > ? order by  a.co_id, a.yymmdd, a.seq]]>
			</value>  
		</entry>
		<entry key="crmstd.findCompanyGreatEventDataGroupByCoIdAndYymmdd">
			<value><![CDATA[SELECT a.co_id, a.yymmdd FROM crqtrmk a join  ( select ltrim(rtrim(co_id)) as co_id, ltrim(rtrim(yymmdd)) as yymmdd from crqtrmk) as b on a.co_id = b.co_id and a.yymmdd = b.yymmdd
								where b.co_id + b.yymmdd > ? group by a.co_id, a.yymmdd order by a.co_id, a.yymmdd]]>
			</value>
		</entry>
        <entry key="crmstd.findPub_da2ByCoid">
            <value><![CDATA[select pub_da2 from crmstd where co_id=? AND pub_da2 <> '']]>
            </value>
        </entry>
        <entry key="crmstd.findCrmstdAllByPubDate2IsEmpty">
            <value><![CDATA[select * from crmstd where pub_da2 <> '' order by co_id]]>
            </value>
        </entry>

	</util:map>
</beans>