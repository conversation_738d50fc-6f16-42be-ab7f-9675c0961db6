/* 
 * L999LOG01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L999LOG01A;

/** 額度進階查詢記錄檔 **/
public interface L999LOG01ADao extends IGenericDao<L999LOG01A> {

	L999LOG01A findByOid(String oid);

	L999LOG01A findByMainId(String mainId);

	List<L999LOG01A> findByCreatorAndItemType(String Creator, String itemType);

	public L999LOG01A findLatestByCreatorAndItemType(String creator,
			String itemType);
}