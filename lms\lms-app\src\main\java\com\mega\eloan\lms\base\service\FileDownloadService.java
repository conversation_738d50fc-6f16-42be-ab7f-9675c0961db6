package com.mega.eloan.lms.base.service;

import java.io.FileNotFoundException;
import java.io.IOException;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;

import tw.com.iisi.cap.exception.CapException;

public interface FileDownloadService {

	/**
	 * 取得檔案下載的資料內容
	 * 
	 * @param params
	 *            PageParameters
	 * @return 資料內容
	 * @throws Exception 
	 * @throws IOException 
	 * @throws ReportException 
	 * @throws FileNotFoundException 
	 */
	public byte[] getContent(PageParameters params) throws CapException, FileNotFoundException, ReportException, IOException, Exception ;

}
