/* 
 * MisIquotjonServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Types;
import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisIquotgurService;

/**
 * <pre>
 * 保證人檔 IQUOTGUR(MIS.ELV42101)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
@Service
public class MisIquotgurServiceImpl extends AbstractMFAloanJdbc implements
		MisIquotgurService {

	@Override
	public void delByCntrNo(String args) {
		this.getJdbc().update("IQUOTGUR.delByCntrNo", new Object[] { args });

	}

	@Override
	public void insert(List<Object[]> DateList) {
		this.getJdbc().batchUpdate(
				"IQUOTGUR.insert",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR }, DateList);

	}

//	@Override
//	public void insertAllTest(String delCntrNo, List<Object[]> DateList,
//			List<Object[]> DateList2) {
//		this.getJdbc().updateByCustParam("IQUOTGUR.delByCntrNo2",
//				new Object[] { delCntrNo }, null);
//		this.getJdbc().updateByCustParam("IQUOTJON.delByCntrNo2",
//				new Object[] { delCntrNo }, null);
//
//		this.getJdbc().batchUpdate(
//				"IQUOTGUR.insert",
//				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
//						Types.CHAR }, DateList);
//
//		this.getJdbc().batchUpdate(
//				"IQUOTJON.insert",
//				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
//						Types.CHAR }, DateList2);
//
//	}
}
