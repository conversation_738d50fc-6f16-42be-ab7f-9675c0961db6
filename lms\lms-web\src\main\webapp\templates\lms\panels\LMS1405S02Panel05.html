<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
    	<th:block th:fragment="LMS1405S02Panel05">
    		<div id="tabs-d" class="tabs">
				<ul>
					<li><a href="#tabs-c03_1"><b><th:block th:text="#{'L140S02Tab.5_01'}">擔保品內容</th:block></b></a></li>
					<li><a href="#tabs-c03_2"><b><th:block th:text="#{'L140S02Tab.5_02'}">擔保品敘述</th:block></b></a></li>
					<th:block th:if="${_panelQualitativeFactor1_visible}">
						<li><a href="#tabs-c03_3"><b><th:block th:text="#{'L140S02Tab.5_03'}">引入評等質化內容</th:block></b></a></li>
					</th:block>
				</ul>
				<div class="tabCtx-warp">
					<div id="tabs-c03_1" class="content">
						<form id="L140M01AForm5" name="L140M01AForm5">
							<table class="tb2" width="100%" border="1" cellpadding="0" cellspacing="0">
								<tr class="hd1" style="text-align:left">
									<td colspan="2">
										<button id="inculdeCMS" type="button">
											<span class="text-only">
												<th:block th:text="#{'btn.cms'}">引進擔保品</th:block>
											</span>
										</button>
										<button id="deleteCMS" type="button">
											<span class="text-only">
												<th:block th:text="#{'button.delete'}">刪除</th:block>
											</span>
										</button>
									</td>
								</tr>
								<tr>
									<td colspan="2">
										<div id="gridviewCollateral"></div>
									</td>
								</tr>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'L140M01A.realEstate'}">(不動產)</th:block>
										<th:block th:text="#{'l1405s02c01.022'}"><!--核貸成數--></th:block><br>
										<th:block th:text="#{'L140M01A.equalToLTV'}">即LTV</th:block>
									</td>
									<td>
										<input type="text" name="approvedPercent" id="approvedPercent" class="numeric" positiveonly="false" integer="3" fraction="2" maxlength="6" size="6"></input>％
										<span class="text-red">
											<th:block th:text="#{'L140M01A.approvedPercent.formula'}">【現請額度+本案擔保品借款餘額(循環用額度、不循環用餘額)】/ 不動產購價或時價取孰低</th:block>
										</span>
									</td>
								</tr>
								<tr id="expectModelKindTr" style="display:none;">
									<td class="hd1">
										<th:block th:text="#{'L140M01A.expectModelKind'}">LGD選用模型</th:block>
									</td>
									<td>
										<label>
											<input type="radio" id="expectModelKind" name="expectModelKind" value="1"></input>
											<th:block th:text="#{'L140M01A.housingLoan'}"><!--房貸--></th:block>
										</label>
										<label>
											<input type="radio" name="expectModelKind" value="2"></input>
											<th:block th:text="#{'L140M01A.nonHousingLoan'}"><!--非房貸--></th:block>
										</label>
									</td>
								</tr>
							</table>
						</form>
						<!--J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核-->
						<fieldset>
							<legend>
								<strong>
									<th:block th:text="#{'L140M01o.stockInfo'}"><!--擔保品股票基本資訊--></th:block>
								</strong>
							</legend>
							<table class="tb2" width="100%" border="1" cellpadding="0" cellspacing="0">
								<tr class="hd1" style="text-align:left">
									<td>
										<button id="inculdeCMSStock" type="button">
											<span class="text-only">
												<th:block th:text="#{'btn.applyCMSItem'}">引進前項擔保品建檔資料</th:block>
											</span>
										</button>
										<button id="addCMSStock" type="button">
											<span class="text-only">
												<th:block th:text="#{'button.add'}">新增</th:block>
											</span>
										</button>
										<button id="deleteCMSStock" type="button">
											<span class="text-only">
												<th:block th:text="#{'button.delete'}">刪除</th:block>
											</span>
										</button>
									</td>
								</tr>
								<tr>
									<td><div id="gridviewCollateralStock"></div></td>
								</tr>
							</table>
						</fieldset>
					</div>
					<div id="tabs-c03_2" class="content">
						<span type="text" id="toALoan3" class="field"></span>
						<table class="tb2" width="100%" border="1" cellpadding="0" cellspacing="0">
							<tr class="hd1" style="text-align:left">
								<td>
									<!-- J-106-0220-001 修改E-LOAN授信管理系統之OBU額度明細表有關「擔保品注意事項敘述」之位置-->
									<div class="text-red">
										※<th:block th:text="#{'L140M01a.message53'}"><!--〈OBU授信案〉下列物品應注意不得列為擔保科目，必須為副擔保--></th:block><br>
										<th:block th:text="#{'L140M01a.message54'}"><!--一、以非授信戶關係企業或個人提供之DBU外幣定存單、境內股票、不動產及其他有關新台幣資產為副擔保--></th:block><br>
										<th:block th:text="#{'L140M01a.message55'}"><!--二、以非授信戶本人之DBU新台幣定存單為副擔保--></th:block><br>
										<th:block th:text="#{'L140M01a.message56'}"><!--三、以新台幣組合式商品為副擔保--></th:block><br>
										<th:block th:text="#{'l140s02p05.008'}"><!--四、以授信戶其在大陸地區之資產、權益或其境內金融機構提供之保證函為副擔保--></th:block>
									</div>
									<span style="display:none" class="caseSpan">
										<label>
											<input id="tab05" type="checkbox" class="caseBox"></input>
											<th:block th:text="#{'button.modify'}"><!--修改--></th:block>
										</label>
									</span>
									<select id="pageNum3" name="pageNum3" class="nodisabled">
										<option value="0" selected="selected">
											<th:block th:text="#{'L140M01b.printMain'}"><!--印於主表--></th:block>
										</option>
										<option value="1">
											<th:block th:text="#{'L140M01b.print01'}"><!--印於附表(一)--></th:block>
										</option>
										<option value="2">
											<th:block th:text="#{'L140M01b.print02'}"><!--印於附表(二)--></th:block>
										</option>
										<option value="3">
											<th:block th:text="#{'L140M01b.print03'}"><!--印於附表(三)--></th:block>
										</option>
									</select><br>
									<button id="reloadCMS" type="button">
										<span class="text-only">
											<th:block th:text="#{'btn.retoContent'}">重新引進</th:block>
										</span>
									</button>
								</td>
							</tr>
							<tr>
								<td>
									<textarea cols="100" rows="10%" id="itemDscr3" name="itemDscr3" class="tckeditor" wicket:message="displayMessage:L140S02Tab.5" showType="b" preview="width:800;heigth:300"></textarea>
								</td>
							</tr>
						</table>
					</div><!-- end tabs-c03_2 -->
					<th:block th:if="${_panelQualitativeFactor2_visible}">
						<div id="tabs-c03_3" class="content">
							<button id="impCMSFromRatingDoc" type="button">
								<span class="text-only">
									<th:block th:text="#{'button.pullin'}">引進</th:block>
								</span>
							</button>
							<form id="QualitativeFactorForm">
								<div id="_panelQualitativeFactor" th:include="${panelName} :: ${panelFragmentName}"></div>
							</form>
						</div>
					</th:block>
				</div><!-- end tabCtx-warp -->
    		</div><!-- end tabs-d -->

			<div id="CMSBox" style="display:none;">
				<div id="CMSBoxDiv">
					<form id='queryCMSDataForm'>
						<table width="800px" class="tb2" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td colspan="2">
									<span class="text-red">
										<th:block th:text="#{'l140s02p05.001'}"><!--應予注意事項--></th:block>：<br>
										<th:block th:text="#{'l140s02p05.002'}"><!--徵提下列擔保品應注意，不得列為擔保科目：以非授信戶本人或關係企業之DBU外幣定存單--></th:block>、<br>
										<th:block th:text="#{'l140s02p05.003'}"><!--境內股票、不動產及其他有關新台幣資產為擔保，以非授信戶本人之DBU新台幣定存單為擔保--></th:block>，<br>
										<th:block th:text="#{'l140s02p05.004'}"><!--以授信戶本人或第三者之新台幣組合式商品為擔保，以授信戶其在大陸地區之資產、權益--></th:block><br>
										<th:block th:text="#{'l140s02p05.005'}"><!--或其境內金融機構提供之保證函為擔保--></th:block>
									</span>
								</td>
							</tr>
							<tr>
								<td width="30%" class="hd1">
									<th:block th:text="#{'L140M01a.branchId'}"><!--分行別--></th:block>&nbsp;&nbsp;
								</td>
								<td width="70%">
									<input type="text" id="selectFilterCMDBrno" name="selectFilterCMDBrno" maxlength="3" minlength="3" size="3" class="upText required"></input>
									<button type="button" id="selectCMSBranchBt">
										<span class="text-only">
											<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
										</span>
									</button>
								</td>
							</tr>
							<tr>
								<td class="hd1">
									<th:block th:text="#{'L140M01a.custId'}"><!--借款人統編--></th:block>
								</td>
								<td>
									<input type="text" id="cmsCustId" name="cmsCustId" size="10" maxlength="10" class="upText required"></input>
									<input type="text" id="cmsDupNo" name="cmsDupNo" size="1" maxlength="1" class="upText required"></input>
								</td>
							</tr>
							<tr>
								<td class="hd1">
									<th:block th:text="#{'l140s02p05.007'}"><!--擔保品種類--></th:block>&nbsp;&nbsp;
								</td>
								<td>
									<select id="cmsType" name="cmsType" comboKey="lmsUseCms_collTyp1" class="required"></select>
								</td>
							</tr>
							<tr>
								<td colspan="2">
									<button id="queryCMSData" type="button">
										<span class="text-only">
											<th:block th:text="#{'button.search'}"><!--查詢--></th:block>
										</span>
									</button>
								</td>
							</tr>
							<tr>
								<td colspan="2">
									<div id="gridviewSrcColl"></div>
								</td>
							</tr>
						</table>
					</form>
				</div>
			</div>

			<div id="L140M01OBox" style="display:none">
				<form action="" id="L140M01OForm">
					<table class="tb2" style="width:100%">
						<tr>
							<td class="hd1" style="width:25%"><th:block th:text="#{'l1405s02c01.023'}"><!--鑑價單位--></th:block></td>
							<td><span id="L140M01O_estBrn" class="field"></span></td>
						</tr>
						<tr>
							<td class="hd1"><th:block th:text="#{'l1405s02c01.018'}"><!--鑑估日期--></th:block></td>
							<td><span id="L140M01O_estDate" class="field"></span></td>
						</tr>
						<tr>
							<td class="hd1"><th:block th:text="#{'l1405s02c01.022'}"><!--核貸成數--></th:block></td>
							<td>
								<input type="text" name="L140M01O_payPercent" id="L140M01O_payPercent" class="numeric required" positiveonly="false" integer="3" fraction="2" maxlength="6" size="6"></input>
							</td>
						</tr>
					</table>
				</form>
			</div>

			<!--J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核-->
			<!--設質比率查詢-->
			<div id="divFindStgaData" style="display:none">
				<form id="formFindStgaData">
					<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td class="hd1"><th:block th:text="#{'L140M01o.stkNo'}">股票代號</th:block></td>
							<td>
								<input type="text" id="stkNo" name="stkNo" size="6" maxlength="6" class="upText"></input>
								<button type="button" id="btnFindStgaStkNo">
									<span class="text-only">
										<th:block th:text="#{'button.search'}">查詢</th:block>
									</span>
								</button>
							</td>
						</tr>
						<tr>
							<td class="hd1"><th:block th:text="#{'L140M01o.stkNm'}">股票名稱</th:block></td>
							<td><input type="text" id="stkNm" name="stkNm" size="30" class="required"></input></td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01o.changeQnty'}">加計本次增減設質總股數</th:block>(A)
							</td>
							<td>
								<input type="text" id="changeQnty" name="changeQnty" size="20" maxlength="19" integer="14" fraction="0" class="numeric required"></input>
								<th:block th:text="#{'L140M01o.StockUnit'}">股</th:block>(+增加/-減少)
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01o.qnty'}">已設質於本行總股數</th:block>(B)
							</td>
							<td>
								<input type="text" id="qnty" name="qnty" size="20" maxlength="19" integer="14" fraction="0" class="numeric required"></input>
								<th:block th:text="#{'L140M01o.StockUnit'}">股</th:block>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01o.stkNum'}">發行總股數</th:block>(C)
							</td>
							<td>
								<label>
									<input type="checkbox" id="ctrlKind" name="ctrlKind" value="2"></input>
									<th:block th:text="#{'L140M01o.ctrlKind_2'}">本案為國際聯貸案或徵提之股票註冊地在國外，且無法取得總發行股數者。</th:block>
								</label><br>
								<input type="text" id="stkNum" name="stkNum" size="20" maxlength="19" integer="14" fraction="0" class="numeric showFindStgaData required"></input>
								<span class="showFindStgaData">
									<th:block th:text="#{'L140M01o.StockUnit'}">股</th:block>
								</span>
							</td>
						</tr>
						<tr class="showFindStgaData">
							<td class="hd1">
								<th:block th:text="#{'L140M01o.setRatio'}">設質比率</th:block>(A+B)/C
							</td>
							<td>
								<span id="setRatio" class="field"></span>%
								<button type="button" id="btnCaculateSetRatio">
									<span class="text-only">
										<th:block th:text="#{'button.calc'}">計算</th:block>
									</span>
								</button>
							</td>
						</tr>
					</table>
				</form>
			</div>
    	</th:block>
    </body>
</html>
