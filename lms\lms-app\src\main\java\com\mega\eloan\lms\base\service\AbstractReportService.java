package com.mega.eloan.lms.base.service;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.text.ParseException;
import java.util.LinkedHashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.common.LMSUtil;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.report.ReportGenerator;

public abstract class AbstractReportService implements FileDownloadService,
		ReportService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(AbstractReportService.class);

	/**
	 * 設定報表範本檔
	 * 
	 * @return
	 */
	public abstract String getReportTemplateFileName();

	/**
	 * 設定報表資料
	 * 
	 * @param rptGenerator
	 *            ReportGenerator
	 * @param params
	 *            PageParameters
	 * @throws CapException
	 * @throws ParseException
	 */
	public abstract void setReportData(ReportGenerator rptGenerator,
			PageParameters params) throws CapException, ParseException;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.ReportService#generateReport(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public OutputStream generateReport(PageParameters params) throws CapException, Exception {
		ReportGenerator rptGenerator = null;
		OutputStream out = null;
		try {
			rptGenerator = new ReportGenerator(params.getString("exportType", Engine.EXPORT_PDF),
					this.getReportTemplateFileName());
			setReportData(rptGenerator, params);
			out = rptGenerator.generateReport();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
			Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
			rptGenerator = new ReportGenerator();
			rptVariableMap.put("ERRORMSG",
					"EFD0066:" + ReportGenerator.getErrorInfoFromException(ex));
			rptGenerator.setVariableData(rptVariableMap);
			try {
				out = rptGenerator.generateExceptionReport(LMSUtil.getLocale());
			} catch (Exception e) {
				LOGGER.error("ReportGenerator.generateExceptionReport() fail! Exception: ", e);
			}
		} finally {
			if (out != null) {
				try {
					out.close();
				} catch (IOException ex) {
					LOGGER.error("[generateReport]close() Exception!!", ex);
				}
			}

		}
		return out;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params)
			throws FileNotFoundException, ReportException, IOException,
			Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
			Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
			ReportGenerator rptGenerator = new ReportGenerator();
			rptVariableMap.put("ERRORMSG",
					"EFD0066:" + ReportGenerator.getErrorInfoFromException(ex));
			rptGenerator.setVariableData(rptVariableMap);
			baos = (ByteArrayOutputStream) rptGenerator
					.generateExceptionReport(LMSUtil.getLocale());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex);
				}
			}

		}
		return baos.toByteArray();
	}
}
