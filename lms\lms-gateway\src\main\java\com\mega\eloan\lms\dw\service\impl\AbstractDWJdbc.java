package com.mega.eloan.lms.dw.service.impl;

import javax.annotation.Resource;
import javax.sql.DataSource;

import tw.com.iisi.cap.context.CapParameter;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.EloanJdbcTemplate;

public class AbstractDWJdbc {

	private EloanJdbcTemplate jdbc;

	@Resource(name = "dwSqlStatement")
	CapParameter dwSQL;

	@Resource(name = "dw-db")
	public void setDataSource(DataSource dataSource) {
		jdbc = new EloanJdbcTemplate(dataSource, GWException.GWTYPE_DW);
		jdbc.setSqlProvider(dwSQL);
		jdbc.setCauseClass(this.getClass());
	}

	/**
	 * get the the jdbc
	 * 
	 * @return the jdbc
	 */
	public EloanJdbcTemplate getJdbc() {
		return jdbc;
	}
	
	/**
	  * 取得Sql
	  * 
	  * @param sqlId
	  *            sqlId
	  * @return sqlString
	  */
	 public String getSqlBySqlId(String sqlId) {
	  return dwSQL.getValue(sqlId);
	 }
}
