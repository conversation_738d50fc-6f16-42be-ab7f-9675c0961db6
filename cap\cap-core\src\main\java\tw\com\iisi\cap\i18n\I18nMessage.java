/**
 * I18nMessage.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.i18n;

import java.util.Locale;
import java.util.ResourceBundle;

import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.MessageSourceResourceBundle;

/**
 * 
 * <p>
 * 多國語系訊息 .(i18n/*.properties)
 * </p>
 * 
 * <pre>
 * $Date: 2010-08-25 11:25:55 +0800 (星期三, 25 八月 2010) $
 * $Author: iris $
 * $Revision: 185 $
 * $HeadURL: svn://***********/MICB_ISDOC/cap/cap-core/src/main/java/tw/com/iisi/cap/i18n/I18nMessage.java $
 * </pre>
 * 
 * <AUTHOR>
 * @version $Revision: 185 $
 * @version
 *          <ul>
 *          <li>2010/03/12,iristu,getMessage()取不到message，則output其key
 *          <li>2010/05/11,iristu,若尚未有Session時，就改取Default Locale。
 *          </ul>
 */
public class I18nMessage {

    /**
     * 訊息來源
     */
    private static MessageSource messageSource;

    /**
     * Instantiates a new i18n message.
     * 
     * @param ms
     *            MessageSource
     */
    public I18nMessage(MessageSource ms) {
        messageSource = ms;
    }

    /**
     * get Message Source
     * 
     * @return {@code messageSource}
     */
    public static MessageSource getMessageSource() {
        return messageSource;
    }

    /**
     * 取得訊息資源包實例<br>
     * 呼叫 {@linkplain org.springframework.context.support.MessageSourceResourceBundle#MessageSourceResourceBundle(MessageSource, Locale, ResourceBundle) MessageSourceResourceBundle} 建構子
     * 
     * @return {@code new MessageSourceResourceBundle(messageSource, LocaleContextHolder.getLocale())}
     */
    public static ResourceBundle getAsResourceBundle() {
        return new MessageSourceResourceBundle(messageSource, LocaleContextHolder.getLocale());
    }

    /**
     * Gets the message.
     * 
     * @param key
     *            the key
     * @param args
     *            the args
     * 
     * @return the message
     */
    public String getMessage(String key, Object[] args) {
        Locale locale = Locale.getDefault();
        locale = LocaleContextHolder.getLocale(); // current thread
        return messageSource.getMessage(key, args, locale);
    }

    /**
     * Gets the message.
     * 
     * @param key
     *            the key
     * 
     * @return the message
     */
    public String getMessage(String key) {
        return getMessage(key, null);
    }
}
