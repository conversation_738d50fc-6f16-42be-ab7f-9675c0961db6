/* 
 *  LMS0011GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.grid;

import java.util.Date;
import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.lms.pages.LMS0015V00Page;
import com.mega.eloan.lms.lns.pages.LMS1201M01Page;
import com.mega.eloan.lms.lns.service.LMS0011Service;
import com.mega.eloan.lms.model.VL015M01A01;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 待辦案件
 * </pre>
 * 
 * @since 2012/1/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/18,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms0011gridhandler")
public class LMS0011GridHandler extends AbstractGridHandler {

	@Resource
	LMS0011Service lms0011Service;

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branchService;

	/**
	 * 待辦案件的queryfilter
	 * 
	 * @param pageSetting
	 *            頁面相關設定
	 * @param params
	 *            前端資料
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryFilter(ISearch pageSetting, PageParameters params) throws CapException {
		String type = Util.nullToSpace(params.getString("type", ""));
		String selectDoc = Util.nullToSpace(params.getString("selectDoc", ""));
		String selectCase = Util.nullToSpace(params.getString("selectCase"));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrid",
				user.getUnitNo());

		IBranch ibranch = branchService.getBranch(user.getUnitNo());

		// 案件文件狀態
		if (!"all".equals(selectDoc) && !Util.isEmpty(selectDoc)) {
			// 如果是營運中心審查中 或待放行要另外做判斷
			String unitType = user.getUnitType();
			if (UnitTypeEnum.營運中心.isEquals(UnitTypeEnum
					.convertToUnitType(unitType))) {
				if (CreditDocStatusEnum.授管處_審查中.isEquals(selectDoc)) {
					selectDoc = CreditDocStatusEnum.營運中心_審查中.getCode();
				} else if (CreditDocStatusEnum.授管處_待放行.isEquals(selectDoc)) {
					selectDoc = CreditDocStatusEnum.營運中心_待放行.getCode();
				}
			}
			// 如果是總行的待覆核 也要另外判斷
			if (!Util.isEmpty(Util.trim(ibranch.getParentBrNo()))) {
				if (CreditDocStatusEnum.海外_待覆核.isEquals(selectDoc)) {
					selectDoc = CreditDocStatusEnum.國內簡易行待覆核.getCode();
				}
			}

			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					selectDoc);
		}

		// 案件種類 簽報書、動審表、...
		if (!"all".equals(selectCase) && !Util.isEmpty(selectCase)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type",
					selectCase);
		}
		switch (Util.parseInt(type)) {
		// 依文件建檔日期查詢
		case 1:
			Date startDate = Util.parseDate(Util.nullToSpace(params
					.getString("startDate")));
			Date endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate")));
			Object[] date = { startDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
					date);
			break;
		// 依客戶ID查詢
		case 2:
			String custId = Util.nullToSpace(params.getString("custId"));
			// String dupNo = Util.nullToSpace(params.getString("dupNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
			// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
			// dupNo);
			break;
		// 依經辦or主管查詢
		case 3:
			String mangerId = Util.nullToSpace(params.getString("mangerId"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "updater",
					mangerId);
			break;

		// 預設為目前登入者 分行 和 使用者ID
		default:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "updater",
					user.getUserId());
			break;

		}
		String sort = params.getString("sort", "");
		if ("2".equals(sort)) {
			// false 就不多帶這條件 不然原本的欄位在grid那邊無法排序
			pageSetting.addOrderBy("createTime", true);

		}
		// 限定只顯示企金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.企金);		
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS0015V00Page.class);
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);
		Page<VL015M01A01> page = lms0011Service.findPage(pageSetting);
		List<VL015M01A01> l015v00as = page.getContent();
		StringBuffer temp = new StringBuffer();
		for (VL015M01A01 l015v00a : l015v00as) {
			if ("L120M01A".equals(l015v00a.getType())) {
				l015v00a.setTypeShow(this.getCaseType(l015v00a, pop2, temp));

			} else {
				l015v00a.setTypeShow(pop.getProperty(StrUtils.concat("model.",
						l015v00a.getType())));
			}
			l015v00a.setCustName(Util.trim(l015v00a.getCustName()));
			// type先暫放文件狀態
			l015v00a.setType(l015v00a.getDocStatus());
			l015v00a.setCaseNo(Util.toSemiCharString(l015v00a.getCaseNo()));
			l015v00a.setDocStatus(getMessage(StrUtils.concat("docStatus.",
					l015v00a.getDocStatus())));
			l015v00a.setCustId(StrUtils.concat(l015v00a.getCustId(), " ",
					l015v00a.getDupNo()));
			l015v00a.setUpdater(this.getUserName(l015v00a.getUpdater()));

		}
		return new CapGridResult(l015v00as, page.getTotalRow());
	}

	/**
	 * 依照案件別代碼取得相對應案件別名稱
	 * 
	 * @param doccode
	 *            String
	 * @return Properties
	 */
	public String docCodeName(String doccode) {
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);
		if ("1".equals(doccode)) {
			return pop2.getProperty("L1205G.grid9");
		} else if ("2".equals(doccode)) {
			return pop2.getProperty("L1205G.grid10");
		} else {
			return pop2.getProperty("L1205G.grid11");
		}
	}

	/**
	 * 取得 案件類別
	 * 
	 * @param model
	 *            近期已收檔
	 * @param pop
	 *            語系檔
	 * @param temp
	 *            暫存的stringBuffer
	 * 
	 * @return
	 */
	private String getCaseType(VL015M01A01 model, Properties pop,
			StringBuffer temp) {
		temp.setLength(0);
		// L1205G.grid1=企金授權內
		// L1205G.grid2=企金授權外
		// L1205G.grid12=個金授權內
		// L1205G.grid13=個金授權外
		if (UtilConstants.Casedoc.DocType.企金.equals(model.getDocType())) {
			if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {
				temp.append(pop.getProperty("L1205G.grid1"));
			} else {
				temp.append(pop.getProperty("L1205G.grid2"));
			}
		} else {
			if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {
				temp.append(pop.getProperty("L1205G.grid12"));
			} else {
				temp.append(pop.getProperty("L1205G.grid13"));
			}
		}
		// L1205G.grid9=一般
		// L1205G.grid10=其他
		// L1205G.grid11=陳復/陳述案
		temp.append("(");
		if (UtilConstants.Casedoc.DocCode.一般.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid9"));
		} else if (UtilConstants.Casedoc.DocCode.其他.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid10"));
		} else {
			temp.append(pop.getProperty("L1205G.grid11"));
		}
		temp.append(")");
		return temp.toString();
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (userId == null) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

}
