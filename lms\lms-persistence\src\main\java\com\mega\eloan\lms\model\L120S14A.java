/* 
 * L120S14A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 合約書資料檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S14A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S14A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 新產生時：getUUID()
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	@Size(max=32)
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 合約編號 **/
	@Size(max=66)
	@Column(name="CONTRACTNO", length=66, columnDefinition="VARCHAR(66)")
	private String contractNo;

	/** 客戶名稱 **/
	@Size(max=150)
	@Column(name="CUSTNAME", length=150, columnDefinition="VARCHAR(150)")
	private String custName;

	/** 立合約人 **/
	@Size(max=150)
	@Column(name="CONTRACTOR", length=150, columnDefinition="VARCHAR(150)")
	private String contractor;

	/** 邀同 **/
	@Size(max=150)
	@Column(name="INVITER", length=150, columnDefinition="VARCHAR(150)")
	private String inviter;

	/** 授信金額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LOANAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal loanAmt;

	/** 
	 * 簽約日<p/>
	 * yyyy-MM-dd
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="SIGNDATE", columnDefinition="DATE")
	private Date signDate;

	/** 
	 * 起算月<p/>
	 * 幾個月
	 */
	private Integer bgnMonth;

	/** 
	 * 動用次數<p/>
	 * 1: 一次、0:分次
	 */
	private Integer useNum;

	/** 
	 * 撥款方式<p/>
	 * 第幾款
	 */
	private Integer fundingWay;

	/** 
	 * 活期存款帳號<p/>
	 * 撥款方式=1
	 */
	@Size(max=25)
	@Column(name="CURRACCOUNT", length=25, columnDefinition="VARCHAR(25)")
	private String currAccount;

	/** 
	 * 乙方負責人<p/>
	 * 撥款方式=2
	 */
	@Size(max=150)
	@Column(name="PARTYPRINCIPAL", length=150, columnDefinition="VARCHAR(150)")
	private String partyPrincipal;

	/** 
	 * 活期儲蓄存款帳號<p/>
	 * 撥款方式=2
	 */
	@Size(max=25)
	@Column(name="CURRSAVACCOUNT", length=25, columnDefinition="VARCHAR(25)")
	private String currSavAccount;

	/** 
	 * 授信期間<p/>
	 * 年
	 */
	private Integer lnyear;

	/** 
	 * 授信起日<p/>
	 * yyyy-MM-dd
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="LNBGNDATE", columnDefinition="DATE")
	private Date lnBgnDate;

	/** 
	 * 授信迄日<p/>
	 * yyyy-MM-dd
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="LNENDDATE", columnDefinition="DATE")
	private Date lnEndDate;

	/** 
	 * 償還方式<p/>
	 * 第幾款
	 */
	private Integer repayWay;

	/** 
	 * 寬限期<p/>
	 * 幾個月、償還方式=2
	 */
	private Integer gracePeriod;

	/** 
	 * 每期<p/>
	 * 每期幾個月、償還方式=2
	 */
	private Integer period;

	/** 
	 * 期數<p/>
	 * 分幾期、償還方式=2
	 */
	private Integer periodNum;

	/** 
	 * 還本期數 - 起<p/>
	 * 第幾期、償還方式=2
	 */
	private Integer capitalBgn;

	/** 
	 * 還本期數 - 迄<p/>
	 * 第幾期、償還方式=2
	 */
	private Integer capitalEnd;

	/** 
	 * 還本金額<p/>
	 * 償還方式=2
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CAPITALAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal capitalAmt;

	/** 
	 * 其他<p/>
	 * 償還方式=3
	 */
	@Size(max=300)
	@Column(name="OTHER", length=300, columnDefinition="VARCHAR(300)")
	private String other;

	/** 
	 * 訂約日<p/>
	 * yyyy-MM-dd
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="CONTRACTDATE", columnDefinition="DATE")
	private Date contractDate;

	/** 訂約年利 **/
	@Digits(integer=8, fraction=6, groups = Check.class)
	@Column(name="CONTRACTRATE", columnDefinition="DECIMAL(8,6)")
	private BigDecimal contractRate;

	/** 加碼年利 **/
	@Digits(integer=8, fraction=6, groups = Check.class)
	@Column(name="ADDRATE", columnDefinition="DECIMAL(8,6)")
	private BigDecimal addRate;

	/** 第二段加碼年利 **/
	@Digits(integer=8, fraction=6, groups = Check.class)
	@Column(name="ADDRATE2", columnDefinition="DECIMAL(8,6)")
	private BigDecimal addRate2;

	/** 地方法院 **/
	@Size(max=15)
	@Column(name="LOCATION", length=15, columnDefinition="VARCHAR(15)")
	private String location;

	/** 正本數 **/
	private Integer origNum;

	/** 副本數 **/
	private Integer copyNum;

	/** 
	 * 共同行銷<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="CROSSSELLING", length=1, columnDefinition="CHAR(1)")
	private String crossSelling;

	/** 
	 * 甲方代理分行<p/>
	 * 分行ID
	 */
	@Size(max=3)
	@Column(name="PROXYBRID", length=3, columnDefinition="CHAR(3)")
	private String proxyBrId;

	/** 
	 * 甲方代理人<p/>
	 * 分行經理ID
	 */
	@Size(max=6)
	@Column(name="PROXY", length=6, columnDefinition="CHAR(6)")
	private String proxy;

	/** 
	 * 甲方代理地址<p/>
	 * 分行地址
	 */
	@Size(max=192)
	@Column(name="PROXYADDR", length=192, columnDefinition="VARCHAR(192)")
	private String proxyAddr;

	/** 乙方 **/
	@Size(max=150)
	@Column(name="PARTY", length=150, columnDefinition="VARCHAR(150)")
	private String party;

	/** 乙方代理人 **/
	@Size(max=150)
	@Column(name="PARTYAGENT", length=150, columnDefinition="VARCHAR(150)")
	private String partyAgent;

	/** 乙方地址 **/
	@Size(max=192)
	@Column(name="PARTYADDR", length=192, columnDefinition="VARCHAR(192)")
	private String partyAddr;

	/** 乙方統編 **/
	@Size(max=10)
	@Column(name="PARTYID", length=10, columnDefinition="VARCHAR(10)")
	private String partyId;

	/** 丙方 **/
	@Size(max=150)
	@Column(name="PARTY2", length=150, columnDefinition="VARCHAR(150)")
	private String party2;

	/** 連保人 **/
	@Size(max=150)
	@Column(name="GUARANTOR", length=150, columnDefinition="VARCHAR(150)")
	private String guarantor;

	/** 連保人統編 **/
	@Size(max=10)
	@Column(name="GUARID", length=10, columnDefinition="VARCHAR(10)")
	private String guarId;

	/** 連保人地址 **/
	@Size(max=192)
	@Column(name="GUARADDR", length=192, columnDefinition="VARCHAR(192)")
	private String guarAddr;

    /** 是否為我國獨資或合夥企業 **/
    @Size(max = 1)
    @Column(name = "ISSOLE", length = 1, columnDefinition = "CHAR(1)")
    private String isSole;

    /** 企業類別 **/
    @Size(max = 2)
    @Column(name = "SOLETYPE", length = 2, columnDefinition = "CHAR(2)")
    private String soleType;

    /** 是否取得商業登記 **/
    @Size(max = 1)
    @Column(name = "HASREGIS", length = 1, columnDefinition = "CHAR(1)")
    private String hasRegis;

    /** 額度序號 **/
    @Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
    private String cntrNo;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 新產生時：getUUID()
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  新產生時：getUUID()
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	public String getRandomCode() {
		return this.randomCode;
	}
	/**
	 *  設定文件亂碼<p/>
	 *  每次儲存：getRandomCode()
	 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 取得合約編號 **/
	public String getContractNo() {
		return this.contractNo;
	}
	/** 設定合約編號 **/
	public void setContractNo(String value) {
		this.contractNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得立合約人 **/
	public String getContractor() {
		return this.contractor;
	}
	/** 設定立合約人 **/
	public void setContractor(String value) {
		this.contractor = value;
	}

	/** 取得邀同 **/
	public String getInviter() {
		return this.inviter;
	}
	/** 設定邀同 **/
	public void setInviter(String value) {
		this.inviter = value;
	}

	/** 取得授信金額 **/
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}
	/** 設定授信金額 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/** 
	 * 取得簽約日<p/>
	 * yyyy-MM-dd
	 */
	public Date getSignDate() {
		return this.signDate;
	}
	/**
	 *  設定簽約日<p/>
	 *  yyyy-MM-dd
	 **/
	public void setSignDate(Date value) {
		this.signDate = value;
	}

	/** 
	 * 取得起算月<p/>
	 * 幾個月
	 */
	public Integer getBgnMonth() {
		return this.bgnMonth;
	}
	/**
	 *  設定起算月<p/>
	 *  幾個月
	 **/
	public void setBgnMonth(Integer value) {
		this.bgnMonth = value;
	}

	/** 
	 * 取得動用次數<p/>
	 * 1: 一次、0:分次
	 */
	public Integer getUseNum() {
		return this.useNum;
	}
	/**
	 *  設定動用次數<p/>
	 *  1: 一次、0:分次
	 **/
	public void setUseNum(Integer value) {
		this.useNum = value;
	}

	/** 
	 * 取得撥款方式<p/>
	 * 第幾款
	 */
	public Integer getFundingWay() {
		return this.fundingWay;
	}
	/**
	 *  設定撥款方式<p/>
	 *  第幾款
	 **/
	public void setFundingWay(Integer value) {
		this.fundingWay = value;
	}

	/** 
	 * 取得活期存款帳號<p/>
	 * 撥款方式=1
	 */
	public String getCurrAccount() {
		return this.currAccount;
	}
	/**
	 *  設定活期存款帳號<p/>
	 *  撥款方式=1
	 **/
	public void setCurrAccount(String value) {
		this.currAccount = value;
	}

	/** 
	 * 取得乙方負責人<p/>
	 * 撥款方式=2
	 */
	public String getPartyPrincipal() {
		return this.partyPrincipal;
	}
	/**
	 *  設定乙方負責人<p/>
	 *  撥款方式=2
	 **/
	public void setPartyPrincipal(String value) {
		this.partyPrincipal = value;
	}

	/** 
	 * 取得活期儲蓄存款帳號<p/>
	 * 撥款方式=2
	 */
	public String getCurrSavAccount() {
		return this.currSavAccount;
	}
	/**
	 *  設定活期儲蓄存款帳號<p/>
	 *  撥款方式=2
	 **/
	public void setCurrSavAccount(String value) {
		this.currSavAccount = value;
	}

	/** 
	 * 取得授信期間<p/>
	 * 年
	 */
	public Integer getLnyear() {
		return this.lnyear;
	}
	/**
	 *  設定授信期間<p/>
	 *  年
	 **/
	public void setLnyear(Integer value) {
		this.lnyear = value;
	}

	/** 
	 * 取得授信起日<p/>
	 * yyyy-MM-dd
	 */
	public Date getLnBgnDate() {
		return this.lnBgnDate;
	}
	/**
	 *  設定授信起日<p/>
	 *  yyyy-MM-dd
	 **/
	public void setLnBgnDate(Date value) {
		this.lnBgnDate = value;
	}

	/** 
	 * 取得授信迄日<p/>
	 * yyyy-MM-dd
	 */
	public Date getLnEndDate() {
		return this.lnEndDate;
	}
	/**
	 *  設定授信迄日<p/>
	 *  yyyy-MM-dd
	 **/
	public void setLnEndDate(Date value) {
		this.lnEndDate = value;
	}

	/** 
	 * 取得償還方式<p/>
	 * 第幾款
	 */
	public Integer getRepayWay() {
		return this.repayWay;
	}
	/**
	 *  設定償還方式<p/>
	 *  第幾款
	 **/
	public void setRepayWay(Integer value) {
		this.repayWay = value;
	}

	/** 
	 * 取得寬限期<p/>
	 * 幾個月、償還方式=2
	 */
	public Integer getGracePeriod() {
		return this.gracePeriod;
	}
	/**
	 *  設定寬限期<p/>
	 *  幾個月、償還方式=2
	 **/
	public void setGracePeriod(Integer value) {
		this.gracePeriod = value;
	}

	/** 
	 * 取得每期<p/>
	 * 每期幾個月、償還方式=2
	 */
	public Integer getPeriod() {
		return this.period;
	}
	/**
	 *  設定每期<p/>
	 *  每期幾個月、償還方式=2
	 **/
	public void setPeriod(Integer value) {
		this.period = value;
	}

	/** 
	 * 取得期數<p/>
	 * 分幾期、償還方式=2
	 */
	public Integer getPeriodNum() {
		return this.periodNum;
	}
	/**
	 *  設定期數<p/>
	 *  分幾期、償還方式=2
	 **/
	public void setPeriodNum(Integer value) {
		this.periodNum = value;
	}

	/** 
	 * 取得還本期數 - 起<p/>
	 * 第幾期、償還方式=2
	 */
	public Integer getCapitalBgn() {
		return this.capitalBgn;
	}
	/**
	 *  設定還本期數 - 起<p/>
	 *  第幾期、償還方式=2
	 **/
	public void setCapitalBgn(Integer value) {
		this.capitalBgn = value;
	}

	/** 
	 * 取得還本期數 - 迄<p/>
	 * 第幾期、償還方式=2
	 */
	public Integer getCapitalEnd() {
		return this.capitalEnd;
	}
	/**
	 *  設定還本期數 - 迄<p/>
	 *  第幾期、償還方式=2
	 **/
	public void setCapitalEnd(Integer value) {
		this.capitalEnd = value;
	}

	/** 
	 * 取得還本金額<p/>
	 * 償還方式=2
	 */
	public BigDecimal getCapitalAmt() {
		return this.capitalAmt;
	}
	/**
	 *  設定還本金額<p/>
	 *  償還方式=2
	 **/
	public void setCapitalAmt(BigDecimal value) {
		this.capitalAmt = value;
	}

	/** 
	 * 取得其他<p/>
	 * 償還方式=3
	 */
	public String getOther() {
		return this.other;
	}
	/**
	 *  設定其他<p/>
	 *  償還方式=3
	 **/
	public void setOther(String value) {
		this.other = value;
	}

	/** 
	 * 取得訂約日<p/>
	 * yyyy-MM-dd
	 */
	public Date getContractDate() {
		return this.contractDate;
	}
	/**
	 *  設定訂約日<p/>
	 *  yyyy-MM-dd
	 **/
	public void setContractDate(Date value) {
		this.contractDate = value;
	}

	/** 取得訂約年利 **/
	public BigDecimal getContractRate() {
		return this.contractRate;
	}
	/** 設定訂約年利 **/
	public void setContractRate(BigDecimal value) {
		this.contractRate = value;
	}

	/** 取得加碼年利 **/
	public BigDecimal getAddRate() {
		return this.addRate;
	}
	/** 設定加碼年利 **/
	public void setAddRate(BigDecimal value) {
		this.addRate = value;
	}

	/** 取得第二段加碼年利 **/
	public BigDecimal getAddRate2() {
		return this.addRate2;
	}
	/** 設定第二段加碼年利 **/
	public void setAddRate2(BigDecimal value) {
		this.addRate2 = value;
	}

	/** 取得地方法院 **/
	public String getLocation() {
		return this.location;
	}
	/** 設定地方法院 **/
	public void setLocation(String value) {
		this.location = value;
	}

	/** 取得正本數 **/
	public Integer getOrigNum() {
		return this.origNum;
	}
	/** 設定正本數 **/
	public void setOrigNum(Integer value) {
		this.origNum = value;
	}

	/** 取得副本數 **/
	public Integer getCopyNum() {
		return this.copyNum;
	}
	/** 設定副本數 **/
	public void setCopyNum(Integer value) {
		this.copyNum = value;
	}

	/** 
	 * 取得共同行銷<p/>
	 * Y/N
	 */
	public String getCrossSelling() {
		return this.crossSelling;
	}
	/**
	 *  設定共同行銷<p/>
	 *  Y/N
	 **/
	public void setCrossSelling(String value) {
		this.crossSelling = value;
	}

	/** 
	 * 取得甲方代理分行<p/>
	 * 分行ID
	 */
	public String getProxyBrId() {
		return this.proxyBrId;
	}
	/**
	 *  設定甲方代理分行<p/>
	 *  分行ID
	 **/
	public void setProxyBrId(String value) {
		this.proxyBrId = value;
	}

	/** 
	 * 取得甲方代理人<p/>
	 * 分行經理ID
	 */
	public String getProxy() {
		return this.proxy;
	}
	/**
	 *  設定甲方代理人<p/>
	 *  分行經理ID
	 **/
	public void setProxy(String value) {
		this.proxy = value;
	}

	/** 
	 * 取得甲方代理地址<p/>
	 * 分行地址
	 */
	public String getProxyAddr() {
		return this.proxyAddr;
	}
	/**
	 *  設定甲方代理地址<p/>
	 *  分行地址
	 **/
	public void setProxyAddr(String value) {
		this.proxyAddr = value;
	}

	/** 取得乙方 **/
	public String getParty() {
		return this.party;
	}
	/** 設定乙方 **/
	public void setParty(String value) {
		this.party = value;
	}

	/** 取得乙方代理人 **/
	public String getPartyAgent() {
		return this.partyAgent;
	}
	/** 設定乙方代理人 **/
	public void setPartyAgent(String value) {
		this.partyAgent = value;
	}

	/** 取得乙方地址 **/
	public String getPartyAddr() {
		return this.partyAddr;
	}
	/** 設定乙方地址 **/
	public void setPartyAddr(String value) {
		this.partyAddr = value;
	}

	/** 取得乙方統編 **/
	public String getPartyId() {
		return this.partyId;
	}
	/** 設定乙方統編 **/
	public void setPartyId(String value) {
		this.partyId = value;
	}

	/** 取得丙方 **/
	public String getParty2() {
		return this.party2;
	}
	/** 設定丙方 **/
	public void setParty2(String value) {
		this.party2 = value;
	}

	/** 取得連保人 **/
	public String getGuarantor() {
		return this.guarantor;
	}
	/** 設定連保人 **/
	public void setGuarantor(String value) {
		this.guarantor = value;
	}

	/** 取得連保人統編 **/
	public String getGuarId() {
		return this.guarId;
	}
	/** 設定連保人統編 **/
	public void setGuarId(String value) {
		this.guarId = value;
	}

	/** 取得連保人地址 **/
	public String getGuarAddr() {
		return this.guarAddr;
	}
	/** 設定連保人地址 **/
	public void setGuarAddr(String value) {
		this.guarAddr = value;
	}

    public void setIsSole(String isSole) {
        this.isSole = isSole;
    }

    public String getIsSole() {
        return isSole;
    }

    public void setSoleType(String soleType) {
        this.soleType = soleType;
    }

    public String getSoleType() {
        return soleType;
    }

    public void setHasRegis(String hasRegis) {
        this.hasRegis = hasRegis;
    }

    public String getHasRegis() {
        return hasRegis;
    }

    /** 取得額度序號 **/
    public String getCntrNo() {
        return this.cntrNo;
    }

    /** 設定額度序號 **/
    public void setCntrNo(String value) {
        if (value != null) {
            // 一率轉大寫額度序號
            value = value.toUpperCase();
        }
        this.cntrNo = value;
    }
}
