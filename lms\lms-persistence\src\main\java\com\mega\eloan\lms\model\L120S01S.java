/* 
 * L120S01S.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 高齡客戶關懷檢核表明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S01S", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S01S extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 關聯主檔OID<p/>
	 * L120S01R.OID
	 */
	@Size(max=32)
	@Column(name="REFOID", length=32, columnDefinition="CHAR(32)")
	private String refOid;
	
	/** 
	 * 項目欄位名稱<p/>
	 * 用第幾大項第幾小項來定義<br/>
	 *  item1_1、item1_2…
	 */
	@Size(max=30)
	@Column(name="ITEMNAME", length=30, columnDefinition="VARCHAR(30)")
	private String itemName;

	/** 
	 * 項目勾選值<p/>
	 * Y是<br/>
	 *  N否NA不適用1正常<br/>
	 *  2異常
	 */
	@Size(max=2)
	@Column(name="ITEMVALUE", length=2, columnDefinition="VARCHAR(2)")
	private String itemValue;

	/** 
	 * 項目說明<p/>
	 * 其他欄位填寫用
	 */
	@Size(max=3000)
	@Column(name="DSCR", length=3000, columnDefinition="VARCHAR(3000)")
	private String dscr;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得關聯主檔OID<p/>
	 * L120S01R.OID
	 */
	public String getRefOid() {
		return this.refOid;
	}
	/**
	 *  設定關聯主檔OID<p/>
	 *  L120S01R.OID
	 **/
	public void setRefOid(String value) {
		this.refOid = value;
	}
	
	/** 
	 * 取得項目欄位名稱<p/>
	 * 用第幾大項第幾小項來定義<br/>
	 *  item1_1、item1_2…
	 */
	public String getItemName() {
		return this.itemName;
	}
	/**
	 *  設定項目欄位名稱<p/>
	 *  用第幾大項第幾小項來定義<br/>
	 *  item1_1、item1_2…
	 **/
	public void setItemName(String value) {
		this.itemName = value;
	}

	/** 
	 * 取得項目勾選值<p/>
	 * Y是<br/>
	 *  N否NA不適用1正常<br/>
	 *  2異常
	 */
	public String getItemValue() {
		return this.itemValue;
	}
	/**
	 *  設定項目勾選值<p/>
	 *  Y是<br/>
	 *  N否NA不適用1正常<br/>
	 *  2異常
	 **/
	public void setItemValue(String value) {
		this.itemValue = value;
	}

	/** 
	 * 取得項目說明<p/>
	 * 其他欄位填寫用
	 */
	public String getDscr() {
		return this.dscr;
	}
	/**
	 *  設定項目說明<p/>
	 *  其他欄位填寫用
	 **/
	public void setDscr(String value) {
		this.dscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
