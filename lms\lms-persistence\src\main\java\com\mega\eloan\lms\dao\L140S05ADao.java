/* 
 * L140S05ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S05A;

/** 條件變更資訊 **/
public interface L140S05ADao extends IGenericDao<L140S05A> {

	L140S05A findByOid(String oid);
	
	List<L140S05A> findByMainId(String mainId);
	
	L140S05A findByUniqueKey(String mainId);

	List<L140S05A> findByIndex01(String mainId);
}