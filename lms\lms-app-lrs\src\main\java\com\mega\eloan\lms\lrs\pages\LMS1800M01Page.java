package com.mega.eloan.lms.lrs.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import tw.com.jcs.common.Util;
import org.springframework.ui.ModelMap;
import com.iisigroup.cap.component.PageParameters;

import tw.com.jcs.auth.AuthType;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lrs.panels.LMS1800S01Panel;
import com.mega.eloan.lms.lrs.panels.LMS1800S02Panel;
import com.mega.eloan.lms.lrs.panels.LMS1800S03Panel;
import com.mega.eloan.lms.model.L180M01A;

@Controller
@RequestMapping("/lrs/lms1800m01/{page}")
public class LMS1800M01Page extends AbstractEloanForm {

	@Autowired
	RetrialService retrialService;
	
	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS1800M01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01A meta = retrialService.findL180M01A_oid(mainOid);
		
		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.編製中));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
				AuthType.Accept, RetrialDocStatusEnum.待覆核));

		boolean _btnGEN_BATCHNO = true;
		boolean rHS_L170M01A = false;
		if(meta!=null && Util.equals(RetrialDocStatusEnum.已產生覆審名單報告檔.getCode(), meta.getDocStatus())){
			rHS_L170M01A = true;
			_btnGEN_BATCHNO = false;
		}
		model.addAttribute("HS_L170M01A", rHS_L170M01A);
		model.addAttribute("_btnGEN_BATCHNO", _btnGEN_BATCHNO);
		
		renderJsI18N(LMS1800M01Page.class);
		renderJsI18N(AbstractEloanPage.class);
		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); 
		model.addAttribute("tabID", tabID);
		
		Panel panel = getPanel(page, meta);
		panel.processPanelData(model, params);
		//---
	}
	
	// 頁籤
	public Panel getPanel(int index, L180M01A meta) {		
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS1800S01Panel(TAB_CTX, true, Util.equals(RetrialDocStatusEnum.編製中.getCode(), meta.getDocStatus()));
			break;
		case 2:
			panel = new LMS1800S02Panel(TAB_CTX, true);
			break;
		case 3:
			panel = new LMS1800S03Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS1800S01Panel(TAB_CTX, true, false);
			break;
		}
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L180M01A.class;
	}
}
