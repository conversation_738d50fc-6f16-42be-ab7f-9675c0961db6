/* 
 * L120S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotNull;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check3;

/** 借款人主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S01A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class L120S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 借款人姓名 **/
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String docType;

	/**
	 * 客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 */
	@Column(name = "TYPCD", length = 1, columnDefinition = "CHAR(1)")
	private String typCd;

	/**
	 * 當地識別ID
	 * <p/>
	 * 101/04/21新增<br/>
	 * ※海外分行用
	 */
	@Column(name = "LOCALCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String localCustId;

	/**
	 * 客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	@Column(name = "CUSTNO", length = 60, columnDefinition = "VARCHAR(60)")
	private String custNo;

	/**
	 * 行員編號
	 * <p/>
	 * 101/06/11新增<br/>
	 * ※個金才需填寫，非必要輸入
	 */
	@Column(name = "STAFFNO", length = 6, columnDefinition = "VARCHAR(6)")
	private String staffNo;

	/**
	 * 主要借款人註記
	 * <p/>
	 * Y/N
	 */
	@Column(name = "KEYMAN", length = 1, columnDefinition = "CHAR(1)")
	private String keyMan;

	/**
	 * 與主要借款人關係
	 * <p/>
	 * 本人、配偶…<br/>
	 * 企業關係人：1X, 2X, …<br/>
	 * 親屬關係：X0, XA, …<br/>
	 * 其他綜合關係：10, 1A, …<br/>
	 * (詳註一)
	 */
	@Column(name = "CUSTRLT", length = 2, columnDefinition = "CHAR(2)")
	@NotNull(message = "{required.message}", groups = Check3.class)
	@NotEmpty(message = "{required.message}", groups = Check3.class)
	private String custRlt;

	/**
	 * 相關身份
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)<br/>
	 * ※非主要借款人時才需填列
	 */
	@Column(name = "CUSTPOS", length = 1, columnDefinition = "CHAR(1)")
	@NotNull(message = "{required.message}", groups = Check3.class)
	@NotEmpty(message = "{required.message}", groups = Check3.class)
	private String custPos;

	/**
	 * OBU公司 存續證明到期日
	 * <p/>
	 * ※以下三欄為報表查詢系統產生每月到期控制清單之用，以控管OBU新作或續約時所需提供公司存續證明或繳交年費證明有效期限，輸入不列印<br/>
	 * ※OBU才需填寫
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "EXISTDATE", columnDefinition = "DATE")
	private Date existDate;

	/**
	 * OBU公司 繳交年費證明到期日
	 * <p/>
	 * ※OBU才需填寫
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "FEEDATE", columnDefinition = "DATE")
	private Date feeDate;

	/**
	 * OBU公司 註冊國有效期
	 * <p/>
	 * ※OBU才需填寫
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "COUNTRYDATE", columnDefinition = "DATE")
	private Date countryDate;

	/**
	 * 負責事業體統一編號
	 * <p/>
	 * ※個金才需填寫
	 */
	@Column(name = "CMPID", length = 10, columnDefinition = "VARCHAR(10)")
	private String cmpId;

	/**
	 * 負責事業體名稱
	 * <p/>
	 * ※個金才需填寫
	 */
	@Column(name = "CMPNM", length = 120, columnDefinition = "VARCHAR(120)")
	private String cmpNm;

	/**
	 * 法人或自然人
	 * <p/>
	 * 101/06/08新增<br/>
	 * N：自然人<br/>
	 * C：法人<br/>
	 * A：主管機關核准作無擔保授信<br/>
	 * B：官股代表
	 */
	@Column(name = "RENCD", length = 1, columnDefinition = "VARCHAR(1)")
	private String renCd;

	/**
	 * 行業別
	 * <p/>
	 * 101/06/08新增
	 */
	@Column(name = "BUSCODE", length = 6, columnDefinition = "VARCHAR(6)")
	private String busCode;

	/**
	 * 次產業別
	 * <p/>
	 * 101/06/08新增
	 */
	@Column(name = "SUBCODE", length = 2, columnDefinition = "VARCHAR(2)")
	private String subCode;

	/**
	 * 身份類別
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 1借保人<br/>
	 * 2擔保品提供人
	 */
	@Column(name = "CUSTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String custType;

	/**
	 * 採用評等模型
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫
	 */
	@Column(name = "CRDMODEL", length = 2, columnDefinition = "VARCHAR(2)")
	private String crdModel;

	/**
	 * 備註
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 20個全型字
	 */
	@Column(name = "RMK", length = 60, columnDefinition = "VARCHAR(60)")
	private String rmk;

	/**
	 * 是否本行有婉卻紀錄
	 * <p/>
	 * 101/03/16新增<br/>
	 * Y.有 N.無
	 */
	@Column(name = "ISREJT", length = 1, columnDefinition = "CHAR(1)")
	private String IsRejt;

	/**
	 * 查詢日期
	 * <p/>
	 * 101/03/16新增<br/>
	 * current date
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "REJTREADDATE", columnDefinition = "DATE")
	private Date rejtReadDate;

	/**
	 * 登錄分行
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REGBR
	 */
	@Column(name = "REJTBRNO", length = 3, columnDefinition = "CHAR(3)")
	private String rejtBrNo;

	/**
	 * 婉卻代碼
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REFUSECD
	 */
	@Column(name = "REJTCODE", columnDefinition = "DECIMAL(2,0)")
	private Integer rejtCode;

	/**
	 * 婉卻理由
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REFUSEDS
	 */
	@Column(name = "REJTREASON", length = 318, columnDefinition = "VARCHAR(318)")
	private String rejtReason;

	/**
	 * 登錄時間
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REGDT
	 */
	@Column(name = "REJTDATE", columnDefinition = "TIMESTAMP")
	private Date rejtDate;

	/**
	 * 婉卻控管種類
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.STATUSCD<br/>
	 * 1.維持控管, 2.警示不控管, D.刪除控管
	 */
	@Column(name = "REJTCASE", length = 1, columnDefinition = "VARCHAR(1)")
	private String rejtCase;

	/**
	 * 婉卻變更Memo
	 * <p/>
	 * 101/03/16新增
	 */
	@Column(name = "REJTCASEADJMEMO", length = 318, columnDefinition = "VARCHAR(318)")
	private String rejtCaseAdjMemo;

	/**
	 * 變更前婉卻控管種類
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REFUSECD
	 */
	@Column(name = "REJTCASEBEFORE", length = 1, columnDefinition = "VARCHAR(1)")
	private String rejtCaseBefore;

	/**
	 * 黑名單查詢結果
	 * <p/>
	 * 2012/10/23 Miller add
	 */
	@Column(name = "BLACKNAME", length = 1024, columnDefinition = "VARCHAR(1024)")
	private String blackName;

	/**
	 * 黑名單查詢日期
	 * <p/>
	 * 2013/04/09 Miller add
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "BLACKDATE", columnDefinition = "DATE")
	private Date blackDate;

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 是否本行有婉卻紀錄(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * Y.有 N.無
	 */
	@Column(name = "ISREJT1", length = 1, columnDefinition = "CHAR(1)")
	private String IsRejt1;

	/**
	 * 查詢日期(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * current date
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "REJTREADDATE1", columnDefinition = "DATE")
	private Date rejtReadDate1;

	/**
	 * 登錄分行(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REGBR
	 */
	@Column(name = "REJTBRNO1", length = 3, columnDefinition = "CHAR(3)")
	private String rejtBrNo1;

	/**
	 * 婉卻代碼(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REFUSECD
	 */
	@Column(name = "REJTCODE1", columnDefinition = "DECIMAL(2,0)")
	private Integer rejtCode1;

	/**
	 * 婉卻理由(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REFUSEDS
	 */
	@Column(name = "REJTREASON1", length = 318, columnDefinition = "VARCHAR(318)")
	private String rejtReason1;

	/**
	 * 登錄時間(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REGDT
	 */
	@Column(name = "REJTDATE1", columnDefinition = "TIMESTAMP")
	private Date rejtDate1;

	/**
	 * 婉卻控管種類(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.STATUSCD<br/>
	 * 1.維持控管, 2.警示不控管, D.刪除控管
	 */
	@Column(name = "REJTCASE1", length = 1, columnDefinition = "VARCHAR(1)")
	private String rejtCase1;

	/**
	 * 婉卻變更Memo(負責人)
	 * <p/>
	 * 102/12/03新增
	 */
	@Column(name = "REJTCASEADJMEMO1", length = 318, columnDefinition = "VARCHAR(318)")
	private String rejtCaseAdjMemo1;

	/**
	 * 變更前婉卻控管種類(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REFUSECD
	 */
	@Column(name = "REJTCASEBEFORE1", length = 1, columnDefinition = "VARCHAR(1)")
	private String rejtCaseBefore1;

	/**
	 * 婉卻負責人統編
	 * <p/>
	 * 102/12/03新增
	 */
	@Column(name = "REJTCHAIRMANID", length = 10, columnDefinition = "VARCHAR(10)")
	private String rejtChairmanId;

	/**
	 * 婉卻負責人統編重複碼
	 * <p/>
	 * 102/12/03新增
	 */
	@Column(name = "REJTCHAIRMANDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String rejtChairmanDupNo;

	/**
	 * 科目顯示順序
	 */
	@Column(name = "CUSTSHOWSEQNUM", columnDefinition = "DECIMAL(3,0)")
	private Integer custShowSeqNum;

	/**
	 * 是否有異常通報紀錄(申貸戶)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Column(name = "ISABNORMAL", length = 1, columnDefinition = "CHAR(1)")
	private String isAbnormal;

	/**
	 * 查詢異常通報紀錄日期(申貸戶)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ABNORMALREADDATE", columnDefinition = "DATE")
	private Date abnormalReadDate;

	/**
	 * 通報分行(申貸戶)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Column(name = "ABNORMALBRNO", length = 3, columnDefinition = "CHAR(3)")
	private String abnormalBrNo;

	/**
	 * 通報時間(申貸戶)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ABNORMALDATE", columnDefinition = "DATE")
	private Date abnormalDate;

	/**
	 * 目前異常通報狀態(申貸戶)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Column(name = "ABNORMALSTATUS", length = 1, columnDefinition = "CHAR(1)")
	private String abnormalStatus;

	/**
	 * 是否有異常通報紀錄(負責人)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Column(name = "ISABNORMAL1", length = 1, columnDefinition = "CHAR(1)")
	private String isAbnormal1;

	/**
	 * 是否有異常通報紀錄(負責人)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Column(name = "ABNORMALCHAIRMANID", length = 10, columnDefinition = "CHAR(10)")
	private String abnormalChairmanId;

	/**
	 * 是否有異常通報紀錄(負責人)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Column(name = "ABNORMALCHAIRMANDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String abnormalChairmanDupNo;

	/**
	 * 查詢異常通報紀錄日期(負責人)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ABNORMALREADDATE1", columnDefinition = "DATE")
	private Date abnormalReadDate1;

	/**
	 * 通報分行(負責人)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Column(name = "ABNORMALBRNO1", length = 3, columnDefinition = "CHAR(3)")
	private String abnormalBrNo1;

	/**
	 * 通報時間(負責人)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ABNORMALDATE1", columnDefinition = "DATE")
	private Date abnormalDate1;

	/**
	 * 目前異常通報狀態(負責人)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Column(name = "ABNORMALSTATUS1", length = 1, columnDefinition = "CHAR(1)")
	private String abnormalStatus1;

	/**
	 * 異常通報簽報書MAINID(申貸戶)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Column(name = "ABNORMALMAINID", length = 32, columnDefinition = "VARCHAR(32)")
	private String abnormalMainId;

	/**
	 * 異常通報簽報書MAINID(負責人)
	 * <p/>
	 * J-105-0179-001 105/07/20新增
	 */
	@Column(name = "ABNORMALMAINID1", length = 32, columnDefinition = "VARCHAR(32)")
	private String abnormalMainId1;

	/**
	 * 新客戶往來註記
	 * <p/>
	 * J-108-0143_05097_B1001 Web e-Loan國內外企金額度明細表簽報性質新作時加註(新客戶往來原有客戶往來)
	 */
	@Column(name = "NEWCUSTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String newCustFlag;

	/**
	 * 申貸戶是否有因不符ESG而暫緩承作 J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表
	 * 」建置，於簽報書增設相對應欄位 Y.有 N.無
	 */
	@Column(name = "ISESGREJT", length = 1, columnDefinition = "CHAR(1)")
	private String isEsgRejt;

	/**
	 * 不符ESG而暫緩承作查詢日期(申貸戶) J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表
	 * 」建置，於簽報書增設相對應欄位 current date
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ESGREJTREADDATE", columnDefinition = "DATE")
	private Date esgRejtReadDate;

	/**
	 * 不符ESG而暫緩承作登錄分行(申貸戶) J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表
	 * 」建置，於簽報書增設相對應欄位
	 */
	@Column(name = "ESGREJTBRNO", length = 3, columnDefinition = "CHAR(3)")
	private String esgRejtBrNo;

	/**
	 * 不符ESG而暫緩承作登錄行員(申貸戶) J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表
	 * 」建置，於簽報書增設相對應欄位
	 */
	@Column(name = "ESGREJTEMPNO", length = 6, columnDefinition = "CHAR(6)")
	private String esgRejtEmpNo;

	/**
	 * 不符ESG而暫緩承登錄時間(申貸戶) J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表
	 * 」建置，於簽報書增設相對應欄位
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ESGREJTDATE", columnDefinition = "DATE")
	private Date esgRejtDate;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得借款人姓名 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定借款人姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	public String getDocType() {
		return this.docType;
	}

	/**
	 * 設定企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/**
	 * 取得客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 */
	public String getTypCd() {
		return this.typCd;
	}

	/**
	 * 設定客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/**
	 * 取得當地識別ID
	 * <p/>
	 * 101/04/21新增<br/>
	 * ※海外分行用
	 */
	public String getLocalCustId() {
		return this.localCustId;
	}

	/**
	 * 設定當地識別ID
	 * <p/>
	 * 101/04/21新增<br/>
	 * ※海外分行用
	 **/
	public void setLocalCustId(String value) {
		this.localCustId = value;
	}

	/**
	 * 取得客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	public String getCustNo() {
		return this.custNo;
	}

	/**
	 * 設定客戶編號
	 * <p/>
	 * 非必要輸入
	 **/
	public void setCustNo(String value) {
		this.custNo = value;
	}

	/**
	 * 取得行員編號
	 * <p/>
	 * 101/06/11新增<br/>
	 * ※個金才需填寫，非必要輸入
	 */
	public String getStaffNo() {
		return this.staffNo;
	}

	/**
	 * 設定行員編號
	 * <p/>
	 * 101/06/11新增<br/>
	 * ※個金才需填寫，非必要輸入
	 **/
	public void setStaffNo(String value) {
		this.staffNo = value;
	}

	/**
	 * 取得主要借款人註記
	 * <p/>
	 * Y/N
	 */
	public String getKeyMan() {
		return this.keyMan;
	}

	/**
	 * 設定主要借款人註記
	 * <p/>
	 * Y/N
	 **/
	public void setKeyMan(String value) {
		this.keyMan = value;
	}

	/**
	 * 取得與主要借款人關係
	 * <p/>
	 * 本人、配偶…<br/>
	 * 企業關係人：1X, 2X, …<br/>
	 * 親屬關係：X0, XA, …<br/>
	 * 其他綜合關係：10, 1A, …<br/>
	 * (詳註一)
	 */
	public String getCustRlt() {
		return this.custRlt;
	}

	/**
	 * 設定與主要借款人關係
	 * <p/>
	 * 本人、配偶…<br/>
	 * 企業關係人：1X, 2X, …<br/>
	 * 親屬關係：X0, XA, …<br/>
	 * 其他綜合關係：10, 1A, …<br/>
	 * (詳註一)
	 **/
	public void setCustRlt(String value) {
		this.custRlt = value;
	}

	/**
	 * 取得相關身份
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)<br/>
	 * ※非主要借款人時才需填列
	 */
	public String getCustPos() {
		return this.custPos;
	}

	/**
	 * 設定相關身份
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)<br/>
	 * ※非主要借款人時才需填列
	 **/
	public void setCustPos(String value) {
		this.custPos = value;
	}

	/**
	 * 取得OBU公司 存續證明到期日
	 * <p/>
	 * ※以下三欄為報表查詢系統產生每月到期控制清單之用，以控管OBU新作或續約時所需提供公司存續證明或繳交年費證明有效期限，輸入不列印<br/>
	 * ※OBU才需填寫
	 */
	public Date getExistDate() {
		return this.existDate;
	}

	/**
	 * 設定OBU公司 存續證明到期日
	 * <p/>
	 * ※以下三欄為報表查詢系統產生每月到期控制清單之用，以控管OBU新作或續約時所需提供公司存續證明或繳交年費證明有效期限，輸入不列印<br/>
	 * ※OBU才需填寫
	 **/
	public void setExistDate(Date value) {
		this.existDate = value;
	}

	/**
	 * 取得OBU公司 繳交年費證明到期日
	 * <p/>
	 * ※OBU才需填寫
	 */
	public Date getFeeDate() {
		return this.feeDate;
	}

	/**
	 * 設定OBU公司 繳交年費證明到期日
	 * <p/>
	 * ※OBU才需填寫
	 **/
	public void setFeeDate(Date value) {
		this.feeDate = value;
	}

	/**
	 * 取得OBU公司 註冊國有效期
	 * <p/>
	 * ※OBU才需填寫
	 */
	public Date getCountryDate() {
		return this.countryDate;
	}

	/**
	 * 設定OBU公司 註冊國有效期
	 * <p/>
	 * ※OBU才需填寫
	 **/
	public void setCountryDate(Date value) {
		this.countryDate = value;
	}

	/**
	 * 取得負責事業體統一編號
	 * <p/>
	 * ※個金才需填寫
	 */
	public String getCmpId() {
		return this.cmpId;
	}

	/**
	 * 設定負責事業體統一編號
	 * <p/>
	 * ※個金才需填寫
	 **/
	public void setCmpId(String value) {
		this.cmpId = value;
	}

	/**
	 * 取得負責事業體名稱
	 * <p/>
	 * ※個金才需填寫
	 */
	public String getCmpNm() {
		return this.cmpNm;
	}

	/**
	 * 設定負責事業體名稱
	 * <p/>
	 * ※個金才需填寫
	 **/
	public void setCmpNm(String value) {
		this.cmpNm = value;
	}

	/**
	 * 取得法人或自然人
	 * <p/>
	 * 101/06/08新增<br/>
	 * N：自然人<br/>
	 * C：法人<br/>
	 * A：主管機關核准作無擔保授信<br/>
	 * B：官股代表
	 */
	public String getRenCd() {
		return this.renCd;
	}

	/**
	 * 設定法人或自然人
	 * <p/>
	 * 101/06/08新增<br/>
	 * N：自然人<br/>
	 * C：法人<br/>
	 * A：主管機關核准作無擔保授信<br/>
	 * B：官股代表
	 **/
	public void setRenCd(String value) {
		this.renCd = value;
	}

	/**
	 * 取得行業別
	 * <p/>
	 * 101/06/08新增
	 */
	public String getBusCode() {
		return this.busCode;
	}

	/**
	 * 設定行業別
	 * <p/>
	 * 101/06/08新增
	 **/
	public void setBusCode(String value) {
		this.busCode = value;
	}

	/**
	 * 取得次產業別
	 * <p/>
	 * 101/06/08新增
	 */
	public String getSubCode() {
		return this.subCode;
	}

	/**
	 * 設定次產業別
	 * <p/>
	 * 101/06/08新增
	 **/
	public void setSubCode(String value) {
		this.subCode = value;
	}

	/**
	 * 取得身份類別
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 1借保人<br/>
	 * 2擔保品提供人
	 */
	public String getCustType() {
		return this.custType;
	}

	/**
	 * 設定身份類別
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 1借保人<br/>
	 * 2擔保品提供人
	 **/
	public void setCustType(String value) {
		this.custType = value;
	}

	/**
	 * 取得採用評等模型
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫
	 */
	public String getCrdModel() {
		return this.crdModel;
	}

	/**
	 * 設定採用評等模型
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫
	 **/
	public void setCrdModel(String value) {
		this.crdModel = value;
	}

	/**
	 * 取得備註
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 20個全型字
	 */
	public String getRmk() {
		return this.rmk;
	}

	/**
	 * 設定備註
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 20個全型字
	 **/
	public void setRmk(String value) {
		this.rmk = value;
	}

	/**
	 * 取得是否本行有婉卻紀錄
	 * <p/>
	 * 101/03/16新增<br/>
	 * Y.有 N.無
	 */
	public String getIsRejt() {
		return this.IsRejt;
	}

	/**
	 * 設定是否本行有婉卻紀錄
	 * <p/>
	 * 101/03/16新增<br/>
	 * Y.有 N.無
	 **/
	public void setIsRejt(String value) {
		this.IsRejt = value;
	}

	/**
	 * 取得查詢日期
	 * <p/>
	 * 101/03/16新增<br/>
	 * current date
	 */
	public Date getRejtReadDate() {
		return this.rejtReadDate;
	}

	/**
	 * 設定查詢日期
	 * <p/>
	 * 101/03/16新增<br/>
	 * current date
	 **/
	public void setRejtReadDate(Date value) {
		this.rejtReadDate = value;
	}

	/**
	 * 取得登錄分行
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REGBR
	 */
	public String getRejtBrNo() {
		return this.rejtBrNo;
	}

	/**
	 * 設定登錄分行
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REGBR
	 **/
	public void setRejtBrNo(String value) {
		this.rejtBrNo = value;
	}

	/**
	 * 取得婉卻代碼
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REFUSECD
	 */
	public Integer getRejtCode() {
		return this.rejtCode;
	}

	/**
	 * 設定婉卻代碼
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REFUSECD
	 **/
	public void setRejtCode(Integer value) {
		this.rejtCode = value;
	}

	/**
	 * 取得婉卻理由
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REFUSEDS
	 */
	public String getRejtReason() {
		return this.rejtReason;
	}

	/**
	 * 設定婉卻理由
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REFUSEDS
	 **/
	public void setRejtReason(String value) {
		this.rejtReason = value;
	}

	/**
	 * 取得登錄時間
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REGDT
	 */
	public Date getRejtDate() {
		return this.rejtDate;
	}

	/**
	 * 設定登錄時間
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REGDT
	 **/
	public void setRejtDate(Date value) {
		this.rejtDate = value;
	}

	/**
	 * 取得婉卻控管種類
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.STATUSCD<br/>
	 * 1.維持控管, 2.警示不控管, D.刪除控管
	 */
	public String getRejtCase() {
		return this.rejtCase;
	}

	/**
	 * 設定婉卻控管種類
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.STATUSCD<br/>
	 * 1.維持控管, 2.警示不控管, D.刪除控管
	 **/
	public void setRejtCase(String value) {
		this.rejtCase = value;
	}

	/**
	 * 取得婉卻變更Memo
	 * <p/>
	 * 101/03/16新增
	 */
	public String getRejtCaseAdjMemo() {
		return this.rejtCaseAdjMemo;
	}

	/**
	 * 設定婉卻變更Memo
	 * <p/>
	 * 101/03/16新增
	 **/
	public void setRejtCaseAdjMemo(String value) {
		this.rejtCaseAdjMemo = value;
	}

	/**
	 * 取得變更前婉卻控管種類
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REFUSECD
	 */
	public String getRejtCaseBefore() {
		return this.rejtCaseBefore;
	}

	/**
	 * 設定變更前婉卻控管種類
	 * <p/>
	 * 101/03/16新增<br/>
	 * MIS.LNUNID.REFUSECD
	 **/
	public void setRejtCaseBefore(String value) {
		this.rejtCaseBefore = value;
	}

	/**
	 * 取得黑名單查詢結果
	 * <p/>
	 * 2012/10/23 Miller add
	 */
	public String getBlackName() {
		return this.blackName;
	}

	/**
	 * 設定黑名單查詢結果
	 * <p/>
	 * 2012/10/23 Miller add
	 **/
	public void setBlackName(String value) {
		this.blackName = value;
	}

	/**
	 * 取得黑名單查詢日期
	 * <p/>
	 * 2013/04/09 Miller add
	 */
	public Date getBlackDate() {
		return this.blackDate;
	}

	/**
	 * 設定黑名單查詢日期
	 * <p/>
	 * 2013/04/09 Miller add
	 **/
	public void setBlackDate(Date value) {
		this.blackDate = value;
	}

	/**
	 * 取得輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}

	/**
	 * 設定輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/**
	 * 取得是否本行有婉卻紀錄(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * Y.有 N.無
	 */
	public String getIsRejt1() {
		return IsRejt1;
	}

	/**
	 * 設定是否本行有婉卻紀錄(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * Y.有 N.無
	 */
	public void setIsRejt1(String isRejt1) {
		IsRejt1 = isRejt1;
	}

	/**
	 * 取得查詢日期(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * current date
	 */
	public Date getRejtReadDate1() {
		return rejtReadDate1;
	}

	/**
	 * 設定查詢日期(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * current date
	 */
	public void setRejtReadDate1(Date rejtReadDate1) {
		this.rejtReadDate1 = rejtReadDate1;
	}

	/**
	 * 取得登錄分行(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REGBR
	 */
	public String getRejtBrNo1() {
		return rejtBrNo1;
	}

	/**
	 * 設定登錄分行(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REGBR
	 */
	public void setRejtBrNo1(String rejtBrNo1) {
		this.rejtBrNo1 = rejtBrNo1;
	}

	/**
	 * 取得婉卻代碼(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REFUSECD
	 */
	public Integer getRejtCode1() {
		return rejtCode1;
	}

	/**
	 * 設定婉卻代碼(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REFUSECD
	 */
	public void setRejtCode1(Integer rejtCode1) {
		this.rejtCode1 = rejtCode1;
	}

	/**
	 * 取得婉卻理由(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REFUSEDS
	 */
	public String getRejtReason1() {
		return rejtReason1;
	}

	/**
	 * 設定婉卻理由(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REFUSEDS
	 */
	public void setRejtReason1(String rejtReason1) {
		this.rejtReason1 = rejtReason1;
	}

	/**
	 * 取得登錄時間(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REGDT
	 */
	public Date getRejtDate1() {
		return rejtDate1;
	}

	/**
	 * 設定登錄時間(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REGDT
	 */
	public void setRejtDate1(Date rejtDate1) {
		this.rejtDate1 = rejtDate1;
	}

	/**
	 * 取得婉卻控管種類(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.STATUSCD<br/>
	 * 1.維持控管, 2.警示不控管, D.刪除控管
	 */
	public String getRejtCase1() {
		return rejtCase1;
	}

	/**
	 * 設定婉卻控管種類(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.STATUSCD<br/>
	 * 1.維持控管, 2.警示不控管, D.刪除控管
	 */
	public void setRejtCase1(String rejtCase1) {
		this.rejtCase1 = rejtCase1;
	}

	/**
	 * 取得婉卻變更Memo(負責人)
	 * <p/>
	 * 102/12/03新增
	 */
	public String getRejtCaseAdjMemo1() {
		return rejtCaseAdjMemo1;
	}

	/**
	 * 設定婉卻變更Memo(負責人)
	 * <p/>
	 * 102/12/03新增
	 */
	public void setRejtCaseAdjMemo1(String rejtCaseAdjMemo1) {
		this.rejtCaseAdjMemo1 = rejtCaseAdjMemo1;
	}

	/**
	 * 取得變更前婉卻控管種類(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REFUSECD
	 */
	public String getRejtCaseBefore1() {
		return rejtCaseBefore1;
	}

	/**
	 * 設定變更前婉卻控管種類(負責人)
	 * <p/>
	 * 102/12/03新增<br/>
	 * MIS.LNUNID.REFUSECD
	 */
	public void setRejtCaseBefore1(String rejtCaseBefore1) {
		this.rejtCaseBefore1 = rejtCaseBefore1;
	}

	/**
	 * 取得婉卻負責人統編
	 * <p/>
	 * 102/12/03新增
	 */
	public String getRejtChairmanId() {
		return rejtChairmanId;
	}

	/**
	 * 設定婉卻負責人統編
	 * <p/>
	 * 102/12/03新增
	 */
	public void setRejtChairmanId(String rejtChairmanId) {
		this.rejtChairmanId = rejtChairmanId;
	}

	/**
	 * 取得婉卻負責人統編重複碼
	 * <p/>
	 * 102/12/03新增
	 */
	public String getRejtChairmanDupNo() {
		return rejtChairmanDupNo;
	}

	/**
	 * 設定婉卻負責人統編重複碼
	 * <p/>
	 * 102/12/03新增
	 */
	public void setRejtChairmanDupNo(String rejtChairmanDupNo) {
		this.rejtChairmanDupNo = rejtChairmanDupNo;
	}

	/** 設定科目顯示順序 **/
	public void setCustShowSeqNum(Integer custShowSeqNum) {
		this.custShowSeqNum = custShowSeqNum;
	}

	/** 取得科目顯示順序 **/
	public Integer getCustShowSeqNum() {
		return custShowSeqNum;
	}

	/** 設定 是否有異常通報紀錄(申貸戶) **/
	public void setIsAbnormal(String isAbnormal) {
		this.isAbnormal = isAbnormal;
	}

	/** 取得是否有異常通報紀錄(申貸戶) **/
	public String getIsAbnormal() {
		return isAbnormal;
	}

	/** 設定查詢異常通報紀錄日期(申貸戶) **/
	public void setAbnormalReadDate(Date abnormalReadDate) {
		this.abnormalReadDate = abnormalReadDate;
	}

	/** 取得查詢異常通報紀錄日期(申貸戶) **/
	public Date getAbnormalReadDate() {
		return abnormalReadDate;
	}

	/** 設定 通報分行(申貸戶) **/
	public void setAbnormalBrNo(String abnormalBrNo) {
		this.abnormalBrNo = abnormalBrNo;
	}

	/** 取得通報分行(申貸戶) **/
	public String getAbnormalBrNo() {
		return abnormalBrNo;
	}

	/** 設定通報時間(申貸戶) **/
	public void setAbnormalDate(Date abnormalDate) {
		this.abnormalDate = abnormalDate;
	}

	/** 取得通報時間(申貸戶) **/
	public Date getAbnormalDate() {
		return abnormalDate;
	}

	/** 設定 目前異常通報狀態(申貸戶) **/
	public void setAbnormalStatus(String abnormalStatus) {
		this.abnormalStatus = abnormalStatus;
	}

	/** 取得 目前異常通報狀態(申貸戶) **/
	public String getAbnormalStatus() {
		return abnormalStatus;
	}

	/** 設定是否有異常通報紀錄(負責人) **/
	public void setIsAbnormal1(String isAbnormal1) {
		this.isAbnormal1 = isAbnormal1;
	}

	/** 取得是否有異常通報紀錄(負責人) **/
	public String getIsAbnormal1() {
		return isAbnormal1;
	}

	/** 設定異常通報負責人統編 **/
	public void setAbnormalChairmanId(String abnormalChairmanId) {
		this.abnormalChairmanId = abnormalChairmanId;
	}

	/** 取得異常通報負責人統編 **/
	public String getAbnormalChairmanId() {
		return abnormalChairmanId;
	}

	/** 設定異常通報負責人重覆序號 **/
	public void setAbnormalChairmanDupNo(String abnormalChairmanDupNo) {
		this.abnormalChairmanDupNo = abnormalChairmanDupNo;
	}

	/** 取得異常通報負責人重覆序號 **/
	public String getAbnormalChairmanDupNo() {
		return abnormalChairmanDupNo;
	}

	/** 設定查詢異常通報紀錄日期(負責人) **/
	public void setAbnormalReadDate1(Date abnormalReadDate1) {
		this.abnormalReadDate1 = abnormalReadDate1;
	}

	/** 取得查詢異常通報紀錄日期(負責人) **/
	public Date getAbnormalReadDate1() {
		return abnormalReadDate1;
	}

	/** 設定通報分行(負責人) **/
	public void setAbnormalBrNo1(String abnormalBrNo1) {
		this.abnormalBrNo1 = abnormalBrNo1;
	}

	/** 取得通報分行(負責人) **/
	public String getAbnormalBrNo1() {
		return abnormalBrNo1;
	}

	/** 設定通報時間(負責人) **/
	public void setAbnormalDate1(Date abnormalDate1) {
		this.abnormalDate1 = abnormalDate1;
	}

	/** 取得通報時間(負責人) **/
	public Date getAbnormalDate1() {
		return abnormalDate1;
	}

	/** 設定目前異常通報狀態(負責人) **/
	public void setAbnormalStatus1(String abnormalStatus1) {
		this.abnormalStatus1 = abnormalStatus1;
	}

	/** 取得目前異常通報狀態(負責人) **/
	public String getAbnormalStatus1() {
		return abnormalStatus1;
	}

	/**
	 * 設定異常通報簽報書MAINID(申貸戶)
	 * 
	 * @param abnormalMainId
	 */
	public void setAbnormalMainId(String abnormalMainId) {
		this.abnormalMainId = abnormalMainId;
	}

	/**
	 * 取得異常通報簽報書MAINID(申貸戶)
	 * 
	 * @param abnormalMainId
	 */
	public String getAbnormalMainId() {
		return abnormalMainId;
	}

	/**
	 * 設定異常通報簽報書MAINID(負責人)
	 * 
	 * @param abnormalMainId
	 */
	public void setAbnormalMainId1(String abnormalMainId1) {
		this.abnormalMainId1 = abnormalMainId1;
	}

	/**
	 * 取得異常通報簽報書MAINID(負責人)
	 * 
	 * @param abnormalMainId
	 */
	public String getAbnormalMainId1() {
		return abnormalMainId1;
	}

	/** 設定新客戶往來註記 **/
	public void setNewCustFlag(String newCustFlag) {
		this.newCustFlag = newCustFlag;
	}

	/** 取得新客戶往來註記 **/
	public String getNewCustFlag() {
		return newCustFlag;
	}

	/** 設定申貸戶是否有因不符ESG而暫緩承作 **/
	public void setIsEsgRejt(String isEsgRejt) {
		this.isEsgRejt = isEsgRejt;
	}

	/** 取得申貸戶是否有因不符ESG而暫緩承作 **/
	public String getIsEsgRejt() {
		return isEsgRejt;
	}

	/** 設定不符ESG而暫緩承作查詢日期 **/
	public void setEsgRejtReadDate(Date esgRejtReadDate) {
		this.esgRejtReadDate = esgRejtReadDate;
	}

	/** 取得不符ESG而暫緩承作查詢日期 **/
	public Date getEsgRejtReadDate() {
		return esgRejtReadDate;
	}

	/** 設定不符ESG而暫緩承作登錄分行 **/
	public void setEsgRejtBrNo(String esgRejtBrNo) {
		this.esgRejtBrNo = esgRejtBrNo;
	}

	/** 取得不符ESG而暫緩承作登錄分行 **/
	public String getEsgRejtBrNo() {
		return esgRejtBrNo;
	}

	/** 設定不符ESG而暫緩承作登錄行員 **/
	public void setEsgRejtEmpNo(String esgRejtEmpNo) {
		this.esgRejtEmpNo = esgRejtEmpNo;
	}

	/** 取得不符ESG而暫緩承作登錄行員 **/
	public String getEsgRejtEmpNo() {
		return esgRejtEmpNo;
	}

	/** 設定不符ESG而暫緩承登錄時間 **/
	public void setEsgRejtDate(Date esgRejtDate) {
		this.esgRejtDate = esgRejtDate;
	}

	/** 取得不符ESG而暫緩承登錄時間 **/
	public Date getEsgRejtDate() {
		return esgRejtDate;
	}

}
