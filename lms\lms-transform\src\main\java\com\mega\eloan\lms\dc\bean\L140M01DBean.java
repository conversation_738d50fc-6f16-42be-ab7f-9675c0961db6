package com.mega.eloan.lms.dc.bean;

import java.io.Serializable;

import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

public class L140M01DBean extends BaseBean implements Serializable {

	private static final long serialVersionUID = -9107087463525093782L;

	private String lmtType = "";// 限額類型
	private String lmtSeq = "";// 序號
	private String subject = "";// 授信科目組合 其值組成樣式 Ex:xxx|xxx|xxx…
	private String lmtCurr = "";// 限額－幣別
	private String lmtAmt = "";// 限額－金額

	public String getLmtType() {
		return lmtType;
	}

	public void setLmtType(String lmtType) {
		this.lmtType = lmtType;
	}

	public String getLmtSeq() {
		return lmtSeq;
	}

	public void setLmtSeq(String lmtSeq) {
		this.lmtSeq = lmtSeq;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getLmtCurr() {
		return lmtCurr;
	}

	public void setLmtCurr(String lmtCurr) {
		this.lmtCurr = lmtCurr;
	}

	public String getLmtAmt() {
		return lmtAmt;
	}

	public void setLmtAmt(String lmtAmt) {
		this.lmtAmt = lmtAmt;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer();
		sb.append(Util.nullToSpace(this.getOid()))
				.append(TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getMainId()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getLmtType()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getLmtSeq()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getSubject()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getLmtCurr()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getLmtAmt()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(this.getCreator()).append(TextDefine.FILE_DELIM);
		sb.append(this.getCreateTime()).append(TextDefine.FILE_DELIM);
		sb.append(this.getUpdater()).append(TextDefine.FILE_DELIM);
		sb.append(this.getUpdateTime());
		return sb.toString();
	}

}
