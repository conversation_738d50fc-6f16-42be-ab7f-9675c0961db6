var initDfd = initDfd || $.Deferred();

initDfd.done(function(json){
	if(json.c120m01a_list){
		var dyna = []
		var showBtn = false;
		if (userInfo && (userInfo.ssoUnitNo || '').indexOf("9") === 0) {
			showBtn = true;
        }
		var varVer = json.varVer;
		$.each(json.c120m01a_list.key, function(idx, jsonItem) {
			if(varVer == "2.0"){
				dyna.push("<table border='1' width='95%' class='tb2'>");
				
				dyna.push("<tr>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1035m01['label.caseBorrower']+(idx+1)+"</td>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1035m01['label.custPos']+"："+jsonItem.c120_custPos+"</td>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1035m01['label.custId']+"：<span class='the_link' data-c121m01d_oid='"+jsonItem.c121m01d_oid+"'>"+jsonItem.c120_custId+"</span></td>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1035m01['label.custName']+"："+jsonItem.c120_custName+"</td>");
				dyna.push("</tr>");
				
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.lms1035m01['C121M01A.varVer']+"</td>");//模型版本
				dyna.push("<td colspan='2'>"+varVer+"</td>");
				dyna.push("</tr>");
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.abstractoverseacls['l120s02.index94']+"</td>");//個人授信信用風險評等表
				dyna.push("<td colspan='2'>"+jsonItem.c120_o_grade+"</td>");
				dyna.push("</tr>");
				//---------
				//這個只有泰國要，越南不需要 
				if(jsonItem.showVeda){
					dyna.push("<tr>");
					dyna.push("<td colspan='2'>"+i18n.lms1035m01['tab07.note.hasNcb']+"</td>");//NCB Credit Report有無
					dyna.push("<td colspan='2'>");
					dyna.push("<input type='radio' name='o2_"+jsonItem.c120_oid+"' value='1' "+(jsonItem.c121m01d_chkItemTHO2=="1"?"checked":"")+" disabled='disabled'> ");			
					dyna.push(i18n.lms1035m01['tab07.note.hasNcb.Y']);
					dyna.push("&nbsp;&nbsp;");
					dyna.push("<input type='radio' name='o2_"+jsonItem.c120_oid+"' value='2' "+(jsonItem.c121m01d_chkItemTHO2=="2"?"checked":"")+" disabled='disabled'> ");			
					dyna.push(i18n.lms1035m01['tab07.note.hasNcb.N']);
					dyna.push("</td>");
					dyna.push("</tr>");
					//---------
					dyna.push("<tr>");
					dyna.push("<td colspan='2' style='vertical-align:top;'>"+i18n.lms1035m01['tab07.note.chkItemInfoNcb']+"</td>");//NCB資訊
					dyna.push("<td colspan='2'><b>"+jsonItem.c121m01d_chkItemInfoNcb+"</b>&nbsp;</td>");
					dyna.push("</tr>");
				}
				
				//---去除調整評等理由欄位，增加房貸/非房貸分隔條
				dyna.push("<tr>");
				dyna.push("<td colspan='2'></td>");
				dyna.push("<td class='hd2' width='25%'>"+i18n.lms1035m01['tab07.mortgage']+"</td>");
				dyna.push("<td class='hd2' width='25%'>"+i18n.lms1035m01['tab07.non-mortgage']+"</td>");
				dyna.push("</tr>");
				
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.lms1035m01['C121M01D.pRating']+"</td>");//初始評等
				dyna.push("<td>"+jsonItem.c121m01d_pRating+"</td>");
				dyna.push("<td>"+jsonItem.c121m01h_pRating+"</td>");
				dyna.push("</tr>");
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.lms1035m01['C121M01D.fRating']+"</td>");//最終評等
				dyna.push("<td>"+jsonItem.c121m01d_fRating+"</td>");
				dyna.push("<td>"+jsonItem.c121m01h_fRating+"</td>");
				dyna.push("</tr>");
				
				dyna.push("</table>");
				dyna.push("<br/>");
				
			}else{
				dyna.push("<table border='1' width='95%' class='tb2'>");
				
				dyna.push("<tr>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1035m01['label.caseBorrower']+(idx+1)+"</td>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1035m01['label.custPos']+"："+jsonItem.c120_custPos+"</td>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1035m01['label.custId']+"：<span class='the_link' data-c121m01d_oid='"+jsonItem.c121m01d_oid+"'>"+jsonItem.c120_custId+"</span></td>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1035m01['label.custName']+"："+jsonItem.c120_custName+"</td>");
				dyna.push("</tr>");
				
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.lms1035m01['C121M01A.varVer']+"</td>");//模型版本
				dyna.push("<td colspan='2'>"+varVer+"</td>");
				dyna.push("</tr>");
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.abstractoverseacls['l120s02.index94']+"</td>");//個人授信信用風險評等表
				dyna.push("<td colspan='2'>"+jsonItem.c120_o_grade+"</td>");
				dyna.push("</tr>");
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.lms1035m01['tab07.note.hasNcb']+"</td>");//NCB Credit Report有無
				dyna.push("<td colspan='2'>");
				dyna.push("<input type='radio' name='o2_"+jsonItem.c120_oid+"' value='1' "+(jsonItem.c121m01d_chkItemTHO2=="1"?"checked":"")+" disabled='disabled'> ");			
				dyna.push(i18n.lms1035m01['tab07.note.hasNcb.Y']);
				dyna.push("&nbsp;&nbsp;");
				dyna.push("<input type='radio' name='o2_"+jsonItem.c120_oid+"' value='2' "+(jsonItem.c121m01d_chkItemTHO2=="2"?"checked":"")+" disabled='disabled'> ");			
				dyna.push(i18n.lms1035m01['tab07.note.hasNcb.N']);
				dyna.push("</td>");
				dyna.push("</tr>");
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2' style='vertical-align:top;'>"+i18n.lms1035m01['tab07.note.chkItemInfoNcb']+"</td>");//NCB資訊
				dyna.push("<td colspan='2'><b>"+jsonItem.c121m01d_chkItemInfoNcb+"</b>&nbsp;</td>");
				dyna.push("</tr>");
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2' style='vertical-align:top;'>"+i18n.lms1035m01['tab07.note.overrideReason']+"</td>");//主觀評等更新理由
				dyna.push("<td colspan='2'>");
				if(jsonItem.c121m01d_adjustReason && jsonItem.c121m01d_adjustReason!=''){
					dyna.push("<textarea cols='80' rows='3' readonly='readonly' >"+jsonItem.c121m01d_adjustReason+"</textarea>");	
				}else{
					dyna.push("<textarea cols='80' readonly='readonly' >&nbsp;</textarea>");	
				}			
				dyna.push("</td>");
				dyna.push("</tr>");
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.lms1035m01['C121M01D.pRating']+"</td>");//初始評等
				dyna.push("<td colspan='2'>"+jsonItem.c121m01d_pRating+"</td>");
				dyna.push("</tr>");
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.lms1035m01['C121M01D.sRating']+"("+i18n.lms1035m01['tab07.note.sRating']+")"+"</td>");//獨立評等
				dyna.push("<td colspan='2'>"+jsonItem.c121m01d_sRating+"</td>");
				dyna.push("</tr>");
				dyna.push("<tr>");
				//---------
				dyna.push("<td colspan='2'>"+i18n.lms1035m01['C121M01D.fRating']+"("+i18n.lms1035m01['tab07.note.fRating']+")"+"</td>");//最終評等
				dyna.push("<td colspan='2'>"+jsonItem.c121m01d_fRating+"</td>");
				dyna.push("</tr>");
				
				dyna.push("</table>");
				dyna.push("<br/>");
			}

		});	
		
		$("#div07").html(dyna.join(""));
		if(showBtn){
			enable_list();
		}
	}
});

function enable_list(){
	$("span.the_link").addClass('enable_open_link');
	$("span.the_link").click(function(){
		if($(this).hasClass( "enable_open_link" )){
			var c121m01d_oid = $(this).attr("data-c121m01d_oid");
			
			$.form.submit({ url:'../lms1035m02'
				, data:{
						 'noOpenDoc':true
						,'c121m01d_oid': c121m01d_oid
					}				
				, target:c121m01d_oid});
		}
		
	});
}
