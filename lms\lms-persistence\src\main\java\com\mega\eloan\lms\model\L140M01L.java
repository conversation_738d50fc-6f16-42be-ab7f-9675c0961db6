/* 
 * L140M01L.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 團貸案資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M01L", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L140M01L extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 團貸案項目
	 * <p/>
	 * 1.母戶<br/>
	 * 2.子戶
	 */
	@Size(max = 1)
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String itemType;

	/**
	 * 團貸案種類
	 * <p/>
	 * 1.房貸案件<br/>
	 * 2.消貸案件
	 */
	@Size(max = 1)
	@Column(name = "LOANTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String loanType;

	/** 團貸案代碼 **/
	@Size(max = 10)
	@Column(name = "CASECODE", length = 10, columnDefinition = "VARCHAR(10)")
	private String caseCode;

	/**
	 * 團貸案名稱
	 * <p/>
	 * 建案名稱或整批貸款專案名稱
	 */
	@Size(max = 120)
	@Column(name = "CASENAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String caseName;

	/**
	 * 簽案年度
	 * <p/>
	 * YYYY
	 */
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "CASEYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer caseYear;

	/** 呈報分行 **/
	@Size(max = 3)
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/**
	 * 總申請額度(幣別)
	 * <p/>
	 * TWD
	 */
	@Size(max = 3)
	@Column(name = "LOANCURR", length = 3, columnDefinition = "CHAR(3)")
	private String loanCurr;

	/**
	 * 總申請額度(金額)
	 * <p/>
	 * 單位：元
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "LOANAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal loanAmt;

	/**
	 * 團貸總戶案號
	 * <p/>
	 * eg.918110001739
	 */
	@Size(max = 12)
	@Column(name = "LOANMASTERNO", length = 12, columnDefinition = "CHAR(12)")
	private String loanMasterNo;

	/**
	 * 土地座落(縣市)
	 * <p/>
	 * (代碼)※參考擔保品土地明細
	 */
	@Size(max = 10)
	@Column(name = "CITYID", length = 10, columnDefinition = "VARCHAR(10)")
	private String cityId;

	/**
	 * 土地座落(鄉鎮市區)
	 * <p/>
	 * (代碼)※參考擔保品土地明細
	 */
	@Size(max = 3)
	@Column(name = "AREAID", length = 3, columnDefinition = "VARCHAR(3)")
	private String areaId;

	/**
	 * 土地座落(段小段)
	 * <p/>
	 * (代碼)※參考擔保品土地明細
	 */
	@Size(max = 4)
	@Column(name = "IR48", length = 4, columnDefinition = "CHAR(4)")
	private String Ir48;

	/**
	 * 土地坐落區-大段
	 * <p/>
	 * 2013-03-29_新增欄位<br/>
	 * 土地坐落區-大段
	 */
	@Size(max = 15)
	@Column(name = "LANDPART1", length = 15, columnDefinition = "VARCHAR(15)")
	private String landpart1;

	/**
	 * 土地坐落區-小段
	 * <p/>
	 * 2013-03-29_新增欄位<br/>
	 * 土地坐落區-小段
	 */
	@Size(max = 15)
	@Column(name = "LANDPART2", length = 15, columnDefinition = "VARCHAR(15)")
	private String landpart2;

	/**
	 * 總行額度有效期限（月數）
	 * <p/>
	 * 自總行核准額度後xx月內有效
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "EXPMONTHS", columnDefinition = "DECIMAL(3,0)")
	private Integer expMonths;

	/**
	 * 總行額度有效起迄日期－起
	 * <p/>
	 * 總戶簽報書核准日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "EXPBDATE", columnDefinition = "DATE")
	private Date expBDate;

	/**
	 * 總行額度有效起迄日期－迄
	 * <p/>
	 * 總戶簽報書核准日期 + 加總行額度有效期限月數
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "EXPEDATE", columnDefinition = "DATE")
	private Date expEDate;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;
	
	/** 敘作理由 **/
	@Column(name = "TODOREASON", columnDefinition = "CHAR(1)")
	private String toDoReason;
	
	/** 不動產分區 **/
	@Column(name = "REALESTATEAREA", columnDefinition = "VARCHAR(2)")
	private String realEstateArea;

	/** 土融額度序號 **/
	@Column(name = "LANDLOANCNTRNO", columnDefinition = "CHAR(12)")
	private String landLoanCntrNo;
	
	/** 建融額度序號 **/
	@Column(name = "CONSTRUCTIONLANDCNTRNO", columnDefinition = "CHAR(12)")
	private String constructionLandCntrNo;
	
	/** 預計完工日 **/
	@Column(name = "EXPECTEDFINISHDATE", columnDefinition = "DATE")
	private Date expectedFinishDate;
	
	/** 整批房貸額度不再動用 **/
	@Column(name = "NOTUSINGMARK", columnDefinition = "CHAR(1)")
	private String notUsingMark;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得團貸案項目
	 * <p/>
	 * 1.母戶<br/>
	 * 2.子戶
	 */
	public String getItemType() {
		return this.itemType;
	}

	/**
	 * 設定團貸案項目
	 * <p/>
	 * 1.母戶<br/>
	 * 2.子戶
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/**
	 * 取得團貸案種類
	 * <p/>
	 * 1.房貸案件<br/>
	 * 2.消貸案件
	 */
	public String getLoanType() {
		return this.loanType;
	}

	/**
	 * 設定團貸案種類
	 * <p/>
	 * 1.房貸案件<br/>
	 * 2.消貸案件
	 **/
	public void setLoanType(String value) {
		this.loanType = value;
	}

	/** 取得團貸案代碼 **/
	public String getCaseCode() {
		return this.caseCode;
	}

	/** 設定團貸案代碼 **/
	public void setCaseCode(String value) {
		this.caseCode = value;
	}

	/**
	 * 取得團貸案名稱
	 * <p/>
	 * 建案名稱或整批貸款專案名稱
	 */
	public String getCaseName() {
		return this.caseName;
	}

	/**
	 * 設定團貸案名稱
	 * <p/>
	 * 建案名稱或整批貸款專案名稱
	 **/
	public void setCaseName(String value) {
		this.caseName = value;
	}

	/**
	 * 取得簽案年度
	 * <p/>
	 * YYYY
	 */
	public Integer getCaseYear() {
		return this.caseYear;
	}

	/**
	 * 設定簽案年度
	 * <p/>
	 * YYYY
	 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/** 取得呈報分行 **/
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/** 設定呈報分行 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/**
	 * 取得總申請額度(幣別)
	 * <p/>
	 * TWD
	 */
	public String getLoanCurr() {
		return this.loanCurr;
	}

	/**
	 * 設定總申請額度(幣別)
	 * <p/>
	 * TWD
	 **/
	public void setLoanCurr(String value) {
		this.loanCurr = value;
	}

	/**
	 * 取得總申請額度(金額)
	 * <p/>
	 * 單位：元
	 */
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}

	/**
	 * 設定總申請額度(金額)
	 * <p/>
	 * 單位：元
	 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/**
	 * 取得團貸總戶案號
	 * <p/>
	 * eg.918110001739
	 */
	public String getLoanMasterNo() {
		return this.loanMasterNo;
	}

	/**
	 * 設定團貸總戶案號
	 * <p/>
	 * eg.918110001739
	 **/
	public void setLoanMasterNo(String value) {
		this.loanMasterNo = value;
	}

	/**
	 * 取得土地座落(縣市)
	 * <p/>
	 * (代碼)※參考擔保品土地明細
	 */
	public String getCityId() {
		return this.cityId;
	}

	/**
	 * 設定土地座落(縣市)
	 * <p/>
	 * (代碼)※參考擔保品土地明細
	 **/
	public void setCityId(String value) {
		this.cityId = value;
	}

	/**
	 * 取得土地座落(鄉鎮市區)
	 * <p/>
	 * (代碼)※參考擔保品土地明細
	 */
	public String getAreaId() {
		return this.areaId;
	}

	/**
	 * 設定土地座落(鄉鎮市區)
	 * <p/>
	 * (代碼)※參考擔保品土地明細
	 **/
	public void setAreaId(String value) {
		this.areaId = value;
	}

	/**
	 * 取得土地座落(段小段)
	 * <p/>
	 * (代碼)※參考擔保品土地明細
	 */
	public String getIr48() {
		return this.Ir48;
	}

	/**
	 * 設定土地座落(段小段)
	 * <p/>
	 * (代碼)※參考擔保品土地明細
	 **/
	public void setIr48(String value) {
		this.Ir48 = value;
	}

	/**
	 * 取得土地坐落區-大段
	 * <p/>
	 * 2013-03-29_新增欄位<br/>
	 * 土地坐落區-大段
	 */
	public String getLandpart1() {
		return this.landpart1;
	}

	/**
	 * 設定土地坐落區-大段
	 * <p/>
	 * 2013-03-29_新增欄位<br/>
	 * 土地坐落區-大段
	 **/
	public void setLandpart1(String value) {
		this.landpart1 = value;
	}

	/**
	 * 取得土地坐落區-小段
	 * <p/>
	 * 2013-03-29_新增欄位<br/>
	 * 土地坐落區-小段
	 */
	public String getLandpart2() {
		return this.landpart2;
	}

	/**
	 * 設定土地坐落區-小段
	 * <p/>
	 * 2013-03-29_新增欄位<br/>
	 * 土地坐落區-小段
	 **/
	public void setLandpart2(String value) {
		this.landpart2 = value;
	}

	/**
	 * 取得總行額度有效期限（月數）
	 * <p/>
	 * 自總行核准額度後xx月內有效
	 */
	public Integer getExpMonths() {
		return this.expMonths;
	}

	/**
	 * 設定總行額度有效期限（月數）
	 * <p/>
	 * 自總行核准額度後xx月內有效
	 **/
	public void setExpMonths(Integer value) {
		this.expMonths = value;
	}

	/**
	 * 取得總行額度有效起迄日期－起
	 * <p/>
	 * 總戶簽報書核准日期
	 */
	public Date getExpBDate() {
		return this.expBDate;
	}

	/**
	 * 設定總行額度有效起迄日期－起
	 * <p/>
	 * 總戶簽報書核准日期
	 **/
	public void setExpBDate(Date value) {
		this.expBDate = value;
	}

	/**
	 * 取得總行額度有效起迄日期－迄
	 * <p/>
	 * 總戶簽報書核准日期 + 加總行額度有效期限月數
	 */
	public Date getExpEDate() {
		return this.expEDate;
	}

	/**
	 * 設定總行額度有效起迄日期－迄
	 * <p/>
	 * 總戶簽報書核准日期 + 加總行額度有效期限月數
	 **/
	public void setExpEDate(Date value) {
		this.expEDate = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 模糊比對結果<br/>
	 * 400個中文字
	 **/
	@Column(name = "CHECKRESULT", length = 3200, columnDefinition = "VARCHAR(3200)")
	private String checkResult;

	/**
	 * 模糊比對結果<br/>
	 * 400個中文字
	 **/
	public String getCheckResult() {
		return checkResult;
	}

	/**
	 * 模糊比對結果<br/>
	 * 400個中文字
	 **/
	public void setCheckResult(String checkResult) {
		this.checkResult = checkResult;
	}

	/**
	 * 是否為建商貸款<br/>
	 * Y|是<br/>
	 * N|否<br/>
	 * 此選項在loanType =1 時用來判斷
	 * **/
	@Size(max = 1)
	@Column(name = "ISBUILDER", length = 1, columnDefinition = "CHAR(1)")
	private String isBuilder;

	/**
	 * 是否為建商貸款<br/>
	 * Y|是<br/>
	 * N|否<br/>
	 * 此選項在loanType =1 時用來判斷
	 * **/
	public String getIsBuilder() {
		return isBuilder;
	}

	/**
	 * 是否為建商貸款<br/>
	 * Y|是<br/>
	 * N|否<br/>
	 * 此選項在loanType =1 時用來判斷
	 * **/
	public void setIsBuilder(String isBuilder) {
		this.isBuilder = isBuilder;
	}

	public String getToDoReason() {
		return toDoReason;
	}

	public void setToDoReason(String toDoReason) {
		this.toDoReason = toDoReason;
	}

	public String getRealEstateArea() {
		return realEstateArea;
	}

	public void setRealEstateArea(String realEstateArea) {
		this.realEstateArea = realEstateArea;
	}

	public String getLandLoanCntrNo() {
		return landLoanCntrNo;
	}

	public void setLandLoanCntrNo(String landLoanCntrNo) {
		this.landLoanCntrNo = landLoanCntrNo;
	}

	public String getConstructionLandCntrNo() {
		return constructionLandCntrNo;
	}

	public void setConstructionLandCntrNo(String constructionLandCntrNo) {
		this.constructionLandCntrNo = constructionLandCntrNo;
	}

	public Date getExpectedFinishDate() {
		return expectedFinishDate;
	}

	public void setExpectedFinishDate(Date expectedFinishDate) {
		this.expectedFinishDate = expectedFinishDate;
	}

	public String getNotUsingMark() {
		return notUsingMark;
	}

	public void setNotUsingMark(String notUsingMark) {
		this.notUsingMark = notUsingMark;
	}
	
}
