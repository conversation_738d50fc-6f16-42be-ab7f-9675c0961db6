package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1131R08RptService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01W;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01W;

import tw.com.iisi.cap.utils.CapEntityUtil;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 證券暨期貨違約交割紀錄
 * </pre>
 *
 * <AUTHOR>
 * @version <ul>
 * <li>2020/01/20, EL08034
 * </ul>
 * @since 2020/01/20
 */
@Service("cls1131r08rptservice")
public class CLS1131R08RptServiceImpl implements FileDownloadService, CLS1131R08RptService {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1131R08RptServiceImpl.class);


	@Resource
	CLS1131Service cls1131Service;

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 *
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params) throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			String custId = Util.trim(params.getString("custId"));
			String dupNo = Util.trim(params.getString("dupNo"));

			boolean isC120M01A = params.getBoolean("isC120M01A");
			int incomeVersion = 0;
			if (isC120M01A) {
				C120S01B bean = cls1131Service.findModelByKey(C120S01B.class, mainId, custId, dupNo);
				incomeVersion = bean.getIncomeDetailVer() == null ? 0 : bean.getIncomeDetailVer().intValue();
			} else {
				C101S01B bean = cls1131Service.findModelByKey(C101S01B.class, mainId, custId, dupNo);
				incomeVersion = bean.getIncomeDetailVer() == null ? 0 : bean.getIncomeDetailVer().intValue();
			}


			if (incomeVersion == 0) {
				baos = (ByteArrayOutputStream) this.generateReport(params);
			} else {
				baos = (ByteArrayOutputStream) this.generateReportVersion(params, incomeVersion);
			}

			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}

	}

	/**
	 * 建立PDF
	 * @param params
	 * @return
	 * @throws FileNotFoundException
	 * @throws IOException
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public OutputStream generateReport(PageParameters params) throws FileNotFoundException, IOException, Exception {

		OutputStream outputStream = null;
		Locale locale = null;
		Map<String, String> rptVariableMap = null;
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		try {
			boolean isC120M01A = params.getBoolean("isC120M01A");
			if (isC120M01A) {
				C120S01B c120s01b = cls1131Service.findModelByKey(C120S01B.class, mainId, custId, dupNo);
				if (c120s01b != null) {
					rptVariableMap = new LinkedHashMap<String, String>();
					for (String key : CapEntityUtil.getColumnName(c120s01b)) {
						rptVariableMap.put(key, Util.trim(c120s01b.get(key)));
					}
				}
			} else {
				C101S01B c101s01b = cls1131Service.findModelByKey(C101S01B.class, mainId, custId, dupNo);
				if (c101s01b != null) {
					rptVariableMap = new LinkedHashMap<String, String>();
					for (String key : CapEntityUtil.getColumnName(c101s01b)) {
						rptVariableMap.put(key, Util.trim(c101s01b.get(key)));
					}
				}
			}
			locale = LMSUtil.getLocale();

			ReportGenerator generator = new ReportGenerator("report/cls/CLS1131R08_" + locale.toString() + ".rpt");

			generator.setVariableData(rptVariableMap);

			LOGGER.info("into generateReport");
			outputStream = generator.generateReport();
			LOGGER.info("exit generateReport");


		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}

	public OutputStream generateReportVersion(PageParameters params, int version) throws Exception {

		OutputStream outputStream = null;
		Locale locale = null;
		Map<String, String> rptVariableMap = null;
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		try {
			boolean isC120M01A = params.getBoolean("isC120M01A");
			if (isC120M01A) {
				List<C120S01W> c120s01ws = (List<C120S01W>) cls1131Service.findListByRelationKey(C120S01W.class, mainId, custId, dupNo);

				//List<C101S01W> c101s01ws = (List<C101S01W>)ClsUtil.changeModelList(C120S01W.class, c120s01ws);
				rptVariableMap = new LinkedHashMap<String, String>();
				if (CollectionUtils.isNotEmpty(c120s01ws)) {
					for (C120S01W w : c120s01ws) {
						String keyString = Util.trim(w.getKeyString());
						String valueString = Util.trim(w.getValueString());
						rptVariableMap.put(keyString, Util.trim(valueString));
					}
				}

				C120S01B c120s01b = cls1131Service.findModelByKey(C120S01B.class, mainId, custId, dupNo);
				if (c120s01b != null) {
					rptVariableMap.put("payAmt", Util.trim(c120s01b.getPayAmt()));
					rptVariableMap.put("othAmt", Util.trim(c120s01b.getOthAmt()));
					rptVariableMap.put("updateTime", c120s01b.getUpdateTime() == null ? "": sdf.format(c120s01b.getUpdateTime()));
				}

				C120M01A meta = cls1131Service.findModelByKey(C120M01A.class, mainId, custId, dupNo);
				if (meta != null) {
					rptVariableMap.put("custName", Util.trim(meta.getCustName()) + " " + Util.trim(meta.getCustId()));
				}
			} else {
				List<C101S01W> c101s01ws = (List<C101S01W>) cls1131Service.findListByRelationKey(C101S01W.class, mainId, custId, dupNo);
				rptVariableMap = new LinkedHashMap<String, String>();
				if (CollectionUtils.isNotEmpty(c101s01ws)) {
					for (C101S01W w : c101s01ws) {
						String keyString = Util.trim(w.getKeyString());
						String valueString = Util.trim(w.getValueString());
						rptVariableMap.put(keyString, Util.trim(valueString));
					}
				}

				C101S01B c101s01b = cls1131Service.findModelByKey(C101S01B.class, mainId, custId, dupNo);
				if (c101s01b != null) {
					rptVariableMap.put("payAmt", Util.trim(c101s01b.getPayAmt()));
					rptVariableMap.put("othAmt", Util.trim(c101s01b.getOthAmt()));
					rptVariableMap.put("updateTime", c101s01b.getUpdateTime() == null ? "": sdf.format(c101s01b.getUpdateTime()));
				}
				C101M01A meta = cls1131Service.findModelByKey(C101M01A.class, mainId, custId, dupNo);
				if (meta != null) {
					rptVariableMap.put("custName", Util.trim(meta.getCustName()) + " " + Util.trim(meta.getCustId()));
				}
			}
			locale = LMSUtil.getLocale();

			ReportGenerator generator = new ReportGenerator("report/cls/CLS1131R08V"+ version + "_" + locale.toString() + ".rpt");

			generator.setVariableData(rptVariableMap);

			LOGGER.info("into generateReport");
			outputStream = generator.generateReport();
			LOGGER.info("exit generateReport");


		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}


}
