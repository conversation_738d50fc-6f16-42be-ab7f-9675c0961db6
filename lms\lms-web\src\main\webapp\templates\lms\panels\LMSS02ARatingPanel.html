<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
		
<body>
	<wicket:panel>
		<div>
    		<span class="text-red"><wicket:message key="title.clsRating">依據本行「消金授信業務信用評等應用細則」第三條：「對於消金授信之借款人、連帶保證人或共同借款人，應分別辦理信用評等，並就其評等結果擇一適用</wicket:message>
			</span>
    	</div>
		<fieldset>
			<legend>
				<b><wicket:message key="l120s01a.subtitle1">借款人明細</wicket:message></b>
			</legend>
			<div id="lmss02aratingpanelFuncContainer" class="funcContainer">
				&nbsp;&nbsp;
				<button type="button" id="btnImpRatingDoc">
					<span class="text-only"><wicket:message key="btn.importRatingDoc">引進評等文件</wicket:message></span>
				</button>
				&nbsp;&nbsp;
				<button type="button" id="btnDelRatingDoc">
					<span class="text-only"><wicket:message key="btn.delRatingDoc">刪除評等文件</wicket:message></span>
				</button>
				&nbsp;&nbsp;
				<button type="button" id="btnChooseKeyBorrower">
					<span class="text-only"><wicket:message key="btn.setCaseDocKeyMan">設定主要借款人</wicket:message></span>
				</button>
				&nbsp;&nbsp;
				<button type="button" id="btnAddOtherCust">
					<span class="text-only"><wicket:message key="btn.addOtherCust">新增從債務人</wicket:message></span>
				</button>
			</div>
			<div id="ratingPanelGrid" width="100%" style="margin-left: 10px; margin-right: 10px">
			</div>
		</fieldset>
		<!--============-->
		<div id="thickBoxFromRatingDoc" style="display:none">
			<table border='0' class='tb2'>
					<tr>
						<td class='hd2'><span id="label_findRatingId" />&nbsp;</td>
						<td><input type="text" id="findRatingId" name="findRatingId" maxlength="10" size="10" class="upText" />
							&nbsp;&nbsp;			
							<button type="button" id="findRatingIdBt">
			                    <span class="text-only"><wicket:message key="button.filter">篩選</wicket:message></span>					
			                </button>			
						</td>
					</tr>				
                </table>
			<div id='grid_fromRatingDoc' />			
		</div>
		<!--============-->
		<div id="thickboxaddborrow" style="display: none;">
			<form id="addborrowForm">				
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td width="20%" class="hd1">
							<input type="radio" name="rborrowA" checked="checked" value="1" onclick="$('._rborrowa').attr('disabled',false);$('._rborrowb').val('').attr('disabled',true);"/>
							<wicket:message key="l120s01a.custid">身分證統編</wicket:message>&nbsp;&nbsp;						
						</td>
						<td width="80%">
							<input type="text" id="addborrowForm_custId" name="addborrowForm_custId" class="max upText _rborrowa" size="10" maxlength="10" />
							&nbsp;&nbsp; 
							<input type="hidden" id="addborrowForm_dupNo" name="addborrowForm_dupNo" value="">
							<button type="button" id="getCustData" name="getCustData">
								<span class="text-only"><wicket:message key="button.pullin">引進</wicket:message></span>
							</button>
						</td>
					</tr>
					<tr>
						<td width="20%" class="hd1">
							<input type="radio" name="rborrowA" value="2" onclick="$('._rborrowa').val('').attr('disabled',true);$('._rborrowb').attr('disabled',false);"/>
							<wicket:message key="l120s01a.custname">借款人姓名</wicket:message>&nbsp;&nbsp;
						</td>
						<td width="80%">
							<input type="text" id="addborrowForm_custName" name="addborrowForm_custName" class="max _rborrowb" maxlength="120" disabled="true" /> &nbsp;&nbsp;							
						</td>
					</tr>
					<tr>
						<td colspan="2" class="text-red">
							<wicket:message key="l120s01a.other16">說明...</wicket:message>
						</td>
					</tr>
				</table>
			</form>
		</div>
				
		<script type="text/javascript" src="pagejs/lms/LMSS02ARatingPanel.js"></script>	
	</wicket:panel>
</body>
</html>
