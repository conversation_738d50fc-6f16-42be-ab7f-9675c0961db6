/* 
 * L730S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L730S01A;

/** 授信報案考核表明細檔 **/
public interface L730S01ADao extends IGenericDao<L730S01A> {

	L730S01A findByOid(String oid);
	
	List<L730S01A> findByMainId(String mainId);
	
	L730S01A findByUniqueKey(String mainId, Date chkYM, String sysType, String itemType, String itemNo);

	List<L730S01A> findByIndex01(String mainId, Date chkYM, String sysType, String itemType, String itemNo);
}