/* 
 * L141M01CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L141M01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;

import com.mega.eloan.lms.model.L141M01C;


/** 共同借款人檔 **/
@Repository
public class L141M01CDaoImpl extends LMSJpaDao<L141M01C, String> implements
		L141M01CDao {

	@Override
	public L141M01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L141M01C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L141M01C> list = createQuery(L141M01C.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L141M01C findByUniqueKey(String mainId, String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L141M01C> findByIndex01(String mainId, String custId,
			String dupNo) {
		ISearch search = createSearchTemplete();
		List<L141M01C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L141M01C.class, search).getResultList();
		}
		return list;
	}
	@Override
	public List<L141M01C> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L141M01C> list = createQuery(L141M01C.class,search).getResultList();
		return list;
	}
	@Override
	public List<L141M01C> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		List<L141M01C> list = createQuery(L141M01C.class, search)
				.getResultList();

		return list;
	}
}