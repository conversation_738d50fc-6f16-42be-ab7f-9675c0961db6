/* 
 * L918S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 授管處停權明細檔 **/
@NamedEntityGraph(name = "L918S01A-entity-graph", attributeNodes = { @NamedAttributeNode("l918m01a") })
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L918S01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L918S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false)		
	private L918M01A l918m01a;
	
	public L918M01A getL918m01a() {
		return l918m01a;
	}
	public void setL918m01a(L918M01A l918m01a) {
		this.l918m01a = l918m01a;
	}
	
	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 設定案件編號<p/>
	 * NOT NULL
	 */
	@Column(name="SETDOCNO", length=50, columnDefinition="CHAR(50)")
	private String setDocNo;

	/** 
	 * 分行別<p/>
	 * NOT NULL
	 */
	@Column(name="BRANCHNO", length=3, columnDefinition="CHAR(3)")
	private String branchNo;

	/** 客戶統編 **/
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 停權代碼<p/>
	 * 1 逾期 2 拒往 3 財異 4 退票
	 */
	@Column(name="SUSPENDCODE", length=1, columnDefinition="CHAR(1)")
	private String suspendCode;

	/** 
	 * 停權月數<p/>
	 * NOT NULL
	 */
	@Column(name="SUSPENDMONS", columnDefinition="DECIMAL(2, 0)")
	private Integer suspendMons;

	/** 
	 * 修改停權月數<p/>
	 * 判斷是否需要註記為修改、刪除用<br/>
	 *  刪除此欄位值設為0
	 */
	@Column(name="MODLIFYMONS", columnDefinition="DECIMAL(2, 0)")
	private Integer modlifyMons;

	/** 
	 * 本次停權月數<p/>
	 * NOT NULL
	 */
	@Column(name="CHANGEMONS", columnDefinition="DECIMAL(2, 0)")
	private Integer changeMons;

	/** 
	 * 停權狀態碼<p/>
	 * NOT NULL<br/>
	 *  狀態碼 1- 有效 2- 失效
	 */
	@Column(name="STOPSTATUS", length=1, columnDefinition="CHAR(1)")
	private String stopStatus;

	/** 最近簽案後首撥日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LOANDATE", columnDefinition="DATE")
	private Date loanDate;

	/** 逾期設定日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="OVDATE", columnDefinition="DATE")
	private Date ovDate;

	/** 
	 * 設定單位<p/>
	 * NOT NULL
	 */
	@Column(name="SETDEPART", length=3, columnDefinition="CHAR(3)")
	private String setDepart;

	/** 
	 * 設定覆核人員<p/>
	 * NOT NULL
	 */
	@Column(name="SETEMPNO", length=6, columnDefinition="CHAR(6)")
	private String setEmpNo;

	/** 
	 * 設定日期<p/>
	 * NOT NULL
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="SETDATE", columnDefinition="DATE")
	private Date setDate;

	/** 
	 * 修改單位<p/>
	 * NOT NULL
	 */
	@Column(name="MODDEPART", length=3, columnDefinition="CHAR(3)")
	private String modDepart;

	/** 
	 * 修改覆核人員<p/>
	 */
	@Column(name="MODEMPNO", length=6, columnDefinition="CHAR(6)")
	private String modEmpNo;

	/** 
	 * 修改日期<p/>
	 * NOT NULL
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="MODDATE", columnDefinition="DATE")
	private Date modDate;
	
	/** 
	 * 修改案件編號<p/>
	 * NOT NULL
	 */
	@Column(name="MODDOCNO", length=50, columnDefinition="CHAR(50)")
	private String modDocNo;

	/** 
	 * 設定文件編號<p/>
	 * NOT NULL
	 */
	@Column(name="SETMAINID", length=32, columnDefinition="CHAR(32)")
	private String setMainId;

	/** 
	 * 修改文件編號<p/>
	 * NOT NULL
	 */
	@Column(name="MODMAINID", length=32, columnDefinition="CHAR(32)")
	private String modMainId;

	/** 
	 * 系統代碼<p/>
	 * 企金 LMS/消金 CLS
	 */
	@Column(name="CLASSNO", length=3, columnDefinition="CHAR(3)")
	private String classNo;

	/** 
	 * 狀態Flag<p/>
	 * 新增：A<br/>
	 *  修改：M<br/>
	 *  刪除：D<br/>
	 *  新增後刪除：R
	 */
	@Column(name="STATUSFLAG", length=1, columnDefinition="CHAR(1)")
	private String statusFlag;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String Creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得設定案件編號<p/>
	 * NOT NULL
	 */
	public String getSetDocNo() {
		return this.setDocNo;
	}
	/**
	 *  設定設定案件編號<p/>
	 *  NOT NULL
	 **/
	public void setSetDocNo(String value) {
		this.setDocNo = value;
	}

	/** 
	 * 取得分行別<p/>
	 * NOT NULL
	 */
	public String getBranchNo() {
		return this.branchNo;
	}
	/**
	 *  設定分行別<p/>
	 *  NOT NULL
	 **/
	public void setBranchNo(String value) {
		this.branchNo = value;
	}

	/** 取得客戶統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定客戶統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得停權代碼<p/>
	 * 1 逾期 2 拒往 3 財異 4 退票
	 */
	public String getSuspendCode() {
		return this.suspendCode;
	}
	/**
	 *  設定停權代碼<p/>
	 *  1 逾期 2 拒往 3 財異 4 退票
	 **/
	public void setSuspendCode(String value) {
		this.suspendCode = value;
	}

	/** 
	 * 取得停權月數<p/>
	 * NOT NULL
	 */
	public Integer getSuspendMons() {
		return this.suspendMons;
	}
	/**
	 *  設定停權月數<p/>
	 *  NOT NULL
	 **/
	public void setSuspendMons(Integer value) {
		this.suspendMons = value;
	}

	/** 
	 * 取得修改停權月數<p/>
	 * 判斷是否需要註記為修改、刪除用<br/>
	 *  刪除此欄位值設為0
	 */
	public Integer getModlifyMons() {
		return this.modlifyMons;
	}
	/**
	 *  設定修改停權月數<p/>
	 *  判斷是否需要註記為修改、刪除用<br/>
	 *  刪除此欄位值設為0
	 **/
	public void setModlifyMons(Integer value) {
		this.modlifyMons = value;
	}

	/** 
	 * 取得本次停權月數<p/>
	 * NOT NULL
	 */
	public Integer getChangeMons() {
		return this.changeMons;
	}
	/**
	 *  設定本次停權月數<p/>
	 *  NOT NULL
	 **/
	public void setChangeMons(Integer value) {
		this.changeMons = value;
	}

	/** 
	 * 取得停權狀態碼<p/>
	 * NOT NULL<br/>
	 *  狀態碼 1- 有效 2- 失效
	 */
	public String getStopStatus() {
		return this.stopStatus;
	}
	/**
	 *  設定停權狀態碼<p/>
	 *  NOT NULL<br/>
	 *  狀態碼 1- 有效 2- 失效
	 **/
	public void setStopStatus(String value) {
		this.stopStatus = value;
	}

	/** 取得最近簽案後首撥日 **/
	public Date getLoanDate() {
		return this.loanDate;
	}
	/** 設定最近簽案後首撥日 **/
	public void setLoanDate(Date value) {
		this.loanDate = value;
	}

	/** 取得逾期設定日 **/
	public Date getOvDate() {
		return this.ovDate;
	}
	/** 設定逾期設定日 **/
	public void setOvDate(Date value) {
		this.ovDate = value;
	}

	/** 
	 * 取得設定單位<p/>
	 * NOT NULL
	 */
	public String getSetDepart() {
		return this.setDepart;
	}
	/**
	 *  設定設定單位<p/>
	 *  NOT NULL
	 **/
	public void setSetDepart(String value) {
		this.setDepart = value;
	}

	/** 
	 * 取得設定覆核人員<p/>
	 * NOT NULL
	 */
	public String getSetEmpNo() {
		return this.setEmpNo;
	}
	/**
	 *  設定設定覆核人員<p/>
	 *  NOT NULL
	 **/
	public void setSetEmpNo(String value) {
		this.setEmpNo = value;
	}

	/** 
	 * 取得設定日期<p/>
	 * NOT NULL
	 */
	public Date getSetDate() {
		return this.setDate;
	}
	/**
	 *  設定設定日期<p/>
	 *  NOT NULL
	 **/
	public void setSetDate(Date value) {
		this.setDate = value;
	}

	/** 
	 * 取得修改單位<p/>
	 * NOT NULL
	 */
	public String getModDepart() {
		return this.modDepart;
	}
	/**
	 *  設定修改單位<p/>
	 *  NOT NULL
	 **/
	public void setModDepart(String value) {
		this.modDepart = value;
	}

	/** 
	 * 取得修改覆核人員<p/>
	 * NOT NULL
	 */
	public String getModEmpNo() {
		return this.modEmpNo;
	}
	/**
	 *  設定修改覆核人員<p/>
	 *  NOT NULL
	 **/
	public void setModEmpNo(String value) {
		this.modEmpNo = value;
	}

	/** 
	 * 取得設定日期<p/>
	 * NOT NULL
	 */
	public Date getModDate() {
		return this.modDate;
	}
	
	/**
	 *  設定設定日期<p/>
	 *  NOT NULL
	 **/
	public void setModDate(Date value) {
		this.modDate = value;
	}	
	
	/** 
	 * 取得修改案件編號<p/>
	 * NOT NULL
	 */
	public String getModDocNo() {
		return this.modDocNo;
	}
	/**
	 *  設定修改案件編號<p/>
	 *  NOT NULL
	 **/
	public void setModDocNo(String value) {
		this.modDocNo = value;
	}

	/** 
	 * 取得設定文件編號<p/>
	 * NOT NULL
	 */
	public String getSetMainId() {
		return this.setMainId;
	}
	/**
	 *  設定設定文件編號<p/>
	 *  NOT NULL
	 **/
	public void setSetMainId(String value) {
		this.setMainId = value;
	}

	/** 
	 * 取得修改文件編號<p/>
	 * NOT NULL
	 */
	public String getModMainId() {
		return this.modMainId;
	}
	/**
	 *  設定修改文件編號<p/>
	 *  NOT NULL
	 **/
	public void setModMainId(String value) {
		this.modMainId = value;
	}

	/** 
	 * 取得系統代碼<p/>
	 * 企金 LMS/消金 CLS
	 */
	public String getClassNo() {
		return this.classNo;
	}
	/**
	 *  設定系統代碼<p/>
	 *  企金 LMS/消金 CLS
	 **/
	public void setClassNo(String value) {
		this.classNo = value;
	}

	/** 
	 * 取得狀態Flag<p/>
	 * 新增：A<br/>
	 *  修改：M<br/>
	 *  刪除：D<br/>
	 *  新增後刪除：R
	 */
	public String getStatusFlag() {
		return this.statusFlag;
	}
	/**
	 *  設定狀態Flag<p/>
	 *  新增：A<br/>
	 *  修改：M<br/>
	 *  刪除：D<br/>
	 *  新增後刪除：R
	 **/
	public void setStatusFlag(String value) {
		this.statusFlag = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.Creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.Creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
