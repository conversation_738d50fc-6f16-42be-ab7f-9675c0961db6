/* 
 * FSS1010Service.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.F101M01A;
import com.mega.eloan.lms.model.F101S01A;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;

/**
 * <pre>
 * 一般財務報表 Service 介面。
 * </pre>
 * 
 * @since 2011/7/26
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/26,Sun<PERSON><PERSON> Wang,new</li>
 *          <li>2011/7/28,<PERSON><PERSON><PERSON>,update for add comment</li>
 *          <li>2011/8/03,<PERSON><PERSON><PERSON>,update for
 *          {@link FSS1010GService#getF1010M01(String)}</li>
 *          <li>2011/8/05,<PERSON>kist Wang,update for add
 *          {@link FSS1010GService#hasTimeFssBas(String)}</li>
 *          <li>2011/8/09,Sunkist Wang,update for add
 *          {@link FSS1010GService#getAccCode(String)}
 *          <li>2011/8/13,Sunkist Wang,update for add
 *          {@link FSS1010GService#deleteF101M01A(String)}
 *          <li>2011/8/17,Sunkist Wang,update for add
 *          {@link FSS1010GService#findF101S01AByMetaAndSubNo(String, String[])}
 *          <li>2011/8/18,Sunkist Wang,update for add
 *          {@link FSS1010GService#isDuplicate(String, String, String, String, BigDecimal, String)}
 *          檢查是否有重覆資料
 *          <li>2011/8/18,Sunkist Wang,update 增加取得前端財報的介面
 *          {@link FSS1010GService#findPreDocs(String, String, String, String, String, String, BigDecimal)}
 *          <li>2011/8/18,Sunkist Wang,update 取得速動資產金額
 *          {@link FSS1010GService#findSpeedAssets(String)}
 *          <li>2011/8/22,Sunkist Wang, update findPreDocs()帶入的參數
 *          <li>2011/8/22,Sunkist Wang, update findPreDocPages()
 *          針對畫面grid上取得前期財報資料。
 *          <li>2011/8/24,Sunkist Wang,add 給信評引進的 findForOriginalMow()介面。
 *          <li>2011/8/24,Sunkist Wang, add findFssRatio() 取得所有財務比率資料。
 *          <li>2011/8/24,Sunkist Wang, 信評相關介面，搬家到FSS1010GService
 *          <li>2011/8/24,Sunkist Wang, 預估相關
 *          </ul>
 */
public interface FSS1010GService extends AbstractService {

	static final String NEW_DOC = "NewDoc";

	/**
	 * it's tempSave use.
	 * 
	 * @param entity
	 *            F101M01A
	 * @param tempSave
	 *            是否為TempSave
	 */
	void save(F101M01A entity, boolean tempSave);

	/**
	 * 儲存一般財務報表及其關聯RelativeMeta。
	 * 
	 * @param entity
	 *            F101M01A
	 * @param isNewDoc
	 *            是否為新件
	 */
	void saveDocument(F101M01A entity, boolean isNewDoc);

	/**
	 * 確認一般財務報表及其關聯RelativeMeta。
	 * 
	 * @param entity
	 *            F101M01A
	 * @param isNewDoc
	 *            是否為新件
	 */
	void saveAndSend(F101M01A entity, boolean isNewDoc);

	/**
	 * 刪除一般財務報表主檔
	 * 
	 * @param oid
	 *            oid
	 */
	void deleteF101M01A(String oid);

	/**
	 * 取得 F101M01A model.
	 * <ul>
	 * <li>
	 * {@link FSS1010M01FormHandler#tempSave(org.apache.wicket.PageParameters, org.apache.wicket.Component)}
	 * <li>
	 * {@link FSS1010M01FormHandler#save(org.apache.wicket.PageParameters, org.apache.wicket.Component)}
	 * </ul>
	 * 
	 * @param oid
	 *            sys pk
	 * @return F1010M01A
	 */
	F101M01A getF101M01(String oid);

	/**
	 * 是否有時報基本資料檔 F101M03A model.
	 * 
	 * @param custId
	 *            客戶統編
	 * @return boolean
	 */
	boolean hasTimeFssBas(String custId);

	/**
	 * 檢查是否有相同共用規則的鍵值。
	 * 
	 * @param custId
	 *            統一編號
	 * @param dupNo
	 *            重覆序號
	 * @param conso
	 *            合併/非合併
	 * @param publicFlag
	 *            公開/非公開
	 * @param year
	 *            年度
	 * @param periodType
	 *            報表類型
	 * @param mainId
	 *            自己的mainId
	 * @param docstatus
	 *            狀態
	 * @param type
	 *            一般/預估
	 * @return boolean
	 */
	boolean isDuplicate(String custId, String dupNo, String conso,
			String publicFlag, String year, String periodType, String mainId,
			String docstatus, String type);

	/**
	 * 依meta 的 mainId 、報表別以及科目代號取得財務3表。
	 * 
	 * @param mainId
	 *            the F101S01A's mainId
	 * @param tab
	 *            報表別
	 * @param subNos
	 *            subNos array
	 * @return F101S01A 3 table of FSS
	 */
	List<F101S01A> findTabByMetaAndSubNo(String mainId, String tab,
			String[] subNos);

	/**
	 * {@link #findTabByMetaAndSubNo(String, String, String[])}
	 * 
	 * @param mainId
	 *            the F101S01A's mainId
	 * @param tab
	 *            報表別
	 * @return F101S01A 3 table of FSS
	 */
	List<F101S01A> findTabByMetaAndSubNo(String mainId, String tab);

	/**
	 * {@link #findTabByMetaAndSubNo(String, String, String[])}
	 * 
	 * @param mainId
	 *            the F101S01A's mainId
	 * @param subNos
	 *            subNos array
	 * @return F101S01A 3 table of FSS
	 */
	List<F101S01A> findTabByMetaAndSubNo(String mainId, String[] subNos);

	/**
	 * 
	 * {@link #findTabByMetaAndSubNo(String, String, String[])}
	 * 
	 * @param mainId
	 *            the F101S01A's mainId
	 * @return F101S01A 3 table of FSS
	 */
	List<F101S01A> findTabByMetaAndSubNo(String mainId);

	/**
	 * 取得前期財報資料。
	 * 
	 * @param branch
	 *            登入者單位代號
	 * @param custId
	 *            統編
	 * @param dupNo
	 *            重覆序號
	 * @param docStatusCode
	 *            docStatus
	 * @param periodType
	 *            財務報表年度類型
	 * @param fssTypeCode
	 *            財務報表類別：一般
	 * @param conso
	 *            輸入合併/非合併
	 * @param year
	 *            前期報表年度
	 * @param mustOnlyPeriodType
	 *            是否一定要找完整年度財報
	 * @return F101M01As
	 */
	List<F101M01A> findPreDocs(String branch, String custId, String dupNo,
			String docStatusCode, String periodType, String fssTypeCode,
			String conso, String year, boolean mustOnlyPeriodType);

	/**
	 * 取得前期財報資料。(for grid)
	 * 
	 * @param branch
	 *            登入者單位代號
	 * @param custId
	 *            統編
	 * @param dupNo
	 *            重覆序號
	 * @param docStatusCode
	 *            docStatus
	 * @param periodType
	 *            財務報表年度類型
	 * @param fssTypeCode
	 *            財務報表類別：一般
	 * @param conso
	 *            輸入合併/非合併
	 * @param year
	 *            前期報表年度
	 * @param mustOnlyPeriodType
	 *            是否一定要找完整年度財報
	 * @return Page<F101M01A>
	 */
	Page<F101M01A> findPreDocsPages(String branch, String custId, String dupNo,
			String docStatusCode, String periodType, String fssTypeCode,
			String conso, String year, boolean mustOnlyPeriodType);

	/**
	 * 新增前期資料。
	 * 
	 * @param meta
	 *            本期財務報表
	 * @param preDoc
	 *            前一期財務報表
	 * @throws CapException
	 */
	void createPreFss(F101M01A meta, F101M01A preDoc) throws CapException;

	/**
	 * 依mainId取得財務主檔。
	 * 
	 * @param cMainId
	 *            by mainId
	 * @return F101M01A
	 */
	F101M01A getF101M01byMainId(String cMainId);

	/**
	 * 依報表類型(typCd)取得月數。
	 * 
	 * <pre>
	 * 年報、半年報（6個月）、第一季季報（3個月）、第二季季報（6個月，等同半年報）、第三季季報（9個月）、第四季季報（12個月，等同年報）、
	 * 其他 以報表期間起迄之年月相減取得其實際月數
	 * </pre>
	 * 
	 * @param periodType
	 *            periodType
	 * @param s
	 *            startDate
	 * @param e
	 *            endDate
	 * @return int 月數。
	 * @throws CapException
	 */
	int getMonthsByTypCd(String periodType, Date s, Date e) throws CapException;

	/**
	 * 設定Flow
	 * 
	 * @param flowName
	 *            流程名稱
	 * @param mainOid
	 *            oid
	 * @param action
	 *            動作
	 */
	void flowControl(String flowName, String mainOid, String action);

	/**
	 * 依此財報主檔， 查詢出已存在的已確認表單，將之調整為歷史狀態。 若查無結果，則略過儲存歷史的動作。
	 * 
	 * @param meta
	 *            財報主檔
	 */
	void saveHistory(F101M01A meta);

	/**
	 * 複製財報
	 * 
	 * @param mainIds
	 *            array of meta's mainId
	 * @return Map<String, String> put in new mainIds
	 */
	Map<String, String> copyDocuments(String[] mainIds);

	/**
	 * 檢查會計科目是否有重覆新增。
	 * 
	 * @param mainOid
	 *            F101M04A mainOid
	 * @param accCode
	 *            accCode
	 * @return boolean
	 */
	boolean isAccTitleDuplicate(String mainOid, String accCode);

	/**
	 * 刪除時報資訊資料
	 * 
	 * @param timeInfoOid
	 *            F101S04A mainOid
	 */
	void deleteTimeInfo(String timeInfoOid);

	/**
	 * get list of F101M01A model by mainId array. and it order by eDate asc.
	 * 
	 * @param mainIds
	 *            String[]
	 * @return List<F101M01A>
	 */
	List<F101M01A> findFssMetaList(String[] mainIds);

	/**
	 * 刪除時報轉檔上傳的檔案。
	 * 
	 * @param oids
	 *            檔案 oid 陣列
	 */
	void deleteDocFiles(String[] oids);

	/**
	 * 時報轉檔批次狀態記錄。
	 * 
	 * @param fileKey
	 *            String
	 */
	void saveTimeFssBatchLog(String fileKey);
	
	List<Map<String, Object>> findFss(String[] mainId, String[][] fssCols) throws CapException;
	
	Map<String, Object> findFss(String mainId, String[][] fssCols) throws CapException;
	
	/**
	 * 排序MainId,依照EDate,由大到小排序
	 * 
	 * @param mainIds
	 *            mainIds
	 * @return
	 */
	List<String> getSortMainId(String[] mainIds);	
	
}
