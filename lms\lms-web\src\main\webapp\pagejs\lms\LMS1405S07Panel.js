var oldOid = "";
var _handler = "";
$(function() {
	//主要申請敘作內容
	gridViewShow();
	A_1_10_4();
	A_1_10_3();
	//授信額度異動情形
	gridViewShow2();
	A_1_10_5();

	$("#showSel04").change(function(i){
/*
		$("#showGrid").show();
		$("#gridviewAA").jqGrid("setGridParam", {//重新設定grid需要查到的資料
			postData : {
				formAction:"queryL140m01a2",
				custId : $("#showSel04 option:selected").val()
			},
			search: true
		}).trigger("reloadGrid");
*/
		$("#showBrid").show();
		$("#formSearch").find("#textBrid").val(userInfo.unitNo);
	});
	$("#buttonSearch04").click(function(){
		if($("#formSearch").valid()){
	        $.ajax({
	            type: "POST",
	            handler: "lms1401s07formhandler",
	            data: {
	                formAction: "getCustData",
	                custId: $("#formSearch").find("#searchId04").val() }
	            }).done(function(responseData04){
	                // alert(JSON.stringify(responseData));
				      var selJson04 = {
					       		item : responseData04.selCus,
					       		format : "{value} - {key}",
					       		space: true
					       	};
				      $("#selCus04").setItems(selJson04);
				      $("#showSel04").show();
					  $("#selCus04").show();
	        });
		}
	});
	$("#buttonSearch05").click(function(){
		if($("#formSearch").valid()){
			$("#showGrid").show();
			$("#gridviewAA").jqGrid("setGridParam", {//重新設定grid需要查到的資料
				postData : {
					formAction:"queryL140m01a2",
					custId : $("#showSel04 option:selected").val(),
					textBrid : $("#textBrid").val()
				},
				page : 1,
				search: true
			}).trigger("reloadGrid");
		}
	});
});

function gridViewShow(){
	var gridView = $("#gridviewShow").iGrid({
		handler: 'lms1201gridhandler',
		//height: 345, //for 15 筆
		height: "230px", //for 10 筆
		//autoHeight: true,
		width: "100%",
		postData : {
			formAction : "queryL120s16a",
			mainId : responseJSON.mainid,
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		sortname: 'printSeq|custId|cntrNo',
        sortorder: 'asc|asc|asc',
		multiselect: true,
		hideMultiselect:false,
		// autofit: false,
		autowidth:true,
		needPager: false,
		colModel: [{
		  colHeader: i18n.lms1405s07["L140S16A.gridb"],//"借款人"
		  name: 'custId',
		  align:"left",
		  width: 110,
		  sortable: true,
		  formatter : 'click',
		  onclick : openDoc2
		}, {
			  colHeader: i18n.lms1405s07["L140S16A.grid4"],//"額度序號"
			  width: 90,
			  name: 'cntrNo',
			  sortable: true
		}, {
			  //J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
			  colHeader: i18n.lms1405s07["L140S16A.grid28"],//性質
			  width: 60,
			  name: 'property',
			  sortable: false 	  
		}, {
		  colHeader: i18n.lms1405s07["L140S16A.grid3"],//"科目"
		  name: 'lnSubject',
		  width: 80,
		  sortable: true

		}, {
		  colHeader: i18n.lms1405s07["L140S16A.grid5"],//期限
		  name: 'payDeadline',
		  width: 110,
		  sortable: true,
		  align:"left"
		}, {
		  colHeader: i18n.lms1405s07["L140S16A.grid6"],//"連保人"
		  name: 'guarantor',
		  width: 80,
		  sortable: true,
		  align:"left"
//		}, {
//		  colHeader: "<span class='text-red'>"+i18n.lms1401s07['L140S16A.grid8']+"</span>",//列印模式
//		  name: 'printMode',
//		  width:50,
//		  sortable: false,
//		  align:"center"
		},{
	      colHeader: "&nbsp",//"檢核欄位",
	      name: 'chkYN',
	      width: 20,
	      sortable: false,
	      align:"center"
	    },{
		  name: 'oid',
		  hidden: true
		}],
			ondblClickRow: function(rowid){
				var data = gridView.getRowData(rowid);
				openDoc2(null, null, data);
		}
	});
}
function A_1_10_4(){
	$("#gridviewCC").iGrid({
   	 handler: "lms1405gridhandler",
        height: 200,
        rownumbers:true,
		multiselect : true,
		hideMultiselect : false,
		caption: "&nbsp;",
		hiddengrid : false,
        sortname: 'printSeq|custId|cntrNo',
        sortorder: 'asc|asc|asc',
        postData : {
				formAction:"queryL140m01a",
				itemType:"1",
				includeNoChange:false
			},
        colModel: [{
            colHeader: i18n.lms1405s07["L140S16A.gridb"],//"借款人"
            width: 140,
            name: 'custName',
            sortable: false
        }, {
            colHeader: i18n.lms1405s07["L140M01a.cntrNo"],//"額度序號",
            name: 'cntrNo',
            width: 80,
            sortable: false
        }, {
            colHeader: i18n.lms1405s07["L140M01a.cntrNoCom"],//"共用額度序號",
            name: 'commSno',
            width: 80,
            sortable: false
        }, {
            colHeader: "&nbsp;",
            name: 'currentApplyCurr',
            width: 25,
            sortable: false,
			 align:"center"
        }, {
            colHeader: i18n.lms1405s07["L140M01a.moneyAmt"],//現請額度,
            name: 'currentApplyAmt',
            width: 100,
            sortable: false,
			 align:"right",
			 formatter:'currency',
			 formatoptions:
			   {
			    thousandsSeparator: ",",
				removeTrailingZero: true,
			    decimalPlaces: 2//小數點到第幾位
			    }
        }, {
            colHeader: i18n.lms1405s07["L140M01a.type"],//"性質"
            name: 'proPerty',
            width: 70,
            sortable: false,
            align:"center",
            formatter:proPertyFormatter
        }, {
            colHeader:i18n.lms1405s07["L140M01a.docStatus"], //"文件狀態",
            name: 'docStatus',
            width: 60,
            sortable: false,
			 align:"center"
        }, {
            colHeader: i18n.lms1405s07["L140M01a.branchId"],//"分行別",
            name: 'ownBrId',
            width: 80,
            sortable: false,
			 align:"center"
        },	{
            name: 'oid',
            hidden: true
        },{
            name: 'printSeq',
            hidden: true
        }],
        	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
    });
}

function A_1_10_3(){
var grid = $("#gridviewAA").iGrid({
  handler: 'lms1201gridhandler',
  //height: 345, //for 15 筆
  height: "230px", //for 10 筆
  //autoHeight: true,
  width: 500,
	postData : {
		formAction : "queryL140m01a2",
		custId : $("#formSearch").find("#searchId").val(),
		textBrid : $("#textBrid").val(),
		rowNum:10
	},
  sortname: 'caseDate',
//  multiselect: true,hideMultiselect:false,
 // autofit: false,
  autowidth:true,
  caption: "&nbsp;",
  hiddengrid : false,
  colModel: [{
      colHeader: i18n.lms1405s07["L140S16A.grid31"],//"簽案日期"
      name: 'caseDate',
		align:"center",
		width: 60,
      sortable: false
  }, {
      colHeader: i18n.lms1405s07["L140S16A.grid32"],//"案號",
      name: 'caseNo',
      width: 150,
      sortable: false
  }, {
      colHeader: i18n.lms1405s07["L140S16A.grid44"],//"明細表/批覆表",
      name: 'itemType',
      width: 80,
      sortable: false,
	  formatter : function(data){
		/*
		L140S16A.grid45=額度明細表
		L140S16A.grid46=額度批覆表
		L140S16A.grid47=母行法人提案意見
		*/
		switch(parseInt(data)){
			case 1:
				return i18n.lms1405s07["L140S16A.grid45"];
			case 2:
				return i18n.lms1405s07["L140S16A.grid46"];
			case 3:
				return i18n.lms1405s07["L140S16A.grid47"];
			default:
				return "";
		};
	  }
  }, {
      colHeader: i18n.lms1405s07["L140S16A.grid33"],//"額度序號",
      name: 'cntrNo',
      width: 80,
      sortable: false,
		align:"center"
  }, {
      colHeader: i18n.lms1405s07["L140S16A.grid34"],//"文件狀態",
      name: 'docStatus',
      width: 30,
      sortable: false
  }, {
      colHeader: i18n.lms1405s07["L140S16A.grid35"],//"現請額度",
      name: 'currentApplyAmt',
      width: 80,
      sortable: false,
	  align:"right"
  }, {
      colHeader: i18n.lms1405s07["L140S16A.grid36"],//"科目",
      name: 'lnSubject',
      width:50,
      sortable: false,
		align:"center"
  },{
      name: 'mainId',
      hidden: true
  }],
  	ondblClickRow: function(rowid){
  }
});
}


/**
 * 讀取利害關係人以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc2(cellvalue, options, rowObject) {
	ilog.debug(rowObject);
	$.ajax({
		handler : "lms1401s07formhandler",
		type : "POST",
		dataType : "json",
		action : "queryL120s16b",
		data : {
			oid : rowObject.oid,
			requery : false }
		}).done(function(obj) {
			var $tLMS1401S07Form01 = $("#tLMS1401S07Form01");
			bbbb999(obj.tLMS1401S07Form01.oid,obj.propertyIsNotChange);
			$tLMS1401S07Form01.setData(obj.tLMS1401S07Form01);
			$tLMS1401S07Form01.find("#oldOid").val(obj.tLMS1401S07Form01.oid);
			if(obj.tLMS1401S07Form01.printMode == '2'){
				$tLMS1401S07Form01.find("[name='printMode']:eq(1)").prop("checked",true);
			}else{
				$tLMS1401S07Form01.find("[name='printMode']:eq(0)").prop("checked",true);
			}
	});
}


/**
 * 刪除 主要敘作條件內容
 */
function deleteL120s16a() {
	var rows = $("#gridviewShow").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for ( var i = 0; i < rows.length; i++) { // 將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
			var data = $("#gridviewShow").getRowData(rows[i]);
			list += ((list == "") ? "" : sign) + data.oid;
		}
	}
	if (list == "") {
		CommonAPI.showMessage(i18n.lms1405s07["L140S16A.alert1"]);
		return;
	}
	$.ajax({
		handler : "lms1401s07formhandler",
		type : "POST",
		dataType : "json",
		action : "deleteL120s16b",
		data : {
			listOid : list,
			sign : sign }
		}).done(function(json) {
			$("#LMS1401S07Form01").find("#gridviewShow").trigger("reloadGrid");
	});
	$.thickbox.close();
}

/**
 * 新增主要敘作條件內容
 */
function openbox222() {
	
	 
	$("#gridviewCC").jqGrid("setGridParam", {
        postData: {
        	itemType:"1",
    		includeNoChange:false
        }
    }).trigger("reloadGrid");
	
	
	$("#openbox222")
			.thickbox(
					{ // 使用選取的內容進行彈窗
						title : i18n.lms1405s07["L140S16A.thickbox15"],// '額度明細表選擇'
						width : 800,
						height : 400,
						modal : true,
						align : "center",
						valign : "bottom",
						i18n : i18n.lms1405s07,
						readOnly : thickboxOptions.readOnly,
						buttons : {
							"L140S16A.thickbox1" : function() {
								var rows = $("#gridviewCC").getGridParam(
										'selarrrow');
								var list = "";
								var sign = ",";
								for ( var i = 0; i < rows.length; i++) { // 將所有已選擇的資料存進變數list裡面
									if (rows[i] != 'undefined'
											&& rows[i] != null && rows[i] != 0) {
										var data = $("#gridviewCC").getRowData(
												rows[i]);
										list += ((list == "") ? "" : sign)
												+ data.oid;
									}
								}
								if (list == "") {
									CommonAPI
											.showMessage(i18n.lms1405s07["L140S16A.alert1"]);
									return;
								}
								$.ajax({
									handler : "lms1401s07formhandler",
									type : "POST",
									dataType : "json",
									action : "addL120s16a",
									data : {
										listOid : list,
										mainId : responseJSON.mainid,
										sign : sign }
									}).done(function(json) {
										$("#LMS1401S07Form01").find(
												"#gridviewShow").trigger(
												"reloadGrid");
								});
								$.thickbox.close();
							},
							"L140S16A.thickbox2" : function() {
								API.confirmMessage(i18n.def['flow.exit'],
										function(res) {
											if (res) {
												$.thickbox.close();
											}
										});
							}
						}
					});
}

//主要敘作條件-thickBox
function bbbb999(oid,propertyIsNotChange){
	
	  //J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
	  if(propertyIsNotChange == "Y"){
		  $(".propertyIsNotChange").show();
	  }else{
		  $(".propertyIsNotChange").show();
	  }
	  
	  $("#borrower-data921").thickbox({	// 使用選取的內容進行彈窗
	    title : i18n.lms1405s07["L140S16A.thickbox14"],//'利害關係人授信條件對照表'
	    width : 700,
	    height : 500,
	    modal : true,
	    i18n:i18n.def,
	    readOnly : thickboxOptions.readOnly,
	    buttons: {
			  	"saveData": function() {
					var $tLMS1401S07Form01 = $("#tLMS1401S07Form01");
					if($tLMS1401S07Form01.valid()){
						$.ajax({		//查詢主要借款人資料
							handler : "lms1401s07formhandler",
							type : "POST",
							dataType : "json",
							action : "saveL120s16b",
							data : {
								oid : oid,
								tLMS1401S07Form01 : JSON.stringify($tLMS1401S07Form01.serializeData()) }
							}).done(function(json) {
								$("#LMS1401S07Form01").find("#gridviewShow").trigger("reloadGrid");
						});
						$.thickbox.close();
					}
			  	},
				"close": function() {
					 API.confirmMessage(i18n.def['flow.exit'], function(res){
							if(res){
								$.thickbox.close();
							}
				        });
				}
		     }
		});
}


function getLocalData() {
	var $tLMS1401S07Form01 = $("#tLMS1401S07Form01");
	var oid = $tLMS1401S07Form01.find("#oldOid").val();
	$.ajax({
		handler : "lms1401s07formhandler",
		type : "POST",
		dataType : "json",
		action : "queryL120s16bl",
		data : {
			oid : oid,
			requery : true }
		}).done(function(obj) {
			var $tLMS1401S07Form01 = $("#tLMS1401S07Form01");
			$tLMS1401S07Form01.setData(obj.tLMS1401S07Form01, false);
	});
}



function gridViewShow2(){
	var gridView2 = $("#gridviewShow2").iGrid({
		handler: 'lms1201gridhandler',
		//height: 345, //for 15 筆
		height: "230px", //for 10 筆
		//autoHeight: true,
		width: "100%",
		postData : {
			formAction : "queryL120s16c",
			mainId : responseJSON.mainid,
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		sortname: 'printSeq|custId',
	    sortorder: 'asc|asc',
		multiselect: true,hideMultiselect:false,
		// autofit: false,
		autowidth:true,
		needPager: false,
		colModel: [{
		  colHeader: i18n.lms1405s07["L140S16A.gridb"],//"借款人"
		  name: 'custId',
		  align:"left",
		  width: 110,
		  sortable: true,
		  formatter : 'click',
		  onclick : openDoc3
		}, {
		  colHeader: i18n.lms1405s07["L120S16C.lvTotAmt"],//前准額度
		  name: 'lvTotAmt',
		  width: 80,
		  sortable: true

		}, {
		  colHeader: i18n.lms1405s07["L120S16C.blTotAmt"],//餘額
		  name: 'blTotAmt',
		  width: 80,
		  sortable: true,
		  align:"left"
		}, {
		  colHeader: i18n.lms1405s07["L120S16C.incApplyTotAmt"],//增加減少
		  name: 'incApplyTotAmt',
		  width: 80,
		  sortable: true,
		  align:"left"
		}, {
			  colHeader: i18n.lms1405s07["L120S16C.incApplyMemo"],//額度增減說明
			  name: 'incApplyMemo',
			  width: 80,
			  sortable: true,
			  align:"left"
		}, {
			  colHeader: i18n.lms1405s07["L120S16C.loanTotAmt"],//授信總額度
			  name: 'loanTotAmt',
			  width: 80,
			  sortable: true,
			  align:"left"
		}, {
			  colHeader: i18n.lms1405s07["L120S16C.assureTotAmt"],//其中擔保
			  name: 'assureTotAmt',
			  width: 80,
			  sortable: true,
			  align:"left"
		},{
	      colHeader: "&nbsp",//"檢核欄位",
	      name: 'chkYN',
	      width: 20,
	      sortable: false,
	      align:"center"
	    },{
		  name: 'oid',
		  hidden: true
		},{
            name: 'printSeq',
            hidden: true
        }],
			ondblClickRow: function(rowid){
				var data = gridView2.getRowData(rowid);
				openDoc3(null, null, data);
		}
	});
}
function A_1_10_5(){
	$("#gridviewDD").iGrid({
   	 handler: "lms1201gridhandler",
        height: 200,
        rownumbers:true,
		multiselect : true,
		hideMultiselect : false,
		caption: "&nbsp;",
		hiddengrid : false,
		sortname: 'keyMan|custShowSeqNum|custId|dupNo',
		sortorder: 'desc|asc|asc|asc',
        postData : {
	        	formAction: "queryL120s01a",
	            rowNum: 500
			},
		rowNum: 500,
		colModel: [{
            colHeader: "&nbsp;", // 主要借款人Flag
            align: "center",
            width: 10, // 設定寬度
            sortable: false, // 是否允許排序
            name: 'keyMan' // col.id
        }, {
            colHeader: i18n.lms1405s07["l120s01a.custid"], // 身分證統編
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lms1405s07["l120s01a.custname2"], // 借款人名稱
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lms1405s07["l120s01a.custrlt"], // 與主要借款人關係
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custRlt' // col.id
        }, {
            colHeader: i18n.lms1405s07["l120s01a.custpos"], // 相關身份
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custPos' // col.id
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
    });
}



/**
 * 讀取利害關係人以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc3(cellvalue, options, rowObject) {
	ilog.debug(rowObject);
	$.ajax({
		handler : "lms1401s07formhandler",
		type : "POST",
		dataType : "json",
		action : "queryL120s16c",
		data : {
			oid : rowObject.oid,
			requery : false }
		}).done(function(obj) {
			var $tLMS1401S07Form02 = $("#tLMS1401S07Form02");
			bbbb888(obj.tLMS1401S07Form02.oid);
			$tLMS1401S07Form02.setData(obj.tLMS1401S07Form02);
			$tLMS1401S07Form02.find("#oldOid").val(obj.tLMS1401S07Form02.oid);
	});
}


/**
 * 新增授信額度異動情形
 */
function openbox333() {
	
	$("#gridviewDD").jqGrid("setGridParam", {
    }).trigger("reloadGrid");
	
	$("#openbox333")
			.thickbox(
					{ // 使用選取的內容進行彈窗
						title : i18n.lms1405s07["L140S16A.thickbox6"],// 借款人選擇
						width : 800,
						height : 400,
						modal : true,
						align : "center",
						valign : "bottom",
						i18n : i18n.lms1405s07,
						readOnly : thickboxOptions.readOnly,
						buttons : {
							"L140S16A.thickbox1" : function() {
								var rows = $("#gridviewDD").getGridParam(
										'selarrrow');
								var list = "";
								var sign = ",";
								for ( var i = 0; i < rows.length; i++) { // 將所有已選擇的資料存進變數list裡面
									if (rows[i] != 'undefined'
											&& rows[i] != null && rows[i] != 0) {
										var data = $("#gridviewDD").getRowData(
												rows[i]);
										list += ((list == "") ? "" : sign)
												+ data.oid;
									}
								}
								if (list == "") {
									CommonAPI
											.showMessage(i18n.lms1405s07["L140S16A.alert1"]);
									return;
								}
								$.ajax({
									handler : "lms1401s07formhandler",
									type : "POST",
									dataType : "json",
									action : "addL120s16c",
									data : {
										listOid : list,
										mainId : responseJSON.mainid,
										sign : sign }
									}).done(function(json) {
										$("#LMS1401S07Form02").find(
												"#gridviewShow2").trigger(
												"reloadGrid");
								});
								$.thickbox.close();
							},
							"L140S16A.thickbox2" : function() {
								API.confirmMessage(i18n.def['flow.exit'],
										function(res) {
											if (res) {
												$.thickbox.close();
											}
										});
							}
						}
					});
}


//主要敘作條件-thickBox
function bbbb888(oid){
	  $("#borrower-data888").thickbox({	// 使用選取的內容進行彈窗
	    title : i18n.lms1405s07["L140S16A.thickbox20"],//授信額度異動情形內容
	    width : 800,
	    height : 500,
	    modal : true,
	    i18n:i18n.def,
	    readOnly : thickboxOptions.readOnly,
	    buttons: {
			  	"saveData": function() {
					var $tLMS1401S07Form02 = $("#tLMS1401S07Form02");
					if($tLMS1401S07Form02.valid()){
						$.ajax({		//查詢主要借款人資料
							handler : "lms1401s07formhandler",
							type : "POST",
							dataType : "json",
							action : "saveL120s16c",
							data : {
								oid : oid,
								tLMS1401S07Form02 : JSON.stringify($tLMS1401S07Form02.serializeData()) }
							}).done(function(json) {
								$("#LMS1401S07Form02").find("#gridviewShow2").trigger("reloadGrid");
						});
						$.thickbox.close();
					}
			  	},
				"close": function() {
					 API.confirmMessage(i18n.def['flow.exit'], function(res){
							if(res){
								$.thickbox.close();
							}
				        });
				}
		     }
		});
}


function getLocalData2() {
	var $tLMS1401S07Form02 = $("#tLMS1401S07Form02");
	var oid = $tLMS1401S07Form02.find("#oldOid").val();
	$.ajax({
		handler : "lms1401s07formhandler",
		type : "POST",
		dataType : "json",
		action : "queryL120s16cl",
		data : {
			oid : oid,
			mainId : responseJSON.mainid,
			requery : true }
		}).done(function(obj) {
			var $tLMS1401S07Form02 = $("#tLMS1401S07Form02");
			$tLMS1401S07Form02.setData(obj.tLMS1401S07Form02, false);
	});
}

/**
 * 刪除 授信額度異動情形
 */
function deleteL120s16c() {
	var rows = $("#gridviewShow2").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for ( var i = 0; i < rows.length; i++) { // 將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
			var data = $("#gridviewShow2").getRowData(rows[i]);
			list += ((list == "") ? "" : sign) + data.oid;
		}
	}
	if (list == "") {
		CommonAPI.showMessage(i18n.lms1405s07["L140S16A.alert1"]);
		return;
	}
	$.ajax({
		handler : "lms1401s07formhandler",
		type : "POST",
		dataType : "json",
		action : "deleteL120s16c",
		data : {
			listOid : list,
			sign : sign }
		}).done(function(json) {
			$("#LMS1401S07Form02").find("#gridviewShow2").trigger("reloadGrid");
	});
	$.thickbox.close();
}


/**
 * 同步額度明細表列印順序
 */
function resetL120s16aPrintSeq() {

	$.ajax({
		handler : "lms1401s07formhandler",
		type : "POST",
		dataType : "json",
		action : "resetL120s16aPrintSeq",
		data : {
			mainId : responseJSON.mainid }
		}).done(function(json) {
			$("#LMS1401S07Form01").find("#gridviewShow").trigger("reloadGrid");
			$("#LMS1401S07Form01").find("#gridviewShow2").trigger("reloadGrid");
	});
	$.thickbox.close();
}

/**  登錄性質的格式化  */
function proPertyFormatter(cellvalue, otions, rowObject){
    var itemName = '';

    if (cellvalue) {
        var list = cellvalue.split("|");
        itemName = i18n.lms1405s02["L140M01a.type" + list[0]];
        if (cellvalue.length > 1) {
            for (var i = 1; i < list.length; i++) {
                var itemone = i18n.lms1405s02["L140M01a.type" + list[i]];
                itemName = itemName + "、" + itemone;
            }//close for
        }//close if
    }
    return itemName;
}

initDfd.done(function(auth) {
	if (thickboxOptions.readOnly) {
		$("#LMS1401S07Form01").find("button").hide();
		$("#LMS1401S07Form02").find("button").hide();

		$("#tLMS1401S07Form01").readOnlyChilds(true);
		$("#tLMS1401S07Form02").readOnlyChilds(true);
		$("#tLMS1401S07Form01").find("button").hide();
		$("#tLMS1401S07Form02").find("button").hide();
	}
});