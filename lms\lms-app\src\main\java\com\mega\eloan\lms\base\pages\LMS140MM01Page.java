package com.mega.eloan.lms.base.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.LMSL140M01MPanel;
import com.mega.eloan.lms.model.L140MM1A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 央行註記異動作業- 編製中
 * </pre>
 * 
 * @since 2014/08/28
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller
@RequestMapping("/cls/lms140mm01/{page}")
public class LMS140MM01Page extends AbstractEloanForm {
	@Autowired
	DocCheckService docCheckService;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model,
				new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_待覆核, CreditDocStatusEnum.先行動用_待覆核));
		renderJsI18N(LMS140MM01Page.class);
		renderJsI18N(LMSL140M01MPanel.class);
		
		//該 panel 抓到的<wicket:message key="..."> 來自數個 page、panel 
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		model.addAttribute("tabID", tabID);
		
		new LMSL140M01MPanel("mainPanel").processPanelData(model, params);
		new DocLogPanel("_docLog").processPanelData(model, params);
		
//		Panel panel = getPanel(page, params);
//		panel.add(new AttributeModifier("id", new Model<String>(tabID)));
//		add(panel);
	}// ;

	// 頁籤
//	public Panel getPanel(int index, PageParameters params) {
//		Panel panel = null;
//		// switch (index) {
//		// case 1:
//
//		panel = new LMSL140M01MPanel(TAB_CTX);
//		// break;
//		// case 2:
//		// renderRespMsgJsI18N("EFD3026"); // 多render一個msgi18n
//		// // renderJsI18N(AbstractEloanPage.class, "EFD3026");
//		// panel = new LMS1601S02Panel(TAB_CTX);
//		// break;
//		// case 3:
//		// renderRespMsgJsI18N("EFD0002"); // 多render一個msgi18n
//		// // renderJsI18N(AbstractEloanPage.class, "EFD0002");
//		// panel = new LMS1605S03Panel(TAB_CTX, params);
//		// break;
//		// case 4:
//		// panel = new LMS1605S04Panel(TAB_CTX);
//		// break;
//		// default:
//		// panel = new LMS1605S01Panel(TAB_CTX);
//		// break;
//		// }
//		return panel;
//	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L140MM1A.class;
	}
}
