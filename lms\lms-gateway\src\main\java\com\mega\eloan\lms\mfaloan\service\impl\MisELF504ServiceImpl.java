package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF504;
import com.mega.eloan.lms.mfaloan.service.MisELF504Service;

@Service
public class MisELF504ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF504Service {

	@Override
	public List<ELF504> findByCntrNo(String cntrNo) {

		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF504.findByCntrNo", new Object[] { cntrNo });

		List<ELF504> list = new ArrayList<ELF504>();
		for (Map<String, Object> row : rowData) {
			ELF504 model = new ELF504();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
}
