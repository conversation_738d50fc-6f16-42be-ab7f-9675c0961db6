package com.mega.eloan.lms.base.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/simple/FileProcessingPage")
public class FileProcessingPage extends AbstractFileDownloadPage {
	
	public FileProcessingPage() {
		super();
	}
	
	@Override
	public String getDownloadFileName() {
		return this.fileDownloadName;
	}

	@Override
	public String getFileDownloadServiceName() {
		return this.serviceName;
	}

	@Override
	protected String getViewName() {
		return null;
	}
}
