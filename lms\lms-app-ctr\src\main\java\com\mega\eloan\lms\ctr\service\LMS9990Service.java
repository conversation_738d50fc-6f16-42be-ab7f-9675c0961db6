/* 
 * LMS9990Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.service;

import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01D;
import com.mega.eloan.lms.model.L140M01F;
import com.mega.eloan.lms.model.L140M01H;
import com.mega.eloan.lms.model.L140M01N;
import com.mega.eloan.lms.model.L999M01A;
import com.mega.eloan.lms.model.L999M01B;
import com.mega.eloan.lms.model.L999M01C;
import com.mega.eloan.lms.model.L999M01D;
import com.mega.eloan.lms.model.L999S01A;
import com.mega.eloan.lms.model.L999S01B;
import com.mega.eloan.lms.model.L999S02A;
import com.mega.eloan.lms.model.L999S04A;
import com.mega.eloan.lms.model.L999S04B;
import com.mega.eloan.lms.model.L999S07A;

/**
 * <pre>
 * 企金約據書 Service
 * </pre>
 * 
 * @since 2012/2/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/11,Ice Lin add
 *          </ul>
 */
public interface LMS9990Service extends AbstractService {

	/**
	 * 利用MAINID找到企金約據書主檔
	 * 
	 * 
	 * @param mainId
	 *            mainId
	 * @return L999M01A 企金約據書主檔
	 */
	L999M01A findL999m01aByMainId(String mainId);

	/**
	 * 利用MAINID找到約據書
	 * 
	 * @param mainId
	 *            mainId
	 * @return L999M01A 企金約據書立約人檔
	 */
	public L999M01B findL999m01bByMainIdType(String mainId, String type);

	/**
	 * 利用MAINID找到綜合授信契約書檔
	 * 
	 * 
	 * @param mainId
	 *            mainId
	 * @return L999S01A 綜合授信契約書檔
	 */
	L999S01A findL999s01aByMainId(String mainId);

	/**
	 * 利用MAINID找到企金約據書連保人(保證人)檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return L999M01C 企金約據書連保人(保證人)檔
	 */
	List<L999M01C> findL999m01cByMainId(String mainId);

	/**
	 * 利用MAINID找到綜合授信契約書借款種類檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return L999S01B 綜合授信契約書借款種類檔 List
	 */
	List<L999S01B> findL999s01bByMainId(String mainId);

	/**
	 * 利用MAINID找到連帶保證書檔
	 * 
	 * 
	 * @param mainId
	 *            mainId
	 * @return L999S02A 連帶保證書檔
	 */
	L999S02A findL999s02aByMainId(String mainId);

	/**
	 * 利用MAINID找到綜合授信契約書借款種類檔
	 * 
	 * 
	 * @param mainId
	 *            mainId
	 * @return L999S01B 綜合授信契約書借款種類檔
	 */
	L999S01B findL999s01bByMainIdItemType(String mainId, String itemType);

	/**
	 * 利用MAINID找到企金約據書項目描述檔 List
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<L999M01D> 企金約據書項目描述檔 List
	 */
	List<L999M01D> findL999m01dByMainId(String mainId);

	/**
	 * 利用MAINID找到 企金約據書項目描述檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return L999M01D 企金約據書項目描述檔
	 */
	L999M01D findL999m01dByMainIdItemType(String mainId, String itemType);

	/**
	 * 取得未輸入重複序號的統編列表
	 * 
	 * @param custId
	 *            客戶統編
	 * @param search
	 *            搜尋條件
	 * @return Page<Map<String, Object>>
	 * @throws CapException
	 *             CapException
	 */
	Page<Map<String, Object>> listDupNoToCustId(String custId, ISearch search)
			throws CapException;

	/**
	 * 利用MAINID找到 中長期契約書檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return L999S04A 中長期契約書檔
	 */
	L999S04A findL999s04aByMainId(String mainId);

	/**
	 * 利用MAINID找到中長期契約書授信內容及條件檔 List
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<L999S04B> 中長期契約書授信內容及條件檔List
	 */
	List<L999S04B> findL999s04bByMainId(String mainId);

	/**
	 * 利用MAINID找到 中長期契約書授信內容及條件檔
	 * 
	 * @param mainId
	 *            mainId
	 * @return L999S04B 中長期契約書授信內容及條件檔
	 */
	L999S04B findL999s04bByMainIdType(String mainId, String type);

	/**
	 * 刪除上傳檔案
	 * 
	 * @param oids
	 *            文件編號陣列
	 */
	void deleteUploadFile(String[] oids);

	/**
	 * 查詢額度明細表 by oid
	 * 
	 * @param oids
	 * @return
	 */
	List<L140M01A> findL140M01AByOids(String[] oids);

	/**
	 * 儲存全部明細
	 * 
	 * @param l999m01as
	 * @param l999m01bs
	 * @param l999m01cs
	 */
	void saveAll(List<L999M01A> l999m01as, List<L999M01B> l999m01bs,
			List<L999M01C> l999m01cs, List<L999S01A> l999s01cs,
			List<L999M01D> l999m01ds);

	/**
	 * 儲存所有的bean
	 * 
	 * @param genericbean
	 */
	void saveAll(List<GenericBean> genericbeans);

	/**
	 * 查詢額度明細表描述檔
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @param itemType
	 *            種類
	 * @return
	 */
	L140M01B findL140M01B(String mainId, String itemType);
	
	/**
	 * 利用MAINID找到信用狀開狀約定書
	 * 
	 * 
	 * @param mainId
	 *            mainId
	 * @return L999S07A 信用狀開狀約定書擋
	 */
	L999S07A findL999s07aByMainId(String mainId);
	
	/**
	 * 查詢符合OIDS條件之連保人資料
	 * @param oids
	 * @return
	 */
	List<L999M01C> findL999M01CByOids(String[] oids);
	
	public L140M01A findL140M01ABymainId(String mainId);
	
	public L120M01A findL120M01ABymainId(String mainId);
	
	public L120S01A findL120s01aByUniqueKey(String mainId, String custId, String dupNo);
	
	public L120S01B findL120s01bByUniqueKey(String mainId, String custId, String dupNo);

	public List<L140M01C> findL140m01cListByMainId(String mainId);
	
	public L140M01C findL140m01cByUniqueKey(String mainId, String loanTP);
	
	public List<L140M01D> findL140m01dByMainIdAndLmtTypeAndSubject(String mainId, String lmtType, String subject);
	
	public List<L140M01F> findL140m01fByMainId(String mainId);
	
	public List<L140M01N> findL140m01nByMainIdAndRateSeqOrderBy(String mainId, Integer rateSeq);
	
	public L140M01H findL140m01hByUniqueKey(String mainId, Integer rateSeq);
}