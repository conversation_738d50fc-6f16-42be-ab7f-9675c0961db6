---------------------------------------------------------
-- LMS.L120S07A 土建融案檢視清單主檔
---------------------------------------------------------
--DROP TABLE LMS.L120S07A;
CREATE TABLE LMS.L120S07A (
	OID           CHAR(32)      not null,
	<PERSON>IN<PERSON>        CHAR(32)      not null,
	COMNAME       VARCHAR(360) ,
	<PERSON><PERSON><PERSON><PERSON>      VARCHAR(360) ,
	CASEAD<PERSON>      VARCHAR(360) ,
	USEPLACE      VARCHAR(128) ,
	BUILDRATIO    DECIMAL(5,2) ,
	INSIDERATIO   DECIMAL(5,2) ,
	BASEWIDE      DECIMAL(15,2),
	TOTALWIDE     DECIMAL(15,2),
	UNDERF        VARCHAR(3)   ,
	<PERSON><PERSON>UN<PERSON>       VARCHAR(3)   ,
	LANDBUYAMT    DECIMAL(15,0),
	LANDAMT1      DECIMAL(15,0),
	LANDAMT2      DECIMAL(15,0),
	ISDMOVTOTAMT  DECIMAL(15,0),
	NEARAMTLAND   DECIMAL(15,0),
	ISTRUSTLAND   CHAR(1)      ,
	LANDBORTOTAMT DECIMAL(15,0),
	LANDBORPERCENT DECIMAL(5,2) ,
	BUILDCOST     DECIMAL(15,0),
	ISTRUSTBUILD  CHAR(1)      ,
	BUILDTOTAMT   DECIMAL(15,0),
	BUILDBORPERCENT DECIMAL(5,2) ,
	CASEWILLSALEAMT DECIMAL(15,0),
	NEARAMTALL    DECIMAL(15,0),
	PROFITLOSTRATIO DECIMAL(5,2) ,
	BORROWRATIO   DECIMAL(5,2) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L120S07A PRIMARY KEY(OID)
) in EL_DATA_4KTS index in EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL120S07A01;
CREATE UNIQUE INDEX LMS.XL120S07A01 ON LMS.L120S07A   (MAINID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L120S07A IS '土建融案檢視清單主檔';
COMMENT ON LMS.L120S07A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	COMNAME       IS '推案公司', 
	CASENAME      IS '案名', 
	CASEADDR      IS '案址', 
	USEPLACE      IS '使用分區', 
	BUILDRATIO    IS '建蔽率', 
	INSIDERATIO   IS '容積率', 
	BASEWIDE      IS '基地面積', 
	TOTALWIDE     IS '總營建面積', 
	UNDERF        IS '樓層地下(層)', 
	GROUNDF       IS '樓層地上(層)', 
	LANDBUYAMT    IS '土地買價(每坪)', 
	LANDAMT1      IS '地鑑(初)估價(每坪)未含容積移轉', 
	LANDAMT2      IS '地鑑(初)估價(每坪)已含容積移轉', 
	ISDMOVTOTAMT  IS '容積移轉總金額', 
	NEARAMTLAND   IS '附近行情價(每坪)', 
	ISTRUSTLAND   IS '是否為辦理信託者', 
	LANDBORTOTAMT IS '土地貸款總金額/融資成數', 
	LANDBORPERCENT IS '土地貸款融資成數', 
	BUILDCOST     IS '建築成本(每坪)', 
	ISTRUSTBUILD  IS '是否為辦理信託者', 
	BUILDTOTAMT   IS '建築融資總金額/融資成數', 
	BUILDBORPERCENT IS '建築融資成數', 
	CASEWILLSALEAMT IS '本案擬銷售價格(每坪)', 
	NEARAMTALL    IS '附近行情價(每坪)', 
	PROFITLOSTRATIO IS '損益兩平比率', 
	BORROWRATIO   IS '土建融每坪借款佔本案售價比率', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
