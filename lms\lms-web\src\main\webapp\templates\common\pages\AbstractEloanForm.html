<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<meta name="_csrf" th:content="${_csrf.token}" />
		<meta name="_csrf_header" th:content="${_csrf.headerName}" />
        <script type="text/javascript">
            var webroot = window.location.pathname.substr(0, window.location.pathname.indexOf("/app"));

            var cnt = window.location.pathname.split('/').length - 3; // /xxx-web/app/a/b/c
            var baseUrl = '';

            //弱掃 Unchecked Input for Loop Condition
            //for (let i = 0; i < cnt; i++) {
            for (let i = 0; i < 500000; i++) {
              if(i >= cnt){
                break;
              }
              baseUrl+= '../';
            }
            var __ajaxHandler='';
            
            function validPath(inputPath){
                if (/-web/.test(inputPath)) {
                    return inputPath;
                }
                return '';
            }
            
            /*if (!window.JAWR) {
                document.write("<script type='text/javascript' src='"+validPath(webroot)+"/jawr_loader.js'></scr" + "ipt>");
            }*/
        </script>
        <script type="text/javascript">
            /*if (!window.i18n) {
                JAWR.loader.style('/bundles/baseCss.cssx');
                JAWR.loader.style('/bundles/coreCss.cssx');
                JAWR.loader.script('/bundles/baseJs.jsx');
                JAWR.loader.script('/bundles/capcommonJs.jsx');
                JAWR.loader.script('/bundles/ckeditor.jsx');
                JAWR.loader.script('/bundles/yuiswfstore.jsx');
            }*/
        </script>
        <script th:inline="javascript">
		    // [refs#206] cache by server startup timestamp 會在 main.js 的 require.config 中使用 jsCache 作為 urlArgs
		    var jsCache = /*[[${jsCache}]]*/;
		    console.log(jsCache);
		</script>
		<link rel="stylesheet" th:href="@{/css/base_css.css}" />
		<link rel="stylesheet" th:href="@{/css/core_css.css}" />
		<link rel="icon" th:href="@{/img/c/mega_logo.gif}" type="image/x-icon" />
		<link rel="shortcut icon" th:href="@{/img/c/mega_logo.gif}" type="image/x-icon" />
		<title>[[#{page.title}]]</title>
    </head>
    <body>
    	<script type="text/javascript" th:src="@{/js/lib/purify/3.0.6/purify.js}"></script>
		<script type="text/javascript" th:src="@{/js/lib/ckeditor5/35.1.0/ckeditor.js}"></script>
		<script type="text/javascript" th:src="@{/js/lib/requirejs/2.3.6/require.min.js}"></script>
		<script type="text/javascript" th:src="@{/js/build-main.js}"></script>
	    <th:block th:each="jsAttr : ${PAGE_JS_LIST}" >
	        <script type="text/javascript" th:utext="${jsAttr.content}" th:id="${jsAttr.id}">
	        </script>
	    </th:block> 
        <div id="localerrortemp">
        </div>
        <div class="body">
            <div id="headerarea">
				<th:block th:if="${not disableHeaderPanel}">
					<div class="header_wrap" th:insert="~{common/panels/EloanHeaderPanel :: header}"></div>
				</th:block>
            </div>
            <div class="eloandoc">
				<th:block th:insert="~{${contentPageName} :: ${contentFragmentName}}"></th:block>
            </div>
        </div>
    </body>
</html>