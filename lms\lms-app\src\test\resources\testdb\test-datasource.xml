<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jdbc="http://www.springframework.org/schema/jdbc"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- <jdbc:embedded-database id="dataSource" type="H2"> -->
	<!-- <jdbc:script location="classpath:ddl/flow_inst.sql" /> -->
	<!-- <jdbc:script location="classpath:ddl/flow_seq.sql" /> -->
	<!-- <jdbc:script location="classpath:ddl/ELSBRN.sql"/> -->
	<!-- <jdbc:script location="classpath:ddl/ELSPGM.sql"/> -->
	<!-- <jdbc:script location="classpath:ddl/ELSRLE.sql"/> -->
	<!-- <jdbc:script location="classpath:ddl/ELSRLF.sql"/> -->
	<!-- <jdbc:script location="classpath:ddl/ELSUSR.sql"/> -->
	<!-- <jdbc:script location="classpath:ddl/ELSUSRR.sql"/> -->
	<!-- </jdbc:embedded-database> -->
	
	<bean id="dsELOANDB_COM"
		class="org.springframework.jdbc.datasource.DriverManagerDataSource">
		<property name="driverClassName" value="${com.jdbc.driver}" />
		<property name="url" value="${com.jdbc.url}" />
		<property name="username" value="${com.jdbc.username}" />
		<property name="password" value="${com.jdbc.password}" />
	</bean>
	
	<bean id="dsICBCRDB"
		class="org.springframework.jdbc.datasource.DriverManagerDataSource">
		<property name="driverClassName" value="${icbcrdb.jdbc.driver}" />
		<property name="url" value="${icbcrdb.jdbc.url}" />
		<property name="username" value="${icbcrdb.jdbc.username}" />
		<property name="password" value="${icbcrdb.jdbc.password}" />
	</bean>
</beans>