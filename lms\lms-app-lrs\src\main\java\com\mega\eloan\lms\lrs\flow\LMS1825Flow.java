package com.mega.eloan.lms.lrs.flow;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.model.L182M01A;

/**<pre>
 * 維護覆審控制檔-流程
 * </pre>
 * @since  2011/9/23
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/23,irene,new
 *          </ul>
 */
@Component
public class LMS1825Flow extends AbstractFlowHandler {

	@Resource
	CommonMetaDao metaDao;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L182M01A.class;
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return RetrialDocStatusEnum.class;
	}
}