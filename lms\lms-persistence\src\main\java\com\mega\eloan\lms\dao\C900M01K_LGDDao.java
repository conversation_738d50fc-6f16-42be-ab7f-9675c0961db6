/* 
 * C900M01K_LGDDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01K_LGD;

/** 授信業務授權額度檔_LGD版 **/
public interface C900M01K_LGDDao extends IGenericDao<C900M01K_LGD> {

	C900M01K_LGD findByOid(String oid);

	/**
	 * 透過docType、brNo、版本撈取一包含有各PD等級的參數
	 * 
	 * @param brNo
	 * @param type
	 * @return
	 */
	List<C900M01K_LGD> findByDocTypeAndVersionAndBrNo(String docType,
			String version, String brNo);

	/**
	 * 透過docType、brClass第X組分行、版本撈取一包參數
	 * 
	 * @param brClass
	 * @return
	 */
	C900M01K_LGD findByDocTypeAndVersionAndBrClassNoPd(String docType,
			String version, String brClass);

	/**
	 * 透過docType、brClass第X組分行、版本撈取一包含有各PD等級的參數
	 * 
	 * @param brClass
	 * @return
	 */
	List<C900M01K_LGD> findByDocTypeAndVersionAndBrClass(String docType,
			String version, String brClass);
	
	/**
	 * 透過docType、版本撈取一包有"授權層級"的資料
	 * 
	 * @param brClass
	 * @return
	 */
	List<C900M01K_LGD> findByDocTypeAndVersionHaveCaseLvl(String docType, String version);
}