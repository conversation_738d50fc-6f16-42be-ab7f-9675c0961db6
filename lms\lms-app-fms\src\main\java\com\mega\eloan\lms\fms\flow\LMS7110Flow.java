package com.mega.eloan.lms.fms.flow;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.dao.L918M01ADao;
import com.mega.eloan.lms.dao.L918S01ADao;
import com.mega.eloan.lms.fms.constants.fmsConstants;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.model.L918M01A;
import com.mega.eloan.lms.model.L918S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 授管處解除停權 - 流程
 * </pre>
 * 
 * @since 2013/1/24
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/24,Miller,new
 *          </ul>
 */
@Component
public class LMS7110Flow extends AbstractFlowHandler {
	public static final String FLOW_CODE = "LMS7110Flow";

	@Resource
	L918M01ADao l918m01aDao;

	@Resource
	L918S01ADao l918s01aDao;

	@Resource
	BranchService branch;
	@Resource
	NumberService number;
	@Resource
	MisStoredProcService misStoredProcService;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L918M01A.class;
	}

	/**
	 * 編製中到待覆核
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapMessageException 
	 * @throws NumberFormatException 
	 */
	@Transition(node = "停權編製中", value = "to停權待覆核")
	public void start(FlowInstance instance) throws NumberFormatException, CapMessageException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L918M01A meta = (L918M01A) l918m01aDao.findByOid(instanceId);
		MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
		// 若無案號則進行給號
		if (Util.isEmpty(Util.trim(meta.getCaseSeq()))
				&& Util.isEmpty(Util.trim(meta.getCaseNo()))) {
			meta.setCaseSeq(Integer.parseInt(number.getNumberWithMax(
					L918M01A.class, unit.getUnitNo(), null, 99999)));
			meta.setCaseYear(Util.parseInt(TWNDate.toAD(new Date()).substring(
					0, 4)));
			StringBuilder caseNum = new StringBuilder();
			IBranch ibranch = branch.getBranch(unit.getUnitNo());
			caseNum.append(Util.toFullCharString(Util.trim(meta.getCaseYear())))
					.append(Util.trim(ibranch.getNameABBR()))
					.append(UtilConstants.Field.兆)
					.append(UtilConstants.Field.授字第)
					.append(Util.toFullCharString(Util.addZeroWithValue(
							Util.trim(meta.getCaseSeq()), 5)))
					.append(UtilConstants.Field.號);
			meta.setCaseNo(caseNum.toString());
			meta.setUpdater(unit.getUserId());
			meta.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l918m01aDao.save(meta);
	}

	@Transition(node = "停權待覆核", value = "to決策")
	public void next(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L918M01A meta = (L918M01A) l918m01aDao.findByOid(instanceId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String result = (String) instance.getAttribute("result");
		if ("to核定".equals(result)) {
			// 檢查主管與經辦是否為同一人
			if (user.getUserId().equals(Util.trim(meta.getStopUpdater()))) {
				// EFD0053=WARN|覆核人員不可與“經辦人員或其它覆核人員”為同一人|
				throw new FlowMessageException("EFD0053");
			}
			instance.setAttribute("result", "to核定");
		} else {
			instance.setAttribute("result", "to退回停權編製中");
		}
	}

	/**
	 * 退回編製中
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策", value = "to退回停權編製中")
	public void back(FlowInstance instance) {
		// String instanceId = instance.getParentInstanceId() != null ? instance
		// .getParentInstanceId().toString() : instance.getId().toString();
		// L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
		// instanceId);
		// // 新增簽章欄
		// lmsService.deleteL120M01F(meta.getMainId(),
		// UtilConstants.BRANCHTYPE.分行,
		// new String[] { UtilConstants.STAFFJOB.執行覆核主管L4 });
		// // 變更額度明細表為編製中
		// lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());
	}

	/**
	 * 核准
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapException
	 * @throws FlowException
	 */
	@Transition(node = "決策", value = "to核定")
	public void complete(FlowInstance instance) throws FlowException,
			CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L918M01A meta = (L918M01A) l918m01aDao.findByOid(instanceId);
		List<L918S01A> list = l918s01aDao.findByMainId(Util.trim(meta
				.getMainId()));

	    int caseCount = 0;
		// Call STORE PROCEDURE Method
		for (L918S01A model : list) {
			String statusFlag = Util.trim(model.getStatusFlag());
			String branchNo = Util.trim(model.getBranchNo());
			String caseNo = Util.trim(model.getSetDocNo());
			String allCustId = getAllCust(Util.trim(model.getCustId()),
					Util.trim(model.getDupNo()));
			String suspendCode = Util.trim(model.getSuspendCode());
			String modifyMons = Util.trim(model.getModlifyMons());
			String unitNo = user.getUnitNo();
			String userId = user.getUserId();
			String oid = Util.trim(model.getOid());
			
			if (fmsConstants.stopRelease.statusFlag.新增.equals(statusFlag)) {
				caseCount = caseCount + 1;
				String upLoadCaseNo = getUploadCaseNo(meta) + "-"+ String.format("%03d", caseCount);
				misStoredProcService
						.addStop(branchNo, upLoadCaseNo, allCustId,
								suspendCode, modifyMons, unitNo, userId, oid);
				model.setSetDocNo(upLoadCaseNo);
				l918s01aDao.save(model);
			} else if (fmsConstants.stopRelease.statusFlag.修改
					.equals(statusFlag)) {
				misStoredProcService.modifyStop(branchNo, caseNo, allCustId,
						suspendCode, modifyMons, unitNo, userId,
						getUploadCaseNo(meta), oid);
			} else if (fmsConstants.stopRelease.statusFlag.刪除
					.equals(statusFlag)) {
				misStoredProcService.delStop(branchNo, caseNo, allCustId,
						suspendCode, modifyMons, unitNo, userId,
						getUploadCaseNo(meta), oid);
			}
			
			
		}

		// 設定覆核主管與覆核時間
		meta.setStopApprover(user.getUserId());
		meta.setStopApprTime(CapDate.getCurrentTimestamp());
		meta.setApproveTime(CapDate.getCurrentTimestamp());
		meta.setUpdateTime(CapDate.getCurrentTimestamp());
		l918m01aDao.save(meta);
	}

	/**
	 * 取得完整Id(統編加重覆序號)
	 * 
	 * @param custid
	 * @param dupNo
	 * @return
	 */
	private String getAllCust(String custid, String dupNo) {
		StringBuilder strb = new StringBuilder();
		// if ("0".equals(dupNo)) {
		// dupNo = "";
		// }
		return strb.append(CapString.fillString(custid, 10, false, ' '))
				.append(dupNo).toString();
	}

	/**
	 * 取得上傳案號-> 民國年 + 分行別+{LMS/CLS}+末五碼流水號
	 * 
	 * @param l918m01a
	 *            授管處解除停權主檔
	 * @return
	 */
	private static String getUploadCaseNo(L918M01A l918m01a) {
		String schema = "LMS";
		String custId = Util.trim(l918m01a.getCustId());
		// 統編：第一碼英文、第二~十數字(長度是10)--CLS
		if (custId.length() == 10) {
			char c = custId.charAt(0);
			if (c >= 'A' && c <= 'Z') {
				if (Util.isNumeric(custId.substring(1))) {
					schema = "CLS";
				}
			}
		}
		return StrUtils.concat(l918m01a.getCaseYear() - 1911,
				l918m01a.getOwnBrId(), schema,
				Util.addZeroWithValue(l918m01a.getCaseSeq(), 5));
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}