function open_G_page(param){
	
	var mainId = param.mainId;
	var custId = param.custId;
	var dupNo = param.dupNo;
	var handler = param.use_handler;
	
	$('#C101S01GDiv').buildItem();
	$('#C101S01GForm').readOnlyChilds();
	
	$.ajax({
		handler : handler,
		action : 'loadScore',
		formId : 'C101S01GForm', 
		data : {
			'mainId':mainId 
			, 'custId':custId
			, 'dupNo':dupNo
			, 'noOpenDoc':true
			, 'markModel':'1'
		},
		success : function(response) {
			$('#C101S01GForm').setValue(response.C101S01GForm);

			$('#C101S01GThickBox').thickbox({
				title : ((i18n.cls1131s01g['C101S01G.title'] || '')+ DOMPurify.sanitize(response.C101S01GForm.varVer)),
				width : 880,
				height : 450,
				align : 'center',
				valign : 'bottom',
				buttons : {				
					'close' : function() {
						$.thickbox.close();
					}
				}
			});
			// default tab
			$('#C101S01GTab').tabs({
				selected : 0
			});
		}
	});
}
