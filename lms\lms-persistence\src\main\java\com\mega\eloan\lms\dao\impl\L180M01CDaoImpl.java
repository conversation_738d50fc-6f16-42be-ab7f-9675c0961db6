/* 
 * L180M01CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.L180M01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L180M01C;

/** 覆審名單明細額度序號檔 **/
@Repository
public class L180M01CDaoImpl extends LMSJpaDao<L180M01C, String> implements
		L180M01CDao {

	@Override
	public L180M01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180M01C> findByMainId(String mainId, String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		List<L180M01C> list = createQuery(L180M01C.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L180M01C findByUniqueKey(String mainId, String custId, String dupNo,
			String elfCntrType, String elfCustCoId, String elfCntrNo,
			String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "elfCntrType",
				elfCntrType);
		search.addSearchModeParameters(SearchMode.EQUALS, "elfCustCoId",
				elfCustCoId);
		search.addSearchModeParameters(SearchMode.EQUALS, "elfCntrNo",
				elfCntrNo);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180M01C> findByCustIdDupId(String custId, String DupNo,
			String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		List<L180M01C> list = createQuery(L180M01C.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L180M01C> findByIndex01(String mainId, String custId,
			String dupNo, String elfCntrType, String elfCustCoId,
			String elfCntrNo, String ctlType) {
		ISearch search = createSearchTemplete();
		List<L180M01C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (elfCntrType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "elfCntrType",
					elfCntrType);
		if (elfCustCoId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "elfCustCoId",
					elfCustCoId);
		if (elfCntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "elfCntrNo",
					elfCntrNo);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L180M01C.class, search).getResultList();
		}
		return list;
	}

	@Override
	public void deleteL180M01CList(String mainId, String custId, String dupNo,
			String ctlType) {
		Query query = getEntityManager().createNamedQuery(
				"L180M01C.deleteByMainId");
		query.setParameter("mainId", mainId); // 設置參數
		query.setParameter("custId", custId); // 設置參數
		query.setParameter("dupNo", dupNo); // 設置參數
		query.setParameter("ctlType", ctlType); // 設置參數
		query.executeUpdate();
	}

	@Override
	public List<L180M01C> findByUniqueKey2(String mainId, String custId,
			String dupNo, String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		List<L180M01C> list = createQuery(L180M01C.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L180M01C> findByCntrNo(String CntrNo, String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "elfCntrNo", CntrNo);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		search.addOrderBy("elfCntrNo");
		List<L180M01C> list = createQuery(L180M01C.class, search)
				.getResultList();

		return list;
	}
}