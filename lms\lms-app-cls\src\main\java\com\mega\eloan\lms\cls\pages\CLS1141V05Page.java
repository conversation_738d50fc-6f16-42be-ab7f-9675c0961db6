/* 
 * CLS1141V05Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.GridViewFilterPanel01;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書 - 海外_已核准
 * </pre>
 * 
 * @since 2011/11/9
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/11/9,Miller Lin,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1141v05")
public class CLS1141V05Page extends AbstractEloanInnerView {

	@Autowired
	AuthService au;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_已核准);

		//========================
		Set<String> eloanRoles = MegaSSOSecurityContext.getEloanRoles();
		String pgmDept = MegaSSOSecurityContext.getPGMDept();
		int transactionCode = Util.parseInt(params
				.getString(EloanConstants.TRANSACTION_CODE));
		
//		boolean _Query = au.auth(pgmDept, eloanRoles, transactionCode,
//				AuthType.Query);
		boolean _Accept = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Accept);
		boolean _Modify = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Modify);
		
		// 加上Button
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		// 主管跟經辦都會出現的按鈕
		list.add(LmsButtonEnum.View);
		list.add(LmsButtonEnum.Filter);
		if(_Accept||_Modify){
			list.add(LmsButtonEnum.ModifyCaseLvl);
		}

		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			// 2013-07-03_先不開敘作條件異動
			// btns.add(CreditButtonEnum.Change);
			list.add(LmsButtonEnum.TableSend);
			list.add(LmsButtonEnum.PrintBook);
			list.add(LmsButtonEnum.PrintNote);
			if(Util.equals("900", user.getSsoUnitNo())){
				list.add(LmsButtonEnum.UPCls);
				list.add(LmsButtonEnum.ImportClsAreaPriceExcel);
			}			
		}
		addToButtonPanel(model, list);

		// 套用哪個i18N檔案
		renderJsI18N(CLS1141V01Page.class);

		setupIPanel(new GridViewFilterPanel01(PANEL_ID), model, params);

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1141V01Page');");
	}

}
