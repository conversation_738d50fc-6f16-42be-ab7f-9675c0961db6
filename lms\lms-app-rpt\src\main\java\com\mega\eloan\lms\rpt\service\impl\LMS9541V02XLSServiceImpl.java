package com.mega.eloan.lms.rpt.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.rpt.service.LMS9541V02Service;

import jxl.Workbook;
import jxl.WorkbookSettings;
import jxl.format.Alignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

@Service("lms9541v02xlsservice")
public class LMS9541V02XLSServiceImpl implements FileDownloadService {
	@Resource
	LMS9541V02Service service;

	@Resource
	EloandbBASEService eloandbBaseService;

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS9541V02XLSServiceImpl.class);

	final BigDecimal BASE = new BigDecimal("10000");

	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		// Properties
		ByteArrayOutputStream baos = null;

		try {
			baos = new ByteArrayOutputStream();
			int type = params.getInt("useType");
			File xls = new File(Thread
					.currentThread()
					.getContextClassLoader()
					.getResource(
							PropUtil.getProperty("loadFile.dir")
									+ "excel/L810M01A" + (type + 1) + ".xls")
					.toURI());
			Workbook wbook = Workbook.getWorkbook(xls);
			// WriteAccessRecord中有用到arraycopy，但長度寫死，導致write會出錯(ArrayIndexOutOfBoundsException)，加上settings便可解決
			WorkbookSettings settings = new WorkbookSettings();
			settings.setWriteAccess(null);

			WritableWorkbook book = Workbook.createWorkbook(baos, wbook,
					settings);
			switch (type) {
			case 1:
				writeContentByBrno(params, book.getSheet(0));
				break;
			case 2:
				writeContentByCity(params, book.getSheet(0));
				break;
			case 3:
				writeContentAll(params, book.getSheet(0));
			}

			book.write();
			book.close();
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}

	@SuppressWarnings("static-access")
	private WritableSheet writeContentByCity(PageParameters params,
			WritableSheet sheet) {
		// initial
		List<Map<String, Object>> list = null;
		int headShift = 7;
		// start
		try {
			WritableCellFormat dateFormat = new WritableCellFormat();
			String endDate = StringEscapeUtils.escapeHtml(params.getString("endDate"));
			dateFormat.setAlignment(Alignment.CENTRE);
			Label date = new Label(0, 1, "截至 "+ endDate,dateFormat);
			sheet.addCell(date);
			// 除了授管處及資訊處可取得全部資料，其他分行依照各分行代號碼得資料
			list = service.getCityData(params.getString("rptType"));
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> pivot = list.get(i);
				String citynm = Util.trim(pivot.get(service.CITYCOLS[0]));
				Label label = new Label(0, i + headShift, citynm);
				sheet.addCell(label);
				for (int j = 1; j < service.CITYCOLS.length; j++) {
					BigDecimal value;
					switch (j) {
					case 1:
					case 5:
						value = Util.parseBigDecimal(pivot
								.get(service.CITYCOLS[j]));
						break;
					default:
						value = Util.parseBigDecimal(
								pivot.get(service.CITYCOLS[j])).divide(BASE);
					}
					label = new Label(j, i + headShift,
							NumConverter.addComma(value));
					sheet.addCell(label);
				}
			}

		} catch (Exception e) {
			LOGGER.error("[getContent] Exception!!", e.getMessage());
		}

		return null;
	}

	@SuppressWarnings("static-access")
	private WritableSheet writeContentByBrno(PageParameters params,
			WritableSheet sheet) {
		// initial
		List<Map<String, Object>> list = null;
		int headShift = 7;
		// start
		try {
			WritableCellFormat dateFormat = new WritableCellFormat();
			dateFormat.setAlignment(Alignment.CENTRE);
			String endDate = StringEscapeUtils.escapeHtml(params.getString("endDate"));
			Label date = new Label(0, 1, "截至 "+ endDate,dateFormat);
			sheet.addCell(date);
			// 除了授管處及資訊處可取得全部資料，其他分行依照各分行代號碼得資料
			list = service.getBrnoData(params.getString("rptType"), endDate);
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> pivot = list.get(i);

				for (int j = 0; j < service.BRNOCOLS.length; j++) {
					String value;
					switch (j) {
					case 0:
						value = Util
								.nullToSpace(pivot.get(service.BRNOCOLS[j]));
						break;
					case 1:
					case 6:
						value = NumConverter
								.addComma(Util.parseBigDecimal(pivot
										.get(service.BRNOCOLS[j])));
						break;
					case 5:
					case 10:
						value = NumConverter
								.addComma(Util.parseBigDecimal(pivot
										.get(service.BRNOCOLS[j])))
								+ "%";
						break;
					default:
						value = NumConverter.addComma(Util.parseBigDecimal(
								pivot.get(service.BRNOCOLS[j])).divide(BASE));
					}

					Label label = new Label(j, i + headShift, value);
					sheet.addCell(label);
				}
			}

		} catch (Exception e) {
			LOGGER.error("[getContent] Exception!!", e.getMessage());
		}

		return null;
	}

	private WritableSheet writeContentAll(PageParameters params,
			WritableSheet sheet) {
		// initial
		String kindNo = params.getString("rptType");
		// start
		try {
			String totapp = service.findElghtappByKindno(kindNo);
			Map<String, Object> totap = service.findTot(kindNo, "ACCEPT");
			Map<String, Object> totac = service.findTot(kindNo, "ALLOCATE");
			Label label = new Label(2, 2, NumConverter.addComma(totapp));
			sheet.addCell(label);

			String allNum = Util.trim(totap.get("COUNT")), allAmt = Util
					.trim(totap.get("LOAN"));
			String acNum = Util.trim(totac.get("COUNT")), acAmt = Util
					.trim(totac.get("LOAN"));

			label = new Label(1, 3, NumConverter.addComma(allNum));
			sheet.addCell(label);
			label = new Label(2, 3, NumConverter.addComma(allAmt));
			sheet.addCell(label);

			label = new Label(1, 4, NumConverter.addComma(acNum));
			sheet.addCell(label);
			label = new Label(2, 4, NumConverter.addComma(acAmt));
			sheet.addCell(label);

			BigDecimal unAcNum = Util.parseBigDecimal(allNum).subtract(
					Util.parseBigDecimal(acNum));
			BigDecimal unAcAmt = Util.parseBigDecimal(allAmt).subtract(
					Util.parseBigDecimal(acAmt));
			label = new Label(1, 5, NumConverter.addComma(unAcNum));
			sheet.addCell(label);
			label = new Label(2, 5, NumConverter.addComma(unAcAmt));
			sheet.addCell(label);

			Map<String, Object> prepare = eloandbBaseService
					.findTotPreparation(kindNo);
			String prepareNum = Util.trim(prepare.get("num"));
			String prepareAmt = (prepare.get("amt") == null) ? "0" : Util
					.trim(prepare.get("amt"));
			label = new Label(1, 6, NumConverter.addComma(prepareNum));
			sheet.addCell(label);
			label = new Label(2, 6, NumConverter.addComma(prepareAmt));
			sheet.addCell(label);
		} catch (Exception e) {
			LOGGER.error("[getContent] Exception!!", e.getMessage());
		}

		return null;
	}
}
