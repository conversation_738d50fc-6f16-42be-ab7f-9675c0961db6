function open_R_page(param){
	
	var mainId = param.mainId;
	var custId = param.custId;
	var dupNo = param.dupNo;
	var handler = param.use_handler;
	
	$('#C101S01RDiv').buildItem();
	$('#C101S01RForm').readOnlyChilds();
	
	$.ajax({
		handler : handler,
		action : 'loadScore',
		formId : 'C101S01RForm', 
		data : {
			'mainId':mainId 
			, 'custId':custId
			, 'dupNo':dupNo
			, 'noOpenDoc':true
			, 'markModel':'3'
		},
		success : function(response) {
			$('#C101S01RForm').setValue(response.C101S01RForm);
			
			$('#C101S01RThickBox').thickbox({
				title : ((i18n.cls1131s01r['C101S01R.title'] || '')+ DOMPurify.sanitize(response.C101S01RForm.varVer)),
				width : 800,
				height : 450,
				align : 'center',
				valign : 'bottom',
				buttons : {				
					'close' : function() {
						$.thickbox.close();
					}
				}
			});
			// default tab
			$('#C101S01RTab').tabs({
				selected : 0
			});
		}
	});
}

