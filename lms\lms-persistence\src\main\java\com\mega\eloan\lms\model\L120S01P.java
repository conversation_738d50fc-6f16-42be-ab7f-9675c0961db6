/* 
 * L120S01P.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 相關人資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S01P", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "rType", "rId", "rDupNo", "rName" }))
public class L120S01P extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 相關人類型
	 * <p/>
	 * 1.自然人, 2.法人
	 */
	@Size(max = 1)
	@Column(name = "TYPE", length = 1, columnDefinition = "CHAR(1)")
	private String type;

	/** 相關人統編 **/
	@Size(max = 10)
	@Column(name = "RID", length = 10, columnDefinition = "VARCHAR(10)")
	private String rId;

	/** 相關人統編重覆碼 **/
	@Size(max = 1)
	@Column(name = "RDUPNO", length = 1, columnDefinition = "VARCHAR(1)")
	private String rDupNo;

	/** 相關人中文名稱 **/
	@Size(max = 120)
	@Column(name = "RNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String rName;

	/** 相關人英文名稱 **/
	@Size(max = 120)
	@Column(name = "RENAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String rEName;

	/**
	 * 相關身分
	 * <p/>
	 * A 實質受益人
	 */
	@Size(max = 2)
	@Column(name = "RTYPE", length = 2, columnDefinition = "VARCHAR(2)")
	private String rType;

	/** 列印順序 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SEQNUM", columnDefinition = "DECIMAL(3,0)")
	private Integer seqNum;

	/** 資料查詢日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "QUERYDATES", columnDefinition = "DATE")
	private Date queryDateS;

	/** 生日 **/
	private Date birthDate;

	/** 國籍 **/
	@Column(length = 2)
	private String nation;

	/** PEPS註記 **/
	@Column(length = 1)
	private String peps;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得相關人類型
	 * <p/>
	 * 1.自然人, 2.法人
	 */
	public String getType() {
		return this.type;
	}

	/**
	 * 設定相關人類型
	 * <p/>
	 * 1.自然人, 2.法人
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 取得相關人統編 **/
	public String getRId() {
		return this.rId;
	}

	/** 設定相關人統編 **/
	public void setRId(String value) {
		this.rId = value;
	}

	/** 取得相關人統編重覆碼 **/
	public String getRDupNo() {
		return this.rDupNo;
	}

	/** 設定相關人統編重覆碼 **/
	public void setRDupNo(String value) {
		this.rDupNo = value;
	}

	/** 取得相關人中文名稱 **/
	public String getRName() {
		return this.rName;
	}

	/** 設定相關人中文名稱 **/
	public void setRName(String value) {
		this.rName = value;
	}

	/** 取得相關人英文名稱 **/
	public String getREName() {
		return this.rEName;
	}

	/** 設定相關人英文名稱 **/
	public void setREName(String value) {
		this.rEName = value;
	}

	/**
	 * 取得相關身分
	 * <p/>
	 * A 實質受益人
	 */
	public String getRType() {
		return this.rType;
	}

	/**
	 * 設定相關身分
	 * <p/>
	 * A 實質受益人
	 **/
	public void setRType(String value) {
		this.rType = value;
	}

	/** 取得列印順序 **/
	public Integer getSeqNum() {
		return this.seqNum;
	}

	/** 設定列印順序 **/
	public void setSeqNum(Integer value) {
		this.seqNum = value;
	}

	/** 取得資料查詢日 **/
	public Date getQueryDateS() {
		return this.queryDateS;
	}

	/** 設定資料查詢日 **/
	public void setQueryDateS(Date value) {
		this.queryDateS = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得生日 **/
	public Date getBirthDate() {
		return birthDate;
	}

	/** 設定生日 **/
	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}

	/** 取得國籍 **/
	public String getNation() {
		return nation;
	}

	/** 設定國籍 **/
	public void setNation(String nation) {
		this.nation = nation;
	}

	/** 取得PEPS註記 **/
	public String getPeps() {
		return peps;
	}

	/** 設定PEPS註記 **/
	public void setPeps(String peps) {
		this.peps = peps;
	}
}
