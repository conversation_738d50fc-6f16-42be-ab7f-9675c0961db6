package com.mega.eloan.lms.lns.flow;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.L250M01A;

@Component
public class LMS2501Flow extends AbstractFlowHandler {

	@Resource
	CommonMetaDao metaDao;

	@Transition(node = "海外_待覆核", value = "確認")
	public void check(FlowInstance instance) {
	}

	@Transition(node = "海外_編製中")
	public void test2(FlowInstance instance) {
	}

	@Transition(node = "海外_待覆核")
	public void test3(FlowInstance instance) {
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L250M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}