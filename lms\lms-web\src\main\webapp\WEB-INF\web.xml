<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://xmlns.jcp.org/xml/ns/javaee"
	xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd" metadata-complete="true" version="3.1">
    <display-name>Mega-LMS-Web</display-name>
    <context-param>
        <param-name>webAppRootKey</param-name>
        <param-value>lms-web.root</param-value>
    </context-param>
    <context-param>
        <param-name>log4jConfigLocation</param-name>
        <param-value>classpath:log4j2.xml</param-value>
    </context-param>
    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>classpath:spring/applicationContext.xml</param-value>
    </context-param>
    <context-param>
    	<param-name>org.eclipse.jetty.servlet.Default.useFileMappedBuffer</param-name>
    	<param-value>false</param-value>
    </context-param>
    <listener>
        <listener-class>org.apache.logging.log4j.web.Log4jServletContextListener</listener-class>
    </listener>
    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>
    <!-- 防止一個用户重複登錄好幾次 -->
    <listener>
        <listener-class>org.springframework.security.web.session.HttpSessionEventPublisher</listener-class>
    </listener>
    <!-- use for Scope("request") -->
    <listener>
        <listener-class>org.springframework.web.context.request.RequestContextListener</listener-class>
    </listener>
    <!-- cap Filter -->
    <filter>
        <filter-name>encodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>forceEncoding</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter>
        <filter-name>log2userInformation</filter-name>
        <filter-class>tw.com.iisi.cap.log.CapLogContextFilter</filter-class>
    </filter>
    <filter>
        <filter-name>springSecurityFilterChain</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>
    <filter>
        <filter-name>capForwardFilter</filter-name>
        <filter-class>tw.com.iisi.cap.web.filter.CapForwardFilter</filter-class>
        <!-- file reader -->
        <init-param>
            <param-name>/file</param-name>
            <param-value>/app/simple/filereader</param-value>
        </init-param>
        <!-- file reader -->
        <init-param>
            <param-name>/report</param-name>
            <param-value>/app/simple/reportreader</param-value>
        </init-param>
        <!-- webroot root for js -->
        <init-param>
            <param-name>/webroot.*</param-name>
            <param-value>/.</param-value>
        </init-param>
    </filter>
    <filter>
        <filter-name>opensessioninview-com</filter-name>
        <filter-class>org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter</filter-class>
        <init-param>
            <param-name>entityManagerFactoryBeanName</param-name>
            <param-value>comEntityManagerFactory</param-value>
        </init-param>
    </filter>
    <filter>
        <filter-name>opensessioninview-lms</filter-name>
        <filter-class>org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter</filter-class>
        <init-param>
            <param-name>entityManagerFactoryBeanName</param-name>
            <param-value>lmsEntityManagerFactory</param-value>
        </init-param>
    </filter>
    
    <filter-mapping>
        <filter-name>encodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>log2userInformation</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>capForwardFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>opensessioninview-com</filter-name>
        <url-pattern>/app/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>opensessioninview-lms</filter-name>
        <url-pattern>/app/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>springSecurityFilterChain</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    
    <!-- Jawr 處理JS(.jsx)與CSS(.cssx)
    <servlet>
        <servlet-name>JSServlet</servlet-name>
        <servlet-class>tw.com.iisi.cap.jawr.CapJawrServlet</servlet-class>
        <init-param>
            <param-name>configLocation</param-name>
            <param-value>/jawr.properties</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>JSServlet</servlet-name>
        <url-pattern>*.jsx</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>JSServlet</servlet-name>
        <url-pattern>/jawr_loader.js</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>JSServlet</servlet-name>
        <url-pattern>/jawr_generator.js</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>CSSServlet</servlet-name>
        <servlet-class>tw.com.iisi.cap.jawr.CapJawrServlet</servlet-class>
        <init-param>
            <param-name>configLocation</param-name>
            <param-value>/jawr.properties</param-value>
        </init-param>
        <init-param>
            <param-name>type</param-name>
            <param-value>css</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>CSSServlet</servlet-name>
        <url-pattern>*.cssx</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>wicket.wicket</servlet-name>
        <servlet-class>org.apache.wicket.protocol.http.WicketServlet</servlet-class>
        <init-param>
            <param-name>applicationFactoryClassName</param-name>
            <param-value>org.apache.wicket.spring.SpringWebApplicationFactory</param-value>
        </init-param>
        <init-param>
            <param-name>configuration</param-name>
            "deployment" or "development"
            <param-value>deployment</param-value>
        </init-param>
        <load-on-startup>2</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>wicket.wicket</servlet-name>
        <url-pattern>/app/*</url-pattern>
    </servlet-mapping> -->
    
    <servlet>
    	<servlet-name>MyDispatcher</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
        	<param-name>contextConfigLocation</param-name>
        	<param-value>classpath:spring/page.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
    	<servlet-name>MyDispatcher</servlet-name>
    	<url-pattern>/app/*</url-pattern>
    </servlet-mapping>
    
    <mime-mapping>
        <extension>ico</extension>
        <mime-type>image/x-icon</mime-type>
    </mime-mapping>
	
	<resource-ref>
		<description>Mega-eLoan COM DB</description>
		<res-ref-name>jdbc/ELOANDB_COM</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
		<description>Mega-eLoan LMS DB</description>
		<res-ref-name>jdbc/ELOANDB_LMS</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
		<description>Mega-eLoan LMS DB</description>
		<res-ref-name>jdbc/ELOANDB_LMS4COM</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
		<description>a-Loan DB</description>
		<res-ref-name>jdbc/ICBCRDB</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
		<description>DW DB</description>
		<res-ref-name>jdbc/DWOTS</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
    <resource-ref>
        <description>ODS DB</description>
        <res-ref-name>jdbc/ODS</res-ref-name>
        <res-type>javax.sql.DataSource</res-type>
        <res-auth>Container</res-auth>
    </resource-ref>
	<resource-ref>
		<description>eTCH DB</description>
		<res-ref-name>jdbc/ETCHDB</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
		<description>eJCIC DB</description>
		<res-ref-name>jdbc/EJCICDB</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	
	<resource-ref>
		<description>AS400 COM</description>
		<res-ref-name>jdbc/OBSDB_COM</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
		<description>AS400 TPE</description>
		<res-ref-name>jdbc/OBSDB_TPE</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
		<description>AS400 0A2</description>
		<res-ref-name>jdbc/OBSDB_0A2</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
		<description>AS400 0C3</description>
		<res-ref-name>jdbc/OBSDB_0C3</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
		<description>AS400 0E3</description>
		<res-ref-name>jdbc/OBSDB_0E3</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
		<description>AS400 0B0</description>
		<res-ref-name>jdbc/OBSDB_0B0</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
		<description>AS400 0B9</description>
		<res-ref-name>jdbc/OBSDB_0B9</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
	   <description>TEJ DB</description>
	   <res-ref-name>jdbc/TEJDB</res-ref-name>
	   <res-type>javax.sql.DataSource</res-type>
	   <res-auth>Container</res-auth>
	</resource-ref>
	<resource-ref>
	   <description>MEGAIMAGE DB</description>
	   <res-ref-name>jdbc/MEGAIMAGEDB</res-ref-name>
	   <res-type>javax.sql.DataSource</res-type>
	   <res-auth>Container</res-auth>
	</resource-ref>
    <session-config>
        <session-timeout>30</session-timeout>
        <cookie-config>
            <http-only>true</http-only>
        </cookie-config>
        <tracking-mode>COOKIE</tracking-mode>
    </session-config>
    
</web-app>
