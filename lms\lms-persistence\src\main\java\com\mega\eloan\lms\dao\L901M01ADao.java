package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L901M01A;

/** 動用審核表稽核項目 **/
public interface L901M01ADao extends IGenericDao<L901M01A> {

	L901M01A findByOid(String oid);

	List<L901M01A> findByMainId(String mainId);

	List<L901M01A> findByItemTypeAndbranchId(String itemType, String branchId);

	List<L901M01A> findByItemTypeAndbranchId(String itemType, String branchId,
			String locale);
}