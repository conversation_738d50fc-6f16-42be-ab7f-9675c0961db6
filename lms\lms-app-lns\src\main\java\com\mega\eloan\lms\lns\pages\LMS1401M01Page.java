package com.mega.eloan.lms.lns.pages;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.panels.LMSL140M01MPanel;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel04;

@Controller
@RequestMapping("/lms/lms1401m01page")
public class LMS1401M01Page extends AbstractEloanForm {

	@Autowired
	LMSService lmsService;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		
		new LMSL140M01MPanel("LMSL140M01MPanel").processPanelData(model, params);

		Map<String, String> msgs = lmsService.getAllDervPeriod();
		renderJsI18NWithMsgName("dervPeriodCodeType", msgs);
		msgs = lmsService.getCodeType("lms120_noFactCountry");
		renderJsI18NWithMsgName("lms120_noFactCountry", msgs);  
		msgs = lmsService.getCodeType("lms120_freezeFactCountry");
		renderJsI18NWithMsgName("lms120_freezeFactCountry", msgs);  
		//J-111-000A_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
		msgs = lmsService.getCodeType("lms140_esgGreenSpendType");
		renderJsI18NWithMsgName("lms140_esgGreenSpendType", msgs);  
		//J-111-000A_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
		msgs = lmsService.getCodeType("lms140_esgSustainLoanType");
		renderJsI18NWithMsgName("lms140_esgSustainLoanType", msgs);  
		//J-113-0329 企金授信新增社會責任授信
		msgs = lmsService.getCodeType("lms140_socialKind");
		renderJsI18NWithMsgName("lms140_socialKind", msgs);
		msgs = lmsService.getCodeType("lms140_socialTa");
		renderJsI18NWithMsgName("lms140_socialTa", msgs);
		msgs = lmsService.getCodeType("lms140_socialResp");
		renderJsI18NWithMsgName("lms140_socialResp", msgs);

		renderJsI18N(LMS1401M01Page.class);
		renderJsI18N(LMS1401S02Page.class);
		// renderJsI18N(LMS1401S02Panel.class);
		renderJsI18N(LMS1401S02Panel04.class);
		renderJsI18N(LMSL140M01MPanel.class);

	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}

}
