/* 
 * LMS1601M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.lns.panels.LMS1401S01Panel;
import com.mega.eloan.lms.lns.panels.LMS1601S01Panel;
import com.mega.eloan.lms.lns.panels.LMS1601S02Panel;
import com.mega.eloan.lms.lns.panels.LMS1601S03Panel;
import com.mega.eloan.lms.lns.panels.LMS1601S04Panel;
import com.mega.eloan.lms.lns.panels.LMS1601S05Panel;
import com.mega.eloan.lms.lns.panels.LMS1601S06Panel;
import com.mega.eloan.lms.lns.service.LMS1601Service;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 動用審核表
 * </pre>
 * 
 * @since 2011/10/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1601m01/{page}")
public class LMS1601M01Page extends AbstractEloanForm {
	@Autowired
	DocCheckService docCheckService;
	
	@Autowired
	LMS1601Service lms1601Service;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {

		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));

		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(), AuthType.Accept, CreditDocStatusEnum.海外_待覆核, CreditDocStatusEnum.先行動用_待覆核));

		/*
		 * add(new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
		 * AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		 */
		String docStatus = params.getString(EloanConstants.MAIN_DOC_STATUS);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		addAclLabel(model, new AclLabel("_btnDOC_REUPMIS", params, getDomainClass(), AuthType.Accept,
				Util.equals(user.getSsoUnitNo(), "900")	&& CreditDocStatusEnum.海外_已核准.getCode().equals(docStatus)));

		renderJsI18N(LMS1601M01Page.class);
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page, params);
		panel.processPanelData(model, params);
		model.addAttribute("tabID", tabID);
		model.addAttribute("show_ivr_panel_visible", lms1601Service.getProjClassFromL160M01A(params.getString(EloanConstants.MAIN_OID)));
	}// ;

	// 頁籤
	public Panel getPanel(int index, PageParameters params) {
		Panel panel = null;
		
		switch (index) {
		case 1:
			panel = new LMS1601S01Panel(TAB_CTX, true);
			break;
		case 2:
			renderRespMsgJsI18N("EFD3026"); // 多render一個msgi18n
			// renderJsI18N(AbstractEloanPage.class, "EFD3026");
			L160M01C l160m01c = this.lms1601Service.getL160M01CByUniqueKey(params.getString(EloanConstants.MAIN_ID), "4", 31);
			panel = new LMS1601S02Panel(TAB_CTX, true, l160m01c != null ? true : false);
			
			break;
		case 3:
			renderRespMsgJsI18N("EFD0002"); // 多render一個msgi18n
			//J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
			renderRespMsgJsI18N("EFD3026"); // 多render一個msgi18n
			// renderJsI18N(AbstractEloanPage.class, "EFD0002");
			renderJsI18N(LMS1401S01Panel.class);
			panel = new LMS1601S03Panel(TAB_CTX, true, params);
			break;
		case 4:
			panel = new LMS1601S04Panel(TAB_CTX, true);
			break;
		case 5:
			//J-106-0029-003  洗錢防制-新增實質受益人
			renderJsI18N(LMSS20APanel.class);
			panel = new LMSS20APanel(TAB_CTX, true);
			break;	
		case 6:
			panel = new LMS1601S06Panel(TAB_CTX, true);
			break;
		case 7:
			//J-109-0150_10702_B1001 Web e-Loan IVR頁籤由模擬動審移至動審表
			panel = new LMS1601S05Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS1601S01Panel(TAB_CTX, true);
			break;
		}
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L160M01A.class;
	}
	
	@Override
	protected String getContentPageName() {
		return "lns/pages/LMS1601M01Page";
	}
}
