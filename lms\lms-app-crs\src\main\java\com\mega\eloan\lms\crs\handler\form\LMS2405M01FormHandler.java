/* 
 * LMS2405M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.handler.form;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;


import com.iisigroup.cap.component.PageParameters;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.pages.LMS2405M01Page;
import com.mega.eloan.lms.crs.pages.LMS2415M01Page;
import com.mega.eloan.lms.crs.service.LMS2405Service;
import com.mega.eloan.lms.crs.service.LMS2415Service;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.Lms491Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金覆審交易
 * </pre>
 * 
 * @since 2011/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/6,irene
 *          </ul>
 **/
@Scope("request")
@Controller("lms2405m01formhandler")
@DomainClass(C240M01A.class)
public class LMS2405M01FormHandler extends AbstractFormHandler {

	@Resource
	UserInfoService userInfoService;

	@Resource
	RetrialService retrialService;
		
	@Resource
	LMS2405Service service;

	@Resource
	LMS2415Service lms2415service;

	@Resource
	LMSService lmsService;
	
	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	BranchService branch;

	@Resource
	DocCheckService docCheckService;

	@Resource
	Lms491Service lms491Service;
	
	@Resource
	CodeTypeService codetypeService;
	
	@Resource
	MisCustdataService misCustdataService;

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	NumberService numberService;
	
	Properties lms2405m01 = MessageBundleScriptCreator
			.getComponentResource(LMS2405M01Page.class);

	Properties abstractEloan = MessageBundleScriptCreator
			.getComponentResource(AbstractEloanPage.class);

	/**
	 * 查詢
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 **/
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryMain(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		int page = Util.parseInt(params.getString("page"));
		C240M01A c240m01a = service.findModelByOid(C240M01A.class, oid);
		if (!CapString.isEmpty(oid)) {
			if (c240m01a != null) {
				String branchName = branch
						.getBranchName(c240m01a.getBranchId());
				result.set("branchName", c240m01a.getBranchId() + " "
						+ branchName);
				result.set("branchInfo", c240m01a.getBranchId() + branchName);
				result.set(
						"status",
						abstractEloan.getProperty("docStatus."
								+ c240m01a.getDocStatus()));
				CapAjaxFormResult c240m01aForm = DataParse.toResult(c240m01a);
				if(!Util.isEmpty(c240m01a.getDataEndDate())){
					c240m01aForm.set("dataEndDate", CapDate.formatDate(c240m01a.getDataEndDate(), 
							UtilConstants.DateFormat.YYYY_MM));
				}
				result.set("C240M01AForm", c240m01aForm);
				result.set("titInfo", c240m01a.getBranchId() + " " + branchName);
			}
		}

		result.set(EloanConstants.OID, oid);
		result.set(EloanConstants.MAIN_OID, oid);
		result.set(EloanConstants.MAIN_DOC_STATUS, c240m01a.getDocStatus());
		
		result.set("creator", Util.nullToSpace(c240m01a.getCreator()) + " " 
				+ Util.nullToSpace(userInfoService.getUserName(Util.nullToSpace(c240m01a.getCreator()))));
		result.set("createTime", Util.nullToSpace(TWNDate.valueOf(c240m01a.getCreateTime())));
		result.set("updater", Util.nullToSpace(c240m01a.getUpdater()) + " " 
				+ Util.nullToSpace(userInfoService.getUserName(Util.nullToSpace(c240m01a.getUpdater()))));
		result.set("updateTime",
				Util.nullToSpace(TWNDate.valueOf(c240m01a.getUpdateTime())));
		// apprId
		result.set(
				"apprId",
				!Util.isEmpty(userInfoService.getUserName(Util.nullToSpace(c240m01a.getHqAppraiserId()))) ? 
						c240m01a.getHqAppraiserId() +" "+ userInfoService.getUserName(Util.nullToSpace(c240m01a.getHqAppraiserId())) : 
							Util.nullToSpace(c240m01a.getHqAppraiserId()));
		if(!Util.isEmpty(c240m01a.getApprover())){
			result.set(
					"approvercn", !Util.isEmpty(userInfoService.getUserName(Util.nullToSpace(c240m01a.getApprover()))) ? 
							c240m01a.getApprover() +" "+ userInfoService.getUserName(Util.nullToSpace(c240m01a.getApprover())) : 
								Util.nullToSpace(c240m01a.getApprover()));
		}
		result.set("page", page);
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryL241m01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		C241M01A c241m01a = service.findModelByOid(C241M01A.class, oid);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String kind = c241m01a.getTypCd();
		RetrialDocStatusEnum e = RetrialDocStatusEnum.getEnum(Util
				.trim(c241m01a.getDocStatus()));
		String docStatus = "";
		if (e != null) {
			docStatus = e.name();
		}

		if (c241m01a != null) {
			result = DataParse.toResult(c241m01a);
			result.set("branchNo1", user.getUnitNo());
			result.set("branchName1", branch.getBranchName(user.getUnitNo()));
			result.set("ownBrId", user.getUnitNo());
			result.set("branchName", branch.getBranchName(user.getUnitNo()));
			result.set("custIdm1", c241m01a.getCustId());
			result.set("custNamem1", c241m01a.getCustName());
			result.set("dupNom1", c241m01a.getDupNo());
			result.set("typCd",
					Util.nullToSpace(TypCdEnum.getEnum(kind).toString()));
			result.set("typCd1",
					Util.nullToSpace(TypCdEnum.getEnum(kind).toString()));
			result.set("docStatus", docStatus);
			String staff = "";
			if(UtilConstants.DEFAULT.否.equals(Util.trim(c241m01a.getStaff()))){
				staff = this.abstractEloan.getProperty("no");
			}else if(UtilConstants.DEFAULT.是.equals(Util.trim(c241m01a.getStaff()))){
				staff = this.abstractEloan.getProperty("yes");
			}
			result.set("staff", staff);
			//panel1
			result.set("creator", Util.trim(c241m01a.getCreator()) + " " + 
					Util.trim(lmsService.getUserName(c241m01a.getCreator())) + 
					"("+TWNDate.toFullAD(c241m01a.getCreateTime())+")");
			result.set("updater", Util.trim(c241m01a.getUpdater()) + " " + 
					Util.trim(lmsService.getUserName(c241m01a.getUpdater())) + 
					"("+Util.nullToSpace(TWNDate.toFullAD(c241m01a.getUpdateTime()))+")");
			//pagel2
			result.set("custIdIn", c241m01a.getCustId());
			result.set("custNameIn", c241m01a.getCustName());
			result.set("dupNoIn", c241m01a.getDupNo());
			result.set("show_shouldReviewDate", TWNDate.toAD(c241m01a.getShouldReviewDate()));
			result.set("show_retrialKind", Util.trim(c241m01a.getRetrialKind()));
			result.set("show_lastRetrialDate", TWNDate.toAD(c241m01a.getLastRetrialDate()));
			Map<String, String> specifyCycleMap = codetypeService
					.findByCodeType("c241m01a_specifyCycle");
			if (specifyCycleMap == null)
				specifyCycleMap = new LinkedHashMap<String, String>();
			Map<String, String> nckdFlagMap = codetypeService.findByCodeType("lms2405m01_NckdFlag");
			result.set("show_nCkdFlag", nckdFlagMap.get(Util
					.nullToSpace(c241m01a.getNCkdFlag())));
			result.set("show_nCkdMemo", Util.nullToSpace(c241m01a.getNCkdMemo()));
			
			result.set("show_specifyCycle", specifyCycleMap.get(Util
					.nullToSpace(c241m01a.getSpecifyCycle())));
			result.set("show_lnDataDate",
					TWNDate.toAD(c241m01a.getLnDataDate()));
			result.set("show_totQuota",
					Util.trim(c241m01a.getTotQuotaCurr()) + " " + NumConverter.addComma(c241m01a.getTotQuota(), "#,##0.00"));
			result.set("show_totBal",
					Util.trim(c241m01a.getTotBalCurr()) + " " + NumConverter.addComma(c241m01a.getTotBal(), "#,##0.00"));
		}
		return result;
	};

	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString("page"));
		String oid = params.getString(EloanConstants.MAIN_OID);

		C240M01A c240m01a = service.findModelByOid(C240M01A.class, oid);

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));

		if (page == 2) {
			// String formc240m01a = params.getString("C240M01AForm");
			// JSONObject jsonc240m01a = JSONObject.fromObject(formc240m01a);
			// jsonc240m01a.remove("typCd");
			// jsonc240m01a.remove(EloanConstants.MAIN_ID);
			// DataParse.toBean(jsonc240m01a, c240m01a);

			Double thisSamplingRate = params.getAsDouble("thisSamplingRate");
			c240m01a.setThisSamplingRate(thisSamplingRate.intValue());
			service.save(c240m01a);

			result.set("C240M01AForm", DataParse.toResult(c240m01a));
			result.set("oid", oid);
			result.set(EloanConstants.MAIN_ID, c240m01a.getMainId());
		}else{
			service.save(c240m01a);
		}
		
		if(page == 1){
			String branchName = branch
					.getBranchName(c240m01a.getBranchId());
			result.set("branchName", c240m01a.getBranchId() + " "
					+ branchName);
			result.set("branchInfo", c240m01a.getBranchId() + branchName);
			result.set(
					"status",
					abstractEloan.getProperty("docStatus."
							+ c240m01a.getDocStatus()));
			CapAjaxFormResult c240m01aForm = DataParse.toResult(c240m01a);
			if(!Util.isEmpty(c240m01a.getDataEndDate())){
				c240m01aForm.set("dataEndDate", CapDate.formatDate(c240m01a.getDataEndDate(), 
						UtilConstants.DateFormat.YYYY_MM));
			}
			c240m01aForm.set("creator", Util.nullToSpace(c240m01a.getCreator()) + " " 
					+ Util.nullToSpace(userInfoService.getUserName(Util.nullToSpace(c240m01a.getCreator()))));
			c240m01aForm.set("updater", Util.nullToSpace(c240m01a.getUpdater()) + " " 
					+ Util.nullToSpace(userInfoService.getUserName(Util.nullToSpace(c240m01a.getUpdater()))));
			result.set("C240M01AForm", c240m01aForm);
			result.set("titInfo", c240m01a.getBranchId() + " " + branchName);
			result.set("createTime", Util.nullToSpace(TWNDate.valueOf(c240m01a.getCreateTime())));
			result.set("updateTime",
					Util.nullToSpace(TWNDate.valueOf(c240m01a.getUpdateTime())));
			// apprId
			result.set(
					"apprId",
					!Util.isEmpty(userInfoService.getUserName(Util.nullToSpace(c240m01a.getHqAppraiserId()))) ? 
							c240m01a.getHqAppraiserId() +" "+ userInfoService.getUserName(Util.nullToSpace(c240m01a.getHqAppraiserId())) : 
								Util.nullToSpace(c240m01a.getHqAppraiserId()));
			if(!Util.isEmpty(c240m01a.getApprover())){
				result.set(
						"approvercn", !Util.isEmpty(userInfoService.getUserName(Util.nullToSpace(c240m01a.getApprover()))) ? 
								c240m01a.getApprover() +" "+ userInfoService.getUserName(Util.nullToSpace(c240m01a.getApprover())) : 
									Util.nullToSpace(c240m01a.getApprover()));
			}
		}
		
		if (params.getAsBoolean("showMsg", true)) {
			// EFD0017=儲存成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
//		List<IBranch> bank = branch.getBranchByUnitType(
//				BranchTypeEnum.海外分行.getCode(), 
//				BranchTypeEnum.海外分行當地有總行.getCode(), 
//				BranchTypeEnum.海外總行泰國.getCode(), 
//				BranchTypeEnum.海外總行澳洲加拿大.getCode());
//
//		List<String> banks = new ArrayList<String>();
//		banks.add("0B9");
//		banks.add("0C2");
//		banks.add("0C5");
//		for (int i = 0, size = bank.size(); i < size; i++) {
//			banks.add(bank.get(i).getBrNo());
//		}
//		Date dataDate = CapDate.parseDate("2012-08-01");
//		for (int i = 0, size = banks.size(); i < size; i++) {
//			try {
//				service.update490(banks.get(i), dataDate);
//			} catch (Exception e) {
//				logger.error(e.getMessage());
//			}
//		}
		
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult tempMain(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "Y"));

		C240M01A c240m01a = (C240M01A) collectionData(params);

		result.set("C240M01AForm", DataParse.toResult(c240m01a));
		result.set(EloanConstants.OID, oid);
		result.set(EloanConstants.MAIN_OID, oid);
		result.set(EloanConstants.MAIN_ID, c240m01a.getMainId());
		result.set(EloanConstants.MAIN_DOC_STATUS, c240m01a.getDocStatus());
		result.set("docStatus", c240m01a.getDocStatus());

		return result;
	}

	private GenericBean collectionData(PageParameters params)
			throws CapException {

		int page = Util.parseInt(params.getString("page"));
		String oid = params.getString(EloanConstants.MAIN_OID);
		C240M01A c240m01a = service.findModelByOid(C240M01A.class, oid);

		if (page == 2) {
			// String formc240m01a = params.getString("C240M01AForm");
			// JSONObject jsonc240m01a = JSONObject.fromObject(formc240m01a);
			// jsonc240m01a.remove("typCd");
			// jsonc240m01a.remove(EloanConstants.MAIN_ID);
			// DataParse.toBean(jsonc240m01a, c240m01a);

			Double thisSamplingRate = params.getAsDouble("thisSamplingRate", 0);
			c240m01a.setThisSamplingRate(thisSamplingRate.intValue());

			service.save(c240m01a);
		}

		return c240m01a;
	}

	/**
	 * 產生名單
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult produce(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String branch = Util.nullToSpace(params.getString("branch"));
		// 是否需要做檢核
		boolean check = params.getAsBoolean("check", false);
		long t1 = System.currentTimeMillis();
		// 檢查是否有名單存在
		Map<String, Object> map = service.checkAlreadyHave(branch,
				params.getString("basedate"));
		boolean success = (Boolean) map.get("success");
		if (!check) {
			success = true;
		}

		if (success) {
			service.produce(branch, params.getString("basedate"));
			result.set("success", true);
		} else {
			result.set("success", false);
			if (!Util.isEmpty(map.get("msg"))) {
				result.set("msg", (String) map.get("msg"));
			}
		}
		logger.info(StrUtils.concat("init() all COST= ",
				(System.currentTimeMillis() - t1), " ms"));

		return result;
	}

	/**
	 * 產生名單-整批產生
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult produceMany(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String bankList = params.getString("list");
		JSONObject jsonBank = JSONObject.fromObject(bankList);
		JSONArray banks = jsonBank.names();
		StringBuilder mag = new StringBuilder();

		for (int i = 0, size = banks.size(); i < size; i++) {
			String yearMonth = (String) jsonBank.getString((String) banks
					.get(i));
			String year = yearMonth.substring(0, 4);
			String mouth = yearMonth.substring(5);
			if (!Util.isInteger(year) || !Util.isInteger(mouth)
					|| !"-".equals(yearMonth.subSequence(4, 5))) {
				mag.append("[" + jsonBank.get(banks.get(i)) + "]"
						+ lms2405m01.getProperty("err.date"));
				continue;
			}
			try {
				service.produce((String) banks.get(i),
						(String) jsonBank.getString((String) banks.get(i)));

			} catch (Exception e) {
				mag.append("[" + jsonBank.get(banks.get(i)) + "]["
						+ (String) banks.get(i) + "]"
						+ lms2405m01.getProperty("failList"));
				logger.error("LMS2405M01FormHandle produceMany EXCEPTION!!", e);
				this.logger.debug(e.getMessage());
			}
		}

		if (!Util.isEmpty(mag.toString())) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, mag.toString());
		}
		return result;
	}

	/**
	 * 產生資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult produceList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		C240M01A c240m01a = service.findModelByOid(C240M01A.class, oid);
		if (c240m01a == null)
			c240m01a = new C240M01A();
		c240m01a.setThisSamplingRate(params.getAsInteger("thisSamplingRate", 0));
		service.produceList(c240m01a);

		result.set("C240M01AForm", DataParse.toResult(c240m01a));
		result.set(EloanConstants.OID, oid);
		result.set(EloanConstants.MAIN_OID, oid);
		result.set(EloanConstants.MAIN_ID, c240m01a.getMainId());

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}

	/**
	 * 新增明細
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult produceNew(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString(EloanConstants.OID);
		String cust = params.getString("custId");
		cust = cust.toUpperCase();
		String dupNo = params.getString("dupNo");

		Map<String, Object> map = service.produceNew(oid, cust, dupNo);

		if ((Boolean) map.get("success")) {
			C240M01A c240m01a = service.findModelByOid(C240M01A.class, oid);
			// 新增成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0035"));
			result = this.handleRateObjMap(map, result);
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
			this.HandlerResultWarmMsg(result);
			int count = service.saveCTLCount(c240m01a.getMainId());
			result.set("reQuantity", count);
		} else {
			Map<String, String> msg = new HashMap<String, String>();
			msg.put("msg", "-"+Util.nullToSpace(map.get("msg")));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", msg), getClass());
		}

		return result;
	}

	/**
	 * 覆審工作底稿Excel
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws Exception 
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult produceExcel(PageParameters params)
			throws Exception {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		if(Util.isEmpty(params.get("mode"))){
			throw new CapMessageException(lms2405m01.getProperty("err.chooseOne"), getClass());
		}
		int mode = params.getInt("mode");
		boolean success = false;
		if (mode == 0) {
			success = service.produceExcel(oid);
		} else if (mode == 1) {
			success = service.producePreExcel(oid);
		} else if (mode == 2) {
			success = service.produceChkExcel(oid);
		}
		if (success) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0025"));
		}

		return result;
	}

	/**
	 * 修改預計覆審日
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveDefault(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		Date defaultDate = null;
		if (!params.getString("date").isEmpty()) {
			defaultDate = CapDate.parseDate(params.getString("date"));
		}
		C240M01A c240m01a = service.findModelByOid(C240M01A.class, oid);
		MegaSSOUserDetails userId = MegaSSOSecurityContext.getUserDetails();
		String creator = c240m01a.getCreator();
		String updater = userId.getUserId();
		// 畫面經辦函有時間值
		c240m01a.setCreator(creator);
		c240m01a.setUpdater(updater);
		c240m01a.setExpectedRetrialDate(defaultDate);
		eloandbBASEService.updateC241m01a(defaultDate, c240m01a.getMainId());
		service.save(c240m01a);
		
		CapAjaxFormResult C240M01AForm = DataParse.toResult(c240m01a);
		C240M01AForm.set("creator", Util.nullToSpace(c240m01a.getCreator()) + " " 
				+ Util.nullToSpace(userInfoService.getUserName(Util.nullToSpace(c240m01a.getCreator()))));
		C240M01AForm.set("updater", Util.nullToSpace(c240m01a.getUpdater()) + " " 
				+ Util.nullToSpace(userInfoService.getUserName(Util.nullToSpace(c240m01a.getUpdater()))));
		result.set("C240M01AForm", C240M01AForm);
		

		// EFD0017=儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		return result;
	}

	/**
	 * 刪除(註記)不覆審客戶
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveNoCTL(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray(EloanConstants.OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String reason = params.getString("reason");
		reason = reason.substring(reason.indexOf(">")+1);
		StringBuilder msg = new StringBuilder();
		for(String oid : oids){
			C241M01A c241m01a = service.findModelByOid(C241M01A.class, oid);
			if (c241m01a != null) {
				if ("SYS".equals(c241m01a.getNCreatData()) || !Util.isEmpty(c241m01a.getProjectNo())) {
					// 如果已經有上傳日期註記時，則不可做不覆審註記
					if (c241m01a != null && c241m01a.getUpDate() != null) {
						if(Util.isEmpty(msg.toString())){
							msg.append(lms2405m01.getProperty("notAllow"));
						}
						continue;
//						throw new CapMessageException(
//								lms2405m01.getProperty("notAllow"), getClass());
					}
					// 判斷是否為管理單位
					if (BranchTypeEnum.營運中心.getCode().equals(
							branch.getBranch(user.getUnitNo()).getUnitType())
							|| BranchTypeEnum.授管處.getCode().equals(
									branch.getBranch(user.getUnitNo())
											.getUnitType())) {
						c241m01a.setGeneralDel("N");
					} else {
						c241m01a.setBranchDel("N");
					}
					c241m01a.setSysDel(null);
					c241m01a.setRetrialYN("N");
					c241m01a.setNCkdFlag(reason.substring(0, 1));
					c241m01a.setNCkdDate(CapDate.getCurrentTimestamp());
					// 為避免還原時又要重捉資料，故保留當時的值
					c241m01a.setRetrialDateOld(c241m01a.getRetrialDate());
					c241m01a.setLastRetrialDateOld(c241m01a.getLastRetrialDate());
					service.save(c241m01a);
					C240M01A c240m01a = service.findC240M01AMByMainId(mainId);
					lms491Service.markUpdate491(c241m01a.getNCkdFlag(), c241m01a.getNCkdDate(), 
							c241m01a.getNCkdMemo(), c240m01a.getBranchId(), c241m01a.getCustId(), c241m01a.getDupNo());
				} else {
					service.deleteC241M01AAndC240M01B(oid);
				}
			}
		}
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		int count = service.saveCTLCount(mainId);
		result.set("reQuantity", count);
		if(Util.isEmpty(msg.toString())){
			// EFD0018=執行成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}else{
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, msg.toString());
		}

		return result;
	}

	/**
	 * 還原不覆審客戶
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveReCTL(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray(EloanConstants.OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
//		String docStatus = params.getString(EloanConstants.MAIN_DOC_STATUS, "");
		for(String oid : oids){
			C241M01A c241m01a = service.findModelByOid(C241M01A.class, oid);
			if (c241m01a != null) {
				c241m01a.setRetrialYN("Y");
				c241m01a.setSysDel(null);
				c241m01a.setBranchDel(null);
				c241m01a.setGeneralDel(null);
				c241m01a.setNCkdFlag(null);
				c241m01a.setNCkdDate(null);
				c241m01a.setNCkdMemo(null);
				C240M01A c240m01a = service.findC240M01AMByMainId(mainId);
//				if (c241m01a.getRetrialDateOld() != null) {
//					c241m01a.setRetrialDate(c241m01a.getRetrialDateOld());
//				}else 
//				if(RetrialDocStatusEnum.已產生覆審名單報告檔.getCode().equals(c240m01a.getDocStatus())){
				//實際覆審日期為預計覆審日(主檔)
				c241m01a.setRetrialDate(c240m01a.getExpectedRetrialDate());
//				}
				if (c241m01a.getLastRetrialDateOld() != null) {
					c241m01a.setLastRetrialDate(c241m01a.getLastRetrialDateOld());
				}
				if(Util.isEmpty(Util.trim(c241m01a.getProjectNo())) && !Util.isEmpty(c240m01a.getBatchNO())){
					Map<String, Object> maxMap = eloandbBASEService.findMaxProjectNo(mainId);
					String max = Util.trim(maxMap.get("PROJECTNO"));
					Integer maxInt = 0;
					if(max.length() > 4 && Util.isInteger(max.substring(max.length()-4, max.length()-1))){
						maxInt = Integer.valueOf(max.substring(max.length()-4, max.length()-1));
					}
					c241m01a.setProjectNo(numberService
							.getCaseNumber(C240M01A.class, c240m01a.getBranchId(),
									TWNDate.toAD(c240m01a.getDataEndDate()).substring(0, 4),
									Util.addZeroWithValue(c240m01a.getBatchNO(), 3)
											+ "-" + Util.addZeroWithValue(maxInt+1, 3)));
				}
			}
			service.save(c241m01a);
			C240M01A c240m01a = service.findC240M01AMByMainId(mainId);
			lms491Service.markUpdate491(c241m01a.getNCkdFlag(), c241m01a.getNCkdDate(), 
					c241m01a.getNCkdMemo(), c240m01a.getBranchId(), c241m01a.getCustId(), c241m01a.getDupNo());
		}
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		int count = service.saveCTLCount(mainId);
		result.set("reQuantity", count);
		// EFD0017=儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));

		return result;
	}

	/**
	 * 給號
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult number(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);

		service.getNumber(oid, true);

		// EFD0017=儲存成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));

		return result;
	}

	/**
	 * 刪除註記
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteMark(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String list = params.getString("list");
		
		boolean edit = false;
		C240M01A c240m01a = service.findModelByOid(C240M01A.class, list);
		List<DocOpener> docOpeners = docCheckService.findByMainId(c240m01a.getMainId());
		for(DocOpener docOpener : docOpeners){
			if(OpenTypeCode.Writing.getCode().equals(docOpener.getOpenType())){
				HashMap<String, String> hm = new HashMap<String, String>();
				hm.put("userId", docOpener.getOpener());
				hm.put("userName",
						userInfoService.getUserName(docOpener.getOpener()));
				edit = true;
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMessage("EFD0009", hm));
				break;
			}
		}
		if(!edit){
			service.deleteMainMark(list);
			// EFD0019=刪除成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
		}
		return result;
	}

	/**
	 * 刪除
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult deleteMain(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String list = params.getString("list");

		service.deleteC240M01A(list);
		// EFD0019=刪除成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
		return result;
	}

	/**
	 * 呈主管覆核
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult r = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String oid = params.getString(EloanConstants.MAIN_OID);
		C240M01A c240m01a = service.findModelByOid(C240M01A.class, oid);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		if (Util.parseInt(params.getString("page")) == 2) {
			Double thisSamplingRate = params.getAsDouble("thisSamplingRate");
			if(!Util.isEmpty(thisSamplingRate)){
				c240m01a.setThisSamplingRate(thisSamplingRate.intValue());
				service.save(c240m01a);
			}
		}
		if(RetrialDocStatusEnum.編製中.getCode().equals(c240m01a.getDocStatus()) 
				&& Util.isEmpty(c240m01a.getExpectedRetrialDate())){
			throw new CapMessageException(lms2405m01.getProperty("err.noDate"), getClass());
		}
		//檢查經辦和主管是否為同一人
		boolean decition = params.getAsBoolean("flowAction", false);
		if(decition && user.getUserId().equals(c240m01a.getUpdater())){
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		if (c240m01a != null) {
			try {
				service.flowAction(c240m01a.getOid(), c240m01a,
						params.containsKey("flowAction"),
						params.getAsBoolean("flowAction", false));
			} catch (Throwable t1) {
				logger.error("LMS2405M01FormHandle flowAction EXCEPTION!!", t1);
			}
		}
		return r;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryPath(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String fieldId = "";
		int mode = params.getInt("mode");
		if (mode == 0) {
			fieldId = "listExcel";
		} else if (mode == 1) {
			fieldId = "listPre";
		} else if (mode == 2) {
			fieldId = "listChk";
		}
		C240M01A c240m01a = service.findModelByOid(C240M01A.class, oid);
		List<DocFile> docFiles = service.findDocFile(c240m01a.getMainId(),
				fieldId);

		String fileOid = (String) docFiles.get(0).getOid();
		result.set("fileOid", fileOid);
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteLoc(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String oid = params.getString(EloanConstants.OID);
		C240M01A c240m01a = service.findModelByOid(C240M01A.class, oid);
		if (c240m01a != null) {
			List<DocOpener> docOpeners = docCheckService.findByMainId(c240m01a
					.getMainId());
			for (DocOpener docOpener : docOpeners) {
				if (user.getUserId().equals(docOpener.getOpener())) {
					docCheckService.unlockDoc(docOpener);
				}
			}
		}

		return result;
	}

	/**
	 * 呈主管覆核
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult showSearchData(PageParameters params)
			throws CapException {
		StringBuffer str = new StringBuffer();
		List<String> resultList = null;
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = params.getString("custId", "");
		String dupNo = params.getString("dupNo", "");
		
		Map<String, Object> custDataMap = misCustdataService.findAllByByCustIdAndDupNo(custId, dupNo);
		if(custDataMap == null){
			custDataMap = new LinkedHashMap<String,Object>();
		}
		result.set("custNameData", Util.trim(custDataMap.get("CNAME")) + " " + custId + " " + dupNo);
		List<Map<String, Object>> dwList = dwdbBASEService
				.findDWADMSselDistinctByCust_Key(custId);
		List<Map<String, Object>> lms491List = (List<Map<String, Object>>) lms491Service
		.find491BycustIdDataNoBranch(custId, dupNo);
		resultList = new LinkedList<String>();
		for (Map<String, Object> map : lms491List) {
			str.setLength(0);
			str.append(Util.nullToSpace(map.get("LRDATE"))).append("!")
					.append(Util.nullToSpace(map.get("CRDATE"))).append("!")
					.append(Util.nullToSpace(map.get("NCKDFLAG"))).append("!")
					.append(Util.nullToSpace(map.get("NCKDDATE"))).append("!")
					.append(Util.nullToSpace(map.get("NCKDMEMO"))).append("!")
					.append(Util.nullToSpace(branch.getBranchName(Util.nullToSpace(map.get("BRANCH")))));
			resultList.add(str.toString());
		}
		// str.append("1!2!3!4!5");
		// resultList.add(str.toString());
		// str.setLength(0);
		// str.append("6!7!8!9!10");
		// resultList.add(str.toString());
		result.set("data491", resultList);
		resultList = new LinkedList<String>();
		for (Map<String, Object> map : dwList) {
			str.setLength(0);
			str.append(Util.nullToSpace(map.get("CANCEL_DT"))).append("!")
					.append(Util.nullToSpace(map.get("FACT_CONTR"))).append("!")
					.append(Util.nullToSpace(branch.getBranchName(Util.nullToSpace(map.get("BR_CD")))));
			resultList.add(str.toString());
		}
		// str.append("1!2");
		// resultList.add(str.toString());
		// str.setLength(0);
		// str.append("6!7");
		// resultList.add(str.toString());
		result.set("datadw", resultList);
		
		return result;
	}

	/**
	 * 處理從service回傳回來的訊息
	 * 
	 * @param result
	 * @param code
	 */
	private void HandlerResultWarmMsg(CapAjaxFormResult result) {
		if (result.containsKey("WARMMSG")) {
			if (result.containsKey("SUCCESSCODE")) {
				result.set(
						CapConstants.AJAX_NOTIFY_MESSAGE,
						result.get(CapConstants.AJAX_NOTIFY_MESSAGE)
								+ "<BR/><BR/>"
								+ Util.nullToSpace(result.get("WARMMSG")));
			} else {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						Util.nullToSpace(result.get("WARMMSG")));
			}

		}
	}

	/**
	 * 處理若有無法轉換的幣別匯率 則回傳內容
	 * 
	 * @param map
	 * @return
	 */
	private CapAjaxFormResult handleRateObjMap(Map<String, Object> map,
			CapAjaxFormResult result) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS2415M01Page.class);
		if (map.get("noRateCurr") != null) {
			result.set(
					"WARMMSG",
					pop.getProperty("C241M01b.warmMsg01")
							+ map.get("noRateCurr"));
		}
		return result;
	}
	
	/**
	 * 更新lms491
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult update490(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<IBranch> bank = branch.getBranchByUnitType(
				BranchTypeEnum.海外分行.getCode(), 
				BranchTypeEnum.海外分行當地有總行.getCode(), 
				BranchTypeEnum.海外總行泰國.getCode(), 
				BranchTypeEnum.海外總行澳洲加拿大.getCode());

		List<String> banks = new ArrayList<String>();
		for (int i = 0, size = bank.size(); i < size; i++) {
			banks.add(bank.get(i).getBrNo());
		}
		Date dataDate = CapDate.parseDate(CapDate
				.getCurrentDate(UtilConstants.DateFormat.YYYY_MM) + "-01");
		StringBuilder failBanks = new StringBuilder();
		for (int i = 0, size = banks.size(); i < size; i++) {
			try {
				service.update490(banks.get(i), dataDate);
			} catch (Exception e) {
				failBanks.append(banks.get(i) + ",");
				logger.error("LMS2405M01FormHandle update490 EXCEPTION!!", e);
				this.logger.debug(e.getMessage());
			}
		}
		
		if (!Util.isEmpty(failBanks.toString())) {
			Map<String, String> msg = new HashMap<String, String>();
			msg.put("msg", failBanks.toString());
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMessage("EFD0025", msg));
		} else {
			// 執行成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}

		return result;
	}
	

	public IResult overSeaProgram(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("overSeaProgram", retrialService.overSeaProgram());		
		return result;
	}
	
	/**
	 * 搜尋分行
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	public IResult queryBranch(PageParameters params)
			throws CapException {
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		TreeMap<String, String> tm = retrialService.getBranch(user.getUnitNo());
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("item", new CapAjaxFormResult(tm));
		result.set("itemOrder", new ArrayList<String>(tm.keySet()));
		return result;
	}
}
