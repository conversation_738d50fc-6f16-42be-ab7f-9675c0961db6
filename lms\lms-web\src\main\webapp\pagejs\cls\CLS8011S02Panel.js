var initDfd = initDfd ||$.Deferred();
initDfd.done(function(json){
	build_c801m01b(json);
	//在 build 完頁面後,若 M01 是 lockDoc, 也跟著 lock
	if(json['initControl_lockDoc']){
		$("#tabForm").lockDoc();		
	}
	//===================================================
	$("#btn_markAll").click(function(){
		$("#tabForm").find(".sel_itemCheck").val("Y");
	});	
	$("#btn_unMarkAll").click(function(){
		$("#tabForm").find(".sel_itemCheck").val("N");
	});	
	$("#btn_addC801M01B").click(function(){
		var itemType = $("#tabForm").find("#pageItemType").val();
		
		$.ajax({
			type: "POST",
	        handler: _handler,
	           data: { formAction: "addC801M01B"
	        	   , 'pageItemType': itemType
	        	   , 'mainOid':$("#mainOid").val()
	        	   }
	           }).done(function(json_addC801M01B){
	        	   build_c801m01b(json_addC801M01B);	
		});
	});	
	//===================================================
	function build_c801m01b(json){
	
		var dyna = [];		
		
		var itemType = $("#tabForm").find("#pageItemType").val();
			
		var arr = json.c801m01b_list[itemType];
		if(arr.length>0){
			var atFirst = true;
			//每一個 itemType 包含的項目
			$.each(arr, function(idx, jsonItem) {
				buildTr(dyna, jsonItem);
			});	
			$("#c801m01b_content").append(dyna.join("\n"));
			$("#c801m01b_content").find(".btn_delSelfAddItem").click(function(){
				var c801m01b_oid = $(this).closest("tr").attr("id");
				//是否確定「刪除」此筆資料?
				API.confirmMessage(i18n.def.action_003, function(result){
		            if (result) {
		            	saveAction().done(function(json){
		        			if(json.saveOkFlag){
		        				$.ajax({
				        			type: "POST",
				        	        handler: _handler,
				        	           data: { formAction: "delC801M01B"
				        	        	   , 'mainOid':$("#mainOid").val()
				        	        	   , 'c801m01b_oid':c801m01b_oid
				        	        	   }
				        	           }).done(function(json_delC801M01B){
				        	        	   	if(json_delC801M01B.delFlag){
												var page = json_delC801M01B.page;
				        	        	   		var tData = {'mainDocStatus': $("#mainDocStatus").val()
				    	    		        	   		, 'mainId': $("#mainId").val()
				    	    		        	   		, 'mainOid': $("#mainOid").val()
			    	    		        	   	};
			    	    		        	   	$.form.submit({ url: page , data: tData });
				        	           }
				        		});
		        			}
		                });		            	
		            }
				});
			});				
		}
	}
	
	function buildTr(dyna, jsonItem){
		dyna.push("<tr style='vertical-align:top' id='c801m01b_"+jsonItem.oid+"' >");
		if(true){
			dyna.push("   <td align='right'>"+jsonItem.itemSeq+"&nbsp;&nbsp;");
			if(jsonItem.isSelfAdd=='Y'){
				dyna.push("   <div align='center'><a href='#' class='btn_delSelfAddItem'>刪</a></div>");
			}
			dyna.push("   </td>");
		}
		
		if(true){
			var _name_itemCheck = "_itemCheck_"+(jsonItem.itemType)+"_"+(jsonItem.itemCode);
			dyna.push("   <td>");
			buildSel_itemCheck(dyna, _name_itemCheck, jsonItem.itemCheck);
			dyna.push("   </td>");	
		}
		
		if(true){			
			if(jsonItem.isSelfAdd=='Y'){
				dyna.push("   <td>");
				var _name_itemContent = "_itemContent_"+(jsonItem.itemType)+"_"+(jsonItem.itemCode);
				dyna.push("   <input type='text' id='"+_name_itemContent+"' name='"+_name_itemContent+"' value='"+jsonItem.itemContent+"' maxlength='300' maxlengthC='100' size='100' >");				
				dyna.push("   </td>");
			}else{
				dyna.push("   <td>"+jsonItem.itemContent+"</td>");	
			}	
		}		
		
		if(true){
			var _name_riskLevel = "_riskLevel_"+(jsonItem.itemType)+"_"+(jsonItem.itemCode);
			dyna.push("   <td>");
			buildSel_riskLevel(dyna, _name_riskLevel, jsonItem.riskLevel);
			dyna.push("   </td>");
		}
		dyna.push("   <td align='right'>"+jsonItem.itemCode+"&nbsp;</td>");
		dyna.push("</tr>");
		
	}
	function buildSel_itemCheck(dyna, elmName, chooseVal){
		dyna.push("<select name='"+elmName+"' id='"+elmName+"' class='sel_itemCheck' >");		
		dyna.push("<option value='N' "+(chooseVal=='N'?" selected ":"")+">"+i18n.cls8011m01['C801M01B.itemCheck.N']+"</option>");
		dyna.push("<option value='Y' "+(chooseVal=='Y'?" selected ":"")+">"+i18n.cls8011m01['C801M01B.itemCheck.Y']+"</option>");
		dyna.push("</select>");
	}
	
	function buildSel_riskLevel(dyna, elmName, chooseVal){
		dyna.push("<select name='"+elmName+"' id='"+elmName+"' >");
		dyna.push("<option value='H' "+(chooseVal=='H'?" selected ":"")+">"+i18n.cls8011m01['C801M01B.riskLevel.H']+"&nbsp;&nbsp;</option>");
		dyna.push("<option value='M' "+(chooseVal=='M'?" selected ":"")+">"+i18n.cls8011m01['C801M01B.riskLevel.M']+"&nbsp;&nbsp;</option>");
		dyna.push("<option value='L' "+(chooseVal=='L'?" selected ":"")+">"+i18n.cls8011m01['C801M01B.riskLevel.L']+"&nbsp;&nbsp;</option>");
		dyna.push("</select>");
	}	
	
});
