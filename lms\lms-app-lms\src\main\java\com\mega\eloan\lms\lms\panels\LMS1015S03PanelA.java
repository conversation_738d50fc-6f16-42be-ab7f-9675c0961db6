package com.mega.eloan.lms.lms.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 消金信用評等模型(擔保品)
 * 此 panel 為純 html(不含 js)，被以下 panel include
 * ● LMS1015S03Panel
 * ● LMS1405S02Panel05
 * 
 * 若直接在 LMS1405S02Panel05 去 add LMS1015S03Panel(沒有A)
 * 因 LMS1015S03Panel 裡面有 js
 * 會抓不到 js 的 path,出現 HTTP 404
 * </pre>
 * 
 * @since 2015/3/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/3/1,EL08034,new
 *          </ul>
 */
public class LMS1015S03PanelA extends Panel {
	private static final long serialVersionUID = 1L;

	public LMS1015S03PanelA(String id) {
		super(id);
	}
}
