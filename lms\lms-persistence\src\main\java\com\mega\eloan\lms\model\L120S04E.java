/* 
 * L120S04E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 往來實績彙總表利潤貢獻度業務別占比檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S04E", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S04E extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 分項統一編號 **/
	@Size(max=10)
	@Column(name="KEYCUSTID", length=10, columnDefinition="VARCHAR (10)")
	private String keyCustId;

	/** 分項重覆序號 **/
	@Size(max=1)
	@Column(name="KEYDUPNO", length=1, columnDefinition="CHAR (1)")
	private String keyDupNo;

	/** 
	 * 種類<p/>
	 * 1：借款人<br/>
	 *  2：借款人暨關係戶
	 */
	@Size(max=1)
	@Column(name="DOCKIND", length=1, columnDefinition="CHAR(1)")
	private String docKind;

	/** 
	 * 資料年月<p/>
	 * 完整年度：YYYY年<br/>
	 *  當年度：YYYY/MM~MM月
	 */
	@Size(max=20)
	@Column(name="DOCDATE", length=20, columnDefinition="VARCHAR(20)")
	private String docDate;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 授信_利潤貢獻<p/>
	 * TWD仟元
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="LOANPCAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal loanPcAmt;

	/**
	 * 存款外匯_利潤貢獻<p/>
	 * TWD仟元
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="DEPFXPCAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal depFxPcAmt;

	/**
	 * 財管_利潤貢獻<p/>
	 * TWD仟元
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="WMPCAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal wmPcAmt;

	/**
	 * 衍生性商品_利潤貢獻<p/>
	 * TWD仟元
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="DERVPCAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal dervPcAmt;

	/**
	 * 員工薪轉_利潤貢獻<p/>
	 * TWD仟元
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="SALARYPCAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal salaryPcAmt;

	/**
	 * 信用卡_利潤貢獻<p/>
	 * TWD仟元
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="CARDPCAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal cardPcAmt;

	/**
	 * 合計_利潤貢獻<p/>
	 * TWD仟元
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="TOTALPCAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal totalPcAmt;

	/**
	 * 其他_利潤貢獻<p/>
	 * TWD仟元
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="OTHERPCAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal otherPcAmt;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得分項統一編號 **/
	public String getKeyCustId() {
		return this.keyCustId;
	}
	/** 設定分項統一編號 **/
	public void setKeyCustId(String value) {
		this.keyCustId = value;
	}

	/** 取得分項重覆序號 **/
	public String getKeyDupNo() {
		return this.keyDupNo;
	}
	/** 設定分項重覆序號 **/
	public void setKeyDupNo(String value) {
		this.keyDupNo = value;
	}

	/** 
	 * 取得種類<p/>
	 * 1：借款人<br/>
	 *  2：借款人暨關係戶
	 */
	public String getDocKind() {
		return this.docKind;
	}
	/**
	 *  設定種類<p/>
	 *  1：借款人<br/>
	 *  2：借款人暨關係戶
	 **/
	public void setDocKind(String value) {
		this.docKind = value;
	}

	/** 
	 * 取得資料年月<p/>
	 * 完整年度：YYYY年<br/>
	 *  當年度：YYYY/MM~MM月
	 */
	public String getDocDate() {
		return this.docDate;
	}
	/**
	 *  設定資料年月<p/>
	 *  完整年度：YYYY年<br/>
	 *  當年度：YYYY/MM~MM月
	 **/
	public void setDocDate(String value) {
		this.docDate = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得授信_利潤貢獻<p/>
	 * TWD仟元
	 */
	public BigDecimal getLoanPcAmt() {
		return this.loanPcAmt;
	}
	/**
	 *  設定授信_利潤貢獻<p/>
	 *  TWD仟元
	 **/
	public void setLoanPcAmt(BigDecimal value) {
		this.loanPcAmt = value;
	}

	/**
	 * 取得存款外匯_利潤貢獻<p/>
	 * TWD仟元
	 */
	public BigDecimal getDepFxPcAmt() {
		return this.depFxPcAmt;
	}
	/**
	 *  設定存款外匯_利潤貢獻<p/>
	 *  TWD仟元
	 **/
	public void setDepFxPcAmt(BigDecimal value) {
		this.depFxPcAmt = value;
	}

	/** 
	 * 取得財管_利潤貢獻<p/>
	 * TWD仟元
	 */
	public BigDecimal getWmPcAmt() {
		return this.wmPcAmt;
	}
	/**
	 *  設定財管_利潤貢獻<p/>
	 *  TWD仟元
	 **/
	public void setWmPcAmt(BigDecimal value) {
		this.wmPcAmt = value;
	}

	/** 
	 * 取得衍生性商品_利潤貢獻<p/>
	 * TWD仟元
	 */
	public BigDecimal getDervPcAmt() {
		return this.dervPcAmt;
	}
	/**
	 *  設定衍生性商品_利潤貢獻<p/>
	 *  TWD仟元
	 **/
	public void setDervPcAmt(BigDecimal value) {
		this.dervPcAmt = value;
	}

	/** 
	 * 取得員工薪轉_利潤貢獻<p/>
	 * TWD仟元
	 */
	public BigDecimal getSalaryPcAmt() {
		return this.salaryPcAmt;
	}
	/**
	 *  設定員工薪轉_利潤貢獻<p/>
	 *  TWD仟元
	 **/
	public void setSalaryPcAmt(BigDecimal value) {
		this.salaryPcAmt = value;
	}

	/** 
	 * 取得信用卡_利潤貢獻<p/>
	 * TWD仟元
	 */
	public BigDecimal getCardPcAmt() {
		return this.cardPcAmt;
	}
	/**
	 *  設定信用卡_利潤貢獻<p/>
	 *  TWD仟元
	 **/
	public void setCardPcAmt(BigDecimal value) {
		this.cardPcAmt = value;
	}

	/**
	 * 取得合計_利潤貢獻<p/>
	 * TWD仟元
	 */
	public BigDecimal getTotalPcAmt() {
		return this.totalPcAmt;
	}
	/**
	 *  設定合計_利潤貢獻<p/>
	 *  TWD仟元
	 **/
	public void setTotalPcAmt(BigDecimal value) {
		this.totalPcAmt = value;
	}

	/**
	 * 取得其他_利潤貢獻<p/>
	 * TWD仟元
	 */
	public BigDecimal getOtherPcAmt() {
		return this.otherPcAmt;
	}
	/**
	 *  設定其他_利潤貢獻<p/>
	 *  TWD仟元
	 **/
	public void setOtherPcAmt(BigDecimal value) {
		this.otherPcAmt = value;
	}
}
