package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.RelativeMeta;


/**
 * <pre>
 * C140S04B model.
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/20,<PERSON>,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140S04B-entity-graph", attributeNodes = { @NamedAttributeNode("c140m04a") })
@Entity
@Table(name="C140S04B", uniqueConstraints = @UniqueConstraint(columnNames ={"oid"}))
public class C140S04B extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

	@Column(precision=12)
	private BigDecimal landAmtUnit;

	@Column(length=3)
	private String landCurr;

	@Column(length=120)
	private String landAddr;

	@Column(precision=11, scale=2)
	private BigDecimal landAm;
	
	@Column(precision=11, scale=2)
	private BigDecimal landVal;
	
	@Column(precision=11, scale=2)
	private BigDecimal landAp;

	@Column(precision=11, scale=2)
	private BigDecimal landBm;

	@Column(precision=11, scale=2)
	private BigDecimal landBp;

	@Column(length=2)
	private String landLevel;
	
	@Column(length=30)
	private String landCust;

	@Column(precision=12)
	private BigDecimal landMm;

	@Column(length=60)
	private String landMp;

	@Column(length=20)
	private String landNum;

	@Column(precision=12)
	private BigDecimal landRateC;

	@Column(precision=12)
	private BigDecimal landRateD;

	@Column(length=1)
	private String landUnit1;

	@Column(length=1)
	private String landUnit2;

	@Column(length=1)
	private String landUse1;

	@Column(length=2)
	private String landUse2;
	
	@Column(length=3)
	private String curr;
	
	@Column(precision=12)
	private BigDecimal amtUnit;
	
	@Column(length=2)
	private String city;
	
	@Column(length=3)
	private String zone ;
	
	@Column(length=30)
	private String section;
	
	@Column(length=30)
	private String sSection;
	
	//bi-directional many-to-one association to C140M04A
    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false)
		})
	private C140M04A c140m04a;

	public BigDecimal getLandAmtUnit() {
		return this.landAmtUnit;
	}

	public void setLandAmtUnit(BigDecimal landAmtUnit) {
		this.landAmtUnit = landAmtUnit;
	}

	public String getLandCurr() {
		return this.landCurr;
	}

	public void setLandCurr(String landCurr) {
		this.landCurr = landCurr;
	}

	public String getLandAddr() {
		return this.landAddr;
	}

	public void setLandAddr(String landAddr) {
		this.landAddr = landAddr;
	}

	public BigDecimal getLandAm() {
		return this.landAm;
	}

	public void setLandAm(BigDecimal landAm) {
		this.landAm = landAm;
	}

	public BigDecimal getLandAp() {
		return this.landAp;
	}

	public void setLandAp(BigDecimal landAp) {
		this.landAp = landAp;
	}

	public BigDecimal getLandBm() {
		return this.landBm;
	}

	public void setLandBm(BigDecimal landBm) {
		this.landBm = landBm;
	}

	public BigDecimal getLandBp() {
		return this.landBp;
	}

	public void setLandBp(BigDecimal landBp) {
		this.landBp = landBp;
	}

	public String getLandLevel() {
		return this.landLevel;
	}

	public void setLandLevel(String landLevel) {
		this.landLevel = landLevel;
	}
	
	public String getLandCust() {
		return this.landCust;
	}

	public void setLandCust(String landCust) {
		this.landCust = landCust;
	}

	public BigDecimal getLandMm() {
		return this.landMm;
	}

	public void setLandMm(BigDecimal landMm) {
		this.landMm = landMm;
	}

	public String getLandMp() {
		return this.landMp;
	}

	public void setLandMp(String landMp) {
		this.landMp = landMp;
	}

	public String getLandNum() {
		return this.landNum;
	}

	public void setLandNum(String landNum) {
		this.landNum = landNum;
	}

	public BigDecimal getLandRateC() {
		return this.landRateC;
	}

	public void setLandRateC(BigDecimal landRateC) {
		this.landRateC = landRateC;
	}

	public BigDecimal getLandRateD() {
		return this.landRateD;
	}

	public void setLandRateD(BigDecimal landRateD) {
		this.landRateD = landRateD;
	}

	public String getLandUnit1() {
		return this.landUnit1;
	}

	public void setLandUnit1(String landUnit1) {
		this.landUnit1 = landUnit1;
	}

	public String getLandUnit2() {
		return this.landUnit2;
	}

	public void setLandUnit2(String landUnit2) {
		this.landUnit2 = landUnit2;
	}

	public String getLandUse1() {
		return this.landUse1;
	}

	public void setLandUse1(String landUse1) {
		this.landUse1 = landUse1;
	}

	public String getLandUse2() {
		return this.landUse2;
	}

	public void setLandUse2(String landUse2) {
		this.landUse2 = landUse2;
	}

	public C140M04A getC140m04a() {
		return this.c140m04a;
	}

	public void setC140m04a(C140M04A c140m04a) {
		this.c140m04a = c140m04a;
	}

	public String getCurr() {
		return curr;
	}

	public void setCurr(String curr) {
		this.curr = curr;
	}

	public BigDecimal getAmtUnit() {
		return amtUnit;
	}

	public void setAmtUnit(BigDecimal amtUnit) {
		this.amtUnit = amtUnit;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getZone() {
		return zone;
	}

	public void setZone(String zone) {
		this.zone = zone;
	}

	public String getSection() {
		return section;
	}

	public void setSection(String section) {
		this.section = section;
	}

	public String getsSection() {
		return sSection;
	}

	public void setsSection(String sSection) {
		this.sSection = sSection;
	}

	public BigDecimal getLandVal() {
		return landVal;
	}

	public void setLandVal(BigDecimal landVal) {
		this.landVal = landVal;
	}
		
}