package com.mega.eloan.lms.mfaloan.service.impl;


import java.util.List;
import java.util.Map;


import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisStuDataService;

@Service
public class MisStuDataServiceImpl extends AbstractMFAloanJdbc implements
MisStuDataService {
	@Override
	public List<Map<String, Object>> getStuData() {
		return getJdbc().queryForList("MIS.STUDATA.Get",
				new String[] {  }, 0, 2000);
	}

	
}
