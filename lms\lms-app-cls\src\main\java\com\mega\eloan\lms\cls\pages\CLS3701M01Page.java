package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.C126M01A;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 房仲引介案件(編製中)
 * </pre>
 *
 * @since 2020/05/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/11/03
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls3701m01/{page}")
public class CLS3701M01Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model,
				new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
						AuthType.Modify, CreditDocStatusEnum.海外_編製中,
						CreditDocStatusEnum.先行動用_已覆核));
		
		addAclLabel(model,
				new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
						AuthType.Accept, CreditDocStatusEnum.海外_編製中,
						CreditDocStatusEnum.先行動用_已覆核));
		
		
		renderJsI18N(CLS3701M01Page.class);

		new DocLogPanel("_docLog").processPanelData(model, params);
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return C126M01A.class;
	}

}
