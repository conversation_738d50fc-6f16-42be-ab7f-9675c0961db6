package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisElacnmService;

@Service
public class MisElacnmServiceImpl extends AbstractMFAloanJdbc implements
		MisElacnmService {

	@Override
	public Map<String, String> getDpCode() {
		Map<String, String> dpCode = new HashMap<String, String>();
		List<Map<String, Object>> ElacnmList = getJdbc().queryForList(
				"ELACNM.GetAll", new String[] {});
		
		//放款科目的對照
		for (Map<String, Object> ElacnmMap : ElacnmList) {
			dpCode.put((String) ElacnmMap.get("LNTP"),
					(String) ElacnmMap.get("BACTNM"));
		}
		
		//會計科目的對照
		for (Map<String, Object> ElacnmMap : ElacnmList) {
			dpCode.put((String) ElacnmMap.get("ACTCD"),
					(String) ElacnmMap.get("BACTNM"));
		}
		return dpCode;
	}

}
