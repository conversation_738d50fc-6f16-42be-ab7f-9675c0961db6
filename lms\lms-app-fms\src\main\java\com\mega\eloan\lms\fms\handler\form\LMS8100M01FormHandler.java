package com.mega.eloan.lms.fms.handler.form;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.pages.LMS8100M01Page;
import com.mega.eloan.lms.fms.service.LMS8100Service;
import com.mega.eloan.lms.model.L300M01A;
import com.mega.eloan.lms.model.L300M01B;
import com.mega.eloan.lms.model.L300M01C;
import com.mega.eloan.lms.model.L300S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.SheetSettings;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.PaperSize;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 覆審考核表作業
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms8100m01formhandler")
@DomainClass(L300M01A.class)
public class LMS8100M01FormHandler extends AbstractFormHandler {

	@Resource
	DocFileService docFileService;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	FlowSimplifyService flowSimplifyService;

	@Resource
	EloandbBASEService eloanDbBaseService;

	@Resource
	LMSService lmsService;

	@Resource
	LMS8100Service lms8100Service;

	@Resource
	RetrialService retrialService;
	
	/**
	 * J-112-0461 授信覆審考核表-編製中-下拉選項統計由每半年改為每季統計。
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult queryProduceInterval(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String produceInterval = lms8100Service.findProduceInterval(CapDate
				.formatDate(new Date(), UtilConstants.DateFormat.YYYY_MM_DD));
		result.set("produceInterval", produceInterval);
		return result;
	}
		
	public IResult queryBrList(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		UnitTypeEnum unitType = UnitTypeEnum.convertToUnitType(branchService
				.getBranch(user.getUnitNo()));
		List<IBranch> ibranchs = null;
		if (unitType.isEquals(UnitTypeEnum.營運中心)) {
			ibranchs = branchService.getBranchOfGroup(user.getUnitNo());

			// 非營運中心單位931覆審
			String[] branch_K = retrialService.getRetrailSpecialBranch();
			for (String brnoK : branch_K) {
				String newBranch = Util.trim(retrialService
						.getRetrailNewBranch(brnoK));
				if (Util.notEquals(brnoK, user.getUnitNo())
						&& Util.equals(newBranch, user.getUnitNo())) {
					ibranchs.add(branchService.getBranch(brnoK));
				}
			}

		} else {
			ibranchs = branchService.getAllBranch();
		}

		TreeMap<String, String> brMap = new TreeMap<String, String>();
		for (IBranch branch : ibranchs) {
			brMap.put(Util.trim(branch.getBrNo()),
					Util.trim(branch.getBrName()));
		}

		CapAjaxFormResult bankList = new CapAjaxFormResult(brMap);
		result.set("brList", bankList);
		result.set("brListOrder", new ArrayList<String>(brMap.keySet()));
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult chkCanAdd(PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String brList = Util.trim(params.getString("brList"));
		String[] brs = brList.split(UtilConstants.Mark.SPILT_MARK);
		String bgnDate = Util.trim(params.getString("bgnDate"));
		String endDate = Util.trim(params.getString("endDate"));

		List<String> hasBr = new ArrayList<String>();
		for (String br : brs) {
			L300M01A l300m01a = lms8100Service.findL300m01aExist(user.getUnitNo(),
					br, bgnDate, endDate);
			if (l300m01a == null) {

			} else {
				hasBr.add(br);
			}
		}
		if (hasBr != null && !hasBr.isEmpty()) {
			result.set("msg", "分行" + StringUtils.join(hasBr, "、")
					+ "已存在資料，請先刪除！");
		}

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newL300m01a(PageParameters params)
			throws CapException, WriteException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = Util.nullToSpace(user.getUnitNo());
		IBranch ibranch = branchService.getBranch(brNo);

		String brList = Util.trim(params.getString("brList"));
		String[] brs = brList.split(UtilConstants.Mark.SPILT_MARK);
		String bgnDate = Util.trim(params.getString("bgnDate"));
		String endDate = Util.trim(params.getString("endDate"));

		List<L300M01A> l300m01aList = new ArrayList<L300M01A>();
		for (String br : brs) {
			L300M01A l300m01a = new L300M01A();
			String l300m01aMainid = "";
			l300m01aMainid = IDGenerator.getUUID();
			l300m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			l300m01a.setOwnBrId(brNo);
			l300m01a.setMainId(l300m01aMainid);
			l300m01a.setCreator(user.getUserId());
			l300m01a.setCreateTime(CapDate.getCurrentTimestamp());
			String txCode = Util.trim(params
					.getString(EloanConstants.TRANSACTION_CODE));
			l300m01a.setTxCode(txCode);
			// UPGRADE: 待確認，URL是否正確
			l300m01a.setDocURL(params.getString("docUrl"));
			if (ibranch != null) {
				l300m01a.setUnitType(UnitTypeEnum.convertToUnitType(ibranch
						.getUnitType()));
			}
			l300m01a.setBranchId(br);
			l300m01a.setBgnDate(CapDate.getDate(bgnDate, "yyyy-MM-dd"));
			l300m01a.setEndDate(CapDate.getDate(endDate, "yyyy-MM-dd"));
			l300m01a.setAssDate(Util.parseDate(CapDate
					.getCurrentDate("yyyy-MM-dd")));
			l300m01a.setProduceFlag("N");
			String paVer = retrialService.getPaFormVerByDate(
					CapDate.getDate(bgnDate, "yyyy-MM-dd"));
			l300m01a.setPaVer(paVer);

			// 初始化明細資料
			this.initDetail(l300m01aMainid, l300m01a);

			List<Map<String, String>> xlsDataList = new ArrayList<Map<String, String>>();
			// 明細資料
			this.findDetail(l300m01a, l300m01aMainid, brNo, br, bgnDate,
					endDate, xlsDataList);

			// 產生明細xls
			this.genDetailXls(l300m01a, xlsDataList);

			l300m01aList.add(l300m01a);
		}

		if (l300m01aList != null && !l300m01aList.isEmpty()) {
			lms8100Service.saveL300m01aList(l300m01aList, true);
		}

		CapAjaxFormResult result = new CapAjaxFormResult();
		return result;
	}

	@SuppressWarnings("unchecked")
	public void initDetail(String l300m01aMainid, L300M01A l300m01a) {
		// 先刪除既有明細資料
		List<L300S01A> l300s01aList = (List<L300S01A>) lms8100Service
				.findListByMainId(L300S01A.class, l300m01aMainid);
		if (l300s01aList != null && l300s01aList.size() > 0) {
			lms8100Service.deleteL300s01as(l300s01aList);
		}

		// new
		List<L300S01A> l300s01as = new ArrayList<L300S01A>();
		if (Util.isNotEmpty(Util.nullToSpace(l300m01aMainid))) {
			Map<String, String> typeMap = lms8100Service.getItemTypeMap(
					Util.nullToSpace(l300m01a.getPaVer()));
			Map<String, BigDecimal> scoreMap = lms8100Service.getItemScoreMap(
					Util.nullToSpace(l300m01a.getPaVer()));
			for (String itemKey : typeMap.keySet()) {
				String type = typeMap.get(itemKey);
				L300S01A l300s01a = new L300S01A();
				l300s01a.setMainId(l300m01aMainid);
				this.newL300s01a(l300s01a, itemKey);
				l300s01a.setItemType(Util.nullToSpace(type));
				BigDecimal score = scoreMap.get(itemKey);
				l300s01a.setItemScore(score);
				l300s01as.add(l300s01a);
			}

			// 獨立項目：加分、減分
			L300S01A l300s01aP = new L300S01A();
			l300s01aP.setMainId(l300m01aMainid);
			this.newL300s01a(l300s01aP, "plusItem");
			l300s01as.add(l300s01aP);

			L300S01A l300s01aM = new L300S01A();
			l300s01aM.setMainId(l300m01aMainid);
			this.newL300s01a(l300s01aM, "minusItem");
			l300s01as.add(l300s01aM);
		}
		if (l300s01as.size() > 0) {
			lms8100Service.saveL300s01aList(l300s01as);
		}
	}

	public void findDetail(L300M01A l300m01a, String l300m01aMainid,
			String ownBrId, String branchId, String bgnDate, String endDate,
			List<Map<String, String>> xlsDataList) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		List<L300S01A> l300s01aList = new ArrayList<L300S01A>();
		if (Util.isNotEmpty(Util.nullToSpace(l300m01aMainid))) {
			String paVer = Util.nullToSpace(l300m01a.getPaVer());
			String propVerStr = (Util.isNotEmpty(paVer) ? ("_" + paVer) : "");

			int lrsNum = 0; // 覆審件數-企金
			int crsNum = 0; // 覆審件數-消金
			int rsNum = 0; // 覆審總件數

			Set<String> itemKeys = new HashSet<String>();
			Map<String, Map<String, String>> itemMap = new LinkedHashMap<String, Map<String, String>>();

			// MainIdListForL300S01A、ListForL300S01A兩個SQL base WHERE條件要一樣
			// 企金
			List<Map<String, Object>> lrsMainIdList = eloanDbBaseService
					.getLrsMainIdListForL300S01A(ownBrId, branchId, bgnDate,
							endDate);
			if (lrsMainIdList != null && !lrsMainIdList.isEmpty()) {
				lrsNum = lrsMainIdList.size();
			}

			List<Map<String, Object>> lrsList = eloanDbBaseService
					.getLrsListForL300S01A(ownBrId, branchId, bgnDate, endDate);
			if (lrsList != null && !lrsList.isEmpty()) {
				this.setDetail("lrs", itemKeys, itemMap, lrsList, xlsDataList, propVerStr);
			}

			// 消金
			List<Map<String, Object>> crsMainIdList = eloanDbBaseService
					.getCrsMainIdListForL300S01A(ownBrId, branchId, bgnDate,
							endDate);
			if (crsMainIdList != null && !crsMainIdList.isEmpty()) {
				crsNum = crsMainIdList.size();
			}
			List<Map<String, Object>> crsList = eloanDbBaseService
					.getCrsListForL300S01A(ownBrId, branchId, bgnDate, endDate);
			if (crsList != null && !crsList.isEmpty()) {
				this.setDetail("crs", itemKeys, itemMap, crsList, xlsDataList, propVerStr);
			}

			rsNum = lrsNum + crsNum;
			l300m01a.setLrsNum(lrsNum);
			l300m01a.setCrsNum(crsNum);
			l300m01a.setRsNum(rsNum);

			BigDecimal subTotal = BigDecimal.ZERO;
			Map<String, BigDecimal> scoreMap = lms8100Service.getItemScoreMap(
					Util.nullToSpace(l300m01a.getPaVer()));
			for (String itemKey : itemMap.keySet()) {
				Map<String, String> iMap = itemMap.get(itemKey);
				if (iMap != null && !iMap.isEmpty()) {
					String itemName = Util.nullToSpace(MapUtils.getString(iMap,
							"itemName"));
					String itemType = Util.nullToSpace(MapUtils.getString(iMap,
							"itemType"));
					L300S01A l300s01a = lms8100Service.findL300s01a(
							l300m01aMainid, itemType, itemName);
					if (l300s01a == null) {
						l300s01a = new L300S01A();
						l300s01a.setMainId(l300m01aMainid);
						this.newL300s01a(l300s01a, itemName);
						l300s01a.setItemType(itemType);
					}

					String cnt = Util.nullToSpace(MapUtils.getString(iMap,
							"itemCnt"));
					BigDecimal bCnt = Util.parseBigDecimal(cnt);
					BigDecimal score = scoreMap.get(itemKey);
					l300s01a.setItemCnt(bCnt);
					l300s01a.setItemScore(score);
					l300s01a.setItemAll(bCnt.multiply(score));
					subTotal = subTotal.add(bCnt.multiply(score));

					// 2022/01/17 連喬凱確認：因有xls檔 故不串備註欄位
					// l300s01a.setItemDscr(Util.nullToSpace(MapUtils.getString(iMap,
					// "itemDscr")));
					l300s01a.setItemDscr("");
					l300s01a.setCreator(user.getUserId());
					l300s01a.setCreateTime(CapDate.getCurrentTimestamp());
					l300s01aList.add(l300s01a);
				}
			}

			l300m01a.setSubTotal(subTotal);
			l300m01a.setAvgScore(BigDecimal.valueOf(rsNum).compareTo(
					BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : subTotal.divide(
					BigDecimal.valueOf(rsNum), 3, BigDecimal.ROUND_HALF_UP));
		}
		if (l300s01aList.size() > 0) {
			lms8100Service.saveL300s01aList(l300s01aList);
		}
	}

	public void setDetail(String sys, Set<String> itemKeys,
			Map<String, Map<String, String>> itemMap,
			List<Map<String, Object>> dList,
			List<Map<String, String>> xlsDataList, String propVerStr) {
		for (Map<String, Object> dMap : dList) {
			if (dMap != null && !dMap.isEmpty()) {
				if (Util.equals(
						Util.nullToSpace(MapUtils.getString(dMap, "NEEDPA")),
						"Y")) {
					String itemName = Util.nullToSpace(MapUtils.getString(dMap,
							"ITEMNAME"));
					String itemType = Util.nullToSpace(MapUtils.getString(dMap,
							"ITEMTYPE"));
					int countVal = 0;
					String itemValue = "";
					if (Util.equals(itemType, "YN")) {
						itemValue = Util.nullToSpace(MapUtils.getString(dMap,
								"ITEMYN"));
						if (Util.equals(itemValue, "Y")) {
							countVal = 1;
							// 塞明細檔 xlsDataList
							this.setXlsData(sys, xlsDataList, dMap, propVerStr);
						}
					} else if (Util.equals(itemType, "CNT")) {
						itemValue = Util.nullToSpace(MapUtils.getString(dMap,
								"ITEMCNT"));
						countVal = NumberUtils.toInt(itemValue);
						if (countVal > 0) {
							// 塞明細檔 xlsDataList
							this.setXlsData(sys, xlsDataList, dMap, propVerStr);
						}
					} else {

					}
					// 2022/01/17 連喬凱確認：因有xls檔 故不串備註欄位
					// String itemDscr =
					// Util.nullToSpace(MapUtils.getString(dMap, "DSCR"));

					Map<String, String> dataMap = new LinkedHashMap<String, String>();
					if (itemKeys.contains(itemName)) {
						dataMap = itemMap.get(itemName);

						String oldItemCnt = dataMap.get("itemCnt");
						int tempCnt = NumberUtils.toInt(oldItemCnt);
						tempCnt += countVal;
						dataMap.put("itemCnt", Integer.toString(tempCnt));

						/*
						 * 2022/01/17 連喬凱確認：因有xls檔 故不串備註欄位 if
						 * (Util.isNotEmpty(itemDscr)) { StringBuffer
						 * tempItemDscr = new
						 * StringBuffer(dataMap.get("itemDscr"));
						 * tempItemDscr.append(tempItemDscr.length() > 0 ? "；" :
						 * "") .append(itemDscr); String tempStr =
						 * tempItemDscr.toString(); if (tempItemDscr.length() >
						 * 3000) { tempStr = tempStr.substring(0, 3000); }
						 * dataMap.put("itemDscr", tempStr); }
						 */

						itemMap.put(itemName, dataMap);
					} else {
						dataMap.put("itemName", itemName);
						dataMap.put("itemType", itemType);
						dataMap.put("itemCnt", Integer.toString(countVal));
						// 2022/01/17 連喬凱確認：因有xls檔 故不串備註欄位
						// dataMap.put("itemDscr", itemDscr);
						itemMap.put(itemName, dataMap);
						itemKeys.add(itemName);
					}
				}
			}
		}
	}

	public void setXlsData(String sys, List<Map<String, String>> xlsDataList,
			Map<String, Object> dMap, String propVerStr) {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS8100M01Page.class);

		String sysName = "";
		if (Util.equals(sys, "lrs")) {
			sysName = "企金";
		} else if (Util.equals(sys, "crs")) {
			sysName = "消金";
		} else {
		}
		String custId = Util.nullToSpace(MapUtils.getString(dMap, "CUSTID"));
		String dupNo = Util.nullToSpace(MapUtils.getString(dMap, "DUPNO"));
		String custName = Util
				.nullToSpace(MapUtils.getString(dMap, "CUSTNAME"));
		String ownBrId = Util.nullToSpace(MapUtils.getString(dMap, "OWNBRID"));
		String branchName = Util.nullToSpace(branchService
				.getBranchName(ownBrId));
		String projectNo = Util.nullToSpace(MapUtils.getString(dMap,
				"PROJECTNO"));
		String itemName = Util
				.nullToSpace(MapUtils.getString(dMap, "ITEMNAME"));
		String itemNameStr = (Util.isEmpty(itemName) ? "" : prop
				.getProperty("L300S01A." + itemName + propVerStr));
		if (itemNameStr == null) {
			itemNameStr = itemName;
		}
		String itemType = Util
				.nullToSpace(MapUtils.getString(dMap, "ITEMTYPE"));
		String itemYN = Util.nullToSpace(MapUtils.getString(dMap, "ITEMYN"));
		String itemCnt = Util.nullToSpace(MapUtils.getString(dMap, "ITEMCNT"));
		String itemVal = "";
		if (Util.equals(itemType, "YN")) {
			itemVal = itemYN;
		} else if (Util.equals(itemType, "CNT")) {
			itemVal = itemCnt;
		} else {
		}
		String itemDscr = Util.nullToSpace(MapUtils.getString(dMap, "DSCR"));

		Map<String, String> dataMap = new LinkedHashMap<String, String>();
		dataMap.put("sys", sysName);
		dataMap.put("cust", Util.addSpaceWithValue(custId, 10) + dupNo);
		dataMap.put("custName", custName);
		dataMap.put("ownBrId", ownBrId + "　" + branchName);
		dataMap.put("projectNo", projectNo);
		dataMap.put("itemName", itemNameStr);
		dataMap.put("itemVal", itemVal);
		dataMap.put("itemDscr", itemDscr);
		xlsDataList.add(dataMap);
	}

	public void genDetailXls(L300M01A l300m01a,
			List<Map<String, String>> xlsDataList) throws WriteException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		ByteArrayOutputStream baos = new ByteArrayOutputStream();

		try {
			WritableWorkbook book = Workbook.createWorkbook(baos);
			WritableSheet sheet1 = book.createSheet("明細", 0);
			SheetSettings settings1 = sheet1.getSettings();
			settings1.setPaperSize(PaperSize.A4);
			settings1.setFitWidth(1); // 設定預覽列印與列印成為一頁, 寬度

			// 字型設定
			WritableFont titleFont = null;
			WritableCellFormat titleFontCenter = null;
			WritableFont font12 = null;
			WritableCellFormat format12Right = null;
			WritableCellFormat format12Center = null;
			WritableCellFormat format12Left = null;
			titleFont = new WritableFont(WritableFont.createFont("標楷體"), 12,
					WritableFont.BOLD);
			titleFontCenter = LMSUtil.setCellFormat(titleFontCenter, titleFont,
					Alignment.CENTRE);
			font12 = new WritableFont(WritableFont.createFont("標楷體"), 12,
					WritableFont.NO_BOLD);
			format12Right = LMSUtil.setCellFormat(format12Right, font12,
					Alignment.RIGHT);
			format12Center = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE);
			format12Left = LMSUtil.setCellFormat(format12Left, font12,
					Alignment.LEFT);

			// 標頭
			int x = 0;
			sheet1.mergeCells(0, x, 6, x);
			sheet1.addCell(new Label(0, x, l300m01a.getOwnBrId() + "覆審考核表–"
					+ l300m01a.getBranchId(), titleFontCenter));
			x++;
			sheet1.mergeCells(0, x, 4, x);
			sheet1.addCell(new Label(0, x, "資料日期："
					+ CapDate.formatDate(l300m01a.getBgnDate(), "yyyy-MM-dd")
					+ "～"
					+ CapDate.formatDate(l300m01a.getEndDate(), "yyyy-MM-dd"),
					format12Left));
			sheet1.mergeCells(5, x, 6, x);
			sheet1.addCell(new Label(5, x, "產生日期："
					+ CapDate.getCurrentDate("yyyy-MM-dd"), format12Right));

			x++;
			// 設定欄寬
			sheet1.setColumnView(0, 5);
			sheet1.addCell(new Label(0, x, "企/消金", format12Center));
			sheet1.setColumnView(1, 14);
			sheet1.addCell(new Label(1, x, "統編", format12Left));
			sheet1.setColumnView(2, 20);
			sheet1.addCell(new Label(2, x, "客戶", format12Left));
			sheet1.setColumnView(3, 20);
			sheet1.addCell(new Label(3, x, "案號", format12Left));
			sheet1.setColumnView(4, 30);
			sheet1.addCell(new Label(4, x, "項目", format12Left));
			sheet1.setColumnView(5, 5);
			sheet1.addCell(new Label(5, x, "註記", format12Center));
			sheet1.setColumnView(6, 30);
			sheet1.addCell(new Label(6, x, "備註", format12Left));

			for (Map<String, String> map : xlsDataList) {
				x++;
				int y = 0;
				// for (String fieldName : map.keySet()) {
				// String tmpVal = Util.trim(map.get(fieldName));
				// sheet1.addCell(new Label(x, y, tmpVal, format12Left));
				// y++;
				// }

				sheet1.addCell(new Label(y, x,
						Util.nullToSpace(map.get("sys")), format12Center));
				y++;
				sheet1.addCell(new Label(y, x,
						Util.nullToSpace(map.get("cust")), format12Left));
				y++;
				sheet1.addCell(new Label(y, x, Util.nullToSpace(map
						.get("custName")), format12Left));
				y++;
				sheet1.addCell(new Label(y, x, Util.nullToSpace(map
						.get("projectNo")), format12Left));
				y++;
				sheet1.addCell(new Label(y, x, Util.nullToSpace(map
						.get("itemName")), format12Left));
				y++;
				sheet1.addCell(new Label(y, x, Util.nullToSpace(map
						.get("itemVal")), format12Center));
				y++;
				sheet1.addCell(new Label(y, x, Util.nullToSpace(map
						.get("itemDscr")), format12Left));
			}

			book.write();
			book.close();

			String fieldId = "paFormChkList";
			String fileName = fieldId
					+ CapDate.formatDate(l300m01a.getBgnDate(), "yyyyMM") + "_"
					+ Util.nullToSpace(l300m01a.getOwnBrId()) + "_"
					+ Util.nullToSpace(l300m01a.getBranchId()) + "_"
					+ Util.nullToSpace(TWNDate.toAD(l300m01a.getAssDate()));

			// 重新引進的話先刪除原有檔案
			List<DocFile> docFiles = docFileService.findByIDAndName(
					l300m01a.getMainId(), fieldId, "");
			if (docFiles != null && !docFiles.isEmpty()) {
				for (DocFile file : docFiles) {
					docFileService.clean(file.getOid());
				}
			}

			DocFile file = new DocFile();
			file.setMainId(l300m01a.getMainId());
			file.setData(baos != null ? baos.toByteArray() : null);
			file.setFileDesc("覆審考核表明細檔" + user.getUserId());
			file.setCrYear(CapDate.getCurrentDate("yyyy"));
			file.setFieldId(fieldId);
			file.setSrcFileName(fileName + ".xls");
			file.setUploadTime(CapDate.getCurrentTimestamp());
			file.setBranchId(l300m01a.getOwnBrId());
			file.setContentType("application/msexcel");
			file.setSysId("FMS");
			docFileService.save(file);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	public void newL300s01a(L300S01A l300s01a, String itemName) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		l300s01a.setItemName(itemName);
		l300s01a.setItemType(null);
		l300s01a.setItemCnt(null);
		l300s01a.setItemScore(null);
		l300s01a.setItemAll(BigDecimal.ZERO);
		l300s01a.setItemDscr("");
		l300s01a.setCreator(user.getUserId());
		l300s01a.setCreateTime(CapDate.getCurrentTimestamp());
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL300m01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			if (lms8100Service.deleteL300m01as(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL300m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));

		formatResultDefault(result);

		if (!Util.isEmpty(oid)) {
			L300M01A l300m01a = lms8100Service.findModelByOid(L300M01A.class,
					oid);
			if (l300m01a == null) {
				// 開啟新案帶入起案的分行和目前文件狀態
				result.set("ownBrId", user.getUnitNo());
				result.set(
						"ownBrName",
						StrUtils.concat(" ",
								branchService.getBranchName(user.getUnitNo())));
				result.set(
						"docStatus",
						this.getMessage("docStatus."
								+ CreditDocStatusEnum.海外_編製中.getCode()));
				result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode());
			} else {
				result.set(EloanConstants.OID,
						CapString.trimNull(l300m01a.getOid()));
				result.set(EloanConstants.MAIN_OID,
						CapString.trimNull(l300m01a.getOid()));
				String branchName = (l300m01a == null ? "" : branchService
						.getBranchName(l300m01a.getBranchId()));
				result.set("titleInfo", l300m01a.getBranchId() + " "
						+ branchName);
				result.set("produceFlag",
						Util.nullToSpace(l300m01a.getProduceFlag()));

				formatResultShow(result, l300m01a);
			}
						
			// J-112-0461 授信覆審考核表-編製中-下拉選項統計由每半年改為每季統計。
			String produceInterval = lms8100Service.findProduceInterval(CapDate
					.formatDate(l300m01a.getCreateTime(),
							UtilConstants.DateFormat.YYYY_MM_DD));
			result.set("produceInterval", produceInterval);
			
		}

		return result;
	}

	public void formatResultDefault(CapAjaxFormResult result) {
		// 先放上初始的扣分標準
		// 先用執行當天執行時間取得最新版本
		Map<String, BigDecimal> scoreMap = lms8100Service.getItemScoreMap(null);
				//Util.nullToSpace(l300m01a.getPaVer()));
		for (String itemName : scoreMap.keySet()) {
			BigDecimal itemScore = scoreMap.get(itemName);
			result.set(itemName + "_itemScore", itemScore);

			result.set(itemName + "_itemType", "");
			result.set(itemName + "_itemCnt", "0");
			result.set(itemName + "_itemAll", "0");
			result.set(itemName + "_itemDscr", "");
		}

		result.set("plusItem_itemAll", "");
		result.set("plusItem_itemDscr", "");
		result.set("minusItem_itemAll", "");
		result.set("minusItem_itemDscr", "");
	}

	public void formatResultShow(CapAjaxFormResult result, L300M01A l300m01a)
			throws CapException {
		DecimalFormat df = new DecimalFormat("#####0.###");
		result.set("assDate", "");
		result.set("subTotal", "");
		result.set("rsNum", "");
		result.set("avgScore", "");
		result.set("realScore", "");
		result.set("totalScore", "");
		if (l300m01a != null) {
			// result = DataParse.toResult(l300m01a);
			result.set(EloanConstants.OID,
					CapString.trimNull(l300m01a.getOid()));
			result.set(EloanConstants.MAIN_OID,
					CapString.trimNull(l300m01a.getOid()));
			result.set(EloanConstants.MAIN_ID,
					CapString.trimNull(l300m01a.getMainId()));
			result.set("creator", Util.nullToSpace(userInfoService
					.getUserName(l300m01a.getCreator())));
			result.set("updater", Util.nullToSpace(userInfoService
					.getUserName(l300m01a.getUpdater())));
			result.set("createTime",
					Util.nullToSpace(TWNDate.valueOf(l300m01a.getCreateTime())));
			result.set("updateTime",
					Util.nullToSpace(TWNDate.valueOf(l300m01a.getUpdateTime())));
			result.set("docStatus",
					getMessage("docStatus." + l300m01a.getDocStatus()));

			result.set("assDate",
					CapDate.formatDate(l300m01a.getAssDate(), "yyyy-MM-dd"));
			result.set("subTotal",
					df.format(Util.parseBigDecimal(l300m01a.getSubTotal())));
			result.set("rsNum", Util.nullToSpace(l300m01a.getRsNum()));
			result.set("avgScore", df.format(Util.parseBigDecimal(
					l300m01a.getAvgScore()).setScale(3,
					BigDecimal.ROUND_HALF_UP)));
			result.set("realScore", df.format(Util.parseBigDecimal(
					l300m01a.getRealScore()).setScale(3,
					BigDecimal.ROUND_HALF_UP)));
			result.set("totalScore", df.format(Util.parseBigDecimal(
					l300m01a.getTotalScore()).setScale(3,
					BigDecimal.ROUND_HALF_UP)));

			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS8100M01Page.class);
			result.set("paForm", lms8100Service.buildPaFormHtml(l300m01a, prop));

			List<L300S01A> l300s01aList = lms8100Service
					.findL300s01aList(l300m01a.getMainId());
			if (l300s01aList != null && !l300s01aList.isEmpty()) {
				for (L300S01A l300s01a : l300s01aList) {
					String itemName = Util.nullToSpace(l300s01a.getItemName());

					result.set(itemName + "_itemType",
							Util.nullToSpace(l300s01a.getItemType()));
					result.set(itemName + "_itemCnt", df.format(l300s01a
							.getItemCnt() == null ? BigDecimal.ZERO : Util
							.parseBigDecimal(l300s01a.getItemCnt())));
					result.set(itemName + "_itemScore", df.format(l300s01a
							.getItemScore() == null ? BigDecimal.ZERO : Util
							.parseBigDecimal(l300s01a.getItemScore())));
					result.set(itemName + "_itemAll", df.format(l300s01a
							.getItemAll() == null ? BigDecimal.ZERO : Util
							.parseBigDecimal(l300s01a.getItemAll())));
					result.set(itemName + "_itemDscr", l300s01a.getItemDscr());
				}
			}

			// 附件
			HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
			JSONArray jsonAraay = new JSONArray();
			List<DocFile> docFiles = docFileService.findByIDAndName(
					l300m01a.getMainId(), "paFormChkList", "");
			for (DocFile docFile : docFiles) {
				JSONObject o = new JSONObject();
				o.putAll(DataParse.toJSON(docFile, true));
				o.put("uploadTime", TWNDate.toFullTW(docFile.getUploadTime()));
				jsonAraay.add(o);
			}
			map.put("divXlsFile", jsonAraay);
			result.set("attchResult", new CapAjaxFormResult(map));
		}
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult reImplL300S01A(PageParameters params)
			throws CapException, WriteException {
		String oid = Util.trim(params.getString(EloanConstants.OID));

		CapAjaxFormResult result = new CapAjaxFormResult();

		L300M01A l300m01a = lms8100Service.findModelByOid(L300M01A.class, oid);
		if (l300m01a != null) {
			List<Map<String, String>> xlsDataList = new ArrayList<Map<String, String>>();
			String l300m01aMainid = l300m01a.getMainId();

			// 初始化明細資料
			this.initDetail(l300m01aMainid, l300m01a);

			// 明細資料
			this.findDetail(l300m01a, l300m01aMainid, l300m01a.getOwnBrId(),
					l300m01a.getBranchId(),
					TWNDate.toAD(l300m01a.getBgnDate()),
					TWNDate.toAD(l300m01a.getEndDate()), xlsDataList);

			// 產生明細xls
			this.genDetailXls(l300m01a, xlsDataList);

			lms8100Service.save(l300m01a);
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		result.add(this.queryL300m01a(params));
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL300M01A(PageParameters params)
			throws CapException {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS8100M01Page.class);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String form = Util.trim(params.getString("mainPanel"));
		Boolean showMsg = params.getAsBoolean("showMsg", false);

		L300M01A l300m01a = null;
		if (Util.isNotEmpty(oid)) {
			l300m01a = lms8100Service.findModelByOid(L300M01A.class, oid);
		} else {
			l300m01a = new L300M01A();
			String paVer = retrialService.getPaFormVerByDate(
					Util.parseDate(CapDate.getCurrentDate("yyyy-MM-dd")));
			l300m01a.setPaVer(paVer);
		}
		JSONObject jsonData = JSONObject.fromObject(form);
		DataParse.toBean(jsonData, l300m01a);
		lms8100Service.save(l300m01a);
		// 檢查欄位
		String validate = null;
		validate = Util.validateColumnSize(l300m01a, prop, "L300M01A");
		if (validate != null) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", validate);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}

		List<L300S01A> l300s01aList = new ArrayList<L300S01A>();
		Map<String, String> typeMap = lms8100Service.getItemTypeMap(
				Util.nullToSpace(l300m01a.getPaVer()));
		Map<String, BigDecimal> scoreMap = lms8100Service.getItemScoreMap(
				Util.nullToSpace(l300m01a.getPaVer()));
		for (String itemKey : typeMap.keySet()) {
			String itemName = itemKey;
			String itemType = typeMap.get(itemKey);
			String itemCnt = Util.nullToSpace(jsonData
					.get(itemKey + "_itemCnt"));
			itemCnt = (Util.isEmpty(itemCnt) ? "0" : itemCnt);
			String itemScore = Util.nullToSpace(jsonData.get(itemKey
					+ "_itemScore"));
			String itemAll = Util.nullToSpace(jsonData
					.get(itemKey + "_itemAll"));
			itemAll = (Util.isEmpty(itemAll) ? "0" : itemAll);
			String itemDscr = Util.nullToSpace(jsonData.get(itemKey
					+ "_itemDscr"));

			L300S01A l300s01a = lms8100Service.findL300s01a(
					l300m01a.getMainId(), itemType, itemName);
			if (l300s01a == null) {
				l300s01a = new L300S01A();
				l300s01a.setMainId(l300m01a.getMainId());
				this.newL300s01a(l300s01a, itemName);
				l300s01a.setItemType(itemType);
			}
			l300s01a.setItemCnt(Util.parseBigDecimal(itemCnt));
			l300s01a.setItemScore(Util.parseBigDecimal((Util.isEmpty(itemScore) ? scoreMap
					.get(itemKey) : itemScore)));
			l300s01a.setItemAll(Util.parseBigDecimal(itemAll));
			l300s01a.setItemDscr(itemDscr);
			l300s01aList.add(l300s01a);
		}

		// 獨立項目：加分、減分
		L300S01A l300s01aP = lms8100Service.findL300s01a(l300m01a.getMainId(),
				null, "plusItem");
		if (l300s01aP == null) {
			l300s01aP = new L300S01A();
			l300s01aP.setMainId(l300m01a.getMainId());
			this.newL300s01a(l300s01aP, "plusItem");
		}
		l300s01aP.setItemAll(Util.parseBigDecimal(jsonData
				.get("plusItem_itemAll")));
		l300s01aP.setItemDscr(jsonData.get("plusItem_itemDscr").toString());
		l300s01aList.add(l300s01aP);

		L300S01A l300s01aM = lms8100Service.findL300s01a(l300m01a.getMainId(),
				null, "minusItem");
		if (l300s01aM == null) {
			l300s01aM = new L300S01A();
			l300s01aM.setMainId(l300m01a.getMainId());
			this.newL300s01a(l300s01aM, "minusItem");
		}
		l300s01aM.setItemAll(Util.parseBigDecimal(jsonData
				.get("minusItem_itemAll")));
		l300s01aM.setItemDscr(jsonData.get("minusItem_itemDscr").toString());
		l300s01aList.add(l300s01aM);

		if (l300s01aList.size() > 0) {
			lms8100Service.saveL300s01aList(l300s01aList);
		}

		if (showMsg) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getBossList(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;
	}

	/**
	 * 呈主管覆核
	 **/
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String oid = params.getString(EloanConstants.MAIN_OID);

		if (Util.isNotEmpty(oid)) {
			L300M01A l300m01a = lms8100Service.findModelByOid(L300M01A.class,
					oid);

			String staffL3Item = Util.trim(params.getString("staffL3Item"));
			String staffL5Item = Util.trim(params.getString("staffL5Item"));
			String staffL6Item = Util.trim(params.getString("staffL6Item"));
			if (Util.isNotEmpty(staffL3Item)) { // 呈主管
				List<L300M01B> models = (List<L300M01B>) lms8100Service
						.findListByMainId(L300M01B.class, l300m01a.getMainId());
				if (!models.isEmpty()) {
					lms8100Service.deleteL300m01bs(models);
				}

				List<L300M01B> l300m01bs = new ArrayList<L300M01B>();

				L300M01B l300m01bL1 = new L300M01B();
				l300m01bL1.setCreator(user.getUserId());
				l300m01bL1.setCreateTime(CapDate.getCurrentTimestamp());
				l300m01bL1.setMainId(l300m01a.getMainId());
				l300m01bL1.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
				l300m01bL1.setStaffNo(user.getUserId());
				l300m01bL1.setBranchType(user.getUnitType());
				l300m01bL1.setBranchId(user.getUnitNo());
				l300m01bs.add(l300m01bL1);

				L300M01B l300m01bL3 = new L300M01B();
				l300m01bL3.setCreator(user.getUserId());
				l300m01bL3.setCreateTime(CapDate.getCurrentTimestamp());
				l300m01bL3.setMainId(l300m01a.getMainId());
				l300m01bL3.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				l300m01bL3.setStaffNo(staffL3Item);
				l300m01bL3.setBranchType(user.getUnitType());
				l300m01bL3.setBranchId(user.getUnitNo());
				l300m01bs.add(l300m01bL3);

				L300M01B l300m01bL5 = new L300M01B();
				l300m01bL5.setCreator(user.getUserId());
				l300m01bL5.setCreateTime(CapDate.getCurrentTimestamp());
				l300m01bL5.setMainId(l300m01a.getMainId());
				l300m01bL5.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
				l300m01bL5.setStaffNo(staffL5Item);
				l300m01bL5.setBranchType(user.getUnitType());
				l300m01bL5.setBranchId(user.getUnitNo());
				l300m01bs.add(l300m01bL5);

				L300M01B l300m01bL6 = new L300M01B();
				l300m01bL6.setCreator(user.getUserId());
				l300m01bL6.setCreateTime(CapDate.getCurrentTimestamp());
				l300m01bL6.setMainId(l300m01a.getMainId());
				l300m01bL6.setStaffJob(UtilConstants.STAFFJOB.分行單位主管L6);
				l300m01bL6.setStaffNo(staffL6Item);
				l300m01bL6.setBranchType(user.getUnitType());
				l300m01bL6.setBranchId(user.getUnitNo());
				l300m01bs.add(l300m01bL6);

				lms8100Service.saveL300m01bList(l300m01bs);
			}
			Boolean upMis = false;
			L300M01B l300m01bL4 = new L300M01B();
			// 如果有這個key值表示是輸入chekDate核准日期
			if (params.containsKey("checkDate")) {
				l300m01a.setApprover(user.getUserId());
				l300m01a.setApproveTime(CapDate.getCurrentTimestamp());
				upMis = true;
				L300M01B l300m01b = lms8100Service.findL300m01b(
						l300m01a.getMainId(), user.getUnitType(),
						user.getUnitNo(), user.getUserId(),
						UtilConstants.STAFFJOB.執行覆核主管L4);
				if (l300m01b == null) {
					l300m01b = new L300M01B();
					l300m01b.setCreator(user.getUserId());
					l300m01b.setCreateTime(CapDate.getCurrentTimestamp());
					l300m01b.setMainId(l300m01a.getMainId());
					l300m01b.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
					l300m01b.setStaffNo(user.getUserId());
					l300m01b.setBranchType(user.getUnitType());
					l300m01b.setBranchId(user.getUnitNo());
				}
				l300m01bL4 = l300m01b;
			}

			if (!Util.isEmpty(l300m01a)) {
				try {
					// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
					if (params.containsKey("flowAction")) {
						// 退回不檢查
						if (params.getBoolean("flowAction")) {
							L300M01B l300m01b = lms8100Service.findL300m01b(
									l300m01a.getMainId(), user.getUnitType(),
									user.getUnitNo(), user.getUserId(),
									UtilConstants.STAFFJOB.經辦L1);

							if (l300m01b != null) {
								// 2022/07/18 授審處連喬凱說不檢核同一人
								String sameGuyPass = Util
										.trim(lmsService
												.getSysParamDataValue("LMS_PAFORM_SAMEGUY_PASS"));
								if (Util.notEquals(sameGuyPass, "Y")) {
									// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
									throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
								}
							} else {
								lms8100Service.save(l300m01bL4);
								upMis = true;
							}
						}
					}
					lms8100Service.flowAction(l300m01a.getOid(), l300m01a,
							params.containsKey("flowAction"),
							params.getAsBoolean("flowAction", false), upMis);
				} catch (FlowException t1) {
					logger.error(
							"[flowAction] lms8100Service.flowAction FlowException!!",
							t1);
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
				} catch (Throwable t1) {
					logger.error(
							"[flowAction]  lms8100Service.flowAction EXCEPTION!!",
							t1);
					throw new CapMessageException(t1.getMessage(), getClass());
				}
			}
		}

		return new CapAjaxFormResult();
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult genRbPdf(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String bgnDate = Util.trim(params.getString("bgnDate"));
		String endDate = Util.trim(params.getString("endDate"));

		L300M01C l300m01c = lms8100Service.findL300m01c(user.getUnitNo(),
				bgnDate, endDate);
		if (l300m01c != null) {
			throw new CapMessageException("排名表已存在！", getClass());
		}
		List<L300M01A> l300m01aList = lms8100Service.findL300m01aProduceList(
				user.getUnitNo(), CreditDocStatusEnum.海外_已核准.getCode(),
				bgnDate, endDate, "Y");
		if (l300m01aList != null && l300m01aList.size() > 0) {
			throw new CapMessageException("考評表已產生排名表！", getClass());
		}
		if (this.calcRealScore(user.getUnitNo(), bgnDate, endDate)) {
			result.set("msg", "done");
		} else {
			// EFD0036=INFO|查無資料!|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		}

		/*
		 * result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		 * .getMainMessage(this.getComponent(),
		 * UtilConstants.AJAX_RSP_MSG.執行成功));
		 */

		return result;
	}

	public boolean calcRealScore(String ownBrId, String bgnDate, String endDate) {
		BigDecimal minVal = BigDecimal.ZERO;
		BigDecimal maxVal = BigDecimal.ZERO;
		BigDecimal mVal = BigDecimal.ZERO;
		boolean passCalc = false;
		List<L300M01A> l300m01as = new ArrayList<L300M01A>();

		List<L300M01A> l300m01aList = lms8100Service.findL300m01aProduceList(
				ownBrId, CreditDocStatusEnum.海外_已核准.getCode(), bgnDate,
				endDate, "N");
		if (l300m01aList != null && l300m01aList.size() > 0) {
			int i = 0;
			for (L300M01A l300m01a : l300m01aList) {
				i++;
				BigDecimal tmpScore = (l300m01a.getAvgScore() == null ? BigDecimal.ZERO
						: l300m01a.getAvgScore());
				if (i == 1) { // 第一筆的時候 最大最小為相同
					minVal = tmpScore;
					maxVal = tmpScore;
				} else {
					// =1 比min更小 tmpScore < minVal
					if (minVal.compareTo(tmpScore) > 0) {
						minVal = tmpScore;
					}
					// =-1 比max更大 tmpScore > maxVal
					if (maxVal.compareTo(tmpScore) < 0) {
						maxVal = tmpScore;
					}
				}
			}

			/*
			 * 最低者為X、最高者為Y （Y-X）/（90-60）＝│A│（絕對值） 60＋﹝（該分行平均扣分值-X）/│A│﹞＝該分行實際得分
			 */
			BigDecimal sVal = maxVal.subtract(minVal);
			// 2022/07/19 授審處連喬凱說到小數點第4位 4捨5入
			mVal = (sVal.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO
					: sVal.divide(new BigDecimal(30), 4,
							BigDecimal.ROUND_HALF_UP));

			if (mVal.compareTo(BigDecimal.ZERO) == 0) {
				passCalc = true; // │A│ 為0 則除法皆為0 大家都是60
			}
			mVal = mVal.abs(); // 取 絕對值

			BigDecimal baseVal = new BigDecimal(60);
			for (L300M01A m01a : l300m01aList) {
				BigDecimal real = BigDecimal.ZERO;
				if (passCalc) {
					m01a.setRealScore(new BigDecimal(60));
				} else {
					BigDecimal avgScore = (m01a.getAvgScore() == null ? BigDecimal.ZERO
							: m01a.getAvgScore());
					real = baseVal.add((avgScore.subtract(minVal)).divide(mVal,
							4, BigDecimal.ROUND_HALF_UP));
					m01a.setRealScore(real);
				}

				// 總分
				BigDecimal plus = BigDecimal.ZERO;
				BigDecimal minus = BigDecimal.ZERO;
				L300S01A l300s01aP = lms8100Service.findL300s01a(
						m01a.getMainId(), null, "plusItem");
				if (l300s01aP != null) {
					plus = (l300s01aP.getItemAll() == null ? BigDecimal.ZERO
							: l300s01aP.getItemAll());
				}
				L300S01A l300s01aM = lms8100Service.findL300s01a(
						m01a.getMainId(), null, "minusItem");
				if (l300s01aM != null) {
					minus = (l300s01aM.getItemAll() == null ? BigDecimal.ZERO
							: l300s01aM.getItemAll());
				}
				m01a.setTotalScore(real.add(plus).add(minus));
				m01a.setProduceFlag("Y");
				l300m01as.add(m01a);
			}
		} else {
			return false;
		}
		if (l300m01as != null && !l300m01as.isEmpty()) {
			lms8100Service.saveL300m01aList(l300m01as, false);
		}
		return true;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult returnToCompiling(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = Util.trim(params.getString(EloanConstants.OID));
		if (Util.isNotEmpty(oid)) {
			L300M01A l300m01a = lms8100Service.findModelByOid(L300M01A.class,
					oid);
			if (l300m01a != null) {
				// 先檢查是否產生排名表
				if (Util.equals(l300m01a.getProduceFlag(), "Y")) {
					throw new CapMessageException("考評表已產生排名表，不可退回！", getClass());
				}
				/*
				 * A考評表 已產生排名表，後產生B考評表，B考評表可退回，所以不用 L300M01C 存不存在當檢核 L300M01C
				 * l300m01c = lms8100Service.findL300m01c(l300m01a.getOwnBrId(),
				 * CapDate.formatDate(l300m01a.getBgnDate(), "yyyy-MM-dd"),
				 * CapDate.formatDate(l300m01a.getEndDate(), "yyyy-MM-dd"));
				 * if(l300m01c != null){ throw new
				 * CapMessageException("考評表已產生排名表，不可退回！", getClass()); }
				 */

				flowSimplifyService.flowStart("LMS8100Flow", l300m01a.getOid(),
						user.getUserId(), user.getUnitNo());

				// 更改文件異動紀錄的類別 建檔=>退回
				lms8100Service.setBackActLog(l300m01a);

				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		}

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL300m01c(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));

		if (Util.isNotEmpty(oid)) {
			L300M01C l300m01c = lms8100Service.findModelByOid(L300M01C.class,
					oid);
			if (l300m01c != null) {
				if (Util.isNotEmpty(Util.nullToSpace(l300m01c.getSendTime()))) {
					throw new CapMessageException("排名表已傳送，不可刪除！", getClass());
				} else {
					l300m01c.setDeletedTime(CapDate.getCurrentTimestamp());
					lms8100Service.save(l300m01c);

					List<L300M01A> l300m01aList = lms8100Service
							.findL300m01aProduceList(l300m01c.getOwnBrId(),
									CreditDocStatusEnum.海外_已核准.getCode(),
									CapDate.formatDate(l300m01c.getBgnDate(),
											"yyyy-MM-dd"),
									CapDate.formatDate(l300m01c.getEndDate(),
											"yyyy-MM-dd"), null);
					if (l300m01aList != null && l300m01aList.size() > 0) {
						for (L300M01A m01a : l300m01aList) {
							m01a.setProduceFlag("N");
							// 計算分數歸0
							m01a.setRealScore(BigDecimal.ZERO);
							m01a.setTotalScore(BigDecimal.ZERO);
							lms8100Service.save(m01a);
						}
					}

					result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
							RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
				}
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendL300m01c(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));

		if (Util.isNotEmpty(oid)) {
			L300M01C l300m01c = lms8100Service.findModelByOid(L300M01C.class,
					oid);
			if (l300m01c != null) {
				if (Util.isNotEmpty(Util.nullToSpace(l300m01c.getSendTime()))) {
					throw new CapMessageException("排名表已傳送，不需再傳送！", getClass());
				}
				l300m01c.setSendTime(CapDate.getCurrentTimestamp());
				lms8100Service.save(l300m01c);

				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult returnL300m01c(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));

		if (Util.isNotEmpty(oid)) {
			L300M01C l300m01c = lms8100Service.findModelByOid(L300M01C.class,
					oid);
			if (l300m01c != null) {
				l300m01c.setSendTime(null);
				lms8100Service.save(l300m01c);

				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			}
		}
		return result;
	}
}
