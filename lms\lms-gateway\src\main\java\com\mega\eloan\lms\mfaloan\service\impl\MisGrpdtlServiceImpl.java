package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisGrpdtlService;

@Service
public class MisGrpdtlServiceImpl extends AbstractMFAloanJdbc implements
		MisGrpdtlService {
	
	public List<?> findGrpdtlForGrpnm(String grpNo) {
		return this.getJdbc().queryForList("MISGRPDTL.selGrpnm",
				new String[] { grpNo },0,1);
	}

	@Override
	public List<?> findGrpdtlForAllGrpid() {
		return this.getJdbc().queryForList("MISGRPDTL.selAllGrpid",
				new String[] {});
	}
	
	/**
	 * 集團代號取得集團基本資料
	 * 
	 * @param groupId
	 *            集團代號
	 * @return Map<String, Object>
	 */
	@Override
	public Map<String, Object> findDtlByGrpId(String groupId) {
		return getJdbc().queryForMap("GRPDTL.findByGrpId",
				new String[] { groupId });
	}// ;

	/**
	 * 取得所有集團基本資料
	 * 
	 * @return List<Map<String, Object>>
	 */
	@Override
	public List<Map<String, Object>> findAllDtl() {
		return getJdbc().queryForList("GRPDTL.findAll", null);
	}

//	public List<Map<String, Object>> findDtlByType(String type, String condition) {
//		List<String> temp = new ArrayList<String>();
//		StringBuilder sb = new StringBuilder();
//		String sql = getSqlBySqlId("GRPDTL.findDtlByType");
//		if ("all".equalsIgnoreCase(type)) {
//			StringBuilder sqlStrBuff = new StringBuilder(sql);
//			sqlStrBuff.delete(sqlStrBuff.lastIndexOf("WHERE GC.{0}"),
//					sqlStrBuff.length() - 1);
//			return getJdbc().queryForList(sqlStrBuff.toString(), null, 0, 1000);
//		} else {
//			StringTokenizer st = new StringTokenizer(condition, ",");
//			while (st.hasMoreElements()) {
//				if ("grpId".equalsIgnoreCase(type)) {
//					temp.add(CapString.cutString(st.nextElement().toString(),
//							"", 4));
//				} else if ("ban".equalsIgnoreCase(type)) {
//					temp.add(CapString.cutString(st.nextElement().toString(),
//							"", 10));
//				} else if ("cmpNm".equalsIgnoreCase(type)) {
//					temp.add(CapString.cutString(st.nextElement().toString(),
//							"", 44));
//				}
//				sb.append("?,");
//			}
//		}
//		sql = MessageFormat.format(sql,
//				new Object[] { type, sb.delete(sb.length() - 1, sb.length()) });
//		return getJdbc().queryForList(sql, temp.toArray());
//	}

	/**
	 * 客戶ID取得其集團代號及名稱
	 * 
	 * @param ban
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return Map<String, Object>
	 */
	@Override
	public Map<String, Object> findCmpByBanDup(String ban, String dupNo) {
		return getJdbc().queryForMap("GRPCMP.findByBanDup",
				new String[] { ban, dupNo });
	}// ;

	/**
	 * 集團代號取得集團轄下公司資料
	 * 
	 * @param grpid
	 *            集團代號
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> findCmpByGrpid(String grpid) {
		return getJdbc().queryForList("GRPCMP.findByGRPID",
				new String[] { grpid });
	}

	/**
	 * 取得新集團代號
	 * 
	 * @return Map<String, Object>
	 */
	public Map<String, Object> findGrpdtlNextGrpid() {
		return getJdbc().queryForMap("GRPDTL.findNextGrpid", null);
	}

	/**
	 * 新增集團企業基本資料
	 * 
	 * @param grpid
	 *            集團代號
	 * @param grpnm
	 *            集團名稱
	 * @param branch
	 *            資料彙整分行代號
	 * @param updater
	 *            資料修改人
	 * @param updateTime
	 *            資料修改日期
	 * @param grpyy
	 *            年度(民國年)
	 * @return 是否成功
	 */
	public int addGrpdtl(String grpid, String grpnm, String branch,
			Timestamp updateTime, String updater, String grpyy) {
		// 新增
		// INSERT INTO MIS.GRPDTL(GRPID, GRPNM, BRANCH, UPDATER, TMESTAMP,
		// GRPYY, GRPGRADE) VALUES(?,?,?,?,?,?,'6')
		return getJdbc()
				.update("GRPDTL.insert",
						new Object[] { grpid, grpnm, branch, updater,
								updateTime, grpyy });
	}

	/**
	 * 依據集團代號刪除集團資料
	 * 
	 * @param grpid
	 *            集團代號
	 * @return 刪除筆數
	 */
	public int delGrpdtl(String grpid) {
		return getJdbc().update("GRPDTL.deleteByGRPID", new Object[] { grpid });
	}

	@Override
	public Map<String, Object> findAdjustByGrpId(String grpId) {
		return getJdbc().queryForMap("GRPDIFF.findAdjustByGrpId",
				new Object[] { grpId });
	}
	
	@Override
	public Map<String, Object> findGrpdtl_selGrpyy() {
		return getJdbc().queryForMap("MISGRPDTL.selGrpyy",
				new Object[] { });
	}	
}
