package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import com.mega.eloan.lms.base.constants.UtilConstants;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

public class CrsVO {
	private static final String[] COL_030_040 = new String[]{
		"LNF030_CHARC_CODE"
		, "LNF030_LN_PURPOSE"
		, "LNF030_LOAN_BAL"
		, "LNF030_LOAN_CLASS"
		, "LNF030_LOAN_NO"
		, "LNF030_OPEN_DATE"
		, "LNF030_STATUS"
		, "LNF030_SWFT"
		, "LNF040_ACT_CODE"
		, "LNF040_ACT_NAME"
		, "LNF040_LNAP_CODE"
	};
	
	private Map<String, Map<String, Object>> this_m_single = new HashMap<String, Map<String, Object>>();
	private Map<String, Boolean> this_m_staff = new HashMap<String, Boolean>();
	
	/*
	 * LNF020_CONTRACT + LNF020_BANK_CODE + LNF020_LN_BR_NO + LNF020_FACT_TYPE
	 * 同1個額度序號,會有 N 個LNF020_FACT_TYPE=31 
	 */
	private Map<String, List<Map<String, Object>>> this_m_LNF020 = new HashMap<String, List<Map<String, Object>>>();
	
	private Map<String, List<Map<String, Object>>> this_m_LNF030_040 = new HashMap<String, List<Map<String, Object>>>();
	
	private String _k_single(String brNo, String custId, String dupNo){
		return Util.trim(brNo)+"_"+Util.trim(custId)+"_"+Util.trim(dupNo);
	}
	
	private String _k_LNF020(String custId, String dupNo){
		return "_"+Util.trim(custId)+"_"+Util.trim(dupNo);
	}
	
	private String _k_LNF030_040(String cntrNo){
		return "_"+Util.trim(cntrNo);
	}
	
	private Map<String, Object> _copy(Map<String, Object> m_new, String[] keys){
		Map<String, Object> r = new HashMap<String, Object>();
		for(String k: keys){
			r.put(k, m_new.get(k));
		}
		return r;
	}
	
	private String _pk_lnf020(String _LNF020_CONTRACT, String _LNF020_BANK_CODE
			, String _LNF020_LN_BR_NO, String _LNF020_FACT_TYPE){
		return Util.trim(_LNF020_CONTRACT+"_"+_LNF020_BANK_CODE+"_"+_LNF020_LN_BR_NO+"_"+_LNF020_FACT_TYPE);
	}
	private void _addVal020(String _k__LNF020, String pk_lnf020, Map<String, Object> m_020){
		boolean inExist = false;
		for(Map<String, Object> exist : this_m_LNF020.get(_k__LNF020)){
			if(Util.equals(pk_lnf020, _pk_lnf020(Util.trim(exist.get("LNF020_CONTRACT")),
					Util.trim(exist.get("LNF020_BANK_CODE")),
					Util.trim(exist.get("LNF020_LN_BR_NO")),
					Util.trim(exist.get("LNF020_FACT_TYPE")) ))){				
				inExist = true;
				break;
			}				
		}
		
		if(inExist==false){
			this_m_LNF020.get(_k__LNF020).add(m_020);
		}
	}
	
	private void _addVal030(String _k_LNF030_040, String loanNo, Map<String, Object> m_030040){
		boolean inExist = false;
		for(Map<String, Object> exist : this_m_LNF030_040.get(_k_LNF030_040)){
			if(Util.equals(loanNo, Util.trim(exist.get("LNF030_LOAN_NO")))){
				inExist = true;
				break;
			}				
		}
		
		if(inExist==false){
			this_m_LNF030_040.get(_k_LNF030_040).add(m_030040);
		}
	}
	
	public void addELF491_LNData(List<Map<String, Object>> row491_020){
		if(CollectionUtils.isEmpty(row491_020)){
			return;
		}
		Map<String, List<Map<String, Object>> > tmp = new HashMap<String, List<Map<String, Object>>>();
		for(Map<String, Object> dataMap: row491_020){
			String brNo = Util.trim(dataMap.get("ELF491_BRANCH"));
			String custId = Util.trim(dataMap.get("ELF491_CUSTID"));
			String dupNo = Util.trim(dataMap.get("ELF491_DUPNO"));			
			String _k_single = _k_single(brNo, custId, dupNo);
			if(!tmp.containsKey(_k_single)){
				tmp.put(_k_single, new ArrayList<Map<String, Object>>());
			}
			tmp.get(_k_single).add(dataMap);
		}
		
		for(String _k_single : tmp.keySet()){
			List<Map<String, Object>> lists = tmp.get(_k_single);
			if(CollectionUtils.isNotEmpty(lists)){
				Map<String, Object> m_first = lists.get(0);
				/*
				 	若加欄位
				 	<entry key="ELF491.sel_whenProduce"> 也要加
				 */
				_copy(m_first, new String[]{"ELF491_BRANCH"
						,"ELF491_CUSTID"
						,"ELF491_DUPNO"
						,"ELF491_MAINCUST"
						,"ELF491_LLRDATE"
						,"ELF491_LRDATE"
						,"ELF491_CRDATE"
						,"ELF491_NCKDFLAG"
						,"ELF491_NCKDDATE"
						,"ELF491_NCKDMEMO"
						,"ELF491_CANCELDT"
						,"ELF491_UPDATER"
						,"ELF491_TMESTAMP"
						,"ELF491_UCKDLINE"
						,"ELF491_UCKDDT"
						,"ELF491_REPORTKIND"
						,"ELF491_REMOMO"
						,"ELF491_NEWFLAG"
						,"ELF491_LASTREALDT"
						,"CNAME"});
				
				String custId = Util.trim(m_first.get("ELF491_CUSTID"));
				String dupNo = Util.trim(m_first.get("ELF491_DUPNO"));			
				
				this_m_single.put(_k_single, m_first);
				this_m_staff.put(LMSUtil.getCustKey_len10custId(custId, dupNo), StringUtils.isNotBlank(Util.trim(m_first.get("MISSTAFF_EMPID"))));
				add_LNData(custId, dupNo, lists);
			}
		}
	}
	
	public void add_LNData(String custId, String dupNo, List<Map<String, Object>> row){
		//處理 m_LNF020
		if(true){
			String _k__LNF020 = _k_LNF020(custId, dupNo);
			if( ! this_m_LNF020.containsKey(_k__LNF020)){
				this_m_LNF020.put(_k__LNF020, new ArrayList<Map<String, Object>>());
			}
			
			for(Map<String, Object> m_new : row){
				String cntrNo = Util.trim(m_new.get("LNF020_CONTRACT"));
				if(Util.isEmpty(cntrNo) ){
					continue;
				}
				
				String _pk_lnf020 = _pk_lnf020(Util.trim(m_new.get("LNF020_CONTRACT")),
						Util.trim(m_new.get("LNF020_BANK_CODE")),
						Util.trim(m_new.get("LNF020_LN_BR_NO")),
						Util.trim(m_new.get("LNF020_FACT_TYPE")) );
						
				_addVal020(_k__LNF020, _pk_lnf020, _copy(m_new, new String[]{
						"LNF020_BANK_CODE" ,"LNF020_BEG_DATE"
						,"LNF020_CONTRACT" ,"LNF020_CREATE_DT"
						,"LNF020_CUST_ID" ,"LNF020_DOCUMENT_NO"
						,"LNF020_DURATION_BG" ,"LNF020_DURATION_ED"
						,"LNF020_END_DATE" ,"LNF020_FACT_AMT"
						,"LNF020_FACT_TYPE" ,"LNF020_GRP_CNTRNO"
						,"LNF020_LN_BR_NO" ,"LNF020_REVOLVE"
						,"LNF020_SWFT" ,"LNF020_USED_AMT"
						,"LNF020_SYND_TYPE" ,"LNF020_PROJ_CLASS"})
						/* 需一併調整 misSQL.xml 裡的 
						    "ELF491.sel_whenProduce"
						  	"LNLNF030.selNewLnfData"
						 */
				);	
			}	
		}
		
		//處理m_LNF030_040
		if(true){			
			for(Map<String, Object> m_new : row){
				String cntrNo = Util.trim(m_new.get("LNF020_CONTRACT"));				
				Map<String, Object> m_030040 = _copy(m_new, COL_030_040);
				String loanNo = Util.trim(m_030040.get("LNF030_LOAN_NO"));
				if(Util.isEmpty(cntrNo) || Util.isEmpty(loanNo)){
					continue;
				}
				//==================				
				String _k_LNF030_040 = _k_LNF030_040(cntrNo);
				if(! this_m_LNF030_040.containsKey(_k_LNF030_040)){
					this_m_LNF030_040.put(_k_LNF030_040, new ArrayList<Map<String, Object>>());	
				}
				_addVal030(_k_LNF030_040, loanNo, m_030040);
			}						
		}
	}
	
	public Collection<Map<String, Object>> listSingle(){
		return this_m_single.values();
	}
	
	public List<Map<String, Object>> getLNF020( String custId, String dupNo){
		List<Map<String, Object>> r = this_m_LNF020.get(_k_LNF020( custId, dupNo));
		if(r==null){
			r = new ArrayList<Map<String, Object>>();
		}
		return r;
	}
	
	/**
	 * 注意：LNF030_LOAN_NO 前3碼是否=LNF020_LN_BR_NO
	 */
	public List<Map<String, Object>> getLNF030_040(String cntrNo){
		List<Map<String, Object>> r = this_m_LNF030_040.get(_k_LNF030_040(cntrNo));
		if(r==null){
			r = new ArrayList<Map<String, Object>>();
		}
		return r;
	}
	
	/**
	 * 在引帳務時
	 * 30-A分行
	 * 31-A分行
	 * 		 ---> 其 QuotaAmt、BalAmt 放母戶的資料【join LNF020,LNF030 where LNF020_FACT_TYPE=30 and LNF030_CHARC_CODE=30】
	 *       ---> 其 sQuotaAmt、sBalAmt 放子戶的資料
	 * 31-B分行
	 */
	public List<Map<String, Object>> getLNF030_040_withLNF020_LN_BR_NO_NoCharcCode30(String cntrNo, String lnf020_ln_br_no){
		List<Map<String, Object>> r = new ArrayList<Map<String, Object>>();
		for(Map<String, Object> dataMap030_040: getLNF030_040(cntrNo)){
			String lnf030_loan_no = Util.trim(dataMap030_040.get("LNF030_LOAN_NO"));
			String lnf030_charc_code = Util.trim(dataMap030_040.get("LNF030_CHARC_CODE"));
			if(Util.isEmpty(lnf030_loan_no)){
				continue;
			}
			if(Util.equals(UtilConstants.Cntrdoc.snoKind.聯貸, lnf030_charc_code)){				
				continue;
			}
			
			if(Util.notEquals(lnf020_ln_br_no, CrsUtil.getBrNoFromLNF030_LOAN_NO(lnf030_loan_no))){
				continue;
			}
			r.add(dataMap030_040);
		}
		return r;
	}
	
	public boolean get_staff(String custId_dupNo){
		return MapUtils.getBooleanValue(this_m_staff, custId_dupNo);
	}
	
	public void set_staff(String custId_dupNo, Boolean val){
		this_m_staff.put(custId_dupNo, val);
	}
	
	public boolean contains_staff(String custId_dupNo){
		return this_m_staff.containsKey(custId_dupNo);
	}
	
	public Map<String, Object> mock030_040(){
		Map<String, Object> r = new HashMap<String, Object>();

		for(String k: COL_030_040){
			Object v = null;
			if(k.indexOf("_BAL")>0){
				v = BigDecimal.ZERO;
			}else if(k.indexOf("_DATE")>0){
				v = CapDate.parseDate(CapDate.ZERO_DATE);
			}else{
				v = "";
			}
			r.put(k, v);
					
		}
		return r;
	}
}
