/* 
 * L120S01M.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;
import org.hibernate.annotations.GenericGenerator;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 信用風險管理遵循主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S01M", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class L120S01M extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 淨值
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "NETVAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal netVal;

	/** 查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "QUERYDATE", columnDefinition = "DATE")
	private Date queryDate;

	/**
	 * 有無銀行法利害關係人
	 * <p/>
	 * 1.有 2.無 3.不適用
	 */
	@Size(max = 1)
	@Column(name = "MBRLT", length = 1, columnDefinition = "VARCHAR(1)")
	private String mbRlt;

	/**
	 * 有無金控法44條利害關係人
	 * <p/>
	 * 1.有 2.無 3.不適用
	 */
	@Size(max = 1)
	@Column(name = "MHRLT44", length = 1, columnDefinition = "VARCHAR(1)")
	private String mhRlt44;

	/**
	 * 有無金控法45條利害關係人
	 * <p/>
	 * 1.有 2.無 3.不適用
	 */
	@Size(max = 1)
	@Column(name = "MHRLT45", length = 1, columnDefinition = "VARCHAR(1)")
	private String mhRlt45;

	/** 集團代號 **/
	@Size(max = 4)
	@Column(name = "GRPNO", length = 4, columnDefinition = "VARCHAR(4)")
	private String grpNo;

	/** 集團評等 **/
	@Size(max = 1)
	@Column(name = "GRPGRADE", length = 1, columnDefinition = "VARCHAR(1)")
	private String grpGrade;

	/** 集團評等年度 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "GRPYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer grpYear;

	/** 集團名稱 **/
	@Size(max = 60)
	@Column(name = "GRPNAME", length = 60, columnDefinition = "VARCHAR(60)")
	private String grpName;

	/** 行業對像別 **/
	@Size(max = 6)
	@Column(name = "BUSCODE", length = 6, columnDefinition = "CHAR(6)")
	private String busCode;

	/**
	 * 客戶類別
	 * <p/>
	 * 1.同一人(銀行、外國政府)<br/>
	 * 2.同一人(其他)
	 */
	@Size(max = 1)
	@Column(name = "CUSTTYPE", length = 1, columnDefinition = "VARCHAR(1)")
	private String custType;

	/** 內部信評 **/
	@Size(max = 2)
	@Column(name = "MOWTBL1", length = 2, columnDefinition = "VARCHAR(2)")
	private String mowTbl1;

	/** 外部評等 **/
	@Size(max = 3)
	@Column(name = "CRSCORE", length = 3, columnDefinition = "VARCHAR(3)")
	private String crsCore;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 是否已引進當地金額 **/
	@Size(max = 1)
	@Column(name = "HASLOCALAMT", length = 1, columnDefinition = "VARCHAR(1)")
	private String hasLocalAmt;

	/** 當地淨值幣別 **/
	@Size(max = 3)
	@Column(name = "LOCALNETVALCURR", length = 3, columnDefinition = "VARCHAR(3)")
	private String localNetValCurr;

	/**
	 * 當地淨值
	 * <p/>
	 * 
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LOCALNETVAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal localNetVal;

	/** 是否已計算授信額度合計 **/
	@Size(max = 1)
	@Column(name = "ISCACULATE", length = 1, columnDefinition = "VARCHAR(1)")
	private String isCaculate;

	/** 當地集團 **/
	@Size(max = 9)
	@Column(name = "LOCALGROUP", length = 9, columnDefinition = "VARCHAR(9)")
	private String localGroup;

	/** 集團企業規模 **/
	@Column(name = "GRPSIZE", length = 1, columnDefinition = "CHAR(01)")
	private String grpSize;

	/** 集團企業規模級別 **/
	@Column(name = "GRPLEVEL", length = 1, columnDefinition = "CHAR(01)")
	private String grpLevel;

	/** 集團評等年度 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "GRPLASTYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer grpLastYear;
	
	/**
	 * JOIN 信用風險管理遵循明細 L120S01N
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l120s01m", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<L120S01N> l120s01n;

	public List<L120S01N> getL120s01n() {
		return l120s01n;
	}

	public void setL120s01n(List<L120S01N> l120s01n) {
		this.l120s01n = l120s01n;
	}

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得淨值
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getNetVal() {
		return this.netVal;
	}

	/**
	 * 設定淨值
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setNetVal(BigDecimal value) {
		this.netVal = value;
	}

	/** 取得查詢日期 **/
	public Date getQueryDate() {
		return this.queryDate;
	}

	/** 設定查詢日期 **/
	public void setQueryDate(Date value) {
		this.queryDate = value;
	}

	/**
	 * 取得有無銀行法利害關係人
	 * <p/>
	 * Y/N
	 */
	public String getMbRlt() {
		return this.mbRlt;
	}

	/**
	 * 設定有無銀行法利害關係人
	 * <p/>
	 * Y/N
	 **/
	public void setMbRlt(String value) {
		this.mbRlt = value;
	}

	/**
	 * 取得有無金控法44條利害關係人
	 * <p/>
	 * Y/N
	 */
	public String getMhRlt44() {
		return this.mhRlt44;
	}

	/**
	 * 設定有無金控法44條利害關係人
	 * <p/>
	 * Y/N
	 **/
	public void setMhRlt44(String value) {
		this.mhRlt44 = value;
	}

	/**
	 * 取得有無金控法45條利害關係人
	 * <p/>
	 * Y/N
	 */
	public String getMhRlt45() {
		return this.mhRlt45;
	}

	/**
	 * 設定有無金控法45條利害關係人
	 * <p/>
	 * Y/N
	 **/
	public void setMhRlt45(String value) {
		this.mhRlt45 = value;
	}

	/** 取得集團代號 **/
	public String getGrpNo() {
		return this.grpNo;
	}

	/** 設定集團代號 **/
	public void setGrpNo(String value) {
		this.grpNo = value;
	}

	/** 取得集團評等 **/
	public String getGrpGrade() {
		return this.grpGrade;
	}

	/** 設定集團評等 **/
	public void setGrpGrade(String value) {
		this.grpGrade = value;
	}

	/** 取得集團評等年度 **/
	public Integer getGrpYear() {
		return this.grpYear;
	}

	/** 設定集團評等年度 **/
	public void setGrpYear(Integer value) {
		this.grpYear = value;
	}

	/** 取得集團名稱 **/
	public String getGrpName() {
		return this.grpName;
	}

	/** 設定集團名稱 **/
	public void setGrpName(String value) {
		this.grpName = value;
	}

	/** 取得行業對像別 **/
	public String getBusCode() {
		return this.busCode;
	}

	/** 設定行業對像別 **/
	public void setBusCode(String value) {
		this.busCode = value;
	}

	/**
	 * 取得客戶類別
	 * <p/>
	 * 1.同一人(銀行、外國政府)<br/>
	 * 2.同一人(其他)
	 */
	public String getCustType() {
		return this.custType;
	}

	/**
	 * 設定客戶類別
	 * <p/>
	 * 1.同一人(銀行、外國政府)<br/>
	 * 2.同一人(其他)
	 **/
	public void setCustType(String value) {
		this.custType = value;
	}

	/** 取得內部信評 **/
	public String getMowTbl1() {
		return this.mowTbl1;
	}

	/** 設定內部信評 **/
	public void setMowTbl1(String value) {
		this.mowTbl1 = value;
	}

	/** 取得外部評等 **/
	public String getCrsCore() {
		return this.crsCore;
	}

	/** 設定外部評等 **/
	public void setCrsCore(String value) {
		this.crsCore = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 設定是否已引進當地金額 **/
	public void setHasLocalAmt(String hasLocalAmt) {
		this.hasLocalAmt = hasLocalAmt;
	}

	/** 取得是否已引進當地金額 **/
	public String getHasLocalAmt() {
		return hasLocalAmt;
	}

	/** 設定當地淨值幣別 **/
	public void setLocalNetValCurr(String localNetValCurr) {
		this.localNetValCurr = localNetValCurr;
	}

	/** 取得當地淨值幣別 **/
	public String getLocalNetValCurr() {
		return localNetValCurr;
	}

	/** 設定當地淨值 **/
	public void setLocalNetVal(BigDecimal localNetVal) {
		this.localNetVal = localNetVal;
	}

	/** 取得當地淨值 **/
	public BigDecimal getLocalNetVal() {
		return localNetVal;
	}

	/** 設定是否已計算授信額度合計 **/
	public void setIsCaculate(String isCaculate) {
		this.isCaculate = isCaculate;
	}

	/** 取得是否已計算授信額度合計 **/
	public String getIsCaculate() {
		return isCaculate;
	}

	/** 設定當地集團 **/
	public void setLocalGroup(String localGroup) {
		this.localGroup = localGroup;
	}

	/** 取得當地集團 **/
	public String getLocalGroup() {
		return localGroup;
	}

	public void setGrpSize(String grpSize) {
		this.grpSize = grpSize;
	}

	public String getGrpSize() {
		return grpSize;
	}

	public void setGrpLevel(String grpLevel) {
		this.grpLevel = grpLevel;
	}

	public String getGrpLevel() {
		return grpLevel;
	}

	public void setGrpLastYear(Integer grpLastYear) {
		this.grpLastYear = grpLastYear;
	}

	public Integer getGrpLastYear() {
		return grpLastYear;
	}
}
