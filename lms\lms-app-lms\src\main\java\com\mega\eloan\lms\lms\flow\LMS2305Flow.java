/* 
 *  LMS2305Flow.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.flow;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01MDao;
import com.mega.eloan.lms.dao.L230A01ADao;
import com.mega.eloan.lms.dao.L230S01ADao;
import com.mega.eloan.lms.mfaloan.service.MisELF447nService;
import com.mega.eloan.lms.mfaloan.service.MisELF503Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L230A01A;
import com.mega.eloan.lms.model.L230M01A;
import com.mega.eloan.lms.model.L230S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 簽約未動用授信案件送作業
 * </pre>
 * 
 * @since 2011/04/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/04/17 new
 *          <li>2013/07/08,Rex,當沒有更新到資料，則新增一筆到447n之中
 *          </ul>
 */

@Component
public class LMS2305Flow extends AbstractFlowHandler {

	@Resource
	CommonMetaDao metaDao;
	@Resource
	L120M01ADao l120m01aDao;
	@Resource
	L230A01ADao l230A01ADao;

	@Resource
	L230S01ADao l230s01aDao;
	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	EmailClient emailClient;

	@Resource
	BranchService branchService;

	@Resource
	MisELF447nService misELF447nService;

	@Resource
	L140M01MDao l140m01mDao;

	@Resource
	LMSService lmsService;

	@Resource
	L120S01BDao l120s01bDao;

	@Resource
	MisELF503Service misELF503Service;

	private static Logger logger = LoggerFactory.getLogger(LMS2305Flow.class);

	/**
	 * 退回經辦
	 * 
	 * @param instance
	 */
	@Transition(node = "決策", value = "to退回")
	public void back(FlowInstance instance) {
		// 退回時要清空覆核主管、授信主管ID
		L230M01A meta = (L230M01A) metaDao.findByOid(getDomainClass(), instance
				.getId().toString());
		meta.setBossId("");
		meta.setManagerId("");
	}

	/**
	 * 核准 要發送信件到918
	 * 
	 * @param instance
	 */
	@SuppressWarnings("unused")
	@Transition(node = "決策", value = "to核定")
	public void acp(FlowInstance instance) {
		L230M01A meta = (L230M01A) metaDao.findByOid(getDomainClass(), instance
				.getId().toString());
		String mail = PropUtil.getProperty("sendMail");
		String[] recv = mail.split(UtilConstants.Mark.SPILT_MARK);

		StringBuffer conetent = new StringBuffer();
		conetent.append("【").append(meta.getOwnBrId()).append(" ")
				.append(branchService.getBranchName(meta.getOwnBrId()))
				.append("】已核准授信額度辦理狀態報送作業");
		updateElf447n(meta);

		// J-109-0202_05097_B1001 Web e-Loan利費率資料提前至授信案件經核定後逕行寫入
		L120M01A l120m01a = l120m01aDao.findByMainId(meta.getUid());
		if (l120m01a != null) {
			boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
					.getBranch(l120m01a.getCaseBrId()).getBrNoFlag());
			if (UtilConstants.Casedoc.DocType.企金.equals(Util.trim(l120m01a
					.getDocType())) && !isOverSea) {

			}
			updateElf503(meta);
		}

		/* 授管處反應信收太多，暫時取消發MAIL */
		// emailClient.send(recv, conetent.toString(), conetent.toString());
		String authUnit = UtilConstants.BankNo.授管處;
		String mainId = meta.getMainId();
		// 並且要新增授權檔到授管處
		L230A01A l230a01a = l230A01ADao.findByUniqueKey(mainId, authUnit);

		if (l230a01a == null) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			l230a01a = new L230A01A();
			l230a01a.setAuthTime(CapDate.getCurrentTimestamp());
			l230a01a.setAuthType(DocAuthTypeEnum.VIEW_TRANSFER.getCode());
			l230a01a.setAuthUnit(authUnit);
			l230a01a.setMainId(mainId);
			l230a01a.setOwner(user.getUserId());
			l230a01a.setOwnUnit(user.getUnitNo());
		}
		l230A01ADao.save(l230a01a);
	}

	/**
	 * 更新447n
	 * 
	 * @param meta
	 *            簽約未動用主檔
	 */
	private void updateElf447n(L230M01A meta) {
		logger.info("=====================[updateElf447n]====================================");
		List<L230S01A> l230s01as = l230s01aDao.findByMainId(meta.getMainId());
		// 文件狀態 0- 預約 ( 預約到期日內有效 ) 1- 報核 ( 報核日起三個月有效 ) 2- 已核定 ( 核定日起六個月有效 )A-
		// 已撤銷B- 已婉卻C- 已退件
		String ELF447N_STATUS = "";
		// DATE NOT NULL,狀態日0- 預約到期日 1- 報核日 2- 核定日A- 撤銷日 B- 婉卻日
		String ELF447N_STATUS_DT = "";
		// DATE 未動用原因維護日
		String ELF447N_NUSEDATE = "";
		// VCHAR(804) 簽約未動用原因
		String ELF447N_NUSEMEMO = "";
		// 簽報書的mainId
		String ELF447N_UNID = meta.getUid();
		int totalData = 0;
		int updateRowCount = 0;
		String ELF447N_CONTRACT = "";
		// 簽約日
		Date signDate = null;

		// J-111-0551_05097_B1003 Web
		// e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
		BigDecimal ELF447N_CURAMT_S = null;
		BigDecimal ELF447N_CURAMT_N = null;

		// 不簽約原因 代碼
		// 01;02;03;04;05;06;07;…
		String ELF447N_NSIGN_CODE = "";
		// 不簽約原因說明(點選第99項時，於100文字以內由營業單位鍵入簡要說明)
		String ELF447N_NSIGN_MEMO = "";

		// 目前時間
		String nowDate = CapDate.formatDate(CapDate.getCurrentTimestamp(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		L120M01A l120m01a = l120m01aDao.findByMainId(meta.getUid());

		// 簽報書核准日
		String l120m01aApproveTime = null;
		if (l120m01a == null) {
			logger.error("l120m01a is null");
			l120m01aApproveTime = CapDate.formatDate(new Date(),
					UtilConstants.DateFormat.YYYY_MM_DD);
		} else {
			l120m01aApproveTime = CapDate.formatDate(l120m01a.getApproveTime(),
					UtilConstants.DateFormat.YYYY_MM_DD);
		}

		for (L230S01A l230s01a : l230s01as) {
			// 如果性質為【不變、取消】不上傳
			if (LMSUtil.isContainValue(l230s01a.getProPerty(),
					UtilConstants.Cntrdoc.Property.不變)
					|| LMSUtil.isContainValue(l230s01a.getProPerty(),
							UtilConstants.Cntrdoc.Property.取消)) {
				continue;
			}

			signDate = l230s01a.getSignDate();
			ELF447N_STATUS_DT = nowDate;
			ELF447N_STATUS = l230s01a.getNuseMemo();
			ELF447N_CONTRACT = l230s01a.getCntrNo();
			ELF447N_NUSEDATE = CapDate.formatDate(l230s01a.getDataDate(),
					UtilConstants.DateFormat.YYYY_MM_DD);
			ELF447N_NUSEMEMO = meta.getReasion();

			String[] reasonArray = Util.trim(l230s01a.getReason()).split(
					UtilConstants.Mark.SPILT_MARK);
			StringBuffer temp = new StringBuffer();
			// 上傳時小於10的欄位前面要補0
			for (String reason : reasonArray) {
				temp.append(temp.length() > 0 ? ";" : "");
				if (Util.isNotEmpty(reason)) {
					temp.append(Util.addZeroWithValue(reason, 2));
				}

			}
			ELF447N_NSIGN_CODE = temp.toString();
			ELF447N_NSIGN_MEMO = Util.trimSizeInOS390(
					Util.trim(l230s01a.getReasonDrc()), 200);

			// J-111-0551_05097_B1003 Web
			// e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
			ELF447N_CURAMT_S = null;
			ELF447N_CURAMT_N = null;
			String srcMainId = Util.trim(l230s01a.getSrcMainId());

			L140M01A l140m01a = l140m01aDao.findByMainId(srcMainId);

			// J-111-0551_05097_B1003 Web
			// e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
			String isSignAmtLowerApplyAmt = "";
			BigDecimal signAmt_S = null;
			BigDecimal signAmt_N = null;
			boolean useL230s01a = false;
			if (UtilConstants.NoUseCase.NuseMemo.已簽約.equals(ELF447N_STATUS)) {
				// J-111-0551_05097_B1003 Web
				// e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
				isSignAmtLowerApplyAmt = Util.trim(l230s01a
						.getIsSignAmtLowerApplyAmt());
				signAmt_S = l230s01a.getSignAmt_S();
				signAmt_N = l230s01a.getSignAmt_N();
			}

			if (Util.equals(isSignAmtLowerApplyAmt, "Y") && signAmt_S != null
					&& signAmt_N != null) {
				// 改以分行輸入的為主
				useL230s01a = true;
				ELF447N_CURAMT_S = signAmt_S;
				ELF447N_CURAMT_N = signAmt_N;
			}

			if (!useL230s01a) {
				if (l140m01a != null) {
					boolean twClsUseAssure = true; // 已經沒用，改讀系統設定LMS_ELF447N_AMT_USE_CLS_ASSURE
					Map<String, BigDecimal> currAmtMap = lmsService
							.getCurrentApplyAmt_S_N(l120m01a, l140m01a, false);
					if (currAmtMap != null && !currAmtMap.isEmpty()) {
						ELF447N_CURAMT_S = currAmtMap.get("ELF447N_CURAMT_S") == null ? null
								: Util.parseBigDecimal(currAmtMap
										.get("ELF447N_CURAMT_S"));
						ELF447N_CURAMT_N = currAmtMap.get("ELF447N_CURAMT_N") == null ? null
								: Util.parseBigDecimal(currAmtMap
										.get("ELF447N_CURAMT_N"));
					}
				}
			}

			// J-111-0551_05097_B1003 Web
			// e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
			if (UtilConstants.NoUseCase.NuseMemo.已簽約.equals(ELF447N_STATUS)) {
				if (Util.isNotEmpty(signDate)) {
					ELF447N_STATUS_DT = CapDate.formatDate(signDate,
							UtilConstants.DateFormat.YYYY_MM_DD);
				}
				ELF447N_NSIGN_CODE = "";
				ELF447N_NSIGN_MEMO = "";

			} else if (UtilConstants.NoUseCase.NuseMemo.已動用
					.equals(ELF447N_STATUS)) {
				if (Util.isNotEmpty(signDate)) {
					ELF447N_STATUS_DT = CapDate.formatDate(signDate,
							UtilConstants.DateFormat.YYYY_MM_DD);
				}
				ELF447N_NSIGN_CODE = "";
				ELF447N_NSIGN_MEMO = "";

			} else if (UtilConstants.NoUseCase.NuseMemo.不簽約註銷額度
					.equals(ELF447N_STATUS)) {
				if (Util.isNotEmpty(ELF447N_NUSEDATE)) {
					ELF447N_STATUS_DT = ELF447N_NUSEDATE;
				}
			} else if (UtilConstants.NoUseCase.NuseMemo.未簽約一不需簽約
					.equals(ELF447N_STATUS)) {
				if (Util.isNotEmpty(ELF447N_NUSEDATE)) {
					ELF447N_STATUS_DT = ELF447N_NUSEDATE;
				}
				ELF447N_NSIGN_CODE = "";
				ELF447N_NSIGN_MEMO = "";
			} else if (UtilConstants.NoUseCase.NuseMemo.未簽約一聯貸案待確認
					.equals(ELF447N_STATUS)) {
				if (Util.isNotEmpty(ELF447N_NUSEDATE)) {
					ELF447N_STATUS_DT = ELF447N_NUSEDATE;
				}
				ELF447N_NSIGN_CODE = "";
				ELF447N_NSIGN_MEMO = "";
			} else if (UtilConstants.NoUseCase.NuseMemo.未簽約一辦理中
					.equals(ELF447N_STATUS)) {
				if (Util.isNotEmpty(ELF447N_NUSEDATE)) {
					ELF447N_STATUS_DT = ELF447N_NUSEDATE;
				}
				ELF447N_NSIGN_CODE = "";
				ELF447N_NSIGN_MEMO = "";
			} else if (UtilConstants.NoUseCase.NuseMemo.未簽約一暫不訂約
					.equals(ELF447N_STATUS)) {
				if (Util.isNotEmpty(ELF447N_NUSEDATE)) {
					ELF447N_STATUS_DT = ELF447N_NUSEDATE;
				}
			} else if (UtilConstants.NoUseCase.NuseMemo.未簽約一溝通中
					.equals(ELF447N_STATUS)) {
				if (Util.isNotEmpty(ELF447N_NUSEDATE)) {
					ELF447N_STATUS_DT = ELF447N_NUSEDATE;
				}
			} else if (UtilConstants.NoUseCase.NuseMemo.未簽約
					.equals(ELF447N_STATUS)) {
				ELF447N_STATUS = "2";
				ELF447N_STATUS_DT = l120m01aApproveTime;
				ELF447N_NSIGN_CODE = "";
				ELF447N_NSIGN_MEMO = "";
			} else {
				// 一般案件
				ELF447N_STATUS = "2";
				ELF447N_STATUS_DT = l120m01aApproveTime;
				ELF447N_NUSEDATE = "0001-01-01";
				ELF447N_NUSEMEMO = "";
				ELF447N_NSIGN_CODE = "";
				ELF447N_NSIGN_MEMO = "";
			}

			// J-111-0551_05097_B1003 Web
			// e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
			int result = misELF447nService.updateByUnidAndContract(
					ELF447N_STATUS, ELF447N_STATUS_DT, ELF447N_NUSEDATE,
					ELF447N_NUSEMEMO, ELF447N_NSIGN_CODE, ELF447N_NSIGN_MEMO,
					ELF447N_UNID, ELF447N_CONTRACT, ELF447N_CURAMT_S,
					ELF447N_CURAMT_N);
			// 2013/07/08,Rex,當沒有更新到資料，則新增一筆到447n之中
			if (result == 0) {
				// String srcMainId = Util.trim(l230s01a.getSrcMainId());
				// L140M01A l140m01a = l140m01aDao.findByMainId(srcMainId);
				if (l140m01a != null) {
					String ELF447N_PROJECT_NO = Util.trimSizeInOS390(
							LMSUtil.getUploadCaseNo(l120m01a), 40);
					String ELF447N_CUSTID = l140m01a.getCustId();
					String ELF447N_DUPNO = l140m01a.getDupNo();
					String ELF447N_CLASS = "1";
					String ELF447N_BRANCH = l120m01a.getCaseBrId();
					String ELF447N_PROCESS_BR = l120m01a.getCaseBrId();
					String ELF447N_FACT_TYPE = Util.trim(l140m01a.getSnoKind());
					String ELF447N_SYSTYPE = l120m01a.getDocType();
					String ELF447N_CASELEVEL = Util.trimSizeInOS390(
							l120m01a.getCaseLvl(), 1);
					String tproperty = "999";
					for (String x : l140m01a.getProPerty().split(
							UtilConstants.Mark.SPILT_MARK)) {
						if (UtilConstants.Cntrdoc.Property.新做.equals(x)
								|| UtilConstants.Cntrdoc.Property.續約.equals(x)
								|| UtilConstants.Cntrdoc.Property.增額.equals(x)) {

							if (Integer.valueOf(x) < Integer.valueOf(tproperty)) {
								tproperty = x;
							}
						}
					}
					if ("999".equals(tproperty)) {
						for (String x : l140m01a.getProPerty().split(
								UtilConstants.Mark.SPILT_MARK)) {
							if (Integer.valueOf(x) < Integer.valueOf(tproperty)) {
								tproperty = x;
							}
						}

					}
					String ELF447N_PROPERTY = tproperty;
					BigDecimal ELF447N_CURAMT = l140m01a.getCurrentApplyAmt();
					String ELF447N_CURR = l140m01a.getCurrentApplyCurr(); // CHAR(3)
					// NULL,現請額度幣別
					BigDecimal ELF447N_OLDAMT = l140m01a.getLV2Amt(); // DECIMAL(15,
																		// 2)
					// NULL,前請額度金額
					String ELF447N_OLDCURR = l140m01a.getLV2Curr(); // CHAR(3)
																	// NOT
					// NULL,前請額度幣別
					/**
					 * 有前准批覆額度 優先當沒有值 再來抓前准額度
					 */

					if (ELF447N_OLDAMT == null) {
						if (l140m01a.getLVAmt() != null) {
							ELF447N_OLDAMT = l140m01a.getLVAmt();
							ELF447N_OLDCURR = l140m01a.getLVCurr();
						} else {
							ELF447N_OLDAMT = BigDecimal.ZERO;
							ELF447N_OLDCURR = "";
						}
					}

					String ELF447N_GRPNO = "";
					String ELF447N_RISK_CNTRY = "";
					// 風險國別
					if (Util.isEmpty(l140m01a.getRiskArea())) {
						ELF447N_RISK_CNTRY = "TW";
					} else {
						ELF447N_RISK_CNTRY = l140m01a.getRiskArea();
					}

					String ELF447N_RISK_AREA = "";
					String ELF447N_BUS_CD = "";
					String ELF447N_BUS_SUB_CD = "";
					String ELF447N_BUILD_NAME = "";
					String ELF447N_SITE1 = "";
					String ELF447N_SITE2 = "";
					String ELF447N_SITE3 = "";
					String ELF447N_SITE4 = "";
					String ELF447N_INT_MEMO = "";
					String ELF447N_RESIDENce = "";

					BigDecimal ELF447N_LAND_AREA = BigDecimal.ZERO; // ' 土地面積 ',
					Date ELF447N_BUILD_DATE = CapDate.getDate("0001/01/01",
							"yyyy/MM/dd");
					BigDecimal ELF447N_WAIT_MONTH = BigDecimal.ZERO; // '
																		// 預計撥款至動工期間月數
																		// ',
					String ELF447N_LOCATE_CD = ""; // ' 擔保品座落區 ',
					BigDecimal ELF447N_SITE3NO = BigDecimal.ZERO; // ' 座落區段 ',
					String ELF447N_SITE4NO = ""; // ' 座落區村里 ',
					String ELF447N_LAND_TYPE = ""; // ' 土地使用分區 ');

					L140M01M l140m01m = l140m01mDao.findByMainId(Util
							.trim(l140m01a.getMainId()));

					if (l140m01m != null) {
						ELF447N_LAND_AREA = (Util.isNotEmpty(l140m01m
								.getAreaLand()) ? l140m01m.getAreaLand()
								: BigDecimal.ZERO); // ' 土地面積 ',

						ELF447N_BUILD_DATE = (Util.isEmpty(l140m01m
								.getBuildDate()) ? CapDate.getDate(
								"0001/01/01", "yyyy/MM/dd") : l140m01m
								.getBuildDate());// 預計取得建照日期(為空值時設定為0001/01/01)

						ELF447N_WAIT_MONTH = (Util.isNotEmpty(l140m01m
								.getWaitMonth()) ? l140m01m.getWaitMonth()
								: BigDecimal.ZERO); // ' 預計撥款至動工期間月數 ',
						ELF447N_LOCATE_CD = (Util.isNotEmpty(l140m01m
								.getLocationCd()) ? Util.trim(l140m01m
								.getLocationCd()) : ""); // ' 擔保品座落區 ',
						ELF447N_SITE3NO = (Util.isNotEmpty(l140m01m
								.getSite3No()) ? l140m01m.getSite3No()
								: BigDecimal.ZERO); // ' 座落區段 ',
						ELF447N_SITE4NO = (Util.isNotEmpty(l140m01m
								.getSite4No()) ? Util.trim(l140m01m
								.getSite4No()) : ""); // ' 座落區村里 ',
						ELF447N_LAND_TYPE = (Util.isNotEmpty(l140m01m
								.getLandType()) ? Util.trim(l140m01m
								.getLandType()) : ""); // ' 土地使用分區 ');
					}

					result = 1;
					Timestamp ELF447N_TMESTAMP = CapDate.getCurrentTimestamp();

					// 產品
					String ELF447N_PROD_CLASS = "";
					ELF447N_PROD_CLASS = Util.trim(l140m01a.getLnType());
					if (Util.equals(ELF447N_PROD_CLASS, "00")) {
						ELF447N_PROD_CLASS = "";
					}

					String ELF447N_PROPERTIES = Util.trim(l140m01a
							.getProPerty());

					// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
					// 實地覆審分行
					String ELF447N_REVIEWBR = "";
					L120S01B l120s01b = l120s01bDao.findByUniqueKey(
							meta.getMainId(), l140m01a.getCustId(),
							l140m01a.getDupNo());
					if (l120s01b != null) {
						ELF447N_REVIEWBR = Util.trim(l120s01b.getReviewBrNo());
					}

					// J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
					String ELF447N_ISHEDGE = "";
					BigDecimal ELF447N_ENHANCEAMT = BigDecimal.ZERO;
					ELF447N_ISHEDGE = Util.trim(lmsService
							.getDerivateSubjectHedgeKinde(l140m01a));
					if (Util.equals(ELF447N_ISHEDGE, "2")) {
						ELF447N_ENHANCEAMT = l140m01a.getEnhanceAmt() == null ? BigDecimal.ZERO
								: l140m01a.getEnhanceAmt();
					}

					// J-107-0357_05097_B1001 Web
					// e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
					String ELF447N_PROJ_CLASS = Util.trim(l140m01a
							.getProjClass());
					if (Util.notEquals(Util.trim(l140m01a.getIsStartUp()), "N")) {
						if (Util.equals(Util.trim(ELF447N_PROJ_CLASS), "05")) {
							ELF447N_PROJ_CLASS = "";
						}
					}
					if (Util.equals(ELF447N_PROJ_CLASS, "00")) {
						ELF447N_PROJ_CLASS = "";
					}

					// J-108-0210_05097_B1001 Web e-Loan企金授信新增上傳借款人基本資料的實收資本額
					String ELF447N_ENDDATE = l120m01a.getEndDate() == null ? null
							: CapDate.formatDate(l120m01a.getEndDate(),
									"yyyy-MM-dd");
					String ELF447N_RGTCURR = "";
					BigDecimal ELF447N_RGTAMT = BigDecimal.ZERO;
					BigDecimal ELF447N_RGTUNIT = BigDecimal.ONE; // 沒有值DEFAULT
																	// 也要是1

					// J-108-0210_05097_B1001 Web e-Loan企金授信新增上傳借款人基本資料的實收資本額
					if (l120s01b != null) {
						ELF447N_RGTCURR = Util.trim(l120s01b.getCptlCurr());
						ELF447N_RGTAMT = Util.parseBigDecimal(l120s01b
								.getCptlAmt() == null ? 0 : l120s01b
								.getCptlAmt());
						ELF447N_RGTUNIT = Util.parseBigDecimal(l120s01b
								.getCptlUnit() == null ? 1 : l120s01b
								.getCptlUnit());

						if (ELF447N_RGTUNIT == null
								|| BigDecimal.ZERO
										.compareTo(Util
												.parseBigDecimal(l120s01b
														.getCptlUnit())) == 0) {
							ELF447N_RGTUNIT = BigDecimal.ONE;
						}
					}

					misELF447nService.insertByL2305FLow(ELF447N_UNID,
							ELF447N_PROJECT_NO, ELF447N_CLASS, ELF447N_CUSTID,
							ELF447N_DUPNO, ELF447N_STATUS, ELF447N_STATUS_DT,
							ELF447N_CONTRACT, ELF447N_BRANCH,
							ELF447N_PROCESS_BR, ELF447N_FACT_TYPE,
							ELF447N_SYSTYPE, ELF447N_CASELEVEL,
							ELF447N_PROPERTY, ELF447N_CURAMT, ELF447N_CURR,
							ELF447N_OLDAMT, ELF447N_OLDCURR, ELF447N_GRPNO,
							ELF447N_RISK_CNTRY, ELF447N_RISK_AREA,
							ELF447N_BUS_CD, ELF447N_BUS_SUB_CD,
							ELF447N_BUILD_NAME, ELF447N_SITE1, ELF447N_SITE2,
							ELF447N_SITE3, ELF447N_SITE4, ELF447N_NUSEDATE,
							ELF447N_NUSEMEMO, ELF447N_TMESTAMP,
							ELF447N_INT_MEMO, ELF447N_RESIDENce,
							ELF447N_NSIGN_CODE, ELF447N_NSIGN_MEMO,
							ELF447N_PROD_CLASS, ELF447N_LAND_AREA,
							ELF447N_BUILD_DATE, ELF447N_WAIT_MONTH,
							ELF447N_LOCATE_CD, ELF447N_SITE3NO,
							ELF447N_SITE4NO, ELF447N_LAND_TYPE,
							ELF447N_PROPERTIES, ELF447N_REVIEWBR,
							ELF447N_ISHEDGE, ELF447N_ENHANCEAMT,
							ELF447N_PROJ_CLASS, ELF447N_ENDDATE,
							ELF447N_RGTCURR, ELF447N_RGTAMT, ELF447N_RGTUNIT,
							ELF447N_CURAMT_S, ELF447N_CURAMT_N);
				} else {
					logger.error("[updateElf447n] l140m01a == null,srcMainId==>"
							+ srcMainId);
				}

			}
			updateRowCount += result;
			totalData++;
		}
		// 如果更新的筆數與額度明細表除了性質為不變的筆數不相等
		if (totalData != updateRowCount) {
			String error = MessageFormat
					.format("update ELF447N total Need Update=[{0}],updateRowCount=[{1}] is not equal",
							new Object[] { totalData, updateRowCount });
			logger.error(error);
			HashMap<String, String> extraMessage = new HashMap<String, String>();
			// EFD0025 = 執行有誤$\{msg\}
			extraMessage.put("msg", error);
			FlowMessageException msg = new FlowMessageException(
					UtilConstants.AJAX_RSP_MSG.執行有誤);
			msg.setExtraMessage(extraMessage);
			throw msg;

		} else {
			logger.info("update ELF447N totalData=[{0}]",
					new Object[] { totalData });
		}
		logger.info("=====================[updateElf447n END]====================================");
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L230M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}

	/**
	 * 更新Elf503 J-109-0202_05097_B1001 Web e-Loan利費率資料提前至授信案件經核定後逕行寫入
	 * 
	 * @param meta
	 *            簽約未動用主檔
	 */
	private void updateElf503(L230M01A meta) {
		logger.info("=====================[updateElf503]====================================");
		List<L230S01A> l230s01as = l230s01aDao.findByMainId(meta.getMainId());
		// 文件狀態 0- 預約 ( 預約到期日內有效 ) 1- 報核 ( 報核日起三個月有效 ) 2- 已核定 ( 核定日起六個月有效 )A-
		// 已撤銷B- 已婉卻C- 已退件
		String ELF447N_STATUS = "";

		// 簽報書的mainId
		String ELF447N_UNID = meta.getUid();

		String ELF447N_CONTRACT = "";

		L120M01A l120m01a = l120m01aDao.findByMainId(meta.getUid());

		List<Object[]> elf503DeleteList = new ArrayList<Object[]>();

		String documentno = LMSUtil.getUploadCaseNo(l120m01a);
		for (L230S01A l230s01a : l230s01as) {

			ELF447N_STATUS = l230s01a.getNuseMemo();
			ELF447N_CONTRACT = l230s01a.getCntrNo();

			if (UtilConstants.NoUseCase.NuseMemo.不簽約註銷額度.equals(ELF447N_STATUS)) {
				String srcMainId = Util.trim(l230s01a.getSrcMainId());

				L140M01A l140m01a = l140m01aDao.findByMainId(srcMainId);
				if (l140m01a != null) {

					elf503DeleteList.add(new Object[] { l140m01a.getCustId(),
							l140m01a.getDupNo(), l140m01a.getCntrNo(),
							documentno });

				}
			}

		}

		logger.info("{}=======>{}", "Start", "misELF503Service.insert|");
		misELF503Service.deleteByDocumentNo(elf503DeleteList);

		logger.info("=====================[updateElf503 END]====================================");
	}
}