package com.mega.eloan.lms.lms.pages;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.AbstractFileDownloadPage;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.common.Util;

/**
 * 提供notes直接下載pdf
 * 
 * security.xml要增加設定
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/simple/FileProcessingPageForFullSearchLMS")
public class FileProcessingPageForFullSearchLms extends
		AbstractFileDownloadPage {

	@Autowired
	L120M01ADao l120m01aDao;

	@Autowired
	LMS1205Service service1205;

	@Autowired
	SysParameterService sysParameterService;

	@Resource
	private HttpServletRequest request;

	public FileProcessingPageForFullSearchLms() {

		super();

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * tw.com.iisi.cap.base.pages.AbstractCapPage#execute(org.apache.wicket.
	 * PageParameters)
	 */
	@Override
	public void execute(ModelMap model, PageParameters params) {
		this.fileDownloadName = params.getString("fileDownloadName");
		this.serviceName = params.getString("serviceName");

		// 原始呼叫完整URL
		// http://**************:9081/lms-web/app/simple/FileProcessingPageForNotes?random=0.7614665330930178&fileDownloadName=LMS1205R01.pdf&serviceName=lms1201r01rptservice&rptOid=R01^2EF2A6BDBAC946FEA9E19CDD4669B973^^^^|R12^8A604F8012AA11EB9B9D046CC0A89955^73251209^0^005110900630^|R29^8A604F8012AA11EB9B9D046CC0A89955^73251209^0^005110900630^|R14^2EF2A6BDBAC946FEA9E19CDD4669B973^^^^|R32^^^^^|R33^^^^^|R36^^^^^|R39^^^^^&mainId=4a292a290b2c473889bb643562b80db0

		// FOR 全文檢索簡化後URL
		// http://**************:9081/lms-web/app/simple/FileProcessingPageForFullSearch?random=0.7614665330930181&mainId=4a292a290b2c473889bb643562b80db0

		// FOR 全文檢索簡化後URL
		// http://**************:9081/lms-web/app/simple/FileProcessingPageForFullSearchLNS?random=0.7614665330930181&mainId=4a292a290b2c473889bb643562b80db0

		MegaSSOUserDetails users = MegaSSOSecurityContext.getUserDetails();

		// UPGRADE: 待確認，RemoteHost是否正確
		// ((WebRequest)
		// getRequestCycle().getRequest()).getHttpServletRequest().getRemoteHost();
		String remoteHost = request.getRemoteHost();

		String ipList = this.sysParameterService
				.getParamValue("SYS_FULLSEARCH_ALLOW_IP_LIST");
		LOGGER.info("[isVaildIP]IP_LIST=[{}] SRC_IP=[{}] ", ipList, remoteHost);
		if (ipList.indexOf(remoteHost) == -1) {
			throw new RuntimeException("使用者IP錯誤");
		}

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);

		if (l120m01a != null) {

			List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();

			if (UtilConstants.Casedoc.DocCode.異常通報
					.equals(l120m01a.getDocCode())) {

				List<Map<String, Object>> beanListJ = service1205
						.getBorrowsInner(mainId, "J");
				if (beanListJ != null && !beanListJ.isEmpty()) {
					beanList.addAll(beanListJ);
					// beanList.addAll(beanList.size(), beanListJ);
				}

			} else {
				beanList = service1205.getBorrowsInner(mainId, "A");

				// 額度批覆書
				String printH = "";
				if (UtilConstants.Casedoc.DocKind.授權外.equals(l120m01a
						.getDocKind())
						&& (CreditDocStatusEnum.泰國_提會待登錄.getCode().equals(
								l120m01a.getDocStatus())
								|| CreditDocStatusEnum.泰國_提會待覆核.getCode()
										.equals(l120m01a.getDocStatus())
								|| CreditDocStatusEnum.海外_已核准.getCode().equals(
										l120m01a.getDocStatus()) || CreditDocStatusEnum.海外_婉卻
								.getCode().equals(l120m01a.getDocStatus()))
						&& !UtilConstants.Casedoc.DocCode.陳復陳述案.equals(l120m01a
								.getDocCode())
						&& !UtilConstants.Casedoc.DocCode.異常通報.equals(l120m01a
								.getDocCode())) {
					printH = UtilConstants.DEFAULT.是;
				} else if (UtilConstants.Casedoc.DocKind.授權內.equals(l120m01a
						.getDocKind())
						&& UtilConstants.Casedoc.AuthLvl.營運中心授權內
								.equals(l120m01a.getAuthLvl())) {
					if ((CreditDocStatusEnum.海外_已核准.getCode().equals(
							l120m01a.getDocStatus()) || CreditDocStatusEnum.海外_婉卻
							.getCode().equals(l120m01a.getDocStatus()))
							&& !UtilConstants.Casedoc.DocCode.陳復陳述案
									.equals(l120m01a.getDocCode())
							&& !UtilConstants.Casedoc.DocCode.異常通報
									.equals(l120m01a.getDocCode())) {
						printH = UtilConstants.DEFAULT.是;
					}
				}

				printH = UtilConstants.DEFAULT.是; // FOR TEST

				if (Util.equals(printH, UtilConstants.DEFAULT.是)) {
					List<Map<String, Object>> beanListH = new ArrayList<Map<String, Object>>();
					beanListH = service1205.getBorrowsInner(mainId, "H");
					if (beanListH != null && !beanListH.isEmpty()) {
						for (Map<String, Object> beanMapH : beanListH) {

							// 為了要讓額度批覆書與額度檢核表印在最後面
							String rpt = Util.trim(MapUtils.getObject(beanMapH,
									"rpt"));
							if (Util.equals(rpt, "R13")
									|| Util.equals(rpt, "R29")) {
								beanMapH.put("rpt", rpt + "FS");
							}

						}
						beanList.addAll(beanListH);

						// beanList.addAll(beanList.size(), beanListH);
					}

				}
			}

			// if (!params.containsKey("rptOid")) {
			// String rptOid =
			// "R01^2EF2A6BDBAC946FEA9E19CDD4669B973^^^^|R12^8A604F8012AA11EB9B9D046CC0A89955^73251209^0^005110900630^|R29^8A604F8012AA11EB9B9D046CC0A89955^73251209^0^005110900630^|R14^2EF2A6BDBAC946FEA9E19CDD4669B973^^^^|R32^^^^^|R33^^^^^|R36^^^^^|R39^^^^^";
			// params.add("rptOid", rptOid);
			// }

			StringBuffer newRptOid = new StringBuffer("");
			if (beanList != null && !beanList.isEmpty()) {
				for (Map<String, Object> beanMap : beanList) {
					String content = Util.trim(MapUtils.getString(beanMap,
							"rpt", ""))
							+ "^"
							+ Util.trim(MapUtils.getString(beanMap, "oid", ""))
							+ "^"
							+ Util.trim(MapUtils.getString(beanMap, "custId",
									""))
							+ "^"
							+ Util.trim(MapUtils
									.getString(beanMap, "dupNo", ""))
							+ "^"
							+ Util.trim(MapUtils.getString(beanMap, "cntrNo",
									""))
							+ "^"
							+ Util.trim(MapUtils.getString(beanMap,
									"refMainId", "")) + "|";
					newRptOid.append(content);
				}
			}

			String rptOid = Util.trim(newRptOid.toString());

			if (Util.notEquals(rptOid, "")) {
				rptOid = rptOid.substring(0, StringUtils.length(rptOid) - 1);
			}

			params.add("rptOid", rptOid);

			if (!params.containsKey("serviceName")) {
				String tserviceName = "";
				if (Util.equals(l120m01a.getDocType(),
						UtilConstants.Casedoc.DocType.企金)) {
					if (Util.equals(l120m01a.getTypCd(),
							UtilConstants.Casedoc.typCd.海外)) {
						tserviceName = "lms1205r01rptservice";
					} else {
						tserviceName = "lms1201r01rptservice";
					}
				} else {
					// 個金
				}

				this.serviceName = tserviceName;
				params.add("serviceName", tserviceName);
			}
		}

		if (!params.containsKey("fileDownloadName")) {
			String tfileDownloadName = "LMS1205R01.pdf";
			this.fileDownloadName = tfileDownloadName;
			params.add("fileDownloadName", tfileDownloadName);
		}

		// getRequestCycle().setRequestTarget(new ResourceStreamRequestTarget(new
		// FileDownloadStreamWriter(params)));

	}

	@Override
	public String getDownloadFileName() {
		return this.fileDownloadName;
	}

	@Override
	public String getFileDownloadServiceName() {
		return this.serviceName;
	}

	// UPGRADE: CES也是註解掉AbstractResourceStreamWriter的段落，待確認是否會影響報表產出功能
//	final class FileDownloadStreamWriter extends AbstractResourceStreamWriter {
//
//		private static final long serialVersionUID = 1L;
//		private PageParameters params;
//
//		/**
//		 * constructor
//		 * 
//		 * @param params
//		 *            PageParameters
//		 */
//		public FileDownloadStreamWriter(PageParameters params) {
//			super();
//			this.params = params;
//		}
//
//		/*
//		 * (non-Javadoc)
//		 * 
//		 * @see
//		 * org.apache.wicket.util.resource.IResourceStreamWriter#write(java.
//		 * io.OutputStream)
//		 */
//		public void write(OutputStream output) {
//			try {
//				output.write(getContent(params));
//				output.flush();
//			} catch (Exception ex) {
//				LOGGER.error(ex.getMessage(), ex);
//				throw new RuntimeException(ex);
//			}
//		}
//
//		/*
//		 * (non-Javadoc)
//		 * 
//		 * @see org.apache.wicket.util.resource.IResourceStream#getContentType()
//		 */
//		public String getContentType() {
//			return getFileContentType();
//		}
//
//	}

	@Override
	protected String getViewName() {
		// TODO Auto-generated method stub
		return null;
	}
}
