/* 
 *MisELF502Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;

import com.mega.eloan.lms.mfaloan.bean.ELF502;

/**
 * <pre>
 * 消金額度介面檔 MIS.ELF502
 * </pre>
 * 
 * @since 2013/1/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/17,REX,new
 *          </ul>
 */
public interface MisELF502Service {

	/**
	 * 查詢額度相關資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @param seq
	 *            產品序號
	 * @return
	 */
	public List<ELF502> findByKey(String custId, String dupNo, String cntrNo,
			int seq);
}
