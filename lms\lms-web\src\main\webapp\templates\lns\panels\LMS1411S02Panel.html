<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
		<th:block th:fragment="panelFragmentBody">
    		<script type="text/javascript">
				loadScript('pagejs/lns/LMS1401S02Panel');
    		</script>
			<fieldset>
				<legend><b><th:block th:text="#{'L140M01a.message48'}"><!--額度明細表--></th:block></b></legend>
				<span class="text-red">
					<th:block th:text="#{'L140M01a.message01'}"><!--請依額度明細表【性質】依下列順序來設定列印順序(由左到右代表列印順序由前到後)--></th:block>：
					<br>
					【<th:block th:text="#{'L140M01a.message02'}"><!--報價、新做、增額、紓困、協議清償、減額、變更條件、續約、提前續約、展期、流用、取消、不變--></th:block>】
					<br>
					<th:block th:text="#{'L140M01a.message72'}"><!--L140M01a.message72=檢核欄V為通過檢核且經過計算，O為 通過檢核 但尚未計算，X為尚未通過檢核--></th:block>
				</span>
                <div id="gridviewC_2"></div>
			</fieldset>
            <div id="borrowvalue" style="display:none;">
            	<table border="0" cellpadding="0" cellspacing="0">
            		<tr>
            			<td><th:block th:text="#{'tab.tit01'}"><!--請選擇此額度明細表之借款人--></th:block>&nbsp;</td>
            		</tr>
            	</table>
                <div id="gridviewborrow"></div>
            </div>
            <div id="transvalue" style="display:none;">
            	<table border="0" cellpadding="0" cellspacing="0">
            		<tr>
            			<td><th:block th:text="#{'L140M01a.message05'}"><!--請選擇1份欲轉入之聯行案件--></th:block>&nbsp;</td>
            		</tr>
            	</table>
                <div id="gridviewtrans"></div>
            </div>
            <div id="copyvalue" style="display:none;">
            	<table border="0" cellpadding="0" cellspacing="0">
            		<tr>
            			<td>
            				<th:block th:text="#{'L140M01a.message06'}"><!--請選擇欲列出之額度明細表資料範圍--></th:block>&nbsp;
            				<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            				<label>
            					<input type="radio" name="copyValueRadio" value="CopyNow" onclick="$('#now').show();$('#other').hide()" checked></input>
								<th:block th:text="#{'L140M01a.message07'}"><!--列出本案借戶項下所有額度明細表--></th:block><br>
            				</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            				<label>
            					<input type="radio" name="copyValueRadio" value="CopyOther" onclick="$('#other').show();$('#now').hide()"></input>
            					<th:block th:text="#{'L140M01a.message08'}"><!--列出特定借戶項下所有額度明細表--></th:block><br>
            				</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            				<label>
            					<input type="radio" name="copyValueRadio" value="CopyAll" onclick="$('#other').hide();$('#now').hide()"></input>
            					<th:block th:text="#{'L140M01a.message09'}"><!--列出所有額度明細表--></th:block>
            				</label>
            			</td>
            		</tr>
            	</table>
            	<span id="now">
            		<th:block th:text="#{'L140M01a.message09'}"><!--列出所有額度明細表--></th:block><br>
            		<select id="nowSelectPerson"></select>
            	</span>
            	<form id="otherTextform" name="otherTextform">
            		<span id="other" style="display:none">
            			<th:block th:text="#{'L140M01a.message11'}"><!--請輸入複製來源之借款人統一編號(加上重覆序號)--></th:block>:
            			<input type="text" id="otherTextid" size="11" maxlength="11"></input>
            		</span>
            	</form>
            </div>
            <div id="copyvalueNowBox" style="display:none;">
            	<span><b><th:block th:text="#{'L140M01a.message12'}"><!--此份額度明細表的「借款人」欄位要設定為何人--></th:block></b></span>
            	<br>
            	<select id="theCopyPerson"></select>
            	<br><br>
            	<table border="0" cellpadding="0" cellspacing="0">
            		<tr>
            			<td>
            				<b>
            					<th:block th:text="#{'L140M01a.message13'}"><!--資料來源--></th:block>：<span id="dataFrom"></span>，
            					<th:block th:text="#{'L140M01a.message14'}"><!--請選擇欲複製的額度明細表(可複選)--></th:block>：
            				</b>
            			</td>
            		</tr>
            	</table>
            </div>
            <div id="opendocBox" style="display:none">
            	<div id="loadPanel" open="true"></div>
            </div>
            <div id="printview" style="display:none;">
            	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
            		<tr>
            			<td class="hd1">
            				<th:block th:text="#{'L140M01a.message15'}"><!--注意事項--></th:block>&nbsp;&nbsp;
            			</td>
            			<td>
            				<span class="text-red">(1)<th:block th:text="#{'L140M01a.message16'}"><!--注意事項--></th:block>。</span>
            			</td>
            		</tr>
            		<tr>
            			<td class="hd1">
            				<th:block th:text="#{'L140M01a.message17'}"><!--注意事項--></th:block>&nbsp;&nbsp;
            			</td>
            			<td>
            				<span class="text-red">(1)<th:block th:text="#{'L140M01a.message18'}"><!--設定各額度明細表之列印順序--></th:block>。</span>
            				<br>
            				<span class="text-red">(2)<th:block th:text="#{'L140M01a.message19'}"><!--執行【寫回額度明細表】回寫所設定之列印順序至各額度明細表中--></th:block>。</span>
            				<br>
            				<span class="text-red">(3)<th:block th:text="#{'L140M01a.message20'}"><!--回到簽報書執行列印--></th:block>。</span>
            			</td>
            		</tr>
            	</table>
                <div id="gridviewprint"></div>
            </div>
            <!-- 特殊登錄案件 -->
            <div id="special" class="content" style="display:none;">
            	<form id="L782M01AForm" name="L782M01AForm">
            		<fieldset>
                        <legend>
                            <strong><th:block th:text="#{'doc.baseInfo'}"><!--基本資訊--></th:block></strong>
                        </legend>
                        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        	<tbody>
                        		<tr>
									<td width="20%" class="hd1">
										<th:block th:text="#{'doc.branchName'}"><!--分行名稱--></th:block>&nbsp;&nbsp;
									</td>
									<td width="30%">
										<input id ="branchId" name="branchId" style="display:none"></input>
										<span id ="branchName"></span>
									</td>
									<td width="20%" class="hd1">
										<th:block th:text="#{'L782M01A.dispatchDate'}"><!--發文日期--></th:block>&nbsp;&nbsp;
									</td>
									<td width="30%">
										<input type='text' id='dispatchDate' name='dispatchDate' class='date required' size='8'></input>
									</td>
                        		</tr>
                        		<tr>
                        			<td class="hd1">
                        				<th:block th:text="#{'L782M01A.custName'}"><!--客戶名稱--></th:block>&nbsp;&nbsp;
                        			</td>
                        			<td>
                        				<span id="specialCustId"></span><th:block th:text="#{'L782M01A.dupNo'}"><!--重覆序號--></th:block>：<span id="specialDupNo"></span>
                        				<br>
                        				(<span class="color-red">DBU</span>)<span id="specialCustName"></span>
                        			</td>
                        			<td class="hd1">
                        				<th:block th:text="#{'L782M01A.loanTP'}"><!--科目--></th:block>&nbsp;&nbsp;
                        			</td>
                        			<td valign="middle">
                        				<select id ="SplieloanTP" name="SplieloanTP" class="required"></select>
                        			</td>
                        		</tr>
                        		<tr>
                        			<td class="hd1">
                        				<th:block th:text="#{'L782M01A.applyAmt'}"><!--額度--></th:block>&nbsp;&nbsp;
                        			</td>
                        			<td width="40%">
                        				<select id="applyCurr" name="applyCurr" class="money required"></select>
                        				<!-- 幣別 -->
                        				<input type='text' id='applyAmt' name='applyAmt' size="18" maxlength="22" integer="13" fraction="2" class="numeric required"></input>
                        			</td>
                        			<td class="hd1">
                        				<th:block th:text="#{'L782M01A.caseType'}"><!--歸類--></th:block>&nbsp;&nbsp;
                        			</td>
                        			<td>
										<!--<select id="caseType" name="caseType" combokey="lms1405m01_SpecialCaseType" />-->
										<select id="caseType" name="caseType" class="required"></select>
                        			</td>
                        		</tr>
                        		<tr>
                        			<td class="hd1">
                        				<th:block th:text="#{'L782M01A.inteRate'}"><!--利費率/其他--></th:block>&nbsp;&nbsp;
                        			</td>
                        			<td colspan="3">
                        				<textarea id="inteRate" name="inteRate" cols="60" maxlength="900" maxlengthC="300"></textarea>
                        			</td>
                        		</tr>
                        		<tr>
                        			<td class="hd1">
                        				<th:block th:text="#{'L782M01A.disp1'}"><!--備註說明--></th:block>&nbsp;&nbsp;
                        			</td>
                        			<td colspan="3">
                        				<textarea id="disp1" name="disp1" cols="60" maxlength="900" maxlengthC="300"></textarea>
                        			</td>
                        		</tr>
                        		<tr>
                        			<td class="hd1">
                        				<th:block th:text="#{'L140M01a.creator'}"><!--建立人員--></th:block>&nbsp;&nbsp;
                        			</td>
                        			<td>
                        				<span id="SplieCreator"></span>
                        			</td>
                        			<td class="hd1">
                        				<th:block th:text="#{'L140M01a.updater'}"><!--最後異動者--></th:block>&nbsp;&nbsp;
                        			</td>
                        			<td>
                        				<span id="SplieUpdater"></span>
                        			</td>
                        		</tr>
                        	</tbody>
                        </table>
            		</fieldset>
            	</form>
            </div>
            <!-- close 特殊登錄案件 -->
			<!--
			因為改成另外一個頁面所以註解掉
			<script type="text/javascript" src="pagejs/lns/LMS1401S02Panel02.js"></script>
			<script type="text/javascript" src="pagejs/lns/LMS1401S02Panel03.js"></script>
			<script type="text/javascript" src="pagejs/lns/LMS1401S02Panel04.js"></script>
			<script type="text/javascript" src="pagejs/lns/LMS1401S02Panel05.js"></script>
			<script type="text/javascript" src="pagejs/lns/LMS1401S02Panel06.js"></script>
			-->
		</th:block>
    </body>
</html>
