package com.mega.eloan.lms.las.report.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.text.DecimalFormat;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.las.report.LMS1925R01RptService;
import com.mega.eloan.lms.las.service.LMS1925Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192M01C;
import com.mega.eloan.lms.model.L192S01A;
import com.mega.eloan.lms.model.L192S02A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * 授信業務工作底稿基本內容列印
 * 
 * <AUTHOR>
 * 
 */
@Service("lms1925r01rptservice")
public class LMS1925R01RptServiceImpl extends AbstractReportService implements
		LMS1925R01RptService {

	@Resource
	LMS1925Service lms1925Service;

	@Resource
	BranchService branchService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	LMSService lmsService;
	
	@Override
	public String getReportTemplateFileName() {
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/las/LMS1925R01_" + locale.toString() + ".rpt";
	}

	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) {
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		setReport001(rptGenerator, mainOid);
	}

	/**
	 * 初始化map 資料，將所有的rportBean的資料初使化，避免少了而產生exception
	 * 
	 * @return
	 */
	private Map<String, String> reNewHashMapParams() {
		Map<String, String> values = new HashMap<String, String>();
		for (int i = 1; i <= 60; i++) {
			values.put("ReportBean.column" + String.format("%02d", i), "");
		}
		return values;
	}

	@Override
	public void setReport001(ReportGenerator rptGenerator, String mainOid) {

		String balDate = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		DecimalFormat df = new DecimalFormat("###,###,###,###,###,###,###,###.##");

		// String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = lms1925Service.getL192M01A(mainOid);
		
		String caseBrId = "";
		if (meta != null) {
			caseBrId = meta.getOwnBrId();
		} else {
			caseBrId = user.getUnitNo();
		}
		
		String logoPath = lmsService.getLogoShowPath(
				UtilConstants.RPTPicType.兆豐LOGO, "00", caseBrId);
		
		// ----------------------------------------------------填入reportBean資料
		Map<String, String> values = reNewHashMapParams();
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();

		values.put("ReportBean.column40", "section1");
		list.add(values);

		Set<L192M01B> l192m01bs = meta.getL192m01bs();
		List<L192M01B> main = new ArrayList<L192M01B>();
		List<L192M01B> notMain = new ArrayList<L192M01B>();

		for (L192M01B data : l192m01bs) {
			if ("1".equals(data.getCustType())) {
				main.add(data);
			} else if ("2".equals(data.getCustType())) {
				notMain.add(data);
			}
		}

		for (int i = 0; i < main.size(); i++) {
			L192M01B tmp = main.get(i);
			if (Util.equals(tmp.getCustId(), meta.getCustId())
					&& Util.equals(tmp.getDupNo(), meta.getDupNo())) {
				main.remove(i);
				main.add(0, tmp);
				break;
			}
		}

		// 先判斷借款人 連保人 誰的筆數比較多，
		int biggestCount = main.size() >= notMain.size() ? main.size()
				: notMain.size();
		values = reNewHashMapParams();

		values.put("ReportBean.column39", "first");

		for (int i = 0; i < biggestCount; i++) {
			values = values == null ? reNewHashMapParams() : values;
			values.put("ReportBean.column40", "part1");
			if (i < main.size()) {
				L192M01B l192m01b = main.get(i);
				values.put("ReportBean.column01", l192m01b.getCustId());
				values.put("ReportBean.column02", l192m01b.getCustName());
				values.put("ReportBean.column03",
						l192m01b.getPosi() == null ? "" : l192m01b.getPosi());

				values.put("ReportBean.column04",
						l192m01b.getIncomeAmt() == null ? "" : l192m01b
								.getIncomeAmt().toString());
			}

			if (i < notMain.size()) {
				L192M01B l192m01b = notMain.get(i);
				values.put("ReportBean.column05", l192m01b.getCustId());
				values.put("ReportBean.column06", l192m01b.getCustName());
				values.put("ReportBean.column07",
						l192m01b.getPosi() == null ? "" : l192m01b.getPosi());

				values.put("ReportBean.column08",
						l192m01b.getIncomeAmt() == null ? "" : l192m01b
								.getIncomeAmt().toString());
			}

			list.add(values);
			values = null;
		}

		// -------------------------------------------------------------------------------------------
		values = reNewHashMapParams();
		values.put("ReportBean.column40", "section2");
		list.add(values);
		// -----------------------------------組申請內容---------------------------------------------------
		Set<L192S01A> l192s01as = meta.getL192s01as();

		values = reNewHashMapParams();
		values.put("ReportBean.column39", "first");
		int l192s01aCount = 0;
		for (L192S01A l192s01a : l192s01as) {

			balDate = l192s01a.getBalDate() == null ? "" : sdf.format(l192s01a
					.getBalDate());

			l192s01aCount++;
			values = values == null ? reNewHashMapParams() : values;
			values.put("ReportBean.column50", String.valueOf(l192s01aCount));
			values.put("ReportBean.column40", "part2");
			values.put("ReportBean.column11",
					l192s01a.getSubject() == null ? "" : l192s01a.getSubject()); // 科目
			values.put("ReportBean.column12", l192s01a.getAccNo() == null ? ""
					: l192s01a.getAccNo()); // 帳號
			values.put(
					"ReportBean.column13",
					l192s01a.getQuotaCurr() == null ? "" : l192s01a
							.getQuotaCurr()); // 額度幣別
			values.put(
					"ReportBean.column14",
					l192s01a.getQuotaAmt() == null ? "" : df.format(l192s01a
							.getQuotaAmt()));// 額度金額

			values.put("ReportBean.column15",
					l192s01a.getBalCurr() == null ? "" : l192s01a.getBalCurr());// 餘額幣別
			values.put("ReportBean.column16", l192s01a.getBalAmt() == null ? ""
					: df.format(l192s01a.getBalAmt()));// 餘額金額

			values.put(
					"ReportBean.column17",
					l192s01a.getFromDate() == null ? "" : sdf.format(l192s01a
							.getFromDate())); // 動用起日

			values.put(
					"ReportBean.column18",
					l192s01a.getEndDate() == null ? "" : sdf.format(l192s01a
							.getEndDate())); // 動用迄日
			values.put("ReportBean.column19", l192s01a.getWay() == null ? ""
					: l192s01a.getWay()); // 計息方式

			values.put(
					"ReportBean.column20",
					l192s01a.getCheckDate() == null ? "" : sdf.format(l192s01a
							.getCheckDate())); // 發票日
			values.put(
					"ReportBean.column21",
					l192s01a.getCheckCurr() == null ? "" : l192s01a
							.getCheckCurr()); // 存執本票-幣別
			values.put(
					"ReportBean.column22",
					l192s01a.getCheckAmt() == null ? "" : df.format(l192s01a
							.getCheckAmt())); // 存執本票-金額
			values.put(
					"ReportBean.column23",
					l192s01a.getEndorser() == null ? "" : l192s01a
							.getEndorser()); // 存執本票-共同發票人/背書人

			list.add(values);
			values = null;
		}

		// -----------------------------------------擔保品--------------------------------------------------
		Set<L192S02A> l192s02as = meta.getL192s02as();
		values = reNewHashMapParams();
		values.put("ReportBean.column39", "first");

		int l192s02aCount = 0;
		for (L192S02A l192s02a : l192s02as) {
			l192s02aCount++;
			values = values == null ? reNewHashMapParams() : values;
			values.put("ReportBean.column50", String.valueOf(l192s02aCount));
			values.put("ReportBean.column40", "part3");
			values.put("ReportBean.column24",
					l192s02a.getGteName() == null ? "" : l192s02a.getGteName()); // 擔保品名稱
			values.put(
					"ReportBean.column25",
					l192s02a.getEstDate() == null ? "" : sdf.format(l192s02a
							.getEstDate())); // 鑑價日期
			values.put("ReportBean.column26", l192s02a.getOwner() == null ? ""
					: l192s02a.getOwner()); // 所有權人

			values.put("ReportBean.column27",
					l192s02a.getEstCurr() == null ? "" : l192s02a.getEstCurr()); // 估值幣別
			values.put("ReportBean.column28", l192s02a.getEstAmt() == null ? ""
					: df.format(l192s02a.getEstAmt()));// 估值金額

			values.put(
					"ReportBean.column29",
					l192s02a.getLoanCurr() == null ? "" : l192s02a
							.getLoanCurr()); // 押值幣別
			values.put(
					"ReportBean.column30",
					l192s02a.getLoanAmt() == null ? "" : df.format(l192s02a
							.getLoanAmt()));// 押值金額

			values.put("ReportBean.column31",
					l192s02a.getSetCurr() == null ? "" : l192s02a.getSetCurr()); // 設定金額(幣別)
			values.put(
					"ReportBean.column32",
					l192s02a.getSetAmt() == null ? "" : df.format(l192s02a
							.getSetAmt()));// 設定金額(金額)

			values.put("ReportBean.column33",
					l192s02a.getSetPosition() == null ? "" : l192s02a
							.getSetPosition().toString()); // 設定順位

			values.put(
					"ReportBean.column34",
					l192s02a.getSetDateFrom() == null ? "" : sdf
							.format(l192s02a.getSetDateFrom())); // 設定期限
			values.put(
					"ReportBean.column35",
					l192s02a.getSetDateEnd() == null ? "" : sdf.format(l192s02a
							.getSetDateEnd())); // 設定期限

			values.put(
					"ReportBean.column36",
					l192s02a.getInsurance() == null ? "" : l192s02a
							.getInsurance());// 險種/金額

			values.put(
					"ReportBean.column37",
					l192s02a.getInsDateFrom() == null ? "" : sdf
							.format(l192s02a.getInsDateFrom())); // 保費期限
			values.put(
					"ReportBean.column38",
					l192s02a.getInsDateEnd() == null ? "" : sdf.format(l192s02a
							.getInsDateEnd())); // 保費期限

			values.put(
					"ReportBean.column41",
					l192s02a.getInsPaper() == null ? "" : l192s02a
							.getInsPaper()); // 保費收據

			list.add(values);
			values = null;
		}

		// -------------------------------------------------------------------------------------------
		values = reNewHashMapParams();
		values.put("ReportBean.column40", "section3");
		list.add(values);
		// -------------------------------------------------------------------------------------------
		values = reNewHashMapParams();
		values.put("ReportBean.column40", "section4");
		list.add(values);
		// -------------------------------------------------------------------------------------------
		values = reNewHashMapParams();
		values.put("ReportBean.column40", "section5");
		list.add(values);

		rptGenerator.setRowsData(list);

		// ========================================================================填入參數欄位

		Map<String, String> prompts = new HashMap<String, String>();
		prompts.put("custId", meta.getCustId());
		prompts.put("custName", meta.getCustName());
		prompts.put("ownBrId", meta.getOwnBrId());
		prompts.put("ownBrName", branchService.getBranchName(meta.getOwnBrId()));
		prompts.put(
				"checkBase",
				meta.getCheckBase() == null ? "" : sdf.format(meta
						.getCheckBase()));
		prompts.put(
				"checkDate",
				meta.getCheckDate() == null ? "" : sdf.format(meta
						.getCheckDate()));
		prompts.put("tNo", meta.getTNo());
		prompts.put("wpNo", meta.getWpNo());
		prompts.put("checkMan", meta.getCheckMan());
		prompts.put("leader", meta.getLeader());
		prompts.put("mtDoc", meta.getMtDoc());
		prompts.put("tAddr", meta.getTAddr());
		prompts.put("tTel", meta.getTTel());
		prompts.put("cdQ1", meta.getCdQ1());
		prompts.put("cdQ2", meta.getCdQ2());
		prompts.put("estUnit", meta.getEstUnit());

		L192M01C l192m01c = meta.getL192m01c();
		prompts.put("ck1", l192m01c.getCk1());
		prompts.put("ck2", l192m01c.getCk2());
		prompts.put("ck3", l192m01c.getCk3());
		prompts.put(
				"ck4Date",
				l192m01c.getCk4Date() == null ? "" : sdf.format(l192m01c
						.getCk4Date()));
		prompts.put(
				"ck5Amt",
				l192m01c.getCk5Amt() == null ? "" : df.format(l192m01c
						.getCk5Amt()));
		prompts.put(
				"ck5Date",
				l192m01c.getCk5Date() == null ? "" : sdf.format(l192m01c
						.getCk5Date()));

		prompts.put(
				"ck6Date",
				l192m01c.getCk6Date() == null ? "" : sdf.format(l192m01c
						.getCk6Date()));

		prompts.put(
				"ck7Date",
				l192m01c.getCk7Date() == null ? "" : sdf.format(l192m01c
						.getCk7Date()));

		prompts.put(
				"ck8Date",
				l192m01c.getCk8Date() == null ? "" : sdf.format(l192m01c
						.getCk8Date()));
		prompts.put("ck9", l192m01c.getCk9());
		prompts.put("ck10", l192m01c.getCk10());

		prompts.put(
				"ck11Date",
				l192m01c.getCk11Date() == null ? "" : sdf.format(l192m01c
						.getCk11Date()));
		prompts.put("ck12", l192m01c.getCk12());
		prompts.put("userItem1", l192m01c.getUserItem1());
		prompts.put("userCk1", l192m01c.getUserCk1());

		prompts.put("processComm", meta.getProcessComm());
		prompts.put("gist", meta.getGist());
		prompts.put("randomCode", meta.getRandomCode());
		prompts.put("balDate", balDate);

		Map<String, Object> custData = misCustdataService
				.findCustdataMapByMainId(meta.getCustId(), meta.getDupNo());
		if (custData != null && custData.size() > 0) {
			String buscd = (String) custData.get("BUSCD");
			if (LMSUtil.isBusCode_060000_130300(buscd)) {
				prompts.put("isEnterpriseCustomer", "N");
			} else {
				prompts.put("isEnterpriseCustomer", "Y");
			}
		} else {
			prompts.put("isEnterpriseCustomers", "?");
		}

		prompts.put("LOGOSHOW", logoPath);
		
		rptGenerator.setVariableData(prompts);

	}
}
