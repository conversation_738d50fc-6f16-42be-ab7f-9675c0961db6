<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
			<script>loadScript('pagejs/cls/CLS1220M02Page')</script>
			<div class="button-menu funcContainer" id="buttonPanel">
				<th:block th:if="${_btnSAVE_visible}">
					<button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04"></span>
	        			<th:block th:text="#{'button.save'}">儲存</th:block>
	        		</button>
				</th:block>
				
				<th:block th:if="${_btnDOC_EDITING_visible}">
										
	        		<button id="btnSend" >
	        			<span class="ui-icon ui-icon-jcs-02"></span>
	        			<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
	        		</button>
					
					<button id="btnCancelEdit">
						<span class="ui-icon ui-icon-wrench"></span>
	        			<th:block th:text="#{'button.cancelEdit'}">取消編製</th:block>
	        		</button>
					
	        	</th:block>
				<th:block th:if="${_btnWAIT_APPROVE_visible}">
	        		<button id="btnAccept" >
	        			<span class="ui-icon ui-icon-jcs-214"></span>
	        			<th:block th:text="#{'button.accept'}">核可</th:block>
	        		</button>
	        		<button id="btnReturn" >
	        			<span class="ui-icon ui-icon-jcs-210"></span>
	        			<th:block th:text="#{'button.return'}">退回</th:block>
					</button>
				</th:block>	
				<th:block th:if="${_btnToEdit_visible}">
					<button id="btnToEdit">
						<span class="ui-icon ui-icon-wrench"></span>
	        			<th:block th:text="#{'button.toEdit'}">轉編製中</th:block>
	        		</button>
					
				</th:block>
				<button id="btnPrint" class="forview">
					<span class="ui-icon ui-icon-jcs-03"></span>
					<th:block th:text="#{'button.print'}">列印</th:block>
				</button>
				<button id="btnExit" class="forview">
					<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}">離開</th:block>
				</button>
			</div>
			<!--=====================-->
			<div class="tit2 color-black">
				<th:block th:text="#{'doc.tit01'}">線上信貸資料</th:block>:
				<span class="color-blue" id="titInfo"></span>
			</div>
			<div class="tabs doc-tabs">
				<ul>
					<li id="tab01"><a href="#tab-01" goto="01"><b><th:block th:text="#{'title.tab01'}">案件資訊</th:block></b></a></li>
					<li id="tab02"><a href="#tab-02" goto="02"><b><th:block th:text="#{'title.tab02'}">基本資料</th:block></b></a></li>
					<li id="tab03"><a href="#tab-03" goto="03"><b><th:block th:text="#{'title.tab03'}">服務單位</th:block></b></a></li>
					<li id="tab04"><a href="#tab-04" goto="04"><b><th:block th:text="#{'title.tab04'}">償債能力</th:block></b></a></li>
					<!--
					<li id="tab05"><a href="#tab-05" goto="05"><b><th:block th:text="#{'title.tab05'}">配偶資料</th:block></b></a></li>
					-->					
                </ul>
                <div class="tabCtx-warp">
                    <form id="tabForm">
						<div th:id="${tabIdx}" th:insert="${panelName} :: ${panelFragmentName}"></div>
                    </form>
                </div>
			</div>	
			
			<div id="div_C122S01BForm" style='display:none;'>
				<form id="editC122S01BForm">
					<table class='tb2'>
						<tr>
							<td class='hd2' nowrap><th:block th:text="#{'C122S01B.cntrNo'}">額度序號</th:block></td>
							<td><span id='cntrNo'></span>&nbsp;</td>
							<td class='hd2' nowrap><th:block th:text="#{'C122S01B.seq'}">序號</th:block></td>
							<td><span id='seq'></span>&nbsp;</td>
							<td class='hd2' nowrap><th:block th:text="#{'C122S01B.prodKind'}">產品種類</th:block></td>
							<td><span id='prodKindDesc'></span>&nbsp;</td>
							<td class='hd2' nowrap><th:block th:text="#{'C122S01B.loanCurr'}">動撥幣別</th:block></td>
							<td><span id='loanCurr'></span>&nbsp;</td>
						</tr>
						<tr><td colspan='8'>
							<table>
								<tr width='100%'>
									<td width='16%' class='hd2'>&nbsp;</td>
									<td width='42%' class='hd2'><th:block th:text="#{'label.current'}">本次</th:block></td>
									<td width='42%' class='hd2'><th:block th:text="#{'label.original'}">核准內容</th:block></td>
								</tr>
								<tr>
									<td class='hd2'><th:block th:text="#{'C122S01B.loanAmt'}">動撥金額</th:block></td>
									<td><input type='text' id='loanAmt' name='loanAmt' class='numeric'></td>
									<td><input type='text' disabled id='loanAmt_o' name='loanAmt_o' class='numeric'>&nbsp;</td>
								</tr>
								<tr>
									<td class='hd2'><th:block th:text="#{'C122S01B.rateDesc'}">利率</th:block></td>
									<td><textarea id='rateDesc' name='rateDesc' style='width:350px;height:100px;'></textarea></td>
									<td><span id='rateDesc_o'></span></td>
								</tr>
							</table>
						</td></tr>
							
					</table>
                </form>
			</div>
		</th:block>
    </body>
</html>
