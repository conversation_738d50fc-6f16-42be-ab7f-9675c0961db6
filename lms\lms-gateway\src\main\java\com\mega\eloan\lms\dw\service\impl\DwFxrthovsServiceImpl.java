/* 
 * DwFxrthovsServiceImpl.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2011 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.dw.service.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.TWNDate;

import com.mega.eloan.lms.dw.service.DwFxrthovsService;
import com.mega.sso.model.IBranch;

@Service("dwFxrthovsService")
public class DwFxrthovsServiceImpl extends AbstractDWJdbc implements
		DwFxrthovsService {

	public List<?> findDW_DWFXRTHOVS_RATE(String brNo) {
		return this.getJdbc().queryForList("DWLNCNTROVS.selByBrNoRate",
				new Object[] { brNo });
	}
	
	public List<?> findDW_DWFXRTHOVS_RATE2(String brNo) {
		return this.getJdbc().queryForList("DW_FXRTHOVS.selRate",
				new Object[] { brNo });
	}

	public List<?> findDW_DWFXRTHOVS_MainCurr(String brNo) {
		return this.getJdbc().queryForList("DWLNCNTROVS.selByBrNoMainCurr",
				new Object[] { brNo });
	}
	
	public List<?> findDW_DWFXRTHOVSSelAll(String branch){
		String month = String.valueOf((Integer.valueOf(TWNDate.toAD(CapDate.getCurrentTimestamp()).substring(5, 7))-1));
		String yearM = TWNDate.toAD(CapDate.getCurrentTimestamp()).substring(0, 4)+month;
		return this.getJdbc().queryForList("DW_FXRTHOVS.selAll", new Object[] { branch,yearM });
	}

	public List<?> findDW_DWFXRTHOVSSelAllMaxDate(String branch){
		return this.getJdbc().queryForList("DW_FXRTHOVS.selAllMaxDate", new Object[] { branch });
	}
	
	
	public BigDecimal getCur1ToCur2Rate(String date, IBranch branch,
			String sourceCurr, String destCur) {
		boolean sourceTWD = "TWD".equals(sourceCurr);
		BigDecimal tmpBD = null;
		String sql;
		if (sourceTWD || "TWD".equals(destCur)) {
			sql = getSqlBySqlId("FXRTHOVS_S.TWDAndCur2Rate");
		} else {
			sql = getSqlBySqlId("FXRTHOVS_S.Cur1ToCur2Rate");
		}
		Object qryDate;
		if (CapString.isEmpty(date)) {
			Map<String, Object> rtn = this.getJdbc().queryForMap(
					"DW_FXRTHOVS.getMaxDT", new Object[0]);// DW_FXRTHOVS.getMaxDT
			qryDate = rtn.get("DT");
			sql = MessageFormat.format(sql, new Object[] { "DWADM.DW_FXRTHOVS",
					"DT" });
		} else {
			int dateLen = date.length(), months = 0;
			if (dateLen == 10) {// yyyy-MM-dd
				months = CapDate.calculateMonths(
						CapDate.getCurrentDate("yyyyMMdd"), date, "yyyyMMdd",
						"yyyy-MM-dd");
			} else if (dateLen == 7) {// yyyy-MM
				months = CapDate.calculateMonths(
						CapDate.getCurrentDate("yyyyMMdd"), date, "yyyyMMdd",
						"yyyy-MM");
				if (months == 0) { // 當月，則查當月匯率檔
					date = date + "-01";
					dateLen = 10;
				}
			}
			if (dateLen == 10 && months == 0) { // yyyy-MM-dd,當月匯率
				// 每日匯率檔 DWADM.DW_FXRTHOVS
				sql = MessageFormat.format(sql, new Object[] {
						"DWADM.DW_FXRTHOVS", "DT" });
			} else {
				// 每月月底匯率檔 DWADM.DW_FXRTHOVS_SS
				sql = MessageFormat.format(sql, new Object[] {
						"DWADM.DW_FXRTHOVS_SS", "CYC_MN" });
				switch (dateLen) {
				case 10: // yyyy-MM-dd
					date = date.substring(0, 7) + "-01";
					break;
				case 7: // yyyy-MM
					date = date + "-01";
					break;
				case 4: // yyyy
					date = date + "-12-01";
					break;
				}
			}
			qryDate = date;
		}

		// 2012,5,8 sk
		if (sourceTWD || "TWD".equals(destCur)) {
			String curr = sourceTWD ? destCur : sourceCurr;
			Map<String, Object> rtn = this.getJdbc().queryForMap(
					StringUtils.replace(sql, "\\", ""),
					new Object[] { qryDate, branch.getBrNo(), curr, curr });
			if(!CollectionUtils.isEmpty(rtn)){
				if(sourceTWD && rtn.get("CURRT2")!=null){
					if(MapUtils.getObject(rtn, "CURRT2") instanceof BigDecimal){
//						return (BigDecimal) (rtn.get("CURRT2"));
						tmpBD = (BigDecimal) (rtn.get("CURRT2"));
					}else if(MapUtils.getObject(rtn, "CURRT2") instanceof Double){
//						return BigDecimal.valueOf(MapUtils.getDouble(rtn, "CURRT2"));
						tmpBD = BigDecimal.valueOf(MapUtils.getDouble(rtn, "CURRT2"));
					}
				}else if(rtn.get("CURR2T")!=null){
					if(MapUtils.getObject(rtn, "CURR2T") instanceof BigDecimal){
//						return (BigDecimal) (rtn.get("CURR2T"));
						tmpBD = (BigDecimal) (rtn.get("CURR2T"));
					}else if(MapUtils.getObject(rtn, "CURR2T") instanceof Double){
//						return BigDecimal.valueOf(MapUtils.getDouble(rtn, "CURR2T"));
						tmpBD = BigDecimal.valueOf(MapUtils.getDouble(rtn, "CURR2T"));
					}
				}
			}
		} else {
			Map<String, Object> rtn = this.getJdbc().queryForMap(
					StringUtils.replace(sql, "\\", "'"),
					new Object[] { qryDate, branch.getBrNo(), sourceCurr, destCur, sourceCurr, destCur });
			if (rtn != null && !rtn.isEmpty()) {
				if(rtn.get("CURR12")!=null){
					if(rtn.get("CURR12") instanceof BigDecimal){
//						return (BigDecimal) rtn.get("CURR12");
						tmpBD = (BigDecimal) rtn.get("CURR12");
					}else{
//						return BigDecimal.valueOf(MapUtils.getDouble(rtn, "CURR12"));
						tmpBD = BigDecimal.valueOf(MapUtils.getDouble(rtn, "CURR12"));
					}
				}
			}
		}
		// 2012/7/9,Tim,(找不到海外匯率檔,改找國內匯率檔)回傳null後,程式繼續找國內匯率檔
		return tmpBD;
	}// ;
	
}
