package com.mega.eloan.lms.mfaloan.bean;

import java.security.Timestamp;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;

/** 可疑代辦案件註記作業檔 **/


public class ELF516 extends GenericBean{
	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 資料年月<p/>
	 * EX:201412
	 */
	@Size(max=6)
	@Column(name="YYYYMM", length=6, columnDefinition="CHAR(06)")
	private String yyyymm;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrno;

	/** 借款人統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)")
	private String custid;

	/** 重複序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(01)")
	private String dupno;

	/** 姓名 **/
	@Size(max=40)
	@Column(name="CUSTNAME", length=40, columnDefinition="CHAR(40)")
	private String custName;

	/** 
	 * 性質<p/>
	 * EX: A.增額、N.新作
	 */
	@Size(max=1)
	@Column(name="STATUS", length=1, columnDefinition="CHAR(01)")
	private String status;

	/** 
	 * 疑似代辦案件訊息<p/>
	 * a.      撥款後回查有其他金融機構短期內接續撥款情形 (指3個月內)<br/>
	 *  b.      貸款後不久即延滯情形 (指3個月內)<br/>
	 *  c.      跨區承作之情形 (對於借款人居住、工作地與案件來源無地源關係之申貸案件)<br/>
	 *  d.      其他可疑情形：                            (請自行填寫)<br/>
	 *  e.    無以上情形   (此項設為預設值)
	 */
	@Size(max=2)
	@Column(name="LNFLAG", length=2, columnDefinition="CHAR(02)")
	private String lnflag;

	/** 其他可疑情形 **/
	@Size(max=300)
	@Column(name="OTHERMEMO", length=300, columnDefinition="VARCHAR(300)")
	private String othermemo;

	/** 資料修改人（行員代號） **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(06)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 資料覆核人（行員代號） **/
	@Size(max=6)
	@Column(name="APPROVER", length=6, columnDefinition="CHAR(06)")
	private String approver;
	
	/** 帳號 **/
	@Size(max=400)
	@Column(name="LOANNO", length=400, columnDefinition="VARCHAR(400)")
	private String loanNo;
	
	/** 查證結果 **/
	@Size(max=400)
	@Column(name="BRANCHCOMM", length=400, columnDefinition="VARCHAR(400)")
	private String branchComm;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得資料年月<p/>
	 * EX:201412
	 */
	public String getYyyymm() {
		return this.yyyymm;
	}
	/**
	 *  設定資料年月<p/>
	 *  EX:201412
	 **/
	public void setYyyymm(String value) {
		this.yyyymm = value;
	}

	/** 取得額度序號 **/
	public String getCntrno() {
		return this.cntrno;
	}
	/** 設定額度序號 **/
	public void setCntrno(String value) {
		this.cntrno = value;
	}

	/** 取得借款人統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定借款人統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得姓名 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 
	 * 取得性質<p/>
	 * EX: A.增額、N.新作
	 */
	public String getStatus() {
		return this.status;
	}
	/**
	 *  設定性質<p/>
	 *  EX: A.增額、N.新作
	 **/
	public void setStatus(String value) {
		this.status = value;
	}

	/** 
	 * 取得疑似代辦案件訊息<p/>
	 * a.      撥款後回查有其他金融機構短期內接續撥款情形 (指3個月內)<br/>
	 *  b.      貸款後不久即延滯情形 (指3個月內)<br/>
	 *  c.      跨區承作之情形 (對於借款人居住、工作地與案件來源無地源關係之申貸案件)<br/>
	 *  d.      其他可疑情形：                            (請自行填寫)<br/>
	 *  e.    無以上情形   (此項設為預設值)
	 */
	public String getLnflag() {
		return this.lnflag;
	}
	/**
	 *  設定疑似代辦案件訊息<p/>
	 *  a.      撥款後回查有其他金融機構短期內接續撥款情形 (指3個月內)<br/>
	 *  b.      貸款後不久即延滯情形 (指3個月內)<br/>
	 *  c.      跨區承作之情形 (對於借款人居住、工作地與案件來源無地源關係之申貸案件)<br/>
	 *  d.      其他可疑情形：                            (請自行填寫)<br/>
	 *  e.    無以上情形   (此項設為預設值)
	 **/
	public void setLnflag(String value) {
		this.lnflag = value;
	}

	/** 取得其他可疑情形 **/
	public String getOthermemo() {
		return this.othermemo;
	}
	/** 設定其他可疑情形 **/
	public void setOthermemo(String value) {
		this.othermemo = value;
	}

	/** 取得資料修改人（行員代號） **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改人（行員代號） **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定資料修改日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得資料覆核人（行員代號） **/
	public String getApprover() {
		return this.approver;
	}
	/** 設定資料覆核人（行員代號） **/
	public void setApprover(String value) {
		this.approver = value;
	}
	
	/** 設定帳號 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}
	
	/** 取得帳號 **/
	public String getLoanNo() {
		return this.loanNo;
	}
	
	/** 設定查證結果 **/
	public void setBranchComm(String value) {
		this.branchComm = value;
	}

	/** 取得查證結果 **/
	public String getBranchComm() {
		return this.branchComm;
	}
}
