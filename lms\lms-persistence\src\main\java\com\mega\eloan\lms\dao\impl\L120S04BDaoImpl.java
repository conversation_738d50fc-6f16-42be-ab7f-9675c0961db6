/* 
 * L120S04BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L120S04BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S04B;

/** 關係戶於本行往來實績彙總表主檔 **/
@Repository
public class L120S04BDaoImpl extends LMSJpaDao<L120S04B, String>
	implements L120S04BDao {

	@Override
	public L120S04B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S04B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S04B> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S04B> findByMainIdKeyCustIdDupNo(String mainId,
			String keyCustId, String keyDupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "keyCustId", keyCustId);
		search.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo", keyDupNo);
		List<L120S04B> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L120S04B> findByMainIdDocKind(String mainId,String [] docKind) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		
		//search.addSearchModeParameters(SearchMode.EQUALS, "docKind", docKind);
		search.addSearchModeParameters(SearchMode.IN, "docKind", docKind);
		List<L120S04B> list = createQuery(search).getResultList();
		return list;
	}

    @Override
    public List<L120S04B> findByMainIdKeyCustIdDupNoDocKind(
            String mainId, String keyCustId, String keyDupNo, String [] docKind) {
        ISearch search = createSearchTemplete();
        search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        search.addSearchModeParameters(SearchMode.EQUALS, "keyCustId", keyCustId);
        search.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo", keyDupNo);
        search.addSearchModeParameters(SearchMode.IN, "docKind", docKind);
        List<L120S04B> list = createQuery(search).getResultList();
        return list;
    }
}