/* 
 * EloandbCMSBASEService
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2011 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.eloandb.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 取逾催資料使用
 * EloandbCMSBASEService
 * </pre>
 * 
 * @since 2013/01/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/26,UFO,new
 *          </ul>
 */

public interface EloandbCOLBASEService {

	/**
	 * 取得案件報告表清單
	 * 
	 * @param mainId
	 *            簽報書之mainId
	 * @return 查詢結果
	 */
	public List<Map<String, Object>> getS104M01AList(String mainId);

}
