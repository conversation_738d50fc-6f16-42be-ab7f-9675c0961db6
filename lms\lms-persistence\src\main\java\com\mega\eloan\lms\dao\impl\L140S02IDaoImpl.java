/* 
 * L140S02IDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L140S02IDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140S02I;

/** 留學貸款檔 **/
@Repository
public class L140S02IDaoImpl extends LMSJpaDao<L140S02I, String> implements
		L140S02IDao {

	@Override
	public L140S02I findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S02I> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140S02I> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140S02I> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "stdCustId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "stdDupNo", DupNo);
		List<L140S02I> list = createQuery(L140S02I.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L140S02I findByUniqueKey(String mainId, Integer seq) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public L140S02I findByUniqueKey(String mainId, Integer seq,
			String stdCustId, String stdDupNo) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		if (stdCustId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "stdCustId",
					stdCustId);
		if (stdDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "stdDupNo",
					stdDupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L140S02I> findByIndex01(String mainId, Integer seq,
			String stdCustId, String stdDupNo) {
		ISearch search = createSearchTemplete();
		List<L140S02I> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		if (stdCustId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "stdCustId",
					stdCustId);
		if (stdDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "stdDupNo",
					stdDupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140S02I> findByIndex01(String mainId, Integer seq) {
		return this.findByIndex01(mainId, seq, null, null);
	}
}