/* 
 * C126S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C126S01A;

/** RPA發查明家事公告細檔 **/
public interface C126S01ADao extends IGenericDao<C126S01A> {

	C126S01A findByOid(String oid);
	
	List<C126S01A> findByMainId(String mainId);
	
	C126S01A findByUniqueKey(String MainId, String branchNo, String empNo, String dataCustomerNo);
}