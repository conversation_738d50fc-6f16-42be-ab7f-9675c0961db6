/* 
 * C140M07ADaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C140M07ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C140M07A;
import com.mega.eloan.lms.model.C140M07A_;

/**
 * <pre>
 * 徵信調查報告書第柒章主檔 - 財務分析
 * </pre>
 * 
 * @since 2011/10/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/05, TimChiang, new
 *          </ul>
 */
@Repository
public class C140M07ADaoImpl extends LMSJpaDao<C140M07A, String> implements
		C140M07ADao {

	@Override
	public List<C140M07A> findByMainPidTab(String uid, String mainId,
			String tab, String subtab) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140M07A_.mainId.getName(), mainId);
//		search.addSearchModeParameters(SearchMode.EQUALS,
//				C140M07A_.pid.getName(), uid);
		if (tab != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					C140M07A_.tab.getName(), tab);
		}
		if (subtab != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					C140M07A_.subtab.getName(), subtab);
		}
		return find(search);
	}

}// ;
