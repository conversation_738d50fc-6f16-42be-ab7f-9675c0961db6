/* 
 *LMS9121ServiceImp.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.dao.C101S01ADao;
import com.mega.eloan.lms.dao.C101S01BDao;
import com.mega.eloan.lms.dao.C101S01CDao;
import com.mega.eloan.lms.dao.C101S01DDao;
import com.mega.eloan.lms.dao.C101S01EDao;
import com.mega.eloan.lms.dao.C101S01FDao;
import com.mega.eloan.lms.dao.C101S01GDao;
import com.mega.eloan.lms.dao.C101S01HDao;
import com.mega.eloan.lms.dao.C101S01IDao;
import com.mega.eloan.lms.dao.C101S01JDao;
import com.mega.eloan.lms.dao.C101S01KDao;
import com.mega.eloan.lms.dao.C101S01LDao;
import com.mega.eloan.lms.dao.C101S01MDao;
import com.mega.eloan.lms.dao.C101S01NDao;
import com.mega.eloan.lms.dao.C101S01PDao;
import com.mega.eloan.lms.dao.C101S01QDao;
import com.mega.eloan.lms.dao.C101S01RDao;
import com.mega.eloan.lms.dao.C101S01YDao;
import com.mega.eloan.lms.dao.C101S02ADao;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.C120S01BDao;
import com.mega.eloan.lms.dao.C120S01CDao;
import com.mega.eloan.lms.dao.C120S01DDao;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.dao.C120S01FDao;
import com.mega.eloan.lms.dao.C120S01GDao;
import com.mega.eloan.lms.dao.C120S01HDao;
import com.mega.eloan.lms.dao.C120S01IDao;
import com.mega.eloan.lms.dao.C120S01JDao;
import com.mega.eloan.lms.dao.C120S01KDao;
import com.mega.eloan.lms.dao.C120S01LDao;
import com.mega.eloan.lms.dao.C120S01MDao;
import com.mega.eloan.lms.dao.C120S01NDao;
import com.mega.eloan.lms.dao.C120S01PDao;
import com.mega.eloan.lms.dao.C120S01QDao;
import com.mega.eloan.lms.dao.C120S01RDao;
import com.mega.eloan.lms.dao.C120S01YDao;
import com.mega.eloan.lms.dao.C120S02ADao;
import com.mega.eloan.lms.dao.C140M04ADao;
import com.mega.eloan.lms.dao.C160S02ADao;
import com.mega.eloan.lms.dao.C241M01BDao;
import com.mega.eloan.lms.dao.C241M01CDao;
import com.mega.eloan.lms.dao.C241M01EDao;
import com.mega.eloan.lms.dao.C999M01BDao;
import com.mega.eloan.lms.dao.C999M01CDao;
import com.mega.eloan.lms.dao.C999S01ADao;
import com.mega.eloan.lms.dao.L000M01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01EDao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L120S01CDao;
import com.mega.eloan.lms.dao.L120S01DDao;
import com.mega.eloan.lms.dao.L120S01EDao;
import com.mega.eloan.lms.dao.L120S01FDao;
import com.mega.eloan.lms.dao.L120S01GDao;
import com.mega.eloan.lms.dao.L120S01HDao;
import com.mega.eloan.lms.dao.L120S01IDao;
import com.mega.eloan.lms.dao.L120S01JDao;
import com.mega.eloan.lms.dao.L120S01KDao;
import com.mega.eloan.lms.dao.L120S01LDao;
import com.mega.eloan.lms.dao.L120S03ADao;
import com.mega.eloan.lms.dao.L120S04ADao;
import com.mega.eloan.lms.dao.L120S05BDao;
import com.mega.eloan.lms.dao.L120S05DDao;
import com.mega.eloan.lms.dao.L120S06ADao;
import com.mega.eloan.lms.dao.L120S06BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01EDao;
import com.mega.eloan.lms.dao.L140M01JDao;
import com.mega.eloan.lms.dao.L140M01ODao;
import com.mega.eloan.lms.dao.L140S01ADao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.dao.L140S02BDao;
import com.mega.eloan.lms.dao.L140S02IDao;
import com.mega.eloan.lms.dao.L141M01CDao;
import com.mega.eloan.lms.dao.L160M01BDao;
import com.mega.eloan.lms.dao.L161S01ADao;
import com.mega.eloan.lms.dao.L162S01ADao;
import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.dao.L170M01BDao;
import com.mega.eloan.lms.dao.L170M01CDao;
import com.mega.eloan.lms.dao.L170M01DDao;
import com.mega.eloan.lms.dao.L170M01EDao;
import com.mega.eloan.lms.dao.L170M01FDao;
import com.mega.eloan.lms.dao.L180M01BDao;
import com.mega.eloan.lms.dao.L180M01CDao;
import com.mega.eloan.lms.dao.L184M01ADao;
import com.mega.eloan.lms.dao.L185M01ADao;
import com.mega.eloan.lms.dao.L192M01BDao;
import com.mega.eloan.lms.dao.L192S01ADao;
import com.mega.eloan.lms.dao.L210M01ADao;
import com.mega.eloan.lms.dao.L210S01ADao;
import com.mega.eloan.lms.dao.L230S01ADao;
import com.mega.eloan.lms.dao.L999M01BDao;
import com.mega.eloan.lms.dao.L999M01CDao;
import com.mega.eloan.lms.dao.L999S05ADao;
import com.mega.eloan.lms.dw.service.DWRKCNTRNOService;
import com.mega.eloan.lms.fms.service.LMS9121Service;
import com.mega.eloan.lms.mfaloan.service.MisCntrNoDataService;
import com.mega.eloan.lms.mfaloan.service.impl.AbstractMFAloanJdbc;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01C;
import com.mega.eloan.lms.model.C101S01D;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01F;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01I;
import com.mega.eloan.lms.model.C101S01J;
import com.mega.eloan.lms.model.C101S01K;
import com.mega.eloan.lms.model.C101S01L;
import com.mega.eloan.lms.model.C101S01M;
import com.mega.eloan.lms.model.C101S01N;
import com.mega.eloan.lms.model.C101S01P;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C101S01Y;
import com.mega.eloan.lms.model.C101S02A;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01F;
import com.mega.eloan.lms.model.C120S01G;
import com.mega.eloan.lms.model.C120S01H;
import com.mega.eloan.lms.model.C120S01I;
import com.mega.eloan.lms.model.C120S01J;
import com.mega.eloan.lms.model.C120S01K;
import com.mega.eloan.lms.model.C120S01L;
import com.mega.eloan.lms.model.C120S01M;
import com.mega.eloan.lms.model.C120S01N;
import com.mega.eloan.lms.model.C120S01P;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01R;
import com.mega.eloan.lms.model.C120S01Y;
import com.mega.eloan.lms.model.C120S02A;
import com.mega.eloan.lms.model.C140M04A;
import com.mega.eloan.lms.model.C160S02A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.C241M01C;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.eloan.lms.model.C999M01B;
import com.mega.eloan.lms.model.C999M01C;
import com.mega.eloan.lms.model.C999S01A;
import com.mega.eloan.lms.model.L000M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L120S01E;
import com.mega.eloan.lms.model.L120S01F;
import com.mega.eloan.lms.model.L120S01G;
import com.mega.eloan.lms.model.L120S01H;
import com.mega.eloan.lms.model.L120S01I;
import com.mega.eloan.lms.model.L120S01J;
import com.mega.eloan.lms.model.L120S01K;
import com.mega.eloan.lms.model.L120S01L;
import com.mega.eloan.lms.model.L120S03A;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S05B;
import com.mega.eloan.lms.model.L120S05D;
import com.mega.eloan.lms.model.L120S06A;
import com.mega.eloan.lms.model.L120S06B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02B;
import com.mega.eloan.lms.model.L140S02I;
import com.mega.eloan.lms.model.L141M01C;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01C;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L180M01C;
import com.mega.eloan.lms.model.L184M01A;
import com.mega.eloan.lms.model.L185M01A;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192S01A;
import com.mega.eloan.lms.model.L210M01A;
import com.mega.eloan.lms.model.L210S01A;
import com.mega.eloan.lms.model.L230S01A;
import com.mega.eloan.lms.model.L999M01B;
import com.mega.eloan.lms.model.L999M01C;
import com.mega.eloan.lms.model.L999S05A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 資料修正
 * </pre>
 * 
 * @since 2012/11/20
 * <AUTHOR> Chang
 * @version <ul>
 *          <li>2012/11/20,Gary Chang,new
 *          </ul>
 */
@Service
public class LMS9121ServiceImpl extends AbstractMFAloanJdbc implements
		LMS9121Service {
	protected final Logger logger = LoggerFactory.getLogger(getClass());
	@Resource
	MisCntrNoDataService miscntrnodataservice;
	@Resource
	DWRKCNTRNOService dwcntrnodataservice;
	@Resource
	L120S03ADao l120s03adao;
	@Resource
	L120S06ADao l120s06adao;
	@Resource
	L120S06BDao l120s06bdao;
	@Resource
	L140M01ADao l140m01adao;
	@Resource
	L160M01BDao l160m01bdao;
	@Resource
	L161S01ADao l161s01adao;
	@Resource
	L162S01ADao l162s01adao;
	@Resource
	L170M01BDao l170m01bdao;
	@Resource
	L180M01CDao l180m01cdao;
	@Resource
	L210M01ADao l210m01adao;

	@Resource
	C241M01BDao c241m01bdao;
	@Resource
	L192S01ADao l192s01adao;
	@Resource
	L141M01CDao l141m01cdao;
	@Resource
	L140S02BDao l140s02bdao;
	@Resource
	L120S01CDao l120s01cdao;
	@Resource
	L170M01EDao l170m01edao;
	@Resource
	L230S01ADao l230s01adao;
	@Resource
	L140S02ADao l140s02adao;
	@Resource
	C999S01ADao c999s01adao;
	@Resource
	L140M01EDao l140m01edao;
	@Resource
	L210S01ADao l210s01adao;
	@Resource
	C241M01CDao c241m01cdao;
	@Resource
	L120S01ADao l120s01adao;
	@Resource
	L120S01BDao l120s01bdao;
	@Resource
	L120S01DDao l120s01ddao;
	@Resource
	L120S01EDao l120s01edao;
	@Resource
	L120S01FDao l120s01fdao;
	@Resource
	L120S01GDao l120s01gdao;
	@Resource
	L120S01HDao _l120s01hdao;
	@Resource
	L120S01IDao _l120s01idao;
	@Resource
	L120S01JDao _l120s01jdao;
	@Resource
	L120S01KDao _l120s01kdao;
	@Resource
	L120S01LDao _l120s01ldao;
	@Resource
	L120S04ADao l120s04adao;
	@Resource
	L120S05BDao l120s05bdao;
	@Resource
	L120S05DDao l120s05ddao;
	@Resource
	L140M01JDao l140m01jdao;
	@Resource
	L170M01CDao l170m01cdao;
	@Resource
	L170M01DDao l170m01ddao;
	@Resource
	L170M01FDao l170m01fdao;
	@Resource
	L192M01BDao l192m01bdao;
	@Resource
	C101S01ADao c101s01adao;
	@Resource
	C101S01BDao c101s01bdao;
	@Resource
	C101S01CDao c101s01cdao;
	@Resource
	C101S01DDao c101s01ddao;
	@Resource
	C101S01EDao c101s01edao;
	@Resource
	C101S01FDao c101s01fdao;
	@Resource
	C101S01GDao c101s01gdao;
	@Resource
	C101S01HDao c101s01hdao;
	@Resource
	C101S01IDao c101s01idao;
	@Resource
	C101S01JDao c101s01jdao;
	@Resource
	C101S01KDao c101s01kdao;
	@Resource
	C101S01LDao c101s01ldao;
	@Resource
	C101S01MDao c101s01mdao;
	@Resource
	C101S01NDao c101s01ndao;
	@Resource
	C101S01PDao c101s01pdao;
	@Resource
	C101S01QDao c101s01qdao;
	@Resource
	C101S01RDao c101s01rdao;
	@Resource
	C120M01ADao c120m01adao;
	@Resource
	C120S01ADao c120s01adao;
	@Resource
	C120S01BDao c120s01bdao;
	@Resource
	C120S01CDao c120s01cdao;
	@Resource
	C120S01DDao c120s01ddao;
	@Resource
	C120S01EDao c120s01edao;
	@Resource
	C120S01FDao c120s01fdao;
	@Resource
	C120S01GDao c120s01gdao;
	@Resource
	C120S01HDao c120s01hdao;
	@Resource
	C120S01IDao c120s01idao;
	@Resource
	C120S01JDao c120s01jdao;
	@Resource
	C120S01KDao c120s01kdao;
	@Resource
	C120S01LDao c120s01ldao;
	@Resource
	C120S01MDao c120s01mdao;
	@Resource
	C120S01NDao c120s01ndao;
	@Resource
	C120S01PDao c120s01pdao;
	@Resource
	C120S01QDao c120s01qdao;
	@Resource
	C120S01RDao c120s01rdao;
	@Resource
	C140M04ADao c140m04adao;
	@Resource
	C241M01EDao c241m01edao;
	@Resource
	C999M01BDao c999m01bdao;
	@Resource
	C999M01CDao c999m01cdao;
	@Resource
	L000M01ADao l000m01adao;
	@Resource
	L120M01ADao l120m01adao;
	@Resource
	L120M01EDao l120m01edao;
	@Resource
	L140M01ODao l140m01odao;
	@Resource
	L140S01ADao l140s01adao;
	@Resource
	L140S02IDao l140s02idao;
	@Resource
	L170M01ADao l170m01adao;
	@Resource
	L180M01BDao l180m01bdao;
	@Resource
	L184M01ADao l184m01adao;
	@Resource
	L185M01ADao l185m01adao;

	@Resource
	L999M01BDao l999m01bdao;
	@Resource
	L999M01CDao l999m01cdao;
	@Resource
	L999S05ADao l999s05adao;
	
	@Resource
	C101S01YDao c101s01ydao;
	@Resource
	C120S01YDao c120s01ydao;
	
	@Resource
	C101S02ADao c101s02aDao;

	@Resource
	C120S02ADao c120s02aDao;
	
	@Resource
	C160S02ADao c160s02aDao;

	String success = "修改成功";
	String error = new String();

	/**
	 * 額度序號相同對象不同明細表
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public List<Map<String, Object>> sntrnoupdate(String OldCntrNo,
			String NewCntrNo) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String[] dwtable = { "DW_RKCNTRNO" };
		String[] mistable = { "QUOTSUB", "QUOTUNIO", "IQUOTAPP", "IQUOTJON",
				"ELLNGTEE", "IQUOTGUR", "ELF447", "ELF447N", "ELLNSEEK",
				"ELCSECNT", "QUOTAPPR", "ELCRTBL", "ELF503", "ELF500",
				"ELF501", "ELF502", "ELAPPCON", "LNF010", "PQUOTNF", "LNF130",
				"PEXTLIMT", "LNF195N", "LNF195Y", "STUDATA" };
		error = "無相關額度序號";
		if (checkNewCntrNo(dwtable, mistable, NewCntrNo)) {
			// 更新MIS & DW R6資料
			List<Map<String, Object>> dwcntrnodata = dwcntrnodataservice
					.selByCntrNo(OldCntrNo);
			List<Map<String, Object>> miscntrnodata = miscntrnodataservice
					.getCntrNoData(mistable, OldCntrNo);
			List<Map<String, Object>> DwResultlist = DwCntrNoUpdate(dwtable,
					dwcntrnodata, OldCntrNo, NewCntrNo);
			List<Map<String, Object>> MisResultlist = MisCntrNoUpdate(mistable,
					miscntrnodata, OldCntrNo, NewCntrNo);
			List<Map<String, Object>> R6lResultist = R6CntrNoUpdate(OldCntrNo,
					NewCntrNo);
			Map<String, Object> DwTitle = new HashMap<String, Object>();
			DwTitle.put("UpdateTable", "Dw");
			list.add(DwTitle);
			list.add(DwResultlist.get(0));
			Map<String, Object> MisTitle = new HashMap<String, Object>();
			MisTitle.put("UpdateTable", "Mis");
			list.add(MisTitle);
			for (int i = 0; i < MisResultlist.size(); i++) {
				list.add(MisResultlist.get(i));
			}
			Map<String, Object> R6Title = new HashMap<String, Object>();
			R6Title.put("UpdateTable", "R6");
			list.add(R6Title);
			for (int i = 0; i < R6lResultist.size(); i++) {
				list.add(R6lResultist.get(i));
			}
		} else {
			Map<String, Object> updateResult = new HashMap<String, Object>();
			updateResult.put("UpdateTable", "無法更新");
			updateResult.put("UpdateResult", "輸入資料重複");
			list.add(updateResult);
		}
		return list;
	}

	/**
	 * 客戶統編修正
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public List<Map<String, Object>> custIdupdate(String OldCustId,
			String OlddupId, String NewCustId, String NewdupId) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String[] dwtable = { "DW_RKADJUST", "DW_RKAPPLICANT", "DW_RKCNTRNO",
				"DW_RKCOLL", "DW_RKCREDIT", "DW_RKJCIC", "DW_RKPROJECT",
				"DW_RKSCORE", "DW_ELINSPSC" };
		String[] mistable = { "PTEAMAPP", "ELAPPCON", "ELLNGTEE", "ELLNSEEK",
				"IQUOTAPP", "LNUNID", "PQUOTNF", "QUOTAINF", "QUOTAPPR",
				"QUOTSUB", "STUDATA", "ELF447", "ELF447N", "ELF500", "ELF501",
				"ELF502", "ELF503", "LNFE0851", "LNFE0854", "LNFE0855",
				"LNFE0856", "LNF010", "LNF130", "LNF164" };
		error = "無相關客戶統編";
		if (checkNewcustId(dwtable, mistable, NewCustId, NewdupId)) {
			// 更新MIS & DW R6資料
			List<Map<String, Object>> dwcntrnodata = dwcntrnodataservice
					.selBycustIdDupNo(dwtable, OldCustId, OlddupId);
			List<Map<String, Object>> miscntrnodata = miscntrnodataservice
					.getcustIdData(mistable, OldCustId, OlddupId);
			List<Map<String, Object>> DwResultlist = DwCustIdUpdate(dwtable,
					dwcntrnodata, OldCustId, OlddupId, NewCustId, NewdupId);
			List<Map<String, Object>> MisResultlist = MisCustIdUpdate(mistable,
					miscntrnodata, OldCustId, OlddupId, NewCustId, NewdupId);
			List<Map<String, Object>> R6lResultist = R6CustIdDupNoUpdate(
					OldCustId, OlddupId, NewCustId, NewdupId);
			Map<String, Object> DwTitle = new HashMap<String, Object>();
			DwTitle.put("UpdateTable", "Dw");
			list.add(DwTitle);
			for (int i = 0; i < DwResultlist.size(); i++) {
				list.add(DwResultlist.get(i));
			}
			Map<String, Object> MisTitle = new HashMap<String, Object>();
			MisTitle.put("UpdateTable", "Mis");
			list.add(MisTitle);
			for (int i = 0; i < MisResultlist.size(); i++) {
				list.add(MisResultlist.get(i));
			}
			Map<String, Object> R6Title = new HashMap<String, Object>();
			R6Title.put("UpdateTable", "R6");
			list.add(R6Title);
			for (int i = 0; i < R6lResultist.size(); i++) {
				list.add(R6lResultist.get(i));
			}
		} else {
			Map<String, Object> updateResult = new HashMap<String, Object>();
			updateResult.put("UpdateTable", "無法更新");
			updateResult.put("UpdateResult", "輸入資料重複");
			list.add(updateResult);
		}
		return list;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error(e.getMessage());
				}
				if (model instanceof L120S03A) {
					l120s03adao.save(((L120S03A) model));
				} else if (model instanceof L120S06A) {
					l120s06adao.save(((L120S06A) model));
				} else if (model instanceof L120S06B) {
					l120s06bdao.save(((L120S06B) model));
				} else if (model instanceof L140M01A) {
					l140m01adao.save(((L140M01A) model));
				} else if (model instanceof L160M01B) {
					l160m01bdao.save(((L160M01B) model));
				} else if (model instanceof L161S01A) {
					l161s01adao.save(((L161S01A) model));
				} else if (model instanceof L162S01A) {
					l162s01adao.save(((L162S01A) model));
				} else if (model instanceof L170M01B) {
					l170m01bdao.save(((L170M01B) model));
				} else if (model instanceof L180M01C) {
					l180m01cdao.save(((L180M01C) model));
				} else if (model instanceof L210M01A) {
					l210m01adao.save(((L210M01A) model));
				} else if (model instanceof C241M01B) {
					c241m01bdao.save(((C241M01B) model));
				} else if (model instanceof L192S01A) {
					l192s01adao.save(((L192S01A) model));
				} else if (model instanceof L141M01C) {
					l141m01cdao.save(((L141M01C) model));
				} else if (model instanceof L140S02B) {
					l140s02bdao.save(((L140S02B) model));
				} else if (model instanceof L120S01C) {
					l120s01cdao.save(((L120S01C) model));
				} else if (model instanceof L170M01E) {
					l170m01edao.save(((L170M01E) model));
				} else if (model instanceof L230S01A) {
					l230s01adao.save(((L230S01A) model));
				} else if (model instanceof L140S02A) {
					l140s02adao.save(((L140S02A) model));
				} else if (model instanceof C999S01A) {
					c999s01adao.save(((C999S01A) model));
				} else if (model instanceof L140M01E) {
					l140m01edao.save(((L140M01E) model));
				} else if (model instanceof L210S01A) {
					l210s01adao.save(((L210S01A) model));
				} else if (model instanceof C241M01C) {
					c241m01cdao.save(((C241M01C) model));
				} else if (model instanceof L120S01A) {
					l120s01adao.save(((L120S01A) model));
				} else if (model instanceof L120S01B) {
					l120s01bdao.save(((L120S01B) model));
				} else if (model instanceof L120S01D) {
					l120s01ddao.save(((L120S01D) model));
				} else if (model instanceof L120S01E) {
					l120s01edao.save(((L120S01E) model));
				} else if (model instanceof L120S01F) {
					l120s01fdao.save(((L120S01F) model));
				} else if (model instanceof L120S01G) {
					l120s01gdao.save(((L120S01G) model));
				} else if (model instanceof L120S01H) {
					_l120s01hdao.save(((L120S01H) model));
				} else if (model instanceof L120S01I) {
					_l120s01idao.save(((L120S01I) model));
				} else if (model instanceof L120S01J) {
					_l120s01jdao.save(((L120S01J) model));
				} else if (model instanceof L120S01K) {
					_l120s01kdao.save(((L120S01K) model));
				} else if (model instanceof L120S01L) {
					_l120s01ldao.save(((L120S01L) model));
				} else if (model instanceof L120S04A) {
					l120s04adao.save(((L120S04A) model));
				} else if (model instanceof L120S05B) {
					l120s05bdao.save(((L120S05B) model));
				} else if (model instanceof L120S05D) {
					l120s05ddao.save(((L120S05D) model));
				} else if (model instanceof L140M01J) {
					l140m01jdao.save(((L140M01J) model));
				} else if (model instanceof L170M01C) {
					l170m01cdao.save(((L170M01C) model));
				} else if (model instanceof L170M01D) {
					l170m01ddao.save(((L170M01D) model));
				} else if (model instanceof L170M01F) {
					l170m01fdao.save(((L170M01F) model));
				} else if (model instanceof L192M01B) {
					l192m01bdao.save(((L192M01B) model));
				} else if (model instanceof C101S01A) {
					c101s01adao.save(((C101S01A) model));
				} else if (model instanceof C101S01B) {
					c101s01bdao.save(((C101S01B) model));
				} else if (model instanceof C101S01C) {
					c101s01cdao.save(((C101S01C) model));
				} else if (model instanceof C101S01D) {
					c101s01ddao.save(((C101S01D) model));
				} else if (model instanceof C101S01E) {
					c101s01edao.save(((C101S01E) model));
				} else if (model instanceof C101S01F) {
					c101s01fdao.save(((C101S01F) model));
				} else if (model instanceof C101S01G) {
					c101s01gdao.save(((C101S01G) model));
				} else if (model instanceof C101S01H) {
					c101s01hdao.save(((C101S01H) model));
				} else if (model instanceof C101S01I) {
					c101s01idao.save(((C101S01I) model));
				} else if (model instanceof C101S01J) {
					c101s01jdao.save(((C101S01J) model));
				} else if (model instanceof C101S01K) {
					c101s01kdao.save(((C101S01K) model));
				} else if (model instanceof C101S01L) {
					c101s01ldao.save(((C101S01L) model));
				} else if (model instanceof C101S01M) {
					c101s01mdao.save(((C101S01M) model));
				} else if (model instanceof C101S01N) {
					c101s01ndao.save(((C101S01N) model));
				} else if (model instanceof C101S01P) {
					c101s01pdao.save(((C101S01P) model));
				} else if (model instanceof C101S01Q) {
					c101s01qdao.save(((C101S01Q) model));
				} else if (model instanceof C101S01R) {
					c101s01rdao.save(((C101S01R) model));
				} else if (model instanceof C120M01A) {
					c120m01adao.save(((C120M01A) model));
				} else if (model instanceof C120S01A) {
					c120s01adao.save(((C120S01A) model));
				} else if (model instanceof C120S01B) {
					c120s01bdao.save(((C120S01B) model));
				} else if (model instanceof C120S01C) {
					c120s01cdao.save(((C120S01C) model));
				} else if (model instanceof C120S01D) {
					c120s01ddao.save(((C120S01D) model));
				} else if (model instanceof C120S01E) {
					c120s01edao.save(((C120S01E) model));
				} else if (model instanceof C120S01F) {
					c120s01fdao.save(((C120S01F) model));
				} else if (model instanceof C120S01G) {
					c120s01gdao.save(((C120S01G) model));
				} else if (model instanceof C120S01H) {
					c120s01hdao.save(((C120S01H) model));
				} else if (model instanceof C120S01I) {
					c120s01idao.save(((C120S01I) model));
				} else if (model instanceof C120S01J) {
					c120s01jdao.save(((C120S01J) model));
				} else if (model instanceof C120S01K) {
					c120s01kdao.save(((C120S01K) model));
				} else if (model instanceof C120S01L) {
					c120s01ldao.save(((C120S01L) model));
				} else if (model instanceof C120S01M) {
					c120s01mdao.save(((C120S01M) model));
				} else if (model instanceof C120S01N) {
					c120s01ndao.save(((C120S01N) model));
				} else if (model instanceof C120S01P) {
					c120s01pdao.save(((C120S01P) model));
				} else if (model instanceof C120S01Q) {
					c120s01qdao.save(((C120S01Q) model));
				} else if (model instanceof C120S01R) {
					c120s01rdao.save(((C120S01R) model));
				} else if (model instanceof C140M04A) {
					c140m04adao.save(((C140M04A) model));
				} else if (model instanceof C241M01E) {
					c241m01edao.save(((C241M01E) model));
				} else if (model instanceof C999M01B) {
					c999m01bdao.save(((C999M01B) model));
				} else if (model instanceof C999M01C) {
					c999m01cdao.save(((C999M01C) model));
				} else if (model instanceof L000M01A) {
					l000m01adao.save(((L000M01A) model));
				} else if (model instanceof L120M01A) {
					l120m01adao.save(((L120M01A) model));
				} else if (model instanceof L120M01E) {
					l120m01edao.save(((L120M01E) model));
				} else if (model instanceof L140M01O) {
					l140m01odao.save(((L140M01O) model));
				} else if (model instanceof L140S01A) {
					l140s01adao.save(((L140S01A) model));
				} else if (model instanceof L140S02I) {
					l140s02idao.save(((L140S02I) model));
				} else if (model instanceof L170M01A) {
					l170m01adao.save(((L170M01A) model));
				} else if (model instanceof L180M01B) {
					l180m01bdao.save(((L180M01B) model));
				} else if (model instanceof L184M01A) {
					l184m01adao.save(((L184M01A) model));
				} else if (model instanceof L185M01A) {
					l185m01adao.save(((L185M01A) model));
				} else if (model instanceof L999M01B) {
					l999m01bdao.save(((L999M01B) model));
				} else if (model instanceof L999M01C) {
					l999m01cdao.save(((L999M01C) model));
				} else if (model instanceof L999S05A) {
					l999s05adao.save(((L999S05A) model));
				}
			}
		}
	}

	/**
	 * 更新MIS額度序號
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public List<Map<String, Object>> MisCntrNoUpdate(String[] table,
			List<Map<String, Object>> miscntrnodata, String OldCntrNo,
			String NewCntrNo) {
		Map<String, Object> misdata = miscntrnodata.get(0);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < misdata.size(); i++) {
			if (Util.parseInt(misdata.get(table[i])) > 0) {
				miscntrnodataservice.MisUpdatedata(table[i], OldCntrNo,
						NewCntrNo);
				list.add(UpdateResult(table[i], true));
			} else {
				list.add(UpdateResult(table[i], false));
			}
		}
		return list;
	}

	/**
	 * 更新MIS客戶統編
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public List<Map<String, Object>> MisCustIdUpdate(String[] table,
			List<Map<String, Object>> miscntrnodata, String OldCustId,
			String OldDupNo, String NewCustId, String NewDupNo) {
		Map<String, Object> misdata = miscntrnodata.get(0);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < misdata.size(); i++) {
			if (Util.parseInt(misdata.get(table[i])) > 0) {
				miscntrnodataservice.MisUpdatedata(table[i], OldCustId,
						OldDupNo, NewCustId, NewDupNo);
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("UpdateTable", table[i]);
				map.put("UpdateResult", success);
				list.add(map);
			} else {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("UpdateTable", table[i]);
				map.put("UpdateResult", error);
				list.add(map);
			}
		}
		return list;
	}

	/**
	 * 更新DW額度序號
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public List<Map<String, Object>> DwCntrNoUpdate(String[] table,
			List<Map<String, Object>> dwcntrnodata, String OldCntrNo,
			String NewCntrNo) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, Object> dwdata = dwcntrnodata.get(0);
		for (int i = 0; i < dwdata.size(); i++) {
			if (Util.parseInt(dwdata.get(table[i])) > 0) {
				dwcntrnodataservice
						.DwUpdatedata(table[i], OldCntrNo, NewCntrNo);
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("UpdateTable", table[i]);
				map.put("UpdateResult", success);
				list.add(map);
			} else {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("UpdateTable", table[i]);
				map.put("UpdateResult", error);
				list.add(map);
			}
		}
		return list;
	}

	/**
	 * 更新DW客戶統編
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public List<Map<String, Object>> DwCustIdUpdate(String[] table,
			List<Map<String, Object>> dwcntrnodata, String OldCustId,
			String OldDupNo, String NewCustId, String NewDupNo) {
		Map<String, Object> dwdata = dwcntrnodata.get(0);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < dwdata.size(); i++) {
			if (Util.parseInt(dwdata.get(table[i])) > 0) {
				dwcntrnodataservice.DwUpdatedata(table[i], OldCustId, OldDupNo,
						NewCustId, NewDupNo);
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("UpdateTable", table[i]);
				map.put("UpdateResult", success);
				list.add(map);
			} else {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("UpdateTable", table[i]);
				map.put("UpdateResult", error);
				list.add(map);
			}
		}
		return list;
	}

	/**
	 * 確認新額度序號在DW,MIS,R6內是否重複
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public boolean checkNewCntrNo(String[] dwtable, String[] mistable,
			String NewCntrNo) {
		Map<String, Object> dwCheck = dwcntrnodataservice
				.selByCntrNo(NewCntrNo).get(0);
		Map<String, Object> misCheck = miscntrnodataservice.getCntrNoData(
				mistable, NewCntrNo).get(0);
		int dwsum = 0;
		int missum = 0;
		for (int i = 0; i < dwCheck.size(); i++) {
			dwsum = dwsum + Util.parseInt(dwCheck.get(dwtable[i]));
		}
		for (int i = 0; i < misCheck.size(); i++) {
			missum = missum + Util.parseInt(misCheck.get(mistable[i]));
		}
		if (dwsum + missum + R6NewCntrNocheck(NewCntrNo) == 0) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 確認新客戶編號及重複序號在DW,MIS內是否重複
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public boolean checkNewcustId(String[] dwtable, String[] mistable,
			String NewCustId, String NewdupNo) {
		Map<String, Object> dwCheck = dwcntrnodataservice.selBycustIdDupNo(
				dwtable, NewCustId, NewdupNo).get(0);
		Map<String, Object> misCheck = miscntrnodataservice.getcustIdData(
				mistable, NewCustId, NewdupNo).get(0);
		int dwsum = 0;
		int missum = 0;
		for (int i = 0; i < dwCheck.size(); i++) {
			dwsum = dwsum + Util.parseInt(dwCheck.get(dwtable[i]));
		}
		for (int i = 0; i < misCheck.size(); i++) {
			missum = missum + Util.parseInt(misCheck.get(mistable[i]));
		}
		if (dwsum + missum + R6NewcustIdDupNocheck(NewCustId, NewdupNo) == 0) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 統計在R6內有相對應額度序號之數量
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public int R6NewCntrNocheck(String NewCntrNo) {
		int R6num = l120s03adao.findByCntrNo(NewCntrNo).size()
				+ l120s06adao.findByCntrNo(NewCntrNo).size()
				+ l120s06bdao.findByCntrNo(NewCntrNo).size()
				+ l140m01adao.findByCntrNo(NewCntrNo).size()
				+ l160m01bdao.findByCntrNo(NewCntrNo).size()
				+ l161s01adao.findByCntrNo(NewCntrNo).size()
				+ l162s01adao.findByCntrNo(NewCntrNo).size()
				+ l170m01bdao.findByCntrNo(NewCntrNo).size()
				+ l180m01cdao.findByCntrNo(NewCntrNo, "").size()
				+ l210m01adao.findByCntrNo(NewCntrNo).size()
				+ c241m01bdao.findByCntrNo(NewCntrNo).size()
				+ l192s01adao.findByCntrNo(NewCntrNo).size()
				+ l141m01cdao.findByCntrNo(NewCntrNo).size()
				+ l140s02bdao.findByCntrNo(NewCntrNo).size()
				+ l120s01cdao.findByCntrNo(NewCntrNo).size()
				+ l170m01edao.findByCntrNo(NewCntrNo, "T").size()
				+ l230s01adao.findByCntrNo(NewCntrNo).size()
				+ l140s02adao.findByCntrNo(NewCntrNo).size()
				+ c999s01adao.findByCntrNo(NewCntrNo).size()
				+ l140m01edao.findByCntrNo(NewCntrNo).size()
				+ l210s01adao.findByCntrNo(NewCntrNo).size();
		return R6num;
	}

	public int R6NewcustIdDupNocheck(String NewCustId, String NewDupNo) {
		int R6sum = c241m01cdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120s01adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120s01bdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120s01cdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120s01ddao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120s01edao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120s01fdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120s01gdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ _l120s01hdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ _l120s01idao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ _l120s01jdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ _l120s01kdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ _l120s01ldao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120s04adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120s05bdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120s05ddao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120s06adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120s06bdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l140m01jdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l141m01cdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l170m01bdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l170m01cdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l170m01ddao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l170m01edao.findByCustIdDupId(NewCustId, NewDupNo, "T").size()
				+ l170m01fdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l192m01bdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01bdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01cdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01ddao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01edao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01fdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01gdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01hdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01idao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01jdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01kdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01ldao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01mdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c101s01ndao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120m01adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01bdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01cdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01ddao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01edao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01fdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01gdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01hdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01idao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01jdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01kdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01ldao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01mdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c120s01ndao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c140m04adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c241m01edao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c999m01bdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ c999m01cdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120m01adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l120m01edao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l140m01odao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l140s01adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l140s02adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l140s02idao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l162s01adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l170m01adao.findByCustIdDupId(NewCustId, NewDupNo, "").size()
				+ l180m01bdao.findByCustIdDupId(NewCustId, NewDupNo, "").size()
				+ l180m01cdao.findByCustIdDupId(NewCustId, NewDupNo, "").size()
				+ l184m01adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l185m01adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l230s01adao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l999m01bdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l999m01cdao.findByCustIdDupId(NewCustId, NewDupNo).size()
				+ l999s05adao.findByCustIdDupId(NewCustId, NewDupNo).size();
		return R6sum;
	}

	/**
	 * 更新R6額度序號
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public List<Map<String, Object>> R6CntrNoUpdate(String OldCntrNo,
			String NewCntrNo) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<L120S03A> l120s03alist = l120s03adao.findByCntrNo(OldCntrNo);
		List<L120S06A> l120s06alist = l120s06adao.findByCntrNo(OldCntrNo);
		List<L120S06B> l120s06blist = l120s06bdao.findByCntrNo(OldCntrNo);
		List<L140M01A> l140m01alist = l140m01adao.findByCntrNo(OldCntrNo);
		List<L160M01B> l160m01blist = l160m01bdao.findByCntrNo(OldCntrNo);
		List<L161S01A> l161s01alist = l161s01adao.findByCntrNo(OldCntrNo);
		List<L162S01A> l162s01alist = l162s01adao.findByCntrNo(OldCntrNo);
		List<L170M01B> l170m01blist = l170m01bdao.findByCntrNo(OldCntrNo);
		List<L180M01C> l180m01clist = l180m01cdao.findByCntrNo(OldCntrNo, "");
		List<L210M01A> l210m01alist = l210m01adao.findByCntrNo(OldCntrNo);
		List<C241M01B> c241m01blist = c241m01bdao.findByCntrNo(OldCntrNo);
		List<L192S01A> l192s01alist = l192s01adao.findByCntrNo(OldCntrNo);
		List<L141M01C> l141m01clist = l141m01cdao.findByCntrNo(OldCntrNo);
		List<L140S02B> l140s02blist = l140s02bdao.findByCntrNo(OldCntrNo);
		List<L120S01C> l120s01clist = l120s01cdao.findByCntrNo(OldCntrNo);
		List<L170M01E> l170m01elist = l170m01edao.findByCntrNo(OldCntrNo, "T");
		List<L230S01A> l230s01alist = l230s01adao.findByCntrNo(OldCntrNo);
		List<L140S02A> l140s02alist = l140s02adao.findByCntrNo(OldCntrNo);
		List<C999S01A> c999s01alist = c999s01adao.findByCntrNo(OldCntrNo);
		List<L140M01E> l140m01elist = l140m01edao.findByCntrNo(OldCntrNo);
		List<L210S01A> l210s01alist = l210s01adao.findByCntrNo(OldCntrNo);
		if (l120s03alist.size() > 0) {
			for (int i = 0; i < l120s03alist.size(); i++) {
				L120S03A model = l120s03alist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L120S03A", true));
		} else {
			list.add(UpdateResult("L120S03A", false));
		}
		if (l120s06alist.size() > 0) {
			for (int i = 0; i < l120s06alist.size(); i++) {
				L120S06A model = l120s06alist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L120S06A", true));
		} else {
			list.add(UpdateResult("L120S06A", false));
		}
		if (l120s06blist.size() > 0) {
			for (int i = 0; i < l120s06blist.size(); i++) {
				L120S06B model = l120s06blist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L120S06B", true));
		} else {
			list.add(UpdateResult("L120S06B", false));
		}
		if (l140m01alist.size() > 0) {
			for (int i = 0; i < l140m01alist.size(); i++) {
				L140M01A model = l140m01alist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L140M01A", true));
		} else {
			list.add(UpdateResult("L140M01A", false));
		}
		if (l160m01blist.size() > 0) {
			for (int i = 0; i < l160m01blist.size(); i++) {
				L160M01B model = l160m01blist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L160M01B", true));
		} else {
			list.add(UpdateResult("L160M01B", false));
		}
		if (l161s01alist.size() > 0) {
			for (int i = 0; i < l161s01alist.size(); i++) {
				L161S01A model = l161s01alist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L161S01A", true));
		} else {
			list.add(UpdateResult("L161S01A", false));
		}
		if (l162s01alist.size() > 0) {
			for (int i = 0; i < l162s01alist.size(); i++) {
				L162S01A model = l162s01alist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L162S01A", true));
		} else {
			list.add(UpdateResult("L162S01A", false));
		}
		if (l170m01blist.size() > 0) {
			for (int i = 0; i < l170m01blist.size(); i++) {
				L170M01B model = l170m01blist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L170M01B", true));
		} else {
			list.add(UpdateResult("L170M01B", false));
		}
		if (l180m01clist.size() > 0) {
			for (int i = 0; i < l180m01clist.size(); i++) {
				L180M01C model = l180m01clist.get(i);
				model.setElfCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L180M01C", true));
		} else {
			list.add(UpdateResult("L180M01C", false));
		}
		if (l210m01alist.size() > 0) {
			for (int i = 0; i < l210m01alist.size(); i++) {
				L210M01A model = l210m01alist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L210M01A", true));
		} else {
			list.add(UpdateResult("L210M01A", false));
		}
		if (c241m01blist.size() > 0) {
			for (int i = 0; i < c241m01blist.size(); i++) {
				C241M01B model = c241m01blist.get(i);
				model.setQuotaNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("C241M01B", true));
		} else {
			list.add(UpdateResult("C241M01B", false));
		}
		if (l192s01alist.size() > 0) {
			for (int i = 0; i < l192s01alist.size(); i++) {
				L192S01A model = l192s01alist.get(i);
				model.setQuotaNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L192S01A", true));
		} else {
			list.add(UpdateResult("L192S01A", false));
		}
		if (l141m01clist.size() > 0) {
			for (int i = 0; i < l141m01clist.size(); i++) {
				L141M01C model = l141m01clist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L141M01C", true));
		} else {
			list.add(UpdateResult("L141M01C", false));
		}
		if (l140s02blist.size() > 0) {
			for (int i = 0; i < l140s02blist.size(); i++) {
				L140S02B model = l140s02blist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L140S02B", true));
		} else {
			list.add(UpdateResult("L140S02B", false));
		}
		if (l120s01clist.size() > 0) {
			for (int i = 0; i < l120s01clist.size(); i++) {
				L120S01C model = l120s01clist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L120S01C", true));
		} else {
			list.add(UpdateResult("L120S01C", false));
		}
		if (l170m01elist.size() > 0) {
			for (int i = 0; i < l170m01elist.size(); i++) {
				L170M01E model = l170m01elist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L170M01E", true));
		} else {
			list.add(UpdateResult("L170M01E", false));
		}
		if (l230s01alist.size() > 0) {
			for (int i = 0; i < l230s01alist.size(); i++) {
				L230S01A model = l230s01alist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L230S01A", true));
		} else {
			list.add(UpdateResult("L230S01A", false));
		}
		if (l140s02alist.size() > 0) {
			for (int i = 0; i < l140s02alist.size(); i++) {
				L140S02A model = l140s02alist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L140S02A", true));
		} else {
			list.add(UpdateResult("L140S02A", false));
		}
		if (c999s01alist.size() > 0) {
			for (int i = 0; i < c999s01alist.size(); i++) {
				C999S01A model = c999s01alist.get(i);
				model.setCntrNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("C999S01A", true));
		} else {
			list.add(UpdateResult("C999S01A", false));
		}
		if (l140m01elist.size() > 0) {
			for (int i = 0; i < l140m01elist.size(); i++) {
				L140M01E model = l140m01elist.get(i);
				model.setShareNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L140M01E", true));
		} else {
			list.add(UpdateResult("L140M01E", false));
		}
		if (l210s01alist.size() > 0) {
			for (int i = 0; i < l210s01alist.size(); i++) {
				L210S01A model = l210s01alist.get(i);
				model.setShareNo(NewCntrNo);
				save(model);
			}
			list.add(UpdateResult("L210S01A", true));
		} else {
			list.add(UpdateResult("L210S01A", false));
		}

		return list;
	}

	/**
	 * 更新R6客戶編號及重複序號
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public List<Map<String, Object>> R6CustIdDupNoUpdate(String OldCustId,
			String OldDupNo, String NewCustId, String NewDupNo) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<C241M01C> c241m01clist = c241m01cdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S01A> l120s01alist = l120s01adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S01B> l120s01blist = l120s01bdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S01C> l120s01clist = l120s01cdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S01D> l120s01dlist = l120s01ddao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S01E> l120s01elist = l120s01edao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S01F> l120s01flist = l120s01fdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S01G> l120s01glist = l120s01gdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S01H> l120s01hlist = _l120s01hdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S01I> l120s01ilist = _l120s01idao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S01J> l120s01jlist = _l120s01jdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S01K> l120s01klist = _l120s01kdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S01L> l120s01llist = _l120s01ldao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S04A> l120s04alist = l120s04adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S05B> l120s05blist = l120s05bdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S05D> l120s05dlist = l120s05ddao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S06A> l120s06alist = l120s06adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120S06B> l120s06blist = l120s06bdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L140M01J> l140m01jlist = l140m01jdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L141M01C> l141m01clist = l141m01cdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L170M01B> l170m01blist = l170m01bdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L170M01C> l170m01clist = l170m01cdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L170M01D> l170m01dlist = l170m01ddao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L170M01E> l170m01elist = l170m01edao.findByCustIdDupId(OldCustId,
				OldDupNo, "T");
		List<L170M01F> l170m01flist = l170m01fdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L192M01B> l192m01blist = l192m01bdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01A> c101s01alist = c101s01adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01B> c101s01blist = c101s01bdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01C> c101s01clist = c101s01cdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01D> c101s01dlist = c101s01ddao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01E> c101s01elist = c101s01edao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01F> c101s01flist = c101s01fdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01G> c101s01glist = c101s01gdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01H> c101s01hlist = c101s01hdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01I> c101s01ilist = c101s01idao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01J> c101s01jlist = c101s01jdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01K> c101s01klist = c101s01kdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01L> c101s01llist = c101s01ldao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01M> c101s01mlist = c101s01mdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01N> c101s01nlist = c101s01ndao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01P> c101s01plist = c101s01pdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01Q> c101s01qlist = c101s01qdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120M01A> c120m01alist = c120m01adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01A> c120s01alist = c120s01adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01B> c120s01blist = c120s01bdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01C> c120s01clist = c120s01cdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01D> c120s01dlist = c120s01ddao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01E> c120s01elist = c120s01edao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01F> c120s01flist = c120s01fdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01G> c120s01glist = c120s01gdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01H> c120s01hlist = c120s01hdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01I> c120s01ilist = c120s01idao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01J> c120s01jlist = c120s01jdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01K> c120s01klist = c120s01kdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01L> c120s01llist = c120s01ldao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01M> c120s01mlist = c120s01mdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01N> c120s01nlist = c120s01ndao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01P> c120s01plist = c120s01pdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01Q> c120s01qlist = c120s01qdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C140M04A> c140m04alist = c140m04adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C241M01B> c241m01blist = c241m01bdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C241M01E> c241m01elist = c241m01edao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C999M01B> c999m01blist = c999m01bdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C999M01C> c999m01clist = c999m01cdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L000M01A> l000m01alist = l000m01adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120M01A> l120m01alist = l120m01adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L120M01E> l120m01elist = l120m01edao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L140M01O> l140m01olist = l140m01odao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L140S01A> l140s01alist = l140s01adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L140S02A> l140s02alist = l140s02adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L140S02I> l140s02ilist = l140s02idao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L162S01A> l162s01alist = l162s01adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L170M01A> l170m01alist = l170m01adao.findByCustIdDupId(OldCustId,
				OldDupNo, "");
		List<L180M01B> l180m01blist = l180m01bdao.findByCustIdDupId(OldCustId,
				OldDupNo, "");
		List<L180M01C> l180m01clist = l180m01cdao.findByCustIdDupId(OldCustId,
				OldDupNo, "");
		List<L184M01A> l184m01alist = l184m01adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L185M01A> l185m01alist = l185m01adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L230S01A> l230s01alist = l230s01adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L999M01B> l999m01blist = l999m01bdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L999M01C> l999m01clist = l999m01cdao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<L999S05A> l999s05alist = l999s05adao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S01Y> c101s01ylist = c101s01ydao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S01Y> c120s01ylist = c120s01ydao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C101S02A> c101s02alist = c101s02aDao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C120S02A> c120s02alist = c120s02aDao.findByCustIdDupId(OldCustId,
				OldDupNo);
		List<C160S02A> c160s02alist = c160s02aDao.findByCustIdDupId(OldCustId,
				OldDupNo);
		if (c101s01alist.size() > 0) {
			for (int i = 0; i < c101s01alist.size(); i++) {
				C101S01A model = c101s01alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01A", true));
		} else {
			list.add(UpdateResult("C101S01A", false));
		}
		if (c101s01blist.size() > 0) {
			for (int i = 0; i < c101s01blist.size(); i++) {
				C101S01B model = c101s01blist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01B", true));
		} else {
			list.add(UpdateResult("C101S01B", false));
		}
		if (c101s01clist.size() > 0) {
			for (int i = 0; i < c101s01clist.size(); i++) {
				C101S01C model = c101s01clist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01C", true));
		} else {
			list.add(UpdateResult("C101S01C", false));
		}
		if (c101s01dlist.size() > 0) {
			for (int i = 0; i < c101s01dlist.size(); i++) {
				C101S01D model = c101s01dlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01D", true));
		} else {
			list.add(UpdateResult("C101S01D", false));
		}
		if (c101s01elist.size() > 0) {
			for (int i = 0; i < c101s01elist.size(); i++) {
				C101S01E model = c101s01elist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01E", true));
		} else {
			list.add(UpdateResult("C101S01E", false));
		}
		if (c101s01flist.size() > 0) {
			for (int i = 0; i < c101s01flist.size(); i++) {
				C101S01F model = c101s01flist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01F", true));
		} else {
			list.add(UpdateResult("C101S01F", false));
		}
		if (c101s01glist.size() > 0) {
			for (int i = 0; i < c101s01glist.size(); i++) {
				C101S01G model = c101s01glist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01G", true));
		} else {
			list.add(UpdateResult("C101S01G", false));
		}
		if (c101s01hlist.size() > 0) {
			for (int i = 0; i < c101s01hlist.size(); i++) {
				C101S01H model = c101s01hlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01H", true));
		} else {
			list.add(UpdateResult("C101S01H", false));
		}
		if (c101s01ilist.size() > 0) {
			for (int i = 0; i < c101s01ilist.size(); i++) {
				C101S01I model = c101s01ilist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01I", true));
		} else {
			list.add(UpdateResult("C101S01I", false));
		}
		if (c101s01jlist.size() > 0) {
			for (int i = 0; i < c101s01jlist.size(); i++) {
				C101S01J model = c101s01jlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01J", true));
		} else {
			list.add(UpdateResult("C101S01J", false));
		}
		if (c101s01klist.size() > 0) {
			for (int i = 0; i < c101s01klist.size(); i++) {
				C101S01K model = c101s01klist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01K", true));
		} else {
			list.add(UpdateResult("C101S01K", false));
		}
		if (c101s01llist.size() > 0) {
			for (int i = 0; i < c101s01llist.size(); i++) {
				C101S01L model = c101s01llist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01L", true));
		} else {
			list.add(UpdateResult("C101S01L", false));
		}
		if (c101s01mlist.size() > 0) {
			for (int i = 0; i < c101s01mlist.size(); i++) {
				C101S01M model = c101s01mlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01M", true));
		} else {
			list.add(UpdateResult("C101S01M", false));
		}
		if (c101s01nlist.size() > 0) {
			for (int i = 0; i < c101s01nlist.size(); i++) {
				C101S01N model = c101s01nlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01N", true));
		} else {
			list.add(UpdateResult("C101S01N", false));
		}
		if (c101s01plist.size() > 0) {
			for (int i = 0; i < c101s01plist.size(); i++) {
				C101S01P model = c101s01plist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01P", true));
		} else {
			list.add(UpdateResult("C101S01P", false));
		}
		if (c101s01qlist.size() > 0) {
			for (int i = 0; i < c101s01qlist.size(); i++) {
				C101S01Q model = c101s01qlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01Q", true));
		} else {
			list.add(UpdateResult("C101S01Q", false));
		}
		if (c120m01alist.size() > 0) {
			for (int i = 0; i < c120m01alist.size(); i++) {
				C120M01A model = c120m01alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120M01A", true));
		} else {
			list.add(UpdateResult("C120M01A", false));
		}
		if (c120s01alist.size() > 0) {
			for (int i = 0; i < c120s01alist.size(); i++) {
				C120S01A model = c120s01alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01A", true));
		} else {
			list.add(UpdateResult("C120S01A", false));
		}
		if (c120s01blist.size() > 0) {
			for (int i = 0; i < c120s01blist.size(); i++) {
				C120S01B model = c120s01blist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01B", true));
		} else {
			list.add(UpdateResult("C120S01B", false));
		}
		if (c120s01clist.size() > 0) {
			for (int i = 0; i < c120s01clist.size(); i++) {
				C120S01C model = c120s01clist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01C", true));
		} else {
			list.add(UpdateResult("C120S01C", false));
		}
		if (c120s01dlist.size() > 0) {
			for (int i = 0; i < c120s01dlist.size(); i++) {
				C120S01D model = c120s01dlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01D", true));
		} else {
			list.add(UpdateResult("C120S01D", false));
		}
		if (c120s01elist.size() > 0) {
			for (int i = 0; i < c120s01elist.size(); i++) {
				C120S01E model = c120s01elist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01E", true));
		} else {
			list.add(UpdateResult("C120S01E", false));
		}
		if (c120s01flist.size() > 0) {
			for (int i = 0; i < c120s01flist.size(); i++) {
				C120S01F model = c120s01flist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01F", true));
		} else {
			list.add(UpdateResult("C120S01F", false));
		}
		if (c120s01glist.size() > 0) {
			for (int i = 0; i < c120s01glist.size(); i++) {
				C120S01G model = c120s01glist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01G", true));
		} else {
			list.add(UpdateResult("C120S01G", false));
		}
		if (c120s01hlist.size() > 0) {
			for (int i = 0; i < c120s01hlist.size(); i++) {
				C120S01H model = c120s01hlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01H", true));
		} else {
			list.add(UpdateResult("C120S01H", false));
		}
		if (c120s01ilist.size() > 0) {
			for (int i = 0; i < c120s01ilist.size(); i++) {
				C120S01I model = c120s01ilist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01I", true));
		} else {
			list.add(UpdateResult("C120S01I", false));
		}
		if (c120s01jlist.size() > 0) {
			for (int i = 0; i < c120s01jlist.size(); i++) {
				C120S01J model = c120s01jlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01J", true));
		} else {
			list.add(UpdateResult("C120S01J", false));
		}
		if (c120s01klist.size() > 0) {
			for (int i = 0; i < c120s01klist.size(); i++) {
				C120S01K model = c120s01klist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01K", true));
		} else {
			list.add(UpdateResult("C120S01K", false));
		}
		if (c120s01llist.size() > 0) {
			for (int i = 0; i < c120s01llist.size(); i++) {
				C120S01L model = c120s01llist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01L", true));
		} else {
			list.add(UpdateResult("C120S01L", false));
		}
		if (c120s01mlist.size() > 0) {
			for (int i = 0; i < c120s01mlist.size(); i++) {
				C120S01M model = c120s01mlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01M", true));
		} else {
			list.add(UpdateResult("C120S01M", false));
		}
		if (c120s01nlist.size() > 0) {
			for (int i = 0; i < c120s01nlist.size(); i++) {
				C120S01N model = c120s01nlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01N", true));
		} else {
			list.add(UpdateResult("C120S01N", false));
		}
		if (c120s01plist.size() > 0) {
			for (int i = 0; i < c120s01plist.size(); i++) {
				C120S01P model = c120s01plist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01P", true));
		} else {
			list.add(UpdateResult("C120S01P", false));
		}
		if (c120s01qlist.size() > 0) {
			for (int i = 0; i < c120s01qlist.size(); i++) {
				C120S01Q model = c120s01qlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01Q", true));
		} else {
			list.add(UpdateResult("C120S01Q", false));
		}
		if (c140m04alist.size() > 0) {
			for (int i = 0; i < c140m04alist.size(); i++) {
				C140M04A model = c140m04alist.get(i);
				model.setPcId(NewCustId);
				model.setPcDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C140M04A", true));
		} else {
			list.add(UpdateResult("C140M04A", false));
		}
		if (c241m01blist.size() > 0) {
			for (int i = 0; i < c241m01blist.size(); i++) {
				C241M01B model = c241m01blist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C241M01B", true));
		} else {
			list.add(UpdateResult("C241M01B", false));
		}
		if (c241m01clist.size() > 0) {
			for (int i = 0; i < c241m01clist.size(); i++) {
				C241M01C model = c241m01clist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C241M01C", true));
		} else {
			list.add(UpdateResult("C241M01C", false));
		}
		if (c241m01elist.size() > 0) {
			for (int i = 0; i < c241m01elist.size(); i++) {
				C241M01E model = c241m01elist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C241M01E", true));
		} else {
			list.add(UpdateResult("C241M01E", false));
		}
		if (c999m01blist.size() > 0) {
			for (int i = 0; i < c999m01blist.size(); i++) {
				C999M01B model = c999m01blist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C999M01B", true));
		} else {
			list.add(UpdateResult("C999M01B", false));
		}
		if (c999m01clist.size() > 0) {
			for (int i = 0; i < c999m01clist.size(); i++) {
				C999M01C model = c999m01clist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C999M01C", true));
		} else {
			list.add(UpdateResult("C999M01C", false));
		}
		if (l000m01alist.size() > 0) {
			for (int i = 0; i < l000m01alist.size(); i++) {
				L000M01A model = l000m01alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L000M01A", true));
		} else {
			list.add(UpdateResult("L000M01A", false));
		}

		if (l120m01alist.size() > 0) {
			for (int i = 0; i < l120m01alist.size(); i++) {
				L120M01A model = l120m01alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120M01A", true));
		} else {
			list.add(UpdateResult("L120M01A", false));
		}
		if (l120m01elist.size() > 0) {
			for (int i = 0; i < l120m01elist.size(); i++) {
				L120M01E model = l120m01elist.get(i);
				model.setDocCustId(NewCustId);
				model.setDocDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120M01E", true));
		} else {
			list.add(UpdateResult("L120M01E", false));
		}
		if (l120s01alist.size() > 0) {
			for (int i = 0; i < l120s01alist.size(); i++) {
				L120S01A model = l120s01alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S01A", true));
		} else {
			list.add(UpdateResult("L120S01A", false));
		}
		if (l120s01blist.size() > 0) {
			for (int i = 0; i < l120s01blist.size(); i++) {
				L120S01B model = l120s01blist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S01B", true));
		} else {
			list.add(UpdateResult("L120S01B", false));
		}
		if (l120s01clist.size() > 0) {
			for (int i = 0; i < l120s01clist.size(); i++) {
				L120S01C model = l120s01clist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S01C", true));
		} else {
			list.add(UpdateResult("L120S01C", false));
		}
		if (l120s01dlist.size() > 0) {
			for (int i = 0; i < l120s01dlist.size(); i++) {
				L120S01D model = l120s01dlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S01D", true));
		} else {
			list.add(UpdateResult("L120S01D", false));
		}
		if (l120s01elist.size() > 0) {
			for (int i = 0; i < l120s01elist.size(); i++) {
				L120S01E model = l120s01elist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S01E", true));
		} else {
			list.add(UpdateResult("L120S01E", false));
		}
		if (l120s01flist.size() > 0) {
			for (int i = 0; i < l120s01flist.size(); i++) {
				L120S01F model = l120s01flist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S01F", true));
		} else {
			list.add(UpdateResult("L120S01F", false));
		}
		if (l120s01glist.size() > 0) {
			for (int i = 0; i < l120s01glist.size(); i++) {
				L120S01G model = l120s01glist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S01G", true));
		} else {
			list.add(UpdateResult("L120S01G", false));
		}
		if (l120s01hlist.size() > 0) {
			for (int i = 0; i < l120s01hlist.size(); i++) {
				L120S01H model = l120s01hlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S01H", true));
		} else {
			list.add(UpdateResult("L120S01H", false));
		}
		if (l120s01ilist.size() > 0) {
			for (int i = 0; i < l120s01ilist.size(); i++) {
				L120S01I model = l120s01ilist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S01I", true));
		} else {
			list.add(UpdateResult("L120S01I", false));
		}
		if (l120s01jlist.size() > 0) {
			for (int i = 0; i < l120s01jlist.size(); i++) {
				L120S01J model = l120s01jlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S01J", true));
		} else {
			list.add(UpdateResult("L120S01J", false));
		}
		if (l120s01klist.size() > 0) {
			for (int i = 0; i < l120s01klist.size(); i++) {
				L120S01K model = l120s01klist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S01K", true));
		} else {
			list.add(UpdateResult("L120S01K", false));
		}
		if (l120s01llist.size() > 0) {
			for (int i = 0; i < l120s01llist.size(); i++) {
				L120S01L model = l120s01llist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S01L", true));
		} else {
			list.add(UpdateResult("L120S01L", false));
		}
		if (l120s04alist.size() > 0) {
			for (int i = 0; i < l120s04alist.size(); i++) {
				L120S04A model = l120s04alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S04A", true));
		} else {
			list.add(UpdateResult("L120S04A", false));
		}
		if (l120s05blist.size() > 0) {
			for (int i = 0; i < l120s05blist.size(); i++) {
				L120S05B model = l120s05blist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S05B", true));
		} else {
			list.add(UpdateResult("L120S05B", false));
		}
		if (l120s05dlist.size() > 0) {
			for (int i = 0; i < l120s05dlist.size(); i++) {
				L120S05D model = l120s05dlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S05D", true));
		} else {
			list.add(UpdateResult("L120S05D", false));
		}
		if (l120s06alist.size() > 0) {
			for (int i = 0; i < l120s06alist.size(); i++) {
				L120S06A model = l120s06alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S06A", true));
		} else {
			list.add(UpdateResult("L120S06A", false));
		}
		if (l120s06blist.size() > 0) {
			for (int i = 0; i < l120s06blist.size(); i++) {
				L120S06B model = l120s06blist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L120S06B", true));
		} else {
			list.add(UpdateResult("L120S06B", false));
		}
		if (l140m01jlist.size() > 0) {
			for (int i = 0; i < l140m01jlist.size(); i++) {
				L140M01J model = l140m01jlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L140M01J", true));
		} else {
			list.add(UpdateResult("L140M01J", false));
		}
		if (l140m01olist.size() > 0) {
			for (int i = 0; i < l140m01olist.size(); i++) {
				L140M01O model = l140m01olist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L140M01O", true));
		} else {
			list.add(UpdateResult("L140M01O", false));
		}
		if (l140s01alist.size() > 0) {
			for (int i = 0; i < l140s01alist.size(); i++) {
				L140S01A model = l140s01alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L140S01A", true));
		} else {
			list.add(UpdateResult("L140S01A", false));
		}
		if (l140s02alist.size() > 0) {
			for (int i = 0; i < l140s02alist.size(); i++) {
				L140S02A model = l140s02alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L140S02A", true));
		} else {
			list.add(UpdateResult("L140S02A", false));
		}
		if (l140s02ilist.size() > 0) {
			for (int i = 0; i < l140s02ilist.size(); i++) {
				L140S02I model = l140s02ilist.get(i);
				model.setStdCustId(NewCustId);
				model.setStdDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L140S02I", true));
		} else {
			list.add(UpdateResult("L140S02I", false));
		}
		if (l141m01clist.size() > 0) {
			for (int i = 0; i < l141m01clist.size(); i++) {
				L141M01C model = l141m01clist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L141M01C", true));
		} else {
			list.add(UpdateResult("L141M01C", false));
		}
		if (l162s01alist.size() > 0) {
			for (int i = 0; i < l162s01alist.size(); i++) {
				L162S01A model = l162s01alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L162S01A", true));
		} else {
			list.add(UpdateResult("L162S01A", false));
		}
		if (l170m01alist.size() > 0) {
			for (int i = 0; i < l170m01alist.size(); i++) {
				L170M01A model = l170m01alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L170M01A", true));
		} else {
			list.add(UpdateResult("L170M01A", false));
		}
		if (l170m01blist.size() > 0) {
			for (int i = 0; i < l170m01blist.size(); i++) {
				L170M01B model = l170m01blist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L170M01B", true));
		} else {
			list.add(UpdateResult("L170M01B", false));
		}
		if (l170m01clist.size() > 0) {
			for (int i = 0; i < l170m01clist.size(); i++) {
				L170M01C model = l170m01clist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L170M01C", true));
		} else {
			list.add(UpdateResult("L170M01C", false));
		}
		if (l170m01dlist.size() > 0) {
			for (int i = 0; i < l170m01dlist.size(); i++) {
				L170M01D model = l170m01dlist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L170M01D", true));
		} else {
			list.add(UpdateResult("L170M01D", false));
		}
		if (l170m01elist.size() > 0) {
			for (int i = 0; i < l170m01elist.size(); i++) {
				L170M01E model = l170m01elist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L170M01E", true));
		} else {
			list.add(UpdateResult("L170M01E", false));
		}
		if (l170m01flist.size() > 0) {
			for (int i = 0; i < l170m01flist.size(); i++) {
				L170M01F model = l170m01flist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L170M01F", true));
		} else {
			list.add(UpdateResult("L170M01F", false));
		}
		if (l180m01blist.size() > 0) {
			for (int i = 0; i < l180m01blist.size(); i++) {
				L180M01B model = l180m01blist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L180M01B", true));
		} else {
			list.add(UpdateResult("L180M01B", false));
		}
		if (l180m01clist.size() > 0) {
			for (int i = 0; i < l180m01clist.size(); i++) {
				L180M01C model = l180m01clist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L180M01C", true));
		} else {
			list.add(UpdateResult("L180M01C", false));
		}
		if (l184m01alist.size() > 0) {
			for (int i = 0; i < l184m01alist.size(); i++) {
				L184M01A model = l184m01alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L184M01A", true));
		} else {
			list.add(UpdateResult("L184M01A", false));
		}
		if (l185m01alist.size() > 0) {
			for (int i = 0; i < l185m01alist.size(); i++) {
				L185M01A model = l185m01alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L185M01A", true));
		} else {
			list.add(UpdateResult("L185M01A", false));
		}
		if (l192m01blist.size() > 0) {
			for (int i = 0; i < l192m01blist.size(); i++) {
				L192M01B model = l192m01blist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L192M01B", true));
		} else {
			list.add(UpdateResult("L192M01B", false));
		}
		if (l230s01alist.size() > 0) {
			for (int i = 0; i < l230s01alist.size(); i++) {
				L230S01A model = l230s01alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L230S01A", true));
		} else {
			list.add(UpdateResult("L230S01A", false));
		}
		if (l999m01blist.size() > 0) {
			for (int i = 0; i < l999m01blist.size(); i++) {
				L999M01B model = l999m01blist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L999M01B", true));
		} else {
			list.add(UpdateResult("L999M01B", false));
		}
		if (l999m01clist.size() > 0) {
			for (int i = 0; i < l999m01clist.size(); i++) {
				L999M01C model = l999m01clist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L999M01C", true));
		} else {
			list.add(UpdateResult("L999M01C", false));
		}
		if (l999s05alist.size() > 0) {
			for (int i = 0; i < l999s05alist.size(); i++) {
				L999S05A model = l999s05alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("L999S05A", true));
		} else {
			list.add(UpdateResult("L999S05A", false));
		}
		if (c101s01ylist.size() > 0) {
			for (int i = 0; i < c101s01ylist.size(); i++) {
				C101S01Y model = c101s01ylist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S01Y", true));
		} else {
			list.add(UpdateResult("C101S01Y", false));
		}
		if (c120s01ylist.size() > 0) {
			for (int i = 0; i < c120s01ylist.size(); i++) {
				C120S01Y model = c120s01ylist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S01Y", true));
		} else {
			list.add(UpdateResult("C120S01Y", false));
		}
		if (c101s02alist.size() > 0) {
			for (int i = 0; i < c101s02alist.size(); i++) {
				C101S02A model = c101s02alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C101S02A", true));
		} else {
			list.add(UpdateResult("C101S02A", false));
		}
		if (c120s02alist.size() > 0) {
			for (int i = 0; i < c120s02alist.size(); i++) {
				C120S02A model = c120s02alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C120S02A", true));
		} else {
			list.add(UpdateResult("C120S02A", false));
		}
		if (c160s02alist.size() > 0) {
			for (int i = 0; i < c160s02alist.size(); i++) {
				C160S02A model = c160s02alist.get(i);
				model.setCustId(NewCustId);
				model.setDupNo(NewDupNo);
				save(model);
			}
			list.add(UpdateResult("C160S02A", true));
		} else {
			list.add(UpdateResult("C160S02A", false));
		}
		return list;
	}

	public Map<String, Object> UpdateResult(String updatemodel, boolean check) {
		Map<String, Object> checkResult = new HashMap<String, Object>();
		if (check) {
			checkResult.put("UpdateTable", updatemodel);
			checkResult.put("UpdateResult", success);
		} else {
			checkResult.put("UpdateTable", updatemodel);
			checkResult.put("UpdateResult", error);
		}
		return checkResult;
	}

}
