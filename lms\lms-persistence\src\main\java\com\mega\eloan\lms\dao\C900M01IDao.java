/* 
 * C900M01IDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01I;

/** ID重配號記錄檔 **/
public interface C900M01IDao extends IGenericDao<C900M01I> {

	public C900M01I findByOid(String oid);
	
	public C900M01I findByMainId(String mainId);
	
	public C900M01I findByUniqueKey(String custId, String dupNo, String ownBrId, String orgCustId, String orgDupNo);

}