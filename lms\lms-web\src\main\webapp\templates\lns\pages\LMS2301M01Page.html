<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="innerPageBody">
        <script type="text/javascript">
            loadScript('pagejs/lns/LMS2301M01Page');
        </script>
            <div class="button-menu funcContainer" id="buttonPanel">
                <!-- "_btnDOC_EDITING -->
                <th:block th:if="${_btnDOC_EDITING_visible}">
                    <button id="btnSave">
                        <span class="ui-icon ui-icon-jcs-04" ></span><th:block th:text="#{'button.save'}">儲存</th:block>
                    </button>
                    <button id="btnSend">
                        <span class="ui-icon ui-icon-jcs-02" ></span><th:block th:text="#{'button.send'}">呈主管覆核</th:block>
                    </button>
                </th:block>
                <!-- _btnWAIT_APPROVE -->
                <th:block th:if="${_btnWAIT_APPROVE_visible}">
                    <button id="btnCheck">
                        <span class="ui-icon ui-icon-jcs-106" ></span><th:block th:text="#{'button.check'}">覆核</th:block>
                    </button>
                </th:block>
                <button id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <th:block th:text="#{'button.exit'}">離開</th:block>
                </button>
            </div>
            <div class="tit2 color-black">
                <span><th:block th:text="#{'l230m01a.title03'}">已核准授信額度辦理狀態報送作業</th:block>： </span>
                (<span id="showTypCd" class="text-red"></span>)<span id="showCustId" class="color-blue"></span>
            </div>
            <div class="tabs doc-tabs">
                <ul>
                    <li><!-- LMS230S01Panel --><a href="#tab-01" goto="01"><b><th:block th:text="#{'doc.docinfo'}">文件資訊</th:block></b></a></li>
                    <li><!-- LMS230S02Panel --><a href="#tab-02" goto="02"><b><th:block th:text="#{'doc.content'}">報送內容</th:block></b></a></li>
                    <li><!-- LMS230S03Panel --><a href="#tab-03" goto="03"><b><th:block th:text="#{'doc.cntrDoc'}">額度明細表資訊</th:block></b></a></li>
                </ul>
                <div class="tabCtx-warp">
                    <input type="hidden" id="mainOid" name="mainOid"/>
                    <input type="hidden" id="mainId" name="mainId"/>
                    <div th:id="${tabIdx}" th:include="${panelName} :: ${panelFragmentName}"></div>
                </div>
            </div><!--呈主管選擇-->
            <div id="selectBossBox" style="display:none;">
                <form id="selectBossForm">
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="hd1" width="60%">
                                <th:block th:text="#{'l230m01a.title11'}"><!--授信主管--></th:block>&nbsp;&nbsp;
                            </td>
                            <td width="40%">
                                <select id="accountPerson" name="accountPerson" class="boss">
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                                <th:block th:text="#{'l230m01a.title10'}"><!--  經副襄理--></th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <select id="manager" name="manager" class="boss">
                                </select>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            <!--覆核視窗-->
            <div id="openCheckBox" style="display:none">
                <div>
                    <label>
                        <input name="checkRadio" type="radio" value="2">
                        <th:block th:text="#{'l230m01a.title02'}"><!--  核准 & 傳送授管處--></th:block>
                    </label>
                    <br/>
                    <label>
                        <input name="checkRadio" type="radio" value="1">
                        <th:block th:text="#{'l230m01a.title01'}"><!--  退回經辦修改--></th:block>
                    </label>
                </div>
            </div>
            <!--輸入核定日期視窗-->
            <div id="openChecDatekBox" style="display:none">
                <div>
                    <input id="forCheckDate" type="text" size="10" maxlength="10" class="date">
                </div>
            </div>
            
        </th:block>
    </body>
</html>
