package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MISSEFService;

@Service
public class MISSEFServiceImpl extends AbstractMF<PERSON><PERSON>anJdbc implements
		MISSEFService {

	@Override
	public Map<String, Object> getAll(String custId, String dupNo) {
		return this.getJdbc().queryForMap("MISSEF.findByCustId",
				new Object[] { custId, dupNo });
	}

}
