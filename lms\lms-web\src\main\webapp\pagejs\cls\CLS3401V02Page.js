var _handler = "cls3401m01formhandler";
var cntrNo_grid_height = 250;
$(function(){	
	
	//default div
	var $gridview = $("#gridview").iGrid({
        handler: "cls3401gridhandler",
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        multiselect: false,  
        sortname: 'approveTime|custId' ,
       	sortorder: 'desc|asc' ,
        postData: {
            formAction: "queryView",
            docStatus : viewstatus	
        },
        colModel: [
            {
            	colHeader: "",name: 'oid', hidden: true
	        }, {
	        	colHeader: "",name: 'mainId', hidden: true
	        }, {
	            colHeader: i18n.cls3401v01["C340M01A.custId"], 
	            align: "left", width: 85, sortable: true, name: 'custId',
				onclick : openDoc, formatter : 'click'
	        }, {
	            colHeader: i18n.cls3401v01["C340M01A.custName"], 
	            align: "left", width: 90, sortable: true, name: 'custName'	        
	        }, {
	            colHeader: i18n.cls3401v01["C340M01A.caseNo"],  //案號
	            align: "left", width: 180, sortable: true, name: 'caseNo'
	        }, {
                colHeader: i18n.cls3401v01["C340M01A.ctrType"], //"契約書種類",
	            align: "left", width: 100, sortable: true, name: 'ctrType'
	        }, {
	            colHeader: i18n.cls3401v01["C340M01A.contrNumber"],  //契約書編號
	            align: "left", width: 100, sortable: true, name: 'contrNumber'
	        }, {
	        	colHeader: i18n.cls3401v01["C340M01A.createTime"], //契約建檔時間(若1份簽報書，產出 N次契約，以建檔時間去區別)
	            align: "left", width: 99, sortable: true, name: 'createTime'
	        }, {
	        	colHeader: i18n.cls3401v01["doc.lastUpdater"], //最後異動者
	            align: "left", width: 80, sortable: true, name: 'updater'
	        }, {
	        	colHeader: i18n.cls3401v01["C340M01A.updateTime"], //最後異動時間
	            align: "left", width: 99, sortable: true, name: 'updateTime'
	        }, {
                name: 'docURL', hidden:true
            }
	     ],
		ondblClickRow : function(rowid){
			openDoc(null, null, $gridview.getRowData(rowid));
		}
    });
	

	
	function openDoc(cellvalue, options, rowObject) {
		//ilog.debug(rowObject);		
		$.form.submit({
			url : '..' + rowObject.docURL + "/01",
			data : {
				'oid' : rowObject.oid,
				'mainOid' : rowObject.oid,
				'mainId' : rowObject.mainId,
				'mainDocStatus' : viewstatus
			},
			target : rowObject.oid
		});					
	};
	
	
	
	
    $("#buttonPanel").find("#btnView").on("click",function(){
    	var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
        	//
        }else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").on("click",function(){
    	var _id = "_div_cls3401v01_filter";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' >");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3401v01["C340M01A.custId"]+"</td><td>"
					+"<input type='text' id='search_custId' name='search_custId'  maxlength='10' class='alphanum' >"					
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3401v01["C340M01A.ctrType"]+"</td><td>"
					+"<select id='search_ploanCtrStatus' name='search_ctrType'>"
					+"<option value=''>"+i18n.def.comboSpace+"</option>"
					+"<option value='1'>"+i18n.cls3401v01["C340M01A.ctrType.1"]+"</option>"
					+"<option value='2'>"+i18n.cls3401v01["C340M01A.ctrType.2"]+"</option>"
					+"<option value='3'>"+i18n.cls3401v01["C340M01A.ctrType.3"]+"</option>"
					+"<option value='A'>"+i18n.cls3401v01["C340M01A.ctrType.A"]+"</option>"
					+"<option value='S'>"+i18n.cls3401v01["C340M01A.ctrType.S"]+"</option>"
					+"<option value='B'>"+i18n.cls3401v01["C340M01A.ctrType.B"]+"</option>"
					+"<option value='L'>"+i18n.cls3401v01["C340M01A.ctrType.L"]+"</option>"
					+((userInfo.unitNo=="002")?("<option value='C'>"+i18n.cls3401v01["C340M01A.ctrType.C"]+"</option>"):"")
					+"</select>"
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3401v01["C340M01A.ploanCtrNo"]+"</td><td>"
					+"<input type='text' id='search_ploanCtrNo' name='search_ploanCtrNo'  maxlength='20'>"					
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3401v01["C340M01A.ploanCtrStatus"]+"</td><td>"
					+"<select id='search_ploanCtrStatus' name='search_ploanCtrStatus'>"
					+"<option value=''>"+i18n.def.comboSpace+"</option>"
					+"<option value='1'>"+i18n.cls3401v01["C340M01A.ploanCtrStatus.1"]+"</option>"
					+"<option value='2'>"+i18n.cls3401v01["C340M01A.ploanCtrStatus.2"]+"</option>"
					+"<option value='9'>"+i18n.cls3401v01["C340M01A.ploanCtrStatus.9"]+"</option>"
					+"</select>"
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3401v01["C340M01A.ploanBorrowerIPAddr"]+"</td><td>"
					+"<input type='text' id='search_ploanBorrowerIPAddr' name='search_ploanBorrowerIPAddr'  maxlength='100'>"					
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3401v01["C340M01A.ploanCtrBegDate"]+"</td><td>"
					+"<input type='text' id='ploanCtrBegDate_beg' name='ploanCtrBegDate_beg' maxlength='10' class='date' />"
					+"<input type='text' id='ploanCtrBegDate_end' name='ploanCtrBegDate_end' maxlength='10' class='date' />"					
					+"</td></tr>");
			
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));
		    
		    $("#"+_form).find(".date").filter(function(){
		        return !$(this).prop('readonly');
		    }).datepicker();
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
			//ui_lms2401.msg02=請選擇不覆審原因
	       title: '篩選',
	       width: 620,
           height: 290,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
            	  // 正常的輸入日期格式為 2021-06-22
            	  // 但若 server-side 收到 20210622 會導致 Exception => 送出前，加上  $("#"+_form).valid()
            	  if( $("#"+_form).valid() ){ 
	                  $.thickbox.close();
	                  //=============
	                  $gridview.setGridParam({
	  	                postData: $.extend(
	  	                	{}
	  	                	,$("#"+_form).serializeData()
	  	                ),
	  	                search: true
	                  }).trigger("reloadGrid");
            	  }
               },
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
    
		})
    }).end().find("#btnDelete").click(function(){ //編製中-刪除
    	var row = $gridview.getGridParam('selrow');
		var list = "";
		if(row){
			var data = $gridview.getRowData(row);
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){
					$.ajax({
						handler : _handler,
						type : "POST",
						dataType : "json",
						data :{
							'formAction' : 'delC340M01A',
							'oid' : data.oid,
							'mainOid' : data.oid,
							'mainId' : data.mainId,
							'mainDocStatus' : viewstatus
						}
						}).done(function(obj) {
				        	$gridview.trigger("reloadGrid");
				          	API.showMessage(i18n.def.runSuccess);      
					});
				}
			})				
		}else{
			API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
			return;
		}
    }).end().find("#btnPrintNote").click(function(){
    	$('#downloadContractForm').injectData({'dlContract':'1_V202008'});
    	$('#downloadContractBox').thickbox({
            title:  i18n.cls3401v01['button.PrintNote'] || '',
            width: 550,
            height: 180,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                'sure': function(){
                    var $form = $('#downloadContractForm');
                    if ($form.valid()) {
                        $.thickbox.close();
                        //~~~
                        var fileDownloadName = "download.doc";
                        var dlContract = $form.find("[name=dlContract]:checked").val();
                        if(dlContract == "1_V202401"){
                           	fileDownloadName = i18n.cls3401v01['label.ctrType_1_rptId_V202401']+".docx";
                        }else if(dlContract == "2_V202401"){
                        	fileDownloadName = i18n.cls3401v01['label.ctrType_2_rptId_V202401']+".docx";
                        }else if(dlContract == "3_V202401"){
                           	fileDownloadName = i18n.cls3401v01['label.ctrType_3_rptId_V202401']+".docx";
                        }
                        else{
                        	API.showErrorMessage("error_undefined_dlContract{"+dlContract+"}");
                        }
                    	//~~~
                        $.form.submit({
                        	url: __ajaxHandler,
                     		target : "_blank",
                     		data : {
                     			_pa : 'lmsdownloadformhandler'
                     			,'dlEmptyWord' :'Y'
                     			,'dlContract' : dlContract
                     			,'fileDownloadName': fileDownloadName 
                     			,'serviceName': 'ContractDocService'
                     		}
                     	 });
                        
                        /* 此方式無法下載為指定檔名   
                        $.form.submit({
                            url: webroot + '/app/simple/FileProcessingService',
                            target: "_blank",
                            data: $.extend($form.serializeData(), {'serviceName': "ContractDocService", 'dlEmptyWord':'Y', 'fileDownloadName':fileDownloadName } )
                        }); */
                    }
                },
                'cancel': function(){
                    $.thickbox.close();
                }
            }
        });
    });
	
	
    
    

    
    

	
