package com.mega.eloan.lms.base.pages;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.lms.base.constants.SimplePDFConstant;
import com.mega.sso.context.MegaSSOSecurityContext;

import tw.com.iisi.cap.base.pages.AbstractBasePage;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapByteArrayDownloadResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

public abstract class AbstractSimplePdfPage extends AbstractBasePage implements SimplePDFConstant {

	public Logger logger = LoggerFactory.getLogger(AbstractSimplePdfPage.class);

	protected String serviceName;
	
	@Autowired
	protected HttpServletResponse response;

	@Autowired
	AuthService au;

	@ResponseBody
	@RequestMapping(method = { RequestMethod.POST, RequestMethod.GET })
	public void processView(ModelMap model, HttpServletRequest req) {
		try {
			PageParameters params = convertRequest(req);
			if (beforeExecute(model)) {
				execute(model, params);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			model.addAttribute(PRE_VALIDATE_MESSAGE, e.getMessage());
		}
	}

	public void execute(ModelMap model, PageParameters params) throws CapException {
		this.logger = LoggerFactory.getLogger(getClass());
		ByteArrayOutputStream bo = null;
		try {
			bo = (ByteArrayOutputStream) generateReport(params);
			if (bo != null) {
				bo.flush();
				boolean isInline = getOutputContentType(params).equals("application/pdf");
				IResult fileResult = new CapByteArrayDownloadResult(bo.toByteArray(), getOutputContentType(params),
						getFileName(), isInline);
				fileResult.respondResult(response);
				logger.debug(fileResult.getLogMessage());
			} else {
				handlerException(null);
			}
		} catch (Exception e) {
			handlerException(e);
		} finally {
			IOUtils.closeQuietly(bo);
			bo = null;
		}
	}

	protected OutputStream generateReport(PageParameters params) throws CapException {
		// AbstractReportService creator =
		// SpringContextHelper.getReportService(getReportCreatorName());
//		OutputStream os = creator.generateReport(params);
//		params.put(TOTAL_BOOK_PAGE, String.valueOf(creator.getReportPageCount(params)));
		// return os;
		return null;
	}

	protected void handlerException(Exception e) {
		String strMsg = "";
		if (e != null) {
			strMsg = e.getMessage();
			if (logger.isErrorEnabled()) {
				if (e instanceof CapMessageException) {
					logger.error(strMsg);
				} else {
					logger.error(strMsg, e);
				}
			}
		}
		// 產生報表時發生錯誤。$\{msg\}
		HashMap<String, String> msgMap = new HashMap<String, String>();
		msgMap.put("msg", strMsg);
		String msg = RespMsgHelper.getMessage("EFD0062", msgMap);
		getPageParameters().put(PRE_VALIDATE_MESSAGE, msg);
		SimpleContextHolder.put(PRE_VALIDATE_MESSAGE, msg);
	}

	@Override
	protected boolean beforeExecute(ModelMap model) throws Exception {
		// 建構子的處理移往 beforeExecute
		if (isCheckAuth()) {
			PageParameters parameters = super.getPageParameters();
			int transactionCode = Util.parseInt(parameters.getString(EloanConstants.TRANSACTION_CODE));
			if (!au.auth(MegaSSOSecurityContext.getPGMDept(), MegaSSOSecurityContext.getEloanRoles(), transactionCode,
					AuthType.Query)) {
				Map<String, String> msg = new HashMap<String, String>();
				msg.put("methodName", "");
				msg.put("authType", String.valueOf(AuthType.Query));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0004"), getClass());
			}
		}
		return true;
	}

	abstract protected String getReportCreatorName();

	abstract protected String getFileName();
	
	abstract protected String getFileDownloadServiceName();

	protected String getOutputContentType(PageParameters params) {
		return params.getString("contentType", "application/pdf");
	}

	protected boolean isCheckAuth() {
		return false;
	}

}
