/* 
 * L120S03ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S03A;

/** 資本適足率影響數資料檔 **/
public interface L120S03ADao extends IGenericDao<L120S03A> {

	L120S03A findByOid(String oid);

	List<L120S03A> findByMainId(String mainId);

	L120S03A findByUniqueKey(String mainId, String cntrMainId, String cntrNo);

	List<L120S03A> findByIndex01(String mainId, String cntrMainId, String cntrNo);

	/**
	 * 跟據額度明細表mainid找到
	 * 
	 * @param cntrMainId
	 *            額度明細表mainid
	 * @return
	 */
	List<L120S03A> findByCntrMainId(String[] cntrMainId);

	int delModel(String mainId);

	List<L120S03A> findByCntrNo(String CntrNo);
}