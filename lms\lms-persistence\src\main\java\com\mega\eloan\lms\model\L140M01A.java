/*

 * L140M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.IDataObject;

/** 額度明細表主檔 **/
@NamedEntityGraph(name = "L140M01A-entity-graph", attributeNodes = { 
		@NamedAttributeNode("l141m01b"),
		@NamedAttributeNode("l120m01c"),
		@NamedAttributeNode("l120m01g"),
		@NamedAttributeNode("l140m03a")
		})	
@Entity
@Table(name = "L140M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L140M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	/**
	 * JOIN條件與關聯檔By聯行額度明細表
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "REFMAINID", nullable = false, insertable = false, updatable = false)
	private L141M01B l141m01b;

	public L141M01B getL141M01B() {
		return l141m01b;
	}

	public void setL141M01B(L141M01B l141m01b) {
		this.l141m01b = l141m01b;
	}

	/**
	 * JOIN條件與關聯檔By案件簽報書
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "REFMAINID", nullable = false, insertable = false, updatable = false)
	private L120M01C l120m01c;

	public L120M01C getL120m01c() {
		return l120m01c;
	}

	public void setL120m01c(L120M01C l120m01c) {
		this.l120m01c = l120m01c;
	}

	/**
	 * JOIN條件與團貸檔By額度明細表的mainid
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false)
	private L120M01G l120m01g;

	public void setL120m01g(L120M01G l120m01g) {
		this.l120m01g = l120m01g;
	}

	public L120M01G getL120m01g() {
		return l120m01g;
	}

	/**
	 * JOIN條件與額度敘述說明檔L140M01B
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L140M01B> l140m01b;

	public Set<L140M01B> getL140m01b() {
		return l140m01b;
	}

	public void setL140m01b(Set<L140M01B> l140m01b) {
		this.l140m01b = l140m01b;
	}

	/**
	 * JOIN條件與授信科目L140M01C
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L140M01C> l140m01c;

	public Set<L140M01C> getL140m01c() {
		return l140m01c;
	}

	public void setL140m01c(Set<L140M01C> l140m01c) {
		this.l140m01c = l140m01c;
	}

	/**
	 * JOIN條件與科目限額L140M01D
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L140M01D> l140m01d;

	public Set<L140M01D> getL140m01d() {
		return l140m01d;
	}

	public void setL140m01d(Set<L140M01D> l140m01d) {
		this.l140m01d = l140m01d;
	}

	/**
	 * JOIN條件與額度聯行攤貸比例檔 L140M01E
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L140M01E> l140m01e;

	public Set<L140M01E> getL140m01e() {
		return l140m01e;
	}

	public void setL140m01e(Set<L140M01E> l140m01e) {
		this.l140m01e = l140m01e;
	}

	/**
	 * JOIN條件與動審後額度聯行攤貸比例檔 L140M01E_AF
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L140M01E_AF> l140m01e_af;

	public void setL140m01e_af(Set<L140M01E_AF> l140m01e_af) {
		this.l140m01e_af = l140m01e_af;
	}

	public Set<L140M01E_AF> getL140m01e_af() {
		return l140m01e_af;
	}
	// /**
	// * (排序)JOIN條件與額度聯行攤貸比例檔 L140M01E
	// *
	// */
	// @OneToMany(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	// @JoinColumn(name = "MAINID", referencedColumnName = "MAINID")
	// @OrderBy("createTime")
	// private List<L140M01E> l140m01es;
	//
	// public List<L140M01E> getL140m01es() {
	// return l140m01es;
	// }
	//
	// public void setL140m01es(List<L140M01E> l140m01es) {
	// this.l140m01es = l140m01es;
	// }
	//

	/**
	 * JOIN條件與額度利費率主檔 L140M01F
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L140M01F> l140m01f;

	public Set<L140M01F> getL140m01f() {
		return l140m01f;
	}

	public void setL140m01f(Set<L140M01F> l140m01f) {
		this.l140m01f = l140m01f;
	}

	/**
	 * JOIN條件與連保人資料 L140M01I
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L140M01I> l140m01i;

	public Set<L140M01I> getL140m01i() {
		return l140m01i;
	}

	public void setL140m01i(Set<L140M01I> l140m01i) {
		this.l140m01i = l140m01i;
	}

	/**
	 * JOIN條件與共同借款人資料 L140M01J
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L140M01J> l140m01j;

	public Set<L140M01J> getL140m01j() {
		return l140m01j;
	}

	public void setL140m01j(Set<L140M01J> l140m01j) {
		this.l140m01j = l140m01j;
	}

	/**
	 * JOIN條件 L140M01ATMP1 全文檢索結果檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L140M01ATMP1> l140m01atmp1;

	public Set<L140M01ATMP1> getL140m01atmp1() {
		return l140m01atmp1;
	}

	public void setL140m01atmp1(Set<L140M01ATMP1> l140m01atmp1) {
		this.l140m01atmp1 = l140m01atmp1;
	}

	// *****************************************************************************

	/**
	 * 是否已查詢聯徵中心不動產鑑價資訊 <br/>
	 * 101/10/25新增For個金 Y/N
	 */
	@Column(name = "INQJCIC", length = 1, columnDefinition = "CHAR(1)")
	private String inqJcic;

	/**
	 * 是否已查詢聯徵中心不動產鑑價資訊 <br/>
	 * 101/10/25新增For個金 Y/N
	 */
	public String getInqJcic() {
		return inqJcic;
	}

	/**
	 * 是否已查詢聯徵中心不動產鑑價資訊 <br/>
	 * 101/10/25新增For個金 Y/N
	 */
	public void setInqJcic(String inqJcic) {
		this.inqJcic = inqJcic;
	}

	/**
	 * 平均動用率<br/>
	 * 101/11/23新增for國內企金
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "USEPAR", length = 1, columnDefinition = "DECIMAL(5,2)")
	private BigDecimal usePar;

	/**
	 * 平均動用率<br/>
	 * 101/11/23新增for國內企金
	 */
	public BigDecimal getUsePar() {
		return usePar;
	}

	/**
	 * 平均動用率<br/>
	 * 101/11/23新增for國內企金
	 */
	public void setUsePar(BigDecimal usePar) {
		this.usePar = usePar;
	}

	/**
	 * 平均動用率資料日期<br/>
	 * 101/11/23新增for國內企金
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "USEPARDATE", columnDefinition = "DATE")
	private Date useParDate;

	/**
	 * 平均動用率資料日期<br/>
	 * 101/11/23新增for國內企金
	 */
	public Date getUseParDate() {
		return useParDate;
	}

	/**
	 * 平均動用率資料日期<br/>
	 * 101/11/23新增for國內企金
	 */
	public void setUseParDate(Date useParDate) {
		this.useParDate = useParDate;
	}

	/**
	 * 是否屬於總處300億專款專用短期不循環週轉放款<br/>
	 * 101/11/23新增for國內企金
	 */
	@Column(name = "HEADITEM6", length = 1, columnDefinition = "CHAR(1)")
	private String headItem6;

	/**
	 * 是否屬於總處300億專款專用短期不循環週轉放款<br/>
	 * 101/11/23新增for國內企金
	 */
	public String getHeadItem6() {
		return headItem6;
	}

	/**
	 * 是否屬於總處300億專款專用短期不循環週轉放款<br/>
	 * 101/11/23新增for國內企金
	 */
	public void setHeadItem6(String headItem6) {
		this.headItem6 = headItem6;
	}

	/**
	 * 是否屬興建房屋<br/>
	 * 是-興建房屋自用 |1<br/>
	 * 是-興建房屋非自用 |2<br/>
	 * 是-暫無興建計畫之購地貸款 |3<br/>
	 * 否-非興建房屋 |N<br/>
	 * Codetype='lms140_residential'
	 * 
	 * 
	 * 
	 * ※需檢查額度性質為無擔時要維護此欄位，且借款人銀行法或利害人關係為Y。再動審表時要上傳。此欄位位於「額度種類欄」
	 */
	@Column(name = "RESIDENTIAL", length = 1, columnDefinition = "CHAR(1)")
	private String residential;

	/**
	 * 是否屬興建房屋<br/>
	 * 是-興建房屋自用 |1<br/>
	 * 是-興建房屋非自用 |2<br/>
	 * 是-暫無興建計畫之購地貸款 |3<br/>
	 * 否-非興建房屋 |N<br/>
	 * Codetype='lms140_residential'
	 * 
	 * 
	 * ※需檢查額度性質為無擔時要維護此欄位，且借款人銀行法或利害人關係為Y。再動審表時要上傳。此欄位位於「額度種類欄」
	 */
	public String getResidential() {
		return residential;
	}

	/**
	 * 是否屬興建房屋<br/>
	 * 是-興建房屋自用 |1<br/>
	 * 是-興建房屋非自用 |2<br/>
	 * 是-暫無興建計畫之購地貸款 |3<br/>
	 * 否-非興建房屋 |N<br/>
	 * Codetype='lms140_residential'
	 * 
	 * 
	 * ※需檢查額度性質為無擔時要維護此欄位，且借款人銀行法或利害人關係為Y。再動審表時要上傳。此欄位位於「額度種類欄」
	 */
	public void setResidential(String residential) {
		this.residential = residential;
	}

	/**
	 * 同一人/同一關係企業受限額註計項目<br/>
	 * codetype = lms140_noLoan <br/>
	 * 101/11/23新增for國內企金<br/>
	 * 非授信科目 | 0<br/>
	 * 配合政府政策，經財政部專案核准之專案授信或經中央銀行專案轉融通之授信 | 1<br/>
	 * 對政府機關之授信 | 2<br/>
	 * 以公債，國庫券，中央銀行可轉讓定期存單或本行金融債券為擔保品授信 | 3<br/>
	 * 依加強推動銀行辦理小額放款業務要點辦理之新台幣一百萬元以下授信 | 4<br/>
	 * 配合政府專案不需列入同一人限額計算，但需列入同一關係企業限額中 | 5<br/>
	 * 
	 * 
	 * ※需檢查額度性質為無擔時要維護此欄位，且借款人銀行法或利害人關係為Y。再動審表時要上傳。此欄位位於「額度種類欄」
	 */
	@Column(name = "NOLOAN", length = 1, columnDefinition = "CHAR(1)")
	private String noLoan;

	/**
	 * 同一人/同一關係企業受限額註計項目<br/>
	 * codetype = lms140_noLoan <br/>
	 * 101/11/23新增for國內企金<br/>
	 * 非授信科目 | 0<br/>
	 * 配合政府政策，經財政部專案核准之專案授信或經中央銀行專案轉融通之授信 | 1<br/>
	 * 對政府機關之授信 | 2<br/>
	 * 以公債，國庫券，中央銀行可轉讓定期存單或本行金融債券為擔保品授信 | 3<br/>
	 * 依加強推動銀行辦理小額放款業務要點辦理之新台幣一百萬元以下授信 | 4<br/>
	 * 配合政府專案不需列入同一人限額計算，但需列入同一關係企業限額中 | 5<br/>
	 * 
	 * 
	 * ※需檢查額度性質為無擔時要維護此欄位，且借款人銀行法或利害人關係為Y。再動審表時要上傳。此欄位位於「額度種類欄」
	 */
	public String getNoLoan() {
		return noLoan;
	}

	/**
	 * 同一人/同一關係企業受限額註計項目<br/>
	 * codetype = lms140_noLoan 101/11/23新增for國內企金<br/>
	 * 非授信科目 | 0<br/>
	 * 配合政府政策，經財政部專案核准之專案授信或經中央銀行專案轉融通之授信 | 1<br/>
	 * 對政府機關之授信 | 2<br/>
	 * 以公債，國庫券，中央銀行可轉讓定期存單或本行金融債券為擔保品授信 | 3<br/>
	 * 依加強推動銀行辦理小額放款業務要點辦理之新台幣一百萬元以下授信 | 4<br/>
	 * 配合政府專案不需列入同一人限額計算，但需列入同一關係企業限額中 | 5<br/>
	 * 
	 * 
	 * ※需檢查額度性質為無擔時要維護此欄位，且借款人銀行法或利害人關係為Y。再動審表時要上傳。此欄位位於「額度種類欄」
	 */
	public void setNoLoan(String noLoan) {
		this.noLoan = noLoan;
	}

	/**
	 * 來源註記
	 * <p/>
	 * 100/12/06新增<br/>
	 * 0.案件產生(for國內個金舊案)<br/>
	 * 1.新增額度明細表(空白)<br/>
	 * 2.複製額度明細表<br/>
	 * 3.轉入額度明細表<br/>
	 * 4.續約/條件變更產生<br/>
	 * 5.帳務 (由帳務系統資訊轉入之資料LN.LNF) <br/>
	 * 6.核准 (由R6轉入之資料) <br/>
	 * 7.動用 (由mis.Elf500轉入之資料)<br/>
	 * 8.帳務銷戶<br/>
	 * 9.核准取銷
	 */
	@Column(name = "DATASRC", length = 1, columnDefinition = "CHAR(1)")
	private String dataSrc;

	/**
	 * 來源文件編號
	 * <p/>
	 * 101/07/17新增<br/>
	 * for「續約/條件變更」<br/>
	 * (為取得變更前資料用)
	 */
	@Column(name = "MAINIDSRC", length = 32, columnDefinition = "CHAR(32)")
	private String mainIdSrc;

	/** 列印順序 **/
	@Column(name = "PRINTSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer printSeq;

	/**
	 * 案件號碼
	 * <p/>
	 * 100/11/24調整
	 */
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/** 簽案日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/**
	 * 案件授權等級
	 * <p/>
	 * 1.分行授權內<br/>
	 * 2.營運中心授權內<br/>
	 * 3.分行授權外<br/>
	 * 4.營運中心授權外
	 */
	@Column(name = "GRANT", length = 1, columnDefinition = "CHAR(1)")
	private String grant;

	/** 分行放行時間 **/
	@Column(name = "BRDTIME", columnDefinition = "TIMESTAMP")
	private Timestamp brdTime;

	/** 營運中心放行時間 **/
	@Column(name = "AREASENDINFO", columnDefinition = "TIMESTAMP")
	private Timestamp areaSendInfo;

	/**
	 * 婉卻代碼
	 * <p/>
	 * 101/02/22調整
	 */
	@Column(name = "CESRJTCAUSE", length = 2, columnDefinition = "VARCHAR(2)")
	private String cesRjtCause;

	/** 婉卻原因 **/
	@Column(name = "CESRJTREASON", length = 1050, columnDefinition = "VARCHAR(1050)")
	private String cesRjtReason;

	/** 本額度風險歸屬國別 **/
	@Column(name = "RISKAREA", length = 2, columnDefinition = "CHAR(2)")
	private String riskArea;

	/**
	 * 本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 */
	@Column(name = "HEADITEM1", length = 1, columnDefinition = "CHAR(1)")
	private String headItem1;

	/** 保證成數 **/
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "GUTPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal gutPercent;

	/** 信保首次動用有效期限 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "GUTCUTDATE", columnDefinition = "DATE")
	private Date gutCutDate;

	/**
	 * 借款人是否為中小企業
	 * <p/>
	 * Y/N（是/否）<br/>
	 * [個金]利率列印位置<br/>
	 * 0|印於主表<br/>
	 * 1|印於附表(一)<br/>
	 */
	@Column(name = "HEADITEM2", length = 1, columnDefinition = "CHAR(1)")
	private String headItem2;

	/**
	 * 本額度是否為聯貸信保
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "SYNDIPFD", length = 1, columnDefinition = "CHAR(1)")
	private String syndIPFD;

	/**
	 * 是否符合送中小信保基金保證條件
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "HEADITEM3", length = 1, columnDefinition = "CHAR(1)")
	private String headItem3;

	/**
	 * 送保種類
	 * <p/>
	 * 101/01/13因應泰行需求新增<br/>
	 * 1.中小信保基金保證<br/>
	 * 2.海外信保基金保證<br/>
	 * ※國內交易預設1，海外預設2
	 */
	@Column(name = "HEADITEM3TTL", length = 1, columnDefinition = "CHAR(1)")
	private String headItem3Ttl;

	/**
	 * 擔保品是否為股票/開放型基金/受益憑證
	 * <p/>
	 * 101/02/23新增<br/>
	 * Y/N
	 */
	@Column(name = "HEADITEM5", length = 1, columnDefinition = "CHAR(1)")
	private String headItem5;

	/**
	 * 擔保維持率採用類型
	 * <p/>
	 * 101/02/23新增<br/>
	 * 0.標準<br/>
	 * 1.自訂
	 */
	@Column(name = "MRATETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String mRateType;

	/**
	 * 擔保維持率
	 * <p/>
	 * 101/02/23新增<br/>
	 * ※(標準擔保維持率為140%)
	 */
	@Column(name = "MRATE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal mRate;

	/**
	 * 性質
	 * <p/>
	 * 100/10/03 新增11、12、13<br/>
	 * 100/12/05調整成notes代碼<br/>
	 * 100/12/20調整欄位長度150(30<br/>
	 * 複選：<br/>
	 * 1報價(報價|13<br/>
	 * 2新作(新做|1<br/>
	 * 3增額(增額|5<br/>
	 * 4紓困(紓困|10<br/>
	 * 5協議清償(協議清償|12<br/>
	 * 6減額(減額|6<br/>
	 * 7變更條件(變更條件|3<br/>
	 * 8續約(續約|2<br/>
	 * 9提前續約(提前續約|11<br/>
	 * 10展期（不良授信案）(展期(不良授信案)|9<br/>
	 * 11流用(流用|4<br/>
	 * 12取消(取消|8<br/>
	 * 13不變(不變|7
	 */
	@Column(name = "PROPERTY", length = 30, columnDefinition = "VARCHAR(30)")
	private String proPerty;

	/**
	 * 額度控管種類
	 * <p/>
	 * 10.一般<br/>
	 * 20.信保<br/>
	 * 30.聯貸<br/>
	 * 40.合作母<br/>
	 * 41.合作子<br/>
	 * 51.個人戶一般
	 */
	@Column(name = "SNOKIND", length = 2, columnDefinition = "CHAR(2)")
	private String snoKind;

	/** 共用額度序號 **/
	@Column(name = "COMMSNO", length = 12, columnDefinition = "CHAR(12)")
	private String commSno;

	/** 額度序號 **/
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/**
	 * 額度性質
	 * <p/>
	 * 1.擔保<br/>
	 * 2.無擔保/信保<br/>
	 * 100/12/27 修改為代碼 上傳時要用<br/>
	 * S:擔保<br/>
	 * N:無擔保/信保
	 */
	@Column(name = "SBJPROPERTY", length = 1, columnDefinition = "CHAR(1)")
	private String sbjProperty;

	/** 原始額度序號 **/
	@Column(name = "SNOOLD", length = 12, columnDefinition = "CHAR(12)")
	private String snoOld;

	/** 原始控管種類 **/
	@Column(name = "SNOKINDOLD", length = 2, columnDefinition = "CHAR(2)")
	private String snoKindOld;

	/**
	 * 本案是否有同業聯貸案額度
	 * <p/>
	 * Y/N（有/無）<br/>
	 * 100/10/03 額度種類選擇本案是否有同業聯貸案額度為是則每份額度明細表都要提供此選項
	 */
	@Column(name = "UNITCASE2", length = 1, columnDefinition = "CHAR(1)")
	private String unitCase2;

	/**
	 * 加計該額度是否逾越限額
	 * <p/>
	 * Y/N（有/無）<br/>
	 * 100/10/13 新增
	 */
	@Column(name = "HEADITEM4", length = 1, columnDefinition = "CHAR(1)")
	private String headItem4;

	/**
	 * 授信科目性質與額度
	 * <p/>
	 * 100個全型字<br/>
	 * (組合文字)供簽報書案由引進用
	 */
	@Column(name = "GIST", length = 300, columnDefinition = "VARCHAR(300)")
	private String gist;

	/**
	 * 授信科目
	 * <p/>
	 * 101/04/12調整<br/>
	 * 200 ( 300<br/>
	 * 100個全型字(組合文字) <br/>
	 * 102.02.18 欄位擴大 300 -> 1536
	 */
	@Column(name = "LNSUBJECT", length = 1536, columnDefinition = "VARCHAR(1536)")
	private String lnSubject;

	/**
	 * 清償期限
	 * <p/>
	 * 101/04/12調整<br/>
	 * 100 ( 200<br/>
	 * (組合文字) <br/>
	 * 102.02.18 欄位擴大 200 -> 600
	 */
	@Column(name = "PAYDEADLINE", length = 600, columnDefinition = "VARCHAR(600)")
	private String payDeadline;

	/**
	 * 衍生性金融商品期數
	 * <p/>
	 * 1.六個月期（含）以內<br/>
	 * 2.六至十二個月期（含）以內<br/>
	 * 3.二年期（含）以內<br/>
	 * 4.三年期（含）以內<br/>
	 * 5.四年期（含）以內<br/>
	 * 6.五年期（含）以內<br/>
	 * 7.五年期以上
	 */
	@Column(name = "DERIVATIVES", length = 100, columnDefinition = "VARCHAR(100)")
	private String derivatives;

	/**
	 * 是否為衍生性金融商品
	 * <p/>
	 * Y/N
	 */
	@Column(name = "ISDERIVATIVES", length = 1, columnDefinition = "CHAR(1)")
	private String isDerivatives;

	/**
	 * 風險係數（%）
	 * <p/>
	 * 101/02/23調整
	 */
	@Digits(integer = 5, fraction = 4, groups = Check.class)
	@Column(name = "DERIVATIVESNUM", columnDefinition = "DECIMAL(9,4)")
	private BigDecimal derivativesNum;

	/** 前准額度－同前頁 **/
	@Column(name = "LASTVALUEREFP1", length = 1, columnDefinition = "CHAR(1)")
	private String lastValueRefP1;

	/** 前准額度－幣別 **/
	@Column(name = "LVCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LVCurr;

	/**
	 * 前准額度－金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 **/
	@Column(name = "LVAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LVAmt;

	/**
	 * 前准批覆授信額度－幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "LV2CURR", length = 3, columnDefinition = "CHAR(3)")
	private String LV2Curr;

	/**
	 * 前准批覆授信額度－金額
	 * <p/>
	 * 101/04/12新增 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "LV2AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LV2Amt;

	/**
	 * 前准批覆擔保授信額度－幣別
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * ※自行輸入
	 */
	@Column(name = "LVASSURECURR", length = 3, columnDefinition = "CHAR(3)")
	private String LVAssureCurr;

	/**
	 * 前准批覆擔保授信額度－金額
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * ※自行輸入 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "LVASSUREAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LVAssureAmt;

	/** 餘額－幣別 **/
	@Column(name = "BLCURR", length = 3, columnDefinition = "CHAR(3)")
	private String BLCurr;

	/**
	 * 餘額－金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 **/
	@Column(name = "BLAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal BLAmt;

	/**
	 * 餘額－備註
	 * <p/>
	 * 101/04/12新增 <br/>
	 * 102.02.18 欄位擴大 60 -> 600
	 */
	@Column(name = "BLMEMO", length = 600, columnDefinition = "VARCHAR(600)")
	private String BLMemo;

	/** 購料放款案下已開狀未到單金額－幣別 **/
	@Column(name = "LCCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LCCurr;

	/** 購料放款案下已開狀未到單金額－金額 **/
	@Column(name = "LCAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LCAmt;

	/** 現請額度－同前頁 **/
	@Column(name = "CURRENTAPPREF", length = 1, columnDefinition = "CHAR(1)")
	private String CurrentAppRef;

	/** 現請額度－幣別 **/
	@Column(name = "CURRENTAPPLYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String currentApplyCurr;

	/**
	 * 現請額度－金額
	 * <p/>
	 * 100/12/05配合上傳DB調整 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "CURRENTAPPLYAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal currentApplyAmt;

	/**
	 * 現請額度－是否循環使用
	 * <p/>
	 * 1.不循環使用<br/>
	 * 2.循環使用
	 */
	@Column(name = "REUSE", length = 1, columnDefinition = "CHAR(1)")
	private String reUse;

	/**
	 * 現請額度－動用幣別
	 * <p/>
	 * 1.或等值其他貨幣<br/>
	 * 2.或等值其他外幣<br/>
	 * 3.之等值其他外幣
	 */
	@Column(name = "OTHERCURR", length = 1, columnDefinition = "CHAR(1)")
	private String otherCurr;

	/**
	 * 聯貸說明
	 * <p/>
	 * 101/03/17調整
	 */
	@Column(name = "COUNTSAY", length = 1024, columnDefinition = "VARCHAR(1024)")
	private String countSay;

	/** 借款收付彙計數－同前頁 **/
	@Column(name = "COLLECTPAYREF", length = 1, columnDefinition = "CHAR(1)")
	private String collectPayRef;

	/** 借款收付彙計數（付）－幣別 **/
	@Column(name = "CPCURR", length = 3, columnDefinition = "CHAR(3)")
	private String CPCurr;

	/**
	 * 動用期限(ELLNSEEK.LNUSENO 或 ELF461_LNUSENO)
	 * <p/>
	 * 0.不再動用<br/>
	 * 1.YYYY-MM-DD～YYYY-MM-DD<br/>
	 * 2.自核准日起MM個月<br/>
	 * 3.自簽約日起MM個月<br/>
	 * 4.自首次動用日起MM個月<br/>
	 * 5.其他
	 */
	@Column(name = "USEDEADLINE", length = 1, columnDefinition = "CHAR(1)")
	private String useDeadline;

	/**
	 * 動用期限－其他(ELLNSEEK.USEFTMN 或ELF461_USEFTMN)
	 * <p/>
	 * 101/03/29調整 <br/>
	 * 102.02.18 欄位擴大 128-> 900
	 */
	@Column(name = "DESP1", length = 900, columnDefinition = "VARCHAR(900)")
	private String desp1;

	/** 風險權數－借款戶 **/
	@Column(name = "ITEMC", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal itemC;

	/** 風險權數－抵減後 **/
	@Column(name = "RITEMD", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rItemD;

	/** 資金成本率 **/
	@Column(name = "ITEMG", columnDefinition = "DECIMAL(8,5)")
	private BigDecimal itemG;

	/** 占資本適足率 **/
	@Column(name = "ITEMF", columnDefinition = "DECIMAL(8,5)")
	private BigDecimal itemF;

	/**
	 * 分行逾放比
	 * <p/>
	 * 101/04/12調整<br/>
	 * 100(300
	 */
	@Column(name = "NPL", length = 300, columnDefinition = "VARCHAR(300)")
	private String npl;

	/** 分行逾放比更新日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "NPLDATE", columnDefinition = "DATE")
	private Date npldate;

	/** 本票－同前頁 **/
	@Column(name = "CHECKNOTEREF", length = 1, columnDefinition = "CHAR(1)")
	private String checkNoteRef;

	/**
	 * 本票 <br/>
	 * 102.02.18 欄位擴大 100 -> 1050
	 * **/
	@Column(name = "CHECKNOTE", length = 1050, columnDefinition = "VARCHAR(1050)")
	private String checkNote;

	/**
	 * 額度本票
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 1.由借款人及連帶保證人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 2.由借款人及一般保證人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 3.由借款人及共同借款人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 4.由借款人出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 5.無此項<br/>
	 * ※除第1, 2, 3項可複選並存外，第4項或第5項皆不可與其他項目並存。<br/>
	 * eg.1|2|3 或 1|3 或 4 或 5
	 */
	@Column(name = "CHECKQNOTE", length = 5, columnDefinition = "VARCHAR(5)")
	private String checkQNote;

	/**
	 * 連保人/保證人
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * 1.連保人(預設)<br/>
	 * 2.保證人
	 */
	@Column(name = "GUARANTORTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String guarantorType;

	/**
	 * 連保人
	 * <p/>
	 * 128個全型字 <br/>
	 * 102.02.18 欄位擴大 384 -> 1800 個金:用來儲存舊案連保人訊息
	 */
	@Column(name = "GUARANTOR", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String guarantor;

	/**
	 * 連保人備註
	 * <p/>
	 * 64個全型字 <br/>
	 * 102.02.18 欄位擴大 192 -> 900
	 */
	@Column(name = "GUARANTORMEMO", length = 900, columnDefinition = "VARCHAR(900)")
	private String guarantorMemo;

	/** 連保人－同前頁 **/
	@Column(name = "GUARANTORREF", length = 1, columnDefinition = "CHAR(1)")
	private String guarantorRef;

	/**
	 * 物上保證人 <br/>
	 * 102.02.18 欄位擴大 100 -> 900
	 * **/
	@Column(name = "GUARANTOR1", length = 900, columnDefinition = "VARCHAR(900)")
	private String guarantor1;

	/**
	 * 本案未送保原因
	 * <p/>
	 * 100/12/05增列<br/>
	 * 1.非中小企業<br/>
	 * 2.本行績優客戶<br/>
	 * 3.十足擔保<br/>
	 * 4.擔保率超過50%<br/>
	 * 5.營業未滿1年<br/>
	 * 6.外國人資本超過50%<br/>
	 * 7.單一法人所佔資本額超過50%<br/>
	 * 8.其他(請自行輸入)
	 */
	@Column(name = "NOINSUREASON", length = 1, columnDefinition = "CHAR(1)")
	private String noInsuReason;

	/**
	 * 本案未送保原因(其他) <br/>
	 * 102.02.18 欄位擴大 100 -> 900
	 **/
	@Column(name = "NOINSUREASONOTHER", length = 900, columnDefinition = "VARCHAR(900)")
	private String noInsuReasonOther;

	/** 擔保授信額度調整幣別 **/
	@Column(name = "ASSURETOTECURR", length = 3, columnDefinition = "CHAR(3)")
	private String assureTotECurr;

	/**
	 * 擔保授信額度調整金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 **/
	@Column(name = "ASSURETOTEAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal assureTotEAmt;

	/**
	 * 合計金額調整註記
	 * <p/>
	 * 101/04/12新增<br/>
	 * Y/N
	 */
	@Column(name = "VALUETUNE", length = 1, columnDefinition = "CHAR(1)")
	private String valueTune;

	/**
	 * 前准(批覆)額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "LVTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LVTotCurr;

	/**
	 * 前准(批覆)額度合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 由【前准批覆授信額度】合計 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "LVTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LVTotAmt;

	/**
	 * 前准擔保授信額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "LVASSTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LVAssTotCurr;

	/**
	 * 前准擔保授信額度合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 由【前准批覆擔保授信額度】合計 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "LVASSTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LVAssTotAmt;

	/**
	 * 新作、增額合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "INCAPPLYTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String incApplyTotCurr;

	/**
	 * 新作、增額合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 新作、增額合計 = 授信額度合計- 前准額度 + 取消、減額合計 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "INCAPPLYTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal incApplyTotAmt;

	/**
	 * 新作、增額擔保額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "INCASSTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String incAssTotCurr;

	/**
	 * 新作、增額擔保額度合計金額
	 * <p/>
	 * 101/04/12新增 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "INCASSTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal incAssTotAmt;

	/**
	 * 取消、減額合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "DECAPPLYTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String decApplyTotCurr;

	/**
	 * 取消、減額合計金額
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "DECAPPLYTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal decApplyTotAmt;

	/**
	 * 取消、減額擔保額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "DECASSTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String decAssTotCurr;

	/**
	 * 取消、減額擔保額度合計金額
	 * <p/>
	 * 101/04/12新增 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "DECASSTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal decAssTotAmt;

	/** 授信額度合計幣別 **/
	@Column(name = "LOANTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LoanTotCurr;

	/**
	 * 授信額度合計金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 **/
	@Column(name = "LOANTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LoanTotAmt;

	/** 擔保授信額度合計幣別 **/
	@Column(name = "ASSURETOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String assureTotCurr;

	/**
	 * 擔保授信額度合計金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 **/
	@Column(name = "ASSURETOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal assureTotAmt;

	/**
	 * 擔保品押值合計幣別
	 * <p/>
	 * 101/04/12新增<br/>
	 * ※自行輸入
	 */
	@Column(name = "ESTTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String estTotCurr;

	/**
	 * 擔保品押值合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * ※自行輸入 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "ESTTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal estTotAmt;

	/** 衍生性金融商品合計幣別 **/
	@Column(name = "LOANTOTZCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LoanTotZCurr;

	/**
	 * 衍生性金融商品合計金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 **/
	@Column(name = "LOANTOTZAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LoanTotZAmt;

	/** 衍生性金融商品相當授信額度合計幣別 **/
	@Column(name = "LOANTOTLCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LoanTotLCurr;

	/**
	 * 衍生性金融商品相當授信額度合計金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 * **/
	@Column(name = "LOANTOTLAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LoanTotLAmt;

	/**
	 * 匯率資料日期
	 * <p/>
	 * 100/11/10新增(保留未用)
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RATEDT", columnDefinition = "DATE")
	private Date rateDT;

	/**
	 * 結算匯率(放款幣折本位幣)
	 * <p/>
	 * 100/11/10新增(保留未用)
	 */
	@Column(name = "RATELOANTOLOC", columnDefinition = "DECIMAL(12,8)")
	private BigDecimal rateLoanToLoc;

	/**
	 * 結算匯率(本位幣折合計幣)
	 * <p/>
	 * 100/11/10新增(保留未用)
	 */
	@Column(name = "RATELOCTOSUM", columnDefinition = "DECIMAL(12,8)")
	private BigDecimal rateLocToSum;

	/**
	 * 結算匯率(交叉匯率)
	 * <p/>
	 * 100/11/10新增(保留未用)<br/>
	 * (保留未用)<br/>
	 * 放款幣先折算本位幣，本位幣再折算合計幣。
	 */
	@Column(name = "RATECROSSOVER", columnDefinition = "DECIMAL(12,8)")
	private BigDecimal rateCrossover;

	/**
	 * 折算匯率
	 * <p/>
	 * 100/11/10新增(保留未用)<br/>
	 * 預設值同「交叉匯率」欄。<br/>
	 * 本欄開放user自行調整，如有異動，則清空「授信額度合計金額」欄。<br/>
	 * 執行【授信額度合計】時，依此匯率計算合計。<br/>
	 * 若執行【授信額度合計】所選取的「合計幣別」與「授信額度合計幣別」欄不同時，則讀取匯率檔重新計算。
	 */
	@Column(name = "RATECONVERT", columnDefinition = "DECIMAL(12,8)")
	private BigDecimal rateConvert;

	/**
	 * 備註 <br/>
	 * 102.02.18 欄位擴大 300 -> 1536
	 **/
	@Column(name = "RMK", length = 1536, columnDefinition = "VARCHAR(1536)")
	private String Rmk;

	/**
	 * 各幣別授信額度合計註記
	 * <p/>
	 * 101/04/12新增<br/>
	 * Y/N
	 */
	@Column(name = "MULTIAMTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String multiAmtFlag;

	/** 各幣別授信額度合計 **/
	@Column(name = "MULTIAMT", length = 300, columnDefinition = "VARCHAR(300)")
	private String multiAmt;

	/**
	 * 各幣別其中擔保合計
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "MULTIASSUREAMT", length = 300, columnDefinition = "VARCHAR(300)")
	private String multiAssureAmt;

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/16調整<br/>
	 * Y：檢誤完成(包含計算合計)<br/>
	 * N：檢誤完成(尚未計算合計)<br/>
	 * 空值：檢誤未完成<br/>
	 * 儲存時檢核所有必要欄位是否已完備，完成則為N。(若chkYN原為Y，亦修改為N)<br/>
	 * 計算合計時檢核所有額度明細表chkYN若尚有空值，則不能執行；計算合計完成後則修改chkYN為Y。<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;

	/**
	 * 是否有衍生性商品科目(Y/N)
	 * <p/>
	 * 101/09/13調整<br/>
	 * Y：有(要顯示衍生性金融商品合計與約當授信合計，列印亦須顯示)<br/>
	 * N：無(若isDerivatives = "Y"而hasDerivatives = "N"
	 * 則為保證或進出口科目，只要顯示風險係數而不顯示衍生性合計 )<br/>
	 * 空值：檢誤未完成<br/>
	 * 儲存時檢核所有必要欄位是否已完備，完成則為N。(若chkYN原為Y，亦修改為N)<br/>
	 * 計算合計時檢核所有額度明細表chkYN若尚有空值，則不能執行；計算合計完成後則修改chkYN為Y。<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name = "HASDERIVATIVES", length = 1, columnDefinition = "CHAR(1)")
	private String hasDerivatives;

	/**
	 * 共同借款人組成文字 <BR/>
	 * 消金團貸額度明細表借用此欄位，來存放 '另案產品科目' <BR/>
	 * 因團貸母戶限輸入1個產品, 當需要有2個科目來描述時, 即可用此欄位<BR/>
	 * 例如：成數75%以內要建有擔科目, 超過75%的要建無擔科目, 即可在 '另案產品科目' 來描述
	 * **/
	@Column(name = "L140M01JSTR", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String l140m01jStr;

	/**
	 * NOTES 轉檔版本
	 */
	@Column(name = "NOTESUP", columnDefinition = "VARCHAR(6)")
	private String notesUp;

	/**
	 * 按轉讓發票金額%動用
	 * <p/>
	 * 102/08/17新增<br/>
	 */
	@Column(name = "LOANPER", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal loanPer;

	/**
	 * 可否借新還舊
	 */
	@Column(name = "BYNEWOLD", columnDefinition = "CHAR(1)")
	private String byNewOld;

	/**
	 * 本案是否為信保續約案
	 */
	@Column(name = "ISGUAOLDCASE", columnDefinition = "CHAR(1)")
	private String isGuaOldCase;

	/**
	 * 產品種類
	 */
	@Column(name = "LNTYPE", columnDefinition = "CHAR(2)")
	private String lnType;

	/**
	 * 本行帳務是否有聯貸拆帳作業
	 */
	@Column(name = "HASLOANASSIGN", columnDefinition = "CHAR(1)")
	private String hasLoanAssign;

	/**
	 * 青年創業及啟動金貸款 申請日期
	 */
	@Column(name = "APPLYDATE", columnDefinition = "DATE")
	private Date applyDate;

	/** 各幣別授信額度合計 **/
	@Column(name = "DERVMULTIAMT", length = 300, columnDefinition = "VARCHAR(300)")
	private String dervMultiAmt;

	/**
	 * 衍生性授信額度合計多幣別說明註記
	 * <p/>
	 * 103/03/24新增<br/>
	 * Y/N
	 */
	@Column(name = "NEWDERVMULTIAMTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String newDervMultiAmtFlag;

	/**
	 * 衍生性係數複選組合字串
	 */
	@Column(name = "DERIVATIVESNUMDSCR", columnDefinition = "VARCHAR(300)")
	private String derivativesNumDscr;

	/**
	 * 額度評等
	 * <p/>
	 * 103/06/09調整<br/>
	 * 300
	 */
	@Column(name = "FACILITYRATING", length = 300, columnDefinition = "VARCHAR(300)")
	private String facilityRating;

	/** 現請擔保額度－幣別 **/
	@Column(name = "ASSUREAPPLYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String assureApplyCurr;

	/** 現請擔保額度－金額 **/
	@Column(name = "ASSUREAPPLYAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal assureApplyAmt;

	/**
	 * 列印明細 1:現請金額/2:信評
	 * **/
	@Column(name = "PRINTDETAILS", length = 20, columnDefinition = "VARCHAR(20)")
	private String printDetails;

	/**
	 * 保證人是否按一定比率負担保證責任 Y/N
	 */
	@Column(name = "GUAPERCENTFG", length = 1, columnDefinition = "CHAR(1)")
	private String guaPercentFg;

	/**
	 * 簽報書OID(計算額度合計後塞值，用來判斷是否需要重新計算授信額度合計)
	 */
	@Column(name = "CNTRCHGOID", columnDefinition = "VARCHAR(32)")
	private String cntrChgOid;

	/**
	 * 擔保品是否為房屋
	 */
	@Column(name = "HEADITEM7", length = 1, columnDefinition = "CHAR(1)")
	private String headItem7;

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 本案是否屬銀行法72-2條控管對象
	 */
	@Column(name = "IS722FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String is722Flag;

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 現行帳務系統或e-Loan前次報案(帳務系統無資料時)是否屬銀行法72-2條控管對象{Y ,
	 * N , A:兩種都有}
	 */
	@Column(name = "IS722ONFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String is722OnFlag;

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 查詢銀行法72-2條控管對象額度序號
	 */
	@Column(name = "IS722CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String is722CntrNo;

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 查詢銀行法72-2條控管對象日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "IS722QDATE", columnDefinition = "DATE")
	private Date is722QDate;

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 額度序號檢核是否存在帳務擋註記
	 */
	@Column(name = "CNTRNOCHKEXISTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String cntrNoChkExistFlag;

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 額度序號檢核是否存在帳務擋日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CNTRNOCHKEXISTDATE", columnDefinition = "DATE")
	private Date cntrNoChkExistDate;

	@Column(name = "ISBUY", length = 1, columnDefinition = "VARCHAR(1)")
	private String isBuy;
	@Column(name = "EXITEM", length = 3, columnDefinition = "VARCHAR(3)")
	private String exItem;
	@Column(name = "ISBUYON", length = 1, columnDefinition = "VARCHAR(1)")
	private String isBuyOn;
	@Column(name = "EXITEMON", length = 60, columnDefinition = "VARCHAR(60)")
	private String exItemOn;

	/**
	 * 利害關係人敘作無擔保授信註記 J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 */
	@Column(name = "UNSECUREFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String unsecureFlag;

	/**
	 * 備份原NOLOAN J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 */
	@Column(name = "NOLOANBK", length = 1, columnDefinition = "CHAR(1)")
	private String noLoanBk;

	/**
	 * 額度明細表檢核供應鏈融資賣放限週轉科目 J-104-0284-001 額度明細表新增是否收取帳務管理費
	 */
	@Column(name = "ISEFIN", length = 1, columnDefinition = "VARCHAR(1)")
	private String isEfin;

	/**
	 * 是否收取帳務管理費 J-104-0264-001 額度明細表新增是否收取帳務管理費
	 */
	@Column(name = "HASAMFEE", length = 1, columnDefinition = "VARCHAR(1)")
	private String hasAmFee;

	/**
	 * 是否為振興經濟非中小企業專案貸款 J-104-0339-001 增加振興經濟非中小企業專案貸款案件及金額欄位
	 */
	@Column(name = "ISNONSMEPROJLOAN", length = 1, columnDefinition = "VARCHAR(1)")
	private String isNonSMEProjLoan;

	/**
	 * J-104-0339-001 增加振興經濟非中小企業專案貸款案件及金額欄位
	 * 
	 * **/
	@Column(name = "NONSMEPROJLOANAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal nonSMEProjLoanAmt;

	/**
	 * 本額度是否有下限利率 J-105-0088-001 Web e-Loan
	 * 企金授信系統利率條件無下限利率時，新增a-Loan是否需建置下限利率欄位並上傳a-Loan檢核。
	 */
	@Column(name = "HASRATELIMIT", length = 1, columnDefinition = "VARCHAR(1)")
	private String hasRateLimit;

	/**
	 * 是否為振興經濟非中小企業專案貸款(動審表變更後) J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	@Column(name = "CISNONSMEPROJLOAN", length = 1, columnDefinition = "VARCHAR(1)")
	private String cIsNonSMEProjLoan;

	/**
	 * 振興經濟非中小企業專案貸款金額(動審表變更後) J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 * 
	 * **/
	@Column(name = "CNONSMEPROJLOANAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cNonSMEProjLoanAmt;

	/**
	 * 振興經濟非中小企業專案貸款金額變更動審表MAINID J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 * 
	 * **/
	@Column(name = "CNONSMEPROJMAINID", columnDefinition = "VARCHAR(32)")
	private String cNonSMEProjMainId;

	/**
	 * 衍生性金融商品期數版本 N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
	 * 
	 * **/
	@Column(name = "DERIVATIVEVERSION", columnDefinition = "VARCHAR(8)")
	private String derivativeVersion;

	/**
	 * 約定融資額度註記 J-105-0155-001 Web e-Loan國內、海外企金額度明細表增加『約定融資額度註記』欄位與上傳a-Loan功能
	 */
	@Column(name = "EXCEPTFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String exceptFlag;

	/**
	 * 產品種類新創產業細目 J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。 <br/>
	 * 值域 select * from com.bcodetype where codetype='lms140_itwCode' and
	 * locale='zh_TW'
	 * <ul>
	 * <li>當 行業別
	 * 符合新創產業(isStartUp=Y)，belongItwCode有值(例如CC|FF)。若在「專案種類」又重複去輸入「05:新創產業
	 * 」，已在程式阻擋</li>
	 * <li>若 行業別 不是新創產業(isStartUp=N)，要看經辦在「專案種類」是否輸入「05:新創產業」。若輸入「05:新創產業」，則要輸入
	 * 新創產業細目( 此欄位 )</li>
	 * </ul>
	 */
	@Column(name = "ITWCODE", length = 2, columnDefinition = "VARCHAR(2)")
	private String itwCode;

	/**
	 * 借款人行業別是否列於新創重點產業 J-105-0308-001 Web
	 * e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 */
	@Column(name = "ISSTARTUP", length = 1, columnDefinition = "VARCHAR(1)")
	private String isStartUp;

	/** 物上保證人－同前頁 **/
	@Column(name = "GUARANTOR1REF", length = 1, columnDefinition = "CHAR(1)")
	private String guarantor1Ref;

	// J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位
	/** 是否屬未核配額度國家 **/
	@Column(name = "ISNOFACTCOUNTRY", length = 1, columnDefinition = "VARCHAR(1)")
	private String isNoFactCountry;

	// J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位
	/** 未核配額度國家 **/
	@Column(name = "NOFACTCOUNTRY", length = 60, columnDefinition = "VARCHAR(60)")
	private String noFactCountry;

	// J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位
	/** 是否屬凍結額度國家 **/
	@Column(name = "ISFREEZEFACTCOUNTRY", length = 1, columnDefinition = "VARCHAR(1)")
	private String isFreezeFactCountry;

	// J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位
	/** 凍結額度國家 **/
	@Column(name = "FREEZEFACTCOUNTRY", length = 60, columnDefinition = "VARCHAR(60)")
	private String freezeFactCountry;

	/**
	 * J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
	 */
	@Column(name = "INSMEFG", length = 1, columnDefinition = "VARCHAR(1)")
	private String inSmeFg;

	/**
	 * J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
	 * 
	 * **/
	@Column(name = "INSMETOAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal inSmeToAmt;

	/**
	 * J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
	 * 
	 * **/
	@Column(name = "INSMECAAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal inSmeCaAmt;

	/**
	 * J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
	 * 
	 * **/
	@Column(name = "ENHANCEAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal enhanceAmt;

	/**
	 * J-107-0027-001 Web e-Loan企金授信額度明細表增列新創重點產業細目選項
	 */
	@Column(name = "BELONGITWCODE", length = 20, columnDefinition = "VARCHAR(20)")
	private String belongItwCode;

	/**
	 * J-107-0091-002 Web e-Loan額度明細表新增引介來源{1:行員引介, 2:房仲引介, 3:金控子公司員工引介,
	 * 4:代書(地政士)引介, 6:本行客戶引介, 7:經總處核准之整批貸款, N:NA}
	 * <ul>
	 * <li>select * from com.bcodetype where
	 * codetype='L140M01A_introductionSource'</li>
	 * </ul>
	 */
	@Column(name = "INTRODUCESRC", length = 1, columnDefinition = "CHAR(1)")
	private String introduceSrc;

	/** 引介行員代號 */
	@Column(name = "MEGAEMPNO", length = 6, columnDefinition = "CHAR(6)")
	private String megaEmpNo;

	/** 引介房仲代號{11111:永慶, 22222:信義, 55555:住商} */
	@Column(name = "AGNTNO", length = 5, columnDefinition = "CHAR(5)")
	private String agntNo;

	/** 引介房仲連鎖店類型{A:直營店, B:加盟店} */
	@Column(name = "AGNTCHAIN", length = 1, columnDefinition = "CHAR(1)")
	private String agntChain;

	/** 買賣合約書編號 */
	@Column(name = "DEALCONTRACTNO", length = 20, columnDefinition = "VARCHAR(20)")
	private String dealContractNo;

	/** 引介子公司代號{90001, 90002...} */
	@Column(name = "MEGACODE", length = 5, columnDefinition = "CHAR(5)")
	private String megaCode;

	/**
	 * 引介子公司分支代號 select * from com.bcodetype where CODETYPE like
	 * 'LNF13E_SUB_UNITNO_%'
	 */
	@Column(name = "SUBUNITNO", length = 5, columnDefinition = "CHAR(5)")
	private String subUnitNo;

	/** 引介子公司員工編號 */
	@Column(name = "SUBEMPNO", length = 6, columnDefinition = "CHAR(6)")
	private String subEmpNo;

	/** 引介子公司員工姓名 */
	@Column(name = "SUBEMPNM", length = 30, columnDefinition = "CHAR(30)")
	private String subEmpNm;

	/** 引介客戶ID/企業統編 */
	@Column(name = "INTROCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String introCustId;

	/** 引介客戶重複序號 */
	@Column(name = "INTRODUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String introDupNo;

	/** 引介客戶名稱/企業名稱 */
	@Column(name = "INTROCUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String introCustName;

	/** 引介來源建商名稱 */
	@Column(name = "INTROBUILDERNAME", length = 90, columnDefinition = "VARCHAR(90)")
	private String introBuilderName;

	/** 引介來源建案名稱 */
	@Column(name = "INTROBIULDCASENAME", length = 90, columnDefinition = "VARCHAR(90)")
	private String introBiuldCaseName;

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 */
	@Column(name = "ARACCMANAGERTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String arAccManagerType;

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 */
	@Column(name = "ARACCMANAGERID", length = 11, columnDefinition = "VARCHAR(11)")
	private String arAccManagerId;

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 */
	@Column(name = "ARACCMANAGERNM", length = 120, columnDefinition = "VARCHAR(120)")
	private String arAccManagerNm;

	/**
	 * J-107-0137_05097_B1001 Web
	 * e-Loan企金授信額度明細表，新增「信保基金保證書發文日期」與「信保基金核准之保證手續費率」
	 * 
	 * **/
	@Column(name = "CGFRATE", columnDefinition = "DECIMAL(6,4)")
	private BigDecimal cgfRate;

	/**
	 * J-107-0137_05097_B1001 Web
	 * e-Loan企金授信額度明細表，新增「信保基金保證書發文日期」與「信保基金核准之保證手續費率」
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CGFDATE", columnDefinition = "DATE")
	private Date cgfDate;

	/**
	 * 是否為重建
	 */
	@Column(name = "REBUILD", length = 1, columnDefinition = "CHAR(1)")
	private String rebuild;

	/**
	 * 是否純營建工程業之營運週轉金
	 */
	@Column(name = "ISINSTALMENT", length = 1, columnDefinition = "CHAR(1)")
	private String isInstalment;

	/**
	 * 是否純營建工程業之營運週轉金
	 */
	@Column(name = "ISINSTALMENTON", length = 1, columnDefinition = "CHAR(1)")
	private String isInstalmentOn;

	/**
	 * J-107-0357_05097_B1001 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表 <br/>
	 * 專案種類<br/>
	 * (企金)from com.bcodetype where codetype='lms140_projClass' <br/>
	 * (國內消金)from com.bcodetype where codetype='L140M01A_PROJCLASS_CLS' <br/>
	 * (中心) from ln.lnf070 where LNF070_TYPE='LNF020-PROJ-CLASS' <br/>
	 * 有一些企金 eLoan 的專案種類{10:歡迎台商回台投資專案貸款, 11:中小企業加速投資貸款, 12:根留台灣企業加速投資專案貸款,
	 * 13:愛企貸專案} 在 A-LOAN 沒有=> 只是為了 e-Loan 統計用 目前{e-Loan的專案種類 會比A-LOAN還要多}
	 */
	@Column(name = "PROJCLASS", length = 2, columnDefinition = "CHAR(1)")
	private String projClass;

	/**
	 * J-108-0116 共同行銷 CrossSelling 共同行銷種類
	 */
	@Column(name = "CSTYPES", length = 5, columnDefinition = "VARCHAR(5)")
	private String csTypes;

	/** 擔保品是否為本行外幣十足存款 */
	@Column(name = "HEADITEM8", length = 1, columnDefinition = "CHAR(1)")
	private String headItem8;

	/**
	 * J-108-0302 是否符合出口實績規範 是否符合出口實績規範
	 */
	@Column(name = "EXPERF_FG", length = 1, columnDefinition = "CHAR(1)")
	private String experf_fg;

	/**
	 * J-108-0302 是否符合出口實績規範 瑕疵額度控管方式
	 */
	@Column(name = "FLAW_FG", length = 1, columnDefinition = "CHAR(1)")
	private String flaw_fg;

	/**
	 * J-108-0302 是否符合出口實績規範 瑕疵額度限額
	 */
	@Column(name = "FLAW_AMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal flaw_amt;

	/**
	 * /** 文件亂碼
	 */
	@Column(name = "RANDOMCODESBR", length = 32, columnDefinition = "VARCHAR(32)")
	private String randomCodeSbr;

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核 是否為徵提股票為擔保
	 */
	@Column(name = "ISCOLLSTOCK", length = 1, columnDefinition = "CHAR(1)")
	private String isCollStock;

	/**
	 * 本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
	 */
	@Column(name = "ISRESCUE", length = 1, columnDefinition = "CHAR(1)")
	private String isRescue;

	/**
	 * 紓困貸款類別
	 */
	@Column(name = "RESCUEITEM", length = 3, columnDefinition = "CHAR(3)")
	private String rescueItem;

	/**
	 * 減收利率
	 */
	@Column(name = "RESCUERATE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rescueRate;

	/**
	 * 本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
	 */
	@Column(name = "ISRESCUE_Af", length = 1, columnDefinition = "CHAR(1)")
	private String isRescue_Af;

	/**
	 * 紓困貸款類別
	 */
	@Column(name = "RESCUEITEM_Af", length = 3, columnDefinition = "CHAR(3)")
	private String rescueItem_Af;

	/**
	 * 減收利率
	 */
	@Column(name = "RESCUERATE_Af", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal rescueRate_Af;

	/**
	 * 本案是否展延六個月
	 */
	@Column(name = "ISEXTENDSIXMON", length = 1, columnDefinition = "CHAR(1)")
	private String isExtendSixMon;

	/**
	 * 合意展延日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RESCUEIBDATE", columnDefinition = "DATE")
	private Date rescueIbDate;

	/**
	 * 截至1090312前既有之本行貸款餘額
	 */
	@Digits(integer = 15, fraction = 2)
	@Column(name = "RESCUEAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal rescueAmt;

	/**
	 * 本案資金來源是否為央行轉融通
	 */
	@Column(name = "ISCBREFIN", length = 1, columnDefinition = "CHAR(1)")
	private String isCbRefin;

	/**
	 * 截至1090312前既有之本行貸款餘額
	 */
	@Digits(integer = 15, fraction = 2)
	@Column(name = "RESCUEAMT_AF", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal rescueAmt_Af;

	/**
	 * 截至1090312前既有之本行貸款幣別
	 */
	@Column(name = "RESCUECURR", length = 3, columnDefinition = "CHAR(3)")
	private String rescueCurr;

	/**
	 * 截至1090312前既有之本行貸款幣別
	 */
	@Column(name = "RESCUECURR_AF", length = 3, columnDefinition = "CHAR(3)")
	private String rescueCurr_Af;

	/**
	 * 本案資金來源是否為央行轉融通
	 */
	@Column(name = "ISCBREFIN_AF", length = 1, columnDefinition = "CHAR(1)")
	private String isCbRefin_Af;

	/**
	 * 合併申請紓困方案
	 */
	@Column(name = "RESCUEITEMSUB", length = 3, columnDefinition = "CHAR(3)")
	private String rescueItemSub;

	/**
	 * 合併申請紓困方案
	 */
	@Column(name = "RESCUEITEMSUB_AF", length = 3, columnDefinition = "CHAR(3)")
	private String rescueItemSub_Af;

	/**
	 * 受理日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RESCUEDATE", columnDefinition = "DATE")
	private Date rescueDate;

	/**
	 * 受理日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RESCUEDATE_AF", columnDefinition = "DATE")
	private Date rescueDate_Af;

	/**
	 * 本案是否屬小規模營業人簡易申貸方案
	 */
	@Column(name = "ISSMALLBUSS", length = 1, columnDefinition = "CHAR(1)")
	private String isSmallBuss;

	/**
	 * 登記期間
	 */
	@Digits(integer = 5, fraction = 0)
	@Column(name = "SBREGISTPERIOD", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal sbRegistPeriod;

	/**
	 * 負責人從事本業期間
	 */
	@Digits(integer = 5, fraction = 0)
	@Column(name = "SBPRINCIPALPERIOD", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal sbPrincipalPeriod;

	/**
	 * 負責人個人信用評分資訊
	 */
	@Digits(integer = 5, fraction = 0)
	@Column(name = "SBCREDITRATING", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal sbCreditRating;

	/**
	 * 擔保情形
	 */
	@Column(name = "SBCOLSTATUS", length = 1, columnDefinition = "CHAR(1)")
	private String sbColStatus;

	/**
	 * 營業狀況
	 */
	@Column(name = "SBBUSSSTATUS", length = 1, columnDefinition = "CHAR(1)")
	private String sbBussStatus;

	/**
	 * 銀行簡易評分表總分
	 */
	@Digits(integer = 5, fraction = 0)
	@Column(name = "SBSCORE", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal sbScore;

	/**
	 * 本次是否有評分
	 */
	@Column(name = "SBHASCREDITRATING", length = 1, columnDefinition = "CHAR(1)")
	private String sbHasCreditRating;

	/**
	 * 本次是否有評分
	 */
	@Column(name = "SBREASONCODE", length = 3, columnDefinition = "CHAR(3)")
	private String sbReasonCode;

	/**
	 * 本案是否屬兆元振興融資方案
	 */
	@Column(name = "ISREVIVE", length = 1, columnDefinition = "CHAR(1)")
	private String isRevive;

	/**
	 * 貸款對象類別
	 */
	@Column(name = "REVIVETARGET", length = 2, columnDefinition = "CHAR(2)")
	private String reviveTarget;

	/**
	 * 六大核心產業
	 */
	@Column(name = "REVIVECOREINDUSTRY", length = 2, columnDefinition = "CHAR(2)")
	private String reviveCoreIndustry;

	/**
	 * 國際鏈結布局
	 */
	@Column(name = "REVIVECHAIN", length = 2, columnDefinition = "CHAR(2)")
	private String reviveChain;

	/**
	 * 貸款用途
	 */
	@Column(name = "REVIVELOANPURPOSE", length = 2, columnDefinition = "CHAR(2)")
	private String reviveLoanPurpose;

	/**
	 * 本案是否屬總處單位引介
	 */
	@Column(name = "ISRESCUEINTRODUCE", length = 1, columnDefinition = "CHAR(1)")
	private String isRescueIntroduce;

	/**
	 * 引介單位
	 */
	@Column(name = "RESCUEINTRODUCEBRNO", length = 3, columnDefinition = "CHAR(3)")
	private String rescueIntroduceBrNo;

	/**
	 * 引介單位行員
	 */
	@Column(name = "RESCUEINTRODUCEUSERID", length = 6, columnDefinition = "CHAR(6)")
	private String rescueIntroduceUserId;

	/** 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構 **/
	@Column(name = "ISOFFICIALCGA", length = 1, columnDefinition = "CHAR(1)")
	private String isOfficialCga;

	/** 保證機構之主權國家 **/
	@Column(name = "CGA_COUNTRY", length = 2, columnDefinition = "CHAR(2)")
	private String cga_country;

	/** 主權國家信評之機構 **/
	@Column(name = "CGA_CRDTYPE", length = 2, columnDefinition = "CHAR(2)")
	private String cga_crdType;

	/** 主權國家信評之地區別 **/
	@Column(name = "CGA_CRDAREA", length = 1, columnDefinition = "CHAR(1)")
	private String cga_crdArea;

	/** 主權國家信評之期間別 **/
	@Column(name = "CGA_CRDPRED", length = 1, columnDefinition = "CHAR(1)")
	private String cga_crdPred;

	/** 主權國家信評之等級 **/
	@Column(name = "CGA_CRDGRAD", length = 10, columnDefinition = "VARCHAR(10)")
	private String cga_crdGrade;

	/** 主權國家信評之風險權數 **/
	@Digits(integer = 3, fraction = 2)
	@Column(name = "CGA_RSKRATIO", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal cga_rskRatio;

	/**
	 * 是否引介「小頭家拚經濟，兆豐超給利專案」
	 */
	@Column(name = "ISMEGASUPERPROFITPROJECT", length = 1, columnDefinition = "CHAR(1)")
	private String isMegaSuperProfitProject;

	/**
	 * 計入兆元振興額度
	 * <p/>
	 * J-109-0275_05097_B1001 Web e-Loan企金授信優化兆元振興融資方案系統欄位及報表
	 */
	@Column(name = "REVIVEAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal reviveAmt;

	/**
	 * 觀光產業別
	 */
	@Column(name = "RESCUEINDUSTRY", length = 1, columnDefinition = "CHAR(1)")
	private String rescueIndustry;

	/**
	 * 營業所在地之縣市別
	 */
	@Column(name = "RESCUECITY", length = 1, columnDefinition = "CHAR(1)")
	private String rescueCity;

	/**
	 * 本案是否屬特殊融資暴險
	 */
	@Column(name = "ISSPECIALFINRISK", length = 1, columnDefinition = "CHAR(1)")
	private String isSpecialFinRisk;

	/**
	 * 特殊融資暴險類別
	 */
	@Column(name = "SPECIALFINRISKTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String specialFinRiskType;

	/**
	 * 本案是否屬不動產ADC暴險
	 */
	@Column(name = "ISCMSADCRISK", length = 1, columnDefinition = "CHAR(1)")
	private String isCmsAdcRisk;

	/**
	 * 本案是否屬109年9至12月提升中小企業放款方案
	 */
	@Column(name = "ISENHANCESMELOAN", length = 1, columnDefinition = "CHAR(1)")
	private String isEnhanceSmeLoan;

	/**
	 * 青創貸款用途(企金在L140M01A, 消金在L140M03A)
	 */
	@Column(name = "YOPURPOSE", length = 1, columnDefinition = "CHAR(1)")
	private String yoPurpose;

	/**
	 * 補貼單位(企金在L140M01A, 消金在L140M03A)
	 */
	@Column(name = "SUBSIDYUT", length = 2, columnDefinition = "VARCHAR(2)")
	private String subSidyut;

	/**
	 * 是否為疑似人頭戶案件
	 */
	@Column(name = "ISSUSPECTEDHEADACCOUNT", length = 1, columnDefinition = "CHAR(1)")
	private String isSuspectedHeadAccount;

	/**
	 * ADC案件編號
	 */
	@Column(name = "ADCCASENO", length = 15, columnDefinition = "CHAR(15)")
	private String adcCaseNo;

	/**
	 * 疑似整批房貸案件原因
	 * 1.非首次移轉者、2.高價住宅、3.投資戶、4.建商/地主/關係戶、5.店面、6.商辦、7.餘屋、8.經區域中心/授信審查處列示須逐案呈報者
	 */
	@Column(name = "SUSPECTEDPACKLOANREASON", length = 2, columnDefinition = "VARCHAR(2)")
	private String suspectedPackLoanReason;

	/**
	 * 疑似整批房貸案件註記
	 */
	@Column(name = "SUSPECTEDPACKLOANFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String suspectedPackLoanFlag;

	/**
	 * 疑似整批房貸案件額度序號
	 */
	@Column(name = "SUSPECTEDPACKLOANCNTRNO", length = 100, columnDefinition = "VARCHAR(100)")
	private String suspectedPackLoanCntrNo;

	/**
	 * 行銷註記 A:EDM、B:簡訊、C:官網、D:新聞、E:貸款利息收據、存款對帳單、F:LINE、N:無
	 */
	@Column(name = "MARKETINGNOTES", length = 50, columnDefinition = "VARCHAR(50)")
	private String marketingNotes;

	/**
	 * 本案是否屬110年行銷名單來源客戶
	 */
	@Column(name = "ISMARKETINGLIST110", length = 1, columnDefinition = "CHAR(1)")
	private String isMarketingList110;

	/**
	 * 前次是否屬109年紓困
	 */
	@Column(name = "ISRESCUEON", length = 1, columnDefinition = "CHAR(1)")
	private String isRescueOn;

	/** 前次文件編號 **/
	@Size(max = 32)
	@Column(name = "ONMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String onMainId;

	/**
	 * 前次紓困貸款類別
	 */
	@Column(name = "RESCUEITEMON", length = 3, columnDefinition = "CHAR(3)")
	private String rescueItemOn;

	/**
	 * 前次案件號碼
	 * <p/>
	 * 100/11/24調整
	 */
	@Column(name = "CASENOON", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNoOn;

	/** 前次額度序號 **/
	@Column(name = "CNTRNOON", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNoOn;

	/**
	 * /** 本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)
	 */
	@Column(name = "GUANAEXPOSURE", length = 1, columnDefinition = "CHAR(1)")
	private String guaNaExposure;

	/** 國發基金加碼保證成數 **/
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "RESCUENDFGUTPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal rescueNdfGutPercent;

	/**
	 * 是否收取授信作業手續費
	 */
	@Column(name = "ISOPERATIONFEE", length = 1, columnDefinition = "CHAR(1)")
	private String isOperationFee;

	/**
	 * 免收取授信作業手續費原因
	 */
	@Column(name = "NOOPERATIONFEE", length = 30, columnDefinition = "VARCHAR(30)")
	private String noOperationFee;

	/**
	 * 免收取授信作業手續費原因其他說明
	 */
	@Column(name = "NOOPERATIONFEEOTH", length = 300, columnDefinition = "VARCHAR(300)")
	private String noOperationFeeOth;

	/**
	 * 專案融資是否屬營運階段
	 */
	@Column(name = "ISPROJECTFINOPERATESTAG", length = 1, columnDefinition = "CHAR(1)")
	private String isProjectFinOperateStag;

	/**
	 * 應收利息幣別
	 */
	@Column(name = "RCVCURR", length = 3, columnDefinition = "CHAR(3)")
	private String rcvCurr;

	/** 應收利息金額 **/
	@Digits(integer = 15, fraction = 2)
	@Column(name = "RCVINT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal rcvInt;

	/** 帳款管理商保證成數 **/
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "ARACCPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal arAccPercent;

	/**
	 * 聯貸幣別
	 */
	@Column(name = "SYNDLOANCURR", length = 3, columnDefinition = "CHAR(3)")
	private String syndLoanCurr;

	/** 聯貸總金額 **/
	@Digits(integer = 15, fraction = 2)
	@Column(name = "SYNDLOANTOTAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal syndLoanTotal;

	/** 參貸金額 **/
	@Digits(integer = 15, fraction = 2)
	@Column(name = "SYNDLOANPART", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal syndLoanPart;

	/**
	 * 綠色授信註記
	 */
	@Column(name = "ISESGGREENLOAN", length = 1, columnDefinition = "CHAR(1)")
	private String isEsgGreenLoan;

	/**
	 * 綠色支出類別
	 */
	@Column(name = "ESGGREENSPENDTYPE", length = 20, columnDefinition = "VARCHAR(20)")
	private String esgGreenSpendType;

	/**
	 * 發電站座
	 */
	@Column(name = "ESGGREENSPENDTYPE1", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal esgGreenSpendType1;

	/**
	 * 裝置容量瓩KW
	 */
	@Column(name = "ESGGREENSPENDTYPE2", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal esgGreenSpendType2;

	/**
	 * 度/年
	 */
	@Column(name = "ESGGREENSPENDTYPE3", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal esgGreenSpendType3;

	/**
	 * 綠色授信註記其他說明
	 */
	@Column(name = "ESGGREENSPENDTYPEZMEMO", length = 300, columnDefinition = "VARCHAR(300)")
	private String esgGreenSpendTypeZMemo;

	/**
	 * 永續績效連結授信註記
	 */
	@Column(name = "ESGSUSTAINLOAN", length = 1, columnDefinition = "CHAR(1)")
	private String esgSustainLoan;

	/**
	 * 永續績效連結授信類別
	 */
	@Column(name = "ESGSUSTAINLOANTYPE", length = 10, columnDefinition = "CHAR(10)")
	private String esgSustainLoanType;

	/**
	 * 永續績效連結授信約定條件全部未達成通報
	 */
	@Column(name = "ESGSUSTAINLOANUNREACH", length = 1, columnDefinition = "CHAR(1)")
	private String esgSustainLoanUnReach;

	/**
	 * 永續績效連結授信約定條件全部未達成通報說明
	 */
	@Column(name = "ESGSUSTAINLOANUNREACHMEMO", length = 300, columnDefinition = "VARCHAR(300)")
	private String esgSustainLoanUnReachMemo;

	/**
	 * 屬本行授信業務授權準則得單獨劃分之業務類
	 */
	@Column(name = "ISSTANDALONEAUTH", length = 1, columnDefinition = "CHAR(1)")
	private String isStandAloneAuth;

	/**
	 * 是否列於六大核心戰略產業
	 * 
	 * J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
	 */
	@Column(name = "ISCOREBUSS", length = 1, columnDefinition = "CHAR(1)")
	private String isCoreBuss;

	/**
	 * 產品種類六大核心戰略產業細目 <br/>
	 * 值域 select * from com.bcodetype where codetype='lms140_itwCodeCoreBuss'
	 * and locale='zh_TW'
	 * <ul>
	 * <li>當 行業別
	 * 符合六大核心產業(isCoreBuss=Y)，belongItwCodeCoreBuss有值(例如CC|FF)，在html就隱藏
	 * itwCodeCoreBuss( 此欄位 )</li>
	 * <li>若 行業別 不是六大核心產業(isCoreBuss=N)，在html會要求經辦輸入 itwCodeCoreBuss( 此欄位 )</li>
	 * </ul>
	 * J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
	 */
	@Column(name = "ITWCODECOREBUSS", length = 2, columnDefinition = "CHAR(2)")
	private String itwCodeCoreBuss;

	/**
	 * 借款人行業別所屬六大核心戰略產業細目
	 * 
	 * J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
	 */
	@Column(name = "BELONGITWCODECOREBUSS", length = 20, columnDefinition = "VARCHAR(20)")
	private String belongItwCodeCoreBuss;

	/**
	 * 送保方式 J-111-0268_05097_B1001 Web e-Loan修改額度明細表信保案件相關功能
	 */
	@Column(name = "GUTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String gutType;

	/**
	 * LGD其中無擔保合計
	 * 
	 * J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTAMT_U", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_U;

	/**
	 * LGD其中擬制部分擔保合計
	 * 
	 * J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTAMT_P", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_P;

	/**
	 * LGD其中擬制十足擔保合計
	 * 
	 * J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTAMT_S", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_S;

	/**
	 * LGD合計金額調整註記
	 * <p/>
	 * J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能 Y/N
	 */
	@Column(name = "VALUETUNELGD", length = 1, columnDefinition = "CHAR(1)")
	private String valueTuneLgd;

	/**
	 * LGD合計版本
	 * <p/>
	 * J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能 Y/N
	 */
	@Column(name = "LGDTOTAMT_VER", length = 13, columnDefinition = "CHAR(13)")
	private String lgdTotAmt_Ver;

	/**
	 * 聯貸案特殊擔保品LGD條件
	 * <p/>
	 * J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則Y/N
	 */
	@Column(name = "SYNDISCMSSPECIAL_1", length = 1, columnDefinition = "CHAR(1)")
	private String syndIsCmsSpecial_1;

	/** 引介分行 */
	@Column(name = "MEGAEMPBRNO", length = 3, columnDefinition = "CHAR(3)")
	private String megaEmpBrNo;

	/** 引介人姓名 */
	@Column(name = "INTRODUCERNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String introducerName;

	/**
	 * LGD1合計
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTAMT_1", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_1;

	/**
	 * LGD2合計
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTAMT_2", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_2;

	/**
	 * LGD3合計
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTAMT_3", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_3;

	/**
	 * LGD4合計
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTAMT_4", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_4;

	/**
	 * LGD5合計
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTAMT_5", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_5;

	/** 額度預期LGD **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "EXPECTLGD", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal expectLgd;

	/** 檢附佐證資料 */
	@Column(name = "ATTACHDOC", length = 1, columnDefinition = "VARCHAR(1)")
	private String attachDoc;

	/** 檢附佐證資料第二層 */
	@Column(name = "ATTACHDOC2", length = 2, columnDefinition = "VARCHAR(2)")
	private String attachDoc2;

	/** 檢附佐證資料第三層 */
	@Column(name = "ATTACHDOC3", length = 3, columnDefinition = "VARCHAR(3)")
	private String attachDoc3;

	/** 檢附佐證資料其他說明 */
	@Column(name = "ATTACHDOCMEMO", length = 60, columnDefinition = "VARCHAR(60)")
	private String attachDocMemo;

	/**
	 * 本案是否屬中小企業千億振興融資方案
	 */
	@Column(name = "ISREVITAL", length = 1, columnDefinition = "CHAR(1)")
	private String isRevital;

	/**
	 * 中小企業千億振興融資方案_貸款對象類別
	 */
	@Column(name = "REVITALTARGET", length = 2, columnDefinition = "CHAR(2)")
	private String revitalTarget;
	/**
	 * 中小企業千億振興融資方案_貸款用途
	 */
	@Column(name = "REVITALLOANPURPOSE", length = 2, columnDefinition = "CHAR(2)")
	private String revitalLoanPurpose;
	/**
	 * 計入中小企業千億振興額度
	 */
	@Column(name = "REVITALAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal revitalAmt;

	/**
	 * 是否命中與其他主借人相同資訊(通訊地址、手機、通訊電話)
	 */
	@Column(name = "ISHITSAMEBORROWERINFO", length = 1, columnDefinition = "CHAR(1)")
	private String isHitSameBorrowerInfo;

	/** 核貸成數 **/
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "APPROVEDPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal approvedPercent;

	/**
	 * 約定融資額度註記問答項目選擇[是]的項目
	 */
	@Column(name = "EXCEPTFLAGQAISY", length = 2, columnDefinition = "VARCHAR(2)")
	private String exceptFlagQAisY;

	/** 房仲證書(明)字號-年 **/
	@Size(max = 3)
	@Column(name = "LICENSEYEAR", length = 3, columnDefinition = "VARCHAR(3)")
	private String licenseYear;

	/** 房仲證書(明)字號-年登字 **/
	@Size(max = 15)
	@Column(name = "LICENSEWORD", length = 15, columnDefinition = "VARCHAR(15)")
	private String licenseWord;

	/** 房仲證書(明)字號-編號 **/
	@Size(max = 6)
	@Column(name = "LICENSENUMBER", length = 6, columnDefinition = "CHAR(6)")
	private String licenseNumber;

	/** 批次保證額度批覆書案號 **/
	@Size(max = 10)
	@Column(name = "BATGUTAMTAPPRCASENO", length = 10, columnDefinition = "VARCHAR(10)")
	private String batGutAmtApprCaseNo;

	/** 批號 **/
	@Size(max = 8)
	@Column(name = "BATCHNO", length = 8, columnDefinition = "VARCHAR(8)")
	private String batchNo;

	/** 額度預期LGD組別 **/
	@Size(max = 2)
	@Column(name = "EXPECTLGDGROUP", length = 2, columnDefinition = "CHAR(2)")
	private String expectLgdGroup;

	/** 涉及ESG風險授信案件之審查註記 **/
	@Size(max = 1)
	@Column(name = "CHECKBTESG", length = 1, columnDefinition = "CHAR(1)")
	private String checkBtEsg;

	/** 是否已向管理行(母戶簽報行)通報占用貸款額度(Y/N) **/
	@Size(max = 1)
	@Column(name = "ISINFORMMBRCHUSEDAMT", length = 1, columnDefinition = "CHAR(1)")
	private String isInformMbrchUsedAmt;

	/**
	 * 高品質專案融資_選項1
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_1", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_1;

	/**
	 * 高品質專案融資_選項2
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_2", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_2;

	/**
	 * 高品質專案融資_選項3
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_3", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_3;

	/**
	 * 高品質專案融資_選項4
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_4", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_4;

	/**
	 * 高品質專案融資_選項5
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_5", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_5;

	/**
	 * 高品質專案融資_最終結果
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJRESULT", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjResult;

	/**
	 * 瑕疵押匯額度合計
	 */
	@Column(name = "FLAWAMTTOTAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal flawAmtTotal;

	/**
	 * 總授信額度(授信額度+出口瑕疵額度)
	 */
	@Column(name = "GENERALLOANTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal generalLoanTotAmt;

	/**
	 * 約定融資額度註記資本計提延伸問答(僅有條件及無條件可取消)
	 */
	@Column(name = "EXCEPTFLAGQAPLUS", length = 1, columnDefinition = "VARCHAR(1)")
	private String exceptFlagQAPlus;

	/**
	 * 單獨另計授權額度合計
	 */
	@Column(name = "STANDALONEAUTHTOTAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal standAloneAuthTotal;

	/**
	 * LGD1合計-全案
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTMGAMT_1", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotMgAmt_1;

	/**
	 * LGD2合計-全案
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTMGAMT_2", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotMgAmt_2;

	/**
	 * LGD3合計-全案
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTMGAMT_3", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotMgAmt_3;

	/**
	 * LGD4合計-全案
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTMGAMT_4", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotMgAmt_4;

	/**
	 * LGD5合計-全案
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTMGAMT_5", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotMgAmt_5;

	/**
	 * 瑕疵押匯額度合計-全案
	 */
	@Column(name = "FLAWAMTMGTOTAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal flawAmtMgTotal;

	/**
	 * 總授信授權額度合計(LGD合計+出口瑕疵額度)-全案
	 */
	@Column(name = "GENERALLOANMGTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal generalLoanMgTotAmt;

	/** LGD全案合併－幣別 **/
	@Column(name = "LGDTOTMGCURR", length = 3, columnDefinition = "CHAR(3)")
	private String lgdTotMgCurr;

	/**
	 * JOIN 個金額度明細表主檔補充資料檔
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = true, insertable = false, updatable = false) })
	private L140M03A l140m03a;

	/**
	 * 額度預期計算LGD選用模型 0.免辦 1.房貸個人評等模型 2.非房貸個人評等模型 3.卡友貸
	 **/
	@Size(max = 1)
	@Column(name = "EXPECTMODELKIND", length = 1, columnDefinition = "CHAR(1)")
	private String expectModelKind;

	/**
	 * 是否徵提保證金(遠匯、換匯交易)
	 * 
	 * J-109-0365_05097_B1001 Web e-Loan國內企金授信額度明細表科目為遠期外匯、換匯交易時，新增是否徵提保證金等相關欄位
	 */
	@Size(max = 1)
	@Column(name = "MARGINFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String marginFlag;

	/**
	 * LGD1合計-合併關係
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTRCAMT_1", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotRcAmt_1;

	/**
	 * LGD2合計-合併關係
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTRCAMT_2", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotRcAmt_2;

	/**
	 * LGD3合計-合併關係
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTRCAMT_3", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotRcAmt_3;

	/**
	 * LGD4合計-合併關係
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTRCAMT_4", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotRcAmt_4;

	/**
	 * LGD5合計-合併關係
	 * 
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 */
	@Column(name = "LGDTOTRCAMT_5", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotRcAmt_5;

	/**
	 * 瑕疵押匯額度合計-合併關係
	 */
	@Column(name = "FLAWAMTRCTOTAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal flawAmtRcTotal;

	/**
	 * 總授信授權額度合計(LGD合計+出口瑕疵額度)-合併關係
	 */
	@Column(name = "GENERALLOANRCTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal generalLoanRcTotAmt;

    /**
     * 屬性評估
     */
    @Size(max = 1)
    @Column(name = "DERIVEVAL", length = 1, columnDefinition = "VARCHAR(1)")
    private String derivEval;

    @Temporal(TemporalType.DATE)
    @Column(name = "EVALDATE", columnDefinition = "DATE")
    private Date evalDate;

	/** J-113-0227 消金房貸核貸成數新增檢核邏輯
	 *  該額度所搭配專案(Y1/Y2/Y3)額度序號 
	 *  註：這個欄位是在搭配的Y1/Y2/Y3去做設定的
	 *  如果這個欄位有值，就代表這一筆是屬於X1額度(但他的projClass不一定會被設定成X1)
	 */
	@Column(name = "PROJREFCNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String projRefCntrNo;
	
	/**
	 * 是否需檢核房貸成數
	 */
	@Column(name = "ISCHECKHOUSERATIO", length = 1, columnDefinition = "CHAR(1)")
	private String isCheckHouseRatio;

	/**
	 * 手續費收取方式
	 */
	@Size(max = 1)
	@Column(name = "OPERATIONFEEWAY", length = 1, columnDefinition = "VARCHAR(1)")
	private String operationFeeWay;

	/**
	 * 授信作業手續費金額
	 */
	@Digits(integer = 9, fraction = 0, groups = Check.class)
	@Column(name = "OPERATIONFEEAMT", columnDefinition = "DECIMAL(9,0)")
	private BigDecimal operationFeeAmt;

	/**
	 * 申請案件號碼
	 */
	@Column(name = "APPNO", length = 14, columnDefinition = "CHAR(14)")
	private String appNo;

	/**
	 * 授信用途科目別
	 */
	@Column(name = "LOANANDCONTTYPE", length = 800, columnDefinition = "VARCHAR(800)")
	private String loanAndContType;
	
	/**
	 * 是否屬於「0403震災受災戶住宅補貼方案」
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "HUALIEN0403", length = 1, columnDefinition = "VARCHAR(1)")
	private String hualien0403;
	
	/**
	 * 房貸信保註記
	 * <p/>
	 * Y
	 */
	@Size(max = 1)
	@Column(name = "ISHOUSELOANGU", length = 1, columnDefinition = "VARCHAR(1)")
	private String isHouseLoanGu;
	
	/** 花蓮地震震災房貸信保成數 **/
	@Digits(integer = 3, fraction = 2)
	@Column(name = "HUALIENGUTPER", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal hualienGutPer;
	
	/**
	 * 社會責任授信註記
	 */
	@Column(name = "SOCIALLOANFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String socialLoanFlag;
	
	/**
	 * 社會責任專案類別
	 */
	@Column(name = "SOCIALKIND", length = 20, columnDefinition = "VARCHAR(20)")
	private String socialKind;
	
	/**
	 * 社會責任專案目標族群
	 */
	@Column(name = "SOCIALTA", length = 20, columnDefinition = "VARCHAR(20)")
	private String socialTa;
	
	/**
	 * 承作社會責任專案者
	 */
	@Column(name = "SOCIALRESP", length = 5, columnDefinition = "VARCHAR(5)")
	private String socialResp;

	/**
	 * 符合中小微企業多元發展專案貸款適用對象 類別
	 * (1)朝數位轉型(存1)；(2)朝淨零轉型(存2)；(3)朝通路發展(存4)
	 * 可複選 3=1+2;6=2+4;7=1+2+4
	 */
	@Column(name = "RESCUEITEMLKIND", columnDefinition = "INTEGER")
	private Integer rescueItemLKind;


	/**
	 * 取得共同借款人組成文字
	 * 
	 * @return
	 */
	public String getL140m01jStr() {
		return l140m01jStr;
	}

	/**
	 * 設定共同借款人組成文字
	 * 
	 * @return
	 */
	public void setL140m01jStr(String l140m01jStr) {
		this.l140m01jStr = l140m01jStr;
	}

	/**
	 * 取得來源註記
	 * <p/>
	 * 100/12/06新增<br/>
	 * 0.案件產生(for國內個金舊案)<br/>
	 * 1.新增額度明細表(空白)<br/>
	 * 2.複製額度明細表<br/>
	 * 3.轉入額度明細表<br/>
	 * 4.續約/條件變更產生 <br/>
	 * 5.帳務 (由帳務系統資訊轉入之資料LN.LNF) <br/>
	 * 6.核准 (由R6轉入之資料) <br/>
	 * 7.動用 (由mis.Elf500轉入之資料)<br/>
	 * 8.帳務銷戶<br/>
	 * 9.核准取銷
	 */
	public String getDataSrc() {
		return this.dataSrc;
	}

	/**
	 * 設定來源註記
	 * <p/>
	 * 100/12/06新增<br/>
	 * 0.案件產生(for國內個金舊案)<br/>
	 * 1.新增額度明細表(空白)<br/>
	 * 2.複製額度明細表<br/>
	 * 3.轉入額度明細表<br/>
	 * 4.續約/條件變更產生 <br/>
	 * 5.帳務 (由帳務系統資訊轉入之資料LN.LNF) <br/>
	 * 6.核准 (由R6轉入之資料) <br/>
	 * 7.動用 (由mis.Elf500轉入之資料)<br/>
	 * 8.帳務銷戶<br/>
	 * 9.核准取銷
	 **/
	public void setDataSrc(String value) {
		this.dataSrc = value;
	}

	/**
	 * 取得來源文件編號
	 * <p/>
	 * 101/07/17新增<br/>
	 * for「續約/條件變更」<br/>
	 * (為取得變更前資料用)
	 */
	public String getMainIdSrc() {
		return this.mainIdSrc;
	}

	/**
	 * 設定來源文件編號
	 * <p/>
	 * 101/07/17新增<br/>
	 * for「續約/條件變更」<br/>
	 * (為取得變更前資料用)
	 **/
	public void setMainIdSrc(String value) {
		this.mainIdSrc = value;
	}

	/** 取得列印順序 **/
	public Integer getPrintSeq() {
		return this.printSeq;
	}

	/** 設定列印順序 **/
	public void setPrintSeq(Integer value) {
		this.printSeq = value;
	}

	/**
	 * 取得案件號碼
	 * <p/>
	 * 100/11/24調整
	 */
	public String getCaseNo() {
		return this.caseNo;
	}

	/**
	 * 設定案件號碼
	 * <p/>
	 * 100/11/24調整
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 取得簽案日期 **/
	public Date getCaseDate() {
		return this.caseDate;
	}

	/** 設定簽案日期 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/**
	 * 取得案件授權等級
	 * <p/>
	 * 1.分行授權內<br/>
	 * 2.營運中心授權內<br/>
	 * 3.分行授權外<br/>
	 * 4.營運中心授權外
	 */
	public String getGrant() {
		return this.grant;
	}

	/**
	 * 設定案件授權等級
	 * <p/>
	 * 1.分行授權內<br/>
	 * 2.營運中心授權內<br/>
	 * 3.分行授權外<br/>
	 * 4.營運中心授權外
	 **/
	public void setGrant(String value) {
		this.grant = value;
	}

	/** 取得分行放行時間 **/
	public Timestamp getBrdTime() {
		return this.brdTime;
	}

	/** 設定分行放行時間 **/
	public void setBrdTime(Timestamp value) {
		this.brdTime = value;
	}

	/** 取得營運中心放行時間 **/
	public Timestamp getAreaSendInfo() {
		return this.areaSendInfo;
	}

	/** 設定營運中心放行時間 **/
	public void setAreaSendInfo(Timestamp value) {
		this.areaSendInfo = value;
	}

	/**
	 * 取得婉卻代碼
	 * <p/>
	 * 101/02/22調整
	 */
	public String getCesRjtCause() {
		return this.cesRjtCause;
	}

	/**
	 * 設定婉卻代碼
	 * <p/>
	 * 101/02/22調整
	 **/
	public void setCesRjtCause(String value) {
		this.cesRjtCause = value;
	}

	/** 取得婉卻原因 **/
	public String getCesRjtReason() {
		return this.cesRjtReason;
	}

	/** 設定婉卻原因 **/
	public void setCesRjtReason(String value) {
		this.cesRjtReason = value;
	}

	/** 取得本額度風險歸屬國別 **/
	public String getRiskArea() {
		return this.riskArea;
	}

	/** 設定本額度風險歸屬國別 **/
	public void setRiskArea(String value) {
		this.riskArea = value;
	}

	/**
	 * 取得本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 */
	public String getHeadItem1() {
		return this.headItem1;
	}

	/**
	 * 設定本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 **/
	public void setHeadItem1(String value) {
		this.headItem1 = value;
	}

	/** 取得保證成數 **/
	public BigDecimal getGutPercent() {
		return this.gutPercent;
	}

	/** 設定保證成數 **/
	public void setGutPercent(BigDecimal value) {
		this.gutPercent = value;
	}

	/** 取得信保首次動用有效期限 **/
	public Date getGutCutDate() {
		return this.gutCutDate;
	}

	/** 設定信保首次動用有效期限 **/
	public void setGutCutDate(Date value) {
		this.gutCutDate = value;
	}

	/**
	 * 取得借款人是否為中小企業
	 * <p/>
	 * Y/N（是/否）<br/>
	 * [個金]利率列印位置<br/>
	 * 0|印於主表<br/>
	 * 1|印於附表(一)<br/>
	 */
	public String getHeadItem2() {
		return this.headItem2;
	}

	/**
	 * 設定借款人是否為中小企業
	 * <p/>
	 * Y/N（是/否）<br/>
	 * [個金]利率列印位置<br/>
	 * 0|印於主表<br/>
	 * 1|印於附表(一)<br/>
	 **/
	public void setHeadItem2(String value) {
		this.headItem2 = value;
	}

	/**
	 * 取得本額度是否為聯貸信保
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getSyndIPFD() {
		return this.syndIPFD;
	}

	/**
	 * 設定本額度是否為聯貸信保
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setSyndIPFD(String value) {
		this.syndIPFD = value;
	}

	/**
	 * 取得是否符合送中小信保基金保證條件
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getHeadItem3() {
		return this.headItem3;
	}

	/**
	 * 設定是否符合送中小信保基金保證條件
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setHeadItem3(String value) {
		this.headItem3 = value;
	}

	/**
	 * 取得送保種類
	 * <p/>
	 * 101/01/13因應泰行需求新增<br/>
	 * 1.中小信保基金保證<br/>
	 * 2.海外信保基金保證<br/>
	 * ※國內交易預設1，海外預設2
	 */
	public String getHeadItem3Ttl() {
		return this.headItem3Ttl;
	}

	/**
	 * 設定送保種類
	 * <p/>
	 * 101/01/13因應泰行需求新增<br/>
	 * 1.中小信保基金保證<br/>
	 * 2.海外信保基金保證<br/>
	 * ※國內交易預設1，海外預設2
	 **/
	public void setHeadItem3Ttl(String value) {
		this.headItem3Ttl = value;
	}

	/**
	 * 取得擔保品是否為股票/開放型基金/受益憑證
	 * <p/>
	 * 101/02/23新增<br/>
	 * Y/N
	 */
	public String getHeadItem5() {
		return this.headItem5;
	}

	/**
	 * 設定擔保品是否為股票/開放型基金/受益憑證
	 * <p/>
	 * 101/02/23新增<br/>
	 * Y/N
	 **/
	public void setHeadItem5(String value) {
		this.headItem5 = value;
	}

	/**
	 * 取得擔保維持率採用類型
	 * <p/>
	 * 101/02/23新增<br/>
	 * 0.標準<br/>
	 * 1.自訂
	 */
	public String getMRateType() {
		return this.mRateType;
	}

	/**
	 * 設定擔保維持率採用類型
	 * <p/>
	 * 101/02/23新增<br/>
	 * 0.標準<br/>
	 * 1.自訂
	 **/
	public void setMRateType(String value) {
		this.mRateType = value;
	}

	/**
	 * 取得擔保維持率
	 * <p/>
	 * 101/02/23新增<br/>
	 * ※(標準擔保維持率為140%)
	 */
	public BigDecimal getMRate() {
		return this.mRate;
	}

	/**
	 * 設定擔保維持率
	 * <p/>
	 * 101/02/23新增<br/>
	 * ※(標準擔保維持率為140%)
	 **/
	public void setMRate(BigDecimal value) {
		this.mRate = value;
	}

	/**
	 * 取得性質
	 * <p/>
	 * 100/10/03 新增11、12、13<br/>
	 * 100/12/05調整成notes代碼<br/>
	 * 100/12/20調整欄位長度150(30<br/>
	 * 複選：<br/>
	 * 1報價(報價|13<br/>
	 * 2新作(新做|1<br/>
	 * 3增額(增額|5<br/>
	 * 4紓困(紓困|10<br/>
	 * 5協議清償(協議清償|12<br/>
	 * 6減額(減額|6<br/>
	 * 7變更條件(變更條件|3<br/>
	 * 8續約(續約|2<br/>
	 * 9提前續約(提前續約|11<br/>
	 * 10展期（不良授信案）(展期(不良授信案)|9<br/>
	 * 11流用(流用|4<br/>
	 * 12取消(取消|8<br/>
	 * 13不變(不變|7
	 */
	public String getProPerty() {
		return this.proPerty;
	}

	/**
	 * 設定性質
	 * <p/>
	 * 100/10/03 新增11、12、13<br/>
	 * 100/12/05調整成notes代碼<br/>
	 * 100/12/20調整欄位長度150(30<br/>
	 * 複選：<br/>
	 * 1報價(報價|13<br/>
	 * 2新作(新做|1<br/>
	 * 3增額(增額|5<br/>
	 * 4紓困(紓困|10<br/>
	 * 5協議清償(協議清償|12<br/>
	 * 6減額(減額|6<br/>
	 * 7變更條件(變更條件|3<br/>
	 * 8續約(續約|2<br/>
	 * 9提前續約(提前續約|11<br/>
	 * 10展期（不良授信案）(展期(不良授信案)|9<br/>
	 * 11流用(流用|4<br/>
	 * 12取消(取消|8<br/>
	 * 13不變(不變|7
	 **/
	public void setProPerty(String value) {
		this.proPerty = value;
	}

	/**
	 * 取得額度控管種類
	 * <p/>
	 * 10.一般<br/>
	 * 20.信保<br/>
	 * 30.聯貸<br/>
	 * 40.合作母<br/>
	 * 41.合作子<br/>
	 * 51.個人戶一般
	 */
	public String getSnoKind() {
		return this.snoKind;
	}

	/**
	 * 設定額度控管種類
	 * <p/>
	 * 10.一般<br/>
	 * 20.信保<br/>
	 * 30.聯貸<br/>
	 * 40.合作母<br/>
	 * 41.合作子<br/>
	 * 51.個人戶一般
	 **/
	public void setSnoKind(String value) {
		this.snoKind = value;
	}

	/** 取得共用額度序號 **/
	public String getCommSno() {
		return this.commSno;
	}

	/** 設定共用額度序號 **/
	public void setCommSno(String value) {
		this.commSno = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		if (value != null) {
			// 一率轉大寫額度序號
			value = value.toUpperCase();
		}
		this.cntrNo = value;
	}

	/**
	 * 取得額度性質
	 * <p/>
	 * 1.擔保<br/>
	 * 2.無擔保/信保<br/>
	 * 100/12/27 修改為代碼 上傳時要用<br/>
	 * S:擔保<br/>
	 * N:無擔保/信保
	 */
	public String getSbjProperty() {
		return this.sbjProperty;
	}

	/**
	 * 設定額度性質
	 * <p/>
	 * 1.擔保<br/>
	 * 2.無擔保/信保<br/>
	 * 100/12/27 修改為代碼 上傳時要用<br/>
	 * S:擔保<br/>
	 * N:無擔保/信保
	 **/
	public void setSbjProperty(String value) {
		this.sbjProperty = value;
	}

	/** 取得原始額度序號 **/
	public String getSnoOld() {
		return this.snoOld;
	}

	/** 設定原始額度序號 **/
	public void setSnoOld(String value) {
		this.snoOld = value;
	}

	/** 取得原始控管種類 **/
	public String getSnoKindOld() {
		return this.snoKindOld;
	}

	/** 設定原始控管種類 **/
	public void setSnoKindOld(String value) {
		this.snoKindOld = value;
	}

	/**
	 * 取得本案是否有同業聯貸案額度
	 * <p/>
	 * Y/N（有/無）<br/>
	 * 100/10/03 額度種類選擇本案是否有同業聯貸案額度為是則每份額度明細表都要提供此選項
	 */
	public String getUnitCase2() {
		return this.unitCase2;
	}

	/**
	 * 設定本案是否有同業聯貸案額度
	 * <p/>
	 * Y/N（有/無）<br/>
	 * 100/10/03 額度種類選擇本案是否有同業聯貸案額度為是則每份額度明細表都要提供此選項
	 **/
	public void setUnitCase2(String value) {
		this.unitCase2 = value;
	}

	/**
	 * 取得加計該額度是否逾越限額
	 * <p/>
	 * Y/N（有/無）<br/>
	 * 100/10/13 新增
	 */
	public String getHeadItem4() {
		return this.headItem4;
	}

	/**
	 * 設定加計該額度是否逾越限額
	 * <p/>
	 * Y/N（有/無）<br/>
	 * 100/10/13 新增
	 **/
	public void setHeadItem4(String value) {
		this.headItem4 = value;
	}

	/**
	 * 取得授信科目性質與額度
	 * <p/>
	 * 100個全型字<br/>
	 * (組合文字)供簽報書案由引進用
	 */
	public String getGist() {
		return this.gist;
	}

	/**
	 * 設定授信科目性質與額度
	 * <p/>
	 * 100個全型字<br/>
	 * (組合文字)供簽報書案由引進用
	 **/
	public void setGist(String value) {
		this.gist = value;
	}

	/**
	 * 取得授信科目
	 * <p/>
	 * 101/04/12調整<br/>
	 * 200 ( 300<br/>
	 * 100個全型字(組合文字)
	 */
	public String getLnSubject() {
		return this.lnSubject;
	}

	/**
	 * 設定授信科目
	 * <p/>
	 * 101/04/12調整<br/>
	 * 200 ( 300<br/>
	 * 100個全型字(組合文字)
	 **/
	public void setLnSubject(String value) {
		this.lnSubject = value;
	}

	/**
	 * 取得清償期限
	 * <p/>
	 * 101/04/12調整<br/>
	 * 100 ( 200<br/>
	 * (組合文字)
	 */
	public String getPayDeadline() {
		return this.payDeadline;
	}

	/**
	 * 設定清償期限
	 * <p/>
	 * 101/04/12調整<br/>
	 * 100 ( 200<br/>
	 * (組合文字)
	 **/
	public void setPayDeadline(String value) {
		this.payDeadline = value;
	}

	/**
	 * 取得衍生性金融商品期數
	 * <p/>
	 * 1.六個月期（含）以內<br/>
	 * 2.六至十二個月期（含）以內<br/>
	 * 3.二年期（含）以內<br/>
	 * 4.三年期（含）以內<br/>
	 * 5.四年期（含）以內<br/>
	 * 6.五年期（含）以內<br/>
	 * 7.五年期以上
	 */
	public String getDerivatives() {
		return this.derivatives;
	}

	/**
	 * 設定衍生性金融商品期數
	 * <p/>
	 * 1.六個月期（含）以內<br/>
	 * 2.六至十二個月期（含）以內<br/>
	 * 3.二年期（含）以內<br/>
	 * 4.三年期（含）以內<br/>
	 * 5.四年期（含）以內<br/>
	 * 6.五年期（含）以內<br/>
	 * 7.五年期以上
	 **/
	public void setDerivatives(String value) {
		this.derivatives = value;
	}

	/**
	 * 取得是否為衍生性金融商品
	 * <p/>
	 * Y/N
	 */
	public String getIsDerivatives() {
		return this.isDerivatives;
	}

	/**
	 * 設定是否為衍生性金融商品
	 * <p/>
	 * Y/N
	 **/
	public void setIsDerivatives(String value) {
		this.isDerivatives = value;
	}

	/**
	 * 取得風險係數（%）
	 * <p/>
	 * 101/02/23調整
	 */
	public BigDecimal getDerivativesNum() {
		return this.derivativesNum;
	}

	/**
	 * 設定風險係數（%）
	 * <p/>
	 * 101/02/23調整
	 **/
	public void setDerivativesNum(BigDecimal value) {
		this.derivativesNum = value;
	}

	/** 取得前准額度－同前頁 **/
	public String getLastValueRefP1() {
		return this.lastValueRefP1;
	}

	/** 設定前准額度－同前頁 **/
	public void setLastValueRefP1(String value) {
		this.lastValueRefP1 = value;
	}

	/** 取得前准額度－幣別 **/
	public String getLVCurr() {
		return this.LVCurr;
	}

	/** 設定前准額度－幣別 **/
	public void setLVCurr(String value) {
		this.LVCurr = value;
	}

	/** 取得前准額度－金額 **/
	public BigDecimal getLVAmt() {
		return this.LVAmt;
	}

	/** 設定前准額度－金額 **/
	public void setLVAmt(BigDecimal value) {
		this.LVAmt = value;
	}

	/**
	 * 取得前准批覆授信額度－幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getLV2Curr() {
		return this.LV2Curr;
	}

	/**
	 * 設定前准批覆授信額度－幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setLV2Curr(String value) {
		this.LV2Curr = value;
	}

	/**
	 * 取得前准批覆授信額度－金額
	 * <p/>
	 * 101/04/12新增
	 */
	public BigDecimal getLV2Amt() {
		return this.LV2Amt;
	}

	/**
	 * 設定前准批覆授信額度－金額
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setLV2Amt(BigDecimal value) {
		this.LV2Amt = value;
	}

	/**
	 * 取得前准批覆擔保授信額度－幣別
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * ※自行輸入
	 */
	public String getLVAssureCurr() {
		return this.LVAssureCurr;
	}

	/**
	 * 設定前准批覆擔保授信額度－幣別
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * ※自行輸入
	 **/
	public void setLVAssureCurr(String value) {
		this.LVAssureCurr = value;
	}

	/**
	 * 取得前准批覆擔保授信額度－金額
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * ※自行輸入
	 */
	public BigDecimal getLVAssureAmt() {
		return this.LVAssureAmt;
	}

	/**
	 * 設定前准批覆擔保授信額度－金額
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * ※自行輸入
	 **/
	public void setLVAssureAmt(BigDecimal value) {
		this.LVAssureAmt = value;
	}

	/** 取得餘額－幣別 **/
	public String getBLCurr() {
		return this.BLCurr;
	}

	/** 設定餘額－幣別 **/
	public void setBLCurr(String value) {
		this.BLCurr = value;
	}

	/** 取得餘額－金額 **/
	public BigDecimal getBLAmt() {
		return this.BLAmt;
	}

	/** 設定餘額－金額 **/
	public void setBLAmt(BigDecimal value) {
		this.BLAmt = value;
	}

	/**
	 * 取得餘額－備註
	 * <p/>
	 * 101/04/12新增
	 */
	public String getBLMemo() {
		return this.BLMemo;
	}

	/**
	 * 設定餘額－備註
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setBLMemo(String value) {
		this.BLMemo = value;
	}

	/** 取得購料放款案下已開狀未到單金額－幣別 **/
	public String getLCCurr() {
		return this.LCCurr;
	}

	/** 設定購料放款案下已開狀未到單金額－幣別 **/
	public void setLCCurr(String value) {
		this.LCCurr = value;
	}

	/** 取得購料放款案下已開狀未到單金額－金額 **/
	public BigDecimal getLCAmt() {
		return this.LCAmt;
	}

	/** 設定購料放款案下已開狀未到單金額－金額 **/
	public void setLCAmt(BigDecimal value) {
		this.LCAmt = value;
	}

	/** 取得現請額度－同前頁 **/
	public String getCurrentAppRef() {
		return this.CurrentAppRef;
	}

	/** 設定現請額度－同前頁 **/
	public void setCurrentAppRef(String value) {
		this.CurrentAppRef = value;
	}

	/** 取得現請額度－幣別 **/
	public String getCurrentApplyCurr() {
		return this.currentApplyCurr;
	}

	/** 設定現請額度－幣別 **/
	public void setCurrentApplyCurr(String value) {
		this.currentApplyCurr = value;
	}

	/**
	 * 取得現請額度－金額
	 * <p/>
	 * 100/12/05配合上傳DB調整
	 */
	public BigDecimal getCurrentApplyAmt() {
		return this.currentApplyAmt;
	}

	/**
	 * 設定現請額度－金額
	 * <p/>
	 * 100/12/05配合上傳DB調整
	 **/
	public void setCurrentApplyAmt(BigDecimal value) {
		this.currentApplyAmt = value;
	}

	/**
	 * 取得現請額度－是否循環使用
	 * <p/>
	 * 1.不循環使用<br/>
	 * 2.循環使用
	 */
	public String getReUse() {
		return this.reUse;
	}

	/**
	 * 設定現請額度－是否循環使用
	 * <p/>
	 * 1.不循環使用<br/>
	 * 2.循環使用
	 **/
	public void setReUse(String value) {
		this.reUse = value;
	}

	/**
	 * 取得現請額度－動用幣別
	 * <p/>
	 * 1.或等值其他貨幣<br/>
	 * 2.或等值其他外幣<br/>
	 * 3.之等值其他外幣
	 */
	public String getOtherCurr() {
		return this.otherCurr;
	}

	/**
	 * 設定現請額度－動用幣別
	 * <p/>
	 * 1.或等值其他貨幣<br/>
	 * 2.或等值其他外幣<br/>
	 * 3.之等值其他外幣
	 **/
	public void setOtherCurr(String value) {
		this.otherCurr = value;
	}

	/**
	 * 取得聯貸說明
	 * <p/>
	 * 101/03/17調整
	 */
	public String getCountSay() {
		return this.countSay;
	}

	/**
	 * 設定聯貸說明
	 * <p/>
	 * 101/03/17調整
	 **/
	public void setCountSay(String value) {
		this.countSay = value;
	}

	/** 取得借款收付彙計數－同前頁 **/
	public String getCollectPayRef() {
		return this.collectPayRef;
	}

	/** 設定借款收付彙計數－同前頁 **/
	public void setCollectPayRef(String value) {
		this.collectPayRef = value;
	}

	/** 取得借款收付彙計數（付）－幣別 **/
	public String getCPCurr() {
		return this.CPCurr;
	}

	/** 設定借款收付彙計數（付）－幣別 **/
	public void setCPCurr(String value) {
		this.CPCurr = value;
	}

	/**
	 * 取得動用期限(ELLNSEEK.LNUSENO 或 ELF461_LNUSENO)
	 * <p/>
	 * 0.不再動用<br/>
	 * 1.YYYY-MM-DD～YYYY-MM-DD<br/>
	 * 2.自核准日起MM個月<br/>
	 * 3.自簽約日起MM個月<br/>
	 * 4.自首次動用日起MM個月<br/>
	 * 5.其他
	 */
	public String getUseDeadline() {
		return this.useDeadline;
	}

	/**
	 * 設定動用期限(ELLNSEEK.LNUSENO 或 ELF461_LNUSENO)
	 * <p/>
	 * 0.不再動用<br/>
	 * 1.YYYY-MM-DD～YYYY-MM-DD<br/>
	 * 2.自核准日起MM個月<br/>
	 * 3.自簽約日起MM個月<br/>
	 * 4.自首次動用日起MM個月<br/>
	 * 5.其他
	 **/
	public void setUseDeadline(String value) {
		this.useDeadline = value;
	}

	/**
	 * 取得動用期限－其他(ELLNSEEK.USEFTMN 或ELF461_USEFTMN)
	 * <p/>
	 * 101/03/29調整
	 */
	public String getDesp1() {
		return this.desp1;
	}

	/**
	 * 設定動用期限－其他(ELLNSEEK.USEFTMN 或ELF461_USEFTMN)
	 * <p/>
	 * 101/03/29調整
	 **/
	public void setDesp1(String value) {
		this.desp1 = value;
	}

	/** 取得風險權數－借款戶 **/
	public BigDecimal getItemC() {
		return this.itemC;
	}

	/** 設定風險權數－借款戶 **/
	public void setItemC(BigDecimal value) {
		this.itemC = value;
	}

	/** 取得風險權數－抵減後 **/
	public BigDecimal getRItemD() {
		return this.rItemD;
	}

	/** 設定風險權數－抵減後 **/
	public void setRItemD(BigDecimal value) {
		this.rItemD = value;
	}

	/** 取得資金成本率 **/
	public BigDecimal getItemG() {
		return this.itemG;
	}

	/** 設定資金成本率 **/
	public void setItemG(BigDecimal value) {
		this.itemG = value;
	}

	/** 取得占資本適足率 **/
	public BigDecimal getItemF() {
		return this.itemF;
	}

	/** 設定占資本適足率 **/
	public void setItemF(BigDecimal value) {
		this.itemF = value;
	}

	/**
	 * 取得分行逾放比
	 * <p/>
	 * 101/04/12調整<br/>
	 * 100(300
	 */
	public String getNpl() {
		return this.npl;
	}

	/**
	 * 設定分行逾放比
	 * <p/>
	 * 101/04/12調整<br/>
	 * 100(300
	 **/
	public void setNpl(String value) {
		this.npl = value;
	}

	/** 取得分行逾放比更新日期 **/
	public Date getNpldate() {
		return this.npldate;
	}

	/** 設定分行逾放比更新日期 **/
	public void setNpldate(Date value) {
		this.npldate = value;
	}

	/** 取得本票－同前頁 **/
	public String getCheckNoteRef() {
		return this.checkNoteRef;
	}

	/** 設定本票－同前頁 **/
	public void setCheckNoteRef(String value) {
		this.checkNoteRef = value;
	}

	/** 取得本票 **/
	public String getCheckNote() {
		return this.checkNote;
	}

	/** 設定本票 **/
	public void setCheckNote(String value) {
		this.checkNote = value;
	}

	/**
	 * 取得額度本票
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 1.由借款人及連帶保證人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 2.由借款人及一般保證人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 3.由借款人及共同借款人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 4.由借款人出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 5.無此項<br/>
	 * ※除第1, 2, 3項可複選並存外，第4項或第5項皆不可與其他項目並存。<br/>
	 * eg.1|2|3 或 1|3 或 4 或 5
	 */
	public String getCheckQNote() {
		return this.checkQNote;
	}

	/**
	 * 設定額度本票
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 1.由借款人及連帶保證人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 2.由借款人及一般保證人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 3.由借款人及共同借款人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 4.由借款人出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 5.無此項<br/>
	 * ※除第1, 2, 3項可複選並存外，第4項或第5項皆不可與其他項目並存。<br/>
	 * eg.1|2|3 或 1|3 或 4 或 5
	 **/
	public void setCheckQNote(String value) {
		this.checkQNote = value;
	}

	/**
	 * 取得連保人/保證人
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * 1.連保人(預設)<br/>
	 * 2.保證人
	 */
	public String getGuarantorType() {
		return this.guarantorType;
	}

	/**
	 * 設定連保人/保證人
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * 1.連保人(預設)<br/>
	 * 2.保證人
	 **/
	public void setGuarantorType(String value) {
		this.guarantorType = value;
	}

	/**
	 * 取得連保人
	 * <p/>
	 * 128個全型字<br/>
	 * 102.02.18 欄位擴大 384 -> 1800 <br/>
	 * 個金:用來儲存舊案連保人訊息
	 */
	public String getGuarantor() {
		return this.guarantor;
	}

	/**
	 * 設定連保人
	 * <p/>
	 * 128個全型字<br/>
	 * 102.02.18 欄位擴大 384 -> 1800 <br/>
	 * 個金:用來儲存舊案連保人訊息
	 **/
	public void setGuarantor(String value) {
		this.guarantor = value;
	}

	/**
	 * 取得連保人備註
	 * <p/>
	 * 64個全型字
	 */
	public String getGuarantorMemo() {
		return this.guarantorMemo;
	}

	/**
	 * 設定連保人備註
	 * <p/>
	 * 64個全型字
	 **/
	public void setGuarantorMemo(String value) {
		this.guarantorMemo = value;
	}

	/** 取得連保人－同前頁 **/
	public String getGuarantorRef() {
		return this.guarantorRef;
	}

	/** 設定連保人－同前頁 **/
	public void setGuarantorRef(String value) {
		this.guarantorRef = value;
	}

	/** 取得物上保證人 **/
	public String getGuarantor1() {
		return this.guarantor1;
	}

	/** 設定物上保證人 **/
	public void setGuarantor1(String value) {
		this.guarantor1 = value;
	}

	/**
	 * 取得本案未送保原因
	 * <p/>
	 * 100/12/05增列<br/>
	 * 1.非中小企業<br/>
	 * 2.本行績優客戶<br/>
	 * 3.十足擔保<br/>
	 * 4.擔保率超過50%<br/>
	 * 5.營業未滿1年<br/>
	 * 6.外國人資本超過50%<br/>
	 * 7.單一法人所佔資本額超過50%<br/>
	 * 8.其他(請自行輸入)
	 */
	public String getNoInsuReason() {
		return this.noInsuReason;
	}

	/**
	 * 設定本案未送保原因
	 * <p/>
	 * 100/12/05增列<br/>
	 * 1.非中小企業<br/>
	 * 2.本行績優客戶<br/>
	 * 3.十足擔保<br/>
	 * 4.擔保率超過50%<br/>
	 * 5.營業未滿1年<br/>
	 * 6.外國人資本超過50%<br/>
	 * 7.單一法人所佔資本額超過50%<br/>
	 * 8.其他(請自行輸入)
	 **/
	public void setNoInsuReason(String value) {
		this.noInsuReason = value;
	}

	/** 取得本案未送保原因(其他) **/
	public String getNoInsuReasonOther() {
		return this.noInsuReasonOther;
	}

	/** 設定本案未送保原因(其他) **/
	public void setNoInsuReasonOther(String value) {
		this.noInsuReasonOther = value;
	}

	/** 取得擔保授信額度調整幣別 **/
	public String getAssureTotECurr() {
		return this.assureTotECurr;
	}

	/** 設定擔保授信額度調整幣別 **/
	public void setAssureTotECurr(String value) {
		this.assureTotECurr = value;
	}

	/** 取得擔保授信額度調整金額 **/
	public BigDecimal getAssureTotEAmt() {
		return this.assureTotEAmt;
	}

	/** 設定擔保授信額度調整金額 **/
	public void setAssureTotEAmt(BigDecimal value) {
		this.assureTotEAmt = value;
	}

	/**
	 * 取得合計金額調整註記
	 * <p/>
	 * 101/04/12新增<br/>
	 * Y/N
	 */
	public String getValueTune() {
		return this.valueTune;
	}

	/**
	 * 設定合計金額調整註記
	 * <p/>
	 * 101/04/12新增<br/>
	 * Y/N
	 **/
	public void setValueTune(String value) {
		this.valueTune = value;
	}

	/**
	 * 取得前准(批覆)額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getLVTotCurr() {
		return this.LVTotCurr;
	}

	/**
	 * 設定前准(批覆)額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setLVTotCurr(String value) {
		this.LVTotCurr = value;
	}

	/**
	 * 取得前准(批覆)額度合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 由【前准批覆授信額度】合計
	 */
	public BigDecimal getLVTotAmt() {
		return this.LVTotAmt;
	}

	/**
	 * 設定前准(批覆)額度合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 由【前准批覆授信額度】合計
	 **/
	public void setLVTotAmt(BigDecimal value) {
		this.LVTotAmt = value;
	}

	/**
	 * 取得前准擔保授信額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getLVAssTotCurr() {
		return this.LVAssTotCurr;
	}

	/**
	 * 設定前准擔保授信額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setLVAssTotCurr(String value) {
		this.LVAssTotCurr = value;
	}

	/**
	 * 取得前准擔保授信額度合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 由【前准批覆擔保授信額度】合計
	 */
	public BigDecimal getLVAssTotAmt() {
		return this.LVAssTotAmt;
	}

	/**
	 * 設定前准擔保授信額度合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 由【前准批覆擔保授信額度】合計
	 **/
	public void setLVAssTotAmt(BigDecimal value) {
		this.LVAssTotAmt = value;
	}

	/**
	 * 取得新作、增額合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getIncApplyTotCurr() {
		return this.incApplyTotCurr;
	}

	/**
	 * 設定新作、增額合計幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setIncApplyTotCurr(String value) {
		this.incApplyTotCurr = value;
	}

	/**
	 * 取得新作、增額合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 新作、增額合計 = 授信額度合計- 前准額度 + 取消、減額合計
	 */
	public BigDecimal getIncApplyTotAmt() {
		return this.incApplyTotAmt;
	}

	/**
	 * 設定新作、增額合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 新作、增額合計 = 授信額度合計- 前准額度 + 取消、減額合計
	 **/
	public void setIncApplyTotAmt(BigDecimal value) {
		this.incApplyTotAmt = value;
	}

	/**
	 * 取得新作、增額擔保額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getIncAssTotCurr() {
		return this.incAssTotCurr;
	}

	/**
	 * 設定新作、增額擔保額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setIncAssTotCurr(String value) {
		this.incAssTotCurr = value;
	}

	/**
	 * 取得新作、增額擔保額度合計金額
	 * <p/>
	 * 101/04/12新增
	 */
	public BigDecimal getIncAssTotAmt() {
		return this.incAssTotAmt;
	}

	/**
	 * 設定新作、增額擔保額度合計金額
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setIncAssTotAmt(BigDecimal value) {
		this.incAssTotAmt = value;
	}

	/**
	 * 取得取消、減額合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getDecApplyTotCurr() {
		return this.decApplyTotCurr;
	}

	/**
	 * 設定取消、減額合計幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setDecApplyTotCurr(String value) {
		this.decApplyTotCurr = value;
	}

	/**
	 * 取得取消、減額合計金額
	 * <p/>
	 * 101/04/12新增
	 */
	public BigDecimal getDecApplyTotAmt() {
		return this.decApplyTotAmt;
	}

	/**
	 * 設定取消、減額合計金額
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setDecApplyTotAmt(BigDecimal value) {
		this.decApplyTotAmt = value;
	}

	/**
	 * 取得取消、減額擔保額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getDecAssTotCurr() {
		return this.decAssTotCurr;
	}

	/**
	 * 設定取消、減額擔保額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setDecAssTotCurr(String value) {
		this.decAssTotCurr = value;
	}

	/**
	 * 取得取消、減額擔保額度合計金額
	 * <p/>
	 * 101/04/12新增
	 */
	public BigDecimal getDecAssTotAmt() {
		return this.decAssTotAmt;
	}

	/**
	 * 設定取消、減額擔保額度合計金額
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setDecAssTotAmt(BigDecimal value) {
		this.decAssTotAmt = value;
	}

	/** 取得授信額度合計幣別 **/
	public String getLoanTotCurr() {
		return this.LoanTotCurr;
	}

	/** 設定授信額度合計幣別 **/
	public void setLoanTotCurr(String value) {
		this.LoanTotCurr = value;
	}

	/** 取得授信額度合計金額 **/
	public BigDecimal getLoanTotAmt() {
		return this.LoanTotAmt;
	}

	/** 設定授信額度合計金額 **/
	public void setLoanTotAmt(BigDecimal value) {
		this.LoanTotAmt = value;
	}

	/** 取得擔保授信額度合計幣別 **/
	public String getAssureTotCurr() {
		return this.assureTotCurr;
	}

	/** 設定擔保授信額度合計幣別 **/
	public void setAssureTotCurr(String value) {
		this.assureTotCurr = value;
	}

	/** 取得擔保授信額度合計金額 **/
	public BigDecimal getAssureTotAmt() {
		return this.assureTotAmt;
	}

	/** 設定擔保授信額度合計金額 **/
	public void setAssureTotAmt(BigDecimal value) {
		this.assureTotAmt = value;
	}

	/**
	 * 取得擔保品押值合計幣別
	 * <p/>
	 * 101/04/12新增<br/>
	 * ※自行輸入
	 */
	public String getEstTotCurr() {
		return this.estTotCurr;
	}

	/**
	 * 設定擔保品押值合計幣別
	 * <p/>
	 * 101/04/12新增<br/>
	 * ※自行輸入
	 **/
	public void setEstTotCurr(String value) {
		this.estTotCurr = value;
	}

	/**
	 * 取得擔保品押值合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * ※自行輸入
	 */
	public BigDecimal getEstTotAmt() {
		return this.estTotAmt;
	}

	/**
	 * 設定擔保品押值合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * ※自行輸入
	 **/
	public void setEstTotAmt(BigDecimal value) {
		this.estTotAmt = value;
	}

	/** 取得衍生性金融商品合計幣別 **/
	public String getLoanTotZCurr() {
		return this.LoanTotZCurr;
	}

	/** 設定衍生性金融商品合計幣別 **/
	public void setLoanTotZCurr(String value) {
		this.LoanTotZCurr = value;
	}

	/** 取得衍生性金融商品合計金額 **/
	public BigDecimal getLoanTotZAmt() {
		return this.LoanTotZAmt;
	}

	/** 設定衍生性金融商品合計金額 **/
	public void setLoanTotZAmt(BigDecimal value) {
		this.LoanTotZAmt = value;
	}

	/** 取得衍生性金融商品相當授信額度合計幣別 **/
	public String getLoanTotLCurr() {
		return this.LoanTotLCurr;
	}

	/** 設定衍生性金融商品相當授信額度合計幣別 **/
	public void setLoanTotLCurr(String value) {
		this.LoanTotLCurr = value;
	}

	/** 取得衍生性金融商品相當授信額度合計金額 **/
	public BigDecimal getLoanTotLAmt() {
		return this.LoanTotLAmt;
	}

	/** 設定衍生性金融商品相當授信額度合計金額 **/
	public void setLoanTotLAmt(BigDecimal value) {
		this.LoanTotLAmt = value;
	}

	/**
	 * 取得匯率資料日期
	 * <p/>
	 * 100/11/10新增(保留未用)
	 */
	public Date getRateDT() {
		return this.rateDT;
	}

	/**
	 * 設定匯率資料日期
	 * <p/>
	 * 100/11/10新增(保留未用)
	 **/
	public void setRateDT(Date value) {
		this.rateDT = value;
	}

	/**
	 * 取得結算匯率(放款幣折本位幣)
	 * <p/>
	 * 100/11/10新增(保留未用)
	 */
	public BigDecimal getRateLoanToLoc() {
		return this.rateLoanToLoc;
	}

	/**
	 * 設定結算匯率(放款幣折本位幣)
	 * <p/>
	 * 100/11/10新增(保留未用)
	 **/
	public void setRateLoanToLoc(BigDecimal value) {
		this.rateLoanToLoc = value;
	}

	/**
	 * 取得結算匯率(本位幣折合計幣)
	 * <p/>
	 * 100/11/10新增(保留未用)
	 */
	public BigDecimal getRateLocToSum() {
		return this.rateLocToSum;
	}

	/**
	 * 設定結算匯率(本位幣折合計幣)
	 * <p/>
	 * 100/11/10新增(保留未用)
	 **/
	public void setRateLocToSum(BigDecimal value) {
		this.rateLocToSum = value;
	}

	/**
	 * 取得結算匯率(交叉匯率)
	 * <p/>
	 * 100/11/10新增(保留未用)<br/>
	 * (保留未用)<br/>
	 * 放款幣先折算本位幣，本位幣再折算合計幣。
	 */
	public BigDecimal getRateCrossover() {
		return this.rateCrossover;
	}

	/**
	 * 設定結算匯率(交叉匯率)
	 * <p/>
	 * 100/11/10新增(保留未用)<br/>
	 * (保留未用)<br/>
	 * 放款幣先折算本位幣，本位幣再折算合計幣。
	 **/
	public void setRateCrossover(BigDecimal value) {
		this.rateCrossover = value;
	}

	/**
	 * 取得折算匯率
	 * <p/>
	 * 100/11/10新增(保留未用)<br/>
	 * 預設值同「交叉匯率」欄。<br/>
	 * 本欄開放user自行調整，如有異動，則清空「授信額度合計金額」欄。<br/>
	 * 執行【授信額度合計】時，依此匯率計算合計。<br/>
	 * 若執行【授信額度合計】所選取的「合計幣別」與「授信額度合計幣別」欄不同時，則讀取匯率檔重新計算。
	 */
	public BigDecimal getRateConvert() {
		return this.rateConvert;
	}

	/**
	 * 設定折算匯率
	 * <p/>
	 * 100/11/10新增(保留未用)<br/>
	 * 預設值同「交叉匯率」欄。<br/>
	 * 本欄開放user自行調整，如有異動，則清空「授信額度合計金額」欄。<br/>
	 * 執行【授信額度合計】時，依此匯率計算合計。<br/>
	 * 若執行【授信額度合計】所選取的「合計幣別」與「授信額度合計幣別」欄不同時，則讀取匯率檔重新計算。
	 **/
	public void setRateConvert(BigDecimal value) {
		this.rateConvert = value;
	}

	/** 取得備註 **/
	public String getRmk() {
		return this.Rmk;
	}

	/** 設定備註 **/
	public void setRmk(String value) {
		this.Rmk = value;
	}

	/**
	 * 取得各幣別授信額度合計註記
	 * <p/>
	 * 101/04/12新增<br/>
	 * Y/N
	 */
	public String getMultiAmtFlag() {
		return this.multiAmtFlag;
	}

	/**
	 * 設定各幣別授信額度合計註記
	 * <p/>
	 * 101/04/12新增<br/>
	 * Y/N
	 **/
	public void setMultiAmtFlag(String value) {
		this.multiAmtFlag = value;
	}

	/** 取得各幣別授信額度合計 **/
	public String getMultiAmt() {
		return this.multiAmt;
	}

	/** 設定各幣別授信額度合計 **/
	public void setMultiAmt(String value) {
		this.multiAmt = value;
	}

	/**
	 * 取得各幣別其中擔保合計
	 * <p/>
	 * 101/04/12新增
	 */
	public String getMultiAssureAmt() {
		return this.multiAssureAmt;
	}

	/**
	 * 設定各幣別其中擔保合計
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setMultiAssureAmt(String value) {
		this.multiAssureAmt = value;
	}

	/**
	 * 取得輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/16調整<br/>
	 * Y：檢誤完成(包含計算合計)<br/>
	 * N：檢誤完成(尚未計算合計)<br/>
	 * 空值：檢誤未完成<br/>
	 * 儲存時檢核所有必要欄位是否已完備，完成則為N。(若chkYN原為Y，亦修改為N)<br/>
	 * 計算合計時檢核所有額度明細表chkYN若尚有空值，則不能執行；計算合計完成後則修改chkYN為Y。<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}

	/**
	 * 設定輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/16調整<br/>
	 * Y：檢誤完成(包含計算合計)<br/>
	 * N：檢誤完成(尚未計算合計)<br/>
	 * 空值：檢誤未完成<br/>
	 * 儲存時檢核所有必要欄位是否已完備，完成則為N。(若chkYN原為Y，亦修改為N)<br/>
	 * 計算合計時檢核所有額度明細表chkYN若尚有空值，則不能執行；計算合計完成後則修改chkYN為Y。<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	public String getHasDerivatives() {
		return hasDerivatives;
	}

	public void setHasDerivatives(String hasDerivatives) {
		this.hasDerivatives = hasDerivatives;
	}

	public L140M03A getL140M03A() {
		return l140m03a;
	}

	public void setL140M03A(L140M03A l140m03a) {
		this.l140m03a = l140m03a;
	}

	public void setNotesUp(String notesUp) {
		this.notesUp = notesUp;
	}

	public String getNotesUp() {
		return notesUp;
	}

	/**
	 * 設定按轉讓發票金額%動用
	 * 
	 * @param loanPer
	 */
	public void setLoanPer(BigDecimal loanPer) {
		this.loanPer = loanPer;
	}

	/**
	 * 取得按轉讓發票金額%動用
	 * 
	 * @return
	 */
	public BigDecimal getLoanPer() {
		return loanPer;
	}

	/**
	 * 設定可否借新還舊
	 * 
	 * @param byNewOld
	 */
	public void setByNewOld(String byNewOld) {
		this.byNewOld = byNewOld;
	}

	/**
	 * 取得可否借新還舊
	 * 
	 * @return
	 */
	public String getByNewOld() {
		return byNewOld;
	}

	/**
	 * 設定本案是否為信保續約案
	 * 
	 * @param isGuaOldCase
	 */
	public void setIsGuaOldCase(String isGuaOldCase) {
		this.isGuaOldCase = isGuaOldCase;
	}

	/**
	 * 取得本案是否為信保續約案
	 * 
	 * @return
	 */
	public String getIsGuaOldCase() {
		return isGuaOldCase;
	}

	/**
	 * 設定產品種類
	 * 
	 * @param byNewOld
	 */
	public void setLnType(String lnType) {
		this.lnType = lnType;
	}

	/**
	 * 取得產品種類
	 * 
	 * @return
	 */
	public String getLnType() {
		return lnType;
	}

	/**
	 * 設定本行帳務是否有聯貸拆帳作業
	 * 
	 * @param byNewOld
	 */
	public void setHasLoanAssign(String hasLoanAssign) {
		this.hasLoanAssign = hasLoanAssign;
	}

	/**
	 * 取得本行帳務是否有聯貸拆帳作業
	 * 
	 * @return
	 */
	public String getHasLoanAssign() {
		return hasLoanAssign;
	}

	/**
	 * 設定青年創業及啟動金貸款 請日期
	 * 
	 * @param applyDate
	 */
	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	/**
	 * 取得青年創業及啟動金貸款 申請日期
	 * 
	 * @return
	 */
	public Date getApplyDate() {
		return applyDate;
	}

	/** 取得各幣別衍生性授信額度合計 **/
	public String getDervMultiAmt() {
		return this.dervMultiAmt;
	}

	/** 設定各幣別衍生性授信額度合計 **/
	public void setDervMultiAmt(String value) {
		this.dervMultiAmt = value;
	}

	/**
	 * 衍生性授信額度合計多幣別說明註記
	 * 
	 * @param newDervMultiAmtFlag
	 */
	public void setNewDervMultiAmtFlag(String newDervMultiAmtFlag) {
		this.newDervMultiAmtFlag = newDervMultiAmtFlag;
	}

	/**
	 * 衍生性授信額度合計多幣別說明註記
	 * 
	 * @param newDervMultiAmtFlag
	 */
	public String getNewDervMultiAmtFlag() {
		return newDervMultiAmtFlag;
	}

	/**
	 * 設定衍生性係數複選組合字串
	 * 
	 * @param derivativesNumDscr
	 */
	public void setDerivativesNumDscr(String derivativesNumDscr) {
		this.derivativesNumDscr = derivativesNumDscr;
	}

	/**
	 * 取得衍生性係數複選組合字串
	 * 
	 * @param derivativesNumDscr
	 */
	public String getDerivativesNumDscr() {
		return derivativesNumDscr;
	}

	/**
	 * 設定額度評等
	 * 
	 * @param derivativesNumDscr
	 */
	public void setFacilityRating(String facilityRating) {
		this.facilityRating = facilityRating;
	}

	/**
	 * 取得額度評等
	 * 
	 * @param derivativesNumDscr
	 */
	public String getFacilityRating() {
		return facilityRating;
	}

	/** 設定現請擔保額度－幣別 **/
	public void setAssureApplyCurr(String value) {
		this.assureApplyCurr = value;
	}

	/** 取得現請擔保額度－幣別 **/
	public String getAssureApplyCurr() {
		return this.assureApplyCurr;
	}

	/** 設定現請擔保額度－金額 **/
	public void setAssureApplyAmt(BigDecimal value) {
		this.assureApplyAmt = value;
	}

	/** 取得現請擔保額度－金額 **/
	public BigDecimal getAssureApplyAmt() {
		return this.assureApplyAmt;
	}

	/**
	 * 設定列印明細 1:現請金額/2:信評
	 * **/
	public void setPrintDetails(String value) {
		this.printDetails = value;
	}

	/**
	 * 取得列印明細 1:現請金額/2:信評
	 * **/
	public String getPrintDetails() {
		return this.printDetails;
	}

	/**
	 * 設定保證人是否按一定比率負担保證責任
	 * **/
	public void setGuaPercentFg(String guaPercentFg) {
		this.guaPercentFg = guaPercentFg;
	}

	/**
	 * 取得保證人是否按一定比率負担保證責任
	 * **/
	public String getGuaPercentFg() {
		return guaPercentFg;
	}

	/**
	 * 設定簽報書OID(計算額度合計後塞值，用來判斷是否需要重新計算授信額度合計)
	 */
	public void setCntrChgOid(String cntrChgOid) {
		this.cntrChgOid = cntrChgOid;
	}

	/**
	 * 取得簽報書OID(計算額度合計後塞值，用來判斷是否需要重新計算授信額度合計)
	 */
	public String getCntrChgOid() {
		return cntrChgOid;
	}

	/**
	 * 取得擔保品是否為房屋
	 */
	public String getHeadItem7() {
		return headItem7;
	}

	/**
	 * 設定擔保品是否為房屋
	 */
	public void setHeadItem7(String headItem7) {
		this.headItem7 = headItem7;
	}

	/**
	 * 設定本案是否屬銀行法72-2條控管對象
	 */
	public void setIs722Flag(String is722Flag) {
		this.is722Flag = is722Flag;
	}

	/**
	 * 取得本案是否屬銀行法72-2條控管對象
	 */
	public String getIs722Flag() {
		return is722Flag;
	}

	/**
	 * 設定 現行帳務系統或e-Loan前次報案(帳務系統無資料時)是否屬銀行法72-2條控管對象{Y , N , A:兩種都有}
	 */
	public void setIs722OnFlag(String is722OnFlag) {
		this.is722OnFlag = is722OnFlag;
	}

	/**
	 * 取得 現行帳務系統或e-Loan前次報案(帳務系統無資料時)是否屬銀行法72-2條控管對象{Y , N , A:兩種都有}
	 */
	public String getIs722OnFlag() {
		return is722OnFlag;
	}

	/**
	 * 設定查詢銀行法72-2條控管對象額度序號
	 */
	public void setIs722CntrNo(String is722CntrNo) {
		this.is722CntrNo = is722CntrNo;
	}

	/**
	 * 取得查詢銀行法72-2條控管對象額度序號
	 */
	public String getIs722CntrNo() {
		return is722CntrNo;
	}

	/**
	 * 設定查詢銀行法72-2條控管對象日期
	 */
	public void setIs722QDate(Date is722QDate) {
		this.is722QDate = is722QDate;
	}

	/**
	 * 取得查詢銀行法72-2條控管對象日期
	 */
	public Date getIs722QDate() {
		return is722QDate;
	}

	/**
	 * 設定額度序號檢核是否存在帳務擋註記
	 */
	public void setCntrNoChkExistFlag(String cntrNoChkExistFlag) {
		this.cntrNoChkExistFlag = cntrNoChkExistFlag;
	}

	/**
	 * 取得額度序號檢核是否存在帳務擋註記
	 */
	public String getCntrNoChkExistFlag() {
		return cntrNoChkExistFlag;
	}

	/**
	 * 設定額度序號檢核是否存在帳務擋日期
	 */
	public void setCntrNoChkExistDate(Date cntrNoChkExistDate) {
		this.cntrNoChkExistDate = cntrNoChkExistDate;
	}

	/**
	 * 取得額度序號檢核是否存在帳務擋日期
	 */
	public Date getCntrNoChkExistDate() {
		return cntrNoChkExistDate;
	}

	/**
	 * 取得是否為購置不動產
	 * 
	 * @return
	 */
	public String getIsBuy() {
		return isBuy;
	}

	/**
	 * 設定是否為購置不動產
	 * 
	 * @param isBuy
	 */
	public void setIsBuy(String isBuy) {
		this.isBuy = isBuy;
	}

	/**
	 * 取得排除條件
	 * 
	 * @return
	 */
	public String getExItem() {
		return exItem;
	}

	/**
	 * 設定排除條件
	 * 
	 * @param exItem
	 */
	public void setExItem(String exItem) {
		this.exItem = exItem;
	}

	/**
	 * 取得前案是否為購置不動產
	 * 
	 * @return
	 */
	public String getIsBuyOn() {
		return isBuyOn;
	}

	/**
	 * 設定前案是否為購置不動產
	 * 
	 * @param isBuyOn
	 */
	public void setIsBuyOn(String isBuyOn) {
		this.isBuyOn = isBuyOn;
	}

	/**
	 * 取得前案排除條件
	 * 
	 * @return
	 */
	public String getExItemOn() {
		return exItemOn;
	}

	/**
	 * 設定前案排除條件
	 * 
	 * @param exItemOn
	 */
	public void setExItemOn(String exItemOn) {
		this.exItemOn = exItemOn;
	}

	/**
	 * 設定利害關係人敘作無擔保授信註記 J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 */
	public void setUnsecureFlag(String unsecureFlag) {
		this.unsecureFlag = unsecureFlag;
	}

	/**
	 * 取得利害關係人敘作無擔保授信註記 J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 */
	public String getUnsecureFlag() {
		return unsecureFlag;
	}

	/**
	 * 設定備份原NOLOAN J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 */
	public void setNoLoanBk(String noLoanBk) {
		this.noLoanBk = noLoanBk;
	}

	/**
	 * 取得備份原NOLOAN J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 */
	public String getNoLoanBk() {
		return noLoanBk;
	}

	/**
	 * 設定是否為供應鏈融資 J-104-0284-001 額度明細表檢核供應鏈融資賣放限週轉科目
	 */
	public void setIsEfin(String isEfin) {
		this.isEfin = isEfin;
	}

	/**
	 * 取得是否為供應鏈融資 J-104-0284-001 額度明細表檢核供應鏈融資賣放限週轉科目
	 */
	public String getIsEfin() {
		return isEfin;
	}

	/**
	 * 設定是否收取帳務管理費 J-104-0264-001 額度明細表新增是否收取帳務管理費
	 */
	public void setHasAmFee(String hasAmFee) {
		this.hasAmFee = hasAmFee;
	}

	/**
	 * 取得是否收取帳務管理費 J-104-0264-001 額度明細表新增是否收取帳務管理費
	 */
	public String getHasAmFee() {
		return hasAmFee;
	}

	/**
	 * 設定是否為振興經濟非中小企業專案貸款 J-104-0339-001 增加振興經濟非中小企業專案貸款案件及金額欄位
	 */
	public void setIsNonSMEProjLoan(String isNonSMEProjLoan) {
		this.isNonSMEProjLoan = isNonSMEProjLoan;
	}

	/**
	 * 取得是否為振興經濟非中小企業專案貸款 J-104-0339-001 增加振興經濟非中小企業專案貸款案件及金額欄位
	 */
	public String getIsNonSMEProjLoan() {
		return isNonSMEProjLoan;
	}

	/**
	 * 設定振興經濟非中小企業專案貸款金額 J-104-0339-001 增加振興經濟非中小企業專案貸款案件及金額欄位
	 */
	public void setNonSMEProjLoanAmt(BigDecimal nonSMEProjLoanAmt) {
		this.nonSMEProjLoanAmt = nonSMEProjLoanAmt;
	}

	/**
	 * 取得振興經濟非中小企業專案貸款金額 J-104-0339-001 增加振興經濟非中小企業專案貸款案件及金額欄位
	 */
	public BigDecimal getNonSMEProjLoanAmt() {
		return nonSMEProjLoanAmt;
	}

	/**
	 * 設定本額度是否有下限利率 J-105-0088-001 Web e-Loan
	 * 企金授信系統利率條件無下限利率時，新增a-Loan是否需建置下限利率欄位並上傳a-Loan檢核。
	 */
	public void setHasRateLimit(String hasRateLimit) {
		this.hasRateLimit = hasRateLimit;
	}

	/**
	 * 取得本額度是否有下限利率 J-105-0088-001 Web e-Loan
	 * 企金授信系統利率條件無下限利率時，新增a-Loan是否需建置下限利率欄位並上傳a-Loan檢核。
	 */
	public String getHasRateLimit() {
		return hasRateLimit;
	}

	/**
	 * 設定是否為振興經濟非中小企業專案貸款 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public void setCIsNonSMEProjLoan(String cIsNonSMEProjLoan) {
		this.cIsNonSMEProjLoan = cIsNonSMEProjLoan;
	}

	/**
	 * 取得是否為振興經濟非中小企業專案貸款 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public String getCIsNonSMEProjLoan() {
		return cIsNonSMEProjLoan;
	}

	/**
	 * 設定振興經濟非中小企業專案貸款金額 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public void setCNonSMEProjLoanAmt(BigDecimal cNonSMEProjLoanAmt) {
		this.cNonSMEProjLoanAmt = cNonSMEProjLoanAmt;
	}

	/**
	 * 取得振興經濟非中小企業專案貸款金額 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public BigDecimal getCNonSMEProjLoanAmt() {
		return cNonSMEProjLoanAmt;
	}

	/**
	 * 設定振興經濟非中小企業專案貸款金額變更動審表MAINID J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public void setCNonSMEProjMainId(String cNonSMEProjMainId) {
		this.cNonSMEProjMainId = cNonSMEProjMainId;
	}

	/**
	 * 取得振興經濟非中小企業專案貸款金額變更動審表MAINID J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public String getCNonSMEProjMainId() {
		return cNonSMEProjMainId;
	}

	/**
	 * 設定衍生性金融商品期數版本 N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
	 */
	public void setDerivativeVersion(String derivativeVersion) {
		this.derivativeVersion = derivativeVersion;
	}

	/**
	 * 取得衍生性金融商品期數版本 N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
	 */
	public String getDerivativeVersion() {
		return derivativeVersion;
	}

	/**
	 * 設定約定融資額度註記 J-105-0155-001 Web e-Loan國內、海外企金額度明細表增加『約定融資額度註記』欄位與上傳a-Loan功能
	 */
	public void setExceptFlag(String exceptFlag) {
		this.exceptFlag = exceptFlag;
	}

	/**
	 * 取得約定融資額度註記 J-105-0155-001 Web e-Loan國內、海外企金額度明細表增加『約定融資額度註記』欄位與上傳a-Loan功能
	 */
	public String getExceptFlag() {
		return exceptFlag;
	}

	/**
	 * 設定產品種類新創產業細目 J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 * 
	 * @param itwCode
	 */
	public void setItwCode(String itwCode) {
		this.itwCode = itwCode;
	}

	/**
	 * 取得產品種類新創產業細目 J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 * 
	 * @return
	 */
	public String getItwCode() {
		return itwCode;
	}

	/**
	 * 設定借款人行業別是否列於新創重點產業 J-105-0308-001 Web
	 * e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 * 
	 * @param itwCode
	 */
	public void setIsStartUp(String isStartUp) {
		this.isStartUp = isStartUp;
	}

	/**
	 * 取得借款人行業別是否列於新創重點產業 J-105-0308-001 Web
	 * e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 * 
	 * @return
	 */
	public String getIsStartUp() {
		return isStartUp;
	}

	/**
	 * 設定物上保證人 J-106-0029-001
	 * 
	 * @param itwCode
	 */
	public void setGuarantor1Ref(String guarantor1Ref) {
		this.guarantor1Ref = guarantor1Ref;
	}

	/**
	 * 取得物上保證人 J-106-0029-001
	 * 
	 * @return
	 */
	public String getGuarantor1Ref() {
		return guarantor1Ref;
	}

	/** 設定是否屬未核配額度國家 **/
	public void setIsNoFactCountry(String isNoFactCountry) {
		this.isNoFactCountry = isNoFactCountry;
	}

	/** 取得是否屬未核配額度國家 **/
	public String getIsNoFactCountry() {
		return isNoFactCountry;
	}

	/** 設定未核配額度國家 **/
	public void setNoFactCountry(String noFactCountry) {
		this.noFactCountry = noFactCountry;
	}

	/** 取得未核配額度國家 **/
	public String getNoFactCountry() {
		return noFactCountry;
	}

	/** 設定是否屬凍結額度國家 **/
	public void setIsFreezeFactCountry(String isFreezeFactCountry) {
		this.isFreezeFactCountry = isFreezeFactCountry;
	}

	/** 取得是否屬凍結額度國家 **/
	public String getIsFreezeFactCountry() {
		return isFreezeFactCountry;
	}

	/** 設定凍結額度國家 **/
	public void setFreezeFactCountry(String freezeFactCountry) {
		this.freezeFactCountry = freezeFactCountry;
	}

	/** 取得凍結額度國家 **/
	public String getFreezeFactCountry() {
		return freezeFactCountry;
	}

	/** 設定是否屬中小企業創新發展專案貸款 **/
	public void setInSmeFg(String inSmeFg) {
		this.inSmeFg = inSmeFg;
	}

	/** 取得是否屬中小企業創新發展專案貸款 **/
	public String getInSmeFg() {
		return inSmeFg;
	}

	/** 設定週轉性支出 **/
	public void setInSmeToAmt(BigDecimal inSmeToAmt) {
		this.inSmeToAmt = inSmeToAmt;
	}

	/** 取得週轉性支出 **/
	public BigDecimal getInSmeToAmt() {
		return inSmeToAmt;
	}

	/** 設定資本性支出 **/
	public void setInSmeCaAmt(BigDecimal inSmeCaAmt) {
		this.inSmeCaAmt = inSmeCaAmt;
	}

	/** 取得資本性支出 **/
	public BigDecimal getInSmeCaAmt() {
		return inSmeCaAmt;
	}

	/** 設定非避險額度－額外信用增強 **/
	public void setEnhanceAmt(BigDecimal enhanceAmt) {
		this.enhanceAmt = enhanceAmt;
	}

	/** 取得非避險額度－額外信用增強 **/
	public BigDecimal getEnhanceAmt() {
		return enhanceAmt;
	}

	/** 設定借款人行業別所屬新創重點產業細目 **/
	public void setBelongItwCode(String belongItwCode) {
		this.belongItwCode = belongItwCode;
	}

	/** 取得借款人行業別所屬新創重點產業細目 **/
	public String getBelongItwCode() {
		return belongItwCode;
	}

	/** 取得引介來源 */
	public String getIntroduceSrc() {
		return introduceSrc;
	}

	/** 設定引介來源 */
	public void setIntroduceSrc(String introduceSrc) {
		this.introduceSrc = introduceSrc;
	}

	/** 取得引介行員代號 */
	public String getMegaEmpNo() {
		return megaEmpNo;
	}

	/** 設定引介行員代號 */
	public void setMegaEmpNo(String megaEmpNo) {
		this.megaEmpNo = megaEmpNo;
	}

	/** 取得引介房仲代號{11111:永慶, 22222:信義, 55555:住商} */
	public String getAgntNo() {
		return agntNo;
	}

	/** 設定引介房仲代號{11111:永慶, 22222:信義, 55555:住商} */
	public void setAgntNo(String agntNo) {
		this.agntNo = agntNo;
	}

	/** 取得引介房仲連鎖店類型{A:直營店, B:加盟店} */
	public String getAgntChain() {
		return agntChain;
	}

	/** 設定引介房仲連鎖店類型{A:直營店, B:加盟店} */
	public void setAgntChain(String agntChain) {
		this.agntChain = agntChain;
	}

	/** 取得引介子公司代號{90001, 90002...} */
	public String getMegaCode() {
		return megaCode;
	}

	/** 設定引介子公司代號 {90001, 90002...} */
	public void setMegaCode(String megaCode) {
		this.megaCode = megaCode;
	}

	/**
	 * 取得引介子公司分支代號 select * from com.bcodetype where CODETYPE like
	 * 'LNF13E_SUB_UNITNO_%'
	 */
	public String getSubUnitNo() {
		return subUnitNo;
	}

	/**
	 * 設定引介子公司分支代號 select * from com.bcodetype where CODETYPE like
	 * 'LNF13E_SUB_UNITNO_%'
	 */
	public void setSubUnitNo(String subUnitNo) {
		this.subUnitNo = subUnitNo;
	}

	/** 取得引介子公司員工編號 */
	public String getSubEmpNo() {
		return subEmpNo;
	}

	/** 設定引介子公司員工編號 */
	public void setSubEmpNo(String subEmpNo) {
		this.subEmpNo = subEmpNo;
	}

	/** 取得引介子公司員工姓名 */
	public String getSubEmpNm() {
		return subEmpNm;
	}

	/** 設定引介子公司員工姓名 */
	public void setSubEmpNm(String subEmpNm) {
		this.subEmpNm = subEmpNm;
	}

	/** 設定帳款管理商類別 **/
	public void setArAccManagerType(String arAccManagerType) {
		this.arAccManagerType = arAccManagerType;
	}

	/** 取得帳款管理商類別 **/
	public String getArAccManagerType() {
		return arAccManagerType;
	}

	/** 設定帳款管理商ID **/
	public void setArAccManagerId(String arAccManagerId) {
		this.arAccManagerId = arAccManagerId;
	}

	/** 取得帳款管理商ID **/
	public String getArAccManagerId() {
		return arAccManagerId;
	}

	/** 設定帳款管理商名稱 **/
	public void setArAccManagerNm(String arAccManagerNm) {
		this.arAccManagerNm = arAccManagerNm;
	}

	/** 取得帳款管理商名稱 **/
	public String getArAccManagerNm() {
		return arAccManagerNm;
	}

	/** 設定信保基金核准之保證手續費率 **/
	public void setCgfRate(BigDecimal cgfRate) {
		this.cgfRate = cgfRate;
	}

	/** 取得信保基金核准之保證手續費率 **/
	public BigDecimal getCgfRate() {
		return cgfRate;
	}

	/** 設定信保基金保證書發文日期 **/
	public void setCgfDate(Date cgfDate) {
		this.cgfDate = cgfDate;
	}

	/** 取得信保基金保證書發文日期 **/
	public Date getCgfDate() {
		return cgfDate;
	}

	/** 是否為重建 **/
	public void setRebuild(String rebuild) {
		this.rebuild = rebuild;
	}

	/** 是否為重建 **/
	public String getRebuild() {
		return rebuild;
	}

	public void setIsInstalment(String isInstalment) {
		this.isInstalment = isInstalment;
	}

	public String getIsInstalment() {
		return isInstalment;
	}

	public void setIsInstalmentOn(String isInstalmentOn) {
		this.isInstalmentOn = isInstalmentOn;
	}

	public String getIsInstalmentOn() {
		return isInstalmentOn;
	}

	/**
	 * J-107-0357_05097_B1001 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
	 * 設定專案種類
	 * 
	 * @param projClass
	 */
	public void setProjClass(String projClass) {
		this.projClass = projClass;
	}

	/**
	 * J-107-0357_05097_B1001 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
	 * 取得專案種類
	 * 
	 * @return
	 */
	public String getProjClass() {
		return projClass;
	}

	/**
	 * J-108-0116 共同行銷 CrossSelling 設定共同行銷種類
	 * 
	 * @param projClass
	 */
	public void setCsTypes(String csTypes) {
		this.csTypes = csTypes;
	}

	/**
	 * J-108-0116 共同行銷 CrossSelling 取得共同行銷種類
	 * 
	 * @return
	 */
	public String getCsTypes() {
		return csTypes;
	}

	/** 取得擔保品是否為本行外幣十足存款 */
	public String getHeadItem8() {
		return headItem8;
	}

	/** 設定擔保品是否為本行外幣十足存款 */
	public void setHeadItem8(String headItem8) {
		this.headItem8 = headItem8;
	}

	/**
	 * 取得是否符合出口實績規範
	 */
	public String getExperf_fg() {
		return experf_fg;
	}

	/**
	 * 設定是否符合出口實績規範
	 */
	public void setExperf_fg(String experf_fg) {
		this.experf_fg = experf_fg;
	}

	/**
	 * 取得瑕疵額度控管方式
	 */
	public String getFlaw_fg() {
		return flaw_fg;
	}

	/**
	 * 設定瑕疵額度控管方式
	 */
	public void setFlaw_fg(String flaw_fg) {
		this.flaw_fg = flaw_fg;
	}

	/**
	 * 取得瑕疵額度限額
	 */
	public BigDecimal getFlaw_amt() {
		return flaw_amt;
	}

	/**
	 * 設定瑕疵額度限額
	 */
	public void setFlaw_amt(BigDecimal flaw_amt) {
		this.flaw_amt = flaw_amt;
	}

	/**
	 * 設定報核批號
	 */
	public void setRandomCodeSbr(String randomCodeSbr) {
		this.randomCodeSbr = randomCodeSbr;
	}

	/**
	 * 取得報核批號
	 */
	public String getRandomCodeSbr() {
		return randomCodeSbr;
	}

	/**
	 * 設定是否為徵提股票為擔保
	 */
	public void setIsCollStock(String isCollStock) {
		this.isCollStock = isCollStock;
	}

	/**
	 * 取得是否為徵提股票為擔保
	 */
	public String getIsCollStock() {
		return isCollStock;
	}

	/**
	 * 設定本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
	 * 
	 * @param isRescue
	 */
	public void setIsRescue(String isRescue) {
		this.isRescue = isRescue;
	}

	/**
	 * 取得本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
	 * 
	 * @param isRescue
	 */
	public String getIsRescue() {
		return isRescue;
	}

	/**
	 * 設定紓困貸款類別
	 * 
	 * @param isRescue
	 */
	public void setRescueItem(String rescueItem) {
		this.rescueItem = rescueItem;
	}

	/**
	 * 取得紓困貸款類別
	 * 
	 * @param isRescue
	 */
	public String getRescueItem() {
		return rescueItem;
	}

	/**
	 * 設定減收利率
	 * 
	 * @param isRescue
	 */
	public void setRescueRate(BigDecimal rescueRate) {
		this.rescueRate = rescueRate;
	}

	/**
	 * 取得減收利率
	 * 
	 * @param isRescue
	 */
	public BigDecimal getRescueRate() {
		return rescueRate;
	}

	/**
	 * 設定本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
	 * 
	 * @param isRescue
	 */
	public void setIsRescue_Af(String isRescue_Af) {
		this.isRescue_Af = isRescue_Af;
	}

	/**
	 * 取得本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
	 * 
	 * @param isRescue
	 */
	public String getIsRescue_Af() {
		return isRescue_Af;
	}

	/**
	 * 設定紓困貸款類別
	 * 
	 * @param isRescue
	 */
	public void setRescueItem_Af(String rescueItem_Af) {
		this.rescueItem_Af = rescueItem_Af;
	}

	/**
	 * 取得紓困貸款類別
	 * 
	 * @param isRescue
	 */
	public String getRescueItem_Af() {
		return rescueItem_Af;
	}

	/**
	 * 設定減收利率
	 * 
	 * @param isRescue
	 */
	public void setRescueRate_Af(BigDecimal rescueRate_Af) {
		this.rescueRate_Af = rescueRate_Af;
	}

	/**
	 * 取得減收利率
	 * 
	 * @param isRescue
	 */
	public BigDecimal getRescueRate_Af() {
		return rescueRate_Af;
	}

	public void setIsExtendSixMon(String isExtendSixMon) {
		this.isExtendSixMon = isExtendSixMon;
	}

	public String getIsExtendSixMon() {
		return isExtendSixMon;
	}

	public void setRescueIbDate(Date rescueIbDate) {
		this.rescueIbDate = rescueIbDate;
	}

	public Date getRescueIbDate() {
		return rescueIbDate;
	}

	public void setRescueAmt(BigDecimal rescueAmt) {
		this.rescueAmt = rescueAmt;
	}

	public BigDecimal getRescueAmt() {
		return rescueAmt;
	}

	public void setIsCbRefin(String isCbRefin) {
		this.isCbRefin = isCbRefin;
	}

	public String getIsCbRefin() {
		return isCbRefin;
	}

	public void setRescueAmt_Af(BigDecimal rescueAmt_Af) {
		this.rescueAmt_Af = rescueAmt_Af;
	}

	public BigDecimal getRescueAmt_Af() {
		return rescueAmt_Af;
	}

	public void setRescueCurr(String rescueCurr) {
		this.rescueCurr = rescueCurr;
	}

	public String getRescueCurr() {
		return rescueCurr;
	}

	public void setRescueCurr_Af(String rescueCurr_Af) {
		this.rescueCurr_Af = rescueCurr_Af;
	}

	public String getRescueCurr_Af() {
		return rescueCurr_Af;
	}

	public void setIsCbRefin_Af(String isCbRefin_Af) {
		this.isCbRefin_Af = isCbRefin_Af;
	}

	public String getIsCbRefin_Af() {
		return isCbRefin_Af;
	}

	public void setRescueItemSub(String rescueItemSub) {
		this.rescueItemSub = rescueItemSub;
	}

	public String getRescueItemSub() {
		return rescueItemSub;
	}

	public void setRescueItemSub_Af(String rescueItemSub_Af) {
		this.rescueItemSub_Af = rescueItemSub_Af;
	}

	public String getRescueItemSub_Af() {
		return rescueItemSub_Af;
	}

	public void setRescueDate(Date rescueDate) {
		this.rescueDate = rescueDate;
	}

	public Date getRescueDate() {
		return rescueDate;
	}

	public void setRescueDate_Af(Date rescueDate_Af) {
		this.rescueDate_Af = rescueDate_Af;
	}

	public Date getRescueDate_Af() {
		return rescueDate_Af;
	}

	public void setIsSmallBuss(String isSmallBuss) {
		this.isSmallBuss = isSmallBuss;
	}

	public String getIsSmallBuss() {
		return isSmallBuss;
	}

	public void setSbRegistPeriod(BigDecimal sbRegistPeriod) {
		this.sbRegistPeriod = sbRegistPeriod;
	}

	public BigDecimal getSbRegistPeriod() {
		return sbRegistPeriod;
	}

	public void setSbPrincipalPeriod(BigDecimal sbPrincipalPeriod) {
		this.sbPrincipalPeriod = sbPrincipalPeriod;
	}

	public BigDecimal getSbPrincipalPeriod() {
		return sbPrincipalPeriod;
	}

	public void setSbCreditRating(BigDecimal sbCreditRating) {
		this.sbCreditRating = sbCreditRating;
	}

	public BigDecimal getSbCreditRating() {
		return sbCreditRating;
	}

	public void setSbColStatus(String sbColStatus) {
		this.sbColStatus = sbColStatus;
	}

	public String getSbColStatus() {
		return sbColStatus;
	}

	public void setSbBussStatus(String sbBussStatus) {
		this.sbBussStatus = sbBussStatus;
	}

	public String getSbBussStatus() {
		return sbBussStatus;
	}

	public void setSbScore(BigDecimal sbScore) {
		this.sbScore = sbScore;
	}

	public BigDecimal getSbScore() {
		return sbScore;
	}

	public void setSbHasCreditRating(String sbHasCreditRating) {
		this.sbHasCreditRating = sbHasCreditRating;
	}

	public String getSbHasCreditRating() {
		return sbHasCreditRating;
	}

	public void setSbReasonCode(String sbReasonCode) {
		this.sbReasonCode = sbReasonCode;
	}

	public String getSbReasonCode() {
		return sbReasonCode;
	}

	public void setIsRevive(String isRevive) {
		this.isRevive = isRevive;
	}

	public String getIsRevive() {
		return isRevive;
	}

	public void setReviveTarget(String reviveTarget) {
		this.reviveTarget = reviveTarget;
	}

	public String getReviveTarget() {
		return reviveTarget;
	}

	public void setReviveCoreIndustry(String reviveCoreIndustry) {
		this.reviveCoreIndustry = reviveCoreIndustry;
	}

	public String getReviveCoreIndustry() {
		return reviveCoreIndustry;
	}

	public void setReviveChain(String reviveChain) {
		this.reviveChain = reviveChain;
	}

	public String getReviveChain() {
		return reviveChain;
	}

	public void setReviveLoanPurpose(String reviveLoanPurpose) {
		this.reviveLoanPurpose = reviveLoanPurpose;
	}

	public String getReviveLoanPurpose() {
		return reviveLoanPurpose;
	}

	public void setIsRescueIntroduce(String isRescueIntroduce) {
		this.isRescueIntroduce = isRescueIntroduce;
	}

	public String getIsRescueIntroduce() {
		return isRescueIntroduce;
	}

	public void setRescueIntroduceBrNo(String rescueIntroduceBrNo) {
		this.rescueIntroduceBrNo = rescueIntroduceBrNo;
	}

	public String getRescueIntroduceBrNo() {
		return rescueIntroduceBrNo;
	}

	public void setRescueIntroduceUserId(String rescueIntroduceUserId) {
		this.rescueIntroduceUserId = rescueIntroduceUserId;
	}

	public String getRescueIntroduceUserId() {
		return rescueIntroduceUserId;
	}

	public void setIsMegaSuperProfitProject(String isMegaSuperProfitProject) {
		this.isMegaSuperProfitProject = isMegaSuperProfitProject;
	}

	public String getIsMegaSuperProfitProject() {
		return isMegaSuperProfitProject;
	}

	/** 取得保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構 **/
	public String getIsOfficialCga() {
		return this.isOfficialCga;
	}

	/** 設定保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構 **/
	public void setIsOfficialCga(String value) {
		this.isOfficialCga = value;
	}

	/** 取得保證機構之主權國家 **/
	public String getCga_country() {
		return this.cga_country;
	}

	/** 設定保證機構之主權國家 **/
	public void setCga_country(String value) {
		this.cga_country = value;
	}

	/** 取得主權國家信評之機構 **/
	public String getCga_crdType() {
		return this.cga_crdType;
	}

	/** 設定主權國家信評之機構 **/
	public void setCga_crdType(String value) {
		this.cga_crdType = value;
	}

	/** 取得主權國家信評之地區別 **/
	public String getCga_crdArea() {
		return this.cga_crdArea;
	}

	/** 設定主權國家信評之地區別 **/
	public void setCga_crdArea(String value) {
		this.cga_crdArea = value;
	}

	/** 取得主權國家信評之期間別 **/
	public String getCga_crdPred() {
		return this.cga_crdPred;
	}

	/** 設定主權國家信評之期間別 **/
	public void setCga_crdPred(String value) {
		this.cga_crdPred = value;
	}

	/** 取得主權國家信評之等級 **/
	public String getCga_crdGrade() {
		return this.cga_crdGrade;
	}

	/** 設定主權國家信評之等級 **/
	public void setCga_crdGrade(String value) {
		this.cga_crdGrade = value;
	}

	/** 取得主權國家信評之風險權數 **/
	public BigDecimal getCga_rskRatio() {
		return this.cga_rskRatio;
	}

	/** 設定主權國家信評之風險權數 **/
	public void setCga_rskRatio(BigDecimal value) {
		this.cga_rskRatio = value;
	}

	/**
	 * 設定計入兆元振興額度
	 * 
	 * @param reviveAmt
	 */
	public void setReviveAmt(BigDecimal reviveAmt) {
		this.reviveAmt = reviveAmt;
	}

	/**
	 * 取得計入兆元振興額度
	 * 
	 * @param reviveAmt
	 */
	public BigDecimal getReviveAmt() {
		return reviveAmt;
	}

	public void setRescueIndustry(String rescueIndustry) {
		this.rescueIndustry = rescueIndustry;
	}

	public String getRescueIndustry() {
		return rescueIndustry;
	}

	public void setRescueCity(String rescueCity) {
		this.rescueCity = rescueCity;
	}

	public String getRescueCity() {
		return rescueCity;
	}

	public void setIsSpecialFinRisk(String isSpecialFinRisk) {
		this.isSpecialFinRisk = isSpecialFinRisk;
	}

	public String getIsSpecialFinRisk() {
		return isSpecialFinRisk;
	}

	public void setSpecialFinRiskType(String specialFinRiskType) {
		this.specialFinRiskType = specialFinRiskType;
	}

	public String getSpecialFinRiskType() {
		return specialFinRiskType;
	}

	public void setIsCmsAdcRisk(String isCmsAdcRisk) {
		this.isCmsAdcRisk = isCmsAdcRisk;
	}

	public String getIsCmsAdcRisk() {
		return isCmsAdcRisk;
	}

	/**
	 * 設定本案是否屬109年9至12月提升中小企業放款方案
	 * 
	 * @param isEnhanceSmeLoan
	 */
	public void setIsEnhanceSmeLoan(String isEnhanceSmeLoan) {
		this.isEnhanceSmeLoan = isEnhanceSmeLoan;
	}

	/**
	 * 取得本案是否屬109年9至12月提升中小企業放款方案
	 * 
	 * @return
	 */
	public String getIsEnhanceSmeLoan() {
		return isEnhanceSmeLoan;
	}

	/**
	 * 設定 青創貸款用途
	 * 
	 * @param yoPurpose
	 */
	public void setYoPurpose(String yoPurpose) {
		this.yoPurpose = yoPurpose;
	}

	/**
	 * 取得青創貸款用途
	 * 
	 * @return
	 */
	public String getYoPurpose() {
		return yoPurpose;
	}

	/**
	 * 設定 補貼單位
	 * 
	 * @param subSidyut
	 */
	public void setSubSidyut(String subSidyut) {
		this.subSidyut = subSidyut;
	}

	/**
	 * 取得補貼單位
	 * 
	 * @return
	 */
	public String getSubSidyut() {
		return subSidyut;
	}

	public String getIsSuspectedHeadAccount() {
		return this.isSuspectedHeadAccount;
	}

	public void setIsSuspectedHeadAccount(String isSuspectedHeadAccount) {
		this.isSuspectedHeadAccount = isSuspectedHeadAccount;
	}

	/**
	 * 設定ADC案件編號
	 * 
	 * @param adcCaseNo
	 */
	public void setAdcCaseNo(String adcCaseNo) {
		this.adcCaseNo = adcCaseNo;
	}

	/**
	 * 取得ADC案件編號
	 * 
	 * @return
	 */
	public String getAdcCaseNo() {
		return adcCaseNo;
	}

	public String getSuspectedPackLoanReason() {
		return this.suspectedPackLoanReason;
	}

	public void setSuspectedPackLoanReason(String SuspectedPackLoanReason) {
		this.suspectedPackLoanReason = SuspectedPackLoanReason;
	}

	public String getSuspectedPackLoanFlag() {
		return this.suspectedPackLoanFlag;
	}

	public void setSuspectedPackLoanFlag(String SuspectedPackLoanFlag) {
		this.suspectedPackLoanFlag = SuspectedPackLoanFlag;
	}

	public void setSuspectedPackLoanCntrNo(String suspectedPackLoanCntrNo) {
		this.suspectedPackLoanCntrNo = suspectedPackLoanCntrNo;
	}

	public String getSuspectedPackLoanCntrNo() {
		return this.suspectedPackLoanCntrNo;
	}

	public String getDealContractNo() {
		return dealContractNo;
	}

	public void setDealContractNo(String dealContractNo) {
		this.dealContractNo = dealContractNo;
	}

	public String getIntroCustId() {
		return introCustId;
	}

	public void setIntroCustId(String introCustId) {
		this.introCustId = introCustId;
	}

	public String getIntroDupNo() {
		return introDupNo;
	}

	public void setIntroDupNo(String introDupNo) {
		this.introDupNo = introDupNo;
	}

	public String getIntroCustName() {
		return introCustName;
	}

	public void setIntroCustName(String introCustName) {
		this.introCustName = introCustName;
	}

	public String getIntroBuilderName() {
		return introBuilderName;
	}

	public void setIntroBuilderName(String introBuilderName) {
		this.introBuilderName = introBuilderName;
	}

	public String getIntroBiuldCaseName() {
		return introBiuldCaseName;
	}

	public void setIntroBiuldCaseName(String introBiuldCaseName) {
		this.introBiuldCaseName = introBiuldCaseName;
	}

	public String getMarketingNotes() {
		return marketingNotes;
	}

	public void setMarketingNotes(String marketingNotes) {
		this.marketingNotes = marketingNotes;
	}

	/**
	 * 設定本案是否屬110年行銷名單來源客戶
	 * 
	 * @param isMarketingList110
	 */
	public void setIsMarketingList110(String isMarketingList110) {
		this.isMarketingList110 = isMarketingList110;
	}

	/**
	 * 取得本案是否屬110年行銷名單來源客戶
	 * 
	 * @return
	 */
	public String getIsMarketingList110() {
		return isMarketingList110;
	}

	/**
	 * 設定前次是否屬109年紓困
	 */
	public void setIsRescueOn(String isRescueOn) {
		this.isRescueOn = isRescueOn;
	}

	/**
	 * 取得前次是否屬109年紓困
	 */
	public String getIsRescueOn() {
		return isRescueOn;
	}

	/**
	 * 設定前次文件編號
	 */
	public void setOnMainId(String onMainId) {
		this.onMainId = onMainId;
	}

	/**
	 * 取得前次文件編號
	 */
	public String getOnMainId() {
		return onMainId;
	}

	/**
	 * 設定前次紓困貸款類別
	 */
	public void setRescueItemOn(String rescueItemOn) {
		this.rescueItemOn = rescueItemOn;
	}

	/**
	 * 取得前次紓困貸款類別
	 */
	public String getRescueItemOn() {
		return rescueItemOn;
	}

	/**
	 * 設定前次案件號碼
	 */
	public void setCaseNoOn(String caseNoOn) {
		this.caseNoOn = caseNoOn;
	}

	/**
	 * 取得前次案件號碼
	 */
	public String getCaseNoOn() {
		return caseNoOn;
	}

	/**
	 * 設定前次額度序號
	 */
	public void setCntrNoOn(String cntrNoOn) {
		this.cntrNoOn = cntrNoOn;
	}

	/**
	 * 取得前次額度序號
	 */
	public String getCntrNoOn() {
		return cntrNoOn;
	}

	/**
	 * 設定本行國家暴險是否以保證人國別為計算基準
	 * 
	 * @param guaNaExposure
	 */
	public void setGuaNaExposure(String guaNaExposure) {
		this.guaNaExposure = guaNaExposure;
	}

	/**
	 * 取得本行國家暴險是否以保證人國別為計算基準
	 * 
	 * @return
	 */
	public String getGuaNaExposure() {
		return guaNaExposure;
	}

	/**
	 * 設定國發基金加碼保證成數
	 * 
	 * @param guaNaExposure
	 */
	public void setRescueNdfGutPercent(BigDecimal rescueNdfGutPercent) {
		this.rescueNdfGutPercent = rescueNdfGutPercent;
	}

	/**
	 * 取得國發基金加碼保證成數
	 * 
	 * @return
	 */
	public BigDecimal getRescueNdfGutPercent() {
		return rescueNdfGutPercent;
	}

	public void setIsOperationFee(String isOperationFee) {
		this.isOperationFee = isOperationFee;
	}

	public String getIsOperationFee() {
		return isOperationFee;
	}

	public void setNoOperationFee(String noOperationFee) {
		this.noOperationFee = noOperationFee;
	}

	public String getNoOperationFee() {
		return noOperationFee;
	}

	/**
	 * 設定免收取授信作業手續費原因其他說明
	 * 
	 * @param guaNaExposure
	 */
	public void setNoOperationFeeOth(String noOperationFeeOth) {
		this.noOperationFeeOth = noOperationFeeOth;
	}

	/**
	 * 取得免收取授信作業手續費原因其他說明
	 * 
	 * @return
	 */
	public String getNoOperationFeeOth() {
		return noOperationFeeOth;
	}

	/**
	 * 設定專案融資是否屬營運階段
	 * 
	 * @param isProjectFinOperateStag
	 */
	public void setIsProjectFinOperateStag(String isProjectFinOperateStag) {
		this.isProjectFinOperateStag = isProjectFinOperateStag;
	}

	/**
	 * 取得專案融資是否屬營運階段
	 * 
	 * @return
	 */
	public String getIsProjectFinOperateStag() {
		return isProjectFinOperateStag;
	}

	/**
	 * 設定應收利息幣別
	 */
	public void setRcvCurr(String rcvCurr) {
		this.rcvCurr = rcvCurr;
	}

	/**
	 * 取得應收利息幣別
	 * 
	 * @return
	 */
	public String getRcvCurr() {
		return rcvCurr;
	}

	/**
	 * 設定應收利息金額
	 */
	public void setRcvInt(BigDecimal rcvInt) {
		this.rcvInt = rcvInt;
	}

	/**
	 * 取得應收利息金額
	 * 
	 * @return
	 */
	public BigDecimal getRcvInt() {
		return rcvInt;
	}

	/**
	 * 設定帳款管理商保證成數
	 */
	public void setArAccPercent(BigDecimal arAccPercent) {
		this.arAccPercent = arAccPercent;
	}

	/**
	 * 取得帳款管理商保證成數
	 * 
	 * @return
	 */
	public BigDecimal getArAccPercent() {
		return arAccPercent;
	}

	/**
	 * 設定聯貸幣別
	 */
	public void setSyndLoanCurr(String syndLoanCurr) {
		this.syndLoanCurr = syndLoanCurr;
	}

	/**
	 * 取得聯貸幣別
	 * 
	 * @return
	 */
	public String getSyndLoanCurr() {
		return syndLoanCurr;
	}

	/**
	 * 設定聯貸總金額
	 */
	public void setSyndLoanTotal(BigDecimal syndLoanTotal) {
		this.syndLoanTotal = syndLoanTotal;
	}

	/**
	 * 取得聯貸總金額
	 * 
	 * @return
	 */
	public BigDecimal getSyndLoanTotal() {
		return syndLoanTotal;
	}

	/**
	 * 設定參貸金額
	 */
	public void setSyndLoanPart(BigDecimal syndLoanPart) {
		this.syndLoanPart = syndLoanPart;
	}

	/**
	 * 取得參貸金額
	 * 
	 * @return
	 */
	public BigDecimal getSyndLoanPart() {
		return syndLoanPart;
	}

	public void setIsEsgGreenLoan(String isEsgGreenLoan) {
		this.isEsgGreenLoan = isEsgGreenLoan;
	}

	public String getIsEsgGreenLoan() {
		return isEsgGreenLoan;
	}

	public void setEsgGreenSpendType(String esgGreenSpendType) {
		this.esgGreenSpendType = esgGreenSpendType;
	}

	public String getEsgGreenSpendType() {
		return esgGreenSpendType;
	}

	public void setEsgGreenSpendTypeZMemo(String esgGreenSpendTypeZMemo) {
		this.esgGreenSpendTypeZMemo = esgGreenSpendTypeZMemo;
	}

	public String getEsgGreenSpendTypeZMemo() {
		return esgGreenSpendTypeZMemo;
	}

	public void setEsgSustainLoan(String esgSustainLoan) {
		this.esgSustainLoan = esgSustainLoan;
	}

	public String getEsgSustainLoan() {
		return esgSustainLoan;
	}

	public void setEsgSustainLoanType(String esgSustainLoanType) {
		this.esgSustainLoanType = esgSustainLoanType;
	}

	public String getEsgSustainLoanType() {
		return esgSustainLoanType;
	}

	public void setEsgSustainLoanUnReach(String esgSustainLoanUnReach) {
		this.esgSustainLoanUnReach = esgSustainLoanUnReach;
	}

	public String getEsgSustainLoanUnReach() {
		return esgSustainLoanUnReach;
	}

	public void setEsgSustainLoanUnReachMemo(String esgSustainLoanUnReachMemo) {
		this.esgSustainLoanUnReachMemo = esgSustainLoanUnReachMemo;
	}

	public String getEsgSustainLoanUnReachMemo() {
		return esgSustainLoanUnReachMemo;
	}

	public void setEsgGreenSpendType1(BigDecimal esgGreenSpendType1) {
		this.esgGreenSpendType1 = esgGreenSpendType1;
	}

	public BigDecimal getEsgGreenSpendType1() {
		return esgGreenSpendType1;
	}

	public void setEsgGreenSpendType2(BigDecimal esgGreenSpendType2) {
		this.esgGreenSpendType2 = esgGreenSpendType2;
	}

	public BigDecimal getEsgGreenSpendType2() {
		return esgGreenSpendType2;
	}

	public void setEsgGreenSpendType3(BigDecimal esgGreenSpendType3) {
		this.esgGreenSpendType3 = esgGreenSpendType3;
	}

	public BigDecimal getEsgGreenSpendType3() {
		return esgGreenSpendType3;
	}

	/**
	 * 設定屬本行授信業務授權準則得單獨劃分之業務
	 * 
	 * @param isStandAloneAuth
	 */
	public void setIsStandAloneAuth(String isStandAloneAuth) {
		this.isStandAloneAuth = isStandAloneAuth;
	}

	/**
	 * 取得屬本行授信業務授權準則得單獨劃分之業務
	 * 
	 * @return
	 */
	public String getIsStandAloneAuth() {
		return isStandAloneAuth;
	}

	/**
	 * 設定是否列於六大核心戰略產業
	 */
	public void setIsCoreBuss(String isCoreBuss) {
		this.isCoreBuss = isCoreBuss;
	}

	/**
	 * 取得是否列於六大核心戰略產業
	 */
	public String getIsCoreBuss() {
		return isCoreBuss;
	}

	/**
	 * 設定產品種類六大核心戰略產業細目
	 */
	public void setItwCodeCoreBuss(String itwCodeCoreBuss) {
		this.itwCodeCoreBuss = itwCodeCoreBuss;
	}

	/**
	 * 取得產品種類六大核心戰略產業細目
	 */
	public String getItwCodeCoreBuss() {
		return itwCodeCoreBuss;
	}

	/**
	 * 設定借款人行業別所屬六大核心戰略產業細目
	 */
	public void setBelongItwCodeCoreBuss(String belongItwCodeCoreBuss) {
		this.belongItwCodeCoreBuss = belongItwCodeCoreBuss;
	}

	/**
	 * 取得借款人行業別所屬六大核心戰略產業細目
	 */
	public String getBelongItwCodeCoreBuss() {
		return belongItwCodeCoreBuss;
	}

	/**
	 * 設定送保方式
	 */
	public void setGutType(String gutType) {
		this.gutType = gutType;
	}

	/**
	 * 取得送保方式
	 */
	public String getGutType() {
		return gutType;
	}

	/**
	 * 設定LGD其中無擔保合計
	 */
	public void setLgdTotAmt_U(BigDecimal lgdTotAmt_U) {
		this.lgdTotAmt_U = lgdTotAmt_U;
	}

	/**
	 * 取得LGD其中無擔保合計
	 */
	public BigDecimal getLgdTotAmt_U() {
		return lgdTotAmt_U;
	}

	/**
	 * 設定LGD其中擬制部分擔保合計
	 */
	public void setLgdTotAmt_P(BigDecimal lgdTotAmt_P) {
		this.lgdTotAmt_P = lgdTotAmt_P;
	}

	/**
	 * 取得LGD其中擬制部分擔保合計
	 */
	public BigDecimal getLgdTotAmt_P() {
		return lgdTotAmt_P;
	}

	/**
	 * 設定LGD其中擬制十足擔保合計
	 */
	public void setLgdTotAmt_S(BigDecimal lgdTotAmt_S) {
		this.lgdTotAmt_S = lgdTotAmt_S;
	}

	/**
	 * 取得LGD其中擬制十足擔保合計
	 */
	public BigDecimal getLgdTotAmt_S() {
		return lgdTotAmt_S;
	}

	/**
	 * 設定LGD合計金額調整註記
	 */
	public void setValueTuneLgd(String valueTuneLgd) {
		this.valueTuneLgd = valueTuneLgd;
	}

	/**
	 * 取得LGD合計金額調整註記
	 */
	public String getValueTuneLgd() {
		return valueTuneLgd;
	}

	/**
	 * 設定LGD合計版本
	 */
	public void setLgdTotAmt_Ver(String lgdTotAmt_Ver) {
		this.lgdTotAmt_Ver = lgdTotAmt_Ver;
	}

	/**
	 * 取得LGD合計版本
	 */
	public String getLgdTotAmt_Ver() {
		return lgdTotAmt_Ver;
	}

	/**
	 * 設定聯貸案特殊擔保品LGD條件
	 */
	public void setSyndIsCmsSpecial_1(String syndIsCmsSpecial_1) {
		this.syndIsCmsSpecial_1 = syndIsCmsSpecial_1;
	}

	/**
	 * 取得聯貸案特殊擔保品LGD條件
	 */
	public String getSyndIsCmsSpecial_1() {
		return syndIsCmsSpecial_1;
	}

	public String getMegaEmpBrNo() {
		return megaEmpBrNo;
	}

	public void setMegaEmpBrNo(String megaEmpBrNo) {
		this.megaEmpBrNo = megaEmpBrNo;
	}

	public String getIntroducerName() {
		return introducerName;
	}

	public void setIntroducerName(String introducerName) {
		this.introducerName = introducerName;
	}

	public void setLgdTotAmt_1(BigDecimal lgdTotAmt_1) {
		this.lgdTotAmt_1 = lgdTotAmt_1;
	}

	public BigDecimal getLgdTotAmt_1() {
		return lgdTotAmt_1;
	}

	public void setLgdTotAmt_2(BigDecimal lgdTotAmt_2) {
		this.lgdTotAmt_2 = lgdTotAmt_2;
	}

	public BigDecimal getLgdTotAmt_2() {
		return lgdTotAmt_2;
	}

	public void setLgdTotAmt_3(BigDecimal lgdTotAmt_3) {
		this.lgdTotAmt_3 = lgdTotAmt_3;
	}

	public BigDecimal getLgdTotAmt_3() {
		return lgdTotAmt_3;
	}

	public void setLgdTotAmt_4(BigDecimal lgdTotAmt_4) {
		this.lgdTotAmt_4 = lgdTotAmt_4;
	}

	public BigDecimal getLgdTotAmt_4() {
		return lgdTotAmt_4;
	}

	public void setLgdTotAmt_5(BigDecimal lgdTotAmt_5) {
		this.lgdTotAmt_5 = lgdTotAmt_5;
	}

	public BigDecimal getLgdTotAmt_5() {
		return lgdTotAmt_5;
	}

	/** 取得額度預期LGD **/
	public BigDecimal getExpectLgd() {
		return this.expectLgd;
	}

	/** 設定額度預期LGD **/
	public void setExpectLgd(BigDecimal value) {
		this.expectLgd = value;
	}

	/** 取得檢附佐證資料 **/
	public String getAttachDoc() {
		return attachDoc;
	}

	/** 設定檢附佐證資料 **/
	public void setAttachDoc(String attachDoc) {
		this.attachDoc = attachDoc;
	}

	/** 取得檢附佐證資料第二層 **/
	public String getAttachDoc2() {
		return attachDoc2;
	}

	/** 設定檢附佐證資料第二層 **/
	public void setAttachDoc2(String attachDoc2) {
		this.attachDoc2 = attachDoc2;
	}

	/** 取得檢附佐證資料第三層 **/
	public String getAttachDoc3() {
		return attachDoc3;
	}

	/** 設定檢附佐證資料第三層 **/
	public void setAttachDoc3(String attachDoc3) {
		this.attachDoc3 = attachDoc3;
	}

	/** 取得檢附佐證資料其他說明 **/
	public String getAttachDocMemo() {
		return attachDocMemo;
	}

	/** 設定檢附佐證資料其他說明 **/
	public void setAttachDocMemo(String attachDocMemo) {
		this.attachDocMemo = attachDocMemo;
	}

	/** 設定本案是否屬中小企業千億振興融資方案 **/
	public void setIsRevital(String isRevital) {
		this.isRevital = isRevital;
	}

	/** 取得本案是否屬中小企業千億振興融資方案 **/
	public String getIsRevital() {
		return isRevital;
	}

	/** 設定中小企業千億振興融資方案_貸款對象類別 **/
	public void setRevitalTarget(String revitalTarget) {
		this.revitalTarget = revitalTarget;
	}

	/** 取得中小企業千億振興融資方案_貸款對象類別 **/
	public String getRevitalTarget() {
		return revitalTarget;
	}

	/** 設定中小企業千億振興融資方案_貸款用途 **/
	public void setRevitalLoanPurpose(String revitalLoanPurpose) {
		this.revitalLoanPurpose = revitalLoanPurpose;
	}

	/** 取得中小企業千億振興融資方案_貸款用途 **/
	public String getRevitalLoanPurpose() {
		return revitalLoanPurpose;
	}

	/** 設定計入中小企業千億振興額度 **/
	public void setRevitalAmt(BigDecimal revitalAmt) {
		this.revitalAmt = revitalAmt;
	}

	/** 取得計入中小企業千億振興額度 **/
	public BigDecimal getRevitalAmt() {
		return revitalAmt;
	}

	public String getIsHitSameBorrowerInfo() {
		return isHitSameBorrowerInfo;
	}

	public void setIsHitSameBorrowerInfo(String isHitSameBorrowerInfo) {
		this.isHitSameBorrowerInfo = isHitSameBorrowerInfo;
	}

	public BigDecimal getApprovedPercent() {
		return approvedPercent;
	}

	public void setApprovedPercent(BigDecimal approvedPercent) {
		this.approvedPercent = approvedPercent;
	}

	/** 設定約定融資額度註記問答項目選擇[是]的項目 **/
	public String getExceptFlagQAisY() {
		return exceptFlagQAisY;
	}

	/** 取得約定融資額度註記問答項目選擇[是]的項目 **/
	public void setExceptFlagQAisY(String exceptFlagQAisY) {
		this.exceptFlagQAisY = exceptFlagQAisY;
	}

	public String getLicenseYear() {
		return licenseYear;
	}

	public void setLicenseYear(String licenseYear) {
		this.licenseYear = licenseYear;
	}

	public String getLicenseWord() {
		return licenseWord;
	}

	public void setLicenseWord(String licenseWord) {
		this.licenseWord = licenseWord;
	}

	public String getLicenseNumber() {
		return licenseNumber;
	}

	public void setLicenseNumber(String licenseNumber) {
		this.licenseNumber = licenseNumber;
	}

	public String getBatGutAmtApprCaseNo() {
		return batGutAmtApprCaseNo;
	}

	public void setBatGutAmtApprCaseNo(String batGutAmtApprCaseNo) {
		this.batGutAmtApprCaseNo = batGutAmtApprCaseNo;
	}

	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}

	public String getExpectLgdGroup() {
		return expectLgdGroup;
	}

	public void setExpectLgdGroup(String expectLgdGroup) {
		this.expectLgdGroup = expectLgdGroup;
	}

	/**
	 * 設定涉及ESG風險授信案件之審查註記
	 * 
	 * @param checkBtEsg
	 */
	public void setCheckBtEsg(String checkBtEsg) {
		this.checkBtEsg = checkBtEsg;
	}

	/**
	 * 取得涉及ESG風險授信案件之審查註記
	 * 
	 * @return
	 */
	public String getCheckBtEsg() {
		return checkBtEsg;
	}

	/** 高品質專案融資_選項1 **/
	public void setIsHighQualityProjOpt_1(String isHighQualityProjOpt_1) {
		this.isHighQualityProjOpt_1 = isHighQualityProjOpt_1;
	}

	public String getIsHighQualityProjOpt_1() {
		return isHighQualityProjOpt_1;
	}

	/** 高品質專案融資_選項2 **/
	public void setIsHighQualityProjOpt_2(String isHighQualityProjOpt_2) {
		this.isHighQualityProjOpt_2 = isHighQualityProjOpt_2;
	}

	public String getIsHighQualityProjOpt_2() {
		return isHighQualityProjOpt_2;
	}

	/** 高品質專案融資_選項3 **/
	public void setIsHighQualityProjOpt_3(String isHighQualityProjOpt_3) {
		this.isHighQualityProjOpt_3 = isHighQualityProjOpt_3;
	}

	public String getIsHighQualityProjOpt_3() {
		return isHighQualityProjOpt_3;
	}

	/** 高品質專案融資_選項4 **/
	public void setIsHighQualityProjOpt_4(String isHighQualityProjOpt_4) {
		this.isHighQualityProjOpt_4 = isHighQualityProjOpt_4;
	}

	public String getIsHighQualityProjOpt_4() {
		return isHighQualityProjOpt_4;
	}

	/** 高品質專案融資_選項5 **/
	public void setIsHighQualityProjOpt_5(String isHighQualityProjOpt_5) {
		this.isHighQualityProjOpt_5 = isHighQualityProjOpt_5;
	}

	public String getIsHighQualityProjOpt_5() {
		return isHighQualityProjOpt_5;
	}

	/** 高品質專案融資_最終結果 **/
	public void setIsHighQualityProjResult(String isHighQualityProjResult) {
		this.isHighQualityProjResult = isHighQualityProjResult;
	}

	public String getIsHighQualityProjResult() {
		return isHighQualityProjResult;
	}

	public String getIsInformMbrchUsedAmt() {
		return isInformMbrchUsedAmt;
	}

	public void setIsInformMbrchUsedAmt(String isInformMbrchUsedAmt) {
		this.isInformMbrchUsedAmt = isInformMbrchUsedAmt;
	}

	/**
	 * 設定瑕疵押匯額度合計
	 * 
	 * @param flawAmtTotal
	 */
	public void setFlawAmtTotal(BigDecimal flawAmtTotal) {
		this.flawAmtTotal = flawAmtTotal;
	}

	/**
	 * 取得瑕疵押匯額度合計
	 * 
	 * @return
	 */
	public BigDecimal getFlawAmtTotal() {
		return flawAmtTotal;
	}

	/**
	 * 設定總授信額度(授信額度+出口瑕疵額度)
	 * 
	 * @param generalLoanTotAmt
	 */
	public void setGeneralLoanTotAmt(BigDecimal generalLoanTotAmt) {
		this.generalLoanTotAmt = generalLoanTotAmt;
	}

	/**
	 * 取得總授信額度(授信額度+出口瑕疵額度)
	 * 
	 * @return
	 */
	public BigDecimal getGeneralLoanTotAmt() {
		return generalLoanTotAmt;
	}

	/** 取得約定融資額度註記資本計提延伸問答(僅有條件及無條件可取消) **/
	public String getExceptFlagQAPlus() {
		return exceptFlagQAPlus;
	}

	/** 設定約定融資額度註記資本計提延伸問答(僅有條件及無條件可取消) **/
	public void setExceptFlagQAPlus(String exceptFlagQAPlus) {
		this.exceptFlagQAPlus = exceptFlagQAPlus;
	}

	/**
	 * 設定單獨另計授權額度合計
	 * 
	 * @param generalLoanTotAmt
	 */
	public void setStandAloneAuthTotal(BigDecimal standAloneAuthTotal) {
		this.standAloneAuthTotal = standAloneAuthTotal;
	}

	/**
	 * 取得單獨另計授權額度合計
	 * 
	 * @return
	 */
	public BigDecimal getStandAloneAuthTotal() {
		return standAloneAuthTotal;
	}

	/**
	 * 設定 LGD1合計-全案
	 * 
	 */
	public void setLgdTotMgAmt_1(BigDecimal lgdTotMgAmt_1) {
		this.lgdTotMgAmt_1 = lgdTotMgAmt_1;
	}

	/**
	 * 取得LGD1合計-全案
	 * 
	 * @return
	 */
	public BigDecimal getLgdTotMgAmt_1() {
		return lgdTotMgAmt_1;
	}

	/**
	 * 設定 LGD2合計-全案
	 * 
	 */
	public void setLgdTotMgAmt_2(BigDecimal lgdTotMgAmt_2) {
		this.lgdTotMgAmt_2 = lgdTotMgAmt_2;
	}

	/**
	 * 取得LGD2合計-全案
	 * 
	 * @return
	 */
	public BigDecimal getLgdTotMgAmt_2() {
		return lgdTotMgAmt_2;
	}

	/**
	 * 設定 LGD3合計-全案
	 * 
	 */
	public void setLgdTotMgAmt_3(BigDecimal lgdTotMgAmt_3) {
		this.lgdTotMgAmt_3 = lgdTotMgAmt_3;
	}

	/**
	 * 取得LGD3合計-全案
	 * 
	 * @return
	 */
	public BigDecimal getLgdTotMgAmt_3() {
		return lgdTotMgAmt_3;
	}

	/**
	 * 設定 LGD4合計-全案
	 * 
	 */
	public void setLgdTotMgAmt_4(BigDecimal lgdTotMgAmt_4) {
		this.lgdTotMgAmt_4 = lgdTotMgAmt_4;
	}

	/**
	 * 取得LGD4合計-全案
	 * 
	 * @return
	 */
	public BigDecimal getLgdTotMgAmt_4() {
		return lgdTotMgAmt_4;
	}

	/**
	 * 設定 LGD5合計-全案
	 * 
	 */
	public void setLgdTotMgAmt_5(BigDecimal lgdTotMgAmt_5) {
		this.lgdTotMgAmt_5 = lgdTotMgAmt_5;
	}

	/**
	 * 取得LGD5合計-全案
	 * 
	 * @return
	 */
	public BigDecimal getLgdTotMgAmt_5() {
		return lgdTotMgAmt_5;
	}

	/**
	 * 設定 瑕疵押匯額度合計-全案
	 * 
	 */
	public void setFlawAmtMgTotal(BigDecimal flawAmtMgTotal) {
		this.flawAmtMgTotal = flawAmtMgTotal;
	}

	/**
	 * 取得瑕疵押匯額度合計-全案
	 * 
	 * @return
	 */
	public BigDecimal getFlawAmtMgTotal() {
		return flawAmtMgTotal;
	}

	/**
	 * 設定 總授信授權額度合計-全案
	 * 
	 */
	public void setGeneralLoanMgTotAmt(BigDecimal generalLoanMgTotAmt) {
		this.generalLoanMgTotAmt = generalLoanMgTotAmt;
	}

	/**
	 * 取得總授信授權額度合計-全案
	 * 
	 * @return
	 */
	public BigDecimal getGeneralLoanMgTotAmt() {
		return generalLoanMgTotAmt;
	}

	public void setLgdTotMgCurr(String lgdTotMgCurr) {
		this.lgdTotMgCurr = lgdTotMgCurr;
	}

	public String getLgdTotMgCurr() {
		return lgdTotMgCurr;
	}

	public String getExpectModelKind() {
		return expectModelKind;
	}

	public void setExpectModelKind(String expectModelKind) {
		this.expectModelKind = expectModelKind;
	}

	/**
	 * 設定 是否徵提保證金(遠匯、換匯交易)
	 * 
	 */
	public void setMarginFlag(String marginFlag) {
		this.marginFlag = marginFlag;
	}

	/**
	 * 取得是否徵提保證金(遠匯、換匯交易)
	 * 
	 * @return
	 */

	public String getMarginFlag() {
		return marginFlag;
	}

	/**
	 * 設定 LGD1合計-合併關係
	 * 
	 */
	public void setLgdTotRcAmt_1(BigDecimal lgdTotRcAmt_1) {
		this.lgdTotRcAmt_1 = lgdTotRcAmt_1;
	}

	/**
	 * 取得LGD1合計-合併關係
	 * 
	 * @return
	 */
	public BigDecimal getLgdTotRcAmt_1() {
		return lgdTotRcAmt_1;
	}

	/**
	 * 設定 LGD2合計-合併關係
	 * 
	 */
	public void setLgdTotRcAmt_2(BigDecimal lgdTotRcAmt_2) {
		this.lgdTotRcAmt_2 = lgdTotRcAmt_2;
	}

	/**
	 * 取得LGD2合計-合併關係
	 * 
	 * @return
	 */
	public BigDecimal getLgdTotRcAmt_2() {
		return lgdTotRcAmt_2;
	}

	/**
	 * 設定 LGD3合計-合併關係
	 * 
	 */
	public void setLgdTotRcAmt_3(BigDecimal lgdTotRcAmt_3) {
		this.lgdTotRcAmt_3 = lgdTotRcAmt_3;
	}

	/**
	 * 取得LGD3合計-合併關係
	 * 
	 * @return
	 */
	public BigDecimal getLgdTotRcAmt_3() {
		return lgdTotRcAmt_3;
	}

	/**
	 * 設定 LGD4合計-合併關係
	 * 
	 */
	public void setLgdTotRcAmt_4(BigDecimal lgdTotRcAmt_4) {
		this.lgdTotRcAmt_4 = lgdTotRcAmt_4;
	}

	/**
	 * 取得LGD4合計-合併關係
	 * 
	 * @return
	 */
	public BigDecimal getLgdTotRcAmt_4() {
		return lgdTotRcAmt_4;
	}

	/**
	 * 設定 LGD5合計-合併關係
	 * 
	 */
	public void setLgdTotRcAmt_5(BigDecimal lgdTotRcAmt_5) {
		this.lgdTotRcAmt_5 = lgdTotRcAmt_5;
	}

	/**
	 * 取得LGD5合計-合併關係
	 * 
	 * @return
	 */
	public BigDecimal getLgdTotRcAmt_5() {
		return lgdTotRcAmt_5;
	}

	/**
	 * 設定 瑕疵押匯額度合計-合併關係
	 * 
	 */
	public void setFlawAmtRcTotal(BigDecimal flawAmtRcTotal) {
		this.flawAmtRcTotal = flawAmtRcTotal;
	}

	/**
	 * 取得瑕疵押匯額度合計-合併關係
	 * 
	 * @return
	 */
	public BigDecimal getFlawAmtRcTotal() {
		return flawAmtRcTotal;
	}

	/**
	 * 設定 總授信授權額度合計-合併關係
	 * 
	 */
	public void setGeneralLoanRcTotAmt(BigDecimal generalLoanRcTotAmt) {
		this.generalLoanRcTotAmt = generalLoanRcTotAmt;
	}

	/**
	 * 取得總授信授權額度合計-合併關係
	 * 
	 * @return
	 */
	public BigDecimal getGeneralLoanRcTotAmt() {
		return generalLoanRcTotAmt;
	}

    /**
     * 設定屬性評估
     */
    public void setDerivEval(String derivEval) {
        this.derivEval = derivEval;
    }

    /**
     * 取得屬性評估
     */
    public String getDerivEval() {
        return derivEval;
    }

	/** 取得評估日期 **/
	public Date getEvalDate() {
		return this.evalDate;
	}

	/** 設定評估日期 **/
	public void setEvalDate(Date value) {
		this.evalDate = value;
	}

	/**
	 * 取得 該額度所搭配專案(Y1/Y2/Y3)額度序號
	 * 
	 */
	public String getProjRefCntrNo() {
		return projRefCntrNo;
	}

	/**
	 * 設定 該額度所搭配專案(Y1/Y2/Y3)額度序號
	 * 
	 */
	public void setProjRefCntrNo(String projRefCntrNo) {
		this.projRefCntrNo = projRefCntrNo;
	}

	public String getIsCheckHouseRatio() {
		return isCheckHouseRatio;
	}

	public void setIsCheckHouseRatio(String isCheckHouseRatio) {
		this.isCheckHouseRatio = isCheckHouseRatio;
	}

	/**
	 * 設定手續費收取方式
	 */
	public void setOperationFeeWay(String operationFeeWay) {
		this.operationFeeWay = operationFeeWay;
	}

	/**
	 * 取得手續費收取方式
	 */
	public String getOperationFeeWay() {
		return operationFeeWay;
	}

	/**
	 * 設定授信作業手續費金額
	 */
	public void setOperationFeeAmt(BigDecimal operationFeeAmt) {
		this.operationFeeAmt = operationFeeAmt;
	}

	/**
	 * 取得授信作業手續費金額
	 */
	public BigDecimal getOperationFeeAmt() {
		return operationFeeAmt;
	}
	
	/**
	 * 取得申請案件號碼
	 */
	public void setAppNo(String appNo) {
		this.appNo = appNo;
	}

	/**
	 * 設定申請案件號碼
	 */
	public String getAppNo() {
		return appNo;
	}

	/**
	 * 設定授信用途科目別
	 */
	public void setLoanAndContType(String loanAndContType) {
		this.loanAndContType = loanAndContType;
	}
	
	/**
	 * 取得授信用途科目別
	 */
	public String getLoanAndContType() {
		return loanAndContType;
	}

	/**
	 * 取得是否屬於0403花蓮地震震災貸款
	 */
	public String getHualien0403() {
		return hualien0403;
	}

	/**
	 * 設定是否屬於0403花蓮地震震災貸款
	 */
	public void setHualien0403(String hualien0403) {
		this.hualien0403 = hualien0403;
	}

	/**
	 * 取得房貸信保註記
	 */
	public String getIsHouseLoanGu() {
		return isHouseLoanGu;
	}

	/**
	 * 設定房貸信保註記
	 */
	public void setIsHouseLoanGu(String isHouseLoanGu) {
		this.isHouseLoanGu = isHouseLoanGu;
	}

	/**
	 * 取得花蓮地震震災房貸信保成數
	 */
	public BigDecimal getHualienGutPer() {
		return hualienGutPer;
	}

	/**
	 * 設定花蓮地震震災房貸信保成數
	 */
	public void setHualienGutPer(BigDecimal hualienGutPer) {
		this.hualienGutPer = hualienGutPer;
	}

	public String getSocialLoanFlag() {
		return socialLoanFlag;
	}

	public void setSocialLoanFlag(String socialLoanFlag) {
		this.socialLoanFlag = socialLoanFlag;
	}

	public String getSocialKind() {
		return socialKind;
	}

	public void setSocialKind(String socialKind) {
		this.socialKind = socialKind;
	}

	public String getSocialTa() {
		return socialTa;
	}

	public void setSocialTa(String socialTa) {
		this.socialTa = socialTa;
	}

	public String getSocialResp() {
		return socialResp;
	}

	public void setSocialResp(String socialResp) {
		this.socialResp = socialResp;
	}

	public Integer getRescueItemLKind() {
		return rescueItemLKind;
	}

	public void setRescueItemLKind(Integer rescueItemLKind) {
		this.rescueItemLKind = rescueItemLKind;
	}
}
