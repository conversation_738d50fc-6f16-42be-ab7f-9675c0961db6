/* 
 * ELF602.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.bean;

import tw.com.iisi.cap.model.GenericBean;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.wicket.markup.html.form.Check;

import java.math.BigDecimal;
import java.util.Date;

/** 貸後管理紀錄檔 **/
//
public class ELF602 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * 序號 (UNID)
	 */
	@Size(max = 60)
	@Column(name = "ELF602_UNID", length = 60, columnDefinition = "CHAR(60)")
	private String elf602_unid;

	/**
	 * 追蹤事項通知日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF602_FO_DATE", columnDefinition = "DATE")
	private Date elf602_fo_date;

	/**
	 * 額度序號
	 */
	@Size(max = 12)
	@Column(name = "ELF602_CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String elf602_cntrno;

	/**
	 * 放款帳號
	 */
	@Size(max = 20)
	@Column(name = "ELF602_LOAN_NO", length = 20, columnDefinition = "CHAR(20)")
	private String elf602_loan_no;

	/**
	 * 業務別 <br/>
	 * FA: 應收帳款 <br/>
	 * FI: 供應鏈融資 <br/>
	 * LN: eLoan
	 */
	@Size(max = 2)
	@Column(name = "ELF602_LOAN_KIND", length = 2, columnDefinition = "CHAR(2)")
	private String elf602_loan_kind;

	/**
	 * 類別 <br/>
	 * 複選 以｜分隔
	 */
	@Size(max = 20)
	@Column(name = "ELF602_FO_KIND", length = 20, columnDefinition = "VARCHAR(20)")
	private String elf602_fo_kind;

	/**
	 * 追蹤事項通知內容
	 */
	@Size(max = 800)
	@Column(name = "ELF602_FO_CONTENT", length = 800, columnDefinition = "VARCHAR(800)")
	private String elf602_fo_content;

	/**
	 * 應辦理追蹤對象 <br/>
	 * 01-帳務人員 <br/>
	 * 02-AO人員
	 */
	@Size(max = 2)
	@Column(name = "ELF602_STAFF", length = 2, columnDefinition = "CHAR(2)")
	private String elf602_staff;

	/**
	 * 檢核日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF602_CHKDATE", columnDefinition = "DATE")
	private Date elf602_chkdate;

	/**
	 * 符合註記
	 */
	@Size(max = 1)
	@Column(name = "ELF602_CONFORM_FG", length = 1, columnDefinition = "CHAR(1)")
	private String elf602_conform_fg;

	/**
	 * 追蹤說明
	 */
	@Size(max = 506)
	@Column(name = "ELF602_FO_MEMO", length = 506, columnDefinition = "VARCHAR(506)")
	private String elf602_fo_memo;

	/**
	 * 辦理狀況 <br/>
	 * 1-未辦理 <br/>
	 * 2-辦理中 <br/>
	 * 3-已完成 <br/>
	 * 4-已刪除
	 */
	@Size(max = 1)
	@Column(name = "ELF602_STATUS", length = 1, columnDefinition = "CHAR(1)")
	private String elf602_status;

	/**
	 * 資料寫入來源
	 */
	@Size(max = 60)
	@Column(name = "ELF602_DATASRC", length = 60, columnDefinition = "VARCHAR(60)")
	private String elf602_datasrc;

	/**
	 * 還款來源異常註記
	 */
	@Size(max = 1)
	@Column(name = "ELF602_UNUSUAL_FG", length = 1, columnDefinition = "CHAR(1)")
	private String elf602_unusual_fg;

	/**
	 * 理由敘述
	 */
	@Size(max = 800)
	@Column(name = "ELF602_UNUSUALDESC", length = 800, columnDefinition = "VARCHAR(800)")
	private String elf602_unusualdesc;

	/**
	 * 是否承做 <br/>
	 * Y 承做 <br/>
	 * N 婉卻
	 */
	@Size(max = 1)
	@Column(name = "ELF602_ISNOTIONAL", length = 1, columnDefinition = "CHAR(1)")
	private String elf602_isnotional;

	/**
	 * 是否申報疑似洗錢 <br/>
	 * Y 申報 <br/>
	 * N 不申報
	 */
	@Size(max = 1)
	@Column(name = "ELF602_ISAML", length = 1, columnDefinition = "CHAR(1)")
	private String elf602_isaml;

	/**
	 * 最近維護日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF602_UPD_DATE", columnDefinition = "DATE")
	private Date elf602_upd_date;

	/**
	 * 最近維護經辦
	 */
	@Size(max = 6)
	@Column(name = "ELF602_UPD_TELLER", length = 6, columnDefinition = "CHAR(6)")
	private String elf602_upd_teller;

	/**
	 * 最近維護主管
	 */
	@Size(max = 6)
	@Column(name = "ELF602_UPD_SUPVNO", length = 6, columnDefinition = "CHAR(6)")
	private String elf602_upd_supvno;

    /**
     * 追蹤事項通知內容 -  全型字
     */
    @Size(max = 402)
    @Column(name = "ELF602_FULL_CONTENT", length = 402, columnDefinition = "VARCHAR(402)")
    private String elf602_full_content;

    /**
     * 附加文件mainId
     */
    @Size(max = 32)
    @Column(name = "ELF602_FIELDMAINID", length = 32, columnDefinition = "VARCHAR(32)")
    private String elf602_fieldMainId;

    /** 證明文件說明 **/
    @Size(max=800)
    @Column(name = "ELF602_FILEDESC", length = 800, columnDefinition = "VARCHAR(800)")
    private String elf602_fileDesc;

	/** 客戶統編 **/
	@Size(max = 10)
	@Column(name="ELf602_CUSTID", length=10, columnDefinition="CHAR(10)")
	private String elf602_custid;

	/** 重複序號 **/
	@Size(max = 1)
	@Column(name="ELf602_DUPNO", length=1, columnDefinition="CHAR(1)")
	private String elf602_dupno;

	/** 最後維護分行 **/
	@Size(max = 3)
	@Column(name="ELF602_BR_NO", length=3, columnDefinition="CHAR(3)")
	private String elf602_br_no;

    /** 案件註記 **/
    @Size(max = 2)
    @Column(name="ELF602_CASE_MARK", length=2, columnDefinition="VARCHAR(2)")
    private String elf602_case_mark;

    /** 分項UID (UNID) **/
    @Size(max = 32)
    @Column(name="ELF602_SUID", length=32, columnDefinition="CHAR(32)")
    private String elf602_suid;
    
    /** 分項簽報書核准時間 **/
    @Column(name="ELF602_SAPPTIME", columnDefinition = "TIMESTAMP")
    private Date elf602_sapptime;
    
    /** 分項序號 **/
    @Digits(integer = 3, fraction = 0, groups = Check.class)
    @Column(name="ELF602_SSEQNO", columnDefinition="DECIMAL(3,0)")
    private BigDecimal elf602_sseqno;
    
    /** 分項ESG 約定條件全部未達成 **/
    @Size(max = 1)
    @Column(name="ELF602_SESGSUNRE", length=1, columnDefinition="CHAR(1)")
    private String elf602_sesgsunre;
    
    /**
	 * 取得序號 (UNID)
	 */
	public String getElf602_unid() {
		return this.elf602_unid;
	}

	/**
	 * 設定序號 (UNID)
	 */
	public void setElf602_unid(String value) {
		this.elf602_unid = value;
	}

	/**
	 * 取得追蹤事項通知日期
	 */
	public Date getElf602_fo_date() {
		return this.elf602_fo_date;
	}

	/**
	 * 設定追蹤事項通知日期
	 */
	public void setElf602_fo_date(Date value) {
		this.elf602_fo_date = value;
	}

	/**
	 * 取得額度序號
	 */
	public String getElf602_cntrno() {
		return this.elf602_cntrno;
	}

	/**
	 * 設定額度序號
	 */
	public void setElf602_cntrno(String value) {
		this.elf602_cntrno = value;
	}

	/**
	 * 取得放款帳號
	 */
	public String getElf602_loan_no() {
		return this.elf602_loan_no;
	}

	/**
	 * 設定放款帳號
	 */
	public void setElf602_loan_no(String value) {
		this.elf602_loan_no = value;
	}

	/**
	 * 取得業務別
	 */
	public String getElf602_loan_kind() {
		return this.elf602_loan_kind;
	}

	/**
	 * 設定業務別
	 */
	public void setElf602_loan_kind(String value) {
		this.elf602_loan_kind = value;
	}

	/**
	 * 取得類別
	 */
	public String getElf602_fo_kind() {
		return this.elf602_fo_kind;
	}

	/**
	 * 設定類別
	 */
	public void setElf602_fo_kind(String value) {
		this.elf602_fo_kind = value;
	}

	/**
	 * 取得追蹤事項通知內容
	 */
	public String getElf602_fo_content() {
		return this.elf602_fo_content;
	}

	/**
	 * 設定追蹤事項通知內容
	 */
	public void setElf602_fo_content(String value) {
		this.elf602_fo_content = value;
	}

	/**
	 * 取得應辦理追蹤對象
	 */
	public String getElf602_staff() {
		return this.elf602_staff;
	}

	/**
	 * 設定應辦理追蹤對象
	 */
	public void setElf602_staff(String value) {
		this.elf602_staff = value;
	}

	/**
	 * 取得檢核日期
	 */
	public Date getElf602_chkdate() {
		return this.elf602_chkdate;
	}

	/**
	 * 設定檢核日期
	 */
	public void setElf602_chkdate(Date value) {
		this.elf602_chkdate = value;
	}

	/**
	 * 取得符合註記
	 */
	public String getElf602_conform_fg() {
		return this.elf602_conform_fg;
	}

	/**
	 * 設定符合註記
	 */
	public void setElf602_conform_fg(String value) {
		this.elf602_conform_fg = value;
	}

	/**
	 * 取得追蹤說明
	 */
	public String getElf602_fo_memo() {
		return this.elf602_fo_memo;
	}

	/**
	 * 設定追蹤說明
	 */
	public void setElf602_fo_memo(String value) {
		this.elf602_fo_memo = value;
	}

	/**
	 * 取得辦理狀況
	 */
	public String getElf602_status() {
		return this.elf602_status;
	}

	/**
	 * 設定辦理狀況
	 */
	public void setElf602_status(String value) {
		this.elf602_status = value;
	}

	/**
	 * 取得資料寫入來源
	 */
	public String getElf602_datasrc() {
		return this.elf602_datasrc;
	}

	/**
	 * 設定資料寫入來源
	 */
	public void setElf602_datasrc(String value) {
		this.elf602_datasrc = value;
	}

	/**
	 * 取得還款來源異常註記
	 */
	public String getElf602_unusual_fg() {
		return this.elf602_unusual_fg;
	}

	/**
	 * 設定還款來源異常註記
	 */
	public void setElf602_unusual_fg(String value) {
		this.elf602_unusual_fg = value;
	}

	/**
	 * 取得理由敘述
	 */
	public String getElf602_unusualdesc() {
		return this.elf602_unusualdesc;
	}

	/**
	 * 設定理由敘述
	 */
	public void setElf602_unusualdesc(String value) {
		this.elf602_unusualdesc = value;
	}

	/**
	 * 取得是否承做
	 */
	public String getElf602_isnotional() {
		return this.elf602_isnotional;
	}

	/**
	 * 設定是否承做
	 */
	public void setElf602_isnotional(String value) {
		this.elf602_isnotional = value;
	}

	/**
	 * 取得是否申報疑似洗錢
	 */
	public String getElf602_isaml() {
		return this.elf602_isaml;
	}

	/**
	 * 設定是否申報疑似洗錢
	 */
	public void setElf602_isaml(String value) {
		this.elf602_isaml = value;
	}

	/**
	 * 取得最近維護日期
	 */
	public Date getElf602_upd_date() {
		return this.elf602_upd_date;
	}

	/**
	 * 設定最近維護日期
	 */
	public void setElf602_upd_date(Date value) {
		this.elf602_upd_date = value;
	}

	/**
	 * 取得最近維護經辦
	 */
	public String getElf602_upd_teller() {
		return this.elf602_upd_teller;
	}

	/**
	 * 設定最近維護經辦
	 */
	public void setElf602_upd_teller(String value) {
		this.elf602_upd_teller = value;
	}

	/**
	 * 取得最近維護主管
	 */
	public String getElf602_upd_supvno() {
		return this.elf602_upd_supvno;
	}

	/**
	 * 設定最近維護主管
	 */
	public void setElf602_upd_supvno(String value) {
		this.elf602_upd_supvno = value;
	}

    /**
     * 取得追蹤事項通知內容 -  全型字
     */
    public String getElf602_full_content() {
        return this.elf602_full_content;
    }

    /**
     * 設定追蹤事項通知內容 -  全型字
     */
    public void setElf602_full_content(String value) {
        this.elf602_full_content = value;
    }

	/**
	 * 取得附加文件mainId
	 */
	public String getElf602_fieldMainId() {
		return this.elf602_fieldMainId;
	}

	/**
	 * 設定附加文件mainId
	 */
	public void setElf602_fieldMainId(String value) {
		this.elf602_fieldMainId = value;
	}

    /**
     * 取得證明文件說明
     */
    public String getElf602_fileDesc() {
        return this.elf602_fileDesc;
    }

    /**
     * 設定證明文件說明
     */
    public void setElf602_fileDesc(String value) {
        this.elf602_fileDesc = value;
    }

	/** 取得借款人統編 **/
	public String getElf602_custid() {
		return this.elf602_custid;
	}

	/** 設定借款人統編 **/
	public void setElf602_custid(String value) {
		this.elf602_custid = value;
	}

	/** 取得重複序號 **/
	public String getElf602_dupno() {
		return this.elf602_dupno;
	}

	/** 設定重複序號 **/
	public void setElf602_dupno(String value) {
		this.elf602_dupno = value;
	}

	/** 取得最後維護分行 **/
	public String getElf602_br_no() {
		return this.elf602_br_no;
	}

	/** 設定最後維護分行 **/
	public void setElf602_br_no(String value) {
		this.elf602_br_no = value;
	}

    /** 取得案件註記 **/
    public String getElf602_case_mark() {
        return this.elf602_case_mark;
    }

    /** 設定案件註記 **/
    public void setElf602_case_mark(String value) {
        this.elf602_case_mark = value;
    }
    
	 /** 取得分項UID **/   
	public String getElf602_suid() {
		return elf602_suid;
	}
	
	/** 設定分項UID **/    
	public void setElf602_suid(String elf602_suid) {
		this.elf602_suid = elf602_suid;
	}

	/** 取得分項核准時間 **/
	public Date getElf602_sapptime() {
		return elf602_sapptime;
	}
	/** 設定分項核准時間 **/
	public void setElf602_sapptime(Date elf602_sapptime) {
		this.elf602_sapptime = elf602_sapptime;
	}

	/** 取得分項序號 **/
	public BigDecimal getElf602_sseqno() {
		return elf602_sseqno;
	}
	/** 設定分項序號 **/
	public void setElf602_sseqno(BigDecimal elf602_sseqno) {
		this.elf602_sseqno = elf602_sseqno;
	}

	/** 取得分項ESG 約定條件全部未達成 **/
	public String getElf602_sesgsunre() {
		return elf602_sesgsunre;
	}
	/** 設定分項ESG 約定條件全部未達成 **/
	public void setElf602_sesgsunre(String elf602_sesgsunre) {
		this.elf602_sesgsunre = elf602_sesgsunre;
	}

}
