/* 
 * L161S01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L161S01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L161S01B;

/** 聯貸案參貸比率一覽表明細檔 **/
@Repository
public class L161S01BDaoImpl extends LMSJpaDao<L161S01B, String> implements
		L161S01BDao {

	@Override
	public L161S01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L161S01B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("slBank", false);
		printSeqMap.put("slBranch", false);
		search.setOrderBy(printSeqMap);
		List<L161S01B> list = createQuery(L161S01B.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L161S01B findByUniqueKey(String mainId, Integer seq) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L161S01B> findByIndex01(String mainId, Integer seq) {
		ISearch search = createSearchTemplete();
		List<L161S01B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L161S01B.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L161S01B> findByOid(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L161S01B> list = createQuery(L161S01B.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<L161S01B> findByMainIdUid(String mainId,String pid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("slBank", false);
		printSeqMap.put("slBranch", false);
		search.setOrderBy(printSeqMap);
		List<L161S01B> list = createQuery(L161S01B.class, search)
				.getResultList();
		return list;
	}
	
}