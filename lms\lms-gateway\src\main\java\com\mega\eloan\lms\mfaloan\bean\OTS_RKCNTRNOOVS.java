package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 額度序號檔 **/
public class OTS_RKCNTRNOOVS extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;

	/** NOTES文件編號 **/
	@Column(name="NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String noteid;

	/** 
	 * 評等日期<p/>
	 * 最終評等日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="RATING_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date rating_date;

	/** 評等文件編號 **/
	@Column(name="RATING_ID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String rating_id;

	/** 客戶統一編號 **/
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String custid;

	/** 重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String dupno;

	/** 主借款人統一編號 **/
	@Column(name="CUST_KEY", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String cust_key;

	/** 
	 * 授信科目<p/>
	 * 3-4碼授信科目
	 */
	@Column(name="LOAN_CODE", length=4, columnDefinition="VARCHAR(4)", nullable=false,unique = true)
	private String loan_code;

	/** 
	 * 評等模型類別(c121m01a.mowType)
	 */
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String mowtype;
	
	/** 
	 * 房貸/非房貸註記(l141m01c.modelType)
	 * N=非房貸、M=房貸
	 */
	@Column(name="MOWTYPE2", length=1, columnDefinition="CHAR(1)")
	private String mowtype2;
	
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	@Column(name="MOWTYPE_COUNTRY", length=2, columnDefinition="CHAR(2)", nullable=false,unique = true)
	private String mowtype_country;

	/** 模型版本-大版 **/
	@Column(name="MOWVER1", columnDefinition="DEC(5,0)", nullable=false,unique = true)
	private Integer mowver1;

	/** 模型版本-小版 **/
	@Column(name="MOWVER2", columnDefinition="DEC(5,0)", nullable=false,unique = true)
	private Integer mowver2;

	/** 
	 * 科目<p/>
	 * 8碼會計科目
	 */
	@Column(name="SUBJCODE", length=8, columnDefinition="VARCHAR(8)")
	private String subjcode;

	/** 
	 * 相關身分<p/>
	 * M: 主借款人  C: 共同借款人  G: 連帶保證人
	 */
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String lngeflag;

	/** 本件額度序號 **/
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrno;

	/** 文件狀態 **/
	@Column(name="DOCSTATUS", length=2, columnDefinition="CHAR(2)")
	private String docstatus;

	/** 申請額度幣別 **/
	@Column(name="LOANCURR", length=3, columnDefinition="CHAR(3)")
	private String loancurr;

	/** 申請額度金額 **/
	@Column(name="LOANAMT", columnDefinition="DEC(13,0)")
	private BigDecimal loanamt;

	/** 授信期間(年) **/
	@Column(name="LOAN_PERIOD", columnDefinition="DEC(15,5)")
	private BigDecimal loan_period;

	/** 額度擔保註記 **/
	@Column(name="S_FLAG", length=1, columnDefinition="CHAR(1)")
	private String s_flag;

	/** 上傳資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATA_SRC_DT", columnDefinition="DATE")
	private Date data_src_dt;

	/** 取得分行別 **/
	public String getBr_cd() {
		return this.br_cd;
	}
	/** 設定分行別 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}

	/** 取得NOTES文件編號 **/
	public String getNoteid() {
		return this.noteid;
	}
	/** 設定NOTES文件編號 **/
	public void setNoteid(String value) {
		this.noteid = value;
	}

	/** 
	 * 取得評等日期<p/>
	 * 最終評等日
	 */
	public Date getRating_date() {
		return this.rating_date;
	}
	/**
	 *  設定評等日期<p/>
	 *  最終評等日
	 **/
	public void setRating_date(Date value) {
		this.rating_date = value;
	}

	/** 取得評等文件編號 **/
	public String getRating_id() {
		return this.rating_id;
	}
	/** 設定評等文件編號 **/
	public void setRating_id(String value) {
		this.rating_id = value;
	}

	/** 取得客戶統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定客戶統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得主借款人統一編號 **/
	public String getCust_key() {
		return this.cust_key;
	}
	/** 設定主借款人統一編號 **/
	public void setCust_key(String value) {
		this.cust_key = value;
	}

	/** 
	 * 取得授信科目<p/>
	 * 3-4碼授信科目
	 */
	public String getLoan_code() {
		return this.loan_code;
	}
	/**
	 *  設定授信科目<p/>
	 *  3-4碼授信科目
	 **/
	public void setLoan_code(String value) {
		this.loan_code = value;
	}

	/** 
	 * 取得評等模型類別(c121m01a.mowType)
	 */
	public String getMowtype() {
		return this.mowtype;
	}
	/**
	 *  設定評等模型類別(c121m01a.mowType)
	 **/
	public void setMowtype(String value) {
		this.mowtype = value;
	}
	
	/** 
	 * 取得房貸/非房貸註記(l141m01c.modelType)
	 * N=非房貸、M=房貸
	 */
	public String getMowtype2() {
		return this.mowtype2;
	}
	/**
	 *  設定房貸/非房貸註記(l141m01c.modelType)
	 *  N=非房貸、M=房貸
	 **/
	public void setMowtype2(String value) {
		this.mowtype2 = value;
	}
	
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	public String getMowtype_country() {
		return this.mowtype_country;
	}
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	public void setMowtype_country(String value) {
		this.mowtype_country = value;
	}
	

	/** 取得模型版本-大版 **/
	public Integer getMowver1() {
		return this.mowver1;
	}
	/** 設定模型版本-大版 **/
	public void setMowver1(Integer value) {
		this.mowver1 = value;
	}

	/** 取得模型版本-小版 **/
	public Integer getMowver2() {
		return this.mowver2;
	}
	/** 設定模型版本-小版 **/
	public void setMowver2(Integer value) {
		this.mowver2 = value;
	}

	/** 
	 * 取得科目<p/>
	 * 8碼會計科目
	 */
	public String getSubjcode() {
		return this.subjcode;
	}
	/**
	 *  設定科目<p/>
	 *  8碼會計科目
	 **/
	public void setSubjcode(String value) {
		this.subjcode = value;
	}

	/** 
	 * 取得相關身分<p/>
	 * M: 主借款人  C: 共同借款人  G: 連帶保證人
	 */
	public String getLngeflag() {
		return this.lngeflag;
	}
	/**
	 *  設定相關身分<p/>
	 *  M: 主借款人  C: 共同借款人  G: 連帶保證人
	 **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 取得本件額度序號 **/
	public String getCntrno() {
		return this.cntrno;
	}
	/** 設定本件額度序號 **/
	public void setCntrno(String value) {
		this.cntrno = value;
	}

	/** 取得文件狀態 **/
	public String getDocstatus() {
		return this.docstatus;
	}
	/** 設定文件狀態 **/
	public void setDocstatus(String value) {
		this.docstatus = value;
	}

	/** 取得申請額度幣別 **/
	public String getLoancurr() {
		return this.loancurr;
	}
	/** 設定申請額度幣別 **/
	public void setLoancurr(String value) {
		this.loancurr = value;
	}

	/** 取得申請額度金額 **/
	public BigDecimal getLoanamt() {
		return this.loanamt;
	}
	/** 設定申請額度金額 **/
	public void setLoanamt(BigDecimal value) {
		this.loanamt = value;
	}

	/** 取得授信期間(年) **/
	public BigDecimal getLoan_period() {
		return this.loan_period;
	}
	/** 設定授信期間(年) **/
	public void setLoan_period(BigDecimal value) {
		this.loan_period = value;
	}

	/** 取得額度擔保註記 **/
	public String getS_flag() {
		return this.s_flag;
	}
	/** 設定額度擔保註記 **/
	public void setS_flag(String value) {
		this.s_flag = value;
	}

	/** 取得上傳資料日期 **/
	public Date getData_src_dt() {
		return this.data_src_dt;
	}
	/** 設定上傳資料日期 **/
	public void setData_src_dt(Date value) {
		this.data_src_dt = value;
	}
}
