package com.mega.eloan.lms.rpt.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.util.HtmlUtils;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.mfaloan.service.MisRatetblService;
import com.mega.eloan.lms.model.C820M01A;
import com.mega.eloan.lms.rpt.pages.CLS9501V01Page;
import com.mega.eloan.lms.rpt.service.CLS9501V01Service;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

@Service("cls9501v01xlsservice")
public class CLS9501V01XLSServiceImpl implements FileDownloadService {

	@Resource
	CLS9501V01Service service;

	@Resource
	MisRatetblService rateService;

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS9501V01XLSServiceImpl.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		// Properties
		ByteArrayOutputStream baos = null;
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS9501V01Page.class);
		try {
			baos = new ByteArrayOutputStream();
			WritableWorkbook book = Workbook.createWorkbook(baos);
			// --------------標題列格式----------
			// 無外框，標楷，12大小
			WritableFont fontStyle = new WritableFont(
					WritableFont.createFont("標楷體"), 12, WritableFont.NO_BOLD,
					false);
			WritableCellFormat justFont = new WritableCellFormat(fontStyle);
			// 有外框，標楷，12大小
			WritableCellFormat withBorder = LMSUtil.setCellFormat(justFont,
					fontStyle, Alignment.CENTRE);
			// 純外框，數字用(標楷+common後太驚悚...
			WritableCellFormat numCell = new WritableCellFormat();
			numCell.setVerticalAlignment(VerticalAlignment.CENTRE);
			numCell.setAlignment(Alignment.RIGHT);
			numCell.setWrap(true);// 自動換行
			numCell.setBorder(jxl.format.Border.ALL,
					jxl.format.BorderLineStyle.THIN);

			// -------------資料內容------------

			// 先將所有資料寫入DB(只改勾選狀態)
			String mainId = params.getString("mainId");
			
			List<C820M01A> allSelects = service.getAllSelects(mainId);
			// 將DB中所有有勾選的資料都寫入
			if (allSelects!=null && allSelects.size()!=0) {
				for (int i = 0; i < allSelects.size(); i++) {
					C820M01A record = allSelects.get(i);
					WritableSheet sheet = book.createSheet(record.getBrno()
							+ record.getBrName(), i + 1);
					String dataYM = Util.trim(record.getDataYM());

					// 文件資訊
					Label head = new Label(0, 0,
							pop.getProperty("C820M01A.brno") + "：", justFont);
					sheet.addCell(head);
					head = new Label(1, 0, record.getBrno(), justFont);
					sheet.addCell(head);
					head = new Label(0, 1, pop.getProperty("C820M01A.brName")
							+ "：", justFont);
					sheet.addCell(head);
					head = new Label(1, 1, record.getBrName(), justFont);
					sheet.addCell(head);
					head = new Label(0, 2, pop.getProperty("dataDate") + "：",
							justFont);
					sheet.addCell(head);
					
					if (Util.isEmpty(dataYM)) {
						sheet.setColumnView(0, 10);
						head = new Label(1, 2, pop.getProperty("emptyDate"),
								justFont);
						sheet.addCell(head);
					} else {
						head = new Label(1, 2, record.getDataYM(), justFont);
						sheet.addCell(head);
						// 取得匯率
						int TWDyear = Util.parseInt(dataYM.split("-")[0]) - 1911;
						Map<String, Object> rate = rateService
								.findByDate(TWDyear + dataYM.split("-")[1]);

						int headShift = 4;
						// 標題
						int[] width = { 13, 30, 15, 5, 15, 15, 5, 15, 15, 8,
								15, 15, 20 };
						for (int x = 0; x < width.length; x++) {
							sheet.setColumnView(x, width[x]);
							Label title = new Label(x, headShift,
									pop.getProperty("col" + x), withBorder);
							sheet.addCell(title);
						}
						List<Map<String, Object>> data = service.queryData(
								record.getBrno(), dataYM);
						headShift++;
						for (int row = 0; row < data.size(); row++) {
							Map<String, Object> pivot = data.get(row);
							// 客戶統編
							Label content = new Label(0, headShift + row,
									HtmlUtils.htmlEscape(Util.trim(pivot.get("custId"))), withBorder);
							sheet.addCell(content);
							// 客戶姓名
							content = new Label(1, headShift + row, HtmlUtils.htmlEscape(Util.trim(pivot.get("custName"))),
									withBorder);
							sheet.addCell(content);
							// 額度序號
							content = new Label(2, headShift + row,
									Util.trim(pivot.get("cntrno")), withBorder);
							sheet.addCell(content);
							// 核准(現請)
							// 幣別
							String applyCurr = Util.trim(pivot
									.get("CURRENTAPPLYCURR"));
							content = new Label(3, headShift + row, applyCurr,
									withBorder);
							sheet.addCell(content);
							// 核准金額
							BigDecimal applyAmt = Util.parseBigDecimal(pivot
									.get("CURRENTAPPLYAMT"));
							content = new Label(4, headShift + row,
									NumConverter.addComma(applyAmt), numCell);
							sheet.addCell(content);
							// 折合台幣
							BigDecimal applyTWD = (Util.isNotEmpty(applyCurr)) ? applyAmt
									.multiply(Util.parseBigDecimal(rate
											.get(applyCurr))) : applyAmt;
							content = new Label(5, headShift + row,
									NumConverter.addComma(applyTWD.setScale(0,
											BigDecimal.ROUND_HALF_UP)), numCell);
							sheet.addCell(content);
							// 前准
							// 幣別
							String lv2Curr = Util.trim(pivot.get("LV2CURR"));
							content = new Label(6, headShift + row, lv2Curr,
									numCell);
							sheet.addCell(content);
							// 前准金額
							BigDecimal lv2Amt = Util.parseBigDecimal(pivot
									.get("LV2AMT"));
							content = new Label(7, headShift + row,
									NumConverter.addComma(lv2Amt), numCell);
							sheet.addCell(content);
							// 折合台幣
							BigDecimal lv2TWD = (Util.isNotEmpty(lv2Curr)) ? lv2Amt
									.multiply(Util.parseBigDecimal(rate
											.get(lv2Curr))) : lv2Amt;
							content = new Label(8, headShift + row,
									NumConverter.addComma(lv2TWD.setScale(0,
											BigDecimal.ROUND_HALF_UP)), numCell);
							sheet.addCell(content);
							// 匯率(月底)
							content = new Label(9, headShift + row,
									Util.trim(rate.get(applyCurr)), numCell);
							sheet.addCell(content);
							// 差額
							content = new Label(10, headShift + row,
									NumConverter.addComma(applyTWD.subtract(
											lv2TWD).setScale(0,
											BigDecimal.ROUND_HALF_UP)), numCell);
							sheet.addCell(content);
							// 共用額度序號
							content = new Label(11, headShift + row,
									Util.trim(pivot.get("COMMSNO")), withBorder);
							sheet.addCell(content);
							// 授權等級
							content = new Label(
									12,
									headShift + row,
									pop.getProperty("lv" + pivot.get("AUTHLVL")),
									withBorder);
							sheet.addCell(content);
						}
					}
				}

			} else {
				WritableSheet sheet = book.createSheet(
						pop.getProperty("nonSelect"), 1);
				Label Msg = new Label(0, 0, pop.getProperty("nonSelect"),
						justFont);
				sheet.addCell(Msg);
			}
			book.write();
			book.close();
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}
}
