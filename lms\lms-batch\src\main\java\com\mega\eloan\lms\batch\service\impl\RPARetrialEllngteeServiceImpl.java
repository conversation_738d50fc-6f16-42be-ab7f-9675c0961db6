package com.mega.eloan.lms.batch.service.impl;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.tools.ant.util.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.gwclient.GWLogger;
import com.mega.eloan.common.gwclient.GWType;
import com.mega.eloan.common.service.GWLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElCUS25Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * RPA 回傳勞工紓困貸款案件狀態
 * </pre>
 * <p>
 * LOCAL Test URL example ： http://localhost/ces-web/app/schedulerRPA
 * <p>
 * Post Request : {"serviceId":"getSmallBusStatus", "vaildIP":"N",
 * "request":{"responseCode":"1","custId":"13724746","brNo":"007",
 * "rpaUserId","078001"}}
 * <p>
 * SIT http://*************/ces-web/app/schedulerRPA
 * 
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/6/2,EL07623,new
 *          </ul>
 * @since 2021/6/2
 */
@Service("queryEllngteeService")
public class RPARetrialEllngteeServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger logger = LoggerFactory
			.getLogger(RPARetrialEllngteeServiceImpl.class);

	@Resource
	LMS1201Service service1201;

	@Resource
	SysParameterService sysParameterService;

	@Resource
	GWLogService gwLogService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	MisELLNGTEEService misEllngteeService;

	@Resource
	MisElCUS25Service elcus25Srv;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisElCUS25Service misElcus25Service;

	@Resource
	MisLNF022Service misLNF022Service;

	@Resource
	MisMISLN20Service misMisln20Service;

	@Resource
	EjcicService ejcicService;

	@Resource
	LMSService lmsService;

	@Value("${systemId}")
	private String sysId;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 * 
	 * REQUEST: http://localhost:9081/lms-web/app/schedulerRPA
	 * {"serviceId":"queryEjcicService"
	 * ,"vaildIP":"N","request":{"custId":"10101013"
	 * ,"dupNo":"0","brNo":"010","rpaUserId":"007623"}}
	 * 
	 * RESPONSE: {"rc":0,"rcmsg":"SUCCESS","message":"執行成功","rcUrl":
	 * "http://***************/ejcic/combination/JCIC0444.jsp?deptid=010&branchnm=%C4%F5%B6%AE%A4%C0%A6%E6&prodid=P1&queryid=10101013&empname=%B6%C0_007623&empid=007623&apid=EL&pur=B4A&purpose=1&cbdeptid=0103&key=ELKEY17925320881792"
	 * }
	 */

	Map<String, Object> lngeFlagNameMap = new HashMap<String, Object>();

	static final String 主要借款人 = "1";
	static final String 負責人 = "2";
	static final String 共同借款人 = "3";
	static final String 共同發票人 = "4";
	static final String 保證人 = "99";

	// J-111-0184_05097_B1001 Web e-Loan覆審發查聯徵, 針對企業負責人未擔任案下保證人者, 查詢理由要改為XGA
	static final String 查詢理由_B4A = "B4A";
	static final String 查詢理由_XGA = "XGA";

	String mainCustJcicFg = "";
	String mainCustNoJcicReason = "";
	String chairManJcicFg = "";
	String chairManNoJcicReason = "";

	// J-111-0184_05097_B1001 Web e-Loan覆審發查聯徵, 針對企業負責人未擔任案下保證人者, 查詢理由要改為XGA
	String chairMainPur = ""; // 負責人查詢理由:B4A=> 第一層 B.原業務往來 第二層 4放款業務(c) 第三層
								// A.取得當事人書面同意、

	// XGA=>第一層 X.其他,第二層G.其他(e),第三層A.當事人同意

	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject mag;
		logger.info("queryEllngteeService 啟動========================");
		logger.info("傳入參數==>[{}]", json.toString());
		GWLogger gwlogger = new GWLogger(GWType.GWTYPE_RPA, gwLogService,
				sysParameterService);

		JSONObject req = json.getJSONObject("request");
		String errorMsg = "";
		String custId = req.optString("custId", "");
		String dupNo = req.optString("dupNo", "");
		String brNo = req.optString("brNo", "");
		String rpaUserId = req.optString("rpaUserId", "");

		gwlogger.logBegin(sysId, custId, "queryEllngteeService",
				req.toString(), System.currentTimeMillis());

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String userId = rpaUserId;

		String areaName = branchService.getBranch(brNo).getBrnGroup();
		if (Util.equals(areaName, "")) {
			areaName = brNo;
		}

		JSONArray jsonArrayAllVal = new JSONArray();

		Map<String, Map<String, Object>> allQueryMap = new LinkedHashMap<String, Map<String, Object>>();
		Map<String, Object> queryMap = null;
		String custKey = "";
		String mainCustType = ""; // 借款人企消金類別 1.企業戶 2.個人戶

		// 設定顯示名稱
		lngeFlagNameMap.put(主要借款人, "主借款人");
		lngeFlagNameMap.put(負責人, "負責人");
		lngeFlagNameMap.put(共同借款人, "共同借款人");
		lngeFlagNameMap.put(共同發票人, "共同發票人");
		lngeFlagNameMap.put(保證人, "保證人");

		mainCustJcicFg = "Y";
		mainCustNoJcicReason = "";
		chairManJcicFg = "Y";
		chairManNoJcicReason = "";

		// J-111-0184_05097_B1001 Web e-Loan覆審發查聯徵, 針對企業負責人未擔任案下保證人者, 查詢理由要改為XGA
		// 至忠，下午 01:37
		// 覆審針對企業負責人沒有擔任連保人者, 查詢理由必須用其他,其他,當事人同意
		chairMainPur = 查詢理由_XGA; // 負責人查詢理由預設XGA，待後面如果同時是連保人，則改為B4A

		BigDecimal adjAmt = BigDecimal.ZERO;
		Map<String, String> _m_NewCase = new HashMap<String, String>();
		Map<String, String> aliveCntrNo = new HashMap<String, String>();

		try {

			// 主債務人*************************************************

			custKey = custId + "-" + dupNo;
			// 判斷LNF022 有效額度是否大於0

			// J-110-0304_05097_B1007 Web e-Loan授信覆審配合RPA作業修改
			// 因為半夜會遇到LNF022倒檔過程資料被清空，導致誤判主借款人於該分行下無有效額度，故授審處金襄理要求改抓LNF020
			// List<Map<String, Object>> ctlDatas = misLNF022Service
			// .getLNF022_forCtl(custId, dupNo, brNo);
			// for (Map<String, Object> ctlData : ctlDatas) {
			// String cntrNo = Util.trim(MapUtils.getString(ctlData,
			// "QUOTANO", ""));
			// BigDecimal tAdjAmt = Util.parseBigDecimal(MapUtils.getString(
			// ctlData, "QUOTAPRV", "0"));
			// // ===
			// if (_m_NewCase.containsKey(cntrNo)) {
			// continue;
			// } else {
			// _m_NewCase.put(cntrNo, cntrNo);
			// adjAmt = adjAmt.add(tAdjAmt);
			//
			// if (tAdjAmt.compareTo(BigDecimal.ZERO) > 0) {
			// aliveCntrNo.put(cntrNo, cntrNo);
			// }
			// }
			//
			// }

			// if (adjAmt.compareTo(BigDecimal.ZERO) <= 0) {
			// mainCustJcicFg = "N";
			// mainCustNoJcicReason = "主借款人於該分行下無有效額度";
			// }

			// J-110-0304_05097_B1007 Web e-Loan授信覆審配合RPA作業修改
			List<Map<String, Object>> ctlDatas = misMisln20Service
					.findRetrialEffectCust(brNo, custId, dupNo);
			if (ctlDatas == null || ctlDatas.isEmpty()) {
				mainCustJcicFg = "N";
				mainCustNoJcicReason = "主借款人於該分行下無有效額度";
			} else {
				for (Map<String, Object> ctlData : ctlDatas) {
					String cntrNo = Util.trim(MapUtils.getString(ctlData,
							"LNF020_CONTRACT", ""));

					aliveCntrNo.put(cntrNo, cntrNo);
				}
			}

			queryMap = new HashMap<String, Object>();
			queryMap.put("lngename", "");
			queryMap.put("lngeFlag", 主要借款人); // 主借款人

			allQueryMap.put(custKey, queryMap);

			Map<String, Object> busCDMap = misCustdataService
					.findBUSCDByCustIdANdDupNo(custId, dupNo);
			String buscd = "";
			if (busCDMap != null) {
				// 行業別代碼
				buscd = Util.trim(busCDMap.get("BUSCD"));
			} else {
				errorMsg = "0024無行業對象別(主借款人):" + custId + " " + dupNo;
			}

			// 依行業對象別判斷是否需要負責人
			if (Util.notEquals(Util.trim(buscd), "")) {

				if (!(Util.equals(buscd, "130300") || Util.equals(buscd,
						"060000"))) {
					mainCustType = "1"; // 1.企業戶 2.個人戶
					// 企業戶
					List<Map<String, Object>> listMap = misElcus25Service
							.findMiselcus25(custId, dupNo);

					if (listMap != null && !listMap.isEmpty()) {

						// 負責人
						String aLoanChairManID = "";
						String aLoanChairmanDupNo = "";
						String aLoanChairManName = "";
						String aLoanChairManEName = "";

						for (Map<String, Object> perMap : listMap) {
							aLoanChairManID = Util.trim(perMap.get("SUP1ID"));
							aLoanChairmanDupNo = Util.trim(perMap
									.get("SUP1DUPNO"));
							aLoanChairManName = Util.toSemiCharString(Util
									.trim(perMap.get("SUP1CNM")));
							aLoanChairManEName = Util.trim(perMap
									.get("SUP1ENM"));

							if (Util.notEquals(aLoanChairManID, "")
									&& Util.notEquals(aLoanChairmanDupNo, "")) {
								queryMap = new HashMap<String, Object>();
								queryMap.put("lngename", aLoanChairManName);
								queryMap.put("lngeFlag", 負責人); // 負責人

								// 負責人最近一年有查詢聯徵紀錄且為本人同意時才查聯徵
								// queryItem=P1 企金 611 P7 消金 128 、135
								// LMS_RPA_RETRIAL_SUP_JCIC_LOG_Y -1
								// 年，負責人最近一年半有查詢聯徵紀錄且為本人同意時才查聯徵
								// LMS_RPA_RETRIAL_SUP_JCIC_LOG_M -6
								// 月，負責人最近一年半有查詢聯徵紀錄且為本人同意時才查聯徵

								String LMS_RPA_RETRIAL_SUP_JCIC_LOG_Y = Util
										.trim(lmsService
												.getSysParamDataValue("LMS_RPA_RETRIAL_SUP_JCIC_LOG_Y"));
								String LMS_RPA_RETRIAL_SUP_JCIC_LOG_M = Util
										.trim(lmsService
												.getSysParamDataValue("LMS_RPA_RETRIAL_SUP_JCIC_LOG_M"));

								Calendar cal = Calendar.getInstance();

								if (Util.equals(LMS_RPA_RETRIAL_SUP_JCIC_LOG_Y,
										"")) {
									// 預設一年
									cal.add(Calendar.YEAR, -1);
								} else {
									cal.add(Calendar.YEAR,
											Util.parseInt(LMS_RPA_RETRIAL_SUP_JCIC_LOG_Y));
								}

								if (Util.equals(LMS_RPA_RETRIAL_SUP_JCIC_LOG_M,
										"")) {
									cal.add(Calendar.MONTH, 0);
								} else {
									cal.add(Calendar.MONTH,
											Util.parseInt(LMS_RPA_RETRIAL_SUP_JCIC_LOG_M));
								}

								String bgnDate = StringUtils.replace(
										TWNDate.toTW(cal.getTime()), "/", "");
								String endDate = StringUtils.replace(TWNDate
										.toTW(CapDate.getCurrentTimestamp()),
										"/", "");

								List<Map<String, Object>> chairMainJcic = ejcicService
										.getZAM003RQueryRecord(aLoanChairManID,
												bgnDate, endDate, "128");
								if (chairMainJcic == null
										|| chairMainJcic.isEmpty()) {
									chairManJcicFg = "N"; // 負責人是否需查聯徵
									chairManNoJcicReason = "負責人最近" + bgnDate
											+ "-" + endDate + "沒有本人同意之聯徵查詢紀錄"; // 負責人不查聯徵理由
								}

								// J-111-0184_05097_B1001 Web e-Loan覆審發查聯徵,
								// 針對企業負責人未擔任案下保證人者, 查詢理由要改為XGA
								// 負責人查詢理由預設XGA，待後面如果同時是連保人，則改為B4A
								chairMainPur = 查詢理由_XGA;

								custKey = aLoanChairManID + "-"
										+ aLoanChairmanDupNo;
								allQueryMap.put(custKey, queryMap);
								break;
							}
						}
					}

				} else {
					// 個人戶
					mainCustType = "2"; // 1.企業戶 2.個人戶
				}

			} else {
				errorMsg = "0024無行業對象別(主借款人):" + custId + " " + dupNo;
			}

			// 依借款人統編抓未銷戶之從債務人
			// List<Map<String, Object>> ellngteeData = misEllngteeService
			// .findMisEllngteeNotCancelAllLngeidForRpa(custId, dupNo);

			for (String aCntrNo : aliveCntrNo.keySet()) {
				if (Util.notEquals(aCntrNo, "")) {

					List<Map<String, Object>> ellngteeData = misEllngteeService
							.findByCntrNo(aCntrNo);

					for (Map<String, Object> ellData : ellngteeData) {

						String lngeid = "";
						String dupNo1 = "";
						String lngename = "";
						String lngeFlag = "";

						lngeid = Util.trim(MapUtils
								.getString(ellData, "LNGEID"));
						dupNo1 = Util.trim(MapUtils
								.getString(ellData, "DUPNO1"));
						lngename = Util.trim(MapUtils.getString(ellData,
								"LNGENM"));
						lngeFlag = Util.trim(MapUtils.getString(ellData,
								"LNGEFLAG"));
						// if (Util.equals(lngeFlag, "C")
						// || Util.equals(lngeFlag, "G")
						// || Util.equals(lngeFlag, "L")
						// || Util.equals(lngeFlag, "N")) {
						if (this.isLngeFlagCanQueryEJcic(lngeFlag)) {

							// C: 共同借款人
							// D: 共同發票人　
							// E: 票據債務人（指金融交易之擔保背書）
							// G: 連帶保證人，擔保品提供人兼連帶保證人
							// L: 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人
							// S: 擔保品提供人
							// N: ㄧ般保證人

							// [上午 11:17] 金至忠 共同發票人不要查

							String newLngeFlag = 保證人; // 保證人
							if (Util.equals(lngeFlag, "C")) {
								newLngeFlag = 共同借款人; // 共同借款人
							}

							custKey = lngeid + "-" + dupNo1;

							if (allQueryMap.containsKey(custKey)) {

								queryMap = allQueryMap.get(custKey);

								String orgLngeFlag = MapUtils.getString(
										queryMap, "lngeFlag");

								// if (!StringUtils.contains(orgLngeFlag,
								// lngeFlag)) {
								// orgLngeFlag = orgLngeFlag + "," + lngeFlag;
								// }

								if (Util.parseInt(orgLngeFlag) == Util
										.parseInt(負責人)) {
									// 如果他是負責人，但是又是連保人或共同借款人，就算他最近一年沒有本人同意的聯徵查詢紀錄，但因為他又是連保人，所以還是要查
									chairManJcicFg = "Y"; // 負責人是否需查聯徵
									chairManNoJcicReason = ""; // 負責人不查聯徵理由

									// J-111-0184_05097_B1001 Web e-Loan覆審發查聯徵,
									// 針對企業負責人未擔任案下保證人者, 查詢理由要改為XGA
									chairMainPur = 查詢理由_B4A;

								}

								if (Util.parseInt(newLngeFlag) <= Util
										.parseInt(orgLngeFlag)) {
									orgLngeFlag = newLngeFlag;
									queryMap.put("lngeFlag", orgLngeFlag);
									// allQueryMap.put(custKey, queryMap);
								}

							} else {
								queryMap = new HashMap<String, Object>();
								queryMap.put("lngename", lngename);
								queryMap.put("lngeFlag", newLngeFlag); // 主借款人
								// allQueryMap.put(custKey, queryMap);

							}

							allQueryMap.put(custKey, queryMap);
						}

					}

				}
			}

			// 主借款人+負責人+從債務人
			for (String tCustKey : allQueryMap.keySet()) {

				queryMap = allQueryMap.get(tCustKey);

				String lngeid = tCustKey.split("-")[0];
				String dupNo1 = tCustKey.split("-")[1];
				;
				String lngename = MapUtils.getString(queryMap, "lngename");
				String lngeFlag = MapUtils.getString(queryMap, "lngeFlag");
				JSONObject jsonVal = new JSONObject();
				errorMsg = this.getCustData(lngeid, dupNo1, lngename, areaName,
						lngeFlag, mainCustType, jsonVal);
				if (Util.equals(errorMsg, "")) {
					jsonArrayAllVal.add(jsonVal);
				} else {
					break;
				}

			}
		} catch (Exception e) {
			errorMsg = e.toString();
		}

		logger.info("queryEllngteeService 結束========================");

		GWException gwException = null;
		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
			gwException = new GWException(errorMsg, getClass(),
					GWException.GWTYPE_RPA);
		}

		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
		} else {
			logger.info("執行成功");
		}

		if (!CapString.isEmpty(errorMsg)) {
			// mag = JSONObject
			// .fromObject("{\"rc\": 0, \"rcmsg\": \"FAIL\", \"message\":\" "
			// + errorMsg + "\"}");

			JSONObject jsonVal = new JSONObject();
			jsonVal.put("rc", "1");
			jsonVal.put("rcmsg", "FAIL");
			jsonVal.put("message", errorMsg);
			mag = jsonVal;

		} else {
			String rcVal = jsonArrayAllVal.toString();
			rcVal = StringUtils.replace(rcVal, "[", "(");
			rcVal = StringUtils.replace(rcVal, "]", ")");
			// mag = JSONObject
			// .fromObject("{\"rc\": 0, \"rcmsg\": \"SUCCESS\", \"message\":\"執行成功\", \"rcVal\": "
			// + rcVal + "}");

			JSONObject jsonVal = new JSONObject();
			jsonVal.put("rc", "0");
			jsonVal.put("rcmsg", "SUCCESS");
			jsonVal.put("message", "執行成功");
			jsonVal.put("rcVal", rcVal);
			mag = jsonVal;

		}

		gwlogger.logEnd(mag.toString(), gwException, "0");
		return mag;
	}

	String getCustData(String custId, String dupNo, String custName,
			String areaName, String lngeFlag, String mainCustType,
			JSONObject jsonVal) {
		String errorMsg = "";
		List<Map<String, Object>> ids = null;
		Map<String, Object> busCDMap = null;

		String lngeid = "";
		String dupNo1 = "";
		String lngename = "";
		String jcicTaxNo = "";
		String buscd = "";
		String custType = ""; // 1.企業戶 2.個人戶
		String prodKind = "";
		String purpose = "";
		lngeid = custId;
		dupNo1 = dupNo;
		lngename = custName;
		jcicTaxNo = "";
		buscd = "";
		custType = ""; // 1.企業戶 2.個人戶
		prodKind = "";

		boolean isOBU = false;
		String queryKey = Util.trim(custId);
		if (queryKey.length() == 10
				&& queryKey.matches("^[\\D]{2}[zZ][\\w]{7}")) {
			isOBU = true;
			// OBU要改以聯徵虛擬統編查聯徵
			ids = elcus25Srv.findById(custId);
			if (!CollectionUtils.isEmpty(ids)) {
				for (Map<String, Object> m : ids) {
					jcicTaxNo = Util.trim(MapUtils.getString(m,
							"CM25_JCIC_TAXNO"));
					if (Util.equals(jcicTaxNo, lngeid)) {
						jcicTaxNo = "";
					}
					break;
				}
			}
		}

		if (Util.equals(mainCustType, "1")) {
			// 企業戶
			purpose = "1";// 查詢目的{1:企業授信, 2:房屋貸款,3:消費性貸款, 4:留學生貸款}
		} else {
			// 個人戶
			purpose = "2";// 查詢目的{1:企業授信, 2:房屋貸款,3:消費性貸款, 4:留學生貸款}
		}

		busCDMap = misCustdataService.findBUSCDByCustIdANdDupNo(lngeid, dupNo1);

		if (busCDMap != null) {
			// 行業別代碼
			buscd = Util.trim(busCDMap.get("BUSCD"));
		} else {
			// errorMsg = "0024無資料:" + custId + " " + dupNo;
			buscd = "";
		}

		// 有行業對象別才檢核
		if (Util.notEquals(Util.trim(buscd), "")) {

			if (Util.equals(buscd, "130300") || Util.equals(buscd, "060000")) {
				// 個人戶
				custType = "2"; // 1.企業戶 2.個人戶
				prodKind = "P7";

			} else {
				// 企業戶
				custType = "1"; // 1.企業戶 2.個人戶
				prodKind = "PB"; // PB=P1產品一+J02

			}

		} else {
			if (Util.trim(lngeid).length() == 10) {
				if (Util.equals(Util.trim(lngeid).substring(2, 3), "Z")) {
					// 企業戶
					custType = "1"; // 1.企業戶 2.個人戶
					prodKind = "PB"; // PB=P1產品一+J02

				} else {
					// 個人戶
					custType = "2"; // 1.企業戶 2.個人戶
					prodKind = "P7";

				}
			} else {
				// 企業戶
				custType = "1"; // 1.企業戶 2.個人戶
				prodKind = "PB"; // PB=P1產品一+J02

			}
		}

		jsonVal.put("LNGEID", lngeid);
		jsonVal.put("DUPNO1", dupNo1);
		jsonVal.put("LNGENAME", lngename);
		jsonVal.put("JCICTAXNO", jcicTaxNo);
		jsonVal.put("PRODKIND", prodKind);
		jsonVal.put("CUSTTYPE", custType);
		jsonVal.put("JCICQID", Util.equals(Util.trim(jcicTaxNo), "") ? lngeid
				: jcicTaxNo);
		jsonVal.put("JCICQDUPNO", dupNo1);
		jsonVal.put("AREANO", areaName);
		jsonVal.put("LNGEFLAG",
				MapUtils.getString(lngeFlagNameMap, lngeFlag, ""));
		jsonVal.put("PURPOSE", purpose); // 查詢目的{1:企業授信, 2:房屋貸款,3:消費性貸款,
											// 4:留學生貸款}

		// 主借款人不用查聯徵，底下所有人都不用查
		jsonVal.put("JCICQFG", mainCustJcicFg);
		jsonVal.put("NOJCICREASON", mainCustNoJcicReason);

		// J-111-0184_05097_B1001 Web e-Loan覆審發查聯徵, 針對企業負責人未擔任案下保證人者, 查詢理由要改為XGA
		if (Util.parseInt(lngeFlag) == Util.parseInt(負責人)) {
			jsonVal.put("PUR", Util.isEmpty(chairMainPur) ? 查詢理由_XGA
					: chairMainPur);
		} else {
			jsonVal.put("PUR", 查詢理由_B4A);
		}

		if (Util.equals(mainCustJcicFg, "Y")) {
			// 主借款人要查聯徵，還要再判斷如果這戶是負責人，是否有一年內查詢紀錄，如果為否，就不能查聯徵
			// 再判斷負責人要不要查
			if (Util.parseInt(lngeFlag) == Util.parseInt(負責人)) {
				if (Util.equals(chairManJcicFg, "N")) {
					jsonVal.put("JCICQFG", chairManJcicFg);
					jsonVal.put("NOJCICREASON", chairManNoJcicReason);
				}
			}
		} else {
			// 主借款人不用查聯徵，底下所有人都不用查
		}

		// OBU沒有聯徵虛擬統編，就不查該戶
		if (isOBU && Util.equals(Util.trim(jcicTaxNo), "")) {
			if (jsonVal.containsKey("JCICQFG")) {
				if (Util.equals(Util.trim(jsonVal.get("JCICQFG")), "Y")) {
					jsonVal.put("JCICQFG", "N");
					jsonVal.put("NOJCICREASON", "OBU戶查無聯徵虛擬統編");
				}
			}

		}

		return errorMsg;

	}

	boolean isLngeFlagCanQueryEJcic(String lngeFlag) {
		boolean canDo = false;

		// LMS_RPA_RETRIAL_ELLNGTEE_FLAG C,G,L,N 覆審主從債務人查聯徵LNGEFLAG
		String LMS_RPA_RETRIAL_ELLNGTEE_FLAG = Util.trim(lmsService
				.getSysParamDataValue("LMS_RPA_RETRIAL_ELLNGTEE_FLAG"));

		if (Util.notEquals(LMS_RPA_RETRIAL_ELLNGTEE_FLAG, "")) {
			for (String xx : LMS_RPA_RETRIAL_ELLNGTEE_FLAG.split(",")) {
				if (Util.equals(xx, lngeFlag)) {
					canDo = true;
					break;
				}
			}
		}

		return canDo;

	}

}
