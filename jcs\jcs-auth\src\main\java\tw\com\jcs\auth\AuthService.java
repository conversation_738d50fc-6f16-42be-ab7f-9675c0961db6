package tw.com.jcs.auth;

import java.util.List;
import java.util.Set;

/**
 * <pre>
 * 權限檢核
 * </pre>
 * 
 * @since 2022年12月21日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月21日
 *          </ul>
 */
public interface AuthService {

    /**
     * 已棄用
     * 
     * @param userId
     * @param auth
     *            {@link tw.com.jcs.auth.Auth}
     * @return
     */
    @Deprecated
    boolean auth(String userId, Auth auth);

    /**
     * 已棄用
     * 
     * @param userId
     * @param code
     * @param type
     * @param rule
     * @return
     */
    @Deprecated
    boolean auth(String userId, int code, int type, int rule);

    /**
     * 權限類型
     * 
     * @param type
     *            類型
     * @return
     */
    // add by fantasy 2011/08/30
    int authType(String type);

    /**
     * 依照傳入的參數檢核權限
     * 
     * @param pgmDept
     *            指定部門
     * @param roles
     *            角色
     * @param code
     *            代碼
     * @param type
     *            類型
     * @param rule
     *            規則
     * @return
     */
    boolean auth(String pgmDept, Set<String> roles, int code, int type, int rule);

    /**
     * 依照傳入的參數檢核權限
     * 
     * @param pgmDept
     *            指定部門
     * @param roles
     *            角色
     * @param code
     *            代碼
     * @param type
     *            類型
     * @return
     */
    boolean auth(String pgmDept, Set<String> roles, int code, int type);

    /**
     * 依照傳入的參數檢核權限
     * 
     * @param pgmDept
     *            指定部門
     * @param roles
     *            角色
     * @param code
     *            代碼
     * @param type
     *            類型
     * @return
     */
    boolean auth(String pgmDept, Set<String> roles, int code, String type);

    /**
     * 依角色及第一層功能代碼取得子功能代碼所有的文件代號清單
     * 
     * @param roles
     *            角色組
     * @param pgmCode1st
     *            第一層功能代碼
     * @return 文件代號清單
     */
    public List<String> getDocIdList(Set<String> roles, int pgmCode1st);
}
