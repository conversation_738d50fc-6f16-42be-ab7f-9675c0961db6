package tw.com.jcs.flow.model;

/**
 * <pre>
 * 兩個值的配對物件
 * </pre>
 * 
 * <AUTHOR> Software Inc.
 *
 * @param <T1>
 *            first的類型
 * @param <T2>
 *            second的類型
 */
public class Pair<T1, T2> {

    T1 first;
    T2 second;

    /**
     * 取得第一個物件
     * 
     * @return
     */
    public T1 getFirst() {
        return first;
    }

    /**
     * 設定第一個物件
     * 
     * @param first
     */
    public void setFirst(T1 first) {
        this.first = first;
    }

    /**
     * 取得第二個物件
     * 
     * @return
     */
    public T2 getSecond() {
        return second;
    }

    /**
     * 設定第二個物件
     * 
     * @param second
     */
    public void setSecond(T2 second) {
        this.second = second;
    }

    /**
     * constructor
     * 
     * @param first
     * @param second
     */
    public Pair(T1 first, T2 second) {
        this.first = first;
        this.second = second;
    }
}
