/*
 * SearchModeParameter.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
*/
package tw.com.iisi.cap.dao.utils;

import org.apache.commons.lang3.builder.ToStringBuilder;
import javax.persistence.criteria.JoinType;

/**
 * <pre>
 * SearchModeParameter
 * 設定查詢的行為模式和內容
 * </pre>
 * 
 * @since 2011/3/28
 * <AUTHOR>
 * @version $Id$
 * @version
 *          <ul>
 *          <li>2011/3/28,iristu,new
 *          </ul>
 */
public class SearchModeParameter {

    /**
     * 查詢模式
     */
    SearchMode mode;
    /**
     * 欄位名稱
     */
    Object key;
    /**
     * 內容
     */
    Object value;
    /**
     * JOIN方法設定
     */
    private JoinType joinType = JoinType.INNER;
    
    public JoinType getJoinType() { return joinType; }
    
    /**
     * 客製化功能：可傳入JOINTYPE，設定SearchMode的Query時的表單JOIN方式，原先預設是INNER JOIN
     * e.g. new SearchModeParameter(SearchMode.EQUALS, "c121a01a.authUnit", user.getUnitNo(), JoinType.LEFT)
     * @param mode
     * @param key
     * @param value
     * @param joinType
     */
    public SearchModeParameter(SearchMode mode, Object key, Object value,
            JoinType joinType) {
		this(mode, key, value);
		if (joinType != null) {
			this.joinType = joinType;
		}
	}
    
    /**
     * 建構子
     * 
     * @param searchMode
     *            查詢模式
     * @param key
     *            欄位名稱
     * @param value
     *            內容
     */
    public SearchModeParameter(SearchMode searchMode, Object key, Object value) {
        this.mode = searchMode;
        this.key = key;
        this.value = value;
    }

    /**
     * 取得查詢模式
     * 
     * @return
     */
    public SearchMode getMode() {
        return mode;
    }

    /**
     * 取得欄位名稱
     * 
     * @param <K>
     * @return
     */
    @SuppressWarnings("unchecked")
    public <K> K getKey() {
        return (K) key;
    }

    /**
     * 取得內容
     */
    public Object getValue() {
        return value;
    }

    /*
     * 轉換為字串
     * 
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

}
