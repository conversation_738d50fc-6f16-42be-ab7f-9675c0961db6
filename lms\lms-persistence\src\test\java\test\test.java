package test;

import java.math.BigDecimal;
import java.util.Date;

import com.mega.eloan.common.utils.BeanValidator;
import com.mega.eloan.lms.validation.group.Check;

public class test {

	public static void main(String args[]) {
		test ap = new test();
		ap.mp();
	} // end of main

	// main process
	public void mp() {
//		System.out.println("start");
//		List<String> list = Util.getBinaryList(7);
//		System.out.println(list.toString());
//		Collections.reverse(list);
//		System.out.println(list.toString());
//		
//		
//		String b = "005,006,";
//		String[] bs = b.split(",");
//		for (String s : bs){
//			System.out.println("s="+s);
//		}
		
		//Properties pop = MessageBundleScriptCreator
		//.getComponentResource(LMS1505M01Page.class);
		
	
	//	Properties pop = new Properties();
	//	pop.getProperty(name);
		
		//String validate = Util.validateColumnSize(l150m01a, pop, "L150M01a");
		
		
		Person person = new Person();
		person.setName("");
		person.setMin("");
		person.setMax("12345");
		person.setNumberMin(3);
		person.setNumberMax(2);
		BigDecimal d = new BigDecimal("0.01");
		person.setDecimal(d);
		person.setDate(new Date());
		//Locale locale = new Locale("zh_CN");
		
		
		System.out.println(BeanValidator.isValid(person));
		
		
		System.out.println(BeanValidator.getInvalidMsg(person));
		System.out.println("xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx");
		System.out.println(BeanValidator.getInvalidMsg(person,Check.class));
		
		
		
		
		//System.out.println(BeanValidator.getValidMsg(person, Person.class));
		/*
		String str = BeanValidator.toJSON(person);
		
		C130M06 c130m06 = new C130M06();
		System.out.println(BeanValidator.isValid(c130m06));
		System.out.println(BeanValidator.getValidMsg(c130m06, C130M06.class));
		
		System.out.println("str="+str);
		*/
		System.out.println("end");
	}
}
