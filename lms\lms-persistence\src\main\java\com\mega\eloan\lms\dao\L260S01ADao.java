/* 
 * L260S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L260S01A;

/** 貸後管理理財商品檔 **/
public interface L260S01ADao extends IGenericDao<L260S01A> {

	L260S01A findByOid(String oid);
	
	L260S01A findByUniqueKey(String mainId, String custId, String dupNo, String proType, String bankProCode, String accNo);
	
	List<L260S01A> findByMainId(String mainId, boolean notIncDel);
}