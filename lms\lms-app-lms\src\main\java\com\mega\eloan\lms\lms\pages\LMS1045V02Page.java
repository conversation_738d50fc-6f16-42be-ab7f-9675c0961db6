package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * 消金信用評等模型(待覆核)
 * </pre>
 * 
 * @since 2017
 * <AUTHOR>
 * @version <ul>
 *          <li>2017,EL09301,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1045v02")
public class LMS1045V02Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_待覆核);
		
		setJavaScriptVar("noOpenDoc", "N");
		
		// 加上Button
		if(true){
		}
		addToButtonPanel(model, LmsButtonEnum.Filter);

		renderJsI18N(LMS1045V01Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/lms/LMS1045V01Page');");
	}

	// @Override
	// public String[] getJavascriptPath() {
	// return new String[] { "pagejs/lms/LMS1045V01Page.js" };
	// }
}