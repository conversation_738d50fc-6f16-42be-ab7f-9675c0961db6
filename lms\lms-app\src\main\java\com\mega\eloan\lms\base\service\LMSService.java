/*
 * LMSService.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import org.kordamp.json.JSONObject;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.MISRows;
import com.mega.eloan.lms.mfaloan.bean.ELF600;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.C900M01K_LGD;
import com.mega.eloan.lms.model.F101M01A;
import com.mega.eloan.lms.model.L120A01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120M01H;
import com.mega.eloan.lms.model.L120M01L;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.eloan.lms.model.L120S01N;
import com.mega.eloan.lms.model.L120S01O;
import com.mega.eloan.lms.model.L120S01Q;
import com.mega.eloan.lms.model.L120S01R;
import com.mega.eloan.lms.model.L120S01S;
import com.mega.eloan.lms.model.L120S01T;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S04C;
import com.mega.eloan.lms.model.L120S04D;
import com.mega.eloan.lms.model.L120S04E;
import com.mega.eloan.lms.model.L120S05A;
import com.mega.eloan.lms.model.L120S05E;
import com.mega.eloan.lms.model.L120S11A;
import com.mega.eloan.lms.model.L120S11A_LOC;
import com.mega.eloan.lms.model.L120S17A;
import com.mega.eloan.lms.model.L120S23A;
import com.mega.eloan.lms.model.L120S24A;
import com.mega.eloan.lms.model.L120S24B;
import com.mega.eloan.lms.model.L130M01A;
import com.mega.eloan.lms.model.L130M01B;
import com.mega.eloan.lms.model.L130S01A;
import com.mega.eloan.lms.model.L130S01B;
import com.mega.eloan.lms.model.L130S02A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01N;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M01Q;
import com.mega.eloan.lms.model.L140M01S;
import com.mega.eloan.lms.model.L140M01T;
import com.mega.eloan.lms.model.L140M01X;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S05A;
import com.mega.eloan.lms.model.L140S06A;
import com.mega.eloan.lms.model.L140S07A;
import com.mega.eloan.lms.model.L140S09A;
import com.mega.eloan.lms.model.L140S09B;
import com.mega.eloan.lms.model.L140S11A;
import com.mega.eloan.lms.model.L140S12A;
import com.mega.eloan.lms.model.L141M01D;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L250M01A;
import com.mega.sso.model.IBranch;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.ICapService;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * BY 專案共用Service
 * </pre>
 *
 * @since 2012/1/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/13,REX,new
 *          </ul>
 */
/**
 * <AUTHOR>
 * 
 */
public interface LMSService extends ICapService {

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	public String getUserName(String userId);

	/**
	 * 匯率轉換
	 * 
	 * @param brId
	 *            分行代號
	 * @return 海外分行利率資料
	 */
	public BranchRate getBranchRate(String brId);

	/**
	 * 儲存授權檔
	 * 
	 * @param meta
	 *            案件簽報書
	 * @param ownBrId
	 *            被授權單位
	 * @param authType
	 *            傳送類別
	 */
	public void saveL12A01A(L120M01A meta, String ownBrId, String authType);

	/**
	 * 變更額度明細表文件狀態
	 * 
	 * @param meta
	 *            簽報書主檔
	 * @param docStatus
	 *            文件狀態
	 */
	public void resetL140M01A(L120M01A meta, String docStatus);

	/**
	 * (108)第 3230 號
	 * 
	 * @param meta
	 * @param docStatus
	 * @param itemType
	 */
	public void resetL140M01AByItemType(L120M01A meta, String docStatus,
			String itemType);

	/**
	 * 新增近期已收
	 * 
	 * @param meta
	 *            案件簽報書
	 * @param ownBrId
	 *            被授權單位
	 */
	public void saveL000M01A(L120M01A meta, String ownBrId);

	/**
	 * 傳送會簽資料
	 * 
	 * @param meta
	 *            案件簽報書
	 * 
	 */
	public void sendL121M01A(L120M01A meta);

	/**
	 * 核准時要將額度明細表傳送至聯行
	 * 
	 * @param meta
	 *            簽報書主檔
	 * @param l140m01as
	 *            要傳送的額度明細表
	 */
	public void sendToL141M01A(L120M01A meta, List<L140M01A> l140m01as);

	/**
	 * 核准時要上傳ELLNSEEK(ELF461)
	 * 
	 * @param meta
	 *            簽報書主檔
	 * 
	 */
	public void uploadELLNSEEK(L120M01A meta) throws CapException;

	/**
	 * 新增簽章欄
	 * 
	 * @param meta
	 *            簽報書主檔
	 * @param branchType
	 *            單位類型 1. 分行<br/>
	 *            2. 母行/海外總行<br/>
	 *            3. 營運中心<br/>
	 *            4. 授管處<br/>
	 *            5. 徵信承作分行
	 * @param staffJob
	 *            人員職稱
	 */
	public void saveL120M01F(L120M01A meta, String branchType, String staffJob);

	/**
	 * 退回時刪除簽章欄
	 * 
	 * @param mainId
	 *            文件編號
	 * @param branchType
	 *            單位類型 1. 分行<br/>
	 *            2. 母行/海外總行<br/>
	 *            3. 營運中心<br/>
	 *            4. 授管處<br/>
	 *            5. 徵信承作分行
	 * 
	 * @param staffJob
	 *            人員職稱
	 * */

	public void deleteL120M01F(String mainId, String branchType,
			String[] staffJobs);

	/**
	 * 判斷退補件狀態
	 * 
	 * @param modle
	 *            簽報書主檔
	 */
	public void checkBackUnit(L120M01A modle);

	/**
	 * 上傳DW"(授信報案考核表)
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 */
	public void upLoadDW(L120M01A l120m01a);

	/**
	 * 上傳MIS"(簽報書+額度明細表)
	 * 
	 * @param l120m01a
	 * @throws CapException
	 * @throws FlowException
	 */
	public void upLoadMIS(L120M01A l120m01a) throws FlowException, CapException;

	/**
	 * 異常通報發送Email方法
	 * 
	 * @param list
	 * 
	 * @param timing
	 *            發送時機：1:總處批覆 海外單位異常通報表 送至海管處T1 ； 0:原發送時機
	 */
	public void sendEmail(String mainId, L120M01A meta, List<L130S01B> list,
			String timing);

	/**
	 * 設定分行放行時間
	 * 
	 * @param modle
	 *            案件簽報書
	 */
	public void setSentTime(L120M01A modle);

	/**
	 * 判斷覆核主管 與經辦是否相同
	 * 
	 * @param modle
	 *            案件簽報書主檔
	 * @param branchType
	 *            簽章欄單位類型
	 * 
	 * @param staffJob
	 *            簽章欄職稱
	 */
	public void checkAppraiser(L120M01A modle, String branchType,
			String staffJob) throws FlowMessageException;

	/**
	 * 更新預約額度檔
	 * 
	 * @param meta
	 *            簽報書主檔
	 * @param type
	 *            執行的動作 T 呈主管 M 覆核
	 * @param docstatus
	 *            文件狀態
	 */
	public void gfnDB2SetELF442_CNTRNO(L120M01A meta, String type,
			String docstatus);

	/**
	 * 取得匯差容許值
	 * 
	 * @param key
	 * @return
	 */
	String getCurrExchangeRate(String key);

	/**
	 * 新增 核准額度資料檔 MIS.ELF447n
	 * 
	 * @param meta
	 *            簽報書主檔
	 * @param itemType
	 *            額度明細表種類
	 * 
	 * @param nextDocstatus
	 *            簽報書下一個文件狀態
	 * @param nextBranchID
	 *            簽報書下一個編製行
	 */
	public void gfnInsertELF447N(L120M01A meta, String itemType,
			String nextDocstatus, String nextBranchID);

	/**
	 * 上傳ELF447N
	 * 
	 * @param meta
	 *            簽報書
	 * @param l140m01a
	 *            額度明細表
	 * @param areaNo
	 *            該國別風險區域別
	 * @param nextDocstatus
	 *            簽報書下一個文件狀態
	 * @param nextBranchID
	 *            簽報書下一個編製行
	 */
	public void gfnDB2ELF447N(L120M01A meta, L140M01A l140m01a, String areaNo,
			String nextDocstatus, String nextBranchID, boolean upElf447n,
			boolean upElf447x);

	/**
	 * 設定授信報案考核表
	 * 
	 * @param meta
	 *            授信簽報書
	 * @param user
	 *            登入的sso檔
	 */
	public void setL730M01A(L120M01A meta, MegaSSOUserDetails user);

	/**
	 * 
	 * 判斷已核准案件簽報書 的額度明細表的itemtype 為何<br/>
	 * 當簽案行為泰國的分行 其授權外 顯示的要為 泰國額度批覆表
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return 額度明細表=1 額度批覆表=2,泰國額度批覆表=3;
	 */
	public String checkL140M01AItemType(L120M01A l120m01a);

	/**
	 * 
	 * 判斷已核准案件簽報書 的額度明細表的itemtype 為何<br/>
	 * 當簽案行為泰國的分行 其授權外 顯示的要為 泰國額度批覆表
	 * 
	 * @param caseBrId
	 *            簽案行
	 * @param docKind
	 *            授權內 授權外
	 * @param authLvl
	 *            授權內 種類
	 * @return 額度明細表=1 額度批覆表=2,泰國額度批覆表=3;
	 */
	public String checkL140M01AItemType(String caseBrId, String docKind,
			String authLvl);

	/**
	 * 上傳婉卻記錄檔(預設要上傳MIS.LNUNID)
	 * 
	 * @param meta
	 *            簽報書主檔
	 */
	public void upLnunid(L120M01A meta) throws CapException;

	/**
	 * 上傳婉卻記錄檔
	 * 
	 * @param meta
	 *            簽報書主檔
	 */
	public void upLnunid(L120M01A meta, boolean upMis) throws CapException;

	/**
	 * 刪除時間大於這個數字的 檔案
	 * 
	 * @param deletDay
	 *            天數
	 */
	// public void batchDeletTime(int deletDay);

	/**
	 * 檢查授權檔資料是否正確
	 * 
	 * @param meta
	 *            簽報書主檔
	 */
	public void checkL120A01A(L120M01A meta);

	/**
	 * 上傳MIS(只上傳MIS447)
	 * 
	 * @param l141m01ds
	 *            簽章欄
	 */
	public void upLoadMIS447OnlyforL141M01A(L120M01A l120m01a,
			List<L141M01D> l141m01ds);

	/**
	 * 搜尋共用的grid
	 * 
	 * @param clazz
	 * @param search
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * 檢查此custId 為OBU 統編
	 * 
	 * @param custId
	 *            客戶統編
	 * @return Boolean true or false
	 */
	public boolean isObuCustID(String custId);

	/**
	 * 身份證字號檢查程式，身份證字號規則： 字母(ABCDEFGHJKLMNPQRSTUVXYWZIO)對應一組數(10~35)，
	 * 令其十位數為X1，個位數為X2；( 如Ａ：X1=1 , X2=0 )；D表示2~9數字 Y = X1 + 9*X2 + 8*D1 + 7*D2 +
	 * 6*D3 + 5*D4 + 4*D5 + 3*D6 + 2*D7+ 1*D8 + D9 如Y能被10整除，則表示該身份證號碼為正確，否則為錯誤。
	 * 臺北市(A)、臺中市(B)、基隆市(C)、臺南市(D)、高雄市(E)、臺北縣(F)、
	 * 宜蘭縣(G)、桃園縣(H)、嘉義市(I)、新竹縣(J)、苗栗縣(K)、臺中縣(L)、
	 * 南投縣(M)、彰化縣(N)、新竹市(O)、雲林縣(P)、嘉義縣(Q)、臺南縣(R)、
	 * 高雄縣(S)、屏東縣(T)、花蓮縣(U)、臺東縣(V)、金門縣(W)、澎湖縣(X)、 陽明山(Y)、連江縣(Z)
	 * 
	 * @param csutId
	 *            客戶統編
	 * @return true or false
	 */
	public boolean isValidTWPID(String csutId);

	/**
	 * 檢查DBU 公司戶
	 * 
	 * @param custId
	 *            客戶統編
	 * @return true | false
	 */
	public boolean isValidTWBID(String custId);

	/**
	 * 檢查海外個金簽報書是否 可以呈主管
	 * 
	 * @param l120m01as
	 *            被夾帶的個金簽報書主檔
	 * @return <pre>
	 * JSONObject{
	 * success:true | false,
	 *  msg: 錯誤訊息
	 *  }
	 * </pre>
	 */
	public JSONObject isCheckClsOk(List<L120M01A> l120m01as);

	/**
	 * 利用oid取得異常通報表主檔
	 * 
	 * @param oid
	 * @return
	 */
	public L130M01A findL130m01aByOid(String oid);

	/**
	 * 利MainId取得異常通報表主檔
	 * 
	 * @param mainId
	 * @return
	 */
	public L130M01A findL130m01aByMainId(String mainId);

	/**
	 * 利用oid取得異常通報表事項檔
	 * 
	 * @param oid
	 * @return
	 */
	public L130M01B findL130m01bByOid(String oid);

	/**
	 * 利用獨特Key取得異常通報事項檔
	 * 
	 * @param mainId
	 * @param branchKind
	 * @return
	 */
	public L130M01B findL130m01bByUniqueKey(String mainId, String branchKind);

	/**
	 * 利用mainId取得異常通報表事項檔群組
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L130M01B> findL130m01bByMainId(String mainId);

	/**
	 * 依照oid陣列刪除相對應的異常通報表事項檔
	 * 
	 * @param oidArray
	 */
	public void deleteListL130m01b(String[] oidArray);

	/**
	 * 依照異常通報表事項檔List刪除相對應的異常通報表事項檔
	 * 
	 * @param list
	 */
	public void deleteListL130m01b(List<L130M01B> list);

	/**
	 * 儲存異常通報表事項檔與明細檔群組
	 * 
	 * @param l130m01b
	 *            異常通報表事項檔
	 * @param list
	 *            異常通報表明細檔群組
	 */
	public void saveL130m01bListL130s01a(L130M01B l130m01b, List<L130S01A> list);

	/**
	 * 依照oid取得異常通報表明細檔
	 * 
	 * @param oid
	 * @return
	 */
	public L130S01A findL130s01aByOid(String oid);

	/**
	 * 依照mainId取得異常通報表明細檔群組
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L130S01A> findL130s01aByMainId(String mainId);

	/**
	 * 依照mainId與單位種類取得異常通報表明細檔群組
	 * 
	 * @param mainId
	 * @param branchKind
	 * @return
	 */
	public List<L130S01A> findByMainIdAndBranchKind(String mainId,
			String branchKind);

	/**
	 * 依照oid陣列刪除相對應的異常通報表明細檔群組
	 * 
	 * @param oidArray
	 */
	public void deleteListL130s01a(String[] oidArray);

	/**
	 * 將異常通報表明細檔List儲存
	 * 
	 * @param list
	 */
	public void saveListL130s01a(List<L130S01A> list);

	/**
	 * 依照異常通報表明細檔List刪除相對應的異常通報表明細檔群組
	 * 
	 * @param list
	 */
	public void deleteListL130s01a(List<L130S01A> list);

	public void deleteByDeletedTimeL130S02A(String mainId, String seqNo,
			String ctlType, String ctlItem);

	public void saveListL130S02A(List<L130S02A> list);

	public void deleteListL130S02A(List<L130S02A> list);

	/**
	 * 依照oid取得異常通報表參貸行檔
	 * 
	 * @param oid
	 * @return
	 */
	public L130S01B findL130s01bByOid(String oid);

	/**
	 * 依照mainId取得異常通報表參貸行檔群組
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L130S01B> findL130s01bByMainId(String mainId);

	/**
	 * 依照oid陣列刪除相對應的異常通報表參貸行檔群組
	 * 
	 * @param oidArray
	 */
	public void deleteListL130s01b(String[] oidArray);

	/**
	 * 依照異常通報表參貸行List刪除相對應的異常通報表參貸行檔群組
	 * 
	 * @param list
	 */
	public void deleteListL130s01b(List<L130S01B> list);

	/**
	 * 儲存使用者指定異常通報表參貸行List
	 * 
	 * @param list
	 */
	public void saveListL130s01b(List<L130S01B> list);

	/**
	 * 儲存異常通報表主檔與異常通報表參貸行List
	 * 
	 * @param model
	 *            異常通報表主檔
	 * @param list
	 *            異常通報表參貸行List
	 */
	public void saveL130m01aL130s01bs(L130M01A model, List<L130S01B> list);

	/**
	 * 更新ELF412 - 匯入LNFE0851
	 * 
	 * @param branch
	 * @param custid
	 * @param dupno
	 * @param mdFlag
	 * @param mddt
	 * @param process
	 * @return
	 */
	public Map<String, String> getLnfe0851(String branch, String custid,
			String dupno);

	/**
	 * 引進異常通報帳務資料
	 * 
	 * @param brno
	 *            分行代碼
	 * @param custId
	 *            統編
	 * @param dupNo
	 *            重覆序號
	 * @param allCustId
	 *            統編+重覆序號
	 * @return 帳務資料Map
	 */
	public Map<String, Object> getUnNormalData(String brno, String custId,
			String dupNo, String allCustId);

	/**
	 * 取得異常類別
	 * 
	 * @return
	 */
	public Map<String, String> getUnNormalClass();

	/**
	 * 查詢異常通報事項(解除異常通報、停權)
	 * 
	 * @return
	 */
	public Map<String, String> selUnNormalBC(String type);

	/**
	 * 依照事項代碼查詢異常通報事項(解除異常通報、停權)
	 * 
	 * @param seqNo
	 * @return
	 */
	public Map<String, String> selUnNormalBCa(String type, String seqNo);

	/**
	 * 查詢異常通報事項-分行
	 * 
	 * @return
	 */
	public Map<String, String> getUnNormal1();

	/**
	 * 開始查詢異常通報事項-分行
	 * 
	 * @param seqNo
	 * @return
	 */
	public Map<String, String> getUnNormal1a(String seqNo);

	/**
	 * 查詢異常通報事項-營運中心
	 * 
	 * @return
	 */
	public Map<String, String> getUnNormal2();

	/**
	 * 開始查詢異常通報事項-營運中心
	 * 
	 * @param seqNo
	 * @return
	 */
	public Map<String, String> getUnNormal2a(String seqNo);

	/**
	 * 查詢異常通報事項-授管處
	 * 
	 * @return
	 */
	public Map<String, String> getUnNormal3();

	/**
	 * 開始查詢異常通報事項-授管處
	 * 
	 * @param seqNo
	 * @return
	 */
	public Map<String, String> getUnNormal3a(String seqNo);

	/**
	 * 查詢該登入分行異常通報明細表是否被停權
	 * 
	 * @return JSONArray物件 JSONArray包含資訊如下: caseNo -> 停權的簽報書案號 result -> true :
	 *         有被停權
	 */
	public JSONObject selIsstop(String docType);

	/**
	 * 查詢TABLE資訊
	 * 
	 * @param <T>
	 * @param clazz
	 * @param mainId
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public <T extends GenericBean> T findModelByMainId(Class clazz,
			String mainId);

	/**
	 * save
	 * 
	 * @param entity
	 */
	void save(GenericBean... entity);

	/**
	 * delete
	 * 
	 * @param entity
	 */
	void delete(GenericBean... entity);

	/**
	 * 為上傳檔案上deleteTime
	 * 
	 * @param oids
	 */
	public void deleteUploadFile(String[] oids);

	/**
	 * 取得特定所需的分行列表資料(排除會計科 .....)
	 * 
	 * @return
	 */
	public List<IBranch> getBranchList();

	/**
	 * 取得該年該月的日期(第一天或是最後一天 <br/>
	 * 此程式在 setFirstResult==false 時有誤 <br/>
	 * {input:2016-02, output:2016-03-31} <br/>
	 * {input:2016-04, output:2016-05-31}
	 * 
	 * @param year
	 * @param month
	 * @param setFirstResult
	 *            true = 1 false = 31 or 30 or 28
	 * @return
	 */
	@Deprecated
	public Date getDayOfMonth(String year, String month, boolean setFirstResult);

	/**
	 * 取得該年該月的日期(第一天或是最後一天 <br/>
	 * 此程式在 setFirstResult==false 時有誤 <br/>
	 * {input:2016-02, output:2016-03-31} <br/>
	 * {input:2016-04, output:2016-05-31}
	 * 
	 * @param year
	 * @param month
	 * @param setFirstResult
	 *            true = 1 false = 31 or 30 or 28
	 * @return
	 */
	@Deprecated
	public Date getDayOfMonth(int year, int month, boolean setFirstResult);

	/**
	 * 儲存移轉分行資料
	 * 
	 * @param listL140
	 *            額度明細表List
	 * @param listAuth
	 *            簽報書授權檔List
	 * @param listMeta
	 *            簽報書主檔List
	 */
	public void saveMoveUnit(List<L140M01A> listL140, List<L120A01A> listAuth,
			List<L120M01A> listMeta);

	/**
	 * 更新核准時團貸
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @param mainIds
	 *            額度明細表mainId陣列
	 */
	public void setL140M03AGrpCntrNo(L120M01A l120m01a, List<String> mainIds);

	/**
	 * 檢核[個金]簽報書案件資料是否齊全
	 * 
	 * @param l120m01a
	 *            個金簽報書主檔
	 * @return 錯誤訊息
	 */
	public String checkClsCase(L120M01A l120m01a);

	/**
	 * 限額控管
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @return JSONObject
	 * 
	 *         <pre>
	 * errorMsg  : 基本資料檢查錯誤訊息
	 * controlMsg: : 限額控管訊息
	 * </pre>
	 */
	public HashMap<String, String> gfnDB2ChkNeedControl(L140M01A l140m01a);

	/**
	 * 限額控管
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @param showCntrNo
	 *            是否顯示額度序號 true 為呈主管時檢查 false 為 給號時檢查
	 * @return
	 * 
	 *         <pre>
	 * errorMsg  : 基本資料檢查錯誤訊息
	 * controlMsg: : 限額控管訊息
	 * 
	 * </pre>
	 * 
	 */
	public HashMap<String, String> gfnDB2ChkNeedControlByCntrDoc(
			L140M01A l140m01a, Boolean showCntrNo);

	/**
	 * 設定簽報書案號
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 */
	public void setCaseNo(L120M01A l120m01a) throws CapMessageException;

	/**
	 * 設定動審表案號
	 * 
	 * @param c160m01a
	 *            動審表主檔
	 */
	public void setCaseNo(C160M01A c160m01a) throws CapMessageException;

	public Page<Map<String, Object>> getCesPrint(List<String> ces140MainIds,
			List<String> ces120MainIds, ISearch search);

	/**
	 * 房貸評分卡上傳DW
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return 筆數
	 */
	// public int L120UploadDW(L120M01A l120m01a);

	// public int L120UploadClsRatingToMIS(L120M01A l120m01a);

	/**
	 * 房貸評分卡上傳DW For OBU
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return 筆數
	 */
	// public int L120UploadDWForOBU(L120M01A l120m01a);

	public <T> void upDwToServer(MISRows<T> misRows, String TableType);

	/**
	 * 依照分行代碼取得該分行相關資料
	 * 
	 * @param brno
	 *            分行代碼
	 * @return 該分行相關資料，詳細資料如下： unitBrno 分行代碼 unitName 分行名稱 unitAddr 分行地址 unitTel
	 *         分行電話 unitTaxNo 分行統編
	 */
	public Map<String, String> getBrnoData(String brno);

	/**
	 * <pre>
	 * 功能說明：判斷該額度序號的限額資料是否已存在，若存在判斷a-Loan是否已處理
	 *                     (1) 若已處理 - 則將該額度序號項下所有額度複製一份 (日期為新核准日, 變更註記為2.取消)
	 *                     (2) 若未處理 - 則將該額度序號項下所有額度之日期改為新核准日, 變更註記為2.取消)
	 * </pre>
	 * 
	 * @param l140m01a
	 *            額度明細表
	 * @param sDate
	 *            系統時間
	 * @param cntrNo
	 *            額度序號
	 * @param upAS400
	 *            是否上傳as400
	 * @param updater
	 *            更新者
	 * @param BRNID
	 *            目前上傳分行
	 */
	public void gfnQUOTSUBProcess(L140M01A l140m01a, String sDate,
			String cntrNo, Boolean upAS400, String updater, String BRNID);

	/**
	 * <pre>
	 * 團貸建案名稱比對
	 * </pre>
	 * 
	 * @param tBuildName
	 *            建案名稱
	 * @return <pre>
	 * {
	 *            branch:分行代碼
	 *            Result:查詢結果
	 *            isALL:是否為完全比對 Y|N
	 *            }
	 * </pre>
	 */
	public Map<String, String> gfnCheckByBuildName(String tBuildName);

	/**
	 * 
	 * 重新設定 額度明細表關聯<br/>
	 * 利害關係人授信條件對照表明細檔(L120S06B) <br/>
	 * 利害關係人授信條件對照表主檔(L120S06A) <br/>
	 * 因作條件續約變更後，因額度明細表 文件編號已改變會對應不到
	 * 
	 * @param mainId
	 *            新簽報書mainId
	 */
	public void reSetL120S06Table(String mainId);

	/**
	 * 複製案件簽報書(不給號)-營運中心將授權外案件改授權內用
	 * 
	 * @param mainId
	 *            複製的mainId來源
	 * @return newMainId 新文件編號
	 */
	String copyL120m01AllNoNum(String mainId);

	/**
	 * 
	 * @param newMainId
	 * @param mainId
	 * @param copyL140M01E
	 * @param resetL140M01McheckYN
	 * @param copyL140M01Q
	 * @param copyL140S03A
	 * @param copyL140M01T
	 *            是否複製72-2相關資訊註記
	 * @param copyL120S04A
	 *            是否複製空地貸款註記借款人與關係人ROA明細
	 * @param copyL140S05A
	 *            是否複製變更條件項目
	 * @param copyL140S11A
	 *            是否複製敘做條件異動比較
	 *  @param copyL140S12A  
	 *            是否複製其他續做條件追蹤分項
	 *    
	 */
	public void copyL140m01AllByLMS(String newMainId, String mainId,
			Boolean copyL140M01E, Boolean resetL140M01McheckYN,
			Boolean copyL140M01Q, Boolean copyL140S03A, Boolean copyL140M01T,
			Boolean copyL120S04A, Boolean copyL140S05A, Boolean copyL140S11A, Boolean copyL140S12A);

	/**
	 * 複製額度明細表， 將選擇的額度明細表複製後儲存
	 * 
	 * @param newMainId
	 *            複製後的mainId
	 * @param mainId
	 *            複製的mainId來源
	 * @param copyL140M01E
	 *            2012_05_28_建霖 是否要複製 攤貸比率
	 */
	public String saveL140m01bDscr1(String mainId, String pageNum,
			Boolean copyL140M01E);

	/**
	 * 複製額度明細表(國內企金)
	 * 
	 * @param mainId
	 *            案件簽報書mainId
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param newMainId
	 *            新的案件簽報書mainId
	 * @param caseNo
	 *            新的案件簽報書案號
	 * @param caseDate
	 *            新的案件簽報書簽案日期
	 * @param copyL140M01T
	 *            是否複製72-2相關資訊註記
	 * @param copyL140S05A
	 *            是否複製變更條件項目
	 * @param copyL140S11A
	 *            是否複製敘做條件異動比較
	 *  @param copyL140S12A  
	 *            是否複製其他續做條件追蹤分項
	 * @throws CapException
	 */
	public void copyCntrdocByLMS(String mainId, String caseType,
			String newMainId, String caseNo, Date caseDate,
			boolean copyL140M01T, boolean copyL120S04A, boolean copyL140S05A,
			boolean copyL140S11A, boolean copyL140S12A) throws CapException;

	/**
	 * 重新查詢72-2相關資訊(轉入聯行額度明細表，引進簽報書，引進舊案額度明細表，都要再重新查詢一次)
	 * 
	 * @param newLm140m01a
	 * @throws CapException
	 */
	public void reset72_2information(L140M01A newLm140m01a) throws CapException;

	/**
	 * 設定 額度明細表 條件續約變更 金額
	 * 
	 * @param l140m01aOld
	 *            來源
	 * @param newLm140m01a
	 *            目標
	 */
	public void setAmtByLMS(L140M01A l140m01aOld, L140M01A newLm140m01a);

	/**
	 * 複製額度明細表， 將選擇的額度明細表複製後儲存
	 * 
	 * @param newMainId
	 *            複製後的mainId
	 * @param mainId
	 *            複製的mainId來源
	 * @param copyL140M01E
	 *            2012_05_28_建霖 是否要複製 攤貸比率
	 * @param isNeedL140S01A
	 *            是否同時引進借保人
	 * @param isNeedL140M01O
	 *            是否同時引進擔保品
	 * @param isNeedL140M01B
	 *            是否同時引進其他敍做條件
	 * @param changeProperty
	 *            是否將產品種類檔 性質改為不變
	 * @param copyL140S05A
	 *            是否複製變更條件項目
	 * @param copyL140S11A
	 *            是否複製敘做條件異動比較
	 */
	void copyL140m01AllByCLS(String newMainId, String mainId,
			Boolean copyL140M01E, Boolean isNeedL140S01A,
			Boolean isNeedL140M01O, Boolean isNeedL140M01B,
			Boolean changeProperty, Boolean copyL140S05A, Boolean copyL140M01Y,
			Boolean copyL140S11A);

	/**
	 * 複製額度明細表(國內個金)
	 * 
	 * @param mainId
	 *            案件簽報書mainId
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param newMainId
	 *            新的案件簽報書mainId
	 * @param caseNo
	 *            新的案件簽報書案號
	 * @param caseDate
	 *            新的案件簽報書簽案日期
	 * @param changeProperty
	 *            是否變更產品種類檔性質
	 * @param copyL140S05A
	 *            是否複製變更條件項目
	 * @throws CapException
	 */
	public void copyCntrdocByCLS_with_newCaseNoCaseDate(String mainId,
			String caseType, String newMainId, String caseNo, Date caseDate,
			Boolean changeProperty, Boolean copyL140S05A) throws CapException;

	/**
	 * 上傳MisELF431
	 * 
	 */
	public void upELF431(L120M01A l120m01a, L140M01A l140m01a);

	/**
	 * 使用MISbean上傳Mis
	 * 
	 */
	public <T> void upMisToServer(MISRows<T> misRows, String TableType);

	/**
	 * by個金案件上傳
	 * 
	 * @param meta
	 *            簽報書主檔
	 */
	// public void upMisByCls(L120M01A meta);

	/**
	 * 代償資訊組字
	 * 
	 */
	public String getl140s01hData(L140S02A l140s02a);

	/**
	 * 
	 * 取得央行購置地區資訊
	 * 
	 * @param SITE1
	 * @param SITE2
	 * @param SITE3
	 * @param SIT4NO
	 * @return
	 */
	public String l140m01msite3(String SITE1, String SITE2, String SITE3,
			String SIT4NO);

	/**
	 * 
	 * 上傳DW在借款人刪除時
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * 
	 * @param c120m01a_oids
	 *            借款人oid 陣列 當為null 為刪除整筆簽報書
	 * @param reason
	 *            刪除原因
	 */
	/*
	 * public void upDwBydeleCust(L120M01A l120m01a, List<String> c120m01a_oids,
	 * String reason, String reasonOth);
	 */

	/**
	 * 儲存簽章欄by 企金夾帶個金案件
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @param l120m01fs
	 *            簽章欄
	 * @param branchType
	 *            單位類型 1. 分行<br/>
	 *            2. 母行/海外總行<br/>
	 *            3. 營運中心<br/>
	 *            4. 授管處<br/>
	 *            5. 徵信承作分行
	 */
	public void saveL120M01FByCls(L120M01A l120m01a, List<L120M01F> l120m01fs,
			String branchType) throws CapException;

	/**
	 * 提會同步 by 企金夾帶個金案件
	 * 
	 * @param l120m01a
	 * @param hqMeetFlag
	 * @param backAfterSign
	 */
	public void setCLSsendTo(L120M01A l120m01a, String hqMeetFlag,
			boolean backAfterSign);

	JSONObject GetLNFE0851UpFlagInner(String mainId) throws CapException;

	/**
	 * FTP到應收帳款FTP SERVER
	 * 
	 * @param mainId
	 *            額度明細表MAINID
	 * @param cntrno
	 *            額度序號
	 */
	public void sendFtpToArServer(String mainId, String cntrno);

	/**
	 * @param l120m01a
	 * @param custSet
	 * @return
	 */
	public HashMap<String, HashMap<String, String>> getCustIdAndCntrNoMap(
			L120M01A l120m01a, HashSet<String> custSet);

	/**
	 * 檢查在帳務中所有額度序號是否皆已產生
	 * 
	 * @param custIdAndCntrNo
	 * @param showGrpLNF020_CONTRACT
	 * @return
	 */
	public String check_NotLoaded_CntrNo(
			HashMap<String, HashMap<String, String>> custIdAndCntrNo,
			boolean showGrpLNF020_CONTRACT);

	/**
	 * 在 GridViewFilterPanel01 中，增加用 L140M01A.MAINID, L140M01A.CNTRNO 去反查
	 * L120M01A
	 * 
	 * @param filterForm
	 * @return
	 */
	public List<String> getL120M01AMainIdByFilterForm(PageParameters filterForm);

	/**
	 * 取得案件性質確認訊息
	 * 
	 * @param mainId
	 * @param l120m01a
	 * @param l140m01as
	 * @return
	 */
	public String procCaseTypeMsg(String mainId, L120M01A l120m01a,
			List<L140M01A> l140m01as);

	/**
	 * 取得案件性質
	 * 
	 * @param mode
	 *            1額度明細表用 2動審表用
	 * @param mainId
	 *            簽報書MAINID
	 * @param l120m01b
	 * @param l140m01a
	 * @return
	 */
	public Map<String, String> getCaseType(String mode, String mainId,
			L120M01B l120m01b, L140M01A l140m01a);

	/**
	 * 取得案件性質(由動審表呼叫 資料來源為L161S01A)
	 * 
	 * @param mode
	 * @param l161s01a
	 * @param l140m01a
	 * @return
	 */
	public Map<String, String> getCaseType(String mode, L161S01A l161s01a,
			L140M01A l140m01a);

	/**
	 * 取得案件性質主程式
	 * 
	 * @param mode
	 * @param unitCase
	 *            同業聯貸
	 * @param uCntBranch
	 *            帳務管理行
	 * @param unitCase2
	 *            額度明細表-本案是否有同業聯貸額度
	 * @param unitMega
	 *            是否有自行聯貸
	 * @param coKind
	 *            合作業務種類
	 * @param mCntrt
	 *            合作母
	 * @param sCntrt
	 *            合作子
	 * @param l140m01a
	 *            額度明細表
	 * @return
	 */
	public Map<String, String> getCaseTypeInner(String mode, String unitCase,
			String uCntBranch, String unitCase2, String unitMega,
			String coKind, String mCntrt, String sCntrt, L140M01A l140m01a);

	/**
	 * 
	 * @param picType
	 *            :圖片種類 EX:logoShow UtilConstants.RPTPicType.兆豐LOGO =
	 *            "rptPath_megaLogo"
	 * @param defaultPath
	 *            defaultVal EX:"00" codeType rptPath_megaLogo 要設一組預設值的路徑
	 * @param caseBrid
	 *            分行代碼
	 * @return
	 */
	public String getLogoShowPath(String picType, String defaultVal,
			String caseBrid);

	/**
	 * 判斷是否為等級1~4的模型
	 *
	 * @param crdType
	 * @return
	 */
	boolean is4GradeMow(String crdType);

	/**
	 * 取得MOW等級所對應之說明
	 * 
	 * @param prop
	 * @param crdType
	 *            MOW 種類 EX: M1、M2....
	 * @param grade
	 *            MOW 等級
	 * @return
	 */
	public String getMowGradeName(Properties prop, String crdType, String grade);

	/**
	 * 判斷特別的衍生性科目需要計算derivativesNum
	 * 
	 * @param items
	 *            授信科目
	 * @return true | false
	 */
	public Boolean hasDerivateSubject961(String[] items);

	/**
	 * 判斷衍生性科目需要計算derivativesNum
	 * 
	 * @param items
	 *            授信科目
	 * @return true | false
	 */
	public Boolean hasDerivateSubject(String[] items);

	Boolean hasDerivateSubjectZ09(String[] items);

	void importL120s01m(String mainId, PageParameters params)
			throws CapException;

	void saveL120s01mno(L120S01M model, List<L120S01N> listn,
			List<L120S01O> listo);

	void saveL120s01nList(List<L120S01N> list);

	void saveL120s01oList(List<L120S01O> list);

	/**
	 * 判斷衍生性科目對應MIS 的類別
	 * 
	 * @param item
	 *            授信科目
	 * @return true | false
	 */
	public String getDerivateSubjectKind(String item);

	/**
	 * 判斷保證科目需要計算derivativesNum
	 * 
	 * @param items
	 *            授信科目
	 * @return true | false
	 */
	public Boolean hasGuaSubject(String[] items);

	/**
	 * 確認是否為衍生性金融商品
	 * 
	 * @param checkItem
	 *            授信科目
	 * @return true | false
	 */
	public boolean isDerivativesItem(String checkItem);

	/**
	 * 確認是否為顯示風險係數科目
	 * 
	 * @param checkItem
	 *            授信科目
	 * @return true | false
	 */
	public boolean isGenDerivativesNumItem(String checkItem);

	/**
	 * 判斷保證科目是否需要顯示風險係數(所有科目為純保證科目才需要顯示)derivativesNum
	 * 
	 * @param items
	 *            授信科目
	 * @return true | false
	 */
	public Boolean chkGuaNeedDerivatives(String[] items);

	/**
	 * 判斷進出口科目需要計算derivativesNum
	 * 
	 * @param items
	 *            授信科目
	 * @return true | false
	 */
	public Boolean chkIXEXNeedDerivatives(String[] items);

	/**
	 * 判斷是否符合大陸地區預約控管
	 * 
	 * @param l140m01a
	 * @param l140m01q
	 * @return
	 */
	public String check_CN_NeedBooking(L140M01A l140m01a, L140M01Q l140m01q);

	/**
	 * 
	 * 判斷 是否為高價住宅<br/>
	 * 判斷 是否為央行受限戶<br/>
	 * 2014-09-18 檢查 程式修改申請編號 : (103) 第 1877 號 申請內容
	 * 
	 * @param l140m01m
	 */
	public void setType_new_20201208(L140M01M l140m01m, String custId,
			List<String> errList, List<String> showList,
			boolean useExist_isLimitCust, String cntrNo, String property,
			String quantLoan);

	/**
	 * 
	 * 判斷 是否為高價住宅<br/>
	 * 判斷 是否為央行受限戶<br/>
	 * 2014-09-18 檢查 程式修改申請編號 : (103) 第 1877 號 申請內容
	 * 
	 * @param l140m01m
	 */
	public void setType_old(L140M01M l140m01m, String custId,
			List<String> errList, List<String> showList,
			boolean useExist_isLimitCust, String cntrNo, String property,
			String quantLoan);

	public void setType(L140M01M l140m01m, String custId, List<String> errList,
			List<String> showList, L140M01A l140m01a);

	/**
	 * 檢查遠匯、換匯是否比照選擇權簽案 J-103-0202-005 Web e-Loan授信簽案衍生性金融商品遠匯與換匯科目，改以交易額度來簽案。
	 * 交易額度(名目本金*風險係數) </pre>
	 * 
	 * @param
	 * @return boolean
	 */
	boolean chkFxTradeEffect();

	/**
	 * 判斷是否有遠匯、換匯科目
	 * 
	 * @param items
	 *            授信科目
	 * @return true | false
	 */
	boolean hasFxSubject(String[] items);

	/**
	 * 回傳所有衍生性商品名稱
	 * 
	 * @return
	 */
	Map<String, String> getAllDervPeriod();

	/*
	 * void verify_G_Q_model_upDW(C120M01A c120m01a, C120S01A c120s01a, C120S01B
	 * c120s01b, C120S01C c120s01c, C120S01G c120s01g, C120S01Q c120s01q);
	 */

	/*
	 * void verify_C121M01A(C121M01A c121m01a, C121S01A c121s01a, C120M01A
	 * c120m01a, C120S01A c120s01a, C120S01B c120s01b, C120S01C c120s01c,
	 * C120S01E c120s01e, GenericBean c121m01_grade);
	 */

	String checkClsRatingModel(L120M01A model, String l140m01a_mainId);

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 * @throws CapException
	 */
	public Map<String, String> queryOnLine772FlagByCntrno(String custId,
			String dupNo, String cntrNo) throws CapException;

	/**
	 * J-104-0240-001 Web e-Loan授信簽報書與額度明細表增加列示借款戶所屬集團企業代號、名稱與註記。
	 * 取得集團名稱(多判斷有無集團列管註記)
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, String> getGroupNameWithBadFlag(String mainId, String custId,
			String dupNo);

	/**
	 * J-104-0279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
	 * 
	 * @param directFg
	 * @param iGolFlag
	 * @param cnBusKind
	 * @param charCd
	 * @return
	 */
	public String directFgToLoanTarget(String directFg, String iGolFlag,
			String cnBusKind, String charCd);

	/**
	 * J-104-0279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別 將主機ELF506
	 * "YYY             " 轉成 1|2|3
	 * 
	 * @param othCrdType
	 * @return
	 */
	public String formatOthCrdTypeFromElf506(String othCrdType);

	public Map<String, String> call_elf517(String cntrNo);

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
	 * 判斷該分行借款人基本資料之「授信信用風險管理」遵循檢核是否需顯示「海外當地限額辦法」之遵循
	 * 
	 * @param caseBrId
	 * @return
	 */
	public String chkCaseBrIdCountryTypeNeedShow_L120S01M_HasLocalAmt(
			String caseBrId);

	/**
	 * 判斷額度明細表是否有屬於某一國家的額度(檢查額度序號與攤貸分行) J-105-0074-001 Web e-Loan
	 * 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。 l140m01a
	 * targetCountryType 要判斷的分行
	 */
	public boolean hasTargetCountryCntrno(L140M01A l140m01a,
			String targetCountryType);

	/**
	 * 將ELF506 資料轉成 Result
	 * 
	 * @param cntrNo
	 * @param preText
	 *            result欄位前置字
	 * @return
	 */
	public CapAjaxFormResult setResultFromELF506(Map<String, Object> elf506,
			String cntrNo, String preText, CapAjaxFormResult result);

	/**
	 * 用途別=購置不動產，卻不屬72-2的原因[A0#, A0N, A03, B11, B22, C01]
	 */
	public String is722_exItem(L140S02A l140s02a);

	public String get_0024_busCd(String custId, String dupNo);

	/**
	 * 依照國內海外分行判斷ELF506來源
	 * 
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> getELF506ByBranchAndCntrno(String cntrNo);

	public List<Map<String, Object>> getELF506ByBranchAndCustId(String custId,
			String dupNo);

	public List<Map<String, Object>> getELF506ByBranchAndAdcCaseNo(
			String adcCaseNo);

	/**
	 * J-105-0074-001 Web e-Loan
	 * 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
	 * 轉入聯行額度明細表引進簽報書、引進舊案額度明細表時，重新更新額度明細表資料
	 */
	public void resetCntrDocRelateDataWhenCopy(L140M01A l140m01a)
			throws CapException;

	/**
	 * J-110-0507 不動產暨72-2針對都更危老，清空欄位{授信條件是否載明}
	 * 
	 * @param list
	 */
	public void resetL140M01TStatedCheck(List<L140M01T> list);

	/**
	 * J-105-0167-001 Web e-Loan 企金授信案件簽報書第八之3增列集團企業(應予注意集團)有關集團評等之「財務警訊項目資訊」。
	 * 將財務警訊代碼組成文字
	 * 
	 * @param grpFinAlert
	 *            1|2|3
	 * @return
	 */
	public String buildGrpFinAlertStr(String grpFinAlert);

	/**
	 * e-Loan衍生性金融商品dervKind (由 getDerivateSubjectKind 取得 )轉a-Loan LNF07B Trade
	 * Type N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
	 * 
	 * @param dervKind
	 * @return
	 */
	public String convertEloanDervTradeTypeToAloan(String dervKind);

	/**
	 * 檢查是否開集團企業(應予注意集團)有關集團評等之「財務警訊項目資訊」顯示功能 上線時要將COMMON 的 LMS_GrpFinAlert_Show
	 * 設成Y J-105-0167-001 Web e-Loan
	 * 企金授信案件簽報書第八之3增列集團企業(應予注意集團)有關集團評等之「財務警訊項目資訊」。
	 * 
	 * @return
	 * @throws CapException
	 */
	public String chkGrpFinAlertCanShow();

	/**
	 * 共用修改ID
	 * 
	 * @param dao
	 * @param custId
	 * @param dupNo
	 * @param custId2
	 * @param dupNo2
	 * @return
	 * @throws CapException
	 */
	public int updateId(@SuppressWarnings("rawtypes") IGenericDao dao,
			String custId, String dupNo, String custId2, String dupNo2)
			throws CapException;

	/**
	 * 修改私募基金 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 * 
	 * @param l120m01a
	 */
	public void uploadL902Data(L120M01A l120m01a, String docStatus);

	/**
	 * J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 * 
	 * @param busCode
	 * @return
	 * @throws CapException
	 */
	public String applyIsStartUpInner(Date caseDate, String busCode)
			throws CapException;

	/**
	 * J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 */
	public String chkIsStartUpStatusError(L120M01A model, L140M01A l140m01a,
			boolean chkBusCode) throws CapException;

	/**
	 * 取得後台管理->系統設定維護->PARAMVALUE
	 * 
	 * @param param
	 * @return
	 */
	public String getSysParamDataValue(String param);

	/**
	 * J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位 將CODETYPE回傳前端JS
	 * 
	 * @param codeType
	 * @return
	 */
	public Map<String, String> getCodeType(String codeType);

	/**
	 * J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位 國家代碼轉為國家說明 TW|US|JP =>
	 * 台灣,美國,日本
	 */
	public String convertChkBoxValueToString(String badCountry);

	/**
	 * J-106-0085-001 Web e-Loan企金授信新增主要還款來源國等相關欄位
	 * 判斷簽報書項下所有額度明細表欄位「是否屬未核配額度國家」或「是否屬凍結額度國家」有勾選為Y者 ---為了簽報書檢核與列印---
	 */
	public Map<String, String> getL140m01asPaySourceCountryFlag(
			L120M01A l120m01a, String custId, String dupNo);

	public void updCustId(L120M01A l120m01a, String orgCustId, String orgDupNo,
			String newCustId, String newDupNo, boolean useNew0024Name,
			String newCustName);

	/**
	 * 取得模擬動審資料
	 * 
	 * @param srcMainId
	 * @param cntrNo
	 * @return
	 */
	List<L250M01A> getSimuTrialDataBeforePay(String srcMainId, String cntrNo);

	/**
	 * 判斷衍生性金融商品科目交易目的是否為避險 J-106-0232-001 Web
	 * e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
	 * 
	 * @param l140m01a
	 *            額度明細表
	 * @return 報案類別
	 */
	public String getDerivateSubjectHedgeKinde(L140M01A l140m01a);

	/**
	 * J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
	 * 
	 * @param l120s05a
	 * @return
	 */
	public boolean isShowGrpRateData(L120S05A l120s05a);

	/**
	 * J-107-0087-001 Web
	 * e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
	 * 
	 * @param l120s05a
	 * @return
	 */
	public boolean isShowGrpRateData(String grpNo, Date cycMn, String grpGrade,
			String badFg, String grpYear);

	/**
	 * J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知分行與授信行銷處。
	 * 
	 * @param l120m01a
	 * @return
	 */
	public String generateProdG1Html(L120M01A l120m01a);

	/**
	 * 簽報發信件通知功能 J-106-0246-002 Web
	 * e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
	 */
	public void notifyByMailForG1To940(FlowInstance instance, String status);

	/**
	 * J-107-0027-001 Web e-Loan企金授信額度明細表增列新創重點產業細目選項
	 */
	public Map<String, String> getMisIsStartUpAndItwCode(String busCode)
			throws CapException;

	String applyIsCoreBuss(Date caseDate, String busCode) throws CapException;

	/**
	 * J-107-0027-001 Web e-Loan企金授信額度明細表增列新創重點產業細目選項
	 */
	public Map<String, String> applyIsStartUpInnerWithMap(Date caseDate,
			String busCode) throws CapException;

	/**
	 * J-107-0063 Web eLoan海外授信管理系統總處批覆後發送異常通報批覆書Mail通知海管處
	 */
	public OutputStream genlms1205r27(PageParameters params, String mainId)
			throws FileNotFoundException, ReportException, IOException,
			CapException, Exception;

	/**
	 * J-107-0087-001 Web
	 * e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
	 * 
	 * @param grpYY
	 *            三位民國年 GRPDTL或四位西元年
	 * @param isGrpYyEmptyIsOld
	 *            無評等年度時是 true:沒評等年度時當做2017年以前舊評等 false:沒評等年度時，檢查系統參數是否已經啟用新集團評等
	 * @return
	 */
	public boolean isNewGrpGrade(String grpYY, boolean chkNew);

	/**
	 * J-107-0087-001 Web
	 * e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
	 * 
	 * @param isNewGrpGrade
	 * @return
	 */
	public String getGrpNoGrade(boolean isNewGrpGrade);

	/**
	 * J-107-0087-001 Web
	 * e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
	 * 
	 * @param grpSize
	 * @param grpLevel
	 * @return
	 */
	public String getGrpSizeLvlShow(String grpSize, String grpLevel);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public List<L140M01S> findL140m01sByMainIdType(String mainId, String type);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public void deleteL140m01sAll(String mainId, String type);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public void saveL140m01sList(List<L140M01S> list);

	String checkCaseIs72_2(L140M01A meta, boolean isPrevious);

	Map<String, String> queryOnLine772FlagByCntrnoSecond(L140M01A l140m01a)
			throws CapException;

	/**
	 * 檢核都更危老案件母戶資料是否存在
	 * 
	 * @param l140m01a
	 * @throws CapMessageException
	 */
	void check722McntrNo(L140M01A l140m01a) throws CapMessageException;

	/**
	 * 發送Email
	 */
	public void sendCommonEmail(GenericBean model, String timing);

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * @param cntrNo
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @param tabFormMainId
	 * @return
	 * @throws CapMessageException
	 */
	public Map<String, String> applyClearLandRoa(String typCd, String brNo,
			GenericBean mainEntity, GenericBean subEntity, String cntrNo,
			String custId, String dupNo, String custName, String queryDateS,
			String queryDateE) throws CapException;

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * @param adoptfg
	 * @param ctlType
	 * @param firstChange
	 * @param cstDate
	 * @param rateAdd
	 * @param custRoa
	 * @param relRoa
	 * @return
	 * @throws CapMessageException
	 */
	public String checkIsLegal(String adoptfg, String ctlType,
			boolean firstChange, boolean cstDate, BigDecimal rateAdd,
			BigDecimal custRoa, BigDecimal relRoa) throws CapMessageException;

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * @param mainId
	 * @param uid
	 * @return
	 */
	public L161S01A findL161s01aByMainIdUid(String mainId, String uid);

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * @param elf600
	 * @return
	 */
	public String isClearLandEffective(ELF600 elf600);

	/**
	 * J-108-0143_05097_B1001 Web e-Loan國內外企金額度明細表簽報性質新作時加註(新客戶往來原有客戶往來)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public String applyIsNewCust(String custId, String dupNo);

	/**
	 * J-108-0116 共同行銷
	 * 
	 * @param meta
	 */
	public void uploadL180R46A(L120M01A meta, L140M01A l140m01a);

	/**
	 * J-108-0116 共同行銷 查詢共同行銷窗口
	 */
	public Map<String, String> querySynBankOfCsTypes(String FINKIND,
			String FINCODE) throws CapException;

	public C160M01B findC160m01bByMainIdUid(String mainId, String uid);

	/**
	 * 個金徵信作業引進簽報書
	 * 
	 * @param targetMainId
	 * @param originMainIds
	 * @throws CapException
	 * @throws IOException
	 */
	void copyC101toC120(String targetMainId, String[] originMainIds)
			throws CapException, IOException;
	
	void copyC101toC101_forDoubleTrack(String targetMainId, String[] originMainIds)
			throws CapException, IOException;

	/**
	 * 刪除企金簽報書引進的全部消金評等資料
	 * 
	 * @param mainId
	 */
	void delteC120CustByMainId(String mainId);

	/**
	 * 取得簽書報引進的消金評等資料
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	C120M01A findc120m01aByUniqueKey(String mainId, String custId, String dupNo);

	/**
	 * J-108-0283 變更條件Condition Change 看額度序號第四碼 若1代表DBU，幣別為TWD
	 * 若為4，代表為OBU，本位幣為USD 若為5，代表海外分行，幣別依照海外分行顯示各自的本位幣，如香港為HKD
	 */
	public String getLocalCurrByCntrNo(String cntrNo);

	/**
	 * J-108-0283 變更條件Condition Change 組成上傳ELF447N文字
	 */
	public String getCondChg(L140S05A l140s05a) throws CapMessageException;

	public void deleteL140S05A(String mainId);

	public void chkL140S05A(L140S05A l140s05a) throws CapMessageException;

	/**
	 * J-108-0303 連鎖店Chain store 上傳控制檔 L140M01V
	 */
	public void uploadL140M01V(L140M01A l140m01a);

	public String chkL140M01W(L120M01A l120m01a, L140M01A l140m01a,
			List<L140M01C> l140m01cs, String unitType, String projClass,
			Properties prop) throws CapMessageException;

	public boolean chkChainStore(String cntrNo, String chainStore);

	/**
	 * J-108-0293 Web e-Loan 未依銀行內部規定 internal regulations
	 */
	public String getElf447nIntReg(List<L140S06A> l140s06as);

	public String getIntRegStr(List<L140S06A> l140s06as);

	public L140M01X findL140M01X(String mainId, String custId, String dupNo);

	public L140M01X getL140M01XValue(String mainId, String custId, String dupNo);

	public String checkHighPriceCheckList(String l140m01aMainId);

	public String checkHighPriceCheckListWhenSave(L140M01X l140m01x,
			boolean isHighHouse, boolean isNotSameOrCancel);

	public List<L140S01A> getCoLenderOfPersonalFinance(String mainId,
			String Identity);

	public void deleteCheckListForHighPricedHousingLoan(String mainId,
			String custId, String dupNo) throws CapMessageException;

	public void saveCheckListForHighPricedHousingLoan(String mainId,
			String custId, String dupNo, String checkListForm);

	public List<Map<String, Object>> queryCheckListOfHighPricedHousingLoan(
			String mainId, String custId, String dupNo);

	public void deleteL140M01X(List<L140M01X> l140m01xList);

	public List<Map<String, String>> getCoLenderForCorporateOrPersonalFinance(
			int docType, String mainId);

	public Map<String, Object> chkMainBiz(String mCntrNo, String custId,
			String dupNo, String type);

	/**
	 * 複製案件簽報書(不給號)-已覆核案件退回
	 * 
	 * J-107-0390_05097_B1001 分行權限之授信案件若於覆核後欲修改,得授權主管得退回至編製中
	 * 
	 * @param mainId
	 *            複製的mainId來源
	 * @return newMainId 新文件編號
	 */
	public String copyL120m01AllNoNum_backApprove(String mainId);

	/**
	 * J-107-0390_05097_B1001 分行權限之授信案件若於覆核後欲修改,得授權主管得退回至編製中
	 * 
	 * @param listOid
	 * @param sign
	 * @param caseType
	 * @return
	 */
	public String backApprove_l120m01a(String listOid, String sign,
			String caseType) throws CapException;

	/**
	 * J-107-0390_05097_B1001 分行權限之授信案件若於覆核後欲修改,得授權主管得退回至編製中
	 */
	public boolean backApprove_checkCanDo(L120M01A l120m01a);

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * @param model
	 * @param chkl140m01as
	 * @param passEmptyCaseLvl
	 * @return
	 */
	public String chkCaseLvlWithCMSStock(L120M01A model,
			List<L140M01A> chkl140m01as, boolean passEmptyCaseLvl);

	/**
	 * J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
	 * 
	 * 判斷借款人基本資料要不要顯示有無依當地法規所規範之關係人(含實質關係人)授信
	 * 
	 * @return
	 */
	public boolean needShowLocalRlt(String QueryBrId);

	/**
	 * J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 判斷是否需要輸入減收利率
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean needRescueRate(String rescueItem);

	/**
	 * J-109-0077_05097_B1005 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 判斷是否需要輸入合併申請紓困方案
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean needRescueItemSub(String rescueItem);

	/**
	 * J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 判斷是否需要顯示減收利率
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemOldCase(String rescueItem);

	/**
	 * J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 判斷是否需要顯示減收利率
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemRescueRate(String rescueItem, String rescueItemSub);

	/**
	 * J-109-0077_05097_B1010 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 判斷是否需要顯示本案是否屬小規模營業人簡易申貸方案
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean needShowRescueSmallBuss();

	/**
	 * J-109-0077_05097_B1014 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 檢核紓困貸款類別資料是否正確
	 * 
	 * 現請額度與現請幣別
	 * 
	 * @param rescueItem
	 * @return
	 */
	public String chkRescueItemCurrentApply(String rescueItem,
			String currentApplyCurr, BigDecimal currentApplyAmt);

	/**
	 * J-109-0077_05097_B1015 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 判斷是否需要現有員工人數
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemNeedEmpCount(String rescueItem,
			String rescueItemSub);

	/**
	 * J-109-0077_05097_B1015 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 檢核純紓困案不用檢核婉卻不得授權內簽報
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemCanPassReject(String rescueItem,
			String rescueItemSub);

	/**
	 * J-109-0077_05097_B1015 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 檢核純紓困案不用檢核婉卻不得授權內簽報
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemCanPassAbnormal(String rescueItem,
			String rescueItemSub);

	/**
	 * J-109-0077_05097_B1017 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 檢核減收利率(固定利率)應大於0
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemRescueRateNotZero(String rescueItem,
			String rescueItemSub);

	/**
	 * J-109-0077_05097_B1017 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 檢核減收或補貼利率不得超過規定
	 * 
	 * 現請額度與現請幣別
	 * 
	 * @param rescueItem
	 * @return
	 */
	public String chkRescueItemRescueRateLess(String rescueItem,
			String rescueItemSub, BigDecimal rescueRate);

	/**
	 * J-109-0077_05097_B1018 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 判斷是否需要掛件文號
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemNeedRescueNo(String rescueItem,
			String rescueItemSub);

	/**
	 * J-109-0077_05097_B1019 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 檢核紓困貸款類別利率條件不得有扣稅負擔
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemNoRateTax(String rescueItem, String rescueItemSub);

	public boolean isNeedChkIsRescueSameL140m01a(String rescueItem);

	public boolean isNeedChkResueItemSameL140m01a(String rescueItem);

	public boolean isNeedChkIsCbRefinSameL140m01a(String rescueItem);

	public boolean isNeedChkHeadItem1SameL140m01a(String rescueItem);

	/**
	 * J-109-0077_05097_B1020 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 檢核紓困貸款類別要與額度明細表一致
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isRescueSmallBussOnlyCaseC(L160M01A l160m01a);

	public boolean isRescueOnlyCaseF(L160M01A l160m01a);

	/**
	 * 疫後振興
	 * 
	 * 判斷是否為疫後振興方案
	 */
	public boolean isResueItemCaseF(String rescueItem);

	/**
	 * 是否為0403花蓮地震融資保證專案
	 *
	 * @param rescueItem
	 * @return
	 */
    boolean isResueItemCaseJ(String rescueItem);

	/**
	 * 是否為中小微政策性貸款
	 *
	 * @param rescueItem
	 * @return
	 */
	boolean isResueItemCaseL(String rescueItem);

	/**
	 * 疫後振興
	 * 
	 * 判斷是否需簡易評分表
	 */
	public Map<String, Boolean> checkCaseFNeedSimpleScoreCard(L120M01A l120m01a);

	public boolean checkNeedContract(String mainId);

	/**
	 * J-109-0371_05097_B1002 簡化青年創業及啟動金貸款簽報書簽案流程
	 * 
	 * @param mainId
	 * @return
	 */
	public boolean checkNeedContract_lnType61(L120M01A l120m01a);

	/**
	 * J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean hidePanelbyCaseType(L120M01A l120m01a);

	public boolean hidePanelbyCaseTypeF(L120M01A l120m01a);

	/**
	 * J-113-0251 符合0403 花蓮J03 信用保證成數10成者，採用簡易評分表及簡化申貸程序
	 *
	 * @param l160m01a
	 * @return
	 */
	boolean isRescueOnlyCaseJ03HeadItem1(L160M01A l160m01a);

	/**
	 * J-109-0077_05097_B1020 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * 檢核紓困貸款類別要與額度明細表一致
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isRescueSmallBussOnlyCaseC(L120M01A l120m01a);

	public boolean isRescueOnlyCaseF(L120M01A l120m01a);

	public List<L120M01E> findL120m01eByMainId(String mainId);

	/**
	 * J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * 利率不得低於規定
	 * 
	 * 利率幣別
	 * 
	 * @param rescueItem
	 * @return
	 */
	public String chkReviveRateMatchStd(List<L140M01N> l140m01ns);

	/**
	 * J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * 檢核兆元振興方案檢核大陸地區不得敘做
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isReviveNotCN();

	/**
	 * J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * 檢核兆元振興方案額度性質續約不得敘做
	 * 
	 * @param rescueItem
	 * @return
	 */
	public String chkRevivePropertyIsLegal(String property);

	/**
	 * J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * @param l140m01a
	 * @return
	 * @throws CapException
	 */
	public boolean isReviveDefaultY(L140M01A l140m01a, String unitNo);

	/**
	 * J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * 檢核兆元振興方案額度性質續約不得敘做
	 * 
	 * @param rescueItem
	 * @return
	 */
	public String chkReviveCurrIsLegal(L140M01A l140m01a, String forRptUse);

	/**
	 * J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * 檢核兆元振興方案全屬排除科目不得適用
	 * 
	 * @param rescueItem
	 * @return
	 */
	public String chkReviveSubjectIsLegal(L140M01A l140m01a);

	/**
	 * J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * 檢核兆元振興方案額度性質不得適用
	 * 
	 * @param rescueItem
	 * @return
	 */
	public String chkRevivePropertyIsLegal(L140M01A l140m01a);

	/**
	 * J-109-0226 Web e-loan 建案餘屋及貸款資料控制管理
	 * 
	 * 檢核建案餘屋及貸款資料正確性
	 * 
	 * @param rescueItem
	 * @return
	 */
	public String checkFinancingDataForUnsoldHouseInFinishedConstruction(
			boolean isNotSameOrCancel, String property, L140M01M l140m01m,
			String docKind, String cntrNo);

	public String checkToSupervisorForUnsoldHouseMortgageInfo(
			List<L140M01A> l140m01a_list, Properties l140m01mProp);

	/**
	 * 判斷該筆兆元方案是否計績
	 * 
	 * @param l140m01a
	 * @param l120m01a
	 * @param busCodProperty
	 *            : FOR LmsBatchCommonServiceImpl產生L180R54A用，預設為空白
	 * @param needChkReUse
	 *            : FOR LmsBatchCommonServiceImpl產生L180R54A用，預設為TRUE
	 * @return
	 */
	public String chkReviveConformReport(L140M01A l140m01a, L120M01A l120m01a,
			String busCodProperty, boolean needChkReUse);

	public String converDocstatus(String docstatus);

	/**
	 * J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構
	 * 取得外部信評相關欄位下拉選單
	 */
	public CapAjaxFormResult getCrdSelect();

	/**
	 * J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構
	 * 取得主權國家信評之對應風險權數
	 */
	public BigDecimal getCrdGradeToRskRatio(PageParameters params);

	/**
	 * J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構 取得主權國家之信評
	 */
	public Map<String, Object> queryCountryCrd(String country);

	/**
	 * J-GGG-XXXX
	 * 
	 * 檢查異常通報授權是否正確
	 */
	public String chkAbnormalDocKind(String docType, String docKind,
			String docCode, String authLvl, String caseType, String caseBrId);

	/**
	 * J-GGG-XXXX
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean hideAbnormalPanelbyCaseType(L120M01A l120m01a);

	/**
	 * J-GGG-XXXX
	 * 
	 * @param l120m01a
	 * @return
	 */
	public List<L130S01A> findL130s01aInsertData(String mainId);

	/**
	 * J-GGG-XXXX
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean isAbnormalAuthLvlAreaSign(L120M01A l120m01a);

	/**
	 * J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
	 * 
	 * 判斷借款人僅有央行C方案
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public String[] getOnlySmallBussCaseC(String custId, String dupNo);

	/**
	 * 取得核准文號
	 * 
	 * @param meta
	 *            案件簽報書
	 * @return 核准文號
	 * @throws CapMessageException
	 */
	public String getAreaSignNo(L120M01A meta) throws CapMessageException;

	/**
	 * 取得授審處核准文號
	 * 
	 * J-112-0021_05097_B1001 Web e-Loan企金授信異常通報案件結果副知徵信單位
	 * 
	 * @param meta
	 *            案件簽報書
	 * @return 核准文號
	 * @throws CapMessageException
	 */
	public String getHeadSignNo(L120M01A meta) throws CapMessageException;

	/**
	 * 上傳結構化利率MIS.ELF503
	 * 
	 * 
	 * J-109-0202_05097_B1001 Web e-Loan利費率資料提前至授信案件經核定後逕行寫入
	 * 
	 * @param BRNID
	 *            上傳分行
	 * @param l160m01a
	 *            動審表
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01a
	 *            簽報書
	 * @param ELF503_UPDATER
	 *            上傳者
	 * @param sDate
	 *            目前上傳時間
	 * @param dataList
	 *            MIS OBJECT[]
	 * @param cntrNo
	 *            額度序號
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重複序號
	 * @throws CapException
	 */
	public void uploadELF503(String BRNID, L140M01A l140m01a,
			L120M01A l120m01a, String ELF503_UPDATER, String sDate,
			List<Object[]> dataList, List<Object[]> elf503DeleteList,
			String cntrNo, String custId, String dupNo) throws CapException;

	/**
	 * 呈現上傳的值
	 * 
	 * @param dataList
	 */
	public void showDataListValue(List<Object[]> dataList);

	/**
	 * J-109-0309_05097_B1001 Web e-Loan企金授信觀光局利息補貼額度控管及新增營業所在地欄位
	 * 
	 * 判斷是否需要顯示觀光局資料
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemNeedTourism(String rescueItem);

	/**
	 * J-109-0239_05097_B1001 Web
	 * e-Loane-Loan授信管理系統案件簽報書之額度明細表新增「特殊融資或不動產ADC融資暴險註記」
	 * 
	 * 判斷是否要顯示本案是否屬特殊融資暴險
	 * 
	 * @param l120m01a
	 * @param l140m01a
	 * @return
	 * @throws CapException
	 */
	public boolean needShowIsSpecialFinRisk(L120M01A l120m01a, L140M01A l140m01a)
			throws CapException;

	/**
	 * 
	 * 判斷若是紓困有補貼利息,請當天須做L505-39減息金額上限建檔
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemNeedL505_39(String rescueItem);

	public L140S07A findL140s07aByOid(String oid);

	public void deleteL140s07a(String oid);

	public List<L140S07A> findL140s07aByMainId(String mainId);

	public L140S07A findMaxSeqNumByMainId(String mainId);

	public L140S09A findL140s09aByOid(String oid);

	public List<L140S09A> findL140s09aByMainId(String mainId);

	public int findMaxSeqNumByMainId(Class clazz, String mainId, String bizCat,
			Integer bizCatId);

	public int findMaxBizCatSeqNumByMainId(Class clazz, String mainId);

	public int findMaxBizCatIdByMainIdAndBizCat(Class clazz, String mainId,
			String bizCat);

	public L140S09B findL140s09bByOid(String oid);

	public List<L140S09B> findL140s09bByMainId(String mainId);

	public List<L140S09A> findL140s09a(String mainId, String loanTPs,
			String bizCat, String bizItem);

	public List<L140S09A> findL140s09aByBizCat(String mainId, String bizCat,
			Integer bizCatId);

	void saveL140s09aList(List<L140S09A> list);

	void saveL140s09bList(List<L140S09B> list);

	public String getContCodeType(String bizCat, String bizItem);

	public void deleteL140s09as(List<L140S09A> list);

	public void deleteL140s09bs(List<L140S09B> list);

	public boolean changeBizCatSeqNum(String mainId, String bizCat,
			boolean upOrDown, Integer bizCatId);

	public boolean changeSeqNumL140S09A(L140S09A l140s09a, boolean upOrDown);

	public boolean changeSeqNumL140S09B(L140S09B l140s09b, boolean upOrDown);

	public boolean resetL140S09AAllSeqNum(String mainId);

	public boolean resetL140S09ABizCatSeqNum(String mainId);

	public boolean resetL140S09BAllSeqNum(String mainId);

	public String getL140s09Str(String mainId, Properties pop)
			throws CapMessageException;

	public String getL140S09bCont(L140S09B l140s09b, Properties pop)
			throws CapMessageException;

	public HashMap<String, String[]> getL140S09bMap();

	public L140S11A findL140s11aByOid(String oid);

	public List<L140S11A> findL140s11aListByMainId(String mainId);

	public L140S11A findL140s11aMaxSeqNumByMainId(String mainId);

	public void saveL140s11aList(List<L140S11A> list);

	public void deleteL140s11as(List<L140S11A> list);

	public boolean resetL140s11aAllSeqNum(String mainId);

	/**
	 * 敘做條件異動情形 > 分項表格向上、下設定序號
	 * 
	 * @param model
	 *            L140S11A
	 * @param upOrDown
	 *            向上=true 向下=false
	 * @return
	 */
	boolean changeL140s11aSeqNum(L140S11A l140s11a, boolean upOrDown);

	public String getL140s11aStr(String mainId, Properties pop);

	public String getL140s11aStrForDoc(List<L140S11A> list,
			Map<String, Object[]> cntrNoMap);

	public String getL120s04aMemoStr(String mainId);

	public String plusL140M01BitemDscr4(String mainId, String data);

	public boolean getL140S09aIsPrint(String bizCat, String bizItem);

	public String getL140S09bCont_single(String divId, L140S09B l140s09b,
			Properties pop) throws CapMessageException;

	/**
	 * J-110-0372 跨科目之通用條件
	 */
	public boolean isCommonBizCat(String bizCat);

	/**
	 * J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
	 * 
	 * @param l120s05a
	 * @return
	 */
	public boolean isShowGrpRateData(L120S05E l120s05e);

	public void insertOrUpdateELF517ForUnsoldHouseFinanceingData(
			String documentNo, L140M01M l140m01m, boolean hasElf517,
			String cntrNo);

	public boolean isOpenUnsoldHouseLoanInfoFunction();

	/**
	 * 取得額度明細表產品類別
	 * 
	 * J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
	 * 
	 * @param l140m01a
	 * @return
	 */
	public Map<String, String> getGenL140m01aCaseType(L140M01A l140m01a);

	/**
	 * J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
	 * 
	 * 檢核簽報書額度明細表是否皆為青年創業及啟動金貸款
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isOnlyLnType61(L120M01A l120m01a);

	/**
	 * J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean hidePanelbyCaseType_lnType61(L120M01A l120m01a);

	/**
	 * J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
	 * 
	 * 檢核紓困貸款類別要與額度明細表一致
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isOnlyLnType61(L160M01A l160m01a);

	// J-109-0370 相關評估改版
	Page<Map<String, Object>> findListbyL120S01Aseq(String tableName,
			String mainId);

	L120S01Q findL120s01qByOid(String oid);

	List<L120S01Q> findL120s01qListByMainId(String mainId);

	L120S01Q findL120s01qByMainIdCustIdDupNo(String mainId, String custId,
			String dupNo);

	void saveL120s01qList(List<L120S01Q> list);

	void deleteL120s01qList(List<L120S01Q> list);

	List<L120S01T> findL120s01tListByMainId(String mainId);

	/**
	 * J-111-0220 高齡客戶關懷檢核表-畫面填表新增主檔+明細檔資料
	 * 
	 * @param oid
	 * @param mainId
	 * @param versionDate
	 * @param formL1205s07
	 * @param pop
	 * @param isCls
	 */
	void saveL120s01rAndL120s01s(String oid, String mainId, String versionDate,
			String formL1205s07, Properties pop, boolean isCls)
			throws CapMessageException;

	/**
	 * J-111-0220 高齡客戶關懷檢核表-find主檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S01R findL120s01rByOid(String oid);

	/**
	 * J-111-0220 高齡客戶關懷檢核表-find主檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	L120S01R findL120s01rByMainIdAndCustId(String mainId, String custId,
			String dupNo);

	/**
	 * J-111-0220 高齡客戶關懷檢核表-刪除主檔+明細
	 * 
	 * @param oids
	 */
	void deleteL120s01r(String[] oids);

	/**
	 * J-111-0220 高齡客戶關懷檢核表-find明細檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L120S01S> findL120s01sByRefOid(String RefOid);

	/**
	 * 企金專用 J-111-0220 產生相關保證人清單
	 * 
	 * @param mainId
	 * @param search
	 * @return
	 */
	Page<Map<String, Object>> findL120s01rCustChoose(String mainId,
			ISearch search);

	/**
	 * 企金專用 J-111-0220 高齡客戶關懷檢核表-產生主檔
	 * 
	 * @param mainId
	 */
	void genL120s01r(String mainId, String[] chooseCustArr, String versionDate);

	/**
	 * 消金專用 J-111-0220 高齡客戶關懷檢核表-產生主檔
	 * 
	 * @param mainId
	 */
	void genL120s01rForCls(String mainId, String versionDate);

	/**
	 * 消金專用 J-111-0220 高齡客戶關懷檢核表- 產生借款人超過65歲清單
	 * 
	 * @param mainId
	 */
	Map<String, String> findIsAge65ForCls(String mainId);

	/**
	 * J-111-0454 產生風險權數主檔
	 * 
	 * @param mainId
	 */
	String genL120s24a(String mainId, String versionDate, Properties pop);

	/**
	 * J-111-0454 find L120S24A
	 * 
	 * @param oid
	 */
	L120S24A findL120s24aByOid(String oid);

	/**
	 * J-111-0454 find L120S24A
	 * 
	 * @param oid
	 */
	L120S24A findL120s24aByRefMainIdAndCntrNo(String refMainId, String cntrNo);

	/**
	 * J-111-0454 儲存L120S24A主檔
	 */
	void saveL120s24a(String oid, String mainId, String l120S24ADetail,
			Properties popPanel) throws CapMessageException;

	/**
	 * J-111-0454 刪除L120S24A主檔
	 */
	void deleteL120s24a(String[] oids);

	/**
	 * J-111-0454 寫回額度明細表
	 */
	void writeBackToL140m01a(String mainId, String[] oids);

	/**
	 * J-111-0454 引進匯率
	 */
	Map<String, String> importRateLoanToLoc_s24a(String oid);

	/**
	 * J-111-0454 重新引進外部評等 type:1 -> 借款人本人 type:2 -> 畫面輸入的連帶保證人
	 */
	String reImportCrdGrade_s24a(String mainId, String oid, String custId,
			String dupNo, String type, Properties pop);

	/**
	 * J-111-0454 計算抵減前風險權數
	 */
	Map<String, String> calBeforeDeductRW_s24a(String oid,
			String bowrrowerClass_s24a, String hasEstate_s24a,
			String isCBControl_s24a, String LTVClass_s24a_Y,
			String LTVClass_s24a_N, String LTVType_s24a,
			String estateIsLand_s24a, String isFarmWood_s24a, String LTV_s24a,
			Properties popPanel) throws CapMessageException;

	/**
	 * J-111-0454 find L120S24B
	 * 
	 * @param mainId
	 * @param refOid_s24b
	 *            ,
	 * @param search
	 * @return
	 */
	Page<L120S24B> findL120s24bByRefOid_s24b(String mainId, String refOid_s24b,
			ISearch search);

	/**
	 * J-111-0454 find L120S24B
	 */
	L120S24B findL120s24bByOid(String oid);

	/**
	 * J-111-0454 儲存風險權數擔保品明細
	 */
	Map<String, String> saveL120s24b(String oid, String mainId,
			String refOid_s24b, String quaColl_s24b, String collCurr_s24b,
			String collAmt_s24b, Properties pop) throws CapMessageException;

	/**
	 * J-113-0499 儲存風險權數擔保品明細
	 */
	Map<String, String> saveL120s24b_2025(String oid, String mainId,
			String refOid_s24b, String quaColl_s24b, String collCrdGrade_s24b,
			String collCurr_s24b, String collAmt_s24b, Properties pop)
			throws CapMessageException;
	
	/**
	 * J-111-0454 刪除風險權數擔保品明細
	 */
	Map<String, String> deleteL120s24b(String mainId, String refOid_s24b,
			String[] oids);

	/**
	 * J-111-0454 撈借款人說明->1.中央政府/央行
	 */
	Page<Map<String, Object>> queryL120s24aCentralGov(ISearch search);

	/**
	 * J-111-0454 撈借款人說明->2.非營利國營事業名單
	 */
	Page<Map<String, Object>> queryL120s24aNonPro(ISearch search);

	/**
	 * J-111-0454 計算保證機構風險權數
	 */
	Map<String, Object> calGutDeptRW_s24a(String oid, String gutClass_s24a,
			String gutDeptCountry_s24a, String gutDeptID_s24a,
			Properties popPanel) throws CapMessageException;

	/**
	 * 取連帶保證人相關資訊 會直接寫入L120S24A
	 * 
	 * @param oid
	 * @param guarId_s24a
	 * @param popPanel
	 * @return
	 */
	Map<String, String> findGuar_s24a(String oid, String guarId_s24a,
			Properties pop) throws CapMessageException;

	/**
	 * 重新計算連帶保證人的風險權數
	 * 
	 * @param oid
	 * @param guarId_s24a
	 * @param popPanel
	 * @return
	 */
	Map<String, String> calGuarRW_s24a(String oid, Properties pop)
			throws CapMessageException;

	/**
	 * J-111-0454 計算抵減後風險權數 純試算不儲存 <BR>
	 * J-112-0375 新增參數gutClass_s24a
	 * 
	 * @param oid
	 * @param mainId
	 * @param currentApplyAmt_s24a_big
	 * @param onlyGuar_s24a
	 * @param ccf_s24a_big
	 * @param beforeDeductRW_s24a_big
	 * @param hasQuaColl_s24a
	 * @param hasGutClass_s24a
	 * @param gutPercent_s24a_big
	 * @param gutDeptRW_s24a_big
	 * @param extractBetterGuar_s24a
	 * @param guarRW_s24a_big
	 * @param pop
	 * @param gutClass_s24a
	 * @return
	 */
	Map<String, String> calDeductRW_s24a(String oid, String mainId,
			BigDecimal currentApplyAmt_s24a_big, String onlyGuar_s24a,
			BigDecimal ccf_s24a_big, BigDecimal beforeDeductRW_s24a_big,
			String hasQuaColl_s24a, String hasGutClass_s24a,
			BigDecimal gutPercent_s24a_big, BigDecimal gutDeptRW_s24a_big,
			String extractBetterGuar_s24a, BigDecimal guarRW_s24a_big,
			Properties pop, String gutClass_s24a);

	/**
	 * 是否顯示s10風險權數頁籤
	 * 
	 * @param l120m01a
	 * @return
	 */
	boolean showPanelLms140s10(L120M01A l120m01a);

	/**
	 * 是否隱藏"舊"風險權數頁籤
	 * 
	 * @param l120m01a
	 * @return
	 */
	boolean hideOldRWPanel(L120M01A l120m01a);

	/**
	 * 是否啟用2025版風險權數
	 * @return
	 */
	public String isL120s24a2025On();

	/**
	 * 判斷額度明細表是否需要風險權數試算
	 * 
	 * @param l140m01a
	 * @return
	 */
	boolean chkCntrNoNeedRW(L140M01A l140m01a);

	/**
	 * J-111-0536 為有效管控授信簽報授權層級避免逾權, 增加eloan企金授信管理授信簽報之相關措施
	 * 
	 * @param mainId
	 * @param itemType
	 * @return
	 */
	String showLoanTotAmtWriteSuggest(String mainId, String itemType);

	List<L120S04A> findL120s04aByMainIdKeyCustIdDupNo(String mainId,
			String keyCustId, String keyDupNo);

	List<L120S04A> findL120s04aByMainIdKeyCustIdDupNoPrtFlag(String mainId,
			String keyCustId, String keyDupNo, String prtFlag);

	List<L120S04B> findL120s04bByMainIdKeyCustIdDupNoDocKind(String mainId,
			String keyCustId, String keyDupNo, String[] docKind);

	List<L120S04C> findL120s04cByMainIdKeyCustIdDupNoDocKind(String mainId,
			String keyCustId, String keyDupNo, String[] docKind);

	List<L120S04E> findL120s04eByMainIdKeyCustIdDupNoDocKind(String mainId,
			String keyCustId, String keyDupNo, String[] docKind);

	List<L120S04B> findL120s04bByMainIdKeyCustIdDupNo(String mainId,
			String keyCustId, String keyDupNo);

	List<L120S04C> findL120s04cByMainIdKeyCustIdDupNo(String mainId,
			String keyCustId, String keyDupNo);

	List<L120S04E> findL120s04eByMainIdKeyCustIdDupNo(String mainId,
			String keyCustId, String keyDupNo);

	List<L120S04E> findL120s04eByMainIdDocKind(String mainId, String[] docKind);

	List<L120S04D> findL120s04dListByMainId(String mainId);

	L120S04D findL120s04dByOid(String oid);

	void saveL120s04dList(List<L120S04D> list);

	void deleteL120s04dList(List<L120S04D> list);

	/**
	 * J-109-0351_05097_B1001 e-Loan企金「青年創業及啟動金貸款」簽報書修改
	 * 
	 * 屬於要控管的2020/08/01後申請的案子
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean needChkLnType61ApplyDateAfter20200801(String lnType,
			Date applyDate);

	/**
	 * J-109-0419_05097_B1001 兆元振興方案新增額度為不循環時才符合報送之判斷
	 * 
	 * forReport:true 為報送使用(要判斷2020/11/1後才開始排除不循環)，false
	 * 為額度明細表儲存檢核用(單純檢查不循環，不用判斷是不是2020/11/1)
	 * 
	 */
	public String chkReviveReUseIsLegal(L140M01A l140m01a, L120M01A l120m01a,
			boolean forReport);

	/**
	 * J-109-0459_05097_B1001 Web e-Loan簡化微型企業簽報書資僅為動用新台幣案件時得免執行制裁/管制名單掃描。
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean hidePanelbyCaseType_miniFlag_passAml(L120M01A l120m01a);

	/**
	 * J-109-0459_05097_B1001 Web e-Loan簡化微型企業簽報書資僅為動用新台幣案件時得免執行制裁/管制名單掃描。
	 * 
	 * 
	 * 為節省分行簽報「微型企業」程序，於反洗錢暨金融犯罪防治處規劃之新掃描點上線前，擬簡化小微企業進行制裁/管制名單之掃描，
	 * 並依反洗錢暨金融犯罪防治處建議
	 * ，僅針對新台幣放款案件進行簡化。因徵信作業時無法控管是否為新台幣放款，本次簡化微型企業於「授信簽案及審查」(詳附件二)
	 * 掃描程序，如簽報書資料僅為動用新台幣案件(無外幣利率條件或額度無得動用等值其他外幣等)得免執行制裁/管制名單掃描。
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean isLmsCaseReportCanPassAml(L120M01A l120m01a);

	public L140M01A findL140M01AByL120m01cMainIdAndcntrNo(String caseMainId,
			String cntrNo, String itemType);

	public HashMap<String, String> getShareBrId(L120M01A l120m01a);

	public HashMap<String, String> getShareBrIdByCond(L120M01A l120m01a,
			String specifyItemType, String[] outProperty);

	public HashMap<String, String> getShareBrIdByCntrDoc(L140M01A l140m01a);

	public String getBrNameByLocale(String BrId, Locale locale);

	public String processCentralBankRealEstateRuleInitVersion(
			String l140m01a_mainId);

	/**
	 * J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
	 * 
	 * 儲存徵信簽章資訊
	 * 
	 * @param mainId
	 */
	public void setL120s17aData(String mainId);

	public String getNewAdcCaseNo() throws CapMessageException;

	/**
	 * J-109-0470_05097_B1001 Web e-Loan授信簽案配合本行110年施行LTV法，土建融案件新增案件編號
	 * 
	 * @param caseBrId
	 * @return
	 * @throws CapMessageException
	 */
	public String getNewAdcCaseNo(String caseBrId) throws CapMessageException;

	public HashSet<String> getAdcCaseNoList(String type, String custId,
			String dupNo, String cntrNo, String adcCaseNo);

	/**
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param dataSource
	 * @param data
	 * @return
	 */
	public Map<String, String> getGuarantorPriority(String dataSource,
			String data);

	/**
	 * J-110-0104_05097_B1001 Web
	 * e-Loan企金授信配合組織再造，預計北一區、北二區合併為北區授管中心，北區營業單位之授管中心授權外案件直送授信審查處
	 * 
	 * @param brNo
	 * @return
	 */
	public boolean isAreaBranchDirectSendHead(L120M01A l120m01a);

	public void setType_new_20210319(L140M01M l140m01m, String custId,
			List<String> errList, List<String> showList,
			boolean useExist_isLimitCust, String cntrNo, String property,
			String quantLoan);

	public void gotoSetType(String version, L140M01M l140m01m, String custId,
			List<String> errList, List<String> showList,
			boolean useExist_isLimitCust, String cntrNo, String property,
			String quantLoan);

	public void setMortgageDetailCodeAndCleanUnusedField(JSONObject jsonObject,
			L140M01M l140m01m, String version);

	public void saveC101S04W(C101S04W c101s04w);

	/**
	 * J-110-0117_05097_B1001 Web e-Loan企金授信簽報書借款人行業代號別屬投資公司授信案件時，新增相關提醒及檢核功能。
	 * 
	 * @param caseBrId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> getFinDataByCaseBrIdAndCustId(String caseBrId,
			String custId, String dupNo);

	/**
	 * 
	 * J-110-0117_05097_B1001 Web e-Loan企金授信簽報書借款人行業代號別屬投資公司授信案件時，新增相關提醒及檢核功能。
	 * 
	 * 就海內外分行(含子行)於e-loan企金簽報系統，簽報借款人行業代號別屬投資公司(行
	 * 業代號：6499及6811)授信案件時，新增相關提醒及檢核功能，如修改內容。
	 * 1、請於分行端編製中於送呈主管覆核及覆核主管於覆核時，新增提醒視窗，內容為：
	 * 借戶為本行「辦理投資公司授信業務須知」第二條所定義之投資公司，請確實依該 須知之相關規定辦理，並應檢視借款人是否有連續三年呈現稅前虧損或淨值低於資
	 * 本額之50%或負債比率高於500%等三項指標之一者，若是，本案則應簽報總處核定 。
	 * 
	 * 2、倘有符合上述第1點所列三項指標之一者，該簽報書各別額度序號於「與本行內部規
	 * 範不符之註記說明」欄位值，若勾選「無須例外註記」時，請新增提示視窗，內容 為：
	 * 經查借戶有符合連續三年呈現稅前虧損或淨值低於資本額之50%或負債比率高於500%
	 * 等三項指標之一情形，請確認借戶是否為本行「辦理投資公司授信業務須知」第二 條所定義之投資公司，若是，例外註記之內部規範名稱選項，應勾選「辦理投資公
	 * 司授信業務須知」。 檢視借戶是否為投資公司，若是，提醒視窗是否依檢視條件出現相關提醒視窗。
	 * 
	 * @param l120m01a
	 * @param chkl140m01as
	 * @return
	 */
	public Map<String, String> chkNeedExceptionNoteForInvestmentCompany(
			L120M01A l120m01a, List<L140M01A> chkl140m01as);

	public void deleteDocFileForDeleteBorrower(String l120m01a_mainId,
			String srcFileName);

	/**
	 * J-110-0209_05097_B1001 Web e-Loan國內企金簽報書額度明細表中增列「創兆貸」專案，並產生統計報表案
	 * 
	 * 判斷是否為110年度紓困方案
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemSubSidy(String rescueItem, String year);

	/**
	 * J-110-0209_05097_B1001 Web e-Loan國內企金簽報書額度明細表中增列「創兆貸」專案，並產生統計報表案
	 * 
	 * 紓困代碼新舊轉換對照
	 * 
	 * 
	 * @param rescueItem
	 * @return
	 */
	public String resueItemSubSidyMapping(String rescueItem);

	/**
	 * 判斷是否為小規模額度明細表
	 * 
	 * @param l140m01a
	 * @return
	 */
	public boolean isCntrDocSmallBussC(L140M01A l140m01a);

	/**
	 * 取得MIS客戶行業別與客戶類別
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重複序號
	 * @return
	 */
	public JSONObject getCustBusCDAndClass(String custId, String dupNo);

	/**
	 * 
	 * 簽報書檢核有沒有小規模營業人額度明細表
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean isL120m01aRescueSmallBussHasCaseC(L120M01A l120m01a);

	/**
	 * 企金戶的借款人: 會有 insert LNUNID, update LNUNID 企金戶的負責人: 只有 update LNUNID
	 * 
	 * @param meta
	 * @param listCust
	 */
	public void updateLnunidWithL120S01A(L120M01A meta, List<L120S01A> listCust);

	/**
	 * 傳送婉卻紀錄到ORACLE (HTTP_POST)
	 * 
	 * P-108-0046_05097_B1004 Web e-Loan配合Oracle系統建置，修改AML相關功能。
	 * 
	 * sendRejectListToOracle
	 * 
	 * @param meta
	 */
	public String sendRejectListToOracle(L120M01A meta,
			List<L140M01A> rejectl140m01as);

	/**
	 * J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean hidePanelbyCaseType_003(L120M01A l120m01a);

	/**
	 * J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
	 * 
	 * 判斷簽報書屬於發基金協助新創事業紓困融資加碼方案微型企業簽報書
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isOnlyCaseType003(L120M01A l120m01a);

	/**
	 * 判斷額度明細表是否為國發基金協助新創事業紓困融資加碼方案 J-110-0CCC_05097_B1001 Web
	 * e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
	 * 
	 * @param l140m01a
	 * @return
	 */
	public boolean isCntrDocStartUpReliefPackage(L140M01A l140m01a);

	/**
	 * J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
	 * 
	 * 判斷紓困類別F是否為國發基金協助新創事業紓困融資加碼方案
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemStartUpReliefPackage(String rescueItem,
			String rescueItemSub);

	/**
	 * 
	 * J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
	 * 
	 * 簽報書檢核有沒有任何一筆國發基金協助新創事業紓困融資加碼方案額度明細表
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean isL120m01aHasStartUpReliefCntrDoc(L120M01A l120m01a);

	/**
	 * J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
	 * 
	 * 檢核動審表是否皆為新創事業紓困融資加碼方案
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isOnlyCaseType003(L160M01A l160m01a);

	/**
	 * J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
	 * 
	 * 判斷簽報書屬於發基金協助新創事業紓困融資加碼方案微型企業簽報書
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean hidePanelbyCaseType_004(L120M01A l120m01a);

	/**
	 * J-110-0281_05097_B1001 Web
	 * e-Loan授信配合「公股攜手，兆元振興」融資方案已結案，謹申請取消e-LOAN簽報書相關欄位之顯示
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean needShowIsRevive(L120M01A l120m01a);

	/**
	 * J-112-0200 中小企業千億振興融資方案
	 */
	public boolean needShowIsRevital(L120M01A l120m01a);

	public boolean needChkCaseRevital();

	/**
	 * J-110-0288_05097_B1001 Web e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
	 * 
	 * 判斷是否需要國發基金加碼保證成數
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemNeedNdfGutPercent(String rescueItem);

	/**
	 * J-110-0288_05097_B1002 Web e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
	 * 
	 * 判斷是否需要是否符合110年5~12月營業額減少達15%
	 * 
	 * @param rescueItem
	 * @return
	 */
	public boolean isResueItemNeedIsTurnoverDecreased(String rescueItem);

	/**
	 * J-110-0288_05097_B1002 Web e-Loan配合國發基金新創事業紓困加碼方案H01、A07專案修改
	 * 
	 * 檢核減收或補貼利率不得超過規定
	 * 
	 * 現請額度與現請幣別
	 * 
	 * @param rescueItem
	 * @return
	 */
	public String chkRescueItemRescueRateHigh(String rescueItem,
			String rescueItemSub, BigDecimal rescueRate);

	public Map<String, Object> get_F101S01A_B_By_CaseReport(int idx,
			List<F101M01A> f101m01a_list);

	/**
	 * J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean hidePanelbyCaseType_caseType_00A(L120M01A l120m01a);

	void deleteUploadFileRealTime(String[] oids);

	public void setType_new_20210924(L140M01M l140m01m, String custId,
			List<String> errList, List<String> showList,
			boolean useExist_isLimitCust, String cntrNo, String property,
			String quantLoan);

	public void setType_new_20211217(L140M01M l140m01m, String custId,
			List<String> errList, List<String> showList,
			boolean useExist_isLimitCust, String cntrNo, String property,
			String quantLoan);

	public Map<String, CapAjaxFormResult> getReportCodeMapOfRealEstateRuleVersion(
			String version);

	public void setMortgageDetailCodeForRealEstateBusinessRule(
			CapAjaxFormResult result, String cbcCase,
			String realEstateLoanLimitReason);

	public String checkGracePeriodForNaturalPersonBuyingHouse(String version,
			String cbccase, String realEstateLoanLimitReason, String custId,
			List<L140S02A> l140s02as, String property);

	public String getTipsMsgForCentralBankMortgageMark(String version,
			String cbccase, String keepYN, Date actStartDate);

	/** J-110-0458 企金授權內其他 - 「簡易簽報」選項，適用方案「LIBOR退場變更利率條件簡易簽報」 */
	public boolean isLiborExitCase(L120M01A model);

	public boolean isEuroyenTiborExitCase(L120M01A model);

	/**
	 * J-110-0986_05097_B1001 於簽報書新增LGD欄位
	 */
	public List<L140M01O> findL140m01oByMainId(String mainId);

	/**
	 * J-110-0455_05097_B1001 企金授信簽報書新增72-2簽案預約控管
	 * 
	 * 判斷簽案是否需要72-2預約
	 * 
	 * @param l140m01a
	 * @return
	 */
	public String chk722NeedElf442(L140M01A l140m01a);

	/**
	 * J-113-0334 企金RWA預約
	 *
	 * @param l140m01a
	 * @return
	 */
	String chkRwaCase2NeedElf442(L140M01A l140m01a);

	/**
	 * J-110-0499 配合作業合理化提案，調整會議決議及相關A、B、C版 組會議決議
	 */
	public String getLMSDoc9MainData(L120M01H l120m01h, String type);

	/**
	 * J-111-0052 修改借戶暨關係戶與本行往來實績彙總表 確認是否為 105/8/1本行自動化通路定存交易限制前之高利拆單自動展期戶
	 */
	public boolean isHinsCust(String custId);

	public boolean isOpenPaperlessSigningFunction();

	/**
	 * J-110-0485_05097_B1001 於簽報書新增LGD欄位
	 * 
	 * @param l120m01a
	 */
	public void upLoadDwLgd(L120M01A l120m01a);

	/**
	 * J-110-0485_05097_B1001 於簽報書新增LGD欄位
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean showPanelLms140s08(L120M01A l120m01a, String caseBrId);

	/**
	 * J-111-000A_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
	 * 
	 * @param codeType
	 * @param value
	 * @return
	 */
	public String convertChkBoxValueToString(String codeType, String value);

	/**
	 * J-111-000A_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
	 * 
	 * @param codeType
	 * @param value
	 * @return
	 */
	public String convertEsgSustainLoanTypeToAloan(String esgSustainLoanType);

	public Date getApprovalDateByLnf447nForIs3rdHighHouseRule(String cntrno);

	public String proccessHloanLimitValueForIs3rdHighHouseRule(
			String elf500_version, String elf500_hLoanLimit,
			String elf500_hLoanLimt_2);

	public void setL140m01mValueByELF500(L140M01M l140m01m, String version,
			String realEstateLoanLimitReason, String is3rdHignHouse);

	public String checkVersionByRealEstateNoteRule(String version,
			L140M01A l140m01a, String cbcCase, String landBuildYN,
			String prodClass, boolean isCheck01O, String plusReason,
			String remainLoanYN);

	/**
	 * J-111-0087_05097_B1001 Web e-Loan國內海外企金簽報書就不同業務授權獨立產生LGD數據
	 * 
	 * @param l120m01a
	 */
	public void upLoadElf025(L120M01A l120m01a);

	/**
	 * J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
	 */
	public Map<String, String> getMisIsCoreBussAndItwCodeCoreBuss(String busCode)
			throws CapException;

	public boolean isOpenDebtRatioControlRuleFunction();

	/**
	 * J-111-0112_05097_B1001 Web e-Loan企金授信管理系統新增紓困代碼檢核
	 * 
	 * @param rescueItem
	 * @param chkDate
	 * @param chkType
	 * @return
	 */
	public String isRescueItemExpired(String rescueItem, String chkDate,
			String chkType);

	public String checkIs722ExcludeItems(String estateType,
			String isRentOrSell, String clsOrLms) throws CapMessageException;

	/**
	 * J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
	 * 
	 * @param inVersion
	 *            :可以NULL，NULL時抓最新，並回傳version
	 * @param inUnitNo
	 *            :可以NULL，NULL時抓登入人員
	 * @param expectLgd
	 *            :可以NULL，若有值，則判斷授權群組回傳lgdAuthGroup
	 * @return
	 */
	public Map<String, String> getLgdTotAmtParam(L140M01A l140m01a,
			BigDecimal expectLgd);

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 檢查授信LGD逾權相關參數是否設定正確
	 * 
	 * @param crdType
	 * @param version
	 * @return
	 */
	void checkC900m01k_LGDParam(L120M01A l120m01a, List<L140M01A> listL140m01a,
			String source) throws CapMessageException;

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 檢查出口押匯逾權相關參數是否設定正確
	 * 
	 * @param l120m01a
	 * @throws CapMessageException
	 */
	void checkC900m02kParam(L120M01A l120m01a) throws CapMessageException;

	/**
	 * J-111-0461 建議授權層級，找出C900M01P各版本最低PD等級
	 * 
	 * @param version
	 * @param docType
	 * @return
	 */
	String getC900m01pWorst(String version, String docType);

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 抓借款人信用評等項次，會在這隻判斷企金/個金
	 * 
	 * @param mainId
	 * @param docType
	 * @param typCd
	 * @param custId
	 * @param dupNo
	 * @param C900m01pAll
	 * @return
	 */
	String findBestPdGroup(String mainId, String docType, String typCd,
			String custId, String dupNo, List<L140M01A> listL140m01a,
			Map<String, Map<String, String>> C900m01pAll);

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 找出該借款人本行信用模型評等及外部信用評等，擇孰高等級
	 * 
	 * "企金"在用的抓借款人PD分組
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param c900m01pCommonMap
	 * @param c900m01pSpecialMap
	 * @return
	 */
	String findBestPdGroupLms(String mainId, String custId, String dupNo,
			Map<String, Map<String, String>> C900m01pAll);

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 找出該借款人消金評等項次
	 * 
	 * 同時擁有本行消金房貸及非房貸模型評等時，擇劣等級適用
	 * 
	 * "消金"在用的抓借款人PD分組
	 * 
	 * @param mainId
	 * @param typCd
	 * @param custId
	 * @param dupNo
	 * @param c900m01pCommonMap
	 * @param c900m01pSpecialMap
	 * @return
	 */
	String findBestPdGroupCls(String mainId, String typCd, String custId,
			String dupNo, List<L140M01A> listL140m01a,
			Map<String, Map<String, String>> C900m01pAll);

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 取得該version所有的C900M01P對應表
	 * 
	 * @param docType
	 * @param version
	 * @return
	 */
	Map<String, Map<String, String>> findC900m01pAll(String docType,
			String version);

	/**
	 * J-113-0233
	 * 撈L120M01L取得PD，傳入L140M01A判斷該額度的LGD組別，pdGroupMap可放入已使用到的PD，避免過多重複查找資料
	 * 
	 * @param l120m01a
	 * @param l140m01a
	 * @param pdGroupMap
	 * @return
	 */
	public String getPdGroupAndLgd(L120M01A l120m01a, L140M01A l140m01a,
			Map<String, String> pdGroupMap);
	
	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 取得該間分行的C900M01K_LGD相關資料
	 * 
	 * @param l120m01a
	 * @return
	 */
	Map<String, Object> getBranchC900m01k_lgd(L120M01A l120m01a,
			String c900m01k_lgdVersion, Map<String, String> branchInfoMap);

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 取得借款人各種授信額度，自己的、合併關係企業的、加總的
	 * 
	 * 以及一些判斷會用到的參數
	 * 
	 * @param mainId
	 * @param l120m01a
	 * @param listL140m01a
	 * @param needCountLGDMap
	 * @param version
	 * @param lmsLgdCountTotal
	 * @return
	 */
	Map<String, Object> getAllBorrowerIdMap(String mainId, L120M01A l120m01a,
			List<L140M01A> listL140m01a, Map<String, L140M01A> needCountLGDMap,
			Integer lmsLgdCountTotal);

	/**
	 * J-111-0461
	 * 
	 * 判斷該借款人是否為乾淨的單獨劃分授權額度
	 * 
	 * 有單獨劃分授權且其他非單獨劃分的皆為不變、取消(or沒有)
	 * 
	 * 這種要直接pass授信額度逾權檢核
	 * 
	 * @param listL140m01a
	 * @return
	 */
	List<String> checkIsStandAloneAuthPureByCust(List<L140M01A> listL140m01a);

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 檢查是否需要計算建議授權層級
	 * 
	 * @param mainId
	 * @param l120m01a
	 * @param listL140m01a
	 * @param needCountLGDMap
	 * @throws CapMessageException
	 */
	void suggestCaseLvlCheck(String mainId, L120M01A l120m01a,
			List<L140M01A> listL140m01a, Map<String, L140M01A> needCountLGDMap)
			throws CapMessageException;

	/**
	 * 一樣是呼叫suggestCaseLvlCheck，但只要傳入mainId即可
	 * 
	 * 主要是為了讓其他非計算授信額度合計的地方，也可以做完事情後重新去計算層級
	 * 
	 * 但他的額度明細表都要是計算過的
	 * @param mainId
	 * @throws CapMessageException
	 */
	void reCalSuggestCaseLvlCheck(String mainId) throws CapMessageException;
	
	void reCalSuggestCaseLvlCheck(String mainId, String mode) throws CapMessageException;
	
	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 傳入該簽報書的所有額度明細表，透過lmsLgdService.isL140m01aNeedCountLgdTotal
	 * 
	 * 去清整出都是需要計算合計的額度明細map
	 * 
	 * @param l140m01as
	 * @return
	 */
	public Map<String, L140M01A> getNeedCountLgdTotalL140m01a(List<L140M01A> l140m01as);
	
	/**
	 * 紀錄該簽報書當下選擇的授權計算模式
	 * 
	 * @param mainId
	 * @param suggestMode
	 */
	void setSuggestCaseLvlMode(String mainId, String suggestMode);

	/**
	 * 回傳給前端要出現哪些建議審核層級選項
	 * 
	 * @param mainId
	 * @param listL140m01a
	 * @return
	 */
	String showSuggestModeRadio(String mainId, List<L140M01A> listL140m01a);

	/**
	 * 判斷infoMap裡有沒有霸王條款2，因為以後可能會長出更多細項規則
	 * 
	 * 這支改成專門判斷有沒有king2，代表有沒有異動此業務
	 * 
	 * @param l120m01l
	 * @return
	 */
	public boolean haveKingRule2(L120M01L l120m01l);
	
	/**
	 * 是否啟用建議審核層級顯示不變
	 * 
	 * @param mainId
	 * @return
	 */
	public boolean checkCaseLvlNGEnable(String mainId);
	
	/**
	 * 檢查整份簽報書的L120M01L，重新確認一下我到底需不需要提供建議核定層級表
	 * 
	 * 需要提供:Y
	 * 需要提供但皆不變:G
	 * 不需要提供:N
	 * @param mainId
	 * @return
	 */
	public String checkNeedProvideCaseLvl(String mainId);
	
	/**
	 * J-111-0461 逾越授權新版，走LGD的檢核
	 * 
	 * 純抓出第一筆額度明細
	 * 
	 * @param listL140m01a
	 * @return
	 */
	public L140M01A findFirstL140M1A(List<L140M01A> listL140m01a);

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 用企/個金、版本號找出C900M01K_LGD參數檔
	 * 
	 * brNo和brClass擇一輸入
	 * 
	 * @param docType
	 * @param version
	 * @param brNo
	 * @param brClass
	 * @return
	 */
	List<C900M01K_LGD> findC900m01k_LGDByBrNoOrBrClass(String docType,
			String version, String brNo, String brClass);

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 撈出L120M01L by mainId
	 * 
	 * @param mainId
	 * @param loanKind
	 * @return
	 */
	public List<L120M01L> findL120m01lByMainId(String mainId);

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 撈出L120M01L by mainId、業務別
	 * 
	 * 會撈出多筆，因為有多個人
	 * 
	 * @param mainId
	 * @param loanKind
	 * @return
	 */
	public List<L120M01L> findL120m01lByMainIdAndLoanKind(String mainId,
			String loanKind);

	/**
	 * 取得所有借款人合併的那一筆資料 L120M01L資料by業務
	 * 
	 * @param mainId
	 * @param loanKind
	 * @return
	 */
	public L120M01L findL120m01lIsFullCaseByMainIdAndLoanKind(String mainId,
			String loanKind);

	/**
	 * 取得該案最高的建議審核層級
	 * 
	 * @param mainId
	 * @return
	 */
	public Map<String, String> findL120m01lHighestCaseLvl(String mainId);

	/**
	 * 取得該案疑似逾權的相關註記、說明
	 * 
	 * @param mainId
	 * @return
	 */
	public Map<String, String> findL120m01kOverAuthMessage(String mainId);

	/**
	 * J-111-0536 新增逾權計算說明的thickbox
	 * 
	 * @param mainId
	 * @return
	 */
	public String showOverAuthDetail(String mainId);

	/**
	 * 透過L120M01L來判斷該簽報書是否逾越授權，並押上逾權註記
	 * 
	 * @param l120m01a
	 */
	public void judgeL120m01aIsOverAuthByL120m01l(L120M01A l120m01a);

	/**
	 * 用來檢核建議核定層級不同地方，不同的狀況時要不要可以用<BR/>
	 * type:1->額度明細表裡的建議核定層級(個人)<BR/>
	 * type:2->簽報書畫面的建議核定層級<BR/>
	 * type:3->報表裡面的建議核定層級<BR/>
	 * 
	 * @param type
	 * @param l120m01a
	 * @return
	 */
	public boolean checkEveryWhereSuggestCaseLvl(String type, L120M01A l120m01a);

	/**
	 * 取得某借款人的最高建議核定層級(授信、出口)<BR/>
	 * 為了在額度明細表上顯示
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, String> findL120m01lCaseLvlByCustId(String mainId,
			String custId, String dupNo);

	/**
	 * J-111-0461 建議授權層級
	 * 
	 * 授信額度逾越授權檢核是否啟用LGD判斷逾權/是否啟用建議審核層級
	 * 
	 * 檢查合併關係企業版本
	 * 
	 * @param mainId
	 * @param listL140m01a
	 * @param inUnitNo
	 * @param source
	 * @throws CapMessageException
	 */
	public void checkL120s11aLgdVersion(String mainId, L120M01A l120m01a,
			List<L140M01A> listL140m01a, String source)
			throws CapMessageException;

	/**
	 * 決定是否啟用新版的逾權檢核機制
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean isOverAuthUseNewVersion(L120M01A l120m01a);

	/**
	 * J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
	 * 
	 * @param l120m01a
	 * @return
	 */
	public String getLgdTotAmtVerByCaseDate(L120M01A l120m01a) throws Exception;

	/**
	 * 判斷額度明細表科目所屬業務種類是否需要RWA
	 * 
	 * J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	 * 
	 * @param l140m01a
	 * @return
	 */
	public boolean getRwaBussType(L140M01A l140m01a);

	/**
	 * J-111-0397_05097_B1001 Web e-Loan國內企金授信新增權加權風險性資產(RWA)相關計算與預約機制
	 * 
	 * 判斷額度序號是否需要有加權風險性資產(RWA)L120S23A對應資料
	 * 
	 * @param l140m01a
	 * @return
	 */
	public boolean chkCntrNoNeedRwa(L140M01A l140m01a);

	/**
	 * J-111-0397_05097_B1001 Web e-Loan國內企金授信新增權加權風險性資產(RWA)相關計算與預約機制
	 * 
	 * 判斷額度序號是否需要有加權風險性資產(RWA)預約
	 * 
	 * @param l140m01a
	 * @return
	 */
	public String chkRwaNeedElf442(L140M01A l140m01a);

	/**
	 * J-111-0397 RWA
	 */
	public String[] getRwaApplyAmt(L140M01A l140m01a);

	public boolean showPanelLms140s09(L120M01A l120m01a);

	public boolean chkRwaStandard(L120S23A l120s23a);

	public boolean chkNeedRorwa(L120S23A l120s23a);

	public boolean chkCalcRorwa(L120S23A l120s23a);

	/**
	 * J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
	 * 
	 * @param l120m01a
	 * @param caseBrId
	 * @param l140m01a
	 * @param itemType
	 * @return
	 */
	public boolean showLgdTotAmt(L120M01A l120m01a, String unitNo,
			String itemType);

	/**
	 * 檢核借款人ID不能執行ELOAN簽案
	 * 
	 * J-111-0406_05097_B1001 Web
	 * e-Loan企金授信12305479(興富發建設股份有限公司)限制不得動撥及選擇上述所列二授信業務暨其相對應之會計科目。
	 * 
	 * @param l140m01a
	 * @param chkTime
	 *            1:簽報書 2:動審表
	 * @return
	 */
	public String chkNoLoanId(L140M01A l140m01a, String chkTime);

	/**
	 * J-111-0406_05097_B1001 Web
	 * e-Loan企金授信12305479(興富發建設股份有限公司)限制不得動撥及選擇上述所列二授信業務暨其相對應之會計科目。
	 * 
	 * 取得後台設定之系統參數(參數值為JSON)
	 * 
	 * @param sysParamKey
	 * @param jsonKey
	 * @return
	 */
	public String getSysParamJsonValue(String sysParamKey, String jsonKey);

	public L140M01A getL140m01aByCntrNoForNew(String cntrNo);

	public String getLossDiffRate(L140M01A l140m01a);

	/**
	 * PD等級評等級數對應PD違約率
	 * 
	 * 主要是這些參數都不該跟LGDVersion有掛勾
	 * 
	 * @param mainId
	 * @param l140m01a
	 * @param custId
	 * @param dupNo
	 * @param isNoL120s01c
	 * @param pop
	 * @return
	 * @throws CapMessageException
	 */
	public Map<String, String> getPDLostDiffRate(String mainId, String custId,
			String dupNo, boolean isNoL120s01c, Properties pop)
			throws CapMessageException;

	/**
	 * 取得各授信業務授權額參數檔版本
	 * 
	 * @param l120m01a
	 * @param loanKind
	 * @return
	 */
	public String getCaseLvlParamVersion(L120M01A l120m01a, String loanKind);

	/**
	 * LGD組別對應違約率
	 * 
	 * @param mainId
	 * @param cntrNo
	 * @return
	 */
	public Map<String, String> getLGDLostDiffRate(String mainId, String custId,
			String dupNo, L140M01A l140m01a, List<L140M01A> l140m01aListCust,
			Properties pop) throws CapMessageException;

	/**
	 * // J-112-0217
	 * 配合修訂「本行授信利率暨保證及承兌手續費計收標準實施要點」，修改e-loan簽報書_利率定價合理性及收益率分析表格頁籤。
	 * 
	 * 判斷是否為新戶
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public boolean checkIsNewCust(String mainId, String custId, String dupNo);

	public boolean showPanelLms140s11(L120M01A l120m01a);

	/**
	 * 判斷額度明細表是否需要BIS評估表
	 * 
	 * 
	 * @param l140m01a
	 * @return
	 */
	public boolean chkCntrNoNeedBis(L140M01A l140m01a);

	/**
	 * 判斷額度明細表科目所屬業務種類是否需要BIS評估表
	 * 
	 * 
	 * @param l140m01a
	 * @return
	 */
	public boolean getBisBussType(L140M01A l140m01a);

	/**
	 * BIS評估表-取得表內表外與最大CCF
	 * 
	 * @param l140m01a
	 * @return
	 */
	public String[] getSheetItemAndCcf(L140M01A l140m01a);

	/**
	 * J-111-0488_05097_B1001 Web e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
	 * 
	 * @param l120m01a
	 * @return
	 */
	public boolean isOverAuthSendHead(L120M01A l120m01a);

	public String checkEmptyLandLoan(L140M01M l140m01m, String cntrNo)
			throws CapMessageException;

	/**
	 * 檢查 報案類別
	 * 
	 * @param l140m01a
	 *            額度明細表
	 * @return 報案類別
	 */
	public String checkELF447NCLASS(L140M01A l140m01a, String version);

	public List<ELF600> genELF600ObjectWhenNoRecord(List<ELF600> elf600List,
			L140M01M l140m01m, String cntrNo, String custId, String dupNo);

	/**
	 * J-111-0443_05097_B1006 Web e-Loan企金授信開發授信BIS評估表
	 * 
	 * @param l120m01a
	 * @return
	 */
	public String chkRptDocStatusCanEditBorrower(L120M01A l120m01a);

	/**
	 * J-111-0515_05097_B1001 Web
	 * e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
	 * 
	 * @param l120m01a
	 * @param l140m01a
	 * @return
	 * @throws CapMessageException
	 */
	public Map<String, BigDecimal> getCurrentApplyAmt_S_N(L120M01A l120m01a,
			L140M01A l140m01a, boolean useSignAmtLowerApplyAmtY);

	/**
	 * J-111-0551 在途授信額度 ClsConstants.RptNo.授信信用風險管理_遵循檢核表
	 */
	public String getClsR30PrintVerStr(L120M01A l120m01a, L120S01M l120s01m);

	/**
	 * J-111-0630_05097_B1001 Web e-Loan授信系統額度明細表擔保品欄位為徵提股票設質案件時，一律跳出警訊視窗
	 * 
	 * @param model
	 * @param chkl140m01as
	 * @return
	 */
	public String showMsgWithCMSStock(L120M01A model,
			List<L140M01A> chkl140m01as);

	/**
	 * J-111-0466 依據企金業務處940F11108-4號傳真函，調整"集團／關係企業與本行授信往來條件比較表"格式 取得最新信評
	 */
	public Map<String, Object> getLastedCrd(String custId, String dupNo,
			int frag);

	/**
	 * J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。 取得業務別占比之利潤貢獻度
	 * 
	 * docKind 1：借款人 2：借款人暨關係戶
	 */
	public JSONObject getAttributesGroupByBC3_CD(JSONObject jsonData,
			boolean docKind1, String year, String custKey, String dupNo,
			String dateS, String dateE);

	/**
	 * M-112-0140 「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度，另以附註方式說明薪轉戶持有信用卡之貢獻度。
	 */
	void importL120s04b_SalaryCard(String mainId, String custId,
			String dupNo, String queryDateS, String queryDateE, Properties pop,
			JSONObject jsonData) throws CapException;

	/**
	 * J-110-0330 AO帳務管理員 LNF013
	 * 
	 * @param meta
	 *            簽報書主檔
	 * @param itemType
	 *            額度明細表種類
	 * @param docType
	 *            企/消金代號[1=企金 2=消金]
	 */
	public void gfnLNF013(L120M01A meta);

	public void gfnInsertLNF013(L120M01A meta);

	public void gfnDeleteLNF013(L120M01A meta);

	/**
	 * J-112-0183 額度明細表隱藏110年特定工廠欄位
	 * 
	 * @param mainId
	 * @return
	 */
	public boolean checkIsMarketingList110Hide(Date caseDate);

	/**
	 * J-112-0183 額度明細表隱藏新創產業欄位
	 * 
	 * @param mainId
	 * @return
	 */
	public boolean checkIsStartUpHide(Date caseDate);

	void backElf442(L120M01A meta);

	public void setRealEstateApprovedLoanPercent(List<L140M01O> l140m01oList,
												 BigDecimal approvedPercent);

	public void saveL140M01O(List<L140M01O> l140m01oList);

	public void setRealEstateApprovedLoanPercent(L140M01O l140m01o,
			BigDecimal approvedPercent);

	public void processL140m01oRealEstateValue(String l140m01a_mainId,
			BigDecimal approvedPercent);

	public String checkL140m01oRealEstateValue(List<L140M01O> l140m01oList,
			BigDecimal approvedPercent);

	public boolean showExceptFlagQA(L120M01A l120m01a);
	
	public boolean showExceptFlagQ2Q7Plus(L140M01A l140m01a);

	/**
	 * J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
	 * J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
	 * 
	 * @param mainId
	 * @return
	 */
	public L120S17A findL120s17aByMainId(String mainId);

	public BigDecimal processLgd(L120M01A l120m01a, L140M01A l140m01a,
			L120M01C l120m01c);

	public void setType_new_20230616(L140M01M l140m01m, String custId,
			List<String> errList, List<String> showList,
			boolean useExist_isLimitCust, String cntrNo, String property,
			String quantLoan);
	public void setType_new_20240614(L140M01M l140m01m, String custId,
			 List<String> errList, List<String> showList,
			 boolean useExist_isLimitCust, String cntrNo, String property,
			 String quantLoan);

	/**
	 * 替換DW收檔時不可被接受字元
	 * 
	 * @param inStr
	 * @return
	 */
	public String replaceDwUnacceptableChar(String inStr);

	public String getTipsMsgForRecycleUse(String landBuildYN, String prodClass,
			String reUse);

	public boolean needShowGeneralLoanTotal(L120M01A l120m01a, String caseBrId,
			String itemType);

	public boolean needShowLoanCountRcTotal(L120M01A l120m01a, String caseBrId,
			String itemType);

	/**
	 * 取得ODS狀態
	 * 
	 * @return
	 */
	public String[] getODS_Status();

	/**
	 * 取得受告誡處分資訊
	 * 
	 * @param idNo
	 * @return Map<String, Object>
	 */
	public Map<String, Object> queryOdsCmfwarnp(String idNo);

	public L120M01C findL120M01C_refMainId(String refMainId);

	/**
	 * 取得 舊同一額度屬[專案種類為22-辦理企業戶購置廠辦整批分戶貸款]之筆數
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @return int 筆數
	 */
	int queryProjClass22Count(String cntrNo);

	public L140S05A findL140s05aByMainId(String mainId);

	public L120S11A findL120s11aByMainIdCsutIdItemSeq(String mainId,
			String custId, String dupNo, Integer itemSeq);

	public List<L120S11A> findL120s11aByMainIdCustId(String mainId,
			String custId, String dupNo);

	/**
	 * 撈合併關係企業資料(海外與當地往來)
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param itemSeq
	 * @return
	 */
	public L120S11A_LOC findL120s11a_locByMainIdCsutIdItemSeq(String mainId,
			String custId, String dupNo, Integer itemSeq);

	/**
	 * 撈合併關係企業資料(海外與當地往來)
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<L120S11A_LOC> findL120s11a_locByMainIdCustId(String mainId,
			String custId, String dupNo);

	
	/**
	 * G-113-0036_11850_B1001 Web e-Loan動審核准時要將額度明細表傳送至聯行
	 * 
	 * @param meta
	 *            簽報書主檔
	 * @param l161s01as
	 *            要傳送的額度明細表
	 * @param l160m01a
	 *            動用審核表主檔
	 */
	public void L1601M01AsendToL141M01A(L120M01A meta, List<L140M01A> l140m01as, L160M01A l160m01a);
	
	/**
	 * J-113-0035 ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意承諾待追蹤ESG連結條款」的登錄機制
	 * @param oid
	 * @return
	 */
	public L140S12A findL140s12aByOid(String oid);
	
	/**
	 * J-113-0349 新增綠色授信、社會責任授信暨永續績效連結授信提示訊息
	 * @param listL140m01a
	 * @return
	 */
	public StringBuffer cfmMsg_esg(List<L140M01A> l140m01as);
	
	/**
	 * J-113-0442 赤道原則相關欄位檢核
	 * @param l120s01q
	 * @return
	 */
	public String epsCheckMsg(L120S01Q l120s01q);
	
	public List<L140S12A> findL140s12aByOids(String[] oids);

	public List<L140S12A> findL140s12aListByMainId(String mainId);
	
	public List<L140S12A> findL140s12aListByCaseMainId(String caseMainId);

	public L140S12A findL140s12aMaxSeqNumByMainId(String mainId);

	public void saveL140s12aList(List<L140S12A> list);
	
	public void deleteL140s12as(List<L140S12A> list);
	
	public boolean resetL140s12aAllSeqNum(String mainId);
	
	public String getL140s12aStr(String mainId, L140M01A l140m01a, Properties pop);

	public String checkIsBlockNewCaseSigningCaseByProdKindAndAmount(L140S02A l140s02a, BigDecimal currentApplyAmt);

	public void setType_new_20240919(L140M01M l140m01m, String custId, List<String> errList, List<String> showList,
			boolean useExist_isLimitCust, String cntrNo, String property, String quantLoan);

}