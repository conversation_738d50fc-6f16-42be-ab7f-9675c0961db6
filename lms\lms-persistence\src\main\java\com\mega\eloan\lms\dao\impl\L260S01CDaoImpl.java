/* 
 * L260S01CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L260S01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L260S01C;

/** 貸後管理實價登錄檔 **/
@Repository
public class L260S01CDaoImpl extends LMSJpaDao<L260S01C, String>
	implements L260S01CDao {

	@Override
	public L260S01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L260S01C> findByMainId(String mainId, boolean notIncDel) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(notIncDel){
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		}
		search.setMaxResults(Integer.MAX_VALUE);
		List<L260S01C> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L260S01C> findByIndex01(String mainId, String cntrNo){
		ISearch search = createSearchTemplete();
		List<L260S01C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L260S01C> findByIndex02(String raspFileOid, boolean notIncDel){
		ISearch search = createSearchTemplete();
		List<L260S01C> list = null;
		if (raspFileOid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "raspFileOid", raspFileOid);
		if(notIncDel){
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		}
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L260S01C> findByIndex03(String mainId, String refMainId, boolean notIncDel){
		ISearch search = createSearchTemplete();
		List<L260S01C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (refMainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "refMainId", refMainId);
		if(notIncDel){
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		}
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}