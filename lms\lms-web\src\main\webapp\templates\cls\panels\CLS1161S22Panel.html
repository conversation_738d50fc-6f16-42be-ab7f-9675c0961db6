<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<div id="C160M02ADiv" class="content">
				<div>
					<div style='float:left;'>	
					<table border='0' class='tb2'>
						<tr>
							<td class='hd2'><th:block th:text="#{'C161M02A.custId'}"></th:block>&nbsp;</td>							
							<td><input type="text" id="findBaseDataId" name="findBaseDataId" maxlength="10" size="10" class="upText" />
								&nbsp;&nbsp;			
								<button type="button" id="findBaseDataIdBt" class="forview">
				                    <span class="text-only"><th:block th:text="#{'button.filter'}">篩選</th:block></span>					
				                </button>			
							</td>
						</tr>				
	                </table>
					</div>
					<div style='float:left;margin-left:45px;'>
						<button type="button" id="btEjcicQueryPrint" name="btEjcicQueryPrint" class="forview"><span class="text-only">聯徵EJCIC查詢列印</span></button>
						<button type="button" id="btEtchQueryPrint" name="btEtchQueryPrint" class="forview"><span class="text-only">票信ETCH查詢列印</span></button>
						<button type="button" id="btExpAdj" name="btExpAdj" class="forview"><span class="text-only">匯出</span></button>
						<th:block th:if="${bt_for912_visible}">
								<button type="button" id="btOpenQ" name="btOpenQ" class="forview"><span class="text-only">開啟等級評分表</span></button>
						</th:block>
					</div>
					
					<div style='clear:both;'>
					</div>
				</div>
				<fieldset>
					<legend><b><th:block th:text="#{'title.tab02'}">詳細資料</th:block></b></legend>
					<div id="gridview"></div>
				</fieldset>		
				<!-- 非房貸信用評分表 -->
		<div id="scoreNotHouseLoanSheet" ></div>
		</div>
		
		<script type="text/javascript">loadScript('pagejs/cls/CLS1161S22Panel');</script>
	</th:block>
</body>
</html>
