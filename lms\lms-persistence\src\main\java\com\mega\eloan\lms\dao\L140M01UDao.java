/* 
 * L140M01UDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01U;

/** 共同行銷資訊檔 **/
public interface L140M01UDao extends IGenericDao<L140M01U> {

	L140M01U findByOid(String oid);
	
	List<L140M01U> findByMainId(String mainId);

	List<L140M01U> findByIndex01(String mainId);

	List<L140M01U> findByIndex02(String cntrNo, String type);
	
	L140M01U findByIndex03(String mainId, String type);
}