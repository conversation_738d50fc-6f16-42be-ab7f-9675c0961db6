//J-110-0986_05097_B1001 於簽報書新增LGD欄位
var gCanShowDetail = "N";
//J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
var gCanEditReadOnly = "N";
var _openerLockDoc140s08= false;
//J-112-0210_05097_B1002 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
var gShowIsElcreComItem = "";

$(function() {
    gridViewLGD();
    gridViewEAD();
    gridViewS21C();
    setItems();
    gridViewGuarantorBestLgd();

    $("#addL120S21A").click(function(){
        openEAD(null, null, null);
    });

    $("#newCntrNoCo_s21a").click(function(){
        $("#newCntrNoCo_s21aBox").thickbox({
            title: "",
            width: 400,
            height: 150,
            modal: true,
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var type = $("input[name=newCntrNoCo_s21aRadio]:checked").val();
                    var cntrNoCo_s21a = $("#cntrNoCo_s21a").val();
                    if ($.trim(cntrNoCo_s21a) != "" && type != "del") {
                        return CommonAPI.showErrorMessage(i18n.lms1405s08["errMsg04"]);
                    }
                    if(type == "new") {
                    	 var newCoBrno_s21a = $.trim($("#newCoBrno_s21a").val());
                        $.ajax({
                            handler: "lms1405s08formhandler",
                            action: "getNewCntrNoCo_s21a",
                            data: {
                                cntrNoCo_s21a: cntrNoCo_s21a,
                                newCoBrno_s21a: newCoBrno_s21a,
                                mainId: responseJSON.mainid }
                            }).done(function(obj){
                                $("#cntrNoCo_s21a").val(obj.newCntrNoCo_s21aStr);
                                $.thickbox.close();
                        });
                    } else if(type == "org") {
                        var orgCntrNoCo_s21a = $.trim($("#orgCntrNoCo_s21a").val());
                        if (orgCntrNoCo_s21a == "") {
                            return CommonAPI.showErrorMessage(i18n.lms1405s08["chkInput"]
                                    + i18n.lms1405s08["L120S21A.cntrNoCo_s21a"]);
                        }
                        $.ajax({
                            handler: "lms1405s08formhandler",
                            action: "chkCntrNoCo_s21a",
                            data: {
                                cntrNoCo_s21a: cntrNoCo_s21a,
                                orgCntrNoCo_s21a: orgCntrNoCo_s21a,
                                mainId: responseJSON.mainid,
                                save: true }
                            }).done(function(obj){
                                if (obj.msg && obj.msg != "") {
                                    return API.showErrorMessage(obj.msg);
                                } else {
                                    $("#cntrNoCo_s21a").val(orgCntrNoCo_s21a);
                                    $.thickbox.close();
                                }
                        });
                    } else if(type == "del") {
                        $.ajax({
                            handler: "lms1405s08formhandler",
                            action: "cleanCntrNoCo_s21a",
                            data: {
                                cntrNoCo_s21a: cntrNoCo_s21a,
                                mainId: responseJSON.mainid }
                            }).done(function(obj){
                                $("#cntrNoCo_s21a").val('');
                                $.thickbox.close();
                        });
                    }
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    });

    $("input[name=newCntrNoCo_s21aRadio]").click(function(){
        if ("org" == $(this).val()) {
            $("#originalInput_s21a").show();
        } else {
            $("#originalInput_s21a").hide();
            $("#orgCntrNoCo_s21a").val('');
        }
    });
    
    $("input[name=newCntrNoCo_s21aRadio]").click(function(){
        if ("new" == $(this).val()) {
            $("#newBrnoInput").show();
        } else {
            $("#newBrnoInput").hide();
            $("#newCoBrno_s21a").val('');
        }
    });
    

    $("#queryCntrNoCo_s21a").click(function(){
        var cntrNoCo_s21a = $.trim($("#cntrNoCo_s21a").val());
        if(cntrNoCo_s21a == ""){
            return CommonAPI.showErrorMessage(i18n.lms1405s08["chkInput"]
                    + i18n.lms1405s08["L120S21A.cntrNoCo_s21a"]);
        }

        $.ajax({
            handler: "lms1405s08formhandler",
            data: {
                formAction: "importCoData",
                mainId: responseJSON.mainid,
                cntrNoCo_s21a: cntrNoCo_s21a }
            }).done(function(obj){
                $("#currCo_s21a").val(obj.currCo_s21a);
                $("#factAmtCo_s21a").val(obj.factAmtCo_s21a);
                reloadLgdView();
                $("#cntrNo_s21aGrid").jqGrid("setGridParam", {
                    postData: { // 這裡call的func是GirdHandler
                        formAction: "queryL120s21a",
                        cntrNoCo_s21a: cntrNoCo_s21a,
                        mainId: responseJSON.mainid
                    },
                    search: true
                }).trigger("reloadGrid");
        });
    });

    $("#addS21A").click(function(){
        openEADsub(null, null, null);
    });

    $("#delS21A").click(function(){
        var select  = $("#cntrNo_s21aGrid").getGridParam('selrow');
        if(select == "" || select == null || select == undefined){
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        var cntrNoCo_s21a = $.trim($("#cntrNoCo_s21a").val());
        var BFcntrNoCo_s21a = $.trim($("#BFcntrNoCo_s21a").val());
        var rows = $("#cntrNo_s21aGrid").getGridParam('selarrrow');
    	var list = "";
    	var sign = ",";
    	var delCount = 0;
    	for ( var i = 0; i < rows.length; i++) { // 將所有已選擇的資料存進變數list裡面
    		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
    			var data = $("#cntrNo_s21aGrid").getRowData(rows[i]);
    			list += ((list == "") ? "" : sign) + data.oid;
    			delCount = delCount +1;
    		}
    	}
        var isNew = false;
        var gridCount=$("#cntrNo_s21aGrid").jqGrid('getGridParam','records');
        if(cntrNoCo_s21a == "" && BFcntrNoCo_s21a == ""){
            // 外層的新增
            if(gridCount <= delCount ){    // 最後一筆刪除
                isNew = true;
            }
        } else {
            // 修改 - 有共用序號 起碼一筆
            if(gridCount <= delCount ){
                return CommonAPI.showErrorMessage(i18n.lms1405s08["errMsg02"]);
            }
        }
 
        
    	if (list == "") {
    		CommonAPI.showMessage(i18n.lms1405s08["errMsg02"]);
    		return;
    	}
    	
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
            if(b){
                var data = $("#cntrNo_s21aGrid").getRowData(select);
                $.ajax({
                    handler: "lms1405s08formhandler",
                    data: {
                        formAction: "delL120S21AByOid",
                        mainId: responseJSON.mainid,
                        listOid : list,
            			sign : sign
                        //oid : data.oid
						}
                    }).done(function(obj){
                        if(isNew){
                            $.thickbox.close(); // EadDetailThickbox
                            $("#gridviewEAD").trigger("reloadGrid");
                            $("#cntrNo_s21aGrid").jqGrid("setGridParam", {
                                postData: { // 這裡call的func是GirdHandler
                                    formAction: "queryL120s21a",
                                    cntrNoCo_s21a: cntrNoCo_s21a,
                                    mainId: responseJSON.mainid
                                },
                                search: true
                            }).trigger("reloadGrid");
                            
                        } else {
                            $("#cntrNo_s21aGrid").jqGrid("setGridParam", {
                                postData: { // 這裡call的func是GirdHandler
                                    formAction: "queryL120s21a",
                                    cntrNoCo_s21a: cntrNoCo_s21a,
                                    mainId: responseJSON.mainid
                                },
                                search: true
                            }).trigger("reloadGrid");
                        }
                        reloadLgdView();
                });
            }
        });
    });

    $("#impS21A").click(function(){
        var cntrNoCo_s21a = $.trim($("#cntrNoCo_s21a").val());
        if(cntrNoCo_s21a == ""){
            return CommonAPI.showErrorMessage(i18n.lms1405s08["chkInput"]
                    + i18n.lms1405s08["L120S21A.cntrNoCo_s21a"]);
        }
        $.ajax({
            handler: "lms1405s08formhandler",
            data: {
                formAction: "getLatestLnf252",
                mainId: responseJSON.mainid,
                cntrNoCo_s21a: cntrNoCo_s21a }
            }).done(function(obj){
            	reloadLgdView();
                $("#cntrNo_s21aGrid").jqGrid("setGridParam", {
                    postData: { // 這裡call的func是GirdHandler
                        formAction: "queryL120s21a",
                        cntrNoCo_s21a: cntrNoCo_s21a,
                        mainId: responseJSON.mainid
                    },
                    search: true
                }).trigger("reloadGrid");
        });
    });
    
    //J-110-0485_05097_B1006 Web e-Loan授信簽報新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
    $("#impS21aAll").click(function(){
        $.ajax({
            handler: "lms1405s08formhandler",
            data: {
                formAction: "getLatestLnf252All",
                mainId: responseJSON.mainid }
            }).done(function(obj){
            	reloadLgdView();
        });
    });

    $("#delL120S21A").click(function(){
//        var select  = $("#gridviewEAD").getGridParam('selrow');
//        if(select == "" || select == null || select == undefined){
//            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
//        }
        
        var rows = $("#gridviewEAD").getGridParam('selarrrow');
    	var list = "";
    	var sign = ",";
    	for ( var i = 0; i < rows.length; i++) { // 將所有已選擇的資料存進變數list裡面
    		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
    			var data = $("#gridviewEAD").getRowData(rows[i]);
    			list += ((list == "") ? "" : sign) + data.cntrNoCo_s21a;
    		}
    	}
    	if (list == "") {
    		CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
    		return;
    	}
    	
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
            if(b){
                //var data = $("#gridviewEAD").getRowData(select);
                $.ajax({
                    handler: "lms1405s08formhandler",
                    data: {
                        formAction: "delL120S21A",
                        mainId: responseJSON.mainid,
                        listOid : list,
            			sign : sign
                        //cntrNoCo_s21a: data.cntrNoCo_s21a
						}
                    }).done(function(obj){
                        $("#gridviewEAD").trigger("reloadGrid");
                        reloadLgdView();
                });
            }else{
                return ;
            }
        });
    });

    $("#impALoan").click(function(){
    	
    	var gridCount=$("#gridviewEAD").jqGrid('getGridParam','records');
    	
    	if(gridCount > 0){
    		CommonAPI.iConfirmDialog({
				message: i18n.lms1405s08["L120S21A.message04"], //"是否要先清除已存在之資料
				buttons: API.createJSON([{
					key: i18n.def.yes,
					value: function(){
						$.thickbox.close();
						$.ajax({
			                handler: "lms1405s08formhandler",
			                data: {
			                    formAction: "impALoan",
			                    mainId: responseJSON.mainid,
			                    delFlag:"Y" }
			                }).done(function(obj){
			                    $("#gridviewEAD").trigger("reloadGrid");
			                    reloadLgdView();
			            });
					}
				}, {
					key: i18n.def.no,
					value: function(){
						$.thickbox.close();
						$.ajax({
			                handler: "lms1405s08formhandler",
			                data: {
			                    formAction: "impALoan",
			                    mainId: responseJSON.mainid,
			                    delFlag:"N" }
			                }).done(function(obj){
			                    $("#gridviewEAD").trigger("reloadGrid");
			                    reloadLgdView();
			            });
					}
				}, {
					key: i18n.def.cancel,
					value: function(){
						$.thickbox.close();
					}
				}])
			});
    	}else{
    		$.ajax({
                handler: "lms1405s08formhandler",
                data: {
                    formAction: "impALoan",
                    mainId: responseJSON.mainid,
                    delFlag:"N" }
                }).done(function(obj){
                    $("#gridviewEAD").trigger("reloadGrid");
                    reloadLgdView();
            });
    	}
    	 
    	 
        
    });

    $("#calcEAD").click(function(){
        $.ajax({
            handler: "lms1405s08formhandler",
            data: {
                formAction: "calcEAD",
                mainId: responseJSON.mainid }
            }).done(function(obj){
            	$("#gridviewEAD").trigger("reloadGrid");
            	reloadLgdView();
                
            	if(obj.errorMsg != ""){
            		return CommonAPI.showErrorMessage(obj.errorMsg);
            	}
 
        });
    });

    $("#impCntr").click(function(){
        $.ajax({
            handler: "lms1405s08formhandler",
            data: {
                formAction: "impCntr",
                mainId: responseJSON.mainid }
            }).done(function(obj){
            	reloadLgdView();
        });
    });

    $("#calcLGD").click(function(){
        $.ajax({
            handler: "lms1405s08formhandler",
            data: {
                formAction: "calcLGD",
                mainId: responseJSON.mainid }
            }).done(function(obj){
            	reloadLgdView();
        });
    });
    
    
    $("#addS21C").click(function(){
        openS21C(null, null, null);
    });

    $("#delS21C").click(function(){
        var rows = $("#gridviewS21C").getGridParam('selarrrow');
    	var list = "";
    	var sign = ",";
    	var delCount = 0;
    	for ( var i = 0; i < rows.length; i++) { // 將所有已選擇的資料存進變數list裡面
    		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
    			var data = $("#gridviewS21C").getRowData(rows[i]);
    			list += ((list == "") ? "" : sign) + data.oid;
    			delCount = delCount +1;
    		}
    	}
 
    	if (list == "") {
    		CommonAPI.showMessage(i18n.lms1405s08["errMsg02"]);
    		return;
    	}
    	
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
            if(b){
               $.ajax({
                    handler: "lms1405s08formhandler",
                    data: {
                        formAction: "delL120S21CByOid",
                        listOid : list,
            			sign : sign
                        //oid : data.oid
						}
                    }).done(function(obj){
                    	var cntrNoS21c = $("#formLgdDetail").find("#cntrNo_s21b").val();
                    	updateCollateralRecoveryOth(cntrNoS21c);
                    	$.thickbox.close(); // EadDetailThickbox
                        $("#gridviewS21C").trigger("reloadGrid");
                });
            }
        });
    });

    $("#impS21C").click(function(){
    	 
    	var gridCount=$("#gridviewS21C").jqGrid('getGridParam','records');
    	if (gridCount > 0) {
    		// confirmDelete=是否確定刪除?
            CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
                if(b){
                	applyL120s21cCmsData();
                }
            });
    	}else{
    		applyL120s21cCmsData();
    	}
 
    });
    
    $("#btnCreateLGDExl").click(function(){
   	 
    	$.form.submit({
    	     url: "../../simple/FileProcessingService",
    	     target: "_blank",
    	     data: {
	    	     mainId: responseJSON.mainId,
	    	     fileDownloadName: "LMS120S21.xls",
	    	     serviceName: "lmslgdxlsservice"
    	     }
    	});
 
    });
    
    //J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
    $("input[name=headItem1_s21b]").click(function(){
        if ("Y" == $(this).val()) {
            $(".show_gutPercent_s21b").show();
        } else {
            $(".show_gutPercent_s21b").hide();
            $("#gutPercent_s21b").val('');
        }
    });
    
    $("input[name=unionFlag]").click(function(){
        if ("Y" == $(this).val()) {
            $(".show_unionFlag").show();
        } else {
            $(".show_unionFlag").hide();
            $("#unionCurr").val('');
            $("#unionAmt").val('');
            $("#syndAmt").val('');
        }
    });
    
    //J-112-0210_05097_B1005 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
    $("input[name=unionFlag_s21c]").click(function(){
        if ("Y" == $(this).val()) {
            $(".show_unionAmt_s21c").show();
        } else {
            $(".show_unionAmt_s21c").hide();
            $("#unionCurr_s21c").val('');
            $("#unionAmt_s21c").val('');
            $("#syndAmt_s21c").val('');
        }
    });
    
    //J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
    $("input[name=hasGuarantor_s21b]").change(function(){
        if ("Y" == $(this).val()) {
        	$(".showHasGuarantor_s21b_Y").show();
        } else {
        	$(".showHasGuarantor_s21b_Y").hide();
        	$("input[name='isGuarantorEffect_s21b']:radio:checked" ).prop( "checked" ,false);
        	$("input[name='isGuarantorEffect_s21b']").trigger("change");
        }
    });
    
    //J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
    $("input[name=isGuarantorEffect_s21b]").change(function(){
    	//J-114-XXX1 LGD合格保證人納入境外保證人
        if ("1" == $(this).val() || "2" == $(this).val() || "Y" == $(this).val()) {
            $(".showIsGuarantorEffect_s21b_Y").show();
            if ("1" == $(this).val()  ) {
            	$(".showIsGuarantorEffect_s21b_Y_1").show();
            }else{
            	$(".showIsGuarantorEffect_s21b_Y_1").hide();
            }
            if ("2" == $(this).val() ) {
            	$(".showIsGuarantorEffect_s21b_Y_2").show();
            }else{
            	$(".showIsGuarantorEffect_s21b_Y_2").hide();
            }
        } else {
        	$(".showIsGuarantorEffect_s21b_Y").find(':input').not(':button, :submit, :reset, :hidden').prop('checked', false).prop('selected', false).not(':checkbox, :radio').val('');
        	$(".showIsGuarantorEffect_s21b_Y").find('.field').val('');
        	$(".showIsGuarantorEffect_s21b_Y").hide();
        }
    });
    
  //J-114-XXX1 LGD合格保證人納入境外保證人
    $("#guarantorCrdType2_s21b").change(function(){
    	$("#guarantorGradeOrg2_s21b").val('');
    	$("#guarantorGradeNew2_s21b").val('');
    });
    //J-114-XXX1 LGD合格保證人納入境外保證人
    $("#chgGuarantorGradeNew2").click(function(){
    	chgGuarantorGradeNew2();
    });
     
    //J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
    $("#impGuaS21b").click(function(){
    	setGuarantorBestLgd();
    });
    
    //J-112-0210_05097_B1002 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
    $("input[name=colCoUseFlag_s21c]").click(function(){
        if ("Y" == $(this).val()) {
            $("#show_colShareRate_s21c").show();
        } else {
            $("#show_colShareRate_s21c").hide();
            //$("#colShareRate_s21c").val('');
        }
    });
    
    //J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
    $("#colKind_s21c").change(function(){
        if ("05" == $(this).val().substring(0, 2) ) {
            $("#show_cmsGrtrt_s21c").show();
            
            //J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
            $(".hideRgstInfo").find(':input').not(':button, :submit, :reset, :hidden').prop('checked', false).prop('selected', false).not(':checkbox, :radio').val('');
            $(".hideRgstInfo").hide();
            //L120S21C.colTimeValue_s21c_05=送保金額
            //L120S21C.colTimeValue_s21c=估值
            $("#colTimeValue_s21c_title").val(i18n.lms1405s08["L120S21C.colTimeValue_s21c_05"]);
        } else {
            $("#show_cmsGrtrt_s21c").hide();
            $("#cmsGrtrt_s21c").val('');
            
            //J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
            var collkind = $(this).val().substring(0, 2);
            if ("01" == collkind || "02" == collkind ) {
            	 $(".hideRgstInfo").show();
            }else{
            	$(".hideRgstInfo").find(':input').not(':button, :submit, :reset, :hidden').prop('checked',false).prop('selected',false).not(':checkbox, :radio').val('');
                $(".hideRgstInfo").hide();
            }
           
            //L120S21C.colTimeValue_s21c_05=送保金額
            //L120S21C.colTimeValue_s21c=估值
            $("#colTimeValue_s21c_title").val(i18n.lms1405s08["L120S21C.colTimeValue_s21c"]);
            
        }
        
        //J-112-0210_05097_B1002 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
        //股票、公司債、可轉換公司債擔保品  自建時請增加  是否為「同一關係企業」之勾選欄位
        var value = $(this).val();
        $("#show_isElcreCom_s21c").hide();
        if(gShowIsElcreComItem){   
			$.each(gShowIsElcreComItem, function(i,v){   
				if (value.indexOf(v) > -1) {
					$("#show_isElcreCom_s21c").show();
				}
			});
		}
 
    });
    
});

function applyL120s21cCmsData(){
	var $form = $("#formLgdDetail");
	var cntrNo_s21b = $.trim($("#formLgdDetail").find("#cntrNo_s21b").val());
    $.ajax({
        handler: "lms1405s08formhandler",
        data: {
            formAction: "applyL120s21cCmsData",
            mainId: responseJSON.mainid,
            cntrNo: cntrNo_s21b }
        }).done(function(obj){
        	//清除畫面上的比率
        	updateCollateralRecoveryOth(cntrNo_s21b);
        	
            $("#gridviewS21C").jqGrid("setGridParam", {
                postData: { // 這裡call的func是GirdHandler
                    formAction: "queryL120s21c",
                    cntrNo_s21c: cntrNo_s21b,
                    mainId: responseJSON.mainid
                },
                search: true
            }).trigger("reloadGrid");
    });
}

function checkEadCo(isClose){
    var cntrNoCo_s21a = $.trim($("#cntrNoCo_s21a").val());
    var BFcntrNoCo_s21a = $.trim($("#BFcntrNoCo_s21a").val());
    if(cntrNoCo_s21a == ""){
        if(BFcntrNoCo_s21a == ""){
            // 完全新增一個共用序號
            // 檢查下方L120S21A Grid 是否有資料，有的話一定要有共用序號
            if(isClose){
                var gridCount=$("#cntrNo_s21aGrid").jqGrid('getGridParam','records');
                if(gridCount == 0){
                } else {
                    return CommonAPI.showErrorMessage(i18n.lms1405s08["chkInput"]
                            + i18n.lms1405s08["L120S21A.cntrNoCo_s21a"]);
                }
            } else {
                return CommonAPI.showErrorMessage(i18n.lms1405s08["chkInput"]
                        + i18n.lms1405s08["L120S21A.cntrNoCo_s21a"]);
            }
        } else {
            // 修改原共用額度
            return CommonAPI.showErrorMessage(i18n.lms1405s08["chkInput"]
                    + i18n.lms1405s08["L120S21A.cntrNoCo_s21a"]);
        }
    }

    var currCo_s21a = $.trim($("#currCo_s21a option:selected").val());
    var factAmtCo_s21a = $.trim($("#factAmtCo_s21a").val());
    if(!isClose){
        if(currCo_s21a == "" || factAmtCo_s21a == ""){
            return CommonAPI.showErrorMessage(i18n.lms1405s08["chkInput"]
                    + i18n.lms1405s08["L120S21A.co_s21a"]);
        }
    }

	return true;
}

function gridViewEAD(){
    $("#gridviewEAD").iGrid({
        height: "230px",
        width: "100%",
        needPager: false,
        multiselect: false,
        handler: "lms1201gridhandler",
		multiselect: true,
        postData: {
            formAction: "queryL120s21aDistinct",
            mainId: responseJSON.mainid
        },
        loadComplete: function(){
            $('#gridviewEAD a').click(function(e){
                // 避免<a href="#"> go to top
                e.preventDefault();
            });
        },
        colModel: [{
            colHeader: i18n.lms1405s08["L120S21A.cntrNoCo_s21a"],
            name: 'cntrNoCo_s21a',
            align: 'center',
            width: 60,
            sortable: false,
            formatter: 'click',
            onclick: openEAD
        },{
            colHeader: i18n.lms1405s08["L120S21A.currCo_s21a"],   //額度共管幣別
            name: 'currCo_s21a',
            align: 'center',
            width: 40,
            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21A.co_s21a"],    //額度共管限額
            name: 'factAmtCo_s21a',
            align: 'right',
            width: 100,
            sortable: false,
            formatter: 'currency',
            formatoptions:{
                thousandsSeparator: ",",
                decimalPlaces: 2    //小數點到第幾位
            }
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridviewEAD").getRowData(rowid);
            openEAD(null, null, data);
        }
    });

    $("#cntrNo_s21aGrid").iGrid({
        needPager: false,
        multiselect: true,
        handler: "lms1201gridhandler",
        postData: {
            formAction: "queryL120s21a",
            cntrNoCo_s21a: $.trim($("#formEadDetail").find("#cntrNoCo_s21a").val()),
            mainId: responseJSON.mainid
        },
        loadComplete: function(){
            $('#cntrNo_s21aGrid a').click(function(e){
                // 避免<a href="#"> go to top
                e.preventDefault();
            });
        },
        colModel: [{
            colHeader: i18n.lms1405s08["L120S21A.cntrNo_s21a"],
            name: 'cntrNo_s21a',
            align: 'center',
            width: 100,
            sortable: false,
            formatter: 'click',
            onclick: openEADsub
        },{
            colHeader: i18n.lms1405s08["L120S21A.currentApply_s21a"],
            name: 'currentApply_s21a',
            align: 'right',
            width: 100,
            sortable: false
//        },{
//            colHeader: i18n.lms1405s08["L120S21A.bl_s21a"],
//            name: 'bl_s21a',
//            align: 'right',
//            width: 100,
//            sortable: false
//        },{
//            colHeader: i18n.lms1405s08["L120S21A.rcv_s21a"],
//            name: 'rcv_s21a',
//            align: 'right',
//            width: 100,
//            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21A.reUse_s21a"],    //循環註記
            name: 'reUse_s21a',
            align: 'center',
            width: 60,
            sortable: false    
        },{
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#cntrNo_s21aGrid").getRowData(rowid);
            openEADsub(null, null, data);
        }
    });
}

function gridViewLGD(){
	
	if ($("#gridviewLGD_1").length) {
	//簡易
    $("#gridviewLGD_1").iGrid({
        height: "230px",
        width: "100%",
        needPager: false,
        multiselect: false,
        handler: "lms1201gridhandler",
        postData: {
            formAction: "queryL120s21b_1",
            mainId: responseJSON.mainid
        },
        loadComplete: function(){
            $('#gridviewLGD_1 a').click(function(e){
                // 避免<a href="#"> go to top
                e.preventDefault();
            });
        },
        colModel: [{
            colHeader: i18n.lms1405s08["L120S21B.custId_s21b"],//借款人名稱
            name: 'custId_s21b',
            align: 'left',
            width: 35,
            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21B.custLgd"]+"(%)",
            name: 'custLgd',
            align: 'right',
            width: 25,
            sortable: false
        },{
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            
        }
    });
	}
    
    
    //詳細
    $("#gridviewLGD").iGrid({
        height: "230px",
        width: "100%",
        needPager: false,
        multiselect: false,
        handler: "lms1201gridhandler",
        postData: {
            formAction: "queryL120s21b",
            mainId: responseJSON.mainid
        },
        loadComplete: function(){
            $('#gridviewLGD a').click(function(e){
                // 避免<a href="#"> go to top
                e.preventDefault();
            });
        },
        colModel: [{
            colHeader: i18n.lms1405s08["L120S21B.custId_s21b"],//借款人名稱
            name: 'custId_s21b',
            align: 'left',
            width: 35,
            sortable: false,
            formatter: 'click',
            onclick: openLGD
        },{
            colHeader: i18n.lms1405s08["L120S21B.cntrNo_s21b"],
            name: 'cntrNo_s21b',
            align: 'center',
            width: 35,
            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21B.cntrEad_s21b"],
            name: 'cntrEad_s21b',
            align: 'right',
            width: 40,
            sortable: false,
            formatter: 'currency',
            formatoptions:{
                thousandsSeparator: ",",
                decimalPlaces: 2    //小數點到第幾位
            }
        },{
            colHeader: i18n.lms1405s08["L120S21B.grid3"],  //信保
            name: 'headItem1_s21b',
            align: 'center',
            width: 16,
            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21B.grid4"], //公司保證
            name: 'hasGuarantor_s21b',
            align: 'center',
            width: 16,
            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21B.grid5"], //聯貸
            name: 'unionFlag',
            align: 'center',
            width: 16,
            sortable: false    
        },{
            colHeader: i18n.lms1405s08["L120S21B.grid1"],  //已建檔擔保品回收
            name: 'collateralRecoveryCms',
            align: 'right',
            width: 40,
            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21B.grid2"],  //未建檔擔保品回收
            name: 'collateralRecoveryOth',
            align: 'right',
            width: 40,
            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21B.creditRecovery"],  //信用保證回收
            name: 'creditRecovery',
            align: 'right',
            width: 40,
            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21B.expectLgd"]+"(%)",
            name: 'expectLgd',
            align: 'right',
            width: 25,
            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21B.custLgd"]+"(%)",
            name: 'custLgd',
            align: 'right',
            width: 25,
            sortable: false,
            hidden: true
        },{
            colHeader: i18n.lms1405s08["L120S21B.bussType_s21b"],   //LGD業務種類    J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
            name: 'bussType_s21b',
            align: 'left',
            width: 25,
            sortable: false,
            hidden: true   //J-111-0083_05097_B1002 Web e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類    
        },{
            colHeader: i18n.lms1405s08["L120S21B.bussLgd"]+"(%)",   //業務種類LGD  J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
            name: 'bussLgd',
            align: 'right',
            width: 25,
            sortable: false,
            hidden: true   //J-111-0083_05097_B1002 Web e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
        },{
            colHeader: i18n.lms1405s08["L120S21B.isStandAloneAuth_s21b"],   //屬本行授信業務授權準則得單獨劃分之業務類  J-111-0083_05097_B1002 Web e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
            name: 'isStandAloneAuth_s21b',
            align: 'left',
            width: 25,
            sortable: false            
        },{
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridviewLGD").getRowData(rowid);
            openLGD(null, null, data);
        }
    });
}

function gridViewS21C(){
    $("#gridviewS21C").iGrid({
        height: "230px",
        width: "100%",
        needPager: false,
        multiselect: true,
        localFirst: true,
        handler: "lms1201gridhandler",
        postData: {
            formAction: "queryL120s21c",
            cntrNo_s21c: $.trim($("#formLgdDetail").find("#cntrNo_s21b").val()),
            mainId: responseJSON.mainid
        },
        loadComplete: function(){
            $('#gridviewS21C a').click(function(e){
                // 避免<a href="#"> go to top
                e.preventDefault();
            });
        },
        colModel: [{
            colHeader: i18n.lms1405s08["L120S21C.collType_s21c"], //建檔種類
            name: 'collType_s21c',
            align: 'center',
            width: 20,
            sortable: true
        },{
            colHeader: i18n.lms1405s08["L120S21C.colKind_s21c"], //擔保品種類
            name: 'colKind_s21c',
            align: 'center',
            width: 20,
            sortable: true,
            formatter: 'click',
            onclick: openS21C
        },{
            colHeader: i18n.lms1405s08["L120S21C.colCurr_s21c"], //擔保品幣別
            name: 'colCurr_s21c',
            align: 'center',
            width: 14,
            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21C.colTimeValue_s21c"], //購置時價
            name: 'colTimeValue_s21c',
            align: 'right',
            width: 40,
            sortable: false    //J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
        },{
        	//J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
            colHeader: i18n.lms1405s08["L120S21C.colPreRgstAmt_s21c"], //前順位設定金額
            name: 'colPreRgstAmt_s21c',
            align: 'right',
            width: 40,
            sortable: false  
        },{
        	//J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
            colHeader: i18n.lms1405s08["L120S21C.colRgstAmt_s21c"], //擔保品設定金額
            name: 'colRgstAmt_s21c',
            align: 'right',
            width: 40,
            sortable: false  
        },{
            colHeader: i18n.lms1405s08["L120S21C.colCoUseFlag_s21c"], //是否與其他額度共用
            name: 'colCoUseFlag_s21c',
            align: 'center',
            width: 10,
            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21C.colShareRate_s21c"], //分配比率
            name: 'colShareRate_s21c',
            align: 'right',
            width: 20,
            sortable: false
        },{
            colHeader: i18n.lms1405s08["L120S21C.colRecoveryTwd_s21c"], //分配後擔保品回收Twd
            name: 'colRecoveryTwd_s21c',
            align: 'right',
            width: 40,
            sortable: false //J-110-0485_05097_B1008 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
        },{
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridviewS21C").getRowData(rowid);
            openS21C(null, null, data);
        }
    });
}

function setItems(){
    var obj = CommonAPI.loadCombos(["Common_Currcy", "LGD_CollKind",
        "LGD_ProdRecvType", "LGD_ArAccManagerType"]);

    $("#formEadDetail").find("#currCo_s21a").setItems({
        item: obj.Common_Currcy,
        format: "{value} - {key}",
        space: true
    });
    $("#formEadDetailSub").find("#currentApplyCurr_s21a").setItems({
        item: obj.Common_Currcy,
        format: "{value} - {key}",
        space: true
    });
    
    //J-110-0485_05097_B1002 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
    //2022-01-19 研議e-Loan授信管理系統之LGD及額度暴險估算規則相關議題
//    $("#formEadDetailSub").find("#blCurr_s21a").setItems({
//        item: obj.Common_Currcy,
//        format: "{value} - {key}",
//        space: true
//    });
//    $("#formEadDetailSub").find("#rcvCurr_s21a").setItems({
//        item: obj.Common_Currcy,
//        format: "{value} - {key}",
//        space: true
//    });

//    $("#formLgdDetail").find(".CollKind").setItems({
//        item: obj.LGD_CollKind,
//        format: "{value} - {key}",
//        space: true
//    });
    $("#formLgdDetail").find(".Curr").setItems({
        item: obj.Common_Currcy,
        format: "{value} - {key}",
        space: true
    });

    $("#formLgdDetail").find("#prodRecvType").setItems({
        item: obj.LGD_ProdRecvType,
        format: "{value} - {key}",
        space: true
    });

    $("#formLgdDetail").find("#arAccManagerType_s21b").setItems({
        item: obj.LGD_ArAccManagerType,
        format: "{value} - {key}",
        space: true
    });
    
    
    $("#formLgdDetail_S21C").find(".CollKind").setItems({
        item: obj.LGD_CollKind,
        format: "{value} - {key}",
        space: true
    });
    
    
    $("#formLgdDetail_S21C").find(".Curr").setItems({
        item: obj.Common_Currcy,
        format: "{value} - {key}",
        space: true
    });
    
    //J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
    $("#formLgdDetail_S21C").find(".CollKind").find("option[value=999901]").prop("disabled", true);
    
    //J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
    $("#formLgdDetail_S21C").find(".CollKind").find("option[value=020100]").prop("disabled", true);
}

function openEAD(cellvalue, options, rowObject) {
    var $form = $("#formEadDetail");
    $form.reset();

    $("#cntrNo_s21aGrid").jqGrid("setGridParam", {
        postData: { // 這裡call的func是GirdHandler
            formAction: "queryL120s21a",
            cntrNoCo_s21a: (rowObject== null ? "" : rowObject.cntrNoCo_s21a),
            mainId: responseJSON.mainid
        },
        search: true
    }).trigger("reloadGrid");

    var buttons = {
        "saveData": function(){
            if ($form.valid()) {
                if(checkEadCo(false)){
                    var gridCount=$("#cntrNo_s21aGrid").jqGrid('getGridParam','records');
                    if(gridCount == 0){
                        return CommonAPI.showErrorMessage(i18n.lms1405s08["chkInput"]
                                + i18n.lms1405s08["L120S21A.cntrNoCo_s21a"]);
                    }
                    $.ajax({
                        handler: "lms1405s08formhandler",
                        data: $.extend($form.serializeData(), {
                            BFcntrNoCo_s21a: $("#BFcntrNoCo_s21a").val(),
                            formAction: "saveL120S21A",
                            mainId: responseJSON.mainid
                        })
						}).done(function(obj){
                            $("#gridviewEAD").trigger("reloadGrid");
                    });
                }
            }
        },
        "close": function(){
            if(checkEadCo(true)){
                $.thickbox.close();
                $("#gridviewEAD").trigger("reloadGrid");
            }
        }
    }

    $.ajax({
        handler: "lms1405s08formhandler",
        action: "queryL120s21a",
        data: {
            cntrNoCo_s21a: (rowObject== null ? "" : rowObject.cntrNoCo_s21a),
            mainId: responseJSON.mainid }
        }).done(function(obj){
            $form.injectData(obj);

            $("#EadDetailThickbox").thickbox({
                title: "",
                width: 700,
                height: 400,
                modal: true,
                readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                i18n: i18n.def,
                buttons: buttons
            });
    });
}

function openEADsub(cellvalue, options, rowObject) {
    var $form = $("#formEadDetailSub");
    $form.reset();

    var buttons = {
        "saveData": function(){
            if ($form.valid()) {
                $.ajax({
                    handler: "lms1405s08formhandler",
                    data: $.extend($form.serializeData(), $("#formEadDetail").serializeData(), {
                        formAction: "saveL120S21Asub",
                        oid: $form.find("#oid_s21a").val(),
                        mainId: responseJSON.mainid,
                        type: (rowObject== null ? "new" : "edit")
                    })
					}).done(function(obj){
                        $.thickbox.close();
                        reloadLgdView();
                        $("#cntrNo_s21aGrid").jqGrid("setGridParam", {
                            postData: { // 這裡call的func是GirdHandler
                                formAction: "queryL120s21a",
                                cntrNoCo_s21a: $.trim($("#formEadDetail").find("#cntrNoCo_s21a").val()),
                                mainId: responseJSON.mainid
                            },
                            search: true
                        }).trigger("reloadGrid");
                });
            }
        },
        "close": function(){
            $.thickbox.close();
        }
    }

    $.ajax({
        handler: "lms1405s08formhandler",
        action: "queryL120s21aSub",
        data: {
            oid: (rowObject== null ? "" : rowObject.oid) }
        }).done(function(obj){
            $form.injectData(obj);

            if(rowObject== null){   // 全新案
                $("#cntrNo_s21a").prop("disabled", false);
            } else {    // 修改 不能動額度序號
                $("#cntrNo_s21a").prop("disabled", true);
            }

            $("#EadDetailSubThickbox").thickbox({
                title: "",
                width: 500,
                height: 250,
                modal: true,
                readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                i18n: i18n.def,
                buttons: buttons
            });
    });
}

function openLGD(cellvalue, options, rowObject) {
    var $form = $("#formLgdDetail");
    $form.reset();

    $("#gridviewS21C").jqGrid("setGridParam", {
        postData: { // 這裡call的func是GirdHandler
            formAction: "queryL120s21c",
            cntrNo_s21c: (rowObject== null ? "" : rowObject.cntrNo_s21b),
            mainId: responseJSON.mainid
        },
        search: true
    }).trigger("reloadGrid");
    
    
    $("input[name=headItem1_s21b]").click(function(){
        if ("Y" == $(this).val()) {
            $(".show_gutPercent_s21b").show();
        } else {
            $(".show_gutPercent_s21b").hide();
            $("#gutPercent_s21b").val('');
        }
    });
    
    $("input[name=unionFlag]").click(function(){
        if ("Y" == $(this).val()) {
            $(".show_unionFlag").show();
        } else {
            $(".show_unionFlag").hide();
            $("#unionCurr").val('');
            $("#unionAmt").val('');
            $("#syndAmt").val('');
        }
    });
    
    var buttons = {
        "saveData": function(){
            if ($form.valid()) {
                $.ajax({
                    handler: "lms1405s08formhandler",
                    data: $.extend($form.serializeData(), {
                        formAction: "saveL120s21b",
                        oid: rowObject.oid,
                        mainId: responseJSON.mainid,
                        formLgdDetail: JSON.stringify($("#formLgdDetail").serializeData())
                    })
					}).done(function(obj1){
                    	$form.injectData(obj1);
                        $("#gridviewLGD").trigger("reloadGrid");
                        $("#gridviewLGD_1").trigger("reloadGrid");
                });
            }
        },
        "close": function(){
            $.thickbox.close();
            $("#gridviewLGD").trigger("reloadGrid");
            $("#gridviewLGD_1").trigger("reloadGrid");
        }
    }


    $.ajax({
        handler: "lms1405s08formhandler",
        action: "queryL120s21b",
        data: {
            oid: rowObject.oid }
        }).done(function(obj){

            $form.injectData(obj);
            
        	if(gCanShowDetail == "Y"){
        		$form.find('.showDetail').show();
        	}else{
        		$form.find('.showDetail').hide();
        	}
        	
        	//J-112-0210_05097_B1002 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
        	gShowIsElcreComItem = obj.showIsElcreComItem;
        	
        	//J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
        	$("input[name='hasGuarantor_s21b']:checked").triggerHandler("change");
        	
        	//J-114-XXX1 LGD合格保證人納入境外保證人
//        	if(responseJSON.typCd != "5"){
//        		$form.find('.showIsGuarantorEffect_s21b_Y_TW').show();
//        	}else{
//        		// [下午 02:41] 楊竺軒(風險控管處,高級辦事員)
//				// 建霖好，不好意思依授審處的意思，國內海外連保公司判斷條件要相同，海外請加回股票上市上櫃情形，一樣要是台灣上市櫃公司才可以符合條件，謝謝~
//        		$form.find('.showIsGuarantorEffect_s21b_Y_TW').show();
//        	}

            $("#LgdDetailThickbox").thickbox({
                title: "",
                width: 1000,
                height: 600,
                modal: true,
                open:function(){
                	//$form.find(':input').not(':button, :submit, :reset, :hidden').attr("readonly", true).attr("disabled", "disabled");
                },
                readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                i18n: i18n.def,
                buttons: buttons
            });
    });

    
}


function openS21C(cellvalue, options, rowObject) {
    var $form = $("#formLgdDetail_S21C");
    $form.reset();
    
    
    $("#formLgdDetail_S21C").find("#oid_s21c").val("");
    
    //J-112-0210_05097_B1005 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
    var unionFlag = $("#formLgdDetail").find("input[name=unionFlag]:checked").val();
    //alert("unionFlag="+unionFlag);
	if(unionFlag == "Y"){
    	$("#formLgdDetail_S21C").find(".show_unionFlag_s21c").show();
    }else{
    	$("#formLgdDetail_S21C").find(".show_unionFlag_s21c").hide();
    }
    
    
    var type =  (rowObject== null ? "new" : "edit");
    var cntrNoS21c = $("#formLgdDetail").find("#cntrNo_s21b").val();
    if(type == "new") {
    	$("#formLgdDetail_S21C").find("#cntrNo_s21c").val(cntrNoS21c);
    	$("#formLgdDetail_S21C").find("#collType_s21c").val("2"); //預設未建檔
    }
    

    var buttons = {
        "saveData": function(){
            if ($form.valid()) {
                $.ajax({
                    handler: "lms1405s08formhandler",
                    data: $.extend($form.serializeData(), {
                        formAction: "saveL120s21c",
                        oid: $form.find("#oid_s21c").val(),
                        mainId: responseJSON.mainid,
                        type: type,
                        cntrNoS21c : cntrNoS21c,
                        formLgdDetail_S21C: JSON.stringify($form.serializeData())
                    })
					}).done(function(obj){
                    	$form.injectData(obj);
                    	updateCollateralRecoveryOth(cntrNoS21c);
                        $("#gridviewS21C").trigger("reloadGrid");
                });
            }
        },
        "close": function(){
            $.thickbox.close();
        }
    }
    
    $.ajax({
        handler: "lms1405s08formhandler",
        action: "queryL120s21c",
        data: {
            oid: (rowObject== null ? "" : rowObject.oid),
            cntrNoS21c : cntrNoS21c }
        }).done(function(obj){
            $form.injectData(obj);
             
        	if(gCanShowDetail == "Y"){
        		$form.find('.showDetail').show();
        	}else{
        		$form.find('.showDetail').hide();
        	}
        	
        	//J-112-0210_05097_B1005 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
        	resetL120s21cUnionRate();

            $("#formLgdDetail_S21C").find("#oid_s21c").val(obj.oid_s21c);
            
            var buttons ;
            //J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
            if($form.find("#collType_s21c").val() != "2"){
            	//已建檔
            	
            	//顯示擔保品估價報告書資訊
            	$form.find(".showCollType1").show();
            	
            	buttons = {
            	        "close": function(){
            	            $.thickbox.close();
            	        }
            	    }
            }else{
            	//未建檔
            	
            	//隱藏擔保品估價報告書資訊
            	$form.find(".showCollType1").hide();
            	
            	buttons = {
        	        "saveData": function(){
        	            if ($form.valid()) {
        	                $.ajax({
        	                    handler: "lms1405s08formhandler",
        	                    data: $.extend($form.serializeData(), {
        	                        formAction: "saveL120s21c",
        	                        oid: $form.find("#oid_s21c").val(),
        	                        mainId: responseJSON.mainid,
        	                        type: type,
        	                        cntrNoS21c : cntrNoS21c,
        	                        formLgdDetail_S21C: JSON.stringify($form.serializeData())
        	                    })
								}).done(function(obj){
        	                    	$form.injectData(obj);
        	                    	//J-112-0210_05097_B1005 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
        	                        resetL120s21cUnionRate();
        	                    	updateCollateralRecoveryOth(cntrNoS21c);
        	                        $("#gridviewS21C").trigger("reloadGrid");
        	                });
        	            }
        	        },
        	        "close": function(){
        	            $.thickbox.close();
        	        }
        	    }
            }
 
            //J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
            $("#colKind_s21c").trigger("change");
             
            $("#LgdDetailThickbox_S21C").thickbox({
                title: "",
                width: 1000,
                height: 400,
                modal: true,
                open:function(){
                	if($form.find("#collType_s21c").val() != "2"){
                		//J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
                		//已建檔全部都不能編輯
                		$form.find(':input').not(':button, :submit, :reset, :hidden').prop("readonly", true).prop("disabled", true); 
                	}else{
                		$form.find(':input').not(':button, :submit, :reset, #collType_s21c').prop("readonly", false).prop("disabled", false);
                	}
                },
                readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                i18n: i18n.def,
                buttons: buttons
            });
    });
}

//L120S21C擔保品異動後(新增、刪除)，重新計算L120S21B的collateralRecoveryOth
function updateCollateralRecoveryOth(cntrNoS21c){
	var $form = $("#formLgdDetail");
	$form.reset();
	$.ajax({
         handler: "lms1405s08formhandler",
         data: $.extend($form.serializeData(), {
             formAction: "updateCollateralRecoveryOth",
             mainId: responseJSON.mainid,
             cntrNoS21c:cntrNoS21c,
             formLgdDetail: JSON.stringify($("#formLgdDetail").serializeData())
         })
		 }).done(function(obj){
        	 //$form.find('#collateralRecoveryOth').val(obj.collateralRecoveryOth);
        	 $form.injectData(obj);
             $("#gridviewLGD").trigger("reloadGrid");
             $("#gridviewLGD_1").trigger("reloadGrid");
     });
}


function reloadLgdView(){
	$("#gridviewLGD").trigger("reloadGrid");
    $("#gridviewLGD_1").trigger("reloadGrid");
}


function ShowOrHide(button_act,sh_item) {
	var chk_value=$('#'+button_act).html();
	//alert(chk_value);
	if (chk_value==i18n.lms1405s08["button.showDetailLGD_Y"]) {
		$('#'+sh_item).show();
		$('#'+sh_item+'_S').hide();
		$('#'+button_act).html(i18n.lms1405s08["button.showDetailLGD_N"]);
	} else {
		$('#'+sh_item).hide();
		$('#'+sh_item+'_S').show();
		$('#'+button_act).html(i18n.lms1405s08["button.showDetailLGD_Y"]);
	}
}


initDfd.done(function(auth) {

	$.ajax({
        handler: "lms1405s08formhandler",
        action: "canShowDetail",
        async: false,
        data: {}
        }).done(function(json){
        	gCanShowDetail = json.canShowDetail;
        	//J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
        	gCanEditReadOnly = json.canEditReadOnly;
        	_openerLockDoc140s08 = thickboxOptions.readOnly;
        	
        	//J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
//        	if(gCanShowDetail != "Y"){
//        		if (thickboxOptions.readOnly) {
//            		$("#LMS1401S08Form01").find("button").hide();
//            		$("#LMS1401S08Form02").find("button").hide();
//            		$("#formEadDetail").find("button").hide();
//            		$("#formEadDetailSub").find("button").hide();
//            		$("#formLgdDetail").find("button").hide();
//            		$("#formLgdDetail_S21C").find("button").hide();
//            		
//            		$("#formEadDetail").readOnlyChilds(true);
//            		$("#formEadDetailSub").readOnlyChilds(true);
//            		$("#formLgdDetail").readOnlyChilds(true);
//            		$("#formLgdDetail_S21C").readOnlyChilds(true);
//            	}
//        	}else{
//        		_openerLockDoc140s08 = false;
//        	}
        	
        	//J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
        	if (thickboxOptions.readOnly) {
        		if(gCanEditReadOnly != "Y"){
        			$("#LMS1401S08Form01").find("button").hide();
            		$("#LMS1401S08Form02").find("button").hide();
            		$("#formEadDetail").find("button").hide();
            		$("#formEadDetailSub").find("button").hide();
            		$("#formLgdDetail").find("button").hide();
            		$("#formLgdDetail_S21C").find("button").hide();
            		
            		$("#formEadDetail").readOnlyChilds(true);
            		$("#formEadDetailSub").readOnlyChilds(true);
            		$("#formLgdDetail").readOnlyChilds(true);
            		$("#formLgdDetail_S21C").readOnlyChilds(true);
            	}else{
            		_openerLockDoc140s08 = false;
            	}
        	}
        	
        	
        	
 
        	if(gCanShowDetail == "Y"){
        		$('#btnCreateLGDExl').show();
        	}else{
        		$('#btnCreateLGDExl').hide();
        	}
        	
        	//$('#btnShowDetailLGD').show();
        	
    });
	
	
	//J-114-XXX1 LGD合格保證人納入境外保證人
	$("#crdGradeGrid_s21b").iGrid({
        handler: 'lms1810gridhandler',
        height: 270,
        postData: {
        	queryType: "L140S08",
            fcrdType: "1",
            fcrdArea: "1",
            fcrdPred: "1",
            formAction: "query_elfFcrdGrad"
        },
        needPager: false,
        shrinkToFit: true,
        colModel: [{ colHeader: '評等等級', name: 'ratingGrad', sortable: false, align: "left" }]
    });
 
});
 
//J-112-0210_05097_B1001 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
function setGuarantorBestLgd(){ 

	//J-114-XXX1 LGD合格保證人納入境外保證人
	var isGuarantorEffect_s21b = $("#formLgdDetail").find("input[name='isGuarantorEffect_s21b']:radio:checked" ).val(); 
	if(!isGuarantorEffect_s21b){
		//L120S21B.isGuarantorEffect_s21b=本案是否有保證人/共借人符合下列(1)或(2)之所有條件
		return CommonAPI.showErrorMessage(i18n.lms1405s08['L120S21B.isGuarantorEffect_s21b']+i18n.def['val.required']);
	}
    //var doFormAction = isGuarantorEffect_s21b == '2' ? 'queryL140m01i_Lgd_OBU' : 'queryL140m01i_Lgd';
	
	$("#gridViewGuarantorBestLgd").jqGrid("setGridParam", {
		postData : {
			formAction : 'queryL140m01i_Lgd',
			mainId : responseJSON.mainId,
			custId_s21b : $("#formLgdDetail").find("#custId_s21b").val(),
			cntrNo_s21b : $("#formLgdDetail").find("#cntrNo_s21b").val(),
			isGuarantorEffect_s21b: isGuarantorEffect_s21b
		},
		search : true
	}).trigger("reloadGrid");
	
	$("#setGuarantorBestLgdThickBox").thickbox({
        title: i18n.lms1405s08["button.impGuaS21b"], //button.impGuaS21b=引進保證人/共同借款人代表
        width: 800,
        height: 500,
        modal: true,
        align: "center",
        valign: "bottom",
        readOnly: _openerLockDoc140s08,
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var $grid = $("#gridViewGuarantorBestLgd");
                //單筆
                var girdId = $grid.getGridParam('selrow');
                if (!girdId) {
                    //grid_selector=請選擇資料
                    return API.showMessage(i18n.def["grid_selector"]);
                }
                var rowData = $grid.getRowData(girdId);
                $('#formLgdDetail').find(".showIsGuarantorEffect_s21b_Y").find(':input').not(':button, :submit, :reset, :hidden').prop('checked', false).prop('selected',false).not(':checkbox, :radio').val('');
                $('#formLgdDetail').find(".showIsGuarantorEffect_s21b_Y").find('.field').val('');
                if (rowData) {
                	$('#formLgdDetail').find("#guarantorId_s21b").val(rowData.rId);
                	$('#formLgdDetail').find("#guarantorDupNo_s21b").val(rowData.rDupNo);
                	$('#formLgdDetail').find("#guarantorRName_s21b").val(rowData.rName);
                	$('#formLgdDetail').find("#guarantorCrdType_s21b").val(rowData.crdType);
                	$('#formLgdDetail').find("#guarantorCrdTYear_s21b").val(rowData.crdTYear);
                	$('#formLgdDetail').find("#guarantorGradeOrg_s21b").val(rowData.gradeOrg);
                	$('#formLgdDetail').find("#guarantorGradeNew_s21b").val(rowData.gradeNew);
                	$('#formLgdDetail').find("#guarantorNtCode_s21b").val(rowData.ntCode);
                	//J-114-XXX1 LGD合格保證人納入境外保證人
                	if(isGuarantorEffect_s21b =="1"){
                		$('#formLgdDetail').find("#guarantorStockStatus_s21b").val(rowData.stockStatus);
                    	$('#formLgdDetail').find("#guarantorStockNum_s21b").val(rowData.stockNum);
                	}else{
                		$('#formLgdDetail').find("#guarantorStockStatus_s21b").val('');
                    	$('#formLgdDetail').find("#guarantorStockNum_s21b").val('');
                	}
                	
                	//J-114-XXX1 LGD合格保證人納入境外保證人
                	if(isGuarantorEffect_s21b =="2"){
                		//異動後
                		$('#formLgdDetail').find("#guarantorCrdType2_s21b").val(rowData.crdType2);
                    	$('#formLgdDetail').find("#guarantorCrdTYear2_s21b").val(rowData.crdTYear2);
                    	$('#formLgdDetail').find("#guarantorGradeOrg2_s21b").val(rowData.gradeOrg2);
                    	$('#formLgdDetail').find("#guarantorGradeNew2_s21b").val(rowData.gradeNew2);
                    	//異動前
                    	$('#formLgdDetail').find("#guarantorCrdType3_s21b").val(rowData.crdType2);
                    	$('#formLgdDetail').find("#guarantorCrdTYear3_s21b").val(rowData.crdTYear2);
                    	$('#formLgdDetail').find("#guarantorGradeOrg3_s21b").val(rowData.gradeOrg2);
                    	$('#formLgdDetail').find("#guarantorGradeNew3_s21b").val(rowData.gradeNew2); 
                    	
                    	//J-113-0102_05097_B1001 修改e-Loan LGD之公司保證回收率估算規則
                    	$('#formLgdDetail').find("#guarantorStkCatNm_s21b").val(rowData.stkCatNm);
                	}else{
                		$('#formLgdDetail').find(".showIsGuarantorEffect_s21b_Y_2").find(':input').not(':button, :submit, :reset, :hidden').prop('checked',false).prop('selected',false).not(':checkbox, :radio').val('');
                        $('#formLgdDetail').find(".showIsGuarantorEffect_s21b_Y_2").find('.field').val('');
                	}
             
//                    $.ajax({
//                        handler: inits.fhandle,
//                        data: {//把資料轉成json
//                            formAction: "queryContentData",
//                            oid: $("#tabFormId").val(),
//                            cesMainId: rowData.mainId
//                        },
//                        success: function(responseData){
//                            $.thickbox.close();
//                            $('#gridviewNatural').trigger('reloadGrid');
//                            $('#gridviewCorporate').trigger('reloadGrid');
//                        }
//                    });
                	$.thickbox.close();
                }
     
            },
            "cancel": function(){
            	 
                $.thickbox.close();
            }
        }
    });  
}
function gridViewGuarantorBestLgd(){
	
    $("#gridViewGuarantorBestLgd").iGrid({
		needPager: false,
        handler: 'lms1401gridhandler',
        postData: {
            formAction: "queryL140m01i_Lgd",
            mainId : responseJSON.mainId,
            custId_s21b : "",
			cntrNo_s21b : "",
			isGuarantorEffect_s21b : ""  //J-114-XXX1 LGD合格保證人納入境外保證人
        },
        height: 200,
        //width: '100%',
        //autowidth: true,
        shrinkToFit:false,
        sortname: 'rId',
        sortorder: 'asc',
        async: false,
        colModel: [{
            colHeader: i18n.lms1405s08["guaLgdView.rId"],// "統編",
            width: 80,
            name: 'rId',
            sortable: false,
            align: "left"
        }, {
            colHeader: i18n.lms1405s08["guaLgdView.rName"],// "名稱",
            name: 'rName',
            width: 150,
            sortable: false,
            align: "left"    	
        }, {
            colHeader: i18n.lms1405s08["guaLgdView.rType"],//"類別",
            name: 'rType',
            width: 60,
            sortable: false,
            align: "center"
        }, {
            colHeader: i18n.lms1405s08["guaLgdView.ntCode"],//"註冊地國別",
            name: 'ntCode',
            width: 40,
            sortable: false,
            align: "center"	 
        }, {
        	colHeader: i18n.lms1405s08["guaLgdView.crdType"],//"評等種類",
            name: 'crdTypeNm',
            width: 150,
            sortable: false,
            align: "left"
        }, {
        	colHeader: i18n.lms1405s08["guaLgdView.crdTYear"],//"評等日期",
            name: 'crdTYear',
            width: 80,
            sortable: false,
            align: "center"   
        }, {
        	colHeader: i18n.lms1405s08["guaLgdView.gradeOrg"],//"原始評等等級",
            name: 'gradeOrg',
            width: 50,
            sortable: false,
            align: "center"    
        }, {
        	colHeader: i18n.lms1405s08["guaLgdView.gradeNew"],//"轉換後評等等級",
            name: 'gradeNew',
            hidden: true 
        }, {
        	colHeader: i18n.lms1405s08["guaLgdView.stockStatus"],//"上市上櫃情形 ",
            name: 'stockStatusNm',
            width: 50,
            sortable: false,
            align: "center"  
        }, {
        	colHeader: i18n.lms1405s08["guaLgdView.stockNum"],//"股票代號",
            name: 'stockNum',
            width: 50,
            sortable: false,
            align: "center"  
        }, {
        	colHeader: i18n.lms1405s08["guaLgdView.crdType2"],//"外部評等種類", J-114-XXX1 LGD合格保證人納入境外保證人
            name: 'crdTypeNm2',
            width: 100,
            sortable: false,
            align: "left"
        }, {
        	colHeader: i18n.lms1405s08["guaLgdView.crdTYear2"],//"外部評等日期", J-114-XXX1 LGD合格保證人納入境外保證人
            name: 'crdTYear2',
            width: 80,
            sortable: false,
            align: "center"   
        }, {
        	colHeader: i18n.lms1405s08["guaLgdView.gradeOrg2"],//"外部原始評等等級", J-114-XXX1 LGD合格保證人納入境外保證人
            name: 'gradeOrg2',
            width: 100,
            sortable: false,
            align: "center"    
        }, {
        	colHeader: i18n.lms1405s08["guaLgdView.gradeNew2"],//"外部轉換後評等等級", J-114-XXX1 LGD合格保證人納入境外保證人
            name: 'gradeNew2',
            hidden: true     
        }, {
        	// J-113-0102_05097_B1001 修改e-Loan LGD之公司保證回收率估算規則
        	colHeader: i18n.lms1405s08["L120S21B.guarantorStkCatNm_s21b"],//本行所認可之海外交易所掛牌
            name: 'stkCatNmShow',
            width: 200,
            sortable: false,
            align: "left"       
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'rDupNo',
            hidden: true   
        }, {
            name: 'crdType',
            hidden: true    
        }, {
            name: 'stockStatus',
            hidden: true    
        }, {
            name: 'crdType2',   //J-114-XXX1 LGD合格保證人納入境外保證人
            hidden: true   
        }, {
        	// J-113-0102_05097_B1001 修改e-Loan LGD之公司保證回收率估算規則
            name: 'stkCatNm',    
            hidden: true           
        }],
        onSelectRow: function(id){
            
        }
    });
}

//J-114-XXX1 LGD合格保證人納入境外保證人
function chgGuarantorGradeNew2(){ 
	 
	var guarantorCrdType2_s21b = $("#formLgdDetail").find("#guarantorCrdType2_s21b" ).val(); 
	if(!guarantorCrdType2_s21b){
		//L120S21B.guarantorCrdType2_s21b=外部評等種類
		return CommonAPI.showErrorMessage(i18n.lms1405s08['L120S21B.guarantorCrdType2_s21b']+i18n.def['val.required']);
	}
	
	var crdType = guarantorCrdType2_s21b;
	var crdArea = "1";
	var crdPred = "1";
	 
	$("#crdGradeGrid_s21b").jqGrid("setGridParam", {
        postData : {
            handler: "lms1810gridhandler",
            queryType: "L140S08",
            fcrdType: crdType,
            fcrdArea: crdArea,
            fcrdPred: crdPred,
            formAction: "query_elfFcrdGrad"
        },
        search: true
    }).trigger("reloadGrid");
	
	$("#crdGradeThickbox_s21b").thickbox({
        title: "",
        width: 400,
        height: 400,
        align: "center",
        valign: "bottom",
        modal: false,
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                 var selrow = $("#crdGradeGrid_s21b").getGridParam('selrow');
                 if (selrow) {
                    var data =  $("#crdGradeGrid_s21b").getRowData(selrow);
                    if(data.ratingGrad){
                    	 var ratingGrad = data.ratingGrad.split("|");
                         var gradeOrg = ratingGrad[0];
                         var gradeNew = ratingGrad[1];
                         
                         $("#guarantorGradeOrg2_s21b").val( gradeOrg);
                         $("#guarantorGradeNew2_s21b").val( gradeNew );
                    }else{
                    	$("#guarantorGradeOrg2_s21b").val( "");
                        $("#guarantorGradeNew2_s21b").val( "" );
                    }
                   
                  
                    $.thickbox.close();
                 }
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}


function resetL120s21cUnionRate(){
	//J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
    //J-112-0210_05097_B1005 Web e-Loan企金授信修改LGD之進出口自償回收及機器設備回收率等
    var unionFlag_s21c = $("#formLgdDetail_S21C").find("input[name=unionFlag_s21c]:checked").val();
    if(unionFlag_s21c == "Y"){
    	//自建擔保品有自己的聯貸比例
    	var syndAmt_s21c = $("#formLgdDetail_S21C").find("#syndAmt_s21c").val().replace(/,/g,"");    //本行參貸額度(預計)
    	var unionAmt_s21c = $("#formLgdDetail_S21C").find("#unionAmt_s21c").val().replace(/,/g,"");  //聯合授信案總金額(預計)
    	//alert("1:"+syndAmt_s21c+ "-" + unionAmt_s21c );
    	if(!isNaN(syndAmt_s21c) && !isNaN(unionAmt_s21c) ){
    		$("#formLgdDetail_S21C").find("#unionRate").val(Math.round( (syndAmt_s21c *100/unionAmt_s21c) *100    ) /100 );
    	} else{
    		$("#formLgdDetail_S21C").find("#unionRate").val("");
    	}
    }else{
    	var unionFlag = $("#formLgdDetail").find("input[name=unionFlag]:checked").val();
    	//alert("unionFlag=" + unionFlag );
        if(unionFlag == "Y"){
        	 
        	var syndAmt = $("#formLgdDetail").find("#syndAmt").val().replace(/,/g,"");    //本行參貸額度(預計)
        	var unionAmt = $("#formLgdDetail").find("#unionAmt").val().replace(/,/g,"");  //聯合授信案總金額(預計)
        	//alert("2:"+syndAmt+ "-" + unionAmt );
        	if(!isNaN(syndAmt) && !isNaN(unionAmt) ){
        		$("#formLgdDetail_S21C").find("#unionRate").val(Math.round( (syndAmt *100/unionAmt) *100    ) /100 );
        	} else{
        		$("#formLgdDetail_S21C").find("#unionRate").val("");
        	}
        	
        }else{
        	$("#formLgdDetail_S21C").find("#unionRate").val("N.A.");
        }
    }

}
