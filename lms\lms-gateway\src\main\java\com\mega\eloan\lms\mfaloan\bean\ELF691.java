package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** ELF691 聯徵查調資料介接檔 **/
public class ELF691 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/**
	 * 統一編號
	 */
	@Column(name = "ELF691_CUSTID", length = 10, columnDefinition = "CHAR(10)", nullable=false,unique = true)
	private String elf691_custid;
	
	/**
	 * 重複序號
	 */
	@Column(name = "ELF691_DUPNO", length = 1, columnDefinition = "CHAR(1)", nullable=false,unique = true)
	private String elf691_dupno;
	
	/**
	 * 查詢項目
	 */
	@Column(name = "ELF691_Q_ITEM", length = 3, columnDefinition = "CHAR(3)", nullable=false,unique = true)
	private String elf691_q_item;
	
	/**
	 * 查詢理由
	 */
	@Column(name = "ELF691_Q_REASON", length = 3, columnDefinition = "CHAR(3)")	
	private String elf691_q_reason;
	
	/**
	 * 發查分行
	 */
	@Column(name = "ELF691_BRN", length = 3, columnDefinition = "CHAR(3)")	
	private String elf691_brn;
	
	/**
	 * 發查行員代號
	 */
	@Column(name = "ELF691_STAFFNO", length = 6, columnDefinition = "CHAR(6)")	
	private String elf691_staffno;
	
	/**
	 * 發查時間
	 */
//	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ELF691_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf691_tmestamp;
	
	/**
	 * 發查KEY1
	 */
	@Column(name = "ELF691_KEY1", length = 30, columnDefinition = "VARCHAR(30)")
	private String elf691_key1;
	
	/**
	 * 發查KEY2
	 */
	@Column(name = "ELF691_KEY1", length = 30, columnDefinition = "VARCHAR(30)")
	private String elf691_key2;
	
	/**
	 * 發查IP
	 */
	@Column(name = "ELF691_IP", length = 45, columnDefinition = "VARCHAR(45)")
	private String elf691_ip;
	
	/**
	 * 聯徵送出時間
	 */
//	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ELF691_EJ_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf691_ej_tmestamp;

	public String getElf691_custid() {
		return elf691_custid;
	}

	public void setElf691_custid(String elf691_custid) {
		this.elf691_custid = elf691_custid;
	}

	public String getElf691_dupno() {
		return elf691_dupno;
	}

	public void setElf691_dupno(String elf691_dupno) {
		this.elf691_dupno = elf691_dupno;
	}

	public String getElf691_q_item() {
		return elf691_q_item;
	}

	public void setElf691_q_item(String elf691_q_item) {
		this.elf691_q_item = elf691_q_item;
	}

	public String getElf691_q_reason() {
		return elf691_q_reason;
	}

	public void setElf691_q_reason(String elf691_q_reason) {
		this.elf691_q_reason = elf691_q_reason;
	}

	public String getElf691_brn() {
		return elf691_brn;
	}

	public void setElf691_brn(String elf691_brn) {
		this.elf691_brn = elf691_brn;
	}

	public String getElf691_staffno() {
		return elf691_staffno;
	}

	public void setElf691_staffno(String elf691_staffno) {
		this.elf691_staffno = elf691_staffno;
	}

	public Timestamp getElf691_tmestamp() {
		return elf691_tmestamp;
	}

	public void setElf691_tmestamp(Timestamp elf691_tmestamp) {
		this.elf691_tmestamp = elf691_tmestamp;
	}

	public String getElf691_key1() {
		return elf691_key1;
	}

	public void setElf691_key1(String elf691_key1) {
		this.elf691_key1 = elf691_key1;
	}

	public String getElf691_key2() {
		return elf691_key2;
	}

	public void setElf691_key2(String elf691_key2) {
		this.elf691_key2 = elf691_key2;
	}

	public Timestamp getElf691_ej_tmestamp() {
		return elf691_ej_tmestamp;
	}

	public void setElf691_ej_tmestamp(Timestamp elf691_ej_tmestamp) {
		this.elf691_ej_tmestamp = elf691_ej_tmestamp;
	}

	public String getElf691_ip() {
		return elf691_ip;
	}

	public void setElf691_ip(String elf691_ip) {
		this.elf691_ip = elf691_ip;
	}
	
}
