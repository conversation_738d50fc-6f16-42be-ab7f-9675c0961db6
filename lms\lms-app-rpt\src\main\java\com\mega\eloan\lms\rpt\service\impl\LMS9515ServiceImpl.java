/* 
 *  LMS9515ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service.impl;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import jxl.SheetSettings;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.PaperSize;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.dao.L784A01ADao;
import com.mega.eloan.lms.dao.L784M01ADao;
import com.mega.eloan.lms.dao.L784S01ADao;
import com.mega.eloan.lms.dao.L784S07ADao;
import com.mega.eloan.lms.dao.VL784S07A01Dao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.Dw_elf411ovsService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.Lms412Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF447Service;
import com.mega.eloan.lms.mfaloan.service.MisElcsecntService;
import com.mega.eloan.lms.mfaloan.service.MisEllnseekservice;
import com.mega.eloan.lms.mfaloan.service.MisLMS422Service;
import com.mega.eloan.lms.mfaloan.service.MisLNFE0851Service;
import com.mega.eloan.lms.model.L784A01A;
import com.mega.eloan.lms.model.L784M01A;
import com.mega.eloan.lms.model.L784S01A;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.eloan.lms.model.VL784S07A01;
import com.mega.eloan.lms.rpt.pages.LMS9511V01Page;
import com.mega.eloan.lms.rpt.pages.LMS9515V01Page;
import com.mega.eloan.lms.rpt.service.LMS9515Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS9515ServiceImpl extends AbstractCapService implements
		LMS9515Service {

	protected final Logger logger = LoggerFactory.getLogger(getClass());

	@Resource
	ProdService prodService;

	@Resource
	DocFileDao docFileDao;

	@Autowired
	DocFileService fileService;

	@Resource
	BranchService branchService;

	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	TempDataService tempDataService;

	@Resource
	LMSService lmsService;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	Dw_elf411ovsService dwElf411ovsService;

	@Resource
	Lms412Service lms412Service;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	MisLMS422Service misLms422Service;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisLNFE0851Service misLnfe0851Service;

	@Resource
	MisElcsecntService misElcsecntService;

	@Resource
	MisEllnseekservice misEllnseekService;

	@Resource
	MisELF447Service misElf447Service;

	@Resource
	L784A01ADao l784a01aDao;

	@Resource
	L784M01ADao l784m01aDao;

	@Resource
	L784S07ADao l784s07aDao;

	@Resource
	L784S01ADao l784s01aDao;

	@Resource
	VL784S07A01Dao vl784s07a01Dao;

	/**
	 * 取得上個月的第一天
	 * 
	 * @return
	 */
	@SuppressWarnings("unused")
	private Date getExMonthDate(int month) {
		return this.getExMonthDate(month, true);
	}

	/**
	 * 取得上個月的第一天
	 * 
	 * @return
	 */
	@SuppressWarnings("deprecation")
	private Date getExMonthDate(int month, boolean setFirstResult) {
		Date dateStartDate = CapDate.getCurrentTimestamp();
		dateStartDate.setMonth(dateStartDate.getMonth() + month);
		if (setFirstResult) {
			dateStartDate.setDate(1);
		}
		return dateStartDate;
	}

	@Override
	public List<Map<String, Object>> findType1ByBrNoAndDate(String ovUnitNo,
			Date dateStartDate, Date dateEndDate, String caseDept, String ctype)
			throws CapException {
		// 功能說明：任何時候於管理報表執行產生"授信契約已逾期控指表",
		// 將擷取DW_LNQUOTOV中之距今2 個月內資料並以 報表方式呈現
		// 距今二個月前之日期
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> rows = dwdbService
				.findDW_LNQUOTOVforNewReportType1ByBrNo(ovUnitNo,
						TWNDate.toAD(dateStartDate), TWNDate.toAD(dateEndDate));
		Map<String, Object> dataCollection = null;
		for (Map<String, Object> dataMap : rows) {
			dataCollection = new LinkedHashMap<String, Object>();
			dataCollection.put("brNo", ovUnitNo);
			String custId = Util.trim(dataMap.get("CUSTID"));
			dataCollection.put("custId", custId);
			Map<String, Object> custMap = misCustdataService.findByIdDupNo(
					custId.length() > 10 ? custId.substring(0, 9) : custId,
					custId.length() > 10 ? custId.substring(9, 10) : "0");
			if (custMap != null) {
				dataCollection.put("custName", Util.trim(custMap.get("CNAME")));
				if (custMap != null)
					custMap.clear();
			}
			dataCollection.put("contract", Util.trim(dataMap.get("CNTRNO")));
			// BEG_DATEG 動用起日(DWADM.DW_LNQUOTOV)
			dataCollection.put("begDate", (Date) dataMap.get("BDATE"));
			// END_DATE 動用迄日(DWADM.DW_LNQUOTOV)
			dataCollection.put("endDate", (Date) dataMap.get("EDATE"));
			// FACT_SWFT 額度幣別
			dataCollection.put("factSwft", Util.trim(dataMap.get("CURR")));
			// FACT_AMT 核准額度(原幣)
			dataCollection.put("factAmt",
					LMSUtil.toBigDecimal(dataMap.get("FACTAMT")));
			// FACT_AMT_NT 額度金額(換算台幣)
			dataCollection.put("factAmtNt",
					LMSUtil.toBigDecimal(dataMap.get("FACTNT")));
			list.add(dataCollection);
		}
		return list;
	}

	// @Override
	// public List<Map<String, Object>> findType9BystartYMendYM(String TWYM,
	// String startTWYM, String endTWYM, String type) throws CapException {
	// List<Map<String, Object>> rows = dwdbService
	// .findDW_ELINSPDT_sel9ByBRANCHIDCHKYMTYPCDYM(startTWYM, endTWYM,
	// TWYM, type);
	// return rows;
	// }

	/**
	 * J-108-0192_05097_B1001 Web e-Loan企金授信新增每季海外營業單位授信報案考核彙總表
	 * 
	 * type9-營業單位授信報案考核彙總表 抓全年跟抓指定月份
	 * 
	 * @param TWYM_START
	 *            本月/本季起始年月
	 * @param TWYM_END
	 *            本月/本季結束年月
	 * @param startTWYM
	 *            開始年月
	 * @param endTWYM
	 *            結束年月
	 * @param type
	 *            排序方式不同
	 * @return
	 * @throws CapException
	 */
	@Override
	public List<Map<String, Object>> findType9BystartYMendYM(String TWYM_START,
			String TWYM_END, String startTWYM, String endTWYM, String type)
			throws CapException {
		List<Map<String, Object>> rows = dwdbService
				.findDW_ELINSPDT_sel9ByBRANCHIDCHKYMTYPCDYM(startTWYM, endTWYM,
						TWYM_START, TWYM_END, type);
		return rows;
	}

	@Override
	public List<L784S01A> findType2ByBrNoAndDate(String rptMode,
			String ovUnitNo, Date dateStartDate, Date dateEndDate,
			String docStatusCode, String docKind, String docStatusCode2)
			throws CapException {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		Map<String, String> subItemeMap = null;
		Map<String, String> subCodeMap = null;
		subCodeMap = prodService.getSubCode();
		subItemeMap = codetypeService.findByCodeType("lms1405m01_SubItem");
		if (subItemeMap == null) {
			subItemeMap = new LinkedHashMap<String, String>();
		}
		if (subCodeMap == null) {
			subCodeMap = new LinkedHashMap<String, String>();
		}
		String mainId = IDGenerator.getUUID();
		List<Map<String, Object>> rows = null;
		if (Util.equals(rptMode, "2")) {
			// Y01、Z01有勾Y時走總行模式，其他走分行模式

			String newOvUnitNo = "";
			IBranch ibranch = branchService.getBranch(ovUnitNo);

			String accGroup = ibranch.getAccGroup();
			if (Util.isNotEmpty(accGroup)) {
				newOvUnitNo = accGroup;
			}

			rows = eloandbBaseService.findforNewReportType2ByAccGroup(
					newOvUnitNo, docStatusCode, docKind,
					TWNDate.toAD(dateStartDate), TWNDate.toAD(dateEndDate),
					docStatusCode2, UtilConstants.Casedoc.typCd.海外,
					UtilConstants.Casedoc.typCd.海外);
		} else if (Util.equals(rptMode, "3")) {
			// J-112-0JJJ_05097_B1001 Web e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
			IBranch ibranch = branchService.getBranch(ovUnitNo);
			String country = Util.trim(ibranch.getCountryType());

			if (Util.equals(UtilConstants.BankNo.東京分行, ovUnitNo)) {
				rows = eloandbBaseService.findforNewReportType2ByCountryHead(
						docStatusCode, country, ovUnitNo,
						TWNDate.toAD(dateStartDate), TWNDate.toAD(dateEndDate),
						docStatusCode2, UtilConstants.Casedoc.typCd.海外,
						UtilConstants.Casedoc.typCd.海外);
			}
		} else {
			// 分行模式
			rows = eloandbBaseService.findforNewReportType2ByBrNo(ovUnitNo,
					docStatusCode, docKind, TWNDate.toAD(dateStartDate),
					TWNDate.toAD(dateEndDate), docStatusCode2,
					UtilConstants.Casedoc.typCd.海外,
					UtilConstants.Casedoc.typCd.海外);

		}

		List<L784S01A> list784s01 = new ArrayList<L784S01A>();
		for (Map<String, Object> dataMap : rows) {
			L784S01A l784s01a = new L784S01A();
			String docType = Util.trim(dataMap.get("DOCTYPE"));
			String brId = Util.trim(dataMap.get("CASEBRID"));
			String custId = Util.trim(dataMap.get("CUSTID"));
			String dupNo = Util.trim(dataMap.get("DUPNO"));
			String cntrNo = Util.trim(dataMap.get("CNTRNO"));
			String custName = Util.trim(dataMap.get("CUSTNAME"));
			String lnSubject = Util.trim(subItemeMap.get(Util.trim(dataMap
					.get("LNSUBJECT"))))
					+ (Integer
							.parseInt(Util.trim(dataMap.get("LNSUBJECTCOUNT"))) > 1 ? prop
							.getProperty("L784M01a.less") : "");
			String lnSubject2 = Util.trim(subCodeMap.get(Util.trim(dataMap
					.get("LNSUBJECT2"))))
					+ (Integer.parseInt(Util.trim(dataMap
							.get("LNSUBJECTCOUNT2"))) > 1 ? prop
							.getProperty("L784M01a.less") : "");
			String currentApplyCurr = Util
					.trim(dataMap.get("CURRENTAPPLYCURR"));
			BigDecimal currentApplyAmt = LMSUtil.toBigDecimal(dataMap
					.get("CURRENTAPPLYAMT"));
			String useDeadline = Util.trim(dataMap.get("USEDEADLINE"));
			String desp1 = Util.trim(dataMap.get("DESP1"));
			String property = Util.trim(dataMap.get("PROPERTY"));
			String randomCode = Util.trim(dataMap.get("RANDOMCODE"));
			Date endDate = (Date) dataMap.get("ENDDATE");
			String staffNo = Util.trim(dataMap.get("STAFFNO"));

			String L120M01A_MainId = Util.trim(dataMap.get("MAINID"));
			String L140M01A_MainId = Util.trim(dataMap.get("REFMAINID"));

			l784s01a.setCustId(custId);
			l784s01a.setBrId(brId);
			l784s01a.setDupNo(dupNo);
			l784s01a.setMainId(mainId);
			l784s01a.setCntrNo(cntrNo);
			l784s01a.setCustName(custName);
			l784s01a.setLnSubject(lnSubject);
			l784s01a.setCurrentApplyCurr(currentApplyCurr);
			l784s01a.setCurrentApplyAmt(currentApplyAmt);
			l784s01a.setUseDeadline(useDeadline);
			l784s01a.setDesp1(desp1);
			l784s01a.setProperty(property);
			l784s01a.setRandomCode(randomCode);
			l784s01a.setEndDate(endDate);
			l784s01a.setStaffNo(staffNo);
			l784s01a.setL120M01A_MainId(L120M01A_MainId);
			l784s01a.setL140M01A_MainId(L140M01A_MainId);
			list784s01.add(l784s01a);
		}
		// 一次儲存多筆
		l784s01aDao.save(list784s01);

		return list784s01;
	}

	@Override
	public List<L784S01A> findType2ByAreaAndDate(String ovUnitNo,
			String docStatusCode, String docKind, String docStatusCode2,
			Date dateStartDate, Date dateEndDate) throws CapException {
		String mainId = IDGenerator.getUUID();
		List<Map<String, Object>> rows = eloandbBaseService
				.findforNewReportType2ByArea(ovUnitNo, docStatusCode,
						docStatusCode2, TWNDate.toAD(dateStartDate),
						TWNDate.toAD(dateEndDate));
		List<L784S01A> list784s01 = new ArrayList<L784S01A>();
		for (Map<String, Object> dataMap : rows) {
			L784S01A l784s01a = new L784S01A();
			String brId = Util.trim(dataMap.get("CASEBRID"));
			String custId = Util.trim(dataMap.get("CUSTID"));
			String dupNo = Util.trim(dataMap.get("DUPNO"));
			String cntrNo = Util.trim(dataMap.get("CNTRNO"));
			String custName = Util.trim(dataMap.get("CUSTNAME"));
			String lnSubject = Util.trim(dataMap.get("LNSUBJECT"));
			String currentApplyCurr = Util
					.trim(dataMap.get("CURRENTAPPLYCURR"));
			BigDecimal currentApplyAmt = LMSUtil.toBigDecimal(dataMap
					.get("CURRENTAPPLYAMT"));
			String useDeadline = Util.trim(dataMap.get("USEDEADLINE"));
			String desp1 = Util.trim(dataMap.get("DESP1"));
			String property = Util.trim(dataMap.get("PROPERTY"));
			String randomCode = Util.trim(dataMap.get("RANDOMCODE"));
			Date endDate = (Date) dataMap.get("ENDDATE");
			String staffNo = Util.trim(dataMap.get("STAFFNO"));
			String L120M01A_MainId = Util.trim(dataMap.get("MAINID"));
			String L140M01A_MainId = Util.trim(dataMap.get("REFMAINID"));

			l784s01a.setBrId(brId);
			l784s01a.setCustId(custId);
			l784s01a.setDupNo(dupNo);
			l784s01a.setMainId(mainId);
			l784s01a.setCntrNo(cntrNo);
			l784s01a.setCustName(custName);
			l784s01a.setLnSubject(lnSubject);
			l784s01a.setCurrentApplyCurr(currentApplyCurr);
			l784s01a.setCurrentApplyAmt(currentApplyAmt);
			l784s01a.setUseDeadline(useDeadline);
			l784s01a.setDesp1(desp1);
			l784s01a.setProperty(property);
			l784s01a.setRandomCode(randomCode);
			l784s01a.setEndDate(endDate);
			l784s01a.setStaffNo(staffNo);
			l784s01a.setL120M01A_MainId(L120M01A_MainId);
			l784s01a.setL140M01A_MainId(L140M01A_MainId);
			list784s01.add(l784s01a);
		}
		// 一次儲存多筆
		l784s01aDao.save(list784s01);

		return list784s01;
	}

	@Override
	public List<Map<String, Object>> findType3ByBrNoAndDate(String ovUnitNo,
			String benDate, String endDate, String caseDept)
			throws CapException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> rows = misEllnseekService
				.findMisEllnseekforNewReportType3ByBrNo(benDate, endDate,
						ovUnitNo);
		Map<String, Object> dataCollection = null;
		for (Map<String, Object> dataMap : rows) {
			dataCollection = new HashMap<String, Object>();
			String custId = Util.trim(dataMap.get("CUSTID"));
			dataCollection.put("custId", custId);
			String dupNo = Util.trim(dataMap.get("DupNo"));
			dataCollection.put("dupNo", dupNo);
			// CUSTDATA(客戶基本資料檔)找 CNAME(中文戶名)
			String cname = findCustData(custId, dupNo);
			dataCollection.put("cname", cname);
			String cntrno = Util.trim(dataMap.get("CNTRNO"));
			dataCollection.put("cntrno", cntrno);
			Date gutcdate = (Date) dataMap.get("GUTCDATE");
			dataCollection.put("gutcdate", gutcdate);
			String projno = Util.trim(dataMap.get("PROJNO"));
			dataCollection.put("projno", projno);
			String property = Util.trim(dataMap.get("PROPERTY"));
			dataCollection.put("property", property);

			String yy = Util.trim(dataMap.get("APPRYY"));
			String mm = Util.trim(dataMap.get("APPRMM"));
			String appdateStr = yy + "-" + mm + "-" + "01";
			dataCollection.put("appdate", appdateStr);
			list.add(dataCollection);
		}
		return list;
	}

	/**
	 * CUSTDATA(客戶基本資料檔)找 CNAME(中文戶名)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */

	@SuppressWarnings("rawtypes")
	private String findCustData(String custId, String dupNo) {
		String cname = "";
		Map dataMap = misCustdataService.findCustdataSelCname(custId, dupNo);
		if (dataMap != null) {
			cname = Util.trim(dataMap.get("CNAME"));
		}
		return cname;
	}

	@Override
	public List<Map<String, Object>> findType5ByBrNoAndDate(List<IBranch> ovUnitNo,
			String benDate, String endDate, String otherCondition)
			throws CapException {
		List<Map<String, Object>> rows = misElf447Service
				.findElf447forNewReportType5ByBrNo(ovUnitNo, benDate, endDate,
						otherCondition);
		// 單位別 ELF447_CHKBRANCH
		// 客戶別(逐案) ELF447_CUSTID
		// 核准敘作額度 ELF447_LTCurr,
		// 案號 ELF447_PROJNO
		// 得分 ELF447_SYSTYPE,

		List<String> brNoIdlist = new ArrayList<String>();
		List<String> custIdlist = new ArrayList<String>();
		List<String> dupNolist = new ArrayList<String>();
		List<String> cnamelist = new ArrayList<String>();
		List<String> projnolist = new ArrayList<String>();
		List<String> systypelist = new ArrayList<String>();
		List<BigDecimal> twdLoanAmtlist = new ArrayList<BigDecimal>();

		for (Map<String, Object> dataMap477 : rows) {
			String brNo = Util.trim(dataMap477.get("ELF447_CHKBRANCH"));
			brNoIdlist.add(brNo);

			String custId = Util.trim(dataMap477.get("ELF447_CUSTID"));
			custIdlist.add(custId);
			String dupNo = Util.trim(dataMap477.get("ELF447_DUPNO"));
			dupNolist.add(dupNo);

			// CUSTDATA(客戶基本資料檔)找 CNAME(中文戶名)
			String cname = findCustData(custId, dupNo);
			cnamelist.add(cname);

			String projno = Util.trim(dataMap477.get("ELF447_PROJNO"));
			projnolist.add(projno);

			String systype = Util.trim(dataMap477.get("ELF447_SYSTYPE"));
			systypelist.add(systype);

			BigDecimal twdLoanAmt = LMSUtil.nullToZeroBigDecimal(dataMap477
					.get("TWD_LOANAMT"));
			// String twdLoanAmt = Util.trim((String)
			// dataMap477.get("TWD_LoanAmt"));
			twdLoanAmtlist.add(twdLoanAmt);

		}

		// 轉成EXCEL
		// produceExcel(size, brNoIdlist, ranMainId, custIdlist, dupNolist,
		// cnamelist, projnolist, systypelist, twdLoanAmtlist);

		return null;
	}

	@Override
	public List<Map<String, Object>> findType6ByBrNoAndDate(String brNo,
			String dataDate, String caseDept, String ctype) throws CapException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		// 授權外,ctype = "2";
		List<Map<String, Object>> rows = eloandbBaseService
				.findL120M01AJoinL120A01A_selAreaSendInfo(dataDate, brNo);

		Map<String, Object> dataCollection = null;
		for (Map<String, Object> dataMap : rows) {
			dataCollection = new HashMap<String, Object>();
			// 分行別
			String caseBrId = Util.trim(dataMap.get("caseBrId"));
			// 統一編號
			String custId = Util.trim(dataMap.get("CUSTID"));
			// 客戶名稱
			String custName = Util.trim(dataMap.get("CUSTNAME"));
			// 報表亂碼
			String randomCode = Util.trim(dataMap.get("RANDOMCODE"));
			// 案號
			String caseNo = Util.trim(dataMap.get("CASENO"));

			// 營運中心負責經辦
			String areaAppraiser = Util.trim(dataMap.get("AREAAPPRAISER"));
			// 分行最後送件日
			String areaSendInfo = Util.trim(dataMap.get("sendLastTime"));
			// 分屬(企/個金案件)
			String docType = Util.trim(dataMap.get("docType"));

			dataCollection.put("year", dataDate.split("-")[0]);
			dataCollection.put("month", dataDate.split("-")[1]);
			dataCollection.put("dd", dataDate.split("-")[2]);
			dataCollection.put("caseBrId", caseBrId);
			dataCollection.put("custId", custId);
			dataCollection.put("custName", custName);
			dataCollection.put("randomCode", randomCode);
			dataCollection.put("caseNo", caseNo);
			dataCollection.put("areaAppraiser", areaAppraiser);
			dataCollection.put("areaSendInfo", areaSendInfo);
			dataCollection.put("docType", docType);
			list.add(dataCollection);

		}// while end

		return list;
	}

	@Override
	public List<L784S07A> findType7ByBrNoAndDate(String apprYY, String apprMM,
			String caseDept, String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 功能說明：月初由法金處產生「每月常董會報告事項彙總及申報案件數統計表」
		// 參數說明：YY,MM-資料年月, tBranchID- 產生該分行的資料(若為ALL表示產生所有分行的資料)
		// caseDept - 1.法金處案件 2. 個金處案件, 3:全部
		this.delete784s07(apprYY, apprMM, mainId);

		List<L784S07A> list784s07 = new ArrayList<L784S07A>();
		List<Map<String, Object>> rows = null;
		rows = misElcsecntService.findElcsecntforType7ByAllBrNoAndDate(apprYY,
				apprMM);
		List<IBranch> branchList = lmsService.getBranchList();
		for (Map<String, Object> dataMap : rows) {
			L784S07A l784s07a = new L784S07A();
			// 新作
			BigDecimal tCitem1 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM1REC"));
			BigDecimal tCitem1Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM1AMT"));
			// 續約
			BigDecimal tCitem2 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM2REC"));
			BigDecimal tCitem2Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM2AMT"));
			// 變更條件
			BigDecimal tCitem3 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM3REC"));
			BigDecimal tCitem3Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM3AMT"));
			// 合計

			// 無擔保授信
			BigDecimal tCitem4 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM4REC"));
			BigDecimal tCitem4Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM4AMT"));
			// 擔保授信
			BigDecimal tCitem5 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM5REC"));
			BigDecimal tCitem5Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM5AMT"));
			// 申報案件
			BigDecimal tCitem6 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM6REC"));
			BigDecimal tCitem6Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM6AMT"));
			// 核准案件
			BigDecimal tCitem7 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM7REC"));
			BigDecimal tCitem7Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM7AMT"));
			// 授權內案件
			BigDecimal tCitem8 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM8REC"));
			BigDecimal tCitem8Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM8AMT"));
			// 提會案件
			BigDecimal tCitem9 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM9REC"));
			BigDecimal tCitem9Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM9AMT"));
			// 常董會案件
			BigDecimal tCitem10 = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM10REC"));
			BigDecimal tCitem10Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM10AMT"));
			// 覆審案件
			BigDecimal tCitem11 = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM11REC"));
			BigDecimal tCitem11Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM11AMT"));

			l784s07a.setBrNo(Util.trim(dataMap.get("BRNO")));
			l784s07a.setMainId(mainId);
			l784s07a.setApprMM(apprMM);
			l784s07a.setApprYY(apprYY);
			l784s07a.setCaseDept(Util.trim(dataMap.get("CASEDEPT")));
			l784s07a.setCItem1Rec(tCitem1 == null ? null : tCitem1.intValue());
			l784s07a.setCItem1Amt(tCitem1Amt);
			l784s07a.setCItem2Rec(tCitem2 == null ? null : tCitem2.intValue());
			l784s07a.setCItem2Amt(tCitem2Amt);
			l784s07a.setCItem3Rec(tCitem3 == null ? null : tCitem3.intValue());
			l784s07a.setCItem3Amt(tCitem3Amt);
			l784s07a.setCItem4Rec(tCitem4 == null ? null : tCitem4.intValue());
			l784s07a.setCItem4Amt(tCitem4Amt);
			l784s07a.setCItem5Rec(tCitem5 == null ? null : tCitem5.intValue());
			l784s07a.setCItem5Amt(tCitem5Amt);
			l784s07a.setCItem6Rec(tCitem6 == null ? null : tCitem6.intValue());
			l784s07a.setCItem6Amt(tCitem6Amt);
			l784s07a.setCItem7Rec(tCitem7 == null ? null : tCitem7.intValue());
			l784s07a.setCItem7Amt(tCitem7Amt);
			l784s07a.setCItem8Rec(tCitem8 == null ? null : tCitem8.intValue());
			l784s07a.setCItem8Amt(tCitem8Amt);
			l784s07a.setCItem9Rec(tCitem9 == null ? null : tCitem9.intValue());
			l784s07a.setCItem9Amt(tCitem9Amt);
			l784s07a.setCItem10Rec(tCitem10 == null ? null : tCitem10
					.intValue());
			l784s07a.setCItem10Amt(tCitem10Amt);
			l784s07a.setCItem11Rec(tCitem11 == null ? null : tCitem11
					.intValue());
			l784s07a.setCItem11Amt(tCitem11Amt);
			l784s07a.setCreateTime(CapDate.getCurrentTimestamp());
			l784s07a.setCreator(user.getUserId());
			l784s07a.setUpdateTime(CapDate.getCurrentTimestamp());
			l784s07a.setUpdater(user.getUserId());
			list784s07.add(l784s07a);
			branchList.remove(branchService.getBranch(l784s07a.getBrNo()));
		}// while end
			// 一次儲存多筆

		for (IBranch branch : branchList) {
			L784S07A l784s07a = new L784S07A();
			l784s07a.setBrNo(Util.trim(branch.getBrNo()));
			l784s07a.setMainId(mainId);
			l784s07a.setApprMM(apprMM);
			l784s07a.setApprYY(apprYY);
			l784s07a.setCaseDept(caseDept);
			l784s07a.setCreateTime(CapDate.getCurrentTimestamp());
			l784s07a.setCreator(user.getUserId());
			l784s07a.setUpdateTime(CapDate.getCurrentTimestamp());
			l784s07a.setUpdater(user.getUserId());
			list784s07.add(l784s07a);
		}
		this.savel784s07aList(list784s07);

		// 當為空值則為TRUE
		return list784s07;

	}

	@Override
	public List<L784S07A> findL784S07AByMainId(String mainId)
			throws CapException {
		return l784s07aDao.findByMainId(mainId);
	}

	@Override
	public List<L784S07A> findL784S07AByMainIdBrNo(String mainId, String brNo)
			throws CapException {
		return l784s07aDao.findByMainIdBrNo(mainId, brNo);
	}

	@Override
	public List<L784S01A> findL784S01AByMainId(String mainId)
			throws CapException {
		return l784s01aDao.findByMainId(mainId);
	}

	@Override
	public List<L784S07A> findL784S07A(String mainId, String brNo,
			String apprYY, String apprMM, String caseDept) throws CapException {
		return l784s07aDao
				.findByIndex01(mainId, brNo, apprYY, apprMM, caseDept);
	}

	@Override
	public void savel784s07aList(List<L784S07A> list) {
		l784s07aDao.save(list);
	}

	@Override
	public List<L784S07A> findL784S07AByMainIdApprYM(String mainId,
			String apprYY, String apprMM, String caseDept) throws CapException {
		return l784s07aDao.findByMainIdApprYM(mainId, apprYY, apprMM, caseDept);
	}

	@Override
	public List<Map<String, Object>> findType8ByBrNoAndDate(String brNo,
			Date dataDate, String caseDept, String ctype) {
		// 功能說明：月初由法金處產生「本行各營業單位各級授權範圍內承做授信案件統計表」
		// 參數說明：YY,MM-資料年月
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> rows = misElcsecntService
				.findElcsecntforType8ByAllBrNoAndDate(TWNDate.toTW(dataDate)
						.split("/")[0], TWNDate.toTW(dataDate).split("/")[1],
						ctype);
		for (Map<String, Object> dataMap : rows) {
			Map<String, Object> dataCollection = new HashMap<String, Object>();
			dataCollection.put("year", TWNDate.toAD(dataDate).split("-")[0]);
			dataCollection.put("month", TWNDate.toAD(dataDate).split("-")[1]);
			dataCollection.put("brNo", Util.trim(dataMap.get("brNo")));

			// 新作
			dataCollection.put("tCITEM1",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM1")));
			dataCollection.put("tCITEM1AMT",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM1AMT")));

			// 續約
			dataCollection.put("tCITEM2",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM2")));

			dataCollection.put("tCITEM2AMT",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM2AMT")));

			// 變更條件
			dataCollection.put("tCITEM3",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM3")));

			dataCollection.put("tCITEM3AMT",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM3AMT")));

			// 合計

			// 無擔保授信
			dataCollection.put("tCITEM4",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM4")));

			dataCollection.put("tCITEM4AMT",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM4AMT")));

			// 擔保授信
			dataCollection.put("tCITEM5",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM5")));

			dataCollection.put("tCITEM5AMT",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM5AMT")));

			// 申報案件
			dataCollection.put("tCITEM6",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM6")));

			// 核准案件
			dataCollection.put("tCITEM7",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM7")));

			// 授權內案件
			dataCollection.put("tCITEM8",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM8")));

			// 提會案件
			dataCollection.put("tCITEM9",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM9")));

			// 常董會案件
			dataCollection.put("tCITEM10",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM10")));

			// 覆審案件
			dataCollection.put("tCitem11",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM11")));

			list.add(dataCollection);
		}// while end

		return list;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L784S07A) {
					((L784S07A) model).setUpdater(user.getUserId());
					((L784S07A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if ("".equals(Util.nullToSpace(((L784S07A) model)
							.getCreateTime()))) {
						((L784S07A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L784S07A) model)
							.getCreator()))) {
						((L784S07A) model).setCreator(user.getUserId());
					}
					l784s07aDao.save((L784S07A) model);
				} else if (model instanceof L784S01A) {
					((L784S01A) model).setUpdater(user.getUserId());
					((L784S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if ("".equals(Util.nullToSpace(((L784S01A) model)
							.getCreateTime()))) {
						((L784S01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L784S01A) model)
							.getCreator()))) {
						((L784S01A) model).setCreator(user.getUserId());
					}
					l784s01aDao.save((L784S01A) model);
				} else if (model instanceof L784M01A) {
					((L784M01A) model).setUpdater(user.getUserId());
					((L784M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if ("".equals(Util.nullToSpace(((L784M01A) model)
							.getCreatDate()))) {
						// Date createDate = CapDate.getCurrentTimestamp();
						// createDate.setMonth(createDate.getMonth() - 1 );
						// createDate.setDate(1);
						((L784M01A) model).setCreatDate(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L784M01A) model)
							.getCreateTime()))) {
						((L784M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L784M01A) model)
							.getCreator()))) {
						((L784M01A) model).setCreator(user.getUserId());
					}
					if ("".equals(Util.nullToSpace(((L784M01A) model)
							.getRandomCode()))) {
						((L784M01A) model).setRandomCode(IDGenerator.getUUID());
					}
					l784m01aDao.save((L784M01A) model);
				} else if (model instanceof L784A01A) {
					l784a01aDao.save((L784A01A) model);
				} else if (model instanceof L784S07A) {
					((L784S07A) model).setUpdater(user.getUserId());
					((L784S07A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l784s07aDao.save((L784S07A) model);
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L784S07A.class) {
			return l784s07aDao.findPage(search);
		} else if (clazz == L784M01A.class) {
			return l784m01aDao.findPage(search);
		} else if (clazz == L784S01A.class) {
			return l784s01aDao.findPage(search);
		} else if (clazz == VL784S07A01.class) {
			return vl784s07a01Dao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L784S07A.class) {
			return (T) l784s07aDao.find(oid);
		} else if (clazz == L784S01A.class) {
			return (T) l784s01aDao.find(oid);
		} else if (clazz == L784M01A.class) {
			return (T) l784m01aDao.find(oid);
		}
		return null;
	}

	@Override
	public Map<String, Object> selL784M01A(String rpttype, String year) {
		return eloandbBaseService.selL784m01abyRPTTYPEYear(rpttype, year);
	}

	@Override
	public void delete784s07(String year, String month, String mainId) {
		eloandbBaseService.deleteL784s07(year, month, mainId);
	}

	@Override
	public void saveFlieName(List<?> list, String fileName, int action,
			String brNo, Date dataStartDate, Date dataEndDate,
			String ranMainId, String caseDept, String randomCode) {
		// 指定刪除mainId
		// String mainId = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L784M01A l784m01a = this.findL784m01aByMainId(ranMainId);
		if (l784m01a == null) {
			l784m01a = new L784M01A();
			// 新增一筆至 企金授信管理報表檔(同ranMainId)
			l784m01a.setMainId(ranMainId);
			l784m01a.setRptType(action + "");
			l784m01a.setReportFile(fileName);
			l784m01a.setOwnBrId(brNo);
			l784m01a.setRandomCode(randomCode);
			l784m01a.setRecCount(list.size());
			l784m01a.setBranchId(user.getUnitNo());
			// 資料基準日
			l784m01a.setDataDate(dataStartDate);
			l784m01a.setDataEDate(dataEndDate);
			// 5.海外
			l784m01a.setTypCd(TypCdEnum.海外.getCode());
			l784m01a.setCaseDept(Util.trim(caseDept));
			this.save(l784m01a);
			// 新增一筆至 企金授信管理報表授權檔(同ranMainId)
			L784A01A l784a01a = new L784A01A();
			l784a01a.setMainId(ranMainId);
			l784a01a.setOwnUnit(brNo);
			l784a01a.setAuthTime(CapDate.getCurrentTimestamp());
			// 1.編製/移送,2.代編,3.傳簽,4.傳送(授權)
			l784a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
			// 被授權單位
			l784a01a.setAuthUnit(brNo);
			this.save(l784a01a);
		}
	}

	@Override
	public List<IBranch> findBranch201() {

		return null;
	}

	@Override
	public List<DocFile> findDocFile(String mainId, String docURL) {
		List<DocFile> docFile = docFileDao.findByMainIdAndFieldId(mainId,
				docURL);
		return docFile;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L784S07A.class) {
			return l784s07aDao.findByMainId(mainId);
		} else if (clazz == L784S01A.class) {
			return l784s01aDao.findByMainId(mainId);
		} else if (clazz == L784M01A.class) {
			return l784m01aDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public L784M01A findL784m01aByMainId(String mainId) {
		return l784m01aDao.findByMainIdUn(mainId);
	}

	@Override
	public void tranSportExcel(List<Map<String, Object>> list, String listName) {
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// //String brNo =user.getUnitNo();
		// String ranMainId = IDGenerator.getRandomCode();
		// DocFile docFile = new DocFile();
		// docFile.setBranchId(user.getUnitNo());
		// docFile.setContentType("application/msexcel");
		// docFile.setMainId(ranMainId);
		// docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		// docFile.setFieldId(listName);
		// docFile.setSrcFileName(listName + ".xls");
		// docFile.setUploadTime(CapDate.getCurrentTimestamp());
		// docFile.setSysId("lms");
		// docFile.setData(new byte[] {});
		// fileService.save(docFile, false);
		//
		// String filename = null;
		// String xlsOid = null;
		// List<DocFile> docFileList = this.findDocFile(ranMainId, listName);
		// for (DocFile temp : docFileList) {
		// // System.out.println(fileService.getFilePath(temp));
		// filename = PropUtil.getProperty("docFile.dir") + "/lms/"
		// + user.getUnitNo() + "/" + CapDate.getCurrentDate("yyyy")
		// + "/" + ranMainId + "/" + listName + "/" + temp.getOid()
		// + ".xls";
		// xlsOid = temp.getOid();
		//
		// }
		//
		// File chkFile = new File(filename);
		//
		// // 檢查檔案是否已存在
		// if (!chkFile.mkdirs() || chkFile.exists()) {
		// chkFile.delete();
		// }
		//
		// try {
		// String path = PropUtil.getProperty("loadFile.dir")
		// + "excel/L784M01A.xls";
		//
		// URL urlRpt = null;
		// urlRpt = Thread.currentThread().getContextClassLoader()
		// .getResource(path);
		// File file = new File(urlRpt.toURI());
		// Workbook a = Workbook.getWorkbook(file);
		// WritableWorkbook test = Workbook.createWorkbook(new File(filename),
		// a);
		//
		// WritableSheet sheet = test.getSheet(0);
		//
		// WritableFont headFont12 = new WritableFont(
		// WritableFont.createFont("標楷體"), 24);
		// // 宣告文字格式
		// WritableCellFormat NcellFormatL = new WritableCellFormat(headFont12);
		// // 靠右
		// NcellFormatL.setAlignment(Alignment.CENTRE);
		// // 自動換行
		// NcellFormatL.setWrap(true);
		//
		// int size = list.size();
		// for (int i = 0; i <= size; i++) {
		//
		// // // 分行代碼
		// // Label labelA = new Label(0, i + 1, user.getUnitNo());
		// // sheet.addCell(labelA);
		// //
		// // // 客戶統編
		// // if (custIdlist.get(i) != null || custIdlist.get(i).isEmpty())
		// // {
		// // Label labelB = new Label(1, i + 1, custIdlist.get(i));
		// // sheet.addCell(labelB);
		// // }
		// // // 客戶名稱
		// // if (custNmlist.get(i) != null || custNmlist.get(i).isEmpty())
		// // {
		// // Label labelC = new Label(2, i + 1, custNmlist.get(i));
		// // sheet.addCell(labelC);
		// // }
		// // // 是否為主要授信戶
		// // if (pkCustlist.get(i) != null || pkCustlist.get(i).isEmpty())
		// // {
		// // Label labelD = new Label(3, i + 1, pkCustlist.get(i));
		// // sheet.addCell(labelD);
		// // }
		// //
		// // // 信用評等內部風險 (新)
		// // if (cdrisknlist.get(i) != null ||
		// // cdrisknlist.get(i).isEmpty()) {
		// // Label labelD = new Label(4, i + 1, cdrisknlist.get(i));
		// // sheet.addCell(labelD);
		// // }
		// //
		// // // 信用評等
		// // if (cdriskolist.get(i) != null ||
		// // cdriskolist.get(i).isEmpty()) {
		// // Label labelD = new Label(5, i + 1, cdriskolist.get(i));
		// // sheet.addCell(labelD);
		// // }
		// //
		// // // 額度序號
		// // if (cntrnolist.get(i) != null || cntrnolist.get(i).isEmpty())
		// // {
		// // Label labelE = new Label(6, i + 1, cntrnolist.get(i));
		// // sheet.addCell(labelE);
		// // }
		// //
		// // // 額度(等值台幣)
		// // if (factAmtlist.get(i) != null) {
		// // Label labelF = new Label(7, i + 1,
		// // String.valueOf(factAmtlist.get(i)));
		// // sheet.addCell(labelF);
		// // }
		// //
		// // // 授信期間起日
		// // if (useInitDtlist.get(i) != null) {
		// // Label labelG = new Label(8, i + 1,
		// // (useInitDtlist.get(i)).toString());
		// // sheet.addCell(labelG);
		// // }
		// //
		// // // 授信期間止日
		// // if (useDueDtlist.get(i) != null) {
		// // Label labelH = new Label(9, i + 1,
		// // (useDueDtlist.get(i)).toString());
		// // sheet.addCell(labelH);
		// // }
		// // // 動用起日
		// // if (lnInitDtlist.get(i) != null) {
		// // Label labelI = new Label(10, i + 1,
		// // (lnInitDtlist.get(i)).toString());
		// // sheet.addCell(labelI);
		// // }
		// // // 動用日
		// // if (lnDueDtist.get(i) != null) {
		// // Label labelJ = new Label(11, i + 1,
		// // (lnDueDtist.get(i)).toString());
		// // sheet.addCell(labelJ);
		// // }
		// // // 上次覆審日
		// // if (lastDtlist.get(i) != null) {
		// // Label labelK = new Label(12, i + 1,
		// // (lastDtlist.get(i)).toString());
		// // sheet.addCell(labelK);
		// // }
		//
		// }
		// test.write();
		// test.close();
		// // 存進 sL784M01A
		// this.saveFlieName(xlsOid, 5, user.getUnitNo(), ranMainId, "", "",
		// "");
		// } catch (BiffException e) {
		// logger.error(e.getMessage());
		// } catch (IOException e) {
		// logger.error(e.getMessage());
		// } catch (WriteException e) {
		// logger.error(e.getMessage());
		// } catch (URISyntaxException e) {
		// logger.error(e.getMessage());
		// }

	}

	@SuppressWarnings("unchecked")
	@Override
	public void tranSportExcel9(List<Map<String, Object>> list,
			String caseDept, int action, Date dataStartDate, Date dataEndDate,
			Map<String, Map<String, String>> map01,
			Map<String, Map<String, String>> map02,
			Map<String, Map<String, String>> map01Over,
			Map<String, Map<String, String>> map02Over,
			List<IBranch> branchType0List, List<IBranch> branchType1List,
			List<IBranch> branchType2List, List<IBranch> branchType3List,
			List<IBranch> branchType4List, List<IBranch> branchType5List,
			List<IBranch> branchType6List, List<IBranch> branchType7List,
			List<IBranch> branchType8List, List<IBranch> branchType9SList,
			List<IBranch> branchType9List, String listName) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String ovUnitNo = user.getUnitNo();
		String ranMainId = IDGenerator.getRandomCode();
		String randomCode = IDGenerator.getRandomCode();
		// String brNo =user.getUnitNo();
		DocFile docFile = null;
		File file = null;
		Label label = null;
		WritableFont font20 = null;
		WritableFont font18 = null;
		WritableFont font16 = null;
		WritableFont font14 = null;
		WritableFont font12 = null;
		WritableCellFormat format20Center = null;
		WritableCellFormat format18Center = null;
		WritableCellFormat format16Right = null;
		WritableCellFormat format16Center = null;
		WritableCellFormat format16Left = null;
		WritableCellFormat format16NoBordLeft = null;
		WritableCellFormat format14Right = null;
		WritableCellFormat format14Center = null;
		WritableCellFormat format14Left = null;
		WritableCellFormat format14NoBordLeft = null;
		WritableCellFormat format12Center = null;
		WritableCellFormat format12Left = null;
		WritableCellFormat format12Right = null;
		WritableWorkbook workbook = null;
		WritableSheet sheet = null;
		Map<String, Object> map = null;
		Properties prop = null;
		try {
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS9515V01Page.class);
			docFile = new DocFile();
			docFile.setBranchId(ovUnitNo);
			docFile.setContentType("application/msexcel");
			docFile.setMainId(ranMainId);
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(listName);
			docFile.setSrcFileName(listName + ".xls");
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(fileService.getSysId());
			docFile.setData(new byte[] {});
			fileService.save(docFile, false);

			String filename = null;
			String xlsOid = null;
			List<DocFile> docFileList = this.findDocFile(ranMainId, listName);
			filename = LMSUtil.getUploadFilePath(ovUnitNo, ranMainId, listName);
			for (DocFile temp : docFileList) {
				xlsOid = temp.getOid();
				file = new File(filename);
				file.mkdirs();
			}
			file = new File(filename + "/" + xlsOid + ".xls");

			// 字型設定
			font20 = new WritableFont(WritableFont.createFont("標楷體"), 20,
					WritableFont.BOLD);
			format20Center = LMSUtil.setCellFormat(format20Center, font20,
					Alignment.CENTRE);
			font18 = new WritableFont(WritableFont.createFont("標楷體"), 18,
					WritableFont.BOLD);
			format18Center = LMSUtil.setCellFormat(format18Center, font18,
					Alignment.CENTRE);
			font16 = new WritableFont(WritableFont.createFont("標楷體"), 16,
					WritableFont.BOLD);
			format16Right = LMSUtil.setCellFormat(format16Right, font16,
					Alignment.RIGHT);
			format16Center = LMSUtil.setCellFormat(format16Center, font16,
					Alignment.CENTRE);
			format16Left = LMSUtil.setCellFormat(format16Left, font16,
					Alignment.LEFT);
			format16NoBordLeft = LMSUtil.setCellFormat(format16NoBordLeft,
					font16, Alignment.LEFT, false);
			font14 = new WritableFont(WritableFont.createFont("標楷體"), 14,
					WritableFont.BOLD);
			format14Right = LMSUtil.setCellFormat(format14Right, font14,
					Alignment.RIGHT);
			format14Center = LMSUtil.setCellFormat(format14Center, font14,
					Alignment.CENTRE);
			format14Left = LMSUtil.setCellFormat(format14Left, font14,
					Alignment.LEFT);
			format14NoBordLeft = LMSUtil.setCellFormat(format14NoBordLeft,
					font14, Alignment.LEFT, false);
			font12 = new WritableFont(WritableFont.createFont("標楷體"), 12,
					WritableFont.NO_BOLD);
			format12Center = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE);
			format12Left = LMSUtil.setCellFormat(format12Left, font12,
					Alignment.LEFT);
			format12Right = LMSUtil.setCellFormat(format12Right, font12,
					Alignment.RIGHT);

			workbook = Workbook.createWorkbook(file);
			sheet = workbook.createSheet(
					prop.getProperty("LMS9515X09.number01"), 0);
			SheetSettings setting = sheet.getSettings();
			setting.setPaperSize(PaperSize.A4);
			setting.setScaleFactor(37);
			// 合併單元格 // 合併第一列第一行到第六列第一行的所有單元格
			sheet.mergeCells(0, 0, 7, 0);
			label = new Label(0, 0, prop.getProperty("LMS9515X09.number02")
					+ TWNDate.toTW(dataStartDate).split("/")[0]
					+ prop.getProperty("LMS9515X09.number03")
					+ TWNDate.toTW(dataStartDate).split("/")[1]
					+ prop.getProperty("LMS9515X09.number04"), format14Left);
			sheet.addCell(label);
			int x = 0;
			int y = 2;
			sheet = this
					.setType9Title(false, sheet, format14Center, 0, 1, prop);
			// 此為特殊的 國外部及金控總部分行
			map = this.setColumnData(false, sheet, format18Center,
					format14Center, format14Left, format14Right,
					branchType0List, map01, x, y, prop);
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			// 國內
			map = this.setColumnData(false, sheet, format18Center,
					format14Center, format14Left, format14Right,
					branchType1List, map01, x, y, prop);
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			map = this.setColumnData(false, sheet, format18Center,
					format14Center, format14Left, format14Right,
					branchType2List, map01, x, y, prop);
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			map = this.setColumnData(false, sheet, format18Center,
					format14Center, format14Left, format14Right,
					branchType3List, map01, x, y, prop);
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			map = this.setColumnData(false, sheet, format18Center,
					format14Center, format14Left, format14Right,
					branchType4List, map01, x, y, prop);
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			map = this.setColumnData(false, sheet, format18Center,
					format14Center, format14Left, format14Right,
					branchType5List, map01, x, y, prop);
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			map = this.setColumnData(false, sheet, format18Center,
					format14Center, format14Left, format14Right,
					branchType6List, map01, x, y, prop);
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			map = this.setColumnData(false, sheet, format18Center,
					format14Center, format14Left, format14Right,
					branchType7List, map01, x, y, prop);
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			map = this.setColumnData(false, sheet, format18Center,
					format14Center, format14Left, format14Right,
					branchType8List, map01, x, y, prop);
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			// 顯示國外分行 需要跟國內分行間格3行 若是超過72行 則要換地方輸入
			y += 2;
			if (y >= 72) {
				x += 8;
				y = 2;
			}
			sheet = this.setType9Title(true, sheet, format14Center, x, y, prop);
			y++;
			map = this.setColumnData(true, sheet, format18Center,
					format14Center, format14Left, format14Right,
					branchType9SList, map01Over, x, y, prop);
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			map = this.setColumnData(true, sheet, format18Center,
					format14Center, format14Left, format14Right,
					branchType9List, map01Over, x, y, prop);
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			y += 1;
			sheet = this.setLastTitle(sheet, format14NoBordLeft, x, y, prop);

			sheet = workbook.createSheet(
					prop.getProperty("LMS9515X09.number05"), 1);
			setting = sheet.getSettings();
			setting.setPaperSize(PaperSize.A4);
			setting.setScaleFactor(33);
			// 合併單元格 // 合併第一列第一行到第六列第一行的所有單元格
			// 左上角到右下角 x , y, xx,yy
			sheet.mergeCells(0, 0, 17, 0);
			label = new Label(0, 0, prop.getProperty("LMS9515X09.number06"),
					format20Center);
			sheet.addCell(label);
			sheet.mergeCells(0, 1, 17, 1);
			label = new Label(0, 1, TWNDate.toTW(dataStartDate).split("/")[0]
					+ prop.getProperty("LMS9515X09.number03")
					+ TWNDate.toTW(dataStartDate).split("/")[1]
					+ prop.getProperty("LMS9515X09.number04"), format18Center);
			sheet.addCell(label);
			x = 0;
			y = 3;
			sheet = this.setType9OtherTitle(false, false, sheet,
					format18Center, format14Center, 0, 2, prop);
			map = this.setColumnOtherData(false, sheet, format18Center,
					format14Center, format16Left, format16Right,
					format16Center, map01, x, y, false, prop, "AVGT");
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			// 顯示國外分行 需要跟國內分行間格3行 若是超過72行 則要換地方輸入
			y += 2;
			if (y >= 73) {
				x += 9;
				y = 3;
			}
			sheet = this.setType9OtherTitle(false, true, sheet, format18Center,
					format14Center, x, y, prop);
			y++;
			map = this.setColumnOtherData(true, sheet, format18Center,
					format14Center, format16Left, format16Right,
					format16Center, map01Over, x, y, false, prop, "AVGT");
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			y += 1;
			sheet = this.setLastTitle(sheet, format16NoBordLeft, x, y, prop);

			sheet = workbook.createSheet(
					prop.getProperty("LMS9515X09.number07"), 2);
			setting = sheet.getSettings();
			setting.setPaperSize(PaperSize.A4);
			setting.setScaleFactor(33);
			// 合併單元格 // 合併第一列第一行到第六列第一行的所有單元格
			sheet.mergeCells(0, 0, 17, 0);
			label = new Label(0, 0, prop.getProperty("LMS9515X09.number08"),
					format20Center);
			sheet.addCell(label);
			sheet.mergeCells(0, 1, 17, 1);
			label = new Label(0, 1, TWNDate.toTW(dataStartDate).split("/")[0]
					+ prop.getProperty("LMS9515X09.number03")
					+ TWNDate.toTW(dataStartDate).split("/")[1]
					+ prop.getProperty("LMS9515X09.number04"), format18Center);
			sheet.addCell(label);
			x = 0;
			y = 3;
			sheet = this.setType9OtherTitle(true, false, sheet, format18Center,
					format14Center, 0, 2, prop);
			map = this.setColumnOtherData(false, sheet, format18Center,
					format14Center, format16Left, format16Right,
					format16Center, map02, x, y, true, prop, "TITEMALL");
			map02 = (Map<String, Map<String, String>>) map.get("map02");
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			// 顯示國外分行 需要跟國內分行間格3行 若是超過72行 則要換地方輸入
			y += 2;
			if (y >= 73) {
				x += 9;
				y = 3;
			}
			sheet = this.setType9OtherTitle(true, true, sheet, format18Center,
					format14Center, x, y, prop);
			y++;
			map = this.setColumnOtherData(true, sheet, format18Center,
					format14Center, format16Left, format16Right,
					format16Center, map02Over, x, y, true, prop, "TITEMALL");
			sheet = (WritableSheet) map.get("sheet");
			x = (Integer) map.get("x");
			y = (Integer) map.get("y");
			y += 1;
			sheet = this.setLastTitle(sheet, format16NoBordLeft, x, y, prop);

			workbook.write();
			workbook.close();
			// 存進 sL784M01A
			this.saveFlieName(list, xlsOid, action, ovUnitNo, dataStartDate,
					dataStartDate, ranMainId, caseDept, randomCode);
		} catch (IOException e) {
			logger.error(e.getMessage());
		} catch (WriteException e) {
			logger.error(e.getMessage());
		} finally {

		}
	}

	private WritableSheet setLastTitle(WritableSheet sheet,
			WritableCellFormat formatLeft, int x, int y, Properties prop)
			throws RowsExceededException, WriteException {
		sheet.mergeCells(x, y, x + 7, y);
		sheet.addCell(new Label(x, y, prop.getProperty("LMS9515X09.number09"),
				formatLeft));
		sheet.mergeCells(x, y + 1, x + 7, y + 1);
		sheet.addCell(new Label(x, y + 1, prop
				.getProperty("LMS9515X09.number10"), formatLeft));
		sheet.mergeCells(x, y + 2, x + 7, y + 2);
		sheet.addCell(new Label(x, y + 2, prop
				.getProperty("LMS9515X09.number11"), formatLeft));
		sheet.mergeCells(x, y + 3, x + 7, y + 3);
		sheet.addCell(new Label(x, y + 3, prop
				.getProperty("LMS9515X09.number12"), formatLeft));
		return sheet;
	}

	/**
	 * 特定組別塞資料 -原始資料
	 * 
	 * @param overSeaResult
	 *            是否為海外
	 * @param sheet
	 * @param format14Center
	 *            字型
	 * @param format12Left
	 *            字型
	 * @param format12Right
	 *            字型
	 * @param format12Center
	 *            字型
	 * @param branchList
	 *            特定組別的銀行資料
	 * @param map01
	 *            特定月扣分資料
	 * @param x
	 *            X軸位置
	 * @param y
	 *            Y軸位置
	 * @param groupNo
	 *            組別
	 * @return
	 * @throws RowsExceededException
	 * @throws WriteException
	 */
	private Map<String, Object> setColumnData(boolean overSeaResult,
			WritableSheet sheet, WritableCellFormat format18Center,
			WritableCellFormat format14Center, WritableCellFormat format14Left,
			WritableCellFormat format14Right, List<IBranch> branchList,
			Map<String, Map<String, String>> map01, int x, int y,
			Properties prop) throws RowsExceededException, WriteException {
		Map<String, Object> map = new LinkedHashMap<String, Object>();
		Label label = null;
		// branchList裡面存放的已經是特定的北一區 或是北二區 或是....
		// 所以現在要按照組別排序
		for (int brClass = 0; brClass <= 5; brClass++) {
			for (IBranch branch : branchList) {
				if ((brClass + "").equals(branch.getBrClass())) {
					if (y >= 72) {
						x += 8;
						y = 2;
						sheet = this.setType9Title(overSeaResult, sheet,
								format14Center, x, 1, prop);
					}
					label = new Label(x + 0, y, brClass + "", format14Left);
					sheet.addCell(label);
					label = new Label(x + 1, y,
							overSeaResult ? ""
									: Util.trim(Util.isNotEmpty(branch
											.getBrnGroup()) ? branchService
											.getBranch(branch.getBrnGroup())
											.getNameABBR() : ""), format14Left);
					sheet.addCell(label);
					label = new Label(x + 2, y, branch.getBrName(),
							format14Left);
					sheet.addCell(label);
					label = new Label(x + 3, y, branch.getBrNo(),
							format14Center);
					sheet.addCell(label);
					Map<String, String> innMap = map01.get(branch.getBrNo());
					label = new Label(
							x + 4,
							y,
							innMap != null
									&& !"0.00".equals(NumConverter.addComma(
											innMap.get("FITEMALL"), ",##0.00")) ? NumConverter
									.addComma(innMap.get("FITEMALL"), ",##0.00")
									: "-", format14Right);
					sheet.addCell(label);
					label = new Label(x + 5, y,
							innMap != null ? NumConverter.addComma(innMap
									.get("FCOUNT")) : "0", format14Right);
					sheet.addCell(label);
					label = new Label(
							x + 6,
							y,
							innMap != null
									&& !"0.00".equals(NumConverter.addComma(
											innMap.get("AVGF"), ",##0.00")) ? NumConverter
									.addComma(innMap.get("AVGF"), ",##0.00")
									: "-", format14Right);
					sheet.addCell(label);
					y++;
				}
			}
		}
		map.put("sheet", sheet);
		map.put("x", x);
		map.put("y", y);
		return map;
	}

	/**
	 * 特定組別塞資料 -有排序
	 * 
	 * @param overSeaResult
	 *            是否為海外
	 * @param sheet
	 * @param format14Center
	 *            字型
	 * @param format12Left
	 *            字型
	 * @param format12Right
	 *            字型
	 * @param format12Center
	 *            字型
	 * @param branchList
	 *            特定組別的銀行資料
	 * @param map01
	 *            特定月扣分資料
	 * @param map02
	 *            當年度扣分資料
	 * @param x
	 *            X軸位置
	 * @param y
	 *            Y軸位置
	 * @param groupNo
	 *            組別
	 * @param sortResult
	 *            false 為 特定月份扣分排序 true 為全年扣分排序
	 * @return
	 * @throws RowsExceededException
	 * @throws WriteException
	 */
	private Map<String, Object> setColumnOtherData(boolean overSeaResult,
			WritableSheet sheet, WritableCellFormat format18Center,
			WritableCellFormat format14Center, WritableCellFormat format16Left,
			WritableCellFormat format16Right,
			WritableCellFormat format16Center,
			Map<String, Map<String, String>> map, int x, int y,
			boolean sortResult, Properties prop, String field)
			throws RowsExceededException, WriteException {
		Map<String, Object> returnMap = new LinkedHashMap<String, Object>();
		for (String branchId : map.keySet()) {
			Map<String, String> innMap = map.get(branchId);
			if (innMap == null)
				innMap = new LinkedHashMap<String, String>();
			Map<String, Object> sheetMap = this.setLabel01(sheet,
					format18Center, format14Center, format16Left,
					format16Right, format16Center, innMap, x, y, sortResult,
					overSeaResult, prop, field);
			sheet = (WritableSheet) sheetMap.get("sheet");
			x = (Integer) sheetMap.get("x");
			y = (Integer) sheetMap.get("y");
		}
		returnMap.put("sheet", sheet);
		returnMap.put("x", x);
		returnMap.put("y", y);
		return returnMap;
	}

	private Map<String, Object> setLabel01(WritableSheet sheet,
			WritableCellFormat format18Center,
			WritableCellFormat format14Center, WritableCellFormat format16Left,
			WritableCellFormat format16Right,
			WritableCellFormat format16Center, Map<String, String> innMap,
			int x, int y, boolean sortResult, boolean overSeaResult,
			Properties prop, String field) throws RowsExceededException,
			WriteException {
		Map<String, Object> map = new LinkedHashMap<String, Object>();
		if (y >= 73) {
			x += 9;
			y = 3;
			sheet = this.setType9OtherTitle(sortResult, overSeaResult, sheet,
					format18Center, format14Center, x, 2, prop);
		}
		Label label = null;
		label = new Label(x + 0, y, "Y".equals(innMap.get("BRCLASS")) ? ""
				: innMap.get("BRCLASS"), format16Left);
		sheet.addCell(label);
		label = new Label(x + 1, y, overSeaResult ? "" : Util.trim(Util
				.isNotEmpty(innMap.get("BRNGROUP")) ? branchService.getBranch(
				innMap.get("BRNGROUP")).getNameABBR() : ""), format16Left);
		sheet.addCell(label);
		label = new Label(x + 2, y, innMap.get("BRNAME"), format16Left);
		sheet.addCell(label);
		label = new Label(x + 3, y, innMap.get("BRNO"), format16Center);
		sheet.addCell(label);
		label = new Label(x + 4, y, NumConverter.addComma(innMap
				.get("FITEMALL")), format16Right);
		sheet.addCell(label);
		label = new Label(x + 5, y,
				NumConverter.addComma(innMap.get("FCOUNT")), format16Right);
		sheet.addCell(label);
		label = new Label(x + 6, y, !"0.00".equals(NumConverter.addComma(
				innMap.get("AVGF"), ",##0.00")) ? NumConverter.addComma(
				innMap.get("AVGF"), ",##0.00") : "-", format16Right);
		sheet.addCell(label);
		label = new Label(x + 7, y, !"0.00".equals(NumConverter.addComma(
				innMap.get(field), ",##0.00")) ? NumConverter.addComma(
				innMap.get(field), ",##0.00") : "-", format16Right);
		sheet.addCell(label);
		y++;
		map.put("sheet", sheet);
		map.put("x", x);
		map.put("y", y);
		return map;
	}

	/**
	 * 設定type9 營業單位授信報案考核彙總表 xls的title -原始資料
	 * 
	 * @param sheet
	 * @param format14Left
	 *            字型
	 * @param x
	 *            X軸位置
	 * @param y
	 *            Y軸位置
	 * @return
	 * @throws RowsExceededException
	 * @throws WriteException
	 */
	private WritableSheet setType9Title(boolean overSeaResult,
			WritableSheet sheet, WritableCellFormat format14Center, int x,
			int y, Properties prop) throws RowsExceededException,
			WriteException {
		sheet.setColumnView(x + 0, 10);
		sheet.setColumnView(x + 1, 20);
		sheet.setColumnView(x + 2, 25);
		sheet.setColumnView(x + 3, 10);
		sheet.setColumnView(x + 4, 14);
		sheet.setColumnView(x + 5, 15);
		sheet.setColumnView(x + 6, 17);
		sheet.setColumnView(x + 7, 1);
		sheet.addCell(new Label(x + 0, y, prop
				.getProperty("LMS9515X09.number13"), format14Center));
		sheet.addCell(new Label(x + 1, y, overSeaResult ? "" : prop
				.getProperty("LMS9515X09.number14"), format14Center));
		sheet.addCell(new Label(x + 2, y, overSeaResult ? prop
				.getProperty("LMS9515X09.number15") : prop
				.getProperty("LMS9515X09.number16"), format14Center));
		sheet.addCell(new Label(x + 3, y, prop
				.getProperty("LMS9515X09.number17"), format14Center));
		sheet.addCell(new Label(x + 4, y, prop
				.getProperty("LMS9515X09.number07"), format14Center));
		sheet.addCell(new Label(x + 5, y, prop
				.getProperty("LMS9515X09.number18"), format14Center));
		sheet.addCell(new Label(x + 6, y, prop
				.getProperty("LMS9515X09.number05"), format14Center));
		return sheet;
	}

	/**
	 * 設定type9 營業單位授信報案考核彙總表 xls的title -有排序
	 * 
	 * @param sheet
	 * @param format14Left
	 *            字型
	 * @param x
	 *            X軸位置
	 * @param y
	 *            Y軸位置
	 * @return
	 * @throws RowsExceededException
	 * @throws WriteException
	 */
	private WritableSheet setType9OtherTitle(boolean sortResult,
			boolean overSeaResult, WritableSheet sheet,
			WritableCellFormat format18Center,
			WritableCellFormat format14Center, int x, int y, Properties prop)
			throws RowsExceededException, WriteException {
		sheet.setColumnView(x + 0, 10);
		sheet.setColumnView(x + 1, 20);
		sheet.setColumnView(x + 2, 25);
		sheet.setColumnView(x + 3, 10);
		sheet.setColumnView(x + 4, 14);
		sheet.setColumnView(x + 5, 15);
		sheet.setColumnView(x + 6, 17);
		sheet.setColumnView(x + 7, 17);
		sheet.setColumnView(x + 8, 1);
		sheet.addCell(new Label(x + 0, y, prop
				.getProperty("LMS9515X09.number13"), format14Center));
		sheet.addCell(new Label(x + 1, y, overSeaResult ? "" : prop
				.getProperty("LMS9515X09.number14"), format14Center));
		sheet.addCell(new Label(x + 2, y, overSeaResult ? prop
				.getProperty("LMS9515X09.number15") : prop
				.getProperty("LMS9515X09.number16"), format18Center));
		sheet.addCell(new Label(x + 3, y, prop
				.getProperty("LMS9515X09.number17"), format14Center));
		sheet.addCell(new Label(x + 4, y, prop
				.getProperty("LMS9515X09.number07"), format14Center));
		sheet.addCell(new Label(x + 5, y, prop
				.getProperty("LMS9515X09.number18"), format14Center));
		sheet.addCell(new Label(x + 6, y, prop
				.getProperty("LMS9515X09.number05"), format14Center));
		sheet.addCell(new Label(x + 7, y, sortResult ? prop
				.getProperty("LMS9515X09.number19") : prop
				.getProperty("LMS9515X09.number20"), format14Center));
		return sheet;
	}

	@Override
	public boolean delete(String[] oids, String listName) {
		boolean flag = false;
		String mainId = "";
		List<DocFile> excelFileList = new ArrayList<DocFile>();
		for (int i = 0, size = oids.length; i < size; i++) {
			L784M01A l784m01a = this.findModelByOid(L784M01A.class, oids[i]);
			if (l784m01a != null) {
				// 判斷「LMS.L784M01A.hqCheckDate」，若已有值則不可刪除，並跳出訊息窗「此報表授管處已完成備查。」
				if (l784m01a.getHqCheckDate() == null) {
					mainId = l784m01a.getMainId();
					// 設定刪除並非直接刪除 ，只是標記刪除時間
					// 在DocFile是否有此筆資料
					List<DocFile> docFiles = docFileDao.findByMainIdAndFieldId(
							mainId, listName);
					for (DocFile excelFile : docFiles) {
						// 設定刪除並非直接刪除 ，只是標記刪除時間
						excelFile.setDeletedTime(CapDate.getCurrentTimestamp());
						excelFileList.add(excelFile);
					}
					// List<L784S01A> l784s01aList = (List<L784S01A>)
					// this.findListByMainId(L784S01A.class, mainId);
					// if (!Util.isEmpty(l784s01aList)) {
					// l784s01aDao.delete(l784s01aList);
					// }
					// List<L784A01A> l784a01aList =
					// l784a01aDao.findByMainId(mainId);
					// if (!Util.isEmpty(l784a01aList)) {
					// l784a01aDao.delete(l784a01aList);
					// flag = true;
					// }
					l784m01a.setDeletedTime(CapDate.getCurrentTimestamp());
					l784m01aDao.save(l784m01a);
				}
			}
			docFileDao.save(excelFileList);
		}
		return flag;
	}

	@Override
	public boolean cheaksendLastTime(String oid) {
		boolean check = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L784M01A l784m01a = this.findModelByOid(L784M01A.class, oid);
		if (l784m01a != null) {
			if (l784m01a.getSendLastTime() == null) {
				// 刪除授權檔 LMS.L784A01A.authUnit=授管處(918)
				List<L784A01A> l784a01aList = l784a01aDao.findByMainId(l784m01a
						.getMainId());
				for (L784A01A l784a01a : l784a01aList) {
					// a.刪除授權檔：LMS.L784A01A.mainid=LMS.L784M 01A.mainid and
					// LMS.L784A01A.authUnit=授管處(918)
					if (UtilConstants.BankNo.授管處.equals(l784a01a.getAuthUnit())) {
						l784a01aDao.delete(l784a01a);
					}
				}
				// b.產生授權檔：產生一筆新記錄
				L784A01A l784a01a = new L784A01A();
				l784a01a.setMainId(l784m01a.getMainId());
				// 4.傳送(授權)
				l784a01a.setAuthType(DocAuthTypeEnum.VIEW_TRANSFER.getCode());
				l784a01a.setAuthUnit(UtilConstants.BankNo.授管處);
				l784a01a.setOwnUnit(l784m01a.getOwnBrId());
				l784a01a.setAuthTime(CapDate.getCurrentTimestamp());
				// c.更新L784M01A報表檔：
				l784m01a.setSendFirst(user.getUserId());
				l784m01a.setSendFirstTime(CapDate.getCurrentTimestamp());
				l784m01a.setSendLast(user.getUserId());
				l784m01a.setSendLastTime(CapDate.getCurrentTimestamp());
				save(l784m01a, l784a01a);
			} else {
				// 已經傳送過
				check = true;
			}
		}
		return check;
	}

	@Override
	public List<Map<String, Object>> findType10ByBrNoAndDate(String ovUnitNo,
			String benDate, String endDate) throws CapException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, String> item_STATUS = codetypeService
				.findByCodeType("lms2305m01_status");
		Map<String, String> item_CASELVL = codetypeService
				.findByCodeType("lms1205m01_caseLvl");
		BranchRate branchRate = lmsService.getBranchRate(ovUnitNo);
		List<Map<String, Object>> rows = eloandbBaseService.queryPdf10Data(
				ovUnitNo, benDate, endDate);
		Map<String, Object> dataCollection = null;
		for (Map<String, Object> dataMap : rows) {
			dataCollection = new HashMap<String, Object>();
			String CASEBRID = Util.trim((String) dataMap.get("CASEBRID")); // 分行代號
			dataCollection.put("CASEBRID", CASEBRID);

			String BRNAME = branchService.getBranchName(CASEBRID); // 分行名稱
			dataCollection.put("BRNAME", BRNAME);

			String CUSTID = Util.trim((String) dataMap.get("CUSTID")); // ID
			dataCollection.put("CUSTID", CUSTID);

			String CUSTNAME = Util.trim((String) dataMap.get("CUSTNAME")); // 授信戶名稱
			dataCollection.put("CUSTNAME", CUSTNAME);

			// Util.trim((String) dataMap.get("ENDDATE"));
			String ENDDATE = dataMap.get("ENDDATE").toString(); // 核准日期
			dataCollection.put("ENDDATE", ENDDATE);

			String CASENO = Util.trim((String) dataMap.get("CASENO")); // 簽報書案號
			dataCollection.put("CASENO", CASENO);

			String CNTRNO = Util.trim((String) dataMap.get("CNTRNO")); // 額度序號
			dataCollection.put("CNTRNO", CNTRNO);

			BigDecimal amt_T = new BigDecimal(1000); // 核准額度
			String curr = Util.trim((String) dataMap.get("CURRENTAPPLYCURR")); // 幣別
			BigDecimal amt = LMSUtil.toBigDecimal(dataMap
					.get("CURRENTAPPLYAMT"));// 金額
			if (Util.notEquals("TWD", curr) && curr != null
					&& Util.notEquals(curr, "")
					&& Util.notEquals(BigDecimal.ZERO, amt)) {
				amt_T = branchRate.toOtherAmt(curr, "TWD",
						((amt == null) ? BigDecimal.ZERO : amt));
			} else {
				amt_T = ((amt == null) ? BigDecimal.ZERO : amt);
			}
			dataCollection.put("AMT", amt_T.divide(new BigDecimal("1000"), 0,
					BigDecimal.ROUND_HALF_UP));

			String STATUS = ""; // 案件狀態
			Map<String, Object> l230Map = eloandbBaseService
					.findL230S01LastByCntrno(CNTRNO, "05O");
			if (l230Map != null && !l230Map.isEmpty()) {
				STATUS = Util.trim(l230Map.get("NUSEMEMO"));
				if (Util.equals("2", STATUS) || Util.isEmpty(STATUS)) {
					STATUS = "已核定";
				} else {
					if (item_STATUS != null && Util.isNotEmpty(item_STATUS)) {
						STATUS = MapUtils.getString(item_STATUS, STATUS, "");
					}
				}
			} else {
				STATUS = "已核定";
			}
			dataCollection.put("STATUS", STATUS);

			String CASELVL = Util.trim((String) dataMap.get("CASELVL")); // 授權等級
			if (item_CASELVL != null && Util.isNotEmpty(item_CASELVL)) {
				CASELVL = MapUtils.getString(item_CASELVL, CASELVL, "");
			}
			dataCollection.put("CASELVL", CASELVL);

			list.add(dataCollection);
		}
		return list;
	}

}
