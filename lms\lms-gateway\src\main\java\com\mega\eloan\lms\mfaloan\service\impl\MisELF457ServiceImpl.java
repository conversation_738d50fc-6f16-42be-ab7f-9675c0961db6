package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF457;
import com.mega.eloan.lms.mfaloan.service.MisELF457Service;

/**
 * <pre>
 * 地政士進件額度資料 MIS.ELF457
 * </pre>
 * 
 * @since 2018/4/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2018/4/17,EL08034,new
 *          </ul>
 */
@Service
public class MisELF457ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF457Service {
	
	@Override
	public ELF457 findByCntrNo(String cntrNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListByCustParam("select"
				, new Object[] { "ELF457", "elf457_cntrno=?  " }
				, new Object[] {cntrNo});		
		List<ELF457> list = toELF457(rowData);
		
		if(list.size()==1){			
			return list.get(0);
		}else{
			return null;
		}
	}

	private List<ELF457> toELF457(List<Map<String, Object>> rowData){
		List<ELF457> list = new ArrayList<ELF457>();
		for (Map<String, Object> row : rowData) {
			ELF457 model = new ELF457();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
}
