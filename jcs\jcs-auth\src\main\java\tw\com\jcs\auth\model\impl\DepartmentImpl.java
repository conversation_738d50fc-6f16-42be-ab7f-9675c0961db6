package tw.com.jcs.auth.model.impl;

import tw.com.jcs.auth.model.Department;

/**
 * <pre>
 * DepartmentImpl
 * </pre>
 * 
 * @since 2022年12月13日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月13日
 *          </ul>
 */
public class DepartmentImpl implements Department {

    private static final long serialVersionUID = 557153479230523814L;

    /**
     * 部門id
     */
    String id;

    /**
     * 部門名稱
     */
    String name;

    /**
     * 類型
     */
    String type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

}
