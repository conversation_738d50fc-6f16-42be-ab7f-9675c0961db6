/* 
 * LMS7005FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.fms.pages.LMS7005V00Page;
import com.mega.eloan.lms.fms.service.LMS7005Service;
import com.mega.eloan.lms.model.L700M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 案件分案對照表
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/9/29,Miller Lin,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7005formhandler")
public class LMS7005FormHandler extends AbstractFormHandler {

	@Resource
	LMS7005Service service;

	@Resource
	BranchService branch;

	@Resource
	UserInfoService userinfoservice;

	/**
	 * 查詢
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL700m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("oid");
		L700M01A l700m01a = service.findL700m01aByOid(oid);
		List<IBranch> listAll = branch.getAllBranch();
		Map<String, String> mapBid = new TreeMap<String, String>();
		Map<String, String> mapGrp = new TreeMap<String, String>();
		for (IBranch brn : listAll) {
			mapBid.put(brn.getBrNo(), brn.getBrName());
			mapGrp.put(brn.getBrNo(), brn.getBrnGroup());
		}
		CapAjaxFormResult resultBid = new CapAjaxFormResult(mapBid);
		CapAjaxFormResult resultGrp = new CapAjaxFormResult(mapGrp);
		SignEnum[] sign = { SignEnum.首長, SignEnum.甲級主管, SignEnum.單位主管,
				SignEnum.乙級主管 };
		Map<String, String> allPeople = userinfoservice.getBRUserName(user
				.getUnitNo());
		CapAjaxFormResult allPeopleMap = new CapAjaxFormResult(allPeople);
		Map<String, String> mainPeople = userinfoservice.findByBrnoAndSignId(
				user.getUnitNo(), sign);
		CapAjaxFormResult mainPeopleMap = new CapAjaxFormResult(mainPeople);
		CapAjaxFormResult formL700m01a = DataParse.toResult(l700m01a);
		StringBuilder sbGrp = new StringBuilder();
		sbGrp.append(l700m01a.getGroupId()).append(" ")
				.append(branch.getBranchName(l700m01a.getGroupId()));
		formL700m01a.set("cgroupId", Util.trim(sbGrp.toString()));
		formL700m01a.set("groupId", Util.trim(l700m01a.getGroupId()));
		StringBuilder fullUp = new StringBuilder();
		fullUp.append(getPerName(l700m01a.getUpdater())).append(" (")
				.append(TWNDate.toFullTW(l700m01a.getUpdateTime())).append(")");
		formL700m01a.set("updater", Util.trim(fullUp.toString()));
		result.set("L700M01AForm", formL700m01a);
		// 負責經辦人員
		result.set("userNo", allPeopleMap);
		// 負責主管人員號碼
		result.set("reCheck", mainPeopleMap);
		result.set("itemBranch", resultBid);
		result.set("itemGroup", resultGrp);
		return result;
	}// ;

	/**
	 * 查詢欲新增的資料(分行別與隸屬區域營運中心)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryAddData(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<IBranch> listAll = branch.getAllBranch();
		Map<String, String> mapBid = new TreeMap<String, String>();
		Map<String, String> mapGrp = new TreeMap<String, String>();
		for (IBranch brn : listAll) {
			mapBid.put(brn.getBrNo(), brn.getBrName());
			mapGrp.put(brn.getBrNo(), brn.getBrnGroup());
		}
		CapAjaxFormResult resultBid = new CapAjaxFormResult(mapBid);
		CapAjaxFormResult resultGrp = new CapAjaxFormResult(mapGrp);
		SignEnum[] sign = { SignEnum.首長, SignEnum.甲級主管, SignEnum.單位主管,
				SignEnum.乙級主管 };
		Map<String, String> allPeople = userinfoservice.getBRUserName(user
				.getUnitNo());
		CapAjaxFormResult allPeopleMap = new CapAjaxFormResult(allPeople);
		Map<String, String> mainPeople = userinfoservice.findByBrnoAndSignId(
				user.getUnitNo(), sign);
		CapAjaxFormResult mainPeopleMap = new CapAjaxFormResult(mainPeople);
		// 負責經辦人員
		result.set("userNo", allPeopleMap);
		// 負責主管人員號碼
		result.set("reCheck", mainPeopleMap);
		result.set("itemBranch", resultBid);
		result.set("itemGroup", resultGrp);
		StringBuilder sbGrp = new StringBuilder();
		sbGrp.append((String) resultGrp.get("908")).append(" ")
				.append(branch.getBranchName((String) resultGrp.get("908")));
		result.set("cgroupId", Util.trim(sbGrp.toString()));
		result.set("groupId", (String) resultGrp.get("908"));
		// StringBuilder fullUp = new StringBuilder();
		// fullUp.append(getPerName(user.getUserId()))
		// .append(" (").append(TWNDate.toFullTW(CapDate.getCurrentTimestamp())).append(")");
		// result.set("cupdater", Util.trim(fullUp.toString()));
		// result.set("updater", Util.trim(fullUp.toString()));
		return result;
	}// ;

	/**
	 * 查詢使用者選擇分行代碼之隸屬區域營運中心
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryGrorpId(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<IBranch> listAll = branch.getAllBranch();
		Map<String, String> mapGrp = new TreeMap<String, String>();
		for (IBranch brn : listAll) {
			mapGrp.put(brn.getBrNo(), brn.getBrnGroup());
		}
		CapAjaxFormResult resultGrp = new CapAjaxFormResult(mapGrp);
		String val = params.getString("branch");
		// 20130416 Vector 傳回空白而非null
		StringBuilder sbGrp = new StringBuilder();
		String grpNo = Util.nullToSpace(resultGrp.get(val));
		sbGrp.append(Util.nullToSpace(grpNo)).append(" ")
				.append(Util.nullToSpace(branch.getBranchName(grpNo)));
		result.set("cgroupId", Util.nullToSpace(sbGrp.toString()));
		result.set("groupId", Util.nullToSpace(resultGrp.get(val)));
		return result;
	}// ;

	/**
	 * 檢查分行別(有無重複)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	private boolean checkL700m01a(String branchId) throws CapException {
		List<L700M01A> listL700m01a = service.findL700m01aList();
		for (L700M01A model : listL700m01a) {
			if (branchId.equals(model.getBranchId())) {
				return true;
			}
		}
		return false;
	}// ;

	/**
	 * 儲存(含修改)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveL700m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String formL700m01a = Util.trim(params.getString("L700M01AForm"));
		String oid = Util.trim(params.getString("oid"));
		String branchId = Util.trim(params.getString("branchId"));
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS7005V00Page.class);
		L700M01A l700m01a;
		if (Util.isEmpty(oid)) {
			if (checkL700m01a(branchId)) {
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.注意,
						pop.getProperty("l720v00.alert3")), getClass());
			}
			l700m01a = new L700M01A();
			l700m01a.setCreator(user.getUserId());
			l700m01a.setCreateTime(CapDate.getCurrentTimestamp());
		} else {
			l700m01a = service.findL700m01aByOid(oid);
			if (l700m01a == null) {
				l700m01a = new L700M01A();
				l700m01a.setCreator(user.getUserId());
				l700m01a.setCreateTime(CapDate.getCurrentTimestamp());
			}
		}
		DataParse.toBean(formL700m01a, l700m01a);

		// 建檔單位
		l700m01a.setOwnBrId(user.getUnitNo());

		// 不是國外部則沒有科(組)別
		if (!"007".equals(branchId)) {
			l700m01a.setSubject("");
		}
		service.save(l700m01a);
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage("EFD0017"));
		}
		return result;
	}// ;

	/**
	 * 刪除
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult deleteL700m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.nullToSpace(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);
		service.deleteListL700m01a(oidArray);
		// 印出刪除成功訊息
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
				RespMsgHelper.getMainMessage("EFD0019"));
		return result;
	}// ;

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	private String getPerName(String id) {
		return (!Util.isEmpty(userinfoservice.getUserName(id)) ? userinfoservice
				.getUserName(id) : id);
	}
}
