/**
 * FileUploadHandler.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,
		Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.handler;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.action.IAction;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.plugin.IFrameAjaxHandlerPlugin;
import tw.com.iisi.cap.response.IResult;

/**
 * <pre>
 * 檔案上傳。
 * </pre>
 * 
 * @since 2010/11/24
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/8/25,sunkistwang,new
 *          </ul>
 */
public abstract class FileUploadHandler extends FormHandler implements IFrameAjaxHandlerPlugin {

    /**
     * {@value #FILE_COLUMN_SYMBOL}
     */
    public final static String FILE_COLUMN_SYMBOL = "|!|";

    /**
     * {@value #LOCALFILE_FIELDNAME}
     */
    public final static String LOCALFILE_FIELDNAME = "localfile";

    /*
     * 上傳後執行的動作
     * 
     * @see tw.com.iisi.cap.handler.FormHandler#getAction(java.lang.String)
     */
    @Override
    public IAction getAction(String formAction) {
        return new AfterUploaded();
    }

    /** do afterUploaded action */
    class AfterUploaded implements IAction {

        @Override
        public IResult doWork(PageParameters params) throws CapException {
            return afterUploaded(params);
        }
    }// ;

    /**
     * 上傳檔案後之動作
     * 
     * @param params
     *            page參數
     * @return FormResult
     */
    public abstract IResult afterUploaded(PageParameters params) throws CapException;

    /*
     * 取得Operation Name
     * 
     * @see tw.com.iisi.cap.handler.FormHandler#getOperationName()
     */
    @Override
    public String getOperationName() {
        return "fileUploadOperation";
    }

}// ~
