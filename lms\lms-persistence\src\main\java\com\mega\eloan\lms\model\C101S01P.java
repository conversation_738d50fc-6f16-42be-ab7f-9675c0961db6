/* 
 * C101S01P.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;
import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金相關查詢所擔任負責人或董監事之企業是否於本行有授信額度達一億元以上名單 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01P", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "comCustId" }))
public class C101S01P extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 借保人統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 借保人重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 企業統編 **/
	@Size(max = 11)
	@Column(name = "COMCUSTID", length = 11, columnDefinition = "VARCHAR(11)")
	private String comCustId;

	/** 企業戶名 **/
	@Size(max = 250)
	@Column(name = "COMNAME", length = 250, columnDefinition = "VARCHAR(250)")
	private String comName;
	
	/** 企業核准額度 **/
	@Digits(integer = 15, fraction = 2)
	@Column(name = "FACT_AMT", columnDefinition = "DEC(15,2)")
	private BigDecimal fact_amt;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得借保人統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定借保人統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得借保人重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定借保人重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}
	
	/** 取得企業統編 **/
	public String getComCustId() {
		return this.comCustId;
	}

	/** 設定企業統編 **/
	public void setComCustId(String value) {
		this.comCustId = value;
	}

	/** 取得企業戶名 **/
	public String getComName() {
		return this.comName;
	}

	/** 設定企業戶名 **/
	public void setComName(String value) {
		this.comName = value;
	}
	
	/** 取得企業核准額度 **/
	public BigDecimal getFact_amt() {
		return this.fact_amt;
	}

	/** 設定企業核准額度 **/
	public void setFact_amt(BigDecimal value) {
		this.fact_amt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
