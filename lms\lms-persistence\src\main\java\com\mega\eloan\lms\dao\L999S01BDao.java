/* 
 * L999S01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L999S01B;

/** 綜合授信契約書借款種類檔 **/
public interface L999S01BDao extends IGenericDao<L999S01B> {

	L999S01B findByOid(String oid);

	List<L999S01B> findByMainId(String mainId);

	L999S01B findByUniqueKey(String mainId, String itemType);

	List<L999S01B> findByIndex01(String mainId, String itemType);
}