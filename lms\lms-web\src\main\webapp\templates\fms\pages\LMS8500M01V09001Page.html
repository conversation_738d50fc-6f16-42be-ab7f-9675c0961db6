<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
	<body>
		<wicket:extend>
			<script type="text/javascript" src="pagejs/fms/LMS8500M01V09001Page.js?r=20240521"></script>

			<div class="button-menu funcContainer" id="buttonPanel">
				
				<!--編製中 -->
				<wicket:enclosure><span wicket:id="_btnDOC_EDITING" />
	        		<button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04" />
	        			<wicket:message key="button.save"><!--儲存--></wicket:message>
	        		</button>
					<button id="btnSend" >
	        			<span class="ui-icon ui-icon-jcs-02" />
	        			<wicket:message key="button.send" ><!--呈主管覆核--></wicket:message>
	        		</button>
		        </wicket:enclosure>		
				
				<!--待覆核 -->
				<wicket:enclosure><span wicket:id="_btnWAIT_APPROVE" />
	        		<button id="btnCheck" >
	        			<span class="ui-icon ui-icon-jcs-106" />
	        			<wicket:message key="button.check" ><!--覆核--></wicket:message>
	        		</button>
		        </wicket:enclosure>				
		        
                <button id="btnExit"  class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					<wicket:message key="button.exit"><!--離開--></wicket:message>
				</button>
				
            </div>
			<div class="tit2 color-black">
				<span id="title">共用參數表</span>
			</div>
			
			<form id="mainPanel">
				<fieldset>
					<!--J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表-->
					<button type="button" id="importCodeTypeExl"><span class="text-only">匯入EXCEL</span></button><br>
					<!--
					<table class="tb2" width="100%" border="1" cellpadding="0" cellspacing="0">		
						<tr>
							<td class="hd1">
								資料日期
							</td>	
							<td>
								<input type="text" class="required date" name="lmsBisSelfCapitalDate" id="lmsBisSelfCapitalDate"/> <br>
							</td>	
						</tr>
					</table>	
					-->
					<!--這裡用來放資料，經辦上傳，主管核准後要生效的資料-->
					<b class="color-red">此功能僅可以新增CODETYPE，不可UPDATE和DELETE</b><br/>
					<b class="color-red">若資料庫中已存在相同(LOCALE, CODETYPE, CODEVALUE)則無法匯入</b>
					<div id="gridview"></div>
					
					<div id="importByExl_codeTypeFile" style="display:none; margin-top:5px;">
			            <table border="1" cellpadding="0" cellspacing="0">
			                <tr>
			                    <td>
			                        <input type="file" id="uploadCodeTypeFile" name="uploadCodeTypeFile" class="required"/><br>
									EXCEL檔案類型須為 Excel 97-2003 活頁簿(*.xls) ，若為.xlsx請另存新檔，並將存檔類型改選前述類型即可。
			                    </td>
			                </tr>
			            </table>
			        </div>
                </fieldset>
			</form>
			
			<div id="docPanel">
				<fieldset>
                    <legend>
                        <b><wicket:message key="doc.docUpdateLog"><!-- 文件異動紀錄 --></wicket:message></b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer"><!-- 文件異動紀錄--> <div wicket:id="_docLog" /></div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                            	<td width="35%" class="hd1">
                                    <wicket:message key="doc.creator"><!--  文件建立者--></wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="15%">
                                    <span id='creator'/>(<span id='createTime'/>)
                                </td>
                                <td width="30%" class="hd1">
                                    <wicket:message key="doc.lastUpdater"><!--  最後異動者--></wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="20%">
                                    <span id='updater'/>(<span id='updateTime'/>)
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                </td>
                                <td>
                                </td>
                                <td class="hd1">
                                    <wicket:message key="doc.docCode"><!--文件亂碼--></wicket:message>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="randomCode" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
				
				<fieldset>
	                <div id="tabs-appr" class="tabs" style='width:99%;'>
	                    <ul>
	                        <li>
	                            <a href="#tabs-appr01"><b><wicket:message key="L850M01A.title01"></wicket:message></b></a>
	                        </li>
	                    </ul>
						<div class="tabCtx-warp">
	                        <div id="tabs-appr01" class="content">
 							<table width="100%">
                                <tr>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <wicket:message key="L850M01A.managerId"><!--經副襄理--></wicket:message>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="managerId" />
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <wicket:message key="L850M01A.bossId"><!-- 授信主管--></wicket:message>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="bossId" />
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <wicket:message key="L850M01A.reCheckId"><!--覆核主管--></wicket:message>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="reCheckId"/>
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <wicket:message key="L850M01A.apprId"><!--  經辦--></wicket:message>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="showApprId"/>
                                    </td>
                                </tr>
                            </table>
							</div>
	                    </div>
	               </div>				   
	            </fieldset>
			</div>
			
			<div id="openCheckBox" style="display:none"> 
				<div>
				<span id="check1" style="display:none">
				 	<label><input name="checkRadio" type="radio" value="3"><wicket:message key="accept"><!--  核准--></wicket:message></label><br/>
					<label><input name="checkRadio" type="radio" value="1"><wicket:message key="back"><!--  退回經辦修改--></wicket:message></label>
				</span>
				</div>
			</div>
			<div id="selectBossBox"  style="display:none;">
			  <form id="selectBossForm">
	         	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                 <tr>
	            		<td class="hd1" width="60%"><wicket:message key="L850M01A.selectBoss"><!--  授信主管人數--></wicket:message>&nbsp;&nbsp;</td>
	                    <td width="40%"><select id="numPerson" name="numPerson">
	                    		<option value="1">1</option>
	                    		<option value="2">2</option>
	                            <option value="3">3</option>
								<option value="4">4</option>
	                    		<option value="5">5</option>
	                            <option value="6">6</option>
								<option value="7">7</option>
	                    		<option value="8">8</option>
	                            <option value="9">9</option>
								<option value="10">10</option>
	                    	</select>
						</td>
	                 </tr>
	                 <tr >
	                 	<td class="hd1" ><wicket:message key="L850M01A.bossId"><!--  授信主管--></wicket:message>&nbsp;&nbsp;</td>
	            		<td >
	            			<div id="bossItem"></div>
	                 	</td>
	                 </tr>
	                 <tr >
	            		<td class="hd1"><wicket:message key="L850M01A.managerId"><!--經副襄理--></wicket:message>&nbsp;&nbsp;</td>
	                    <td><div id="managerItem"></div></td>
	                 </tr>
	           	 </table>
				</form>
  			</div>
		</wicket:extend>
    </body>
</html>
