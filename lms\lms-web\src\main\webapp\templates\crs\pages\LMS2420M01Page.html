<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="innerPageBody">
			<script type="text/javascript">
				loadScript('pagejs/crs/LMS2420M01Page');
    		</script>
            <div class="button-menu funcContainer" id="buttonPanel">
            	<!-- ===================================== -->
            	<th:block th:if="${_btnSave_visible}">
					<button type="button" id="btnSave" >
						<span class="ui-icon ui-icon-jcs-04"></span>
						<th:block th:text="#{'button.save'}">儲存</th:block>
					</button>
				</th:block>
				
				<button type="button" id="btnExit" class="forview">
					<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}">離開</th:block>
				</button>
			</div>	
				
			<div class="tit2 color-black">
                <table width="100%">
                    <tr>
                        <td width="100%">
                        	<th:block th:text="#{'doc.title'}">預約整批產生模式</th:block>
						</td>
                    </tr>
                </table>
            </div>
			
            <form id="tabForm">
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                <tbody>
	                	<tr>
	                        <td width="30%" class="hd1" nowrap>
	                            <th:block th:text="#{'lms2420m01.schTime'}">指定整批產生名單時間</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <input type="text" size="8" maxlength="10" class="date required" _requiredLength="10" id="sch_date" name="sch_date" />&nbsp;
								<input type="text" size="1" maxlength="2" class="required" id="sch_hh" name="sch_hh" />：<input type="text" size="1" maxlength="2" class="required" id="sch_mm" name="sch_mm" />
								(24小時制‧EX：13:30)
	                        </td>
							<td width="30%" class="hd1" nowrap>
	                            <th:block th:text="#{'lms2420m01.status'}">處理情形</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <span id="status"></span>&nbsp;
	                        </td>
	                    </tr>
						<tr>
	                        <td class="hd1">
	                            <th:block th:text="#{'lms2420m01.creator'}">文件建立人員</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <span id="creator"></span>&nbsp;
	                        </td>
							<td class="hd1">
	                            <th:block th:text="#{'lms2420m01.updater'}">最後異動人員</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <span id="updater"></span>&nbsp;
	                        </td>
	                    </tr>
					</tbody>
	            </table>
				<div>
				<th:block th:text="#{'label.def_yyyyMM'}">資料年月</th:block>： <input id='def_yyyyMM' name='def_yyyyMM' type='text' maxlength='7' size='7'>(YYYY-MM)
				<button type='button' id='btn_bringDate'><th:block th:text="#{'labe.btn_bringDate'}">更新資料年月</th:block></button>
				</div>      
				<div id="orderBox"></div>
            </form>			
        </th:block>
    </body>
</html>

