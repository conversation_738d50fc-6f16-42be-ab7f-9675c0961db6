package com.mega.eloan.lms.cls.service.impl;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;

import javax.annotation.Resource;

import org.aspectj.util.FileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.dao.C101S04WDao;
import com.mega.eloan.lms.dao.C120S04WDao;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120S04W;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Util;

@Service("cls1131RpaFileService")
public class CLS1131RPAFileServiceImpl implements FileDownloadService {
	
	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1131RPAFileServiceImpl.class);

	@Resource
	RPAProcessService rpaProcessService;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	DocFileDao docFileDao;
	
	@Resource
	C101S04WDao c101s04wDao;
	
	@Resource
	C120S04WDao c120s04wDao;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException, IOException, URISyntaxException {
		
		String oid = Util.trim(params.getString("oid"));
		String dataSource = Util.trim(params.getString("dataSource"));
		String docFileOid = null;
		
		if("C101S04W".equals(dataSource)){
			C101S04W c101s04w = c101s04wDao.findByOid(oid);
			docFileOid = c101s04w.getDocfileoid();
		}
		else{
			C120S04W c120s04w = c120s04wDao.findByOid(oid);
			docFileOid = c120s04w.getDocfileoid();
		}
		
		ISearch search = docFileDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.OID, docFileOid);
		DocFile docFile = docFileDao.findUniqueOrNone(search);
		
		byte[] bytes = null;
		if (docFile != null && docFile.getSrcFileName().indexOf("jpg") > -1) {
			File file = docFileService.getRealFile(docFile);
			bytes = FileUtil.readAsByteArray(file);
		}
		
		return bytes;
	}
}
