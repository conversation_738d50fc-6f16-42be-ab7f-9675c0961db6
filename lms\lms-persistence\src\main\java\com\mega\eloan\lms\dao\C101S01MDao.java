/* 
 * C101S01MDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S01M;


/** 個金相關查詢聯徵非Z類檔 **/
public interface C101S01MDao extends IGenericDao<C101S01M> {

	C101S01M findByOid(String oid);

	List<C101S01M> findByMainId(String mainId);

	C101S01M findByUniqueKey(String mainId, String custId, String dupNo,
			Date qDate);

	List<C101S01M> findByIndex01(String mainId, String custId, String dupNo,
			Date qDate);
	
	List<C101S01M> findByCustIdDupId(String custId,String DupNo);

	int deleteByOid(String oid);
}