/* 
 * L120S05DDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S05DDao;
import com.mega.eloan.lms.model.L120S05D;


/** 借款人集團授信明細檔 **/
@Repository
public class L120S05DDaoImpl extends LMSJpaDao<L120S05D, String>
	implements L120S05DDao {

	@Override
	public L120S05D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S05D> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S05D> list = createQuery(L120S05D.class,search).getResultList();
		return list;
	}
	
	@Override
	public L120S05D findByUniqueKey(String mainId, String custId, String dupCode){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupCode", dupCode);
		return findUniqueOrNone(search);
	}
	@Override
	public List<L120S05D> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L120S05D> list = createQuery(L120S05D.class,search).getResultList();
		return list;
	}
	@Override
	public List<L120S05D> findByIndex01(String mainId, String custId, String dupCode){
		ISearch search = createSearchTemplete();
		List<L120S05D> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupCode != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupCode", dupCode);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(L120S05D.class,search).getResultList();
		}
		return list;
	}
	
	@Override
	public int delModel(String mainId){
		Query query = getEntityManager().createNamedQuery("L120S05D.delModel");
		query.setParameter("MAINID", mainId); //設置參數
		return query.executeUpdate();
	}
}