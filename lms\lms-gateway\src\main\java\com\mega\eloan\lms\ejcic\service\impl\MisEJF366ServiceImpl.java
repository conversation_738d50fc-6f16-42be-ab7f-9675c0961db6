/* 
 * MisEJF366ServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.ejcic.service.MisEJF366Service;

/**
 * <pre>
 * MIS.BAM206>>MIS.EJV36601>>MIS.EJF366
 * </pre>
 * @since 2012/03/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/03/23,TimChiang,new
 *          </ul>
 */
@Service
public class MisEJF366ServiceImpl extends AbstractEjcicJdbc implements MisEJF366Service {

	@Override
	public Map<String, Object> findTkLoanAndCrLoanStatus(String id, String prodId) {
		return getJdbc().queryForMap("BAM206.findTkLoanAndCrLoanStatus", new String[]{id, prodId});
	}

}
