package com.mega.eloan.lms.lrs.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.ui.ModelMap;
import com.iisigroup.cap.component.PageParameters;

import tw.com.jcs.common.Util;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.panels.RetrialPtMgrIdPanel;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lrs.panels.LMS1700S01Panel;
import com.mega.eloan.lms.lrs.panels.LMS1700S02Panel;
import com.mega.eloan.lms.lrs.panels.LMS1700S03Panel;
import com.mega.eloan.lms.lrs.panels.LMS1700S04Panel;
import com.mega.eloan.lms.lrs.panels.LMS1700S05Panel;
import com.mega.eloan.lms.lrs.panels.LMS1700S06Panel;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 企金覆審報告表
 * </pre>
 * 
 * @since 2012/2/15
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2012/2/15,jessica,new
 *          </ul>
 */
@Controller
@RequestMapping("/lrs/lms1700m01/{page}")
public class LMS1700M01Page extends AbstractEloanForm {

	@Autowired
	RetrialService retrialService;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS1700M01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L170M01A meta = null;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean showRetrialStaff_befEndPtA = false;// 編製完成,且上傳
		boolean showRetrialStaff_befEndPtB = false;// 只上傳
		boolean showRetrialStaff_AtEndPt = false;
		boolean is_flowClass_throughBr = false;
		boolean _btnSave = false;
		boolean _btnAddToL180M01A = false;
		boolean _btnLoadLms8000v04 = false;
		boolean _btnMOVE_TO_BR = false;
		boolean _btnREG_BRANCH_COMM = false;
		boolean _btnBack_R_L1 = false;
		boolean _btnBack_E_L1 = false;
		boolean isRetrialTeam = false;
		// J-110-0505_05097_B1001 Web
		// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
		boolean _btnPrintLatestL140M01A = false;

		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL170M01A_oid(mainOid);

			if (meta != null) {
				is_flowClass_throughBr = retrialService.is_flowClass_throughBr(meta);
			}

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			if (Util.equals(Util.trim(meta.getCtlType()), LrsUtil.CTLTYPE_自辦覆審)) {
				isRetrialTeam = true;
			} else {
				isRetrialTeam = CrsUtil.isRetrialTeam(user);
			}

			if (meta != null) {
				if (isRetrialTeam) {
					// 覆審組人員才有的權限
					if (Util.equals(RetrialDocStatusEnum.已覆核已核定.getCode(), meta.getDocStatus())) {
						showRetrialStaff_AtEndPt = true;
						// 已覆核已核定, 仍可儲存後上傳
						if (CrsUtil.canSaveL170M01A(user, meta)) {
							showRetrialStaff_befEndPtB = true;
						}
					} else {
						/*
						 * 931 在到分行前 , click 上傳, 只上傳, 不變更 docStatus 分行傳回後, click 上傳, 會上傳, 且變更 docStatus
						 * 
						 * 非931 在到分行前, click 上傳, 上傳, 且變更 docStatus
						 */
						if (is_flowClass_throughBr) {
							if (Util.equals(RetrialDocStatusEnum.已覆核未核定.getCode(), meta.getDocStatus())) {
								showRetrialStaff_befEndPtA = true;
							} else {
								if (Util.equals(RetrialDocStatusEnum.區中心_待覆核.getCode(), meta.getDocStatus())) {
									// 此頁面為覆核頁面，先不出現上傳ELF412的按鈕
								} else {
									showRetrialStaff_befEndPtB = true;
								}
							}
						} else {
							showRetrialStaff_befEndPtA = true;
						}
					}
				}
			}
		}
		if (meta != null) {
			if (isRetrialTeam) {
				_btnSave = CrsUtil.canSaveL170M01A(user, meta);
				if (!_btnSave) {
					if (!retrialService.hidePaFormPanel()) { // 有考評表頁籤
						if (!LrsUtil.isFromNotes(meta)) { // 不是NOTES舊案
							String docStatus = Util.trim(meta.getDocStatus());
							if (Util.equals(docStatus, RetrialDocStatusEnum.已覆核未核定.getCode())) {
								// 2022/04/18 授審處連喬凱來電 分處對覆審報告表有回頭打考評表之需求
								// 1. 「已覆核已核定」之覆審報告表可以退為「已覆核未核定」修改_限制為有考評表且有傳送至分行的覆審報告表
								// 2. 不限上傳者本人，任何人都可以修改
								_btnSave = true;
							}
						}
					}
				}
			}

			if (isRetrialTeam && Util.isEmpty(Util.trim(meta.getPid()))) {
				if (LrsUtil.isFromNotes(meta)) {
					// still false
				} else {
					_btnAddToL180M01A = true;
				}
			}

			if (is_flowClass_throughBr && isRetrialTeam
					&& Util.equals(RetrialDocStatusEnum.區中心_編製中.getCode(), meta.getDocStatus())) {
				_btnMOVE_TO_BR = true;
			}

			if (Util.equals(RetrialDocStatusEnum.編製中.getCode(), meta.getDocStatus())) {
				_btnREG_BRANCH_COMM = true;
			} else {
				if (is_flowClass_throughBr) {

				} else {
					if (Util.equals(RetrialDocStatusEnum.區中心_編製中.getCode(), meta.getDocStatus())) {
						_btnREG_BRANCH_COMM = true;
					}
				}
			}
			// 退回「覆審單位」覆審人員
			if (Util.equals(RetrialDocStatusEnum.編製中.getCode(), meta.getDocStatus())
					|| Util.equals(RetrialDocStatusEnum.已覆核未核定.getCode(), meta.getDocStatus())) {
				_btnBack_R_L1 = true;
			}
			// 退回「受檢單位」經辦
			if (Util.equals(RetrialDocStatusEnum.已覆核未核定.getCode(), meta.getDocStatus())) {
				_btnBack_E_L1 = true;
			}

			// 開啟貸後管理查詢
			if (isRetrialTeam) {
				if (Util.equals(RetrialDocStatusEnum.區中心_編製中.getCode(), meta.getDocStatus())
						|| Util.equals(RetrialDocStatusEnum.編製中.getCode(), meta.getDocStatus())) {
					_btnLoadLms8000v04 = true;
				}
			}

			// J-110-0505_05097_B1001 Web
			// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
			// 開啟列印額度明細表/批覆書
			if (isRetrialTeam) {
				if (Util.equals(RetrialDocStatusEnum.區中心_編製中.getCode(), meta.getDocStatus())
						|| Util.equals(RetrialDocStatusEnum.編製中.getCode(), meta.getDocStatus())) {
					_btnPrintLatestL140M01A = true;
				}
			}

		}

		model.addAttribute("_btnSave", _btnSave);
		model.addAttribute("_btnAddToL180M01A", _btnAddToL180M01A);
		model.addAttribute("_btnLoadLms8000v04", _btnLoadLms8000v04);
		// J-110-0505_05097_B1001 Web
		// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
		model.addAttribute("_btnPrintLatestL140M01A", _btnPrintLatestL140M01A);
		// J-111-0554 配合授審處增進管理效益，修改相關功能程式
		model.addAttribute("_btnPrintCollSet", _btnPrintLatestL140M01A);
		model.addAttribute("_btnAREA_WAITAPPROVE",
				Util.equals(RetrialDocStatusEnum.區中心_待覆核.getCode(), meta.getDocStatus()));
		model.addAttribute("_btnBRANCH_EDITING", Util.equals(RetrialDocStatusEnum.編製中.getCode(), meta.getDocStatus()));
		model.addAttribute("_btnBRANCH_WAITAPPROVE",
				Util.equals(RetrialDocStatusEnum.待覆核.getCode(), meta.getDocStatus()));
		model.addAttribute("_btnMOVE_TO_BR", _btnMOVE_TO_BR);
		model.addAttribute("_btnREG_BRANCH_COMM", _btnREG_BRANCH_COMM);
		model.addAttribute("_btnBack_R_L1", _btnBack_R_L1);
		model.addAttribute("_btnBack_E_L1", _btnBack_E_L1);
		model.addAttribute("showRetrialStaff_befEndPtA", showRetrialStaff_befEndPtA);
		model.addAttribute("showRetrialStaff_befEndPtB", showRetrialStaff_befEndPtB);
		model.addAttribute("showRetrialStaff_AtEndPt", showRetrialStaff_AtEndPt);
		renderJsI18N(LMS1700M01Page.class);
		renderJsI18N(AbstractEloanPage.class);

		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2);
		model.addAttribute("tabID", tabID);

		Panel panel = getPanel(page, meta);
		panel.processPanelData(model, params);

		new RetrialPtMgrIdPanel("divRetrialPtMgrIdPanel").processPanelData(model, params);
	}

	// 頁籤
	public Panel getPanel(int index, L170M01A meta) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS1700S01Panel(TAB_CTX, true, meta);
			break;
		case 2:
			panel = new LMS1700S02Panel(TAB_CTX, true, meta);
			break;
		case 3:
			panel = new LMS1700S03Panel(TAB_CTX, true);
			break;
		case 4:
			panel = new LMS1700S04Panel(TAB_CTX, true);
			break;
		case 5:
			panel = new LMS1700S05Panel(TAB_CTX, true);
			break;
		case 6:
			panel = new LMS1700S06Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS1700S01Panel(TAB_CTX, true, meta);
			break;
		}
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L170M01A.class;
	}
}
