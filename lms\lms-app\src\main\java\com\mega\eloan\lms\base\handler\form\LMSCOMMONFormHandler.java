/* 
 *  LMSCOMMONFormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.handler.form;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.gwclient.OBSMqGwClient;
import com.mega.eloan.common.gwclient.OBSMqGwReqMessage;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.BeanValidator;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.RealEstateLoanUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.LMSL140M01MPanel;
import com.mega.eloan.lms.base.panels.LMSM02BPanel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.CentralBankControlService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C101S01EDao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01CDao;
import com.mega.eloan.lms.dao.L130M01ADao;
import com.mega.eloan.lms.dao.L130S01ADao;
import com.mega.eloan.lms.dao.L130S02ADao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01CDao;
import com.mega.eloan.lms.dao.L800M01ADao;
import com.mega.eloan.lms.dc.action.ColumnGetDBInfoFactory;
import com.mega.eloan.lms.dc.util.Column;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.mfaloan.bean.ELF600;
import com.mega.eloan.lms.mfaloan.service.LNLNF07AService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF348Service;
import com.mega.eloan.lms.mfaloan.service.MisELF442Service;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import com.mega.eloan.lms.mfaloan.service.MisELF517Service;
import com.mega.eloan.lms.mfaloan.service.MisELF600Service;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.mfaloan.service.MisGrpdtlService;
import com.mega.eloan.lms.mfaloan.service.MisLNF076Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.L120A01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L130M01A;
import com.mega.eloan.lms.model.L130M01B;
import com.mega.eloan.lms.model.L130S01A;
import com.mega.eloan.lms.model.L130S01B;
import com.mega.eloan.lms.model.L130S02A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M01Q;
import com.mega.eloan.lms.model.L140M01X;
import com.mega.eloan.lms.model.L140S05A;
import com.mega.eloan.lms.model.L140S07A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L800M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 共用的formHandle
 * </pre>
 * 
 * @since 2012/10/3
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/3,REX,new
 *          <li>2013/06/24,Fantasy,queryBlackName add ci0024 check flag
 *          </ul>
 */
@Scope("request")
@Controller("lmscommonformhandler")
public class LMSCOMMONFormHandler extends AbstractFormHandler {

	@Resource
	CodeTypeService codetypeService;
	@Resource
	LMSService lmsService;
	@Resource
	CLSService clsService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	BranchService branchService;

	@Resource
	L120M01ADao l120m01aDao;
	@Resource
	L120M01CDao l120m01cDao;
	@Resource
	L130M01ADao l130m01aDao;
	@Resource
	L130S02ADao l130s02aDao;
	@Resource
	L140M01ADao l140m01aDao;
	@Resource
	L130S01ADao l130s01aDao;
	@Resource
	CodeTypeService codeService;
	@Resource
	MisLNF076Service misLnf076Service;

	@Resource
	MisStoredProcService msps;

	@Resource
	MisCustdataService mcs;
	@Resource
	SysParameterService sysParameterService;
	@Resource
	OBSMqGwClient obsMqGwClient;
	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;

	@Resource
	L800M01ADao l800m01aDao;

	@Resource
	MisELF506Service misELF506Service;

	@Resource
	C101S01EDao c101s01eDao;

	@Resource
	MisELF442Service misELF442Service;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	DwLnquotovService dwLnquotovService;

	@Resource
	ICustomerService customerService;

	@Resource
	MisMISLN20Service misMISLN20Service;

	@Resource
	MisGrpdtlService misGrpdtlService;

	@Resource
	LNLNF07AService lnLNF07AService;

	@Resource
	MisGrpcmpService misGrpcmpService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisELF517Service misElf517Service;

	@Resource
	L140M01CDao l140m01cDao;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	MisELF600Service misELF600Service;

	@Resource
	DocFileService docFileService;

	@Resource
	CentralBankControlService centralBankControlService;

	private static Logger loggerCom = LoggerFactory
			.getLogger(LMSCOMMONFormHandler.class);

	/**
	 * 測試用code
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult testCode(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// EL1LMSB1.NSF VLMSDB201Z;VLMS14020G;VLMS11020G;VLMS14011G T05201
		// 192.168.211.193 LMS ELOANDB 007
		// lmsService.upMisByCls(l120m01aDao
		// .findByMainId("b4b6ee702a7d4adaa5266c2ac3eea469"));
		// OnlineDCHandler.main(null);
		this.logger.debug("VANCE ========================== START");
		for (Column c : ColumnGetDBInfoFactory.getInstance().getInfo(null,
				"LMS", "L140M01A")) {
			this.logger.debug(c.toString());
		}
		this.logger.debug("VANCE ========================== END");

		return result;
	}

	/**
	 * 查詢國外分行
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryForeignBranch(PageParameters params)
			throws CapException {
		String type = Util.trim(params.getString("type"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> brank = new TreeMap<String, String>();
		if ("12".equals(type)) {
			List<Map<String, Object>> rows = this.misdbBASEService
					.findMISELFBKSNOBank();

			for (Map<String, Object> dataMap : rows) {
				String code = Util.trim(((String) dataMap.get("NUMBER")));
				String name = Util.trim(((String) dataMap.get("NAME")));
				brank.put(code, name);
			}
		} else if ("99".equals(type)) {
			List<Map<String, Object>> rows = this.misdbBASEService
					.findMISSynBankBy99();
			for (Map<String, Object> dataMap : rows) {
				String code = Util.trim(((String) dataMap.get("CODE")));
				String name = Util.trim(((String) dataMap.get("NAME")));
				brank.put(code, name);
			}
		}
		result.set("foreignBranch", new CapAjaxFormResult(brank));

		return result;
	}

	/**
	 * 查詢所選銀行底下的分行
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBranch(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainBranch = Util.trim(params.getString("mainBranch"));
		Map<String, String> m = new TreeMap<String, String>();
		// 如果是兆豐銀行查詢的位置不一樣
		if (UtilConstants.兆豐銀行代碼.equals(mainBranch)) {
			// 抓需要的銀行代碼
			List<IBranch> bank = branchService.getAllBranch();
			for (IBranch b : bank) {
				String brName = Util.trim(b.getBrName());
				String brCode = b.getBrNo();
				m.put(brCode, brName);
			}
		} else {
			List<Map<String, Object>> rows = misdbBASEService
					.findMISSynBank(Util.trim(mainBranch));
			for (Map<String, Object> dataMap : rows) {
				String code = Util.trim(((String) dataMap.get("CODE")));
				String name = Util.trim(((String) dataMap.get("NAME")));
				m.put(code, name);
			}
		}
		result.set("brankList", new CapAjaxFormResult(m));
		return result;
	}

	/**
	 * 檢查是否有異常通報明細檔
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkL130s01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("hasL130s01a", false);
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		// J-GGG-XXXX
		// List<L130S01A> list = l130s01aDao.findInsertData(mainId);
		List<L130S01A> list = lmsService.findL130s01aInsertData(mainId);

		if (list != null && !list.isEmpty()) {
			result.set("hasL130s01a", true);
		}
		return result;
	}

	/**
	 * 取得往來異常戶系統資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getLnfe0851(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult unNormalForm = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new HashMap<String, String>();
		L120M01A model = l120m01aDao.findByMainId(mainId);
		L130M01A l130m01a = lmsService.findL130m01aByMainId(mainId);
		// 儲存串好的異常類別代碼與異常類別名稱
		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		String mdClass = null;
		String process = null;
		if (model != null) {
			// 開始進行查詢作業
			// map = lmsService.getLnfe0851("005", "05127512", "0"); // 測試用資料
			map = lmsService.getLnfe0851(Util.trim(model.getCaseBrId()),
					Util.trim(model.getCustId()), Util.trim(model.getDupNo()));
			if (!map.isEmpty()) {
				// 若登錄日期為空，則顯示查無往來異常戶資料訊息
				if (Util.isEmpty(Util.trim(map.get("mddt")))) {
					// other.msg1=查無往來異常戶資料
					throw new CapMessageException(
							pop.getProperty("other.msg1"), getClass());
				} else {
					// 若異常通報主檔為空則建立一個(避免使用者尚未儲存異常通報就點擊引進往來異常戶資料按鈕)
					if (l130m01a == null) {
						l130m01a = new L130M01A();
						l130m01a.setMainId(mainId);
						l130m01a.setDocType(Util.trim(model.getDocType()));
						l130m01a.setCreateTime(CapDate.getCurrentTimestamp());
						l130m01a.setCreator(user.getUserId());
					}
					// 異常類別代碼
					mdClass = Util.trim(map.get("mdClass"));
					// 異常類別名稱
					process = Util.trim(map.get("process"));
					sb.append(mdClass).append("-").append(process);
					l130m01a.setMdClass(mdClass);
					l130m01a.setProcess(process);
					// 開始設定資料到前端
					unNormalForm = DataParse.toResult(l130m01a,
							DataParse.Delete, "oid");
					unNormalForm.set("pMdClass", sb.toString());
					result.set("unNormalForm", unNormalForm);
					lmsService.save(l130m01a);
					// 印出執行成功訊息!
					result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
							RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
				}
			} else {
				// 印出查無資料訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料));
			}
		}
		return result;
	}

	/**
	 * 引進異常通報帳務資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getUnNormalData(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L130M01A model = lmsService.findL130m01aByMainId(mainId);
		L120M01A meta = l120m01aDao.findByMainId(mainId);
		Map<String, Object> map = new HashMap<String, Object>();
		String custId = Util.trim(meta.getCustId());
		String dupNo = Util.trim(meta.getDupNo());
		String brno = Util.trim(meta.getCaseBrId());
		String allCustId = getAllCust(custId, dupNo, false);
		// 避免使用者尚未儲存異常通報表就執行此功能(引進帳務資料)
		if (model == null) {
			model = new L130M01A();
			model.setMainId(mainId);
			model.setDocType(Util.trim(meta.getDocType()));
			model.setCreateTime(CapDate.getCurrentTimestamp());
			model.setCreator(user.getUserId());
		}
		// 開始透過SQL向MIS取得相關帳務資料
		map = lmsService.getUnNormalData(brno, custId, dupNo, allCustId);
		// J-110-0250 小規異常通報
		// 國內企金 小規異常通報 擔保品預設 "信保基金十成保證"
		if (meta != null) {
			if (!UtilConstants.Casedoc.typCd.海外.equals(Util.trim(meta
					.getTypCd()))
					&& lmsService.hideAbnormalPanelbyCaseType(meta)) {
				map.put("collStat", "信保基金10成保證");
			}
		}
		if (!map.isEmpty()) {
			CapAjaxFormResult unNormalForm = new CapAjaxFormResult(map);
			CapBeanUtil.map2Bean(map, model);
			lmsService.save(model);
			result.set("unNormalForm", unNormalForm);
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		} else {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		return result;
	}

	/**
	 * 取得完整Id(統編加重覆序號)
	 * 
	 * @param custId
	 *            統編
	 * @param dupNo
	 *            重覆序號
	 * @param trimZero
	 *            是否要去除重覆序號為零數值
	 * @return
	 */
	private String getAllCust(String custId, String dupNo, boolean trimZero) {
		if (trimZero) {
			return getAllCust(custId, dupNo);
		} else {
			StringBuilder strb = new StringBuilder();
			return strb.append(CapString.fillString(custId, 10, false, ' '))
					.append(dupNo).toString();
		}
	}

	/**
	 * 取得完整Id(統編加重覆序號)-- 重覆序號為零會自動改成空白
	 * 
	 * @param custId
	 *            統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	private String getAllCust(String custId, String dupNo) {
		StringBuilder strb = new StringBuilder();
		if ("0".equals(dupNo)) {
			dupNo = UtilConstants.Mark.SPACE;
		}
		return strb.append(CapString.fillString(custId, 10, false, ' '))
				.append(dupNo).toString();
	}

	/**
	 * 取得異常通報類別
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getUnNormalClass(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> map = lmsService.getUnNormalClass();
		if (!map.isEmpty()) {
			CapAjaxFormResult sMdClass = new CapAjaxFormResult(map);
			result.set("sMdClass", sMdClass);
		}
		return result;
	}

	/**
	 * 取得異常通報事項
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getUnNormal(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		String unitType = params.getString("unitType");
		// J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// J-110-0250_05097_B1001 Web e-Loan信保案件異常通報預設勾選依信保基金規定辦理項目
		String promiseCase = Util.trim(params.getString("promiseCase"));

		List<L130S01A> listL130s01a = lmsService.findL130s01aByMainId(mainId);
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> map = new TreeMap<String, String>();
		// 是否為解除異常通報或停權
		boolean isOther = params.getBoolean("isOther");
		// 解除異常通報:B,停權:C
		String type = Util.trim(params.getString("type"));

		StringBuffer default_seq = new StringBuffer("");

		if (isOther) {
			// 解除異常通報或停權
			map = lmsService.selUnNormalBC(type);
		} else {
			// 一般事項
			if (UtilConstants.BankNo.授管處.equals(unitNo)
					&& Util.notEquals(unitType, "5")) {
				// 授管處
				map = lmsService.getUnNormal3();
			} else if (UtilConstants.BankNo.中區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.中部區域授信中心.equals(unitNo)
					|| UtilConstants.BankNo.北一區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.北二區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.南區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.南部區域授信中心.equals(unitNo)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(unitNo)) {
				// 營運中心
				map = lmsService.getUnNormal2();
			} else {
				// 分行
				map = lmsService.getUnNormal1();

				if (lmsService.hideAbnormalPanelbyCaseType(l120m01a)) {

					// J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
					boolean hasA01 = false;
					boolean hasA27_28 = false;
					boolean hasA21 = false;
					boolean haseA33 = false;
					boolean isClose = false;

					if (listL130s01a != null && !listL130s01a.isEmpty()) {
						for (L130S01A l130s01a : listL130s01a) {

							if (Util.equals(Util.trim(l130s01a.getSeqNo()),
									"A01")) {
								hasA01 = true;
							}
							if (Util.equals(Util.trim(l130s01a.getSeqNo()),
									"A21")) {
								hasA21 = true;
							}
							if (Util.equals(Util.trim(l130s01a.getSeqNo()),
									"A27")
									|| Util.equals(
											Util.trim(l130s01a.getSeqNo()),
											"A28")) {
								hasA27_28 = true;
							}
							if (Util.equals(Util.trim(l130s01a.getSeqNo()),
									"A33")) {
								haseA33 = true;
							}

						}
					}

					// 除解除異常通報外，系統檢核小規模營業人異常通報項目
					if (!hasA01) {
						default_seq.append(
								(Util.equals(default_seq.toString(), "") ? ""
										: ",")).append("A01");
					}
					if (!hasA21) {
						default_seq.append(
								(Util.equals(default_seq.toString(), "") ? ""
										: ",")).append("A21");
					}
					if (!hasA27_28) {
						default_seq.append(
								(Util.equals(default_seq.toString(), "") ? ""
										: ",")).append("A28");
					}
					if (!haseA33) {
						default_seq.append(
								(Util.equals(default_seq.toString(), "") ? ""
										: ",")).append("A33");
					}
				} else if (lmsService
						.hidePanelbyCaseType_caseType_00A(l120m01a)) {
					// J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
					// 區域營運中心/授信審查處權限內異常通報(含小規模營業人)
					boolean hasA01 = false;
					boolean hasA27_28 = false;
					boolean hasA21 = false;
					boolean haseA33 = false;
					boolean isClose = false;

					if (listL130s01a != null && !listL130s01a.isEmpty()) {
						for (L130S01A l130s01a : listL130s01a) {

							if (Util.equals(Util.trim(l130s01a.getSeqNo()),
									"A01")) {
								hasA01 = true;
							}
							if (Util.equals(Util.trim(l130s01a.getSeqNo()),
									"A21")) {
								hasA21 = true;
							}
							if (Util.equals(Util.trim(l130s01a.getSeqNo()),
									"A27")
									|| Util.equals(
											Util.trim(l130s01a.getSeqNo()),
											"A28")) {
								hasA27_28 = true;
							}
							if (Util.equals(Util.trim(l130s01a.getSeqNo()),
									"A33")) {
								haseA33 = true;
							}

						}
					}

					// 除解除異常通報外，系統檢核小規模營業人異常通報項目
					if (!hasA01) {
						default_seq.append(
								(Util.equals(default_seq.toString(), "") ? ""
										: ",")).append("A01");
					}

					if (Util.equals(Util.trim(promiseCase), "Y")) {
						if (!hasA21) {
							default_seq
									.append((Util.equals(
											default_seq.toString(), "") ? ""
											: ",")).append("A21");
						}
					}

					if (!hasA27_28) {
						default_seq.append(
								(Util.equals(default_seq.toString(), "") ? ""
										: ",")).append("A28");
					}
					if (!haseA33) {
						default_seq.append(
								(Util.equals(default_seq.toString(), "") ? ""
										: ",")).append("A33");
					}

				} else {
					// J-110-0250_05097_B1001 Web e-Loan信保案件異常通報預設勾選依信保基金規定辦理項目

					if (Util.equals(Util.trim(promiseCase), "Y")) {
						// J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程

						boolean hasA21 = false;

						boolean isClose = false;

						if (listL130s01a != null && !listL130s01a.isEmpty()) {
							for (L130S01A l130s01a : listL130s01a) {

								if (Util.equals(Util.trim(l130s01a.getSeqNo()),
										"A21")) {
									hasA21 = true;
								}

							}

						}
						if (!hasA21) {
							default_seq
									.append((Util.equals(
											default_seq.toString(), "") ? ""
											: ",")).append("A21");
						}

					}

				}

			}
		}
		if (!map.isEmpty()) {
			CapAjaxFormResult seq = new CapAjaxFormResult(map);
			result.set("seq", seq);
		}

		result.set("default_seq", default_seq.toString());

		return result;
	}

	/**
	 * 新增使用者勾選異常通報事項並組合字串
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addUnNormal(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		String unitType = params.getString("unitType");
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		List<L130M01B> listL130m01b = lmsService.findL130m01bByMainId(mainId);
		List<L130S01A> listAll = lmsService.findL130s01aByMainId(mainId);
		// 防止取得的結果是null
		if (listAll.isEmpty()) {
			listAll = new ArrayList<L130S01A>();
		}
		L130M01B l130m01b = null;
		List<L130S01A> list = new ArrayList<L130S01A>();
		// 依照登錄分行判斷要抓取哪種分行的資料
		if (UtilConstants.BankNo.授管處.equals(unitNo)
				&& Util.notEquals(unitType, "5")) {
			// 授管處
			list = lmsService.findByMainIdAndBranchKind(mainId,
					UtilConstants.branchKind2.授管處);
		} else if (UtilConstants.BankNo.中區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.中部區域授信中心.equals(unitNo)
				|| UtilConstants.BankNo.北一區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.北二區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.南區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.南部區域授信中心.equals(unitNo)
				|| UtilConstants.BankNo.桃竹苗區營運中心.equals(unitNo)) {
			// 營運中心
			list = lmsService.findByMainIdAndBranchKind(mainId,
					UtilConstants.branchKind2.營運中心);
		} else {
			// 分行
			list = lmsService.findByMainIdAndBranchKind(mainId,
					UtilConstants.branchKind2.分行);
		}
		List<L130S01A> sortList = new ArrayList<L130S01A>();
		List<L130S01A> addList = new ArrayList<L130S01A>();
		String seqKind = null;
		if (list.isEmpty()) {
			// 防止第一次新增發生錯誤
			list = new ArrayList<L130S01A>();
		} else {
			// 將已有的資料塞入List裡(之後排序要用)
			sortList.addAll(list);
		}
		String[] seqNos = params.getStringArray("seqNos");
		if (seqNos.length > 0) {
			// 進行檢核是否新增重覆異常通報事項
			JSONObject json = isDuplicateSeq(seqNos, listAll);
			if (!json.isEmpty()) {
				StringBuilder sb = new StringBuilder();
				// other.msg2=已產生
				// other.msg3=明細，系統不再新增!!
				sb.append(pop.getProperty("other.msg2"))
						.append(json.get("seqNo")).append("[")
						.append(json.get("seqName")).append("]")
						.append(pop.getProperty("other.msg3"));
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.注意, sb.toString()),
						getClass());
			}
			for (String seqNo : seqNos) {
				Map<String, String> map = getL130s01aData(seqNo, unitType);
				if (!map.isEmpty()) {
					L130S01A model = new L130S01A();
					model.setMainId(mainId);
					model.setCreateBY(UtilConstants.Casedoc.L120s04aCreateBY.人工產生);
					model.setCreateTime(CapDate.getCurrentTimestamp());
					model.setCreator(user.getUserId());
					// 將Map塞入Model
					DataParse.toBean(DataParse.transformJSON(map), model);
					// 若為停權則設定停權Flag
					if ("C".equals(Util.trim(map.get("bigKind")))) {
						// 設定停權Flag
						model.setIsStop("Y");
						// 設定預設停權月
						if (UtilConstants.BankNo.授管處.equals(unitNo)
								&& Util.notEquals(unitType, "5")) {
							// 授管處
							model.setHeadMonth(Util.trim(map.get("stopMon")));
						} else {
							// 營運中心
							model.setAreaMonth(Util.trim(map.get("stopMon")));
						}
					}
					if (UtilConstants.BankNo.授管處.equals(unitNo)
							&& Util.notEquals(unitType, "5")) {
						// 授管處
						seqKind = Util.trim(model.getSeqKind());
						if (seqNo.indexOf("A9") != -1) {
							if ("A91".equals(seqNo) || "A92".equals(seqNo)
									|| "A93".equals(seqNo)
									|| "A94".equals(seqNo)
									|| "A95".equals(seqNo)
									|| "A96".equals(seqNo)
									|| "A97".equals(seqNo)
									|| "A98".equals(seqNo)) {
								// 建霖：營運中心或授管處新增之事項(A91~A98)，擬/已辦應該都是空的
								model.setSeqKind(UtilConstants.seqKind.空白);
							} else {
								model.setSeqKind(UtilConstants.seqKind.其他); // 總處新增其他，就顯示其他|3
							}
						} else {
							model.setSeqKind(UtilConstants.seqKind.空白);
						}
						model.setBranchKind(UtilConstants.branchKind2.授管處);
					} else if (UtilConstants.BankNo.中區營運中心.equals(unitNo)
							|| UtilConstants.BankNo.中部區域授信中心.equals(unitNo)
							|| UtilConstants.BankNo.北一區營運中心.equals(unitNo)
							|| UtilConstants.BankNo.北二區營運中心.equals(unitNo)
							|| UtilConstants.BankNo.南區營運中心.equals(unitNo)
							|| UtilConstants.BankNo.南部區域授信中心.equals(unitNo)
							|| UtilConstants.BankNo.桃竹苗區營運中心.equals(unitNo)) {
						// 營運中心
						seqKind = Util.trim(model.getSeqKind());
						if (seqNo.indexOf("A9") != -1) {
							if ("A91".equals(seqNo) || "A92".equals(seqNo)
									|| "A93".equals(seqNo)
									|| "A94".equals(seqNo)
									|| "A95".equals(seqNo)
									|| "A96".equals(seqNo)
									|| "A97".equals(seqNo)
									|| "A98".equals(seqNo)) {
								// 建霖：營運中心或授管處新增之事項(A91~A98)，擬/已辦應該都是空的
								model.setSeqKind(UtilConstants.seqKind.空白);
							} else {
								model.setSeqKind(UtilConstants.seqKind.其他); // 總處新增其他，就顯示其他|3
							}
						} else {
							model.setSeqKind(UtilConstants.seqKind.空白);
						}
						model.setBranchKind(UtilConstants.branchKind2.營運中心);
					} else {
						// 分行
						model.setBranchKind(UtilConstants.branchKind2.分行);
						seqKind = Util.trim(model.getSeqKind());
						switch (Util.parseInt(seqKind)) {
						case 0:
							// 已辦|1 擬辦|2 其他|3
							model.setSeqKind(UtilConstants.seqKind.擬辦);
							break;
						case 1:
							// 已辦|1 擬辦|2 其他|3
							model.setSeqKind(seqKind);
							break;
						case 2:
							// 已辦|1 擬辦|2 其他|3
							model.setSeqKind(seqKind);
							break;
						case 3:
							// 已辦|1 擬辦|2 其他|3
							model.setSeqKind(seqKind);
							// other.msg4=詳陳報及說明事項欄
							model.setDocDscr(pop.getProperty("other.msg4"));
						default:
							// 已辦|1 擬辦|2 其他|3
							model.setSeqKind(UtilConstants.seqKind.已辦);
						}
						;
						// 選擇A99其他時，擬/已辦要預設顯示其他
						if ("A99".equals(seqNo)) {
							model.setSeqKind(UtilConstants.seqKind.其他);
						}
					}
					addList.add(model);
					sortList.add(model);
				}
			}

			// J-GGG-XXXX
			l130m01b = getL130m01b(listAll, listL130m01b, sortList, mainId);
			lmsService.saveL130m01bListL130s01a(l130m01b, addList);
			// 將合併後的字串設定到前端
			result.set("willIdea", Util.trim(l130m01b.getSeqDscr()));
			// 印出新增成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.新增成功));
		}
		return result;
	}

	/**
	 * 取得合併後的L130M01B
	 * 
	 * @param list
	 * @param mainId
	 * @return
	 */
	private L130M01B getL130m01b(List<L130S01A> listAll,
			List<L130M01B> listL130m01b, List<L130S01A> list, String mainId) {
		L130M01B l130m01b = null;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		String unitType = user.getUnitType();
		if (UtilConstants.BankNo.授管處.equals(unitNo)
				&& (Util.equals(unitType, "S"))) {
			// 授管處
			if (!listL130m01b.isEmpty()) {
				for (L130M01B model : listL130m01b) {
					if (UtilConstants.branchKind.授管處合併字串.equals(Util.trim(model
							.getBranchKind()))) {
						lmsService.delete(model);
						break;
					}
				}
			}
			l130m01b = initL130m01b(mainId, UtilConstants.branchKind.授管處合併字串);
		} else if (UtilConstants.BankNo.中區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.中部區域授信中心.equals(unitNo)
				|| UtilConstants.BankNo.北一區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.北二區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.南區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.南部區域授信中心.equals(unitNo)
				|| UtilConstants.BankNo.桃竹苗區營運中心.equals(unitNo)) {
			// 營運中心
			if (!listL130m01b.isEmpty()) {
				for (L130M01B model : listL130m01b) {
					if (UtilConstants.branchKind.營運中心合併字串.equals(Util
							.trim(model.getBranchKind()))) {
						lmsService.delete(model);
						break;
					}
				}
			}
			l130m01b = initL130m01b(mainId, UtilConstants.branchKind.營運中心合併字串);
		} else {
			// 分行
			if (!listL130m01b.isEmpty()) {
				for (L130M01B model : listL130m01b) {
					if (UtilConstants.branchKind.分行合併字串.equals(Util.trim(model
							.getBranchKind()))) {
						lmsService.delete(model);
						break;
					}
				}
			}
			l130m01b = initL130m01b(mainId, UtilConstants.branchKind.分行合併字串);
		}
		// 開始進行組合
		l130m01b.setSeqDscr(getSeqDscr(listAll, list, l130m01b));
		return l130m01b;
	}

	/**
	 * 取得合併後的L130M01B(指定參數)
	 * 
	 * @param listL130m01b
	 * @param list
	 * @param mainId
	 * @param branchKind
	 * @return
	 */
	private L130M01B getL130m01b(List<L130S01A> listAll,
			List<L130M01B> listL130m01b, List<L130S01A> list, String mainId,
			String branchKind) {
		L130M01B l130m01b = null;
		if (UtilConstants.branchKind2.授管處.equals(branchKind)) {
			// 授管處
			if (!listL130m01b.isEmpty()) {
				for (L130M01B model : listL130m01b) {
					if (UtilConstants.branchKind.授管處合併字串.equals(Util.trim(model
							.getBranchKind()))) {
						lmsService.delete(model);
						break;
					}
				}
			}
			l130m01b = initL130m01b(mainId, UtilConstants.branchKind.授管處合併字串);
		} else if (UtilConstants.branchKind2.營運中心.equals(branchKind)) {
			// 營運中心
			if (!listL130m01b.isEmpty()) {
				for (L130M01B model : listL130m01b) {
					if (UtilConstants.branchKind.營運中心合併字串.equals(Util
							.trim(model.getBranchKind()))) {
						lmsService.delete(model);
						break;
					}
				}
			}
			l130m01b = initL130m01b(mainId, UtilConstants.branchKind.營運中心合併字串);
		} else if (UtilConstants.branchKind2.分行.equals(branchKind)) {
			// 分行
			if (!listL130m01b.isEmpty()) {
				for (L130M01B model : listL130m01b) {
					if (UtilConstants.branchKind.分行合併字串.equals(Util.trim(model
							.getBranchKind()))) {
						lmsService.delete(model);
						break;
					}
				}
			}
			l130m01b = initL130m01b(mainId, UtilConstants.branchKind.分行合併字串);
		}
		// 開始進行組合
		l130m01b.setSeqDscr(getSeqDscr(listAll, list, l130m01b));
		return l130m01b;
	}

	/**
	 * 初始化L130M01B
	 * 
	 * @param mainId
	 *            文件編號
	 * @param branchKind
	 *            單位種類
	 * @return
	 */
	private L130M01B initL130m01b(String mainId, String branchKind) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L130M01B model = new L130M01B();
		model.setBranchKind(branchKind);
		model.setMainId(mainId);
		model.setCreateTime(CapDate.getCurrentTimestamp());
		model.setCreator(user.getUserId());
		return model;
	}

	/**
	 * 取得組合後的字串
	 * 
	 * @param listAll
	 *            Grid事項所有單位事項
	 * @param list
	 *            該單位相關的事項
	 * @param l130m01b
	 * @return
	 */
	private String getSeqDscr(List<L130S01A> listAll, List<L130S01A> list,
			L130M01B l130m01b) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		// 需要合併字串的List(營運中心、授管處專用)
		List<L130S01A> tempList = new ArrayList<L130S01A>();
		// 比對條件的List(營運中心、授管處專用)
		List<L130S01A> chkList = new ArrayList<L130S01A>();
		List<L130S01A> sortList = new ArrayList<L130S01A>();
		chkList.clear();
		tempList.clear();
		sortList.clear();
		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		String branchKind = Util.trim(l130m01b.getBranchKind());
		if (UtilConstants.branchKind.分行合併字串.equals(branchKind)) {
			for (L130S01A model : list) {
				// 凡使用者登錄擬(已)辦之事項皆合併起來。
				// 若為1:已，若為2:擬，若為其他數值：空白
				if (UtilConstants.seqKind.擬辦.equals(Util.trim(model
						.getSeqKind()))) {
					// other.msg5=◎擬
					sb.append(
							(sb.length() > 0) ? "<br/>"
									: UtilConstants.Mark.SPACE)
							.append(("C".equals(Util.trim(model.getBigKind()))) ? UtilConstants.Mark.SPACE
									: pop.getProperty("other.msg5"))
							.append(getSetData(model));
				} else if (UtilConstants.seqKind.已辦.equals(Util.trim(model
						.getSeqKind()))) {
					// other.msg6=◎已
					sb.append(
							(sb.length() > 0) ? "<br/>"
									: UtilConstants.Mark.SPACE)
							.append(pop.getProperty("other.msg6"))
							.append(getSetData(model));
				} else {
					sb.append(
							(sb.length() > 0) ? "<br/>"
									: UtilConstants.Mark.SPACE).append("◎")
							.append(getSetData(model));
				}
			}
		} else if (UtilConstants.branchKind.營運中心合併字串.equals(branchKind)) {
			// 當營運中心不為空時執行合併作業
			chkList.addAll(list);
			// if (!list.isEmpty()) {
			// chkList.addAll(list);
			// }
			for (L130S01A l130s01a : chkList) {
				if ("1".equals(Util.trim(l130s01a.getAreaDecide()))) {
					tempList.add(l130s01a);
				}
			}
			// 進行排序
			// sortL130s01a(tempList, sortList);
			for (L130S01A model : tempList) {
				String headTitle = UtilConstants.Mark.SPACE;
				// 使用者登錄擬(已)辦事項後，將營運中心批復後的事項合併起來。
				// 若為1:空白
				// 若為2:若代碼為A9開頭，則為空白，否則為同意。
				// 若為其他數值：若代碼為A9開頭，則為空白，否則為應。
				// A91~ A99會將項目名稱加說明(docDscr)組進去，例如: 其他應辦理事項(一)：應由借戶.....
				if ("1".equals(Util.trim(model.getAreaDecide()))) {
					// if (UtilConstants.seqKind.已辦.equals(Util.trim(model
					// .getSeqKind()))) {
					// headTitle = "◎";
					// } else
					if ("1".equals(Util.trim(model.getSeqKind()))) {
						if (!Util.trim(model.getSeqNo()).startsWith("A9")) {
							// other.msg8=◎同意
							headTitle = pop.getProperty("other.msg8");
						} else {
							headTitle = "◎";
						}
					} else {
						if (!Util.trim(model.getSeqNo()).startsWith("A9")) {
							// other.msg7=◎應
							headTitle = pop.getProperty("other.msg7");
						} else {
							headTitle = "◎";
						}
					}

					// 依建霖提供信件新增程式 Miller added at 2013/01/10
					if ("B".equals(Util.trim(model.getBigKind()))) {
						// other.msg8=◎同意
						headTitle = pop.getProperty("other.msg8");
					}
					sb.append(
							(sb.length() > 0) ? "<br/>"
									: UtilConstants.Mark.SPACE).append(
							getSetData2(model, headTitle, branchKind));
				}
			}
			// }
		} else if (UtilConstants.branchKind.授管處合併字串.equals(branchKind)) {
			// 當授管處不為空時執行合併作業
			chkList.addAll(list);
			// if (!list.isEmpty()) {
			// chkList.addAll(list);
			// }
			for (L130S01A l130s01a : chkList) {
				if ("1".equals(Util.trim(l130s01a.getHeadDecide()))) {
					tempList.add(l130s01a);
				}
			}
			// tempList.addAll(chkList);

			// 進行排序
			// sortL130s01a(tempList, sortList);
			for (L130S01A model : tempList) {
				String headTitle = UtilConstants.Mark.SPACE;
				// 使用者登錄擬(已)辦事項後，將授管處批復後的事項合併起來。
				// 若為1:空白
				// 若為2:若代碼為A9開頭，則為空白，否則為同意。
				// 若為其他數值：若代碼為A9開頭，則為空白，否則為應。
				// A91~ A99會將項目名稱加說明(docDscr)組進去，例如: 其他應辦理事項(一)：應由借戶.....
				if ("1".equals(Util.trim(model.getHeadDecide()))) {
					// if (UtilConstants.seqKind.已辦.equals(Util.trim(model
					// .getSeqKind()))) {
					// headTitle = "◎";
					// } else
					if ("1".equals(Util.trim(model.getSeqKind()))) {
						if (!Util.trim(model.getSeqNo()).startsWith("A9")) {
							// other.msg8=◎同意
							headTitle = pop.getProperty("other.msg8");
						} else {
							headTitle = "◎";
						}
					} else {
						if (!Util.trim(model.getSeqNo()).startsWith("A9")) {
							// other.msg7=◎應
							headTitle = pop.getProperty("other.msg7");
						} else {
							headTitle = "◎";
						}
					}

					// 依建霖提供信件新增程式 Miller added at 2013/01/10
					if ("B".equals(Util.trim(model.getBigKind()))) {
						// other.msg8=◎同意
						headTitle = pop.getProperty("other.msg8");
					}
					sb.append(
							(sb.length() > 0) ? "<br/>"
									: UtilConstants.Mark.SPACE).append(
							getSetData2(model, headTitle, branchKind));
				}
			}
			// }
		}

		if (sb.length() > 0) {
			if (UtilConstants.branchKind.營運中心合併字串.equals(branchKind)
					|| UtilConstants.branchKind.授管處合併字串.equals(branchKind)) {
				// other.msg9=核定事項：<br/>
				return pop.getProperty("other.msg9") + sb.toString();
			} else {
				return sb.toString();
			}
		} else {
			return UtilConstants.Mark.SPACE;
		}
	}

	private void sync_L130S01A_ctlDscr(String l130s01a_oid) {
		L130S01A l130s01a = lmsService.findL130s01aByOid(l130s01a_oid);
		if (l130s01a == null) {
			return;
		}
		String mainId = l130s01a.getMainId();
		String seqNo = l130s01a.getSeqNo();
		String ctlDscr = _gen_l130s02a_ctlDscr(mainId, seqNo);
		l130s01a.setCtlDscr(ctlDscr);
		lmsService.save(l130s01a);
	}

	private String _gen_l130s02a_ctlDscr(String mainId, String seqNo) {
		String ctlDscr = "";

		Set<String> seqNoSet = new HashSet<String>();
		if (true) {
			seqNoSet.add(LMSUtil.L130S01A_A01);
			seqNoSet.add(LMSUtil.L130S01A_A32);
		}
		if (seqNoSet.contains(seqNo)) {
			Map<String, TreeSet<String>> tm = new TreeMap<String, TreeSet<String>>();

			List<L130S02A> l130s02a_list = l130s02aDao.findByMainIdSeqNo(
					mainId, seqNo);
			for (L130S02A l130s02a : l130s02a_list) {
				String ctlType = l130s02a.getCtlType();
				String ctlName = Util.trim(l130s02a.getCtlName());
				String _msg = Util.trim(l130s02a.getCtlItem())
						+ (Util.isNotEmpty(ctlName) ? " " : "") + ctlName;
				if (!tm.containsKey(ctlType)) {
					tm.put(ctlType, new TreeSet<String>());
				}
				tm.get(ctlType).add(_msg);
			}

			List<String> r = new ArrayList<String>();
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSM02BPanel.class);
			for (String ctlType : tm.keySet()) {
				String msg = StringUtils.join(tm.get(ctlType), "、");
				String ctlTypeDesc = "";
				if (Util.equals("1", ctlType)) {
					ctlTypeDesc = prop.getProperty("L130S02A.ctlType.1");
				} else if (Util.equals("2", ctlType)) {
					ctlTypeDesc = prop.getProperty("L130S02A.ctlType.2");
				} else if (Util.equals("3", ctlType)) {
					ctlTypeDesc = prop.getProperty("L130S02A.ctlType.3");
				} else {
					ctlTypeDesc = ctlType;
				}
				r.add(ctlTypeDesc + "：" + msg);
			}

			ctlDscr = StringUtils.join(r, "。");
		}
		return ctlDscr;
	}

	private String _getStr(L130S01A model) {
		return Util.trim(model.getDocDscr());
	}

	private String _getSeqName(L130S01A model) {
		String r = Util.trim(model.getSeqName());
		String ctlDscr = Util.trim(model.getCtlDscr());
		Set<String> seqNoSet = new HashSet<String>();
		if (true) {
			seqNoSet.add(LMSUtil.L130S01A_A01);
			seqNoSet.add(LMSUtil.L130S01A_A32);
		}
		if (seqNoSet.contains(model.getSeqNo()) && Util.isNotEmpty(ctlDscr)) {
			ctlDscr = ("(" + ctlDscr + ")");
			// ---------
			r += ctlDscr;
		}
		return r;
	}

	/**
	 * 取得分行異常通報事項串好的資訊
	 * 
	 * @param model
	 *            異常通報表明細檔
	 * @return 分行異常通報事項串好的資訊
	 */
	private String getSetData(L130S01A model) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		if (model != null) {
			String seqName = _getSeqName(model);
			String runDate = CapDate.formatDate(model.getRunDate(),
					UtilConstants.DateFormat.YYYY_MM_DD);
			String setKind = Util.trim(model.getSetKind());
			String setCurr = Util.trim(model.getSetCurr());
			BigDecimal setAmt = model.getSetAmt();
			String docDscr = _getStr(model);

			if (Util.isNotEmpty(seqName)) {
				sb.append(seqName)
						.append((Util.isNotEmpty(setAmt)
								|| Util.isNotEmpty(docDscr) || Util
								.isNotEmpty(runDate)) ? "：" : "。");
			}
			if (Util.isNotEmpty(runDate)) {
				// other.msg10=日期：
				sb.append(pop.getProperty("other.msg10")).append(runDate)
						.append("，");
			}
			if (UtilConstants.setKind.金額.equals(setKind)) {
				// 沒金額資料就不要印
				if (Util.isNotEmpty(setAmt)) {
					// other.msg11=金額：
					sb.append(pop.getProperty("other.msg11"));
				}
			} else if (UtilConstants.setKind.設押金額.equals(setKind)) {
				// 沒金額資料就不要印
				if (Util.isNotEmpty(setAmt)) {
					// other.msg12=設押金額：
					sb.append(pop.getProperty("other.msg12"));
				}
			} else if (UtilConstants.setKind.解除曝險金額.equals(setKind)) {
				// 沒金額資料就不要印
				if (Util.isNotEmpty(setAmt)) {
					// other.msg13=解除曝險金額：
					sb.append(pop.getProperty("other.msg13"));
				}
			} else {
				// 沒金額資料就不要印
				if (Util.isNotEmpty(setAmt)) {
					// other.msg11=金額：
					sb.append(pop.getProperty("other.msg11"));
				}
			}
			if (Util.isNotEmpty(setCurr)) {
				sb.append(setCurr);
			}
			if (Util.isNotEmpty(setAmt)) {
				// other.msg14=仟元。
				sb.append(NumConverter.addComma(setAmt)).append(
						pop.getProperty("other.msg14"));
				if (Util.isNotEmpty(docDscr)) {
					// 若後面有值則已逗號取代
					sb.deleteCharAt(sb.length() - 1).append("，");
				}
			}
			if (Util.isNotEmpty(docDscr)) {
				sb.append(docDscr).append("。");
			}
			if (sb.length() > 0 && sb.toString().charAt(sb.length() - 1) == '，') {
				sb.setLength(sb.length() - 1);
				sb.append("。");
			}
		}
		return sb.toString();
	}

	/**
	 * 取得授管處(營運中心)異常通報事項串好的資訊
	 * 
	 * @param model
	 *            異常通報表明細檔
	 * @return 授管處(營運中心)異常通報事項串好的資訊
	 */
	private String getSetData2(L130S01A model, String headTitle,
			String branchKind) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		// J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
		L120M01A l120m01a = l120m01aDao.findByMainId(model.getMainId());
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		if (model != null) {
			String seqNo = Util.trim(model.getSeqNo());
			String seqName = _getSeqName(model);
			String bigKind = Util.trim(model.getBigKind());
			String docDscr = _getStr(model);
			if (seqNo.startsWith("A9")) {
				sb.append(headTitle).append(seqName)
						.append((Util.isNotEmpty(docDscr)) ? "：" : "。");
			} else {
				if ("C".equals(bigKind)) {
					// 依規定暫停單位主管授權
					// other.msg15=◎依規定暫停單位主管授權
					// other.msg15a=◎擬依規定暫停單位主管授權
					// other.msg16=個月
					// 營運中心選擇C01~C04時，前面要加擬，與授管處不同(授管處不用)
					if (!Util.trim(model.getSeqNo()).startsWith("A9")) {

						// J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
						sb.append(
								((!lmsService
										.isAbnormalAuthLvlAreaSign(l120m01a) && UtilConstants.branchKind.營運中心合併字串
										.equals(branchKind))) ? pop
										.getProperty("other.msg15a") : pop
										.getProperty("other.msg15"))
								.append(Util.isEmpty(Util.trim(model
										.getHeadMonth())) ? Util.trim(model
										.getAreaMonth()) : Util.trim(model
										.getHeadMonth()))
								.append(pop.getProperty("other.msg16"))
								.append("：")
								.append(Util.trim(model.getSeqName()))
								.append((Util.isNotEmpty(docDscr)) ? "，" : "。");
					} else {
						sb.append(headTitle).append(seqName)
								.append((Util.isNotEmpty(docDscr)) ? "：" : "。");
					}
				} else if ("B".equals(bigKind)) {
					// other.msg17=解除異常通報－
					sb.append(headTitle).append(pop.getProperty("other.msg17"))
							.append(Util.trim(model.getSeqName()))
							.append((Util.isNotEmpty(docDscr)) ? "：" : "。");
				} else {
					sb.append(headTitle).append(seqName)
							.append((Util.isNotEmpty(docDscr)) ? "：" : "。");
				}
			}
			if (Util.isNotEmpty(docDscr)) {
				sb.append(docDscr).append("。");
			}
		}
		return sb.toString();
	}

	// /**
	// * 依照顯示順序(seqShow)升冪排序已合併的List
	// *
	// * @param tempList
	// * @param sortList
	// */
	// private void sortL130s01a(List<L130S01A> tempList, List<L130S01A>
	// sortList) {
	// // 進行排序
	// for (L130S01A model : tempList) {
	// sortList.add(model);
	// }
	// Collections.sort(sortList, new Comparator<L130S01A>() {
	// public int compare(L130S01A model1, L130S01A model2) {
	// return (Util.parseInt(Util.trim(model1.getSeqShow())) > Util
	// .parseInt(Util.trim(model2.getSeqShow())) ? 1 : (Util
	// .parseInt(Util.trim(model1.getSeqShow())) == Util
	// .parseInt(Util.trim(model2.getSeqShow())) ? 0 : -1));
	// }
	// });
	// }

	/**
	 * 查詢異常通報事項檔
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL130m01b(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// 由前端傳入參數決定要查詢哪種異常通報事項檔
		String branchKind = Util.trim(params.getString("branchKind"));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		List<L130S01A> list = new ArrayList<L130S01A>();
		List<L130S01A> listAll = lmsService.findL130s01aByMainId(mainId);
		if (listAll.isEmpty()) {
			listAll = new ArrayList<L130S01A>();
		}
		L130M01B model = null;
		// 取得授管批覆意見Model
		L130M01B headModel = lmsService.findL130m01bByUniqueKey(mainId,
				UtilConstants.branchKind.授管處批覆意見_給分行看的);
		if (Util.isNotEmpty(branchKind)) {
			// 參數有值則透過參數查詢
			if (branchKind.equals(UtilConstants.branchKind2.授管處)) {
				// 授管處
				list = lmsService.findByMainIdAndBranchKind(mainId,
						UtilConstants.branchKind2.授管處);
			} else if (branchKind.equals(UtilConstants.branchKind2.營運中心)) {
				// 營運中心
				list = lmsService.findByMainIdAndBranchKind(mainId,
						UtilConstants.branchKind2.營運中心);
			} else if (branchKind.equals(UtilConstants.branchKind2.分行)) {
				// 分行
				list = lmsService.findByMainIdAndBranchKind(mainId,
						UtilConstants.branchKind2.分行);
			}
		} else {
			String unitType = params.getString("unitType");
			if (UtilConstants.BankNo.授管處.equals(unitNo)
					&& Util.notEquals(unitType, "5")) {
				// 授管處
				list = lmsService.findByMainIdAndBranchKind(mainId,
						UtilConstants.branchKind2.授管處);
			} else if (UtilConstants.BankNo.中區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.中部區域授信中心.equals(unitNo)
					|| UtilConstants.BankNo.北一區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.北二區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.南區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.南部區域授信中心.equals(unitNo)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(unitNo)) {
				// 營運中心
				list = lmsService.findByMainIdAndBranchKind(mainId,
						UtilConstants.branchKind2.營運中心);
			} else {
				// 分行
				list = lmsService.findByMainIdAndBranchKind(mainId,
						UtilConstants.branchKind2.分行);
			}
		}
		List<L130M01B> listL130m01b = lmsService.findL130m01bByMainId(mainId);

		if (Util.isNotEmpty(branchKind)) {
			// 透過參數取得合併字串
			model = getL130m01b(listAll, listL130m01b, list, mainId, branchKind);
		} else {
			// 直接依照登錄分行取得合併字串
			model = getL130m01b(listAll, listL130m01b, list, mainId);
		}
		// 將合併後的字串儲存
		lmsService.save(model);
		// 將合併後的字串設定到前端
		result.set("willIdea", (model == null) ? UtilConstants.Mark.SPACE
				: Util.trim(model.getSeqDscr()));
		// 設定授管處批覆意見
		result.set("willIdea4", (headModel == null) ? UtilConstants.Mark.SPACE
				: Util.trim(headModel.getSeqDscr()));
		return result;
	}

	/**
	 * 查詢異常通報明細
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL130s01a(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		Map<String, String> map = new HashMap<String, String>();
		Map<String, String> sqlMap = new HashMap<String, String>();
		Map<String, String> currMap = codeService
				.findByCodeType("Common_Currcy");
		boolean isOther = params.getBoolean("isOther");
		String type = Util.trim(params.getString("type"));

		L130S01A model = lmsService.findL130s01aByOid(oid);
		if (model != null) {
			CapAjaxFormResult tGridContentForm = DataParse.toResult(model);
			String branchKind = Util.trim(model.getBranchKind());
			// 透過SQL查詢異常通報事項明細
			if (isOther) {
				// 解除異常通報、停權
				sqlMap = lmsService.selUnNormalBCa(type,
						Util.trim(model.getSeqNo()));
			} else {
				// 一般事項
				if (UtilConstants.branchKind2.分行.equals(branchKind)) {
					sqlMap = lmsService.getUnNormal1a(Util.trim(model
							.getSeqNo()));
				} else if (UtilConstants.branchKind2.營運中心.equals(branchKind)) {
					sqlMap = lmsService.getUnNormal2a(Util.trim(model
							.getSeqNo()));
				} else if (UtilConstants.branchKind2.授管處.equals(branchKind)) {
					sqlMap = lmsService.getUnNormal3a(Util.trim(model
							.getSeqNo()));
				}
			}

			// 開始依照擬/已辦設定變動下拉式選單
			String seqKind = Util.trim(sqlMap.get("seqKind"));
			switch (Util.parseInt(seqKind)) {
			// other.msg18=已辦
			// other.msg19=擬辦
			// other.msg20=其他
			case 0:
				map.put("1", pop.getProperty("other.msg18"));
				map.put("2", pop.getProperty("other.msg19"));
				break;
			case 1:
				map.put("1", pop.getProperty("other.msg18"));
				break;
			case 2:
				map.put("2", pop.getProperty("other.msg19"));
				break;
			case 3:
				map.put("3", pop.getProperty("other.msg20"));
				break;
			default:
			}
			;
			tGridContentForm.set("seqKind", new CapAjaxFormResult(map));
			tGridContentForm.set("seqKindVal", Util.trim(model.getSeqKind()));
			// other.msg21=人工產生
			// other.msg22=系統產生
			tGridContentForm
					.set("createBY",
							UtilConstants.Casedoc.L120s04aCreateBY.人工產生
									.equals(Util.trim(model.getCreateBY())) ? pop
									.getProperty("other.msg21")
									: UtilConstants.Casedoc.L120s04aCreateBY.系統產生
											.equals(Util.trim(model
													.getCreateBY())) ? pop
											.getProperty("other.msg22")
											: UtilConstants.Mark.SPACE);
			if (!currMap.isEmpty()) {
				tGridContentForm.set("setCurr", new CapAjaxFormResult(currMap));
				tGridContentForm.set("setCurrVal",
						Util.trim(model.getSetCurr()));
			}
			result.set("tGridContentForm", tGridContentForm);
		}
		return result;
	}

	/**
	 * 檢核是否新增重覆異常通報事項
	 * 
	 * @param seqNos
	 *            異常通報事項代碼
	 * @param list
	 *            異常通報明細群組
	 * @return json 有值: 有重覆, json 無值: 無重覆
	 */
	private JSONObject isDuplicateSeq(String[] seqNos, List<L130S01A> list) {
		JSONObject json = new JSONObject();
		for (String seqNo : seqNos) {
			for (L130S01A model : list) {
				if (seqNo.equals(Util.trim(model.getSeqNo()))) {
					// 是否重覆
					json.put("result", true);
					// 重覆項目代碼
					json.put("seqNo", Util.trim(model.getSeqNo()));
					// 重覆項目名稱
					json.put("seqName", Util.trim(model.getSeqName()));
					return json;
				}
			}
		}
		return json;
	}

	/**
	 * 依照異常通報事項代碼取得明細資料
	 * 
	 * @param seqNo
	 *            異常通報事項代碼
	 * @return 明細資料Map
	 */
	private Map<String, String> getL130s01aData(String seqNo, String unitType) {
		Map<String, String> map = new HashMap<String, String>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		if (seqNo.startsWith("B") || seqNo.startsWith("C")) {
			if (seqNo.startsWith("B")) {
				// 解除異常通報
				map = lmsService.selUnNormalBCa("B", seqNo);
			} else {
				// 停權
				map = lmsService.selUnNormalBCa("C", seqNo);
			}
		} else {
			if (UtilConstants.BankNo.授管處.equals(unitNo)
					&& Util.notEquals(unitType, "5")) {
				// 授管處
				map = lmsService.getUnNormal3a(seqNo);
			} else if (UtilConstants.BankNo.中區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.中部區域授信中心.equals(unitNo)
					|| UtilConstants.BankNo.北一區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.北二區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.南區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.南部區域授信中心.equals(unitNo)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(unitNo)) {
				// 營運中心
				map = lmsService.getUnNormal2a(seqNo);
			} else {
				// 分行
				map = lmsService.getUnNormal1a(seqNo);
			}
		}
		return map;
	}

	/**
	 * 批覆事項(營運中心、授管處專用)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult decideL130s01a(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		String branchKind = null;
		if (UtilConstants.BankNo.授管處.equals(unitNo)) {
			// 授管處
			branchKind = UtilConstants.branchKind2.授管處;
		} else if (UtilConstants.BankNo.中區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.中部區域授信中心.equals(unitNo)
				|| UtilConstants.BankNo.北一區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.北二區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.南區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.南部區域授信中心.equals(unitNo)
				|| UtilConstants.BankNo.桃竹苗區營運中心.equals(unitNo)) {
			// 營運中心
			branchKind = UtilConstants.branchKind2.營運中心;
		}
		String kind = Util.trim(params.getString("kind"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oids = params.getString("oids");
		List<L130S01A> list = new ArrayList<L130S01A>();
		if (Util.isNotEmpty(oids)) {
			String aOid[] = oids.split(",");
			for (String oid : aOid) {
				L130S01A model = lmsService.findL130s01aByOid(oid);
				if (model != null) {
					if ("1".equals(kind)
							&& !UtilConstants.seqKind.已辦.equals(Util.trim(model
									.getSeqKind()))) {
						// 核定
						if (UtilConstants.branchKind2.營運中心.equals(branchKind)) {
							// 營運中心
							model.setAreaDecide(UtilConstants.DEFAULT.是);
						} else {
							// 授管處
							model.setHeadDecide(UtilConstants.DEFAULT.是);
							if (Util.isEmpty(Util.trim(model.getHeadMonth()))) {
								model.setHeadMonth(Util.trim(model
										.getAreaMonth()));
							}
						}
					} else if ("2".equals(kind)
							&& !UtilConstants.seqKind.已辦.equals(Util.trim(model
									.getSeqKind()))) {
						// 還原
						if (UtilConstants.branchKind2.營運中心.equals(branchKind)) {
							// 營運中心
							model.setAreaDecide(UtilConstants.Mark.SPACE);
						} else {
							// 授管處
							model.setHeadDecide(UtilConstants.Mark.SPACE);
						}
					}
					list.add(model);
				}
			}
		}
		if (!list.isEmpty()) {
			// 進行儲存
			lmsService.saveListL130s01a(list);
		} else {
			// other.msg23=找不到資料執行本功能，請確認資料!
			throw new CapMessageException(
					RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.注意, pop.getProperty("other.msg23")),
					getClass());
		}
		return result;
	}

	/**
	 * 依照使用者勾選異常通報明細事項進行刪除作業
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL130s01a(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		String unitType = params.getString("unitType");
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oids = params.getString("oids");
		List<L130S01A> list = new ArrayList<L130S01A>();
		// 記錄非本行新增事項資料
		List<String> listMsg = new ArrayList<String>();
		String branchKind = null;
		if (UtilConstants.BankNo.授管處.equals(unitNo)
				&& Util.notEquals(unitType, "5")) {
			// 授管處
			branchKind = UtilConstants.branchKind2.授管處;
		} else if (UtilConstants.BankNo.中區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.中部區域授信中心.equals(unitNo)
				|| UtilConstants.BankNo.北一區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.北二區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.南區營運中心.equals(unitNo)
				|| UtilConstants.BankNo.南部區域授信中心.equals(unitNo)
				|| UtilConstants.BankNo.桃竹苗區營運中心.equals(unitNo)) {
			// 營運中心
			branchKind = UtilConstants.branchKind2.營運中心;
		} else {
			// 分行
			branchKind = UtilConstants.branchKind2.分行;
		}
		if (Util.isNotEmpty(oids)) {
			String aOid[] = oids.split(",");
			for (String oid : aOid) {
				L130S01A model = lmsService.findL130s01aByOid(oid);
				if (model != null) {
					// 自己新增的事項才可加入List準備刪除
					if (branchKind.equals(Util.trim(model.getBranchKind()))) {
						list.add(model);
					} else {
						StringBuilder tempSb = new StringBuilder();
						tempSb.setLength(0);
						tempSb.append(Util.trim(model.getSeqNo()))
								.append(" - ")
								.append(Util.trim(model.getSeqName()));
						listMsg.add(tempSb.toString());
					}
				}
			}
		}
		// 檢查到有非本行新增事項則無法刪除
		if (!listMsg.isEmpty()) {
			StringBuilder tempSb = new StringBuilder();
			tempSb.setLength(0);
			for (String msg : listMsg) {
				tempSb.append("<br/>").append(msg);
			}
			// other.msg24=以下事項非本行新增事項，無法刪除，請確認！
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.注意,
					pop.getProperty("other.msg24") + tempSb.toString()), getClass());
		} else if (!list.isEmpty()) {
			if (true) {
				// 若刪除 seqNo為A01或A32，把 L130S02A 也刪掉
				String[] seqNoArr = { LMSUtil.L130S01A_A01,
						LMSUtil.L130S01A_A32 };
				for (String seqNo : seqNoArr) {
					boolean matchSeqNo = false;
					String match_l130s01a_mainId = "";
					for (L130S01A l130s01a : list) {
						if (Util.equals(seqNo, l130s01a.getSeqNo())) {
							matchSeqNo = true;
							match_l130s01a_mainId = l130s01a.getMainId();
							break;
						}
					}
					if (matchSeqNo) {
						List<L130S02A> l130s02a_list = l130s02aDao
								.findByMainIdSeqNo(match_l130s01a_mainId, seqNo);
						if (CollectionUtils.isNotEmpty(l130s02a_list)) {
							lmsService.deleteListL130S02A(l130s02a_list);
						}
					}
				}
			}
			// 進行刪除
			lmsService.deleteListL130s01a(list);
			// 印出刪除成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		} else {
			// other.msg25=找不到資料可刪除，請確認資料!
			throw new CapMessageException(
					RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.注意, pop.getProperty("other.msg25")),
					getClass());
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL130S02A(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ====
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oids = Util.trim(params.getString("oids"));
		String l130s01a_oid = Util.trim(params.getString("l130s01a_oid"));

		List<L130S02A> delList = new ArrayList<L130S02A>();
		if (Util.isNotEmpty(oids)) {
			String[] dataSplit = oids.split("\\|");
			for (String oid : dataSplit) {
				L130S02A model = l130s02aDao.findByOid_NotDel(oid);
				if (model != null) {
					delList.add(model);
				}
			}
		}

		if (delList.size() == 0) {
			// other.msg25=找不到資料可刪除，請確認資料!
			throw new CapMessageException(
					RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.注意, pop.getProperty("other.msg25")),
					getClass());
		} else {
			// 進行刪除
			lmsService.deleteListL130S02A(delList);
			sync_L130S01A_ctlDscr(l130s01a_oid);
			// 印出刪除成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addL130S02A(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ====
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String seqNo = Util.trim(params.getString("seqNo"));
		String endDate = Util.trim(params.getString("endDate"));
		String ctlType = Util.trim(params.getString("ctlType"));
		String ctlItem_raw = Util.trim(params.getString("ctlItem"));
		String ctlName = Util.trim(params.getString("ctlName"));
		String l130s01a_oid = Util.trim(params.getString("l130s01a_oid"));

		List<L130S02A> list = new ArrayList<L130S02A>();
		if (Util.isNotEmpty(ctlItem_raw)) {
			String[] dataSplit = Util.trim(ctlItem_raw).split("\\|");
			List<String> ctlItem_ok = new ArrayList<String>();
			List<String> ctlItem_fail = new ArrayList<String>();
			for (String ctlItem : dataSplit) {
				lmsService.deleteByDeletedTimeL130S02A(mainId, seqNo, ctlType,
						ctlItem);

				L130S02A o = l130s02aDao.findByUniqueIdx(mainId, seqNo,
						ctlType, ctlItem);
				if (o == null) {
					ctlItem_ok.add(ctlItem);
				} else {
					ctlItem_fail.add(ctlItem);
				}
			}

			if (ctlItem_fail.size() == 1 && ctlItem_ok.size() == 0) {
				// 一次新增 1筆，錯了拋 Exception
				// 一次新增 1筆 N 筆 (EX: cntrNo)，有部分已存在
				Map<String, String> map = new HashMap<String, String>();
				map.put("msg", ctlItem_fail.get(0));

				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.資料已存在, map),
						getClass());
			}
			for (String ctlItem : ctlItem_ok) {
				L130S02A model = new L130S02A();
				// ------
				_set_L130S02A(model, mainId, seqNo, endDate, ctlType, ctlItem,
						ctlName);
				// ------
				list.add(model);
			}
		}

		if (list.size() > 0) {
			lmsService.saveListL130S02A(list);
			sync_L130S01A_ctlDscr(l130s01a_oid);
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.新增成功));
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addL130S02AFree(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ====
		Properties prop_LMSM02BPanel = MessageBundleScriptCreator
				.getComponentResource(LMSM02BPanel.class);

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String seqNo = Util.trim(params.getString("seqNo"));
		String endDate = Util.trim(params.getString("endDate"));
		String ctlType = Util.trim(params.getString("ctlType"));
		String ctlItem = Util.trim(params.getString("ctlItem"));
		String ctlName = "";
		String l130s01a_oid = Util.trim(params.getString("l130s01a_oid"));

		if (Util.isEmpty(ctlItem)) {
			HashMap<String, String> msg = new HashMap<String, String>();
			msg.put("colName",
					prop_LMSM02BPanel.getProperty("L130S02A.ctlItem"));

			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, msg), getClass());
		}
		if (true) {
			// 驗證資料
			if (Util.equals("1", ctlType)) {
				// 額度序號，只檢查長度是否有12
				if (ctlItem.length() != 12) {
					HashMap<String, String> msg = new HashMap<String, String>();
					msg.put("colName", ctlItem);

					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.查無資料, msg),
							getClass());
				}
			} else if (Util.equals("2", ctlType)) {
				Map<String, Object> custMap = null;
				if (ctlItem.length() <= 11) {// 統編+重複碼
					custMap = customerService.findByIdDupNo(
							Util.trim(Util.getLeftStr(ctlItem, 10)),
							Util.getRightStr(ctlItem, 1));
				}
				if (MapUtils.isEmpty(custMap)) {
					HashMap<String, String> msg = new HashMap<String, String>();
					msg.put("colName", ctlItem);

					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.查無資料, msg),
							getClass());
				} else {
					ctlName = Util.trim(MapUtils.getString(custMap, "CNAME"));
				}
			} else if (Util.equals("3", ctlType)) {
				Map<String, Object> grpMap = null;
				if (ctlItem.length() <= 4) {// 集團
					grpMap = misGrpdtlService.findDtlByGrpId(ctlItem);
				}
				if (MapUtils.isEmpty(grpMap)) {
					HashMap<String, String> msg = new HashMap<String, String>();
					msg.put("colName", ctlItem);

					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.查無資料, msg),
							getClass());
				} else {
					ctlName = Util.trim(MapUtils.getString(grpMap, "GRPNM"));// 集團名稱
				}
			} else {
				HashMap<String, String> msg = new HashMap<String, String>();
				msg.put("colName",
						prop_LMSM02BPanel.getProperty("L130S02A.ctlType"));

				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, msg),
						getClass());
			}
		}

		List<L130S02A> list = new ArrayList<L130S02A>();
		if (true) {
			if (true) {
				lmsService.deleteByDeletedTimeL130S02A(mainId, seqNo, ctlType,
						ctlItem);

				L130S02A o = l130s02aDao.findByUniqueIdx(mainId, seqNo,
						ctlType, ctlItem);
				if (o != null) {
					Map<String, String> map = new HashMap<String, String>();
					map.put("msg", ctlItem);

					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.資料已存在, map),
							getClass());
				}
			}

			L130S02A model = new L130S02A();
			// ------
			_set_L130S02A(model, mainId, seqNo, endDate, ctlType, ctlItem,
					ctlName);
			// ------
			list.add(model);
		}

		if (list.size() > 0) {
			lmsService.saveListL130S02A(list);
			sync_L130S01A_ctlDscr(l130s01a_oid);
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.新增成功));
		}
		return result;
	}

	private void _set_L130S02A(L130S02A model, String mainId, String seqNo,
			String endDate, String ctlType, String ctlItem, String ctlName) {
		model.setMainId(mainId);
		model.setSeqNo(seqNo);
		if (Util.equals(LMSUtil.L130S01A_A01, seqNo)) {
			model.setEndDate(CapDate.parseDate(endDate));
		} else {
			model.setEndDate(null);
		}
		model.setCtlType(ctlType);
		model.setCtlItem(ctlItem);
		model.setCtlName(ctlName);
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryL130S02A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("oid"));
		L130S02A model = null;
		if (Util.isNotEmpty(oid)) {
			model = l130s02aDao.findByOid_NotDel(oid);

		}
		if (model != null) {
			result = DataParse.toResult(model);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult updateL130S02A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("oid"));
		String endDate = Util.trim(params.getString("endDate"));

		List<L130S02A> updateList = new ArrayList<L130S02A>();
		if (Util.isNotEmpty(oid)) {
			L130S02A model = l130s02aDao.findByOid_NotDel(oid);
			if (model != null) {
				model.setEndDate(CapDate.parseDate(endDate));
				// ---
				updateList.add(model);
			}
		}

		if (updateList.size() > 0) {
			lmsService.saveListL130S02A(updateList);
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}
		return result;
	}

	/**
	 * 儲存異常通報明細資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL130s01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String tGridContentForm = Util.trim(params
				.getString("tGridContentForm"));
		JSONObject json = new JSONObject();
		L130S01A model = lmsService.findL130s01aByOid(oid);
		if (model != null) {
			// 當Form有值時
			if (Util.isNotEmpty(tGridContentForm)) {
				json = JSONObject.fromObject(tGridContentForm);
				json.remove("createBY");
			}
			DataParse.toBean(json, model);
			if (Util.isEmpty(Util.trim(model.getSeqKind()))) {
				model.setSeqKind(UtilConstants.seqKind.空白);
			}
			lmsService.save(model);
			sync_L130S01A_ctlDscr(model.getOid());
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		return result;
	}

	/**
	 * 儲存授管處給分行看批覆意見(授管處專用)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL130m01b(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String headDscr = Util.trim(params.getString("headDscr"));
		L130M01B model = lmsService.findL130m01bByUniqueKey(mainId,
				UtilConstants.branchKind.授管處批覆意見_給分行看的);
		if (model == null) {
			model = new L130M01B();
			model.setMainId(mainId);
			model.setBranchKind(UtilConstants.branchKind.授管處批覆意見_給分行看的);
			model.setCreateTime(CapDate.getCurrentTimestamp());
			model.setCreator(user.getUserId());
		}
		model.setSeqDscr(headDscr);
		lmsService.save(model);
		// 印出儲存成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 查詢授管處給分行看批覆意見(授管處專用)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL130m01bHead(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L130M01B model = lmsService.findL130m01bByUniqueKey(mainId,
				UtilConstants.branchKind.授管處批覆意見_給分行看的);
		if (model != null) {
			result.set("headDscr", Util.trim(model.getSeqDscr()));
		} else {
			result.set("headDscr", UtilConstants.Mark.SPACE);
		}
		return result;
	}

	/**
	 * 檢查本行簽報書底下所有異常通報明細是否有被停權
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkIsStop(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		// 是否為新增案件
		boolean isNew = params.getBoolean("isNew");
		String docKind = Util.trim(params.getString("docKind"));
		String docCode = Util.trim(params.getString("docCode"));
		String docType = Util.trim(params.getString("docType"));
		// J-GGG-XXXX
		String caseType = Util.trim(params.getString("caseType"));
		String caseTypeA = Util.trim(params.getString("caseTypeA"));
		String authLvl = Util.trim(params.getString("authLvl"));

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}
		if (isNew != true || Util.equals(docType, "")) {
			docType = l120m01a.getDocType();
		}
		// J-110-0250_05097_B1001 Web e-Loan信保案件異常通報預設勾選依信保基金規定辦理項目
		if (isNew != true && Util.equals(docKind, "")) {
			docKind = l120m01a.getDocKind();
		}

		// String docType = l120m01a.getDocType();

		CapAjaxFormResult result = new CapAjaxFormResult();
		// 新增(呈主管覆核)授權內簽報書才需要進行此檢查作業

		// J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
		if (UtilConstants.Casedoc.DocCode.異常通報.equals(docCode)) {
			String errAbnormalMsg = lmsService
					.chkAbnormalDocKind(docType, docKind, docCode, authLvl,
							caseType, l120m01a.getCaseBrId());
			if (Util.notEquals(errAbnormalMsg, "")) {
				// other.msg174=異常通報必須為授權外案件！
				// other.msg217=異常通報不得為分行授權內案件簽報
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, errAbnormalMsg),
						getClass());
			}

			// J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
			// 國內 企金
			if (Util.equals(caseType, "")) {
				caseType = l120m01a.getCaseType();
			}
			if (Util.equals(UtilConstants.Casedoc.caseType.區域營運中心_授信審查處權限內異常通報,
					Util.trim(caseType))
					&& Util.equals(UtilConstants.Casedoc.caseTypeA.小規模營業人,
							Util.trim(caseTypeA))) {
				String chkOnlyCaseC = Util.trim(lmsService
						.getSysParamDataValue("LMS_ABNORMAL_CHK_ONLY_CASE_C"));
				if (Util.notEquals(chkOnlyCaseC, "N")) {
					String custId = Util.trim(l120m01a.getCustId());
					String dupNo = Util.trim(l120m01a.getDupNo());
					if (Util.notEquals(custId, "") && Util.notEquals(dupNo, "")) {

						if (!UtilConstants.Casedoc.typCd.海外.equals(Util
								.trim(l120m01a.getTypCd()))) {
							if (UtilConstants.Casedoc.DocType.企金
									.equals(docType)) {
								String[] arr = lmsService
										.getOnlySmallBussCaseC(custId, dupNo);
								if (Util.notEquals(arr[0], "Y")) {

									// other.msg219=借款人項下尚有非小規模營業人之下列其他未銷戶額度，簽報書文件資訊頁籤之欄位「適用方案」不得為「小規模營業人」：<BR>{0}
									String msg = MessageFormat.format(
											pop.getProperty("other.msg219"),
											new Object[] { arr[1] });

									throw new CapMessageException(
											RespMsgHelper.getMessage(
													UtilConstants.AJAX_RSP_MSG.執行有誤,
													msg), getClass());

								}
							}

						}

					}
				}

			}

		}

		if (UtilConstants.Casedoc.DocKind.授權內.equals(docKind)) {

			JSONObject json = lmsService.selIsstop("1".equals(docType) ? "LMS"
					: "CLS");

			// result = "1" -> 停權
			if (!json.isEmpty() && "1".equals(Util.trim(json.get("result")))) {
				StringBuilder sb = new StringBuilder();
				sb.setLength(0);

				// J-107-0298_05097_B1001 分行停權時若e-Loan簽報書中僅有遠匯額度時，簽報書可依分行權限內覆核。
				// 若案下非不變的額度科目都為遠匯額度(961 962 963 964)，則不用停權
				boolean onlyFx = false;
				if (!isNew && "1".equals(docType)) {

					String checkItem14 = "961,962,963,964"; // 遠匯、換匯現請額度只能為USD

					List<L140M01A> l140m01as = l140m01aDao
							.findL140m01aListByL120m01cMainIdOrderByCust(
									mainId,
									UtilConstants.Cntrdoc.ItemType.額度明細表);

					int totalL140m01aCount = 0;
					int has961Count = 0;
					for (L140M01A l140m01a : l140m01as) {
						if (!LMSUtil.isContainValue(
								Util.trim(l140m01a.getProPerty()),
								UtilConstants.Cntrdoc.Property.不變)) {

							totalL140m01aCount = totalL140m01aCount + 1;
							boolean hasCheckItem14 = false;
							List<L140M01C> l140m01cs = l140m01cDao
									.findByMainId(l140m01a.getMainId());
							if (l140m01cs != null && !l140m01cs.isEmpty()) {

								// 用來暫存已登錄授信科目
								HashMap<String, String> itemMap = new HashMap<String, String>();
								for (L140M01C l140m01c : l140m01cs) {
									itemMap.put(l140m01c.getLoanTP(), "");
								}

								String[] item14 = checkItem14.split(",");
								List<String> asList14 = Arrays.asList(item14);

								for (String key : itemMap.keySet()) {
									if (asList14.contains(Util.getLeftStr(key,
											3))) {
										// 有遠匯科目
										has961Count = has961Count + 1;
										break;
									}
								}

							}

						}

					}

					if (totalL140m01aCount > 0
							&& totalL140m01aCount == has961Count) {
						// 有額度明細表且所有非不變的額度明細表皆為遠匯額度
						onlyFx = true;
					}

				}

				// 比對停權起日,因為拒往為公告次日，所以公告當天ELF510已經有資料，但是ELF510_SUSPENDED_BD 為下一日
				String beginDtStr = Util.trim(json.get("beginDt")); // 停權起日
				Date beginDt = Util.parseDate(beginDtStr);
				Date toDay = CapDate.getCurrentTimestamp();
				// 判斷本日是否已達停權起日
				if (beginDt != null) {
					if (toDay.compareTo(beginDt) >= 0) {
						if (isNew) {
							// other.msg26=本行有異常通報案件已被停權，無法新增授權內簽報書
							sb.append(pop.getProperty("other.msg26"));
							result.set("addMsg", sb.toString());
						} else {
							if (UtilConstants.Casedoc.AuthLvl.分行授權內
									.equals(l120m01a.getAuthLvl())) {
								// other.msg27=本行有異常通報案件已被停權，無法呈主管覆核
								sb.append(pop.getProperty("other.msg27"));

								if (!onlyFx) {
									throw new CapMessageException(
											RespMsgHelper.getMessage(
													UtilConstants.AJAX_RSP_MSG.注意,
													sb.toString()), getClass());
								}

							}
						}
					}

				} else {
					if (isNew) {
						// other.msg26=本行有異常通報案件已被停權，無法新增授權內簽報書
						sb.append(pop.getProperty("other.msg26"));
						result.set("addMsg", sb.toString());
					} else {
						if (UtilConstants.Casedoc.AuthLvl.分行授權內.equals(l120m01a
								.getAuthLvl())) {
							// other.msg27=本行有異常通報案件已被停權，無法呈主管覆核
							sb.append(pop.getProperty("other.msg27"));

							if (!onlyFx) {
								throw new CapMessageException(
										RespMsgHelper.getMessage(
												UtilConstants.AJAX_RSP_MSG.注意,
												sb.toString()), getClass());
							}

						}
					}
				}

			}
		}
		return result;
	}

	/**
	 * 依照參貸行代碼設定異常通報表參貸行檔
	 * 
	 * @param brans
	 *            參貸行代碼
	 * @param mainId
	 *            文件編號
	 * @return 設定好的異常通報參貸行群組
	 */
	private List<L130S01B> setListL130s01b(String[] brans, String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L130S01B> listL130s01b = lmsService.findL130s01bByMainId(mainId);
		List<L130S01B> list = new ArrayList<L130S01B>();
		// 檢查使用者是否有勾選至少一筆參貸行
		if (brans.length > 0) {
			if (!listL130s01b.isEmpty()) {
				// 刪除已存在的異常通報參貸行檔群組
				lmsService.deleteListL130s01b(listL130s01b);
			}
			// 開始設定參貸行
			for (String bran : brans) {
				L130S01B model = new L130S01B();
				model.setMainId(mainId);
				model.setBranchIds(bran);
				model.setCreateTime(CapDate.getCurrentTimestamp());
				model.setCreator(user.getUserId());
				list.add(model);
			}
		}
		return list;
	}

	/**
	 * 儲存異常通報表參貸行檔
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveListL130s01b(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String branchs = Util.trim(params.getString("branchs"));
		String brans[] = branchs.split(",");
		// 開始設定參貸行資料
		List<L130S01B> listL130s01b = setListL130s01b(brans, mainId);
		try {
			SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
					Util.trim(params.getString("tempSave",
							UtilConstants.DEFAULT.否)));
			lmsService.saveListL130s01b(listL130s01b);
		} catch (Exception e) {
			logger.error("[saveAll] service1201.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", UtilConstants.Mark.HTMLSPACE);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}
		// 印出儲存成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 取得往來異常戶異動類別
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = false)
	public IResult GetLNFE0851UpFlag(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		// J-GGG-XXXX
		List<L130S01A> list = lmsService.findL130s01aInsertData(mainId);

		String flag = null;
		// 一般事項參數
		List<String> sbKindNoA = new ArrayList<String>();
		// sbKindNoA.setLength(0);
		// 解除異常通報參數
		List<String> sbKindNoB = new ArrayList<String>();
		// sbKindNoB.setLength(0);
		// 停權參數
		List<String> sbKindNoC = new ArrayList<String>();
		// sbKindNoC.setLength(0);
		// 參數Map
		Map<String, String[]> map = new HashMap<String, String[]>();
		for (L130S01A l130s01a : list) {
			if ("A".equals(Util.trim(l130s01a.getBigKind()))) {
				// sbKindNoA.append(
				// (sbKindNoA.length() > 0) ? ","
				// : UtilConstants.Mark.SPACE).append(
				// getUnNormalArg(l130s01a));
				sbKindNoA.add(getUnNormalArg(l130s01a));
			} else if ("B".equals(Util.trim(l130s01a.getBigKind()))) {
				// sbKindNoB.append(
				// (sbKindNoB.length() > 0) ? ","
				// : UtilConstants.Mark.SPACE).append(
				// getUnNormalArg(l130s01a));
				sbKindNoB.add(getUnNormalArg(l130s01a));
			} else if ("C".equals(Util.trim(l130s01a.getBigKind()))) {
				// sbKindNoC.append(
				// (sbKindNoC.length() > 0) ? ","
				// : UtilConstants.Mark.SPACE).append(
				// getUnNormalArg(l130s01a));
				sbKindNoC.add(getUnNormalArg(l130s01a));
			}
		}
		if (sbKindNoA.size() > 0) {
			map.put("A", sbKindNoA.toArray(new String[0]));
		}
		if (sbKindNoB.size() > 0) {
			map.put("B", sbKindNoB.toArray(new String[0]));
		}
		if (sbKindNoC.size() > 0) {
			map.put("C", sbKindNoC.toArray(new String[0]));
		}
		// 查詢LNFE0851更新註記並塞入List裡
		if (map != null && !map.isEmpty()) {
			List<Map<String, Object>> tempMaps = misdbBASEService
					.selLnfe0851Flag(map);
			if (!tempMaps.isEmpty()) {
				for (Map<String, Object> tempMap : tempMaps) {
					flag = Util.trim(tempMap.get("LNFE0851_FLAG"));
					break;
				}
			}
		}
		result.set("lnfe0851UpFlag", Util.trim(flag));
		return result;
	}

	/**
	 * 取得異常通報參數
	 * 
	 * @param l130s01a
	 *            異常通報明細檔
	 * @return
	 */
	private String getUnNormalArg(L130S01A l130s01a) {
		StringBuilder sbKind = new StringBuilder();
		sbKind.setLength(0);
		if (Util.isNotEmpty(Util.trim(l130s01a.getSeqNo()))) {
			sbKind.append(LMSUtil.checkSubStr(Util.trim(l130s01a.getSeqNo()), 1) ? Util
					.trim(l130s01a.getSeqNo()).substring(1) : Util
					.trim(l130s01a.getSeqNo()));
		}
		return sbKind.toString();
	}

	/**
	 * 刪除上傳檔案
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteUploadFile(PageParameters params)
			throws CapException {

		// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
		String realTime = Util.trim(params.getString("realTime"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");

		if (oids.length > 0) {
			if (Util.equals(realTime, "Y")) {
				lmsService.deleteUploadFileRealTime(oids);
			} else {
				lmsService.deleteUploadFile(oids);
			}
		}

		return result;

	}

	/**
	 * 查詢央行購置註記
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            mainId : L140M01A.mainId<Br/>
	 * @param parent
	 *            Component
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL140M01M(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("cntrNoMainId"));
		L140M01M l140m01m = lmsService
				.findModelByMainId(L140M01M.class, mainId);
		if (l140m01m != null) {
			result = DataParse
					.toResult(l140m01m, DataParse.Delete, new String[] {
							EloanConstants.MAIN_ID, EloanConstants.OID });

			this.lmsService.setMortgageDetailCodeForRealEstateBusinessRule(
					result, l140m01m.getCbcCase(),
					l140m01m.getRealEstateLoanLimitReason());

			if (l140m01m.getSit3No() != null) {
				result.set("sit3No", StringUtils.leftPad(l140m01m.getSit3No()
						.toPlainString(), 4, "0"));
			}

			if (l140m01m.getRemainLoanSite3No() != null) {
				result.set("remainLoanSite3No", StringUtils.leftPad(l140m01m
						.getRemainLoanSite3No().toPlainString(), 4, "0"));
			}

			if (l140m01m.getSite3No() != null) {
				result.set("site3No", StringUtils.leftPad(l140m01m.getSite3No()
						.toPlainString(), 4, "0"));
			}

			if (l140m01m.getBremainLoanSite3No() != null) {
				result.set("bremainLoanSite3No", StringUtils.leftPad(l140m01m
						.getBremainLoanSite3No().toPlainString(), 4, "0"));
			}
		}

		L140M01A l140m01a = this.lmsService.findModelByMainId(L140M01A.class,
				mainId);
		// J-111-0297 修改案帶入現請額度為核貸額度, 計算 擔保品總貸款成數
		result.set(
				"approvedLoanAmt",
				l140m01a == null || l140m01a.getCurrentApplyAmt() == null ? BigDecimal.ZERO
						: l140m01a.getCurrentApplyAmt());

		result.set("totalTimeVal",
				this.centralBankControlService.getCMStSumAmtAdj(mainId));

		return result;
	}

	/**
	 * 儲存央行購置註記
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            cntrNoMainId : L140M01A.mainId<Br/>
	 * @param parent
	 *            Component
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveL140M01M(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString("cntrNoMainId"));
		String l140m01mForm = Util.trim(params.getString("LMS140M01MForm"));
		L140M01M l140m01m = lmsService
				.findModelByMainId(L140M01M.class, mainId);

		L140M01A l140m01a = lmsService
				.findModelByMainId(L140M01A.class, mainId);

		if (l140m01a == null) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSCommomPage.class);
			HashMap<String, String> msg = new HashMap<String, String>();
			// other.msg202=額度明細表已不存在，請確認是否已刪除！
			msg.put("msg", Util.trim((String) prop.get("other.msg202")));
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}

		String l140m01aCntrno = "";

		// 先將l140m01mForm丟到JSON OBJ中再透過jsonGist.get("landBuildCntrno")取出值來
		JSONObject jsonGist = JSONObject.fromObject(l140m01mForm);
		String l140m01mlandBuildCntrno = Util.trim(jsonGist
				.get("landBuildCntrno"));

		List<Map<String, Object>> l140m01aCntrnos = eloandbBASEService
				.findAllCNTRNOByL140M01AMainid(mainId);
		Set<String> existCntrNo = new HashSet<String>();
		for (Map<String, Object> cntrnoData : l140m01aCntrnos) {
			String cntrno = Util.trim(cntrnoData.get("CNTRNO"));
			if (Util.isNotEmpty(cntrno)) {
				existCntrNo.add(cntrno);
			}
		}

		String isChangCntrno = ""; // 確認是否覆蓋額度序號
		String landBuildYN = Util.trim(jsonGist.get("landBuildYN"));
		if (l140m01a != null && "Y".equals(landBuildYN)) {
			l140m01aCntrno = Util.trim(l140m01a.getCntrNo());

			if ("".equals(l140m01aCntrno)) {
				// 避免選入重覆的額度序號
				if (existCntrNo.contains(l140m01mlandBuildCntrno)) {
					Properties prop = MessageBundleScriptCreator
							.getComponentResource(LMSCommomPage.class);
					HashMap<String, String> msg = new HashMap<String, String>();
					// lmsL120M01A.error008=所選取的土建融預約額度序號已存在於本次簽報書中請再次確認。
					msg.put("msg", Util.trim((String) prop
							.get("lmsL120M01A.error008")));
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
							getClass());
				}
				l140m01a.setCntrNo(l140m01mlandBuildCntrno);
			} else {
				if (!l140m01aCntrno.equals(l140m01mlandBuildCntrno)) {
					Properties prop = MessageBundleScriptCreator
							.getComponentResource(LMSCommomPage.class);
					HashMap<String, String> msg = new HashMap<String, String>();
					// lmsL120M01A.error007=本案額度序號和所選取的土建融預約額度序號不符請再次確認。

					if ("CLS".equals(Util.trim(l140m01a.getDocURL()))) {
						if ("1".equals(Util.trim(l140m01a.getDataSrc()))
								|| "2".equals(Util.trim(l140m01a.getDataSrc()))) {
							isChangCntrno = "Y";
							result.set("isChangCntrno", isChangCntrno);
							return result;
						}
					}
					msg.put("msg", Util.trim((String) prop
							.get("lmsL120M01A.error007")));
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
							getClass());
				}
			}
		}

		if (l140m01m == null) {
			l140m01m = new L140M01M();
			l140m01m.setCreateTime(CapDate.getCurrentTimestamp());
			l140m01m.setCreator(user.getUserId());
			l140m01m.setMainId(mainId);
		}
		l140m01m.setUpdateTime(CapDate.getCurrentTimestamp());
		l140m01m.setUpdater(user.getUserId());
		DataParse.toBean(l140m01mForm, l140m01m);

		JSONObject l140m01mObject = JSONObject.fromObject(l140m01mForm);
		String cbRuleVersion = Util.trim(params.getString("cbLoanVersion"));
		this.lmsService.setMortgageDetailCodeAndCleanUnusedField(
				l140m01mObject, l140m01m, cbRuleVersion);

		if (!BeanValidator.isValid(l140m01m)) {
			throw new CapMessageException(BeanValidator.getValidMsg(l140m01m,
					LMSL140M01MPanel.class), getClass());
		}
		List<String> errList = new ArrayList<String>();
		List<String> showList = new ArrayList<String>();
		String showMsg = "";

		// *** 央行109-12-08對辦理不動產抵押貸款業務規定 ******************************
		String version = Arrays.asList(RealEstateLoanUtil.newVersions)
				.contains(cbRuleVersion) ? cbRuleVersion : null;
		l140m01m.setVersion(version);
		String tmpMsg = this.lmsService.checkVersionByRealEstateNoteRule(
				l140m01m.getVersion(), l140m01a, l140m01m.getCbcCase(),
				l140m01m.getLandBuildYN(), l140m01m.getProdClass(), true,
				l140m01m.getPlusReason(), l140m01m.getRemainLoanYN());
		if (!"".equals(tmpMsg)) {
			errList.add(tmpMsg);
		}

		String tipsMsg = this.lmsService.getTipsMsgForCentralBankMortgageMark(
				l140m01m.getVersion(), l140m01m.getCbcCase(),
				l140m01m.getKeepYN(), l140m01m.getActStartDate());
		if (!"".equals(tipsMsg)) {
			showList.add(tipsMsg);
		}

		tipsMsg = this.lmsService.getTipsMsgForRecycleUse(landBuildYN,
				l140m01m.getProdClass(), l140m01a.getReUse());
		if (!"".equals(tipsMsg)) {
			showList.add(tipsMsg);
		}
		// ***************************************************************************

		lmsService.setType(l140m01m, Util.trim(l140m01a.getCustId()), errList,
				showList, l140m01a);

		String errorMsg = "";
		if (errList.size() > 0) {

			errorMsg += StringUtils.join(errList, "<br/>");

		} else if (showList.size() > 0) {
			showMsg += StringUtils.join(showList, "<br/>");
		}
		if (Util.notEquals(l140m01m.getLandBuildYN(), "Y")) {
			l140m01m.setProdClass("");
			l140m01m.setLandBuildCntrno("");
			l140m01m.setWaitMonth(null);
			l140m01m.setBuildDate(null);
			l140m01m.setLandType("");
			l140m01m.setAreaLand(null);
			l140m01m.setLocationCity("");
			l140m01m.setLocationCd("");
			l140m01m.setSite3No(null);
			l140m01m.setSite4No("");
		}

		String proPerty = Util.trim(l140m01a.getProPerty());
		// 判斷案件為不為取消或不變(全部取消 或全部不變)
		boolean isNotSameOrCancel = !(UtilConstants.Cntrdoc.Property.取消
				.equals(proPerty) || UtilConstants.Cntrdoc.Property.不變
				.equals(proPerty));

		errorMsg += this.lmsService.checkEmptyLandLoan(l140m01m, l140m01a.getCntrNo());

		// *** J-108-0097 購置高價住宅貸款檢核表 ***************************
		if ("Y".equals(l140m01m.getIsHighHouse())) {
			L140M01X l140m01x = lmsService.getL140M01XValue(mainId,
					l140m01a.getCustId(), l140m01a.getDupNo());
			lmsService.save(l140m01x);
		}

		errorMsg += lmsService.checkHighPriceCheckList(mainId);

		// *** J-109-0226 檢核 建案完工未出售房屋融資註記 ********************
		if (this.lmsService.isOpenUnsoldHouseLoanInfoFunction()
				&& "Y".equals(l140m01m.getRemainLoanYN())) {

			errorMsg += this.lmsService
					.checkFinancingDataForUnsoldHouseInFinishedConstruction(
							isNotSameOrCancel, l140m01a.getProPerty(),
							l140m01m, l140m01a.getL120m01c().getL120m01a()
									.getDocKind(), l140m01a.getCntrNo());
		}

		if (Util.isNotEmpty(errorMsg)) {
			l140m01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
			l140m01m.setChkYN(UtilConstants.DEFAULT.否);
		}

		lmsService.save(l140m01m);
		lmsService.save(l140m01a);

		if (!"".equals(errorMsg)) {
			throw new CapMessageException(errorMsg, getClass());
		}

		result.set("cntrNo", l140m01a.getCntrNo());
		result.set("prodClass", l140m01m.getProdClass());
		if (Util.isEmpty(showMsg)) {
			showMsg = RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功);
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, showMsg);

		result.set("isChangCntrno", isChangCntrno);
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult compelSaveL140M01M(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString("cntrNoMainId"));
		String l140m01mForm = Util.trim(params.getString("LMS140M01MForm"));
		L140M01M l140m01m = lmsService
				.findModelByMainId(L140M01M.class, mainId);
		L140M01A l140m01a = lmsService
				.findModelByMainId(L140M01A.class, mainId);
		// 先將l140m01mForm丟到JSON OBJ中再透過jsonGist.get("landBuildCntrno")取出值來
		JSONObject jsonGist = JSONObject.fromObject(l140m01mForm);
		String l140m01mlandBuildCntrno = Util.trim(jsonGist
				.get("landBuildCntrno"));

		List<Map<String, Object>> l140m01aCntrnos = eloandbBASEService
				.findAllCNTRNOByL140M01AMainid(mainId);
		Set<String> existCntrNo = new HashSet<String>();
		for (Map<String, Object> cntrnoData : l140m01aCntrnos) {
			String cntrno = Util.trim(cntrnoData.get("CNTRNO"));
			if (Util.isNotEmpty(cntrno)) {
				existCntrNo.add(cntrno);
			}
		}

		String landBuildYN = Util.trim(jsonGist.get("landBuildYN"));
		if (l140m01a != null && "Y".equals(landBuildYN)) {
			// 避免選入重覆的額度序號
			if (existCntrNo.contains(l140m01mlandBuildCntrno)) {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMSCommomPage.class);
				HashMap<String, String> msg = new HashMap<String, String>();
				// lmsL120M01A.error008=所選取的土建融預約額度序號已存在於本次簽報書中請再次確認。
				msg.put("msg",
						Util.trim((String) prop.get("lmsL120M01A.error008")));
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
						getClass());
			}

			l140m01a.setCntrNo(l140m01mlandBuildCntrno);
		}

		if (l140m01m == null) {
			l140m01m = new L140M01M();
			l140m01m.setCreateTime(CapDate.getCurrentTimestamp());
			l140m01m.setCreator(user.getUserId());
			l140m01m.setMainId(mainId);
		}
		l140m01m.setUpdateTime(CapDate.getCurrentTimestamp());
		l140m01m.setUpdater(user.getUserId());
		DataParse.toBean(l140m01mForm, l140m01m);
		if (!BeanValidator.isValid(l140m01m)) {
			throw new CapMessageException(BeanValidator.getValidMsg(l140m01m,
					LMSL140M01MPanel.class), getClass());
		}
		List<String> errList = new ArrayList<String>();
		List<String> showList = new ArrayList<String>();
		String showMsg = "";
		lmsService.setType(l140m01m, Util.trim(l140m01a.getCustId()), errList,
				showList, l140m01a);
		if (errList.size() > 0) {
			throw new CapMessageException(StringUtils.join(errList, "<br/>"),
					getClass());
		} else if (showList.size() > 0) {
			showMsg = StringUtils.join(showList, "<br/>");
		}

		lmsService.save(l140m01m);
		lmsService.save(l140m01a);

		result.set("cntrNo", l140m01a.getCntrNo());
		if (Util.isEmpty(showMsg)) {
			showMsg = RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功);
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, showMsg);
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL140M01Q(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("cntrNoMainId"));
		L140M01A l140m01a = lmsService
				.findModelByMainId(L140M01A.class, mainId);
		L140M01Q l140m01q = lmsService
				.findModelByMainId(L140M01Q.class, mainId);

		if (l140m01a == null) {
			// EFD2180=請先儲存後再執行此功能
			throw new CapMessageException(RespMsgHelper.getMessage("EFD2180"), getClass());
		}

		String cntrNo = Util.trim(l140m01a.getCntrNo());
		if (Util.isEmpty(cntrNo)) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSCommomPage.class);
			HashMap<String, String> msg = new HashMap<String, String>();
			msg.put("msg", Util.trim((String) prop.get("lmsL120M01A.error006")));
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		String cntrNoQ = "";
		boolean showBefore = false;
		boolean showDerv = false;

		if (l140m01q != null) {
			cntrNoQ = l140m01q.getCntrNoQ();
			result = DataParse
					.toResult(l140m01q, DataParse.Delete, new String[] {
							EloanConstants.MAIN_ID, EloanConstants.OID });

			String bcnLoanFg = CapString.trimNull(l140m01q.getBcnLoanFg());
			if (!"".equals(bcnLoanFg)) {
				showBefore = true;
			}

			String sTradeFg = l140m01q.getsTradeFg();
			if ("N".equals(sTradeFg)) {
				BigDecimal guar1Rate = l140m01q.getGuar1Rate();
				BigDecimal guar2Rate = l140m01q.getGuar2Rate();
				BigDecimal guar3Rate = l140m01q.getGuar3Rate();
				BigDecimal coll1Rate = l140m01q.getColl1Rate();
				BigDecimal coll2Rate = l140m01q.getColl2Rate();
				BigDecimal coll3Rate = l140m01q.getColl3Rate();
				BigDecimal coll4Rate = l140m01q.getColl4Rate();
				BigDecimal coll5Rate = l140m01q.getColl5Rate();

				BigDecimal total = guar1Rate.add(guar2Rate).add(guar3Rate)
						.add(coll1Rate).add(coll2Rate).add(coll3Rate)
						.add(coll4Rate).add(coll5Rate);
				if (total.intValue() == 100) {
					result.set("rickTrFg", "Y");
				} else {
					result.set("rickTrFg", "N");
				}
			}

			String bsTradeFg = l140m01q.getBsTradeFg();
			if ("N".equals(bsTradeFg)) {
				BigDecimal bguar1Rate = l140m01q.getBguar1Rate();
				BigDecimal bguar2Rate = l140m01q.getBguar2Rate();
				BigDecimal bguar3Rate = l140m01q.getBguar3Rate();
				BigDecimal bcoll1Rate = l140m01q.getBcoll1Rate();
				BigDecimal bcoll2Rate = l140m01q.getBcoll2Rate();
				BigDecimal bcoll3Rate = l140m01q.getBcoll3Rate();
				BigDecimal bcoll4Rate = l140m01q.getBcoll4Rate();
				BigDecimal bcoll5Rate = l140m01q.getBcoll5Rate();

				BigDecimal btotal = bguar1Rate.add(bguar2Rate).add(bguar3Rate)
						.add(bcoll1Rate).add(bcoll2Rate).add(bcoll3Rate)
						.add(bcoll4Rate).add(bcoll5Rate);
				if (btotal.intValue() == 100) {
					result.set("brickTrFg", "Y");
				} else {
					result.set("brickTrFg", "N");
				}
			}

			// J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			// 將db存的值轉為jsonArray
			if (Util.isNotEmpty(l140m01q.getOthCrdType())) {
				String othCrdType = l140m01q.getOthCrdType();
				String[] othCrdTypeArray = othCrdType.split("\\|");
				JSONArray value = new JSONArray();
				for (String txt : othCrdTypeArray) {
					if (Util.isNotEmpty(Util.trim(txt))) {
						value.add(Util.trim(txt));
					}
				}
				result.set("othCrdType", value.toString());
			}

			if (Util.isNotEmpty(l140m01q.getBothCrdType())) {
				String bothCrdType = l140m01q.getBothCrdType();
				String[] bothCrdTypeArray = bothCrdType.split("\\|");
				JSONArray value = new JSONArray();
				for (String txt : bothCrdTypeArray) {
					if (Util.isNotEmpty(Util.trim(txt))) {
						value.add(Util.trim(txt));
					}
				}
				result.set("bothCrdType", value.toString());
			}
			// J-104-00279-001 END
		}

		// 無資料時，要抓前案資料供使用者比對
		// || (l140m01q != null && Util.equals(l140m01q.getBcnLoanFg(), ""))
		if (l140m01q == null) {
			Map<String, Object> elf506 = lmsService
					.getELF506ByBranchAndCntrno(cntrNo);
			// Map<String, Object> elf506 =
			// misELF506Service.getByCntrNo(cntrNo);
			if (elf506 != null && !elf506.isEmpty()) {

				result = lmsService.setResultFromELF506(elf506, cntrNo, "b",
						result);

				// String elf506CntrNo = MapUtils.getString(elf506,
				// "ELF506_CNTRNO", "");
				// String bcnLoanFg = MapUtils.getString(elf506,
				// "ELF506_CN_LOAN_FG", "");
				// String bigolFlag = MapUtils.getString(elf506,
				// "ELF506_IGOL_FLAG", "");
				// String bdirectFg = MapUtils.getString(elf506,
				// "ELF506_DIRECT_FG", "");
				// // J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
				// String bcnBusKind = MapUtils.getString(elf506,
				// "ELF506_CN_BUS_KIND", "");
				// String bsTradeFg = MapUtils.getString(elf506,
				// "ELF506_S_TRADE_FG", "");
				//
				// String bcnTMUFg = MapUtils.getString(elf506,
				// "ELF506_CN_TMU_FG", "");
				//
				// BigDecimal bguar1Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_GUAR1_RATE", BigDecimal.ZERO);
				// BigDecimal bguar2Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_GUAR2_RATE", BigDecimal.ZERO);
				// BigDecimal bguar3Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_GUAR3_RATE", BigDecimal.ZERO);
				// BigDecimal bcoll1Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_COLL1_RATE", BigDecimal.ZERO);
				// BigDecimal bcoll2Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_COLL2_RATE", BigDecimal.ZERO);
				// BigDecimal bcoll3Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_COLL3_RATE", BigDecimal.ZERO);
				// BigDecimal bcoll4Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_COLL4_RATE", BigDecimal.ZERO);
				// BigDecimal bcoll5Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_COLL5_RATE", BigDecimal.ZERO);
				//
				// // BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
				//
				// // BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
				// if (Util.equals(bcnLoanFg, "N")) {
				// bigolFlag = "";
				// }
				//
				// String bloanTarget = MapUtils.getString(elf506,
				// "ELF506_LOAN_TARGET", "");
				// String bisType = MapUtils.getString(elf506, "ELF506_IS_TYPE",
				// "");
				// String bgrntType = MapUtils.getString(elf506,
				// "ELF506_GRNT_TYPE", "");
				// String bgrntClass = MapUtils.getString(elf506,
				// "ELF506_GRNT_CLASS", "");
				// String bothCrdType = MapUtils.getString(elf506,
				// "ELF506_OTHCRD_TYPE", "");
				// String bunionArea3 = MapUtils.getString(elf506,
				// "ELF506_UNION_AREA3", "");
				// // END J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
				//
				// // J-105-0074-001 Web e-Loan
				// // 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
				// String bnCnSblcFg = MapUtils.getString(elf506,
				// "ELF506_NCN_SBLC_FG", "");
				//
				// // elf506.get("ELF506_MODIFYTIME");
				// // elf506.get("ELF506_CREATETIME");
				// // elf506.get("ELF506_CREATEUNIT");
				// // elf506.get("ELF506_MODIFYUNIT");
				// // elf506.get("ELF506_DOCUMENT_NO");
				// result.set("bcnLoanFg", bcnLoanFg);
				// result.set("biGolFlag", bigolFlag);
				// result.set("bdirectFg", bdirectFg);
				// // J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
				// result.set("bcnBusKind", bcnBusKind);
				// result.set("bsTradeFg", bsTradeFg);
				// result.set("bguar1Rate", bguar1Rate);
				// result.set("bguar2Rate", bguar2Rate);
				// result.set("bguar3Rate", bguar3Rate);
				// result.set("bcoll1Rate", bcoll1Rate);
				// result.set("bcoll2Rate", bcoll2Rate);
				// result.set("bcoll3Rate", bcoll3Rate);
				// result.set("bcoll4Rate", bcoll4Rate);
				// result.set("bcoll5Rate", bcoll5Rate);
				//
				// result.set("bcnTMUFg", bcnTMUFg);
				//
				// // BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
				// result.set("bloanTarget", bloanTarget);
				// result.set("bisType", bisType);
				// result.set("bgrntType", bgrntType);
				// result.set("bgrntClass", bgrntClass);
				//
				// // result.set("bothCrdType", bothCrdType);
				// // ELF506_OTHCRD_TYPE="YYY                 "
				// bothCrdType = lmsService
				// .formatOthCrdTypeFromElf506(bothCrdType);
				// if (Util.isNotEmpty(bothCrdType)) {
				// String[] bothCrdTypeArray = bothCrdType.split("\\|");
				// JSONArray value = new JSONArray();
				// for (String txt : bothCrdTypeArray) {
				// if (Util.isNotEmpty(Util.trim(txt))) {
				// value.add(Util.trim(txt));
				// }
				// }
				// result.set("bothCrdType", value.toString());
				// }
				//
				// result.set("bunionArea3", bunionArea3);
				// // END J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
				//
				// BigDecimal bTotal =
				// bguar1Rate.add(bguar2Rate).add(bguar3Rate)
				// .add(bcoll1Rate).add(bcoll2Rate).add(bcoll3Rate)
				// .add(bcoll4Rate).add(bcoll5Rate);
				// if (bTotal.intValue() == 100) {
				// result.set("brickTrFg", "Y");
				// } else {
				// result.set("brickTrFg", "N");
				// }
				//
				// // J-105-0074-001 Web e-Loan
				// // 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
				// result.set("bnCnSblcFg", bnCnSblcFg);
				//
				//
				// cntrNoQ = elf506CntrNo;

				// J-112-0462 「對大陸地區授信業務控管註記」新增三提問，以額度序號取得已核准之最新一筆註記
				if(true){
					CapAjaxFormResult oldData = findOldL140M01QItems(cntrNo);
					if(Util.isNotEmpty(oldData)){
						result.add(oldData);
					}
				}
				showBefore = true;

			}
		}

		if (Util.isEmpty(cntrNoQ)) {
			cntrNoQ = l140m01a.getCntrNo();
		}
		result.set("cntrNoQ", cntrNoQ);
		result.set("showBefore", showBefore);

		Boolean hasDerivateSubjectFlag = false;
		ArrayList<String> itemsAll = new ArrayList<String>();
		Set<L140M01C> l140m01cs = l140m01a.getL140m01c();

		for (L140M01C l140m01c : l140m01cs) {
			itemsAll.add(l140m01c.getLoanTP());
		}
		// 判斷衍生性科目是否要產生風險係數
		hasDerivateSubjectFlag = lmsService.hasDerivateSubject(itemsAll
				.toArray(new String[itemsAll.size()]));

		if (hasDerivateSubjectFlag == true) {
			showDerv = true;
		}

		result.set("showDerv", showDerv);
		// J-112-0462 「對大陸地區授信業務控管註記」新增三提問，用來判斷消金案件
		result.set("m01aDocUrl", Util.trim(l140m01a.getDocURL()));

		// J-105-0074-001 Web e-Loan
		// 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
		boolean isCnBrno = false;
		isCnBrno = lmsService.hasTargetCountryCntrno(l140m01a, "CN");
		if (isCnBrno) {
			result.set("showForCNCntrno", true);
		} else {
			result.set("showForCNCntrno", false);
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult requeryHistoryToL140M01Q(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("cntrNoMainId"));
		L140M01A l140m01a = lmsService
				.findModelByMainId(L140M01A.class, mainId);

		String l140m01qForm = Util.trim(params.getString("LMS140M01QForm"));
		JSONObject js = JSONObject.fromObject(l140m01qForm);

		result.set("cnLoanFg", js.optString("cnLoanFg"));
		result.set("iGolFlag", js.optString("iGolFlag"));
		result.set("directFg", js.optString("directFg"));
		// J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
		result.set("cnBusKind", js.optString("cnBusKind"));
		result.set("sTradeFg", js.optString("sTradeFg"));
		result.set("rickTrFg", js.optString("rickTrFg"));
		result.set("guar1Rate", js.optString("guar1Rate"));
		result.set("guar2Rate", js.optString("guar2Rate"));
		result.set("guar3Rate", js.optString("guar3Rate"));

		result.set("coll1Rate", js.optString("coll1Rate"));
		result.set("coll2Rate", js.optString("coll2Rate"));
		result.set("coll3Rate", js.optString("coll3Rate"));
		result.set("coll4Rate", js.optString("coll4Rate"));
		result.set("coll5Rate", js.optString("coll5Rate"));

		// J-103-0076 衍生性金融商品立約人身份別
		result.set("cnTMUFg", js.optString("cnTMUFg"));

		// BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
		result.set("loanTarget", js.optString("loanTarget"));
		result.set("isType", js.optString("isType"));
		result.set("grntType", js.optString("grntType"));
		result.set("grntClass", js.optString("grntClass"));
		result.set("othCrdType", js.optString("othCrdType"));
		result.set("unionArea3", js.optString("unionArea3"));
		// END J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別

		// J-105-0074-001 Web e-Loan
		// 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
		result.set("nCnSblcFg", js.optString("nCnSblcFg"));
		
		// J-112-0462 「對大陸地區授信業務控管註記」新增三提問
		// 本案資金流向是否為中國
		result.set("fundsToCn", js.optString("fundsToCn"));
		// 借款人之股權結構往上追溯至中國籍股東止，其合計中國籍股東持股達50%者，請選是
		result.set("cnShareholder", js.optString("cnShareholder"));
		// 本案任一保證人國籍/註冊地是否為中國
		result.set("cnGuarantor", js.optString("cnGuarantor"));

		String cntrNo = Util.trim(l140m01a.getCntrNo());
		if (Util.isEmpty(cntrNo)) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSCommomPage.class);
			HashMap<String, String> msg = new HashMap<String, String>();
			msg.put("msg", Util.trim((String) prop.get("lmsL120M01A.error006")));
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		boolean showBefore = false;
		boolean showDerv = false;
		Map<String, Object> elf506 = lmsService
				.getELF506ByBranchAndCntrno(cntrNo);
		// misELF506Service.getByCntrNo(cntrNo);
		if (elf506 != null && !elf506.isEmpty()) {

			result = lmsService
					.setResultFromELF506(elf506, cntrNo, "b", result);

			// String bcnLoanFg = MapUtils.getString(elf506,
			// "ELF506_CN_LOAN_FG",
			// "");
			// String bigolFlag = MapUtils.getString(elf506, "ELF506_IGOL_FLAG",
			// "");
			// String bdirectFg = MapUtils.getString(elf506, "ELF506_DIRECT_FG",
			// "");
			// // J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
			// String bcnBusKind = MapUtils.getString(elf506,
			// "ELF506_CN_BUS_KIND", "");
			// String bsTradeFg = MapUtils.getString(elf506,
			// "ELF506_S_TRADE_FG",
			// "");
			//
			// String bcnTMUFg = MapUtils
			// .getString(elf506, "ELF506_CN_TMU_FG", "");
			//
			// BigDecimal bguar1Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_GUAR1_RATE", BigDecimal.ZERO);
			// BigDecimal bguar2Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_GUAR2_RATE", BigDecimal.ZERO);
			// BigDecimal bguar3Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_GUAR3_RATE", BigDecimal.ZERO);
			// BigDecimal bcoll1Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_COLL1_RATE", BigDecimal.ZERO);
			// BigDecimal bcoll2Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_COLL2_RATE", BigDecimal.ZERO);
			// BigDecimal bcoll3Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_COLL3_RATE", BigDecimal.ZERO);
			// BigDecimal bcoll4Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_COLL4_RATE", BigDecimal.ZERO);
			// BigDecimal bcoll5Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_COLL5_RATE", BigDecimal.ZERO);
			//
			// // BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			// if (Util.equals(bcnLoanFg, "N")) {
			// bigolFlag = "";
			// }
			//
			// String bloanTarget = MapUtils.getString(elf506,
			// "ELF506_LOAN_TARGET", "");
			// String bisType = MapUtils.getString(elf506, "ELF506_IS_TYPE",
			// "");
			// String bgrntType = MapUtils.getString(elf506, "ELF506_GRNT_TYPE",
			// "");
			// String bgrntClass = MapUtils.getString(elf506,
			// "ELF506_GRNT_CLASS",
			// "");
			// String bothCrdType = MapUtils.getString(elf506,
			// "ELF506_OTHCRD_TYPE", "");
			// String bunionArea3 = MapUtils.getString(elf506,
			// "ELF506_UNION_AREA3", "");
			// // END J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			//
			// // J-105-0074-001 Web e-Loan
			// // 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
			// String bnCnSblcFg = MapUtils.getString(elf506,
			// "ELF506_NCN_SBLC_FG", "");
			//
			// result.set("bcnLoanFg", bcnLoanFg);
			// result.set("biGolFlag", bigolFlag);
			// result.set("bdirectFg", bdirectFg);
			// // J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
			// result.set("bcnBusKind", bcnBusKind);
			// result.set("bsTradeFg", bsTradeFg);
			// result.set("bguar1Rate", bguar1Rate);
			// result.set("bguar2Rate", bguar2Rate);
			// result.set("bguar3Rate", bguar3Rate);
			// result.set("bcoll1Rate", bcoll1Rate);
			// result.set("bcoll2Rate", bcoll2Rate);
			// result.set("bcoll3Rate", bcoll3Rate);
			// result.set("bcoll4Rate", bcoll4Rate);
			// result.set("bcoll5Rate", bcoll5Rate);
			// result.set("bcnTMUFg", bcnTMUFg);
			//
			// // BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			// result.set("bloanTarget", bloanTarget);
			// result.set("bisType", bisType);
			// result.set("bgrntType", bgrntType);
			// result.set("bgrntClass", bgrntClass);
			//
			// // result.set("bothCrdType", bothCrdType);
			// // ELF506_OTHCRD_TYPE="YYY                 "
			// bothCrdType = lmsService.formatOthCrdTypeFromElf506(bothCrdType);
			// if (Util.isNotEmpty(bothCrdType)) {
			// String[] bothCrdTypeArray = bothCrdType.split("\\|");
			// JSONArray value = new JSONArray();
			// for (String txt : bothCrdTypeArray) {
			// if (Util.isNotEmpty(Util.trim(txt))) {
			// value.add(Util.trim(txt));
			// }
			// }
			// result.set("bothCrdType", value.toString());
			// }
			//
			// result.set("bunionArea3", bunionArea3);
			// // END J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			//
			// // J-105-0074-001 Web e-Loan
			// // 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
			// result.set("bnCnSblcFg", bnCnSblcFg);
			//
			// BigDecimal bTotal = bguar1Rate.add(bguar2Rate).add(bguar3Rate)
			// .add(bcoll1Rate).add(bcoll2Rate).add(bcoll3Rate)
			// .add(bcoll4Rate).add(bcoll5Rate);
			// if (bTotal.intValue() == 100) {
			// result.set("brickTrFg", "Y");
			// } else {
			// result.set("brickTrFg", "N");
			// }

			showBefore = true;

		}
		result.set("cntrNoQ", cntrNo);
		result.set("showBefore", showBefore);

		Boolean hasDerivateSubjectFlag = false;
		ArrayList<String> itemsAll = new ArrayList<String>();
		Set<L140M01C> l140m01cs = l140m01a.getL140m01c();

		for (L140M01C l140m01c : l140m01cs) {
			itemsAll.add(l140m01c.getLoanTP());
		}
		// 判斷衍生性科目是否要產生風險係數
		hasDerivateSubjectFlag = lmsService.hasDerivateSubject(itemsAll
				.toArray(new String[itemsAll.size()]));

		if (hasDerivateSubjectFlag == true) {
			showDerv = true;
		}
		result.set("showDerv", showDerv);

		// J-105-0074-001 Web e-Loan
		// 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
		boolean isCnBrno = false;
		isCnBrno = lmsService.hasTargetCountryCntrno(l140m01a, "CN");
		if (isCnBrno) {
			result.set("showForCNCntrno", true);
		} else {
			result.set("showForCNCntrno", false);
		}
		// J-112-0462 「對大陸地區授信業務控管註記」新增三提問，以額度序號取得已核准之最新一筆註記
		if(true){
			CapAjaxFormResult oldData = findOldL140M01QItems(cntrNo);
			if(Util.isNotEmpty(oldData)){
				result.add(oldData);
			}
		}

		return result;
	}
	
	// J-112-0462 「對大陸地區授信業務控管註記」新增三提問，以額度序號取得已核准之最新一筆註記
	private CapAjaxFormResult findOldL140M01QItems(String cntrNo){
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		List<Map<String, Object>> oldL140m01qs = eloandbBASEService.findLastByCntrNo(cntrNo);
		if(oldL140m01qs != null && oldL140m01qs.size() > 0 ){
			Map<String, Object> lastL140m01q = oldL140m01qs.get(0);
			//本案資金流向是否為中國
			result.set("bfundsToCn", Util.trim(lastL140m01q.get("FUNDSTOCN")));
			//借款人之股權結構往上追溯至中國籍股東止，其合計中國籍股東持股達50%者
			result.set("bcnShareholder", Util.trim(lastL140m01q.get("CNSHAREHOLDER")));
			//本案任一保證人國籍/註冊地是否為中國
			result.set("bcnGuarantor", Util.trim(lastL140m01q.get("CNGUARANTOR")));
		}
		
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveL140M01Q(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString("cntrNoMainId"));
		String l140m01qForm = Util.trim(params.getString("LMS140M01QForm"));
		L140M01Q l140m01q = lmsService
				.findModelByMainId(L140M01Q.class, mainId);
		if (l140m01q == null) {
			l140m01q = new L140M01Q();
			l140m01q.setCreateTime(CapDate.getCurrentTimestamp());
			l140m01q.setCreator(user.getUserId());
			l140m01q.setMainId(mainId);
		}
		l140m01q.setUpdateTime(CapDate.getCurrentTimestamp());
		l140m01q.setUpdater(user.getUserId());
		DataParse.toBean(l140m01qForm, l140m01q);
		if (!BeanValidator.isValid(l140m01q)) {
			throw new CapMessageException(BeanValidator.getValidMsg(l140m01q,
					LMSL140M01MPanel.class), getClass());
		}

		Boolean hasDerivateSubjectFlag = false;
		ArrayList<String> itemsAll = new ArrayList<String>();
		L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
		Set<L140M01C> l140m01cs = l140m01a.getL140m01c();

		for (L140M01C l140m01c : l140m01cs) {
			itemsAll.add(l140m01c.getLoanTP());
		}
		// 判斷是否衍生性科目
		hasDerivateSubjectFlag = lmsService.hasDerivateSubject(itemsAll
				.toArray(new String[itemsAll.size()]));

		if (hasDerivateSubjectFlag != true) {
			l140m01q.setCnTMUFg("-");
		}

		/*
		 * 原提案人授管處-蔡瑜瑾說新的授信對象暫時不納入這個檢核 104.11.04 StringBuffer warnMsg = new
		 * StringBuffer(""); if (l140m01a != null) { if
		 * (Util.equals(l140m01q.getCnLoanFg(), "Y")) { if
		 * (LMSUtil.isObuId(Util.trim(l140m01a.getCustId()))) {
		 * if(Util.equals(Util.getLeftStr(l140m01a.getCustId(), 3),"CNZ")){ if
		 * (Util.notEquals(l140m01q.getDirectFg(), "11") &&
		 * Util.notEquals(l140m01q.getDirectFg(), "12") &&
		 * Util.notEquals(l140m01q.getDirectFg(), "13")) { //
		 * other.msg204=檢核有誤，請務必確認
		 * !!<BR>大陸授信案件之註記如借款人之統編前3碼為「CNZ」者，限選授信對象別為以下三者之一
		 * :<BR>11:直接授信-大陸地區台商(經主管機關許可投資
		 * )。<BR>12:直接授信-大陸地區法人(陸資企業)、機關、團體。<BR>13:直接授信
		 * -大陸地區外商(第三地區法人在大陸地區之分支機構)。<BR>
		 * warnMsg.append(pop.get("other.msg204"));
		 * 
		 * } }
		 * 
		 * } }
		 * 
		 * }
		 */

		StringBuffer warnMsg = new StringBuffer("");
		if (l140m01a != null) {
			if (Util.equals(l140m01q.getCnLoanFg(), "Y")) {

				String ntCode = "";
				List<?> rows1 = misCustdataService.findCustdataForList(
						l140m01a.getCustId(), l140m01a.getDupNo());

				Iterator<?> it1 = rows1.iterator();
				if (it1.hasNext()) {
					Map<?, ?> dataMap1 = (Map<?, ?>) it1.next();
					// 國別註冊地
					if (!Util.isEmpty(Util.trim(Util.nullToSpace(dataMap1
							.get("NATIONCODE"))))) {
						ntCode = Util.trim(Util.nullToSpace(dataMap1
								.get("NATIONCODE")));
					}
				}

				if (Util.equals(ntCode, "CN")) {
					if (Util.notEquals(l140m01q.getLoanTarget(), "11210")
							&& Util.notEquals(l140m01q.getLoanTarget(), "11220")
							&& Util.notEquals(l140m01q.getLoanTarget(), "11230")
							&& Util.notEquals(l140m01q.getLoanTarget(), "11240")
							&& Util.notEquals(l140m01q.getLoanTarget(), "11241")
							&& Util.notEquals(l140m01q.getLoanTarget(), "11242")
							&& Util.notEquals(l140m01q.getLoanTarget(), "11243")
							&& Util.notEquals(l140m01q.getLoanTarget(), "11250")) {
						// other.msg206=公司註冊地國別為CN，授信對象別限下列代碼
						// 11210、11220、11230、11240、11241、11250
						warnMsg.append(pop.get("other.msg206"));

					}
				}

			}

		}

		// BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
		// 儲存時清除舊案與預設值
		// 1.清除做廢欄位
		l140m01q.setDirectFg("");
		l140m01q.setBdirectFg("");
		l140m01q.setCnBusKind("");
		l140m01q.setBcnBusKind("");

		// 2.清除選單殘值欄位
		if (Util.notEquals(Util.trim(l140m01q.getiGolFlag()), "Y")) {
			l140m01q.setIsType("");
		}

		if (Util.equals(Util.trim(l140m01q.getLoanTarget()), "")) {
			l140m01q.setGrntType("");
		} else {
			if (Util.notEquals(
					Util.trim(l140m01q.getLoanTarget()).subSequence(0, 2), "12")) {
				l140m01q.setGrntType("");
			}
		}

		if (Util.notEquals(Util.trim(l140m01q.getGrntClass()), "2")) {
			l140m01q.setOthCrdType("");
		}
		// END J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別

		// J-105-0074-001 Web e-Loan
		// 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
		if (!lmsService.hasTargetCountryCntrno(l140m01a, "CN")) {
			l140m01q.setNCnSblcFg("");
		}

		lmsService.save(l140m01q);
		result.set(
				CapConstants.AJAX_NOTIFY_MESSAGE,
				Util.equals(warnMsg.toString(), "") ? "" : warnMsg.toString()
						+ "<BR><BR>"
								+ RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult moveUnit(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String fromCntr = null;
		String fromAllCust = null;
		String fromBrno = null;
		String toBrno = null;
		String toCntr = null;
		String custId = null;
		String dupNo = null;
		// 開始查詢移轉分行資料
		List<Map<String, Object>> listMap = misLnf076Service.selMoveUnit();
		List<L140M01A> listL140toSet = new ArrayList<L140M01A>();
		List<L120A01A> listAuthtoSet = new ArrayList<L120A01A>();
		List<L120M01A> listMetatoSet = new ArrayList<L120M01A>();
		int allCount = listMap.size();
		int sucCount = 0;
		int failCount = 0;
		if (listMap != null && !listMap.isEmpty()) {
			for (Map<String, Object> map : listMap) {
				try {
					fromBrno = Util.trim(map.get("LNF076_LN_BR_NO"));
					fromAllCust = Util.trim(map.get("LNF076_CUST_ID"));
					fromCntr = Util.trim(map.get("LNF076_CONTRACT"));
					toBrno = Util.trim(map.get("LNF076_C_ABRNO"));
					toCntr = Util.trim(map.get("LNF076_C_CONTRACT"));
					custId = LMSUtil.checkSubStr(fromAllCust, 0,
							fromAllCust.length() - 1) ? fromAllCust.substring(
							0, fromAllCust.length() - 1)
							: UtilConstants.Mark.SPACE;
					dupNo = LMSUtil.checkSubStr(fromAllCust,
							fromAllCust.length() - 1) ? fromAllCust
							.substring(fromAllCust.length() - 1)
							: UtilConstants.Mark.SPACE;
					List<L140M01A> listL140 = l140m01aDao
							.findL140m01aListBycntrNo(fromCntr, custId, dupNo);
					if (listL140 != null && !listL140.isEmpty()) {
						for (L140M01A model : listL140) {
							if (fromBrno.equals(Util.trim(model.getOwnBrId()))) {
								L120M01C relModel = l120m01cDao
										.findoneByRefMainId(Util.trim(model
												.getMainId()));
								if (relModel != null) {
									L120M01A meta = l120m01aDao
											.findByMainId(Util.trim(relModel
													.getMainId()));
									if (meta != null) {
										Set<L120A01A> listAuth = meta
												.getL120a01a();
										if (listAuth != null
												&& !listAuth.isEmpty()) {
											for (L120A01A l120a01a : listAuth) {
												if (Util.trim(
														l120a01a.getAuthUnit())
														.equals(Util.trim(l120a01a
																.getOwnUnit()))) {
													l120a01a.setAuthUnit(toBrno);
												}
												l120a01a.setOwnUnit(toBrno);
												listAuthtoSet.add(l120a01a);
											}
										}
										meta.setUpdater(user.getUserId());
										meta.setUpdateTime(CapDate
												.getCurrentTimestamp());
										meta.setOwnBrId(toBrno);
										meta.setCaseBrId(toBrno);
										listMetatoSet.add(meta);
									}
								}
								model.setUpdater(user.getUserId());
								model.setUpdateTime(CapDate
										.getCurrentTimestamp());
								model.setCntrNo(toCntr);
								model.setOwnBrId(toBrno);
								listL140toSet.add(model);
							}
						}
					}
					sucCount++;
				} catch (Exception e) {
					StringBuilder errMsg = new StringBuilder();
					errMsg.setLength(0);
					errMsg.append("移轉資料失敗！失敗資料明細：").append("分行代號：")
							.append(fromBrno).append(" ").append("統一編號：")
							.append(fromAllCust).append(" ").append("額度序號：")
							.append(fromCntr).append("<br/>")
							.append(e.getMessage());
					loggerCom.error(errMsg.toString());
					failCount++;
				}
			}
		}
		// 開始儲存移轉分行資料
		lmsService.saveMoveUnit(listL140toSet, listAuthtoSet, listMetatoSet);
		// other.msg132=執行成功！總共執行{0}筆資料，成功{1}筆，失敗{2}筆，詳細失敗情形請參閱LOG系統訊息。
		String msg = MessageFormat.format(pop.getProperty("other.msg132"),
				new Object[] { allCount, sucCount, failCount });
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, msg);
		return result;
	}

	/**
	 * 查詢黑名單
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public IResult queryBlackName(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String cust = Util.trim(params.getString("cust"));
		String cId = Util.trim(params.getString("custId"));
		String dNo = Util.trim(params.getString("dupNo", "0"));

		JSONObject custJson = Util.isEmpty(cust) ? new JSONObject()
				: JSONObject.fromObject(cust);
		if (Util.isNotEmpty(cId)) {
			JSONObject json = new JSONObject();
			json.put("custId", cId);
			json.put("dupNo", dNo);
			custJson.put(cId + dNo, json);
		}

		Set<String> set = (Set<String>) custJson.keySet();
		for (String key : set) {
			StringBuffer message = new StringBuffer();

			JSONObject json = custJson.getJSONObject(key);
			String custId = Util.trim(json.get("custId"));
			String dupNo = Util.trim(json.get("dupNo"));
			String eName = Util.trim(json.get("eName"));
			String cName = Util.trim(json.get("custName"));
			boolean ci0024 = true;

			Map<String, Object> custData = mcs.findAllByByCustIdAndDupNo(
					custId, dupNo);
			if (custData == null || custData.isEmpty()) {
				ci0024 = false;
				eName = "false";
				message.append(custId).append(" ").append(dupNo).append(" ");
				message.append("客戶中文檔0024 無此借款人資料 ！！");
			} else {
				if (Util.isEmpty(cName))
					cName = Util.trim(custData.get("CNAME"));
				if (Util.isEmpty(eName))
					eName = Util.trim(custData.get("ENAME"));

				// TODO 0024客戶檔中沒有英文名字時改先去捉LMS.C101M01A值
				if (Util.isEmpty(eName)) {
					List<C101S01E> c101s01elist = c101s01eDao
							.findByCustIdDupId(custId, dupNo);
					for (C101S01E c101s01e : c101s01elist) {
						if (c101s01e != null) {
							eName = Util.trim(c101s01e.getEName());
						}
					}
				}

				if (Util.isNotEmpty(eName)) {
					// *******************************************************************
					// BGN J-106-0029-003 Web
					// e-Loan授信簽報書借款人基本資料與動審表黑名單查詢調整使用共用模組
					// Map<String, Object> map = msps.callLNSP0130(
					// user.getUnitNo(), eName);

					Map<String, Object> map = new HashMap<String, Object>();
					map.put("SP_RETURN", "");
					map.put("SP_OUTPUT_AREA", "          ");
					map.put("SP_ERROR_MSG", "");

					List<String> blackList = customerService.findBlackList(
							user.getUnitNo(), eName, "");

					String blackListCode = blackList
							.get(ICustomerService.BlackList_ReturnCode);
					String memo = blackList
							.get(ICustomerService.BlackList_OFACName);

					if (Util.equals(blackListCode,
							UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)
							|| Util.equals(
									blackListCode,
									UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)
							|| Util.equals(
									blackListCode,
									UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)) {
						map.put("SP_RETURN", "YES");
						map.put("SP_OUTPUT_AREA", "?" + blackListCode + memo);
						map.put("SP_ERROR_MSG", "");
					} else {
						// ???
						map.put("SP_RETURN", "NO");
						map.put("SP_OUTPUT_AREA", "          ");
						map.put("SP_ERROR_MSG", "No this return code:"
								+ blackListCode);
					}
					// END J-106-0029-003 Web
					// e-Loan授信簽報書借款人基本資料與動審表黑名單查詢調整使用共用模組
					// *******************************************************************

					if (map != null) {
						if ("YES".equals(Util.trim(map.get("SP_RETURN")))) {
							String outResult = Util.trim(map
									.get("SP_OUTPUT_AREA"));
							if (LMSUtil.checkSubStr(outResult, 1, 3)) {
								// 02:找到黑名單, 04:可能是黑名單
								int code = Util.parseInt(outResult.substring(1,
										3));
								message.append(message.length() > 0 ? "\r" : "");
								message.append(custId).append(" ")
										.append(dupNo).append(" ")
										.append(eName);
								// if (code == 2) // 找到黑名單
								// message.append("OSAMA BIN LADEN");
								if (Util.isNotEmpty(cName)) {
									message.append("【").append(cName)
											.append("】 ");
								}

								message.append(" ");

								if (code == 2) {
									message.append("存在於黑名單資料");
								} else if (code == 4) {
									/*
									 * 2017-02-08 經向四組確認 未來可能隨時增加 "不同組織/單位"
									 * 的黑名單比對 為免每增加一個名單, 前端系統就要再改一次程式
									 * 
									 * 目前黑名單比對,只會傳回 02(正中),04(疑似) *
									 */
									message.append("請至 0015-11 系統提示疑似黑名單查詢");
								} else {
									message.append("未列於黑名單");
								}
							}
						} else {
							HashMap<String, String> msg = new HashMap<String, String>();
							msg.put("msg", Util.trim(map.get("SP_ERROR_MSG")));
							// 錯誤訊息
							throw new CapMessageException(
									RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
						}
					}
				} else {
					message.append(custId).append(" ").append(dupNo);
					message.append(" 查無英文名");
				}
			}

			json.put("eName", eName);
			json.put("message", message.toString());
			json.put("ci0024", ci0024);
		}

		result.set("cust", new CapAjaxFormResult(custJson));

		return result;
	}

	/**
	 * 查詢黑名單 手動輸入
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public IResult queryEname(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		StringBuffer message = new StringBuffer();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String name = Util.trim(params.getString("englishName"));

		try {
			OBSMqGwReqMessage obsmqReq = new OBSMqGwReqMessage("004", "");
			obsmqReq.setDefaultRequestHeader(user.getUnitNo());
			obsmqReq.addRequest("ENG-NAME", name);
			JSONObject json = obsMqGwClient.send(obsmqReq);
			logger.info("004==>json" + json.toString());
			if (json != null) {
				message.append(name).append(" ");
				JSONArray record = json.getJSONArray("RECORD");
				if (record.isEmpty()) {
					// L160M01A.message39=未列於黑名單
					message.append("未列於黑名單");
				} else {
					// L160M01A.message41=可能是黑名單
					message.append("可能是黑名單");
				}
			}
		} catch (GWException t1) {
			logger.error("LMSCOMMONFormHandler.queryEname", t1);
			throw t1;
		} catch (Exception e) {
			logger.error("queryEname LMSCOMMONFormHandler EXCEPTION!!", e);
			HashMap<String, String> param = new HashMap<String, String>();
			param.put("dsName", "MQ");
			// EFD0010=ERROR|系統連接資料庫$\{dsName\}不成功，請洽資訊處|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0010", param), getClass());
		}

		result.set("message", message.toString());
		result.set("queryDate",
				CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD));

		return result;

	}

	/**
	 * 查詢縣市區域代碼選單
	 * 
	 * @param params
	 *            <pre>
	 *            queryType 查詢的代號
	 *             1 :查縣市
	 *             2 :查區域
	 * </pre>
	 * @param
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryCityCode(PageParameters params)
			throws CapException {
		String queryType = params.getString("queryType");
		CapAjaxFormResult result = new CapAjaxFormResult();
		if ("1".equals(queryType)) {
			List<CodeType> countiesList = codetypeService.findByCodeTypeList(
					"counties", "zh_TW");
			for (CodeType codetype : countiesList) {
				result.set(codetype.getCodeDesc3(), codetype.getCodeDesc());
			}
		} else {
			String key = params.getString("key");
			CodeType value = null;
			List<CodeType> values = codetypeService.findByCodeTypeAndCodeDescs(
					"counties", null, null, key, "zh_TW");
			if (values != null && !values.isEmpty()) {
				value = values.get(0);
			}
			if (Util.isNotEmpty(value)) {
				List<CodeType> countiesList = codetypeService
						.findByCodeTypeList("counties" + value.getCodeValue(),
								"zh_TW");
				for (CodeType codetype : countiesList) {
					result.set(codetype.getCodeDesc2(), codetype.getCodeDesc());
				}
			}
		}

		return result;
	}

	/**
	 * 是否要出現呈總行的按鈕
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBranchData(PageParameters params)
			throws CapException {
		// select PARENTBRNO from COM.BELSBRN where BRNO=? with ur
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A meta = lmsService.findModelByMainId(L120M01A.class, mainId);
		IBranch ibranch = branchService.getBranch(user.getUnitNo());
		// 當目前登錄 分行有總行時才顯示 呈總行的按鈕
		if (Util.isEmpty(Util.trim(ibranch.getParentBrNo()))) {
			result.set("showSendBranch", false);
		} else {
			result.set("showSendBranch", true);
		}

		// J-109-0363_05097_B1001 Web
		// e-Loan授信修改日本地區分行大阪分行授權外案件經由東京分行放行後送呈授信審查處(Country Head 模式)。
		// 取得目前登錄分行國別
		result.set("country", ibranch.getCountryType());
		result.set("brno", ibranch.getBrNo());

		// 加會營運中心單位
		result.set(
				"areaBrId",
				(meta == null) ? UtilConstants.Mark.SPACE : Util.trim(meta
						.getAreaBrId()));
		// 所屬母行
		result.set("parentBrNo", ibranch.getParentBrNo());
		// 呈主管種類(呈主管審核批覆OR呈主管覆核)-- 特殊分行退回會簽意見使用
		result.set("spectialFlag", Util.trim(meta.getRptId()));

		// J-110-0104_05097_B1001 Web
		// e-Loan企金授信配合組織再造，預計北一區、北二區合併為北區授管中心，北區營業單位之授管中心授權外案件直送授信審查處
		String sendDirectlyHead = "N";
		if (lmsService.isAreaBranchDirectSendHead(meta)) {
			// J-112-0076_05097_B1001 Web
			// e-Loan授信針對一般異常通報案件(非小規C方案)，報案流程變更為要經北區營運中心後，再送授信審查處
			sendDirectlyHead = "Y";
		}

		result.set("sendDirectlyHead", sendDirectlyHead);

		// 消金無紙化 only 單位主管才可放行，其餘覆核人員僅新增覆核軌跡 by 008831 2021/12/14
		String clsOnlyBrnMgrAprv = "N";
		if (UtilConstants.Casedoc.DocType.個金
				.equals(Util.trim(meta.getDocType()))
				&& UtilConstants.Casedoc.SimplifyFlag.快速審核信貸.equals(meta
						.getSimplifyFlag())) {
			clsOnlyBrnMgrAprv = CapString.trimNull(sysParameterService
					.getParamValue("CLS_ONLY_BRNMGR_APRV"));
		} else {
			// 當 消金簽報書 且 SimplifyFlag=E 才去檢核{系統參數}
			// 否則，會造成 一般的消金簽報書 在 授信主管 click 覆核後，docStatus仍停留在「待覆核」
		}
		result.set("clsOnlyBrnMgrAprv", clsOnlyBrnMgrAprv);

		// 檢查user是否為單位/授權主管
		/*
		 * By 柏翰 因現在時間緊迫 ● 分行經理 就是{單位/授權主管}、{單位主管} ● 先不處理 副理 代替 經理 核決這件事
		 * 
		 * 若要處理這件事，還要有一套機制，讓e-Loan 系統能區分得出來 {副理} 只是授信主管角色、沒有代替經理核決 {副理}
		 * 除了授信主管角色、還有{代替經理}核決
		 * 
		 * 這套機制, 之後再和 消金處 討論
		 * 
		 * =>現在只能【先求有】
		 */

		// //TODO 抓 l120m01f_L5 單位/授權主管?? 柏翰說直接抓分行經理
		// List<L120M01F> list = (List<L120M01F>)
		// clsService.findL120M01F_mainId(mainId);
		// Boolean userIsBrnMgr = false;
		// for (L120M01F data : list) {
		// if (UtilConstants.BRANCHTYPE.分行.equals(data.getBranchType()) &&
		// UtilConstants.STAFFJOB.單位授權主管L5.equals(data.getStaffJob()) &&
		// data.getStaffNo().equals(user.getUserId())) {
		// userIsBrnMgr = true;
		// }
		// }
		//
		// if (userIsBrnMgr) {
		// result.set("userIsBrnMgr", "Y");
		// } else {
		// result.set("userIsBrnMgr", "N");
		// }

		List<String> approvalSupervisorList = new ArrayList<String>();
		approvalSupervisorList.add(ibranch.getBrnMgr());

		// 呂襄理新增"授權主管"也可為"已核准覆核主管"
		if (this.lmsService.isOpenPaperlessSigningFunction()) {

			List<L800M01A> list = l800m01aDao.findByBrno();
			for (L800M01A model : list) {
				if ("V".equals(Util.trim(model.getIsType3()))) {
					approvalSupervisorList.add(model.getZhuGuan());
				}
			}
		}

		// 分行檔單位主管(每日凌晨更新 from sso)
		if (approvalSupervisorList.contains(user.getUserId())) {
			result.set("userIsBrnMgr", "Y");
		} else {
			result.set("userIsBrnMgr", "N");
		}

		// J-111-0488_05097_B1001 Web e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
		result.set("overAuthSendHead",
				lmsService.isOverAuthSendHead(meta) ? "Y" : "N");

		return result;
	}

	/**
	 * 取得徵信資信簡表線上列印網址
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query)
	public IResult printCes(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String cesUrl = Util.trim(sysParameterService
				.getParamValue(SysParamConstants.SSO_URL_PREFIX + "CES"));
		JSONObject cesData = JSONObject.fromObject(params.getString("cesData"));
		String typCd = Util.trim(cesData.getString("typCd"));
		String cesMainId = Util.trim(cesData.getString("mainId"));
		String cesOid = Util.trim(cesData.getString("oid"));
		String webService = null;
		StringBuilder sbUrl = new StringBuilder();
		sbUrl.setLength(0);
		if (Util.isNotEmpty(typCd)) {
			switch (typCd.charAt(0)) {
			case '1':
				webService = "ces1201r01fornotes";
				break;
			case '4':
				webService = "ces1204r01fornotes";
				break;
			case '5':
				webService = "ces1205r01fornotes";
				break;
			default:
				webService = "ces1201r01fornotes";
			}
			;
		} else {
			webService = "ces1201r01fornotes";
		}
		sbUrl.append(
				LMSUtil.checkSubStr(cesUrl, 0, cesUrl.indexOf("/ces-web/")) ? cesUrl
						.substring(0, cesUrl.indexOf("/ces-web/")) : cesUrl)
				.append("/ces-web/app/ces/").append(Util.trim(webService))
				.append("?printMainId=").append(cesMainId)
				.append("&printMainOid=").append(cesOid);
		result.set("webLink", sbUrl.toString());
		return result;
	}// ;

	/**
	 * 常用主管名單查詢
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryCommonManager(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		JSONObject managers = new JSONObject();

		List<L800M01A> list = l800m01aDao.findByBrno();
		if (!list.isEmpty()) {
			for (L800M01A model : list) {
				String dataType = Util.trim(model.getDataType());
				String zhuGuan = Util.trim(model.getZhuGuan());
				String zGName = Util.trim(model.getZGName());

				for (int i = 1; i <= 5; i++) {
					String value = String.valueOf(i);
					if ("V".equals(Util.trim(model.get("isType" + value)))) {
						String key = dataType + value;
						JSONObject json = (JSONObject) managers.get(key);
						if (json != null) {
							json.put(zhuGuan, zGName);
						} else {
							json = new JSONObject();
							json.put(zhuGuan, zGName);
							managers.put(key, json);
						}
					}
				}
			}
		}

		result.set("managers", new CapAjaxFormResult(managers));
		return result;
	}

	@Resource
	MisELF348Service mis348Service;

	/**
	 * 取得段(SITE3)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult querySIET3(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String SITE1 = params.getString("fCity");
		String SITE2 = params.getString("fZip");
		// 是否值為中文
		boolean byName = params.getBoolean("byName");
		List<Map<String, Object>> rowData = eloandbcmsBASEService
				.getCMSC101M09FindSECTION1(SITE1, SITE2);
		LinkedHashMap<String, String> SITE3s = new LinkedHashMap<String, String>();
		for (Map<String, Object> data : rowData) {
			String key = Util.trim(data.get("IR48"));
			String value = Util.trim(data.get("SITE3"));
			String site4 = Util.trim(data.get("SITE4"));
			if (byName) {
				SITE3s.put(value, value);
			} else {
				SITE3s.put(key, value + site4);
			}

		}
		result.set("SITE3", new CapAjaxFormResult(SITE3s));
		return result;
	}// ;

	/**
	 * 取得小段(SITE4)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult querySIET4(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String SITE1 = Util.trim(params.getString("fCity"));
		String SITE2 = params.getString("fZip");
		String fSITE3 = Util.trim(params.getString("fSITE3"));

		// 是否值為中文
		boolean byName = params.getBoolean("byName");
		List<Map<String, Object>> rowData = eloandbcmsBASEService
				.getCMSC101M09FindSECTION2(SITE1, SITE2, fSITE3);
		LinkedHashMap<String, String> SITE4 = new LinkedHashMap<String, String>();
		for (Map<String, Object> data : rowData) {
			String key = Util.trim(data.get("SITE4"));
			String value = Util.trim(data.get("SITE4"));
			if (Util.isEmpty(key)) {
				continue;
			}
			if (byName) {
				key = value;
			}
			SITE4.put(key, value);
		}
		result.set("SITE4", new CapAjaxFormResult(SITE4));
		return result;
	}// ;

	/**
	 * 查詢擔保品座落-村
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult querySIT4NO(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String LOCATE1DESC = Util.trim(params.getString("LOCATE1DESC"));
		String LOCATE2DESC = params.getString("LOCATE2DESC");
		LOCATE1DESC = LOCATE1DESC.replace("台", "臺");
		/*
		 * 因 LOCATE2DESC 可能有 臺東市 or 臺西鄉 or 霧臺鄉
		 */
		if (Util.equals(LOCATE2DESC, "台東市") || Util.equals(LOCATE2DESC, "台西鄉")
				|| Util.equals(LOCATE2DESC, "霧台鄉")) {
			LOCATE2DESC = LOCATE2DESC.replace("台", "臺");
		}
		List<Map<String, Object>> c101m15s = eloandbcmsBASEService
				.getCMSC101M15ByLOCATE1DESC(LOCATE1DESC, LOCATE2DESC);
		HashMap<String, String> options = new HashMap<String, String>();
		for (Map<String, Object> data : c101m15s) {
			options.put(Util.trim(data.get("LOCATE3CODE")),
					Util.trim(data.get("LOCATE3DESC")));
		}
		result.set("SITE4", new CapAjaxFormResult(options));
		return result;
	}

	/**
	 * 查詢擔保品合計數
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult querySuminAmt(PageParameters params)
			throws CapException {

		String mainId = Util.trim(params.getString("mainId")); // L140M01A_mainId

		List<Map<String, Object>> c100m01_rptTypeFlag_old_list = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> c100m01_rptTypeFlag_1_list = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> c100m01_rptTypeFlag_2_list = new ArrayList<Map<String, Object>>();
		/*
		 * 在引入擔保品時，可能引入N筆，但會同時混雜[舊版、分開估價、合併估價]
		 */
		for (L140M01O l140m01o : clsService.findL140M01O(mainId)) {
			Map<String, Object> c100m01_map = eloandbcmsBASEService
					.getC100M01ByOid(l140m01o.getCmsOid());
			if (MapUtils.isEmpty(c100m01_map)) {
				continue;
			}
			String rptTypeFlag = Util.trim(MapUtils.getString(c100m01_map,
					"RPTTYPEFLAG"));
			if (Util.equals("", rptTypeFlag)) {
				c100m01_rptTypeFlag_old_list.add(c100m01_map);
			} else if (Util.equals("1", rptTypeFlag)) {
				c100m01_rptTypeFlag_1_list.add(c100m01_map);
			} else if (Util.equals("2", rptTypeFlag)
					|| Util.equals("3", rptTypeFlag)
					|| Util.equals("4", rptTypeFlag)) {
				// J-109-0074擔保品不動產地上權相關修改
				// rptTypeFlag=3:新版地上權估價
				c100m01_rptTypeFlag_2_list.add(c100m01_map);
			}
		}

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMSL140M01MPanel.class);

		if (c100m01_rptTypeFlag_old_list.size() > 0
				&& c100m01_rptTypeFlag_1_list.size() == 0
				&& c100m01_rptTypeFlag_2_list.size() == 0) { // 在 L140M01O
																// 包含的資料，全部都是[舊版]
																// => 按原有邏輯
			Map<String, Object> data = eloandbcmsBASEService
					.findC101M06ByL140M01A(mainId);

			CapAjaxFormResult result = new CapAjaxFormResult(data);
			// SINAMT >>購價
			// SLNDAMT >>土地估值
			// SBLDAMT >>建物估值
			// SLNDTAX >>土地增值稅
			// SDISAMT >>扣除寬限期預提折舊金額
			// SMINAMT >>房屋稅與地價稅 (當 minTax='Y')
			// STTLAMT >>放款值(本案押品應計放款值 , 已扣除前順位抵押金額)
			// SDEAMT >> 建物折舊
			for (String col : new String[] { "SINAMT", "SLNDAMT", "SBLDAMT",
					"SLNDTAX", "SDISAMT", "SMINAMT", "STTLAMT", "SDEAMT" }) {
				if (!result.containsKey(col)) {
					result.set(col, "");
				}
			}

			BigDecimal SINAMT = Util.parseBigDecimal(data.get("SINAMT"));
			BigDecimal SLNDAMT = Util.parseBigDecimal(data.get("SLNDAMT"));
			BigDecimal SBLDAMT = Util.parseBigDecimal(data.get("SBLDAMT"));
			BigDecimal SLNDTAX = Util.parseBigDecimal(data.get("SLNDTAX"));
			BigDecimal SDISAMT = Util.parseBigDecimal(data.get("SDISAMT"));
			BigDecimal SMINAMT = Util.parseBigDecimal(data.get("SMINAMT"));
			BigDecimal SDEAMT = Util.parseBigDecimal(data.get("SDEAMT"));
			// double STTLAMT = Util.parseDouble(data.get("STTLAMT"));

			// double TotLNDAMTBLDAMT =
			// Util.parseDouble(CapMath.add(Util.trim(data.get("SLNDAMT")),
			// Util.trim(data.get("SBLDAMT")), 0));
			// 擔保品鑑價 = 土地估值 + 建物估值 + 土地增值稅 + 建物折舊
			BigDecimal nowAMT = SLNDAMT.add(SBLDAMT).add(SLNDTAX).add(SDEAMT);
			result.set("nowAMT", nowAMT);

			// mins = Math.min(SINAMT, nowAMT)
			BigDecimal mins = SINAMT;
			if (nowAMT.compareTo(mins) < 0) {
				mins = nowAMT;
			}

			result.set("valueAMT", mins.subtract(SLNDTAX).subtract(SDISAMT));

			// L140M01M.Mome04=本案引入之不動產擔保品中寬限期預提折舊金額合計{0}；土地應計增值稅合計{1}；房屋稅與地價稅金額合計{2}。
			result.set("SLNDTAX", MessageFormat.format(
					prop.getProperty("L140M01M.Mome04"),
					String.valueOf(SDISAMT), String.valueOf(SLNDTAX),
					String.valueOf(SMINAMT)));
			return result;
		} else {
			CapAjaxFormResult result = new CapAjaxFormResult();
			return result;
		}
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryAllBranch(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		JSONArray branchCount = new JSONArray();
		List<IBranch> bank = branchService.getAllBranch();
		for (IBranch b : bank) {
			JSONObject json = new JSONObject();
			String brName = Util.trim(b.getBrName());
			String brCode = b.getBrNo();
			json.put("brCode", brCode);
			json.put("brName", brName);

			branchCount.add(json);
		}

		result.set("size", branchCount.size());
		result.set("item", branchCount);

		return result;

	}

	/**
	 * 檢查原案額度序號
	 * 
	 * @param params
	 *            <pre>
	 *            {String}cntrNo 額度序號 
	 *            {String}justSave  1.為查詢舊額度序號2.儲存預約額度序號 3,聯行攤貸比例  
	 *            {String}oid額度明細表文件編號
	 * </pre>
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkCntrno(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("errMsg", "");
		String cntrNo = params.getString("cntrNo", "");
		String custId = params.getString("custId", "");
		String dupNo = params.getString("dupNo", "0");

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		if (Util.isEmpty(cntrNo)) {
			// result.set("errMsg", "L140M01a.message68");
			String msg = prop.getProperty("L140M01a.message68");
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}

		// 2012_05_22_ 建霖說看已核准簽報書的額度明細表額度序號與攤貸行(l140m01e)的
		List<L140M01A> l140m01as = l140m01aDao.findL140m01aListBycntrNo(cntrNo,
				custId, dupNo);
		int countE = eloandbBASEService.findL140M01EByCustIdAndDupNoAndCntrno(
				custId, dupNo, cntrNo);
		// 是否檢查額度種類
		// 在額度明細表查詢不到時在到帳務系統內查詢
		if (l140m01as.isEmpty() && countE == 0) {
			int count = 0;

			// 檢查預約額度
			int count442 = misELF442Service.findELF442ByCntrNoByCheck3(custId,
					dupNo, cntrNo).size();

			// 海外查 DW_ASLNQUOT
			if (TypCdEnum.海外.getCode().equals(cntrNo.substring(3, 4))) {
				count = dwLnquotovService.findByCntrNoAndCustIdAndDupNo(cntrNo,
						custId, dupNo);

			} else {
				custId = Util.addSpaceWithValue(custId, 10);
				// X為 遠匯
				if ("X".equals(cntrNo.substring(7, 8))) {
					count = misdbBASEService.findLNF197BycntrNoAndCustId(
							cntrNo, custId, dupNo);
				} else {
					count = misdbBASEService.findMISLN20BycntrNoAndCustId(
							cntrNo, custId, dupNo);
				}
			}

			if (count == 0 && count442 == 0) {
				// L140M01a.message69=此舊額度序號：{0} 並未存在於帳務系統, 請確認後再輸入!
				// result.set("errMsg", "L140M01a.message69");
				String msg = MessageFormat.format(
						prop.getProperty("L140M01a.message69"), cntrNo);
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
						getClass());
			}
		}

		return result;// 傳回執行這個動作的AjAX
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryLnf07aCntrNoControl(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo", "0"));

		List<Map<String, Object>> raw_list = lnLNF07AService
				.findByCntrNoControl();
		List<Map<String, Object>> contract_list = _group_lnf07a_list(
				"LNPS166 CONTRACT CONTROL", raw_list);
		List<Map<String, Object>> id_list = _group_lnf07a_list(
				"LNPS166 ID CONTROL", raw_list);
		List<Map<String, Object>> group_list = _group_lnf07a_list(
				"LNPS166 GROUP CONTROL", raw_list);

		if (Util.isNotEmpty(custId)) {
			// 統編
			String idDup = LMSUtil.getCustKey_len10custId(custId, dupNo);
			Set<String> in_idDupSet = new HashSet<String>();
			if (true) {
				in_idDupSet.add(idDup);
			}
			// 額度
			List<Map<String, Object>> lnf020_list = misMISLN20Service
					.findByCustIdDupNoIncludeCancel(idDup);
			Set<String> in_cntrNoSet = new HashSet<String>();
			for (Map<String, Object> mapLNF020 : lnf020_list) {
				String cntrNo = MapUtils
						.getString(mapLNF020, "LNF020_CONTRACT");
				in_cntrNoSet.add(cntrNo);
			}
			// 集團
			Set<String> in_grpSet = new HashSet<String>();
			List<Map<String, Object>> grpDataList = misGrpcmpService
					.findGrpcmpSelGrpdtl(custId, dupNo);
			for (Map<String, Object> grpItem : grpDataList) {
				String grpId = Util.trim(Util.getRightStr(
						MapUtils.getString(grpItem, "GRPID"), 3));
				in_grpSet.add(grpId);
			}

			contract_list = _filter_ctlItem(contract_list, in_cntrNoSet);
			id_list = _filter_ctlItem(id_list, in_idDupSet);
			group_list = _filter_ctlItem(group_list, in_grpSet);
		}
		HashMap<String, JSONArray> map_contract = new HashMap<String, JSONArray>();
		HashMap<String, JSONArray> map_id = new HashMap<String, JSONArray>();
		HashMap<String, JSONArray> map_group = new HashMap<String, JSONArray>();

		if (true) {
			_proc_lnf07a_list(contract_list, map_contract);
			_proc_lnf07a_list(id_list, map_id);
			_proc_lnf07a_list(group_list, map_group);
		}
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMSM02BPanel.class);
		Properties prop_2 = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		Properties prop_3 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		result.set("str_ctlItem", prop.getProperty("L130S02A.ctlItem"));// 控管資料
		result.set("str_startDate", prop_3.getProperty("other.msg65"));// 核准日期
		result.set("str_endDate", prop.getProperty("L130S02A.endDate"));
		result.set("contract_title", prop.getProperty("L130S02A.ctlType.1"));
		result.set("contract_size", contract_list.size());
		result.set("contract_data", new CapAjaxFormResult(map_contract));
		result.set(
				"contract_strCnt",
				MessageFormat.format(prop_2.getProperty("findDataCnt"),
						Util.trim(contract_list.size())));
		result.set("id_title", prop.getProperty("L130S02A.ctlType.2"));
		result.set("id_size", id_list.size());
		result.set("id_data", new CapAjaxFormResult(map_id));
		result.set(
				"id_strCnt",
				MessageFormat.format(prop_2.getProperty("findDataCnt"),
						Util.trim(id_list.size())));
		result.set("group_title", prop.getProperty("L130S02A.ctlType.3"));
		result.set("group_size", group_list.size());
		result.set("group_data", new CapAjaxFormResult(map_group));
		result.set(
				"group_strCnt",
				MessageFormat.format(prop_2.getProperty("findDataCnt"),
						Util.trim(group_list.size())));

		return result;// 傳回執行這個動作的AjAX
	}

	private List<Map<String, Object>> _filter_ctlItem(
			List<Map<String, Object>> src_list, Set<String> allowSet) {
		List<Map<String, Object>> r = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> obj : src_list) {
			String ctlItem = Util.trim(MapUtils.getObject(obj, "LNF07A_KEY_2"));
			if (allowSet.contains(ctlItem)) {
				r.add(obj);
			}
		}
		return r;
	}

	private void _proc_lnf07a_list(List<Map<String, Object>> ctl_list,
			Map<String, JSONArray> map_ctl) {
		JSONArray jsonArray = new JSONArray();
		for (Map<String, Object> row : ctl_list) {

			JSONObject o = new JSONObject();
			o.put("ctlItem", Util.trim(MapUtils.getObject(row, "LNF07A_KEY_2")));
			o.put("ctlSDate", StringUtils.left(
					Util.trim(MapUtils.getObject(row, "LNF07A_CONTENT_1")), 10));
			o.put("ctlEDate", StringUtils.left(
					Util.trim(MapUtils.getObject(row, "LNF07A_CONTENT_2")), 10));
			String inCtl_caseNo = Util.trim(MapUtils.getObject(row,
					"LNF07A_CONTENT_3"));
			String release_caseNo = Util.trim(MapUtils.getObject(row,
					"LNF07A_CONTENT_4"));
			o.put("memo", Util.isNotEmpty(release_caseNo) ? release_caseNo
					: inCtl_caseNo);
			jsonArray.add(o);
		}
		map_ctl.put("arr", jsonArray);
	}

	private List<Map<String, Object>> _group_lnf07a_list(String sLNF07A_KEY_1,
			List<Map<String, Object>> raw_list) {
		List<Map<String, Object>> r = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> row : raw_list) {
			if (Util.equals(sLNF07A_KEY_1,
					Util.trim(MapUtils.getString(row, "LNF07A_KEY_1")))) {
				r.add(row);
			}
		}
		return r;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult applyELF517(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String cntrNo = Util.trim(params.getString("cntrNo"));

		Map<String, String> map = lmsService.call_elf517(cntrNo);
		for (String key : map.keySet()) {
			String val = map.get(key);
			result.set(key, val);
		}

		return result;// 傳回執行這個動作的AjAX
	}

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult applyClearLand(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String cntrNo = Util.trim(params.getString("cntrNo"));

		String[] needCol = new String[] { "isClearLand", "ctlType", "fstDate",
				"lstDate", "isChgStDate", "cstDate", "cstReason", "adoptFg",
				"isChgRate", "rateAdd", "custRoa", "relRoa", "roaBgnDate",
				"roaEndDate", "isLegal" };
		// 清除其他欄位
		for (String col : needCol) {
			result.set(col, "");
		}

		ELF600 elf600 = misELF600Service.findByContract(cntrNo);
		String isClearLand = lmsService.isClearLandEffective(elf600);
		result.set("isClearLand", isClearLand);
		if (Util.equals(isClearLand, "Y")) {
			result.set("ctlType", Util.trim(elf600.getElf600_ctltype()));
			result.set("fstDate", Util.trim(elf600.getElf600_fstdate()));
			result.set("lstDate", Util.trim(elf600.getElf600_lstdate()));
			result.set("actStartDate", Util.trim(elf600.getElf600_act_st_date()));
		} else {
			result.set("ctlType", "");
			result.set("fstDate", "");
			result.set("lstDate", "");
			result.set("actStartDate", "");
		}

		return result;// 傳回執行這個動作的AjAX
	}

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult applyClearLandRoa(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		String subMainId = Util.trim(params.getString("subMainId"));
		String mainMainId = Util.trim(params.getString("mainMainId"));
		String mainMainId_CLS = Util.trim(params.getString("mainMainId_CLS"));
		String callFrom = Util.trim(params.getString("callFrom"));

		// 1.引進借款人與關係戶名單
		String queryDateS = "";
		String queryDateE = "";

		boolean isTestEmail = true; // 是否為測試信件
		isTestEmail = "true".equals(PropUtil.getProperty("isTestEmail")) ? true
				: false; // 是否為測試信件

		Date max_cyc_mn = this.dwdbService.getMD_CUPFM_OTS_max_cyc_mn();

		String MAX_CYC_MN = "";
		String MIN_CYC_MN = "";
		if (max_cyc_mn != null) {
			MAX_CYC_MN = CapDate.formatDate(max_cyc_mn,
					UtilConstants.DateFormat.YYYY_MM_DD);
		} else {
			throw new CapMessageException("資料倉儲無最新資料", getClass());
		}

		queryDateS = CapDate
				.formatDate(CapDate.addMonth(Util.parseDate(MAX_CYC_MN), -11),
						"yyyy-MM-dd");
		queryDateE = MAX_CYC_MN;

		// FOR TEST
		// queryDateS = "2019-01-01";
		// queryDateE = "2019-03-01";

		Map<String, String> map = null;
		if (Util.equals(callFrom, "L140M01A")) {
			// 額度明細表
			L140M01A l140m01a = lmsService.findModelByMainId(L140M01A.class,
					subMainId);

			L120M01A l120m01a = lmsService.findModelByMainId(L120M01A.class,
					mainMainId);
			// 代表為個金簽報書
			if (null == l140m01a) {
				l140m01a = lmsService.findModelByMainId(L140M01A.class,
						mainMainId_CLS);
				l120m01a = l140m01a.getL120m01c().getL120m01a();
			}

			String brNo = l120m01a.getCaseBrId();
			String typCd = l120m01a.getTypCd();
			map = lmsService.applyClearLandRoa(typCd, brNo, l120m01a, l140m01a,
					cntrNo, custId, dupNo, custName, queryDateS, queryDateE);
		} else if (Util.equals(callFrom, "L161S01A")) {
			// 聯貸案參貸比率一覽表主檔....額度動用資訊一覽表
			// L161S01A

			L160M01A l160m01a = lmsService.findModelByMainId(L160M01A.class,
					mainMainId);
			L161S01A l161s01a = lmsService.findL161s01aByMainIdUid(mainMainId,
					subMainId);

			String brNo = l160m01a.getCaseBrId();
			String typCd = l160m01a.getTypCd();

			map = lmsService.applyClearLandRoa(typCd, brNo, l160m01a, l161s01a,
					cntrNo, custId, dupNo, custName, queryDateS, queryDateE);

		} else if (Util.equals(callFrom, "C160M01B")) {
			// 聯貸案參貸比率一覽表主檔....額度動用資訊一覽表
			// L161S01A

			C160M01A c160m01a = lmsService.findModelByMainId(C160M01A.class,
					mainMainId);
			C160M01B c160m01b = lmsService.findC160m01bByMainIdUid(mainMainId,
					subMainId);

			String brNo = c160m01a.getCaseBrId();
			String typCd = c160m01a.getTypCd();

			map = lmsService.applyClearLandRoa(typCd, brNo, c160m01a, c160m01b,
					cntrNo, custId, dupNo, custName, queryDateS, queryDateE);

		}

		for (String key : map.keySet()) {
			String val = map.get(key);
			result.set(key, val);
		}

		// result.set("custRoa", "1.25");
		// result.set("relRoa", "2.25");
		// result.set("roaBgnDate", "2018-04-09");
		// result.set("roaEndDate", "2019-04-09");

		return result;// 傳回執行這個動作的AjAX
	}

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult checkIsLegal(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		String isClearLand = params.getString("isClearLand", "");
		String isChgStDate = params.getString("isChgStDate", "");
		String isChgRate = params.getString("isChgRate", "");
		String ctlType = params.getString("ctlType", "");
		String fstDate = params.getString("fstDate", "");
		String lstDate = params.getString("lstDate", "");
		String cst_Date = params.getString("cstDate", "");
		String adoptFg = params.getString("adoptFg", "");
		BigDecimal rateAdd = Util.parseBigDecimal(0);
		BigDecimal custRoa = Util.parseBigDecimal(0);
		BigDecimal relRoa = Util.parseBigDecimal(0);

		if (Util.equals(isChgRate, "Y")
				|| (Util.equals(isChgStDate, "Y") && adoptFg.contains("3"))) {
			if (Util.notEquals("", params.getString("rateAdd"))
					&& Util.isNumeric(NumConverter.delCommaString(params
							.getString("rateAdd")))) {
				rateAdd = Util.parseBigDecimal(NumConverter
						.delCommaString(String.valueOf(params
								.getDouble("rateAdd"))));
			} else {
				// L140MM4A.rateAdd=利率再加碼幅度 notMatchFormat=格式有誤
				throw new CapMessageException(
						(pop.getProperty("L140M01M.rateAdd") + pop
								.getProperty("notMatchFormat")),
						getClass());
			}

			if (Util.notEquals("", params.getString("custRoa"))
					&& Util.isNumeric(NumConverter.delCommaString(params
							.getString("custRoa")))) {
				custRoa = Util.parseBigDecimal(NumConverter
						.delCommaString(String.valueOf(params
								.getDouble("custRoa"))));
			} else {
				// L140MM4A.custRoa=借款人ROA notMatchFormat=格式有誤
				throw new CapMessageException(
						(pop.getProperty("L140M01M.custRoa") + pop
								.getProperty("notMatchFormat")),
						getClass());
			}

			if (Util.notEquals("", params.getString("relRoa"))
					&& Util.isNumeric(NumConverter.delCommaString(params
							.getString("relRoa")))) {
				relRoa = Util.parseBigDecimal(NumConverter
						.delCommaString(String.valueOf(params
								.getDouble("relRoa"))));
			} else {
				// L140MM4A.relRoa=關係人ROA notMatchFormat=格式有誤
				throw new CapMessageException(
						(pop.getProperty("L140M01M.relRoa") + pop
								.getProperty("notMatchFormat")),
						getClass());
			}
		}

		boolean firstChange = (Util.equals(fstDate, lstDate)) ? true : false;
		boolean cstDate = (Util.equals(Util.nullToSpace(cst_Date), "")) ? false
				: true;

		if (Util.equals(adoptFg, "")) {
			if (Util.equals(isChgRate, "Y") && Util.equals(isChgStDate, "N")) {
				adoptFg = "3";
			}
		}
		String outcome = lmsService.checkIsLegal(adoptFg, ctlType, firstChange,
				cstDate, rateAdd, custRoa, relRoa);
		result.set("isLegal", outcome);

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL140S05A(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSL140M01MPanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("cntrNoMainId"));
		L140M01A l140m01a = lmsService
				.findModelByMainId(L140M01A.class, mainId);
		L140S05A l140s05a = lmsService
				.findModelByMainId(L140S05A.class, mainId);
		if (l140s05a != null) {
			result.set("has", true);
			result = DataParse
					.toResult(l140s05a, true, DataParse.Delete, new String[] {
							EloanConstants.MAIN_ID, EloanConstants.OID });
		} else {
			result.set("has", false);
		}
		// 本位幣
		String localCurr = "";
		if (Util.isNotEmpty(Util.trim(l140m01a.getCntrNo()))) {
			localCurr = lmsService.getLocalCurrByCntrNo(l140m01a.getCntrNo());
		} else {
			// L140S05A.error04=請先取得額度序號
			throw new CapMessageException(pop.getProperty("L140S05A.error04"),
					getClass());
		}
		// String localCurr =
		// lmsService.getLocalCurrByCntrNo(l140m01a.getCntrNo());
		// String localCurr = Util.trim(branchService.getBranch(
		// MegaSSOSecurityContext.getUnitNo()).getUseSWFT());
		result.set("localCurr", localCurr);
		result.set("intRateL_C", localCurr);
		result.set("rateL_C", localCurr);

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveL140S05A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString("cntrNoMainId"));
		String l140s05aForm = Util.trim(params.getString("LMS140S05AForm"));
		L140S05A l140s05a = lmsService
				.findModelByMainId(L140S05A.class, mainId);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSL140M01MPanel.class);
		if (l140s05a == null) {
			l140s05a = new L140S05A();
			l140s05a.setCreateTime(CapDate.getCurrentTimestamp());
			l140s05a.setCreator(user.getUserId());
			l140s05a.setMainId(mainId);
		}
		l140s05a.setUpdateTime(CapDate.getCurrentTimestamp());
		l140s05a.setUpdater(user.getUserId());

		DataParse.toBean(l140s05aForm, l140s05a);
		// 本位幣若為NA 清空幣別欄位
		if (Util.equals(l140s05a.getQuant_intRateL(), "X")) {
			l140s05a.setIntRateL_C(null);
		}
		if (Util.equals(l140s05a.getQuant_rateL(), "X")) {
			l140s05a.setRateL_C(null);
		}
		if (!BeanValidator.isValid(l140s05a)) {
			throw new CapMessageException(BeanValidator.getValidMsg(l140s05a,
					LMSL140M01MPanel.class), getClass());
		}

		JSONObject jsonL140s05a = JSONObject.fromObject(l140s05aForm);
		String[] chkQuant = new String[] { "intRateL", "intRateF", "rateL",
				"rateF", "grace", "drawdown", "loan", "estComDate", "others" };
		String[] chk2 = new String[] { "intRateL", "intRateF", "rateL", "rateF" };
		for (String s : chkQuant) {
			String v = jsonL140s05a.optString("quant_" + s, "");
			if (Util.isNotEmpty(v) && Util.notEquals("X", v)) {
				if (ArrayUtils.contains(chk2, s)) {
					String curr = jsonL140s05a.optString(s + "_C", "");
					String rate = jsonL140s05a.optString(s + "_R", "");
					if (Util.isEmpty(curr) || Util.isEmpty(rate)) {
						// L140S05A.error01=尚有欄位未輸入
						throw new CapMessageException(
								pop.getProperty("L140S05A.error01"), getClass());
					}
				} else {
					String value = jsonL140s05a.optString(s, "");
					if (Util.isEmpty(value)) {
						// L140S05A.error01=尚有欄位未輸入
						throw new CapMessageException(
								pop.getProperty("L140S05A.error01"), getClass());
					}
				}
			}
		}
		boolean atLeast = false;
		String[] chkAtLeast = new String[] { "intRateL", "intRateF", "rateL",
				"rateF", "grace", "drawdown", "loan", "coll", "guarantor",
				"commitments", "estComDate", "others" };
		for (String s : chkAtLeast) {
			String v = jsonL140s05a.optString("quant_" + s, "");
			if (Util.isNotEmpty(v) && Util.notEquals("X", v)) {
				atLeast = true;
				break;
			}
		}
		if (!atLeast) {
			// L140S05A.error03=變更條件項目至少輸入一項
			throw new CapMessageException(pop.getProperty("L140S05A.error03"),
					getClass());
		}

		String cond_chg = lmsService.getCondChg(l140s05a);// 上傳用文字
		l140s05a.setCond_chg(cond_chg);
		
		// J-112-0553 央行購地貸款-變更條件-授信期間增加-實際動工日有值則不限制央行成數檢核
		L140M01M l140m01m = lmsService.findModelByMainId(L140M01M.class, mainId);
		if(l140m01m != null 
				&& UtilConstants.L140M01MCbcCase.土地抵押貸款.equals(l140m01m.getCbcCase()) 
				&& l140m01m.getActStartDate() != null
				&& !UtilConstants.Usedoc.upType.增額.equals(l140s05a.getQuant_loan())){
			
			l140m01m.setChkYN(UtilConstants.DEFAULT.否);
			L140M01A l140m01a = lmsService.findModelByMainId(L140M01A.class, mainId);
			l140m01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核); // 變更條件項目, 把明細表檢核欄清空
			this.lmsService.save(l140m01m, l140m01a);
		}

		lmsService.save(l140s05a);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveCheckListForHighPricedHousingLoan(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String checkListForm = Util.trim(params.getString("checkListForm"));
		lmsService.saveCheckListForHighPricedHousingLoan(mainId,
				params.getString("custId"), params.getString("dupNo"),
				checkListForm);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteCheckListForHighPricedHousingLoan(
			PageParameters params) throws CapException {

		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		lmsService.deleteCheckListForHighPricedHousingLoan(mainId,
				params.getString("custId"), params.getString("dupNo"));
		return new CapAjaxFormResult();
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryCheckListOfHighPricedHousingLoan(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String mainId = Util.nullToSpace(params.getString("l140m01aMainId"));

		L140M01X l140m01x = lmsService.findL140M01X(mainId, custId, dupNo);
		L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
		L140M01M l140m01m = lmsService
				.findModelByMainId(L140M01M.class, mainId);
		int docType = Integer.parseInt(l140m01a.getL120m01c().getL120m01a()
				.getDocType());

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		List<Map<String, String>> coLenderList = lmsService
				.getCoLenderForCorporateOrPersonalFinance(docType, mainId);
		String lender = "";
		for (Map<String, String> map : coLenderList) {
			lender += map.get("custId") + " " + map.get("custName") + "、";
		}

		result.set("mainLender", custId + " " + params.getString("custName"));
		result.set("coLender",
				lender.length() > 0 ? lender.substring(0, lender.length() - 1)
						: lender);
		if (l140m01x != null) {
			result.set("isGracePeriod", l140m01x.getIsGracePeriod());
			result.set("isGtSpecificInterest",
					l140m01x.getIsGtSpecificInterest());
			result.set("isCalculatedAtLowerAmount",
					l140m01x.getIsCalculatedAtLowerAmount());
			result.set("dealAmount",
					LMSUtil.pretty_numStr(l140m01x.getDealAmount()));
			result.set("appraisePrice",
					LMSUtil.pretty_numStr(l140m01x.getAppraisePrice()));
			result.set("approvedLoanQuota",
					LMSUtil.pretty_numStr(l140m01x.getApprovedLoanQuota()));
			result.set("isNewOrAddLoan", l140m01x.getIsNewOrAddLoan());
			result.set("newOrAddLoanQuota",
					LMSUtil.pretty_numStr(l140m01x.getNewOrAddLoanQuota()));
			result.set("isExistingLoan", l140m01x.getIsExistingLoan());
			result.set("existingLoanQuota",
					LMSUtil.pretty_numStr(l140m01x.getExistingLoanQuota()));
			result.set("isOtherAgencyLoan", l140m01x.getIsOtherAgencyLoan());
			result.set("otherAgencyLoanQuota",
					LMSUtil.pretty_numStr(l140m01x.getOtherAgencyLoanQuota()));
			result.set("isDepositPledgedLoans",
					l140m01x.getIsDepositPledgedLoans());
			result.set("isLeOriginalLoanQuota",
					l140m01x.getIsLeOriginalLoanQuota());
			result.set("isNotSpecificProjectCommit",
					l140m01x.getIsNotSpecificProjectCommit());
			result.set("isExplainToLender", l140m01x.getIsExplainToLender());
			result.set("description", l140m01x.getDescription());
			result.set("isShareAcquisition", l140m01x.getIsShareAcquisition());
			result.set("type",
					prop.getProperty("L140M01X.highPricedHousingLoanCheckList"));
		}

		result.set("version", l140m01m != null ? l140m01m.getVersion() : "");
		result.set("caseDate", l140m01a.getCaseDate());

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult isOpenUnsoldHouseLoanInfoFunction(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("isOpenUnsoldHouseLoanInfoFunction",
				this.lmsService.isOpenUnsoldHouseLoanInfoFunction());
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL140S07A(PageParameters params)
			throws CapException {

		String oid = Util.trim(params.getString("oid"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		L140S07A l140s07a = lmsService.findL140s07aByOid(oid);

		if (l140s07a != null) {
			String item = params.getString("item");
			String befText = params.getString("befText");
			String aftText = params.getString("aftText");
			l140s07a.setItem(item);
			l140s07a.setBefText(befText);
			l140s07a.setAftText(aftText);

			lmsService.save(l140s07a);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryL140S07Adetail(PageParameters params)
			throws CapException {

		String oid = Util.trim(params.getString("tOid"));
		CapAjaxFormResult result = new CapAjaxFormResult();

		L140S07A l140s07a = lmsService.findL140s07aByOid(oid);
		result.set("seqNum", "");
		result.set("item", "");
		result.set("befText", "");
		result.set("aftText", "");
		if (l140s07a != null) {
			result = DataParse.toResult(l140s07a);
		}

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL140s07a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String deleteOid = params.getString("tOid");
		lmsService.deleteL140s07a(deleteOid);

		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult addL140S07A(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String options = Util.trim(params.getString("options"));

		if (Util.equals("add", options)) {
			// List<L140S07A> list = lmsService.findL140s07aByMainId(mainId);
			// int seqNum = 1;
			// if (!list.isEmpty()) {
			// seqNum = list.size()+1;
			// }
			int seqNum = 1;
			L140S07A max = lmsService.findMaxSeqNumByMainId(mainId);
			if (max != null) {
				seqNum = max.getSeqNum() + 1;
			}

			L140S07A l140s07a = new L140S07A();
			l140s07a.setMainId(mainId);
			l140s07a.setSeqNum(seqNum);
			l140s07a.setItem("");
			l140s07a.setBefText("");
			l140s07a.setAftText("");
			l140s07a.setCreator(user.getUserId());
			l140s07a.setCreateTime(CapDate.getCurrentTimestamp());
			lmsService.save(l140s07a);
			if (l140s07a != null) {
				result = DataParse.toResult(l140s07a);
			}
		}

		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getFileOid(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String fieldId = Util.trim(params.getString("fieldId"));
		String fileOid = "";

		List<DocFile> docFiles = docFileService.findByIDAndName(mainId,
				fieldId, null);
		for (DocFile docFile : docFiles) {
			fileOid = docFile.getOid();
		}
		result.set("fileOid", fileOid);
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult isOpenPaperlessSigningFunction(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("isOpenPaperlessSigningFunction",
				this.lmsService.isOpenPaperlessSigningFunction());
		return result;
	}

	/**
	 * J-111-0551_05097_B1006 Web
	 * e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult chkApproveUnestablshed(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String errMsg = "";
		String approveUnestablshExlTextarea = Util.trim(params
				.getString("approveUnestablshExlTextarea"));

		String[] queryIdArr = null;
		if (Util.isEmpty(approveUnestablshExlTextarea)) {

			errMsg = "查詢ID不得為空白";
			result.set("errMsg", errMsg);
			return result;
		}

		if (!CapString.checkRegularMatch(approveUnestablshExlTextarea,
				"[a-zA-Z0-9,]")) {
			errMsg = "查詢ID只能輸入數字、英文字與逗號";
			result.set("errMsg", errMsg);
			return result;
		}

		approveUnestablshExlTextarea = StringUtils.replace(
				approveUnestablshExlTextarea, " ", "");
		approveUnestablshExlTextarea = StringUtils.replace(
				approveUnestablshExlTextarea, "\n", "");

		queryIdArr = approveUnestablshExlTextarea.split(",");

		Map<String, String> queryIdMap = new HashMap<String, String>();
		StringBuffer notExistCustId = new StringBuffer("");
		for (String queryId : queryIdArr) {
			String custId = Util.getLeftStr(queryId,
					StringUtils.length(queryId) - 1);
			String dupNo = Util.getRightStr(queryId, 1);
			// 檢查0024有無借款人ID
			Map<String, Object> map_cust = customerService.findByIdDupNo(
					custId, dupNo);
			if (map_cust != null && !map_cust.isEmpty()) {
				queryIdMap.put(custId + "-" + dupNo,
						Util.trim(MapUtils.getString(map_cust, "CNAME")));
			} else {
				notExistCustId.append(Util.isEmpty(notExistCustId) ? "" : "、");
				notExistCustId.append(custId + "-" + dupNo);
			}
		}

		if (!Util.isEmpty(notExistCustId)) {

			errMsg = "下列查詢之ID不存在於0024，請修正後再查詢(統編-重複序號)：<br>"
					+ notExistCustId.toString();
			result.set("errMsg", errMsg);
			return result;
		}

		result.set("errMsg", errMsg);
		result.set("approveUnestablshExlTextarea", approveUnestablshExlTextarea);
		return result;
	}

}
