/* 
 * L140MM2ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140MM3A;

/** 都更危老註記維護主檔 **/
public interface L140MM3ADao extends IGenericDao<L140MM3A> {

	L140MM3A findByOid(String oid);
	
	List<L140MM3A> findByMainId(String mainId);

	L140MM3A findByUniqueKey(String mainId);
}