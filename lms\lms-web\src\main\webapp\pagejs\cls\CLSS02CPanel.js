var pageAction = {
    handler: 'cls1141formhandler',
    grid: null,
    custGrid: null,
    build: function(obj){
    	//ilog.debug("obj.show_s01r="+(obj.show_s01r||''));
        //
    	var gridview_colModel = [
    	       {
	            name: 'oid',
	            hidden: true //是否隱藏
	        }, {
	            name: 'mainId',
	            hidden: true //是否隱藏
	        }, {
	            name: 'custId', //身分證統編
	            hidden: true //是否隱藏
	        }, {
	            name: 'dupNo', //身分證統編重複碼
	            hidden: true //是否隱藏
	        }, {
	            name: 'custPos', //相關身份
	            hidden: true //是否隱藏
	        }, {
	            colHeader: "&nbsp",
	            align: "left",
	            width: 10, //設定寬度
	            sortable: true, //是否允許排序
	            name: 'keyMan'
	        }, {
	            colHeader: i18n.clss02c["C120M01A.custId"], //身分證統編
	            align: "left",
	            width: 100, //設定寬度
	            sortable: true, //是否允許排序
	            name: 'custNumber', //col.id
	            formatter: 'click',
	            onclick: pageAction.openDocByGrid
	        }, {
	            colHeader: i18n.clss02c["C120M01A.custName"], //借款人姓名
	            align: "left",
	            width: 100, //設定寬度
	            sortable: true, //是否允許排序
	            name: 'custName' //col.id
	        }, {
	            colHeader: i18n.clss02c["C120M01A.markModel"], //評等類型
	            hidden: true,
	            name: 'markModel' //col.id
	        }, {
	            colHeader: i18n.clss02c["markModel.C101S01G.grade3"], //房貸最終評等
	            align: "center",
	            width: 60, //設定寬度
	            sortable: false, //是否允許排序
	            name: 'c120s01g.grade3' //col.id
	        }, {
	            colHeader: i18n.clss02c["markModel.C101S01Q.grade3"], //非房貸最終評等
	            align: "center",
	            width: 60, //設定寬度
	            sortable: false, //是否允許排序
	            name: 'c120s01q.grade3' //col.id
	        }
    	];
    	
    	if(obj.show_s01r=="Y"){
    		gridview_colModel.push({
                colHeader: i18n.clss02c["markModel.C101S01R.grade3"], //專案信貸(非團體)最終評等
                align: "center",
                width: 60, //設定寬度
                sortable: false, //是否允許排序
                name: 'c120s01r.grade3' //col.id    	
            });
    	}
    	if(true){
    		gridview_colModel.push({
    			colHeader: i18n.clss02c["C120M01A.rmk"], //備註
                align: "center",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'rmk' //col.id	
            });
    		gridview_colModel.push({
    			colHeader: i18n.clss02c["varVer.C101S01G"],
                align: "left",
                width: 20,
                sortable: false, //是否允許排序
                name: 'c120s01g.varVer' //col.id
            });
    		gridview_colModel.push({
    			colHeader: i18n.clss02c["varVer.C101S01Q"],
                align: "left",
                width: 20,
                sortable: false, //是否允許排序
                name: 'c120s01q.varVer' //col.id            
            });    		
    	}
    	
    	if(obj.show_s01r=="Y"){
    		gridview_colModel.push({
    			colHeader: i18n.clss02c["varVer.C101S01R"],
                align: "left",
                width: 20,
                sortable: false, //是否允許排序
                name: 'c120s01r.varVer' //col.id           
            });    
    	}
        pageAction.grid = $("#gridview").iGrid({
            //localFirst: true,
            handler: 'cls1131gridhandler',
            height: 250,
            action: "queryC120m01a", //
            rowNum: 15,
            sortname: "keyMan|custShowSeqNum|custId|dupNo",
            sortorder: 'desc|asc|asc|asc',
            rownumbers: true,
            colModel: gridview_colModel,
            ondblClickRow: function(rowid){
                var data = pageAction.grid.getRowData(rowid);
                pageAction.openDoc(data);
            }
        });
        //=====================================
        //借保人引進清單Grid
        var ClsCustGrid_colModel = [
	  	       {
                name: 'oid',
                hidden: true //是否隱藏
            }, {
                name: 'mainId',
                hidden: true //是否隱藏
            }, {
                name: 'custId', //身分證統編
                hidden: true //是否隱藏
            }, {
                name: 'dupNo', //身分證統編重複碼
                hidden: true //是否隱藏
            }, {
                colHeader: i18n.clss02c["C120M01A.custId"], //身分證統編
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'custNumber' //col.id
            }, {
                colHeader: i18n.clss02c["C120M01A.custName"], //借款人姓名
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'custName' //col.id
            }, {
                colHeader: i18n.clss02c["C120M01A.markModel"], //評等類型
                hidden: true,
                name: 'markModel' //col.id
            }, {
                colHeader: i18n.clss02c["markModel.C101S01G.grade3"], //房貸最終評等
                align: "center",
                width: 60, //設定寬度
                sortable: false, //是否允許排序
                name: 'c101s01g.grade3' //col.id
            }, {
                colHeader: i18n.clss02c["markModel.C101S01Q.grade3"], //非房貸最終評等
                align: "center",
                width: 60, //設定寬度
                sortable: false, //是否允許排序
                name: 'c101s01q.grade3' //col.id
            }
	  	];
	  	
	  	if(obj.show_s01r=="Y"){
	  		ClsCustGrid_colModel.push({
	  			colHeader: i18n.clss02c["markModel.C101S01R.grade3"], //專案信貸(非團體)最終評等
                align: "center",
                width: 60, //設定寬度
                sortable: false, //是否允許排序
                name: 'c101s01r.grade3' //col.id	
	        });
	  	}
             
	  	if(true){
	  		ClsCustGrid_colModel.push({
	  			colHeader: i18n.clss02c["C120M01A.rmk"], //備註
                align: "center",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'rmk' //col.id
	        });
	  		ClsCustGrid_colModel.push({
	  			colHeader: i18n.clss02c["varVer.C101S01G"],
                align: "left",
                width: 20,
                sortable: false, //是否允許排序
                name: 'c101s01g.varVer' //col.id
	        });
	  		ClsCustGrid_colModel.push({
	  			colHeader: i18n.clss02c["varVer.C101S01Q"],
                align: "left",
                width: 20,
                sortable: false, //是否允許排序
                name: 'c101s01q.varVer' //col.id
	        });
	  	}
	  	
	  	if(obj.show_s01r=="Y"){
	  		ClsCustGrid_colModel.push({
	  			colHeader: i18n.clss02c["varVer.C101S01R"],
                align: "left",
                width: 20,
                sortable: false, //是否允許排序
                name: 'c101s01r.varVer' //col.id
	        });
	  	}
	  	
        pageAction.custGrid = $("#ClsCustGrid").iGrid({
            localFirst: true,
            handler: 'cls1131gridhandler',
            height: 210,
            action: "importList",
            multiselect: true,
            hideMultiselect: false,
            rowNum: 15,
            sortname: "custId",
            sortorder: 'asc',
            rownumbers: true,
            colModel: ClsCustGrid_colModel
        });
        
        if($("#buttonPanel").find("#btnSave").is("button")){
    		var codetype_v = $("select#input_ntCode").attr("data-codetype") ||'';
    		if(codetype_v){
    			$.ajax({
    				type : "POST", handler : "lms1015m01formhandler", async: false,//用「同步」的方式
    				data : {
    					formAction : "codeTypeWithOrder" ,
    					key : codetype_v
    				},
    				success:function(obj){
    					var chooseItem1 = $("select#input_ntCode");
    					
    					var _addSpace = false;
    					if(chooseItem1.attr("space")=="true"){
    						_addSpace = true;	
    					}
    					
    					$.each(obj.itemOrder, function(idx, c_val) {
    						var currobj = {};
    						var c_desc = obj.item[c_val];
    						currobj[c_val] = c_desc;
    				
    						//select
    						chooseItem1.setItems({ item: currobj, format: "{value}-{key}", clear:false, space: (_addSpace?(idx==0):false) });
    					});
    				}
    			});
    		}
    	}
        
        //build button 
        //引進借款人
        $('#importLendCollateral').click(function(){
            if (obj.docCode == "5") {
				//MegaApi.showErrorMessage(i18n.def["confirmTitle"], i18n.def["grid.selrow"]);
				
				// C120M01A.custNameNote=為達到一份簽報書可同時簽報多筆案件，<br/>團貸戶的戶名請勿包含專案名稱，<br/>因在列印時已會將其專案名稱組入戶名中呈現。
				CommonAPI.confirmMessage(i18n.clss02c["C120M01A.custNameNote"], function(b){
                   if (b) {
                           pageAction.openSelectCustType();
                          }
                   });                
            } else {
                pageAction.openClsCustList();
            }
            
        });
        //新增擔保品提供人
        $('#addLendCollateral').click(function(){
            pageAction.addClsCust();
        });
        
        //新增擔保品提供人
        $('#setKeyManBt').click(function(){
            pageAction.setKeyManBt();
        });
        
        //調整順序
        $('#setCustShowSeqNum').click(function(){
            pageAction.setCustShowSeqNum();
        });
    },
    
    openDocByGrid: function(col, set, data){
        pageAction.openDoc(data);
    },
    /**
     * 開啟文件
     */
    openDoc: function(data){
        //當為擔保品提供人開起的視窗不一樣
        if (data.custPos == 'S') {
            pageAction.openCustPosnS(data);
        } else {
            if ($('#CLS1131S01ThickBox').size() == 0) {
                $('#ClsCustInfo').load(webroot + '/app/cls/cls1131s01', function(){
                    CLS1131S01.handler = pageAction.handler; //set handler
                    CLS1131S01.readOnly = true;
                    data.isC120M01A = true;
                    CLS1131S01.open(data);
                });
            } else {
                data.isC120M01A = true;
                CLS1131S01.open(data);
            }
        }
        
        
        
    },
    /**
     * 取得資料表之選擇列
     */
    getRowData: function(){
        var row = pageAction.grid.getGridParam('selrow');
        var data;
        if (row) {
            data = pageAction.grid.getRowData(row);
        } else {
            MegaApi.showErrorMessage(i18n.def["confirmTitle"], i18n.def["grid.selrow"]);
        }
        return data;
    },
    /**
     * 重整資料表
     */
    reloadGrid: function(data){
        if (data) {
            pageAction.grid.jqGrid("setGridParam", {
                postData: data,
                page: 1,
                search: true
            }).trigger("reloadGrid");
        } else {
            pageAction.grid.trigger('reloadGrid');
        }
    },
    /**
     * 當為團貸案時出現
     */
    openSelectCustType: function(){
        $("#selectCustTypeBox").thickbox({
            title: i18n.cls1141m01["CLS1141.100"],//CLS1141.100=借款人引進類別
            width: 200,
            height: 150,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var value = $("#selectCustTypeDiv").find("[name=selectCustType]:checked").val();
                    if (value == "") {
                        //grid_selector=請選擇資料
                        return API.showMessage("grid_selector");
                    }
                    $.thickbox.close();
                    if (value == "1") {
                        pageAction.openClsCustList();
                    } else {
                        AddCustAction.open({
                            handler: pageAction.handler,
                            action: 'addParenetCust',
                            dupNoInput: true,
                            doNewUser: true,
                            fn: function(obj){
                                pageAction.setShowTitleCust(obj);
                                
                                if(obj.has_ntCode=="Y"){
                            		//出現「執行成功」
                            	}else{
                            		//關掉 AddCustAction 的 thickbox
//                            		$.thickbox.close();
                            		//===============
                            		pageAction.openNtCodeBox(obj);
                            	}
                            }
                        });
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 借保人清單
     */
    openClsCustList: function(){
        pageAction.custGrid.reload({
            findId: ""
        });
        //清空篩選欄位
        $("#findId").val("");
        $('#ClsCustList').thickbox({
            title: i18n.clss02c['button.importLendCollateral'] || '',
            width: 700,
            height: 380,
            modal: true,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                'sure': function(){
                    var datas = pageAction.getCustRowData();
                    if (datas) {
                        var rows = [];
                        for (var name in datas) {
                            var data = datas[name];
                            rows.push(data.mainId);
                        }
                        $.ajax({
                        	//檢查所選的ID評等模型是否有正確計算
                        	handler: pageAction.handler,
                            action: 'checkClsRating',
                            formId: 'empty', //test
                            data: {
                                rows: rows
                            },
                            success: function(response){
                                if (response.notSame) {
                                	MegaApi.showErrorMessage(i18n.def["confirmTitle"], response.changeMsg);
                                } else {
                                	 $.ajax({
                                         handler: pageAction.handler,
                                         action: 'importLendCollateral',
                                         formId: 'empty', //test
                                         data: {
                                             rows: rows
                                         },
                                         success: function(response){
                                             $.thickbox.close();
                                             pageAction.reloadGrid();
                                             check_clsRatingModel();// in CLS1141M01Page.js【模型過版時，檢核是否需重引】
                                             if (response.needMainCust) {
                                                 pageAction.setMasterCust();
                                             } else {
                                                 MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["runSuccess"]);
                                             }
                                             if (response.bfRejCaseMsg) {
                                                 MegaApi.showPopMessage(i18n.def["confirmTitle"], response.bfRejCaseMsg);
                                             }
                                             if (response.c900m01Msg) {
                                                 MegaApi.showPopMessage(i18n.def["confirmTitle"], response.c900m01Msg);
                                             }
                                         }
                                     });
                                }
                            }
                        });
                    }
                },
                'cancel': function(){
                    $.thickbox.close();
                }
            }
        });
        pageAction.reloadCustGrid();
    },
    /**
     * 建立主要借款人
     */
    setMasterCust: function(){
        //請選擇主要借款人
        $.ajax({
            handler: pageAction.handler,
            formId: "empty",
            action: "queryAllCust",
            data: {},
            success: function(obj){
				var div = $("#ClsSelectMainCustBox");
				div.find("#ClsSelectMainCustList").setItems({
                    space: false,
                    item: obj
                });
                $("#ClsSelectMainCustBox").thickbox({
                    title: i18n.clss02c["title.setMainCust"],//設定主要借款人
                    width: 300,
                    height: 100,
                    modal: true,
                    align: "center",
                    valign: "bottom",
                    readOnly: _openerLockDoc == "1",
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                            $.ajax({
                                handler: pageAction.handler,
                                formId: "empty",
                                action: "setMainCust",
                                data: {
                                    custOid: $("#ClsSelectMainCustList").val()
                                },
                                success: function(obj){
                                    pageAction.setShowTitleCust(obj);
                                    API.triggerOpener("gridview", "reloadGrid");
                                    pageAction.reloadGrid();
                                    $.thickbox.close();
                                }
                            });
                            
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        });
    },
    /**
     * 設定上方顯示資訊
     * @param {Object} obj
     */
    setShowTitleCust: function(obj){
        $("#showCustId").text(obj.showCustId);
        $("#showTypCd").text(obj.showTypCd);
        // 異常通報引進集團名稱時用到，Miller added at 2013/01/17
        $("#mainCustId").val(obj.mainCustId);
        $("#mainDupNo").val(obj.mainDupNo);
    },
    
    /**
     * 取得資料表之選擇列(單筆)
     */
    getSingleCustRowData: function(){
        var row = pageAction.CustGrid.getGridParam('selrow');
        var data;
        if (row) {
            data = pageAction.CustGrid.getRowData(row);
        } else {
            MegaApi.showErrorMessage(i18n.def["confirmTitle"], i18n.def["grid.selrow"]);
        }
        return data;
    },
    /**
     * 取得資料表之選擇列(多筆)
     */
    getCustRowData: function(){
        var datas = pageAction.custGrid.getSelRowDatas();
        if (!datas) {
            //MegaApi.showErrorMessage(i18n.def["confirmTitle"],i18n.def["grid.selrow"]);
            MegaApi.showErrorMessage(i18n.def["confirmTitle"], i18n.def["action_005"]);
        }
        return datas;
    },
    /**
     * 重整借保人資料表
     */
    reloadCustGrid: function(data){
        if (data) {
            pageAction.custGrid.jqGrid("setGridParam", {
                postData: data,
                page: 1,
                search: true
            }).trigger("reloadGrid");
        } else {
            pageAction.custGrid.trigger('reloadGrid');
        }
    },
    /**
     * 新增擔保品提供人
     */
    addClsCust: function(){
        AddCustAction.open({
            handler: pageAction.handler,
            action: 'addCust',
            doNewUser: true,
            fn: function(obj){
            	if(obj.has_ntCode=="Y"){
            		//出現「執行成功」
            	}else{
            		//關掉 AddCustAction 的 thickbox
            		$.thickbox.close();
            		//===============
            		pageAction.openNtCodeBox(obj);
            	}
            }
        });
    },
    /**
     * 設定主要借款人
     */
    setKeyManBt: function(){
        var $grid = pageAction.grid;
        var rowData = $grid.getSingleData();
        if (rowData) {
            $.ajax({
                handler: pageAction.handler,
                formId: 'empty',
                action: "setKeyMan",
                data: {
                    custOid: rowData.oid
                },
                success: function(obj){
                    pageAction.setShowTitleCust(obj);
                    pageAction.reloadGrid();
                    API.triggerOpener("gridview", "reloadGrid");
                }
            });
        }
        
    },
    /**
     * 調整順序
     */
    setCustShowSeqNum: function(){
    	$("#_div_custShowSeqNum_content").empty();
    	
    	$.ajax({ type: "POST", handler: pageAction.handler
			, action: 'load_custShowSeqNum',formId: 'empty',data: {}
			, success: function(json_load_uiSeq){
				if(json_load_uiSeq.c120m01a_cnt > 0 ){

					var dyna = [];					
					$.each( json_load_uiSeq.c120m01a_list.arr, function(idx, item){						
						dyna.push("<tr>");						
																				
							dyna.push("<td>"+item.custId + " " +item.dupNo+"</td>");
							dyna.push("<td>"+item.custName+"</td>");
							dyna.push("<td>");
							dyna.push("<input type='hidden' id='c120cust_oid'   name='c120cust_oid'   value='"+item.oid+"'>");
							dyna.push("<input type='text'   id='c120cust_uiSeq' name='c120cust_uiSeq' value='"+item.custShowSeqNum+"' class='numeric' maxlength='3' integer='3' fraction='0' size='3'>");
							dyna.push("</td>");
													
						dyna.push("</tr>");
					});
					$("#_div_custShowSeqNum_content").html( dyna.join("\n") );
					
			    	$("#_div_custShowSeqNum").thickbox({
			            title: i18n.clss02c["button.chgCustShowSeqNum"],
			            width: 600, height: 300, modal: true, i18n: i18n.def,
			            buttons: API.createJSON([{
			                key: i18n.def['saveData'],
			                value: function(){
			                	$.ajax({ type: "POST", handler: pageAction.handler
			            			, data: $.extend( 
			            				{'formAction': 'save_custShowSeqNum' }
			            				, $("#frm_custShowSeqNum").serializeData() 
			            			)
			            			, success: function(json_save_uiSeq){			            				
			            				pageAction.reloadGrid();
			            				
			            				$.thickbox.close();			            				
			            				API.showMessage(i18n.def.runSuccess);
			            			}
			            		});
			                }
			            }, {
			                key: i18n.def['close'],
			                value: function(){
			                    $.thickbox.close();
			                }
			            }])
			        });
				}else{
					API.showMessage(i18n.def.noData);
				}
			}
    	});
    	
    
    },
    /**
     * 開啟擔保品提供人視窗
     * @param {Object} data 借款人資料
     */
    openCustPosnS: function(rowdata){
    	$.ajax({
            handler: pageAction.handler,
            action: 'queryC120S01A_CustPosS',
            data: rowdata,
            success: function(jsonData){
            	$("#openCustPosnSBoxDiv").injectData($.extend(rowdata, jsonData));
                $("#openCustPosnSBox").thickbox({
                    title: i18n.cls1141m01["L1205G.grid8"],
                    width: 500,
                    height: 170,
                    modal: true,
                    readOnly: _openerLockDoc == "1" || CLSAction.isReadOnly(),
                    i18n: i18n.def,
                    buttons: {
                        "del": function(){
                            $.ajax({
                                handler: pageAction.handler,
                                formId: 'empty',
                                action: "deleteCust",
                                data: rowdata,
                                success: function(obj){
                                    pageAction.reloadGrid();
                                    $.thickbox.close();
                                }
                            });
                            
                        },
                        "close": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        });
        
    },
    /**
     * 開啟擔保品提供人視窗
     * @param {Object} data 借款人資料
     */
    openNtCodeBox: function(jsonObj){
    	$("#openNtCodeBox").thickbox({
            title: i18n.def.grid_selector,
            width: 400,
            height: 170,
            modal: true,
            readOnly: _openerLockDoc == "1" || CLSAction.isReadOnly(),
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $.ajax({
                        handler: 'cls1141formhandler',
                        formId: 'empty',
                        action: "updateNtCode",
                        data: $.extend(jsonObj, {'ntCode': $("select#input_ntCode").val() }),
                        success: function(json_updateNtCode){                                    
                            $.thickbox.close();
                        }
                    });                    
                }
            }
        });        
    }
}

// initDfd 定義在  CLS1141M01Page.js
initDfd.done(function(obj){
    pageAction.build(obj);
    $("#findIdBt").click(function(){
        pageAction.custGrid.reload({
            findId: $("#findId").val()
        });
    });
	
	if(obj.is_L120M01A_contains_brmpJsonData == 'Y' && obj.simplifyFlag == 'E'){
		$("#importLendCollateral").hide();
		$("#addLendCollateral").hide();
		$("#setKeyManBt").hide();
		$("#setCustShowSeqNum").hide();
	}
});
