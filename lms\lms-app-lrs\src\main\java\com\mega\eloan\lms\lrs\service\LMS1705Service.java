/* 
 *  LMS1705Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01C;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01G;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.sso.userdetails.MegaSSOUserDetails;

public interface LMS1705Service extends ICapService {

	/**
	 * ScustId, dupNo, branch 找此筆資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @param 海外編製中
	 * @return
	 */
	boolean findL170M01AByUnkey(String custId, String dupNo, String branch,
			String docStatus, String ctlType);

	/**
	 * 取得L170M01G
	 * 
	 * @param mainId
	 * @param branchType
	 *            單位
	 * @param staffJob
	 *            代號
	 * @return
	 */
	L170M01G findL170m01gByBranchTypeStaffJob(String mainId, String branchType,
			String staffJob);

	/**
	 * 利用MainId做搜尋
	 * 
	 * @param <T>
	 * @param clazz
	 * @param mainId
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	<T extends GenericBean> T findModelByMainId(Class clazz, String mainId);

	/**
	 * 利用MainId做搜尋 <L170M01D>許多筆
	 * 
	 * @param mainId
	 * @return
	 */
	List<L170M01D> findL170m01dByMainId(String mainId);

	/**
	 * 搜尋 <L170M01D>特定單一筆
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param itemNo
	 * @return
	 */
	L170M01D findL170m01dByMainId(String mainId, String custId, String dupNo,
			String itemNo);

	/**
	 * 利用CrdType(DB=DBU大型企業,DL=DBU中小型企業 ,OU=0BU )做搜尋 <L170M01E>單一筆
	 * 
	 * @param i
	 * @param dupNo
	 * @param custId
	 * @return
	 */
	List<L170M01E> findL170m01eByCrdType(String mainId, String custId,
			String dupNo, int i, String timeFlag);

	/**
	 * 利用MainId做搜尋 <L170M01E>許多筆
	 * 
	 * @param mainId
	 * @return
	 */
	List<L170M01E> findL170m01eByMainId(String mainId, String timeFlag);

	/**
	 * 利用MainId做搜尋 <L170M01E>許多筆
	 * 
	 * @param mainId
	 * @return
	 */
	List<L170M01G> findL170m01gByMainId(String mainId);

	/**
	 * 找 信用評等資料 ( crdType ='DB',''DL,'OU')
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	boolean findL170m01eFindCrdType(String mainId, String custId, String dupNo,
			String timeFlag);

	/**
	 * 儲存(篩選 全部的 model)
	 * 
	 * @param entity
	 */
	void save(GenericBean... entity);

	/**
	 * 儲存好幾筆資料(L170M01B)
	 * 
	 * @param list
	 */
	void saveL170m01bList(List<L170M01B> list);

	/**
	 * 儲存好幾筆資料(L170M01C)
	 * 
	 * @param list
	 */
	public void saveL170m01cList(List<L170M01C> list);

	/**
	 * 儲存好幾筆資料(L170M01D)
	 * 
	 * @param list
	 */
	void saveL170m01dList(List<L170M01D> list);

	/**
	 * 刪除L170M01A(好幾筆)
	 * 
	 * @param oids
	 */
	boolean deleteL170m01aList(String[] oids);

	/**
	 * 刪除L170m01bListByMainId 不包含lnDateDate為null (下SQL)
	 * 
	 * @param mainId
	 * @return
	 */
	boolean deleteL170m01bListNotLnDataDate(String mainId);

	/**
	 * 刪除L170m01dListByMainId (下SQL)
	 * 
	 * @param mainId
	 */
	void deleteL170m01dList(String mainId);

	/**
	 * 刪除L170m01dListByMainId (下SQL)
	 * 
	 * @param mainId
	 */
	void deleteL170m01eList(String mainId);

	/**
	 * 刪除ByOid (篩選 全部的 model)
	 * 
	 * @param clazz
	 * @param oid
	 */
	void delete(Class<?> clazz, String oid);

	/**
	 * 刪除ByMainId(篩選 全部的 model)
	 * 
	 * @param clazz
	 * @param oid
	 */
	void deleteByMainId(Class<?> clazz, String mainId);

	/**
	 * 搜尋ByOid(篩選model)
	 * 
	 * @param <T>
	 * @param clazz
	 * @param oid
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	<T extends GenericBean> T findModelByOid(Class clazz, String oid);

	/**
	 * 搜尋L170M01A有幾筆
	 * 
	 * @param addSearchModeParameters
	 * @return
	 */
	List<L170M01A> findL170m01aList(ISearch addSearchModeParameters);

	/**
	 * 搜尋L170M01BListByMainId
	 * 
	 * @param mainId
	 * @return
	 */
	List<L170M01B> findL170m01bList(String mainId);

	/**
	 * flowAction
	 * 
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */
	public void flowAction(String oid, MegaSSOUserDetails user,
			HashMap<String, Object> data);

	/**
	 * 啟動流程
	 * 
	 * @param oid
	 *            oid(instid)
	 * @param flow_code
	 *            流程代號
	 */
	public void startFlow(String oid, String flow_code);

	/**
	 * 搜尋類別裡有幾筆資料撈出
	 * 
	 * @param clazz
	 * @param search
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * 搜尋覆審控制檔 MIS.ELF412
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> findMisByCustIdDupNoBranch(String custId, String dupNo,
			String branch);

	/**
	 * 更新覆審控制檔ELF412
	 * 
	 * @param mainId
	 * @param dupNo
	 * @param custId
	 * @param branch
	 * @return
	 */
	boolean updateElf412(L170M01A l170m01a, String mainId, String dupNo,
			String custId, String branch);

	/**
	 * 引進信用評等資料(引進信用評等用 )
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<Map<String, Object>> findElf338nByType(String custId, String dupNo,
			String ovUnitNo, String type1, String type2, String type3,
			String type4, String type5, String type6);

	/**
	 * 引進信用評等資料(引進信用評等用 )
	 * 
	 * @param custId
	 * @param dupNo
	 * @param type
	 *            有無財報迄日
	 * @return
	 */
	List<Map<String, Object>> findElfmow1ByType(String custId, String dupNo,
			String types);

	/**
	 * 新增一筆資料至L180M01B明細
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	void insertUpdate180M01B(String mainId, String custId, String dupNo,
			String branch, Date retrialDate, L170M01A l170m01a,
			L180M01A l180m01a);

	/**
	 * 新增一筆資料至L180M01C
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	void insert180M01C(String mainId, String custId, String dupNo,
			String branch, String mainIdMax, String ctlType);

	/**
	 * 找CES.F101A01A , CES.F101M01A 資料
	 * 
	 * @param brNo
	 * @param custId
	 * @param dupNo
	 * @param search
	 * @return
	 */
	public List<Map<String, Object>> getCesf101(String brNo, String custId,
			String dupNo);

	/**
	 * Find ratioNo 所對應的 ratio (XX比率)
	 * 
	 * @param list
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	L170M01C findF101S01BBymainId(String mainId, String[] list, String custId,
			String dupNo, String[] ratioNolist, L170M01C l170m01c);

	/**
	 * Find CES.F101S01A By mainId (營業收入,營業利益,稅前損益)
	 * 
	 * @param mainId
	 */
	L170M01C findF101s01ABymainId(String mainId, String[] list, String custId,
			String duopNo, L170M01C l170m01c, L170M01A l170m01a);

	/**
	 * Fin(引進主要授信戶) FROM DWELF412OVS
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	Map finLms412ByCustIdAndDupNo(String custId, String dupNo, String brNo);

	/**
	 * Find (保證人) FROM MIS.Ellngtee
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	List<Map<String, Object>> findEllngteeByCustIdAndDupNo(String custId,
			String dupNo, String cntrNo);

	/**
	 * 取得保證人
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNoList
	 * @return
	 */
	public Object[] getGuarantor(String custId, String dupNo, String mainId);

	/**
	 * Find (負責人) FROM MIS.ELCUS25 MIS.BSTBL LMS.CUSTDATA
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	Map<String, Object> findCharminByCustIdDupNoBranch(String custId,
			String dupNo, String brNo);

	/**
	 * 取得grid頁面所需資料
	 * 
	 * @param mainId
	 *            mainId
	 * @param search
	 *            search
	 * @return Page<Map<String, Object>>
	 * @throws CapException
	 *             CapException
	 */
	Page<Map<String, Object>> getBorrows(String mainId, ISearch search)
			throws CapException;

	/**
	 * 尋找覆審名單主檔裡資料
	 * 
	 * @param branch
	 * @return
	 */
	List<L180M01A> findL180m01a(String branch, Date retrialDate);

	/**
	 * tempSave (L170M01D)
	 * 
	 * @param models
	 */
	void saveL170m01dList2(List<L170M01D> models);

	/**
	 * (1)檢查信用評等是否有資料才可以更新覆審控制檔,(2)檢查L170M01B是否有授信資料才可以更新覆審控制檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	int checkDataBeforUpdate412(String mainId, String custId, String dupNo);

	/**
	 * MIS.ELF338N (評等等級)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param crdtype
	 * @return
	 */
	String findCrtdttblByCrdtype(String custId, String dupNo, String crdtype,
			String brNo);

	/**
	 * <pre>
	 * 信評表類別(ELF338N) 
	 * CA=泰國GroupA , CB=泰國GroupB,CK=自訂,
	 * CS=消金評等,NM=Moody,NS=S&P,NF=Fitch,NC=中華信評
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 * </pre>
	 */
	List<Map<String, Object>> findcrdTypeByCustId(String custId, String dupNo,
			String brNo);

	/**
	 * 前次最新覆審報告表
	 * 
	 * @param custId
	 * @param dupNo
	 * @param ownBrId
	 * @param docStatus
	 * @return
	 */
	// TODO:未完成
	L170M01A findModelByDocStatus(String custId, String dupNo, String ownBrId,
			String docStatus);

	/**
	 * Grig 顯示欄位
	 * 
	 * @param docStatus
	 *            文件狀態
	 * @param retrialDate1
	 *            起日
	 * @param retrialDate2
	 *            迄日
	 * @param custId
	 *            客戶統編
	 * @param brNo
	 *            分行別
	 * @param search
	 *            order by 條件
	 * @return
	 */
	Page<Map<String, Object>> findL170m01AJoin(String docStatus,
			String retrialDate1, String retrialDate2, String custId,
			String custName, String brNo, ISearch search);

	/**
	 * 取得擔保品資料
	 * 
	 * @param l170m01a
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> getCMSData(L170M01A l170m01a, String cntrNo);

	/**
	 * J-108-0260 海外覆審檢視表 取得該案複審內容版本
	 */
	public String getReviewType(L170M01A l170m01a);

	/**
	 * J-108-0260 海外覆審檢視表 取得檢視表項目
	 */
	public HashMap<String, String> getChkList(String reviewType);
}
