<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
	<body>
		<wicket:extend>
			<style type="text/css">
				.linkDocFile{color:#5291EF;text-decoration:underline;}
			</style>
			<script type="text/javascript" src="pagejs/fms/LMS8100M01Page.js?20230928"></script>
			
			<div class="button-menu funcContainer" id="buttonPanel">
				<!--編製中 -->
				<wicket:enclosure><span wicket:id="_btnDOC_EDITING" />
	        		<button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04"/>
	        			<wicket:message key="button.save"><!--儲存--></wicket:message>
	        		</button>
					<button id="btnSend" >
	        			<span class="ui-icon ui-icon-jcs-02"/>
	        			<wicket:message key="button.send" ><!--呈主管覆核--></wicket:message>
	        		</button>
		        </wicket:enclosure>		
				
				<!--待覆核 -->
				<wicket:enclosure><span wicket:id="_btnWAIT_APPROVE" />
	        		<button id="btnCheck" >
	        			<span class="ui-icon ui-icon-jcs-106"/>
	        			<wicket:message key="button.check" ><!--覆核--></wicket:message>
	        		</button>
		        </wicket:enclosure>

				<button id="btnPrint" class="forview">
                	<span class="ui-icon ui-icon-jcs-03"></span>
					<wicket:message key="button.print"><!--列印--></wicket:message>
				</button>
                <button id="btnExit"  class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					<wicket:message key="button.exit"><!--離開--></wicket:message>
				</button>
			</div>
			
			<div class="tit2 color-black">
				<wicket:message key="title"></wicket:message> <span class="color-blue" id="titleInfo"/>
			</div>
			
			<form id="mainPanel">
				<input type="text" class="hide" id="mainId" name="mainId"/>
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td width='53%' style="border-top:hidden;border-left:hidden;border-right:hidden;"></td>
						<td style="border-top:hidden;border-left:hidden;border-right:hidden;"></td>
					</tr>
					<tr>
						<td class="hd2">
							<button type="button" id="reImpl"><wicket:message key="btn.reImpl">重新引入</wicket:message></button>
							<wicket:message key="L300M01A.assDate">考評日期</wicket:message>：<span class="field text-red field" id="assDate" name="assDate"></span>
						</td>
						<td class="hd2" colspan="4">明細檔：<div id="divXlsFile"></div></td>
					</tr>
				</table>
				<p id="paForm"></p>
				<!--
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr class="hd2">
						<td width='53%'><wicket:message key="L300S01A.itemName">考評項目</wicket:message></td>
						<td width='10%'><wicket:message key="L300S01A.itemCnt">項次</wicket:message><br/>(1)</td>
						<td width='7%'><wicket:message key="L300S01A.itemScore">扣標準</wicket:message><br/>(2)</td>
						<td width='10%'><wicket:message key="L300S01A.itemAll">扣分</wicket:message><br/>(3)=(1)×(2)</td>
						<td width='30%'><wicket:message key="L300S01A.itemDscr">備註</wicket:message></td>
					</tr>
					<tr>
						<td><wicket:message key="L300S01A.paItem01"></wicket:message></td>
						<td>
							<input type="text" id="paItem01_itemType" name="paItem01_itemType" value="YN" style="display:none"/>
							<input type="text" id="paItem01_itemCnt" name="paItem01_itemCnt" class="max mathit numeric" positiveonly="true" size="3" integer="3" fraction="0" value="0"/>戶
						</td>
						<td><input type="text" name="paItem01_itemScore" id="paItem01_itemScore" readonly="true" size="1"/>分</td>
						<td><input type="text" name="paItem01_itemAll" id="paItem01_itemAll" class="totalit" readonly="true" size="1"/>分</td>
						<td><textarea id="paItem01_itemDscr" name="paItem01_itemDscr" cols="25" rows="2" class="max txt_mult" maxlengthC="3400"></textarea></td>
					</tr>
					<tr>
						<td><wicket:message key="L300S01A.paItem02"></wicket:message></td>
						<td>
							<input type="text" id="paItem02_itemType" name="paItem02_itemType" value="YN" style="display:none"/>
							<input type="text" id="paItem02_itemCnt" name="paItem02_itemCnt" class="max mathit numeric" positiveonly="true" size="3" integer="3" fraction="0" value="0"/>戶
						</td>
						<td><input type="text" name="paItem02_itemScore" id="paItem02_itemScore" readonly="true" size="1"/>分</td>
						<td><input type="text" name="paItem02_itemAll" id="paItem02_itemAll" class="totalit" readonly="true" size="1"/>分</td>
						<td><textarea id="paItem02_itemDscr" name="paItem02_itemDscr" cols="25" rows="2" class="max txt_mult" maxlengthC="3400"></textarea></td>
					</tr>
					<tr>
						<td><wicket:message key="L300S01A.paItem03"></wicket:message></td>
						<td>
							<input type="text" id="paItem03_itemType" name="paItem03_itemType" value="CNT" style="display:none"/>
							<input type="text" id="paItem03_itemCnt" name="paItem03_itemCnt" class="max mathit numeric" positiveonly="true" size="3" integer="3" fraction="0" value="0"/>項
						</td>
						<td><input type="text" name="paItem03_itemScore" id="paItem03_itemScore" readonly="true" size="1"/>分</td>
						<td><input type="text" name="paItem03_itemAll" id="paItem03_itemAll" class="totalit" readonly="true" size="1"/>分</td>
						<td><textarea id="paItem03_itemDscr" name="paItem03_itemDscr" cols="25" rows="2" class="max txt_mult" maxlengthC="3400"></textarea></td>
					</tr>
					<tr>
						<td><wicket:message key="L300S01A.paItem04"></wicket:message></td>
						<td>
							<input type="text" id="paItem04_itemType" name="paItem04_itemType" value="CNT" style="display:none"/>
							<input type="text" id="paItem04_itemCnt" name="paItem04_itemCnt" class="max mathit numeric" positiveonly="true" size="3" integer="3" fraction="0" value="0"/>項
						</td>
						<td><input type="text" name="paItem04_itemScore" id="paItem04_itemScore" readonly="true" size="1"/>分</td>
						<td><input type="text" name="paItem04_itemAll" id="paItem04_itemAll" class="totalit" readonly="true" size="1"/>分</td>
						<td><textarea id="paItem04_itemDscr" name="paItem04_itemDscr" cols="25" rows="2" class="max txt_mult" maxlengthC="3400"></textarea></td>
					</tr>
					<tr>
						<td><wicket:message key="L300S01A.paItem05"></wicket:message></td>
						<td>
							<input type="text" id="paItem05_itemType" name="paItem05_itemType" value="CNT" style="display:none"/>
							<input type="text" id="paItem05_itemCnt" name="paItem05_itemCnt" class="max mathit numeric" positiveonly="true" size="3" integer="3" fraction="0" value="0"/>項
						</td>
						<td><input type="text" name="paItem05_itemScore" id="paItem05_itemScore" readonly="true" size="1"/>分</td>
						<td><input type="text" name="paItem05_itemAll" id="paItem05_itemAll" class="totalit" readonly="true" size="1"/>分</td>
						<td><textarea id="paItem05_itemDscr" name="paItem05_itemDscr" cols="25" rows="2" class="max txt_mult" maxlengthC="3400"></textarea></td>
					</tr>
					<tr>
						<td><wicket:message key="L300S01A.paItem06"></wicket:message></td>
						<td>
							<input type="text" id="paItem06_itemType" name="paItem06_itemType" value="YN" style="display:none"/>
							<input type="text" id="paItem06_itemCnt" name="paItem06_itemCnt" class="max mathit numeric" positiveonly="true" size="3" integer="3" fraction="0" value="0"/>戶
						</td>
						<td><input type="text" name="paItem06_itemScore" id="paItem06_itemScore" readonly="true" size="1"/>分</td>
						<td><input type="text" name="paItem06_itemAll" id="paItem06_itemAll" class="totalit" readonly="true" size="1"/>分</td>
						<td><textarea id="paItem06_itemDscr" name="paItem06_itemDscr" cols="25" rows="2" class="max txt_mult" maxlengthC="3400"></textarea></td>
					</tr>
					<tr>
						<td><wicket:message key="L300S01A.paItem07"></wicket:message></td>
						<td>
							<input type="text" id="paItem07_itemType" name="paItem07_itemType" value="YN" style="display:none"/>
							<input type="text" id="paItem07_itemCnt" name="paItem07_itemCnt" class="max mathit numeric" positiveonly="true" size="3" integer="3" fraction="0" value="0"/>戶
						</td>
						<td><input type="text" name="paItem07_itemScore" id="paItem07_itemScore" readonly="true" size="1"/>分</td>
						<td><input type="text" name="paItem07_itemAll" id="paItem07_itemAll" class="totalit" readonly="true" size="1"/>分</td>
						<td><textarea id="paItem07_itemDscr" name="paItem07_itemDscr" cols="25" rows="2" class="max txt_mult" maxlengthC="3400"></textarea></td>
					</tr>
					<tr>
						<td><wicket:message key="L300S01A.paItem08"></wicket:message></td>
						<td>
							<input type="text" id="paItem08_itemType" name="paItem08_itemType" value="YN" style="display:none"/>
							<input type="text" id="paItem08_itemCnt" name="paItem08_itemCnt" class="max mathit numeric" positiveonly="true" size="3" integer="3" fraction="0" value="0"/>戶
						</td>
						<td><input type="text" name="paItem08_itemScore" id="paItem08_itemScore" readonly="true" size="1"/>分</td>
						<td><input type="text" name="paItem08_itemAll" id="paItem08_itemAll" class="totalit" readonly="true" size="1"/>分</td>
						<td><textarea id="paItem08_itemDscr" name="paItem08_itemDscr" cols="25" rows="2" class="max txt_mult" maxlengthC="3400"></textarea></td>
					</tr>
					-->
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td width='53%' style="border-top:hidden;border-left:hidden;border-right:hidden;"></td>
						<td width='10%' style="border-top:hidden;border-left:hidden;border-right:hidden;"></td>
						<td width='7%' style="border-top:hidden;border-left:hidden;border-right:hidden;"></td>
						<td width='10%' style="border-top:hidden;border-left:hidden;border-right:hidden;"></td>
						<td width='30%' style="border-top:hidden;border-left:hidden;border-right:hidden;"></td>
					</tr>
					<tr>
						<td class="hd1" colspan="3">
							<button type="button" id="reCalc"><wicket:message key="btn.reCalc">重新計算</wicket:message></button>
							<wicket:message key="L300M01A.subTotal">扣分小計</wicket:message>（A）
						</td>
						<td><input type="text" name="subTotal" id="subTotal" readonly="true" size="3"/>分</td>
						<td></td>
					</tr>
					<tr>
						<td class="hd1" colspan="3"><wicket:message key="L300M01A.rsNum">覆審件數</wicket:message>（B）</td>
						<td><input type="text" name="rsNum" id="rsNum" readonly="true" size="3"/>件</td>
						<td></td>
					</tr>
					<tr>
						<td class="hd1" colspan="3"><wicket:message key="L300M01A.avgScore">平均扣分值</wicket:message>（A/B）（取至小數第三位）</td>
						<td><input type="text" name="avgScore" id="avgScore" readonly="true" size="3"/></td>
						<td></td>
					</tr>
					<tr class="afterCalc">
						<td class="hd1" colspan="3"><wicket:message key="L300M01A.realScore">換算後實際得分</wicket:message>（※詳說明二、換算公式）</td>
						<td><input type="text" name="realScore" id="realScore" readonly="true" size="3"/>分</td>
						<td></td>
					</tr>
					<tr>
						<td colspan="3"><wicket:message key="L300S01A.plusItem"></wicket:message>（※詳說明三、）</td>
						<td>
							<input type="text" id="plusItem_itemAll" name="plusItem_itemAll" class="max numeric" positiveonly="true" size="6" integer="3" fraction="2" value="0" max="10" min="0"/>分
						</td>
						<td><textarea id="plusItem_itemDscr" name="plusItem_itemDscr" cols="25" rows="2" class="max txt_mult" maxlengthC="3400"></textarea></td>
					</tr>
					<tr>
						<td colspan="3"><wicket:message key="L300S01A.minusItem"></wicket:message>（※詳說明三、）</td>
						<td>
							<input type="text" id="minusItem_itemAll" name="minusItem_itemAll" class="max numeric" positiveonly="false" size="6" integer="3" fraction="2" value="0" max="0" min="-10"/>分
						</td>
						<td><textarea id="minusItem_itemDscr" name="minusItem_itemDscr" cols="25" rows="2" class="max txt_mult" maxlengthC="3400"></textarea></td>
					</tr>
					<tr class="afterCalc">
						<td class="hd1" colspan="3"><wicket:message key="L300M01A.totalScore">總評分</wicket:message></td>
						<td><input type="text" name="totalScore" id="totalScore" readonly="true" size="3"/>分</td>
						<td></td>
					</tr>
				</table>
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td colspan="5" class="bgcolor2">※	說明：</td>
					</tr>
					<tr>
						<td colspan="5" id="memo1" >&nbsp;&nbsp;一、	考評期間：分為上半年、下半年，各六個月為一期。</td>
					</tr>
					<tr>
						<td colspan="5">&nbsp;&nbsp;二、	將區域內之各分行平均扣分值（A/B）由高至低排列，以最低者為X、最高者為Y，實際得分區間訂為60分至90分（最低分不可調整）。該分行實際得分換算公式：
							<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							（Y-X）/（90-60）＝│A│（絕對值）
							<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							60＋﹝（該分行平均扣分值-X）/│A│﹞＝該分行實際得分
						</td>
					</tr>
					<tr>
						<td colspan="5">&nbsp;&nbsp;三、	其他（加分項目）如營業單位授信作業嚴謹、檔案管理整潔、覆審配合良好、資料準備妥善、發現缺失迅速改正、保全措施確實、……等良好表現，於備註欄簡單列舉，每項加2分，最多加分10分。反之則為其他（減分項目），最多減10分。</td>
					</tr>
				</table>
			</form>
			
			<div id="docPanel">
				<fieldset>
                    <legend>
                        <b><wicket:message key="doc.docUpdateLog"><!-- 文件異動紀錄 --></wicket:message></b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer"><!-- 文件異動紀錄--> <div wicket:id="_docLog" /></div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                            	<td width="35%" class="hd1">
                                    <wicket:message key="doc.creator"><!--  文件建立者--></wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="15%">
                                    <span id='creator'/>(<span id='createTime'/>)
                                </td>
                                <td width="30%" class="hd1">
                                    <wicket:message key="doc.lastUpdater"><!--  最後異動者--></wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="20%">
                                    <span id='updater'/>(<span id='updateTime'/>)
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
			</div>
			
			<div id="openCheckBox" style="display:none;"> 
				<div>
					<span id="check1" style="display:none">
					 	<label><input name="checkRadio" type="radio" value="3"/><wicket:message key="accept"><!--  核准--></wicket:message></label><br/>
						<label><input name="checkRadio" type="radio" value="1"/><wicket:message key="back"><!--  退回經辦修改--></wicket:message></label>
					</span>
				</div>
			</div>
			<div id="selectBossBox"  style="display:none;">
			  <form id="selectBossForm">
	         	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                 <tr >
	                 	<td class="hd1" width="60%"><wicket:message key="staffJob.L3"><!--襄理sHeadReCheck--></wicket:message>&nbsp;&nbsp;</td>
	            		<td width="40%">
	            			<select id="staffL3Item" name="staffL3Item" class="boss"/>
							<!--<div id="staffL3Item"></div>-->
	                 	</td>
	                 </tr>
					<tr >
	                 	<td class="hd1" ><wicket:message key="staffJob.L5"><!--副營運長sHeadSubLeader--></wicket:message>&nbsp;&nbsp;</td>
	            		<td >
	            			<select id="staffL5Item" name="staffL4Item" class="boss"/>
							<!--<div id="staffL4Item"></div>-->
	                 	</td>
	                 </tr>
	                 <tr >
	            		<td class="hd1"><wicket:message key="staffJob.L6"><!--協理sHeadLeader--></wicket:message>&nbsp;&nbsp;</td>
	                    <td>
							<select id="staffL6Item" name="staffL5Item" class="boss"/>
							<!--<div id="staffL5Item"></div>-->
						</td>
	                 </tr>
	           	 </table>
				</form>
  			</div>
		</wicket:extend>
    </body>
</html>
