var thisOid = "";
var gridCES;
var gridCreditRisk;
pageJsInit(function() {
$(function() {
initDfd = initDfd || $.Deferred(); 
initDfd.done(function(){
	gridSelCes1("");
    gridSelCes2("");
    gridSelCes3("");

	$("#tabs-w").tabs({ //需先檢核成功後才能切tab,否則儲存時,無法顯示其它被隱藏的tab的錯誤
        select: function(event, ui){
			var pageIndex = $(this).find("li.ui-tabs-selected").index();
            switch (pageIndex + 1) {
              case 1:
                if (!$("#L120S01aForm").valid()) {
					event.preventDefault();
                }
                break;
			  case 4:
                if (!$("#L120S01fForm").valid()) {
					event.preventDefault();
                }
                break;	
           }
        }
    });
	
	//J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	$.ajax({
        type: "POST",
        handler: "codetypehandler",
		async: false ,
        data: {
            formAction: "branchByUnitTypeExceptHeadOffice"
        },
        success: function(responseData){
            var json = {
                format: "{value} - {key}",
                item: responseData
            };
            
            $("#reviewBrNo").setItems(json);
            
        }
    });
	
    //變更徵信財務科目選擇的顏色
    $("[name=set]").on("click", function(){
        var $this = $(this);
        if ($this.attr("checked")) {
            $this.closest("td").css("background", "#C0C0C0");
        }
        else {
            $this.closest("td").css("background", "#FFFFFF");
        }
    });
    
	
	
    $("#stockStatus").change(function(){
		
        var value = $(this).val();
        if (value == "6") {
            $(this).nextAll().hide();
            $("#spanStock,#stockDate").val('');
        }
        else {
            $("#spanStock").html($(this).find("option:selected").html());
            $(this).nextAll().show();
        }
    });
    $("#posType").change(function(){
        var $posDscr = $("#posDscr");
        if ($(this).find("option:selected").val() == "9") {
            $posDscr.show();
        }
        else {
            $posDscr.hide();
            $posDscr.val("");
        }
    });
    $("input[name='invMFlag']").click(function(){
        var $class = $(".sBorrowData");
        if ($(this).val() == "1") {
//            if (responseJSON.docType == "1" &&
//            responseJSON.docKind == "1") {
            if (responseJSON.docType == "1") {
                if (responseJSON.docKind == "1") {
                    $class.show();
                } else if (responseJSON.docKind == "2") {
                     $class.hide();
                     $("#hInvMDscr").show();
                 }
            }
            else {
                $class.hide();
            }
        }
        else {
            $(".invShow").find("#invMAmt").val("");
            $class.hide();
            $("#invMDscr").val("");
        }
        $class = null;
    });
    $("input[name='unfConFlag']").click(function(){
        var $class = $(".unfConMemoShow");
        if ($(this).val() == "Y") {
            $class.show();
        } else {
            $class.hide();
            $class.find("#unfConMemo").val("");
        }
    });
    $("input[name='mbRlt']").click(function(){
        var $class = $(".dscr1");
        if ($(this).val() == "1") {
            $class.show();
        }
        else {
            $class.hide();
            $class.find("#mbRltDscr").val("");
            $class.find("#mbMhRltDscr").val("");
        }
        $class = null;
    });
    $("input[name='mhRlt44']").click(function(){
        var $class = $(".dscr2");
        if ($(this).val() == "1") {
            $class.show();
        }
        else {
            $class.hide();
            $class.find("#mhRlt44Dscr").val("");
        }
        $class = null;
    });
    $("input[name='mhRlt45']").click(function(){
        var $class = $(".dscr3");
        if ($(this).val() == "1") {
            $class.show();
        }
        else {
            $class.hide();
            $class.find("#mhRlt45Dscr").val("");
        }
        $class = null;
    });
    $("input[name=invMFlag]").click(function(){
        var $class = $(".none");
        if ($(this).val() != "1") {
            $class.hide();
            $class.find("#aprAmt").val("");
        }
        else {
            $class.show();
        }
        $class = null;
    });
    $("input[name='fctMbRlt']").click(function(){
        var $class = $(".dscr4");
        if ($(this).val() == "1" ||
        $("input[name='fctMhRlt']:checked").val() == "1") {
            $class.show();
        }
        else {
            $class.hide();
            $class.find("#fctMhRltDscr").val("");
        }
        $class = null;
    });
    $("input[name='fctMhRlt']").click(function(){
        var $class = $(".dscr4");
        if ($(this).val() == "1" ||
        $("input[name='fctMbRlt']:checked").val() == "1") {
            $class.show();
        }
        else {
            $class.hide();
            $class.find("#fctMhRltDscr").val("");
        }
        $class = null;
    });
	$("input[name='mbRlt33']").click(function(){
        var $class = $("#tabs-w").find(".dscr33");
        if ($(this).val() == "1") {
            $class.show();
        }
        else {
            $class.hide();
            $class.find("#mbRltDscr33").val("");
        }
        $class = null;	
    });
	
	//J-108-0178_05097_B1001 Web e-Loan企金授權外簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
	$("input[name='caRlt206']").click(function(){
        var $class = $("#tabs-w").find(".dscr206");
        if ($(this).val() == "Y") {
            $class.show();
        }
        else {
            $class.hide();
            $class.find("#caRlt206Dscr").val("");
        }
        $class = null;	
    });
	
	
	//J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
	$("input[name='localRlt']").click(function(){
        var $class = $("#tabs-w").find(".dscrLocal");
        if ($(this).val() == "1") {
            $class.show();
        }
        else {
            $class.hide();
            $class.find("#localRltDscr").val("");
        }
        $class = null;	
    });
	
	
    $("#keyMan").click(function(){
        if ($(this).attr("checked") == true) {
            $(this).val("Y");
            $("#custRltShow").val("");
            $("#custRlt").html("");
            $("#custPos option:eq(0)").attr("selected", true);
            $("#chk_radio1").hide();
        }
        else {
            $(this).val("N");
            if ($("#custRlt").html() != "" &&
            $("#custPos option:selected").val() !=
            "") {
                $("[name='custRlt_content'] option").each(function(i){
                    var $this = $(this);
                    if ($this.val() ==
                    $("#custRlt").html()) {
                        $("#custRltShow").val($("#custRlt").html() +
                        " " +
                        $this.text());
                    }
                });
                $("#chk_radio1").show();
            }
            else {
                $("#custRlt").html("");
                $("#custPos option:eq(0)").attr("selected", true);
                $("#chk_radio1").show();
            }
        }
    });
    
    
    
    $("input[name='hasWarnGrade']").click(function(){
    	//J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
        var $class = $("#tabs-w").find(".showWarnGradeNote");
        if ($(this).val() == "1") {
            $class.show();
        }
        else {
            $class.hide();
            $class.find("#warnGradeNote").val("");
        }
        $class = null; 
    });
    
    $("input[name='hasDowngradeGrade']").click(function(){
    	//J-110-0445_05097_B1001 於簽報書借款人基本資料之信用評等下新增欄位
    	var $class = $("#tabs-w").find("#showDowngradeGradeNote");
        if ($(this).val() == "1") {
            $class.show();
        }
        else {
            $class.hide();
            $class.find("#downgradeGradeStatus").val("");
            $class.find("#downgradeGradeNote").val("");
        }
        $class = null;    
    });
    
    
    
    
	
	$("#L120S01aForm").find("input[name='privateEquityFg']").click(function(){
        //J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
        var $class = $("#L120S01aForm").find(".privateEquityShow");
        if ($(this).val() == "Y") {
            $class.show();
        }
        else {
            $class.hide();
            $class.find("#privateEquityNo").val("");
            $class.find("#privateEquityName").val("");
        }
        $class = null;	
    });
	
	$("#L120S01aForm").find("input[name='bfPrivateEquityFg']").click(function(){
        //J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
        var $class = $("#L120S01aForm").find(".bfPrivateEquityShow");
        if ($(this).val() == "Y") {
            $class.show();
        }
        else {
            $class.hide();
            $class.find("#bfPrivateEquityNo").val("");
            $class.find("#bfPrivateEquityName").val("");
        }
        $class = null;		
    });
	
    $("#custRlt_main").change(function(){
        if ($("#custRlt_main option:selected").val() ==
        "1") {
            // 企業關係人
            $("#relation").empty();
            $("#trRel").show();
            $("#relation").append(DOMPurify.sanitize($("#custRlt_main option:selected").text()));
            $(".company").show();
            $(".family").hide();
            $(".other").hide();
        }
        else 
            if ($("#custRlt_main option:selected").val() ==
            "2") {
                // 親屬關係
                $("#relation").empty();
                $("#trRel").show();
                $("#relation").append(DOMPurify.sanitize($("#custRlt_main option:selected").text()));
                $(".company").hide();
                $(".family").show();
                $(".other").hide();
            }
            else 
                if ($("#custRlt_main option:selected").val() ==
                "3") {
                    // 其他綜合關係
                    $("#relation").empty();
                    $("#trRel").show();
                    $("#relation").append(DOMPurify.sanitize($("#custRlt_main option:selected").text()));
                    $(".company").hide();
                    $(".family").hide();
                    $(".other").show();
                }
                else {
                    // 請選擇
                    $("#relation").empty();
                    $("#trRel").hide();
                    $(".company").hide();
                    $(".family").hide();
                    $(".other").hide();
                }
    });
    
    var mowGrid = $("#mowGrid").iGrid({ // 借款人基本資料GridView
        handler: 'lms1205gridhandler',
        height: 175,
        //sortname: 'crdTYear',
        postData: {
            formAction: "queryMowTrust",
            thisOid: thisOid,
            rowNum: 10
        },
        rownumbers: true,
        rowNum: 10,
        caption: "&nbsp;",
        hiddengrid: false,
        // multiselect : true,
        colModel: [{
            colHeader: i18n.lmss02["mowTrust.crdType"], // 評等表類型
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'crdType' // col.id
        }, {
            colHeader: i18n.lmss02["mowTrust.crdTBR"], // 評等單位
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            name: 'crdTBR' // col.id
        }, {
            colHeader: i18n.lmss02["mowTrust.crdTYear"], // 評等（公佈）日期
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'crdTYear' // col.id
        }, {
            colHeader: i18n.lmss02["mowTrust.grade"], // 評等等級
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'grade' // col.id
        }, {
            colHeader: "finYear", // 評等財報年度
            name: 'finYear',
            hidden: true
        }, {
            colHeader: "prospect", // 評等展望
            name: 'prospect',
            hidden: true
        }, {
            colHeader: "prCustId", // 保證企業統一編號
            name: 'prCustId',
            hidden: true
        }, {
            colHeader: "prDupNo", // 保證企業重覆序號
            name: 'prDupNo',
            hidden: true
        }, {
            colHeader: "prCNAME", // 保證企業名稱
            name: 'prCNAME',
            hidden: true
        }, {
            colHeader: "prFR", // 保證企業最終評等
            name: 'prFR',
            hidden: true
        }, {
            colHeader: "prFinDate", // 保證企業所引用之財報期間
            name: 'prFinDate',
            hidden: true
        }, {
            colHeader: "prMOWBr", // 保證企業之評等單位
            name: 'prMOWBr',
            hidden: true
        }, {
            colHeader: "custId",
            name: 'custId',
            hidden: true
        }, {
            colHeader: "dupNo",
            name: 'dupNo',
            hidden: true
        }, {
            colHeader: "_crdType",
            name: '_crdType',
            hidden: true
        }, {
            colHeader: "_crdTBR",
            name: '_crdTBR',
            hidden: true
        }, {
            colHeader: "noteId",
            name: 'noteId',
            hidden: true    
        }, {
            colHeader: "pr",
            name: 'pr',
            hidden: true    
        }, {
            colHeader: "sa",
            name: 'sa',
            hidden: true    
        }, {
            colHeader: "spr",
            name: 'spr',
            hidden: true    
        }, {
            colHeader: "fr",
            name: 'fr',
            hidden: true   
        }, {
            colHeader: "warn1",
            name: 'warn1',
            hidden: true   
        }, {
            colHeader: "warn2",
            name: 'warn2',
            hidden: true   
        }, {
            colHeader: "warn3",
            name: 'warn3',
            hidden: true        
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        }
    });
    
    var cesGrid1 = $("#cesGrid1").iGrid({ // 借款人基本資料GridView
        handler: 'lms1205gridhandler',
        height: 175,
        sortname: 'custName',
        postData: {
            formAction: "queryL120s01e1",
            thisOid: thisOid,
            rowNum: 5
        },
        rownumbers: true,
        rowNum: 5,
        caption: "&nbsp;",
        hiddengrid: false,
        // multiselect : true,
        colModel: [{
            colHeader: i18n.lmss02["l120s02.grid2"], // 主要借款人
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lmss02["l120s02.grid3"], // 建立日期
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id
        }, {
            colHeader: i18n.lmss02["l120s02.grid1"], // 資信簡表編號
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'sn' // col.id
        }, {
            colHeader: i18n.lmss02["l120s02.grid4"], // 文件狀態
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'docStatus' // col.id
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "gaapFlag",
            name: "gaapFlag",
            hidden: true    
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        }
    });
    
    var cesGrid2 = $("#cesGrid2").iGrid({ // 借款人基本資料GridView
        handler: 'lms1205gridhandler',
        height: 175,
        sortname: 'createTime',
        postData: {
            formAction: "queryL120s01e2",
            thisOid: thisOid,
            rowNum: 5
        },
        rownumbers: true,
        rowNum: 5,
        caption: "&nbsp;",
        hiddengrid: false,
        // multiselect : true,
        colModel: [{
            colHeader: i18n.lmss02["l120s02.grid3"], // 建立日期
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id
        }, {
            colHeader: i18n.lmss02["l120s02.grid5"], // 核准日期
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'approveTime' // col.id
        }, {
            colHeader: i18n.lmss02["l120s02.grid4"], // 文件狀態
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'docStatus' // col.id
        }, {
            colHeader: i18n.lmss02["l120s02.grid2"], // 主要借款人
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        }
    });
    
    //信用風險管理遵循
    gridCreditRisk = $("#gridCreditRisk").iGrid({
        localFirst: true,
        handler: "lms1205gridhandler",
        height: 60,
        autowidth: true,
        postData: {
            formAction: "queryCreditRisk"
        },
        rowNum: 15,
        multiselect: false,
        colModel: [{
            colHeader: i18n.lmss02["l120s01m.item2"], //"查詢日期"
            name: 'queryDate',
            width: 50,
            align: "center",
            sortable: false,
            formatter: 'click',
            onclick: tL120s01m
        }, {
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = gridCreditRisk.getRowData(rowid);
            tL120s01m(null, null, data);
        }
    });
    
    gridCES = $("#gridCES").iGrid({
        handler: 'lms1205gridhandler',
        height: 175,
        sortname: 'createTime',
        postData: {
            formAction: "queryL120s01e2",
            rowNum: 10
        },
        caption: "&nbsp;",
        hiddengrid: false,
        localFirst: true,
        rownumbers: true,
        rowNum: 10,
        //multiselect : true,
        colModel: [{
            colHeader: i18n.lmss02["l120s02.grid3"], //建立日期
            align: "left",
            width: 100, //設定寬度
            sortable: false, //是否允許排序
            name: 'createTime' //col.id
        }, {
            colHeader: i18n.lmss02["l120s02.grid5"], //核准日期
            align: "left",
            width: 100, //設定寬度
            sortable: false, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'approveTime' //col.id
        }, {
            colHeader: i18n.lmss02["l120s02.grid4"], //文件狀態
            align: "left",
            width: 100, //設定寬度
            sortable: false, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'docStatus' //col.id
        }, {
            colHeader: i18n.lmss02["l120s02.grid2"], //主要借款人
            align: "left",
            width: 100, //設定寬度
            sortable: false, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'custName' //col.id
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        },
        loadComplete: function(){
            if (gridCES == undefined) {
                //localFirst也會執行，固要先判斷
                return false;
            }
            if (gridCES.is(':visible')) {
                //如果使用者一直按grid上的重新整理的特別處理
                return false;
            }
            var $L120S01aForm = $("#L120S01aForm");
            var cesRowCount = gridCES.getGridParam("reccount");
            if (cesRowCount > 0) {
                $("#openCES").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.lmss02["l120s01e.btn1"],
                    width: 640,
                    height: 350,
                    align: 'center',
                    valign: 'bottom',
                    modal: true,
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                            var selrow = gridCES.getGridParam('selrow');
                            if (selrow) {
                                var ret = gridCES.getRowData(selrow);
                                $.ajax({
                                    type: "POST",
                                    handler: _handler,
                                    action: "getBorrowDataFromCES",
                                    data: {
                                        cesMainId: ret.mainId
                                    },
                                    success: function(responseData){
                                        if (responseData.chairman != "") {
                                            $L120S01aForm.find("#chairman").val(responseData.chairman);
                                            $L120S01aForm.find("#chairmanId").val(responseData.chairmanId);
                                        }
                                        if (responseData.gManager != "") {
                                            $L120S01aForm.find("#gManager").val(responseData.gManager);
                                            $L120S01aForm.find("#gManagerDscr").val(i18n.lmss02["l120s01b.gmanager"]);
                                            
                                        }
                                        $L120S01aForm.find("#cmpAddr").val(responseData.cmpAddr);
                                        $L120S01aForm.find("#ntCode").val(responseData.ntCode);
                                        $L120S01aForm.find("#cmpAddr").val(responseData.cmpAddr);
                                        
                                        //J-102-0206 股票上市櫃情形與日期有值時就不要蓋掉$L120S01aForm.find("#stockStatus").val(responseData.stockStatus);
                                        var stockStatus = $L120S01aForm.find("#stockStatus").val();
                                        var stockDate = $L120S01aForm.find("#stockDate").val();
                                        if (stockStatus == "") {
                                            $L120S01aForm.find("#stockStatus").val(responseData.stockStatus);
                                        }
                                        if (stockDate == "") {
                                            $L120S01aForm.find("#stockDate").val(responseData.stockDate);
                                        }
                                        $L120S01aForm.find("#stockStatus").trigger('change');
                                        
                                        $L120S01aForm.find("#factoryAddr").val(responseData.factoryAddr);
                                        $L120S01aForm.find("#rgtAmt").val(responseData.rgtAmt);
                                        $L120S01aForm.find("#cptlAmt").val(responseData.cptlAmt);
                                        $L120S01aForm.find("#rgtCurr").val(responseData.rgtCurr);
                                        $L120S01aForm.find("#cptlCurr").val(responseData.cptlCurr);
                                        $L120S01aForm.find("#rgtUnit").val(responseData.rgtUnit);
                                        $L120S01aForm.find("#cptlUnit").val(responseData.cptlUnit);
                                        $L120S01aForm.find("#stockHolder").val(responseData.stockHolder);
                                        $L120S01aForm.find("#estDate").val(responseData.estDate);
                                        $L120S01aForm.find("input[type='radio'][name='invMFlag'][value='" + responseData.invMFlag + "']").attr("checked", true).trigger("click");
                                        ;
                                        
//                                        if (responseJSON.docKind == "1") {
                                            //授權內簽報書才有主要營業項目
                                            $L120S01aForm.find("#itemOfBusi").val(responseData.itemOfBusi);
                                            $L120S01aForm.find("#invMDscr").val(responseData.invMDscr);
//                                        }
                                        
                                        $.thickbox.close();
                                        importCust();
                                    }
                                });
                            }
                            else {
                                CommonAPI.showMessage(i18n.def["grid.selrow"]);
                            }
                        },
                        "close": function(){
                            $.thickbox.close();
                            importCust();
                        }
                    }
                });
            }
            else {
                importCust();
            }
        }
    });
	
	var $L120S01aForm = $("#L120S01aForm");
	 
	//J-106-0029-003  洗錢防制-新增實質受益人
	/** 關係人-自然人  */
	$("#gridviewNatural").iGrid({
        height: 200,
        handler: "lms1201gridhandler",
        localFirst: true,
        sortname: 'seqNum|createTime',
        sortorder: 'asc|asc',
        postData: {
            formAction: "queryL120s01p",
            mainId: responseJSON.mainId,
            type: "1",
            custId: $L120S01aForm.find("#custId").val(),
            dupNo: $L120S01aForm.find("#dupNo").val()
        },
        multiselect: true,
        hideMultiselect: false,
        colModel: [{
            colHeader: i18n.lmss02["L120S01p.custIdAndDupNo"],//"統編(含重覆序號)"
            width: 80,
            name: 'rId',
            sortable: true,
            formatter: 'click',
            onclick: newToglePersonBT
        }, {
            colHeader: i18n.lmss02["L120S01p.conPersonName"],//"名稱",
            name: 'rName',
            width: 100,
            sortable: false
        }, {
            colHeader: i18n.lmss02["L120S01p.conPersonEName"],//"英文名稱",
            name: 'rEName',
            width: 100,
            sortable: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "rType",
            name: 'rType',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridviewNatural").getRowData(rowid);
            newToglePersonBT(null, null, data);
        }
    });

    
	//J-106-0029-003  洗錢防制-新增實質受益人 
	/** 關係人-法人 */
	$("#gridviewCorporate").iGrid({
        height: 200,
        handler: "lms1201gridhandler",
        localFirst: true,
        sortname: 'seqNum|createTime',
        sortorder: 'asc|asc',
        postData: {
            formAction: "queryL120s01p",
            mainId: responseJSON.mainId,
            type: "2",
            custId: $L120S01aForm.find("#custId").val(),
            dupNo: $L120S01aForm.find("#dupNo").val()
        },
        multiselect: true,
        hideMultiselect: false,
        colModel: [{
            colHeader: i18n.lmss02["L120S01p.custIdAndDupNo"],//"統編(含重覆序號)"
            width: 80,
            name: 'rId',
            sortable: true,
            formatter: 'click',
            onclick: newToglePersonBT
        }, {
            colHeader: i18n.lmss02["L120S01p.conPersonName"],//"名稱",
            name: 'rName',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lmss02["L120S01p.conPersonEName"],//"英文名稱",
            name: 'rEName',
            width: 100,
            sortable: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "rType",
            name: 'rType',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridviewCorporate").getRowData(rowid);
            newToglePersonBT(null, null, data);
        }
    });

    // J-112-0125 私募基金
    $("#gridPrivateEquity").iGrid({
        localFirst: true,
        handler: "lms1201gridhandler",
        height: 90,
        autowidth: true,
        postData: {
            formAction: "queryL120s01t",
            mainId: responseJSON.mainId,
            custId: $L120S01aForm.find("#custId").val(),
            dupNo: $L120S01aForm.find("#dupNo").val(),
            flag: "Y"
        },
        rowNum: 3,
        multiselect: false,
        colModel: [{
            colHeader: i18n.lmss02["l120s01b.privateEquity"],
            name: 'privateFund',
            width: 50,
            align: "center",
            sortable: false
        }, {
            name: 'oid',
            hidden: true
        }]
    });

    // J-112-0125 私募基金
    $("#gridBfPrivateEquity").iGrid({
        localFirst: true,
        handler: "lms1201gridhandler",
        height: 90,
        autowidth: true,
        postData: {
            formAction: "queryL120s01t",
            mainId: responseJSON.mainId,
            custId: $L120S01aForm.find("#custId").val(),
            dupNo: $L120S01aForm.find("#dupNo").val(),
            flag: "N"
        },
        rowNum: 3,
        multiselect: false,
        colModel: [{
            colHeader: i18n.lmss02["l120s01b.privateEquity"],
            name: 'privateFund',
            width: 50,
            align: "center",
            sortable: false
        }, {
            name: 'oid',
            hidden: true
        }]
    });
    
    //J-106-0029-003  洗錢防制-新增實質受益人
    //自動帶入使用者姓名
    $("#rId,#rDupNo").blur(function(){
        var custId = $("#rId").val();
        var dupNo = $("#rDupNo").val();
        if ($.trim(custId).length > 0 && $.trim(dupNo).length > 0) {
            $.ajax({
                handler: "amlrelateformhandler",
                action: "getMisCustData",
                data: {
                    custId: custId,
                    dupNo: dupNo
                },
                success: function(obj){
                    if (!$.isEmptyObject(obj)) {
                        $("#rName").val(obj.custName);
						$("#rEName").val(obj.custEName);
                    }
                }
            });
        }
    });

    // J-109-0370
    Panel06Gridview01();
	GroupInfoGridview01();

	//J-106-0029-003  洗錢防制-新增實質受益人
	/** 登錄實質受益人  */
	$("#toglePersonBT").click(function(){
		
		/** 登錄實質受益人  */
	  
		var rType = "7"; //實質受益人
		var $L120S01aForm = $("#L120S01aForm");
		
		//讓每次開起box都是第一頁
		$("#toglePersonTabs").tabs({
			selected: 0
		});
		 
		$("#gridviewNatural").jqGrid("setGridParam", {//重新設定grid需要查到的資料
			postData: {
				formAction: "queryL120s01p",
				mainId: responseJSON.mainId,
				type: "1",
				custId: $L120S01aForm.find("#custId").val(),
				dupNo: $L120S01aForm.find("#dupNo").val(),
				rType: rType
			},
			search: true,
			loadComplete: function(){
			    
				$("#gridviewCorporate").jqGrid("setGridParam", {//重新設定grid需要查到的資料
					postData: {
						formAction: "queryL120s01p",
						mainId: responseJSON.mainId,
						type: "2",
						custId: $L120S01aForm.find("#custId").val(),
						dupNo: $L120S01aForm.find("#dupNo").val(),
						rType: rType
					},
					search: true,
					loadComplete: function(){
					 
						$('#gridviewNatural').jqGrid('setGridParam', {
							loadComplete: function(){
								//執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
							}
						});
						$('#gridviewCorporate').jqGrid('setGridParam', {
							loadComplete: function(){
								//執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
							}
						});
						
						 
						var btnAction = API.createJSON([{
							key: i18n.def['close'],
							value: function(){
								$.thickbox.close();
							}
						}]);
						
						 
						if (responseJSON.readOnly != "true") {
						
							if (userInfo.ssoUnitNo == "900") {
								btnAction = API.createJSON([{
									//引進
									key: i18n.lmss02['L120S01p.btnApply'],
									value: function(){
									
										var countN = $("#gridviewNatural").jqGrid('getGridParam', 'records');
										var countC = $("#gridviewCorporate").jqGrid('getGridParam', 'records');
										if (countN + countC > 0) {
											//confirmBeforeDeleteAll=執行時會刪除已存在之資料，是否確定執行？
											CommonAPI.confirmMessage(i18n.def["confirmBeforeDeleteAll"], function(b){
												if (b) {
													//是的function
													$.ajax({
														handler: "amlrelateformhandler",
														data: {//把資料轉成json
															formAction: "applyBeneficiaryData",
															mainId: responseJSON.mainId,
															custId: $L120S01aForm.find("#custId").val(),
															dupNo: $L120S01aForm.find("#dupNo").val()
														},
														success: function(responseData){
															$('#gridviewNatural').trigger('reloadGrid');
															$('#gridviewCorporate').trigger('reloadGrid');
															if (responseData.errMsg) {
																return CommonAPI.showMessage(responseData.errMsg);
															}
															
														}
													});
												}
											});
										}
										else {
											$.ajax({
												handler: "amlrelateformhandler",
												data: {//把資料轉成json
													formAction: "applyBeneficiaryData",
													mainId: responseJSON.mainId,
													custId: $L120S01aForm.find("#custId").val(),
													dupNo: $L120S01aForm.find("#dupNo").val()
												},
												success: function(responseData){
													$('#gridviewNatural').trigger('reloadGrid');
													$('#gridviewCorporate').trigger('reloadGrid');
													if (responseData.errMsg) {
														return CommonAPI.showMessage(responseData.errMsg);
													}
												}
											});
										}
										
									}
									//只能引進0024
								}, {
									key: i18n.def['newData'],
									value: function(){
										newToglePersonBT(null, null, null, rType);
									}
								}, {
									key: i18n.def['del'],
									value: function(){
									
										var id1 = $("#gridviewNatural").getGridParam('selarrrow');
										var id2 = $("#gridviewCorporate").getGridParam('selarrrow');
										var data1 = [];
										var data2 = [];
										if (id1.length == 0 && id2.length == 0) {
											//TMMDeleteError=請先選擇需修改(刪除)之資料列
											return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
										}
										
										//confirmDelete=是否確定刪除?
										CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
											if (b) {
												if (id1.length > 0) {
													for (var i = 0; i < id1.length; i++) {
														data1[i] = $("#gridviewNatural").getRowData(id1[i]).oid;
													}
												}
												
												if (id2.length > 0) {
													for (var i = 0; i < id2.length; i++) {
														data2[i] = $("#gridviewCorporate").getRowData(id2[i]).oid;
													}
												}
												
												$.ajax({
													handler: "amlrelateformhandler",
													data: {//把資料轉成json
														formAction: "deleteL120s01p",
														oids: data1.concat(data2),
														mainId: responseJSON.mainId,
														showMsg: true
													},
													success: function(responseData){
														$('#gridviewNatural').trigger('reloadGrid');
														$('#gridviewCorporate').trigger('reloadGrid');
													}
												});
											}
										});
									}
								}, {
									key: i18n.def['close'],
									value: function(){
									
									
										//關閉的時候將相關人寫到畫面上 guarantor
										
										var corpId = $("#gridviewCorporate").jqGrid('getDataIDs');
										var natId = $("#gridviewNatural").jqGrid('getDataIDs');
										var data = "";
										var sign;
										var guaIndex = 0;
										var needIndex = false;
										if (natId.length + corpId.length > 1) {
											needIndex = true;
										}
										
										for (var i = 0; i < natId.length; i++) {
											(data.length > 0) ? sign = "、" : sign = "";
											guaIndex = guaIndex + 1;
											
											if (needIndex == true) {
												data = data + sign + guaIndex + "." + $("#gridviewNatural").getRowData(natId[i]).rName;//+ guaPercentStr;
											}
											else {
												data = data + sign + $("#gridviewNatural").getRowData(natId[i]).rName;//+ guaPercentStr;
											}
										}
										for (var i = 0; i < corpId.length; i++) {
											(data.length > 0) ? sign = "、" : sign = "";
											guaIndex = guaIndex + 1;
											
											if (needIndex == true) {
												data = data + sign + guaIndex + "." + $("#gridviewCorporate").getRowData(corpId[i]).rName;//+ guaPercentStr;
											}
											else {
												data = data + sign + $("#gridviewCorporate").getRowData(corpId[i]).rName;//+ guaPercentStr;
											}
										}
										
										//J-109-0067_05097_B1001 Web e-Loan 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
//										if (!data || $.trim(data) == "") {
//											data = i18n.lmss02['nohave']
//										}
										
										$.ajax({
											handler: "amlrelateformhandler",
											action: "saveL120s01pBeneficiaryStr",
											data: {
												mainId: responseJSON.mainId,
												custId: $L120S01aForm.find("#custId").val(),
												dupNo: $L120S01aForm.find("#dupNo").val(),
												beneficiary: data,
												rType: rType,
												callFrom: "1" //簽報書
											},
											success: function(obj){
												//$("#beneficiary").val(data);
												$("#beneficiary").val(obj.beneficiary);
												$.thickbox.close();
											}
										});
									}
								}]);
							}
							else {
								//一般分行只能引進0024
								btnAction = API.createJSON([{
									//引進
									key: i18n.lmss02['L120S01p.btnApply'],
									value: function(){
									
										var countN = $("#gridviewNatural").jqGrid('getGridParam', 'records');
										var countC = $("#gridviewCorporate").jqGrid('getGridParam', 'records');
										if (countN + countC > 0) {
											//confirmBeforeDeleteAll=執行時會刪除已存在之資料，是否確定執行？
											CommonAPI.confirmMessage(i18n.def["confirmBeforeDeleteAll"], function(b){
												if (b) {
													//是的function
													$.ajax({
														handler: "amlrelateformhandler",
														data: {//把資料轉成json
															formAction: "applyBeneficiaryData",
															mainId: responseJSON.mainId,
															custId: $L120S01aForm.find("#custId").val(),
															dupNo: $L120S01aForm.find("#dupNo").val()
														},
														success: function(responseData){
															$('#gridviewNatural').trigger('reloadGrid');
															$('#gridviewCorporate').trigger('reloadGrid');
															if (responseData.errMsg) {
																return CommonAPI.showMessage(responseData.errMsg);
															}
															
														}
													});
												}
											});
										}
										else {
											$.ajax({
												handler: "amlrelateformhandler",
												data: {//把資料轉成json
													formAction: "applyBeneficiaryData",
													mainId: responseJSON.mainId,
													custId: $L120S01aForm.find("#custId").val(),
													dupNo: $L120S01aForm.find("#dupNo").val()
												},
												success: function(responseData){
													$('#gridviewNatural').trigger('reloadGrid');
													$('#gridviewCorporate').trigger('reloadGrid');
													if (responseData.errMsg) {
														return CommonAPI.showMessage(responseData.errMsg);
													}
												}
											});
										}
										
									}
									
								}, {
									key: i18n.def['close'],
									value: function(){
									
									
										//關閉的時候將相關人寫到畫面上 guarantor
										
										var corpId = $("#gridviewCorporate").jqGrid('getDataIDs');
										var natId = $("#gridviewNatural").jqGrid('getDataIDs');
										var data = "";
										var sign;
										var guaIndex = 0;
										var needIndex = false;
										if (natId.length + corpId.length > 1) {
											needIndex = true;
										}
										
										for (var i = 0; i < natId.length; i++) {
											(data.length > 0) ? sign = "、" : sign = "";
											guaIndex = guaIndex + 1;
											
											if (needIndex == true) {
												data = data + sign + guaIndex + "." + $("#gridviewNatural").getRowData(natId[i]).rName;//+ guaPercentStr;
											}
											else {
												data = data + sign + $("#gridviewNatural").getRowData(natId[i]).rName;//+ guaPercentStr;
											}
										}
										for (var i = 0; i < corpId.length; i++) {
											(data.length > 0) ? sign = "、" : sign = "";
											guaIndex = guaIndex + 1;
											
											if (needIndex == true) {
												data = data + sign + guaIndex + "." + $("#gridviewCorporate").getRowData(corpId[i]).rName;//+ guaPercentStr;
											}
											else {
												data = data + sign + $("#gridviewCorporate").getRowData(corpId[i]).rName;//+ guaPercentStr;
											}
										}
										
//										if (!data || $.trim(data) == "") {
//											data = i18n.lmss02['nohave']
//										}
										
										$.ajax({
											handler: "amlrelateformhandler",
											action: "saveL120s01pBeneficiaryStr",
											data: {
												mainId: responseJSON.mainId,
												custId: $L120S01aForm.find("#custId").val(),
												dupNo: $L120S01aForm.find("#dupNo").val(),
												beneficiary: data,
												rType: rType,
												callFrom: "1" //簽報書
											},
											success: function(obj){
												//$("#beneficiary").val(data);
												$("#beneficiary").val(obj.beneficiary);
												$.thickbox.close();
											}
										});
									}
								}]);
							}
							
						}
						
						
						$("#toglePersonBox").thickbox({ // 使用選取的內容進行彈窗
							title: i18n.lmss02["other.login"] + i18n.lmss02["L120S01p.beneficiary"],//'other.login=登錄 ',  L120S01p.conPersonNew
							width: 700,
							height: 410,
							readOnly: false,
							modal: true,
							buttons: btnAction
						});
						
					}
				}).trigger("reloadGrid");
			}
		}).trigger("reloadGrid");
	});
	
	//J-113-0075_12473_B1001 異常通報案件新增欄位
	var docCode = responseJSON.docCode;
	if("4" == responseJSON.docCode){
		$(".docCode4Show").show();
		$(".docCode4Change").attr("rowspan", "5");
	} else {
		$(".docCode4Show").hide();
	}
	var item = API.loadOrderCombosAsList("L120S01B_notReRatingRsn_os")["L120S01B_notReRatingRsn_os"];
	$("#notReRatingRsn").setItems({ size: "1", item: item, clear: true, itemType: 'radio' });
	checkReRating();
	checkNotReRatingRsn();
    $("input[name='reRatingFlag']:radio").change(function(){
    	checkReRating();
    });
    $L120S01aForm.find("input[name='notReRatingRsn']").change(function(){
    	checkNotReRatingRsn();
    });
    $("input[name='reRatingFlag']:radio").click(function(){
    	if("N" == $("input[name='reRatingFlag']:radio:checked").val()){ 
    		//於「已重辦信用評等」勾選「否」時，跳出「重辦評等時機」視窗
    		CommonAPI.showMessage(i18n.lmss02["l120s01b.reRatingTimingMsg"]);
    	}
    })
	
});

});
});
//J-113-0075_12473_B1001 異常通報案件新增欄位之控制
function checkReRating(){
	if("N" == $("input[name='reRatingFlag']:radio:checked").val()){ 
		$("input[name='notReRatingRsn']:radio").attr('disabled', false);
		checkNotReRatingRsn();
	} else {
		//於「已重辦信用評等」勾選「是」或未勾時，則「未重辦信用評等原因」選項唯讀
		$("input[name='notReRatingRsn']:radio").attr('checked', false).attr('disabled', true);
		$("#otherRsnDesc").val("").attr('disabled', true);
	}
}
function checkNotReRatingRsn(){
	if("6" == $("input[name='notReRatingRsn']:radio:checked").val()){
		$("#otherRsnDesc").attr('disabled', false);
	}else{
		$("#otherRsnDesc").val("").attr('disabled', true);
	}
}

function setGrpData(){
    var $L120S01aForm = $("#L120S01aForm");
    $.ajax({
        type: "POST",
        handler: _handler,
        data: {
            formAction: "showGrpData",
            custId: $L120S01aForm.find("#custId").val(),
            dupNo: $L120S01aForm.find("#dupNo").val()
        },
        success: function(responseData){
            $L120S01aForm.setData(responseData.L120S01aForm, false);
            uGroupInfoGridview01();
        }
    });
}

function editGrpData(cellvalue, options, rowObject){

	var oid = rowObject.oid;
	var $L120S01aForm = $("#L120S01aForm");
	$.ajax({
        type: "POST",
        handler: _handler,
        data: {
            formAction: "queryL120s05e",
            mainId: responseJSON.mainId,
            oid: oid,
            qCustId: $L120S01aForm.find("#custId").val(),
            qDupNo: $L120S01aForm.find("#dupNo").val()
        },
        success: function(responseData){

        	var $LMS1205S05eForm = $("#LMS1205S05eForm");

         	//J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			var grpYear = responseData.LMS1205S05eForm.grpYear;//.grpYear_s05e;
			var grpGrrd = responseData.LMS1205S05eForm.grpGrrd;
			if(grpYear){
				//判斷2017以後為新版，之前為舊版
				if(parseInt(grpYear, 10) >= 2017){
					var obj = CommonAPI.loadCombos(["GroupGrade2017"]);
			        //評等等級
			        $("#grpGrrd").setItems({
			            item: obj.GroupGrade2017,
			            format: "{key}"
			        });

				}else{
					var obj = CommonAPI.loadCombos(["GroupGrade"]);
			        //評等等級
			        $("#grpGrrd").setItems({
			            item: obj.GroupGrade,
			            format: "{key}"
			        });
				}

			}else{
				var obj = CommonAPI.loadCombos(["GroupGrade"]);

		        //評等等級
		        $("#grpGrrd").setItems({
		            item: obj.GroupGrade,
		            format: "{key}"
		        });
			}

			$LMS1205S05eForm.reset();
	        $LMS1205S05eForm.setData(responseData.LMS1205S05eForm, false);


        	if(responseJSON.readOnly == "true"){
        		$LMS1205S05eForm.readOnlyChilds(true);
        		$LMS1205S05eForm.find("button").hide();
        		$LMS1205S05eForm.find(".grpBtn").show();
        	}

        	uPanel06Gridview01();

        	var buttons = {};

        	if(responseJSON.readOnly == "true"){

        		 buttons['close'] = function(){
        			 API.confirmMessage(i18n.def['flow.exit'], function(res){
                         if (res) {
                             $.thickbox.close();
                         }
                     });
                 };

        	}else{
        		//l120s02.thickbox2=取消
        		buttons["l120s02.thickbox2" ] = function(){
       			 API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                };
        	}

            var editGrpData = $("#thickboxEditGrp").thickbox({
                // 使用選取的內容進行彈窗
                title: i18n.lmss02["l120s02.thickbox10"],
                width: 900,
                height: 400,
                align: 'center',
                valign: 'bottom',
                modal: true,
                i18n: i18n.lmss02,
                buttons:buttons
            });

        }
    });
}

function ShowOrHide(button_act,sh_item) {
	var chk_value=$('#'+button_act).html();
	if (chk_value==i18n.lmss02["l120s05.btn8"]) {
		$('#'+sh_item).show();
		$('#'+button_act).html(i18n.lmss02["l120s05.btn9"]);
	} else {
		$('#'+sh_item).hide();
		$('#'+button_act).html(i18n.lmss02["l120s05.btn8"]);
	}
	if(sh_item == 'GroupDetail1') {
		uPanel06Gridview01();
	}
}

//LMS1201S05Page06 load
function Panel06Gridview01(){
	var $L120S01aForm = $("#L120S01aForm");
	$("#Panel06Gridview01").iGrid({
		handler: 'lms1205gridhandler',
		height: 120, //for 10 筆
		sortname : 'custId',
		postData : {
			formAction : "queryL120s05f",
			mainId : responseJSON.mainid,
			qCustId: $L120S01aForm.find("#custId").val(),
            qDupNo: $L120S01aForm.find("#dupNo").val(),
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rowNum:10,
		rownumbers:true,
		colModel: [{
			colHeader: i18n.lmss02["l120s05.grid1"],//"【集團企業】明細<br />(戶名最多顯示長度24個全形字)"
			name: 'custName',
			width: 180,
			sortable: false,
			align:"left"
		}, {
			colHeader: i18n.lmss02["l120s05.grid2"],//"授信總額度(海外)"
			name: 'totAmtB',
			width: 100,
			sortable: false,
			formatter : function(data) {
                if (data == null) {
                    return "";
                }
                else {
                    // 加入撇節符號
                    return util.addComma(data);
                }
			},
			align:"right"
		}, {
			colHeader: i18n.lmss02["l120s05.grid3"],//"授信總額度(台灣)"
			name: 'totAmtA',
			width: 100,
			sortable: false,
			formatter : function(data) {
                if (data == null) {
                    return "";
                }
                else {
                    // 加入撇節符號
                    return util.addComma(data);
                }
			},
			align:"right"
		}, {
			colHeader: i18n.lmss02["l120s05.grid4"],//"無擔保授信額度(海外)"
			name: 'crdAmtB',
			width: 100,
			sortable: false,
			formatter : function(data) {
                if (data == null) {
                    return "";
                }
                else {
                    // 加入撇節符號
                    return util.addComma(data);
                }
			},
			align:"right"
		}, {
			colHeader: i18n.lmss02["l120s05.grid5"],//"無擔保授信額度(台灣)"
			name: 'crdAmtA',
			width: 100,
			sortable: false,
			formatter : function(data) {
                if (data == null) {
                    return "";
                }
                else {
                    // 加入撇節符號
                    return util.addComma(data);
                }
			},
			align:"right"
		}, {
			colHeader: i18n.lmss02["l120s05.grid6"],//"扣除一～四項<br />授信總額度"
			name: 'excAmt',
			width: 100,
			sortable: false,
			formatter : function(data) {
                if (data == null) {
                    return "";
                }
                else {
                    // 加入撇節符號
                    return util.addComma(data);
                }
			},
			align:"right"
		}, {
			colHeader: i18n.lmss02["l120s05.grid7"],//"扣除一～四項<br />無擔保授信額度"
			name: 'excrdAmt',
			width: 100,
			sortable: false,
			formatter : function(data) {
                if (data == null) {
                    return "";
                }
                else {
                    // 加入撇節符號
                    return util.addComma(data);
                }
			},
			align:"right"
		}, {
            colHeader: i18n.lmss02["l120s05.grid30"],//在途授信總額度
            name: 'totElAllAmt',
            width: 100,
            sortable: false,
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                removeTrailingZero: true,
                decimalPlaces: 0,   //小數點到第幾位
                defaultValue: ""
            },
            align:"right"
        }, {
            colHeader: i18n.lmss02["l120s05.grid31"],//在途無擔保額度
            name: 'crdElAllAmt',
            width: 100,
            sortable: false,
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                removeTrailingZero: true,
                decimalPlaces: 0,   //小數點到第幾位
                defaultValue: ""
            },
            align:"right"
        }, {
			name: 'custId',
			hidden: true
		}, {
			name: 'oid',
			hidden: true
		}],
		ondblClickRow: function(rowid){
		}
	});
}

function uPanel06Gridview01(){
	   var $L120S01aForm = $("#L120S01aForm");
	   $("#Panel06Gridview01").jqGrid("setGridParam", {
		postData : {
			formAction : "queryL120s05f",
			mainId : responseJSON.mainId,
			qCustId: $L120S01aForm.find("#custId").val(),
            qDupNo: $L120S01aForm.find("#dupNo").val(),
			rowNum:10
		},
		search: true
	   }).trigger("reloadGrid");
}

function GroupInfoGridview01(){
	var $L120S01aForm = $("#L120S01aForm");
	$("#GroupInfoGridview01").iGrid({
		handler: 'lms1205gridhandler',
		height: 80,
        autowidth: true,
		sortname : 'custId',
		postData : {
			formAction : "queryL120s05e",
			mainId : responseJSON.mainid,
			qCustId: $L120S01aForm.find("#custId").val(),
            qDupNo: $L120S01aForm.find("#dupNo").val(),
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rowNum:10,
		rownumbers:true,
		multiselect: false,
		colModel: [{
			colHeader: i18n.lmss02["l120s05a.grpflagDscr"], //集團種類
			name: 'grpFlag',
			width: 150,
            align: "left",
            sortable: false,
            formatter: 'click',
            onclick: editGrpData
		}, {
			colHeader: i18n.lmss02["l120s01m.item2"], //"查詢日期"
			name: 'inqDate',
			width: 20,
            align: "center",
            sortable: false
		}, {
			name: 'oid',
			hidden: true
		}],
		ondblClickRow: function(rowid){
			var data = gridCreditRisk.getRowData(rowid);
			editGrpData(null, null, data);
		}
	});
}

function uGroupInfoGridview01(){
    var $L120S01aForm = $("#L120S01aForm");
    $("#GroupInfoGridview01").jqGrid("setGridParam", {
        postData : {
            formAction : "queryL120s05e",
            mainId : responseJSON.mainId,
            qCustId: $L120S01aForm.find("#custId").val(),
            qDupNo: $L120S01aForm.find("#dupNo").val(),
            rowNum:10
        },
        search: true
    }).trigger("reloadGrid");
}

function getGrpData(){
    var getGrpData = $("#thickboxGrp").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lmss02["l120s02.thickbox10"],
        width: 640,
        height: 200,
        align: 'center',
        valign: 'bottom',
        modal: true,
        i18n: i18n.lmss02,
        buttons: {
            "l120s02.thickbox1": function(showMsg){
                $.ajax({
                    type: "POST",
                    handler: _handler,
                    data: {
                        formAction: "getGrpData",
                        selKey: $("#grpData option:selected").html(),
                        selVal: $("#grpData option:selected").val()
                    },
                    success: function(responseData){
                        $("#L120S01aForm").setData(responseData.L120S01aForm, false);
                        $.thickbox.close();
                    }
                });
            },
            "l120s02.thickbox2": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function getCustData2(){
    //J-110-0458 企金授權內其他 - 「簡易簽報」選項，適用方案「LIBOR退場變更利率條件簡易簽報」
    if(lmsM01Json.isLiborExitCase() || lmsM01Json.isEuroyenTiborExitCase()){
        // 小規模營業人
        // step 1. mowGrid();   引進信用風險內部評等
        // step 2. importBorrowFromCES();   引進徵信資料
        // step 3. importCust();
        // step 4. toglePersonBtInner(rType);    引進實際受益人rType = "7";、高階管理人rType = "10";、具控制權人rType = "11";

        latestMow();    // step 1.2.3

        // 等5s 再開始
        setTimeout(function(){
            importPerson(); // step 4
        }, 5000);
    } else {
        $("#thickboxTrust").thickbox({
            // 使用選取的內容進行彈窗
            title: i18n.lmss02["l120s02.thickbox16"],
            width: 300,
            height: 200,
            modal: true,
            align: "center",
            valign: "bottom",
            i18n: i18n.lmss02,
            buttons: {
                "l120s02.thickbox1": function(){
                    $(".trust").val("");
                    var trustVal = $("input[name='selTrust']:radio:checked").val();
                    if (trustVal == 1) {
                        importTrust(1);
                        $.thickbox.close();
                    }
                    else
                        if (trustVal == 2) {
                            //getL120s01c(2);
                            // 內部Mow信評;
                            mowGrid();
                            $.thickbox.close();
                            $("#mowGrid").resetSelection();
                            $("#thickboxMow").thickbox({
                                // 使用選取的內容進行彈窗
                                title: i18n.lmss02["l120s02.thickbox17"],
                                width: 640,
                                height: 380,
                                modal: true,
                                align: "center",
                                valign: "bottom",
                                i18n: i18n.def,
                                buttons: {
                                    "sure": function(){
                                        var $L120S01aForm = $("#L120S01aForm");
                                        var row = $("#mowGrid").getGridParam('selrow');
                                        var crdType = "", crdTBR = "", crdTYear = "", grade = "", finYear = "";
                                        var prospect = "", prCustId = "", prDupNo = "", prCNAME = "", prFR = "";
                                        var prFinDate = "", prMOWBr = "";
                                        var data = $("#mowGrid").getRowData(row);
                                        crdTYear = data.crdTYear;
                                        crdTYear = (crdTYear == undefined ? "" : crdTYear);
                                        if (crdTYear != "") {
                                            $.ajax({
                                                handler: _handler,
                                                type: "POST",
                                                dataType: "json",
                                                data: {
                                                    formAction: "saveMowTrust",
                                                    mainId: responseJSON.mainId,
                                                    crdType: data.crdType,
                                                    _crdType: data._crdType,
                                                    crdTBR: data.crdTBR,
                                                    _crdTBR: data._crdTBR,
                                                    crdTYear: crdTYear,
                                                    grade: data.grade,
                                                    finYear: data.finYear,
                                                    prospect: data.prospect,
                                                    prCustId: data.prCustId,
                                                    prDupNo: data.prDupNo,
                                                    prCNAME: data.prCNAME,
                                                    prFR: data.prFR,
                                                    prFinDate: data.prFinDate,
                                                    prMOWBr: data.prMOWBr,
                                                    custId: $L120S01aForm.find("#custId").html(),
                                                    dupNo: $L120S01aForm.find("#dupNo").html(),
                                                    noteId: data.noteId,
                                                    pr: data.pr,
                                                    sa: data.sa,
                                                    spr: data.spr,
                                                    fr: data.fr,
                                                    warn1: data.warn1,
                                                    warn2: data.warn2,
                                                    warn3: data.warn3
                                                },
                                                success: function(json){
                                                    var $L120S01aForm = $("#L120S01aForm");
                                                    $L120S01aForm.setData(json.L120S01aForm, false);
                                                    $.thickbox.close();
                                                    $.thickbox.close();
                                                    importBorrowFromCES();
                                                //importCust();
                                                }
                                            });
                                        }
                                        else {
                                            CommonAPI.showMessage(i18n.lmss02["l120s02.alert1"]);
                                        }
                                    },
                                    "cancel": function(){
                                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                                            if (res) {
                                                $.thickbox.close();
                                            }
                                        });
                                    }
                                }
                            });
                        }
                        else
                            if (trustVal == 3) {
                                importTrust(4);
                                $.thickbox.close();
                            }
                },
                "l120s02.thickbox2": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    }
}

function latestMow(){
    $.ajax({
        handler: "lms1105formhandler",  // 寫在LMSM01FormHandler 透過lms1105formhandler呼叫
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getLatestMow",
            mainId: responseJSON.mainId,
            thisOid: thisOid
        },
        success: function(json){
            $("#L120S01aForm").setData(json.L120S01aForm, false);
            // step 2. importBorrowFromCES();   引進徵信資料
            latestCES();
        }
    });
}

function latestCES(){
    $.ajax({
        handler: "lms1105formhandler",  // 寫在LMSM01FormHandler 透過lms1105formhandler呼叫
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getLatestCes",
            mainId: responseJSON.mainId,
            thisOid: thisOid
        },
        success: function(json){
            if(json.noCES){
            	var $L120S01aForm = $("#L120S01aForm");

            	// 大陸投資預設為無
                $("#L120S01aForm").find("input[name='invMFlag'][value=2]").attr("checked", true).trigger("click");

                //J-110-0234_05097_B1002 Web e-Loan國內企金簽報書小規模RPA修改
                var mbRlt33Val = $("input[name='mbRlt33']:radio:checked").val();
                if( mbRlt33Val== undefined || mbRlt33Val == "" ){
                	$L120S01aForm.find("input[name='mbRlt33'][value=2]").attr("checked", true).trigger("click");
                }

                importCust();
            } else {
                var $L120S01aForm = $("#L120S01aForm");

                if (json.chairman != "") {
                    $L120S01aForm.find("#chairman").val(json.chairman);
                    $L120S01aForm.find("#chairmanId").val(json.chairmanId);
                }
                if (json.gManager != "") {
                    $L120S01aForm.find("#gManager").val(json.gManager);
                    $L120S01aForm.find("#gManagerDscr").val(i18n.lmss02["l120s01b.gmanager"]);
                }

                $L120S01aForm.find("#ntCode").val(json.ntCode);
                $L120S01aForm.find("#cmpAddr").val(json.cmpAddr);

                //J-102-0206 股票上市櫃情形與日期有值時就不要蓋掉$L120S01aForm.find("#stockStatus").val(responseData.stockStatus);
                var stockStatus = $L120S01aForm.find("#stockStatus").val();
                var stockDate = $L120S01aForm.find("#stockDate").val();
                if (stockStatus == "") {
                    $L120S01aForm.find("#stockStatus").val(json.stockStatus);
                }
                if (stockDate == "") {
                    $L120S01aForm.find("#stockDate").val(json.stockDate);
                }
                $L120S01aForm.find("#stockStatus").trigger('change');

                $L120S01aForm.find("#factoryAddr").val(json.factoryAddr);
                $L120S01aForm.find("#rgtAmt").val(json.rgtAmt);
                $L120S01aForm.find("#cptlAmt").val(json.cptlAmt);
                $L120S01aForm.find("#rgtCurr").val(json.rgtCurr);
                $L120S01aForm.find("#cptlCurr").val(json.cptlCurr);
                $L120S01aForm.find("#rgtUnit").val(json.rgtUnit);
                $L120S01aForm.find("#cptlUnit").val(json.cptlUnit);
                $L120S01aForm.find("#stockHolder").val(json.stockHolder);
                $L120S01aForm.find("#estDate").val(json.estDate);

                // 確認大陸投資 是否有值
                if(json.invMFlag == ""){
                    // 大陸投資預設為無
                    $L120S01aForm.find("input[name='invMFlag'][value=2]").attr("checked", true).trigger("click");
                } else {
                    $L120S01aForm.find("input[type='radio'][name='invMFlag'][value='" + json.invMFlag + "']").attr("checked", true).trigger("click");
                }

                //J-110-0234_05097_B1002 Web e-Loan國內企金簽報書小規模RPA修改
                var mbRlt33Val = $("input[name='mbRlt33']:radio:checked").val();
                if( mbRlt33Val== undefined || mbRlt33Val == "" ){
                	$L120S01aForm.find("input[name='mbRlt33'][value=2]").attr("checked", true).trigger("click");
                }

//                if (responseJSON.docKind == "1") {
                    setTimeout(function(){
                        //授權內簽報書才有主要營業項目
                        $L120S01aForm.find("#itemOfBusi").val(json.itemOfBusi);
                        $L120S01aForm.find("#invMDscr").val(json.invMDscr);
                    }, 500);
//                }

                importCust();

                if(json.CESMAINID != undefined && json.CESMAINID != ""){
                    $.ajax({ // 查詢主要借款人資料
                        handler : "lms1105formhandler", // 寫在LMSM01FormHandler 透過lms1105formhandler呼叫
                        type : "POST",
                        dataType : "json",
                        data : {
                            formAction : "findRelate1",
                            formL120m01e : "{}",
                            mainId : responseJSON.mainId,
                            cesMainId : json.CESMAINID,
                            notShowMsg : 'Y'
                        },
                        success : function(obj) {

                        }
                    });
                }
            }
        }
    });
}

function importPerson(){
    // 實際受益人rType = "7";、高階管理人rType = "10";、具控制權人rType = "11";
    var $L120S01aForm = $("#L120S01aForm");

    // 應收帳款預設不適用
    $L120S01aForm.find("input[name='fctMbRlt'][value=3]").attr("checked", true);
    $L120S01aForm.find("input[name='fctMhRlt'][value=3]").attr("checked", true);

    //  實質受益人
    $.ajax({
        handler: "amlrelateformhandler",
        data: {//把資料轉成json
            formAction: "applyBeneficiaryData",
            mainId: responseJSON.mainId,
            custId: $L120S01aForm.find("#custId").val(),
            dupNo: $L120S01aForm.find("#dupNo").val()
        },
        success: function(responseData){
            //  高階管理人員
            $.ajax({
                handler: "amlrelateformhandler",
                data: {//把資料轉成json
                    formAction: "applySeniorMgrData",
                    mainId: responseJSON.mainId,
                    custId: $L120S01aForm.find("#custId").val(),
                    dupNo: $L120S01aForm.find("#dupNo").val()
                },
                success: function(responseData){
                    //  具控制權人
                    $.ajax({
                        handler: "amlrelateformhandler",
                        data: {//把資料轉成json
                            formAction: "applyCtrlPeoData",
                            mainId: responseJSON.mainId,
                            custId: $L120S01aForm.find("#custId").val(),
                            dupNo: $L120S01aForm.find("#dupNo").val()
                        },
                        success: function(responseData){
                            // 寫到畫面上
                            $.ajax({
                                handler: "amlrelateformhandler",
                                action: "saveL120s01pStr",
                                data: {
                                    mainId: responseJSON.mainId,
                                    custId: $L120S01aForm.find("#custId").val(),
                                    dupNo: $L120S01aForm.find("#dupNo").val()
                                },
                                success: function(obj){
                                    $L120S01aForm.find("#beneficiary").val(obj.beneficiary);
                                    $L120S01aForm.find("#seniorMgr").val(obj.seniorMgr);
                                    $L120S01aForm.find("#ctrlPeo").val(obj.ctrlPeo);
                                }
                            });
                            //  寫到畫面上 DONE
                        }
                    });
                    //  具控制權人 DONE
                }
            });
            //  高階管理人員 DONE
        }
    });//  實質受益人 DONE
}

function importBorrowFromCES(){
    gridCES.jqGrid('setGridParam', {
        postData: {
            formAction: "queryL120s01e2",
            rowNum: 10,
            thisOid: thisOid
        }
    });
    gridCES.trigger("reloadGrid");
}

function importTrust(frag){
    $.ajax({
        type: "POST",
        handler: _handler,
        data: {
            formAction: "queryL120s01c",
            mainId: responseJSON.mainId,
            oid: thisOid,
            frag: frag,
            showMsg: false
        },
        success: function(responseData){
            var $L120S01aForm = $("#L120S01aForm");
            if (frag == 2 || frag == 4) {
                $L120S01aForm.find("#showGrade").hide();
            }
            else {
                $L120S01aForm.find("#showGrade").show();
            }
            $L120S01aForm.setData(responseData.L120S01aForm, false);
            if (frag == 3) {
                if (responseData.L120S01aForm.grade1 == undefined &&
                responseData.L120S01aForm.grade2 == undefined &&
                responseData.L120S01aForm.grade3 == undefined &&
                responseData.L120S01aForm.grade7 == undefined &&
                responseData.L120S01aForm.grade8 == undefined &&
                responseData.L120S01aForm.grade9 == undefined) {
                    $L120S01aForm.find(".hideMow1").hide();
                    $L120S01aForm.find(".hideMow2").hide();
                    $L120S01aForm.find(".hideMow3").hide();
                    //J-113-0141_12473_B1001 海外 外部信評之評等機構增列中華信評
                    $L120S01aForm.find(".hideMow7").hide();
                    //J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
                    $L120S01aForm.find(".hideMow8").hide();
                    $L120S01aForm.find(".hideMow9").hide();
                }
                else {
                    if (responseData.L120S01aForm.grade1 != undefined) {
                        $L120S01aForm.find(".hideMow1").show();
                    }
                    if (responseData.L120S01aForm.grade2 != undefined) {
                        $L120S01aForm.find(".hideMow2").show();
                    }
                    if (responseData.L120S01aForm.grade3 != undefined) {
                        $L120S01aForm.find(".hideMow3").show();
                    }
                    
                    //J-113-0141_12473_B1001 海外 外部信評之評等機構增列中華信評
                    if (responseData.L120S01aForm.grade7 != undefined) {
                        $L120S01aForm.find(".hideMow7").show();
                    }
                    
                    //J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
                    if (responseData.L120S01aForm.grade8 != undefined) {
                        $L120S01aForm.find(".hideMow8").show();
                    }
                    if (responseData.L120S01aForm.grade9 != undefined) {
                        $L120S01aForm.find(".hideMow9").show();
                    }
                }
            }
            importBorrowFromCES();
        }
    });
}

function importCust(){
	var errorMsg = "";
    var $L120S01aForm = $("#L120S01aForm");
    $.ajax({
        type: "POST",
        handler: "customerformhandler",
        data: {
            formAction: "custQueryBy0024ByIdDupNo",
            sendId: $L120S01aForm.find("#custId").val(),
            dupNo: $L120S01aForm.find("#dupNo").val()
        },
        success: function(responseData){
            $L120S01aForm.find("#custName").val(responseData.cname);
        }
    });
    $.ajax({
        type: "POST",
        handler: _handler,
        data: {
            formAction: "getCustData2",
            mainId: responseJSON.mainId,
            custId: $L120S01aForm.find("#custId").val(),
            dupNo: $L120S01aForm.find("#dupNo").val(),
            showMsg: false,
            show: false
        },
        success: function(responseData){
            $L120S01aForm.setData(responseData.L120S01aForm, false);
            getL120s01c(3, false);
            // 開始引進婉卻
            $.ajax({
                handler: _handler,
                type: "POST",
                dataType: "json",
                data: {
                    formAction: "getReject",
                    mainId: responseJSON.mainId,
                    custId: $("#L120S01aForm").find("#custId").html(),
                    dupNo: $("#L120S01aForm").find("#dupNo").html(),
                    noMsg: true
                },
                success: function(json){
                    $("#L120S01aForm").setData(json, false);
                    var rejtCaseAdjMemo = $("#L120S01aForm").find("#rejtCaseAdjMemo").val();
                    if (rejtCaseAdjMemo == undefined || rejtCaseAdjMemo == null || rejtCaseAdjMemo == "") {
                        $("#L120S01aForm .hideMemo").hide();
                    }
                    else {
                        $("#L120S01aForm .hideMemo").show();
                    }
                    
                    $.ajax({
                        handler: _handler,
                        type: "POST",
                        dataType: "json",
                        data: {
                            formAction: "getReject1",
                            mainId: responseJSON.mainId,
                            custId: $("#L120S01aForm").find("#custId").html(),
                            dupNo: $("#L120S01aForm").find("#dupNo").html(),
                            chairmanId: $("#L120S01aForm").find("#chairmanId").val(),
                            chairmanDupNo: $("#L120S01aForm").find("#chairmanDupNo").val(),
                            noMsg: true,
                            chairmanVisible: $("#L120S01aForm").find("#chairmanId").is(':visible')
                        },
                        success: function(json){
                            if (json.isPassed) {
                                $("#L120S01aForm").setData(json, false);
                                if (json.L120S01aForm.IsRejt1 == "") {
                                    $("#L120S01aForm").find("[name=IsRejt1]").attr("checked", false);
                                }
                                var rejtCaseAdjMemo1 = $("#L120S01aForm").find("#rejtCaseAdjMemo1").val();
                                if (rejtCaseAdjMemo1 == undefined || rejtCaseAdjMemo1 == null || rejtCaseAdjMemo1 == "") {
                                    $("#L120S01aForm .hideMemo1").hide();
                                }
                                else {
                                    $("#L120S01aForm .hideMemo1").show();
                                }
                            }
                            
							
							//重新引進0024註冊地國別(因為徵信可能是所在地國別)
							$.ajax({
						        type: "POST",
						        handler: "customerformhandler",
						        data: {
						            formAction: "custNtCodeQueryBy0024ByIdDupNo",
						            sendId: $L120S01aForm.find("#custId").val(),
						            dupNo: $L120S01aForm.find("#dupNo").val()
						        },
						        success: function(responseData){
									if(responseData.ntCode != "" && responseData.ntCode != 'undefined' ){
										$L120S01aForm.find("#ntCode").val(responseData.ntCode);
									}
									
						        }
						    });
									
                            // 以下開始實作引進利害關係人
                            $.ajax({
                                type: "POST",
                                handler: _handler,
                                data: {
                                    formAction: "getRlt",
                                    oid: thisOid,
                                    showMsg: false,
                                    show: false
                                },
                                success: function(responseData){
									errorMsg += responseData.errorMsg;
                                    var $L120S01aForm = $("#L120S01aForm");
                                    $L120S01aForm.setData(responseData.L120S01aForm, false);
                                    $L120S01aForm.find("input[name='mbRlt']").each(function(i){
                                        var $this = $(this);
                                        if (responseData.L120S01aForm.mbRlt ==
                                        $this.val()) {
                                            $this.attr("checked", true);
                                        }else if(responseData.L120S01aForm.mbRlt ==""){
													$this.attr("checked", false);
												}
                                        else 
                                            if (responseData.L120S01aForm.mhRlt == "0") {
                                                if ($this.val() == "2") {
                                                    $this.attr("checked", true);
                                                }
                                            }
                                    });
                                    $L120S01aForm.find("input[name='mhRlt44']").each(function(j){
                                        var $this = $(this);
                                        if (responseData.L120S01aForm.mhRlt44 ==
                                        $this.val()) {
                                            $this.attr("checked", true);
                                        }else if(responseData.L120S01aForm.mhRlt44 ==""){
											$this.attr("checked", false);
										}
                                        else 
                                            if (responseData.L120S01aForm.mhRlt44 == "0") {
                                                if ($this.val() == "2") {
                                                    $this.attr("checked", true);
                                                }
                                            }
                                    });
                                    $L120S01aForm.find("input[name='mhRlt45']").each(function(k){
                                        var $this = $(this);
                                        if (responseData.L120S01aForm.mhRlt45 ==
                                        $this.val()) {
                                            $this.attr("checked", true);
                                        }else if(responseData.L120S01aForm.mhRlt45 ==""){
											$this.attr("checked", false);
										}
                                        else 
                                            if (responseData.L120S01aForm.mhRlt45 == "0") {
                                                if ($this.val() == "2") {
                                                    $this.attr("checked", true);
                                                }
                                            }
                                    });
                                    
                                    //J-108-0178_05097_B1001 Web e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
                                    if(responseData.showCaRlt206 == "Y"){
                                    	$L120S01aForm.find("input[name='caRlt206']").each(function(i){
                                            var $this = $(this);
                                            if (responseData.L120S01aForm.caRlt206 ==
                                            $this.val()) {
                                                $this.attr("checked", true);
                                            }
                                            else 
                                                if (responseData.L120S01aForm.caRlt206 == "") {
                                                    $this.attr("checked", false);
                                                }
                                                else 
                                                    if (responseData.L120S01aForm.caRlt206 == "0") {
                                                        if ($this.val() == "N") {
                                                            $this.attr("checked", true);
                                                        }
                                                    }
                                        });
                                    }
                                    
                                    
                                  //J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
                                  if(responseData.showLocalRlt == "Y"){
                                    	$L120S01aForm.find("input[name='localRlt']").each(function(i){
                                            var $this = $(this);
                                            if (responseData.L120S01aForm.localRlt ==
                                            $this.val()) {
                                                $this.attr("checked", true);
                                            }
                                            else 
                                                if (responseData.L120S01aForm.localRlt == "") {
                                                    $this.attr("checked", false);
                                                }
                                                else 
                                                    if (responseData.L120S01aForm.localRlt == "0") {
                                                        if ($this.val() == "N") {
                                                            $this.attr("checked", true);
                                                        }
                                                    }
                                        });
                                    }
                                    
                                    
                                    var $class1 = $(".dscr1");
                                    var $class2 = $(".dscr2")
                                    var $class3 = $(".dscr3")
                                    //J-108-0178_05097_B1001 Web e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
                                    var $class206 = $(".dscr206")
                                    //J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
                                    var $classRltLocal = $(".dscrLocal")
                                            
                                    if ($("input[name='mbRlt']:radio:checked").val() == "1") {
                                        $class1.show();
                                    }
                                    else {
                                        $class1.hide();
                                        $class1.find("#mbRltDscr").val("");
                                        $class1.find("#mbMhRltDscr").val("");
                                    }
                                    if ($("input[name='mhRlt44']:radio:checked").val() == "1") {
                                        $class2.show();
                                    }
                                    else {
                                        $class2.hide();
                                        $class2.find("#mhRlt44Dscr").val("");
                                    }
                                    if ($("input[name='mhRlt45']:radio:checked").val() == "1") {
                                        $class3.show();
                                    }
                                    else {
                                        $class3.hide();
                                        $class3.find("#mhRlt45Dscr").val("");
                                    }

                                    //J-108-0178_05097_B1001 Web e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
                                    if(responseData.showCaRlt206 == "Y"){
                                    	if ($("input[name='caRlt206']:radio:checked").val() == "Y") {
                                            $class206.show();
                                        }
                                        else {
                                        	$class206.hide();
                                        	$class206.find("#caRlt206Dscr").val("");
                                        	 
                                        }
                                    }
                                    
                                  //J-108-0178_05097_B1001 Web e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
                                    if(responseData.showLocalRlt == "Y"){
                                    	if ($("input[name='localRlt']:radio:checked").val() == "1") {
                                    		$classRltLocal.show();
                                        }
                                        else {
                                        	$classRltLocal.hide();
                                        	$classRltLocal.find("#localRltDscr").val("");
                                        	 
                                        }
                                    }
                                    
                                    
                                    $class1 = null;
                                    $class2 = null;
                                    $class3 = null;
                                    //J-108-0178_05097_B1001 Web e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
                                    $class206 = null;
                                    //J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
                                    $classRltLocal = null;
                                    
                                    //---
                                    // 查詢隸屬集團		
                                    $.ajax({
                                        type: "POST",
                                        handler: _handler,
                                        data: {
                                            formAction: "showGrpData",
                                            custId: $L120S01aForm.find("#custId").val(),
                                            dupNo: $L120S01aForm.find("#dupNo").val()
                                        },
                                        success: function(responseData){
                                            $L120S01aForm.setData(responseData.L120S01aForm, false);
											uGroupInfoGridview01();
											//J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
											$.ajax({
								                handler: _handler,
								                type: "POST",
								                dataType: "json",
								                data: {
								                    formAction: "getAbnormal",
								                    mainId: responseJSON.mainId,
								                    custId: $("#L120S01aForm").find("#custId").html(),
								                    dupNo: $("#L120S01aForm").find("#dupNo").html(),
								                    noMsg: true
								                },
								                success: function(jsonAbnormal){
													$("#L120S01aForm").setData(jsonAbnormal, false);
								                    
								                    $.ajax({
								                        handler: _handler,
								                        type: "POST",
								                        dataType: "json",
								                        data: {
								                            formAction: "getAbnormal1",
								                            mainId: responseJSON.mainId,
								                            custId: $("#L120S01aForm").find("#custId").html(),
								                            dupNo: $("#L120S01aForm").find("#dupNo").html(),
								                            chairmanId: $("#L120S01aForm").find("#chairmanId").val(),
								                            chairmanDupNo: $("#L120S01aForm").find("#chairmanDupNo").val(),
								                            noMsg: true,
								                            chairmanVisible: $("#L120S01aForm").find("#chairmanId").is(':visible')
								                        },
								                        success: function(jsonAbnormal1){
								                            if (jsonAbnormal1.isPassed) {
								                                $("#L120S01aForm").setData(jsonAbnormal1, false);
								                                if (jsonAbnormal1.L120S01aForm.isAbnormal1 == "") {
								                                    $("#L120S01aForm").find("[name=isAbnormal1]").attr("checked", false);
								                                }
								                            }
															
															
															//J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
															$.ajax({
										                        handler: _handler,
										                        type: "POST",
										                        dataType: "json",
										                        data: {
										                            formAction: "applyPrivateEquityData",
										                            mainId: responseJSON.mainId,
														            custId: $("#L120S01aForm").find("#custId").val(),
														            dupNo: $("#L120S01aForm").find("#dupNo").val()
										                        },
										                        success: function(jsonPrivateEquityData){
										                            $("#L120S01aForm").setData(jsonPrivateEquityData.L120S01aForm, false);
										                            // J-112-0125 私募基金
										                            gridPrivateEquityReLoad('Y');
                                                                    gridPrivateEquityReLoad('N');
																	// 第一次新增客戶時，自動查詢信用風險管理遵循
				                                                    //applyCreditRisk(false);

																	var count = $("#gridCreditRisk").jqGrid('getGridParam', 'records');
																    var $L120S01aForm = $("#L120S01aForm");
																    var custId = $L120S01aForm.find("#custId").val();
																    var dupNo = $L120S01aForm.find("#dupNo").val();
																    $.ajax({
														                handler: _handler,
														                type: "POST",
														                dataType: "json",
														                data: {
														                    formAction: "deleteL120s01m",
														                    custId: custId,
														                    dupNo: dupNo,
														                    mainId: responseJSON.mainid
														                },
														                success: function(deleteL120s01Jjson){
														                    //$.thickbox.close();
														                    // 開始產生實績彙總表
														                    //importL120S01m(needAsk);
																			var groupNo = $L120S01aForm.find("#groupNo").val();
																		    var groupName = $L120S01aForm.find("#groupName").val();
																		    var mbRlt = $("input[name='mbRlt']:radio:checked").val();
																		    var mhRlt44 = $("input[name='mhRlt44']:radio:checked").val();
																		    var mhRlt45 = $("input[name='mhRlt45']:radio:checked").val();
																		    var custId = $L120S01aForm.find("#custId").val();
																		    var dupNo = $L120S01aForm.find("#dupNo").val();
																		    var busCode = $L120S01aForm.find("#busCode").val();
																		    var custName = $L120S01aForm.find("#custName").val();
																		    $.ajax({
																		        handler: _handler,
																		        type: "POST",
																		        dataType: "json",
																		        data: {
																		            formAction: "importL120s01m",
																		            mainId: responseJSON.mainid,
																		            custId: $L120S01aForm.find("#custId").val(),
																		            dupNo: $L120S01aForm.find("#dupNo").val(),
																		            groupNo: groupNo,
																		            groupName: groupName,
																		            mbRlt: mbRlt,
																		            mhRlt44: mhRlt44,
																		            mhRlt45: mhRlt45,
																		            busCode: busCode,
																		            custName: custName,
																					needAsk:false
																		        },
																		        success: function(importL120s01mJson){
																		            // 更新實績彙總表Grid
																		            $("#gridCreditRisk").jqGrid("setGridParam", {
																		                postData: {
																		                    formAction: "queryCreditRisk",
																		                    custId: custId,
																		                    dupNo: dupNo
																		                },
																		                search: true
																		            }).trigger("reloadGrid");
																		            
																					errorMsg += importL120s01mJson.errorMsg;
																		           
																				   // 初始化黑名單
								                                                    $L120S01aForm.find("#blackName").html("");
								                                                    $.ajax({
								                                                        handler: _handler,
								                                                        type: "POST",
								                                                        dataType: "json",
								                                                        data: {
								                                                            formAction: "getBlack",
								                                                            mainId: responseJSON.mainId,
								                                                            custId: $L120S01aForm.find("#custId").html(),
								                                                            dupNo: $L120S01aForm.find("#dupNo").html(),
								                                                            eName: $("#formBlack").find("#eName").val(),
								                                                            inputEname: false,
								                                                            noMsg: true
								                                                        },
								                                                        success: function(json2){
								    
								                                                            if (json2.needInput) {
								                                                                $("#formBlack").reset();
								                                                                $("#formBlack").find("#eName").val(json2.eName);
								                                                                errorMsg += json2.errorMsg;
								                                                                responseJSON["custErrorMsg"] = null;
								                                                                if (errorMsg.length > 0) {
								                                                                    responseJSON["custErrorMsg"] = errorMsg;
								                                                                }
								                                                                tBlack();
								                                                            }
								                                                            else {
								                                                                $L120S01aForm.setData(json2, false);
								                                                                errorMsg += json2.errorMsg;
								                                                                if (errorMsg.length > 0) {
								                                                                    CommonAPI.showErrorMessage(errorMsg);
								                                                                }
								                                                            }
																							
																							
								                                                        }
								                                                    }); //引進黑名單END	
																		        }
																		    });
														                }
														            });//引進信用風險管理遵循END		
															
														        }
														    });//引進私募基金END
														}	
													}); //引進負責人異常通報END		
													 
												}	
											}); //引進申貸戶異常通報END
											
											
                                        }
                                    });// 查詢隸屬集團END		
                                }
                            });
                        }
                    });
                }
            });
            
        }
    });
}

function printRlt(printType){
    CommonAPI.confirmMessage(i18n.lmss02["l120s02.confirm5"], function(b){
        if (b) {
            var $L120S01aForm = $("#L120S01aForm");
            $.form.submit({
                url: "../../simple/FileProcessingService",
                target: "_blank",
                data: {
                    showDataType: printType,
                    mainId: responseJSON.mainId,
                    custId: $L120S01aForm.find("#custId").val(),
                    dupNo: $L120S01aForm.find("#dupNo").val(),
                    fileDownloadName: "LMS1205R13.pdf",
                    serviceName: "lms1205r13rptservice"
                }
            });
        }
    });
}

function getRlt(){
    CommonAPI.confirmMessage(i18n.lmss02["l120s02.confirm3"], function(b){
        if (b) {
            $.ajax({
                type: "POST",
                handler: _handler,
                data: {
                    formAction: "getRlt",
                    oid: thisOid
                },
                success: function(responseData){
                    var $L120S01aForm = $("#L120S01aForm");
                    $L120S01aForm.setData(responseData.L120S01aForm, false);
                    $L120S01aForm.find("input[name='mbRlt']").each(function(i){
                        var $this = $(this);
                        if (responseData.L120S01aForm.mbRlt ==
                        $this.val()) {
                            $this.attr("checked", true);
                        }
                        else 
                            if (responseData.L120S01aForm.mhRlt == "0") {
                                if ($this.val() == "2") {
                                    $this.attr("checked", true);
                                }
                            }
                    });
                    $L120S01aForm.find("input[name='mhRlt44']").each(function(j){
                        var $this = $(this);
                        if (responseData.L120S01aForm.mhRlt44 ==
                        $this.val()) {
                            $this.attr("checked", true);
                        }
                        else 
                            if (responseData.L120S01aForm.mhRlt44 == "0") {
                                if ($this.val() == "2") {
                                    $this.attr("checked", true);
                                }
                            }
                    });
                    $L120S01aForm.find("input[name='mhRlt45']").each(function(k){
                        var $this = $(this);
                        if (responseData.L120S01aForm.mhRlt45 ==
                        $this.val()) {
                            $this.attr("checked", true);
                        }
                        else 
                            if (responseData.L120S01aForm.mhRlt45 == "0") {
                                if ($this.val() == "2") {
                                    $this.attr("checked", true);
                                }
                            }
                    });
                    
                    //J-108-0178_05097_B1001 Web e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
                    if(responseData.showCaRlt206 == "Y"){
                    	$L120S01aForm.find("input[name='caRlt206']").each(function(i){
                            var $this = $(this);
                            if (responseData.L120S01aForm.caRlt206 ==
                            $this.val()) {
                                $this.attr("checked", true);
                            }
                            else 
                                if (responseData.L120S01aForm.caRlt206 == "0") {
                                    if ($this.val() == "N") {
                                        $this.attr("checked", true);
                                    }
                                }
                        });
                    }
                    
                   //J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
                    if(responseData.showLocalRlt == "Y"){
                    	$L120S01aForm.find("input[name='localRlt']").each(function(i){
                            var $this = $(this);
                            if (responseData.L120S01aForm.localRlt ==
                            $this.val()) {
                                $this.attr("checked", true);
                            }
                            else 
                                if (responseData.L120S01aForm.localRlt == "0") {
                                    if ($this.val() == "2") {
                                        $this.attr("checked", true);
                                    }
                                }
                        });
                    }
                    
                    
                    var $class1 = $(".dscr1");
                    var $class2 = $(".dscr2");
                    var $class3 = $(".dscr3");
                    //J-108-0178_05097_B1001 Web e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
                    var $class206 = $(".dscr206");
                    //J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
                    var $classLocalRlt = $(".dscrLocal");
                    
                    if ($("input[name='mbRlt']:radio:checked").val() == "1") {
                        $class1.show();
                    }
                    else {
                        $class1.hide();
                        $class1.find("#mbRltDscr").val("");
                        $class1.find("#mbMhRltDscr").val("");
                    }
                    if ($("input[name='mhRlt44']:radio:checked").val() == "1") {
                        $class2.show();
                    }
                    else {
                        $class2.hide();
                        $class2.find("#mhRlt44Dscr").val("");
                    }
                    if ($("input[name='mhRlt45']:radio:checked").val() == "1") {
                        $class3.show();
                    }
                    else {
                        $class3.hide();
                        $class3.find("#mhRlt45Dscr").val("");
                    }
                    
                    //J-108-0178_05097_B1001 Web e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
                    if(responseData.showCaRlt206 == "Y"){
                    	if ($("input[name='caRlt206']:radio:checked").val() == "Y") {
                    		$class206.show();
                        }
                        else {
                        	$class206.hide();
                        	$class206.find("#caRlt206Dscr").val("");
                        }
                    }
                    
                    //J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
                    if(responseData.showLocalRlt == "Y"){
                    	if ($("input[name='localRlt']:radio:checked").val() == "1") {
                    		$classLocalRlt.show();
                        }
                        else {
                        	$classLocalRlt.hide();
                        	$classLocalRlt.find("#localRltDscr").val("");
                        }
                    }
                    
                    
                    $class1 = null;
                    $class2 = null;
                    $class3 = null;
                    //J-108-0178_05097_B1001 Web e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
                    $class206 = null;
                    //J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
                    $classLocalRlt = null;
                    
                    if (responseData.noData != null &&
                    responseData.noData != undefined &&
                    responseData.noData != "") {
                        CommonAPI.showErrorMessage(i18n.lmss02('l120s02.alert13', {
                            'colName': responseData.noData
                        }));
                    }
                }
            });
        }
        else {
            CommonAPI.showMessage(i18n.lmss02["l120s02.alert10"]);
        }
    });
}

function mowGrid(){
    $("#mowGrid").jqGrid("setGridParam", {
        postData: {
            formAction: "queryMowTrust",
            thisOid: thisOid,
            rowNum: 10
        },
        search: true
    }).trigger("reloadGrid");
}

function cesGrid1(){
    $("#cesGrid1").jqGrid("setGridParam", {
        postData: {
            formAction: "queryL120s01e1",
            thisOid: thisOid,
            rowNum: 5
        },
        search: true
    }).trigger("reloadGrid");
}

function cesGrid2(){
    $("#cesGrid2").jqGrid("setGridParam", {
        postData: {
            formAction: "queryL120s01e2",
            thisOid: thisOid,
            rowNum: 5
        },
        search: true
    }).trigger("reloadGrid");
}

function getL120s01c(frag){
    var $L120S01aForm = $("#L120S01aForm");
    $.ajax({
        type: "POST",
        handler: _handler,
        data: {
            formAction: "queryL120s01c",
            mainId: responseJSON.mainId,
            oid: thisOid,
            showMsg: true,
            frag: frag
        },
        success: function(responseData){
            var $L120S01aForm = $("#L120S01aForm");
            if (frag == 2 || frag == 4) {
                $L120S01aForm.find("#showGrade").hide();
            }
            else {
                $L120S01aForm.find("#showGrade").show();
            }
            $L120S01aForm.setData(responseData.L120S01aForm, false);
            if (frag == 3) {
                if (responseData.L120S01aForm.grade1 == undefined &&
                responseData.L120S01aForm.grade2 == undefined &&
                responseData.L120S01aForm.grade3 == undefined &&
                responseData.L120S01aForm.grade7 == undefined &&
                responseData.L120S01aForm.grade8 == undefined &&
                responseData.L120S01aForm.grade9 == undefined) {
                    $L120S01aForm.find(".hideMow1").hide();
                    $L120S01aForm.find(".hideMow2").hide();
                    $L120S01aForm.find(".hideMow3").hide();
                    //J-113-0141_12473_B1001 海外 外部信評之評等機構增列中華信評
                    $L120S01aForm.find(".hideMow7").hide();
                    //J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
                    $L120S01aForm.find(".hideMow8").hide();
                    $L120S01aForm.find(".hideMow9").hide();
                }
                else {
                    if (responseData.L120S01aForm.grade1 != undefined) {
                        $L120S01aForm.find(".hideMow1").show();
                    }
                    if (responseData.L120S01aForm.grade2 != undefined) {
                        $L120S01aForm.find(".hideMow2").show();
                    }
                    if (responseData.L120S01aForm.grade3 != undefined) {
                        $L120S01aForm.find(".hideMow3").show();
                    }
                    
                    //J-113-0141_12473_B1001 海外 外部信評之評等機構增列中華信評
                    if (responseData.L120S01aForm.grade7 != undefined) {
                        $L120S01aForm.find(".hideMow7").show();
                    }
                    
                    //J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
                    if (responseData.L120S01aForm.grade8 != undefined) {
                        $L120S01aForm.find(".hideMow8").show();
                    }
                    if (responseData.L120S01aForm.grade9 != undefined) {
                        $L120S01aForm.find(".hideMow9").show();
                    }
                }
            }
            if (frag == 5) {
                if (responseData.L120S01aForm.grade4 == undefined &&
                responseData.L120S01aForm.grade5 == undefined &&
                responseData.L120S01aForm.grade6 == undefined) {
                    $L120S01aForm.find(".hideMow4").hide();
                    $L120S01aForm.find(".hideMow5").hide();
                    $L120S01aForm.find(".hideMow6").hide();
                }
                else {
                    $L120S01aForm.find(".hideMow4").hide();
                    $L120S01aForm.find(".hideMow5").hide();
                    $L120S01aForm.find(".hideMow6").hide();
                    if (responseData.L120S01aForm.grade4 != undefined) {
                        $L120S01aForm.find(".hideMow4").show();
                    }
                    if (responseData.L120S01aForm.grade5 != undefined) {
                        $L120S01aForm.find(".hideMow5").show();
                    }
                    if (responseData.L120S01aForm.grade6 != undefined) {
                        $L120S01aForm.find(".hideMow6").show();
                    }
                }
            }
        }
    });
}

/**
 * 當地信評-查詢分行界面
 */
function tLocalTrust(){
    $("#fLocalTrust").find("#brno").val(userInfo.unitNo);
    $("#tLocalTrust").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lmss02["l120s01c.localgrade"],
        width: 400,
        height: 200,
        modal: true,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                if ($("#fLocalTrust").valid()) {
                    $.thickbox.close();
                    getL120s01c(5, true);
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function getL120s01c(frag, showMsg){
    var $L120S01aForm = $("#L120S01aForm");
    if (frag == 5) {
        $L120S01aForm.find("#crdTYear4").val("");
        $L120S01aForm.find(".hideMow4").hide();
        $L120S01aForm.find("#grade4").val("");
        
        $L120S01aForm.find("#crdTYear5").val("");
        $L120S01aForm.find(".hideMow5").hide();
        $L120S01aForm.find("#grade5").val("");
        
        $L120S01aForm.find("#crdTYear6").val("");
        $L120S01aForm.find(".hideMow6").hide();
        $L120S01aForm.find("#grade6").val("");
    }
    else 
        if (frag == 3) {
            $L120S01aForm.find("#crdTYear1").val("");
            $L120S01aForm.find(".hideMow1").hide();
            $L120S01aForm.find("#grade1").val("");
            
            $L120S01aForm.find("#crdTYear2").val("");
            $L120S01aForm.find(".hideMow2").hide();
            $L120S01aForm.find("#grade2").val("");
            
            $L120S01aForm.find("#crdTYear3").val("");
            $L120S01aForm.find(".hideMow3").hide();
            $L120S01aForm.find("#grade3").val("");
            
            //J-113-0141_12473_B1001 海外 外部信評之評等機構增列中華信評
            $L120S01aForm.find("#crdTYear7").val("");
            $L120S01aForm.find(".hideMow7").hide();
            $L120S01aForm.find("#grade7").val("");
            
            //J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
            $L120S01aForm.find("#crdTYear8").val("");
            $L120S01aForm.find(".hideMow8").hide();
            $L120S01aForm.find("#grade8").val("");
            
            $L120S01aForm.find("#crdTYear9").val("");
            $L120S01aForm.find(".hideMow9").hide();
            $L120S01aForm.find("#grade9").val("");
            
        }
    
    $.ajax({
        type: "POST",
        handler: _handler,
        data: {
            formAction: "queryL120s01c",
            mainId: responseJSON.mainId,
            oid: thisOid,
            frag: frag,
            brno: $("#fLocalTrust").find("#brno").val(),
            showMsg: showMsg
        },
        success: function(responseData){
        
            if (frag == 2 || frag == 4) {
                $L120S01aForm.find("#showGrade").hide();
            }
            else {
                $L120S01aForm.find("#showGrade").show();
            }
            
            
            $L120S01aForm.setData(responseData.L120S01aForm, false);
            if (frag == 3) {
                if (responseData.L120S01aForm.grade1 == undefined &&
                responseData.L120S01aForm.grade2 == undefined &&
                responseData.L120S01aForm.grade3 == undefined &&
                responseData.L120S01aForm.grade7 == undefined &&
                responseData.L120S01aForm.grade8 == undefined &&
                responseData.L120S01aForm.grade9 == undefined) {
                    $L120S01aForm.find(".hideMow1").hide();
                    $L120S01aForm.find(".hideMow2").hide();
                    $L120S01aForm.find(".hideMow3").hide();
                    //J-113-0141_12473_B1001 海外 外部信評之評等機構增列中華信評
                    $L120S01aForm.find(".hideMow7").hide();
                    //J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
                    $L120S01aForm.find(".hideMow8").hide();
                    $L120S01aForm.find(".hideMow9").hide();
                }
                else {
                    if (responseData.L120S01aForm.grade1 != undefined) {
                        $L120S01aForm.find(".hideMow1").show();
                    }
                    if (responseData.L120S01aForm.grade2 != undefined) {
                        $L120S01aForm.find(".hideMow2").show();
                    }
                    if (responseData.L120S01aForm.grade3 != undefined) {
                        $L120S01aForm.find(".hideMow3").show();
                    }
                    
                    //J-113-0141_12473_B1001 海外 外部信評之評等機構增列中華信評
                    if (responseData.L120S01aForm.grade7 != undefined) {
                        $L120S01aForm.find(".hideMow7").show();
                    }
                    
                    //J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
                    if (responseData.L120S01aForm.grade8 != undefined) {
                        $L120S01aForm.find(".hideMow8").show();
                    }
                    if (responseData.L120S01aForm.grade9 != undefined) {
                        $L120S01aForm.find(".hideMow9").show();
                    }
                }
            }
            
            if (frag == 5) {
            
                if (responseData.L120S01aForm.grade4 == undefined &&
                responseData.L120S01aForm.grade5 == undefined &&
                responseData.L120S01aForm.grade6 == undefined) {
                    $L120S01aForm.find(".hideMow4").hide();
                    $L120S01aForm.find(".hideMow5").hide();
                    $L120S01aForm.find(".hideMow6").hide();
                }
                else {
                    $L120S01aForm.find(".hideMow4").hide();
                    $L120S01aForm.find(".hideMow5").hide();
                    $L120S01aForm.find(".hideMow6").hide();
                    if (responseData.L120S01aForm.grade4 != undefined) {
                        $L120S01aForm.find(".hideMow4").show();
                    }
                    if (responseData.L120S01aForm.grade5 != undefined) {
                        $L120S01aForm.find(".hideMow5").show();
                    }
                    if (responseData.L120S01aForm.grade6 != undefined) {
                        $L120S01aForm.find(".hideMow6").show();
                    }
                }
            }
        }
    });
}

/**
 * 開啟借款人資料ThickBox內容(企金)
 *
 * p.s. 海外消金寫在 LMSS02APage02.js
 * @param oid
 */
function openDocAddBorrow(oid){
    var $L120S01aForm = $("#L120S01aForm");
    
	//J-106-0029-002  洗錢防制-新增洗錢防制頁籤
	$L120S01aForm.find(".noHideBt").show();
			
    var unitNo = userInfo.unitNo;
    if ((unitNo == "918" && responseJSON.mainDocStatus == "L1H") ||
    ((unitNo == "920" ||
    unitNo == "922" ||
    unitNo == "931" ||
    unitNo == "932" ||
    unitNo == "933" ||
    unitNo == "934" ||
    unitNo == "935") &&
    responseJSON.mainDocStatus == "L1C")) {
        $("#erjButton").find("button").show();
        $("#erjButton1").find("button").show();
    }
    else {
        $("#erjButton").find("button").hide();
        $("#erjButton1").find("button").hide();
    }
    //---
    var rejtCaseAdjMemo = $L120S01aForm.find("#rejtCaseAdjMemo").val();
    if (rejtCaseAdjMemo == undefined || rejtCaseAdjMemo == null || rejtCaseAdjMemo == "") {
        $("#L120S01aForm .hideMemo").hide();
    }
    else {
        $("#L120S01aForm .hideMemo").show();
    }
    
    var rejtCaseAdjMemo1 = $L120S01aForm.find("#rejtCaseAdjMemo1").val();
    if (rejtCaseAdjMemo1 == undefined || rejtCaseAdjMemo1 == null || rejtCaseAdjMemo1 == "") {
        $("#L120S01aForm .hideMemo1").hide();
    }
    else {
        $("#L120S01aForm .hideMemo1").show();
    }
    //---
//    if (responseJSON.docType == "1" &&
//    responseJSON.docKind == "1") {
    if (responseJSON.docType == "1") {
        if (responseJSON.docKind == "1") {
            $(".hBorrowData2").hide();
            $("#sitemOfBusi").show();
            $("#sprodMkt").hide();
            $("#sgrade").hide();
            $("#sIsPrint").hide();
        } else if (responseJSON.docKind == "2") {
            $("#sitemOfBusi").show();
		    $("#sprodMkt").show();
		    $("#sgrade").show();
		    $("#sIsPrint").show();
        }
    }
    else {
        $("#sitemOfBusi").hide();
        $("#sprodMkt").hide();
        $("#sgrade").hide();
        $("#sIsPrint").hide();
    }

    // J-110-0371 新版簽報書_個人
    var busCode = $("#L120S01aForm").find("#busCode").val();
    if(busCode == "060000" || busCode == "130300"){
        $(".sPersonal").show();
    } else {
        $(".sPersonal").hide();
    }

    $(".tab-c-item").show();
    
    gridCreditRiskReLoad();
    // J-112-0125 私募基金
    gridPrivateEquityReLoad('Y');
    gridPrivateEquityReLoad('N');
    uGroupInfoGridview01();
    
    // 此方法為點選新增或修改借款人資料時所開啟的ThickBox內容
    thisOid = oid;
    
    //每次開啟都是第一頁
    $("#tabs-w").tabs({
        selected: 0
    });
    $("#openDocaddborrow").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lmss02["l120s02.thickbox6"],
        width: 960,
        height: 500,
        modal: true,
        i18n: i18n.def,
        buttons: {
            "saveData": function(showMsg){
                var $L120S01aForm = $("#L120S01aForm");
                if ($L120S01aForm.find("#keyMan").val() ==
                "N") {
                    if (responseJSON.docType == 1 && responseJSON.docKind == 2 && responseJSON.docCode == 1) {
                        if (checkMonth($("#fxbDateM").val()) &&
                        checkMonth($("#fxeDateM").val()) &&
                        checkMonth($("#imbDateM").val()) &&
                        checkMonth($("#imeDateM").val()) &&
                        checkMonth($("#exbDateM").val()) &&
                        checkMonth($("#exeDateM").val()) &&
                        checkMonth($("#cntrbDateM").val()) &&
                        checkMonth($("#cntreDateM").val())) {
                            saveBorrow(thisOid, showMsg);
                        }
                        else {
                            return false;
                            //saveBorrow(thisOid, showMsg);
                        }
                    }
                    else {
                        saveBorrow(thisOid, showMsg);
                    }
                    // }
                }
                else {
                    if (responseJSON.docType == 1 && responseJSON.docKind == 2 && responseJSON.docCode == 1) {
                        if (checkMonth($("#fxbDateM").val()) &&
                        checkMonth($("#fxeDateM").val()) &&
                        checkMonth($("#imbDateM").val()) &&
                        checkMonth($("#imeDateM").val()) &&
                        checkMonth($("#exbDateM").val()) &&
                        checkMonth($("#exeDateM").val()) &&
                        checkMonth($("#cntrbDateM").val()) &&
                        checkMonth($("#cntreDateM").val())) {
                            saveBorrow(thisOid, showMsg);
                        }
                        else {
                            return false;
                            //saveBorrow(thisOid, showMsg);
                        }
                    }
                    else {
                        saveBorrow(thisOid, showMsg);
                    }
                }
                //								}
            },
            "del": function(){
                CommonAPI.confirmMessage(i18n.lmss02["l120s02.confirm1"], function(b){
                    if (b) {
                        // 是的function
                        $.ajax({
                            type: "POST",
                            handler: _handler,
                            data: {
                                formAction: "deleteBorrowMain",
                                mainId: responseJSON.mainId,
                                oid: oid
                            },
                            success: function(responseData){
                                var $showBorrowData = $("#showBorrowData");
                                $showBorrowData.reset();
                                $showBorrowData.setData(responseData.showBorrowData, false);
                                // 更新借款人基本資料Grid內容
                                $("#l120s01agrid").trigger("reloadGrid");
                                // 更新授信簽報書Grid內容
                                CommonAPI.triggerOpener("gridview", "reloadGrid");
                            }
                        });
                        $.thickbox.close();
                    }
                    else {
                        // 否的function
                        CommonAPI.showMessage(i18n.lmss02["l120s02.alert5"]);
                    }
                })
            },
            "close": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 檢查是否為正常的月份
 *
 * @param String
 */
function checkMonth(mm){
    if (mm != "") {
        if (mm > 12 || mm <= 00 || mm <= 0) {
            CommonAPI.showMessage(i18n.lmss02["l120s02.alert6"]);
            return false;
        }
        else {
            return true;
        }
    }
    else {
        return true;
    }
}

/**
 * 儲存主要借款人(含修改)
 */
function saveBorrow(oid, showMsg){
    FormAction.open = true;
    var errMsg = [];
    var $L120S01aForm = $("#L120S01aForm");
    $L120S01aForm.find(".number").each(function(i){
        var $this = $(this);
        $this.val(RemoveStringComma($this.val()));
    });
    
    var $L120S01fForm = $("#L120S01fForm");
    
    $L120S01fForm.find(".number").each(function(j){
        var $this = $(this);
        $this.val(RemoveStringComma($this.val()));
    });
    
    // 檢查信評是否引進
    var noTrust = true;
    $L120S01aForm.find(".trust").each(function(k){
        var $this = $(this);
        if ($this.html() != null && $this.html() != undefined && $this.html() != "") {
            noTrust = false;
        }
    });
    if (noTrust) {
        CommonAPI.showMessage(i18n.lmss02["l120s02.alert14"]);
        return;
    }
    
    // 檢查利害關係人是否引進	
    var noLiHai = true;
    var noLiHai33 = true;
    var mbRltVal = $L120S01aForm.find("[name='mbRlt']:radio:checked").val();
    var mbRlt33Val = $L120S01aForm.find("[name='mbRlt33']:radio:checked").val();
    var mhRlt44Val = $L120S01aForm.find("[name='mhRlt44']:radio:checked").val();
    var mhRlt45Val = $L120S01aForm.find("[name='mhRlt45']:radio:checked").val();
    //J-108-0178_05097_B1001 Web e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
    var caRlt206Val = false;
    //J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
    var localRltVal = false;
    
    $.ajax({
        handler: _handler,
        type: "POST",
        async: false,
        dataType: "json",
        data: {
            formAction: "getLiHaiFlag",
            mainId: responseJSON.mainid,
            custId: custId,
            dupNo: dupNo,
            groupNo: groupNo,
            groupName: groupName,
            mbRlt: mbRlt,
            mhRlt44: mhRlt44,
            mhRlt45: mhRlt45,
            busCode: busCode
        },
        success: function(jsonLiHi){
        	
        	//J-108-0178_05097_B1001 Web e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
        	var caRlt206Val = $L120S01aForm.find("[name='caRlt206']:radio:checked").val();
            if (jsonLiHi.showCaRlt206 == "N") {
            	caRlt206Val = true;
            }
            
            //J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
            var localRltVal = $L120S01aForm.find("[name='localRlt']:radio:checked").val();
            if (jsonLiHi.showLocalRlt == "N") {
            	localRltVal = true;
            }
            
            if ((mbRltVal != null && mbRltVal != undefined && mbRltVal != "") &&
            (mhRlt44Val != null && mhRlt44Val != undefined && mhRlt44Val != "") &&
            (mhRlt45Val != null && mhRlt45Val != undefined && mhRlt45Val != "")&&
            (caRlt206Val != null && caRlt206Val != undefined && caRlt206Val != "")&&
            (localRltVal != null && localRltVal != undefined && localRltVal != "")) {
                noLiHai = false;
            }
            if (noLiHai) {
            	errMsg.push(i18n.lmss02["l120s02.alert15"]);
//                CommonAPI.showMessage(i18n.lmss02["l120s02.alert15"]);
//                return;
            }
            
        }
    });
    
    if (mbRlt33Val != null) {
        noLiHai33 = false;
    }
    if (noLiHai33) {
        errMsg.push(i18n.lmss02["l120s02.alert32"]);
    }
    
    // 檢查信用風險管理遵循是否引進	
    var noCreditRisk = true;
    var count = $("#gridCreditRisk").jqGrid('getGridParam', 'records');
    if (count > 0) {
        noCreditRisk = false;
    }
    if (noCreditRisk) {
        errMsg.push(i18n.lmss02["l120s02.alert30"]);
    }
    
    // 檢查借款人所屬集團、利害關係人、行業對項別與信用風險管理遵循是否一致
    var groupNo = $L120S01aForm.find("#groupNo").val();
    var groupName = $L120S01aForm.find("#groupName").val();
    var mbRlt = $("input[name='mbRlt']:radio:checked").val();
    var mhRlt44 = $("input[name='mhRlt44']:radio:checked").val();
    var mhRlt45 = $("input[name='mhRlt45']:radio:checked").val();
    var custId = $L120S01aForm.find("#custId").val();
    var dupNo = $L120S01aForm.find("#dupNo").val();
    var busCode = $L120S01aForm.find("#busCode").val();
    
    $.ajax({
        handler: _handler,
        type: "POST",
        async: false,
        dataType: "json",
        data: {
            formAction: "chkL120s01m",
            mainId: responseJSON.mainid,
            custId: custId,
            dupNo: dupNo,
            groupNo: groupNo,
            groupName: groupName,
            mbRlt: mbRlt,
            mhRlt44: mhRlt44,
            mhRlt45: mhRlt45,
            busCode: busCode
        },
        success: function(json){
            if (json.notSame == "Y") {
                errMsg.push(i18n.lmss02["l120s02.alert31"]);
            }
        }
    });
    
    
    //提示使用者尚未填妥項目
    if (!$L120S01aForm.valid()) {
        return false;
    }
    if (!$("#L120S01gForm_1").valid()) {
        return false;
    }
    if (!$("#L120S01gForm_2").valid()) {
        return false;
    }
    if (!$L120S01fForm.valid()) {
        return false;
    }
    
    //檢查存放款及外匯往來情形幣別是否為空
    if ($L120S01fForm.find("#rcdFlag").attr("checked")) {
        var noCurr = false;
        $L120S01fForm.find("[name $= 'Curr']").each(function(i){
            var $this = $(this);
            if ($this.next("[name $= 'Amt']").val() != "" && $this.val() == "") {
                noCurr = true;
            }
        });
        if (noCurr) {
            CommonAPI.showErrorMessage(i18n.lmss02["l120s02.alert28"]);
            return;
        }
    }
    if ($("#L120S01aForm").find("#groupName").is(":visible")) {
        var groupName = $L120S01aForm.find("#groupName").val();
        if (groupName == undefined || groupName == null || groupName == "") {
            // l120s02.alert16=尚未引進隸屬集團企業!
            errMsg.push(i18n.lmss02["l120s02.alert16"]);
            /*	             
             return CommonAPI.showMessage(i18n.lmss02["l120s02.alert16"]);
             */
        }
    }
	
	//J-104-0240-001  Web e-Loan授信簽報書與額度明細表增加列示借款戶所屬集團企業代號、名稱與註記。
	var groupNo = $L120S01aForm.find("#groupNo").val();
	if(groupNo){
		var groupBadFlag = $L120S01aForm.find("#groupBadFlag").val();
	    if (!groupBadFlag) {
	        // l120s02.alert37=尚未引進集團列管註記!
	        errMsg.push(i18n.lmss02["l120s02.alert37"]);
	    }
	}
	
    
    
    var busCode = $L120S01aForm.find("#busCode").val();
    var custClass = $L120S01aForm.find("#custClass").val();
    if ((busCode == undefined || busCode == null || busCode == "") || (custClass == undefined || custClass == null || custClass == "")) {
        // l120s02.alert29=尚未引進行業對象別/客戶類別！
        errMsg.push(i18n.lmss02["l120s02.alert29"]);
        /*             
         return CommonAPI.showMessage(i18n.lmss02["l120s02.alert29"]);
         */
    }
    
    //J-113-0075_12473_B1002 異常通報案件檢查 已重辦信用評等 及 未重辦信用評等原因
    var reRatingFlag = '';
    var notReRatingRsn = '';
    var otherRsnDesc = '';
    if("4" == responseJSON.docCode){
    	reRatingFlag = $("input[name='reRatingFlag']:radio:checked").val();
    	notReRatingRsn = $("input[name='notReRatingRsn']:radio:checked").val();
    	otherRsnDesc = $("#otherRsnDesc").val();
    }
    
    // 儲存主要借款人(含修改)
    $.ajax({
        type: "POST",
        handler: _handler,
        action: 'saveBorrow',
        data: {
            formAction: "saveBorrow",
            L120S01aForm: JSON.stringify($L120S01aForm.serializeData()),
            L120S01fForm: JSON.stringify($L120S01fForm.serializeData()),
            oid: oid,
            keyMan: $L120S01aForm.find("#keyMan").val(),
            chairman: $L120S01aForm.find("#chairman").html(),
            invMDscr: $("#invMDscr").val(),//getCkeditor("invMDscr"),
            fxbDateY: $("#fxbDateY").val(),
            fxbDateM: $("#fxbDateM").val(),
            fxeDateY: $("#fxeDateY").val(),
            fxeDateM: $("#fxeDateM").val(),
            imbDateY: $("#imbDateY").val(),
            imbDateM: $("#imbDateM").val(),
            imeDateY: $("#imeDateY").val(),
            imeDateM: $("#imeDateM").val(),
            exbDateY: $("#exbDateY").val(),
            exbDateM: $("#exbDateM").val(),
            exeDateY: $("#exeDateY").val(),
            exeDateM: $("#exeDateM").val(),
            cntrbDateY: $("#cntrbDateY").val(),
            cntrbDateM: $("#cntrbDateM").val(),
            cntreDateY: $("#cntreDateY").val(),
            cntreDateM: $("#cntreDateM").val(),
            idDscr1: $("#idDscr1").val(),//getCkeditor("idDscr1"),
            idDscr2: $("#idDscr2").val(),//getCkeditor("idDscr2"),
            custRlt: "",
            custPos: "",
            rcdFlag: $("#L120S01gForm_1").find("#rcdFlag:checkbox").attr("checked"),
            runFlag: $("#L120S01gForm_1").find("#runFlag:checkbox").attr("checked"),
            finFlag: $("#L120S01gForm_2").find("#finFlag:checkbox").attr("checked"),
            errMsg: errMsg,
            showMsg: showMsg,
            prodMkt: $("#L120S01aForm").find("#prodMkt").val(),//getCkeditor("prodMkt"),
            isPrint: $("#L120S01aForm").find("#isPrint:checkbox").attr("checked"),
            //J-113-0075_12473_B1002 異常通報案件檢查 已重辦信用評等 及 未重辦信用評等原因，送到後端檢核
            reRatingFlag: reRatingFlag,
            notReRatingRsn: notReRatingRsn,
            otherRsnDesc: otherRsnDesc
        },
        success: function(responseData){
            var $L120S01aForm = $("#L120S01aForm");
            FormAction.open = false;
            // alert(JSON.stringify(responseData));
            // showBorrowData
            if (responseData.keyMan == "Y") {
                // 為主要借款人
                $("#showBorrowData").setData(responseData.showBorrowData, false);
                $L120S01aForm.find("#keyMan").attr("checked", true);
                $("#chk_radio1").hide();
                $L120S01aForm.find("#custRlt").html("");
                $L120S01aForm.find("#custPos option:eq(0)").attr("selected", true);
            }
            else {
                // 為非主要借款人
                $("#showBorrowData").setData(responseData.showBorrowData, false);
                $L120S01aForm.find("#keyMan").attr("checked", false);
                $("#chk_radio1").show();
            }
            // 更新Grid內容
            $("#l120s01agrid").trigger("reloadGrid");
            gridPrivateEquityReLoad('Y');   // J-112-0125 私募基金
            if (!responseData.ExistKeyMan) {
                if (responseData.errorMsg != null && responseData.errorMsg != undefined && responseData.errorMsg != "") {
                    CommonAPI.showErrorMessage(responseData.errorMsg);
                }
                // 更新授信簽報書Grid內容
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            }
            else {
                var check;
                CommonAPI.confirmMessage(i18n.lmss02('l120s02.confirm2', {
                    'borrower': responseData.oldBorrower
                }), function(b){
                    if (b) {
                        // 是的function
                        check = true;
                    }
                    else {
                        // 否的function
                        // User 決定不覆蓋主要借款人
                        check = false;
                    }
                    $.ajax({
                        // 依照使用者選擇決定執行覆蓋主要借款人動作
                        type: "POST",
                        handler: _handler,
                        data: {
                            formAction: "modifyBorrow",
                            mainOid: responseData.haveKeyManDocNo,
                            docOid: responseData.haveKeyManOid,
                            oldOid: responseData.needtoAddOid,
                            errorMsg: responseData.errorMsg,
                            errorMsg2: responseData.errorMsg2,
                            errorMsg3: responseData.errorMsg3,
                            check: check,
                            showMsg: showMsg
                        },
                        success: function(responseData){
                            // alert(check);
                            // 更新Grid內容
                            $("#l120s01agrid").trigger("reloadGrid");
                            if (responseData.check) {
                                // 要覆蓋主要借款人
                                $("#showBorrowData").setData(responseData.showBorrowData, false);
                                $L120S01aForm.find("#keyMan").attr("checked", true);
                                $("#chk_radio1").hide();
                                $L120S01aForm.find("#custRlt").html("");
                                $L120S01aForm.find("#custPos option:eq(0)").attr("selected", true);
                                if (responseData.errorMsg != null && responseData.errorMsg != undefined && responseData.errorMsg != "") {
                                    CommonAPI.showErrorMessage(responseData.errorMsg);
                                }
                                else {
                                    //Show 出儲存成功訊息
                                    responseData.SaveSuccess;
                                }
                                // 更新授信簽報書Grid內容
                                CommonAPI.triggerOpener("gridview", "reloadGrid");
                                // 更新Grid內容
                                $("#l120s01agrid").trigger("reloadGrid");
                            }
                            else 
                                if (!responseData.check) {
                                    // 不覆蓋主要借款人
                                    $L120S01aForm.find("#keyMan").attr("checked", false);
                                    //為非主要借款人
                                    $L120S01aForm.find("#keyMan").val("N");
                                    $("#custRltShow").val("");
                                    $("#custRlt").html("");
                                    $("#custPos option:eq(0)").attr("selected", true);
                                    $("#chk_radio1").show();
                                    if (responseData.errorMsg != null && responseData.errorMsg != undefined && responseData.errorMsg != "") {
                                        CommonAPI.showErrorMessage(responseData.errorMsg);
                                    }
                                    else {
                                        //Show 出儲存成功訊息
                                        responseData.SaveSuccess;
                                    }
                                }
                        }
                    });
                })
            }
            
            // 金額加上逗號
            $L120S01aForm.find(".number").each(function(i){
                var $this = $(this);
                $this.val(money_format($this.val()));
            });
            $("#L120S01gForm_1").find(".number").each(function(i){
                var $this = $(this);
                $this.val(money_format($this.val()));
            });
            $("#L120S01gForm_2").find(".number").each(function(i){
                var $this = $(this);
                $this.val(money_format($this.val()));
            });
            $L120S01fForm.find(".number").each(function(i){
                var $this = $(this);
                if ($this.attr("id") != "fxYear" &&
                $this.attr("id") != "imYear" &&
                $this.attr("id") != "exYear" &&
                $this.attr("id") != "fxbDateY" &&
                $this.attr("id") != "fxeDateY" &&
                $this.attr("id") != "imbDateY" &&
                $this.attr("id") != "imeDateY" &&
                $this.attr("id") != "exbDateY" &&
                $this.attr("id") != "exeDateY" &&
                $this.attr("id") != "cntrbDateY" &&
                $this.attr("id") != "cntreDateY" &&
                $this.attr("id") != "fxbDateM" &&
                $this.attr("id") != "fxeDateM" &&
                $this.attr("id") != "imbDateM" &&
                $this.attr("id") != "imeDateM" &&
                $this.attr("id") != "exbDateM" &&
                $this.attr("id") != "exeDateM" &&
                $this.attr("id") != "cntrbDateM" &&
                $this.attr("id") != "cntreDateM") {
                    $this.val(money_format($this.val()));
                }
            });
        }
    });
}

/**
 * 檢查財務比率有無資料，有資料則顯示並自動勾選其項目
 */
function checkExist(){
    $("#L120S01gForm_2").find("#tabs-3_show span").each(function(b){
        var $this = $(this);
        if ($this.html() != "") {
            $this.parent().parent().show();
            if ($this.parent().parent().attr("id").substr(0, 7) != null &&
            $this.parent().parent().attr("id").substr(0, 7) != undefined &&
            $this.parent().parent().attr("id").substr(0, 7) != "") {
                $("#L120S01gForm_1").find("#" + $this.parent().parent().attr("id")).attr("checked", true);
            }
        }
    });
}

function buildL120s01eKind2(json){
    $("#tabs-3_showTable tr:not('.head')").remove();
    if ((json.L120S01gForm_2.hasOwnProperty("finRatioName"))) {
        var text = "";
        $.each(json.L120S01gForm_2.finRatioName, function(key, value){
            //先畫出html，再將值塞進去
            text += "<tr id='' class='classfinItem'>" + "\n";
            text += "<td class='hd1' style='text-align: center'>" + value + "</td>" + "\n";
            text += "<td align='center'><input type='text' name='finRatio_C_" + key + "' class='field canEdit2 rt' id='finRatio_C_" + key + "' readonly='readonly' /></td>" + "\n"
            text += "<td align='center'><input type='text' name='finRatio_B_" + key + "' class='field canEdit2 rt' id='finRatio_B_" + key + "' readonly='readonly' /></td>" + "\n"
            text += "<td align='center'><input type='text' name='finRatio_A_" + key + "' class='field canEdit2 rt' id='finRatio_A_" + key + "' readonly='readonly' /></td>" + "\n"
            text += "</tr>"
        });
        $("#tabs-3_showTable").append(text);
        
        if ((json.L120S01gForm_2.hasOwnProperty("finRatioC"))) {
            $.each(json.L120S01gForm_2.finRatioC, function(key, value){
                $("#finRatio_C_" + key).val(value);
            });
        }
        if ((json.L120S01gForm_2.hasOwnProperty("finRatioB"))) {
            $.each(json.L120S01gForm_2.finRatioB, function(key, value){
                $("#finRatio_B_" + key).val(value);
            });
        }
        if ((json.L120S01gForm_2.hasOwnProperty("finRatioA"))) {
            $.each(json.L120S01gForm_2.finRatioA, function(key, value){
                $("#finRatio_A_" + key).val(value);
            });
        }
        
        $("#finRatioYear_C").val("").val(json.L120S01gForm_2.finRatioYear_C);
        $("#finRatioYear_B").val("").val(json.L120S01gForm_2.finRatioYear_B);
        $("#finRatioYear_A").val("").val(json.L120S01gForm_2.finRatioYear_A);
    }
}

/**
 * 主要借款人關係選擇ThickBox
 */
function openCustRlt(){
    var $custRltTd = $("#custRltTd");
    // 初始化借款人標題
    $("#trRel").hide();
    $("#relation").empty();
    $("#custRlt_main option:eq(0)").attr("selected", true);
    $custRltTd.find("#custRlt_content1 option:eq(0)").attr("selected", true);
    $custRltTd.find("#custRlt_content2 option:eq(0)").attr("selected", true);
    $custRltTd.find("#custRlt_content3 option:eq(0)").attr("selected", true);
    $custRltTd.find("#custRlt_content4 option:eq(0)").attr("selected", true);
    $custRltTd.find("#custRlt_content1").hide();
    $custRltTd.find("#custRlt_content2").hide();
    $custRltTd.find("#custRlt_content3").hide();
    $custRltTd.find("#custRlt_content4").hide();
    // 查詢與主要借款人關係
    $.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "queryCustRlt",
            thisOid: thisOid,
            isOther: false
        },
        success: function(json){
            var $custRltTd = $("#custRltTd");
            if (json.isOther) {
                if (json.isNull) {
                    // 如果為空則初始化選項
                    $("#custRlt_main option:eq(0)").attr("selected", true);
                    $custRltTd.find("#custRlt_content3").hide();
                    $custRltTd.find("#custRlt_content4").hide();
                    $("#trRel").hide();
                }
                else {
                    // 其他綜合關係
                    $("#custRlt_main option:eq(3)").attr("selected", true);
                    $("#trRel").show();
                    $("#relation").append(DOMPurify.sanitize($("#custRlt_main option:selected").text()));
                    $custRltTd.find("#custRlt_content3 option").each(function(j){
                        var $this = $(this);
                        if ($this.attr("value") == json.custRlt_content1) {
                            $this.attr("selected", true);
                            $this.parents().show();
                        }
                    });
                    $custRltTd.find("#custRlt_content4 option").each(function(k){
                        var $this = $(this);
                        if ($this.attr("value") == json.custRlt_content2) {
                            $this.attr("selected", true);
                            $this.parents().show();
                        }
                    });
                }
            }
            else {
                $("[name='custRlt_content'] option").each(function(i){
                    // 非其他綜合關係
                    var $this = $(this);
                    if ($this.attr("value") == json.custRlt_content) {
                        switch (json.custRlt_main) {
                            case "1":
                                $("#custRlt_main option:eq(1)").attr("selected", true);
                                $("#trRel").show();
                                $("#relation").append(DOMPurify.sanitize($("#custRlt_main option:selected").text()));
                                break;
                            case "2":
                                $("#custRlt_main option:eq(2)").attr("selected", true);
                                $("#trRel").show();
                                $("#relation").append(DOMPurify.sanitize($("#custRlt_main option:selected").text()));
                                break;
                            default:
                                $("#trRel").hide();
                                break;
                        }
                        $this.attr("selected", true);
                        $this.parents().show();
                    }
                });
            }
        }
    });
    
    var openCustRlt = $("#thickboxCustRlt").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lmss02["l120s02.thickbox7"],
        width: 640,
        height: 200,
        align: 'center',
        valign: 'bottom',
        modal: true,
        i18n: i18n.lmss02,
        buttons: {
            "l120s02.thickbox1": function(showMsg){
                var $custRltTd = $("#custRltTd");
                if ($("#custRlt_main option:selected").val() == "" ||
                $("#custRltTd :nth-child(" +
                		DOMPurify.sanitize($('#custRlt_main option:selected').val()) +
                ") option:selected").val() ==
                "") {
                    CommonAPI.showMessage(i18n.lmss02["l120s02.alert7"]);
                }
                else {
                    // 初始化關係類別
                    $("#custRltShow").val("");
                    $("#custRlt").html("");
                    if ($("#custRlt_main option:selected").val() ==
                    "3") {
                        // 設定存取到後端的值
                        $("#custRlt").html(DOMPurify.sanitize($("#custRltTd").find("#custRlt_content3 option:selected").val()) +
                        		DOMPurify.sanitize($custRltTd.find("#custRlt_content4 option:selected").val()));
                        // 設定顯示給user看的內容
                        $("#custRltShow").val($("#custRltTd").find("#custRlt_content3 option:selected").val() +
                        $custRltTd.find("#custRlt_content4 option:selected").val() +
                        " " +
                        $custRltTd.find("#custRlt_content3 option:selected").text() +
                        "-" +
                        $custRltTd.find("#custRlt_content4 option:selected").text());
                    }
                    else {
                        // 設定存取到後端的值
                        $("#custRlt").html(DOMPurify.sanitize($("#custRltTd :nth-child(" +
                        		DOMPurify.sanitize($('#custRlt_main option:selected').val()) +
                        ") option:selected").val()));
                        // 設定顯示給user看的內容
                        $("#custRltShow").val($("#custRltTd :nth-child(" +
                        		DOMPurify.sanitize($('#custRlt_main option:selected').val()) +
                        ") option:selected").val() +
                        " " +
                        $("#custRltTd :nth-child(" +
                        		DOMPurify.sanitize($('#custRlt_main option:selected').val()) +
                        ") option:selected").text());
                    }
                    $.thickbox.close();
                }
            },
            "l120s02.thickbox2": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 引進徵信資料按鈕ThickBox
 */
function setValue(){
    // 引進徵信資料按鈕ThickBox
	$("[name=set]").closest("td").css("background", "#FFFFFF");
    cesGrid1();
    cesGrid2();
    getCesMainId();
    return false;
}

/**
 * 修改營運概況資料按鈕ThickBox
 */
function editPanel2(){
    // 複製Form資料到可編輯Form上
    copyDataByForm("L120S01gForm_1", "editFPanel2", "canEdit1", "_", "", "%");
    if ($("#_finRatioName4").val() == "") {
        $("#_finRatioD4").attr("readOnly", true);
        $("#_finRatioC4").attr("readOnly", true);
        $("#_finRatioB4").attr("readOnly", true);
        $("#_finRatioA4").attr("readOnly", true);
    }
    else {
        $("#_finRatioD4").attr("readOnly", false);
        $("#_finRatioC4").attr("readOnly", false);
        $("#_finRatioB4").attr("readOnly", false);
        $("#_finRatioA4").attr("readOnly", false);
    }
    $("#editPanel2").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lmss02["l120s01e.btn2"],
        width: 800,
        height: 400,
        modal: true,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var finYear_D = $("#_finYear_D").val();
                var finYear_C = $("#_finYear_C").val();
                var finYear_B = $("#_finYear_B").val();
                var finYear_A = $("#_finYear_A").val();
                
                if (finYear_D != "" && (finYear_D == finYear_C || finYear_D == finYear_B || finYear_D == finYear_A)) {
                    CommonAPI.showErrorMessage("項目/年度日期不可相同");
                    return false;
                }
                
                if (finYear_C != "" && (finYear_C == finYear_B || finYear_C == finYear_A)) {
                    CommonAPI.showErrorMessage("項目/年度日期不可相同");
                    return false;
                }
                if (finYear_B != "" && (finYear_B == finYear_A)) {
                    CommonAPI.showErrorMessage("項目/年度日期不可相同");
                    return false;
                }
                
                
                if ($("#editFPanel2").valid()) {
                    $.ajax({
                        type: "POST",
                        handler: "lms1205formhandler",
                        data: {
                            formAction: "editBorrowPage02",
                            thisOid: thisOid,
                            editFPanel2: JSON.stringify($("#editFPanel2").serializeData())
                        }
					}).done(function(responseData){
						var $L120S01gForm_1 = $("#L120S01gForm_1");
						$L120S01gForm_1.setData(responseData.L120S01gForm_1, false);

						var newRunUnit = $L120S01gForm_1.find("#runUnit").val();
						var newRunCurr = $L120S01gForm_1.find("#runCurr").val();

						var $L120S01aForm_1 = $("#L120S01aForm");
						$L120S01aForm_1.find("#runUnit").val(newRunUnit);
						$L120S01aForm_1.find("#runCurr").val(newRunCurr);

						$.thickbox.close();
						$.thickbox.close();
						CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                    });
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 修改財務壯況資料按鈕ThickBox
 */
function editPanel3(){

    $("#tabs-3_showCopy").thickbox({
        title: i18n.lmss02["l120s01e.btn2"],
        width: 800,
        height: 400,
        modal: true,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        open: function(){
            $("#copyResult").empty();
            $("#copyResult").append($("#tabs-3_showTable").clone());
            
            $("#copyResult").find("input").each(function(){
                var $this = $(this);
                var id = $this.attr("id");
                var name = $this.attr("name");
                $this.attr("id", "_" + id);
                $this.attr("name", "_" + name);
                $this.attr("readOnly", false);
            });
            
            $("#copyResult").find("input[name^='_finRatioYear_']").datepicker();
        },
        close: function(){
            $("#copyResult").empty();
        },
        buttons: {
        
            "sure": function(){
                var finRatioYear_C = $("#_finRatioYear_C").val();
                var finRatioYear_B = $("#_finRatioYear_B").val();
                var finRatioYear_A = $("#_finRatioYear_A").val();
                
                if (finRatioYear_C != "" && (finRatioYear_C == finRatioYear_B || finRatioYear_C == finRatioYear_A)) {
                    CommonAPI.showErrorMessage("項目/年度日期不可相同");
                    return false;
                }
                if (finRatioYear_B != "" && (finRatioYear_B == finRatioYear_A)) {
                    CommonAPI.showErrorMessage("項目/年度日期不可相同");
                    return false;
                }
                
                $.ajax({
                    type: "POST",
                    handler: "lms1205formhandler",
                    data: {
                        formAction: "editBorrowPage03",
                        thisOid: thisOid,
                        finRatioData: JSON.stringify($("#copyResult").serializeData())
                    }
				}).done(function(json){
					$("#copyResult").empty();
					$.thickbox.close();
					$.thickbox.close();
					buildL120s01eKind2(json);
                });
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
    
    return;
    
    // 依據財務狀況項目顯示
    var $editFPanel3 = $("#editFPanel3");
    $("#L120S01gForm_2 .classfinItem").each(function(i){
        var $this = $(this);
        if ($this.is(":visible")) {
            $editFPanel3.find("#_" + $this.attr("id")).show();
        }
        else {
            $editFPanel3.find("#_" + $this.attr("id")).hide();
        }
    });
    // 複製Form資料到可編輯Form上
    copyDataByForm("L120S01gForm_2", "editFPanel3", "canEdit2", "__", "", "%");
    $("#editPanel3").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lmss02["l120s01e.btn2"],
        width: 800,
        height: 400,
        modal: true,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                if ($("#editFPanel3").valid()) {
                    $.ajax({
                        type: "POST",
                        handler: "lms1205formhandler",
                        data: {
                            formAction: "editBorrowPage03",
                            thisOid: thisOid,
                            editFPanel3: JSON.stringify($("#editFPanel3").serializeData())
                        }
					}).done(function(responseData){
						var $L120S01gForm_2 = $("#L120S01gForm_2");
						$L120S01gForm_2.setData(responseData.L120S01gForm_2, false);
						$.thickbox.close();
						$.thickbox.close();
						CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                    });
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function getCesMainId(){
    // 引進徵信資料按鈕ThickBox
    $("#getCes").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lmss02["l120s02.thickbox9"],
        width: 640,
        height: 480,
        modal: true,
        align: "center",
        valign: "bottom",
        i18n: i18n.lmss02,
        buttons: {
            "l120s02.thickbox1": function(){
                // 資信簡表
                var row1 = $("#cesGrid1").getGridParam('selrow');
                var list1 = "";
                var data1 = $("#cesGrid1").getRowData(row1);
                list1 = data1.mainId;
                var gaapFlag = data1.gaapFlag + "";
                // 徵信報告
                var row2 = $("#cesGrid2").getGridParam('selrow');
                var list2 = "";
                var data2 = $("#cesGrid2").getRowData(row2);
                list2 = data2.mainId;
                list1 = (list1 == undefined ? "" : list1);
                list2 = (list2 == undefined ? "" : list2);
                var finRatio;
                var fssType;
                
                var run = function(){
                    if (list1 != "" || list2 != "") {
                        $.ajax({
                            handler: "lms1205formhandler",
                            type: "POST",
                            dataType: "json",
                            data: {
                                formAction: "getL120s01e",
                                mainId: responseJSON.mainId,
                                cesMainId1: list1,
                                cesMainId2: list2,
                                thisOid: thisOid,
                                finItem: finRatio,
                                gaapFlag: gaapFlag,
                                fssType: fssType
                            },
                            success: function(borrowData){
                                //var borrowData = $.extend(borrowData, json);
                                var $formIdDscr1 = $("#formIdDscr1");
                                var $L120S01gForm_1 = $("#L120S01gForm_1");
                                var $formIdDscr2 = $("#formIdDscr2");
                                var $L120S01gForm_2 = $("#L120S01gForm_2");
                                var $L120S01fForm = $("#L120S01fForm");
                                var $L120S01aForm = $("#L120S01aForm");
                                //第一頁籤
                                $L120S01aForm.setData(borrowData.L120S01aForm, false);
                                
                                /*
                                 // 第二頁籤
                                 $formIdDscr1.find("#idDscr1").val("");
                                 $L120S01gForm_1.reset();
                                 // 第三頁籤
                                 $formIdDscr2.find("#idDscr2").val("");
                                 $L120S01gForm_2.reset();
                                 // 第四頁籤
                                 $L120S01fForm.reset();
                                 */
                                // 第二頁籤
                                if ($formIdDscr1 && list2 != "") {
                                    $formIdDscr1.setData(borrowData.formIdDscr1);
                                }
                                if ($L120S01gForm_1) {
                                    $L120S01gForm_1.setData(borrowData.L120S01gForm_1);
                                }
                                
                                // 控制"引進徵信相關資料隱藏顯示功能"										
                                if (borrowData.L120S01gForm_1.rcdFlag == "Y") {
                                    $L120S01gForm_1.find("#rcdFlag").attr("checked", true);
                                    $L120S01gForm_1.find(".hideThis").show();
                                    $formIdDscr1.find(".hideThis").show();
                                    //$L120S01gForm_1.find(".cesCheck").attr("checked", true);
                                }
                                else {
                                    $L120S01gForm_1.find("#rcdFlag").attr("checked", false);
                                    $L120S01gForm_1.find(".hideThis").hide();
                                    $formIdDscr1.find(".hideThis").hide();
                                    //$L120S01gForm_1.find(".cesCheck").attr("checked", false);
                                }
                                if (borrowData.L120S01gForm_1.runFlag == "Y") {
                                    $L120S01gForm_1.find("#runFlag").attr("checked", true);
                                    $L120S01gForm_1.find("#tabs-2_show").show();
                                }
                                else {
                                    $L120S01gForm_1.find("#runFlag").attr("checked", false);
                                    $L120S01gForm_1.find("#tabs-2_show").hide();
                                }
                                $L120S01gForm_1.find("#rcdFlag:checkbox").attr("checked", true);
                                // 第三頁籤
                                if ($formIdDscr2 && list2 != "") {
                                    $formIdDscr2.setData(borrowData.formIdDscr2);
                                }
                                if ($L120S01gForm_2) {
                                    $L120S01gForm_2.setData(borrowData.L120S01gForm_2);
                                }
                                buildL120s01eKind2(borrowData);
                                // 控制"引進徵信相關資料隱藏顯示功能"										
                                if (borrowData.L120S01gForm_1.rcdFlag == "Y") {
                                    $L120S01gForm_2.find(".hideThis").show();
                                    $formIdDscr2.find(".hideThis").show();
                                    //$L120S01gForm_2.find(".cesCheck").attr("checked", true);
                                }
                                else {
                                    $L120S01gForm_2.find(".hideThis").hide();
                                    $formIdDscr2.find(".hideThis").hide();
                                    //$L120S01gForm_2.find(".cesCheck").attr("checked", false);
                                }
                                if (borrowData.L120S01gForm_2.finFlag == "Y") {
                                    $L120S01gForm_2.find("#finFlag").attr("checked", true);
                                    $L120S01gForm_2.find("#tabs-3_show").show();
                                }
                                else {
                                    $L120S01gForm_2.find("#finFlag").attr("checked", false);
                                    $L120S01gForm_2.find("#tabs-3_show").hide();
                                }
                                // 第四頁籤
                                if ($L120S01fForm) {
                                    $L120S01fForm.setData(borrowData.L120S01fForm);
                                }
                                // 控制"引進徵信相關資料隱藏顯示功能"										
                                if (borrowData.L120S01gForm_1.rcdFlag == "Y") {
                                    $L120S01fForm.find(".hideThis").show();
                                    $L120S01fForm.find("#rcdFlag").attr("checked", true);
                                }
                                else {
                                    $L120S01fForm.find(".hideThis").hide();
                                    $L120S01fForm.find("#rcdFlag").attr("checked", false);
                                }
                                $("#showBorrowData").setData(borrowData.showBorrowData, false);
                               
                                
                            }
                        });
                        $.thickbox.close();
                    }
                    else {
                        $.thickbox.close();
                        CommonAPI.showMessage(i18n.lmss02["l120s02.alert1"]);
                    }
                };
                
                if (list1 != "") {
                    $.ajax({
                        handler: "lms1205formhandler",
                        type: "POST",
                        data: {
                            formAction: "getFinRatioItems",
                            cesMainId1: list1,
                            gaapFlag: gaapFlag
                        },
                        success: function(json){
                            $("#setRatioValueTable").empty();
                            var text = "";
                            fssType = json.fssType + "";
                            gaapFlag = json.gaapFlag + "";
                            $.each(json.finRatioItem, function(key, value){
                                switch (gaapFlag) {
                                    case "1":
                                    	//IFRS
                                        switch (fssType) {
                                            case "M":
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "C":
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;    
                                            case "L"://租賃業
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "B"://
                                                if (key == "r80" || key == "r77" || key == "r82") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "H"://
                                                if (key == "r20" || key == "r13" || key == "r24") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "S"://
                                                if (key == "r11" || key == "r12" || key == "r23") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "I"://
                                                if (key == "r20" || key == "r25" || key == "r26") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "P"://
                                                if (key == "r20" || key == "r25" || key == "r73") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "V"://
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            default :
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;    
                                        }
                                        break
                                    case "2":
                                    	//EAS
                                    	//J-109-0422_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
                                        switch (fssType) {
                                            case "M":
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "C":
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;    
                                            case "L"://租賃業
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "B"://
                                                if (key == "r80" || key == "r77" || key == "r82") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "H"://
                                                if (key == "r20" || key == "r13" || key == "r24") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "S"://
                                                if (key == "r11" || key == "r12" || key == "r23") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "I"://
                                                if (key == "r20" || key == "r25" || key == "r26") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "P"://
                                                if (key == "r20" || key == "r25" || key == "r73") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "V"://
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            default :
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;    
                                        }
                                        break    
                                    case "0":
                                        switch (fssType) {
                                            case "1":
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "2":
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                            case "3":
                                                if (key == "r20" || key == "r13" || key == "r58") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                                
                                            case "4":
                                                if (key == "r47" || key == "r48" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                                
                                            case "5":
                                                if (key == "r11" || key == "r12" || key == "r37") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                                
                                            case "6":
                                                if (key == "r80" || key == "r81" || key == "r82") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                                
                                            case "7":
                                                if (key == "r11" || key == "r12" || key == "r20") {
                                                    //預設勾選
                                                    text += "<tr><td style='background:rgb(192, 192, 192);'><label><input type='checkbox' checked='checked' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                else {
                                                    text += "<tr><td><label><input type='checkbox' name='finRatio' id='" + key + "' value='" + key + "' />" + value + "</label></td></tr>";
                                                }
                                                break;
                                        }
                                        
                                        break;
                                        
                                }
                            });
                            
                            $("#setRatioValueTable").append(text);
                            
                            //J-102-0147 取消存貨、應收款項、總資產、固定資產等五項週轉率(日)及營業利益率變動情形之科目選擇
                            $('input:checkbox[name="finRatio"][value="r55"]').attr('disabled', true); //存貨週轉率(日)
                            $('input:checkbox[name="finRatio"][value="r51"]').attr('disabled', true); //應收款項週轉率(日)
                            $('input:checkbox[name="finRatio"][value="r53"]').attr('disabled', true); //應付款項週轉率(日)
                            $('input:checkbox[name="finRatio"][value="r59"]').attr('disabled', true); //總資產週轉率(日)
                            $('input:checkbox[name="finRatio"][value="r57"]').attr('disabled', true); //固定資產週轉率(日)
                            $('input:checkbox[name="finRatio"][value="r37"]').attr('disabled', true); //營業利益率變動情形
                            $("#setRatioValue").thickbox({
                                title: i18n.lmss02["l120s02.thickbox8"],
                                width: 340,
                                height: 380,
                                modal: false,
                                align: "center",
                                valign: "bottom",
                                i18n: i18n.def,
                                buttons: {
                                    "sure": function(){
                                        finRatio = new Array();
                                        var $checkObject = $("input[name='finRatio']:checked");
                                        if ($checkObject.size() == 0) {
                                            CommonAPI.showMessage(i18n.lmss02["l120s02.alert9"]);
                                        }
                                        else {
                                            $("input[name='finRatio']:checked").each(function(i){
                                                var $this = $(this);
                                                finRatio[i] = $this.val();
                                            });
                                            $.thickbox.close();
                                            run();
                                        }
                                    },
                                    "cancel": function(){
                                        $.thickbox.close();
                                    }
                                }
                            });
                            
                        }
                        
                    });
                }
                else {
                    run();
                }
                
                
                
            },
            "l120s02.thickbox2": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 隱藏顯示財務比率項目
 *
 * @param obj
 * @param idName
 */
function showHide(obj, idName){
    // 隱藏顯示財務比率項目
    if (obj.checked) {
        if (idName.substring(0, 1) == ".") {
            $(idName).show();
        }
        else {
            $('#' + idName).show();
        }
    }
    else {
        if (idName.substring(0, 1) == ".") {
            $(idName).hide();
        }
        else {
            $('#' + idName).hide();
        }
    }
}

function checkUncheck(obj, idName){
    // 隱藏顯示財務比率項目
    if (obj.checked) {
        if (idName.substring(0, 1) == ".") {
            $(idName).attr("checked", true);
        }
        else {
            $('#' + idName).attr("checked", true);
        }
    }
    else {
        if (idName.substring(0, 1) == ".") {
            $(idName).attr("checked", false);
        }
        else {
            $('#' + idName).attr("checked", false);
        }
    }
}

/**
 * 取得行業對象別與客戶類別
 */
function getBusCdAndCustClass(){
    var $L120S01aForm = $("#L120S01aForm");
    $.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getBusCdAndCustClass",
            mainId: responseJSON.mainId,
            custId: $L120S01aForm.find("#custId").html(),
            dupNo: $L120S01aForm.find("#dupNo").html()
        },
        success: function(json){
        
            $L120S01aForm.setData(json, false);
        }
    });
}



/**  信用風險管理遵循grid  */
function gridCreditRiskReLoad(){
    var $L120S01aForm = $("#L120S01aForm");
    var custId = $L120S01aForm.find("#custId").val();
    var dupNo = $L120S01aForm.find("#dupNo").val();
    gridCreditRisk.jqGrid("setGridParam", {
        postData: {
            formAction: "queryCreditRisk",
            mainId: responseJSON.mainid,
            custId: custId,
            dupNo: dupNo
        },
        search: true
    }).trigger("reloadGrid");
    
}

/**  J-112-0125 私募基金grid  */
function gridPrivateEquityReLoad(flag){
    var $L120S01aForm = $("#L120S01aForm");
    var custId = $L120S01aForm.find("#custId").val();
    var dupNo = $L120S01aForm.find("#dupNo").val();
    var gridFund = $("#gridPrivateEquity");
    if(flag == "N") {
        gridFund = $("#gridBfPrivateEquity");
    }
    gridFund.jqGrid("setGridParam", {
        postData: {
            formAction: "queryL120s01t",
            mainId: responseJSON.mainid,
            custId: custId,
            dupNo: dupNo,
            flag: flag
        },
        search: true
    }).trigger("reloadGrid");
}

/**  J-112-0125 清除私募基金  */
function gridPrivateEquityClean(flag){
    var $L120S01aForm = $("#L120S01aForm");
    var custId = $L120S01aForm.find("#custId").val();
    var dupNo = $L120S01aForm.find("#dupNo").val();
    var gridFund = $("#gridPrivateEquity");
    if(flag == "N") {
        gridFund = $("#gridBfPrivateEquity");
    }
    $.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "deleteL120s01ts",
            mainId: responseJSON.mainid,
            custId: custId,
            dupNo: dupNo,
            flag: flag
        },
        success: function(json){
        }
    });
}

function applyCreditRisk(needAsk){
    var count = $("#gridCreditRisk").jqGrid('getGridParam', 'records');
    var $L120S01aForm = $("#L120S01aForm");
    var custId = $L120S01aForm.find("#custId").val();
    var dupNo = $L120S01aForm.find("#dupNo").val();
    
    if (count > 0) {
        if (needAsk == true) {
            //"執行引進後會刪除已存在之信用風險管理遵循表，是否確定執行？"
            CommonAPI.confirmMessage(i18n.lmss02["l120s01m.confirm1"], function(b){
                if (b) {
                    //是的function
                    $.ajax({
                        handler: _handler,
                        type: "POST",
                        dataType: "json",
                        data: {
                            formAction: "deleteL120s01m",
                            custId: custId,
                            dupNo: dupNo,
                            mainId: responseJSON.mainid
                        },
                        success: function(json){
                            //$.thickbox.close();
                            // 開始產生實績彙總表
                            importL120S01m(needAsk);
                        }
                    });
                }
            });
        }
        else {
            $.ajax({
                handler: _handler,
                type: "POST",
                dataType: "json",
                data: {
                    formAction: "deleteL120s01m",
                    custId: custId,
                    dupNo: dupNo,
                    mainId: responseJSON.mainid
                },
                success: function(json){
                    //$.thickbox.close();
                    // 開始產生實績彙總表
                    importL120S01m(needAsk);
                }
            });
        }
        
    }
    else {
        importL120S01m(needAsk);
    }
}

/**
 * 執行產生往來實績彙總表
 */
function importL120S01m(needAsk){
    var $L120S01aForm = $("#L120S01aForm");
    var groupNo = $L120S01aForm.find("#groupNo").val();
    var groupName = $L120S01aForm.find("#groupName").val();
    var mbRlt = $("input[name='mbRlt']:radio:checked").val();
    var mhRlt44 = $("input[name='mhRlt44']:radio:checked").val();
    var mhRlt45 = $("input[name='mhRlt45']:radio:checked").val();
    var custId = $L120S01aForm.find("#custId").val();
    var dupNo = $L120S01aForm.find("#dupNo").val();
    var busCode = $L120S01aForm.find("#busCode").val();
    var custName = $L120S01aForm.find("#custName").val();

    $.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "importL120s01m",
            mainId: responseJSON.mainid,
            custId: $L120S01aForm.find("#custId").val(),
            dupNo: $L120S01aForm.find("#dupNo").val(),
            groupNo: groupNo,
            groupName: groupName,
            mbRlt: mbRlt,
            mhRlt44: mhRlt44,
            mhRlt45: mhRlt45,
            busCode: busCode,
            custName: custName,
			needAsk:needAsk
        },
        success: function(json){
            // 更新實績彙總表Grid
            $("#gridCreditRisk").jqGrid("setGridParam", {
                postData: {
                    formAction: "queryCreditRisk",
                    custId: custId,
                    dupNo: dupNo
                },
                search: true
            }).trigger("reloadGrid");
            
            if (needAsk == true) {
                //執行成功
                CommonAPI.showMessage(i18n.lmss02["l120s01m.message1"]);
            }
            
        }
    });
}

/**
 * 查詢並開啟往來實績彙總表ThickBox
 */
function tL120s01m(cellvalue, options, rowObject){
    var oid = rowObject.oid;
    var $L120S01aForm = $("#L120S01aForm");
    var custId = $L120S01aForm.find("#custId").val();
    var dupNo = $L120S01aForm.find("#dupNo").val();
    
    // 進行查詢 
    $.ajax({ //查詢主要借款人資料
        handler: _handler,
        type: "POST",
        dataType: "json",
        action: "queryL120s01m",
        data: {
            oid: oid,
            custId: custId,
            dupNo: dupNo,
            mainId: responseJSON.mainId
        },
        success: function(json){
            var $formL120s01m = $("#formL120s01m");
            
          //J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。			
			var grpYear = json.formL120s01m.grpYear;
			var grpGrrd = json.formL120s01m.grpGrade;
			if(grpYear){
				//判斷2017以後為新版，之前為舊版
				if(parseInt(grpYear, 10) >= 2017){
					var obj = CommonAPI.loadCombos(["GroupGrade2017"]); 
			        //評等等級
			        $("#grpGrade").setItems({
			            item: obj.GroupGrade2017,
			            format: "{key}"
			        });
	
				}else{
					var obj = CommonAPI.loadCombos(["GroupGrade"]);
			        //評等等級
			        $("#grpGrade").setItems({
			            item: obj.GroupGrade,
			            format: "{key}"
			        });
				}
	
			}else{
				var obj = CommonAPI.loadCombos(["GroupGrade"]);
		        
		        //評等等級
		        $("#grpGrade").setItems({
		            item: obj.GroupGrade,
		            format: "{key}"
		        });

			}
			
            $formL120s01m.setData(json.formL120s01m);
            $formL120s01m.readOnlyChilds(true);
            
            if (json.formL120s01m.dataNotShow_020 == "Y") {
                $formL120s01m.find("#data_020").hide();
				$formL120s01m.find("#data_022").hide();
//              J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
                $formL120s01m.find("#data_023").hide();
            }
            else {
                $formL120s01m.find("#data_020").show();
				
				//J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
				if($formL120s01m.find("#localGroup").val() == "" ){
					//舊案
			        $formL120s01m.find("#data_022").show();	
				}else{
					$formL120s01m.find("#data_022").hide();
				}
				//J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
                $formL120s01m.find("#data_023").show();
            }
            
            if (json.formL120s01m.dataNotShow_080 == "Y") {
                $formL120s01m.find("#data_080").hide();
				$formL120s01m.find("#data_082").hide();
				$formL120s01m.find("#data_083").hide();
            }
            else {
                $formL120s01m.find("#data_080").show();
				
				//J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
				if($formL120s01m.find("#localGroup").val() == "" ){
					//舊案
			        $formL120s01m.find("#data_082").show();
				}else{
					$formL120s01m.find("#data_082").hide();
				}
				$formL120s01m.find("#data_083").show();
            }
            
            if (json.formL120s01m.dataNotShow_030 == "Y") {
                $formL120s01m.find("#data_030").hide();
				$formL120s01m.find("#data_032").hide();
				$formL120s01m.find("#data_033").hide();
            }
            else {
                $formL120s01m.find("#data_030").show();
				
				//J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
				if($formL120s01m.find("#localGroup").val() == "" ){
					//舊案
			        $formL120s01m.find("#data_032").show();
				}else{
					$formL120s01m.find("#data_032").hide();
				}
				$formL120s01m.find("#data_033").show();
            }
            
            if (json.formL120s01m.grpNo == "") {
                $formL120s01m.find("#grpYear").hide();
                $formL120s01m.find("#grpGrade").hide();
            }else{
				$formL120s01m.find("#grpYear").show();
                $formL120s01m.find("#grpGrade").show();
			}
            
            if (json.formL120s01m.mbRlt == "1") {
				$formL120s01m.find("#dataDate_060").show();
                $formL120s01m.find("#dataDate_070").hide();
            }
            else {
                $formL120s01m.find("#dataDate_060").hide();
				$formL120s01m.find("#dataDate_070").show();
            }
            
			//G-104-0097-001 Web e-Loan 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
			if (json.formL120s01m.hasLocalAmt == "Y") {
				//J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
            	if (json.formL120s01m.countryType == "TH") {
            		 $formL120s01m.find(".showLocal").show();
            		 $formL120s01m.find(".showLocal_KH").hide();
                     $formL120s01m.find("#localNetValCurr1").val(json.formL120s01m.localNetValCurr);
            	}else if (json.formL120s01m.countryType == "KH") {
            		 $formL120s01m.find(".showLocal").hide();
           		     $formL120s01m.find(".showLocal_KH").show();
                     $formL120s01m.find("#localNetValCurr1_KH").val(json.formL120s01m.localNetValCurr_KH);
            	}else{
            		 $formL120s01m.find(".showLocal").hide();
           		     $formL120s01m.find(".showLocal_KH").hide();
                     $formL120s01m.find("#localNetValCurr1").val('');
                     $formL120s01m.find("#localNetValCurr1_KH").val('');
            	}
            	
				
			}else{
				//J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
                $formL120s01m.find(".showLocal").hide();
                $formL120s01m.find(".showLocal_KH").hide();
                $formL120s01m.find("#localNetValCurr1").val("");
                $formL120s01m.find("#localNetValCurr1_KH").val("");
			}	
			
			if (json.formL120s01m.dataNotShow_090 == "Y") {
                $formL120s01m.find("#data_090").hide();
				$formL120s01m.find("#data_092").hide();
				$formL120s01m.find("#showLocalGroup").val("");
            }
            else {
				
				//J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
				if($formL120s01m.find("#localGroup").val() == "" ){
					//舊案
			        $formL120s01m.find("#data_090").hide();
				    $formL120s01m.find("#data_092").hide();
					$formL120s01m.find("#showLocalGroup").val("");
				}else{
					$formL120s01m.find("#data_090").show();
				    $formL120s01m.find("#data_092").show();
					//無集團不顯示集團代號
					if($formL120s01m.find("#localGroup").val()=="000000000"){
						$formL120s01m.find("#showLocalGroup").val("N.A.");
					}else{
						$formL120s01m.find("#showLocalGroup").val(parseInt($formL120s01m.find("#localGroup").val(),10));
					}	
				}
				
                
            }
						
            $("#tL120s01m").thickbox({ // 使用選取的內容進行彈窗
                title: i18n.lmss02["l120s01m.item26"], // 「授信信用風險管理」遵循檢核
                width: 965,
                height: 480,
                modal: true,
                i18n: i18n.def,
                buttons: {
                
                    "close": function(){
                        $.thickbox.close();
                        //                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                        //                            if (res) {
                        //                                $.thickbox.close();
                        //                            }
                        //                        });
                    }
                }
            });
        }
    });
}

function showDetail(relType,hasLocal){

    var $L120S01aForm = $("#L120S01aForm");
    var custId = $L120S01aForm.find("#custId").val();
    var dupNo = $L120S01aForm.find("#dupNo").val();
    
	
    // 進行查詢 
    $.ajax({ //查詢主要借款人資料
        handler: _handler,
        type: "POST",
        dataType: "json",
        action: "queryL120s01o",
        data: {
            custId: custId,
            dupNo: dupNo,
            relType: relType,
            mainId: responseJSON.mainId,
			hasLocal:hasLocal
        },
        success: function(json){
            var $formL120s01o = $("#formL120s01o");
            $formL120s01o.find("#showDetailHtml").html(DOMPurify.sanitize(json.showDetailResult));
            
			//G-104-0097-001 Web e-Loan 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。						
			//海外_編製中("01O"),	
			//海外_待補件("07O"), 	
			//會簽後修改編製中("01K"),	
            var buttons = {};
			if(responseJSON.mainDocStatus =="01O" || responseJSON.mainDocStatus =="07O"  || responseJSON.mainDocStatus =="01K"  ){
				//$formL120s01o.readOnlyChilds(false);   =>不可以設為false 因為有其他不需要開放編輯的input欄位
				if(hasLocal == "Y"){
					 buttons["sure"] = function(){
					    $.ajax({  	    
		                    type: "POST",
							dataType: "json",
		                    handler: _handler,
		                    data: {
		                        formAction: "resetLocalL120S01OAndReCaculate",
		                        custId: custId,
					            dupNo: dupNo,
					            relType: relType,
					            mainId: responseJSON.mainId,
								FORML120S01O: JSON.stringify($formL120s01o.serializeData())
		                    },
		                    success: function(json){
		                        $.thickbox.close();
		                        $.thickbox.close(); 
		                    }
		                });
					};
				}
				
			}else{
				$formL120s01o.readOnlyChilds(true);
			}
			
			buttons ["cancel" ] = function(){
                 $. thickbox.close ();
            };
			
            $("#tL120s01o").thickbox({ // 使用選取的內容進行彈窗
                title: i18n.lmss02["l120s01m.item26"], // 「授信信用風險管理」遵循檢核
                width: 965,
                height: 480,
                modal: true,
                i18n: i18n.def,
                buttons: buttons
            });
        }
    });
    
}

function printCreditRisk(){
    //列印信用風險管理遵循
    var count = $("#gridCreditRisk").jqGrid('getGridParam', 'records');
    var $L120S01aForm = $("#L120S01aForm");
    var custId = $L120S01aForm.find("#custId").val();
    var dupNo = $L120S01aForm.find("#dupNo").val();
    
    //先檢查是否有資料
    if (count == 0) {
        //EFD0002=INFO|報表無資料|
        return CommonAPI.showErrorMessage(i18n.msg["EFD0002"]);
    }
    
    $.form.submit({
        url: "../../simple/FileProcessingService",
        target: "_blank",
        data: {
            rptOid: "R30" + "^" + "",
            mainId: responseJSON.mainId,
            mainOid: $("#mainOid").val(),
            fileDownloadName: "lms1205r30.pdf",
            serviceName: "lms1205r01rptservice",
            custId: custId,
            dupNo: dupNo,
            mode: "ONE"
        }
    });
}

function delCreditRisk(){
    var count = $("#gridCreditRisk").jqGrid('getGridParam', 'records');
    var $L120S01aForm = $("#L120S01aForm");
    var custId = $L120S01aForm.find("#custId").val();
    var dupNo = $L120S01aForm.find("#dupNo").val();
    
    if (count > 0) {
        //是否確定要刪除信用風險管理遵循表？
        CommonAPI.confirmMessage(i18n.lmss02["l120s01m.confirm2"], function(b){
            if (b) {
                //是的function
                $.ajax({
                    handler: _handler,
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "deleteL120s01m",
                        custId: custId,
                        dupNo: dupNo,
                        mainId: responseJSON.mainid
                    },
                    success: function(json){
                    
                    
                        $("#gridCreditRisk").jqGrid("setGridParam", {
                            postData: {
                                formAction: "queryCreditRisk",
                                custId: custId,
                                dupNo: dupNo
                            },
                            search: true
                        }).trigger("reloadGrid");
                        
                        //刪除成功
                        CommonAPI.showMessage(i18n.lmss02["l120s01m.message2"]);
                    }
                });
            }
        });
        
        
    }
    else {
        //無資料可刪除
        CommonAPI.showMessage(i18n.lmss02["l120s01m.message3"]);
    }
}


/*
 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
 */
function applyPrivateEquityData(){
    var $L120S01aForm = $("#L120S01aForm");
    $.ajax({
        type: "POST",
        handler: _handler,
        data: {
            formAction: "applyPrivateEquityData",
            mainId: responseJSON.mainId,
            custId: $L120S01aForm.find("#custId").val(),
            dupNo: $L120S01aForm.find("#dupNo").val()
        },
        success: function(responseData){
            $L120S01aForm.setData(responseData.L120S01aForm, false);
            // J-112-0125 私募基金
            gridPrivateEquityReLoad('Y');
            gridPrivateEquityReLoad('N');
        }
    });
}

/*
 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
 */
function setPrivateEquityData(){
	
	$.ajax({
        type: "POST",
        handler: _handler,
        data: {
            formAction: "getPrivateEquityData"
        },
        success: function(obj){
               
			    //若所屬之私募基金未列於選單中，請先洽授管處趙晉毅襄理(分機2593)新增。
			    $("#privateEquityNotify").val(obj.privateEquityNotify);
				
				var $select = $("#privateEquityData");
				$("#privateEquityData").val('');
				$select.empty();
                var tempStr = "";
				if (obj.privateEquityData) {
                    for (var i in obj.privateEquityData) {
                        
                        var tmp = $("<option></option>");
                        tmp.attr("value", obj.privateEquityData[i].key);
                        tmp.text(obj.privateEquityData[i].key +"."+obj.privateEquityData[i].val);
						$select.append(tmp);  
                    }                        

                }

				var getGrpData = $("#thickboxPrivateEquity").thickbox({
		        // 使用選取的內容進行彈窗
		        title: "",
		        width: 640,
		        height: 200,
		        align: 'center',
		        valign: 'bottom',
		        modal: true,
		        i18n: i18n.def,
		        buttons: {
		            "sure": function(showMsg){		
					            
					   $("#privateEquityNo").val($("#privateEquityData").val());
					   $("#privateEquityName").val($("#privateEquityData").find("option:selected").text().substring(5));
                       $.thickbox.close();
		            },
		            "cancel": function(){
		                API.confirmMessage(i18n.def['flow.exit'], function(res){
		                    if (res) {
		                        $.thickbox.close();
		                    }
		                });
		            }
		        }
		    });
			 
        }
    });
	
    
}

// J-112-0125 私募基金
function addPrivateEquity(){
    var $L120S01aForm = $("#L120S01aForm");
    var custId = $L120S01aForm.find("#custId").val();
    var dupNo = $L120S01aForm.find("#dupNo").val();
    var peNoList;
    var peNoSize = 0;
    $.ajax({
        handler: _handler,
        data: {
            formAction: "getL120s01tList",
            mainId: responseJSON.mainid,
            custId: custId,
            dupNo: dupNo,
            flag: "Y"
        },
        success: function(obj){
            peNoList = obj.peNoList;
            peNoSize = obj.peNoSize;
        }
    }).done(function(){
        if(peNoSize >= 3) {
            return CommonAPI.showErrorMessage(i18n.def["grid.maxSelrow"].replace('${0}','').replace('${1}','3'));
        } else {
            $.ajax({
                type: "POST",
                handler: _handler,
                data: {
                  formAction: "getPrivateEquityData"
                },
                success: function(obj){
                    //若所屬之私募基金未列於選單中，請先洽授管處趙晉毅襄理(分機2593)新增。
                    $("#privateEquityNotify").val(obj.privateEquityNotify);

                    var $select = $("#privateEquityData");
                    $("#privateEquityData").val('');
                    $select.empty();
                    var tempStr = "";
					if (obj.privateEquityData) {
					    for (var i in obj.privateEquityData) {
					        if($.inArray(obj.privateEquityData[i].key, peNoList) == -1){
					            var tmp = $("<option></option>");
					            tmp.attr("value", obj.privateEquityData[i].key);
					            tmp.text(obj.privateEquityData[i].key + "." + obj.privateEquityData[i].val);
					            $select.append(tmp);
					        }
					    }
					}

                    var getGrpData = $("#thickboxPrivateEquity").thickbox({
                        // 使用選取的內容進行彈窗
                        title: "",
                        width: 640,
                        height: 200,
                        align: 'center',
                        valign: 'bottom',
                        modal: true,
                        i18n: i18n.def,
                        buttons: {
                            "sure": function(){
                                $.ajax({
                                    handler: _handler,
                                    data: {
                                        formAction: "addL120s01t",
                                        mainId: responseJSON.mainid,
                                        custId: custId,
                                        dupNo: dupNo,
                                        flag: "Y",
                                        peNo: $("#privateEquityData").val(),
                                        peName: $("#privateEquityData").find("option:selected").text().substring(5)
                                    },
                                    success: function(obj){
                                        $("#gridPrivateEquity").trigger("reloadGrid");
                                    }
                                });
                                $.thickbox.close();
                            },
                            "cancel": function(){
                                API.confirmMessage(i18n.def['flow.exit'], function(res){
                                    if (res) {
                                        $.thickbox.close();
                                    }
                                });
                            }
                        }
                    });
                }
            });
        }
    });
}

// J-112-0125 私募基金
function delPrivateEquity(){
    var row = $("#gridPrivateEquity").getGridParam('selrow');
    if (!row) {
        return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
    }
    else {
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var data = $("#gridPrivateEquity").getRowData(row);
                var oid = data.oid;
                $.ajax({
                    handler: _handler,
                    data: {
                        formAction: "deleteL120s01tByOid",
                        oid: oid
                    },
                    success: function(obj){
                        $("#gridPrivateEquity").trigger("reloadGrid");
                    }
                });
            }
        });
    }
}

//J-106-0029-003  洗錢防制-新增實質受益人
function newToglePersonBT(callValue, setting, data, rType){//新增連保人
    //先將表格初始化
    $("#L120S01PForm").reset();
    
    //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
    var thisRType = "";
    if (data && data.rType) {
        thisRType = data.rType;
    }
    else {
        thisRType = rType;
    }
    
    var $L120S01aForm = $("#L120S01aForm");
    
    
    $.ajax({
        handler: "amlrelateformhandler",
        data: {//把資料轉成json
            formAction: "queryL120s01p",
            oid: (data && data.oid) || "",
            showMsg: true,
            rType: thisRType
        },
        success: function(obj){
            $('#L120S01PForm').injectData(obj);
            
            if (!$('#L120S01PForm').find("#rType").val()) {
                //新增時後端不會傳rType上來，故由前端塞值
                $('#L120S01PForm').find("#rType").val(thisRType);
            }
            $("input[name='toglePersonType']").trigger('change');
			
			var btnAction = API.createJSON([{
				key: i18n.def['close'],
                value: function(){
                    $.thickbox.close();
                }
		    }]);
			
			if (userInfo.ssoUnitNo == "900") {
			   btnAction = API.createJSON([{
					key: i18n.def['sure'],
	                value: function(){
	                    var $L120S01PForm = $("#L120S01PForm");
                        if (!$L120S01PForm.valid()) {
                            return;
                        }
                        
                        var toglePersonType = $("[name=toglePersonType]:checked").val();
                        
                        
                        //判斷新增的是自然人還是法人，再去分要存到哪個table，並且同時儲存到主檔TABLE裡
                        $.ajax({
                            handler: "amlrelateformhandler",
                            action: "saveL120s01p",
                            data: {//把資料轉成json
                                type: $("[name=toglePersonType]:checked").val(),
                                oid: $("#l120s01pFormOid").val(),
                                mainId: responseJSON.mainId,
                                custId: $L120S01aForm.find("#custId").val(),
                                dupNo: $L120S01aForm.find("#dupNo").val(),
                                rType: thisRType,
                                L120S01PForm: JSON.stringify($("#L120S01PForm").serializeData())
                            },
                            success: function(responseData){
                                $.thickbox.close();
                                $('#gridviewNatural').trigger('reloadGrid');
                                $('#gridviewCorporate').trigger('reloadGrid');
                            }
                        });
	                }
				}, {
					key: i18n.def['close'],
	                value: function(){
	                    $.thickbox.close();
	                }	
			    }]);
			} 
			
			
            $("#newToglePersonBox").thickbox({ // 使用選取的內容進行彈窗
                title: i18n.lmss02["L120S01p.beneficiary"],
                width: 490,
                height: 400,
                modal: true,
                align: "center",
                valign: "bottom",
                readOnly: false,
                i18n: i18n.def,
                buttons: btnAction
            });
        }
    });
}

/*
 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
 */
function applyIsNewCust(){
    var $L120S01aForm = $("#L120S01aForm");
    $.ajax({
        type: "POST",
        handler: _handler,
        data: {
            formAction: "applyIsNewCust",
            custId: $L120S01aForm.find("#custId").val(),
            dupNo: $L120S01aForm.find("#dupNo").val()
        },
        success: function(responseData){
        	$L120S01aForm.setData(responseData.L120S01aForm, false);
        }
    });
}

function openthe2(list){
	$.ajax({
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		data :
		{
			formAction : "getC140m01a2",
			page : "61",
			mainId : responseJSON.mainId,
			cesOid : list.oid,
			cesMainId : list.mainId
		},
		success : function(json) {
			$("#tabForm01").reset();
			$("#tabForm01").setData(json,false);
			thickboxCes6();
		}
	});
}

function thickboxCes6(){
	$("#thickboxCes6").thickbox({     // 使用選取的內容進行彈窗
        title : i18n.lmss02["l120s05.thickbox8"],
        width : 960,
        height : 480,
        modal : true,
        i18n:i18n.lmss02,
        buttons: {
            "l120s05.thickbox9": function() {
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if(res){
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}





