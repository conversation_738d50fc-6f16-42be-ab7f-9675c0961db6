package com.mega.eloan.lms.crs.flow;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.dao.C240M01ADao;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Component
public class LMS2405Flow extends AbstractFlowHandler {
	
	@Resource
	C240M01ADao c240m01aDao;
	
	@Transition(node = "區中心_編製中", value = "呈主管")
	public void send(FlowInstance instance) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C240M01A c240m01a = c240m01aDao.findByOid(instance
				.getId().toString());
		c240m01a.setHqAppraiserId(user.getUserId());
		c240m01aDao.save(c240m01a);
	}
	
	@Transition(node = "確認", value = "核定")
	public void apply(FlowInstance instance) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C240M01A c240m01a = c240m01aDao.findByOid(instance
				.getId().toString());
		c240m01a.setApprover(user.getUserId());
		c240m01a.setApproveTime(CapDate.getCurrentTimestamp());
		c240m01a.setReManagerId(user.getUserId());
		c240m01a.setReCheckTime(CapDate.getCurrentTimestamp());
		c240m01aDao.save(c240m01a);
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return C240M01A.class;
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return RetrialDocStatusEnum.class;
	}
}
