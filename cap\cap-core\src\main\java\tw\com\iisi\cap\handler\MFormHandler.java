/*_
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */

package tw.com.iisi.cap.handler;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import org.springframework.util.ReflectionUtils;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.action.IAction;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * 直接以method name來執行(formAction傳入method name)
 * 若未指定method時，預設執行doWork()
 * </pre>
 * 
 * @since 2010/12/8
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/12/8,iristu,new
 *          </ul>
 */
public abstract class MFormHandler extends FormHandler {

    /**
     * <pre>
     * 直接以method name來執行
     * </pre>
     * 
     * @param formAction
     *            action
     * @return IAction
     */
    @Override
    public IAction getAction(String formAction) {
        return new MethodExecuteAction(this);
    }

    /**
     * <pre>
     * 以method name來執行動作
     * </pre>
     */
    private class MethodExecuteAction implements IAction {

        /**
         * 要執行動作的Handler
         */
        MFormHandler executeHandler;

        /**
         * 建構子
         * 
         * @param executeObj
         */
        public MethodExecuteAction(MFormHandler executeObj) {
            this.executeHandler = executeObj;
        }

        /*
         * 以傳入資料帶有的資訊調用對應方法
         * 
         * @see tw.com.iisi.cap.action.IAction#doWork(com.iisigroup.cap.component.PageParameters)
         */
        @SuppressWarnings("rawtypes")
        @Override
        public IResult doWork(PageParameters params) throws CapException {
            IResult rtn = null;
            String methodId = params.getString(FORM_ACTION);
            if (CapString.isEmpty(methodId)) {
                methodId = "doWork";
            }
            try {
                Class[] paramTypes = { PageParameters.class };
                Method method = ReflectionUtils.findMethod(executeHandler.getClass(), methodId, paramTypes);
                if (method != null) {
                    rtn = (IResult) method.invoke(executeHandler, params);
                }
            } catch (InvocationTargetException e) {
                if (e.getCause() instanceof CapMessageException) {
                    throw (CapMessageException) e.getCause();
                } else if (e.getCause() instanceof CapException) {
                    throw (CapException) e.getCause();
                } else {
                    throw new CapException(e.getCause(), executeHandler.getClass());
                }
            } catch (Throwable t) {
                throw new CapException(t, executeHandler.getClass());
            }
            return rtn;
        }

    }// ;

    /**
     * <pre>
     * 若未傳送formAction值，則default執行此method
     * </pre>
     * 
     * @param params
     *            RequestParameters
     * @param parent
     *            Component
     * @return IResult
     */
    public IResult doWork(PageParameters params) throws CapException {
        return null;
    }

}
