/*
 * C124M01B.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/** ESG 綠色企業資料 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L290M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"stkNo", "receiveDate"}))
public class L290M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Column(name="mainId", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String mainId;

	/** 證券代號 **/
	@Column(name="stkNo", length=6, columnDefinition="CHAR(6)", nullable=false)
	private String stkNo;

	/** 證券統編 **/
	@Column(name="stkCMP", length=10, columnDefinition="CHAR(10)", nullable=false)
	private String stkCMP;

	/** 收件日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="receiveDate", columnDefinition="DATE", nullable=false)
	private Date receiveDate;

	/** 資料來源 **/
	@Column(name="source", length=30, columnDefinition="CHAR(30)", nullable=false)
	private String source;

	/** 公司名稱 **/
	@Column(name="stkName", length=100, columnDefinition="CHAR(100)")
	private String stkName;

	/** Sustainalytics ESG 風險評分(100-0, 0分最佳) **/
	@Column(name="esgScore", columnDefinition="DECIMAL(6,2)")
	private BigDecimal esgScore;

	/** FTSE Russell ESG 評級 (0-5, 5級最佳)  **/
	@Column(name="esgLevel", columnDefinition="DECIMAL(6,2)")
	private BigDecimal esgLevel;

	/** ISS 環境揭露評級 (1-10, 1級最佳)  **/
	@Column(name="issLevel1", columnDefinition="DECIMAL(6,2)")
	private BigDecimal issLevel1;

	/** ISS 社會揭露評級 (1-10, 1級最佳) **/
	@Column(name="issLevel2", columnDefinition="DECIMAL(6,2)")
	private BigDecimal issLevel2;

	/** 台灣公司治理評鑑  (前5%最佳)  **/
	@Column(name="companyGovernance", columnDefinition="CHAR(100)")
	private String companyGovernance;

	/**
	 * 刪除註記日期
	 */
	@Column(columnDefinition = "TIMESTAMP")
	private Timestamp deletedTime;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** ISS ESG評級 (A-D，A最佳)  **/
	@Column(name="issLevel", columnDefinition="CHAR(5)")
	private String issLevel;

	/** S&P Global ESG評分(0-100，100分最佳) **/
	@Column(name="spScore", columnDefinition="DECIMAL(6,2)")
	private BigDecimal spScore;

	/** MSCI ESG評分(AAA-CCC，AAA最佳) **/
	@Column(name="msciLevel", columnDefinition="CHAR(5)")
	private String msciLevel;

	/** Moody's ESG評分(0-100，100分最佳) **/
	@Column(name="moody", columnDefinition="DECIMAL(6,2)")
	private BigDecimal moody;

	/** SinoPac+ ESG 評等 (A+~C, A+最佳)**/
	@Column(name="sinoPac", columnDefinition="CHAR(5)")
	private String sinoPac;

	/** 台灣永續評鑑 (AAA~D, AAA最佳)  **/
	@Column(name="sustainability", columnDefinition="CHAR(5)")
	private String Sustainability;

	/**
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	public String getStkNo() {
		return this.stkNo;
	}
	public void setStkNo(String value) {
		this.stkNo = value;
	}

	public String getSource() {
		return this.source;
	}
	public void setSource(String value) {
		this.source = value;
	}

	public String getStkCMP() {
		return this.stkCMP;
	}
	public void setStkCMP(String value) {
		this.stkCMP = value;
	}

	public String getStkName() {
		return this.stkName;
	}
	public void setStkName(String value) {
		this.stkName = value;
	}

	public BigDecimal getEsgScore() {
		return this.esgScore;
	}
	public void setEsgScore(BigDecimal value) {
		this.esgScore = value;
	}

	public BigDecimal getEsgLevel() {
		return this.esgLevel;
	}
	public void setEsgLevel(BigDecimal value) {
		this.esgLevel = value;
	}

	public BigDecimal getIssLevel1() {
		return this.issLevel1;
	}
	public void setIssLevel1(BigDecimal value) {
		this.issLevel1 = value;
	}

	public BigDecimal getIssLevel2() {
		return this.issLevel2;
	}
	public void setIssLevel2(BigDecimal value) {
		this.issLevel2 = value;
	}

	public String getCompanyGovernance() {
		return this.companyGovernance;
	}
	public void setCompanyGovernance(String value) {
		this.companyGovernance = value;
	}

	/** 取得收件日期 **/
	public Date getReceiveDate() {
		return this.receiveDate;
	}
	/** 設定收件日期 **/
	public void setReceiveDate(Date value) {
		this.receiveDate = value;
	}

	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	public String getIssLevel() {
		return this.issLevel;
	}
	public void setIssLevel(String value) {
		this.issLevel = value;
	}

	public BigDecimal getSpScore() {
		return this.spScore;
	}
	public void setSpScore(BigDecimal value) {
		this.spScore = value;
	}

	public String getMsciLevel() {
		return this.msciLevel;
	}
	public void setMsciLevel(String value) {
		this.msciLevel = value;
	}

	public BigDecimal getMoody() {
		return this.moody;
	}
	public void setMoody(BigDecimal value) {
		this.moody = value;
	}

	public String getSinoPac() {
		return sinoPac;
	}

	public void setSinoPac(String sinoPac) {
		this.sinoPac = sinoPac;
	}

	public String getSustainability() {
		return Sustainability;
	}

	public void setSustainability(String sustainability) {
		Sustainability = sustainability;
	}

	public Object[] toObjectArray() {
		return new Object[] { this.mainId, this.stkNo, this.stkName, this.esgScore,
				this.esgLevel, this.issLevel1, this.issLevel2, this.companyGovernance,
				this.receiveDate,this.deletedTime, this.createTime, this.creator,
				this.updateTime, this.updater, this.stkCMP, this.source,
				this.msciLevel, this.issLevel, this.spScore, this.moody,this.sinoPac,this.Sustainability};
	}
}
