package com.mega.eloan.batch.service.impl;

import java.math.BigDecimal;
import java.util.Date;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lms.service.LMS1415Service;
import com.mega.eloan.lms.model.L999LOG01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * 定義在此package的batch，一開始的aop並不會注入transaction，transaction的控制是由所call的service來控制
 * 其它寫在com.mega.eloan.ces.batch.service.impl，則一開始就會本身就會注入transaction
 * 
 * 應收帳款模型批次EXCEL轉檔
 */
@Service("lmsbatch7840serviceimpl")
public class LMSBatch7840ServiceImpl extends AbstractCapService implements
		WebBatchService {

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	LMS1205Service lms1205Service;

	@Resource
	LMS1415Service lms1415Service;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	LMSService lmsService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	EloandbBASEService eloandbBASEService;

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	private static final String MOWA100_TEMPLATE_VERSION = "MOWA100_Excel_Version";

	private static final int START_ROW = 8;

	@Override
	public JSONObject execute(JSONObject json) {

		logger.info("lmsbatch7840 json (授信案例蒐尋比對報表): " + json);

		JSONObject result = new JSONObject();

		JSONObject requestJson = JSONObject.fromObject(json
				.getString("request"));

		result = queryL140m01a2(json);

		return result;
	}

	private JSONObject queryL140m01a2(JSONObject json) {

		JSONObject result = new JSONObject();
		JSONObject requestJson = JSONObject.fromObject(json
				.getString("request"));
		L999LOG01A logDoc = null;

		// 全文檢索專用欄位**************************************************************
		String fxUserId = "";
		String fxGroupId = "";
		String fxCaseDateName = ""; // approveTime or caseDate
		String fxCaseDateS = "";
		String fxCaseDateE = "";
		String fxEndDateS = "";
		String fxEndDateE = "";
		String fxTypCd = "";
		String fxDocType = "";
		String fxDocKind = "";
		String fxDocCode = "";
		String fxUpdaterName = ""; // hqAppraiser areaAppraiser updater
		String fxUpdater = "";
		String fxCaseBrId = "";
		String fxCustId = "";
		String fxDocRslt = "";
		StringBuffer fxDocStatus = new StringBuffer("");
		String fxLnSubject = "";
		String fxRateText = "";
		String fxOtherCondition = "";
		String fxReportOther = "";
		String fxReportReason1 = "";
		String fxReportReason2 = "";
		String fxAreaOption = "";
		String fxCollateral = "";
		String fxCustName = "";
		String fxBusCode = "";
		String fxCntrNo = "";
		String fxCurr = "";
		String fxLmtDays1 = "";
		String fxLmtDays2 = "";
		String fxRateText1 = "";
		String fxGuarantor = "";
		String fxCollateral1 = "";

		// J-107-0069-001
		// e-Loan授信系統「同類授信對象」之搜尋條件請增加「模型評等等級」，並請開放區域營運中心可代營業單位搜尋全行符合條件之同類授信對象。
		String fxCrGrade = "";
		String fxCrKind = "";

		// 個金專用
		String fxIsCls = "";
		String fxProdKind = "";
		String fxLnSubjectCls = "";
		String fxRateTextCls = "";
		String fxLnMonth1 = "";
		String fxLnMonth2 = "";

		// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
		// 授審處及各分處協助營業單位辦理利害關係人授信案件於E-Loan系統辦理全行「同類授信對象」案例搜尋，其中搜尋條件「擔保品--不動產」項下依建物謄本主要用途增加次選項，即按擔保品檔中建物「主要用途」欄：計有「住家用」、「商業用」、「工業用」、「住商用」、「住工用」、「工商用」、「農舍用」、「其他」等8類
		String fxBldUse = "";
		String fxOnlyLand = "";

		// 執行完成寫入一筆結果**************************************************

		String filterForm = requestJson.optString("filterForm");
		JSONObject params = JSONObject.fromObject(filterForm);

		String docStatus = Util.nullToSpace(requestJson
				.optString(EloanConstants.DOC_STATUS));

		String unitType = Util.trim(requestJson.optString("unitType"));
		String typCd = Util.trim(params.optString("typCd"));
		String docType = Util.trim(params.optString("docType"));
		String docKind = Util.trim(params.optString("docKind"));
		String docCode = Util.trim(params.optString("docCode"));
		String custName = Util.trim(params.optString("custName"));
		String updater = Util.trim(params.optString("updater"));
		String approveDateS = Util.nullToSpace(Util.trim(params
				.getString("approveDateS")));
		String approveDateE = Util.nullToSpace(Util.trim(params
				.getString("approveDateE")));
		String caseBrId = Util.nullToSpace(params.optString("caseBrId"));

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.optString("custId"));
		String cntrNo = Util.trim(params.optString("cntrNo"));

		String unitNo = requestJson.optString("unitNo");
		String uid = Util.trim(requestJson.optString("uid", ""));

		fxUserId = requestJson.optString("userId");
		fxGroupId = unitNo;

		Page<? extends GenericBean> page = null;

		if (Util.notEquals(uid, "")) {
			logDoc = lms1405Service.findL999log01aByMainId(uid);
		} else {
			logDoc = lms1405Service.findLatestL999log01a(fxUserId, "2");
		}

		if (logDoc == null) {
			// 新增一筆L999LOG01A
			logDoc = new L999LOG01A();
			logDoc.setMainId(IDGenerator.getUUID());
			logDoc.setItemType("2");
			logDoc.setResult("E"); // 執行中
			logDoc.setItemDscr(filterForm);
			logDoc.setCreateTime(CapDate.getCurrentTimestamp());
			logDoc.setCreator(fxUserId);
			logDoc.setUpdater("");
			logDoc.setUpdateTime(null);
			lms1405Service.save(logDoc);
		} else {
			logDoc.setResult("E"); // 執行中
			logDoc.setUpdater(fxUserId);
			logDoc.setUpdateTime(CapDate.getCurrentTimestamp());
			lms1405Service.save(logDoc);
		}

		String logDocMainId = logDoc.getMainId();

		// 2012-09-06 黃建霖 end

		Date fromDate = null;
		Date endDate = null;
		String fromDateStr = null;
		String endDateStr = null;

		if (!Util.isEmpty(Util.nullToSpace(params.optString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
			fromDateStr = Util.nullToSpace(params.optString("fromDate"));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.optString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
			endDateStr = Util.nullToSpace(params.optString("endDate"));
		}

		// Miller added at 2012/12/17
		// boolean isReject = params.getBoolean("isReject");

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			String[] reasonStr = { fromDateStr, endDateStr };

			if (docStatus.equals("03K|01K|02K|04K")) {

				fxCaseDateName = "approveTime";

			} else {

				fxCaseDateName = "caseDate";
			}
			fxCaseDateS = params.optString("fromDate");
			fxCaseDateE = params.optString("endDate");

		}
		if (!Util.isEmpty(caseBrId)) {
			fxCaseBrId = caseBrId;
		}

		// 篩選婉卻/變更格式選項 Miller added at 2012/12/17
		// if (isReject) {
		// // 3婉卻變更格式
		// fxDocRslt = "3";
		// }
		if (Util.isNotEmpty(typCd)) {
			fxTypCd = typCd;
		}
		if (Util.isNotEmpty(docType)) {
			fxDocType = docType;
		}
		if (Util.isNotEmpty(docKind)) {
			fxDocKind = docKind;
		}
		if (Util.isNotEmpty(docCode)) {
			fxDocCode = docCode;
		}

		if (Util.isNotEmpty(cntrNo)) {
			fxCntrNo = cntrNo;
		}

		if (Util.isNotEmpty(updater)) {

			if (UtilConstants.BankNo.授管處.equals(unitNo)) {
				fxUpdaterName = "hqAppraiser";
			} else if (UtilConstants.BankNo.中區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.中部區域授信中心.equals(unitNo)
					|| UtilConstants.BankNo.北一區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.北二區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.南區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.南部區域授信中心.equals(unitNo)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(unitNo)) {
				fxUpdaterName = "areaAppraiser";
			} else {
				fxUpdaterName = "updater";
			}
			fxUpdater = updater;

		}
		if (!Util.isEmpty(approveDateS) && !Util.isEmpty(approveDateE)) {
			fxEndDateS = approveDateS;
			fxEndDateE = approveDateE;
		}

		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}

		StringBuffer txDocStatus = null;

		txDocStatus = new StringBuffer(" ( ");

		txDocStatus.append("   DOCSTATUS = '"
				+ CreditDocStatusEnum.海外_已核准.getCode() + "' ");
		txDocStatus.append("OR DOCSTATUS = '"
				+ CreditDocStatusEnum.泰國_提會待登錄.getCode() + "' ");
		txDocStatus.append("OR DOCSTATUS = '"
				+ CreditDocStatusEnum.泰國_提會待覆核.getCode() + "' ");

		txDocStatus.append(") ");

		if (Util.notEquals(fxDocStatus.toString(), "")) {
			fxDocStatus.append(" AND ");
			fxDocStatus.append(txDocStatus);
		} else {
			fxDocStatus.append(txDocStatus);
		}

		// ********************************************************************

		// String fxFlag = Util.trim(params.optString("fxFlag")); // 進階查詢為Y
		String fxFlag = "Y"; // 進階查詢

		// 全文檢索

		// 若是第一次Query(reQuery = "Y" ，由LMS1205V01Page.js來) 就要先CALL

		if (Util.isNotEmpty(custId)) {
			fxCustId = custId;
		}

		if (Util.isNotEmpty(custName)) {
			fxCustName = "%" + Util.trim(custName) + "%";
		}

		// 暫時不將docCode當篩選條件(有點複雜)，反正撈L120M01A時會再篩選一次

		fxDocCode = "";
		fxTypCd = "";

		// 全文檢索專用欄位
		fxLnSubject = Util.trim(params.optString("fxLnSubject"));
		fxRateText = Util.trim(params.optString("fxRateText"));
		fxOtherCondition = Util.trim(params.optString("fxOtherCondition"));
		// fxReportOther = Util.trim(params.optString("fxReportOther"));
		// fxReportReason1 = Util.trim(params.optString("fxReportReason1"));
		// fxReportReason2 = Util.trim(params.optString("fxReportReason1"));
		// fxAreaOption = Util.trim(params.optString("fxAreaOption"));
		fxCollateral = Util.trim(params.optString("fxCollateral"));
		fxCollateral1 = Util.trim(params.optString("fxCollateral1"));
		fxBusCode = Util.trim(params.optString("fxBusCode"));

		fxCurr = Util.trim(params.optString("fxCurr")); // TWD|USD|XXX
		fxLmtDays1 = Util.trim(params.optString("fxLmtDays1")); // 100
		fxLmtDays2 = Util.trim(params.optString("fxLmtDays2")); // 365
		fxRateText1 = Util.trim(params.optString("fxRateText1")); // SL|AA|BB|CC
		fxGuarantor = Util.trim(params.optString("fxGuarantor")); // Y/N

		// J-107-0069-001
		// e-Loan授信系統「同類授信對象」之搜尋條件請增加「模型評等等級」，並請開放區域營運中心可代營業單位搜尋全行符合條件之同類授信對象。
		fxCrGrade = Util.trim(params.optString("fxCrGrade")); // 1-10 A-E
		fxCrKind = Util.trim(params.optString("fxCrKind")); // 1/2

		// 個金專用
		fxIsCls = Util.trim(params.optString("fxIsCls"));
		fxProdKind = Util.trim(params.optString("fxProdKind")); // 100
		fxLnSubjectCls = Util.trim(params.optString("fxLnSubjectCls")); // 100
		fxRateTextCls = Util.trim(params.optString("fxRateTextCls")); // 100
		fxLnMonth1 = Util.trim(params.optString("fxLnMonth1")); // 100
		fxLnMonth2 = Util.trim(params.optString("fxLnMonth2")); // 365

		// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
		fxBldUse = Util.trim(params.optString("fxBldUse"));
		fxOnlyLand = Util.trim(params.optString("fxOnlyLand"));

		// fxLnSubject = Util.notEquals(fxLnSubject, "") ? "%"
		// + fxLnSubject + "%" : "";
		fxRateText = Util.notEquals(fxRateText, "") ? "%" + fxRateText + "%"
				: "";
		fxOtherCondition = Util.notEquals(fxOtherCondition, "") ? "%"
				+ fxOtherCondition + "%" : "";
		fxReportOther = Util.notEquals(fxReportOther, "") ? "%" + fxReportOther
				+ "%" : "";
		fxReportReason1 = Util.notEquals(fxReportReason1, "") ? "%"
				+ fxReportReason1 + "%" : "";
		fxReportReason2 = Util.notEquals(fxReportReason1, "") ? "%"
				+ fxReportReason1 + "%" : "";
		fxAreaOption = Util.notEquals(fxAreaOption, "") ? "%" + fxAreaOption
				+ "%" : "";
		fxCollateral = Util.notEquals(fxCollateral, "") ? "%" + fxCollateral
				+ "%" : "";
		fxBusCode = Util.notEquals(fxBusCode, "") ? fxBusCode : "";
		try {
			// FOR TEST
			// fxCustName = "%統%";
			// J-107-0069-001
			// e-Loan授信系統「同類授信對象」之搜尋條件請增加「模型評等等級」，並請開放區域營運中心可代營業單位搜尋全行符合條件之同類授信對象。
			// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
			int tCount = lms1405Service.execFullTextSearch(fxUserId, fxGroupId,
					fxCaseDateName, fxCaseDateS, fxCaseDateE, fxEndDateS,
					fxEndDateE, fxTypCd, fxDocType, fxDocKind, fxDocCode,
					fxUpdaterName, fxUpdater, fxCaseBrId, fxCustId, fxDocRslt,
					fxDocStatus.toString(), fxLnSubject, fxRateText,
					fxOtherCondition, fxReportOther, fxReportReason1,
					fxReportReason2, fxAreaOption, fxCollateral, fxCustName,
					fxBusCode, fxCurr, fxLmtDays1, fxLmtDays2, fxRateText1,
					fxGuarantor, fxCntrNo, fxCollateral1, unitType, fxIsCls,
					fxProdKind, fxLnSubjectCls, fxRateTextCls, fxLnMonth1,
					fxLnMonth2, fxCrGrade, fxCrKind, logDocMainId, fxBldUse,
					fxOnlyLand);

			logDoc.setResult("Y"); // 執行成功
			logDoc.setUpdater(fxUserId);
			logDoc.setUpdateTime(CapDate.getCurrentTimestamp());
			logDoc.setExeCount(new BigDecimal(tCount));
			lms1405Service.save(logDoc);

			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName()
					+ "執行成功！共 " + tCount + " 筆\r\n");

		} catch (Exception ex) {

			logDoc.setResult("N"); // 執行成功
			logDoc.setExecMsg(StrUtils.getStackTrace(ex));
			logDoc.setUpdater(fxUserId);
			logDoc.setUpdateTime(CapDate.getCurrentTimestamp());
			lms1405Service.save(logDoc);

			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName()
					+ "執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);

		} finally {

		}

		// **************************************************

		return result;

	}
}
