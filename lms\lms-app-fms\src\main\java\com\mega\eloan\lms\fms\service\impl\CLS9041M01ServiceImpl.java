/* 
 *CLS9041ServiceImp.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.lms.dao.C004M01ADao;
import com.mega.eloan.lms.dao.C004S01ADao;
import com.mega.eloan.lms.fms.service.CLS9041M01Service;
import com.mega.eloan.lms.mfaloan.service.impl.AbstractMFAloanJdbc;
import com.mega.eloan.lms.model.C004M01A;
import com.mega.eloan.lms.model.C004S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 政策性留學生貸款送保彙報
 * </pre>
 * 
 * @since 2012/11/05
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/01,Vector Lo,new
 *          </ul>
 */
/* Use MIS-RDB */
@Service
public class CLS9041M01ServiceImpl extends AbstractMFAloanJdbc implements
		CLS9041M01Service {

	private static final Logger logger = LoggerFactory
			.getLogger(CLS9041M01ServiceImpl.class);

	@Autowired
	private DocFileDao docFileDao;

	@Resource
	C004M01ADao c004m01aDao;

	@Resource
	C004S01ADao c004s01aDao;

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("CapException!!", e);
				}

				if (model instanceof C004M01A) {
					c004m01aDao.save(((C004M01A) model));
				} else if (model instanceof C004S01A) {
					c004s01aDao.save(((C004S01A) model));
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model instanceof C004M01A) {
				c004m01aDao.delete(((C004M01A) model));
			} else if (model instanceof C004S01A) {
				c004s01aDao.delete(((C004S01A) model));
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch pageSetting) {
		if (clazz == C004M01A.class) {
			return c004m01aDao.findPage(pageSetting);
		} else if (clazz == C004S01A.class) {
			return c004s01aDao.findPage(pageSetting);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C004M01A.class) {
			C004M01A model = Util.isEmpty(oid) ? null : c004m01aDao
					.findByOid(oid);
			return (T) (model == null ? null : model);
		} else if (clazz == C004S01A.class) {
			C004S01A model = Util.isEmpty(oid) ? null : c004s01aDao
					.findByOid(oid);
			return (T) (model == null ? null : model);
		}
		return null;
	}

	@Override
	public void deleteQ(String mainId) {
		ISearch search = c004s01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		c004s01aDao.delete(c004s01aDao.find(search));
	}

	@Override
	public void saveQ(List<C004S01A> qList) {
		c004s01aDao.save(qList);
	}

	@Override
	public List<C004S01A> getQ(String mainId) {
		ISearch search = c004s01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("brno");
		return c004s01aDao.find(search);
	}

	@Override
	public Page<? extends GenericBean> findFile(String mainId) {
		ISearch search = docFileDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		return docFileDao.findPage(search);
	}

	@Override
	public void deleteFile(String oid) {
		ISearch search = docFileDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		List<DocFile> docfiles = docFileDao.find(search);
		for (DocFile docfile : docfiles) {
			docfile.setDeletedTime(CapDate.getCurrentTimestamp());
		}
		docFileDao.save(docfiles);
	}

	@Override
	public List<Map<String, Object>> getMisData(String beginDate,
			String endDate, String rptType) {
		//S1會用 <entry key="MIS.LNF192.NEWgetS1">
		if(Util.equals("S2", rptType)){
			return getJdbc().queryForListWithMax("MIS.LNF192.getS2", new String[] { beginDate, endDate });
		}else if(Util.equals("S3", rptType)){
			return getJdbc().queryForListWithMax("MIS.LNF192.getS3", new String[] { beginDate, endDate });
		}else{
			return getJdbc().queryForListWithMax("MIS.LNF192.get" + rptType, new String[] { beginDate, endDate });	
		}		
	}

	@Override
	public List<Map<String, Object>> getMisDataNewS1(String beginDate,
			String endDate, String rptType) {
		return getJdbc().queryForListWithMax(
				"MIS.LNF192.NEWgetS1",
				new String[] { beginDate, endDate, beginDate, endDate,
						beginDate, beginDate, endDate });
	}

	@Override
	public Map<String, Object> findBrnoAndStuidS2(String beginDate, String endDate) {
		List<Map<String, Object>> data = getJdbc().queryForListWithMax(
				"MIS.LNF192.getS2", new String[] { beginDate, endDate });
		Map<String, Object> result = new LinkedHashMap<String, Object>();
		for (Map<String, Object> pivot : data) {
			result.put(pivot.get("STU_ID").toString(),
					pivot.get("LNF192_BR_NO"));
		}
		return result;
	}
	
	@Override
	public Map<String, Object> findBrnoAndStuidS3(String beginDate, String endDate) {
		List<Map<String, Object>> data = getJdbc().queryForListWithMax(
				"MIS.LNF192.getS3", new String[] { beginDate, endDate });
		Map<String, Object> result = new LinkedHashMap<String, Object>();
		for (Map<String, Object> pivot : data) {
			result.put(Util.trim(pivot.get("LNF192_STUDENT_ID")).toUpperCase(),
					pivot.get("LNF192_BR_NO"));
		}
		return result;
	}

	@Override
	public boolean sendRpt(String unid, Date date) {
		List<C004M01A> rpt = findC004M01AByUnid(unid);
		for (C004M01A pivot : rpt) {
			pivot.setRptDate(date);
			c004m01aDao.save(pivot);
		}
		return true;
	}

	@Override
	public void updateLNF192S1(String sendDate,
			String brNo, String studentId, String begDate, String endDate) {
		
		this.getJdbc().update("MIS.LNF192.NEWupdateS1",
				new Object[] { sendDate, brNo, studentId, begDate, endDate});
		
		
	}
	
	@Override
	public void updateLNF192S2(String sendDate, String custId,
			String begDate){
		this.getJdbc().update(
				"MIS.LNF192.NEWupdateS2",
				new Object[] { sendDate, custId, begDate});
	}
	
	@Override
	public List<C004M01A> findC004M01AByUnid(String unid){
		ISearch search = c004m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "unid", unid);
		List<C004M01A> rpt = c004m01aDao.find(search);
		
		return rpt;
	}
}
