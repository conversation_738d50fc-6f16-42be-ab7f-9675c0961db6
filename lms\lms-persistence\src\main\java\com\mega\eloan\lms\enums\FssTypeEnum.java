/* 
 * FssTypeEnum.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.enums;

/**
 * <pre>
 * 財務報表類別(一般/預估)。
 * </pre>
 * 
 * @since 2011/7/28
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/28,Sunkist Wang,new</li>
 *          </ul>
 */
public enum FssTypeEnum {

    /**
     * 一般
     */
    GENERAL("1"),

    /**
     * 預估
     */
    ESTIMATE("2");

    private String code;

    FssTypeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public boolean isEquals(Object other) {
        if (other instanceof String) {
            return code.equals(other);
        } else {
            return super.equals(other);
        }
    }

    public static FssTypeEnum getEnum(String code) {
        for (FssTypeEnum enums : FssTypeEnum.values()) {
            if (enums.isEquals(code)) {
                return enums;
            }
        }
        return null;
    }
}
