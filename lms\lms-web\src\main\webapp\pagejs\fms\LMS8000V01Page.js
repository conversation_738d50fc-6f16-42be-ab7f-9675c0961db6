// 1-額度序號 2-額度序號+放款帳號
//
var newType = "1";
$(document).ready(function(){
    var result_s = CommonAPI.loadOrderCombosAsList(["postLoan_followKind"]);
    init_grid();

    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];

        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }

        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($("#gridview").getRowData(rows[i]).oid);
                }

                $.ajax({
                    handler: "lms8000m01formhandler",
                    data: {
                        formAction: "deleteL260m01a",
                        oids: data
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            }
        });
    }).end().find("#btnAdd").click(function(){
        openNewCaseBox().done(function(result){
            chose_custId(result).done(function(resultFrom_chose_custId){
                chose_cntrNo(resultFrom_chose_custId).done(function(resultFrom_chose_cntrNo){
                    $.ajax({
                        handler: "lms8000m01formhandler",
                        action: 'newl260m01a',
                        data: {
                            custId: resultFrom_chose_cntrNo.custId,
                            dupNo: resultFrom_chose_cntrNo.dupNo,
                            custName: resultFrom_chose_cntrNo.custName,
                            cntrNo: resultFrom_chose_cntrNo.cntrNo,
                            loanNo: resultFrom_chose_cntrNo.loanNo,
                            newType: newType
                        },
                        success: function(obj){
                            $.form.submit({
                                url: '../fms/lms8000m01/01',
                                data: {
                                    oid: obj.oid,
                                    mainId: obj.mainId,
                                    mainOid: obj.oid,
                                    mainDocStatus: viewstatus,
                                    newType: newType
                                },
                                target: obj.oid
                            });
                            // $.thickbox.close();
                            if (obj.raspMsg && obj.raspMsg != "") {
                                CommonAPI.showMessage(obj.raspMsg);
                            }
                            $("#gridview").trigger("reloadGrid");
                        }
                    });
                });
            });
        });
    }).end().find("#btnImportExcel940CustId").click(function(){
        let limitFileSize = 5 * 1024 * 1024; // 5m
        MegaApi.uploadDialog({
            handler: "lms8000fileuploadhandler",
            fieldId: "postLoan940",
            title: i18n && i18n.def.insertfile || "請選擇xls",
            fileCheck: ['xls'],
            subTitle: i18n.def('insertfileSize', {
                'fileSize': (limitFileSize / 1048576).toFixed(2)
            }),
            limitSize: limitFileSize,
            width: 320,
            height: 150,
            data: {
                // J-113-0490_07623_B1001 公司訪談紀錄表批次調閱

            },
            success: function(obj){
                if(obj.success){
                    CommonAPI.showMessage("批次已發動請等待寄送MAIL!");
                }
            }
        });
    }).end().find("#btnFilter").click(function(){
        openFilterBox();
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }

        var count = 0;
        var ids = new Array();
        ids = $("#gridview").getGridParam('selarrrow');
        for (var i in ids) {
            var rows = $("#gridview").jqGrid('getRowData', ids[i]);
            count++;
//            if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
//                count++;
//            }
        }

        if (count > 1) {
            // L260M01A.error01=此功能不能多選
            CommonAPI.showMessage(i18n.lms8000v01["L260M01A.error01"]);
        } else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    });

    $("#getCustData").click(function(){
        var $queryStopForm = $("#queryStopForm");
        var $custId = $queryStopForm.find("#addCustId").val();
        if(($custId == null || $custId == undefined || $custId == '')){
            // 請輸入統一編號
            CommonAPI.showErrorMessage(i18n.lms8000v01["checkInput"] + i18n.lms8000v01["L260M01A.custId"]);
            return;
        }else{
            var defaultOption = {};
            if($custId != null && $custId != undefined && $custId != ''){
                defaultOption = {
                    defaultValue: $custId //預設值
                };
            }
            //綁入MegaID
            CommonAPI.openQueryBox(
                $.extend({
                    doNewUser: false,
                    defaultCustType : ($custId != null && $custId != undefined && $custId != '') ? "1" : "",
                    divId:"queryStopForm", //在哪個div 底下
                    autoResponse: { // 是否自動回填資訊
                           id: "addCustId", // 統一編號欄位ID
                           dupno: "addDupNo", // 重覆編號欄位ID
                          name: "addCustName" // 客戶名稱欄位ID
                    },fn:function(obj){
                    }
                },defaultOption)
            );
        }
    });

    function setOverSeaHideUI(overSeaHideVal){
        if(overSeaHideVal){//} && overSeaHideVal == "Y"){
            $("span[class*=overSeaHide]").hide();
            $("span[class*=overSeaShow]").show();
        } else {
            $("span[class*=overSeaHide]").show();
            $("span[class*=overSeaShow]").hide();
        }
    }

	function chose_custId(result){
        var my_dfd = $.Deferred();
        $("#queryStopForm").find("#addCustId").val('');
        $("#queryStopForm").find("#addDupNo").val('');
        $("#queryStopForm").find("#addCustName").val('');
        $("#queryStop").thickbox({
            title : i18n.def["query"],//'查詢',
            width : 500,
            height : 230,
            modal : true,
            align : 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons : {
                'sure' : function(){
                    var $queryStopForm = $("#queryStopForm");
                    var custId = $queryStopForm.find("#addCustId").val();
                    var dupNo = $queryStopForm.find("#addDupNo").val();
                    var custName = $queryStopForm.find("#addCustName").val();
                    if ($queryStopForm.valid()){
                        if(custId == undefined || custId == null || custId == ""){
                            CommonAPI.showErrorMessage(i18n.lms8000v01["checkInput"] + i18n.lms8000v01["L260M01A.custId"]);
                            return;
                        }else{
                            if((custName == undefined || custName == null || custName == "") && (dupNo == undefined || dupNo == null || dupNo == "")){
                                CommonAPI.showErrorMessage(i18n.lms8000v01["checkSelect"] + i18n.lms8000v01["L260M01A.custName"]);
                                return;
                            }else{
                                $.thickbox.close();
                                my_dfd.resolve( $.extend(result,{'custId':custId, 'dupNo':dupNo, 'custName':custName}) );
                            }
                        }
                    }
                },
                'cancel' : function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
//        AddCustAction.open({
//            handler: 'lms8000m01formhandler',
//            action : 'echo_custId',
//            data : {
//            },
//            callback : function(json){
//                // 關掉 AddCustAction 的
//                $.thickbox.close();
//                my_dfd.resolve( $.extend(result,json) );
//            }
//        });
        return my_dfd.promise();
    }

    function openNewCaseBox(){
        var my_dfd = $.Deferred();
        var $filterForm = $("#newCaseForm");
        $filterForm.reset();            // 初始化

        setOverSeaHideUI(userInfo.isOverSea);

        $("#newCaseBox").thickbox({
            title: "",
            width: 400,
            height: 210,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#newCaseForm").valid()) {
                        return;
                    }
                    newType = $("input[name='newCase']:checked").val();
                    $.thickbox.close();
                    my_dfd.resolve(newType);
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
        return my_dfd.promise();
    }

    function chose_cntrNo(resultFrom_chose_custId){
        var my_dfd = $.Deferred();

        GetCntrnoGrid.jqGrid("setGridParam", {
            postData: {
                newType: newType,
                custId: resultFrom_chose_custId.custId,
                dupNo: resultFrom_chose_custId.dupNo
            },
            search: true
        }).trigger("reloadGrid");

        $("#GetCntrnoThickBox").thickbox({
           title: i18n.lms8000v01["checkSelect"]+i18n.lms8000v01["L260M01A.cntrNo"],
           width: 400,
           height: 450,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
                "sure": function(){
                     var data = GetCntrnoGrid.getSingleData();
                     if (data) {
                        $.ajax({
                            handler: "lms8000m01formhandler",
                            action: "chkL260M01ANotEnd",
                            data: {
                                single: true,
                                custId: resultFrom_chose_custId.custId,
                                dupNo: resultFrom_chose_custId.dupNo,
                                cntrNo: data.cntrNo,
                                loanNo: data.loanNo
                            },
                            success: function(obj){
                                if(obj.msg){
                                    return CommonAPI.showErrorMessage(i18n.lms8000v01["msg.alreadyHave"]);
                                } else {
                                    $.thickbox.close();
                                    var cntrNo = data.cntrNo;
                                    var loanNo = data.loanNo;
                                    var sDate = data.sDate;
                                    my_dfd.resolve($.extend(resultFrom_chose_custId,
                                                    {'cntrNo':cntrNo, 'loanNo':loanNo, 'sDate':sDate}));
                                }
                            }
                        });
                     }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });

        return my_dfd.promise();
    }

    function openDoc(cellvalue, options, rowObject){
        $.form.submit({			
            url: '../fms/lms8000m01/01',
            data: {
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus
            },
            target: rowObject.oid
        });
    }

    // 篩選
    function openFilterBox(){
        var $filterForm = $("#filterForm");
        $filterForm.reset();            // 初始化

        setOverSeaHideUI(userInfo.isOverSea);

        // J-113-0490 07623
        $("#followKind").setItems({
            item: result_s.postLoan_followKind,
            format: "{key}",
            size: 5,
            maxlength: 2
        });

        // J-112-0418 配合企金處依據企金處112年一般業務查核意見，E-LOAN企金授信管理系統修改增加「綠色授信」註記之管控機制等。
        var followKind11Element = $("input:checkbox[name=followKind][value='11']");
        followKind11Element.change(function(){

            var greenLoanChk= followKind11Element.attr("checked");
            if( oneRowDataFrom=="B" && greenLoanChk ){
                followKind11Element.attr("disabled",true);
                $("input:checkbox[name=followKind][value='10']").attr("disabled",true);
            }

        });
        // J-109-0339 弱化惡化 檢核互斥
        $("input[name=followKind]").click(function(){
            var cols = ["W1", "W2", "W3", "D1", "D2", "D3"];
            var chkValue = DOMPurify.sanitize(String($(this).val()));
            // == -1 代表沒找到
            if ($.inArray(chkValue, cols) != -1) {
                var type = chkValue.substring(0, 1);
                var seq = chkValue.substring(1, 2);
                // 需要檢查
                if (this.checked) {
                    if (type == "W") {
                        $("[name=followKind][value='D" + seq + "']").removeAttr("checked").attr("disabled", "disabled");
                    }
                    else
                    if (type == "D") {
                        $("[name=followKind][value='W" + seq + "']").removeAttr("checked").attr("disabled", "disabled");
                    }
                }
                else {
                    if (type == "W") {
                        $("[name=followKind][value='D" + seq + "']").removeAttr("disabled");
                    }
                    else
                    if (type == "D") {
                        $("[name=followKind][value='W" + seq + "']").removeAttr("disabled");
                    }
                }
            }
        });

        var isV3Url = __ajaxHandler.indexOf('lms8000v03') > -1;
        $("#filterBox").thickbox({
            // LMS8000V01.title=請輸入欲查詢項目：
            title: i18n.lms8000v01["LMS8000V01.title"],
            width: isV3Url ? 750 : 370,
            height: isV3Url ? 450 : 210,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            open: function (){
                $("#filterForm").find("#custId").removeClass("alphanum");
            },
            buttons: {
                "sure": function(){
                    let filterForm = $("#filterForm");
                    if (!filterForm.valid()) {
                        return;
                    }
                    if(filterForm.find("[name=followKind]:checked").size() > 0 && !filterForm.find("#followUpType").val()){
                        // L260M01A.error05=請選擇追蹤類型!
                        API.showErrorMessage(i18n.lms8000v01["L260M01A.error05"]);
                        return;
                    }
                    grid();

                    $.thickbox.close();
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    }
    function getSelectItem(){
        var data = [];
        $("#itemSpan_followKind").find("[name=followKind]:checked").each(function(v, k){
            data.push($(k).val());
        });
        return data.join("|");
    }
    function grid(){
        $("#gridview").jqGrid("setGridParam", {
            postData: $.extend($("#filterForm").serializeData(), {
                handler: "lms8000gridhandler",
                formAction: "queryL260M01A",
                docStatus: viewstatus,
                searchType: "filter",
                followKind: getSelectItem()
            }),
            rowNum: 15,
            page: 1
        }).trigger("reloadGrid");
    }

    var GetCntrnoGrid = $('#GetCntrnoGrid').iGrid({
        handler: 'lms8000gridhandler', //設定handler
        height: 300, //設定高度
        action: 'queryGetCntrno', //執行的Method
        postData: {

        },
        needPager: true,
        rownumbers: true,
        colModel: [{
            colHeader: i18n.lms8000v01["L260M01A.cntrNo"], // 額度序號
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'cntrNo'
        },{
            colHeader: (userInfo.isOverSea ? i18n.lms8000v01["L260M01A.loanNoOvs"] : i18n.lms8000v01["L260M01A.loanNo"]), // 放款帳號
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'loanNo'
        }, {
            name: 'sDate',
            hidden: true
        }],
        loadComplete: function () {
            if( GetCntrnoGrid.getGridParam("records")>0){

            }else{
                var custId = GetCntrnoGrid.getGridParam("postData")['custId'];
                if(custId && custId.length>1){
                    $.thickbox.close();
                    // L260M01A.error03=無相關額度序號
                    API.showErrorMessage(i18n.lms8000v01["L260M01A.error03"]);
                }
            }
        }
    });

    function init_grid(){
        if (txCode == "336093"){    // 編製完成
            var grid = $("#gridview").iGrid({
                handler: 'lms8000gridhandler',
                height: 350,
                width: 785,
                autowidth: false,
                action: "queryL260M01A",
                postData: {
                    docStatus: viewstatus,
                    searchType: "init"
                },
                rowNum: 15,
                sortname: "approveTime|custId",
                sortorder: "desc|desc",
                multiselect: true,
                colModel: [{
                    colHeader: i18n.lms8000v01["L260M01A.custId"], //借款戶統一編號
                    align: "left", width: 100, sortable: true, name: 'custId',
                    formatter: 'click', onclick: openDoc
                }, {
                    colHeader: i18n.lms8000v01["L260M01A.custName"], //借款戶名稱
                    align: "left", width: 100, sortable: true, name: 'custName'
                }, {
                    colHeader: i18n.lms8000v01["L260M01A.cntrNo"], //額度序號
                    align: "left", width: 100, sortable: true, name: 'cntrNo'
                }, {
                    colHeader: (userInfo.isOverSea ? i18n.lms8000v01["L260M01A.loanNoOvs"] : i18n.lms8000v01["L260M01A.loanNo"]), //放款帳號
                    align: "left", width: 100, sortable: true, name: 'loanNo'
                }, {
                    colHeader: i18n.lms8000v01['L260M01A.updater'],//經辦
                    name: 'updater',
                    width: 80,
                    sortable: true,
                    align: "center"
                }, {
                    colHeader: i18n.lms8000v01['L260M01A.approver'],//"覆核",
                    name: 'approver',
                    width: 80,
                    sortable: true,
                    align: "center"
                }, {
                    colHeader: i18n.lms8000v01["L260M01A.approveTime"], // 核准日期
                    align: "left",
                    width: 80, // 設定寬度
                    sortable: true, // 是否允許排序
                    name: 'approveTime',
                    formatter: 'date',
                    formatoptions: {
                        srcformat: 'Y-m-d H:i:s',
                        newformat: 'Y-m-d H:i'
                    }
                }, {
                    name: 'oid',
                    hidden: true
                }, {
                    name: 'mainId',
                    hidden: true
                }, {
                    name: 'docURL',
                    hidden: true
                }],
                ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                    var data = $("#gridview").getRowData(rowid);
                    openDoc(null, null, data);
                }
            });
        } else {
            var grid = $("#gridview").iGrid({
                handler: 'lms8000gridhandler',
                height: 350,
                width: 785,
                autowidth: false,
                action: "queryL260M01A",
                postData: {
                    docStatus: viewstatus,
                    searchType: "init"
                },
                rowNum: 15,
                sortname: "createTime|custId",
                sortorder: "desc|desc",//desc
                multiselect: true,
                colModel: [{
                    colHeader: i18n.lms8000v01["L260M01A.custId"], //借款戶統一編號
                    align: "left", width: 100, sortable: true, name: 'custId',
                    formatter: 'click', onclick: openDoc
                }, {
                    colHeader: i18n.lms8000v01["L260M01A.custName"], //借款戶名稱
                    align: "left", width: 100, sortable: true, name: 'custName'
                }, {
        	        colHeader: i18n.lms8000v01["L260M01A.cntrNo"], //額度序號
        	        align: "left", width: 100, sortable: true, name: 'cntrNo'
        	    }, {
        	        colHeader: (userInfo.isOverSea ? i18n.lms8000v01["L260M01A.loanNoOvs"] : i18n.lms8000v01["L260M01A.loanNo"]), //放款帳號
        	        align: "left", width: 100, sortable: true, name: 'loanNo'
        	    }, {
                    colHeader: i18n.lms8000v01['L260M01A.updater'],//經辦
                    name: 'updater',
                    width: 80,
                    align: "center"
                }, {
                    colHeader: i18n.lms8000v01["L260M01A.createTime"], //建立日期
                    align: "left",
                    width: 80, // 設定寬
                    name: 'createTime',
                    formatter: 'date',
                    formatoptions: {
                        srcformat: 'Y-m-d H:i:s',
                        newformat: 'Y-m-d H:i'
                    }
                }, {
                    name: 'oid',
                    hidden: true
                }, {
                    name: 'mainId',
                    hidden: true
                }],
                ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                    var data = $("#gridview").getRowData(rowid);
                    openDoc(null, null, data);
                }
            });
        }
    }
});

