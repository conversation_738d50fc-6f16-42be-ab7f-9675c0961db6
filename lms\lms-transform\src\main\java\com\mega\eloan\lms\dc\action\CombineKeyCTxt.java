/* 
 * CombineKeyL120.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2013 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.dc.action;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.List;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.BrnoConfig;
import com.mega.eloan.lms.dc.conf.ConfigData;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * CombineKeyL120 : 匯集所有的L120M01C資料至load_db2;DXL_KeyList資料至logs
 * </pre>
 * 
 * @since 2013/3/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/25,Bang,new
 *          </ul>
 */
public class CombineKeyCTxt extends BaseAction {

	protected Logger logger = LoggerFactory.getLogger(CombineKeyCTxt.class);
	@SuppressWarnings("unused")
	private ConfigData config = null;
	private static String schema = "";// 目前執行的系統名稱:LMS或CLS
	private String logsDirPath = "";
	private String dxlDirRootPath = "";
	private String loadDB2DirPath = "";
	private static String lmsView = "";
	private static String clsView = "";
	private PrintWriter writeCom = null;

	/**
	 * @param args
	 */
	public static void main(String[] args) {

		try {
			CombineKeyCTxt cbk = new CombineKeyCTxt();

			if(!validate_args(args)){
				return;
			}
			
			if (args.length == 0) {
				throw new DCException("請輸入程式參數！");
			}
			cbk.initParam(args);
			cbk.writerTxt();
		} catch (Exception e) {
			e.printStackTrace();
			throw new DCException(e);
		}
	}

	public CombineKeyCTxt() {
		
	}
	
	private static boolean validate_args(String[] args){
		String[] sysArr = new String[]{TextDefine.SCHEMA_LMS, TextDefine.SCHEMA_CLS};
		if(args.length==0 || !ArrayUtils.contains(sysArr, args[0])){
			return false;
		}		
		return true;
	}
	
	@SuppressWarnings("static-access")
	public CombineKeyCTxt(ConfigData config,String schema) {
		if (config != null) {
			this.configData = config;
		}
		this.schema = schema;
		this.initParam(new String[]{schema});
		this.writerTxt();
	}
	
	
	/**
	 * 初始化參數資訊
	 * 
	 * @param args
	 */
	private void initParam(String[] args) {
		try {
			schema = args[0];
			if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
				dxlDirRootPath = configData.getLmsDxlDirRootPath();// homePath\today\LMS
				logsDirPath = configData.getLmsLogsDirPath();// User當前工作目錄\log\logs\執行日期\LMS
				loadDB2DirPath = configData.getLmsloadDB2DirPath();
				lmsView = configData.getLMSViewName();
			} else {
				dxlDirRootPath = configData.getClsDxlDirRootPath();// homePath\today\CLS
				logsDirPath = configData.getClsLogsDirPath();// User當前工作目錄\log\logs\執行日期\CLS
				clsView = configData.getCLSViewName();
			}
			String dtFile = logsDirPath + File.separator + schema
					+ "_combineKeyL120.log";
			writeCom = new PrintWriter(new BufferedWriter(
					new OutputStreamWriter(new FileOutputStream(
							new File(dtFile)), TextDefine.ENCODING_UTF8)), true);

		} catch (Exception e) {
			String errmsg = new StringBuffer().append(
					"執行CombineKeyL120 之initParam時產生錯誤").toString();
			this.logger.error(errmsg, e);
			throw new DCException(errmsg + "==>" + e.getLocalizedMessage(), e);
		}
	}

	/**
	 * 合併檔案
	 */
	@SuppressWarnings("unused")
	private void writerTxt() {
		List<String> brnoList = BrnoConfig.getInstance().getBrnoList();
		String[] view = null;
		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
			view = lmsView.split(TextDefine.SYMBOL_SEMICOLON);
			combineL120M01C();
		} else if (TextDefine.SCHEMA_CLS.equalsIgnoreCase(schema)) {
			view = clsView.split(TextDefine.SYMBOL_SEMICOLON);
			combineDxlKeyList();
		}

		// for (String brno : brnoList) {
		// for (String viewName : view) {
		// if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
		// combineL120M01C(brno, viewName);
		// } else {
		// combineDxlKeyList(brno, viewName);
		// }
		// }
		// }
		if (writeCom != null) {
			IOUtils.closeQuietly(writeCom);
		}
	}

	/**
	 * 合併各分行/ViewName/TEXT下的L120M01C檔至load_db2/data下
	 * 
	 */
	private void combineL120M01C() {
		try {
			String db2TxtRoot = this.loadDB2DirPath + File.separator+ this.configData.getDataPath()+ File.separator
					+ "L120M01C.txt";

			File file = new File(this.dxlDirRootPath);
			for (File fe : file.listFiles()) {
				// 第一層 分行代號
				if (fe.isDirectory()) {
					for (File subDir : fe.listFiles()) {
						// 第二層 notes view name
						if (subDir.isDirectory()) {
							this.logger.debug("--分行代號-:" + fe.getName()
									+ ":---View name---:"
									+ subDir.getName() + ", 開始時間:"
									+ Util.getNowTime());
							// 第三層 ==================檔案
							File filePath = new File(subDir.getAbsolutePath()
									+ this.configData.getTextPath());
							if (filePath.isDirectory()) {
								for (File o : filePath.listFiles()) {
									if (o.isFile()
											&& "L120M01C.txt"
													.equalsIgnoreCase(o
															.getName())) {
										String srFile = o.getAbsolutePath();
										String dtFile = db2TxtRoot;
										copyAppendFiles(srFile, dtFile);
									}
								}
							}
							this.logger.debug("--分行代號-:" + fe.getName()
									+ ":---View name---:"
									+ subDir.getName() + ", 結束時間:"
									+ Util.getNowTime());
						}
					}
				}
			}
			// 利用設定檔讀取方式
			// String txtRoot = this.dxlDirRootPath + File.separator + brno
			// + File.separator + viewName + this.configData.getTextPath()
			// + File.separator + "L120M01C.txt";
			// File keyFile = new File(txtRoot);
			// if (keyFile.exists()) {
			// String srFile = txtRoot;
			// String dtFile = db2TxtRoot;
			// copyAppendFiles(srFile, dtFile);
			// }
		} catch (Exception e) {
			String errmsg = "CombineKeyL120在執行合併L120M01C檔案時發生錯誤 :";
			this.writeCom.println(errmsg + e);
			this.logger.error(errmsg, e);
			throw new DCException(errmsg, e);
		}
	}

	/**
	 * 合併分行\ViewName\TEXT下的DXL_Key檔至log\logs\執行日期\CLS下
	 * 
	 */
	private void combineDxlKeyList() {
		try {
			String keyListRoot = this.logsDirPath + File.separator
					+ "DXL_KeyList.txt";

			File file = new File(this.dxlDirRootPath);
			for (File fe : file.listFiles()) {
				// 第一層 分行代號
				if (fe.isDirectory()) {
					for (File subDir : fe.listFiles()) {
						// 第二層 notes view name
						if (subDir.isDirectory()) {
							this.logger.debug("--分行代號-:" + fe.getName()
									+ ":---View name---:"
									+ subDir.getName() + ", 開始時間:"
									+ Util.getNowTime());
							// 第三層 ==================檔案
							File filePath = new File(subDir.getAbsolutePath()
									+ this.configData.getTextPath());
							if (filePath.isDirectory()) {
								for (File o : filePath.listFiles()) {
									if (o.isFile()
											&& "DXL_Key.txt".equalsIgnoreCase(o
													.getName())) {
										String srFile = o.getAbsolutePath();
										String dtFile = keyListRoot;
										copyAppendFiles(srFile, dtFile);
									}
								}
							}
							this.logger.debug("--分行代號-:" + fe.getName()
									+ ":---View name---:"
									+ subDir.getName() + ", 結束時間:"
									+ Util.getNowTime());
						}
					}
				}
			}

			// 利用設定檔讀取方式
			// String keyRoot = this.dxlDirRootPath + File.separator + brno
			// + File.separator + viewName + this.configData.getTextPath()
			// + File.separator + "DXL_Key.txt";
			// File keyFile = new File(keyRoot);
			// if (keyFile.exists()) {
			// String srFile = keyRoot;
			// String dtFile = keyListRoot;
			// copyAppendFiles(srFile, dtFile);
			// }
		} catch (Exception e) {
			String errmsg = "CombineKeyL120在執行合併DXL_Key檔案時發生錯誤 :";
			this.writeCom.println(errmsg + e);
			this.logger.error(errmsg, e);
			throw new DCException(errmsg, e);
		}
	}

	/**
	 * 寫入檔案
	 * 
	 * @param srFile
	 *            來源檔案
	 * @param dtFile
	 *            目標檔案
	 * @return true:寫入成功 , false:寫入失敗
	 */
	private boolean copyAppendFiles(String srFile, String dtFile) {
		try {
			File f1 = new File(srFile);
			File f2 = new File(dtFile);
			InputStream in = new FileInputStream(f1);

			OutputStream out;
			if (f2.exists()) {
				// For Append the file.
				out = new FileOutputStream(f2, true);
			} else {
				// For Create or Overwrite the file.
				out = new FileOutputStream(f2);
			}
			byte[] buf = new byte[4096];
			int len;
			while ((len = in.read(buf)) > 0) {
				out.write(buf, 0, len);
			}
			IOUtils.closeQuietly(in);
			IOUtils.closeQuietly(out);
		} catch (Exception ex) {
			String errmsg = "合併檔案時出現錯誤 ,來源檔案 :" + srFile + " , 目的地 :" + dtFile;
			this.logger.error(errmsg, ex);
			throw new DCException(errmsg, ex);
		}
		return true;
	}

}
