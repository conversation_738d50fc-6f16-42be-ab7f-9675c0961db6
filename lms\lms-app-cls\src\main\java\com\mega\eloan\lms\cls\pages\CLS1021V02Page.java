/* 
 * CLS1021V02Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * 購置房屋擔保放款風險權數檢核表 - 待覆核
 * </pre>
 * 
 * @since 2013/01/03
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/03,<PERSON><PERSON><PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1021v02")
public class CLS1021V02Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_待覆核,
				CreditDocStatusEnum.先行動用_待覆核);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.View);
		renderJsI18N(CLS1021M01Page.class);
		renderJsI18N(CLS1021V01Page.class);
		
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/cls/CLS1021V02Page')");
	}

}
