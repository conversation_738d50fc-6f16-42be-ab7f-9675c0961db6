package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 模擬動審 - 檢核表
 * </pre>
 * 
 * @since
 * <AUTHOR>
 * @version <ul>
 *          <li>
 *          </ul>
 */
public class CLS1161S43Panel extends Panel {
	
	/**/
	private static final long serialVersionUID = 1L;
	
	public CLS1161S43Panel(String id) {
		super(id);
		
//		if(true){
//			add(LMSUtil.genHtmlComponent("show_default_btn", true));
//		}
	}

	public CLS1161S43Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

}
