<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:extend>
        	<script type="text/javascript" src="pagejs/fms/LMS7110M01Page.js"></script>
        	<div class="button-menu funcContainer" id="buttonPanel">
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02" />
					<wicket:message key="button.send">呈主管覆核</wicket:message>
				</button>
				<button type="button" id="btnQueryStop">
					<span class="ui-icon ui-icon-jcs-102" />
					<wicket:message key="button.queryStop">查詢目前停權起迄</wicket:message>
				</button>
				<button id="btnExit" class="forview">
	                    <span class="ui-icon ui-icon-jcs-01"></span>
	                    <wicket:message key="button.exit">離開</wicket:message>
	           	</button>
			</div>
			<div class="tit2" id="stopDetail1" >
				<form id="formStopDetail1">
					<input type="hidden" id="oid" name="oid"/>
					<input type="hidden" id="mainId" name="mainId"/>					
					<table class="tb2" width="95%">
						<tr>
							<td width="25%" class="hd1"><wicket:message key="html.index4">分行代碼</wicket:message>&nbsp;&nbsp;</td>
							<td width="25%"><span class="color-blue" id="caseBrId" name="caseBrId"></span></td>
							<td width="25%" class="hd1"><wicket:message key="html.index2">客戶統一編號</wicket:message>&nbsp;&nbsp;</td>
							<td width="25%"><span class="color-blue" id="custId" name="custId"></span>
							<span class="color-blue" id="dupNo" name="dupNo"></span>
							<span class="color-blue" id="custName" name="custName"></span></td>
						</tr>
						<tr>
							<td class="hd1"><wicket:message key="html.index1">案件號碼</wicket:message>&nbsp;&nbsp;</td>
							<td><span class="color-blue" id="caseNo" name="caseNo"></span></td>
							<td class="hd1"><wicket:message key="html.index5">停權單位經辦</wicket:message>&nbsp;&nbsp;</td>
							<td><span class="color-blue" id="stopUpdater" name="stopUpdater"></span></td>
						</tr>
						<tr>
							<td class="hd1"><wicket:message key="html.index6">停權單位經辦建立時間</wicket:message>&nbsp;&nbsp;</td>
							<td><span class="color-blue" id="caseDate" name="caseDate"></span></td>
							<td class="hd1"><wicket:message key="html.index7">停權單位覆核主管</wicket:message>&nbsp;&nbsp;</td>
							<td><span class="color-blue" id="stopApprover" name="stopApprover"></span></td>
						</tr>
						<tr>
							<td class="hd1"><wicket:message key="html.index8">停權單位覆核時間</wicket:message>&nbsp;&nbsp;</td>
							<td colspan="3"><span class="color-blue" id="stopApprTime" name="stopApprTime"></span></td>
						</tr>
					</table>
				</form>
			</div>			
			<!-- 停權Grid明細 -->
			<div id="stopDetail2" style="display:none">
				<form id="formStopDetail2">
					<table width="100%" class="tb2">
						<tr>
							<td width="25%" class="hd1">
								<wicket:message key="subGrid.index1">分行代碼</wicket:message>
							</td>
							<td width="25%">
								<select class="addNeed required" id="branchNo" name="branchNo"></select>
							</td>
							<td width="25%" class="hd1">
								<wicket:message key="subGrid.index3">停權案件號碼</wicket:message>
							</td>
							<td width="25%">
								<input type="text" disabled="true" id="setDocNo" name="setDocNo"/>
							</td>							
						</tr>
						<tr>
							<td class="hd1">
								<wicket:message key="subGrid.index2">分行統編</wicket:message>
							</td>
							<td>
								<input class="addNeed max upText required" type="text" id="custId" name="custId" size="10" maxlength="10"/>&nbsp;&nbsp;
								<wicket:message key="html.index3">重覆序號</wicket:message>
								<input class="addNeed max required" type="text" id="dupNo" name="dupNo" size="1" maxlength="1"/>
							</td>
							<td class="hd1">
								<wicket:message key="subGrid.index4">停權月份</wicket:message>
							</td>
							<td>
								<input type="text" class="max numText required" id="modlifyMons" name="modlifyMons" size="2" maxlength="2"/><wicket:message key="html.index9">月</wicket:message>
								<input type="hidden" id="suspendMons" name="suspendMons"/>
							</td>							
						</tr>						
						<tr>
							<td class="hd1">
								<wicket:message key="subGrid.index6">停權代碼</wicket:message>
							</td>
							<td colspan="3">
								<select class="addNeed required" id="suspendCode" name="suspendCode">
									<option value=""><wicket:message key="html.index10">--請選擇--</wicket:message></option>
									<option value="1"><wicket:message key="html.index11">發生逾期放款者</wicket:message></option>
									<option value="2"><wicket:message key="html.index12">遭公告票據拒絕往來者</wicket:message></option>
									<option value="3"><wicket:message key="html.index13">發生財產遭法院扣押，自行評估或經簽報營運中心核定足以影響財務或還款、繳息能力，應採防範措施者</wicket:message></option>
									<option value="4"><wicket:message key="html.index14">發生退票位於七個營業日內補足，自行評估或經簽報營運中心核定足以影響債信重大者</wicket:message></option>
								</select>								
							</td>							
						</tr>																		
					</table>					
				</form>
			</div>
			<div class="funcContainer tit2">
				<form id="formStopBtn">
					<button type="button" onClick="pageAction.addDetail()">
						<span class="text-only"><wicket:message key="btn.index1">新增</wicket:message></span>
					</button>
					<button type="button" onClick="pageAction.delGridDetail()">
						<span class="text-only"><wicket:message key="btn.index2">刪除</wicket:message></span>
					</button>
					<button type="button" onClick="pageAction.undoGridDetail()">
						<span class="text-only"><wicket:message key="btn.index3">取消刪除</wicket:message></span>
					</button>
			        <div id="gridStopView">
			            <div id="gridStopDetail" name="gridStopDetail"></div>
			        </div>
				</form>
			</div>
        </wicket:extend>
    </body>
</html>
