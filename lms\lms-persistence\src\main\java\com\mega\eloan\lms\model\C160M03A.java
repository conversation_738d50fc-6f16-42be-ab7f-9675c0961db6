package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 整批自動開戶 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C160M03A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C160M03A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 批號 **/
	@Size(max=10)
	@Column(name="PACKNO", length=4, columnDefinition="VARCHAR(4)")
	private String packNo;

	/** 
	 * Excel ID<p/>
	 * Excel file id
	 */
	@Size(max=32)
	@Column(name="EXCELID", length=32, columnDefinition="CHAR(32)")
	private String excelId;

	/** 完成筆數 **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="FINCOUNT", columnDefinition="DECIMAL(4,0)")
	private Integer finCount;

	/** 總筆數 **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="TOTCOUNT", columnDefinition="DECIMAL(4,0)")
	private Integer totCount;

	
	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得批號 **/
	public String getPackNo() {
		return this.packNo;
	}
	/** 設定批號 **/
	public void setPackNo(String value) {
		this.packNo = value;
	}

	/** 
	 * 取得Excel ID<p/>
	 * Excel file id
	 */
	public String getExcelId() {
		return this.excelId;
	}
	/**
	 *  設定Excel ID<p/>
	 *  Excel file id
	 **/
	public void setExcelId(String value) {
		this.excelId = value;
	}

	/** 取得完成筆數 **/
	public Integer getFinCount() {
		return this.finCount;
	}
	/** 設定完成筆數 **/
	public void setFinCount(Integer value) {
		this.finCount = value;
	}

	/** 取得總筆數 **/
	public Integer getTotCount() {
		return this.totCount;
	}
	/** 設定總筆數 **/
	public void setTotCount(Integer value) {
		this.totCount = value;
	}
}
