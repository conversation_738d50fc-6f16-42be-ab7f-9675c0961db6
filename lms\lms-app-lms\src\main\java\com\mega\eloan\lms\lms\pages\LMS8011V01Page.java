package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;

@Controller
@RequestMapping("/lms/lms8011v01")
public class LMS8011V01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CLSDocStatusEnum.編製中);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.Filter, LmsButtonEnum.Add, LmsButtonEnum.Delete, LmsButtonEnum.FCheck,
				LmsButtonEnum.Print);
		// build i18n
		renderJsI18N(LMS8011V01Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/lms/LMS8011V01Page');");
	}

	// @Override
	// public String[] getJavascriptPath() {
	// return new String[] { "pagejs/lms/LMS8011V01Page.js" };
	// }
}
