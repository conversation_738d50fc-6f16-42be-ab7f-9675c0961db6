<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<style>
#C101S01EDiv .hd3 {
	font-weight: bold;
	background: #F5EBFF;
	color: #000000
}

#C101S01EDiv .hd4 {
	font-weight: bold;
	background: #EBF5FF;
	color: #000000
}

#C101S01EDiv .hd5 {
	font-weight: bold;
	background: #EBEBFF;
	color: #000000
}
</style>

		<!-- 個金相關查詢資料檔 -->
		<div id="C101S01EDiv" name="C101S01EDiv">
			<form id="C101S01EForm" name="C101S0_1EForm">
				<fieldset style="margin-top: 5px;"id="interfaceSystemDataInquiryFieldSet">
					<legend>
						<b><th:block
								th:text="#{'C101S01S.title.interfaceSystemDataInquiry'}">徵信資料查詢</th:block></b>
					</legend>
					<div>
						<th:block th:if="${DivOneBtnQueryEJ_ETCH_V0}">
							<button type="button" id="queryDataArchivalRecord"
								name="queryDataArchivalRecord">
								<span class="text-only"><th:block
										th:text="#{'C101S01S.queryDataArchivalRecord'}">資料建檔記錄查詢</th:block></span>
							</button>
						</th:block>
						<th:block th:if="${DivOneBtnQueryEJ_ETCH_V1}">
							<div class="hideWhenC120M01A hideWhenCLS1131S01_readOnly">
								<button type="button" id="btnSendOneButtonQuery_P7"
									name="btnSendOneButtonQuery_P7" class="btnSendOneButtonQuery ">
									<span class="text-only">房貸一鍵查詢</span>
								</button>
								<span style='margin-right: 36px;' class='color-red'><th:block
										th:text="#{'C101S01S.P7_btn_memo'}">(點選即一鍵查詢下列資料)</th:block></span>
								<button type="button" id="btnSendOneButtonQuery_P9"
									name="btnSendOneButtonQuery_P9" class="btnSendOneButtonQuery ">
									<span class="text-only">信貸一鍵查詢</span>
								</button>
								<span style='margin-right: 0px;' class='color-red'><th:block
										th:text="#{'C101S01S.P9_btn_memo'}">(額外加查B29,B68,J10,Z13)</th:block></span>
								<div>
									<span class="color-red " id="href_OneBtnQuery">＊初次使用一鍵查詢功能請先參閱<u
										style="cursor: pointer;">說明文件</u></span>
								</div>
								<table border='1'>
									<tr>
										<td style='background-color: #D6EAF8;'>資料建檔系統</td>
										<td style='background-color: #AED6F1;'>聯徵T70查詢</td>
										<td style='background-color: #85C1E9;' nowrap>MQ票信查詢系統</td>
										<td style='background-color: #3498DB;'>聯徵查詢系統</td>
									</tr>
									<tr style='vertical-align: top;'>
										<td nowrap style='border-bottom: 0px; padding-right: 12px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td class='noborder'>信用異常資料查詢 <br /> + 利害關係人查詢 <br />
														+ 婉卻記錄查詢
													</td>
												</tr>
											</table>
										</td>
										<td nowrap style='border-bottom: 0px; padding-right: 12px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td class='noborder'>證券暨期貨<br />違約交割查詢
													</td>
												</tr>
											</table>
										</td>
										<td style='border-bottom: 0px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td class='noborder'>票信查詢</td>
												</tr>
											</table>
										</td>
										<td nowrap style='border-bottom: 0px; padding-right: 12px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td rowspan='2' class='noborder' nowrap>組合查詢 產品七</td>
													<!--
											<td class='noborder'><label style='font-weight: normal; letter-spacing:normal; padding:0px;'><input type='radio' name='ej_ST_Query_Z21' value='Y'>加查 Z21</label> 
												<label style='font-weight: normal; letter-spacing:normal; padding:0px; margin-left:12px;'><input type='radio' name='ej_ST_Query_Z21' value='N'>不查 Z21</label>
												
												身分證領補換資料查詢驗證
											</td>
										</tr>
										<tr nowrap >
											<td class='noborder'>
												<label style='font-weight: normal; letter-spacing:normal; padding:0px;'><input type='radio' name='ej_ST_Query_Z13' value='Y'>加查 Z13</label>
												<label style='font-weight: normal; letter-spacing:normal; padding:0px; margin-left:12px;'><input type='radio' name='ej_ST_Query_Z13' value='N'>不查 Z13</label>
												
												補充註記(揭露是否 受監護／輔助宣告)
											</td>
											-->
												</tr>
											</table>
										</td>
										<td style='border-bottom: 0px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td class='noborder'>國民身分證領換補<br />資料查詢
													</td>
												</tr>
											</table>
										</td>
										<td style='border-bottom: 0px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td class='noborder'>受監護/輔助宣告<br />資料查詢
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td style='border-top: 0px; padding-bottom: 12px;'><u
											id='btn_qry_C101S01S'>查詢資料建檔</u></td>
										<td style='border-top: 0px; padding-bottom: 12px;'><u
											id='btn_qry_Ejcic_T70'>查詢違約交割</u></td>
										<td style='border-top: 0px; padding-bottom: 12px;'>&nbsp;
										</td>
										<!--	
									<td style='border-top:0px; padding-bottom:12px;'><u id='btn_qry_EJ_ST_Z21'>單查Z21</u>	
									    <u id='btn_qry_EJ_ST_Z13' style='margin-left:12px;'>單查Z13</u>	
									</td>
									-->
									</tr>
								</table>
								<button type="button" id="btGetOneBtnQuery"
									name="btGetOneBtnQuery" class=" ">
									<span class="text-only">取得查詢結果</span>
								</button>
							</div>
						</th:block>
						<th:block th:if="${DivOneBtnQueryEJ_ETCH_V2}">
							<div class="hideWhenC120M01A hideWhenCLS1131S01_readOnly">
								<table border='0'>
									<tr style='vertical-align: top'>
										<td style='border: 0; padding-right: 30px;'>
											<button type="button" id="btnSendOneButtonQuery_P7"
												name="btnSendOneButtonQuery_P7"
												class="btnSendOneButtonQuery ">
												<span class="text-only">房貸一鍵查詢</span>
											</button> <span class='color-red'><th:block
													th:text="#{'C101S01S.P7_btn_memo'}">(點選即一鍵查詢下列資料)</th:block></span>
										</td>
										<td style='border: 0;'>
											<button type="button" id="btnSendOneButtonQuery_P9"
												name="btnSendOneButtonQuery_P9"
												class="btnSendOneButtonQuery ">
												<span class="text-only">信貸一鍵查詢</span>
											</button> <span style='margin-right: 0px;' class='color-red'><th:block
													th:text="#{'C101S01S.P9_btn_memo'}">(額外加查B29,B68,J10)</th:block></span>
											<div style="display: none;">
												<button type="button" id="btnSendOneButtonQuery_Prod69"
													name="btnSendOneButtonQuery_Prod69"
													class="btnSendOneButtonQuery ">
													<span class="text-only">勞工紓困一鍵查詢</span>
												</button>
												<span style='margin-right: 0px;' class='color-red'><th:block
														th:text="#{'C101S01S.Prod69_btn_memo'}">(額外加查B36,D10,R20)</th:block></span>
											</div>
										</td>
									</tr>
								</table>

								<div>
									<span class="color-red " id="href_OneBtnQuery">＊初次使用一鍵查詢功能請先參閱<u
										style="cursor: pointer;">說明文件</u></span>
								</div>
								<table border='1'>
									<tr>
										<td style='background-color: #D6EAF8;'>資料建檔系統</td>
										<td style='background-color: #c0dff4;'>聯徵T70查詢</td>
										<td style='background-color: #aad4f1;' nowrap>MQ票信查詢系統</td>
										<td style='background-color: #95c9ed;'>聯徵查詢系統</td>
										<td style='background-color: #D6EAF8;'>內政部戶政司網站</td>
										<td style='background-color: #c0dff4;'>司法院網站</td>
										<td id="finHoldingSystem" style='background-color: #D6EAF8;'>金控資訊系統
										</td>
										<td style='background-color: #aad4f1;'>負面新聞資料庫查詢</td>
										<td style='background-color: #95c9ed;'>聯徵B95/B98查詢</td>
									</tr>
									<tr style='vertical-align: top;'>
										<td nowrap style='border-bottom: 0px; padding-right: 12px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td class='noborder'>信用異常資料查詢 <br /> + 利害關係人查詢 <br />
														+ 婉卻記錄查詢
													</td>
												</tr>
											</table>
										</td>
										<td nowrap style='border-bottom: 0px; padding-right: 12px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td class='noborder'>證券暨期貨<br />違約交割查詢
													</td>
												</tr>
											</table>
										</td>
										<td style='border-bottom: 0px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td class='noborder'>票信查詢</td>
												</tr>
											</table>
										</td>
										<td nowrap style='border-bottom: 0px; padding-right: 12px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td rowspan='2' class='noborder' nowrap>組合查詢 產品七</td>
													<!--
												<td class='noborder'><label style='font-weight: normal; letter-spacing:normal; padding:0px;'><input type='radio' name='ej_ST_Query_Z21' value='Y'>加查 Z21</label> 
													<label style='font-weight: normal; letter-spacing:normal; padding:0px; margin-left:12px;'><input type='radio' name='ej_ST_Query_Z21' value='N'>不查 Z21</label>
													
													身分證領補換資料查詢驗證
												</td>
											</tr>
											<tr nowrap >
												<td class='noborder'>
													<label style='font-weight: normal; letter-spacing:normal; padding:0px;'><input type='radio' name='ej_ST_Query_Z13' value='Y'>加查 Z13</label>
													<label style='font-weight: normal; letter-spacing:normal; padding:0px; margin-left:12px;'><input type='radio' name='ej_ST_Query_Z13' value='N'>不查 Z13</label>
													
													補充註記(揭露是否 受監護／輔助宣告)
												</td>
												-->
												</tr>
											</table>
										</td>
										<td style='border-bottom: 0px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td class='noborder'>國民身分證領換補<br />資料查詢
													</td>
												</tr>
											</table>
										</td>
										<td style='border-bottom: 0px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td class='noborder'>受監護/輔助宣告<br />資料查詢
													</td>
												</tr>
											</table>
										</td>
										<td style='border-bottom: 0px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td class='noborder'>負面新聞<br />資料庫查詢
													</td>
												</tr>
											</table>
										</td>
										<td style='border-bottom: 0px;'>
											<table border='0' class='tb2'>
												<tr style='vertical-align: top;'>
													<td class='noborder'>農安貸款審核資訊(B95) <br />
														青安貸款審核資訊(B98)
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td style='border-top: 0px; padding-bottom: 12px;'><u
											id='btn_qry_C101S01S'>查詢資料建檔</u></td>
										<td style='border-top: 0px; padding-bottom: 12px;'><u
											id='btn_qry_Ejcic_T70'>查詢違約交割</u></td>
										<td style='border-top: 0px; padding-bottom: 12px;'>&nbsp;
										</td>
										<td style='border-top: 0px; padding-bottom: 12px;'>&nbsp;
										</td>
										<td width="18%" nowrap
											style='border-top: 0px; padding-bottom: 12px;'><u
											id='btn_qry_API_C101S01S'>查詢身分證領換補</u></td>
										<td width="16%" nowrap
											style='border-top: 0px; padding-bottom: 12px;'><u
											id='btn_qry_RPA_C101S04W'>查詢受監護/輔助</u></td>
										<td width="16%" nowrap
											style='border-top: 0px; padding-bottom: 12px;'><u
											id='btn_qry_WiseNews'>查詢負面新聞</u></td>
										<td width="16%" nowrap
											style='border-top: 0px; padding-bottom: 12px;'><u
											id='btn_qry_Ejcic_B98_B95'>查詢(僅青安貸款需要)</u></td>
										<!--	
									<td style='border-top:0px; padding-bottom:12px;'><u id='btn_qry_EJ_ST_Z21'>單查Z21</u>	
									    <u id='btn_qry_EJ_ST_Z13' style='margin-left:12px;'>單查Z13</u>
										
										
										<div style='margin-top:12px;'>
										<u id='btn_qry_EJ_ST_Z21_Prod69'>單查Z21(辦理勞工紓困貸款)</u>	
									    <u id='btn_qry_EJ_ST_Z13_Prod69' style='margin-left:12px;'>單查13(辦理勞工紓困貸款)</u>
										<u id='btn_qry_EJ_ST_D10' style='margin-left:24px;'>單查D10</u>	
										<u id='btn_qry_EJ_ST_R20' style='margin-left:12px;'>單查R20</u>	
										</div>
									</td>
									-->
										<td id="queryFinDefaultDeli"
											style='border-top: 0px; padding-bottom: 12px;'><u
											id='btn_qry_EAI_CURIQ01'>查詢金控違約交割</u></td>
									</tr>
								</table>
								<button type="button" id="btGetOneBtnQuery"
									name="btGetOneBtnQuery" class=" ">
									<span class="text-only">取得查詢結果</span>
								</button>
								<button type="button" id="btGetOneBtnPrintHTML"
									name="btGetOneBtnPrintHTML" class=" ">
									<span class="text-only">一鍵列印HTML</span>
								</button>
								<span style="display: none;"><button type="button"
										id="btGetOneBtnPrintPDF" name="btGetOneBtnPrintPDF" class=" ">
										<span class="text-only">一鍵列印PDF</span>
									</button></span>
							</div>
						</th:block>
						<div style="margin-top: 5px;" id="dataArchivalRecordData">
							<div id="gridview1"></div>
						</div>
					</div>
				</fieldset>
				<table width="100%">
					<tr>
						<td colspan="2"><th:block th:text="#{'C101S01E.ejcic'}">聯徵EJCIC</th:block>/<th:block
								th:text="#{'C101S01E.etch'}">票信EJCIC</th:block>
							<th:block th:text="#{'C101S01E.queryHttp'}">查詢網址</th:block>： <a
							href="https://employee.megabank.com.tw/" target="_blank">https://employee.megabank.com.tw/</a>
						</td>
						<td><a id="ejcicLink" href="#" onclick=""><th:block
									th:text="#{'C101S01E.ejcic'}">聯徵EJCIC</th:block>
								<th:block th:text="#{'C101S01E.queryHttp'}">查詢網址</th:block></a></td>
						<td><a id="etchLink" href="#" onclick=""><th:block
									th:text="#{'C101S01E.etch'}">票信EJCIC</th:block>
								<th:block th:text="#{'C101S01E.queryHttp'}">查詢網址</th:block></a></td>
					</tr>
					<tr>
						<td colspan="4">
							<button type="button" id="btDataQuery" name="btDataQuery">
								<span class="text-only"><th:block
										th:text="#{'C101S01E.btDataQuery'}">相關資料查詢</th:block></span>
							</button>
							<button type="button" id="btEjcicQueryPrint"
								name="btEjcicQueryPrint" class="forview">
								<span class="text-only"><th:block
										th:text="#{'C101S01E.ejcic'}">聯徵EJCIC</th:block>
									<th:block th:text="#{'C101S01E.queryPrint'}">查詢列印</th:block></span>
							</button>
							<button type="button" id="btEtchQueryPrint"
								name="btEtchQueryPrint" class="forview">
								<span class="text-only"><th:block
										th:text="#{'C101S01E.etch'}">票信EJCIC</th:block>
									<th:block th:text="#{'C101S01E.queryPrint'}">查詢列印</th:block></span>
							</button> <sub><th:block th:text="#{'C101S01E.dataOrigin'}">資料來源</th:block>:<th:block
									th:text="#{'C101S01E.ejcicDb'}">票交所及聯徵中心資料庫</th:block></sub>&nbsp;&nbsp;
							<button type="button" id="btSendAmlList" name="btSendAmlList"
								style='display: none;'>
								<span class="text-only"><th:block
										th:text="#{'C101S01E.btSendAmlList'}">傳送名單掃描</th:block></span>
							</button>
							<button type="button" id="btCheckAmlResult"
								name="btCheckAmlResult" style='display: none;'>
								<span class="text-only"><th:block
										th:text="#{'C101S01E.btCheckAmlResult'}">取得黑名單查詢結果</th:block></span>
							</button>
						</td>

					</tr>
					<tr>
						<td width="20%" align="right">◎<th:block
								th:text="#{'C101S01E.eChkFlag'}">有無票信退補記錄</th:block>：&nbsp;&nbsp;
						</td>
						<td width="30%"><input type="radio" id="eChkFlag"
							name="eChkFlag" class="ABCD CD" codeType="HaveNoNa" /></td>
						<td width="20%" align="right">◎<th:block
								th:text="#{'C101S01E.eJcicFlag'}">有無聯徵逾催呆記錄</th:block>：&nbsp;&nbsp;
						</td>
						<td width="30%"><input type="radio" id="eJcicFlag"
							name="eJcicFlag" class="ABCD CD" codeType="HaveNoNa" /></td>
					</tr>
					<tr>
						<td align="right"><th:block
								th:text="#{'C101S01E.eChkDDate'}">票信資料截止日</th:block>：&nbsp;&nbsp;</td>
						<td><input type="text" id="eChkDDate" name="eChkDDate"
							class="date ABCD CD" /></td>
						<td align="right"><th:block
								th:text="#{'C101S01E.eJcicDDate'}">聯徵資料日期</th:block>：&nbsp;&nbsp;</td>
						<td><input type="text" id="eJcicDDate" name="eJcicDDate"
							class="date ABCD CD" /></td>
					</tr>
					<tr>
						<td align="right"><th:block
								th:text="#{'C101S01E.eChkQDate'}">票信查詢日期</th:block>：&nbsp;&nbsp;</td>
						<td><input type="text" id="eChkQDate" name="eChkQDate"
							class="date ABCD CD" /></td>
						<td align="right"><th:block
								th:text="#{'C101S01E.eJcicQDate'}">聯徵查詢日期</th:block>：&nbsp;&nbsp;</td>
						<td><input type="text" id="eJcicQDate" name="eJcicQDate"
							class="date ABCD CD" /></td>
					</tr>
					<tr>
						<td align="right">◎<th:block
								th:text="#{'C101S01E.isFromOld'}">無法提供票信電子資料</th:block>：&nbsp;&nbsp;
						</td>
						<td><input type="radio" id="isFromOld" name="isFromOld"
							class="ABCD AB" codeType="Common_YesNo" itemStyle="sort:desc" /></td>
						<td align="right">&nbsp;&nbsp;</td>
						<td>&nbsp;</td>
					</tr>
				</table>
				<br />
				<table class="tb2" width="100%">
					<tr>
						<td width="45%" class="hd3" align="center"><th:block
								th:text="#{'C101S01E.titleType'}">類別</th:block></td>
						<td width="25%" class="hd3" align="center"><th:block
								th:text="#{'C101S01E.titleHaveNo'}">有/無</th:block></td>
						<td width="20%" class="hd3" align="center"><th:block
								th:text="#{'C101S01E.titleDetial'}">明細</th:block></td>
						<td width="10%" class="hd3" align="center">&nbsp;</td>
					</tr>
					<tr>
						<td class="hd4" colspan="4"><th:block
								th:text="#{'C101S01E.subTitle1'}">本行資料庫</th:block>&nbsp;&nbsp; <span
							class="text-only color-red"><th:block
									th:text="#{'C101S01E.memo'}">(如0024中有建配偶資料者，其配偶利害關係人資料亦會一併查詢)</th:block></span>
						</td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata1'}">婉卻紀錄</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata1" name="isQdata1"
							class="readonly" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata1"><th:block
									th:text="#{'C101S01E.btQuery'}">
									<th:block th:text="#{'C101S01E.btQuery'}">查詢</th:block>
								</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata29'}">是否有異常通報紀錄</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata29" name="isQdata29"
							class="readonly" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata29"><th:block
									th:text="#{'C101S01E.btQuery'}">
									<th:block th:text="#{'C101S01E.btQuery'}">查詢</th:block>
								</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata2'}">本行利害關係人</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata2" name="isQdata2"
							class="readonly" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata2"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata3'}">金控利害關係人(44條)</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata3" name="isQdata3"
							class="readonly" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata3"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata16'}">金控利害關係人(45條)</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata16" name="isQdata16"
							class="readonly" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata16"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata6'}">主從債務人(不含本次資料)</th:block>&nbsp;&nbsp;
							<button type="button" id="btOpenDetial" name="btOpenDetial"
								class="forview">
								<span class="text-only"><th:block
										th:text="#{'C101S01E.btOpenDetial'}">開啟關聯戶貸款查詢明細</th:block></span>
							</button></td>
						<td><input type="radio" id="isQdata6" name="isQdata6"
							class="readonly" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata6"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr class="mortChk" style="display: none;">
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata31'}">聯徵B42從債務查詢－擔保品類別</th:block>
							<a class="tip" href="#"><span class="text-red">(說明)</span>
								<p style="z-index: 9999">
									<th:block th:text="#{'C101S01E.isQdata31.isQdata32.explain'}">有無房貸</th:block>
								</p> </a></td>
						<td><input type="radio" id="isQdata31" name="isQdata31"
							class="" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata31"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr class="mortChk" style="display: none;">
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata32'}">聯徵B42共同債務查詢－擔保品類別</th:block>
							<a class="tip" href="#"><span class="text-red">(說明)</span>
								<p style="z-index: 9999">
									<th:block th:text="#{'C101S01E.isQdata31.isQdata32.explain'}">有無房貸</th:block>
								</p> </a></td>
						<td><input type="radio" id="isQdata32" name="isQdata32"
							class="" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata32"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr class="mortChk" style="display: none;">
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata33'}">借款人三年內購置不動產結案資訊，是否有近一年有二戶以上授信借貸結案紀錄</th:block>
							<a class="tip" href="#"><span class="text-red">(說明)</span>
								<p style="z-index: 9999">
									<th:block th:text="#{'C101S01E.isQdata33.isQdata34.explain'}">檢視聯徵紀錄BAM425~428資訊</th:block>
								</p> </a></td>
						<td><input type="radio" id="isQdata33" name="isQdata33"
							class="" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata33"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr class="mortChk" style="display: none;">
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata34'}">借款人三年內購置不動產結案資訊，是否有近三年有二戶以上授信借貸結案紀錄</th:block>
							<a class="tip" href="#"><span class="text-red">(說明)</span>
								<p style="z-index: 9999">
									<th:block th:text="#{'C101S01E.isQdata33.isQdata34.explain'}">檢視聯徵紀錄BAM425~428資訊</th:block>
								</p> </a></td>
						<td><input type="radio" id="isQdata34" name="isQdata34"
							class="" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata34"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata5'}">對同一自然人授信總餘額比率</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata5" name="isQdata5"
							class="readonly" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata5"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata18'}">疑似偽造證件或財力證明</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata18" name="isQdata18"
							class="readonly" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata18"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'title.wm_flag.A'}">績優理財客戶</th:block>&nbsp;&nbsp;</td>
						<td><span id='c101s01e_wm_data'></span> &nbsp; <th:block
								th:text="#{'l120s01m.item2'}">查詢日期</th:block>：<span
							id='wm_qDate'></span> &nbsp;</td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQWM">
								<!-- 同 ClsConstants.財管理財客戶等級 -->
								<th:block th:text="#{'C101S01E.btQuery'}">查詢</th:block>
						</a></td>
					</tr>
					<!-- 大數據風險API START-->
					<th:block th:if="${DivWitcherFin}">
						<tbody>
							<tr>
								<td class="hd4" colspan="4"><th:block
										th:text="#{'C101S01E.subTitle4'}">大數據風險報告</th:block> <a
									href="#" class="query" queryType="witcherFin">
										<button type="button">
											<span class="text-only"><th:block
													th:text="#{'C101S01E.btQuery'}">查詢</th:block></span>
										</button>
								</a>&nbsp;&nbsp; <th:block th:text="#{'C101S01E.s02b.mpnum'}">查詢電話號碼:</th:block><span
									id="mpnum" class="field"></span>&nbsp;&nbsp; <th:block
										th:text="#{'C101S01E.s02b.wfUpdateTime'}">最後查詢時間:</th:block><span
									id="wfUpdateTime" class="field"></span></td>
							</tr>
							<!-- 風險報告 -->
							<tr>
								<td class="hd2"><th:block
										th:text="#{'C101S01E.in_fraud_db'}">疑似詐欺電話</th:block>&nbsp;&nbsp;</td>
								<td><input type="radio" id="in_fraud_db"
									name="in_fraud_db" class="ABCD CD" codeType="C101S02B_HML" /></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
							<tr>
								<td class="hd2"><th:block
										th:text="#{'C101S01E.phony_account_cluster'}">疑似人頭戶</th:block>&nbsp;&nbsp;</td>
								<td><input type="radio" id="phony_account_cluster"
									name="phony_account_cluster" class="ABCD CD"
									codeType="C101S02B_HML" /></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
							<tr>
								<td class="hd2"><th:block
										th:text="#{'C101S01E.agency_cluster'}">與代辦業者電話號碼聯繫頻率</th:block>&nbsp;&nbsp;</td>
								<td><input type="radio" id="agency_cluster"
									name="agency_cluster" class="ABCD CD" codeType="C101S02B_HML" /></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
							<tr>
								<td class="hd2"><th:block
										th:text="#{'C101S01E.reachable'}">一定期間內沒有任何通話或連網紀錄</th:block>&nbsp;&nbsp;</td>
								<td><input type="radio" id="reachable" name="reachable"
									class="ABCD CD" codeType="C101S02B_HML" /></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
							<tr>
								<td class="hd2"><th:block
										th:text="#{'C101S01E.special_loan_cluster'}">與民間特殊借貸業者電話號碼聯繫頻率</th:block>&nbsp;&nbsp;</td>
								<td><input type="radio" id="special_loan_cluster"
									name="special_loan_cluster" class="ABCD CD"
									codeType="C101S02B_HML" /></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
							<tr>
								<td class="hd2"><th:block
										th:text="#{'C101S01E.pawnshop_cluster'}">與當鋪業者電話號碼聯繫頻率</th:block>&nbsp;&nbsp;</td>
								<td><input type="radio" id="pawnshop_cluster"
									name="pawnshop_cluster" class="ABCD CD" codeType="C101S02B_HML" /></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
							<tr>
								<td class="hd2"><th:block
										th:text="#{'C101S01E.debt_collect_cluster'}">與催繳討債業者電話號碼聯繫頻率</th:block>&nbsp;&nbsp;</td>
								<td><input type="radio" id="debt_collect_cluster"
									name="debt_collect_cluster" class="ABCD CD"
									codeType="C101S02B_HML" /></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
							<tr>
								<td class="hd2"><th:block
										th:text="#{'C101S01E.spec_career_cluster'}">與八大行業業者電話號碼聯繫頻率</th:block>&nbsp;&nbsp;</td>
								<td><input type="radio" id="spec_career_cluster"
									name="spec_career_cluster" class="ABCD CD"
									codeType="C101S02B_HML" /></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
							<tr>
								<td class="hd2"><th:block
										th:text="#{'C101S01E.installment_cluster'}">與資融業者電話號碼聯繫頻率</th:block>&nbsp;&nbsp;</td>
								<td><input type="radio" id="installment_cluster"
									name="installment_cluster" class="ABCD CD"
									codeType="C101S02B_HML" /></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
							<tr>
								<td class="hd2"><th:block
										th:text="#{'C101S01E.whitelist_cluster'}">與銀行業電話號碼聯繫頻率</th:block>&nbsp;&nbsp;</td>
								<td><input type="radio" id="whitelist_cluster"
									name="whitelist_cluster" class="ABCD CD"
									codeType="C101S02B_HML" /></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
							<!-- 信用評等 -->
							<tr>
								<td class="hd2"><th:block th:text="#{'C101S01E.rank'}">大數據風險評等</th:block>&nbsp;&nbsp;</td>
								<td><span id="rank" class="field"></span></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
							<!-- 特定代書資料庫(客製化) -->
							<tr>
								<td class="hd2"><th:block
										th:text="#{'C101S01E.suspicious_land_administration_agents_cluster'}">與特定代書業者電話號碼聯繫頻率</th:block>&nbsp;&nbsp;</td>
								<td><input type="radio" id="suspicious_laa_cluster"
									name="suspicious_laa_cluster" class="ABCD CD"
									codeType="C101S02B_LAA" /></td>
								<td align="center"></td>
								<td align="center"></td>
							</tr>
						</tbody>
					</th:block>
					<!-- 大數據風險API END-->
					<tr>
						<td class="hd4" colspan="4"><th:block
								th:text="#{'C101S01E.subTitle2'}">外來資料庫(聯徵、票交所、兆豐證...........)</th:block>&nbsp;&nbsp;</td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata4'}">歸戶(本行餘額為a-Loan資料、他行餘額為聯徵資料)</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata4" name="isQdata4"
							class="ABCD CD" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata4"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata14'}">近一年內不含查詢當日非Z類被聯行查詢紀錄明細</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata14" name="isQdata14"
							class="ABCD CD" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata14"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata17'}">擔任負責人或董監事之企業是否於本行有授信額度達一億元以上</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata17" name="isQdata17"
							class="ABCD CD" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata17"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr style='vertical-align: top;'>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata7'}">黑名單</th:block>
							<span class="color-blue"><a id="eNameLink" href="#">(<span
									type="text" id="eName" class="field"></span>&nbsp;)
							</a></span>&nbsp;&nbsp;<br /> <a
							href="http://www.boca.gov.tw/sp?xdURL=E2C/c2102-5.asp&CtNode=677&mp=1"
							target="_blank"><th:block
									th:text="#{'C101S01E.isQdata7Link'}">外交部領事事務局外文姓名中譯英網站</th:block></a>&nbsp;&nbsp;<br />
							<span type="text" id="isQdata7Memo" class="field"></span> <select
							id="ans1" name="ans1" disabled="true">
								<option value=""></option>
								<option value="02">存在於黑名單資料。</option>
								<option value="04">疑似黑名單。</option>
								<option value="14">疑似自行黑名單 請至0015-16 自行黑名單查詢。</option>
								<option value="61">疑似政治敏感人物名單 請至0015-61 政治敏感人物查詢。</option>
								<!-- 參考 CLS1131R01RptServiceImpl，應和列印的文字一致 -->
						</select>
							<div id="amlDiv2" class=" " style='display: none;'>
								<th:block th:text="#{'L120S09A.refNo'}">掃描對象編號</th:block>
								：<span id='l120s09b_refNo' class='' style='color: blue;'></span>
								&nbsp;<br />
								<th:block th:text="#{'L120S09A.uniqueKey'}">掃描批號</th:block>
								：<span id='l120s09b_uniqueKey' class='' style='color: blue;'></span>
								&nbsp;
							</div></td>
						<td><input type="radio" id="isQdata7" name="isQdata7"
							class="ABCD CD" codeType="HaveNoNa" />
							<div id="amlDiv" class=" " style='display: none;'>
								<th:block th:text="#{'L120S09B.queryDateS'}">資料查詢日期</th:block>
								：<span id='l120s09b_queryDateS' class='' style='color: blue;'></span>
								&nbsp;<br />
								<th:block th:text="#{'L120S09B.ncResult'}">案件調查結果</th:block>
								：<span id='l120s09b_ncResult' class='' style='color: blue;'></span>
								&nbsp;<br />
								<!--
									<th:block th:text="#{'L120S09B.ncCaseId'}">案例ID</th:block>：<span id='l120s09b_ncCaseId' class='' style='color:blue;' ></span> &nbsp;<br/>
									-->
								<th:block th:text="#{'L120S09A.memo'}">命中代碼</th:block>
								：<span id='l120s09a_memo' class='' style='color: blue;'></span>
								&nbsp;<br /> <span>※<a
									th:href="@{/img/lms/AML_Route_Rule_EL.htm}" target="_blank"
									style='color: blue;'><th:block
											th:text="#{'L120S09A.openRouteRule'}">開啟命中代碼說明</th:block></a></span>
							</div></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata7"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr style='vertical-align: top;'>
						<td class="hd2"><th:block th:text="#{'C101S01E.cmfWarnp'}">告誡戶</th:block><br />
							<span type="text" id="cmfwarnpQueryResultInfo" class="field"></span>
						</td>
						<td><input type="radio" id="cmfWarnpResult"
							name="cmfWarnpResult" class="ABCD CD" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="cmfWarnp"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr id="isQdata8Tr">
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata8'}">證券暨期貨違約交割紀錄</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata8" name="isQdata8"
							class="ABCD CD" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata8"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr id="isQdata30Tr">
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata30'}">聯徵T70證券暨期貨違約交割記錄</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata30" name="isQdata30"
							class="ABCD CD" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata30"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata9'}">退票紀錄</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata9" name="isQdata9"
							class="ABCD CD" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata9"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata10'}">拒絕往來紀錄</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata10" name="isQdata10"
							class="ABCD CD" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata10"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata11'}">主債務逾期、催收、呆帳紀錄</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata11" name="isQdata11"
							class="ABCD CD" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata11"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata13'}">信用卡強停紀錄</th:block>&nbsp;&nbsp;</td>
						<td><input type="radio" id="isQdata13" name="isQdata13"
							class="ABCD CD" codeType="HaveNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQdata13"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'l120s01m.item26'}">「授信信用風險管理」遵循檢核</th:block>&nbsp;&nbsp;</td>
						<td><th:block th:text="#{'l120s01m.item2'}">查詢日期</th:block>：<span
							id='l120s01m_queryDate' class='class_l120s01m_queryDate'
							style='color: blue; text-decoration: underline;'></span> &nbsp;</td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center"><a href="#" class="query"
							queryType="isQL120S01M"><th:block
									th:text="#{'C101S01E.btQuery'}">查詢</th:block></a></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata12'}">身分證補、換發紀錄</th:block>&nbsp;&nbsp;<br />
							<a href="https://www.ris.gov.tw/webapply/6" target="_blank"><th:block
									th:text="#{'C101S01E.isQdata12Link'}">國民身分證領補換查詢網站</th:block></a></td>
						<td><input type="radio" id="isQdata12" name="isQdata12"
							codeType="YesNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center">&nbsp;<!-- <a href="#" class="query" queryType="isQdata12" ><th:block th:text="#{'C101S01E.btQuery'}">查詢</th:block></a> --></td>
					</tr>
					<tr>
						<td class="hd2"><th:block th:text="#{'C101S01E.isQdata15'}">成年監護制度查詢紀錄</th:block>&nbsp;&nbsp;<br />
							<a href="http://domestic.judicial.gov.tw/abbs/wkw/WHD9HN01.jsp"
							target="_blank"><th:block
									th:text="#{'C101S01E.isQdata15Link'}">司法院訊息查詢網站</th:block></a></td>
						<td><input type="radio" id="isQdata15" name="isQdata15"
							codeType="YesNoNa" /></td>
						<td align="center"><a href="#" class="showDetial">【<th:block
									th:text="#{'C101S01E.displayDetial'}">顯示明細</th:block>】
						</a></td>
						<td align="center">&nbsp;<!-- <a href="#" class="query" queryType="isQdata15" ><th:block th:text="#{'C101S01E.btQuery'}">查詢</th:block></a> --></td>
					</tr>
					<tr>
						<td class="hd4" colspan="4"><th:block
								th:text="#{'C101S01E.subTitle3'}">自行查驗區</th:block>&nbsp;&nbsp;</td>
					</tr>
					<tr>
						<td class="hd2"><b class="star">＊</b> <th:block
								th:text="#{'C101S01E.mbRlt33_thisCase'}">本案</th:block> <th:block
								th:text="#{'C101S01E.mbRlt33'}">「銀行法第33條之2、銀行法第33條之4」之情形</th:block>&nbsp;&nbsp;<br />
							<th:block th:text="#{'C101S01E.mbRlt33_extra'}">(若有，請敘明授信對象之利害關係情形，並須為擔保授信)</th:block>
						</td>
						<td colspan='3'><label
							style="letter-spacing: 0px; cursor: pointer;"><input
								id="mbRlt33" type="radio" value="1" name="mbRlt33">
							<th:block th:text="#{'rdo.have'}">有</th:block></label> <label
							style="letter-spacing: 0px; cursor: pointer;"><input
								id="mbRlt33" type="radio" value="2" name="mbRlt33">
							<th:block th:text="#{'rdo.nohave'}">無</th:block></label> <label
							style="letter-spacing: 0px; cursor: pointer;"><input
								id="mbRlt33" type="radio" value="3" name="mbRlt33">
							<th:block th:text="#{'rdo.notApplicable'}">不適用</th:block></label> <br>&nbsp;
						<input type="text" class="max" id="mbRltDscr33" name="mbRltDscr33"
							maxlength="1800" maxlengthC="600" size="70" /></td>
					</tr>
					<tr style='vertical-align: top;'>
						<td class="hd2"><b class="star">＊</b> <th:block
								th:text="#{'C101S01E.caseSrcFlag'}">進件來源</th:block></td>
						<td colspan='3'>
							<table border='0'>
								<tr>
									<!--										<td style='border: 0px;'><label><input type="radio" id="caseSrcFlag" name="caseSrcFlag" value="L"><th:block th:text="#{'C101S01E.caseSrcFlag.L'}">經地政士進件</th:block></label></td>-->
									<!--										<td style='border: 0px;'><label><input type="radio" id="caseSrcFlag" name="caseSrcFlag" value="P"><th:block th:text="#{'C101S01E.caseSrcFlag.P'}">個人送件</th:block></label></td>									-->
									<!--										<td nowrap style='border: 0px;'><label><input type="radio" id="caseSrcFlag" name="caseSrcFlag" value="O"><th:block th:text="#{'C101S01E.caseSrcFlag.O'}">其它</th:block></label></td>-->

									<td style='border: 0px;'><label><input
											type="radio" id="caseSrcFlag" name="caseSrcFlag" value="A">
										<th:block th:text="#{'C101S01E.caseSrcFlag.A'}">買賣件經地政士辦理</th:block></label></td>
									<td style='border: 0px;'><label><input
											type="radio" id="caseSrcFlag" name="caseSrcFlag" value="B">
										<th:block th:text="#{'C101S01E.caseSrcFlag.B'}">買賣件非經地政士辦理</th:block></label></td>
									<td nowrap style='border: 0px;'><label><input
											type="radio" id="caseSrcFlag" name="caseSrcFlag" value="C">
										<th:block th:text="#{'C101S01E.caseSrcFlag.C'}">非買賣件</th:block></label></td>
								</tr>
							</table>
							<div id='div_caseSrcFlag_O'>
								<th:block th:text="#{'C101S01E.label.caseSrcMemo'}">說明</th:block>
								：<input type="text" class="max" id="caseSrcMemo"
									name="caseSrcMemo" maxlength="60" maxlengthC="20" size="50" />
							</div>

							<div>
								<span style='padding-right: 190px;'></span><span th:utext="${C101S01E_laaWord_url}">
									<!--地政士url-->
								</span> <span style='padding-right: 5px;'></span><span
									id="href_LaaNoticeItem" class="text-red">※<u
									style="cursor: pointer;">填寫說明</u></span> <br />
								<button type="button" id="C101S01YAdd">
									<span class="text-only"><th:block
											th:text="#{'C101S01Y.add'}">新增</th:block></span>
								</button>
								<button type="button" id="C101S01YDelete">
									<span class="text-only"><th:block
											th:text="#{'C101S01Y.delete'}">刪除</th:block></span>
								</button>
								<button type="button" id="queryRPA">
									<th:block th:text="#{'queryRPA'}">自動RPA查詢黑名單</th:block>
								</button>
								<button type="button" class="forview" id="queryRPAResult">
									<th:block th:text="#{'queryRPAResult'}">RPA查詢結果</th:block>
								</button>
								<div id="laaGrid"
									style="width: 100%; margin-left: 0px; margin-right: 0px"></div>
								<div>
									<span>╳:地政士黑名單拒絕名單</span><span>△:地政士黑名單警示名單</span>
								</div>
								<span class="text-red field" id='msg_Laa'></span>
								<table class="tb2">
									<tr>
										<td class="hd2" nowrap><th:block
												th:text="#{'C101S01E.agentPId'}">陪同/代辦人員統編</th:block></td>
										<td><input type="text" id="agentPId" name="agentPId"
											class="" maxlength="10" size="12" /></td>
									</tr>
									<tr>
										<td class="hd2" nowrap><th:block
												th:text="#{'C101S01E.agentPName'}">陪同/代辦人員姓名</th:block></td>
										<td nowrap><input type="text" id="agentPName"
											name="agentPName" class="" maxlength="120" maxlengthC="40"
											size="20" /></td>
									</tr>
								</table>
								<span class="text-red field" id='msg_agentPIdCmp'></span>
							</div>

						</td>
					</tr>
					<tr>
						<td class="hd2"><b class="star">＊</b>
						<th:block th:text="#{'C101S01E.amlBadNews'}">AML重大負面新聞</th:block></td>
						<td colspan='3'>
							<div th:utext="${amlBadNewsLabel}"></div>
						</td>
					</tr>
				</table>

				<table class="tb2" width="100%" border="0" cellspacing="0"
					cellpadding="0">
					<tbody>
						<tr>
							<td class="hd1" colspan="2">
								<div style="text-align: left;">
									<th:block th:text="#{'C101S01E.creditBadNews'}">信用負面新聞(訊息)</th:block>
								</div>
							</td>
						</tr>
						<tr>
							<td colspan="2">
								<div style="color: red; font-weight: bold;">
									<th:block th:text="#{'C101S01E.creditBadNews.note'}">負責人從事高風險衍生性金融交易；集團企業整體營運狀況及關係人資金往來情形，若有財務不佳或不透明、關係人資金往來頻繁或用途不明等應列入本負面新聞(訊息)揭露</th:block>
								</div>
								<div id="creditBadNewsDiv">
									<textarea class="ickeditor" id="creditBadNews"name="creditBadNews"></textarea>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</form>

			<!-- 黑名單英文名 -->
			<div id="eNameThickBox" style="display: none;">
				<table class="tb2" width="100%">
					<tr>
						<td class="hd2" align="right"><th:block
								th:text="#{'C101S01E.eName'}">英文名</th:block>&nbsp;&nbsp;</td>
						<td><input type="text" id="eName" name="eName"
							class="required max atTB"
							onblur="this.value = (this.value || '').toUpperCase();"
							maxlength="120" size='70' /></td>
					</tr>
					<tr class='hs_SAS_AML' style="display: none;">
						<td class="hd2" align="right">中心英文名&nbsp;</td>
						<td><span id="eName0024" class="atTB"></span>&nbsp;</td>
					</tr>
				</table>
				<!--J-109-0190_05097_B1001 為加速勞工紓困貸款之案件簽報，簡化e-Loan洗錢防制流程-->
				<!--
					<br>
					<span class="color-red" >若本案為勞工紓困貸款案件(產品種類：69)，e-Loan系統中之制裁/管制名單掃瞄，僅須於「動審」階段執行</span>	
					<br>
					-->
				<br>
				<a
					href="http://www.boca.gov.tw/sp?xdURL=E2C/c2102-5.asp&CtNode=677&mp=1"
					target="_blank"><th:block th:text="#{'C101S01E.isQdata7Link'}">外交部領事事務局外文姓名中譯英網站</th:block></a>


			</div>


			<!--信用風險管理遵循Credit risk follow-->
			<div id="tL120s01m" style="display: none;">
				<form id="formL120s01m">
					<div>
						<table class="tb2" width="100%">
							<tr style="border: 0px hidden; border-width: 0px;">
								<td style="border: 0px;" colspan="4" align="right"><th:block
										th:text="#{'l120s01m.item1'}">(單位:TWD 仟元)</th:block></td>
							</tr>
							<tr>
								<td class="hd1" width="15%" align="right"><th:block
										th:text="#{'l120s01m.item2'}">查詢日期</th:block>&nbsp;&nbsp;</td>
								<td width="35%"><input type="text" id="queryDate"
									class="date" name="queryDate" size="13" /></td>
								<td class="hd1" width="15%" align="right"><th:block
										th:text="#{'l120s01m.item3'}">淨值</th:block>&nbsp;&nbsp;</td>
								<td width="35%"><input type="text" class="numeric"
									id="netVal" name="netVal" positiveonly="true" integer="15"
									fraction="2" size="13" maxlength="13" /> &nbsp;</td>
							</tr>
							<tr>
								<td class="hd1" width="15%" align="right"><th:block
										th:text="#{'l120s01m.item4'}">隸屬集團</th:block>&nbsp;&nbsp;</td>
								<td width="35%"><span class="color-red" id="grpNo"
									name="grpNo"></span>&nbsp;<span class="color-red" id="grpName"
									name="grpName"></span>&nbsp;</td>
								<td class="hd1" align="right" width="15%"><th:block
										th:text="#{'l120s01m.item5'}">集團評等</th:block>&nbsp;&nbsp;</td>
								<!--J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。-->
								<td><span class="color-red" id="grpYear" name="grpYear"
									width="35%"></span>&nbsp; <select id="grpGrade"
									name="grpGrade">
										<!--
										  <option value="1"><th:block th:text="#{'l120s01m.item6'}">1級</th:block></option>
										  <option value="2"><th:block th:text="#{'l120s01m.item7'}">2級</th:block></option>
										  <option value="3"><th:block th:text="#{'l120s01m.item8'}">3級</th:block></option>
										  <option value="4"><th:block th:text="#{'l120s01m.item9'}">4級</th:block></option>
										  <option value="5"><th:block th:text="#{'l120s01m.item10'}">5級</th:block></option>
										  <option value="6"><th:block th:text="#{'l120s01m.item11'}">未評等</th:block></option>
										  <option value="7"><th:block th:text="#{'l120s01m.item12'}">問題集團</th:block></option>
										  -->
								</select>&nbsp; <span id="grpSizeLvlShow"></span></td>
							</tr>
							<tr>
								<td class="hd1" width="15%" align="right"><th:block
										th:text="#{'l120s01m.item15'}">銀行法所稱與本行有利害關係</th:block>&nbsp;&nbsp;</td>
								<td width="35%"><label><input id="mbRlt"
										name="mbRlt" type="radio" value='1' />
									<th:block th:text="#{'rdo.have'}">有</th:block></label> <label><input
										name="mbRlt" type="radio" value='2' />
									<th:block th:text="#{'rdo.nohave'}">無</th:block> </label> <label><input
										name="mbRlt" type="radio" value='3' />
									<th:block th:text="#{'rdo.notApplicable'}">不適用</th:block></label></td>
								<td class="hd1" width="15%" align="right"><th:block
										th:text="#{'l120s01m.item16'}">金控法44條所稱與金控有利害關係人</th:block>&nbsp;&nbsp;</td>
								<td width="35%"><label><input id="mhRlt44"
										name="mhRlt44" type="radio" value='1' />
									<th:block th:text="#{'rdo.have'}">有</th:block> </label> <label><input
										name="mhRlt44" type="radio" value='2' />
									<th:block th:text="#{'rdo.nohave'}">無</th:block> </label> <label><input
										name="mhRlt44" type="radio" value='3' />
									<th:block th:text="#{'rdo.notApplicable'}">不適用</th:block></label></td>
							</tr>
							<tr style="display: none;">
								<td class="hd1" width="15%" align="right"><th:block
										th:text="#{'l120s01m.item17'}">金控法45條所稱與金控有利害關係人</th:block>&nbsp;&nbsp;</td>
								<td width="35%"><label><input id="mhRlt45"
										name="mhRlt45" type="radio" value='1' />
									<th:block th:text="#{'rdo.have'}">有</th:block></label> <label><input
										name="mhRlt45" type="radio" value='2' />
									<th:block th:text="#{'rdo.nohave'}">無</th:block></label> <label><input
										name="mhRlt45" type="radio" value='3' />
									<th:block th:text="#{'rdo.notApplicable'}">不適用</th:block></label></td>
							</tr>
						</table>
					</div>
					<div>
						<table class="tb2" width="100%">
							<tr style="border: 0px hidden; border-width: 0px;">
								<td style="border: 0px;" align="left" colspan="6"><th:block
										th:text="#{'l120s01m.item18'}">說明1：「佔淨值％」無條件進位至小數第二位</th:block></td>
								<td style="border: 0px;" align="right" colspan="2"><th:block
										th:text="#{'l120s01m.item1'}">(單位:TWD 仟元)</th:block></td>
							</tr>
							<tr style="border: 0px hidden; border-width: 0px;">
								<td style="border: 0px;" align="left" colspan="8"><th:block
										th:text="#{'l120s01m.item19'}">說明2：餘額已扣除依規可不計入同一關係企業第一～四項者之合計</th:block></td>
							</tr>
							<tr style="border: 0px hidden; border-width: 0px;">
								<td style="border: 0px;" align="left" colspan="8"><th:block
										th:text="#{'l120s01m.item27'}">說明3：關係企業資料僅含「有控制與從屬關係」與「相互投資關係」。</th:block></td>
							</tr>
							<tr>
								<td class="hd1" style="text-align: left;" colspan="5"
									width="70%"><th:block th:text="#{'l120s01m.item20'}">※「銀行法第33條之3授權規定事項辦法」之遵循</th:block></td>
								<td class="hd1" colspan="3" align="right" width="30%"><th:block
										th:text="#{'l120s01m.item21'}">>資料日期</th:block>： <input
									type="text" id="dataDate_010" class="date" name="dataDate_010"
									size="13" /></td>
							</tr>

							<tr>
								<td class="hd2" width="20%"><span class="hd2"></span></td>
								<td class="hd2" width="10%"><center>
										<th:block th:text="#{'l120s01m.item22'}">授信總餘額</th:block>
									</center></td>
								<td class="hd2" width="10%"><center>
										<th:block th:text="#{'l120s01m.item23'}">佔淨值</th:block>
										％
									</center></td>
								<td class="hd2" width="10%"><center>
										<th:block th:text="#{'l120s01m.item24'}">法定限額比率</th:block>
										％
									</center></td>
								<td class="hd2" width="10%"><center>
										<th:block th:text="#{'l120s01m.item25'}">無擔保授信餘額</th:block>
									</center></td>
								<td class="hd2" width="10%"><center>
										<th:block th:text="#{'l120s01m.item23'}">佔淨值</th:block>
										％
									</center></td>
								<td class="hd2" width="10%"><center>
										<th:block th:text="#{'l120s01m.item24'}">法定限額比率</th:block>
										％
									</center></td>
								<td class="hd2" width="20%"><center>
										<th:block th:text="#{'l120s01m.item28'}">備註</th:block>
									</center></td>
							</tr>
							<tr>
								<td><th:block th:text="#{'l120s01m.item29'}">同一人</th:block></td>
								<td><input type="text" class="color-red numeric"
									id="totalBal_010" name="totalBal_010" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="shareOfNet_010" name="shareOfNet_010" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="lawLimit_010" name="lawLimit_010" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="totalBal_011" name="totalBal_011" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="shareOfNet_011" name="shareOfNet_011" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="lawLimit_011" name="lawLimit_011" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td>
									<button type="button" class="noHideBt"
										onclick="showDetail('010')">
										<span class="text-only"><th:block
												th:text="#{'l120s01m.item30'}">調閱明細</th:block></span>
									</button>
								</td>
							</tr>
							<tr id="data_030" style="display: none;">
								<td><th:block th:text="#{'l120s01m.item31'}">同一關係企業</th:block></td>
								<td><input type="text" class="color-red numeric"
									id="totalBal_030" name="totalBal_030" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="shareOfNet_030" name="shareOfNet_030" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="lawLimit_030" name="lawLimit_030" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="totalBal_031" name="totalBal_031" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="shareOfNet_031" name="shareOfNet_031" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="lawLimit_031" name="lawLimit_031" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td>
									<button type="button" class="noHideBt"
										onclick="showDetail('030')">
										<span class="text-only"><th:block
												th:text="#{'l120s01m.item30'}">調閱明細</th:block></span>
									</button>
								</td>
							</tr>
							<tr id="data_020" style="display: none;">
								<td><th:block th:text="#{'l120s01m.item32'}">同一關係人－自然人</th:block></td>
								<td><input type="text" class="color-red numeric"
									id="totalBal_020" name="totalBal_020" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="shareOfNet_020" name="shareOfNet_020" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="lawLimit_020" name="lawLimit_020" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="totalBal_021" name="totalBal_021" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="shareOfNet_021" name="shareOfNet_021" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="lawLimit_021" name="lawLimit_021" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td>
									<button type="button" class="noHideBt"
										onclick="showDetail('020')">
										<span class="text-only"><th:block
												th:text="#{'l120s01m.item30'}">調閱明細</th:block></span>
									</button>
								</td>
							</tr>
							<tr id="data_080" style="display: none;">
								<td><th:block th:text="#{'l120s01m.item33'}">同一關係人－法人及自然人</th:block></td>
								<td><input type="text" class="color-red numeric"
									id="totalBal_080" name="totalBal_080" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="shareOfNet_080" name="shareOfNet_080" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="lawLimit_080" name="lawLimit_080" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="totalBal_081" name="totalBal_081" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="shareOfNet_081" name="shareOfNet_081" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td><input type="text" class="color-red numeric"
									id="lawLimit_081" name="lawLimit_081" positiveonly="true"
									integer="15" fraction="2" size="10" /></td>
								<td>
									<button type="button" class="noHideBt"
										onclick="showDetail('080')">
										<span class="text-only"><th:block
												th:text="#{'l120s01m.item30'}">調閱明細</th:block></span>
									</button>
								</td>
							</tr>
						</table>
					</div>
					<div>
						<table class="tb2" width="100%">
							<tr>
								<td class="hd1" style="text-align: left;" colspan="3"><th:block
										th:text="#{'l120s01m.item34'}">※本行「信用風險集中度彙總管理準則」之遵循</th:block></td>
								<td class="hd1" colspan="2" align="right"><th:block
										th:text="#{'l120s01m.item21'}">資料日期</th:block>： <input
									type="text" id="dataDate_040" class="date" name="dataDate_040"
									size="13" /></td>
							</tr>
							<tr>
								<td class="hd2" width="12%"><span class="hd2"></span></td>
								<td class="hd2" width="11%"><center>
										<th:block th:text="#{'l120s01m.item35'}">總暴險</th:block>
									</center></td>
								<td class="hd2" width="11%"><center>
										<th:block th:text="#{'l120s01m.item23'}">佔淨值</th:block>
										％
									</center></td>
								<td class="hd2" width="11%"><center>
										<th:block th:text="#{'l120s01m.item24'}">法定限額比率</th:block>
										％
									</center></td>
								<td class="hd2" width="20%"><center>
										<th:block th:text="#{'l120s01m.item28'}">備註</th:block>
									</center></td>

							</tr>
							<tr>
								<td><th:block th:text="#{'l120s01m.item29'}">同一人</th:block></td>
								<td><input type="text" class="color-red numeric"
									id="totalBal_040" name="totalBal_040" positiveonly="true"
									integer="15" fraction="2" size="13" /></td>
								<td><input type="text" class="color-red numeric"
									id="shareOfNet_040" name="shareOfNet_040" positiveonly="true"
									integer="15" fraction="2" size="13" /></td>
								<td><input type="text" class="color-red numeric"
									id="lawLimit_040" name="lawLimit_040" positiveonly="true"
									integer="15" fraction="2" size="13" /></td>
								<td>
									<button type="button" class="noHideBt"
										onclick="showDetail('040')">
										<span class="text-only"><th:block
												th:text="#{'l120s01m.item30'}">調閱明細</th:block></span>
									</button>
								</td>
							</tr>
							<tr>
								<!--J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。-->
								<!--<td><th:block th:text="#{'l120s01m.item37'}">集團企業</th:block></td>-->
								<td><span class="field" id="showGroupKind"></span></td>
								<td><input type="text" class="color-red numeric"
									id="totalBal_050" name="totalBal_050" positiveonly="true"
									integer="15" fraction="2" size="13" /></td>
								<td><input type="text" class="color-red numeric"
									id="shareOfNet_050" name="shareOfNet_050" positiveonly="true"
									integer="15" fraction="2" size="13" /></td>
								<td><input type="text" class="color-red numeric"
									id="lawLimit_050" name="lawLimit_050" positiveonly="true"
									integer="15" fraction="2" size="13" /></td>
								<td>
									<button type="button" class="noHideBt"
										onclick="showDetail('050')">
										<span class="text-only"><th:block
												th:text="#{'l120s01m.item30'}">調閱明細</th:block></span>
									</button>
								</td>
							</tr>
						</table>
					</div>
					<div>
						<table class="tb2" width="100%">
							<tr>
								<td class="hd1" style="text-align: left;" colspan="2"><th:block
										th:text="#{'l120s01m.item38'}">※本行「利害關係人法定授信限額比率」之遵循</th:block></td>
								<td class="hd1" colspan="2" align="right"><th:block
										th:text="#{'l120s01m.item21'}">資料日期</th:block>： <input
									type="text" id="dataDate_060" class="date" name="dataDate_060"
									size="13" /> <input type="text" id="dataDate_070"
									class="date" name="dataDate_070" size="13" /></td>
							</tr>
							<tr>
								<td class="hd2" width="12%"><span class="hd2"></span></td>
								<td class="hd2" width="11%"><center>
										<th:block th:text="#{'l120s01m.item39'}">擔保授信餘額</th:block>
									</center></td>
								<td class="hd2" width="11%"><center>
										<th:block th:text="#{'l120s01m.item23'}">佔淨值</th:block>
										％
									</center></td>
								<td class="hd2" width="11%"><center>
										<th:block th:text="#{'l120s01m.item24'}">法定限額比率</th:block>
										％
									</center></td>
							</tr>
							<tr>
								<td><th:block th:text="#{'l120s01m.item40'}">銀行法</th:block></td>
								<td><input type="text" class="color-red numeric"
									id="totalBal_060" name="totalBal_060" positiveonly="true"
									integer="15" fraction="2" size="13" /></td>
								<td><input type="text" class="color-red numeric"
									id="shareOfNet_060" name="shareOfNet_060" positiveonly="true"
									integer="15" fraction="2" size="13" /></td>
								<td><input type="text" class="color-red numeric"
									id="lawLimit_060" name="lawLimit_060" positiveonly="true"
									integer="15" fraction="2" size="13" /></td>
							</tr>
							<tr>
								<td><th:block th:text="#{'l120s01m.item41'}">金控法第44條</th:block></td>
								<td><input type="text" class="color-red numeric"
									id="totalBal_070" name="totalBal_070" positiveonly="true"
									integer="15" fraction="2" size="13" /></td>
								<td><input type="text" class="color-red numeric"
									id="shareOfNet_070" name="shareOfNet_070" positiveonly="true"
									integer="15" fraction="2" size="13" /></td>
								<td><input type="text" class="color-red numeric"
									id="lawLimit_070" name="lawLimit_070" positiveonly="true"
									integer="15" fraction="2" size="13" /></td>
							</tr>
						</table>
					</div>

				</form>
			</div>

			<!--信用風險管理遵循Credit risk follow明細調閱-->
			<div id="tL120s01o" style="display: none;">
				<form id="formL120s01o">
					<div id="showDetailHtml"></div>
				</form>
			</div>
			<!--RPA地政士-->
			<div id="rpaTypeBox" class="content" style="display: none;">
				<form id="rpaTypeForm">
					<table width="100%" class="tb2">
						<tr>
							<td class="hd2" nowrap>地政士姓名</td>
							<td colspan="3"><input type="text" id="queryLaaName"
								name="queryLaaName" class="" maxlength="40" size="12" /></td>
						</tr>
					</table>
				</form>
			</div>
			<div id="RpaResultDetailBox" style="display: none">
				<table>
					<tr>
						<td>
							<div id="RpaGridDetail" open="true"
								style="margin-left: 0px; margin-right: 0px"></div>
						</td>
					</tr>
				</table>
			</div>
			<div id="RpaMultResultBox" style="display: none">
				<table width="100%" class="tb2" id="tbRpaMultResult">
				</table>
			</div>
			<div id="laaDetail" class="content" style="display: none">
				<div id="C101S01YDiv">
					<form id="C101S01YForm">
						<input type="text" id="C101S01YOid" style="display: none;" />
						<table class="tb2">
							<tr>
								<td class="hd2" nowrap><th:block
										th:text="#{'C101S01E.laaName'}">地政士姓名</th:block></td>
								<td><input type="text" id="laaName" name="laaName"
									class="required" maxlength="40" size="12" /></td>
							</tr>
							<tr>
								<td class="hd2" nowrap><th:block
										th:text="#{'C101S01E.label.laaCert'}">地政士證書字號</th:block></td>
								<td nowrap>(<input type="text" id="laaYear" name="laaYear"
									class="digits" maxlength="3" size="1" />)&nbsp; <input
									type="text" id="laaWord" name="laaWord" class="" maxlength="4"
									size="7" />
								<th:block th:text="#{'C101S01E.label.laaWordPost'}">字第</th:block>
									&nbsp; <input type="text" id="laaNo" name="laaNo"
									class="digits" maxlength="6" size="3" />
								<th:block th:text="#{'C101S01E.label.laaNoPost'}">號</th:block>
									&nbsp;
								</td>
							</tr>
							<tr>
								<td class="hd2" nowrap><th:block
										th:text="#{'C101S01E.laaOfficeId'}">地政士事務所統編</th:block></td>
								<td><input type="text" id="laaOfficeId" name="laaOfficeId"
									class="" maxlength="10" size="12" /></td>
							</tr>
							<tr>
								<td class="hd2" nowrap><th:block
										th:text="#{'C101S01E.laaOffice'}">地政士事務所名稱</th:block></td>
								<td><input type="text" id="laaOffice" name="laaOffice"
									class="" maxlengthC="50" size="30" /></td>
							</tr>
							<tr>
								<td class="hd2" nowrap><th:block
										th:text="#{'C101S01E.laaDesc'}">受警示地政士理由說明</th:block></td>
								<td><input type="text" id="laaDesc" name="laaDesc" class=""
									maxlengthC="300" size="30" /></td>
							</tr>
						</table>
					</form>
				</div>
			</div>
		</div>
	</th:block>
</body>
</html>
