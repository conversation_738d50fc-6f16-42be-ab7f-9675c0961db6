
package com.mega.eloan.lms.fms.panels;

import java.util.ArrayList;
import java.util.List;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.service.LMS8200Service;
import com.mega.eloan.lms.model.L820M01S;

/**
 * <pre>
 * 個金徵信作業 - 行內_身分證驗證HTML
 * </pre>
 * 
 * @since 2020/05/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/05/25,09763,new
 *          </ul>
 */
public class LMS820M01S5Panel extends Panel {

	@Autowired
	LMSService lmsService;
	
	@Autowired
	LMS8200Service lms8200Service;

	@Autowired
	CodeTypeService codeTypeService;

	private static final long serialVersionUID = 1L;

	/**
	 * @param id
	 */
	public LMS820M01S5Panel(String id, PageParameters params) {
		super(id);
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		JSONObject json = new JSONObject();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");
			
		// 行內_身分證驗證
		List<L820M01S> type1List = lms8200Service.findL820M01S_byIdDupDataType(mainId, custId, dupNo, "5");
		byte[] byteArr = type1List.get(0).getReportFile();
		String str = new String(byteArr);
		json = JSONObject.fromObject(str);
		
		// UPGRADE: 前端須配合改為 <span th:utext......>（類似於 setEscapeModelStrings(false)，不轉義 HTML，直接解析）
		// setLabel("queryDateTime", json.optString("queryDateTime"));
		// setLabel("id", json.optString("id"));
		// setLabel("issuingDate", json.optString("issuingDate"));
		// setLabel("issuingPlace", json.optString("issuingPlace"));
		// setLabel("applyCodeName", json.optString("applyCodeName"));
		// setLabel("resultMsg", json.optString("resultMsg"));
		model.addAttribute("queryDateTime", json.optString("queryDateTime"));
		model.addAttribute("id", json.optString("id"));
		model.addAttribute("issuingDate", json.optString("issuingDate"));
		model.addAttribute("issuingPlace", json.optString("issuingPlace"));
		model.addAttribute("applyCodeName", json.optString("applyCodeName"));
		model.addAttribute("resultMsg", json.optString("resultMsg"));

		 //是否在頁面上加入列印換頁DIV
		boolean addPageBreak = params.getAsBoolean("addPageBreak", false);
		model.addAttribute("addPageBreak", addPageBreak);
		
		boolean isShowWarnMessage = params.getAsBoolean("isShowWarnMessage", "1".equals(json.optString("checkIdCardApply")) ? false : true);
		model.addAttribute("isShowWarnMessage", isShowWarnMessage);
		
	}

	// UPGRADE: 承上，前端須配合改為 <span th:utext......>（類似於 setEscapeModelStrings(false)，不轉義 HTML，直接解析）
//	private void setLabel(String id, String text, ) {
//		Label label = new Label(id, text);
//		label.setEscapeModelStrings(false);
//		add(label);
//		
//		model.addAttribute("C101S01E_laaWord_url", sbLaa.toString());
//	}

	private List<JSONObject> convertJSONArrayToList(JSONArray array) {
		List<JSONObject> list = new ArrayList<JSONObject>();
		for (int i = 0; i < array.size(); i++) {
			list.add(array.getJSONObject(i));
		}
		return list;
	}
}
