package com.mega.eloan.lms.rpt.service;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import jxl.write.WriteException;
import tw.com.iisi.cap.exception.CapException;

import com.mega.eloan.lms.model.L180R19H;
import com.mega.eloan.lms.model.LELF412A;
import com.mega.eloan.lms.model.LELF412B;
import com.mega.eloan.lms.model.LELF412C;
import com.mega.eloan.lms.model.LMSBATCH;

public interface LMS9511R01XLSService {

	/**
	 * <pre>
	 * 產生XLS:授信契約產生主辦聯貸案一覽表
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS161T02Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * <pre>
	 * 產生XLS:授信案件統計表
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R11Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * <pre>
	 * 產生XLS:「金融機構辦理振興經濟非中小企業專案貸款」案件統計表
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R12Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * <pre>
	 * 產生XLS:「企業自行申請展延案件」案件統計表
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R13Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 
	 * J-108-0192_05097_B1001 Web e-Loan企金授信新增每季海外營業單位授信報案考核彙總表
	 * 
	 * <pre>
	 * 產生XLS:營業單位授信報案考核彙總表
	 * @param mainId
	 * @param rptType M:月報  Q:季報
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R14Report(String mainId, String rptType)
			throws CapException, IOException, Exception;

	/**
	 * <pre>
	 * 產生XLS:分行授信淨增加額度統計表
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R18Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * <pre>
	 * 產生XLS:振興經濟非中小企業專案貸款暨信用保證要點執行情形調查表
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R26Report(String mainId) throws CapException,
			IOException, Exception;

	String generateCLS180R12Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R13Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R14AReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R14BReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R14CReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R14DReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R15Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R15BReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R15CReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R15DReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R15EReport(LMSBATCH batch) throws CapException,
			IOException, Exception;
	
	/**
	 * J-112-0465 新增「○○區營運中心覆審類別「額度一千萬元以下十足擔保信保七成循環動用」抽樣之授信戶明細表
	 * 
	 * @param batch
	 * @paran tmpReMark
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateCLS180R15FReport(LMSBATCH batch, String tmpReMark)
			throws CapException, IOException, Exception;

	String generateCLS180R16Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R18Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R18BReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R19Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R20Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R23Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R25Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateLMS180R19Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateLMS180R20Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateLMS180R21Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateLMS180R22Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateLMS180R24Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	/**
	 * 產生XLS:授信業務異常通報月報
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R25Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 產生XLS:授信異常通報案件報送統計表
	 * 
	 * @param mainId
	 * @param includeSmallBuss
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R27Report(String mainId, boolean includeSmallBuss)
			throws CapException, IOException, Exception;

	String generateLMS180R28Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	/**
	 * J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R29Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-105-0214-001 Web e-Loan 管理報表新增授信簽案已核准未能簽約撥貸原因表。
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R30Report(String mainId,
			Map<String, String> brInfoMap) throws CapException, IOException,
			Exception;

	/**
	 * J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R31Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-105-0321-001 Web e-Loan授信管理系統增加營運中心轄下分行往來客戶有全行通報異常情形彙總表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R32Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R33Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 * 
	 * 常董會權限 逾期未覆審名單
	 * 
	 * @param batch
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R19BReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	/**
	 * J-107-0152_05097_B1001 產生金控總部分行是否有逾期尚未辦理覆審之名單。
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R34Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-107-0169_05097_B1001
	 * 產生國內、外各分行及子行尚未完成屬董事會（或常董會）權限核定之企金戶實地覆審名單，內容包括分行代號與名稱、應實地覆審借戶名稱與ID及最遲應覆審日。
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R35Report(String mainId) throws CapException,
			IOException, Exception;

	// J-107-0196_05097_B1001 Web
	// e-Loan企金授信系統管理報表新增以各營運中心之所載聯貸案企金案件，並按期產製報表，以供後續追蹤聯貸案件進度及收益。
	public String generateLMS180R36Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-107-0224_05097_B1001 Web e-Loan企金處新增企金授信案件敘做情形及比較表 LMS180R37
	 */
	public String generateLMS180R37Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 企金已核准授信額度辦理狀態通報彙總表 LMS180R38
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R38Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 * 
	 * 一般/土建融覆審用
	 * 
	 * @param brNo
	 * @param baseDate
	 * @param colCnt
	 * @return
	 */
	public List<String[]> gfnGenerateCTL_FLMS180R01(String brNo, Date baseDate,
			int colCnt, boolean isWriteL180R19, String l180r19hMainId,
			Date l180r19hDataDate);

	/**
	 * J-107-0342_05097_B1001 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @return
	 */
	public Map<String, Integer> getHeaderMap_flms180R01();

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 * 
	 * 常董會權限 逾期未覆審名單
	 * 
	 * @param brNo
	 * @param baseDate
	 * @param colCnt
	 * @return
	 */
	public List<String[]> gfnGenerateCTL_FLMS180R01B(String brNo,
			Date baseDate, int colCnt, boolean isWriteL180R19,
			String l180r19hMainId, Date l180r19hDataDate);

	/**
	 * J-107-0342_05097_B1001 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @return
	 */
	public Map<String, Integer> getHeaderMap_flms180R01B();

	/**
	 * J-107-0249 配合投資處產出共同行銷客戶名單 LMS180R39
	 */
	public String generateLMS180R39Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-107-0234 簽報階段都更危老業務統計表 LMS180R40
	 */
	public String generateLMS180R40Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-107-0342_05097_B1002 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param l180r19h
	 */
	public void saveL180r19h(L180R19H l180r19h);

	/**
	 * J-107-0342_05097_B1002 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param l180r19h
	 */
	public void saveLelf412b(LELF412B lelf412b);

	/**
	 * J-107-0342_05097_B1002 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param l180r19h
	 */
	public void saveLelf412a(LELF412A lelf412a);

	/**
	 * J-107-0357_05097_B1001 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
	 * 
	 * 協助工業區及產業園區建廠優惠貸款專案執行情形統計月報表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R41Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 * 
	 * @param l180r19hMainId
	 * @param l180r19hDataDate
	 * @param docType
	 * @param ctlType
	 * @param brNo
	 */
	public void saveLelf412c(LELF412C lelf412c);

	/**
	 * J-107-0396_09301_B1001 Web e-Loan授信管理系統新增海外分行過去半年內董事會（或常董會）權限核定之企業戶授信案件名單
	 * LMS9515R10
	 */
	public String generateLMS9515R10Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 新核准往來客戶及新增放款額度統計表
	 * 
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R42Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 新核准往來客戶明細表
	 * 
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R42TReport(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 國內分行每季新做無擔保中小企業戶授信額度明細表 LMS180R43
	 * 
	 * M-108-0066_05097_B1001 Web e-Loan企金授信因應稽核處風險導向內部稽核制度填報風險監控指標需要新增企金處報表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R43Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 國內分行每季新作副總權限以上授信額度累計金額 LMS180R44
	 * 
	 * M-108-0066_05097_B1001 Web e-Loan企金授信因應稽核處風險導向內部稽核制度填報風險監控指標需要新增企金處報表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R44Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * LMS180R45 J-108-0107_05097_B1001 國內分行新核准往來企金客戶數統計表(按分行列表)
	 * 
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R45Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-108-0116 共同行銷擔保品投保未結案明細表 LMS180R46
	 */
	public String generateLMS180R46Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-108-0166 企業社會責任貸放情形統計表 LMS180R47
	 */
	public String generateLMS180R47Report(String mainId) throws CapException,
			IOException, Exception;

	public String generateCLS180R30Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	/**
	 * 
	 * J-107-0342_05097_B1003 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * <pre>
	 * 產生XLS:對區域營運中心授信覆審作業之管理績效考核表
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R48Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 
	 * J-108-0272_10702_B1001 Web e-Loan 調整報表邏輯及彙總欄位
	 * 
	 * <pre>
	 * 產生XLS:對區域營運中心授信覆審作業之管理績效考核表
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R49Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-108-0304 投資台灣三大方案專案貸款執行情形統計表 LMS180R50
	 */
	public String generateLMS180R50Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-109-0025 愛企貸專案統計表 LMS180R51
	 */
	public String generateLMS180R51Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-109-0077_05097_B1001 產生因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R52Report(String mainId) throws CapException,
			IOException, Exception;

	public String generateCLS180R40Report(String mainId) throws WriteException,
			IOException;

	/**
	 * 產生XLS:法令遵循自評授信案件明細報表
	 * 
	 * J-109-0132_05097_B1001 e-Loan授信系統新增「法令遵循自評檢核表」之抽測筆數所需之各檢核項目授信案件明細報表。
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R53Report(String mainId) throws CapException,
			IOException, Exception;

	public String generateCLS180R41Report(String mainId) throws WriteException,
			IOException;

	public String generateCLS180R50ForBatchMortgageReport(String mainId)
			throws WriteException, IOException;

	/**
	 * 產生XLS:特定金錢信託案件量統計報表 J-110-0142_10172_B1001
	 * 新增「以本行擔任受託人之特定金錢信託受益權設質擔保授信」案件量統計報表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateCLS180R42Report(String mainId, String brNo)
			throws WriteException, IOException;

	/**
	 * 兆元振興融資方案辦理情形統計表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R54Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 兆元振興融資方案明細表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R54TReport(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 兆元振興融資方案辦理情形預估報表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R55Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 兆元振興融資方案分行核准情形總表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R56Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 建案完成未出售房屋融資統計表
	 * 
	 * J-109-0226 建案餘屋及貸款資料控制管理
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS1401R03ReportForUnsoldHouseFinancingDataReport(
			String mainId) throws CapException, IOException, Exception;

	/**
	 * LMS180R58 小規模營業人授信異常通報表
	 * 
	 * J-109-0315_05097_B1001 Web e-loan企金授信新增小規模營業人央行C方案授信案件之異常通報上一個月獲核定之異動名單
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R58Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-109-0362 青年創業及啟動金貸款辦理情形總表 LMS180R59
	 */
	public String generateLMS180R59Report(String mainId) throws CapException,
			IOException, Exception;

	public String generateCLS180R51ForBatchMortgageStatisticsReport(
			String mainId) throws CapException, IOException, Exception;

	/**
	 * J-109-0519_05097_B1001 Web e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	public String generateLMS180R60Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-110-0018_05097_B1001 Web
	 * e-Loan簽報書額度明細表中增列「兆豐百億挺你專案」及「協助農地工廠合法化融資貸款」兩項專案，並產生統計報表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R61Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-110-0038_05097_B1001 Web e-Loan企金額度明細表新增「本案是否屬110年行銷名單來源客戶」並產生統計報表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R62Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-110-0049_05097_B1001 Web e-Loan企金授信增加「境內法人於國際金融業務分行辦理外幣授信業務報表」
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R63Report(String mainId) throws CapException,
			IOException, Exception;

	// J-109-0513_10702_B1001 匯出全行地政士黑名單
	public String generateCLS180R52ForBatchMortgageReport(String mainId)
			throws CapException, IOException, Exception;

	/**
	 * J-109-0479_05097_B1004 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R65Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-109-0102_05097_B1001 Web e-Loan 配合業管處爭覽管理委員會存款，提供e-Loan土建融案資料
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R66Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-110-0211_11557_B1002 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateCLS180R53Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 國內營業單位海外信保基金基金案件表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R67Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * 企/消金授信簽報案件經區域營運中心流程進度控管表
	 * 
	 * @param mainId
	 * @param docType
	 * @return
	 */
	String generateLMS180R68Report(String mainId, String docType)
			throws CapException, IOException, Exception;

	String generateCLS180R26Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	/**
	 * 歡喜信貸案件明細表
	 * 
	 * @param LMSBATCH
	 * @return String
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateCLS180R27Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	/**
	 * 歡喜信貸ESG明細表
	 * 
	 * @param LMSBATCH
	 * @return String
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateCLS180R27BReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	/**
	 * 歡喜信貸KYC分案報表
	 * 
	 * @param LMSBATCH
	 * @return String
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateCLS180R27CReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	/**
	 * 歡喜信貸婉拒案件自動發送簡訊失敗顧客清單
	 * 
	 * @param LMSBATCH
	 * @return String
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateCLS180R27DReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	/**
	 * LMS180R69 企金綠色授信暨ESG簽報案件明細表
	 * 
	 * @param mainId
	 * @param docType
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R69Report(String mainId) throws CapException,
			IOException, Exception;

	String generateCLS180R55ForUnsoldHouseTrackReport(String mainId)
			throws CapException, IOException, Exception;

	String generateCLS180R57ForMidCycleAnnualReviewList(String mainId)
			throws CapException, IOException, Exception;

	/**
	 * J-111-0178 Web e-Loan個金授信新增訪談紀錄表及其管理報表
	 * 
	 * CLS180R58 客戶訪談紀錄表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateCLS180R58Report(String mainId) throws CapException,
			IOException, Exception;

	String generateCLS180R70ForPlantLoanTrackingReportOfNotIn722Limitation(
			String mainId) throws CapException, IOException, Exception;

	/**
	 * J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
	 * 
	 * LMS180R72 企金授信核准案件BIS評估表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R72Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-111-0411_05097_B1002 Web e-Loan企金授信新增不動產授信例外管理相關功能
	 * 
	 * LMS180R73 不動產授信例外管理報表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R73Report(String mainId) throws CapException,
			IOException, Exception;

	String generateCLS180R59ForMortgageCaseRecommendedByLandsmanReport(
			String mainId) throws CapException, IOException, Exception;

	// J-112-0200_05097_B1001 Web e-Loan系統新增「中小企業千億振興融資方案」統計報表
	public String generateLMS180R74Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-112-0342 新增產生企金授信簽報案件明細檔
	 */
	public String generateLMS180R75Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-112-0399 Web e-Loan 新增分行承作購置住宅貸款年限40年統計表
	 * 
	 * CLS180R60 分行承作購置住宅貸款年限40年統計表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateCLS180R60Report(String mainId) throws CapException,
			IOException, Exception;
	
	/**
	 * J-113-0009 Web e-Loan 新增房貸案件明細表
	 * 
	 * CLS180R61房貸案件明細表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateCLS180R61Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * LMS180R76 授信案件曾涉及ESG風險因而有條件通過或未核准之情形表
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R76Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-113-0237 配合消金處，於ELOAN端企金業務處新增青創追蹤報表
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
    String generateLMS180R77Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-113-0237 配合消金處，於ELOAN端消金業務處新增青創追蹤報表
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateCLS180R62Report(String mainId) throws CapException,
			IOException, Exception;
}