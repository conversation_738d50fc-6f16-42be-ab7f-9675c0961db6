package com.mega.eloan.lms.fms.handler.form;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.fms.pages.CLS9071V02Page;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;


@Scope("request")
@Controller("cls9071formhandler")
public class CLS9071FormHandler extends AbstractFormHandler {
	//復原TFS J-111-0636_05097_B100X
	@Resource
	CLSService clsService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	MisPTEAMAPPService misPTEAMAPPService;
	
	@DomainAuth(AuthType.Query)
	public IResult loadData(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
				
		Set<String> allowBr = CrsUtil.grp_detail_allow_companyIdDup(user.getUnitNo());
		Map<String, String> src_map = clsService.get_codeTypeWithOrder("cls9071_companyIdDup",
				LocaleContextHolder.getLocale().toString());
		Map<String, String> map = new HashMap<String, String>();
		for(String k: src_map.keySet()){
			if(allowBr.contains(Util.trim(k))){
				map.put(k, src_map.get(k));
			}
		}
		
		result.set("item", new CapAjaxFormResult(map));
		result.set("itemOrder", new ArrayList<String>(allowBr));
		return result;
	}
	
	@DomainAuth(AuthType.Query)
	public IResult loadV02(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
				
		TreeMap<String, String> tm = new TreeMap<String, String>();
		List<IBranch> bankList = new ArrayList<IBranch>();
		
		String logonBr = user.getUnitNo();
		
		String[] headquarters = new String[] { UtilConstants.BankNo.授管處, UtilConstants.BankNo.授信行銷處, UtilConstants.BankNo.消金業務處,
				UtilConstants.BankNo.北一區營運中心, UtilConstants.BankNo.北二區營運中心,
				UtilConstants.BankNo.桃竹苗區營運中心, UtilConstants.BankNo.中區營運中心,
				UtilConstants.BankNo.南區營運中心 };
		
		if (Util.equals(UtilConstants.BankNo.資訊處, logonBr)
				|| Util.equals(UtilConstants.BankNo.授管處, logonBr)
				|| Util.equals(UtilConstants.BankNo.授信行銷處, logonBr)
				|| Util.equals(UtilConstants.BankNo.消金業務處, logonBr)) {
			for (String s : headquarters) {
				_addBankAndSub(bankList, s);
			}
			_addBankAndSub(bankList, UtilConstants.BankNo.國外部);
			_addBankAndSub(bankList, UtilConstants.BankNo.金控總部分行);
			_addBankAndSub(bankList, UtilConstants.BankNo.國金部);
			_addBankAndSub(bankList, UtilConstants.BankNo.私銀處作業組);
		} else {
			// 一般分行, 007, 201, 營運中心
			_addBankAndSub(bankList, logonBr);
		}
		
		for (IBranch b : bankList) {
			tm.put(Util.trim(b.getBrNo()), Util.trim(b.getBrName()));
		}
		
		for (String rk : headquarters) {
			tm.remove(rk);
		}
		result.set("br_addSpace", tm.size()>1?"Y":"N");
		result.set("br_item", new CapAjaxFormResult(tm));
		result.set("br_itemOrder", new ArrayList<String>(tm.keySet()));
		
		String dataYM = Util.trim(StringUtils.substring(TWNDate.toAD(CapDate.addMonth(CrsUtil.get_sysMonth_1st(), -1)), 0, 7));
		result.set("dataYM", dataYM);
		return result;
	}
	
	private void _addBankAndSub(List<IBranch> bankList, String brNo) {
		bankList.addAll(branchService.getBranchOfGroup(brNo));
		bankList.add(branchService.getBranch(brNo));
	}

	@DomainAuth(AuthType.Query)
	public IResult checkPteamappForm(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS9071V02Page.class);
		
		String custId = Util.trim(params.getString("custId"));
		String grpCntrNo = Util.trim(params.getString("grpCntrNo"));
		String rptNo = Util.trim(params.getString("rptNo"));
		String p_dataYM1 = Util.trim(params.getString("p_dataYM1"));
		String c_dataYM1 = Util.trim(params.getString("c_dataYM1"));
		String c_dataYM2 = Util.trim(params.getString("c_dataYM2"));
		
		if(Util.isNotEmpty(custId)){
			List<Map<String, Object>> list = misPTEAMAPPService.getDataByCustId(custId);
			if(list.size()==0){
				throw new CapMessageException(prop.getProperty("pteamapp.custid")+" "+custId+" 未存在於整批貸款檔。", getClass() );
			}
		}
		if(Util.isNotEmpty(grpCntrNo)){
			PTEAMAPP pteamapp = misPTEAMAPPService.getDataByLnf020GrpCntrNo(grpCntrNo);
			
			if(pteamapp==null){
				throw new CapMessageException(prop.getProperty("pteamapp.grpcntrno")+" "+grpCntrNo+" 未存在於整批貸款檔。", getClass() );
				
			}else{
				if(Util.isNotEmpty(custId)){
					if(Util.equals(custId, Util.trim(pteamapp.getCustid()))){
						
					}else{
						throw new CapMessageException(prop.getProperty("pteamapp.grpcntrno")+" "+grpCntrNo+" 之公司統編非 "+custId, getClass() );		
					}
				}	
			}				
		}
		
		if(true){
			if(Util.equals(rptNo, "1")){
				if(!valid_dateYM(p_dataYM1)){
					throw new CapMessageException(p_dataYM1+ "的資料不合理", getClass() );	
				}
				
			}else if(Util.equals(rptNo, "2")){
				if(!valid_dateYM(c_dataYM1)){
					throw new CapMessageException("(起)"+c_dataYM1+ "的資料不合理", getClass() );	
				}
				if(!valid_dateYM(c_dataYM2)){
					throw new CapMessageException("(迄)"+c_dataYM2+ "的資料不合理", getClass() );	
				}
			}else if(Util.equals(rptNo, "3")){
				if(Util.equals("", grpCntrNo)){
					throw new CapMessageException("應輸入"+ prop.getProperty("pteamapp.grpcntrno"), getClass() );	
				}
			}	
		}
		return result;
	}
	private boolean valid_dateYM(String dataYM){
		return Util.equals(dataYM, StringUtils.substring(TWNDate.toAD(CapDate.parseDate(String.valueOf(dataYM+"-01"))), 0, 7));
	}
}
