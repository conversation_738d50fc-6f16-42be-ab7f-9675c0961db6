<html xmlns:th="http://www.thymeleaf.org">
	<body>
     	<div class="funcContainer" id="buttonPanel" >
     		<!-- 按鈕列 -->
	   		<th:block th:each="btnItem : ${BUTTON_PANEL_OPTIONS}" >
	   			 <th:block th:if="${btnItem.type} eq 'buttonFragmentCode'">
	   			 	<th:block th:replace="base/LmsButtonEnum :: ${btnItem.content}"></th:block>
	   			 </th:block>
	   			 <th:block th:if="${btnItem.type} eq 'html'" >
	   			 	<th:block th:utext="${btnItem.content}"></th:block>
	   			 </th:block>
	   		</th:block>
     	</div>
		<div class="clear" style="display:none;" id="iPanel">
			<th:block th:if="${ not #strings.isEmpty(iPanelPageName) }">
				<th:block th:replace="${iPanelPageName} :: ${iPanelFragmentName}"></th:block>
			</th:block>
		</div>
		
		<div class="clear"></div>
		
      	<div id="gridview"></div>
      	<div>
		    <!-- 將 java 程式寫的 javascript 列出 -->
			<th:block th:each="jsAttr : ${PAGE_JS_LIST}" >
				<script type="text/javascript" th:utext="${jsAttr.content}" th:id="${jsAttr.id}">
				</script>
			</th:block>  
      	</div>
      	
        <th:block th:insert="(${hasHtml} ?: true) ? ~{${contentPageName} :: ${contentFragmentName}} : ~{}"></th:block>
        <th:block th:if="!(${hasHtml} ?: false)">
            <script type="text/javascript" th:utext="${loadScript}"></script>
        </th:block>
	</body>
</html>