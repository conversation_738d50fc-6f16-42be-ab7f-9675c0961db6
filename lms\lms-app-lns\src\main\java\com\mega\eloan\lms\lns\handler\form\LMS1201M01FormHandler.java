package com.mega.eloan.lms.lns.handler.form;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.BranchDateTimeFormatter;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.lns.pages.LMS1201S05Page06a;
import com.mega.eloan.lms.lns.pages.LMS1201V01Page;
import com.mega.eloan.lms.lns.panels.LMS1201S05Panel;
import com.mega.eloan.lms.lns.panels.LMSS02BPanel;
import com.mega.eloan.lms.lns.panels.LMSS07APanel;
import com.mega.eloan.lms.model.C140S09A;
import com.mega.eloan.lms.model.C140SDSC;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01E;
import com.mega.eloan.lms.model.L120S01F;
import com.mega.eloan.lms.model.L120S01G;
import com.mega.eloan.lms.model.L120S05A;
import com.mega.eloan.lms.model.L120S05B;
import com.mega.eloan.lms.model.L120S05C;
import com.mega.eloan.lms.model.L120S05D;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書(企金授權外) FormHandler
 * </pre>
 * d
 * @since 2011/8/6
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2012/8/16,贊介 負責人資料改為可編輯欄位，解決輸入負責人姓名空白問題
 *          <li>2011/8/6,Miller Lin,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1201formhandler")
@DomainClass(L120M01A.class)
public class LMS1201M01FormHandler extends LMSM01CFormHandler {

	/**
	 * <pre>
	 * 利用JDBC取資料(集團)
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	public IResult getJdbcData(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch iBranch = branchSrv.getBranch(MegaSSOSecurityContext
				.getUnitNo());
		String cesMainId = params.getString("cesMainId");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String grpSrc = params.getString("grpSrc");

		// J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
		Properties pop1205s06a = MessageBundleScriptCreator
				.getComponentResource(LMS1201S05Page06a.class);

		// 建立相關聯的資料表
		L120S05A l120s05a = service1201.findL120s05aByMainId(mainId);
		if (l120s05a != null) {
			// 若資料已存在就刪除
			service1201.delete(l120s05a);
		}
		// 建立新資料表
		l120s05a = new L120S05A();
		String form06Lms1205S05 = params.getString("LMS1205S05Form06");
		JSONObject jsonLms1205s05Form06 = JSONObject
				.fromObject(form06Lms1205S05);
		if (jsonLms1205s05Form06.isEmpty()) {
			jsonLms1205s05Form06 = new JSONObject();
		}

		jsonLms1205s05Form06.put(EloanConstants.MAIN_ID, mainId);
		jsonLms1205s05Form06.put("creator", user.getUserId());
		jsonLms1205s05Form06.put("createTime", new BranchDateTimeFormatter(
				iBranch).reformat(CapDate.parseToString(CapDate
				.getCurrentTimestamp())));
		jsonLms1205s05Form06.put("updater", user.getUserId());
		jsonLms1205s05Form06.put("updateTime", new BranchDateTimeFormatter(
				iBranch).reformat(CapDate.parseToString(CapDate
				.getCurrentTimestamp())));
		jsonLms1205s05Form06.put("grpSrc", Util.nullToSpace(grpSrc));
		DataParse.toBean(jsonLms1205s05Form06, l120s05a);

		String fromOtherDoc = "N";
		// 這裡實作引進資信簡表等相關來源來取得GroupId...
		if (!Util.isEmpty(cesMainId)) {
			service1201.findGrpId(jsonLms1205s05Form06, cesMainId);
		} else {
			jsonLms1205s05Form06.put("grpNo", "");
			jsonLms1205s05Form06.put("grpName", "");
			// 從相關文件取得集團代碼
			List<L120M01E> list = service1201.findL120m01eByMainId(mainId);
			if (!list.isEmpty()) {
				for (L120M01E l120m01e : list) {
					if ("5".equals(Util.trim(l120m01e.getDocType()))) {
						if (!Util.trim(l120m01e.getDocDscr()).isEmpty()) {
							jsonLms1205s05Form06.put(
									"grpNo",
									Util.trim(l120m01e.getDocDscr()).substring(
											0, 4));
							jsonLms1205s05Form06.put(
									"grpName",
									Util.trim(l120m01e.getDocDscr()).substring(
											5));
							fromOtherDoc = "Y";
						}
					}
				}
			}
			if (Util.isEmpty(jsonLms1205s05Form06.getString("grpNo"))) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSS07APanel.class);
				if (l120s05a != null) {
					// 若資料已存在就刪除
					service1201.delete(l120s05a);
				}
				// 最新集團企業授信往來情形為空，請至"相關文件"引進！
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", pop.getProperty("L1205S07.error11")),
						getClass());
			}
			C140SDSC c140sdsc = service1201.getC140SDSCByMainPidTab(mainId,
					mainId, "91", "gcom_SrcDate");
			if (c140sdsc != null) {
				jsonLms1205s05Form06.put("endDscr",
						Util.trim(c140sdsc.getVal()));
			}

			// 計算金額及餘額
			List<C140S09A> listC140s09a = service1201.getC140S09A(mainId,
					mainId);
			BigDecimal totGabkAmt = new BigDecimal("0");
			BigDecimal totGabkBal = new BigDecimal("0");
			for (C140S09A model : listC140s09a) {
				if (!Util.isEmpty(model.getGAbkAmt())) {
					totGabkAmt = totGabkAmt.add(model.getGAbkAmt());
				}
				if (!Util.isEmpty(model.getGAbkAmt())) {
					totGabkBal = totGabkBal.add(model.getGAbkBal());
				}
			}

			jsonLms1205s05Form06.put("fcltAmt",
					Util.trim(Util.nullToSpace(totGabkAmt.toString())));
			jsonLms1205s05Form06.put("lnAmt",
					Util.trim(Util.nullToSpace(totGabkBal.toString())));
		}
		// 處理八.1之該集團之淨值,營收及八.3:集團評等及八.4 之相關欄位(OK)
		// J-102-0087 集團之淨值,營收改抓徵信報告第九章，故需傳入資信簡表MAINID到gfnDb2GetRptGroupData
		// gfnDb2GetRptGroupData 傳入資信簡表MAINID用
		// JSONArray jr = JSONArray.fromObject(params.get("showBorrowData"));
		// JSONObject sbjson = jr.getJSONObject(0);
		// if (sbjson != null){
		// String caseBrId = sbjson.optString("caseBrId");
		// }
		String caseBrId = MegaSSOSecurityContext.getUnitNo();
		jsonLms1205s05Form06.put("gfnDb2GetRptGroupData_Parm_cesMainId",
				Util.nullToSpace(cesMainId));
		jsonLms1205s05Form06.put("gfnDb2GetRptGroupData_Parm_caseBrID",
				Util.nullToSpace(caseBrId));
		jsonLms1205s05Form06.put("gfnDb2GetRptGroupData_Parm_fromOtherDoc",
				Util.nullToSpace(fromOtherDoc));

		service1201.gfnDb2GetRptGroupData(jsonLms1205s05Form06);
		List<L120S05B> l120s05b = service1201.findL120s05bByMainId(mainId);
		if (l120s05b.isEmpty()) {
			l120s05b = new ArrayList<L120S05B>();
		} else {
			// 刪除L120S05B然後新增
			service1201.deleteListL120s05b(l120s05b);
			l120s05b = new ArrayList<L120S05B>();
		}

		// J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
		l120s05b = service1201.findL120s05b1(jsonLms1205s05Form06);
		l120s05a = service1201.saveAndQueryListL120s05b(l120s05b, l120s05a,
				jsonLms1205s05Form06);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201S05Panel.class);
		CapAjaxFormResult myFormResult = DataParse.toResult(l120s05a);
		if (!l120s05b.isEmpty()) {
			int tIndex = 1;
			if (l120s05b.size() <= 1) {
				tIndex = 0;
			}
			myFormResult.set("lnDate", CapDate.formatDate(l120s05b.get(tIndex)
					.getLnDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpQDate", CapDate.formatDate(l120s05b.get(tIndex)
					.getGpQDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpComDate", CapDate.formatDate(
					l120s05b.get(tIndex).getGpComDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpRiskDate", CapDate.formatDate(
					l120s05b.get(tIndex).getGpRiskDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
		}

		// J-105-0167-001 Web e-Loan
		// 企金授信案件簽報書第八之3增列集團企業(應予注意集團)有關集團評等之「財務警訊項目資訊」。
		myFormResult.set("grpFinAlertStr",
				lmsService.buildGrpFinAlertStr(l120s05a.getGrpFinAlert()));

		if ("Y".equals(l120s05a.getGrpOver())) {
			myFormResult.set("grpOver", pop.getProperty("l120s05.other36"));
		} else {
			myFormResult.set("grpOver", pop.getProperty("l120s05.other35"));
		}

		DecimalFormat df = new DecimalFormat("0.00");

		// J-105-0017-001 Web e-Loan企金授信授權外簽報書第八章增加本行買入集團企業無擔保債券有效額度與餘額。
		String[] titles = new String[] { "totMega", "crdMega", "lntMega",
				"lncMega", "excMega", "sumMega", "othMega", "finMega",
				"rskMega", "lmtMega", "gcrdMega", "bondFactMega", "bondBalMega" };
		for (String title : titles) {
			Object value = l120s05a.get(title);
			if (!Util.isEmpty(value)) {
				myFormResult.set(title, df.format(value));
			}
		}

		// J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
		DecimalFormat dfGrpRate = new DecimalFormat("#0.0000");
		String[] titlesGrpRate = new String[] { "avgTwdRt", "avgUsdRt",
				"avgTwdSRt", "avgUsdSRt", "avgTwdNRt", "avgUsdNRt",
				"maxTwdSRt", "maxUsdSRt", "maxTwdNRt", "maxUsdNRt",
				"minTwdSRt", "minUsdSRt", "minTwdNRt", "minUsdNRt" };
		for (String title : titlesGrpRate) {
			Object value = l120s05a.get(title);
			if (!Util.isEmpty(value)) {
				myFormResult.set(title, dfGrpRate.format(value));
			} else {
				myFormResult.set(title, "");
			}
		}

		// J-107-0087-001 Web
		// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
		boolean newGrpGrade = lmsService.isNewGrpGrade(
				Util.trim(l120s05a.getGrpYear()), true);
		String defultNoGrade = lmsService.getGrpNoGrade(newGrpGrade);

		// J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
		if (!lmsService.isShowGrpRateData(l120s05a)) {
			// 尚無「集團企業代號」者或集團企業註記「財務危機集團企業」者
			myFormResult.set("showGrpRate", "N");
		} else {
			myFormResult.set("showGrpRate", "Y");

			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			if (Util.equals(l120s05a.getGrpGrade(), defultNoGrade)) {
				if (Util.equals(l120s05a.getBadFg(), "0")
						|| Util.equals(l120s05a.getBadFg(), "")) {
					// l120s05a.grpGradeMemo_2=(為新戶或未評等)
					myFormResult.set("grpGradeMemo",
							pop1205s06a.getProperty("l120s05a.grpGradeMemo_2"));
				} else {
					// l120s05a.grpGradeMemo_3=(為新戶或未評等，惟列管應予注意集團企業者）
					myFormResult.set("grpGradeMemo",
							pop1205s06a.getProperty("l120s05a.grpGradeMemo_3"));
				}
			} else {
				if (Util.equals(l120s05a.getBadFg(), "0")
						|| Util.equals(l120s05a.getBadFg(), "")) {
					// l120s05a.grpGradeMemo_2=(為新戶或未評等)
					myFormResult.set("grpGradeMemo", "");
				} else {
					// l120s05a.grpGradeMemo_1=(且列管應予注意集團企業者）
					myFormResult.set("grpGradeMemo",
							pop1205s06a.getProperty("l120s05a.grpGradeMemo_1"));
				}
			}
		}

		myFormResult.set("grpFlag",
				Util.isEmpty(Util.trim(l120s05a.getGrpNo())) ? "N" : "Y");

		// J-105-0159-001 Web e-Loan企金授信授權外簽報書說明八，修改一律顯示本行對該集團授信限額與無擔保授信額度限額。
		// if (!pop.getProperty("l120s05a.no2").equals(l120s05a.getGrpDscr())) {
		// myFormResult.set("_hLmtAmt", true);
		// myFormResult.set("_hGcrdAmt", true);
		// } else {
		// myFormResult.set("_hLmtAmt", false);
		// myFormResult.set("_hGcrdAmt", false);
		// }
		myFormResult.set("_hLmtAmt", false);
		myFormResult.set("_hGcrdAmt", false);

		if ("1".equals(l120s05a.getGrpSrc())) {
			myFormResult.set("grpSrc", pop.getProperty("l120s05.data1"));
		} else if ("2".equals(l120s05a.getGrpSrc())) {
			myFormResult.set("grpSrc", pop.getProperty("l120s05.data2"));
		} else {
			myFormResult.set("grpSrc", "");
		}
		// 民國轉西元年
		myFormResult
				.set("endYear",
						(Util.trim(Util.nullToSpace(l120s05a.getEndYear()))
								.length() == 4) ? l120s05a.getEndYear()
								: ((Util.trim(Util.nullToSpace(l120s05a
										.getEndYear()))).length() == 0) ? null
										: Util.parseInt(l120s05a.getEndYear()) + 1911);
		myFormResult.set("grpGrrd",
				Util.trim(jsonLms1205s05Form06.getString("grpGrrd")));

		// J-107-0087-001 Web
		// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
		if (newGrpGrade) {
			// (大A)、(中B)....
			myFormResult.set(
					"grpSizeLvlShow",
					lmsService.getGrpSizeLvlShow(
							Util.trim(l120s05a.getGrpSize()),
							Util.trim(l120s05a.getGrpLevel())));
		} else {
			myFormResult.set("grpSizeLvlShow", "");
		}

		// if
		// (Util.isChineseChar(Util.trim(jsonLms1205s05Form06.get("grpGrrd"))))
		// {
		// // 控制"級"隱藏條件
		// myFormResult.set("_hGrpGrrd", true);
		// }
		result.set("LMS1205S05Form06", myFormResult);
		if (JSONObject.fromObject(myFormResult.getResult()).isEmpty()) {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		} else {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}
		return result;
	}

	/**
	 * 刪除集團企業資料
	 * 
	 * @param mainId
	 */
	@SuppressWarnings("unused")
	private void delGrp(String mainId) {
		L120S05A l120s05a = service1201.findL120s05aByMainId(mainId);
		List<L120S05B> list = service1201.findL120s05bByMainId(mainId);
		if (l120s05a != null) {
			// 若資料已存在就刪除
			service1201.delete(l120s05a);
		}
		if (!list.isEmpty()) {
			// 刪除L120S05B然後新增
			service1201.deleteListL120s05b(list);
		}
	}

	/**
	 * 刪除關係企業資料
	 * 
	 * @param mainId
	 */
	@SuppressWarnings("unused")
	private void delRlt(String mainId) {
		L120S05C l120s05c = service1201.findL120s05cByMainId(mainId);
		List<L120S05D> list = service1201.findL120s05dByMainId(mainId);
		if (l120s05c != null) {
			// 若資料已存在就刪除
			service1201.delete(l120s05c);
		}
		if (!list.isEmpty()) {
			// 刪除L120S05B然後新增
			service1201.deleteListL120s05d(list);
		}
	}

	/**
	 * 利用JDBC取資料(關係企業)
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	public IResult getJdbcRelData(PageParameters params) throws CapException {
		IBranch iBranch = branchSrv.getBranch(MegaSSOSecurityContext
				.getUnitNo());
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// 建立相關聯的資料表
		L120S05C l120s05c = service1201.findL120s05cByMainId(mainId);
		if (l120s05c == null) {
			// 無資料
			// 建立新資料表
			l120s05c = new L120S05C();
		} else {
			// 刪除L120S05A然後新增
			service1201.delete(l120s05c);
			l120s05c = new L120S05C();
		}
		String formLms1205s0507 = params.getString("LMS1205S05Form07");
		JSONObject jsonFormLms1205s0507 = new JSONObject();
		if (!Util.isEmpty(formLms1205s0507)) {
			jsonFormLms1205s0507 = JSONObject.fromObject(formLms1205s0507);
		}
		jsonFormLms1205s0507.put(EloanConstants.MAIN_ID, mainId);
		jsonFormLms1205s0507.put("creator", user.getUserId());
		jsonFormLms1205s0507.put("createTime", new BranchDateTimeFormatter(
				iBranch).reformat(CapDate.parseToString(CapDate
				.getCurrentTimestamp())));
		DataParse.toBean(jsonFormLms1205s0507, l120s05c);

		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
		jsonFormLms1205s0507.put("custId", l120m01a.getCustId());
		jsonFormLms1205s0507.put("dupNo", l120m01a.getDupNo());
		jsonFormLms1205s0507.put("custName",
				Util.toFullCharString(l120m01a.getCustName()));

		List<L120S05D> l120s05d = service1201.findL120s05dByMainId(mainId);
		if (l120s05d == null) {
			l120s05d = new ArrayList<L120S05D>();
		} else {
			// 刪除L120S05D然後新增
			service1201.deleteListL120s05d(l120s05d);
			l120s05d = new ArrayList<L120S05D>();
		}
		try {
			l120s05d = service1201.findL120s05d1(jsonFormLms1205s0507);
		} catch (Exception e) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0048"), getClass());
		}

		l120s05c = service1201.saveAndQueryListL120s05d(l120s05d, l120s05c,
				jsonFormLms1205s0507);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201S05Panel.class);
		CapAjaxFormResult myFormResult = DataParse.toResult(l120s05c);
		if (!l120s05d.isEmpty()) {
			myFormResult.set("lnDate", CapDate.formatDate(l120s05d.get(0)
					.getLnDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpQDate", CapDate.formatDate(l120s05d.get(0)
					.getGpQDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpComDate", CapDate.formatDate(l120s05d.get(0)
					.getGpComDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpRiskDate", CapDate.formatDate(l120s05d.get(0)
					.getGpRiskDate(), UtilConstants.DateFormat.YYYY_MM_DD));
		}
		if (l120s05c != null) {
			if ("Y".equals(l120s05c.getRltOver())) {
				myFormResult.set("rltOver", pop.getProperty("l120s05.other36"));
			} else {
				myFormResult.set("rltOver", pop.getProperty("l120s05.other35"));
			}
			if (!Util.isEmpty(l120s05c.getTotMega())) {
				if (l120s05c.getTotMega() == 0) {
					myFormResult.set("totMega", "0.00");
				} else {
					myFormResult
							.set("totMega", add2Zero(l120s05c.getTotMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getCrdMega())) {
				if (l120s05c.getCrdMega() == 0) {
					myFormResult.set("crdMega", "0.00");
				} else {
					myFormResult
							.set("crdMega", add2Zero(l120s05c.getCrdMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getLntMega())) {
				if (l120s05c.getLntMega() == 0) {
					myFormResult.set("lntMega", "0.00");
				} else {
					myFormResult
							.set("lntMega", add2Zero(l120s05c.getLntMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getLncMega())) {
				if (l120s05c.getLncMega() == 0) {
					myFormResult.set("lncMega", "0.00");
				} else {
					myFormResult
							.set("lncMega", add2Zero(l120s05c.getLncMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getExcMega())) {
				if (l120s05c.getExcMega() == 0) {
					myFormResult.set("excMega", "0.00");
				} else {
					myFormResult
							.set("excMega", add2Zero(l120s05c.getExcMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getSumMega())) {
				if (l120s05c.getSumMega() == 0) {
					myFormResult.set("sumMega", "0.00");
				} else {
					myFormResult
							.set("sumMega", add2Zero(l120s05c.getSumMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getOthMega())) {
				if (l120s05c.getOthMega() == 0) {
					myFormResult.set("othMega", "0.00");
				} else {
					myFormResult
							.set("othMega", add2Zero(l120s05c.getOthMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getFinMega())) {
				if (l120s05c.getFinMega() == 0) {
					myFormResult.set("finMega", "0.00");
				} else {
					myFormResult
							.set("finMega", add2Zero(l120s05c.getFinMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getRskMega())) {
				if (l120s05c.getRskMega() == 0) {
					myFormResult.set("rskMega", "0.00");
				} else {
					myFormResult
							.set("rskMega", add2Zero(l120s05c.getRskMega()));
				}
			}
		}
		myFormResult.set("rltFlag", "Y");
		result.set("LMS1205S05Form07", myFormResult);
		if (JSONObject.fromObject(myFormResult.getResult()).isEmpty()) {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		} else {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}
		return result;
	}

	/**
	 * 利用JDBC取資料(關係企業) J-106-0110-001 Web
	 * e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	public IResult getJdbcDataRelGroup(PageParameters params) throws CapException {
		IBranch iBranch = branchSrv.getBranch(MegaSSOSecurityContext
				.getUnitNo());
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// 建立相關聯的資料表
		L120S05A l120s05a = service1201.findL120s05aByMainId(mainId);
		if (l120s05a == null) {
			// 無資料
			// 建立新資料表
			l120s05a = new L120S05A();
		} else {
			// 刪除L120S05A然後新增
			service1201.delete(l120s05a);
			l120s05a = new L120S05A();
		}
		String form06Lms1205S05 = params.getString("LMS1205S05Form06");
		JSONObject jsonLms1205s05Form06 = new JSONObject();
		if (!Util.isEmpty(form06Lms1205S05)) {
			jsonLms1205s05Form06 = JSONObject.fromObject(form06Lms1205S05);
		}
		jsonLms1205s05Form06.put(EloanConstants.MAIN_ID, mainId);
		jsonLms1205s05Form06.put("creator", user.getUserId());
		jsonLms1205s05Form06.put("createTime", new BranchDateTimeFormatter(
				iBranch).reformat(CapDate.parseToString(CapDate
				.getCurrentTimestamp())));
		jsonLms1205s05Form06.put("updater", user.getUserId());
		jsonLms1205s05Form06.put("updateTime", new BranchDateTimeFormatter(
				iBranch).reformat(CapDate.parseToString(CapDate
				.getCurrentTimestamp())));
		jsonLms1205s05Form06.put("grpSrc", "");
		DataParse.toBean(jsonLms1205s05Form06, l120s05a);

		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
		jsonLms1205s05Form06.put("custId", l120m01a.getCustId());
		jsonLms1205s05Form06.put("dupNo", l120m01a.getDupNo());
		jsonLms1205s05Form06.put("custName",
				Util.toFullCharString(l120m01a.getCustName()));
		jsonLms1205s05Form06.put("grpNo", "");
		jsonLms1205s05Form06.put("grpName", "");

		boolean hasRelGrp = false;
		List<Map<String, Object>> rows = this.misElcrcoService
				.findElcrecomByIdDupnoWithR01R02R03ExceptSelf(
						l120m01a.getCustId(), l120m01a.getDupNo());
		if (rows != null && !rows.isEmpty()) {
			hasRelGrp = true;
		}

		List<L120S05B> l120s05b = service1201.findL120s05bByMainId(mainId);
		if (l120s05b == null) {
			l120s05b = new ArrayList<L120S05B>();
		} else {
			// 刪除L120S05D然後新增
			service1201.deleteListL120s05b(l120s05b);
			l120s05b = new ArrayList<L120S05B>();
		}
		try {
			l120s05b = service1201.findL120s05b1_A(jsonLms1205s05Form06);
		} catch (Exception e) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0048"), getClass());
		}

		l120s05a = service1201.saveAndQueryListL120s05a_A(l120s05b, l120s05a,
				jsonLms1205s05Form06, hasRelGrp);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201S05Panel.class);
		CapAjaxFormResult myFormResult = DataParse.toResult(l120s05a);
		if (!l120s05b.isEmpty()) {
			myFormResult.set("lnDate", CapDate.formatDate(l120s05b.get(0)
					.getLnDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpQDate", CapDate.formatDate(l120s05b.get(0)
					.getGpQDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpComDate", CapDate.formatDate(l120s05b.get(0)
					.getGpComDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpRiskDate", CapDate.formatDate(l120s05b.get(0)
					.getGpRiskDate(), UtilConstants.DateFormat.YYYY_MM_DD));
		}
		if (l120s05a != null) {
			if ("Y".equals(l120s05a.getGrpOver())) {
				myFormResult.set("grpOver", pop.getProperty("l120s05.other36"));
			} else {
				myFormResult.set("grpOver", pop.getProperty("l120s05.other35"));
			}
			if (!Util.isEmpty(l120s05a.getTotMega())) {
				if (l120s05a.getTotMega() == 0) {
					myFormResult.set("totMega", "0.00");
				} else {
					myFormResult
							.set("totMega", add2Zero(l120s05a.getTotMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getCrdMega())) {
				if (l120s05a.getCrdMega() == 0) {
					myFormResult.set("crdMega", "0.00");
				} else {
					myFormResult
							.set("crdMega", add2Zero(l120s05a.getCrdMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getLntMega())) {
				if (l120s05a.getLntMega() == 0) {
					myFormResult.set("lntMega", "0.00");
				} else {
					myFormResult
							.set("lntMega", add2Zero(l120s05a.getLntMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getLncMega())) {
				if (l120s05a.getLncMega() == 0) {
					myFormResult.set("lncMega", "0.00");
				} else {
					myFormResult
							.set("lncMega", add2Zero(l120s05a.getLncMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getExcMega())) {
				if (l120s05a.getExcMega() == 0) {
					myFormResult.set("excMega", "0.00");
				} else {
					myFormResult
							.set("excMega", add2Zero(l120s05a.getExcMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getSumMega())) {
				if (l120s05a.getSumMega() == 0) {
					myFormResult.set("sumMega", "0.00");
				} else {
					myFormResult
							.set("sumMega", add2Zero(l120s05a.getSumMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getOthMega())) {
				if (l120s05a.getOthMega() == 0) {
					myFormResult.set("othMega", "0.00");
				} else {
					myFormResult
							.set("othMega", add2Zero(l120s05a.getOthMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getFinMega())) {
				if (l120s05a.getFinMega() == 0) {
					myFormResult.set("finMega", "0.00");
				} else {
					myFormResult
							.set("finMega", add2Zero(l120s05a.getFinMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getRskMega())) {
				if (l120s05a.getRskMega() == 0) {
					myFormResult.set("rskMega", "0.00");
				} else {
					myFormResult
							.set("rskMega", add2Zero(l120s05a.getRskMega()));
				}
			}
			// myFormResult.set("grpFlag", "A");
		} else {
			myFormResult.set("grpFlag", "N");
		}

		result.set("LMS1205S05Form06", myFormResult);
		if (JSONObject.fromObject(myFormResult.getResult()).isEmpty()) {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		} else {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}
		return result;
	}

	/**
	 * <pre>
	 * 查詢(主要借款人關係)並引進『四、營運概況』、『五、財務狀況』、『七、存放款及外匯往來情形』。 
	 * @param params PageParameters
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryToGetData(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String thisOid = params.getString(EloanConstants.OID);
		L120S01A l120s01a = service1201.findL120s01aByOid(thisOid);
		L120M01A l120m01a = service1201.findL120m01aByMainId(l120s01a
				.getMainId());
		L120S01B l120s01b = service1201
				.findL120s01bByUniqueKey(l120s01a.getMainId(),
						l120s01a.getCustId(), l120s01a.getDupNo());
		// 設定營運概況借款人統編
		l120m01a.setCesCustId(l120s01a.getCustId());
		// 設定營運概況重覆序號
		l120m01a.setCesDupNo(l120s01a.getDupNo());
		// 『四、營運概況』Query...
		if (!CapString.isEmpty(thisOid)) {
			L120S01G l120s01g1 = service1201.findL120s01gByUniqueKey(
					l120s01a.getMainId(), l120s01a.getCustId(),
					l120s01a.getDupNo(), "1");

			CapAjaxFormResult resultL120s01g1 = new CapAjaxFormResult();
			CapAjaxFormResult formIdDscr1 = new CapAjaxFormResult();
			if (l120s01g1 == null) {
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
			}
			// 營運獲利情形

			resultL120s01g1.set("runFlag", Util.trim(l120s01b.getRunFlag()));
			resultL120s01g1.set("runCurr", Util.trim(l120s01b.getRunCurr()));
			resultL120s01g1.set("runUnit", Util.trim(l120s01b.getRunUnit()));

			String gaapFlag = CapString.trimNull(l120s01b.getGaapFlag());
			String tradeType = CapString.trimNull(l120s01b.getTradeType());
			gaapFlag = "".equals(gaapFlag) ? " " : gaapFlag;
			tradeType = "".equals(tradeType) ? " " : tradeType;

			JSONObject l120s01eKind1Data = service1201.getL120s01eKind1Data(
					l120s01a.getMainId(), l120s01a.getCustId(),
					l120s01a.getDupNo(), gaapFlag.charAt(0),
					tradeType.charAt(0));
			resultL120s01g1.putAll(new CapAjaxFormResult(l120s01eKind1Data));

			if (l120s01g1 != null) {
				formIdDscr1.set("idDscr1",
						Util.nullToSpace(l120s01g1.getDataDscr()));
				resultL120s01g1.set("cesTypCd",
						getMessage("typCd." + Util.trim(l120s01a.getTypCd())));
				// resultL120s01g1.set("cesTypCd",
				// TypCdEnum.getEnum(Util.trim(l120s01a.getTypCd()))
				// .name());
				resultL120s01g1.set("cesCustId",
						Util.trim(l120m01a.getCesCustId()));
				resultL120s01g1.set("cesDupNo",
						Util.trim(l120m01a.getCesDupNo()));
				resultL120s01g1.set("cesCustName",
						Util.trim(l120s01a.getCustName()));
				// 當有多個model要對應form時.. 依序指定
				result.set("LMS1205S05Form02", resultL120s01g1);
				result.set("formIdDscr1", formIdDscr1);
			}
		}

		// 『五、財務狀況』Query...
		if (!CapString.isEmpty(thisOid)) {
			L120S01G l120s01g2 = service1201.findL120s01gByUniqueKey(
					l120s01a.getMainId(), l120s01a.getCustId(),
					l120s01a.getDupNo(), "2");

			CapAjaxFormResult resultL120s01g2 = new CapAjaxFormResult();
			CapAjaxFormResult formIdDscr2 = new CapAjaxFormResult();
			if (l120s01g2 == null) {
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
			}
			// 主要財務比率
			resultL120s01g2.set("finFlag", Util.trim(l120s01b.getFinFlag()));

			String gaapFlag = CapString.trimNull(l120s01b.getGaapFlag());
			String tradeType = CapString.trimNull(l120s01b.getTradeType());
			gaapFlag = "".equals(gaapFlag) ? " " : gaapFlag;
			tradeType = "".equals(tradeType) ? " " : tradeType;

			JSONObject l120s01eKind2Data = service1201.getL120s01eKind2Data(
					l120s01a.getMainId(), l120s01a.getCustId(),
					l120s01a.getDupNo(), gaapFlag.charAt(0),
					tradeType.charAt(0));
			resultL120s01g2.putAll(new CapAjaxFormResult(l120s01eKind2Data));

			if (l120s01g2 != null) {
				formIdDscr2.set("idDscr2",
						Util.nullToSpace(l120s01g2.getDataDscr()));
				resultL120s01g2.set("cesTypCd",
						getMessage("typCd." + Util.trim(l120s01a.getTypCd())));
				// resultL120s01g2.set("cesTypCd",
				// TypCdEnum.getEnum(Util.trim(l120s01a.getTypCd()))
				// .name());
				resultL120s01g2.set("cesCustId",
						Util.trim(l120m01a.getCesCustId()));
				resultL120s01g2.set("cesDupNo",
						Util.trim(l120m01a.getCesDupNo()));
				resultL120s01g2.set("cesCustName",
						Util.trim(l120s01a.getCustName()));
				// 當有多個model要對應form時.. 依序指定
				result.set("LMS1205S05Form03", resultL120s01g2);
				result.set("formIdDscr2", formIdDscr2);
			}
		}

		// 『七、存放款及外匯往來情形』Query...
		if (!CapString.isEmpty(thisOid)) {
			L120S01F l120s01f = service1201.findL120s01fByUniqueKey(
					l120s01a.getMainId(), l120s01a.getCustId(),
					l120s01a.getDupNo());
			if (l120s01f == null) {
				l120s01f = new L120S01F();
				l120s01f.setMainId(l120s01a.getMainId());
				l120s01f.setCreateTime(CapDate.getCurrentTimestamp());
				l120s01f.setCreator(user.getUserId());
			}
			SimpleDateFormat bartDateFormat = new SimpleDateFormat(
					UtilConstants.DateFormat.YYYY_MM_DD);
			String fxb = checknull(bartDateFormat, l120s01f.getFxBDate());
			String fxe = checknull(bartDateFormat, l120s01f.getFxEDate());
			String imb = checknull(bartDateFormat, l120s01f.getImBDate());
			String ime = checknull(bartDateFormat, l120s01f.getImEDate());
			String exb = checknull(bartDateFormat, l120s01f.getExBDate());
			String exe = checknull(bartDateFormat, l120s01f.getExEDate());
			String cntrb = checknull(bartDateFormat, l120s01f.getCntrBDate());
			String cntre = checknull(bartDateFormat, l120s01f.getCntrEDate());

			CapAjaxFormResult resultL120s01f = DataParse.toResult(l120s01f);

			CodeType codetype1 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("dpAvgUnit"))));
			CodeType codetype2 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("fxUnit"))));
			CodeType codetype3 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("fx2Unit"))));
			CodeType codetype4 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("imUnit"))));
			CodeType codetype5 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("im2Unit"))));
			CodeType codetype6 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("exUnit"))));
			CodeType codetype7 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("ex2Unit"))));
			CodeType codetype8 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("cntrUnit"))));
			CodeType codetype9 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("nonLoanUnit"))));

			if (codetype1 != null) {
				resultL120s01f.set("dpAvgUnit",
						Util.isNumeric(Util.trim(codetype1.getCodeDesc())) ? ""
								: Util.trim(codetype1.getCodeDesc()));
			} else {
				resultL120s01f.set("dpAvgUnit", "");
			}
			if (codetype2 != null) {
				resultL120s01f.set("fxUnit",
						Util.isNumeric(Util.trim(codetype2.getCodeDesc())) ? ""
								: Util.trim(codetype2.getCodeDesc()));
			} else {
				resultL120s01f.set("fxUnit", "");
			}
			if (codetype3 != null) {
				resultL120s01f.set("fx2Unit",
						Util.isNumeric(Util.trim(codetype3.getCodeDesc())) ? ""
								: Util.trim(codetype3.getCodeDesc()));
			} else {
				resultL120s01f.set("fx2Unit", "");
			}
			if (codetype4 != null) {
				resultL120s01f.set("imUnit",
						Util.isNumeric(Util.trim(codetype4.getCodeDesc())) ? ""
								: Util.trim(codetype4.getCodeDesc()));
			} else {
				resultL120s01f.set("imUnit", "");
			}
			if (codetype5 != null) {
				resultL120s01f.set("im2Unit",
						Util.isNumeric(Util.trim(codetype5.getCodeDesc())) ? ""
								: Util.trim(codetype5.getCodeDesc()));
			} else {
				resultL120s01f.set("im2Unit", "");
			}
			if (codetype6 != null) {
				resultL120s01f.set("exUnit",
						Util.isNumeric(Util.trim(codetype6.getCodeDesc())) ? ""
								: Util.trim(codetype6.getCodeDesc()));
			} else {
				resultL120s01f.set("exUnit", "");
			}
			if (codetype7 != null) {
				resultL120s01f.set("ex2Unit",
						Util.isNumeric(Util.trim(codetype7.getCodeDesc())) ? ""
								: Util.trim(codetype7.getCodeDesc()));
			} else {
				resultL120s01f.set("ex2Unit", "");
			}
			if (codetype8 != null) {
				resultL120s01f.set("cntrUnit",
						Util.isNumeric(Util.trim(codetype8.getCodeDesc())) ? ""
								: Util.trim(codetype8.getCodeDesc()));
			} else {
				resultL120s01f.set("cntrUnit", "");
			}
			if (codetype9 != null) {
				resultL120s01f.set("nonLoanUnit",
						Util.isNumeric(Util.trim(codetype9.getCodeDesc())) ? ""
								: Util.trim(codetype9.getCodeDesc()));
			} else {
				resultL120s01f.set("nonLoanUnit", "");
			}

			resultL120s01f.set("fxYear", trimZero(NumConverter
					.delCommaString(Util.nullToSpace(resultL120s01f
							.get("fxYear")))));
			resultL120s01f.set("imYear", trimZero(NumConverter
					.delCommaString(Util.nullToSpace(resultL120s01f
							.get("imYear")))));
			resultL120s01f.set("exYear", trimZero(NumConverter
					.delCommaString(Util.nullToSpace(resultL120s01f
							.get("exYear")))));
			// 舊型用法(不活)
			translate("fxb", fxb, resultL120s01f);
			translate("fxe", fxe, resultL120s01f);
			translate("imb", imb, resultL120s01f);
			translate("ime", ime, resultL120s01f);
			translate("exb", exb, resultL120s01f);
			translate("exe", exe, resultL120s01f);
			translate("cntrb", cntrb, resultL120s01f);
			translate("cntre", cntre, resultL120s01f);

			resultL120s01f.set("cesTypCd",
					getMessage("typCd." + Util.trim(l120s01a.getTypCd())));
			// resultL120s01f.set("cesTypCd",
			// TypCdEnum.getEnum(Util.trim(l120s01a.getTypCd())).name());
			resultL120s01f.set("cesCustId", Util.trim(l120m01a.getCesCustId()));
			resultL120s01f.set("cesDupNo", Util.trim(l120m01a.getCesDupNo()));
			resultL120s01f
					.set("cesCustName", Util.trim(l120s01a.getCustName()));
			// 當有多個model要對應form時.. 依序指定
			result.set("LMS1205S05Form05", resultL120s01f);
		}
		service1201.save(l120m01a);
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}

	/**
	 * 引進徵信資信簡表取得營運概況與財務狀況及存放款外匯往來情形
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getL120s01e(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String cesMainId1 = params.getString("cesMainId1");
		String cesMainId2 = params.getString("cesMainId2");
		String thisOid = params.getString("thisOid");
		String[] finItem = params.getStringArray("finItem");
		String gaapFlag = params.getString("gaapFlag", " ");
		String tradeType = params.getString("fssType", " ");

		Map<String, List<String>> map = new HashMap<String, List<String>>();
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
		L120S01A l120s01a = service1201.findL120s01aByOid(thisOid);
		String custId = l120s01a.getCustId();
		String dupNo = l120s01a.getDupNo();
		L120S01B l120s01b = service1201.findL120s01bByUniqueKey(mainId, custId,
				dupNo);
		if (StringUtils.length(cesMainId1) == 32) {
			l120s01b.setGaapFlag(gaapFlag);
			l120s01b.setTradeType(tradeType);
			service1201.save(l120s01b);
		}
		gaapFlag = l120s01b.getGaapFlag();
		tradeType = l120s01b.getTradeType();

		// 如果已有資料則刪除重新引進
		if (StringUtils.length(cesMainId1) == 32) {
			eloanDbBaseService.L120S01E_delByMainIdCustData(mainId, custId,
					dupNo);
		}

		List<L120S01E> list1 = new ArrayList<L120S01E>();

		List<Map<String, Object>> ofss1Datas = eloanDbBaseService
				.findC120M01A_selCustData3(cesMainId1);

		if (StringUtils.length(cesMainId1) == 32) {
			if (ofss1Datas != null && ofss1Datas.size() > 0) {
				Map<String, Object> ofss1Data = ofss1Datas.get(0);
				String ofss1 = MapUtils.getString(ofss1Data, "ofss1", "1");
				// 0為未勾選，代表不是N.A.
				if ("0".equals(ofss1)) {
					list1 = service1201.findListL120s01e("1",
							Util.nullToSpace(mainId), Util.nullToSpace(custId),
							Util.nullToSpace(dupNo), finItem, map, cesMainId1);
				}
			}
		}

		CapAjaxFormResult resultl120s01g1 = new CapAjaxFormResult();
		CapAjaxFormResult formIdDscr1 = new CapAjaxFormResult();
		if (!list1.isEmpty()) {
		}
		// 設定營運概況分析與評估(徵信報告)
		L120S01G model1 = service1201.findL120s01gByUniqueKey(mainId, custId,
				dupNo, "1");
		if (model1 == null) {
			MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
			model1 = new L120S01G();
			model1.setMainId(mainId);
			model1.setDataType("1");
			model1.setCustId(custId);
			model1.setDupNo(dupNo);
			model1.setCreateTime(CapDate.getCurrentTimestamp());
			model1.setCreator(unit.getUserId());
		}

		if (StringUtils.isNotEmpty(cesMainId2)) {
			model1 = service1201.find120s01g(model1, cesMainId2);
			formIdDscr1.set("idDscr1", model1.getDataDscr());
		}
		// 財務狀況(資信簡表)
		List<L120S01E> list2 = new ArrayList<L120S01E>();

		if (StringUtils.length(cesMainId1) == 32) {
			if (ofss1Datas != null && ofss1Datas.size() > 0) {
				Map<String, Object> ofss1Data = ofss1Datas.get(0);
				String gfss1 = MapUtils.getString(ofss1Data, "gfss1", "1");
				if ("0".equals(gfss1)) {
					list2 = service1201.findListL120s01e("2",
							Util.nullToSpace(mainId), Util.nullToSpace(custId),
							Util.nullToSpace(dupNo), finItem, map, cesMainId1);
				}
			}
		}

		CapAjaxFormResult resultL120s01g_2 = new CapAjaxFormResult();
		CapAjaxFormResult formIdDscr2 = new CapAjaxFormResult();

		// 設定財務狀況分析與評估(徵信報告)
		L120S01G model2 = service1201.findL120s01gByUniqueKey(mainId, custId,
				dupNo, "2");
		if (model2 == null) {
			MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
			model2 = new L120S01G();
			model2.setMainId(mainId);
			model2.setDataType("2");
			model2.setCustId(custId);
			model2.setDupNo(dupNo);
			model2.setCreateTime(CapDate.getCurrentTimestamp());
			model2.setCreator(unit.getUserId());
		}
		if (StringUtils.isNotEmpty(cesMainId2)) {
			model2 = service1201.find120s01g(model2, cesMainId2);
			formIdDscr2.set("idDscr2", model2.getDataDscr());
		}

		// 存放款外匯往來情形(資信簡表)
		L120S01F model3 = service1201.findL120s01fByUniqueKey(mainId, custId,
				dupNo);
		if (model3 == null) {
			MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
			model3 = new L120S01F();
			model3.setMainId(mainId);
			model3.setCustId(custId);
			model3.setDupNo(dupNo);
			model3.setCreateTime(CapDate.getCurrentTimestamp());
			model3.setCreator(unit.getUserId());
		}
		if (StringUtils.length(cesMainId1) == 32) {
			model3 = service1201.findl120s01f(model3, cesMainId1);
		}

		CapAjaxFormResult L120S01fForm = DataParse.toResult(model3);
		L120S01fForm.set("dpAvgUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("dpAvgUnit"))));
		L120S01fForm.set("fxUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("fxUnit"))));
		L120S01fForm.set("fx2Unit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("fx2Unit"))));
		L120S01fForm.set("imUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("imUnit"))));
		L120S01fForm.set("im2Unit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("im2Unit"))));
		L120S01fForm.set("exUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("exUnit"))));
		L120S01fForm.set("ex2Unit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("ex2Unit"))));
		L120S01fForm.set("cntrUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("cntrUnit"))));
		L120S01fForm.set("nonLoanUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("nonLoanUnit"))));
		L120S01fForm.set("fxYear", trimZero(NumConverter.delCommaString(Util
				.nullToSpace(model3.getFxYear()))));
		L120S01fForm.set("imYear", trimZero(NumConverter.delCommaString(Util
				.nullToSpace(model3.getImYear()))));
		L120S01fForm.set("exYear", trimZero(NumConverter.delCommaString(Util
				.nullToSpace(model3.getExYear()))));
		SimpleDateFormat bartDateFormat = new SimpleDateFormat(
				UtilConstants.DateFormat.YYYY_MM_DD);
		String fxb = checknull(bartDateFormat, model3.getFxBDate());
		String fxe = checknull(bartDateFormat, model3.getFxEDate());
		String imb = checknull(bartDateFormat, model3.getImBDate());
		String ime = checknull(bartDateFormat, model3.getImEDate());
		String exb = checknull(bartDateFormat, model3.getExBDate());
		String exe = checknull(bartDateFormat, model3.getExEDate());
		String cntrb = checknull(bartDateFormat, model3.getCntrBDate());
		String cntre = checknull(bartDateFormat, model3.getCntrEDate());
		translate("fxb", fxb, L120S01fForm); // 舊型用法(不活)
		translate("fxe", fxe, L120S01fForm);
		translate("imb", imb, L120S01fForm);
		translate("ime", ime, L120S01fForm);
		translate("exb", exb, L120S01fForm);
		translate("exe", exe, L120S01fForm);
		translate("cntrb", cntrb, L120S01fForm);
		translate("cntre", cntre, L120S01fForm);
		L120S01fForm.set("rcdFlag", ("Y".equals(l120s01b.getRcdFlag()) ? "Y"
				: "N"));
		// 進行儲存
		service1201.saveL120s01e(list1, list2, l120m01a, model1, model2,
				model3, l120s01b);
		if (params.getAsBoolean("showMsg", true)) {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}

		CapAjaxFormResult showBorrowData = new CapAjaxFormResult();

		// -------------------------------------共用--------------------------------------------------------------
		gaapFlag = "".equals(Util.trim(gaapFlag)) ? " " : gaapFlag;
		tradeType = "".equals(Util.trim(tradeType)) ? " " : tradeType;

		// ------------------------------------營運概況--------------------------------------------------------

		JSONObject l120s01eKind1Data = service1201.getL120s01eKind1Data(mainId,
				custId, dupNo, gaapFlag.charAt(0), tradeType.charAt(0));
		resultl120s01g1.putAll(new CapAjaxFormResult(l120s01eKind1Data));

		// ------------------------------------財務狀況--------------------------------------------------------

		JSONObject l120s01eKind2Data = service1201.getL120s01eKind2Data(mainId,
				custId, dupNo, gaapFlag.charAt(0), tradeType.charAt(0));
		resultL120s01g_2.putAll(new CapAjaxFormResult(l120s01eKind2Data));
		// --------------------------------------------------------------------------------------------------
		if (l120s01b != null) {
			l120s01b.setRcdFlag("Y");
			if (StringUtils.length(cesMainId1) == 32) {
				l120s01b.setRunFlag((list1.isEmpty()) ? "N" : "Y");
				l120s01b.setFinFlag((list2.isEmpty()) ? "N" : "Y");
			}

		}

		showBorrowData.set("custId", Util.trim(l120m01a.getCustId()));
		showBorrowData.set("dupNo", Util.trim(l120m01a.getDupNo()));
		resultl120s01g1.set("rcdFlag", Util.trim(l120s01b.getRcdFlag()));
		resultl120s01g1.set("runFlag", Util.trim(l120s01b.getRunFlag()));
		resultl120s01g1.set("runCurr", Util.trim(l120s01b.getRunCurr()));
		resultl120s01g1.set("runUnit", Util.trim(l120s01b.getRunUnit()));

		resultL120s01g_2.set("finFlag", Util.trim(l120s01b.getFinFlag()));
		result.set("L120S01gForm_1", resultl120s01g1);
		result.set("formIdDscr1", formIdDscr1);
		result.set("L120S01gForm_2", resultL120s01g_2);
		result.set("formIdDscr2", formIdDscr2);
		result.set("L120S01fForm", L120S01fForm); // 當有多個model要對應form時..
		result.set("showBorrowData", showBorrowData);

		CapAjaxFormResult l120S01aForm = new CapAjaxFormResult();
		// 因為選的gaap/ifrs，行業別資料可能會變，所以需將資料再回傳頁面
		l120S01aForm.set("gaapFlag", l120s01b.getGaapFlag());
		l120S01aForm.set("tradeType", l120s01b.getTradeType());
		result.set("L120S01aForm", l120S01aForm);

		if (Util.isNotEmpty(cesMainId1)) {
			// 資信簡表
			params.put("cesMainId", cesMainId1);
		} else {
			params.put("cesMainId", UtilConstants.Mark.SPACE);
		}
		params.put("custId", custId);
		params.put("dupNo", dupNo);
		findRelate2(params);
		if (Util.isNotEmpty(cesMainId2)) {
			// 徵信報告
			params.put("cesMainId", cesMainId2);
		} else {
			params.put("cesMainId", UtilConstants.Mark.SPACE);
		}
		params.put("custId", custId);
		params.put("dupNo", dupNo);
		findRelate1(params);
		// 依序指定
		return result;
	}// ;

	/**
	 * 儲存已修改集團欄位
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120S05A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String gformEditable = params.getString("gformEditable");
		JSONObject jsonForm = JSONObject.fromObject(gformEditable);
		String editCol[] = params.getStringArray("editCol");
		for (int i = 0; i < editCol.length; i++) {
			jsonForm.put(editCol[i], Util.trim(jsonForm.get("_" + editCol[i])));
		}
		L120S05A model = service1201.findL120s05aByMainId(mainId);

		if (model != null) {
			DataParse.toBean(jsonForm, model);

			model.setGrpOver(Util.trim(params.getString("_grpOver")));
			model.setGrpGrrd(Util.trim(params.getString("_grpGrrd")));

			// J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
			if (jsonForm.containsKey("_lmtAmt")) {
				model.setLmtAmt(Util.isEmpty(Util.trim(jsonForm.optString(
						"_lmtAmt", ""))) ? null : new BigDecimal(Util
						.trim(jsonForm.optString("_lmtAmt", ""))));
			} else {
				model.setLmtAmt(null);
			}

			// J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
			if (jsonForm.containsKey("_gcrdAmt")) {
				model.setGcrdAmt(Util.isEmpty(Util.trim(jsonForm.optString(
						"_gcrdAmt", ""))) ? null : new BigDecimal(Util
						.trim(jsonForm.optString("_gcrdAmt", ""))));
			} else {
				model.setGcrdAmt(null);
			}

			service1201.save(model);
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		} else {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSCommomPage.class);
			HashMap<String, String> msg = new HashMap<String, String>();
			// other.msg203=請執行「引進相關資料」後再執行本功能
			msg.put("msg", Util.trim((String) prop.get("other.msg203")));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}

		CapAjaxFormResult LMS1205S05Form06 = DataParse.toResult(model,
				DataParse.Need, editCol);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201S05Panel.class);
		LMS1205S05Form06.set(
				"grpOver",
				"Y".equals(Util.trim(model.getGrpOver())) ? pop
						.getProperty("l120s05a.grpovershowy") : pop
						.getProperty("l120s05a.grpovershown"));
		/*
		 * LMS1205S05Form06.set( "grpGrrd",
		 * "".equals(Util.trim(model.getGrpGrrd())) ? pop
		 * .getProperty("l120s05.no0") : "6".equals(Util
		 * .trim(model.getGrpGrrd())) ? pop .getProperty("l120s05.no0") :
		 * "7".equals(Util .trim(model.getGrpGrrd())) ? pop
		 * .getProperty("l120s05.no6") : Util.trim(model .getGrpGrrd()));
		 */
		LMS1205S05Form06.set("grpGrrd", Util.trim(model.getGrpGrrd()));

		// if (Util.isChineseChar(Util.trim(LMS1205S05Form06.get("grpGrrd")))) {
		// // 控制"級"隱藏條件
		// LMS1205S05Form06.set("_hGrpGrrd", true);
		// }

		DecimalFormat df = new DecimalFormat("0.00");

		// J-105-0017-001 Web e-Loan企金授信授權外簽報書第八章增加本行買入集團企業無擔保債券有效額度與餘額。
		String[] titles = new String[] { "totMega", "crdMega", "lntMega",
				"lncMega", "excMega", "sumMega", "othMega", "finMega",
				"rskMega", "lmtMega", "gcrdMega", "bondFactMega", "bondBalMega" };
		for (String title : titles) {
			Object value = model.get(title);
			if (!Util.isEmpty(value)) {
				LMS1205S05Form06.set(title, df.format(value));
			}
		}

		// J-105-0159-001 Web e-Loan企金授信授權外簽報書說明八，修改一律顯示本行對該集團授信限額與無擔保授信額度限額。
		// if (!pop.getProperty("l120s05a.no2").equals(model.getGrpDscr())) {
		// LMS1205S05Form06.set("_hLmtAmt", true);
		// LMS1205S05Form06.set("_hGcrdAmt", true);
		// } else {
		// LMS1205S05Form06.set("_hLmtAmt", false);
		// LMS1205S05Form06.set("_hGcrdAmt", false);
		// }
		LMS1205S05Form06.set("_hLmtAmt", false);
		LMS1205S05Form06.set("_hGcrdAmt", false);

		result.set("LMS1205S05Form06", LMS1205S05Form06);
		return result;
	}// ;

	/**
	 * 儲存已修改關係企業欄位
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120S05C(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String rformEditable = params.getString("rformEditable");
		JSONObject jsonForm = JSONObject.fromObject(rformEditable);
		String editCol[] = params.getStringArray("editCol");
		for (int i = 0; i < editCol.length; i++) {
			jsonForm.put(editCol[i], Util.trim(jsonForm.get("_" + editCol[i])));
		}
		L120S05C model = service1201.findL120s05cByMainId(mainId);
		if (model != null) {
			DataParse.toBean(jsonForm, model);
			model.setRltOver(Util.trim(params.getString("_rltOver")));
			service1201.save(model);
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		CapAjaxFormResult LMS1205S05Form07 = DataParse.toResult(model,
				DataParse.Need, editCol);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201S05Panel.class);
		LMS1205S05Form07.set(
				"rltOver",
				"Y".equals(Util.trim(model.getRltOver())) ? pop
						.getProperty("l120s05a.grpovershowy") : pop
						.getProperty("l120s05a.grpovershown"));
		if (!Util.isEmpty(model.getTotMega())) {
			if (model.getTotMega() == 0) {
				LMS1205S05Form07.set("totMega", "0.00");
			} else {
				LMS1205S05Form07.set("totMega", add2Zero(model.getTotMega()));
			}
		}
		if (!Util.isEmpty(model.getCrdMega())) {
			if (model.getCrdMega() == 0) {
				LMS1205S05Form07.set("crdMega", "0.00");
			} else {
				LMS1205S05Form07.set("crdMega", add2Zero(model.getCrdMega()));
			}
		}
		if (!Util.isEmpty(model.getLntMega())) {
			if (model.getLntMega() == 0) {
				LMS1205S05Form07.set("lntMega", "0.00");
			} else {
				LMS1205S05Form07.set("lntMega", add2Zero(model.getLntMega()));
			}
		}
		if (!Util.isEmpty(model.getLncMega())) {
			if (model.getLncMega() == 0) {
				LMS1205S05Form07.set("lncMega", "0.00");
			} else {
				LMS1205S05Form07.set("lncMega", add2Zero(model.getLncMega()));
			}
		}
		if (!Util.isEmpty(model.getExcMega())) {
			if (model.getExcMega() == 0) {
				LMS1205S05Form07.set("excMega", "0.00");
			} else {
				LMS1205S05Form07.set("excMega", add2Zero(model.getExcMega()));
			}
		}
		if (!Util.isEmpty(model.getSumMega())) {
			if (model.getSumMega() == 0) {
				LMS1205S05Form07.set("sumMega", "0.00");
			} else {
				LMS1205S05Form07.set("sumMega", add2Zero(model.getSumMega()));
			}
		}
		if (!Util.isEmpty(model.getOthMega())) {
			if (model.getOthMega() == 0) {
				LMS1205S05Form07.set("othMega", "0.00");
			} else {
				LMS1205S05Form07.set("othMega", add2Zero(model.getOthMega()));
			}
		}
		if (!Util.isEmpty(model.getFinMega())) {
			if (model.getFinMega() == 0) {
				LMS1205S05Form07.set("finMega", "0.00");
			} else {
				LMS1205S05Form07.set("finMega", add2Zero(model.getFinMega()));
			}
		}
		if (!Util.isEmpty(model.getRskMega())) {
			if (model.getRskMega() == 0) {
				LMS1205S05Form07.set("rskMega", "0.00");
			} else {
				LMS1205S05Form07.set("rskMega", add2Zero(model.getRskMega()));
			}
		}
		result.set("LMS1205S05Form07", LMS1205S05Form07);
		return result;
	}// ;

	/**
	 * 企金授權外說明分頁查詢
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120S05(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String page = params.getString("page");
		L120M01A l120m01a;
		CapAjaxFormResult tallSeeCustData;
		l120m01a = service1201.findL120m01aByMainId(mainId);
		tallSeeCustData = DataParse.toResult(l120m01a);
		if (!Util.isEmpty(Util.nullToSpace(l120m01a.getTypCd()))
				&& !Util.isEmpty(Util.nullToSpace(l120m01a.getCustId()))
				&& !Util.isEmpty(Util.nullToSpace(l120m01a.getDupNo()))
				&& !Util.isEmpty(Util.nullToSpace(l120m01a.getCustName()))) {
			// 透過共用Common取得相對應的區部別名稱
			tallSeeCustData.set("typCd",
					getMessage("typCd." + l120m01a.getTypCd()));
			// tallSeeCustData.set("typCd",
			// TypCdEnum.getEnum(l120m01a.getTypCd())
			// .name());
			// 手動指定特定欄位塞特定的值...(因同一Tab由兩個Table組成而使用)
			tallSeeCustData.set("custId",
					Util.nullToSpace(l120m01a.getCustId()));
			tallSeeCustData.set("dupNo", Util.nullToSpace(l120m01a.getDupNo()));
			tallSeeCustData.set("custName",
					Util.nullToSpace(l120m01a.getCustName()));
			tallSeeCustData.set("sendFirst",
					Util.nullToSpace(l120m01a.getSendFirst()));
			tallSeeCustData.set("sendFirstTime",
					Util.nullToSpace(l120m01a.getSendFirstTime()));
			tallSeeCustData.set("sendLast",
					Util.nullToSpace(l120m01a.getSendLast()));
			tallSeeCustData.set("sendLastTime",
					Util.nullToSpace(l120m01a.getSendLastTime()));
		}
		L120S01B l120s01b = service1201.findL120s01bByUniqueKey(mainId,
				Util.trim(l120m01a.getCesCustId()),
				Util.trim(l120m01a.getCesDupNo()));
		// 企金一般
		if ("5a".equals(page)) {
			// 業務概況
			CapAjaxFormResult resultl120m01a = DataParse.toResult(l120m01a);
			L120M01D l120m01d = service1201
					.findL120m01dByUniqueKey(mainId, "1");
			if (l120m01d != null) {
				resultl120m01a.set("itemDscr01",
						Util.trim(l120m01d.getItemDscr()));
			}
			result.set("LMS1205S05Form01", resultl120m01a);
		} else if ("5b".equals(page)) {
			// 營運概況
			if (!Util.isEmpty(l120m01a.getCesCustId())
					&& !Util.isEmpty(l120m01a.getCesDupNo())) {
				CapAjaxFormResult resultl120s01g1 = new CapAjaxFormResult();
				result.set("noCesCustId", false);

				String gaapFlag = "";
				String tradeType = "";
				if (l120s01b != null) {
					gaapFlag = CapString.trimNull(l120s01b.getGaapFlag());
					tradeType = CapString.trimNull(l120s01b.getTradeType());
				}

				gaapFlag = "".equals(gaapFlag) ? " " : gaapFlag;
				tradeType = "".equals(tradeType) ? " " : tradeType;

				// 取得營遇概況json內容
				JSONObject l120s01eKind1Data = service1201
						.getL120s01eKind1Data(mainId, l120m01a.getCesCustId(),
								l120m01a.getCesDupNo(), gaapFlag.charAt(0),
								tradeType.charAt(0));
				resultl120s01g1
						.putAll(new CapAjaxFormResult(l120s01eKind1Data));

				L120S01G l120s01g1 = service1201.findL120s01gByUniqueKey(
						l120m01a.getMainId(), l120m01a.getCesCustId(),
						l120m01a.getCesDupNo(), "1");

				if (l120s01g1 != null) {
					resultl120s01g1.putAll(DataParse.toResult(l120s01g1));
				}
				if (l120s01b != null) {
					resultl120s01g1.set("runFlag",
							Util.trim(l120s01b.getRunFlag()));
					resultl120s01g1.set("runCurr",
							Util.trim(l120s01b.getRunCurr()));
					resultl120s01g1.set("runUnit",
							Util.trim(l120s01b.getRunUnit()));
				} else {
					resultl120s01g1.set("runFlag", "N");
				}

				// 分析與評估(營運概況)
				if (l120s01g1 != null) {
					resultl120s01g1.set("idDscr1",
							Util.trim(l120s01g1.getDataDscr()));
					L120S01A l120s01a = service1201.findL120s01aByUniqueKey(
							mainId, l120m01a.getCesCustId(),
							l120m01a.getCesDupNo());
					resultl120s01g1.set(
							"cesTypCd",
							getMessage("typCd."
									+ Util.trim(l120s01a.getTypCd())));
					// resultl120s01g1.set("cesTypCd",
					// TypCdEnum.getEnum(Util.trim(l120s01a.getTypCd()))
					// .name());
					resultl120s01g1.set("cesCustId",
							Util.trim(l120s01a.getCustId()));
					resultl120s01g1.set("cesDupNo",
							Util.trim(l120s01a.getDupNo()));
					resultl120s01g1.set("cesCustName",
							Util.trim(l120s01a.getCustName()));
					// 當有多個model要對應form時.. 依序指定
					resultl120s01g1.add(tallSeeCustData);
				}
				result.set("LMS1205S05Form02", resultl120s01g1);
			} else {
				result.set("noCesCustId", true);
				result.set("LMS1205S05Form02", "");
			}
		} else if ("5c".equals(page)) {
			// 財務狀況
			if (!Util.isEmpty(l120m01a.getCesCustId())
					&& !Util.isEmpty(l120m01a.getCesDupNo())) {

				L120S01G l120s01g2 = service1201.findL120s01gByUniqueKey(
						l120m01a.getMainId(), l120m01a.getCesCustId(),
						l120m01a.getCesDupNo(),
						UtilConstants.Casedoc.L120s01gType.財務狀況分析與評估);

				CapAjaxFormResult resultL120s01g_2 = new CapAjaxFormResult();
				if (l120s01g2 != null) {
					resultL120s01g_2.putAll(DataParse.toResult(l120s01g2));
				}
				if (l120s01b != null) {
					resultL120s01g_2.set("finFlag",
							Util.trim(l120s01b.getFinFlag()));
				} else {
					resultL120s01g_2.set("finFlag", "N");
				}

				String gaapFlag = l120s01b != null ? CapString
						.trimNull(l120s01b.getGaapFlag()) : "";
				String tradeType = l120s01b != null ? CapString
						.trimNull(l120s01b.getTradeType()) : "";
				gaapFlag = "".equals(gaapFlag) ? " " : gaapFlag;
				tradeType = "".equals(tradeType) ? " " : tradeType;

				// 取得財務概況json內容
				JSONObject l120s01eKind2Data = service1201
						.getL120s01eKind2Data(mainId, l120m01a.getCesCustId(),
								l120m01a.getCesDupNo(), gaapFlag.charAt(0),
								tradeType.charAt(0));
				resultL120s01g_2
						.putAll(new CapAjaxFormResult(l120s01eKind2Data));

				// 分析與評估(財務狀況)
				if (l120s01g2 != null) {
					resultL120s01g_2.set("idDscr2",
							Util.nullToSpace(l120s01g2.getDataDscr()));
					L120S01A l120s01a = service1201.findL120s01aByUniqueKey(
							mainId, l120m01a.getCesCustId(),
							l120m01a.getCesDupNo());
					resultL120s01g_2.set(
							"cesTypCd",
							getMessage("typCd."
									+ Util.trim(l120s01a.getTypCd())));
					// resultL120s01g_2.set("cesTypCd",
					// TypCdEnum.getEnum(Util.trim(l120s01a.getTypCd()))
					// .name());
					resultL120s01g_2.set("cesCustId",
							Util.trim(l120s01a.getCustId()));
					resultL120s01g_2.set("cesDupNo",
							Util.trim(l120s01a.getDupNo()));
					resultL120s01g_2.set("cesCustName",
							Util.trim(l120s01a.getCustName()));
					// 當有多個model要對應form時.. 依序指定
					resultL120s01g_2.add(tallSeeCustData);
				}
				result.set("LMS1205S05Form03", resultL120s01g_2);
			} else {
				result.set("noCesCustId", true);
				result.set("LMS1205S05Form03", "");
			}
		} else if ("5d".equals(page)) {
			// 中長期
			CapAjaxFormResult Lms1205s05form04 = new CapAjaxFormResult();
			L120M01D l120m01da = service1201.findL120m01dByUniqueKey(mainId,
					"2");
			if (l120m01da != null) {
				Lms1205s05form04.set("itemDscr02",
						Util.trim(l120m01da.getItemDscr()));
			}
			Lms1205s05form04.set("longCaseFlag", l120m01a.getLongCaseFlag());
			Lms1205s05form04.set("longCaseDscr", l120m01a.getLongCaseDscr());
			result.set("LMS1205S05Form04", Lms1205s05form04);
		} else if ("5e".equals(page)) {
			// 存放款及外匯往來
			L120S01F l120s01f = service1201.findL120s01fByUniqueKey(mainId,
					l120m01a.getCesCustId(), l120m01a.getCesDupNo());
			CapAjaxFormResult form05Lms1205s05 = new CapAjaxFormResult();
			if (l120s01f != null) {
				form05Lms1205s05 = DataParse.toResult(l120s01f);

				CodeType codetype1 = codeService
						.findByCodeTypeAndCodeValue("lms1205s01_Unit",
								NumConverter.delCommaString(Util
										.nullToSpace(form05Lms1205s05
												.get("dpAvgUnit"))));
				CodeType codetype2 = codeService.findByCodeTypeAndCodeValue(
						"lms1205s01_Unit", NumConverter.delCommaString(Util
								.nullToSpace(form05Lms1205s05.get("fxUnit"))));
				CodeType codetype3 = codeService.findByCodeTypeAndCodeValue(
						"lms1205s01_Unit", NumConverter.delCommaString(Util
								.nullToSpace(form05Lms1205s05.get("fx2Unit"))));
				CodeType codetype4 = codeService.findByCodeTypeAndCodeValue(
						"lms1205s01_Unit", NumConverter.delCommaString(Util
								.nullToSpace(form05Lms1205s05.get("imUnit"))));
				CodeType codetype5 = codeService.findByCodeTypeAndCodeValue(
						"lms1205s01_Unit", NumConverter.delCommaString(Util
								.nullToSpace(form05Lms1205s05.get("im2Unit"))));
				CodeType codetype6 = codeService.findByCodeTypeAndCodeValue(
						"lms1205s01_Unit", NumConverter.delCommaString(Util
								.nullToSpace(form05Lms1205s05.get("exUnit"))));
				CodeType codetype7 = codeService.findByCodeTypeAndCodeValue(
						"lms1205s01_Unit", NumConverter.delCommaString(Util
								.nullToSpace(form05Lms1205s05.get("ex2Unit"))));
				CodeType codetype8 = codeService
						.findByCodeTypeAndCodeValue("lms1205s01_Unit",
								NumConverter.delCommaString(Util
										.nullToSpace(form05Lms1205s05
												.get("cntrUnit"))));
				CodeType codetype9 = codeService.findByCodeTypeAndCodeValue(
						"lms1205s01_Unit", NumConverter.delCommaString(Util
								.nullToSpace(form05Lms1205s05
										.get("nonLoanUnit"))));

				if (codetype1 != null) {
					form05Lms1205s05
							.set("dpAvgUnit",
									Util.isNumeric(Util.trim(codetype1
											.getCodeDesc())) ? "" : Util
											.trim(codetype1.getCodeDesc()));
				} else {
					form05Lms1205s05.set("dpAvgUnit", "");
				}
				if (codetype2 != null) {
					form05Lms1205s05
							.set("fxUnit",
									Util.isNumeric(Util.trim(codetype2
											.getCodeDesc())) ? "" : Util
											.trim(codetype2.getCodeDesc()));
				} else {
					form05Lms1205s05.set("fxUnit", "");
				}
				if (codetype3 != null) {
					form05Lms1205s05
							.set("fx2Unit",
									Util.isNumeric(Util.trim(codetype3
											.getCodeDesc())) ? "" : Util
											.trim(codetype3.getCodeDesc()));
				} else {
					form05Lms1205s05.set("fx2Unit", "");
				}
				if (codetype4 != null) {
					form05Lms1205s05
							.set("imUnit",
									Util.isNumeric(Util.trim(codetype4
											.getCodeDesc())) ? "" : Util
											.trim(codetype4.getCodeDesc()));
				} else {
					form05Lms1205s05.set("imUnit", "");
				}
				if (codetype5 != null) {
					form05Lms1205s05
							.set("im2Unit",
									Util.isNumeric(Util.trim(codetype5
											.getCodeDesc())) ? "" : Util
											.trim(codetype5.getCodeDesc()));
				} else {
					form05Lms1205s05.set("im2Unit", "");
				}
				if (codetype6 != null) {
					form05Lms1205s05
							.set("exUnit",
									Util.isNumeric(Util.trim(codetype6
											.getCodeDesc())) ? "" : Util
											.trim(codetype6.getCodeDesc()));
				} else {
					form05Lms1205s05.set("exUnit", "");
				}
				if (codetype7 != null) {
					form05Lms1205s05
							.set("ex2Unit",
									Util.isNumeric(Util.trim(codetype7
											.getCodeDesc())) ? "" : Util
											.trim(codetype7.getCodeDesc()));
				} else {
					form05Lms1205s05.set("ex2Unit", "");
				}
				if (codetype8 != null) {
					form05Lms1205s05
							.set("cntrUnit",
									Util.isNumeric(Util.trim(codetype8
											.getCodeDesc())) ? "" : Util
											.trim(codetype8.getCodeDesc()));
				} else {
					form05Lms1205s05.set("cntrUnit", "");
				}
				if (codetype9 != null) {
					form05Lms1205s05
							.set("nonLoanUnit",
									Util.isNumeric(Util.trim(codetype9
											.getCodeDesc())) ? "" : Util
											.trim(codetype9.getCodeDesc()));
				} else {
					form05Lms1205s05.set("nonLoanUnit", "");
				}

				form05Lms1205s05.set("fxYear", trimZero(NumConverter
						.delCommaString(Util.nullToSpace(form05Lms1205s05
								.get("fxYear")))));
				form05Lms1205s05.set("imYear", trimZero(NumConverter
						.delCommaString(Util.nullToSpace(form05Lms1205s05
								.get("imYear")))));
				form05Lms1205s05.set("exYear", trimZero(NumConverter
						.delCommaString(Util.nullToSpace(form05Lms1205s05
								.get("exYear")))));

				// 2013-07-26因為徵信資信簡表平均存款可以打N.A.故配合調整
				if (l120s01f.getDpAvgAmt() == null
						&& !"".equals(Util.nullToSpace(l120s01f.getDpAvgCurr()))) {
					form05Lms1205s05.set("dpAvgAmt", "N.A.");
				}

				SimpleDateFormat bartDateFormat = new SimpleDateFormat(
						UtilConstants.DateFormat.YYYY_MM_DD);
				String fxb = checknull(bartDateFormat, l120s01f.getFxBDate());
				String fxe = checknull(bartDateFormat, l120s01f.getFxEDate());
				String imb = checknull(bartDateFormat, l120s01f.getImBDate());
				String ime = checknull(bartDateFormat, l120s01f.getImEDate());
				String exb = checknull(bartDateFormat, l120s01f.getExBDate());
				String exe = checknull(bartDateFormat, l120s01f.getExEDate());
				String cntrb = checknull(bartDateFormat,
						l120s01f.getCntrBDate());
				String cntre = checknull(bartDateFormat,
						l120s01f.getCntrEDate());
				translate("fxb", fxb, form05Lms1205s05); // 舊型用法(不活)
				translate("fxe", fxe, form05Lms1205s05);
				translate("imb", imb, form05Lms1205s05);
				translate("ime", ime, form05Lms1205s05);
				translate("exb", exb, form05Lms1205s05);
				translate("exe", exe, form05Lms1205s05);
				translate("cntrb", cntrb, form05Lms1205s05);
				translate("cntre", cntre, form05Lms1205s05);
			}

			L120S01A l120s01a = service1201.findL120s01aByUniqueKey(mainId,
					l120m01a.getCesCustId(), l120m01a.getCesDupNo());
			if (l120s01a != null) {
				form05Lms1205s05.set("cesTypCd",
						getMessage("typCd." + Util.trim(l120s01a.getTypCd())));
				// form05Lms1205s05.set("cesTypCd",
				// TypCdEnum.getEnum(Util.trim(l120s01a.getTypCd()))
				// .name());
				form05Lms1205s05.set("cesCustId",
						Util.trim(l120s01a.getCustId()));
				form05Lms1205s05
						.set("cesDupNo", Util.trim(l120s01a.getDupNo()));
				form05Lms1205s05.set("cesCustName",
						Util.trim(l120s01a.getCustName()));
			}
			form05Lms1205s05.add(tallSeeCustData);
			result.set("LMS1205S05Form05", form05Lms1205s05);
		} else if ("5f".equals(page)) {
			// 集團企業查詢
			L120S05A l120s05a = service1201.findL120s05aByMainId(mainId);
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS1201S05Panel.class);
			if (l120s05a != null) {
				CapAjaxFormResult form06Lms1205s05 = DataParse
						.toResult(l120s05a);
				if ("1".equals(l120s05a.getGrpSrc())) {
					form06Lms1205s05.set("grpSrc",
							pop.getProperty("l120s05.data1"));
				} else if ("2".equals(l120s05a.getGrpSrc())) {
					form06Lms1205s05.set("grpSrc",
							pop.getProperty("l120s05.data2"));
				} else {
					form06Lms1205s05.set("grpSrc", "");
				}
				if ("Y".equals(l120s05a.getGrpOver())) {
					form06Lms1205s05.set("grpOver",
							pop.getProperty("l120s05.other36"));
				} else {
					form06Lms1205s05.set("grpOver",
							pop.getProperty("l120s05.other35"));
				}

				// J-106-0110-001 Web
				// e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
				if (Util.notEquals(Util.trim(l120s05a.getGrpFlag()), "")) {
					form06Lms1205s05.set("grpFlag",
							Util.trim(l120s05a.getGrpFlag()));
				} else {
					form06Lms1205s05.set("grpFlag", "N");
				}

				// J-105-0159-001 Web
				// e-Loan企金授信授權外簽報書說明八，修改一律顯示本行對該集團授信限額與無擔保授信額度限額。
				// if (!pop.getProperty("l120s05a.no2").equals(
				// l120s05a.getGrpDscr())) {
				// form06Lms1205s05.set("_hLmtAmt", true);
				// form06Lms1205s05.set("_hGcrdAmt", true);
				// } else {
				// form06Lms1205s05.set("_hLmtAmt", false);
				// form06Lms1205s05.set("_hGcrdAmt", false);
				// }
				form06Lms1205s05.set("_hLmtAmt", false);
				form06Lms1205s05.set("_hGcrdAmt", false);

				List<L120S05B> list = service1201.findL120s05bByMainId(mainId);
				for (L120S05B l120s05b : list) {
					if (!Util
							.isEmpty(Util.trim(form06Lms1205s05.get("gpQDate")))
							&& !Util.isEmpty(Util.trim(form06Lms1205s05
									.get("lnDate")))
							&& !Util.isEmpty(Util.trim(form06Lms1205s05
									.get("gpComDate")))
							&& !Util.isEmpty(Util.trim(form06Lms1205s05
									.get("gpRiskDate")))) {
						break;
					} else {
						if (!Util.isEmpty(l120s05b.getGpQDate())) {
							form06Lms1205s05.set("gpQDate",
									l120s05b.getGpQDate());
						}
						if (!Util.isEmpty(l120s05b.getLnDate())) {
							form06Lms1205s05
									.set("lnDate", l120s05b.getLnDate());
						}
						if (!Util.isEmpty(l120s05b.getGpComDate())) {
							form06Lms1205s05.set("gpComDate",
									l120s05b.getGpComDate());
						}
						if (!Util.isEmpty(l120s05b.getGpRiskDate())) {
							form06Lms1205s05.set("gpRiskDate",
									l120s05b.getGpRiskDate());
						}
					}
				}
				DecimalFormat df = new DecimalFormat("0.00");

				// J-105-0167-001 Web e-Loan
				// 企金授信案件簽報書第八之3增列集團企業(應予注意集團)有關集團評等之「財務警訊項目資訊」。
				form06Lms1205s05.set("grpFinAlertStr", lmsService
						.buildGrpFinAlertStr(l120s05a.getGrpFinAlert()));

				// J-105-0017-001 Web e-Loan企金授信授權外簽報書第八章增加本行買入集團企業無擔保債券有效額度與餘額。
				// J-107-0395_05097_B1001 Web
				// e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍
				// "bondFlag", "bdBal",
				// "bdBalMega", "bdBalN", "bdBalNg", "bdBalNgMega",
				// "bdBalNog"
				String[] titles = new String[] { "totMega", "crdMega",
						"lntMega", "lncMega", "excMega", "sumMega", "othMega",
						"finMega", "rskMega", "lmtMega", "gcrdMega",
						"bondFactMega", "bondBalMega", "bdBalMega",
						"bdBalNMega" };
				for (String title : titles) {
					Object value = l120s05a.get(title);
					if (!Util.isEmpty(value)) {
						form06Lms1205s05.set(title, df.format(value));
					}
				}

				// J-107-0007-001 Web
				// e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
				DecimalFormat dfGrpRate = new DecimalFormat("#0.0000");
				String[] titlesGrpRate = new String[] { "avgTwdRt", "avgUsdRt",
						"avgTwdSRt", "avgUsdSRt", "avgTwdNRt", "avgUsdNRt",
						"maxTwdSRt", "maxUsdSRt", "maxTwdNRt", "maxUsdNRt",
						"minTwdSRt", "minUsdSRt", "minTwdNRt", "minUsdNRt" };
				for (String title : titlesGrpRate) {
					Object value = l120s05a.get(title);
					if (!Util.isEmpty(value)) {
						form06Lms1205s05.set(title, dfGrpRate.format(value));
					} else {
						form06Lms1205s05.set(title, "");
					}
				}

				// 民國轉西元年
				form06Lms1205s05
						.set("endYear",
								(Util.trim(
										Util.nullToSpace(l120s05a.getEndYear()))
										.length() == 4) ? l120s05a.getEndYear()
										: ((Util.trim(Util.nullToSpace(l120s05a
												.getEndYear()))).length() == 0) ? null
												: Util.parseInt(l120s05a
														.getEndYear()) + 1911);
				// form06Lms1205s05.set(
				// "grpGrrd",
				// "".equals(Util.trim(l120s05a.getGrpGrrd())) ? pop
				// .getProperty("l120s05.no0") : "6".equals(Util
				// .trim(l120s05a.getGrpGrrd())) ? pop
				// .getProperty("l120s05.no0") : "7".equals(Util
				// .trim(l120s05a.getGrpGrrd())) ? pop
				// .getProperty("l120s05.no6") : Util
				// .trim(l120s05a.getGrpGrrd()));
				form06Lms1205s05.set("grpGrrd",
						Util.trim(l120s05a.getGrpGrrd()));
				// if (Util.isChineseChar(Util.trim(form06Lms1205s05
				// .get("grpGrrd")))) {
				// // 控制"級"隱藏條件
				// form06Lms1205s05.set("_hGrpGrrd", true);
				// }

				// J-107-0087-001 Web
				// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
				boolean newGrpGrade = lmsService.isNewGrpGrade(
						Util.trim(l120s05a.getGrpYear()), true);
				String defultNoGrade = lmsService.getGrpNoGrade(newGrpGrade);

				// J-107-0007-001 Web
				// e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
				if (!lmsService.isShowGrpRateData(l120s05a)) {
					// 尚無「集團企業代號」者或集團企業註記「財務危機集團企業」者
					form06Lms1205s05.set("showGrpRate", "N");
				} else {
					Properties pop1205s06a = MessageBundleScriptCreator
							.getComponentResource(LMS1201S05Page06a.class);
					form06Lms1205s05.set("showGrpRate", "Y");

					// J-107-0087-001 Web
					// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
					if (Util.equals(l120s05a.getGrpGrade(), defultNoGrade)) {
						if (Util.equals(l120s05a.getBadFg(), "0")
								|| Util.equals(l120s05a.getBadFg(), "")) {
							// l120s05a.grpGradeMemo_2=(為新戶或未評等)
							form06Lms1205s05.set("grpGradeMemo", pop1205s06a
									.getProperty("l120s05a.grpGradeMemo_2"));
						} else {
							// l120s05a.grpGradeMemo_3=(為新戶或未評等，惟列管應予注意集團企業者）
							form06Lms1205s05.set("grpGradeMemo", pop1205s06a
									.getProperty("l120s05a.grpGradeMemo_3"));
						}
					} else {
						if (Util.equals(l120s05a.getBadFg(), "0")
								|| Util.equals(l120s05a.getBadFg(), "")) {
							// l120s05a.grpGradeMemo_2=(為新戶或未評等)
							form06Lms1205s05.set("grpGradeMemo", "");
						} else {
							// l120s05a.grpGradeMemo_1=(且列管應予注意集團企業者）
							form06Lms1205s05.set("grpGradeMemo", pop1205s06a
									.getProperty("l120s05a.grpGradeMemo_1"));
						}
					}
				}

				// J-107-0087-001 Web
				// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
				if (newGrpGrade) {
					// (大A、中B....)
					form06Lms1205s05.set(
							"grpSizeLvlShow",
							lmsService.getGrpSizeLvlShow(
									Util.trim(l120s05a.getGrpSize()),
									Util.trim(l120s05a.getGrpLevel())));
				} else {
					form06Lms1205s05.set("grpSizeLvlShow", "");
				}

				result.set("LMS1205S05Form06", form06Lms1205s05);
			} else {
				CapAjaxFormResult form06Lms1205s05 = new CapAjaxFormResult();
				form06Lms1205s05.set("grpFlag", "N");
				result.set("LMS1205S05Form06", form06Lms1205s05);
			}
		} else {
			// 關係企業
			L120S05C l120s05c = service1201.findL120s05cByMainId(mainId);
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS1201S05Panel.class);
			if (l120s05c != null) {
				CapAjaxFormResult form07Lms1205s05 = DataParse
						.toResult(l120s05c);
				if ("Y".equals(l120s05c.getRltOver())) {
					form07Lms1205s05.set("rltOver",
							pop.getProperty("l120s05.other36"));
				} else {
					form07Lms1205s05.set("rltOver",
							pop.getProperty("l120s05.other35"));
				}
				if ("Y".equals(l120s05c.getRltFlag())) {
					form07Lms1205s05.set("rltFlag", "Y");
				} else {
					form07Lms1205s05.set("rltFlag", "N");
				}

				if (!Util.isEmpty(l120s05c.getTotMega())) {
					if (l120s05c.getTotMega() == 0) {
						form07Lms1205s05.set("totMega", "0.00");
					} else {
						form07Lms1205s05.set("totMega",
								add2Zero(l120s05c.getTotMega()));
					}
				}
				if (!Util.isEmpty(l120s05c.getCrdMega())) {
					if (l120s05c.getCrdMega() == 0) {
						form07Lms1205s05.set("crdMega", "0.00");
					} else {
						form07Lms1205s05.set("crdMega",
								add2Zero(l120s05c.getCrdMega()));
					}
				}
				if (!Util.isEmpty(l120s05c.getLntMega())) {
					if (l120s05c.getLntMega() == 0) {
						form07Lms1205s05.set("lntMega", "0.00");
					} else {
						form07Lms1205s05.set("lntMega",
								add2Zero(l120s05c.getLntMega()));
					}
				}
				if (!Util.isEmpty(l120s05c.getLncMega())) {
					if (l120s05c.getLncMega() == 0) {
						form07Lms1205s05.set("lncMega", "0.00");
					} else {
						form07Lms1205s05.set("lncMega",
								add2Zero(l120s05c.getLncMega()));
					}
				}
				if (!Util.isEmpty(l120s05c.getExcMega())) {
					if (l120s05c.getExcMega() == 0) {
						form07Lms1205s05.set("excMega", "0.00");
					} else {
						form07Lms1205s05.set("excMega",
								add2Zero(l120s05c.getExcMega()));
					}
				}
				if (!Util.isEmpty(l120s05c.getSumMega())) {
					if (l120s05c.getSumMega() == 0) {
						form07Lms1205s05.set("sumMega", "0.00");
					} else {
						form07Lms1205s05.set("sumMega",
								add2Zero(l120s05c.getSumMega()));
					}
				}
				if (!Util.isEmpty(l120s05c.getOthMega())) {
					if (l120s05c.getOthMega() == 0) {
						form07Lms1205s05.set("othMega", "0.00");
					} else {
						form07Lms1205s05.set("othMega",
								add2Zero(l120s05c.getOthMega()));
					}
				}
				if (!Util.isEmpty(l120s05c.getFinMega())) {
					if (l120s05c.getFinMega() == 0) {
						form07Lms1205s05.set("finMega", "0.00");
					} else {
						form07Lms1205s05.set("finMega",
								add2Zero(l120s05c.getFinMega()));
					}
				}
				if (!Util.isEmpty(l120s05c.getRskMega())) {
					if (l120s05c.getRskMega() == 0) {
						form07Lms1205s05.set("rskMega", "0.00");
					} else {
						form07Lms1205s05.set("rskMega",
								add2Zero(l120s05c.getRskMega()));
					}
				}
				result.set("LMS1205S05Form07", form07Lms1205s05);
			} else {
				CapAjaxFormResult form07Lms1205s05 = new CapAjaxFormResult();
				// J-106-0110-001 Web
				// e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
				form07Lms1205s05.set("rltFlag", "N");
				result.set("LMS1205S05Form07", form07Lms1205s05);
			}
		}
		result.set("showBorrowData", tallSeeCustData);
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getFinRatioItems(PageParameters params) throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS02BPanel.class);

		CapAjaxFormResult result = new CapAjaxFormResult();
		String cesMainId = params.getString("cesMainId1");
		String gaapFlag = params.getString("gaapFlag", " ");
		String fssType = " ";

		List<Map<String, Object>> datas = eloanDbBaseService
				.findC120M01C_selL120s01e2(Util.trim(String.valueOf(cesMainId)));

		// 當抓不到資料時，回傳財務因子類別以IFRS一般財報為準
		if (CollectionUtils.isEmpty(datas)) {
			gaapFlag = "1";
			fssType = "M";
		} else {
			for (Map<String, Object> map : datas) {
				fssType = (String) map.get("fssType");
				fssType = StringUtils.right(fssType, 1);
			}
		}

		String[] codes = service1201.getFssItemCode(gaapFlag.charAt(0),
				fssType.charAt(0));

		if (codes == null) {
			// 改抓預設值 怕徵信新增行業時沒有配合修改(EX.營造業)
			gaapFlag = "1";
			fssType = "M";
			codes = service1201.getFssItemCode(gaapFlag.charAt(0),
					fssType.charAt(0));
		}

		String kind2 = codes[1];

		String[] kind2_codes = kind2.split("\\|");
		JSONObject finRatioItem = new JSONObject();
		for (String kind2_code : kind2_codes) {
			// System.out.println(kind2_code);
			finRatioItem.put(
					kind2_code,
					pop.getProperty(String.valueOf(gaapFlag)
							+ String.valueOf(fssType) + "." + kind2_code));
		}

		result.set("finRatioItem", new CapAjaxFormResult(finRatioItem));
		result.set("fssType", fssType);
		result.set("gaapFlag", gaapFlag);

		return result;

	}

	/**
	 * 取得往來異常戶異動類別
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = false)
	public IResult GetLNFE0851UpFlag(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		JSONObject jsonob = lmsService.GetLNFE0851UpFlagInner(mainId);
		result.set("lnfe0851UpFlag", jsonob.getString("lnfe0851UpFlag"));
		result.set("allCustName", jsonob.getString("allCustName"));
		return result;
	}

	/**
	 * 引進平均動用率
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult ImportAvgURate(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = params.getString("custId");
		String sDate = params.getString("sDate");
		String eDate = params.getString("eDate");
		String unitNo = MegaSSOSecurityContext.getUnitNo();
		BigDecimal avgURate = misDbService.importAvgURateByCustIdDate(custId,
				sDate, eDate, unitNo);
		result.set("avgURate", avgURate == null ? "" : avgURate.toString());
		return result;
	}

	/**
	 * J-110-0375 常董稿案由
	 * 取得常董稿案由
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getGistForMd(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		if(Util.isNotEmpty(mainId)){
			L120M01D l120m01dY = service1201.findL120m01dByUniqueKey(mainId, 
					UtilConstants.Casedoc.L120m01dItemType.常董會案由);
			if (l120m01dY != null) {
				result.set("itemDscrY", 
						(Util.isEmpty(Util.nullToSpace(l120m01dY.getItemDscr())) ? 
								UtilConstants.Mark.SPACE : l120m01dY.getItemDscr()));
			} else {
				result.set("itemDscrY", UtilConstants.Mark.SPACE);
			}
		} else {
			result.set("itemDscrY", UtilConstants.Mark.SPACE);
		}
		return result;
	}
	
	/**
	 * J-110-0375 常董稿案由
	 * 設定常董稿案由
	 */
	@DomainAuth(AuthType.Modify)
	public IResult setGistForMd(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		String itemDscrY = params.getString("itemDscrY");
		
		if(Util.isNotEmpty(mainId)){
			L120M01D l120m01dY = service1201.findL120m01dByUniqueKey(mainId, 
					UtilConstants.Casedoc.L120m01dItemType.常董會案由);
			if (l120m01dY == null) {
				l120m01dY = new L120M01D();
				l120m01dY.setMainId(mainId);
				l120m01dY.setItemType(UtilConstants.Casedoc.L120m01dItemType.常董會案由);
				l120m01dY.setCreateTime(CapDate.getCurrentTimestamp());
				l120m01dY.setCreator(user.getUserId());
			}
			if (!CapString.isEmpty(Util.trim(itemDscrY))) {
				l120m01dY.setItemDscr(itemDscrY);
			} else {
				l120m01dY.setItemDscr(UtilConstants.Mark.SPACE);
			}
			service1201.save(l120m01dY);
			
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		} else {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行有誤));
		}
		return result;
	}
	
	/**
	 * J-113-0306 Eloan>企業授信>案件簽報書送呈區域中心審核後，若被退件，在「待補件/撤件」中之被撤件之案件，能否設計可以再撈到編製中重新簽報，以增進作業效率
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult reBackL120m01a(PageParameters params) throws CapException {
		Properties popLms1201v01 = MessageBundleScriptCreator
		.getComponentResource(LMS1201V01Page.class);
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("errorMsg", "");

		String oid = params.getString("oid"); //l120m01a.oid
		L120M01A l120m01a = service1201.findL120m01aByOid(oid);
		//只有撤件能退
		if(Util.trim(l120m01a.getDocStatus()).equals(CreditDocStatusEnum.海外_待撤件.getCode())){
			// 更新Docstatus
			l120m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			service1201.save(l120m01a);
			List<L140M01A> listL140m01a = lms1401Service
					.findL140m01aListByL120m01cMainId(l120m01a.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表);
			if (listL140m01a != null) {
				for (L140M01A l140m01a : listL140m01a) {
					l140m01a.setDocStatus(FlowDocStatusEnum.編製中.getCode());
				}
				lms1401Service.saveL140m01aList(listL140m01a);
			}
			// 印出  執行成功訊息
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}else{//拋錯
			Map<String, String> messageMap = new HashMap<String, String>();
			//EFD0025-執行有誤文件非撤件案件
			messageMap.put("msg", ": " + popLms1201v01.getProperty("l120v05.error01"));
								throw new CapMessageException(getPopMessage("EFD0025",
										messageMap), getClass());
		}

		return result;
	}
	
	
}
