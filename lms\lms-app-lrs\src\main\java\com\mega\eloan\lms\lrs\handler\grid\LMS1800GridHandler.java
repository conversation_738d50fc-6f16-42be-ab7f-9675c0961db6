package com.mega.eloan.lms.lrs.handler.grid;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lrs.pages.LMS1800M01Page;
import com.mega.eloan.lms.lrs.service.LMS1805Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("lms1800gridhandler")
public class LMS1800GridHandler extends AbstractGridHandler {

	@Resource
	UserInfoService userservice;

	@Resource
	LMS1805Service service;

	@Resource
	BranchService branchService;

	@Resource
	RetrialService retrialService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	CodeTypeService codeTypeService;

	Properties prop_lms1800m01 = MessageBundleScriptCreator
			.getComponentResource(LMS1800M01Page.class);
	
	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapGridResult query(ISearch pageSetting, PageParameters params) throws CapException, ParseException {

		String dataDate = Util.nullToSpace(params.getString("dataDate"));
		if (Util.isNotEmpty(dataDate)) {
			Date dataDateObj = sdf.parse(dataDate + "-01");
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dataDate", dataDateObj);
		}
		String brId = Util.nullToSpace(params.getString("brId"));
		if (Util.isNotEmpty(brId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId",
					brId);
		}

		String gridview_param = Util.trim(params.getString("gridview_param"));
		if (Util.isNotEmpty(gridview_param)) {
			if (Util.equals(gridview_param, "THIS_MONTH")) {
				String s = Util.trim(StringUtils.substring(
						TWNDate.toAD(new Date()), 0, 7));
				pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
						"defaultCTLDate", s + "-01");
				pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
						"defaultCTLDate", CrsUtil.getDataEndDate(s));

			}
		}

		// 建立主要Search 條件
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS, docStatus);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l180a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);

		Page page = retrialService.findPage(L180M01A.class, pageSetting);
		List<L180M01A> list = page.getContent();
		for (L180M01A model : list) {
			// PEO 人工新增 SYS 系統新增
			if ("2".equals(model.getCreateBy())) {
				model.setCreateBy(prop_lms1800m01
						.getProperty("L180M01A.createBy.2"));
			} else if ("1".equals(model.getCreateBy())) {
				model.setCreateBy(prop_lms1800m01
						.getProperty("L180M01A.createBy.1"));
			}
			if (!Util.isEmpty(model.getUpdater())) {
				model.setUpdater(!Util.isEmpty(userservice.getUserName(model
						.getUpdater())) ? userservice.getUserName(model
						.getUpdater()) : model.getUpdater());
			}
			if (!Util.isEmpty(model.getCreator())) {
				model.setCreator(!Util.isEmpty(userservice.getUserName(model
						.getCreator())) ? userservice.getUserName(model
						.getCreator()) : model.getCreator());
			}
			model.setBranchId(model.getBranchId()
					+ " "
					+ Util.trim(branchService.getBranchName(model.getBranchId())));

			// J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
			// RPA狀態*****************************************************
			if (!Util.isEmpty(Util.trim(model.getStatus()))) {
				model.setStatus(prop_lms1800m01.getProperty("rpa.status."
						+ Util.trim(model.getStatus())));

			} else {
				model.setStatus("");
			}

		}

		return new CapGridResult(list, page.getTotalRow());
	}

	public CapMapGridResult queryList(ISearch pageSetting,
			PageParameters params) throws CapException {
		Map<String, String> map_lrs_NewAdd = retrialService.get_lrs_NewAdd();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String type = Util.trim(params.getString("type", ""));
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> src_list = eloandbBASEService.findL180M01B(
				mainId, type);

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String unitCtlType = "";

		// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
		Map<String, String> ctlTypeMap = new LinkedHashMap<String, String>();
		List<CodeType> codeTypelist = codeTypeService.findByCodeTypeList(
				"lms1815m01_elfCtlType", LocaleContextHolder.getLocale().toString());
		if (CollectionUtils.isNotEmpty(list)) {
			for (CodeType ct : codeTypelist) {
				if (retrialService.chkCtlTypeByBrNo(user.getUnitNo(),
						ct.getCodeValue())) {
					ctlTypeMap.put(ct.getCodeValue(), ct.getCodeDesc());
				}
			}
		}

		if (ctlTypeMap.size() == 1) {
			String cltType = Util
					.trim((String) ctlTypeMap.keySet().toArray()[0]);
			unitCtlType = cltType;
		} else {
			unitCtlType = "Z";
		}

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		// J-109-0313 小規模覆審
		String[] colArr = new String[] { "oid", "mainId", "coMainId",
				"elfCName", "elfMainCust", "elfMDFlag", "elfDBUOBU",
				"elfRCkdLine", "newNCkdFlag", "createBY", "newBy170M01",
				"showDocStatus1", "showLRDate", "ctlType", "sbScore",
                "elfRandomType" };
		for (Map<String, Object> src_row : src_list) {

			// J-110-0396 配合授審處，E-Loan企金授信覆審系統修改每月需覆審名單報表，優化由系統排除免覆審名單等事項。
			String newNCkdFlag = Util.nullToSpace(MapUtils.getString(src_row, "newNCkdFlag"));
			if(Util.equals(type, "Y") && Util.isNotEmpty(newNCkdFlag)){
				continue;
			}
			if(Util.equals(type, "N") && Util.isEmpty(newNCkdFlag)){
				continue;
			}

			Map<String, Object> row = new HashMap<String, Object>();
			for (String col : colArr) {
				row.put(col, Util.trim(MapUtils.getString(src_row, col)));
			}
			row.put("projectSeq",
					NumConverter.addComma(Util.trim(MapUtils.getString(src_row,
							"projectSeq")), "000"));
			row.put("custId", Util.trim(MapUtils.getString(src_row, "custId"))
					+ Util.trim(MapUtils.getString(src_row, "dupNo")));
			row.put("elfNewAddDesc", LMSUtil.getDesc(map_lrs_NewAdd,
					Util.trim(MapUtils.getString(src_row, "elfNewAdd"))));
			row.put("elfNewDate", LrsUtil.toStrYM(LrsUtil
					.model_elfNewDate_to_Date(Util.trim(MapUtils.getString(
							src_row, "elfNewDate")))));

			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			String realCkFg = Util.trim(MapUtils.getString(src_row,
					"elfRealCkFg"));
			String realDt = "";
			if (Util.equals(
					Util.trim(MapUtils.getString(src_row, "elfRealCkFg")), "Y")) {

				if (Util.notEquals(
						Util.trim(MapUtils.getString(src_row, "elfRealDt")), "")) {

					realDt = Util
							.trim(MapUtils.getString(src_row, "elfRealDt"));
				}

			}

			row.put("realCkFg", realCkFg);
			row.put("realDt", realDt);

			row.put("unitCtlType", unitCtlType);

			// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
			row.put("elfIsRescueDesc", LMSUtil.getDesc(yes_noMap(),
					Util.trim(MapUtils.getString(src_row, "elfIsRescue"))));
			row.put("elfGuarFlagDesc", LMSUtil.getDesc(yes_noMap(),
					Util.trim(MapUtils.getString(src_row, "elfGuarFlag"))));
			row.put("elfNewRescueDesc", LMSUtil.getDesc(yes_noMap(),
					Util.trim(MapUtils.getString(src_row, "elfNewRescue"))));
			row.put("elfNewRescueYM", LrsUtil.toStrYM(LrsUtil
					.model_elfNewDate_to_Date(Util.trim(MapUtils.getString(
							src_row, "elfNewRescueYM")))));
			// J-109-0313 小規模覆審
			String isSmallBuss = Util.trim(MapUtils.getString(src_row,
					"isSmallBuss"));
			row.put("smallBussDesc",
					Util.equals(isSmallBuss, "Y") ? LMSUtil.getDesc(
							yes_noMap(), isSmallBuss) : "");
			row.put("showDocStatus1Val",
					Util.trim(MapUtils.getString(src_row, "showDocStatus1")));

			list.add(row);
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	private Map<String, String> _rptNoMap() {
		Map<String, String> m = new HashMap<String, String>();
		m.put("R12", "額度明細表");
		m.put("R13", "額度批覆表");
		return m;
	}

	public CapMapGridResult queryL140M01A(ISearch pageSetting,
			PageParameters params) throws CapException {

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String parStrArr = Util.trim(params.getString("parStrArr"));
		if (Util.isNotEmpty(parStrArr)) {
			Map<String, String> rptDescMap = _rptNoMap();

			Map<String, L180M01B> oid_l180m01b_map = new HashMap<String, L180M01B>();
			Map<String, L140M01A> oid_l140m01a_map = new HashMap<String, L140M01A>();
			if (true) {
				List<String> oid_list_l180m01b = new ArrayList<String>();
				List<String> oid_list_l140m01a = new ArrayList<String>();
				for (String parStr : parStrArr.split("\\|")) {
					String[] parArr = Util.trim(parStr).split("\\^");
					String l180m01b_oid = parArr[0];
					String l140m01a_oid = parArr[2];
					// ~~~
					oid_list_l180m01b.add(l180m01b_oid);
					oid_list_l140m01a.add(l140m01a_oid);
				}
				if (oid_list_l180m01b.size() > 0) {
					for (L180M01B l180m01b : retrialService
							.findL180M01B_oid(oid_list_l180m01b)) {
						oid_l180m01b_map.put(l180m01b.getOid(), l180m01b);
					}
				}
				if (oid_list_l140m01a.size() > 0) {
					for (L140M01A l140m01a : retrialService
							.findL140M01A_oid(oid_list_l140m01a)) {
						oid_l140m01a_map.put(l140m01a.getOid(), l140m01a);
					}
				}
			}

			for (String parStr : parStrArr.split("\\|")) {
				String[] parArr = Util.trim(parStr).split("\\^");
				String l180m01b_oid = parArr[0];
				String rptNo = parArr[1];
				String l140m01a_oid = parArr[2];

				L180M01B l180m01b = oid_l180m01b_map.get(l180m01b_oid);
				L140M01A l140m01a = oid_l140m01a_map.get(l140m01a_oid);

				Map<String, Object> row = new HashMap<String, Object>();

				row.put("rptNo", rptNo);
				row.put("rptNoDesc", LMSUtil.getDesc(rptDescMap, rptNo));
				row.put("custId", l180m01b.getCustId());
				row.put("dupNo", l180m01b.getDupNo());
				row.put("cName", l180m01b.getElfCName());

				row.put("oid", l140m01a.getOid());
				row.put("cntrNo", l140m01a.getCntrNo());
				row.put("cntrCustid", l140m01a.getCustId());
				row.put("cntrDupno", l140m01a.getDupNo());
				row.put("cntrCName", l140m01a.getCustName());
				// ---
				list.add(row);
			}
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapMapGridResult queryNckdFlagO(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = Util.trim(params.getString("mainOid"));
		L180M01A meta = retrialService.findL180M01A_oid(mainOid);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId",
				meta.getBranchId());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l180a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				Util.trim(meta.getOwnBrId()));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				RetrialDocStatusEnum.已產生覆審名單報告檔.getCode());

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		@SuppressWarnings("rawtypes")
		Page src_page = retrialService.findPage(L180M01A.class, pageSetting);
		@SuppressWarnings("unchecked")
		List<L180M01A> src_list = src_page.getContent();

		String show_branchId = meta.getBranchId() + " "
				+ branchService.getBranchName(meta.getBranchId());
		for (L180M01A l180m01a : src_list) {

			Map<String, Object> row = new HashMap<String, Object>();
			if (Util.equals(meta.getOid(), l180m01a.getOid())) {
				continue;
			}
			LMSUtil.meta_to_map(row, l180m01a, new String[] { "branchId",
					"dataDate", "defaultCTLDate", "oid", "mainId" });

			row.put("show_branchId", show_branchId);
			row.put("dataSrc", "LMS");
			// ---
			list.add(row);
		}

		boolean ctlTypeA = retrialService.chkCtlTypeByBrNo(user.getUnitNo(),
				LrsUtil.CTLTYPE_主辦覆審);

		boolean ctlTypeB = retrialService.chkCtlTypeByBrNo(user.getUnitNo(),
				LrsUtil.CTLTYPE_自辦覆審);

		// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
		// if ((ctlTypeA && ctlTypeB) || ctlTypeA) {
		if (ctlTypeA) {

			if (list.size() == 0) {
				Set<String> existRPTDOCID = new HashSet<String>();
				for (Map<String, Object> elf493Map : misdbBASEService
						.findELF493_docIdList(meta.getBranchId())) {

					Map<String, Object> row = new HashMap<String, Object>();
					String _ELF493_BRANCH = Util.trim(MapUtils.getString(
							elf493Map, "ELF493_BRANCH"));
					Date _ELF493_DFCTLDT = (Date) MapUtils.getObject(elf493Map,
							"ELF493_DFCTLDT");
					String _ELF493_DATADTY = Util.trim(MapUtils.getString(
							elf493Map, "ELF493_DATADTY"));
					String _ELF493_DATADTM = Util.trim(MapUtils.getString(
							elf493Map, "ELF493_DATADTM"));
					String _ELF493_RPTDOCID = Util.trim(MapUtils.getString(
							elf493Map, "ELF493_RPTDOCID"));
					if (existRPTDOCID.contains(_ELF493_RPTDOCID)) {
						continue;
					} else {
						existRPTDOCID.add(_ELF493_RPTDOCID);
					}
					// 抓ELF493的資料
					row.put("branchId", _ELF493_BRANCH);
					row.put("dataDate", _ELF493_DATADTY + "-" + _ELF493_DATADTM);
					row.put("defaultCTLDate",
							Util.trim(TWNDate.toAD(_ELF493_DFCTLDT)));
					row.put("oid", _ELF493_RPTDOCID);
					row.put("mainId", _ELF493_RPTDOCID);
					row.put("show_branchId", show_branchId);
					row.put("dataSrc", "MIS");
					// ---
					list.add(row);
				}
			}
		}

		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	private Map<String, String> yes_noMap() {
		Map<String, String> m = new HashMap<String, String>();
		Properties prop_abstractEloan = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		m.put("Y", prop_abstractEloan.getProperty("yes"));
		m.put("N", prop_abstractEloan.getProperty("no"));
		return m;
	}
}
