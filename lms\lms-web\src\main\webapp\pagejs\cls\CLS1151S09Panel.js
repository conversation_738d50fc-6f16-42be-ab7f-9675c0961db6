var PanelAction09 = {
    isInit: false,
    /**
     *頁面初始化的動作 只執行一次
     * */
    initAction: function(){
        this.afterSetFormDataAction();
        this.initl140s11aForm();
    },
    /**
     *設定完值後載入頁面後的動作
     * */
    afterSetFormDataAction: function(){
        if($('#pageNum9').val() == '0'){
			$('#itemDscr9').attr('distanceWord','44');
		}else{
			$('#itemDscr9').attr('distanceWord','53');
		}
    },
    /**
     * J-112-0451 新增敘做條件異動比較表
     */
    initl140s11aForm: function(){
    	PanelAction09.initL140s11aGrid();
    	// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
        if (_M.itemType != "1") {
            $("#btnOpenL140s11a").hide();
            $("#addL140s11a").hide();
            $("#deleteL140s11a").hide();
        }
        if(_M.isReadOnly){//可讀
            $("#btnPreviewL140s11a").show();
        }
        //開啟分項表格
        $("#btnOpenL140s11a").click(function(){
            var thickButtons = {
                "close": function(){
                    $.thickbox.close();
                }
            }

            $("#l140s11aThickbox").thickbox({
                title: i18n.cls1151s01["btn.openL140s11a"],
                width: 800,
                height: 300,
                align: "center",
                valign: "bottom",
                buttons: thickButtons
            });

            $("#l140s11aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
                postData : {
                    formAction: "queryL140s11aList",
                    tabFormMainId: _M.tabMainId
                },
                search : true
            }).trigger("reloadGrid");
        });
        //預覽分項表格編輯之敘做案件異動比較表
        $("#btnPreviewL140s11a").click(function(){
            var previewL140s11aBox = $("#previewL140s11aBox");
            var previewL140s11aSpan = $("#previewL140s11aSpan");
            //J-113-0241 ELOAN-額度明細表-敘做條件異動情形-開啟分項表格編輯功能調整
            previewL140s11aBox.thickbox({
                title: i18n.cls1151s01["btn.previewL140s11a"],
                align: "center",
                valign: "bottom",
                width: 700,
                height: 500,
                buttons: {
                    "close": function(){
                        $.thickbox.close();
                    }
                },
                open: function(){
                	$.ajax({
                        handler: "cls1151m01formhandler",   //inits.fhandle,
                        action: "previewL140s11a",
                        data: {
                            tabFormMainId: _M.tabMainId
                        },
                        success: function(obj){
                            // 弱掃高風險 previewL140s11aSpan.html(obj.previewL140s11aStr);
                            previewL140s11aSpan.injectData({'previewL140s11aSpan':obj.previewL140s11aStr});
                        }
                    });
                }
            });
        });
        //新增
        $("#addL140s11a").click(function(){
        	PanelAction09.openL140S11aDetailBox(null, "add", null);
        });
        //刪除
        $("#deleteL140s11a").click(function(){
            var row = $("#l140s11aGrid").getGridParam('selrow');

            if (!row) {
                return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            }
            else {
                // confirmDelete=是否確定刪除?
                CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                    if (b) {
                        var data = $("#l140s11aGrid").getRowData(row);
                        var oid = data.oid;
                        $.ajax({
                            type: "POST",
                            handler: "cls1151m01formhandler",
                            data: {
                                formAction: "deleteL140s11a",
                                oid: oid
                            },
                            success: function(responseData){
                                $("#l140s11aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
                                    postData : {
                                        formAction: "queryL140s11aList",
                                        tabFormMainId: _M.tabMainId
                                    },
                                    search : true
                                }).trigger("reloadGrid");
                            }
                        });
                    }
                });
            }
        });
        //向上移動
        $("#upL140s11aSeq").click(function(){
        	PanelAction09.L140S11aUpDownBox(true);
        });
        //向下移動
        $("#downL140s11aSeq").click(function(){
        	PanelAction09.L140S11aUpDownBox(false);
        });
    },
    initL140s11aGrid: function(){
        $("#l140s11aGrid").iGrid({
            handler: "lms1401gridhandler",
            needPager: false,
            height: 100,
            sortname: 'seqNum|createTime',
            sortorder: 'asc|asc',
            postData: {
                formAction: "queryL140s11aList",
                tabFormMainId: _M.tabMainId
            },
            loadComplete: function(){
                $('#l140s11aGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.cls1151s01["L140S11A.seqNum"],
                name: 'seqNum',
                width: 15,
                sortable : false,
                align: "center",
                hidden: true
            },{
                colHeader: i18n.cls1151s01["L140S11A.applyItem"],
                name: 'applyItem',
                width: 100,
                sortable : false,
                align: "center"
            },{
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = $("#l140s11aGrid").getRowData(rowid);
                PanelAction09.openL140S11aDetailBox(null, null, data);
            }
        }).trigger("reloadGrid");
    },
    openL140S11aDetailBox: function(cellvalue, options, data){
        var l140s11aFormDetail = $("#l140s11aFormDetail");
        var l140s11aDetailThickbox = $("#l140s11aDetailThickbox");
        l140s11aFormDetail.find("textarea").attr("readonly", false).val("");

        // 當新增時 data 會是 null
        var hasData = false;
        if (data != 'undefined' && data != null && data != 0) {
            hasData = true;
        }
        //J-113-0241 ELOAN-額度明細表-敘做條件異動情形-開啟分項表格編輯功能調整
        var buttons = {
        	"saveData": function(){
            	$.ajax({
                	handler: "cls1151m01formhandler",  //inits.fhandle,
                	action: "saveL140s11a",
                	data: $.extend(l140s11aFormDetail.serializeData(), {
                    	oid: (hasData ? data.oid : ""),    // $("#l140s11aOid").val()
                        tabFormMainId: _M.tabMainId
                    }),
                    success: function(obj){
                    	$("#l140s11aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
                         	postData : {
                            	formAction: "queryL140s11aList",
                            	tabFormMainId: _M.tabMainId
                            },
                            search : true
                        }).trigger("reloadGrid");
                        //saveSuccess=儲存成功
                        CommonAPI.confirmMessage(i18n.def["saveSuccess"], function(b){
                        	if (b) {
                            	$.thickbox.close();
                            }
                        });
                    }
                });
            }
         }
         if (_M.isReadOnly || _M.itemType != "1") {
        	 delete buttons.saveData;
        	 l140s11aFormDetail.find("textarea").attr("readonly", true);
         }
         
        l140s11aDetailThickbox.thickbox({
        	title: "",
        	width: 1250,
        	height: 600,
        	modal: false,   // 會有X關閉視窗
        	i18n: i18n.def,
        	align: "center",
        	valign: "bottom",
        	buttons: buttons,
        	open: function(){
        		//前准
        		if(!$.isEmptyObject(CKEDITOR.instances["befApply"])){
                	CKEDITOR.instances["befApply"].resize(360, 400);
                }
                //本次申請
                if(!$.isEmptyObject(CKEDITOR.instances["aftApply"])){
                	CKEDITOR.instances["aftApply"].resize(360, 400);
                }
                //備註
                if(!$.isEmptyObject(CKEDITOR.instances["applyRemark"])){
                    CKEDITOR.instances["applyRemark"].resize(360, 400);
                }
                $.ajax({
                	handler: "cls1151m01formhandler",  //inits.fhandle,
                    action: "queryL140s11aDetail",
                    data: {
                        oid: (hasData ? data.oid : "")
                    },
                    success: function(obj){
                    	l140s11aFormDetail.injectData(obj);
                    }
                });
        	}
        });
    },
    //敘做條件異動情形 > 分項表格向上或向下移動
    L140S11aUpDownBox: function(upDown){
    	//向上=true 向下=false
    	var row = $("#l140s11aGrid").getGridParam('selrow');
        if (!row || row == "") {
            return CommonAPI.showMessage(i18n.def["grid.selrow"])
        }  
        var data;
        if (row != 'undefined' && row != null) {
            data = $("#l140s11aGrid").getRowData(row);
        }
        //更新排序
        $.ajax({
            type: "POST",
            handler: "cls1151m01formhandler",
            data: {
                formAction: "changel140s11aSeqNum",
                detailOid: data.oid,
                tabFormMainId: _M.tabMainId,
                upOrDown: upDown
            },
            success: function(){ 
                $("#l140s11aGrid").trigger("reloadGrid");
            }
        });
    },
    
};

_M.pageInitAcion["09"] = PanelAction09;
