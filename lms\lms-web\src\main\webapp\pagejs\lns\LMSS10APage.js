var initS10aJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金
			this.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金
			this.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			this.handlerName = "lms1111formhandler";
		}else{
			this.handlerName = "lms1301formhandler";
		}		
	},
	// 設定附加檔案內容
	fileSet : function(upFileId, delFileId, fieldId, fileGridId){
		//上傳檔案按鈕
		$("#" + upFileId).click(function(){
			var limitFileSize=9437103;
			MegaApi.uploadDialog({
				fieldId:fieldId,
	            fieldIdHtml:"size='30'",
	            fileDescId:"fileDesc",
	            fileDescHtml:"size='30' maxlength='30'",
				subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
				limitSize:limitFileSize,
	            width:320,
	            height:190,			
				data:{
					mainId:$("#mainId").val()				
				},
				success : function(obj) {
					$("#" + fileGridId).trigger("reloadGrid");
				}
		   });
		});
		
		//刪除檔案按鈕
		$("#" + delFileId).click(function(){
			var select  = $("#" + fileGridId).getGridParam('selrow');		
			// confirmDelete=是否確定刪除?
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){				
					var data = $("#" + fileGridId).getRowData(select);
					if(data.oid == "" || data.oid == undefined || data.oid == null){		
						// TMMDeleteError=請先選擇需修改(刪除)之資料列
						CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
						return;
					}				
					$.ajax({
						handler : (this.handlerName == null) ? "lms1201formhandler" : this.handlerName,
						type : "POST",
						dataType : "json",
						data : {
							formAction : "deleteUploadFile",
							fileOid:data.oid
						},
						success : function(obj) {
							$("#" + fileGridId).trigger("reloadGrid");
						}
					});
				}else{
					return ;
				}
			});
		});		
	},
	// 設定附加檔案Grid
	fileGrid : function(fileGridId, fieldId){
		//檔案上傳grid
		$("#" + fileGridId).iGrid({
			handler : 'lms1201gridhandler',
			height : 150,
			sortname : 'srcFileName',
			postData : {
				formAction : "queryfile",
				fieldId:fieldId,
				mainId:responseJSON.mainId
			},
			rowNum : 15,
			caption: "&nbsp;",
			hiddengrid : false,
			//expandOnLoad : true,	//只對subgrid有用
			//multiselect : true,
			colModel : [ {
				colHeader :(responseJSON.docURL == "/lms/lms1301m01")? i18n.lmss10['l120m01a.srcFileName'] : i18n.lmss10a['l120m01a.srcFileName'],//原始檔案名稱,
				name : 'srcFileName',
				width : 120,
				align: "left",
				sortable : false,
				formatter : 'click',
				onclick : openDoc
			}, {
				colHeader :(responseJSON.docURL == "/lms/lms1301m01")? i18n.lmss10['l120m01a.fileDesc'] : i18n.lmss10a['l120m01a.fileDesc'],//檔案說明
				name : 'fileDesc',
				width : 140,
				sortable : false
			}, {
				colHeader :(responseJSON.docURL == "/lms/lms1301m01")? i18n.lmss10['l120m01a.uploadTime'] : i18n.lmss10a['l120m01a.uploadTime'],//上傳時間
				name : 'uploadTime',
				width : 140,
				sortable : false
			}, {
				name : 'oid',
				hidden : true
			}]
		});		
	}
};

$(document).ready(function() {
	setCloseConfirm(true);
	// 設定handler名稱
	initS10aJson.setHandler();

	// JSON說: 國內附加檔案只會有一個 at 2012/10/03
	// 設定附加檔案內容
	initS10aJson.fileSet("uploadFile", "deleteFile", "upFile1", "gridfile");
	//initS10aJson.fileSet("uploadFile2", "deleteFile2", "upFile2", "gridfile2");
	//initS10aJson.fileSet("uploadFile3", "deleteFile3", "upFile3", "gridfile3");

	// 設定附加檔案Grid
	initS10aJson.fileGrid("gridfile", "upFile1");
	//initS10aJson.fileGrid("gridfile2", "upFile2");
	//initS10aJson.fileGrid("gridfile3", "upFile3");
	
	// 把多餘的file內容隱藏
	$("#filehide1").hide();
	$("#filehide2").hide();
});

function openDoc(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}