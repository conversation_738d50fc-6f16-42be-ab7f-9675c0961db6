initDfd.done(function(){
    var grid = $("#gridview_A-1-9-1").iGrid({
        handler: 'cls1141gridhandler',
        // height: 345, //for 15 筆
        // height: 230, //for 10 筆(有垂直捲軸)
        height: "230px", // for 10 筆(無垂直捲軸)
        //autoHeight: true,
        sortname: 'cntrNo',
        postData: {
            formAction: "queryL120s03a",
            mainId: responseJSON.mainid,
            rowNum: 10
        },
        //width : "910",
        width: "847",
        shrinkToFit: true,
        autowidth: false,
        hideHeader: true,
        //		caption: "&nbsp;",
        //		hiddengrid : false,
        colModel: [{
            // colHeader: "額度序號",
            name: 'cntrNo',
            align: "left",
            // width: 72,
            //width: 120,
			width: 100,
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            // colHeader: "非信保",
            name: 'crdFlag',
            align: "center",
            width: 50,
            sortable: true
        }, {
            // colHeader: "H.原始申請額度",
            name: 'applyAmt',
            align: "right",
            // width: 80,
            width: 70,
            sortable: true,
            formatter: function(data){
                if (data == null) {
                    return "";
                }
                else {
                    // 加入撇節符號
                    return util.addComma(data);
                }
            }
        }, {
            // colHeader: "A.授信額度",
            name: 'fcltAmt',
            align: "right",
            // width: 80,
            width: 70,
            sortable: true,
            formatter: function(data){
                if (data == null) {
                    return "";
                }
                else {
                    // 加入撇節符號
                    return util.addComma(data);
                }
            }
        }, {
            // colHeader: "B.合格擔保品扺減額",
            name: 'crdRatio',
            align: "right",
            // width: 81,
            width: 70,
            sortable: true,
            formatter: kindFormat
		}, {
			//J-104-0084-001  Web e-Loan授信系統修改風險權數計算明細表，就信保基金保證之授信案，風險權數改為得由經辦修改
            // colHeader: "B'.風險權數",
            name: 'crdRskRatio',
            align: "right",
            // width: 81,
            width: 70,
            sortable: true,
            formatter: function(data){
                if (data == null) {
                    return "";
                }
                else {
                    return data + "%";
                }
            }		
        }, {
            // colHeader: "C：風險權數<br/>(保證人請註記)%",
            name: 'rskMega',
            align: "right",
            width: 70,
            sortable: true,
            formatter: kindFormat2
        }, {
            // colHeader: "－",
            name: 'rskCrd',
            align: "right",
            // width: 82,
            width: 70,
            sortable: true
        }, {
            // colHeader: "D：(A-B) &times; C<br/>風險抵減<br/>後暴險額",
            name: 'rskAmt2',
            align: "right",
            // width: 84,
            width: 80,
            sortable: true,
            formatter: function(data){
                if (data == null) {
                    return "";
                }
                else {
                    // 加入撇節符號
                    return util.addComma(data);
                }
            }
        }, {
            // colHeader: "D-1：抵減後<br/>風險權數<br/>D &divide; A",
            name: 'rskr2',
            align: "right",
            width: 70,
            sortable: true,
            formatter: function(data){
                if (data == null) {
                    return "";
                }
                else {
                    return data + "%";
                }
            }
        }, {
            // colHeader: "E：D &times; 10%<br/>資本使用額<br/>(免填)",
            name: 'camt2',
            align: "right",
            width: 70,
            sortable: true,
            hidden: true
        }, {
            // colHeader: "F：D(信保為C) &times; 0.007%<br/>&divide;
            // 1,000,000<br/>占資本適足率",
            name: 'bisr2',
            align: "right",
            // width: 87,
            width: 80,
            sortable: true,
            hidden: true,
            formatter: 'currency',
            formatoptions: {
                decimalPlaces: 5,//小數點到第幾位
                suffix: "<br>/10000"
            }
        }, {
            // colHeader: "G：D &times; 0.1%<br/>&divide; 本額度<br/>資金成本率",
            name: 'costr2',
            align: "right",
            // width: 82,
            width: 70,
            sortable: true,
            hidden: true,
            formatter: 'currency',
            formatoptions: {
                decimalPlaces: 4,//小數點到第幾位
                suffix: "%"
            }
        }, {
            colHeader: "&nbsp",//"檢核欄位",
            name: 'chkYN',
            width: 15,
            sortable: true,
            align: "center"
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'collAmt',
            hidden: true
        }, {
            name: 'rskRatio',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = grid.getRowData(rowid);
            //addBis();
            openDoc(null, null, data);
        }
    });
    function kindFormat(cellvalue, options, rowObject){
        if (rowObject[1] == i18n.clss07a["L1205S07.index2"]) {
            if (rowObject[15] == null) {
                return "";
            }
            else {
                if (rowObject[15] == null) {
                    return "";
                }
                else {
                    // 加入撇節符號
                    return util.addComma(rowObject[15]);
                }
            }
        }
        else {
            return cellvalue + '%';
        }
    }
    function kindFormat2(cellvalue, options, rowObject){
        if (rowObject[1] == i18n.clss07a["L1205S07.index2"]) {
            if (rowObject[16] == null) {
                return "";
            }
            else {
                return rowObject[16] + '%';
            }
        }
        else {
            return cellvalue;
        }
    }
	
	$("#openTablesSay").click(function(){
        $("#openTablesSayBox").thickbox({
            title: i18n.def.confirmTitle,
            width: 800,
            height: 450,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
	
    $("#btnDeleteBis").click(function(){
        var selrow = grid.getGridParam('selrow');
        if (selrow) {
            var ret = grid.getRowData(selrow);
            API.flowConfirmAction({
                message: i18n.def["action_003"],
                handler: responseJSON["handler"],
                action: "deleteBis",
                data: {
                    deleteOid: ret.oid
                },
                success: function(){
                    grid.trigger("reloadGrid");
                    
                }
            });
        }
    });
	
    $("#openTablesCompareLink").click(function(){
        $("#openTablesCompareBox").thickbox({
            title: i18n.def.confirmTitle,
            width: 800,
            height: 300,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
	$("#crdFlag").change(function(){
		var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
		$tLMS1205S07Form02.find("input[type='hidden']").remove();
		if($(this).val()=="2"){
			$tLMS1205S07Form02.find("#crdRatio").attr({id: "collAmt", name: "collAmt"});
			$tLMS1205S07Form02.find("#rskMega").attr({id: "rskRatio", name: "rskRatio"});
			$tLMS1205S07Form02.find("#rskCrd").val("");
			
			//J-104-0084-001  Web e-Loan授信系統修改風險權數計算明細表，就信保基金保證之授信案，風險權數改為得由經辦修改
			$tLMS1205S07Form02.find("#crdRskRatio").val("");
			
			$tLMS1205S07Form02.find("#rskAmt2").attr({id: "rskAmt1", name: "rskAmt1"});
			$tLMS1205S07Form02.find("#rskr2").attr({id: "rskr1", name: "rskr1"});
			$tLMS1205S07Form02.find("#camt2").attr({id: "camt1", name: "camt1"});
			$tLMS1205S07Form02.find("#bisr2").attr({id: "bisr1", name: "bisr1"});
			$tLMS1205S07Form02.find("#costr2").attr({id: "costr1", name: "costr1"});
		}else{
			$tLMS1205S07Form02.find("#collAmt").attr({id: "crdRatio", name: "crdRatio"});
			$tLMS1205S07Form02.find("#rskRatio").attr({id: "rskMega", name: "rskMega"});
			$tLMS1205S07Form02.find("#rskCrd").val("");
			
			//J-104-0084-001  Web e-Loan授信系統修改風險權數計算明細表，就信保基金保證之授信案，風險權數改為得由經辦修改
			$tLMS1205S07Form02.find("#crdRskRatio").val(100);
			
			$tLMS1205S07Form02.find("#rskAmt1").attr({id: "rskAmt2", name: "rskAmt2"});
			$tLMS1205S07Form02.find("#rskr1").attr({id: "rskr2", name: "rskr2"});
			$tLMS1205S07Form02.find("#camt1").attr({id: "camt2", name: "camt2"});
			$tLMS1205S07Form02.find("#bisr1").attr({id: "bisr2", name: "bisr2"});
			$tLMS1205S07Form02.find("#costr1").attr({id: "costr2", name: "costr2"});
		}
	})
});

