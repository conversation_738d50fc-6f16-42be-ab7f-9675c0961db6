/* 
 * ELF431.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.GenericBean;

/** 優惠房貸額度控管檔 **/

public class ELF431 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Column(name="ELF431_BRNO", length=3, columnDefinition="CHAR(03)",unique = true)
	private String elf431_brno;

	/** 客戶統編 **/
	@Column(name="ELF431_CUSTID", length=10, columnDefinition="CHAR(10)",unique = true)
	private String elf431_custid;

	/** 重複序號 **/
	@Column(name="ELF431_DUPNO", length=1, columnDefinition="CHAR(01)",unique = true)
	private String elf431_dupno;

	/** 
	 * 文件ID<p/>
	 * 審核書之UNIDDOCID
	 */
	@Column(name="ELF431_UNDOCID", length=32, columnDefinition="CHAR(32)")
	private String elf431_undocid;

	/** 
	 * 地區別<p/>
	 * 審核書之LAREA
	 */
	@Column(name="ELF431_AREANO", length=8, columnDefinition="CHAR(08)")
	private String elf431_areano;

	/** 客戶姓名 **/
	@Column(name="ELF431_CNAME", length=20, columnDefinition="CHAR(20)")
	private String elf431_cname;

	/** 額度申請日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF431_APPDATE", columnDefinition="DATE")
	private Date elf431_appdate;

	/** 
	 * 受理額度<p/>
	 * 單位：新台幣元
	 */
	@Column(name="ELF431_APPMONEY", columnDefinition="DECIMAL(9)")
	private BigDecimal elf431_appmoney;

	/** 
	 * 優惠額度(登記金額?)<p/>
	 * 單位：新台幣元
	 */
	@Column(name="ELF431_FAVLOAN", columnDefinition="DECIMAL(9)")
	private BigDecimal elf431_favloan;

	/** 
	 * 新/中古屋<p/>
	 * N：新屋<br/>
	 *  O：中古屋
	 */
	@Column(name="ELF431_NOHOUSE", length=1, columnDefinition="CHAR(01)")
	private String elf431_nohouse;

	/** 
	 * 撥款日(ELOAN 撥款日)<p/>
	 * 審核書核准時上傳 9999-12-31<br/>
	 *  動審表撥款時上傳日期<br/>
	 *  個金部取消額度時<br/>
	 *  1111-01-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ELF431_APPRDATE", columnDefinition="DATE")
	private Date elf431_apprdate;

	/** 優惠房貸額度(動撥金額?) **/
	@Column(name="ELF431_FAVCNTNO", length=12, columnDefinition="CHAR(12)")
	private String elf431_favcntno;

	/** 一般房貸額度 **/
	@Column(name="ELF431_ORCNTNO1", length=12, columnDefinition="CHAR(12)")
	private String elf431_orcntno1;

	/** 
	 * 是否為轉貸戶<p/>
	 * ‘N’非轉貸戶
	 */
	@Column(name="ELF431_ORCNTNO2", length=12, columnDefinition="CHAR(12)")
	private String elf431_orcntno2;

	/** 資料修改人(行員代號) **/
	@Column(name="ELF431_UPDATER", length=8, columnDefinition="CHAR(08)")
	private String elf431_updater;

	/** 資料修改日期 **/
	@Column(name="ELF431_TMESTAMP", columnDefinition="TIMESTAMP")
	private Date elf431_tmestamp;

	/** 
	 * 類別<p/>
	 * 1：八千億優惠房貸<br/>
	 *  2：三千億優惠房貸<br/>
	 *  3：九十七年度二千億優惠房貸<br/>
	 *  4:青年安心成家(991126新增)
	 */
	@Column(name="ELF431_KINDNO", length=2, columnDefinition="CHAR(02)",unique = true)
	private String elf431_kindno;

	/** Aloan 撥款日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF431_LOANDATE", columnDefinition="DATE")
	private Date elf431_loandate;

	/** 優惠貸款額度序號 **/
	@Column(name="ELF431_CNTRNO", length=12, columnDefinition="CHAR(12)",unique = true)
	private String elf431_cntrno;

	/** 售屋者ID **/
	@Column(name="ELF431_SELLERID", length=11, columnDefinition="CHAR(11)")
	private String elf431_sellerid;

	/** 取得分行別 **/
	public String getElf431_brno() {
		return this.elf431_brno;
	}
	/** 設定分行別 **/
	public void setElf431_brno(String value) {
		this.elf431_brno = value;
	}

	/** 取得客戶統編 **/
	public String getElf431_custid() {
		return this.elf431_custid;
	}
	/** 設定客戶統編 **/
	public void setElf431_custid(String value) {
		this.elf431_custid = value;
	}

	/** 取得重複序號 **/
	public String getElf431_dupno() {
		return this.elf431_dupno;
	}
	/** 設定重複序號 **/
	public void setElf431_dupno(String value) {
		this.elf431_dupno = value;
	}

	/** 
	 * 取得文件ID<p/>
	 * 審核書之UNIDDOCID
	 */
	public String getElf431_undocid() {
		return this.elf431_undocid;
	}
	/**
	 *  設定文件ID<p/>
	 *  審核書之UNIDDOCID
	 **/
	public void setElf431_undocid(String value) {
		this.elf431_undocid = value;
	}

	/** 
	 * 取得地區別<p/>
	 * 審核書之LAREA
	 */
	public String getElf431_areano() {
		return this.elf431_areano;
	}
	/**
	 *  設定地區別<p/>
	 *  審核書之LAREA
	 **/
	public void setElf431_areano(String value) {
		this.elf431_areano = value;
	}

	/** 取得客戶姓名 **/
	public String getElf431_cname() {
		return this.elf431_cname;
	}
	/** 設定客戶姓名 **/
	public void setElf431_cname(String value) {
		this.elf431_cname = value;
	}

	/** 取得額度申請日 **/
	public Date getElf431_appdate() {
		return this.elf431_appdate;
	}
	/** 設定額度申請日 **/
	public void setElf431_appdate(Date value) {
		this.elf431_appdate = value;
	}

	/** 
	 * 取得受理額度<p/>
	 * 單位：新台幣元
	 */
	public BigDecimal getElf431_appmoney() {
		return this.elf431_appmoney;
	}
	/**
	 *  設定受理額度<p/>
	 *  單位：新台幣元
	 **/
	public void setElf431_appmoney(BigDecimal value) {
		this.elf431_appmoney = value;
	}

	/** 
	 * 取得優惠額度(登記金額?)<p/>
	 * 單位：新台幣元
	 */
	public BigDecimal getElf431_favloan() {
		return this.elf431_favloan;
	}
	/**
	 *  設定優惠額度(登記金額?)<p/>
	 *  單位：新台幣元
	 **/
	public void setElf431_favloan(BigDecimal value) {
		this.elf431_favloan = value;
	}

	/** 
	 * 取得新/中古屋<p/>
	 * N：新屋<br/>
	 *  O：中古屋
	 */
	public String getElf431_nohouse() {
		return this.elf431_nohouse;
	}
	/**
	 *  設定新/中古屋<p/>
	 *  N：新屋<br/>
	 *  O：中古屋
	 **/
	public void setElf431_nohouse(String value) {
		this.elf431_nohouse = value;
	}

	/** 
	 * 取得撥款日(ELOAN 撥款日)<p/>
	 * 審核書核准時上傳 9999-12-31<br/>
	 *  動審表撥款時上傳日期<br/>
	 *  個金部取消額度時<br/>
	 *  1111-01-01
	 */
	public Date getElf431_apprdate() {
		return this.elf431_apprdate;
	}
	/**
	 *  設定撥款日(ELOAN 撥款日)<p/>
	 *  審核書核准時上傳 9999-12-31<br/>
	 *  動審表撥款時上傳日期<br/>
	 *  個金部取消額度時<br/>
	 *  1111-01-01
	 **/
	public void setElf431_apprdate(Date value) {
		this.elf431_apprdate = value;
	}

	/** 取得優惠房貸額度(動撥金額?) **/
	public String getElf431_favcntno() {
		return this.elf431_favcntno;
	}
	/** 設定優惠房貸額度(動撥金額?) **/
	public void setElf431_favcntno(String value) {
		this.elf431_favcntno = value;
	}

	/** 取得一般房貸額度 **/
	public String getElf431_orcntno1() {
		return this.elf431_orcntno1;
	}
	/** 設定一般房貸額度 **/
	public void setElf431_orcntno1(String value) {
		this.elf431_orcntno1 = value;
	}

	/** 
	 * 取得是否為轉貸戶<p/>
	 * ‘N’非轉貸戶
	 */
	public String getElf431_orcntno2() {
		return this.elf431_orcntno2;
	}
	/**
	 *  設定是否為轉貸戶<p/>
	 *  ‘N’非轉貸戶
	 **/
	public void setElf431_orcntno2(String value) {
		this.elf431_orcntno2 = value;
	}

	/** 取得資料修改人(行員代號) **/
	public String getElf431_updater() {
		return this.elf431_updater;
	}
	/** 設定資料修改人(行員代號) **/
	public void setElf431_updater(String value) {
		this.elf431_updater = value;
	}

	/** 取得資料修改日期 **/
	public Date getElf431_tmestamp() {
		return this.elf431_tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setElf431_tmestamp(Date value) {
		this.elf431_tmestamp = value;
	}

	/** 
	 * 取得類別<p/>
	 * 1：八千億優惠房貸<br/>
	 *  2：三千億優惠房貸<br/>
	 *  3：九十七年度二千億優惠房貸<br/>
	 *  4:青年安心成家(991126新增)
	 */
	public String getElf431_kindno() {
		return this.elf431_kindno;
	}
	/**
	 *  設定類別<p/>
	 *  1：八千億優惠房貸<br/>
	 *  2：三千億優惠房貸<br/>
	 *  3：九十七年度二千億優惠房貸<br/>
	 *  4:青年安心成家(991126新增)
	 **/
	public void setElf431_kindno(String value) {
		this.elf431_kindno = value;
	}

	/** 取得Aloan 撥款日 **/
	public Date getElf431_loandate() {
		return this.elf431_loandate;
	}
	/** 設定Aloan 撥款日 **/
	public void setElf431_loandate(Date value) {
		this.elf431_loandate = value;
	}

	/** 取得優惠貸款額度序號 **/
	public String getElf431_cntrno() {
		return this.elf431_cntrno;
	}
	/** 設定優惠貸款額度序號 **/
	public void setElf431_cntrno(String value) {
		this.elf431_cntrno = value;
	}

	/** 取得售屋者ID **/
	public String getElf431_sellerid() {
		return this.elf431_sellerid;
	}
	/** 設定售屋者ID **/
	public void setElf431_sellerid(String value) {
		this.elf431_sellerid = value;
	}
}
