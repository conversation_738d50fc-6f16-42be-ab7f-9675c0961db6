package com.mega.eloan.lms.lrs.handler.form;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.gwclient.EloanBatchClient;
import com.mega.eloan.common.gwclient.EloanSubsysBatReqMessage;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lrs.constants.lrsConstants;
import com.mega.eloan.lms.lrs.flow.LMS1700Flow;
import com.mega.eloan.lms.lrs.pages.LMS1700M01Page;
import com.mega.eloan.lms.lrs.pages.LMS1700M02Page;
import com.mega.eloan.lms.lrs.pages.LMS1810M01Page;
import com.mega.eloan.lms.lrs.service.LMS1700Service;
import com.mega.eloan.lms.lrs.service.LMS1810Service;
import com.mega.eloan.lms.mfaloan.service.MisELF412Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01C;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L170M01G;
import com.mega.eloan.lms.model.L170M01H;
import com.mega.eloan.lms.model.L170M01J;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L181A01A;
import com.mega.eloan.lms.model.L181M01A;
import com.mega.eloan.lms.model.L181M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("lms1700m01formhandler")
@DomainClass(L170M01A.class)
public class LMS1700M01Formhandler extends AbstractFormHandler {
	private static final int MAXLEN_L170M01B_MAJORMEMO = StrUtils
			.getEntityFileldLegth(L170M01B.class, "majorMemo", 768);
	private static final int MAXLEN_L170M01D_CHKTEXT = StrUtils
			.getEntityFileldLegth(L170M01D.class, "chkText", 192);

	private static final String INPUT_CHKCHECK = "chkCheck";

	@Resource
	EloanBatchClient eloanBatchClient;

	@Resource
	RetrialService retrialService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branchService;

	@Resource
	FlowSimplifyService flowSimplifyService;

	@Resource
	TempDataService tempDataService;

	@Resource
	DocCheckService docCheckService;

	@Resource
	DocLogService docLogService;

	@Resource
	LMS1700Service lms1700Service;

	@Resource
	LMS1810Service lms1810Service;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	MisELF412Service misELF412Service;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	DocFileService docFileService;

	@Resource
	LMSService lmsService;

    @Resource
    SysParameterService sysParameterService;

	Properties prop_lms1810m01 = MessageBundleScriptCreator
			.getComponentResource(LMS1810M01Page.class);
	Properties prop_lms1700m01 = MessageBundleScriptCreator
			.getComponentResource(LMS1700M01Page.class);
	Properties prop_lms1700m02 = MessageBundleScriptCreator
			.getComponentResource(LMS1700M02Page.class);

	private CapAjaxFormResult defaultResult(PageParameters params,
			L170M01A meta, CapAjaxFormResult result) throws CapException {
		String branchName = meta == null ? "" : branchService
				.getBranchName(meta.getOwnBrId());
		if (true) {
			result.set("custId", meta.getCustId());
			result.set("dupNo", meta.getDupNo());
			// 若在 受檢端 處理中.EX: 編製中_分行端, 待覆核_分行端
			// 不應直接上傳 elf412
			boolean processingBranchComm = false;
			if (Util.equals(RetrialDocStatusEnum.編製中.getCode(),
					meta.getDocStatus())
					|| Util.equals(RetrialDocStatusEnum.待覆核.getCode(),
							meta.getDocStatus())) {
				processingBranchComm = true;
			}
			result.set("processingBranchComm", processingBranchComm ? "Y" : "N");
			result.set("is_flowClass_throughBr",
					retrialService.is_flowClass_throughBr(meta) ? "Y" : "N");
			result.set("isFromNotes", LrsUtil.isFromNotes(meta) ? "Y" : "N");
			result.set("rptId", meta.getRptId());
		}
		// required information
		result.set(EloanConstants.PAGE,
				Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));

		result.set("titleBr", meta.getOwnBrId() + " " + branchName);

		// J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		// L170M01A.realRpFgStrN=一般
		// L170M01A.realRpFg1=土建融實地
		// L180M01B.ctlType_B=董事會(或常董會)權限案件實地覆審
		// J-107-0254_09301_B1001 配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
		// L180M01B.ctlType_C=價金履約保證額度覆審
		String titleCtlType = "";
		if (Util.equals(Util.trim(meta.getCtlType()), LrsUtil.CTLTYPE_自辦覆審)) {
			titleCtlType = prop_lms1700m01.getProperty("L180M01B.ctlType_B");
		} else if (Util.equals(Util.trim(meta.getCtlType()),
				LrsUtil.CTLTYPE_主辦覆審)) {
			if (Util.equals(meta.getRealRpFg(), "Y")) {
				titleCtlType = prop_lms1700m01
						.getProperty("L170M01A.realRpFg1");
			} else {
				titleCtlType = prop_lms1700m01
						.getProperty("L170M01A.realRpFgStrN");
			}
		} else {
			titleCtlType = prop_lms1700m01.getProperty("L180M01B.ctlType_C");
		}
		result.set("titleCtlType", titleCtlType);
		result.set(
				"titleInfo",
				(LrsUtil.isFromNotes(meta) ? "(notes)" : "")
						+ (meta.getCustId() + " " + meta.getDupNo() + "  " + meta
								.getCustName()));
		if (true) {
			boolean isLock = false;
			Set<String> docstatus_lockSet = new HashSet<String>();
			{
				docstatus_lockSet.add(RetrialDocStatusEnum.待覆核.getCode());
			}

			if (docstatus_lockSet.contains(Util.trim(meta.getDocStatus()))) {
				isLock = true;
			}

			result.set("lock", isLock);
		}

		// 隱藏頁籤
		if (retrialService.hidePaFormPanel()) {
			result.set("hidePaFormPanel", "Y");
		} else {
			result.set("hidePaFormPanel", "N");
		}

		return result;
	}

	private String _id_name(String raw_id) {
		String id = Util.trim(raw_id);
		if (StringUtils.equalsIgnoreCase(id, "BATCH")) {
			return id;
		} else {
			return Util.trim(id + " "
					+ Util.trim(userInfoService.getUserName(id)));
		}
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L170M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL170M01A_oid(mainOid);

			String page = params.getString(EloanConstants.PAGE);
			if ("01".equals(page)) {
				if (true) {
					// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					LMSUtil.addMetaToResult(result, meta, new String[] {
							"ownBrId", "retrialDate", "lastRetrialDate",
							"projectNo", "typCd", "custName", "chairman",
							"tradeType", "randomCode", "realCkFg", "realDt",
							"realRpFg", "ctlType" });
				}

				if (true) {
					String creditType = "";
					String creditGrade = "";
					String excreditType = "";
					String excreditGrade = "";

					String mowType = "";
					String mowGrade = "";
					String exmowType = "";
					String exmowGrade = "";

					String fcrdType = "";
					String fcrdArea = "";
					String fcrdPred = "";
					String fcrdGrad = "";
					String exfcrdType = "";
					String exfcrdArea = "";
					String exfcrdPred = "";
					String exfcrdGrad = "";
					Map<String, List<L170M01E>> m_l170m01e = retrialService
							.findL170M01E_type(retrialService
									.findL170M01E(meta));
					if (true) {
						L170M01E l170m01e_C = LrsUtil.firstElm(
								m_l170m01e.get(LrsUtil.M01E_FLAG_C), "T");
						if (l170m01e_C != null) {
							creditType = l170m01e_C.getCrdType();
							creditGrade = Util.trim(l170m01e_C.getGrade());
						}
						// 前次信用評等
						L170M01E l170m01e_exC = LrsUtil.firstElm(
								m_l170m01e.get(LrsUtil.M01E_FLAG_C), "L");
						if (l170m01e_exC != null) {
							excreditType = l170m01e_exC.getCrdType();
							excreditGrade = Util.trim(l170m01e_exC.getGrade());
						}
					}
					if (true) {
						L170M01E l170m01e_M = LrsUtil.firstElm(
								m_l170m01e.get(LrsUtil.M01E_FLAG_M), "T");
						if (l170m01e_M != null) {
							mowType = l170m01e_M.getCrdType();
							mowGrade = Util.trim(l170m01e_M.getGrade());
						}
						// 前次信用風險內部評等
						L170M01E l170m01e_exM = LrsUtil.firstElm(
								m_l170m01e.get(LrsUtil.M01E_FLAG_M), "L");
						if (l170m01e_exM != null) {
							exmowType = l170m01e_exM.getCrdType();
							exmowGrade = Util.trim(l170m01e_exM.getGrade());
						}
					}
					if (true) {
						L170M01E l170m01e_F = LrsUtil.firstElm(
								m_l170m01e.get(LrsUtil.M01E_FLAG_F), "T");
						if (l170m01e_F != null) {
							fcrdType = l170m01e_F.getCrdType();
							fcrdArea = l170m01e_F.getFcrdArea();
							fcrdPred = l170m01e_F.getFcrdPred();
							fcrdGrad = retrialService.toStr(l170m01e_F);
						}
						// 前次外部評等類別
						L170M01E l170m01e_exF = LrsUtil.firstElm(
								m_l170m01e.get(LrsUtil.M01E_FLAG_F), "L");
						if (l170m01e_exF != null) {
							exfcrdType = l170m01e_exF.getCrdType();
							exfcrdArea = l170m01e_exF.getFcrdArea();
							exfcrdPred = l170m01e_exF.getFcrdPred();
							exfcrdGrad = retrialService.toStr(l170m01e_exF);
						}
					}

					// ----------------------------------
					result.set("CreditType", creditType);
					result.set("CreditGrade", creditGrade);
					result.set("exCreditType", excreditType);
					result.set("exCreditGrade", excreditGrade);

					result.set("MowType", mowType);
					result.set("MowGrade", mowGrade);
					result.set("exMowType", exmowType);
					result.set("exMowGrade", exmowGrade);

					result.set("FcrdType", fcrdType);
					result.set("FcrdArea", fcrdArea);
					result.set("FcrdPred", fcrdPred);
					result.set("FcrdGrad", fcrdGrad);
					result.set("exFcrdType", exfcrdType);
					result.set("exFcrdArea", exfcrdArea);
					result.set("exFcrdPred", exfcrdPred);
					result.set("exFcrdGrad", exfcrdGrad);

				}
				if (true) {
					List<L170M01H> l170m01h_list = retrialService
							.findL170M01H_fmtSYS_l170m01a(meta);
					boolean hasCntrNo = CollectionUtils
							.isNotEmpty(l170m01h_list);
					if (hasCntrNo == false) {
						hasCntrNo = CollectionUtils.isNotEmpty(retrialService
								.findL170M01B_orderBy(meta));
					}
					String rltGuarantor = Util.trim(LrsUtil.toStr_Guarantor(
							l170m01h_list, hasCntrNo));
					String rltBorrower = Util.trim(LrsUtil.toStr_Borrower(
							l170m01h_list, hasCntrNo));

					if (Util.isEmpty(rltGuarantor)) {
						rltGuarantor = prop_lms1700m01
								.getProperty("ui_lms1700.msg26");
					}
					if (Util.isEmpty(rltBorrower)) {
						rltBorrower = prop_lms1700m01
								.getProperty("ui_lms1700.msg26");
					}
					result.set("rltGuarantor", rltGuarantor);
					result.set("rltBorrower", rltBorrower);
				}
				if (true) {
					if (true) {
						result.set("freeG",
								Util.equals(meta.getFreeG(), "Y") ? "Y" : "N");
						result.set("freeC",
								Util.equals(meta.getFreeC(), "Y") ? "Y" : "N");
					}
					if (true) {
						L170M01H l170m01h_freeG = retrialService
								.findL170M01H_fmtFreeG_l170m01a(meta);
						L170M01H l170m01h_freeC = retrialService
								.findL170M01H_fmtFreeC_l170m01a(meta);
						result.set("rltGuarantor_free", Util
								.trim(l170m01h_freeG != null ? l170m01h_freeG
										.getCustName() : ""));
						result.set("rltBorrower_free", Util
								.trim(l170m01h_freeC != null ? l170m01h_freeC
										.getCustName() : ""));
					}
				}
				if (true) {
					String nckdFlag = Util.trim(meta.getNCkdFlag());
					String nckdInfo = "";
					if (Util.isNotEmpty(nckdFlag)) {
						nckdInfo = LMSUtil.getDesc(
								retrialService.get_lrs_NckdFlagMap(), nckdFlag);
					}
					result.set("nckdInfo", nckdInfo);
				}
				result.set("mLoanPerson",
						LrsUtil.decide_mLoanPerson(meta.getMLoanPerson()));
				result.set("mLoanPersonA",
						LrsUtil.decide_mLoanPerson(meta.getMLoanPersonA()));
				result.set("ownBrName",
						branchService.getBranchName(meta.getOwnBrId()));
				result.set("status",
						lms1700Service.l170m01a_docStatusDesc(meta));
				result.set("busCd_bussKind",
						LrsUtil.get_s01_busCd_bussKind(meta));
				if (true) {
					result.set("cesBizInfo", LrsUtil.get_s01_cesBizInfo(meta));
				}

				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime",
						Util.trim(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime",
						Util.trim(TWNDate.valueOf(meta.getUpdateTime())));
				// ---
				CapAjaxFormResult selItem = new CapAjaxFormResult();
				CapAjaxFormResult selItemOrder = new CapAjaxFormResult();
				if (true) {
					LinkedHashMap<String, String> typeCdMap = new LinkedHashMap<String, String>();
					typeCdMap.put("1", "DBU");
					typeCdMap.put("4", "OBU");
					_setMapWithOrder(selItem, "typCd", typeCdMap);
					_setMapWithOrder(selItem, "CreditType",
							retrialService.get_lrs_CrdtType_2_withXX());
					_setMapWithOrder(selItem, "CreditGrade",
							retrialService.get_lrs_CrdtTbl());
					_setMapWithOrder(selItem, "exCreditType",
							retrialService.get_lrs_exType("exC"));
					_setMapWithOrder(selItem, "exCreditGrade",
							retrialService.get_lrs_CrdtTbl());

					_setMapWithOrder(selItem, "MowType",
							retrialService.get_lrs_MowType_2_withXX());
					_setMapWithOrder(selItem, "MowGrade",
							retrialService.get_lrs_MowTbl());
					_setMapWithOrder(selItem, "exMowType",
							retrialService.get_lrs_exType("exM"));
					_setMapWithOrder(selItem, "exMowGrade",
							retrialService.get_lrs_MowTbl());

					_setMapWithOrder(selItem, "FcrdType",
							retrialService.get_lrs_FcrdType_2());
					_setMapWithOrder(selItem, "FcrdArea",
							retrialService.get_lrs_FcrdArea());
					_setMapWithOrder(selItem, "FcrdPred",
							retrialService.get_lrs_FcrdPred());
					_setMapWithOrder(selItem, "exFcrdType",
							retrialService.get_lrs_FcrdType_2());
					_setMapWithOrder(selItem, "exFcrdArea",
							retrialService.get_lrs_FcrdArea());
					_setMapWithOrder(selItem, "exFcrdPred",
							retrialService.get_lrs_FcrdPred());
				}
				result.set("selItem", selItem);
				result.set("selItemOrder", selItemOrder);
			} else if ("02".equals(page)) {
				_l170m01b_toResult(meta, result);
				L170M01A befMeta = retrialService.findL170M01A_bef(meta);
				result.set("bef_oid", befMeta == null ? "" : befMeta.getOid());
			} else if ("03".equals(page)) {
				L170M01C l170m01c = meta.getL170m01c();

				Map<String, String> finRatioMap = retrialService
						.get_lrs_FinRatio();
				_l170m01c_toResult(l170m01c, finRatioMap, result);
				// ---
				CapAjaxFormResult selItem = new CapAjaxFormResult();
				CapAjaxFormResult selItemOrder = new CapAjaxFormResult();
				if (true) {
					_setMapWithOrder(selItem, "finRatio", finRatioMap);
				}
				result.set("selItem", selItem);
				result.set("selItemOrder", selItemOrder);
			} else if ("04".equals(page)) {
				if (true) {// 組 item
					
					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					result.set("ctlType", Util.trim(meta.getCtlType()));

					HashMap<String, String> map_title = new HashMap<String, String>();
					if (true) {
						if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {
							map_title.put(LrsUtil.D_董事會或常董會權限, "");

						} else if (Util.equals(meta.getCtlType(),
								LrsUtil.CTLTYPE_價金履約)) {
							map_title.put(LrsUtil.A_徵信事項, "徵信事項");
							map_title.put(LrsUtil.B_債權確保, "債權確保");
							map_title.put(LrsUtil.C_其他, "糾紛案件之處理");
						} else {
							map_title.put(LrsUtil.A_徵信事項, "徵信事項");
							map_title.put(LrsUtil.B_債權確保, "債權確保");
							map_title.put(LrsUtil.C_其他, "其他");
							map_title.put(LrsUtil.Z_電腦建檔資料, "電腦建檔資料");
							map_title.put(LrsUtil.Y_履約條件, "履約條件");
							map_title.put(LrsUtil.X_土建融, "土建融"); // J-106-0145-001
																	// Web
																	// e-Loan
																	// 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
						}
						// ======
						result.set("l170m01d_title", new CapAjaxFormResult(
								map_title));
					}

					// ---
					{
						HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();

						List<L170M01D> list = retrialService
								.findL170M01D_orderBySeq(meta);
						for (String itemType : map_title.keySet()) {
							setL170M01D(map, itemType, list,
									Util.trim(meta.getCtlType()),meta);
						}
						result.set("l170m01d_list", new CapAjaxFormResult(map));
						// 指定 chkText 長度
						int chkText_maxlengthC = MAXLEN_L170M01D_CHKTEXT / 3;
						result.set("l170m01d_chkText_maxlength",
								MAXLEN_L170M01D_CHKTEXT);
						result.set("l170m01d_chkText_maxlengthC",
								chkText_maxlengthC);

					}
				}
			} else if ("05".equals(page)) {
				if (true) {
					L170M01F l170m01f = meta.getL170m01f();
					LMSUtil.addMetaToResult(result, l170m01f, new String[] {
							"retialComm", "conFlag", "condition", "upDate",
							"branchComm" });
					result.set("approver", _id_name(meta.getApprover()));
				}

				_signInfo(result, meta);
			} else if ("06".equals(page)) {
				LMSUtil.addMetaToResult(result, meta, new String[] { "needPa" });
				result.set("paForm", retrialService.buildPaFormHtml(meta, prop_lms1700m01));

				Set<L170M01J> l170m01js = meta.getL170m01js();
				if (l170m01js != null && !l170m01js.isEmpty()) {
					for (L170M01J l170m01j : l170m01js) {
						String itemName = Util.trim(l170m01j.getItemName());
						result.set(itemName + "_" + "dscr", l170m01j.getDscr());
						result.set(itemName + "_" + "itemYn",
								Util.nullToSpace(l170m01j.getItemYn()));
						result.set(itemName + "_" + "itemCnt",
								Util.nullToSpace(l170m01j.getItemCnt()));
					}
				}
			}
		}
		return defaultResult(params, meta, result);
	}

	private void _l170m01b_toResult(L170M01A meta, CapAjaxFormResult result)
			throws CapException {

		LMSUtil.addMetaToResult(result, meta, new String[] { "totQuotaCurr",
				"totBalCurr", "lnDataDate" });
		result.set(
				"totQuota",
				meta.getTotQuota() == null ? "" : CrsUtil.amtDivide1000(meta
						.getTotQuota()));
		result.set(
				"totBal",
				meta.getTotBal() == null ? "" : CrsUtil.amtDivide1000(meta
						.getTotBal()));
	}

	private void _l170m01c_toResult(L170M01C l170m01c,
			Map<String, String> finRatioMap, CapAjaxFormResult result)
			throws CapException {

		LMSUtil.addMetaToResult(result, l170m01c, new String[] { "curr",
				"unit", "ratioNo1", "ratioNo2", "ratioNo3", "ratioNo4"

				, "fromDate1", "endDate1", "amt11", "amt12", "amt13",
				"rateDate1", "rate11", "rate12", "rate13", "rate14"

				, "fromDate2", "endDate2", "amt21", "amt22", "amt23",
				"rateDate2", "rate21", "rate22", "rate23", "rate24"

				, "fromDate3", "endDate3", "amt31", "amt32", "amt33",
				"rateDate3", "rate31", "rate32", "rate33", "rate34" });
		result.set("ratioNo1_desc",
				LMSUtil.getDesc(finRatioMap, Util.trim(l170m01c.getRatioNo1())));
		result.set("ratioNo2_desc",
				LMSUtil.getDesc(finRatioMap, Util.trim(l170m01c.getRatioNo2())));
		result.set("ratioNo3_desc",
				LMSUtil.getDesc(finRatioMap, Util.trim(l170m01c.getRatioNo3())));
		result.set("ratioNo4_desc",
				LMSUtil.getDesc(finRatioMap, Util.trim(l170m01c.getRatioNo4())));
	}

	private void setL170M01D(HashMap<String, JSONArray> map, String itemType,
			List<L170M01D> raw_list, String ctlType,L170M01A meta) throws CapException {
		JSONArray jsonAraay = new JSONArray();

		// ---
		for (L170M01D l170m01d : raw_list) {
			if (Util.notEquals(itemType, l170m01d.getItemType())) {
				continue;
			}
			// J-111-0031 更動覆審系統內以下九式覆審報告表之文字內容。
			L170M01A l170m01a = retrialService.findL170M01A_mainId(l170m01d
					.getMainId());
			if (l170m01a == null) {
				l170m01a = new L170M01A();
			}
							
			JSONObject o = new JSONObject();
			o.putAll(DataParse.toJSON(l170m01d, true));
			
			List<String> realRpFgNoShow = new ArrayList<String>();									

			// 替代‵0‵為checkBox
			if (Util.equals(LrsUtil.N002, l170m01d.getItemNo())
					|| Util.equals(LrsUtil.B003_ref_N002, l170m01d.getItemNo())) {

				o.put("itemContent",
						StringUtils.replace(
								l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC,
								"<label><input type='checkbox' id='"
										+ INPUT_CHKCHECK
										+ "' name='"
										+ INPUT_CHKCHECK
										+ "' value='N' "
										+ (Util.equals("N",
												l170m01d.getChkCheck()) ? " checked "
												: "")
										+ " >"
										+ prop_lms1700m01
												.getProperty("label.N2")
										+ "</label>"));
			}
			if (Util.equals(LrsUtil.CTLTYPE_價金履約, ctlType)) {
				if (Util.equals("B014", l170m01d.getItemNo())) {
					o.put("_chkResult_fmt", "N|Y|K");
				} else {
					o.put("_chkResult_fmt", "Y|N|K");
				}
			} else {
				o.put("_chkResult_fmt", LrsUtil.chkResult_fmt(l170m01d));
			}

			if (Util.notEquals(LrsUtil.CTLTYPE_價金履約, ctlType)) {
				// 授信用途是否與申貸用途相符？
				if (Util.equals(LrsUtil.N008, l170m01d.getItemNo())) {
					// J-111-0031 更動覆審系統內以下九式覆審報告表之文字內容。
					if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
							LrsUtil.V_20220401)) {
						o.put("_prefix", LrsUtil.Z_DESC_N008_20220401);
					} else {
						// 原版
						// 中間版本 ">=", LrsUtil.V_20170603
						o.put("_prefix", LrsUtil.Z_DESC_N008);
					}
				}

				// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
				// 中長期放款（含土建融之案件）之申貸計劃及其自籌款是否照預定進度與核定條件執行？
				if (Util.equals(LrsUtil.N011, l170m01d.getItemNo())) {
					o.put("_prefix", LrsUtil.X_DESC_N011);
					o.put("_x_html_loc", "Y");
				}

				// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
				// 借戶是否依照約定條件履行？
				if (Util.equals(LrsUtil.XA1A, l170m01d.getItemNo())) {
					o.put("_prefix", LrsUtil.X_DESC_XA11_N);
				}

				// 工程預付款及/或履約保證其工程進度及履約情形是否正常？
				if (Util.equals(LrsUtil.N012, l170m01d.getItemNo())) {
					o.put("_prefix", LrsUtil.Z_DESC_N012);
				}
				// 授信電腦建檔資料覆核是否確實？
				if (Util.equals(LrsUtil.N027, l170m01d.getItemNo())) {
					o.put("_prefix", LrsUtil.Z_DESC_N027);
					o.put("_z_html_loc", "Y");
				}

				// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
				// 借戶是否依照約定條件履行？
				if (Util.equals(LrsUtil.N015, l170m01d.getItemNo())) {
					o.put("_prefix", LrsUtil.Y_DESC_N015);
					o.put("_y_html_loc", "Y");
				}

				// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
				// 借戶是否依照約定條件履行？
				if (Util.equals(LrsUtil.YA13, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.YB13, l170m01d.getItemNo())) {
					o.put("_prefix", LrsUtil.Y_DESC_YA11_ALL);
				}
				if (Util.equals(LrsUtil.YA11, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.YA12, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.YB11, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.YB12, l170m01d.getItemNo())) {
					if (Util.equals(LrsUtil.YA12, l170m01d.getItemNo())
							|| Util.equals(LrsUtil.YB11, l170m01d.getItemNo())
							|| Util.equals(LrsUtil.YB12, l170m01d.getItemNo())) {
						o.put("_prefix", LrsUtil.Y_DESC_YA11_ALL);
					} else {
						o.put("_prefix", LrsUtil.Y_DESC_YA11_N);
					}
					// J-109-0336 檢視事項及承諾事項之管控機制
					if (Util.equals(LrsUtil.YA11, l170m01d.getItemNo())) {
						o.put("_u_note", "　" + ("<u>") + LrsUtil.Y_NOTE_N015
								+ ("</u>"));
					} else {
						o.put("_u_note", "　" + ("<u>") + LrsUtil.Y_NOTE_YAYB_NA
								+ ("</u>"));
					}
				}

				// 辦理應收帳款承購應注意買方之付款是否有逾期30日以上之情形？
				if (Util.equals(LrsUtil.N025, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.N029, l170m01d.getItemNo())) {
					o.put("_prefix", LrsUtil.Z_DESC_N025);
				}
				if (Util.equals(LrsUtil.ZB1A, l170m01d.getItemNo())) {
					o.put("_itemPost", LrsUtil.Z_DESC_ZB1A_Y);
				}

				if (Util.equals(LrsUtil.ZA30, l170m01d.getItemNo())) {
					o.put("_u_note", "　" + ("<u>") + LrsUtil.Z_NOTE_ZA30
							+ ("</u>"));
				}

				// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
				if (Util.equals(LrsUtil.ZB21, l170m01d.getItemNo())) {
					o.put("_u_note", "　　" + ("<u>") + LrsUtil.Z_NOTE_ZB21
							+ ("</u>"));
				}

				// "前次覆審有無應行改善事項？
				if (Util.equals(LrsUtil.N017, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.B015_N017, l170m01d.getItemNo())) {
					o.put("_prefix", LrsUtil.Z_DESC_N017);
					o.put("_chkPreReview_fmt", "Y3|N3");// 【已改善|未改善】
				}
			} else {
				// J-107-0254_09301_B1001 配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
				if (Util.equals("B005", l170m01d.getItemNo())) {
					o.put("_prefix", LrsUtil.CTLTYPE_C_B005_prefix);
				} else if (Util.equals("B009", l170m01d.getItemNo())) {
					o.put("_prefix", LrsUtil.CTLTYPE_C_B009_prefix);
				} else if (Util.equals("B007", l170m01d.getItemNo())) {
					o.put("_u_note", "　" + ("<u>")
							+ LrsUtil.CTLTYPE_C_B007_note + ("</u>"));
				}
			}

			if (true) {
				String _ptMgrName = "";
				String ptMgrId = Util.trim(l170m01d.getPtMgrId());
				if (Util.isNotEmpty(ptMgrId)) {
					_ptMgrName = Util
							.trim(userInfoService.getUserName(ptMgrId));
				}
				o.put("_ptMgrName", _ptMgrName);
			}

			// J-108-0268 逾期情形
			// L170M01A l170m01a =
			// retrialService.findL170M01A_mainId(l170m01d.getMainId());
			String overDueText = "";
			if (l170m01a.getOvQryDt() != null) {
				// ui_lms1700.msg35=本金最長逾期天數：{0}天，利息最長逾期天數：{1}天。引進資料日期:{2}
				overDueText = MessageFormat.format(
						prop_lms1700m01.getProperty("ui_lms1700.msg35"),
						Util.nullToSpace(l170m01a.getCapDays()),
						Util.nullToSpace(l170m01a.getIntDays()),
						TWNDate.toAD(l170m01a.getOvQryDt()));
			}
			if (Util.equals(ctlType, LrsUtil.CTLTYPE_主辦覆審)
					&& Util.equals(LrsUtil.N009, l170m01d.getItemNo())) {
				o.put("_overDue", "Y");
				o.put("overDueText", overDueText);
			} else if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)
					&& Util.equals(LrsUtil.B014, l170m01d.getItemNo())) {
				o.put("_overDue", "Y");
				o.put("overDueText", overDueText);
			} else {
				o.put("_overDue", "");
				o.put("overDueText", "");
			}

			// ========================
			// 替代Z開頭的項目‵0‵為 radio
			if (Util.equals(LrsUtil.ZA23, l170m01d.getItemNo())) {
				String _str = "<label><input id='_chkResult_ZA23_v_Y' type='radio' value='Y' name='_chkResult_ZA23' "
						+ (Util.equals("Y", l170m01d.getChkResult()) ? " checked "
								: "")
						+ ">"
						+ prop_lms1700m01.getProperty("label.Y2")
						+ "</label>"
						+ "<label><input id='_chkResult_ZA23_v_N' type='radio' value='N' name='_chkResult_ZA23' "
						+ (Util.equals("N", l170m01d.getChkResult()) ? " checked "
								: "")
						+ ">"
						+ prop_lms1700m01.getProperty("label.N2") + "</label>";

				o.put("itemContent",
						StringUtils.replace(l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC, _str)
								+ LrsUtil.Z_DESC_ZA23_Y);
			}
			if (Util.equals(LrsUtil.ZB1A, l170m01d.getItemNo())) {
				String _str = "<label><input id='_chkResult_ZB1A_v_Y' type='radio' value='Y' name='_chkResult_ZB1A' "
						+ (Util.equals("Y", l170m01d.getChkResult()) ? " checked "
								: "")
						+ ">"
						+ prop_lms1700m01.getProperty("label.Y2")
						+ "</label>"
						+ "<label><input id='_chkResult_ZB1A_v_N' type='radio' value='N' name='_chkResult_ZB1A' "
						+ (Util.equals("N", l170m01d.getChkResult()) ? " checked "
								: "")
						+ ">"
						+ prop_lms1700m01.getProperty("label.N2") + "</label>";

				o.put("itemContent",
						StringUtils.replace(l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC, _str)
								+ LrsUtil.Z_DESC_ZB1A_Y);
			}
			// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
			if (Util.equals(LrsUtil.ZB2A, l170m01d.getItemNo())) {
				String _str = "<label><input id='_chkResult_ZB2A_v_Y' type='radio' value='Y' name='_chkResult_ZB2A' "
						+ (Util.equals("Y", l170m01d.getChkResult()) ? " checked "
								: "")
						+ ">"
						+ prop_lms1700m01.getProperty("label.Y2")
						+ "</label>"
						+ "<label><input id='_chkResult_ZB2A_v_N' type='radio' value='N' name='_chkResult_ZB2A' "
						+ (Util.equals("N", l170m01d.getChkResult()) ? " checked "
								: "")
						+ ">"
						+ prop_lms1700m01.getProperty("label.N2") + "</label>";

				o.put("itemContent", StringUtils.replace(
						l170m01d.getItemContent(), LrsUtil.L170M01D_RPLC, _str));
			}
			// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
			if (Util.equals(LrsUtil.ZB34, l170m01d.getItemNo())) {
				String _str = "<label><input id='_chkResult_ZB34_v_Y' type='radio' value='Y' name='_chkResult_ZB34' "
					+ (Util.equals("Y", l170m01d.getChkResult()) ? " checked "
							: "")
					+ ">"
					+ prop_lms1700m01.getProperty("label.Y2")
					+ "</label>"
					+ "<label><input id='_chkResult_ZB34_v_N' type='radio' value='N' name='_chkResult_ZB34' "
					+ (Util.equals("N", l170m01d.getChkResult()) ? " checked "
							: "")
					+ ">"
					+ prop_lms1700m01.getProperty("label.N2") + "</label>";

			    o.put("itemContent", StringUtils.replace(
					    l170m01d.getItemContent(), LrsUtil.L170M01D_RPLC, _str));
			}
			
			if (Util.equals(LrsUtil.ZB3N, l170m01d.getItemNo())) {
				String _str = "<label><input id='_chkResult_ZB3N_v_Y' type='radio' value='Y' name='_chkResult_ZB3N' "
					+ (Util.equals("Y", l170m01d.getChkResult()) ? " checked "
							: "")
					+ ">"
					+ prop_lms1700m01.getProperty("label.Y2")
					+ "</label>"
					+ "<label><input id='_chkResult_ZB3N_v_N' type='radio' value='N' name='_chkResult_ZB3N' "
					+ (Util.equals("N", l170m01d.getChkResult()) ? " checked "
							: "")
					+ ">"
					+ prop_lms1700m01.getProperty("label.N2") + "</label>";

			    o.put("itemContent", StringUtils.replace(
					    l170m01d.getItemContent(), LrsUtil.L170M01D_RPLC, _str));
			}
			
			
			if (Util.equals(LrsUtil.ZB3Q, l170m01d.getItemNo())) {
				String _str = "<label><input id='_chkResult_ZB3Q_v_Y' type='radio' value='Y' name='_chkResult_ZB3Q' "
					+ (Util.equals("Y", l170m01d.getChkResult()) ? " checked "
							: "")
					+ ">"
					+ prop_lms1700m01.getProperty("label.Y2")
					+ "</label>"
					+ "<label><input id='_chkResult_ZB3Q_v_N' type='radio' value='N' name='_chkResult_ZB3Q' "
					+ (Util.equals("N", l170m01d.getChkResult()) ? " checked "
							: "")
					+ ">"
					+ prop_lms1700m01.getProperty("label.N2") + "</label>";

			    o.put("itemContent", StringUtils.replace(
					    l170m01d.getItemContent(), LrsUtil.L170M01D_RPLC, _str));
			}
						
			// ========================
			// 替代Z開頭的項目‵0‵為 是否

			if (CrsUtil.inCollection(l170m01d.getItemNo(), LrsUtil.Z_YNBOX)) {
				String yn = prop_lms1700m01.getProperty("label.Y")
						+ prop_lms1700m01.getProperty("label.N");// 是否
				
				
				if (// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
						Util.equals(LrsUtil.ZB30, l170m01d.getItemNo())
					 || Util.equals(LrsUtil.ZB3A, l170m01d.getItemNo())
					 || Util.equals(LrsUtil.ZB3C, l170m01d.getItemNo())
					 || Util.equals(LrsUtil.ZB3E, l170m01d.getItemNo())
					 // J-113-0204  新增及修正說明文句
					 || Util.equals(LrsUtil.ZC1A, l170m01d.getItemNo())
					 || Util.equals(LrsUtil.ZC30, l170m01d.getItemNo())
					 || Util.equals(LrsUtil.ZC3A, l170m01d.getItemNo())
					 || Util.equals(LrsUtil.ZC3B, l170m01d.getItemNo())
					 || Util.equals(LrsUtil.ZC3C, l170m01d.getItemNo())
					 || Util.equals(LrsUtil.ZC3D, l170m01d.getItemNo())
				   ) {
						yn="";
					}
								
				o.put("itemContent", StringUtils.replace(
						l170m01d.getItemContent(), LrsUtil.L170M01D_RPLC, yn));
			}
			// ========================
			// 電腦建檔項目縮排
			if (true) {
				String itemNo = (String) o.get("itemNo");
				String itemContent = (String) o.get("itemContent");
				if (CrsUtil.inCollection(itemNo, LrsUtil.Z_TAB1)) {
					o.put("itemContent", "　" + itemContent);
				} else if (CrsUtil.inCollection(itemNo, LrsUtil.Z_TAB2)) {
					o.put("itemContent", "　　" + itemContent);
				} else if (CrsUtil.inCollection(itemNo, LrsUtil.Z_TAB3)) {
					o.put("itemContent", "　　　" + itemContent);
				}
			}
			
			
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_20240601)) {
			    
				realRpFgNoShow = new ArrayList<String>();
				realRpFgNoShow.add(LrsUtil.N033);
				realRpFgNoShow.add(LrsUtil.ZA13);			
				realRpFgNoShow.add(LrsUtil.ZB14);
			    realRpFgNoShow.add(LrsUtil.ZB15);
			    realRpFgNoShow.add(LrsUtil.ZB40);
			    realRpFgNoShow.add(LrsUtil.ZB50);
			    realRpFgNoShow.add(LrsUtil.ZC10);
			    realRpFgNoShow.add(LrsUtil.ZC1A);
			    realRpFgNoShow.add(LrsUtil.ZC20);
			    realRpFgNoShow.add(LrsUtil.ZC00);
			    realRpFgNoShow.add(LrsUtil.ZC30);
			    realRpFgNoShow.add(LrsUtil.ZC3A);
			    realRpFgNoShow.add(LrsUtil.ZC3B);
			    realRpFgNoShow.add(LrsUtil.ZC3C);
			    realRpFgNoShow.add(LrsUtil.ZC3D);
				
				// J-113-0204  新增及修正說明文句
				String itemContent = MapUtils.getString(o,"itemContent");
				if(Util.equals("Y",Util.trim(meta.getRealRpFg()))){
					
					if (realRpFgNoShow.contains(l170m01d.getItemNo())){
						continue;
					}
					
					if (Util.equals(LrsUtil.ZB10, l170m01d.getItemNo())){
						itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "1.");
					}
					if (Util.equals(LrsUtil.ZB20, l170m01d.getItemNo())){
						itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "2.");
					}
					if (Util.equals(LrsUtil.ZB30, l170m01d.getItemNo())){
						itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "3.");
					}
					if(Util.equals(LrsUtil.ZB11, l170m01d.getItemNo())){
						itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSTR, "");						
					}
					
				}else{
					
					if (Util.equals(LrsUtil.ZB40, l170m01d.getItemNo())){
						itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "1.");
					}
					if (Util.equals(LrsUtil.ZB50, l170m01d.getItemNo())){
						itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "2.");
					}
					if (Util.equals(LrsUtil.ZB10, l170m01d.getItemNo())){
						itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "3.");
					}
					if (Util.equals(LrsUtil.ZB20, l170m01d.getItemNo())){
						itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "4.");
					}
					if (Util.equals(LrsUtil.ZB30, l170m01d.getItemNo())){
						itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSEQ, "5.");
					}
					if(Util.equals(LrsUtil.ZB11, l170m01d.getItemNo())){
						itemContent=StringUtils.replace(itemContent,LrsUtil.L170M01D_RPLCSTR, "土地");						
					}
					
				}
				
				o.put("itemContent",itemContent);				
			}

			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			// 履約========================
			// 案下授信額度"有無"「應檢視事項」，若勾「有」，則應檢視下列各項： "有無" 改成radio
			if (Util.equals(LrsUtil.YA1A, l170m01d.getItemNo())) {
				String _str = "<label><input id='_chkResult_YA1A_v_Y' type='radio' value='Y' name='_chkResult_YA1A' "
						+ (Util.equals("Y", l170m01d.getChkResult()) ? " checked "
								: "")
						+ ">"
						+ prop_lms1700m01.getProperty("label.Y2")
						+ "</label>"
						+ "<label><input id='_chkResult_YA1A_v_N' type='radio' value='N' name='_chkResult_YA1A' "
						+ (Util.equals("N", l170m01d.getChkResult()) ? " checked "
								: "")
						+ ">"
						+ prop_lms1700m01.getProperty("label.N2") + "</label>";

				o.put("itemContent",
						StringUtils.replace(l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC, _str)
								+ LrsUtil.Y_DESC_YA1A_Y);
			}

			// 案下授信額度"有無"「承諾事項」，若勾「有」，則應檢視下列各項： "有無" 改成radio
			if (Util.equals(LrsUtil.YB1A, l170m01d.getItemNo())) {
				String _str = "<label><input id='_chkResult_YB1A_v_Y' type='radio' value='Y' name='_chkResult_YB1A' "
						+ (Util.equals("Y", l170m01d.getChkResult()) ? " checked "
								: "")
						+ ">"
						+ prop_lms1700m01.getProperty("label.Y2")
						+ "</label>"
						+ "<label><input id='_chkResult_YB1A_v_N' type='radio' value='N' name='_chkResult_YB1A' "
						+ (Util.equals("N", l170m01d.getChkResult()) ? " checked "
								: "")
						+ ">"
						+ prop_lms1700m01.getProperty("label.N2") + "</label>";

				o.put("itemContent",
						StringUtils.replace(l170m01d.getItemContent(),
								LrsUtil.L170M01D_RPLC, _str)
								+ LrsUtil.Y_DESC_YB1A_Y);
			}

			// ========================
			// 替代Y_YNBOX開頭的項目‵0‵為 是否

			if (CrsUtil.inCollection(l170m01d.getItemNo(), LrsUtil.Y_YNBOX)) {
				String yn = prop_lms1700m01.getProperty("label.Y")
						+ prop_lms1700m01.getProperty("label.N");// 是否

				o.put("itemContent", StringUtils.replace(
						l170m01d.getItemContent(), LrsUtil.L170M01D_RPLC, yn));
			}
			// ========================
			// 替代X_YNBOX開頭的項目‵0‵為 是否
			// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
			if (CrsUtil.inCollection(l170m01d.getItemNo(), LrsUtil.X_YNBOX)) {
				String yn = prop_lms1700m01.getProperty("label.Y")
						+ prop_lms1700m01.getProperty("label.N");// 是否

				o.put("itemContent", StringUtils.replace(
						l170m01d.getItemContent(), LrsUtil.L170M01D_RPLC, yn));
			}
			// ========================
			// 電腦建檔項目縮排
			if (true) {
				String itemNo = (String) o.get("itemNo");
				String itemContent = (String) o.get("itemContent");
				if (CrsUtil.inCollection(itemNo, LrsUtil.Y_TAB1)) {
					o.put("itemContent", "　" + itemContent);
				} else if (CrsUtil.inCollection(itemNo, LrsUtil.Y_TAB2)) {
					o.put("itemContent", "　　" + itemContent);
				}
			}

			// ========================
			// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
			// 電腦建檔項目縮排
			if (true) {
				String itemNo = (String) o.get("itemNo");
				String itemContent = (String) o.get("itemContent");
				if (CrsUtil.inCollection(itemNo, LrsUtil.X_TAB1)) {
					o.put("itemContent", "　" + itemContent);
				} else if (CrsUtil.inCollection(itemNo, LrsUtil.X_TAB2)) {
					o.put("itemContent", "　　" + itemContent);
				}
			}

			jsonAraay.add(o);
		}
		map.put(itemType, jsonAraay);
	}

	private void _setMapWithOrder(CapAjaxFormResult result, String elm,
			Map<String, String> m) {
		List<Map<String, String>> ord = new ArrayList<>();
		if (m instanceof LinkedHashMap) {
			for (String k : m.keySet()) {
				Map<String, String> r = new LinkedHashMap<>(); // 創建一個 Map 來代表每個 JSON 物件
		        r.put("value", k);
		        r.put("desc", m.get(k));
		        ord.add(r); // 將 Map 加入列表
			}
		} else {
			for (String k : m.keySet()) {
				Map<String, String> r = new LinkedHashMap<>(); // 創建一個 Map 來代表每個 JSON 物件
		        r.put("value", k);
		        r.put("desc", m.get(k));
		        ord.add(r); // 將 Map 加入列表
			}
		}
		result.set(elm, ord);

	}

	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params)
			throws CapException {
		return _saveAction(params, "Y");
	}

	private CapAjaxFormResult _saveAction(PageParameters params, String tempSave) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y",
				params.getString("allowIncomplete"));
		// ===
		String KEY = "saveOkFlag";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L170M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try {
				meta = retrialService.findL170M01A_oid(mainOid);

				String page = params.getString(EloanConstants.PAGE);
				if ("01".equals(page)) {
					// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
					CapBeanUtil.map2Bean(params, meta, new String[] {
							"retrialDate", "lastRetrialDate", "typCd",
							"chairman", "mLoanPerson", "mLoanPersonA", "freeG",
							"freeC", "realCkFg", "realDt", "realRpFg" });

					if (true) {// 處理 L170M01H
						String userId = user.getUserId();
						Timestamp nowTS = CapDate.getCurrentTimestamp();
						if (Util.equals("Y", meta.getFreeG())) {
							L170M01H l170m01h_freeG = retrialService
									.findL170M01H_fmtFreeG_l170m01a(meta);
							if (l170m01h_freeG == null) {
								l170m01h_freeG = new L170M01H();

								l170m01h_freeG.setMainId(meta.getMainId());
								l170m01h_freeG.setCustId(meta.getCustId());
								l170m01h_freeG.setDupNo(meta.getDupNo());
								l170m01h_freeG.setDebType(LrsUtil.M01H_FLAG_G);
								l170m01h_freeG
										.setDebId(LrsUtil.M01H_FREE_DEBID_GN);
								l170m01h_freeG.setDebDupNo("0");
								l170m01h_freeG.setCustName("");
								l170m01h_freeG.setCreator(userId);
								l170m01h_freeG.setCreateTime(nowTS);
								l170m01h_freeG.setUpdater(userId);
								l170m01h_freeG.setUpdateTime(nowTS);
							}
							l170m01h_freeG.setCustName(Util.trim(params
									.getString("rltGuarantor_free")));
							// ---
							retrialService.save(l170m01h_freeG);
						}
						if (Util.equals("Y", meta.getFreeC())) {
							L170M01H l170m01h_freeC = retrialService
									.findL170M01H_fmtFreeC_l170m01a(meta);
							if (l170m01h_freeC == null) {
								l170m01h_freeC = new L170M01H();

								l170m01h_freeC.setMainId(meta.getMainId());
								l170m01h_freeC.setCustId(meta.getCustId());
								l170m01h_freeC.setDupNo(meta.getDupNo());
								l170m01h_freeC.setDebType(LrsUtil.M01H_FLAG_C);
								l170m01h_freeC
										.setDebId(LrsUtil.M01H_FREE_DEBID_C);
								l170m01h_freeC.setDebDupNo("0");
								l170m01h_freeC.setCustName("");
								l170m01h_freeC.setCreator(userId);
								l170m01h_freeC.setCreateTime(nowTS);
								l170m01h_freeC.setUpdater(userId);
								l170m01h_freeC.setUpdateTime(nowTS);
							}
							l170m01h_freeC.setCustName(Util.trim(params
									.getString("rltBorrower_free")));
							// ---
							retrialService.save(l170m01h_freeC);
						}
					}
					if (true) {// 處理 L170M01E
						Map<String, List<L170M01E>> m_l170m01e = retrialService
								.findL170M01E_type(retrialService
										.findL170M01E(meta));
						if (true) {
							String CreditType = Util.trim(params
									.getString("CreditType"));
							String CreditGrade = Util.trim(params
									.getString("CreditGrade"));
							if (Util.isEmpty(CreditType)) {
								L170M01E l170m01e_C = LrsUtil.firstElm(
										m_l170m01e.get(LrsUtil.M01E_FLAG_C),
										"T");
								if (l170m01e_C != null) {
									retrialService.del(l170m01e_C);
								}
							} else {
								L170M01E l170m01e_C = LrsUtil.firstElm(
										m_l170m01e.get(LrsUtil.M01E_FLAG_C),
										"T");
								if (l170m01e_C == null) {
									l170m01e_C = new L170M01E();
									// ---
									LrsUtil.initl170m01e(l170m01e_C, meta,
											user.getUserId(), "T");
								}
								l170m01e_C.setCrdType(CreditType);
								l170m01e_C.setGrade(CreditGrade);
								retrialService.save(l170m01e_C);
							}
						}
						// 前次信用評等
						if (true) {
							String exCreditType = Util.trim(params
									.getString("exCreditType"));
							String exCreditGrade = Util.trim(params
									.getString("exCreditGrade"));
							if (Util.isEmpty(exCreditType)) {
								L170M01E l170m01e_exC = LrsUtil.firstElm(
										m_l170m01e.get(LrsUtil.M01E_FLAG_C),
										"L");
								if (l170m01e_exC != null) {
									retrialService.del(l170m01e_exC);
								}
							} else {
								L170M01E l170m01e_exC = LrsUtil.firstElm(
										m_l170m01e.get(LrsUtil.M01E_FLAG_C),
										"L");
								if (l170m01e_exC == null) {
									l170m01e_exC = new L170M01E();
									LrsUtil.initl170m01e(l170m01e_exC, meta,
											user.getUserId(), "L");
								}
								l170m01e_exC.setCrdType(exCreditType);
								l170m01e_exC.setGrade(exCreditGrade);
								retrialService.save(l170m01e_exC);
							}
						}
						if (true) {
							String MowType = Util.trim(params
									.getString("MowType"));
							String MowGrade = Util.trim(params
									.getString("MowGrade"));
							if (Util.isEmpty(MowType)) {
								L170M01E l170m01e_M = LrsUtil.firstElm(
										m_l170m01e.get(LrsUtil.M01E_FLAG_M),
										"T");
								if (l170m01e_M != null) {
									retrialService.del(l170m01e_M);
								}
							} else {
								L170M01E l170m01e_M = LrsUtil.firstElm(
										m_l170m01e.get(LrsUtil.M01E_FLAG_M),
										"T");
								if (l170m01e_M == null) {
									l170m01e_M = new L170M01E();
									// ---
									LrsUtil.initl170m01e(l170m01e_M, meta,
											user.getUserId(), "T");
								}
								l170m01e_M.setCrdType(MowType);
								l170m01e_M.setGrade(MowGrade);
								retrialService.save(l170m01e_M);
							}
						}
						// 前次信用風險內部評等
						if (true) {
							String exMowType = Util.trim(params
									.getString("exMowType"));
							String exMowGrade = Util.trim(params
									.getString("exMowGrade"));
							if (Util.isEmpty(exMowType)) {
								L170M01E l170m01e_exM = LrsUtil.firstElm(
										m_l170m01e.get(LrsUtil.M01E_FLAG_M),
										"L");
								if (l170m01e_exM != null) {
									retrialService.del(l170m01e_exM);
								}
							} else {
								L170M01E l170m01e_exM = LrsUtil.firstElm(
										m_l170m01e.get(LrsUtil.M01E_FLAG_M),
										"L");
								if (l170m01e_exM == null) {
									l170m01e_exM = new L170M01E();
									LrsUtil.initl170m01e(l170m01e_exM, meta,
											user.getUserId(), "L");
								}
								l170m01e_exM.setCrdType(exMowType);
								l170m01e_exM.setGrade(exMowGrade);
								retrialService.save(l170m01e_exM);
							}
						}
						if (true) {
							String FcrdType = Util.trim(params
									.getString("FcrdType"));
							String FcrdArea = Util.trim(params
									.getString("FcrdArea"));
							String FcrdPred = Util.trim(params
									.getString("FcrdPred"));
							String FcrdGrad = Util.trim(params
									.getString("FcrdGrad"));

							if (Util.isEmpty(FcrdType)
									|| Util.isEmpty(FcrdGrad)) {
								L170M01E l170m01e_F = LrsUtil.firstElm(
										m_l170m01e.get(LrsUtil.M01E_FLAG_F),
										"T");
								if (l170m01e_F != null) {
									retrialService.del(l170m01e_F);
								}
							} else {
								L170M01E l170m01e_F = null;
								if (LrsUtil.firstElm(
										m_l170m01e.get(LrsUtil.M01E_FLAG_F),
										"T") != null) {
									if (LrsUtil.isCustL170M01E(LrsUtil
											.firstElm(m_l170m01e
													.get(LrsUtil.M01E_FLAG_F),
													"T"))) {
										l170m01e_F = LrsUtil
												.firstElm(
														m_l170m01e
																.get(LrsUtil.M01E_FLAG_F),
														"T");
									} else {
										retrialService
												.del(LrsUtil.firstElm(
														m_l170m01e
																.get(LrsUtil.M01E_FLAG_F),
														"T"));
									}
								}

								if (l170m01e_F == null) {
									l170m01e_F = new L170M01E();
									// ---
									LrsUtil.initl170m01e(l170m01e_F, meta,
											user.getUserId(), "T");
								}
								LrsUtil.setL170M01E_FCRD(l170m01e_F, FcrdType,
										FcrdArea, FcrdPred, FcrdGrad);

								retrialService.save(l170m01e_F);
							}
						}
						// 前次外部評等類別
						if (true) {
							String exFcrdType = Util.trim(params
									.getString("exFcrdType"));
							String exFcrdArea = Util.trim(params
									.getString("exFcrdArea"));
							String exFcrdPred = Util.trim(params
									.getString("exFcrdPred"));
							String exFcrdGrad = Util.trim(params
									.getString("exFcrdGrad"));

							if (Util.isEmpty(exFcrdType)
									|| Util.isEmpty(exFcrdGrad)) {
								L170M01E l170m01e_exF = LrsUtil.firstElm(
										m_l170m01e.get(LrsUtil.M01E_FLAG_F),
										"L");
								if (l170m01e_exF != null) {
									retrialService.del(l170m01e_exF);
								}
							} else {
								L170M01E l170m01e_exF = null;
								if (LrsUtil.firstElm(
										m_l170m01e.get(LrsUtil.M01E_FLAG_F),
										"L") != null) {
									if (LrsUtil.isCustL170M01E(LrsUtil
											.firstElm(m_l170m01e
													.get(LrsUtil.M01E_FLAG_F),
													"L"))) {
										l170m01e_exF = LrsUtil
												.firstElm(
														m_l170m01e
																.get(LrsUtil.M01E_FLAG_F),
														"L");
									} else {
										retrialService
												.del(LrsUtil.firstElm(
														m_l170m01e
																.get(LrsUtil.M01E_FLAG_F),
														"L"));
									}
								}

								if (l170m01e_exF == null) {
									l170m01e_exF = new L170M01E();
									LrsUtil.initl170m01e(l170m01e_exF, meta,
											user.getUserId(), "L");
								}
								LrsUtil.setL170M01E_FCRD(l170m01e_exF,
										exFcrdType, exFcrdArea, exFcrdPred,
										exFcrdGrad);

								retrialService.save(l170m01e_exF);
							}
						}
					}

					String paVer = retrialService.getPaFormVer(meta);
					if (Util.notEquals(meta.getPaVer(), paVer)) {
						// 清除舊資料
						List<L170M01J> orgM01jList = retrialService
								.findL170M01JByMainId(meta);
						if (orgM01jList != null && orgM01jList.size() > 0) {
							retrialService.deleteL170M01J(orgM01jList);
						}
						List<L170M01J> l170m01jList = new ArrayList<L170M01J>();
						LrsUtil.initL170m01j(l170m01jList, meta, user.getUserId(), paVer);
						if (!l170m01jList.isEmpty()) {
							retrialService.saveL170M01J(l170m01jList);
						}
						// 2023/05/09 [上午 11:44] 巫鎧戎(資訊處,專員) 受審說全部清掉
						meta.setNeedPa(null);
					}
					meta.setPaVer(paVer);
				} else if ("02".equals(page)) {

				} else if ("03".equals(page)) {
					L170M01C l170m01c = meta.getL170m01c();
					CapBeanUtil.map2Bean(params, l170m01c, new String[] {
							"curr", "unit", "ratioNo1", "ratioNo2", "ratioNo3",
							"ratioNo4", "fromDate1", "endDate1", "amt11",
							"amt12", "amt13", "rateDate1", "rate11", "rate12",
							"rate13", "rate14"

							, "fromDate2", "endDate2", "amt21", "amt22",
							"amt23", "rateDate2", "rate21", "rate22", "rate23",
							"rate24"

							, "fromDate3", "endDate3", "amt31", "amt32",
							"amt33", "rateDate3", "rate31", "rate32", "rate33",
							"rate34" });
					retrialService.save(l170m01c);
				} else if ("04".equals(page)) {
					List<L170M01D> l170m01d_list = retrialService
							.findL170M01D_orderBySeq(meta);
					/*
					 * _chkPreReview_N013 _chkResult_N001, _chkResult_N002
					 * _chkText_N001, _chkText_N002
					 */
					for (L170M01D l170m01d : l170m01d_list) {
						// 和消金覆審不同，有一些列是隱藏的
						// 所以拿掉 if(!params.containsKey(key)){ continue; }

						{// part 1
							String key = "_chkResult_" + l170m01d.getItemNo();
							l170m01d.setChkResult(Util.trim(params
									.getString(key)));
						}
						{// part 2
							String key = "_chkText_" + l170m01d.getItemNo();
							l170m01d.setChkText(Util.truncateString(
									Util.trim(params.getString(key)),
									MAXLEN_L170M01D_CHKTEXT));
						}
						{// part 3
							String key = "_chkPreReview_"
									+ l170m01d.getItemNo();
							l170m01d.setChkPreReview(Util.trim(params
									.getString(key)));
						}
						if (Util.notEquals(LrsUtil.CTLTYPE_價金履約,
								meta.getCtlType())) {
							if (Util.equals(LrsUtil.N002, l170m01d.getItemNo())
									|| Util.equals(LrsUtil.B003_ref_N002,
											l170m01d.getItemNo())) {

								String chkCheck = Util.trim(params
										.getString(INPUT_CHKCHECK));
								l170m01d.setChkCheck(chkCheck);
							}
						}
						// ---
						// 若在 loop 內一個一個call save(model) 在切換頁籤時，有一些慢
						// 改在 loop 外面 call save(list)
						// retrialService.save(l170m01d);
					}
					retrialService.saveL170M01D(l170m01d_list);
				} else if ("05".equals(page)) {
					L170M01F l170m01f = meta.getL170m01f();
					// ---
					CapBeanUtil.map2Bean(params, l170m01f, new String[] {
							"retialComm", "conFlag", "condition" });
					// ---
					retrialService.save(l170m01f);
				} else if ("06".equals(page)) {
					CapBeanUtil.map2Bean(params, meta,
							new String[] { "needPa" });
					String paVer = Util.nullToSpace(meta.getPaVer());
					String[] m01jCol = retrialService.getPaCol(paVer, "1");
					String[] m01jColYn = retrialService.getPaCol(paVer, "2");
					String[] m01jColCnt = retrialService.getPaCol(paVer, "3");
					List<L170M01J> l170m01jList = new ArrayList<L170M01J>();
					for (String fieldName : m01jCol) {
						String itemName = fieldName;
						String itemType = params.getString(fieldName
								+ "_itemType", "");
						String dscr = params.getString(fieldName + "_dscr", "");
						String itemYn = params.getString(fieldName + "_itemYn",
								"");
						String itemCnt = params.getString(fieldName
								+ "_itemCnt", "0");
						L170M01J l170m01j = retrialService
								.findL170M01JByMainIdItemName(meta, itemName);
						if (l170m01j == null) {
							l170m01j = new L170M01J();
							l170m01j.setMainId(meta.getMainId());
							l170m01j.setItemName(itemName);
							l170m01j.setCreator(user.getUserId());
							l170m01j.setCreateTime(CapDate
									.getCurrentTimestamp());
						}
						// l170m01j.setItemType(itemType); 因為html隱藏會讀不到 itemType
						// 導致 itemType為空
						if (ArrayUtils.contains(m01jColYn, itemName)) {
							l170m01j.setItemType("YN");
							itemType = "YN";
						} else if (ArrayUtils.contains(m01jColCnt, itemName)) {
							l170m01j.setItemType("CNT");
							itemType = "CNT";
						} else {
							l170m01j.setItemType(itemType);
						}
						l170m01j.setDscr(dscr);
						l170m01j.setItemYn(Util.equals(itemType, "YN") ? itemYn
								: null);
						l170m01j.setItemCnt(Util.equals(itemType, "CNT") ? NumberUtils
								.toInt(itemCnt) : null);
						l170m01j.setUpdater(user.getUserId());
						l170m01j.setUpdateTime(CapDate.getCurrentTimestamp());
						l170m01jList.add(l170m01j);
					}
					if (!l170m01jList.isEmpty()) {
						retrialService.saveL170M01J(l170m01jList);
					}
				}
				if (Util.notEquals("Y",
						SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					L170M01G a_L1 = retrialService.setL170M01G_L1(meta,
							lrsConstants.BRANCHTYPE.覆審單位, user.getUserId(),
							user.getUnitNo());
					retrialService.save(a_L1);
				}
				retrialService.save(meta);
				// ===
				if (Util.notEquals("Y",
						SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					// 在tempSave<>Y,若有未填欄位,丟 CapMessageException, 讓
					// saveOkFlag==false

					String msg = lms1700Service.checkIncompleteMsg(meta);
					if (Util.isNotEmpty(msg)) {
						if (allowIncomplete) {
							result.set("IncompleteMsg", msg);
						} else {
							throw new CapMessageException(msg, getClass());
						}
					}

				}
				result.set(KEY, true);
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}
		}
		result.add(query(params));

		return result;
	}

	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		boolean addMetaDesc = Util.equals("Y",
				Util.trim(params.getString("addMetaDesc")));
		L170M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL170M01A_oid(mainOid);

			List<L170M01G> l170m01g_list = retrialService
					.findL170M01G_l170m01a(meta);
			boolean no_1_L1 = CollectionUtils.isEmpty(retrialService
					.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
							lrsConstants.BRANCHTYPE.受檢單位, "L1"));
			// J-111-0033_05097_B1001 Web e-Loan
			// 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
			boolean no_1_L2 = CollectionUtils.isEmpty(retrialService
					.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
							lrsConstants.BRANCHTYPE.受檢單位, "L2"));
			boolean no_1_L4 = CollectionUtils.isEmpty(retrialService
					.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
							lrsConstants.BRANCHTYPE.受檢單位, "L4"));
			boolean no_1_L5 = CollectionUtils.isEmpty(retrialService
					.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
							lrsConstants.BRANCHTYPE.受檢單位, "L5"));

			boolean no_2_L1 = CollectionUtils.isEmpty(retrialService
					.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
							lrsConstants.BRANCHTYPE.覆審單位, "L1"));
			boolean no_2_L4 = CollectionUtils.isEmpty(retrialService
					.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
							lrsConstants.BRANCHTYPE.覆審單位, "L4"));
			boolean no_2_L5 = CollectionUtils.isEmpty(retrialService
					.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
							lrsConstants.BRANCHTYPE.覆審單位, "L5"));

			String errMsg = "";
			if (Util.equals("to_待覆核_覆審組", decisionExpr)) {
				errMsg = lms1700Service.checkIncompleteMsg(meta);

				if (Util.isEmpty(errMsg)) {
					Set<String> msgSet = new LinkedHashSet<String>();
					// 不論何種 status,都檢查 覆審組 的簽章欄
					// J-111-0033_05097_B1001 Web e-Loan
					// 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
					_lackSign(msgSet, meta, lrsConstants.BRANCHTYPE.覆審單位,
							no_2_L1, no_2_L4, no_2_L5, false);

					errMsg = StringUtils.join(msgSet, "<br/>");
				}
			} else if (Util.equals("to_編製中_分行端", decisionExpr)) {
				errMsg = lms1700Service.checkIncompleteMsg(meta);
			} else if (Util.equals("呈主管", decisionExpr)) {
				// 受檢單位 經辦 → 呈主管
				Set<String> msgSet = new LinkedHashSet<String>();

				if (_errorBranchComm(meta)) {
					// 請輸入受檢單位洽辦情形
					msgSet.add("請輸入受檢單位洽辦情形");
				}

				_lackSign(msgSet, meta, lrsConstants.BRANCHTYPE.受檢單位, no_1_L1,
						no_1_L4, no_1_L5, no_1_L2);

				errMsg = StringUtils.join(msgSet, "<br/>");
			} else if (Util.equals("to_已覆核未核定", decisionExpr)) {
				// 受檢單位 主管覆核

				// 檢查經辦和主管是否為同一人
				if (userId_in_L1(user.getUserId(),
						retrialService.findL170M01G_byBranchTypeStaffJob(
								l170m01g_list, lrsConstants.BRANCHTYPE.受檢單位,
								"L1"))) {
					errMsg = RespMsgHelper.getMessage("EFD0053");
				}

			} else if (Util.equals("to_已覆核已核定", decisionExpr)) {

				Set<String> msgSet = new LinkedHashSet<String>();

				// 不論何種 status,都檢查 覆審組 的簽章欄
				// J-111-0033_05097_B1001 Web e-Loan
				// 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
				_lackSign(msgSet, meta, lrsConstants.BRANCHTYPE.覆審單位, no_2_L1,
						no_2_L4, no_2_L5, false);

				if (Util.equals(meta.getDocStatus(),
						RetrialDocStatusEnum.區中心_編製中.getCode())) {
					// 在覆審組_編製中 → 已覆核已核定
				} else if (Util.equals(meta.getDocStatus(),
						RetrialDocStatusEnum.已覆核未核定.getCode())) {
					// 已覆核未核定 → 已覆核已核定. 會分[UI,BATCH]. 這裡只有 UI
					_lackSign(msgSet, meta, lrsConstants.BRANCHTYPE.受檢單位,
							no_1_L1, no_1_L4, no_1_L5, no_1_L2);
				} else {
					// 其它狀況(受檢單位待覆核, 覆審組就按上傳)
				}

				errMsg = StringUtils.join(msgSet, "<br/>");
			}

			if (Util.isNotEmpty(errMsg)) {
				if (addMetaDesc) {
					errMsg = (meta.getCustId() + " " + meta.getCustName()
							+ "<br/>" + errMsg);
				}
				throw new CapMessageException(errMsg, getClass());
			}

			flowSimplifyService.flowNext(meta.getOid(), decisionExpr);

			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(),
					user.getUserId());
		}
		return defaultResult(params, meta, result);
	}

	private boolean _errorBranchComm(L170M01A meta) {
		L170M01F l170m01f = retrialService.findL170M01F(meta);
		if (l170m01f == null) {
			return false;
		}
		return Util.isEmpty(Util.trim(l170m01f.getBranchComm()));
	}

	private boolean userId_in_L1(String userId, List<L170M01G> l170m01g_list) {
		boolean r = false;
		for (L170M01G l170m01g : l170m01g_list) {
			if (Util.equals(userId, l170m01g.getStaffNo())) {
				r = true;
			}
		}
		return r;
	}

	private void _lackSign(Set<String> msgSet, L170M01A meta, String signRole,
			boolean lackL1, boolean lackL4, boolean lackL5, boolean lackL2) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String ownBrId = meta.getOwnBrId();
		if (Util.equals(lrsConstants.BRANCHTYPE.受檢單位, signRole)) {
			_lackSignHelper(msgSet, lackL1, signRole, "reviewBrn.1.appraiser");
			_lackSignHelper(msgSet, lackL4, signRole, "reviewBrn.1.reChecker");
			_lackSignHelper(msgSet, lackL5, signRole, "reviewBrn.1.manager");
			// J-111-0033_05097_B1001 Web e-Loan
			// 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位

			// 行員編號 003895
			// 提問單位 045-鳳山分行
			// 姓名 黃秀梅 (女) 職稱 襄理(十一職等)
			// 辦公室分機 216
			// 提問內容 覆審案件報告表回復
			// 提示
			// 請輸入簽章欄(受檢單位)帳戶管理員
			// 但是該公司未達AO標準

			// [下午 05:32] 詹琨琪(企金業務處,科長)
			// 本行帳戶管理員辦法已經改了....現在只是要企金戶都要配一名AO
			// 早期才有分

			_lackSignHelper(msgSet, lackL2, signRole, "reviewBrn.1.ao");
		} else if (Util.equals(lrsConstants.BRANCHTYPE.覆審單位, signRole)) {
			boolean chkL4 = LMSUtil.isSpecialBranch(ownBrId)
					|| retrialService.is_flowClass_throughBr(meta);

			// 目前007,025,201 分行, 要檢查 覆審組的L5 為必填
			boolean chkL5 = LMSUtil.isSpecialBranch(ownBrId);

			// J-111-0622_05097_B1002 Web
			// e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
			// 如果是931覆審的007、201，覆審種類為 CTLTYPE A、C案件，要比照目前931的做法
			if (chkL5) {
				if (retrialService.isSpecialBranchChgTo931_lrs(meta)) {
					chkL5 = false;
				}
			}

			_lackSign_R(chkL4, chkL5, msgSet, lackL1, lackL4, lackL5);

		}
	}

	private void _lackSign_R(boolean chkL4, boolean chkL5, Set<String> msgSet,
			boolean lackL1, boolean lackL4, boolean lackL5) {
		String signRole = lrsConstants.BRANCHTYPE.覆審單位;
		_lackSignHelper(msgSet, lackL1, signRole, "reviewBrn.2.appraiser");

		if (chkL4) {
			_lackSignHelper(msgSet, lackL4, signRole, "reviewBrn.2.reChecker");
		}

		if (chkL5) {
			_lackSignHelper(msgSet, lackL5, signRole, "reviewBrn.2.manager");
		}
	}

	private void _lackSignHelper(Set<String> msgSet, boolean bLack,
			String signRole, String propKey) {
		if (bLack) {
			String roleDesc = "";
			if (Util.equals(lrsConstants.BRANCHTYPE.受檢單位, signRole)) {
				roleDesc = prop_lms1700m01.getProperty("reviewBrn.1");
			} else if (Util.equals(lrsConstants.BRANCHTYPE.覆審單位, signRole)) {
				roleDesc = prop_lms1700m01.getProperty("reviewBrn.2");
			}
			/*
			 * ui_lms1700.msg09=請輸入 label.signature=簽章欄 reviewBrn.1=受檢單位
			 */
			String s = prop_lms1700m01.getProperty("ui_lms1700.msg09")
					+ prop_lms1700m01.getProperty("label.signature") + "("
					+ roleDesc + ")" + prop_lms1700m01.getProperty(propKey);
			msgSet.add(s);
		}
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newL181M01A(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		L170M01A meta = retrialService.findL170M01A_oid(params
				.getString(EloanConstants.OID));
		String elfBranch = meta.getOwnBrId();
		// ===========================
		// 參考 LMS1810M01FormHandler
		// 參考 queryELF412

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		L181M01A existObj = lms1810Service.findInProcessData(elfBranch,
				meta.getCustId(), meta.getDupNo(), new String[] {
						RetrialDocStatusEnum.編製中.getCode(),
						RetrialDocStatusEnum.待覆核.getCode() },
				meta.getCtlType(), user.getUnitNo());
		if (existObj != null) {
			// 分行:{0} 統編: {1}-{2} 已有未覆核資料，請先覆核或刪除
			throw new CapMessageException(MessageFormat.format(
					prop_lms1810m01.getProperty("msg.alreadyHave"), elfBranch,
					meta.getCustId(), meta.getDupNo()), getClass());
		}
		// 參考 newMain
		L181M01A l181m01a = new L181M01A();
		l181m01a.setMainId(IDGenerator.getUUID());
		l181m01a.setOwnBrId(user.getUnitNo());
		l181m01a.setUnitType(UnitTypeEnum.convertToUnitType(user.getUnitType()));
		l181m01a.setTypCd(TypCdEnum.DBU.getCode());
		l181m01a.setDeletedTime(null);

		// ````參考 doCustIdSaved
		l181m01a.setCustId(meta.getCustId());
		l181m01a.setDupNo(meta.getDupNo());
		l181m01a.setCustName(meta.getCustName());
		l181m01a.setElfBranch(elfBranch);

		L181M01B bfObj = new L181M01B();
		L181M01B afObj = new L181M01B();

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		lms1810Service.setterL181Model(l181m01a, bfObj, afObj,
				meta.getCtlType());

		retrialService.save(bfObj);
		retrialService.save(afObj);
		// ````參考 doCustIdSaved

		if (true) {
			// 授權檔
			L181A01A l181a01a = new L181A01A();
			l181a01a.setMainId(l181m01a.getMainId());
			l181a01a.setOwnUnit(l181m01a.getOwnBrId());
			l181a01a.setOwner(user.getUserId());
			l181a01a.setAuthUnit(l181m01a.getOwnBrId());
			l181a01a.setAuthType("1");
			retrialService.save(l181a01a);
		}
		retrialService.save(l181m01a);

		flowSimplifyService.flowStart("LMS1815Flow", l181m01a.getOid(),
				user.getUserId(), user.getUnitNo());

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(EloanConstants.OID, Util.trim(l181m01a.getOid()));
		result.set(EloanConstants.MAIN_OID, Util.trim(l181m01a.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, l181m01a.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(l181m01a.getMainId()));
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult impData(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ---
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String flag = Util.trim(params.getString("flag"));
		L170M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL170M01A_oid(mainOid);
			// ---
			Map<String, String> showItem = new HashMap<String, String>();
			lms1700Service.impData(meta, flag, showItem);
			if (true) {
				String[] proc_arr = { "rltGuarantor", "rltBorrower" };
				for (String column : proc_arr) {
					if (showItem.containsKey(column)
							&& Util.isEmpty(Util.trim(showItem.get(column)))) {
						showItem.put(column,
								prop_lms1700m01.getProperty("ui_lms1700.msg26"));
					}
				}
			}
			for (String k : showItem.keySet()) {
				String v = showItem.get(k);
				result.set(k, v);
			}
		}
		return defaultResult(params, meta, result);
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteMeta(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ---

		CapAjaxFormResult result = new CapAjaxFormResult();
		String failmsg = "";
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L170M01A meta = retrialService.findL170M01A_oid(mainOid);
		Map<String, String> lockedUser = docCheckService.listLockedDocUser(meta
				.getMainId());
		if (lockedUser != null) {
			failmsg = meta.getCustId() + " "
					+ getPopMessage("EFD0055", lockedUser);
		}
		if (Util.isEmpty(failmsg)) {
			String docStatus = Util.trim(meta.getDocStatus());
			if (Util.notEquals(docStatus,
					RetrialDocStatusEnum.區中心_編製中.getCode())) {
				failmsg = meta.getCustId() + " 非「覆審人員編製中」之文件，不可刪除。";
			}
		}

		if (Util.isEmpty(failmsg)) {
			// check upDate
			L170M01F l170m01f = meta.getL170m01f();
			if (l170m01f != null) {
				if (l170m01f.getUpDate() != null) {
					failmsg = meta.getCustId() + " 已上傳覆審控制檔，不可刪除。";
				}
			}
		}

		if (Util.isEmpty(failmsg)) {
			lms1700Service.delMeta(meta);
		}

		result.set("failmsg", failmsg);
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult chkL170M01A(PageParameters params)
			throws CapException {
		String ownBrId = Util.trim(params.getString("ownBrId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		String ctlType = Util.trim(params.getString("ctlType",
				LrsUtil.CTLTYPE_主辦覆審));
		String confirmMsg = "";

		CapAjaxFormResult result = new CapAjaxFormResult();
		List<L170M01A> list = lms1700Service.findUnFinish(ownBrId, custId,
				dupNo);
		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, Integer> map = new HashMap<String, Integer>();
			for (L170M01A l170m01a : list) {
				String desc = lms1700Service.l170m01a_docStatusDesc(l170m01a);
				if (map.containsKey(desc)) {
					map.put(desc, map.get(desc) + 1);
				} else {
					map.put(desc, 1);
				}
			}
			List<String> msgList = new ArrayList<String>();
			for (String desc : map.keySet()) {
				int cnt = map.get(desc);
				msgList.add(cnt + "筆" + desc);
			}
			confirmMsg = ownBrId + "分行" + custId + " " + dupNo + "<br/>已有 [ "
					+ StringUtils.join(msgList, "、") + " ] " + "<br/>是否仍要新增？";
		}
		result.set("ownBrId", ownBrId);
		result.set("custId", custId);
		result.set("dupNo", dupNo);
		result.set("custName", custName);
		result.set("confirmMsg", confirmMsg);
		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		result.set("ctlType", ctlType);
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addL170M01A(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ---
		String ownBrId = Util.trim(params.getString("ownBrId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		String ctlType = Util
				.equals(Util.trim(params.getString("ctlType")), "") ? LrsUtil.CTLTYPE_主辦覆審
				: Util.trim(params.getString("ctlType"));

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (Util.isEmpty(ownBrId)) {
			// enterBranch=請輸入分行代碼
			throw new CapMessageException(
					prop_lms1810m01.getProperty("enterBranch"), getClass());
		}
		if (Util.isEmpty(custId)) {
			// enterCustId=請輸入借款人統一編號
			throw new CapMessageException(
					prop_lms1810m01.getProperty("enterCustId"), getClass());
		}

		List<String> failMsgList = new ArrayList<String>();
		String retrialBrNo = user.getUnitNo();
		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		L170M01A l170m01a = lms1700Service.addNewL170(retrialBrNo, ownBrId,
				custId, dupNo, custName, true, failMsgList, ctlType);
		if (l170m01a == null) {
			String errMsg = RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤);
			if (CollectionUtils.isNotEmpty(failMsgList)) {
				errMsg = StringUtils.join(failMsgList, "<br/>");
			}
			throw new CapMessageException(errMsg, getClass());
		} else {
			l170m01a.setRetrialDate(null);
			flowSimplifyService.flowStart(LMS1700Flow.LMS1700FLOW,
					l170m01a.getOid(), user.getUserId(), user.getUnitNo());
			retrialService.save(l170m01a);
		}

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getBranchComm(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		L170M01A meta = retrialService.findL170M01A_oid(mainOid);
		result.set("reg_branchComm", meta.getL170m01f().getBranchComm());
		return defaultResult(params, meta, result);
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveBranchComm(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);

		L170M01A meta = retrialService.findL170M01A_oid(mainOid);
		L170M01F l170m01f = meta.getL170m01f();
		CapBeanUtil.map2Bean(params, l170m01f, new String[] { "branchComm" });
		if (Util.isNotEmpty(Util.trim(l170m01f.getBranchComm()))) {
			// 若有輸入受檢單位洽辦情形，不得＜
			if (Util.trim(l170m01f.getBranchComm()).getBytes().length < 4) {
				throw new CapMessageException(
						prop_lms1700m01.getProperty("ui_lms1700.msg27"),
						getClass());
			}
		}
		retrialService.save(l170m01f);
		retrialService.save(meta);
		// ==========
		result.set("branchComm", l170m01f.getBranchComm());
		return result;
	}

	public IResult check_bef_btnSend(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L170M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL170M01A_oid(mainOid);

			if (_errorBranchComm(meta)) {
				// 請輸入受檢單位洽辦情形
				throw new CapMessageException(
						prop_lms1700m01.getProperty("ui_lms1700.msg09")
								+ prop_lms1700m01
										.getProperty("L170M01F.branchComm"),
						getClass());
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getSignList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String signRole = Util.trim(params.getString("signRole"));
		L170M01A meta = retrialService.findL170M01A_oid(mainOid);

		String chooseUnitNo = "";

		if (Util.equals("E", signRole)) {
			// 受檢單位
			chooseUnitNo = meta.getOwnBrId();
		} else if (Util.equals("R", signRole)) {
			check_pid(meta);

			// 覆審單位
			L180M01A l180m01a = retrialService.findL180M01A(meta);
			if (l180m01a != null) {
				chooseUnitNo = l180m01a.getOwnBrId();
			}
		}

		if (Util.isEmpty(chooseUnitNo)) {
			throw new CapMessageException("chooseUnitNo isEmpty", getClass());
		}
		_set_signList(result, chooseUnitNo);

		if (Util.equals("R", signRole)
				&& retrialService.is_flowClass_throughBr(meta)
				&& Util.equals(RetrialDocStatusEnum.已覆核未核定.getCode(),
						meta.getDocStatus())) {
			// 若已有 L4, L5, 不跳出
			List<L170M01G> l170m01g_list = retrialService
					.findL170M01G_l170m01a(meta);

			if (l170m01g_list != null && l170m01g_list.size() > 0) {
				boolean has_R_L4 = CollectionUtils.isNotEmpty(retrialService
						.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
								lrsConstants.BRANCHTYPE.覆審單位, "L4"));
				boolean has_R_L5 = CollectionUtils.isNotEmpty(retrialService
						.findL170M01G_byBranchTypeStaffJob(l170m01g_list,
								lrsConstants.BRANCHTYPE.覆審單位, "L5"));
				if (has_R_L4 || has_R_L5) {
					result.set("canSkip", "Y");
				}
			}
		}
		return result;
	}

	private void _set_signList(CapAjaxFormResult result, String chooseUnitNo) {
		// J-111-0033_05097_B1001 Web e-Loan
		// 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
		SignEnum[] signs_l2 = { SignEnum.經辦人員, SignEnum.甲級主管, SignEnum.乙級主管,
				SignEnum.首長, SignEnum.單位主管 };

		SignEnum[] signs_l4 = { SignEnum.甲級主管, SignEnum.乙級主管, SignEnum.首長,
				SignEnum.單位主管 };
		SignEnum[] signs_l5 = { SignEnum.甲級主管, SignEnum.首長, SignEnum.單位主管 };
		Map<String, String> l2_list = userInfoService.findByBrnoAndSignId(
				chooseUnitNo, signs_l2);
		Map<String, String> l4_list = userInfoService.findByBrnoAndSignId(
				chooseUnitNo, signs_l4);
		Map<String, String> l5_list = userInfoService.findByBrnoAndSignId(
				chooseUnitNo, signs_l5);
		result.set("chooseUnitNo", chooseUnitNo);
		result.set("l4_list", new CapAjaxFormResult(l4_list));
		result.set("l5_list", new CapAjaxFormResult(l5_list));
		// J-111-0033_05097_B1001 Web e-Loan
		// 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
		result.set("l2_list", new CapAjaxFormResult(l2_list));
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignList(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		String key = "saveSignFlag";
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(key, "N");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String signRole = Util.trim(params.getString("signRole"));
		// J-111-0033_05097_B1001 Web e-Loan
		// 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
		List<String> l2List = _staffStr(params.getString("l2Arr"));
		List<String> l4List = _staffStr(params.getString("l4Arr"));
		List<String> l5List = _staffStr(params.getString("l5Arr"));
		L170M01A meta = retrialService.findL170M01A_oid(mainOid);
		// =====================
		String branchType = "";
		if (Util.equals("E", signRole)) {
			// 受檢單位
			branchType = lrsConstants.BRANCHTYPE.受檢單位;
		} else if (Util.equals("R", signRole)) {
			// 覆審單位
			branchType = lrsConstants.BRANCHTYPE.覆審單位;
		}

		// J-111-0033_05097_B1001 Web e-Loan
		// 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
		retrialService.saveL170M01G_L1L4L5(meta, branchType, user, l4List,
				l5List, l2List);
		// ==========
		_signInfo(result, meta);
		result.set(key, "Y");
		return result;
	}

	private List<String> _staffStr(String src) {
		List<String> list = new ArrayList<String>();
		for (String raw_staffNo : Util.trim(src).split("\\|")) {
			String s = Util.trim(raw_staffNo);
			if (Util.isEmpty(s)) {
				continue;
			}
			if (list.contains(s)) {
				continue;
			}
			list.add(s);
		}
		return list;
	}

	private void _signInfo(CapAjaxFormResult result, L170M01A meta) {

		List<L170M01G> list = retrialService.findL170M01G_l170m01a(meta);
		String branch_L1 = _filter_branchType_staffJob(list,
				lrsConstants.BRANCHTYPE.受檢單位, UtilConstants.STAFFJOB.經辦L1);
		// J-111-0033_05097_B1001 Web e-Loan
		// 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
		String branch_L2 = _filter_branchType_staffJob(list,
				lrsConstants.BRANCHTYPE.受檢單位, UtilConstants.STAFFJOB.帳戶管理員L2);
		String branch_L4 = _filter_branchType_staffJob(list,
				lrsConstants.BRANCHTYPE.受檢單位, UtilConstants.STAFFJOB.執行覆核主管L4);
		String branch_L5 = _filter_branchType_staffJob(list,
				lrsConstants.BRANCHTYPE.受檢單位, UtilConstants.STAFFJOB.單位授權主管L5);

		String area_L1 = _filter_branchType_staffJob(list,
				lrsConstants.BRANCHTYPE.覆審單位, UtilConstants.STAFFJOB.經辦L1);
		String area_L4 = _filter_branchType_staffJob(list,
				lrsConstants.BRANCHTYPE.覆審單位, UtilConstants.STAFFJOB.執行覆核主管L4);
		String area_L5 = _filter_branchType_staffJob(list,
				lrsConstants.BRANCHTYPE.覆審單位, UtilConstants.STAFFJOB.單位授權主管L5);

		// 受檢
		result.set("appraiser1", branch_L1);
		result.set("reCheck1", branch_L4);
		result.set("manager1", branch_L5);
		// J-111-0033_05097_B1001 Web e-Loan
		// 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
		result.set("ao1", branch_L2);
		// 覆審組
		result.set("appraiser2", area_L1);
		result.set("reCheck2", area_L4);
		result.set("manager2", area_L5);
	}

	private String _filter_branchType_staffJob(List<L170M01G> list,
			String branchType, String staffJob) {
		List<L170M01G> filter = retrialService
				.findL170M01G_byBranchTypeStaffJob(list, branchType, staffJob);
		List<String> staffNoStr = new ArrayList<String>();
		if (CollectionUtils.isNotEmpty(filter)) {
			for (L170M01G l170m01g : filter) {
				staffNoStr.add(Util.trim(l170m01g.getStaffNo()) + " "
						+ Util.trim(lmsService.getUserName(l170m01g
								.getStaffNo())));
			}
		}

		return StringUtils.join(staffNoStr, "&nbsp;&nbsp;、<br/>");
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult end_to_01A(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String forceFlag = Util.trim(params.getString("forceFlag"));
		String targetDocStatus = Util.trim(params.getString("targetDocStatus"));
		boolean passedFlag = false;

		if (Util.isNotEmpty(mainOid)) {
			L170M01A meta = retrialService.findL170M01A_oid(mainOid);
			if (Util.equals(RetrialDocStatusEnum.已覆核已核定.getCode(),
					meta.getDocStatus())
					&& LrsUtil.isFromNotes(meta) == false) {
				L170M01G l170m01g = retrialService
						.findL170M01G_first_L1_retrialTeam(meta);
				if (l170m01g != null
						&& Util.notEquals(l170m01g.getStaffNo(),
								user.getUserId())) {
					if (Util.equals("Y", forceFlag)) {
						// 不卡 userId
					} else {
						// ui_lms1700.msg08=本案由{0}覆審，不可再由您異動
						throw new CapMessageException(
								MessageFormat.format(
										prop_lms1700m01
												.getProperty("ui_lms1700.msg08"),
										Util.isNotEmpty(l170m01g.getStaffName()) ? l170m01g
												.getStaffName() : l170m01g
												.getStaffNo()), getClass());
					}
				}

				passedFlag = true;
				// 要把 TEMPSAVE_RUN 指定為 N
				if (retrialService.is_flowClass_throughBr(meta)
						&& Util.equals(targetDocStatus,
								RetrialDocStatusEnum.編製中.getCode())) {
					Map<String, Object> map = new HashMap<String, Object>();
					map.put(LMS1700Flow.ZINITPARAM, "to010");
					// 退到[受檢單位]編製中
					flowSimplifyService.flowStart(LMS1700Flow.LMS1700FLOW,
							meta.getOid(), user.getUserId(), user.getUnitNo(),
							map);
				} else if (retrialService.is_flowClass_throughBr(meta)
						&& Util.equals(targetDocStatus,
								RetrialDocStatusEnum.已覆核未核定.getCode())) {
					// 2022/04/18 授審處連喬凱來電 分處對覆審報告表有回頭打考評表之需求
					// 1. 「已覆核已核定」之覆審報告表可以退為「已覆核未核定」修改_限制為有考評表且有傳送至分行的覆審報告表
					// 2. 不限上傳者本人，任何人都可以修改
					Map<String, Object> map = new HashMap<String, Object>();
					map.put(LMS1700Flow.ZINITPARAM, "to050");
					// 退到[已覆核未核定]
					flowSimplifyService.flowStart(LMS1700Flow.LMS1700FLOW,
							meta.getOid(), user.getUserId(), user.getUnitNo(),
							map);
				} else {
					if (true) {
						List<String> l4List = new ArrayList<String>();
						List<String> l5List = new ArrayList<String>();
						// 007提出，在退回時，要把簽章欄清掉
						// 列印時才不會印出 簽章欄
						retrialService.saveL170M01G_L1L4L5(meta,
								lrsConstants.BRANCHTYPE.覆審單位, user, l4List,
								l5List, null);
					}
					// 退到[覆審單位]編製中
					flowSimplifyService.flowStart(LMS1700Flow.LMS1700FLOW,
							meta.getOid(), user.getUserId(), user.getUnitNo());
				}
				retrialService.saveReInitFlow(meta);// 由編製完成 → 編製中，要再存一次。目的： 把
													// bTempData 的資料刪掉
			}

		}
		result.set("passedFlag", passedFlag ? "Y" : "N");

		return result;
	}

	public IResult onlyUp412(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ---
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp now = CapDate.getCurrentTimestamp();

		L170M01A meta = retrialService.findL170M01A_oid(params
				.getString(EloanConstants.MAIN_OID));

		String errMsg = lms1700Service.checkIncompleteMsg(meta);
		if (Util.isNotEmpty(errMsg)) {
			throw new CapMessageException(errMsg, getClass());
		}

		if (true) {
			meta.setApprover(user.getUserId());
			meta.setApproveTime(now);

			L170M01F l170m01f = meta.getL170m01f();
			l170m01f.setUpDate(now);
			// ---
			retrialService.save(l170m01f);
		}
		retrialService.save(meta);

		check_pid(meta);

		lms1700Service.up_to_mis(meta);

		result.add(query(params));
		return result;
	}

	private void check_pid(L170M01A meta) throws CapMessageException {
		if (Util.isEmpty(Util.trim(meta.getPid()))) {
			// ui_lms1700.msg23=請先執行「{0}」
			throw new CapMessageException(MessageFormat.format(
					prop_lms1700m01.getProperty("ui_lms1700.msg23"),
					prop_lms1700m01.getProperty("button.AddToL180M01A")),
					getClass());
		}
	}

	/**
	 * 刪除上傳檔案
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteUploadFile(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
		// L224與聯徵查詢結果
		String fid = params.getString("fileOid");

		// 後台管理->系統設定維護->LMS_RETRIAL_CAN_DEL_RPA_FILE 覆審報告表可以刪除RPA產生的資料
		String LMS_RETRIAL_CAN_DEL_RPA_FILE = Util.trim(lmsService
				.getSysParamDataValue("LMS_RETRIAL_CAN_DEL_RPA_FILE"));

		DocFile doc = docFileService.findByOidAndSysId(fid, "LMS");
		if (doc != null
				&& Util.equals(Util.trim(doc.getFlag()), "R")
				&& ("crs".equals(Util.trim(doc.getFieldId())) || "lrs"
						.equals(Util.trim(doc.getFieldId())))) {
			// RPA產生的檔案
			boolean canDel = false;
			if (Util.notEquals(LMS_RETRIAL_CAN_DEL_RPA_FILE, "")
					&& Util.notEquals(LMS_RETRIAL_CAN_DEL_RPA_FILE, "XXX")) {
				String[] item = LMS_RETRIAL_CAN_DEL_RPA_FILE.split(",");
				for (String xx : item) {
					if (Util.equals(Util.trim(user.getUnitNo()), xx)) {
						canDel = true;
						break;
					}
				}
			}

			if (!canDel) {
				throw new CapMessageException("覆審人員不可刪除RPA產生之檔案", getClass());
			}

		}

		if (retrialService.delfile(fid)) {
			// ...
		}
		return new CapAjaxFormResult();
	}

	@DomainAuth(value = AuthType.Modify)
	public IResult chgRetrialDate(PageParameters params)
			throws CapException {

		L170M01A meta = retrialService.findL170M01A_oid(params
				.getString(EloanConstants.MAIN_OID));
		if (meta != null) {
			CapBeanUtil.map2Bean(params, meta, new String[] { "retrialDate" });
			retrialService.save(meta);
		}
		return new CapAjaxFormResult();
	}

	/**
	 * 覆審項目預設值
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult l170m01d_defaultVal(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L170M01A l170m01a = retrialService.findL170M01A_oid(mainOid);
		boolean hasCMS = "Y".equals(Util.trim(params.getString("hasCMS")));
		List<String> msgList = new ArrayList<String>();
		Map<String, String> map = retrialService.get_l170m01d_defaultVal(
				l170m01a, hasCMS, msgList);
		result.set("defVal", new CapAjaxFormResult(map));
		if (msgList.size() > 0) {
			result.set("msg", StringUtils.join(msgList, "<br/>"));
		}

		return defaultResult(params, l170m01a, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult getFinData(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		L170M01C l170m01c = new L170M01C();
		if (true) {
			String[] _f101m01a_mainIds = Util.trim(
					params.getString("f101m01a_mainIds")).split("\\|");

			l170m01c.setRatioNo1(Util.trim(params.getString("ratioNo1")));
			l170m01c.setRatioNo2(Util.trim(params.getString("ratioNo2")));
			l170m01c.setRatioNo3(Util.trim(params.getString("ratioNo3")));
			l170m01c.setRatioNo4(Util.trim(params.getString("ratioNo4")));

			lms1700Service.getFinData(l170m01c, _f101m01a_mainIds);
		}
		Map<String, String> finRatioMap = retrialService.get_lrs_FinRatio();
		_l170m01c_toResult(l170m01c, finRatioMap, result);
		return result;
	}

	public IResult callBatch(PageParameters params)
			throws CapException, IOException {

		int jq_timeout = Util.parseInt(params.getString("jq_timeout"));
		if (jq_timeout == 0) {
			jq_timeout = 60 * 60;// default
		}

		String act = Util.trim(params.getString("act"));
		List<String> paramList = new ArrayList<String>();

		// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
		if (Util.equals("imp_l170m01b", act)
				|| Util.equals("imp_l170m01c", act)
				|| Util.equals("update_ptMgrId", act)
				|| Util.equals("update_l170m01a_rpid", act)) {

			paramList.add("oids");
			paramList.add("retrialDate");
			paramList.add("unitNo");
		}
		if (Util.equals("update_ptMgrId", act)) {
			paramList.add("ptMgrId");
		}
		// ---
		EloanSubsysBatReqMessage esbrm = new EloanSubsysBatReqMessage();
		esbrm.setUrl(SysParamConstants.SYS_URL_LMS);
		esbrm.setReqFormat(EloanSubsysBatReqMessage.REQ_FMT_JSON);
		esbrm.setServiceId("lrs2BatchServiceImpl");
		esbrm.setTimeout(jq_timeout);
		esbrm.setLocalUrl(true);

		JSONObject requestJSON = new JSONObject();
		requestJSON.element("act", act);
		for (String k : paramList) {
			requestJSON.element(k, params.getString(k));
		}
		// ---
		esbrm.setRequestJSON(requestJSON);

		String respStr = eloanBatchClient.send(esbrm);
		logger.debug("send to batch data={}", respStr);
		// =============
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("r", respStr);
		return result;
	}

	public IResult check_impBySelOrDate(PageParameters params)
			throws CapException, IOException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		List<String> errMsg = new ArrayList<String>();
		List<L170M01A> list = lms1700Service.impBySelOrDate(
				Util.trim(params.getString("oids")),
				Util.trim(params.getString("retrialDate")),
				Util.trim(params.getString("unitNo")), errMsg);
		if (CollectionUtils.isNotEmpty(errMsg)) {
			throw new CapMessageException(StringUtils.join(errMsg, "，"),
					getClass());
		} else {
			// passed
			logger.trace("get l170m01a_size=" + list.size());
		}

		return result;
	}

	public IResult check_befMeta(PageParameters params)
			throws CapException, IOException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		List<String> errMsg = new ArrayList<String>();
		List<L170M01A> list = lms1700Service.impBySelOrDate(
				Util.trim(params.getString("oids")),
				Util.trim(params.getString("retrialDate")),
				Util.trim(params.getString("unitNo")), errMsg);

		String msg = "";
		String has_befMeta_oids = "";
		String befMeta_oids = "";
		if (true) {
			List<String> lrDate_noBefMeta_list = new ArrayList<String>();
			List<String> has_befMeta_oid_list = new ArrayList<String>();
			List<String> befMeta_oid_list = new ArrayList<String>();
			for (L170M01A meta : list) {
				L170M01A befMeta = retrialService.findL170M01A_bef(meta);
				if (befMeta != null) {
					has_befMeta_oid_list.add(meta.getOid());
					befMeta_oid_list.add(befMeta.getOid());
				} else {
					if (meta.getLastRetrialDate() == null) {
						// skip
					} else {
						lrDate_noBefMeta_list.add(Util.trim(meta.getCustId())
								+ " " + Util.trim(meta.getCustName()));
					}
				}
			}
			has_befMeta_oids = StringUtils.join(has_befMeta_oid_list, "|");
			befMeta_oids = StringUtils.join(befMeta_oid_list, "|");
			if (lrDate_noBefMeta_list.size() > 0) {
				msg = "有前次覆審日但於Web e-Loan 查無覆審報告表，" + "共 "
						+ lrDate_noBefMeta_list.size() + " 筆：" + "<br/>"
						+ StringUtils.join(lrDate_noBefMeta_list, "、") + "。";

			}
		}

		if (Util.isNotEmpty(msg)) {
			result.set("msg", msg);
		}
		result.set("has_befMeta_oids", has_befMeta_oids);
		result.set("befMeta_oids", befMeta_oids);
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryLNDetail(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String l170m01b_oid = Util.trim(params.getString("l170m01b_oid"));
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		L170M01A meta = retrialService.findL170M01A_oid(mainOid);
		L170M01B l170m01b = null;
		if (Util.isNotEmpty(l170m01b_oid)) {
			l170m01b = retrialService.findL170M01B_oid(l170m01b_oid);
		}
		if (l170m01b == null) {
			l170m01b = new L170M01B();
			_set_default_cntrNo(l170m01b);
		}

		LMSUtil.addMetaToResult(result, l170m01b, new String[] { "loanTP",
				"subject", "cntrNo", "revolve", "quotaCurr", "balCurr",
				"fromDate", "endDate", "guaranteeName", "estCurr", "loanCurr",
				"insMemo", "majorMemo" });
		result.set("newCase", Util.equals("Y", l170m01b.getNewCase()) ? "Y"
				: "");
		result.set(
				"quotaAmt",
				l170m01b.getQuotaAmt() == null ? "" : CrsUtil
						.amtDivide1000(l170m01b.getQuotaAmt()));
		result.set(
				"balAmt",
				l170m01b.getBalAmt() == null ? "" : CrsUtil
						.amtDivide1000(l170m01b.getBalAmt()));
		result.set(
				"estAmt",
				l170m01b.getEstAmt() == null ? "" : CrsUtil
						.amtDivide1000(l170m01b.getEstAmt()));
		result.set(
				"loanAmt",
				l170m01b.getLoanAmt() == null ? "" : CrsUtil
						.amtDivide1000_floor(l170m01b.getLoanAmt()));
		result.set("retrialDate",
				Util.trim(TWNDate.toAD(meta.getRetrialDate())));
		result.set("custInfo", meta.getCustId() + " " + meta.getDupNo() + " "
				+ meta.getCustName());
		result.set("metaDocStatus", lms1700Service.l170m01a_docStatusDesc(meta));
		result.set("createBy", prop_lms1700m02.getProperty(l170m01b
				.getLnDataDate() == null ? "label.createBy.PEO"
				: "label.createBy.SYS"));
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveLNDetail(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		String key = "saveLNFlag";
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(key, "N");

		String l170m01b_oid = Util.trim(params.getString("l170m01b_oid"));
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		L170M01A meta = retrialService.findL170M01A_oid(mainOid);
		L170M01B l170m01b = null;
		if (Util.isNotEmpty(l170m01b_oid)) {
			l170m01b = retrialService.findL170M01B_oid(l170m01b_oid);
		}
		if (l170m01b == null) {
			l170m01b = new L170M01B();

			l170m01b.setMainId(meta.getMainId());
			l170m01b.setCustId(meta.getCustId());
			l170m01b.setDupNo(meta.getDupNo());
			l170m01b.setCntrNo("");
			l170m01b.setLoanNo("");
		}

		List<String> col_list = new ArrayList<String>();
		if (l170m01b.getLnDataDate() == null) {
			// 儲存-人工產生(subject 另外判斷)
			_add_to_list(col_list, new String[] { "cntrNo", "quotaCurr",
					"quotaAmt", "fromDate", "endDate", "revolve" });
		}

		if (LrsUtil.isL170M01B_Subject_W(l170m01b)) {
			_add_to_list(col_list, new String[] { "subject" });
		}

		// 儲存-人工產生、系統產生 都有的欄位
		_add_to_list(col_list, new String[] { "newCase", "balCurr", "balAmt",
				"guaranteeName", "estCurr", "estAmt", "loanCurr", "loanAmt",
				"insMemo", "majorMemo" });

		CapBeanUtil.map2Bean(params, l170m01b,
				col_list.toArray(new String[col_list.size()]));
		// ---
		_set_default_cntrNo(l170m01b);
		l170m01b.setMajorMemo(Util.truncateString(l170m01b.getMajorMemo(),
				MAXLEN_L170M01B_MAJORMEMO));
		BigDecimal amt1000 = new BigDecimal("1000");
		for (String column : col_list) {
			if (Util.equals("quotaAmt", column)) {
				if (l170m01b.getQuotaAmt() != null) {
					l170m01b.setQuotaAmt(amt1000.multiply(l170m01b
							.getQuotaAmt()));
				} else {
					l170m01b.setQuotaCurr("");
				}
			} else if (Util.equals("balAmt", column)) {
				if (l170m01b.getBalAmt() != null) {
					l170m01b.setBalAmt(amt1000.multiply(l170m01b.getBalAmt()));
				} else {
					l170m01b.setBalCurr("");
				}
			} else if (Util.equals("estAmt", column)) {
				if (l170m01b.getEstAmt() != null) {
					l170m01b.setEstAmt(amt1000.multiply(l170m01b.getEstAmt()));
				} else {
					l170m01b.setEstCurr("");
				}
			} else if (Util.equals("loanAmt", column)) {
				if (l170m01b.getLoanAmt() != null) {
					l170m01b.setLoanAmt(amt1000.multiply(l170m01b.getLoanAmt()));
				} else {
					l170m01b.setLoanCurr("");
				}
			}
		}
		retrialService.save(l170m01b);

		String errMsg = check_l170m01b_errMsg(l170m01b);
		if (Util.isNotEmpty(errMsg)) {
			throw new CapMessageException(errMsg, getClass());
		}
		result.set(key, "Y");
		return result;
	}

	private String check_l170m01b_errMsg(L170M01B l170m01b) {
		List<String> r = new ArrayList<String>();
		_check_curr_amt(r, l170m01b.getQuotaCurr(), l170m01b.getQuotaAmt(),
				prop_lms1700m02.getProperty("label.quotaAmt"));
		_check_curr_amt(r, l170m01b.getBalCurr(), l170m01b.getBalAmt(),
				prop_lms1700m02.getProperty("label.balAmt"));
		_check_curr_amt(r, l170m01b.getEstCurr(), l170m01b.getEstAmt(),
				prop_lms1700m02.getProperty("label.estAmt"));
		_check_curr_amt(r, l170m01b.getLoanCurr(), l170m01b.getLoanAmt(),
				prop_lms1700m02.getProperty("label.loanAmt"));
		return StringUtils.join(r, "<br/>");
	}

	private void _check_curr_amt(List<String> r, String curr, BigDecimal amt,
			String desc_col) {
		boolean emptyCurr = Util.isEmpty(Util.trim(curr));
		if (emptyCurr && amt != null) {
			// ui_lms1700.msg21=無幣別、有金額
			r.add("【" + desc_col + "】"
					+ prop_lms1700m01.getProperty("ui_lms1700.msg21"));
		} else if (!emptyCurr && amt == null) {
			// ui_lms1700.msg22=無金額、有幣別
			r.add("【" + desc_col + "】"
					+ prop_lms1700m01.getProperty("ui_lms1700.msg22"));
		}
	}

	private void _set_default_cntrNo(L170M01B l170m01b) {
		if (Util.isEmpty(Util.trim(l170m01b.getCntrNo()))) {
			l170m01b.setCntrNo("N.A.");
		}
	}

	private void _add_to_list(List<String> r, String[] arr) {
		for (String s : arr) {
			r.add(s);
		}
	}

	@DomainAuth(AuthType.Modify)
	public IResult importLNSingle(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L170M01A meta = retrialService.findL170M01A_oid(mainOid);

		retrialService.importLNtoL170M01B(meta);
		if (true) {
			Map<String, String> showItem = new HashMap<String, String>();
			lms1700Service.impData(meta, "07", showItem);
			lms1700Service.impData(meta, "08", showItem);
		}

		_l170m01b_toResult(meta, result);
		return defaultResult(params, meta, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult deleteLN(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L170M01A meta = retrialService.findL170M01A_oid(mainOid);

		retrialService.delLNtoL170M01B(meta,
				retrialService.findL170M01B_orderBy(meta));
		if (true) {
			Map<String, String> showItem = new HashMap<String, String>();
			lms1700Service.impData(meta, "07", showItem);
			lms1700Service.impData(meta, "08", showItem);
		}

		_l170m01b_toResult(meta, result);
		return defaultResult(params, meta, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult deleteLN_PEO(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L170M01A meta = retrialService.findL170M01A_oid(mainOid);

		List<L170M01B> dcList = new ArrayList<L170M01B>();
		if (true) {
			String oids = Util.trim(params.getString("oids"));
			if (Util.isNotEmpty(oids)) {
				Set<String> dcSet = new HashSet<String>();
				String[] dc_oidArr = oids.split("\\|");
				for (String oid : dc_oidArr) {
					dcSet.add(oid);
				}
				List<L170M01B> l170m01b_list = retrialService
						.findL170M01B_orderBy(meta);
				if (CollectionUtils.isNotEmpty(l170m01b_list)) {
					for (L170M01B l170m01b : l170m01b_list) {
						if (dcSet.contains(l170m01b.getOid())) {
							dcList.add(l170m01b);
						}
					}
				}
			}
		}

		retrialService.delLNtoL170M01B(meta, dcList);
		if (true) {
			Map<String, String> showItem = new HashMap<String, String>();
			lms1700Service.impData(meta, "07", showItem);
			lms1700Service.impData(meta, "08", showItem);
		}

		_l170m01b_toResult(meta, result);
		return defaultResult(params, meta, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult save_pid(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("pidOkFlag", "N");

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String pid = Util.trim(params.getString("pid"));
		L170M01A meta = retrialService.findL170M01A_oid(mainOid);
		if (Util.isNotEmpty(pid)) {
			meta.setPid(pid);
		}

		retrialService.save(meta);
		result.set("pidOkFlag", "Y");
		return defaultResult(params, meta, result);
	}

	public IResult importBefText(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		L170M01A meta = retrialService.findL170M01A_oid(params
				.getString(EloanConstants.MAIN_OID));
		if (CrsUtil.canSaveL170M01A(user, meta)) {
			L170M01A befMeta = retrialService.findL170M01A_bef(meta);

			if (befMeta == null) {
				throw new CapMessageException(
						prop_lms1700m01.getProperty("ui_lms1700.msg24"),
						getClass());
			}
			lms1700Service.replaceWithBef(meta, befMeta);
		}

		return result;
	}

	public IResult importBefText_batch(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String oids = Util.trim(params.getString("oids"));
		if (Util.isNotEmpty(oids)) {
			String[] oid_arr = oids.split("\\|");
			for (String oid : oid_arr) {
				L170M01A meta = retrialService.findL170M01A_oid(oid);
				if (CrsUtil.canSaveL170M01A(user, meta)) {
					L170M01A befMeta = retrialService.findL170M01A_bef(meta);

					if (befMeta == null) {
						continue;
					}
					lms1700Service.replaceWithBef(meta, befMeta);
				}
			}
		}

		return result;
	}

	public IResult query_is_flowClass_throughBr(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C240M01A c240m01a = new C240M01A();
		c240m01a.setOwnBrId(user.getUnitNo());
		result.set("is_flowClass_throughBr",
				CrsUtil.is_flowClass_throughBr(sysParameterService,c240m01a) ? "Y" : "N");
		return result;
	}

	public IResult getSignList_V(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		_set_signList(result, user.getUnitNo());
		return result;
	}

	public IResult checkSignList_V(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		List<String> l4List = _staffStr(params.getString("l4Arr"));
		List<String> l5List = _staffStr(params.getString("l5Arr"));
		// ==========
		boolean no_2_L1 = false;
		boolean no_2_L4 = CollectionUtils.isEmpty(l4List);
		boolean no_2_L5 = CollectionUtils.isEmpty(l5List);

		Set<String> msgSet = new LinkedHashSet<String>();
		// 目前只有 is_flowClass_throughBr==Y, 才有整批
		boolean chkL4 = true;
		boolean chkL5 = false;
		_lackSign_R(chkL4, chkL5, msgSet, no_2_L1, no_2_L4, no_2_L5);
		if (msgSet.size() > 0) {
			String errMsg = StringUtils.join(msgSet, "<br/>");
			throw new CapMessageException(errMsg, getClass());
		}
		return result;
	}

	@DomainAuth(AuthType.Modify)
	public IResult do_SignAndFlow(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<String> list_notComplate = new ArrayList<String>();
		List<String> list_opened = new ArrayList<String>();
		List<String> list_pid = new ArrayList<String>();
		List<String> list_flowError = new ArrayList<String>();
		LinkedHashMap<String, String> oid_name_map = new LinkedHashMap<String, String>();
		if (true) {
			List<String> l4List = _staffStr(params.getString("l4Arr"));
			List<String> l5List = _staffStr(params.getString("l5Arr"));
			List<L170M01A> list = _oids_l170m01gL1(
					Util.trim(params.getString("oids")), user.getUserId());
			if (list.size() == 0) {
				throw new CapMessageException("無符合的資料", getClass());
			}

			for (L170M01A model : list) {
				if (true) {
					Map<String, String> lockedUser = docCheckService
							.listLockedDocUser(model.getMainId());
					if (lockedUser != null) {
						list_opened.add(Util.trim(model.getCustName()));
						continue;
					}
				}

				if (true) {
					String msg_notComplate = lms1700Service
							.checkIncompleteMsg(model);
					if (Util.isNotEmpty(msg_notComplate)) {
						list_notComplate.add(Util.trim(model.getCustName()));
						continue;
					}
				}

				if (true) {
					boolean r_pid = false;
					try {
						check_pid(model);
						r_pid = true;
					} catch (Exception e) {
					}
					if (r_pid == false) {
						list_pid.add(Util.trim(model.getCustName()));
						continue;
					}
				}

				if (true) {
					// 寫入簽章欄
					retrialService.saveL170M01G_L1L4L5(model,
							lrsConstants.BRANCHTYPE.覆審單位, user, l4List, l5List,
							null);
					// ===
					oid_name_map.put(model.getOid(),
							Util.trim(model.getCustName()));
				}
			}
		}

		boolean reloadGrid = false;
		if (MapUtils.isNotEmpty(oid_name_map)) {
			for (String mainOid : oid_name_map.keySet()) {
				String custName = oid_name_map.get(mainOid);

				try {
					params.put(EloanConstants.MAIN_OID, mainOid);
					// XXX 套用 flowAction,檢核
					flowAction(params);
					// ---
					reloadGrid = true;
				} catch (Exception e) {
					logger.error("do_SignAndFlow:" + custName + "["
							+ e.getMessage() + "]");
					// ~~~
					list_flowError.add(custName);
				}
			}
		}

		if (true) {
			List<String> msg = new ArrayList<String>();
			if (list_opened.size() > 0) {
				msg.add("資料開啟中：" + StringUtils.join(list_opened, ",") + "。");
			}
			if (list_notComplate.size() > 0) {
				msg.add("資料不完整：" + StringUtils.join(list_notComplate, ",")
						+ "。");
			}
			if (list_pid.size() > 0) {
				String desc = MessageFormat.format(
						prop_lms1700m01.getProperty("ui_lms1700.msg23"),
						prop_lms1700m01.getProperty("button.AddToL180M01A"));
				msg.add(desc + "：" + StringUtils.join(list_pid, ",") + "。");
			}
			if (list_flowError.size() > 0) {
				msg.add("流程錯誤：" + StringUtils.join(list_flowError, ",") + "。");
			}
			// ======
			if (msg.size() > 0) {
				msg.add("所以未呈核");
				result.set("msg", StringUtils.join(msg, "<br/>"));
			}
		}
		if (reloadGrid) {
			result.set("doReloadGrid", "Y");
		}
		return result;
	}

	private List<L170M01A> _oids_l170m01gL1(String oids, String retrialStaffNo) {
		List<String> errMsg = new ArrayList<String>();
		List<L170M01A> raw_list = lms1700Service.impBySelOrDate(oids, "", "",
				errMsg);

		List<L170M01A> r = new ArrayList<L170M01A>();
		for (L170M01A model : raw_list) {
			if (Util.notEquals(model.getDocStatus(),
					RetrialDocStatusEnum.區中心_編製中.getCode())) {
				continue;
			}

			L170M01G l170m01g = retrialService
					.findL170M01G_first_L1_retrialTeam(model);
			if (l170m01g == null) {
				continue;
			}

			if (Util.equals(retrialStaffNo, l170m01g.getStaffNo())) {
				r.add(model);
			}
		}
		return r;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult update_itemNo_ptMgrId(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();

		String ptMgrId = Util.trim(params.getString("ptMgrId"));
		String model_oid = params.getString("model_oid");
		String prefix = "l170m01d_";
		if (Util.isNotEmpty(model_oid)
				&& StringUtils.startsWith(model_oid, prefix)) {
			String oid = StringUtils.substring(model_oid, prefix.length());
			if (Util.isNotEmpty(oid)) {
				L170M01D l170m01d = retrialService.findL170M01D_oid(oid);
				if (l170m01d != null) {
					l170m01d.setPtMgrId(ptMgrId);
					retrialService.save(l170m01d);
					// ---
					result.set("id", l170m01d.getPtMgrId());
					result.set("name",
							userInfoService.getUserName(l170m01d.getPtMgrId()));
				}
			}
		}
		return result;
	}

	/**
	 * 取得覆審報告表警告訊息 注意，本功能為儲存時呼叫檢核 (by
	 * LMS1700M01Page.js)，所以要用前端回傳之tabForm來判斷(儲存前畫面內容)，不能用meta判斷
	 * 
	 * LMS1700ServiceImpl.java 的 checkIncompleteMsg 才是儲存後之檢核，可以用meta判斷
	 * J-105-0287-001/003
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 * @throws IOException
	 */
	public IResult getL170mWarnMsg(PageParameters params)
			throws CapException, IOException {

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1700M01Page.class);

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		String tabForm = params.getString("tabForm", "");
		JSONObject jsonTabForm = JSONObject.fromObject(tabForm);

		StringBuffer warnMsg = new StringBuffer("");

		L170M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL170M01A_oid(mainOid);
		} else {
			return result;
		}

		String custId = meta.getCustId();
		String dupNo = meta.getDupNo();
		String brNo = meta.getOwnBrId();

		// J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		String eLastRetrialDate = jsonTabForm.optString("lastRetrialDate", "");
		// 注意，本功能為儲存時呼叫檢核 (by LMS1700M01Page.js)，所以要用前端回傳之tabForm來判斷，不能用meta判斷
		// 取得前段要檢核的值

		// J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		// if (Util.notEquals(Util.trim(meta.getCtlType()),
		// LrsUtil.CTLTYPE_自辦覆審)) {
		// J-107-0254_09301_B1001 配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
		// 價金履約無實地覆審相關功能
		if (Util.equals(Util.trim(meta.getCtlType()), LrsUtil.CTLTYPE_主辦覆審)) {

			// J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
			String eRealRpFg = jsonTabForm.optString("realRpFg", "");
			String eRetrialDate = jsonTabForm.optString("retrialDate", "");
			String eRealCkFg = jsonTabForm.optString("realCkFg", "");
			String eRealDt = Util.equals(
					Util.trim(jsonTabForm.optString("realDt", "")), "") ? ""
					: jsonTabForm.optString("realDt", "");

			// J-106-0221-001 Web e-Loan
			// 企金授信覆審報告表，於非土建融覆審案件若勾選為「授信案件（含土建融實地覆審）覆審報告表」時，新增提示訊息。
			if (jsonTabForm.containsKey("realRpFg")) {
				// eRealRpFg = jsonTabForm.optString("realRpFg", "");
				// eRetrialDate = jsonTabForm.optString("retrialDate", "");
				// eRealCkFg = jsonTabForm.optString("realCkFg", "");
				// eRealDt = Util.equals(
				// Util.trim(jsonTabForm.optString("realDt", "")), "") ? ""
				// : jsonTabForm.optString("realDt", "");
			} else {
				eRealRpFg = meta.getRealRpFg();
				eRetrialDate = Util.trim(TWNDate.toAD(meta.getRetrialDate()));
				eRealCkFg = meta.getRealCkFg();
				eRealDt = Util.trim(TWNDate.toAD(meta.getRealDt()));
			}

			// 取得ALOAN實際註記與實地覆審基準日
			String aReealDt = "";
			String aRealCkFg = "";
			String aRealContract = "";
			Map<String, String> minDataMap = retrialService.gfnGetAloanRealDt2(
					custId, dupNo, brNo);
			if (minDataMap != null && !minDataMap.isEmpty()) {
				aReealDt = MapUtils.getString(minDataMap, "realDt", "");
				aRealCkFg = MapUtils.getString(minDataMap, "realCkFg", "");
				aRealContract = Util.trim(MapUtils.getString(minDataMap,
						"realContract", ""));
			}

			if (Util.notEquals(aRealCkFg, "") && Util.notEquals(eRealCkFg, "")) {
				if (Util.notEquals(eRealCkFg, aRealCkFg)) {
					// ui_lms1700.msg28=e-Loan覆審控制檔實地覆審註記【{0}】與a-Loan帳務檔【{1}】不符
					warnMsg.append(MessageFormat.format(
							prop.getProperty("ui_lms1700.msg28"), eRealCkFg,
							aRealCkFg));

					if (Util.notEquals(aRealContract, "")) {

						warnMsg.append("(");
						warnMsg.append(prop.getProperty("L170M01B.cntrNo"));
						warnMsg.append(":");
						warnMsg.append(aRealContract);
						warnMsg.append(")");
					}

					warnMsg.append("<BR>");
				}
			}

			// 實地覆審註記為Y 但本案非實地覆審報告表 檢核有沒有超過半年
			if (Util.equals(Util.trim(eRealRpFg), "N")) {
				if (Util.equals(Util.trim(eRealCkFg), "Y")) {
					// 超過半年未實地覆審

					if (Util.notEquals(eRetrialDate, "")
							&& Util.notEquals(eRealDt, "")) {
						Date retrialDate = Util.parseDate(eRetrialDate);
						Date thisRealDt = Util.parseDate(eRealDt);

						int addedMonth = 7;

						Date calcDate = CapDate
								.addMonth(thisRealDt, addedMonth);

						if (LMSUtil.cmpDate(retrialDate, ">", calcDate)) {

							// ui_lms1700.msg29=實地覆審基準日加七個月已超過本次覆審日期，請確認本次是否需要實地覆審
							warnMsg.append(prop.getProperty("ui_lms1700.msg29"));
							warnMsg.append("<BR>");
						}
					}

				}
			}

			// J-106-0221-001 Web e-Loan
			// 企金授信覆審報告表，於非土建融覆審案件若勾選為「授信案件（含土建融實地覆審）覆審報告表」時，新增提示訊息。
			if (Util.equals(Util.trim(aRealCkFg), "N")
					&& Util.equals(eRealRpFg, "Y")) {

				// J-106-0221-001 Web e-Loan
				// 企金授信覆審報告表，於非土建融覆審案件若勾選為「授信案件（含土建融實地覆審）覆審報告表」時，新增提示訊息。
				// ui_lms1700.msg33=無土建融額度之企金授信覆審案件，請確認本案覆審報告表->文件資訊頁籤，欄位「本案是否為土建融實地覆審報告表」勾選為【是】是否正確。
				warnMsg.append(prop.getProperty("ui_lms1700.msg33"));
				warnMsg.append("<BR>");
			}

		}

		// J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
		// 先抓ELF494 上次覆審報告表的覆審日期

		// 最近一次覆審報告表日期
		Date elf494_lrdate = null;

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		List<Map<String, Object>> elf493_494_list = misdbBASEService
				.gfnGenerateCTL_FLMS180R12_with_ctlType(meta.getOwnBrId(),
						custId, dupNo, StringUtils.substring(
								Util.trim(TWNDate.toAD(meta.getRetrialDate())),
								0, 7), meta.getCtlType());

		if (!CollectionUtils.isEmpty(elf493_494_list)) {
			for (Map<String, Object> elf493_494 : elf493_494_list) {
				if (Util.notEquals(elf493_494.get("ELF494_LRDATE"), "")) {
					elf494_lrdate = Util.parseDate(elf493_494
							.get("ELF494_LRDATE"));
				}
				break;
			}
		}

		Date rptLastRetrialDate = null;
		if (Util.notEquals(eLastRetrialDate, "")) {
			rptLastRetrialDate = Util.parseDate(eLastRetrialDate);
		}
		if (elf494_lrdate != null && rptLastRetrialDate != null) {
			if (LMSUtil.cmpDate(elf494_lrdate, ">", rptLastRetrialDate)
					|| LMSUtil.cmpDate(elf494_lrdate, "<", rptLastRetrialDate)) {

				// ui_lms1700.msg32=本次覆審報告表之上次覆審日【{0}】與最近一次已上傳覆審報告表之覆審日【{1}】不同。
				warnMsg.append(MessageFormat.format(
						prop.getProperty("ui_lms1700.msg32"),
						Util.trim(TWNDate.toAD(rptLastRetrialDate)),
						Util.trim(TWNDate.toAD(elf494_lrdate))));
				warnMsg.append("<BR>");

			}
		}

		if (Util.notEquals(warnMsg.toString(), "")) {
			// ui_lms1700.msg30=下列訊息僅提示，確認完成後請按確定繼續：<BR><BR>
			warnMsg.insert(0, prop.getProperty("ui_lms1700.msg30"));
		}

		result.set("warnMsg", warnMsg.toString());

		return result;
	}

	public IResult getL170mTipsMsg(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		StringBuffer tipsMsg = new StringBuffer("");

		L170M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL170M01A_oid(mainOid);
		} else {
			return result;
		}

		// 2022/01/24 授審連喬凱說 出警示
		// 是否有須扣分情事： 是 卻未輸入任考評項目
		boolean isEmpty = true;
		if (!retrialService.hidePaFormPanel()) {
			if (meta != null) {
				if (Util.equals(Util.nullToSpace(meta.getNeedPa()), "Y")) {
					String paVer = Util.nullToSpace(meta.getPaVer());
					String[] m01jCol = retrialService.getPaCol(paVer, "1");
					String[] m01jColYn = retrialService.getPaCol(paVer, "2");
					String[] m01jColCnt = retrialService.getPaCol(paVer, "3");
					Set<L170M01J> l170m01js = meta.getL170m01js();
					if (l170m01js != null && !l170m01js.isEmpty()) {
						for (L170M01J l170m01j : l170m01js) {
							String itemName = Util.trim(l170m01j.getItemName());
							if (ArrayUtils.contains(m01jColYn, itemName)) {
								// 任一有勾選是
								if (Util.equals(
										Util.trim(l170m01j.getItemYn()), "Y")) {
									isEmpty = false;
									break;
								}
							} else if (ArrayUtils.contains(m01jColCnt, itemName)) {
								// 任一大於0
								BigDecimal cnt = Util.parseBigDecimal(l170m01j
										.getItemCnt());
								if (cnt.compareTo(BigDecimal.ZERO) == 1) {
									isEmpty = false;
									break;
								}
							} else {
							}
						}
					}
				} else {
					isEmpty = false;
				}
			}
		} else {
			isEmpty = false;
		}
		if (isEmpty) {
			tipsMsg.append("僅提示！！考評表尚未輸入任一情事！");
		}

		result.set("tipsMsg", tipsMsg.toString());

		return result;
	}

	/**
	 * 取得覆審報告是否為實際覆審報告(土建融) J-106-0145-001 Web e-Loan
	 * 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult getl170m01_defaultVal(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L170M01A l170m01a = retrialService.findL170M01A_oid(mainOid);
		result.set("realRpFg", Util.trim(l170m01a.getRealRpFg()));

		return result;
	}

	/** J-108-0268 覆審案件 客戶逾期情形 **/
	@DomainAuth(AuthType.Modify)
	public IResult getOverDueData(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L170M01A l170m01a = retrialService.findL170M01A_oid(mainOid);
		Map<String, Object> map = retrialService
				.getOverDueData(new Date(), Util.trim(l170m01a.getCustId()),
						Util.trim(l170m01a.getDupNo()));
		String CapDays = (map.get("CapDays") == null ? "0" : map.get("CapDays")
				.toString());
		String CapOvDate = (map.get("CapOvDate") == null ? null : map.get(
				"CapOvDate").toString());
		String CapDataDate = (map.get("CapDataDate") == null ? null : map.get(
				"CapDataDate").toString());
		String IntDays = (map.get("IntDays") == null ? "0" : map.get("IntDays")
				.toString());
		String IntOvDate = (map.get("IntOvDate") == null ? null : map.get(
				"IntOvDate").toString());
		String IntDataDate = (map.get("IntDataDate") == null ? null : map.get(
				"IntDataDate").toString());
		String qryDate = (map.get("qryDate") == null ? null : map
				.get("qryDate").toString());
		DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Date qryD = new Date();
		Date CapD = new Date();
		Date CapDataD = new Date();
		Date IntD = new Date();
		Date IntDataD = new Date();
		try {
			if (map.get("CapDays") == null) { // 沒有本金逾期資料
				l170m01a.setCapDays(0);
				l170m01a.setCapDt(null);
				l170m01a.setCapDataDt(null);
			} else {
				l170m01a.setCapDays((Integer) map.get("CapDays"));
				CapD = ((CapOvDate != null) ? format.parse(CapOvDate) : null);
				CapDataD = ((CapDataDate != null) ? format.parse(CapDataDate)
						: null);
				l170m01a.setCapDt(CapD);
				l170m01a.setCapDataDt(CapDataD);
			}
			if (map.get("IntDays") == null) { // 沒有利息逾期資料
				l170m01a.setIntDays(0);
				l170m01a.setIntDt(null);
				l170m01a.setIntDataDt(null);
			} else {
				l170m01a.setIntDays((Integer) map.get("IntDays"));
				IntD = ((IntOvDate != null) ? format.parse(IntOvDate) : null);
				IntDataD = ((IntDataDate != null) ? format.parse(IntDataDate)
						: null);
				l170m01a.setIntDt(IntD);
				l170m01a.setIntDataDt(IntDataD);
			}

			qryD = format.parse(qryDate);
			l170m01a.setOvQryDt(qryD); // 查詢日
		} catch (ParseException e) {
			throw new RuntimeException("無法初始化日期！" + e);
		}

		retrialService.save(l170m01a);

		// ui_lms1700.msg35=本金最長逾期天數：{0}天，利息最長逾期天數：{1}天。引進資料日期:{2}
		result.set("overDueText", MessageFormat.format(
				prop_lms1700m01.getProperty("ui_lms1700.msg35"), CapDays,
				IntDays, Util.nullToSpace(qryDate)));
		return defaultResult(params, l170m01a, result);
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryLastRetrialDate(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L170M01A meta = null;
		if (Util.isNotEmpty(mainId)) {
			meta = retrialService.findL170M01A_mainId(mainId);

			LMSUtil.addMetaToResult(result, meta, new String[] { "ownBrId",
					"retrialDate", "lastRetrialDate", "projectNo", "typCd",
					"custName", "chairman", "tradeType", "randomCode",
					"realCkFg", "realDt", "realRpFg", "ctlType" });
		} else {
			result.set("lastRetrialDate", "");
		}

		return result;
	}

	/*
	 * 取得貸後管理額度序號列表 參考 LMS8000GridHandler queryGetCntrno
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryPostLoanList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L170M01A meta = null;
		TreeMap<String, String> cntrnoMap = new TreeMap<String, String>();
		List<String> cntrnoList = new ArrayList<String>();
		if (Util.isNotEmpty(mainId)) {
			meta = retrialService.findL170M01A_mainId(mainId);
			if (meta != null) {
				String custId = Util.trim(meta.getCustId());
				String dupNo = Util.trim(meta.getDupNo());
				dupNo = (Util.isEmpty(dupNo) ? "0" : dupNo);
				if (Util.isNotEmpty(custId)) {
					List<Map<String, Object>> data = misdbBASEService
							.getContractAndLoanNo(custId, dupNo);
					for (Map<String, Object> map : data) {
						String cntrNo = Util.trim(MapUtils.getString(map,
								"CONTRACT"));
						if (cntrnoList != null && cntrnoList.contains(cntrNo)) {
							continue; // 排除重複
						} else {
							if (cntrNo.length() < 3) {
								continue;
							}
							// 2021/10/01 金至忠襄理說不得跨分行 所以非該行額度序號不顯示
							if (Util.notEquals(cntrNo.substring(0, 3),
									meta.getOwnBrId())) {
								continue;
							}
							cntrnoMap.put(cntrNo, cntrNo);
							cntrnoList.add(cntrNo);
						}
					}
				}
			}
		}
		if (cntrnoMap.size() < 1) { // 避免傳出 null 字串
			cntrnoMap.put("", "");
		}
		result.set("cntrnoList", new CapAjaxFormResult(cntrnoMap));
		return result;
	}

	// J-110-0505_05097_B1001 Web
	// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPrintL140M01AParam(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = Util.trim(params.getString("oids")).split("\\|");
		List<String> noL120M01A = new ArrayList<String>();
		List<String> noL140M01A = new ArrayList<String>();
		List<String> paramList = new ArrayList<String>();
		String ctlType = LrsUtil.CTLTYPE_主辦覆審;
		List<String> notProc = new ArrayList<String>();

		if (oids != null && oids.length > 0) {
			List<L170M01A> l170m01a_list = new ArrayList<L170M01A>();

			if (true) {
				List<String> oid_list = new ArrayList<String>();
				for (String oid : oids) {
					oid_list.add(oid);
				}
				l170m01a_list = retrialService.findL170M01A_oid(oid_list);
				for (L170M01A l170m01a : l170m01a_list) {

					Map<String, String> cntrNo_list = new HashMap<String, String>();
					Set<String> cntrNo_Set = new HashSet<String>();
					List<L170M01B> l170m01b_list = retrialService
							.findL170M01B_orderBy(l170m01a);

					if (l170m01b_list == null || l170m01b_list.isEmpty()) {
						// 若覆審報告表沒有授信額度資料，就先自動引進
						retrialService.importLNtoL170M01B(l170m01a);
						l170m01b_list = retrialService
								.findL170M01B_orderBy(l170m01a);
					}

					if (CollectionUtils.isNotEmpty(l170m01b_list)) {
						for (L170M01B l170m01b : l170m01b_list) {
							String cntrNo = Util.trim(l170m01b.getCntrNo());
							cntrNo_list.put(cntrNo, "");
							cntrNo_Set.add(cntrNo); // 要查詢的額度Set
						}
					}

					// [下午 01:28] 金至忠(授信審查處,襄理)
					// 我問覆審人員他們是用id查覆審分行已核准簽報書, 拿該分行的額度明細表出來看!(沒有在對額度序號)
					// List<Map<String, Object>> dataList = lms1700Service
					// .findPrint_L140M01A_By_CntrNos(cntrNo_Set);

					if (cntrNo_Set != null && !cntrNo_Set.isEmpty()) {

						List<Map<String, Object>> dataList = retrialService
								.findPrint_L140M01A_By_CustId(
										l170m01a.getCustId(),
										l170m01a.getDupNo(), cntrNo_Set);

						if (dataList != null && !dataList.isEmpty()) {

							for (Map<String, Object> dataMap : dataList) {

								// OID, CUSTID, CNTRNO, MAINID, ENDDATE,
								// ISHEADCHECK
								String OID = Util.trim(MapUtils.getString(
										dataMap, "OID"));
								String CNTRNO = Util.trim(MapUtils.getString(
										dataMap, "CNTRNO"));

								String ISHEADCHECK = Util.trim(MapUtils
										.getString(dataMap, "ISHEADCHECK"));

								String rptNo = Util.equals("Y", ISHEADCHECK) ? "R13"
										: "R12";

								cntrNo_list.put(CNTRNO, "Y");

								paramList.add(l170m01a.getOid() + "^" + rptNo
										+ "^" + OID);
							}
						}
					} else {
						notProc.add("借款人無額度明細表/批覆書：" + l170m01a.getCustId()
								+ " " + l170m01a.getDupNo());
					}

					// 額度找不到額度明細表/批覆書
					for (String cntrNoStr : cntrNo_list.keySet()) {
						if (Util.notEquals(cntrNo_list.get(cntrNoStr), "Y")) {
							noL140M01A.add(cntrNoStr);
						}
					}

				}
			}

		}

		if (true) {

			if (noL140M01A.size() > 0) {
				notProc.add("無額度明細表/批覆書：" + StringUtils.join(noL140M01A, "、"));
			}

		}
		if (notProc.size() > 0) {
			result.set("notProc", StringUtils.join(notProc, "<br/>"));
		}
		if (paramList.size() > 0) {
			result.set("parStr", StringUtils.join(paramList, "|"));
		}
		return result;
	}

	/**
	 * J-111-0560 配合授信審查處，Web-eloan授信管理系統，覆審作業聯徵資料PPA已查詢部份,增加一鍵查詢功能，自動比對債票信及卡信資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getEjcicReusltRecord(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L170M01A meta = null;
		if (Util.isNotEmpty(mainId)) {
			meta = retrialService.findL170M01A_mainId(mainId);
		}
		// 自動比對債票信及卡信資料
		Map<String, String> resultMap = retrialService.getEjcicReusltRecord(
				meta, false);
		// 回傳頁面結果
		result.putAll(resultMap);
		return result;
	}
	
	// J-111-0554 配合授審處增進管理效益，修改相關功能程式 add 列印擔保品設定資料表
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPrintCollSet(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = Util.trim(params.getString("oids")).split("\\|");
		List<String> noL140M01A = new ArrayList<String>();
		List<String> paramList = new ArrayList<String>();
		List<String> notProc = new ArrayList<String>();

		if (oids != null && oids.length > 0) {
			List<L170M01A> l170m01a_list = new ArrayList<L170M01A>();

			if (true) {
				List<String> oid_list = new ArrayList<String>();
				for (String oid : oids) {
					oid_list.add(oid);
				}
				l170m01a_list = retrialService.findL170M01A_oid(oid_list);
				for (L170M01A l170m01a : l170m01a_list) {

					Set<String> cntrNo_Set = new HashSet<String>();
					List<L170M01B> l170m01b_list = retrialService
							.findL170M01B_orderBy(l170m01a);

					if (l170m01b_list == null || l170m01b_list.isEmpty()) {
						// 若覆審報告表沒有授信額度資料，就先自動引進
						retrialService.importLNtoL170M01B(l170m01a);
						l170m01b_list = retrialService
								.findL170M01B_orderBy(l170m01a);
					}

					if (CollectionUtils.isNotEmpty(l170m01b_list)) {
						for (L170M01B l170m01b : l170m01b_list) {
							String cntrNo = Util.trim(l170m01b.getCntrNo());
							cntrNo_Set.add(cntrNo); // 要查詢的額度Set
						}
					}
					if (cntrNo_Set != null && !cntrNo_Set.isEmpty()) {

						//依額度撈取擔保品設定資料表(限不動產/動產)
						List<Map<String, Object>> dataList = retrialService
								.findCMS_C100m01byCntrno(cntrNo_Set);

						if (dataList != null && !dataList.isEmpty()) {

							for (Map<String, Object> dataMap : dataList) {
								String MAINID = Util.trim(MapUtils.getString(
										dataMap, "MAINID"));
//								String BRANCH = Util.trim(MapUtils.getString(
//										dataMap, "BRANCH"));
//								String CUSTID = Util.trim(MapUtils.getString(
//										dataMap, "CUSTID"));
//								String COLLNO = Util.trim(MapUtils.getString(
//										dataMap, "COLLNO"));
//								String COLLTYP1 = Util.trim(MapUtils.getString(
//										dataMap, "COLLTYP1"));
//								String CNTRNO = Util.trim(MapUtils.getString(
//										dataMap, "CNTRNO"));
								paramList.add(MAINID);  //估價報告書mainid
							}
						} else {
							notProc.add("查無擔保品設定資料表：" + l170m01a.getCustId()
									+ " " + l170m01a.getDupNo());
						}
					} else {
						notProc.add("無授信額度資料：" + l170m01a.getCustId()
								+ " " + l170m01a.getDupNo());
					}
				}
			}
		}
		if (notProc.size() > 0) {
			result.set("notProc", StringUtils.join(notProc, "<br/>"));
		}
		if (paramList.size() > 0) {
			result.set("parStr", StringUtils.join(paramList, "|"));
		}
		return result;
	}
}
