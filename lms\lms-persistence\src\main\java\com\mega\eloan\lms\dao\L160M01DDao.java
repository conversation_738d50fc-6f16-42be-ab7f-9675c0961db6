/* 
 * L160M01DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L160M01D;

/** 案件簽章欄檔 **/
public interface L160M01DDao extends IGenericDao<L160M01D> {

	L160M01D findByOid(String oid);

	List<L160M01D> findByMainId(String mainId);

	L160M01D findByUniqueKey(String mainId, String staffNo, String staffJob);

	List<L160M01D> findByIndex01(String mainId, String staffNo, String staffJob);
}