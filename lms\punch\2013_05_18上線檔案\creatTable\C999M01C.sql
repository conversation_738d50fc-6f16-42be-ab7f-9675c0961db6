---------------------------------------------------------
-- LMS.C999M01C 個金約據書連保人(保證人)檔
---------------------------------------------------------
--DROP TABLE LMS.C999M01C;
CREATE TABLE LMS.C999M01C (
	OID           CHAR(32)      not null,
	MAIN<PERSON>        CHAR(32)      not null,
	CUSTID        VARCHAR(10)   not null,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(1)       not null,
	CUSTPOS       CHAR(1)       not null,
	<PERSON>US<PERSON>NA<PERSON>      VARCHAR(120) ,
	ADDRZIP       DECIMAL(5,0) ,
	ADDRCITY      VARCHAR(12)  ,
	ADDR<PERSON>W<PERSON>      VARCHAR(12)  ,
	<PERSON><PERSON>          VARCHAR(300) ,
	CREATOR       CHAR(6)      ,
	CREATE<PERSON><PERSON>    TIMESTAMP    ,
	UPDATE<PERSON>       CHAR(6)      ,
	UPDA<PERSON><PERSON><PERSON>    TIMESTAMP    ,

	constraint P_C999M01C PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC999M01C01;
--CREATE UNIQUE INDEX LMS.XC999M01C01 ON LMS.C999M01C (OID);
--DROP INDEX LMS.XC999M01C02;
--CREATE INDEX LMS.XC999M01C02 ON LMS.C999M01C   (CUSTID, DUPNO);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C999M01C IS '個金約據書連保人(保證人)檔';
COMMENT ON LMS.C999M01C (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	CUSTID        IS '統一編號', 
	DUPNO         IS '重覆序號', 
	CUSTPOS       IS '性質(相關身份)', 
	CUSTNAME      IS '名稱', 
	ADDRZIP       IS '郵遞區號', 
	ADDRCITY      IS '地址(縣市)', 
	ADDRTOWN      IS '地址(區鄉鎮市)', 
	ADDR          IS '地址', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
