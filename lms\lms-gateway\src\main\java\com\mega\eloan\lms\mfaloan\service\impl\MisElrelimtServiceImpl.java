package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisElrelimtService;

@Service
public class MisElrelimtServiceImpl extends AbstractMFAloanJdbc implements
		MisElrelimtService {

	public List<?> findRPTGroupData2() {
		return this.getJdbc().queryForList("MISELRELIMT.selRPTGroupData2",
				new String[] {});
	}

	public List<?> findLNF022V14() {
		return this.getJdbc().queryForList("MISELRELIMT.selLNF022V14",
				new String[] {});
	}

	public List<?> findMegaNetValue() {
		return this.getJdbc().queryForList("MISELRELIMT.selMegaNetValue",
				new String[] {});
	}

}
