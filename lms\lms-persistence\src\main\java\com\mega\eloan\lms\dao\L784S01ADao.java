/* 
 * L784S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L784S01A;


/** 已敘做授信案件明細 **/
public interface L784S01ADao extends IGenericDao<L784S01A> {

	L784S01A findByOid(String oid);

	List<L784S01A> findByMainId(String mainId);

	L784S01A findByUniqueKey(String mainId, String brId, String custId, String dupNo,
			String cntrNo);

	List<L784S01A> findByIndex01(String mainId, String custId, String dupNo,
			String cntrNo);
	
	List<L784S01A> findByCntrNo(String CntrNo);
	
	List<L784S01A> findByCustIdDupId(String custId,String DupNo);
}