/* 
 *MisIquotappService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.IQUOTAPP;
import com.mega.eloan.lms.mfaloan.service.MisIquotappService;

import tw.com.jcs.common.Util;
/**
 * <pre>
 * 核准額度資料檔 IQUOTAPP(MIS.ELV38801)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
@Service
public class MisIquotappServiceImpl extends AbstractMFAloanJdbc implements
		MisIquotappService {

	@Override
	public List<Map<String, Object>> findByCntrNo(String... cntrNos) {
		String cntrNoParams = Util.genSqlParam(cntrNos);
		return this.getJdbc().queryForListByCustParam("IQUOTAPP.selByCntrNo",
				new Object[] { cntrNoParams }, cntrNos);

	}

	@Override
	public void insert(String quotaNo, String custId, String dupNo,
			String icbcNo, String cName, String omgrName, String fmgrName,
			String approLvl, String updater) {
		this.getJdbc().update(
				"IQUOTAPP.insert",
				new Object[] { quotaNo, custId, dupNo, icbcNo, cName, omgrName,
						fmgrName, approLvl, updater });

	}

	@Override
	public void update(String custId, String dupNo, String icbcNo,
			String cName, String fmgrName, String approLvl, String updater,
			String quotaNo) {
		this.getJdbc().update(
				"IQUOTAPP.update",
				new Object[] { custId, dupNo, icbcNo, cName, fmgrName,
						approLvl, updater, quotaNo });

	}

	@Override
	public IQUOTAPP findIQUOTAPPByUKey(String QUOTANO) {
		List<Map<String, Object>> rowData = this.getJdbc()
				.queryForList("IQUOTAPP.selByCntrNo2",
						new Object[] { QUOTANO });
		if (rowData.size() > 0) {
			List<IQUOTAPP> list = new ArrayList<IQUOTAPP>();
			for (Map<String, Object> row : rowData) {
				IQUOTAPP model = new IQUOTAPP();
				DataParse.map2Bean(row, model);
				list.add(model);
			}
			return list.get(0);
		} else {
			return null;
		}
	}
}
