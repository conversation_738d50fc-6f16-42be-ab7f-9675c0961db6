/* 
 * CLS9041GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */package com.mega.eloan.lms.fms.handler.grid;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.fms.service.CLS9041M06Service;
import com.mega.eloan.lms.model.C004M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 政策性留學生貸款送保彙報(S1~S3)
 * </pre>
 * 
 * @since 2012/11/05
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/01,Vector Lo,new
 *          </ul>
 */
@Scope("request")
@Controller("cls9041m06gridhandler")
public class CLS9041M06GridHandler extends AbstractGridHandler {

	@Resource
	CLS9041M06Service service;

	@Resource
	DocFileService docFileService;

	/**
	 * 依篩選條件 查詢C004M01A資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult query(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 篩選
		if (params.getBoolean("_search")) {
			String creator = params.getString("creator");
			String bgnDate = params.getString("bgnDate");
			String endDate = params.getString("endDate");
			if (!Util.isEmpty(creator)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"creator", creator);
			}
			if (!Util.isEmpty(bgnDate) && !Util.isEmpty(endDate)) {
				pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
						"createTime", bgnDate);
				pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
						"createTime", endDate);
			}
		}
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "rptType", null);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptName", "政策性留學生貸款補貼息相關報表");
		Page page = service.findPage(C004M01A.class, pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得 調閱 上傳的資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryfile(ISearch pageSetting, PageParameters params) throws CapException {

		Page page = service.findFile(params.getString("mainId"));
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
}
