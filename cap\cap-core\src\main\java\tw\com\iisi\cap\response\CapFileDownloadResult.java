/*
 * CapFileDownloadResult.java
 * 
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.response;

import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FilenameUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.reference.DefaultHTTPUtilities;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapCommonUtil;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * CapFileDownloadResult implements IResult
 * 檔案下載
 * </pre>
 * 
 * @since 2010/11/24
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>iristu,2010/11/24,new
 *          </ul>
 */
public class CapFileDownloadResult implements IResult {

    /**
     * 要下載的檔案
     */
    protected String _file;
    /**
     * 檔案輸出的名字
     */
    protected String _outputName;
    /**
     * 媒體類型
     */
    protected String _contentType;
    /**
     * 前端請求
     */
    protected PageParameters _request;

    /**
     * 建構子
     */
    public CapFileDownloadResult() {
        super();
    }

    /**
     * 
     * @param request
     *            前端請求
     * @param file
     *            要下載的檔案名稱
     * @param contentType
     *            媒體類型
     */
    public CapFileDownloadResult(PageParameters request, String file, String contentType) {
        this(request, file, null, contentType);
    }

    /**
     * 
     * @param request
     *            前端請求
     * @param file
     *            要下載的檔案名稱
     * @param outputName
     *            檔案輸出的名字
     * @param contentType
     *            媒體類型
     */
    public CapFileDownloadResult(PageParameters request, String file, String outputName, String contentType) {
        this._request = request;
        this._file = file;
        this._outputName = CapString.isEmpty(outputName) ? FilenameUtils.getName(_file) : outputName;
        this._contentType = contentType;
    }

    /*
     * 返回空字串
     * 
     * @see tw.com.iisi.cap.response.IResult#getResult()
     */
    @Override
    public String getResult() {
        return "";
    }// ;

    /*
     * 取得下載檔案的Log訊息
     * 
     * @see tw.com.iisi.cap.response.IResult#getLogMessage()
     */
    @Override
    public String getLogMessage() {
        return new StringBuffer("Download file:").append(_file).toString();
    }

    /*
     * 加入檔案下載的詳細資訊
     * 
     * @see tw.com.iisi.cap.response.IResult#add(tw.com.iisi.cap.response.IResult)
     */
    @Override
    public void add(IResult result) {
        if (result instanceof CapFileDownloadResult) {
            CapFileDownloadResult r = (CapFileDownloadResult) result;
            this._contentType = r._contentType;
            this._file = r._file;
            this._outputName = r._outputName;
            this._request = r._request;
        }
    }

    /*
     * 回應檔案下載結果
     * 
     * @see tw.com.iisi.cap.response.IResult#respondResult(javax.servlet.ServletResponse)
     */
    @Override
    public void respondResult(ServletResponse response) throws CapException {
        try {
            if (_outputName != null && response instanceof HttpServletResponse) {
                DefaultHTTPUtilities httpUtilities = new DefaultHTTPUtilities();
                HttpServletResponse resp = (HttpServletResponse) response;
                // [refs # 47] HTTP Response Splitting 2200523, 改用ESAPI的 httpUtilities.setHeader
                httpUtilities.setHeader(resp, "Content-Type", _contentType);
                // [refs # 47] HTTP Response Splitting 2200523, 改用ESAPI.encoder().encodeForURL
                resp.setHeader("Content-Disposition", "attachment;filename=\"" + ESAPI.encoder().encodeForURL(_outputName) + "\"");
                httpUtilities.setHeader(resp, "Cache-Control", "public");
                httpUtilities.setHeader(resp, "Pragma", "public");
            }
            CapCommonUtil.copyTo(_file, response.getOutputStream());
        } catch (Exception e) {
            throw new CapException(e, getClass());
        }
    }

}
