package com.mega.eloan.lms.lns.panels;

import com.mega.eloan.common.panels.Panel;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;

/**
 * <pre>
 * 模擬動審文件資訊
 * </pre>
 * 
 * @since
 * <AUTHOR>
 * @version <ul>
 *          <li>
 *          </ul>
 */
public class LMS2501S01Panel extends Panel {

	public LMS2501S01Panel(String id) {
		super(id);
	}
	
	public LMS2501S01Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		new DocLogPanel("_docLog").processPanelData(model, params);
	}
}
