ul.tree, ul.tree * {
    list-style-type: none;
    margin: 0;
    padding: 0 0 5px 0;
}

ul.tree img.arrow {
    padding: 2px 0 0 0;
    border: 0;
    width: 20px;
}

ul.tree li {
    padding: 4px 0 0 0;
    clear: both;
}

ul.tree li ul {
    padding: 0 0 0 20px;
    margin: 0;
}

ul.tree label {
    cursor: pointer;
    font-weight: bold;
    float: left;
    display: inline;
    white-space: nowrap;
}

ul.tree label.hover {
    color: red;
}

ul.tree li .arrow {
    width: 16px;
    height: 16px;
    padding: 0;
    margin: 0;
    cursor: pointer;
    float: left;
    background: transparent no-repeat 0 3px;
}

ul.tree li .collapsed {
    background-image: url(/webroot/img/collapsed.gif);
}

ul.tree li .expanded {
    background-image: url(/webroot/img/expanded.gif);
}

ul.tree li .checkbox {
    width: 16px;
    height: 16px;
    padding: 0;
    margin: 0;
    cursor: pointer;
    float: left;
    background: transparent no-repeat 0 2px;
    background-image: url(/webroot/img/check0.gif);
}

ul.tree li .checked {
    background-image: url(/webroot/img/check2.gif);
}

ul.tree li .half_checked {
    background-image: url(/webroot/img/check1.gif);
}