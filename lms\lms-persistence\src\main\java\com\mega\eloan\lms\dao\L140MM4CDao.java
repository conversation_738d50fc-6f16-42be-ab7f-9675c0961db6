package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140MM4C;


public interface L140MM4CDao extends IGenericDao<L140MM4C> {

	List<L140MM4C> findByMainId(String mainId);

	L140MM4C findByOid(String oid);

	L140MM4C findCurrentByMainId(String mainId);

	L140MM4C findLastByMainId(String mainId);

	L140MM4C findByMainIdEstateType(String mainId, String estateType);

}