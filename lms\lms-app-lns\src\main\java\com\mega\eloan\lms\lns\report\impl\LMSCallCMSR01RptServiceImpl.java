package com.mega.eloan.lms.lns.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.security.KeyStore;

import javax.annotation.Resource;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.scheme.PlainSocketFactory;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.conn.tsccm.ThreadSafeClientConnManager;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.http.util.EntityUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.gwclient.RPAHttpSSLSocketFactory;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMS2501Service;
import com.mega.eloan.lms.dao.C241M01ADao;
import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.L170M01A;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * 列印覆審擔保品設定資料表
 * 
 * <AUTHOR>
 * 
 */
@Service("lmscallcmsr01rptservice")
public class LMSCallCMSR01RptServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMSCallCMSR01RptServiceImpl.class);

	@Resource
	LMS2501Service lms2501Service;

	@Resource
	UserInfoService userInfoService;

	@Resource
	SysParameterService sysParamService;
	
	@Resource
	L170M01ADao l170m01aDao;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	C241M01ADao c241m01aDao;
	
	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 */
	public OutputStream generateReport(PageParameters params) throws Exception {
		OutputStream outputStream = this.genCollSet(params);

		String saveFile = Util.trim(params.getString("saveFile", ""));
		
		if (!Util.isEmpty(saveFile)) {
			processPdfToFile(saveFile, (ByteArrayOutputStream) outputStream);
		}
		
		return outputStream;
	}

	@SuppressWarnings("unchecked")
	public OutputStream genCollSet(PageParameters pageparams) throws Exception {

		//傳入參數
		String oid = pageparams.getString(EloanConstants.OID); //覆審報告表，主檔 oid
		String mainIds = Util.nullToSpace(pageparams.getString("mainIds"));
		
		OutputStream outputStream = null;
		String errorMsg = "";
		
		String[] dataSplit = mainIds.split("\\|");
		
		
		
		
		//呼叫 CMS API 列印設定資料表
		try {
			int sec = 30;

			KeyStore trustStore = KeyStore.getInstance(KeyStore
					.getDefaultType());
			trustStore.load(null, null);

			SSLSocketFactory sf = new RPAHttpSSLSocketFactory(trustStore);
			final HttpParams params = new BasicHttpParams();
			HttpConnectionParams.setStaleCheckingEnabled(params, false);
			HttpConnectionParams.setConnectionTimeout(params, sec * 1000);
			HttpConnectionParams.setSoTimeout(params, sec * 1000);
			HttpConnectionParams.setSocketBufferSize(params, 8192 * 5);
			SchemeRegistry registry = new SchemeRegistry();
			registry.register(new Scheme("http", PlainSocketFactory
					.getSocketFactory(), 80));
			registry.register(new Scheme("https", sf, 443));
			ClientConnectionManager ccm = new ThreadSafeClientConnManager(
					params, registry);
			HttpClient httpclient = new DefaultHttpClient(ccm, params);

			HttpPost httpPost = new HttpPost(
					sysParamService
							.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL));
			//RPA_GW_RESPONSE_URL
			//SIT => https://eloansit.megabank.com.tw/cms-web/app/schedulerRPA
			//LOCAL_TEST => http://localhost:9182/cms-web/app/schedulerRPA
			
			httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
			// 設定Header Authorization=token

			JSONObject reqJSONObj = new JSONObject();
			JSONObject request = new JSONObject();

			request.put("uniqueID", oid);  //覆審報告表，主檔 oid
	

			JSONArray collMainIds = new JSONArray();
			for (String collMainId : dataSplit) {
				JSONObject tempJson = new JSONObject();
				tempJson.put("collMainId", collMainId);
				
				collMainIds.add(tempJson);
			}
			request.put("collMainIds", collMainIds);

			reqJSONObj.put("serviceId", "fileProcessingPageLmsForCollSet");
			reqJSONObj.put("vaildIP", "N");
			reqJSONObj.put("request", request.toString());

			LOGGER.info("CollSet CALL CMS Begin*******************************************************************");
			LOGGER.info(reqJSONObj.toString());

			StringEntity stringEntity = new StringEntity(reqJSONObj.toString(),
					"UTF-8");
			stringEntity.setContentEncoding("UTF-8");
			httpPost.setEntity(stringEntity);

			HttpResponse response = null;
			response = httpclient.execute(httpPost);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 回傳內容
				String content = EntityUtils.toString(response.getEntity(),
						"UTF-8");// UTF-8 big5
				LOGGER.info(content);

				JSONObject responseJson = JSONObject.fromObject(content);
				String rc = responseJson.optString("rc", "1");

				if (Util.equals(rc, "0")) {
					// SUCCESS
					LOGGER.info("Response SUCCESS");
					String data = responseJson.getString("data");
					
					byte[] bytes1 = LMSUtil.base64StringToImage(data);
					
					//InputStream is = new ByteArrayInputStream(bytes1);
					OutputStream os = new ByteArrayOutputStream();
			        try {
			            os.write(bytes1);
			            os.close();
			        } catch (IOException e) {
			            e.printStackTrace();
			        } finally {
						if (os != null) {
							try {
								os.close();
							} catch (IOException e) {
								LOGGER.debug("outputStream close Error", getClass());
							}
						}
					}
					
			        outputStream = os;
				} else {
					// FAIL
					errorMsg = "Response FAIL:"
							+ responseJson.optString("message", "FAIL");

					LOGGER.error(errorMsg);
				}

			} else {
				errorMsg = "HTTP ERROR:"
						+ response.getStatusLine().getStatusCode();
			}
		} catch (IOException ioe) {
			errorMsg = StrUtils.getStackTrace(ioe);
			LOGGER.error(errorMsg);
		} catch (Exception e) {
			errorMsg = StrUtils.getStackTrace(e);
			LOGGER.error(errorMsg);
		} finally {

		}
		return outputStream;

	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	@SuppressWarnings("unused")
	private void processPdfToFile(String saveFile,
			ByteArrayOutputStream outputStream) throws FileNotFoundException,
			IOException, Exception {

		if (outputStream == null) {
			return;
		}

		// saveFile =
		// "mainId:"+responseJSON.mainId+";"+"fileId:lrs"+";"+"class:L170M01A"+";"+"brNo:" + userInfo.unitNo+";"+"rpa:N";;
		String[] saveFileArr = Util.trim(saveFile).split(";");

		String mainIdArr = saveFileArr[0];
		String fileIdArr = saveFileArr[1];
		String classArr = saveFileArr[2];
		String brNoArr = saveFileArr[3];
		String rpaArr = saveFileArr[4];

		String mainId = Util.trim(mainIdArr).split(":")[1]; // l170m01a mainId
		String fileId = Util.trim(fileIdArr).split(":")[1]; // lrs
		String className = Util.trim(classArr).split(":")[1]; // L170M01A
		String brNo = Util.trim(brNoArr).split(":")[1]; // 931
		String rpa = Util.trim(rpaArr).split(":")[1]; // 931

		LOGGER.info("傳入參數==>[{}]", mainId + ";" + fileId + ";" + className);

		if (Util.equals(className, "L170M01A") && Util.equals(fileId, "lrs")) {
			//企金覆審
			L170M01A l170m01a = l170m01aDao.findByMainId(mainId);
			if (l170m01a != null) {
				try {
					// 設定上傳檔案資訊
					DocFile docFile = new DocFile();
					docFile.setBranchId(brNo);
					docFile.setContentType("application/pdf");
					docFile.setMainId(l170m01a.getMainId());
					docFile.setPid(null);
					docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
					docFile.setFieldId(fileId);
					docFile.setDeletedTime(null);
					docFile.setSrcFileName(l170m01a.getCustId() + " "
							+ l170m01a.getCustName() + "擔保品設定資料表"
							+ CapDate.getCurrentDate("yyyy-MM-dd") + ".pdf");
					docFile.setUploadTime(CapDate.getCurrentTimestamp());
					docFile.setSysId(docFileService.getSysId());
					docFile.setFileSize(outputStream.toByteArray().length);
					docFile.setFileDesc("擔保品設定資料表"
							+ (Util.equals(rpa, "Y") ? "(系統產生)" : ""));
					docFile.setTotPages(224);
					docFile.setFlag((Util.equals(rpa, "Y") ? "R" : ""));
					docFile.setData(outputStream.toByteArray());
					String fileKey = docFileService.save(docFile);
				} catch (Exception e) {
					String errorMsg = e.getMessage();
					LOGGER.error(e.getMessage(), getClass());
					e.printStackTrace();
					throw new CapMessageException("PDF寫入檔案錯誤:" + errorMsg,
							getClass());
				}
			}
		} else if (Util.equals(className, "C241M01A") && Util.equals(fileId, "crs")) {
			//消金覆審
			C241M01A c241m01a = c241m01aDao.findByMainId(mainId);
			if (c241m01a != null) {
				try {
					// 設定上傳檔案資訊
					DocFile docFile = new DocFile();
					docFile.setBranchId(brNo);
					docFile.setContentType("application/pdf");
					docFile.setMainId(c241m01a.getMainId());
					docFile.setPid(null);
					docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
					docFile.setFieldId(fileId);
					docFile.setDeletedTime(null);
					docFile.setSrcFileName(c241m01a.getCustId() + " "
							+ c241m01a.getCustName() + "擔保品設定資料表"
							+ CapDate.getCurrentDate("yyyy-MM-dd") + ".pdf");
					docFile.setUploadTime(CapDate.getCurrentTimestamp());
					docFile.setSysId(docFileService.getSysId());
					docFile.setFileSize(outputStream.toByteArray().length);
					docFile.setFileDesc("擔保品設定資料表"
							+ (Util.equals(rpa, "Y") ? "(系統產生)" : ""));
					docFile.setTotPages(224);
					docFile.setFlag((Util.equals(rpa, "Y") ? "R" : ""));
					docFile.setData(outputStream.toByteArray());
					String fileKey = docFileService.save(docFile);
				} catch (Exception e) {
					String errorMsg = e.getMessage();
					LOGGER.error(e.getMessage(), getClass());
					e.printStackTrace();
					throw new CapMessageException("PDF寫入檔案錯誤:" + errorMsg,
							getClass());
				}
			}
		}
	}
}
