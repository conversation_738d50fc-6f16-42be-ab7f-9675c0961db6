package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S02B;

/** 大數據風險報告查詢結果 **/
public interface C101S02BDao extends IGenericDao<C101S02B> {

	C101S02B findByOid(String oid);

	List<C101S02B> findByMainId(String mainId);

	C101S02B findByUniqueKey(String mainId, String custId, String dupNo);

	public int deleteByOid(String oid);

	List<C101S02B> findByList(String mainId, String custId,
			String dupNo);
}