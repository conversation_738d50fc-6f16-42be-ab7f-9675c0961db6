$(function() {
	$('.bottomFrame').show();
	
	$.urlParam = function(name){
	    var results = new RegExp('[\?&]' + name + '=([^&#]*)').exec(window.location.href);
	    if (results == null){
	       return null;
	    }
	    else {
	       return decodeURI(results[1]) || 0;
	    }
	}
	
	if (window.location.href.indexOf('maintain') > -1
		&& $.urlParam('source') == 'COL') {
		
		setTimeout(function(){
            $("a[href='#']:contains('貸後管理追蹤檢核表維護')").click();
        }, 600)
		
		setTimeout(function(){
            $("a[href='../../app/fms/lms8000v05?txCode=336094']:contains('查詢未完成案件')").click();
        }, 800)
	}
	
	
	// 調整topMenu size
	var paddingSize = $("#menu dd").length > 8 ? "0 13px" : "0 20px";
	$("#menu a").css("padding", paddingSize);
	$("#menu a[href $= 'app/home/<USER>']").click(linkToCOL);
	
	//J-109-0363_05097_B1001 Web e-Loan授信修改日本地區分行大阪分行授權外案件經由東京分行放行後送呈授信審查處(Country Head 模式)。 
	$.ajax({
        handler: "codetypehandler",
        data: {
            formAction: "queryBranchData2"
        },
        success: function(obj){
            
            switch (obj.country) {
				case "JP":	
					if(userInfo.userLocale == "zh_TW"){
						$("#menu2 a[href*='331021']").html('<span class=\"menu-icon icon-5\"></span>呈管理行');
						$("#menu2 a[href*='331022']").html('<span class=\"menu-icon icon-5\"></span>待覆核');
				    	$("#menu2 a[href*='331023']").html('<span class=\"menu-icon icon-5\"></span>管理行待登錄');
				    	$("#menu2 a[href*='331024']").html('<span class=\"menu-icon icon-5\"></span>管理行待覆核');
					}
			    	break;
	            default:
	                 
	                break;
	        }
        }
    });
	
	
});

function linkToCOL(e) {
	e.preventDefault();
    MegaApi.linkToMS({
        system: "COL",
        url: "app/home/<USER>",
		mainId : "COL_" + userInfo.unitNo
    });
}