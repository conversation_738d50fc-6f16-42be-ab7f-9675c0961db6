/* 
 * C999S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金約據書產品種類檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C999S01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C999S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	@ToStringExclude
	@OneToMany(mappedBy = "c999s01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C999S01B> c999s01bs;

	public List<C999S01B> getC999s01bs() {
		return c999s01bs;
	}

	public void setC999s01bs(List<C999S01B> c999s01bs) {
		this.c999s01bs = c999s01bs;
	}	
	
	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * UID<p/>
	 * 2012/08/14新增<br/>
	 *  關聯CLS.C999S01B.pid
	 */
	@Column(name="UID", length=32, columnDefinition="CHAR(32)")
	private String uid;

	/** 
	 * 項次<p/>
	 * 例如：甲、乙、丙…
	 */
	@Column(name="ITEMNO", columnDefinition="DECIMAL(3,0)")
	private Integer itemNo;

	/** 
	 * 刪除註記<p/>
	 * 2012/08/06新增<br/>
	 *  1.執行【刪除】時，寫入執行刪除時間<br/>
	 *  2.執行【取消刪除】時，清除刪除時間
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Date deletedTime;

	/** 額度序號 **/
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 產品種類 **/
	@Column(name="PRODKIND", length=2, columnDefinition="CHAR(2)")
	private String prodKind;

	/** 科目 **/
	@Column(name="SUBJCODE", length=8, columnDefinition="VARCHAR(8)")
	private String subjCode;

	/** 幣別 **/
	@Column(name="LOANCURR", length=3, columnDefinition="CHAR(3)")
	private String loanCurr;

	/** 金額 **/
	@Column(name="LOANAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal loanAmt;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得UID<p/>
	 * 2012/08/14新增<br/>
	 *  關聯CLS.C999S01B.pid
	 */
	public String getUid() {
		return this.uid;
	}
	/**
	 *  設定UID<p/>
	 *  2012/08/14新增<br/>
	 *  關聯CLS.C999S01B.pid
	 **/
	public void setUid(String value) {
		this.uid = value;
	}

	/** 
	 * 取得項次<p/>
	 * 例如：甲、乙、丙…
	 */
	public Integer getItemNo() {
		return this.itemNo;
	}
	/**
	 *  設定項次<p/>
	 *  例如：甲、乙、丙…
	 **/
	public void setItemNo(Integer value) {
		this.itemNo = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 2012/08/06新增<br/>
	 *  1.執行【刪除】時，寫入執行刪除時間<br/>
	 *  2.執行【取消刪除】時，清除刪除時間
	 */
	public Date getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  2012/08/06新增<br/>
	 *  1.執行【刪除】時，寫入執行刪除時間<br/>
	 *  2.執行【取消刪除】時，清除刪除時間
	 **/
	public void setDeletedTime(Date value) {
		this.deletedTime = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得產品種類 **/
	public String getProdKind() {
		return this.prodKind;
	}
	/** 設定產品種類 **/
	public void setProdKind(String value) {
		this.prodKind = value;
	}

	/** 取得科目 **/
	public String getSubjCode() {
		return this.subjCode;
	}
	/** 設定科目 **/
	public void setSubjCode(String value) {
		this.subjCode = value;
	}

	/** 取得幣別 **/
	public String getLoanCurr() {
		return this.loanCurr;
	}
	/** 設定幣別 **/
	public void setLoanCurr(String value) {
		this.loanCurr = value;
	}

	/** 取得金額 **/
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}
	/** 設定金額 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
