/* 
 * C140A01ADaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.lms.dao.C140A01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C140A01A;
import com.mega.eloan.lms.model.C140A01A_;
import com.mega.eloan.lms.model.C140M01A;
import com.mega.sso.context.MegaSSOSecurityContext;

/**
 * <pre>
 * 徵信調查報告書  授權 Dao
 * </pre>
 * 
 * @since 2011/11/16
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
@Repository
public class C140A01ADaoImpl extends LMSJpaDao<C140A01A, String> implements
		C140A01ADao {
	
	@Override
	public C140A01A findOwnAuth(C140M01A meta){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140A01A_.mainId.getName(), meta.getMainId());
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140A01A_.pid.getName(), meta.getUid());
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140A01A_.authUnit.getName(), MegaSSOSecurityContext.getUnitNo());
		return findUniqueOrNone(search);
	}
	
	
	@Override
	public C140A01A findModify(C140M01A meta){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140A01A_.mainId.getName(), meta.getMainId());
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140A01A_.pid.getName(), meta.getUid());
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140A01A_.authType.getName(), DocAuthTypeEnum.MODIFY.getCode());
		return findUniqueOrNone(search);
	}
}// ;
