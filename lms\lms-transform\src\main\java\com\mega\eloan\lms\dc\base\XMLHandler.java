package com.mega.eloan.lms.dc.base;

import java.util.ArrayList;

import org.apache.commons.lang.StringUtils;
import org.apache.xpath.XPathAPI;
import org.apache.xpath.domapi.XPathEvaluatorImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.ls.DOMImplementationLS;
import org.w3c.dom.ls.LSSerializer;
import org.w3c.dom.traversal.NodeIterator;
import org.w3c.dom.xpath.XPathEvaluator;
import org.w3c.dom.xpath.XPathNSResolver;
import org.w3c.dom.xpath.XPathResult;

import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

public class XMLHandler {
	private Logger logger = LoggerFactory.getLogger(XMLHandler.class);
	// 先將小寫轉大寫再來比對資料
	private static final String ITEM_PATH = "//item[translate(@name,'abcdefghijklmnopqrstuvwxyz','ABCDEFGHIJKLMNOPQRSTUVWXYZ')='";

	public String getItemValue(Document domDoc, String strKey) {
		if (StringUtils.isBlank(strKey)) {
			return "";
		}
		String strItem = ITEM_PATH + strKey.toUpperCase() + "']";
		return readNodeValue(domDoc, strItem, strKey).replaceAll(
				TextDefine.SYMBOL_SEMICOLON, "；");// 後面的"；"是全形
	}

	/**
	 * <pre>
	 * 取多重值(預設分隔符號為";")
	 * ex:  &lt;txtlst&gt;&lt;txt&gt;AAAA&lt;/txt&gt;&lt;txt&gt;BBBB&lt;/txt&gt;...&lt;/txtlst&gt;
	 * 取得的值會是 AAAA;BBBB...
	 * </pre>
	 * 
	 * @param domDoc
	 * @param strName
	 *            String:name欄位值
	 * @return sb
	 * @throws Exception
	 */
	public String getItemValueByMup(Document domDoc, String strName)
			throws Exception {
		return getItemValueByMup(domDoc, strName, ";");
	}

	/**
	 * <pre>
	 * 取多重值
	 * ex: &lt;txtlst&gt;&lt;txt&gt;AAAA&lt;/txt&gt;&lt;txt&gt;BBBB&lt;/txt&gt;...&lt;/txtlst&gt;
	 * 取得的值會是 AAAA;BBBB... (中間;為分隔符號)
	 * </pre>
	 * 
	 * @param domDoc
	 * @param strName
	 * @param separated
	 *            分隔符號
	 * @return
	 * @throws Exception
	 */
	public String getItemValueByMup(Document domDoc, String strName,
			String separated) throws Exception {
		if (StringUtils.isBlank(strName)) {
			return "";
		}
		String strItem = ITEM_PATH + strName.toUpperCase() + "']//text";
		StringBuffer sb = new StringBuffer();
		NodeIterator nl = XPathAPI.selectNodeIterator(domDoc, strItem);
		Node node;
		while ((node = nl.nextNode()) != null) {
			// 先替換資料中如果有該分隔符號，把分隔符號換成全形
			sb.append(
					Util.nullToSpace(node.getTextContent()).replaceAll(
							"\\" + separated, Util.toFullCharString(separated)))
					.append(separated);
		}
		return sb.toString().replaceAll("\\" + separated + "$", "");
	}

	/**
	 * 將多重值取成陣列
	 * 
	 * @param domDoc
	 * @param itemName
	 * @return
	 * @throws Exception
	 */
	public String[] getItemValueByMupArray(Document domDoc, String itemName)
			throws Exception {
		if (StringUtils.isBlank(itemName)) {
			return new String[] {};
		}
		ArrayList<String> lst = new ArrayList<String>();
		String strItem = ITEM_PATH + itemName.toUpperCase() + "']//text";
		NodeIterator nl = XPathAPI.selectNodeIterator(domDoc, strItem);
		Node node;
		while ((node = nl.nextNode()) != null) {
			lst.add(Util.nullToSpace(node.getTextContent()));
		}
		return lst.toArray(new String[] {});
	}

	public static final String BREAK_BR = "1";
	public static final String BREAK_N = "2";

	/**
	 * for richText 中有break使用(將break替換成p)
	 * 
	 * @param document
	 * @param strName
	 *            String:name欄位值
	 * @return sb
	 */
	public String getItemValueByRichText(Document document, String strName) {
		return getItemValueByRichText(document, strName, "text", BREAK_BR);
	}

	/**
	 * for richText 中有break使用(將break替換成\n)
	 * 
	 * @param document
	 * @param strName
	 *            String:name欄位值
	 * @return sb
	 */
	public String getItemValueByRichTextType2(Document document, String strName) {
		return getItemValueByRichText(document, strName, "text", BREAK_N);
	}

	/**
	 * for richText 中有break使用(將break替換成p)
	 * 
	 * @param document
	 * @param strName
	 *            String:name欄位值
	 * @return sb
	 */
	public String getItemValueByRichText(Document document, String strName,
			String tag, String type) {
		if (StringUtils.isBlank(strName)) {
			return "";
		}
		String xpath = ITEM_PATH + strName.toUpperCase() + "']//" + tag;
		Node node = selectSingleNode(document, xpath);
		if (TextDefine.RICHTEXT.equalsIgnoreCase(tag)) {
			return innerXml(node);
		}
		if (BREAK_N.equals(type)) {
			return nodeToStringByTextType2(node);
		}
		return nodeToStringByText(node);
	}

	public String readNodeValue(org.w3c.dom.Document domDoc, String xpath,
			String strKey) {
		String str = "";
		try {
			NodeIterator nl = XPathAPI.selectNodeIterator(domDoc, xpath);
			Node node;
			while ((node = nl.nextNode()) != null) {
				str = printNode(node, strKey);
			}
		} catch (Exception err) {
		}
		return Util.nullToSpace(str);
	}

	@SuppressWarnings("unused")
	private String printNode(Node node, String indent) {
		switch (node.getNodeType()) {

		case Node.DOCUMENT_NODE:
			NodeList nodes = node.getChildNodes();
			if (nodes != null) {
				for (int i = 0; i < nodes.getLength(); i++) {
					return printNode(nodes.item(i), "");
				}
			}
			break;

		case Node.ELEMENT_NODE:
			// recurse on each child
			NodeList children = node.getChildNodes();
			if (children != null) {
				for (int i = 0; i < children.getLength(); i++) {
					if (children.item(i).toString().trim().length() > 0) {
						return printNode(children.item(i), indent);
					}
				}
			}
			break;

		case Node.TEXT_NODE:
			return node.getNodeValue();
		}
		return "";
	}

	/**
	 * 讀取Dom物件中符合XPath所指定的第一個節點中的值 .
	 * 
	 * Note:呼叫此Mthod時傳入Document 會比傳入Node時使用較少的計憶體[memory resource].
	 * 
	 * @param document
	 * @param xpath
	 */
	public Node selectSingleNode(Document document, String xpath) {
		if (document == null) {
			this.logger.info("XmlTool" + "Warning->參數document =null。");
			return null;
		}

		Node nodeResult = null;
		// for XPath
		XPathEvaluator evaluator = null;
		XPathNSResolver resolver = null;
		// Create an XPath evaluator and pass in the document.
		evaluator = new XPathEvaluatorImpl(document);
		Node _node = document.getFirstChild();
		resolver = evaluator.createNSResolver(_node);

		XPathResult xpathResult = null;
		xpathResult = (XPathResult) evaluator.evaluate(xpath, _node, resolver,
				XPathResult.UNORDERED_NODE_ITERATOR_TYPE, null);

		nodeResult = xpathResult.iterateNext();
		// if (nodeResult == null) {
		// System.out.println("XmlTool" + "Warning->找不到[xpath=" + xpath
		// + "]所指定的節點。");
		// }
		return nodeResult;
	}

	/**
	 * 將XML節點內容轉為字串
	 * 
	 * @param node
	 * @return
	 */
	public String nodeToStringByText(Node node) {
		if (node == null) {
			return "";
		}
		NodeList childNodes = node.getChildNodes();
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < childNodes.getLength(); i++) {
			if ("break".equalsIgnoreCase(childNodes.item(i).getNodeName())) {
				sb.append("<br/>");
			} else {
				sb.append(childNodes.item(i).getNodeValue());
			}
		}
		return sb.toString().replaceAll("\r", "").replaceAll("\n", "")
				.replaceAll(TextDefine.SYMBOL_SEMICOLON, "；");
	}

	/**
	 * 將XML節點內容轉為字串
	 * 
	 * @param node
	 * @return
	 */
	public String nodeToStringByTextType2(Node node) {
		if (node == null) {
			return "";
		}
		NodeList childNodes = node.getChildNodes();
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < childNodes.getLength(); i++) {
			if ("break".equalsIgnoreCase(childNodes.item(i).getNodeName())) {
				sb.append("\n");
			} else {
				sb.append(childNodes.item(i).getNodeValue());
			}
		}
		return sb.toString();
	}

	/**
	 * 將XML節點內容轉為字串 for richtext html使用
	 * 
	 * @param node
	 * @return
	 */
	public String innerXml(Node node) {
		if (node == null) {
			return "";
		}
		DOMImplementationLS lsImpl = (DOMImplementationLS) node
				.getOwnerDocument().getImplementation().getFeature("LS", "3.0");
		LSSerializer lsSerializer = lsImpl.createLSSerializer();
		lsSerializer.getDomConfig().setParameter("xml-declaration", false);
		NodeList childNodes = node.getChildNodes();
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < childNodes.getLength(); i++) {
			sb.append(lsSerializer.writeToString(childNodes.item(i)));
		}
		return sb.toString();
	}
}
