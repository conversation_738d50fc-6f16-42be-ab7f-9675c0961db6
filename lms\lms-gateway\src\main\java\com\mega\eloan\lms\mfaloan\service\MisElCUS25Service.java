package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * MIS.ELCUS25
 * </pre>
 * 
 * @since 2011/11/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/4,jessica,new
 *          </ul>
 */
public interface MisElCUS25Service {

	/**
	 * 引進 MIS.ELCUS25 INNER JOIN MIS.ELCUS25
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	Map<String, Object> getMiselcus25(String custId, String dupNo);
	
	/**
	 * 引進Mis.ELCUS25
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<Map<String, Object>> findMiselcus25(String custId, String dupNo);
	
	/**
	 * 取得公司戶資料
	 * 
	 * @param custId
	 *            統一編號
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> findById(String custId);
	
	/**
	 * 有可能查到 0 筆資料
	 */
	public Map<String, Object> findByPk(String custId, String dupNo);
	
	public String getEjcicId_RpsVersion(String custId);
	
	/**
	 * J-111-0207 eloan國內企金管理系統，動用檢核表增加一項目:「信保案件負責人客戶基本資料檔是否已建有配偶資料」
	 * 用借款人查詢負責人
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> getMiselcus25_Principal(String custId, String dupNo);
}
