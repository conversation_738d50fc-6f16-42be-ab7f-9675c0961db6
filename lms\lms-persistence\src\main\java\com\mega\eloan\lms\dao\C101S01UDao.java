package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S01U;

/** 個金EJ標準查詢檔 **/
public interface C101S01UDao extends IGenericDao<C101S01U> {

	public C101S01U findByOid(String oid);
	
	public List<C101S01U> findByMainId(String mainId);
	public List<C101S01U> findByMainIdCustIdDupNo(String mainId, String custId, String dupNo);
	public List<C101S01U> findByMainIdCustIdDupNoTxid(String mainId, String custId, String dupNo, String txid);
	public List<C101S01U> findByMainIdTxidSendTimeBefore(String mainId, String[] txid_arr, String sendTimeCmp);
	public int deleteByOid(String oid);
	public C101S01U findLatestByCustIdDupNoTxid(String custId, String dupNo, String txid);
	public List<C101S01U> findByMainIdTxid(String mainId, String txid);
}