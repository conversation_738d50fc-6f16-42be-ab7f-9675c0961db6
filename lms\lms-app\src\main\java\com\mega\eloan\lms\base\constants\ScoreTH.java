package com.mega.eloan.lms.base.constants;


/**
 * <pre>
 * 泰國評等表
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
public interface ScoreTH {

	//房貸
	static final String 設定檔_House_TH_V1_0 = "cls/scoreTH.properties";
	static final String 設定檔_House_TH_V2_0 = "cls/scoreTH_V2_0.properties";
	
	//非房貸
	static final String 設定檔_notHouse_TH_V1_0 = "cls/scoreTH.properties";
	static final String 設定檔_notHouse_TH_V2_0 = "cls/scoreNotHouseLoanTH_V2_0.properties";

	static final String Comma = ",";
	static final String Point = ".";
	static final String Semicolon = ";";

	static final String 分數 = "score";
	static final String 公式 = "formula";
	static final String 欄位 = "columns";

	interface column {
		
		String 評等建立日期 = "grdCDate";
		String 評等調整日期 = "grdTDate";
		String 完成最終評等日期 = "ratingDate";		
		String NCB查詢日期 = "ncbQDate";
		String 模型版本 = "varVer";		
		String 初始評等 = "pRating";
		String 獨立評等 = "sRating";
		String 支援評等 = "sprtRating";
		String 調整評等 = "adjRating";
		String 最終評等 = "fRating";
		String 原始最終評等 = "orgFr";
		
		String 升降等_依風險點數 = "adj_pts";
		String 升降等_觸動特殊警訊 = "adj_matchS";
		String 升降等_依其他資訊 = "adj_ui";

		
		String 註記不調整 = "noAdj";
		String 調整狀態 = "adjustStatus";
		String 調整註記 = "adjustFlag";
		String 調整理由 = "adjustReason";
		
		//因子的原始資料				
		String 年薪幣別 = "raw_payCurr";
		String 出生日M1 = "raw_m1";		
		String 年薪金額 = "raw_payAmt";
		String 其它收入幣別 = "raw_otherCurr";
		String 其它收入金額 = "raw_otherAmt";
		String 家庭所得幣別 = "raw_hincomeCurr";
		String 財富管理_本行幣別 = "raw_invMBalCurr";
		String 財富管理_它行幣別 = "raw_invOBalCurr";
		String 金融機構存款往來情形幣別 = "raw_branAmtCurr";
		String 每月個人收入幣別 = "raw_totMmInCurr";
		String 每月個人支出幣別 = "raw_totMmExpCurr";
		String 轉換匯率_年薪 = "exRate_pay";
		String 轉換匯率_其他收入 = "exRate_oth";
		String 轉換匯率_家庭所得 = "exRate_hincome";		
		String 轉換匯率_財富管理本行  = "exRate_invMBal";
		String 轉換匯率_財富管理它行  = "exRate_invOBal";
		String 轉換匯率_金融機構存款往來情形 = "exRate_branAmt";
		String 轉換匯率_每月個人收入 = "exRate_totMmIn";
		String 轉換匯率_每月個人支出 = "exRate_totMmExp";
		
		String 月份數A5 = "raw_a5";
		String 個人年收入P3 = "raw_p3_idv";
		String 身份別P3 = "raw_p3_pos";
		//負面資訊
		String TH一般警訊1 = "chkItemTHG1";
		String TH一般警訊2 = "chkItemTHG2";
		String TH特殊警訊1 = "chkItemTHS1";
		String TH其他資訊1 = "chkItemTHO1";
		String TH其他資訊2 = "chkItemTHO2";
		String TH其他資訊3 = "chkItemTHO3";
		String 累加風險點數 = "sumRiskPt";
		
		//因子
		String 因子M5_職業 = "item_m5";
		String 因子M7_年資 = "item_m7";
		String 因子P3_夫妻年收入_幣別 = "item_p3_curr";
		String 因子P3_借款人及連保人之年收入合計 = "item_p3";
		String 因子P4_個人負債比率 = "item_p4";
		String 因子A5_契約年限 = "item_a5";
		String 因子Z1 = "item_z1";		
		String 因子Z2 = "item_z2";
		String 因子Z3_擔保率 = "item_z3";
		String 因子M1 = "item_m1";
		String 因子edu_學歷 = "item_edu";
		
		//標準化因子
		String 標準化M5 = "std_m5";
		String 標準化M7 = "std_m7";
		String 標準化P3 = "std_p3";
		String 標準化P4 = "std_p4";
		String 標準化A5 = "std_a5";
		String 標準化Z1 = "std_z1";		
		String 標準化Z2 = "std_z2";
		String 標準化Z3 = "std_z3";
		//加權因子
		String 加權M5 = "weight_m5";
		String 加權M7 = "weight_m7";
		String 加權P3 = "weight_p3";
		String 加權P4 = "weight_p4";
		String 加權A5 = "weight_a5";
		String 加權Z1 = "weight_z1";		
		String 加權Z2 = "weight_z2";
		String 加權Z3 = "weight_z3";
		//2.0新增因子
		String 加權M1 = "weight_m1";
		String 加權edu = "weight_edu";
		
		//權重分數(2.0新增)
		String 權重分數M5 = "weight_scr_m5";
		String 權重分數M7 = "weight_scr_m7";
		String 權重分數P3 = "weight_scr_p3";
		String 權重分數P4 = "weight_scr_p4";
		String 權重分數A5 = "weight_scr_a5";
		String 權重分數Z1 = "weight_scr_z1";		
		String 權重分數Z2 = "weight_scr_z2";
		String 權重分數M1 = "weight_scr_m1";
		String 權重分數edu = "weight_scr_edu";
		
		String 合計WeightedScore = "scr_core";
		String 核心模型分數 = "std_core";
		
		//3.0版新增
		String 預測壞率 = "pd";
		String 截距 = "interCept";
		String 斜率 = "slope";
		
	}

	interface type {
		String 泰國消金模型基本 = "thBase";
		String 泰國消金模型評等 = "thGrade";
		String 泰國消金模型違約機率 = "thDR";
	}
	
	/**
	 * <pre>
	 * 消金模型基本
	 * 名稱要和 score*.properties 中的設定相同 ，由 interfaceName+"."+fieldName
	 * </pre>
	 */
	interface thBase {		
		String 職業_M5= "scr_m5";
		String 年資_M7= "scr_m7";
		String 借款人及連保人之年收入合計_P3 = "scr_p3";
		String 個人負債比率_P4 = "scr_p4";
		String 契約年限_A5 = "scr_a5";
		String 擔保品地點及種類_Z1 = "scr_z1";
		String 市場環境及變現性_Z2 = "scr_z2";
		String 擔保率_Z3 = "scr_z3";
	}
	
	interface thBase_V2_0{		
		String 職業_M5= "scr_m5";
		String 年資_M7= "scr_m7";
//		String 借款人及連保人之年收入合計_P3 = "scr_p3";
		String 個人負債比率_P4 = "scr_p4";
		String 契約年限_A5 = "scr_a5";
		String 擔保品地點及種類_Z1 = "scr_z1";
		String 市場環境及變現性_Z2 = "scr_z2";
		String 學歷_EDU = "scr_edu";
		String 年齡_M1 = "scr_m1";
	}
	
	interface thBase_V2_0_P3_TH {
		String 借款人及連保人之年收入合計_P3_TH = "scr_p3";
	}
	interface thBase_V2_0_P3_VN {
		String 借款人及連保人之年收入合計_P3_TH = "scr_p3";
	}

	/**
	 * <pre>
	 * 消金模型評等等級項目
	 * </pre>
	 */
	interface thGrade {
		String 等級 = "level";
	}	

	/**
	 * <pre>
	 * 消金模型違約機率
	 * </pre>
	 */
	interface thDR {
		String 違約機率_預估3年期 = "dr_3yr";
		String 違約機率_預估1年期 = "dr_1yr";
	}
	
	interface thDR_V2_0 {
		String 違約機率_預估1年期 = "dr_1yr";
	}
}
