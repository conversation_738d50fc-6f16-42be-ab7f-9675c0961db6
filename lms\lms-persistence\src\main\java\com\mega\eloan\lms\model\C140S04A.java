package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.RelativeMeta;


/**
 * <pre>
 * C140S04A model.
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/20,<PERSON>,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140S04A-entity-graph", attributeNodes = { @NamedAttributeNode("c140m04a") })
@Entity
@Table(name="C140S04A", uniqueConstraints = @UniqueConstraint(columnNames ={"oid"}))
public class C140S04A extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

	@Column(precision=12)
	private BigDecimal amtUnitST;

	@Column(length=60)
	private String invBusna1;

	@Column(length=3)
	private String invCap11;

	@Column(precision=12)
	private BigDecimal invCap21;

	@Column(length=60)
	private String invKind1;

	@Column(length=10)
	private String invTit1;

	@Column(precision=12)
	private BigDecimal invUnit11;

	@Column(precision=12)
	private BigDecimal invUnit21;

	//bi-directional many-to-one association to C140M04A
    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false)
		})
	private C140M04A c140m04a;

	public BigDecimal getAmtUnitST() {
		return this.amtUnitST;
	}

	public void setAmtUnitST(BigDecimal amtUnitST) {
		this.amtUnitST = amtUnitST;
	}

	public String getInvBusna1() {
		return this.invBusna1;
	}

	public void setInvBusna1(String invBusna1) {
		this.invBusna1 = invBusna1;
	}

	public String getInvCap11() {
		return this.invCap11;
	}

	public void setInvCap11(String invCap11) {
		this.invCap11 = invCap11;
	}

	public BigDecimal getInvCap21() {
		return this.invCap21;
	}

	public void setInvCap21(BigDecimal invCap21) {
		this.invCap21 = invCap21;
	}

	public String getInvKind1() {
		return this.invKind1;
	}

	public void setInvKind1(String invKind1) {
		this.invKind1 = invKind1;
	}

	public String getInvTit1() {
		return this.invTit1;
	}

	public void setInvTit1(String invTit1) {
		this.invTit1 = invTit1;
	}

	public BigDecimal getInvUnit11() {
		return this.invUnit11;
	}

	public void setInvUnit11(BigDecimal invUnit11) {
		this.invUnit11 = invUnit11;
	}

	public BigDecimal getInvUnit21() {
		return this.invUnit21;
	}

	public void setInvUnit21(BigDecimal invUnit21) {
		this.invUnit21 = invUnit21;
	}

	public C140M04A getC140m04a() {
		return this.c140m04a;
	}

	public void setC140m04a(C140M04A c140m04a) {
		this.c140m04a = c140m04a;
	}
	
}