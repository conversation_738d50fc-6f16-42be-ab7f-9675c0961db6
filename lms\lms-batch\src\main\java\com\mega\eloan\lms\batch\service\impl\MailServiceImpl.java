package com.mega.eloan.lms.batch.service.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.JSONUtil;
import com.mega.eloan.lms.fms.service.CLS9021Service;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * batch 報表
 * </pre>
 * 
 * @since 2013/07/09
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/07/09,Vector,new
 *          </ul>
 */
@Service("MailServiceImpl")
public class MailServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(MailServiceImpl.class);

	private static final long serialVersionUID = 1L;


	@Resource
	CLS9021Service service;
	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		String today = CapDate.getCurrentDate("yyyy-MM-dd");
		long t1 = System.currentTimeMillis();

		LOGGER.info(StrUtils.concat("[" + today
				+ "] The Batch for email is starting."));
		// 可以從json內取得參數
		if (LOGGER.isTraceEnabled()) {
			LOGGER.trace("傳入參數" + json.toString());
		}
		StringBuilder failItem = new StringBuilder();
		/* 批次開始 */
		
		String key = "kindNo";
		String kindNo = Util.trim(json.get(key));
		JSONObject request = json.getJSONObject("request");
		if(request != null){
			if(request.containsKey(key)){
				kindNo = Util.trim(request.getString(key));
			}
		}
		LOGGER.info("kindNo["+kindNo+"]");
		if(Util.isEmpty(kindNo)){
			kindNo = "5";
		}				
		String version = Util.trim(request.optString("version"));
		Map<String, String> paramMap = new HashMap<String, String>();
		paramMap.putAll(JSONUtil.parseJsonToStringMap(request));
		if(!service.sendMail(kindNo, version, paramMap)){
			failItem.append("發送失敗");
		}
		

		LOGGER.info("[" + today + "]The Batch for email is finish.Cost time : "
				+ (System.currentTimeMillis() - t1));

		JSONObject mag = new JSONObject();
		if (Util.isEmpty(failItem.toString())) {
			mag = WebBatchCode.RC_SUCCESS;
		} else {
			mag = WebBatchCode.RC_ERROR;
			mag.element(WebBatchCode.P_RC_MSG, failItem.toString());
		}
		return mag;
	}
}
