/* 
 * LMS1815ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.dao.L181A01ADao;
import com.mega.eloan.lms.dao.L181M01ADao;
import com.mega.eloan.lms.dao.L181M01BDao;
import com.mega.eloan.lms.dw.service.DWAslndavgovsService;
import com.mega.eloan.lms.eloandb.service.Lms412Service;
import com.mega.eloan.lms.lrs.service.LMS1805Service;
import com.mega.eloan.lms.lrs.service.LMS1815Service;
import com.mega.eloan.lms.model.L181A01A;
import com.mega.eloan.lms.model.L181M01A;
import com.mega.eloan.lms.model.L181M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS1815ServiceImpl extends AbstractCapService implements
		LMS1815Service {
	private static Logger logger = LoggerFactory
			.getLogger(LMS1815ServiceImpl.class);

	@Resource
	TempDataService tempDataService;

	@Resource
	L181M01ADao l181m01aDao;

	@Resource
	L181M01BDao l181m01bDao;

	@Resource
	L181A01ADao l181a01aDao;

	@Resource
	LMS1805Service lms1805Service;

	@Resource
	FlowService flowService;

	@Resource
	DocLogService docLogService;

	@Resource
	Lms412Service lms412Service;

	@Resource
	DWAslndavgovsService dwAslndavgovsService;

	public void startFlow(String mainOid) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		flowService.start("LMS1815Flow", mainOid, user.getUserId(),
				user.getUnitNo());
	}

	/**
	 * 跑flow
	 * 
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType) throws Throwable {
		L181M01A l181m01a = (L181M01A) model;
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS1815Flow", mainOid,
						user.getUserId(), user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				if (resultType) {
					List<L181M01B> l181m01bs = findL180m02bByMainId(l181m01a
							.getMainId());
					for (L181M01B l181m01b : l181m01bs) {
						if ("2".equals(l181m01b.getType())) {

							List<Map<String, Object>> list = lms412Service
									.findLms412ByCustIdNoMis(
											l181m01a.getElfBranch(),
											l181m01a.getCustId(),
											l181m01a.getDupNo());
							String elfProcess = Util.truncateString(
									l181m01b.getElfProcess(), 200);
							String elfNckdMemo = Util.truncateString(
									l181m01b.getElfNCkdMemo(), 200);
							String elfMemo = Util.truncateString(
									l181m01b.getElfMemo(), 200);
							if (Util.isEmpty(list) || list.size() == 0) {
								lms412Service.addLMS412ByMainTain(
										l181m01b.getElfMainCust(),
										l181m01b.getElfCrdType(),
										l181m01b.getElfCrdTTbl(),
										l181m01b.getElfMowType(),
										l181m01b.getElfMowTbl1(),
										l181m01b.getElfLRDate(),
										l181m01b.getElfRCkdLine(),
										l181m01b.getElfMDFlag(),
										l181m01b.getElfMDDt(),
										elfProcess,
										l181m01b.getElfNewAdd(),
										l181m01b.getElfNewDate(),
										l181m01b.getElfNCkdFlag(),
										l181m01b.getElfNCkdDate(),
										elfNckdMemo,
										l181m01b.getElfDBUOBU(),
										CapDate.getCurrentTimestamp(),
										l181m01a.getUpdater(),
										elfMemo,
										CapDate.getCurrentTimestamp(),
										l181m01b.getElfUCkdLINE(),
										l181m01b.getElfUCkdDt(),
										l181m01b.getElfNextNwDt(),
										l181m01a.getUCase(),
										l181m01a.getUCaseRole(),
										
										l181m01b.getElfIsAllNew(), // J-108-0078_05097_B1001
										// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
										
										l181m01a.getElfBranch(),
										l181m01a.getCustId(),
										l181m01a.getDupNo());
							} else {
								this.lms412Service.saveBymaintain(
										l181m01b.getElfMainCust(),
										Util.trim(l181m01b.getElfCrdType()),
										Util.trim(l181m01b.getElfCrdTTbl()),
										Util.trim(l181m01b.getElfMowType()),
										Util.trim(l181m01b.getElfMowTbl1()),
										l181m01b.getElfLRDate(),
										Util.trim(l181m01b.getElfRCkdLine()),
										Util.trim(l181m01b.getElfMDFlag()),
										l181m01b.getElfMDDt(),
										elfProcess,
										Util.trim(l181m01b.getElfNewAdd()),
										l181m01b.getElfNewDate(),
										Util.trim(l181m01b.getElfNCkdFlag()),
										l181m01b.getElfNCkdDate(),
										elfNckdMemo,
										Util.trim(l181m01b.getElfDBUOBU()),
										CapDate.getCurrentTimestamp(),
										l181m01a.getUpdater(),
										elfMemo,
										CapDate.getCurrentTimestamp(),
										Util.trim(l181m01b.getElfUCkdLINE()),
										l181m01b.getElfUCkdDt(),
										l181m01b.getElfNextNwDt(),
										Util.trim(l181m01a.getUCase()),
										Util.trim(l181m01a.getUCaseRole()),
										
										l181m01b.getElfIsAllNew(), // J-108-0078_05097_B1001
										// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。

										
										Util.trim(l181m01a.getElfBranch()),
										Util.trim(l181m01a.getCustId()),
										Util.trim(l181m01a.getDupNo()));
							}
						}
					}
				}
				inst.setAttribute("result", resultType ? "核定" : "退回");
			}
			inst.next();
			// l181m01aDao.save(l181m01a);
		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	@Override
	public void deleteL180m02a(String oid) {
		L181M01A l180m02a = findModelByOid(L181M01A.class, oid);
		String mainId = l180m02a.getMainId();
		deleteL180m02bList(mainId);
		delete(L181M01A.class, l180m02a.getOid());
	}

	@Override
	public L181M01A fingL180m02aByBranch(String branch, String custId,
			String dupno, String ctlType) {
		return l181m01aDao.findByBranchCustId(branch, custId, dupno, ctlType);
	}

	@Override
	public void deleteL180m02bList(String mainId) {
		List<L181M01B> l180m02bs = findL180m02bByMainId(mainId);
		l181m01bDao.delete(l180m02bs);
	}

	@Override
	public List<L181M01B> findL180m02bByMainId(String MainId) {
		return l181m01bDao.findByMainId(MainId);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L181M01A.class) {
			return (T) l181m01aDao.find(oid);
		} else if (clazz == L181M01B.class) {
			return (T) l181m01bDao.find(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L181M01A.class) {
			return l181m01aDao.findPage(search);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L181M01A) {
					((L181M01A) model).setRandomCode(IDGenerator
							.getRandomCode());
					((L181M01A) model).setUpdater(user.getUserId());
					((L181M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());

					if (((L181M01A) model).getCreator() == null) {
						((L181M01A) model).setCreator(user.getUserId());
						((L181M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L181M01A) model)
								.getMainId());
					}
					String oid = ((L181M01A) model).getOid();
					l181m01aDao.save(((L181M01A) model));
					if (Util.isEmpty(oid)) {
						startFlow(((L181M01A) model).getOid());
						docLogService.record(((L181M01A) model).getOid(),
								DocLogEnum.CREATE);
					} else {
						docLogService.record(((L181M01A) model).getOid(),
								DocLogEnum.SAVE);
					}
				} else if (model instanceof L181M01B) {
					l181m01bDao.save(((L181M01B) model));
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L181M01B) model)
								.getMainId());
					}
					docLogService.record(((L181M01B) model).getOid(),
							DocLogEnum.SAVE);
				} else if (model instanceof L181A01A) {
					l181a01aDao.save(((L181A01A) model));
					docLogService.record(((L181A01A) model).getOid(),
							DocLogEnum.SAVE);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public void delete(Class clazz, String oid) {
		if (clazz == L181M01A.class) {
			L181M01A l180m02a = l181m01aDao.findByOid(oid);
			l181m01aDao.delete(l180m02a);
			docLogService.record(l180m02a.getOid(), DocLogEnum.DELETE);
		} else if (clazz == L181M01B.class) {
			L181M01B l180m02b = l181m01bDao.findByOid(oid);
			l181m01bDao.delete(l180m02b);
			docLogService.record(l180m02b.getOid(), DocLogEnum.DELETE);
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public void saveNew(L181M01A l181m01a, List<L181M01B> l181m01bList) {

		L181M01B l181m01b = null;
		L181M01B l180m02bNew = null;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		l181m01a.setUnitType(UnitTypeEnum.convertToUnitType(user.getUnitType()));
		l181m01a.setOwnBrId(user.getUnitNo());
		for (L181M01B temp : l181m01bList) {
			if ("1".equals(temp.getType())) {
				l181m01b = temp;
			} else if ("2".equals(temp.getType())) {
				l180m02bNew = temp;
			}
		}

		// 授權檔
		L181A01A l181a01a = new L181A01A();
		l181a01a.setMainId(l181m01a.getMainId());
		l181a01a.setOwnUnit(user.getUnitNo());
		l181a01a.setOwner(user.getUserId());
		l181a01a.setAuthUnit(user.getUnitNo());
		l181a01a.setAuthType("1");
		save(l181m01a, l181a01a);

		// 1:原始資料 2:修改資料
		if (l181m01b == null) {
			l181m01b = new L181M01B();
			Map lms412 = lms412Service.findLms412Uni(l181m01a.getElfBranch(),
					l181m01a.getCustId(), l181m01a.getDupNo());
			if (lms412 != null) {
				l181m01b.setElfMainCust(Util.trim(lms412.get("MAINCUST")));
				l181m01b.setElfCrdType(Util.trim(lms412.get("CRDTYPE")));
				l181m01b.setElfCrdTTbl(Util.trim(lms412.get("CRDTTBL")));
				l181m01b.setElfMowType(Util.trim(lms412.get("MOWTYPE")));
				l181m01b.setElfMowTbl1(Util.trim(lms412.get("MOWTBL1")));
				l181m01b.setElfLRDate((Date) lms412.get("LRDATE"));
				l181m01b.setElfRCkdLine(Util.trim(lms412.get("RCKDLINE")));
				l181m01b.setElfUCkdLINE(Util.trim(lms412.get("UCKDLINE")));
				l181m01b.setElfUCkdDt((Date) lms412.get("UCKDDT"));
				l181m01b.setElfMDFlag(Util.trim(lms412.get("MDFLAG")));
				l181m01b.setElfMDDt((Date) lms412.get("MDDT"));
				l181m01b.setElfProcess(Util.trim(lms412.get("PROCESS")));
				l181m01b.setElfNewAdd(Util.trim(lms412.get("NEWADD")));
				String newDate = Util.trim(lms412.get("NEWDATE"));
				l181m01b.setElfNewDate(newDate);
				l181m01b.setElfNCkdFlag(Util.trim(lms412.get("NCKDFLAG")));
				l181m01b.setElfNCkdDate((Date) lms412.get("NCKDDATE"));
				l181m01b.setElfNCkdMemo(Util.trim(lms412.get("NCKDMEMO")));
				l181m01b.setElfNextNwDt((Date) lms412.get("NEXTNWDT"));
				l181m01b.setElfDBUOBU(Util.trim(lms412.get("DBUOBU")));
				l181m01b.setElfMemo(Util.trim(lms412.get("MEMO")));

				// J-108-0078_05097_B1001
				// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
				l181m01b.setElfIsAllNew(Util.trim(lms412.get("ISALLNEW")));

			}

			l181m01b.setMainId(l181m01a.getMainId());
			l181m01b.setType("1");
			this.save(l181m01b);
		}
		if (l180m02bNew == null) {
			l180m02bNew = new L181M01B();
			try {
				CapBeanUtil.copyBean(l181m01b, l180m02bNew,
						CapBeanUtil.getFieldName(L181M01B.class, true));
			} catch (CapException e) {
				logger.error("LMS1815ServiceImpl saveNew EXCEPTION!!", e);
			}
			l180m02bNew.setOid(null);
			l180m02bNew.setMainId(l181m01a.getMainId());
			l180m02bNew.setType("2");
			this.save(l180m02bNew);
		}
	}

	@Override
	public Date cauNextDate(Map<String, Object> map) {
		if (map != null) {
			String[] value = lms1805Service.cauculateDate(null, map, 3);
			return CapDate.parseDate(value[0]);
		}
		return null;
	}

	@Override
	public Date cauNextDate(String oid) {
		L181M01A l180m02a = findModelByOid(L181M01A.class, oid);
		List<L181M01B> l180m02bs = findL180m02bByMainId(l180m02a.getMainId());
		for (L181M01B l180m02b : l180m02bs) {
			if ("2".equals(Util.trim(l180m02b.getType()))) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("NCKDFLAG", l180m02b.getElfNCkdFlag());
				String lrdate = "0001-01-01";
				if (!Util.isEmpty(l180m02b.getElfLRDate())) {
					lrdate = Util.addZeroWithValue(
							TWNDate.toAD(l180m02b.getElfLRDate()), 10);
				}
				// if(Util.isEmpty(lrdate) || "0001-01-01".equals(lrdate)){
				//
				// }
				map.put("LRDATE", CapDate.parseDate(lrdate));
				map.put("MAINCUST", l180m02b.getElfMainCust());
				map.put("MOWTBL1", l180m02b.getElfMowTbl1());
				map.put("MOWTYPE", l180m02b.getElfMowType());
				map.put("CRDTTBL", l180m02b.getElfCrdTTbl());
				map.put("NEWDATE", l180m02b.getElfNewDate());
				map.put("MDFLAG", l180m02b.getElfMDFlag());
				map.put("RCKDLINE", l180m02b.getElfRCkdLine());
				map.put("MDDT", l180m02b.getElfMDDt());
				map.put("UCKDLINE", l180m02b.getElfUCkdLINE());
				map.put("UCKDDT", l180m02b.getElfUCkdDt());

				// J-108-0078_05097_B1001
				// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
				map.put("BRANCH", l180m02a.getElfBranch());
				map.put("ISALLNEW", l180m02b.getElfIsAllNew());

				String[] value = lms1805Service.cauculateDate(null, map, 3);
				return CapDate.parseDate(value[0]);
			}
		}
		return null;
	}

	@Override
	public Map<String, Object> flowCases(String[] oids, boolean flowAction) {
		Map<String, Object> sucessOrNot = new HashMap<String, Object>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuilder failBank = new StringBuilder();
		boolean result = true;
		int count = 0;
		for (String oid : oids) {
			L181M01A meta = findModelByOid(L181M01A.class, oid);
			try {
				// System.out.println(user.getUserId());
				// System.out.println(l180m02a.getUpdater());
				if (!Util.trim(meta.getUpdater()).equals(user.getUserId())) {
					flowAction(meta.getOid(), meta, true, flowAction);
					count++;
				} else {
					result = false;
				}
			} catch (Throwable e) {
				logger.error("LMS1815ServiceImpl flowCases EXCEPTION!!", e);
				failBank.append(meta.getCustId() + ";");
			}
		}
		if (failBank == null || failBank.toString().isEmpty()) {
			if (count > 0) {
				sucessOrNot.put("success", true);
			} else {
				sucessOrNot.put("success", false);
			}
		} else {
			sucessOrNot.put("success", false);
			sucessOrNot.put("failBank", failBank.toString());
		}
		if (!result) {
			sucessOrNot.put("special", "Y");
		}
		return sucessOrNot;
	}

	@Override
	public String findCstate(L181M01A l181m01a) {
		Map<String, Object> map = this.dwAslndavgovsService.findCstate(
				l181m01a.getCustId(), l181m01a.getDupNo(),
				l181m01a.getElfBranch());
		int count = (Integer) map.get("TCOUNT");
		String normalFG = (String) map.get("NORMAL_FG");
		String ovFG = (String) map.get("OV_FG");
		String obFG = (String) map.get("OB_FG");
		String cdFG = (String) map.get("CD_FG");

		String status = "";
		if (count == 0) {
			l181m01a.setElfCState("0");
		} else {
			l181m01a.setElfCancelDt(null);
			if (UtilConstants.DEFAULT.是.equals(normalFG)) {
				l181m01a.setElfCState("1");
				status = "1"; // 正常
			} else {
				if (UtilConstants.DEFAULT.是.equals(cdFG)) {
					l181m01a.setElfCState("4");
					status = "4";// 呆帳
				} else if (UtilConstants.DEFAULT.是.equals(obFG)) {
					l181m01a.setElfCState("3");
					status = "3";// 催收
				} else if (UtilConstants.DEFAULT.是.equals(ovFG)) {
					l181m01a.setElfCState("2");
					status = "2";// 逾期
				} else {
					l181m01a.setElfCState("0");
					status = "0";// 無餘額
				}
			}
		}
		save(l181m01a);
		return status;
	}
}
