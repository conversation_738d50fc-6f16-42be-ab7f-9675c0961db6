/* 
 * L140M01MDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01M;

/** 維護央行購屋/空地/建屋貸款註記資訊 **/
public interface L140M01MDao extends IGenericDao<L140M01M> {

	L140M01M findByOid(String oid);

	L140M01M findByMainId(String mainId);
	
	L140M01M findByUniqueKey(String mainId, String cbcCase);

	List<L140M01M> findByIndex01(String mainId, String cbcCase);

}