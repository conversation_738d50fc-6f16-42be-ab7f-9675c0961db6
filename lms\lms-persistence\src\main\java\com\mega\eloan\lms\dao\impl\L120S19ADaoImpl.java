package com.mega.eloan.lms.dao.impl;

import com.mega.eloan.lms.dao.L120S19ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S19A;

import java.util.List;

import org.springframework.stereotype.Repository;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

/**
 * 決策平台 電文回傳
 **/
@Repository
public class L120S19ADaoImpl extends LMSJpaDao<L120S19A, String> implements L120S19ADao {

	@Override
	public L120S19A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S19A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return find(search);
	}
	@Override
	public List<L120S19A> findByMainIdItemType(String mainId, String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		return find(search);
	}
	@Override
	public List<L120S19A> findByMainIdItemTypeOrderBy(String mainId, String itemType,boolean isDesc) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		if(isDesc){
			search.addOrderBy("itemVersion", true);
		}else{
			search.addOrderBy("itemVersion", false);
		}
		return find(search);
	}

	@Override
	public L120S19A findByMainId_itemType_latest_itemVersion(String mainId, String itemType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		search.addOrderBy("itemVersion", true);
		return findUniqueOrNone(search);
	}

	@Override
	public L120S19A findByMainId_itemType_first_itemVersion(String mainId, String itemType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		search.addOrderBy("itemVersion", false);
		return findUniqueOrNone(search);
	}
}