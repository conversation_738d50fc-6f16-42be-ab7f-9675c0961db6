/* 
 * L120S05C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 借款人關係企業相關資料檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S05C", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class L120S05C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 借款人是否有關係企業<p/>
	 * Y/N（是/否）
	 */
	@Column(name="RLTFLAG", length=1, columnDefinition="CHAR(1)")
	private String rltFlag;

	/** 資料查詢日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="INQDATE", columnDefinition="DATE")
	private Date inqDate;

	/** 
	 * 授信總額度<p/>
	 * 單位：TWD仟元<br/>
	 *  (totAmtA+totAmtB)
	 */
	@Column(name="TOTAMT", columnDefinition="DECIMAL(13,0)")
	private Long totAmt;

	/** 
	 * 授信總額度(國內)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	@Column(name="TOTAMTA", columnDefinition="DECIMAL(13,0)")
	private Long totAmtA;

	/** 
	 * 授信總額度(海外)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	@Column(name="TOTAMTB", columnDefinition="DECIMAL(13,0)")
	private Long totAmtB;

	/** 授信總額度占本行淨值 **/
	@Column(name="TOTMEGA", columnDefinition="DECIMAL(5,2)")
	private Double totMega;

	/** 
	 * 無擔保授信總額度<p/>
	 * 單位：TWD仟元<br/>
	 *  (crdAmtA+crdAmtB)
	 */
	@Column(name="CRDAMT", columnDefinition="DECIMAL(13,0)")
	private Long crdAmt;

	/** 
	 * 無擔保授信總額度(國內)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	@Column(name="CRDAMTA", columnDefinition="DECIMAL(13,0)")
	private Long crdAmtA;

	/** 
	 * 無擔保授信總額度(海外)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	@Column(name="CRDAMTB", columnDefinition="DECIMAL(13,0)")
	private Long crdAmtB;

	/** 無擔保授信總額度占本行淨值 **/
	@Column(name="CRDMEGA", columnDefinition="DECIMAL(5,2)")
	private Double crdMega;

	/** 
	 * 授信總餘額<p/>
	 * 單位：TWD仟元<br/>
	 *  (lntAmtA+lntAmtB)
	 */
	@Column(name="LNTAMT", columnDefinition="DECIMAL(13,0)")
	private Long lntAmt;

	/** 
	 * 授信總餘額(國內)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	@Column(name="LNTAMTA", columnDefinition="DECIMAL(13,0)")
	private Long lntAmtA;

	/** 
	 * 授信總餘額(海外)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	@Column(name="LNTAMTB", columnDefinition="DECIMAL(13,0)")
	private Long lntAmtB;

	/** 授信總餘額占本行淨值 **/
	@Column(name="LNTMEGA", columnDefinition="DECIMAL(5,2)")
	private Double lntMega;

	/** 
	 * 無擔保授信總餘額<p/>
	 * 單位：TWD仟元<br/>
	 *  (lncAmtA+lncAmtB)
	 */
	@Column(name="LNCAMT", columnDefinition="DECIMAL(13,0)")
	private Long lncAmt;

	/** 
	 * 無擔保授信總餘額(國內)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	@Column(name="LNCAMTA", columnDefinition="DECIMAL(13,0)")
	private Long lncAmtA;

	/** 
	 * 無擔保授信總餘額(海外)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	@Column(name="LNCAMTB", columnDefinition="DECIMAL(13,0)")
	private Long lncAmtB;

	/** 無擔保授信總餘額占本行淨值 **/
	@Column(name="LNCMEGA", columnDefinition="DECIMAL(5,2)")
	private Double lncMega;

	/** 
	 * 不計入同一關係企業第一～四項者之合計額度<p/>
	 * 單位：TWD仟元<br/>
	 *  (若扣除可不計入同一關係企業第一～四項者之合計額度為TWD999,999,999仟元，占本行淨值的999.99%，合計餘額為TWD999,999,999仟元，占本行淨值的999.99%。)
	 */
	@Column(name="EXCAMT", columnDefinition="DECIMAL(13,0)")
	private Long excAmt;

	/** 不計入同一關係企業第一～四項者之合計額度占本行淨值 **/
	@Column(name="EXCMEGA", columnDefinition="DECIMAL(5,2)")
	private Double excMega;

	/** 
	 * 合計餘額<p/>
	 * 單位：TWD仟元
	 */
	@Column(name="SUMAMT", columnDefinition="DECIMAL(13,0)")
	private Long sumAmt;

	/** 占本行淨值 **/
	@Column(name="SUMMEGA", columnDefinition="DECIMAL(5,2)")
	private Double sumMega;

	/** 
	 * 本行轉投資等非授信業務金額<p/>
	 * 單位：TWD仟元
	 */
	@Column(name="OTHAMT", columnDefinition="DECIMAL(13,0)")
	private Long othAmt;

	/** 非授信業務占本行淨值 **/
	@Column(name="OTHMEGA", columnDefinition="DECIMAL(5,2)")
	private Double othMega;

	/** 
	 * 金融商品金額<p/>
	 * 單位：TWD仟元
	 */
	@Column(name="FINAMT", columnDefinition="DECIMAL(13,0)")
	private Long finAmt;

	/** 金融商品金額占本行淨值 **/
	@Column(name="FINMEGA", columnDefinition="DECIMAL(5,2)")
	private Double finMega;

	/** 
	 * 曝險總額<p/>
	 * 單位：TWD仟元
	 */
	@Column(name="RSKAMT", columnDefinition="DECIMAL(13,0)")
	private Long rskAmt;

	/** 曝險總額占本行淨值 **/
	@Column(name="RSKMEGA", columnDefinition="DECIMAL(5,2)")
	private Double rskMega;

	/** 
	 * 關係企業在全體金融機構授信總餘額 未 逾其淨值或營收<p/>
	 * Y/N（已/未）
	 */
	@Column(name="RLTOVER", length=1, columnDefinition="CHAR(1)")
	private String rltOver;

	/** 
	 * 輸入資料檢誤完成(Y/N)<p/>
	 * 100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name="CHKYN", length=1, columnDefinition="CHAR(1)")
	private String chkYN;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得借款人是否有關係企業<p/>
	 * Y/N（是/否）
	 */
	public String getRltFlag() {
		return this.rltFlag;
	}
	/**
	 *  設定借款人是否有關係企業<p/>
	 *  Y/N（是/否）
	 **/
	public void setRltFlag(String value) {
		this.rltFlag = value;
	}

	/** 取得資料查詢日 **/
	public Date getInqDate() {
		return this.inqDate;
	}
	/** 設定資料查詢日 **/
	public void setInqDate(Date value) {
		this.inqDate = value;
	}

	/** 
	 * 取得授信總額度<p/>
	 * 單位：TWD仟元<br/>
	 *  (totAmtA+totAmtB)
	 */
	public Long getTotAmt() {
		return this.totAmt;
	}
	/**
	 *  設定授信總額度<p/>
	 *  單位：TWD仟元<br/>
	 *  (totAmtA+totAmtB)
	 **/
	public void setTotAmt(Long value) {
		this.totAmt = value;
	}

	/** 
	 * 取得授信總額度(國內)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	public Long getTotAmtA() {
		return this.totAmtA;
	}
	/**
	 *  設定授信總額度(國內)<p/>
	 *  100/09/28調整<br/>
	 *  單位：TWD仟元
	 **/
	public void setTotAmtA(Long value) {
		this.totAmtA = value;
	}

	/** 
	 * 取得授信總額度(海外)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	public Long getTotAmtB() {
		return this.totAmtB;
	}
	/**
	 *  設定授信總額度(海外)<p/>
	 *  100/09/28調整<br/>
	 *  單位：TWD仟元
	 **/
	public void setTotAmtB(Long value) {
		this.totAmtB = value;
	}

	/** 取得授信總額度占本行淨值 **/
	public Double getTotMega() {
		return this.totMega;
	}
	/** 設定授信總額度占本行淨值 **/
	public void setTotMega(Double value) {
		this.totMega = value;
	}

	/** 
	 * 取得無擔保授信總額度<p/>
	 * 單位：TWD仟元<br/>
	 *  (crdAmtA+crdAmtB)
	 */
	public Long getCrdAmt() {
		return this.crdAmt;
	}
	/**
	 *  設定無擔保授信總額度<p/>
	 *  單位：TWD仟元<br/>
	 *  (crdAmtA+crdAmtB)
	 **/
	public void setCrdAmt(Long value) {
		this.crdAmt = value;
	}

	/** 
	 * 取得無擔保授信總額度(國內)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	public Long getCrdAmtA() {
		return this.crdAmtA;
	}
	/**
	 *  設定無擔保授信總額度(國內)<p/>
	 *  100/09/28調整<br/>
	 *  單位：TWD仟元
	 **/
	public void setCrdAmtA(Long value) {
		this.crdAmtA = value;
	}

	/** 
	 * 取得無擔保授信總額度(海外)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	public Long getCrdAmtB() {
		return this.crdAmtB;
	}
	/**
	 *  設定無擔保授信總額度(海外)<p/>
	 *  100/09/28調整<br/>
	 *  單位：TWD仟元
	 **/
	public void setCrdAmtB(Long value) {
		this.crdAmtB = value;
	}

	/** 取得無擔保授信總額度占本行淨值 **/
	public Double getCrdMega() {
		return this.crdMega;
	}
	/** 設定無擔保授信總額度占本行淨值 **/
	public void setCrdMega(Double value) {
		this.crdMega = value;
	}

	/** 
	 * 取得授信總餘額<p/>
	 * 單位：TWD仟元<br/>
	 *  (lntAmtA+lntAmtB)
	 */
	public Long getLntAmt() {
		return this.lntAmt;
	}
	/**
	 *  設定授信總餘額<p/>
	 *  單位：TWD仟元<br/>
	 *  (lntAmtA+lntAmtB)
	 **/
	public void setLntAmt(Long value) {
		this.lntAmt = value;
	}

	/** 
	 * 取得授信總餘額(國內)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	public Long getLntAmtA() {
		return this.lntAmtA;
	}
	/**
	 *  設定授信總餘額(國內)<p/>
	 *  100/09/28調整<br/>
	 *  單位：TWD仟元
	 **/
	public void setLntAmtA(Long value) {
		this.lntAmtA = value;
	}

	/** 
	 * 取得授信總餘額(海外)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	public Long getLntAmtB() {
		return this.lntAmtB;
	}
	/**
	 *  設定授信總餘額(海外)<p/>
	 *  100/09/28調整<br/>
	 *  單位：TWD仟元
	 **/
	public void setLntAmtB(Long value) {
		this.lntAmtB = value;
	}

	/** 取得授信總餘額占本行淨值 **/
	public Double getLntMega() {
		return this.lntMega;
	}
	/** 設定授信總餘額占本行淨值 **/
	public void setLntMega(Double value) {
		this.lntMega = value;
	}

	/** 
	 * 取得無擔保授信總餘額<p/>
	 * 單位：TWD仟元<br/>
	 *  (lncAmtA+lncAmtB)
	 */
	public Long getLncAmt() {
		return this.lncAmt;
	}
	/**
	 *  設定無擔保授信總餘額<p/>
	 *  單位：TWD仟元<br/>
	 *  (lncAmtA+lncAmtB)
	 **/
	public void setLncAmt(Long value) {
		this.lncAmt = value;
	}

	/** 
	 * 取得無擔保授信總餘額(國內)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	public Long getLncAmtA() {
		return this.lncAmtA;
	}
	/**
	 *  設定無擔保授信總餘額(國內)<p/>
	 *  100/09/28調整<br/>
	 *  單位：TWD仟元
	 **/
	public void setLncAmtA(Long value) {
		this.lncAmtA = value;
	}

	/** 
	 * 取得無擔保授信總餘額(海外)<p/>
	 * 100/09/28調整<br/>
	 *  單位：TWD仟元
	 */
	public Long getLncAmtB() {
		return this.lncAmtB;
	}
	/**
	 *  設定無擔保授信總餘額(海外)<p/>
	 *  100/09/28調整<br/>
	 *  單位：TWD仟元
	 **/
	public void setLncAmtB(Long value) {
		this.lncAmtB = value;
	}

	/** 取得無擔保授信總餘額占本行淨值 **/
	public Double getLncMega() {
		return this.lncMega;
	}
	/** 設定無擔保授信總餘額占本行淨值 **/
	public void setLncMega(Double value) {
		this.lncMega = value;
	}

	/** 
	 * 取得不計入同一關係企業第一～四項者之合計額度<p/>
	 * 單位：TWD仟元<br/>
	 *  (若扣除可不計入同一關係企業第一～四項者之合計額度為TWD999,999,999仟元，占本行淨值的999.99%，合計餘額為TWD999,999,999仟元，占本行淨值的999.99%。)
	 */
	public Long getExcAmt() {
		return this.excAmt;
	}
	/**
	 *  設定不計入同一關係企業第一～四項者之合計額度<p/>
	 *  單位：TWD仟元<br/>
	 *  (若扣除可不計入同一關係企業第一～四項者之合計額度為TWD999,999,999仟元，占本行淨值的999.99%，合計餘額為TWD999,999,999仟元，占本行淨值的999.99%。)
	 **/
	public void setExcAmt(Long value) {
		this.excAmt = value;
	}

	/** 取得不計入同一關係企業第一～四項者之合計額度占本行淨值 **/
	public Double getExcMega() {
		return this.excMega;
	}
	/** 設定不計入同一關係企業第一～四項者之合計額度占本行淨值 **/
	public void setExcMega(Double value) {
		this.excMega = value;
	}

	/** 
	 * 取得合計餘額<p/>
	 * 單位：TWD仟元
	 */
	public Long getSumAmt() {
		return this.sumAmt;
	}
	/**
	 *  設定合計餘額<p/>
	 *  單位：TWD仟元
	 **/
	public void setSumAmt(Long value) {
		this.sumAmt = value;
	}

	/** 取得占本行淨值 **/
	public Double getSumMega() {
		return this.sumMega;
	}
	/** 設定占本行淨值 **/
	public void setSumMega(Double value) {
		this.sumMega = value;
	}

	/** 
	 * 取得本行轉投資等非授信業務金額<p/>
	 * 單位：TWD仟元
	 */
	public Long getOthAmt() {
		return this.othAmt;
	}
	/**
	 *  設定本行轉投資等非授信業務金額<p/>
	 *  單位：TWD仟元
	 **/
	public void setOthAmt(Long value) {
		this.othAmt = value;
	}

	/** 取得非授信業務占本行淨值 **/
	public Double getOthMega() {
		return this.othMega;
	}
	/** 設定非授信業務占本行淨值 **/
	public void setOthMega(Double value) {
		this.othMega = value;
	}

	/** 
	 * 取得金融商品金額<p/>
	 * 單位：TWD仟元
	 */
	public Long getFinAmt() {
		return this.finAmt;
	}
	/**
	 *  設定金融商品金額<p/>
	 *  單位：TWD仟元
	 **/
	public void setFinAmt(Long value) {
		this.finAmt = value;
	}

	/** 取得金融商品金額占本行淨值 **/
	public Double getFinMega() {
		return this.finMega;
	}
	/** 設定金融商品金額占本行淨值 **/
	public void setFinMega(Double value) {
		this.finMega = value;
	}

	/** 
	 * 取得曝險總額<p/>
	 * 單位：TWD仟元
	 */
	public Long getRskAmt() {
		return this.rskAmt;
	}
	/**
	 *  設定曝險總額<p/>
	 *  單位：TWD仟元
	 **/
	public void setRskAmt(Long value) {
		this.rskAmt = value;
	}

	/** 取得曝險總額占本行淨值 **/
	public Double getRskMega() {
		return this.rskMega;
	}
	/** 設定曝險總額占本行淨值 **/
	public void setRskMega(Double value) {
		this.rskMega = value;
	}

	/** 
	 * 取得關係企業在全體金融機構授信總餘額 未 逾其淨值或營收<p/>
	 * Y/N（已/未）
	 */
	public String getRltOver() {
		return this.rltOver;
	}
	/**
	 *  設定關係企業在全體金融機構授信總餘額 未 逾其淨值或營收<p/>
	 *  Y/N（已/未）
	 **/
	public void setRltOver(String value) {
		this.rltOver = value;
	}

	/** 
	 * 取得輸入資料檢誤完成(Y/N)<p/>
	 * 100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}
	/**
	 *  設定輸入資料檢誤完成(Y/N)<p/>
	 *  100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
