/* 
 * L170M01F.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 覆審意見檔 **/
@NamedEntityGraph(name = "L170M01F-entity-graph", attributeNodes = { @NamedAttributeNode("l170m01a")})
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L170M01F", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class L170M01F extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;


	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", insertable = false, updatable = false) })
	private L170M01A l170m01a;
	
	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 覆審意見_一、為確保本行債權，有無必要辦理保全措施
	 * <br/>Y/N 
	 */
	@Column(name = "RETIALCOMM", length = 1, columnDefinition = "VARCHAR(1)")
	private String retialComm;

	/**
	 * 覆審意見_二、覆審情形
	 * <p/>
	 * 1.覆審正常<br/>
	 * 2.異常情形，應改善或注意事項
	 */
	@Column(name = "CONFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String conFlag;

	/**
	 * 覆審意見_異常情形，應改善或注意事項
	 * <p/>
	 * 128個全型字
	 */
	@Column(name = "CONDITION", length = 1500, columnDefinition = "VARCHAR(1500)")
	private String condition;

	/**
	 * 覆審意見_四、編製完成日期
	 * <p/>
	 * 107/07調整<br/>
	 * DATE ( TIMESTAMP
	 */
	@Column(name = "UPDATE", columnDefinition = "TIMESTAMP")
	private Timestamp upDate;

	/**
	 * 受檢單位洽辦情形
	 * <p/>
	 * 128個全型字
	 */
	@Column(name = "BRANCHCOMM", length = 2400, columnDefinition = "VARCHAR(2400)")
	private String branchComm;

	/**
	 * 正/副營運長／ 經副襄理
	 * <p/>
	 * 100/11/10刪除<br/>
	 * 移至L170M01G
	 */
	@Column(name = "MANAGERID", length = 6, columnDefinition = "CHAR(6)")
	private String managerId;

	/**
	 * 覆審主管
	 * <p/>
	 * 100/11/10刪除<br/>
	 * 移至L170M01G
	 */
	@Column(name = "BOSSID", length = 6, columnDefinition = "CHAR(6)")
	private String bossId;

	/**
	 * 覆審人員
	 * <p/>
	 * 100/11/10刪除<br/>
	 * 移至L170M01G
	 */
	@Column(name = "APPRID", length = 6, columnDefinition = "CHAR(6)")
	private String apprId;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 覆審意見_三、目前評等
	 * <p/>
	 * 107/07增加<br/>
	 */
	@Column(name = "CURRATE", length = 3, columnDefinition = "CHAR(3)")
	private String curRate;
	
	/**
	 * 覆審意見_三、建議評等
	 * <p/>
	 * 107/07增加<br/>
	 */
	@Column(name = "SUGRATE", length = 3, columnDefinition = "CHAR(3)")
	private String sugRate;
	
	public L170M01A getL170m01a() {
		return l170m01a;
	}
	
	public void setL170m01A(L170M01A l170m01a) {
		this.l170m01a = l170m01a;
	}

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得覆審意見_一、為確保本行債權，有無必要辦理保全措施
	 * <br/>Y/N
	 */
	public String getRetialComm() {
		return this.retialComm;
	}

	/** 設定覆審意見_一、為確保本行債權，有無必要辦理保全措施
	 * <br/>Y/N
	 */
	public void setRetialComm(String value) {
		this.retialComm = value;
	}

	/**
	 * 取得覆審意見_二、覆審情形
	 * <p/>
	 * 1.覆審正常<br/>
	 * 2.異常情形，應改善或注意事項
	 */
	public String getConFlag() {
		return this.conFlag;
	}

	/**
	 * 設定覆審意見_二、覆審情形
	 * <p/>
	 * 1.覆審正常<br/>
	 * 2.異常情形，應改善或注意事項
	 **/
	public void setConFlag(String value) {
		this.conFlag = value;
	}

	/**
	 * 取得覆審意見_異常情形，應改善或注意事項
	 * <p/>
	 * 128個全型字
	 */
	public String getCondition() {
		return this.condition;
	}

	/**
	 * 設定覆審意見_異常情形，應改善或注意事項
	 * <p/>
	 * 128個全型字
	 **/
	public void setCondition(String value) {
		this.condition = value;
	}

	/**
	 * 取得覆審意見_三、編製完成日期
	 * <p/>
	 * 100/09/23調整<br/>
	 * DATE ( TIMESTAMP
	 */
	public Timestamp getUpDate() {
		return this.upDate;
	}

	/**
	 * 設定覆審意見_三、編製完成日期
	 * <p/>
	 * 100/09/23調整<br/>
	 * DATE ( TIMESTAMP
	 **/
	public void setUpDate(Timestamp value) {
		this.upDate = value;
	}

	/**
	 * 取得受檢單位洽辦情形
	 * <p/>
	 * 128個全型字
	 */
	public String getBranchComm() {
		return this.branchComm;
	}

	/**
	 * 設定受檢單位洽辦情形
	 * <p/>
	 * 128個全型字
	 **/
	public void setBranchComm(String value) {
		this.branchComm = value;
	}

	/**
	 * 取得正/副營運長／ 經副襄理
	 * <p/>
	 * 100/11/10刪除<br/>
	 * 移至L170M01G
	 */
	public String getManagerId() {
		return this.managerId;
	}

	/**
	 * 設定正/副營運長／ 經副襄理
	 * <p/>
	 * 100/11/10刪除<br/>
	 * 移至L170M01G
	 **/
	public void setManagerId(String value) {
		this.managerId = value;
	}

	/**
	 * 取得覆審主管
	 * <p/>
	 * 100/11/10刪除<br/>
	 * 移至L170M01G
	 */
	public String getBossId() {
		return this.bossId;
	}

	/**
	 * 設定覆審主管
	 * <p/>
	 * 100/11/10刪除<br/>
	 * 移至L170M01G
	 **/
	public void setBossId(String value) {
		this.bossId = value;
	}

	/**
	 * 取得覆審人員
	 * <p/>
	 * 100/11/10刪除<br/>
	 * 移至L170M01G
	 */
	public String getApprId() {
		return this.apprId;
	}

	/**
	 * 設定覆審人員
	 * <p/>
	 * 100/11/10刪除<br/>
	 * 移至L170M01G
	 **/
	public void setApprId(String value) {
		this.apprId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	
	/**
	 * 覆審意見_三、目前評等
	 * <p/>
	 * 107/07增加<br/>
	 */
	public String getCurRate() {
		return this.curRate;
	}

	/**
	 * 覆審意見_三、目前評等
	 * <p/>
	 * 107/07增加<br/>
	 */
	public void setCurRate(String value) {
		this.curRate = value;
	}
	
	/**
	 * 覆審意見_三、建議評等
	 * <p/>
	 * 107/07增加<br/>
	 */
	public String getSugRate() {
		return this.sugRate;
	}

	/**
	 * 覆審意見_三、建議評等
	 * <p/>
	 * 107/07增加<br/>
	 */
	public void setSugRate(String value) {
		this.sugRate = value;
	}

}
