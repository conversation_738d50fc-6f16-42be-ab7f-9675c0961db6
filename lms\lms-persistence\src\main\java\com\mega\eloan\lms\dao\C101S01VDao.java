/* 
 * L120S15ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S01V;

/** 申請資料核對表檔 **/
public interface C101S01VDao extends IGenericDao<C101S01V> {

	C101S01V findByOid(String oid);
	
	List<C101S01V> findByMainId(String mainId);
	public List<C101S01V> findByMainIdandItemsName(String mainId,String CustID,String Dupno,String item_Name);
	public List<C101S01V> findByMainIdandCustID(String mainId,String CustID,String Dupno);
	public List<C101S01V> findByUniqueKey(String mainId,String ownBrId,String custID,String dupNo);

	public int deleteByOid(String oid);
}