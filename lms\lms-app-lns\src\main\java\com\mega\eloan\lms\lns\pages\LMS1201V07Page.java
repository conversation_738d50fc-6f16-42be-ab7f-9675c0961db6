/* 
 * LMS1201V07Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import tw.com.jcs.auth.AuthType;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.GridViewFilterPanel01;


/**<pre>
 * 授信簽報書呈授管處/營運中心
 * </pre>
 * @since  2011/11/9
 * <AUTHOR> @version <ul>
 *           <li>2011/11/9,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1201v07")
public class LMS1201V07Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		//設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_陳復案_陳述案);
		if (this.getAuth(AuthType.Accept)) {
			// 主管權限時要顯示的按鈕...
			addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Filter);
		} else {
			// 否則需要顯示的按鈕
			// 加上Button
			addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Filter);
		}
		//套用哪個i18N檔案
		renderJsI18N(LMS1201V01Page.class);
		
		model.addAttribute("loadScript", "loadScript('pagejs/lns/LMS1201V01Page');");
		setupIPanel(new GridViewFilterPanel01(PANEL_ID), model, params);
	}// ;

}
