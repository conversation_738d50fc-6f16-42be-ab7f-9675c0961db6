/**
 * 約據書 綜合授信契約書
 * @since
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
var initDfd = $.Deferred();
var inits = {
    fhandle: "lms9990m01formhandler",
    queryAction: "queryL999m01aM02",
    saveAction: "saveL999m01aM02",
    contractType: responseJSON.contractType //"01"//約據書種類
};
var LMS999Action = {
    ActionSFormId: "#ActionSForm",
    ActionMFormId: "#ActionMForm",
    save: function(showMsg, tofn){
        var $ActionSForm = $(LMS999Action.ActionSFormId);
        var $ActionMForm = $(LMS999Action.ActionMFormId);
        if (LMS999Action.validForm()) {
            $.ajax({
                async: false,
                handler: inits.fhandle,
                action: inits.saveAction,
                data: {
                    showMsg: showMsg,
                    contractType: inits.contractType,//約據書種類
                    ActionMForm: JSON.stringify($ActionMForm.serializeData()),
                    ActionSForm: JSON.stringify($ActionSForm.serializeData())
                },
            }).done(function(responseData){
	//                    $ActionMForm.injectData(responseData);
   //                    $ActionSForm.injectData(responseData);
               if (tofn) {
                   tofn();
               }
			});
            
        }
    },
    /**執行列印*/
    printAction: function(){
    
        var fileDName = "";
        switch (responseJSON.contractType +"") {
            case "01":
                fileDName = "LMS9990W01.doc";
                break;
            case "08":
                fileDName = "LMS9990W01V.doc";
                break;
            case "09":
                fileDName = "LMS9990W01HV.doc";
                break;
            default:
                break;
        }
        
        $.capFileDownload({
            handler: "lmsdownloadformhandler",
            data: {
                mainId: responseJSON.mainId,
                contractType: responseJSON.contractType,
                fileDownloadName: fileDName, //"LMS9990W01.doc",
                serviceName: "lms9990doc01service"
            }
        });
    },
    /**驗證readOnly狀態*/
    checkReadonly: function(){
        var auth = (responseJSON ? responseJSON.Auth : {}); //權限
        if (auth.readOnly) {
            return true;
        }
        return false;
    },
    /**驗證表單是否正確*/
    validForm: function(){
        if (responseJSON.page == "01") {
            var start = $("#usedSDate").val();
            var end = $("#usedEDate").val();
            if (start > end) {
                //L999M01AS03.title03=動用期間
                //EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
                CommonAPI.showErrorMessage(i18n.msg('EFD3026').replace(/\$\\{colName\\}/, i18n.lms9990m02['L999M01AS03.title03'] + " "));
                return false;
            }
        }
        return $(LMS999Action.ActionSFormId).valid() && $(LMS999Action.ActionMFormId).valid();
    }
};

/**
 *tempSave
 */
$.extend(window.tempSave, {
    handler: inits.fhandle,
    action: "tempsaveL999m01aM02",
    beforeCheck: function(){
        return LMS999Action.validForm();
    },
    sendData: function(){
        var $ActionSForm = $(LMS999Action.ActionSFormId);
        var $ActionMForm = $(LMS999Action.ActionMFormId);
        return {
            contractType: inits.contractType,//約據書種類
            ActionMForm: JSON.stringify($ActionMForm.serializeData()),
            ActionSForm: JSON.stringify($ActionSForm.serializeData())
        }
    }
});

$(function() {
    $.form.init({
        formHandler: inits.fhandle,
        formPostData: {
            formAction: inits.queryAction,
            srcMainId: responseJSON.srcMainId
        },
        loadSuccess: function(json){
            initDfd.resolve(json);
        }
    });
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(){
        LMS999Action.save(true);
    }).end().find("#btnPrint").click(function(){
        if (LMS999Action.checkReadonly()) {
            LMS999Action.printAction();
        }
        else {
        
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    LMS999Action.save(false, LMS999Action.printAction);
                }
            });
        }
    });
});



