/* 
 * LMS1601S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 購置房屋擔保放款風險權數檢核表  - 文件資訊
 * </pre>
 * 
 * @since 2012/12/28
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/28,<PERSON><PERSON><PERSON>,new
 *          </ul>
 */
public class LMS9131S01Panel extends Panel {

	public LMS9131S01Panel(String id) {
		super(id);
	}
	/**/
	private static final long serialVersionUID = 1L;
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		new DocLogPanel("_docLog").processPanelData(model, params);
	}

}
