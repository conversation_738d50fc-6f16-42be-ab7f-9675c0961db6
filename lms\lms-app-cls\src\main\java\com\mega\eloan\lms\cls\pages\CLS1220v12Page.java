/* 
 * CLS1161V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;

/**
 * <pre>
 * 中鋼消貸資料下載
 * </pre>
 * 
 * @since 2021/10/19
 * <AUTHOR> @version <ul>
 *          <li>2021/10/19,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1220v12")
public class CLS1220v12Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.Filter,
				LmsButtonEnum.CreateExcel);
		// build i18n
		renderJsI18N(CLS1220v12Page.class);
		// add panel
		// add(new CLS1131S01Panel("CLS1131S01"));

		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1220v12Page');");
	}// ;

}
