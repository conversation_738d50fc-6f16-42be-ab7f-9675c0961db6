/* 
 * LMS9990M07Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.ui.ModelMap;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.ctr.panels.LMS9990S17Panel;
import com.mega.eloan.lms.ctr.panels.LMS9990S25Panel;
import com.mega.eloan.lms.ctr.panels.LMS9990S26Panel;
import com.mega.eloan.lms.ctr.panels.LMS9990S27Panel;
import com.mega.eloan.lms.ctr.panels.LMS9990S28Panel;
import com.mega.eloan.lms.ctr.panels.LMS9990S30APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S31APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S32APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S33APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S34Panel;
import com.mega.eloan.lms.ctr.panels.LMS9990S35Panel;
import com.mega.eloan.lms.ctr.panels.LMS9990S36Panel;
import com.mega.eloan.lms.model.C999M01A;

/**<pre>
 * // * 個金約據書-子頁(政策性留學貸款)
 * </pre>
 * @since  2012/7/24
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/7/24,Miller,new
 *          </ul>
 */

@Controller
@RequestMapping("/ctr/lms9990m08/{page}")
public class LMS9990M08Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS9990M08Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		// 依權限設定button

		renderJsI18N(LMS9990M07Page.class);
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page);
		model.addAttribute("tabID", tabID);
		panel.processPanelData(model, params);
	}// ;

	// 頁籤
	public Panel getPanel(int index) {
		Panel panel = null;
		switch (index) {
		case 1:
			// 產品種類
			panel = new LMS9990S17Panel(TAB_CTX, true);
			break;
//		case 8:
//			// 保證條款
//			panel = new LMS9990S24Panel(TAB_CTX);
//			break;
		case 9:
			// 申請方式及借款期限、寬限期
			panel = new LMS9990S25Panel(TAB_CTX, true);
			break;
		case 10:
			// 契約金額(教育部補助留學生借款契約書)
			panel = new LMS9990S26Panel(TAB_CTX, true);
			break;
		case 11:
			// 借款用途(教育部補助留學生借款契約書)
			panel = new LMS9990S27Panel(TAB_CTX, true);
			break;
		case 12:
			// 動用方式(教育部補助留學生借款契約書)
			panel = new LMS9990S28Panel(TAB_CTX, true);
			break;
		case 14:
			// 違約金及遲延利息(教育部補助留學生借款契約書)
			panel = new LMS9990S30APanel(TAB_CTX, true);
			break;
		case 15:
			// 資料提供
			panel = new LMS9990S31APanel(TAB_CTX, true);
			break;
		case 16:
			// 服務
			panel = new LMS9990S32APanel(TAB_CTX, true);
			break;
		case 17:
			// 管轄法院
			panel = new LMS9990S33APanel(TAB_CTX, true);
			break;
		case 18:
			// 撥款方式(教育部補助留學生借款契約書)
			panel = new LMS9990S34Panel(TAB_CTX, true);
			break;
		case 19:
			// 償還辦法(教育部補助留學生借款契約書)
			panel = new LMS9990S35Panel(TAB_CTX, true);
			break;
		case 20:
			// 利息計付(教育部補助留學生借款契約書)
			panel = new LMS9990S36Panel(TAB_CTX, true);
			break;			
		default:
			// 預設頁面
			panel = new LMS9990S17Panel(TAB_CTX, true);
			break;
		}
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C999M01A.class;

	}

}
