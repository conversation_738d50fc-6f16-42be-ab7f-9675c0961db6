<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
    	<wicket:panel>
	        <!--Start : 篩選條件-->
			<div id="query_dialog" class="popup_cont">
				<form id="queryForm">
				<fieldset>
				<legend>
					<wicket:message key="pop.01">請輸入欲查詢之條件</wicket:message>
				</legend>
				<table width="100%" class="tb2" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td width="35%" class="hd2 rt">
							<b><span id="subTitle"></span></b>
						</td>
						<td>
							<input type="text" id="dateS" name="dateS" size="12" class="date"/>
							<b>~</b>
							<input type="text" id="dateE" name="dateE" size="12" class="date"/>
						</td>
					</tr>
				</table>
				</fieldset>
				</form>
			</div>
		<!--End : 篩選條件-->
		<script type="text/javascript">
                var openFilter;
				$(document).ready(function(){
				    openFilter = function(title, grid, sendTitle){
						var $this = $("#query_dialog");
						if(sendTitle){
							$this.find("#subTitle").html('<wicket:message key="pop.02">資料產生日期</wicket:message>');
						}else{
							$this.find("#subTitle").html('<wicket:message key="pop.03">執行日期</wicket:message>');
						}
				    	$this.thickbox({
							title: title,
					        modal: false, height: 220, width:  520,
							valign: 'bottom', align: 'center',
							open:function(){$(this).find("#dateS,#dateE").val(CommonAPI.getToday())},
					        buttons: API.createJSON([{
					            key: i18n.def.sure,
					            value: function(){
				            		grid.jqGrid('setGridParam', {
					                    postData: this.find("#queryForm").serializeData()
					                });
					            	grid.trigger("reloadGrid",[{page:1}]); $.thickbox.close();
					            }
					        }, {
					            key: i18n.def.cancel,
					            value: function(){ $.thickbox.close(); }
					        }])	
					    });
				    };
				});
            </script>
		</wicket:panel>
	</body>
</html>
