page.title=LMS7820M01 Special Case Listing
#==================================================
# L782M01A\uff0e\u984d\u5ea6\u7279\u6b8a\u6848\u4ef6\u767b\u8a18\u8868  LMS7820V00Page Grid
L782M01A.dispatchDate=Correspondence Date
L782M01A.loanTP=Credit Line
L782M01A.inteRate=Interest (Fee) Rate/Others
L782M01A.custName=Customer Name
# \u984d\u5ea6\u7279\u6b8a\u6848\u4ef6\u767b\u8a18\u8868
L782M01A.title=Special Case Listing
L782M01A.loanTP=Credit Line
L782M01A.dispatchDate=Correspondence Date
L782M01A.applyCurr=Currency
L782M01A.applyAmt=Balance
L782M01A.caseType=Category
L782M01A.inteRate=Interest (Fee) Rate/Others
L782M01A.disp1=Remarks
L782M01A.printMess=This report lists out all special cases whose [Remark] field contain data (those with blank remarks \n are not listed); are you sure to proceed with printing?

L782M01A.title01=Please enter the Filtering criteria
L782M01A.releaseDateS=Correspondence Date-Start
L782M01A.releaseDateE=Correspondence Date-End
L782M01A.error01=Start Date can not be greater than the end date