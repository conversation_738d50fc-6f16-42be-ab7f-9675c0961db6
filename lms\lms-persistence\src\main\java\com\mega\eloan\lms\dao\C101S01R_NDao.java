package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S01R_N;

/** 個金卡友貸信用評等表 **/
public interface C101S01R_NDao extends IGenericDao<C101S01R_N> {

	C101S01R_N findByOid(String oid);
	
	List<C101S01R_N> findByMainId(String mainId);
	
	C101S01R_N findByUniqueKey(String mainId, String ownBrId, String custId, String dupNo);
	
	List<C101S01R_N> findByIndex01(String mainId, String ownBrId, String custId,
			String dupNo);

	List<C101S01R_N> findByCustIdDupId(String custId,String dupNo);
	
	int deleteByOid(String oid);
}