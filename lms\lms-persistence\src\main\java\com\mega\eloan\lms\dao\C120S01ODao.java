/* 
 * C120S01ODao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01O;

/** 個金相關查詢關聯戶貨款明細 **/
public interface C120S01ODao extends IGenericDao<C120S01O> {

	C120S01O findByOid(String oid);

	List<C120S01O> findByMainId(String mainId);

	C120S01O findByUniqueKey(String mainId, String custId, String dupNo);

	List<C120S01O> findByIndex01(String mainId, String custId, String dupNo);

	int deleteByOid(String oid);
}