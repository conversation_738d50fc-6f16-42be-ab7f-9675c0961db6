/*_
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */

package tw.com.iisi.cap.handler;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;

import org.kordamp.json.JSONArray;
import org.springframework.util.ReflectionUtils;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.action.IAction;
import tw.com.iisi.cap.dao.utils.AbstractSearchSetting;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.enums.IGridEnum;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.IGridResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * 直接以method name來執行(formAction傳入method name)
 * 若未指定method時，預設執行doWork()
 * </pre>
 * 
 * @since 2011/6/20
 * <AUTHOR>
 * @version $Id$
 * @version
 *          <ul>
 *          <li>2011/6/20,RodesChen,new
 *          <li>2011/7/7,Rodeschen,修正前端無上傳order 錯誤
 *          <li>2011/9/29,IrisTu,增加預設可以多組sort的欄位,js設定時以「|」分隔,如sortname: 'column1|column2',sortorder:'asc|asc'.
 *          <li>2011/10/27,IrisTu,改使用IGridResult
 *          </ul>
 */
public abstract class MGridHandler extends FormHandler {

    /**
     * <pre>
     * 直接以method name來執行
     * </pre>
     * 
     * @param formAction
     *            action
     * @return IAction
     */
    @Override
    public IAction getAction(String formAction) {
        return new MethodExecuteAction(this);
    }

    /**
     * <pre>
     * MethodExecuteAction
     * </pre>
     */
    private class MethodExecuteAction implements IAction {

        /**
         * 要執行動作的Handler
         */
        MGridHandler executeHandler;

        /**
         * 建構子
         * 
         * @param executeObj
         */
        public MethodExecuteAction(MGridHandler executeObj) {
            this.executeHandler = executeObj;
        }

        /*
         * 以傳入資料帶有的資訊調用對應方法
         * 
         * @see tw.com.iisi.cap.action.IAction#doWork(com.iisigroup.cap.component.PageParameters)
         */
        @SuppressWarnings({ "rawtypes" })
        @Override
        public IResult doWork(PageParameters params) throws CapException {
            IResult rtn = null;
            String methodId = params.getString(FORM_ACTION);
            if (CapString.isEmpty(methodId)) {
                methodId = "doWork";
            }
            Class[] paramTypes = { ISearch.class, PageParameters.class };
            Method method = ReflectionUtils.findMethod(executeHandler.getClass(), methodId, paramTypes);
            if (method != null) {
                rtn = execute(method, params);
            }

            return rtn;
        }

    }// ;

    /**
     * 以傳入的資料設定Grid
     * 
     * @param method
     *            方法
     * @param params
     *            前端資料
     * @return IGridResult result
     * @throws CapException
     */
    @SuppressWarnings({ "rawtypes" })
    private IResult execute(Method method, PageParameters params) throws CapException {
        ISearch search = createSearchTemplete();
        setParameters(params);
        beforeExcute(params);
        boolean pages = params.containsKey(IGridEnum.PAGE.getCode());
        int page = 0, pageRows = 0, startRow = 0;
        if (pages) {
            // page = params.getAsInteger(IGridEnum.PAGE.getCode());
            // 頁碼增加預設值,排除頁碼輸入非數字 add by Fantasy 2013/01/23
            page = params.getAsInteger(IGridEnum.PAGE.getCode(), 1);
            pageRows = params.getAsInteger(IGridEnum.PAGEROWS.getCode());
            startRow = (page - 1) * pageRows;
            search.setFirstResult(startRow).setMaxResults(pageRows);
        }
        boolean sort = params.containsKey(IGridEnum.SORTCOLUMN.getCode()) && !CapString.isEmpty(params.getString(IGridEnum.SORTCOLUMN.getCode()));
        if (sort) {
            String[] sortBy = params.getString(IGridEnum.SORTCOLUMN.getCode()).split(",");
            String[] isAscAry = params.getString(IGridEnum.SORTTYPE.getCode(), "asc").split(",");
            for (int i = 0; i < sortBy.length; i++) {
                String isAsc = (i < isAscAry.length) ? isAscAry[i] : "asc";
                search.addOrderBy(sortBy[i], !IGridEnum.SORTASC.getCode().equals(isAsc));
            }
        }
        IGridResult result = null;
        try {
            result = (IGridResult) method.invoke(this, search, params);

            result.setColumns(getColumns(params.getString(IGridEnum.COL_PARAM.getCode())));
            result.setPage(page);
            result.setPageCount(result.getRecords(), pageRows);

        } catch (InvocationTargetException e) {
            if (e.getCause() instanceof CapMessageException) {
                throw (CapMessageException) e.getCause();
            } else if (e.getCause() instanceof CapException) {
                throw (CapException) e.getCause();
            } else {
                throw new CapException(e.getCause(), this.getClass());
            }
        } catch (Throwable t) {
            throw new CapException(t, this.getClass());
        }

        return result;
    }

    /**
     * <pre>
     * 若未傳送formAction值，則default執行此method
     * </pre>
     * 
     * @param params
     *            RequestParameters
     * @param parent
     *            Component
     * @return IResult
     */
    public IResult doWork(PageParameters params) throws CapException {
        return null;
    }

    /**
     * 取得iGrid中的Column Name
     * 
     * @param params
     *            String
     * @return String string[]
     */
    @SuppressWarnings("unchecked")
    public String[] getColumns(String params) throws CapException {
        JSONArray arr = JSONArray.fromObject(params);
        String[] colNames = new String[arr.size()];
        for (int i = 0; i < arr.size(); i++) {
            Map<String, String> m = (Map<String, String>) arr.get(i);
            colNames[i] = m.containsKey(IGridEnum.COL_INDEX.getCode()) ? m.get(IGridEnum.COL_NAME.getCode()) + "|" + m.get(IGridEnum.COL_INDEX.getCode()) : m.get(IGridEnum.COL_NAME.getCode());
        }
        return colNames;
    };

    /**
     * before execute
     * 
     * @param params
     *            RequestParameters
     * @param parent
     *            Component
     */
    public void beforeExcute(PageParameters params) throws CapException {
        // customize
    }

    /**
     * 建立查詢模板
     * 
     * @return {@code new GridSearch()}
     */
    protected ISearch createSearchTemplete() {
        return new GridSearch();
    }

    /**
     * <pre>
     * GridSearch extends AbstractSearchSetting
     * </pre>
     */
    private class GridSearch extends AbstractSearchSetting {

        private static final long serialVersionUID = 1L;

    }

}
