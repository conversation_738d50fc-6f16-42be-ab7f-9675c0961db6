package com.mega.eloan.lms.lms.report.impl;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.pages.LMS1505M01Page;
import com.mega.eloan.lms.lms.service.LMS1505Service;
import com.mega.eloan.lms.model.L150M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 產生小放會PDF
 * 
 * <AUTHOR>
 * 
 */
@Service("lms1505r01rptservice")
public class LMS1505R01RptServiceImpl extends AbstractReportService {

	@Resource
	LMS1505Service service1505;

	@Resource
	BranchService branch;

	@Resource
	UserInfoService userInfo;

	@Resource
	LMSService lmsService;

	@Resource
	CodeTypeService codetypeservice;

	@Override
	public String getReportTemplateFileName() {
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/lms/LMS1505R01_" + locale.toString() + ".rpt";
		// return "D:/test_Mega_Report/LMS1505R01_zh_TW.rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 * org.apache.wicket.PageParameters)
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		// L160M01A．小放會主檔
		L150M01A l150m01a = null;
		String branchName = null;
		Locale locale = null;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		try {
			// zh_TW: 正體中文
			// zh_CN: 簡體中文
			// en_US: 英文
			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();

			l150m01a = service1505.findL150m01aByOid(mainOid);
			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(l150m01a.getOwnBrId())));

			// 分行名稱
			rptVariableMap.put("BRANCHNAME", branchName);
			String logoPath = lmsService.getLogoShowPath(
					UtilConstants.RPTPicType.兆豐LOGO, "00",
					l150m01a.getOwnBrId());
			rptVariableMap.put("LOGOSHOW", logoPath);
			rptVariableMap = this.setL150M01AData(rptVariableMap, l150m01a);
			// this.generator.setLang(java.util.Locale.TAIWAN);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			reportTools.setRowsData(titleRows);
			// new ReportGenerator().checkVariableExist("C:/test.txt",
			// rptVariableMap);
			// reportTools.setTestMethod(true);
		} finally {

		}
	}

	/**
	 * 塞入變數MAP資料使用(L120M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL150M01AData(
			Map<String, String> rptVariableMap, L150M01A l150m01a) {
		if (l150m01a == null) {
			l150m01a = new L150M01A();
		}
		rptVariableMap.put("L150M01A.RANDOMCODE",
				Util.nullToSpace(l150m01a.getRandomCode()));
		rptVariableMap.put("L150M01A.MEETINGDATE",
				Util.nullToSpace(TWNDate.toAD(l150m01a.getMeetingDate())));
		rptVariableMap.put("L150M01A.MEETINGTIME",
				Util.nullToSpace(l150m01a.getMeetingTime()));
		rptVariableMap.put("L150M01A.MEETINGPLACE",
				Util.nullToSpace(l150m01a.getMeetingPlace()));
		rptVariableMap
				.put("L150M01A.CHAIRMAN",
						Util.nullToSpace(this.getUserName(
								l150m01a.getChairManCN(),
								l150m01a.getChairMan())));
		rptVariableMap.put("L150M01A.ACCOUNTING", Util.nullToSpace(this
				.getUserName(l150m01a.getAccountingCN(),
						l150m01a.getAccounting())));
		rptVariableMap.put("L150M01A.PRESENT", l150m01a.getPresent());
		rptVariableMap.put("L150M01A.GIST",
				Util.nullToSpace(l150m01a.getGist()));
		rptVariableMap.put("L150M01A.DESCRIPTION",
				Util.nullToSpace(l150m01a.getDescription()));
		rptVariableMap.put("L150M01A.RESOLUTION", l150m01a.getResolution());

		rptVariableMap
				.put("L150M01A.RECORDER",
						Util.nullToSpace(this.getUserName(
								l150m01a.getRecorderCN(),
								l150m01a.getRecorder())));

		// J-112-0057_05097_B1001 Web e-Loan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
		// eloan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
		// 1.「帳戶管理員」改「覆核」
		// 2.「紀錄」改「經辦」
		// 3.取消「遵守法令主管」

		// J-112-0057_05097_B1002 Web e-Loan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
		// [下午 01:29] 金至忠(授信審查處,襄理)
		// 授審處小會會議記錄格式, 幫忙把法令遵循主管加回來, 要不要寫單子
		//
		// [下午 01:30] 黃建霖(資訊處,高級專員)
		// ........

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1505M01Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// J-112-0057_05097_B1003 Web e-Loan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
		rptVariableMap.put("L150M01A.UNITNO", user.getUnitNo());

		Map<String, String> unitMgrTitleMap = codetypeservice.findByCodeType(
				"l150m01a_unitMgr_title", LMSUtil.getLocale().toString());

		String unitMgrTitle = MapUtils.getString(unitMgrTitleMap,
				user.getUnitNo(), "處  長");

		if (user.getUnitNo().equals(UtilConstants.BankNo.授管處)) {
			rptVariableMap.put("L150M01a.accounting.title",
					prop.getProperty("L150M01a.approver")); // 覆核
			rptVariableMap.put("L150M01a.recorder.title",
					prop.getProperty("L150M01a.appraiser")); // 經辦
			rptVariableMap.put("L150M01a.lawsBoss.show", "Y"); // 遵守法令主管
			rptVariableMap.put("L150M01A.LAWSBOSS", Util.nullToSpace(this
					.getUserName(l150m01a.getLawsBossCN(),
							l150m01a.getLawsBoss())));
			rptVariableMap.put("L150M01a.unitMgr.title", unitMgrTitle); // 單位主管TITLE
		} else {
			rptVariableMap.put("L150M01a.accounting.title",
					prop.getProperty("L150M01a.accounting")); // 帳戶管理員
			rptVariableMap.put("L150M01a.recorder.title",
					prop.getProperty("L150M01a.recorder")); // 紀錄

			// J-112-0057_05097_B1002 Web e-Loan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
			// [下午 01:29] 金至忠(授信審查處,襄理)
			// 授審處小會會議記錄格式, 幫忙把法令遵循主管加回來
			rptVariableMap.put("L150M01a.lawsBoss.show", "Y"); // 遵守法令主管

			rptVariableMap.put("L150M01A.LAWSBOSS", Util.nullToSpace(this
					.getUserName(l150m01a.getLawsBossCN(),
							l150m01a.getLawsBoss())));
			rptVariableMap.put("L150M01a.unitMgr.title", unitMgrTitle); // 單位主管TITLE
		}
		return rptVariableMap;
	}

	private String getUserName(String userName, String id) {
		return !"".equals(userName) ? userName : Util.nullToSpace(userInfo
				.getUserName(Util.nullToSpace(id)));
	}
}
