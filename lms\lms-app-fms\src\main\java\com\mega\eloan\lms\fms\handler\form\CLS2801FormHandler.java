package com.mega.eloan.lms.fms.handler.form;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.fms.flow.CLS2801Flow;
import com.mega.eloan.lms.fms.pages.CLS2801M01Page;
import com.mega.eloan.lms.fms.service.CLS2801Service;
import com.mega.eloan.lms.mfaloan.bean.ELF508;
import com.mega.eloan.lms.mfaloan.service.MisELF508Service;
import com.mega.eloan.lms.model.L140MM2A;
import com.mega.eloan.lms.model.L140S02L;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;


@Scope("request")
@Controller("cls2801formhandler")
public class CLS2801FormHandler extends AbstractFormHandler {

	@Resource
	CLS2801Service service;

	@Resource
	CLSService clsService;
	
	@Resource
	ICustomerService iCustomerService;
	
	@Resource
	UserInfoService userInfoService;

	@Resource 
	FlowSimplifyService flowSimplifyService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	UserInfoService userService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	MisELF508Service misELF508Service;
	
	
	Properties prop = MessageBundleScriptCreator
		.getComponentResource(CLS2801M01Page.class);

	Properties prop_AbstractEloanPage = MessageBundleScriptCreator
	.getComponentResource(AbstractEloanPage.class);
	
	@DomainAuth(AuthType.Modify)
	public IResult deleteMark(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String KEY = "saveOkFlag";
		
		String list = params.getString("list");
		String docStatus = params.getString("docStatus");
		
		result.set(KEY, false);
		
		L140MM2A meta = null;
		if (Util.isNotEmpty(list)) {
			meta = service.findL140MM2A_oid(list);	
			
			if(meta!=null){
				
				List<DocOpener> docOpeners = docCheckService.findByMainId(meta.getMainId());
				for(DocOpener docOpener : docOpeners){
					if(OpenTypeCode.Writing.getCode().equals(docOpener.getOpenType())){
						HashMap<String, String> hm = new HashMap<String, String>();
						hm.put("userId", docOpener.getOpener());
						hm.put("userName",
								userInfoService.getUserName(docOpener.getOpener()));
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0009", hm), getClass());
					}
				}
				
				//判斷編制中刪除，其它 更新DeletedTime
				if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
					flowSimplifyService.flowCancel(meta.getOid());
					service.delete(meta);
					// 刪除文件異動記錄
					docLogService.deleteLog(meta.getOid());
				}
				result.set(KEY, true);
			}			
		}
		return defaultResult(params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify)
	public L140MM2A newL140MM2AData(PageParameters params)
			throws CapException {
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		
		String custName = Util.trim(params.getString("custName"));
	
		L140MM2A meta = new L140MM2A();

		meta.setMainId(IDGenerator.getUUID());
		meta.setSeq(0);
		meta.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		meta.setCustId(params.getString("custId"));
		meta.setDupNo(params.getString("dupNo"));
		meta.setCustName(custName);
		meta.setOwnBrId(user.getUnitNo());
		meta.setUnitType(user.getUnitType());		
		meta.setCreator(user.getUserId());
		meta.setCreateTime(nowTS);
		meta.setUpdater(user.getUserId());
		meta.setUpdateTime(nowTS);
		meta.setCntrNo(params.getString("cntrNo"));
		meta.setIsClosed(false);
		meta.setRandomCode(IDGenerator.getRandomCode());
		meta.setDocStatus(FlowDocStatusEnum.編製中.getCode());
		meta.setLoanCurr("TWD");
				
		return meta;
	}
	
	@DomainAuth(AuthType.Modify)
	public L140S02L newL140S02LData(PageParameters params,String MainId,int Seq)
			throws CapException {
	
		L140S02L l140s02l = new L140S02L();
		
		CapBeanUtil.map2Bean(params, l140s02l, new String[] {"mainId","seq","hold_no","owner_id",
				"app_date","house_adr","owner_nm","ownsp_id","ownsp_nm","spouse_id","spouse_nm",
				"coll_ln","coll_bn","coll_addr","set_hold"});
		
		l140s02l.setMainId(MainId);
		l140s02l.setSeq(Seq);
		
		return l140s02l;
	}

	private CapAjaxFormResult defaultResult(PageParameters params, L140MM2A meta,
			CapAjaxFormResult result) throws CapException {		
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
		return result;
	}
	
	/**
	 * 查詢L140S02L 
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		
		L140MM2A meta = null;
		L140S02L l140s02l = null;
		if (Util.isNotEmpty(mainOid)) {
			
			meta = service.findL140MM2A_oid(mainOid);
			l140s02l = service.findByUniqueKey(meta.getMainId(), meta.getSeq());
			
			LMSUtil.addMetaToResult(result, meta, new String[]{"oid","mainId","seq","hold_no","owner_id",
					"app_date","house_adr","owner_nm","ownsp_id","ownsp_nm","spouse_id","spouse_nm",
					"coll_ln","coll_bn","coll_addr","set_hold","cancel_date","eloan_date","aloan_date"});
			
			result.set("creator", Util.trim(userInfoService.getUserName(meta.getCreator())));
			result.set("createTime", Util.trim(meta.getCreateTime()));
			result.set("updater", Util.trim(userInfoService.getUserName(meta.getUpdater())));
			result.set("updateTime", Util.trim(meta.getUpdateTime()));
			result.set("ownBrIdDesc", meta.getOwnBrId() + branchService.getBranchName(meta.getOwnBrId()));
					
			String docStatus = meta.getDocStatus();			
			if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.010");
			}else if(Util.equals(FlowDocStatusEnum.待覆核.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.020");
			}else if(Util.equals(FlowDocStatusEnum.已核准.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.030");
			}
			result.set("docStatus", docStatus);	
			
			//put L140S02L to result
			result.set("hold_no", Util.trim(l140s02l.getHold_no()));
			result.set("owner_id", Util.trim(l140s02l.getOwner_id()));
			result.set("app_date", l140s02l.getApp_date());
			result.set("house_adr", Util.trim(l140s02l.getHouse_adr()));
			result.set("owner_nm", Util.trim(l140s02l.getOwner_nm()));
			result.set("ownsp_id", Util.trim(l140s02l.getOwnsp_id()));
			result.set("ownsp_nm", Util.trim(l140s02l.getOwnsp_nm()));
			result.set("spouse_id", Util.trim(l140s02l.getSpouse_id()));
			result.set("spouse_nm", Util.trim(l140s02l.getSpouse_nm()));
			result.set("coll_ln", Util.trim(l140s02l.getColl_ln()));
			result.set("coll_bn", Util.trim(l140s02l.getColl_bn()));
			result.set("coll_addr", Util.trim(l140s02l.getColl_addr()));
			result.set("set_hold", l140s02l.getSet_hold());
			
		}
		
		return defaultResult(params, meta, result);
	}
	
	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,String tempSave)
	throws CapException{
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);

		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L140MM2A meta = null;
		L140S02L l140s02l = null;
		if (Util.isNotEmpty(mainOid)) {
			//Update
			try{
				meta = service.findL140MM2A_oid(mainOid);
				l140s02l = service.findByUniqueKey(meta.getMainId(), meta.getSeq());
				
				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(CapDate.getCurrentTimestamp());
				meta.setRandomCode(IDGenerator.getRandomCode());
				meta.setDeletedTime(null);			

				if(!flowSimplifyService.flowExist(meta.getOid())){
					flowSimplifyService.flowStart(CLS2801Flow.FLOW_CODE, meta.getOid(), user.getUserId(), user.getUnitNo());	
				}
									
				CapBeanUtil.map2Bean(params, l140s02l, new String[] {"app_date","house_adr","owner_nm","ownsp_id","ownsp_nm","spouse_id","spouse_nm",
						"coll_ln","coll_bn","coll_addr","set_hold"});

				service.save(meta, l140s02l);
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}
			result.add(query(params));
		}else{
			//Insert
			try{
				meta = newL140MM2AData(params);
				service.save(meta);
				l140s02l = newL140S02LData(params,meta.getMainId(),meta.getSeq());
				service.save(l140s02l);
				
				mainOid = meta.getOid();
				flowSimplifyService.flowStart(CLS2801Flow.FLOW_CODE, meta.getOid(), user.getUserId(), user.getUnitNo());
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}
			
			//重新撈資料
			meta = service.findL140MM2A_oid(mainOid);	
			l140s02l = service.findByUniqueKey(meta.getMainId(), meta.getSeq());
			
			LMSUtil.addMetaToResult(result, meta, new String[]{"oid","mainId","seq","hold_no","owner_id",
					"app_date","house_adr","owner_nm","ownsp_id","ownsp_nm","spouse_id","spouse_nm",
					"coll_ln","coll_bn","coll_addr","set_hold","cancel_date","eloan_date","aloan_date"});
			
			result.set("creator", Util.trim(userInfoService.getUserName(meta.getCreator())));
			result.set("updater", Util.trim(userInfoService.getUserName(meta.getUpdater())));
			result.set("createTime", Util.trim(meta.getCreateTime()));
			result.set("updateTime", Util.trim(meta.getUpdateTime()));
		
			String docStatus = meta.getDocStatus();			
			if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.010");
			}else if(Util.equals(FlowDocStatusEnum.待覆核.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.020");
			}else if(Util.equals(FlowDocStatusEnum.已核准.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.030");
			}
			result.set("docStatus", docStatus);
			
			result.set("hold_no", Util.trim(l140s02l.getHold_no()));
			result.set("owner_id", Util.trim(l140s02l.getOwner_id()));
			result.set("app_date", l140s02l.getApp_date());
			result.set("house_adr", Util.trim(l140s02l.getHouse_adr()));
			result.set("owner_nm", Util.trim(l140s02l.getOwner_nm()));
			result.set("ownsp_id", Util.trim(l140s02l.getOwnsp_id()));
			result.set("ownsp_nm", Util.trim(l140s02l.getOwnsp_nm()));
			result.set("spouse_id", Util.trim(l140s02l.getSpouse_id()));
			result.set("spouse_nm", Util.trim(l140s02l.getSpouse_nm()));
			result.set("coll_ln", Util.trim(l140s02l.getColl_ln()));
			result.set("coll_bn", Util.trim(l140s02l.getColl_bn()));
			result.set("coll_addr", Util.trim(l140s02l.getColl_addr()));
			result.set("set_hold", l140s02l.getSet_hold());
			
			// common 要求塞值欄位
			result.set(EloanConstants.MAIN_OID, meta.getOid());
			result.set(EloanConstants.MAIN_ID, meta.getMainId());
			result.set(EloanConstants.MAIN_UID, meta.getUid());
			result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());			
			result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
			
		}

		return result;
	}
	
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		L140MM2A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = service.findL140MM2A_oid(mainOid);
			
			String errMsg = "";
			if(Util.equals("核定", decisionExpr) || Util.equals("解除核定", decisionExpr)){
				//檢查經辦和主管是否為同一人
				if(Util.equals(user.getUserId(), meta.getUpdater())){
					errMsg = RespMsgHelper.getMessage("EFD0053");	
				}			
			}
			if(Util.isNotEmpty(errMsg)){
				throw new CapMessageException(errMsg, getClass());
			}
			
			flowSimplifyService.flowNext(meta.getOid(), decisionExpr);
			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
	
	public IResult echo_custId(PageParameters params) throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		result.set("custId", Util.trim(params.getString("custId")));
		result.set("dupNo", Util.trim(params.getString("dupNo")));
		result.set("custName", Util.trim(params.getString("custName")));
		result.set("cntrNo", Util.trim(params.getString("cntrNo")));
		return result;
	
	}
	
	/**
	 * 新增L140MM2A  L140S02L
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newl140mm2a(PageParameters params)	//params: custId /	dupNo /	custName /	cntrNo / sDate
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String l140mm2aMainid = "";
		
		//use cntrno to get ELF508	//Elf508 PK : CUST_ID + DISAS_TYPE + HOLD_NO + OWNER_ID
		List<ELF508> elf508Data_list = misELF508Service.findByCntrNo(params.getString("cntrNo"));
		ELF508 elf508Data = elf508Data_list.get(0);

		//L140MM2A
		L140MM2A l140mm2a = new L140MM2A();
		
		l140mm2aMainid = IDGenerator.getUUID();
		
		l140mm2a.setMainId(l140mm2aMainid);
		l140mm2a.setSeq(0);
		
		l140mm2a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		l140mm2a.setCustId(params.getString("custId"));
		l140mm2a.setDupNo(params.getString("dupNo"));
		l140mm2a.setCustName(params.getString("custName"));
		l140mm2a.setUnitType(user.getUnitType());
		l140mm2a.setOwnBrId(user.getUnitNo());
		l140mm2a.setDocStatus(FlowDocStatusEnum.編製中.getCode());
		String txCode = Util.trim(params.getString(EloanConstants.TRANSACTION_CODE));
		l140mm2a.setTxCode(txCode);
		l140mm2a.setCreator(user.getUserId());
		l140mm2a.setCreateTime(CapDate.getCurrentTimestamp());
		l140mm2a.setUpdater(user.getUserId());
		l140mm2a.setUpdateTime(CapDate.getCurrentTimestamp());
		l140mm2a.setDeletedTime(CapDate.getCurrentTimestamp());	//btnSave時記得清空
		l140mm2a.setCntrNo(params.getString("cntrNo"));
		l140mm2a.setLoanCurr("TWD");
		l140mm2a.setLoanAmt(elf508Data.getElf508_app_amt());		
		l140mm2a.setLoanType(elf508Data.getElf508_loan_type());
		l140mm2a.setDisasType(elf508Data.getElf508_disas_type());		
		
		//L140S02L
		L140S02L l140s02l = new L140S02L();
		
		l140s02l.setMainId(l140mm2aMainid);
		l140s02l.setSeq(l140mm2a.getSeq());
		
		l140s02l.setHold_no(elf508Data.getElf508_hold_no());	//ReadOnly
		l140s02l.setOwner_id(elf508Data.getElf508_owner_id());	//ReadOnly
		l140s02l.setApp_date(elf508Data.getElf508_app_date());
		l140s02l.setHouse_adr(elf508Data.getElf508_house_adr());
		l140s02l.setOwner_nm(elf508Data.getElf508_owner_nm());
		l140s02l.setOwnsp_id(elf508Data.getElf508_ownsp_id());
		l140s02l.setOwnsp_nm(elf508Data.getElf508_ownsp_nm());
		l140s02l.setSpouse_id(elf508Data.getElf508_spouse_id());
		l140s02l.setSpouse_nm(elf508Data.getElf508_spouse_nm());
		l140s02l.setColl_ln(elf508Data.getElf508_coll_ln());
		l140s02l.setColl_bn(elf508Data.getElf508_coll_bn());
		l140s02l.setColl_addr(elf508Data.getElf508_coll_addr());
		l140s02l.setSet_hold(elf508Data.getElf508_set_hold());
		//ReadOnly
		l140s02l.setCancel_date(elf508Data.getElf508_cancel_date());
		l140s02l.setEloan_date(elf508Data.getElf508_eloan_date());
		l140s02l.setAloan_date(elf508Data.getElf508_aloan_date());
		
		//service to save [l140mm2a, l140s02L]
		service.save(l140mm2a, l140s02l);
				
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(EloanConstants.OID, l140mm2a.getOid());
		
		return result; 
	}
}
