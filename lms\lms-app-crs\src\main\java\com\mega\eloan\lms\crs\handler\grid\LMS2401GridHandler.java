package com.mega.eloan.lms.crs.handler.grid;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.pages.LMS2401M01Page;
import com.mega.eloan.lms.crs.service.LMS2400Service;
import com.mega.eloan.lms.mfaloan.service.MisELF490BService;
import com.mega.eloan.lms.mfaloan.service.MisELF491CService;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;


@Scope("request")
@Controller("lms2401gridhandler")
public class LMS2401GridHandler extends AbstractGridHandler {
	
	@Resource
	BranchService branchService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	RetrialService retrialService;
	
	@Resource
	LMS2400Service lms2400Service;
	
	@Resource
	MisMISLN20Service misMISLN20Service;
	
	@Resource
	MisELF490BService misELF490BService;
	
	@Resource
	MisELF491CService misELF491CService;
	
	//J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
	Properties prop_lms2401m01 = MessageBundleScriptCreator
	.getComponentResource(LMS2401M01Page.class);
	
	/**
	 * 查詢Grid 覆審明細
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */	
	public CapMapGridResult queryList(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
						
		List<C241M01A> rows = retrialService.grid_C241M01A_default(mainId); 
		
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for(C241M01A c241m01a : rows){
			C241M01E c241m01e = retrialService.findC241M01E_first_L1(c241m01a, "2");
			//---
			Map<String, Object> row = new HashMap<String, Object>();
			LMSUtil.meta_to_map(row, c241m01a, new String[]{ "custName"
					, "totQuota", "lastRetrialDate", "shouldReviewDate", "retrialKind"
					, "docStatus", "docKind", "grpCntrNo", "retrialYN", "nCreatData", "condition"
					, "lastRealDt" , "nckdDetail"
					, "oid", "mainId", "custId", "dupNo"});
			
			row.put("projectNo", CrsUtil.seqPart2nd_FromProjectNo(c241m01a.getProjectNo()));
			row.put("retrialYN_NCreatData", Util.trim(c241m01a.getRetrialYN())+"|"+Util.trim(c241m01a.getNCreatData()));
			row.put("custId_dupNo", Util.trim(c241m01a.getCustId())+" "+Util.trim(c241m01a.getDupNo()));
						
			row.put("docStatusDesc", lms2400Service.c241m01a_docStatusDesc(c241m01a));
			
			row.put("newCase", _newCaseDesc(Util.trim(c241m01a.getNewCase())));
			row.put("totQuota", NumConverter.addComma(c241m01a.getTotQuota()));
			row.put("totBal", NumConverter.addComma(c241m01a.getTotBal()));
			row.put("nCkdFlag",Util.trim(c241m01a.getNCkdFlag())+(Util.isNotEmpty(c241m01a.getNCkdMemo())?("-"+Util.trim(c241m01a.getNCkdMemo())):""));
			row.put("uploadFlag", CrsUtil.haveBeenUpload(c241m01a)?"V":"");
			
			add_column_realFmt(row, c241m01a);
			//---
			String approverStaff = Util.trim(c241m01a.getApprover());
			String approverStaffName =  userInfoService.getUserName(approverStaff);
			if(Util.isNotEmpty(approverStaffName)){
				approverStaff = approverStaffName;
			}
			row.put("approverStaff", approverStaff);
			//---
			String retrialStaff = "";
			if(c241m01e !=null ){
				retrialStaff = Util.isNotEmpty(c241m01e.getStaffName())?c241m01e.getStaffName():c241m01e.getStaffNo(); 
			}			
			row.put("retrialStaff", retrialStaff);
			//---
			row.put("movetobr_flag", movetobr_flag(c241m01a));
			row.put("c240m01a_mainId", mainId);
			
			//J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
			if (!Util.isEmpty(Util.trim(c241m01a.getStatus()))) {
				row.put("status",
						prop_lms2401m01.getProperty("rpa.status."
								+ Util.trim(c241m01a.getStatus())));
			}else{
				row.put("status", "");
			}
			
			
			//---
			list.add(row);
		}
		
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
		
	}
	
	/** 在前端JS, 判斷可否 "送受檢單位登錄"
	 * @param c241m01a
	 * @return Y/N
	 */
	private String movetobr_flag(C241M01A c241m01a){
		String movetobr_flag = "N";
		if(CrsUtil.isCaseS(c241m01a)){
			
		}else{
			movetobr_flag = Util.equals(RetrialDocStatusEnum.區中心_待覆核.getCode(), c241m01a.getDocStatus())?"Y":"N";	
		}			
		return movetobr_flag;
	} 
	
	private void add_column_realFmt(Map<String, Object> row, C241M01A c241m01a){
		/*
		* signal 符號 △  ◎
		*/
		String _realFmt = "";
		if(CrsUtil.docKindN_since_R11(c241m01a)){
			if(Util.equals(CrsUtil.DOCFMT_土建融實地覆審, c241m01a.getDocFmt())){
				_realFmt = "◎";
			}else{
				if(Util.equals("Y", c241m01a.getRealCkFg())){
					_realFmt = "△";
				}
			}	
		}			
		row.put("_realFmt", _realFmt);
	}
	
	public CapMapGridResult queryListUnderGrpCntrNo(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		String grpCntrNo = Util.trim(params.getString("grpCntrNo"));
		String c240m01a_mainId = Util.trim(params.getString("c240m01a_mainId"));
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for (C241M01A c241m01a : retrialService.grid_C241M01A_byGrpCntrNo(c240m01a_mainId, grpCntrNo)) {
			C241M01E c241m01e = retrialService.findC241M01E_first_L1(c241m01a, "2");
			
			Map<String, Object> row = new HashMap<String, Object>();
			LMSUtil.meta_to_map(row, c241m01a, new String[]{ "custName"
					, "grpCntrNo", "totBalCurr" , "totQuotaCurr", "docStatus"
					, "oid", "mainId", "custId", "dupNo", "retrialYN", "lastRealDt"});
			row.put("projectNo", CrsUtil.seqPartLast_FromProjectNo(c241m01a.getProjectNo()));
			row.put("totQuota", NumConverter.addComma(c241m01a.getTotQuota()));
			row.put("totBal", NumConverter.addComma(c241m01a.getTotBal()));
			row.put("custId_dupNo", Util.trim(c241m01a.getCustId())+" "+Util.trim(c241m01a.getDupNo()));
			row.put("docStatusDesc", lms2400Service.c241m01a_docStatusDesc(c241m01a));
			row.put("uploadFlag", CrsUtil.haveBeenUpload(c241m01a)?"V":"");

			add_column_realFmt(row, c241m01a);
			//---
			String approverStaff = Util.trim(c241m01a.getApprover());
			String approverStaffName =  userInfoService.getUserName(approverStaff);
			if(Util.isNotEmpty(approverStaffName)){
				approverStaff = approverStaffName;
			}
			row.put("approverStaff", approverStaff);
			//---
			String retrialStaff = "";
			if(c241m01e !=null ){
				retrialStaff = Util.isNotEmpty(c241m01e.getStaffName())?c241m01e.getStaffName():c241m01e.getStaffNo(); 
			}			
			row.put("retrialStaff", retrialStaff);
			//---
			row.put("movetobr_flag", movetobr_flag(c241m01a));
			//---
			list.add(row);
		}
		
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
	
	private String _newCaseDesc(String s){
		Map<String, String> m = retrialService.get_crs_NewCaseDescMap();
		if(m.containsKey(s)){
			//P【人工】
			//G【團貸】
			
			return "【"+m.get(s)+"】";
		}else{
			return s;
		}
		
	}
	
	public CapMapGridResult queryNckdFlagO(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = Util.trim(params.getString("mainOid"));
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId", meta.getBranchId());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "c240a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		
		@SuppressWarnings("rawtypes")
		Page src_page = retrialService.findPage(C240M01A.class, pageSetting);		
		@SuppressWarnings("unchecked")
		List<C240M01A> src_list = src_page.getContent();
		
		String show_branchId = meta.getBranchId() + " "+branchService.getBranchName(meta.getBranchId());
		for (C240M01A c240m01a : src_list) {
			
			Map<String, Object> row = new HashMap<String, Object>();
			if(Util.equals(meta.getOid(), c240m01a.getOid())){
				continue;
			}
			LMSUtil.meta_to_map(row, c240m01a, new String[]{ 
					"branchId", "dataEndDate", "expectedRetrialDate"
					, "oid", "mainId"});
			
			row.put("show_branchId", show_branchId);
			
			//---
			list.add(row);
		}
		
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
	
	public CapMapGridResult queryEscrowComIdList(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> src : misMISLN20Service.findLNF660()) {
			Map<String, Object> row = new HashMap<String, Object>();
			
			row.put("cntrNo", Util.trim(src.get("LNF660_M_CONTRACT"))); 
			row.put("lnf660_loan_class", Util.trim(src.get("LNF660_LOAN_CLASS")));
			row.put("DP_AC", Util.trim(src.get("LNF660_DP_AC")));
			row.put("comId", Util.trim(src.get("LNF020_CUST_ID")));
			row.put("comName", Util.trim(src.get("CNAME")));
			//---
			list.add(row);
		}
		
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
	
	public CapMapGridResult queryEscrowList(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		
		String mainOid = Util.trim(params.getString("mainOid"));
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		String type = Util.trim(params.getString("type"));
		String sDate = Util.trim(params.getString("sDate"));		
		//---
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String lnf660_loan_class = Util.trim(params.getString("lnf660_loan_class"));
		//String DP_AC = Util.trim(params.getString("DP_AC"));
		String comId = Util.trim(params.getString("comId"));
		String comName = Util.trim(params.getString("comName"));
		//---
		String lnf034_CP_BANK_CD = meta.getBranchId();
		
		List<Map<String, Object>> list = lms2400Service.escrowList(type, lnf660_loan_class
				, comId, comName, cntrNo, lnf034_CP_BANK_CD, sDate );
		for(Map<String, Object> item : list){
			item.put("show_escrow_bal", NumConverter.addComma((BigDecimal)item.get("escrow_bal")));
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryElsUser(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		String userId = Util.trim(params.getString("userId"));
		String userName = Util.trim(params.getString("userName"));
		pageSetting.addSearchModeParameters(SearchMode.LIKE, "userId", userId+"%");
		pageSetting.addSearchModeParameters(SearchMode.LIKE, "userName", userName+"%");
		Page page = retrialService.findPage(ElsUser.class, pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapMapGridResult queryDocKindS_R1toR5(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		String mainOid = Util.trim(params.getString("mainOid"));
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		String brNo = meta.getBranchId();	
		//---
		String pa_ym = Util.trim(params.getString("pa_ym"));
		String empNo = Util.trim(params.getString("empNo"));
		String beg_yyyyMMdd = CrsUtil.get_CLS180R18_period_byEndYM(pa_ym, 10)[0];
		String end_yyyyMMdd = CrsUtil.get_CLS180R18_period_byEndYM(pa_ym, 10)[1];
		//---
		List<Map<String, Object>> src_list = misELF490BService.chooseCust_by_brNo_empNo_R1toR5(beg_yyyyMMdd, end_yyyyMMdd, brNo, empNo);
		if(src_list.size()==0){
			src_list = misELF490BService.chooseCust_by_brNo_specificEmpNo(beg_yyyyMMdd, end_yyyyMMdd, brNo, empNo);
		}
		return _proc_docKindS(pageSetting, src_list);
	}
	
	public CapMapGridResult queryDocKindS_R6(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainOid = Util.trim(params.getString("mainOid"));
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		String brNo = meta.getBranchId();
		//---
		String pa_ym = Util.trim(params.getString("pa_ym"));
		String empNo = Util.trim(params.getString("empNo"));
		String beg_yyyyMMdd = CrsUtil.get_CLS180R18_period_byEndYM(pa_ym, 10)[0];
		String end_yyyyMMdd = CrsUtil.get_CLS180R18_period_byEndYM(pa_ym, 10)[1];
		//---
		List<Map<String, Object>> src_list = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> unsecure_list = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> secure_list = new ArrayList<Map<String, Object>>();
		for(Map<String, Object> row : misELF490BService.chooseCust_by_brNo_empNo_R6(beg_yyyyMMdd, end_yyyyMMdd, brNo, empNo)){
			if(Util.equals("1", MapUtils.getString(row, "ORD"))){
				unsecure_list.add(row);
			}else{
				secure_list.add(row);
			}
		}
		if(unsecure_list.size()>0){
			src_list = unsecure_list;
		}else{
			src_list = secure_list;
		}
		return _proc_docKindS(pageSetting, src_list);
	}
	
	public CapMapGridResult queryDocKindS_specificEmpNo(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainOid = Util.trim(params.getString("mainOid"));
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		String brNo = meta.getBranchId();
		//---
		String pa_ym = Util.trim(params.getString("pa_ym"));
		String empNo = Util.trim(params.getString("empNo"));
		String beg_yyyyMMdd = CrsUtil.get_CLS180R18_period_byEndYM(pa_ym, 10)[0];
		String end_yyyyMMdd = CrsUtil.get_CLS180R18_period_byEndYM(pa_ym, 10)[1];
		
		if(true){ //前端可能只輸入5碼行編, 前補0
			empNo = Util.getRightStr("000000"+empNo, 6);	
		}
		//---
		List<Map<String, Object>> src_list = misELF490BService.chooseCust_by_brNo_specificEmpNo(beg_yyyyMMdd, end_yyyyMMdd, brNo, empNo);		
		return _proc_docKindS(pageSetting, src_list);
	}
	
	private CapMapGridResult _proc_docKindS(ISearch pageSetting, List<Map<String, Object>> src_list){
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Set<String> existIdDupSet = new HashSet<String>();

		for(Map<String, Object> item : src_list){
			String LNF022_CUST_ID = MapUtils.getString(item, "LNF022_CUST_ID");
			if(existIdDupSet.contains(LNF022_CUST_ID)){
				continue;
			}else{
				existIdDupSet.add(LNF022_CUST_ID);
			}
						
			item.put("custId_dupNo", LNF022_CUST_ID);
			item.put("custName", Util.trim(MapUtils.getString(item, "CNAME")));
			//========
			list.add(item);
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapMapGridResult queryRule95_1(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		String mainOid = Util.trim(params.getString("mainOid"));
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		String brNo = meta.getBranchId();	
		//---		
		String retrial_dt = Util.trim(TWNDate.toAD(meta.getExpectedRetrialDate()));
		String[] donePeriod_arr = CrsUtil.get_R95_1_done_period(retrial_dt); 
		String donePeriod_beg = donePeriod_arr[0];
		String donePeriod_end = donePeriod_arr[1];
		
		List<Map<String, Object>> src_list = misELF491CService.selByBrno_ELF491C_RULE_NO_95_1(brNo, donePeriod_beg, donePeriod_end, CrsUtil.R95_1);
		Set<String> existIdDupSet = new HashSet<String>();
		for(Map<String, Object> item : src_list){
			String LNF020_CUST_ID = MapUtils.getString(item, "LNF020_CUST_ID");
			if(existIdDupSet.contains(LNF020_CUST_ID)){
				continue;
			}else{
				existIdDupSet.add(LNF020_CUST_ID);
			}
						
			item.put("custId_dupNo", LNF020_CUST_ID);
			item.put("custName", Util.trim(MapUtils.getString(item, "CNAME")));
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(src_list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapMapGridResult queryProjectCreditLoan(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		String mainOid = Util.trim(params.getString("mainOid"));
		int fetch_size = params.getInt("fetch_size", 250);
		C240M01A meta = retrialService.findC240M01A_oid(mainOid);
		String brNo = meta.getBranchId();	
		Date nowDate = CapDate.getCurrentTimestamp();
		String projectCreditLoanSDate = params.getString("projectCreditLoanSDate");
		String projectCreditLoanEDate = params.getString("projectCreditLoanEDate");
		if(CrsUtil.isNull_or_ZeroDate(CapDate.parseDate(projectCreditLoanSDate)) || CrsUtil.isNull_or_ZeroDate(CapDate.parseDate(projectCreditLoanEDate))){
			projectCreditLoanSDate = TWNDate.toAD(CapDate.addMonth(nowDate, -12));
			projectCreditLoanEDate = TWNDate.toAD(CapDate.shiftDays(nowDate, -1));
		}
		//---
		List<Map<String, Object>> src_list = misELF491CService.selByBrno_Rule_projectCreditLoan(brNo, projectCreditLoanSDate, projectCreditLoanEDate, fetch_size);
		Set<String> existIdDupSet = new HashSet<String>();
		for(Map<String, Object> item : src_list){
			String LNF030_CUST_ID = MapUtils.getString(item, "LNF030_CUST_ID");
			if(existIdDupSet.contains(LNF030_CUST_ID)){
				continue;
			}else{
				existIdDupSet.add(LNF030_CUST_ID);
			}
						
			item.put("custId_dupNo", LNF030_CUST_ID);
			item.put("custName", Util.trim(MapUtils.getString(item, "CNAME")));
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(src_list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
}
