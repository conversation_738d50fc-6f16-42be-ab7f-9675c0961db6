/* 
 * C900M01K.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 授信業務授權額度檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C900M01K", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900M01K extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 授權層級<p/>
	 * 參考lms1205m01_caseLvl<br/>
	 *  A.董事會 <br/>
	 *  1.常務董事會<br/>
	 *  6.總經理<br/>
	 *  7.副總經理<br/>
	 *  8.授信審查處處長<br/>
	 *  B.區域營運中心營運長
	 */
	@Size(max=2)
	@Column(name="CASELVL", length=2, columnDefinition="VARCHAR(2)")
	private String caseLvl;

	/** 
	 * 分行代號<p/>
	 * COM.BELSBRN.brNo<br/>
	 *  放特別指定的分行代號<br/>
	 *  007.國外部<br/>
	 *  201金控總部分行<br/>
	 *  025.國際金融業務分行經理
	 */
	@Size(max=3)
	@Column(name="BRNO", length=3, columnDefinition="VARCHAR(3)")
	private String brNo;

	/** 
	 * 分行等級<p/>
	 * COM.BELSBRN.brClass
	 */
	@Size(max=1)
	@Column(name="BRCLASS", length=1, columnDefinition="VARCHAR(1)")
	private String brClass;

	/** 
	 * 項目<p/>
	 * 目前有A、B兩項
	 */
	@Size(max=1)
	@Column(name="TYPE", length=1, columnDefinition="VARCHAR(1)")
	private String type;

	/** 無擔保授信每戶限額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTAMTN", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factAmtN;

	/** 擔保授信每戶限額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTAMTS", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factAmtS;

	/** 授信每戶總額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factAmt;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 單獨劃分授信每戶限額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTAMTALOAN", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factAmtAloan;
	
	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得授權層級<p/>
	 * 參考lms1205m01_caseLvl<br/>
	 *  A.董事會 <br/>
	 *  1.常務董事會<br/>
	 *  6.總經理<br/>
	 *  7.副總經理<br/>
	 *  8.授信審查處處長<br/>
	 *  B.區域營運中心營運長
	 */
	public String getCaseLvl() {
		return this.caseLvl;
	}
	/**
	 *  設定授權層級<p/>
	 *  參考lms1205m01_caseLvl<br/>
	 *  A.董事會 <br/>
	 *  1.常務董事會<br/>
	 *  6.總經理<br/>
	 *  7.副總經理<br/>
	 *  8.授信審查處處長<br/>
	 *  B.區域營運中心營運長
	 **/
	public void setCaseLvl(String value) {
		this.caseLvl = value;
	}

	/** 
	 * 取得分行代號<p/>
	 * COM.BELSBRN.brNo<br/>
	 *  放特別指定的分行代號<br/>
	 *  007.國外部<br/>
	 *  201金控總部分行<br/>
	 *  025.國際金融業務分行經理
	 */
	public String getBrNo() {
		return this.brNo;
	}
	/**
	 *  設定分行代號<p/>
	 *  COM.BELSBRN.brNo<br/>
	 *  放特別指定的分行代號<br/>
	 *  007.國外部<br/>
	 *  201金控總部分行<br/>
	 *  025.國際金融業務分行經理
	 **/
	public void setBrNo(String value) {
		this.brNo = value;
	}

	/** 
	 * 取得分行等級<p/>
	 * COM.BELSBRN.brClass
	 */
	public String getBrClass() {
		return this.brClass;
	}
	/**
	 *  設定分行等級<p/>
	 *  COM.BELSBRN.brClass
	 **/
	public void setBrClass(String value) {
		this.brClass = value;
	}

	/** 
	 * 取得項目<p/>
	 * 目前有A、B兩項
	 */
	public String getType() {
		return this.type;
	}
	/**
	 *  設定項目<p/>
	 *  目前有A、B兩項
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 取得無擔保授信每戶限額 **/
	public BigDecimal getFactAmtN() {
		return this.factAmtN;
	}
	/** 設定無擔保授信每戶限額 **/
	public void setFactAmtN(BigDecimal value) {
		this.factAmtN = value;
	}

	/** 取得擔保授信每戶限額 **/
	public BigDecimal getFactAmtS() {
		return this.factAmtS;
	}
	/** 設定擔保授信每戶限額 **/
	public void setFactAmtS(BigDecimal value) {
		this.factAmtS = value;
	}

	/** 取得授信每戶總額 **/
	public BigDecimal getFactAmt() {
		return this.factAmt;
	}
	/** 設定授信每戶總額 **/
	public void setFactAmt(BigDecimal value) {
		this.factAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得單獨劃分授信每戶限額 **/
	public BigDecimal getFactAmtAloan() {
		return this.factAmtAloan;
	}
	/** 設定單獨劃分授信每戶限額 **/
	public void setFactAmtAloan(BigDecimal value) {
		this.factAmtAloan = value;
	}
}
