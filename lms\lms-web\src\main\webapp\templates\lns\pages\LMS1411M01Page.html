<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
    	<th:block th:fragment="innerPageBody">
    		<script type="text/javascript">
				loadScript('pagejs/lns/LMS1411M01Page');
    		</script>
    		<div class="button-menu funcContainer" id="buttonPanel">
    			<!--編製中 -->
    			<th:block th:if="${_btnDOC_EDITING_visible}">
    				<button type="button" id="btnSend">
    					<span class="ui-icon ui-icon-jcs-02"></span>
    					<th:block th:text="#{'button.send'}"><!--呈主管覆核--></th:block>
    				</button>
    			</th:block>
    			<!--待覆核 -->
    			<th:block th:if="${_btnWAIT_APPROVE_visible}">
    				<button type="button" id="btnCheck">
    					<span class="ui-icon ui-icon-jcs-106"></span>
    					<th:block th:text="#{'button.check'}"><!--覆核--></th:block>
    				</button>
    			</th:block>
                <button id="btnPrint" class="forview">
                    <span class="ui-icon ui-icon-jcs-03"></span>
    				<th:block th:text="#{'button.print'}"><!--列印--></th:block>
                </button>
                <button id="btnExit" type="button" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
    				<th:block th:text="#{'button.exit'}"><!--離開--></th:block>
                </button>
    		</div>
    		<div class="tit2 color-black">
				<th:block th:text="#{'L141M01A.title001'}"><!--聯行額度明細表--></th:block>：
                (<span id="showTypCd" class="text-red"></span>)<span id="showCustId" class="color-blue"></span>
    		</div>
            <div class="tabs doc-tabs">
            	<ul>
					<li>
						<a href="#tab-01" goto="01"><b><th:block th:text="#{'doc.docinfo'}"><!--文件資訊--></th:block></b></a>
					</li>
                    <li>
                    	<a href="#tab-02" goto="02"><b><th:block th:text="#{'L141M01A.title002'}"><!--額度明細表細目--></th:block></b></a>
                    </li>
                    <li>
                    	<a href="#tab-03" goto="03"><b><th:block th:text="#{'L141M01A.title003'}"><!--其他簽案相關資訊--></th:block></b></a>
                    </li>
            	</ul>
                <div class="tabCtx-warp">
                	<div th:id="${tabID}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
                </div>
            </div>
            <div id="openCheckBox" style="display:none">
            	<span id="check1">
            		<label>
            			<input name="checkRadio" type="radio" value="2"></input>
            			<th:block th:text="#{'L141M01A.btn02'}"><!--確認--></th:block>
            		</label>
            		<br>
            		<label>
                        <input name="checkRadio" type="radio" value="1"></input>
            			<th:block th:text="#{'L141M01A.btn01'}"><!--退回經辦修改--></th:block>
            		</label>
            	</span>
            </div>
            <!--列印-->
            <div id="printView" style="display:none;">
				<div id="printGrid"></div>
            </div>
            <div id="openChecDatekBox" style="display:none">
            	<div>
                    <input id="forCheckDate" type="text" size="10" maxlength="10" class="date"></input>
            	</div>
            </div>
            <div id="selectBossBox" style="display:none;">
            	<form id="selectBossForm">
            		<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
            			<tr>
            				<td class="hd1">
								<th:block th:text="#{'L141M01A.title012'}"><!--帳戶管理員--></th:block>&nbsp;&nbsp;
            				</td>
            				<td>
            					<select id="accounting" name="accounting" class="boss"></select>
            				</td>
            			</tr>
            			<tr>
            				<td class="hd1" width="60%">
								<th:block th:text="#{'L141M01A.selectBoss'}"><!--授信主管人數--></th:block>&nbsp;&nbsp;
            				</td>
            				<td width="40%">
            					<select id="numPerson" name="numPerson">
									<option value="1">1</option>
									<option value="2">2</option>
									<option value="3">3</option>
									<option value="4">4</option>
									<option value="5">5</option>
									<option value="6">6</option>
									<option value="7">7</option>
									<option value="8">8</option>
									<option value="9">9</option>
									<option value="10">10</option>
            					</select>
            				</td>
            			</tr>
            			<tr>
                            <td class="hd1">
								<th:block th:text="#{'L141M01A.bossId'}"><!--授信主管--></th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                            	<div>
									<th:block th:text="#{'L141M01A.no'}"><!--第--></th:block>1<th:block th:text="#{'L141M01A.site'}"><!--位--></th:block>
									<th:block th:text="#{'L141M01A.bossId'}"><!--授信主管--></th:block>&nbsp;&nbsp;
									<select id="mainBoss" name="boss1" class="boss"></select>
                                    <span id="newBossSpan"></span>
                            	</div>
                            </td>
            			</tr>
            			<tr>
            				<td class="hd1">
								<th:block th:text="#{'L141M01A.title010'}"><!--單位/授權主管--></th:block>&nbsp;&nbsp;
            				</td>
            				<td>
                                <select id="manager" name="manager" class="boss"></select>
            				</td>
            			</tr>
            		</table>
            	</form>
            </div>
    	</th:block>
    </body>
</html>
