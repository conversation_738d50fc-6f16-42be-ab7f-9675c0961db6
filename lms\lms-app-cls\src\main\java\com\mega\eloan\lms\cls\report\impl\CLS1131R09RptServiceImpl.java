package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1131R08RptService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01C;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01C;

import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 列印新版負債比
 * </pre>
 *
 * <AUTHOR>
 * @version <ul>
 * <li>2020/01/20, EL08034
 * </ul>
 * @since 2020/01/20
 */
@Service("cls1131r09rptservice")
public class CLS1131R09RptServiceImpl implements FileDownloadService, CLS1131R08RptService {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1131R09RptServiceImpl.class);


	@Resource
	CLS1131Service cls1131Service;

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 *
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params) throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}

	}

	/**
	 * 建立PDF
	 * @param params
	 * @return
	 * @throws FileNotFoundException
	 * @throws IOException
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public OutputStream generateReport(PageParameters params) throws FileNotFoundException, IOException, Exception {

		OutputStream outputStream = null;
		Locale locale = null;
		Map<String, String> rptVariableMap = null;
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		try {
			boolean isC120M01A = params.getBoolean("isC120M01A");
			if (isC120M01A) {
				C120S01C c120s01c = cls1131Service.findModelByKey(C120S01C.class, mainId, custId, dupNo);
				if (c120s01c != null && !CapString.isEmpty(c120s01c.getRateData())) {
					rptVariableMap = new LinkedHashMap<String, String>();
					JSONObject json = DataParse.toJSON(c120s01c.getRateData());
					
					C120M01A c120m01a = cls1131Service.findModelByKey(C120M01A.class, mainId, custId, dupNo);
					if (c120m01a != null) {
						rptVariableMap.put("CUSTID" , c120m01a.getCustId() + " " + c120m01a.getDupNo());
						rptVariableMap.put("CUSTNAME" , c120m01a.getCustName());
					}
					rptVariableMap = setReportData(rptVariableMap, json);
				}
			} else {
				C101S01C c101s01c = cls1131Service.findModelByKey(C101S01C.class, mainId, custId, dupNo);
				if (c101s01c != null && !CapString.isEmpty(c101s01c.getRateData())) {
					rptVariableMap = new LinkedHashMap<String, String>();
					JSONObject json = DataParse.toJSON(c101s01c.getRateData());
					
					C101M01A c101m01a = cls1131Service.findModelByKey(C101M01A.class, mainId, custId, dupNo);
					if (c101m01a != null) {
						rptVariableMap.put("CUSTID" , c101m01a.getCustId() + " " + c101m01a.getDupNo());
						rptVariableMap.put("CUSTNAME" , c101m01a.getCustName());
					}
					
					rptVariableMap = setReportData(rptVariableMap, json);
				}
			}
			locale = LMSUtil.getLocale();

			ReportGenerator generator = new ReportGenerator("report/cls/CLS1131R09RateDate_" + locale.toString() + ".rpt");

			generator.setVariableData(rptVariableMap);

			LOGGER.info("into generateReport");
			outputStream = generator.generateReport();
			LOGGER.info("exit generateReport");


		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}

	private Map<String, String> setReportData(
			Map<String, String> rptVariableMap, JSONObject json) {
		//動態組HTML
		StringBuffer appendHtml = new StringBuffer();
		
		//加head才有框線
		appendHtml.append("<html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8'><style>table, th {border-collapse: collapse;border-style: solid;border-width: 1px;}td {border-collapse: collapse;border-style: solid;border-width: 1px;padding-top: 2px;padding-right: 2px;padding-bottom: 2px;padding-left: 2px;}</style></head>");
		appendHtml.append("<body>");
		appendHtml.append("<div width='100%'>");
		appendHtml.append("<table width='100%' >  ");
		//本案資訊
		appendHtml.append("<tbody>  ");
		appendHtml.append("	<tr>                        ");
		appendHtml.append("		<td colspan='7'>                    ");
		appendHtml.append("			【本案資訊】&nbsp;&nbsp;&nbsp;      ");
		if (Util.trim(json.get("guarantorFlag")).equals("Y")) {
			appendHtml.append("■&nbsp;");
		} else {
			appendHtml.append("□&nbsp;");
		}
		appendHtml.append("			本案為保證人      ");
		appendHtml.append("		</td>                   ");
		appendHtml.append("	</tr>                       ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td width='21%'>                                                                                                                                                               ");
		if (Util.trim(json.get("mode_1")).equals("1")) {
			appendHtml.append("		■&nbsp;期付金&nbsp;&nbsp;&nbsp;□&nbsp;按月計息 ");
		} else if (Util.trim(json.get("mode_1")).equals("2")){
			appendHtml.append("		□&nbsp;期付金&nbsp;&nbsp;&nbsp;■&nbsp;按月計息 ");
		} else {
			appendHtml.append("		□&nbsp;期付金&nbsp;&nbsp;&nbsp;□&nbsp;按月計息 ");
		}
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		額度：" + (!Util.isEmpty(json.get("loan_1")) ? NumConverter.addComma(json.get("loan_1")) : "") + "仟元                                             ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		期間：" + (!Util.isEmpty(json.get("period_1")) ? NumConverter.addComma(json.get("period_1")) : "") + "期                                                     ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		寬限期：" + (!Util.isEmpty(json.get("extPeriod_1")) ? NumConverter.addComma(json.get("extPeriod_1")) : "") + "期                                          ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		利率：" + (!Util.isEmpty(json.get("rate_1")) ? NumConverter.addComma(json.get("rate_1")) : "") + "%                      ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		月付金：" + (!Util.isEmpty(json.get("mPay_1")) ? NumConverter.addComma(json.get("mPay_1")) : "") + "元                                         ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		調整後月付金：" + (!Util.isEmpty(json.get("mPayCh_1")) ? NumConverter.addComma(json.get("mPayCh_1")) : "") + "元                                         ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td width='21%'>                                                                                                                                                               ");
		if (Util.trim(json.get("mode_2")).equals("1")) {
			appendHtml.append("		■&nbsp;期付金&nbsp;&nbsp;&nbsp;□&nbsp;按月計息 ");
		} else if (Util.trim(json.get("mode_2")).equals("2")){
			appendHtml.append("		□&nbsp;期付金&nbsp;&nbsp;&nbsp;■&nbsp;按月計息 ");
		} else {
			appendHtml.append("		□&nbsp;期付金&nbsp;&nbsp;&nbsp;□&nbsp;按月計息 ");
		}
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		額度：" + (!Util.isEmpty(json.get("loan_2")) ? NumConverter.addComma(json.get("loan_2")) : "") + "仟元                                             ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		期間：" + (!Util.isEmpty(json.get("period_2")) ? NumConverter.addComma(json.get("period_2")) : "") + "期                                                     ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		寬限期：" + (!Util.isEmpty(json.get("extPeriod_2")) ? NumConverter.addComma(json.get("extPeriod_2")) : "") + "期                                          ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		利率：" + (!Util.isEmpty(json.get("rate_2")) ? NumConverter.addComma(json.get("rate_2")) : "") + "%                      ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		月付金：" + (!Util.isEmpty(json.get("mPay_2")) ? NumConverter.addComma(json.get("mPay_2")) : "") + "元                                         ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		調整後月付金：" + (!Util.isEmpty(json.get("mPayCh_2")) ? NumConverter.addComma(json.get("mPayCh_2")) : "") + "元                                         ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td width='21%'>                                                                                                                                                               ");
		if (Util.trim(json.get("mode_3")).equals("1")) {
			appendHtml.append("		■&nbsp;期付金&nbsp;&nbsp;&nbsp;□&nbsp;按月計息 ");
		} else if (Util.trim(json.get("mode_3")).equals("2")){
			appendHtml.append("		□&nbsp;期付金&nbsp;&nbsp;&nbsp;■&nbsp;按月計息 ");
		} else {
			appendHtml.append("		□&nbsp;期付金&nbsp;&nbsp;&nbsp;□&nbsp;按月計息 ");
		}
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		額度：" + (!Util.isEmpty(json.get("loan_3")) ? NumConverter.addComma(json.get("loan_3")) : "") + "仟元                                             ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		期間：" + (!Util.isEmpty(json.get("period_3")) ? NumConverter.addComma(json.get("period_3")) : "") + "期                                                     ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		寬限期：" + (!Util.isEmpty(json.get("extPeriod_3")) ? NumConverter.addComma(json.get("extPeriod_3")) : "") + "期                                          ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		利率：" + (!Util.isEmpty(json.get("rate_3")) ? NumConverter.addComma(json.get("rate_3")) : "") + "%                      ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		月付金：" + (!Util.isEmpty(json.get("mPay_3")) ? NumConverter.addComma(json.get("mPay_3")) : "") + "元                                         ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		調整後月付金：" + (!Util.isEmpty(json.get("mPayCh_3")) ? NumConverter.addComma(json.get("mPayCh_3")) : "") + "元                                         ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td width='21%'>                                                                                                                                                               ");
		if (Util.trim(json.get("mode_4")).equals("1")) {
			appendHtml.append("		■&nbsp;期付金&nbsp;&nbsp;&nbsp;□&nbsp;按月計息 ");
		} else if (Util.trim(json.get("mode_4")).equals("2")){
			appendHtml.append("		□&nbsp;期付金&nbsp;&nbsp;&nbsp;■&nbsp;按月計息 ");
		} else {
			appendHtml.append("		□&nbsp;期付金&nbsp;&nbsp;&nbsp;□&nbsp;按月計息 ");
		}
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		額度：" + (!Util.isEmpty(json.get("loan_4")) ? NumConverter.addComma(json.get("loan_4")) : "") + "仟元                                             ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		期間：" + (!Util.isEmpty(json.get("period_4")) ? NumConverter.addComma(json.get("period_4")) : "") + "期                                                     ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		寬限期：" + (!Util.isEmpty(json.get("extPeriod_4")) ? NumConverter.addComma(json.get("extPeriod_4")) : "") + "期                                          ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		利率：" + (!Util.isEmpty(json.get("rate_4")) ? NumConverter.addComma(json.get("rate_4")) : "") + "%                      ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		月付金：" + (!Util.isEmpty(json.get("mPay_4")) ? NumConverter.addComma(json.get("mPay_4")) : "") + "元                                         ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		調整後月付金：" + (!Util.isEmpty(json.get("mPayCh_4")) ? NumConverter.addComma(json.get("mPayCh_4")) : "") + "元                                         ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td width='21%'>                                                                                                                                                               ");
		if (Util.trim(json.get("mode_5")).equals("1")) {
			appendHtml.append("		■&nbsp;期付金&nbsp;&nbsp;&nbsp;□&nbsp;按月計息 ");
		} else if (Util.trim(json.get("mode_5")).equals("2")){
			appendHtml.append("		□&nbsp;期付金&nbsp;&nbsp;&nbsp;■&nbsp;按月計息 ");
		} else {
			appendHtml.append("		□&nbsp;期付金&nbsp;&nbsp;&nbsp;□&nbsp;按月計息 ");
		}
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		額度：" + (!Util.isEmpty(json.get("loan_5")) ? NumConverter.addComma(json.get("loan_5")) : "") + "仟元                                             ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		期間：" + (!Util.isEmpty(json.get("period_5")) ? NumConverter.addComma(json.get("period_5")) : "") + "期                                                     ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		寬限期：" + (!Util.isEmpty(json.get("extPeriod_5")) ? NumConverter.addComma(json.get("extPeriod_5")) : "") + "期                                          ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		利率：" + (!Util.isEmpty(json.get("rate_5")) ? NumConverter.addComma(json.get("rate_5")) : "") + "%                      ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		月付金：" + (!Util.isEmpty(json.get("mPay_5")) ? NumConverter.addComma(json.get("mPay_5")) : "") + "元                                         ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		調整後月付金：" + (!Util.isEmpty(json.get("mPayCh_5")) ? NumConverter.addComma(json.get("mPayCh_5")) : "") + "元                                         ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("</tbody>  ");
		appendHtml.append("</table></br>                     ");
		
		//配偶資訊
		appendHtml.append("<table width='100%' >  ");
		appendHtml.append("<tbody>  ");
		appendHtml.append("	<tr>                        ");
		appendHtml.append("		<td colspan='2'>                    ");
		appendHtml.append("			【配偶資訊】      ");
		appendHtml.append("		</td>                   ");
		appendHtml.append("	</tr>                       ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td width='25%'>                                                                                                                                                               ");
		appendHtml.append("		夫妻年收入 ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("yFamAmtR")) ? NumConverter.addComma(json.get("yFamAmtR")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		配偶貸款期付金  ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("yPeriod")) ? NumConverter.addComma(json.get("yPeriod")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		配偶信用卡循環  ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("yCycle")) ? NumConverter.addComma(json.get("yCycle")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		配偶分期未償還金額 ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("yPeriodUnpay")) ? NumConverter.addComma(json.get("yPeriodUnpay")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("</tbody>  ");
		appendHtml.append("</table></br>                     ");
		
		//家庭
		appendHtml.append("<table width='100%' >  ");
		appendHtml.append("<tbody>  ");
		appendHtml.append("	<tr>                        ");
		appendHtml.append("		<td colspan='2'>                    ");
		appendHtml.append("			【家庭資訊】      ");
		appendHtml.append("		</td>                   ");
		appendHtml.append("	</tr>                       ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td width='25%'>                                                                                                                                                               ");
		appendHtml.append("		借款人及家庭年收入 ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("fFamAmtR")) ? NumConverter.addComma(json.get("fFamAmtR")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		家庭貸款期付金  ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("fPeriod")) ? NumConverter.addComma(json.get("fPeriod")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		家庭信用卡循環  ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("fCycle")) ? NumConverter.addComma(json.get("fCycle")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		家庭分期未償還金額 ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("fPeriodUnpay")) ? NumConverter.addComma(json.get("fPeriodUnpay")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("</tbody>  ");
		appendHtml.append("</table></br>                     ");
		
		//聯徵資訊
		appendHtml.append("<table width='100%' >  ");
		appendHtml.append("<tbody>  ");
		appendHtml.append("	<tr>                        ");
		appendHtml.append("		<td colspan='7'>                    ");
		appendHtml.append("			【聯徵資訊】      ");
		appendHtml.append("		</td>                   ");
		appendHtml.append("	</tr>                       ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("<tr>                             ");
		appendHtml.append("	<td width='6%' align='center'>  ");
		appendHtml.append("		項目                        ");
		appendHtml.append("	</td>                           ");
		appendHtml.append("	<td width='23%' align='center'> ");
		appendHtml.append("		行庫名稱                    ");
		appendHtml.append("	</td>                           ");
		appendHtml.append("	<td width='6%' align='center'>  ");
		appendHtml.append("		聯徵<br/>科目               ");
		appendHtml.append("	</td>                           ");
		appendHtml.append("	<td width='21%' align='center'> ");
		appendHtml.append("		中文名稱                    ");
		appendHtml.append("	</td>                           ");
		appendHtml.append("	<td width='14%'  align='center'>");
		appendHtml.append("		額度(仟元)                  ");
		appendHtml.append("	</td>                           ");
		appendHtml.append("	<td width='14%' align='center'> ");
		appendHtml.append("		試算月付金(元)              ");
		appendHtml.append("	</td>                           ");
		appendHtml.append("	<td width='16%' align='center'> ");
		appendHtml.append("		調整後月付金(元)            ");
		appendHtml.append("	</td>                           ");
		appendHtml.append("	</tr>                           ");
		//foreach jcic item
		if (!Util.isEmpty(json.get("jcicCount"))) {
			if (json.getInt("jcicCount") == 0) {
				appendHtml.append("	<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td></tr>");
			} else {
				for ( int i = 1 ; i <= json.getInt("jcicCount") ; i ++) {
					appendHtml.append("	<tr>                           ");
					appendHtml.append("	<td align='center'>" + (!Util.isEmpty(json.get("jcic_" + i)) ? NumConverter.addComma(json.get("jcic_" + i)) : "") + "</td>");
					appendHtml.append("	<td align='left'>" + (!Util.isEmpty(json.get("jcic_bankname_" + i)) ? Util.trim(json.get("jcic_bankname_" + i)) : "") + "</td>");
					appendHtml.append("	<td align='center'>" + (!Util.isEmpty(json.get("jcic_item_" + i)) ? Util.trim(json.get("jcic_item_" + i)) : "") + "</td>");
					appendHtml.append("	<td align='left'>" + (!Util.isEmpty(json.get("jcic_itemname_" + i)) ? Util.trim(json.get("jcic_itemname_" + i)) : "") + "</td>");
					appendHtml.append("	<td align='right'>" + (!Util.isEmpty(json.get("jcic_loan_" + i)) ? NumConverter.addComma(json.get("jcic_loan_" + i)) : "") + "</td>");
					appendHtml.append("	<td align='right'>" + (!Util.isEmpty(json.get("jcic_period_" + i)) ? NumConverter.addComma(json.get("jcic_period_" + i)) : "") + "</td>");
					appendHtml.append("	<td align='right'>" + (!Util.isEmpty(json.get("jcic_period_ch_" + i)) ? NumConverter.addComma(json.get("jcic_period_ch_" + i)) : "") + "</td>");
					appendHtml.append("	</tr>                           ");
				}
			}
		} else {
			appendHtml.append("	<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td></tr>");
		}
		
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td colspan='2'>                                                                                                                                                               ");
		appendHtml.append("		信用卡循環(本行)   ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td colspan='2'>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("jcic_credit_017")) ? NumConverter.addComma(json.get("jcic_credit_017")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>調整後(元)</td><td colspan='2'>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("jcic_credit_ch_017")) ? NumConverter.addComma(json.get("jcic_credit_ch_017")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td colspan='2'>                                                                                                                                                               ");
		appendHtml.append("		信用卡循環(他行)   ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td colspan='2'>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("jcic_credit_not_017")) ? NumConverter.addComma(json.get("jcic_credit_not_017")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>調整後(元)</td><td colspan='2'>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("jcic_credit_not_ch_017")) ? NumConverter.addComma(json.get("jcic_credit_not_ch_017")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td colspan='2'>                                                                                                                                                               ");
		appendHtml.append("		分期未償還金額  ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td colspan='2'>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("jcic_credit_unpay")) ? NumConverter.addComma(json.get("jcic_credit_unpay")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>調整後(元)</td><td colspan='2'>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("jcic_credit_ch_unpay")) ? NumConverter.addComma(json.get("jcic_credit_ch_unpay")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("</tbody>  ");
		appendHtml.append("</table></br>                     ");
		
		//負債比
		appendHtml.append("<table width='100%' >  ");
		appendHtml.append("<tbody>  ");
		appendHtml.append("	<tr>                        ");
		appendHtml.append("		<td colspan='2'>                    ");
		appendHtml.append("			【負債比】      ");
		appendHtml.append("		</td>                   ");
		appendHtml.append("	</tr>                       ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td width='25%'>                                                                                                                                                               ");
		appendHtml.append("		個人收入  ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("pAllAmt")) ? NumConverter.addComma(json.get("pAllAmt")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		個人負債總額  ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("pDebtAmt")) ? NumConverter.addComma(json.get("pDebtAmt")) : "") + "元");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		個人負債比率   ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("dRateR")) ? NumConverter.addComma(json.get("dRateR")) : "") + "%");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		夫妻負債比率  ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("yRateR")) ? NumConverter.addComma(json.get("yRateR")) : "") + "%");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		appendHtml.append("<tr>                                                                                                                                                                ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("		家庭負債比率  ");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("	<td>                                                                                                                                                               ");
		appendHtml.append("	 " + (!Util.isEmpty(json.get("fRateR")) ? NumConverter.addComma(json.get("fRateR")) : "") + "%");
		appendHtml.append("	</td>                                                                                                                                                              ");
		appendHtml.append("</tr>                                                                                                                                                               ");
		
		
		appendHtml.append("</tbody>  ");
		appendHtml.append("</table></br>                     ");

		appendHtml.append("</div>                     ");
		appendHtml.append("</body></html>");
		
		rptVariableMap.put("htmlContent" , appendHtml.toString());
		return rptVariableMap;
	}


}
