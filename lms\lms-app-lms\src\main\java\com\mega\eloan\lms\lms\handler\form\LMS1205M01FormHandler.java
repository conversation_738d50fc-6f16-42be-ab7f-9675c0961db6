/* 
 * LMS1205FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.BranchDateTimeFormatter;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.utils.BeanValidator;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.lms.pages.LMS1205M01Page;
import com.mega.eloan.lms.lms.pages.LMS1205S05Page06a;
import com.mega.eloan.lms.lms.pages.LMS1205V01Page;
import com.mega.eloan.lms.lms.pages.LMSS02Page;
import com.mega.eloan.lms.lms.panels.LMS1205S05Panel;
import com.mega.eloan.lms.lms.panels.LMSS02Panel;
import com.mega.eloan.lms.lms.panels.LMSS07Panel;
import com.mega.eloan.lms.model.C140S09A;
import com.mega.eloan.lms.model.C140SDSC;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L120S01E;
import com.mega.eloan.lms.model.L120S01F;
import com.mega.eloan.lms.model.L120S01G;
import com.mega.eloan.lms.model.L120S05A;
import com.mega.eloan.lms.model.L120S05B;
import com.mega.eloan.lms.model.L120S05C;
import com.mega.eloan.lms.model.L120S05D;
import com.mega.eloan.lms.model.L120S17A;
import com.mega.eloan.lms.model.L121M01B;
import com.mega.eloan.lms.model.L130M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.Check2;
import com.mega.eloan.lms.validation.group.Check3;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書(企金授權外) FormHandler
 * </pre>
 * 
 * @since 2011/8/6
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2012/8/16,贊介 負責人資料改為可編輯欄位，解決輸入負責人姓名空白問題
 *          <li>2011/8/6,Miller Lin,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1205formhandler")
@DomainClass(L120M01A.class)
public class LMS1205M01FormHandler extends LMSM01FormHandler {

	/**
	 * <pre>
	 * 利用JDBC取資料(集團)
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	public IResult getJdbcData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch iBranch = branch.getBranch(MegaSSOSecurityContext.getUnitNo());
		String cesMainId = params.getString("cesMainId");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String grpSrc = params.getString("grpSrc");

		// J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
		Properties pop1205s06a = MessageBundleScriptCreator
				.getComponentResource(LMS1205S05Page06a.class);

		// 建立相關聯的資料表
		L120S05A l120s05a = service1205.findL120s05aByMainId(mainId);
		if (l120s05a != null) {
			// 若資料已存在就刪除
			service1205.delete(l120s05a);
		}
		// 建立新資料表
		l120s05a = new L120S05A();
		String form06Lms1205S05 = params.getString("LMS1205S05Form06");
		JSONObject jsonLms1205s05Form06 = JSONObject
				.fromObject(form06Lms1205S05);
		if (jsonLms1205s05Form06.isEmpty()) {
			jsonLms1205s05Form06 = new JSONObject();
		}
		jsonLms1205s05Form06.put(EloanConstants.MAIN_ID, mainId);
		jsonLms1205s05Form06.put("creator", user.getUserId());
		jsonLms1205s05Form06.put("createTime", new BranchDateTimeFormatter(
				iBranch).reformat(CapDate.parseToString(CapDate
				.getCurrentTimestamp())));
		jsonLms1205s05Form06.put("updater", user.getUserId());
		jsonLms1205s05Form06.put("updateTime", new BranchDateTimeFormatter(
				iBranch).reformat(CapDate.parseToString(CapDate
				.getCurrentTimestamp())));
		jsonLms1205s05Form06.put("grpSrc", Util.nullToSpace(grpSrc));
		DataParse.toBean(jsonLms1205s05Form06, l120s05a);

		String fromOtherDoc = "N";
		// 這裡實作引進資信簡表等相關來源來取得GroupId...
		if (!Util.isEmpty(cesMainId)) {
			service1205.findGrpId(jsonLms1205s05Form06, cesMainId);
		} else {
			jsonLms1205s05Form06.put("grpNo", "");
			jsonLms1205s05Form06.put("grpName", "");
			// 從相關文件取得集團代碼
			List<L120M01E> list = service1205.findL120m01eByMainId(mainId);
			if (!list.isEmpty()) {
				for (L120M01E l120m01e : list) {
					if ("5".equals(Util.trim(l120m01e.getDocType()))) {
						if (!Util.trim(l120m01e.getDocDscr()).isEmpty()) {
							jsonLms1205s05Form06.put(
									"grpNo",
									Util.trim(l120m01e.getDocDscr()).substring(
											0, 4));
							jsonLms1205s05Form06.put(
									"grpName",
									Util.trim(l120m01e.getDocDscr()).substring(
											5));
							fromOtherDoc = "Y";
						}
					}
				}
			}
			if (Util.isEmpty(jsonLms1205s05Form06.getString("grpNo"))) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSS07Panel.class);
				if (l120s05a != null) {
					// 若資料已存在就刪除
					service1205.delete(l120s05a);
				}
				// 最新集團企業授信往來情形為空，請至"相關文件"引進！
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", pop.getProperty("L1205S07.error11")),
						getClass());
			}
			C140SDSC c140sdsc = service1205.getC140SDSCByMainPidTab(mainId,
					mainId, "91", "gcom_SrcDate");
			if (c140sdsc != null) {
				jsonLms1205s05Form06.put("endDscr",
						Util.trim(c140sdsc.getVal()));
			}

			// 計算金額及餘額
			List<C140S09A> listC140s09a = service1205.getC140S09A(mainId,
					mainId);
			BigDecimal totGabkAmt = new BigDecimal("0");
			BigDecimal totGabkBal = new BigDecimal("0");
			for (C140S09A model : listC140s09a) {
				if (!Util.isEmpty(model.getGAbkAmt())) {
					totGabkAmt = totGabkAmt.add(model.getGAbkAmt());
				}
				if (!Util.isEmpty(model.getGAbkAmt())) {
					totGabkBal = totGabkBal.add(model.getGAbkBal());
				}
			}

			jsonLms1205s05Form06.put("fcltAmt",
					Util.trim(Util.nullToSpace(totGabkAmt.toString())));
			jsonLms1205s05Form06.put("lnAmt",
					Util.trim(Util.nullToSpace(totGabkBal.toString())));
		}
		// 處理八.1之該集團之淨值,營收及八.3:集團評等及八.4 之相關欄位(OK)
		// **************************海外與國內差異
		// params回傳不同******************************************
		// JSONArray jr = JSONArray.fromObject(params.get("showBorrowData"));
		// JSONObject sbjson = jr.getJSONObject(0);
		// String caseBrId = sbjson.optString("caseBrId");
		// String caseBrId=params.getString("caseBrId");
		String caseBrId = MegaSSOSecurityContext.getUnitNo();
		jsonLms1205s05Form06.put("gfnDb2GetRptGroupData_Parm_cesMainId",
				Util.nullToSpace(cesMainId));
		jsonLms1205s05Form06.put("gfnDb2GetRptGroupData_Parm_caseBrID",
				Util.nullToSpace(caseBrId));
		jsonLms1205s05Form06.put("gfnDb2GetRptGroupData_Parm_fromOtherDoc",
				Util.nullToSpace(fromOtherDoc));
		// ****************************************************************************************
		service1205.gfnDb2GetRptGroupData(jsonLms1205s05Form06);
		List<L120S05B> l120s05b = service1205.findL120s05bByMainId(mainId);
		if (l120s05b.isEmpty()) {
			l120s05b = new ArrayList<L120S05B>();
		} else {
			// 刪除L120S05B然後新增
			service1205.deleteListL120s05b(l120s05b);
			l120s05b = new ArrayList<L120S05B>();
		}
		l120s05b = service1205.findL120s05b1(jsonLms1205s05Form06);
		l120s05a = service1205.saveAndQueryListL120s05b(l120s05b, l120s05a,
				jsonLms1205s05Form06);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205S05Panel.class);
		CapAjaxFormResult myFormResult = DataParse.toResult(l120s05a);
		if (l120s05a.getFcltAmt() == null) {
			myFormResult.set("fcltAmt", "N.A.");
		}
		if (l120s05a.getLnAmt() == null) {
			myFormResult.set("lnAmt", "N.A.");
		}
		if (l120s05a.getEndDate() == null) {
			myFormResult.set("endDate", "N.A.");
		}
		if (!l120s05b.isEmpty()) {
			int tIndex = 1;
			if (l120s05b.size() <= 1) {
				tIndex = 0;
			}
			myFormResult.set("lnDate", CapDate.formatDate(l120s05b.get(tIndex)
					.getLnDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpQDate", CapDate.formatDate(l120s05b.get(tIndex)
					.getGpQDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpComDate", CapDate.formatDate(
					l120s05b.get(tIndex).getGpComDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpRiskDate", CapDate.formatDate(
					l120s05b.get(tIndex).getGpRiskDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
		}

		// J-105-0167-001 Web e-Loan
		// 企金授信案件簽報書第八之3增列集團企業(應予注意集團)有關集團評等之「財務警訊項目資訊」。
		myFormResult.set("grpFinAlertStr",
				lmsService.buildGrpFinAlertStr(l120s05a.getGrpFinAlert()));

		if ("Y".equals(l120s05a.getGrpOver())) {
			myFormResult.set("grpOver", pop.getProperty("l120s05.other36"));
		} else {
			myFormResult.set("grpOver", pop.getProperty("l120s05.other35"));
		}
		if (!Util.isEmpty(l120s05a.getTotMega())) {
			if (l120s05a.getTotMega() == 0) {
				myFormResult.set("totMega", "0.00");
			} else {
				myFormResult.set("totMega", add2Zero(l120s05a.getTotMega()));
			}
		}
		if (!Util.isEmpty(l120s05a.getCrdMega())) {
			if (l120s05a.getCrdMega() == 0) {
				myFormResult.set("crdMega", "0.00");
			} else {
				myFormResult.set("crdMega", add2Zero(l120s05a.getCrdMega()));
			}
		}
		if (!Util.isEmpty(l120s05a.getLntMega())) {
			if (l120s05a.getLntMega() == 0) {
				myFormResult.set("lntMega", "0.00");
			} else {
				myFormResult.set("lntMega", add2Zero(l120s05a.getLntMega()));
			}
		}
		if (!Util.isEmpty(l120s05a.getLncMega())) {
			if (l120s05a.getLncMega() == 0) {
				myFormResult.set("lncMega", "0.00");
			} else {
				myFormResult.set("lncMega", add2Zero(l120s05a.getLncMega()));
			}
		}
		if (!Util.isEmpty(l120s05a.getExcMega())) {
			if (l120s05a.getExcMega() == 0) {
				myFormResult.set("excMega", "0.00");
			} else {
				myFormResult.set("excMega", add2Zero(l120s05a.getExcMega()));
			}
		}
		if (!Util.isEmpty(l120s05a.getSumMega())) {
			if (l120s05a.getSumMega() == 0) {
				myFormResult.set("sumMega", "0.00");
			} else {
				myFormResult.set("sumMega", add2Zero(l120s05a.getSumMega()));
			}
		}
		if (!Util.isEmpty(l120s05a.getOthMega())) {
			if (l120s05a.getOthMega() == 0) {
				myFormResult.set("othMega", "0.00");
			} else {
				myFormResult.set("othMega", add2Zero(l120s05a.getOthMega()));
			}
		}
		if (!Util.isEmpty(l120s05a.getFinMega())) {
			if (l120s05a.getFinMega() == 0) {
				myFormResult.set("finMega", "0.00");
			} else {
				myFormResult.set("finMega", add2Zero(l120s05a.getFinMega()));
			}
		}
		if (!Util.isEmpty(l120s05a.getLmtMega())) {
			if (l120s05a.getLmtMega() == 0) {
				myFormResult.set("lmtMega", "0.00");
			} else {
				myFormResult.set("lmtMega", add2Zero(l120s05a.getLmtMega()));
			}
		}
		if (!Util.isEmpty(l120s05a.getGcrdMega())) {
			if (l120s05a.getGcrdMega() == 0) {
				myFormResult.set("gcrdMega", "0.00");
			} else {
				myFormResult.set("gcrdMega", add2Zero(l120s05a.getGcrdMega()));
			}
		}
		if (!Util.isEmpty(l120s05a.getRskMega())) {
			if (l120s05a.getRskMega() == 0) {
				myFormResult.set("rskMega", "0.00");
			} else {
				myFormResult.set("rskMega", add2Zero(l120s05a.getRskMega()));
			}
		}

		// BGN J-105-0017-001 Web e-Loan企金授信授權外簽報書第八章增加本行買入集團企業無擔保債券有效額度與餘額。
		if (!Util.isEmpty(l120s05a.getBondFactMega())) {
			if (l120s05a.getBondFactMega() == 0) {
				myFormResult.set("bondFactMega", "0.00");
			} else {
				myFormResult.set("bondFactMega",
						add2Zero(l120s05a.getBondFactMega()));
			}
		}

		if (!Util.isEmpty(l120s05a.getBondBalMega())) {
			if (l120s05a.getBondBalMega() == 0) {
				myFormResult.set("bondBalMega", "0.00");
			} else {
				myFormResult.set("bondBalMega",
						add2Zero(l120s05a.getBondBalMega()));
			}
		}
		// END J-105-0017-001 Web e-Loan企金授信授權外簽報書第八章增加本行買入集團企業無擔保債券有效額度與餘額。

		// BGN J-107-0395_05097_B1001 Web
		// e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍
		if (!Util.isEmpty(l120s05a.getBdBalMega())) {
			if (BigDecimal.ZERO.compareTo(l120s05a.getBdBalMega()) == 0) {
				myFormResult.set("bdBalMega", "0.00");
			} else {
				myFormResult.set("bdBalMega", add2Zero(l120s05a.getBdBalMega()
						.doubleValue()));
			}
		}

		if (!Util.isEmpty(l120s05a.getBdBalNMega())) {
			if (BigDecimal.ZERO.compareTo(l120s05a.getBdBalNMega()) == 0) {
				myFormResult.set("bdBalNMega", "0.00");
			} else {
				myFormResult.set("bdBalNMega", add2Zero(l120s05a
						.getBdBalNMega().doubleValue()));
			}
		}
		// END J-107-0395_05097_B1001 Web
		// e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍

		// J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
		DecimalFormat dfGrpRate = new DecimalFormat("#0.0000");
		String[] titlesGrpRate = new String[] { "avgTwdRt", "avgUsdRt",
				"avgTwdSRt", "avgUsdSRt", "avgTwdNRt", "avgUsdNRt",
				"maxTwdSRt", "maxUsdSRt", "maxTwdNRt", "maxUsdNRt",
				"minTwdSRt", "minUsdSRt", "minTwdNRt", "minUsdNRt" };
		for (String title : titlesGrpRate) {
			Object value = l120s05a.get(title);
			if (!Util.isEmpty(value)) {
				myFormResult.set(title, dfGrpRate.format(value));
			} else {
				myFormResult.set(title, "");
			}
		}

		// J-107-0087-001 Web
		// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
		boolean newGrpGrade = lmsService.isNewGrpGrade(
				Util.trim(l120s05a.getGrpYear()), true);
		String defultNoGrade = lmsService.getGrpNoGrade(newGrpGrade);

		// J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
		if (!lmsService.isShowGrpRateData(l120s05a)) {
			// 尚無「集團企業代號」者或集團企業註記「財務危機集團企業」者
			myFormResult.set("showGrpRate", "N");
		} else {
			myFormResult.set("showGrpRate", "Y");

			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			if (Util.equals(l120s05a.getGrpGrade(), defultNoGrade)) {
				if (Util.equals(l120s05a.getBadFg(), "0")
						|| Util.equals(l120s05a.getBadFg(), "")) {
					// l120s05a.grpGradeMemo_2=(為新戶或未評等)
					myFormResult.set("grpGradeMemo",
							pop1205s06a.getProperty("l120s05a.grpGradeMemo_2"));
				} else {
					// l120s05a.grpGradeMemo_3=(為新戶或未評等，惟列管應予注意集團企業者）
					myFormResult.set("grpGradeMemo",
							pop1205s06a.getProperty("l120s05a.grpGradeMemo_3"));
				}
			} else {
				if (Util.equals(l120s05a.getBadFg(), "0")
						|| Util.equals(l120s05a.getBadFg(), "")) {
					// l120s05a.grpGradeMemo_2=(為新戶或未評等)
					myFormResult.set("grpGradeMemo", "");
				} else {
					// l120s05a.grpGradeMemo_1=(且列管應予注意集團企業者）
					myFormResult.set("grpGradeMemo",
							pop1205s06a.getProperty("l120s05a.grpGradeMemo_1"));
				}
			}
		}

		myFormResult.set("grpFlag",
				Util.isEmpty(Util.trim(l120s05a.getGrpNo())) ? "N" : "Y");

		// J-105-0159-001 Web e-Loan企金授信授權外簽報書說明八，修改一律顯示本行對該集團授信限額與無擔保授信額度限額。
		// if (!pop.getProperty("l120s05a.no2").equals(l120s05a.getGrpDscr())) {
		// myFormResult.set("_hLmtAmt", true);
		// myFormResult.set("_hGcrdAmt", true);
		// } else {
		// myFormResult.set("_hLmtAmt", false);
		// myFormResult.set("_hGcrdAmt", false);
		// }
		myFormResult.set("_hLmtAmt", false);
		myFormResult.set("_hGcrdAmt", false);

		if ("1".equals(l120s05a.getGrpSrc())) {
			myFormResult.set("grpSrc", pop.getProperty("l120s05.data1"));
		} else if ("2".equals(l120s05a.getGrpSrc())) {
			myFormResult.set("grpSrc", pop.getProperty("l120s05.data2"));
		} else {
			myFormResult.set("grpSrc", "");
		}

		// 民國轉西元年
		myFormResult
				.set("endYear",
						(Util.trim(Util.nullToSpace(l120s05a.getEndYear()))
								.length() == 4) ? l120s05a.getEndYear()
								: ((Util.trim(Util.nullToSpace(l120s05a
										.getEndYear()))).length() == 0) ? null
										: Util.parseInt(l120s05a.getEndYear()) + 1911);

		myFormResult.set("grpGrrd",
				Util.trim(jsonLms1205s05Form06.getString("grpGrrd")));

		// J-107-0087-001 Web
		// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
		if (newGrpGrade) {
			// (大A)、(中B)....
			myFormResult.set(
					"grpSizeLvlShow",
					lmsService.getGrpSizeLvlShow(
							Util.trim(l120s05a.getGrpSize()),
							Util.trim(l120s05a.getGrpLevel())));
		} else {
			myFormResult.set("grpSizeLvlShow", "");
		}

		result.set("LMS1205S05Form06", myFormResult);
		if (JSONObject.fromObject(myFormResult.getResult()).isEmpty()) {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		} else {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}
		return result;
	}

	/**
	 * 刪除集團企業資料
	 * 
	 * @param mainId
	 */
	@SuppressWarnings("unused")
	private void delGrp(String mainId) {
		L120S05A l120s05a = service1205.findL120s05aByMainId(mainId);
		List<L120S05B> list = service1205.findL120s05bByMainId(mainId);
		if (l120s05a != null) {
			// 若資料已存在就刪除
			service1205.delete(l120s05a);
		}
		if (!list.isEmpty()) {
			// 刪除L120S05B然後新增
			service1205.deleteListL120s05b(list);
		}
	}

	/**
	 * 刪除關係企業資料
	 * 
	 * @param mainId
	 */
	@SuppressWarnings("unused")
	private void delRlt(String mainId) {
		L120S05C l120s05c = service1205.findL120s05cByMainId(mainId);
		List<L120S05D> list = service1205.findL120s05dByMainId(mainId);
		if (l120s05c != null) {
			// 若資料已存在就刪除
			service1205.delete(l120s05c);
		}
		if (!list.isEmpty()) {
			// 刪除L120S05B然後新增
			service1205.deleteListL120s05d(list);
		}
	}

	/**
	 * 利用JDBC取資料(關係企業)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	public IResult getJdbcRelData(PageParameters params)
			throws CapException {
		IBranch iBranch = branch.getBranch(MegaSSOSecurityContext.getUnitNo());
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// 建立相關聯的資料表
		L120S05C l120s05c = service1205.findL120s05cByMainId(mainId);
		if (l120s05c == null) {
			// 無資料
			// 建立新資料表
			l120s05c = new L120S05C();
		} else {
			// 刪除L120S05A然後新增
			service1205.delete(l120s05c);
			l120s05c = new L120S05C();
		}
		String formLms1205s0507 = params.getString("LMS1205S05Form07");
		JSONObject jsonFormLms1205s0507 = new JSONObject();
		if (!Util.isEmpty(formLms1205s0507)) {
			jsonFormLms1205s0507 = JSONObject.fromObject(formLms1205s0507);
		}
		jsonFormLms1205s0507.put(EloanConstants.MAIN_ID, mainId);
		jsonFormLms1205s0507.put("creator", user.getUserId());
		jsonFormLms1205s0507.put("createTime", new BranchDateTimeFormatter(
				iBranch).reformat(CapDate.parseToString(CapDate
				.getCurrentTimestamp())));
		DataParse.toBean(jsonFormLms1205s0507, l120s05c);

		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		jsonFormLms1205s0507.put("custId", l120m01a.getCustId());
		jsonFormLms1205s0507.put("dupNo", l120m01a.getDupNo());
		jsonFormLms1205s0507.put("custName",
				Util.toFullCharString(l120m01a.getCustName()));

		List<L120S05D> l120s05d = service1205.findL120s05dByMainId(mainId);
		if (l120s05d == null) {
			l120s05d = new ArrayList<L120S05D>();
		} else {
			// 刪除L120S05D然後新增
			service1205.deleteListL120s05d(l120s05d);
			l120s05d = new ArrayList<L120S05D>();
		}
		try {
			l120s05d = service1205.findL120s05d1(jsonFormLms1205s0507);
		} catch (Exception e) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0048"), getClass());
		}

		l120s05c = service1205.saveAndQueryListL120s05d(l120s05d, l120s05c,
				jsonFormLms1205s0507);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205S05Panel.class);
		CapAjaxFormResult myFormResult = DataParse.toResult(l120s05c);
		if (!l120s05d.isEmpty()) {
			myFormResult.set("lnDate", CapDate.formatDate(l120s05d.get(0)
					.getLnDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpQDate", CapDate.formatDate(l120s05d.get(0)
					.getGpQDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpComDate", CapDate.formatDate(l120s05d.get(0)
					.getGpComDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpRiskDate", CapDate.formatDate(l120s05d.get(0)
					.getGpRiskDate(), UtilConstants.DateFormat.YYYY_MM_DD));
		}
		if (l120s05c != null) {
			if ("Y".equals(l120s05c.getRltOver())) {
				myFormResult.set("rltOver", pop.getProperty("l120s05.other36"));
			} else {
				myFormResult.set("rltOver", pop.getProperty("l120s05.other35"));
			}
			if (!Util.isEmpty(l120s05c.getTotMega())) {
				if (l120s05c.getTotMega() == 0) {
					myFormResult.set("totMega", "0.00");
				} else {
					myFormResult
							.set("totMega", add2Zero(l120s05c.getTotMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getCrdMega())) {
				if (l120s05c.getCrdMega() == 0) {
					myFormResult.set("crdMega", "0.00");
				} else {
					myFormResult
							.set("crdMega", add2Zero(l120s05c.getCrdMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getLntMega())) {
				if (l120s05c.getLntMega() == 0) {
					myFormResult.set("lntMega", "0.00");
				} else {
					myFormResult
							.set("lntMega", add2Zero(l120s05c.getLntMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getLncMega())) {
				if (l120s05c.getLncMega() == 0) {
					myFormResult.set("lncMega", "0.00");
				} else {
					myFormResult
							.set("lncMega", add2Zero(l120s05c.getLncMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getExcMega())) {
				if (l120s05c.getExcMega() == 0) {
					myFormResult.set("excMega", "0.00");
				} else {
					myFormResult
							.set("excMega", add2Zero(l120s05c.getExcMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getSumMega())) {
				if (l120s05c.getSumMega() == 0) {
					myFormResult.set("sumMega", "0.00");
				} else {
					myFormResult
							.set("sumMega", add2Zero(l120s05c.getSumMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getOthMega())) {
				if (l120s05c.getOthMega() == 0) {
					myFormResult.set("othMega", "0.00");
				} else {
					myFormResult
							.set("othMega", add2Zero(l120s05c.getOthMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getFinMega())) {
				if (l120s05c.getFinMega() == 0) {
					myFormResult.set("finMega", "0.00");
				} else {
					myFormResult
							.set("finMega", add2Zero(l120s05c.getFinMega()));
				}
			}
			if (!Util.isEmpty(l120s05c.getRskMega())) {
				if (l120s05c.getRskMega() == 0) {
					myFormResult.set("rskMega", "0.00");
				} else {
					myFormResult
							.set("rskMega", add2Zero(l120s05c.getRskMega()));
				}
			}

		}
		myFormResult.set("rltFlag", "Y");
		result.set("LMS1205S05Form07", myFormResult);
		if (JSONObject.fromObject(myFormResult.getResult()).isEmpty()) {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		} else {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}
		return result;
	}

	/**
	 * 利用JDBC取資料(關係企業) J-106-0110-001 Web
	 * e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	public IResult getJdbcDataRelGroup(PageParameters params)
			throws CapException {
		IBranch iBranch = branchSrv.getBranch(MegaSSOSecurityContext
				.getUnitNo());
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// 建立相關聯的資料表
		L120S05A l120s05a = service1205.findL120s05aByMainId(mainId);
		if (l120s05a == null) {
			// 無資料
			// 建立新資料表
			l120s05a = new L120S05A();
		} else {
			// 刪除L120S05A然後新增
			service1205.delete(l120s05a);
			l120s05a = new L120S05A();
		}
		String form06Lms1205S05 = params.getString("LMS1205S05Form06");
		JSONObject jsonLms1205s05Form06 = new JSONObject();
		if (!Util.isEmpty(form06Lms1205S05)) {
			jsonLms1205s05Form06 = JSONObject.fromObject(form06Lms1205S05);
		}
		jsonLms1205s05Form06.put(EloanConstants.MAIN_ID, mainId);
		jsonLms1205s05Form06.put("creator", user.getUserId());
		jsonLms1205s05Form06.put("createTime", new BranchDateTimeFormatter(
				iBranch).reformat(CapDate.parseToString(CapDate
				.getCurrentTimestamp())));
		jsonLms1205s05Form06.put("updater", user.getUserId());
		jsonLms1205s05Form06.put("updateTime", new BranchDateTimeFormatter(
				iBranch).reformat(CapDate.parseToString(CapDate
				.getCurrentTimestamp())));
		jsonLms1205s05Form06.put("grpSrc", "");
		DataParse.toBean(jsonLms1205s05Form06, l120s05a);

		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		jsonLms1205s05Form06.put("custId", l120m01a.getCustId());
		jsonLms1205s05Form06.put("dupNo", l120m01a.getDupNo());
		jsonLms1205s05Form06.put("custName",
				Util.toFullCharString(l120m01a.getCustName()));
		jsonLms1205s05Form06.put("grpNo", "");
		jsonLms1205s05Form06.put("grpName", "");

		boolean hasRelGrp = false;
		List<Map<String, Object>> rows = this.misElcrcoService
				.findElcrecomByIdDupnoWithR01R02R03ExceptSelf(
						l120m01a.getCustId(), l120m01a.getDupNo());
		if (rows != null && !rows.isEmpty()) {
			hasRelGrp = true;
		}

		List<L120S05B> l120s05b = service1205.findL120s05bByMainId(mainId);
		if (l120s05b == null) {
			l120s05b = new ArrayList<L120S05B>();
		} else {
			// 刪除L120S05D然後新增
			service1205.deleteListL120s05b(l120s05b);
			l120s05b = new ArrayList<L120S05B>();
		}

		// 抓國內+海外帳務主程式
		try {
			l120s05b = service1205.findL120s05b1_A(jsonLms1205s05Form06);
		} catch (Exception e) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0048"), getClass());
		}

		l120s05a = service1205.saveAndQueryListL120s05a_A(l120s05b, l120s05a,
				jsonLms1205s05Form06, hasRelGrp);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205S05Panel.class);
		CapAjaxFormResult myFormResult = DataParse.toResult(l120s05a);
		if (!l120s05b.isEmpty()) {
			myFormResult.set("lnDate", CapDate.formatDate(l120s05b.get(0)
					.getLnDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpQDate", CapDate.formatDate(l120s05b.get(0)
					.getGpQDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpComDate", CapDate.formatDate(l120s05b.get(0)
					.getGpComDate(), UtilConstants.DateFormat.YYYY_MM_DD));
			myFormResult.set("gpRiskDate", CapDate.formatDate(l120s05b.get(0)
					.getGpRiskDate(), UtilConstants.DateFormat.YYYY_MM_DD));
		}
		if (l120s05a != null) {
			if ("Y".equals(l120s05a.getGrpOver())) {
				myFormResult.set("grpOver", pop.getProperty("l120s05.other36"));
			} else {
				myFormResult.set("grpOver", pop.getProperty("l120s05.other35"));
			}
			if (!Util.isEmpty(l120s05a.getTotMega())) {
				if (l120s05a.getTotMega() == 0) {
					myFormResult.set("totMega", "0.00");
				} else {
					myFormResult
							.set("totMega", add2Zero(l120s05a.getTotMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getCrdMega())) {
				if (l120s05a.getCrdMega() == 0) {
					myFormResult.set("crdMega", "0.00");
				} else {
					myFormResult
							.set("crdMega", add2Zero(l120s05a.getCrdMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getLntMega())) {
				if (l120s05a.getLntMega() == 0) {
					myFormResult.set("lntMega", "0.00");
				} else {
					myFormResult
							.set("lntMega", add2Zero(l120s05a.getLntMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getLncMega())) {
				if (l120s05a.getLncMega() == 0) {
					myFormResult.set("lncMega", "0.00");
				} else {
					myFormResult
							.set("lncMega", add2Zero(l120s05a.getLncMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getExcMega())) {
				if (l120s05a.getExcMega() == 0) {
					myFormResult.set("excMega", "0.00");
				} else {
					myFormResult
							.set("excMega", add2Zero(l120s05a.getExcMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getSumMega())) {
				if (l120s05a.getSumMega() == 0) {
					myFormResult.set("sumMega", "0.00");
				} else {
					myFormResult
							.set("sumMega", add2Zero(l120s05a.getSumMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getOthMega())) {
				if (l120s05a.getOthMega() == 0) {
					myFormResult.set("othMega", "0.00");
				} else {
					myFormResult
							.set("othMega", add2Zero(l120s05a.getOthMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getFinMega())) {
				if (l120s05a.getFinMega() == 0) {
					myFormResult.set("finMega", "0.00");
				} else {
					myFormResult
							.set("finMega", add2Zero(l120s05a.getFinMega()));
				}
			}
			if (!Util.isEmpty(l120s05a.getRskMega())) {
				if (l120s05a.getRskMega() == 0) {
					myFormResult.set("rskMega", "0.00");
				} else {
					myFormResult
							.set("rskMega", add2Zero(l120s05a.getRskMega()));
				}
			}
			// myFormResult.set("grpFlag", "A");
		} else {
			myFormResult.set("grpFlag", "N");
		}

		result.set("LMS1205S05Form06", myFormResult);
		if (JSONObject.fromObject(myFormResult.getResult()).isEmpty()) {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		} else {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}
		return result;
	}

	/**
	 * 將使用者選擇集團資料代入至借款人隸屬集團
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getGrpData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String selKey = params.getString("selKey");
		String selVal = params.getString("selVal");
		CapAjaxFormResult formL120s01a = new CapAjaxFormResult();
		// setItem 把Key 和Value設顛倒了，所以在這邊Key = value, Value = key
		formL120s01a.set("groupNo", selVal);
		formL120s01a.set("groupName", selKey.substring(5));
		result.set("L120S01aForm", formL120s01a);
		return result;
	}

	/**
	 * <pre>
	 * 新增借款人主檔(企金)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addBorrowMain(PageParameters params)
			throws CapException {
		IBranch iBranch = branch.getBranch(MegaSSOSecurityContext.getUnitNo());
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String docType = Util.trim(params.getString("docType"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		String buscd = Util.trim(params.getString("buscd"));
		String renCd = Util.trim(params.getString("renCd"));
		boolean check = params.getBoolean("check");
		JSONObject addborrowJson = new JSONObject();
		addborrowJson.put("docType", docType);
		addborrowJson.put("typCd", "5");
		addborrowJson.put("custId", custId);
		addborrowJson.put("dupNo", dupNo);
		addborrowJson.put("custName", Util.truncateString(custName, 120));
		addborrowJson.put(EloanConstants.MAIN_ID, mainId);
		addborrowJson.put("creator", user.getUserId());
		addborrowJson
				.put("createTime", new BranchDateTimeFormatter(iBranch)
						.reformat(CapDate.parseToString(CapDate
								.getCurrentTimestamp())));
		JSONObject jsonCustClass = new JSONObject();
		jsonCustClass = getCustBusCDAndClass(custId, dupNo);

		addborrowJson.put("busCode", jsonCustClass.getString("busCode"));
		addborrowJson.put("bussKind", jsonCustClass.getString("bussKind"));
		addborrowJson.put("ecoNm", jsonCustClass.getString("ecoNm"));
		addborrowJson.put("ecoNm07A", jsonCustClass.getString("ecoNm07A"));
		addborrowJson.put("custClass", jsonCustClass.getString("custClass"));
		addborrowJson.put("displayBusCd",
				jsonCustClass.getString("displayBusCd"));

		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		CapAjaxFormResult showBorrowData = new CapAjaxFormResult();
		if (check) {
			addborrowJson.put("keyMan", "Y");
			// 當是主要借款人且為第一次新增時
			l120m01a.setCustId(custId);
			l120m01a.setDupNo(dupNo);
			l120m01a.setCustName(Util.truncateString(custName, 120));
			String typCd = "5";
			l120m01a.setTypCd(typCd);
			showBorrowData.set("custId", custId);
			showBorrowData.set("dupNo", dupNo);
			showBorrowData.set("custName", Util.truncateString(custName, 120));
			showBorrowData.set("typCd", getMessage("typCd." + typCd));
			// showBorrowData.set("typCd", TypCdEnum.getEnum(typCd).name());

			// J-110-0458 企金授權內其他 - 「簡易簽報」選項，適用方案「LIBOR退場變更利率條件簡易簽報」
			// 說明塞預設值
			if (lmsService.isLiborExitCase(l120m01a)) {
				Map<String, String> dscrMap = codeService.findByCodeType(
						"liborExitDscr", LMSUtil.getLocale().toString());
				L120M01D l120m01d03 = service1205.findL120m01dByUniqueKey(
						mainId, UtilConstants.Casedoc.L120m01dItemType.其他);
				if (l120m01d03 != null) {
					// 如果是空的，就可以塞值
					if (Util.isEmpty(Util.nullToSpace(l120m01d03.getItemDscr()))) {
						// 依據授信審查處中華民國XX.XX.XX總授審字第XXXXXX函辦理。
						String dscr = Util.nullToSpace(dscrMap.get("dscr"));
						l120m01d03.setItemDscr(dscr);
						service1205.save(l120m01d03);
					}
				}
			}
			if (lmsService.isEuroyenTiborExitCase(l120m01a)) {
				Map<String, String> dscrMap = codeService.findByCodeType(
						"EuroyenTiborExitDscr", LMSUtil.getLocale().toString());
				L120M01D l120m01d03 = service1205.findL120m01dByUniqueKey(
						mainId, UtilConstants.Casedoc.L120m01dItemType.其他);
				if (l120m01d03 != null) {
					if (Util.isEmpty(Util.nullToSpace(l120m01d03.getItemDscr()))) {
						String dscr = Util.nullToSpace(dscrMap.get("dscr"));
						l120m01d03.setItemDscr(dscr);
						service1205.save(l120m01d03);
					}
				}
			}

			result.set("showBorrowData", showBorrowData);
		} else {
			addborrowJson.put("keyMan", "N");
		}
		// 以下建立相關聯的資料表
		L120S01A l120s01a = new L120S01A();
		L120S01B l120s01b = new L120S01B();
		L120S01D l120s01d = new L120S01D();
		L120S01F l120s01f = new L120S01F();
		L120S01G l120s01g_1 = new L120S01G();
		L120S01G l120s01g_2 = new L120S01G();
		// 到此結束
		DataParse.toBean(addborrowJson, l120s01a); // 將addborrowJson data 置入
		l120s01a.setRenCd(Util.trim(renCd));
		l120s01a.setBusCode(Util.trim(buscd));
		// L120S01A
		DataParse.toBean(addborrowJson, l120s01b); // 將addborrowJson data 置入
													// L120S01B
		DataParse.toBean(addborrowJson, l120s01d); // 將addborrowJson data 置入
													// L120S01D
		DataParse.toBean(addborrowJson, l120s01f); // 將addborrowJson data 置入
													// L120S01F
		DataParse.toBean(addborrowJson, l120s01g_1); // 將addborrowJson data 置入
														// L120S01G_1
		DataParse.toBean(addborrowJson, l120s01g_2); // 將addborrowJson data 置入
														// L120S01G_2
		// 以下設定值給新建立的資料表
		l120s01g_1.setDataType("1");
		l120s01g_1.setDataDscr("");
		l120s01g_2.setDataType("2");
		l120s01g_2.setDataDscr("");
		// 再來儲存資料
		L120M01A model = service1205.findL120m01aByMainId(mainId);
		service1205.save(model, l120s01a, l120s01b, l120s01d, l120s01f,
				l120s01g_1, l120s01g_2);
		result.set(EloanConstants.OID, l120s01a.getOid());
		CapAjaxFormResult tadd = DataParse.toResult(l120s01a); // 處理新增後客戶型態顯示
		CapAjaxFormResult tadd2 = new CapAjaxFormResult(); // 處理營運概況分析評估
		CapAjaxFormResult tadd3 = new CapAjaxFormResult(); // 處理財務概況分析評估
		CapAjaxFormResult tadd4 = new CapAjaxFormResult(jsonCustClass); // 行業對象別與客戶類別
		// 依據不同的客戶型態顯示相對應結果
		tadd.set("typCd", getMessage("typCd." + l120s01a.getTypCd()));
		// tadd.set("typCd", TypCdEnum.getEnum(l120s01a.getTypCd()).name());
		tadd.set("invMDscr", "");
		tadd.set("_renCd", renCd);
		tadd.set("_buscd", buscd);
		tadd2.set("idDscr1", space);
		tadd3.set("idDscr2", space);

		tadd.add(tadd4);

		result.set("L120S01aForm", tadd); // 將model轉為回傳物件並置指定的formName
		result.set("formIdDscr1", tadd2); // 將model轉為回傳物件並置指定的formName
		result.set("formIdDscr2", tadd3); // 將model轉為回傳物件並置指定的formName
		return result;
	}

	/**
	 * <pre>
	 * 查詢(主要借款人關係)並引進『四、營運概況』、『五、財務狀況』、『七、存放款及外匯往來情形』。 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryToGetData(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String thisOid = params.getString(EloanConstants.OID);
		L120S01A l120s01a = service1205.findL120s01aByOid(thisOid);
		L120M01A l120m01a = service1205.findL120m01aByMainId(l120s01a
				.getMainId());
		L120S01B l120s01b = service1205
				.findL120s01bByUniqueKey(l120s01a.getMainId(),
						l120s01a.getCustId(), l120s01a.getDupNo());
		// 設定營運概況借款人統編
		l120m01a.setCesCustId(l120s01a.getCustId());
		// 設定營運概況重覆序號
		l120m01a.setCesDupNo(l120s01a.getDupNo());
		// 『四、營運概況』Query...
		if (!CapString.isEmpty(thisOid)) {
			L120S01G l120s01g1 = service1205.findL120s01gByUniqueKey(
					l120s01a.getMainId(), l120s01a.getCustId(),
					l120s01a.getDupNo(), "1");

			CapAjaxFormResult resultL120s01g1 = new CapAjaxFormResult();
			CapAjaxFormResult formIdDscr1 = new CapAjaxFormResult();
			if (l120s01g1 == null) {
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
			}
			// 營運獲利情形

			resultL120s01g1.set("runFlag", Util.trim(l120s01b.getRunFlag()));
			resultL120s01g1.set("runCurr", Util.trim(l120s01b.getRunCurr()));
			resultL120s01g1.set("runUnit", Util.trim(l120s01b.getRunUnit()));

			String gaapFlag = CapString.trimNull(l120s01b.getGaapFlag());
			String tradeType = CapString.trimNull(l120s01b.getTradeType());
			gaapFlag = "".equals(gaapFlag) ? " " : gaapFlag;
			tradeType = "".equals(tradeType) ? " " : tradeType;

			JSONObject l120s01eKind1Data = service1205.getL120s01eKind1Data(
					l120s01a.getMainId(), l120s01a.getCustId(),
					l120s01a.getDupNo(), gaapFlag.charAt(0),
					tradeType.charAt(0));
			resultL120s01g1.putAll(new CapAjaxFormResult(l120s01eKind1Data));

			if (l120s01g1 != null) {
				formIdDscr1.set("idDscr1",
						Util.nullToSpace(l120s01g1.getDataDscr()));
				resultL120s01g1.set("cesTypCd",
						getMessage("typCd." + Util.trim(l120s01a.getTypCd())));
				// resultL120s01g1.set("cesTypCd",
				// TypCdEnum.getEnum(Util.trim(l120s01a.getTypCd()))
				// .name());
				resultL120s01g1.set("cesCustId",
						Util.trim(l120m01a.getCesCustId()));
				resultL120s01g1.set("cesDupNo",
						Util.trim(l120m01a.getCesDupNo()));
				resultL120s01g1.set("cesCustName",
						Util.trim(l120s01a.getCustName()));
				// 當有多個model要對應form時.. 依序指定
				resultL120s01g1.set("idDscr1",
						Util.trim(l120s01g1.getDataDscr()));
				result.set("LMS1205S05Form02", resultL120s01g1);
				result.set("formIdDscr1", formIdDscr1);
			}
		}

		// 『五、財務狀況』Query...
		if (!CapString.isEmpty(thisOid)) {
			L120S01G l120s01g2 = service1205.findL120s01gByUniqueKey(
					l120s01a.getMainId(), l120s01a.getCustId(),
					l120s01a.getDupNo(), "2");

			CapAjaxFormResult resultL120s01g2 = new CapAjaxFormResult();
			CapAjaxFormResult formIdDscr2 = new CapAjaxFormResult();
			if (l120s01g2 == null) {
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
			}
			// 主要財務比率
			resultL120s01g2.set("finFlag", Util.trim(l120s01b.getFinFlag()));

			String gaapFlag = CapString.trimNull(l120s01b.getGaapFlag());
			String tradeType = CapString.trimNull(l120s01b.getTradeType());
			gaapFlag = "".equals(gaapFlag) ? " " : gaapFlag;
			tradeType = "".equals(tradeType) ? " " : tradeType;

			JSONObject l120s01eKind2Data = service1205.getL120s01eKind2Data(
					l120s01a.getMainId(), l120s01a.getCustId(),
					l120s01a.getDupNo(), gaapFlag.charAt(0),
					tradeType.charAt(0));
			resultL120s01g2.putAll(new CapAjaxFormResult(l120s01eKind2Data));

			if (l120s01g2 != null) {
				formIdDscr2.set("idDscr2",
						Util.nullToSpace(l120s01g2.getDataDscr()));
				resultL120s01g2.set("cesTypCd",
						getMessage("typCd." + Util.trim(l120s01a.getTypCd())));
				// resultL120s01g2.set("cesTypCd",
				// TypCdEnum.getEnum(Util.trim(l120s01a.getTypCd()))
				// .name());
				resultL120s01g2.set("cesCustId",
						Util.trim(l120m01a.getCesCustId()));
				resultL120s01g2.set("cesDupNo",
						Util.trim(l120m01a.getCesDupNo()));
				resultL120s01g2.set("cesCustName",
						Util.trim(l120s01a.getCustName()));
				resultL120s01g2.set("idDscr2",
						Util.trim(l120s01g2.getDataDscr()));
				// 當有多個model要對應form時.. 依序指定
				result.set("LMS1205S05Form03", resultL120s01g2);
				result.set("formIdDscr2", formIdDscr2);
			}
		}

		// 『七、存放款及外匯往來情形』Query...
		if (!CapString.isEmpty(thisOid)) {
			L120S01F l120s01f = service1205.findL120s01fByUniqueKey(
					l120s01a.getMainId(), l120s01a.getCustId(),
					l120s01a.getDupNo());
			if (l120s01f == null) {
				l120s01f = new L120S01F();
				l120s01f.setMainId(l120s01a.getMainId());
				l120s01f.setCreateTime(CapDate.getCurrentTimestamp());
				l120s01f.setCreator(user.getUserId());
			}
			SimpleDateFormat bartDateFormat = new SimpleDateFormat(
					UtilConstants.DateFormat.YYYY_MM_DD);
			String fxb = checknull(bartDateFormat, l120s01f.getFxBDate());
			String fxe = checknull(bartDateFormat, l120s01f.getFxEDate());
			String imb = checknull(bartDateFormat, l120s01f.getImBDate());
			String ime = checknull(bartDateFormat, l120s01f.getImEDate());
			String exb = checknull(bartDateFormat, l120s01f.getExBDate());
			String exe = checknull(bartDateFormat, l120s01f.getExEDate());
			String cntrb = checknull(bartDateFormat, l120s01f.getCntrBDate());
			String cntre = checknull(bartDateFormat, l120s01f.getCntrEDate());

			CapAjaxFormResult resultL120s01f = DataParse.toResult(l120s01f);

			CodeType codetype1 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("dpAvgUnit"))));
			CodeType codetype2 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("fxUnit"))));
			CodeType codetype3 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("fx2Unit"))));
			CodeType codetype4 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("imUnit"))));
			CodeType codetype5 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("im2Unit"))));
			CodeType codetype6 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("exUnit"))));
			CodeType codetype7 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("ex2Unit"))));
			CodeType codetype8 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("cntrUnit"))));
			CodeType codetype9 = codeService.findByCodeTypeAndCodeValue(
					"lms1205s01_Unit", NumConverter.delCommaString(Util
							.nullToSpace(resultL120s01f.get("nonLoanUnit"))));

			if (codetype1 != null) {
				resultL120s01f.set("dpAvgUnit",
						Util.isNumeric(Util.trim(codetype1.getCodeDesc())) ? ""
								: Util.trim(codetype1.getCodeDesc()));
			} else {
				resultL120s01f.set("dpAvgUnit", "");
			}
			if (codetype2 != null) {
				resultL120s01f.set("fxUnit",
						Util.isNumeric(Util.trim(codetype2.getCodeDesc())) ? ""
								: Util.trim(codetype2.getCodeDesc()));
			} else {
				resultL120s01f.set("fxUnit", "");
			}
			if (codetype3 != null) {
				resultL120s01f.set("fx2Unit",
						Util.isNumeric(Util.trim(codetype3.getCodeDesc())) ? ""
								: Util.trim(codetype3.getCodeDesc()));
			} else {
				resultL120s01f.set("fx2Unit", "");
			}
			if (codetype4 != null) {
				resultL120s01f.set("imUnit",
						Util.isNumeric(Util.trim(codetype4.getCodeDesc())) ? ""
								: Util.trim(codetype4.getCodeDesc()));
			} else {
				resultL120s01f.set("imUnit", "");
			}
			if (codetype5 != null) {
				resultL120s01f.set("im2Unit",
						Util.isNumeric(Util.trim(codetype5.getCodeDesc())) ? ""
								: Util.trim(codetype5.getCodeDesc()));
			} else {
				resultL120s01f.set("im2Unit", "");
			}
			if (codetype6 != null) {
				resultL120s01f.set("exUnit",
						Util.isNumeric(Util.trim(codetype6.getCodeDesc())) ? ""
								: Util.trim(codetype6.getCodeDesc()));
			} else {
				resultL120s01f.set("exUnit", "");
			}
			if (codetype7 != null) {
				resultL120s01f.set("ex2Unit",
						Util.isNumeric(Util.trim(codetype7.getCodeDesc())) ? ""
								: Util.trim(codetype7.getCodeDesc()));
			} else {
				resultL120s01f.set("ex2Unit", "");
			}
			if (codetype8 != null) {
				resultL120s01f.set("cntrUnit",
						Util.isNumeric(Util.trim(codetype8.getCodeDesc())) ? ""
								: Util.trim(codetype8.getCodeDesc()));
			} else {
				resultL120s01f.set("cntrUnit", "");
			}
			if (codetype9 != null) {
				resultL120s01f.set("nonLoanUnit",
						Util.isNumeric(Util.trim(codetype9.getCodeDesc())) ? ""
								: Util.trim(codetype9.getCodeDesc()));
			} else {
				resultL120s01f.set("nonLoanUnit", "");
			}

			resultL120s01f.set("fxYear", trimZero(NumConverter
					.delCommaString(Util.nullToSpace(resultL120s01f
							.get("fxYear")))));
			resultL120s01f.set("imYear", trimZero(NumConverter
					.delCommaString(Util.nullToSpace(resultL120s01f
							.get("imYear")))));
			resultL120s01f.set("exYear", trimZero(NumConverter
					.delCommaString(Util.nullToSpace(resultL120s01f
							.get("exYear")))));
			// 舊型用法(不活)
			translate("fxb", fxb, resultL120s01f);
			translate("fxe", fxe, resultL120s01f);
			translate("imb", imb, resultL120s01f);
			translate("ime", ime, resultL120s01f);
			translate("exb", exb, resultL120s01f);
			translate("exe", exe, resultL120s01f);
			translate("cntrb", cntrb, resultL120s01f);
			translate("cntre", cntre, resultL120s01f);

			resultL120s01f.set("cesTypCd",
					getMessage("typCd." + Util.trim(l120s01a.getTypCd())));
			// resultL120s01f.set("cesTypCd",
			// TypCdEnum.getEnum(Util.trim(l120s01a.getTypCd())).name());
			resultL120s01f.set("cesCustId", Util.trim(l120m01a.getCesCustId()));
			resultL120s01f.set("cesDupNo", Util.trim(l120m01a.getCesDupNo()));
			resultL120s01f
					.set("cesCustName", Util.trim(l120s01a.getCustName()));
			// 當有多個model要對應form時.. 依序指定
			result.set("LMS1205S05Form05", resultL120s01f);
		}
		service1205.save(l120m01a);
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}

	/**
	 * <pre>
	 * 查詢銀行法及金控法44 45條(企金)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getRlt(PageParameters params)
			throws CapException {
		return super._LMSM01FormHandler_getRlt(params);
	}

	/**
	 * 引進徵信資信簡表取得營運概況與財務狀況及存放款外匯往來情形
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getL120s01e(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String cesMainId1 = params.getString("cesMainId1");
		String cesMainId2 = params.getString("cesMainId2");
		String thisOid = params.getString("thisOid");
		String[] finItem = params.getStringArray("finItem");
		String gaapFlag = params.getString("gaapFlag", " ");
		String tradeType = params.getString("fssType", " ");

		Map<String, List<String>> map = new HashMap<String, List<String>>();
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		L120S01A l120s01a = service1205.findL120s01aByOid(thisOid);
		String custId = l120s01a.getCustId();
		String dupNo = l120s01a.getDupNo();
		L120S01B l120s01b = service1205.findL120s01bByUniqueKey(mainId, custId,
				dupNo);
		if (StringUtils.length(cesMainId1) == 32) {
			l120s01b.setGaapFlag(gaapFlag);
			l120s01b.setTradeType(tradeType);
			service1205.save(l120s01b);
		}
		gaapFlag = l120s01b.getGaapFlag();
		tradeType = l120s01b.getTradeType();

		// 如果已有資料則刪除重新引進
		if (StringUtils.length(cesMainId1) == 32) {
			eloanDbBaseService.L120S01E_delByMainIdCustData(mainId, custId,
					dupNo);
		}

		List<L120S01E> list1 = new ArrayList<L120S01E>();

		List<Map<String, Object>> ofss1Datas = eloanDbBaseService
				.findC120M01A_selCustData3(cesMainId1);

		if (StringUtils.length(cesMainId1) == 32) {
			if (ofss1Datas != null && ofss1Datas.size() > 0) {
				Map<String, Object> ofss1Data = ofss1Datas.get(0);
				String ofss1 = MapUtils.getString(ofss1Data, "ofss1", "1");
				// 0為未勾選，代表不是N.A.
				if ("0".equals(ofss1)) {
					list1 = service1205.findListL120s01e("1",
							Util.nullToSpace(mainId), Util.nullToSpace(custId),
							Util.nullToSpace(dupNo), finItem, map, cesMainId1);
				}
			}
		}

		CapAjaxFormResult resultl120s01g1 = new CapAjaxFormResult();
		CapAjaxFormResult formIdDscr1 = new CapAjaxFormResult();
		if (!list1.isEmpty()) {
		}
		// 設定營運概況分析與評估(徵信報告)
		L120S01G model1 = service1205.findL120s01gByUniqueKey(mainId, custId,
				dupNo, "1");
		if (model1 == null) {
			MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
			model1 = new L120S01G();
			model1.setMainId(mainId);
			model1.setDataType("1");
			model1.setCustId(custId);
			model1.setDupNo(dupNo);
			model1.setCreateTime(CapDate.getCurrentTimestamp());
			model1.setCreator(unit.getUserId());
		}

		if (StringUtils.isNotEmpty(cesMainId2)) {
			model1 = service1205.find120s01g(model1, cesMainId2);
			formIdDscr1.set("idDscr1", model1.getDataDscr());
		}
		// 財務狀況(資信簡表)
		List<L120S01E> list2 = new ArrayList<L120S01E>();

		if (StringUtils.length(cesMainId1) == 32) {
			if (ofss1Datas != null && ofss1Datas.size() > 0) {
				Map<String, Object> ofss1Data = ofss1Datas.get(0);
				String gfss1 = MapUtils.getString(ofss1Data, "gfss1", "1");
				if ("0".equals(gfss1)) {
					list2 = service1205.findListL120s01e("2",
							Util.nullToSpace(mainId), Util.nullToSpace(custId),
							Util.nullToSpace(dupNo), finItem, map, cesMainId1);
				}
			}
		}

		CapAjaxFormResult resultL120s01g_2 = new CapAjaxFormResult();
		CapAjaxFormResult formIdDscr2 = new CapAjaxFormResult();

		// 設定財務狀況分析與評估(徵信報告)
		L120S01G model2 = service1205.findL120s01gByUniqueKey(mainId, custId,
				dupNo, "2");
		if (model2 == null) {
			MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
			model2 = new L120S01G();
			model2.setMainId(mainId);
			model2.setDataType("2");
			model2.setCustId(custId);
			model2.setDupNo(dupNo);
			model2.setCreateTime(CapDate.getCurrentTimestamp());
			model2.setCreator(unit.getUserId());
		}
		if (StringUtils.isNotEmpty(cesMainId2)) {
			model2 = service1205.find120s01g(model2, cesMainId2);
			formIdDscr2.set("idDscr2", model2.getDataDscr());
		}

		// 存放款外匯往來情形(資信簡表)
		L120S01F model3 = service1205.findL120s01fByUniqueKey(mainId, custId,
				dupNo);
		if (model3 == null) {
			MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
			model3 = new L120S01F();
			model3.setMainId(mainId);
			model3.setCustId(custId);
			model3.setDupNo(dupNo);
			model3.setCreateTime(CapDate.getCurrentTimestamp());
			model3.setCreator(unit.getUserId());
		}
		if (StringUtils.length(cesMainId1) == 32) {
			model3 = service1205.findl120s01f(model3, cesMainId1);
		}

		CapAjaxFormResult L120S01fForm = DataParse.toResult(model3);
		L120S01fForm.set("dpAvgUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("dpAvgUnit"))));
		L120S01fForm.set("fxUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("fxUnit"))));
		L120S01fForm.set("fx2Unit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("fx2Unit"))));
		L120S01fForm.set("imUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("imUnit"))));
		L120S01fForm.set("im2Unit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("im2Unit"))));
		L120S01fForm.set("exUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("exUnit"))));
		L120S01fForm.set("ex2Unit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("ex2Unit"))));
		L120S01fForm.set("cntrUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("cntrUnit"))));
		L120S01fForm.set("nonLoanUnit", NumConverter.delCommaString(Util
				.nullToSpace(L120S01fForm.get("nonLoanUnit"))));
		L120S01fForm.set("fxYear", trimZero(NumConverter.delCommaString(Util
				.nullToSpace(model3.getFxYear()))));
		L120S01fForm.set("imYear", trimZero(NumConverter.delCommaString(Util
				.nullToSpace(model3.getImYear()))));
		L120S01fForm.set("exYear", trimZero(NumConverter.delCommaString(Util
				.nullToSpace(model3.getExYear()))));
		SimpleDateFormat bartDateFormat = new SimpleDateFormat(
				UtilConstants.DateFormat.YYYY_MM_DD);
		String fxb = checknull(bartDateFormat, model3.getFxBDate());
		String fxe = checknull(bartDateFormat, model3.getFxEDate());
		String imb = checknull(bartDateFormat, model3.getImBDate());
		String ime = checknull(bartDateFormat, model3.getImEDate());
		String exb = checknull(bartDateFormat, model3.getExBDate());
		String exe = checknull(bartDateFormat, model3.getExEDate());
		String cntrb = checknull(bartDateFormat, model3.getCntrBDate());
		String cntre = checknull(bartDateFormat, model3.getCntrEDate());
		translate("fxb", fxb, L120S01fForm); // 舊型用法(不活)
		translate("fxe", fxe, L120S01fForm);
		translate("imb", imb, L120S01fForm);
		translate("ime", ime, L120S01fForm);
		translate("exb", exb, L120S01fForm);
		translate("exe", exe, L120S01fForm);
		translate("cntrb", cntrb, L120S01fForm);
		translate("cntre", cntre, L120S01fForm);
		L120S01fForm.set("rcdFlag", ("Y".equals(l120s01b.getRcdFlag()) ? "Y"
				: "N"));
		// 進行儲存
		service1205.saveL120s01e(list1, list2, l120m01a, model1, model2,
				model3, l120s01b);
		if (params.getAsBoolean("showMsg", true)) {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}

		CapAjaxFormResult showBorrowData = new CapAjaxFormResult();

		// -------------------------------------共用--------------------------------------------------------------
		gaapFlag = "".equals(Util.trim(gaapFlag)) ? " " : gaapFlag;
		tradeType = "".equals(Util.trim(tradeType)) ? " " : tradeType;

		// ------------------------------------營運概況--------------------------------------------------------

		JSONObject l120s01eKind1Data = service1205.getL120s01eKind1Data(mainId,
				custId, dupNo, gaapFlag.charAt(0), tradeType.charAt(0));
		resultl120s01g1.putAll(new CapAjaxFormResult(l120s01eKind1Data));

		// ------------------------------------財務狀況--------------------------------------------------------

		JSONObject l120s01eKind2Data = service1205.getL120s01eKind2Data(mainId,
				custId, dupNo, gaapFlag.charAt(0), tradeType.charAt(0));
		resultL120s01g_2.putAll(new CapAjaxFormResult(l120s01eKind2Data));
		// --------------------------------------------------------------------------------------------------
		if (l120s01b != null) {
			l120s01b.setRcdFlag("Y");
			if (StringUtils.length(cesMainId1) == 32) {
				l120s01b.setRunFlag((list1.isEmpty()) ? "N" : "Y");
				l120s01b.setFinFlag((list2.isEmpty()) ? "N" : "Y");
			}

		}

		showBorrowData.set("custId", Util.trim(l120m01a.getCustId()));
		showBorrowData.set("dupNo", Util.trim(l120m01a.getDupNo()));
		resultl120s01g1.set("rcdFlag", Util.trim(l120s01b.getRcdFlag()));
		resultl120s01g1.set("runFlag", Util.trim(l120s01b.getRunFlag()));
		resultl120s01g1.set("runCurr", Util.trim(l120s01b.getRunCurr()));
		resultl120s01g1.set("runUnit", Util.trim(l120s01b.getRunUnit()));

		resultL120s01g_2.set("finFlag", Util.trim(l120s01b.getFinFlag()));
		result.set("L120S01gForm_1", resultl120s01g1);
		result.set("formIdDscr1", formIdDscr1);
		result.set("L120S01gForm_2", resultL120s01g_2);
		result.set("formIdDscr2", formIdDscr2);
		result.set("L120S01fForm", L120S01fForm); // 當有多個model要對應form時..
		result.set("showBorrowData", showBorrowData);

		CapAjaxFormResult l120S01aForm = new CapAjaxFormResult();
		// 因為選的gaap/ifrs，行業別資料可能會變，所以需將資料再回傳頁面
		l120S01aForm.set("gaapFlag", l120s01b.getGaapFlag());
		l120S01aForm.set("tradeType", l120s01b.getTradeType());
		result.set("L120S01aForm", l120S01aForm);

		if (Util.isNotEmpty(cesMainId1)) {
			// 資信簡表
			params.put("cesMainId", cesMainId1);
		} else {
			params.put("cesMainId", UtilConstants.Mark.SPACE);
		}
		params.put("custId", custId);
		params.put("dupNo", dupNo);
		findRelate2(params);
		if (Util.isNotEmpty(cesMainId2)) {
			// 徵信報告
			params.put("cesMainId", cesMainId2);
		} else {
			params.put("cesMainId", UtilConstants.Mark.SPACE);
		}
		params.put("custId", custId);
		params.put("dupNo", dupNo);
		findRelate1(params);
		// 依序指定
		return result;
	}// ;

	/**
	 * <pre>
	 * 儲存-企金(全部借款人內容)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveBorrow(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult showBorrowData = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);

		boolean rcdFlag = params.getBoolean("rcdFlag");
		boolean runFlag = params.getBoolean("runFlag");
		boolean finFlag = params.getBoolean("finFlag");
		String invMDscr = params.getString("invMDscr");
		// J-109-0370 相關評估改版
		String prodMkt = Util.trim(params.getString("prodMkt"));
		boolean isPrint = params.getAsBoolean("isPrint", true);
		// 借款人主檔 及企金基本資料檔 的前端資料
		String formL120s01a = params.getString("L120S01aForm");
		L120S01A l120s01a = service1205.findL120s01aByOid(oid);

		String errMsgs[] = params.getStringArray("errMsg");
		StringBuilder errorMsg = new StringBuilder();
		errorMsg.setLength(0);
		// 串前端傳來的錯誤訊息
		if (errMsgs.length > 0) {
			for (String errMsg : errMsgs) {
				errorMsg.append(
						(errorMsg.length() > 0) ? "<br/>"
								: UtilConstants.Mark.SPACE).append(errMsg);
			}
		}

		super._LMSM01FormHandler_setL120s01a(l120s01a, formL120s01a);
		// ============================L120S01A資料到此就定位======================================
		L120S01B l120s01b = service1205
				.findL120s01bByUniqueKey(l120s01a.getMainId(),
						l120s01a.getCustId(), l120s01a.getDupNo());
		if (l120s01b == null) {
			l120s01b = new L120S01B();
			l120s01b.setMainId(l120s01a.getMainId());
			l120s01b.setCustId(l120s01a.getCustId());
			l120s01b.setDupNo(l120s01a.getDupNo());
		}
		// 將L120S01aForm data 置入L120S01B Model
		DataParse.toBean(formL120s01a, l120s01b);
		l120s01b.setAprCurr("TWD");

		if ("Y".equals(l120s01a.getKeyMan())) {
			// 如果為主要借款人
			if ("1".equals(l120s01b.getInvMFlag())) {
				// 若有赴大陸投資
				if (!BeanValidator.isValid(l120s01b, Check.class)) {
					errorMsg.append(BeanValidator.getValidMsg(l120s01b,
							LMSS02Page.class, Check.class));
				}
				if (!Util.isEmpty(l120s01b.getAprCurr())
						&& !Util.isEmpty(l120s01b.getInvMAmt())
						&& !Util.isEmpty(l120s01b.getInvMCurr())
						&& !Util.isEmpty(l120s01b.getAprAmt())) {
					// 如果赴大陸投資金額和經濟部投審金額皆有輸入
					l120s01a.setChkYN("Y");
				} else {
					l120s01a.setChkYN("N");
				}
			} else {
				// 若無赴大陸投資
				l120s01a.setChkYN("Y");
			}
			if (!BeanValidator.isValid(l120s01b, Check2.class)) {
				errorMsg.append(BeanValidator.getValidMsg(l120s01b,
						LMSS02Page.class, Check2.class));
				l120s01a.setChkYN("N");
			}
		} else {
			// 如果不是主要借款人則檢查相關身份與主要借款人關係是否不為空
			// J-110-0458 企金授權內其他 -
			// 「簡易簽報」選項，適用方案「LIBOR退場變更利率條件簡易簽報」，可能毫無相關，故拿掉此檢核
			L120M01A l120m01a = service1205.findL120m01aByMainId(l120s01a
					.getMainId());
			if (l120m01a != null) {
				if (!(lmsService.isLiborExitCase(l120m01a) || lmsService.isEuroyenTiborExitCase(l120m01a))) {
					if (!BeanValidator.isValid(l120s01a, Check3.class)) {
						errorMsg.append(BeanValidator.getValidMsg(l120s01a,
								LMSS02Page.class, Check3.class));
					}
					if (!Util.isEmpty(l120s01a.getCustPos())
							&& !Util.isEmpty(l120s01a.getCustRlt())) {
						if ("1".equals(l120s01b.getInvMFlag())) {
							// 若有赴大陸投資
							if (!BeanValidator.isValid(l120s01b, Check.class)) {
								errorMsg.append(BeanValidator
										.getValidMsg(l120s01b,
												LMSS02Page.class, Check.class));
							}
							if (!Util.isEmpty(l120s01b.getAprCurr())
									&& !Util.isEmpty(l120s01b.getInvMAmt())
									&& !Util.isEmpty(l120s01b.getInvMCurr())
									&& !Util.isEmpty(l120s01b.getAprAmt())) {
								// 如果赴大陸投資金額和經濟部投審金額皆有輸入
								l120s01a.setChkYN("Y");
							} else {
								l120s01a.setChkYN("N");
							}
						} else {
							// 若無赴大陸投資
							l120s01a.setChkYN("Y");
						}
					} else {
						l120s01a.setChkYN("N");
					}
				} else {
					// LIBOR退場 不檢查大陸投資跟與主要借款人關係
					l120s01a.setChkYN(UtilConstants.DEFAULT.是);
				}
			}
			if (!BeanValidator.isValid(l120s01b, Check2.class)) {
				errorMsg.append(BeanValidator.getValidMsg(l120s01b,
						LMSS02Page.class, Check2.class));
				l120s01a.setChkYN("N");
			}
		}
		l120s01b.setRcdFlag((rcdFlag ? "Y" : "N"));
		l120s01b.setRunFlag((runFlag ? "Y" : "N"));
		l120s01b.setFinFlag((finFlag ? "Y" : "N"));
		l120s01b.setInvMDscr(Util.truncateString(Util.trim(invMDscr), 1536));
		l120s01b.setIsPrint(isPrint ? "Y" : "N");
		l120s01b.setProdMkt(Util.trim(prodMkt));
		super._LMSM01FormHandler_setL120s01b_ChkYN(l120s01a, l120s01b, errorMsg);
		// ============================L120S01B資料到此就定位======================================
		L120S01D l120s01d = service1205
				.findL120s01dByUniqueKey(l120s01a.getMainId(),
						l120s01a.getCustId(), l120s01a.getDupNo());
		if (l120s01d == null) {
			l120s01d = new L120S01D();
			l120s01d.setMainId(l120s01a.getMainId());
			l120s01d.setCustId(l120s01a.getCustId());
			l120s01d.setDupNo(l120s01a.getDupNo());
		}
		// 將L120S01aForm data 置入L120S01D Model
		DataParse.toBean(formL120s01a, l120s01d);
		// ===================L120S01D資料到此就定位=======================
		// 企金分析與評估檔(part1) 的前端資料
		String form1L120s01g = params.getString("formIdDscr1");
		JSONObject jsontest = JSONObject.fromObject(form1L120s01g);
		L120S01G l120s01g1 = service1205.findL120s01gByUniqueKey(
				l120s01a.getMainId(), l120s01a.getCustId(),
				l120s01a.getDupNo(), "1");
		if (l120s01g1 == null) {
			l120s01g1 = new L120S01G();
			l120s01g1.setMainId(l120s01a.getMainId());
			l120s01g1.setCustId(l120s01a.getCustId());
			l120s01g1.setDupNo(l120s01a.getDupNo());
			l120s01g1.setDataType("1");
		}
		// 設定使用者所輸入Ckeditor的資料
		l120s01g1.setDataDscr(Util.nullToSpace(jsontest.get("idDscr1")));
		// l120s01g1.setDataDscr(params.getString("idDscr1"));
		// ====================L120S01G資料到此就定位======================
		// 企金分析與評估檔(part2) 的前端資料
		String form2L120s01g = params.getString("formIdDscr2");
		JSONObject jsontest2 = JSONObject.fromObject(form2L120s01g);
		L120S01G l120s01g2 = service1205.findL120s01gByUniqueKey(
				l120s01a.getMainId(), l120s01a.getCustId(),
				l120s01a.getDupNo(), "2");
		if (l120s01g2 == null) {
			l120s01g2 = new L120S01G();
			l120s01g2.setMainId(l120s01a.getMainId());
			l120s01g2.setCustId(l120s01a.getCustId());
			l120s01g2.setDupNo(l120s01a.getDupNo());
			l120s01g2.setDataType("2");
		}
		// 設定使用者所輸入Ckeditor的資料
		l120s01g2.setDataDscr(Util.nullToSpace(jsontest2.get("idDscr2")));
		// l120s01g2.setDataDscr(params.getString("idDscr2"));
		// ====================L120S01G資料到此就定位======================
		// 企金存放款外匯往來檔 的前端資料
		String formL120s01f = params.getString("L120S01fForm");
		L120S01F l120s01f = service1205
				.findL120s01fByUniqueKey(l120s01a.getMainId(),
						l120s01a.getCustId(), l120s01a.getDupNo());
		if (l120s01f == null) {
			l120s01f = new L120S01F();
			l120s01f.setMainId(l120s01a.getMainId());
			l120s01f.setCustId(l120s01a.getCustId());
			l120s01f.setDupNo(l120s01a.getDupNo());
		}
		// 將L120S01fForm data 置入 L120S01F Model
		DataParse.toBean(formL120s01f, l120s01f);
		if (!Util.isEmpty(Util.nullToSpace(l120s01f.getAvgURate()))) {
			if (l120s01f.getAvgURate() <= 999.99 && l120s01f.getAvgURate() >= 0) {
				l120s01f.setAvgURate(l120s01f.getAvgURate());
			} else {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSS02Panel.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", pop.getProperty("l120s02.alert12")),
						getClass());
			}
		}

		// 配合徵信資信簡表平均存款可以打N.A.
		JSONObject jsonObj = new JSONObject();
		if (!Util.isEmpty(formL120s01f)) {
			jsonObj = JSONObject.fromObject(formL120s01f);
		}

		if ("N.A.".equals(Util.nullToSpace(jsonObj.get("dpAvgAmt")))) {
			l120s01f.setDpAvgAmt(null);
		}

		// 將自定義的欄位組合好塞到資料表對應欄位裡
		l120s01f.setFxBDate(checkSpace(params.getString("fxbDateY"),
				checkLength(params.getString("fxbDateM"))));
		l120s01f.setFxEDate(checkSpace(params.getString("fxeDateY"),
				checkLength(params.getString("fxeDateM"))));
		l120s01f.setImBDate(checkSpace(params.getString("imbDateY"),
				checkLength(params.getString("imbDateM"))));
		l120s01f.setImEDate(checkSpace(params.getString("imeDateY"),
				checkLength(params.getString("imeDateM"))));
		l120s01f.setExBDate(checkSpace(params.getString("exbDateY"),
				checkLength(params.getString("exbDateM"))));
		l120s01f.setExEDate(checkSpace(params.getString("exeDateY"),
				checkLength(params.getString("exeDateM"))));
		l120s01f.setCntrBDate(checkSpace(params.getString("cntrbDateY"),
				checkLength(params.getString("cntrbDateM"))));
		l120s01f.setCntrEDate(checkSpace(params.getString("cntreDateY"),
				checkLength(params.getString("cntreDateM"))));
		// ===================L120S01F資料到此就定位===========================

		if (errorMsg.length() > 0) {
			l120s01a.setChkYN(UtilConstants.DEFAULT.否);
		}

		// ===================以下開始驗證主要借款人===========================

		// 異常通報的集團改抓借款人基本資料，所以新報送的簽報書清掉原異常通報集團內容
		L130M01A l130m01a = service1205.findL130m01aByMainId(l120s01a
				.getMainId());
		if (l130m01a != null) {
			if (Util.notEquals(Util.trim(l130m01a.getGrpId()), "")
					|| Util.notEquals(Util.trim(l130m01a.getGrpName()), "")) {
				l130m01a.setGrpId("");
				l130m01a.setGrpName("");
				service1205.save(l130m01a);
			}
		}

		// 先抓出所有記錄主要借款人資料
		List<L120S01A> list = service1205.findL120s01aByMainId(l120s01a
				.getMainId());
		L120M01A l120m01a = service1205.findL120m01aByMainId(l120s01a
				.getMainId());
		// 主要營業項目設定控制
		// if (UtilConstants.Casedoc.DocKind.授權外.equals(Util.trim(l120m01a
		// .getDocKind()))) {
		// l120m01a.setItemOfBusi(JSONObject.fromObject(formL120s01a)
		// .getString("itemOfBusi"));
		// } else {
		// 授權內
		l120s01b.setBussItem(JSONObject.fromObject(formL120s01a).getString(
				"itemOfBusi"));
		// }

		// 當使用者按下儲存後(將簽報書刪除時間註記砍掉，代表此簽報書要保留。)
		l120m01a.setDeletedTime(null);
		MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
		if (Util.isEmpty(l120m01a.getCaseSeq())
				&& Util.isEmpty(l120m01a.getCaseNo())) {
			l120m01a.setCaseSeq(Integer.parseInt(number.getNumberWithMax(
					L120M01A.class, unit.getUnitNo(), null, 99999)));
			StringBuilder caseNum = new StringBuilder();
			IBranch ibranch = branch.getBranch(unit.getUnitNo());
			// Properties pop = MessageBundleScriptCreator
			// .getComponentResource(LMS1205M01Page.class);
			caseNum.append(
					Util.toFullCharString(l120m01a.getCaseYear().toString()))
					.append(Util.trim(ibranch.getNameABBR()))
					.append(UtilConstants.Field.兆)
					.append(UtilConstants.Field.授字第)
					.append(Util.toFullCharString(Util.addZeroWithValue(
							Util.trim(l120m01a.getCaseSeq()), 5)))
					.append(UtilConstants.Field.號);
			l120m01a.setCaseNo(caseNum.toString());
		}

		String needToAddOid = "";
		int count = 0;
		// 取得使用者點選的借款人主表Oid
		String oidOid = l120s01a.getOid();
		for (int i = 0; i < list.size(); i++) {
			L120S01A model = list.get(i);
			if ("Y".equals(Util.nullToSpace(model.getKeyMan()))) {
				// 找出需要被覆蓋的主要借款人資料
				if (!(Util.nullToSpace(model.getOid()).equals(oidOid))) {
					count++;
					needToAddOid = model.getOid();
				}
			}
		}

		// 當是主要借款人且為第一次新增時
		if ("Y".equals(Util.nullToSpace(params.getString("keyMan")))
				&& count == 0) {
			result.set("keyMan", "Y");
			String custId = l120s01a.getCustId();
			String dupNo = l120s01a.getDupNo();
			String custName = l120s01a.getCustName();
			String typCd = l120s01a.getTypCd();
			l120m01a.setCustId(custId);
			l120m01a.setDupNo(dupNo);
			l120m01a.setCustName(Util.truncateString(custName, 120));
			l120m01a.setTypCd("5");
			showBorrowData.set("custId", custId);
			showBorrowData.set("dupNo", dupNo);
			showBorrowData.set("custName", Util.truncateString(custName, 120));
			showBorrowData.set("typCd", getMessage("typCd." + typCd));
			// showBorrowData.set("typCd", TypCdEnum.getEnum(typCd).name());
			try {
				service1205.save(l120m01a, l120s01a, l120s01b, l120s01d,
						l120s01g1, l120s01g2, l120s01f);
			} catch (Exception e) {
				logger.error("[saveBorrow] service1205.save EXCEPTION!!", e);
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", space);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			result.set("showBorrowData", showBorrowData);
			if (errorMsg.length() > 0) {
				errorMsg.insert(0, RespMsgHelper.getMainMessage("EFD0017") + "<br/><br/>");
				result.set("errorMsg", errorMsg.toString());
			} else {
				// 印出儲存成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
			}
		}
		// 當新增主要借款人且已有主要借款人時
		else if ("Y".equals(params.getString("keyMan")) && count == 1) {
			result.set("keyMan", "Y");
			// 將使用者勾選新主要借款人的Oid傳到前端以做進一步處理
			result.set("haveKeyManOid", l120s01a.getOid());
			// 將授信簽報書OID傳到前端以新增主要借款人
			result.set("haveKeyManDocNo", l120m01a.getOid());
			// 將被覆蓋(原主要借款人)的Oid傳到前端以做進一步處理
			result.set("needtoAddOid", needToAddOid);
			// 傳到前端判定為已存在主要借款人
			result.set("ExistKeyMan", true);
			L120S01A oldL120s01a = service1205.findL120s01aByOid(needToAddOid);
			// 原主要借款人名稱
			result.set("oldBorrower", oldL120s01a.getCustName());
			// 先儲存進去之後 L120S01A Model再做處理
			try {
				service1205.save(l120m01a, l120s01a, l120s01b, l120s01d,
						l120s01g1, l120s01g2, l120s01f);
			} catch (Exception e) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", space);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			if (errorMsg.length() > 0) {
				result.set("errorMsg", errorMsg.toString());
			}

			return result;
		}
		// 使用者將原主要借款人取消時
		else if ("N".equals(params.getString("keyMan")) && count == 0) {
			l120s01a.setKeyMan("N");
			result.set("keyMan", "N");
			l120s01a.setChkYN("N");
			l120m01a.setCustId("");
			l120m01a.setDupNo("");
			l120m01a.setCustName("");
			l120m01a.setTypCd("");
			showBorrowData.set("custId", "");
			showBorrowData.set("dupNo", "");
			showBorrowData.set("custName", "");
			showBorrowData.set("typCd", "");
			result.set("ExistKeyMan", false);
			try {
				service1205.save(l120m01a, l120s01a, l120s01b, l120s01d,
						l120s01g1, l120s01g2, l120s01f);
			} catch (Exception e) {
				logger.error("[saveBorrow] service1205.save EXCEPTION!!", e);
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", space);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			result.set("showBorrowData", showBorrowData);
			if (errorMsg.length() > 0) {
				errorMsg.insert(0, RespMsgHelper.getMainMessage("EFD0017") + "<br/><br/>");
				result.set("errorMsg", errorMsg.toString());
			} else {
				// 印出儲存成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
			}
		} else {
			// 當使用者新增不是主要借款人資料時...
			l120s01a.setKeyMan("N");
			result.set("keyMan", "N");
			try {
				service1205.save(l120m01a, l120s01a, l120s01b, l120s01d,
						l120s01g1, l120s01g2, l120s01f);
			} catch (Exception e) {
				logger.error("[saveBorrow] service1205.save EXCEPTION!!", e);
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", space);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			if (errorMsg.length() > 0) {
				errorMsg.insert(
						0,
						RespMsgHelper.getMainMessage("EFD0017") + "<br/><br/>");
				result.set("errorMsg", errorMsg.toString());
			} else {
				// 印出儲存成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
			}
		}

		return result;
	}

	/**
	 * 營運概況修改欄位儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult editBorrowPage02(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String thisOid = Util.trim(params.getString("thisOid"));
		L120S01A l120s01a = service1205.findL120s01aByOid(thisOid);
		String editFPanel2 = Util.trim(params.getString("editFPanel2"));
		if (l120s01a != null) {
			String mainId = Util.trim(l120s01a.getMainId());
			String custId = Util.trim(l120s01a.getCustId());
			String dupNo = Util.trim(l120s01a.getDupNo());
			L120S01B l120s01b = service1205.findL120s01bByUniqueKey(mainId,
					custId, dupNo);
			CapAjaxFormResult L120S01gForm_1 = new CapAjaxFormResult();
			if (Util.isNotEmpty(editFPanel2)) {

				String gaapFlag = CapString.trimNull(l120s01b.getGaapFlag());
				String tradeType = CapString.trimNull(l120s01b.getTradeType());
				gaapFlag = "".equals(gaapFlag) ? " " : gaapFlag;
				tradeType = "".equals(tradeType) ? " " : tradeType;

				String[] fssItemCode = service1205.getFssItemCode(
						gaapFlag.charAt(0), tradeType.charAt(0));
				String titleCode_kind_1 = fssItemCode[0];
				String[] finGroups = titleCode_kind_1.split("\\|");

				List<L120S01E> listL120s01e = service1205
						.findListL120s01eByUniqueKey(
								UtilConstants.Casedoc.L120s01eKind.營運概況,
								mainId, custId, dupNo); // 抓出所有符合條件的資料
				List<L120S01E> listToAdd = new ArrayList<L120S01E>();
				if (!listL120s01e.isEmpty()) {
					service1205.deleteListL120s01e(listL120s01e);
				}
				JSONObject json = JSONObject.fromObject(editFPanel2);
				if (l120s01b != null) {
					l120s01b.setRunCurr(Util.trim(json.getString("_runCurr")));
					l120s01b.setRunUnit(LMSUtil.toBigDecimal(Util.trim(json
							.getString("_runUnit"))));
				}
				String[] flag = new String[] { "D", "C", "B", "A" };
				for (int k = 0; k < flag.length; k++) {
					if (json.containsKey("_finYear_" + flag[k])
							&& json.getString("_finYear_" + flag[k]).length() == 10) {
						for (int i = 0; i < finGroups.length; i++) {

							String finGroup = finGroups[i];
							String[] finItems = finGroup.split("\\^");
							L120S01E model = new L120S01E();
							model.setMainId(mainId);
							model.setCustId(custId);
							model.setDupNo(dupNo);
							model.setFinKind(UtilConstants.Casedoc.L120s01eKind.營運概況);
							model.setFinYear(Util.parseDate(json
									.getString("_finYear_" + flag[k])));
							model.setFinItem("");
							for (int j = 0; j < finItems.length; j++) {

								if (j == 0) {
									String itemValue = "_finAmt" + flag[k]
											+ (i + 1);
									Long value = NumberUtils.isNumber(json
											.getString(itemValue)) ? new Long(
											json.getString(itemValue)) : null;
									model.setFinAmtCode(finItems[j]);
									model.setFinAmt(value);
									L120S01gForm_1.set("finAmt" + flag[k]
											+ (i + 1), value == null ? "N.A."
											: String.valueOf(value));

								} else if (j == 1) {
									String itemValue = "_finRatio" + flag[k]
											+ (i + 1);
									BigDecimal value = NumberUtils
											.isNumber(json.getString(itemValue)) ? new BigDecimal(
											json.getString(itemValue)) : null;
									model.setFinRatioCode(finItems[j]);
									model.setFinRatio(value);
									L120S01gForm_1.set("finRatio" + flag[k]
											+ (i + 1), value == null ? "N.A."
											: String.valueOf(value));

								}

							}
							listToAdd.add(model);
						}

					}
				}

				service1205.saveListL120s01e(listToAdd, l120s01b);

				JSONObject l120s01eKind1Data = service1205
						.getL120s01eKind1Data(mainId, custId, dupNo,
								gaapFlag.charAt(0), tradeType.charAt(0));
				L120S01gForm_1.putAll(new CapAjaxFormResult(l120s01eKind1Data));

				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
				L120S01gForm_1.set("runCurr", Util.trim(l120s01b.getRunCurr()));
				L120S01gForm_1.set("runUnit", Util.trim(l120s01b.getRunUnit()));
				result.set("L120S01gForm_1", L120S01gForm_1);
			}
		}
		return result;
	}

	/**
	 * 財務狀況修改欄位儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult editBorrowPage03(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String thisOid = Util.trim(params.getString("thisOid"));
		L120S01A l120s01a = service1205.findL120s01aByOid(thisOid);
		String editFPanel3 = Util.trim(params.getString("finRatioData"));
		if (l120s01a != null) {
			String[] flag = new String[] { "C", "B", "A" };
			String mainId = Util.trim(l120s01a.getMainId());
			String custId = Util.trim(l120s01a.getCustId());
			String dupNo = Util.trim(l120s01a.getDupNo());

			L120S01B l120s01b = service1205.findL120s01bByUniqueKey(mainId,
					custId, dupNo);

			String gaapFlag = CapString.trimNull(l120s01b.getGaapFlag());
			String tradeType = CapString.trimNull(l120s01b.getTradeType());
			gaapFlag = "".equals(gaapFlag) ? " " : gaapFlag;
			tradeType = "".equals(tradeType) ? " " : tradeType;

			String[] fssItemCode = service1205.getFssItemCode(
					gaapFlag.charAt(0), tradeType.charAt(0));
			String titleCode_kind_2 = fssItemCode[1];
			String[] finGroups = titleCode_kind_2.split("\\|");

			CapAjaxFormResult L120S01gForm_2 = new CapAjaxFormResult();
			if (Util.isNotEmpty(editFPanel3)) {
				List<L120S01E> listL120s01e = service1205
						.findListL120s01eByUniqueKey(
								UtilConstants.Casedoc.L120s01eKind.財務狀況,
								mainId, custId, dupNo); // 抓出所有符合條件的資料
				List<L120S01E> listToAdd = new ArrayList<L120S01E>();
				if (!listL120s01e.isEmpty()) {
					service1205.deleteListL120s01e(listL120s01e);
				}
				JSONObject json = JSONObject.fromObject(editFPanel3);
				for (int k = 0; k < flag.length; k++) {
					String finRatioYear = "_finRatioYear_" + flag[k];
					if (json.containsKey(finRatioYear)
							&& json.getString(finRatioYear).length() == 10) {
						for (int i = 0; i < finGroups.length; i++) {
							String key = finGroups[i];// r11,r12
							String finRatioKey = "_finRatio_" + flag[k] + "_"
									+ key;
							if (!json.containsKey(finRatioKey)) {
								continue;
							}
							L120S01E model = new L120S01E();
							model.setMainId(mainId);
							model.setCustId(custId);
							model.setDupNo(dupNo);
							model.setFinKind(UtilConstants.Casedoc.L120s01eKind.財務狀況);
							model.setFinYear(Util.parseDate(json
									.getString(finRatioYear)));
							String value = json.optString(finRatioKey);
							BigDecimal ratioValue = NumberUtils.isNumber(value) ? new BigDecimal(
									value).setScale(2, RoundingMode.DOWN)
									: null;
							model.setFinItem("");
							model.setFinRatio(ratioValue);
							model.setFinRatioCode(key);
							model.setUpdater(user.getUserId());
							model.setUpdateTime(new Date());
							listToAdd.add(model);
						}
					}
				}
				/**
				 * String keys[] = Util.getMapKey(json); Map<String, String> map
				 * = new HashMap<String, String>(); String tempFinRatio = null;
				 * for (String key : keys) { if (key.contains("finYear")) { //
				 * 年份 if (LMSUtil.checkSubStr(key, 2)) {
				 * L120S01gForm_2.set(key.substring(2),
				 * Util.trim(json.getString(key))); } if
				 * (LMSUtil.checkSubStr(key, key.length() - 1)) {
				 * map.put("finYear_" + key.substring(key.length() - 1),
				 * Util.trim(json.getString(key))); } } else if
				 * (key.contains("Answer")) { // 比率 tempFinRatio =
				 * Util.trim(json.getString(key)); if (LMSUtil.checkSubStr(key,
				 * 2) && LMSUtil.checkSubStr(key, key.length() - 2)) {
				 * L120S01gForm_2 .set(key.substring(2), tempFinRatio +
				 * (needPercent(Util.trim(key.substring(key .length() - 2))) ?
				 * "%" : UtilConstants.Mark.SPACE)); map.put(key.substring(2),
				 * tempFinRatio); } } } if (!map.isEmpty()) { String mapKeys[] =
				 * Util.getMapKey(map); for (String mapKey : mapKeys) { if
				 * (mapKey.contains("Answer")) { // 比率 if (LMSUtil
				 * .checkSubStr(mapKey, mapKey.length() - 2) &&
				 * LMSUtil.checkSubStr(mapKey, mapKey.length() - 4,
				 * mapKey.length() - 3)) { String finItem = Util.trim(mapKey
				 * .substring(mapKey.length() - 2)); String yearKind =
				 * Util.trim(mapKey.substring( mapKey.length() - 4,
				 * mapKey.length() - 3)); String finYear = map.get("finYear_" +
				 * yearKind); String idKey = yearKind + "_" + finItem; if
				 * (Util.isNotEmpty(finYear) && Util.isNotEmpty(finItem)) {
				 * L120S01E model = new L120S01E(); model.setMainId(mainId);
				 * model.setCustId(custId); model.setDupNo(dupNo);
				 * model.setFinKind(UtilConstants.Casedoc.L120s01eKind.財務狀況);
				 * model.setFinYear(Util.parseDate(finYear));
				 * model.setFinItem(("N.A.").equals(finItem) ? null : finItem);
				 * model.setFinRatio(("N.A.").equals(map .get("Answer_" +
				 * idKey)) ? null : LMSUtil.toBigDecimal(map .get("Answer_" +
				 * idKey)));
				 * 
				 * listToAdd.add(model); } } } } }
				 */
				service1205.saveListL120s01e(listToAdd);

				JSONObject l120s01eKind2Data = service1205
						.getL120s01eKind2Data(mainId, custId, dupNo,
								gaapFlag.charAt(0), tradeType.charAt(0));
				L120S01gForm_2.putAll(new CapAjaxFormResult(l120s01eKind2Data));

				// 印出執行成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
				result.set("L120S01gForm_2", L120S01gForm_2);
			}
		}
		return result;
	}

	/**
	 * 儲存會簽內容(國金部)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContent(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120M01aForm14 = new CapAjaxFormResult();
		CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
		boolean queryArea = params.getBoolean("queryArea");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String itemDscr09 = params.getString("itemDscr09");
		String sSeaManager = trimNull(params.getString("sSeaManager"));
		String sSeaBoss = trimNull(params.getString("sSeaBoss"));
		String sSeaAoName = trimNull(params.getString("sSeaAoName"));
		String sSeaAppraiserCN = trimNull(params.getString("sSeaAppraiserCN"));
		String sAreaLeader = trimNull(params.getString("sAreaLeader"));
		String sAreaSubLeader = trimNull(params.getString("sAreaSubLeader"));
		String sAreaManager = trimNull(params.getString("sAreaManager"));
		String sAreaAppraiser = trimNull(params.getString("sAreaAppraiser"));

		if (queryArea) {
			LMS1205S01Form
					.set("areaLeader", withIdName(Util.trim(sAreaLeader)));
			LMS1205S01Form.set("areaSubLeader",
					withIdName(Util.trim(sAreaSubLeader)));
			LMS1205S01Form.set("areaManager",
					withIdName(Util.trim(sAreaManager)));
			LMS1205S01Form.set("areaAppraiser",
					withIdName(Util.trim(sAreaAppraiser)));
		} else {
			LMS1205S01Form
					.set("seaManager", withIdName(Util.trim(sSeaManager)));
			LMS1205S01Form.set("seaBoss", withIdName(Util.trim(sSeaBoss)));
			LMS1205S01Form.set("seaAoName", withIdName(Util.trim(sSeaAoName)));
			LMS1205S01Form.set("seaAppraiserCN",
					withIdName(Util.trim(sSeaAppraiserCN)));
		}

		L120M01aForm14.set("itemDscr09", itemDscr09);
		L121M01B model = service1205.findL121m01bByUniqueKey(mainId, "9");
		// List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		if (model == null) {
			model = new L121M01B();
			model.setMainId(mainId);
			model.setItemType("9");
			model.setCreateTime(CapDate.getCurrentTimestamp());
			model.setCreator(user.getUserId());
		}
		List<L120M01F> saveList = new ArrayList<L120M01F>();

		if (!Util.isEmpty(Util.trim(itemDscr09))) {
			model.setItemDscr(itemDscr09);
		} else {
			model.setItemDscr("");
		}
		try {
			service1205.saveSea(model, saveList);
		} catch (Exception e) {
			logger.error("[saveSignContent] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set("L120M01aForm14", L120M01aForm14);
		result.set("LMS1205S01Form", LMS1205S01Form);
		return result;
	}

	/**
	 * 儲存簽章欄(海外聯貸)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContentF1(PageParameters params)
			throws CapException {
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120M01aForm14 = new CapAjaxFormResult();
		CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
		boolean queryArea = params.getBoolean("queryArea");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// String itemDscr09 = params.getString("itemDscr09");
		String sSeaManager = trimNull(params.getString("sSeaManager"));
		String sSeaBoss = trimNull(params.getString("sSeaBoss"));
		String sSeaAoName = trimNull(params.getString("sSeaAoName"));
		String sSeaAppraiserCN = trimNull(params.getString("sSeaAppraiserCN"));
		String sUnitManager1 = trimNull(params.getString("sUnitManager1"));
		String sAreaLeader = trimNull(params.getString("sAreaLeader"));
		String sAreaSubLeader = trimNull(params.getString("sAreaSubLeader"));
		String sAreaManager = trimNull(params.getString("sAreaManager"));
		String sAreaAppraiser = trimNull(params.getString("sAreaAppraiser"));
		String sUnitManager3 = trimNull(params.getString("sUnitManager3"));

		if (queryArea) {
			LMS1205S01Form
					.set("areaLeader", withIdName(Util.trim(sAreaLeader)));
			LMS1205S01Form.set("areaSubLeader",
					withIdName(Util.trim(sAreaSubLeader)));
			LMS1205S01Form.set("areaManager",
					withIdName(Util.trim(sAreaManager)));
			LMS1205S01Form.set("areaAppraiser",
					withIdName(Util.trim(sAreaAppraiser)));
		} else {
			LMS1205S01Form
					.set("seaManager", withIdName(Util.trim(sSeaManager)));
			LMS1205S01Form.set("seaBoss", withIdName(Util.trim(sSeaBoss)));
			LMS1205S01Form.set("seaAoName", withIdName(Util.trim(sSeaAoName)));
			LMS1205S01Form.set("seaAppraiserCN",
					withIdName(Util.trim(sSeaAppraiserCN)));
		}

		List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		List<L120M01F> saveList = new ArrayList<L120M01F>();
		if (!list.isEmpty()) {
			for (L120M01F l120m01f : list) {
				if ("6".equals(l120m01f.getBranchType())) {
					saveList.add(l120m01f);
				}
			}
		}
		if (!saveList.isEmpty()) {
			service1205.delListL120m01f(saveList);
			saveList.clear();
		}
		// 第一次新增營運簽章欄
		if (queryArea) {
			saveList.add(addSeaL120m01f(mainId, "L6", sAreaLeader));
			saveList.add(addSeaL120m01f(mainId, "L5", sAreaSubLeader));
			saveList.add(addSeaL120m01f(mainId, "L3", sAreaManager));
			saveList.add(addSeaL120m01f(mainId, "L1", sAreaAppraiser));
			saveList.add(addSeaL120m01f(mainId, "L9", sUnitManager1));
		} else {
			saveList.add(addSeaL120m01f(mainId, "L5", sSeaManager));
			saveList.add(addSeaL120m01f(mainId, "L3", sSeaBoss));
			if (!Util.isEmpty(sSeaAoName)) {
				saveList.add(addSeaL120m01f(mainId, "L2", sSeaAoName));
			}
			saveList.add(addSeaL120m01f(mainId, "L1", sSeaAppraiserCN));
			saveList.add(addSeaL120m01f(mainId, "L9", sUnitManager3));
		}

		try {
			service1205.saveSea(null, saveList);
		} catch (Exception e) {
			logger.error("[saveSignContent] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set("L120M01aForm14", L120M01aForm14);
		result.set("LMS1205S01Form", LMS1205S01Form);
		return result;
	}

	/**
	 * 呈主管放行(海外聯貸)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendBossSea(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		L121M01B l120m01b = service1205.findL121m01bByUniqueKey(mainId, "9");
		List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		StringBuilder errorMsg = new StringBuilder();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		if (!Util.isEmpty(l120m01a.getAreaDocstatus())
				&& "025".equals(user.getUnitNo())) {
			// 國金部
			boolean haseData = checkPeo(list, UtilConstants.BRANCHTYPE.國金部_營運中心);
			if (!haseData) {
				// 拋出沒有登錄國金部主管訊息
				errorMsg.append(pop.getProperty("l120m01a.error35"))
						.append("、");
			}
		} else if (!Util.isEmpty(l120m01a.getAreaDocstatus())
				&& !"025".equals(user.getUnitNo())) {
			// 營運中心會簽
			boolean haseData = checkPeo(list, UtilConstants.BRANCHTYPE.國金部_營運中心);
			if (!haseData) {
				// 拋出沒有登錄營運中心主管訊息
				errorMsg.append(pop.getProperty("l120m01a.error34"))
						.append("、");
			}
		}

		if (errorMsg.length() > 0) {
			errorMsg.deleteCharAt(errorMsg.length() - 1).append(
					pop.getProperty("l120m01a.error16"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", errorMsg.toString()), getClass());
		}

		if (l120m01b != null) {
			if (!Util.isEmpty(l120m01b.getItemDscr())) {
				// 會簽單位放行主管
				l120m01a.setAreaApprover(user.getUserId());
				// 會簽單位經辦
				l120m01a.setAreaUpdater(l120m01b.getUpdater());
				// 會簽單位放行時間
				l120m01a.setAreaApprTime(CapDate.getCurrentTimestamp());
				// 會簽文件狀態
				l120m01a.setAreaDocstatus(CreditDocStatusEnum.營運中心_海外聯貸案_待放行
						.getCode());
			} else {
				// 尚未登錄海外聯貸案會簽意見，請登入後再執行本功能！
				throw new CapMessageException(
						pop.getProperty("l120m01a.error25"), getClass());
			}
		} else {
			// 尚未登錄海外聯貸案會簽意見，請登入後再執行本功能！
			throw new CapMessageException(pop.getProperty("l120m01a.error25"),
					getClass());
		}

		// 找出已存在放行人員準備刪除
		L120M01F delL120m01f = null;
		// 準備儲存的放行人員
		L120M01F addL120m01f = addSeaL120m01f(mainId, "L4", user.getUserId());
		if (!list.isEmpty()) {
			for (L120M01F l120m01f : list) {
				if ("6".equals(l120m01f.getBranchType())) {
					if ("L4".equals(Util.trim(l120m01f.getStaffJob()))) {
						delL120m01f = l120m01f;
						break;
					}
				}
			}
		}
		if (delL120m01f != null) {
			service1205.delete(delL120m01f);
		}
		service1205.saveSeaAndDel(l120m01a, l120m01b, addL120m01f);
		// service1205.save(l120m01a, l120m01b);
		// if (params.getAsBoolean("showMsg", true)) {
		// // 印出儲存成功訊息!
		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		// .getMainMessage(this.getComponent(), "EFD0017"));
		// }
		return result;
	}

	/**
	 * 退回會簽意見
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult backSea(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		L121M01B l121m01b = service1205.findL121m01bByUniqueKey(mainId, "9");
		if (l121m01b == null) {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS1205M01Page.class);
			// 尚未登錄海外聯貸案會簽意見，請登入後再執行本功能！
			throw new CapMessageException(pop.getProperty("l120m01a.error25"),
					getClass());
		}
		// 會簽文件狀態
		l120m01a.setAreaDocstatus(CreditDocStatusEnum.營運中心_海外聯貸案_會簽中.getCode());
		service1205.save(l120m01a);
		if (params.getAsBoolean("showMsg", true)) {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}
		return result;
	}

	/**
	 * 退會簽中OR呈已會簽(海外聯貸案用)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = false)
	public IResult returnGoBossSea(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String frag = params.getString("frag");
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		L121M01B l120m01b = service1205.findL121m01bByUniqueKey(mainId, "9");
		if (l120m01b != null) {
			if (!Util.isEmpty(l120m01b.getItemDscr())) {
				// 會簽單位放行主管
				l120m01a.setAreaApprover(user.getUserId());
				// 會簽單位經辦
				l120m01a.setAreaUpdater(l120m01b.getUpdater());
				// 會簽單位放行時間
				l120m01a.setAreaApprTime(CapDate.getCurrentTimestamp());
				// 會簽文件狀態
				if ("1".equals(frag)) {
					// 退回經辦修改
					l120m01a.setAreaDocstatus(CreditDocStatusEnum.營運中心_海外聯貸案_會簽中
							.getCode());
				} else if ("8".equals(frag)) {
					// 確認
					l120m01a.setAreaDocstatus(CreditDocStatusEnum.營運中心_海外聯貸案_已會簽
							.getCode());
				}
			}
		} else {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS1205M01Page.class);
			throw new CapMessageException(pop.getProperty("l120m01a.error25"),
					getClass());
		}
		service1205.save(l120m01a, l120m01b);
		// if (params.getAsBoolean("showMsg", true)) {
		// // 印出儲存成功訊息!
		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		// .getMainMessage(this.getComponent(), "EFD0017"));
		// }
		return result;
	}

	/**
	 * 儲存會簽內容(授管處補充說明+審查意見)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContent2(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120M01aForm13 = new CapAjaxFormResult();
		CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String itemDscr0A = params.getString("itemDscr0A");
		String itemDscr0B = params.getString("itemDscr0B");
		String sHeadLeader = trimNull(params.getString("sHeadLeader"));
		String sHeadSubLeader = trimNull(params.getString("sHeadSubLeader"));
		String sHeadReCheck = trimNull(params.getString("sHeadReCheck"));
		String sHeadAppraiser = trimNull(params.getString("sHeadAppraiser"));
		LMS1205S01Form.set("headLeader", withIdName(Util.trim(sHeadLeader)));
		LMS1205S01Form.set("headSubLeader",
				withIdName(Util.trim(sHeadSubLeader)));
		LMS1205S01Form.set("headReCheck", withIdName(Util.trim(sHeadReCheck)));
		LMS1205S01Form.set("headAppraiser",
				withIdName(Util.trim(sHeadAppraiser)));

		L120M01aForm13.set("itemDscr0A", itemDscr0A);
		L120M01aForm13.set("itemDscr0B", itemDscr0B);
		L120M01D model1 = service1205.findL120m01dByUniqueKey(mainId, "A");
		L120M01D model2 = service1205.findL120m01dByUniqueKey(mainId, "B");
		// List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		if (model1 == null) {
			model1 = new L120M01D();
			model1.setMainId(mainId);
			model1.setItemType("A");
			model1.setCreateTime(CapDate.getCurrentTimestamp());
			model1.setCreator(user.getUserId());
		}
		if (model2 == null) {
			model2 = new L120M01D();
			model2.setMainId(mainId);
			model2.setItemType("B");
			model2.setCreateTime(CapDate.getCurrentTimestamp());
			model2.setCreator(user.getUserId());
		}

		if (!Util.isEmpty(Util.trim(itemDscr0A))) {
			model1.setItemDscr(itemDscr0A);
		} else {
			model1.setItemDscr("");
		}
		if (!Util.isEmpty(Util.trim(itemDscr0B))) {
			model2.setItemDscr(itemDscr0B);
		} else {
			model2.setItemDscr("");
		}
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		try {
			// service1205.saveArea(l120m01a, model1, model2, saveList);
			service1205.save(l120m01a, model1, model2);
		} catch (Exception e) {
			logger.error("[saveSignContent2] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set("L120M01aForm13", L120M01aForm13);
		result.set("LMS1205S01Form", LMS1205S01Form);
		return result;
	}

	/**
	 * 儲存簽章欄(授管處)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignContentF2(PageParameters params)
			throws CapException {
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult L120M01aForm13 = new CapAjaxFormResult();
		CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// String itemDscr0A = params.getString("itemDscr0A");
		// String itemDscr0B = params.getString("itemDscr0B");
		String sHeadLeader = trimNull(params.getString("sHeadLeader"));
		String sHeadSubLeader = trimNull(params.getString("sHeadSubLeader"));
		String sHeadReCheck = trimNull(params.getString("sHeadReCheck"));
		String sHeadAppraiser = trimNull(params.getString("sHeadAppraiser"));
		String sUnitManager2 = trimNull(params.getString("sUnitManager2"));

		LMS1205S01Form.set("headLeader", withIdName(Util.trim(sHeadLeader)));
		LMS1205S01Form.set("headSubLeader",
				withIdName(Util.trim(sHeadSubLeader)));
		LMS1205S01Form.set("headReCheck", withIdName(Util.trim(sHeadReCheck)));
		LMS1205S01Form.set("headAppraiser",
				withIdName(Util.trim(sHeadAppraiser)));

		List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
		List<L120M01F> saveList = new ArrayList<L120M01F>();
		if (!list.isEmpty()) {
			for (L120M01F model : list) {
				if ("4".equals(model.getBranchType())) {
					saveList.add(model);
				}
			}
		}
		if (!saveList.isEmpty()) {
			service1205.delListL120m01f(saveList);
			saveList.clear();
		}
		// 第一次新增營運簽章欄
		saveList.add(addHeadL120m01f(mainId, "L6", sHeadLeader));
		saveList.add(addHeadL120m01f(mainId, "L5", sHeadSubLeader));
		saveList.add(addHeadL120m01f(mainId, "L3", sHeadReCheck));
		saveList.add(addHeadL120m01f(mainId, "L1", sHeadAppraiser));
		saveList.add(addHeadL120m01f(mainId, "L9", sUnitManager2));
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		try {
			// service1205.saveArea(l120m01a, model1, model2, saveList);
			service1205.saveArea(l120m01a, saveList);
		} catch (Exception e) {
			logger.error("[saveSignContent2] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		result.set("L120M01aForm13", L120M01aForm13);
		result.set("LMS1205S01Form", LMS1205S01Form);
		return result;
	}

	// /**
	// * 儲存會簽內容(營運中心說明及意見)
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	// public IResult saveSignContent3(PageParameters params)
	// throws CapException {
	// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// CapAjaxFormResult L120M01aForm15 = new CapAjaxFormResult();
	// CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// String itemDscr07 = params.getString("itemDscr07");
	// String itemDscr08 = params.getString("itemDscr08");
	// String sAreaLeader = trimNull(params.getString("sAreaLeader"));
	// String sAreaSubLeader = trimNull(params.getString("sAreaSubLeader"));
	// String sAreaManager = trimNull(params.getString("sAreaManager"));
	// String sAreaAppraiser = trimNull(params.getString("sAreaAppraiser"));
	// String itemTitle = params.getString("itemTitle");
	// String _itemTitle = params.getString("_itemTitle");
	//
	// if (!Util.isEmpty(sAreaLeader)) {
	// LMS1205S01Form
	// .set("areaLeader", withIdName(Util.trim(sAreaLeader)));
	// }
	// if (!Util.isEmpty(sAreaSubLeader)) {
	// LMS1205S01Form.set("areaSubLeader",
	// withIdName(Util.trim(sAreaSubLeader)));
	// }
	// if (!Util.isEmpty(sAreaManager)) {
	// LMS1205S01Form.set("areaManager",
	// withIdName(Util.trim(sAreaManager)));
	// }
	// if (!Util.isEmpty(sAreaAppraiser)) {
	// LMS1205S01Form.set("areaAppraiser",
	// withIdName(Util.trim(sAreaAppraiser)));
	// }
	//
	// L120M01aForm15.set("itemDscr07", itemDscr07);
	// L120M01aForm15.set("itemDscr08", itemDscr08);
	// L120M01D model1 = service1205.findL120m01dByUniqueKey(mainId, "7");
	// L120M01D model2 = service1205.findL120m01dByUniqueKey(mainId, "8");
	// // List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
	// if (model1 == null) {
	// model1 = new L120M01D();
	// model1.setMainId(mainId);
	// model1.setItemType("7");
	// model1.setCreateTime(CapDate.getCurrentTimestamp());
	// model1.setCreator(user.getUserId());
	// }
	// if (model2 == null) {
	// model2 = new L120M01D();
	// model2.setMainId(mainId);
	// model2.setItemType("8");
	// model2.setCreateTime(CapDate.getCurrentTimestamp());
	// model2.setCreator(user.getUserId());
	// }
	//
	// if (!Util.isEmpty(Util.trim(itemDscr07))) {
	// model1.setItemDscr(itemDscr07);
	// } else {
	// model1.setItemDscr("");
	// }
	// if (!Util.isEmpty(Util.trim(itemDscr08))) {
	// model2.setItemDscr(itemDscr08);
	// } else {
	// model2.setItemDscr("");
	// }
	// if (!Util.isEmpty(Util.trim(itemTitle))) {
	// if ("1".equals(Util.trim(_itemTitle))) {
	// L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
	// model2.setItemTitle(Util.trim(l120m01a.getRptTitleArea1()));
	// } else {
	// model2.setItemTitle(Util.trim(itemTitle));
	// }
	// }
	// L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
	// try {
	// // service1205.saveArea(l120m01a, model1, model2, saveList);
	// service1205.save(l120m01a, model1, model2);
	// } catch (Exception e) {
	// logger.error("[saveSignContent3] service1205.save EXCEPTION!!", e);
	// Map<String, String> param = new HashMap<String, String>();
	// param.put("colName", space);
	// throw new CapMessageException(RespMsgHelper.getMessage(parent,
	// "EFD0007", param), getClass());
	// }
	// if (params.getAsBoolean("showMsg", true)) {
	// // 印出儲存成功訊息!
	// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
	// .getMainMessage(this.getComponent(), "EFD0017"));
	// }
	// result.set("L120M01aForm15", L120M01aForm15);
	// result.set("LMS1205S01Form", LMS1205S01Form);
	// return result;
	// }

	// /**
	// * 儲存簽章欄(營運中心)
	// *
	// * @param params
	// * @param parent
	// * @return
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	// public IResult saveSignContentF3(PageParameters params)
	// throws CapException {
	// // MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// CapAjaxFormResult L120M01aForm15 = new CapAjaxFormResult();
	// CapAjaxFormResult LMS1205S01Form = new CapAjaxFormResult();
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// // String itemDscr07 = params.getString("itemDscr07");
	// // String itemDscr08 = params.getString("itemDscr08");
	// String sAreaLeader = trimNull(params.getString("sAreaLeader"));
	// String sAreaSubLeader = trimNull(params.getString("sAreaSubLeader"));
	// String sAreaManager = trimNull(params.getString("sAreaManager"));
	// String sAreaAppraiser = trimNull(params.getString("sAreaAppraiser"));
	// String sUnitManager3 = trimNull(params.getString("sUnitManager3"));
	//
	// LMS1205S01Form.set("areaLeader", withIdName(Util.trim(sAreaLeader)));
	// LMS1205S01Form.set("areaSubLeader",
	// withIdName(Util.trim(sAreaSubLeader)));
	// LMS1205S01Form.set("areaManager", withIdName(Util.trim(sAreaManager)));
	// LMS1205S01Form.set("areaAppraiser",
	// withIdName(Util.trim(sAreaAppraiser)));
	//
	// List<L120M01F> list = service1205.findL120m01fByMainId(mainId);
	//
	// List<L120M01F> saveList = new ArrayList<L120M01F>();
	// if (!Util.isEmpty(sAreaLeader) && !Util.isEmpty(sAreaSubLeader)
	// && !Util.isEmpty(sAreaManager) && !Util.isEmpty(sAreaAppraiser)) {
	// if (!list.isEmpty()) {
	// for (L120M01F model : list) {
	// if ("3".equals(model.getBranchType())) {
	// saveList.add(model);
	// }
	// }
	// }
	// if (!saveList.isEmpty()) {
	// service1205.delListL120m01f(saveList);
	// saveList.clear();
	// }
	// // 第一次新增營運簽章欄
	// saveList.add(addAreaL120m01f(mainId, "L6", sAreaLeader));
	// saveList.add(addAreaL120m01f(mainId, "L5", sAreaSubLeader));
	// saveList.add(addAreaL120m01f(mainId, "L3", sAreaManager));
	// saveList.add(addAreaL120m01f(mainId, "L1", sAreaAppraiser));
	// saveList.add(addAreaL120m01f(mainId, "L9", sUnitManager3));
	// }
	//
	// L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
	// try {
	// // service1205.saveArea(l120m01a, model1, model2, saveList);
	// service1205.saveArea(l120m01a, saveList);
	// } catch (Exception e) {
	// logger.error("[saveSignContent3] service1205.save EXCEPTION!!", e);
	// Map<String, String> param = new HashMap<String, String>();
	// param.put("colName", space);
	// throw new CapMessageException(RespMsgHelper.getMessage(parent,
	// "EFD0007", param), getClass());
	// }
	// if (params.getAsBoolean("showMsg", true)) {
	// // 印出儲存成功訊息!
	// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
	// .getMainMessage(this.getComponent(), "EFD0017"));
	// }
	// result.set("L120M01aForm15", L120M01aForm15);
	// result.set("LMS1205S01Form", LMS1205S01Form);
	// return result;
	// }

	/**
	 * 新建國金部簽章欄
	 * 
	 * @param mainId
	 * @param headJob
	 * @param headNo
	 * @return
	 */
	public L120M01F addSeaL120m01f(String mainId, String headJob, String headNo) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01F l120m01f = new L120M01F();
		l120m01f.setMainId(mainId);
		l120m01f.setBranchType("6");
		l120m01f.setBranchId(user.getUnitNo());
		l120m01f.setStaffJob(headJob);
		l120m01f.setStaffNo(headNo);
		l120m01f.setCreator(user.getUserId());
		l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
		l120m01f.setUpdater(user.getUserId());
		l120m01f.setUpdateTime(CapDate.getCurrentTimestamp());
		return l120m01f;
	}

	/**
	 * 新建授管處簽章欄
	 * 
	 * @param mainId
	 * @param headJob
	 * @param headNo
	 * @return
	 */
	private L120M01F addHeadL120m01f(String mainId, String headJob,
			String headNo) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01F l120m01f = new L120M01F();
		l120m01f.setMainId(mainId);
		l120m01f.setBranchType("4");
		l120m01f.setBranchId(user.getUnitNo());
		l120m01f.setStaffJob(headJob);
		l120m01f.setStaffNo(headNo);
		l120m01f.setCreator(user.getUserId());
		l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
		l120m01f.setUpdater(user.getUserId());
		l120m01f.setUpdateTime(CapDate.getCurrentTimestamp());
		return l120m01f;
	}

	// /**
	// * 新建營運中心簽章欄
	// *
	// * @param mainId
	// * @param areaJob
	// * @param areaNo
	// * @return
	// */
	// private L120M01F addAreaL120m01f(String mainId, String areaJob,
	// String areaNo) {
	// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	// L120M01F l120m01f = new L120M01F();
	// l120m01f.setMainId(mainId);
	// l120m01f.setBranchType("3");
	// l120m01f.setBranchId(user.getUnitNo());
	// l120m01f.setStaffJob(areaJob);
	// l120m01f.setStaffNo(areaNo);
	// l120m01f.setCreator(user.getUserId());
	// l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
	// l120m01f.setUpdater(user.getUserId());
	// l120m01f.setUpdateTime(CapDate.getCurrentTimestamp());
	// return l120m01f;
	// }

	// /**
	// * 查詢會簽內容(營運中心)
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	// public IResult querySignContent(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// L121M01B model = service1205.findL121m01bByUniqueKey(mainId, "9");
	// if (model != null) {
	// CapAjaxFormResult formSea = DataParse.toResult(model);
	// formSea.set("itemDscr09", Util.trim(model.getItemDscr()));
	// result.set("formSea", formSea);
	// } else {
	// CapAjaxFormResult formSea = new CapAjaxFormResult();
	// formSea.set("itemDscr09", "");
	// result.set("formSea", formSea);
	// }
	// return result;
	// }

	/**
	 * 查詢會簽內容(授管處補充說明+審查意見)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult querySignContent2(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01D l120m01d0A = service1205.findL120m01dByUniqueKey(mainId, "A");
		L120M01D l120m01d0B = service1205.findL120m01dByUniqueKey(mainId, "B");
		CapAjaxFormResult form0AL120m01d = new CapAjaxFormResult();
		CapAjaxFormResult form0BL120m01d = new CapAjaxFormResult();
		if (l120m01d0A != null) {
			form0AL120m01d = DataParse.toResult(l120m01d0A);
			form0AL120m01d.set("tItemDscr0A",
					Util.trim(l120m01d0A.getItemDscr()));
		} else {
			form0AL120m01d = new CapAjaxFormResult();
			form0AL120m01d.set("tItemDscr0A", "");
		}
		if (l120m01d0B != null) {
			form0BL120m01d = DataParse.toResult(l120m01d0B);
			form0BL120m01d.set("tItemDscr0B",
					Util.trim(l120m01d0B.getItemDscr()));
		} else {
			form0BL120m01d = new CapAjaxFormResult();
			form0BL120m01d.set("tItemDscr0B", "");
		}
		form0AL120m01d.add(form0BL120m01d);
		result.set("L120M01aForm13", form0AL120m01d);
		return result;
	}

	// /**
	// * 查詢會簽內容(營運中心說明及意見)
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	// public IResult querySignContent3(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// L120M01D l120m01d07 = service1205.findL120m01dByUniqueKey(mainId, "7");
	// L120M01D l120m01d08 = service1205.findL120m01dByUniqueKey(mainId, "8");
	// CapAjaxFormResult form07L120m01d = new CapAjaxFormResult();
	// CapAjaxFormResult form08L120m01d = new CapAjaxFormResult();
	// if (l120m01d07 != null) {
	// form07L120m01d = DataParse.toResult(l120m01d07);
	// form07L120m01d.set("tItemDscr07",
	// Util.trim(l120m01d07.getItemDscr()));
	// } else {
	// form07L120m01d = new CapAjaxFormResult();
	// form07L120m01d.set("tItemDscr07", "");
	// }
	// if (l120m01d08 != null) {
	// form08L120m01d = DataParse.toResult(l120m01d08);
	// form08L120m01d.set("tItemDscr08",
	// Util.trim(l120m01d08.getItemDscr()));
	// } else {
	// form08L120m01d = new CapAjaxFormResult();
	// form08L120m01d.set("tItemDscr08", "");
	// }
	// form07L120m01d.add(form08L120m01d);
	// result.set("L120M01aForm15", form07L120m01d);
	// return result;
	// }

	/**
	 * 提會(提授審、提催收、提常董)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendTo(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String hqMeetFlag = params.getString("hqMeetFlag");
		// J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
		String meetingDate = Util.trim(params.getString("meetingDate"));
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01A model = service1205.findL120m01aByMainId(mainId);
		if (model != null) {
			if (!"3".equals(hqMeetFlag) && !UtilConstants.Casedoc.HqMeetFlag.審計委員會.equals(hqMeetFlag)) {
				model.setMeetingType(hqMeetFlag);
			}
			model.setHqMeetFlag(hqMeetFlag);

			service1205.save(model);

			// J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
			// J-109-0479_05097_B1001 Web
			// e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
			if (Util.notEquals(meetingDate, "")) {
				L120S17A l120s17a = service1205.findL120s17aByMainId(mainId);
				if (l120s17a == null) {
					l120s17a = new L120S17A();
					l120s17a.setMainId(mainId);
					l120s17a.setCreateTime(CapDate.getCurrentTimestamp());
					l120s17a.setCreator(user.getUserId());
				}

				if (Util.equals(hqMeetFlag, "1")) {
					// 提授審會
					l120s17a.setDateF1(Util.parseDate(meetingDate));
				} else if (Util.equals(hqMeetFlag, "2")) {
					// 提催收會
					l120s17a.setDateF2(Util.parseDate(meetingDate));
				} else if (Util.equals(hqMeetFlag, UtilConstants.Casedoc.HqMeetFlag.審計委員會)) {
					// J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
					l120s17a.setDateF4(Util.parseDate(meetingDate));
				} else {
					// 提常董會
					l120s17a.setDateF3(Util.parseDate(meetingDate));
				}

				if (UtilConstants.unitType.授管處.equals(Util.trim(user
						.getUnitType()))) {

					l120s17a.setRoleG(user.getUserId());

				}

				l120s17a.setUpdater(user.getUserId());
				l120s17a.setUpdateTime(CapDate.getCurrentTimestamp());

				service1205.save(l120s17a);

				//J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
				// J-109-0479_05097_B1001 Web
				// e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
				lmsService.setL120s17aData(mainId);

			}

		}
		// // 印出執行成功訊息!
		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
		// RespMsgHelper.getMainMessage(this.getComponent(), "EFD0018"));
		return result;
	}

	// /**
	// * 查詢授審會會期
	// *
	// * @param params
	// * @param parent
	// * @return
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	// public IResult queryLogin1(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// CapAjaxFormResult formLms1200v62 = new CapAjaxFormResult();
	// formLms1200v62.set("rptTitle1a", "");
	// formLms1200v62.set("rptTitle1b", "");
	// formLms1200v62.set("rptTitle1c", "");
	// formLms1200v62.set("rptTitle1d", "");
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// L120M01A model = service1205.findL120m01aByMainId(mainId);
	// if (model != null) {
	// result.set(EloanConstants.OID, model.getOid());
	// String rptTitle1 = model.getRptTitle1();
	// if (!Util.isEmpty(rptTitle1)) {
	// formLms1200v62.set("rptTitle1a", rptTitle1.substring(0, 3));
	// formLms1200v62.set("rptTitle1b", rptTitle1.substring(4, 6));
	// formLms1200v62.set("rptTitle1c", rptTitle1.substring(7, 9));
	// String times = rptTitle1.substring(11);
	// formLms1200v62.set("rptTitle1d",
	// times.substring(0, times.indexOf("次", 0)));
	// }
	// }
	//
	// result.set("LMS1200V62Form1", formLms1200v62);
	// return result;
	// }
	//
	// /**
	// * 登錄授審會會期(含營運中心)
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	// public IResult login1(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// String LMS1200V62Form1 = params.getString("LMS1200V62Form1");
	// String caseName = params.getString("caseName");
	// boolean isArea = params.getBoolean("isArea");
	// JSONObject json = JSONObject.fromObject(LMS1200V62Form1);
	// StringBuilder strB = new StringBuilder();
	// strB.append(
	// Util.addZeroWithValue(
	// Util.trim((String) json.get("rptTitle1a")), 3))
	// .append("年")
	// .append(Util.addZeroWithValue(
	// Util.trim((String) json.get("rptTitle1b")), 2))
	// .append("月")
	// .append(Util.addZeroWithValue(
	// Util.trim((String) json.get("rptTitle1c")), 2))
	// .append("日第")
	// .append(Util.trim((String) json.get("rptTitle1d"))).append("次")
	// .append(Util.trim(caseName));
	// // 取得list中所有資料組成的字串
	// String listOid = params.getString("oids");
	// // 取得sign的資料
	// String strSign = ",";
	// String oid = params.getString(EloanConstants.OID);
	// List<L120M01A> list = new ArrayList<L120M01A>();
	// if (!Util.isEmpty(oid)) {
	// L120M01A model = service1205.findL120m01aByOid(oid);
	// if (model != null) {
	// if (isArea) {
	// // 營運中心授審會會期
	// model.setRptTitleArea1(strB.toString());
	// } else {
	// // 授管處授審會/催收會會期
	// model.setRptTitle1(strB.toString());
	// }
	// list.add(model);
	// }
	// } else {
	// // 將已取得的字串轉換成一陣列，分割辨識為sign內容
	// String[] oidArray = listOid.split(strSign);
	// if (oidArray.length > 0) {
	// for (String theOid : oidArray) {
	// L120M01A model = service1205.findL120m01aByOid(theOid);
	// if (model != null) {
	// if (isArea) {
	// // 營運中心授審會會期
	// model.setRptTitleArea1(strB.toString());
	// } else {
	// // 授管處授審會/催收會會期
	// model.setRptTitle1(strB.toString());
	// }
	// list.add(model);
	// }
	// }
	// }
	// }
	//
	// try {
	// service1205.saveL120m01as(list);
	// } catch (Exception e) {
	// logger.error("[login1] service1205.save EXCEPTION!!", e);
	// Map<String, String> param = new HashMap<String, String>();
	// param.put("colName", space);
	// throw new CapMessageException(RespMsgHelper.getMessage(parent,
	// "EFD0007", param), getClass());
	// }
	// // 印出執行成功訊息!
	// result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
	// RespMsgHelper.getMainMessage(this.getComponent(), "EFD0018"));
	// return result;
	// }

	/**
	 * 登錄催收會會期
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult login2(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String LMS1200V63Form1 = params.getString("LMS1200V63Form1");
		JSONObject json = JSONObject.fromObject(LMS1200V63Form1);
		boolean isDelete = params.getBoolean("isDelete");
		StringBuilder strB = new StringBuilder("");

		if (isDelete == true) {

		} else {
			strB.append(
					Util.addZeroWithValue(
							Util.trim((String) json.get("rptTitle1a")), 3))
					.append("年")
					.append(Util.addZeroWithValue(
							Util.trim((String) json.get("rptTitle1b")), 2))
					.append("月")
					.append(Util.addZeroWithValue(
							Util.trim((String) json.get("rptTitle1c")), 2))
					.append("日第")
					.append(Util.trim((String) json.get("rptTitle1d")))
					.append("次").append("逾期放款、催收款及呆帳審議委員會");
		}

		// 取得list中所有資料組成的字串
		String listOid = params.getString("oids");
		// 取得sign的資料
		String strSign = ",";
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// String oid = params.getString(EloanConstants.OID);
		List<L120M01A> list = new ArrayList<L120M01A>();
		if (oidArray.length > 0) {
			for (String oid : oidArray) {
				L120M01A model = service1205.findL120m01aByOid(oid);
				if (model != null) {
					model.setRptTitle1(strB.toString());
					list.add(model);
				}
			}
		}
		try {
			service1205.saveL120m01as(list);
		} catch (Exception e) {
			logger.error("[login2] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}

	/**
	 * 登錄常董會會期
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult login3(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String LMS1200V64Form1 = params.getString("LMS1200V64Form1");
		String caseName = params.getString("caseName");
		boolean isDelete = params.getBoolean("isDelete");
		JSONObject json = JSONObject.fromObject(LMS1200V64Form1);
		StringBuilder strB = new StringBuilder("");

		if (isDelete == true) {

		} else {
			strB.append(
					Util.addZeroWithValue(
							Util.trim((String) json.get("rptTitle1a")), 3))
					.append("年")
					.append(Util.addZeroWithValue(
							Util.trim((String) json.get("rptTitle1b")), 2))
					.append("月")
					.append(Util.addZeroWithValue(
							Util.trim((String) json.get("rptTitle1c")), 2))
					.append("日第")
					.append(Util.trim((String) json.get("rptTitle1d")))
					.append("屆第")
					.append(Util.trim((String) json.get("rptTitle1e")))
					.append("次").append(Util.trim(caseName));
		}

		// 取得list中所有資料組成的字串
		String listOid = params.getString("oids");
		// 取得sign的資料
		String strSign = ",";
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// String oid = params.getString(EloanConstants.OID);
		List<L120M01A> list = new ArrayList<L120M01A>();
		if (oidArray.length > 0) {
			for (String oid : oidArray) {
				L120M01A model = service1205.findL120m01aByOid(oid);
				if (model != null) {
					model.setRptTitle2(strB.toString());
					list.add(model);
				}
			}
		}
		try {
			service1205.saveL120m01as(list);
		} catch (Exception e) {
			logger.error("[login3] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}

	/**
	 * 儲存已修改集團欄位
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120S05A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String gformEditable = params.getString("gformEditable");
		JSONObject jsonForm = JSONObject.fromObject(gformEditable);
		String editCol[] = params.getStringArray("editCol");
		for (int i = 0; i < editCol.length; i++) {
			jsonForm.put(editCol[i], Util.trim(jsonForm.get("_" + editCol[i])));
		}
		L120S05A model = service1205.findL120s05aByMainId(mainId);

		if (model != null) {
			DataParse.toBean(jsonForm, model);
			model.setGrpOver(Util.trim(params.getString("_grpOver")));
			// model.setLmtAmt(Util.isEmpty(Util.trim(jsonForm
			// .getString("_lmtAmt"))) ? null : new BigDecimal(Util
			// .trim(jsonForm.getString("_lmtAmt"))));
			// model.setGcrdAmt(Util.isEmpty(Util.trim(jsonForm
			// .getString("_gcrdAmt"))) ? null : new BigDecimal(Util
			// .trim(jsonForm.getString("_gcrdAmt"))));

			// J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
			if (jsonForm.containsKey("_lmtAmt")) {
				model.setLmtAmt(Util.isEmpty(Util.trim(jsonForm.optString(
						"_lmtAmt", ""))) ? null : new BigDecimal(Util
						.trim(jsonForm.optString("_lmtAmt", ""))));
			} else {
				model.setLmtAmt(null);
			}

			// J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
			if (jsonForm.containsKey("_gcrdAmt")) {
				model.setGcrdAmt(Util.isEmpty(Util.trim(jsonForm.optString(
						"_gcrdAmt", ""))) ? null : new BigDecimal(Util
						.trim(jsonForm.optString("_gcrdAmt", ""))));
			} else {
				model.setGcrdAmt(null);
			}

			service1205.save(model);
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		} else {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSCommomPage.class);
			HashMap<String, String> msg = new HashMap<String, String>();
			// other.msg203=請執行「引進相關資料」後再執行本功能
			msg.put("msg", Util.trim((String) prop.get("other.msg203")));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}

		CapAjaxFormResult LMS1205S05Form06 = DataParse.toResult(model,
				DataParse.Need, editCol);
		// 海外分行引不到這2個值，所以讓他可以NA顯示
		if (model.getFcltAmt() == null) {
			LMS1205S05Form06.set("fcltAmt", "N.A.");
		}
		if (model.getLnAmt() == null) {
			LMS1205S05Form06.set("lnAmt", "N.A.");
		}
		if (model.getEndDate() == null) {
			LMS1205S05Form06.set("endDate", "N.A.");
		}
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205S05Panel.class);
		LMS1205S05Form06.set(
				"grpOver",
				"Y".equals(Util.trim(model.getGrpOver())) ? pop
						.getProperty("l120s05a.grpovershowy") : pop
						.getProperty("l120s05a.grpovershown"));
		if (!Util.isEmpty(model.getTotMega())) {
			if (model.getTotMega() == 0) {
				LMS1205S05Form06.set("totMega", "0.00");
			} else {
				LMS1205S05Form06.set("totMega", add2Zero(model.getTotMega()));
			}
		}
		if (!Util.isEmpty(model.getCrdMega())) {
			if (model.getCrdMega() == 0) {
				LMS1205S05Form06.set("crdMega", "0.00");
			} else {
				LMS1205S05Form06.set("crdMega", add2Zero(model.getCrdMega()));
			}
		}
		if (!Util.isEmpty(model.getLntMega())) {
			if (model.getLntMega() == 0) {
				LMS1205S05Form06.set("lntMega", "0.00");
			} else {
				LMS1205S05Form06.set("lntMega", add2Zero(model.getLntMega()));
			}
		}
		if (!Util.isEmpty(model.getLncMega())) {
			if (model.getLncMega() == 0) {
				LMS1205S05Form06.set("lncMega", "0.00");
			} else {
				LMS1205S05Form06.set("lncMega", add2Zero(model.getLncMega()));
			}
		}
		if (!Util.isEmpty(model.getExcMega())) {
			if (model.getExcMega() == 0) {
				LMS1205S05Form06.set("excMega", "0.00");
			} else {
				LMS1205S05Form06.set("excMega", add2Zero(model.getExcMega()));
			}
		}
		if (!Util.isEmpty(model.getSumMega())) {
			if (model.getSumMega() == 0) {
				LMS1205S05Form06.set("sumMega", "0.00");
			} else {
				LMS1205S05Form06.set("sumMega", add2Zero(model.getSumMega()));
			}
		}
		if (!Util.isEmpty(model.getOthMega())) {
			if (model.getOthMega() == 0) {
				LMS1205S05Form06.set("othMega", "0.00");
			} else {
				LMS1205S05Form06.set("othMega", add2Zero(model.getOthMega()));
			}
		}
		if (!Util.isEmpty(model.getFinMega())) {
			if (model.getFinMega() == 0) {
				LMS1205S05Form06.set("finMega", "0.00");
			} else {
				LMS1205S05Form06.set("finMega", add2Zero(model.getFinMega()));
			}
		}
		if (!Util.isEmpty(model.getRskMega())) {
			if (model.getRskMega() == 0) {
				LMS1205S05Form06.set("rskMega", "0.00");
			} else {
				LMS1205S05Form06.set("rskMega", add2Zero(model.getRskMega()));
			}
		}
		if (!Util.isEmpty(model.getLmtMega())) {
			if (model.getLmtMega() == 0) {
				LMS1205S05Form06.set("lmtMega", "0.00");
			} else {
				LMS1205S05Form06.set("lmtMega", add2Zero(model.getLmtMega()));
			}
		}
		if (!Util.isEmpty(model.getGcrdMega())) {
			if (model.getGcrdMega() == 0) {
				LMS1205S05Form06.set("gcrdMega", "0.00");
			} else {
				LMS1205S05Form06.set("gcrdMega", add2Zero(model.getGcrdMega()));
			}
		}
		// BGN J-105-0017-001 Web e-Loan企金授信授權外簽報書第八章增加本行買入集團企業無擔保債券有效額度與餘額。
		if (!Util.isEmpty(model.getBondFactMega())) {
			if (model.getBondFactMega() == 0) {
				LMS1205S05Form06.set("bondFactMega", "0.00");
			} else {
				LMS1205S05Form06.set("bondFactMega",
						add2Zero(model.getBondFactMega()));
			}
		}

		if (!Util.isEmpty(model.getBondBalMega())) {
			if (model.getBondBalMega() == 0) {
				LMS1205S05Form06.set("bondBalMega", "0.00");
			} else {
				LMS1205S05Form06.set("bondBalMega",
						add2Zero(model.getBondBalMega()));
			}
		}
		// END J-105-0017-001 Web e-Loan企金授信授權外簽報書第八章增加本行買入集團企業無擔保債券有效額度與餘額。

		// J-105-0159-001 Web e-Loan企金授信授權外簽報書說明八，修改一律顯示本行對該集團授信限額與無擔保授信額度限額。
		// if (!pop.getProperty("l120s05a.no2").equals(model.getGrpDscr())) {
		// LMS1205S05Form06.set("_hLmtAmt", true);
		// LMS1205S05Form06.set("_hGcrdAmt", true);
		// } else {
		// LMS1205S05Form06.set("_hLmtAmt", false);
		// LMS1205S05Form06.set("_hGcrdAmt", false);
		// }
		LMS1205S05Form06.set("_hLmtAmt", false);
		LMS1205S05Form06.set("_hGcrdAmt", false);

		result.set("LMS1205S05Form06", LMS1205S05Form06);
		return result;
	}// ;

	/**
	 * 儲存已修改關係企業欄位
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120S05C(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String rformEditable = params.getString("rformEditable");
		JSONObject jsonForm = JSONObject.fromObject(rformEditable);
		String editCol[] = params.getStringArray("editCol");
		for (int i = 0; i < editCol.length; i++) {
			jsonForm.put(editCol[i], Util.trim(jsonForm.get("_" + editCol[i])));
		}
		L120S05C model = service1205.findL120s05cByMainId(mainId);
		if (model != null) {
			DataParse.toBean(jsonForm, model);
			model.setRltOver(Util.trim(params.getString("_rltOver")));
			service1205.save(model);
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		CapAjaxFormResult LMS1205S05Form07 = DataParse.toResult(model,
				DataParse.Need, editCol);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205S05Panel.class);
		LMS1205S05Form07.set(
				"rltOver",
				"Y".equals(Util.trim(model.getRltOver())) ? pop
						.getProperty("l120s05a.grpovershowy") : pop
						.getProperty("l120s05a.grpovershown"));
		if (!Util.isEmpty(model.getTotMega())) {
			if (model.getTotMega() == 0) {
				LMS1205S05Form07.set("totMega", "0.00");
			} else {
				LMS1205S05Form07.set("totMega", add2Zero(model.getTotMega()));
			}
		}
		if (!Util.isEmpty(model.getCrdMega())) {
			if (model.getCrdMega() == 0) {
				LMS1205S05Form07.set("crdMega", "0.00");
			} else {
				LMS1205S05Form07.set("crdMega", add2Zero(model.getCrdMega()));
			}
		}
		if (!Util.isEmpty(model.getLntMega())) {
			if (model.getLntMega() == 0) {
				LMS1205S05Form07.set("lntMega", "0.00");
			} else {
				LMS1205S05Form07.set("lntMega", add2Zero(model.getLntMega()));
			}
		}
		if (!Util.isEmpty(model.getLncMega())) {
			if (model.getLncMega() == 0) {
				LMS1205S05Form07.set("lncMega", "0.00");
			} else {
				LMS1205S05Form07.set("lncMega", add2Zero(model.getLncMega()));
			}
		}
		if (!Util.isEmpty(model.getExcMega())) {
			if (model.getExcMega() == 0) {
				LMS1205S05Form07.set("excMega", "0.00");
			} else {
				LMS1205S05Form07.set("excMega", add2Zero(model.getExcMega()));
			}
		}
		if (!Util.isEmpty(model.getSumMega())) {
			if (model.getSumMega() == 0) {
				LMS1205S05Form07.set("sumMega", "0.00");
			} else {
				LMS1205S05Form07.set("sumMega", add2Zero(model.getSumMega()));
			}
		}
		if (!Util.isEmpty(model.getOthMega())) {
			if (model.getOthMega() == 0) {
				LMS1205S05Form07.set("othMega", "0.00");
			} else {
				LMS1205S05Form07.set("othMega", add2Zero(model.getOthMega()));
			}
		}
		if (!Util.isEmpty(model.getFinMega())) {
			if (model.getFinMega() == 0) {
				LMS1205S05Form07.set("finMega", "0.00");
			} else {
				LMS1205S05Form07.set("finMega", add2Zero(model.getFinMega()));
			}
		}
		if (!Util.isEmpty(model.getRskMega())) {
			if (model.getRskMega() == 0) {
				LMS1205S05Form07.set("rskMega", "0.00");
			} else {
				LMS1205S05Form07.set("rskMega", add2Zero(model.getRskMega()));
			}
		}
		result.set("LMS1205S05Form07", LMS1205S05Form07);
		return result;
	}// ;

	/**
	 * 房貸評分卡上傳DW For OBU
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult upDwByL120M01AForOBU(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");

		List<L120M01A> l120m01as = service1205.findL120m01asByOids(oids);
		if (l120m01as == null || l120m01as.isEmpty()) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		} else {
			for (L120M01A l120m01a : l120m01as) {
				clsService.L120UploadDWForOBU(l120m01a);
			}

			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}
		return result;
	}

	/**
	 * J-107-0045-001 Web e-Loan企金授信簽報書配合海外啟用IFRS徵信報告調整財報引進相關功能。
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getFinRatioItems(PageParameters params)
			throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS02Panel.class);

		CapAjaxFormResult result = new CapAjaxFormResult();
		String cesMainId = params.getString("cesMainId1");
		String gaapFlag = params.getString("gaapFlag", " ");
		String fssType = " ";

		List<Map<String, Object>> datas = eloanDbBaseService
				.findC120M01C_selL120s01e2(Util.trim(String.valueOf(cesMainId)));

		// 當抓不到資料時，回傳財務因子類別以IFRS一般財報為準
		if (CollectionUtils.isEmpty(datas)) {
			gaapFlag = "1";
			fssType = "M";
		} else {
			for (Map<String, Object> map : datas) {
				fssType = (String) map.get("fssType");
				fssType = StringUtils.right(fssType, 1);
			}
		}

		String[] codes = service1205.getFssItemCode(gaapFlag.charAt(0),
				fssType.charAt(0));

		if (codes == null) {
			// 改抓預設值 怕徵信新增行業時沒有配合修改(EX.營造業)
			gaapFlag = "1";
			fssType = "M";
			codes = service1205.getFssItemCode(gaapFlag.charAt(0),
					fssType.charAt(0));
		}

		String kind2 = codes[1];

		String[] kind2_codes = kind2.split("\\|");
		JSONObject finRatioItem = new JSONObject();
		for (String kind2_code : kind2_codes) {
			// System.out.println(kind2_code);
			finRatioItem.put(
					kind2_code,
					pop.getProperty(String.valueOf(gaapFlag)
							+ String.valueOf(fssType) + "." + kind2_code));
		}

		result.set("finRatioItem", new CapAjaxFormResult(finRatioItem));
		result.set("fssType", fssType);
		result.set("gaapFlag", gaapFlag);

		return result;

	}

	/**
	 * J-110-0375 常董稿案由 取得常董稿案由
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getGistForMd(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		if (Util.isNotEmpty(mainId)) {
			L120M01D l120m01dY = service1205.findL120m01dByUniqueKey(mainId,
					UtilConstants.Casedoc.L120m01dItemType.常董會案由);
			if (l120m01dY != null) {
				result.set(
						"itemDscrY",
						(Util.isEmpty(Util.nullToSpace(l120m01dY.getItemDscr())) ? UtilConstants.Mark.SPACE
								: l120m01dY.getItemDscr()));
			} else {
				result.set("itemDscrY", UtilConstants.Mark.SPACE);
			}
		} else {
			result.set("itemDscrY", UtilConstants.Mark.SPACE);
		}
		return result;
	}

	/**
	 * J-110-0375 常董稿案由 設定常董稿案由
	 */
	@DomainAuth(AuthType.Modify)
	public IResult setGistForMd(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String itemDscrY = params.getString("itemDscrY");

		if (Util.isNotEmpty(mainId)) {
			L120M01D l120m01dY = service1205.findL120m01dByUniqueKey(mainId,
					UtilConstants.Casedoc.L120m01dItemType.常董會案由);
			if (l120m01dY == null) {
				l120m01dY = new L120M01D();
				l120m01dY.setMainId(mainId);
				l120m01dY
						.setItemType(UtilConstants.Casedoc.L120m01dItemType.常董會案由);
				l120m01dY.setCreateTime(CapDate.getCurrentTimestamp());
				l120m01dY.setCreator(user.getUserId());
			}
			if (!CapString.isEmpty(Util.trim(itemDscrY))) {
				l120m01dY.setItemDscr(itemDscrY);
			} else {
				l120m01dY.setItemDscr(UtilConstants.Mark.SPACE);
			}
			service1205.save(l120m01dY);

			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		} else {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行有誤));
		}
		return result;
	}

	/**
	 * J-113-0306 Eloan>企業授信>案件簽報書送呈區域中心審核後，若被退件，在「待補件/撤件」中之被撤件之案件，能否設計可以再撈到編製中重新簽報，以增進作業效率
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult reBackL120m01a(PageParameters params)
			throws CapException {
		Properties popLms1205v01 = MessageBundleScriptCreator
		.getComponentResource(LMS1205V01Page.class);
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("errorMsg", "");

		String oid = params.getString("oid"); //l120m01a.oid
		L120M01A l120m01a = service1205.findL120m01aByOid(oid);
		//只有撤件能退
		if(Util.trim(l120m01a.getDocStatus()).equals(CreditDocStatusEnum.海外_待撤件.getCode())){
			// 更新Docstatus
			l120m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			service1205.save(l120m01a);
			
			List<L140M01A> listL140m01a = lms1405Service
					.findL140m01aListByL120m01cMainId(l120m01a.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表);
			if(listL140m01a != null){
				for (L140M01A l140m01a : listL140m01a) {
					l140m01a.setDocStatus(FlowDocStatusEnum.編製中.getCode());
				}
				lms1405Service.saveL140m01aList(listL140m01a);
			}
			// 印出  執行成功訊息
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		}else{//拋錯
			Map<String, String> messageMap = new HashMap<String, String>();
			//EFD0025-執行有誤文件非撤件案件
			messageMap.put("msg", ": " + popLms1205v01.getProperty("l120v05.error01"));
								throw new CapMessageException(getPopMessage("EFD0025",
										messageMap), getClass());
		}

		return result;
	}
	
	/**
	 * 登錄審計委員會會期
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult login4(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String LMS1200V65Form1 = params.getString("LMS1200V65Form1");
		String caseName = params.getString("caseName");
		boolean isDelete = params.getBoolean("isDelete");
		JSONObject json = JSONObject.fromObject(LMS1200V65Form1);
		StringBuilder strB = new StringBuilder("");

		if (isDelete == true) {

		} else {
			strB.append(
					Util.addZeroWithValue(
							Util.trim((String) json.get("rptTitle1a")), 3))
					.append("年")
					.append(Util.addZeroWithValue(
							Util.trim((String) json.get("rptTitle1b")), 2))
					.append("月")
					.append(Util.addZeroWithValue(
							Util.trim((String) json.get("rptTitle1c")), 2))
					.append("日第")
					.append(Util.trim((String) json.get("rptTitle1d")))
					.append("屆第")
					.append(Util.trim((String) json.get("rptTitle1e")))
					.append("次").append(Util.trim(caseName));
		}

		// 取得list中所有資料組成的字串
		String listOid = params.getString("oids");
		// 取得sign的資料
		String strSign = ",";
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// String oid = params.getString(EloanConstants.OID);
		List<L120M01A> list = new ArrayList<L120M01A>();
		if (oidArray.length > 0) {
			for (String oid : oidArray) {
				L120M01A model = service1205.findL120m01aByOid(oid);
				if (model != null) {
					model.setRptTitle3(strB.toString());
					list.add(model);
				}
			}
		}
		try {
			service1205.saveL120m01as(list);
		} catch (Exception e) {
			logger.error("[login3] service1205.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", space);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}
}
