package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 整批自動開戶分戶檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C160S03A", uniqueConstraints = @UniqueConstraint(columnNames = {"cntrNo","seqNo"}))
public class C160S03A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 額度案件序號 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="SEQNO", columnDefinition="DECIMAL(5, 0)")
	private Integer seqNo;

	/** 利率代碼 **/
	@Size(max=2)
	@Column(name="INT_CODE", length=2, columnDefinition="CHAR(02)")
	private String int_code;

	/** 利率/加減碼 **/
	@Digits(integer=2, fraction=6, groups = Check.class)
	@Column(name="INT_SPRD", columnDefinition="DECIMAL(8,6)")
	private BigDecimal int_sprd;

	/** 利率方式 **/
	@Size(max=1)
	@Column(name="INT_TYPE", length=1, columnDefinition="CHAR(01)")
	private String int_type;

	/** 利率變動方式 **/
	@Size(max=1)
	@Column(name="INTCHG_TYPE", length=1, columnDefinition="CHAR(01)")
	private String intchg_type;

	/** 利率變動週期 **/
	@Size(max=1)
	@Column(name="INTCHG_CYCL", length=1, columnDefinition="CHAR(01)")
	private String intchg_cycl;

	/** 資金來源 **/
	@Size(max=1)
	@Column(name="FNDSRE", length=1, columnDefinition="CHAR(1)")
	private String fndSre;

	/** 資金來源小類 **/
	@Size(max=3)
	@Column(name="FUND_TYPE2", length=3, columnDefinition="CHAR(03)")
	private String fund_type2;

	/** 用途別 **/
	@Size(max=1)
	@Column(name="LNPURS", length=1, columnDefinition="CHAR(1)")
	private String lnPurs;

	/** 融資業務分類 **/
	@Size(max=1)
	@Column(name="LN_PURPOSE", length=1, columnDefinition="CHAR(1)")
	private String ln_purpose;

	/** 貸款總期數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="MONTHCNT", columnDefinition="DECIMAL(3, 0)")
	private Integer monthCnt;

	/** 到期日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DUE_DT", columnDefinition="DATE")
	private Date due_dt;

	/** 還款基準日 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="RT_DD", columnDefinition="DECIMAL(2, 0)")
	private Integer rt_dd;

	/** 下次還款日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="NT_RT_DT", columnDefinition="DATE")
	private Date nt_rt_dt;

	/** 扣帳基準日 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="INT_RT_DD", columnDefinition="DECIMAL(2, 0)")
	private Integer int_rt_dd;

	/** 下次扣帳日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="INT_INTRT_DT", columnDefinition="DATE")
	private Date int_intrt_dt;

	/** 
	 * 償還方式{2:本息平均,3:本金平均}
	 */
	@Size(max=1)
	@Column(name="AVGPAY", length=1, columnDefinition="CHAR(1)")
	private String avgPay;

	/** 每期攤還本金 **/
	@Digits(integer=13, fraction=2, groups = Check.class)
	@Column(name="EHPAYCPT", columnDefinition="DECIMAL(15, 2)")
	private BigDecimal ehPayCpt;

	/** 期付金繳款週期{1:月,2:雙週} **/
	@Size(max=1)
	@Column(name="INTRT_CYCL", length=1, columnDefinition="CHAR(1)")
	private String intrt_cycl;
	
	/** 自動進帳 **/
	@Size(max=1)
	@Column(name="AUTORCT", length=1, columnDefinition="CHAR(1)")
	private String autoRct;

	/** 進帳日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="RCTDATE", columnDefinition="DATE")
	private Date rctDate;

	/** 存款帳號 (進帳帳號) **/
	@Size(max=14)
	@Column(name="ACCNO", length=14, columnDefinition="CHAR(14)")
	private String accNo;

	/** 放款幣別 **/
	@Size(max=3)
	@Column(name="SWFT", length=3, columnDefinition="CHAR(03)")
	private String swft;

	/** 進帳金額 **/
	@Digits(integer=13, fraction=2, groups = Check.class)
	@Column(name="RCTAMT", columnDefinition="DECIMAL(15, 2)")
	private BigDecimal rctAmt;

	/** 自動扣帳 **/
	@Size(max=1)
	@Column(name="AUTOPAY", length=1, columnDefinition="CHAR(1)")
	private String autoPay;

	/** 扣帳帳號 **/
	@Size(max=14)
	@Column(name="ATPAYNO", length=14, columnDefinition="CHAR(14)")
	private String atPayNo;

	/** 客戶編號–分戶 **/
	@Size(max=10)
	@Column(name="CUSTID_S", length=10, columnDefinition="CHAR(10)")
	private String custId_s;

	/** 重複序號–分戶 **/
	@Size(max=1)
	@Column(name="DUPNO_S", length=1, columnDefinition="CHAR(1)")
	private String dupNo_s;

	/** 利率代碼–分戶 **/
	@Size(max=2)
	@Column(name="INT_CODE_S", length=2, columnDefinition="CHAR(02)")
	private String int_code_s;

	/** 利率/加減碼–分戶 **/
	@Digits(integer=2, fraction=6, groups = Check.class)
	@Column(name="INT_SPRD_S", columnDefinition="DECIMAL(8,6)")
	private BigDecimal int_sprd_s;

	/** 利率方式–分戶 **/
	@Size(max=1)
	@Column(name="INT_TYPE_S", length=1, columnDefinition="CHAR(01)")
	private String int_type_s;

	/** 利率變動方式–分戶 **/
	@Size(max=1)
	@Column(name="INTCHG_TYPE_S", length=1, columnDefinition="CHAR(01)")
	private String intchg_type_s;

	/** 利率變動週期–分戶 **/
	@Size(max=1)
	@Column(name="INTCHG_CYCL_S", length=1, columnDefinition="CHAR(01)")
	private String intchg_cycl_s;

	/** 貸款總期數–分戶 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="MONTHCNT_S", columnDefinition="DECIMAL(3, 0)")
	private Integer monthCnt_s;

	/** 到期日–分戶 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DUE_DT_S", columnDefinition="DATE")
	private Date due_dt_s;

	/** 還款基準日–分戶 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="RT_DD_S", columnDefinition="DECIMAL(2, 0)")
	private Integer rt_dd_s;

	/** 下次還款日–分戶 **/
	@Temporal(TemporalType.DATE)
	@Column(name="NT_RT_DT_S", columnDefinition="DATE")
	private Date nt_rt_dt_s;

	/** 扣帳基準日-分戶 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="INT_RT_DD_S", columnDefinition="DECIMAL(2, 0)")
	private Integer int_rt_dd_s;

	/** 下次扣帳日–分戶 **/
	@Temporal(TemporalType.DATE)
	@Column(name="INT_INTRT_DT_S", columnDefinition="DATE")
	private Date int_intrt_dt_s;

	/** 償還方式–分戶 **/
	@Size(max=1)
	@Column(name="AVGPAY_S", length=1, columnDefinition="CHAR(1)")
	private String avgPay_s;

	/** 每期攤還本金–分戶 **/
	@Digits(integer=13, fraction=2, groups = Check.class)
	@Column(name="EHPAYCPT_S", columnDefinition="DECIMAL(15, 2)")
	private BigDecimal ehPayCpt_s;

	/** 期付金繳款週期{1:月,2:雙週} **/
	@Size(max=1)
	@Column(name="INTRT_CYCL_S", length=1, columnDefinition="CHAR(1)")
	private String intrt_cycl_s;
	
	/** 自動扣帳–分戶 **/
	@Size(max=1)
	@Column(name="AUTOPAY_S", length=1, columnDefinition="CHAR(1)")
	private String autoPay_s;

	/** 扣帳帳號–分戶 **/
	@Size(max=14)
	@Column(name="ATPAYNO_S", length=14, columnDefinition="CHAR(14)")
	private String atPayNo_s;

	/** 通訊地址指標–分戶 **/
	@Size(max=2)
	@Column(name="ADR_PTR_S", length=2, columnDefinition="CHAR(2)")
	private String adr_ptr_s;

	/** 公司電話指標–分戶 **/
	@Size(max=2)
	@Column(name="OPH_PTR_S", length=2, columnDefinition="CHAR(2)")
	private String oph_ptr_s;

	/** 住家電話指標–分戶 **/
	@Size(max=2)
	@Column(name="HPH_PTR_S", length=2, columnDefinition="CHAR(2)")
	private String hph_ptr_s;

	/** 行動電話指標–分戶 **/
	@Size(max=2)
	@Column(name="MPH_PTR_S", length=2, columnDefinition="CHAR(2)")
	private String mph_ptr_s;

	/** EMAIL指標–分戶 **/
	@Size(max=2)
	@Column(name="EML_PTR_S", length=2, columnDefinition="CHAR(2)")
	private String eml_ptr_s;
	
	/** 備註–分戶 **/
	@Size(max=60)
	@Column(name="MEMO_S", length=60, columnDefinition="VARCHAR(60)")
	private String memo_s;

	/** 扣帳失敗通知方式–分戶{' ':不通知 ,'1':簡訊通知, '2':EMAIL 通知} **/
	@Size(max=1)
	@Column(name="UNPAY_FG_S", length=1, columnDefinition="CHAR(1)")
	private String unpay_fg_s;
	
	/** 資料檢核 **/
	@Size(max=1)
	@Column(name="CHKYN", length=1, columnDefinition="CHAR(1)")
	private String chkYN;
	
	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;
	
	/** 扣稅負擔值 **/
	@Digits(integer=4, fraction=3)
	@Column(name="TAX_RATE", columnDefinition="DECIMAL(4,3)")
	private BigDecimal tax_rate;
	
	/** 寬限期_起 **/
	@Digits(integer=3, fraction=0)
	@Column(name="ALLOW_BEG", columnDefinition="DECIMAL(3,0)")
	private Integer allow_beg;
	
	/** 寬限期_迄 **/
	@Digits(integer=3, fraction=0)
	@Column(name="ALLOW_END", columnDefinition="DECIMAL(3,0)")
	private Integer allow_end;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得額度案件序號 **/
	public Integer getSeqNo() {
		return this.seqNo;
	}
	/** 設定額度案件序號 **/
	public void setSeqNo(Integer value) {
		this.seqNo = value;
	}
	
	/** 取得利率代碼 **/
	public String getInt_code() {
		return this.int_code;
	}
	/** 設定利率代碼 **/
	public void setInt_code(String value) {
		this.int_code = value;
	}

	/** 取得利率/加減碼 **/
	public BigDecimal getInt_sprd() {
		return this.int_sprd;
	}
	/** 設定利率/加減碼 **/
	public void setInt_sprd(BigDecimal value) {
		this.int_sprd = value;
	}

	/** 取得利率方式 **/
	public String getInt_type() {
		return this.int_type;
	}
	/** 設定利率方式 **/
	public void setInt_type(String value) {
		this.int_type = value;
	}

	/** 取得利率變動方式 **/
	public String getIntchg_type() {
		return this.intchg_type;
	}
	/** 設定利率變動方式 **/
	public void setIntchg_type(String value) {
		this.intchg_type = value;
	}

	/** 取得利率變動週期 **/
	public String getIntchg_cycl() {
		return this.intchg_cycl;
	}
	/** 設定利率變動週期 **/
	public void setIntchg_cycl(String value) {
		this.intchg_cycl = value;
	}

	/** 取得資金來源 **/
	public String getFndSre() {
		return this.fndSre;
	}
	/** 設定資金來源 **/
	public void setFndSre(String value) {
		this.fndSre = value;
	}

	/** 取得資金來源小類 **/
	public String getFund_type2() {
		return this.fund_type2;
	}
	/** 設定資金來源小類 **/
	public void setFund_type2(String value) {
		this.fund_type2 = value;
	}

	/** 取得用途別 **/
	public String getLnPurs() {
		return this.lnPurs;
	}
	/** 設定用途別 **/
	public void setLnPurs(String value) {
		this.lnPurs = value;
	}

	/** 取得融資業務分類 **/
	public String getLn_purpose() {
		return this.ln_purpose;
	}
	/** 設定融資業務分類 **/
	public void setLn_purpose(String value) {
		this.ln_purpose = value;
	}

	/** 取得貸款總期數 **/
	public Integer getMonthCnt() {
		return this.monthCnt;
	}
	/** 設定貸款總期數 **/
	public void setMonthCnt(Integer value) {
		this.monthCnt = value;
	}

	/** 取得到期日 **/
	public Date getDue_dt() {
		return this.due_dt;
	}
	/** 設定到期日 **/
	public void setDue_dt(Date value) {
		this.due_dt = value;
	}

	/** 取得還款基準日 **/
	public Integer getRt_dd() {
		return this.rt_dd;
	}
	/** 設定還款基準日 **/
	public void setRt_dd(Integer value) {
		this.rt_dd = value;
	}

	/** 取得下次還款日 **/
	public Date getNt_rt_dt() {
		return this.nt_rt_dt;
	}
	/** 設定下次還款日 **/
	public void setNt_rt_dt(Date value) {
		this.nt_rt_dt = value;
	}

	/** 取得扣帳基準日 **/
	public Integer getInt_rt_dd() {
		return this.int_rt_dd;
	}
	/** 設定扣帳基準日 **/
	public void setInt_rt_dd(Integer value) {
		this.int_rt_dd = value;
	}

	/** 取得下次扣帳日 **/
	public Date getInt_intrt_dt() {
		return this.int_intrt_dt;
	}
	/** 設定下次扣帳日 **/
	public void setInt_intrt_dt(Date value) {
		this.int_intrt_dt = value;
	}

	/** 
	 * 取得償還方式{2:本息平均,3:本金平均}
	 */
	public String getAvgPay() {
		return this.avgPay;
	}
	/**
	 *  設定償還方式{2:本息平均,3:本金平均}
	 **/
	public void setAvgPay(String value) {
		this.avgPay = value;
	}

	/** 取得每期攤還本金 **/
	public BigDecimal getEhPayCpt() {
		return this.ehPayCpt;
	}
	/** 設定每期攤還本金 **/
	public void setEhPayCpt(BigDecimal value) {
		this.ehPayCpt = value;
	}

	/** 取得期付金繳款週期{1:月,2:雙週} **/
	public String getIntrt_cycl() {
		return this.intrt_cycl;
	}
	/** 設定期付金繳款週期{1:月,2:雙週} **/
	public void setIntrt_cycl(String value) {
		this.intrt_cycl = value;
	}
	
	/** 取得自動進帳 **/
	public String getAutoRct() {
		return this.autoRct;
	}
	/** 設定自動進帳 **/
	public void setAutoRct(String value) {
		this.autoRct = value;
	}

	/** 取得進帳日期 **/
	public Date getRctDate() {
		return this.rctDate;
	}
	/** 設定進帳日期 **/
	public void setRctDate(Date value) {
		this.rctDate = value;
	}

	/** 取得存款帳號 (進帳帳號) **/
	public String getAccNo() {
		return this.accNo;
	}
	/** 設定存款帳號 (進帳帳號) **/
	public void setAccNo(String value) {
		this.accNo = value;
	}

	/** 取得放款幣別 **/
	public String getSwft() {
		return this.swft;
	}
	/** 設定放款幣別 **/
	public void setSwft(String value) {
		this.swft = value;
	}

	/** 取得進帳金額 **/
	public BigDecimal getRctAmt() {
		return this.rctAmt;
	}
	/** 設定進帳金額 **/
	public void setRctAmt (BigDecimal value) {
		this.rctAmt = value;
	}

	/** 取得自動扣帳 **/
	public String getAutoPay() {
		return this.autoPay;
	}
	/** 設定自動扣帳 **/
	public void setAutoPay(String value) {
		this.autoPay = value;
	}

	/** 取得扣帳帳號 **/
	public String getAtPayNo() {
		return this.atPayNo;
	}
	/** 設定扣帳帳號 **/
	public void setAtPayNo(String value) {
		this.atPayNo = value;
	}
	
	/** 取得客戶編號–分戶 **/
	public String getCustId_s() {
		return this.custId_s;
	}
	/** 設定客戶編號–分戶 **/
	public void setCustId_s(String value) {
		this.custId_s = value;
	}
	
	/** 取得重複序號–分戶 **/
	public String getDupNo_s() {
		return this.dupNo_s;
	}
	/** 設定重複序號–分戶 **/
	public void setDupNo_s(String value) {
		this.dupNo_s = value;
	}

	/** 取得利率代碼–分戶 **/
	public String getInt_code_s() {
		return this.int_code_s;
	}
	/** 設定利率代碼–分戶 **/
	public void setInt_code_s(String value) {
		this.int_code_s = value;
	}

	/** 取得利率/加減碼–分戶 **/
	public BigDecimal getInt_sprd_s() {
		return this.int_sprd_s;
	}
	/** 設定利率/加減碼–分戶 **/
	public void setInt_sprd_s(BigDecimal value) {
		this.int_sprd_s = value;
	}

	/** 取得利率方式–分戶 **/
	public String getInt_type_s() {
		return this.int_type_s;
	}
	/** 設定利率方式–分戶 **/
	public void setInt_type_s(String value) {
		this.int_type_s = value;
	}

	/** 取得利率變動方式–分戶 **/
	public String getIntchg_type_s() {
		return this.intchg_type_s;
	}
	/** 設定利率變動方式–分戶 **/
	public void setIntchg_type_s(String value) {
		this.intchg_type_s = value;
	}

	/** 取得利率變動週期–分戶 **/
	public String getIntchg_cycl_s() {
		return this.intchg_cycl_s;
	}
	/** 設定利率變動週期–分戶 **/
	public void setIntchg_cycl_s(String value) {
		this.intchg_cycl_s = value;
	}

	/** 取得貸款總期數–分戶 **/
	public Integer getMonthCnt_s () {
		return this.monthCnt_s ;
	}
	/** 設定貸款總期數–分戶 **/
	public void setMonthCnt_s (Integer value) {
		this.monthCnt_s  = value;
	}

	/** 取得到期日–分戶 **/
	public Date getDue_dt_s() {
		return this.due_dt_s;
	}
	/** 設定到期日–分戶 **/
	public void setDue_dt_s(Date value) {
		this.due_dt_s = value;
	}

	/** 取得還款基準日–分戶 **/
	public Integer getRt_dd_s() {
		return this.rt_dd_s;
	}
	/** 設定還款基準日–分戶 **/
	public void setRt_dd_s(Integer value) {
		this.rt_dd_s = value;
	}

	/** 取得下次還款日–分戶 **/
	public Date getNt_rt_dt_s() {
		return this.nt_rt_dt_s;
	}
	/** 設定下次還款日–分戶 **/
	public void setNt_rt_dt_s(Date value) {
		this.nt_rt_dt_s = value;
	}

	/** 取得扣帳基準日-分戶 **/
	public Integer getInt_rt_dd_s() {
		return this.int_rt_dd_s;
	}
	/** 設定扣帳基準日-分戶 **/
	public void setInt_rt_dd_s(Integer value) {
		this.int_rt_dd_s = value;
	}

	/** 取得下次扣帳日–分戶 **/
	public Date getInt_intrt_dt_s () {
		return this.int_intrt_dt_s ;
	}
	/** 設定下次扣帳日–分戶 **/
	public void setInt_intrt_dt_s (Date value) {
		this.int_intrt_dt_s  = value;
	}

	/** 取得償還方式–分戶 **/
	public String getAvgPay_s() {
		return this.avgPay_s;
	}
	/** 設定償還方式–分戶 **/
	public void setAvgPay_s(String value) {
		this.avgPay_s = value;
	}

	/** 取得每期攤還本金–分戶 **/
	public BigDecimal getEhPayCpt_s () {
		return this.ehPayCpt_s ;
	}
	/** 設定每期攤還本金–分戶 **/
	public void setEhPayCpt_s (BigDecimal value) {
		this.ehPayCpt_s  = value;
	}
	
	/** 取得期付金繳款週期–分戶{1:月,2:雙週} **/
	public String getIntrt_cycl_s() {
		return this.intrt_cycl_s;
	}
	/** 設定期付金繳款週期–分戶{1:月,2:雙週} **/
	public void setIntrt_cycl_s(String value) {
		this.intrt_cycl_s = value;
	}
	
	/** 取得自動扣帳–分戶 **/
	public String getAutoPay_s () {
		return this.autoPay_s ;
	}
	/** 設定自動扣帳–分戶 **/
	public void setAutoPay_s (String value) {
		this.autoPay_s  = value;
	}

	/** 取得扣帳帳號–分戶 **/
	public String getAtPayNo_s() {
		return this.atPayNo_s;
	}
	/** 設定扣帳帳號–分戶 **/
	public void setAtPayNo_s(String value) {
		this.atPayNo_s = value;
	}

	/** 取得通訊地址指標–分戶 **/
	public String getAdr_ptr_s() {
		return this.adr_ptr_s;
	}
	/** 設定通訊地址指標–分戶 **/
	public void setAdr_ptr_s(String value) {
		this.adr_ptr_s = value;
	}

	/** 取得公司電話指標–分戶 **/
	public String getOph_ptr_s() {
		return this.oph_ptr_s;
	}
	/** 設定公司電話指標–分戶 **/
	public void setOph_ptr_s(String value) {
		this.oph_ptr_s = value;
	}

	/** 取得住家電話指標–分戶 **/
	public String getHph_ptr_s() {
		return this.hph_ptr_s;
	}
	/** 設定住家電話指標–分戶 **/
	public void setHph_ptr_s(String value) {
		this.hph_ptr_s = value;
	}

	/** 取得行動電話指標–分戶 **/
	public String getMph_ptr_s() {
		return this.mph_ptr_s;
	}
	/** 設定行動電話指標–分戶 **/
	public void setMph_ptr_s(String value) {
		this.mph_ptr_s = value;
	}
	
	/** 取得EMAIL指標–分戶 **/
	public String getEml_ptr_s() {
		return this.eml_ptr_s;
	}
	/** 設定EMAIL指標–分戶 **/
	public void setEml_ptr_s(String value) {
		this.eml_ptr_s = value;
	}

	/** 取得備註–分戶 **/
	public String getMemo_s() {
		return this.memo_s;
	}
	/** 設定備註–分戶 **/
	public void setMemo_s(String value) {
		this.memo_s = value;
	}
	
	/** 取得扣帳失敗通知方式–分戶{' ':不通知 ,'1':簡訊通知, '2':EMAIL 通知} **/
	public String getUnpay_fg_s() {
		return this.unpay_fg_s;
	}
	/** 設定扣帳失敗通知方式–分戶{' ':不通知 ,'1':簡訊通知, '2':EMAIL 通知} **/
	public void setUnpay_fg_s(String value) {
		this.unpay_fg_s = value;
	}
	
	/** 取得資料檢核 **/
	public String getChkYN() {
		return this.chkYN;
	}
	/** 設定資料檢核 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}



	/** 取得扣稅負擔值 **/
	public BigDecimal getTax_rate() {
		return tax_rate;
	}
	/** 設定扣稅負擔值 **/
	public void setTax_rate(BigDecimal new_tax_rate) {
		this.tax_rate = new_tax_rate;
	}
	
	/** 取得寬限期_起 **/
	public Integer getAllow_beg() {
		return allow_beg;
	}
	/** 設定寬限期_起 **/
	public void setAllow_beg(Integer new_allow_beg) {
		this.allow_beg = new_allow_beg;
	}
	
	/** 取得寬限期_迄 **/
	public Integer getAllow_end() {
		return allow_end;
	}
	/** 設定寬限期_迄 **/
	public void setAllow_end(Integer new_allow_end) {
		this.allow_end = new_allow_end;
	}
}
