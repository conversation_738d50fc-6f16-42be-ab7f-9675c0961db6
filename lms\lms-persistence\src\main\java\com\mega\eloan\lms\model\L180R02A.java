/* 
 * L180R02A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 營運中心授權內外已核准已婉卻授信案件 **/
@Entity
@Table(name = "L180R02A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L180R02A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** mainId **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 經辦 **/
	@Column(name = "APPROVER", length = 18, columnDefinition = "VARCHAR(18)")
	private String approver;

	/** 分行別 **/
	@Column(name = "BRNO", length = 3, columnDefinition = "CHAR(3)")
	private String brno;

	/** 營運中心代碼 **/
	@Column(name = "AREABRANCHID", length = 3, columnDefinition = "CHAR(3)")
	private String areaBranchId;

	/** 核定日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/** 案件號碼 **/
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/** 統一編號 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/** 額度序號 **/
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/**
	 * 企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String docType;

	/**
	 * 授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 */
	@Column(name = "DOCKIND", length = 1, columnDefinition = "CHAR(1)")
	private String docKind;

	/** 授信科目 **/
	@Column(name = "LNSUBJECT", length = 300, columnDefinition = "VARCHAR(300)")
	private String lnSubject;

	/**
	 * 流用不合計
	 * <p/>
	 * N為不合計 A為增加 D為減少X為不合計增加Y為不合計減少
	 */
	@Column(name = "NOUSEAMT", length = 1, columnDefinition = "CHAR(1)")
	private String noUseAmt;

	/** 增減額度幣別 **/
	@Column(name = "ASCURR", length = 3, columnDefinition = "CHAR(3)")
	private String asCurr;

	/** 增減額度金額(元) **/
	@Column(name = "ASAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal asAmt;

	/** 額度－幣別 **/
	@Column(name = "LV2CURR", length = 3, columnDefinition = "CHAR(3)")
	private String LV2Curr;

	/** 額度－金額 **/
	@Column(name = "LV2AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LV2Amt;

	/** 動用期限 **/
	@Column(name = "DESP1", length = 900, columnDefinition = "VARCHAR(900)")
	private String desp1;

	/**
	 * 性質
	 * <p/>
	 * 100/10/03 新增11、12、13<br/>
	 * 100/12/05調整成notes代碼<br/>
	 * 100/12/20調整欄位長度150(30<br/>
	 * 複選：<br/>
	 * 1報價(報價|13<br/>
	 * 2新作(新做|1<br/>
	 * 3增額(增額|5<br/>
	 * 4紓困(紓困|10<br/>
	 * 5協議清償(協議清償|12<br/>
	 * 6減額(減額|6<br/>
	 * 7變更條件(變更條件|3<br/>
	 * 8續約(續約|2<br/>
	 * 9提前續約(提前續約|11<br/>
	 * 10展期（不良授信案）(展期(不良授信案)|9<br/>
	 * 11流用(流用|4<br/>
	 * 12取消(取消|8<br/>
	 * 13不變(不變|7
	 */
	@Column(name = "PROPERTY", length = 30, columnDefinition = "VARCHAR(30)")
	private String property;

	/** 備查日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "HQCHECKDATE", columnDefinition = "DATE")
	private Date hqCheckDate;

	/**
	 * 備註
	 * <p/>
	 * 100個全型字
	 */
	@Column(name = "HQCHECKMEMO", length = 300, columnDefinition = "VARCHAR(300)")
	private String hqCheckMemo;

	/**
	 * 審核不通過
	 * <p/>
	 * Y為審核不通過
	 */
	@Column(name = "AUDIT", length = 1, columnDefinition = "CHAR(1)")
	private String audit;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/** L120M01A_MainId **/
	@Column(name="L120M01A_MainId", columnDefinition="CHAR(32)")
	private String L120M01A_MainId;
	
	/** L120M01A_MainId **/
	@Column(name="L140M01A_MainId", columnDefinition="CHAR(32)")
	private String L140M01A_MainId;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得經辦 **/
	public String getApprover() {
		return this.approver;
	}

	/** 設定經辦 **/
	public void setApprover(String value) {
		this.approver = value;
	}

	/** 取得分行別 **/
	public String getBrno() {
		return this.brno;
	}

	/** 設定分行別 **/
	public void setBrno(String value) {
		this.brno = value;
	}

	/** 取得營運中心代碼 **/
	public String getAreaBranchId() {
		return this.areaBranchId;
	}

	/** 設定營運中心代碼 **/
	public void setAreaBranchId(String value) {
		this.areaBranchId = value;
	}

	/** 取得核定日 **/
	public Date getCaseDate() {
		return this.caseDate;
	}

	/** 設定核定日 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/** 取得案件號碼 **/
	public String getCaseNo() {
		return this.caseNo;
	}

	/** 設定案件號碼 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/**
	 * 取得企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	public String getDocType() {
		return this.docType;
	}

	/**
	 * 設定企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/**
	 * 取得授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 */
	public String getDocKind() {
		return this.docKind;
	}

	/**
	 * 設定授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 **/
	public void setDocKind(String value) {
		this.docKind = value;
	}

	/** 取得授信科目 **/
	public String getLnSubject() {
		return this.lnSubject;
	}

	/** 設定授信科目 **/
	public void setLnSubject(String value) {
		this.lnSubject = value;
	}

	/**
	 * 取得流用不合計
	 * <p/>
	 * N為不合計 A為增加 D為減少
	 */
	public String getNoUseAmt() {
		return this.noUseAmt;
	}

	/**
	 * 設定流用不合計
	 * <p/>
	 * N為不合計 A為增加 D為減少
	 **/
	public void setNoUseAmt(String value) {
		this.noUseAmt = value;
	}

	/** 取得增減額度幣別 **/
	public String getAsCurr() {
		return this.asCurr;
	}

	/** 設定增減額度幣別 **/
	public void setAsCurr(String value) {
		this.asCurr = value;
	}

	/** 取得增減額度金額(元) **/
	public BigDecimal getAsAmt() {
		return this.asAmt;
	}

	/** 設定增減額度金額(元) **/
	public void setAsAmt(BigDecimal value) {
		this.asAmt = value;
	}

	/** 取得前准批覆授信額度－幣別 **/
	public String getLV2Curr() {
		return this.LV2Curr;
	}

	/** 設定前准批覆授信額度－幣別 **/
	public void setLV2Curr(String value) {
		this.LV2Curr = value;
	}

	/** 取得前准批覆授信額度－金額 **/
	public BigDecimal getLV2Amt() {
		return this.LV2Amt;
	}

	/** 設定前准批覆授信額度－金額 **/
	public void setLV2Amt(BigDecimal value) {
		this.LV2Amt = value;
	}

	/** 取得動用期限 **/
	public String getDesp1() {
		return this.desp1;
	}

	/** 設定動用期限 **/
	public void setDesp1(String value) {
		this.desp1 = value;
	}

	/**
	 * 取得性質
	 * <p/>
	 * 100/10/03 新增11、12、13<br/>
	 * 100/12/05調整成notes代碼<br/>
	 * 100/12/20調整欄位長度150(30<br/>
	 * 複選：<br/>
	 * 1報價(報價|13<br/>
	 * 2新作(新做|1<br/>
	 * 3增額(增額|5<br/>
	 * 4紓困(紓困|10<br/>
	 * 5協議清償(協議清償|12<br/>
	 * 6減額(減額|6<br/>
	 * 7變更條件(變更條件|3<br/>
	 * 8續約(續約|2<br/>
	 * 9提前續約(提前續約|11<br/>
	 * 10展期（不良授信案）(展期(不良授信案)|9<br/>
	 * 11流用(流用|4<br/>
	 * 12取消(取消|8<br/>
	 * 13不變(不變|7
	 */
	public String getProperty() {
		return this.property;
	}

	/**
	 * 設定性質
	 * <p/>
	 * 100/10/03 新增11、12、13<br/>
	 * 100/12/05調整成notes代碼<br/>
	 * 100/12/20調整欄位長度150(30<br/>
	 * 複選：<br/>
	 * 1報價(報價|13<br/>
	 * 2新作(新做|1<br/>
	 * 3增額(增額|5<br/>
	 * 4紓困(紓困|10<br/>
	 * 5協議清償(協議清償|12<br/>
	 * 6減額(減額|6<br/>
	 * 7變更條件(變更條件|3<br/>
	 * 8續約(續約|2<br/>
	 * 9提前續約(提前續約|11<br/>
	 * 10展期（不良授信案）(展期(不良授信案)|9<br/>
	 * 11流用(流用|4<br/>
	 * 12取消(取消|8<br/>
	 * 13不變(不變|7
	 **/
	public void setProperty(String value) {
		this.property = value;
	}

	/** 取得備查日期 **/
	public Date getHqCheckDate() {
		return this.hqCheckDate;
	}

	/** 設定備查日期 **/
	public void setHqCheckDate(Date value) {
		this.hqCheckDate = value;
	}

	/**
	 * 取得備註
	 * <p/>
	 * 100個全型字
	 */
	public String getHqCheckMemo() {
		return this.hqCheckMemo;
	}

	/**
	 * 設定備註
	 * <p/>
	 * 100個全型字
	 **/
	public void setHqCheckMemo(String value) {
		this.hqCheckMemo = value;
	}

	/**
	 * 取得審核不通過
	 * <p/>
	 * Y為審核不通過
	 */
	public String getAudit() {
		return this.audit;
	}

	/**
	 * 設定審核不通過
	 * <p/>
	 * Y為審核不通過
	 **/
	public void setAudit(String value) {
		this.audit = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	
	/**
	 * @param setL120M01A_MainId the setL120M01A_MainId to set
	 */
	public void setL120M01A_MainId(String L120M01A_MainId) {
		this.L120M01A_MainId = L120M01A_MainId;
	}
	/**
	 * @return the setL120M01A_MainId
	 */
	public String getL120M01A_MainId() {
		return L120M01A_MainId;
	}
	
	/**
	 * @param setL140M01A_MainId the setL140M01A_MainId to set
	 */
	public void setL140M01A_MainId(String L140M01A_MainId) {
		this.L140M01A_MainId = L140M01A_MainId;
	}
	/**
	 * @return the setL140M01A_MainId
	 */
	public String getL140M01A_MainId() {
		return L140M01A_MainId;
	}
}
