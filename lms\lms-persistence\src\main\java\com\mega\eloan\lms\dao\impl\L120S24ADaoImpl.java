/* 
 * L120S24ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S24ADao;
import com.mega.eloan.lms.model.L120S24A;

/** 風控風險權數主檔 **/
@Repository
public class L120S24ADaoImpl extends LMSJpaDao<L120S24A, String>
	implements L120S24ADao {

	@Override
	public L120S24A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public L120S24A findByRefMainIdAndCntrNo(String refMainId, String cntrNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "refMainId_s24a", refMainId);// L140M01A.mainId
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo_s24a", cntrNo);// L140M01A.mainId
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S24A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S24A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S24A> findByIndex01(String mainId){
		ISearch search = createSearchTemplete();
		List<L120S24A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
    public List<L120S24A> findByMainIdCntrNo(String mainId, String cntrNo){
        ISearch search = createSearchTemplete();
        List<L120S24A> list = null;
        if (mainId != null)
            search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        if (cntrNo != null)
            search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo_s24a", cntrNo);
        //檢查是否有查詢參數
        List<SearchModeParameter> searchList = search.getSearchModeParameters();
        if (searchList.size() != 0){
            list = createQuery(search).getResultList();
        }
        return list;
    }
	
	@Override
	public List<L120S24A> findDifferentBowrrowerClass(String mainId, String custId,
			String dupNo, String bowrrowerClass_s24a, String oid){
		ISearch search = createSearchTemplete();
        List<L120S24A> list = null;
        if (mainId != null)
            search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        if (custId != null)
            search.addSearchModeParameters(SearchMode.EQUALS, "custId_s24a", custId);
        if (dupNo != null)
            search.addSearchModeParameters(SearchMode.EQUALS, "dupNo_s24a", dupNo);
        if (bowrrowerClass_s24a != null){
        	search.addSearchModeParameters(SearchMode.NOT_EQUALS, "bowrrowerClass_s24a", bowrrowerClass_s24a);
        	search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "bowrrowerClass_s24a", "");// 不為NULL
        	search.addSearchModeParameters(SearchMode.NOT_EQUALS, "bowrrowerClass_s24a", "");// 不為空字串
        }
        if (oid != null)
            search.addSearchModeParameters(SearchMode.NOT_EQUALS, "oid", oid);
        //檢查是否有查詢參數
        List<SearchModeParameter> searchList = search.getSearchModeParameters();
        if (searchList.size() != 0){
            list = createQuery(search).getResultList();
        }
        return list;
	}
}