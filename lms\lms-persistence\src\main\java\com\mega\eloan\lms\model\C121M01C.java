package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 澳洲消金評等表 **/
@NamedEntityGraph(name = "C121M01C-entity-graph", attributeNodes = { @NamedAttributeNode("c120m01a") })
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C121M01C", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class C121M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 編製單位代號 **/
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 評等文件ID **/
	@Size(max=32)
	@Column(name="RATINGID", length=32, columnDefinition="CHAR(32)")
	private String ratingId;

	/** 
	 * 評等建立日期<p/>
	 * 建立初始評等的日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="GRDCDATE", columnDefinition="DATE")
	private Date grdCDate;

	/** 評等調整日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="GRDTDATE", columnDefinition="DATE")
	private Date grdTDate;

	/** 完成最終評等日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="RATINGDATE", columnDefinition="DATE")
	private Date ratingDate;

	/** VEDA查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="VEDAQDATE", columnDefinition="DATE")
	private Date vedaQDate;

	/** 模型版本 **/
	@Size(max=3)
	@Column(name="VARVER", length=3, columnDefinition="VARCHAR(3)")
	private String varVer;

	/** 初始評等 **/
	@Size(max=2)
	@Column(name="PRATING", length=2, columnDefinition="VARCHAR(2)")
	private String pRating;

	/** 
	 * 獨立評等<p/>
	 * (考慮VEDA Report資訊後)
	 */
	@Size(max=2)
	@Column(name="SRATING", length=2, columnDefinition="VARCHAR(2)")
	private String sRating;

	/** 支援評等 **/
	@Size(max=2)
	@Column(name="SPRTRATING", length=2, columnDefinition="VARCHAR(2)")
	private String sprtRating;

	/** 調整評等 **/
	@Size(max=2)
	@Column(name="ADJRATING", length=2, columnDefinition="VARCHAR(2)")
	private String adjRating;

	/** 
	 * 最終評等<p/>
	 * (考量主觀評等)
	 */
	@Size(max=2)
	@Column(name="FRATING", length=2, columnDefinition="VARCHAR(2)")
	private String fRating;

	/** 原始最終評等 **/
	@Size(max=2)
	@Column(name="ORGFR", length=2, columnDefinition="VARCHAR(2)")
	private String orgFr;

	/** 
	 * 註記不需調整<p/>
	 * 1是/2否
	 */
	@Size(max=1)
	@Column(name="NOADJ", length=1, columnDefinition="CHAR(1)")
	private String noAdj;

	/** 
	 * 調整狀態<p/>
	 * 1.調升 2.調降 3.回復
	 */
	@Size(max=1)
	@Column(name="ADJUSTSTATUS", length=1, columnDefinition="CHAR(1)")
	private String adjustStatus;

	/** 
	 * 調整註記<p/>
	 * 1.淨資產2.職業3.其它
	 */
	@Size(max=1)
	@Column(name="ADJUSTFLAG", length=1, columnDefinition="CHAR(1)")
	private String adjustFlag;

	/** 調整理由 **/
	@Size(max=300)
	@Column(name="ADJUSTREASON", length=300, columnDefinition="VARCHAR(300)")
	private String adjustReason;

	/** M1出生日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="RAW_M1", columnDefinition="Date")
	private Date raw_m1;

	/** 年薪幣別 **/
	@Size(max=3)
	@Column(name="RAW_PAYCURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_payCurr;

	/** 其它收入幣別 **/
	@Size(max=3)
	@Column(name="RAW_OTHERCURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_otherCurr;

	/** P3家庭所得幣別 **/
	@Size(max=3)
	@Column(name="RAW_HINCOMECURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_hincomeCurr;

	/** P3家庭所得金額 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="RAW_HINCOMEAMT", columnDefinition="DEC(13,0)")
	private BigDecimal raw_hincomeAmt;

	/** 本次新做案下不動產租金收入幣別 **/
	@Size(max=3)
	@Column(name="RAW_RINCOMECURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_rincomeCurr;

	/** 財富管理-本行幣別 **/
	@Size(max=3)
	@Column(name="RAW_INVMBALCURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_invMBalCurr;

	/** 財富管理-它行幣別 **/
	@Size(max=3)
	@Column(name="RAW_INVOBALCURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_invOBalCurr;

	/** 金融機構存款往來情形幣別 **/
	@Size(max=3)
	@Column(name="RAW_BRANAMTCURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_branAmtCurr;

	/** 轉換匯率(年薪) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_PAY", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_pay;

	/** 轉換匯率(其他收入) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_OTH", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_oth;

	/** 轉換匯率(家庭所得) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_HINCOME", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_hincome;

	/** 轉換匯率(本次新做案下不動產租金收入) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_RINCOME", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_rincome;

	/** 轉換匯率(財富管理-本行) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_INVMBAL", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_invMBal;

	/** 轉換匯率(財富管理-它行) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_INVOBAL", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_invOBal;

	/** 轉換匯率(金融機構存款往來情形) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_BRANAMT", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_branAmt;

	/** A5月份數 **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="RAW_A5", columnDefinition="DEC(4,0)")
	private Integer raw_a5;

	/** 
	 * AU負面一般1<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEMAUG1", length=1, columnDefinition="CHAR(1)")
	private String chkItemAUG1;

	/** 
	 * AU負面一般2<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEMAUG2", length=1, columnDefinition="CHAR(1)")
	private String chkItemAUG2;

	/** 
	 * AU負面一般3<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEMAUG3", length=1, columnDefinition="CHAR(1)")
	private String chkItemAUG3;

	/** 
	 * AU負面特殊1<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEMAUS1", length=1, columnDefinition="CHAR(1)")
	private String chkItemAUS1;

	/** 
	 * AU負面特殊2<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEMAUS2", length=1, columnDefinition="CHAR(1)")
	private String chkItemAUS2;

	/** 
	 * AU其他資訊1<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEMAUO1", length=1, columnDefinition="CHAR(1)")
	private String chkItemAUO1;
	
	/** 累加風險點數 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="SUMRISKPT", columnDefinition="DEC(2,0)")
	private Integer sumRiskPt;

	/** 將Weighted Score相加 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="SCR_CORE", columnDefinition="DEC(8,5)")
	private BigDecimal scr_core;

	/** 核心模型分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_CORE", columnDefinition="DEC(8,5)")
	private BigDecimal std_core;

	/** 年齡 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="ITEM_M1", columnDefinition="DEC(3,0)")
	private Integer item_m1;

	/** 年齡 0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_M1", columnDefinition="DEC(3,0)")
	private BigDecimal scr_m1;

	/** 年齡std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_M1", columnDefinition="DEC(8,5)")
	private BigDecimal std_m1;

	/** 年齡權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_M1", columnDefinition="DEC(5,2)")
	private BigDecimal weight_m1;

	/** 職業 **/
	@Size(max=2)
	@Column(name="ITEM_M5", length=2, columnDefinition="VARCHAR(2)")
	private String item_m5;

	/** 職業0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_M5", columnDefinition="DEC(3,0)")
	private BigDecimal scr_m5;

	/** 職業std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_M5", columnDefinition="DEC(8,5)")
	private BigDecimal std_m5;

	/** 職業權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_M5", columnDefinition="DEC(5,2)")
	private BigDecimal weight_m5;

	/** 年資 **/
	@Column(name="ITEM_M7", columnDefinition="DEC(5,2)")
	private BigDecimal item_m7;

	/** 年資0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_M7", columnDefinition="DEC(3,0)")
	private BigDecimal scr_m7;

	/** 年資std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_M7", columnDefinition="DEC(8,5)")
	private BigDecimal std_m7;

	/** 年資權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_M7", columnDefinition="DEC(5,2)")
	private BigDecimal weight_m7;

	/** ICR_NA {0:填寫ICR, 1:借款目的為開立保證函, 2:未進行ICR分析} **/
	@Size(max=1)
	@Column(name="ITEM_D1_NA", length=1, columnDefinition="CHAR(1)")
	private String item_d1_na;

	/** ICR **/
	@Digits(integer=15, fraction=5, groups = Check.class)
	@Column(name="ITEM_D1_ICR", columnDefinition="DEC(15,5)")
	private BigDecimal item_d1_icr;

	/** ICR 0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_D1", columnDefinition="DEC(3,0)")
	private BigDecimal scr_d1;

	/** ICR std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_D1", columnDefinition="DEC(8,5)")
	private BigDecimal std_d1;

	/** ICR權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_D1", columnDefinition="DEC(5,2)")
	private BigDecimal weight_d1;

	/** 夫妻年收入幣別 **/
	@Size(max=3)
	@Column(name="ITEM_P3_CURR", length=3, columnDefinition="VARCHAR(3)")
	private String item_p3_curr;
	
	/** 夫妻年收入 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="ITEM_P3", columnDefinition="DEC(13,0)")
	private BigDecimal item_p3;

	/** 夫妻年收入0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_P3", columnDefinition="DEC(3,0)")
	private BigDecimal scr_p3;

	/** 夫妻年收入std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_P3", columnDefinition="DEC(8,5)")
	private BigDecimal std_p3;

	/** 夫妻年收入權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_P3", columnDefinition="DEC(5,2)")
	private BigDecimal weight_p3;

	/** 契約年限 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="ITEM_A5", columnDefinition="DEC(5,2)")
	private BigDecimal item_a5;

	/** 契約年限0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_A5", columnDefinition="DEC(3,0)")
	private BigDecimal scr_a5;

	/** 契約年限std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_A5", columnDefinition="DEC(8,5)")
	private BigDecimal std_a5;

	/** 契約年限權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_A5", columnDefinition="DEC(5,2)")
	private BigDecimal weight_a5;

	/** VedaScore **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="ITEM_O1", columnDefinition="DEC(4,0)")
	private Integer item_o1;

	/** VedaScore 0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_O1", columnDefinition="DEC(3,0)")
	private BigDecimal scr_o1;

	/** VedaScorestd **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_O1", columnDefinition="DEC(8,5)")
	private BigDecimal std_o1;

	/** VedaScore權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_O1", columnDefinition="DEC(5,2)")
	private BigDecimal weight_o1;

	/** 擔保品地點及種類 **/
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ITEM_Z1", columnDefinition="DEC(1,0)")
	private Integer item_z1;

	/** 擔保品地點及種類0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_Z1", columnDefinition="DEC(3,0)")
	private BigDecimal scr_z1;

	/** 擔保品地點及種類std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_Z1", columnDefinition="DEC(8,5)")
	private BigDecimal std_z1;

	/** 擔保品地點及種類權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_Z1", columnDefinition="DEC(5,2)")
	private BigDecimal weight_z1;

	/** 市場環境及變現性 **/
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ITEM_Z2", columnDefinition="DEC(1,0)")
	private Integer item_z2;

	/** 市場環境及變現性 0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_Z2", columnDefinition="DEC(3,0)")
	private BigDecimal scr_z2;

	/** 市場環境及變現性std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_Z2", columnDefinition="DEC(8,5)")
	private BigDecimal std_z2;

	/** 市場環境及變現性權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_Z2", columnDefinition="DEC(5,2)")
	private BigDecimal weight_z2;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="VARCHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="VARCHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 違約機率(預估3年期) **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="DR_3YR", columnDefinition="DEC(8,5)")
	private BigDecimal dr_3yr;

	/** 違約機率(預估1年期) **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="DR_1YR", columnDefinition="DEC(8,5)")
	private BigDecimal dr_1yr;

	/** 升降等-依風險點數{升等1；降等-1} **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="ADJ_PTS", columnDefinition="DEC(2,0)")
	private Integer adj_pts;

	/** 升降等-依特殊警訊 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="ADJ_SW", columnDefinition="DEC(2,0)")
	private Integer adj_sw;

	/** 升降等-依其他資訊 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="ADJ_OI", columnDefinition="DEC(2,0)")
	private Integer adj_oi;
	
	/** 學歷 **/
	@Size(max=2)
	@Column(name="ITEM_EDU", length=2, columnDefinition="VARCHAR(2)")
	private String item_edu;

	/** 學歷分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_EDU", columnDefinition="DEC(3,0)")
	private BigDecimal scr_edu;

	/** 學歷權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_EDU", columnDefinition="DEC(5,2)")
	private BigDecimal weight_edu;
	
	/** 學歷權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_EDU", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_edu;
	
	/** 個人負債比 **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="ITEM_DRATE", columnDefinition="DEC(7,4)")
	private BigDecimal item_drate;

	/** 個人負債比分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_DRATE", columnDefinition="DEC(3,0)")
	private BigDecimal scr_drate;
	
	/** 個人負債比權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_DRATE", columnDefinition="DEC(5,2)")
	private BigDecimal weight_drate;
	
	/** 個人負債比權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_DRATE", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_drate;
	
	/** 年齡權重分數分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_M1", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_m1;
	
	/** 年資權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_M7", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_m7;
	
	/** 契約年限權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_A5", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_a5;
	
	/** 職業權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_M5", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_m5;
	
	/** 年收入權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_P3", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_p3;
	
	/** 擔保品種類權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_Z1", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_z1;
	
	/** 市場環境及變現性權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_Z2", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_z2;
	
	
	/**
	 * 預測壞率
	 * <p/>
	 * 計算：小數點4 位，四捨五入<br/>
	 * (1/(1+Exp(scrNum13 – A/B)))
	 */
	@Digits(integer = 9, fraction = 4, groups = Check.class)
	@Column(name = "PD", columnDefinition = "DEC(9,4)")
	private BigDecimal pd;
	
	/** 
	 * 截距<p/>
	 * 日本房貸2.0
	 */
	@Digits(integer=6, fraction=4, groups = Check.class)
	@Column(name="INTERCEPT", columnDefinition="DECIMAL(6,4)")
	private BigDecimal interCept;

	/** 
	 * 斜率<p/>
	 * 日本房貸2.0
	 */
	@Digits(integer=6, fraction=4, groups = Check.class)
	@Column(name="SLOPE", columnDefinition="DECIMAL(6,4)")
	private BigDecimal slope;
	
	
	
	

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得編製單位代號 **/
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/** 設定編製單位代號 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得評等文件ID **/
	public String getRatingId() {
		return this.ratingId;
	}
	/** 設定評等文件ID **/
	public void setRatingId(String value) {
		this.ratingId = value;
	}

	/** 
	 * 取得評等建立日期<p/>
	 * 建立初始評等的日期
	 */
	public Date getGrdCDate() {
		return this.grdCDate;
	}
	/**
	 *  設定評等建立日期<p/>
	 *  建立初始評等的日期
	 **/
	public void setGrdCDate(Date value) {
		this.grdCDate = value;
	}

	/** 取得評等調整日期 **/
	public Date getGrdTDate() {
		return this.grdTDate;
	}
	/** 設定評等調整日期 **/
	public void setGrdTDate(Date value) {
		this.grdTDate = value;
	}

	/** 取得完成最終評等日期 **/
	public Date getRatingDate() {
		return this.ratingDate;
	}
	/** 設定完成最終評等日期 **/
	public void setRatingDate(Date value) {
		this.ratingDate = value;
	}

	/** 取得VEDA查詢日期 **/
	public Date getVedaQDate() {
		return this.vedaQDate;
	}
	/** 設定VEDA查詢日期 **/
	public void setVedaQDate(Date value) {
		this.vedaQDate = value;
	}

	/** 取得模型版本 **/
	public String getVarVer() {
		return this.varVer;
	}
	/** 設定模型版本 **/
	public void setVarVer(String value) {
		this.varVer = value;
	}

	/** 取得初始評等 **/
	public String getPRating() {
		return this.pRating;
	}
	/** 設定初始評等 **/
	public void setPRating(String value) {
		this.pRating = value;
	}

	/** 
	 * 取得獨立評等<p/>
	 * (考慮VEDA Report資訊後)
	 */
	public String getSRating() {
		return this.sRating;
	}
	/**
	 *  設定獨立評等<p/>
	 *  (考慮VEDA Report資訊後)
	 **/
	public void setSRating(String value) {
		this.sRating = value;
	}

	/** 取得支援評等 **/
	public String getSprtRating() {
		return this.sprtRating;
	}
	/** 設定支援評等 **/
	public void setSprtRating(String value) {
		this.sprtRating = value;
	}

	/** 取得調整評等 **/
	public String getAdjRating() {
		return this.adjRating;
	}
	/** 設定調整評等 **/
	public void setAdjRating(String value) {
		this.adjRating = value;
	}

	/** 
	 * 取得最終評等<p/>
	 * (考量主觀評等)
	 */
	public String getFRating() {
		return this.fRating;
	}
	/**
	 *  設定最終評等<p/>
	 *  (考量主觀評等)
	 **/
	public void setFRating(String value) {
		this.fRating = value;
	}

	/** 取得原始最終評等 **/
	public String getOrgFr() {
		return this.orgFr;
	}
	/** 設定原始最終評等 **/
	public void setOrgFr(String value) {
		this.orgFr = value;
	}

	/** 
	 * 取得註記不需調整<p/>
	 * 1是/2否
	 */
	public String getNoAdj() {
		return this.noAdj;
	}
	/**
	 *  設定註記不需調整<p/>
	 *  1是/2否
	 **/
	public void setNoAdj(String value) {
		this.noAdj = value;
	}

	/** 
	 * 取得調整狀態<p/>
	 * 1.調升 2.調降 3.回復
	 */
	public String getAdjustStatus() {
		return this.adjustStatus;
	}
	/**
	 *  設定調整狀態<p/>
	 *  1.調升 2.調降 3.回復
	 **/
	public void setAdjustStatus(String value) {
		this.adjustStatus = value;
	}

	/** 
	 * 取得調整註記<p/>
	 * 1.淨資產2.職業3.其它
	 */
	public String getAdjustFlag() {
		return this.adjustFlag;
	}
	/**
	 *  設定調整註記<p/>
	 *  1.淨資產2.職業3.其它
	 **/
	public void setAdjustFlag(String value) {
		this.adjustFlag = value;
	}

	/** 取得調整理由 **/
	public String getAdjustReason() {
		return this.adjustReason;
	}
	/** 設定調整理由 **/
	public void setAdjustReason(String value) {
		this.adjustReason = value;
	}

	/** 取得M1出生日 **/
	public Date getRaw_m1() {
		return this.raw_m1;
	}
	/** 設定M1出生日 **/
	public void setRaw_m1(Date value) {
		this.raw_m1 = value;
	}

	/** 取得年薪幣別 **/
	public String getRaw_payCurr() {
		return this.raw_payCurr;
	}
	/** 設定年薪幣別 **/
	public void setRaw_payCurr(String value) {
		this.raw_payCurr = value;
	}

	/** 取得其它收入幣別 **/
	public String getRaw_otherCurr() {
		return this.raw_otherCurr;
	}
	/** 設定其它收入幣別 **/
	public void setRaw_otherCurr(String value) {
		this.raw_otherCurr = value;
	}

	/** 取得P3家庭所得幣別 **/
	public String getRaw_hincomeCurr() {
		return this.raw_hincomeCurr;
	}
	/** 設定P3家庭所得幣別 **/
	public void setRaw_hincomeCurr(String value) {
		this.raw_hincomeCurr = value;
	}

	/** 取得P3家庭所得金額 **/
	public BigDecimal getRaw_hincomeAmt() {
		return this.raw_hincomeAmt;
	}
	/** 設定P3家庭所得金額 **/
	public void setRaw_hincomeAmt(BigDecimal value) {
		this.raw_hincomeAmt = value;
	}

	/** 取得本次新做案下不動產租金收入幣別 **/
	public String getRaw_rincomeCurr() {
		return this.raw_rincomeCurr;
	}
	/** 設定本次新做案下不動產租金收入幣別 **/
	public void setRaw_rincomeCurr(String value) {
		this.raw_rincomeCurr = value;
	}

	/** 取得財富管理-本行幣別 **/
	public String getRaw_invMBalCurr() {
		return this.raw_invMBalCurr;
	}
	/** 設定財富管理-本行幣別 **/
	public void setRaw_invMBalCurr(String value) {
		this.raw_invMBalCurr = value;
	}

	/** 取得財富管理-它行幣別 **/
	public String getRaw_invOBalCurr() {
		return this.raw_invOBalCurr;
	}
	/** 設定財富管理-它行幣別 **/
	public void setRaw_invOBalCurr(String value) {
		this.raw_invOBalCurr = value;
	}

	/** 取得金融機構存款往來情形幣別 **/
	public String getRaw_branAmtCurr() {
		return this.raw_branAmtCurr;
	}
	/** 設定金融機構存款往來情形幣別 **/
	public void setRaw_branAmtCurr(String value) {
		this.raw_branAmtCurr = value;
	}

	/** 取得轉換匯率(年薪) **/
	public BigDecimal getExRate_pay() {
		return this.exRate_pay;
	}
	/** 設定轉換匯率(年薪) **/
	public void setExRate_pay(BigDecimal value) {
		this.exRate_pay = value;
	}

	/** 取得轉換匯率(其他收入) **/
	public BigDecimal getExRate_oth() {
		return this.exRate_oth;
	}
	/** 設定轉換匯率(其他收入) **/
	public void setExRate_oth(BigDecimal value) {
		this.exRate_oth = value;
	}

	/** 取得轉換匯率(家庭所得) **/
	public BigDecimal getExRate_hincome() {
		return this.exRate_hincome;
	}
	/** 設定轉換匯率(家庭所得) **/
	public void setExRate_hincome(BigDecimal value) {
		this.exRate_hincome = value;
	}

	/** 取得轉換匯率(本次新做案下不動產租金收入) **/
	public BigDecimal getExRate_rincome() {
		return this.exRate_rincome;
	}
	/** 設定轉換匯率(本次新做案下不動產租金收入) **/
	public void setExRate_rincome(BigDecimal value) {
		this.exRate_rincome = value;
	}

	/** 取得轉換匯率(財富管理-本行) **/
	public BigDecimal getExRate_invMBal() {
		return this.exRate_invMBal;
	}
	/** 設定轉換匯率(財富管理-本行) **/
	public void setExRate_invMBal(BigDecimal value) {
		this.exRate_invMBal = value;
	}

	/** 取得轉換匯率(財富管理-它行) **/
	public BigDecimal getExRate_invOBal() {
		return this.exRate_invOBal;
	}
	/** 設定轉換匯率(財富管理-它行) **/
	public void setExRate_invOBal(BigDecimal value) {
		this.exRate_invOBal = value;
	}

	/** 取得轉換匯率(金融機構存款往來情形) **/
	public BigDecimal getExRate_branAmt() {
		return this.exRate_branAmt;
	}
	/** 設定轉換匯率(金融機構存款往來情形) **/
	public void setExRate_branAmt(BigDecimal value) {
		this.exRate_branAmt = value;
	}

	/** 取得A5月份數 **/
	public Integer getRaw_a5() {
		return this.raw_a5;
	}
	/** 設定A5月份數 **/
	public void setRaw_a5(Integer value) {
		this.raw_a5 = value;
	}

	/** 
	 * 取得AU負面一般1<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItemAUG1() {
		return this.chkItemAUG1;
	}
	/**
	 *  設定AU負面一般1<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItemAUG1(String value) {
		this.chkItemAUG1 = value;
	}

	/** 
	 * 取得AU負面一般2<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItemAUG2() {
		return this.chkItemAUG2;
	}
	/**
	 *  設定AU負面一般2<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItemAUG2(String value) {
		this.chkItemAUG2 = value;
	}

	/** 
	 * 取得AU負面一般3<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItemAUG3() {
		return this.chkItemAUG3;
	}
	/**
	 *  設定AU負面一般3<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItemAUG3(String value) {
		this.chkItemAUG3 = value;
	}

	/** 
	 * 取得AU負面特殊1<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItemAUS1() {
		return this.chkItemAUS1;
	}
	/**
	 *  設定AU負面特殊1<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItemAUS1(String value) {
		this.chkItemAUS1 = value;
	}

	/** 
	 * 取得AU負面特殊2<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItemAUS2() {
		return this.chkItemAUS2;
	}
	/**
	 *  設定AU負面特殊2<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItemAUS2(String value) {
		this.chkItemAUS2 = value;
	}

	/** 
	 * 取得AU其他資訊1<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItemAUO1() {
		return this.chkItemAUO1;
	}
	/**
	 *  設定AU其他資訊1<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItemAUO1(String value) {
		this.chkItemAUO1 = value;
	}
	
	/** 取得累加風險點數 **/
	public Integer getSumRiskPt() {
		return this.sumRiskPt;
	}
	/** 設定累加風險點數 **/
	public void setSumRiskPt(Integer value) {
		this.sumRiskPt = value;
	}

	/** 取得將Weighted Score相加 **/
	public BigDecimal getScr_core() {
		return this.scr_core;
	}
	/** 設定將Weighted Score相加 **/
	public void setScr_core(BigDecimal value) {
		this.scr_core = value;
	}

	/** 取得核心模型分數 **/
	public BigDecimal getStd_core() {
		return this.std_core;
	}
	/** 設定核心模型分數 **/
	public void setStd_core(BigDecimal value) {
		this.std_core = value;
	}

	/** 取得年齡 **/
	public Integer getItem_m1() {
		return this.item_m1;
	}
	/** 設定年齡 **/
	public void setItem_m1(Integer value) {
		this.item_m1 = value;
	}

	/** 取得年齡 0~100分數 **/
	public BigDecimal getScr_m1() {
		return this.scr_m1;
	}
	/** 設定年齡 0~100分數 **/
	public void setScr_m1(BigDecimal value) {
		this.scr_m1 = value;
	}

	/** 取得年齡std **/
	public BigDecimal getStd_m1() {
		return this.std_m1;
	}
	/** 設定年齡std **/
	public void setStd_m1(BigDecimal value) {
		this.std_m1 = value;
	}

	/** 取得年齡權重 **/
	public BigDecimal getWeight_m1() {
		return this.weight_m1;
	}
	/** 設定年齡權重 **/
	public void setWeight_m1(BigDecimal value) {
		this.weight_m1 = value;
	}

	/** 取得職業 **/
	public String getItem_m5() {
		return this.item_m5;
	}
	/** 設定職業 **/
	public void setItem_m5(String value) {
		this.item_m5 = value;
	}

	/** 取得職業0~100分數 **/
	public BigDecimal getScr_m5() {
		return this.scr_m5;
	}
	/** 設定職業0~100分數 **/
	public void setScr_m5(BigDecimal value) {
		this.scr_m5 = value;
	}

	/** 取得職業std **/
	public BigDecimal getStd_m5() {
		return this.std_m5;
	}
	/** 設定職業std **/
	public void setStd_m5(BigDecimal value) {
		this.std_m5 = value;
	}

	/** 取得職業權重 **/
	public BigDecimal getWeight_m5() {
		return this.weight_m5;
	}
	/** 設定職業權重 **/
	public void setWeight_m5(BigDecimal value) {
		this.weight_m5 = value;
	}

	/** 取得年資 **/
	public BigDecimal getItem_m7() {
		return this.item_m7;
	}
	/** 設定年資 **/
	public void setItem_m7(BigDecimal value) {
		this.item_m7 = value;
	}

	/** 取得年資0~100分數 **/
	public BigDecimal getScr_m7() {
		return this.scr_m7;
	}
	/** 設定年資0~100分數 **/
	public void setScr_m7(BigDecimal value) {
		this.scr_m7 = value;
	}

	/** 取得年資std **/
	public BigDecimal getStd_m7() {
		return this.std_m7;
	}
	/** 設定年資std **/
	public void setStd_m7(BigDecimal value) {
		this.std_m7 = value;
	}

	/** 取得年資權重 **/
	public BigDecimal getWeight_m7() {
		return this.weight_m7;
	}
	/** 設定年資權重 **/
	public void setWeight_m7(BigDecimal value) {
		this.weight_m7 = value;
	}

	/** 取得ICR_NA {0:填寫ICR, 1:借款目的為開立保證函, 2:未進行ICR分析} **/
	public String getItem_d1_na() {
		return this.item_d1_na;
	}
	/** 設定ICR_NA {0:填寫ICR, 1:借款目的為開立保證函, 2:未進行ICR分析} **/
	public void setItem_d1_na(String value) {
		this.item_d1_na = value;
	}

	/** 取得ICR **/
	public BigDecimal getItem_d1_icr() {
		return this.item_d1_icr;
	}
	/** 設定ICR **/
	public void setItem_d1_icr(BigDecimal value) {
		this.item_d1_icr = value;
	}

	/** 取得ICR 0~100分數 **/
	public BigDecimal getScr_d1() {
		return this.scr_d1;
	}
	/** 設定ICR 0~100分數 **/
	public void setScr_d1(BigDecimal value) {
		this.scr_d1 = value;
	}

	/** 取得ICR std **/
	public BigDecimal getStd_d1() {
		return this.std_d1;
	}
	/** 設定ICR std **/
	public void setStd_d1(BigDecimal value) {
		this.std_d1 = value;
	}

	/** 取得ICR權重 **/
	public BigDecimal getWeight_d1() {
		return this.weight_d1;
	}
	/** 設定ICR權重 **/
	public void setWeight_d1(BigDecimal value) {
		this.weight_d1 = value;
	}
	
	/** 取得夫妻年收入幣別 **/
	public String getItem_p3_curr() {
		return this.item_p3_curr;
	}	
	/** 設定夫妻年收入幣別 **/
	public void setItem_p3_curr(String value) {
		this.item_p3_curr = value;
	}
	
	/** 取得夫妻年收入 **/
	public BigDecimal getItem_p3() {
		return this.item_p3;
	}
	/** 設定夫妻年收入 **/
	public void setItem_p3(BigDecimal value) {
		this.item_p3 = value;
	}

	/** 取得夫妻年收入0~100分數 **/
	public BigDecimal getScr_p3() {
		return this.scr_p3;
	}
	/** 設定夫妻年收入0~100分數 **/
	public void setScr_p3(BigDecimal value) {
		this.scr_p3 = value;
	}

	/** 取得夫妻年收入std **/
	public BigDecimal getStd_p3() {
		return this.std_p3;
	}
	/** 設定夫妻年收入std **/
	public void setStd_p3(BigDecimal value) {
		this.std_p3 = value;
	}

	/** 取得夫妻年收入權重 **/
	public BigDecimal getWeight_p3() {
		return this.weight_p3;
	}
	/** 設定夫妻年收入權重 **/
	public void setWeight_p3(BigDecimal value) {
		this.weight_p3 = value;
	}

	/** 取得契約年限 **/
	public BigDecimal getItem_a5() {
		return this.item_a5;
	}
	/** 設定契約年限 **/
	public void setItem_a5(BigDecimal value) {
		this.item_a5 = value;
	}

	/** 取得契約年限0~100分數 **/
	public BigDecimal getScr_a5() {
		return this.scr_a5;
	}
	/** 設定契約年限0~100分數 **/
	public void setScr_a5(BigDecimal value) {
		this.scr_a5 = value;
	}

	/** 取得契約年限std **/
	public BigDecimal getStd_a5() {
		return this.std_a5;
	}
	/** 設定契約年限std **/
	public void setStd_a5(BigDecimal value) {
		this.std_a5 = value;
	}

	/** 取得契約年限權重 **/
	public BigDecimal getWeight_a5() {
		return this.weight_a5;
	}
	/** 設定契約年限權重 **/
	public void setWeight_a5(BigDecimal value) {
		this.weight_a5 = value;
	}

	/** 取得VedaScore **/
	public Integer getItem_o1() {
		return this.item_o1;
	}
	/** 設定VedaScore **/
	public void setItem_o1(Integer value) {
		this.item_o1 = value;
	}

	/** 取得VedaScore 0~100分數 **/
	public BigDecimal getScr_o1() {
		return this.scr_o1;
	}
	/** 設定VedaScore 0~100分數 **/
	public void setScr_o1(BigDecimal value) {
		this.scr_o1 = value;
	}

	/** 取得VedaScorestd **/
	public BigDecimal getStd_o1() {
		return this.std_o1;
	}
	/** 設定VedaScorestd **/
	public void setStd_o1(BigDecimal value) {
		this.std_o1 = value;
	}

	/** 取得VedaScore權重 **/
	public BigDecimal getWeight_o1() {
		return this.weight_o1;
	}
	/** 設定VedaScore權重 **/
	public void setWeight_o1(BigDecimal value) {
		this.weight_o1 = value;
	}

	/** 取得擔保品地點及種類 **/
	public Integer getItem_z1() {
		return this.item_z1;
	}
	/** 設定擔保品地點及種類 **/
	public void setItem_z1(Integer value) {
		this.item_z1 = value;
	}

	/** 取得擔保品地點及種類0~100分數 **/
	public BigDecimal getScr_z1() {
		return this.scr_z1;
	}
	/** 設定擔保品地點及種類0~100分數 **/
	public void setScr_z1(BigDecimal value) {
		this.scr_z1 = value;
	}

	/** 取得擔保品地點及種類std **/
	public BigDecimal getStd_z1() {
		return this.std_z1;
	}
	/** 設定擔保品地點及種類std **/
	public void setStd_z1(BigDecimal value) {
		this.std_z1 = value;
	}

	/** 取得擔保品地點及種類權重 **/
	public BigDecimal getWeight_z1() {
		return this.weight_z1;
	}
	/** 設定擔保品地點及種類權重 **/
	public void setWeight_z1(BigDecimal value) {
		this.weight_z1 = value;
	}

	/** 取得市場環境及變現性 **/
	public Integer getItem_z2() {
		return this.item_z2;
	}
	/** 設定市場環境及變現性 **/
	public void setItem_z2(Integer value) {
		this.item_z2 = value;
	}

	/** 取得市場環境及變現性 0~100分數 **/
	public BigDecimal getScr_z2() {
		return this.scr_z2;
	}
	/** 設定市場環境及變現性 0~100分數 **/
	public void setScr_z2(BigDecimal value) {
		this.scr_z2 = value;
	}

	/** 取得市場環境及變現性std **/
	public BigDecimal getStd_z2() {
		return this.std_z2;
	}
	/** 設定市場環境及變現性std **/
	public void setStd_z2(BigDecimal value) {
		this.std_z2 = value;
	}

	/** 取得市場環境及變現性權重 **/
	public BigDecimal getWeight_z2() {
		return this.weight_z2;
	}
	/** 設定市場環境及變現性權重 **/
	public void setWeight_z2(BigDecimal value) {
		this.weight_z2 = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得違約機率(預估3年期) **/
	public BigDecimal getDr_3yr() {
		return this.dr_3yr;
	}
	/** 設定違約機率(預估3年期) **/
	public void setDr_3yr(BigDecimal value) {
		this.dr_3yr = value;
	}

	/** 取得違約機率(預估1年期) **/
	public BigDecimal getDr_1yr() {
		return this.dr_1yr;
	}
	/** 設定違約機率(預估1年期) **/
	public void setDr_1yr(BigDecimal value) {
		this.dr_1yr = value;
	}

	/** 取得升降等-依風險點數{升等1；降等-1} **/
	public Integer getAdj_pts() {
		return this.adj_pts;
	}
	/** 設定升降等-依風險點數{升等1；降等-1} **/
	public void setAdj_pts(Integer value) {
		this.adj_pts = value;
	}

	/** 取得升降等-依特殊警訊 **/
	public Integer getAdj_sw() {
		return this.adj_sw;
	}
	/** 設定升降等-依特殊警訊 **/
	public void setAdj_sw(Integer value) {
		this.adj_sw = value;
	}

	/** 取得升降等-依其他資訊 **/
	public Integer getAdj_oi() {
		return this.adj_oi;
	}
	/** 設定升降等-依其他資訊 **/
	public void setAdj_oi(Integer value) {
		this.adj_oi = value;
	}
	
	/** 取得學歷 **/
	public String getItem_edu() {
		return this.item_edu;
	}
	/** 設定學歷 **/
	public void setItem_edu(String value) {
		this.item_edu = value;
	}

	/** 取得學歷分數 **/
	public BigDecimal getScr_edu() {
		return this.scr_edu;
	}
	/** 設定學歷分數 **/
	public void setScr_edu(BigDecimal value) {
		this.scr_edu = value;
	}
	
	/** 取得學歷權重 **/
	public BigDecimal getWeight_edu() {
		return this.weight_edu;
	}
	/** 設定學歷權重 **/
	public void setWeight_edu(BigDecimal value) {
		this.weight_edu = value;
	}
	
	/** 取得學歷權重分數 **/
	public BigDecimal getWeight_scr_edu() {
		return this.weight_scr_edu;
	}
	/** 設定學歷權重分數 **/
	public void setWeight_scr_edu(BigDecimal weight_scr_edu) {
		this.weight_scr_edu = weight_scr_edu;
	}

	/** 取得個人負債比 **/
	public BigDecimal getItem_drate() {
		return this.item_drate;
	}
	/** 設定個人負債比 **/
	public void setItem_drate(BigDecimal value) {
		this.item_drate = value;
	}

	/** 取得個人負債比分數 **/
	public BigDecimal getScr_drate() {
		return this.scr_drate;
	}
	/** 設定個人負債比分數 **/
	public void setScr_drate(BigDecimal value) {
		this.scr_drate = value;
	}
	
	/** 取得個人負債比權重 **/
	public BigDecimal getWeight_drate() {
		return this.weight_drate;
	}
	/** 設定個人負債比權重 **/
	public void setWeight_drate(BigDecimal value) {
		this.weight_drate = value;
	}
	
	/** 取得個人負債比權重分數 **/
	public BigDecimal getWeight_scr_drate() {
		return this.weight_scr_drate;
	}
	/** 設定個人負債比權重分數 **/
	public void setWeight_scr_drate(BigDecimal weight_scr_drate) {
		this.weight_scr_drate = weight_scr_drate;
	}
	
	/** 取得年齡權重分數**/
	public BigDecimal getWeight_scr_m1() {
		return this.weight_scr_m1;
	}
	/** 設定年齡權重分數 **/
	public void setWeight_scr_m1(BigDecimal weight_scr_m1) {
		this.weight_scr_m1 = weight_scr_m1;
	}

	/** 取得年資權重分數**/
	public BigDecimal getWeight_scr_m7() {
		return this.weight_scr_m7;
	}
	/** 設定年資權重分數 **/
	public void setWeight_scr_m7(BigDecimal weight_scr_m7) {
		this.weight_scr_m7 = weight_scr_m7;
	}
	
	/** 取得契約年限權重分數**/
	public BigDecimal getWeight_scr_a5() {
		return this.weight_scr_a5;
	}
	/** 設定契約年限權重分數 **/
	public void setWeight_scr_a5(BigDecimal weight_scr_a5) {
		this.weight_scr_a5 = weight_scr_a5;
	}
	
	/** 取得職業權重分數**/
	public BigDecimal getWeight_scr_m5() {
		return this.weight_scr_m5;
	}
	/** 設定職業權重分數 **/
	public void setWeight_scr_m5(BigDecimal weight_scr_m5) {
		this.weight_scr_m5 = weight_scr_m5;
	}
	
	/** 取得年收入權重分數**/
	public BigDecimal getWeight_scr_p3() {
		return this.weight_scr_p3;
	}
	/** 設定年收入權重分數 **/
	public void setWeight_scr_p3(BigDecimal weight_scr_p3) {
		this.weight_scr_p3 = weight_scr_p3;
	}
	
	/** 取得擔保品種類權重分數**/
	public BigDecimal getWeight_scr_z1() {
		return this.weight_scr_z1;
	}
	/** 設定擔保品種類權重分數 **/
	public void setWeight_scr_z1(BigDecimal weight_scr_z1) {
		this.weight_scr_z1 = weight_scr_z1;
	}
	
	/** 取得市場環境及變現性權重分數 **/
	public BigDecimal getWeight_scr_z2() {
		return this.weight_scr_z2;
	}
	/** 設定市場環境及變現性權重分數 **/
	public void setWeight_scr_z2(BigDecimal weight_scr_z2) {
		this.weight_scr_z2 = weight_scr_z2;
	}
	
	
	/**
	 * 取得預測壞率
	 * <p/>
	 * 計算：小數點4 位，四捨五入<br/>
	 * (1/(1+Exp(scrNum13 – A/B)))
	 */
	public BigDecimal getPd() {
		return this.pd;
	}

	/**
	 * 設定預測壞率
	 * <p/>
	 * 計算：小數點4 位，四捨五入<br/>
	 * (1/(1+Exp(scrNum13 – A/B)))
	 **/
	public void setPd(BigDecimal value) {
		this.pd = value;
	}
	
	/** 
	 * 取得截距<p/> 
	 * 日本房貸2.0
	 */
	public BigDecimal getInterCept() {
		return this.interCept;
	}
	/**
	 *  設定截距<p/> 
	 *  日本房貸2.0
	 **/
	public void setInterCept(BigDecimal value) {
		this.interCept = value;
	}

	/** 
	 * 取得斜率<p/> 
	 * 日本房貸2.0
	 */
	public BigDecimal getSlope() {
		return this.slope;
	}
	/**
	 *  設定斜率<p/> 
	 *  日本房貸2.0
	 **/
	public void setSlope(BigDecimal value) {
		this.slope = value;
	}

	/**
	 * join
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C120M01A c120m01a;

	public void setC120m01a(C120M01A c120m01a) {
		this.c120m01a = c120m01a;
	}

	public C120M01A getC120m01a() {
		return c120m01a;
	}
}
