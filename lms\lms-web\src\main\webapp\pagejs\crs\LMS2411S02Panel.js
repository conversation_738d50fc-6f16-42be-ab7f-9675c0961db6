var initDfd = initDfd || new $.Deferred();
initDfd.done(function(json){
	var initControl_lockDoc = json['initControl_lockDoc'];
	
	if(json.retrialYN=="Y"){
		$("#prompt_retrialDate").show();
	}else{
		$("#retrialDate").removeClass( "required" )
	}
	
	if(json.docKind=="N"){
		if(initControl_lockDoc){
			
		}else{
			$("#div_btn_importBefText").show();	
		}
	}
	
	var $gridviewC241M01B = null;
	var $gridviewGrpDetail = null;
	if($("#PanelG").length>0){
		buildPanelG();
		buildPanelG_attch(json);
	}
	if( $("#PanelN").length>0 ||  $("#PanelP").length>0 ){
		buildPanelN();
	}
	
	if( $("#table_C241M01F").length>0 ){
		if(json.c241m01f_list){
			var dyna = []
			dyna.push("<table border='1' width='95%' class='tb2'>");
			if(true){
				dyna.push("<tr>");
				dyna.push("<td class='hd2' >"+"簽報書案號"+"</td>");
				dyna.push("<td class='hd2' >"+"本案引介人"+"</td>");
				dyna.push("<td class='hd2' >"+"經辦人員"+"</td>");
				dyna.push("<td class='hd2' >"+"覆核主管"+"</td>");
				dyna.push("</tr>");				
			}				
			$.each(json.c241m01f_list.key, function(idx, jsonItem) {
				
				dyna.push("<tr style='vertical-align:top;'>");
				dyna.push("<td >"+(jsonItem.m01f_caseInfo||'')+"&nbsp;</td>");
				dyna.push("<td >"+(jsonItem.m01f_rel_type_1||'')+"&nbsp;</td>");
				dyna.push("<td >"+(jsonItem.m01f_rel_type_2||'')+"&nbsp;</td>");
				dyna.push("<td >"+(jsonItem.m01f_rel_type_3||'')+"&nbsp;</td>");
				dyna.push("</tr>");
			});	
			dyna.push("</table>");			
			
			$("#table_C241M01F").html(dyna.join(""));		
		}
	}
	
	function buildPanelN(){		
		$gridviewC241M01B = $("#gridviewC241M01B").iGrid({
	        handler: 'lms2411gridhandler',        
	        height: 120,
	        postData: {
	        	mainOid: json.mainOid,
	            formAction: "queryC241M01B"
	        },
	        needPager: false,        
	        shrinkToFit: false,       
	        colModel: [
	          {//"覆審",
	            colHeader: i18n.lms2411m01['grid.ynReview'], name: 'ynReview', width: 30, sortable: true, align: "center",
	            formatter: function(cellvalue, options, rowObject){
	                if (cellvalue == "Y") {
	                    return "<img src='webroot/img/lms/V.png'>";
	                }else if (cellvalue == "N") {
	                	return "<img src='webroot/img/lms/X.png'>";
	                }else{
	                	return cellvalue;
	                }
	            } 
	          },{//"性質"
	            colHeader: i18n.lms2411m01['grid.quotaType'], name: 'quotaType', width: 40, sortable: true, align: "left"
	          },{//"額度序號"
	            colHeader: i18n.lms2411m01['C241M01B.quotaNo'], name: 'quotaNo', width: 90, sortable: true, align: "left"
	          },{//"帳號"
	            colHeader: i18n.lms2411m01['grid.loanNo'], name: 'loanNo', width: 100, sortable: true, align: "left", formatter: 'click', onclick: gridClickLNDetail
	          }, {//"科目",
	            colHeader: i18n.lms2411m01['grid.subjectDesc'], name: 'subjectDesc', width: 100, sortable: false, align: "left"
	          }, {//"動用/契約起日",
	            colHeader: i18n.lms2411m01['grid.use_loan_dateF'], name: 'use_loan_dateF', width: 100, sortable: false, align: "center"
	          }, { //"循環",
	        	colHeader: i18n.lms2411m01['grid.revolve'], name: 'reVolve', width: 30, sortable: true, align: "left"
	          }, {
			    colHeader: " ", name: 'quotaCurr', width: 30, sortable: true, align: "left"
	          }, { //"額度",
		        colHeader: i18n.lms2411m01['grid.totQuota'], name: 'quotaAmt', width: 100, sortable: true, align: "right"
	          }, {
				colHeader: " ", name: 'showBalCurr', width: 30, sortable: true, align: "left"
	          }, { //"餘額",
		        colHeader: i18n.lms2411m01['grid.totBal'], name: 'showBalAmt', width: 100, sortable: true, align: "right"
	          }, { //"擔保品類別",
		        colHeader: i18n.lms2411m01['C241M01B.guaranteeKind'], name: 'guaranteeKind', width: 100, sortable: true, align: "left"
	          }, { //"前次覆審日期",
			        colHeader: i18n.lms2411m01['C241M01B.dateOfReview'], name: 'dateOfReview', width: 100, sortable: true, align: "left"
	         }	        
	        , { name: 'oid', hidden: true }
	        , { name: 'mainId', hidden: true }
	        ],        
	        ondblClickRow: function(rowid){
	        	gridClickLNDetail(null, null, $("#gridviewC241M01B").getRowData(rowid));
	        }        
	    });		
	}
	function gridClickLNDetail(cellvalue, options, rowObject){
		var c241m01b_oid = rowObject.oid;
		
		var _id = "_divId_LNDetail";
		if ($("#"+_id).length == 0){
			$('body').append("<div id='"+_id+"' style='display:none;'></div>");		
		}
		$("#"+_id).empty();//clear
		
		var param = {'c241m01b_oid':c241m01b_oid};
		$("#"+_id).load("../lms2411m02", param,  function() {
			//隱碼headerarea
			$("#"+_id).find("#headerarea").hide();
			$("#"+_id).find(".body").css("width", "850px");
			
			$.ajax({
		        handler: _handler,
		        data: $.extend({}, param, {
                    formAction: "queryLNDetail"
                })
		        }).done(function(json_queryLNDetail){
		        	//---
		        	//將 radio button、select box 先建好
		        	$("#"+_id).find("#lnForm").find("#quotaType").setItems({
	        			width : "100%",
	        			border : "none",
	             	    size : 4,
	             	    item : json_queryLNDetail.addt.quotaType
	             	});
		        	
		        	if(json_queryLNDetail.haveBeenUpload=="Y"){
		        		//已上傳過的案件,不可由「覆審」→「不覆審」
		        		$("#"+_id).find("#lnForm").find("[name=ynReview]").prop("disabled", "true");
		        	}
		        	//---
		        	$("#"+_id).find("#lnForm").injectData(json_queryLNDetail);
					//---
					
					if(initControl_lockDoc){
						$("#"+_id).find("#lnForm").lockDoc();
						$("#buttonPanelLNDetail").find("#btnSaveLNDetail").addClass(" ui-state-disabled ").prop("disabled", true);
					}
		        	
		        	var pagesize = tb_getPageSize();
					var x = pagesize[0] - 60;
					if(x>900){
						x = 900;
					}
					var y = pagesize[1] - 40;
		        	$("#"+_id).thickbox({
		 		       title: i18n.lms2411m01["labe.lnDetail"], //帳務明細
		 		       width: x,
		 	           height: y,
		 	           align: "center",
		 	           valign: "bottom",
		 	           modal: false,
		 	           i18n: i18n.def,
		 	           buttons: {}
		 			});
		    });
			
		});
		
	}
	function buildPanelG(){
		$gridviewGrpDetail = $("#gridviewGrpDetail").iGrid({
	        handler: 'lms2401gridhandler',        
	        height: 180,
	        postData: {
	        	grpCntrNo: json.grpCntrNo,
	        	c240m01a_mainId: json.c240m01a_mainId,
	            formAction: "queryListUnderGrpCntrNo"
	        },
	        needPager: false,        
	        shrinkToFit: false,
	        multiselect: true,        
	        colModel: [
	          {//"序號",
	            colHeader:i18n.lms2411m01['grid.projectNo'], name: 'projectNo', width: 30, sortable: true, align: "cetner" 
	          }, {//"覆審",
	            colHeader: i18n.lms2411m01['grid.retrialYN'], name: 'retrialYN', width: 30, sortable: true, align: "center",
	            formatter: function(cellvalue, options, rowObject){
	                if (cellvalue == "Y") {
	                    return "<img src='webroot/img/lms/V.png'>";
	                }else if (cellvalue == "N") {
	                	return "<img src='webroot/img/lms/X.png'>";
	                }else{
	                	return cellvalue;
	                }
	            } 
	          },{//"客戶統編"
	            colHeader: i18n.lms2411m01['grid.custId'], name: 'custId_dupNo', width: 90, sortable: true, align: "left", formatter: 'click', onclick: gridClickUnderGrpCntrNo
	          },{//"客戶姓名",
	            colHeader: i18n.lms2411m01['grid.custName'], name: 'custName', width: 150, sortable: true, align: "left"
	          },{
		        colHeader: i18n.lms2411m01['grid.curr'],name: 'totBalCurr',width: 50,sortable: true, align: "center"
	          },{
	            colHeader: i18n.lms2411m01['grid.totBal'], name: 'totBal', width: 100, sortable: true, align: "right"	            	
	          },{//"覆審流程點",
	              colHeader: i18n.lms2411m01['grid.docStatusDesc'], name: 'docStatusDesc',width: 125, sortable: true,align: "left"            
	          },{//"上傳註記",
	              colHeader: i18n.lms2411m01['grid.uploadFlag'], name: 'uploadFlag',width: 15, sortable: true,align: "left"
	          }, {//"上傳人員",
	              colHeader: i18n.lms2411m01['label.c241m01a_approver'], name: 'approverStaff',width: 60, sortable: true,align: "left"
	          },{//"覆審人員",
	              colHeader: i18n.lms2411m01['grid.retrialStaff'], name: 'retrialStaff',width: 60, sortable: true,align: "left"
	          }, {
	              colHeader: "實地", name: '_realFmt',width: 30, sortable: false, align: "center"
	          }, {//"上次覆審日期",
	              colHeader: i18n.lms2401m01['C241M01A.lastRealDt'], name: 'lastRealDt', width: 65, sortable: true, align: "center"          
	          }       
	        , { name: 'movetobr_flag', hidden: true }
	        , { name: 'custId', hidden: true }
	        , { name: 'dupNo', hidden: true }   
	        , { name: 'oid', hidden: true }
	        , { name: 'mainId', hidden: true }	        
	        ],        
	        ondblClickRow: function(rowid){
	        	gridClickUnderGrpCntrNo(null, null, $("#gridviewGrpDetail").getRowData(rowid));
	        }        
	    });
	}
	
	function buildPanelG_attch(passParam){
		$.each(['grpDetailFile'], function(idx, divId) {
			var dyna = [];
			{
				var arr = passParam.attch[divId];
				$.each(arr, function(idx, jsonItem) {
					dyna.push("<span class='linkDocFile' oid='"+jsonItem.oid+"'>"+jsonItem.srcFileName+"</span>&nbsp;&nbsp;&nbsp;&nbsp;"
					+"("+jsonItem.uploadTime+")<sub class='delDocFile' oid='"+jsonItem.oid+"'>刪</sub>");					
				});	
			}					
			$("#"+divId).html(dyna.join("<br/>"));
		});	
		$('span.linkDocFile').click(function(){
			var oid = jQuery(this).attr("oid");
			
			$.capFileDownload({
		        handler:"simplefiledwnhandler",
		        data : {
		            fileOid:oid
		        }
			});				
		});
		$('sub.delDocFile').click(function(){
			var oid = jQuery(this).attr("oid");
			//ui_lms2411.msg05=是否刪除檔案?
			CommonAPI.confirmMessage(i18n.lms2411m01["ui_lms2411.msg05"], function(b){
	            if (b) {
	            	$.ajax({
                        handler: _handler,
                        data: {
                            formAction: "deleteUploadFile",
                            fileOid: oid
                        }
                        }).done(function(obj){
                        	buildPanelG_attch(obj);
                    });
	            }
	        });			
		});
	}
	function gridClickUnderGrpCntrNo(cellvalue, options, rowObject){
		$.ajax({ handler: 'lms2411m01formhandler', data: {formAction: "checkFlow", 'mainOid':rowObject.oid, 'act':'initFlow'} }).done(function(json_checkFlow){
			if(json_checkFlow.passedFlag=="Y"){
				$.ajax({ handler: 'lms2411m01formhandler', data: {formAction: "openPageParam", 'mainOid':rowObject.oid}
	                }).done(function(json_openPageParam){                    	
	                	var postData = {
	                		'mainOid': json_openPageParam.mainOid, 
	                		'mainId': json_openPageParam.mainId,
	                		'mainDocStatus': json_openPageParam.mainDocStatus
	                	}
	                	//若 A 已開啟工作底稿的001號覆審報告表,而 B 要開啟002號覆審報告表
	                	//在 B 的json_openPageParam 會多出{openerLockDoc:true}
	                	$.form.submit({ url:'../lms2411m01/02', data:postData, target:rowObject.oid});
	            });	
        }});				
	}
	
	//重引帳務資料
	$("#btn_importLN").click(function(){
		$.ajax({
           type: "POST",
           handler: _handler,
           data: {
               formAction: "importLNSingle",
               mainOid: $("#mainOid").val()
           }
           }).done(function(responseData){
        	var is_docKindP = false;
        	var overdueP = '';
        	buildOverdue(is_docKindP, responseData.overdueYN, overdueP);
        	
           	var tabForm = $("#tabForm");
			tabForm.injectData(responseData);
			
           	//更新 $gridviewC241M01B 的 Grid
			$gridviewC241M01B.trigger("reloadGrid");
			
			//一般戶 更新 LMS2401S02 的 Grid
           	CommonAPI.triggerOpener("gridview", "reloadGrid");
           	
            //團貸子戶 更新 母戶 的 Grid
           	CommonAPI.triggerOpener("gridviewGrpDetail", "reloadGrid");
           	
           	if(responseData.show_msg10=="Y"){
           		API.showMessage(i18n.lms2411m01["ui_lms2411.msg10"]);//已上傳過的案件，不更新帳務資料
           	}
       });
		
	});
	//調閱覆審報告
	$("#btn_printBefReviewDoc").click(function(){
		if(json.bef_oid.length==0){
			API.showMessage(i18n.lms2411m01["ui_lms2411.msg14"]);//於 Web e-Loan 未查到前次覆審報告表
		}else{
			printC241M01A( json.bef_oid+"^"+json.bef_mainId );	
		}
		
	});
	
	$("#crs_retrialKind_memo").click(function(){
		var _id = "_div_crs_retrialKind_memo";
		 	
		if ($("#"+_id).length == 0){
			var dyna = [];
 			dyna.push("<div id='"+_id+"' style='display:none;'>");			
 			dyna.push("	<table width='100%' border='1' >");
 			dyna.push("	<tr><td width='100px'>"+"覆審類別"+"</td><td>"+"說明"+"</td></tr>");
 			
			var jsonList = API.loadOrderCombosAsList("C241M01A_retrialKind")["C241M01A_retrialKind"]
			jsonList = convertItems(jsonList);
			$.each(jsonList, function(idx, jsonItem) {
				
				var k = jsonItem.value;
				var v = jsonItem.desc;
				
				dyna.push(" <tr><td>"+k+"</td><td>"+v+"</td></tr>");         		
			});			
			
 			dyna.push(" </table>");		 			
 			dyna.push("</div>");
 			
 		    $('body').append(dyna.join(""));		    
		}
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	       title: "",
	       width: 600,
           height: 500,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
	});
	
	var chkRptVersion = function(bef_diffVer){
		var my_dfd = $.Deferred();
		if(bef_diffVer=="Y"){
			CommonAPI.confirmMessage(i18n.lms2411m01["ui_lms2411.msg17"], function(b){
	            if (b) {
	            	my_dfd.resolve();    	
	            }else{
	            	my_dfd.reject();
	            }
	        });
    	}else{
    		my_dfd.resolve();
    	}
		return my_dfd.promise();
	}
	
	//匯入前次覆審意見資料
	$("#btn_importBefText").click(function(){
		if(json.bef_oid.length==0){
			API.showMessage(i18n.lms2411m01["ui_lms2411.msg14"]);//於 Web e-Loan 未查到前次覆審報告表
		}else{
			CommonAPI.confirmMessage(i18n.lms2411m01["ui_lms2411.msg16"], function(b){
	            if (b) {
	            	chkRptVersion( json.bef_diffVer ).done(function(){            		
	            		$.ajax({
				            handler: _handler,
				            type: "POST",
				            dataType: "json",
				            data: {
				                formAction: "importBefText",
				                mainOid: $("#mainOid").val(),
				                'bef_oid': json.bef_oid
				            }
				            }).done(function(json_produce_grpDetail_attch){
				            	API.showMessage(i18n.def.runSuccess);
				        }); 
	        		});
	            }
	        });
		}
	});
	//勾選列印
	$("#btn_print").click(function(){	
		var rowId_arr = $gridviewGrpDetail.getGridParam('selarrrow');
   	 	var oid_arr = [];
   	 	for (var i = 0; i < rowId_arr.length; i++) {
			var data = $gridviewGrpDetail.getRowData(rowId_arr[i]);
			oid_arr.push(data.oid+"^"+data.mainId);
        }

   	 	if(oid_arr.length==0){   	 		
   	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
   	 		return;
   	 	}
   	 	printC241M01A( oid_arr.join("|") );
   	 	
	});
	//匯出本案團貸所有明細
	$("#btn_exp_grpDetail").click(function(){
		$.ajax({
            handler: _handler,
            type: "POST",
            dataType: "json",
            data: {
                formAction: "produce_grpDetail_attch",
                mainOid: $("#mainOid").val()
            }
            }).done(function(json_produce_grpDetail_attch){
            	buildPanelG_attch(json_produce_grpDetail_attch);
        });
		
	});
	//新增團貸子戶
	$("#btn_add_grpDetail").click(function(){
		AddCustAction.open({
    		handler: _handler,
			action : 'produceNewGrpDetail',
			data : {
                mainOid: $("mainOid").val()
            },
			callback : function(responseData){
				$gridviewGrpDetail.trigger("reloadGrid");
            	
            	//關掉 AddCustAction 的 thickbox
            	$.thickbox.close();
			}
		});
		
	});
	//勾選資料送受檢單位登錄
	$("#btn_movetobr_grpDetail").click(function(){
		var rowId_arr = $gridviewGrpDetail.getGridParam('selarrrow');
   	 	var oid_arr = [];
   	 	var not_oid_arr = [];
   	 	for (var i = 0; i < rowId_arr.length; i++) {
			var data = $gridviewGrpDetail.getRowData(rowId_arr[i]);
			if(data.movetobr_flag=="Y"){
      			oid_arr.push(data.oid);
      		}else{
      			not_oid_arr.push(data.custId+" "+data.custName);
      		}
        }

	   	if(not_oid_arr.length > 0 ){
	   	    //ui_lms2401.msg08=未維護覆審報告表
	   		API.showMessage(not_oid_arr.join("、")+i18n.lms2401m01['ui_lms2401.msg08']);
	   		return;
	   	}
	   	if(oid_arr.length==0){
	   		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
	   		return;
	   	}
	   	//區中心_編製中 的代碼為 01A
      	//區中心_待覆核 的代碼為 02A
      	var param = {'mainDocStatus':'02A', 'decisionExpr':'to_編製中_分行端', 'addMetaDesc':'Y'};      	
      	//===================================
      	var cnt = 0;
		var target = oid_arr.length;
			
		var theQueue = $({});
		$.each(oid_arr,function(k, v_oid) {                                            			
			theQueue.queue('myqueue', function(next) {
				$.ajax({
		            type: "POST",
		            handler: "lms2411m01formhandler", action: "flowAction",
		            data:$.extend( { mainOid: v_oid } , param )                
		            }).done(function(json){ }
		        ).complete(function(){//用 complete(不論done, fail)
		        	cnt++;
  					if(cnt==target){  						
  						//更新 Grid
  						$gridviewGrpDetail.trigger("reloadGrid");  		            	  
      				}
  					//---
  					//把 next() 寫在 finish 的 callback 裡
  					//才會 1個 url 抓到 response 後,再get下1個
  					//不然會1次跳 N 個出來
  					next();
		        });				
			});                                            			 
		});
		theQueue.dequeue('myqueue');
		
	});
});
