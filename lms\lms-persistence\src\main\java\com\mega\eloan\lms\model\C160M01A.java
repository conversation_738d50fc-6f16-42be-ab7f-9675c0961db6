/* 
 * C160M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.bval.constraints.NotEmpty;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.Check2;
import com.mega.eloan.lms.validation.group.Check3;

import tw.com.iisi.cap.model.IDataObject;

/** 動用審核表主檔 **/
@NamedEntityGraph(name = "C160M01A-entity-graph", attributeNodes = { 
		@NamedAttributeNode("c160a01a"),
		@NamedAttributeNode("c160m01d")
		})
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C160M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C160M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * 來源註記
	 * <p/>
	 * 100/12/20新增<br/>
	 * 1.案件簽報書<br/>
	 * 2.聯行額度明細表
	 */
	@Size(max = 1)
	@Column(name = "DATASRC", length = 1, columnDefinition = "CHAR(1)")
	private String dataSrc;

	/**
	 * 來源文件編號
	 * <p/>
	 * 100/12/20調整<br/>
	 * caseType=1|一般, 來源簽報書/聯行額度明細表的文件編號<br/>
	 * 來源：L120M01A.mainId<br/>
	 * 來源：L141M01A.mainId<br/>
	 * caseType=2|團貸 => empty <br/>
	 * caseType=3|整批匯入 => empty
	 */
	@Size(max = 32)
	@Column(name = "SRCMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String srcMainId;

	/**
	 * 案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "CASEYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer caseYear;

	/**
	 * 案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Size(max = 3)
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/**
	 * 是否先行動用
	 * <p/>
	 * 100/10/17新增<br/>
	 * Y/N 用來判斷是否有先行動用表
	 */
	@Size(max = 1)
	@Column(name = "USETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String useType;

	/**
	 * 案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "CASESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer caseSeq;

	/**
	 * 案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@NotNull(message = "{required.message}", groups = Check2.class)
	@NotEmpty(message = "{required.message}", groups = Check2.class)
	@Size(max = 62)
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/**
	 * 簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/**
	 * 案件審核層級
	 * <p/>
	 * 100/10/06新增<br/>
	 * 資料來源：案件簽報書<br/>
	 * 常董會權限、常董會權限簽奉總經理核批、常董會權限簽准由副總經理核批、利費率變更案件由總處經理核定、屬常董會授權總經理逕核案件、總經理權限內、
	 * 副總經理權限、企金部協理權限、其他、董事會權限、利費率變更案件由董事長核定、個金處經理權限、區域營運中心主任/副主任權限
	 */
	@Size(max = 2)
	@Column(name = "CASELVL", length = 2, columnDefinition = "CHAR(2)")
	private String caseLvl;

	/**
	 * 動審表種類
	 * <p/>
	 * 1|一般<br/>
	 * 2|團貸<br/>
	 * 3|整批匯入
	 */
	@Size(max = 1)
	@Column(name = "CASETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String caseType;

	/**
	 * 簽報書項下額度明細表是否全部動用
	 * <p/>
	 * Y/N<br/>
	 * 如為Y則固定顯示："簽報書項下額度明細表全部動用"；如為N則顯示所有額度序號。
	 */
	@Size(max = 1)
	@Column(name = "ALLCANPAY", length = 1, columnDefinition = "CHAR(1)")
	private String allCanPay;

	/**
	 * 團貸資訊-總額度申請年度
	 * <p/>
	 * caseType =2<br/>
	 * eg.100
	 */
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "PARENTYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer parentYear;

	/**
	 * 團貸資訊-總戶序號
	 * <p/>
	 * caseType =2<br/>
	 * eg.918110001739
	 */
	@Size(max = 12)
	@Column(name = "LOANMASTERNO", length = 12, columnDefinition = "CHAR(12)")
	private String loanMasterNo;

	/**
	 * 團貸資訊-批號
	 * <p/>
	 * caseType =2<br/>
	 * eg.01
	 */
	@Size(max = 10)
	@Column(name = "LOANPACKNO", length = 10, columnDefinition = "VARCHAR(10)")
	private String loanPackNo;

	/**
	 * 團貸資訊-簽案分行
	 * <p/>
	 * caseType =2
	 */
	@Size(max = 3)
	@Column(name = "ISSUEBRNO", length = 3, columnDefinition = "CHAR(3)")
	private String issueBrNo;

	/**
	 * 本案核准之編號
	 * <p/>
	 * caseType =3
	 */
	@Size(max = 12)
	@Column(name = "APPROVEDNO", length = 12, columnDefinition = "VARCHAR(12)")
	private String approvedNo;

	/**
	 * 批號
	 * <p/>
	 * caseType =2<br/>
	 * caseType =3
	 */
	@Size(max = 10)
	@Column(name = "PACKNO", length = 10, columnDefinition = "VARCHAR(10)")
	private String packNo;

	/**
	 * Excel ID
	 * <p/>
	 * Excel file id
	 */
	@Size(max = 32)
	@Column(name = "EXCELID", length = 32, columnDefinition = "CHAR(32)")
	private String excelId;

	/**
	 * 是否流用旗下子公司
	 * <p/>
	 * 是|Y<br/>
	 * 否|N
	 */
	@Size(max = 1)
	@Column(name = "ISUSECHILDREN", length = 1, columnDefinition = "CHAR(1)")
	private String isUseChildren;

	/** 子公司統編 **/
	@Size(max = 10)
	@Column(name = "CHILDRENID", length = 10, columnDefinition = "VARCHAR(10)")
	private String childrenId;

	/** 子公司名稱 **/
	@Size(max = 120)
	@Column(name = "CHILDRENNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String childrenName;

	/** 流用序號 **/
	@Size(max = 12)
	@Column(name = "CHILDRENSEQ", length = 12, columnDefinition = "VARCHAR(12)")
	private String childrenSeq;

	/** 完成筆數 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "FINCOUNT", columnDefinition = "DECIMAL(4,0)")
	private Integer finCount;

	/** 總筆數 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "TOTCOUNT", columnDefinition = "DECIMAL(4,0)")
	private Integer totCount;

	/**
	 * 批示
	 * <p/>
	 * 1024個全型字
	 */
	@Size(max = 3072)
	@Column(name = "SIGN", length = 3072, columnDefinition = "VARCHAR(3072)")
	private String sign;

	/**
	 * 審核意見
	 * <p/>
	 * 預設：貸放手續齊全，擬准予動用<br/>
	 * 1024個全型字
	 */
	@NotNull(message = "{required.message}", groups = Check3.class)
	@NotEmpty(message = "{required.message}", groups = Check3.class)
	@Size(max = 3072)
	@Column(name = "COMM", length = 3072, columnDefinition = "VARCHAR(3072)")
	private String comm;

	/**
	 * 總行經辦
	 * <p/>
	 * 當為授權外時918的覆核主管
	 */
	@Size(max = 6)
	@Column(name = "MAINAPPRID", length = 6, columnDefinition = "CHAR(6)")
	private String mainApprId;

	/**
	 * 總行覆核主管
	 * <p/>
	 * 當為授權外時918的覆核主管
	 */
	@Size(max = 6)
	@Column(name = "MAINMANAGERID", length = 6, columnDefinition = "CHAR(6)")
	private String mainManagerId;

	/** 經辦 **/
	@Size(max = 6)
	@Column(name = "APPRID", length = 6, columnDefinition = "CHAR(6)")
	private String apprId;

	/** 覆核主管 **/
	@Size(max = 6)
	@Column(name = "RECHECKID", length = 6, columnDefinition = "CHAR(6)")
	private String reCheckId;

	/**
	 * 授信主管
	 * <p/>
	 * 呈主管覆核時，勾選授信主管名單
	 */
	@Size(max = 6)
	@Column(name = "BOSSID", length = 6, columnDefinition = "CHAR(6)")
	private String bossId;

	/**
	 * 經副襄理
	 * <p/>
	 * 呈主管覆核時，勾選經副襄理名單
	 */
	@Size(max = 6)
	@Column(name = "MANAGERID", length = 6, columnDefinition = "CHAR(6)")
	private String managerId;

	/**
	 * RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	@Size(max = 32)
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;
	
	/**
	 * BUILDNAME
	 * 專案名稱
	 */
	@Size(max = 120)
	@Column(name = "BUILDNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String buildName;

	/** 動審表總額度 **/
	@Digits(integer = 15, fraction = 0)
	@Column(name = "TOTAMT", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal totAmt;
	
	/** 原始動審表總額度 **/
	@Digits(integer = 15, fraction = 0)
	@Column(name = "BFTOTAMT", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal bfTotAmt;
	
	/** AML RefNo' */
	@Column(name = "AMLREFNO", length = 28, columnDefinition = "VARCHAR(28)")
	private String amlRefNo;
	
	@Column(name = "ORGAPPROVETIME", columnDefinition = "TIMESTAMP")
	private Date orgApproveTime;
	
	/**新貸款戶註記*/
	@Column(name="NEWCUSTFLAG", length=1, columnDefinition="CHAR(1)")
	private String newCustFlag;
	
	/**執行照會經辦*/
	@Column(name="NOTER", length=6, columnDefinition="CHAR(6)")
	private String noter;

	/**照會時間*/
	@Column(name="NOTETIME", columnDefinition="TIMESTAMP")
	private Timestamp noteTime;
	
	/**
	 * 取得來源註記
	 * <p/>
	 * 100/12/20新增<br/>
	 * 1.案件簽報書<br/>
	 * 2.聯行額度明細表
	 */
	public String getDataSrc() {
		return this.dataSrc;
	}

	/**
	 * 設定來源註記
	 * <p/>
	 * 100/12/20新增<br/>
	 * 1.案件簽報書<br/>
	 * 2.聯行額度明細表
	 **/
	public void setDataSrc(String value) {
		this.dataSrc = value;
	}

	/**
	 * 取得來源文件編號
	 * <p/>
	 * 100/12/20調整<br/>
	 * caseType=1|一般, 來源簽報書/聯行額度明細表的文件編號<br/>
	 * 來源：L120M01A.mainId<br/>
	 * 來源：L141M01A.mainId<br/>
	 * caseType=2|團貸 => empty <br/>
	 * caseType=3|整批匯入 => empty
	 */
	public String getSrcMainId() {
		return this.srcMainId;
	}

	/**
	 * 設定來源文件編號
	 * <p/>
	 * 100/12/20調整<br/>
	 * caseType=1|一般, 來源簽報書/聯行額度明細表的文件編號<br/>
	 * 來源：L120M01A.mainId<br/>
	 * 來源：L141M01A.mainId<br/>
	 * caseType=2|團貸 => empty <br/>
	 * caseType=3|整批匯入 => empty
	 **/
	public void setSrcMainId(String value) {
		this.srcMainId = value;
	}

	/**
	 * 取得案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseYear() {
		return this.caseYear;
	}

	/**
	 * 設定案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/**
	 * 取得案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/**
	 * 設定案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/**
	 * 取得是否先行動用
	 * <p/>
	 * 100/10/17新增<br/>
	 * Y/N 用來判斷是否有先行動用表
	 */
	public String getUseType() {
		return this.useType;
	}

	/**
	 * 設定是否先行動用
	 * <p/>
	 * 100/10/17新增<br/>
	 * Y/N 用來判斷是否有先行動用表
	 **/
	public void setUseType(String value) {
		this.useType = value;
	}

	/**
	 * 取得案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseSeq() {
		return this.caseSeq;
	}

	/**
	 * 設定案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/**
	 * 取得案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseNo() {
		return this.caseNo;
	}

	/**
	 * 設定案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/**
	 * 取得簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public Date getCaseDate() {
		return this.caseDate;
	}

	/**
	 * 設定簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/**
	 * 取得案件審核層級
	 * <p/>
	 * 100/10/06新增<br/>
	 * 資料來源：案件簽報書<br/>
	 * 常董會權限、常董會權限簽奉總經理核批、常董會權限簽准由副總經理核批、利費率變更案件由總處經理核定、屬常董會授權總經理逕核案件、總經理權限內、
	 * 副總經理權限、企金部協理權限、其他、董事會權限、利費率變更案件由董事長核定、個金處經理權限、區域營運中心主任/副主任權限
	 */
	public String getCaseLvl() {
		return this.caseLvl;
	}

	/**
	 * 設定案件審核層級
	 * <p/>
	 * 100/10/06新增<br/>
	 * 資料來源：案件簽報書<br/>
	 * 常董會權限、常董會權限簽奉總經理核批、常董會權限簽准由副總經理核批、利費率變更案件由總處經理核定、屬常董會授權總經理逕核案件、總經理權限內、
	 * 副總經理權限、企金部協理權限、其他、董事會權限、利費率變更案件由董事長核定、個金處經理權限、區域營運中心主任/副主任權限
	 **/
	public void setCaseLvl(String value) {
		this.caseLvl = value;
	}

	/**
	 * 取得動審表種類
	 * <p/>
	 * 1|一般<br/>
	 * 2|團貸<br/>
	 * 3|整批匯入
	 */
	public String getCaseType() {
		return this.caseType;
	}

	/**
	 * 設定動審表種類
	 * <p/>
	 * 1|一般<br/>
	 * 2|團貸<br/>
	 * 3|整批匯入
	 **/
	public void setCaseType(String value) {
		this.caseType = value;
	}

	/**
	 * 取得簽報書項下額度明細表是否全部動用
	 * <p/>
	 * Y/N<br/>
	 * 如為Y則固定顯示："簽報書項下額度明細表全部動用"；如為N則顯示所有額度序號。
	 */
	public String getAllCanPay() {
		return this.allCanPay;
	}

	/**
	 * 設定簽報書項下額度明細表是否全部動用
	 * <p/>
	 * Y/N<br/>
	 * 如為Y則固定顯示："簽報書項下額度明細表全部動用"；如為N則顯示所有額度序號。
	 **/
	public void setAllCanPay(String value) {
		this.allCanPay = value;
	}

	/**
	 * 取得團貸資訊-總額度申請年度
	 * <p/>
	 * caseType =2<br/>
	 * eg.100
	 */
	public Integer getParentYear() {
		return this.parentYear;
	}

	/**
	 * 設定團貸資訊-總額度申請年度
	 * <p/>
	 * caseType =2<br/>
	 * eg.100
	 **/
	public void setParentYear(Integer value) {
		this.parentYear = value;
	}

	/**
	 * 取得團貸資訊-總戶序號
	 * <p/>
	 * caseType =2<br/>
	 * eg.918110001739
	 */
	public String getLoanMasterNo() {
		return this.loanMasterNo;
	}

	/**
	 * 設定團貸資訊-總戶序號
	 * <p/>
	 * caseType =2<br/>
	 * eg.918110001739
	 **/
	public void setLoanMasterNo(String value) {
		this.loanMasterNo = value;
	}

	/**
	 * 取得團貸資訊-批號
	 * <p/>
	 * caseType =2<br/>
	 * eg.01
	 */
	public String getLoanPackNo() {
		return this.loanPackNo;
	}

	/**
	 * 設定團貸資訊-批號
	 * <p/>
	 * caseType =2<br/>
	 * eg.01
	 **/
	public void setLoanPackNo(String value) {
		this.loanPackNo = value;
	}

	/**
	 * 取得團貸資訊-簽案分行
	 * <p/>
	 * caseType =2
	 */
	public String getIssueBrNo() {
		return this.issueBrNo;
	}

	/**
	 * 設定團貸資訊-簽案分行
	 * <p/>
	 * caseType =2
	 **/
	public void setIssueBrNo(String value) {
		this.issueBrNo = value;
	}

	/**
	 * 取得本案核准之編號
	 * <p/>
	 * caseType =3
	 */
	public String getApprovedNo() {
		return this.approvedNo;
	}

	/**
	 * 設定本案核准之編號
	 * <p/>
	 * caseType =3
	 **/
	public void setApprovedNo(String value) {
		this.approvedNo = value;
	}

	/**
	 * 取得批號
	 * <p/>
	 * caseType =2<br/>
	 * caseType =3
	 */
	public String getPackNo() {
		return this.packNo;
	}

	/**
	 * 設定批號
	 * <p/>
	 * caseType =2<br/>
	 * caseType =3
	 **/
	public void setPackNo(String value) {
		this.packNo = value;
	}

	/**
	 * 取得Excel ID
	 * <p/>
	 * Excel file id
	 */
	public String getExcelId() {
		return this.excelId;
	}

	/**
	 * 設定Excel ID
	 * <p/>
	 * Excel file id
	 **/
	public void setExcelId(String value) {
		this.excelId = value;
	}

	/**
	 * 取得是否流用旗下子公司
	 * <p/>
	 * 是|Y<br/>
	 * 否|N
	 */
	public String getIsUseChildren() {
		return this.isUseChildren;
	}

	/**
	 * 設定是否流用旗下子公司
	 * <p/>
	 * 是|Y<br/>
	 * 否|N
	 **/
	public void setIsUseChildren(String value) {
		this.isUseChildren = value;
	}

	/** 取得子公司統編 **/
	public String getChildrenId() {
		return this.childrenId;
	}

	/** 設定子公司統編 **/
	public void setChildrenId(String value) {
		this.childrenId = value;
	}

	/** 取得子公司名稱 **/
	public String getChildrenName() {
		return this.childrenName;
	}

	/** 設定子公司名稱 **/
	public void setChildrenName(String value) {
		this.childrenName = value;
	}

	/** 取得流用序號 **/
	public String getChildrenSeq() {
		return this.childrenSeq;
	}

	/** 設定流用序號 **/
	public void setChildrenSeq(String value) {
		this.childrenSeq = value;
	}

	/** 取得完成筆數 **/
	public Integer getFinCount() {
		return this.finCount;
	}

	/** 設定完成筆數 **/
	public void setFinCount(Integer value) {
		this.finCount = value;
	}

	/** 取得總筆數 **/
	public Integer getTotCount() {
		return this.totCount;
	}

	/** 設定總筆數 **/
	public void setTotCount(Integer value) {
		this.totCount = value;
	}

	/**
	 * 取得批示
	 * <p/>
	 * 1024個全型字
	 */
	public String getSign() {
		return this.sign;
	}

	/**
	 * 設定批示
	 * <p/>
	 * 1024個全型字
	 **/
	public void setSign(String value) {
		this.sign = value;
	}

	/**
	 * 取得審核意見
	 * <p/>
	 * 預設：貸放手續齊全，擬准予動用<br/>
	 * 1024個全型字
	 */
	public String getComm() {
		return this.comm;
	}

	/**
	 * 設定審核意見
	 * <p/>
	 * 預設：貸放手續齊全，擬准予動用<br/>
	 * 1024個全型字
	 **/
	public void setComm(String value) {
		this.comm = value;
	}

	/**
	 * 取得總行經辦
	 * <p/>
	 * 當為授權外時918的覆核主管
	 */
	public String getMainApprId() {
		return this.mainApprId;
	}

	/**
	 * 設定總行經辦
	 * <p/>
	 * 當為授權外時918的覆核主管
	 **/
	public void setMainApprId(String value) {
		this.mainApprId = value;
	}

	/**
	 * 取得總行覆核主管
	 * <p/>
	 * 當為授權外時918的覆核主管
	 */
	public String getMainManagerId() {
		return this.mainManagerId;
	}

	/**
	 * 設定總行覆核主管
	 * <p/>
	 * 當為授權外時918的覆核主管
	 **/
	public void setMainManagerId(String value) {
		this.mainManagerId = value;
	}

	/** 取得經辦 **/
	public String getApprId() {
		return this.apprId;
	}

	/** 設定經辦 **/
	public void setApprId(String value) {
		this.apprId = value;
	}

	/** 取得覆核主管 **/
	public String getReCheckId() {
		return this.reCheckId;
	}

	/** 設定覆核主管 **/
	public void setReCheckId(String value) {
		this.reCheckId = value;
	}

	/**
	 * 取得授信主管
	 * <p/>
	 * 呈主管覆核時，勾選授信主管名單
	 */
	public String getBossId() {
		return this.bossId;
	}

	/**
	 * 設定授信主管
	 * <p/>
	 * 呈主管覆核時，勾選授信主管名單
	 **/
	public void setBossId(String value) {
		this.bossId = value;
	}

	/**
	 * 取得經副襄理
	 * <p/>
	 * 呈主管覆核時，勾選經副襄理名單
	 */
	public String getManagerId() {
		return this.managerId;
	}

	/**
	 * 設定經副襄理
	 * <p/>
	 * 呈主管覆核時，勾選經副襄理名單
	 **/
	public void setManagerId(String value) {
		this.managerId = value;
	}

	/**
	 * 取得RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}

	/**
	 * 設定RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}

	/**
	 * BUILDNAME
	 * 設定專案名稱
	 */
	public void setBuildName(String value) {
		this.buildName = value;
	}

	/**
	 * BUILDNAME
	 * 取得專案名稱
	 */
	public String getBuildName() {
		return this.buildName;
	}

	/** 設定動審表總額度 */
	public void setTotAmt(BigDecimal value) {
		this.totAmt = value;
	}

	/** 取得動審表總額度 */
	public BigDecimal getTotAmt() {
		return this.totAmt;
	}
	
	/** 設定原始動審表總額度 */
	public void setBfTotAmt(BigDecimal value) {
		this.bfTotAmt = value;
	}

	/** 取得原始動審表總額度 */
	public BigDecimal getBfTotAmt() {
		return this.bfTotAmt;
	}
	
	public void setAmlRefNo(String value) {
		this.amlRefNo = value;
	}
	public String getAmlRefNo() {
		return this.amlRefNo;
	}
	
	/**
	 * 設定首次主管核准日
	 * 
	 * @param orgApproveTime
	 */
	public void setOrgApproveTime(Date orgApproveTime) {
		this.orgApproveTime = orgApproveTime;
	}

	/**
	 * 取得首次主管核准日
	 * 
	 * @param orgApproveTime
	 */
	public Date getOrgApproveTime() {
		return orgApproveTime;
	}
	
	public String getNewCustFlag() {
		return newCustFlag;
	}

	public void setNewCustFlag(String newCustFlag) {
		this.newCustFlag = newCustFlag;
	}
	
	public String getNoter() {
		return noter;
	}

	public void setNoter(String noter) {
		this.noter = noter;
	}

	public Timestamp getNoteTime() {
		return noteTime;
	}

	public void setNoteTime(Timestamp noteTime) {
		this.noteTime = noteTime;
	}
	
	/**
	 * join
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false) })
	private C160A01A c160a01a;

	public void setC160a01a(C160A01A c160a01a) {
		this.c160a01a = c160a01a;
	}

	public C160A01A getC160a01a() {
		return c160a01a;
	}

	/**
	 * join
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false) })
	private C160M01D c160m01d;

	public void setC160m01d(C160M01D c160m01d) {
		this.c160m01d = c160m01d;
	}

	public C160M01D getC160m01d() {
		return c160m01d;
	}
	
	/**
	 * 顯示用欄位
	 * For View 顯示是否為紓困
	 * @param isOnlyProd69
	 */
	@Transient
	private boolean isOnlyProd69;
	
	public void setIsOnlyProd69(boolean isOnlyProd69) {
		this.isOnlyProd69 = isOnlyProd69;
	}

	public boolean isOnlyProd69() {
		return isOnlyProd69;
	}
}
