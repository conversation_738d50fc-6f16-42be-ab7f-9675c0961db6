
package com.mega.eloan.lms.cls.panels;

import java.io.IOException;
import java.net.URISyntaxException;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.cls.service.impl.CLS1131RPAFileServiceImpl;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120S04W;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金徵信作業 - RPA受監護輔助宣告查詢 HTML
 * </pre>
 * 
 * @since 2022/05/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/05/010173,new
 *          </ul>
 */
public class CLS101S01S6Panel extends Panel {

	@Autowired
	CLSService clsService;

	@Autowired
	CLS1131RPAFileServiceImpl cls1131RpaFileService;
	
	@Autowired
	RPAProcessService rpaProcessService;

	private static final long serialVersionUID = 1L;

	/**
	 * @param id
	 * @throws IOException 
	 */
	public CLS101S01S6Panel(String id) {
		super(id);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		byte[] bytes = null;
		String encodedfile = null;
		try {
			String mainId = params.getString(EloanConstants.MAIN_ID);
			String custId = params.getString("custId");
			boolean isC120M01A = Util.equals("Y", Util.trim(params.getString("isC120M01A")));
			String oid = null;
			String dataSrcMemo = "";
			if(isC120M01A){
				
				C120S04W c120S04w = rpaProcessService.getC120S04WBy(mainId, custId);
				if(null != c120S04w){
					oid = c120S04w.getOid();
					dataSrcMemo = "C120S04W";
				}
			}
			else{
				
				C101S04W c101S04w = rpaProcessService.getC101S04WBy(mainId, custId);
				if(null != c101S04w){
					oid = c101S04w.getOid();			
					dataSrcMemo = "C101S04W";
				}
			}
			
			PageParameters Pparams = new CapMvcParameters();
			Pparams.put("oid", oid);
			Pparams.put("dataSource", dataSrcMemo);
			
			bytes = cls1131RpaFileService.getContent(Pparams);
			encodedfile = new String(Base64.encodeBase64(bytes), "UTF-8");
			encodedfile = "data:image/png;base64," + encodedfile;
			
			StringBuilder sb = new StringBuilder();
            sb.append("<label>")
            .append("<img align='left' src='")
            .append(encodedfile)
            .append(" 'width='1100'/></label>")
            .append("<br/>");

			// UPGRADE: 前端須配合改Thymeleaf的樣式
			// Label label = new Label("rpaPictureId", sb.toString());
			// label.setEscapeModelStrings(false);
			// add(label);
			model.addAttribute("rpaPictureId", sb.toString());
	        
			//是否在頁面上加入列印換頁DIV
//			boolean addPageBreak = params.getAsBoolean("addPageBreak", false);
//			add(LMSUtil.genHtmlComponent("addPageBreak", addPageBreak));
		} catch (CapException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (URISyntaxException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
