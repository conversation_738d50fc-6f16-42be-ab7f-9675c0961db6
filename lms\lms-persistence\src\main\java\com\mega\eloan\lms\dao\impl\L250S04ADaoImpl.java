package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L250S04ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L250S04A;

@Repository
public class L250S04ADaoImpl extends LMSJpaDao<L250S04A, String> implements
		L250S04ADao {

	@Override
	public List<L250S04A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L250S04A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L250S04A> findByMainIdAndGroup(String mainId, Integer group) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "group", group);
		search.addOrderBy("sub1Order");
		List<L250S04A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L250S04A findByKey(String mainId, Integer group, Integer sub1Item) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "group", group);
		search.addSearchModeParameters(SearchMode.EQUALS, "sub1Item", sub1Item);
		return findUniqueOrNone(search);

	}
}