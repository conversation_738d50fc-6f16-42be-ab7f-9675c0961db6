package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;


@Controller
@RequestMapping(path = "/fms/cls3001v02")
public class CLS3001V02Page extends AbstractEloanInnerView {

	public CLS3001V02Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.待覆核);
		setJavaScriptVar("noOpenDoc", "N");
		//---
		addToButtonPanel(model, LmsButtonEnum.Filter);
		
		renderJsI18N(CLS3001V01Page.class);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/CLS3001V01Page.js" };
	}
	
	/*@Override
	protected void addPanel(String panelId) {
		add(new CLS2901FilterPanel(panelId));
	}*/
	
}
