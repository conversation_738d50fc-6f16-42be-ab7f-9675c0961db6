package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.lms.panels.LMSM01Panel;

/**
 * <pre>
 * 說明(企金授權外) - 營運概況
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1205S05A")
public class LMS1205S05Page01 extends AbstractEloanForm {

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		renderJsI18N(AbstractEloanPage.class);
		renderJsI18N(LMS1205S05Page01.class);
		renderJsI18N(LMSM01Panel.class);
		
		// 共用借款人引進界面
		model.addAttribute("_PanelB1_visible", true);
		new LMSM01Panel("lmsm01_panel").processPanelData(model, params);
	}

	@Override
    public String getViewName() {
        return "common/pages/None";
    }

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
}
