/* 
 * LMS140MV01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.pages;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.common.RealEstateLoanUtil;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthType;



/**
 * <pre>
 * 央行註記異動作業- 編製中
 * </pre>
 * 
 * @since 2014/08/28
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller
@RequestMapping("/cls/lms140mv01")
public class LMS140MV01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);

		// 加上Button
		List<EloanPageFragment> list = new ArrayList<>();
		// 主管跟經辦都會出現的按鈕

		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
			list.add(LmsButtonEnum.View);
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			list.addAll(Arrays.asList(LmsButtonEnum.Add, LmsButtonEnum.Modify, LmsButtonEnum.Delete));
		}

		addToButtonPanel(model, list);
		
		
		//J-113-0390 更新央行113-09-19對辦理不動產抵押貸款業務規定
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// TextField<?> textField = (TextField<?>) new TextField<Object>("l140mm1aLatestVersion")
		// .add(new SimpleAttributeModifier("id", "l140mm1aLatestVersion"))
		// .add(new SimpleAttributeModifier("name", "l140mm1aLatestVersion"))
		// .add(new SimpleAttributeModifier("type", "text"))
		// .add(new SimpleAttributeModifier("value", RealEstateLoanUtil.latestVersion));
		// add(textField);
		model.addAttribute("latestVersion", RealEstateLoanUtil.latestVersion);

		renderJsI18N(LMS140MV01Page.class);
		renderJsI18N(LMS140MM01Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/base/LMS140MV01Page');");
	}
	
	 public String[] getJavascriptPath() {
	 return new String[] { "pagejs/base/LMS140MV01Page.js" };
	 }

}
