/* 
 * L161S01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L161S01B;

/** 聯貸案參貸比率一覽表明細檔 **/
public interface L161S01BDao extends IGenericDao<L161S01B> {

	L161S01B findByOid(String oid);
	
	List<L161S01B> findByMainId(String mainId);
	
	/**
	 * 找出所找的所有oid
	 * @param oids oid 陣列
	 * @return
	 */
	List<L161S01B> findByOid(String[] oids);
	
	L161S01B findByUniqueKey(String mainId, Integer seq);

	List<L161S01B> findByIndex01(String mainId, Integer seq);
	
	List<L161S01B> findByMainIdUid(String mainId,String pid);
}