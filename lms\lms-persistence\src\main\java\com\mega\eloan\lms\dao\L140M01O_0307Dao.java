/* 
 * L140M01O_0307Dao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01O_0307;

/** 擔保品資料股票明細檔 **/
public interface L140M01O_0307Dao extends IGenericDao<L140M01O_0307> {

	L140M01O_0307 findByOid(String oid);

	List<L140M01O_0307> findByMainId(String mainId);

	List<L140M01O_0307> findByIndex01(String mainId, String stkNo);

	List<L140M01O_0307> findByIndex02(String mainId);

	List<L140M01O_0307> findByOids(String[] oids);

	List<L140M01O_0307> findByStkNoAndStkNm(String mainId, String stkNo,
			String stkNm);
}