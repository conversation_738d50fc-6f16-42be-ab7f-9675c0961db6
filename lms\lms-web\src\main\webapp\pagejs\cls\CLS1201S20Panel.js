/*
* 
* 簽報書
* 	initDfd.done(function(obj){
*  	});
*   	
* 動審表
*	無 initDfd，所以用 $(document).ready(function() {
*
*/
var CLS_L120S09A_PAGE_FORM = "CLS1201S20Form";
var CLS_L120S09A_SINGLE_FORM = "tL120S09AForm";
/**
 * 重新引進查詢名單
 */
function importBlackList(){
	ask_importBlackList().done(function(){
		$.ajax({
			handler : 'cls1141m01formhandler',type : "POST", dataType : "json",
			data : {
				formAction : "importBlackList",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				$("#gridview_blackList").trigger("reloadGrid");
				$("#"+CLS_L120S09A_PAGE_FORM).injectData(json);
				// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
            	triggerRemarkDiv(json);
			}
		});		
	});
}
function ask_importBlackList(){
	var my_dfd = $.Deferred();    	
	    		
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		//"執行引進後會刪除已存在之查詢名單，是否確定執行？"
		CommonAPI.confirmMessage(i18n.cls1201s20["L120S09a.message05"], function(b){
			if (b) {
				my_dfd.resolve();
			}else{
				my_dfd.reject();
			}
		});		
	}else{
		my_dfd.resolve();		
	}
	return my_dfd.promise();
}

function sync_l120s09a_to_c160m01b(){
	$.ajax({
		handler : 'cls1161formhandler', type : "POST", dataType : "json",
		data : {
			formAction : "sync_l120s09a_to_c160m01b",
			mainId : responseJSON.mainid
		},
		success : function(json_sync) {
			API.showMessage("已將查詢結果寫入[額度明細>黑名單]");
		}
	});		
}

/**
 * 黑名單查詢
 */
function queryBlackList(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		$.ajax({
			handler : 'cls1141m01formhandler', type : "POST", dataType : "json",
			data : {
				formAction : "queryBlackList",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				$("#gridview_blackList").trigger("reloadGrid");
				if(json.blackListQDate){
					$("#"+CLS_L120S09A_PAGE_FORM).find("#blackListQDate").text(json.blackListQDate);
				}
				
				if(json.sync_l120s09a_to_c160m01b=="Y"){
					sync_l120s09a_to_c160m01b();
				}
			}
		});				
	}else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.cls1201s20["L120S09a.message02"]);
		return;
	}
}

function sendAmlList(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		//J-109-0426_05097_B1001 為加速青創貸款之案件簽報，擬比照勞工紓困貸款方式，申請簡化洗錢防制流程
		$.ajax({
            handler: "cls1141m01formhandler",
            action: "isCanPassAml",
            data: {
            	mainId: responseJSON.mainId
            },
            success: function(json){ 
            	if(json.isCanPassAml == "Y"){
            		//J-109-0190_05097_B1001 為加速勞工紓困貸款之案件簽報，簡化e-Loan洗錢防制流程
            		//J-109-0426_05097_B1001 為加速青創貸款之案件簽報，擬比照勞工紓困貸款方式，申請簡化洗錢防制流程
            		var confirmShowMsg = "";
            		confirmShowMsg = json.callFrom == "C160M01A" ? "" : "●e-Loan系統中之制裁/管制名單掃瞄，僅須於「動審」階段執行。<BR>" ; 
        		    API.confirmMessage("本案為勞工紓困貸款案件(產品種類：69)／青年創業及啟動金貸款案件(產品種類：61)：<BR>"+confirmShowMsg+"●若當日有相同名單，且已完成掃描及調查之結果，亦可透過「引進當日已完成掃描/調查結果」按鈕直接引進。<BR><BR>是否仍要執行名單掃描？若仍要重新送掃請按「確定」。",function(x){
        			if(x){
        				//是的function

        				API.confirmMessage(i18n.cls1201s20["L120S09a.message20"],function(b){
    					if(b){
    						$.ajax({
            					handler : 'cls1141m01formhandler', type : "POST", dataType : "json",
            					data : {
            						formAction : "sendAmlList",
            						mainId : responseJSON.mainid
            					},
            					success : function(json) {
            						$("#gridview_blackList").trigger("reloadGrid");
            						
            						$("#"+CLS_L120S09A_PAGE_FORM).injectData(json);
            						// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
                                	triggerRemarkDiv(json);
            						
            						//refresh page
            						var btnOnArr = ["cls1201s20_btn_checkAmlResult"];
            						var btnOffArr = ["cls1201s20_btn_importBlackList", "cls1201s20_btn_sendAmlList"
            						                 , "cls1201s20_btn_addData", "cls1201s20_btn_deleteList"];
            						elmArr_on_off(btnOnArr, btnOffArr);
            					}
            				});	
				
    					}else{
    						//否的function
    						 
    					}
    					});	
        				
        				
        			}else{
        				
        			}
        			});			
            	}else{
            		API.confirmMessage(i18n.cls1201s20["L120S09a.message20"],function(b){
					if(b){
						$.ajax({
        					handler : 'cls1141m01formhandler', type : "POST", dataType : "json",
        					data : {
        						formAction : "sendAmlList",
        						mainId : responseJSON.mainid
        					},
        					success : function(json) {
        						$("#gridview_blackList").trigger("reloadGrid");
        						
        						$("#"+CLS_L120S09A_PAGE_FORM).injectData(json);
        						// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
                            	triggerRemarkDiv(json);
        						
        						//refresh page
        						var btnOnArr = ["cls1201s20_btn_checkAmlResult"];
        						var btnOffArr = ["cls1201s20_btn_importBlackList", "cls1201s20_btn_sendAmlList"
        						                 , "cls1201s20_btn_addData", "cls1201s20_btn_deleteList"];
        						elmArr_on_off(btnOnArr, btnOffArr);
        					}
        				});	
			
					}else{
						//否的function
						 
					}
					});	
            	}
            }
        });
		
		
		
		
	
    }else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.cls1201s20["L120S09a.message02"]);
		return;
	}
}

function checkAmlResult(){
	$.ajax({
		handler : 'cls1141m01formhandler', type : "POST", dataType : "json",
		data : {
			formAction : "checkAmlResult",
			mainId : responseJSON.mainid
		},
		success : function(json) {
			$("#gridview_blackList").trigger("reloadGrid");
			
			$("#"+CLS_L120S09A_PAGE_FORM).injectData(json);
			// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
        	triggerRemarkDiv(json);
			
			if(json.is_aml_lockEdit=="N"){
				//refresh page
				var btnOffArr = ["cls1201s20_btn_checkAmlResult"];
				if(true){
					/*
					目前, lockEdit 只有針對 E00(掃描中)
					在未 lockEdit 的狀況下, 應該也要允許再去查詢狀態
					
					可能第1次查到的結果是001(Hit) , 並未lockEdit
					user 可能會有以下行動
					(1)檢視輸入的資料, 看是否因為 keyin錯誤, 導致Hit. 若這樣, 可能 user會在e-loan更改資料後, 再送查詢
					(2)user遇到001(Hit)後, 在AML輸入［調查結果, 可否承做］, 然後在e-loan按查詢去抓該 refNo 最新狀態
					*/
					btnOffArr = [];					
				}
				
				var btnOnArr = ["cls1201s20_btn_importBlackList", "cls1201s20_btn_sendAmlList"
				                 , "cls1201s20_btn_addData", "cls1201s20_btn_deleteList"];
				elmArr_on_off(btnOnArr, btnOffArr);
			}
			
			if(json.sync_l120s09a_to_c160m01b=="Y"){
				sync_l120s09a_to_c160m01b();
			}
		}
	});	
}

function applyCm1AmlStatus(){
	$.ajax({
		handler : 'cls1141m01formhandler', type : "POST", dataType : "json",
		data : {
			formAction : "applyCm1AmlStatus",
			mainId : responseJSON.mainid
		},
		success : function(json) {
			$("#gridview_blackList").trigger("reloadGrid");
		}
	});	
}
function applyLuvRiskLevel(){
	$.ajax({
		handler : 'cls1141m01formhandler', type : "POST", dataType : "json",
		data : {
			formAction : "applyLuvRiskLevel",
			mainId : responseJSON.mainid
		},
		success : function(json) {
			$("#gridview_blackList").trigger("reloadGrid");
			// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」
			triggerRemarkDiv(json);
		}
	});	
}

/**
 * J-113-0082 配合法務部新規，新增引入「受告誡處分」資訊
 */
function importCmfwarnpResult(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		$.ajax({
			handler : 'cls1141m01formhandler',
			type : "POST",
			dataType : "json",
			data : {
				formAction : "importCmfwarnp",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				//initS20aJson.initForShow();
				$("#gridview_blackList").trigger("reloadGrid");
				if(json.resultMsg && json.resultMsg != ""){
					CommonAPI.showMessage(json.resultMsg);
				}
			}
		});				
	}else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.cls1201s20["L120S09a.message02"]);
		return;
	}
}

function elmArr_on_off(btnOnArr, btnOffArr){
	$.each( btnOnArr, function(idx,btnId){					
		$("#"+btnId).removeClass(" ui-state-disabled ").removeAttr("disabled");
	});
	$.each( btnOffArr, function(idx,btnId){					
		$("#"+btnId).addClass(" ui-state-disabled ").attr("disabled", "true");
	});
}

/**
 * 新增查詢名單
 */
function addData() {
	$("#"+CLS_L120S09A_SINGLE_FORM).reset();
					
	l120s09aThick("");
}

/**
 * 刪除名單
 */
function deleteList(){
	var rows = $("#gridview_blackList").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
			var data = $("#gridview_blackList").getRowData(rows[i]);
			list += ((list == "") ? "" : sign ) + data.oid;
		}
	}
	if (list == "") {
		//"尚未選取資料"
		CommonAPI.showMessage(i18n.cls1201s20["L120S09a.message03"]);
		return;
	}	

    //"是否確定要刪除名單"
	API.confirmMessage(i18n.cls1201s20["L120S09a.message04"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : 'cls1141m01formhandler', type : "POST", dataType : "json",
				action : "deleteL120s09a",
				data : {
					listOid : list,
					mainId : responseJSON.mainid
				},
				success : function(json) {
					$("#gridview_blackList").trigger("reloadGrid");
				}
			});
		}else{
			//...
		}
	})	
}


/**
 * 開起查詢名單以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDocBL(cellvalue, options, rowObject) {
	var $objForm = $("#"+CLS_L120S09A_SINGLE_FORM);
	
	$objForm.reset();
	
	$.ajax({
		handler : 'cls1141m01formhandler', type : "POST", dataType : "json",
		action : "fetchL120s09a",
		data : {
			oid : rowObject.oid
		},
		success : function(json_c) {
			var json = json_c[CLS_L120S09A_SINGLE_FORM];
			for(o in json.custRelation){
				$objForm.find("[name=custRelation]").each(function(i){
					var $this = $(this);
					if($this.val() == json.custRelation[o]){
						$this.attr("checked",true);
					}
				});
			}

		    $objForm.setData(json,false);
			
			l120s09aThick(json.oid);
		}
	});	
}

/**
 * 登錄洗錢防制名單ThickBox
 */
function l120s09aThick(oid) {
	$("#blackListDetail").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.cls1201s20["L120S09a.inputBlacklist"],//"登錄洗錢防制名單",
		width : 960,
		height : 610,
		modal : true,
		i18n:i18n.def,
		buttons : {
			"saveData" : function() {
				
				var $objForm = $("#"+CLS_L120S09A_SINGLE_FORM);
				
				var list = "";
				var sign = ",";
				if($objForm.valid()){
					API.confirmMessage(i18n.cls1201s20["L120S09a.message22"],function(b){
					if(b){
					$objForm.find("[name=custRelation]:checkbox:checked").each(function(i){
						list += ((list == "") ? "" : sign ) + $(this).val();
					});
					
					$.ajax({
						handler : 'cls1141m01formhandler', type : "POST", dataType : "json",
						action : "saveL120s09a",
						data : {
							CLS_L120S09A_SINGLE_FORM : JSON.stringify($objForm.serializeData()),
							mainId : responseJSON.mainid,
			  				custId : $objForm.find("#custId").val(),
			  				dupNo : $objForm.find("#dupNo").val(),
							oid : oid,
							list : list
						},
						success : function(json) {
							$("#gridview_blackList").trigger("reloadGrid");
							oid = json.newOid;
						}
					});
					}else{
						//否的function
						 
					}
				});
					
				}
			},
			"close" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
					if(res){
						$.thickbox.close();
					}
		         });
			}
		}
	});
}

/**
 * 引進戶名
 */
function applyCustName() {
	var $objForm = $("#"+CLS_L120S09A_SINGLE_FORM);
	$.ajax({
		handler : 'cls1141m01formhandler', type : "POST", dataType : "json",
		action : "fetchL120s09aCustName",
		data : {
			custId : $objForm.find("#custId").val(),
			dupNo : $objForm.find("#dupNo").val()
		},
		success : function(obj) {			
			if(obj.custName){
				$objForm.find("#custName").val(obj.custName);
			}			
			if(obj.custEName){
				$objForm.find("#custEName").val(obj.custEName);
			}			
			if(obj.country){
				$objForm.find("#country").val(obj.country);
			}
			
		}
	});	
}



/**
 * 引進當日已完成掃描/調查名單
 */
var ApplyAmlStatusDoneAction = {
    grid: null,
    isLoad: false,
    init: function(){
        if (this.isLoad) {
            this._reloadGrid();
            return false;
        }
        this.isLoad = true;
        this.grid = $("#selectAmlStatusDoneView").iGrid({
            handler: "cls1141gridhandler",
            height: "175px",
            action: "queryL120s09bNcResultDone",
            postData: {
                mainId: responseJSON.mainId
            },
            rownumbers: true,
            colModel: [{
                colHeader: "資料查詢日期",
                align: "left",
                width: 20,
                sortable: false,
                name: 'QUERYDATES'
            }, {
                colHeader: "案件調查結果",
                align: "left",
                width: 30,
                sortable: false,
                name: 'NCRESULTDSCR'
            }, {
                colHeader: "掃描對象編號(AML識別序號)",
                align: "left",
                width: 40,
                sortable: false,
                name: 'REFNO'
            }, {
                colHeader: "掃描批號(重複序號)",
                align: "left",
                width: 50,
                sortable: false,
                name: 'UNIQUEKEY'	  	
            }, {
                colHeader: "",
                align: "right",
                width: 30,
                sortable: false,
                name: 'MAINID',
                hidden: true
            }, {
                colHeader: "",
                align: "right",
                width: 30,
                sortable: false,
                name: 'OID',
                hidden: true    
            }]
        });
    },
    /**
     *更新grid
     */
    _reloadGrid: function(){
        this.grid.jqGrid("setGridParam", {
            postData: {
                mainId: responseJSON.mainId
            },
            page: 1,
            search: true
        }).trigger("reloadGrid");
    },
    /**
     * 引進當日已完成掃描/調查結果
     */
    applyAmlStatusDone: function(){

    	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
    	if(count > 0){
            //J-109-0426_05097_B1001 為加速青創貸款之案件簽報，擬比照勞工紓困貸款方式，申請簡化洗錢防制流程
			$.ajax({
                handler: "cls1141m01formhandler",
                action: "isCanPassAml",
                data: {
                	mainId: responseJSON.mainId
                },
                success: function(json){ 
                	if(json.isCanPassAml == "Y"){
                		ApplyAmlStatusDoneAction.init();
            			ApplyAmlStatusDoneAction.openBox();	
                	}else{
                		CommonAPI.showMessage("本功能僅限純紓困案／青年創業及啟動金貸款案件(產品種類：61)簽報書使用");
                	}
                }
            });
    	
        }else{
    		//"必須先執行【引進查詢名單】才能執行本功能！"
    		CommonAPI.showMessage(i18n.cls1201s20["L120S09a.message02"]);
    		return;
    	}
 
    },
    /**
     * 選擇當日已完成掃描/調查結果
     */
    openBox: function(){
        $("#selectAmlStatusDoneBox").thickbox({
            title: "選擇當日已完成掃描/調查結果",
            width: 900,
            height: 350,
            modal: true,
            readOnly: false,
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var girdId = ApplyAmlStatusDoneAction.grid.getGridParam('selrow');
                    if (!girdId) {
                        //grid_selector=請選擇資料
                        return API.showMessage(i18n.def["grid_selector"]);
                    }
                    var data = ApplyAmlStatusDoneAction.grid.getRowData(girdId);
                    $.ajax({
                        handler: "cls1141m01formhandler",
                        action: "checkAmlResultFromOtherCase",
                        data: {
                        	mainId: responseJSON.mainId,
                        	seletMainId: data.MAINID,
                        	seletUniqueKey: data.UNIQUEKEY,
                        	seletOid: data.OID
                        },
                        success: function(json){ 
                        	$.thickbox.close();
                        	$("#gridview_blackList").trigger("reloadGrid");
                        	
                        	$("#"+CLS_L120S09A_PAGE_FORM).injectData(json);
                        	// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
                        	triggerRemarkDiv(json);
                        	
                        	if(json.is_aml_lockEdit=="N"){
                				//refresh page
                				var btnOffArr = ["cls1201s20_btn_checkAmlResult"];
                				if(true){
                					/*
                					目前, lockEdit 只有針對 E00(掃描中)
                					在未 lockEdit 的狀況下, 應該也要允許再去查詢狀態
                					
                					可能第1次查到的結果是001(Hit) , 並未lockEdit
                					user 可能會有以下行動
                					(1)檢視輸入的資料, 看是否因為 keyin錯誤, 導致Hit. 若這樣, 可能 user會在e-loan更改資料後, 再送查詢
                					(2)user遇到001(Hit)後, 在AML輸入［調查結果, 可否承做］, 然後在e-loan按查詢去抓該 refNo 最新狀態
                					*/
                					btnOffArr = [];					
                				}
                				
                				var btnOnArr = ["cls1201s20_btn_importBlackList", "cls1201s20_btn_sendAmlList"
                				                 , "cls1201s20_btn_addData", "cls1201s20_btn_deleteList"];
                				elmArr_on_off(btnOnArr, btnOffArr);
                			}
                			
                			if(json.sync_l120s09a_to_c160m01b=="Y"){
                				sync_l120s09a_to_c160m01b();
                			}
                        	
                			$.thickbox.close();
                        	 
                            
                        }
                    });
                    
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
};

/**
 * J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」
 * 把判斷都交給後端決定
 */
function triggerRemarkDiv(json){
	if(json.showNcResultRemark && json.showNcResultRemark == "Y"){
		$("#"+CLS_L120S09A_PAGE_FORM).find("#ncResultRemarkDiv").show();
	}else{
		$("#"+CLS_L120S09A_PAGE_FORM).find("#ncResultRemarkDiv").hide();
	}
	if(json.showHighRiskRemark && json.showHighRiskRemark == "Y"){
		$("#"+CLS_L120S09A_PAGE_FORM).find("#highRiskRemarkDiv").show();
	}else{
		$("#"+CLS_L120S09A_PAGE_FORM).find("#highRiskRemarkDiv").hide();
	}
}



$(document).ready(function() {
	ilog.debug("<EMAIL>"); 
	var grid_column_sas_show = $("#gridview_blackList").attr("data-show") ||'';
	var grid_column_sas_prop = {};
	if(grid_column_sas_show=="Y"){
		
	}else{
		grid_column_sas_prop["hidden"] = true;
	}
	var gridBlackList = $("#gridview_blackList").iGrid({
		needPager: false,
		handler : 'lms1201gridhandler',
		// height: 345, //for 15 筆
		height : "300px", // for 10 筆
		// autoHeight: true,
		width : "100%",
		postData : {
			formAction : "queryL120s09a",
			mainId : responseJSON.mainid
		},
		//rowNum:30,
		sortname : 'seqNum|checkSeq|custRelation|custId|dupNo|custEName|custName',  //J-107-0070-001  Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
		sortorder:'asc|asc|asc|asc|asc|asc',
		multiselect: true,
		hideMultiselect:false,
		autowidth : true,
		colModel : [{
        	 colHeader: i18n.cls1201s20["L120S09a.blackListCode"],//"查詢結果",
        	 name: 'blackListCode',
             width: 4,
             sortable: false,
			 align:"center"
		},{
			colHeader : i18n.cls1201s20["L120S09a.memo"],//"備註" → "命中代碼", 
			name : 'memo',
			width : 12,
			sortable : false,
			align : 'left'	
		},{
			colHeader : i18n.cls1201s20["L120S09a.cm1AmlStatus"],//"0024註記", 
			name : 'cm1AmlStatus',
			width : 6,
			sortable : false,
			align : 'center'	
		},{
			//J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
			colHeader : i18n.cls1201s20["L120S09a.luvRiskLevel"],//"風險等級",   
			name : 'luvRiskLevel',
			width : 5,
			sortable : false,
			align : 'center'		
		},$.extend({
			colHeader : i18n.cls1201s20["L120S09a.checkSeq"],//"序號", 
			name : 'checkSeq',
			width : 3,
			sortable : false,
			align : 'left'
		}, grid_column_sas_prop),{
			colHeader : i18n.cls1201s20["L120S09a.custName"],//"戶名",
			width : 11,
			name : 'custName',
			sortable : false,
			align : 'left',
			formatter : 'click',
			onclick : openDocBL
		},{
			colHeader : i18n.cls1201s20["L120S09a.custEName"],//"英文戶名",
			width : 20,
			name : 'custEName',
			align : 'left'
		},{
			colHeader : i18n.cls1201s20["L120S09a.country"],//"國別", 
			name : 'country',
			width : 4,
			sortable : false,
			align : 'left'
        },{
			colHeader : i18n.cls1201s20["L120S09a.custRelation"],//"與本案關係",
			name : 'custRelationIndex',
			width : 13,
			sortable : false,
			align : "left"
		},{
			colHeader : i18n.cls1201s20["L120S09a.custId"],//"統編", 
			name : 'custId',
			width : 7,
			sortable : false,
			align : 'center'
		},{
        	colHeader : i18n.cls1201s20["L120S09a.cmfwarnpResult"],//"受告誡處分",
			name : 'cmfwarnpResult',
			width : 4,
			sortable : false,
			align : 'center'
        },{
			name : 'oid',
			hidden : true
		},{
			name : 'seqNum',
			hidden : true	
		} ],
		ondblClickRow : function(rowid) {
			var data = gridBlackList.getRowData(rowid);
			openDocBL(null, null, data);
		}
	});
	
});

