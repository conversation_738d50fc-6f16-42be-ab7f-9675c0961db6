package com.mega.eloan.lms.batch.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.gwclient.GWLogger;
import com.mega.eloan.common.gwclient.GWType;
import com.mega.eloan.common.service.GWLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.model.L120M01A;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * RPA 回傳勞工紓困貸款案件狀態
 * </pre>
 * <p>
 * LOCAL Test URL example ： http://localhost/ces-web/app/schedulerRPA
 * <p>
 * Post Request : {"serviceId":"getStartUpStatus", "vaildIP":"N",
 * "request":{"responseCode":"1","custId":"13724746","brNo":"007",
 * "rpaUserId","078001"}}
 * <p>
 * SIT http://*************/ces-web/app/schedulerRPA
 * 
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/6/2,EL07623,new
 *          </ul>
 * @since 2021/6/2
 */
@Service("getStartUpStatus")
public class RPAGetStartUpStatusServiceImpl extends AbstractCapService
		implements WebBatchService {

	private static Logger logger = LoggerFactory
			.getLogger(RPAGetStartUpStatusServiceImpl.class);

	@Resource
	LMS1201Service service1201;

	@Resource
	SysParameterService sysParameterService;

	@Resource
	GWLogService gwLogService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Value("${systemId}")
	private String sysId;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject mag;
		logger.info("getStartUpStatus 啟動========================");
		logger.info("傳入參數==>[{}]", json.toString());
		GWLogger gwlogger = new GWLogger(GWType.GWTYPE_RPA, gwLogService,
				sysParameterService);

		JSONObject req = json.getJSONObject("request");
		String errorMsg = "";
		String custId = req.optString("custId", "");
		String brNo = req.optString("brNo", "");
		String rpaUserId = req.optString("rpaUserId", "");
		String responseCode = req.optString("responseCode");

		gwlogger.logBegin(sysId, custId, "getStartUpStatus", req.toString(),
				System.currentTimeMillis());

		String camType = Util.trim(req.optString("camType"));

		// 例如: 31150989_050ee094de754740bf799e7d14770122_1
		// _1 :代表簽報書為600萬以下，「有」微型企業頁籤要點選
		// _2: 代表簽報書為601-2600萬，「沒有」微型企業頁籤要點選
		if (Util.equals(camType, "1")) {
			camType = UtilConstants.Casedoc.caseType.國發基金協助新創事業紓困融資加碼方案600萬;
		} else {
			camType = UtilConstants.Casedoc.caseType.國發基金協助新創事業紓困融資加碼方案2600萬;
		}

		List<L120M01A> l120m01aList = service1201
				.findByBranchCustIdIsStartUpRelief(brNo, custId, camType);
		if (!l120m01aList.isEmpty()) {
			if (l120m01aList.size() == 1) {
				saveRPAStatus(l120m01aList.get(0), responseCode);
			} else {
				boolean findDoc = false;
				for (L120M01A l120m01a : l120m01aList) {
					if (rpaUserId.equals(l120m01a.getUpdater())) {
						logger.info("custId:{},", new Object[] { custId,
								responseCode });
						findDoc = true;
						saveRPAStatus(l120m01a, responseCode);
						break;
					}
				}
				if (!findDoc) {
					errorMsg = "查無此國發基金協助新創事業紓困融資加碼方案案件簽報書:" + custId + "!";
				}
			}
		} else {
			errorMsg = "查無此國發基金協助新創事業紓困融資加碼方案案件簽報書:" + custId + "!";
		}

		logger.info("getStartUpStatus 結束========================");

		GWException gwException = null;
		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
			gwException = new GWException(errorMsg, getClass(),
					GWException.GWTYPE_RPA);
		}

		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
		} else {
			logger.info("執行成功");
		}

		mag = JSONObject
				.fromObject("{\"rc\": 0, \"rcmsg\": \"SUCCESS\", \"message\":\"執行成功\"}");
		gwlogger.logEnd(mag.toString(), gwException, "0");
		return mag;
	}

	private void saveRPAStatus(L120M01A l120m01a, String responseCode) {
		l120m01a.setApplyStatus(responseCode);
		l120m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		service1201.saveWithoutUpdater(l120m01a);
	}

}
