/* 
 * LMS9990V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import com.iisigroup.cap.component.PageParameters;

import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * 個金約據書
 * </pre>
 * 
 * @since 2012/02/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/02/07,ICE,new
 *          </ul>
 */

@Controller
@RequestMapping("/ctr/lms9990v02")
public class LMS9990V02Page extends AbstractEloanInnerView {

	public LMS9990V02Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_已核准); 
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.Search);
		renderJsI18N(LMS9990V01Page.class);
		model.addAttribute("loadScript", "loadScript('pagejs/ctr/LMS9990V02Page');");
	}

}
