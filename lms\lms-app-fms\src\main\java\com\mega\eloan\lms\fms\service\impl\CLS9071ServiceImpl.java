package com.mega.eloan.lms.fms.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.Pteamapp_LNF;
import com.mega.eloan.lms.base.common.Pteamapp_TOT;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dao.C900M01GDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.fms.pages.CLS9071V02Page;
import com.mega.eloan.lms.fms.service.CLS9071Service;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C900M01G;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.NumberFormats;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Service
public class CLS9071ServiceImpl extends AbstractCapService implements CLS9071Service {
	private static int rowHeightAt14 = 360;
	
	@Resource
	CLSService clsService;
	
	@Resource
	L140M01ADao l140m01aDao;
	
	@Resource
	C900M01GDao c900m01gDao;
	
	@Resource
	ICustomerService customerService; 
	
	@Resource
	MisPTEAMAPPService misPTEAMAPPService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Override
	public void genExcel(ByteArrayOutputStream outputStream, String grpCntrNo)
			throws IOException, WriteException {
		
		WritableWorkbook workbook = null;
		WritableSheet sheet1 = null;
		if (true) {
			// ---
			workbook = Workbook.createWorkbook(outputStream);
			sheet1 = workbook.createSheet("明細", 0);

			// ======
			WritableFont headFont_memo = new WritableFont(
					WritableFont.createFont("標楷體"), 10);
			WritableCellFormat cellFormat_memo = new WritableCellFormat(headFont_memo);
			{
				cellFormat_memo.setAlignment(Alignment.LEFT);
				cellFormat_memo.setWrap(false);
			}
			// ======
			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableCellFormat cellFormatL = new WritableCellFormat(headFont12);
			{
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setWrap(true);
			}

			WritableCellFormat cellFormatR = new WritableCellFormat(headFont12);
			{
				cellFormatR.setAlignment(Alignment.RIGHT);
				cellFormatL.setWrap(true);
			}

			WritableCellFormat cellFormatL_Border = new WritableCellFormat(
					cellFormatL);
			{
				cellFormatL_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}

			WritableCellFormat cellFormatR_Border = new WritableCellFormat(
					cellFormatR);
			{
				cellFormatR_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}
			// ======
			WritableFont headFont14 = new WritableFont(
					WritableFont.createFont("標楷體"), 14);
			WritableCellFormat cellFormatC_14 = new WritableCellFormat(
					headFont14);
			{
				cellFormatC_14.setAlignment(Alignment.LEFT);
				cellFormatC_14.setWrap(true);
			}
			//======
			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("統編", 16);
			headerMap.put("姓名", 20);
			headerMap.put("額度序號", 16);
			headerMap.put("狀態", 20);
			headerMap.put("幣別", 8);
			headerMap.put("金額", 16);
			headerMap.put("備註", 32);
			
			int totalColSize = headerMap.size();
			
			Map<String, String> statusMap = new HashMap<String, String>();
			statusMap.put("1", "1-簽案中");
			statusMap.put("2", "2-簽案核准");
			statusMap.put("3", "3-動審");
			//======
			List<String[]> rows = new ArrayList<String[]>();
			Map<String,BigDecimal> sum_1 = new HashMap<String,BigDecimal>();
			Map<String,BigDecimal> sum_2 = new HashMap<String,BigDecimal>();
			Map<String,BigDecimal> sum_3 = new HashMap<String,BigDecimal>();
			if(Util.isNotEmpty(Util.trim(grpCntrNo))){		
				for(C900M01G c900m01g : c900m01gDao.findByUsedDetail(grpCntrNo)){
					
					if(Util.equals(c900m01g.getUseFlag(), "D")){
						/*						 
						(1)有的狀況，是未撥款，在簽新額度時，把舊案核准的額度 取消
						(2)有的是 減額
						(3)有的是 要把貸款轉換到其它銀行(實際上 '曾經' 有佔用)
						=> 應該不能讀到 e-loan 最近一個核准額度的狀態=取消，就把它從 C900M01G 給拿掉
						 
						 
						另外, 在 刪除 簽報書時, 是先押 deletedTime, 等 N 天後再由 batch 刪除
						但若之後發現誤刪, 要把資料 拉回 正常狀態 
						也會影響 c900m01g 的內容
						*/
						continue;
					}
					
					String[] arr = new String[totalColSize];
					for (int i_col = 0; i_col < totalColSize; i_col++) {
						arr[i_col] = "";
					}
					int col = 0;
					arr[col++] = Util.trim(c900m01g.getCustId()+"-"+c900m01g.getDupNo());
					arr[col++] = getCustName(c900m01g.getCustId(), c900m01g.getDupNo());
					arr[col++] = c900m01g.getCntrNo();					
					arr[col++] = LMSUtil.getDesc(statusMap, c900m01g.getStatus());
					
					String curr = "";
					BigDecimal val = null;
					String memo = "";
					if(Util.equals("1", c900m01g.getStatus())){
						curr = c900m01g.getApplyCurr();
						val = c900m01g.getApplyAmt();
					}else if(Util.equals("2", c900m01g.getStatus())){
						curr = c900m01g.getApproveCurr();
						val = c900m01g.getApproveAmt();
						memo = "核准日:"+Util.trim(TWNDate.toAD(c900m01g.getApproveDate()));
					}else if(Util.equals("3", c900m01g.getStatus())){
						curr = c900m01g.getLoanCurr();
						val = c900m01g.getLoanAmt();
						memo = "動用日:"+Util.trim(TWNDate.toAD(c900m01g.getLoanDate()));						
					}
					arr[col++] = curr;
				    arr[col++] = LMSUtil.pretty_numStr(val);
				    arr[col++] = memo;
				    // ---
				    if(Util.equals("1", c900m01g.getStatus())){
				    	add_amt_to_map(sum_1, curr, val);
				    }else if(Util.equals("2", c900m01g.getStatus())){
				    	add_amt_to_map(sum_2, curr, val);
				    }else if(Util.equals("3", c900m01g.getStatus())){
				    	add_amt_to_map(sum_3, curr, val);
				    }
					// ---
					rows.add(arr);	
				}				
			}

			// ==============================
			int rowIdx = 0;
			// ==============================
			if (true) {
				sheet1.setRowView(rowIdx, rowHeightAt14, false);
				sheet1.mergeCells(0, rowIdx, totalColSize - 1, rowIdx);
				sheet1.addCell(new Label(0, rowIdx, "母戶額度序號："+grpCntrNo,
						cellFormatC_14));
			}
			// ==============================
			rowIdx++;
			int colIdx = 0;
			for (String h : headerMap.keySet()) {
				int colWidth = headerMap.get(h);
				sheet1.setColumnView(colIdx, colWidth);
				sheet1.addCell(new Label(colIdx, rowIdx, h, cellFormatL_Border));
				// ---
				colIdx++;
			}
			// ==============================
			if(true){
				for (String[] arr : rows) {
					int colLen = arr.length;
					if(true){
						rowIdx++;	
					}						
					for (int i_col = 0; i_col < colLen; i_col++) {
						sheet1.addCell(new Label(i_col, rowIdx, arr[i_col],
								(i_col == 5) ? cellFormatR_Border
										: cellFormatL_Border));
					}			
				}	
			}
			// ==============================
			if (true) {
				rowIdx = output_sum(rowIdx, statusMap.get("1")+"合計", sum_1, sheet1, cellFormatL, cellFormatR);
				rowIdx = output_sum(rowIdx, statusMap.get("2")+"合計", sum_2, sheet1, cellFormatL, cellFormatR);
				rowIdx = output_sum(rowIdx, statusMap.get("3")+"合計", sum_3, sheet1, cellFormatL, cellFormatR);
			}
			if (true) {
				rowIdx++;
				sheet1.addCell(new Label(0, rowIdx, "註1. 資料來源為2015-04後的 e-loan 簽報書、動審表",
						cellFormat_memo));
			}
			// ---
			workbook.write();
			workbook.close();
		}	
	}
	
	private void add_amt_to_map(Map<String,BigDecimal> sumMap, String curr, BigDecimal amt){
		if(!sumMap.containsKey(curr)){
			sumMap.put(curr, BigDecimal.ZERO);
		}
		BigDecimal newAmt = sumMap.get(curr).add(amt);
		sumMap.put(curr, newAmt);
	}
	
	private int output_sum(int src_rowIdx, String statusDesc, Map<String,BigDecimal> sum_map
			, WritableSheet sheet1, WritableCellFormat cellFormatL, WritableCellFormat cellFormatR) 
	throws RowsExceededException, WriteException{
		int rowIdx = src_rowIdx;
		if (true) {	
			int flag = 0;
			for(String curr: sum_map.keySet()){
				rowIdx++;
				if(flag==0){
					sheet1.addCell(new Label(3, rowIdx , statusDesc, cellFormatL));
					flag++;
				}					
				sheet1.addCell(new Label(4, rowIdx , curr, cellFormatL));
				sheet1.addCell(new Label(5, rowIdx , LMSUtil.pretty_numStr(sum_map.get(curr)), cellFormatR));
			}
		}	
		return rowIdx;
	}
	
	private String getCustName(String id, String dup){
		Map<String, Object> custMap = customerService.findByIdDupNo(id, dup);
		return Util.trim(MapUtils.getString(custMap, "CNAME"));
	}	

	@Override
	public void genExcel_J_107_0046(ByteArrayOutputStream outputStream, boolean includeOnTheWay, 
			String raw_brNo, String custId, String p_grpCntrNo, String rptNo,
			String p_dataYM1, String c_dataYM1, String c_dataYM2 , boolean isAUDIT)
	throws IOException, WriteException {
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS9071V02Page.class);
		
		String issuebrno = raw_brNo;
		if(true){
			String logonBr = user.getUnitNo();
			if(Util.isEmpty(Util.trim(raw_brNo))){
				if (isAUDIT || Util.equals(UtilConstants.BankNo.資訊處, logonBr)
						|| Util.equals(UtilConstants.BankNo.授管處, logonBr)
						|| Util.equals(UtilConstants.BankNo.授信行銷處, logonBr)
						|| Util.equals(UtilConstants.BankNo.消金業務處, logonBr)) {
					//可查全部
				}else{
					//只可查「報案分行」=自己的資料
					issuebrno = user.getUnitNo();	
				}			
			}else{
				//前端已選擇
			}			
		}
		
		WritableWorkbook workbook = null;
		
		if (true) {
			// ---
			workbook = Workbook.createWorkbook(outputStream);
			// ======
			WritableFont headFont_memo = new WritableFont(
					WritableFont.createFont("標楷體"), 10);
			WritableCellFormat cellFormat_memo = new WritableCellFormat(headFont_memo);
			{
				cellFormat_memo.setAlignment(Alignment.LEFT);
				cellFormat_memo.setWrap(false);
			}
			// ======
			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableCellFormat cellFormatL = new WritableCellFormat(headFont12);
			{
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setVerticalAlignment(VerticalAlignment.TOP);
				cellFormatL.setWrap(true);				
			}
			WritableCellFormat cellFormatL_nowrap = new WritableCellFormat(headFont12);
			{
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setVerticalAlignment(VerticalAlignment.TOP);
				cellFormatL.setWrap(false);				
			}
			

			WritableCellFormat cellFormatL_Border = new WritableCellFormat(
					cellFormatL);
			{
				cellFormatL_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}
			WritableCellFormat cellFormatL_nowrap_Border = new WritableCellFormat(
					cellFormatL_nowrap);
			{
				cellFormatL_nowrap_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}
			
			// ======
			WritableFont headFont14 = new WritableFont(
					WritableFont.createFont("標楷體"), 14);
			WritableCellFormat cellFormatC_14 = new WritableCellFormat(
					headFont14);
			{
				cellFormatC_14.setAlignment(Alignment.LEFT);
				cellFormatC_14.setWrap(true);
			}
			// ======
			//數字的 cell format
			WritableCellFormat amtTWDFormat = new WritableCellFormat(headFont12, NumberFormats.FORMAT1);
			if(true){
				amtTWDFormat.setVerticalAlignment(VerticalAlignment.TOP);
				amtTWDFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
			}
			WritableCellFormat intRate6Format = new WritableCellFormat(headFont12, new jxl.write.NumberFormat("#.000000"));
			if(true){
				intRate6Format.setVerticalAlignment(VerticalAlignment.TOP);
				intRate6Format.setBorder(Border.ALL, BorderLineStyle.THIN);
			}
			//======
			String show_c_dataYM2 = c_dataYM2;
			String p_dataYM_X = p_dataYM1;
			String p_dataYM_Y = p_dataYM1;
			String c_dataYM_X = c_dataYM1;
			String c_dataYM_Y= c_dataYM2;
			String lnf155_data_ym = "";
			String dataPeriodFlag = "M";
			if(true){
				if(Util.equals(rptNo, "1")){
					c_dataYM_X = "1900-01";
					c_dataYM_Y = "9999-12";
					if(true){
						c_dataYM_Y = p_dataYM1;
					}
					lnf155_data_ym = p_dataYM1;
				}else if(Util.equals(rptNo, "2")){
					p_dataYM_X = "9999-12";
					p_dataYM_Y = "1900-01";
					
					lnf155_data_ym = c_dataYM2;
				}else if(Util.equals(rptNo, "3")){
					p_dataYM_X = "9999-12";
					p_dataYM_Y = "1900-01";
					c_dataYM_X = "1900-01";
					c_dataYM_Y = "9999-12";
					
					show_c_dataYM2 = Util.trim(StringUtils.substring(TWNDate.toAD(CapDate.addMonth(CrsUtil.get_sysMonth_1st(), -1)), 0, 7));

					dataPeriodFlag = "D";
				} else if(Util.equals(rptNo, "4")){
					//J-111-0365 新增按月提供近一年(上年度1月至今年度最近月份)分行敘做「消金整批貸款清單」月報予稽核處
					//註解原因，母戶p_dataYM_X抓，抓dataYM還有效即可，不然筆數會太多
//					p_dataYM_X = "9999-12";
//					p_dataYM_Y = "1900-01";
					
					//c_dataYM_X //子戶撥款日起，從前面傳入
					//c_dataYM_Y //子戶撥款日迄，從前面傳入
					if(true){
						c_dataYM_Y = p_dataYM1;
					}
					lnf155_data_ym = p_dataYM1;
				}
			}
			
			List<Map<String, Object>> parent_list = new ArrayList<Map<String, Object>>();
			if(true){
				List<Map<String, Object>> parent1_list = new ArrayList<Map<String, Object>>();
				List<Map<String, Object>> parent2_list = misdbBASEService.gen_J_107_0046_parent(issuebrno, custId, p_grpCntrNo, p_grpCntrNo, p_dataYM_X, p_dataYM_Y);
				if(Util.isNotEmpty(p_grpCntrNo)){
					PTEAMAPP pteamapp = misPTEAMAPPService.getDataByLnf020GrpCntrNo(p_grpCntrNo);
					if(pteamapp!=null){
						String mGrpCntrNo = Util.trim(pteamapp.getMgrpcntrno());
						if(Util.isNotEmpty(mGrpCntrNo)){
							parent1_list = misdbBASEService.gen_J_107_0046_parent(issuebrno, custId, mGrpCntrNo, p_grpCntrNo, p_dataYM_X, p_dataYM_Y);
						}
					}
				}
				parent_list.addAll(parent1_list);
				parent_list.addAll(parent2_list);
				
			}
			List<Map<String, Object>> child_list = misdbBASEService.gen_J_107_0046_child(issuebrno, custId, p_grpCntrNo, p_grpCntrNo, p_dataYM_X, p_dataYM_Y, c_dataYM_X, c_dataYM_Y, lnf155_data_ym);
			if(Util.equals(rptNo, "1") || Util.equals(rptNo, "4")){
				//團貸子戶可能是2個分行攤貸 , LNF155只有31的帳號, 但為了「統計筆數」能精確一點, XLS只抓出30的帳號
				//此狀況，另外去抓 lnf155
				List<Map<String, Object>> child_factType3031_list = misdbBASEService.gen_J_107_0046_childfor_factType3031(issuebrno, custId, p_grpCntrNo, p_grpCntrNo, p_dataYM1);
				Map<String, BigDecimal> cntrNo_lnap__loan_rate_m_map = new HashMap<String, BigDecimal>();
				Map<String, BigDecimal> cntrNo_lnap__loan_bal_m_map = new HashMap<String, BigDecimal>();
				for(Map<String, Object> dataRow3031:child_factType3031_list){
					String lnf020_contract = Util.trim(MapUtils.getString(dataRow3031, "LNF020_CONTRACT"));
					String lnap = Util.trim(MapUtils.getString(dataRow3031, "LNAP"));
					BigDecimal loan_rate_m = (BigDecimal)MapUtils.getObject(dataRow3031, "LOAN_RATE_M");
					BigDecimal loan_bal_m = (BigDecimal)MapUtils.getObject(dataRow3031, "LOAN_BAL_M");
					String tmp_key = lnf020_contract+"-"+lnap;
					cntrNo_lnap__loan_rate_m_map.put(tmp_key, loan_rate_m);
					cntrNo_lnap__loan_bal_m_map.put(tmp_key, loan_bal_m);

				}
				for(Map<String, Object> dataRow:child_list){
				
					String lnf030_charc_code = Util.trim(MapUtils.getString(dataRow, "LNF030_CHARC_CODE"));
					if(Util.equals(UtilConstants.Cntrdoc.snoKind.聯貸, lnf030_charc_code)){
						String lnf020_contract = Util.trim(MapUtils.getString(dataRow, "LNF020_CONTRACT"));					
						String lnap = Util.trim(MapUtils.getString(dataRow, "LNAP"));
						String tmp_key = lnf020_contract+"-"+lnap;
						if(cntrNo_lnap__loan_rate_m_map.containsKey(tmp_key)){				
							dataRow.put("LOAN_RATE_M", cntrNo_lnap__loan_rate_m_map.get(tmp_key));
							dataRow.put("LOAN_BAL_M", cntrNo_lnap__loan_bal_m_map.get(tmp_key));	
						}							
					}				
				}	
			}			
			
			Map<String, PTEAMAPP> grpCntrNo_PTEAMAPP_map = new LinkedHashMap<String, PTEAMAPP>();
			LinkedHashMap<String, Map<String, Object>> grpCntrNo_RawParent_map = new LinkedHashMap<String, Map<String, Object>>();
			for(Map<String, Object> rowData_p : parent_list){
				PTEAMAPP model = new PTEAMAPP();
				DataParse.map2Bean(rowData_p, model);
				//~~~
				grpCntrNo_PTEAMAPP_map.put(model.getGrpcntrno(), model);
				//~~~
				grpCntrNo_RawParent_map.put(model.getGrpcntrno(), rowData_p);
			}
			// ==============================
			List<Pteamapp_LNF> rows_list = CrsUtil.convert_to_Pteamapp_LNF(child_list);
			Set<String> lnf_loan_date_cntrNoSet = new HashSet<String>();
			for(Pteamapp_LNF obj : rows_list){
				lnf_loan_date_cntrNoSet.add(obj.getLnf020_contract());
			}
			if(true){
				String default_dataYM = Util.trim(StringUtils.substring(TWNDate.toAD(CapDate.addMonth(CrsUtil.get_sysMonth_1st(), -1)), 0, 7));
				if(Util.equals("", lnf155_data_ym) || Util.equals(default_dataYM, lnf155_data_ym)){
					
					ISearch search = clsService.getMetaSearch();
					if(true){
						/*
						若 母戶簽案行=201, 子戶承做行=021
						但 c900m01g 裡, 只有 "子戶承做行", 缺少 issuebrno
						search.addSearchModeParameters(SearchMode.LIKE, "cntrNo", issuebrno+"%");
						*/
						if(Util.isNotEmpty(p_grpCntrNo)){
							search.addSearchModeParameters(SearchMode.LIKE, "grpcntrno", p_grpCntrNo+"%");	
						}else{
							search.addSearchModeParameters(SearchMode.IN, "grpcntrno", grpCntrNo_PTEAMAPP_map.keySet());	
						}
						if(includeOnTheWay){
							
						}else{
							//只包含已核准
							search.addSearchModeParameters(SearchMode.NOT_EQUALS, "status", "1");	
						}
						search.addSearchModeParameters(SearchMode.IS_NULL, "lnf_loan_date", "");
						//=========
						search.setMaxResults(Integer.MAX_VALUE);	
					}	
					List<C900M01G> c900m01g_list = clsService.findC900M01G_search(search);
					for(C900M01G c900m01g: c900m01g_list){
						if(Util.equals(c900m01g.getUseFlag(), "D")){
							continue;
						}
						if(lnf_loan_date_cntrNoSet.contains(c900m01g.getCntrNo())){
							/* 測試環境倒檔時間差 */
							continue;
						}
						if(grpCntrNo_PTEAMAPP_map.containsKey(c900m01g.getGrpcntrno())){
							Pteamapp_LNF onTheWay_obj = new Pteamapp_LNF();
							//----------
							BigDecimal fact_amt = null;
							BigDecimal first_fact_amt = null;
							if(true){								
								if(fact_amt==null && c900m01g.getLoanAmt()!=null){
									fact_amt = c900m01g.getLoanAmt();
								}
								if(fact_amt==null && c900m01g.getApproveAmt()!=null){
									fact_amt = c900m01g.getApproveAmt();
								}
								if(fact_amt==null && c900m01g.getApplyAmt()!=null){
									fact_amt = c900m01g.getApplyAmt();
								}	
								if(fact_amt==null){
									fact_amt = BigDecimal.ZERO;
								}									
							}
							if(true){							
								if(first_fact_amt==null && c900m01g.getLoanAmt()!=null){
									first_fact_amt = c900m01g.getLoanAmt();
								}
								if(first_fact_amt==null && c900m01g.getApproveAmt()!=null){
									first_fact_amt = c900m01g.getApproveAmt();
								}
								if(first_fact_amt==null){
									first_fact_amt = BigDecimal.ZERO;
								}
							}
							L140M01A l140m01a = l140m01aDao.findByCntrNoForNew(c900m01g.getCntrNo());
							String cName = "";
							if(l140m01a!=null){
								cName = Util.trim(l140m01a.getCustName());
							}
							onTheWay_obj.setLnf020_cust_id(LMSUtil.getCustKey_len10custId(c900m01g.getCustId(), c900m01g.getDupNo()));
							onTheWay_obj.setBrno(CrsUtil.getBrNoFromLNF020_CONTRACT(c900m01g.getCntrNo()));
							onTheWay_obj.setLnf020_contract(c900m01g.getCntrNo());
							onTheWay_obj.setLnf020_grp_cntrno(c900m01g.getGrpcntrno());
							onTheWay_obj.setRaw_lnf020_grp_cntrno(c900m01g.getGrpcntrno());//中鋼整批團貸, 不會在 e-loan 簽案
							onTheWay_obj.setFact_amt(fact_amt);
							onTheWay_obj.setFirst_fact_amt(first_fact_amt);
							onTheWay_obj.setLoan_bal(BigDecimal.ZERO);
							onTheWay_obj.setLoan_bal_m(BigDecimal.ZERO);
							onTheWay_obj.setCName(cName);
							//----------
							rows_list.add(onTheWay_obj);
						}
					}
				}				
			}
			Map<String, TreeMap<String, Pteamapp_TOT>> totGrpCntrNoMap = CrsUtil.totGrpCntrNoMap(rows_list, dataPeriodFlag);
			
			String str = ""; 
			String filterInfo = "";
			String exeTime = "";
			if(true){				
				if(Util.equals(rptNo, "1") || Util.equals(rptNo, "4")){
					str = prop.getProperty("rptNo.type.1a")+p_dataYM1+prop.getProperty("rptNo.type.1b");
				}else if(Util.equals(rptNo, "2")){
					str = prop.getProperty("rptNo.type.2a")+c_dataYM1+prop.getProperty("rptNo.type.2b")+show_c_dataYM2+prop.getProperty("rptNo.type.2c");
				}else if(Util.equals(rptNo, "3")){
					str = prop.getProperty("rptNo.type.3")+p_grpCntrNo;
				}	
				
				if(true){
					List<String> param_list = new ArrayList<String>();
					if(Util.isNotEmpty(issuebrno)){
						param_list.add( prop.getProperty("pteamapp.issuebrno")+"："+issuebrno);
					}
					if(Util.isNotEmpty(custId)){
						param_list.add( prop.getProperty("pteamapp.custid")+"："+custId);
					}
					if(Util.isNotEmpty(p_grpCntrNo)){
						param_list.add( prop.getProperty("pteamapp.grpcntrno")+"："+p_grpCntrNo);
					}
					String filter = StringUtils.join(param_list, " , ");
					if(Util.equals(filter, "")){
						filter = "無";
					}
					filterInfo = prop.getProperty("filter")+"："+filter;
				}
				if(true){
					exeTime = "執行時間"+"："+new TWNDate().toFullAD('-');
				}
			}			
			int sheetIdx = 0;
			if (isAUDIT) { 
				//J-111-0365 新增按月提供近一年(上年度1月至今年度最近月份)分行敘做「消金整批貸款清單」月報予稽核處
				genExcel_J_107_0046_sheet_detail(workbook, sheetIdx++, rows_list, grpCntrNo_PTEAMAPP_map, rptNo, dataPeriodFlag, lnf155_data_ym, prop, 
						str, filterInfo, exeTime, cellFormatC_14, cellFormatL, cellFormatL_nowrap_Border, cellFormatL_Border, amtTWDFormat, intRate6Format);			
			} else {
				genExcel_J_107_0046_sheet_detail(workbook, sheetIdx++, rows_list, grpCntrNo_PTEAMAPP_map, rptNo, dataPeriodFlag, lnf155_data_ym, prop, 
						str, filterInfo, exeTime, cellFormatC_14, cellFormatL, cellFormatL_nowrap_Border, cellFormatL_Border, amtTWDFormat, intRate6Format);			
				genExcel_J_107_0046_sheet_sum(workbook, sheetIdx++, false, rows_list,grpCntrNo_PTEAMAPP_map, grpCntrNo_RawParent_map, totGrpCntrNoMap, rptNo, dataPeriodFlag, lnf155_data_ym, prop, 
						str, filterInfo, exeTime, cellFormatC_14, cellFormatL, cellFormatL_nowrap_Border, cellFormatL_Border, amtTWDFormat, intRate6Format);
				genExcel_J_107_0046_sheet_sum(workbook, sheetIdx++, true, rows_list,grpCntrNo_PTEAMAPP_map, grpCntrNo_RawParent_map, totGrpCntrNoMap, rptNo, dataPeriodFlag, lnf155_data_ym, prop, 
						str, filterInfo, exeTime, cellFormatC_14, cellFormatL, cellFormatL_nowrap_Border, cellFormatL_Border, amtTWDFormat, intRate6Format);
				genExcel_J_107_0046_sheet_param(workbook, sheetIdx++, rows_list, grpCntrNo_PTEAMAPP_map, grpCntrNo_RawParent_map, rptNo, dataPeriodFlag, lnf155_data_ym, prop, 
						str, filterInfo, exeTime, cellFormatC_14, cellFormatL, cellFormatL_nowrap_Border, cellFormatL_Border, amtTWDFormat, intRate6Format);
				if(Util.equals(UtilConstants.BankNo.資訊處, user.getUnitNo())){
					genExcel_J_107_0046_sheet_parent(workbook, sheetIdx++, rows_list, grpCntrNo_PTEAMAPP_map, grpCntrNo_RawParent_map, rptNo, dataPeriodFlag, lnf155_data_ym, prop, 
							str, filterInfo, exeTime, cellFormatC_14, cellFormatL, cellFormatL_nowrap_Border, cellFormatL_Border, amtTWDFormat, intRate6Format);
				}	
			}
					
			// ==============================
			workbook.write();
			workbook.close();
		}	
	}
	
	private String bal_desc_J_107_0046(String rptNo, String dataPeriodFlag, String lnf155_data_ym){
		if(Util.equals("1", rptNo) || Util.equals("2", rptNo) || Util.equals("4", rptNo)){
			return lnf155_data_ym+"餘額"; 
		}else{
			return "目前餘額";
		}		
	}
	private String int_rate_desc_J_107_0046(String rptNo, String dataPeriodFlag, String lnf155_data_ym){
		if(Util.equals("1", rptNo) || Util.equals("2", rptNo) || Util.equals("4", rptNo)){
			return lnf155_data_ym+"利率"; 
		}else{
			return "目前利率";
		}		
	}
	
	
	private void genExcel_J_107_0046_sheet_detail(WritableWorkbook workbook, int sheetIdx, List<Pteamapp_LNF> rows_list, Map<String, PTEAMAPP> grpCntrNo_PTEAMAPP_map
			, String rptNo, String dataPeriodFlag, String lnf155_data_ym
			, Properties prop, String str, String filterInfo, String exeTime
			, WritableCellFormat cellFormatC_14, WritableCellFormat cellFormatL, WritableCellFormat cellFormatL_nowrap_Border, WritableCellFormat cellFormatL_Border
			, WritableCellFormat amtTWDFormat, WritableCellFormat intRate6Format) 
	throws WriteException{
		WritableSheet sheet1 = workbook.createSheet("子戶明細表(表1)", sheetIdx);
		// ==============================
		Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
		headerMap.put(prop.getProperty("pteamapp.custid"), 16);
		headerMap.put(prop.getProperty("pteamapp.issuebrno"), 10);
		headerMap.put(prop.getProperty("pteamapp.grpcntrno"), 16);			
		headerMap.put(prop.getProperty("real.grpcntrno"), 16);
		headerMap.put(prop.getProperty("pteamapp.projectNm"), 35);
		headerMap.put(prop.getProperty("pteamapp.subCompIdNm"), 20);
		//~~~
		headerMap.put(prop.getProperty("child.brNo"), 10);
		headerMap.put("客戶統編", 16);
		headerMap.put("客戶姓名", 20);
		headerMap.put("子戶額度序號", 16);
		headerMap.put("放款帳號", 18);
		headerMap.put("科目", 8);
		headerMap.put("首撥日", 14);
		headerMap.put(prop.getProperty("child.elf447n_curamt"), 16);
		headerMap.put("首撥金額", 16);
		headerMap.put(prop.getProperty("child.fact_amt"), 16);
		headerMap.put(bal_desc_J_107_0046(rptNo, dataPeriodFlag, lnf155_data_ym), 16);
		headerMap.put(int_rate_desc_J_107_0046(rptNo, dataPeriodFlag, lnf155_data_ym), 16);
		headerMap.put("銷戶日", 14);
		headerMap.put("是否列入72-2(Y/N)", 16);
		
		
		int rowIdx = 0;
		// ==============================
		// 報表表頭
		
		// ==============================
		// 顯示 grid 的 header, detail
		if(true){
			int colIdx = 0;
			for (String h : headerMap.keySet()) {
				int colWidth = headerMap.get(h);
				sheet1.setColumnView(colIdx, colWidth);
				sheet1.addCell(new Label(colIdx, rowIdx, h, cellFormatL_nowrap_Border));
				// ---
				colIdx++;
			}
			// ==============================
			if(true){
				for (Pteamapp_LNF vo : rows_list) {					
					if(true){
						rowIdx++;	
					}	
					int i_col = 0;
					//===========
					PTEAMAPP pteamapp = grpCntrNo_PTEAMAPP_map.get(vo.getRaw_lnf020_grp_cntrno());
					if(pteamapp==null){
						pteamapp = new PTEAMAPP();
					}
					//===========
					BigDecimal first_fact_amt = BigDecimal.ZERO;
					BigDecimal lnf030_1st_ln_amt = BigDecimal.ZERO;
					BigDecimal fact_amt = BigDecimal.ZERO;					
					BigDecimal bal = BigDecimal.ZERO;
					BigDecimal rate = BigDecimal.ZERO;
					if(vo.getFirst_fact_amt()!=null){
						first_fact_amt = vo.getFirst_fact_amt(); 
					}
					if(vo.getLnf030_1st_ln_amt()!=null){
						lnf030_1st_ln_amt = vo.getLnf030_1st_ln_amt(); 
					}
					if(vo.getFact_amt()!=null){
						fact_amt = vo.getFact_amt(); 
					}
					BigDecimal chose_bal = vo.fetch_bal(dataPeriodFlag);
					if(chose_bal!=null){
						bal = chose_bal; 
					}
					BigDecimal chose_rate = vo.fetch_rate(dataPeriodFlag);
					if(chose_rate!=null){
						rate = chose_rate; 
					}
					//===========
					sheet1.addCell(new Label(i_col++, rowIdx, LMSUtil.getCustKey_len10custId(pteamapp.getCustid(), pteamapp.getDupno()) , cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getIssuebrno() , cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, vo.getLnf020_grp_cntrno() , cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, vo.getRaw_lnf020_grp_cntrno(), cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getProjectnm() , cellFormatL_nowrap_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, getSubCompIdNm(pteamapp) , cellFormatL_nowrap_Border));
					
					sheet1.addCell(new Label(i_col++, rowIdx, vo.getBrno() , cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, vo.getLnf020_cust_id() , cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, vo.getCName() , cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, vo.getLnf020_contract() , cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, vo.getLnf030_loan_no() , cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, vo.getLnap() , cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, Util.trim(TWNDate.toAD(vo.getLnf030_loan_date())) , cellFormatL_Border));
					sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, first_fact_amt.doubleValue() , amtTWDFormat));
					sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, lnf030_1st_ln_amt.doubleValue() , amtTWDFormat));
					sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, fact_amt.doubleValue() , amtTWDFormat));
					sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, bal.doubleValue() , amtTWDFormat));
					sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, rate.doubleValue() , intRate6Format));
					sheet1.addCell(new Label(i_col++, rowIdx, Util.trim(TWNDate.toAD(vo.getLnf030_cancel_date())) , cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, Util.trim(vo.getElf506_722_flag()) , cellFormatL_Border));
				}	
			}
		}
	}
	private void genExcel_J_107_0046_sheet_param(WritableWorkbook workbook, int sheetIdx, List<Pteamapp_LNF> rows_list, Map<String, PTEAMAPP> grpCntrNo_PTEAMAPP_map, LinkedHashMap<String, Map<String, Object>> grpCntrNo_RawParent_map 
			, String rptNo, String dataPeriodFlag, String lnf155_data_ym
			, Properties prop, String str, String filterInfo, String exeTime
			, WritableCellFormat cellFormatC_14, WritableCellFormat cellFormatL, WritableCellFormat cellFormatL_nowrap_Border, WritableCellFormat cellFormatL_Border
			, WritableCellFormat amtTWDFormat, WritableCellFormat intRate6Format) 
	throws WriteException{
		WritableSheet sheet1 = workbook.createSheet("輸入條件", sheetIdx);
		int rowIdx = 0;		
		sheet1.addCell(new Label(0, rowIdx, str, cellFormatL));

		rowIdx++;
		sheet1.addCell(new Label(0, rowIdx, filterInfo, cellFormatL));	

		rowIdx++;
		sheet1.addCell(new Label(0, rowIdx, exeTime, cellFormatL));	
	}
	private void genExcel_J_107_0046_sheet_parent(WritableWorkbook workbook, int sheetIdx, List<Pteamapp_LNF> rows_list, Map<String, PTEAMAPP> grpCntrNo_PTEAMAPP_map, LinkedHashMap<String, Map<String, Object>> grpCntrNo_RawParent_map 
			, String rptNo, String dataPeriodFlag, String lnf155_data_ym
			, Properties prop, String str, String filterInfo, String exeTime
			, WritableCellFormat cellFormatC_14, WritableCellFormat cellFormatL, WritableCellFormat cellFormatL_nowrap_Border, WritableCellFormat cellFormatL_Border
			, WritableCellFormat amtTWDFormat, WritableCellFormat intRate6Format) 
	throws WriteException{
		WritableSheet sheet1 = workbook.createSheet("Appendix團貸母戶", sheetIdx);
		// ==============================
		Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
		headerMap.put(prop.getProperty("pteamapp.custid"), 16);
		headerMap.put(prop.getProperty("pteamapp.issuebrno"), 10);
		headerMap.put(prop.getProperty("pteamapp.grpcntrno"), 16);			
		headerMap.put(prop.getProperty("real.grpcntrno"), 16);
		headerMap.put(prop.getProperty("pteamapp.projectNm"), 35);
		headerMap.put(prop.getProperty("pteamapp.subCompIdNm"), 20);
		headerMap.put(prop.getProperty("pteamapp.totamt"), 16);
		headerMap.put("首次核准額度", 16);
		headerMap.put("額度起日", 16);			
		headerMap.put("額度迄日", 16);			
		headerMap.put("最後簽案核准日", 18);
		headerMap.put("縣市", 16);
		headerMap.put("鄉鎮市區", 16);
		headerMap.put("段", 16);
		headerMap.put("小段", 16);
		//~~~
		
		int rowIdx = 0;
		// ==============================
		// 報表表頭
		// ==============================
		// 顯示 grid 的 header, detail
		if(true){
			int colIdx = 0;
			for (String h : headerMap.keySet()) {
				int colWidth = headerMap.get(h);
				sheet1.setColumnView(colIdx, colWidth);
				sheet1.addCell(new Label(colIdx, rowIdx, h, cellFormatL_nowrap_Border));
				// ---
				colIdx++;
			}
			// ==============================
			if(true){
				Set<String> cntrNoSet = new HashSet<String>();
				for(Pteamapp_LNF vo: rows_list){
					cntrNoSet.add(vo.getLnf020_contract());
					cntrNoSet.add(vo.getRaw_lnf020_grp_cntrno());
				}
				
				for (String grpCntrNo: grpCntrNo_RawParent_map.keySet()) {
					PTEAMAPP pteamapp = grpCntrNo_PTEAMAPP_map.get(grpCntrNo);
					if(pteamapp==null){
						pteamapp = new PTEAMAPP();
					}
					if(true){
						if(Util.equals(rptNo, "1") || Util.equals(rptNo, "4")){
							//依母戶
						}else if(Util.equals(rptNo, "2")){
							//在 UI 是依子戶, 在 select 時先全抓
							
							if(cntrNoSet.contains(grpCntrNo)){
								//show
							}else{
								continue;
							}
						}else if(Util.equals(rptNo, "3")){
							//已限定1筆
						}
					}
					if(true){
						rowIdx++;	
					}	
					int i_col = 0;
					//===========
					
					Map<String, Object> rawParent = grpCntrNo_RawParent_map.get(grpCntrNo);
					BigDecimal totAmt = BigDecimal.ZERO;
					if(pteamapp.getTotamt()!=null){
						totAmt = pteamapp.getTotamt(); 
					}
					
					BigDecimal first_totAmt = totAmt; // default 值, 在100年之前的 grpCntrNo 缺少 ELF447N
					if(true){
						BigDecimal val_first_totAmt = (BigDecimal)MapUtils.getObject(rawParent, "ELF447N_CURAMT");
						if(val_first_totAmt!=null){
							first_totAmt = val_first_totAmt;	
						}						 
					}
					
					/*
					 * 中鋼子公司, 流用母額度的金額, 沒有各個子公司自己的限額
					 */
					if(Util.isNotEmpty(Util.trim(pteamapp.getMgrpcntrno()))){
						totAmt = BigDecimal.ZERO;
						first_totAmt = BigDecimal.ZERO;
					}					
					//中鋼子公司, 流用母額度時, 沒有簽案 => ELF447_CHKDATE_MAX 為空
					//===========
					sheet1.addCell(new Label(i_col++, rowIdx, LMSUtil.getCustKey_len10custId(pteamapp.getCustid(), pteamapp.getDupno()) , cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getIssuebrno() , cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, Util.trim(MapUtils.getString(rawParent, "LNF020_GRP_CNTRNO")), cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, grpCntrNo, cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getProjectnm() , cellFormatL_nowrap_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, getSubCompIdNm(pteamapp) , cellFormatL_nowrap_Border));
					sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, totAmt.doubleValue() , amtTWDFormat));
					sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, first_totAmt.doubleValue() , amtTWDFormat));
					sheet1.addCell(new Label(i_col++, rowIdx, Util.trim(TWNDate.toAD(pteamapp.getEfffrom())), cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, Util.trim(TWNDate.toAD(pteamapp.getEffend())), cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, Util.trim(TWNDate.toAD((Date)MapUtils.getObject(rawParent, "ELF447_CHKDATE_MAX"))), cellFormatL_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getSite1() , cellFormatL_nowrap_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getSite2() , cellFormatL_nowrap_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getSite3() , cellFormatL_nowrap_Border));
					sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getSite4() , cellFormatL_nowrap_Border));
				}	
			}
		}
	}
	
	private String getSubCompIdNm(PTEAMAPP pteamapp){
		return Util.trim( pteamapp.getSubcompid()+" "+pteamapp.getSubcompnm());		
	}
	
	private void genExcel_J_107_0046_sheet_sum(WritableWorkbook workbook, int sheetIdx, boolean byBrNo, List<Pteamapp_LNF> rows_list, Map<String, PTEAMAPP> grpCntrNo_PTEAMAPP_map, LinkedHashMap<String, Map<String, Object>> grpCntrNo_RawParent_map
			, Map<String, TreeMap<String, Pteamapp_TOT>> totGrpCntrNoMap
			, String rptNo, String dataPeriodFlag, String lnf155_data_ym
			, Properties prop, String str, String filterInfo, String exeTime
			, WritableCellFormat cellFormatC_14, WritableCellFormat cellFormatL, WritableCellFormat cellFormatL_nowrap_Border, WritableCellFormat cellFormatL_Border
			, WritableCellFormat amtTWDFormat, WritableCellFormat intRate6Format) 
	throws WriteException{
		WritableSheet sheet1 = null;
		if(byBrNo){
			sheet1 = workbook.createSheet("母戶按分行敘作合計表(表3)", sheetIdx );	
		}else{
			sheet1 = workbook.createSheet("母戶總覽表(表2)", sheetIdx);
		}
		// ==============================
		Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
		headerMap.put(prop.getProperty("pteamapp.custid"), 16);
		headerMap.put(prop.getProperty("pteamapp.issuebrno"), 10);
		headerMap.put(prop.getProperty("pteamapp.grpcntrno"), 16);
		if(!byBrNo){
			headerMap.put(prop.getProperty("real.grpcntrno"), 16);
		}
		headerMap.put(prop.getProperty("pteamapp.projectNm"), 70);
		if(!byBrNo){
			headerMap.put(prop.getProperty("pteamapp.subCompIdNm"), 16);
		}
		headerMap.put(prop.getProperty("pteamapp.totamt"), 19);
		if(byBrNo){
			headerMap.put(prop.getProperty("child.brNo"), 10);
		}else{
			headerMap.put("首次核准額度", 16);
			headerMap.put("額度起日", 16);			
			headerMap.put("額度迄日", 16);			
			headerMap.put("最後簽案核准日", 18);
			headerMap.put("縣市", 16);
			headerMap.put("鄉鎮市區", 16);
			headerMap.put("段", 16);
			headerMap.put("小段", 16);
		}
		headerMap.put("件數by ID", 12);
		headerMap.put("件數by 額度", 14);
		headerMap.put("件數by 帳號", 14);
		headerMap.put("科目=321筆數", 16);
		headerMap.put("科目<>321筆數", 16);
		headerMap.put(prop.getProperty("child.elf447n_curamt")+"合計", 16);
		headerMap.put(prop.getProperty("child.fact_amt")+"合計", 16);
		headerMap.put(bal_desc_J_107_0046(rptNo, dataPeriodFlag, lnf155_data_ym)+"合計", 18);
		if(!byBrNo){
			headerMap.put("是否消貸", 10);
		}
		//~~~		
		int rowIdx = 0;		
		// ==============================
		// 顯示 grid 的 header, detail
		if(true){			
			int colIdx = 0;
			for (String h : headerMap.keySet()) {
				int colWidth = headerMap.get(h);
				sheet1.setColumnView(colIdx, colWidth);
				sheet1.addCell(new Label(colIdx, rowIdx, h, cellFormatL_nowrap_Border));
				// ---
				colIdx++;
			}
			// ==============================
			if(byBrNo){
				for (String grpCntrNo: totGrpCntrNoMap.keySet()) {				
					
					PTEAMAPP pteamapp = grpCntrNo_PTEAMAPP_map.get(grpCntrNo);
					if(pteamapp==null){
						pteamapp = new PTEAMAPP();
					}
					Map<String, Pteamapp_TOT> totBrNoMap = totGrpCntrNoMap.get(grpCntrNo);
					//===========
					for(String brNo: totBrNoMap.keySet() ){
						if(true){
							rowIdx++;	
						}	
						int i_col = 0;
						//===========
						sheet1.addCell(new Label(i_col++, rowIdx, LMSUtil.getCustKey_len10custId(pteamapp.getCustid(), pteamapp.getDupno()) , cellFormatL_Border));
						sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getIssuebrno() , cellFormatL_Border));
						sheet1.addCell(new Label(i_col++, rowIdx, grpCntrNo, cellFormatL_Border));
						sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getProjectnm() , cellFormatL_nowrap_Border));
						
						sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, pteamapp.getTotamt().doubleValue() , amtTWDFormat));
						//=========================
						Pteamapp_TOT tot = totBrNoMap.get(brNo);
						if(true){
							sheet1.addCell(new Label(i_col++, rowIdx, brNo, cellFormatL_Border));	
						}
						sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getCntById() , amtTWDFormat));
						sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getCntByCntrNo() , amtTWDFormat));
						sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getCntByLoanNo() , amtTWDFormat));
						sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getCntLnapEq321() , amtTWDFormat));
						sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getCntLnapDiff321() , amtTWDFormat));
						sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getSumFirstFactAmt().doubleValue() , amtTWDFormat));
						sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getSumFactAmt().doubleValue() , amtTWDFormat));
						sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getSumBal().doubleValue() , amtTWDFormat));							
					}
				}
			}else{
				Set<String> cntrNoSet = new HashSet<String>();
				for(Pteamapp_LNF vo: rows_list){
					cntrNoSet.add(vo.getLnf020_contract());
					cntrNoSet.add(vo.getRaw_lnf020_grp_cntrno());
				}
				
//				for (String grpCntrNo: totGrpCntrNoMap.keySet()) {				
				for (String grpCntrNo: grpCntrNo_RawParent_map.keySet()) {
					PTEAMAPP pteamapp = grpCntrNo_PTEAMAPP_map.get(grpCntrNo);
					if(pteamapp==null){
						pteamapp = new PTEAMAPP();
					}
					
					if(true){
						if(Util.equals(rptNo, "1") || Util.equals(rptNo, "4")){
							//依母戶
						}else if(Util.equals(rptNo, "2")){
							//在 UI 是依子戶, 在 select 時先全抓
							
							if(cntrNoSet.contains(grpCntrNo)){
								//show
							}else{
								continue;
							}
						}else if(Util.equals(rptNo, "3")){
							//已限定1筆
						}
					}
					/*
					 	中鋼流用子戶, 其合計後的結果 totGrpCntrNoMap, keySet 不會包含流用子戶
					 */
					Map<String, Pteamapp_TOT> totBrNoMap = totGrpCntrNoMap.containsKey(grpCntrNo)?totGrpCntrNoMap.get(grpCntrNo):new HashMap<String, Pteamapp_TOT>();
					Map<String, Object> rawParent = grpCntrNo_RawParent_map.get(grpCntrNo);
					//===========
						if(true){
							rowIdx++;	
						}	
						int i_col = 0;
						//===========						
						sheet1.addCell(new Label(i_col++, rowIdx, LMSUtil.getCustKey_len10custId(pteamapp.getCustid(), pteamapp.getDupno()) , cellFormatL_Border));
						sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getIssuebrno() , cellFormatL_Border));
						sheet1.addCell(new Label(i_col++, rowIdx, Util.trim(MapUtils.getString(rawParent, "LNF020_GRP_CNTRNO")), cellFormatL_Border));
						sheet1.addCell(new Label(i_col++, rowIdx, grpCntrNo, cellFormatL_Border));
						sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getProjectnm() , cellFormatL_nowrap_Border));		
						sheet1.addCell(new Label(i_col++, rowIdx, getSubCompIdNm(pteamapp) , cellFormatL_nowrap_Border));
						
						if(true){
							
							BigDecimal totAmt = BigDecimal.ZERO;
							if(pteamapp.getTotamt()!=null){
								totAmt = pteamapp.getTotamt(); 
							}
							
							BigDecimal first_totAmt = totAmt; // default 值, 在100年之前的 grpCntrNo 缺少 ELF447N
							if(true){
								BigDecimal val_first_totAmt = (BigDecimal)MapUtils.getObject(rawParent, "ELF447N_CURAMT");
								if(val_first_totAmt!=null){
									first_totAmt = val_first_totAmt;	
								}						 
							}				
							
							/*
							 * 中鋼子公司, 流用母額度的金額, 沒有各個子公司自己的限額
							 */
							if(Util.isNotEmpty(Util.trim(pteamapp.getMgrpcntrno()))){
								totAmt = BigDecimal.ZERO;
								first_totAmt = BigDecimal.ZERO;
							}		
							sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, totAmt.doubleValue() , amtTWDFormat));
							sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, first_totAmt.doubleValue() , amtTWDFormat));
							sheet1.addCell(new Label(i_col++, rowIdx, Util.trim(TWNDate.toAD(pteamapp.getEfffrom())), cellFormatL_Border));
							sheet1.addCell(new Label(i_col++, rowIdx, Util.trim(TWNDate.toAD(pteamapp.getEffend())), cellFormatL_Border));
							sheet1.addCell(new Label(i_col++, rowIdx, Util.trim(TWNDate.toAD((Date)MapUtils.getObject(rawParent, "ELF447_CHKDATE_MAX"))), cellFormatL_Border));
							sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getSite1() , cellFormatL_nowrap_Border));
							sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getSite2() , cellFormatL_nowrap_Border));
							sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getSite3() , cellFormatL_nowrap_Border));
							sheet1.addCell(new Label(i_col++, rowIdx, pteamapp.getSite4() , cellFormatL_nowrap_Border));
						}
						//=========================
						Pteamapp_TOT tot = CrsUtil.sumAllBrNo(totBrNoMap);
						if(true){
							sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getCntById() , amtTWDFormat));
							sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getCntByCntrNo() , amtTWDFormat));
							sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getCntByLoanNo() , amtTWDFormat));
							sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getCntLnapEq321() , amtTWDFormat));
							sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getCntLnapDiff321() , amtTWDFormat));
							sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getSumFirstFactAmt().doubleValue() , amtTWDFormat));
							sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getSumFactAmt().doubleValue() , amtTWDFormat));
							sheet1.addCell(new jxl.write.Number(i_col++, rowIdx, tot.getSumBal().doubleValue() , amtTWDFormat));	
						}
						
						if(true){
							String is321 = "";
							if(tot.getCntLnapEq321()>0){
								if(tot.getCntLnapDiff321()>0){
									is321 = "P";
								}else{
									is321 = "A";
								}
							}else{
								if(tot.getCntLnapDiff321()>0){
									is321 = "N";
								}else{
									is321 = "N";
								}
							}
							/*
							 * 中鋼子公司, 流用母額度的金額, 沒有各個子公司自己的限額
							 */
							if(Util.isNotEmpty(Util.trim(pteamapp.getMgrpcntrno()))){
								is321 = "";
							}	
							sheet1.addCell(new Label(i_col++, rowIdx, is321, cellFormatL_Border));
						}
										
				}	
			}
		}
	}

	/**
	 * 傳送稽核處data
	 */
	@Override
	public List<Map<String, Object>> get_J_107_0046ForAudit(boolean includeOnTheWay,
			String raw_brNo, String custId, String p_grpCntrNo, String rptNo,
			String p_dataYM1, String c_dataYM1, String c_dataYM2,
			boolean isAUDIT) {
		List<Map<String, Object>> lstData = new ArrayList<Map<String, Object>>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS9071V02Page.class);
		
		String issuebrno = raw_brNo;
		if(true){
			String logonBr = user.getUnitNo();
			if(Util.isEmpty(Util.trim(raw_brNo))){
				if (isAUDIT || Util.equals(UtilConstants.BankNo.資訊處, logonBr)
						|| Util.equals(UtilConstants.BankNo.授管處, logonBr)
						|| Util.equals(UtilConstants.BankNo.授信行銷處, logonBr)
						|| Util.equals(UtilConstants.BankNo.消金業務處, logonBr)) {
					//可查全部
				}else{
					//只可查「報案分行」=自己的資料
					issuebrno = user.getUnitNo();	
				}			
			}else{
				//前端已選擇
			}			
		}
		
		//======
		String show_c_dataYM2 = c_dataYM2;
		String p_dataYM_X = p_dataYM1;
		String p_dataYM_Y = p_dataYM1;
		String c_dataYM_X = c_dataYM1;
		String c_dataYM_Y= c_dataYM2;
		String lnf155_data_ym = "";
		String dataPeriodFlag = "M";
		if(true){
			if(Util.equals(rptNo, "1")){
				c_dataYM_X = "1900-01";
				c_dataYM_Y = "9999-12";
				if(true){
					c_dataYM_Y = p_dataYM1;
				}
				lnf155_data_ym = p_dataYM1;
			}else if(Util.equals(rptNo, "2")){
				p_dataYM_X = "9999-12";
				p_dataYM_Y = "1900-01";
				
				lnf155_data_ym = c_dataYM2;
			}else if(Util.equals(rptNo, "3")){
				p_dataYM_X = "9999-12";
				p_dataYM_Y = "1900-01";
				c_dataYM_X = "1900-01";
				c_dataYM_Y = "9999-12";
				
				show_c_dataYM2 = Util.trim(StringUtils.substring(TWNDate.toAD(CapDate.addMonth(CrsUtil.get_sysMonth_1st(), -1)), 0, 7));

				dataPeriodFlag = "D";
			} else if(Util.equals(rptNo, "4")){
				//J-111-0365 新增按月提供近一年(上年度1月至今年度最近月份)分行敘做「消金整批貸款清單」月報予稽核處
				//註解原因，母戶p_dataYM_X抓，抓dataYM還有效即可，不然筆數會太多
//				p_dataYM_X = "9999-12";
//				p_dataYM_Y = "1900-01";
				
				//c_dataYM_X //子戶撥款日起，從前面傳入
				//c_dataYM_Y //子戶撥款日迄，從前面傳入
				if(true){
					c_dataYM_Y = p_dataYM1;
				}
				lnf155_data_ym = p_dataYM1;
			}	
		}
		
		List<Map<String, Object>> parent_list = new ArrayList<Map<String, Object>>();
		if(true){
			List<Map<String, Object>> parent1_list = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> parent2_list = misdbBASEService.gen_J_107_0046_parent(issuebrno, custId, p_grpCntrNo, p_grpCntrNo, p_dataYM_X, p_dataYM_Y);
			if(Util.isNotEmpty(p_grpCntrNo)){
				PTEAMAPP pteamapp = misPTEAMAPPService.getDataByLnf020GrpCntrNo(p_grpCntrNo);
				if(pteamapp!=null){
					String mGrpCntrNo = Util.trim(pteamapp.getMgrpcntrno());
					if(Util.isNotEmpty(mGrpCntrNo)){
						parent1_list = misdbBASEService.gen_J_107_0046_parent(issuebrno, custId, mGrpCntrNo, p_grpCntrNo, p_dataYM_X, p_dataYM_Y);
					}
				}
			}
			parent_list.addAll(parent1_list);
			parent_list.addAll(parent2_list);
			
		}
		List<Map<String, Object>> child_list = misdbBASEService.gen_J_107_0046_child(issuebrno, custId, p_grpCntrNo, p_grpCntrNo, p_dataYM_X, p_dataYM_Y, c_dataYM_X, c_dataYM_Y, lnf155_data_ym);
		if(Util.equals(rptNo, "1") || Util.equals(rptNo, "4")){
			//團貸子戶可能是2個分行攤貸 , LNF155只有31的帳號, 但為了「統計筆數」能精確一點, XLS只抓出30的帳號
			//此狀況，另外去抓 lnf155
			List<Map<String, Object>> child_factType3031_list = misdbBASEService.gen_J_107_0046_childfor_factType3031(issuebrno, custId, p_grpCntrNo, p_grpCntrNo, p_dataYM1);
			Map<String, BigDecimal> cntrNo_lnap__loan_rate_m_map = new HashMap<String, BigDecimal>();
			Map<String, BigDecimal> cntrNo_lnap__loan_bal_m_map = new HashMap<String, BigDecimal>();
			for(Map<String, Object> dataRow3031:child_factType3031_list){
				String lnf020_contract = Util.trim(MapUtils.getString(dataRow3031, "LNF020_CONTRACT"));
				String lnap = Util.trim(MapUtils.getString(dataRow3031, "LNAP"));
				BigDecimal loan_rate_m = (BigDecimal)MapUtils.getObject(dataRow3031, "LOAN_RATE_M");
				BigDecimal loan_bal_m = (BigDecimal)MapUtils.getObject(dataRow3031, "LOAN_BAL_M");
				String tmp_key = lnf020_contract+"-"+lnap;
				cntrNo_lnap__loan_rate_m_map.put(tmp_key, loan_rate_m);
				cntrNo_lnap__loan_bal_m_map.put(tmp_key, loan_bal_m);

			}
			for(Map<String, Object> dataRow:child_list){
			
				String lnf030_charc_code = Util.trim(MapUtils.getString(dataRow, "LNF030_CHARC_CODE"));
				if(Util.equals(UtilConstants.Cntrdoc.snoKind.聯貸, lnf030_charc_code)){
					String lnf020_contract = Util.trim(MapUtils.getString(dataRow, "LNF020_CONTRACT"));					
					String lnap = Util.trim(MapUtils.getString(dataRow, "LNAP"));
					String tmp_key = lnf020_contract+"-"+lnap;
					if(cntrNo_lnap__loan_rate_m_map.containsKey(tmp_key)){				
						dataRow.put("LOAN_RATE_M", cntrNo_lnap__loan_rate_m_map.get(tmp_key));
						dataRow.put("LOAN_BAL_M", cntrNo_lnap__loan_bal_m_map.get(tmp_key));	
					}							
				}				
			}	
		}			
		
		Map<String, PTEAMAPP> grpCntrNo_PTEAMAPP_map = new LinkedHashMap<String, PTEAMAPP>();
		LinkedHashMap<String, Map<String, Object>> grpCntrNo_RawParent_map = new LinkedHashMap<String, Map<String, Object>>();
		for(Map<String, Object> rowData_p : parent_list){
			PTEAMAPP model = new PTEAMAPP();
			DataParse.map2Bean(rowData_p, model);
			//~~~
			grpCntrNo_PTEAMAPP_map.put(model.getGrpcntrno(), model);
			//~~~
			grpCntrNo_RawParent_map.put(model.getGrpcntrno(), rowData_p);
		}
		// ==============================
		List<Pteamapp_LNF> rows_list = CrsUtil.convert_to_Pteamapp_LNF(child_list);
		Set<String> lnf_loan_date_cntrNoSet = new HashSet<String>();
		for(Pteamapp_LNF obj : rows_list){
			lnf_loan_date_cntrNoSet.add(obj.getLnf020_contract());
		}
		if(true){
			String default_dataYM = Util.trim(StringUtils.substring(TWNDate.toAD(CapDate.addMonth(CrsUtil.get_sysMonth_1st(), -1)), 0, 7));
			if(Util.equals("", lnf155_data_ym) || Util.equals(default_dataYM, lnf155_data_ym)){
				
				ISearch search = clsService.getMetaSearch();
				if(true){
					/*
					若 母戶簽案行=201, 子戶承做行=021
					但 c900m01g 裡, 只有 "子戶承做行", 缺少 issuebrno
					search.addSearchModeParameters(SearchMode.LIKE, "cntrNo", issuebrno+"%");
					*/
					if(Util.isNotEmpty(p_grpCntrNo)){
						search.addSearchModeParameters(SearchMode.LIKE, "grpcntrno", p_grpCntrNo+"%");	
					}else{
						search.addSearchModeParameters(SearchMode.IN, "grpcntrno", grpCntrNo_PTEAMAPP_map.keySet());	
					}
					if(includeOnTheWay){
						
					}else{
						//只包含已核准
						search.addSearchModeParameters(SearchMode.NOT_EQUALS, "status", "1");	
					}
					search.addSearchModeParameters(SearchMode.IS_NULL, "lnf_loan_date", "");
					//=========
					search.setMaxResults(Integer.MAX_VALUE);	
				}	
				List<C900M01G> c900m01g_list = clsService.findC900M01G_search(search);
				for(C900M01G c900m01g: c900m01g_list){
					if(Util.equals(c900m01g.getUseFlag(), "D")){
						continue;
					}
					if(lnf_loan_date_cntrNoSet.contains(c900m01g.getCntrNo())){
						/* 測試環境倒檔時間差 */
						continue;
					}
					if(grpCntrNo_PTEAMAPP_map.containsKey(c900m01g.getGrpcntrno())){
						Pteamapp_LNF onTheWay_obj = new Pteamapp_LNF();
						//----------
						BigDecimal fact_amt = null;
						BigDecimal first_fact_amt = null;
						if(true){								
							if(fact_amt==null && c900m01g.getLoanAmt()!=null){
								fact_amt = c900m01g.getLoanAmt();
							}
							if(fact_amt==null && c900m01g.getApproveAmt()!=null){
								fact_amt = c900m01g.getApproveAmt();
							}
							if(fact_amt==null && c900m01g.getApplyAmt()!=null){
								fact_amt = c900m01g.getApplyAmt();
							}	
							if(fact_amt==null){
								fact_amt = BigDecimal.ZERO;
							}									
						}
						if(true){							
							if(first_fact_amt==null && c900m01g.getLoanAmt()!=null){
								first_fact_amt = c900m01g.getLoanAmt();
							}
							if(first_fact_amt==null && c900m01g.getApproveAmt()!=null){
								first_fact_amt = c900m01g.getApproveAmt();
							}
							if(first_fact_amt==null){
								first_fact_amt = BigDecimal.ZERO;
							}
						}
						L140M01A l140m01a = l140m01aDao.findByCntrNoForNew(c900m01g.getCntrNo());
						String cName = "";
						if(l140m01a!=null){
							cName = Util.trim(l140m01a.getCustName());
						}
						onTheWay_obj.setLnf020_cust_id(LMSUtil.getCustKey_len10custId(c900m01g.getCustId(), c900m01g.getDupNo()));
						onTheWay_obj.setBrno(CrsUtil.getBrNoFromLNF020_CONTRACT(c900m01g.getCntrNo()));
						onTheWay_obj.setLnf020_contract(c900m01g.getCntrNo());
						onTheWay_obj.setLnf020_grp_cntrno(c900m01g.getGrpcntrno());
						onTheWay_obj.setRaw_lnf020_grp_cntrno(c900m01g.getGrpcntrno());//中鋼整批團貸, 不會在 e-loan 簽案
						onTheWay_obj.setFact_amt(fact_amt);
						onTheWay_obj.setFirst_fact_amt(first_fact_amt);
						onTheWay_obj.setLoan_bal(BigDecimal.ZERO);
						onTheWay_obj.setLoan_bal_m(BigDecimal.ZERO);
						onTheWay_obj.setCName(cName);
						//----------
						rows_list.add(onTheWay_obj);
					}
				}
			}				
		}
		Map<String, TreeMap<String, Pteamapp_TOT>> totGrpCntrNoMap = CrsUtil.totGrpCntrNoMap(rows_list, dataPeriodFlag);
		
		for (Pteamapp_LNF vo : rows_list) {
			Map<String, Object> map = new HashMap<String, Object>();
			int i_col = 0;
			//===========
			PTEAMAPP pteamapp = grpCntrNo_PTEAMAPP_map.get(vo.getRaw_lnf020_grp_cntrno());
			if(pteamapp==null){
				pteamapp = new PTEAMAPP();
			}
			//===========
			BigDecimal first_fact_amt = BigDecimal.ZERO;
			BigDecimal lnf030_1st_ln_amt = BigDecimal.ZERO;
			BigDecimal fact_amt = BigDecimal.ZERO;					
			BigDecimal bal = BigDecimal.ZERO;
			BigDecimal rate = BigDecimal.ZERO;
			if(vo.getFirst_fact_amt()!=null){
				first_fact_amt = vo.getFirst_fact_amt(); 
			}
			if(vo.getLnf030_1st_ln_amt()!=null){
				lnf030_1st_ln_amt = vo.getLnf030_1st_ln_amt(); 
			}
			if(vo.getFact_amt()!=null){
				fact_amt = vo.getFact_amt(); 
			}
			BigDecimal chose_bal = vo.fetch_bal(dataPeriodFlag);
			if(chose_bal!=null){
				bal = chose_bal; 
			}
			BigDecimal chose_rate = vo.fetch_rate(dataPeriodFlag);
			if(chose_rate!=null){
				rate = chose_rate; 
			}
			//===========
			map.put("field1", LMSUtil.getCustKey_len10custId(pteamapp.getCustid(), pteamapp.getDupno()));
			map.put("field2", pteamapp.getIssuebrno());
			map.put("field3", vo.getLnf020_grp_cntrno());
			map.put("field4", vo.getRaw_lnf020_grp_cntrno());
			map.put("field5", pteamapp.getProjectnm());
			map.put("field6", getSubCompIdNm(pteamapp));
			map.put("field7", vo.getBrno());
			map.put("field8", vo.getLnf020_cust_id());
			map.put("field9", vo.getCName());
			map.put("field10", vo.getLnf020_contract());
			map.put("field11", vo.getLnf030_loan_no());
			map.put("field12", vo.getLnap());
			map.put("field13", Util.trim(TWNDate.toAD(vo.getLnf030_loan_date())));
			map.put("field14", first_fact_amt.toString());
			map.put("field15", lnf030_1st_ln_amt.toString() );
			map.put("field16", fact_amt.toString());
			map.put("field17", bal.toString());
			map.put("field18", rate.toString());
			map.put("field19", Util.trim(TWNDate.toAD(vo.getLnf030_cancel_date())));
			
			lstData.add(map);
		}
		return lstData;
	}
}
