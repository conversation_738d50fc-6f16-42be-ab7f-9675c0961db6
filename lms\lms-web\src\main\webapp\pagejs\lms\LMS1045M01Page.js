var initDfd = $.Deferred();
var _handler = "lms1045m01formhandler";

$(document).ready(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	var initControl_lockDoc = false;

	$.ajax({
	  type: "POST",
	  handler: _handler,
	  data: {//把資料轉成json
	      formAction: "queryc123m01a",
	      mainOid: $("#mainOid").val() || responseJSON.mainOid
	  },              
	  success: function(json){
	  	// 控制頁面 Read/Write
			if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
				tabForm.lockDoc();
				initControl_lockDoc = true;
				json['initControl_lockDoc'] = initControl_lockDoc;
			}
			tabForm.injectData(json);
	  }
	});
	
	btnPanel.find("#btnSave").click(function(){
		if(!tabForm.valid()){
			return;
		}
		if(checkNum()){
			saveAction({'allowIncomplete':'Y'}).done(function(json){
				if(json.saveOkFlag){
					if(json.IncompleteMsg){
						API.showMessage(i18n.def.saveSuccess+"<br/>-------------------<br/>"+json.IncompleteMsg);
					}else{
						API.showMessage(i18n.def.saveSuccess);	
					}	
				}
			});
		}
		else{
			API.showErrorMessage(i18n.lms1045m01['ErrorMessage']);
		}
	}).end().find("#btnSend").click(function(){
		if(!tabForm.valid()){
			return;
		}
		if(checkNum()){
			saveAction().done(function(json_saveAction){
	    		if(json_saveAction.saveOkFlag){
	    			API.confirmMessage(i18n.def.confirmApply, function(result){
	    	            if (result) {
	    	            	flowAction({'decisionExpr':'呈主管'});    	
	    	        	}
	    	    	});
	    		}
	    	});
		}
		else{
			API.showErrorMessage(i18n.lms1045m01['ErrorMessage']);
		}
	}).end().find("#btnAccept").click(function(){
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		var decisionExprExtend = "";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.def['accept']+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.def['return']+"</label></p>");
			dyna.push("</form>");		
			dyna.push("</div>");		
		    $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	flowAction({'decisionExpr':'核定'});
                    }else if(val=="2"){
                    	flowAction({'decisionExpr':'退回'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	}).end().find("#btnPrint").click(function(){
		if (checkReadonly()) {
            printAction();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                	if(checkNum()){
                		saveAction().done(function(){printAction();});
                	}
            		else{
            			API.showErrorMessage(i18n.lms1045m01['ErrorMessage']);
            		}
                }
            });
        }
	});
	
	var saveAction = function(opts){		
		if(tabForm.valid()){			
			return $.ajax({
                type: "POST",
                handler: _handler,
                data:$.extend( {
                	formAction: "saveMain",
                    mainOid: $("#mainOid").val() || responseJSON.mainOid
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
                ),                
                success: function(json){
                	responseJSON.mainOid = json.mainOid;
                	$("#mainOid").val(json.mainOid);
                	tabForm.injectData(json);
                	//更新 opener 的 Grid
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                }
            });
		}else{
			return $.Deferred();
		}
	}
	
	var flowAction = function(opts){
		return $.ajax({
	        type: "POST",
	        handler: _handler, 
	        action: "flowAction",
	        data:$.extend( {
				        	mainOid: $("#mainOid").val(),
				        	mainDocStatus: $("#mainDocStatus").val() 
			            }, ( opts||{} )
			        ),
	        success: function(json){            	
	        	API.triggerOpener(); 
	        	window.close();            	
	        }
	    });
	}
	
    // 列印動作
    function printAction(){
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                mainOid: $("#mainOid").val() || responseJSON.mainOid,   
                fileDownloadName: "lms1045.pdf",
                serviceName: "lms1045rptservice"
            }
        });
    }
	
	/**
     * Score
     */
	var $BEACON = tabForm.find("#selectBEACON");
	var $ServiceRatio = tabForm.find("#selectServiceRatio");
	var $LTV = tabForm.find("#selectLTV");
	var $Years = tabForm.find("#selectYears");
	var $Stability = tabForm.find("#selectStability");
	var $Types = tabForm.find("#selectTypes");
	var $Assessment = tabForm.find("#selectAssessment");
	
	var $TotalScore = tabForm.find("#TotalScore");
	var $RiskRating = tabForm.find("#RiskRating");
	var $EvaluationResults = tabForm.find("#EvaluationResults");
	var $Frequency = tabForm.find("#Frequency");
	
	$BEACON.change(function(){ 
		CalculationScore();
	})
	$ServiceRatio.change(function(){ 
		CalculationScore();
	})
	$LTV.change(function(){ 
		CalculationScore();
	})
	$Years.change(function(){ 
		CalculationScore();
	})
	$Stability.change(function(){ 
		CalculationScore();
	})
	$Types.change(function(){ 
		CalculationScore();
	})
	$Assessment.change(function(){ 
		CalculationScore();
	})
	
	function CalculationScore(){
		var Sum = parseInt($BEACON.val()) + parseInt($ServiceRatio.val()) + parseInt($LTV.val()) + parseInt($Years.val()) 
			+ parseInt($Stability.val()) + parseInt($Types.val()) + parseInt($Assessment.val());
		$TotalScore.text(Sum);
		ChangeRiskRating();
	}
	
	function ChangeRiskRating(){
		var TotalScoreValue = $TotalScore.text();
		switch (true) {
			case (TotalScoreValue < 50 ):
				$RiskRating.text('10');
				break;
			case (TotalScoreValue >= 50 && TotalScoreValue <= 54 ):
				$RiskRating.text('9');
				break;
			case (TotalScoreValue >= 55 && TotalScoreValue <= 59 ):
				$RiskRating.text('8');
				break;
			case (TotalScoreValue >= 60 && TotalScoreValue <= 64 ):
				$RiskRating.text('7');
				break;
			case (TotalScoreValue >= 65 && TotalScoreValue <= 69 ):
				$RiskRating.text('6');
				break;
			case (TotalScoreValue >= 70 && TotalScoreValue <= 74 ):
				$RiskRating.text('5');
				break;
			case (TotalScoreValue >= 75 && TotalScoreValue <= 79 ):
				$RiskRating.text('4');
				break;
			case (TotalScoreValue >= 80 && TotalScoreValue <= 84 ):
				$RiskRating.text('3');
				break;
			case (TotalScoreValue >= 85 && TotalScoreValue <= 89 ):
				$RiskRating.text('2');
				break;
			case (TotalScoreValue >= 90 ):
				$RiskRating.text('1');
				break;
		}
		ChangeEvaluationResults();
	}
	
	function ChangeEvaluationResults(){
		var RiskRatingValue = $RiskRating.text();
		switch (RiskRatingValue) {
			case '10':
				$EvaluationResults.text('Loss');
				break;
			case '9':
				$EvaluationResults.text('Doubtful');
				break;
			case '8':
			case '7':
				$EvaluationResults.text('Substandard');
				break;
			case '6':
			case '5':
				$EvaluationResults.text('Especially Mentioned');
				break;
			case '4':
			case '3':
			case '2':
			case '1':
				$EvaluationResults.text('Satisfactory');
				break;
		}
		ChangeFrequency();
	}
	
	function ChangeFrequency(){
		var EvaluationResultsValue = $EvaluationResults.text();
		switch (EvaluationResultsValue) {
			case "Loss":
				$Frequency.text('--');
				break;
			case "Doubtful":
			case "Substandard":
				$Frequency.text('Quarterly');
				break;
			case "Especially Mentioned":
				$Frequency.text('Semi-Annually');
				break;
			case "Satisfactory":
				$Frequency.text('Annually');
				break;
		}
	}
	
	//驗證readOnly狀態
	function checkReadonly(){
		var auth = (responseJSON ? responseJSON.Auth : {}); //權限
	    if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
			return true ;
		}
		return false ;
	}
	
	function checkNum(){
		if(parseInt($BEACON.val())>0 && parseInt($ServiceRatio.val())>0 && parseInt($LTV.val())>0 && parseInt($Years.val())>0 
				&& parseInt($Stability.val())>0 && parseInt($Types.val())>0 && parseInt($Assessment.val())>0) {
			return true ;
		}
		return false ;
	}
});