/* 
 * CLS1141V09Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.GridViewFilterPanel01;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 國內簡易行待覆核(個金)
 * </pre>
 * 
 * @since 2013/3/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/5,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1141v09")
public class CLS1141V09Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.國內簡易行待覆核);
		// 加上Button
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		// 主管跟經辦都會出現的按鈕
		list.add(LmsButtonEnum.View);
		list.add(LmsButtonEnum.Filter);

		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			list.add(LmsButtonEnum.Change);
			list.add(LmsButtonEnum.TableSend);
			list.add(LmsButtonEnum.PrintBook);
		}
		addToButtonPanel(model, list);
		// 套用哪個i18N檔案
		renderJsI18N(CLS1141V01Page.class);

		setupIPanel(new GridViewFilterPanel01(PANEL_ID), model, params);

		// UPGRADE: 待確認畫面是否正常
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1141V01Page');");
	}

}
