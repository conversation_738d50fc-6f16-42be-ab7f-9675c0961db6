package com.mega.eloan.lms.dao.impl;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.mega.eloan.lms.dao.L192M01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01C;

@Repository
public class L192M01CDaoImpl extends LMSJpaDao<L192M01C, String> implements
		L192M01CDao {

	@Override
	public int deleteByMeta(L192M01A meta) {
		Query query = entityManager
				.createNamedQuery("l192m01c.deleteByMainId");
		query.setParameter("MAINID", meta.getMainId());
		return query.executeUpdate();
	}

}
