package com.mega.eloan.lms.rpt.service.impl;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import jxl.Cell;
import jxl.SheetSettings;
import jxl.Workbook;
import jxl.WorkbookSettings;
import jxl.format.Alignment;
import jxl.format.PageOrientation;
import jxl.format.PaperSize;
import jxl.format.VerticalAlignment;
import jxl.read.biff.BiffException;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.util.HtmlUtils;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.dao.ElsUserDao;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.gwclient.AUDITFTPClient;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.base.service.ProdService.ProdKindEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.pages.CLS1161S02APage;
import com.mega.eloan.lms.cls.report.impl.CLS1141R01RptServiceImpl;
import com.mega.eloan.lms.dao.C160M01BDao;
import com.mega.eloan.lms.dao.C160S01BDao;
import com.mega.eloan.lms.dao.C900M01ADao;
import com.mega.eloan.lms.dao.C900M01BDao;
import com.mega.eloan.lms.dao.C900S02CDao;
import com.mega.eloan.lms.dao.L180R02ADao;
import com.mega.eloan.lms.dao.L784A01ADao;
import com.mega.eloan.lms.dao.L784M01ADao;
import com.mega.eloan.lms.dao.L784S01ADao;
import com.mega.eloan.lms.dao.L784S07ADao;
import com.mega.eloan.lms.dao.LMSBATCHDao;
import com.mega.eloan.lms.dao.LMSRPTDao;
import com.mega.eloan.lms.dao.VL784S07A01Dao;
import com.mega.eloan.lms.dw.service.DWCLSReportService;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.LmsCustdataService;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF447Service;
import com.mega.eloan.lms.mfaloan.service.MisElcsecntService;
import com.mega.eloan.lms.mfaloan.service.MisEllnseekservice;
import com.mega.eloan.lms.mfaloan.service.MisRptDataService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C900M01A;
import com.mega.eloan.lms.model.C900M03A;
import com.mega.eloan.lms.model.C900S02E;
import com.mega.eloan.lms.model.C900S02F;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L180R02A;
import com.mega.eloan.lms.model.L784S01A;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.eloan.lms.model.LMSBATCH;
import com.mega.eloan.lms.model.LMSRPT;
import com.mega.eloan.lms.model.VL784S07A01;
import com.mega.eloan.lms.rpt.pages.LMS9511V01Page;
import com.mega.eloan.lms.rpt.report.LMS9511R01RptService;
import com.mega.eloan.lms.rpt.report.LMS9511R04RptService;
import com.mega.eloan.lms.rpt.service.LMS9511R01XLSService;
import com.mega.eloan.lms.rpt.service.LMS9511Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS9511ServiceImpl extends AbstractCapService implements
		LMS9511Service {
	private static final Logger logger = LoggerFactory
			.getLogger(LMS9511ServiceImpl.class);

	private static final String LRS_CODETYPESTR = "lms9511v01_docType1";
	private static final String CRS_CODETYPESTR = "lms9511v01_docType2";

	private static final String CLS180R03_ADJUST_GRADE = "ADJUST_GRADE";
	private static final String CLS180R03_NOWFROM_NOWEND = "NOWFROM_NOWEND";
	private static final String CLS180R03R04_SUM_CTRTXAMT = "SUM_CTRTXAMT"; // J-108-0290
																			// 近3年內移轉成交價格
	private static final String CLS180R04_TOTALLOANAMT = "TOTALLOANAMT";
	private static final String COLLTYPE05 = "05";
	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	LMS9511R01XLSService lms9511r01XlsService;

	@Resource
	LMS9511R01RptService lms9511r01Service;

	@Resource
	MisEllnseekservice misEllnseekService;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	MisElcsecntService misElcsecntService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	LMSRPTDao lmsRptDao;

	@Resource
	LMSBATCHDao lmsBatchDao;

	@Resource
	ProdService prodService;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	L784S01ADao l784s01aDao;

	@Resource
	L180R02ADao l180r02aDao;

	@Resource
	VL784S07A01Dao vl784s07a01Dao;

	@Resource
	L784S07ADao l784s07aDao;

	@Resource
	LMSService lmsService;

	@Resource
	MisRptDataService misService;

	@Resource
	DWCLSReportService clsDwService;

	@Resource
	DocFileService docFileService;

	@Resource
	BranchService branchService;

	@Resource
	LMS9511R01RptService lms9511r01rptService;

	@Resource
	MisELF447Service misElf447Service;

	@Resource
	DocFileDao docFileDao;

	@Resource
	RetrialService retrialService;

	// ============CLS============
	@Resource
	LMS9511R04RptService clsRptService;

	@Resource
	C160M01BDao c160m01bDao;

	@Resource
	L784A01ADao l784a01aDao;

	@Resource
	L784M01ADao l784m01aDao;

	@Resource
	C160S01BDao c160s01bDao;

	// 對照產品種類
	@Resource
	C900M01ADao c900m01aDao;

	@Resource
	C900M01BDao c900m01bDao;

	@Resource
	C900S02CDao c900s02cDao;

	@Resource
	CLSService clsService;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	LmsCustdataService lmsCustdataService;

	@Resource
	AUDITFTPClient auditFtpClient;

	@Resource
	ElsUserDao elsUserDao;

	@Resource
	UserInfoService userInfoService;

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof LMSRPT) {
					((LMSRPT) model).setUpdater(user.getUserId());
					((LMSRPT) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					lmsRptDao.save((LMSRPT) model);
				} else if (model instanceof LMSBATCH) {
					((LMSBATCH) model).setUpdater(user.getUserId());
					((LMSBATCH) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					lmsBatchDao.save((LMSBATCH) model);
				}
			}

		}

	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof LMSRPT) {
					lmsRptDao.delete((LMSRPT) model);
				} else if (model instanceof LMSBATCH) {
					lmsBatchDao.delete((LMSBATCH) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == LMSRPT.class) {
			return lmsRptDao.findPage(search);
		} else if (clazz == LMSBATCH.class) {
			return lmsBatchDao.findPage(search);
		} else if (clazz == L784S01A.class) {
			return l784s01aDao.findPage(search);
		} else if (clazz == VL784S07A01.class) {
			return vl784s07a01Dao.findPage(search);
		} else if (clazz == L784S07A.class) {
			return l784s07aDao.findPage(search);
		} else if (clazz == L180R02A.class) {
			return l180r02aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == LMSRPT.class) {
			return (T) lmsRptDao.findByOid(oid);
		} else if (clazz == LMSBATCH.class) {
			return (T) lmsBatchDao.findByOid(oid);
		} else if (clazz == L784S01A.class) {
			return (T) l784s01aDao.findByOid(oid);
		} else if (clazz == L180R02A.class) {
			return (T) l180r02aDao.findByOid(oid);
		}
		return null;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public <T extends GenericBean> T findModelByMainId(Class clazz,
			String mainId) {
		if (clazz == LMSRPT.class) {
			return (T) lmsRptDao.findByIndex03(mainId);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == LMSRPT.class) {
			return lmsRptDao.findByMainId(mainId);
		} else if (clazz == LMSBATCH.class) {
			return lmsBatchDao.findByMainId(mainId);
		} else if (clazz == L180R02A.class) {
			return l180r02aDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public List<L180R02A> findL180R02AListByIndex02(String mainId,
			String areaBranchId, String audit) {
		return l180r02aDao.findByIndex02(mainId, areaBranchId, audit);
	}

	@Override
	public List<L180R02A> findL180R02AListByMainId(String mainId) {
		return l180r02aDao.findByMainId(mainId);
	}

	@Override
	public LMSBATCH findLMSBATCHByMainId(String mainId) {
		return lmsBatchDao.findByIndex03(mainId);
	}

	@Override
	public boolean checkL180R02AData(String unitNo, String rptNo, Date bngDate,
			Date endDate) {
		String mainId = null;
		boolean checkLmsRptResult = false;
		boolean checkAResult = false;
		List<LMSRPT> lmsrptList = null;
		lmsrptList = lmsRptDao.findByIndex02(unitNo, bngDate, endDate, rptNo,
				"Y");
		if (lmsrptList.size() != 0) {
			for (LMSRPT lmsRpt : lmsrptList) {
				// 代表已經送授管處
				if (Util.isNotEmpty(lmsRpt.getSendTime())) {
					checkLmsRptResult = true;
				} else {
					checkAResult = true;
				}
			}
			if (checkAResult) {

			} else if (checkLmsRptResult) {
				List<L180R02A> l180r02aList = this.findL180R02AListByIndex02(
						mainId, null, null);
				if (l180r02aList != null) {
					for (L180R02A l180r02a : l180r02aList) {
						if ("Y".equals(l180r02a.getAudit())) {
							checkAResult = true;
						}
					}
				} else {
					checkAResult = true;
				}
			} else {
				checkAResult = true;
			}
		} else {
			checkAResult = true;
		}

		return checkAResult;
	}

	@Override
	public boolean checkL180R01Data(String remark, String unitNo,
			String userId, String rptNo, Date bngDate, Date endDate) {
		boolean checkLmsRptResult = true;
		List<LMSRPT> lmsrptList = null;
		lmsrptList = lmsRptDao.findByIndex02(unitNo, bngDate, endDate, rptNo,
				"Y");
		if (lmsrptList.size() != 0) {
			for (LMSRPT lmsRpt : lmsrptList) {
				if ("Y".equals(lmsRpt.getCfrmFlag())) {
					checkLmsRptResult = false;
				}
			}
		} else {
			checkLmsRptResult = true;
		}

		return checkLmsRptResult;
	}

	@Override
	public String checkAndReplaceBatchData(String debugStr, String remark,
			String lmsbatch_branch, String userId, String rptNo, Date bngDate,
			Date endDate) throws RowsExceededException, BiffException,
			WriteException, IOException, URISyntaxException,
			CapMessageException, Exception {
		String mainId = null;
		List<LMSBATCH> lmsBatchList = new LinkedList<LMSBATCH>();
		List<LMSRPT> lmsrptList = null;
		List<LMSRPT> lmsrptList2 = new LinkedList<LMSRPT>();
		boolean result = false;
		List<LMSRPT> lmsRptList2 = null;
		boolean checkAResult = false;
		// 授管處必須要有一筆才可以看的見資料
		if (UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單.equals(rptNo)
				|| UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單.equals(rptNo)
				|| UtilConstants.RPTREPORT.DOCTYPE1.營運中心每日授權外授信案件清單
						.equals(rptNo)) {
			lmsRptList2 = lmsRptDao.findByIndex04(UtilConstants.BankNo.授管處,
					rptNo, bngDate, "Y");
			// 如果有的話 則不需要新增
			if (lmsRptList2.size() > 0) {
				result = false;
			} else {
				result = true;
			}
		}
		logger.debug("條件[lmsbatch_branch=" + lmsbatch_branch + "][bngDate="
				+ Util.trim(TWNDate.toAD(bngDate)) + "][endDate="
				+ Util.trim(TWNDate.toAD(endDate)) + "][rptNo=" + rptNo + "]");

		// TODO 在此判斷, 當[brNo, rptNo, nowRpt='Y', bngDate, endDate ]都相同, 則 update
		// lms.lmsrpt set reportOidFile=? where mainId=?
		// 在 /elnfs/LMS/brNo/year/mainId/rptNo 新增檔案, 其檔名=reportOidFile 的32碼
		lmsrptList = lmsRptDao.findByIndex02(lmsbatch_branch, bngDate, endDate,
				rptNo, "Y");

		_debug("lmsrptList.size()=" + lmsrptList.size() + ",bngDate="
				+ TWNDate.toAD(bngDate) + ",endDate=" + TWNDate.toAD(endDate));

		if (lmsrptList.size() > 0) {
			for (LMSRPT lmsRpt : lmsrptList) {
				checkAResult = true;
				mainId = lmsRpt.getMainId();
				LMSBATCH batch = new LMSBATCH();
				batch.setMainId(lmsRpt.getMainId());
				batch.setBgnDate(lmsRpt.getBgnDate());
				batch.setEndDate(lmsRpt.getEndDate());
				batch.setDataDate(lmsRpt.getDataDate());
				batch.setNowRpt(lmsRpt.getNowRpt());
				batch.setRptNo(lmsRpt.getRptNo());

				if (rptNo.startsWith(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料)) {
					// 因同樣的 rptNo, 可能在 remark 的參數不同(EX:由單一分行 012 改成 030 )
					// 導致下載的檔名有異動
					// 所以重產生 rptName
					batch.setRptName(CrsUtil.cls180R14Name(rptNo,
							lmsbatch_branch, remark));
				} else if (rptNo
						.equals(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表)) {
					batch.setRptName(CrsUtil.cls180R15Name(rptNo,
							lmsbatch_branch, remark));
				} else if (rptNo
						.equals(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表)) {
					batch.setRptName(CrsUtil.cls180R15BName(rptNo,
							lmsbatch_branch, remark));
				} else if (rptNo
						.equals(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表)) {
					batch.setRptName(CrsUtil.cls180R15CName(rptNo,
							lmsbatch_branch, remark));
				} else if (rptNo
						.equals(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表)) {
					batch.setRptName(CrsUtil.cls180R15DName(rptNo,
							lmsbatch_branch, remark));
				} else if (rptNo
						.equals(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表)) {
					batch.setRptName(CrsUtil.cls180R15EName(rptNo,
							lmsbatch_branch, remark));
				} else if (rptNo
						.equals(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表)) {
					// J-112-0465
					// 新增「○○區營運中心覆審類別「額度一千萬元以下十足擔保信保七成循環動用」抽樣之授信戶明細表
					batch.setRptName(CrsUtil.cls180R15FName(rptNo,
							lmsbatch_branch, remark));
				} else if (rptNo
						.startsWith(UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表)) {
					String rptName = get_rptName(CRS_CODETYPESTR, batch);
					batch.setRptName(CrsUtil.cls180R16Name(rptName,
							batch.getBgnDate(), batch.getEndDate()));
				} else if (rptNo
						.startsWith(UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單)) {
					Map<String, String> brInfoMap = retrialService
							.get_lrs_rpt_brMap(remark);
					String rptName = get_rptName(LRS_CODETYPESTR, batch);
					batch.setRptName(LrsUtil.lms180R19Name(rptName,
							batch.getDataDate(), brInfoMap));
				} else if (rptNo
						.startsWith(UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單)) {
					Map<String, String> brInfoMap = retrialService
							.get_lrs_rpt_brMap(remark);
					String rptName = get_rptName(LRS_CODETYPESTR, batch);
					batch.setRptName(LrsUtil.lms180R20Name(rptName, brInfoMap));
				} else if (rptNo
						.startsWith(UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表)) {
					Map<String, String> brInfoMap = retrialService
							.get_lrs_rpt_brMap(remark);
					String rptName = get_rptName(LRS_CODETYPESTR, batch);
					batch.setRptName(LrsUtil.lms180R21Name(rptName,
							batch.getBgnDate(), batch.getEndDate(), brInfoMap));
				} else if (rptNo
						.startsWith(UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表)) {
					Map<String, String> brInfoMap = retrialService
							.get_lrs_rpt_brMap(remark);
					String rptName = get_rptName(LRS_CODETYPESTR, batch);
					batch.setRptName(LrsUtil.lms180R22Name(rptName, brInfoMap));
				} else if (rptNo
						.startsWith(UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表)) {
					String rptName = get_rptName(LRS_CODETYPESTR, batch);
					batch.setRptName(LrsUtil.lms180R23Name(rptName,
							batch.getBgnDate(), batch.getEndDate()));
				} else if (rptNo
						.startsWith(UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表)) {
					String rptName = get_rptName(LRS_CODETYPESTR, batch);
					batch.setRptName(LrsUtil.lms180R24Name(rptName,
							batch.getBgnDate(), batch.getEndDate()));
				} else if (rptNo
						.startsWith(UtilConstants.RPTREPORT.DOCTYPE1.覆審經理記點統計明細表)) {
					// 更改 rptName
					String rptName = get_rptName(LRS_CODETYPESTR, batch);
					batch.setRptName(LrsUtil.lms180R28Name(rptName,
							batch.getBgnDate(), batch.getEndDate()));
				} else if (Util.equals(rptNo,
						UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報_稽核處)) {
					batch.setRptName(build_cls_rptName(batch));
				} else if (Util.equals(rptNo,
						UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表_稽核處)) {
					batch.setRptName(build_cls_rptName(batch));
				} else {
					batch.setRptName(lmsRpt.getRptName());
				}
				// ========================================================
				batch.setRemarks(lmsRpt.getRemarks());
				batch.setRandomCode(lmsRpt.getRandomCode() == null ? IDGenerator
						.getUUID() : lmsRpt.getRandomCode());
				batch.setUpdater(userId);
				batch.setUpdateTime(CapDate.getCurrentTimestamp());
				if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單)) {
					List<L784S01A> l784s01aList = l784s01aDao
							.findByMainId(batch.getMainId());
					l784s01aDao.delete(l784s01aList);
					// checkAResult = true;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營運中心每日授權外授信案件清單)) {
					// 傳送授管處
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表)) {
					// 已敘作消金案件清單
					lmsRptList2 = lmsRptDao.findByIndex04(
							UtilConstants.BankNo.授管處, rptNo, bngDate, "Y");
					for (LMSRPT record : lmsRptList2) {
						if (UtilConstants.BankNo.授管處.equals(record.getBranch())) {
							this.delete(record);
						}
					}
					batch.setRemarks(remark);
					checkAResult = true;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.審件統計表)) {
					List<DocFile> files = docFileService.findByIDAndPid(mainId,
							null);
					String prodKind = remark.split(";")[0].split("=")[1];
					for (DocFile beenCreate : files) {
						if (prodKind
								.equals(beenCreate.getFieldId().split("-")[1])) {
							// 搜尋該類是否已有歷史報表
							// 刪除原檔案
							beenCreate.setDeletedTime(CapDate
									.getCurrentTimestamp());
							docFileService.save(beenCreate);
						}
					}
					batch.setRemarks(remark);
					checkAResult = true;
				} else if (rptNo.substring(0, 3).equals("CLS")) {
					batch.setRemarks(remark);
					checkAResult = true;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單)) {
					List<L180R02A> l180r02aList = l180r02aDao.findByIndex02(
							mainId, null, null);

					l180r02aDao.delete(l180r02aList);
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.覆審經理記點統計明細表)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.簽報階段都更危老業務統計表)) {
					// 更改 remark
					batch.setRemarks(remark);
				}

				_debug("[" + batch.getOid() + "]["
						+ TWNDate.toAD(batch.getBgnDate()) + "~"
						+ TWNDate.toAD(batch.getEndDate()) + "]"
						+ batch.getRemarks());

				if (checkAResult) {
					batch.setBranch(lmsRpt.getBranch());
					lmsBatchList.add(batch);
					lmsrptList2.add(lmsRpt);
				}
			}
		}
		if (result) {
			if (UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單.equals(rptNo)
					|| UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單
							.equals(rptNo)
					|| UtilConstants.RPTREPORT.DOCTYPE1.營運中心每日授權外授信案件清單
							.equals(rptNo)) {
				Map<String, String> rptNameMap = codetypeService
						.findByCodeType(LRS_CODETYPESTR);
				LMSRPT lmsRpt = new LMSRPT();
				lmsRpt.setMainId(IDGenerator.getUUID());
				lmsRpt.setBgnDate(bngDate);
				lmsRpt.setEndDate(endDate);
				lmsRpt.setBranch(UtilConstants.BankNo.授管處);
				lmsRpt.setDataDate(bngDate);
				lmsRpt.setNowRpt("Y");
				lmsRpt.setRptNo(rptNo);
				lmsRpt.setRptName(rptNameMap.get(rptNo));
				lmsRpt.setRandomCode(IDGenerator.getUUID());
				lmsRpt.setRemarks("");
				lmsRpt.setUpdater("SYSTEM");
				lmsRpt.setUpdateTime(CapDate.getCurrentTimestamp());
				eloandbBaseService.updateNowTyperptTbl(
						Util.trim(lmsRpt.getRptNo()),
						Util.trim(lmsRpt.getBranch()));
				lmsRptDao.save(lmsRpt);
			}
		}
		lmsBatchDao.save(lmsBatchList);
		lmsRptDao.delete(lmsrptList2);
		return mainId;
	}

	// TODO 若接收到的 param 值有誤，再把此 method 啟動來 debug
	private void _debug(String s) {

	}

	@Override
	public String get_rptName(String codeTypeStr, LMSBATCH batch) {

		String rptName = batch.getRptNo();
		CodeType codetype = codetypeService.findByCodeTypeAndCodeValue(
				codeTypeStr, batch.getRptNo());
		if (codetype != null) {
			rptName = Util.trim(codetype.getCodeDesc());
		}
		return rptName;
	}

	@Override
	public LMSBATCH addbatchData(String remark, String lmsbatch_branch,
			String rptNo, Date bngDate, Date endDate, String mainId)
			throws RowsExceededException, BiffException, WriteException,
			IOException, URISyntaxException, CapMessageException, Exception {
		Date dataDate = CapDate.getCurrentTimestamp();
		Map<String, String> rptNameMap = null;
		String rptStart = rptNo.substring(0, 3);
		if (rptStart.equals("LMS")) {
			rptNameMap = codetypeService.findByCodeType(LRS_CODETYPESTR);
		} else if (rptStart.equals("CLS")) {
			// Vector done
			rptNameMap = codetypeService.findByCodeType(CRS_CODETYPESTR);
		}

		if (rptNameMap == null) {
			rptNameMap = new LinkedHashMap<String, String>();
		}
		LMSBATCH batchTbl = null;
		if (mainId != null) {
			batchTbl = lmsBatchDao.findByIndex03(mainId);
		}
		// 若是沒有同區間內相同時間資料 就得新產過一筆資料
		if (batchTbl == null) {
			batchTbl = new LMSBATCH();
			mainId = IDGenerator.getUUID();
			if (rptStart.equals("LMS")) {
				dataDate = bngDate;
				if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單)) {
					// 已敘做授信案件清單
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單)) {
					// 營運中心已敘做授信案件清單
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單)) {
					// 信保案件未動用屆期清單
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表)) {
					// 授信契約已逾期控制表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表)) {
					// 授信契約產生主辦聯貸案一覽表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信案件統計表)) {
					// 授信案件統計表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.金融機構辦理振興經濟非中小企業專案貸款)) {
					// 金融機構辦理振興經濟非中小企業專案貸款
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企業自行申請展延案件案件統計表)) {
					// 企業自行申請展延案件
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營業單位授信報案考核彙總表)) {
					// 營業單位授信報案考核彙總表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營業單位授信報案考核彙總表_季報)) {
					// 營業單位授信報案考核彙總表_季報
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營運中心每日授權外授信案件清單)) {
					// 區域中心每日授權外授信案件清單
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.常董會報告事項彙總及申報案件數統計表)) {
					// 常董會報告事項彙總及申報案件數統計表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.本行各營業單位各級授權範圍內承做授信案件統計表)) {
					// 本行各營業單位各級授權範圍內承做授信案件統計表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.分行授信淨增加額度統計表)) {
					// 分行授信淨增加額度統計表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.振興經濟非中小企業專案貸款暨信用保證要點執行情形調查表)) {
					// 振興經濟非中小企業專案貸款暨信用保證要點執行情形調查表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報)) {
					// 授信業務異常通報月報
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表)) {
					// 授信異常通報案件報送統計表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表)) {
					dataDate = endDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.覆審經理記點統計明細表)) {
					dataDate = CapDate.getCurrentTimestamp();
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.中小企業創新發展專案貸款執行情形統計月報表)) {
					// 中小企業創新發展專案貸款執行情形統計月報表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.金控總部分行逾期尚未辦理覆審名單_一次性)) {
					// 金控總部分行逾期尚未辦理覆審名單_一次性
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.尚未完成常董會企金戶實地覆審名單_一次性)) {
					// 尚未完成常董會企金戶實地覆審名單_一次性
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金聯貸新作案件統計表)) {
					// 企金聯貸新作案件統計表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授信案件敘做情形及比較表)) {
					// 企金授信案件敘做情形及比較表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金共同行銷報表)) {
					dataDate = CapDate.getCurrentTimestamp();
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.簽報階段都更危老業務統計表)) {
					dataDate = CapDate.getCurrentTimestamp();
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.新核准往來客戶及新增放款額度統計表)) {
					// 新核准往來客戶及新增放款額度統計表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.新核准往來客戶明細表)) {
					// 新核准往來客戶明細表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.共同行銷擔保品投保未結案明細表)) {
					dataDate = CapDate.getCurrentTimestamp();
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企業社會責任貸放情形統計表)) {
					dataDate = CapDate.getCurrentTimestamp();
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.對區域營運中心授信覆審作業之管理績效考核表)) {
					// 對區域營運中心授信覆審作業之管理績效考核表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.中小企業創新發展專案貸款執行情形統計月報表)) {
					dataDate = CapDate.getCurrentTimestamp();
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.法令遵循自評授信案件明細報表)) {
					// 法令遵循自評授信案件明細報表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案辦理情形統計表)) {
					// 兆元振興融資方案辦理情形統計表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案明細表)) {
					// 兆元振興融資方案明細表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案辦理情形總表)) {
					// 兆元振興融資方案辦理情形預估報表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案分行核准情形總表)) {
					// 兆元振興融資方案分行核准情形總表
					// 兆元振興融資方案明細表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人授信異常通報表)) {
					// 兆元振興融資方案辦理情形預估報表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.青年創業及啟動金貸款辦理情形總表)) {
					// J-109-0362 青年創業及啟動金貸款辦理情形總表 LMS180R59
					dataDate = CapDate.getCurrentTimestamp();
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人兌付振興三倍券達888張名單)) {
					// 小規模營業人兌付振興三倍券達888張名單
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表)) {
					// 額度專案種類明細表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.案件屬110年行銷名單來源客戶簽案資料)) {
					// 案件屬110年行銷名單來源客戶簽案資料
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表)) {
					// 境內法人於國際金融業務分行辦理外幣授信業務報表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表_不含小規)) {
					// 授信異常通報案件報送統計表_不含小規
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授權外案件流程進度控管表)) {
					// 企金授權外案件流程進度控管表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.全行地政士黑名單CSV)) {
					// 全行地政士黑名單
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.中小企業千億振興融資方案)) {
					// 中小企業千億振興融資方案
				}
			} else if (rptStart.equals("CLS")) {
				// Vector done
				if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單)) {
					// 報案考核表被扣分清單
					dataDate = bngDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.婉拒案件明細表)) {
					// 婉拒案件明細表
					dataDate = bngDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.已收件待辦中案件報表)) {
					// 已收件待辦中案件報表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單)) {
					// 已敘作消金案件清單
					dataDate = bngDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報_稽核處)) {
					// 授權內分行敘做房屋貸款月報
					dataDate = bngDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表)
						|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表_稽核處)) {
					// 授權內已敘做個人消費金融業務月報表
					dataDate = bngDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.審件統計表)) {
					// 審件統計表
					dataDate = bngDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表)) {
					// 敘做無自用住宅購屋放款明細表
					dataDate = bngDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金授信已逾期控制表)) {
					// 消金授信已逾期控制表
					dataDate = bngDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金處新做案件_當月_當年度累計_統計表)) {
					// 在 2018-10-02 產生 2018-09 的資料，在 grid 顯示的資料年月應該要是
					// 2018-09(而非系統日)
					dataDate = bngDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表)) {
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表)) {
				} else if (rptNo
						.startsWith(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料)) {
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表)) {
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表)) {
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表)) {
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表)) {
					// J-112-0465
					// 新增「○○區營運中心覆審類別「額度一千萬元以下十足擔保信保七成循環動用」抽樣之授信戶明細表
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表)) {
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表)) {
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表)) {
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金整批房貸消貸敘做明細)) {
					dataDate = bngDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金線上貸款餘額表)) {
					dataDate = bngDate;
				} else if (Util.equals(
						UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單,
						rptNo)
						|| Util.equals(
								UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單,
								rptNo)
						|| Util.equals(
								UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單,
								rptNo)) {
					dataDate = bngDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息明細表)) {
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.全行地政士黑名單)) {
					// 全行地政士黑名單
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸案件明細表)) {
					if (Util.isEmpty(bngDate) && Util.isEmpty(endDate)) {
						String sysDate = TWNDate.toAD(CapDate
								.getCurrentTimestamp());
						String thisYear = String
								.valueOf(Util.parseInt(StringUtils.substring(
										sysDate, 0, 4)));
						Date batchBgnDate = CapDate.addMonth(
								CapDate.parseDate(StringUtils.substring(
										sysDate, 0, 7) + "-01"), -1);
						Date batchEndDate = CapDate.shiftDays(
								CapDate.parseDate(StringUtils.substring(
										sysDate, 0, 7) + "-01"), -1);
						bngDate = batchBgnDate;
						endDate = batchEndDate;
					}
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.客戶訪談紀錄表)) {
					// 客戶訪談紀錄表
					dataDate = bngDate;
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸ESG明細表)) {
					if (Util.isEmpty(bngDate) && Util.isEmpty(endDate)) {
						String sysDate = TWNDate.toAD(CapDate
								.getCurrentTimestamp());
						String thisYear = String
								.valueOf(Util.parseInt(StringUtils.substring(
										sysDate, 0, 4)));
						Date batchBgnDate = CapDate.addMonth(
								CapDate.parseDate(StringUtils.substring(
										sysDate, 0, 7) + "-01"), -1);
						Date batchEndDate = CapDate.shiftDays(
								CapDate.parseDate(StringUtils.substring(
										sysDate, 0, 7) + "-01"), -1);
						bngDate = batchBgnDate;
						endDate = batchEndDate;
					}
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸KYC分案報表)) {
					if (Util.isEmpty(bngDate) && Util.isEmpty(endDate)) {
						String sysDate = TWNDate.toAD(CapDate
								.getCurrentTimestamp());
						Date batchBgnDate = CapDate.addMonth(
								CapDate.parseDate(StringUtils.substring(
										sysDate, 0, 7) + "-01"), -1);
						Date batchEndDate = CapDate.shiftDays(
								CapDate.parseDate(StringUtils.substring(
										sysDate, 0, 7) + "-01"), -1);
						bngDate = batchBgnDate;
						endDate = batchEndDate;
					}
				} else if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸婉拒案件自動發送簡訊失敗顧客清單)) {
					if (Util.isEmpty(bngDate) && Util.isEmpty(endDate)) {
						String sysDate = TWNDate.toAD(CapDate
								.getCurrentTimestamp());
						Date batchBgnDate = CapDate.addMonth(
								CapDate.parseDate(StringUtils.substring(
										sysDate, 0, 7) + "-01"), -1);
						Date batchEndDate = CapDate.shiftDays(
								CapDate.parseDate(StringUtils.substring(
										sysDate, 0, 7) + "-01"), -1);
						bngDate = batchBgnDate;
						endDate = batchEndDate;
					}
				}
			}

			batchTbl.setRemarks(remark);
			batchTbl.setBgnDate(bngDate);
			batchTbl.setEndDate(endDate);
			batchTbl.setNowRpt("Y");
			batchTbl.setDataDate(dataDate);
			batchTbl.setBthDate(CapDate.getCurrentTimestamp());
			batchTbl.setBranch(lmsbatch_branch);
			batchTbl.setMainId(mainId);
			batchTbl.setRptNo(rptNo);
			batchTbl.setRandomCode(IDGenerator.getUUID());
			batchTbl.setRptName(Util.trim(rptNameMap.get(rptNo)));

			if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表)) {
				batchTbl.setRptName(batchTbl.getRptName() + "("
						+ StringUtils.substring(TWNDate.toAD(bngDate), 0, 4)
						+ ")");
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表)) {
				batchTbl.setRptName(StringUtils.substring(
						TWNDate.toAD(bngDate), 0, 7)
						+ "~"
						+ StringUtils.substring(TWNDate.toAD(endDate), 0, 7)
						+ batchTbl.getRptName());
			} else if (rptNo
					.startsWith(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料)) {
				batchTbl.setRptName(CrsUtil.cls180R14Name(rptNo,
						lmsbatch_branch, remark));
			} else if (rptNo
					.startsWith(UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息明細表)) {
				batchTbl.setRptName(CrsUtil.CLS250R01Name(rptNo,
						lmsbatch_branch, remark));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審名單)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審件數控管表)) {
				String rptName = batchTbl.getRptName();
				batchTbl.setRptName(CrsUtil.cls180R18Name(rptName,
						batchTbl.getEndDate()));
			} else if (rptNo
					.startsWith(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處註記清單)) {
				batchTbl.setRptName(CrsUtil.CLS180R21Name(rptNo,
						lmsbatch_branch, remark));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表)) {
				batchTbl.setRptName(CrsUtil.cls180R15Name(rptNo,
						lmsbatch_branch, remark));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表)) {
				batchTbl.setRptName(CrsUtil.cls180R15BName(rptNo,
						lmsbatch_branch, remark));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表)) {
				batchTbl.setRptName(CrsUtil.cls180R15EName(rptNo,
						lmsbatch_branch, remark));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表)) {
				batchTbl.setRptName(CrsUtil.cls180R15CName(rptNo,
						lmsbatch_branch, remark));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表)) {
				batchTbl.setRptName(CrsUtil.cls180R15DName(rptNo,
						lmsbatch_branch, remark));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表)) {
				// J-112-0465 新增「○○區營運中心覆審類別「額度一千萬元以下十足擔保信保七成循環動用」抽樣之授信戶明細表
				batchTbl.setRptName(CrsUtil.cls180R15FName(rptNo,
						lmsbatch_branch, remark));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表)) {
				String rptName = batchTbl.getRptName();
				batchTbl.setRptName(CrsUtil.cls180R16Name(rptName,
						batchTbl.getBgnDate(), batchTbl.getEndDate()));
			} else if (Util.equals(rptNo,
					UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報_稽核處)
					|| Util.equals(
							rptNo,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表_稽核處)) {
				batchTbl.setRptName(build_cls_rptName(batchTbl));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單)) {

				Map<String, String> brInfoMap = retrialService
						.get_lrs_rpt_brMap(batchTbl.getRemarks());
				batchTbl.setRptName(LrsUtil.lms180R19Name(
						batchTbl.getRptName(), batchTbl.getDataDate(),
						brInfoMap));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權)) {

				Map<String, String> brInfoMap = retrialService
						.get_lrs_rpt_brMap(batchTbl.getRemarks());
				batchTbl.setRptName(LrsUtil.lms180R19Name(
						batchTbl.getRptName(), batchTbl.getDataDate(),
						brInfoMap));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單)) {
				Map<String, String> brInfoMap = retrialService
						.get_lrs_rpt_brMap(batchTbl.getRemarks());
				batchTbl.setRptName(LrsUtil.lms180R20Name(
						batchTbl.getRptName(), brInfoMap));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表)) {

				Map<String, String> brInfoMap = retrialService
						.get_lrs_rpt_brMap(batchTbl.getRemarks());
				batchTbl.setRptName(LrsUtil.lms180R21Name(
						batchTbl.getRptName(), batchTbl.getBgnDate(),
						batchTbl.getEndDate(), brInfoMap));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表)) {
				Map<String, String> brInfoMap = retrialService
						.get_lrs_rpt_brMap(batchTbl.getRemarks());
				batchTbl.setRptName(LrsUtil.lms180R22Name(
						batchTbl.getRptName(), brInfoMap));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表)) {
				batchTbl.setRptName(LrsUtil.lms180R23Name(
						batchTbl.getRptName(), batchTbl.getBgnDate(),
						batchTbl.getEndDate()));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權)) {
				batchTbl.setRptName(LrsUtil.lms180R23Name(
						batchTbl.getRptName(), batchTbl.getBgnDate(),
						batchTbl.getEndDate()));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表)) {
				String rptName = batchTbl.getRptName();
				batchTbl.setRptName(LrsUtil.lms180R24Name(rptName,
						batchTbl.getBgnDate(), batchTbl.getEndDate()));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.覆審經理記點統計明細表)) {
				batchTbl.setRptName(LrsUtil.lms180R28Name(
						batchTbl.getRptName(), batchTbl.getBgnDate(),
						batchTbl.getEndDate()));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表)) {

				Map<String, String> remarkMap = CrsUtil.parseRptRemark(remark);

				String projName = Util.trim(remarkMap.get("projName"));

				batchTbl.setRptName(LrsUtil.lms180R61Name(
						batchTbl.getRptName(), projName));
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表)) {

				// Map<String, String> remarkMap =
				// CrsUtil.parseRptRemark(remark);

			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.全行地政士黑名單CSV)) {

			}
			this.save(batchTbl);
		} else {

			if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表)) {

				/**
				 * 要清掉REMARK報表參數的報表加在這邊 (報表TITLE 要變之情況下)
				 */

				Map<String, String> remarkMap = CrsUtil.parseRptRemark(remark);

				String projName = Util.trim(remarkMap.get("projName"));

				// 因為有可能REMARK會不同內容，但又不想要因此改變報表名稱，所以重塞一次參數
				// 授信異常通報案件報送統計表 參數有日期、分行、統編
				/**
				 * 此處也會更新dataDate
				 */
				// J-108-0116 共同行銷擔保品投保未結案明細表 - 更新dataDate
				batchTbl.setRemarks(remark);
				batchTbl.setBgnDate(bngDate);
				batchTbl.setEndDate(endDate);
				batchTbl.setNowRpt("Y");
				batchTbl.setDataDate(dataDate);
				batchTbl.setBthDate(CapDate.getCurrentTimestamp());
				batchTbl.setBranch(lmsbatch_branch);
				batchTbl.setMainId(mainId);
				batchTbl.setRptNo(rptNo);
				batchTbl.setRandomCode(IDGenerator.getUUID());
				batchTbl.setRptName(LrsUtil.lms180R61Name(
						Util.trim(rptNameMap.get(rptNo)), projName));
				this.save(batchTbl);

			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營業單位辦理對私募基金旗下投資事業敘做授信案件清單)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信簽案已核准未能簽約撥貸原因表)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.簽報階段都更危老業務統計表)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.共同行銷擔保品投保未結案明細表)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企業社會責任貸放情形統計表)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.中小企業創新發展專案貸款執行情形統計月報表)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.投資台灣三大方案專案貸款執行情形統計表)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.青年創業及啟動金貸款辦理情形總表)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表_不含小規)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授權外案件流程進度控管表)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授信簽報案件經區域營運中心流程進度控管表)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.中小企業千億振興融資方案)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金授信簽報案件經區域營運中心流程進度控管表)) {

				// J-113-0125_05097_B1001
				// 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
				/**
				 * 要清掉REMARK報表參數的報表加在這邊 (報表TITLE 不變之情況下)
				 */
				// J-105-0065-001 Web e-Loan 授信管理系統修改異常通報副知風控處說明文字及新增統計報表
				// 因為有可能REMARK會不同內容，但又不想要因此改變報表名稱，所以重塞一次參數
				// 授信異常通報案件報送統計表 參數有日期、分行、統編
				/**
				 * 此處也會更新dataDate
				 */
				// J-108-0116 共同行銷擔保品投保未結案明細表 - 更新dataDate

				// J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
				// J-111-0600_05097_B1001 Web
				// e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
				// J-111-0600_05097_B1002 Web
				// e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
				batchTbl.setRemarks(remark);
				batchTbl.setBgnDate(bngDate);
				batchTbl.setEndDate(endDate);
				batchTbl.setNowRpt("Y");
				batchTbl.setDataDate(dataDate);
				batchTbl.setBthDate(CapDate.getCurrentTimestamp());
				batchTbl.setBranch(lmsbatch_branch);
				batchTbl.setMainId(mainId);
				batchTbl.setRptNo(rptNo);
				batchTbl.setRandomCode(IDGenerator.getUUID());
				batchTbl.setRptName(Util.trim(rptNameMap.get(rptNo)));
				this.save(batchTbl);
			}

		}

		return batchTbl;
	}

	@Override
	public Boolean execBatchData(String mainId, boolean checkResult)
			throws Exception {
		boolean result = false;
		LMSBATCH batchtbl = null;
		try {
			batchtbl = lmsBatchDao.findByIndex03(mainId);
			if (batchtbl == null) {
				batchtbl = new LMSBATCH();
			}
			String rptNo = batchtbl.getRptNo();
			if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單)) {
				// 已敘做授信案件清單
				this.updateLMS180R01(batchtbl);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單)) {
				// 營運中心已敘做授信案件清單
				this.updateLMS180R02A(batchtbl);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單)) {
				// 信保案件未動用屆期清單
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表)) {
				// 授信契約已逾期控制表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表)) {
				// 授信契約產生主辦聯貸案一覽表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信案件統計表)) {
				// 授信案件統計表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.金融機構辦理振興經濟非中小企業專案貸款)) {
				// 金融機構辦理振興經濟非中小企業專案貸款
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企業自行申請展延案件案件統計表)) {
				// 企業自行申請展延案件
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營業單位授信報案考核彙總表)) {
				// 營業單位授信報案考核彙總表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營業單位授信報案考核彙總表_季報)) {
				// 營業單位授信報案考核彙總表_季報
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營運中心每日授權外授信案件清單)) {
				// 區域中心每日授權外授信案件清單
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.常董會報告事項彙總及申報案件數統計表)) {
				// 常董會報告事項彙總及申報案件數統計表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.本行各營業單位各級授權範圍內承做授信案件統計表)) {
				// 本行各營業單位各級授權範圍內承做授信案件統計表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.分行授信淨增加額度統計表)) {
				// 分行授信淨增加額度統計表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.振興經濟非中小企業專案貸款暨信用保證要點執行情形調查表)) {
				// 振興經濟非中小企業專案貸款暨信用保證要點執行情形調查表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報)) {
				// 授信業務異常通報月報
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表)) {
				// 授信異常通報案件報送統計表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營業單位辦理對私募基金旗下投資事業敘做授信案件清單)) {
				// 營業單位辦理對私募基金旗下投資事業敘做授信案件清單
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信簽案已核准未能簽約撥貸原因表)) {
				// 授信簽案已核准未能簽約撥貸原因表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.中小企業創新發展專案貸款執行情形統計月報表)) {
				// 中小企業創新發展專案貸款執行情形統計月報表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.金控總部分行逾期尚未辦理覆審名單_一次性)) {
				// 金控總部分行逾期尚未辦理覆審名單_一次性
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.尚未完成常董會企金戶實地覆審名單_一次性)) {
				// 尚未完成常董會企金戶實地覆審名單_一次性
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金聯貸新作案件統計表)) {
				// 企金聯貸新作案件統計表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授信案件敘做情形及比較表)) {
				// 企金授信案件敘做情形及比較表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單)) {
				// 已敘作消金案件清單
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表)) {
				// 敘做無自用住宅購屋放款明細表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報)) {
				// 授權內分行敘做房屋貸款月報
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表)) {
				// 授權內已敘做個人消費金融業務月報表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單)) {
				// 報案考核表被扣分清單
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.審件統計表)) {
				// 審件統計表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.分行對保費明細表)) {
				// 分行對保費明細表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.已收件待辦中案件報表)) {
				// 已收件待辦中案件報表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.婉拒案件明細表)) {
				// 婉拒案件明細表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金授信已逾期控制表)) {
				// 消金授信已逾期控制表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.已核准尚未撥款案件報表)) {
				// 已核准尚未撥款案件報表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表)) {
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表)) {
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表)) {
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.全行地政士黑名單)) {
				// 地政士黑名單
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.全行地政士黑名單CSV)) {
				// 地政士黑名單CSV
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金共同行銷報表)) {

			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.簽報階段都更危老業務統計表)) {

			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.新核准往來客戶及新增放款額度統計表)) {

			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.新核准往來客戶明細表)) {

			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企業社會責任貸放情形統計表)) {
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.對區域營運中心授信覆審作業之管理績效考核表)) {
				// 對區域營運中心授信覆審作業之管理績效考核表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.法令遵循自評授信案件明細報表)) {
				// 法令遵循自評授信案件明細報表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案辦理情形統計表)) {

			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案明細表)) {

			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案辦理情形總表)) {

			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案分行核准情形總表)) {

			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人授信異常通報表)) {

			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人兌付振興三倍券達888張名單)) {
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表)) {
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.案件屬110年行銷名單來源客戶簽案資料)) {
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表)) {
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表_不含小規)) {
				// 授信異常通報案件報送統計表_不含小規
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授權外案件流程進度控管表)) {
				// 企金授權外案件流程進度控管表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸案件明細表)) {
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.客戶訪談紀錄表)) {
				// 客戶訪談紀錄表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸ESG明細表)) {
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸KYC分案報表)) {
				// 歡喜信貸KYC分案報表
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.中小企業千億振興融資方案)) {
				// 中小企業千億振興融資方案
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸婉拒案件自動發送簡訊失敗顧客清單)) {
			}

			boolean updateResult = true;
			if (checkResult
					&& rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.常董會報告事項彙總及申報案件數統計表)) {
				// 常董會報告事項彙總及申報案件數統計表
				updateResult = false;
			}
			if (updateResult) {
				eloandbBaseService.updateNowTyperptTbl(
						Util.trim(batchtbl.getRptNo()),
						Util.trim(batchtbl.getBranch()));
			}
			eloandbBaseService.batchTblCopyToRptTbl(Util.trim(batchtbl
					.getMainId()));
			eloandbBaseService.delBatchTblNowRpts(Util.trim(batchtbl
					.getMainId()));// 刪除批次成功的batchTbl
			result = true;
		} finally {
		}
		return result;
	}

	@Override
	public Boolean createFileForAddBatch(LMSBATCH batch,
			Map<String, String> clsRptName, String tmpReMark) throws Exception {
		String rptNo = batch.getRptNo();
		boolean fileResult = false;
		if (batch != null) {
			boolean pdfResult = true;
			String oid = null;
			if (UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單.equals(batch
					.getRptNo())) {
				oid = lms9511r01Service.generateLMS180R01Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.本行各營業單位各級授權範圍內承做授信案件統計表
					.equals(batch.getRptNo())) {
				oid = lms9511r01Service.generateLMS180R17Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.營運中心每日授權外授信案件清單
					.equals(batch.getRptNo())) {
				oid = lms9511r01Service.generateLMS180R15Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.營業單位授信報案考核彙總表
					.equals(batch.getRptNo())) {
				// rptType M:月報 Q:季報
				oid = lms9511r01XlsService.generateLMS180R14Report(
						batch.getMainId(), "M");
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.營業單位授信報案考核彙總表_季報
					.equals(batch.getRptNo())) {
				// J-108-0192_05097_B1001 Web e-Loan企金授信新增每季海外營業單位授信報案考核彙總表
				// rptType M:月報 Q:季報
				oid = lms9511r01XlsService.generateLMS180R14Report(
						batch.getMainId(), "Q");
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表.equals(batch
					.getRptNo())) {
				oid = lms9511r01Service.generateLMS180R05Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單
					.equals(batch.getRptNo())) {
				oid = lms9511r01Service.generateLMS180R10Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.授信案件統計表.equals(batch
					.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R11Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS161T02Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.金融機構辦理振興經濟非中小企業專案貸款
					.equalsIgnoreCase(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R12Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企業自行申請展延案件案件統計表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R13Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單.equals(batch
					.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R19Report(batch);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R19BReport(batch);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R20Report(batch);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R21Report(batch);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表.equals(batch
					.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R22Report(batch);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表.equals(batch
					.getRptNo())) {
				oid = lms9511r01Service.generateLMS180R23Report(batch);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權
					.equals(batch.getRptNo())) {
				oid = lms9511r01Service.generateLMS180R23BReport(batch);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R24Report(batch);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.覆審經理記點統計明細表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R28Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表)) {
				// 疑似代辦案件報表
				oid = this.buildCLS250R01Xls(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表)) {
				oid = this.buildCLS250R01Xls(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表)) {
				oid = this.buildCLS250R01Xls(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單)) {
				// 留存同一通訊處
				oid = this.buildCLS180R21Xls(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單)) {
				// 留存同一通訊處
				oid = this.buildCLS180R22Xls(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單)) {
				// 已敘作消金案件清單
				oid = this.buildCLS180R01Xls(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表)) {
				// 敘做無自用住宅購屋放款明細表
				String[] cols = { "NUM", "CUSTID", "CUSTNAME", "JOBTITLE",
						"APPROVEDAMT", "RATEDESC", "LNSTARTDATE", "LNENDDATE",
						"RTYPE", "RNAME", "RJOBTITLE", "PROPERTY" };

				oid = this.createExcel(clsRptName.get(batch.getRptNo()),
						this.findCLS180R02Data(batch), batch, 6, "APPROVEDAMT",
						cols, 1000, 80, true);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報)) {
				// 授權內分行敘做房屋貸款月報
				String[] cols = { "NUM", "CUSTID", "CUSTNAME", "COMNAME",
						"JOBTITLE", "APPROVEDAMT", "TLOANTWD", "INAMT",
						CLS180R03R04_SUM_CTRTXAMT, "TTAXADDR", "RATEDESC",
						"LNSTARTDATE", "LNENDDATE", CLS180R03_NOWFROM_NOWEND,
						"RTYPE", "RNAME", "RCOMNAME", "RJOBTITLE",
						"LOANTOTAMT", "AMTCODE", "PROPERTY", "CNTRNO",
						"GRADE1", CLS180R03_ADJUST_GRADE, "ADJUSTREASON" };
				boolean by_brNo = true;
				oid = this.createExcel(clsRptName.get(batch.getRptNo()),
						this.findCLS180R03Data(batch, by_brNo), batch, 6,
						"APPROVEDAMT", cols, 1000, 50, true);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報_稽核處)) {
				// 授權內分行敘做房屋貸款月報_稽核處
				String[] cols = { "NUM",
						"CUSTID",
						"CUSTNAME",
						"COMNAME",
						"JOBTITLE",
						"APPROVEDAMT",
						"TLOANTWD",
						"INAMT",
						// J-111-0438,20240408增加疑似高價住宅
						"SUS_HP_HOUSE",
						// 20220408何仁正(稽核處,副稽核)TEAMS訊息要求增加估值/區域平均參考值
						"INAMT_EMAPESTAMT_RATE", CLS180R03R04_SUM_CTRTXAMT,
						"TTAXADDR",
						"RATEDESC",
						"LNSTARTDATE",
						"LNENDDATE",
						CLS180R03_NOWFROM_NOWEND,
						"LOANTOTAMT",
						"AMTCODE",
						"PROPERTY",
						"CNTRNO",
						"GRADE1",
						CLS180R03_ADJUST_GRADE,
						"ADJUSTREASON",
						// J-110-0414增列「借款人年齡」及「借款人年齡加計貸款年限後之年齡」借款人年所得INCOME
						"AGE",
						"LNENDDATEAGE",
						"INCOME",
						// J-110-0414增列保證人欄位
						// 20220310
						// 王錦文(稽核處,副稽核)TEAMS訊息增加保證人年齡和保證人年齡加計貸款年限後之年齡之欄位
						"RTYPE0", "RNAME0", "RCOMNAME0", "RJOBTITLE0",
						"RINCOME0", "RAGE0", "RLNENDDATEAGE0", "RTYPE1",
						"RNAME1", "RCOMNAME1", "RJOBTITLE1", "RINCOME1",
						"RAGE1", "RLNENDDATEAGE1",
						//J-113-0299 增加「個人負債比率」、「夫妻負債比率」
						"DRATE", "YRATE"};
				boolean by_brNo = false;
				oid = this.createCSV_CLS180R03B(
						this.findCLS180R03Data(batch, by_brNo), batch, 6,
						"APPROVEDAMT", cols);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表)) {
				// 授權內已敘做個人消費金融業務月報表
				String[] cols = { "NUM", "PRODNM", "CUSTID", "CUSTNAME",
						"COMNAME", "JOBTITLE", "APPROVEDAMT", "PROPERTY",
						"RATEDESC", "LNSTARTDATE", "LNENDDATE", "COLLTYP1",
						CLS180R04_TOTALLOANAMT, CLS180R03R04_SUM_CTRTXAMT,
						"RTYPE", "RNAME", "RCOMNAME", "RJOBTITLE", "LOANTOTAMT" };
				oid = this.createExcel(clsRptName.get(batch.getRptNo()),
						this.findCLS180R04Data(batch), batch, 6, "APPROVEDAMT",
						cols, 1000, 60, true);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表_稽核處)) {
				// 授權內已敘做個人消費金融業務月報表
				String[] cols = {
						"NUM",
						"PRODNM",
						"CUSTID",
						"CUSTNAME",
						"COMNAME",
						"JOBTITLE",
						// J-110-0413
						// 增列「借款人年齡AGE」及「借款人年齡加計貸款年限後之年齡LNENDDATEAGE」借款人年所得INCOME
						"AGE", "LNENDDATEAGE", "INCOME", "APPROVEDAMT",
						"PROPERTY", "RATEDESC", "LNSTARTDATE", "LNENDDATE",
						"COLLTYP1", CLS180R04_TOTALLOANAMT,
						CLS180R03R04_SUM_CTRTXAMT, "RTYPE0", "RNAME0",
						"RCOMNAME0",
						"RJOBTITLE0",
						// J-110-0413 增列 保證人年所得RINCOME」
						"RINCOME0",
						// 20220310
						// 王錦文(稽核處,副稽核)TEAMS訊息增加保證人年齡和保證人年齡加計貸款年限後之年齡之欄位
						"RAGE0", "RLNENDDATEAGE0", "RTYPE1", "RNAME1",
						"RCOMNAME1", "RJOBTITLE1",
						// J-110-0413 增列 保證人年所得RINCOME」
						"RINCOME1",
						// 20220310
						// 王錦文(稽核處,副稽核)TEAMS訊息增加保證人年齡和保證人年齡加計貸款年限後之年齡之欄位
						"RAGE1", "RLNENDDATEAGE1", "LOANTOTAMT",
						// J-110-0413 增列
						// 模型初始評等GRADE1」、「調整評等GRADE2」、「調整評等理由ADJUSTREASON
						"GRADE1", "GRADE2", "ADJUSTREASON",
						// 20220309 王錦文(稽核處,副稽核)TEAMS訊息要求增加額度序號 不動產擔保品估值
						"cntrNo", "INAMT",
						// 20220408何仁正(稽核處,副稽核)TEAMS訊息要求增加估值/區域平均參考值
						"INAMT_EMAPESTAMT_RATE",
						// J-113-0299 增加「個人負債比率」、「夫妻負債比率」
						"DRATE", "YRATE"
						};
				oid = this.createCSV_CLS180R04B(this.findCLS180R04Data(batch),
						batch, 6, "APPROVEDAMT", cols);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單)) {
				// 報案考核表被扣分清單
				oid = this.createCLS180R05PDF(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.審件統計表)) {
				// 審件統計表-用mainId對應
				this.createCLS180R06PDF(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.婉拒案件明細表)) {
				// 婉拒案件明細表
				String[] cols = { "NUM", "APPROVETIME", "CASENO", "CUSTNAME",
						"CUSTID", "LOANTOTAMT", "CESRJTCAUSE", "APPROVER" };
				oid = this.createExcel(clsRptName.get(batch.getRptNo()),
						this.findCLS180R09Data(batch), batch, 5, "loanTotAmt",
						cols, 1000, 61, false);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金授信已逾期控制表)) {
				// 消金授信已逾期控制表
				String[] cols = { "NUM", "CUSTID", "CNTRNO", "CURR", "FACTAMT",
						"BDATE", "EDATE" };
				oid = this.createExcel(clsRptName.get(batch.getRptNo()),
						this.findCLS180R10Data(batch), batch, 5, "loanTotAmt",
						cols, 1, 90, false);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.已核准尚未撥款案件報表)) {
				// 已核准尚未撥款案件報表-用mainId對應!
				oid = this.buildCLS180R11Data(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表)) {
				oid = lms9511r01XlsService.generateCLS180R12Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表)) {
				oid = lms9511r01XlsService.generateCLS180R13Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月)) {
				oid = lms9511r01XlsService.generateCLS180R14AReport(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂)) {
				oid = lms9511r01XlsService.generateCLS180R14BReport(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起)) {
				oid = lms9511r01XlsService.generateCLS180R14CReport(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限)) {
				oid = lms9511r01XlsService.generateCLS180R14DReport(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表)) {
				oid = lms9511r01XlsService.generateCLS180R15Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表)) {
				oid = lms9511r01XlsService.generateCLS180R15BReport(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表)) {
				oid = lms9511r01XlsService.generateCLS180R15CReport(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表)) {
				oid = lms9511r01XlsService.generateCLS180R15DReport(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表)) {
				oid = lms9511r01XlsService.generateCLS180R15EReport(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表)) {
				// J-112-0465 新增「○○區營運中心覆審類別「額度一千萬元以下十足擔保信保七成循環動用」抽樣之授信戶明細表
				oid = lms9511r01XlsService.generateCLS180R15FReport(batch,
						tmpReMark);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表)) {
				oid = lms9511r01XlsService.generateCLS180R16Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金處新做案件_當月_當年度累計_統計表)) {
				oid = lms9511r01Service.generateCLS180R17Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審名單)) {
				oid = lms9511r01XlsService.generateCLS180R18Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審件數控管表)) {
				oid = lms9511r01XlsService.generateCLS180R18BReport(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金整批房貸消貸敘做明細)) {
				oid = lms9511r01XlsService.generateCLS180R19Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金線上貸款餘額表)) {
				oid = lms9511r01XlsService.generateCLS180R20Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金自住型房貸成長方案統計報表)) {
				oid = lms9511r01XlsService.generateCLS180R23Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.台電員工貸款明細表)) {
				oid = lms9511r01XlsService.generateCLS180R30Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金線上信貸申貸清單)) {
				oid = lms9511r01XlsService.generateCLS180R25Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.消金線上房貸申貸清單)) {
				oid = lms9511r01XlsService.generateCLS180R26Report(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸案件明細表)) {
				oid = lms9511r01XlsService.generateCLS180R27Report(batch);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.分行授信淨增加額度統計表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R18Report(batch
						.getMainId());
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸ESG明細表)) {
				oid = lms9511r01XlsService.generateCLS180R27BReport(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸KYC分案報表)) {
				oid = lms9511r01XlsService.generateCLS180R27CReport(batch);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸婉拒案件自動發送簡訊失敗顧客清單)) {
				oid = lms9511r01XlsService.generateCLS180R27DReport(batch);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.振興經濟非中小企業專案貸款暨信用保證要點執行情形調查表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R26Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報.equals(batch
					.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R25Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表
					.equals(batch.getRptNo())) {
				// J-110-0084_05097_B1001 Web
				// e-Loan企金授信新增「授信異常通報案件報送統計表(不含小規模營業人貸款)」
				oid = lms9511r01XlsService.generateLMS180R27Report(
						batch.getMainId(), true);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.營業單位辦理對私募基金旗下投資事業敘做授信案件清單
					.equals(batch.getRptNo())) {
				// J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
				oid = lms9511r01XlsService.generateLMS180R29Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.授信簽案已核准未能簽約撥貸原因表
					.equals(batch.getRptNo())) {
				// J-105-0214-001 Web e-Loan 管理報表新增授信簽案已核准未能簽約撥貸原因表。
				Map<String, String> brInfoMap = retrialService
						.get_lrs_rpt_brMap(batch.getRemarks());
				oid = lms9511r01XlsService.generateLMS180R30Report(
						batch.getMainId(), brInfoMap);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表
					.equals(batch.getRptNo())) {
				// J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
				oid = lms9511r01XlsService.generateLMS180R31Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.營運中心轄下分行往來客戶有全行通報異常情形彙總表
					.equals(batch.getRptNo())) {
				// J-105-0321-001 Web e-Loan授信管理系統增加營運中心轄下分行往來客戶有全行通報異常情形彙總表
				oid = lms9511r01XlsService.generateLMS180R32Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.中小企業創新發展專案貸款執行情形統計月報表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R33Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.金控總部分行逾期尚未辦理覆審名單_一次性
					.equals(batch.getRptNo())) {
				// LMS180R34 金控總部分行逾期尚未辦理覆審名單_一次性
				oid = lms9511r01XlsService.generateLMS180R34Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.尚未完成常董會企金戶實地覆審名單_一次性
					.equals(batch.getRptNo())) {
				// LMS180R35 尚未完成常董會企金戶實地覆審名單_一次性
				oid = lms9511r01XlsService.generateLMS180R35Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金聯貸新作案件統計表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R36Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金授信案件敘做情形及比較表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R37Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金已核准授信額度辦理狀態通報彙總表
					.equals(batch.getRptNo())) {
				// J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
				oid = lms9511r01XlsService.generateLMS180R38Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金共同行銷報表.equals(batch
					.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R39Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.簽報階段都更危老業務統計表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R40Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.協助工業區及產業園區建廠優惠貸款專案執行情形統計月報表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R41Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.新核准往來客戶及新增放款額度統計表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R42Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.新核准往來客戶明細表.equals(batch
					.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R42TReport(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.國內分行每季新做無擔保中小企業戶授信額度明細表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R43Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.國內分行每季新作副總權限以上授信額度累計金額
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R44Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.國內分行新核准往來企金客戶數統計表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R45Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE3.董事會權限核定授信案件報表
					.equals(batch.getRptNo())) {
				lms9511r01XlsService
						.generateLMS9515R10Report(batch.getMainId());
				pdfResult = false;
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.共同行銷擔保品投保未結案明細表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R46Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企業社會責任貸放情形統計表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R47Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.對區域營運中心授信覆審作業之管理績效考核表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R48Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.國內分行新核准往來企金客戶數統計表含舊戶
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R49Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.投資台灣三大方案專案貸款執行情形統計表
					.equals(batch.getRptNo())) {
				LMSRPT lmsRpt = lmsRptDao.findByIndex03(batch.getMainId());
				String exportType = Util.trim(lmsRpt.getRemarks());
				if (Util.equals(exportType, "PDF")) {
					oid = lms9511r01Service.tranSportLMS180R50Pdf(batch
							.getMainId());
				} else {
					oid = lms9511r01XlsService.generateLMS180R50Report(batch
							.getMainId());
				}
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.愛企貸專案統計表.equals(batch
					.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R51Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表
					.equals(batch.getRptNo())) {
				// LMS180R52
				oid = lms9511r01XlsService.generateLMS180R52Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.法令遵循自評授信案件明細報表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R53Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE2.勞工紓困貸款彙總表.equals(batch
					.getRptNo())) {
				// CLS180R40
				oid = lms9511r01XlsService.generateCLS180R40Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE2.勞工紓困貸款明細表.equals(batch
					.getRptNo())) {
				// CLS180R41
				oid = lms9511r01XlsService.generateCLS180R41Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE2.特定金錢信託案件量統計報表
					.equals(batch.getRptNo())) {
				// CLS180R42
				oid = lms9511r01XlsService.generateCLS180R42Report(
						batch.getMainId(), batch.getBranch());
			} else if (UtilConstants.RPTREPORT.DOCTYPE2.六個月內到期土建融案件月報表
					.equals(batch.getRptNo())) {

				// CLS180R50
				oid = lms9511r01XlsService
						.generateCLS180R50ForBatchMortgageReport(batch
								.getMainId());

			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.全行地政士黑名單)) {
				// 全行地政士黑名單
				String[] cols = { "地政士姓名", "分行別", "證書年份", "證書字號", "證書字號",
						"ESTATEAGENTFLAG", "RECORD17FLAG", "控管原因", "狀態",
						"核准人員", "核准日期", "備註", "建立日期" };
				oid = lms9511r01XlsService
						.generateCLS180R52ForBatchMortgageReport(batch
								.getMainId());
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.全行地政士黑名單CSV)) {
				// 全行地政士黑名單CSV
				// String[] cols = {
				// "CUSTNAME","CUSTNAME","AGENTCERTYEAR","AGENTCERTWORD","AGENTCERTNO"
				// ,"ESTATEAGENTFLAG","RECORD17FLAG","MEMO","CTLFLAG","CODEDESC2","CODEDESC","CODEDESC3"};
				String[] cols = { "地政士姓名", "分行別", "證書字號", "房仲註記", "懲戒紀錄17條註記",
						"控管原因", "狀態", "備註", "核准人員", "核准日期", "建立人員", "建立日期" };
				oid = this.createCSV_CLS180R52(batch, cols);

			} else if (UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案辦理情形統計表
					.equals(batch.getRptNo())) {

				oid = lms9511r01XlsService.generateLMS180R54Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案明細表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R54TReport(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案辦理情形總表
					.equals(batch.getRptNo())) {

				oid = lms9511r01XlsService.generateLMS180R55Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案分行核准情形總表
					.equals(batch.getRptNo())) {

				oid = lms9511r01XlsService.generateLMS180R56Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人授信異常通報表
					.equals(batch.getRptNo())) {

				oid = lms9511r01XlsService.generateLMS180R58Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.青年創業及啟動金貸款辦理情形總表
					.equals(batch.getRptNo())) {
				// J-109-0362 青年創業及啟動金貸款辦理情形總表 LMS180R59
				oid = lms9511r01XlsService.generateLMS180R59Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.建案完成未出售房屋融資統計表
					.equals(batch.getRptNo())) {

				oid = lms9511r01XlsService
						.generateLMS1401R03ReportForUnsoldHouseFinancingDataReport(batch
								.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人兌付振興三倍券達888張名單
					.equals(batch.getRptNo())) {
				// LMS180R60 小規模營業人兌付振興三倍券達888張名單
				oid = lms9511r01XlsService.generateLMS180R60Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表.equals(batch
					.getRptNo())) {
				// LMS180R61 額度專案種類明細表
				oid = lms9511r01XlsService.generateLMS180R61Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.案件屬110年行銷名單來源客戶簽案資料
					.equals(batch.getRptNo())) {
				// LMS180R62 案件屬110年行銷名單來源客戶簽案資料
				oid = lms9511r01XlsService.generateLMS180R62Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表
					.equals(batch.getRptNo())) {
				// LMS180R63 境內法人於國際金融業務分行辦理外幣授信業務報表
				oid = lms9511r01XlsService.generateLMS180R63Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表_不含小規
					.equals(batch.getRptNo())) {
				// J-110-0084_05097_B1001 Web
				// e-Loan企金授信新增「授信異常通報案件報送統計表(不含小規模營業人貸款)」
				oid = lms9511r01XlsService.generateLMS180R27Report(
						batch.getMainId(), false);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金授權外案件流程進度控管表
					.equals(batch.getRptNo())) {
				// J-109-0479_05097_B1004 Web
				// e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
				oid = lms9511r01XlsService.generateLMS180R65Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金授信簽報案件經區域營運中心流程進度控管表
					.equals(batch.getRptNo())) {
				// J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率,
				// 增加企/消金授信簽報案件經區域營運中心流程進度控管表
				oid = lms9511r01XlsService.generateLMS180R68Report(
						batch.getMainId(), UtilConstants.Casedoc.DocType.企金);
			} else if (UtilConstants.RPTREPORT.DOCTYPE2.消金授信簽報案件經區域營運中心流程進度控管表
					.equals(batch.getRptNo())) {
				// J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率,
				// 增加企/消金授信簽報案件經區域營運中心流程進度控管表
				oid = lms9511r01XlsService.generateLMS180R68Report(
						batch.getMainId(), UtilConstants.Casedoc.DocType.個金);
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.土建融案資料表.equals(batch
					.getRptNo())) {
				// J-109-0102_05097_B1001 Web e-Loan
				// 配合業管處爭覽管理委員會存款，提供e-Loan土建融案資料
				oid = lms9511r01XlsService.generateLMS180R66Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE2.整批房貸統計表.equals(batch
					.getRptNo())) {

				oid = lms9511r01XlsService
						.generateCLS180R51ForBatchMortgageStatisticsReport(batch
								.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE2.整批貸款明細表.equals(batch
					.getRptNo())) {
				// J-110-0211_11557_B1002
				// 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
				oid = lms9511r01XlsService.generateCLS180R53Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.國內營業單位海外信保基金基金案件表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R67Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金綠色授信暨ESG簽報案件明細表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R69Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.待售房屋去化落後追蹤表
					.equals(batch.getRptNo())) {

				oid = lms9511r01XlsService
						.generateCLS180R55ForUnsoldHouseTrackReport(batch
								.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE2.中期循環年度檢視表.equals(batch
					.getRptNo())) {

				oid = lms9511r01XlsService
						.generateCLS180R57ForMidCycleAnnualReviewList(batch
								.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.免計入銀行法72_2限額控管之廠房貸款案件追蹤表
					.equals(batch.getRptNo())) {

				oid = lms9511r01XlsService
						.generateCLS180R70ForPlantLoanTrackingReportOfNotIn722Limitation(batch
								.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表
					.equals(batch.getRptNo())) {

				oid = lms9511r01XlsService.generateLMS180R72Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.不動產授信例外管理報表
					.equals(batch.getRptNo())) {

				oid = lms9511r01XlsService.generateLMS180R73Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE2.客戶訪談紀錄表.equals(batch
					.getRptNo())) {

				oid = lms9511r01XlsService.generateCLS180R58Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE2.國內消金貸款地政士引介房貸案發生逾放比例統計表
					.equals(batch.getRptNo())) {

				oid = lms9511r01XlsService
						.generateCLS180R59ForMortgageCaseRecommendedByLandsmanReport(batch
								.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.中小企業千億振興融資方案
					.equals(batch.getRptNo())) {

				oid = lms9511r01XlsService.generateLMS180R74Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.企金授信簽報案件明細檔
					.equals(batch.getRptNo())) {
				// J-112-0342 新增產生企金授信簽報案件明細檔
				oid = lms9511r01XlsService.generateLMS180R75Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE2.分行承作購置住宅貸款年限逾30年統計表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateCLS180R60Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.授信案件曾涉及ESG風險因而有條件通過或未核准之情形表
					.equals(batch.getRptNo())) {
				oid = lms9511r01XlsService.generateLMS180R76Report(batch
						.getMainId());
			} else if (UtilConstants.RPTREPORT.DOCTYPE2.房貸案件明細表.equals(batch
					.getRptNo())) {
				oid = lms9511r01XlsService.generateCLS180R61Report(batch
						.getMainId());
            } else if (UtilConstants.RPTREPORT.DOCTYPE1.青創追蹤報表.equals(batch
                    .getRptNo())) {
                // J-113-0237 配合消金處，於ELOAN端消金業務處及企金業務處新增青創追蹤報表
                oid = lms9511r01XlsService.generateLMS180R77Report(batch
                        .getMainId());
            } else if (UtilConstants.RPTREPORT.DOCTYPE2.青創追蹤報表.equals(batch
                    .getRptNo())) {
                // J-113-0237 配合消金處，於ELOAN端消金業務處及企金業務處新增青創追蹤報表
                oid = lms9511r01XlsService.generateCLS180R62Report(batch
                        .getMainId());
			}

			if (pdfResult) {
				LMSRPT lmsRpt = this.findModelByOid(LMSRPT.class,
						batch.getOid());
				lmsRpt.setReportOidFile(oid);
				this.save(lmsRpt);
			}
		}

		fileResult = true;
		return fileResult;
	}

	@Override
	public void updateLMS180R01(LMSBATCH batchtbl) {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		Map<String, String> subItemeMap = null;
		Map<String, String> subCodeMap = null;
		subCodeMap = prodService.getSubCode();
		subItemeMap = codetypeService.findByCodeType("lms1405m01_SubItem");
		if (subItemeMap == null) {
			subItemeMap = new LinkedHashMap<String, String>();
		}
		if (subCodeMap == null) {
			subCodeMap = new LinkedHashMap<String, String>();
		}
		List<Map<String, Object>> rows = eloandbBaseService
				.findforNewReportType2ByBrNo(batchtbl.getBranch(),
						CreditDocStatusEnum.海外_已核准.getCode(),
						UtilConstants.Casedoc.DocKind.授權內,
						TWNDate.toAD(batchtbl.getBgnDate()),
						TWNDate.toAD(batchtbl.getEndDate()),
						FlowDocStatusEnum.已核准.getCode(),
						UtilConstants.Casedoc.typCd.DBU,
						UtilConstants.Casedoc.typCd.OBU);
		List<L784S01A> l784s01List = new ArrayList<L784S01A>();
		int count = 0;
		for (Map<String, Object> dataMap : rows) {
			L784S01A l784s01a = new L784S01A();
			String docType = Util.trim(dataMap.get("DOCTYPE"));
			String brId = Util.trim(dataMap.get("CASEBRID"));
			String custId = Util.trim(dataMap.get("CUSTID"));
			String dupNo = Util.trim(dataMap.get("DUPNO"));
			String cntrNo = Util.trim(dataMap.get("CNTRNO"));
			String custName = Util.trim(dataMap.get("CUSTNAME"));
			String lnSubject = Util.trim(subItemeMap.get(Util.trim(dataMap
					.get("LNSUBJECT"))))
					+ (Integer
							.parseInt(Util.trim(dataMap.get("LNSUBJECTCOUNT"))) > 1 ? prop
							.getProperty("L784M01a.less") : "");
			String lnSubject2 = Util.trim(subCodeMap.get(Util.trim(dataMap
					.get("LNSUBJECT2"))))
					+ (Integer.parseInt(Util.trim(dataMap
							.get("LNSUBJECTCOUNT2"))) > 1 ? prop
							.getProperty("L784M01a.less") : "");
			String currentApplyCurr = Util
					.trim(dataMap.get("CURRENTAPPLYCURR"));
			BigDecimal currentApplyAmt = LMSUtil.toBigDecimal(dataMap
					.get("CURRENTAPPLYAMT"));
			String useDeadline = Util.trim(dataMap.get("USEDEADLINE"));
			String desp1 = Util.trim(dataMap.get("DESP1"));
			String property = Util.trim(dataMap.get("PROPERTY"));
			String randomCode = Util.trim(dataMap.get("RANDOMCODE"));
			Date endDate = (Date) dataMap.get("ENDDATE");
			String staffNo = Util.trim(dataMap.get("STAFFNO"));

			String L120M01A_MainId = Util.trim(dataMap.get("MAINID"));
			String L140M01A_MainId = Util.trim(dataMap.get("REFMAINID"));

			l784s01a.setBrId(brId);
			l784s01a.setCustId(custId);
			l784s01a.setDupNo(dupNo);
			l784s01a.setMainId(batchtbl.getMainId());
			l784s01a.setCntrNo(cntrNo);
			l784s01a.setCustName(custName);
			if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {
				l784s01a.setLnSubject(lnSubject);
			} else {
				l784s01a.setLnSubject(lnSubject2);
			}
			l784s01a.setCurrentApplyCurr(currentApplyCurr);
			l784s01a.setCurrentApplyAmt(currentApplyAmt);
			l784s01a.setUseDeadline(useDeadline);
			l784s01a.setDesp1(desp1);
			l784s01a.setProperty(property);
			l784s01a.setRandomCode(randomCode);
			l784s01a.setEndDate(endDate);
			l784s01a.setStaffNo(staffNo);
			l784s01a.setL120M01A_MainId(L120M01A_MainId);
			l784s01a.setL140M01A_MainId(L140M01A_MainId);
			l784s01List.add(l784s01a);
			count++;
		}
		// 一次儲存多筆
		l784s01aDao.save(l784s01List);
	}

	@Override
	public void updateLMS180R02A(LMSBATCH batchtbl) {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		Map<String, String> subItemeMap = null;
		Map<String, String> subCodeMap = null;
		subCodeMap = prodService.getSubCode();
		subItemeMap = codetypeService.findByCodeType("lms1405m01_SubItem");
		if (subItemeMap == null) {
			subItemeMap = new LinkedHashMap<String, String>();
		}
		if (subCodeMap == null) {
			subCodeMap = new LinkedHashMap<String, String>();
		}
		List<Map<String, Object>> rows = eloandbBaseService
				.queryLMS180R02AData(TWNDate.toAD(batchtbl.getBgnDate()),
						TWNDate.toAD(batchtbl.getEndDate()),
						batchtbl.getBranch());
		// Map<String, Object> LMS918Map =
		// eloandbBaseService.getL180R02A918LMSRPT(TWNDate.toAD(batchtbl.getBgnDate()),
		// TWNDate.toAD(batchtbl.getEndDate()));
		List<L180R02A> l180r02aList = new ArrayList<L180R02A>();
		L180R02A l180r02a = null;
		String userId = batchtbl.getUpdater();
		// String mainId918 = "";
		// if(LMS918Map != null){
		// mainId918 = Util.trim(LMS918Map.get("MAINID"));
		// }
		for (Map<String, Object> map : rows) {
			String docType = Util.trim(map.get("DOCTYPE"));
			String property = Util.trim(map.get("PROPERTY"));
			String docStatus = Util.trim(map.get("DOCSTATUS"));
			String lnSubject = Util.trim(subItemeMap.get(Util.trim(map
					.get("LNSUBJECT"))))
					+ (Integer.parseInt(Util.trim(map.get("LNSUBJECTCOUNT"))) > 1 ? prop
							.getProperty("L784M01a.less") : "");
			String lnSubject2 = Util.trim(subCodeMap.get(Util.trim(map
					.get("LNSUBJECT2"))))
					+ (Integer.parseInt(Util.trim(map.get("LNSUBJECTCOUNT2"))) > 1 ? prop
							.getProperty("L784M01a.less") : "");
			boolean noUseResult = false;
			boolean speNoUseResult = false;
			String currentapplyCurr = Util.trim(map.get("CURRENTAPPLYCURR"));
			BigDecimal currentapplyAmt = LMSUtil.toBigDecimal(map
					.get("CURRENTAPPLYAMT")) == null ? BigDecimal.ZERO
					: LMSUtil.toBigDecimal(map.get("CURRENTAPPLYAMT"));
			String LV2Curr = "".equals(Util.trim(map.get("LV2CURR"))) ? currentapplyCurr
					: Util.trim(map.get("LV2CURR"));
			BigDecimal LV2Amt = LMSUtil.toBigDecimal(map.get("LV2AMT")) == null ? BigDecimal.ZERO
					: LMSUtil.toBigDecimal(map.get("LV2AMT"));
			BigDecimal asAmt = BigDecimal.ZERO;
			// 取出不需要計算是否新增刪除的資料
			// 企金
			if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {
				if ((!"".equals(Util.trim(map.get("COMMSNO"))) && !Util.trim(
						map.get("COMMSNO"))
						.equals(Util.trim(map.get("CNTRNO"))))) {
					noUseResult = true;
				}
			} else {
				if (!"0".equals(Util.trim(map.get("CHKCOUNT")))) {
					noUseResult = true;
				}
			}

			if (!currentapplyCurr.equals(LV2Curr)) {
				BranchRate rate = lmsService.getBranchRate(Util.trim(map
						.get("BRANCHID")));
				LV2Amt = rate.toOtherAmt(LV2Curr, currentapplyCurr, LV2Amt);
				LV2Curr = currentapplyCurr;
			}

			if (("|" + property + "|").indexOf("|"
					+ UtilConstants.Cntrdoc.Property.不變 + "|") != -1
					|| docStatus.equals(FlowDocStatusEnum.婉卻.getCode())) {
				speNoUseResult = true;
			} else if (noUseResult) {
				asAmt = LMSUtil.subTotal(currentapplyAmt, LV2Amt).abs();
			} else {
				asAmt = LMSUtil.subTotal(currentapplyAmt, LV2Amt).abs();
			}

			String L120M01A_MainId = Util.trim(map.get("MAINID"));
			String L140M01A_MainId = Util.trim(map.get("REFMAINID"));

			l180r02a = new L180R02A();
			l180r02a.setMainId(batchtbl.getMainId());
			// l180r02a.setMainId918(mainId918);
			l180r02a.setCustId(Util.trim(map.get("CUSTID")));
			l180r02a.setDupNo(Util.trim(map.get("DUPNO")));
			l180r02a.setCustName(Util.trim(map.get("CUSTNAME")));
			l180r02a.setBrno(Util.trim(map.get("BRANCHID")));
			l180r02a.setCntrNo(Util.trim(map.get("CNTRNO")));
			l180r02a.setCaseDate((Date) map.get("CASEDATE"));
			l180r02a.setApprover(Util.trim(map.get("USERNAME")));
			l180r02a.setDocType(docType);
			l180r02a.setDocKind(Util.trim(map.get("DOCKIND")));
			l180r02a.setCaseNo(Util.trim(map.get("CASENO")));
			if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {
				l180r02a.setLnSubject(lnSubject);
			} else {
				l180r02a.setLnSubject(lnSubject2);
			}
			l180r02a.setProperty(Util.trim(map.get("PROPERTY")));
			l180r02a.setLV2Curr(currentapplyCurr);
			l180r02a.setLV2Amt(currentapplyAmt);
			if (speNoUseResult) {
				l180r02a.setNoUseAmt("A");
			} else if (!noUseResult) {
				if (currentapplyAmt.compareTo(LV2Amt) > 0) {
					l180r02a.setNoUseAmt("A");
				} else if (currentapplyAmt.compareTo(LV2Amt) < 0) {
					l180r02a.setNoUseAmt("D");
				} else {
					l180r02a.setNoUseAmt("A");
				}
			} else {
				if (currentapplyAmt.compareTo(LV2Amt) > 0) {
					l180r02a.setNoUseAmt("X");
				} else if (currentapplyAmt.compareTo(LV2Amt) < 0) {
					l180r02a.setNoUseAmt("Y");
				} else {
					l180r02a.setNoUseAmt("X");
				}
			}
			l180r02a.setAsCurr(currentapplyCurr);
			l180r02a.setAsAmt(asAmt);
			l180r02a.setDesp1(LMSUtil.getUseDeadline(Util.nullToSpace(Util
					.trim(map.get("USEDEADLINE"))), Util.nullToSpace(Util
					.trim(map.get("DESP1"))), MessageBundleScriptCreator
					.getComponentResource(LMS1405S02Panel.class)));
			l180r02a.setCreator(userId);
			l180r02a.setCreateTime(CapDate.getCurrentTimestamp());
			l180r02a.setUpdater(userId);
			l180r02a.setUpdateTime(CapDate.getCurrentTimestamp());
			l180r02a.setL120M01A_MainId(L120M01A_MainId);
			l180r02a.setL140M01A_MainId(L140M01A_MainId);
			l180r02aList.add(l180r02a);
		}
		l180r02aDao.save(l180r02aList);
	}

	@Override
	public List<Map<String, Object>> findType5ByBrNoAndDate(
			List<IBranch> ovUnitNo, String benDate, String endDate,
			String otherCondition) throws CapException {
		List<Map<String, Object>> rows = misElf447Service
				.findElf447forNewReportType5ByBrNo(ovUnitNo, benDate, endDate,
						otherCondition);
		// 單位別 ELF447_CHKBRANCH
		// 客戶別(逐案) ELF447_CUSTID
		// 核准敘作額度 ELF447_LTCurr,
		// 案號 ELF447_PROJNO
		// 得分 ELF447_SYSTYPE,

		List<String> brNoIdlist = new ArrayList<String>();
		List<String> custIdlist = new ArrayList<String>();
		List<String> dupNolist = new ArrayList<String>();
		List<String> cnamelist = new ArrayList<String>();
		List<String> projnolist = new ArrayList<String>();
		List<String> systypelist = new ArrayList<String>();
		List<Double> twdLoanAmtlist = new ArrayList<Double>();

		for (Map<String, Object> dataMap477 : rows) {
			String brNo = Util.trim(dataMap477.get("ELF447_CHKBRANCH"));
			brNoIdlist.add(brNo);

			String custId = Util.trim(dataMap477.get("ELF447_CUSTID"));
			custIdlist.add(custId);
			String dupNo = Util.trim(dataMap477.get("ELF447_DUPNO"));
			dupNolist.add(dupNo);

			// CUSTDATA(客戶基本資料檔)找 CNAME(中文戶名)
			String cname = this.findCustData(custId, dupNo);
			cnamelist.add(cname);

			String projno = Util.trim(dataMap477.get("ELF447_PROJNO"));
			projnolist.add(projno);

			String systype = Util.trim(dataMap477.get("ELF447_SYSTYPE"));
			systypelist.add(systype);

			double twdLoanAmt = Util.parseDouble((String) dataMap477
					.get("TWD_LoanAmt"));
			twdLoanAmtlist.add(twdLoanAmt);

		}

		// 轉成EXCEL
		// produceExcel(size, brNoIdlist, ranMainId, custIdlist, dupNolist,
		// cnamelist, projnolist, systypelist, twdLoanAmtlist);

		return rows;
	}

	@Override
	public List<Map<String, Object>> findType1ByBrNoAndDate(String ovUnitNo,
			Date dateStartDate, Date dateEndDate, String caseDept, String ctype)
			throws CapException {
		// 功能說明：任何時候於管理報表執行產生"授信契約已逾期控指表",
		// 將擷取DW_LNQUOTOV中之距今2 個月內資料並以 報表方式呈現
		// 距今二個月前之日期
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		// List<Map<String, Object>> rows = dwdbService
		// .findDW_LNQUOTOVforNewReportType1ByBrNo(ovUnitNo,
		// TWNDate.toAD(dateStartDate), TWNDate.toAD(dateEndDate));
		List<Map<String, Object>> rows = misService
				.findforNewReportType1ByBrNo(ovUnitNo,
						TWNDate.toAD(dateStartDate), TWNDate.toAD(dateEndDate));
		Map<String, Object> dataCollection = null;
		for (Map<String, Object> dataMap : rows) {
			dataCollection = new LinkedHashMap<String, Object>();
			dataCollection.put("brNo", ovUnitNo);
			String custId = Util.trim(dataMap.get("CUSTID"));
			dataCollection.put("custId", custId);
			Map<String, Object> custMap = misCustdataService.findByIdDupNo(
					custId.length() > 10 ? custId.substring(0, 10) : custId,
					custId.length() > 10 ? custId.substring(10, 11) : "0");
			if (custMap != null) {
				dataCollection.put("custName", Util.trim(custMap.get("CNAME")));
				if (custMap != null)
					custMap.clear();
			}
			dataCollection.put("contract", Util.trim(dataMap.get("CNTRNO")));
			// BEG_DATEG 動用起日(DWADM.DW_LNQUOTOV)
			dataCollection.put("begDate", (Date) dataMap.get("BDATE"));
			// END_DATE 動用迄日(DWADM.DW_LNQUOTOV)
			dataCollection.put("endDate", (Date) dataMap.get("EDATE"));
			// FACT_SWFT 額度幣別
			dataCollection.put("factSwft", Util.trim(dataMap.get("CURR")));
			// FACT_AMT 核准額度(已動用)
			dataCollection.put("factAmt",
					LMSUtil.toBigDecimal(dataMap.get("FACTAMT")));
			// FACT_AMT_NT 額度金額(原金額)
			dataCollection.put("factAmtNt",
					LMSUtil.toBigDecimal(dataMap.get("FACTNT")));
			list.add(dataCollection);
		}
		return list;
	}

	@Override
	public List<Map<String, Object>> findType3ByBrNoAndDate(String ovUnitNo,
			String benDate, String endDate, String caseDept)
			throws CapException {

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> rows = misEllnseekService
				.findMisEllnseekforNewReportType3ByBrNo(benDate, endDate,
						ovUnitNo);
		Map<String, Object> dataCollection = null;
		String pattern01 = "[A-Za-z0-9]{14}";
		Pattern pat = Pattern.compile(pattern01);
		Matcher mat = null;
		DecimalFormat format = new DecimalFormat("##000");
		for (Map<String, Object> dataMap : rows) {
			dataCollection = new HashMap<String, Object>();
			String custId = Util.trim(dataMap.get("CUSTID"));
			dataCollection.put("custId", custId);
			String dupNo = Util.trim(dataMap.get("DUPNO"));
			dataCollection.put("dupNo", dupNo);
			// CUSTDATA(客戶基本資料檔)找 CNAME(中文戶名)
			// String cname = findCustData(custId, dupNo);
			String cname = Util.trim(dataMap.get("CNAME"));
			dataCollection.put("cname", cname);
			String cntrno = Util.trim(dataMap.get("CNTRNO"));
			dataCollection.put("cntrno", cntrno);
			Date gutcdate = (Date) dataMap.get("GUTCDATE");
			dataCollection.put("gutcdate", gutcdate);
			String projno = Util.trim(dataMap.get("PROJNO"));
			StringBuffer projName = new StringBuffer();
			mat = pat.matcher(Util.trim(projno));
			if (projno.length() >= 14 && mat.matches()) {
				IBranch branch = branchService
						.getBranch(projno.substring(3, 6));
				projName.append(Util.toFullCharString(projno.substring(0, 3)));
				projName.append(branch.getNameABBR());
				projName.append(Util.trim(prop
						.getProperty("LMS9511R05.number01")));
				projName.append(Util.toFullCharString(format.format(Integer
						.parseInt(projno.substring(9, 14)))));
				projName.append(Util.trim(prop
						.getProperty("LMS9511R05.number02")));
			} else {
				projName.append(projno);
			}

			dataCollection.put("projno", projName.toString());
			String property = Util.trim(dataMap.get("PROPERTY"));
			dataCollection.put("property", property);

			// String yy = Util.trim(dataMap.get("APPRYY"));
			// String mm = Util.trim(dataMap.get("APPRMM"));
			// String appdateStr = yy + "-" + mm + "-" + "01";
			String appDate = Util.trim(dataMap.get("APPDATE"));
			dataCollection.put("appdate", appDate);
			list.add(dataCollection);
		}
		return list;
	}

	@Override
	public List<Map<String, Object>> findType6ByBrNoAndDate(String brNo,
			String dataDate, String caseDept, String ctype) throws CapException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		// 授權外,ctype = "2";
		List<Map<String, Object>> rows = eloandbBaseService
				.findL120M01AJoinL120A01A_selAreaSendInfo(dataDate, brNo);
		Map<String, Object> dataCollection = null;
		for (Map<String, Object> dataMap : rows) {
			dataCollection = new HashMap<String, Object>();
			// 分行別
			String caseBrId = Util.trim(dataMap.get("CASEBRID"));
			// 統一編號
			String custId = Util.trim(dataMap.get("CUSTID"));
			// 客戶名稱
			String custName = Util.trim(dataMap.get("CUSTNAME"));
			// 報表亂碼
			String randomCode = Util.trim(dataMap.get("RANDOMCODE"));
			// 案號
			String caseNo = Util.trim(dataMap.get("CASENO"));

			// 營運中心負責經辦
			String areaAppraiser = Util.trim(dataMap.get("AREAAPPRAISER"));
			// 分行最後送件日
			String areaSendInfo = Util.trim(dataMap.get("AREASENDINFO"));
			// 分屬(企/個金案件)
			String docType = Util.trim(dataMap.get("DOCTYPE"));

			dataCollection.put("YEAR", dataDate.split("-")[0]);
			dataCollection.put("MONTH", dataDate.split("-")[1]);
			dataCollection.put("DD", dataDate.split("-")[2]);
			dataCollection.put("CASEBRID", caseBrId);
			dataCollection.put("CUSTID", custId);
			dataCollection.put("CUSTNAME", custName);
			dataCollection.put("RANDOMCODE", randomCode);
			dataCollection.put("CASENO", caseNo);
			dataCollection.put("AREAAPPRAISER", areaAppraiser);
			dataCollection.put("AREASENDINFO", areaSendInfo);
			dataCollection.put("DOCTYPE", docType);
			list.add(dataCollection);

		}// while end

		return list;
	}

	@Override
	public List<Map<String, Object>> findType8ByBrNoAndDate(String brNo,
			Date dataDate, String ctype) {
		// 功能說明：月初由法金處產生「本行各營業單位各級授權範圍內承做授信案件統計表」
		// 參數說明：YY,MM-資料年月
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> rows = misElcsecntService
				.findElcsecntforType8ByAllBrNoAndDate(TWNDate.toTW(dataDate)
						.split("/")[0], TWNDate.toTW(dataDate).split("/")[1],
						ctype);
		for (Map<String, Object> dataMap : rows) {
			Map<String, Object> dataCollection = new HashMap<String, Object>();
			dataCollection.put("year", TWNDate.toAD(dataDate).split("-")[0]);
			dataCollection.put("month", TWNDate.toAD(dataDate).split("-")[1]);
			dataCollection.put("brNo", Util.trim(dataMap.get("brNo")));

			// 新作
			dataCollection.put("tCITEM1",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM1")));
			dataCollection.put("tCITEM1AMT",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM1AMT")));

			// 續約
			dataCollection.put("tCITEM2",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM2")));

			dataCollection.put("tCITEM2AMT",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM2AMT")));

			// 變更條件
			dataCollection.put("tCITEM3",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM3")));

			dataCollection.put("tCITEM3AMT",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM3AMT")));

			// 合計

			// 無擔保授信
			dataCollection.put("tCITEM4",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM4")));

			dataCollection.put("tCITEM4AMT",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM4AMT")));

			// 擔保授信
			dataCollection.put("tCITEM5",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM5")));

			dataCollection.put("tCITEM5AMT",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM5AMT")));

			// 申報案件
			dataCollection.put("tCITEM6",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM6")));

			// 核准案件
			dataCollection.put("tCITEM7",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM7")));

			// 授權內案件
			dataCollection.put("tCITEM8",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM8")));

			// 提會案件
			dataCollection.put("tCITEM9",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM9")));

			// 常董會案件
			dataCollection.put("tCITEM10",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM10")));

			// 覆審案件
			dataCollection.put("tCitem11",
					LMSUtil.toBigDecimal(dataMap.get("tCITEM11")));

			list.add(dataCollection);
		}// while end

		return list;
	}

	/**
	 * 建立已敘做授信案件清單報表
	 * 
	 * @param batchtbl
	 */
	@Override
	public String buildCLS180R01Xls(LMSBATCH batchtbl) {
		DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		List<Map<String, Object>> data = misService.getCLS180R01Data(
				batchtbl.getBranch(), format.format(batchtbl.getBgnDate()),
				format.format(batchtbl.getEndDate()));
		Map<String, String> empMap = new HashMap<String, String>(); // 放經辦名稱，去misStaff撈不到的會去COM.BELSUSR撈

		if (data != null) {
			ByteArrayOutputStream baos = null;
			try {
				baos = new ByteArrayOutputStream();
				String tempPath = PropUtil.getProperty("loadFile.dir")
						+ "excel/CLS180R01.xls";
				File xls = new File(Thread.currentThread()
						.getContextClassLoader().getResource(tempPath).toURI());
				Workbook wbook = Workbook.getWorkbook(xls);
				// WriteAccessRecord中有用到arraycopy，但長度寫死，
				// 導致write會出錯(ArrayIndexOutOfBoundsException)，加上settings便可解決
				WorkbookSettings settings = new WorkbookSettings();
				settings.setWriteAccess(null);

				WritableWorkbook book = Workbook.createWorkbook(baos, wbook,
						settings);
				WritableSheet sheet = book.getSheet(0);
				// --------------特殊格式----------
				WritableFont fontStyle = new WritableFont(
						WritableFont.createFont("標楷體"), 12,
						WritableFont.NO_BOLD, false);
				WritableCellFormat cellformat = null;
				cellformat = LMSUtil.setCellFormat(cellformat, fontStyle,
						Alignment.LEFT);
				// 無邊框
				WritableCellFormat justFont = new WritableCellFormat(fontStyle);

				// 改成橫式
				SheetSettings setting = sheet.getSettings();
				setting.setPaperSize(PaperSize.A4);
				// setting.setScaleFactor(80);
				setting.setFitWidth(1); // 設定預覽列印與列印成為一頁, 寬度
				setting.setOrientation(PageOrientation.LANDSCAPE);

				// --------------內容--------------
				// 表頭
				Properties pro = MessageBundleScriptCreator
						.getComponentResource(LMS9511V01Page.class);
				Label head = new Label(1, 1, batchtbl.getBranch());
				sheet.addCell(head);
				head = new Label(1, 2, format.format(batchtbl.getBgnDate()));
				sheet.addCell(head);
				head = new Label(3, 2, format.format(batchtbl.getEndDate()));
				sheet.addCell(head);
				head = new Label(9, 2, pro.getProperty("1"));
				sheet.addCell(head);
				Cell title = sheet.getCell(0, 0);
				head = new Label(0, 0, branchService.getBranchName(batchtbl
						.getBranch()) + title.getContents());
				head.setCellFormat(title.getCellFormat());
				sheet.addCell(head);

				int headShift = 4;
				Map<String, String> introSrcMap = codetypeService
						.findByCodeType("L140M01A_introductionSource");
				Map<String, String> agentNoMap = codetypeService
						.findByCodeType("L140M01A_agntNo");
				Map<String, String> creditLoanTypeMap = new HashMap<String, String>();
				creditLoanTypeMap.put("1",
						pro.getProperty("CLS180R01.creditLoanType.1"));
				creditLoanTypeMap.put("2",
						pro.getProperty("CLS180R01.creditLoanType.2"));
				creditLoanTypeMap.put("4",
						pro.getProperty("CLS180R01.creditLoanType.4"));

				for (int i = 0; i < data.size(); i++) {
					Map<String, Object> pivot = data.get(i);
					// 統一編號
					String value = HtmlUtils.htmlEscape(Util.trim(pivot
							.get("LNF150_CUST_ID")));
					Label label = new Label(0, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 戶名
					value = Util.trim(pivot.get("CName"));
					label = new Label(1, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 額度序號
					value = Util.trim(pivot.get("LNF150_CONTRACT"));
					label = new Label(2, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 帳號
					value = Util.trim(pivot.get("LNF150_LOAN_NO"));
					label = new Label(3, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 授信科目
					value = Util.trim(pivot.get("LNF040_LNAP_CODE")) + "-"
							+ Util.trim(pivot.get("LNF040_ACT_NAME_S"));
					label = new Label(4, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 產品資訊
					value = HtmlUtils.htmlEscape(Util.trim(pivot
							.get("LNF110_CLASS_NAME")));
					label = new Label(5, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 產品額度
					value = Util.trim(pivot.get("ELF501_SWFT"))
							+ NumConverter.addComma(pivot
									.get("ELF501_LNAP_AMT"));
					label = new Label(6, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 總額度(元)
					value = Util.trim(pivot.get("LNF020_SWFT"))
							+ NumConverter.addComma(pivot
									.get("LNF020_FACT_AMT"));
					label = new Label(7, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 期間
					value = TWNDate.toTW(Util.parseDate(pivot
							.get("LNF150_BEG_DATE")))
							+ "-"
							+ TWNDate.toTW(Util.parseDate(pivot
									.get("LNF150_END_DATE")));
					label = new Label(8, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 引介來源
					String elf500IntroSrc = HtmlUtils.htmlEscape(Util
							.trim(pivot.get("ELF500_INTRO_SRC")));
					value = introSrcMap.get(elf500IntroSrc);
					label = new Label(9, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 引介來源-明細
					String srcNo = HtmlUtils.htmlEscape(Util.trim(pivot
							.get("SRC_NO")));
					if ("2".equals(elf500IntroSrc)) {
						// 房仲代號轉換
						value = agentNoMap.get(srcNo);
					} else {
						value = srcNo;
					}
					label = new Label(10, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 授權---不確定!!
					value = "9".equals(pivot.get("LNF020_AUTHO_LEVEL")) ? "內"
							: "外";
					label = new Label(11, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 經辦(編號)
					value = HtmlUtils.htmlEscape(Util.trim(pivot
							.get("ELF447_EMP_NO")));
					String empNo = value;// 為了要去發查SSO，先把經辦編號留起來
					label = new Label(12, headShift + i, value, cellformat);
					sheet.addCell(label);
					// 經辦(姓名)
					value = HtmlUtils.htmlEscape(Util.trim(pivot
							.get("MISSTAFF_CNAME")));
					// 經辦姓名FOR RPA User會在misstaff裡面串不到，去eLoan肚子裡串
					if (Util.isEmpty(value)) {
						value = empMap.get(empNo);
						if (Util.isEmpty(value)) {
							ElsUser elsUser = elsUserDao.findByUserId(empNo);
							if (elsUser != null) {
								value = elsUser.getUserName();
								empMap.put(empNo, value);// 放進empMap裡，避免同一員編要一直去查
							}
						}
					}
					label = new Label(13, headShift + i, value, cellformat);
					sheet.addCell(label);

					// 授信貸款種類
					String purposeSub = String.valueOf(pivot
							.get("ELF501_LNPURS_SUB"));
					purposeSub = CapString.isEmpty(purposeSub)
							&& "Y".equals(String.valueOf(pivot
									.get("ELF500_HP_HOUSE"))) ? "4"
							: purposeSub;
					value = creditLoanTypeMap.get(purposeSub);
					label = new Label(14, headShift + i, value, cellformat);
					sheet.addCell(label);
				}
				// 簽章欄
				Label signature = new Label(0, headShift + data.size(),
						pro.getProperty("tblCreater"), justFont);// 製表
				sheet.addCell(signature);
				signature = new Label(3, headShift + data.size(),
						pro.getProperty("tblApprover"), justFont);// 覆核
				sheet.addCell(signature);
				signature = new Label(5, headShift + data.size(),
						pro.getProperty("tblCompetent"), justFont);// 主管
				sheet.addCell(signature);
				// 報表亂碼
				WritableCellFormat toRight = new WritableCellFormat(fontStyle);
				toRight.setAlignment(Alignment.RIGHT);
				Label randomCode = new Label(11, headShift + data.size() + 1,
						pro.getProperty("randomCode")
								+ IDGenerator.getRandomCode(), toRight);// 製表
				sheet.addCell(randomCode);

				book.write();
				book.close();

				DocFile file = new DocFile();
				file.setMainId(batchtbl.getMainId());
				file.setData(baos.toByteArray());
				file.setCrYear(CapDate.getCurrentDate("yyyy"));
				file.setFieldId("rpt");
				file.setSrcFileName(batchtbl.getRptName() + ".xls");
				file.setUploadTime(CapDate.getCurrentTimestamp());
				file.setBranchId(batchtbl.getBranch());
				file.setContentType("application/msexcel");
				file.setSysId("LMS");
				docFileService.save(file);
				file = docFileDao.find(file);
				return file.getOid();

			} catch (Exception ex) {
				logger.error("[getContent] Exception!!", ex);
			} finally {
				if (baos != null) {
					try {
						baos.close();
					} catch (IOException ex) {
						logger.error("[getContent] Exception!!", ex);
					}
				}
			}

		}
		return null;
	}

	/**
	 * <pre>
	 * 取得 敘做無自用住宅購屋放款明細表 資料
	 * 
	 * @param LMSBATCH settings
	 * @return List<Map<String, Object>> data
	 */
	@Override
	public List<Map<String, Object>> findCLS180R02Data(LMSBATCH batchtbl) {
		DateFormat format = new SimpleDateFormat("yyyy-MM-%");
		String typeStr = batchtbl.getRemarks().split(";")[0];
		int dateType = Util.parseInt(typeStr.split("=")[1]);
		final String[] dates = { "approveTime", "useStartDate", "lnStartDate" };
		List<Map<String, Object>> result = new LinkedList<Map<String, Object>>();
		List<Map<String, Object>> data = eloandbBaseService.queryLoanList(
				batchtbl.getBranch(), dates[dateType],
				format.format(batchtbl.getBgnDate()));
		int locate = 0;
		// property
		Map<String, String> propertyMap = codetypeService
				.findByCodeType("L140S02A_property");

		// 職稱中文對應
		Map<String, String> jobTitleMap = codetypeService
				.findByCodeType("lms1205s01_jobTitle");

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		// 代入職稱
		for (int i = 0; i < data.size(); i++) {
			Map<String, Object> pivot = data.get(i);
			pivot.put("NUM", i + 1);
			// 2013-07-01 fix資料有br的問題
			pivot.put("RATEDESC",
					Util.trim(pivot.get("rateDesc")).replaceAll("<br>", "\n")
							.replaceAll("<BR>", "\n"));
			// 2013-07-01 fix資料有br的問題 END
			result.add(pivot);
			// locate++;
			pivot.put("PROPERTY", Util.nullToSpace(pivot.get("isUseDate"))
					+ Util.nullToSpace(propertyMap.get(pivot.get("property")))
					+ pivot.get("cntrNo"));
			List<Map<String, Object>> custs = eloandbBaseService.findBorrower(
					Util.nullToSpace(pivot.get("mainId")),
					Util.nullToSpace(pivot.get("cntrNo")));
			if (custs != null && custs.size() > 0) {
				Map<String, Object> current = custs.get(0);
				pivot.put("CUSTNAME", Util.nullToSpace(current.get("custName")));
				pivot.put("CUSTID",
						Util.nullToSpace(current.get("C120S01B_custId")));
				pivot.put("JOBTITLE", Util.nullToSpace(jobTitleMap.get(current
						.get("C120S01B_jobTitle"))));

				// 多重借款人
				for (int x = 1; x < custs.size(); x++) {
					current = custs.get(x);
					Map<String, Object> sameRecord = new HashMap<String, Object>();
					sameRecord.put("CUSTNAME",
							Util.nullToSpace(current.get("custName")));
					sameRecord.put("CUSTID",
							Util.nullToSpace(current.get("C120S01B_custId")));
					sameRecord.put("JOBTITLE", Util.nullToSpace(jobTitleMap
							.get(current.get("C120S01B_jobTitle"))));

					sameRecord.put("REPEAT", true);
					result.add(sameRecord);
					// locate++;
				}
			} else {
				pivot.put("CUSTNAME", "");
				pivot.put("CUSTID", "");
				pivot.put("JOBTITLE", "");
			}

			List<Map<String, Object>> guarantor = eloandbBaseService
					.findGuarantor(Util.nullToSpace(pivot.get("mainId")),
							Util.nullToSpace(pivot.get("cntrNo")));
			if (guarantor != null && guarantor.size() > 0) {
				Map<String, Object> current = guarantor.get(0);
				String custId = Util.nullToSpace(current.get("C160S01B_rId"));
				pivot.put("RNAME",
						Util.nullToSpace(current.get("C160S01B_rName")));
				pivot.put("RID", custId);
				pivot.put(
						"RTYPE",
						Util.nullToSpace(pop.getProperty("rType"
								+ current.get("C160S01B_rType"))));
				pivot.put("RJOBTITLE", Util.nullToSpace(jobTitleMap.get(current
						.get("C120S01B_jobTitle"))));
				// int shift = 0;

				// 多重保證人
				for (int x = 1; x < guarantor.size(); x++) {
					current = guarantor.get(x);
					Map<String, Object> sameRecord = null;
					boolean isRepeat = result.get(result.size() - 1).get(
							"REPEAT") != null;
					if (i + x <= result.size() && isRepeat) {
						sameRecord = result.get(locate + x);
					} else {
						sameRecord = new HashMap<String, Object>();
					}
					custId = Util.nullToSpace(current.get("C160S01B_rId"));
					sameRecord.put("RNAME",
							Util.nullToSpace(current.get("C160S01B_rName")));
					sameRecord.put("RCOMNAME",
							Util.nullToSpace(current.get("C120S01B_comName")));
					sameRecord.put("RID", custId);
					sameRecord.put(
							"RTYPE",
							Util.nullToSpace(pop.getProperty("rType"
									+ current.get("C160S01B_rType"))));
					sameRecord.put("RJOBTITLE", Util.nullToSpace(jobTitleMap
							.get(current.get("C120S01B_jobTitle"))));

					if (!isRepeat) {
						result.add(sameRecord);
						// locate++;
					}
				}
			} else {
				pivot.put("RNAME", "");
				pivot.put("RID", "");
				pivot.put("RTYPE", "");
				pivot.put("RJOBTITLE", "");
			}
			locate = result.size();
		}

		return (data == null) ? null : result;
	}

	/**
	 * <pre>
	 * 取得 分行敘做房屋貸款月報(授權內) 資料
	 * 
	 * @param LMSBATCH settings
	 * @return List<Map<String, Object>> data
	 */
	@Override
	public List<Map<String, Object>> findCLS180R03Data(LMSBATCH batchtbl,
			boolean by_brNo) {
		DateFormat format = new SimpleDateFormat("yyyy-MM-%");
		String typeStr = batchtbl.getRemarks().split(";")[0];
		int dateType = Util.parseInt(typeStr.split("=")[1]);
		final String[] dates = { "approveTime", "useStartDate", "lnStartDate" };
		List<Map<String, Object>> result = new LinkedList<Map<String, Object>>();
		String branch = batchtbl.getBranch();
		/*
		 * 依 remarks : dataType=0/1/2 來決定查 DB 時，要採用 queryBrnoAmt.approveTime 或
		 * queryBrnoAmt.useStartDate 或 queryBrnoAmt.lnStartDate
		 */
		List<Map<String, Object>> data = eloandbBaseService.queryBrnoAmt(
				by_brNo ? (branch + "%") : "%", dates[dateType],
				format.format(batchtbl.getBgnDate()));
		BranchRate branchRate = lmsService.getBranchRate(branch);
		int locate = 0;
		// property
		Map<String, String> propertyMap = codetypeService
				.findByCodeType("L140S02A_property");

		// 職稱中文對應
		Map<String, String> jobTitleMap = codetypeService
				.findByCodeType("lms1205s01_jobTitle");

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		Properties prop_cls1161s02a = MessageBundleScriptCreator
				.getComponentResource(CLS1161S02APage.class);

		// 逐筆資料
		for (int i = 0; i < data.size(); i++) {
			Map<String, Object> pivot = data.get(i);
			String amainId = Util.nullToSpace(pivot.get("mainId"));
			// 序號
			pivot.put("NUM", i + 1);

			// 2013-07-01 fix資料有br的問題
			pivot.put("RATEDESC",
					Util.trim(pivot.get("RATEDESC")).replaceAll("<br>", "\n")
							.replaceAll("<BR>", "\n"));
			// 2013-07-01 fix資料有br的問題 END

			// ============================================================
			// 在 CLS180R03 產出的 xls 底部的備註說明
			// 房貸種類代號：Ａ.購屋儲蓄貸款 Ｂ.非自用住宅購屋貸款 Ｃ.房屋修繕貸款 Ｄ.自用住宅購屋貸款。
			// C102M01A.SELFCHK 購置房屋擔保放款風險權數檢核表 .是否為自用住宅{當上述四個條件皆為「是」即為Y} =>
			// 同1額度可能有2個產品
			String type = Util.trim(pivot.get("SELFCHK"));
			if (type.length() == 0) {
				type = "C";
			} else if ("Y".equals(type)) {
				type = "D";
			} else {
				type = "B";
			}

			pivot.put("AMTCODE", Util.nullToSpace(type));
			String cntrNo = Util.trim(pivot.get("cntrNo"));
			/*
			 * 在 xls 的欄位［備註］呈現的 text 如下 2019-09-01 新做051110800999
			 */
			pivot.put("PROPERTY", Util.nullToSpace(pivot.get("isUseDate"))
					+ Util.nullToSpace(propertyMap.get(pivot.get("property")))
					+ cntrNo + getC160M01A_caseType(pivot.get("caseType")));

			if (true) {
				String _CLS180R03_ADJUST_GRADE = "";
				String GRADE1 = Util.trim(pivot.get("GRADE1"));
				String GRADE3 = Util.trim(pivot.get("GRADE3"));
				if (Util.isNotEmpty(GRADE1) && Util.isNotEmpty(GRADE3)) {
					int adjust_grade = Util.parseInt(GRADE1)
							- Util.parseInt(GRADE3);
					_CLS180R03_ADJUST_GRADE = String.valueOf(adjust_grade);
				}
				pivot.put(CLS180R03_ADJUST_GRADE, _CLS180R03_ADJUST_GRADE);
			}
			// ============================================================
			// 寬限期(抓C160S01C.nowFrom , nowEnd)
			String nowFrom_nowEndStr = "";
			if (true) {
				String nowFrom = Util.nullToSpace(pivot.get("NowFrom"));
				String nowEnd = Util.nullToSpace(pivot.get("NowEnd"));
				if (Util.isEmpty(nowFrom) && Util.isEmpty(nowEnd)) {

				} else {
					nowFrom_nowEndStr = "第" + nowFrom + "~" + nowEnd + "期";
				}
			}
			pivot.put(CLS180R03_NOWFROM_NOWEND, nowFrom_nowEndStr);

			// 擔保品
			List<Map<String, Object>> prods = eloandbBaseService.findProducts(
					amainId, cntrNo);

			// TODO 放款值(loanTwd 在消金動審表的 '外層' 呈現，目前未開放修改)
			BigDecimal sum_c160s01a_loanTwd = Util.parseBigDecimal("0");

			// 購價或時價(inAmt)
			BigDecimal sum_c160s01a_inAmt = Util.parseBigDecimal("0");

			// 區域平均參考值(EMAPESTAMT)
			BigDecimal sum_c160s01a_EMAPESTAMT = Util.parseBigDecimal("0");

			String addrCommon = prods.size() > 1 ? "" : "等" + prods.size()
					+ "筆";
			ArrayList<String> collCtrTxDateAmtStr = new ArrayList<String>();
			for (int p = 0; p < prods.size(); p++) {
				Map<String, Object> prod = prods.get(p);
				sum_c160s01a_loanTwd = sum_c160s01a_loanTwd.add(Util
						.parseBigDecimal(prod.get("TloanTwd")));

				String theCollTyp1 = Util.trim(prod.get("collTyp1"));
				if (true) {
					if (Util.equals(UtilConstants.CollTyp1.不動產, theCollTyp1)) {
						List<String> each_c160s01a_ctr_list = new ArrayList<String>();
						if (true) {
							Date ctrTxDate = (Date) prod.get("CTRTXDATE");
							if (CrsUtil.isNOT_null_and_NOTZeroDate(ctrTxDate)) {
								each_c160s01a_ctr_list.add(prop_cls1161s02a
										.getProperty("C160S01A.ctrTxDate")
										+ "：" + TWNDate.toAD(ctrTxDate) + "。");
							}
						}
						if (true) {
							Object raw_CTRTXAMT = MapUtils.getObject(prod,
									"CTRTXAMT");
							if (raw_CTRTXAMT != null
									&& Util.isNotEmpty(Util.trim(raw_CTRTXAMT))) {
								BigDecimal ctrTxAmt = CrsUtil
										.parseBigDecimal(raw_CTRTXAMT);
								;
								each_c160s01a_ctr_list.add(prop_cls1161s02a
										.getProperty("C160S01A.ctrTxAmt")
										+ "："
										+ NumConverter.addComma(ctrTxAmt)
										+ (by_brNo ? "" : "仟元") + "。");
							}
						}

						if (each_c160s01a_ctr_list.size() > 0) {
							collCtrTxDateAmtStr.add(StringUtils.join(
									each_c160s01a_ctr_list, ""));
						}

						sum_c160s01a_inAmt = sum_c160s01a_inAmt.add(Util
								.parseBigDecimal(prod.get("TinAmt")));
						sum_c160s01a_EMAPESTAMT = sum_c160s01a_EMAPESTAMT
								.add(Util.parseBigDecimal(prod
										.get("EMAPESTAMT")));
					}
				}
			}
			pivot.put("TLOANTWD", sum_c160s01a_loanTwd);
			pivot.put("INAMT", sum_c160s01a_inAmt);
			if (BigDecimal.ZERO.compareTo(sum_c160s01a_EMAPESTAMT) != 0) {
				pivot.put("INAMT_EMAPESTAMT_RATE", sum_c160s01a_inAmt.divide(
						sum_c160s01a_EMAPESTAMT, 2, BigDecimal.ROUND_HALF_UP));
			}
			pivot.put(CLS180R03R04_SUM_CTRTXAMT,
					_SUM_CTRTXAMT(collCtrTxDateAmtStr));
			if (prods.size() > 0) {
				String _taxAddr = Util.trim(prods.get(0).get("TtaxAddr"));
				if (Util.isEmpty(_taxAddr)) {
					int collTyp1_cnt = 0;
					for (Map<String, Object> rowItem : prods) {
						if (Util.equals(rowItem.get("COLLTYP1"), "01")) {
							++collTyp1_cnt;
							_taxAddr = Util.trim(rowItem.get("TtaxAddr"));
						}
					}
					addrCommon = collTyp1_cnt > 1 ? "" : "等" + collTyp1_cnt
							+ "筆";
				}
				pivot.put("TTAXADDR", _taxAddr + addrCommon);
			} else {
				pivot.put("TTAXADDR", "無");
			}

			result.add(pivot);
			// locate++;
			// 主借款人
			List<Map<String, Object>> custs = eloandbBaseService.findBorrower(
					Util.nullToSpace(pivot.get("mainId")),
					Util.nullToSpace(cntrNo));
			if (custs != null && custs.size() > 0) {
				Map<String, Object> current = custs.get(0);
				String mainCustId = Util.addSpaceWithValue(
						Util.trim(current.get("C120S01B_custId")), 10)
						+ Util.trim(current.get("C120S01B_dupNo"));

				pivot.put("CUSTNAME", Util.nullToSpace(current.get("custName")));
				pivot.put("CUSTID",
						Util.nullToSpace(current.get("C120S01B_custId")));
				pivot.put("JOBTITLE", Util.nullToSpace(jobTitleMap.get(current
						.get("C120S01B_jobTitle"))));
				pivot.put("COMNAME",
						Util.nullToSpace(current.get("C120S01B_comName")));
				String birthday = Util.nullToSpace(current
						.get("C120S01A_birthday"));
				String caseDay = Util.nullToSpace(pivot.get("CASEDATE"));
				String lnEndDay = Util.nullToSpace(pivot.get("LNENDDATE"));
				// 計算借款時年齡與加計貸款年限後之年齡
				Map<String, Object> calResultMap = calculateAgelnEndDayAge(
						birthday, caseDay, lnEndDay);
				pivot.put("AGE", calResultMap.get("AGE"));
				pivot.put("LNENDDATEAGE", calResultMap.get("LNENDDATEAGE"));
				// 原單位為萬元，產生EXCEL單位為元，要乘10000
				String income = CapMath.multiply("10000",
						Util.nullToSpace(current.get("C120S01B_income")));
				pivot.put("INCOME", income);
				HashSet<String> custIds = new HashSet<String>();
				custIds.add(mainCustId);
				if ("B".equals(Util.nullToSpace(current.get("C120S01B_custId")))) {
					custIds.add(Util.addSpaceWithValue(
							Util.trim(current.get("mCustId")), 10)
							+ Util.trim(current.get("mDupNo")));
				}

				// R03欄位[總額度(含配偶)]= sum(LNF020_FACT_AMT) and LNF020_FACT_TYPE
				// not in ('31','63')
				BigDecimal _sum_LNF020_FACT_AMT = this.sum_LNF020_FACT_AMT(
						custIds, branchRate);

				// 多重借款人
				for (int x = 1; x < custs.size(); x++) {
					current = custs.get(x);
					Map<String, Object> sameRecord = new HashMap<String, Object>();
					mainCustId = Util.nullToSpace(current
							.get("C120S01B_custId"));
					// loanTotAmt = loanTotAmt.add(countLoanTotAmt(mainCustId));
					// loanTotAmt = loanTotAmt.add(countLoanTotAmt(Util
					// .nullToSpace(current.get("mCustId"))));

					// loanTotAmt = loanTotAmt.add(countLoanTotAmt(custIds,
					// branchRate));
					sameRecord.put("CUSTNAME",
							Util.nullToSpace(current.get("custName")));
					sameRecord.put("CUSTID",
							Util.nullToSpace(current.get("C120S01B_custId")));
					sameRecord.put("JOBTITLE", Util.nullToSpace(jobTitleMap
							.get(current.get("C120S01B_jobTitle"))));
					sameRecord.put("COMNAME",
							Util.nullToSpace(current.get("C120S01B_comName")));

					sameRecord.put("REPEAT", true);
					income = CapMath.multiply("10",
							Util.nullToSpace(current.get("C120S01B_income")));
					sameRecord.put("INCOME", income);
					result.add(sameRecord);
					// locate++;
				}
				// 加入count後額度

				pivot.put("LOANTOTAMT", _sum_LNF020_FACT_AMT);
				
				// 疑似高價住宅
				String others = codetypeService.getDescOfCodeType("HP_HOUSE", "OTHERS");
				BigDecimal hpHouseAmt = CapMath.getBigDecimal(others);
				Map<String, String> hpHouseMap = codetypeService.findByCodeType("HP_HOUSE");
				for (String city : hpHouseMap.keySet()) {
				    if (CapString.trimNull(pivot.get("TTAXADDR")).contains(city)) {
				    	hpHouseAmt = CapMath.getBigDecimal(hpHouseMap.get(city));
				    }
				}
				BigDecimal hpHouseAmt95Per = hpHouseAmt.multiply(CapMath.getBigDecimal("0.95"));
				boolean isSusHpHouse = CapMath.compare(CapString.trimNull(pivot.get("INAMT")), hpHouseAmt95Per) == 1
					&& CapMath.compare(CapString.trimNull(pivot.get("INAMT")), hpHouseAmt) == -1;
				String susHpHouse = isSusHpHouse ? "Y" : "";
				pivot.put("SUS_HP_HOUSE", susHpHouse);
			} else {
				pivot.put("CUSTNAME", "");
				pivot.put("COMNAME", "");
				pivot.put("CUSTID", "");
				pivot.put("JOBTITLE", "");
				pivot.put("LOANTOTAMT", "");
				pivot.put("TLOANTWD", "");
				pivot.put("INAMT", "");
				pivot.put("TTAXADDR", "");
				pivot.put("AGE", "");
				pivot.put("LNENDDATEAGE", "");
				pivot.put("INCOME", "");
				pivot.put("INAMT_EMAPESTAMT_RATE", "");
				// 疑似高價住宅
				pivot.put("SUS_HP_HOUSE", "");
			}
			// 借保人
			List<Map<String, Object>> guarantor = eloandbBaseService
					.findGuarantor(Util.nullToSpace(pivot.get("mainId")),
							Util.nullToSpace(cntrNo));
			String[] rColKeys = new String[] { "RNAME", "RCOMNAME", "RID",
					"RTYPE", "RJOBTITLE", "RINCOME", "RAGE", "RLNENDDATEAGE" };
			for (String key : rColKeys) {
				for (int j = 0; j < 1; j++) {
					pivot.put(key + j, "");
				}
			}
			if (guarantor != null && guarantor.size() > 0) {
				Map<String, Object> current = guarantor.get(0);
				String custId = Util.nullToSpace(current.get("C160S01B_rId"));
				pivot.put("RNAME",
						Util.nullToSpace(current.get("C160S01B_rName")));
				pivot.put("RCOMNAME",
						Util.nullToSpace(current.get("C120S01B_comName")));
				pivot.put("RID", custId);
				pivot.put(
						"RTYPE",
						Util.nullToSpace(pop.getProperty("rType"
								+ current.get("C160S01B_rType"))));
				pivot.put("RJOBTITLE", Util.nullToSpace(jobTitleMap.get(current
						.get("C120S01B_jobTitle"))));
				// 原單位為萬元，產生EXCEL單位為元，要乘10000
				String income = CapMath.multiply("10000",
						Util.nullToSpace(current.get("C120S01B_income")));
				pivot.put("RINCOME", income);
				String birthday = Util.nullToSpace(current
						.get("C120S01A_birthday"));
				String caseDay = Util.nullToSpace(pivot.get("CASEDATE"));
				String lnEndDay = Util.nullToSpace(pivot.get("LNENDDATE"));
				// 計算借款時年齡與加計貸款年限後之年齡
				Map<String, Object> calResultMap = calculateAgelnEndDayAge(
						birthday, caseDay, lnEndDay);
				pivot.put("RAGE", calResultMap.get("AGE"));
				pivot.put("RLNENDDATEAGE", calResultMap.get("LNENDDATEAGE"));
				// 增加欄位
				if (UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報_稽核處
						.equals(batchtbl.getRptNo())) {
					for (String key : rColKeys) {
						pivot.put(key + "0", pivot.get(key));
					}
				}

				// 多重保證人
				for (int x = 1; x < guarantor.size(); x++) {
					current = guarantor.get(x);
					Map<String, Object> sameRecord = null;
					boolean isRepeat = result.get(result.size() - 1).get(
							"REPEAT") != null;
					if (i + x <= result.size() && isRepeat) {
						sameRecord = result.get(locate + x);
					} else {
						sameRecord = new HashMap<String, Object>();
					}
					custId = Util.nullToSpace(current.get("C160S01B_rId"));
					sameRecord.put("RNAME",
							Util.nullToSpace(current.get("C160S01B_rName")));
					sameRecord.put("RCOMNAME",
							Util.nullToSpace(current.get("C120S01B_comName")));
					sameRecord.put("RID", custId);
					sameRecord.put(
							"RTYPE",
							Util.nullToSpace(pop.getProperty("rType"
									+ current.get("C160S01B_rType"))));
					sameRecord.put("RJOBTITLE", Util.nullToSpace(jobTitleMap
							.get(current.get("C120S01B_jobTitle"))));
					// 原單位為萬元，產生EXCEL單位為元，要乘10000
					income = CapMath.multiply("10000",
							Util.nullToSpace(current.get("C120S01B_income")));
					sameRecord.put("RINCOME", income);
					birthday = Util.nullToSpace(current
							.get("C120S01A_birthday"));
					// 計算借款時年齡與加計貸款年限後之年齡
					calResultMap = calculateAgelnEndDayAge(birthday, caseDay,
							lnEndDay);
					sameRecord.put("RAGE", calResultMap.get("AGE"));
					sameRecord.put("RLNENDDATEAGE",
							calResultMap.get("LNENDDATEAGE"));
					if (!isRepeat) {
						result.add(sameRecord);
						// 增加欄位
						// locate++;
					}
					if (x == 1
							&& UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報_稽核處
									.equals(batchtbl.getRptNo())) {
						for (String key : rColKeys) {
							pivot.put(key + x, sameRecord.get(key));
						}
					}
				}
			} else {
				pivot.put("RNAME", "");
				pivot.put("RID", "");
				pivot.put("RTYPE", "");
				pivot.put("RCOMNAME", "");
				pivot.put("RJOBTITLE", "");
				pivot.put("RINCOME", "");
				pivot.put("RAGE", "");
				pivot.put("RLNENDDATEAGE", "");
			}
			locate = result.size();
		}

		return (data == null) ? null : result;
	}

	/**
	 * <pre>
	 * 取得 已敘做個人消費金融業務月報表(授權內) 資料
	 * 
	 * @param LMSBATCH settings
	 * @return List<Map<String, Object>> data
	 */
	@Override
	public List<Map<String, Object>> findCLS180R04Data(LMSBATCH batchtbl) {

		DateFormat format = new SimpleDateFormat("yyyy-MM-%");
		String typeStr = batchtbl.getRemarks().split(";")[0];
		int dateType = Util.parseInt(typeStr.split("=")[1]);
		final String[] dates = { "approveTime", "useStartDate", "lnStartDate" };
		List<Map<String, Object>> result = new LinkedList<Map<String, Object>>();
		String branch = batchtbl.getBranch();
		boolean by_brNo = true;
		if (UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表_稽核處
				.equals(batchtbl.getRptNo())) {
			by_brNo = false;
		}
		List<Map<String, Object>> data = eloandbBaseService
				.queryConsumerFinance(by_brNo ? (branch + "%") : "%",
						dates[dateType], format.format(batchtbl.getBgnDate()));
		BranchRate branchRate = lmsService.getBranchRate(branch);
		int locate = 0;
		// 職稱中文對應
		Map<String, String> jobTitleMap = codetypeService
				.findByCodeType("lms1205s01_jobTitle");
		Map<String, String> prodMap = c900m01aDao.getProdMap();
		Map<String, String> propertyMap = codetypeService
				.findByCodeType("lms1405s02_proPerty");
		Map<String, String> collTypeMap = codetypeService
				.findByCodeType("cms1090_collTyp1");
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1141R01RptServiceImpl.class);
		Properties prop_cls1161s02a = MessageBundleScriptCreator
				.getComponentResource(CLS1161S02APage.class);
		// 代入職稱
		for (int i = 0; i < data.size(); i++) {
			Map<String, Object> pivot = data.get(i);
			String amainId = Util.nullToSpace(pivot.get("mainId"));
			String l140m01a_mainId = Util.trim(pivot.get("qREFMAINID"));
			pivot.put("NUM", i + 1);
			// 2013-07-01 fix資料有br的問題
			pivot.put("RATEDESC",
					Util.trim(pivot.get("RATEDESC")).replaceAll("<br>", "\n")
							.replaceAll("<BR>", "\n"));
			// 2013-07-01 fix資料有br的問題 END

			// 套入文字
			pivot.put("PRODNM", prodMap.get(pivot.get("prodKind")));

			/**
			 * <pre>
			 * 搭配長擔貸款/長擔額度序號-
			 * 只有在產品種類為02/04 AND科目為202/204 AND動用方式[支票/金融卡]時才會顯示出來。
			 *  符合條件且選擇[是]時
			 *  產品種類為02需將本筆短擔的科目中文置換為"歡喜理財家－行家理財"；
			 * 	如改為否時需再置換為"行家理財－短期"
			 * </pre>
			 */
			if (CrsUtil.is_02(Util.trim(pivot.get("prodKind")))
					|| CrsUtil.is_68(Util.trim(pivot.get("prodKind")))) {
				if ("Y".equals(pivot.get("chkUsed"))) {
					// L140S02A.prodKind02=歡喜理財家-行家理財
					pivot.put("PRODNM", prop.getProperty("L140S02A.prodKind02"));

				}
			}
			if (ProdKindEnum.房貸專案_B案_100億_31.getCode().equals(
					pivot.get("prodKind"))
					&& ClsUtil.is_403_603(Util.trim(pivot.get("SUBJCODE")))) {
				// CLS180R04
				pivot.put(
						"PRODNM",
						Util.trim(pivot.get("PRODNM"))
								+ prop.getProperty("L140S02A.prodKind31_403603"));
			}

			pivot.put("PROPERTY", propertyMap.get(pivot.get("property"))
					+ getC160M01A_caseType(pivot.get("caseType")));
			String cntrNo = Util.nullToSpace(pivot.get("cntrNo"));
			// 擔保品
			List<Map<String, Object>> prods = eloandbBaseService.findProducts(
					amainId, cntrNo);

			// 押值(totalLoanAmt)
			// 押值(折算後) totLnAmt
			BigDecimal sum_c160s01a_totLnAmt = Util.parseBigDecimal("0");
			// 購價或時價(inAmt)
			BigDecimal sum_c160s01a_inAmt = Util.parseBigDecimal("0");
			// 區域平均參考值(EMAPESTAMT)
			BigDecimal sum_c160s01a_EMAPESTAMT = Util.parseBigDecimal("0");

			Map<String, String> collTyp1 = new HashMap<String, String>();
			ArrayList<String> collStr = new ArrayList<String>();
			ArrayList<String> collCtrTxDateAmtStr = new ArrayList<String>();
			for (int p = 0; p < prods.size(); p++) {
				Map<String, Object> prod = prods.get(p);

				// 因 押值 可在 動審表 時，由分行修正
				// 優先抓取 totLnAmt，讓呈現的 CLS180R04 的資料更正確
				if (Util.equals("Y", prod.get("UNITCHG"))) {
					sum_c160s01a_totLnAmt = sum_c160s01a_totLnAmt.add(Util
							.parseBigDecimal(prod.get("TOTLNAMT")).multiply(
									new BigDecimal("1000")));
				} else {
					sum_c160s01a_totLnAmt = sum_c160s01a_totLnAmt.add(Util
							.parseBigDecimal(prod.get("TloanTwd")));
				}

				String theCollTyp1 = Util.trim(prod.get("collTyp1"));
				if (Util.isEmpty(collTyp1.get(theCollTyp1))) { // 用 theCollTyp1
																// 來控制只出現1次(避免不動產、不動產)
					// 判斷該筆若為勞工紓困，ctheCollTyp1為空白，一率寫入[05]=保證
					if (ProdKindEnum.勞工紓困貸款_69.getCode().equals(
							pivot.get("prodKind"))) {
						if (Util.isEmpty(theCollTyp1)) {
							theCollTyp1 = COLLTYPE05;
							// J-110-0233 在
							// 2021-06開辦的勞工紓困貸款，因額度明細表是人RPA產出，未引入擔保品 =>
							// 「押值」顯示「核准額度金額」而非0
							sum_c160s01a_totLnAmt = Util.parseBigDecimal(pivot
									.get("APPROVEDAMT"));
						}
					}

					collStr.add(Util.nullToSpace(collTypeMap.get(theCollTyp1)));
					collTyp1.put(theCollTyp1, "dup");
				}

				if (Util.equals(UtilConstants.CollTyp1.不動產, theCollTyp1)) {
					List<String> each_c160s01a_ctr_list = new ArrayList<String>();
					if (true) {
						Date ctrTxDate = (Date) prod.get("CTRTXDATE");
						if (CrsUtil.isNOT_null_and_NOTZeroDate(ctrTxDate)) {
							each_c160s01a_ctr_list.add(prop_cls1161s02a
									.getProperty("C160S01A.ctrTxDate")
									+ "："
									+ TWNDate.toAD(ctrTxDate) + "。");
						}
					}
					if (true) {
						Object raw_CTRTXAMT = MapUtils.getObject(prod,
								"CTRTXAMT");
						if (raw_CTRTXAMT != null
								&& Util.isNotEmpty(Util.trim(raw_CTRTXAMT))) {
							BigDecimal ctrTxAmt = CrsUtil
									.parseBigDecimal(raw_CTRTXAMT);
							;
							each_c160s01a_ctr_list.add(prop_cls1161s02a
									.getProperty("C160S01A.ctrTxAmt")
									+ "："
									+ NumConverter.addComma(ctrTxAmt) + "。");
						}
					}

					if (each_c160s01a_ctr_list.size() > 0) {
						collCtrTxDateAmtStr.add(StringUtils.join(
								each_c160s01a_ctr_list, ""));
					}

					sum_c160s01a_inAmt = sum_c160s01a_inAmt.add(Util
							.parseBigDecimal(prod.get("TinAmt")));
					sum_c160s01a_EMAPESTAMT = sum_c160s01a_inAmt.add(Util
							.parseBigDecimal(prod.get("EMAPESTAMT")));
				}
			}
			/*
			 * 之前輸入擔保品的欄位，金額單位不一致，後來加入 unitChg ，統一為「仟元」 在 createExcel(...) 指定
			 * base=1000 當 (col instanceof BigDecimal) 就去除 base
			 */
			pivot.put(CLS180R04_TOTALLOANAMT, sum_c160s01a_totLnAmt);
			pivot.put("COLLTYP1", StringUtils.join(collStr, "、"));
			pivot.put(CLS180R03R04_SUM_CTRTXAMT,
					_SUM_CTRTXAMT(collCtrTxDateAmtStr));
			pivot.put("INAMT", sum_c160s01a_inAmt);
			pivot.put("EMAPESTAMT", sum_c160s01a_EMAPESTAMT);
			if (BigDecimal.ZERO.compareTo(sum_c160s01a_EMAPESTAMT) != 0) {
				pivot.put("INAMT_EMAPESTAMT_RATE", sum_c160s01a_inAmt.divide(
						sum_c160s01a_EMAPESTAMT, 2, BigDecimal.ROUND_HALF_UP));
			}		
			result.add(pivot);

			List<Map<String, Object>> custs = eloandbBaseService.findBorrower(
					amainId, cntrNo);
			if (custs != null && custs.size() > 0) {
				Map<String, Object> current = custs.get(0);
				String mainCustId = Util.addSpaceWithValue(
						Util.trim(current.get("C120S01B_custId")), 10)
						+ Util.trim(current.get("C120S01B_dupNo"));
				pivot.put("CUSTNAME", Util.nullToSpace(current.get("custName")));
				pivot.put("CUSTID",
						Util.nullToSpace(current.get("C120S01B_custId")));
				pivot.put("JOBTITLE", Util.nullToSpace(jobTitleMap.get(current
						.get("C120S01B_jobTitle"))));
				pivot.put("COMNAME",
						Util.nullToSpace(current.get("C120S01B_comName")));
				String birthday = Util.nullToSpace(current
						.get("C120S01A_birthday"));
				String caseDay = Util.nullToSpace(pivot.get("CASEDATE"));
				String lnEndDay = Util.nullToSpace(pivot.get("LNENDDATE"));
				// 計算借款時年齡與加計貸款年限後之年齡
				Map<String, Object> calResultMap = calculateAgelnEndDayAge(
						birthday, caseDay, lnEndDay);
				pivot.put("AGE", calResultMap.get("AGE"));
				pivot.put("LNENDDATEAGE", calResultMap.get("LNENDDATEAGE"));
				// 原單位為萬元
				String income = CapMath.multiply("10000",
						Util.nullToSpace(current.get("C120S01B_income")));
				pivot.put("INCOME", income);
				HashSet<String> custIds = new HashSet<String>();
				custIds.add(mainCustId);
				// J-107-0033[授信總額度]改為「簽案時額度明細表中授信額度合計之金額」。
				// R04欄位[授信總額度]
				BigDecimal _LOANTOTAMT = this._l140m01a_LoanTotAmt(
						l140m01a_mainId, branchRate);
				if (_LOANTOTAMT == null
						|| _LOANTOTAMT.compareTo(BigDecimal.ZERO) == 0) {
					_LOANTOTAMT = this.sum_LNF020_FACT_AMT(custIds, branchRate);
				}
				if (true) {
					// ask 授管處, 單位：新臺幣仟元, 四捨五入至整數位
					BigDecimal baseOn = new BigDecimal("1000");
					_LOANTOTAMT = Arithmetic.div(_LOANTOTAMT, baseOn, 0)
							.multiply(baseOn);
				}
				// 多重借款人
				for (int x = 1; x < custs.size(); x++) {
					current = custs.get(x);
					mainCustId = Util.nullToSpace(current
							.get("C120S01B_custId"));
					// loanTotAmt = countLoanTotAmt(custIds, branchRate);
					Map<String, Object> sameRecord = new HashMap<String, Object>();
					sameRecord.put("CUSTNAME",
							Util.nullToSpace(current.get("custName")));
					sameRecord.put("CUSTID",
							Util.nullToSpace(current.get("C120S01B_custId")));
					sameRecord.put("JOBTITLE", Util.nullToSpace(jobTitleMap
							.get(current.get("C120S01B_jobTitle"))));
					sameRecord.put("COMNAME",
							Util.nullToSpace(current.get("C120S01B_comName")));
					// 原單位為萬元
					income = CapMath.multiply("10000",
							Util.nullToSpace(current.get("C120S01B_income")));
					sameRecord.put("INCOME", income);
				}
				// 加入count後額度
				pivot.put("LOANTOTAMT", _LOANTOTAMT);
			} else {
				pivot.put("CUSTNAME", "");
				pivot.put("COMNAME", "");
				pivot.put("CUSTID", "");
				pivot.put("JOBTITLE", "");
				pivot.put("LOANTOTAMT", 0);
				pivot.put("APPROVEDAMT", 0);
				pivot.put("AGE", "");
				pivot.put("LNENDDATEAGE", "");
				pivot.put("INCOME", "");
				pivot.put("INAMT", "");
				pivot.put("EMAPESTAMT", "");
				pivot.put("INAMT_EMAPESTAMT_RATE", "");
			}

			List<Map<String, Object>> guarantor = eloandbBaseService
					.findGuarantor(amainId, cntrNo);
			String[] rColKeys = new String[] { "RNAME", "RCOMNAME", "RID",
					"RTYPE", "RJOBTITLE", "RINCOME", "RAGE", "RLNENDDATEAGE" };
			if (UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表_稽核處
					.equals(batchtbl.getRptNo())) {
				for (String key : rColKeys) {
					for (int j = 0; j < 1; j++) {
						pivot.put(key + j, "");
					}
				}
			}
			if (guarantor != null && guarantor.size() > 0) {
				Map<String, Object> current = guarantor.get(0);
				String custId = Util.nullToSpace(current.get("C160S01B_rId"));
				pivot.put("RNAME",
						Util.nullToSpace(current.get("C160S01B_rName")));
				pivot.put("RCOMNAME",
						Util.nullToSpace(current.get("C120S01B_comName")));
				pivot.put("RID", custId);
				pivot.put(
						"RTYPE",
						Util.nullToSpace(pop.getProperty("rType"
								+ current.get("C160S01B_rType"))));
				pivot.put("RJOBTITLE", Util.nullToSpace(jobTitleMap.get(current
						.get("C120S01B_jobTitle"))));
				// J-110-0413 新增保證人年所得
				// 原單位為萬元
				String rincome = CapMath.multiply("10000",
						Util.nullToSpace(current.get("C120S01B_income")));
				pivot.put("RINCOME", rincome);
				String birthday = Util.nullToSpace(current
						.get("C120S01A_birthday"));
				String caseDay = Util.nullToSpace(pivot.get("CASEDATE"));
				String lnEndDay = Util.nullToSpace(pivot.get("LNENDDATE"));
				// 計算借款時年齡與加計貸款年限後之年齡
				Map<String, Object> calResultMap = calculateAgelnEndDayAge(
						birthday, caseDay, lnEndDay);
				pivot.put("RAGE", calResultMap.get("AGE"));
				pivot.put("RLNENDDATEAGE", calResultMap.get("LNENDDATEAGE"));
				// 增加欄位
				if (UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表_稽核處
						.equals(batchtbl.getRptNo())) {
					for (String key : rColKeys) {
						pivot.put(key + "0", pivot.get(key));
					}
				}

				// 多重保證人
				for (int x = 1; x < guarantor.size(); x++) {
					current = guarantor.get(x);
					Map<String, Object> sameRecord = null;
					boolean isRepeat = result.get(result.size() - 1).get(
							"REPEAT") != null;
					if (i + x <= result.size() && isRepeat) {
						sameRecord = result.get(locate + x);
					} else {
						sameRecord = new HashMap<String, Object>();
					}
					custId = Util.nullToSpace(current.get("C160S01B_rId"));
					sameRecord.put("RNAME",
							Util.nullToSpace(current.get("C160S01B_rName")));
					sameRecord.put("RCOMNAME",
							Util.nullToSpace(current.get("C120S01B_comName")));
					sameRecord.put("RID", custId);
					sameRecord.put(
							"RTYPE",
							Util.nullToSpace(pop.getProperty("rType"
									+ current.get("C160S01B_rType"))));
					sameRecord.put("RJOBTITLE", Util.nullToSpace(jobTitleMap
							.get(current.get("C120S01B_jobTitle"))));
					// 原單位為萬元
					rincome = CapMath.multiply("10000",
							Util.nullToSpace(current.get("C120S01B_income")));
					sameRecord.put("RINCOME", rincome);
					birthday = Util.nullToSpace(current
							.get("C120S01A_birthday"));
					// 計算借款時年齡與加計貸款年限後之年齡
					calResultMap = calculateAgelnEndDayAge(birthday, caseDay,
							lnEndDay);
					sameRecord.put("RAGE", calResultMap.get("AGE"));
					sameRecord.put("RLNENDDATEAGE",
							calResultMap.get("LNENDDATEAGE"));
					if (!isRepeat) {
						result.add(sameRecord);
					}
					// 增加欄位
					if (x == 1
							&& UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表_稽核處
									.equals(batchtbl.getRptNo())) {
						for (String key : rColKeys) {
							pivot.put(key + x, sameRecord.get(key));
						}
					}
				}
			} else {
				pivot.put("RNAME", "");
				pivot.put("RID", "");
				pivot.put("RTYPE", "");
				pivot.put("RCOMNAME", "");
				pivot.put("RJOBTITLE", "");
				pivot.put("RINCOME", "");
				pivot.put("RAGE", "");
				pivot.put("RLNENDDATEAGE", "");
			}
			locate = result.size();
		}

		return (data == null) ? null : result;
	}

	// J-108-0290 增加顯示此額度所對應的擔保品契約價格
	private String _SUM_CTRTXAMT(List<String> collCtrTxDateAmtStr) {
		String val_SUM_CTRTXAMT = "";
		if (collCtrTxDateAmtStr.size() <= 1) {
			val_SUM_CTRTXAMT = StringUtils.join(collCtrTxDateAmtStr, "");
		} else {
			for (int idx = 1; idx <= collCtrTxDateAmtStr.size(); idx++) {
				val_SUM_CTRTXAMT += ("(" + idx + ")"
						+ collCtrTxDateAmtStr.get(idx - 1) + " ");
			}
		}
		return val_SUM_CTRTXAMT;
	}

	/**
	 * 計算月報總額度
	 * 
	 * @param custIds
	 *            客戶統編
	 * @param branchRate
	 *            匯率轉換
	 * @return
	 */
	private BigDecimal sum_LNF020_FACT_AMT(HashSet<String> custIds,
			BranchRate branchRate) {
		BigDecimal result = BigDecimal.ZERO;
		List<Map<String, Object>> rows = misService.caleLNF020AMT(custIds);
		for (Map<String, Object> data : rows) {
			BigDecimal amt = Util.parseBigDecimal(data.get("AMT"));
			result = result.add(branchRate.toTWDAmt(
					Util.trim(data.get("CURR")), amt));
		}
		return result;
	}

	private BigDecimal _l140m01a_LoanTotAmt(String l140m01a_mainId,
			BranchRate branchRate) {
		L140M01A l140m01a = clsService.findL140M01A_mainId(l140m01a_mainId);
		if (l140m01a != null) {
			String curr = Util.trim(l140m01a.getLoanTotCurr());
			BigDecimal amt = l140m01a.getLoanTotAmt();
			if (amt != null) {
				if (Util.equals("TWD", curr)) {
					return amt;
				} else {
					return branchRate.toTWDAmt(curr, amt);
				}
			}
		}
		return null;
	}

	@Override
	public String createCLS180R05PDF(LMSBATCH batchtbl) {
		String[] dataDate = TWNDate.toTW(batchtbl.getDataDate()).split("/");
		List<Map<String, Object>> data = clsDwService.queryDeductPoint(
				batchtbl.getBranch(), dataDate[0] + dataDate[1]);
		if (Util.isNotEmpty(data)) {

			try {
				for (int i = 0; i < data.size(); i++) {
					Map<String, Object> pivot = data.get(i);
					pivot.put("NUM", i + 1);
					Map<String, Object> custData = misService.getCustData(
							Util.trim(pivot.get("custId")),
							Util.trim(pivot.get("dupNo")));
					if (custData != null) {
						pivot.put("CUSTNAME", custData.get("CName"));
					}
				}
				return clsRptService.generateReport(data, 5, batchtbl);

			} catch (Exception e) {
				logger.error("[getContent] Exception!!", e);
			}
		}
		return null;
	}

	@Override
	public String createCLS180R06PDF(LMSBATCH batchtbl) {
		try {
			List<Map<String, Object>> data = new LinkedList<Map<String, Object>>();
			Date bgnDate = batchtbl.getBgnDate(), endDate = batchtbl
					.getEndDate();
			Map<String, Object> result = new HashMap<String, Object>();
			// 產品種類
			String[] prod = batchtbl.getRemarks().split(";");
			String prodKind = prod[0].split("=")[1];
			result.put("CLS180R06.prodName", prod[1].split("=")[1]);
			result.put("CLS180R06.bgnDate", TWNDate.toTW(batchtbl.getBgnDate()));
			result.put("CLS180R06.endDate", TWNDate.toTW(batchtbl.getEndDate()));
			result.put("CLS180R06.amtBase", "NT$");
			// 核准
			Map<String, Object> finds = eloandbBaseService.queryC180R06Data(
					prodKind, new String[] { "030", "030" },
					TWNDate.toAD(bgnDate), TWNDate.toAD(endDate));
			result.put("CLS180R06.approveNum",
					NumConverter.addComma(finds.get("NUM")));
			Object amt = finds.get("AMT");
			if (amt == null)
				result.put("CLS180R06.approveAmt", 0);
			else
				result.put("CLS180R06.approveAmt", NumConverter.addComma(amt));
			// 婉卻
			finds = eloandbBaseService.queryC180R06Data(prodKind, new String[] {
					"040", "040" }, TWNDate.toAD(bgnDate),
					TWNDate.toAD(endDate));
			result.put("CLS180R06.rejectNum",
					NumConverter.addComma(finds.get("NUM")));
			// 撥款
			finds = eloandbBaseService.queryC180R06Data(prodKind, null,
					TWNDate.toAD(bgnDate), TWNDate.toAD(endDate));
			result.put("CLS180R06.appropriationNum",
					NumConverter.addComma(finds.get("NUM")));
			amt = finds.get("AMT");
			if (amt == null)
				result.put("CLS180R06.appropriationAmt", 0);
			else
				result.put("CLS180R06.appropriationAmt",
						NumConverter.addComma(finds.get("AMT")));
			// 未結案
			finds = eloandbBaseService.queryC180R06Data(prodKind, new String[] {
					"010", "020" }, TWNDate.toAD(bgnDate),
					TWNDate.toAD(endDate));
			result.put("CLS180R06.pendingNum",
					NumConverter.addComma(finds.get("NUM")));
			data.add(result);
			return clsRptService.generateReport(data, 6, batchtbl);

		} catch (Exception e) {
			logger.error("[getContent] Exception!!", e);
		}
		return null;
	}

	@Override
	public List<Map<String, Object>> findCLS180R09Data(LMSBATCH batchtbl) {
		List<Map<String, Object>> result = eloandbBaseService.queryReject(
				batchtbl.getBranch(), Util.getDate(batchtbl.getBgnDate()),
				Util.getDate(batchtbl.getEndDate()));
		Map<String, String> rejectMap = codetypeService
				.findByCodeType("RejtCode");
		for (int i = 0; i < result.size(); i++) {
			Map<String, Object> pivot = result.get(i);
			pivot.put("NUM", i + 1);
			pivot.put(
					"APPROVETIME",
					TWNDate.toAD(CapDate.getDate(
							Util.trim(pivot.get("APPROVETIME")), "yyyy-MM-dd")));
			pivot.put("CESRJTCAUSE", rejectMap.get(pivot.get("CESRJTCAUSE")));
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> findCLS180R10Data(LMSBATCH batchtbl) {
		List<Map<String, Object>> result = misService.getCLS180R10Data(
				batchtbl.getBranch(), Util.getDate(batchtbl.getBgnDate()),
				Util.getDate(batchtbl.getEndDate()));
		for (int i = 0; i < result.size(); i++) {
			Map<String, Object> pivot = result.get(i);
			pivot.put("NUM", i + 1);
		}
		return result;
	}

	private String build_cls_rptName(LMSBATCH batchtbl) {
		String rptName = get_rptName(CRS_CODETYPESTR, batchtbl);
		String rptNo = batchtbl.getRptNo();
		if (Util.equals(rptNo,
				UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報_稽核處)
				|| Util.equals(rptNo,
						UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表_稽核處)) {
			return StringUtils.substring(TWNDate.toAD(batchtbl.getDataDate()),
					0, 7) + "_" + rptName;
		}
		return rptName;
	}

	/**
	 * 在 commons-csv 的 QuoteMode 有分為 MINIMAL , NON_NUMERIC 但目前 e-Loan 所使用的 lib
	 * 不包含這個 jar 檔
	 */
	private void createCSV(Writer writer, List<String[]> list,
			Set<Integer> numeric_idx, final char CSV_QUOTE, String replaceCrLf,
			boolean _QuoteMode_NON_NUMERIC) throws IOException {
		for (String[] data_arr : list) {
			logger.info("data_arr.length!!" + data_arr.length);
			int size = data_arr.length;
			for (int i = 0; i < size; i++) {
				String colData = data_arr[i];
				if (numeric_idx.contains(i)) {
					writer.write(colData);
				} else {
					colData = StringUtils.replace(colData, "\n", replaceCrLf); // 處理換行
					colData = StringEscapeUtils.escapeCsv(colData);
					logger.info("[getContent] Exception!!" + CSV_QUOTE);
					boolean addQuoteChar = colData.length() == 0 ? true
							: (colData.charAt(0) != CSV_QUOTE || colData
									.charAt(colData.length() - 1) != CSV_QUOTE);
					if (_QuoteMode_NON_NUMERIC && addQuoteChar) {
						writer.write(CSV_QUOTE);
					}
					writer.write(colData);
					if (_QuoteMode_NON_NUMERIC && addQuoteChar) {
						writer.write(CSV_QUOTE);
					}
				}

				if (i == size - 1) {
					writer.write("\r\n");
				} else {
					writer.write(",");
				}
			}
		}
	}

	/**
	 * 本來的報表 CLS180R03 <br/>
	 * 若一個額度序號有3個保證人，會在主借人之下，再多出2列 => 和 稽核處 討論後，傳檔時不需傳送保證人的4個欄位［性質、姓名、服務機關 、職務］ <br/>
	 * 數值部分 ● XLS 是單位千元，為了閱讀方便，資料格式有千分位符號。例如：1,234 ● CSV 是單位元，傳的資料格式，不需有「千分位符號」
	 */
	private String createCSV_CLS180R03B(List<Map<String, Object>> data,
			LMSBATCH batchtbl, int firstRow, String countRow, String[] colOrder) { // J-110-0013
																					// ,
																					// (109)
																					// 第
																					// 3808
																					// 號
																					// ,
																					// 「授權內分行敘做房屋貸款月報」增列「模型初始評等」、「調整評等」、「調整評等理由」等欄位
		if (Util.isNotEmpty(data)) {
			int colSize = colOrder.length;
			ByteArrayOutputStream baos = null;
			Writer writer = null;
			try {
				List<String[]> list = new ArrayList<String[]>();
				for (int i = 0; i < data.size(); i++) {
					Map<String, Object> pivot = data.get(i);
					// ~~~~~~~~~~~~~~~~~~~~~~~~~
					// 移除 NUM=空白 的資料行
					// 當1個額度有多個保證人，第2~N個保證人的 {NUM=空白}
					if (true) {
						if (Util.isEmpty(Util.trim(MapUtils.getString(pivot,
								"NUM")))) {
							continue;
						}
					}
					// ~~~~~~~~~~~~~~~~~~~~~~~~~
					String[] lineDataArr = new String[colSize];

					for (int j = 0; j < colOrder.length; j++) {
						Object col = pivot.get(colOrder[j]);
						if (col instanceof BigDecimal) {
							lineDataArr[j] = LMSUtil
									.pretty_numStr((BigDecimal) col);
						} else {
							lineDataArr[j] = Util.toSemiCharString(Util
									.trim(col));
						}
					}
					// ~~~~~~~~~~~~~~~~~~~~~~~~~
					list.add(lineDataArr);
				}

				Set<Integer> numeric_idx = new HashSet<Integer>();
				numeric_idx.add(0);
				numeric_idx.add(5);
				numeric_idx.add(6);
				numeric_idx.add(7);
				// numeric_idx.add(8); 近3年內移轉成交價格 是組字
				numeric_idx.add(15); // "總額度(含配偶)"
				numeric_idx.add(19); // 初始評等
				numeric_idx.add(20); // 調整評等
				numeric_idx.add(24); // 借款人年所得
				numeric_idx.add(29); // 保證人年所得
				numeric_idx.add(36); // 保證人年所得
				char CSV_QUOTE = '"'; // char quoteChar = '\u0022';
				String replaceCrLf = "   ";
				boolean _QuoteMode_NON_NUMERIC = true;

				baos = new ByteArrayOutputStream();
				writer = new BufferedWriter(new OutputStreamWriter(baos));
				createCSV(writer, list, numeric_idx, CSV_QUOTE, replaceCrLf,
						_QuoteMode_NON_NUMERIC);
				// logger.debug("bef_close, baos.length={"+baos.toByteArray().length+"}");
				writer.close(); // 其 java API 註解： Closes the stream, flushing it
								// first.
				// logger.debug("aft_close, baos.length={"+baos.toByteArray().length+"}");
				// ---------------------------
				DocFile file = new DocFile();
				file.setMainId(batchtbl.getMainId());
				file.setData(baos.toByteArray());
				file.setCrYear(CapDate.getCurrentDate("yyyy"));
				file.setFieldId("rpt");
				file.setSrcFileName(batchtbl.getRptName() + ".csv");
				file.setUploadTime(CapDate.getCurrentTimestamp());
				file.setBranchId(batchtbl.getBranch());
				file.setContentType("text/csv");
				file.setSysId("LMS");
				docFileService.save(file);
				file = docFileDao.find(file);
				return file.getOid();
			} catch (Exception ex) {
				logger.error("[getContent] Exception!!", ex);
			} finally {
				IOUtils.closeQuietly(writer);
				IOUtils.closeQuietly(baos);
			}
		}
		return null;
	}

	/**
	 * 授權內已敘做個人消費金融業務月報表_稽核處
	 * J-110-0413請按月提供增列「模型初始評等GRADE1」、「調整評等GRADE2」、「調整評等理由ADJUSTREASON
	 * 」、「額度除以放款值」、「LTV」、「借款人年齡」及「借款人年齡加計貸款年限後之年齡」等七個欄位之近一年(上年度1月至今年度最近月份)分行授權內「
	 * 已敘做個人消費金融業務月報表(授權內)」予稽核處。
	 */
	private String createCSV_CLS180R04B(List<Map<String, Object>> data,
			LMSBATCH batchtbl, int firstRow, String countRow, String[] colOrder) {
		if (Util.isNotEmpty(data)) {
			int colSize = colOrder.length;
			ByteArrayOutputStream baos = null;
			Writer writer = null;
			try {
				List<String[]> list = new ArrayList<String[]>();
				for (int i = 0; i < data.size(); i++) {
					Map<String, Object> pivot = data.get(i);
					// ~~~~~~~~~~~~~~~~~~~~~~~~~
					// 移除 NUM=空白 的資料行
					// 當1個額度有多個保證人，第2~N個保證人的 {NUM=空白}
					if (true) {
						if (Util.isEmpty(Util.trim(MapUtils.getString(pivot,
								"NUM")))) {
							continue;
						}
					}
					// ~~~~~~~~~~~~~~~~~~~~~~~~~
					String[] lineDataArr = new String[colSize];

					for (int j = 0; j < colOrder.length; j++) {
						Object col = pivot.get(colOrder[j]);
						if (col instanceof BigDecimal) {
							lineDataArr[j] = LMSUtil
									.pretty_numStr((BigDecimal) col);
						} else {
							lineDataArr[j] = Util.toSemiCharString(Util
									.trim(col));
						}
					}
					// ~~~~~~~~~~~~~~~~~~~~~~~~~
					list.add(lineDataArr);
				}

				Set<Integer> numeric_idx = new HashSet<Integer>();
				numeric_idx.add(0); // 序號
				numeric_idx.add(6); // 借款人年齡
				numeric_idx.add(7); // 借款人年齡加計貸款年限後之年齡
				numeric_idx.add(8); // 借款人年所得
				numeric_idx.add(9); // 額度
				numeric_idx.add(15); // 擔保品押值
				// numeric_idx.add(16); //擔保品近3年內移轉成交價格
				numeric_idx.add(21); // 保證人年所得
				numeric_idx.add(28); // 保證人年所得
				numeric_idx.add(31); // 授信總額度
				numeric_idx.add(32); // 模型初始評等
				numeric_idx.add(33); // 調整評等
				numeric_idx.add(36); // 不動產擔保品估值
				char CSV_QUOTE = '"'; // char quoteChar = '\u0022';
				String replaceCrLf = "   ";
				boolean _QuoteMode_NON_NUMERIC = true;

				baos = new ByteArrayOutputStream();
				writer = new BufferedWriter(new OutputStreamWriter(baos));
				createCSV(writer, list, numeric_idx, CSV_QUOTE, replaceCrLf,
						_QuoteMode_NON_NUMERIC);
				// logger.debug("bef_close, baos.length={"+baos.toByteArray().length+"}");
				writer.close(); // 其 java API 註解： Closes the stream, flushing it
								// first.
				// logger.debug("aft_close, baos.length={"+baos.toByteArray().length+"}");
				// ---------------------------
				DocFile file = new DocFile();
				file.setMainId(batchtbl.getMainId());
				file.setData(baos.toByteArray());
				file.setCrYear(CapDate.getCurrentDate("yyyy"));
				file.setFieldId("rpt");
				file.setSrcFileName(batchtbl.getRptName() + ".csv");
				file.setUploadTime(CapDate.getCurrentTimestamp());
				file.setBranchId(batchtbl.getBranch());
				file.setContentType("text/csv");
				file.setSysId("LMS");
				docFileService.save(file);
				file = docFileDao.find(file);
				return file.getOid();
			} catch (Exception ex) {
				logger.error("[getContent] Exception!!", ex);
			} finally {
				IOUtils.closeQuietly(writer);
				IOUtils.closeQuietly(baos);
			}
		}
		return null;
	}

	/**
	 * 土 CLS180R52<br/>
	 * 
	 */

	private String createCSV_CLS180R52(LMSBATCH batchtbl, String[] colOrder) {
		DocFile docFile = null;

		// 抓取DB資料抓並產出檔案(.CSV)
		List<Map<String, Object>> data = null;
		data = eloandbBaseService.listCLS180R52();
		// docFile = lms9511Service.saveDocFile("001","test", listName, "xls");
		if (Util.isNotEmpty(data)) {
			int colSize = colOrder.length;
			ByteArrayOutputStream baos = null;
			Writer writer = null;
			try {
				List<String[]> list = new ArrayList<String[]>();
				for (int i = 0; i < data.size(); i++) {
					Map<String, Object> map = data.get(i);
					String[] lineDataArr = new String[colSize];
					String custName = Util.trim(map.get("CUSTNAME"));
					String ownBrid = Util.trim(map.get("OWNBRID"));
					String ownBrName = branchService.getBranchName(ownBrid);
					String agentCertYear = Util.trim(map.get("AGENTCERTYEAR"));
					String agentCertWord = Util.trim(map.get("AGENTCERTWORD"));
					String agentCertNo = Util.trim(map.get("AGENTCERTNO"));
					String estateAgentFlag = Util.trim(map
							.get("ESTATEAGENTFLAG"));
					String record17Flag = Util.trim(map.get("RECORD17FLAG"));
					String codeDesc = Util.trim(map.get("CODEDESC"));
					String codeDesc3 = Util.trim(map.get("CODEDESC3"));
					String status = "";
					if (Util.isNotEmpty(codeDesc3)) {
						if (codeDesc3.equals(LMSUtil.地政士黑名單拒絕名單)) {
							status = "拒絕";
						} else if (codeDesc3.equals(LMSUtil.地政士黑名單警示名單)) {
							status = "警示";
						}
					}
					String approver = Util.trim(map.get("APPROVER"));
					String approvetime = Util.trim(map.get("APPROVETIME"));
					String memo = Util.trim(map.get("MEMO"));
					String creator = Util.trim(map.get("CREATOR"));
					String createTime = Util.trim(map.get("CREATETIME"));

					// 開始寫入資料
					lineDataArr[0] = custName;
					lineDataArr[1] = ownBrid + " " + ownBrName;
					lineDataArr[2] = "(" + agentCertYear + ")" + agentCertWord
							+ "字第" + agentCertNo + "號";
					lineDataArr[3] = estateAgentFlag;
					lineDataArr[4] = record17Flag;
					lineDataArr[5] = codeDesc;
					lineDataArr[6] = status;
					lineDataArr[7] = memo;
					lineDataArr[8] = approver;
					lineDataArr[9] = approvetime;
					lineDataArr[10] = creator;
					lineDataArr[11] = createTime;

					// for (int j = 0; j < colOrder.length; j++) {
					// Object col = pivot.get(colOrder[j]);
					// lineDataArr[j] = Util.toSemiCharString(Util.trim(col));
					//
					// }
					// ~~~~~~~~~~~~~~~~~~~~~~~~~
					list.add(lineDataArr);
				}

				Set<Integer> numeric_idx = new HashSet<Integer>();
				char CSV_QUOTE = '"'; // char quoteChar = '\u0022';
				String replaceCrLf = "   ";
				boolean _QuoteMode_NON_NUMERIC = true;

				baos = new ByteArrayOutputStream();
				byte[] BOM_UTF8 = { (byte) 0xEF, (byte) 0xBB, (byte) 0xBF };
				baos.write(BOM_UTF8);
				writer = new BufferedWriter(new OutputStreamWriter(baos));
				createCSV(writer, list, numeric_idx, CSV_QUOTE, replaceCrLf,
						_QuoteMode_NON_NUMERIC);

				writer.close(); // 其 java API 註解： Closes the stream, flushing it
								// first.

				String filename = CapDate.getCurrentDate("yyyy-MM")
						+ "全行地政士黑名單";
				DocFile file = new DocFile();
				file.setMainId(batchtbl.getMainId());
				file.setData(baos.toByteArray());
				file.setCrYear(CapDate.getCurrentDate("yyyy"));
				file.setFieldId("rpt");
				file.setSrcFileName(filename + ".csv");
				file.setUploadTime(CapDate.getCurrentTimestamp());
				file.setBranchId(batchtbl.getBranch());
				file.setContentType("text/csv");
				file.setSysId("LMS");
				docFileService.save(file);
				file = docFileDao.find(file);
				return file.getOid();
			} catch (Exception ex) {
				logger.error("[getContent] Exception!!", ex);
			} finally {
				IOUtils.closeQuietly(writer);
				IOUtils.closeQuietly(baos);
			}
		}
		return null;

	}

	/**
	 * <pre>
	 * 建立 Excel
	 * 
	 * @param String 
	 * 				報表名稱
	 * @param List<Map<String,Object>> 
	 * 				資料
	 * @param LMSBATCH settings
	 * @param int
	 * 				起始row number
	 * @param String
	 * 				需加總column名
	 * @param Map<String,String> key=colNM,value=order
	 * 				Column順序
	 * @param int
	 * 				金額單位
	 * 
	 * @return boolean
	 * 是否成功
	 */
	@SuppressWarnings("deprecation")
	@Override
	public String createExcel(String rptName, List<Map<String, Object>> data,
			LMSBATCH batchtbl, int firstRow, String countRow,
			String[] colOrder, int base, int zoom, boolean isTransverse) {
		if (Util.isNotEmpty(data)) {
			ByteArrayOutputStream baos = null;
			FileOutputStream writer = null;
			try {
				baos = new ByteArrayOutputStream();
				String tempPath = PropUtil.getProperty("loadFile.dir")
						+ "excel/" + batchtbl.getRptNo() + ".xls";
				File xls = new File(Thread.currentThread()
						.getContextClassLoader().getResource(tempPath).toURI());
				Workbook wbook = Workbook.getWorkbook(xls);
				// WriteAccessRecord中有用到arraycopy，但長度寫死，
				// 導致write會出錯(ArrayIndexOutOfBoundsException)，加上settings便可解決
				WorkbookSettings settings = new WorkbookSettings();
				settings.setWriteAccess(null);

				WritableWorkbook book = Workbook.createWorkbook(baos, wbook,
						settings);
				WritableSheet sheet = book.getSheet(0);
				// 改成橫式
				SheetSettings setting = sheet.getSettings();
				setting.setPaperSize(PaperSize.A4);
				// 設定列印標題
				setting.setPrintTitlesRow(0, firstRow - 2);
				// 設定邊界
				setting.setHeaderMargin(0);
				setting.setFooterMargin(0);
				setting.setTopMargin(0);
				setting.setBottomMargin(0);
				// 自動調整頁寬
				setting.setFitWidth(1);

				if (isTransverse)
					setting.setOrientation(PageOrientation.LANDSCAPE);

				// --------------特殊格式----------
				// 無外框，標楷，10大小
				WritableFont font10Style = new WritableFont(
						WritableFont.createFont("標楷體"), 10,
						WritableFont.NO_BOLD, false);
				WritableCellFormat justFont = new WritableCellFormat(
						font10Style);
				// 無外框，標楷，12大小
				WritableFont font12Style = new WritableFont(
						WritableFont.createFont("標楷體"), 12,
						WritableFont.NO_BOLD, false);
				WritableCellFormat just12Font = new WritableCellFormat(
						font12Style);
				// 無外框，置右，12大小
				WritableCellFormat toRight = new WritableCellFormat(font12Style);
				toRight.setAlignment(Alignment.RIGHT);
				// 有外框，置中，標楷，10大小
				// WritableCellFormat withBorder
				// =LMSUtil.setCellFormat(justFont,fontStyle, Alignment.CENTRE);
				// 有外框，置左，標楷，10大小
				WritableCellFormat withBorderLeft = LMSUtil.setCellFormat(
						justFont, font10Style, Alignment.LEFT);
				// 純外框，置右，數字用(標楷+common後太驚悚...
				WritableCellFormat numCell = new WritableCellFormat();
				numCell.setVerticalAlignment(VerticalAlignment.CENTRE); // 若註解掉這行,
																		// 會align靠底部
				numCell.setAlignment(Alignment.RIGHT);
				numCell.setWrap(true);// 自動換行
				numCell.setBorder(jxl.format.Border.ALL,
						jxl.format.BorderLineStyle.THIN);
				// --------------內容--------------
				BigDecimal sumup = new BigDecimal(0);
				sumup.setScale(0, BigDecimal.ROUND_HALF_UP);
				BigDecimal baseOn = new BigDecimal(String.valueOf(base));
				firstRow--;// JXL從0開始
				// 表頭
				Properties pro = MessageBundleScriptCreator
						.getComponentResource(LMS9511V01Page.class);
				DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
				Label head = new Label(2, 1, batchtbl.getBranch(), just12Font);
				sheet.addCell(head);
				sheet.mergeCells(2, 2, 4, 2);
				head = new Label(2, 2, format.format(batchtbl.getBgnDate())
						+ " 至 " + format.format(batchtbl.getEndDate()),
						just12Font);
				sheet.addCell(head);
				sheet.mergeCells(colOrder.length - 2, 2, colOrder.length - 1, 2);
				head = new Label(colOrder.length - 2, 2, "單位："
						+ pro.getProperty("TWD")
						+ pro.getProperty(String.valueOf(base)), toRight);
				sheet.addCell(head);

				Cell title = sheet.getCell(0, 0);
				head = new Label(0, 0, branchService.getBranchName(batchtbl
						.getBranch()) + " " + title.getContents());
				head.setCellFormat(title.getCellFormat());
				sheet.addCell(head);

				for (int i = 0; i < data.size(); i++) {
					Map<String, Object> pivot = data.get(i);
					for (int j = 0; j < colOrder.length; j++) {
						Object col = pivot.get(colOrder[j]);
						if (col instanceof BigDecimal) {
							BigDecimal colnum = Util.parseBigDecimal(col);
							colnum.setScale(0, BigDecimal.ROUND_HALF_UP);
							if (countRow.equalsIgnoreCase(colOrder[j])) {
								sumup = sumup.add(colnum);
							}
							Label label = new Label(j, firstRow + i,
									NumConverter.addComma(colnum.divide(baseOn,
											BigDecimal.ROUND_HALF_UP)), numCell);
							sheet.addCell(label);
						} else {
							Label label = new Label(j, firstRow + i,
									HtmlUtils.htmlEscape(Util
											.toSemiCharString(Util
													.nullToSpace(col))),
									withBorderLeft);
							sheet.addCell(label);
						}
					}

				}
				// --------------合計--------------
				int totalRow = data.size();
				int displaySumRow = data.size();
				// 2013-09-04調整報表合計筆數誤加了從債務人
				/*
				 * 可能 保證人 1 R223XXXXXX 王OO 學生並於父親公司兼職實習 A B 2 F227XXXXXX 汪OO
				 * 安侯企業管理股份有限公司顧問師 C
				 * 
				 * totalRow 會有 3 筆 但在合併欄位
				 * 
				 * 顯示合計：2筆，共N仟元
				 */
				if (data.size() > 0) {
					for (int i = data.size() - 1; i >= 0; i--) {
						Map<String, Object> map = data.get(i);
						String strNum = Util.trim(map.get("NUM"));
						if (CapString.isNumeric(strNum)) {
							displaySumRow = Integer.parseInt(strNum);
							break;
						}
					}
				}
				// 合併單元格
				sheet.mergeCells(0, firstRow + totalRow, colOrder.length - 1,
						firstRow + totalRow);
				Label total = new Label(0, firstRow + totalRow,
						pro.getProperty("total")
								+ NumConverter.addComma(displaySumRow)
								+ pro.getProperty("total2")
								+ NumConverter.addComma(sumup.divide(baseOn,
										BigDecimal.ROUND_HALF_UP))
								+ pro.getProperty(String.valueOf(base)),
						withBorderLeft);
				sheet.addCell(total);
				firstRow++;// 推移

				// --------------備註欄--------------
				String rmk = pro.getProperty(batchtbl.getRptNo() + "_remark");
				if (Util.isNotEmpty(rmk)) {

					/* 20130702 修改(授權內)敘做房屋貸款月報的common */
					if (UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報
							.equals(batchtbl.getRptNo())) {
						format = new SimpleDateFormat("yyyy-MM-%");
						String typeStr = batchtbl.getRemarks().split(";")[0];
						int dateType = Util.parseInt(typeStr.split("=")[1]);
						final String[] dates = { "approveTime", "useStartDate",
								"lnStartDate" };
						Map<String, Object> monthTotal = eloandbBaseService
								.monthTotal(batchtbl.getBranch(),
										dates[dateType],
										format.format(batchtbl.getBgnDate()));
						BigDecimal groupAmt = Util.parseBigDecimal(monthTotal
								.get("AMT"));
						groupAmt = groupAmt.divide(Util.parseBigDecimal(base),
								BigDecimal.ROUND_HALF_DOWN);
						/*
						 * R03_remark2=已報准敘做整批房貸案本月動用 1000=仟元
						 */
						rmk = pro.getProperty("R03_remark2")
								+ " "
								+ NumConverter
										.addComma(monthTotal.get("COUNT"))
								+ " " + pro.getProperty("total2") + " "
								+ NumConverter.addComma(groupAmt) + " "
								+ pro.getProperty(String.valueOf(base)) + "\n"
								+ rmk;
					}
					/* 0702 end */

					// 合併後無法自動調整列高....
					sheet.setRowView(
							firstRow + totalRow,
							sheet.getRowHeight(firstRow + totalRow)
									* rmk.split("\n").length);
					sheet.mergeCells(0, firstRow + totalRow,
							colOrder.length - 1, firstRow + totalRow);// 合併

					Label remark = new Label(0, firstRow + totalRow, rmk,
							withBorderLeft);// 塞值
					sheet.addCell(remark);
					firstRow++;// 推移
				}
				// --------------簽章欄--------------
				// 合併
				sheet.mergeCells(0, firstRow + totalRow, colOrder.length - 1,
						firstRow + totalRow);
				// 產空白撐開間距
				String space = "        ";// 8空白
				for (int s = 0; s < colOrder.length * 0.33; s++) {
					space += "        ";
				}
				Label signature = new Label(0, firstRow + totalRow,
						pro.getProperty("tblCreater") + space
								+ pro.getProperty("tblApprover") + space
								+ pro.getProperty("tblCompetent"), justFont);
				sheet.addCell(signature);
				firstRow += 2;// 推移
				// --------------報表亂碼--------------
				Label randomCode = new Label(colOrder.length - 1, firstRow
						+ totalRow, pro.getProperty("randomCode")
						+ IDGenerator.getRandomCode(), toRight);
				sheet.addCell(randomCode);
				firstRow++;// 推移
				// --------------完成--------------
				book.write();
				book.close();

				DocFile file = new DocFile();
				file.setMainId(batchtbl.getMainId());
				file.setData(baos.toByteArray());
				file.setCrYear(CapDate.getCurrentDate("yyyy"));
				file.setFieldId("rpt");
				file.setSrcFileName(rptName + ".xls");
				file.setUploadTime(CapDate.getCurrentTimestamp());
				file.setBranchId(batchtbl.getBranch());
				file.setContentType("application/msexcel");
				file.setSysId("LMS");
				docFileService.save(file);
				file = docFileDao.find(file);
				return file.getOid();
			} catch (Exception ex) {
				logger.error("[getContent] Exception!!", ex);
			} finally {
				if (writer != null) {
					try {
						writer.close();
					} catch (IOException ex) {
						logger.error("[getContent] Exception!!", ex);
					}
				}
				if (baos != null) {
					try {
						baos.close();
					} catch (IOException ex) {
						logger.error("[getContent] Exception!!", ex);
					}
				}
			}

		}
		return null;
	}

	/**
	 * <pre>
	 * 產生 已核准尚未撥款案件報表 Excel
	 * 
	 * @param LMSBATCH settings
	 * @return List<Map<String, Object>> data
	 */
	@Override
	public String buildCLS180R11Data(LMSBATCH batchtbl) {
		/* 流程:l784m01a=>l784a01a=>excel x 5 */
		String[] fileName = { "4-1-1", "4-1-2", "4-1-3", "4-2-1", "4-3-1" };
		// 讀取的行名稱
		String[][] readCols = {
				{ "", "", "LNFE0600_LS_BAL", "LNFE0600_BAL",
						"LNFE0600_USE_AMT", "LNFE0600_NEW_BAL",
						"LNFE0600_NEW_CNT", "" },
				{ "", "", "LNFE0600_LS_BAL", "LNFE0600_BAL",
						"LNFE0600_NEW_BAL", "LNFE0600_NEW_CNT", "" },
				{ "", "", "LNFE0600_AVG_INTRT", "LNFE0600_NEW_INTRT",
						"LNFE0600_LOAN_RATE", "LNFE0600_RT_TERM" },
				{ "", "", "LNFE0600_NEW_CNT", "LNFE0600_OV_BAL",
						"LNFE0600_NEW_BAL", "LNFE0600_OB_BAL",
						"LNFE0600_OV_CNT" },
				{ "", "", "LNFE0600_LS_BAL", "LNFE0600_LS_BAL",
						"LNFE0600_USE_AMT", "LNFE0600_NEW_BAL",
						"LNFE0600_NEW_CNT", "" } };
		// 各表行數
		int[] cols = { 8, 7, 6, 7, 8 };
		Map<String, String> sites = codetypeService
				.findByCodeType("lnfe0600_site1no");
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		String mainId = batchtbl.getMainId();

		// 判斷季別
		DateFormat format = new SimpleDateFormat("MM");
		String season;
		int month = Util.parseInt(format.format(batchtbl.getDataDate()));
		if (month >= 1 && month <= 3) {
			season = pop.getProperty("season4");
		} else if (month >= 4 && month <= 6) {
			season = pop.getProperty("season1");
		} else if (month >= 7 && month <= 9) {
			season = pop.getProperty("season2");
		} else {
			season = pop.getProperty("season3");
		}
		List<DocFile> files = docFileDao.findByMainIdAndFieldId(mainId, "rpt");
		boolean newRecord = (files == null || files.size() == 0);
		if (files != null && files.size() != 5) {// 使用者更動過資料就全刪重新建立
			docFileService.deleteByMainId(mainId);
			newRecord = true;
		}
		DocFile fileRecord = null;
		for (int i = 0; i < fileName.length; i++) {
			ByteArrayOutputStream baos = null;
			List<Map<String, Object>> list = misService
					.getCLS180R11Data(fileName[i]);
			if (newRecord) {
				fileRecord = new DocFile();
				fileRecord.setMainId(mainId);
				fileRecord.setCrYear(CapDate.getCurrentDate("yyyy"));
				fileRecord.setFieldId("rpt");
				fileRecord.setSrcFileName(fileName[i]
						+ pop.getProperty(fileName[i]) + ".xls");
				fileRecord.setUploadTime(CapDate.getCurrentTimestamp());
				fileRecord.setBranchId(batchtbl.getBranch());
				fileRecord.setContentType("application/msexcel");
				fileRecord.setSysId("LMS");
			} else {
				fileRecord = files.get(i);
			}
			try {
				baos = new ByteArrayOutputStream();

				WritableWorkbook book = Workbook.createWorkbook(baos);
				WritableSheet sheet = book.createSheet("1", 0);

				// --------------格式----------
				// 無外框，標楷，12大小
				WritableFont fontStyle = new WritableFont(
						WritableFont.createFont("標楷體"), 12,
						WritableFont.NO_BOLD, false);
				WritableCellFormat justFont = new WritableCellFormat(fontStyle);
				// 有外框，標楷，12大小
				WritableCellFormat withBorder = LMSUtil.setCellFormat(justFont,
						fontStyle, Alignment.CENTRE);
				// 純外框，數字用(標楷+common後太驚悚...
				WritableCellFormat numCell = new WritableCellFormat();
				numCell.setVerticalAlignment(VerticalAlignment.CENTRE);
				numCell.setAlignment(Alignment.RIGHT);
				numCell.setWrap(true);// 自動換行
				numCell.setBorder(jxl.format.Border.ALL,
						jxl.format.BorderLineStyle.THIN);

				// --------------標題列--------------
				for (int j = 0; j < cols[i]; j++) {
					Label labelT1 = new Label(j, 0, pop.getProperty(fileName[i]
							+ j), withBorder);
					sheet.addCell(labelT1);
					sheet.setColumnView(j, 20);
				}
				// --------------內容--------------
				for (int y = 0; y < list.size(); y++) {
					Map<String, Object> pivot = list.get(y);
					Label dataDate = new Label(0, y + 1, season, withBorder);
					sheet.addCell(dataDate);
					Label site = new Label(1, y + 1, sites.get(pivot
							.get("LNFE0600_SITE1NO")), withBorder);
					sheet.addCell(site);
					for (int x = 2; x < cols[i] - 1; x++) {
						Label label = new Label(
								x,
								y + 1,
								NumConverter.addComma(pivot.get(readCols[i][x])),
								withBorder);
						sheet.addCell(label);
					}
					if (i == 2 || i == 3) {// 不加總
						Label label = new Label(cols[i] - 1, y + 1,
								NumConverter.addComma(pivot
										.get(readCols[i][cols[i] - 1])),
								numCell);
						sheet.addCell(label);
					} else {
						BigDecimal result = Util.parseBigDecimal(
								pivot.get("LNFE0600_NEW_BAL")).setScale(5,
								BigDecimal.ROUND_HALF_UP);
						BigDecimal division = Util.parseBigDecimal(pivot
								.get("LNFE0600_NEW_CNT"));
						if (result.intValue() != 0 && division.intValue() != 0) {
							result = result.divide(division,
									BigDecimal.ROUND_HALF_DOWN);
						} else {
							result = Util.parseBigDecimal(0);
						}
						Label sum = new Label(cols[i] - 1, y + 1,
								NumConverter.addComma(result), numCell);
						sheet.addCell(sum);
					}
				}
				book.write();
				book.close();
				fileRecord.setData(baos.toByteArray());
				docFileService.save(fileRecord);
			} catch (Exception ex) {
				logger.error("[getContent] Exception!!", ex);
			} finally {
				if (baos != null) {
					try {
						System.out.print("close");
						baos.close();
					} catch (IOException ex) {
						logger.error("[getContent] Exception!!", ex);
					}
				}
			}
		}

		return mainId;
	}

	@SuppressWarnings("unchecked")
	@Override
	public int cheaksendTime(LMSRPT lmsRpt) {
		int check = 0;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (lmsRpt != null) {
			if (lmsRpt.getSendTime() == null) {
				if ("CLS".equals(lmsRpt.getRptNo().substring(0, 3))) {
					lmsRpt.setSender(user.getUserId());
					lmsRpt.setSendTime(CapDate.getCurrentTimestamp());
					this.save(lmsRpt);
					// 產生授管處的record
					LMSRPT send918 = new LMSRPT();
					List<LMSRPT> data = (List<LMSRPT>) findListByMainId(
							LMSRPT.class, lmsRpt.getMainId());
					for (LMSRPT record : data) {
						if (record.getBranch().equals(UtilConstants.BankNo.授管處)) {
							send918 = record;
							break;
						}
					}
					send918.setMainId(lmsRpt.getMainId());
					send918.setBgnDate(lmsRpt.getBgnDate());
					send918.setBthDate(lmsRpt.getBthDate());
					send918.setBranch(UtilConstants.BankNo.授管處);
					send918.setDataDate(lmsRpt.getDataDate());
					send918.setEndDate(lmsRpt.getEndDate());
					send918.setNowRpt(lmsRpt.getNowRpt());
					send918.setRandomCode(lmsRpt.getRandomCode());
					send918.setRemarks(lmsRpt.getRemarks());
					send918.setUpdater("SYSTEM");
					send918.setUpdateTime(lmsRpt.getUpdateTime());
					send918.setRptName(lmsRpt.getRptName());
					send918.setRptNo(lmsRpt.getRptNo());
					send918.setSender(user.getUserId());
					send918.setSendTime(CapDate.getCurrentTimestamp());
					send918.setReportOidFile(lmsRpt.getReportOidFile());
					this.save(send918);
				} else {
					// c.更新LMSRPT報表檔：
					lmsRpt.setSender(user.getUserId());
					lmsRpt.setSendTime(CapDate.getCurrentTimestamp());
					this.save(lmsRpt);
				}
			} else {
				// 已經傳送過
				check = 1;
			}
		} else {
			check = 2;
		}
		return check;
	}

	// @Override
	// public int cheaksendTime(List<LMSRPT> lmsRptList) {
	// int check = 0;
	// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	// for(LMSRPT lmsRpt : lmsRptList){
	// if(check != 1 && check != 2){
	// if (lmsRpt != null) {
	// if (lmsRpt.getSendTime() == null) {
	// // c.更新LMSRPT報表檔：
	// lmsRpt.setSender(user.getUserId());
	// lmsRpt.setSendTime(CapDate.getCurrentTimestamp());
	// } else {
	// // 已經傳送過
	// check = 1;
	// }
	// }else{
	// check = 2;
	// }
	// }
	// }
	// if(check != 1 && check != 2){
	// lmsRptDao.save(lmsRptList);
	// }
	//
	// return check;
	// }

	@Override
	public List<L784S07A> findL784S07AByMainId(String mainId)
			throws CapException {
		return l784s07aDao.findByMainId(mainId);
	}

	@Override
	public void savel784s07aList(List<L784S07A> list) {
		l784s07aDao.save(list);
	}

	@Override
	public void saveLmsRptList(List<LMSRPT> list) {
		lmsRptDao.save(list);
	}

	@Override
	public void savel180r02aList(List<L180R02A> list) {
		l180r02aDao.save(list);
	}

	@Override
	public void delete784s07(String year, String month, String mainId) {
		eloandbBaseService.deleteL784s07(year, month, mainId);
	}

	@Override
	public void findAndSaveLMS180R16Date(String userId, String apprYY,
			String apprMM, String caseDept, String mainId) {

		// 功能說明：月初由法金處產生「每月常董會報告事項彙總及申報案件數統計表」
		// 參數說明：YY,MM-資料年月, tBranchID- 產生該分行的資料(若為ALL表示產生所有分行的資料)
		// caseDept - 1.法金處案件 2. 個金處案件, 3:全部
		this.delete784s07(apprYY, apprMM, mainId);

		List<L784S07A> l784s07List = new ArrayList<L784S07A>();
		List<Map<String, Object>> rows = null;
		rows = misElcsecntService.findElcsecntforType7ByAllBrNoAndDate(apprYY,
				apprMM);
		List<IBranch> branchList = lmsService.getBranchList();
		List<IBranch> branchList2 = new LinkedList<IBranch>();
		for (Map<String, Object> dataMap : rows) {
			L784S07A l784s07a = new L784S07A();
			// 新作
			BigDecimal tCitem1 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM1REC"));
			BigDecimal tCitem1Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM1AMT"));
			// 續約
			BigDecimal tCitem2 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM2REC"));
			BigDecimal tCitem2Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM2AMT"));
			// 變更條件
			BigDecimal tCitem3 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM3REC"));
			BigDecimal tCitem3Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM3AMT"));
			// 合計

			// 無擔保授信
			BigDecimal tCitem4 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM4REC"));
			BigDecimal tCitem4Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM4AMT"));
			// 擔保授信
			BigDecimal tCitem5 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM5REC"));
			BigDecimal tCitem5Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM5AMT"));
			// 申報案件
			BigDecimal tCitem6 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM6REC"));
			BigDecimal tCitem6Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM6AMT"));
			// 核准案件
			BigDecimal tCitem7 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM7REC"));
			BigDecimal tCitem7Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM7AMT"));
			// 授權內案件
			BigDecimal tCitem8 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM8REC"));
			BigDecimal tCitem8Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM8AMT"));
			// 提會案件
			BigDecimal tCitem9 = LMSUtil
					.toBigDecimal(dataMap.get("tCITEM9REC"));
			BigDecimal tCitem9Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM9AMT"));
			// 常董會案件
			BigDecimal tCitem10 = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM10REC"));
			BigDecimal tCitem10Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM10AMT"));
			// 覆審案件
			BigDecimal tCitem11 = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM11REC"));
			BigDecimal tCitem11Amt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM11AMT"));

			// J-108-0242_05097_B1001 Web
			// e-Loan每月常董會報告事項彙總及申報案件數統計表新做案件之筆數統計再區分為新戶及原授信戶
			// 新作新客戶(筆數)
			BigDecimal tCitem1New = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM1NEWREC"));
			// 新做新客戶(金額)
			BigDecimal tCitem1NewAmt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM1NEWAMT"));

			// 新做舊客戶(筆數)
			BigDecimal tCitem1Old = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM1OLDREC"));
			// 新做舊客戶(金額)
			BigDecimal tCitem1OldAmt = LMSUtil.toBigDecimal(dataMap
					.get("tCITEM1OLDAMT"));

			l784s07a.setBrNo(Util.trim(dataMap.get("BRNO")));
			l784s07a.setMainId(mainId);
			l784s07a.setApprMM(apprMM);
			l784s07a.setApprYY(apprYY);
			l784s07a.setCaseDept(Util.trim(dataMap.get("CASEDEPT")));
			l784s07a.setCItem1Rec(tCitem1 == null ? null : tCitem1.intValue());
			l784s07a.setCItem1Amt(tCitem1Amt);
			l784s07a.setCItem2Rec(tCitem2 == null ? null : tCitem2.intValue());
			l784s07a.setCItem2Amt(tCitem2Amt);
			l784s07a.setCItem3Rec(tCitem3 == null ? null : tCitem3.intValue());
			l784s07a.setCItem3Amt(tCitem3Amt);
			l784s07a.setCItem4Rec(tCitem4 == null ? null : tCitem4.intValue());
			l784s07a.setCItem4Amt(tCitem4Amt);
			l784s07a.setCItem5Rec(tCitem5 == null ? null : tCitem5.intValue());
			l784s07a.setCItem5Amt(tCitem5Amt);
			l784s07a.setCItem6Rec(tCitem6 == null ? null : tCitem6.intValue());
			l784s07a.setCItem6Amt(tCitem6Amt);
			l784s07a.setCItem7Rec(tCitem7 == null ? null : tCitem7.intValue());
			l784s07a.setCItem7Amt(tCitem7Amt);
			l784s07a.setCItem8Rec(tCitem8 == null ? null : tCitem8.intValue());
			l784s07a.setCItem8Amt(tCitem8Amt);
			l784s07a.setCItem9Rec(tCitem9 == null ? null : tCitem9.intValue());
			l784s07a.setCItem9Amt(tCitem9Amt);
			l784s07a.setCItem10Rec(tCitem10 == null ? null : tCitem10
					.intValue());
			l784s07a.setCItem10Amt(tCitem10Amt);
			l784s07a.setCItem11Rec(tCitem11 == null ? null : tCitem11
					.intValue());
			l784s07a.setCItem11Amt(tCitem11Amt);
			l784s07a.setCreateTime(CapDate.getCurrentTimestamp());
			l784s07a.setCreator(userId);
			l784s07a.setUpdateTime(CapDate.getCurrentTimestamp());
			l784s07a.setUpdater(userId);

			// J-108-0242_05097_B1001 Web
			// e-Loan每月常董會報告事項彙總及申報案件數統計表新做案件之筆數統計再區分為新戶及原授信戶
			l784s07a.setCItem1NewRec(tCitem1New == null ? null : tCitem1New
					.intValue());
			l784s07a.setCItem1NewAmt(tCitem1NewAmt);
			l784s07a.setCItem1OldRec(tCitem1Old == null ? null : tCitem1Old
					.intValue());
			l784s07a.setCItem1OldAmt(tCitem1OldAmt);

			l784s07List.add(l784s07a);
			branchList2.add(branchService.getBranch(l784s07a.getBrNo()));
		}// while end
			// 一次儲存多筆
		branchList.removeAll(branchList2);
		for (IBranch branch : branchList) {
			L784S07A l784s07a = new L784S07A();
			l784s07a.setBrNo(Util.trim(branch.getBrNo()));
			l784s07a.setMainId(mainId);
			l784s07a.setApprMM(apprMM);
			l784s07a.setApprYY(apprYY);
			l784s07a.setCaseDept(caseDept);
			l784s07a.setCreateTime(CapDate.getCurrentTimestamp());
			l784s07a.setCreator(userId);
			l784s07a.setUpdateTime(CapDate.getCurrentTimestamp());
			l784s07a.setUpdater(userId);
			l784s07List.add(l784s07a);
		}
		this.savel784s07aList(l784s07List);
		// return l784s07List;

	}

	@Override
	public void importLMS180R16Data(Date dataDate, Date dataStartDate) {
		// listName = "pdf7";
		LMSRPT lmsRpt = null;
		List<LMSRPT> lmsRptList = lmsRptDao.findByIndex04(
				UtilConstants.BankNo.授管處,
				UtilConstants.RPTREPORT.DOCTYPE1.常董會報告事項彙總及申報案件數統計表, dataDate,
				null);
		if (lmsRptList.size() > 0) {
			lmsRpt = lmsRptList.get(0);
		} else {
			lmsRpt = new LMSRPT();
		}
		// 查詢ELCSECNT再新增至L784S07A
		this.findAndSaveLMS180R16Date(lmsRpt.getUpdater(),
				TWNDate.toTW(dataStartDate).split("/")[0],
				TWNDate.toTW(dataStartDate).split("/")[1],
				UtilConstants.Casedoc.DocType.企金個金,
				Util.trim(lmsRpt.getMainId()));

	}

	@Override
	public CapAjaxFormResult getProduct() {
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<C900M01A> data = c900m01aDao.find(c900m01aDao
				.createSearchTemplete());
		if (data != null) {
			for (int i = 0; i < data.size(); i++) {
				C900M01A pivot = data.get(i);
				String prodFullName = (Util.isEmpty(pivot.getProdNm2())) ? pivot
						.getProdNm1() : pivot.getProdNm1() + "-"
						+ pivot.getProdNm2();
				result.set(pivot.getProdKind(), prodFullName);
			}
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS9511V01Page.class);
			result.set("%", pop.getProperty("ALL"));
		}
		return result;

	}

	/**
	 * CUSTDATA(客戶基本資料檔)找 CNAME(中文戶名)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */

	@SuppressWarnings("rawtypes")
	private String findCustData(String custId, String dupNo) {
		String cname = "";
		Map dataMap = misCustdataService.findCustdataSelCname(custId, dupNo);
		if (dataMap != null) {
			cname = Util.trim(dataMap.get("CNAME"));
		}
		return cname;
	}

	@Override
	public DocFile saveDocFile(String brNo, String mainId, String listName,
			String fileExtName) {
		List<DocFile> docFileList = this.findDocFile(mainId, listName);
		String filename = LMSUtil.getUploadFilePath(brNo, mainId, listName);
		File file = new File(filename);
		if (!file.exists()) {
			file.mkdirs();
		} else {
			for (DocFile docFile : docFileList) {
				docFile.setDeletedTime(null);
			}
			docFileDao.save(docFileList);
		}
		DocFile docFile = new DocFile();
		docFile.setBranchId(brNo);
		if ("pdf".equalsIgnoreCase(listName)) {
			docFile.setContentType("application/mspdf");
		} else if ("xls".equalsIgnoreCase(listName)) {
			docFile.setContentType("application/msexcel");
		}
		docFile.setMainId(mainId);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(listName);
		docFile.setSrcFileName(listName + "." + fileExtName);
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(docFileService.getSysId());
		docFile.setData(new byte[] {});
		docFileService.save(docFile, false);
		return docFile;
	}

	@Override
	public List<DocFile> findDocFile(String mainId, String FieldId) {
		List<DocFile> docFile = docFileDao.findByMainIdAndFieldId(mainId,
				FieldId);
		return docFile;

	}

	/**
	 * J-108-0192_05097_B1001 Web e-Loan企金授信新增每季海外營業單位授信報案考核彙總表
	 * 
	 * type9-營業單位授信報案考核彙總表 抓全年跟抓指定月份
	 * 
	 * @param TWYM_START
	 *            本月/本季起始年月
	 * @param TWYM_END
	 *            本月/本季結束年月
	 * @param startTWYM
	 *            開始年月
	 * @param endTWYM
	 *            結束年月
	 * @param type
	 *            排序方式不同
	 * @return
	 * @throws CapException
	 */
	@Override
	public List<Map<String, Object>> findType9BystartYMendYM(String TWYM_START,
			String TWYM_END, String startTWYM, String endTWYM, String type)
			throws CapException {
		List<Map<String, Object>> rows = dwdbService
				.findDW_ELINSPDT_sel9ByBRANCHIDCHKYMTYPCDYM(startTWYM, endTWYM,
						TWYM_START, TWYM_END, type);
		return rows;
	}

	@Override
	public boolean deleteFile(String id, boolean useMainId) {
		ISearch search = docFileDao.createSearchTemplete();
		String idName = useMainId ? "mainId" : "oid";
		search.addSearchModeParameters(SearchMode.EQUALS, idName, id);
		List<DocFile> list = docFileDao.find(search);
		for (DocFile record : list) {
			record.setDeletedTime(CapDate.getCurrentTimestamp());
			docFileDao.save(record);
		}

		return true;
	}

	@Override
	public LMSRPT findByLMSRPT(String mainId) {
		return lmsRptDao.findByIndex03(mainId);
	}

	@Override
	public List<LMSRPT> findLMSRptForRPTNO(String rptNo) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		ISearch search = lmsRptDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "rptNo", rptNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "branch",
				user.getUnitNo());
		search.addOrderBy("dataDate", true);
		return lmsRptDao.find(search);
	}

	@Override
	public List<LMSRPT> findFileGrid(String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		ISearch search = lmsRptDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "nowRpt", "Y");
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "branch",
				user.getUnitNo());
		return lmsRptDao.find(search);
	}

	@Override
	public List<Map<String, Object>> findApproverForL180R02A(String mainId) {
		List<Map<String, Object>> list = eloandbBaseService
				.findApproverForL180R02A(mainId);
		return list;
	}

	@SuppressWarnings("deprecation")
	@Override
	public Date getStartDateForLMSRPT(String dataStartDate) {
		Date bngDate = null;
		if (dataStartDate == null) {
			bngDate = null;
		} else if (dataStartDate.length() == 10) {
			bngDate = TWNDate.valueOf(dataStartDate);
		} else if (dataStartDate.length() == 7) {
			bngDate = lmsService.getDayOfMonth(dataStartDate.substring(0, 4),
					dataStartDate.substring(5, 7), true);
		} else if (dataStartDate.length() == 4) {
			bngDate = lmsService.getDayOfMonth(dataStartDate, "1", true);
		} else {
			bngDate = null;
		}
		return bngDate;
	}

	@Override
	public Date getEndDateForLMSRPT(String dataEndDate) {
		Date endDate = null;
		if (dataEndDate == null) {
			endDate = null;
		} else if (dataEndDate.length() == 10) {
			endDate = TWNDate.valueOf(dataEndDate);
		} else if (dataEndDate.length() == 7) {
			endDate = TWNDate.valueOf(CrsUtil.getDataEndDate(StringUtils
					.substring(dataEndDate, 0, 4)
					+ "-"
					+ StringUtils.substring(dataEndDate, 5, 7)));
		} else if (dataEndDate.length() == 4) {
			endDate = TWNDate.valueOf(CrsUtil.getDataEndDate(dataEndDate
					+ "-12"));
		} else {
			endDate = null;
		}
		return endDate;
	}

	@Override
	public CapAjaxFormResult checkBatchData(CapAjaxFormResult result,
			String rptNo, String remark, Date bngDate, Date endDate,
			Properties prop) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Calendar calendar = Calendar.getInstance();
		// 檢核營運中心已敘做案件清單資料是否符合重新產生
		if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單)) {
			boolean checkAResult = this.checkL180R02AData(user.getUnitNo(),
					rptNo, bngDate, endDate);

			if (!checkAResult) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						prop.getProperty("checkData.number01"));
				result.set("SUCCESS", "N");
				return result;
			}
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.常董會報告事項彙總及申報案件數統計表)) {
			int year = Integer.parseInt(TWNDate.toAD(bngDate).split("-")[0]);
			if (year > calendar.get(Calendar.YEAR)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						prop.getProperty("checkData.number02"));
				result.set("SUCCESS", "N");
				return result;
			}
			List<LMSRPT> lmsRptList = this.findLMSRptForRPTNO(rptNo);
			for (LMSRPT lmsRpt : lmsRptList) {
				int year2 = Integer.parseInt(TWNDate.toAD(lmsRpt.getDataDate())
						.split("-")[0]);
				if (year == year2) {
					result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
							prop.getProperty("checkData.number03"));
					result.set("SUCCESS", "N");
					return result;
				}
			}
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單)
				|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單)) {
			boolean checkAResult = this
					.checkL180R01Data(remark, user.getUnitNo(),
							user.getUserId(), rptNo, bngDate, endDate);
			if (!checkAResult) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						prop.getProperty("checkData.number04"));
				result.set("SUCCESS", "N");
				return result;
			} else {

			}
		}
		return result;
	}

	private String getC160M01A_caseType(Object caseType) {
		if (Util.equals(caseType, "2")) {
			return "*"; // *表示該件為團貸案件
		} else if (Util.equals(caseType, "3")) {
			return "@"; // @表示為整批匯入案件
		} else {
			return "";
		}
	}

	@Override
	public String buildCLS250R01Xls(LMSBATCH batchtbl) {
		String tBranch = batchtbl.getBranch();
		WritableCellFormat format12Right = null;
		WritableCellFormat format12Left = null;
		WritableCellFormat format12BCenter = null;
		WritableCellFormat format12BLeft = null;
		WritableCellFormat format12BRight = null;
		WritableCellFormat fmt_th_BorderLeft = null;

		WritableCellFormat fmt_td_BorderLeft = null;
		WritableCellFormat fmt_td_BorderRight = null;

		WritableCellFormat format8Left = null;

		List<Map<String, Object>> data = null;
		Date begDate = batchtbl.getBgnDate();
		Date endDate = batchtbl.getEndDate();
		String tBegDate = new SimpleDateFormat("yyyyMM").format(begDate);
		String tEndDate = new SimpleDateFormat("yyyyMM").format(endDate);
		String dataRange = batchtbl.getRptNo().substring(9, 10);
		Boolean tAll = true;

		// ===========================================
		// 在 SQL 查詢時，未加上 brNo 的條件
		// 但之後，用 Java 程式去控制
		// 9開頭的總處單位，可看到{全部分行}的資料
		// 非總處單位，只可看到{該分行}的資料
		if ("A".equals(dataRange)) {
			tAll = false;
			data = misdbBASEService.findELF516_forRptDataNoEdit();
		} else if ("B".equals(dataRange)) {
			data = misdbBASEService.findELF516_forRptDataEdit(tBegDate,
					tEndDate);
		} else if ("C".equals(dataRange)) {
			data = misdbBASEService
					.findELF516_forRptDataAll(tBegDate, tEndDate);
		}
		Map<String, String> lnFlag_map = codetypeService
				.findByCodeType("C250M01A_lnFlag");

		int headShift = 4;
		DateFormat format = new SimpleDateFormat("yyyy-MM-dd");

		ByteArrayOutputStream baos = null;
		try {
			// 字型設定
			WritableFont font12B = new WritableFont(
					WritableFont.createFont("新細明體"), 12, WritableFont.BOLD);
			WritableFont font12 = new WritableFont(
					WritableFont.createFont("新細明體"), 12);
			WritableFont font14 = new WritableFont(
					WritableFont.createFont("新細明體"), 14);
			WritableFont font_th = new WritableFont(
					WritableFont.createFont("新細明體"), 9);
			format12BCenter = LMSUtil.setCellFormat(format12BCenter, font12B,
					Alignment.CENTRE, false);
			format12BLeft = LMSUtil.setCellFormat(format12BLeft, font12B,
					Alignment.LEFT, false);
			format12BRight = LMSUtil.setCellFormat(format12BRight, font12B,
					Alignment.RIGHT, false);
			format12Right = LMSUtil.setCellFormat(format12Right, font12,
					Alignment.RIGHT, false);
			format12Left = LMSUtil.setCellFormat(format12Left, font12,
					Alignment.LEFT, false);
			fmt_th_BorderLeft = LMSUtil.setCellFormat(fmt_th_BorderLeft,
					font_th, Alignment.LEFT, true);
			fmt_td_BorderLeft = LMSUtil.setCellFormat(fmt_td_BorderLeft,
					font14, Alignment.LEFT, true);
			fmt_td_BorderRight = LMSUtil.setCellFormat(fmt_td_BorderRight,
					font14, Alignment.RIGHT, true);

			format8Left = LMSUtil.setCellFormat(format8Left, font_th,
					Alignment.LEFT, false);

			baos = new ByteArrayOutputStream();
			String tempPath = PropUtil.getProperty("loadFile.dir")
					+ "excel/CLS250R01.xls";
			File xls = new File(Thread.currentThread().getContextClassLoader()
					.getResource(tempPath).toURI());
			Workbook wbook = Workbook.getWorkbook(xls);
			// WriteAccessRecord中有用到arraycopy，但長度寫死，
			// 導致write會出錯(ArrayIndexOutOfBoundsException)，加上settings便可解決
			WorkbookSettings settings = new WorkbookSettings();
			settings.setWriteAccess(null);

			WritableWorkbook book = Workbook.createWorkbook(baos, wbook,
					settings);
			WritableSheet sheet = book.getSheet(0);
			// sheet.mergeCells(0, 1, 12, 1); //
			// 合并单元格（列、行）第一变量是列，第二变量是行--第三个变量是到第几列，第四变量是行
			// --------------特殊格式----------
			WritableFont fontStyle = new WritableFont(
					WritableFont.createFont("標楷體"), 12, WritableFont.NO_BOLD,
					false);
			WritableCellFormat cellformat = null;
			cellformat = LMSUtil.setCellFormat(cellformat, fontStyle,
					Alignment.LEFT);
			// sheet.setColumnView(col, width);設寬度
			// 無邊框
			WritableCellFormat justFont = new WritableCellFormat(fontStyle);

			SheetSettings setting = sheet.getSettings();
			setting.setPaperSize(PaperSize.A4);
			// setting.setScaleFactor(80);
			setting.setFitWidth(1); // 設定預覽列印與列印成為一頁, 寬度

			sheet.mergeCells(1, 2, 3, 2);
			sheet.setColumnView(0, 11);
			sheet.setColumnView(1, 5);
			sheet.setColumnView(2, 15);
			sheet.setColumnView(3, 20);
			sheet.setColumnView(4, 15);

			if (tAll) {
				sheet.mergeCells(0, 0, 10, 0);
				sheet.setColumnView(5, 20);

				int columnIdx = 5;
				if (true) {
					sheet.setColumnView(++columnIdx, 20); // 疑似代辦案件訊息-A
					sheet.setColumnView(++columnIdx, 20);
					sheet.setColumnView(++columnIdx, 20);
					sheet.setColumnView(++columnIdx, 20);
					sheet.setColumnView(++columnIdx, 20); // 疑似代辦案件訊息-E
				}

				sheet.setColumnView(++columnIdx, 8); // 經辦
				sheet.setColumnView(++columnIdx, 8); // 覆核人
				sheet.setColumnView(++columnIdx, 11); // 覆核日期
				sheet.setColumnView(++columnIdx, 20); // 查證結果
				setting.setOrientation(PageOrientation.LANDSCAPE); // 改成橫式
			} else {
				sheet.mergeCells(0, 0, 4, 0);
				setting.setOrientation(PageOrientation.PORTRAIT); // 改成直式
			}

			// --------------標題--------------
			// 表頭

			Properties pro = MessageBundleScriptCreator
					.getComponentResource(LMS9511V01Page.class);
			Label head = null;
			head = new Label(0, 1, pro.getProperty("CLS250R01.colTitle11"));
			sheet.addCell(head);
			head = new Label(0, 2, pro.getProperty("CLS250R01.colTitle12"));
			sheet.addCell(head);

			head = new Label(1, 1, batchtbl.getBranch());
			sheet.addCell(head);
			String tBrName = branchService.getBranchName(tBranch);

			if (tAll) {
				if ("B".equals(dataRange)) {
					tBrName = MessageFormat.format(
							pro.getProperty("CLS250R01.title01"), tBrName); // CLS250R01.title01={0}　疑似代辦案件已註記清單
				} else if ("C".equals(dataRange)) {
					tBrName = MessageFormat.format(
							pro.getProperty("CLS250R01.title02"), tBrName); // CLS250R01.title02={0}　疑似代辦案件全部清單
				}

				head = new Label(1, 2, format.format(batchtbl.getBgnDate())
						+ pro.getProperty("CLS250R01.colTitle13")
						+ format.format(batchtbl.getEndDate()));
				sheet.addCell(head);
			} else {
				tBrName = MessageFormat.format(
						pro.getProperty("CLS250R01.title00"), tBrName); // CLS250R01.title00={0}　疑似代辦案件未註記清單

				head = new Label(1, 2, pro.getProperty("CLS250R01.colTitle13")
						+ format.format(batchtbl.getEndDate()));
				sheet.addCell(head);
			}

			Label signature = null;
			signature = new Label(0, 0, tBrName, format12BCenter);
			sheet.addCell(signature);
			signature = new Label(0, 3,
					pro.getProperty("CLS250R01.colTitle00"), cellformat);// 資料年月
			sheet.addCell(signature);
			signature = new Label(1, 3,
					pro.getProperty("CLS250R01.colTitle01"), cellformat);// 性質
			sheet.addCell(signature);
			signature = new Label(2, 3,
					pro.getProperty("CLS250R01.colTitle02"), cellformat);// 統一編號
			sheet.addCell(signature);
			signature = new Label(3, 3,
					pro.getProperty("CLS250R01.colTitle03"), cellformat);// 戶名
			sheet.addCell(signature);
			signature = new Label(4, 3,
					pro.getProperty("CLS250R01.colTitle04"), cellformat);// 額度序號
			sheet.addCell(signature);

			if (tAll) {
				signature = new Label(5, 3,
						pro.getProperty("CLS250R01.colTitle05"), cellformat);// 帳號
				sheet.addCell(signature);

				// 疑似代辦案件訊息
				int columnIdx = 5;
				String[] lnFlag_arr = { "A", "B", "C", "D", "E", "F"};
				for (String lnFlag : lnFlag_arr) {
					++columnIdx;
					String columnName = LMSUtil.getDesc(lnFlag_map, lnFlag);
					signature = new Label(columnIdx, 3, columnName, cellformat);
					sheet.addCell(signature);
				}

				++columnIdx; // 經辦
				signature = new Label(columnIdx, 3,
						pro.getProperty("CLS250R01.colTitle08"), cellformat);
				sheet.addCell(signature);

				++columnIdx; // 覆核人
				signature = new Label(columnIdx, 3,
						pro.getProperty("CLS250R01.colTitle09"), cellformat);
				sheet.addCell(signature);

				++columnIdx; // 覆核日期
				signature = new Label(columnIdx, 3,
						pro.getProperty("CLS250R01.colTitle10"), cellformat);
				sheet.addCell(signature);

				++columnIdx; // 查證結果
				signature = new Label(columnIdx, 3,
						pro.getProperty("CLS250R01.colTitle15"), cellformat);
				sheet.addCell(signature);
				
				++columnIdx; // 產品種類
				signature = new Label(columnIdx, 3,
						pro.getProperty("CLS250R01.colTitle16"), cellformat);
				sheet.addCell(signature);
				
				++columnIdx; // 動撥日期
				signature = new Label(columnIdx, 3,
						pro.getProperty("CLS250R01.colTitle17"), cellformat);
				sheet.addCell(signature);
			}
			int tAllColumnCnt = 17;
			// --------------內容---------------
			int tCount = 0;
			if (data != null) {
				tCount = this._setCellData(tBranch, data, cellformat,
						headShift, sheet, pro, tAll, baos, lnFlag_map);
			}

			if (tCount == 0) {
				String tValue = "無資料";

				Label label = new Label(0, headShift + tCount, tValue,
						cellformat); // 資料年月
				sheet.addCell(label);

				if (tAll) {
					// 列出選項後， merge 由10→14
					sheet.mergeCells(0, headShift + tCount, tAllColumnCnt,
							headShift + tCount);
				} else {
					sheet.mergeCells(0, headShift + tCount, 4, headShift
							+ tCount);
				}
				tCount++;
			}

			// 簽章欄
			signature = new Label(0, headShift + tCount,
					pro.getProperty("tblCreater"), justFont);// 製表
			sheet.addCell(signature);
			signature = new Label(3, headShift + tCount,
					pro.getProperty("tblApprover"), justFont);// 覆核
			sheet.addCell(signature);
			signature = new Label(5, headShift + tCount,
					pro.getProperty("tblCompetent"), justFont);// 主管
			sheet.addCell(signature);
			// 報表亂碼
			WritableCellFormat toRight = new WritableCellFormat(fontStyle);
			toRight.setAlignment(Alignment.RIGHT);
			Label randomCode = new Label(
					7,
					headShift + tCount + 1,
					pro.getProperty("randomCode") + IDGenerator.getRandomCode(),
					toRight);// 製表
			sheet.addCell(randomCode);

			String C250M01A_MEMO = "";
			C250M01A_MEMO = pro.getProperty("C250M01A.MEMO01") + "\n"
					+ pro.getProperty("C250M01A.MEMO02") + "\n"
					+ pro.getProperty("C250M01A.MEMO03");

			signature = new Label(0, headShift + tCount + 2, C250M01A_MEMO,
					format8Left);// MEMO
			sheet.addCell(signature);

			if (tAll) {
				// 列出選項後， merge 由10→14
				sheet.mergeCells(0, headShift + tCount + 2, tAllColumnCnt,
						headShift + tCount + 2);
				sheet.setRowView(headShift + tCount + 2, 680);
			} else {
				sheet.mergeCells(0, headShift + tCount + 2, 4, headShift
						+ tCount + 2);
				sheet.setRowView(headShift + tCount + 2, 1300);
			}

			book.write();
			book.close();

			DocFile file = new DocFile();
			file.setMainId(batchtbl.getMainId());
			file.setData(baos.toByteArray());
			file.setCrYear(CapDate.getCurrentDate("yyyy"));
			file.setFieldId("rpt");
			file.setSrcFileName(batchtbl.getRptName() + ".xls");
			file.setUploadTime(CapDate.getCurrentTimestamp());
			file.setBranchId(batchtbl.getBranch());
			file.setContentType("application/msexcel");
			file.setSysId("LMS");
			docFileService.save(file);
			file = docFileDao.find(file);
			return file.getOid();

		} catch (Exception ex) {
			logger.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					logger.error("[getContent] Exception!!", ex);
				}
			}
		}
		return null;
	}

	private int _setCellData(String tBranch, List<Map<String, Object>> data,
			WritableCellFormat cellformat, int headShift, WritableSheet sheet,
			Properties pro, Boolean tAll, ByteArrayOutputStream baos,
			Map<String, String> lnFlag_map) {
		int tCount = 0;
		try {

			for (int i = 0; i < data.size(); i++) {
				Map<String, Object> pivot = data.get(i);

				if (!"9".equals(tBranch.substring(0, 1))) {
					String pBranch = Util.trim(pivot.get("ELF516_CNTRNO"));
					if (!"".equals(pBranch)) {
						pBranch = pBranch.substring(0, 3);
						if (!pBranch.equals(tBranch)) {
							continue;
						}
					}
				}
				// 資料年月
				String value = Util.trim(pivot.get("ELF516_YYYYMM"));
				Label label = new Label(0, headShift + tCount, value,
						cellformat);
				sheet.addCell(label);
				// 性質
				value = Util.trim(pivot.get("ELF516_STATUS"));
				if ("A".equals(value)) {
					value = pro.getProperty("CLS250R01.statusA");
				} else if ("N".equals(value)) {
					value = pro.getProperty("CLS250R01.statusN");
				}
				label = new Label(1, headShift + tCount, value, cellformat);
				sheet.addCell(label);
				// 統一編號
				value = Util.trim(pivot.get("ELF516_CUSTID")) + "-"
						+ Util.trim(pivot.get("ELF516_DUPNO"));
				label = new Label(2, headShift + tCount, value, cellformat);
				sheet.addCell(label);
				// 姓名
				value = Util.trim(pivot.get("ELF516_CUSTNAME"));
				label = new Label(3, headShift + tCount, value, cellformat);
				sheet.addCell(label);
				// 額度序號
				value = Util.trim(pivot.get("ELF516_CNTRNO"));
				label = new Label(4, headShift + tCount, value, cellformat);
				sheet.addCell(label);
				if (tAll) {
					// 帳號
					value = Util.trim(pivot.get("ELF516_LOANNO"));
					label = new Label(5, headShift + tCount, value, cellformat);
					sheet.addCell(label);
					// 疑似代辦案件訊息
					int columnIdx = 5;
					if (true) {
						String ELF516_LNFLAG = Util.trim(pivot
								.get("ELF516_LNFLAG"));
						String defaultOption = Util.isEmpty(ELF516_LNFLAG) ? ""
								: "-";
						String valueA = defaultOption;
						String valueB = defaultOption;
						String valueC = defaultOption;
						String valueD = defaultOption;
						String valueE = defaultOption;
						String valueF = defaultOption;
						/*
						 * A 撥款後回查有其他金融機構短期內接續撥款情形 (指3個月內) B 貸款後不久即延滯情形 (指3個月內)
						 * C 跨區承作之情形 (對於借款人居住、工作地與案件來源無地源關係之申貸案件) D 其他可疑情形 E
						 * 經查並無仲介代辦情況出現
						 */
						if (Util.equals("A", ELF516_LNFLAG)) {
							valueA = "有";
							/*
							 * 在 201804 的額度序號 031110600133 , 被輸入 A,
							 * 但Memo的文字=經107.05.02查詢聯徵並無其他金融機構短期內連續撥款情形
							 */
						} else if (Util.equals("B", ELF516_LNFLAG)) {
							valueB = "有";
						} else if (Util.equals("C", ELF516_LNFLAG)) {
							valueC = "有";
						} else if (Util.equals("D", ELF516_LNFLAG)) {
							valueD = Util.trim(pivot.get("ELF516_OTHERMEMO"));
							/*
							 * 在 201805 的額度序號 026110700062 , 被輸入 D,
							 * 但Memo的文字=因該筆土地向國有財產署標購
							 * ，撥款繳付財產署尾款時暫掛無擔科目，待該筆土地設定予本行後即轉為擔保科目。 在 201805
							 * 的額度序號 044110700029 , 被輸入 D,
							 * 但Memo的文字=經查並無仲介代辦情況出現【文字與E相同】 => 即使輸入 D(沒有輸入 E),
							 * 也可能代表 ok ===> 不能把「XLS欄位 - 經查並無仲介代辦情況出現」填入"否"
							 */
							valueE = defaultOption;
						} else if (Util.equals("E", ELF516_LNFLAG)) {
							valueE = "是";
						} else if (Util.equals("F", ELF516_LNFLAG)) {
							valueF = "有";
						} else if (Util.equals("Z", ELF516_LNFLAG)) {
							// 為未撥款已銷戶案件 or 3個月後一直未撥款案件, 不需維護疑似代辦案件功能,
							// 所以也無須出現在報表中
							continue;
						}

						List<String> list = new ArrayList<String>();
						list.add(valueA);
						list.add(valueB);
						list.add(valueC);
						list.add(valueD);
						list.add(valueE);
						list.add(valueF);

						for (String val_LNFLAG : list) {
							++columnIdx;

							label = new Label(columnIdx, headShift + tCount,
									val_LNFLAG, cellformat);
							sheet.addCell(label);
						}
					}

					// 經辦
					++columnIdx;
					value = Util.trim(pivot.get("ELF516_UPDATER"));
					label = new Label(columnIdx, headShift + tCount, value,
							cellformat);
					sheet.addCell(label);
					// 覆核人
					++columnIdx;
					value = Util.trim(pivot.get("ELF516_APPROVER"));
					label = new Label(columnIdx, headShift + tCount, value,
							cellformat);
					sheet.addCell(label);
					// 覆核日期
					++columnIdx;
					value = Util.trim(pivot.get("ELF516_APPROVETME"));
					if (!"".equals(value)) {
						value = value.substring(0, 10);
					}
					label = new Label(columnIdx, headShift + tCount, value,
							cellformat);
					sheet.addCell(label);

					// 查證結果
					++columnIdx;
					value = Util.trim(pivot.get("ELF516_BRANCHCOMM"));
					label = new Label(columnIdx, headShift + tCount, value,
							cellformat);
					sheet.addCell(label);
					
					// 產品種類
					++columnIdx;
					value = Util.trim(pivot.get("LNF030_LOAN_CLASS"));
					label = new Label(columnIdx, headShift + tCount, value,
							cellformat);
					sheet.addCell(label);
					
					// 動撥日期
					++columnIdx;
					value = Util.trim(pivot.get("LNF030_LOAN_DATE"));
					label = new Label(columnIdx, headShift + tCount, value,
							cellformat);
					sheet.addCell(label);
				}

				tCount++;
			}
		} catch (Exception ex) {
			logger.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					logger.error("[getContent] Exception!!", ex);
				}
			}
		}
		return tCount;
	}

	@Override
	public String buildCLS180R21Xls(LMSBATCH batchtbl) {

		Date begDate = batchtbl.getBgnDate();
		Date endDate = batchtbl.getEndDate();
		String tBegDate = TWNDate.toAD(begDate);
		String tEndDate = TWNDate.toAD(endDate);
		String rptNo = batchtbl.getRptNo();

		// M-110-0236
		// 疑似代辦案件註記清單、消金借款人留存同一通訊處註記清單(含地址、電話、email比對清單)，自110年9月起移交予消金業務
		// 此報表是查詢 LMS.C900S02E（同一通訊指標控制檔）
		String rpt_brno = "";
		if (Util.equals(batchtbl.getBranch(), UtilConstants.BankNo.授管處)
				|| Util.equals(batchtbl.getBranch(), UtilConstants.BankNo.消金業務處)
				|| Util.equals(batchtbl.getBranch(), UtilConstants.BankNo.資訊處)) {
			rpt_brno = ""; // ALL
		} else {
			rpt_brno = Util.trim(batchtbl.getBranch());
		}

		ISearch search_cond = clsService.getMetaSearch();
		if (Util.isNotEmpty(rpt_brno)) {
			search_cond.addSearchModeParameters(SearchMode.EQUALS, "brNo",
					batchtbl.getBranch());
		}
		search_cond.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");

		if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
				rptNo)) {
			search_cond.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.IS_NULL, "chk_result",
							""), new SearchModeParameter(SearchMode.EQUALS,
							"chk_result", ""));
		} else if (Util.equals(
				UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單, rptNo)) {
			search_cond.addSearchModeParameters(SearchMode.GREATER_THAN,
					"chk_result", "");
			// ==========
			search_cond.addSearchModeParameters(SearchMode.BETWEEN, "cyc_mn",
					new Object[] { tBegDate, tEndDate });
		} else if (Util.equals(
				UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單, rptNo)) {
			search_cond.addSearchModeParameters(SearchMode.BETWEEN, "cyc_mn",
					new Object[] { tBegDate, tEndDate });
		}
		if (true) {
			search_cond.addOrderBy("cyc_mn");
			search_cond.addOrderBy("brNo");
			search_cond.addOrderBy("rel_flag");
			search_cond.addOrderBy("text");
		}
		if (true) {
			search_cond.setMaxResults(Integer.MAX_VALUE);
		}

		Page<? extends GenericBean> page = clsService.findPage(C900S02E.class,
				search_cond);
		@SuppressWarnings("unchecked")
		List<C900S02E> c900s02e_list = (List<C900S02E>) page.getContent();
		try {
			DocFile docFile = _xls_CLS180R21(batchtbl, rpt_brno, rptNo,
					c900s02e_list);
			return docFile.getOid();
		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e));
		}
		return "";
	}

	private DocFile _xls_CLS180R21(LMSBATCH batch, String rpt_brno,
			String rptNo, List<C900S02E> c900s02e_list) throws WriteException,
			IOException {

		File file = null;
		Label label = null;
		WritableFont font_rpt_header = null;
		WritableFont font_choose = null;
		WritableCellFormat cellFormat_rpt_header = null;
		WritableCellFormat cellFormatL = null;
		WritableCellFormat cellFormatL_Border = null;
		WritableCellFormat cellFormatR_Border = null;

		WritableWorkbook workbook = null;
		WritableSheet sheet = null;
		DocFile docFile = null;
		String filename = LMSUtil.getUploadFilePath(batch.getBranch(),
				batch.getMainId(), batch.getRptNo());
		try {
			docFile = saveDocFile(batch.getBranch(), batch.getMainId(),
					batch.getRptNo(), "xls");
			// 設定下載的 xls 名稱
			docFile.setSrcFileName(batch.getRptName() + ".xls");
			docFileService.save(docFile, false);

			file = new File(filename);
			file.mkdirs();
			file = new File(filename + "/" + docFile.getOid() + ".xls");

			// 字型設定
			font_rpt_header = new WritableFont(WritableFont.createFont("新細明體"),
					12, WritableFont.BOLD);
			font_choose = new WritableFont(WritableFont.createFont("新細明體"), 9);

			cellFormat_rpt_header = LMSUtil.setCellFormat(
					cellFormat_rpt_header, font_rpt_header, Alignment.CENTRE,
					false);
			cellFormatL_Border = LMSUtil.setCellFormat(cellFormatL_Border,
					font_choose, Alignment.LEFT, true);
			cellFormatR_Border = LMSUtil.setCellFormat(cellFormatR_Border,
					font_choose, Alignment.RIGHT, true);
			cellFormatL = LMSUtil.setCellFormat(cellFormatL_Border,
					font_choose, Alignment.LEFT, false);

			workbook = Workbook.createWorkbook(file);

			if (true) {
				sheet = workbook.createSheet("Sheet1", 0);
				SheetSettings setting = sheet.getSettings();
				setting.setPaperSize(PaperSize.A4);

				setting.setFitWidth(1); // 設定預覽列印與列印成為一頁, 寬度
				setting.setOrientation(PageOrientation.PORTRAIT);

				Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
				headerMap.put("資料年月", 10);
				headerMap.put("分行", 14);
				headerMap.put("客戶ID", 12);
				headerMap.put("客戶姓名", 16);
				headerMap.put("通訊處類型", 10);
				headerMap.put("通訊處", 70);
				headerMap.put("相同通訊處借戶明細", 28);
				if (Util.equals(
						UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
						rptNo)) {
				} else if (Util.equals(
						UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單,
						rptNo)
						|| Util.equals(
								UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
								rptNo)) {
					headerMap.put("查證結果", 10);
					headerMap.put("理由及後續處理方式", 24);
					headerMap.put("經辦", 10);
					headerMap.put("覆核人", 10);
					headerMap.put("覆核日期", 10);
				}
				
				if(Util.equals(
						UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
						rptNo)){
					
					headerMap.put("額度序號", 10);
					headerMap.put("產品種類59", 10);
					headerMap.put("撥款日期", 10);
				}

				int col_size = headerMap.size();
				String dataYM = "";
				// 合併單元格
				{
					sheet.setRowView(0, 500);
					// ~~~
					int rowIdx = 0;
					sheet.mergeCells(0, rowIdx, col_size - 1, rowIdx);
					String mainTitle_1 = "";
					if (Util.isEmpty(rpt_brno)) {

					} else {
						mainTitle_1 = batch.getBranch()
								+ branchService
										.getBranchName(batch.getBranch());
					}
					String mainTitle_2 = "";
					if (Util.equals(
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
							rptNo)) {
						mainTitle_2 = "消金借款人留存同一通訊處未註記清單";
					} else if (Util.equals(
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單,
							rptNo)) {
						mainTitle_2 = "消金借款人留存同一通訊處已註記清單";
						dataYM = StringUtils.substring(
								TWNDate.toAD(batch.getBgnDate()), 0, 7)
								+ "~"
								+ StringUtils.substring(
										TWNDate.toAD(batch.getEndDate()), 0, 7);
					} else if (Util.equals(
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
							rptNo)) {
						mainTitle_2 = "消金借款人留存同一通訊處全部清單";
						dataYM = StringUtils.substring(
								TWNDate.toAD(batch.getBgnDate()), 0, 7)
								+ "~"
								+ StringUtils.substring(
										TWNDate.toAD(batch.getEndDate()), 0, 7);
					}
					label = new Label(0, rowIdx, Util.trim(mainTitle_1
							+ mainTitle_2), cellFormat_rpt_header);
					sheet.addCell(label);
				}
				int rowIdx = 1;
				{
					sheet.mergeCells(0, rowIdx, 1, rowIdx);
					label = new Label(0, rowIdx, "查詢日期："
							+ Util.trim(TWNDate.toAD(batch.getUpdateTime())),
							cellFormatL);
					sheet.addCell(label);

					String promptMsg = "";
					if (Util.equals(
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
							rptNo)) {
						String cyc_mn = StringUtils
								.substring(Util.trim(TWNDate.toAD(CapDate
										.addMonth(CrsUtil.get_sysMonth_1st(),
												-1))), 0, 7)
								+ "-01";
						String fn = CrsUtil.FN_KEY_C900S02E;
						String genDate = StringUtils.replace(cyc_mn, "-", "");
						String genTime = "000000";
						C900M03A m03a_for_C900S02E = clsService
								.findC900M03A_fnGenDateGenTime(fn, genDate,
										genTime);
						if (m03a_for_C900S02E == null) {
							promptMsg = "資料範圍【尚未包含"
									+ StringUtils.substring(cyc_mn, 0, 7) + "】";
						}
					}
					if (Util.isNotEmpty(promptMsg)) {
						sheet.mergeCells(2, rowIdx, 4, rowIdx);
						label = new Label(2, rowIdx, promptMsg, cellFormatL);
						sheet.addCell(label);
					}

					if (Util.isNotEmpty(dataYM)) {
						++rowIdx;
						sheet.mergeCells(0, rowIdx, 1, rowIdx);
						label = new Label(0, rowIdx, "資料年月：" + dataYM,
								cellFormatL);
						sheet.addCell(label);
					}
				}

				if (true) {
					++rowIdx;
					int colIdx = 0;
					for (String h : headerMap.keySet()) {
						int colWidth = headerMap.get(h);

						sheet.setColumnView(colIdx, colWidth);
						sheet.addCell(new Label(colIdx, rowIdx, h,
								cellFormatL_Border));
						// ---
						colIdx++;
					}
				}

				if (true) {
					++rowIdx;
					List<String[]> r_part_1 = detail_CLS180R21(rptNo,
							c900s02e_list);
					boolean is_rpt_has_data = r_part_1.size() > 0;
					if (is_rpt_has_data) {

					} else {
						// 塞入一筆空白列
						String[] row = new String[col_size];
						for (int i = 0; i < col_size; i++) {
							row[i] = "";
						}
						r_part_1.add(row);
					}

					if (true) {
						for (String[] arr : r_part_1) {
							int colLen = arr.length;
							for (int i_col = 0; i_col < colLen; i_col++) {
								sheet.addCell(new Label(i_col, rowIdx,
										arr[i_col], cellFormatL_Border));

							}
							// ---
							rowIdx++;
						}
					}

					if (is_rpt_has_data) {

					} else {
						// 無資料
						sheet.mergeCells(0, rowIdx, col_size - 1, rowIdx);
						label = new Label(0, rowIdx, "輸入條件查無資料", cellFormatL);
						sheet.addCell(label);
						rowIdx += 2;
					}
				}
				
				String remark=
					"註1：消金定義：房貸、行家理財、消貸(排除外勞貸款及存單質借)。\n" + 
					"註2：疑似人頭戶定義：資料倉儲系統留存之通訊資料[包括：地址(戶籍地、通訊處、郵政信箱)、電話(手機號碼、住宅及公司電話)、傳真、E-MAIL等]有相同之借款人。\n" + 
					"註3：本報表檢核原則：逐月檢視對留存同一通訊處(含地址、電話、E-MAIL)之授信關聯戶。";

				sheet.mergeCells(0, rowIdx, col_size - 1, rowIdx + 2);
				label = new Label(0, rowIdx, remark, cellFormatL);
				sheet.addCell(label);
				
			}
			workbook.write();
			workbook.close();
		} finally {

		}
		return docFile;
	}

	private List<String[]> detail_CLS180R21(String rptNo,
			List<C900S02E> c900s02e_list) {
		List<String[]> r = new ArrayList<String[]>();
		Map<String, String> brNo_brName = new HashMap<String, String>();
		Map<String, String> rel_flag_desc = new HashMap<String, String>();
		rel_flag_desc.put("1", "地址");
		rel_flag_desc.put("2", "電話");
		rel_flag_desc.put("3", "E-mail");
		Map<String, String> chk_result_desc = new HashMap<String, String>();
		chk_result_desc.put("Y", "正常");
		chk_result_desc.put("N", "異常");
		int col_cnt = 7;
		if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
				rptNo)) {

		} else if (Util.equals(
				UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單, rptNo)) {
			col_cnt = col_cnt + 5;
		} else if(Util.equals(
				UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
				rptNo)){
			col_cnt = col_cnt + 8;
		}
		for (C900S02E c900s02e : c900s02e_list) {
			String BRNO = c900s02e.getBrNo();
			List<String> c900s02f_data = new ArrayList<String>();
			if (true) {
				for (C900S02F c900s02f : clsService
						.findC900S02F_mainId_order(c900s02e.getMainId())) {
					c900s02f_data.add(CrsUtil.build_C900S02F_info(c900s02f));
				}
			}
			// ========

			String[] row = new String[col_cnt];
			row[0] = StringUtils.substring(TWNDate.toAD(c900s02e.getCyc_mn()),
					0, 7);
			row[1] = BRNO + get_brname(brNo_brName, BRNO);
			row[2] = c900s02e.getCustId() + "-" + c900s02e.getDupNo();
			row[3] = c900s02e.getCname();
			row[4] = LMSUtil.getDesc(rel_flag_desc, c900s02e.getRel_flag());
			row[5] = c900s02e.getText();
			row[6] = StringUtils.join(c900s02f_data, "\r\n");
			if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單,
					rptNo)
					|| Util.equals(
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
							rptNo)) {
				row[7] = LMSUtil.getDesc(chk_result_desc,
						c900s02e.getChk_result());
				row[8] = c900s02e.getChk_memo();
				row[9] = c900s02e.getUpdater();
				row[10] = c900s02e.getApprover();
				row[11] = Util.trim(TWNDate.toAD(c900s02e.getApproveTime()));
			}
			
			if(Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單, rptNo)){
				
				List<String> prodKindList = new ArrayList<String>();
				List<String> cntrNoList = new ArrayList<String>();
				List<Map<String, Object>> list = this.eloandbBaseService.findProdKind59NewCase(c900s02e.getCustId(), c900s02e.getDupNo());
				String loanDate = null;
				for(Map<String, Object> map : list){
					prodKindList.add(CapString.trimNull(map.get("PRODKIND")));
					cntrNoList.add(CapString.trimNull(map.get("CNTRNO")));
					loanDate = this.misdbBASEService.getLatestFirstLoanDateByLnf030Cntrno(CapString.trimNull(map.get("CNTRNO")));
				}
				
				row[12] = StringUtils.join(cntrNoList, "、");
				row[13] = StringUtils.join(prodKindList, "、");
				row[14] = loanDate;
			}
			// ========
			r.add(row);

		}
		return r;
	}

	@Override
	public String buildCLS180R22Xls(LMSBATCH batchtbl) {
		String cyc_mn = TWNDate.toAD(batchtbl.getBgnDate());
		String rel_flag = "X"; // default
		if (true) {
			String rptNo = batchtbl.getRptNo();
			if (Util.equals(
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單, rptNo)) {
				rel_flag = "1";
			} else if (Util.equals(
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單, rptNo)) {
				rel_flag = "2";
			} else if (Util
					.equals(UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單,
							rptNo)) {
				rel_flag = "3";
			}
		}

		try {
			DocFile docFile = _xls_CLS180R22(batchtbl, cyc_mn, rel_flag);
			return docFile.getOid();
		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e));
		}
		return "";
	}

	private DocFile _xls_CLS180R22(LMSBATCH batch, String cyc_mn,
			String rel_flag) throws WriteException, IOException {
		boolean isDwCustRelFirstTotalRun = CrsUtil
				.isDwCustRelFirstTotalRun(cyc_mn);
		String rpt_brno = "";
		// M-110-0236
		// 疑似代辦案件註記清單、消金借款人留存同一通訊處註記清單(含地址、電話、email比對清單)，自110年9月起移交予消金業務
		if (Util.equals(batch.getBranch(), UtilConstants.BankNo.授管處)
				|| Util.equals(batch.getBranch(), UtilConstants.BankNo.消金業務處)
				|| Util.equals(batch.getBranch(), UtilConstants.BankNo.資訊處)) {
			rpt_brno = ""; // ALL
		} else {
			rpt_brno = batch.getBranch();
		}

		File file = null;
		Label label = null;
		WritableFont font_rpt_header = null;
		WritableFont font_choose = null;
		WritableCellFormat cellFormat_rpt_header = null;
		WritableCellFormat cellFormatL = null;
		WritableCellFormat cellFormatL_Border = null;
		WritableCellFormat cellFormatR_Border = null;

		WritableWorkbook workbook = null;
		WritableSheet sheet = null;
		DocFile docFile = null;
		String filename = LMSUtil.getUploadFilePath(batch.getBranch(),
				batch.getMainId(), batch.getRptNo());
		try {
			docFile = saveDocFile(batch.getBranch(), batch.getMainId(),
					batch.getRptNo(), "xls");
			// 設定下載的 xls 名稱
			docFile.setSrcFileName(batch.getRptName() + ".xls");
			docFileService.save(docFile, false);

			file = new File(filename);
			file.mkdirs();
			file = new File(filename + "/" + docFile.getOid() + ".xls");

			// 字型設定
			font_rpt_header = new WritableFont(WritableFont.createFont("新細明體"),
					12, WritableFont.BOLD);
			font_choose = new WritableFont(WritableFont.createFont("新細明體"), 9);

			cellFormat_rpt_header = LMSUtil.setCellFormat(
					cellFormat_rpt_header, font_rpt_header, Alignment.CENTRE,
					false);
			cellFormatL_Border = LMSUtil.setCellFormat(cellFormatL_Border,
					font_choose, Alignment.LEFT, true);
			cellFormatR_Border = LMSUtil.setCellFormat(cellFormatR_Border,
					font_choose, Alignment.RIGHT, true);
			cellFormatL = LMSUtil.setCellFormat(cellFormatL_Border,
					font_choose, Alignment.LEFT, false);

			workbook = Workbook.createWorkbook(file);

			if (true) {
				sheet = workbook.createSheet("Sheet1", 0);
				SheetSettings setting = sheet.getSettings();
				setting.setPaperSize(PaperSize.A4);

				setting.setFitWidth(1); // 設定預覽列印與列印成為一頁, 寬度
				setting.setOrientation(PageOrientation.PORTRAIT);

				String dataYM = Util.trim(StringUtils.substring(
						TWNDate.toAD(batch.getDataDate()), 0, 7));

				Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
				headerMap.put("分行", 14);
				headerMap.put("通訊處", Util.equals("1", rel_flag) ? 70 : 40);
				headerMap.put("客戶ID", 12);
				headerMap.put("客戶姓名", 16);
				headerMap.put("授信往來分行", 14);
				headerMap.put("需註記案件", 10);

				int col_size = headerMap.size();
				// 合併單元格
				{
					sheet.setRowView(0, 500);
					// ~~~
					int rowIdx = 0;
					sheet.mergeCells(0, rowIdx, col_size - 1, rowIdx);
					String mainTitle_1 = "";
					if (Util.isEmpty(rpt_brno)) {

					} else {
						mainTitle_1 = batch.getBranch()
								+ branchService
										.getBranchName(batch.getBranch());
					}
					String mainTitle_2 = batch.getRptName();
					label = new Label(0, rowIdx, Util.trim(mainTitle_1
							+ mainTitle_2), cellFormat_rpt_header);
					sheet.addCell(label);
				}
				{
					int rowIdx = 1;
					sheet.mergeCells(0, rowIdx, 1, rowIdx);
					label = new Label(0, rowIdx, "查詢日期："
							+ Util.trim(TWNDate.toAD(batch.getUpdateTime())),
							cellFormatL);
					sheet.addCell(label);

					rowIdx = 2;
					sheet.mergeCells(0, rowIdx, 1, rowIdx);
					label = new Label(0, rowIdx, "資料年月：" + dataYM, cellFormatL);
					sheet.addCell(label);
				}

				if (true) {
					int rowIdx = 3;
					int colIdx = 0;
					for (String h : headerMap.keySet()) {
						int colWidth = headerMap.get(h);

						sheet.setColumnView(colIdx, colWidth);
						sheet.addCell(new Label(colIdx, rowIdx, h,
								cellFormatL_Border));
						// ---
						colIdx++;
					}
				}

				if (true) {
					int rowIdx = 4;
					List<String[]> r_part_1 = detail_CLS180R22(
							isDwCustRelFirstTotalRun, cyc_mn, rel_flag,
							rpt_brno);
					boolean is_rpt_has_data = r_part_1.size() > 0;
					if (is_rpt_has_data) {

					} else {
						// 塞入一筆空白列
						String[] row = new String[col_size];
						for (int i = 0; i < col_size; i++) {
							row[i] = "";
						}
						r_part_1.add(row);
					}

					if (true) {
						for (String[] arr : r_part_1) {
							int colLen = arr.length;
							for (int i_col = 0; i_col < colLen; i_col++) {
								sheet.addCell(new Label(i_col, rowIdx,
										arr[i_col], cellFormatL_Border));

							}
							// ---
							rowIdx++;
						}
					}

					if (is_rpt_has_data) {

					} else {
						// 無資料
						sheet.mergeCells(0, rowIdx, col_size - 1, rowIdx);
						label = new Label(0, rowIdx, "輸入條件查無資料", cellFormatL);
						sheet.addCell(label);
					}
				}
			}
			workbook.write();
			workbook.close();
		} finally {

		}
		return docFile;
	}

	private List<String[]> detail_CLS180R22(boolean isDwCustRelFirstTotalRun,
			String cyc_mn, String rel_flag, String rpt_brno) {
		List<Map<String, Object>> text_list = eloandbBaseService
				.prep_text_from_C900S03C(isDwCustRelFirstTotalRun, cyc_mn,
						rel_flag, rpt_brno);

		List<String[]> r = new ArrayList<String[]>();
		Map<String, String> brNo_brName = new HashMap<String, String>();
		Map<String, String> idDup_cName = new HashMap<String, String>();
		for (Map<String, Object> map : text_list) {

			String RPT_BRNO = Util.trim(MapUtils.getString(map, "RPT_BRNO"));
			String text = Util.trim(MapUtils.getString(map, "TEXT"));

			List<Map<String, Object>> layer2_list = eloandbBaseService
					.prep_custKey_brNo_from_C900S03C_text(cyc_mn, rel_flag,
							text);
			/*
			 * 需註記的客戶，先出現
			 */
			List<Map<String, Object>> layer2_brA_list = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> layer2_brB_list = new ArrayList<Map<String, Object>>();
			for (Map<String, Object> map_2 : layer2_list) {
				String BRNO = Util.trim(MapUtils.getString(map_2, "BRNO"));
				if (Util.equals(RPT_BRNO, BRNO)) {
					layer2_brA_list.add(map_2);
				} else {
					layer2_brB_list.add(map_2);
				}
			}
			List<Map<String, Object>> layer2_brAB_list = new ArrayList<Map<String, Object>>();
			layer2_brAB_list.addAll(layer2_brA_list);
			layer2_brAB_list.addAll(layer2_brB_list);
			for (Map<String, Object> map_2 : layer2_brAB_list) {
				String CUST_KEY = Util.trim(MapUtils.getString(map_2,
						"CUST_KEY"));
				String BRNO = Util.trim(MapUtils.getString(map_2, "BRNO"));
				String MARK_FLAG = Util.equals(RPT_BRNO, BRNO) ? "Y" : "";
				String CUST_ID = CrsUtil.get_custId_from_custKey(CUST_KEY);
				String CUST_DUP_NO = CrsUtil.get_dupNo_from_custKey(CUST_KEY);
				// ========
				String[] row = new String[6];
				row[0] = RPT_BRNO + get_brname(brNo_brName, RPT_BRNO);
				row[1] = text;
				row[2] = CUST_ID + "-" + CUST_DUP_NO;
				row[3] = get_cname(idDup_cName, CUST_ID, CUST_DUP_NO);
				row[4] = BRNO + get_brname(brNo_brName, BRNO);
				row[5] = MARK_FLAG;
				// ========
				r.add(row);
			}

		}
		return r;
	}

	private String get_brname(Map<String, String> brNo_brName, String brno) {
		if (!brNo_brName.containsKey(brno)) {
			String cName = branchService.getBranchName(brno);
			brNo_brName.put(brno, cName);
		}
		return brNo_brName.get(brno);
	}

	private String get_cname(Map<String, String> idDup_cName, String custId,
			String dupNo) {
		String idDup = LMSUtil.getCustKey_len10custId(custId, dupNo);
		if (!idDup_cName.containsKey(idDup)) {
			String cName = "";
			if (Util.isEmpty(cName)) { // 先從 ELOAN
				Map<String, Object> map = lmsCustdataService.findCustDataCname(
						custId, dupNo);
				cName = Util.trim(MapUtils.getString(map, "CNAME"));
			}
			if (Util.isEmpty(cName)) { // 再從 MIS
				Map<String, Object> latestData = iCustomerService
						.findByIdDupNo(custId, dupNo);
				cName = Util.trim(MapUtils.getString(latestData, "CNAME"));
			}
			idDup_cName.put(idDup, cName);
		}
		return idDup_cName.get(idDup);
	}

	@Override
	public void sync_CLS180R17_data(Date endDate) {
		boolean delThenInsertData = clsService
				.is_function_on_codetype("C900S02C_fillData");
		if (delThenInsertData) {
			String data_ym = StringUtils.substring(
					Util.trim(TWNDate.toAD(endDate)), 0, 7);
			c900s02cDao.deleteByDataYM(data_ym);
			// ================
			String year_1st = StringUtils.substring(data_ym, 0, 4) + "-01-01";
			String data_ym_1st = data_ym + "-01";

			List<Object[]> batchValues = new ArrayList<Object[]>();
			List<Map<String, Object>> fetch_list = new ArrayList<Map<String, Object>>();
			if (clsService.is_function_on_codetype("J-107-0360")) {
				// 排除residential ='2'
				fetch_list = eloandbBaseService
						.findC900S02C_fillData_exclude940(year_1st,
								data_ym_1st, TWNDate.toAD(endDate));
			} else {
				fetch_list = eloandbBaseService.findC900S02C_fillData(year_1st,
						data_ym_1st, TWNDate.toAD(endDate));
			}
			for (Map<String, Object> map : fetch_list) {
				Object[] o = new Object[8];
				// ===
				o[0] = data_ym;
				o[1] = Util.trim(MapUtils.getString(map, "MAINID"));
				o[2] = Util.trim(MapUtils.getString(map, "CASEBRID"));
				o[3] = Util.trim(MapUtils.getString(map, "BRNGROUP"));
				o[4] = MapUtils.getObject(map, "CASEDATE");
				o[5] = MapUtils.getObject(map, "ENDDATE");
				o[6] = Util.trim(MapUtils.getString(map, "LLML081_F"));
				o[7] = MapUtils.getObject(map, "SUM_F_AMT");
				// ===
				batchValues.add(o);
			}
			eloandbBaseService.batchInsert_C900S02C(batchValues);
		}
	}

	@Override
	public int send_CLS180R03B_to_Audit(String lmsRpt_mainId) {
		// 驗證 FTP 連線，若失敗，會拋出 GWException
		auditFtpClient.test();

		LMSRPT lmsRpt = findByLMSRPT(lmsRpt_mainId);
		if (lmsRpt != null) {
			DocFile docFile = docFileService.read(lmsRpt.getReportOidFile());
			if (docFile != null) {
				File gen_xls = docFileService.getRealFile(docFile);
				if (gen_xls != null) {
					// 交付稽核處時，不採用 {寄 e-mail}
					// emailClient.send(isTextFormat, null, toAddr, content,
					// content, attFiles);

					byte[] localFile = docFile.getData();
					String serverDir = auditFtpClient.getServerDir();
					boolean isBinaryFile = true;
					boolean delFtpFile = true;
					boolean isAddDate = false;
					String remoteFileName = Util.trim(docFile.getSrcFileName());
					auditFtpClient
							.send(lmsRpt_mainId, localFile, serverDir,
									remoteFileName, isBinaryFile, delFtpFile,
									isAddDate);
					// ~~~~~~
					return 0;
				} else {
					return -3;
				}
			} else {
				return -2;
			}
		} else {
			return -1;
		}
	}

	/**
	 * 計算借款時年齡與加計貸款年限後之年齡
	 * 
	 * @param birthday
	 *            生日
	 * @param caseDay
	 *            借款日
	 * @param lnEndDay
	 *            到期日
	 * @return
	 */
	private Map<String, Object> calculateAgelnEndDayAge(String birthday,
			String caseDay, String lnEndDay) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (CapString.isEmpty(birthday)) {
			map.put("AGE", "");
			map.put("LNENDDATEAGE", "");
			return map;
		}
		Calendar calendarBirthday = Calendar.getInstance();
		calendarBirthday.setTime(CapDate.getDate(birthday, "yyyy-MM-dd"));
		Calendar calendarCaseDay = Calendar.getInstance();
		calendarCaseDay.setTime(CapDate.getDate(caseDay, "yyyy-MM-dd"));
		int age = calendarCaseDay.get(Calendar.YEAR)
				- calendarBirthday.get(Calendar.YEAR);
		if (calendarCaseDay.get(Calendar.MONTH) * 100
				+ calendarCaseDay.get(Calendar.DAY_OF_MONTH) < calendarBirthday
				.get(Calendar.MONTH)
				* 100
				+ calendarBirthday.get(Calendar.DAY_OF_MONTH)) {
			age = age - 1;
		}
		// 預設為相同年齡-短期借款時
		int lnEndDayAge = age;
		// 如有借款到期日，則計算借款到期日年齡
		if (!CapString.isEmpty(lnEndDay)) {
			Calendar calendarLnEndDay = Calendar.getInstance();
			calendarLnEndDay.setTime(CapDate.getDate(lnEndDay, "yyyy-MM-dd"));
			lnEndDayAge = calendarLnEndDay.get(Calendar.YEAR)
					- calendarBirthday.get(Calendar.YEAR);
			if (calendarLnEndDay.get(Calendar.MONTH) * 100
					+ calendarLnEndDay.get(Calendar.DAY_OF_MONTH) < calendarBirthday
					.get(Calendar.MONTH)
					* 100
					+ calendarBirthday.get(Calendar.DAY_OF_MONTH)) {
				lnEndDayAge = lnEndDayAge - 1;
			}
		}
		map.put("AGE", age);
		map.put("LNENDDATEAGE", lnEndDayAge);
		return map;
	}

	/**
	 * J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	 * 
	 * @param brNo
	 * @return
	 */
	@Override
	public CapAjaxFormResult getStaffNo(String brNo) {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String branch = CapString.trimNull(brNo);
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管, SignEnum.經辦人員 };
		if (CapString.isEmpty(branch)) {
			branch = MegaSSOSecurityContext.getUnitNo();
		}
		result.putAll(userInfoService
				.findByBrnoAndSignIdNotLeave(branch, signs));

		return result;

	}

}
