package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** <pre>DW_LNCUSTREL檔
 * </pre>
 * 
 * @since 2019/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/02, J-107-0129 , 在 SLMS-00074 將 DW 的資料抄寫到  ELOANDB
 *          </li>
 *          </ul>
 */
@Entity
@Table(name="C900S03C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900S03C extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	@Temporal(TemporalType.DATE)
	@Column(name = "CYC_MN", columnDefinition = "DATE")
	private Date cyc_mn;               
	
	@Column(name="CUST_KEY", length=11, columnDefinition="CHAR(11)")
	private String cust_key;
	
	@Column(name="CUST_ID", length=10, columnDefinition="CHAR(10)")
	private String cust_id;

	@Column(name="CUST_DUP_NO", length=1, columnDefinition="CHAR(1)")
	private String cust_dup_no;
	
	/** 地址:1, 電話:2, e-mail:3 */
	@Column(name="REL_FLAG", length=1, columnDefinition="CHAR(1)")
	private String rel_flag;
	
	@Column(name="TEXT", length=120, columnDefinition="CHAR(120)")
	private String text;
	
	@Column(name="REL_UPD", length=1, columnDefinition="CHAR(1)")
	private String rel_upd;
	
	@Digits(integer=10, fraction=0)
	@Column(name="DUP_CNT", columnDefinition="DEC(10,0)")
	private BigDecimal dup_cnt;
	
	@Column(name="DW_LST_DATA_SRC", length=16, columnDefinition="CHAR(16)")
	private String dw_lst_data_src;
	
	@Temporal(TemporalType.DATE)
	@Column(name = "DW_DATA_SRC_DT", columnDefinition = "DATE")
	private Date dw_data_src_dt;               
		
	@Temporal(TemporalType.DATE)
	@Column(name = "DW_LST_MNT_DT", columnDefinition = "DATE")
	private Date dw_lst_mnt_dt;               
		
	public String getOid() {
		return this.oid;
	}
	
	public void setOid(String value) {
		this.oid = value;
	}

	public Date getCyc_mn() {
		return cyc_mn;
	}

	public void setCyc_mn(Date cyc_mn) {
		this.cyc_mn = cyc_mn;
	}

	public String getCust_key() {
		return cust_key;
	}

	public void setCust_key(String cust_key) {
		this.cust_key = cust_key;
	}

	public String getCust_id() {
		return cust_id;
	}

	public void setCust_id(String cust_id) {
		this.cust_id = cust_id;
	}

	public String getCust_dup_no() {
		return cust_dup_no;
	}

	public void setCust_dup_no(String cust_dup_no) {
		this.cust_dup_no = cust_dup_no;
	}

	public String getRel_flag() {
		return rel_flag;
	}

	public void setRel_flag(String rel_flag) {
		this.rel_flag = rel_flag;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public String getRel_upd() {
		return rel_upd;
	}

	public void setRel_upd(String rel_upd) {
		this.rel_upd = rel_upd;
	}
	
	public BigDecimal getDup_cnt() {
		return dup_cnt;
	}

	public void setDup_cnt(BigDecimal dup_cnt) {
		this.dup_cnt = dup_cnt;
	}

	public String getDw_lst_data_src() {
		return dw_lst_data_src;
	}

	public void setDw_lst_data_src(String dw_lst_data_src) {
		this.dw_lst_data_src = dw_lst_data_src;
	}

	public Date getDw_data_src_dt() {
		return dw_data_src_dt;
	}

	public void setDw_data_src_dt(Date dw_data_src_dt) {
		this.dw_data_src_dt = dw_data_src_dt;
	}

	public Date getDw_lst_mnt_dt() {
		return dw_lst_mnt_dt;
	}

	public void setDw_lst_mnt_dt(Date dw_lst_mnt_dt) {
		this.dw_lst_mnt_dt = dw_lst_mnt_dt;
	}	
}
