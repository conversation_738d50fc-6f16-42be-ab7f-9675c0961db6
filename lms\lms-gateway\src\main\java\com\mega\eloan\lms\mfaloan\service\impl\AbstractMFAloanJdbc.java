package com.mega.eloan.lms.mfaloan.service.impl;

import javax.annotation.Resource;
import javax.sql.DataSource;

import tw.com.iisi.cap.context.CapParameter;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.EloanJdbcTemplate;

public class AbstractMFAloanJdbc {
	private EloanJdbcTemplate jdbc;

	/**
	 * get the the jdbc
	 * 
	 * @return the jdbc
	 */
	public EloanJdbcTemplate getJdbc() {
		return jdbc;
	}

	@Resource(name = "misSqlStatement")
	CapParameter sqlp;

	@Resource(name = "icbcrdb-db")
	public void setDataSource(DataSource dataSource) {
		jdbc = new EloanJdbcTemplate(dataSource, GWException.GWTYPE_MFALOAN);
		jdbc.setSqlProvider(sqlp);
		jdbc.setCauseClass(this.getClass());
	}

	/**
	 * 取得Sql
	 * @param sqlId sqlId
	 * @return sqlString
	 */
	public String getSqlBySqlId(String sqlId) {
		return sqlp.getValue(sqlId);
	}	

}
