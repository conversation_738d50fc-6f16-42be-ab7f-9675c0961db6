package com.mega.eloan.lms.rpt.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeSet;

import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;




/**
 * <pre>
 * 中期循環年度檢視表
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface CLS180R59Service {
	
	public void setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException;

	public void setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap1, Properties prop, int colIndex, int rowIndex) throws WriteException;

	public Map<String, Integer> getHeaderMapForSummaryOverdueCase();

	public Map<String, Integer> getOverdueCaseSummaryListTitle();

	public Map<String, Integer> getOverdueCaseDetailListTitle();

	public Map<String, List<String>> setBodyContentForSummaryOverdueCase(WritableSheet sheet, int colIndex, int rowIndex,
			Properties prop, Map<String, TreeSet<String>> keyBranchCodeMap,
			Map<String, String> keyNameMap,
			Map<String, Integer> numOverDueCaseAllBranch_A,
			Map<String, Integer> numTotalIntroCaseAllBranch_B,
			Map<String, BigDecimal> amtOverDueAllBranch_C,
			Map<String, BigDecimal> amtTotalIntroAllBranch_D,
			Map<String, Integer> numOverDueCaseBranch_E,
			Map<String, Integer> numIntroCaseBranch_F,
			Map<String, BigDecimal> amtOverDueBranch_G,
			Map<String, BigDecimal> amtIntroBranch_H,
			Map<String, Integer> numTotalIntroCaseBranch)
			throws RowsExceededException, WriteException;

	void setBodyContentForDetailOverdueCase(WritableSheet sheet, int colIndex,
			int rowIndex, Properties prop, List<Map<String, Object>> overdueList)
			throws RowsExceededException, WriteException;

	Map<String, Integer> getHeaderMapForDetailOverdueCase();

	Map<String, Integer> getTitleMap1();

	Map<String, Integer> getTitleMap2();

	Map<String, Integer> getTitleMap3();

	Map<String, Integer> getHeaderMapForLandsmenRankingList();

	void setBodyContentForLandsmenRankingList(WritableSheet sheet,
			int colIndex, int rowIndex, Properties prop,
			List<Map<String, Object>> overdueList,
			Map<String, Integer> numTotalIntroCaseAllBranch_B) throws WriteException;

	Map<String, Integer> getTitleMap4();

	Map<String, Integer> getHeaderMapForBranchRankingByLandsmenList();
	
	void setBodyContentForBranchRankingByLandsmenList(WritableSheet sheet,
			int colIndex, int rowIndex, Properties prop,
			Map<String, List<String>> sortedTop3BrNoMap,
			Map<String, String> keyNameMap,
			Map<String, Integer> numIntroCaseBranch_F,
			Map<String, Integer> numTotalIntroCaseBranch_I, Map<String, String> landsmanNoMap,
			Map<String, Integer> numTotalIntroCaseAllBranch_B)
			throws WriteException;

	Map<String, Integer> getTitleMap5();

	Map<String, Integer> getHeaderMapForTop3BranchRankingList();

	void setBodyContentForsetBodyContentForTop3BranchRankingList(
			WritableSheet sheet, int colIndex, int rowIndex, Properties prop,
			Map<String, Integer> numTotalIntroCaseBranch_I)
			throws WriteException;

	Map<String, Integer> getTitleMap6();

	Map<String, Integer> getHeaderMapForNormalLandsmenDetailList();

	void setBodyContentForNormalLandsmenDetailList(WritableSheet sheet,
			int colIndex, int rowIndex, Properties prop,
			List<Map<String, Object>> overdueList) throws WriteException;

	void setTitleContentOfUnit(WritableSheet sheet, Properties prop,
			int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex)
			throws WriteException;

	Map<String, Integer> getTitleMapOfDataPeriod();

	void setTitleContentOfDataPeriod(WritableSheet sheet, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, String fromDate, String toDate) throws WriteException;

	int getCount(Map<String, Integer> map, String key);

}
