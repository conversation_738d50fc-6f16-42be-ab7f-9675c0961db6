$(function() {
	var _handler = "lms1700m01formhandler";
	var is_flowClass_throughBr = "N";
	var gBrCtlType = ""; 	//分行覆審種類
	var show_condition = false;
	var show_branchComm = false;
	String.prototype.startsWith = function(str){
		    return (this.match('^' + str) == str)
	}
	if(viewstatus=="01A"){
	}else{
		show_condition = true;
	}
	
	if(viewstatus=="010" || viewstatus=="020" || viewstatus.indexOf("050")>=0  || viewstatus.indexOf("060")>=0 ){
		show_branchComm = true;
	}
	
	if( $("#pageInit_is_flowClass_throughBr").length>0  ){
		$.ajax({
	        type: "POST", async: false, handler: _handler,
	        data: {
	            formAction: "query_is_flowClass_throughBr"
	        },
		}).done(function(obj){
			is_flowClass_throughBr = obj.is_flowClass_throughBr||'N';

			if(is_flowClass_throughBr=="Y"){
				$("#sign_cntR").change(function(){
			        var $target = $("#new_sign_R_L4Span");
			        //清空原本的
			        $target.empty();
			        
			        var newdiv = "";
			        var val = parseInt($(this).val(), 10);
			        if (val > 1) {
			            for (var i = 2, count = val + 1; i < count; i++) {
			            	var idName = "sign_R_L4_"+i;
			            	
			                newdiv+=  
			                ("<div>" 
			                + i18n.lms1700m01['label.signSeqDescPrefix'] + i +i18n.lms1700m01['label.signSeqDescPostfix']
			                +"&nbsp;&nbsp;&nbsp;<select id='"+idName+"' name='"+idName+"' class='selectSign_R_L4'/>&nbsp;"
			                +"</div>");
			            }
			            $target.append(newdiv);
			            
			            var copyFrom = $("#sign_R_L4_1").html();//get srcElm html content
			            $(".selectSign_R_L4").html(copyFrom);
			        }
			    });
			}
		});
	}

	if(true){
		if( $("#_div_impBySelOrDate").length>0){
			//指定 label 的說明
			var $frm = $("#_div_impBySelOrDate_form");
			$frm.find("span#desc_retrialDate2").val( i18n.lms1700v01['L170M01A.retrialDate']);
			
			//先把輸入欄位隱藏
			$frm.find("#_div_impBySelOrDate_date").hide();
			
			$frm.find("[name='impBySelOrDate']").change(function(){
				var val = $frm.find("[name='impBySelOrDate']:checked").val();
				
				if(val=="1"){//依勾選之覆審報告表
					$frm.find("#_div_impBySelOrDate_date").hide();
				}else if(val=="2"){
					$frm.find("#_div_impBySelOrDate_date").show();		
				}
			});
			
			$frm.find("[name='impBySelOrDate'][value=1]").prop("checked", "checked").trigger('change');
		}
	}
		
	var my_colModel = [];	
	my_colModel.push({ name: 'oid', hidden: true });
	my_colModel.push({ name: 'mainId', hidden: true });
	my_colModel.push({ name: 'mainDocStatus', hidden: true });
	if(viewstatus.indexOf("|")>0){//複合的狀態，才顯示 docStatus
		my_colModel.push({colHeader : i18n.lms1700v01['L170M01A.docStatus'],name : 'docStatus', width : 60, sortable : true});
	}
	my_colModel.push({colHeader : i18n.lms1700v01['L170M01A.retrialDate'],name : 'retrialDate', align : "center", width : 65, sortable : true});
	if(userInfo && (userInfo.unitNo || '').startsWith('9')){//9開頭的分行，才顯示分行別
		my_colModel.push({colHeader : i18n.lms1700v01['L170M01A.ownBrId'],name : 'ownBrId', width : (viewstatus=="01A"?80:50), sortable : true});	
	}
	if(viewstatus=="01A" || viewstatus=="010"){
		my_colModel.push({colHeader : i18n.lms1700v01['L170M01A.projectNo'],name : 'projectNo', width : 50, sortable : true});	
	}
	
	my_colModel.push({colHeader : i18n.lms1700v01['L170M01A.custId'],name : 'custId', width : 90, sortable : true, formatter : 'click', onclick : openDoc});
	my_colModel.push({colHeader : i18n.lms1700v01['L170M01A.custName'],name : 'custName', width : 100, sortable : true});
	
	
	//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	$.ajax({
        type: "POST",
        handler: "lms1800formhandler",
		async: false ,
        data: {	 'formAction': 'getCtlTypeByBrNo'
        },
	}).done(function(responseData){
		gBrCtlType = responseData.ctlType;
		//if(gBrCtlType == "Z"){
			// 主辦+自辦覆審
			my_colModel.push({colHeader : i18n.lms1700v01['L180M01B.ctlType'],name : 'ctlType', align : "center", width : 50, sortable : true});
		//}
	});
      
	
	
	
	my_colModel.push({
		colHeader : i18n.lms1700v01['label.mLoanPerson'],name : 'mLoanPerson', align : "center", width : 45, sortable : true,
        formatter: function(value){
        	if(value == "Y"){
        		return "<img src='webroot/img/lms/M.png'>";
        	}else if(value == "N"){
        		return "";
        	}else{
        		return value;
        	}
        }
	});
	my_colModel.push({colHeader : i18n.lms1700v01['L170M01A.lastRetrialDate'],name : 'lastRetrialDate', width : 65, sortable : true});
	if(viewstatus=="01A"){
		my_colModel.push({
			colHeader : i18n.lms1700v01['label.upFlag'],name : 'upFlag', align : "center", width : 40, sortable : false,
            formatter: function(value){
            	if(value == "N"){
            		return "<img src='webroot/img/lms/X.png'>";
            	}else if(value == "Y"){
            		return "<img src='webroot/img/lms/V.png'>";
            	}else{
            		return value;
            	}
            }
		});	
	}
	
	my_colModel.push({colHeader : i18n.lms1700v01['label.retrialStaff'],name : 'retrialStaff', width : 60, sortable : false});
	
	if(viewstatus=="01A"){
		my_colModel.push({colHeader : i18n.lms1700v01['label.nCkdFlag'],name : 'nCkdFlag', width : 45, sortable : true});	
	}
	if(show_condition){
		my_colModel.push({colHeader : i18n.lms1700v01['L170M01F.condition'],name : 'condition', width : 130, sortable : false});	
	}
	if(show_branchComm){
		my_colModel.push({colHeader : i18n.lms1700v01['L170M01F.branchComm'],name : 'branchComm', width : 130, sortable : false});
	}
	
	//J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
	if(viewstatus=="01A"){
		my_colModel.push({colHeader : "RPA結果",name : 'status', width : 60, sortable : false});
	}
	
	 
	
	var the_sortname = 'retrialDate|ownBrId|projectNo|custId'; // 預設排序
	var the_sortorder = "desc|asc|asc|asc";
	
	var gridview_param = "";
    if( $("#gridview_param").find("[name=grid_flag]").length>0  ){
    	gridview_param = $("#gridview_param").find("[name=grid_flag]").val();
    }
    
	var grid = $("#gridview").iGrid({
		handler : 'lms1700gridhandler',
		height : 350,
		rowNum:100,
		multiselect : true,
		shrinkToFit: false,
		sortname: the_sortname, 
        sortorder: the_sortorder,
		postData : {
			docStatus : viewstatus,
			'gridview_param' : gridview_param,
			formAction:"queryL170M01A"
		},
		colModel : my_colModel
	});
	
	$("#buttonPanel").find("#btnFilter").click(function(){
		FilterAction.openBox();
	}).end().find("#btnPrint").click(function(){
      	var oid_arr = gridSelectOidArr();
      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
		// J-107-0254_09301_B1001  配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
		var ctltypeHasC = gridHasCtltypeC();
		if (ctltypeHasC == "N") {
			printL170M01A(oid_arr.join("|"));
		} else {
			$.form.submit({
                url: "../simple/FileProcessingService",
                target: "_blank",
                data: {
                    'rptOid': oid_arr.join("|"),
                    'isTwoPhase':'Y',     
                    'printR03':'N',
                    'fileDownloadName': "lms1700r01.pdf",
                    serviceName: "lms1700r01rptservice"     
                }
            }); 
		}
	}).end().find("#btnBatchApproved").click(function(){//受檢行-待覆核 
		proc_btnBatchApproved();
	}).end().find("#btnBatchTask").click(function(){//覆審組-編製中
		proc_btnBatchTask();
    	
	}).end().find("#btnAdd").click(function(){
		proc_btnAdd();
		
	}).end().find("#btnDelete").click(function(){
		proc_btnDelete();
		
	}).end().find("#btnMaintain").click(function(){
		proc_btnMaintain();
		
	}).end().find("#btnSendToExamUnit").click(function(){//覆審組-待覆核
		proc_btnSendToExamUnit();
				
	}).end().find("#btnAllSend").click(function(){//已覆核未核定
		var oid_arr = gridSelectOidArr();
      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
		
      	var _id = "_div_btnAllSend";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />核定</label></p>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380, height: 180, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    $.thickbox.close();
                    
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    var opts = {};
                    if(val=="1"){
                    	var cnt = 0;
                      	var ok_cnt = 0;
                		var target = oid_arr.length;
                			
                		var theQueue = $({});
                		$.each(oid_arr,function(k, v_oid) {                                            			
                			theQueue.queue('myqueue', function(next) {
                				$.ajax({
                		            type: "POST",
                		            handler: "lms1700m01formhandler",
									action: "flowAction",
                		            data:{ 'mainOid': v_oid,
										   'mainDocStatus':'050',
										   'addMetaDesc':'Y',
										   'decisionExpr':'to_已覆核已核定'} ,                
                				}).done(function(){
                					ok_cnt++;
                		        }).always(function(){//用 complete(不論done, fail)
                		        	cnt++;
                  					if(cnt==target){  						
                  						$("#gridview").trigger("reloadGrid");
                  						if(cnt > ok_cnt){
                  							API.showMessage("請開啟個別的覆審報告表 ，執行「"+i18n.lms1700m01['button.Update412']+"」");
                  						}
                      				}
                  					//---
                  					//把 next() 寫在 finish 的 callback 裡
                  					//才會 1個 url 抓到 response 後,再get下1個
                  					//不然會1次跳 N 個出來
                  					next();
                		        });				
                			});                                            			 
                		});
                		theQueue.dequeue('myqueue');
                    }
                   
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
    });
	
	function proc_btnAdd_branch(){
		var my_dfd = $.Deferred();

		var _id = "_div_proc_btnAdd_branch";
		var _form = _id+"_form";
		
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' width='100%' border='1' cellspacing='0'  cellpadding='0' >");
			dyna.push("	<tr>");
			dyna.push("	<td class='hd1' style='width:30%'><th:block th:text='#{'L170M01A.ownBrId'}'>分行別</th:block></td>");
			dyna.push("	<td style='width:70%'><select id='add_ownBrId' name='add_ownBrId' class='required'></select></td>");
			dyna.push("	</tr>");
			
			//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			//gBrCtlType -> from document.ready
			// J-107-0254_09301_B1001  配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
			// 覆審種類改下拉選單
			if(gBrCtlType == "Z"){
				//非只有主辦覆審---自辦覆審  或 主辦+自辦覆審
				dyna.push("	<tr >");  
				dyna.push("	<td class='hd1' style='width:30%'>覆審種類</td>");
				dyna.push("	<td style='width:70%'>");
				dyna.push("	 <select id='_ctlType' name='_ctlType' comboType='2'></select>");
				dyna.push("	</td>");
				dyna.push("	</tr>");
			}
					
//			$.ajax({
//	            type: "POST",
//	            handler: "lms1800formhandler",
//				async: false ,
//	            data: {	 'formAction': 'getCtlTypeByBrNo'
//	            },
//	            success: function(responseData){
//					
//					gBrCtlType = responseData.ctlType;
//					
//					if(gBrCtlType == "Z"){
//						//非只有主辦覆審---自辦覆審  或 主辦+自辦覆審
//						dyna.push("	<tr >");  
//						dyna.push("	<td class='hd1' style='width:30%'>覆審種類</td>");
//						dyna.push("	<td style='width:70%'>");
//						dyna.push("	<input type='radio' id='ctlType' name='ctlType' value='A' checked='checked' /><wicket:message key='L180M01B.ctlType_A'>一般覆審/土建融實地覆審</wicket:message><br> ");
//						dyna.push("	<input type='radio'              name='ctlType' value='B'                   /><wicket:message key='L180M01B.ctlType_B'>董事會(或常董會)權限案件實地覆審</wicket:message> ");
//						dyna.push("	</td>");
//						dyna.push("	</tr>");
//					}
//	            }
//	        });  
//			
			
			
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		     
		     $.ajax({
		    	 type: 'post',
				 handler: 'lms1800formhandler',
				 data:{'formAction':'queryBranch'},
			 }).done(function(obj){
				 var _addSpace = false;
					
				$.each(obj.itemOrder, function(idx, brNo) {
					var currobj = {};
					var brName = obj.item[brNo];
					currobj[brNo] = brName;
					var form = $("#"+_form); 
					form.find("#add_ownBrId").setItems({ item: currobj, format: "{value} {key}", clear:false, space: (_addSpace?(idx==0):false) });
				});	
			 })	 

			// J-107-0254_09301_B1001  配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
			// 覆審種類下拉選單
			if (gBrCtlType == "Z") {
				$.ajax({
					type: 'post',
					handler: 'lms1800formhandler',
					data: {
						'formAction': 'getCtlTypeByBrNo'
					},
				}).done(function(obj){
					$.each(obj.selItem, function(itemName, kvMap){
						var chooseItem = $("#" + encodeURI(itemName));
						var _addSpace = false;
						if (chooseItem.prop("space") == "true") {
							_addSpace = true;
						}
						var _fmt = "{key}";
						if (chooseItem.prop("myShowKey") === "Y") {
							_fmt = "{value}" + sep + "{key}";
						}
						$.each(obj.selItemOrder[itemName], function(idx, kVal){
							var currobj = {};
							currobj[kVal] = kvMap[kVal];
							
							chooseItem.setItems({
								item: currobj,
								format: _fmt,
								clear: false,
								space: (_addSpace ? (idx == 0) : false)
							});
						});
					});
				});
			}
		}
		//clear data
		//$("#"+_form).reset();
		
		$("#"+_id).thickbox({
	       title: i18n.lms1700v01["L170M01A.ownBrId"], width: 400, height: 200, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
           buttons: {
               "sure": function(){
                   if (!$("#"+_form).valid()) {
                   	   return;
                   }
                   var add_ownBrId = $("#"+_form).find("[name='add_ownBrId']").val();
				   
				   //J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				   //只有主辦覆審預設為A，自辦覆審  或 主辦+自辦覆審抓radio結果
				   //var ctlType = gBrCtlType !="Z" ? gBrCtlType : $("#"+_form).find("[name='ctlType']:radio:checked").val();
				   // J-107-0254_09301_B1001  配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
				   // 覆審種類下拉選單
				   var ctlType = gBrCtlType !="Z" ? gBrCtlType : $("#_ctlType").val();
				   ilog.debug('ctlType - ' + ctlType);
	               $.thickbox.close();
	               my_dfd.resolve({'ownBrId':add_ownBrId,'ctlType':ctlType});
               },
               "cancel": function(){
            	   $.thickbox.close();
            	   my_dfd.reject();
               }
           }
		});		
    	return my_dfd.promise();
	}
	function run_addL170M01A(confirmMsg){
		var my_dfd = $.Deferred();
		if(confirmMsg==""){
			my_dfd.resolve();
		}else{
			API.confirmMessage(confirmMsg, function(result){
	            if (result) {
	            	my_dfd.resolve();    	
	            }else{
	            	my_dfd.reject();
	            }
			});
		}
    	return my_dfd.promise();
	}
	function proc_btnAdd(){
		proc_btnAdd_branch().done(function(json){
			AddCustAction.open({
	    		handler: _handler,
				action : 'chkL170M01A',
				data : {
	                ownBrId: json.ownBrId,
					ctlType: json.ctlType
	            },
				callback : function(json_chkL170M01A){
					//關掉 AddCustAction 的 thickbox
	            	$.thickbox.close();
	            	//---------------------
	            	run_addL170M01A(json_chkL170M01A.confirmMsg).done(function(){
						$.ajax({
	                        type: "POST",
	                        handler: _handler,
							action: "addL170M01A",
	                        data: json_chkL170M01A,  
						}).done(function(json){
							grid.trigger("reloadGrid");
						});           
	            	});	            	
				}
			});	
		});
	}
	function proc_btnDelete(){
		API.confirmMessage(i18n.def["confirmDelete"], function(result){
            if (result) {
            	var oid_arr = gridSelectOidArr();
            	var opts = {};
            	if(oid_arr.length==0){   	 		
           	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
        	 		return;
        	 	}
            	var failmsgarr = [];
            	var cnt = 0;
            	
           	 	$.each(oid_arr, function(idx, oid){
               	 	$.ajax({
                        type: "POST",
                        handler: _handler, action: "deleteMeta",
                        data: $.extend({
                        	mainOid: oid, 
                        	mainDocStatus: viewstatus 
                        }, (opts || {})),  
					}).done(function(json){
						cnt++;

						if(json.failmsg && json.failmsg.length>1){
							failmsgarr.push(json.failmsg);
						}

						if(cnt>=oid_arr.length){
							grid.trigger("reloadGrid");
							$.thickbox.close();
							
							if(failmsgarr.length==0){
								API.showMessage(i18n.def.confirmDeleteSuccess);
							}else{
								API.showErrorMessage(failmsgarr.join("<br/><br/>"));                        			
							}
						}
					});            
           	 	});
        	}
    	});
	}
	function proc_btnMaintain(){
		var oid_arr = gridSelectOidArr();
      	if(oid_arr.length==0 || oid_arr.length>1){
      		API.showMessage("請選取一筆資料");
      		return;
      	}
		
		$.ajax({type: "POST", handler: _handler,
            data: {
            	oid:oid_arr[0], 
                formAction: "newL181M01A"                	
            },
		}).done(function(json_newL181M01A){
			$.form.submit({
				url : '../lrs/lms1810m01/01',
				data : json_newL181M01A,
				target : json_newL181M01A.oid
			});
		})
	}
	function proc_btnSendToExamUnit(){
		var oid_arr = gridSelectOidArr();
      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
		var _id = "_div_btnSendToExamUnit";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.lms1700m01["ui_lms1700.msg10"]+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='3' class='required' />"+i18n.lms1700m01["ui_lms1700.msg11"]+"</label></p>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380, height: 180, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    var opts = {};
                    if(val=="1"){
                    	opts = {'decisionExpr':'to_編製中_分行端'};
                    }else if(val=="3"){
                    	opts = {'decisionExpr':'backto_編製中_覆審組'};
                    }
                    
                    if(true){
                    	var theQueue = $({});
                		var cnt = 0;
                		$.each(oid_arr,function(k, _oid) {                                            			
                			theQueue.queue('myqueue', function(next) {
                				 
                				flowAction($.extend(opts, {'mainOid':_oid, 'mainDocStatus': viewstatus})
                				).always(function(){//用 complete(不論done, fail)
                		        	cnt++;
                  					
                		        	grid.trigger("reloadGrid");
                      				if(cnt==oid_arr.length){
                      					$.thickbox.close();
                      					API.showMessage(i18n.def.runSuccess);
                      				}
                  					//---
                  					next();
                		        });				
                			});                                            			 
                		});
                		theQueue.dequeue('myqueue');
                    }
                   
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	}
	function proc_btnBatchTask(){
		
		var _id = "_div_btnBatchTask";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+"整批列印"+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+"整批重新引進覆審報告表授信資料"+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='3' class='required' />"+"整批產生前次覆審內容及意見一覽表"+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='4' class='required' />"+"整批列印前次覆審報告表"+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='5' class='required' />"+"整批引進最近三次財務資料"+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='6' class='required' />"+"修改覆審日期"+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='7' class='required' />"+"整批引進前次覆審報告表"+"</label></p>");
			if(is_flowClass_throughBr=="Y"){
				dyna.push("<p><label><input type='radio' name='decisionExpr' value='8' class='required' />"+"整批移受檢單位登錄"+"</label></p>");	
			}
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='9' class='required' />"+"重引覆審項目「應負責經理」"+"</label></p>");
			//J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			//J-106-0123-001 Web e-Loan國內企金覆審增加覆審項目「立約當日是否依規定查詢銀行法及金控法44條利害關係人之資料後再行簽約」
			//J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
			// J-107-0290_09301_B1001 CTLTYPE_主辦覆審 第18項拆項 N025 
			//J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
			// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
			// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
			// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
			// J-113-0204  新增及修正說明文句
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='10' class='required' />"+"修改覆審報告表格式為最新版本(2024.06)"+"</label></p>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		var v_height = 330 +( $("#"+_form+" input[name=decisionExpr]").length - 7)*30;
			
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmRun"],
	        width: 400, height: v_height, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if (true) {
    	            	$.thickbox.close();
    	            	
    	            	if(val=="1"){
	            			var oid_arr = gridSelectOidArr();
	            			if(oid_arr.length==0){
	            	      		API.showMessage(i18n.def.action_006);//請先選擇需「列印」之資料列
	            	      		return;
	            	      	}
							// J-107-0254_09301_B1001  配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
							var ctltypeHasC = gridHasCtltypeC();
							if (ctltypeHasC == "N") {
								printL170M01A(oid_arr.join("|"));
							} else {
								$.form.submit({
					                url: "../simple/FileProcessingService",
					                target: "_blank",
					                data: {
					                    'rptOid': oid_arr.join("|"),
					                    'isTwoPhase':'Y',     
					                    'printR03':'N',
					                    'fileDownloadName': "lms1700r01.pdf",
					                    serviceName: "lms1700r01rptservice"     
					                }
					            }); 
							}
                        }else if(val=="2"){
                        	_ALMS17003();
                        }else if(val=="3"){
                        	_ALMS17004();
                        }else if(val=="4"){
                        	_ALMS17005();
                        }else if(val=="5"){                        	
                        	_ALMS17006WEB();                    	
                        }else if(val=="6"){
                        	var rowDataIdx_arr = gridSelectRowDataIdxArr();
                          	if(rowDataIdx_arr.length==0){
                          		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
                          		return;
                          	}
                        	batchTask_chgRetrialDate(rowDataIdx_arr);
                        }else if(val=="7"){
                        	batchTask_impPreL170M01A();
                        }else if(val=="8"){
                        	batchTask_moveToBranch();
                        }else if(val=="9"){
                        	batchTask_update_ptMgrId();
						}else if(val=="10"){
							//J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
                        	batchTask_update_L170M01A_201611();	
                        }
    	            }                  
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	}
	
	/**
	 * 回傳值可能有 2 種
	 * ● oids
	 * ● retrialDate , unitNo[供查詢 l170a01a.authUnit]
	 */
	function impBySelOrDate(){
		var my_dfd = $.Deferred();
		impBySelOrDate_getParam().done(function(params){
			$.ajax({
	            handler: _handler,
				action: "check_impBySelOrDate", 
	            data: ( params||{} ),
			}).done(function(json_check_impBySelOrDate){
				my_dfd.resolve(params);
			})
    	});    		
		return my_dfd;
	}
	function impBySelOrDate_getParam(){
		var my_dfd = $.Deferred();    
		
		var _id = "_div_impBySelOrDate";
		var _form = _id+"_form";
		var $frm = $("#"+_form);
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: "", width: 350, height: 250, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	        buttons: {
	             "sure": function(){
	            	
	                if (! $frm.valid()) {
	                     return;
	                }
	                var impBySelOrDate = $frm.find("[name='impBySelOrDate']:checked").val();
	                var params = {};
	                if(impBySelOrDate=="1"){
	                	var oid_arr = gridSelectOidArr();
	            		if(oid_arr.length==0){
	                  		API.showMessage(i18n.def.action_005);
	                  		return;
	                  	}
	            		params['oids'] = oid_arr.join("|");
	            		//~~~~~~
		                $.thickbox.close();
		                my_dfd.resolve(params);
	                }else if(impBySelOrDate=="2"){
	                	var retrialDate = $frm.find("#retrialDate2").val();
		                if(retrialDate==''){
		                	API.showErrorMessage(i18n.lms1700v01['ui_lms1700.msg09']+i18n.lms1700v01['L170M01A.retrialDate']);
		                	return;	
		                }
		                params['retrialDate'] = retrialDate;
		                params['unitNo'] = userInfo.unitNo;
		                //~~~~~~
		                $.thickbox.close();
		                my_dfd.resolve(params);
	                } 
	             },
	             "cancel": function(){
	             	$.thickbox.close();
	             	my_dfd.reject();
	             }
	         }
	    });
		
		return my_dfd.promise();
	}
	
	//整批重新引進覆審報告表授信資料
	function _ALMS17003(){
		impBySelOrDate().done(function(params){
			var my_timeout = 7200000;//ms
			if(true){//先延長 timer，不然在處理過程中，會 timeout
				timer_long_ajax_beg(my_timeout);	
			}
    		$.ajax({
                handler: _handler,
				action: "callBatch",
				timeout: my_timeout,
                data: $.extend({
					'act':'imp_l170m01b',
					'jq_timeout': (my_timeout/1000)}, params ),
			}).done(function(json_callBatch){
				if(true){//恢復 timer
					timer_long_ajax_end();
				}
				if(json_callBatch.r.response==="SUCCESS"){
					API.showMessage(i18n.def.runSuccess);
				}else{
					API.showErrorMessage(json_callBatch.r.response);
				}
			});
    	});
	}
	
	//整批產生覆審結果彙整一覽表
	function _ALMS17004(){
		impBySelOrDate().done(function(params){
			$.ajax({
                handler: _handler,
				action: "check_befMeta",
                data: (params||{} ),
			}).done(function(json_check_befMeta){
				dfd_showErrorMsg_ifHas(json_check_befMeta.msg).done(function(){
					if(json_check_befMeta.has_befMeta_oids != ""){
						$.form.submit({
				        	url: __ajaxHandler,
				     		target : "_blank",
				     		data : {
				     			_pa : 'lmsdownloadformhandler',
				     			'xlsKind': '1',
				                'oids': json_check_befMeta.has_befMeta_oids,
				     			'fileDownloadName' : "data.xls",
				     			'serviceName' : "lms1700xlsservcie"
				     		}
				     	 });                		
					}		
				}); 
			})
    	});
	}
	//整批列印前次覆審報告表
	function _ALMS17005(){
		impBySelOrDate().done(function(params){
			$.ajax({
                handler: _handler,
				action: "check_befMeta",
                data: (params||{} ),
			}).done(function(json_check_befMeta){
				dfd_showErrorMsg_ifHas(json_check_befMeta.msg).done(function(){
					if(json_check_befMeta.has_befMeta_oids != ""){
						printL170M01A( json_check_befMeta.befMeta_oids );                		
					}	
				});
			});	
    	});
	}
	//整批引進最近三次財務資料
	function _ALMS17006WEB(){
		impBySelOrDate().done(function(params){
			var my_timeout = 7200000;//ms
			if(true){//先延長 timer，不然在處理過程中，會 timeout
				timer_long_ajax_beg(my_timeout);	
			}
    		$.ajax({
                handler: _handler,
				action: "callBatch",
				timeout: my_timeout,
                data: $.extend({
					'act':'imp_l170m01c',
					'jq_timeout': (my_timeout/1000)}, params ),
			}).done(function(json_callBatch){
				if(true){//恢復 timer
					timer_long_ajax_end();
				}
				if(json_callBatch.r.response==="SUCCESS"){
					API.showMessage(i18n.def.runSuccess);
				}else{
					API.showErrorMessage(json_callBatch.r.response);
				}
			});
    	});
	}
	
	//修改覆審日期
	function batchTask_chgRetrialDate(rowDataIdx_arr){
		var _id = "_div_chgRetrialDate";
		var _form = _id+"_form";
		var $frm = $("#"+_form);
		
		$frm.find("span#desc_retrialDate").val( i18n.lms1700v01['L170M01A.retrialDate']);
    	$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: "", width: 350, height: 100, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	        buttons: {
	             "sure": function(){
	            	
	                if (! $frm.valid()) {
	                     return;
	                }
	                var retrialDate = $frm.find("#retrialDate").val();
	                 
	                if(true){
	                	var theQueue = $({});
	            		var cnt = 0;
	            		$.each(rowDataIdx_arr,function(k, rowDataIdx) {
	            			theQueue.queue('myqueue', function(next) {
	            				var data = grid.getRowData(rowDataIdx);
	            				 $.ajax({
	        	                     handler: _handler,
									 type: "POST",
									 dataType: "json",
	        	                     data: {
	        	                         formAction: "chgRetrialDate",
	        	                         'mainOid': data.oid,
	        	                         'mainDocStatus':data.mainDocStatus,
	        	                         'retrialDate':retrialDate
	        	                     },
	        	                 }).always(function(){//用 complete(不論done, fail)
	            		        	cnt++;
	              					
	            		        	//不是每次更新 grid.trigger("reloadGrid");
	                  				if(cnt==rowDataIdx_arr.length){
	                  					grid.trigger("reloadGrid");
	                  					$.thickbox.close();
	                  					API.showMessage(i18n.def.runSuccess);
	                  				}
	              					//---
	              					next();
	            		        });				
	            			});                                            			 
	            		});
	            		theQueue.dequeue('myqueue');
	                }  
	                
	             },
	             "cancel": function(){
	             	$.thickbox.close();
	             }
	         }
	    });
	}
	//整批引進前次覆審報告表
	function batchTask_impPreL170M01A(){
		impBySelOrDate().done(function(params){
			$.ajax({
                handler: _handler,
				action: "check_befMeta",
                data: (params||{} ),
			}).done(function(json_check_befMeta){
				dfd_showErrorMsg_ifHas(json_check_befMeta.msg).done(function(){
					if(json_check_befMeta.has_befMeta_oids != ""){
						var my_timeout = 7200000;//ms
						if(true){//先延長 timer，不然在處理過程中，會 timeout
							timer_long_ajax_beg(my_timeout);	
						}                		
						$.ajax({
				            handler: _handler,
							action: "importBefText_batch",
							timeout: my_timeout,
				            data: {'oids':json_check_befMeta.has_befMeta_oids},
						}).done(function(json_importBefText_batch){
							if(true){//恢復 timer
								timer_long_ajax_end();
							}
							if(json_importBefText_batch.msg){
								API.showErrorMessage(json_importBefText_batch.msg);
							}else{
								API.showMessage(i18n.def.runSuccess);
							}
						})
					}
				});
			});
    	});
	}
	
	function batchTask_moveToBranch(){
		var oid_arr = gridSelectOidArr();
		if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);
      		return;
      	}
		
		API.confirmMessage("是否整批移受檢單位登錄？<br/>(不是您為覆審經辦的案件，將不會呈核)", function(r_dialog){
            if (r_dialog) {            	
            	signContentL170M01G_V().done(function(signParam){
            		$.ajax({
    		            type: "POST",
						handler: _handler,
						action: "do_SignAndFlow",
    		            data:$.extend(signParam || {} ,
    		            	{    'oids': oid_arr.join("|")
    		            		,'mainDocStatus':viewstatus
    		            		,'decisionExpr':'to_待覆核_覆審組'
    		            	}),
					}).done(function(json){
						if(json.msg){
							API.showMessage(json.msg);
						}
						if(json.doReloadGrid=="Y"){
							$("#gridview").trigger("reloadGrid");
						}
					})
            	});
            }
		});
	}
	
	function batchTask_update_ptMgrId(){
		impBySelOrDate().done(function(params){
			var my_timeout = 7200000;//ms
			if(true){//先延長 timer，不然在處理過程中，會 timeout
				timer_long_ajax_beg(my_timeout);	
			}
			var options = {};
			var my_dfd = $.Deferred();
			my_dfd.done(function(rtnPtMgrIdJSON){
				$.ajax({
	                handler: _handler,
					action: "callBatch",
					timeout: my_timeout,
	                data: $.extend({
						'act':'update_ptMgrId',
						'ptMgrId':rtnPtMgrIdJSON.ptMgrId,
						'jq_timeout': (my_timeout/1000)}, params ),
				}).done(function(json_callBatch){
					if(true){//恢復 timer
						timer_long_ajax_end();
					}
					if(json_callBatch.r.response==="SUCCESS"){
						API.showMessage(i18n.def.runSuccess);
					}else{
						API.showErrorMessage(json_callBatch.r.response);
					}
				});
			});
			RetrialPtMgrIdPanelAction.open(options, my_dfd);
    	});
	}
	
	//J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	//整批引進最近三次財務資料
	function batchTask_update_L170M01A_201611(){
		impBySelOrDate().done(function(params){
			var my_timeout = 7200000;//ms
			if(true){//先延長 timer，不然在處理過程中，會 timeout
				timer_long_ajax_beg(my_timeout);	
			}
    		$.ajax({
                handler: _handler, 
				action: "callBatch", 
				timeout: my_timeout,
                data: $.extend({
					'act':'update_l170m01a_rpid',
					'jq_timeout': (my_timeout/1000)}, params ),
			}).done(function(json_callBatch){
				if(true){//恢復 timer
					timer_long_ajax_end();
				}
				if(json_callBatch.r.response==="SUCCESS"){
					API.showMessage(i18n.def.runSuccess);
				}else{
					API.showErrorMessage(json_callBatch.r.response);
				}
			});
    	});
	}
	

	
	function signContentL170M01G_V(){
		var my_dfd = $.Deferred(); 
		var signRole = "R";
		var param = {'signRole': signRole};
		$.ajax({ 
			type: "POST",
			handler: _handler,
			data: $.extend({formAction: "getSignList_V"}, param),
		}).done(function(json_signContent){
				if(true){
					var condL4 = null;
					var condL5 = null;
					var $div = null;
					var tb_title = "";
					if(true){
						condL4 = ".selectSign_R_L4"; 
						condL5 = ".selectSign_R_L5"; 
						$div = $("#divSignContent_R"); 
						tb_title = i18n.lms1700m01["reviewBrn.2"];//覆審單位
					}
					
					var tempcondL4=$(condL4);
					var tempcondL5=$(condL5);
					tempcondL4.setItems({ item: json_signContent.l4_list, space: true });
					tempcondL5.setItems({ item: json_signContent.l5_list, space: true });

			      	//=========
					$div.thickbox({
			  	        title: i18n.lms1700m01["label.signature"]+"("+tb_title+")",
			  	        width: 580, height: 250, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
			              buttons: {
			                  "sure": function(){
			                  	var l4Arr = [];
			                  	var l5Arr = [];
			                  	
			                  	$.each( tempcondL4, function(idx,obj){
			                  		var tempObj=$(obj);
			                  		var val = tempObj.val();                        	
			                  		l4Arr.push( val );
			                  	});
			                
			                  	$.each( tempcondL5, function(idx,obj){
			                  		var tempObj=$(obj);
			                  		var val = tempObj.val();                        		
			                  		l5Arr.push( val );
			                  	});

			                  	var signParam = {'l4Arr':l4Arr.join("|"),'l5Arr':l5Arr.join("|")};
			                  	                        		
			                  	$.ajax({ 
									type: "POST",
									handler: _handler,
			              			data:$.extend({formAction: "checkSignList_V"}, signParam, param),
								}).done(function(json_checkSignList_V){
									my_dfd.resolve(signParam);
									$.thickbox.close();
								});
			                  },
			                  "cancel": function(){
			                  	my_dfd.reject();
			                  	$.thickbox.close();
			                  }
			              }
			  	    });   
				}
		});
		return my_dfd.promise();
	}
	
	/**
	 * 若在 showErrorMsg 後，又立刻發動 ajax
	 * 會導致 blockUI，無法把 message 的 thickbox 關掉
	 */
	function dfd_showErrorMsg_ifHas(msg){
		var my_dfd = $.Deferred();    
		if(msg){
    		API.showErrorMessage(msg, function(){ my_dfd.resolve(); });
    	}else{
    		my_dfd.resolve();
    	}
		return my_dfd.promise();
	}
	
	function proc_btnBatchApproved(){
		var oid_arr = gridSelectOidArr();
      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
		var _id = "_div_btnBatchApproved";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.lms1700m01["ui_lms1700.msg13"]+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.lms1700m01["ui_lms1700.msg12"]+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='3' class='required' />"+i18n.lms1700m01["ui_lms1700.msg11"]+"</label></p>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380, height: 180, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    var opts = {};
                    if(val=="1"){
                    	opts = {'decisionExpr':'to_已覆核未核定'};
                    }else if(val=="2"){
                    	opts = {'decisionExpr':'backto_編製中_分行端'};
                    }else if(val=="3"){
                    	opts = {'decisionExpr':'backto_編製中_覆審組'};
                    }
                    
                    if(true){
                    	var theQueue = $({});
                		var cnt = 0;
                		$.each(oid_arr,function(k, _oid) {                                            			
                			theQueue.queue('myqueue', function(next) {

                				flowAction($.extend(opts, {'mainOid':_oid, 'mainDocStatus': viewstatus})
                				).always(function(){//用 complete(不論done, fail)
                		        	cnt++;
                  					
                		        	grid.trigger("reloadGrid");
                      				if(cnt==oid_arr.length){
                      					$.thickbox.close();
                      					API.showMessage(i18n.def.runSuccess);
                      				}
                  					//---
                  					next();
                		        });				
                			});                                            			 
                		});
                		theQueue.dequeue('myqueue');
                    }
                   
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	}
	
	function gridSelectRowDataIdxArr(){
		return grid.getGridParam('selarrrow');		
	}
	function gridSelectOidArr(){
    	var rowId_arr = gridSelectRowDataIdxArr();
		var oid_arr = [];
   	 	for (var i = 0; i < rowId_arr.length; i++) {
			var data = grid.getRowData(rowId_arr[i]);
			oid_arr.push(data.oid);    			
        }
   	 	return oid_arr;
    }
	
	// J-107-0254_09301_B1001  配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
	function gridHasCtltypeC(){
    	var rowId_arr = gridSelectRowDataIdxArr();
		var has = "Y";	//預設只存在type C
   	 	for (var i = 0; i < rowId_arr.length; i++) {
			var data = grid.getRowData(rowId_arr[i]);
			if(data.ctlType!="C"){
				has = "N";	//任一列非type C 即顯示是否列印附表
				break;
			}  			
        }
   	 	return has;
    }
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler,
			action: "flowAction",
            data:( opts||{} ),
		}).done(function(json){
		});                
	}
	
	/**
	 * 同時印多份: 
	 * oid_arr.push(data.oid+"^"+data.mainId);
	 * rptOid: oid_arr.join("|")
	 */
	function printL170M01A(rptOid){
		var _id = "_div_printR03";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("		<p>是否同時列印覆審項目第15項附表？</p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='Y' class='required' />列印附表</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='N' class='required' />不印附表</label></p>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: '',
	        width: 380, height: 180, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	        buttons: {
	            "sure": function(){
	                if (!$("#"+_form).valid()) {
	                    return;
	                }
	                $.thickbox.close();
	                
	                var printR03 = $("#"+_form).find("[name='decisionExpr']:checked").val();                
	            	$.form.submit({
	                    url: "../simple/FileProcessingService",
	                    target: "_blank",
	                    data: {
	                        'rptOid': rptOid,
	                        'isTwoPhase':'Y',     
	                        'printR03':printR03,
	                        'fileDownloadName': "lms1700r01.pdf",
	                        serviceName: "lms1700r01rptservice"     
	                    }
	                });               
	               
	            },
	            "cancel": function(){
	                $.thickbox.close();
	            }
	        }
		});
	}

	function openDoc(cellvalue, options, rowObject){
		var postData = {
			'mainOid': rowObject.oid, 
			'mainId': rowObject.mainId,
			'mainDocStatus': rowObject.mainDocStatus
			//一般是用'mainDocStatus': viewstatus
			//但有複合 docStatus的狀況，可能 viewstatus 為 050|060
		}
		$.form.submit({ url:'../lrs/lms1700m01/01', data:postData, target:rowObject.oid});
	}
});
