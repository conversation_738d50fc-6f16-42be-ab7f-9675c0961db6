package com.mega.eloan.lms.lrs.flow;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.dao.L180M01BDao;
import com.mega.eloan.lms.eloandb.service.Lms412Service;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**<pre>
 * 覆審名單檔-流程
 * </pre>
 * @since  2011/9/23
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/23,irene,new
 *          </ul>
 */
@Component
public class LMS1805Flow extends AbstractFlowHandler {

	@Resource
	L180M01ADao l180m01aDao;
	
	@Resource
	L180M01BDao l180M01bDao;
	
	@Resource
	Lms412Service lms412Service;
	
	@Transition(node="確認", value="核定")
	public void accept(FlowInstance instance) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String instanceId = instance.getId().toString();
		L180M01A l180m01a = l180m01aDao.findByOid(instanceId);
		//寫入文件核定者
		l180m01a.setApprover(user.getUserId());
		l180m01a.setApproveTime(CapDate.getCurrentTimestamp());
		lms412Service.applyUpdate412(l180m01a.getMainId(), l180m01a.getBranchId(), l180m01a.getDataDate());
		l180m01aDao.save(l180m01a);
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return L180M01A.class;
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return RetrialDocStatusEnum.class;
	}
}