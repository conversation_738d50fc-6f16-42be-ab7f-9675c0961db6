<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="innerPageBody">
	        <script type="text/javascript">
	    		require(['pagejs/lms/LMS1025MAINPage'], function(){
	    			loadScript('pagejs/lms/LMS1025M00Page');
	    		});
	        </script>

            <div id="buttonPanel" class="button-menu funcContainer">
            </div>
            <div id="opendocBox">
                <div class="tit2 color-black">
                   <table width="100%">
            		<tr>
                		<td width="100%">
                			<th:block th:text="#{'doc.title'}">客戶基本資料表</th:block>：                			
                			<!-- 避免和評等文件, 簽報書的 titleInfo 重複-->
							<span class="color-blue" id="lms1025m00page_titleInfo" />
                		</td>
            		</tr>
        			</table>
                </div>	
				
               <div th:include="base/panels/OverSeaCLSOuterPanel :: panelFragmentBody"></div>
            </div>
		</th:block>
    </body>
</html>