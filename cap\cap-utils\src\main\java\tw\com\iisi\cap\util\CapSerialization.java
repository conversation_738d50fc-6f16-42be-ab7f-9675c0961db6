/* 
 * CapSerialization.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package tw.com.iisi.cap.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * <pre>
 * Object seriaization utilities
 * </pre>
 * 
 * @since 2003/5/20
 * <AUTHOR> <PERSON>
 * @version
 *          <ul>
 *          <li>2011/6/27,iristu,copy from gaia
 *          </ul>
 */
public class CapSerialization {

    /**
     * 壓縮
     */
    private boolean compress = false;

    /**
     * 建構子
     * 
     * @param compress
     *            是否壓縮
     */
    CapSerialization(boolean compress) {
        this.compress = compress;
    }

    private static CapSerialization inst = new CapSerialization(false);
    private static CapSerialization compressInst = new CapSerialization(true);

    /**
     * 建立實體
     * 
     * @return
     */
    public static CapSerialization newInstance() {
        return inst;
    }

    /**
     * 建立壓縮後實體
     * 
     * @return
     */
    public static CapSerialization newCompressInstance() {
        return compressInst;
    }

    /**
     * 判斷是否有壓縮
     * 
     * @return
     */
    public boolean isCompress() {
        return compress;

    }

    /**
     * 設置是否壓縮
     * 
     * @param b
     */
    public void setCompress(boolean b) {
        compress = b;
    }

    /**
     * compress byte array data with GZIP.
     * 
     * @param input
     *            the input data
     * @return the compressed data
     * @throws java.io.IOException
     */
    public byte[] compress(byte[] input) throws java.io.IOException {
        byte[] result = null;
        java.io.ByteArrayOutputStream baout = new java.io.ByteArrayOutputStream();
        GZIPOutputStream gzipout = new GZIPOutputStream(baout);
        gzipout.write(input);
        gzipout.finish();
        result = baout.toByteArray();
        return result;
    }

    /**
     * decompress byte array data with GZIP.
     * 
     * @param input
     *            the input compressed data
     * @return the decompress data
     * @throws java.io.IOException
     */
    public byte[] decompress(byte[] input) throws java.io.IOException {

        byte[] buf = new byte[2048];
        byte[] result = null;
        java.io.ByteArrayInputStream bain = new java.io.ByteArrayInputStream(input);
        GZIPInputStream gzipin = new GZIPInputStream(bain);
        ByteArrayOutputStream baout = new ByteArrayOutputStream();
        int size;
        while ((size = gzipin.read(buf)) != -1) {
            baout.write(buf, 0, size);
        }
        gzipin.close();
        result = baout.toByteArray();
        return result;
    }

    /**
     * Load data from savedData string
     * 
     * @param in
     *            the saved string
     * @return the original data
     */
    public Object loadData(String in) {
        return loadDataFromByteArray(CapString.hexStrToByteArray(in), compress);
    }

    /**
     * 從 Byte Array 中取得資料
     * 
     * @param in
     * @param compress
     */
    public Object loadDataFromByteArray(byte[] in, boolean compress) {
        if (in == null) {
            return null;
        }
        ByteArrayInputStream bais = null;
        ObjectInputStream ois = null;
        try {
            bais = compress ? new ByteArrayInputStream(decompress(in)) : new ByteArrayInputStream(in);

            ois = new ObjectInputStream(bais);
            Object o = ois.readObject();
            return o;
        } catch (Exception e) {
            e.getMessage();
        } finally {
            try {
                if (ois != null)
                    ois.close();
            } catch (Exception e) {
                e.getMessage();
            }
            try {
                if (bais != null)
                    bais.close();
            } catch (Exception e) {
                e.getMessage();
            }
        }
        return null;
    }

    /**
     * Serializate the object to string
     * 
     * @param o
     *            the input object
     * @return the object's serialized string
     */
    public String saveData(Object o) {
        return CapString.byteArrayToHexString(saveDataToByteArray(o, compress));
    }

    /**
     * 將資料以 Byte Array 形式儲存
     * 
     * @param o
     * @param compress
     */
    public byte[] saveDataToByteArray(Object o, boolean compress) {
        if (o == null) {
            return null;
        }
        ByteArrayOutputStream baos = null;
        ObjectOutputStream oos = null;
        try {
            baos = new ByteArrayOutputStream();
            oos = new ObjectOutputStream(baos);
            oos.writeObject(o);
            byte[] out = baos.toByteArray();
            oos.close();
            baos.close();
            return compress ? compress(out) : out;
        } catch (Exception e) {
            e.getMessage();
        } finally {
            try {
                if (oos != null) {
                    oos.close();
                }
            } catch (Exception e) {
                e.getMessage();
            }
            try {
                if (baos != null) {
                    baos.close();
                }
            } catch (Exception e) {
                e.getMessage();
            }
        }
        return null;
    }

}
