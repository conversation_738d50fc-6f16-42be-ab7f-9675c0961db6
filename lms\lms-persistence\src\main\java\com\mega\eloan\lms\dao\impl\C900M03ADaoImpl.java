package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C900M03ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M03A;

/** FTP_H檔  **/
@Repository
public class C900M03ADaoImpl extends LMSJpaDao<C900M03A, String>
	implements C900M03ADao {

	@Override
	public C900M03A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public C900M03A findByFnGenDateGenTime(String fn, String genDate, String genTime) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "fn", fn);
		search.addSearchModeParameters(SearchMode.EQUALS, "genDate", genDate);
		search.addSearchModeParameters(SearchMode.EQUALS, "genTime", genTime);
		return findUniqueOrNone(search);
	}
	
	@Override
	public C900M03A findByFnMaxGenDate(String fn){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "fn", fn);
		search.addOrderBy("genDate", true);
		search.addOrderBy("genTime", true);
		return findUniqueOrNone(search);		
	}
	
	@Override	
	public List<C900M03A> findByFnBfGenDate(String fn, String genDate){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "fn", fn);
		search.addSearchModeParameters(SearchMode.LESS_THAN, "genDate", genDate);
		return find(search);
		
	}
}