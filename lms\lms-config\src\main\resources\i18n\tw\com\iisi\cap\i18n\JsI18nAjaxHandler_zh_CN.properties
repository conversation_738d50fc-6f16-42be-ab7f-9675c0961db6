#---------------------------------------------#
# javascript commom.js use
#---------------------------------------------#
comboSpace=--\u8bf7\u9009\u62e9--
yes=\u662f
no=\u5426
all=\u5168\u90e8
close=\u5173\u95ed
cancel=\u53d6\u6d88
include=\u5f15\u7528
noData=\u67e5\u65e0\u6570\u636e\uff0c\u8bf7\u91cd\u65b0\u67e5\u8be2\u3002
timeout=\u670d\u52a1\u5668\u8054\u673a\u903e\u65f6\uff0c\u8bf7\u7a0d\u540e\u518d\u8bd5\u3002
connectError=\u670d\u52a1\u5668\u8054\u673a\u5931\u8d25\uff0c\u8bf7\u7a0d\u540e\u518d\u8bd5\uff0c\u6216\u68c0\u67e5\u7f51\u7edc\u662f\u5426\u6709\u95ee\u9898\u3002
sessionTimeout=\u60a8\u7684\u767b\u5165\u5df1\u7ecf\u8fc7\u671f\n\u8bf7\u7531\u5458\u5de5\u4e13\u7528\u7f51\u767b\u5165
loading=\u8fdc\u7a0b\u7cfb\u7edf\u300a\u8054\u673a\u4e2d\u300b\uff0c\u8bf7\u7a0d\u5f85
lastDBMonidyTime=\u4e3b\u673a\u6570\u636e\u5e93\u6700\u540e\u5f02\u52a8\u4eba\u5458
sure=\u786e\u5b9a
enter=\u8f93\u5165
compID=\u7edf\u4e00\u7f16\u53f7
megaID=\u7edf\u4e00\u7f16\u53f7
dupNo=\u91cd\u590d\u5e8f\u53f7
name=\u59d3\u540d
compName=\u59d3\u540d/\u540d\u79f0
query=\u67e5\u8be2
look=\u8c03\u9605
reQuery=\u91cd\u65b0\u5f15\u8fdb
reModify=\u91cd\u65b0\u7f16\u8f91
import=\u5f15\u8fdb
calculate=\u8ba1\u7b97
del=\u5220\u9664
print=\u6253\u5370
deleted=\u6b64\u6587\u4ef6\u5df2\u8bbe\u5b9a\u4e3a\u5220\u9664\u5e76\u4e14\u4e3b\u7ba1\u9000\u56de\u4e0d\u53ef\u6267\u884c\u4efb\u4f55\u52a8\u4f5c\uff0c\u8bf7\u56de\u4e3b\u753b\u9762\u91cd\u65b0\u67e5\u8be2\u6216\u65b0\u589e\u6848\u4ef6\u3002
newData=\u65b0\u589e
newCustomer=\u65b0\u5ba2\u6237
selectOption=\u9009\u9879
saveData=\u50a8\u5b58
accept=\u6838\u5b9a
return=\u9000\u56de
saveSuccess=\u50a8\u5b58\u6210\u529f
txnSuccess=\u4ea4\u6613\u6210\u529f!
runSuccess=\u6267\u884c\u6210\u529f
addSuccess=\u65b0\u589e\u6210\u529f
saveDataClose=\u50a8\u5b58\u540e\u79bb\u5f00
CloseWithoutSave=\u4e0d\u50a8\u5b58\u79bb\u5f00
confirmSaveLeave=\u662f\u5426\u786e\u5b9a\u50a8\u5b58\u540e\u79bb\u5f00?
closewindows=\u300c\u5173\u95ed\u300d \u6216 \u300c\u91cd\u6574\u300d \u5c06\u4f1a\u9057\u5931\u76ee\u524d\u7f16\u8f91\u4e2d\u7684\u8d44\u6599
count=\u5e8f\u53f7
action=\u5f02\u52a8\u6a21\u5f0f
actoin_001=\u662f\u5426\u6267\u884c\u6b64\u52a8\u4f5c?
action_002=\u8bf7\u5148\u9009\u62e9\u9700\u300c\u4fee\u6539/\u5220\u9664\u300d\u4e4b\u6570\u636e\u5217\u3002
action_003=\u662f\u5426\u786e\u5b9a\u300c\u5220\u9664\u300d\u6b64\u7b14\u8d44\u6599?
action_004=\u8bf7\u5148\u9009\u62e9\u9700\u300c\u8c03\u9605\u300d\u4e4b\u6570\u636e\u5217
action_005=\u8bf7\u5148\u9009\u53d6\u4e00\u7b14\u4ee5\u4e0a\u4e4b\u6570\u636e\u5217
action_006=\u8bf7\u5148\u9009\u62e9\u9700\u300c\u6253\u5370\u300d\u4e4b\u6570\u636e\u5217
confirmApply=\u662f\u5426\u786e\u5b9a\u5448\u4e3b\u7ba1\u590d\u6838?
confirmApply2=\u662f\u5426\u786e\u5b9a\u5448\u4e3b\u7ba1\u590d\u6838?
confirmApprove=\u662f\u5426\u786e\u5b9a\u590d\u6838?
confirmApprove2=\u662f\u5426\u8981\u5448\u9001?
confirmApprove3=\u662f\u5426\u786e\u5b9a\u6838\u51c6?
confirmReturn=\u662f\u5426\u786e\u5b9a\u9000\u56de?
confirmReject=\u662f\u5426\u786e\u5b9a\u62d2\u7edd?
confirmDelete=\u662f\u5426\u786e\u5b9a\u5220\u9664?
confirmCopy=\u662f\u5426\u786e\u5b9a\u590d\u5236?
confirmSend=\u662f\u5426\u786e\u8ba4\u9001\u51fa?
confirmRun=\u662f\u5426\u786e\u5b9a\u6267\u884c\u6b64\u529f\u80fd?
confirmCk=\u5185\u5bb9\u5df2\u5f02\u52a8\uff0c\u662f\u5426\u9700\u8981\u50a8\u5b58?
confirmApplySuccess=\u590d\u6838\u6210\u529f
confirmApplySuccess1=\u6838\u51c6\u6210\u529f
confirmApplySuccess2=\u5448\u9001\u6210\u529f
confirmReturnSuccess=\u9000\u56de\u6210\u529f
confirmDeleteSuccess=\u5220\u9664\u6210\u529f
confirmCopySuccess=\u590d\u5236\u6210\u529f
confirmApplySuccess3=\u5448\u4e3b\u7ba1\u590d\u6838\u6210\u529f
confirmRejectSuccess=\u5df2\u62d2\u7edd
confirmDeliverSuccess=\u4f20\u9001\u6210\u529f
confirmContinueRun=\u6570\u636e\u5df2\u5f02\u52a8\uff0c\u5c1a\u672a\u50a8\u5b58\uff0c\u662f\u5426\u7ee7\u7eed\u6267\u884c?
confirmTitle=\u63d0\u793a
id_reason=\u67e5\u8be2\u7406\u7531
grid_selector=\u8bf7\u9009\u62e9\u6570\u636e
#J-106-0029-002  \u6d17\u9322\u9632\u5236-\u65b0\u589e\u6d17\u9322\u9632\u5236\u9801\u7c64
confirmBeforeDeleteAll=\u6267\u884c\u65f6\u4f1a\u5220\u9664\u5df2\u5b58\u5728\u4e4b\u8d44\u6599\uff0c\u662f\u5426\u786e\u5b9a\u6267\u884c\uff1f
#J-107-0390_05097_B1001 \u5206\u884c\u6b0a\u9650\u4e4b\u6388\u4fe1\u6848\u4ef6\u82e5\u65bc\u8986\u6838\u5f8c\u6b32\u4fee\u6539,\u5f97\u6388\u6b0a\u4e3b\u7ba1\u5f97\u9000\u56de\u81f3\u7de8\u88fd\u4e2d
confirmBackApprove=\u9000\u56de\u540e\u6848\u4ef6\u5fc5\u987b\u91cd\u65b0\u8986\u6838\uff0c\u65e0\u6cd5\u76f4\u63a5\u590d\u539f\u4e3a\u5df2\u6838\u51c6\u72b6\u6001\uff0c\u662f\u5426\u786e\u5b9a\u9000\u56de\u5df2\u6838\u51c6\u6848\u4ef6?


#(\u53cc\u51fb\u9f20\u6807\u5de6\u952e\u5f15\u5165)
TMInsert=\u662f\u5426\u65b0\u589e\u6b64\u7b14\u8d44\u6599?
TMModify=\u662f\u5426\u786e\u5b9a\u4fee\u6539\u6b64\u7b14\u8d44\u6599?
TMDelete=\u662f\u5426\u786e\u5b9a\u5220\u9664\u6b64\u7b14\u8d44\u6599?
TMMDeleteError=\u8bf7\u5148\u9009\u62e9\u9700\u4fee\u6539(\u5220\u9664)\u4e4b\u6570\u636e\u5217
TMNoChangeModify=\u5e76\u672a\u66f4\u52a8\u4efb\u4f55\u6570\u636e\uff0c\u65e0\u9700\u4fee\u6539\u6570\u636e!
fileSelect=\u8bf7\u5148\u9009\u62e9\u6863\u6848
fileSelError=\u8bf7\u4f7f\u7528\u6b63\u786e\u6863\u6848,\u6269\u5c55\u540d\u4e3a
#fileXlsError=\u8bf7\u4f7f\u7528*.xls\u6a94
fileUploadError=\u6863\u6848\u8bfb\u53d6\u5931\u8d25\uff0c\u8bf7\u91cd\u65b0\u4e0a\u4f20
fileUploading=\u6863\u6848\u8bfb\u53d6\u4e2d\u8bf7\u8010\u5fc3\u7b49\u5019\u2026
fileUploadSuccess=\u6863\u6848\u4e0a\u4f20\u5b8c\u6210!
attachfile=\u9644\u52a0\u6863\u6848
insertfile=\u8bf7\u9009\u62e9\u9644\u52a0\u6863\u6848
insertfileSize=\u76ee\u524d\u53ef\u4e0a\u4f20\u6863\u6848\u5927\u5c0f\u4e3a$\{fileSize\}M
#id_dcTitle=(\u8bf7\u53cc\u51fb\u6240\u9700\u4e4b\u9009\u9879)
#id_error=\u7edf\u4e00\u7f16\u53f7\u68c0\u6838\u9519\u8bef
lastModifyBy=\u7f16\u5236\u4eba\u5458
lastModifyRole=\u7f16\u5236\u4eba\u5458\u7fa4\u7ec4
lastModifyName=\u7f16\u5236\u4eba\u5458
lastModifyTime=\u6700\u540e\u66f4\u65b0\u65e5\u671f
lastUpdater=\u6700\u540e\u5f02\u52a8\u4eba\u5458
lastUpdateTime=\u6700\u540e\u5f02\u52a8\u65f6\u95f4
actionType=\u6848\u4ef6\u72b6\u6001
createTime=\u5efa\u7acb\u65e5\u671f
tabchange=\u9875\u9762\u6570\u636e\u5904\u7406\u4e2d\u2026
localtempResolve=\u662f\u5426\u91cd\u65b0\u6267\u884c\u5148\u524d\u672a\u6b63\u5e38\u50a8\u5b58\u52a8\u4f5c?
requireSave=\u70e6\u8bf7\u5148\u50a8\u5b58\u540e\uff0c\u518d\u6267\u884c\u5176\u52a8\u4f5c\uff0c\u8c22\u8c22\u3002
saveBeforePrint=\u6267\u884c\u6253\u5370\u5c06\u81ea\u52a8\u50a8\u5b58\u6570\u636e\uff0c\u662f\u5426\u7ee7\u7eed\u6b64\u52a8\u4f5c? 
saveBeforeSend=\u6267\u884c\u5c06\u81ea\u52a8\u50a8\u5b58\u6570\u636e\uff0c\u662f\u5426\u7ee7\u7eed\u6b64\u52a8\u4f5c? 
saveBeforeAction=\u5c06\u81ea\u52a8\u50a8\u5b58\u6570\u636e\uff0c$\{btnAction\}
lognView=\u767b\u5f55/\u8c03\u9605\u6838\u5907\u6ce8\u8bb0
#---------------------------------------------#
# javascript commom.js use(validation)
#---------------------------------------------#
val.required=\u6b64\u4e3a\u5fc5\u586b\u5b57\u6bb5.
#
val.remote=Please fix this field.
#Please fix this field.
val.email=E-Mail\u683c\u5f0f\u9519\u8bef
#Please enter a valid email address.
val.url=\u7f51\u5740\u683c\u5f0f\u9519\u8bef.
#Please enter a valid URL.
val.date=\u65e5\u671f\u683c\u5f0f\u9519\u8bef.(YYYYMMDD)
val.date2=\u65e5\u671f\u683c\u5f0f\u9519\u8bef.(YYYY-MM)
val.date3=\u65e5\u671f\u683c\u5f0f\u9519\u8bef.(YYYYMM)
val.date4=\u65e5\u671f\u683c\u5f0f\u9519\u8bef.(YYYY/MM)
#Please enter a valid date(YYYYMMDD).
val.dateISO=Please enter a valid date (ISO).
#Please enter a valid date (ISO).
#val.dateD=Bitte geben Sie ein g\u00fcltiges Datum ein.
#Bitte geben Sie ein g\u00fcltiges Datum ein.
val.number=\u8bf7\u8f93\u5165\u6570\u5b57.
#Please enter a valid number.
#val.numberDE=Bitte geben Sie eine Nummer ein.
#Bitte geben Sie eine Nummer ein.
val.digits=\u8bf7\u8f93\u5165\u6570\u5b57(\u65e0\u6b63\u8d1f\u53f7)
#Please enter only digits
val.creditcard=Please enter a valid credit card number.
#Please enter a valid credit card number.
val.equalTo=Please enter the same value again.
#Please enter the same value again.
val.accept=Please enter a value with a valid extension.
#Please enter a value with a valid extension.
val.maxlength=\u6700\u591a\u8f93\u5165{0}\u4e2a\u5b57\u7b26.
#$.validator.format("Please enter no more than {0} characters.
val.minlength=\u6700\u5c11\u8f93\u5165{0}\u4e2a\u5b57\u7b26
#Please enter at least {0} characters.
val.rangelength=\u8bf7\u8f93\u5165 {0} \u5230 {1} \u4f4d\u5b57\u7b26
#Please enter a value between {0} and {1} characters long.
val.range=Please enter a value between {0} and {1}.
#Please enter a value between {0} and {1}.
val.max=\u5fc5\u9700\u5c0f\u4e8e\u6216\u7b49\u4e8e {0} 
#Please enter a value less than or equal to {0}.
val.min=\u5fc5\u9700\u5927\u4e8e\u6216\u7b49\u4e8e {0}
#Please enter a value greater than or equal to {0}.
val.twid=\u8eab\u4efd\u8bc1\u8f93\u5165\u9519\u8bef.
val.compNo=\u7edf\u4e00\u7f16\u53f7\u8f93\u5165\u9519\u8bef.
val.foreign=\u5916\u56fd\u81ea\u7136\u4eba\u7edf\u7f16\u8f93\u5165\u9519\u8bef.
val.requiredLength=\u8bf7\u8f93\u5165 {0} \u4e2a\u5b57\u7b26
val.checkID=\u300c\u8eab\u4efd\u8bc1\u300d\u6216\u300c\u7edf\u4e00\u7f16\u53f7\u300d\u8f93\u5165\u9519\u8bef.
val.tooLong=\u6587\u5b57\u9577\u5ea6\u904e\u9577
#\u65e5\u671f
val.ineldate=\u8bf7\u8f93\u5165\u5e74\u6708
val.inelbranch=\u8bf7\u9009\u5206\u884c
val.ip=\u8bf7\u8f93\u5165\u5408\u6cd5IP
val.time=\u8bf7\u8f93\u5165\u5408\u6cd5\u65f6\u95f4 00:00~ 23:59
val.noSignNumber=\u8bf7\u8f93\u5165\u6570\u5b57(\u65e0\u6b63\u8d1f\u53f7)
val.checkmaxlength=\u4e0d\u53ef\u8d85\u8fc7 {0} \u4e2a\u5b57\u7b26
val.numeric=\u8bf7\u8f93\u5165\u6570\u5b57\uff0c\u6574\u6570 ${0} \u4f4d
val.numericFraction=,\u5c0f\u6570 ${0} \u4f4d
val.phone=\u7535\u8bdd\u683c\u5f0f\u9519\u8bef
val.alphanum=\u53ea\u80fd\u8f93\u5165\u82f1\u6587\u53ca\u6570\u5b57
val.obuText=\u8bf7\u8f93\u5165\u82f1\u6570\u5b57
val.numText=\u8bf7\u8f93\u5165\u6570\u5b57
val.enText=\u8bf7\u8f93\u5165\u82f1\u6587\u5b57
val.halfword=\u53ea\u80fd\u8f93\u5165\u534a\u578b\u5b57
#\u5171\u4eabGrid i18n
grid.pgtext=\u7b2c{0}\u9875,\u5171{1}\u9875
grid.emptyrecords=\u67e5\u65e0\u8d44\u6599
grid.recordtext={0}~{1}/\u5171{2}\u7b14
grid.loadtext=\u67e5\u8be2\u4e2d\uff0c\u8bf7\u7a0d\u540e\uff01
grid.refresh=\u91cd\u65b0\u6574\u7406
grid.up=\u4e0a\u79fb
grid.down=\u4e0b\u79fb
grid.selrow=\u8bf7\u5148\u9009\u62e9\u4e00\u7b14\u6570\u636e\u3002
grid.check=\u786e\u8ba4\u4fee\u6539
grid.showAllBranch=\u5206\u884c\u6e05\u5355\u67e5\u8be2
grid.branchNo=\u5206\u884c\u4ee3\u53f7
grid.branchName=\u5206\u884c\u540d\u79f0
grid.branchGroup=\u6240\u5c5e\u8425\u8fd0\u4e2d\u5fc3\u540d\u79f0
grid.datePeriodCheck=\u8bf7\u8f93\u5165\u65e5\u671f\u533a\u95f4\u4e4b\u8d77\u8bab\u65e5\u671f!
localSave.quotaExceededError=\u8bf7\u5141\u8bb8\u5728\u60a8\u8ba1\u7b97\u673a\u4e0a\u52a0\u5927\u672c\u673a\u50a8\u5b58\u533a\u5bb9\u91cf\u7684\u6743\u9650\u3002
grid.docName=\u6587\u4ef6\u540d
grid.maxSelrow=${0} \u6700\u591a\u53ea\u53ef\u9009\u62e9${1}\u7b14\u6570\u636e\u3002
#\u6587\u4ef6\u5f02\u52a8\u8bb0\u5f55
docLog.logNum=\u8bb0\u5f55\u5e8f\u53f7
docLog.logTime=\u8bb0\u5f55\u65e5\u671f\u65f6\u95f4
docLog.unitId=\u6267\u884c\u4eba\u6240\u5c5e\u5355\u4f4d
docLog.userId=\u6267\u884c\u4eba\u5458\u4ee3\u53f7
docLog.userName=\u6267\u884c\u4eba\u5458\u59d3\u540d
docLog.userPos=\u6267\u884c\u4eba\u5458\u804c\u52a1
docLog.actCode=\u6267\u884c\u9879\u76ee
docLog.title=\u6587\u4ef6\u5f02\u52a8\u8bb0\u5f55
#\u6d41\u7a0b\u7c7b
flow.confirmSend=\u662f\u5426\u786e\u5b9a\u8981\u4f20\u9001?
flow.sent=\u4f20\u9001\u6210\u529f!!
flow.exit=\u662f\u5426\u786e\u5b9a\u79bb\u5f00?
flow.confirmSend2=\u662f\u5426\u786e\u5b9a\u5448\u4e3b\u7ba1\u590d\u6838?
flow.sent2=\u5448\u9001\u6210\u529f!!
flow.confirmReturn=\u662f\u5426\u786e\u5b9a\u9000\u56de?
flow.returned=\u9000\u56de\u6210\u529f!!
flow.needCreateContract=\u662f\u5426\u81ea\u52a8\u4ea7\u751f\u5e76\u8986\u6838\u7ebf\u4e0a\u5bf9\u4fdd\u5951\u7ea6\u4e66\uff1f
#\u5f15\u5165\u5ba2\u6237ID
includeId.title=\u5ba2\u6237\u6570\u636e\u67e5\u8be2
includeId.subTitle=\u7edf\u4e00\u7f16\u53f7
includeId.newCustName=(\u65b0\u5ba2\u6237)
includeId.noData=\u5ba2\u6237\u57fa\u672c\u6570\u636e\u6587\u4ef6\u67e5\u65e0\u8be5\u7edf\u4e00\u7f16\u53f7
includeId.selData=\u8bf7\u9009\u62e9\u4e00\u7b14\u6570\u636e!!
#0024\u5efa\u6a94
creatCust.queryType1=\u4f9d\u7edf\u7f16\u67e5\u8be2
creatCust.queryType2=\u4f9d\u5ba2\u6237\u82f1\u6587\u540d\u67e5\u8be2(\u516c\u53f8\u6237)
creatCust.error=\u8bf7\u8f93\u5165\u67e5\u8be2\u5185\u5bb9
creatCust.custType=\u5ba2\u6237\u7c7b\u522b
creatCust.custType1=\u4e2a\u4eba\u6237
creatCust.custType2=\u516c\u53f8\u6237
creatCust.headNation=\u516c\u53f8\u6ce8\u518c\u5730\u56fd\u522b(\u4e2a\u4eba\u56fd\u7c4d)
creatCust.regNation=\u6240\u5728\u5730\u56fd\u522b(\u4e2a\u4eba\u51fa\u751f\u5730)
creatCust.licenseType=\u8bc1\u4ef6\u7c7b\u578b
creatCust.licenseNO=\u8bc1\u4ef6\u53f7\u7801
creatCust.birthday=\u516c\u53f8\u8bbe\u7acb\u65e5 ( \u4e2a\u4eba\u6237\u51fa\u751f\u5e74\u6708\u65e5 )
creatCust.CNAME=\u7e41\u4f53\u6237\u540d ( \u5168\u89d2 )
creatCust.LNAME=\u5f53\u5730\u6237\u540d ( \u5168\u89d2 )
creatCust.ENAME=\u82f1\u6587\u6237\u540d ( \u534a\u89d2 )
creatCust.custName=\u6237\u540d\u8bf7\u62e9\u4efb\u4e00\u8bed\u7cfb\u8f93\u5165
creatCust.ENAMEerror=\u82f1\u6587\u6237\u540d\u683c\u5f0f\u9519\u8bef
creatCust.bstbl=\u4e3b\u8ba1\u5904\u884c\u4e1a\u5bf9\u8c61\u522b\u5927\u7c7b
creatCust.bstb2=\u4e3b\u8ba1\u5904\u884c\u4e1a\u5bf9\u8c61\u522b\u4e2d\u7c7b
creatCust.bstb3=\u4e3b\u8ba1\u5904\u884c\u4e1a\u5bf9\u8c61\u522b\u7ec6\u7c7b
creatCust.bstb4=\u884c\u4e1a\u5bf9\u8c61\u522b\u7ec6\u5206\u7c7b
creatCust.bstbError=\u8bf7\u9009\u62e9\u4e00\u79cd\u884c\u4e1a\u522b
creatCust.localId=\u6d77\u5916\u5206\u884c\u5f53\u5730 AS400 \u7f16\u7801 ID
creatCust1.060000=\u79c1\u4eba
creatCust1.130300=\u5728\u53f0\u65e0\u4f4f\u6240\u5916\u56fd\u4eba
creatCust.companyDate=\u540c\u5ba2\u6237ID\u516c\u53f8\u6237\u8bbe\u7acb\u65e5
creatCust.reqID=\u8d1f\u8d23\u4ebaID
creatCust.swiftID=\u540c\u4e1a\u4ee3\u7801
creatCust.buscd=\u884c\u4e1a\u522b
creatCust.newUserDisabled=\u65e0\u65b0\u589e\u7528\u6237\u6743\u529b
creatCust.failAndConfirmExit=\u5efa\u7acb\u65b0\u5ba2\u6237\u53d1\u751f\u9519\u8bef\uff0c\u662f\u5426\u7ed3\u675f\u65b0\u5ba2\u6237\u5efa\u6587\u4ef6\u4f5c\u4e1a\uff1f
creatCust.MEMO=\u6d77\u5916\u5206\u884c\u4e2a\u4eba\u6237\u65f6\uff0c\u4e3b\u8ba1\u5904\u884c\u4e1a\u5bf9\u8c61\u522b\u5927\u7c7b\u8b2e\u9009\u62e9[\u79c1\u4eba]
#\u6863\u6848\u4e0a\u4f20
uploadFile.button=\u4e0a\u4f20\u6570\u636e\u81f3\u4e3b\u673a
uploadFile.uploadTime=\u4e0a\u4f20\u65f6\u95f4
uploadFile.srcFileName=\u6587\u4ef6\u540d
uploadFile.srcFileDesc=\u6863\u6848\u8bf4\u660e
#\u62a5\u8868\u7c7b
printError=\u4ea7\u751f\u62a5\u8868\u9519\u8bef!!
printPrcess=\u62a5\u8868\u7ed8\u5236\u4e2d \uff0c\u8bf7\u7a0d\u5019
openTckeditBoxmsg=\u5f00\u542f{0}\u767b\u5f55\u753b\u9762
backdoc.msg1=\u786e\u8ba4\u6267\u884c\u53d6\u6d88\u590d\u6838?
#\u753b\u9762\u8baf\u606f
err.chooseBoss=\u8bf7\u9009\u62e9\u4e3b\u7ba1
common.L140M01M=\u592e\u884c\u8d2d\u4f4f/\u7a7a\u5730/\u5174\u5efa\u623f\u5c4b\u7edf\u8ba1\u62a5\u8868\u7528\u76f8\u5173\u4fe1\u606f
common.L140M01Q=\u5927\u9646\u5730\u533a\u6388\u4fe1\u4e1a\u52a1\u63a7\u7ba1\u6ce8\u8bb0
common.L140S05A=\u53d8\u66f4\u6761\u4ef6\u9879\u76ee
common.001=\u5b57\u6bb5\u68c0\u6838\u672a\u5b8c\u6210\uff0c\u8bf7\u586b\u59a5\u540e\u518d\u9001\u51fa
common.002=\u8bf7\u9009\u62e9\u6bcd\u884c\u5355\u4f4d/\u6388\u6743\u4e3b\u7ba1
common.003=\u7ecf\u529e
common.004=\u5e38\u7528\u4e3b\u7ba1
defaultFontSize=\u5b57\u53f7\u5efa\u8bae\u4f7f\u752816
#\u9650\u5b9aCKEDIT\u4e00\u884c\u5b57\u6570\u6709\u591a\u957f\u7684\u5907\u6ce8
lms.ckeditRemark1=\u6ce81:\u5efa\u8bae\u5b57\u578b16
lms.ckeditRemark2=\u6ce82:|\u2190\u5b57\u578b16\u65f6\u5efa\u8bae\u6362\u884c
lms.ckeditRemark3=\u6ce81:|\u2190\u5efa\u8bae\u6362\u884c
queryByEnglish=\u4f9d\u5ba2\u6237\u82f1\u6587\u540d\u67e5\u8be2
