(function(a){a.jgrid={defaults:{recordtext:"{0} - {1}\u3000共 {2} 条",emptyrecords:"无数据显示",loadtext:"读取中...",pgtext:" {0} 共 {1} 页"},search:{caption:"搜索...",Find:"查找",Reset:"重置",odata:["等于\u3000\u3000","不等\u3000\u3000","小于\u3000\u3000","小于等于","大于\u3000\u3000","大于等于","开始于","不开始于","属于\u3000\u3000","不属于","结束于","不结束于","包含\u3000\u3000","不包含"],groupOps:[{op:"AND",text:"所有"},{op:"OR",text:"任一"}],matchText:" 匹配",rulesText:" 规则"},edit:{addCaption:"添加记录",editCaption:"编辑记录",bSubmit:"提交",bCancel:"取消",bClose:"关闭",saveData:"数据已改变，是否保存？",bYes:"是",bNo:"否",bExit:"取消",msg:{required:"此字段必需",number:"请输入有效数字",minValue:"输值必须大于等于 ",maxValue:"输值必须小于等于 ",email:"这不是有效的e-mail地址",integer:"请输入有效整数",date:"请输入有效时间",url:"无效网址。前缀必须为 ('http://' 或 'https://')",nodefined:" 未定义！",novalue:" 需要返回值！",customarray:"自定义函数需要返回数组！",customfcheck:"Custom function should be present in case of custom checking!"}},view:{caption:"查看记录",bClose:"关闭"},del:{caption:"删除",msg:"删除所选记录？",bSubmit:"删除",bCancel:"取消"},nav:{edittext:"",edittitle:"编辑所选记录",addtext:"",addtitle:"添加新记录",deltext:"",deltitle:"删除所选记录",searchtext:"",searchtitle:"查找",refreshtext:"",refreshtitle:"刷新表格",alertcap:"注意",alerttext:"请选择记录",viewtext:"",viewtitle:"查看所选记录"},col:{caption:"选择列",bSubmit:"确定",bCancel:"取消"},errors:{errcap:"错误",nourl:"没有设置url",norecords:"没有要处理的记录",model:"colNames 和 colModel 长度不等！"},formatter:{integer:{thousandsSeparator:" ",defaultValue:"0"},number:{decimalSeparator:".",thousandsSeparator:" ",decimalPlaces:2,defaultValue:"0.00"},currency:{decimalSeparator:".",thousandsSeparator:" ",decimalPlaces:2,prefix:"",suffix:"",defaultValue:"0.00"},date:{dayNames:["Sun","Mon","Tue","Wed","Thr","Fri","Sat","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],monthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec","January","February","March","April","May","June","July","August","September","October","November","December"],AmPm:["am","pm","AM","PM"],S:function(b){return b<11||b>13?["st","nd","rd","th"][Math.min((b-1)%10,3)]:"th"},srcformat:"Y-m-d",newformat:"m-d-Y",masks:{ISO8601Long:"Y-m-d H:i:s",ISO8601Short:"Y-m-d",ShortDate:"Y/j/n",LongDate:"l, F d, Y",FullDateTime:"l, F d, Y g:i:s A",MonthDay:"F d",ShortTime:"g:i A",LongTime:"g:i:s A",SortableDateTime:"Y-m-d\\TH:i:s",UniversalSortableDateTime:"Y-m-d H:i:sO",YearMonth:"F, Y"},reformatAfterEdit:false},baseLinkUrl:"",showAction:"",target:"",checkbox:{disabled:true},idName:"id"}}})(jQuery);