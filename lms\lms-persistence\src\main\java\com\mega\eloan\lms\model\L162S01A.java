/* 
 * L162S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 主從債務人資料表檔 **/
@NamedEntityGraph(name = "L162S01A-entity-graph", attributeNodes = { @NamedAttributeNode("l160m01a") })
@Entity
@Table(name = "L162S01A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "cntrNo", "rId", "rDupNo", "rType" }))
public class L162S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L160M01A l160m01a;

	public L160M01A getL160m01a() {
		return l160m01a;
	}

	public void setL160m01a(L160M01A l160m01a) {
		this.l160m01a = l160m01a;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 主債務人統編
	 * <p/>
	 * 引進額度明細表之借款人統編
	 */
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 主債務人統編重覆碼 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 額度序號
	 * <p/>
	 * 即額度明細表之額度序號
	 */
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/**
	 * 從債務人統編
	 * <p/>
	 * 引進額度明細表之連保人－連保人統編，引進可修改
	 */
	@Column(name = "RID", length = 10, columnDefinition = "VARCHAR(10)")
	private String rId;

	/** 從債務人統編重覆碼 **/
	@Column(name = "RDUPNO", length = 1, columnDefinition = "VARCHAR(1)")
	private String rDupNo;

	/**
	 * 從債務人名稱
	 * <p/>
	 * 101/07/05調整<br/>
	 * 引進額度明細表之連保人－連保人姓名，引進可修改<br/>
	 * VARCHAR(60)( VARCHAR(120)
	 */
	@Column(name = "RNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String rName;

	/**
	 * 關係類別
	 * <p/>
	 * 引進額度明細表之連保人－與借款人關係，引進可修改<br/>
	 * 關係類別選項：<br/>
	 * 企業關係人(含自然人、法人)：類別細項詳註一<br/>
	 * 親屬關係：類別細項詳註一<br/>
	 * 其他綜合關係：上述兩項之類別細項混合勾選<br/>
	 * 註:<br/>
	 * 只有主債務人統編與從債務人統編相同者,其關係可為空白,其餘欄位皆不可為空白
	 */
	@Column(name = "RKINDM", length = 1, columnDefinition = "VARCHAR(1)")
	private String rKindM;

	/** 關係類別細項 **/
	@Column(name = "RKINDD", length = 2, columnDefinition = "VARCHAR(2)")
	private String rKindD;

	/**
	 * 國別
	 * <p/>
	 * 引進該額度明細表之連保人－連保人國別，引進可修改
	 */
	@Column(name = "RCOUNTRY", length = 2, columnDefinition = "VARCHAR(2)")
	private String rCountry;

	/**
	 * 相關身份
	 * <p/>
	 * 同一筆額度序號下之第一筆主從債務人資料固定為借款人(即主債務人與從債務人統編相同時)，相關身份固定為「C共同借款人」，其餘自行勾選。<br/>
	 * 選項：<br/>
	 * C 共同借款人<br/>
	 * D 共同發票人<br/>
	 * E 票據債務人(指金融交易之擔保背書)<br/>
	 * G 連帶保證人，擔保品提供人兼連帶保證人<br/>
	 * L 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人<br/>
	 * S 擔保品提供人<br/>
	 * N ㄧ般保證人
	 */
	@Column(name = "RTYPE", length = 1, columnDefinition = "VARCHAR(1)")
	private String rType;

	/**
	 * 董監事任期止日 (保證人保證迄日)
	 * <p/>
	 * 註:<br/>
	 * 依民法第753條之1，明定「因擔任法人董事、監察人或其他有代表權之人而為該法人擔任保證人者，僅就任職期間法人所生之債務負保證責任。」，
	 * 故保證人之關係別若為借款人之董事
	 * 、監察人者或其他有代表權之人者，必須於【董監事任期止日(保證人保證迄日)】欄位輸入【董監事任期止日】YYYY-MM-DD。
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "DUEDATE", columnDefinition = "DATE")
	private Date dueDate;

	/**
	 * 資料來源
	 * <p/>
	 * 100/10/19新增<br/>
	 * 1.額度明細表<br/>
	 * 2.自行新增
	 */
	@Column(name = "DATASRC", length = 1, columnDefinition = "CHAR(1)")
	private String dataSrc;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 負担保證責任比率 J-103-0299
	 */
	@Column(name = "GUAPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal guaPercent;

	/** Grid 顯示負担保證責任比率 ###.## **/
	@Transient
	private String guaPercentStr;

	/**
	 * 信用品質順序 J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 */
	@Column(name = "PRIORITY", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal priority;

	/**
	 * 本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)
	 */
	@Column(name = "GUANAEXPOSURE", length = 1, columnDefinition = "CHAR(1)")
	private String guaNaExposure;

	/**
	 * 擔保限額Guarante Amount
	 */
	@Column(name = "GRTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal grtAmt;
	
	/**
	 * 當地客戶識別ID Local Id
	 */
	@Column(name = "LOCALID", columnDefinition = "VARCHAR(30)")
	private String localId;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得主債務人統編
	 * <p/>
	 * 引進額度明細表之借款人統編
	 */
	public String getCustId() {
		return this.custId;
	}

	/**
	 * 設定主債務人統編
	 * <p/>
	 * 引進額度明細表之借款人統編
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得主債務人統編重覆碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定主債務人統編重覆碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得額度序號
	 * <p/>
	 * 即額度明細表之額度序號
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}

	/**
	 * 設定額度序號
	 * <p/>
	 * 即額度明細表之額度序號
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/**
	 * 取得從債務人統編
	 * <p/>
	 * 引進額度明細表之連保人－連保人統編，引進可修改
	 */
	public String getRId() {
		return this.rId;
	}

	/**
	 * 設定從債務人統編
	 * <p/>
	 * 引進額度明細表之連保人－連保人統編，引進可修改
	 **/
	public void setRId(String value) {
		this.rId = value;
	}

	/** 取得從債務人統編重覆碼 **/
	public String getRDupNo() {
		return this.rDupNo;
	}

	/** 設定從債務人統編重覆碼 **/
	public void setRDupNo(String value) {
		this.rDupNo = value;
	}

	/**
	 * 取得從債務人名稱
	 * <p/>
	 * 101/07/05調整<br/>
	 * 引進額度明細表之連保人－連保人姓名，引進可修改<br/>
	 * VARCHAR(60)( VARCHAR(120)
	 */
	public String getRName() {
		return this.rName;
	}

	/**
	 * 設定從債務人名稱
	 * <p/>
	 * 101/07/05調整<br/>
	 * 引進額度明細表之連保人－連保人姓名，引進可修改<br/>
	 * VARCHAR(60)( VARCHAR(120)
	 **/
	public void setRName(String value) {
		this.rName = value;
	}

	/**
	 * 取得關係類別
	 * <p/>
	 * 引進額度明細表之連保人－與借款人關係，引進可修改<br/>
	 * 關係類別選項：<br/>
	 * 企業關係人(含自然人、法人)：類別細項詳註一<br/>
	 * 親屬關係：類別細項詳註一<br/>
	 * 其他綜合關係：上述兩項之類別細項混合勾選<br/>
	 * 註:<br/>
	 * 只有主債務人統編與從債務人統編相同者,其關係可為空白,其餘欄位皆不可為空白
	 */
	public String getRKindM() {
		return this.rKindM;
	}

	/**
	 * 設定關係類別
	 * <p/>
	 * 引進額度明細表之連保人－與借款人關係，引進可修改<br/>
	 * 關係類別選項：<br/>
	 * 企業關係人(含自然人、法人)：類別細項詳註一<br/>
	 * 親屬關係：類別細項詳註一<br/>
	 * 其他綜合關係：上述兩項之類別細項混合勾選<br/>
	 * 註:<br/>
	 * 只有主債務人統編與從債務人統編相同者,其關係可為空白,其餘欄位皆不可為空白
	 **/
	public void setRKindM(String value) {
		this.rKindM = value;
	}

	/** 取得關係類別細項 **/
	public String getRKindD() {
		return this.rKindD;
	}

	/** 設定關係類別細項 **/
	public void setRKindD(String value) {
		this.rKindD = value;
	}

	/**
	 * 取得國別
	 * <p/>
	 * 引進該額度明細表之連保人－連保人國別，引進可修改
	 */
	public String getRCountry() {
		return this.rCountry;
	}

	/**
	 * 設定國別
	 * <p/>
	 * 引進該額度明細表之連保人－連保人國別，引進可修改
	 **/
	public void setRCountry(String value) {
		this.rCountry = value;
	}

	/**
	 * 取得相關身份
	 * <p/>
	 * 同一筆額度序號下之第一筆主從債務人資料固定為借款人(即主債務人與從債務人統編相同時)，相關身份固定為「C共同借款人」，其餘自行勾選。<br/>
	 * 選項：<br/>
	 * C 共同借款人<br/>
	 * D 共同發票人<br/>
	 * E 票據債務人(指金融交易之擔保背書)<br/>
	 * G 連帶保證人，擔保品提供人兼連帶保證人<br/>
	 * L 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人<br/>
	 * S 擔保品提供人<br/>
	 * N ㄧ般保證人
	 */
	public String getRType() {
		return this.rType;
	}

	/**
	 * 設定相關身份
	 * <p/>
	 * 同一筆額度序號下之第一筆主從債務人資料固定為借款人(即主債務人與從債務人統編相同時)，相關身份固定為「C共同借款人」，其餘自行勾選。<br/>
	 * 選項：<br/>
	 * C 共同借款人<br/>
	 * D 共同發票人<br/>
	 * E 票據債務人(指金融交易之擔保背書)<br/>
	 * G 連帶保證人，擔保品提供人兼連帶保證人<br/>
	 * L 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人<br/>
	 * S 擔保品提供人<br/>
	 * N ㄧ般保證人
	 **/
	public void setRType(String value) {
		this.rType = value;
	}

	/**
	 * 取得董監事任期止日 (保證人保證迄日)
	 * <p/>
	 * 註:<br/>
	 * 依民法第753條之1，明定「因擔任法人董事、監察人或其他有代表權之人而為該法人擔任保證人者，僅就任職期間法人所生之債務負保證責任。」，
	 * 故保證人之關係別若為借款人之董事
	 * 、監察人者或其他有代表權之人者，必須於【董監事任期止日(保證人保證迄日)】欄位輸入【董監事任期止日】YYYY-MM-DD。
	 */
	public Date getDueDate() {
		return this.dueDate;
	}

	/**
	 * 設定董監事任期止日 (保證人保證迄日)
	 * <p/>
	 * 註:<br/>
	 * 依民法第753條之1，明定「因擔任法人董事、監察人或其他有代表權之人而為該法人擔任保證人者，僅就任職期間法人所生之債務負保證責任。」，
	 * 故保證人之關係別若為借款人之董事
	 * 、監察人者或其他有代表權之人者，必須於【董監事任期止日(保證人保證迄日)】欄位輸入【董監事任期止日】YYYY-MM-DD。
	 **/
	public void setDueDate(Date value) {
		this.dueDate = value;
	}

	/**
	 * 取得資料來源
	 * <p/>
	 * 100/10/19新增<br/>
	 * 1.額度明細表<br/>
	 * 2.自行新增
	 */
	public String getDataSrc() {
		return this.dataSrc;
	}

	/**
	 * 設定資料來源
	 * <p/>
	 * 100/10/19新增<br/>
	 * 1.額度明細表<br/>
	 * 2.自行新增
	 **/
	public void setDataSrc(String value) {
		this.dataSrc = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/** 設定負担保證責任比率 **/
	public void setGuaPercent(BigDecimal guaPercent) {
		this.guaPercent = guaPercent;
	}

	/** 取得負担保證責任比率 **/
	public BigDecimal getGuaPercent() {
		return guaPercent;
	}

	public void setGuaPercentStr(String guaPercentStr) {
		this.guaPercentStr = guaPercentStr;
	}

	public String getGuaPercentStr() {
		return guaPercentStr;
	}

	/** 設定信用品質順序 **/
	public void setPriority(BigDecimal priority) {
		this.priority = priority;
	}

	/** 取得信用品質順序 **/
	public BigDecimal getPriority() {
		return priority;
	}

	/**
	 * 設定本行國家暴險是否以保證人國別為計算基準
	 * 
	 * @param guaNaExposure
	 */
	public void setGuaNaExposure(String guaNaExposure) {
		this.guaNaExposure = guaNaExposure;
	}

	/**
	 * 取得本行國家暴險是否以保證人國別為計算基準
	 * 
	 * @return
	 */
	public String getGuaNaExposure() {
		return guaNaExposure;
	}

	/** 設定擔保限額Guarantee Amount **/
	public void setGrtAmt(BigDecimal grtamt) {
		this.grtAmt = grtamt;
	}

	/** 取得擔保限額Guarantee Amount **/
	public BigDecimal getGrtAmt() {
		return grtAmt;
	}
	
	/**
	 * 取得當地客戶識別ID Local Id
	 * 
	 * VARCHAR(30)
	 */
	public String getLocalId() {
		return this.localId;
	}

	/**
	 * 取得當地客戶識別ID Local Id
	 * 
	 * VARCHAR(30)
	 **/
	public void setLocalId(String localid) {
		this.localId = localid;
	}
}
