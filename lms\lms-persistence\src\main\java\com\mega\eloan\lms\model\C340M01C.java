package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 消金契約書內容檔 **/
@NamedEntityGraph(name = "C340M01C-entity-graph", attributeNodes = { @NamedAttributeNode("c340m01a") })
@Entity
@Table(name = "C340M01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "itemType" }))
public class C340M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false) })
	private C340M01A c340m01a;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 項目類別 **/
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String itemType;

	/** 項目說明 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "JSONDATA", columnDefinition = "CLOB")
	private String jsonData;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/** 項目說明 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "INITIALJSONDATA", columnDefinition = "CLOB")
	private String initialJsonData;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getItemType() {
		return itemType;
	}

	public void setItemType(String itemType) {
		this.itemType = itemType;
	}

	public String getJsonData() {
		return jsonData;
	}

	public void setJsonData(String jsonData) {
		this.jsonData = jsonData;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public C340M01A getC340m01a() {
		return c340m01a;
	}

	public void setC340m01a(C340M01A c340m01a) {
		this.c340m01a = c340m01a;
	}

	public String getInitialJsonData() {
		return initialJsonData;
	}

	public void setInitialJsonData(String initialJsonData) {
		this.initialJsonData = initialJsonData;
	}
}
