package com.mega.eloan.lms.mfaloan.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF498;

/**
 * <pre>
 * 消金新案紀錄檔
 * </pre>
 * 
 * @since 2013/3/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/7,EL08034,new
 *          </ul>
 */
public interface MisELF498Service {
	public ELF498 findByPk(String elf498_branch, String elf498_custid,String elf498_dupno, Date elf498_newdate);
	public List<ELF498> findNotRetrial(String elf498_branch, String elf498_custid,String elf498_dupno);
	public List<Map<String,Object>> sel_gfnGetActualReviewData(String elf498_branch, String elf498_latestdate);	
}
