/* 
 * MisELF509ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF509;
import com.mega.eloan.lms.mfaloan.service.MisELF509Service;

@Service
public class MisELF509ServiceImpl extends AbstractMFAloanJdbc implements MisELF509Service {

	@Override
	public ELF509 findByUniqueKey1(String ELF509_CASE_NO, String ELF509_FEE_ITEM, Integer ELF509_SEQ_NO) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF509_findByUniqueKey1",
				new Object[] { ELF509_CASE_NO, ELF509_FEE_ITEM, ELF509_SEQ_NO });
		if (rowData.size() > 0) {
			Map<String, Object> row = rowData.get(0);
			ELF509 model = new ELF509();
			DataParse.map2Bean(row, model);
			return model;
		} else {
			return null;
		}
	}

}
