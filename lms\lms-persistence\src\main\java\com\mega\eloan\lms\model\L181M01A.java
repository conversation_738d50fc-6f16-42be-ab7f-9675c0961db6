/* 
 * L180M02A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 覆審控制維護主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L181M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L181M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l181m01a", fetch = FetchType.LAZY)
	private Set<L181A01A> l181a01a;

	public Set<L181A01A> getL181a01a() {
		return l181a01a;
	}

	public void setL181a01a(Set<L181A01A> l181m01a) {
		this.l181a01a = l181m01a;
	}

	/** 分行代碼 **/
	@Column(name = "ELFBRANCH", length = 3, columnDefinition = "CHAR(3)")
	private String elfBranch;

	/**
	 * 資料日期
	 * <p/>
	 * 未使用
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFDATADT", columnDefinition = "DATE")
	private Date elfDataDt;

	/**
	 * 上上次覆審日
	 * <p/>
	 * 未使用
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFLLRDATE", columnDefinition = "DATE")
	private Date elfLLRDate;

	/**
	 * 上次電腦計算週期 (原始週期)
	 * <p/>
	 * 未使用
	 */
	@Column(name = "ELFOCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elfOCkdLine;

	/**
	 * 戶況
	 * <p/>
	 * 不提供修改
	 */
	@Column(name = "ELFCSTATE", length = 1, columnDefinition = "CHAR(1)")
	private String elfCState;

	/**
	 * 銷戶日
	 * <p/>
	 * 不提供修改
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFCANCELDT", columnDefinition = "DATE")
	private Date elfCancelDt;

	/**
	 * 人工維護日
	 * <p/>
	 * 不提供修改
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFUPDDATE", columnDefinition = "DATE")
	private Date elfUpdDate;

	/**
	 * 人工調整ID
	 * <p/>
	 * 不提供修改
	 */
	@Column(name = "ELFUPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String elfUpdater;

	/**
	 * 上次資料更新日
	 * <p/>
	 * 不提供修改
	 */
	@Column(name = "ELFTMESTAMP", columnDefinition = "TIMESTAMP")
	private Date elfTmeStamp;

	/**
	 * 是否為聯貸案
	 * <p/>
	 * Y/N
	 */
	@Column(name = "UCASE", length = 1, columnDefinition = "CHAR(1)")
	private String uCase;

	/**
	 * 聯貸案身份
	 * <p/>
	 * 1.主辦行, 2.參貸行, 3.參貸同業
	 */
	@Column(name = "UCASEROLE", length = 1, columnDefinition = "CHAR(1)")
	private String uCaseRole;

	/**
	 * 覆審名單類別 J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	@Column(name = "CTLTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String ctlType;

	/** 取得分行代碼 **/
	public String getElfBranch() {
		return this.elfBranch;
	}

	/** 設定分行代碼 **/
	public void setElfBranch(String value) {
		this.elfBranch = value;
	}

	/**
	 * 取得資料日期
	 * <p/>
	 * 未使用
	 */
	public Date getElfDataDt() {
		return this.elfDataDt;
	}

	/**
	 * 設定資料日期
	 * <p/>
	 * 未使用
	 **/
	public void setElfDataDt(Date value) {
		this.elfDataDt = value;
	}

	/**
	 * 取得上上次覆審日
	 * <p/>
	 * 未使用
	 */
	public Date getElfLLRDate() {
		return this.elfLLRDate;
	}

	/**
	 * 設定上上次覆審日
	 * <p/>
	 * 未使用
	 **/
	public void setElfLLRDate(Date value) {
		this.elfLLRDate = value;
	}

	/**
	 * 取得上次電腦計算週期 (原始週期)
	 * <p/>
	 * 未使用
	 */
	public String getElfOCkdLine() {
		return this.elfOCkdLine;
	}

	/**
	 * 設定上次電腦計算週期 (原始週期)
	 * <p/>
	 * 未使用
	 **/
	public void setElfOCkdLine(String value) {
		this.elfOCkdLine = value;
	}

	/**
	 * 取得戶況
	 * <p/>
	 * 不提供修改
	 */
	public String getElfCState() {
		return this.elfCState;
	}

	/**
	 * 設定戶況
	 * <p/>
	 * 不提供修改
	 **/
	public void setElfCState(String value) {
		this.elfCState = value;
	}

	/**
	 * 取得銷戶日
	 * <p/>
	 * 不提供修改
	 */
	public Date getElfCancelDt() {
		return this.elfCancelDt;
	}

	/**
	 * 設定銷戶日
	 * <p/>
	 * 不提供修改
	 **/
	public void setElfCancelDt(Date value) {
		this.elfCancelDt = value;
	}

	/**
	 * 取得人工維護日
	 * <p/>
	 * 不提供修改
	 */
	public Date getElfUpdDate() {
		return this.elfUpdDate;
	}

	/**
	 * 設定人工維護日
	 * <p/>
	 * 不提供修改
	 **/
	public void setElfUpdDate(Date value) {
		this.elfUpdDate = value;
	}

	/**
	 * 取得人工調整ID
	 * <p/>
	 * 不提供修改
	 */
	public String getElfUpdater() {
		return this.elfUpdater;
	}

	/**
	 * 設定人工調整ID
	 * <p/>
	 * 不提供修改
	 **/
	public void setElfUpdater(String value) {
		this.elfUpdater = value;
	}

	/**
	 * 取得上次資料更新日
	 * <p/>
	 * 不提供修改
	 */
	public Date getElfTmeStamp() {
		return this.elfTmeStamp;
	}

	/**
	 * 設定上次資料更新日
	 * <p/>
	 * 不提供修改
	 **/
	public void setElfTmeStamp(Date value) {
		this.elfTmeStamp = value;
	}

	/**
	 * 取得是否為聯貸案
	 * <p/>
	 * Y/N
	 */
	public String getUCase() {
		return this.uCase;
	}

	/**
	 * 設定是否為聯貸案
	 * <p/>
	 * Y/N
	 **/
	public void setUCase(String value) {
		this.uCase = value;
	}

	/**
	 * 取得聯貸案身份
	 * <p/>
	 * 1.主辦行, 2.參貸行, 3.參貸同業
	 */
	public String getUCaseRole() {
		return this.uCaseRole;
	}

	/**
	 * 設定聯貸案身份
	 * <p/>
	 * 1.主辦行, 2.參貸行, 3.參貸同業
	 **/
	public void setUCaseRole(String value) {
		this.uCaseRole = value;
	}

	/** 設定覆審名單類別 **/
	public void setCtlType(String ctlType) {
		this.ctlType = ctlType;
	}

	/** 取得覆審名單類別 **/
	public String getCtlType() {
		return ctlType;
	}
}
