/* 
 * C242M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.IDataObject;

/** 覆審預約單檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C242M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C242M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 排程時間 **/
	@Column(name="SCHTIME", columnDefinition="TIMESTAMP")
	private Timestamp schTime;

	/** 實際執行時間 **/
	@Column(name="EXETIME", columnDefinition="TIMESTAMP")
	private Timestamp exeTime;

	/** 
	 * 執行參數<p/>
	 * 002^2014-07|003^2014-07
	 */
	@Size(max=768)
	@Column(name="EXEPARAM", length=768, columnDefinition="VARCHAR(768)")
	private String exeParam;

	/** 取得排程時間 **/
	public Timestamp getSchTime() {
		return this.schTime;
	}
	/** 設定排程時間 **/
	public void setSchTime(Timestamp value) {
		this.schTime = value;
	}

	/** 取得實際執行時間 **/
	public Timestamp getExeTime() {
		return this.exeTime;
	}
	/** 設定實際執行時間 **/
	public void setExeTime(Timestamp value) {
		this.exeTime = value;
	}

	/** 
	 * 取得執行參數<p/>
	 * 002^2014-07|003^2014-07
	 */
	public String getExeParam() {
		return this.exeParam;
	}
	/**
	 *  設定執行參數<p/>
	 *  002^2014-07|003^2014-07
	 **/
	public void setExeParam(String value) {
		this.exeParam = value;
	}
}
