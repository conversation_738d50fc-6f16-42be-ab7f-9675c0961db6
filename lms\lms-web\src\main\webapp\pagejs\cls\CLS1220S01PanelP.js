var initDfd = window.initDfd || $.Deferred();
var pageAction = {
		grid: null,
		custGrid: null,
		build: function(obj){
			
		var gridview_colModel = [
		   {
			   name : 'oid',
			   hidden : true //是否隱藏
		   },{
			   name : 'mainId',
			   hidden : true //是否隱藏
		   },{
	            name: 'custId', //身分證統編
	            hidden: true //是否隱藏
		   }, {
	            name: 'dupNo', //身分證統編重複碼
	            hidden: true //是否隱藏
	       }, {
	            name: 'custPos', //相關身份
	            hidden: true //是否隱藏
	       }, {
	            name: 'keyMan',
	            hidden: true //是否隱藏
	        }, {
			   colHeader: i18n.cls1220m04["C122M01A.custId"],//客戶ID
			   width: 50,
			   name: 'custNumber',
			   sortable: true,
			   formatter: 'click',
			   onclick : pageAction.openDocByGrid
//			   onclick : pageAction.saveDocInfo
		   }, {
			   colHeader: i18n.cls1220m04["C122M01A.dupNo"],//重複序號
			   width: 35,
			   name: 'dupNo',
			   sortable: true
		   }, {
	            colHeader: i18n.cls1220m04["C122M01A.custName"], //借款人姓名
	            align: "left",
	            width: 80, //設定寬度
	            sortable: true, //是否允許排序
	            name: 'custName' //col.id
	        }, {
	            colHeader: i18n.cls1220m04["C122M01A.markModel"], //評等類型
	            hidden: true,
	            name: 'markModel' //col.id
	        }, {
	            colHeader: i18n.cls1220m04["markModel.C101S01G.grade3"], //房貸最終評等
	            align: "center",
	            width: 50, //設定寬度
	            sortable: false, //是否允許排序
	            name: 'c120s01g.grade3' //col.id
	        }, {
	            colHeader: i18n.cls1220m04["markModel.C101S01Q.grade3"], //非房貸最終評等
	            align: "center",
	            width: 50, //設定寬度
	            sortable: false, //是否允許排序
	            name: 'c120s01q.grade3' //col.id
	        }, {
			   colHeader: i18n.cls1220m04["C122M01A.updateTime"],//異動日期
			   name: 'updateTime',
			   align: 'center',
			   width: 60
		   }];
		
		
		var loadGridInfo = $("#OpenCustInfoDiv").iGrid({
			//localFirst: true,
			handler: 'cls1222gridhandler',
			height: 100,
			action: "importCustInfo", //
			rowNum: 15,
			sortname: "custId|dupNo",
			sortorder: 'asc|asc',
			rownumbers: true,
			colModel: gridview_colModel,
			ondblClickRow: function(rowid){
				var data = loadGridInfo.getRowData(rowid);
				pageAction.openDoc(data);
				}
		});
		},
		openDocByGrid: function(col, set, data){
	        pageAction.openDoc(data);
	    },
	    /**
	     * 開啟文件
	     */
	    openDoc: function(data){
	    	if(data.flowId == "1"){
	    		data.mainId = data.remainId;
	    		//當為擔保品提供人開起的視窗不一樣
                if (data.custPos == 'S') {
                    pageAction.openCustPosnS(data);
                } else {
                    if ($('#CLS1131S01ThickBox').length == 0) {
                        $('#ClsCustInfo').load(webroot + '/app/cls/cls1131s01', function(){
							setTimeout(function() {
	                            CLS1131S01.handler = "cls1131formhandler"; //set handler
	                            CLS1131S01.readOnly = false;
	                            data.isC120M01A = false;
	                            data.isC122M01A = true;
	                            data.c122m01a_mainId = responseJSON.mainId;
	                            data.c122m01a_mainOid = responseJSON.mainOid;
	                            data.c122m01a_oid = responseJSON.oid;
	                            data.applyKind='P';
	                            CLS1131S01.open(data);
							}, 500);
                        });
                    } else {
                        data.isC120M01A = false;
                        data.isC122M01A = true;
                        data.applyKind='P';
                        CLS1131S01.open(data);
                    }
                }
	    	}
	    	if(data.flowId == "2"){
	    	    var lockOption = {};
	    	    var url = '../..' + '/cls/cls1141m01'+ '/02';
                $.form.submit({
                    url: url,
                    data: $.extend({
                        mainDocStatus: data.reMainDocStatus,
                        mainId: data.remainId,
                        mainOid: data.reOid,
//                        docType: rowObject.docType,
//                        docCode: rowObject.docCode,
//                        docKind: rowObject.docRslt,
                        docURL: '/cls/cls1141m01',
                        ownBrId: userInfo.unitNo,
//                        caseBrId: rowObject.caseBrId,
//                        authLvl: rowObject.authLvl,
//                        areaDocstatus: rowObject.areaDocstatus,
//                        areaBrId: rowObject.areaBrId,
//                        areaChk: rowObject.areaChk,
//                        docStatus: rowObject.uid,
//                        hqMeetFlag: rowObject.hqMeetFlag,
                        isC122M01A:true,
                        oid: data.reOid
                    }, lockOption),
                    target: data.reOid
                });
            }
	    },
	    /**
	     * 取得資料表之選擇列
	     */
	    getRowData: function(){
	        var row = pageAction.grid.getGridParam('selrow');
	        var data;
	        if (row) {
	            data = pageAction.grid.getRowData(row);
	        } else {
	            MegaApi.showErrorMessage(i18n.def["confirmTitle"], i18n.def["grid.selrow"]);
	        }
	        return data;
	    }
}


initDfd.done(function(json){
	pageAction.build(json);
	FastCreditLoans.json=json;
	//初始化處理
	//取得帳號權限組
    $.ajax({
        handler: 'cls1220m10formhandler',
        action: 'check_only_expermission'
    }).done(function(responseData){
		if(responseData.only_ex_permission){//僅有電銷權限, 無其他EL相關權限 true=是, false=否
			$("#gbox_docStatusGrid").hide();
		}else{
			var docStstus = json.mainDocStatus;
			//J-112-0006 開放待派案也可以看到[徵信]、[補件通知]的button
			if(docStstus == "A01" || docStstus == "A02" || responseData.isItemB || docStstus == "A00"){ //待派案
				$("#buttonB00").show();
				$("#buttonA02").show();
			}else{
				$("#buttonB00").hide();
				$("#buttonA02").hide();
			}
			//J-113-0199 開放建議開放、人工審核可以產生簽報書
			if(docStstus == "B03" || docStstus == "B04" || docStstus == "C00" ){
				$("#buttonB0304").show();
			}else{
				$("#buttonB0304").hide();
			}
			$("#deleteDocStatusGrid").hide();
			if(responseData.isItemB || responseData.isItemA || docStstus == "C00" ){ //目前只有徵信功能，先綁定此案扭僅出現於徵信照會時
				$("#deleteDocStatusGrid").show();
			}
			if(responseData.is943master){
				$("#changeOrgBrId").show();
			}
			if(json.incomType == "1"){
				$("#sameIpImportData").hide();
				// 青創目前無從債務人
				if (json.applyKind == "I" || json.applyKind == "J") {
					$("#relManageButtons").hide();
				} else {
					$("#relManageButtons").show();
				}
			}
		}
	});
	
	
	var tabForm = $("#tabForm");
	var attchGrid = $("#attchGrid").iGrid({
        handler: 'cls1221gridhandler',
        height: 100,
        width: 400,
        autowidth: false,
        action: "queryAttch",
        sortname: "flag|uploadTime",
        sortorder: "asc|asc",
        postData: {
            mainId: responseJSON.mainId
        },
        needPager:false,
        colModel : [ {
			colHeader : i18n.cls1220m04['label.docFileFlag'],
			name : 'flag',
			width : 35,
			sortable : false
        }, {
			colHeader : i18n.cls1220m04['label.srcFileName'],
			name : 'srcFileName',
			width : 120,
			align: "left",
			sortable : false,
			formatter : 'click',
			onclick : openFile
        }, {
			colHeader : i18n.cls1220m04['label.uploadTime'],
			name : 'uploadTime',
			width : 100,
			sortable : false
		}, {
			colHeader : i18n.cls1220m04['label.fileSrc'],
			name : 'fileSrc',
			width : 35,
			sortable : false
		}, {
			name : 'fieldId',
			hidden : true
		}, {
			name : 'oid',
			hidden : true
		}]				
	});
	
	var mEGAImageGrid = $("#mEGAImageGrid").iGrid({
        handler: 'cls1221gridhandler',
        height: 100,
        width: 400,
        autowidth: false,
        action: "queryMegaImageList",
        sortname: "flag|uploadTime",
        sortorder: "asc|asc",
        postData: {
            mainId: responseJSON.mainId
        },
        needPager:false,
        colModel : [ {
			colHeader : i18n.cls1220m04['label.docFileFlag'],
			name : 'flag',
			width : 35,
			sortable : false
        }, {
			colHeader : i18n.cls1220m04['label.srcFileName'],
			name : 'srcFileName',
			width : 120,
			align: "left",
			sortable : false
        }, {
			colHeader : i18n.cls1220m04['label.uploadTime'],
			name : 'uploadTime',
			width : 100,
			sortable : false
		}, {
			colHeader : i18n.cls1220m04['label.fileSrc'],
			name : 'fileSrc',
			width : 35,
			sortable : false
		}, {
			name : 'fieldId',
			hidden : true
		}, {
			name : 'oid',
			hidden : true
		}, {
			name : 'docFileOid',
			hidden : true
		}]				
	});
	
	var iXMLStatusGrid = $("#iXMLStatusGrid").iGrid({
		handler: 'cls1222gridhandler',
        height: 100,
        width: 750,
        autowidth: false,
        action: "getC122M01GGrid",
        sortname: "sendFTPTime|queryJCICTime",
        sortorder: "asc|asc",
        postData: {
            mainId: responseJSON.mainId
        },
        needPager:false,
        colModel : [ {
        	colHeader : i18n.cls1220m04['label.iXMLStatusGrid.01'],//序號
			align : "left",
			width : 15, 
			name : 'seq'
		},{
        	colHeader : i18n.cls1220m04['label.iXMLStatusGrid.02'],//JCIC 發送驗章時間
			align : "left",
			width : 60, 
			name : 'sendFTPTime'
		}, {
			colHeader : i18n.cls1220m04['label.iXMLStatusGrid.03'],//JCIC 發送驗章狀態
			align : "left",
			width : 60, 
			name : 'sendFTPStatus'
		}, {
			colHeader : i18n.cls1220m04['label.iXMLStatusGrid.04'],//JCIC 驗章完成時間
			align : "left",
			width : 60, 
			name : 'receivedFTPTime'
		}, {
			colHeader : i18n.cls1220m04['label.iXMLStatusGrid.05'],// JCIC 發查時間
			align : "left",
			width : 60, 
			name : 'queryJCICTime'
		}, {
			colHeader : i18n.cls1220m04['label.iXMLStatusGrid.06'],//JCIC 資料回傳時間
			align : "left",
			width : 60, 
			name : 'receivedJCICTime'
        }]
        
	});
	
	var docStatusGrid = $("#docStatusGrid").iGrid({
        handler: 'cls1222gridhandler',
        height: 100,
        width: 400,
        autowidth: false,
        action: "getDataSustus",
        sortname: 'flowId|remainId' ,
        sortorder: "asc|remainId",
        postData: {
            mainId: responseJSON.mainId
        },
        needPager:false,
        colModel : [ {
			colHeader : i18n.cls1220m04['label.docStatusGrid.01'],
			align : "left",
			width : 60, 
			name : 'flowIdDesc'
        }, {
			colHeader: i18n.cls1220m04['label.docStatusGrid.02'],
			width: 50,
			name: 'flowdocStatus',
			sortable: true,
			formatter: 'click',
			onclick : pageAction.openDocByGrid
		}, {
			name : 'flowId',
			hidden : true
        }, {
			name : 'custId',
			hidden : true
        }, {
			name : 'dupNo',
			hidden : true
        }, {
            name: 'custName', 
            hidden : true
        }, {
			name : 'remainId',
			hidden : true
        }, {
            name : 'reOid',
            hidden : true
        }, {
             name : 'reMainDocStatus',
             hidden : true
        }, {
			name : 'mainId',
			hidden : true
        }, {
			name : 'oid',
			hidden : true
        }]				
	});
	var SameIpInfoIn30Day = $("#SameIpInfoIn30Day").iGrid({
		handler : 'cls1222gridhandler',
		height : 200,
		width : 600,
		autowidth : false,
		action : "getSameIpInfoIn30Day",
		sortname : "applyTS",
		sortorder : "desc",
		postData : {
			mainId : responseJSON.mainId
		},
		needPager : false,
		colModel : [ {
			colHeader : i18n.cls1220m04['grid.ownBrId'],
			name : 'ownBrId',
			width : 50,
			sortable : false
		}, {
			colHeader : i18n.cls1220m04['C122M01A.custId'],
			name : 'custId',
			width : 50,
			sortable : false
		}, {
			colHeader : i18n.cls1220m04['C122M01A.custName'],
			name : 'custName',
			width : 50,
			sortable : false
		}, {
			colHeader : i18n.cls1220m04['C122M01A.applyDateTime'],
			name : 'applyTS',
			width : 60,
			sortable : false
		}, {
			colHeader : i18n.cls1220m04['C122M01A.applyAmt.2'],
			name : 'applyAmt',
			align : "right",
			width : 50,
			sortable : false
		}, {
			name : 'oid',
			hidden : true
		} ]
	});
	
	 var setBridData = function(){
	    	//設定經辦下拉選單(有無擔保品-指定收件行員)
		 	var chgOrgBrId = $("#chgOrgBrId");
	    	return $.ajax({
	    		type : "POST",
	    		handler: "cls1220m10formhandler",
	    	    action: "getAllOrgBrId"
	    	}).done(function(responseData){
				if(responseData.Success){ //成功
					chgOrgBrId.setItems({
						item: responseData.childMap,
						space: true,
						format: "{value} {key}"
					});
				}
			});
	    }
	
	$("span.ploan_attch_downloadZIP").click(function(){
		recordQueryReason(function(){
			$.form.submit({
	        	url: __ajaxHandler,
	     		target : "_blank",
	     		data : { 
	     			'_pa' : 'lmsdownloadformhandler' ,
	     			'serviceName': "cls1220r04rptservice" ,
	     			'oid' : responseJSON.mainOid ,
	     			'mainId' : responseJSON.mainId ,     			
	     			'fileDownloadName': 'attach_'+(json.ploanCaseNo||'')+'.zip'
	     		}
	     	});
		});
	});
	
	$("span.changeOrgBrId").click(function(){
		setBridData().done(function(){
			$("#changeOrgBrIdDiv").thickbox({
	  	       	 title: i18n.cls1220m04['spanbutton.changeOrgBrId'], width: 350, height: 200, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
	  	            buttons: {
	  	                "sure": function(){
	  	                	$.ajax({
	  		    				type : "POST",
	  		    				handler : "cls1220m10formhandler",
	  		    				data : {
	  		    					formAction : "chgOrgBrId",
	  		    					mainOid : responseJSON.mainOid,
	  		    					mainId: responseJSON.mainId,
	  		    					chgOrgBrId: $("select#chgOrgBrId").val()
	  		    				}
	  		    			}).done(function(responseData){
								if(responseData.Success){
									$.thickbox.close();
									reLoad(responseData ,json);
								}else{
									CommonAPI.showErrorMessage("變更原始申貸分行失敗");
								}
							}); 
	  	                },
	  	                "cancel": function(){
	  	               	 $.thickbox.close();
	  	                }
	  	            }
	  	        });
		});
		
	});

	var ploanGridRelationData = $("#ploanGridRelationData").iGrid({
        handler: 'cls1221gridhandler',
        height: 100,
        width: 400,
        autowidth: false,
        action: "queryApplyKindPE_relationData",
        postData: {
            mainId: responseJSON.mainId
        },
        colModel : [ {colHeader : i18n.cls1220m04['ploanObj.relationData.relationTypeName'], name : 'relationTypeName', width : 120,  align: "left", sortable : false}
                    ,{colHeader : i18n.cls1220m04['ploanObj.relationData.relationName'], name : 'relationName', width : 120,  align: "left", sortable : false}
                    ,{colHeader : i18n.cls1220m04['ploanObj.relationData.relationIdNo'], name : 'relationIdNo', width : 120,  align: "left", sortable : false}
                    , {name : 'oid', hidden : true}
                   ]				
	});
	
	var ploanGridServedData = $("#ploanGridServedData").iGrid({
        handler: 'cls1221gridhandler',
        height: 100,
        width: 780,
        autowidth: false,
        action: "queryApplyKindPE_servedData",
        postData: {
            mainId: responseJSON.mainId
        },
        colModel : [ {colHeader : i18n.cls1220m04['ploanObj.servedData.companyRepresentativeType'], name : 'companyRepresentativeType', width : 160,  align: "left", sortable : false}
        			,{colHeader : i18n.cls1220m04['ploanObj.servedData.servedTitle'], name : 'servedTitle', width : 160,  align: "left", sortable : false}
                    ,{colHeader : i18n.cls1220m04['ploanObj.servedData.companyName'], name : 'companyName', width : 200,  align: "left", sortable : false}
                    ,{colHeader : i18n.cls1220m04['ploanObj.servedData.taxNo'], name : 'taxNo', width : 80,  align: "left", sortable : false}
                    ,{colHeader : i18n.cls1220m04['ploanObj.servedData.comment'], name : 'comment', width : 300,  align: "left", sortable : false}
                    , {name : 'oid', hidden : true}
                   ]				
	});
	
	var gridC122M01C = $("#gridC122M01C").iGrid({
        handler: 'cls1221gridhandler',
        height: 100,
        width: 800,
        autowidth: false,
        action: "queryC122M01C",
        postData: {
            mainId: responseJSON.mainId
        },
        colModel : [ {colHeader : i18n.cls1220m04['C122M01C.brNoBef'], name : 'brNoBef', width : 120,  align: "left", sortable : false}
                    ,{colHeader : i18n.cls1220m04['C122M01C.brNoAft'], name : 'brNoAft', width : 120,  align: "left", sortable : false}
                    ,{colHeader : i18n.cls1220m04['C122M01C.creator'], name : 'creator', width : 100,  align: "left", sortable : false}
                    ,{colHeader : i18n.cls1220m04['C122M01C.createTime'], name : 'createTime', width : 120,  align: "left", sortable : false}
                    ,{colHeader : i18n.cls1220m04['C122M01C.memo'], name : 'memo', width : 320,  align: "left", sortable : false}
                    , {name : 'oid', hidden : true}
                   ]				
	});
	
	//存檔
	var saveAction = function(opts){
        	return $.ajax({
                type: "POST",
                handler: "cls1220m04formhandler",
                data:$.extend( {
                	formAction: "saveMain",
                    page: responseJSON.page,
                    mainOid: responseJSON.mainOid
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
                )
            }).done(function(json){
				tabForm.injectData(json);
				//更新 opener 的 Grid
				API.triggerOpener();
			});
    }
	
	var Open_OpenCustInfoDiv = function(){
    	var buttons = {};
		buttons[i18n.def.close] = function(){				
			$.thickbox.close();
        };
        $("#OpenCustInfoDiv").jqGrid('setGridParam', {
			postData: {
				mainId: responseJSON.mainId,
				isSearchByCustId: 'N'
			}
		});
        $("#OpenCustInfoDiv").trigger("reloadGrid");
        var gridCount = $("#OpenCustInfoDiv").getGridParam('records');
    	if(gridCount>0){
    		$('#btnMakeCust').hide();
    	}
    	$("#queryCustIdDiv").hide();
    	$("#queryCustId").val('');
       	$("#OpenCustInfo").thickbox({
       		title: i18n.cls1220m04['button.makeCust'],
            width: 800,
            height: 250,
            modal: true,
			align: "center",
			valign: 'bottom',
            i18n: i18n.def,
            buttons: {
				"sure": function(){
					var row = $("#OpenCustInfoDiv").getGridParam('selrow');
			        var data;
			        if (row) {
			            data = $("#OpenCustInfoDiv").getRowData(row);
						$.ajax({
						type : "POST",
						handler : "cls1220m10formhandler",
						data : {
							formAction : "newC122S01H_flowId01",
							mainId: json.mainId,
							dataMainOid : data.oid,
							remainId: data.mainId,
							custId: data.custId,
							custName: data.custName,
							dupNo: data.dupNo,
							flowId: "1"
							}
						}).done(function(responseData){
							if(responseData.Success){
//									window.close();
								if(responseData.rpaError){
									MegaApi.showErrorMessage(responseData.rpaError ,function(){
										reLoad(responseData ,json);
										CommonAPI.triggerOpener("gridview", "reloadGrid");
									});
								}else{
									reLoad(responseData ,json);
									CommonAPI.triggerOpener("gridview", "reloadGrid");
								}
//									reLoad(responseData ,json);
							}else{
								CommonAPI.showErrorMessage(responseData.Message);
							}
						});
			        } else {
			            MegaApi.showErrorMessage(i18n.def["confirmTitle"], i18n.def["grid.selrow"]);
			        }
					
					

					},
					"cancel": function(){
						$.thickbox.close();
						}
					}
        });
    }
	
	/**
	 * 從債務人引入選單
	 */
	function Open_Rel_OpenCustInfoDiv(relForm) {
    	var buttons = {};
		buttons[i18n.def.close] = function(){				
			$.thickbox.close();
        };
        $("#OpenCustInfoDiv").jqGrid('setGridParam', {
			postData: {
				mainId: responseJSON.mainId,
				custId: '',
				isSearchByCustId: 'Y'
			}
		});
        $("#OpenCustInfoDiv").trigger("reloadGrid");
        $("#queryCustIdDiv").show();
    	$("#queryCustId").val('');
    	$('#btnMakeCust').hide();
       	$("#OpenCustInfo").thickbox({
       		title: i18n.cls1220m04['button.makeCust'],
            width: 800,
            height: 350,
            modal: true,
			align: "center",
			valign: 'bottom',
            i18n: i18n.def,
            buttons: {
				"sure": function(){
					var data;
					var row = $("#OpenCustInfoDiv").getGridParam('selrow');
			        if (row) {
			        	data = $("#OpenCustInfoDiv").getRowData(row);
			        	relForm.find("#rel_remainId").val(data.mainId);
						relForm.find("#rel_custId").val(data.custId);
						relForm.find("#rel_dupNo").val(data.dupNo);
						relForm.find("#rel_custName").text(data.custName);
						relForm.find("#rel_flowId").val("1");
			            $.thickbox.close();
			        }
				},
				"cancel": function(){
					$.thickbox.close();
				}
			}
        });
	}
	
	// 重設OpenCustInfoDiv
	$("#resetOpenCustInfoDiv").click(function(){
		var queryCustId = $("#queryCustId").val();
		$("#OpenCustInfoDiv").jqGrid('setGridParam', {
			postData: {
				mainId: responseJSON.mainId,
				custId: queryCustId,
				isSearchByCustId: 'Y'
			}
		});
        $("#OpenCustInfoDiv").trigger("reloadGrid");
	});

	//iXML
	if(json.showIxml != "Y"){
		$(".ploan_ixml_allowed").hide();
	}

	if(json.applyKind=="P" || json.applyKind=="E" || json.applyKind=="I" || json.applyKind=="J"){
		var ploanGridRelateCase = $("#ploanGridRelateCase").iGrid({
	        handler: 'cls1221gridhandler',
	        height: 80,
	        width: 400,
	        autowidth: false,
	        action: "queryApplyKindPE_relateCase",
	        postData: {
	        	'ploanCaseId': $("#ploanCaseId").val(), 'applyKind':'Q'
	        },
	        colModel : [ {colHeader : i18n.cls1220m04['label.ploanRelateCase.custId'], name : 'custId', width : 100,  align: "left", formatter: 'click', onclick : openPloanRelateCase}
			            ,{colHeader : i18n.cls1220m04['label.ploanRelateCase.custName'], name : 'custName', width : 100,  align: "left"}
			            ,{colHeader : i18n.cls1220m04['label.ploanRelateCase.custPos'], name : 'ploanCasePos', width : 80,  align: "left"}
			            ,{colHeader : i18n.cls1220m04['label.docStatusGrid.02'], name : 'flowdocStatus', width : 80,  align: "left", formatter: 'click', onclick : pageAction.openDocByGrid}
			            ,{colHeader : " ", name : 'isClosed', width : 30,  align: "center"}
			            , {name : 'oid', hidden : true}
			            , {name : 'mainId', hidden : true}
			            , {name : 'docStatus', hidden : true}
			            , {name : 'applyKind', hidden : true}
			            , {name : 'dupNo', hidden : true}
			            , {name : 'ploanCasePosCode', hidden : true}
			            , {name : 'remainId', hidden : true}
			            , {name : 'flowId', hidden : true}
			           ]
		});	
		$("#custNameLabel").val(i18n.cls1220m04['C122M01A.custName']);	
	
		$("#gridCntrInfo").iGrid({
	        handler: 'cls1221gridhandler',
	        height: 80, width: 600,
	        autowidth: false,
	        needPager: false,
	        action: "queryApplyKindPE_L140M01A",
	        postData: {
	        	mainId: responseJSON.mainId
	        },
	        colModel : [ {colHeader : i18n.cls1220m04['label.gridCntrInfo.cntrNo'], name : 'cntrNo', width : 100,  align: "left", sortable : false,formatter: 'click', onclick : openL140M01A}
	                    ,{colHeader : i18n.cls1220m04['label.gridCntrInfo.caseNo'], name : 'caseNo', width : 200,  align: "left", sortable : false}
	                    ,{colHeader : i18n.cls1220m04['label.gridCntrInfo.caseDate'], name : 'caseDate', width : 80,  align: "left", sortable : false}
	                    ,{colHeader : i18n.cls1220m04['label.gridCntrInfo.endDate'], name : 'endDate', width : 80,  align: "left", sortable : false}
	                    ,{colHeader : i18n.cls1220m04['label.gridCntrInfo.chkYN'], name : 'chkYN', width : 30,  align: "center", sortable : false}
	                    , {name : 'tabMainId', hidden : true}
	                    , {name : 'caseMainId', hidden : true}
	                   ]				
		});	
		$("div.ploan_relateWithMainBorrower").hide();
	}else{
		$(".ploan_decide_by_main_borrowser").hide();
		$("#custNameLabel").val(i18n.cls1220m04['label.ploanRelateCase']);
	}
	
	function openL140M01A(cellvalue, options, rowObject){
		$.ajax({type : "POST", handler : 'cls1220m01formhandler', action : "getL140M01AParam",
			data : {
				'cntrNoMainId': rowObject.tabMainId
			}
		}).done(function(json){				
			if(json.isFound=="Y"){
				$.form.submit({
					 url: "../../cls/cls1151s01",
					 data: $.extend(
						json
						, {}
					 ),
					 target: json.l140m01a_oid
				 });
			}				
		});
	}
	function openPloanRelateCase(cellvalue, options, rowObject){
    	$.form.submit({
			url : '../cls1220m04/01',
			data : {
                mainOid: rowObject.oid,
                mainId: rowObject.mainId,
                mainDocStatus: rowObject.docStatus,
                noOpenDoc: true
            },
            target: rowObject.oid
		});
    };
    
    
    var checkDataToMakeCust = function(){
    	var my_dfd = $.Deferred();
    	$.ajax({
    		type : "POST",
			handler : "cls1220m10formhandler",
			data : {
				formAction : "checkDataToMakeCust",
				mainOid : responseJSON.mainOid,
				mainId: responseJSON.mainId
			}
        }).done(function(responseData){
			var Success = responseData.Success;
			if(responseData.Success){
				my_dfd.resolve();
			}else{
				CommonAPI.showErrorMessage(responseData.Message);
				my_dfd.reject();
			}
		});
    	return my_dfd;
    }

    var check0024Data = function(){
    	var my_dfd = $.Deferred();
    	$.ajax({
    		type : "POST",
			handler : "cls1220m10formhandler",
			data : {
				formAction : "check0024Data",
				mainOid : responseJSON.mainOid,
				mainId: responseJSON.mainId
			}
        }).done(function(responseData){
			if(responseData.Success){
				my_dfd.resolve();
			}else{
				CommonAPI.showErrorMessage(responseData.Message);
				my_dfd.reject();
			}
		});
    	return my_dfd;
    }
    
    
    var AddNewCust = function(){
    	var my_dfd = $.Deferred();
    	$.ajax({
    		type : "POST",
			handler : "cls1220m10formhandler",
			data : {
				formAction : "AddNewCust",
				mainOid : responseJSON.mainOid,
				mainId: responseJSON.mainId
			}
        }).done(function(responseData){
			var Success = responseData.Success;
			if(responseData.Success){
				my_dfd.resolve({'addCustByUser':'S','mainOid':responseData.mainOid,'mainId':responseData.mainId,'custId':responseData.custId,'dupNo':responseData.dupNo});
			}else{
				CommonAPI.showErrorMessage(responseData.Message);
				
			}
		});
    	return my_dfd;
    }
    
    var UpdateCust = function(rowObject){
    	var my_dfd = $.Deferred();
    	$.ajax({
    		type : "POST",
			handler : "cls1220m10formhandler",
			data : {
				formAction : "UpdateCust",
				mainOid : responseJSON.mainOid,
				mainId: responseJSON.mainId,
				custId: rowObject.custId,
				dupNo: rowObject.dupNo
			}
        }).done(function(responseData){
			var Success = responseData.Success;
			if(responseData.Success){
				my_dfd.resolve();
			}else{
				CommonAPI.showErrorMessage(responseData.Message);
				my_dfd.reject();
			}
		});
    	return my_dfd;
    }
    

	
	//上傳檔案按鈕
	$("#uploadFile").click(function(){
		$("#formId_temp").val("");
		$("#stakeholderID_temp").val("");
		var limitFileSize=9437103;
		var openUploadDialog = MegaApi.uploadDialog({
			fieldId:"userUpload" + userInfo.userId,
            fieldIdHtml:"size='30'",
            fileDescId:"fileDesc",
            fileDescHtml:"size='30' maxlength='30'",
			subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
			limitSize:limitFileSize,
            width:400,
            height:300,			
			data:{
				mainId: responseJSON.mainId,
				flag:"9"
			},
			// 新增上傳前動作
			beforeUpload : function(obj) {
				// 檢查上傳檔案類型，可用底層fileCheck參數檢查，因客製化訊息改寫
				// MEGAImageService.UPLOAD_AVALIBLE_EXTENTION_EXPR="PDF|JPG|PNG|TIF|DOC|DOCX|XLS|XLSX|HTML|CSV"
				var fileVal = $("#" + this.fieldId).val();
				var fileCheck = ['PDF', 'JPG', 'PNG', 'TIF', 'DOC', 'DOCX', 'XLS', 'XLSX', 'HTML', 'CSV'];
				var regs = "";
	            $(fileCheck).each(function(index, value){
	                regs += (value + "|");
	            });
	            regs = regs.replace(/\|$/, "");
	            if (!((new RegExp("(" + regs + ")$", "i")).test(fileVal))) {
					CommonAPI.showErrorMessage(i18n.cls1220m04['Message.dataCheck.15'] + "  (" + regs + ")");
	                return false;
	            }
				// 檢核文件種類
				if (!$("#uploadFormId").val()) {
					API.showErrorMessage(i18n.def['grid_selector']);
					return false;
				} else {
					// 將選擇的資料格式化後存放到TEMP
					var formId = $("#uploadFormId").val();
					var rel = $("#uploadFileRelationship").val().split("-")[0];
					var custId = $("#uploadFileRelationship").val().split("-")[1];
					$("#formId_temp").val(formId);
					$("#stakeholderID_temp").val(custId);
				}
			},
			success : function(obj) {
				attchGrid.trigger("reloadGrid");
				// 執行上傳文件數位化系統
				obj.formId = $("#formId_temp").val();
				obj.stakeholderID = $("#stakeholderID_temp").val();
				eLoanUploadImageFile(obj);
			}
	    });
		var optionData = [];
		// 1.先開啟上傳視窗，查詢主借人與從債務人下拉選單資料
		$.when(openUploadDialog, getUploadFileRelationshipList(optionData)).then(function(r){
			// 2.複製DIV到裡面，調整ID、NAME
			if ($("#uploadFileDialog").find("#uploadFileDiv_temp").length ==  0) {
				$("#userUpload" + userInfo.userId).after($("#uploadFileDiv_temp").clone());
				var uploadFileDiv = $("#uploadFileDialog").find("#uploadFileDiv_temp");
				uploadFileDiv.attr("id", "uploadFileDiv").show();
				uploadFileDiv.find("[id$='_temp'],[name='_temp']").each(function(){
					if(this.id) {
						this.id = this.id.replace("_temp","");
					}
					if(this.name) {
						this.name = this.name.replace("_temp","");
					}
				});
				// 3.塞入主借人與從債務人下拉選單資料
				var uploadFileRelationshipSelect = uploadFileDiv.find("#uploadFileRelationship");
				uploadFileRelationshipSelect.empty();
				for (var i=0; i< optionData.length; i++) {
					var option = $("<option></option>");
					option.attr("value", optionData[i].value);
					option.text(optionData[i].text);
					uploadFileRelationshipSelect.append(option);
				}
				// J-112-0164 隱藏文件種類下拉選項
				// CXO00970：MyData資料
				// CXO00980：財力證明-線上補件 Ploan上傳
				// CXO00990：其他表單-線上補件 Ploan上傳
				// CLI00200：兆豐銀行貸款申請書(線上版)
				// CLI00300：兆豐銀行房屋貸款申請書(線上版)
				var removeOptions = ['CXO00970', 'CXO00980', 'CXO00990', 'CLI00200', 'CLI00300'];
				for (var i=0; i< removeOptions.length; i++) {
					$("#uploadFormId option[value='" + removeOptions[i] + "']").remove();
				}
			}
		});
	});
	
	/**
	 * 取得上傳視窗對應客戶下拉選單
	 */
	function getUploadFileRelationshipList(optionData) {
		return $.ajax({
			type : "POST",
			handler : "cls1220m01formhandler",
			data : {
				formAction : "getUploadFileRelationshipList",
				mainOid : responseJSON.mainOid,
				mainId: responseJSON.mainId
			}
		}).done(function(json){
			for(var key in json) {
				var text = "";
				optionData.push({value:key, text:json[key]});
			}
		});
	}
	
	//開文件數位化系統畫面，顯示符合的影像
	$("#btnCLSQuery").click(function(){
		getEloanQueryImageListUrl().done(function(json){
			var checksum = json.checksum.replace(/\r/g,"").replace(/\n/g,"");
			$.form.submit({
				url: json.url,
				type: "POST",
				target: responseJSON.mainId,
				data: {
					'BranchCode': json.BranchCode,
					'UserCode': json.UserCode,
					'CaseNo': json.CaseNo,
					'id': json.id,
					'Sender': json.Sender,
					'checksum': checksum
				}
			});
	    });
	});
	
	//產生文件掃描封面條碼
	$("#btnPrintBarcode").click(function(){
		$.form.submit({
           url: "../../simple/FileProcessingService",
           target: "_blank",
           data: {
               'mainId': responseJSON.mainId,
               'fileDownloadName': "CLS1220BarcodeR01.pdf",
               serviceName: "cls1220barcoder01rptservice"
           }
       });
	});
	
	//同步檔案清單
	$("#btnRefreshAttchGrid").click(function(){
		attchGrid.trigger("reloadGrid");
		mEGAImageGrid.trigger("reloadGrid");
	});
	
	//下載
	$("#btnRPAQueryCLImage").click(function(){
		$.form.submit({
        	url: __ajaxHandler,
     		target : "_blank",
     		data : { 
     			'_pa' : 'lmsdownloadformhandler' ,
     			'serviceName': "cls1220MegaImageFileService" ,
     			'custId' : $("#custId").val(),
     			'mainId' : responseJSON.mainId ,     			
     			'fileDownloadName': $("#ploanCaseId").val() + "_MEGAImage" +'.zip'
     		}
     	});
	});
	
	//刪除檔案按鈕
	$("#deleteFile,#deleteFileMegaImage").click(function(){
		var buttonId = $(this).attr("id");
		var actionGrid;
		if ("deleteFileMegaImage" == buttonId) {
			actionGrid = mEGAImageGrid;
		} else {
			actionGrid = attchGrid;
		}
		var select  = actionGrid.getGridParam('selrow');
		// confirmDelete=是否確定刪除?
		CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
			if(b){				
				var data = actionGrid.getRowData(select);
				if(data.oid == "" || data.oid == undefined || data.oid == null){		
					// TMMDeleteError=請先選擇需修改(刪除)之資料列
					CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
					return;
				}
				if(!data.fieldId.match(/^userUpload/) && !data.fieldId.match(/^MEGAImage/)) {
					CommonAPI.showMessage("客戶自行上傳檔案不能刪除");
					return;
				}
				$.ajax({
					handler : "cls1220m01formhandler",
					type : "POST",
					dataType : "json",
					data : {
						formAction : "eloanCloseImage",
						mainId: responseJSON.mainId,
						docId:data.oid,
						docFileOid:data.docFileOid || "",
						fieldId:data.fieldId
					},
					success : function(obj) {
						attchGrid.trigger("reloadGrid");
						mEGAImageGrid.trigger("reloadGrid");
					}
				});
			}else{
				return ;
			}
		});
	});

	
	
	
	//待補件 >> 變更狀態為待補件,---要存檔---
	$("#buttonA02").click(function(){
		saveAction({'allowIncomplete':'Y','checkSave':'Y'}).done(function(json){
			if(json.saveOkFlag){
				var dyna = [];
				if(true){
					$.ajax({
						type : "POST",
						handler : "cls1220m10formhandler",
						data : {
							formAction : "updateDocStatus_A02",
							mainOid : responseJSON.mainOid,
							mainId: responseJSON.mainId
						}
					}).done(function(responseData){
						if(responseData.Success){
							CommonAPI.triggerOpener("gridview", "reloadGrid");
							reLoad(responseData ,json);
//								CommonAPI.showMessage(responseData.Message);
						}else{
							CommonAPI.showErrorMessage(responseData.Message);
						}
					});
//					dyna.push(i18n.def.saveSuccess);
				}	
//				API.showMessage(dyna.join("<br/>-------------------<br/>"));
			}
		});
	});

	
	//結案 
	$("#button_caseClosed").click(function(){
		saveAction({'allowIncomplete':'Y','checkSave':'Y','checkCredit':'N'}).done(function(json){
			if(json.saveOkFlag){
				var dyna = [];
				if(true){
					$("#CaseClosedDiv").thickbox({
						title: i18n.cls1220m04['button.caseClosed'], width: 550, height: 350, align: 'center', valign: 'bottom', modal: false, i18n: i18n.def,
						buttons: {
							"sure": function(){
								$.ajax({
									type : "POST",
									handler : "cls1220m10formhandler",
									data : {
										formAction : "CaseClosed",
										mainOid : responseJSON.mainOid,
										mainId: responseJSON.mainId,
										CaseClosedItemI: $('#CaseClosedItemI').val(),
										CaseClosedType: $("input[name='CaseClosedType']:radio:checked").val()
										}
									}).done(function(responseData){
										if(responseData.Success){
											window.close();
											CommonAPI.triggerOpener("gridview", "reloadGrid");
//												reLoad(responseData ,json);
										}else{
											CommonAPI.showErrorMessage(responseData.Message);
										}
									});
								},
								"cancel": function(){
									$.thickbox.close();
									}
								}
					});
				}	
			}
		});
	});
	
	
	//徵信 >> 
	$("#buttonB00").click(function(){
		if(responseJSON.mainApplyKind == "I" || responseJSON.mainApplyKind == "J" ){//青創案件需檢查青創欄位，不符規定不可發動徵信
			$.ajax({
				type: "POST", handler: "cls1220m10formhandler",
				data:{ formAction: "checkC122s01c", 
					mainId: responseJSON.mainId,
					applyKind: responseJSON.mainApplyKind}
			}).done(function(json){
				if(json.alertMsg){
					API.showMessage(i18n.cls1220m04['Message.dataCheck.09']+"<br>"+json.alertMsg);
				}else{
					Open_OpenCustInfoDiv();
				}
			});
		}else{
			Open_OpenCustInfoDiv();
		}
	});
	
	// 調閱30日內同IP進件資料
	$("#sameIpImportData").click(function() {
		$("#SameIpInfoIn30DayDiv").thickbox({
			title : i18n.cls1220m04['button.sameIpImportData'],
			width : 610,
			height : 350,
			align : 'center',
			valign : 'bottom',
			modal : false,
			i18n : i18n.def,
			buttons : {
				"close" : function() {
					$.thickbox.close();
				}
			}
		});
	});

	
	
	//刪除徵信資料
	$("#deleteDocStatusGrid").click(function(){
		var select  = docStatusGrid.getGridParam('selrow');					
		var data = docStatusGrid.getRowData(select);
		debugger;
		if(data.oid == "" || data.oid == undefined || data.oid == null){		
			// TMMDeleteError=請先選擇需修改(刪除)之資料列
			CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
			return;
		}
		$.ajax({
			handler : "cls1220m10formhandler",
			type : "POST",
			dataType : "json",
			data : {
				formAction : "deleteDocStatusGrid",
				oids:data.oid
			},
			success : function(responseData) {
			    if(responseData.Success){
                     docStatusGrid.trigger("reloadGrid");
                 }else{
                     CommonAPI.showErrorMessage(responseData.Message);
                 }

			}
		});
	});
	
	$("#btnMakeCust").click(function(){ //產生個金徵徵信資料
		//因主管也可以改派案資料，所以派案前先儲存
        saveAction({'allowIncomplete':'Y','checkSave':'Y','checkCredit':'N'}).done(function(json){
        	//存完檔案先檢查資料
        	if(json.saveOkFlag){
        		var dyna = [];
        		if(true){
        			checkDataToMakeCust().done(function(json){ //檢查基本必填欄位
        				check0024Data().done(function(){ //檢查0024是否有建檔
        					AddNewCust().done(function(json){ //產生一筆個金徵信資料
        						//要不要自動更新要確認一下
        						UpdateCust(json).done(function(){
        							$("#OpenCustInfoDiv").trigger("reloadGrid");
        							$("#btnMakeCust").hide();
        							
        						});
        					});
        				});	
            		});			
            	}	
            	if(json.IncompleteMsg){
            		dyna.push(json.IncompleteMsg);
            		API.showMessage(dyna.join("<br/>-------------------<br/>"));
            	}	
            }
        });	
    });

	//新增簽報書
	$("#buttonB0304").click(function(){
	    FastCreditLoans.init();
        FastCreditLoans.open();
    });
	
	$("select[name^=ploan_]").readOnly();
	// 調閱資料須加註查詢理由，查詢是否當日已有紀錄
	// J-110-0395調閱資料須加註查詢理由
	$.ajax({
        handler: 'cls1220m01formhandler',
        action: 'findTodayRecord',
        data : {
            mainId: responseJSON.mainId
        }
    }).done(function(r){
		if (r.queryReasonIsRecorded) {
			$("#queryReasonIsRecorded").val(r.queryReasonIsRecorded);
		}
	});
	
	// 線下從債務人維護
	// 從債務人檔案清單
	var relDocFileGrid = $("#relDocFileGrid").iGrid({
        handler: 'cls1221gridhandler',
        height: 100,
        width: 400,
        autowidth: false,
        action: "queryDocFileListById",
        sortname: "flag|uploadTime",
        sortorder: "asc|asc",
        postData: {
            mainId: responseJSON.mainId,
            custId: ""
        },
        needPager:false,
        colModel : [ {
			colHeader : i18n.cls1220m04['label.docFileFlag'],
			name : 'flag',
			width : 35,
			sortable : false
        }, {
			colHeader : i18n.cls1220m04['label.srcFileName'],
			name : 'srcFileName',
			width : 120,
			align: "left",
			sortable : false,
			formatter : '',
			onclick : openFile
        }, {
			colHeader : i18n.cls1220m04['label.uploadTime'],
			name : 'uploadTime',
			width : 100,
			sortable : false
		}, {
			colHeader : i18n.cls1220m04['label.fileSrc'],
			name : 'fileSrc',
			width : 35,
			sortable : false
		}, {
			name : 'fieldId',
			hidden : true
		}, {
			name : 'oid',
			hidden : true
		}]				
	});
	$("#addPloanRelateCase, #editPloanRelateCase, #deletePloanRelateCase").click(function(){
		var mType;
		var select  = $("#ploanGridRelateCase").getGridParam('selrow');					
		var data = $("#ploanGridRelateCase").getRowData(select);
		var btnId = $(this).attr("id");
		if ("addPloanRelateCase" == btnId) {
			mType = 1;
		} else if ("editPloanRelateCase" == btnId) {
			mType = 2;
		} else if ("deletePloanRelateCase" == btnId) {
			mType = 3;
		}
		var relForm = $("#ploanRelateCaseDialogForm");
		relForm.reset();
		relForm.find("#mTypeC").text($(this).text().trim());
		if(1 != mType 
				&& (data.mainId == "" || data.mainId == undefined || data.mainId == null)){		
			// TMMDeleteError=請先選擇需修改(刪除)之資料列
			CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
			return;
		}
		$("#ploanRelateCaseDiv").thickbox({
			title: i18n.cls1220m04['label.ploanRelateCase'],
			height: 450,
			width: 450,
			align: 'center',
			valign: 'bottom',
			modal: false,
			i18n: i18n.def,
			open: function(){
				if(1 != mType) {
					relForm.find("#rel_mainId").val(data.mainId);
					relForm.find("#rel_custId").val(data.custId);
					relForm.find("#rel_dupNo").val(data.dupNo);
					relForm.find("#rel_custName").text(data.custName);
					relForm.find("#rel_ploanCasePosCode").val(data.ploanCasePosCode);
			    	relForm.find("#rel_remainId").val(data.remainId);
			    	relForm.find("#rel_flowId").val(data.flowId);
			    	$("#getCustName").hide();
				} else {
					$("#getCustName").show();
				}
				relDocFileGrid.jqGrid('clearGridData');
				relDocFileGrid.jqGrid('setGridParam', {
					postData: {
						mainId: responseJSON.mainId,
						custId: relForm.find("#rel_custId").val()
					}
				});
				relDocFileGrid.trigger("reloadGrid");
			},
			buttons: {
				"sure": function(){
					if (!relForm.valid()) {
						return;
					}
					if(3 == mType) {
						// Message.dataCheck.11=＊從債務人存在文件，刪除從債務人檔案會同時刪除
						// confirmDelete=是否確定刪除?
						var msg = i18n.cls1220m04["Message.dataCheck.11"] + "<br/>" + i18n.def["confirmDelete"]
						CommonAPI.confirmMessage(msg,function(b){
							if(b){				
								ploanRelateCaseManage(mType, relForm);
							} else{
								return ;
							}
						});
					} else {
						ploanRelateCaseManage(mType, relForm);
					}
				},
				"cancel": function(){
					$.thickbox.close();
				}
			}
		});
	});
	
	/**
	 * 線下進件從債務人維護
	 */
	function ploanRelateCaseManage(mType, relForm) {
		var data = relForm.serializeData();
		var data = $.extend(data, {
			mType: mType,
			mainId: responseJSON.mainId
		});
		$.ajax({
			handler : "cls1220m10formhandler",
			action: "ploanRelateCaseManage",
			data: data
		}).done(function(result){
			$("#ploanGridRelateCase").trigger("reloadGrid");
			mEGAImageGrid.trigger("reloadGrid");
			attchGrid.trigger("reloadGrid");
			$.thickbox.close();
		});
	}
	
	//引進客戶
    $("#getCustName").click(function(){
    	var relForm = $("#ploanRelateCaseDialogForm");
    	Open_Rel_OpenCustInfoDiv(relForm);
	});
	
    $("#ploanRelateCaseDialogForm").find("#rel_custName").click(function(){
    	var relForm = $("#ploanRelateCaseDialogForm");
    	var data = {};
    	data["custId"] = relForm.find("#rel_custId").val();
    	data["dupNo"] = relForm.find("#rel_dupNo").val();
    	data["mainId"] = relForm.find("#rel_remainId").val();
    	data["remainId"] = relForm.find("#rel_remainId").val();
    	data["custName"] = relForm.find("#rel_custName").text();
    	data["flowId"] = relForm.find("#rel_flowId").val();
    	if (data.remainId) {
    		pageAction.openDoc(data);
    	}
	});
	
});

var FastCreditLoans = {
    json:null,
    init:function(){
        var brmpFormName  = "createCntrNo_brmp_creditCheckForm";

        $('#'+brmpFormName).buildItem();

        $('#createCntrNo_brmp_creditCheck_custPos_rKindM').change(function(){
                var value = $(this).val() + '';
                var $form = $('#'+brmpFormName);
                $form.find('.brmp_creditCheck_custPos_rKindD').hide().val('');
                switch (value) {
                    case '1':
                    case '2':
                        var $obj = $form.find('#createCntrNo_brmp_creditCheck_custPos_rKindD' + value);
                        $obj.val($form.find('#createCntrNo_brmp_creditCheck_custPos_rKindD').val()).show();
                        break;
                    case '3':
                        var s = ($form.find('#createCntrNo_brmp_creditCheck_custPos_rKindD').val() || '').split('');
                        $form.find('#createCntrNo_brmp_creditCheck_custPos_rKindD31').val(s.length >= 2 ? s[0] : '').show();
                        $form.find('#createCntrNo_brmp_creditCheck_custPos_rKindD32').val(s.length >= 2 ? s[1] : '').show();
                        break;
                }

                //當關係類別為2-親屬關係, 非X0-本人時, 顯示與借款人同住選項
                brmp_isShowLiveWithBorrowerItem(value, $form.find('#createCntrNo_brmp_creditCheck_custPos_rKindD'+ value).val(), $form);

        });

        $('#'+brmpFormName).find('#createCntrNo_brmp_creditCheck_custPos_rKindD1,#createCntrNo_brmp_creditCheck_custPos_rKindD2').change(function(){
            $('#'+brmpFormName).find('#createCntrNo_brmp_creditCheck_custPos_rKindD').val($(this).val());
            var formObject = $('#'+brmpFormName);
            brmp_isShowLiveWithBorrowerItem(formObject.find('#createCntrNo_brmp_creditCheck_custPos_rKindM').val(), $(this).val(), formObject);
        });

        $('#'+brmpFormName).find('#createCntrNo_brmp_creditCheck_custPos_rKindD31,#createCntrNo_brmp_creditCheck_custPos_rKindD32').change(function(){
            $('#'+brmpFormName).find('#createCntrNo_brmp_creditCheck_custPos_rKindD').val($('#createCntrNo_brmp_creditCheck_custPos_rKindD31').val() + $('#createCntrNo_brmp_creditCheck_custPos_rKindD32').val());
        });
        $('#createCntrNo_brmp_imp_onlineCaseNo').click(function(){
            var $form = $('#'+brmpFormName);

            var createCntrNo_brmp_creditCheck_custId = $form.find("#createCntrNo_brmp_creditCheck_custId").val();
            if(createCntrNo_brmp_creditCheck_custId==""){
                API.showMessage("請先輸入「借款人統編」");
            }else{
                $.ajax({ type: "POST",handler: "cls1141m01formhandler",
                    data: {
                         'formAction': "get_latest_c122m01a_applyKind_P"
                        , 'custId' : createCntrNo_brmp_creditCheck_custId
                    }
                }).done(function(json){
					if(json.onlineCaseNo){
						$form.injectData({'createCntrNo_brmp_creditCheck_onlineCaseNo':json.onlineCaseNo });
						$form.injectData({'createCntrNo_brmp_creditCheck_usePlan':json.usePlan });
					}else{
						API.showMessage("客戶"+createCntrNo_brmp_creditCheck_custId+" 近期無「線上申貸」記錄");
					}
					//J-111-0160 無紙化簽報，檢核行銷為銀行行員，前端自動帶入引介來源
					if(json.introduceSrc){
						$form.injectData({'createCntrNo_brmp_creditCheck_introduceSrc':json.introduceSrc });
						IntroductionSource_PaperlessSigning.change();
						if(json.introduceSrc=="1"){
							$form.injectData({'megaEmpNo':json.marketStaff });
							$form.injectData({'megaEmpBrNo':json.megaEmpBrNo });
							$form.injectData({'megaEmpBrNoName':json.megaEmpBrNoName });
						}
						if(json.introduceSrc=="3"){
							IntroductionSource_PaperlessSigning.loadMegaSubUnitNo(json.megaCode);
							$form.injectData({'megaCode':json.megaCode });
							$form.injectData({'subEmpNo':json.subEmpNo });
						}
					}
					else{
						$form.injectData({'createCntrNo_brmp_creditCheck_introduceSrc':'' });
						IntroductionSource_PaperlessSigning.change();
					}
				});
            }
        });
        //新增>> 快速審核(信貸)
        $("#createCntrNo_brmp_creditCheckForm").find("#createCntrNo_brmp_creditCheck_custId").change(function(){
            prepare_gen_71_brmp_termGroupRule($("#createCntrNo_brmp_creditCheck_custId").val(), $("#createCntrNo_brmp_creditCheck_dupNo").val(),
                    $("select#createCntrNo_brmp_creditCheck_termGroup"), $("#createCntrNo_brmp_creditCheck_termGroupSub"),
                    $("#span_brmp_creditCheck_termGroupRuleResultText"));
        });
        $("#createCntrNo_brmp_creditCheckForm").find("#createCntrNo_brmp_creditCheck_dupNo").change(function(){
            prepare_gen_71_brmp_termGroupRule($("#createCntrNo_brmp_creditCheck_custId").val(), $("#createCntrNo_brmp_creditCheck_dupNo").val(),
                    $("select#createCntrNo_brmp_creditCheck_termGroup"), $("#createCntrNo_brmp_creditCheck_termGroupSub"),
                    $("#span_createCntrNo_fast_prodKind71_termGroupRuleResultText"));
        });
        $("#createCntrNo_brmp_creditCheck_custId").change(function(){
            prepare_gen_71_brmp_sum_compensationAmt($(this).val(),$("#createCntrNo_brmp_creditCheck_dupNo").val());
        });
        $.ajax({
            type : "POST",
            handler: "cls1141m01formhandler",
            action: "getPloanPlan"
        }).done(function(responseData){
			// 使用專案
			$("#createCntrNo_brmp_creditCheck_usePlan").setItems({
				// i18n : i18n.samplehome,
				item : responseData.usePlanItem,
				format : "{key}" // ,
			// value :
			});
		});
    },
	open:function(){

		$.thickbox.close();

        var _form = "createCntrNo_brmp_creditCheckForm";
		var $form = $('#'+_form);
        $("#"+_form).reset();

        //J-112-0145_10702_B1001
        $("#" + _form).injectData({
            'createCntrNo_brmp_creditCheck_pConBegEnd_fg': '2'  //在 reset 後，預計12期
        });

        if(true){
            $("#"+_form).find("#createCntrNo_brmp_creditCheck_introduceSrc").removeOptions(["2","4","8"]);
        }
        $("#"+_form).find(".brmp_creditCheck_custPos_rKindD").hide();
        $("#"+_form).find(".brmp_creditCheck_custPos_isLiveWithBorrowerSpan").hide();
        $("#"+_form).find("#tr_EsgGtype_brmp").hide();
        $("#"+_form).injectData({
              'createCntrNo_brmp_creditCheck_dupNo':'0'
            , 'createCntrNo_brmp_creditCheck_custId': $('#custId').val()
            , 'createCntrNo_brmp_creditCheck_onlineCaseNo':$('#ploanCaseNo').val()
            , 'createCntrNo_brmp_creditCheck_otherAdjustAmt':'0'
            , 'createCntrNo_brmp_creditCheck_compensationAmt':'0'
            , 'createCntrNo_brmp_creditCheck_esggnLoanFg':'N'    //在 reset 後，綠色支出類型 會被清空
            , 'createCntrNo_brmp_creditCheck_usePlan':$('#usePlan').val()
        });
        prepare_gen_71_brmp_termGroupRule($("#createCntrNo_brmp_creditCheck_custId").val(), $("#createCntrNo_brmp_creditCheck_dupNo").val(),
                            $("select#createCntrNo_brmp_creditCheck_termGroup"), $("#createCntrNo_brmp_creditCheck_termGroupSub"),
                            $("#span_brmp_creditCheck_termGroupRuleResultText"));
        $.ajax({ type: "POST",handler: "cls1141m01formhandler",
            data: {
                 'formAction': "get_latest_c122m01a_applyKind_P"
                , 'custId' : $('#custId').val()
            }
        }).done(function(json){
			if(json.onlineCaseNo){
				 $("#"+_form).injectData({'createCntrNo_brmp_creditCheck_usePlan':json.usePlan });
				 if(json.usePlan==null ||json.usePlan==''){
					$('#createCntrNo_brmp_creditCheck_usePlan').val($('#usePlan').val());
				 }
			}else{
				API.showMessage("客戶"+$("#createCntrNo_brmp_creditCheck_custId").val()+" 近期無「線上申貸」記錄3");
			}
			//J-111-0160 無紙化簽報，檢核行銷為銀行行員，前端自動帶入引介來源
			if(json.introduceSrc){
				$form.injectData({'createCntrNo_brmp_creditCheck_introduceSrc':json.introduceSrc });
				IntroductionSource_PaperlessSigning.change();
				if(json.introduceSrc=="1"){
					$form.injectData({'megaEmpNo':json.marketStaff });
					$form.injectData({'megaEmpBrNo':json.megaEmpBrNo });
					$form.injectData({'megaEmpBrNoName':json.megaEmpBrNoName });
				}
				if(json.introduceSrc=="3"){
					IntroductionSource_PaperlessSigning.loadMegaSubUnitNo(json.megaCode);
					$form.injectData({'megaCode':json.megaCode });
					$form.injectData({'subEmpNo':json.subEmpNo });
				}
			}
			else{
				$("#"+_form).injectData({'createCntrNo_brmp_creditCheck_introduceSrc':'' });
				IntroductionSource_PaperlessSigning.change();
			}
		});
        $("#"+_form).find("#div_createCntrNo_brmp_creditCheck_induceFlagOrNot").hide();
        $("#"+_form).find("#tr_createCntrNo_brmp_creditCheck_induceFlagXV").hide();

        $("#createCntrNo_brmp_creditCheckDiv").thickbox({
            title: '本次申貸資料', width: 860, height: 720, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var frmJSON = $("#"+_form).serializeData();

                    var createCntrNo_brmp_creditCheck_custId = frmJSON["createCntrNo_brmp_creditCheck_custId"];
                    var createCntrNo_brmp_creditCheck_dupNo = frmJSON["createCntrNo_brmp_creditCheck_dupNo"];
                    var createCntrNo_brmp_creditCheck_termGroup = frmJSON["createCntrNo_brmp_creditCheck_termGroup"];
                    var createCntrNo_brmp_creditCheck_termGroupSub = frmJSON["createCntrNo_brmp_creditCheck_termGroupSub"];
                    var createCntrNo_brmp_creditCheck_creditLoanPurpose = frmJSON["createCntrNo_brmp_creditCheck_creditLoanPurpose"];
                    var createCntrNo_brmp_creditCheck_onlineCaseNo = frmJSON["createCntrNo_brmp_creditCheck_onlineCaseNo"];

                    var createCntrNo_brmp_creditCheck_custPos_custId = frmJSON["createCntrNo_brmp_creditCheck_custPos_custId"];
                    var createCntrNo_brmp_creditCheck_custPos_dupNo = frmJSON["createCntrNo_brmp_creditCheck_custPos_dupNo"];
                    var createCntrNo_brmp_creditCheck_custPos_rKindM = frmJSON["createCntrNo_brmp_creditCheck_custPos_rKindM"];
                    var createCntrNo_brmp_creditCheck_custPos_rKindD = frmJSON["createCntrNo_brmp_creditCheck_custPos_rKindD"];

                    var createCntrNo_brmp_creditCheck_lnYear = frmJSON["createCntrNo_brmp_creditCheck_lnYear"];
                    var createCntrNo_brmp_creditCheck_lnMonth = frmJSON["createCntrNo_brmp_creditCheck_lnMonth"];
                    var createCntrNo_brmp_creditCheck_esggnLoanFg = frmJSON["createCntrNo_brmp_creditCheck_esggnLoanFg"]
                    var createCntrNo_brmp_creditCheck_esggtype = frmJSON["createCntrNo_brmp_creditCheck_esggtype"]
                    var createCntrNo_brmp_creditCheck_esggtypeZMemo = frmJSON["createCntrNo_brmp_creditCheck_esggtypeZMemo"]
                    var createCntrNo_brmp_creditCheck_houseLoanFlag = frmJSON["createCntrNo_brmp_creditCheck_houseLoanFlag"]
                    var usePlan = frmJSON["createCntrNo_brmp_creditCheck_usePlan"]
                    //消金信貸，從債務人 應為 N:一般保證人
                    prepare_gen_71_brmp_caseDoc_tabDoc(createCntrNo_brmp_creditCheck_custId, createCntrNo_brmp_creditCheck_dupNo
                            , createCntrNo_brmp_creditCheck_termGroup, createCntrNo_brmp_creditCheck_creditLoanPurpose, createCntrNo_brmp_creditCheck_onlineCaseNo
                            , createCntrNo_brmp_creditCheck_custPos_custId, createCntrNo_brmp_creditCheck_custPos_dupNo
                            , createCntrNo_brmp_creditCheck_custPos_rKindM, createCntrNo_brmp_creditCheck_custPos_rKindD
                            , createCntrNo_brmp_creditCheck_lnYear, createCntrNo_brmp_creditCheck_lnMonth
                            , createCntrNo_brmp_creditCheck_esggnLoanFg
                            , createCntrNo_brmp_creditCheck_esggtype, createCntrNo_brmp_creditCheck_esggtypeZMemo,createCntrNo_brmp_creditCheck_houseLoanFlag,usePlan).done(function(json_prepare_gen_71_caseDoc_tabDoc){
                        //~~~~
                        var json_param = {'docKind':'1'  //授權別 1授權內
                            , 'docCode': '1'	//案件別 1一般
                            , 'authLvl': '1'	//授權等級{1:分行授權內 ; 3:營運中心授權內}
                            , 'ngFlag' : '0'	//協議案註記 0無
                            };
                        cfm_msg_checkIsStop(json_param).done(function(){
                            dfd_addL120m01a(json_param).done(function(caseDoc_param){
                               dfd_71_importLendCollateral(caseDoc_param, json_prepare_gen_71_caseDoc_tabDoc.c101_mainId).done(function(){
                                  dfd_71_setMainCust(caseDoc_param).done(function(json_71_setMainCust){
                                     dfd_71_importLendCollateral_custPos(caseDoc_param, json_prepare_gen_71_caseDoc_tabDoc.custPos_c101_mainId).done(function(){
                                         dfd_71_caseDoc_saveAll(caseDoc_param).done(function(){
                                                //產出 brmp 的l140m01a ===> 參考 dfd_71_gen_tabDoc (... )
                                                brmp_creditCheck_new(caseDoc_param.mainId, json_71_setMainCust.c120m01a_oid , _form ).done(function(json_71_gen_tabDoc){
                                                    brmp_upd_caseDoc___tabDoc_cntrNo_npl(caseDoc_param.mainId, json_71_gen_tabDoc.tabMainId).done(function(){

                                                       dfd_import_simplifyFlag_D___L120S15A_content(caseDoc_param.mainId).done(function(){
                                                          dfd_import_property7_cntrNo(caseDoc_param.mainId, json_71_setMainCust.c120m01a_oid , "N" ).done(function(){
                                                             dfd_fast_checkSend_CLS_L120M01A_countValue(caseDoc_param.mainId).done(function(){
                                                                dfd_fastCntrNo_CLS_L120M01A_sendAML(caseDoc_param.mainId).done(function(json_sendAML){
                                                                   dfd_fastCntrNo_CLS_L120M01A_getAML(caseDoc_param.mainId, json_sendAML).done(function(){
                                                                     dfd_unlockDoc(caseDoc_param.mainId).done(function(){
                                                                     //=======================
                                                                     $.thickbox.close();
                                                                     //=======================
                                                                     //在 Grid 出現 新增的簽報書
                                                                     $("#gridview").trigger("reloadGrid");
                                                                     if(json_71_gen_tabDoc.rejectDesc && json_71_gen_tabDoc.rejectDesc!=''){
                                                                        API.showErrorMessage( json_71_gen_tabDoc.rejectDesc );
                                                                     }else{
                                                                        if(json_71_gen_tabDoc.statusMissing && json_71_gen_tabDoc.statusMissing!=''){
                                                                            API.showErrorMessage( json_71_gen_tabDoc.statusMissing );
                                                                        }
                                                                        //建立關聯
                                                                        else{
                                                                            $.ajax({
                                                                                type : "POST",
                                                                                handler : "cls1220m10formhandler",
                                                                                data : {
                                                                                formAction : "newC122S01H_flowId01",
                                                                                mainId: responseJSON.mainId,
                                                                                remainId: caseDoc_param.mainId,
                                                                                custId: $("createCntrNo_brmp_creditCheck_custId").val(),
                                                                                custName: $("#custName").val(),
                                                                                dupNo:  $("createCntrNo_brmp_creditCheck_dupNo").val(),
                                                                                flowId: "2"
                                                                                }
                                                                            }).done(function(responseData){
																				if(responseData.Success){
												//									window.close();
																					if(responseData.rpaError){
																						MegaApi.showErrorMessage(responseData.rpaError ,function(){
																							reLoad(responseData ,FastCreditLoans.json);
																							CommonAPI.triggerOpener("gridview", "reloadGrid");
																						});
																					}else{
																						reLoad(responseData ,FastCreditLoans.json);
//                                                                                            $("#gbox_docStatusGrid").trigger("reloadGrid");
																						CommonAPI.triggerOpener("gridview", "reloadGrid");
																					}
												//									reLoad(responseData ,json);
																				}else{
																					CommonAPI.showErrorMessage(responseData.Message);
																				}
																			});
                                                                        }
                                                                     }
                                                                    });
                                                                   });
                                                                });
                                                             });
                                                         });
                                                      });
                                                   });
                                                });
                                          });

                                     });
                                  });
                               });
                            });
                        });
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
	}
};
var IntroductionSource_PaperlessSigning = {

	show: function(introduceSrc){

		switch (introduceSrc) {
		    case '1':
		        $("#employeeDiv").show();
		        break;
		    case '3':
		        $("#megaSubCompanyDiv").show();
		        break;
		    case '5':
				$("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
		        break;
		    case '6':
		        $("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				break;
			case '9':
				$("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				break;
			case 'A':
				$("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				break;
		}
	},

	change: function(){

		$("#employeeDiv").hide();
		$("#megaSubCompanyDiv").hide();
		$("#customerOrCompanyDiv").hide();
		$("#introducerNameDiv").hide();

		//行員引介
		$("#employeeDiv").find("input:text").val("");
		//金控子公司員工引介
		$("#megaSubCompanyDiv").find("select").val("");
		$("#megaSubCompanyDiv").find("input:text").val("");
		//往來企金戶所屬員工, 本行客戶引介
		$("#customerOrCompanyDiv").find("input:text").val("");
		//引介人姓名
		$("#introducerNameDiv").find("input:text").val("");

		IntroductionSource_PaperlessSigning.show($("#createCntrNo_brmp_creditCheck_introduceSrc").val());
	},

	openBox: function(){

		var buttons = {};
		buttons[i18n.def.close] = function(){
			$.thickbox.close();
        };

       	$("#openBox_realEstateIntroduction").thickbox({
            title: i18n.cls1151s01['page01.title.introductionRealEstateAgentInfo'],
            width: 550,
            height: 250,
            modal: true,
			align: "center",
			valign: 'bottom',
            i18n: i18n.def,
            buttons: buttons
        });
	},

	importCustomerOrCompanyInfo: function(){

		AddCustAction.open({
    		handler: 'cls1151m01formhandler',
			action : 'importCustomerOrCompanyInfo',
			data : {
            },
			callback : function(json){
            	// 關掉 AddCustAction 的
            	$.thickbox.close();
				$("#introCustId").val(json.introCustId);
				$("#introDupNo").val(json.introDupNo);
				$("#introCustName").val(json.introCustName);
				var introduceSrc = $("#createCntrNo_brmp_creditCheck_introduceSrc").val();
				if(introduceSrc == '9' || introduceSrc == 'A'){
					$("#introducerName").val(json.introCustName)
				}
			}
		});
	},

	//帶入引介子公司分支代號
	loadMegaSubUnitNo: function(megaCode){

		var form = $("#createCntrNo_brmp_creditCheckForm");
		if (megaCode == '') {
			$("#subUnitNo").setItems({});
			$("#subEmpNo").val("");
			$("#subEmpNm").val("");
		}
		else {
			var key_sub_unitNo = ('LNF13E_SUB_UNITNO_' + megaCode);
			var item = CommonAPI.loadCombos(key_sub_unitNo);
			form.find("#subUnitNo").setItems({
				item: item[key_sub_unitNo],
				format: "{value} - {key}"
			});
		}
	},

	selectBranch: function(){
		var form = $("#createCntrNo_brmp_creditCheckForm");
		$.ajax({
            handler: "cls1220m10formhandler",
            action: "getAllOrgBrId"
        }).done(function(responseData){
			if(responseData.Success){ //成功
				form.find("#selectBranch").setItems({
					item: responseData.childMap,
					space: true,
					format: "{value} - {key}"
				});
			}
		})		.done(function(){
			$("#selectBranchDiv").thickbox({
  	       	 	title: i18n.cls1141v01['page01.title.selectBranch'], width: 350, height: 100, align: 'center', valign: 'bottom', modal: true,
				i18n: i18n.def,
  	            buttons: {
  	                "sure": function(){
  	                	var branchStr = $("#selectBranch option:selected").text();
						var branchArray = branchStr.split("-");
						$("#megaEmpBrNo").val($.trim(branchArray[0]));
						$("#megaEmpBrNoName").val($.trim(branchArray[1]));
						$.thickbox.close();
  	                },
  	                "cancel": function(){
  	               		$.thickbox.close();
  	                }
  	            }
  	        });
		});
	}
}
function openFile(cellvalue, options, rowObject){
	// 下載&開啟檔案
	// 文件數位化檔案直接開啟系統畫面顯示
	if("MEGAImage" == rowObject.fieldId) {
		getEloanQueryImageListUrl().done(function(json){
            if(json.url){
            	window.open(json.url, responseJSON.mainId);
            }
        });
	} else {
		recordQueryReason(function(){
			$.capFileDownload({
				handler:"simplefiledwnhandler",
				data : {
					fileOid:rowObject.oid
				}
			});
		});
	}
}

/**
 *  取得eLoan連動影像查詢URL
 * @returns
 */
function getEloanQueryImageListUrl(){
    return $.ajax({
        type : "POST", handler : "cls1220m01formhandler",
        data : {
           formAction : "getEloanQueryImageListUrl" , async: false,//用「同步」的方式
           mainId : responseJSON.mainId
        }
    }).done(function(obj){});
}

/**
 * eloan消金文件影像查詢匯入功能
 */
function eLoanUploadImageFile(json) {
	return $.ajax({
        handler: 'cls1220m01formhandler',
        action: 'eLoanUploadImageFile',
        data : {
            mainId: responseJSON.mainId,
            formId: json.formId || "",
            docFileOid: json.fileKey || "",
            stakeholderID: json.stakeholderID || ""
        }
    }).done(function(r){});
}

function reLoad(responseData, rowObject){

	$.form.submit({
	    url: location.href,
	    target: $("#mainOid").val(),
	    data: {
	        oid: rowObject.oid,
	        mainId: rowObject.mainId,
	        mainOid: rowObject.mainOid,
	        mainDocStatus: responseData.docStatus,
	        docStatus: responseData.docStatus
	    }
	});
}




function recordQueryReason(action) {
	if ("Y" == $("#queryReasonIsRecorded").val()) {
		if (action) {
			action();
		}
	} else {
		var setting = new Object();
		setting.id = "queryReasonDialog";
		setting.reasonCodeType = "C122S01E_queryReason";
		var queryReasonDailog = CommonAPI.queryReasonDialog(setting, function() {
			var queryReason = queryReasonDailog.find("#sseidQueryReason").val();
	        if (!queryReason) {
	            //id_reasonMsg=請先輸入查詢理由!
	            CommonAPI.showMessage(i18n.def['id_reasonMsg']);
				return;
	        }
			// J-110-0395調閱資料須加註查詢理由
			$.ajax({
	            handler: 'cls1220m01formhandler',
	            action: 'recordQueryReason',
	            data : {
	                mainId: responseJSON.mainId,
					queryReason: queryReason
		        }
	        }).done(function(r){
				$("#queryReasonIsRecorded").val("Y");
			});
			if (action) {
				action();
			}
		});
	}
}

function prepare_gen_71_brmp_termGroupRule(custId, dupNo, termGroupObj, termGroupSubObj, termGroupRuleResultTextObj){
	var my_dfd = $.Deferred();
	termGroupObj.val("");
    termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
	if(custId != "" && dupNo != ""){
		$.ajax({
	        type: "POST",
	        handler: "cls1141m01formhandler",
	        data: {formAction: "show_last_brmp_termGroupRule", "custId":custId, "dupNo":dupNo}
	    }).done(function(jsonparm){
			//console.log(jsonparm);
			var resultmsg = "";
			if(jsonparm.hasbrmp004){//有結果
				if(jsonparm.brmp004data.result.termGroup != null && jsonparm.brmp004data.result.termGroup != ""){//最終客群分類 "X":不承作 , "S":小額 , "N":普惠 , "G":優質, "E":行員
					termGroupObj.val(jsonparm.brmp004data.result.termGroup);
					resultmsg += termGroupObj.find(":selected").text();
					if(jsonparm.brmp004data.result.applyDBRType != null){
						termGroupSubObj.val(jsonparm.brmp004data.result.applyDBRType);
						if(jsonparm.brmp004data.result.applyDBRType == "A"){
							resultmsg += " (DBR上限15倍)";
						}
					}
					termGroupRuleResultTextObj.text(resultmsg);
				}else{
					termGroupObj.val("");
					termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
					CommonAPI.showMessage(i18n.cls1141v01['L140S02A.chkTermGroup']);
				}
			}else if(jsonparm.isBankMan){
				termGroupObj.val("E");
				termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
			}else{
				termGroupObj.val("");
				termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
				CommonAPI.showMessage(i18n.cls1141v01['L140S02A.chkTermGroup']);
			}
		});
	}
	return my_dfd.promise();
}
$("input[type='radio'][name='createCntrNo_brmp_creditCheck_creditLoanPurpose']").change(function(){
	if($(this).val()=="G"){
		$("#div_createCntrNo_brmp_creditCheck_induceFlagOrNot").show();
	}else{
		$("#createCntrNo_brmp_creditCheck_induceFlagOrNot").prop("checked", false).trigger("change");
		$("#div_createCntrNo_brmp_creditCheck_induceFlagOrNot").hide();
	}
});

function cfm_msg_checkIsStop(json_param){
	var my_dfd = $.Deferred();

	$.ajax({
        type : "POST",
        handler : "cls1220m10formhandler",
        data : {
        formAction : "newC122S01H_flowId01",
        mainId: responseJSON.mainId,
        remainId: "",
        custId: $("createCntrNo_brmp_creditCheck_custId").val(),
        custName: $("#custName").val(),
        dupNo:  $("createCntrNo_brmp_creditCheck_dupNo").val(),
        flowId: "2",
        checkOnly: "true"
        }
    }).done(function(responseData){
		if(responseData.Success){
//									window.close();
			if(responseData.rpaError){
				MegaApi.showErrorMessage(responseData.rpaError ,function(){
					reLoad(responseData ,FastCreditLoans.json);
					CommonAPI.triggerOpener("gridview", "reloadGrid");
				});
			}else{
				//reLoad(responseData ,FastCreditLoans.json);
				CommonAPI.triggerOpener("gridview", "reloadGrid");
			}
			my_dfd.resolve();
//									reLoad(responseData ,json);
		}else{
			CommonAPI.showErrorMessage(responseData.Message);
			my_dfd.reject();
		}
	});

	return my_dfd.promise();
}
function dfd_addL120m01a(json_param){
	var my_dfd = $.Deferred();
	//==============================================================
	// 因為 form 裡沒有 ID, 所以用  find("[name=?]").val(?); 去塞資料
	// $("#LMS1205V01Form").injectData(json_param);
	$.each(json_param, function(k, v) {
		$("#LMS1205V01Form").find("[name="+k+"][value="+v+"]").trigger('click').prop("checked", true);;
    });
	//==============================================================
	$.ajax({
        handler: "cls1141m01formhandler", action: "addL120m01a",
        formId: "LMS1205V01Form"
    }).done(function(json_resp_addL120m01a){

		var caseDoc_param = {};
		caseDoc_param["mainId"] = json_resp_addL120m01a.mainId;
		caseDoc_param["oid"] = json_resp_addL120m01a.mainOid;
		caseDoc_param["mainOid"]= caseDoc_param["oid"];
		caseDoc_param["docStatus"] = json_resp_addL120m01a.docStatus;
		caseDoc_param["mainDocStatus"] = caseDoc_param["docStatus"];

	 ilog.debug("json_resp_addL120m01a"
			 +"{mainDocStatus="+caseDoc_param.docStatus
			 +",oid="+caseDoc_param.oid
			 +",mainId="+caseDoc_param.mainId
			 +" }");
	 my_dfd.resolve(caseDoc_param);
});
	return my_dfd.promise();
}
function dfd_71_importLendCollateral(caseDoc_param, c101_mainId){
	return  $.ajax({handler: "cls1141formhandler", action: "importLendCollateral", formId: 'empty',
        	data:$.extend(caseDoc_param, { 'rows': c101_mainId}
        )
    }).done(function(json_resp_importLendCollateral){
		ilog.debug("{done}json_resp_importLendCollateral");
});
}
function dfd_71_setMainCust(caseDoc_param){
	var my_dfd = $.Deferred();
	$.ajax({handler: "cls1141formhandler", action: "setMainCust", formId: 'empty', data:$.extend(caseDoc_param, {'custOid': ''} // c120m01a.oid
    	)
    }).done(function(json_resp_setMainCust){
		ilog.debug("{done}json_resp_setMainCust");
		if(json_resp_setMainCust.chose_c120m01a){
			my_dfd.resolve({'c120m01a_oid':(json_resp_setMainCust.chose_c120m01a.c120m01a_oid||'') });
		}else{
			my_dfd.resolve({'c120m01a_oid':''});
		}

	});
	return my_dfd.promise();
}

function dfd_71_importLendCollateral_custPos(caseDoc_param, custPos_c101_mainId){
	var my_dfd = $.Deferred()
	if(custPos_c101_mainId && custPos_c101_mainId != ""){
		$.ajax({handler: "cls1141formhandler", action: "importLendCollateral", formId: 'empty',
        	data:$.extend(caseDoc_param, { 'rows': custPos_c101_mainId}
        )
		}).done(function(json_resp_importLendCollateral){
			ilog.debug("{done}json_resp_importLendCollateral_custPos{custPos_c101_mainId='"+custPos_c101_mainId+"'}");
			my_dfd.resolve();
	});
	}else{
		ilog.debug("importLendCollateral_custPos{custPos_c101_mainId=''}");
		my_dfd.resolve();
	}
	return my_dfd.promise();
}
function dfd_71_caseDoc_saveAll(caseDoc_param){
  	 return $.ajax({handler: "cls1141m01formhandler", action: "saveAll", formId: 'empty', data:$.extend(caseDoc_param, { 'page': '2'}
           )
     }).done(function(json_resp_saveAll){
		ilog.debug("{done}json_resp_saveAll");
	});
}
function brmp_creditCheck_new(caseMainId, debtor_createCntrNo_brmp_creditCheck, param_formId){
	var my_dfd = $.Deferred();
	var frmJSON = $("#"+param_formId).serializeData();
	$.ajax({
        handler: "cls1141m01formhandler",
		formId: 'empty',
        action: "brmp_creditCheck_new",
        data: {'caseMainId': caseMainId
        	, 'custId': frmJSON["createCntrNo_brmp_creditCheck_custId"]
        	, 'dupNo': frmJSON["createCntrNo_brmp_creditCheck_dupNo"]
        	, 'custPos_custId': frmJSON["createCntrNo_brmp_creditCheck_custPos_custId"]
        	, 'custPos_dupNo': frmJSON["createCntrNo_brmp_creditCheck_custPos_dupNo"]
          	, 'custPos_rKindM': frmJSON["createCntrNo_brmp_creditCheck_custPos_rKindM"]
          	, 'custPos_rKindD': frmJSON["createCntrNo_brmp_creditCheck_custPos_rKindD"]
          	, 'custPos_isLiveWithBorrower': frmJSON["createCntrNo_brmp_creditCheck_custPos_isLiveWithBorrower"]
			, 'applyAmt': frmJSON["createCntrNo_brmp_creditCheck_amt"]
    		, 'lnYear': frmJSON["createCntrNo_brmp_creditCheck_lnYear"]
    		, 'lnMonth': frmJSON["createCntrNo_brmp_creditCheck_lnMonth"]
			, 'pConBegEnd_fg': frmJSON["createCntrNo_brmp_creditCheck_pConBegEnd_fg"]
			, 'onlineCaseNo': frmJSON["createCntrNo_brmp_creditCheck_onlineCaseNo"]
			, 'introduceSrc': frmJSON["createCntrNo_brmp_creditCheck_introduceSrc"]
			, 'megaEmpNo': frmJSON["megaEmpNo"]
			, 'megaCode': frmJSON["megaCode"]
			, 'subUnitNo': frmJSON["subUnitNo"] || ''
			, 'subEmpNo': frmJSON["subEmpNo"]
			, 'subEmpNm': frmJSON["subEmpNm"]
			, 'introCustId': frmJSON["introCustId"]
			, 'introDupNo': frmJSON["introDupNo"]
			, 'introCustName': frmJSON["introCustName"]
			, 'creditLoanPurpose': frmJSON["createCntrNo_brmp_creditCheck_creditLoanPurpose"]
			, 'induceFlagOrNot': frmJSON["createCntrNo_brmp_creditCheck_induceFlagOrNot"]
			, 'termGroup': frmJSON["createCntrNo_brmp_creditCheck_termGroup"]
			, 'termGroupSub': frmJSON["createCntrNo_brmp_creditCheck_termGroupSub"]
			, 'coBrandedCardUserType01': frmJSON["createCntrNo_brmp_creditCheck_coBrandedCardUserType01"]
//            , 'lnap': frmJSON["createCntrNo_brmp_creditCheck_lnap"]
            , 'stageCount': frmJSON["createCntrNo_brmp_creditCheck_stageCount"]
//          , 'stageCount': $frm.find("#createCntrNo_brmp_creditCheck_stageCount").val() 此寫法因 radio 的 id, name 都相同，會抓錯
    		, 'otherAdjustAmt': frmJSON["createCntrNo_brmp_creditCheck_otherAdjustAmt"]
    		, 'compensationAmt': frmJSON["createCntrNo_brmp_creditCheck_compensationAmt"]
			, 'induceFlagXV': frmJSON["createCntrNo_brmp_creditCheck_induceFlagXV"]
			, 'esggnLoanFg': frmJSON["createCntrNo_brmp_creditCheck_esggnLoanFg"]
			, 'esggtype': frmJSON["createCntrNo_brmp_creditCheck_esggtype"]
			, 'esggtypeZMemo': frmJSON["createCntrNo_brmp_creditCheck_esggtypeZMemo"]
			, 'houseLoanFlag': frmJSON["createCntrNo_brmp_creditCheck_houseLoanFlag"]
			, 'megaEmpBrNo': frmJSON["megaEmpBrNo"]
			, 'introducerName': frmJSON["introducerName"]
			, 'usePlan': frmJSON["usePlan"]
        }
    }).done(function(obj){
		ilog.debug("brmp_creditCheck_new {tabMainId="+(obj.l140m01a_MainId||'')+",rejectDesc="+(obj.rejectDesc||'')+",statusMissing="+(obj.statusMissing||'')+"}");
		if(obj.l140m01a_MainId && obj.l140m01a_MainId != ""){
			my_dfd.resolve({'tabMainId':(obj.l140m01a_MainId||'')
				, 'rejectDesc': (obj.rejectDesc||'')
				, 'statusMissing': (obj.statusMissing||'')
				});
			/*  之後的 CLS1141S03Action.getContractNo(...) , CLS1141S03Action.getBranchNplRatiosInfo(...) , updateCreditCardMembersLoanData(...)
				 在 dfd_71_upd_caseDoc___tabDoc_cntrNo_npl(...) 去做
			*/
		}else{
			ilog.debug("brmp_creditCheck_new {no} l140m01a_MainId");
			//若無報價
			my_dfd.reject();
			//=======================
			$.thickbox.close();
			$("#gridview").trigger("reloadGrid");
		}
	}).fail(function(obj){
		ilog.debug("brmp_creditCheck_new {fail}");
		//若 BRMP 執行失敗，讓  user 也能看到已產出的簽報書，免得一直 click
		my_dfd.reject();
		//=======================
		$.thickbox.close();
		$("#gridview").trigger("reloadGrid");
	});


	return my_dfd.promise();
}
function brmp_upd_caseDoc___tabDoc_cntrNo_npl(caseMainId, tabMainId){
	//之後在 L120M01A 會增加 simplifyFlag
	return $.ajax({handler: "cls1141m01formhandler", action: "upd_caseDoc___tabDoc_cntrNo_npl", formId: 'empty',
		 data:{'caseMainId': caseMainId, 'simplifyFlag' : ''
			 , 'tabMainId': tabMainId, 'tab_printSeq' : 1
		 }
	}).done(function(json){
		ilog.debug("{done}json_resp_upd_caseDoc___tabDoc_cntrNo_npl , {cntrNo="+(json.cntrNo||'')+"}");
	});
}
function dfd_import_simplifyFlag_D___L120S15A_content(caseMainId){
	return $.ajax({handler: "cls1141m01formhandler", action: "import_simplifyFlag_D___L120S15A_content", formId: 'empty',
		 data:{'mainId': caseMainId
		 }
	}).done(function(json){
		ilog.debug("{done}import_simplifyFlag_D___L120S15A_content");
	});
}
function dfd_import_property7_cntrNo(caseMainId, custOid, importBisFlag ){
	var my_dfd = $.Deferred();
	$.ajax({handler: "cls1151m01formhandler", action: "addL140m01a", formId: 'empty',
		 data:{'mainId': caseMainId, 'custOid': custOid
		 }
	}).done(function(json_addL140m01a){
		ilog.debug("{done}dfd_import_property7_cntrNo___{addL140m01a}");
		//J-111-0096 Web e-Loan消金因應不動產暴險以貸放比率(LTV)決定適用之風險權數，消金個人戶授信案件免填「風險權數」(取消現行系統要求風險權數影響數評估)
		if(importBisFlag =="Y"){
			$.ajax({handler: "cls1141m01formhandler", action: "getBis", formId: 'empty',
				data:{'mainId': caseMainId
					, 'refresh' : false
					, 'trigger_in_dfd_chain' : true
				}
			}).done(function(json_getBis){
				//因在 server 端寫 result.set(CapConstants.AJAX_NOTIFY_MESSAGE, ...)
				$.thickbox.close();
				ilog.debug("{done}dfd_import_property7_cntrNo___{getBis}");
				//~~~
				my_dfd.resolve();
			});
	   }else{
		   my_dfd.resolve();
	   }
	});
	return my_dfd.promise();
}
function dfd_fast_checkSend_CLS_L120M01A_countValue(caseMainId){
	var my_dfd = $.Deferred();
	$.ajax({handler: "cls1141m01formhandler", action: "fastCntrNo_checkSend_CLS_L120M01A_countValue", formId: 'empty',
		 data:{'mainId': caseMainId
		 }
	}).done(function(json){
		ilog.debug("{done_dfd_fast_checkSend_CLS_L120M01A_countValue}");
		if(json.msg){
			ilog.debug("msg="+json.msg);
		}
		my_dfd.resolve();
	});
	return my_dfd.promise();
}

function dfd_fastCntrNo_CLS_L120M01A_sendAML(caseMainId){
	var my_dfd = $.Deferred();
	$.ajax({handler: "cls1141m01formhandler", action: "fastCntrNo_CLS_L120M01A_sendAML", formId: 'empty',
		 data:{'mainId': caseMainId
		 }
	}).done(function(json){
		ilog.debug("{done_dfd_fastCntrNo_CLS_L120M01A_sendAML}=>need_getAML="+(json.need_getAML||""));
		if(json.msg){
			ilog.debug("msg="+json.msg);
		}
		my_dfd.resolve(json);
	});
	return my_dfd.promise();
}

function dfd_fastCntrNo_CLS_L120M01A_getAML(caseMainId, json_sendAML){
	var my_dfd = $.Deferred();
	if(json_sendAML.need_getAML=="Y"){
		$.ajax({handler: "cls1141m01formhandler", action: "fastCntrNo_CLS_L120M01A_getAML", formId: 'empty',
			 data:{'mainId': caseMainId
			 }
		}).done(function(json){
			ilog.debug("{done_dfd_fastCntrNo_CLS_L120M01A_getAML}");
			if(json.msg){
				ilog.debug("msg="+json.msg);
			}
			my_dfd.resolve();
		});
	}else{
		my_dfd.resolve();
	}
	return my_dfd.promise();
}

function dfd_unlockDoc(lockMainId){
	//需有 AuthType.Accept  return $.ajax({handler: 'unlockdocformhandler', action: "unlockDoc", formId: 'empty',
	return $.ajax({handler: 'cls1141m01formhandler', action: "unlockDoc_aft_genL120M01A", formId: 'empty',
        data: { 'lockMainId': lockMainId
        }
    }).done(function(responseData){
		ilog.debug("{done}dfd_unlockDoc");
	});
}
$("input[type='radio'][name='createCntrNo_brmp_creditCheck_esggnLoanFg']").change(function(){
	if($(this).val()=="Y"){
		$("#tr_EsgGtype_brmp").show();
	}else if($(this).val()=="N"){
		$("#createCntrNo_brmp_creditCheckForm").injectData({'createCntrNo_brmp_creditCheck_esggtype':'', 'createCntrNo_brmp_creditCheck_esggtypeZMemo':''});
		$("#tr_EsgGtype_brmp").hide();
	}
});

$("#createCntrNo_brmp_creditCheck_induceFlagOrNot").change(function() {
	if($(this).is(':checked')){
		$("#createCntrNo_brmp_creditCheckForm").injectData({'createCntrNo_brmp_creditCheck_induceFlagXV':'V'});
        $("#tr_createCntrNo_brmp_creditCheck_induceFlagXV").show();
	}else{
		$("#tr_createCntrNo_brmp_creditCheck_induceFlagXV").hide();
	}
});

$("#createCntrNo_brmp_creditCheck_esggtype").change(function(){
	if($(this).val()=="Z"){
		$("#div_createCntrNo_brmp_creditCheck_esggtypeZ").show();
	}else{
		$("#div_createCntrNo_brmp_creditCheck_esggtypeZ").find(':input').not(':button, :submit, :reset, :hidden').not(':checkbox, :radio').val('');
		$("#div_createCntrNo_brmp_creditCheck_esggtypeZ").hide();
	}
});
function prepare_gen_71_brmp_sum_compensationAmt(custId,dupNo){
	var my_dfd = $.Deferred(); //為了檢核 決策平台 連 EJCIC狀況，在 server 端就先 run API：headAccountValidation
	$.ajax({
        type: "POST",
        handler: "cls1141m01formhandler",
        data: {formAction: "prepare_gen_71_brmp_sum_compensationAmt", "custId":custId, "dupNo":dupNo}
    }).done(function(json){
		if(json.compensationAmt>0){
			$("#createCntrNo_brmp_creditCheck_compensationAmt").val(json.compensationAmt);
		}
	});
	return my_dfd.promise();
}
function prepare_gen_71_brmp_caseDoc_tabDoc(custId, dupNo, termGroup, creditLoanPurpose, onlineCaseNo, custPos_custId, custPos_dupNo, custPos_rKindM, custPos_rKindD, lnYear, lnMonth
		, esggnLoanFg
		, esggtype, esggtypeZMemo, houseLoanFlag, usePlan){
	var my_dfd = $.Deferred(); //為了檢核 決策平台 連 EJCIC狀況，在 server 端就先 run API：headAccountValidation
	$.ajax({
        type: "POST",
        handler: "cls1141m01formhandler",
        data: {formAction: "prepare_gen_71_brmp_caseDoc_tabDoc", "custId":custId, "dupNo":dupNo
        	, 'termGroup': termGroup, 'creditLoanPurpose':creditLoanPurpose, 'onlineCaseNo': onlineCaseNo
        	, "custPos_custId": custPos_custId, "custPos_dupNo":custPos_dupNo
        	, "custPos_rKindM": custPos_rKindM, "custPos_rKindD":custPos_rKindD
        	, "lnYear":lnYear, "lnMonth":lnMonth
        	, "esggnLoanFg":esggnLoanFg
        	, "esggtype":esggtype, "esggtypeZMemo":esggtypeZMemo,"houseLoanFlag":houseLoanFlag,"usePlan":usePlan, "simplifyFlag":"E"
        	}
    }).done(function(json){
		my_dfd.resolve( json );
	});
	return my_dfd.promise();
}
