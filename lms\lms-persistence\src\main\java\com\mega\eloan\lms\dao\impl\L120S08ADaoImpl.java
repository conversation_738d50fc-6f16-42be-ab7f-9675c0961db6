/* 
 * L120S08ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S08ADao;
import com.mega.eloan.lms.model.L120S01P;
import com.mega.eloan.lms.model.L120S08A;
import com.mega.eloan.lms.model.L140M01A;

/** 利率定價核理性分析表主檔 **/
@Repository
public class L120S08ADaoImpl extends LMSJpaDao<L120S08A, String> implements
		L120S08ADao {

	@Override
	public L120S08A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S08A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S08A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S08A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120S08A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S08A> findByMainIdCurr(String mainId, String curr) {
		ISearch search = createSearchTemplete();
		List<L120S08A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (curr != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "curr", curr);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S08A> findL120s08aListByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		return find(search);
	}

	@Override
	public List<L120S08A> findByMainIdCurrCustIdPrintGroup(String mainId,
			String curr, String custId, String dupNo, BigDecimal printGroup) {
		ISearch search = createSearchTemplete();
		List<L120S08A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (curr != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "curr", curr);

		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);

		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		if (printGroup != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "printGroup",
					printGroup);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public L120S08A findMaxSeqNoByMainIdAndCurr(String mainId, String curr) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "curr", curr);
		search.addOrderBy("seqNo", true);

		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S08A> findByMainIdAndVersionDate(String mainId,
			String versionDate) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "versionDate",
				versionDate);
		List<L120S08A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S08A> findByMainIdCustIdPrintGroup(String mainId,
			String custId, String dupNo, BigDecimal printGroup) {
		ISearch search = createSearchTemplete();
		List<L120S08A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);

		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		if (printGroup != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "printGroup",
					printGroup);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public L120S08A findByMainIdCustIdPrintGroupCurr(String mainId,
			String custId, String dupNo, BigDecimal printGroup, String curr){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "printGroup",	printGroup);
		search.addSearchModeParameters(SearchMode.EQUALS, "curr", curr);

		return findUniqueOrNone(search);
	}

}