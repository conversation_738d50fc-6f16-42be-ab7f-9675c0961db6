---------------------------------------------------------
-- LMS.LMSBATCH 授信批次報表檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.LMSBATCH;
CREATE TABLE LMS.LMSBATCH (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	<PERSON>ANCH        CHAR(3)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>      DATE         ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>       DATE         ,
	<PERSON>ND<PERSON>TE       DATE         ,
	RP<PERSON><PERSON>O         VARCHAR(10)  ,
	R<PERSON><PERSON><PERSON><PERSON>       VARCHAR(120) ,
	NOWRPT        CHAR(01)     ,
	<PERSON><PERSON><PERSON><PERSON>       TIMESTAMP    ,
	RANDOMCODE    VARCHAR(32)  ,
	REMARKS       varchar(200) ,
	UPDATER       VARCHAR(6)   ,
	UPDATETIME    TIMESTAMP    ,
	constraint P_LMSBATCH PRIMARY KEY(OID)
) IN EL_DATA_4KTS INDEX IN EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XLMSBATCH01;
CREATE INDEX LMS.XLMSBATCH01 ON LMS.LMSBATCH   (BRANCH, ENDDATE, RPTNO, NOWRPT);
--DROP INDEX LMS.XLMSBATCH02;
CREATE INDEX LMS.XLMSBATCH02 ON LMS.LMSBATCH   (BRANCH, BGNDATE, ENDDATE, RPTNO, NOWRPT);
--DROP INDEX LMS.XLMSBATCH03;
CREATE INDEX LMS.XLMSBATCH03 ON LMS.LMSBATCH   (MAINID);
--DROP INDEX LMS.XLMSBATCH04;
CREATE INDEX LMS.XLMSBATCH04 ON LMS.LMSBATCH   (MAINID, RPTNO, DATADATE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.LMSBATCH IS '授信批次報表檔';
COMMENT ON LMS.LMSBATCH (
	OID           IS 'oid', 
	MAINID        IS 'mainId', 
	BRANCH        IS '分行代碼', 
	DATADATE      IS '資料日期', 
	BGNDATE       IS '起日', 
	ENDDATE       IS '迄日', 
	RPTNO         IS '報表代號', 
	RPTNAME       IS '報表名稱', 
	NOWRPT        IS '是否為最新報表', 
	BTHDATE       IS '批次時間', 
	RANDOMCODE    IS '報表亂碼', 
	REMARKS       IS '備註或其他需使用的值', 
	UPDATER       IS '資料修改人(行編)', 
	UPDATETIME    IS '資料修改日期'
);
