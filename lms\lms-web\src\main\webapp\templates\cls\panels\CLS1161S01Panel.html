<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<script type="text/javascript">loadScript('pagejs/cls/CLS1161S01Panel');</script>
		<div id="C160M01ADiv" class="content">
			<form id="C160M01AForm" name="C160M01AForm" >
				<fieldset>
					<legend><b><th:block th:text="#{'title.baseInfo'}">基本資訊</th:block></b></legend>
					<table class="tb2" width="100%">
						<tr>
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M01A.ownBrId'}">編製單位代號</th:block>&nbsp;&nbsp;</td>
							<td width="35%">
								<span id="ownBrId" class="field" ></span>&nbsp;<span id="ownBrIdName" class="field" ></span>
							</td>
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M01A.caseNo'}">案件號碼</th:block>&nbsp;&nbsp;</td>
							<td width="35%"><span id="caseNo" class="field" ></span>&nbsp;</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.docStatus'}">目前文件狀態</th:block>&nbsp;&nbsp;</td>
							<td><b class="text-red"><span id="docStatus" class="field" ></span>&nbsp;</b></td>
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.caseDate'}">簽案日期</th:block>&nbsp;&nbsp;</td>
							<td><span id="caseDate" class="field" ></span>&nbsp;</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.custId'}">統一編號</th:block>&nbsp;&nbsp;</td>
							<td><span id="custId" class="field" ></span>&nbsp;<span id="dupNo" class="field" ></span></td>
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.caseLvl'}">案件審核層級</th:block>&nbsp;&nbsp;</td>
							<td><span id="caseLvl" class="field" ></span>&nbsp;<span id="caseLvlCn" class="field" ></span></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.cntrNo'}">額度序號</th:block>&nbsp;&nbsp;</td>
							<td colspan="3" >
								<th:block th:if="${_btCaseType1_visible}">
									<button type="button" id="btCaseType1" >
										<span class="text-only"><th:block th:text="#{'button.btCaseType1'}">選擇額度明細表</th:block></span>
									</button>
								</th:block>
								<th:block th:if="${_btCaseType2_visible}">
									<button type="button" id="btCaseType2" >
										<span class="text-only"><th:block th:text="#{'button.btCaseType2'}">選擇額度明細表(團貸用)</th:block></span>
									</button>
								</th:block>
								<th:block th:if="${_btCaseType3_visible}">
									<button type="button" id="btCaseType3" >
										<span class="text-only"><th:block th:text="#{'button.btCaseType3'}">匯入EXCEL</th:block></span>
									</button>
								</th:block>
								<br/>
								<span id="cntrNo" class="field" ></span>
							</td>
						</tr>
						<th:block th:if="${_groupLoanInfo_visible}">
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'C160M01A.groupLoanInfo'}">團貸總戶資訊</th:block>&nbsp;&nbsp;</td>
								<td>

									<table width="100%" border="0" cellpadding="0" cellspacing="0" >											
											<span id="parentYear" class="field" style="display:none"></span> <!-- "C160M01A.parentYear" 團貸資訊-總額度申請年度 -->
											<span id="loanPackNo" class="field" style="display:none"></span> <!-- "C160M01A.loanPackNo" 團貸資訊-批號 -->
										<tr>
											<td>															
												<th:block th:text="#{'C160M01A.A08'}">專案名稱</th:block>
											</td>
											<td colspan="3">	
												<span id="buildName" class="field"></span>
											</td>
										</tr>
										<tr>
											<td>
												<th:block th:text="#{'C160M01A.loanMasterNo'}">總戶序號</th:block>													
											</td>
											<td>
												<span id="loanMasterNo" class="field"></span>
											</td>
											<td  nowrap>
												<th:block th:text="#{'C160M01A.issueBrNo'}">簽案行</th:block>
											</td>
											<td>
												<span id="issueBrNo" class="field"></span>												
											</td>												
										</tr>
										
										
										
										
										
										
										
										
									</table>
								</td>
								<td class="hd2" align="right">
									<th:block th:text="#{'C160M01A.packNo'}">批號</th:block>&nbsp;&nbsp;<br/>
									<button type="button" id="btPackNo" >
										<span class="text-only"><th:block th:text="#{'button.btGetNumber'}">給號</th:block></span>
									</button>
								</td>
								<td><span id="packNo" class="field" ></span>&nbsp;</td>
							</tr>
						</th:block>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.mainApprId'}">總行經辦</th:block>&nbsp;&nbsp;</td>
							<td><span id="mainApprId" class="field" ></span>&nbsp;</td>
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.mainManagerId'}">總行覆核主管</th:block>&nbsp;&nbsp;</td>
							<td><span id="mainManagerId" class="field" ></span>&nbsp;</td>
						</tr>
					</table>
					<div id="displayCntrNo" ></div>
					<th:block th:if="${_note_visible}"><button type="button" id="btnNote" >
							<span class="text-only">經辦照會</span>
						</button>
						<br>
						<span>照會經辦：<input type="text" name="noter" id="noter" readonly="readonly"/></span>
						<br>
						<span>照會時間：<input type="text" name="noteTime" id="noteTime" readonly="readonly"/></span>
					</th:block>
				</fieldset>
				<fieldset>
					<legend><b><th:block th:text="#{'title.docLog'}">文件異動記錄</th:block></b></legend>
					<div th:include="common/panels/DocLogPanel :: DocLogPanel"></div>
					<table class="tb2" width="100%">
						<tr>
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M01A.creator'}">建立人員號碼</th:block>&nbsp;&nbsp;</td>
							<td width="35%"><span id="creator" class="field" ></span>(<span id="createTime" class="field" ></span>)</td>
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M01A.updater'}">異動人員號碼</th:block>&nbsp;&nbsp;</td>
							<td width="35%"><span id="updater" class="field" ></span>(<span id="updateTime" class="field" ></span>)</td>
						</tr>
						<tr>
							<td class="hd2" align="right">&nbsp;</td>
							<td>&nbsp;</td>
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.randomCode'}">文件亂碼</th:block>&nbsp;&nbsp;</td>
							<td><span id="randomCode" class="field" ></span>&nbsp;</td>
						</tr>
					</table>
				</fieldset>
				<fieldset>
					<legend><b><th:block th:text="#{'title.businessUnits'}">營業單位</th:block></b></legend>
					<table width="100%">
						<tr>
							<td width="12%" align="right"><b class="text-red"><th:block th:text="#{'C160M01A.managerId'}">經副襄理</th:block>：&nbsp;</b></td>
							<td width="12%"><span id="managerId" class="field" ></span>&nbsp;<span id="managerNm" class="field" ></span></td>
							<td width="12%" align="right"><b class="text-red"><th:block th:text="#{'C160M01A.bossId'}">授信主管</th:block>：&nbsp;</b></td>
							<td width="12%"><span id="bossId" class="field" ></span>&nbsp;</td>
							<td width="12%" align="right"><b class="text-red"><th:block th:text="#{'C160M01A.reCheckId'}">覆核主管</th:block>：&nbsp;</b></td>
							<td width="12%"><span id="reCheckId" class="field" ></span>&nbsp;<span id="reCheckNm" class="field" ></span></td>
							<td width="12%" align="right"><b class="text-red"><th:block th:text="#{'C160M01A.apprId'}">經辦</th:block>：&nbsp;</b></td>
							<td width="12%"><span id="apprId" class="field" ></span>&nbsp;<span id="apprNm" class="field" ></span></td>
						</tr>
					</table>
				</fieldset>
			</form>
		</div>
		
		<!-- 選擇額度明細表 -->
		<div id="CaseType1ThickBox" style="display:none;" >
			<form id="CaseType1Form" >
				<table class="tb2" width="100%">
					<tr>
						<td width="30%" class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160M01A.dataDrc'}">種類</th:block>&nbsp;&nbsp;</td>
						<td width="70%">
							<label style="letter-spacing:0px;cursor:pointer;"><input type="radio" id="dataSrc" name="dataSrc" class="required" value="1" checked="checked" /><th:block th:text="#{'C160M01A.dataDrc1'}">自行簽報之額度明細表</th:block></label><br/>
							<label style="letter-spacing:0px;cursor:pointer;"><input type="radio" id="dataSrc" name="dataSrc" class="required" value="2" /><th:block th:text="#{'C160M01A.dataDrc2'}">聯貸案簽報行傳來之額度明細表</th:block></label>
						</td>
					</tr>
					<tr>
						<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160M01A.custId'}">統一編號</th:block>&nbsp;&nbsp;</td>
						<td ><input id="queryId" name="queryId" class="required upText" maxlength="10" size="12"/>&nbsp;<input id="queryDupNo" name="queryDupNo" class="upText" maxlength="1" size="1"  /></td>
					</tr>
				</table>
			</form>
		</div>
		
		<!-- 選擇給號方式(團貸用-->
		 <div id="GetNumberBox" style="display:none">
	      	<form id="GetNumberForm" name="GetNumberForm">
	      	<table>
		      <label><b><th:block th:text="#{'C160M01A.messege1'}"><!-- 請選擇給號方式--></th:block>：</b></label>
		      <tbody>
		        <tr>
		          <td>
		          	<label><input type="radio" name="GetNumber" value="1" />
		            <th:block th:text="#{'button.btAutoGetNumber'}"><!--自動給號--></th:block></label>&nbsp;&nbsp;&nbsp;&nbsp;
		            <label><input type="radio" name="GetNumber" value="2" checked="checked"/>
		         	<th:block th:text="#{'button.btSetGetNumber'}"><!--手動輸入--></th:block></label>
					<input id="SetpackNo" name="SetpackNo" type="text" class="required" size="4" maxlength="4" minlength="4"/>
				  </td>
		        </tr>
		      </tbody>
		    </table>
			</form>
		  </div>
		
		<!-- 選擇額度明細表(團貸用) -->
		<div id="CaseType2ThickBox" style="display:none;" >
			<form id="CaseType2Form" >
				<table class="tb2" width="100%">
					<tr>
						<td width="40%" class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160M01A.groupID'}">團貸母戶客戶ID</th:block>&nbsp;&nbsp;</td>
						<td width="60%">
							<input id="groupId" name="groupId" class="required" maxlength="10" size="12" />&nbsp;
							<input id="groupDupNo" name="groupDupNo"  maxlength="1" size="1" />
						</td>
					</tr>
				</table>
			</form>
		</div>
		
		<!-- 匯入EXCEL -->
		<div id="CaseType3ThickBox" style="display:none;" >
			<form id="CaseType3Form">
				<th:block th:if="${_excelInfo_visible}">
					<table class="tb2" width="100%">
						<tr>
							<td width="30%" class="hd2" align="right">
								<button type="button" id="btUploadExcel" >
									<span class="text-only"><th:block th:text="#{'button.btUploadExcel'}">上傳Excel</th:block></span>
								</button>
							</td>
							<td width="70%">
								<input type="hidden" id="excelId" name="excelId" />
								<a href="#" id="downloadExcel" ><th:block th:text="#{'button.view'}">調閱</th:block></a>
								&nbsp;&nbsp;
								<span id="progressTr" class="text-red" style="display:none;" >
									<th:block th:text="#{'C160M01A.progress'}">處理進度</th:block>：
									<b><span id="progress" class="field" ></span></b>&nbsp;%
								</span>
								<div>
									<span class='text-red'><th:block th:text="#{'msg.excel_row_cnt_max'}">配合AML系統名單掃描，上傳 excel 之筆數，一次請勿超過300筆。</th:block></span>
								</div>
							</td>
						</tr>
						<!--
						<tr>
							<td width="30%" class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160M01A.fileName'}">檔案名稱</th:block>&nbsp;&nbsp;</td>
							<td width="70%"><input type="file" id="excelName" name="excelName" class="required" /></td>
						</tr>
						-->
						<tr>
							<td width="30%" class="hd2" align="right"><th:block th:text="#{'C160M01A.finCount'}">完成筆數</th:block>&nbsp;&nbsp;</td>
							<td width="70%"><span id="finCount" class="field" ></span>/<span id="totCount" class="field" ></span></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.custId1'}">匯入之機關團體統編</th:block>&nbsp;&nbsp;</td>
							<td><span id="custId" class="field" ></span>&nbsp;<span id="dupNo" class="field" ></span></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.custName1'}">匯入之機關團體名稱</th:block>&nbsp;&nbsp;</td>
							<td><span id="custName" class="field" ></span>&nbsp;</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160M01A.caseNo1'}">本案核准之編號</th:block>&nbsp;&nbsp;</td>
							<td>
								<span id="approvedNo" class="field" ></span>&nbsp;
								<button type="button" id="selectCaseNo" >
									<span class="text-only"><th:block th:text="#{'button.pullinAgain'}">重新引進</th:block></span>
								</button>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160M01A.isUseChildren'}">是否流用旗下子公司</th:block>&nbsp;&nbsp;</td>
							<td ><input type="radio" id="isUseChildren" name="isUseChildren" class="required" codeType="Common_YesNo" itemStyle="sort:desc" />&nbsp;</td>
						</tr>
						<tr class="children" >
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.childrenId'}">子公司統編</th:block>&nbsp;&nbsp;</td>
							<td >
								<input type="text" id="childrenId" name="childrenId" class="compNo max required" maxlength="8" />&nbsp;
								<button type="button" id="childrenPullinAgain" >
									<span class="text-only"><th:block th:text="#{'button.pullinAgain'}">重新引進</th:block></span>
								</button>
							</td>
						</tr>
						<tr class="children" >
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.childrenName'}">子公司名稱</th:block>&nbsp;&nbsp;</td>
							<td ><input type="text" id="childrenName" name="childrenName" class="max required" maxlength="120" />&nbsp;</td>
						</tr>
						<tr class="children" >
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.childrenSeq'}">流用序號</th:block>&nbsp;&nbsp;</td>
							<td ><span id="childrenSeq" class="field" ></span>&nbsp;</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C160M01A.packNo'}">批號</th:block>&nbsp;&nbsp;</td>
							<td >
								<span id="packNo" class="field" ></span>&nbsp;&nbsp;
								<button id="btPackNo" type="button" >
									<span class="text-only"><th:block th:text="#{'button.btGetNumber'}">給號</th:block></span>
								</button>
							</td>
						</tr>
					</table>
				</th:block>
			</form>
		</div>
		
		<!-- 案件簽報書 -->
		<div id="L120M01AThickBox" style="display:none;" >
			<div id="L120M01AGrid" ></div>
		</div>
		
		<!-- 團貸年度總額度檔-->
		<div id="PTEAMAPPThickBox" style="display:none;" >
			<div id="PTEAMAPPGrid" ></div>
		</div>
		
		<!-- 額度明細表 -->
		<div id="L140M01AThickBox" style="display:none;" >
			<!-- 未動用 -->
			<div class="isUse" >
				<b><th:block th:text="#{'L140M03A.isUseN'}">未動用</th:block></b>
				<div id="L140M01AGrid2" ></div>
				<br/>
			</div>
			<!-- 已動用 -->
			<span class="isUse" ><b><th:block th:text="#{'L140M03A.isUseY'}">已動用</th:block></b></span>
			<div id="L140M01AGrid" ></div>
		</div>

		
	</th:block>
</body>
</html>
