/* 
 * LMS2301ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L230A01ADao;
import com.mega.eloan.lms.dao.L230M01ADao;
import com.mega.eloan.lms.dao.L230S01ADao;
import com.mega.eloan.lms.dao.VL230M01Dao;
import com.mega.eloan.lms.lns.service.LMS2301Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L230A01A;
import com.mega.eloan.lms.model.L230M01A;
import com.mega.eloan.lms.model.L230S01A;
import com.mega.eloan.lms.model.VL230M01;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 簽約未動用授信案件送作業
 * </pre>
 * 
 * @since 2012/1/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/12,REX,new
 *          </ul>
 */
@Service
public class LMS2301ServiceImpl extends AbstractCapService implements
		LMS2301Service {
	private static Logger logger = LoggerFactory
			.getLogger(LMS2301ServiceImpl.class);
	@Resource
	L230M01ADao l230m01aDao;

	@Resource
	L230A01ADao l230a01aDao;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L230S01ADao l230s01aDao;

	@Resource
	VL230M01Dao VL230M01Dao;

	@Resource
	DocLogService docLogService;

	@Resource
	FlowService flowService;

	@Resource
	FlowNameService flowNameService;

	@Resource
	TempDataService tempDataService;

	@Resource
	LMSService lmsService;

	@Resource
	L140M01ADao l140m01aDao;

	/**
	 * 啟動flow
	 * 
	 * @param mainOid
	 */
	@Override
	public void startFlow(String mainOid) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		flowService.start("LMS2305Flow", mainOid, user.getUserId(),
				user.getUnitNo());
	}

	@Override
	public void flowAction(String mainOid, GenericBean model,
			List<L230S01A> l230s01as, boolean setResult, String action)
			throws Throwable {

		if (model instanceof L230M01A) {
			save((L230M01A) model);
		}
		l230s01aDao.save(l230s01as);
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS2305Flow", mainOid,
						user.getUserId(), user.getUnitNo());
			}

			if (setResult) {
				inst.setAttribute("result", action);

			}

			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}

	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L230M01A) {
					((L230M01A) model).setUpdater(user.getUserId());
					((L230M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());

					if (Util.isEmpty(((L230M01A) model).getOid())) {
						L230A01A l230a01a = new L230A01A();
						l230a01a.setAuthTime(CapDate.getCurrentTimestamp());
						l230a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
						l230a01a.setAuthUnit(user.getUnitNo());
						l230a01a.setMainId(((L230M01A) model).getMainId());
						l230a01a.setOwner(user.getUserId());
						l230a01a.setOwnUnit(user.getUnitNo());
						l230a01aDao.save(l230a01a);
						l230m01aDao.save((L230M01A) model);
						this.startFlow(((L230M01A) model).getOid());
					} else {
						l230m01aDao.save((L230M01A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((L230M01A) model)
									.getMainId());
							docLogService.record(((L230M01A) model).getOid(),
									DocLogEnum.SAVE);
						}

					}

				} else if (model instanceof L230S01A) {
					((L230S01A) model).setUpdater(user.getUserId());
					((L230S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l230s01aDao.save((L230S01A) model);
				}
			}
		}

	}

	@Override
	public void delete(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L230M01A) {
					((L230M01A) model).setDeletedTime(CapDate
							.getCurrentTimestamp());
					((L230M01A) model).setUpdater(user.getUserId());
					docLogService.record(((L230M01A) model).getOid(),
							DocLogEnum.DELETE);
					save(model);
				}
			}

		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L230M01A.class) {
			return l230m01aDao.findPage(search);
		} else if (clazz == L120M01A.class) {
			return l120m01aDao.findPage(search);
		} else if (clazz == VL230M01.class) {
			return VL230M01Dao.findPage(search);
		} else if (clazz == L230S01A.class) {
			return l230s01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L230M01A.class) {
			return (T) l230m01aDao.findByOid(oid);
		} else if (clazz == L120M01A.class) {
			return (T) l120m01aDao.findByOid(oid);
		} else if (clazz == L230S01A.class) {
			return (T) l230s01aDao.findByOid(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L230M01A.class) {
			return l230m01aDao.findListByMainId(mainId);
		} else if (clazz == L230S01A.class) {
			return l230s01aDao.findByMainId(mainId);
		}
		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS2305Service#deleteL230M01AByOids(java
	 * .lang.String[])
	 */
	@Override
	public void deleteL230M01AByOids(String[] oids) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L230M01A> l230m01as = l230m01aDao.findByOids(oids);
		for (L230M01A l230m01a : l230m01as) {
			logger.debug("delete L230M01A oid==>[{}]", l230m01a.getOid());
			l230m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l230m01a.setUpdater(user.getUserId());
			docLogService.record(l230m01a.getOid(), DocLogEnum.DELETE);
		}
		l230m01aDao.save(l230m01as);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS2305Service#findL120M01AByMainId(java
	 * .lang.String)
	 */
	@Override
	public L120M01A findL120M01AByMainId(String mainId) {
		return l120m01aDao.findByMainId(mainId);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS2305Service#saveMain(com.mega.eloan
	 * .lms.model.L230M01A, java.util.List)
	 */
	@Override
	public void saveMain(L230M01A l230m01a, List<L230S01A> l230s01as) {
		this.save(l230m01a);
		l230s01aDao.save(l230s01as);
	}

	@Override
	public Boolean reloadL230S01A(L230M01A l230m01a) {
		Boolean result = true;
		// 目前Uid是拿來存原案簽報書的mainId
		L120M01A l120m01a = l120m01aDao.findByMainId(l230m01a.getUid());
		String mainId = l230m01a.getMainId();
		// 刪除原本的
		List<L230S01A> l230s01asOld = l230s01aDao.findByMainId(mainId);
		l230s01aDao.delete(l230s01asOld);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		ArrayList<L230S01A> l230s01as = new ArrayList<L230S01A>();
		if (l120m01a != null) {
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainIdAndDocstatus(
							l120m01a.getMainId(), new String[] {
									FlowDocStatusEnum.已核准.getCode(),
									FlowDocStatusEnum.婉卻.getCode() });
			for (L140M01A l140m01a : l140m01as) {
				L230S01A l230s01a = new L230S01A();
				try {
					CapBeanUtil.copyBean(l140m01a, l230s01a, new String[] {
							"custId", "dupNo", "custName", "ownBrId",
							"docStatus", "caseNo", "caseDate", "cntrNo",
							"lnSubject", "proPerty", "currentApplyCurr",
							"currentApplyAmt" });
					l230s01a.setOid(null);
					l230s01a.setMainId(l230m01a.getMainId());
					l230s01a.setSrcMainId(l140m01a.getMainId());
					l230s01a.setCreator(user.getUnitNo());
					l230s01a.setCreateTime(CapDate.getCurrentTimestamp());
					l230s01a.setUpdater(user.getUnitNo());
					l230s01a.setUpdateTime(CapDate.getCurrentTimestamp());
					l230s01as.add(l230s01a);

				} catch (CapException e) {
					result = false;
					logger.error(e.getMessage());
					logger.info("[reloadL230S01A] EXCEPTION! copyBean", e);

				}
			}
			l230s01aDao.save(l230s01as);
		} else {
			result = false;
			logger.info("[reloadL230S01A] EXCEPTION! L120M01A is Null");
		}

		return result;
	}

	@Override
	public void deleteL230S01A(L230M01A l230m01a) {
		List<L230S01A> l230s01as = l230s01aDao.findByMainId(l230m01a
				.getMainId());
		if (l230s01as != null && !l230s01as.isEmpty()) {
			l230s01aDao.delete(l230s01as);
		}

	}

}
