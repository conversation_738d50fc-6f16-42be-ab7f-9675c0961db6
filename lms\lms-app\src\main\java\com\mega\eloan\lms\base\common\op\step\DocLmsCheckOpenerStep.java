package com.mega.eloan.lms.base.common.op.step;

import java.lang.reflect.Method;

import javax.annotation.Resource;

import org.springframework.util.ReflectionUtils;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.op.step.CheckOpenerOpStep;
import com.mega.eloan.common.service.DocCheckService;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.handler.FormHandler;
import tw.com.iisi.cap.handler.MGridHandler;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.util.StringChecker;

/**
 * <pre>
 * Override DocLmsCheckOpenerStep
 * </pre>
 * 
 * @since 2025/6/2
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>
 *          </ul>
 */
public class DocLmsCheckOpenerStep extends CheckOpenerOpStep {
	
    @Resource
    DocCheckService checkService;

    /**
     * 取得文件主檔
     * 
     * @param params
     *            頁面請求參數
     * @param executeHandler
     *            處理前端req的Handler
     * @return
     * @throws CapException
     */
    @Override
    public Meta getMeta(PageParameters params, FormHandler executeHandler) throws CapException {

        String docOid = params.getString(EloanConstants.MAIN_OID, "");
        String methodId = params.getString(FORM_ACTION, "");
        // [refs #45] Trust Boundary Violation 對變數檢驗後再使用
        String checkDocOid = StringChecker.checkXssString(docOid);
        String checkmethodId = StringChecker.checkXssString(methodId);

        boolean checkDocStatus = true;

        Class<?>[] paramTypes = null;
        // 判斷是否為GridHandler
        if (executeHandler instanceof MGridHandler) {
            paramTypes = new Class[] { ISearch.class, PageParameters.class };
        } else {
            paramTypes = new Class[] { PageParameters.class };
        }
        Method method = (CapString.isEmpty(checkmethodId) ? null : ReflectionUtils.findMethod(executeHandler.getClass(), checkmethodId, paramTypes));

        Class<? extends Meta> clazz = null;
        if (checkDocStatus && !CapString.isEmpty(checkDocOid)) {
        	
            if (method != null && method.isAnnotationPresent(DomainAuth.class)) {
                DomainAuth domainAuth = method.getAnnotation(DomainAuth.class);
                if (!domainAuth.CheckDocStatus()) {
                    return null; // CheckDocStatus = false 的方法不需要檢查 Meta
                }
            }
        	
            if (method != null && method.isAnnotationPresent(DomainClass.class)) {
                clazz = method.getAnnotation(DomainClass.class).value();
            } else if (executeHandler.getClass().isAnnotationPresent(DomainClass.class)) {
                clazz = executeHandler.getClass().getAnnotation(DomainClass.class).value();
            }

            if (clazz != null) {
                Meta meta = checkService.findByMainOid(clazz, checkDocOid);
                String checkedDocStatus = StringChecker.checkXssString(meta.getDocStatus());
                SimpleContextHolder.put(CTX_DOCSTATUS + checkDocOid, checkedDocStatus);
                return meta;
            }
        }

        return null;

    }

}
