/* 
 *MisIquotappService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <pre>
 * 核准額度資料檔 IQUOTAPP(MIS.ELV38801)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
public interface MisELF517Service {
	 
    /**
     * 
     * @param cntrNo 額度序號
     * @return
     */
	public Map<String, Object> findByCntrNo(String cntrNo);
	
	/**
	 * 
	 * @param cntrNo 額度序號
	 * @param remainLoanYN 建案完工未出售融資 Y/N
	 * @param remainLoanClass 融資案件分類
	 * @param remainLoanLocationCity 擔保品座落縣市別
	 * @param remainLoanLocationCd 擔保品座落鄉鎮市區別
	 * @param remainLoanSite3No 擔保品座落段小段
	 * @param remainLoanSite4No 擔保品座落村里
	 * @param updater 異動來源
	 * @param documentNo 案號
	 */
	public void insert(String cntrNo, String remainLoanYN, String remainLoanClass,
			String remainLoanLocationCity, BigDecimal remainLoanSite2No, BigDecimal remainLoanSite3No, String remainLoanSite4No,
			BigDecimal firstLoanUnsoldHouseQuantity, BigDecimal currentUnsoldHouseQuantity,
			BigDecimal monthOfCreditPeriod, Date finalCreditPeriodEndDate, String documentNo);

	/**
	 * 
	 * @param cntrNo 額度序號
	 * @param remainLoanYN 建案完工未出售融資 Y/N
	 * @param remainLoanClass 融資案件分類
	 * @param remainLoanLocationCity 擔保品座落縣市別
	 * @param remainLoanLocationCd 擔保品座落鄉鎮市區別
	 * @param remainLoanSite3No 擔保品座落段小段
	 * @param remainLoanSite4No 擔保品座落村里
	 * @param updater 異動來源
	 * @param documentNo 案號
	 */
	public void update(String cntrNo, String remainLoanYN, String remainLoanClass,
			String remainLoanLocationCity, BigDecimal remainLoanSite2No, BigDecimal remainLoanSite3No, String remainLoanSite4No,
			BigDecimal firstLoanUnsoldHouseQuantity, BigDecimal currentUnsoldHouseQuantity,
			BigDecimal monthOfCreditPeriod, Date finalCreditPeriodEndDate, String documentNo);

	/**
	 * J-110-0497 餘屋貸款貸後管理 - 新增
	 */
	public void insertForFms(String cntrNo, String buildName, BigDecimal begForSell, BigDecimal soldNumber,
             String proStatus, String behindDesc, String isSameCase);

	/**
	 * J-110-0497 餘屋貸款貸後管理 - 更新
	 */
	public void updateForFms(String cntrNo, String buildName, BigDecimal begForSell, BigDecimal soldNumber,
             String proStatus, String behindDesc, String isSameCase);
	 
	/**
	 * 查詢前次ELF517 資料
	 * J-104-0276-001 額度明細檢核附表「建案完工未出售房屋融資註記」欄位，增加引進最近一次「建案完工未出售房屋融資註記」欄位。
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> findByCntrNoFullField(String cntrNo)	;
	
	public Map<String, Object> getByCntrNo(String cntrNo);
}
