package com.mega.eloan.lms.lrs.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;


import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import com.iisigroup.cap.component.PageParameters;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.inet.report.ReportException;
import com.mega.eloan.lms.base.common.LmsExcelUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lrs.report.LMS1700R01RptService;
import com.mega.eloan.lms.lrs.report.LMS1700XLSService;
import com.mega.eloan.lms.lrs.service.LMS1801Service;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.sso.service.BranchService;

@Service("lms1700xlsservcie")
public class LMS1700XLSServiceImpl implements LMS1700XLSService, FileDownloadService{
	@Resource
	BranchService branchService;
	
	@Resource
	LMS1801Service lms1801Service;
	
	@Resource
	RetrialService retrialService;

	@Resource
	LMS1700R01RptService lms1700R01RptService;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			String mode = Util.trim(params.getString("xlsKind"));
			if(Util.equals("1", mode)){
				baos = (ByteArrayOutputStream) this.generateXls_ALMS17004(params);	
			}
			if(baos==null){
				return null;
			}else{
				return baos.toByteArray();	
			}			
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}
	
	private ByteArrayOutputStream generateXls_ALMS17004(PageParameters params) throws IOException, Exception {
				
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		
		List<L170M01A> list = new ArrayList<L170M01A>();
		if(true){
			String oids = Util.trim(params.getString("oids"));
			if(Util.isNotEmpty(oids)){
				String[] oid_arr = oids.split("\\|");
				
				for(String oid: oid_arr){
					L170M01A l170m01a = retrialService.findL170M01A_oid(oid);
					if(l170m01a!=null){
						list.add(l170m01a);
					}
				}			
			}	
		}
		
		if(list.size()>0){
			Set<String> cnt_ownBrId = new HashSet<String>();
			Set<String> cnt_retrialDate = new HashSet<String>();
			for(L170M01A l170m01a : list){
				cnt_ownBrId.add(l170m01a.getOwnBrId());
				cnt_retrialDate.add(Util.trim(TWNDate.toAD(l170m01a.getRetrialDate())));
			}
			
			if(cnt_ownBrId.size()==1 && cnt_retrialDate.size()==1){
				_ALMS17004( outputStream, list, list.get(0).getOwnBrId(), Util.trim(TWNDate.toAD(list.get(0).getRetrialDate())));
			}else{
				lms1801Service.genExcelWithMsg(outputStream, "請選擇同一分行【"+StringUtils.join(cnt_ownBrId, "、")+"】、"
						+"同一覆審日期【"+StringUtils.join(cnt_retrialDate, "、")+"】的資料");	
			}			
		}else{
			lms1801Service.genExcelWithMsg(outputStream, "查無資料");
		}		
		
		if(outputStream!=null){
			outputStream.flush();	
		}		
		return outputStream;
	}
	
	private void _ALMS17004(ByteArrayOutputStream outputStream, List<L170M01A> list, String ownBrId, String retrialDate)
	throws IOException {
		
		HSSFWorkbook workbook = null;
		HSSFSheet sheet1 = null;
		if(true) {			
			workbook = new HSSFWorkbook();
			sheet1 = workbook.createSheet("Sheet1");
						
			HSSFFont headFont12 = null;
			HSSFFont headFont14 = null;
			HSSFCellStyle cellFormatL = null;
			HSSFCellStyle cellFormatL_Border = null;
			HSSFCellStyle cellFormatR_Border = null;
			HSSFCellStyle cellFormatL_14 = null;
			
			headFont12 = workbook.createFont();
			headFont12.setFontName("標楷體");
			headFont12.setFontHeightInPoints((short) 12);
			headFont12.setBold(false);
			
			cellFormatL = LmsExcelUtil.setCellFormat(workbook, headFont12, HorizontalAlignment.LEFT, 
					false, true);
			cellFormatL.setVerticalAlignment(VerticalAlignment.TOP);
			
			cellFormatL.setVerticalAlignment(VerticalAlignment.TOP);
			
			cellFormatL_Border = LmsExcelUtil.setCellFormat(workbook, headFont12, HorizontalAlignment.LEFT, 
					true, true);
			cellFormatL.setVerticalAlignment(VerticalAlignment.TOP);
			
			cellFormatR_Border = LmsExcelUtil.setCellFormat(workbook, headFont12, HorizontalAlignment.RIGHT, 
					true, true);
			cellFormatL.setVerticalAlignment(VerticalAlignment.TOP);
			
			//======
			headFont14 = workbook.createFont();
			headFont14.setFontName("標楷體");
			headFont14.setFontHeightInPoints((short) 14);
			headFont14.setBold(false);
			
			cellFormatL_14 = LmsExcelUtil.setCellFormat(workbook, headFont14, HorizontalAlignment.LEFT, 
					false, true);
			cellFormatL_14.setVerticalAlignment(VerticalAlignment.BOTTOM);
			
			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("序號", 6);
			headerMap.put("統一編號", 15);
			headerMap.put("客戶名稱", 20);
			headerMap.put("前次覆審日期", 15);	
			headerMap.put("前次覆審內容", 25);
			headerMap.put("前次覆審結果及意見", 25);
			headerMap.put("不覆審註記", 15);
			headerMap.put("備註", 7);
			
			int totalColSize = headerMap.size();
			List<String[]> rows = new ArrayList<String[]>();
			String nextLine = "\r\n";
			for(L170M01A l170m01a: list){
				L170M01A befMeta = retrialService.findL170M01A_bef(l170m01a);
				if(befMeta==null){
					continue;
				}
				List<L170M01D> befl170m01d_list = retrialService.findL170M01D_orderBySeq(befMeta);
				L170M01F befl170m01f = befMeta.getL170m01f();
				if(befl170m01f==null){
					befl170m01f = new L170M01F(); 
				}
				String chkResult = "";
				if(true){
					String retialcomm = (Util.equals("Y", befl170m01f.getRetialComm())?"是":"否");
					chkResult = "一、為確保本行債權，有無必要辦理保全措施："+retialcomm+"。"+nextLine;
					
					String bef_condition = Util.trim(befl170m01f.getCondition());
					if(Util.equals("1", befl170m01f.getConFlag())){
						chkResult += ("二、覆審正常");
						if(Util.isNotEmpty(bef_condition)){
							chkResult += ("："+nextLine+bef_condition);	
						}
					}else{
						chkResult += ("二、異常情形，應改善或注意事項："+nextLine + bef_condition);
					}
				}
				//---
				String[] arr = new String[totalColSize];
				for(int i_col = 0; i_col <totalColSize ; i_col++){
					arr[i_col] = "";
				}
				arr[0] = "";//之後顯示 count 
				arr[1] = Util.trim(befMeta.getCustId())+Util.trim(befMeta.getDupNo());
				arr[2] = Util.trim(befMeta.getCustName());
				arr[3] = Util.trim(TWNDate.toAD(befMeta.getLastRetrialDate()));
				arr[4] = lms1700R01RptService.get_L170M01D_ITEMCONTENTS(befl170m01d_list);
				arr[5] = chkResult;
				arr[6] = Util.trim(befMeta.getNCkdFlag());
				arr[7] = "";
				//---
				rows.add(arr);
			}
			
			if(true){
				int i=1;
				for(String[] arr :rows){
					arr[0] = String.valueOf(i);
					i++;
				}
			}			
			//==============================
			int rowIdx = 0;
			int rowHeightAt14 = 360;
			int rowHeightAt12 = 300;
			//==============================
			if(true){
				if(sheet1.getRow(rowIdx) == null) {
					sheet1.createRow(rowIdx);
				}
				//列高，單位是1/20pt
				sheet1.getRow(rowIdx).setHeightInPoints(rowHeightAt14/20);
				sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize-1));
				LmsExcelUtil.addCell(sheet1.getRow(rowIdx), 0, branchService.getBranchName(ownBrId)
						+ "前次覆審內容及意見一覽表", cellFormatL_14);
			}
			//==============================
			if(true){
				rowIdx = 1;
				int colIdx_row1 = 3;
				
				if(sheet1.getRow(rowIdx) == null) {
					sheet1.createRow(rowIdx);
				}
				sheet1.getRow(rowIdx).setHeightInPoints(rowHeightAt12/20);
				sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row1-1));
				LmsExcelUtil.addCell(sheet1.getRow(rowIdx), 0, "分行代碼：" + ownBrId,  cellFormatL);
				sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row1, totalColSize-1));
				LmsExcelUtil.addCell(sheet1.getRow(rowIdx), colIdx_row1, "",  cellFormatL);
			}
			if(true){
				rowIdx = 2;
				int colIdx_row1 = 5;
				
				if(sheet1.getRow(rowIdx) == null) {
					sheet1.createRow(rowIdx);
				}
				sheet1.getRow(rowIdx).setHeightInPoints(rowHeightAt12/20);
				sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row1-1));
				LmsExcelUtil.addCell(sheet1.getRow(rowIdx), 0, "預計覆審日：" + retrialDate,  cellFormatL);
				sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row1, totalColSize-1));
				LmsExcelUtil.addCell(sheet1.getRow(rowIdx), colIdx_row1, "查詢日期：" + TWNDate.toAD(new Date()),  cellFormatL);
			}
			//==============================
			rowIdx = 3;
			int colIdx = 0;
			if(sheet1.getRow(rowIdx) == null) {
				sheet1.createRow(rowIdx);
			}
			HSSFRow row3 = sheet1.getRow(rowIdx);
			for(String h: headerMap.keySet()){
				int colWidth = headerMap.get(h);
				sheet1.setColumnWidth(colIdx, colWidth * 256);
				LmsExcelUtil.addCell(row3, colIdx, h,  cellFormatL_Border);
				//---
				colIdx++;
			}
			//==============================
			rowIdx = 4;
			int i = 0;
			if(sheet1.getRow(rowIdx) == null) {
				sheet1.createRow(rowIdx);
			}
			HSSFRow row4 = sheet1.getRow(rowIdx);
			
			for(String[] arr: rows){	
				int colLen = arr.length;
				for(int i_col = 0; i_col <colLen ; i_col++){
					LmsExcelUtil.addCell(row4, i_col, arr[i_col], (i_col==0)?cellFormatR_Border:cellFormatL_Border);
				}	
				i++;
			}
			
			workbook.write(outputStream);
			workbook.close();
		}
	}
}
