/* 
 * L120S01Q.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 簽報書主檔資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S01Q", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class L120S01Q extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	// /** 借款人姓名 **/
	// @Size(max=150)
	// @Column(name="CUSTNAME", length=150, columnDefinition="VARCHAR(150)")
	// private String custName;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 版本 **/
	@Size(max = 2)
	@Column(name = "VER", length = 2, columnDefinition = "VARCHAR (2)")
	private String ver;

	/**
	 * 有無善進社會責任
	 * <p/>
	 * 赤道原則，Ver=01
	 */
	@Size(max = 1)
	@Column(name = "HASDUTY", length = 1, columnDefinition = "CHAR(1)")
	private String hasDuty;

	/**
	 * 有無顯著之具體事實
	 * <p/>
	 * 赤道原則，Ver=01
	 */
	@Size(max = 1)
	@Column(name = "HASDEEDS", length = 1, columnDefinition = "CHAR(1)")
	private String hasDeeds;

	/**
	 * 是否已評估客戶之合法性及誠信經營政策
	 * <p/>
	 * 企業誠信經營評估
	 */
	@Size(max = 1)
	@Column(name = "HASLEGALITY", length = 1, columnDefinition = "CHAR(1)")
	private String hasLegality;

	/**
	 * 有無涉及不誠信
	 * <p/>
	 * 企業誠信經營評估
	 */
	@Size(max = 1)
	@Column(name = "HASBADFAITH", length = 1, columnDefinition = "CHAR(1)")
	private String hasBadFaith;

	/**
	 * 涉及不誠信說明
	 * <p/>
	 * Ver => 01.02 <br/>
	 * 存放在 L120M01D ItemType=Q<br/>
	 * Ver = 03 存於此欄位
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "BADFAITHMEMO", columnDefinition = "CLOB")
	private String badFaithMemo;

	/**
	 * 是否未善盡環境保護
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 1)
	@Column(name = "HASD1", length = 1, columnDefinition = "VARCHAR (1)")
	private String hasD1;

	/**
	 * 是否未善盡企業社會責任
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 1)
	@Column(name = "HASD2", length = 1, columnDefinition = "VARCHAR (1)")
	private String hasD2;

	/**
	 * 是否發生涉及侵害人權行為或事件
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 1)
	@Column(name = "HASD3", length = 1, columnDefinition = "VARCHAR (1)")
	private String hasD3;

	/**
	 * 未善盡環境保護_細項1
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 5)
	@Column(name = "ITEM1_D1", length = 5, columnDefinition = "VARCHAR (5)")
	private String item1_D1;

	/**
	 * 未善盡環境保護_細項1統編
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 10)
	@Column(name = "ITEMID_D1", length = 10, columnDefinition = "VARCHAR (10)")
	private String itemId_D1;

	/**
	 * 未善盡環境保護_細項1統編重覆碼
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 1)
	@Column(name = "ITEMDUPNO_D1", length = 1, columnDefinition = "VARCHAR (1)")
	private String itemDupNo_D1;

	/**
	 * 未善盡環境保護_細項1名稱
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 120)
	@Column(name = "ITEMNAME_D1", length = 120, columnDefinition = "VARCHAR (120)")
	private String itemName_D1;

	/**
	 * 未善盡環境保護_細項2
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 20)
	@Column(name = "ITEM2_D1", length = 20, columnDefinition = "VARCHAR (20)")
	private String item2_D1;

	/**
	 * 未善盡環境保護_細項2備註
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 300)
	@Column(name = "ITEMMEMO_D1", length = 300, columnDefinition = "VARCHAR (300)")
	private String itemMemo_D1;

	/**
	 * 未善盡環境保護_細項3
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 1)
	@Column(name = "ITEM3_D1", length = 1, columnDefinition = "VARCHAR (1)")
	private String item3_D1;

	/**
	 * 未善盡企業社會責任_細項1
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 5)
	@Column(name = "ITEM1_D2", length = 5, columnDefinition = "VARCHAR (5)")
	private String item1_D2;

	/**
	 * 未善盡企業社會責任_細項1統編
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 10)
	@Column(name = "ITEMID_D2", length = 10, columnDefinition = "VARCHAR (10)")
	private String itemId_D2;

	/**
	 * 未善盡企業社會責任_細項1統編重覆碼
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 1)
	@Column(name = "ITEMDUPNO_D2", length = 1, columnDefinition = "VARCHAR (1)")
	private String itemDupNo_D2;

	/**
	 * 未善盡企業社會責任_細項1名稱
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 120)
	@Column(name = "ITEMNAME_D2", length = 120, columnDefinition = "VARCHAR (120)")
	private String itemName_D2;

	/**
	 * 未善盡企業社會責任_細項2
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 20)
	@Column(name = "ITEM2_D2", length = 20, columnDefinition = "VARCHAR (20)")
	private String item2_D2;

	/**
	 * 未善盡企業社會責任_細項2備註
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 300)
	@Column(name = "ITEMMEMO_D2", length = 300, columnDefinition = "VARCHAR (300)")
	private String itemMemo_D2;

	/**
	 * 發生涉及侵害人權行為或事件_細項1
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 5)
	@Column(name = "ITEM1_D3", length = 5, columnDefinition = "VARCHAR (5)")
	private String item1_D3;

	/**
	 * 發生涉及侵害人權行為或事件_細項1統編
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 10)
	@Column(name = "ITEMID_D3", length = 10, columnDefinition = "VARCHAR (10)")
	private String itemId_D3;

	/**
	 * 發生涉及侵害人權行為或事件_細項1統編重覆碼
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 1)
	@Column(name = "ITEMDUPNO_D3", length = 1, columnDefinition = "VARCHAR (1)")
	private String itemDupNo_D3;

	/**
	 * 發生涉及侵害人權行為或事件_細項1名稱
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 120)
	@Column(name = "ITEMNAME_D3", length = 120, columnDefinition = "VARCHAR (120)")
	private String itemName_D3;

	/**
	 * 發生涉及侵害人權行為或事件_細項2
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 20)
	@Column(name = "ITEM2_D3", length = 20, columnDefinition = "VARCHAR (20)")
	private String item2_D3;

	/**
	 * 發生涉及侵害人權行為或事件_細項2備註
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	@Size(max = 300)
	@Column(name = "ITEMMEMO_D3", length = 300, columnDefinition = "VARCHAR (300)")
	private String itemMemo_D3;

	/**
	 * 是否有ESG評等
	 * <p/>
	 * 赤道原則，Ver=03
	 */
	@Size(max = 1)
	@Column(name = "HASESG", length = 1, columnDefinition = "CHAR(1)")
	private String hasESG;

	/**
	 * 評等機構
	 * <p/>
	 * 赤道原則，Ver=03
	 */
	@Size(max = 30)
	@Column(name = "ESGAGENCY", length = 30, columnDefinition = "VARCHAR(30)")
	private String esgAgency;

	/**
	 * 評等
	 * <p/>
	 * 赤道原則，Ver=03
	 */
	@Size(max = 20)
	@Column(name = "ESGGRADE", length = 10, columnDefinition = "VARCHAR(10)")
	private String esgGrade;

	/**
	 * 中國「限汙令」影響評估
	 * <p/>
	 * 企業誠信經營評估，Ver=03
	 */
	@Size(max = 1)
	@Column(name = "HASCNLIM", length = 1, columnDefinition = "CHAR(1)")
	private String hasCnLim;

	/**
	 * 中國「限汙令」說明
	 * <p/>
	 * 企業誠信經營評估，Ver=03
	 */
	@Size(max = 1530)
	@Column(name = "CNLIMMEMO", length = 1530, columnDefinition = "VARCHAR(1530)")
	private String cnLimMemo;

	/**
	 * 是否通過檢核
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "CHECKYN", length = 1, columnDefinition = "VARCHAR(1)")
	private String checkYN;

	/** 資料來源 **/
	@Column(name = "ESGSOURCE", length = 30, columnDefinition = "CHAR(30)", nullable = false)
	private String esgSource;

	/** Sustainalytics ESG 風險評分(100-0, 0分最佳) **/
	@Column(name = "ESGSCORE", columnDefinition = "VARCHAR(10)")
	private String esgScore;

	/**
	 * MSCI<br/>
	 * ESG評級<br/>
	 * (AAA-CCC, <font color='red'>AAA最佳</font>)
	 **/
	@Column(name = "MSCILEVEL", columnDefinition = "CHAR(5)")
	private String msciLevel;

	/** FTSE Russell ESG 評級 (0-5, 5級最佳) **/
	@Column(name = "ESGLEVEL", columnDefinition = "VARCHAR 10)")
	private String esgLevel;

	/** ISS 環境揭露評級 (1-10, 1級最佳) **/
	@Column(name = "ISSLEVEL1", columnDefinition = "VARCHAR(10)")
	private String issLevel1;

	/** ISS 社會揭露評級 (1-10, 1級最佳) **/
	@Column(name = "ISSLEVEL2", columnDefinition = "VARCHAR(10)")
	private String issLevel2;

	/** 台灣公司治理評鑑 (前5%最佳) **/
	@Column(name = "COMPANYGOVERNANCE", columnDefinition = "CHAR(100)")
	private String companyGovernance;

	/** 收件日期 **/
	@Column(name = "ESGRECEIVEDATE", columnDefinition = "VARCHAR(20)")
	private String esgReceiveDate;

	/**
	 * ISS<br/>
	 * ESG評級<br/>
	 * (A-D, <font color='red'>A級最佳</font>)
	 **/
	@Column(name = "ISSLEVEL", columnDefinition = "CHAR(5)")
	private String issLevel;

	/** Moody ESG評分(0-100,100分最佳) **/
	@Column(name = "MOODY", columnDefinition = "VARCHAR(10)")
	private String moody;

	/** S&P Global ESG評分(0-100,100分最佳) **/
	@Column(name = "SPSCORE", columnDefinition = "VARCHAR(10)")
	private String spScore;

	/**
	 * ESG最終評估結論
	 * <p/>
	 * CES.C290M01A.FINALASSESSMENT
	 */
	@Size(max = 20)
	@Column(name = "FINALASSESSMENT", columnDefinition = "VARCHAR(20)")
	private String finalAssessment;

    /**
     * 有無涉及不誠信_細項
     */
    @Size(max = 20)
    @Column(name = "BADFAITHITEM", length = 20, columnDefinition = "VARCHAR(20)")
    private String badFaithItem;

	/** 查詢徵信永續經營相關資訊日期 **/
	@Column(name = "QCESESGDATE", columnDefinition = "VARCHAR(20)")
	private String qCesEsgDate;

	/** 是否屬高環境 **/
	@Size(max = 1)
	@Column(name = "ISHIGHENV", length = 1, columnDefinition = "VARCHAR(1)")
	private String isHighEnv;

	/** 是否屬高碳排 **/
	@Size(max = 1)
	@Column(name = "ISHIGHCARBONEMS", length = 1, columnDefinition = "VARCHAR(1)")
	private String isHighCarbonEms;

	/** 是否屬去碳化 **/
	@Size(max = 1)
	@Column(name = "ISDECARBONEMS", length = 1, columnDefinition = "VARCHAR(1)")
	private String isDeCarbonEms;

	/** 是否屬永續 **/
	@Size(max = 1)
	@Column(name = "ISSUSTAIN", length = 1, columnDefinition = "VARCHAR(1)")
	private String isSustain;

	/** SinoPac+ ESG 評等 (A+~C, A+最佳) **/
	@Column(name = "SINOPAC", columnDefinition = "VARCHAR(10)")
	private String sinopac;

	/** 台灣永續評鑑 (AAA~D, AAA最佳) **/
	@Column(name = "SUSTAINABILITY", columnDefinition = "VARCHAR(10)")
	private String sustainability;

	/** 永續說明 **/
	@Column(name = "SUSTAINMEMO", columnDefinition = "VARCHAR(300)")
	private String sustainMemo;

	/** 查詢SBT狀態日期 **/
	@Column(name = "QESGSBTIDATE", columnDefinition = "VARCHAR(20)")
	private String qEsgSbtiDate;

	/** SBT狀態 **/
	@Column(name = "SBTIISCOMMITED", columnDefinition = "VARCHAR(50)")
	private String sbtiIsCommited;

	/** SBT調查表核准日 **/
	@Column(name = "SBTIAPPROVEDATE", columnDefinition = "VARCHAR(20)")
	private String sbtiApproveDate;

	/** 查詢永續程度自評結果日期 **/
	@Column(name = "QSUSTAINEVALDATE", columnDefinition = "VARCHAR(20)")
	private String qSustainEvalDate;

	/** 經濟活動數量 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SEREC", columnDefinition = "DECIMAL(3,0)")
	private Integer seRec;

	/** 經濟活動營收占比 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SERATESUM", columnDefinition = "DECIMAL(3,0)")
	private Integer seRateSum;

	/** 符合永續經濟活動認定參考指引之經濟活動數量 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SEREC_Y", columnDefinition = "DECIMAL(3,0)")
	private Integer seRec_Y;

	/** 符合永續經濟活動認定參考指引之經濟活動營收比重 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SERATEPCT", columnDefinition = "DECIMAL(3,0)")
	private Integer seRatePct;
	
	/** 是否符合赤道原則授信案件 **/
	@Size(max = 1)
	@Column(name = "EPSFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String epsFlag;
	
	/** 產業類別 **/
	@Size(max = 2)
	@Column(name = "INDUSTRYNO", length = 2, columnDefinition = "VARCHAR(2)")
	private String industryNo;
	
	/** 環境社會風險分級 **/
	@Size(max = 1)
	@Column(name = "ENVANDSOCIRISKLVL", length = 1, columnDefinition = "VARCHAR(1)")
	private String envAndSociRiskLvl;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	// /** 取得借款人姓名 **/
	// public String getCustName() {
	// return this.custName;
	// }
	// /** 設定借款人姓名 **/
	// public void setCustName(String value) {
	// this.custName = value;
	// }

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得版本 **/
	public String getVer() {
		return this.ver;
	}

	/** 設定版本 **/
	public void setVer(String value) {
		this.ver = value;
	}

	/**
	 * 取得有無善進社會責任
	 * <p/>
	 * 赤道原則，Ver=01
	 */
	public String getHasDuty() {
		return this.hasDuty;
	}

	/**
	 * 設定有無善進社會責任
	 * <p/>
	 * 赤道原則，Ver=01
	 **/
	public void setHasDuty(String value) {
		this.hasDuty = value;
	}

	/**
	 * 取得有無顯著之具體事實
	 * <p/>
	 * 赤道原則，Ver=01
	 */
	public String getHasDeeds() {
		return this.hasDeeds;
	}

	/**
	 * 設定有無顯著之具體事實
	 * <p/>
	 * 赤道原則，Ver=01
	 **/
	public void setHasDeeds(String value) {
		this.hasDeeds = value;
	}

	/**
	 * 取得是否已評估客戶之合法性及誠信經營政策
	 * <p/>
	 * 企業誠信經營評估
	 */
	public String getHasLegality() {
		return this.hasLegality;
	}

	/**
	 * 設定是否已評估客戶之合法性及誠信經營政策
	 * <p/>
	 * 企業誠信經營評估
	 **/
	public void setHasLegality(String value) {
		this.hasLegality = value;
	}

	/**
	 * 取得有無涉及不誠信
	 * <p/>
	 * 企業誠信經營評估
	 */
	public String getHasBadFaith() {
		return this.hasBadFaith;
	}

	/**
	 * 設定有無涉及不誠信
	 * <p/>
	 * 企業誠信經營評估
	 **/
	public void setHasBadFaith(String value) {
		this.hasBadFaith = value;
	}

	/**
	 * 取得涉及不誠信說明
	 * <p/>
	 * Ver => 01.02 <br/>
	 * 存放在 L120M01D ItemType=Q<br/>
	 * Ver = 03 存於此欄位
	 */
	public String getBadFaithMemo() {
		return this.badFaithMemo;
	}

	/**
	 * 設定涉及不誠信說明
	 * <p/>
	 * Ver => 01.02 <br/>
	 * 存放在 L120M01D ItemType=Q<br/>
	 * Ver = 03 存於此欄位
	 **/
	public void setBadFaithMemo(String value) {
		this.badFaithMemo = value;
	}

	/**
	 * 取得是否未善盡環境保護
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getHasD1() {
		return this.hasD1;
	}

	/**
	 * 設定是否未善盡環境保護
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setHasD1(String value) {
		this.hasD1 = value;
	}

	/**
	 * 取得是否未善盡企業社會責任
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getHasD2() {
		return this.hasD2;
	}

	/**
	 * 設定是否未善盡企業社會責任
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setHasD2(String value) {
		this.hasD2 = value;
	}

	/**
	 * 取得是否發生涉及侵害人權行為或事件
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getHasD3() {
		return this.hasD3;
	}

	/**
	 * 設定是否發生涉及侵害人權行為或事件
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setHasD3(String value) {
		this.hasD3 = value;
	}

	/**
	 * 取得未善盡環境保護_細項1
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItem1_D1() {
		return this.item1_D1;
	}

	/**
	 * 設定未善盡環境保護_細項1
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItem1_D1(String value) {
		this.item1_D1 = value;
	}

	/**
	 * 取得未善盡環境保護_細項1統編
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItemId_D1() {
		return this.itemId_D1;
	}

	/**
	 * 設定未善盡環境保護_細項1統編
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItemId_D1(String value) {
		this.itemId_D1 = value;
	}

	/**
	 * 取得未善盡環境保護_細項1統編重覆碼
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItemDupNo_D1() {
		return this.itemDupNo_D1;
	}

	/**
	 * 設定未善盡環境保護_細項1統編重覆碼
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItemDupNo_D1(String value) {
		this.itemDupNo_D1 = value;
	}

	/**
	 * 取得未善盡環境保護_細項1名稱
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItemName_D1() {
		return this.itemName_D1;
	}

	/**
	 * 設定未善盡環境保護_細項1名稱
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItemName_D1(String value) {
		this.itemName_D1 = value;
	}

	/**
	 * 取得未善盡環境保護_細項2
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItem2_D1() {
		return this.item2_D1;
	}

	/**
	 * 設定未善盡環境保護_細項2
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItem2_D1(String value) {
		this.item2_D1 = value;
	}

	/**
	 * 取得未善盡環境保護_細項2備註
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItemMemo_D1() {
		return this.itemMemo_D1;
	}

	/**
	 * 設定未善盡環境保護_細項2備註
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItemMemo_D1(String value) {
		this.itemMemo_D1 = value;
	}

	/**
	 * 取得未善盡環境保護_細項3
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItem3_D1() {
		return this.item3_D1;
	}

	/**
	 * 設定未善盡環境保護_細項3
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItem3_D1(String value) {
		this.item3_D1 = value;
	}

	/**
	 * 取得未善盡企業社會責任_細項1
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItem1_D2() {
		return this.item1_D2;
	}

	/**
	 * 設定未善盡企業社會責任_細項1
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItem1_D2(String value) {
		this.item1_D2 = value;
	}

	/**
	 * 取得未善盡企業社會責任_細項1統編
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItemId_D2() {
		return this.itemId_D2;
	}

	/**
	 * 設定未善盡企業社會責任_細項1統編
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItemId_D2(String value) {
		this.itemId_D2 = value;
	}

	/**
	 * 取得未善盡企業社會責任_細項1統編重覆碼
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItemDupNo_D2() {
		return this.itemDupNo_D2;
	}

	/**
	 * 設定未善盡企業社會責任_細項1統編重覆碼
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItemDupNo_D2(String value) {
		this.itemDupNo_D2 = value;
	}

	/**
	 * 取得未善盡企業社會責任_細項1名稱
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItemName_D2() {
		return this.itemName_D2;
	}

	/**
	 * 設定未善盡企業社會責任_細項1名稱
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItemName_D2(String value) {
		this.itemName_D2 = value;
	}

	/**
	 * 取得未善盡企業社會責任_細項2
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItem2_D2() {
		return this.item2_D2;
	}

	/**
	 * 設定未善盡企業社會責任_細項2
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItem2_D2(String value) {
		this.item2_D2 = value;
	}

	/**
	 * 取得未善盡企業社會責任_細項2備註
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItemMemo_D2() {
		return this.itemMemo_D2;
	}

	/**
	 * 設定未善盡企業社會責任_細項2備註
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItemMemo_D2(String value) {
		this.itemMemo_D2 = value;
	}

	/**
	 * 取得發生涉及侵害人權行為或事件_細項1
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItem1_D3() {
		return this.item1_D3;
	}

	/**
	 * 設定發生涉及侵害人權行為或事件_細項1
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItem1_D3(String value) {
		this.item1_D3 = value;
	}

	/**
	 * 取得發生涉及侵害人權行為或事件_細項1統編
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItemId_D3() {
		return this.itemId_D3;
	}

	/**
	 * 設定發生涉及侵害人權行為或事件_細項1統編
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItemId_D3(String value) {
		this.itemId_D3 = value;
	}

	/**
	 * 取得發生涉及侵害人權行為或事件_細項1統編重覆碼
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItemDupNo_D3() {
		return this.itemDupNo_D3;
	}

	/**
	 * 設定發生涉及侵害人權行為或事件_細項1統編重覆碼
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItemDupNo_D3(String value) {
		this.itemDupNo_D3 = value;
	}

	/**
	 * 取得發生涉及侵害人權行為或事件_細項1名稱
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItemName_D3() {
		return this.itemName_D3;
	}

	/**
	 * 設定發生涉及侵害人權行為或事件_細項1名稱
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItemName_D3(String value) {
		this.itemName_D3 = value;
	}

	/**
	 * 取得發生涉及侵害人權行為或事件_細項2
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItem2_D3() {
		return this.item2_D3;
	}

	/**
	 * 設定發生涉及侵害人權行為或事件_細項2
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItem2_D3(String value) {
		this.item2_D3 = value;
	}

	/**
	 * 取得發生涉及侵害人權行為或事件_細項2備註
	 * <p/>
	 * 赤道原則，Ver > 02
	 */
	public String getItemMemo_D3() {
		return this.itemMemo_D3;
	}

	/**
	 * 設定發生涉及侵害人權行為或事件_細項2備註
	 * <p/>
	 * 赤道原則，Ver > 02
	 **/
	public void setItemMemo_D3(String value) {
		this.itemMemo_D3 = value;
	}

	/**
	 * 取得是否有ESG評等
	 * <p/>
	 * 赤道原則，Ver=03
	 */
	public String getHasESG() {
		return this.hasESG;
	}

	/**
	 * 設定是否有ESG評等
	 * <p/>
	 * 赤道原則，Ver=03
	 **/
	public void setHasESG(String value) {
		this.hasESG = value;
	}

	/**
	 * 取得評等機構
	 * <p/>
	 * 赤道原則，Ver=03
	 */
	public String getEsgAgency() {
		return this.esgAgency;
	}

	/**
	 * 設定評等機構
	 * <p/>
	 * 赤道原則，Ver=03
	 **/
	public void setEsgAgency(String value) {
		this.esgAgency = value;
	}

	/**
	 * 取得評等
	 * <p/>
	 * 赤道原則，Ver=03
	 */
	public String getEsgGrade() {
		return this.esgGrade;
	}

	/**
	 * 設定評等
	 * <p/>
	 * 赤道原則，Ver=03
	 **/
	public void setEsgGrade(String value) {
		this.esgGrade = value;
	}

	/**
	 * 取得是否通過檢核
	 * <p/>
	 * Y/N
	 */
	public String getCheckYN() {
		return this.checkYN;
	}

	/**
	 * 取得中國「限汙令」影響評估
	 * <p/>
	 * 企業誠信經營評估，Ver=03
	 */
	public String getHasCnLim() {
		return this.hasCnLim;
	}

	/**
	 * 設定中國「限汙令」影響評估
	 * <p/>
	 * 企業誠信經營評估，Ver=03
	 **/
	public void setHasCnLim(String value) {
		this.hasCnLim = value;
	}

	/**
	 * 取得中國「限汙令」說明
	 * <p/>
	 * 企業誠信經營評估，Ver=03
	 */
	public String getCnLimMemo() {
		return this.cnLimMemo;
	}

	/**
	 * 設定中國「限汙令」說明
	 * <p/>
	 * 企業誠信經營評估，Ver=03
	 **/
	public void setCnLimMemo(String value) {
		this.cnLimMemo = value;
	}

	/**
	 * 設定是否通過檢核
	 * <p/>
	 * Y/N
	 **/
	public void setCheckYN(String value) {
		this.checkYN = value;
	}

	public void setEsgSource(String esgSource) {
		this.esgSource = esgSource;
	}

	public String getEsgSource() {
		return esgSource;
	}

	public void setEsgScore(String esgScore) {
		this.esgScore = esgScore;
	}

	public String getEsgScore() {
		return esgScore;
	}

	public void setMsciLevel(String msciLevel) {
		this.msciLevel = msciLevel;
	}

	public String getMsciLevel() {
		return msciLevel;
	}

	public void setEsgLevel(String esgLevel) {
		this.esgLevel = esgLevel;
	}

	public String getEsgLevel() {
		return esgLevel;
	}

	public void setIssLevel1(String issLevel1) {
		this.issLevel1 = issLevel1;
	}

	public String getIssLevel1() {
		return issLevel1;
	}

	public void setIssLevel2(String issLevel2) {
		this.issLevel2 = issLevel2;
	}

	public String getIssLevel2() {
		return issLevel2;
	}

	public void setCompanyGovernance(String companyGovernance) {
		this.companyGovernance = companyGovernance;
	}

	public String getCompanyGovernance() {
		return companyGovernance;
	}

	public void setEsgReceiveDate(String esgReceiveDate) {
		this.esgReceiveDate = esgReceiveDate;
	}

	public String getEsgReceiveDate() {
		return esgReceiveDate;
	}

	public void setIssLevel(String issLevel) {
		this.issLevel = issLevel;
	}

	public String getIssLevel() {
		return issLevel;
	}

	public void setMoody(String moody) {
		this.moody = moody;
	}

	public String getMoody() {
		return moody;
	}

	public void setSpScore(String spScore) {
		this.spScore = spScore;
	}

	public String getSpScore() {
		return spScore;
	}

	/**
	 * 取得ESG最終評估結論
	 * <p/>
	 * CES.C290M01A.FINALASSESSMENT
	 */
	public String getFinalAssessment() {
		return finalAssessment;
	}

	/**
	 * 設定ESG最終評估結論
	 * <p/>
	 * CES.C290M01A.FINALASSESSMENT
	 */
	public void setFinalAssessment(String finalAssessment) {
		this.finalAssessment = finalAssessment;
	}

    /**
     * 取得有無涉及不誠信_細項
     */
    public String getBadFaithItem() {
        return badFaithItem;
    }

    /**
     * 設定有無涉及不誠信_細項
     */
    public void setBadFaithItem(String badFaithItem) {
        this.badFaithItem = badFaithItem;
    }

	/**
	 * 取得查詢徵信永續經營相關資訊日期
	 */
	public String getQCesEsgDate() {
		return qCesEsgDate;
	}

	/**
	 * 設定查詢徵信永續經營相關資訊日期
	 */
    public void setQCesEsgDate(String value) {
		this.qCesEsgDate = value;
	}

	/**
	 * 取得是否屬高環境
	 */
	public String getIsHighEnv() {
		return isHighEnv;
	}

	/**
	 * 設定是否屬高環境
	 */
	public void setIsHighEnv(String value) {
		this.isHighEnv = value;
	}

	/**
	 * 取得是否屬高碳排
	 */
	public String getIsHighCarbonEms() {
		return isHighCarbonEms;
	}

	/**
	 * 設定是否屬高碳排
	 */
	public void setIsHighCarbonEms(String value) {
		this.isHighCarbonEms = value;
	}

	/**
	 * 取得是否屬去碳化
	 */
	public String getIsDeCarbonEms() {
		return isDeCarbonEms;
	}

	/**
	 * 設定是否屬去碳化
	 */
	public void setIsDeCarbonEms(String value) {
		this.isDeCarbonEms = value;
	}

	/**
	 * 取得是否屬永續
	 */
	public String getIsSustain() {
		return isSustain;
	}

	/**
	 * 設定是否屬永續
	 */
	public void setIsSustain(String value) {
		this.isSustain = value;
	}

	/**
	 * 取得SinoPac+ ESG 評等 (A+~C, A+最佳)
	 */
	public String getSinopac() {
		return sinopac;
	}

	/**
	 * 設定SinoPac+ ESG 評等 (A+~C, A+最佳)
	 */
	public void setSinopac(String value) {
		this.sinopac = value;
	}

	/**
	 * 取得台灣永續評鑑 (AAA~D, AAA最佳)
	 */
	public String getSustainability() {
		return sustainability;
	}

	/**
	 * 設定台灣永續評鑑 (AAA~D, AAA最佳)
	 */
	public void setSustainability(String value) {
		this.sustainability = value;
	}

	/**
	 * 取得永續說明
	 */
	public String getSustainMemo() {
		return sustainMemo;
	}

	/**
	 * 設定永續說明
	 */
	public void setSustainMemo(String value) {
		this.sustainMemo = value;
	}

	/**
	 * 取得查詢SBT狀態日期
	 */
	public String getQEsgSbtiDate() {
		return qEsgSbtiDate;
	}

	/**
	 * 設定查詢SBT狀態日期
	 */
	public void setQEsgSbtiDate(String value) {
		this.qEsgSbtiDate = value;
	}

	/**
	 * 取得SBT狀態
	 */
	public String getSbtiIsCommited() {
		return sbtiIsCommited;
	}

	/**
	 * 設定SBT狀態
	 */
	public void setSbtiIsCommited(String value) {
		this.sbtiIsCommited = value;
	}

	/**
	 * 取得查詢SBT狀態日期
	 */
	public String getSbtiApproveDate() {
		return sbtiApproveDate;
	}

	/**
	 * 設定查詢SBT狀態日期
	 */
	public void setSbtiApproveDate(String value) {
		this.sbtiApproveDate = value;
	}

	/**
	 * 取得查詢永續程度自評結果日期
	 */
	public String getQSustainEvalDate() {
		return qSustainEvalDate;
	}

	/**
	 * 設定查詢永續程度自評結果日期
	 */
	public void setQSustainEvalDate(String value) {
		this.qSustainEvalDate = value;
	}

	/**
	 * 取得經濟活動數量
	 */
	public Integer getSeRec() {
		return this.seRec;
	}
	/**
	 *  設定經濟活動數量
	 **/
	public void setSeRec(Integer value) {
		this.seRec = value;
	}

	/**
	 * 取得經濟活動營收占比
	 */
	public Integer getSeRateSum() {
		return this.seRateSum;
	}
	/**
	 *  設定經濟活動營收占比
	 **/
	public void setSeRateSum(Integer value) {
		this.seRateSum = value;
	}

	/**
	 * 取得符合永續經濟活動認定參考指引之經濟活動數量
	 */
	public Integer getSeRec_Y() {
		return this.seRec_Y;
	}
	/**
	 *  設定符合永續經濟活動認定參考指引之經濟活動數量
	 **/
	public void setSeRec_Y(Integer value) {
		this.seRec_Y = value;
	}

	/**
	 * 取得符合永續經濟活動認定參考指引之經濟活動營收比重
	 */
	public Integer getSeRatePct() {
		return this.seRatePct;
	}
	/**
	 *  設定符合永續經濟活動認定參考指引之經濟活動營收比重
	 **/
	public void setSeRatePct(Integer value) {
		this.seRatePct = value;
	}

	public String getEpsFlag() {
		return epsFlag;
	}

	public void setEpsFlag(String epsFlag) {
		this.epsFlag = epsFlag;
	}

	public String getIndustryNo() {
		return industryNo;
	}

	public void setIndustryNo(String industryNo) {
		this.industryNo = industryNo;
	}

	public String getEnvAndSociRiskLvl() {
		return envAndSociRiskLvl;
	}

	public void setEnvAndSociRiskLvl(String envAndSociRiskLvl) {
		this.envAndSociRiskLvl = envAndSociRiskLvl;
	}
	
}
