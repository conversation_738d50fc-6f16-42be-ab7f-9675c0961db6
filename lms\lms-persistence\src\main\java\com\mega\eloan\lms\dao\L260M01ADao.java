/* 
 * L260M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L260M01A;

/** 貸後管理主檔 **/
//
public interface L260M01ADao extends IGenericDao<L260M01A> {

	L260M01A findByOid(String oid);
	
	L260M01A findByMainId(String mainId);
	
	List<L260M01A> findByDocStatus(String docStatus);

	List<L260M01A> findByIndex01(String mainId);

	List<L260M01A> findByIndex02(String custId, String dupNo, String cntrNo, String loanNo);
	List<L260M01A> findByIndex03(String custId, String dupNo, String cntrNo,
								 String loanNo, String docStatus, String branchId);

	List<L260M01A> findByIndex04(String ownBrId, String custId, String dupNo,
								 String cntrNo, String loanNo, String[] docStatusArray);
	
	List<L260M01A> findByNotEqualsDocsStatus(String custId, String dupNo, String cntrNo,
			 String loanNo, String[] docStatus, String branchId);
}