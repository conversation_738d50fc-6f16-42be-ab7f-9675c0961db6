/* 
 * CollTransferServiceImpl.java
 * 
 * Copyright (c) 2011-2011 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import javax.annotation.Resource;

import tw.com.iisi.cap.exception.CapException;

import com.mega.eloan.lms.base.common.MISRows;
import com.mega.eloan.lms.base.service.CollTransferService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;

public class CollTransferServiceImpl implements CollTransferService {

	@Resource
	MisdbBASEService misdbBaseService;

	@Override
	public <T> void upMisToServer(MISRows<T> misRows) throws CapException {
		misdbBaseService.delete(misRows.getKeyMsgFmtParam("MIS"),
				misRows.getKeyValues());
		misdbBaseService.insert(misRows.getMsgFmtParam("MIS"), misRows.getTypes(),
				misRows.getValues());
	}
}
