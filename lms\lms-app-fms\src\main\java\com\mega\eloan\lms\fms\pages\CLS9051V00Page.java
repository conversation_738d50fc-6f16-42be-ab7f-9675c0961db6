package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanButtonItem;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.fms.panels.CLS9051S01Panel;

@Controller
@RequestMapping(path = "/fms/cls9051v00")
public class CLS9051V00Page extends AbstractEloanInnerView {

	public CLS9051V00Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {		
		addToButtonPanel(model, new EloanButtonItem("btnImport", "ui-icon-jcs-21", "匯入excel"));
		addToButtonPanel(model, new EloanButtonItem("btnDelete", "ui-icon-jcs-09", "刪除"));

		renderJsI18N(CLS9051V00Page.class);
		
		setupIPanel(new CLS9051S01Panel(PANEL_ID), model, params);
	}// ;

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/CLS9051V00Page.js" };
	}
}
