/* 
 * L300M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 覆審考核表主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L300M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L300M01A extends Meta implements IDataObject, IDocObject {
	//@Table(name="L300M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"ownBrId","branchId","bgnDate","endDate"}))
	private static final long serialVersionUID = 1L;

//	/** 
//	 * 刪除註記<p/>
//	 * 文件刪除時使用(非立即性刪除)
//	 */
//	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
//	private Timestamp deletedTime;

	/** 分行 **/
	@Size(max=3)
	@Column(name="BRANCHID", length=3, columnDefinition="CHAR(3)")
	private String branchId;

	/** 
	 * 資料起日<p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="BGNDATE", columnDefinition="DATE")
	private Date bgnDate;

	/** 
	 * 資料迄日<p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ENDDATE", columnDefinition="DATE")
	private Date endDate;

	/** 
	 * 考評日期<p/>
	 * Assessment
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ASSDATE", columnDefinition="DATE")
	private Date assDate;

	/** 
	 * 扣分小計<p/>
	 * 評分項目總合
	 */
	@Digits(integer=6, fraction=2, groups = Check.class)
	@Column(name="SUBTOTAL", columnDefinition="DECIMAL(6,2)")
	private BigDecimal subTotal;

	/** 覆審件數-企金 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="LRSNUM", columnDefinition="DECIMAL(5,0)")
	private Integer lrsNum;

	/** 覆審件數-消金 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="CRSNUM", columnDefinition="DECIMAL(5,0)")
	private Integer crsNum;

	/** 覆審總件數 **/
	@Digits(integer=6, fraction=0, groups = Check.class)
	@Column(name="RSNUM", columnDefinition="DECIMAL(6,0)")
	private Integer rsNum;

	/** 
	 * 平均扣分值<p/>
	 * 扣分小計 /覆審總件數<br/>subTotal / rsNum
	 */
	@Digits(integer=15, fraction=4, groups = Check.class)
	@Column(name="AVGSCORE", columnDefinition="DECIMAL(19,4)")
	private BigDecimal avgScore;

	/** 
	 * 換算後實際得分<p/>
	 * 產生排名表後自動計算
	 */
	@Digits(integer=16, fraction=4, groups = Check.class)
	@Column(name="REALSCORE", columnDefinition="DECIMAL(20,4)")
	private BigDecimal realScore;

	/** 
	 * 總評分<p/>
	 * 產生排名表後自動計算
	 */
	@Digits(integer=16, fraction=4, groups = Check.class)
	@Column(name="TOTALSCORE", columnDefinition="DECIMAL(20,4)")
	private BigDecimal totalScore;
	
	/** 
	 * 是否已產製排名表<p/>
	 * Y:已產製 　N:未產製
	 */
	@Size(max=1)
	@Column(name="PRODUCEFLAG", length=1, columnDefinition="CHAR(1)")
	private String produceFlag;

	/**
	 * 考核表版本
	 */
	@Column(name = "PAVER", length = 15, columnDefinition = "VARCHAR(15)")
	private String paVer;

	/** 取得分行 **/
	public String getBranchId() {
		return this.branchId;
	}
	/** 設定分行 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/** 
	 * 取得資料起日<p/>
	 * YYYY-MM-DD
	 */
	public Date getBgnDate() {
		return this.bgnDate;
	}
	/**
	 *  設定資料起日<p/>
	 *  YYYY-MM-DD
	 **/
	public void setBgnDate(Date value) {
		this.bgnDate = value;
	}

	/** 
	 * 取得資料迄日<p/>
	 * YYYY-MM-DD
	 */
	public Date getEndDate() {
		return this.endDate;
	}
	/**
	 *  設定資料迄日<p/>
	 *  YYYY-MM-DD
	 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}
	
	/** 
	 * 取得考評日期<p/>
	 * Assessment
	 */
	public Date getAssDate() {
		return this.assDate;
	}
	/**
	 *  設定考評日期<p/>
	 *  Assessment
	 **/
	public void setAssDate(Date value) {
		this.assDate = value;
	}

	/** 
	 * 取得扣分小計<p/>
	 * 評分項目總合
	 */
	public BigDecimal getSubTotal() {
		return this.subTotal;
	}
	/**
	 *  設定扣分小計<p/>
	 *  評分項目總合
	 **/
	public void setSubTotal(BigDecimal value) {
		this.subTotal = value;
	}

	/** 取得覆審件數-企金 **/
	public Integer getLrsNum() {
		return this.lrsNum;
	}
	/** 設定覆審件數-企金 **/
	public void setLrsNum(Integer value) {
		this.lrsNum = value;
	}

	/** 取得覆審件數-消金 **/
	public Integer getCrsNum() {
		return this.crsNum;
	}
	/** 設定覆審件數-消金 **/
	public void setCrsNum(Integer value) {
		this.crsNum = value;
	}

	/** 取得覆審總件數 **/
	public Integer getRsNum() {
		return this.rsNum;
	}
	/** 設定覆審總件數 **/
	public void setRsNum(Integer value) {
		this.rsNum = value;
	}

	/** 
	 * 取得平均扣分值<p/>
	 * 扣分小計 /覆審總件數<br/>subTotal / rsNum
	 */
	public BigDecimal getAvgScore() {
		return this.avgScore;
	}
	/**
	 *  設定平均扣分值<p/>
	 *  扣分小計 /覆審總件數<br/>subTotal / rsNum
	 **/
	public void setAvgScore(BigDecimal value) {
		this.avgScore = value;
	}

	/** 
	 * 取得換算後實際得分<p/>
	 * 產生排名表後自動計算
	 */
	public BigDecimal getRealScore() {
		return this.realScore;
	}
	/**
	 *  設定換算後實際得分<p/>
	 *  產生排名表後自動計算
	 **/
	public void setRealScore(BigDecimal value) {
		this.realScore = value;
	}

	/** 
	 * 取得總評分<p/>
	 * 產生排名表後自動計算
	 */
	public BigDecimal getTotalScore() {
		return this.totalScore;
	}
	/**
	 *  設定總評分<p/>
	 *  產生排名表後自動計算
	 **/
	public void setTotalScore(BigDecimal value) {
		this.totalScore = value;
	}

	/** 
	 * 取得是否已產製排名表<p/>
	 * Y:已產製 　N:未產製
	 */
	public String getProduceFlag() {
		return this.produceFlag;
	}
	/**
	 *  設定是否已產製排名表<p/>
	 *  Y:已產製 　N:未產製
	 **/
	public void setProduceFlag(String value) {
		this.produceFlag = value;
	}

	/**
	 * 取得考核表版本<p/>
	 */
	public String getPaVer() {
		return this.paVer;
	}
	/**
	 *  設定考核表版本<p/>
	 **/
	public void setPaVer(String value) {
		this.paVer = value;
	}
}
