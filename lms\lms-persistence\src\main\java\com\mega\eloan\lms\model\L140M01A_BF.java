/* 
 * L140M01A_BF.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 額度明細表(變動前) **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M01A_BF", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L140M01A_BF extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 */
	@Size(max = 1)
	@Column(name = "HEADITEM1_BF", length = 1, columnDefinition = "CHAR(1)")
	private String HeadItem1_BF;

	/** 現請額度幣別 **/
	@Size(max = 3)
	@Column(name = "APLCURR_BF", length = 3, columnDefinition = "CHAR(3)")
	private String APLCurr_BF;

	/**
	 * 現請額度
	 * <p/>
	 * ※2013-1-15 變更欄位大小為<br/>
	 * DECIMAL(17,2)
	 */
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "CURRENTAPPLY_BF", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal CurrentApply_BF;

	/**
	 * 循環/不循環
	 * <p/>
	 * 1.不循環使用<br/>
	 * 2.循環使用
	 */
	@Size(max = 1)
	@Column(name = "REUSE_BF", length = 1, columnDefinition = "CHAR(1)")
	private String ReUse_BF;

	/** 保證成數 **/
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "GUTPERCENT_BF", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal GutPercent_BF;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 */
	public String getHeadItem1_BF() {
		return this.HeadItem1_BF;
	}

	/**
	 * 設定本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 **/
	public void setHeadItem1_BF(String value) {
		this.HeadItem1_BF = value;
	}

	/** 取得現請額度幣別 **/
	public String getAPLCurr_BF() {
		return this.APLCurr_BF;
	}

	/** 設定現請額度幣別 **/
	public void setAPLCurr_BF(String value) {
		this.APLCurr_BF = value;
	}

	/**
	 * 取得現請額度
	 * <p/>
	 * ※2013-1-15 變更欄位大小為<br/>
	 * DECIMAL(17,2)
	 */
	public BigDecimal getCurrentApply_BF() {
		return this.CurrentApply_BF;
	}

	/**
	 * 設定現請額度
	 * <p/>
	 * ※2013-1-15 變更欄位大小為<br/>
	 * DECIMAL(17,2)
	 **/
	public void setCurrentApply_BF(BigDecimal value) {
		this.CurrentApply_BF = value;
	}

	/**
	 * 取得循環/不循環
	 * <p/>
	 * 1.不循環使用<br/>
	 * 2.循環使用
	 */
	public String getReUse_BF() {
		return this.ReUse_BF;
	}

	/**
	 * 設定循環/不循環
	 * <p/>
	 * 1.不循環使用<br/>
	 * 2.循環使用
	 **/
	public void setReUse_BF(String value) {
		this.ReUse_BF = value;
	}

	/** 取得保證成數 **/
	public BigDecimal getGutPercent_BF() {
		return this.GutPercent_BF;
	}

	/** 設定保證成數 **/
	public void setGutPercent_BF(BigDecimal value) {
		this.GutPercent_BF = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
