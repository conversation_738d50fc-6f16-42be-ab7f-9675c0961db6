package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisCntrNoDataService;

@Service
public class MisCntrNoDataServiceImpl extends AbstractMFAloanJdbc implements
		MisCntrNoDataService {
	@Override
	public List<Map<String, Object>> getCntrNoData(String table[], String CntrNo) {
		String[] CntrNos = new String[table.length];
		for (int i = 0; i < CntrNos.length; i++) {
			CntrNos[i] = CntrNo;
		}
		return getJdbc().queryForList("MIS.CntrNoData.selByCntrNo", CntrNos, 0,
				2000);
	}
	public List<Map<String, Object>> getcustIdData(String table[],String custId,
			String dupNo){
		int custId11=3;//使用custId Char(11)的TABLE數量
		int custId10=table.length-custId11;
		String[] custIds = new String[custId10*2+custId11];
		for (int i = 0; i <custId10*2; i=i+2) {
			custIds[i] = custId;
			custIds[i+1]=dupNo;
		}
		for(int i=0;i<custId11;i++){
			custIds[custId10*2+i] = custId+dupNo;
		}
		
		return this.getJdbc().queryForList("MIS.custIdData.selBycustIddupNo",
				custIds);
	}
	@Override
	public void MisUpdatedata(String table, String OldCntrNo, String NewCntrNo) {
		getJdbc().update("MIS." + table + ".Update",
				new String[] { NewCntrNo, OldCntrNo });
	}
	public void MisUpdatedata(String table,String OldCustId,String OldDupNo,
			String NewCustId,String NewDupNo) {
		if(Util.equals(table, "LNF010")||Util.equals(table, "LNF130")||Util.equals(table,"LNF164")){
			getJdbc().update("MIS." + table + ".UpdateByCustId",
					new String[] { NewCustId+NewDupNo,OldCustId+OldDupNo});
		}else{
			getJdbc().update("MIS." + table + ".UpdateByCustId",
					new String[] { NewCustId, NewDupNo,OldCustId,OldDupNo});
		}
	}
}
