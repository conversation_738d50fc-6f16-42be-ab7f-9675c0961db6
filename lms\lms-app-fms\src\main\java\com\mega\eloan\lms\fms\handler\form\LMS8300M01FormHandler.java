package com.mega.eloan.lms.fms.handler.form;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.gwclient.IdentificationCheckGwClient;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.dao.C101S01ADao;
import com.mega.eloan.lms.fms.pages.LMS8300M01Page;
import com.mega.eloan.lms.fms.service.LMS8300Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.mfaloan.service.impl.LNLNF013ServiceImpl;
import com.mega.eloan.lms.model.L830M01A;
import com.mega.eloan.lms.model.L830M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 客戶帳戶管理員維護作業
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms8300m01formhandler")
//@DomainClass(L830M01A.class)
public class LMS8300M01FormHandler extends AbstractFormHandler {
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	LMS8300Service lms8300Service;
	
	@Resource
	BranchService branchService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	RPAProcessService rpaProcessService;
	
	@Resource
	IdentificationCheckGwClient identificationCheckGwClient;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	LNLNF013ServiceImpl lnLNF013ServiceImpl;
	
	@Resource
	C101S01ADao c101s01aDao;
	
	Properties prop;
	/**
	 * 新增L830M01B、L830M01A
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveAction(PageParameters params)
			throws CapException {
		String newAOid = Util.trim(params.getString("newAOid"));
		boolean isNew = params.getAsBoolean("isNew");
		String mainId = Util.trim(params.getString("mainId"));
		String actionType = Util.trim(params.getString("actionType"));
		List<String> datasList = Arrays.asList(params.getStringArray("rows"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		//資料檢查
		List<String> errMsg_List = new ArrayList<String>();
		errMsg_List = this.dataCheck(newAOid);
		if(errMsg_List != null && errMsg_List.size()>0){//有錯誤訊息
			String errorMessage = "";
			for (int i = 0; i < errMsg_List.size(); i++) {
				errorMessage = errorMessage + errMsg_List.get(i) + "<br>";
			}
			result.set("errorMessage",errorMessage);
			result.set("Success", false);
		}else{ //沒有錯誤才往下做
			CreditDocStatusEnum docstatus = CreditDocStatusEnum.海外_編製中;
			if(actionType.equals("send")){ //無錯誤的情況才可送主管!!否則僅儲存並留於編制中
				docstatus = CreditDocStatusEnum.海外_待覆核;
			}
			if(isNew){ //點[單筆維護]、[批次維護]產生之資料，基本上為[新增]
				String new_mainId = IDGenerator.getUUID();
				String LNF013_STAFF_NO = "";
				//根據rows，產生多筆L830M01A明細資料
				if (!Util.isEmpty(datasList) && datasList.size() > 0) {
					for (String data : datasList) {
						String[] eachdata = data.split(";",-1);
						int cnt = (eachdata == null ? 0 : eachdata.length);
						if (cnt == 3) {
							//單筆維護理論上只會有一筆資料，但安全起見還是拿LNF013搜到的值來存取
							//原帳戶管理員
							LNF013_STAFF_NO = cnt > 0 ? eachdata[0] : "";
							//客戶ID
							String LNF013_CUST_ID = cnt > 1 ? eachdata[1] : "";
							String CUST_ID = LNF013_CUST_ID.substring(0, LNF013_CUST_ID.length()-1);
							String DUPNO = LNF013_CUST_ID.substring(LNF013_CUST_ID.length()-1);
							//客戶名稱
							String CNAME = cnt > 1 ? eachdata[2] : "";
							
							createL830M01A(params, new_mainId, LNF013_CUST_ID, CUST_ID, DUPNO, LNF013_STAFF_NO, CNAME, newAOid, docstatus);
						}
					}
				}
				//產生一筆L830M01B主建資料
				createL830M01B(params, new_mainId, LNF013_STAFF_NO, newAOid, docstatus);
				
			}else{
				//已存在的資料維護
				//重新產生L830M01A
				List<L830M01A> l830m01aList = (List<L830M01A>) lms8300Service.findListByMainId(L830M01A.class, mainId);
				lms8300Service.deleteL830m01aList(l830m01aList); //先清空原資料，再重新INSERT
				
				String LNF013_STAFF_NO = "";
				//根據rows，產生多筆L830M01A明細資料
				if (!Util.isEmpty(datasList) && datasList.size() > 0) {
					for (String data : datasList) {
						String[] eachdata = data.split(";",-1);
						int cnt = (eachdata == null ? 0 : eachdata.length);
						if (cnt == 3) {
							//單筆維護理論上只會有一筆資料，但安全起見還是拿LNF013搜到的值來存取
							//原帳戶管理員
							LNF013_STAFF_NO = cnt > 0 ? eachdata[0] : "";
							//客戶ID
							String LNF013_CUST_ID = cnt > 1 ? eachdata[1] : "";
							String CUST_ID = LNF013_CUST_ID.substring(1, LNF013_CUST_ID.length()-1);
							String DUPNO = LNF013_CUST_ID.substring(LNF013_CUST_ID.length()-1);
							//客戶名稱
							String CNAME = cnt > 1 ? eachdata[2] : "";
							
							createL830M01A(params, mainId, LNF013_CUST_ID, CUST_ID, DUPNO, LNF013_STAFF_NO, CNAME, newAOid, docstatus);
						}
					}
				}
				
				//更新L830M01B
				List<L830M01B> l830m01bList = (List<L830M01B>) lms8300Service.findListByMainId(L830M01B.class, mainId);
				if(l830m01bList != null && l830m01bList.size()>0){ //L830M01B正常只會有一筆
					for(int i=0;i<l830m01bList.size();i++){ //
						L830M01B l830m01b = l830m01bList.get(i);
						l830m01b.setUpdater(params.getString("_userId"));
						l830m01b.setUpdateTime(CapDate.getCurrentTimestamp());
						l830m01b.setOrigAOid(LNF013_STAFF_NO);
						l830m01b.setNewAOid(newAOid);
						l830m01b.setDocStatus(docstatus);
						//save
						lms8300Service.save(l830m01b);
					}
				}
			}
			result.set("Success", true);
		}
		return result;
	}
	
	private void createL830M01A(PageParameters params, String mainId, String LNF013_CUST_ID, String custId, String dupNo, String origAOId, String custName, String newAOid, CreditDocStatusEnum docstatus)
			throws NumberFormatException, CapMessageException { //L830M01A明細資料
		L830M01A l830m01a = new L830M01A();
		l830m01a.setMainId(mainId);
		l830m01a.setCustId(custId);
		l830m01a.setCustName(custName); //A-LOAN資料無法取得客戶名稱
		l830m01a.setDupNo(dupNo);
		l830m01a.setALcustId(LNF013_CUST_ID);
		l830m01a.setOwnBrId(MegaSSOSecurityContext.getUnitNo());
		l830m01a.setDocStatus(docstatus);
		l830m01a.setCreator(params.getString("_userId"));
		l830m01a.setCreateTime(CapDate.getCurrentTimestamp());
		l830m01a.setOrig_AOId(origAOId);
		l830m01a.setAOId(newAOid);
		
		//save
		lms8300Service.save(l830m01a);
	}
	
	private void createL830M01B(PageParameters params, String mainId, String origAOId, String newAOid, CreditDocStatusEnum docstatus)
			throws NumberFormatException, CapMessageException { //L830M01B主建資料
		L830M01B l830m01b = new L830M01B();
		l830m01b.setMainId(mainId);
		l830m01b.setOwnBrId(MegaSSOSecurityContext.getUnitNo());
		l830m01b.setDocStatus(docstatus);
		
		String maintainType = Util.trim(params.getString("maintainType"));
		l830m01b.setMaintainType(maintainType);
		if(maintainType.equals("S")){
			l830m01b.setFilterCustId(Util.trim(params.getString("custId")));
			l830m01b.setFilterDupNo(Util.trim(params.getString("dupNo")));
			l830m01b.setFilterCustName(Util.trim(params.getString("custName")));
		}else if(maintainType.equals("B")){
			l830m01b.setFilterAOid(Util.trim(params.getString("origAOId")));
		}
		l830m01b.setCreator(params.getString("_userId"));
		l830m01b.setCreateTime(CapDate.getCurrentTimestamp());
		l830m01b.setUpdater(params.getString("_userId"));
		l830m01b.setUpdateTime(CapDate.getCurrentTimestamp());
		l830m01b.setOrigAOid(origAOId);
		l830m01b.setNewAOid(newAOid);
		
		//save
		lms8300Service.save(l830m01b);
	}
	
	/**
	 * 資料檢查
	 * @throws CapException 
	 */
	private ArrayList<String> dataCheck(String newAOid) throws CapException{
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean MISAMFFlag = clsService.is_function_on_codetype("J-110-0330_MISAMF");
		String brNo = user.getUnitNo();
		boolean isNot943 = (brNo.equals("943")) ? false : true;
		ArrayList<String> errMsg_List = new ArrayList<String>();
		if (MISAMFFlag && isNot943) {
			//檢查所選之新帳戶管理員，是否已於C470建檔
			String MISAML_errmsg = run_check_C470(newAOid, MegaSSOSecurityContext.getUnitNo());
			if(MISAML_errmsg != null){ //MISAML無對應的經辦資料
				errMsg_List.add(MISAML_errmsg);
			}
		}
		return errMsg_List;
	}
	
	
	//檢核操作經辦已於該分行之C470(MIS.MISAMF)建檔
	private String run_check_C470(String AOId, String brno) throws CapException {
		String FindAOid = AOId;
		if(FindAOid.length() == 6){ //MISAMF的行員編號AM_EMP_NO只有五碼
			FindAOid = AOId.substring(1, 6);
		}
		String toDay = CapDate.formatDate(new Date(),"yyyy-MM-dd");
		List<Map<String, Object>> overdueList = misdbBASEService.getMISAMF_byCustidBrno(FindAOid, brno, toDay);
		if(overdueList == null || overdueList.size() == 0){
			//請先至BTT系統執行交易代號C470維護XXX，消金帳戶管理員資訊。
			ElsUser elUser = userInfoService.getUser(AOId);
			String cusid_name = AOId;
			if(elUser != null){
				cusid_name = cusid_name+" "+elUser.getUserName();
			}
			String tMsg = MessageFormat.format(this.getI18nMsg("misaml.error"), cusid_name);
			return tMsg;
		}
		return null;
	}
	
	/**
	 * 取得對應訊息
	 * 
	 * @param key
	 * @return
	 */
	private String getI18nMsg(String key) {
		String result = null;
		if (prop == null)
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS8300M01Page.class);
		if (prop != null) {
			result = prop.getProperty(Util.trim(key));
		}
		return Util.trim(result);
	}
	
	
	/**
	 * 行員查詢-只查該分行經辦
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException.
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify + AuthType.Query)
	public IResult getMegaEmpInfo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> bossListAO = new HashMap<String, String>();
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管,SignEnum.甲級主管, SignEnum.乙級主管, SignEnum.經辦人員 };
		bossListAO = userInfoService.findByBrnoAndSignId(
				MegaSSOSecurityContext.getUnitNo(), signs);
		result.set("bossListAO", new CapAjaxFormResult(bossListAO));
		result.set("Success", true);

		return result;
	}
	
	
	/**
	 * 刪除L830M01A、L830M01B 
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL830m01b_a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("mainId");
			
		//清除L830M01A
		List<L830M01A> l830m01aList = (List<L830M01A>) lms8300Service.findListByMainId(L830M01A.class, mainId);
		lms8300Service.deleteL830m01aList(l830m01aList); //先清空原資料，再重新INSERT
		//清除L830M01B
		List<L830M01B> l830m01bList = (List<L830M01B>) lms8300Service.findListByMainId(L830M01B.class, mainId);
		lms8300Service.deleteL830m01bList(l830m01bList); //先清空原資料，再重新INSERT

		return result;
	}
	
	/**
	 * 
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getViewData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("mainId");
		//取得L830M01B的基本資料
		List<L830M01B> l830m01bList = (List<L830M01B>) lms8300Service.findListByMainId(L830M01B.class, mainId);
		if(l830m01bList != null && l830m01bList.size()>0){ //原則上只有一筆
			L830M01B l830m01b = l830m01bList.get(0);
			String maintainType = Util.trim(l830m01b.getMaintainType());
			result.set("maintainType",maintainType); //維護項目
			result.set("mainId",params.getString("mainId")); //維護項目
			result.set("isNew",false); //是否為新增
			result.set("docstatus",l830m01b.getDocStatus()); //案件狀態
			if(maintainType.equals("S")){
				result.set("custId",l830m01b.getFilterCustId()); //篩選條件-客戶ID
				result.set("dupNo",l830m01b.getFilterDupNo()); //篩選條件-檢查碼
				result.set("custName",l830m01b.getFilterCustName()); //篩選條件-客戶名稱
				result.set("singleNewAOid",l830m01b.getNewAOid()); //新帳戶管理員
				
				//因前台搜尋用的變數有點不一樣，多塞一組避免自動查詢撈不到
				result.set("custid",l830m01b.getFilterCustId()); //篩選條件-客戶ID
				result.set("dupno",l830m01b.getFilterDupNo()); //篩選條件-檢查碼
			}else if(maintainType.equals("B")){
				result.set("origAOid",l830m01b.getFilterAOid()); //篩選條件-帳戶管理員
				result.set("batchNewAOid",l830m01b.getNewAOid()); //新帳戶管理員
			}
			
		}
		return result;
	}
	
	/**
	 * 覆核
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult approveAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("mainId");
		String checked = Util.trim(params.getString("checked"));
		String brNo = MegaSSOSecurityContext.getUnitNo();
		Properties prop_lms8300m01 = MessageBundleScriptCreator.getComponentResource(LMS8300M01Page.class);
		if(checked.equals("0")){ //核准
			List<L830M01A> l830m01aList = (List<L830M01A>) lms8300Service.findListByMainId(L830M01A.class, mainId);
			if(l830m01aList != null && l830m01aList.size()>0){ //L830M01A可能會有多筆
				for(int i=0;i<l830m01aList.size();i++){
					L830M01A l830m01a = l830m01aList.get(i);
					//UPDATE A-LOAN資料
					int updateData = lms8300Service.gfnUpdateLNF013(l830m01a, brNo, params.getString("_userId"));
					logger.trace("J-111-0228 UPDATE LNF013 FOR UPDATE :{"
							+ updateData + "}");
					
					//更新E-LOAN資料
					l830m01a.setDocStatus(CreditDocStatusEnum.海外_已核准);
					l830m01a.setApprover(params.getString("_userId"));
					if(updateData == 0){ //沒更新資料
						l830m01a.setMemo(prop_lms8300m01.getProperty("L830M01A.updateMemo"));
					}
					lms8300Service.save(l830m01a);
				}
			}
			List<L830M01B> l830m01bList = (List<L830M01B>) lms8300Service.findListByMainId(L830M01B.class, mainId);
			if(l830m01bList != null && l830m01bList.size()>0){ //原則上只有一筆
				L830M01B l830m01b = l830m01bList.get(0);
				l830m01b.setDocStatus(CreditDocStatusEnum.海外_已核准);
				l830m01b.setApprover(params.getString("_userId"));
				lms8300Service.save(l830m01b);
			}
		}else if(checked.equals("1")){ //退回經辦修改
			List<L830M01B> l830m01bList = (List<L830M01B>) lms8300Service.findListByMainId(L830M01B.class, mainId);
			if(l830m01bList != null && l830m01bList.size()>0){ //原則上只有一筆
				L830M01B l830m01b = l830m01bList.get(0);
				l830m01b.setDocStatus(CreditDocStatusEnum.海外_編製中);
				lms8300Service.save(l830m01b);
			}
			
			List<L830M01A> l830m01aList = (List<L830M01A>) lms8300Service.findListByMainId(L830M01A.class, mainId);
			if(l830m01aList != null && l830m01aList.size()>0){ //L830M01A可能會有多筆
				for(int i=0;i<l830m01aList.size();i++){
					L830M01A l830m01a = l830m01aList.get(i);
					l830m01a.setDocStatus(CreditDocStatusEnum.海外_編製中);
					lms8300Service.save(l830m01a);
				}
			}

		}
		result.set("Success", true);
		return result;
	}
}
