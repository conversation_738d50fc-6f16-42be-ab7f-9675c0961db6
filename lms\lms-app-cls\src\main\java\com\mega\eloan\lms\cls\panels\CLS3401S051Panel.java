
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.C340M01A;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 消金線上對保契約書
 * </pre>
 * 
 * @since 2020/11/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/11/26,EL08034,new
 *          </ul>
 */
public class CLS3401S051Panel extends Panel {

	private C340M01A meta;

	public CLS3401S051Panel(String id) {
		super(id);
	}

	public CLS3401S051Panel(String id, boolean updatePanelName, C340M01A meta) {
		super(id, updatePanelName);
		this.meta = meta;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new DocLogPanel("_docLog").processPanelData(model, params);

		boolean is_send_ploan = (meta!=null && Util.equals(CreditDocStatusEnum.海外_已核准.getCode(), meta.getDocStatus() ));
		model.addAttribute("showPloanColumn", is_send_ploan);
	}

	/**/
	private static final long serialVersionUID = 1L;
}
