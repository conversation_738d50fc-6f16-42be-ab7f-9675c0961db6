/* 
 * LMS1201V11Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.GridViewFilterPanel01;

/**
 * <pre>
 * 授信簽報書異常通報案件
 * </pre>
 * 
 * @since 2012/11/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/29,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1201v11")
public class LMS1201V11Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_已核准);

		addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Filter);

		// 套用哪個i18N檔案
		renderJsI18N(LMS1201V01Page.class);
		
		model.addAttribute("loadScript", "loadScript('pagejs/lns/LMS1201V01Page');");
		setupIPanel(new GridViewFilterPanel01(PANEL_ID), model, params);
	}// ;

}
