package com.mega.eloan.lms.fms.pages;

import java.util.ArrayList;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * 
 * 貸後管理作業 - 歷史紀錄
 * 
 */
@Controller@RequestMapping(path = "/fms/lms8000v06")
public class LMS8000V06Page extends AbstractEloanInnerView {

	public LMS8000V06Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.已覆核案件退回紀錄文件狀態);

		// 加上Button
		ArrayList<Object> btns = new ArrayList<Object>();
		// 主管跟經辦都會出現的按鈕
		addToButtonPanel(model, LmsButtonEnum.Filter, LmsButtonEnum.View);

		renderJsI18N(LMS8000V01Page.class);
	}


	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS8000V01Page.js" };
	}
}