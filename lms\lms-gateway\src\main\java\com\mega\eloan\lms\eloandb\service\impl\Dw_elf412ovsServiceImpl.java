package com.mega.eloan.lms.eloandb.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.TWNDate;

import com.mega.eloan.lms.eloandb.service.Dw_elf412ovsService;

@Service
public class Dw_elf412ovsServiceImpl extends AbstractEloandbJdbc implements
		Dw_elf412ovsService {

	
	@Override
	public Map<String, Object> findDwElf412ovsMaincust(String custId,
			String dupNo, String brNo) {

		return this.getJdbc().queryForMap(
				"DW_ELF412OVS.FindMaincustByCustIdAndDupNo",
				new Object[] { brNo, custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findDwElf412ovsByBranch(String branch,
			Date dataDate) {
		
		return this.getJdbc().queryForList("ELF412OVS.selBybranch",
				new Object[] { branch, TWNDate.toAD(dataDate), branch, branch });
	}

}
