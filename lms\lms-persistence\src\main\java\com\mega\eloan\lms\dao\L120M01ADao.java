package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

import com.mega.eloan.lms.model.L120M01A;

/** 授信簽報書主檔 **/
public interface L120M01ADao extends IGenericDao<L120M01A> {

	L120M01A findByOid(String oid);

	L120M01A findByMainId(String mainId);

	List<Object[]> findMaxCaseSeq();

	List<L120M01A> findMaxCaseSeq2();

	List<L120M01A> findByOids(String[] oids);

	List<L120M01A> findByCustIdDupId(String custId, String DupNo);

	/**
	 * 取得核准文號
	 * 
	 * @return ENDDATE 核准日期 ,MAX SEQ 最大的seq
	 */
	List<Object[]> findMaxEndDateSeq();

	/**
	 * 全文檢索
	 * 
	 * @param userId
	 * @param groupId
	 * @param caseDateS
	 * @param caseDateE
	 * @param endDateS
	 * @param endDateE
	 * @param typCd
	 * @param docType
	 * @param docKind
	 * @param docCode
	 * @param updaterName
	 * @param updater
	 * @param caseBrId
	 * @param custId
	 * @param docRslt
	 * @param docStatus
	 * @param lnSubject
	 * @param rateText
	 * @param otherCondition
	 * @param reportOther
	 * @param reportReason1
	 * @param reportReason2
	 * @param areaOption
	 * @param Collateral
	 * @param custName
	 * @param busCode
	 * @return
	 */
	public List<Object[]> findFullTextSearch(String fxUserId, String fxGroupId,
			String fxCaseDateName, String fxCaseDateS, String fxCaseDateE,
			String fxEndDateS, String fxEndDateE, String fxTypCd,
			String fxDocType, String fxDocKind, String fxDocCode,
			String fxUpdaterName, String fxUpdater, String fxCaseBrId,
			String fxCustId, String fxDocRslt, String fxDocStatus,
			String fxLnSubject, String fxRateText, String fxOtherCondition,
			String fxReportOther, String fxReportReason1,
			String fxReportReason2, String fxAreaOption, String fxCollateral,
			String fxCustName, String fxBusCode, String fxCaseLvl);

	/**
	 * 
	 * @param userId
	 * @return
	 */
	public Page<L120M01A> findL120m01aListByL120m01atmp1UserIdForSearch(
			ISearch search, String userId);

	/**
	 * 透過案件號碼-年度/案件號碼-分行/案件號碼-流水號找出簽報書
	 * 
	 * @param caseYear
	 * @param caseBrid
	 * @param caseSeq
	 * @return
	 */
	public L120M01A findBycaseYearBridSeq(Integer caseYear, String caseBrid,
			Integer caseSeq);

	/**
	 * 依照APPROVETIME 找期間核准的簽報書
	 * 
	 * @param docstatus
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<L120M01A> findByDocStatusAndApproveTime(String[] docstatus,
			String bgnDate, String endDate);

}