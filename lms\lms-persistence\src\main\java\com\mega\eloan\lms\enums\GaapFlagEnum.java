/* 
 * GaapFlag.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.enums;

/**
 * <pre>
 * 會計準則 enum。
 * </pre>
 * 
 * @since 2011/8/4
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/8/4,Sunkist Wang,new</li>
 *          </ul>
 */
public enum GaapFlagEnum {
    /**
     * 0 GAAP
     */
    GAAP("0"),
    /**
     * 1 IFRS
     */
    IFRS("1"),
    /**
     * 2 EAS
     * J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
     */
    EAS("2");

    private String code;

    GaapFlagEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public boolean isEquals(Object other) {
        if (other instanceof String) {
            return code.equals(other);
        } else {
            return super.equals(other);
        }
    }

    public static GaapFlagEnum getEnum(String code) {
        for (GaapFlagEnum enums : GaapFlagEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
