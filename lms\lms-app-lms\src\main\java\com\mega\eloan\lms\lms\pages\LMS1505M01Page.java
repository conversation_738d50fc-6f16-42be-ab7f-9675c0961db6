/* 
 * LMS1505M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.lms.panels.LMS1505S01Panel;
import com.mega.eloan.lms.lms.panels.LMS1505S02Panel;
import com.mega.eloan.lms.model.L150M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 小放會會議記錄
 * </pre>
 * 
 * @since 2011/10/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1505m01/{page}")
public class LMS1505M01Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		renderJsI18N(AbstractEloanPage.class);
		renderJsI18N(LMS1505M01Page.class);
		
		// 依權限設定button
		addAclLabel(model,
				new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));

		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page, model);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
	}// ;

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index, ModelMap model) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS1505S01Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new LMS1505S02Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS1505S02Panel(TAB_CTX, true);
			break;
		}
		if (panel == null) {
			panel = new Panel(TAB_CTX, true);
		}

		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L150M01A.class;

	}

}
