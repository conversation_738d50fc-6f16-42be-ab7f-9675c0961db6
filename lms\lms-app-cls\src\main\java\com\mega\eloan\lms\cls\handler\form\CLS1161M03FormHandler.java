package com.mega.eloan.lms.cls.handler.form;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.gwclient.EloanBatchClient;
import com.mega.eloan.common.gwclient.EloanSubsysBatReqMessage;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.model.C160M03A;
import com.mega.eloan.lms.model.C160S03A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 整批自動開戶FormHandler
 * </pre>
 * 
 * @since 2017/03/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/03/05,EL08034
 *          </ul>
 */
@Scope("request")
@Controller("cls1161m03formhandler")
@DomainClass(C160M03A.class)
public class CLS1161M03FormHandler extends AbstractFormHandler {

	private static final Logger logger = LoggerFactory
			.getLogger(CLS1161M03FormHandler.class);

	@Resource
	CLS1161Service cls1161Service;

	@Resource
	DocFileService docFileService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	NumberService numberService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	ICustomerService iCustomerService;
	

	@Resource
	EloanBatchClient eloanBatchClient;
	
	Properties prop_AbstractEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
	
	/**
	 * 新增案件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addCase(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String txCode = params.getString(EloanConstants.TRANSACTION_CODE);
		
		String idDupCntrNo = Util.trim(params.getString("idDupCntrNo"));
		if(Util.isEmpty(idDupCntrNo)){
			throw new CapMessageException("請選取額度", getClass());
		}
		String[] idDupCntrNoArr =StringUtils.split(idDupCntrNo, "_");
		if(idDupCntrNoArr.length!=3){
			throw new CapMessageException("參數錯誤", getClass());
		}
		String custId = Util.trim(idDupCntrNoArr[0]);
		String dupNo = Util.trim(idDupCntrNoArr[1]);
		String custName = custId+"-"+dupNo;
		String cntrNo = Util.trim(idDupCntrNoArr[2]);
		String mainId = IDGenerator.getUUID();
		
		Map<String, Object> latestData = iCustomerService.findByIdDupNo(custId, dupNo);
		if(latestData!=null){			
			custName = Util.trim(latestData.get("CNAME"));
		}

		// 整批自動開戶
		C160M03A c160m03a = new C160M03A();
		c160m03a.setMainId(mainId);
		c160m03a.setUnitType(user.getUnitType());
		c160m03a.setOwnBrId(user.getUnitNo());
		c160m03a.setTypCd(TypCdEnum.DBU.getCode());
		c160m03a.setDocURL("../cls/cls1161m03/01");
		c160m03a.setDocStatus(CLSDocStatusEnum.編製中);
		c160m03a.setCustId(custId);
		c160m03a.setDupNo(dupNo);
		c160m03a.setCustName(custName);
		c160m03a.setRandomCode(IDGenerator.getRandomCode());
		c160m03a.setTxCode(txCode);
		c160m03a.setDeletedTime(null);
		c160m03a.setCntrNo(cntrNo);
		c160m03a.setPackNo("");
		
		// 儲存
		cls1161Service.save(c160m03a);
		CapAjaxFormResult data = DataParse.toResult(c160m03a);		
		result.set("data", data);
		return result;
	}

	private String get_packNo(String cntrNo) 
	throws CapMessageException{
		
		int maxPackNo = cls1161Service.findC160M03AMaxPackNoByCntrNo(cntrNo);		
		String val = "0000"+(1+maxPackNo);
		return StringUtils.right(val, 4);
	}
	
	/**
	 * 刪除案件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteCase(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = Util.trim(params.getString(EloanConstants.OID));
		C160M03A model = cls1161Service.findModelByOid(C160M03A.class, oid);
		
		if (model != null) {
			List<C160S03A> old_List = cls1161Service.findC160S03AByMainIdOrderBySeqNo(model.getMainId());
			cls1161Service.delete(old_List);
			//=========
			model.setDeletedTime(CapDate.getCurrentTimestamp());
			cls1161Service.save(model);
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult query(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid =  Util.trim(params.getString(EloanConstants.MAIN_OID));
		C160M03A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = cls1161Service.findModelByOid(C160M03A.class, mainOid);
			
			String page = params.getString(EloanConstants.PAGE);		
			if ("01".equals(page)) {				
				{
					LMSUtil.addMetaToResult(result, meta, new String[]{ "ownBrId"
							, "custId", "dupNo", "custName"
							, "cntrNo", "packNo"
							,"randomCode" });
				}				
				
				String ownBrName = Util.trim(branchService.getBranchName(meta.getOwnBrId()));
				result.set("ownBrIdName", ownBrName);				
				result.set("docStatus", _docStatusDesc(meta.getDocStatus()));
				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime", Util.trim(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime",Util.trim(TWNDate.valueOf(meta.getUpdateTime())));

				//=====
				CapAjaxFormResult xlsFrmData = new CapAjaxFormResult();
				xlsFrmData.set("excelId", Util.trim(meta.getExcelId()));
				result.set("xlsFrmData", xlsFrmData);
			} else if ("02".equals(page)) {
				
			}
		}		
		return defaultResult(params, meta, result);
	}
	
	private String _docStatusDesc(String docStatus){
		return Util.trim(prop_AbstractEloanPage.getProperty("docStatus."+docStatus));
	}
	private String _id_name(String raw_id){
		String id = Util.trim(raw_id);		
		if(StringUtils.equalsIgnoreCase(id, "BATCH")){
			return id;
		}else{
			return Util.trim(id+" "+Util.trim(userInfoService.getUserName(id)));	
		}		
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult delC160S03A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		List<C160S03A> old_List = cls1161Service.findC160S03AByMainIdOrderBySeqNo(mainId);
		cls1161Service.delete(old_List);
		return result;
	}
	@DomainAuth(AuthType.Modify)
	public IResult srvImpXlsData(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String excelId = Util.trim(params.getString("excelId"));
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		
		C160M03A meta = cls1161Service.findModelByOid(C160M03A.class, mainOid);
		if(meta==null){
			throw new CapMessageException("mainOid=["+mainOid+"]", getClass());
		}
		DocFile docFile = docFileService.read(excelId);
		if (docFile == null) {
			throw new CapMessageException("請先上傳EXCEL", getClass());
		}
		
		WritableWorkbook workbook = null;
		String filePath = docFileService.getFilePath(docFile);
		String errMsg = "";
		try {
			// UPGRADETODO: Excel套件待處理
			// workbook = Workbook.createWorkbook(new File(filePath),
			// Workbook.getWorkbook(new File(filePath)));
			WritableSheet sheet = workbook.getSheet(0);

			
			if (meta != null) {
				meta.setExcelId(excelId);				
				
				// 取得EXCEL資料
				boolean rtnParse = false;
				int maxSeq = cls1161Service.findC160S03AMaxSeqNoByCntrNo(meta.getCntrNo());
				try{
					rtnParse = cls1161Service.parseC160S03A(sheet, meta, null, maxSeq);	
				}catch(Exception e){
					errMsg = StrUtils.getStackTrace(e);
				}finally{
					if(rtnParse){
						CapAjaxFormResult impXlsDataResultForm = new CapAjaxFormResult();
						impXlsDataResultForm.set("excelId", Util.trim(meta.getExcelId()));
						result.set("impXlsDataResultForm", impXlsDataResultForm);
						
					}else{						
						if(Util.isEmpty(errMsg)){
							errMsg = "parse fail";
						}
					}
				}
			}
			
			workbook.write();
			workbook.close();
		} catch (Exception e) {
			logger.error("parseExcel EXCEPTION!!", e);
			throw new CapMessageException(e, getClass());
		} finally {
			try {
				if (workbook != null) {
					workbook.close();
					workbook = null;
				}
			} catch (Exception e) {
				logger.debug("Workbook close EXCEPTION!", getClass());
			}
		}
	
		if(Util.isNotEmpty(errMsg)){
			throw new CapMessageException(errMsg, getClass());
		}
		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		return _saveAction(params, "Y");
	}

	private CapAjaxFormResult _saveAction(PageParameters params,
			String tempSave) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		
		//===
		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C160M03A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = cls1161Service.findModelByOid(C160M03A.class, mainOid);
			String page = params.getString(EloanConstants.PAGE);
			if ("01".equals(page)) {
				meta.setPackNo(Util.trim(params.getString("packNo")));
			}
			cls1161Service.save(meta);
			
			result.set(KEY, true);
		}
		
		return defaultResult(params, meta, result);
	}	
	
	
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C160M03A meta = null;
		
		if (Util.isNotEmpty(mainOid)) {
			meta = cls1161Service.findModelByOid(C160M03A.class, mainOid);
			List<C160S03A> list = cls1161Service.findC160S03AByMainIdOrderBySeqNo(meta.getMainId());	
			String errMsg = "";
			
			String docStatus = Util.trim(meta.getDocStatus());
			String nextStatus = "";
			DocLogEnum _DocLogEnum = null;
			if(Util.equals(CreditDocStatusEnum.海外_編製中.getCode(), docStatus)){
				nextStatus = CreditDocStatusEnum.海外_待覆核.getCode();
				_DocLogEnum = DocLogEnum.FORWARD;
				
				if(true){					
					if(list.size()==0){
						errMsg = "請匯入至少 1 筆資料";
					}else{
						for(C160S03A bean: list ){
							if(Util.equals("Y", bean.getChkYN())){
								
							}else{
								errMsg = bean.getCustId_s()+"-"+bean.getDupNo_s()+" 資料檢核有誤";
								break;
							}
						}						
					}
					
					if(Util.isEmpty(Util.trim(meta.getPackNo()))){
						errMsg = "請輸入批號";
					}
				}
			}else if(Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), docStatus)){
				//核定、退回
				if(Util.equals("核定", decisionExpr)){					
					if(Util.equals(user.getUserId(), meta.getUpdater())){
						errMsg = RespMsgHelper.getMessage("EFD0053");
					}else{
						nextStatus = CreditDocStatusEnum.海外_已核准.getCode();
						_DocLogEnum = DocLogEnum.ACCEPT;	
					}
				}else if(Util.equals("退回", decisionExpr)){
					nextStatus = CreditDocStatusEnum.海外_編製中.getCode();
					_DocLogEnum = DocLogEnum.BACK;
				}
			}else if(Util.equals(CreditDocStatusEnum.海外_已核准.getCode(), docStatus)){	
				
			}
						
			if(Util.isNotEmpty(errMsg)){				
				throw new CapMessageException(errMsg, getClass());
			}else{
				if(Util.isEmpty(nextStatus)){
					throw new CapMessageException("流程異常["+docStatus+"]", getClass());
				}	
			}
			if(true){
				if(Util.equals(nextStatus, CreditDocStatusEnum.海外_已核准.getCode())){
					meta.setApprover(user.getUserId());
					meta.setApproveTime(CapDate.getCurrentTimestamp());
					//======
					cls1161Service.upELF533(meta, list);
				}else if(Util.equals(nextStatus, CreditDocStatusEnum.海外_編製中.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
				}else if(Util.equals(nextStatus, CreditDocStatusEnum.海外_待覆核.getCode())){
					
				}
				meta.setDocStatus(nextStatus);
				cls1161Service.daoSaveC160M03A(meta);
				//===
				if(_DocLogEnum!=null){
					docLogService.record(meta.getOid(), _DocLogEnum);	
				}				
			}
			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult getNumber(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid =  Util.trim(params.getString(EloanConstants.MAIN_OID));
		C160M03A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = cls1161Service.findModelByOid(C160M03A.class, mainOid);
			
			meta.setPackNo(get_packNo(meta.getCntrNo()));
			cls1161Service.save(meta);
		}		

		return defaultResult(params, meta, result);		
	}
	
	private CapAjaxFormResult defaultResult(PageParameters params, C160M03A meta,
			CapAjaxFormResult result) throws CapException {
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		
		set_titleInfo(result, meta);
		result.set("packNo", Util.trim(meta.getPackNo()));
		return result;
	}
	
	private void set_titleInfo(CapAjaxFormResult result, C160M03A meta){
		result.set("titleInfo", Util.trim(meta.getCustId())
				+"-"+Util.trim(meta.getDupNo())
				+" "+Util.trim(meta.getCntrNo())+"("+Util.trim(meta.getPackNo())+")");
	}


	public IResult doDelApprove(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = Util.trim(params.getString(EloanConstants.OID));
		C160M03A meta = cls1161Service.findModelByOid(C160M03A.class, oid);
		
		if (meta != null) {
			List<C160S03A> c160s03a_List = cls1161Service.findC160S03AByMainIdOrderBySeqNo(meta.getMainId());
			if(cls1161Service.check_can_delElf533(c160s03a_List)){
				//===
				cls1161Service.delElf533(c160s03a_List);
				//===
				meta.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
				meta.setApprover(null);
				meta.setApproveTime(null);
				cls1161Service.daoSaveC160M03A(meta);
			}else{
				throw new CapMessageException("已有資料被 a-loan 引入，不可退回", getClass());
			}			
		}
		return result;
	}
	
	public IResult callBatch(PageParameters params)
			throws CapException, IOException {
		int jq_timeout = Util.parseInt(params.getString("jq_timeout"));
		if (jq_timeout == 0) {
			jq_timeout = 60 * 60;// default
		}
		
		String act = Util.trim(params.getString("act"));
		List<String> paramList = new ArrayList<String>();
		
		if (Util.equals("procC160M03A_xls", act)){
			paramList.add("excelId");			
			paramList.add(EloanConstants.MAIN_OID);
		}
		// ---
		EloanSubsysBatReqMessage esbrm = new EloanSubsysBatReqMessage();
		esbrm.setUrl(SysParamConstants.SYS_URL_LMS);
		esbrm.setReqFormat(EloanSubsysBatReqMessage.REQ_FMT_JSON);
		esbrm.setServiceId("clsCallBatchServiceImpl");
		esbrm.setTimeout(jq_timeout);
		esbrm.setLocalUrl(true);
		
		JSONObject requestJSON = new JSONObject();
		requestJSON.element("act", act);
		for (String k : paramList) {
			requestJSON.element(k, params.getString(k));
		}
		// ---
		esbrm.setRequestJSON(requestJSON);
		
		String respStr = eloanBatchClient.send(esbrm);
		logger.debug("send to batch data={}", respStr);
		// =============
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("r", respStr);
		
		return result;
	}
	
	public IResult fetch_sample_xls(PageParameters params)
			throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String fileOid = "";
		
		if(true){
			String f_mainId = Util.trim(params.getString("f_mainId"));
			String f_fieldId = Util.trim(params.getString("f_fieldId"));
			List<DocFile> list = docFileService.findByIDAndName(f_mainId, f_fieldId, "");
			if(list.size()>0){
				fileOid = list.get(0).getOid();
			}
		}
		if(Util.isNotEmpty(fileOid)){
			result.set("fileOid", fileOid);	
		}		
		
		return result;
	}
}
