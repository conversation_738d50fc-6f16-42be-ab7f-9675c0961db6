/* 
 * LMS2405V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.pages;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.panels.LMS2405FilterPanel;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthType;

@Controller
@RequestMapping("crs/lms2405v01")
public class LMS2405V01Page extends AbstractEloanInnerView {
	
	@Autowired
	RetrialService retrialService;
	
	public LMS2405V01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(RetrialDocStatusEnum.編製中);
		ArrayList<EloanPageFragment> btns = new ArrayList<>();
		if (this.getAuth(AuthType.Modify)) {
			btns.add(LmsButtonEnum.ProducePaper);
			btns.add(LmsButtonEnum.Delete);
			btns.add(LmsButtonEnum.Maintain);
		}
		btns.add(LmsButtonEnum.Filter);
		btns.add(LmsButtonEnum.Search);
		btns.add(LmsButtonEnum.ProduceExcel);
        MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
        if("900".equals(user.getSsoUnitNo())){
			//J-113-0519 一次性修正主機資料用
			btns.add(LmsButtonEnum.UpdateRetrialDataJ1130519);
        }
        addToButtonPanel(model, btns);
		renderJsI18N(LMS2405V01Page.class);
		renderJsI18N(AbstractEloanPage.class);
		model.addAttribute("showejcicList", ! retrialService.overSeaProgram() );
		setupIPanel(new LMS2405FilterPanel(PANEL_ID), model, params);
	}
}
