$(document).ready(function(){
    var gridview = $("#gridview").iGrid({
        handler: 'unlockdocgridhandler',
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        postData: {
            formAction: "query"
        },
        colModel: [{
            colHeader: i18n.unlockdoc['unlockdoc.doc.desc'], // '文件說明',
            name: 'docDesc',
            align: "left",
            width: 450,
            formatter: 'click',
            onclick: doUnlockLink,
            sortable: false
        }, {
            colHeader: i18n.unlockdoc['unlockdoc.doc.type'], // '開啟類型',
            name: 'openType',
            align: "center",
            width: 60,
            sortable: false
        }, {
            colHeader: i18n.unlockdoc['unlockdoc.doc.owner'], // '編輯人員',
            name: 'opener',
            align: "left",
            width: 80,
            sortable: false
        }, {
            colHeader: i18n.unlockdoc['unlockdoc.doc.owner'], // '編輯人員',
            name: 'openerId',
            align: "left",
            width: 80,
            sortable: false,
            hidden: true
        }, {
            colHeader: i18n.unlockdoc['unlockdoc.doc.branch'], // '編輯人員單位',
            name: 'openerUnit',
            align: "left",
            width: 100,
            sortable: false,
            hidden: true
        }, {
            colHeader: i18n.unlockdoc['unlockdoc.doc.time'], // '開啟時間',
            name: 'openTime',
            align: "center",
            width: 140,
            sortable: false
        }, {
            colHeader: i18n.unlockdoc['unlockdoc.custInfo'],
            name: 'oid',
            align: "center",
            width: 200,
            sortable: false,
            hidden: true
        }, {
            colHeader: i18n.unlockdoc['unlockdoc.mainId'], // '案件編號',
            name: 'mainId',
            align: "left",
            width: 250,
            sortable: false,
            hidden: true
        }],
        ondblClickRow: function(){
            doUnlockLink();
        }
    });
    
    
    $('#btnUnlock').click(function(){
        doUnlockLink();
    });
    
    function doUnlockLink(cellvalue, options, rowObject){
        if (!rowObject) {
            var id = $("#gridview").jqGrid('getGridParam', 'selrow');
            if (id) {
                rowObject = $("#gridview").jqGrid('getRowData', id);
            }
        }
        
        if (rowObject) {
            API.confirmMessage(i18n.unlockdoc['unlockdoc.msg1'], function(res){
                if (res) {
                    $.ajax({
                        handler: 'unlockdocformhandler',
                        data: {
                            formAction: 'unlockDoc',
                            oid: rowObject.oid,
                            lockMainId: rowObject.mainId,
                            userId: rowObject.openerId
                        },
                        success: function(responseData){
                            CommonAPI.showMessage(i18n.unlockdoc['unlockdoc.ok']);
                            reloadGrid();                            
                        }
                    });
                    $.thickbox.close();
                }
            });
        }
        else {
            CommonAPI.showErrorMessage(i18n.unlockdoc['unlockdoc.msg2']);
        }
    }
    
    function reloadGrid(){
        gridview.trigger("reloadGrid");
    }
    
});

