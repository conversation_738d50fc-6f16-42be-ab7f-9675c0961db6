
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.cls.pages.CLS1220M01Page;
import com.mega.eloan.lms.cls.pages.CLS1220M03Page;
import com.mega.eloan.lms.cls.pages.CLS1220V01Page;

/**
 * <pre>
 * 線上房貸
 * </pre>
 * 
 * @since 2020/6/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/6/23,EL08034,new
 *          </ul>
 */
public class CLS1220S01PanelE extends Panel {

	private static final long serialVersionUID = 1L;

	/**
	 * @param id
	 */
	public CLS1220S01PanelE(String id) {
		super(id);
	}

	/**
	 * @param id
	 * @param updatePanelName
	 */
	public CLS1220S01PanelE(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		renderJsI18N(CLS1220M01Page.class);
		renderJsI18N(CLS1220M03Page.class);
		renderJsI18N(CLS1220V01Page.class);
		
		new DocLogPanel("_docLog").processPanelData(model, params);
		new CLS1220S06Panel01("_iPanelS").processPanelData(model, params);
	}

}
