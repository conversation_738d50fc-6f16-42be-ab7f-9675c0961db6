package com.mega.eloan.lms.batch.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.security.KeyStore;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.scheme.PlainSocketFactory;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.conn.tsccm.ThreadSafeClientConnManager;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.http.util.EntityUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.gwclient.RPAHttpSSLSocketFactory;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.service.LMSLgdService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120S20A;
import com.mega.eloan.lms.model.L120S20B;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

@Service("lmsbatch0005serviceimpl")
public class LmsBatch0005ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(LmsBatch0005ServiceImpl.class);

	@Resource
	SysParameterService sysParamService;

	@Resource
	BranchService branchService;

	@Resource
	MisdbBASEService misDBService;

	@Resource
	LMSService lmsService;

	// @Resource
	// L120S20ADaoImpl l120s20aDao;
	// @Resource
	// L120S20BDaoImpl l120s20bDao;

	@Resource
	SysParameterService sysparamService;

	@Resource
	LMSLgdService lmsLgdService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	MisMISLN20Service misMISLN20Service;

	@Resource
	MisELLNGTEEService misEllngteeService;

	static boolean isTest = true;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		// @NonTransactional
		JSONObject result = new JSONObject();

		// result = testCase1(json);

		// result = testCase2(json);

		// String mainId = "22222222222222222222222222222222";
		// // 引進ALOAN LNF025共用額度資料
		// String[] cntrNoArr = { "201110600261", "201110200094",
		// "005109190047",
		// "201110600262", "005109500052", "201110600263", "201110200095",
		// "005109700070", "201110600137", "201110500017", "201110200411" };

		// 引進ALOAN LNF025共用額度資料
		// String mainId = "33333333333333333333333333333333";
		// String[] cntrNoArr = { "005109800140", "005109800141",
		// "005109800142", "005410500036", "005410600009" };

		// String mainId = "44444444444444444444444444444444";
		// String[] cntrNoArr = { "042110900529", "042110900530",
		// "042111000088",
		// "042111000091", "042111000092" };

		// String mainId = "55555555555555555555555555555555";
		// String[] cntrNoArr = { "047109500123", "047110600052",
		// "047409600081",
		// "20510950Y123", "20511060Y052", "20540960Y081", "047110700083",
		// "047110700084", "047110700085", "047110700086", "047110800092",
		// "0474098Y2007", "047410200001", "047410900003", "20511080X092",
		// "2054098YX007", "20541020X001", "20541090X003", "047111000026",
		// "047111000027", "047111000028", "047111000029", "049109300100",
		// "049410200002", "231409900001", "231409900002", "231409900003",
		// "238109900099", "238111000003", "238111000004", "238111000005",
		// "238110000311", "238110900014", "238111000027", "238111000028",
		// "238110200036", "238110900338", "238110900339", "238110900341",
		// "238110200037", "238110900342", "238110600107", "238110600108",
		// "238110600109", "238110600110", "238110700115", "240109700003",
		// "240109700004", "212110700064", "212110700065", "212110700066",
		// "212110800011", "212110800012", "212110800013", "212110800014",
		// "213110300092", "213110901084", "213110901085", "213110901086",
		// "213110400062", "213110400063", "213110400064", "213110400065",
		// "071109290002", "071109600105", "071409200133", "071409500121",
		// "075110600056", "075110900451", "075110900452", "075110900465",
		// "018109600118", "018109600127", "018110200014", "018110200015",
		// "018110200018", "018110200019", "018110500082", "018110500083",
		// "018109700016", "041410100001", "041410100002", "041410100003",
		// "042110900529", "042110900530", "042111000088", "042111000091",
		// "042111000092", "005109800140", "005109800141", "005109800142",
		// "005410500036", "005410600009" };

		// String mainId = "66666666666666666666666666666666";
		// String[] cntrNoArr = {
		// "238110200036",
		// "238110200037",
		// "238110900338",
		// "238110900339",
		// "238110900341",
		// "238110900342"
		// };

		String mainId = "77777777777777777777777777777777";
		String[] cntrNoArr = { "017109190005", "017110600043", "017110700016",
				"017110800006", "017110900223", "017109200054", "017110900257",
				"202109900200", "202110400124", "007109500031", "007110400155",
				"007110900058", "020410600022", "055110600248", "005109190047",
				"201110200094", "201110600261", "007410400997", "007410600308",
				"007410700291", "007410900214", "007110400307", "007110400315",
				"005109700070", "201110200095", "201110600263", "005109500052",
				"201110600262", "004110900081", "004110900083", "004110900084",
				"004110900085", "069110300521", "069110300523", "007110800026",
				"007110800027", "007110800028", "007110800267", "016109291021",
				"016110200212", "016110700077", "016110700078", "016110800117",
				"021110600129", "021110800231", "021110800232", "201410900013",
				"201110900296", "201110900327", "201110900328", "008110500086",
				"008110900631", "030110800070", "201110700222", "201110700224",
				"201110500151", "201110500153", "201110700221" };

//		String mainId = "88888888888888888888888888888888";
//		String[] cntrNoArr = { "007410600308", "007410900214" };

		result = testCase3(json, mainId, cntrNoArr);

		return result;
	}

	public JSONObject testCase3(JSONObject json, String mainId,
			String[] cntrNoArr) {
		JSONObject result = new JSONObject();
		List<L120S20B> listL120s20b = null;
		BranchRate branchRate = lmsService.getBranchRate("005");
		try {
			LOGGER.info("-------------------lmsbatch0005serviceimpl start -----------------------------------");

			String errorMsg = "";
			// 清除資料
			if (true) {
				// 1.初始化資料****************************************************************
				// 清除資料
				List<L120S20A> dellistL120s20a = lmsLgdService
						.findL120s20aByMainId(mainId);
				lmsLgdService.deleteListL120s20a(dellistL120s20a);

				List<L120S20B> dellistL120s20b = lmsLgdService
						.findL120s20bByMainId(mainId);
				lmsLgdService.deleteListL120s20b(dellistL120s20b);

			}

			List<String> cntrNos = Arrays.asList(cntrNoArr);
			List<Map<String, Object>> lnf025List = misDBService
					.findlnf025CoByCntrNoForLgd(cntrNos);
			for (Map<String, Object> lnf025 : lnf025List) {

				String LNF025_CONTRACT_CO = MapUtils.getString(lnf025,
						"LNF025_CONTRACT_CO");

				String LNF025_CONTRACT = MapUtils.getString(lnf025,
						"LNF025_CONTRACT");

				// 確認DB是否已經存在
				List<L120S20A> existlistL120s20a = lmsLgdService
						.findL120s20aByMainIdCntrNoCoAndCntrNo(mainId,
								LNF025_CONTRACT_CO, LNF025_CONTRACT);
				if (existlistL120s20a != null && !existlistL120s20a.isEmpty()) {
					// 已經存在
					continue;
				}

				String LIMIT_SWIFT = MapUtils.getString(lnf025, "LIMIT_SWIFT");
				BigDecimal LIMIT_FACT_AMT = Util.equals(
						MapUtils.getString(lnf025, "LIMIT_FACT_AMT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf025,
								"LIMIT_FACT_AMT"));
				if (LIMIT_FACT_AMT.compareTo(BigDecimal.ZERO) == 0) {
					// 限額為0時，不是額度共管，而是科目共管
					continue;
				}

				if (Util.notEquals(LIMIT_SWIFT, "TWD")) {
					LIMIT_FACT_AMT = branchRate.toOtherAmt(LIMIT_SWIFT, "TWD",
							LIMIT_FACT_AMT);
				}

				// FOR TEST
				// if (isTest) {
				// LIMIT_FACT_AMT = new BigDecimal(188860000);
				// }

				String LNF020_SWFT = MapUtils.getString(lnf025, "LNF020_SWFT");
				BigDecimal LNF020_FACT_AMT = Util.equals(
						MapUtils.getString(lnf025, "LNF020_FACT_AMT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf025,
								"LNF020_FACT_AMT"));
				BigDecimal LNF252_FACT_AMT_NT = Util.equals(
						MapUtils.getString(lnf025, "LNF252_FACT_AMT_NT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf025,
								"LNF252_FACT_AMT_NT"));
				BigDecimal LNF252_LOAN_BAL_NT = Util.equals(
						MapUtils.getString(lnf025, "LNF252_LOAN_BAL_NT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf025,
								"LNF252_LOAN_BAL_NT"));
				BigDecimal LNF252_RCV_INT_NT = Util.equals(
						MapUtils.getString(lnf025, "LNF252_RCV_INT_NT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf025,
								"LNF252_RCV_INT_NT"));

				L120S20A l120s20a = new L120S20A();
				l120s20a.setMainId(mainId);
				l120s20a.setCntrNoCo(LNF025_CONTRACT_CO);
				l120s20a.setFactAmtCoTwd(LIMIT_FACT_AMT);
				l120s20a.setCntrNo(LNF025_CONTRACT);
				l120s20a.setCurrentApplyCurr(LNF020_SWFT);
				l120s20a.setCurrentApplyAmt(LNF020_FACT_AMT);
				l120s20a.setCurrentApplyAmtTwd(LNF252_FACT_AMT_NT);
				l120s20a.setBlAmtTwd(LNF252_LOAN_BAL_NT);
				l120s20a.setRcvIntTwd(LNF252_RCV_INT_NT);
				lmsLgdService.save(l120s20a);
			}

			// 沒有共用的直接新增L120S20B
			for (String cntrKey : cntrNos) {
				List<L120S20A> noCoCntrNo = lmsLgdService
						.findL120s20aByMainIdCntrNo(mainId, cntrKey);
				if (noCoCntrNo == null || noCoCntrNo.isEmpty()) {
					// 沒有共用，直接新增L120S20B

					L120S20B l120s20b = new L120S20B();
					l120s20b.setMainId(mainId);
					l120s20b.setCntrNo(cntrKey);

					Map<String, Object> lnf252 = misDBService
							.findLnf252ByCntrNo(cntrKey);
					BigDecimal LNF252_FACT_AMT_NT = Util.equals(
							MapUtils.getString(lnf252, "LNF252_FACT_AMT_NT"),
							"") ? BigDecimal.ZERO : Util
							.parseBigDecimal(MapUtils.getString(lnf252,
									"LNF252_FACT_AMT_NT"));

					l120s20b.setCntrEad(LNF252_FACT_AMT_NT);
					lmsLgdService.save(l120s20b);
				}
			}

			// 2.若有共用(L120S20A)，開始分配EAD***************************************************************
			this.testEad(mainId);

			// 3.若有額度明細表但沒有共用，沒有再L120S20B的，將額度明細表資訊補到L120S20B***************************
			// 針對有額度明細表的

			listL120s20b = lmsLgdService.findL120s20bByMainId(mainId);
			for (L120S20B l120s20b : listL120s20b) {
				errorMsg = this.syncL140m01aData(l120s20b);
				if (Util.notEquals(errorMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"lmsbatch0005serviceimpl 「同步額度明細表資料與Lookups參數」執行失敗！==>"
									+ errorMsg);
				}
			}

			if (true) {

				// 取額分配擔保品**********************************************************
				listL120s20b = lmsLgdService.findL120s20bByMainId(mainId);
				Map<String, String> cntrEadMap = new HashMap<String, String>();
				for (L120S20B l120s20b : listL120s20b) {
					String cntrNo = l120s20b.getCntrNo();
					cntrEadMap.put(cntrNo, l120s20b.getCntrEad() == null ? "0"
							: l120s20b.getCntrEad().toPlainString());
				}

				for (L120S20B l120s20b : listL120s20b) {
					String cntrNo = l120s20b.getCntrNo();
					if (Util.equals(l120s20b.getHasCntrDoc(), "Y")) {
						errorMsg = this.getCollateralRecovery(l120s20b,
								cntrEadMap);
						if (Util.notEquals(errorMsg, "")) {
							result = WebBatchCode.RC_ERROR;
							result.element(WebBatchCode.P_RESPONSE,
									"lmsbatch0005serviceimpl 「取得分配後擔保品回收」執行失敗！==>"
											+ errorMsg);
						}
					}
				}

			}

			// 計算LGD****************************************************************
			listL120s20b = lmsLgdService.findL120s20bByMainId(mainId);
			for (L120S20B l120s20b : listL120s20b) {
				String cntrNo = l120s20b.getCntrNo();
				if (Util.equals(l120s20b.getHasCntrDoc(), "Y")) {
					errorMsg = this.caculateLgd(l120s20b);
					if (Util.notEquals(errorMsg, "")) {
						result = WebBatchCode.RC_ERROR;
						result.element(WebBatchCode.P_RESPONSE,
								"lmsbatch0005serviceimpl 「計算LGD」執行失敗！==>"
										+ errorMsg);
					}
				}

			}

			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"lmsbatch0005serviceimpl執行成功！");

		} catch (Exception ex) {
			ex.printStackTrace();
			result = WebBatchCode.RC_ERROR;
			result.element(
					WebBatchCode.P_RESPONSE,
					"lmsbatch0005serviceimpl執行失敗！==>"
							+ ex.getLocalizedMessage());

		}

		return result;
	}

	public JSONObject testCase2(JSONObject json) {
		BranchRate branchRate = lmsService.getBranchRate("005");
		JSONObject result = new JSONObject();
		List<L120S20B> listL120s20b = null;
		try {
			LOGGER.info("-------------------lmsbatch0005serviceimpl start -----------------------------------");
			String mainId = "22222222222222222222222222222222";
			String errorMsg = "";
			// 清除資料
			if (true) {
				// 1.初始化資料****************************************************************
				// 清除資料
				List<L120S20A> dellistL120s20a = lmsLgdService
						.findL120s20aByMainId(mainId);
				lmsLgdService.deleteListL120s20a(dellistL120s20a);

				List<L120S20B> dellistL120s20b = lmsLgdService
						.findL120s20bByMainId(mainId);
				lmsLgdService.deleteListL120s20b(dellistL120s20b);

			}

			// 引進ALOAN LNF025共用額度資料
			String[] cntrNoArr = { "201110600261", "201110200094",
					"005109190047", "201110600262", "005109500052",
					"201110600263", "201110200095", "005109700070",
					"201110600137", "201110500017", "201110200411" };

			List<String> cntrNos = Arrays.asList(cntrNoArr);
			List<Map<String, Object>> lnf025List = misDBService
					.findlnf025CoByCntrNoForLgd(cntrNos);
			for (Map<String, Object> lnf025 : lnf025List) {
				L120S20A l120s20a = new L120S20A();

				String LNF025_CONTRACT_CO = MapUtils.getString(lnf025,
						"LNF025_CONTRACT_CO");
				String LIMIT_SWIFT = MapUtils.getString(lnf025, "LIMIT_SWIFT");
				BigDecimal LIMIT_FACT_AMT = Util.equals(
						MapUtils.getString(lnf025, "LIMIT_FACT_AMT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf025,
								"LIMIT_FACT_AMT"));
				if (Util.notEquals(LIMIT_SWIFT, "TWD")) {
					LIMIT_FACT_AMT = branchRate.toOtherAmt(LIMIT_SWIFT, "TWD",
							LIMIT_FACT_AMT);
				}

				String LNF025_CONTRACT = MapUtils.getString(lnf025,
						"LNF025_CONTRACT");
				String LNF020_SWFT = MapUtils.getString(lnf025, "LNF020_SWFT");
				BigDecimal LNF020_FACT_AMT = Util.equals(
						MapUtils.getString(lnf025, "LNF020_FACT_AMT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf025,
								"LNF020_FACT_AMT"));
				BigDecimal LNF252_FACT_AMT_NT = Util.equals(
						MapUtils.getString(lnf025, "LNF252_FACT_AMT_NT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf025,
								"LNF252_FACT_AMT_NT"));
				BigDecimal LNF252_LOAN_BAL_NT = Util.equals(
						MapUtils.getString(lnf025, "LNF252_LOAN_BAL_NT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf025,
								"LNF252_LOAN_BAL_NT"));
				BigDecimal LNF252_RCV_INT_NT = Util.equals(
						MapUtils.getString(lnf025, "LNF252_RCV_INT_NT"), "") ? BigDecimal.ZERO
						: Util.parseBigDecimal(MapUtils.getString(lnf025,
								"LNF252_RCV_INT_NT"));

				l120s20a.setMainId(mainId);
				l120s20a.setCntrNoCo(LNF025_CONTRACT_CO);
				l120s20a.setFactAmtCoTwd(LIMIT_FACT_AMT);
				l120s20a.setCntrNo(LNF025_CONTRACT);
				l120s20a.setCurrentApplyCurr(LNF020_SWFT);
				l120s20a.setCurrentApplyAmt(LNF020_FACT_AMT);
				l120s20a.setCurrentApplyAmtTwd(LNF252_FACT_AMT_NT);
				l120s20a.setBlAmtTwd(LNF252_LOAN_BAL_NT);
				l120s20a.setRcvIntTwd(LNF252_RCV_INT_NT);
				lmsLgdService.save(l120s20a);
			}

			// 沒有共用的直接新增L120S20B
			for (String cntrKey : cntrNos) {
				List<L120S20A> noCoCntrNo = lmsLgdService
						.findL120s20aByMainIdCntrNo(mainId, cntrKey);
				if (noCoCntrNo == null || noCoCntrNo.isEmpty()) {
					// 沒有共用，直接新增L120S20B

					L120S20B l120s20b = new L120S20B();
					l120s20b.setMainId(mainId);
					l120s20b.setCntrNo(cntrKey);

					Map<String, Object> lnf252 = misDBService
							.findLnf252ByCntrNo(cntrKey);
					BigDecimal LNF252_FACT_AMT_NT = Util.equals(
							MapUtils.getString(lnf252, "LNF252_FACT_AMT_NT"),
							"") ? BigDecimal.ZERO : Util
							.parseBigDecimal(MapUtils.getString(lnf252,
									"LNF252_FACT_AMT_NT"));

					l120s20b.setCntrEad(LNF252_FACT_AMT_NT);
					lmsLgdService.save(l120s20b);
				}
			}

			// 2.若有共用(L120S20A)，開始分配EAD***************************************************************
			this.testEad(mainId);

			// 3.若有額度明細表但沒有共用，沒有再L120S20B的，將額度明細表資訊補到L120S20B***************************
			// 針對有額度明細表的

			listL120s20b = lmsLgdService.findL120s20bByMainId(mainId);
			for (L120S20B l120s20b : listL120s20b) {
				errorMsg = this.syncL140m01aData(l120s20b);
				if (Util.notEquals(errorMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"lmsbatch0005serviceimpl 「同步額度明細表資料與Lookups參數」執行失敗！==>"
									+ errorMsg);
				}
			}

			if (true) {

				// 取額分配擔保品**********************************************************
				listL120s20b = lmsLgdService.findL120s20bByMainId(mainId);
				Map<String, String> cntrEadMap = new HashMap<String, String>();
				for (L120S20B l120s20b : listL120s20b) {
					String cntrNo = l120s20b.getCntrNo();
					cntrEadMap.put(cntrNo, l120s20b.getCntrEad() == null ? "0"
							: l120s20b.getCntrEad().toPlainString());
				}

				for (L120S20B l120s20b : listL120s20b) {
					String cntrNo = l120s20b.getCntrNo();
					if (Util.equals(l120s20b.getHasCntrDoc(), "Y")) {
						errorMsg = this.getCollateralRecovery(l120s20b,
								cntrEadMap);
						if (Util.notEquals(errorMsg, "")) {
							result = WebBatchCode.RC_ERROR;
							result.element(WebBatchCode.P_RESPONSE,
									"lmsbatch0005serviceimpl 「取得分配後擔保品回收」執行失敗！==>"
											+ errorMsg);
						}
					}
				}

			}

			// 計算LGD****************************************************************
			listL120s20b = lmsLgdService.findL120s20bByMainId(mainId);
			for (L120S20B l120s20b : listL120s20b) {
				String cntrNo = l120s20b.getCntrNo();
				if (Util.equals(l120s20b.getHasCntrDoc(), "Y")) {
					errorMsg = this.caculateLgd(l120s20b);
					if (Util.notEquals(errorMsg, "")) {
						result = WebBatchCode.RC_ERROR;
						result.element(WebBatchCode.P_RESPONSE,
								"lmsbatch0005serviceimpl 「計算LGD」執行失敗！==>"
										+ errorMsg);
					}
				}

			}

			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"lmsbatch0005serviceimpl執行成功！");

		} catch (Exception ex) {
			ex.printStackTrace();
			result = WebBatchCode.RC_ERROR;
			result.element(
					WebBatchCode.P_RESPONSE,
					"lmsbatch0005serviceimpl執行失敗！==>"
							+ ex.getLocalizedMessage());

		}

		return result;
	}

	public JSONObject testCase1(JSONObject json) {
		JSONObject result = new JSONObject();
		List<L120S20B> listL120s20b = null;
		try {
			LOGGER.info("-------------------lmsbatch0004serviceimpl start -----------------------------------");
			String mainId = "********************************";
			String errorMsg = "";
			// 清除資料
			if (false) {
				// 1.初始化資料****************************************************************
				// 清除資料
				List<L120S20A> listL120s20a = lmsLgdService
						.findL120s20aByMainId(mainId);

				// 清除共用額度暫存欄位
				for (L120S20A l120s20a : listL120s20a) {
					l120s20a.setRatio(null);
					l120s20a.setAllocate1(null);
					l120s20a.setAllocate2(null);
					l120s20a.setAllocate3(null);
					l120s20a.setAllocateF(null);
					lmsLgdService.save(l120s20a);
				}

				// 清除額度EAD分配資料
				List<L120S20B> dellistL120s20b = lmsLgdService
						.findL120s20bByMainId(mainId);
				lmsLgdService.deleteListL120s20b(dellistL120s20b);

				// 2.若有共用(L120S20A)，開始分配EAD***************************************************************
				this.testEad(mainId);
			}

			// 3.若有額度明細表但沒有共用，沒有再L120S20B的，將額度明細表資訊補到L120S20B***************************
			// 針對有額度明細表的

			listL120s20b = lmsLgdService.findL120s20bByMainId(mainId);
			for (L120S20B l120s20b : listL120s20b) {
				errorMsg = this.syncL140m01aData(l120s20b);
				if (Util.notEquals(errorMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"lmsbatch0005serviceimpl 「同步額度明細表資料與Lookups參數」執行失敗！==>"
									+ errorMsg);
				}
			}

			if (false) {

				// 取額分配擔保品**********************************************************
				listL120s20b = lmsLgdService.findL120s20bByMainId(mainId);
				Map<String, String> cntrEadMap = new HashMap<String, String>();
				for (L120S20B l120s20b : listL120s20b) {
					String cntrNo = l120s20b.getCntrNo();
					cntrEadMap.put(cntrNo, l120s20b.getCntrEad() == null ? "0"
							: l120s20b.getCntrEad().toPlainString());
				}

				for (L120S20B l120s20b : listL120s20b) {
					String cntrNo = l120s20b.getCntrNo();
					if (Util.equals(l120s20b.getHasCntrDoc(), "Y")) {
						errorMsg = this.getCollateralRecovery(l120s20b,
								cntrEadMap);
						if (Util.notEquals(errorMsg, "")) {
							result = WebBatchCode.RC_ERROR;
							result.element(WebBatchCode.P_RESPONSE,
									"lmsbatch0005serviceimpl 「取得分配後擔保品回收」執行失敗！==>"
											+ errorMsg);
						}
					}
				}

			}

			// 計算LGD****************************************************************
			listL120s20b = lmsLgdService.findL120s20bByMainId(mainId);
			for (L120S20B l120s20b : listL120s20b) {
				String cntrNo = l120s20b.getCntrNo();
				if (Util.equals(l120s20b.getHasCntrDoc(), "Y")) {
					errorMsg = this.caculateLgd(l120s20b);
					if (Util.notEquals(errorMsg, "")) {
						result = WebBatchCode.RC_ERROR;
						result.element(WebBatchCode.P_RESPONSE,
								"lmsbatch0005serviceimpl 「計算LGD」執行失敗！==>"
										+ errorMsg);
					}
				}

			}

			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"lmsbatch0005serviceimpl執行成功！");

		} catch (Exception ex) {
			ex.printStackTrace();
			result = WebBatchCode.RC_ERROR;
			result.element(
					WebBatchCode.P_RESPONSE,
					"lmsbatch0005serviceimpl執行失敗！==>"
							+ ex.getLocalizedMessage());

		}

		return result;
	}

	public void testEad(String mainId) throws CapMessageException {

		Map<String, String> coCntrNoMap = new HashMap<String, String>();

		boolean allocateMainAgain = false;

		// 找出簽報書有設定共用之額度組別
		if (true) {
			List<L120S20A> listL120s20a = lmsLgdService
					.findL120s20aByMainId(mainId);
			for (L120S20A l120s20a : listL120s20a) {
				String cntrNoCo = Util.trim(l120s20a.getCntrNoCo());
				if (!coCntrNoMap.containsKey(cntrNoCo)) {
					coCntrNoMap.put(cntrNoCo, cntrNoCo);
				}
			}
		}

		do {
			allocateMainAgain = false;

			// 開始分配***************************************************************************************************************
			this.reAllocate(mainId);

			// 分配結束，判斷有沒有多組共用***************************************************************************************************************

			// 判斷同一額度序號有沒有出現在其他組
			Map<String, BigDecimal> cntrNoHasMultipleCnrNoCo = new LinkedHashMap<String, BigDecimal>();

			List<Object[]> metaList = lmsLgdService
					.findL120s20aMinAllocate(mainId);
			if (metaList != null && !metaList.isEmpty()) {
				for (Object[] meta : metaList) {
					String cntrNo = Util.trim(meta[0]);
					BigDecimal maxAllocate2 = Util.parseToBigDecimal(Util
							.trim(meta[1]));
					cntrNoHasMultipleCnrNoCo.put(cntrNo, maxAllocate2);
				}
			}

			// 同一額度序號都沒有沒有出現在多組共用，直接用前次分配結果為額度EAD
			if (true) {

				for (String coCntrNoKey : coCntrNoMap.keySet()) {
					boolean canFinish = true;
					List<L120S20A> listL120s20a = lmsLgdService
							.findL120s20aByMainIdCntrNoCo(mainId, coCntrNoKey);
					for (L120S20A l120s20a : listL120s20a) {
						String cntrNo = Util.trim(l120s20a.getCntrNo());

						if (cntrNoHasMultipleCnrNoCo.containsKey(cntrNo)) {
							// 該組共用額度的分配不能馬上結束，留到下一段處理
							canFinish = false;
							break;
						}

					}

					if (canFinish) {
						// 該組共用額度的分配沒有其他額度共用問題，所以可以結束
						for (L120S20A l120s20a : listL120s20a) {
							String cntrNo = Util.trim(l120s20a.getCntrNo());
							// 沒有EAD的，就可以直接用分配後金額來設定額度EAD
							L120S20B l1120s20b = lmsLgdService
									.findL120s20bByMainIdCntrNo(mainId, cntrNo);
							BigDecimal allocate2 = l120s20a.getAllocate2();

							// 分配後額度若大於原始申請額度，則以原始申請額度為主
							allocate2 = allocate2.compareTo(l120s20a
									.getCurrentApplyAmtTwd()) > 0 ? l120s20a
									.getCurrentApplyAmtTwd() : allocate2;

							// 分配後小於0要變成0
							allocate2 = allocate2.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO
									: allocate2;

							l120s20a.setAllocate2(allocate2);
							if (l1120s20b != null) {
								if (l1120s20b.getCntrEad() == null) {
									l120s20a.setAllocateF(allocate2);
									lmsLgdService.save(l120s20a);
									l1120s20b.setCntrEad(allocate2);
								}
							} else {
								l120s20a.setAllocateF(allocate2);
								lmsLgdService.save(l120s20a);
								l1120s20b = new L120S20B();
								l1120s20b.setMainId(mainId);
								l1120s20b.setCntrNo(cntrNo);
								l1120s20b.setCntrEad(allocate2);
							}

							lmsLgdService.save(l120s20a);
							lmsLgdService.save(l1120s20b);

						}
					}

				}
			}

			if (true) {

				// 1.先檢查多組共用之各額度
				boolean gHasComplex = false; // 有沒有多額度共用且交錯共用之情形

				// 如果有多組但沒有交集的，則該組額度已經可以先確認EAD，不再重新分配
				for (String cntrKey : cntrNoHasMultipleCnrNoCo.keySet()) {
					List<L120S20A> listL120s20a = lmsLgdService
							.findL120s20aByMainIdCntrNo(mainId, cntrKey);
					Map<String, String> chkCnrNoCo = new HashMap<String, String>();
					// 找出該額度序號有關的共用序號
					for (L120S20A l120s20a : listL120s20a) {
						chkCnrNoCo.put(l120s20a.getCntrNoCo(), "");
					}
					boolean hasComplex = false;
					if (!chkCnrNoCo.isEmpty()) {
						// 檢查有關的共用序號中，有沒有其他有多筆共用的額度序號
						for (String chkCnrNoCoKey : chkCnrNoCo.keySet()) {

							List<L120S20A> chkL120s20a = lmsLgdService
									.findL120s20aByMainIdCntrNoCo(mainId,
											chkCnrNoCoKey);
							for (L120S20A tl120s20a : chkL120s20a) {
								String tCntrNo = Util.trim(tl120s20a
										.getCntrNo());
								if (Util.notEquals(tCntrNo, cntrKey)) {
									if (cntrNoHasMultipleCnrNoCo
											.containsKey(tCntrNo)) {
										hasComplex = true;
										gHasComplex = true;
										break;
									}
								}
							}

							if (hasComplex) {
								break;
							}

						}
					}

					// 沒有交錯共用，就可以直接決定EAD了
					if (!hasComplex) {

						// 分配後額度若大於原始申請額度，則以原始申請額度為主
						BigDecimal currentApplyAmt = BigDecimal.ZERO;

						List<L120S20A> al120s20as = lmsLgdService
								.findL120s20aByMainIdCntrNo(mainId, cntrKey);
						for (L120S20A xl120s20a : al120s20as) {
							// 取得其中一筆現請額度
							currentApplyAmt = xl120s20a.getCurrentApplyAmtTwd();
							break;
						}

						BigDecimal allocate2 = cntrNoHasMultipleCnrNoCo
								.get(cntrKey);
						allocate2 = allocate2.compareTo(currentApplyAmt) > 0 ? currentApplyAmt
								: allocate2;

						// 分配後小於0要變成0
						allocate2 = allocate2.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO
								: allocate2;

						// 沒有EAD的，就可以直接用分配後金額來設定額度EAD
						L120S20B l1120s20b = lmsLgdService
								.findL120s20bByMainIdCntrNo(mainId, cntrKey);
						if (l1120s20b != null) {
							if (l1120s20b.getCntrEad() == null) {
								l1120s20b.setCntrEad(allocate2);
							}
						} else {
							l1120s20b = new L120S20B();
							l1120s20b.setMainId(mainId);
							l1120s20b.setCntrNo(cntrKey);
							l1120s20b.setCntrEad(allocate2);
						}
						lmsLgdService.save(l1120s20b);

						for (L120S20A l120s20a : listL120s20a) {
							String cntrNo = Util.trim(l120s20a.getCntrNo());
							if (Util.equals(cntrNo, cntrKey)) {
								// 沒有EAD的，就可以直接用分配後金額來設定額度EAD
								l120s20a.setAllocateF(l120s20a.getAllocate2());
								lmsLgdService.save(l120s20a);
							}

						}

					}

				}

				// 有額度序號同時出現在多組共用裡面，且該類額度序號有兩筆以上(交錯共用)
				if (gHasComplex) {

					// 由大至小採用
					for (String cntrKey : cntrNoHasMultipleCnrNoCo.keySet()) {

						L120S20B l120s20b = lmsLgdService
								.findL120s20bByMainIdCntrNo(mainId, cntrKey);

						// 還沒有最後額度EAD的才從最大處理
						if (l120s20b == null || l120s20b.getCntrEad() == null) {
							String cntrNo = cntrKey;
							BigDecimal maxAllocate2 = Util
									.parseToBigDecimal(MapUtils.getString(
											cntrNoHasMultipleCnrNoCo, cntrKey));

							// 判斷這個最大的額度如果扣掉出現的各組限額後仍大於非該額度之餘額合計，則可以採用
							// 確保分配後的額度不會是負的
							boolean canUse = true;
							if (true) {
								// 這個額度序號出現在那些組
								List<L120S20A> listL120s20a = lmsLgdService
										.findL120s20aByMainIdCntrNo(mainId,
												cntrKey);
								for (L120S20A l120s20a : listL120s20a) {
									String cntrNoCo = l120s20a.getCntrNoCo();
									BigDecimal factAmtCoTw = l120s20a
											.getFactAmtCoTwd(); // 限額
									BigDecimal coBalAmtSum = BigDecimal.ZERO;
									BigDecimal coAreadyEadSum = BigDecimal.ZERO;
									// 這些組下非該額度序號的餘額加總
									List<L120S20A> l120s20as = lmsLgdService
											.findL120s20aByMainIdCntrNoCo(
													mainId, cntrNoCo);
									for (L120S20A xl120s20a : l120s20as) {
										if (Util.notEquals(
												xl120s20a.getCntrNo(), cntrNo)) {

											// 是否為待分配
											L120S20B xl120s20b = lmsLgdService
													.findL120s20bByMainIdCntrNo(
															mainId,
															xl120s20a
																	.getCntrNo());

											if (xl120s20b == null
													|| xl120s20b.getCntrEad() == null) {
												// 是待分配
												coBalAmtSum = coBalAmtSum
														.add(xl120s20a
																.getBlAmtTwd() == null ? BigDecimal.ZERO
																: xl120s20a
																		.getBlAmtTwd())
														.add(xl120s20a
																.getRcvIntTwd() == null ? BigDecimal.ZERO
																: xl120s20a
																		.getRcvIntTwd());
											} else {
												coAreadyEadSum = coAreadyEadSum
														.add(xl120s20b
																.getCntrEad());
											}

										}
									}

									// 判斷是否： 共用限額 - 已確定分配EAD - 本次欲使用之最大分配額度 <
									// 該組其他待分配額度之餘額加總
									if (factAmtCoTw.subtract(coAreadyEadSum)
											.subtract(maxAllocate2)
											.compareTo(coBalAmtSum) < 0) {
										// 如果小於，代表可能分配後造成其他額度分配是負的，則繼續使用下一組次大的
										canUse = false;
										break;
									}

								}

							}

							if (canUse) {

								List<L120S20A> listL120s20a = lmsLgdService
										.findL120s20aByMainIdCntrNo(mainId,
												cntrKey);

								// 分配後額度若大於原始申請額度，則以原始申請額度為主
								BigDecimal currentApplyAmt = BigDecimal.ZERO;
								for (L120S20A xl120s20a : listL120s20a) {
									// 取得其中一筆現請額度
									currentApplyAmt = xl120s20a
											.getCurrentApplyAmtTwd();
									break;
								}

								BigDecimal allocate2 = maxAllocate2;
								allocate2 = allocate2
										.compareTo(currentApplyAmt) > 0 ? currentApplyAmt
										: allocate2;

								// 分配後小於0要變成0
								allocate2 = allocate2
										.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO
										: allocate2;

								for (L120S20A l120s20a : listL120s20a) {
									l120s20a.setAllocate2(allocate2);
									l120s20a.setAllocateF(allocate2);
									lmsLgdService.save(l120s20a);
								}

								// 設定額度EAD
								L120S20B l1120s20b = lmsLgdService
										.findL120s20bByMainIdCntrNo(mainId,
												cntrKey);
								if (l1120s20b != null) {
									l1120s20b.setCntrEad(allocate2);
								} else {
									l1120s20b = new L120S20B();
									l1120s20b.setMainId(mainId);
									l1120s20b.setCntrNo(cntrKey);
									l1120s20b.setCntrEad(allocate2);
								}
								lmsLgdService.save(l1120s20b);

								break;
							}
						}

					}

				}

				this.initialAllocate(mainId);

				// 檢查是不是共用都分配完了
				allocateMainAgain = false;
				if (true) {
					List<L120S20A> listL120s20a = lmsLgdService
							.findL120s20aByMainId(mainId);

					// 清除共用額度暫存欄位
					for (L120S20A l120s20a : listL120s20a) {

						String cntrNo = Util.trim(l120s20a.getCntrNo());

						// 看該額度序號有沒已經已分配的EAD
						L120S20B l1120s20b = lmsLgdService
								.findL120s20bByMainIdCntrNo(mainId, cntrNo);

						if (l1120s20b == null || l1120s20b.getCntrEad() == null) {
							allocateMainAgain = true; // 還有其他組，還要重新再分配一次
							break;
						}

					}
				}

			}

		} while (allocateMainAgain);

	}

	public void initialAllocate(String mainId) {

		// 初始化重新分配要清除資料

		List<L120S20A> listL120s20a = lmsLgdService
				.findL120s20aByMainId(mainId);

		// 清除共用額度暫存欄位
		for (L120S20A l120s20a : listL120s20a) {

			String cntrNo = Util.trim(l120s20a.getCntrNo());

			// 看該額度序號有沒已經已分配的EAD
			L120S20B l1120s20b = lmsLgdService.findL120s20bByMainIdCntrNo(
					mainId, cntrNo);

			if (l1120s20b != null) {
				BigDecimal cntrEad = l1120s20b.getCntrEad();
				if (cntrEad != null) {
					// 已有EAD的，就塞值後就不會再參與分配
					l120s20a.setAllocate2(cntrEad);
					l120s20a.setAllocateF(cntrEad);
				} else {
					// 要重新參加分配
					l120s20a.setRatio(null);
					l120s20a.setAllocate1(null);
					l120s20a.setAllocate2(null);
					l120s20a.setAllocate3(null);
					l120s20a.setAllocateF(null);
				}

			} else {
				// 要重新參加分配
				l120s20a.setRatio(null);
				l120s20a.setAllocate1(null);
				l120s20a.setAllocate2(null);
				l120s20a.setAllocate3(null);
				l120s20a.setAllocateF(null);
			}
			lmsLgdService.save(l120s20a);
		}

	}

	public void reAllocate(String mainId) {

		Map<String, String> coCntrNoMap = new HashMap<String, String>();

		this.initialAllocate(mainId);

		if (true) {
			List<L120S20A> listL120s20a = lmsLgdService
					.findL120s20aByMainId(mainId);
			for (L120S20A l120s20a : listL120s20a) {
				String cntrNoCo = Util.trim(l120s20a.getCntrNoCo());
				if (!coCntrNoMap.containsKey(cntrNoCo)) {
					coCntrNoMap.put(cntrNoCo, cntrNoCo);
				}
			}
		}

		if (true) {

			for (String coCntrNoKey : coCntrNoMap.keySet()) {

				List<L120S20A> listL120s20a = lmsLgdService
						.findL120s20aByMainIdCntrNoCo(mainId, coCntrNoKey);

				boolean allocateAgain = false;
				do {
					allocateAgain = false;
					BigDecimal factAmtCoTw = null;
					BigDecimal totalAmt = BigDecimal.ZERO;
					Map<String, String> cntrNoNeedAllocateMap = new HashMap<String, String>();

					// 取得待分配共用限額****************************************************************
					for (L120S20A l120s20a : listL120s20a) {
						String cntrNo = Util.trim(l120s20a.getCntrNo());
						if (factAmtCoTw == null) {
							factAmtCoTw = l120s20a.getFactAmtCoTwd() == null ? BigDecimal.ZERO
									: l120s20a.getFactAmtCoTwd();
						}

						boolean thisCntrNeedAllocate = true;

						// 先看這個額度序號有沒有已經確認分配EAD，若沒有則看該組共用內有沒有最終分配
						L120S20B l120s20b = lmsLgdService
								.findL120s20bByMainIdCntrNo(mainId, cntrNo);
						if (l120s20b == null || l120s20b.getCntrEad() == null) {
							// 沒有EAD
							factAmtCoTw = factAmtCoTw.subtract(l120s20a
									.getAllocate2() == null ? BigDecimal.ZERO
									: l120s20a.getAllocate2());
							if (l120s20a.getAllocate2() != null
									|| l120s20a.getAllocateF() != null) {
								thisCntrNeedAllocate = false;
							}

						} else {
							// L120S01B有EAD
							factAmtCoTw = factAmtCoTw.subtract(l120s20b
									.getCntrEad());
							l120s20a.setAllocate2(l120s20b.getCntrEad());
							l120s20a.setAllocateF(l120s20b.getCntrEad());
							thisCntrNeedAllocate = false;
						}

						// 要分配
						if (thisCntrNeedAllocate) {
							cntrNoNeedAllocateMap.put(cntrNo, cntrNo);
							totalAmt = totalAmt
									.add(l120s20a.getCurrentApplyAmtTwd() == null ? BigDecimal.ZERO
											: l120s20a.getCurrentApplyAmtTwd());
						}
					}

					// 算佔比****************************************************************
					for (L120S20A l120s20a : listL120s20a) {
						String cntrNo = Util.trim(l120s20a.getCntrNo());
						if (cntrNoNeedAllocateMap.containsKey(cntrNo)) {
							BigDecimal currentApplyAmtTwd = l120s20a
									.getCurrentApplyAmtTwd() == null ? BigDecimal.ZERO
									: l120s20a.getCurrentApplyAmtTwd();

							BigDecimal ratio = totalAmt != null
									&& totalAmt.compareTo(BigDecimal.ZERO) > 0 ? (currentApplyAmtTwd)
									.multiply(new BigDecimal(100)).divide(
											totalAmt, 16,
											BigDecimal.ROUND_HALF_UP)
									: BigDecimal.ZERO;

							// 用佔比X共用限額
							BigDecimal allocateTmp = factAmtCoTw
									.multiply(ratio).divide(
											new BigDecimal(100), 1,
											BigDecimal.ROUND_HALF_UP);
							BigDecimal balTwd = l120s20a.getBlAmtTwd() == null ? BigDecimal.ZERO
									: l120s20a.getBlAmtTwd();
							BigDecimal rcvIntTwd = l120s20a.getRcvIntTwd() == null ? BigDecimal.ZERO
									: l120s20a.getRcvIntTwd();

							// 分配後額度如果小於餘額(餘額大於0的才要判斷)
							if (balTwd.compareTo(BigDecimal.ZERO) > 0
									&& allocateTmp.compareTo(balTwd
											.add(rcvIntTwd)) < 0) {
								l120s20a.setAllocate2(balTwd.add(rcvIntTwd));
								allocateAgain = true;
							}

							l120s20a.setRatio(ratio);
							l120s20a.setAllocate1(allocateTmp);
							lmsLgdService.save(l120s20a);

						}

					}

					// 不用再重分配了，代表沒有餘額大於額度的情形，就可以將剩下的 Allocate1 搬到 Allocate2
					if (!allocateAgain) {
						for (L120S20A l120s20a : listL120s20a) {
							if (l120s20a.getAllocate2() == null) {
								l120s20a.setAllocate2(l120s20a.getAllocate1());
								lmsLgdService.save(l120s20a);
							}
						}
					}

				} while (allocateAgain);

			}
		}

	}

	public String getCollateralRecovery(L120S20B l120s20b,
			Map<String, String> cntrEadMap) {
		String errorMsg = "";

		String cntrNo = l120s20b.getCntrNo();

		// FOR TEST
		// if (isTest) {
		// cntrNo = "005110900483";
		// }

		BigDecimal cntrCollAmt = BigDecimal.ZERO;
		try {
			int sec = 30;

			KeyStore trustStore = KeyStore.getInstance(KeyStore
					.getDefaultType());
			trustStore.load(null, null);

			SSLSocketFactory sf = new RPAHttpSSLSocketFactory(trustStore);
			final HttpParams params = new BasicHttpParams();
			HttpConnectionParams.setStaleCheckingEnabled(params, false);
			HttpConnectionParams.setConnectionTimeout(params, sec * 1000);
			HttpConnectionParams.setSoTimeout(params, sec * 1000);
			HttpConnectionParams.setSocketBufferSize(params, 8192 * 5);
			SchemeRegistry registry = new SchemeRegistry();
			registry.register(new Scheme("http", PlainSocketFactory
					.getSocketFactory(), 80));
			registry.register(new Scheme("https", sf, 443));
			ClientConnectionManager ccm = new ThreadSafeClientConnManager(
					params, registry);
			HttpClient httpclient = new DefaultHttpClient(ccm, params);

			HttpPost httpPost = new HttpPost(
					sysParamService
							.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL));
			httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
			// 設定Header Authorization=token

			JSONObject reqJSONObj = new JSONObject();
			JSONObject request = new JSONObject();

			request.put("cntrNo", cntrNo);

			JSONObject cntrEad = new JSONObject();
			cntrEad.putAll(cntrEadMap);

			// FOR TEST
			// if (isTest) {
			// cntrEad.put("005110900483", "21200000");
			// cntrEad.put("005110700082", "100000");
			// cntrEad.put("005109190013", "200000");
			// cntrEad.put("005110500053", "300000");
			// }

			request.put("cntrEad", cntrEad.toString());

			JSONObject collKey = new JSONObject();

			// FOR TEST
			if (isTest) {
				// OID,COLLKEY
				// collKey.put("7C7EC230B49549DD845055E2DE6EADC4",
				// "cbcea88307e14350b108e26979ad71ba");
			}

			List<String> cntrNos = new ArrayList<String>();
			cntrNos.add(l120s20b.getCntrNo());

			List<Map<String, Object>> cmsList = eloandbBASEService
					.findCmsC100m01ByCntrNoForLgd(cntrNos);

			for (Map<String, Object> cmsMap : cmsList) {
				collKey.put(MapUtils.getString(cmsMap, "OID"),
						MapUtils.getString(cmsMap, "COLLKEY"));
			}

			request.put("collKey", collKey.toString());

			reqJSONObj.put("serviceId", "cmscollEDA_LGDService");
			reqJSONObj.put("vaildIP", "N");
			reqJSONObj.put("request", request.toString());

			LOGGER.info(reqJSONObj.toString());

			StringEntity stringEntity = new StringEntity(reqJSONObj.toString(),
					"UTF-8");
			stringEntity.setContentEncoding("UTF-8");
			httpPost.setEntity(stringEntity);

			HttpResponse response = null;
			response = httpclient.execute(httpPost);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 回傳內容
				String content = EntityUtils.toString(response.getEntity(),
						"UTF-8");// UTF-8 big5
				LOGGER.info(content);
				// {"rc":0,"rcmsg":"SUCCESS","message":"執行成功","CollRecycle":"729063574"}
				JSONObject responseJson = JSONObject.fromObject(content);
				String rc = responseJson.optString("rc", "1");
				if (Util.equals(rc, "0")) {
					// SUCCESS
					LOGGER.info("Response SUCCESS");
					cntrCollAmt = Util.parseBigDecimal(responseJson.optString(
							"CollRecycle", "0"));

					// FOR TEST
					// if (isTest) {
					// cntrCollAmt = new BigDecimal(50000000);
					// }

					l120s20b.setCollateralRecovery(cntrCollAmt);
					lmsLgdService.save(l120s20b);

				} else {
					// FAIL
					errorMsg = "Response FAIL:"
							+ responseJson.optString("message", "FAIL");

					LOGGER.error(errorMsg);
				}

			} else {
				errorMsg = "HTTP ERROR:"
						+ response.getStatusLine().getStatusCode();
			}
		} catch (IOException ioe) {
			errorMsg = StrUtils.getStackTrace(ioe);
			LOGGER.error(errorMsg);
		} catch (Exception e) {
			errorMsg = StrUtils.getStackTrace(e);
			LOGGER.error(errorMsg);
		} finally {

		}

		return errorMsg;
	}

	public String syncL140m01aData(L120S20B l120s20b) {
		String errorMsg = "";
		// 找額度明細表
		// FOR TEST
		if (isTest) {
			l120s20b.setHasCntrDoc("Y");
		}

		// 信保、信保保證成數
		l120s20b.setHeadItem1("N");
		l120s20b.setGutPercent(null);

		Map<String, Object> misln20 = misMISLN20Service.findByCntrNo(l120s20b
				.getCntrNo());
		if (misln20 != null && !misln20.isEmpty()) {
			String LNF020_FACT_TYPE = Util.trim(MapUtils.getString(misln20,
					"LNF020_FACT_TYPE"));
			BigDecimal LNF020_IPFD_RATE = Util.equals(
					Util.trim(MapUtils.getString(misln20, "LNF020_IPFD_RATE")),
					"") ? BigDecimal.ZERO : Util.parseBigDecimal(Util
					.trim(MapUtils.getString(misln20, "LNF020_IPFD_RATE")));
			if (Util.equals(LNF020_FACT_TYPE, "20")
					|| Util.equals(LNF020_FACT_TYPE, "50")) {
				if (LNF020_IPFD_RATE.compareTo(BigDecimal.ZERO) > 0) {
					l120s20b.setHeadItem1("Y");
					l120s20b.setGutPercent(LNF020_IPFD_RATE);
				}
			}
		}

		// 有無公司保證人
		l120s20b.setHasGuarantor("N");
		List<Map<String, Object>> ellngteeData = misEllngteeService
				.findByCntrNo(l120s20b.getCntrNo());

		for (Map<String, Object> ellData : ellngteeData) {

			String lngeid = "";
			String dupNo1 = "";
			String lngename = "";
			String lngeFlag = "";

			lngeid = Util.trim(MapUtils.getString(ellData, "LNGEID"));
			dupNo1 = MapUtils.getString(ellData, "DUPNO1");
			lngename = MapUtils.getString(ellData, "LNGENM");
			lngeFlag = MapUtils.getString(ellData, "LNGEFLAG");

			if (Util.equals(lngeFlag, "G") || Util.equals(lngeFlag, "N")) {
				// C: 共同借款人
				// D: 共同發票人　
				// E: 票據債務人（指金融交易之擔保背書）
				// G: 連帶保證人，擔保品提供人兼連帶保證人
				// L: 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人
				// S: 擔保品提供人
				// N: ㄧ般保證人

				if (StringUtils.length(lngeid) == 8
						|| Util.equals(lngeid.substring(2, 3), "Z")) {
					l120s20b.setHasGuarantor("Y");
				}

			}

		}

		if (Util.equals(l120s20b.getHasCntrDoc(), "Y")) {
			l120s20b.setGutRecoveryRate(Util.parseBigDecimal("95")); // 95%
			l120s20b.setPayOffPath(Util.parseBigDecimal("96"));
			l120s20b.setNegotiatePath(Util.parseBigDecimal("4"));
			l120s20b.setTurnPositivePath(Util.parseBigDecimal("0"));
			l120s20b.setNegotiateLossRate(Util.parseBigDecimal("12"));
			l120s20b.setTurnPositiveLossRate(Util.parseBigDecimal("0"));
			l120s20b.setIndirectCost(Util.parseBigDecimal("0.3"));
			l120s20b.setUnsecuredRecoveryRateY(Util.parseBigDecimal("44"));
			l120s20b.setUnsecuredRecoveryRateN(Util.parseBigDecimal("21"));
		}

		lmsLgdService.save(l120s20b);
		return errorMsg;

	}

	public String caculateLgd(L120S20B l120s20b) {
		String errorMsg = "";

		// if (Util.equals(l120s20b.getCntrNo(), "666666666666")) {
		// LOGGER.info("666666666666");
		// }

		// ●額度預期擔保品回收
		// 擔保品合計 + ( (額度EAD-擔保品合計) * 信保保證成數 * 95%(信保回收率 E33) )
		// =SUM($I$17:$I$26,E34)-SUMIF(J17:J26,"是",I17:I26)+(B9-(SUM($I$17:$I$26,E34)-SUMIF(J17:J26,"是",I17:I26)))*C$14*Lookups!$E$33

		BigDecimal expectSecuredRecovery = BigDecimal.ZERO;

		BigDecimal gutPercent = Util.notEquals(l120s20b.getHeadItem1(), "Y") ? BigDecimal.ZERO
				: l120s20b.getGutPercent().divide(new BigDecimal(100));

		// (額度EAD-擔保品合計) * 信保保證成數 * 95%(信保回收率 E33)
		BigDecimal gutRecoveryRate = l120s20b.getGutRecoveryRate().divide(
				new BigDecimal(100));

		// 擔保品合計
		BigDecimal collateralRecovery = l120s20b.getCollateralRecovery() == null ? BigDecimal.ZERO
				: l120s20b.getCollateralRecovery();

		// ( (額度EAD-擔保品合計) * 信保保證成數 * 95%(信保回收率 E33) )
		BigDecimal tmp_expectSecuredRecovery = (l120s20b.getCntrEad()
				.subtract(collateralRecovery)).multiply(gutPercent).multiply(
				gutRecoveryRate);

		expectSecuredRecovery = collateralRecovery
				.add(tmp_expectSecuredRecovery);

		// ●預期無擔保回收:
		// =IFERROR(IF(C44>B9,0,IF(F$4="有",(B9-C44)*Lookups!$E$38,IF(F$4="無",(B9-C44)*Lookups!$E$39,0))),0)
		BigDecimal expectUnsecuredRecovery = BigDecimal.ZERO;
		if (l120s20b.getCntrEad().compareTo(expectSecuredRecovery) <= 0) {
			// 額度EAD < 額度預期擔保品回收 = 0
			expectUnsecuredRecovery = BigDecimal.ZERO;
		} else {
			// 額度EAD > 額度預期擔保品回收
			BigDecimal tmp_expectUnsecuredRecovery = l120s20b.getCntrEad()
					.subtract(expectSecuredRecovery);
			BigDecimal unsecuredRecoveryRateY = l120s20b
					.getUnsecuredRecoveryRateY().divide(new BigDecimal(100));
			BigDecimal unsecuredRecoveryRateN = l120s20b
					.getUnsecuredRecoveryRateN().divide(new BigDecimal(100));
			if (Util.equals(l120s20b.getHasGuarantor(), "Y")) {
				// 有公司保證者 =>(額度EAD-額度預期擔保品回收) * 無擔保回收 - 有公司保證者 44%(E38)
				expectUnsecuredRecovery = tmp_expectUnsecuredRecovery
						.multiply(unsecuredRecoveryRateY);
			} else {
				// 無公司保證者 =>(額度EAD-額度預期擔保品回收) * 無擔保回收 - 無公司保證者 21%(E39)
				expectUnsecuredRecovery = tmp_expectUnsecuredRecovery
						.multiply(unsecuredRecoveryRateN);
			}
		}

		// ●清償損失率
		// =IFERROR(1-(SUM(額度預期擔保品回收,預期無擔保回收)/額度EAD),"")
		BigDecimal payOffLossRate = BigDecimal.ZERO;
		BigDecimal cntrEad = l120s20b.getCntrEad() == null ? BigDecimal.ZERO
				: l120s20b.getCntrEad();
		// 判斷分母(額度EAD)為0時直接給0
		if (cntrEad.compareTo(BigDecimal.ZERO) == 0) {
			payOffLossRate = BigDecimal.ZERO;
		} else {
			BigDecimal tmp_payOffLossRate = (expectSecuredRecovery
					.add(expectUnsecuredRecovery)).divide(cntrEad, 3,
					BigDecimal.ROUND_HALF_UP);

			payOffLossRate = BigDecimal.ONE.subtract(tmp_payOffLossRate);
		}

		// ●預期 LGD
		// 預期 LGD ＝ 清償路徑比例％ × （清償損失率+間接成本）＋ 協商路徑比例 × （協商損失率＋間接成本）
		// 清償路徑比例E43 96%
		// 協商路徑比例E44 4%
		// 轉正路徑比例E45 0%
		// 協商損失率E46 12%
		// 轉正損失率E47 0%
		// 間接成本E48 0.3%
		// =IFERROR(IF((Lookups!$E$43*(清償損失率E44+Lookups!$E$48)+Lookups!$E$44*(Lookups!$E$46+Lookups!$E$48+Lookups!$E$45*(Lookups!$E$47+Lookups!$E$48)))>1,100%,IF(Lookups!$E$43*(E44+Lookups!$E$48)+Lookups!$E$44*(Lookups!$E$46+Lookups!$E$48+Lookups!$E$45*(Lookups!$E$47+Lookups!$E$48))<0,0%,Lookups!$E$43*(E44+Lookups!$E$48)+Lookups!$E$44*(Lookups!$E$46+Lookups!$E$48+Lookups!$E$45*(Lookups!$E$47+Lookups!$E$48)))),"")

		BigDecimal expectLgd = BigDecimal.ZERO;

		BigDecimal payOffPath = l120s20b.getPayOffPath().divide(
				new BigDecimal(100)); // 清償路徑比例E43 96%
		BigDecimal negotiatePath = l120s20b.getNegotiatePath().divide(
				new BigDecimal(100)); // 協商路徑比例E44 4%
		BigDecimal turnPositivePath = l120s20b.getTurnPositivePath().divide(
				new BigDecimal(100)); // 轉正路徑比例E45 0%
		BigDecimal negotiateLossRate = l120s20b.getNegotiateLossRate().divide(
				new BigDecimal(100)); // 協商損失率E46 12%
		BigDecimal turnPositiveLossRate = l120s20b.getTurnPositiveLossRate()
				.divide(new BigDecimal(100)); // 轉正損失率E47 0%
		BigDecimal indirectCost = l120s20b.getIndirectCost().divide(
				new BigDecimal(100)); // 間接成本E48 0.3%

		// 預期LGD = (清償損失率+間接成本) * 清償路徑比例 + (協商損失率+間接成本) * 協商路徑比例 + (轉正損失率+間接成本)
		// * 轉正路徑比例

		// (清償損失率+間接成本) * 清償路徑比例
		BigDecimal tmp_expectLgd_1 = payOffPath.multiply(payOffLossRate
				.add(indirectCost));

		// (轉正損失率+間接成本) * 轉正路徑比例
		BigDecimal tmp_expectLgd_2 = turnPositivePath
				.multiply(turnPositiveLossRate.add(indirectCost));

		// (協商損失率+間接成本) * 協商路徑比例
		BigDecimal tmp_expectLgd_3 = negotiatePath.multiply(negotiateLossRate
				.add(indirectCost));

		BigDecimal tmp_expectLgd = tmp_expectLgd_1.add(tmp_expectLgd_2).add(
				tmp_expectLgd_3);

		// BigDecimal tmp_expectLgd_3 = negotiatePath.multiply(negotiateLossRate
		// .add(indirectCost).add(tmp_expectLgd_2));
		// BigDecimal tmp_expectLgd = tmp_expectLgd_1.add(tmp_expectLgd_3);
		// BigDecimal tmp_expectLgd = tmp_expectLgd_1.add(tmp_expectLgd_3);

		if (tmp_expectLgd.compareTo(BigDecimal.ONE) > 0) {
			expectLgd = BigDecimal.ONE;
		} else if (tmp_expectLgd.compareTo(BigDecimal.ZERO) < 0) {
			expectLgd = BigDecimal.ZERO;
		} else {
			expectLgd = tmp_expectLgd;
		}

		l120s20b.setExpectSecuredRecovery(expectSecuredRecovery); // 額度預期擔保品回收
		l120s20b.setExpectUnsecuredRecovery(expectUnsecuredRecovery); // 預期無擔保回收

		// 當擔保品遠大於分配到額度EAD時，清償損失率可能為很大的負值，造成寫入DB時錯誤，所以要先判斷大小
		BigDecimal savePayOffLossRate = payOffLossRate.multiply(new BigDecimal(
				100));
		if (savePayOffLossRate.compareTo(new BigDecimal(999.99)) > 0) {
			savePayOffLossRate = new BigDecimal(999.99);
		} else if (savePayOffLossRate.compareTo(new BigDecimal(-999.99)) < 0) {
			savePayOffLossRate = new BigDecimal(-999.99);
		} else {
			savePayOffLossRate = savePayOffLossRate;
		}
		l120s20b.setPayOffLossRate(savePayOffLossRate); // 清償損失率

		l120s20b.setExpectLgd(expectLgd.multiply(new BigDecimal(100))); // 預期
																		// LGD

		lmsLgdService.save(l120s20b);

		return errorMsg;
	}

}
