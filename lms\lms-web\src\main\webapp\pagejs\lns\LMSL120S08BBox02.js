var init120s08 = {
	fhandle : "lms1201formhandler",
	ghandle : "lms1201gridhandler",
	fhandle140 : "lms1401m01formhandler",
	ghandle140 : "lms1405gridhandler",
	versionDate : "2018-10-01",
	defButton : {
		"close" : function() {
			$.thickbox.close();
		}
	}
};

var API_2018 = {
    /** 明細檔 */
    openDetailBox : function(type, docOid, data) {
		//showRiskAdd,showCompetition

		var verNo = init120s08.versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
        var curr = $L120S08Form.find('#curr').val();
		
		if (curr == "TWD") {
			$(".showN").show();
			$("#showTitleSY").show();
			$("#showTitleSN").hide();
		} else {
			$(".showN").hide();
			$("#showTitleSY").hide();
			$("#showTitleSN").show();
		}

		var docOid = "";
		if (data) {
			docOid = data.oid;
		}

		if (type == 'new') {
			$L120S08Form.find('#memo').val(''); 
		}		

		var buttons = {};
		if (!thickboxOptions.readOnly) {
			buttons["sure"] = function() {
				var $form = $L120S08Form;

				// 先計算可以先將數字欄位塞0
				if (!API_2018.btnCaculate(init120s08.versionDate)) {
					//L120S08A.error14=申請減碼項目之每項減碼數字不符規定
					CommonAPI.showErrorMessage(i18n.lms1401s03["L120S08A.error14"]);
					return false;
				}

				if (!$form.valid()) {
					return false;
				}

				$.ajax({
					handler : init120s08.fhandle,
					data : {// 把資料轉成json
						formAction : "saveL120s08Data_20181001",
						docOid : docOid,
						mainId : responseJSON.mainId,
						curr : curr,
						versionDate:init120s08.versionDate,
					    verNo:verNo,
						L120S08Form : JSON.stringify($L120S08Form.serializeData())
					}
				}).done(function(data) {
					$.thickbox.close();
					L120s08BoxAPI._triggerMainGrid();
				}); // close ajax
			};

		} else {
			$L120S08Form.readOnlyChilds(true);
			$L120S08Form.find("button").hide();
		}

		buttons["cancel"] = function() {
			$.thickbox.close();
		};

		$("#inputL120s08Box_20181001").thickbox({
			// L120S08A.title2=合理性分析表
			title : i18n.lms1401s03['L120S08A.title2'],
			width : 1100,
			height : 600,
			modal : true,
			readOnly : thickboxOptions.readOnly,
			align : "center",
			i18n : i18n.def,
			valign : "bottom",
			buttons : buttons
		});
	},
	/**
	 * 引進關係戶往來彙總二維表集團評等與貢獻度合計 return true ,false
	 */
	btnApplyGroup : function(versionDate) {

		var verNo = versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
		
		var nowDate = new Date();
		var MM = nowDate.getMonth();
		var YY = nowDate.getFullYear();
		var SMM;
		var SYY;
		if (MM == 0) {
			MM = 12;
		}

		if (MM == 12) {
			SMM = MM - 5;
			YY = YY - 1;
			SYY = YY;
		} else if (MM > 5 && MM < 12) {
			SMM = MM - 5;
			SYY = YY;
		} else {
			SMM = MM + 12 - 5;
			SYY = YY - 1;
		}

		var $tL120S08Form1a = $("#tL120S08Form1a_"+verNo);
		$tL120S08Form1a.find("#queryDateE0").val(YY);
		$tL120S08Form1a.find("#queryDateE1").val(MM);

		$("#inputSearch_"+verNo).thickbox({ // 使用選取的內容進行彈窗
			title : i18n.lms1401s03["L120S08A.fieldTitle48"]+"/"+i18n.lms1401s03["L120S08A.fieldTitle49"], // L120S08A.title4=貢獻度查詢期間
			width : 600,
			height : 300,
			modal : true,
			align : 'center',
			valign : 'bottom',
			i18n : i18n.def,
			buttons : {
				"sure" : function() {					 
					if ($tL120S08Form1a.valid()) {
						if ($tL120S08Form1a.find("#queryDateE1").val() < 1
								|| $tL120S08Form1a.find("#queryDateE1").val() > 12) {
							CommonAPI.showMessage(i18n.lms1401s03["L120S08A.error07"]);
							return;
						} else if ($tL120S08Form1a.find("#queryDateE0").val() <= 0) {
							CommonAPI.showMessage(i18n.lms1401s03["L120S08A.error08"]);
							return;
						} else {
							
							var selectKind = $tL120S08Form1a.find("#selectKind").val();							
							$.thickbox.close();
							
							var qCustId = $L120S08Form.find("#custId").val();
							var qDupNo = $L120S08Form.find("#dupNo").val();

							$.ajax({
								handler : init120s08.fhandle,
								type : "POST",
								dataType : "json",
								data : {
									formAction : "queryL120S08BDWData_20181001",
									mainId : responseJSON.mainid,
									queryDateE0 : $tL120S08Form1a.find("#queryDateE0").val(),
									queryDateE1 : $tL120S08Form1a.find("#queryDateE1").val(),
									qCustId:qCustId,
									qDupNo:qDupNo,
									kind : selectKind
								}
							}).done(function(data) {
								var kindStr = "";
								if(selectKind == "1"){
									kindStr = "P";
								}else{
									kindStr = "G";
								}
								$L120S08Form.find("#halfYearDeposit"+kindStr+"_dscr").val(obj120.halfYearDeposit);
								$L120S08Form.find("#oneYearForeign"+kindStr+"_dscr").val(obj120.oneYearForeign);
								$L120S08Form.find("#oneYearWealth"+kindStr+"_dscr").val(obj120.oneYearWealth);
								$L120S08Form.find("#oneYearIncome"+kindStr+"_dscr").val(obj120.oneYearIncome);		
							});
						}
					}
				},
				"cancel" : function() {
					$.thickbox.close();
				}
			}
		});
	},
	/** 計算 */
	btnCaculate : function(versionDate) {
		var baseRate = 0;
		var verNo = versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);		
		
		if (!$L120S08Form.find("#baseRate").val()) {
			// L120S08A.error12=請先輸入
			// L120S08A.baseRate=基礎放款利利率
			CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12']
					+ i18n.lms1401s03["L120S08A.baseRate"]);
			return false;
		}
		baseRate = parseFloat($L120S08Form.find("#baseRate").val(), 10);

		var disYearRateSTot = parseFloat(0);
		var disYearRateNTot = parseFloat(0);
																												   
		// 沒有區分有擔無擔
		var lostDiffRate_disYearRate = $L120S08Form.find("#lostDiffRate_disYearRate").val();
		if (lostDiffRate_disYearRate) {
			lostDiffRate_disYearRate = parseFloat(lostDiffRate_disYearRate, 10);
		} else {
			lostDiffRate_disYearRate = 0;
			$L120S08Form.find("#lostDiffRate_disYearRate").val(0);
		}
		
		// 檢查有擔無擔是否要計算
		var hasS = false;
		var hasN = false;
		// 擔保利率計算(美元為利率計算)
		$("form[id='"+L120S08FormName+"'] input[id*='_disYearRateS']").each(function() {
			// $(this).attr('id') = this.id
			var disYearRateSId = this.id;
			if ($(this).val()) {
				hasS = true;
			}
		});

		// 無擔保利率計算(只有台幣要計算)
		var curr = $L120S08Form.find("#curr").val();
		if (curr != "USD") {
			$("form[id='"+L120S08FormName+"'] input[id*='_disYearRateN']").each(function() {
				// $(this).attr('id') = this.id
				var disYearRateNId = this.id;
				if ($(this).val()) {
					hasN = true;
				}
			});
		}

		var hasError = false;

		// 擔保利率計算(美元為利率計算)
		if (hasS) {
			// 一開始為基礎放款利利率
			disYearRateSTot = disYearRateSTot + baseRate + lostDiffRate_disYearRate;			
			 
			$("form[id='"+L120S08FormName+"'] input[id*='_disYearRateS']").each(function() {
				// $(this).attr('id') = this.id

				var disYearRateSId = this.id;
				var disYearRateS = 0;

				if ($(this).val()) {
					disYearRateS = parseFloat($(this).val(), 10);
				} else {
					$(this).val(0);
					disYearRateS = 0;
				}

				if (disYearRateSId == "reasonRate_disYearRateS" || disYearRateSId == "otherAdd_disYearRateS") {  
					// 合理利潤率要相加
					disYearRateSTot = disYearRateSTot + disYearRateS;
				} else if (disYearRateSId == "lowestRate_disYearRateS") {
					// 本案申請利率最低不處理
				} else {
					// 借戶各項往來實績：(每項減碼≦0.25%)
					if (disYearRateSId == "halfYearDepositP_disYearRateS"
							|| disYearRateSId == "oneYearForeignP_disYearRateS"
							|| disYearRateSId == "oneYearWealthP_disYearRateS"
							|| disYearRateSId == "oneYearIncomeP_disYearRateS") {
						if (disYearRateS > 0.25) {
							hasError = true;
							return false;
						}
					}
					
					// 個集團或關係人(不含借戶)貢獻：(每項減碼≦0.2%)
					if (disYearRateSId == "halfYearDepositG_disYearRateS"
						|| disYearRateSId == "oneYearForeignG_disYearRateS"
						|| disYearRateSId == "oneYearWealthG_disYearRateS"
						|| disYearRateSId == "oneYearIncomeG_disYearRateS") {
						if (disYearRateS > 0.2) {
							hasError = true;
							return false;
						}
					}
					
					// 列舉實績優異貢獻項目申請特別減碼 (每項≦0.5%)
					if (disYearRateSId == "specialMinus_disYearRateS") {
						if (disYearRateS > 0.5) {
							hasError = true;
							return false;
						}
					}

					// 其他要相減
					disYearRateSTot = disYearRateSTot - disYearRateS;
				}				
			});
			 
			$L120S08Form.find("#lowestRate_disYearRateS").val(disYearRateSTot.toFixed(5));
		}

		// 無擔保利率計算(只有台幣要計算)
		// 一開始為基礎放款利利率
		if (hasN) {
			disYearRateNTot = disYearRateNTot + baseRate + lostDiffRate_disYearRate;
			$("form[id='"+L120S08FormName+"'] input[id*='_disYearRateN']").each(function() {
				// $(this).attr('id') = this.id

				var disYearRateNId = this.id;
				var disYearRateN = 0;

				if ($(this).val()) {
					disYearRateN = parseFloat($(this).val(), 10);
				} else {
					$(this).val(0);
					disYearRateN = 0;
				}

				if (disYearRateNId == "reasonRate_disYearRateN"
					|| disYearRateNId == "otherAdd_disYearRateN") {  
					// 合理利潤率要相加
					disYearRateNTot = disYearRateNTot + disYearRateN;
				} else if (disYearRateNId == "lowestRate_disYearRateN") {
					// 本案申請利率最低不處理
				} else {
					// 借戶各項往來實績：(每項減碼≦0.25%)
					if (disYearRateNId == "halfYearDepositP_disYearRateN"
						|| disYearRateNId == "oneYearForeignP_disYearRateN"
							|| disYearRateNId == "oneYearWealthP_disYearRateN"
							|| disYearRateNId == "oneYearIncomeP_disYearRateN") {
						if (disYearRateN > 0.25) {
							hasError = true;
							return false;
						}
					}
					
					// 個集團或關係人(不含借戶)貢獻：(每項減碼≦0.2%)
					if (disYearRateNId == "halfYearDepositG_disYearRateN"
						|| disYearRateNId == "oneYearForeignG_disYearRateN"
							|| disYearRateNId == "oneYearWealthG_disYearRateN"
							|| disYearRateNId == "oneYearIncomeG_disYearRateN") {
						if (disYearRateN > 0.2) {
							hasError = true;
							return false;
						}
					}
					
					// 列舉實績優異貢獻項目申請特別減碼 (每項≦0.5%)
					if (disYearRateNId == "specialMinus_disYearRateN") {
						if (disYearRateN > 0.5) {
							hasError = true;
							return false;
						}
					}
					
					// 其他要相減
					disYearRateNTot = disYearRateNTot - disYearRateN;
				}
			});

			$L120S08Form.find("#lowestRate_disYearRateN").val(disYearRateNTot.toFixed(5));
		}

		if (hasError) {
			return false;
		} else {
			return true;
		}
	}
};

initDfd.done(function(json) {
    $("#btnApplyRateDscr").click(function() {
        L120s08BoxAPI.btnApplyRateDscr(init120s08.versionDate);
    });
    $("#btnApplyGroup").click(function() {
        API_2018.btnApplyGroup(init120s08.versionDate);
    });
    $("#btnCaculate").click(function() {
        if (!API_2018.btnCaculate(init120s08.versionDate)) {
            CommonAPI.showErrorMessage(i18n.lms1401s03["L120S08A.fieldTitle12"]);
        }
    });
    $("#btnClearS").click(function() {
        L120s08BoxAPI.btnClear('S', init120s08.versionDate);
    });
    $("#btnClearN").click(function() {
        L120s08BoxAPI.btnClear('N', init120s08.versionDate);
    });

    L120s08BoxAPI.gridviewConnomOtherSelect();
});