---------------------------------------------------------
-- LMS.C999S01B 個金約據書契約內容檔
---------------------------------------------------------
--DROP TABLE LMS.C999S01B;
CREATE TABLE LMS.C999S01B (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	PID           CHAR(32)      not null,
	TYPE          CHAR(3)       not null,
	JSONDATA      VARCHAR(3072),
	CREATOR       CHAR(6)      ,
	CREATE<PERSON><PERSON>    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C999S01B PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC999S01B01;
CREATE UNIQUE INDEX LMS.XC999S01B01 ON LMS.C999S01B   (MAINID, PID, TYPE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C999S01B IS '個金約據書契約內容檔';
COMMENT ON LMS.C999S01B (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	PID           IS 'PID', 
	TYPE          IS '契約項目', 
	JSONDATA      IS '契約內容', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
