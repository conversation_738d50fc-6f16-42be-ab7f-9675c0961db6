<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
    	<th:block th:fragment="LMS1401S02Panel04">
    		<div id="tabs-g" class="tabs">
				<ul>
					<li id="tab04_1">
						<a href="#tabs-g01">
							<b><th:block th:text="#{'L140S02Tab.4_01'}"><!--利(費)率登錄--></th:block></b>
						</a>
					</li>
					<li id="tab04_2">
						<a id="tab04_2a" href="#tabs-g02">
							<b><th:block th:text="#{'L140S02Tab.4_02'}"><!--利(費)率敘述--></th:block></b>
						</a>
					</li>
				</ul>
				<div class="tabCtx-warp">
					<div id="tabs-g01" class="content">
						<table class="tb2" width="100%" border="1" cellpadding="0" cellspacing="0">
							<tr class="hd1" style="text-align:left">
								<td>
									<button type="button" id="newRateBt" class="noHideBt">
										<span class="text-only">
											<th:block th:text="#{'button.add'}"><!--新增--></th:block>
										</span>
									</button>
									<button type="button" id="removeRate" class="noHideBt">
										<span class="text-only">
											<th:block th:text="#{'button.delete'}"><!--刪除--></th:block>
										</span>
									</button>
								</td>
							</tr>
							<tr>
								<td class="hd1" style="text-align:left"></td>
							</tr>
							<tr>
								<td>
									<div id="gridviewRate"></div>
								</td>
							</tr>
						</table>
					</div><!--end tabs-g01-->
					<div id="tabs-g02" class="content">
						<table class="tb2" width="100%" border="1" cellpadding="0" cellspacing="0">
							<tr class="hd1" style="text-align:left">
								<td colspan="2">
									<span style="display:none" class="caseSpan">
										<label>
											<input id="tab04" type="checkbox" class="caseBox tabBox"></input>
											<th:block th:text="#{'button.modify'}"><!--修改--></th:block>
										</label>
									</span>
									<select id="pageNum2" class="nodisabled">
										<option value="0" selected="selected">
											<th:block th:text="#{'L140M01b.printMain'}"><!--印於主表--></th:block>
										</option>
										<option value="1">
											<th:block th:text="#{'L140M01b.print01'}"><!--印於附表(一)--></th:block>
										</option>
										<option value="2">
											<th:block th:text="#{'L140M01b.print02'}"><!--印於附表(二)--></th:block>
										</option>
										<option value="3">
											<th:block th:text="#{'L140M01b.print03'}"><!--印於附表(三)--></th:block>
										</option>
									</select> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<button type="button" id="tabRateDrcLink">
										<span class="text-only">
											<th:block th:text="#{'other.toword'}"><!--組成文字串--></th:block>
										</span>
									</button>
									<button type="button" id="cleanRateDrcLink">
										<span class="text-only">
											<th:block th:text="#{'L140M01a.message120'}"><!--清除描述--></th:block>
										</span>
									</button>
								</td>
							</tr>
							<tr class="hd1" style="text-align:left">
								<td colspan="2">
									<span id="itemShowTr"></span><br><br>
								</td>
							</tr>
							<tr>
								<td colspan="2" align="left">
									<textarea id="itemDscr2" name="itemDscr2" cols="130" rows="8%" style="width: 859px; height: 167px;" readonly="readonly" class="caseReadOnly"></textarea>
								</td>
							</tr>
						</table>
					</div><!-- end tabs-g02-->
				</div><!-- tabCtx-warp-->
    		</div><!--end tabs-g-->
			<div id="newRateBox" style="display:none;">
				<!-- newRateBox thickBox -->
				<form id="L140M01FForm" name="L140M01FForm">
					<table width="800px" class="tb2" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td width="30%" class="hd1">
								<th:block th:text="#{'L140M01a.moneyAmt'}"><!--現請額度--></th:block>&nbsp;&nbsp;
							</td>
							<td>
								<span id="moneyNowSapn"></span><!--申請內容 現請額度的幣別-->
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L782M01A.loanTP'}"><!--科目--></th:block>&nbsp;&nbsp;
							</td>
							<td>
								<span id="loanTPListLocal"></span><!--ajax 取得前面選的授信科目-->
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01g.rate'}"><!--利率--></th:block>&nbsp;&nbsp;
							</td>
							<td>
								<span class="text-red">
									※<th:block th:text="#{'L140M01a.message52'}"><!--或等值其他外幣若未勾選則利率只能勾所選定的幣別，不得登錄其他貨幣--></th:block>！
								</span><br>
								<span id="rateBtSpan">
									<button type="button" id="twBT" class="noHideBt">
										<span class="text-only">
											<th:block th:text="#{'L140M01f.TWD'}"><!--新台幣--></th:block>
										</span>
									</button>
									<button type="button" id="usBT" class="noHideBt">
										<span class="text-only">
											<th:block th:text="#{'L140M01f.USD'}"><!--美元--></th:block>
										</span>
									</button>
									<button type="button" id="jpBT" class="noHideBt">
										<span class="text-only">
											<th:block th:text="#{'L140M01f.JPY'}"><!--日圓--></th:block>
										</span>
									</button>
									<button type="button" id="euBT" class="noHideBt">
										<span class="text-only">
											<th:block th:text="#{'L140M01f.EUR'}"><!--歐元--></th:block>
										</span>
									</button>
									<button type="button" id="cnBT" class="noHideBt">
										<span class="text-only">
											<th:block th:text="#{'l1401s02p04.003'}">人民幣</th:block>
										</span>
									</button>
									<button type="button" id="auBT" class="noHideBt">
										<span class="text-only">
											<th:block th:text="#{'l1401s02p04.001'}">澳幣</th:block>
										</span>
									</button>
									<button type="button" id="hkBT" class="noHideBt">
										<span class="text-only">
											<th:block th:text="#{'l1401s02p04.002'}">港幣</th:block>
										</span>
									</button>
									<button type="button" id="otherBT" class="noHideBt">
										<span class="text-only">
											<th:block th:text="#{'L140M01f.Other'}">雜幣</th:block>
										</span>
									</button>
									<button type="button" id="rateBT" class="noHideBt">
										<span class="text-only">
											<th:block th:text="#{'L140M01h.pa2Rate'}">費率</th:block>
										</span>
									</button>
								</span>
							</td>
						</tr>
						<tr>
							<td class="hd1"></td>
							<td id="end" height="100px">
								<button type="button" id="deleteL140M01N" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.delete'}"><!--刪除--></th:block>
									</span>
								</button>
								<button type="button" id="copyL140M01N" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.copy'}">複製</th:block>
									</span>
								</button>
								<div id="gridviewRate02"></div>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01h.pa2Rate'}"><!--費率--></th:block>&nbsp;&nbsp;
							</td>
							<td id="end">
								<textarea id="RateDrc" name="RateDrc" cols="80" rows="10" readonly="readonly" class="caseReadOnly"></textarea>
							</td>
						</tr>
					</table>
				</form>
			</div><!-- newRateBox thickBox END-->
			<div id="rateBox" style="display: none;">
				<!-- TW thickBox -->
				<form action="" id="L140M01NForm" name="L140M01NForm">
					<table class="tb2">
						<tr>
							<td class="hd1">
								<th:block th:text="#{'l1401s02p04.004'}">分段</th:block>&nbsp;&nbsp;
							</td>
							<td>
								<th:block th:text="#{'l1401s02p04.005'}">第</th:block>
								<select name="secNo" id="secNo" class="required"></select>
								<th:block th:text="#{'l1401s02p04.006'}">段</th:block>：<th:block th:text="#{'l1401s02p04.007'}">適用期間</th:block>
								<select name="secNoOp" id="secNoOp" class="required"></select>
								<span id="secMonSpan">
									<th:block th:text="#{'l1401s02p04.005'}">第</th:block>
									<input type="text" name="secBegMon" id="secBegMon" class="numeric required" positiveonly="false" integer="3" maxlength="3" size="2"></input>
									<th:block th:text="#{'l1401s02p04.008'}">月</th:block>~<th:block th:text="#{'l1401s02p04.005'}">第</th:block>
									<input type="text" name="secEndMon" id="secEndMon" class="numeric required" positiveonly="false" integer="3" maxlength="3" size="2"></input>
									<th:block th:text="#{'l1401s02p04.008'}">月</th:block>
								</span>
								<span id="secDateSpan">
									<!--J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業-->
									<span id="secBegDateSpan">
										<th:block th:text="#{'l1401s02p04.026'}">起日</th:block>：
										<input type="text" name="secBegDate" id="secBegDate" class="date required"></input>
									</span> ~
									<span id="secEndDateSpan">
										<th:block th:text="#{'l1401s02p04.009'}">迄日</th:block>：
										<input type="text" name="secEndDate" id="secEndDate" class="date required"></input>
									</span>
								</span>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'l1401s02p04.010'}">利率基礎</th:block>&nbsp;&nbsp;<br>
								<button type="button" id="loginRateBase" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'button.login'}"><!--登錄--></th:block>
									</span>
								</button>
								<input type="text" name="rateBase" id="rateBase" style="display:none"></input><br>
								<button type="button" id="queryRateBase" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'l1401s02p04.052'}"><!--查詢最新掛牌利率--></th:block>
									</span>
								</button>
							</td>
							<td>
								<select name="selectOtherBase" id="selectOtherBase" comboKey="L140M01N_OtherCurr" space="true" class="required"></select><br>
								<textarea name="otherRateDrc" id="otherRateDrc" cols="70" rows="3" maxlengthC="330"></textarea>
								<div id="ByOtherRate1" style="display:none">
									<th:block th:text="#{'l1401s02p04.033'}"><!--加減碼--></th:block>&nbsp;&nbsp;
									<select name="disYearOpOth" id="disYearOpOth" comboKey="lms1405s0204_count" comboType="2" space="true"></select>
									<span id="disYearRateSpanOth" style="display:none">
										<th:block th:text="#{'l1401s02p04.031'}"><!--年率--></th:block>
										<input type="text" id="disYearRateOth" name="disYearRateOth" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
									</span>
								</div>
								<span id="showRateText"></span>
								<table class="tb2" id="rateBaseGroupTb">
									<tr>
										<td class="hd1" style="width:25%">
											<th:block th:text="#{'l1401s02p04.011'}">是否以借款同天期顯示文字</th:block>&nbsp;&nbsp;
										</td>
										<td>
											<select name="rateSetAll" id="rateSetAll" comboKey="Common_YesNo" comboType="2" space="true"></select>
										</td>
									</tr>
									<tr>
										<td class="hd1">
											<th:block th:text="#{'l1401s02p04.012'}">貨幣市場利率群組</th:block>&nbsp;&nbsp;
										</td>
										<td>
											<span id="groupName" class="field"></span>
										</td>
									</tr>
								</table>
								<table class="tb2" id="rateBaseOpTb">
									<tr>
										<td class="hd1">
											<th:block th:text="#{'l1401s02p04.013'}">牌告利率</th:block>&nbsp;&nbsp;<br>
											<button type="button" id="reoladtRate">
												<span class="text-only">
													<th:block th:text="#{'button.pullinAgain'}">重新引進</th:block>
												</span>
											</button>
										</td>
										<td>
											<table>
												<tr>
													<td>
														<input type="text" name="tRateMin" id="tRateMin" disabled></input>
														<button type="button" id="tRateMinEdit" class="noHideBt">
															<span class="text-only">
																<th:block th:text="#{'button.modify'}"><!--修改--></th:block>
															</span>
														</button>
													</td>
													<td>
														<span class="hasMaxShow" style="display:none">～</span>
													</td>
													<td>
														<input type="text" name="tRateMax" id="tRateMax" disabled></input>
														<button type="button" id="tRateMaxEdit" class="noHideBt hasMaxShow">
															<span class="text-only">
																<th:block th:text="#{'button.modify'}"><!--修改--></th:block>
															</span>
														</button>
													</td>
												</tr>
												<tr>
													<td>
														<span class="text-red">
															<th:block th:text="#{'l1401s02p04.032'}"><!--(已調整 )原利率--></th:block>：
														</span>
														<input type="text" name="ctRateMin" id="ctRateMin" disabled></input>
													</td>
													<td>
														<span class="hasMaxShow" style="display:none">～</span>
													</td>
													<td>
														<span class="text-red">
															<th:block th:text="#{'l1401s02p04.032'}"><!--(已調整 )原利率--></th:block>：
														</span>
														<input type="text" name="ctRateMax" id="ctRateMax" disabled></input>
													</td>
												</tr>
											</table>
											<span class="text-red">
												<th:block th:text="#{'l1401s02p04.034'}"><!--顯示之利率資料基準日為前一營業日--></th:block>
											</span>
										</td>
									</tr>
									<tr>
										<td class="hd1">
											<th:block th:text="#{'l1401s02p04.033'}"><!--加減碼--></th:block>&nbsp;&nbsp;
										</td>
										<td>
											<select name="disYearOp" id="disYearOp" comboKey="lms1405s0204_count" comboType="2" space="true"></select>
											<span id="disYearRateSpan" style="display:none">
												<th:block th:text="#{'l1401s02p04.031'}"><!--年率--></th:block>
												<input type="text" id="disYearRate" name="disYearRate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
											</span>
										</td>
									</tr>
									<tr>
										<td class="hd1">
											<th:block th:text="#{'l1401s02p04.027'}"><!--目前敘作利率為--></th:block>(％)&nbsp;&nbsp;
										</td>
										<td>
											<table width="100%">
												<tr>
													<td width="45%">
														<span id="reRateMin" class="field"></span>
														<!--<input type="text" id="reRateMin" name="reRateMin" integer="2" fraction="5" maxlength="8" size="8"/>-->
													</td>
													<td width="10%">
														<span class="hasMaxShow" style="display:none">～</span>
													</td>
													<td width="45%">
														<span id="reRateMax" class="field"></span>
														<!--<input type="text" id="reRateMax" name="reRateMax" integer="2" fraction="5" maxlength="8" size="8"/>-->
													</td>
												</tr>
												<tr>
													<td colspan="3">
														<th:block th:text="#{'l1401s02p04.028'}"><!--組合文字時顯示此項目--></th:block>
														<input type="checkbox" name="reRateSelAll" id="reRateSelAll" value="Y"></input>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr></tr>
								</table>
								<table class="tb2" id="selfOpTb">
									<tr>
										<td class="hd1">
											<th:block th:text="#{'l1401s02p04.014'}">自訂利率參考指標</th:block>&nbsp;&nbsp;
										</td>
										<td>
											<select name="prRateSelect" id="prRateSelect" class="required"></select>
											<input type="hidden" name="prRate" id="prRate"></input>
										</td>
									</tr>
									<tr id="ratePeriodTr">
										<td class="hd1">
											<th:block th:text="#{'l1401s02p04.015'}">自訂利率天期</th:block>&nbsp;&nbsp;
										</td>
										<td>
											<input type="checkbox" name="ratePeriod" id="ratePeriod"></input>
										</td>
									</tr>
									<tr id="attRateTr">
										<td class="hd1">
											<th:block th:text="#{'l1401s02p04.049'}">固定利率</th:block>&nbsp;&nbsp;
										</td>
										<td>
											<input type="text" name="attRate" id="attRate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
										</td>
									</tr>
								</table>
								<table class="tb2" id="forRateUSD">
									<tr>
										<td class="hd1">
											<th:block th:text="#{'l1401s02p04.016'}">自訂利率參考指標U01、U02專用條件</th:block>&nbsp;&nbsp;
										</td>
										<td>
											<table class="tb2">
												<tr>
													<td>
														※<th:block th:text="#{'l1401s02p04.017'}">與借款人敘作之市場利率代碼</th:block>：
														<select name="usdMarket" id="usdMarket">
															<option value="1">SIBOR</option>
															<option value="2">LIBOR</option>
														</select>
													</td>
												</tr>
												<tr>
													<td>
														※<th:block th:text="#{'l1401s02p04.011'}">是否以借款同天期顯示文字</th:block>：
														<select name="usdSetAll" id="usdSetAll" comboKey="Common_YesNo" comboType="2"></select>
													</td>
												</tr>
												<tr id="usdMarketRateTr" style="display:none">
													<td>
														※(A)<th:block th:text="#{'l1401s02p04.017'}">與借款人敘作之市場利率代碼</th:block>：
														<input type="checkbox" name="usdMarketRateSelect" id="usdMarketRateSelect" class="required"></input>
														<input type="hidden" name="usdMarketRate" id="usdMarketRate"></input>
													</td>
												</tr>
												<tr>
													<td>
														※(B)<th:block th:text="#{'l1401s02p04.019'}">稅賦負擔</th:block>：
														<select name="usdRateTax" id="usdRateTax" comboKey="lms1401s0204_rateTax" comboType="2"></select>
														<span id="usdRateTaxSpan">
															，(C)<th:block th:text="#{'l1401s02p04.020'}">內含</th:block>：
															<input type="text" name="usdInsideRate" id="usdInsideRate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
														</span>
													</td>
												</tr>
												<tr>
													<td>
														※(D)<th:block th:text="#{'l1401s02p04.045'}">S/LBOR與TAIFX差額逾</th:block>
														<input type="text" name="usdDesRate" id="usdDesRate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>％
														<th:block th:text="#{'l1401s02p04.046'}">部分由借戶負擔 (整數2位.小數5位)</th:block>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
								<div id="disYearOpDiv2" style="display:none">
									<th:block th:text="#{'l1401s02p04.033'}"><!--加減碼--></th:block>&nbsp;&nbsp;
									<select name="disYearOp2" id="disYearOp2" comboKey="lms1405s0204_count" comboType="2" space="true"></select>
									<span id="disYearRateSpan2" style="display:none">
										<th:block th:text="#{'l1401s02p04.031'}"><!--年率--></th:block>
										<input type="text" id="disYearRate2" name="disYearRate2" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
									</span>
								</div>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'l1401s02p04.073'}">優惠利率</th:block>&nbsp;&nbsp;
							</td>
							<td>
								<!--J-110-0399_05097_B1001 Web eloan額度明細表登錄新台幣利率條件增加「適用新台幣七百億優惠利率檢核」及「適用新台幣競爭性利率檢核」檢核功能-->
								<table>
									<tr>
										<td>
											<input type="checkbox" name="inapplicability" id="inapplicability" value="X"></input>
											<th:block th:text="#{'l1401s02p04.074'}">不得適用「本行各項優惠利率」</th:block><br>
											<th:block th:text="#{'l1401s02p04.075'}">適用優惠利率</th:block>
											<select id="primeRatePlan" name="primeRatePlan"></select>
										</td>
										<td>
											<div class="forRateTWD">
												<button type="button" id="chkForPrimeRate_700e" class="noHideBt">
													<span class="text-only">適用新台幣七百億優惠利率檢核</span>
												</button>
												<button type="button" id="chkForPrimeRate_competitive" class="noHideBt">
													<span class="text-only">適用新台幣競爭性利率檢核</span>
												</button>
											</div>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr class="othRateHide">
							<td class="hd1">
								<th:block th:text="#{'l1401s02p04.021'}">利率補充說明</th:block>&nbsp;&nbsp;
							</td>
							<td>
								<textarea id="rateMemo" name="rateMemo" cols="70" rows="3" maxlengthC="1024"></textarea>
							</td>
						</tr>
						<tr class="othRateHide">
							<td class="hd1">
								<th:block th:text="#{'l1401s02p04.022'}">利率方式</th:block>&nbsp;&nbsp;
							</td>
							<td>
								<select id="rateKind" name="rateKind" class="required"></select>
							</td>
						</tr>
						<tr id="rateChgKindTr">
							<td class="hd1">
								<th:block th:text="#{'l1401s02p04.042'}">利率變動方式</th:block>&nbsp;&nbsp;
							</td>
							<td>
								<select id="rateChgKind" name="rateChgKind" class="required" comboKey="lms1401s0204_rateChgKind" comboType="2"></select>
								<input type="text" id="rateChg1" name="rateChg1" class="numeric required" positiveonly="false" integer="2" maxlength="2" size="2"></input>
							</td>
						</tr>
						<tr class="othRateHide">
							<td class="hd1">
								<th:block th:text="#{'l1401s02p04.023'}">收息方式</th:block>&nbsp;&nbsp;
							</td>
							<td>
								<select id="rateGetInt" name="rateGetInt" class="required"></select>
							</td>
						</tr>
						<tr class="othRateHide">
							<td class="hd1">
								<th:block th:text="#{'l1401s02p04.024'}">聯貸案專用說明</th:block>&nbsp;&nbsp;
							</td>
							<td>
								<textarea id="uionMemo" name="uionMemo" cols="70" rows="3" maxlengthC="1365"></textarea>
							</td>
						</tr>
						<tr class="othRateHide">
							<td class="hd1">
								<span id="showforUsd">﹝E﹞</span>
								<th:block th:text="#{'l1405s02p04.040'}">稅負洽收</th:block>&nbsp;&nbsp;
							</td>
							<td>
								<th:block th:text="#{'l1401s02p04.036'}">稅負由</th:block>
								<select name="rateTax" id="rateTax" comboKey="lms1401s0204_rateTax" comboType="2"></select>
								<th:block th:text="#{'l1401s02p04.037'}">負擔</th:block><br>
								<span id="rateTaxSpan" style="display:none">
									※<th:block th:text="#{'l1401s02p04.035'}">扣稅負擔碼</th:block>
									<input type="text" id="rateTaxCode" name="rateTaxCode" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
									<label>
										<input type="checkbox" id="rateTaxCodeDecideFuture" name="rateTaxCodeDecideFuture" value="Y"></input>
										<th:block th:text="#{'l1401s02p04.070'}">俟帳務系統起帳時再依客戶繳息方式建入</th:block>
									</label>
								</span>
							</td>
						</tr>
						<tr class="othRateHide">
							<td class="hd1">
								<th:block th:text="#{'l1401s02p04.025'}">限制條件/說明</th:block>１&nbsp;&nbsp;
							</td>
							<td>
								<!--J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率-->
								<span class="text-red showRateLimitMemo">
									<b><th:block th:text="#{'l1401s02p04.072'}">「限制條件」只限兩項, 不得再以非結構化之文字方式增加</th:block></b>
								</span><br>
								<select id="rateLimitType" name="rateLimitType" comboKey="lms1401s0204_rateLimitType"></select><br>
								<!--J-105-0088-001 Web e-Loan 企金授信系統利率條件無下限利率時，新增a-Loan是否需建置下限利率欄位並上傳a-Loan檢核。  -->
								<div id="showRateLimitNeedBuild" style="display:none;">
									<th:block th:text="#{'l1401s02p04.067'}">帳務系統是否需建置下限利率</th:block>：
									<select name="rateLimitNeedBuild" id="rateLimitNeedBuild" comboKey="Common_YesNo" comboType="2" class="required" space="true"></select><br>&nbsp;&nbsp;&nbsp;&nbsp;
									<th:block th:text="#{'l1401s02p04.068'}">1.本欄位不影響利率文字組成，僅作為列印額度檢核表時，顯示本案是否有下限利率之判斷條件。</th:block><br>&nbsp;&nbsp;&nbsp;&nbsp;
									<th:block th:text="#{'l1401s02p04.069'}">2.本欄位若為是，a-Loan開戶時提醒經辦必須要建下限利率。</th:block>
								</div><br>
								<table id="rateLimitTypeTable" name="rateLimitTypeTable" style="display:none; width:400px;">
									<tr>
										<td>
											<th:block th:text="#{'l1401s02p04.038'}"><!--下限利率--></th:block>：
											<select id="rateLimit" name="rateLimit" comboKey="lms1401s0204_rateLimit"></select><br>
											<th:block th:text="#{'l1401s02p04.039'}"><!--利率代碼--></th:block>：
											<select id="rateLimitCodeSelect" name="rateLimitCodeSelect" class="required"></select>
											<input type="hidden" name="rateLimitCode" id="rateLimitCode"></input>
											<span id="rateLimitCodeGroup">
												<br>※<th:block th:text="#{'l1401s02p04.011'}"><!--是否以借款同天期顯示文字--></th:block>：
												<select id="rateLimitSetAll" name="rateLimitSetAll" comboKey="Common_YesNo" comboType="2"></select>
												<br>※<th:block th:text="#{'l1401s02p04.012'}"><!--貨幣市場利率群組--></th:block>：
												<span id="rateLimitMarket" class="field"></span>
											</span>
											<span id="rateLimitCountRateSpan1_1">
												<br><select id="rateLimitCountType" name="rateLimitCountType" comboKey="lms1405s0204_count" comboType="2" space="true"></select>
												<span id="rateLimitCountRateSpan1_2" style="display:none">
													<th:block th:text="#{'l1401s02p04.031'}">年率</th:block>
													<input type="text" id="rateLimitCountRate" name="rateLimitCountRate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>%
												</span>
											</span>
											<span id="rateLimitCountPrSpan">
												<br>※<th:block th:text="#{'l1401s02p04.040'}">自定利率</th:block>：
												<input type="text" id="rateLimitCountPr" name="rateLimitCountPr" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>% 
											</span>
											<br>※<th:block th:text="#{'l1401s02p04.019'}">稅賦負擔</th:block>：
											<select id="rateLimitTax" name="rateLimitTax" comboKey="lms1401s0204_rateTax" comboType="2"></select>
											<span id="rateLimitTaxSpan" style="display:none">
												<th:block th:text="#{'l1401s02p04.041'}">除以</th:block>
												<input type="text" id="rateLimitTaxRate" name="rateLimitTaxRate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
											</span>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<!--J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率-->
						<!--下限利率2-->
						<tr class="othRateHide">
							<td class="hd1 showRateLimit2">
								<th:block th:text="#{'l1401s02p04.025'}">限制條件/說明</th:block>２&nbsp;&nbsp;
							</td>
							<td class="showRateLimit2">
								<!--J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率-->
								<span class="text-red">
									<b><th:block th:text="#{'l1401s02p04.072'}">「限制條件」只限兩項, 不得再以非結構化之文字方式增加</th:block></b>
								</span><br>
								<select id="rateLimitType2" name="rateLimitType2" comboKey="lms1401s0204_rateLimitType"></select><br>
								<table id="rateLimitTypeTable2" name="rateLimitTypeTable2" style="display:none; width:400px;">
									<tr>
										<td>
											<th:block th:text="#{'l1401s02p04.038'}"><!--下限利率--></th:block>：
											<select id="rateLimit2" name="rateLimit2" comboKey="lms1401s0204_rateLimit"></select><br>
											<th:block th:text="#{'l1401s02p04.039'}"><!--利率代碼--></th:block>：
											<select id="rateLimitCodeSelect2" name="rateLimitCodeSelect2" class="required"></select>
											<input type="hidden" name="rateLimitCode2" id="rateLimitCode2"></input>
											<!--J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率-->
											<!--雯莉洽金至忠確認，第二組下限利率不能有借款同天期-->
											<div style="display:none;">
												<span id="rateLimitCodeGroup2">
													<br>※<th:block th:text="#{'l1401s02p04.011'}"><!--是否以借款同天期顯示文字--></th:block>：
													<select id="rateLimitSetAll2" name="rateLimitSetAll2" comboKey="Common_YesNo" comboType="2"></select>
													<br>※<th:block th:text="#{'l1401s02p04.012'}"><!--貨幣市場利率群組--></th:block>：
													<span id="rateLimitMarket2" class="field"></span>
												</span>
											</div>
											<span id="rateLimitCountRateSpan2_1">
												<br><select id="rateLimitCountType2" name="rateLimitCountType2" comboKey="lms1405s0204_count" comboType="2" space="true"></select>
												<span id="rateLimitCountRateSpan2_2" style="display:none">
													<th:block th:text="#{'l1401s02p04.031'}">年率</th:block>
													<input type="text" id="rateLimitCountRate2" name="rateLimitCountRate2" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>%
												</span>
											</span>
											<span id="rateLimitCountPrSpan2">
												<br>※<th:block th:text="#{'l1401s02p04.040'}">自定利率</th:block>：
												<input type="text" id="rateLimitCountPr2" name="rateLimitCountPr2" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>%
											</span>
											<br>※<th:block th:text="#{'l1401s02p04.019'}">稅賦負擔</th:block>：
											<select id="rateLimitTax2" name="rateLimitTax2" comboKey="lms1401s0204_rateTax" comboType="2"></select>
											<span id="rateLimitTaxSpan2" style="display:none">
												<th:block th:text="#{'l1401s02p04.041'}">除以</th:block>
												<input type="text" id="rateLimitTaxRate2" name="rateLimitTaxRate2" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
											</span>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<!--ALL-IN BGN-->
						<tr class="othRateHide">
							<td class="hd1">
								<th:block th:text="#{'l1401s02p04.056'}"><!--all-in利率--></th:block>&nbsp;&nbsp;
							</td>
							<td>
								<table class="tb2"> <!--id="rateBaseOpTb"    id="allInRateOpTb" -->
									<tr>
										<td class="hd1" width="40%">
											<th:block th:text="#{'l1401s02p04.063'}"><!--試算--></th:block>
											<th:block th:text="#{'l1401s02p04.064'}"><!--利率--></th:block>1&nbsp;&nbsp;
										</td>
										<td width="60%">
											<table>
												<tr>
													<td>
														<input type="text" name="allInRateMinBf1" id="allInRateMinBf1" disabled></input>
														<!--disabled="disabled" -->
													</td>
													<td>
														<span class="hasMaxShowAllIn1" style="display:none">～</span>
													</td>
													<td>
														<input type="text" name="allInRateMaxBf1" id="allInRateMaxBf1" disabled></input>
														<!--disabled="disabled" -->
													</td>
													<td>
														<button type="button" id="computeAllIn1" onclick="RateAction.openComputeAllInBox('1')">
															<span class="text-only">
																<th:block th:text="#{'l1401s02p04.063'}"><!--試算--></th:block>
															</span>
															<!--一般牌告利率才有的功能-->
														</button>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td class="hd1" width="40%">
											<th:block th:text="#{'l1401s02p04.063'}"><!--試算--></th:block>
											<th:block th:text="#{'l1401s02p04.064'}"><!--利率--></th:block>2&nbsp;&nbsp;
										</td>
										<td width="60%">
											<table>
												<tr>
													<td>
														<input type="text" name="allInRateMinBf2" id="allInRateMinBf2" disabled></input>
														<!--disabled="disabled" -->
													</td>
													<td>
														<span class="hasMaxShowAllIn2" style="display:none">～</span>
													</td>
													<td>
														<input type="text" name="allInRateMaxBf2" id="allInRateMaxBf2" disabled></input>
														<!--disabled="disabled" -->
													</td>
													<td>
														<button type="button" id="computeAllIn2" onclick="RateAction.openComputeAllInBox('2')">
															<span class="text-only">
																<th:block th:text="#{'l1401s02p04.063'}"><!--試算--></th:block>
															</span>
															<!--一般牌告利率才有的功能-->
														</button>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td class="hd1" width="40%">
											<th:block th:text="#{'l1401s02p04.063'}"><!--試算--></th:block>
											<th:block th:text="#{'l1401s02p04.064'}"><!--利率--></th:block>3&nbsp;&nbsp;
										</td>
										<td width="60%">
											<table>
												<tr>
													<td>
														<input type="text" name="allInRateMinBf3" id="allInRateMinBf3" disabled></input>
														<!--disabled="disabled" -->
													</td>
													<td>
														<span class="hasMaxShowAllIn3" style="display:none">～</span>
													</td>
													<td>
														<input type="text" name="allInRateMaxBf3" id="allInRateMaxBf3" disabled></input>
														<!--disabled="disabled" -->
													</td>
													<td>
														<button type="button" id="computeAllIn3" onclick="RateAction.openComputeAllInBox('3')">
															<span class="text-only">
																<th:block th:text="#{'l1401s02p04.063'}"><!--試算--></th:block>
															</span>
															<!--一般牌告利率才有的功能-->
														</button>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td class="hd1">
											<th:block th:text="#{'l1401s02p04.061'}"><!--all-in利率為--></th:block>(％)&nbsp;&nbsp;
										</td>
										<td>
											<table>
												<tr>
													<td>
														<input type="text" name="allInRateMinAf" id="allInRateMinAf"></input>
														<!--disabled="disabled" -->
													</td>
													<td>
														<span class="hasMaxShowAllIn">～</span>
														<!--style="display:none"-->
													</td>
													<td>
														<input type="text" name="allInRateMaxAf" id="allInRateMaxAf"></input>
														<!--disabled="disabled" -->
													</td>
													<td rowspan="2">
														<button type="button" id="compareAllIn">
															<span class="text-only">
																<th:block th:text="#{'l1401s02p04.065'}"><!--最小試算利率--></th:block>
															</span>
														</button>
														<button type="button" id="compareAllInMax">
															<span class="text-only">
																<th:block th:text="#{'l1401s02p04.071'}"><!--最高試算利率--></th:block>
															</span>
														</button>
													</td>
												</tr>
												<tr>
													<td colspan="3">
														<th:block th:text="#{'l1401s02p04.028'}"><!--組合文字時顯示此項目--></th:block>
														<input type="checkbox" name="allInRateSelAll" id="allInRateSelAll" value="Y"></input>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr></tr>
								</table>
							</td>
						</tr>
						<!--ALL-IN END-->
						<tr>
							<td class="hd1">
								<button type="button" id="toRateWordBT" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'other.toword'}">組成文字串</th:block>
									</span>
								</button>
								<button type="button" id="cleanRateBT" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'btn.clean'}">清除欄位內容</th:block>
									</span>
								</button>
							</td>
							<td>
								<textarea name="rateDscr" id="rateDscr" cols="70" rows="6" readonly="readonly"></textarea>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'l1401s02p04.050'}">上傳用文字</th:block>
							</td>
							<td>
								<textarea name="upRateDscr" id="upRateDscr" cols="70" rows="6" readonly="readonly"></textarea>
							</td>
						</tr>
					</table>
				</form>
			</div>
			<!-- rateBox thickBox END-->
			<div id="rateBox2" style="display: none;">
				<!-- rateBox2 thickBox -->
				<form id="L140M01HForm" name="L140M01HForm">
					<table class="tb2">
						<tr>
							<td width="50%" class="hd1">
								<th:block th:text="#{'L140M01h.cpType'}"><!--商業本票保證--></th:block>(1-3)
								<button type="button" id="businessBT" class="noHideBt">
								<span class="text-only">
									<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
								</span>
								</button>
							</td>
							<td width="50%">
								<span id="radioSpan1">
									<span class="rateSpan1" style="display:none">
										1. <span class="word">
											<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>
										</span>
										<input type="text" id="cp1Rate" name="cp1Rate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
										<span class="word">
											％，<th:block th:text="#{'l1405s02p04.046'}"><!--每筆最低收費新台幣--></th:block>&nbsp;
										</span>
										<input type="text" id="cp1Fee" name="cp1Fee" class="numeric required" positiveonly="false" integer="5" fraction="0" maxlength="5" size="5"></input>
										<span class="word">
											&nbsp;<th:block th:text="#{'other.money'}"><!--元--></th:block>
										</span>
									</span>
									<span class="rateSpan2" style="display:none">
										2. <span class="word">
											<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>
										</span>
										<input type="text" id="cp2Rate1" name="cp2Rate1" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
										<span class="word">
											％，<th:block th:text="#{'l1405s02p04.047'}"><!--若由本行簽證承銷，則保證費率為年費率--></th:block>
										</span>
										<input type="text" id="cp2Rate2" name="cp2Rate2" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
										<span class="word">％</span>
									</span>
									<span class="rateSpan3" style="display:none">
										3. <textarea id="cpDes" name="cpDes" maxlength="1050" maxlengthC="350" cols="75" rows="5"></textarea>
										(<th:block th:text="#{'l1405s02p04.048'}"><!--可修改--></th:block>)
									</span>
								</span>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01h.cfType'}"><!--開發保證函--></th:block>
									(1-4) <button type="button" id="promiseBT" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
									</span>
								</button>
							</td>
							<td>
								<span id="radioSpan2">
									<span class="rateSpan1" style="display:none">
										1. <span class="word">
											<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>
										</span>
										<input type="text" id="cf1Rate" name="cf1Rate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
										<span class="word">
											％，<th:block th:text="#{'l1405s02p04.049'}"><!--以每--></th:block>
										</span>
										<select id="cf1Mon1" name="cf1Mon1" class="select nodisabled required">
											<option value="">
												--<th:block th:text="#{'plsSel'}"><!--請選擇--></th:block>--
											</option>
											<option value="1">1</option>
											<option value="3">3</option>
										</select>
										<span class="word">
											<th:block th:text="#{'l1405s02p04.050'}"><!--個月為一期，按期計收--></th:block>，
										</span>
										<select id="cf1MD" name="cf1MD" class="selectX lms1405s0204_monType2 nodisabled required"></select>
										<span id="cf1MDSelect" style="display:none">
											X<th:block th:text="#{'l1405s02p04.051'}"><!--月--></th:block>
											=<input type="text" id="cf1Mon2" name="cf1Mon2" class="number intputX" size="3" maxlength="3"></input>
											<th:block th:text="#{'l1405s02p04.052'}"><!--個月--></th:block>
										</span>
									</span>
									<span class="rateSpan2" style="display:none">
										2. <span class="word">
											<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>
										</span>
										<input type="text" id="cf2Rate" name="cf2Rate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
										<span class="word">
											％，<th:block th:text="#{'l1405s02p04.053'}"><!--按實際保證天數計算--></th:block>，
										</span>
										<select id="cf2MD" name="cf2MD" class="selectX lms1405s0204_monType2 nodisabled required"></select>
										<span id="cf2MDSelect" style="display:none">
											X<th:block th:text="#{'l1405s02p04.051'}"><!--月--></th:block>
											=<input type="text" id="cf2Mon" name="cf2Mon" class="number intputX " size="3" maxlength="3"></input>
											<th:block th:text="#{'l1405s02p04.052'}"><!--個月--></th:block>
										</span>
									</span>
									<span class="rateSpan3" style="display:none">
										3. <span class="word">
											<th:block th:text="#{'l1405s02p04.054'}"><!--國外銀行之複保費由客戶負擔--></th:block>
										</span>
									</span>
									<span class="rateSpan4" style="display:none">
										4. <textarea id="cfDes" name="cfDes" maxlength="1050" maxlengthC="350" cols="75" rows="5"></textarea>
										(<th:block th:text="#{'l1405s02p04.048'}"><!--可修改--></th:block>)
									</span>
								</span>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01h.cpyType'}"><!--公司債保證--></th:block>
									(1-4) <button type="button" id="companyBT" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
									</span>
								</button>
							</td>
							<td>
								<span id="radioSpan3">
									<span class="rateSpan1" style="display:none">
										1. <span class="word">
											<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>
										</span>
										<input type="text" id="cpy1Rate" name="cpy1Rate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
										<span class="word">
											％，<th:block th:text="#{'l1405s02p04.049'}"><!--以每--></th:block>
										</span>
										<select id="cpy1Mon1" class="nodisabled required">
											<option value="">
												--<th:block th:text="#{'plsSel'}"><!--請選擇--></th:block>--
											</option>
											<option value="1">1</option>
											<option value="3">3</option>
										</select>
										<span class="word">
											<th:block th:text="#{'l1405s02p04.050'}"><!--個月為一期，按期計收--></th:block>，
										</span>
										<select id="cpy1MD" name="cpy1MD" class="selectX lms1405s0204_monType2 nodisabled required"></select>
										<span id="cpy1MDSelect" style="display:none">
											X<th:block th:text="#{'l1405s02p04.051'}"><!--月--></th:block>
											=<input type="text" id="cpy1Mon2" name="cpy1Mon2" class="number intputX" size="3" maxlength="3"></input>
											<th:block th:text="#{'l1405s02p04.052'}"><!--個月--></th:block>
										</span>
									</span>
									<span class="rateSpan2" style="display:none">
										2. <span class="word">
											<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>
										</span>
										<input type="text" id="cpy2Rate" name="cpy2Rate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
										<span class="word">
											％，<th:block th:text="#{'l1405s02p04.053'}"><!--按實際保證天數計算--></th:block>，
										</span>
										<select id="cpy2MD" name="cpy2MD" class="selectX lms1405s0204_monType2 nodisabled required"></select>
										<span id="cpy2MDSelect" style="display:none">
											X<th:block th:text="#{'l1405s02p04.051'}"><!--月--></th:block>
											=<input type="text" id="cpy2Mon" name="cpy2Mon" class="number intputX" size="3" maxlength="3"></input>
											<th:block th:text="#{'l1405s02p04.052'}"><!--個月--></th:block>
										</span>
									</span>
									<span class="rateSpan3" style="display:none">
										3. <span class="word">
											<th:block th:text="#{'l1405s02p04.054'}"><!--國外銀行之複保費由客戶負擔--></th:block>
										</span>
									</span>
									<span class="rateSpan4" style="display:none">
										4. <textarea id="cpyDes" name="cpyDes" maxlength="1050" maxlengthC="350" cols="75" rows="5"></textarea>
										(<th:block th:text="#{'l1405s02p04.048'}"><!--可修改--></th:block>)
									</span>
								</span>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01h.paType'}"><!--承兌費率--></th:block>
								(1-4) <button type="button" id="thickRateBT" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'other.login'}"><!--登錄--></th:block>
									</span>
								</button>
							</td>
							<td>
								<span id="radioSpan4">
									<span class="rateSpan1" style="display:none">
										1. <span class="word">
											<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>
										</span>
										<input type="text" id="pa1Rate" name="pa1Rate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
										<span class="word">
											%，<th:block th:text="#{'l1405s02p04.055'}"><!--以每3個月為一期，按期計收--></th:block>，
										</span>
										<select id="pa1MD" name="pa1MD" class="selectX lms1405s0204_monType nodisabled required"></select>
										<span id="pa1MDSelect" style="display:none">
											X<th:block th:text="#{'l1405s02p04.051'}"><!--月--></th:block>
											=<input type="text" id="pa1Mon" name="pa1Mon" class="number intputX" size="3" maxlength="3"></input>
											<th:block th:text="#{'l1405s02p04.052'}"><!--個月--></th:block>
										</span>
									</span>
									<span class="rateSpan2" style="display:none">
										2. <span class="word">
											<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>
										</span>
										<input type="text" id="pa2Rate" name="pa2Rate" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
										<span class="word">
											%，<th:block th:text="#{'l1405s02p04.056'}"><!--按實際承兌日數計收--></th:block>，
										</span>
										<select id="pa2MD" name="pa2MD" class="selectX lms1405s0204_monType nodisabled required"></select>
										<span id="pa2MDSelect" style="display:none">
											X<th:block th:text="#{'l1405s02p04.051'}"><!--月--></th:block>
											=<input type="text" id="pa2Mon" name="pa2Mon" class="number intputX" size="3" maxlength="3"></input>
											<th:block th:text="#{'l1405s02p04.052'}"><!--個月--></th:block>
										</span>
									</span>
									<span class="rateSpan3" style="display:none">
										3. <span class="word">
											<th:block th:text="#{'l1405s02p04.057'}"><!--依規定計收--></th:block>
										</span>
									</span>
									<span class="rateSpan4" style="display:none">
										4. <textarea id="paDes" name="paDes" maxlength="1050" maxlengthC="350" cols="75" rows="5"></textarea><br>
										<!--<a href="#" id="wordBase">[<wicket:message key="l1405s02p04.058">詞庫</wicket:message>]</a>-->
									</span>
								</span>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<th:block th:text="#{'L140M01h.othDes'}"><!--其他--></th:block>
								<button type="button" id="wordBaseBt2" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'l1405s02p04.058'}"><!--詞庫--></th:block>
									</span>
								</button>
							</td>
							<td>
								<textarea id="othDes" name="othDes" maxlength="2010" maxlengthC="670" cols="75" rows="5"></textarea>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<button type="button" id="rateToWordBt" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'other.toword'}"><!--組成文字串--></th:block>
									</span>
								</button><br>
								<button type="button" id="clearRate" class="noHideBt">
									<span class="text-only">
										<th:block th:text="#{'btn.clean'}"><!--清除欄位內容--></th:block>
									</span>
								</button>
							</td>
							<td>
								<textarea id="rateDscr2" name="rateDscr2" cols="70" rows="6%" readonly="readonly" class="caseReadOnly"></textarea>
							</td>
						</tr>
					</table>
					<div id="rateChildrenBox" style="display:none;">
						<!-- rateChildrenBox thickBox --><!--商業本票保證 radio -->
						<table border="0" cellpadding="0" cellspacing="0" id="rateChildrenBox1" style="display:none">
							<tr>
								<td>
									<label>
										<input type="radio" id="cpType" name="cpType" value="1"></input>
										<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>「99%」，
										<th:block th:text="#{'l1405s02p04.059'}"><!--每筆最低收費新台幣「400」元--></th:block>
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="cpType" name="cpType" value="2"></input>
										<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>「99%」，
										<th:block th:text="#{'l1405s02p04.060'}"><!--若由本行簽證承銷，則保證費率為年費率--></th:block>「99%」
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="cpType" name="cpType" value="3"></input>
										<th:block th:text="#{'L140M01h.othDes'}"><!--其他--></th:block>
										(<th:block th:text="#{'l1405s02p04.048'}"><!--可修改--></th:block>)
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="cpType" name="cpType" value="c"></input>
										<span class="text-red">
											<b><th:block th:text="#{'btn.clean'}"><!--清除欄位內容--></th:block></b>
										</span>
									</label>
								</td>
							</tr>
						</table>
						<!--商業本票保證 radio end --><!--開發保證函 radio  -->
						<table border="0" cellpadding="0" cellspacing="0" id="rateChildrenBox2" style="display:none">
							<tr>
								<td>
									<label>
										<input type="radio" id="cfType" name="cfType" value="1"></input>
										<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>「99%」，
										<th:block th:text="#{'l1405s02p04.049'}"><!--以每--></th:block>「1/3」
										<th:block th:text="#{'l1405s02p04.050'}"><!--個月為一期，按期計收--></th:block>
										，「<th:block th:text="#{'l1405s02p04.061'}"><!--於開發日一次收足/採按「季/每半年/逐年/X月」預收--></th:block>」
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="cfType" name="cfType" value="2"></input>
										<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>「99%」，
										<th:block th:text="#{'l1405s02p04.053'}"><!--按實際保證天數計算--></th:block>
										，「<th:block th:text="#{'l1405s02p04.061'}"><!--於開發日一次收足/採按「季/每半年/逐年/X月」預收--></th:block>」
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="cfType" name="cfType" value="3"></input>
										<th:block th:text="#{'l1405s02p04.054'}"><!--國外銀行之複保費由客戶負擔--></th:block>
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="cfType" name="cfType" value="4"></input>
										<th:block th:text="#{'L140M01h.othDes'}"><!--其他--></th:block>
										(<th:block th:text="#{'l1405s02p04.048'}"><!--可修改--></th:block>)
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="cfType" name="cfType" value="c"></input>
										<span class="text-red">
											<b><th:block th:text="#{'btn.clean'}"><!--清除欄位內容--></th:block></b>
										</span>
									</label>
								</td>
							</tr>
						</table>
						<!--開發保證函 radio  end--><!--公司債保證 radio  -->
						<table border="0" cellpadding="0" cellspacing="0" id="rateChildrenBox3" style="display:none">
							<tr>
								<td>
									<label>
										<input type="radio" id="cpyType" name="cpyType" value="1"></input>
										<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>「99%」，
										<th:block th:text="#{'l1405s02p04.049'}"><!--以每--></th:block>「1/3」
										<th:block th:text="#{'l1405s02p04.050'}"><!--個月為一期，按期計收--></th:block>
										，「<th:block th:text="#{'l1405s02p04.061'}"><!--於開發日一次收足/採按「季/每半年/逐年/X月」預收--></th:block>」
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="cpyType" name="cpyType" value="2"></input>
										<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>「99%」，
										<th:block th:text="#{'l1405s02p04.053'}"><!--按實際保證天數計算--></th:block>
										，「<th:block th:text="#{'l1405s02p04.061'}"><!--於開發日一次收足/採按「季/每半年/逐年/X月」預收--></th:block>」
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="cpyType" name="cpyType" value="3"></input>
										<th:block th:text="#{'l1405s02p04.054'}"><!--國外銀行之複保費由客戶負擔--></th:block>
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="cpyType" name="cpyType" value="4"></input>
										<th:block th:text="#{'L140M01h.othDes'}"><!--其他--></th:block>
										(<th:block th:text="#{'l1405s02p04.048'}"><!--可修改--></th:block>)
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="cpyType" name="cpyType" value="c"></input>
										<span class="text-red">
											<b><th:block th:text="#{'btn.clean'}"><!--清除欄位內容--></th:block></b>
										</span>
									</label>
								</td>
							</tr>
						</table>
						<!--公司債保證 radio  end--><!--承兌費率 radio  -->
						<table border="0" cellpadding="0" cellspacing="0" id="rateChildrenBox4" style="display:none">
							<tr>
								<td>
									<label>
										<input type="radio" id="paType" name="paType" value="1"></input>
										<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>「99%」，
										<th:block th:text="#{'l1405s02p04.055'}"><!--以每3個月為一期，按期計收--></th:block>
										，「<th:block th:text="#{'l1405s02p04.062'}"><!--於開發日一次收足/採按「季/每半年/逐年/X月」預收--></th:block>」
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="paType" name="paType" value="2"></input>
										<th:block th:text="#{'L140M01h.cp1Rate'}"><!--年費率--></th:block>「99%」，
										<th:block th:text="#{'l1405s02p04.056'}"><!--按實際承兌日數計收--></th:block>
										，「<th:block th:text="#{'l1405s02p04.062'}"><!--於承兌時一次收足/採按「季/每半年/逐年/X月」預收--></th:block>」
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="paType" name="paType" value="3"></input>
										<th:block th:text="#{'l1405s02p04.057'}"><!--依規定計收--></th:block>
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="paType" name="paType" value="4"></input>
										<th:block th:text="#{'L140M01h.othDes'}"><!--其他--></th:block>
										(<th:block th:text="#{'l1405s02p04.048'}"><!--可修改--></th:block>)
									</label>
								</td>
							</tr>
							<tr>
								<td>
									<label>
										<input type="radio" id="paType" name="paType" value="c"></input>
										<span class="text-red">
											<b><th:block th:text="#{'btn.clean'}"><!--清除欄位內容--></th:block></b>
										</span>
									</label>
								</td>
							</tr>
						</table>
						<!--承兌費率 radio  end-->
					</div>
					<!-- rateChildrenBox thickBox END-->
				</form>
			</div>
			<!-- rateBox2 thickBox END-->
			<div id="rateBaseBox" style="display:none">
				<div>
					<input type="checkbox" name="tempRateBass" id="tempRateBass"></input>
				</div>
			</div>
			<div id="enterRateBox" style="display:none">
				<div id="enterRateBoxDiv">
					<form action="" id="enterRateForm">
						<th:block th:text="#{'l1401s02p04.029'}"><!--利率欄位目前之值為--></th:block>
						「<span id="enterRateInuptNow" name="enterRateInuptNow"></span>」
						<th:block th:text="#{'l1401s02p04.030'}"><!--請輸入新值--></th:block><br>
						<input type="text" name="enterRateInupt" id="enterRateInupt" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
					</form>
				</div>
			</div>

			<!--查詢利率基礎 thickbox  -->
			<div id="queryRateBaseBox" style="display:none">
				<div id="gridviewQueryRateBase"></div>
			</div><!--查詢利率基礎 thickbox  END-->

			<!--選擇掛牌利率 thickbox  -->
			<div id="selAllInRateBaseBox" style="display:none">
				<div id="ByOtherRate1AllIn">
					<th:block th:text="#{'l1401s02p04.033'}"><!--加減碼--></th:block>&nbsp;&nbsp;
					<select name="disYearOpOthAllIn" id="disYearOpOthAllIn" comboKey="lms1405s0204_count" comboType="2" space="true"></select>
					<span id="disYearRateSpanOthAllIn">
						<th:block th:text="#{'l1401s02p04.031'}"><!--年率--></th:block>
						<input type="text" id="disYearRateOthAllIn" name="disYearRateOthAllIn" class="numeric required" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
					</span>
				</div>
				<div id="gridviewQueryRateBaseAllIn"></div>
			</div><!--查詢利率基礎 thickbox  END-->

			<div id= "computeAllInBox" style="display:none">
				<!--XXXXX-->
				<table width="100%">
					<tr>
						<td>
							<table class="tb2"> <!--id="rateBaseOpTb"    id="allInRateOpTb" -->
								<tr>
									<td class="hd1" width="40%">
										<th:block th:text="#{'l1401s02p04.057'}"><!--目前利率(含加減碼)--></th:block>&nbsp;&nbsp;
									</td>
									<td width="60%">
										<table>
											<tr>
												<td>
													<input type="text" name="allInRateMinBfT" id="allInRateMinBfT"></input>
													<!--disabled="disabled" -->
												</td>
												<td>
													<span class="hasMaxShowAllInT">～</span>
													<!--style="display:none"-->
												</td>
												<td>
													<input type="text" name="allInRateMaxBfT" id="allInRateMaxBfT"></input>
													<!--disabled="disabled" -->
												</td>
											</tr>
										</table>
										<button type="button" id="selCurrentRate">
											<span class="text-only">
												<th:block th:text="#{'l1401s02p04.062'}"><!--引進目前敘作利率(含加減碼)--></th:block>
											</span><!--一般牌告利率才有的功能-->
										</button>
										<button type="button" id="selLimitRate">
											<span class="text-only">
												<th:block th:text="#{'l1401s02p04.066'}"><!--引進下限利率(含加減碼)--></th:block>
											</span>
										</button><br>
										<button type="button" id="selAllInRateBase">
											<span class="text-only">
												<th:block th:text="#{'l1401s02p04.058'}"><!--引進掛牌利率--></th:block>
											</span>
										</button>
									</td>
								</tr>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'l1401s02p04.059'}"><!--計算--></th:block>&nbsp;&nbsp;
									</td>
									<td>
										<span>
											<th:block th:text="#{'l1401s02p04.035'}"><!--扣稅負擔碼--></th:block>
											<input type="text" id="rateTaxCodeAllIn" name="rateTaxCodeAllIn" class="numeric" positiveonly="false" integer="2" fraction="5" maxlength="8" size="8"></input>
										</span>
										<button type="button" id="caculateTaxCode">
											<span class="text-only">
												<th:block th:text="#{'l1401s02p04.060'}"><!--除扣稅負擔碼--></th:block>
											</span>
											<!--自訂利率才有的功能-->
										</button>
									</td>
								</tr>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'l1401s02p04.061'}"><!--試算利率結果--></th:block>(％)&nbsp;&nbsp;
									</td>
									<td>
										<table>
											<tr>
												<td>
													<input type="text" name="allInRateMinAfT" id="allInRateMinAfT"></input>
													<!--disabled="disabled" -->
												</td>
												<td>
													<span class="hasMaxShowAllInT" style="display:none">～</span>
												</td>
												<td>
													<input type="text" name="allInRateMaxAfT" id="allInRateMaxAfT"></input>
													<!--disabled="disabled" -->
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr></tr>
							</table>
						</td>
					</tr>
				</table>
			</div>

			<!--J-110-0399_05097_B1001 Web eloan額度明細表登錄新台幣利率條件增加「適用新台幣七百億優惠利率檢核」及「適用新台幣競爭性利率檢核」檢核功能-->
			<div id="chkForPrimeRate700eBox" style="display:none">
				<div id="chkForPrimeRate700eDiv">
					<form action="" id="chkForPrimeRate700eForm">
						<table class="tb2">
							<!--id="rateBaseOpTb"    id="allInRateOpTb" -->
							<tr>
								<td class="hd1" width="40%"> 條件1 </td>
								<td width="60%">
									<span id="chkForPrimeRate700eMsg1"></span>
								</td>
							</tr>
							<tr>
								<td class="hd1"> 條件2 </td>
								<td>
									<table>
										<tr>
											<td> A.客戶利潤貢獻 </td>
											<td>
												最近一年(<span id="chkForPrimeRate700eBgnDate"></span>~<span id="chkForPrimeRate700eEndDate"></span>)<br> TWD &nbsp;&nbsp;
												<input type="text" name="chkForPrimeRate700eAmt1" id="chkForPrimeRate700eAmt1" class="numeric required" positiveonly="true" integer="17" fraction="2" maxlength="20" size="20"></input>元
											</td>
										</tr>
										<tr>
											<td> B.適用額度 </td>
											<td>
												TWD &nbsp;&nbsp;
												<input type="text" name="chkForPrimeRate700eAmt2" id="chkForPrimeRate700eAmt2" class="numeric required" positiveonly="true" integer="17" fraction="2" maxlength="20" size="20"></input>元<br>
												(客戶可適用七百億優惠利率之短期額度合計數)
											</td>
										</tr>
										<tr>
											<td> C.A／B <br>
												<button type="button" id="chkForPrimeRate700eCompute">
													<span class="text-only">計算</span>
												</button>
											</td>
											<td>
												<span id="chkForPrimeRate700eAmt3"></span>％
											</td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td class="hd1" width="40%"> 適用結果 </td>
								<td width="60%">
									<span id="chkForPrimeRate700eMsg2" class="text-red"></span>
								</td>
							</tr>
						</table>
					</form>
				</div>
			</div>
    	</th:block>
    </body>
</html>
