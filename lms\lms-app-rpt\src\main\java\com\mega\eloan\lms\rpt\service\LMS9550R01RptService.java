package com.mega.eloan.lms.rpt.service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.model.L201S99A;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.service.ICapService;

/**
 * <pre>
 * 報送卡務 Service
 * </pre>
 * 
 * @since 2012/7/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/16,<PERSON><PERSON><PERSON>,new
 *          </ul>
 */

public interface LMS9550R01RptService extends ICapService {
	/*
	 * Query
	 */
	/**
	 * 取得傳送卡務資料結果主檔
	 * 
	 * @param oid
	 *            String
	 * @return D201S99A
	 */
	L201S99A getL201S99AByOid(String oid);

	/* SAVE */
	/**
	 * 儲存主檔
	 * @param meta
	 *            D201S99A
	 */
	void saveS99A(L201S99A meta);
	
	/**
	 * 查詢
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	public IResult query(PageParameters params) throws CapException;
	
	/**
	 * 傳送檔案至卡務中心
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	public IResult sendToFTP(PageParameters params)
	throws CapException;
}
