package com.mega.eloan.lms.lrs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.lrs.panels.LMS1700FilterPanel;

/**
 * 受檢單位-待覆核
 */
@Controller
@RequestMapping("/lrs/lms1700v02")
public class LMS1700V02Page extends AbstractEloanInnerView {
	private static final String[] I18N_ARR = new String[]{"ui_lms1700.msg11", "ui_lms1700.msg12", "ui_lms1700.msg13"};
	public LMS1700V02Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {

		setGridViewStatus(RetrialDocStatusEnum.待覆核);
		addToButtonPanel(model, LmsButtonEnum.BatchApproved,
				LmsButtonEnum.Print);
		renderJsI18N(LMS1700V01Page.class);
		renderJsI18N(LMS1700M01Page.class, I18N_ARR);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/lrs/LMS1700V01Page');");
	}
	
	protected void addPanel(ModelMap model, PageParameters params, String panelId) {
		new LMS1700FilterPanel(panelId, true).processPanelData(model, params);
	}
}
