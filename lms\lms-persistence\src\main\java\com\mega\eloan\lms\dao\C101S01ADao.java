/* 
 * C101S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S01A;


/** 個金基本資料檔 **/
public interface C101S01ADao extends IGenericDao<C101S01A> {

	C101S01A findByOid(String oid);

	List<C101S01A> findByMainId(String mainId);

	C101S01A findByUniqueKey(String mainId, String custId, String dupNo);

	List<C101S01A> findByIndex01(String mainId, String custId, String dupNo);
	
	List<C101S01A> findByCustIdDupId(String custId,String DupNo);

	int deleteByOid(String oid);
}