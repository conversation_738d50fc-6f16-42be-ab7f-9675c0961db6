/* 
 * L140S02KDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S02K;

/** 貸款額度評等表 **/
public interface L140S02KDao extends IGenericDao<L140S02K> {

	L140S02K findByOid(String oid);

	List<L140S02K> findByMainId(String mainId);

	L140S02K findByUniqueKey(String mainId, Integer seq);

	List<L140S02K> findByIndex01(String mainId, Integer seq);
}