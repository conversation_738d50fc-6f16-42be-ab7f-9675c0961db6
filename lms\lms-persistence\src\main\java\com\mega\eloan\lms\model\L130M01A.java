/* 
 * L130M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 異常通報表主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L130M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class L130M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 企/個金案件<p/>
	 * 1企金<br/>
	 *  2個金
	 */
	@Column(name="DOCTYPE", length=1, columnDefinition="CHAR(1)")
	private String docType;

	/** 
	 * 負責人統編<p/>
	 * ※企金才需填寫
	 */
	@Column(name="CHAIRMANID", length=10, columnDefinition="VARCHAR(10)")
	private String chairmanId;

	/** 
	 * 負責人統編重複碼<p/>
	 * ※企金才需填寫
	 */
	@Column(name="CHAIRMANDUPNO", length=1, columnDefinition="CHAR(1)")
	private String chairmanDupNo;

	/** 
	 * 負責人姓名<p/>
	 * ※企金才需填寫<br/>
	 *  Miller modify at 2013/01/31<br/>
	 *  約40個中文字
	 */
	@Column(name="CHAIRMAN", length=120, columnDefinition="VARCHAR(120)")
	private String chairman;

	/** 
	 * 異常狀況<p/>
	 * 約1024個中文字
	 */
	@Column(name="PROCESS", length=3072, columnDefinition="VARCHAR(3072)")
	private String process;

	/** 
	 * 初次授信往來日期<p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="FIRSTDATE", columnDefinition="DATE")
	private Date firstDate;

	/** 
	 * 最後一次續約日期<p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="LASTDATE", columnDefinition="DATE")
	private Date lastDate;

	/** 
	 * 擔保品內容及押值<p/>
	 * 對擔保品的描述（約1024個中文字）
	 */
	@Column(name="COLLSTAT", length=3072, columnDefinition="VARCHAR(3072)")
	private String collStat;

	/** 
	 * 信保基金保證案件<p/>
	 * Y:是<br/>
	 *  N:否
	 */
	@Column(name="PROMISECASE", length=1, columnDefinition="CHAR(1)")
	private String promiseCase;

	/** 
	 * 保證成數<p/>
	 * (數字？文字？)成
	 */
	@Column(name="PROMISERATIO", length=3, columnDefinition="VARCHAR(3)")
	private String promiseRatio;

	/** 
	 * 額度<p/>
	 * 千元<br/>
	 *  約1024個中文字
	 */
	@Column(name="AMT", length=3072, columnDefinition="VARCHAR(3072)")
	private String amt;

	/** 
	 * 餘額<p/>
	 * 千元<br/>
	 *  約1024個中文字
	 */
	@Column(name="REMAIND", length=3072, columnDefinition="VARCHAR(3072)")
	private String remaind;

	/** 
	 * 本行曝險金額總計<p/>
	 * TWD千元
	 */
	@Column(name="TOTRISKAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal totRiskAmt;

	/** 
	 * 預估損失金額<p/>
	 * TWD千元
	 */
	@Column(name="LOSTAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal lostAmt;

	/** 
	 * 該戶在聯行之額度<p/>
	 * 千元<br/>
	 *  約1024個中文字
	 */
	@Column(name="BANKAMT", length=3072, columnDefinition="VARCHAR(3072)")
	private String bankAmt;

	/** 
	 * 該戶在聯行之餘額<p/>
	 * 千元<br/>
	 *  約1024個中文字
	 */
	@Column(name="BANKREMAIND", length=3072, columnDefinition="VARCHAR(3072)")
	private String bankRemaind;

	/** 
	 * 所屬企業集團<p/>
	 * 20個全型字
	 */
	@Column(name="GROUP", length=60, columnDefinition="VARCHAR(60)")
	private String group;

	/** 
	 * 該戶在同業之總餘額<p/>
	 * 千元<br/>
	 *  約1024個中文字
	 */
	@Column(name="SAMETOTAMT", length=3072, columnDefinition="VARCHAR(3072)")
	private String sameTotAmt;

	/** 集團代碼 **/
	@Column(name="GRPID", length=4, columnDefinition="VARCHAR(4)")
	private String grpId;

	/** 集團名稱 **/
	@Column(name="GRPNAME", length=60, columnDefinition="VARCHAR(60)")
	private String grpName;

	/** 
	 * 異常類別代碼<p/>
	 * 1:公司停止營業/工廠停工<br/>
	 *  2:公司、負責人或保證人發生退票或遭拒絕往來
	 */
	@Column(name="MDCLASS", length=3, columnDefinition="CHAR(3)")
	private String mdClass;

	/** 
	 * 同業擬(已)採取之措施<p/>
	 * 1024個全型字
	 */
	@Column(name="SAMEIDEA", length=3072, columnDefinition="VARCHAR(3072)")
	private String sameIdea;

	/** 
	 * 陳報及說明事項<p/>
	 * 1024個全型字
	 */
	@Column(name="REPORTDSCR", length=3072, columnDefinition="VARCHAR(3072)")
	private String reportDscr;

	/** 
	 * 是否有參貸行<p/>
	 * Y:是<br/>
	 *  N:否
	 */
	@Column(name="HASEBRID", length=1, columnDefinition="CHAR(1)")
	private String haseBrid;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	
	/** 
	 * 是否要傳送卡務中心<p/>
	 * Y:是<br/>
	 *  N:否
	 */
	@Column(name="NEEDSEND", length=1, columnDefinition="CHAR(1)")
	private String needSend;
	
	/** 
	 * 傳送卡務中心日期<p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="SENDDATE", columnDefinition="DATE")
	private Date sendDate;
	
	/** 
	 * 傳送卡務中心主檔MAINID(L201S99A的 MAINID)
	 */
	@Column(name="SENDMAINID", length=1, columnDefinition="CHAR(32)")
	private String sendMainId;
	
	/** 
	 * 結案註記
	 */
	@Column(name="CLOSEFG", length=1, columnDefinition="CHAR(1)")
	private String closeFg;
	
	/** 副知風控處時間 **/
	@Column(name="SEND912MAILTIME", columnDefinition="TIMESTAMP")
	private Date send912MailTime;
	
	
	/** 
	 * 本案是否屬於本行重大偶發事件通報作業要點應通報案件
	 */
	@Column(name="ISMAJOR", length=1, columnDefinition="CHAR(1)")
	private String isMajor;
	
	/** 
	 * 本案是否為該要點第二條
	 */
	@Column(name="MAJORPT2", length=1, columnDefinition="CHAR(1)")
	private String majorPt2;
	
	/** 
	 * 本案是否為該要點第三條
	 */
	@Column(name="MAJORPT3", length=1, columnDefinition="CHAR(1)")
	private String majorPt3;
	
	/** 
	 * 本案是否已依該要點第四條辦理
	 */
	@Column(name="MAJORPT4", length=1, columnDefinition="CHAR(1)")
	private String majorPt4;
	
	
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得企/個金案件<p/>
	 * 1企金<br/>
	 *  2個金
	 */
	public String getDocType() {
		return this.docType;
	}
	/**
	 *  設定企/個金案件<p/>
	 *  1企金<br/>
	 *  2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/** 
	 * 取得負責人統編<p/>
	 * ※企金才需填寫
	 */
	public String getChairmanId() {
		return this.chairmanId;
	}
	/**
	 *  設定負責人統編<p/>
	 *  ※企金才需填寫
	 **/
	public void setChairmanId(String value) {
		this.chairmanId = value;
	}

	/** 
	 * 取得負責人統編重複碼<p/>
	 * ※企金才需填寫
	 */
	public String getChairmanDupNo() {
		return this.chairmanDupNo;
	}
	/**
	 *  設定負責人統編重複碼<p/>
	 *  ※企金才需填寫
	 **/
	public void setChairmanDupNo(String value) {
		this.chairmanDupNo = value;
	}

	/** 
	 * 取得負責人姓名<p/>
	 * ※企金才需填寫<br/>
	 *  Miller modify at 2013/01/31<br/>
	 *  約40個中文字
	 */
	public String getChairman() {
		return this.chairman;
	}
	/**
	 *  設定負責人姓名<p/>
	 *  ※企金才需填寫<br/>
	 *  Miller modify at 2013/01/31<br/>
	 *  約40個中文字
	 **/
	public void setChairman(String value) {
		this.chairman = value;
	}

	/** 
	 * 取得異常狀況<p/>
	 * 約1024個中文字
	 */
	public String getProcess() {
		return this.process;
	}
	/**
	 *  設定異常狀況<p/>
	 *  約1024個中文字
	 **/
	public void setProcess(String value) {
		this.process = value;
	}

	/** 
	 * 取得初次授信往來日期<p/>
	 * YYYY-MM-DD
	 */
	public Date getFirstDate() {
		return this.firstDate;
	}
	/**
	 *  設定初次授信往來日期<p/>
	 *  YYYY-MM-DD
	 **/
	public void setFirstDate(Date value) {
		this.firstDate = value;
	}

	/** 
	 * 取得最後一次續約日期<p/>
	 * YYYY-MM-DD
	 */
	public Date getLastDate() {
		return this.lastDate;
	}
	/**
	 *  設定最後一次續約日期<p/>
	 *  YYYY-MM-DD
	 **/
	public void setLastDate(Date value) {
		this.lastDate = value;
	}

	/** 
	 * 取得擔保品內容及押值<p/>
	 * 對擔保品的描述（約1024個中文字）
	 */
	public String getCollStat() {
		return this.collStat;
	}
	/**
	 *  設定擔保品內容及押值<p/>
	 *  對擔保品的描述（約1024個中文字）
	 **/
	public void setCollStat(String value) {
		this.collStat = value;
	}

	/** 
	 * 取得信保基金保證案件<p/>
	 * Y:是<br/>
	 *  N:否
	 */
	public String getPromiseCase() {
		return this.promiseCase;
	}
	/**
	 *  設定信保基金保證案件<p/>
	 *  Y:是<br/>
	 *  N:否
	 **/
	public void setPromiseCase(String value) {
		this.promiseCase = value;
	}

	/** 
	 * 取得保證成數<p/>
	 * (數字？文字？)成
	 */
	public String getPromiseRatio() {
		return this.promiseRatio;
	}
	/**
	 *  設定保證成數<p/>
	 *  (數字？文字？)成
	 **/
	public void setPromiseRatio(String value) {
		this.promiseRatio = value;
	}

	/** 
	 * 取得額度<p/>
	 * 千元<br/>
	 *  約1024個中文字
	 */
	public String getAmt() {
		return this.amt;
	}
	/**
	 *  設定額度<p/>
	 *  千元<br/>
	 *  約1024個中文字
	 **/
	public void setAmt(String value) {
		this.amt = value;
	}

	/** 
	 * 取得餘額<p/>
	 * 千元<br/>
	 *  約1024個中文字
	 */
	public String getRemaind() {
		return this.remaind;
	}
	/**
	 *  設定餘額<p/>
	 *  千元<br/>
	 *  約1024個中文字
	 **/
	public void setRemaind(String value) {
		this.remaind = value;
	}

	/** 
	 * 取得本行曝險金額總計<p/>
	 * TWD千元
	 */
	public BigDecimal getTotRiskAmt() {
		return this.totRiskAmt;
	}
	/**
	 *  設定本行曝險金額總計<p/>
	 *  TWD千元
	 **/
	public void setTotRiskAmt(BigDecimal value) {
		this.totRiskAmt = value;
	}

	/** 
	 * 取得預估損失金額<p/>
	 * TWD千元
	 */
	public BigDecimal getLostAmt() {
		return this.lostAmt;
	}
	/**
	 *  設定預估損失金額<p/>
	 *  TWD千元
	 **/
	public void setLostAmt(BigDecimal value) {
		this.lostAmt = value;
	}

	/** 
	 * 取得該戶在聯行之額度<p/>
	 * 千元<br/>
	 *  約1024個中文字
	 */
	public String getBankAmt() {
		return this.bankAmt;
	}
	/**
	 *  設定該戶在聯行之額度<p/>
	 *  千元<br/>
	 *  約1024個中文字
	 **/
	public void setBankAmt(String value) {
		this.bankAmt = value;
	}

	/** 
	 * 取得該戶在聯行之餘額<p/>
	 * 千元<br/>
	 *  約1024個中文字
	 */
	public String getBankRemaind() {
		return this.bankRemaind;
	}
	/**
	 *  設定該戶在聯行之餘額<p/>
	 *  千元<br/>
	 *  約1024個中文字
	 **/
	public void setBankRemaind(String value) {
		this.bankRemaind = value;
	}

	/** 
	 * 取得所屬企業集團<p/>
	 * 20個全型字
	 */
	public String getGroup() {
		return this.group;
	}
	/**
	 *  設定所屬企業集團<p/>
	 *  20個全型字
	 **/
	public void setGroup(String value) {
		this.group = value;
	}

	/** 
	 * 取得該戶在同業之總餘額<p/>
	 * 千元<br/>
	 *  約1024個中文字
	 */
	public String getSameTotAmt() {
		return this.sameTotAmt;
	}
	/**
	 *  設定該戶在同業之總餘額<p/>
	 *  千元<br/>
	 *  約1024個中文字
	 **/
	public void setSameTotAmt(String value) {
		this.sameTotAmt = value;
	}

	/** 取得集團代碼 **/
	public String getGrpId() {
		return this.grpId;
	}
	/** 設定集團代碼 **/
	public void setGrpId(String value) {
		this.grpId = value;
	}

	/** 取得集團名稱 **/
	public String getGrpName() {
		return this.grpName;
	}
	/** 設定集團名稱 **/
	public void setGrpName(String value) {
		this.grpName = value;
	}

	/** 
	 * 取得異常類別代碼<p/>
	 * 1:公司停止營業/工廠停工<br/>
	 *  2:公司、負責人或保證人發生退票或遭拒絕往來
	 */
	public String getMdClass() {
		return this.mdClass;
	}
	/**
	 *  設定異常類別代碼<p/>
	 *  1:公司停止營業/工廠停工<br/>
	 *  2:公司、負責人或保證人發生退票或遭拒絕往來
	 **/
	public void setMdClass(String value) {
		this.mdClass = value;
	}

	/** 
	 * 取得同業擬(已)採取之措施<p/>
	 * 1024個全型字
	 */
	public String getSameIdea() {
		return this.sameIdea;
	}
	/**
	 *  設定同業擬(已)採取之措施<p/>
	 *  1024個全型字
	 **/
	public void setSameIdea(String value) {
		this.sameIdea = value;
	}

	/** 
	 * 取得陳報及說明事項<p/>
	 * 1024個全型字
	 */
	public String getReportDscr() {
		return this.reportDscr;
	}
	/**
	 *  設定陳報及說明事項<p/>
	 *  1024個全型字
	 **/
	public void setReportDscr(String value) {
		this.reportDscr = value;
	}

	/** 
	 * 取得是否有參貸行<p/>
	 * Y:是<br/>
	 *  N:否
	 */
	public String getHaseBrid() {
		return this.haseBrid;
	}
	/**
	 *  設定是否有參貸行<p/>
	 *  Y:是<br/>
	 *  N:否
	 **/
	public void setHaseBrid(String value) {
		this.haseBrid = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	
	/** 設定是否要傳送卡務中心 **/
	public void setNeedSend(String needSend) {
		this.needSend = needSend;
	}
	
	/** 取得是否要傳送卡務中心 **/
	public String getNeedSend() {
		return needSend;
	}
	
	/** 設定傳送卡務中心日期 **/
	public void setSendDate(Date sendDate) {
		this.sendDate = sendDate;
	}
	
	/** 取得傳送卡務中心日期 **/
	public Date getSendDate() {
		return sendDate;
	}
	
	/** 設定傳送卡務中心主檔MAINID **/
	public void setSendMainId(String sendMainId) {
		this.sendMainId = sendMainId;
	}
	
	/** 取得傳送卡務中心主檔MAINID **/
	public String getSendMainId() {
		return sendMainId;
	}
	
	/** 設定結案註記 **/
	public void setCloseFg(String closeFg) {
		this.closeFg = closeFg;
	}
	
	/** 取得結案註記 **/
	public String getCloseFg() {
		return closeFg;
	}
	
	/** 設定副知風控處時間 **/
	public void setSend912MailTime(Date send912MailTime) {
		this.send912MailTime = send912MailTime;
	}
	
	/** 取得副知風控處時間 **/
	public Date getSend912MailTime() {
		return send912MailTime;
	}
	
	/** 設定本案是否屬於本行重大偶發事件通報作業要點應通報案件 **/
	public void setIsMajor(String isMajor) {
		this.isMajor = isMajor;
	}
	
	/** 取得本案是否屬於本行重大偶發事件通報作業要點應通報案件 **/
	public String getIsMajor() {
		return isMajor;
	}
	
	/** 設定本案是否為該要點第二條 **/
	public void setMajorPt2(String majorPt2) {
		this.majorPt2 = majorPt2;
	}
	
	/** 取得本案是否為該要點第二條 **/
	public String getMajorPt2() {
		return majorPt2;
	}
	
	/** 設定本案是否為該要點第三條 **/
	public void setMajorPt3(String majorPt3) {
		this.majorPt3 = majorPt3;
	}
	
	/** 取得本案是否為該要點第三條 **/
	public String getMajorPt3() {
		return majorPt3;
	}
	
	/** 設定本案是否已依該要點第四條辦理 **/
	public void setMajorPt4(String majorPt4) {
		this.majorPt4 = majorPt4;
	}
	
	/** 取得本案是否已依該要點第四條辦理 **/
	public String getMajorPt4() {
		return majorPt4;
	}
}
