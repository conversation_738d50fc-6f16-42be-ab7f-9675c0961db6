package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C160S02ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C101S02A;
import com.mega.eloan.lms.model.C160S02A;

/** 動審額度明細表聯徵查詢結果 **/
@Repository
public class C160S02ADaoImpl extends LMSJpaDao<C160S02A, String>
        implements C160S02ADao {

    @Override
    public C160S02A findByOid(String oid) {
        ISearch search = createSearchTemplete();
        search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
        return findUniqueOrNone(search);
    }

    @Override
    public List<C160S02A> findByList(String mainId, String custId, String dupNo) {

        ISearch search = createSearchTemplete();
        if (mainId != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        }
        if (custId != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
        }
        if (dupNo != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
        }
        search.setMaxResults(Integer.MAX_VALUE);
        List<C160S02A> list = createQuery(search).getResultList();
        return list;
    }

    @Override
    public C160S02A findByItem(String mainId, String custId, String dupNo, String item) {
        ISearch search = createSearchTemplete();
        if (mainId != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
            if (custId != null) {
                search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
            }
            if (dupNo != null) {
                search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
            }
            if (item != null) {
                search.addSearchModeParameters(SearchMode.EQUALS, "ejcicItem", item);
            }
        }
        return findUniqueOrNone(search);
    }
    
    @Override
	public List<C160S02A> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<C160S02A> list = createQuery(C160S02A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<C160S02A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
        if (mainId != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        }
        search.setMaxResults(Integer.MAX_VALUE);
        List<C160S02A> list = createQuery(search).getResultList();
        return list;
	}

	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C160S02A.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
}