var initDfd = $.Deferred(), inits = {
    fhandle: "lms140mm01formhandler",
    ghaddle: "lms140mgridhandler"
};
// 驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    // auth.readOnly ||
    if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
        return true;
    }
    return false;
}

$(document).ready(function(){

    $.form.init({
        formHandler: inits.fhandle,
        formPostData: {// 把form上貼上資料
            formAction: "queryL140mm1a",
            oid: responseJSON.oid
        },
        loadSuccess: function(json){
						
			$("#L140M01M_landBuild").hide();
			
			// 央行註記異動作業 - 新增全新央行註記資料, 隱藏 建案完工未出售房屋融資註記 及 撥貸未動工興建之空地貸款控管註記
			if(json.editType == 'C'){
				$("#L140M01M_Remain_House_loan").hide();
				$("#L140M01M_clear_land_ctrl").hide();
				$("#addBrandNew").show();
			}
			
			var $form = $("#" + LMS140M01MAction.formId);
			LMS140M01MAction.formData = json;
			LMS140M01MAction.init($form, json.version);
			LMS140M01MAction.isInject = true;
			$form.injectData(json);
			//載入 J-109-0226 建案餘屋及貸款資料控制 default value
			LMS140M01MAction.initFinancingDataForUnsoldHouseInFinishedConstruction('1');
			//撥貸未動工興建之空地貸款控管註記設定
			settingEmptyLandLoanColumn(json);
			ClearLandLoanOfNotConstructed.setInitialValue($form, json); 
            LMS140M01MAction.isInject = false;
			$form.find("select").trigger("change", "init");
            LMS140M01MAction.setShareCollAmt($form);
//            $form.readOnlyChilds(isforQuery, "#prodClass");

            responseJSON.mainDocStatus = json.docStatusVal;
            responseJSON.unitLoanCase = json.unitLoanCase;
            var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
            initDfd.resolve(json, auth);
            responseJSON.mainId = json.mainId;

            $("#check1").show();
            if (responseJSON.page == "01") {
                $("#bossId").html(json.bossId);
            }
            // $("label").lockDoc();
            if (checkReadonly()) {
                $(".readOnlyhide").hide();
                $("form").lockDoc();
            }
			$("#openBox_L140M01M").show();
			
			$form.find("[name=cbcCase]").change(function(){
				var value = $(this).val();
				if(value == '1' || value == '5'){
					$form.find("#timeVal").val(LMS140M01MAction.formData.totalTimeVal);
				}
				CentralBankMortgageLoanManagement.switchLatestStartDate($form, json.version);
            });
			
			CentralBankMortgageLoanManagement.switchIs3rdHighHouseOption(json.version, json.peopleMortgageDetail, $form);
        }
    });// close form init
    
// 呈主管覆核 選授信主管人數
    $("#numPerson").change(function(){
        $('#bossItem').empty();
        var value = $(this).val();
        if (value) {
            var html = '';
            for (var i = 1; i <= value; i++) {
                var name = 'boss' + i;
                html += i + '. ' // +i18n.cls1161m01['manager.L3']
                // || '授信主管'
                html += '<select id="' + name + '" name="boss"' +
                '" class="required" CommonManager="kind:2;type:2" />';
                html += '<br/>';
            }
            $('#bossItem').append(html).find('select').each(function(){
                $(this).setItems({
                    item: item,
                    format: "{value} {key}"
                });
            });
        }
        
    });

    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        saveData(true);
    }).end().find("#btnDelete").click(function(){
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "delete",
                        mainOid: $("#oid").val()
                    }
                });
            }
        });
    }).end().find("#btnTest").click(function(){
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "TETSMIS",
                mainOid: $("#mainOid").val()
            },
            success: function(){
            }
        });
    }).end().find("#btnSend").click(function(){
        saveData(false, sendBoss);
    }).end().find("#btnAccept").click(function(){
        flowAction({
            flowAction: true
        });
    }).end().find("#btnCheck").click(function(){
        openCheck();
    }).end().find("#btnPrint").click(function(){
        if (checkReadonly()) {
            printAction();
        }
        else {
            // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    saveData(false, printAction);
                }
            });
        }
    });
    
    // 列印動作
    function printAction(){
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                mainOid: responseJSON.oid,   //$("#oid").val(),
                fileDownloadName: "lms140mr01.pdf",
                serviceName: "lms140mr01rptservice"
            }
        });
    }

    // 儲存的動作
    function saveData(showMsg, tofn){
    // 為檢查UI的值是否皆無異常
		if($("#LMS140M01MForm").valid()==false){
			return;
		}
		
                var allresult = {};
                var localresult = {};
                var selfresult = {};
                FormAction.open = true;
                $.ajax({
                    handler: 'lms140mm01formhandler',
                    data: {// 把資料轉成json
                        formAction: "saveL140mm1a"
                        , page: responseJSON.page
                        , txCode: responseJSON.txCode
                        , showMsg: showMsg
                        //selfresult: JSON.stringify(selfresult)
                        //localresult: JSON.stringify(localresult)
                        //allresult: JSON.stringify(allresult)
                    },
                    success: function(obj){
                        if (responseJSON.page == "01") {
                            $('body').injectData(obj);
                        }
                        
                        if (obj.tReCheckData == "Y") {
                            //L140MM1B.tReCheckData=本案屬自用住宅但其風險權數卻選擇100%，提醒！！請再次確認是否無誤！</br>(PS!並非自用住宅其風險權數即為45%，請依個案情況正確判斷！)
                            //API.showMessage(i18n.lms140mm01["L140MM1B.tReCheckData"]);
                        }
                        
                        
                        CommonAPI.triggerOpener("gridview", "reloadGrid");
                        if ($("#mainOid").val()) {
                            setRequiredSave(false);
                        }
                        else {
                            setRequiredSave(true);
                        }
                        
                        // 執行列印
                        if (!showMsg && tofn) {
                            tofn();
                        }
                    }
                });
    }
    
    //    
    function flowAction(sendData){
        $.ajax({
            handler: inits.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: $("#mainOid").val()
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                API.showPopMessage(i18n.def["runSuccess"], window.close);
            }
        });
    }
    var item;
    // 呈主管 - 編製中
    function sendBoss(){
        $.ajax({
            handler: "lms140mm01formhandler",
            action: "checkData",
            data: {},
            success: function(json){
                $('#managerItem').empty();
                $('#bossItem').empty();
                item = json.bossList;
                var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"/>';
                $('#bossItem').append(bhtml).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:2" />';
                $('#managerItem').append(html).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                
                // L140MM1B.message27=是否呈主管覆核？
                CommonAPI.confirmMessage(i18n.lms140mm01["L140MM1B.message27"], function(b){
                    if (b) {
                        $("#selectBossBox").thickbox({
                            // L140MM1B.bt14=覆核
                            title: i18n.lms140mm01['L140MM1B.bt14'],
                            width: 500,
                            height: 300,
                            modal: true,
                            readOnly: false,
                            valign: "bottom",
                            align: "center",
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                
                                    var selectBoss = $("select[name^=boss]").map(function(){
                                        return $(this).val();
                                    }).toArray();
                                    
                                    for (var i in selectBoss) {
                                        if (selectBoss[i] == "") {
                                            // L140MM1B.error2=請選擇
                                            // L140MM1B.bossId=授信主管
                                            return CommonAPI.showErrorMessage(i18n.lms140mm01['L140MM1B.error2'] +
                                            i18n.lms140mm01['L140MM1B.bossId']);
                                        }
                                    }
                                    if ($("#manager").val() ==
                                    "") {
                                        // L140MM1B.error2=請選擇
                                        // L140MM1B.managerId=經副襄理
                                        return CommonAPI.showErrorMessage(i18n.lms140mm01['L140MM1B.error2'] +
                                        i18n.lms140mm01['L140MM1B.managerId']);
                                    }
                                    // 驗證是否有重複的主管
                                    if (checkArrayRepeat(selectBoss)) {
                                        // L140MM1B.message31=主管人員名單重複請重新選擇
                                        return CommonAPI.showErrorMessage(i18n.lms140mm01['L140MM1B.message31']);
                                    }
                                    
                                    flowAction({
                                        page: responseJSON.page,
                                        saveData: true,
                                        selectBoss: selectBoss,
                                        manager: $("#manager").val()
                                    });
                                    $.thickbox.close();
                                    
                                },
                                
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                });
            }
        });
    }
    
    // 待覆核 - 覆核
    function openCheck(){
        // thickboxOptions.readOnly= false;
        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            // L140MM1B.bt14=覆核
            title: i18n.lms140mm01['L140MM1B.bt14'],
            width: 100,
            height: 100,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        // L140MM1B.error2=請選擇
                        return CommonAPI.showMessage(i18n.lms140mm01['L140MM1B.error2']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            // 一般退回到編製中01O
                            // L140MM1B.message32=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.lms140mm01['L140MM1B.message32'], function(b){
                                if (b) {
                                    flowAction({
                                        flowAction: false
                                    });
                                }
                            });
                            
                            break;
                        case "3":
                            // L140MM1B.message34=該案件是否確定執行核定作業
                            CommonAPI.confirmMessage(i18n.lms140mm01["L140MM1B.message34"], function(b){
                                if (b) {
                                    checkDate();
                                }
                            });
                            break;
                    }
                    
                },
                
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    // 輸入核定日期視窗
    function checkDate(){
        // 帶入今天日期
        $("#forCheckDate").val(CommonAPI.getToday());
        $("#openChecDatekBox").thickbox({ // 使用選取的內容進行彈窗
            // L140MM1B.message38 = 請輸入核定日
            title: i18n.lms140mm01['L140MM1B.message38'],
            width: 100,
            height: 100,
            modal: true,
            valign: "bottom",
            align: "center",
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var forCheckDate = $("#forCheckDate").val();
                    if ($.trim(forCheckDate) == "") {
                        // L140MM1B.message38 = 請輸入核定日
                        return CommonAPI.showErrorMessage(i18n.lms140mm01['L140MM1B.message38']);
                    }
                    flowAction({
                        flowAction: true,
                        checkDate: forCheckDate
                    });
                    $.thickbox.close();
                },
                
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    // 檢查陣列內容是否重複
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        return false;
    }
	
	// 因應有上解除控管日後, 就不會認定為空地貸款, 但分行還是須維護實際動工日, 所以目前無論本案是否為撥貸未動工興建之空地貸款控管對象是或否皆開放修改
	function settingEmptyLandLoanColumn(json){

//		if(json.isClearLand == "Y"){
			$("#showClearLand").show();
//		}
//		else{
//			$("#showClearLand").hide();
//		}
		
		if(json.adoptFg){
			var vals = json.adoptFg.split("|");
            for (var i in vals) {
                $("[name=adoptFg][value=" + vals[i] + "]").attr("checked", true);
            }
		}
		$(".fileMaintainNoEdit").attr("readonly", true).attr("disabled", true);
		$(".removeDate").datepicker("destroy");
		$("#btnApplyClearLandRoa").hide();
		$("#btnApplyIsLegal").hide();
		$("#btnApplyClearLand").hide();
	 }
    
});
