/* 
 * LMS9990M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.ui.ModelMap;
import com.iisigroup.cap.component.PageParameters;

import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.model.L999M01A;

/**
 * <pre>
 * // * 約據書-連帶保證書
 * </pre>
 * 
 * @since 2012/02/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/02/21,ICE,new
 *          </ul>
 */

@Controller
@RequestMapping("/ctr/lms9990m05/{page}")
public class LMS9990M05Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS9990M05Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		// 依權限設定button
		renderJsI18N(LMS9990M05Page.class);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L999M01A.class;

	}

}
