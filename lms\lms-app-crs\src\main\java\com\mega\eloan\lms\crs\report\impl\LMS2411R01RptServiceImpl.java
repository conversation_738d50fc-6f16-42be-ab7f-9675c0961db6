package com.mega.eloan.lms.crs.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import com.iisigroup.cap.component.PageParameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.jcs.common.report.SubReportParam;

import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.pages.LMS2411M01Page;
import com.mega.eloan.lms.crs.report.LMS2411R01RptService;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.mfaloan.service.MisLNF040Service;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.C241M01C;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.service.BranchService;

@Service("lms2411r01rptservice")
public class LMS2411R01RptServiceImpl implements FileDownloadService, LMS2411R01RptService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS2411R01RptServiceImpl.class);
	
	private static final int GRID_CNT = 2;
	private static final String KEY_C241M01A_CUSTNAME = "C241M01A.CUSTNAME";
	private static boolean FILL_TO_GRIDCNT = false;
	private static final String LMS2411R01N = "LMS2411R01N";
	private static final String LMS2411R01G = "LMS2411R01G";
	private static final String LMS2411R01P = "LMS2411R01P"; // 價金履保
	private static final String LMS2411R01S = "LMS2411R01S"; // 防杜代辦覆審
	private static final String LMS2411R01H = "LMS2411R01H"; // 小額/團體消費性貸款(新案)
	
	private static final String OFF_BOX = "□";
	private static final String ON_BOX = "■";
	
	@Resource
	CLSService clsService;
	
	@Resource
	RetrialService retrialService;
	
	@Resource
	BranchService branch;

	@Resource 
	MisPTEAMAPPService misPTEAMAPPService;

	@Resource
	MisLNF040Service misLNF040Service;
	
	@Resource
	UserInfoService userInfoService;

    @Resource
    SysParameterService sysParameterService;
	
	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception 
	 * @throws IOException 
	 */
	public OutputStream generateReport(PageParameters params) throws IOException, Exception {
	
		List<InputStream> list = new LinkedList<InputStream>();
		String[] dataSplit = Util.trim(params.getString("rptOid")).split("\\|");
		OutputStream outputStream = null;
		Locale locale = null;
		
		
		try {
			locale = LMSUtil.getLocale();
			Properties propEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
			
			for (String temp : dataSplit) {
				Map<InputStream,Integer> pdfNameMap = new LinkedHashMap<InputStream,Integer>();
				outputStream = null;
				String oid = temp.split("\\^")[0];
				C241M01A c241m01a = retrialService.findC241M01A_oid(oid);
				
				List<C241M01B> c241m01b_list = retrialService.findC241M01B_c241m01a(c241m01a);
				List<C241M01C> c241m01c_list = retrialService.findC241M01C_c241m01a(c241m01a);

				int subLine = 9;//此數值對應的(x,y).要查 PdfTools.並不一定是愈小,愈上面
				if(CrsUtil.isCaseN(c241m01a)){
					subLine = 13;
				}else if(CrsUtil.isCaseS(c241m01a)){
					subLine = 13;
				}
				
				outputStream = genLMS2411R01(locale, c241m01a, c241m01b_list, c241m01c_list, Util.trim(params.getString("L5DescFlag")));
				if(outputStream != null){
					pdfNameMap.put(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()),subLine);
				}else{
					pdfNameMap.put(null,subLine);
				}
				if(c241m01b_list.size() > GRID_CNT){
					outputStream = null;
					outputStream = genLMS2411R02(locale, c241m01a, c241m01b_list);
					if(outputStream != null){
						pdfNameMap.put(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()),subLine);
					}else{
						pdfNameMap.put(null,subLine);
					}
				}
				
				if(pdfNameMap != null && pdfNameMap.size() > 0){
					outputStream = new ByteArrayOutputStream();
					PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream, propEloanPage.getProperty("PaginationText"), true,locale,subLine);
					list.add(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()));
				}	
			}
			
			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);
		}finally{
			
		}
		return outputStream;
	}

	@Override
	public byte[] getContent(PageParameters params) throws Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}
	
	public OutputStream genLMS2411R01(Locale locale,C241M01A c241m01a, List<C241M01B> c241m01b_list
			, List<C241M01C> c241m01c_list, String l5DescFlag)
			throws FileNotFoundException, IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		
		String rptFile = LMS2411R01N+"20111201";//default
		if(CrsUtil.isCaseN(c241m01a) || CrsUtil.isCaseG___N_Detail(c241m01a)){
			if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202204NA) 
					|| Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202105NA) 
					|| Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202209NA) ){
				rptFile = LMS2411R01N+"202105NA";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202204NB) 
					|| Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202105NB) 
					|| Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202209NB) ){
				rptFile = LMS2411R01N+"202105NB";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202008NA)){
				rptFile = LMS2411R01N+"201907NA";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202008NB)){
				rptFile = LMS2411R01N+"201907NB";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202008GA)){
				rptFile = LMS2411R01N+"201907GA";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202008GB)){
				rptFile = LMS2411R01N+"201907GB";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_201907NA)
					|| Util.equals(c241m01a.getRptId(), CrsUtil.V_N_201909NA)){				
				rptFile = LMS2411R01N+"201907NA";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_201907NB)
					|| Util.equals(c241m01a.getRptId(), CrsUtil.V_N_201909NB)){				
				rptFile = LMS2411R01N+"201907NB";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_201907GA)){				
				rptFile = LMS2411R01N+"201907GA";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_201907GB)){				
				rptFile = LMS2411R01N+"201907GB";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_201805A2)){				
				rptFile = LMS2411R01N+"201805A2";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_201805B2)){
				rptFile = LMS2411R01N+"201805B2";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_201805A)){				
				rptFile = LMS2411R01N+"201805A";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_201805B)){
				rptFile = LMS2411R01N+"201805B";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_201707A)){				
				rptFile = LMS2411R01N+"201707A";
			}else if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_201707B)){
				rptFile = LMS2411R01N+"201707B";
			}else{
				//.同 default , LMS2411R01N20111201_zh_TW.rpt
			}
		}else if(CrsUtil.isCaseG_Parent(c241m01a)){
			if(Util.equals(CrsUtil.V_G_202008, c241m01a.getRptId())){
				/*
				 	LMS2411R01G202008_zh_TW.rpt 與 LMS2411R01H202008_zh_TW.rpt 的中間都是12個項目。差別在於
				 	+ 母戶的rpt，只有「動用期限」
				 	+ 子戶的rpt，則有「動用期限」、「借款期限」
				 */
				rptFile = LMS2411R01G+"202008";
			}else if(Util.equals(CrsUtil.V_G_201805, c241m01a.getRptId())){
				rptFile = LMS2411R01G+"201805";
			}else if(Util.equals(CrsUtil.V_G_20111201, c241m01a.getRptId())){
				rptFile = LMS2411R01G+"20111201";
			}
		}else if(CrsUtil.isCaseH(c241m01a) || CrsUtil.isCaseG___H_Detail(c241m01a)){			
			/*
			 	LMS2411R01G202008_zh_TW.rpt 與 LMS2411R01H202008_zh_TW.rpt 的中間都是12個項目。差別在於
			 	+ 母戶的rpt，只有「動用期限」
			 	+ 子戶的rpt，則有「動用期限」、「借款期限」
			 */
			if(Util.equals(CrsUtil.V_H_202008, c241m01a.getRptId())){
				rptFile = LMS2411R01H+"202008"; //isCaseH(c241m01a) 與 isCaseG___H_Detail(c241m01a) => 共用（同樣的 rpt，不再重拉）		
			}
		}else if(CrsUtil.isCaseP(c241m01a)){
			if(Util.equals(CrsUtil.V_P_202008, c241m01a.getRptId())){
				rptFile = LMS2411R01P+"202008";	
			}else if(Util.equals(CrsUtil.V_P_201809, c241m01a.getRptId())){
				rptFile = LMS2411R01P+"201809";	
			}else if(Util.equals(CrsUtil.V_P_20140101, c241m01a.getRptId())){
				rptFile = LMS2411R01P+"20140101";	
			}
		}else if(CrsUtil.isCaseS(c241m01a)){
			if(Util.equals(CrsUtil.V_S_201902, c241m01a.getRptId()) || Util.equals(CrsUtil.V_S_202204, c241m01a.getRptId())){
				rptFile = LMS2411R01S+"201902";	
			}
		}
		ReportGenerator generator = new ReportGenerator("report/crs/"+rptFile+"_" + locale.toString() + ".rpt");

		OutputStream outputStream = null;		
		Properties prop_lms2411m01 = MessageBundleScriptCreator.getComponentResource(LMS2411M01Page.class);
		try {

			// 分行名稱
			rptVariableMap.put("BRANCHNAME", Util.trim(branch.getBranchName(Util.trim(c241m01a.getOwnBrId()))));
			// 列印今天日期
			rptVariableMap.put("PRINTDAY", TWNDate.toAD(new Date()));
			rptVariableMap = this.setC241M01AData(rptVariableMap, c241m01a, c241m01b_list, prop_lms2411m01 );
			if(CrsUtil.isCaseS(c241m01a)){
				rptVariableMap = this.caseS_setC241M01CListData(rptVariableMap, c241m01a ,c241m01c_list, prop_lms2411m01);
			}else{
				rptVariableMap = this.setC241M01CListData(rptVariableMap, c241m01a ,c241m01c_list, prop_lms2411m01);
			}
			
			if(rptFile.startsWith(LMS2411R01N)){
				titleRows = this.setCase_N_title(titleRows, c241m01b_list, true);	
				setC241M01B_coBorrower(rptVariableMap, c241m01b_list);
			}else if(rptFile.startsWith(LMS2411R01G)){
				titleRows = this.setCase_G_title(titleRows, c241m01a);
			}else if(rptFile.startsWith(LMS2411R01P)){
				titleRows = this.setCase_P_title(titleRows, c241m01a, c241m01b_list, true);
			}else if(rptFile.startsWith(LMS2411R01S)){
				titleRows = this.setCase_S_title(titleRows, c241m01a, c241m01b_list, true);
			}else if(rptFile.startsWith(LMS2411R01H)){
				titleRows = this.setCase_H_title(titleRows, c241m01b_list, true);
			} 
			
			rptVariableMap = this.setC241M01B_guarantor(rptVariableMap, c241m01b_list, prop_lms2411m01);
			rptVariableMap = this.setC241M01AForM01DData(rptVariableMap, c241m01a, prop_lms2411m01);
			rptVariableMap = this.setC241M01EData(rptVariableMap, c241m01a, l5DescFlag);

			generator.setLang(locale);
			if(CrsUtil.isCaseS(c241m01a)){
				SubReportParam subReportParam = new SubReportParam();
				Map<String, String> sign_map = new HashMap<String, String>();
				List<Map<String, String>> sign_rows = new ArrayList<Map<String, String>>();				
				if(true){
					LinkedHashMap<String, Map<String, List<String>>> data_map = retrialService.groupC241M01F_by_caseMainId_rel_type(c241m01a);						 
					
					if(data_map.size()==0){
						Map<String, String> rowData = new LinkedHashMap<String, String>();
						rowData.put("SignBean.column01", "");
						rowData.put("SignBean.column02", "");
						rowData.put("SignBean.column03", "");
						rowData.put("SignBean.column04", "");

						sign_rows.add(rowData);
					}else{
						for(String caseMainId : data_map.keySet()){
							Map<String, List<String>> rel_type_staffStr = data_map.get(caseMainId);
							L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);
							String caseInfo = caseMainId;
							if(l120m01a!=null){
								caseInfo = Util.toSemiCharString(l120m01a.getCaseNo());
							}
							//====
							Map<String, String> rowData = new LinkedHashMap<String, String>();
							rowData.put("SignBean.column01", caseInfo);
							rowData.put("SignBean.column02", StringUtils.join(rel_type_staffStr.get("1"), "\r\n"));
							rowData.put("SignBean.column03", StringUtils.join(rel_type_staffStr.get("2"), "\r\n"));
							rowData.put("SignBean.column04", StringUtils.join(rel_type_staffStr.get("3"), "\r\n"));

							sign_rows.add(rowData); 
						}
					}
				}
				subReportParam.setData(0, sign_map, sign_rows);
				generator.setSubReportParam(subReportParam);
			}
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}			
		}
		return outputStream;
	}

	public OutputStream genLMS2411R02(Locale locale,C241M01A c241m01a, List<C241M01B> c241m01b_list)
			throws FileNotFoundException, IOException, Exception {
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		String rptFile = "LMS2411R02";
		if(CrsUtil.isCaseS(c241m01a)){
			rptFile = "LMS2411R02S";
		}
		ReportGenerator generator = new ReportGenerator(
				"report/crs/"+rptFile+"_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;
		Properties prop_lms2411m01 = MessageBundleScriptCreator.getComponentResource(LMS2411M01Page.class);
		try {
			
			// 分行名稱
			rptVariableMap.put("BRANCHNAME", Util.trim(branch.getBranchName(Util.trim(c241m01a.getOwnBrId()))));
			rptVariableMap = this.setC241M01AData(rptVariableMap, c241m01a, c241m01b_list, prop_lms2411m01 );
			titleRows = setCase_N_title(titleRows, c241m01b_list, false);
			setC241M01B_coBorrower(rptVariableMap, c241m01b_list);
			rptVariableMap = this.setC241M01B_guarantor(rptVariableMap, c241m01b_list, prop_lms2411m01);
			
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}

	/**
	 * 塞入變數MAP資料使用(C241M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param C241M01A
	 *            C241M01A資料資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setC241M01AData(
			Map<String, String> rptVariableMap, C241M01A C241m01a, List<C241M01B> c241m01b_list, Properties prop_lms2411m01 ) {
		rptVariableMap.put("C241M01A.RETRIALDATE",
				Util.nullToSpace(TWNDate.toAD(C241m01a.getRetrialDate())));
		rptVariableMap.put("C241M01A.PROJECTSEQ",
				Util.nullToSpace(C241m01a.getProjectNo()));
		String lastRetrialDate = prop_lms2411m01.getProperty("label.N2");//無
		if(CrsUtil.isNOT_null_and_NOTZeroDate(C241m01a.getLastRetrialDate())){
			lastRetrialDate = TWNDate.toAD(C241m01a.getLastRetrialDate());
		}
		rptVariableMap.put("C241M01A.LASTRETRIALDATE", lastRetrialDate);
		rptVariableMap.put("C241M01A.CUSTID", Util.trim(C241m01a.getCustId()));
		rptVariableMap.put("C241M01A.DUPNO", Util.trim(C241m01a.getDupNo()));
		rptVariableMap.put(KEY_C241M01A_CUSTNAME, Util.trim(C241m01a.getCustName()));
		rptVariableMap.put("C241M01A.RANDOMCODE", Util.trim(C241m01a.getRandomCode()));
		rptVariableMap.put("C241M01A.QUOTAAMT", CrsUtil.amtDivide1000(C241m01a.getTotQuota()));
		rptVariableMap.put("C241M01A.BALAMT", CrsUtil.amtDivide1000(C241m01a.getTotBal()));
		rptVariableMap.put("C241M01A.QUOTACURR", Util.trim(C241m01a.getTotQuotaCurr()));
		rptVariableMap.put("C241M01A.BALCURR", Util.trim(C241m01a.getTotBalCurr()));
		rptVariableMap.put("C241M01A.LNDATADATE", Util.trim(TWNDate.toAD(C241m01a.getLnDataDate())));
		
		rptVariableMap.put("C241M01A.COMID", Util.trim(C241m01a.getComId()));
		rptVariableMap.put("C241M01A.COMDUP", Util.trim(C241m01a.getComDup()));
		rptVariableMap.put("C241M01A.COMNAME", Util.trim(C241m01a.getComName()));
		rptVariableMap.put("C241M01A.COMID_DUP_NAME", Util.trim(C241m01a.getComId())+"-"+Util.trim(C241m01a.getComDup())+" "+Util.trim(C241m01a.getComName()));
		
		rptVariableMap.put("C241M01A.BUYERID", Util.trim(C241m01a.getBuyerId()));
		rptVariableMap.put("C241M01A.BUYERDUP", Util.trim(C241m01a.getBuyerDup()));
		rptVariableMap.put("C241M01A.BUYERNAME", Util.trim(C241m01a.getBuyerName()));
		//賣方名稱： 統一編號：  買方名稱： 統一編號：
		//label.sellerInfo=賣方名稱
		//label.buyerInfo=買方名稱
		//label.id=統一編號		
		String sellerBuyerInfo = Util.trim(prop_lms2411m01.get("label.sellerInfo"))+"："+Util.trim(C241m01a.getCustName())+"　"
		+Util.trim(prop_lms2411m01.get("label.id"))+"："+Util.trim(C241m01a.getCustId())
		+"　　"
		+Util.trim(prop_lms2411m01.get("label.buyerInfo"))+"："+Util.trim(C241m01a.getBuyerName())+"　"
		+Util.trim(prop_lms2411m01.get("label.id"))+"："+Util.trim(C241m01a.getBuyerId());

		rptVariableMap.put("C241M01A.SELLER_BUYER_INFO", sellerBuyerInfo);
		rptVariableMap.put("C241M01A.LNF660_M_CONTRACT", Util.trim(C241m01a.getLnf660_m_contract()));
		rptVariableMap.put("C241M01A.LNF660_M_CURR", Util.trim(C241m01a.getLnf660_m_curr()));
		rptVariableMap.put("C241M01A.LNF660_M_FACT", CrsUtil.amtDivide1000(C241m01a.getLnf660_m_fact()));
		rptVariableMap.put("C241M01A.LNF660_M_BEGDATE", Util.trim(TWNDate.toAD(C241m01a.getLnf660_m_begDate())));
		rptVariableMap.put("C241M01A.LNF660_M_ENDDATE", Util.trim(TWNDate.toAD(C241m01a.getLnf660_m_endDate())));
		String C241M01A_LNF660_M_SUBJ = "";
		if(c241m01b_list.size()>0){
			C241M01A_LNF660_M_SUBJ = Util.trim(c241m01b_list.get(0).getSubjectName());
		}else{
			String lnap = Util.trim(C241m01a.getLnf660_m_subj());
			if(Util.isEmpty(lnap)){
				lnap = "817"; // default
			}
			Map<String, Map<String, Object>> lnf040Map = _lnf040Map(misLNF040Service.selAll());
			Map<String, Object> lnf040_item = lnf040Map.get(lnap);
			C241M01A_LNF660_M_SUBJ = Util.trim(lnf040_item.get("LNF040_ACT_NAME"));
		}
		rptVariableMap.put("C241M01A.LNF660_M_SUBJ", C241M01A_LNF660_M_SUBJ);
		return rptVariableMap;
	}

	private Map<String, Map<String, Object>> _lnf040Map(List<Map<String, Object>> lnf040_list){
		Map<String, Map<String, Object>> r = new HashMap<String, Map<String, Object>>();
		for(Map<String, Object> lnf040:lnf040_list){			
			r.put(Util.trim(lnf040.get("LNF040_LNAP_CODE")), lnf040);
		}
		return r;
	}
	
	private List<Map<String, String>> setCase_G_title(
			List<Map<String, String>> titleRows, C241M01A c241m01a) {
		
		Map<String, String> map = null;
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "L");//title列
		titleRows.add(map);
		
		{
			PTEAMAPP mispteamapp = getPTEAMAPP(c241m01a.getGrpCntrNo());
			//===========
			map = Util.setColumnMap();	
			map.put("ReportBean.column01", "Y");
			/*
			 * 在 notes> 套表 > FCLS241R02. 科目         @If(Subject="";"中期消費者貸款";"")
			 */
			map.put("ReportBean.column02", "中期消費者貸款");
			map.put("ReportBean.column03", Util.nullToSpace(TWNDate.toAD(mispteamapp.getEfffrom())) + "~"
								+ Util.nullToSpace(TWNDate.toAD(mispteamapp.getEffend())) );
			map.put("ReportBean.column04", "" );
			map.put("ReportBean.column05", CrsUtil.amtDivide1000(mispteamapp.getTotamt()));
			
			String c240m01a_mainId = "";
			C240M01A c240m01a = retrialService.findC240M01A_C241M01A(c241m01a);
			if(c240m01a != null){
				c240m01a_mainId = c240m01a.getMainId(); 
			}
			
			List<String> r = new ArrayList<String>();
			for(C241M01A g_item :retrialService.grid_C241M01A_byGrpCntrNo(c240m01a_mainId, c241m01a.getGrpCntrNo())){
				r.add(g_item.getCustId()+" "+g_item.getCustName());
			}			
			map.put("ReportBean.column06", StringUtils.join(r, "、\r\n"));					
			titleRows.add(map);
		}
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "N");//合計列,C241M01C(項目),confFlag(辦理情形)
		titleRows.add(map);	
		return titleRows;
	}

	private List<Map<String, String>> setCase_H_title(
			List<Map<String, String>> titleRows, List<C241M01B> c241m01bList,
			boolean isFirstPage) {
				
		int count = 0;
		Map<String, String> map = null;
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "L");//title 列
		titleRows.add(map);
		
		/* 若客戶同時有2個 歡喜信貸 額度
		 */
		for (C241M01B c241m01b : c241m01bList) {
			if (isFirstPage && count >= GRID_CNT) {
				count++;
			}else if (!isFirstPage && count < GRID_CNT) {
				count++;
			}else{
				map = Util.setColumnMap();	
				map.put("ReportBean.column01", "Y");
				map.put("ReportBean.column02", Util.nullToSpace(c241m01b.getSubjectName()));
				String dt_use = Util.trim(TWNDate.toAD(c241m01b.getUseFDate())) + "~" + Util.trim(TWNDate.toAD(c241m01b.getUseEDate()));
				String dt_loan = Util.trim(TWNDate.toAD(c241m01b.getLoanFDate())) + "~" + Util.trim(TWNDate.toAD(c241m01b.getLoanEDate()));
				String column03 = dt_use+"\r\n"+dt_loan;
				map.put("ReportBean.column03", column03);
				map.put("ReportBean.column04", Util.nullToSpace(c241m01b.getQuotaCurr()) );
				map.put("ReportBean.column05", CrsUtil.amtDivide1000(c241m01b.getQuotaAmt()));
				map.put("ReportBean.column06", "");	
				
				titleRows.add(map);
				count++;
			}
		}
		
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "N");//合計列,C241M01C(項目),confFlag(辦理情形)
		titleRows.add(map);	
		
		return titleRows;
	}
	
	private List<Map<String, String>> setCase_P_title(
			List<Map<String, String>> titleRows, C241M01A c241m01a, List<C241M01B> c241m01bList
			, boolean isFirstPage) {
		
		int count = 0;
		Map<String, String> map = null;
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "L");//title 列
		titleRows.add(map);
		for (C241M01B c241m01b : c241m01bList) {
			if (isFirstPage && count >= GRID_CNT) {
				count++;
			}else if (!isFirstPage && count < GRID_CNT) {
				count++;
			}else{
				map = Util.setColumnMap();	
				map.put("ReportBean.column01", "Y");
				map.put("ReportBean.column02", Util.trim(c241m01b.getSubjectName()));
				map.put("ReportBean.column03", Util.trim(c241m01b.getQuotaNo()));
				map.put("ReportBean.column04", Util.nullToSpace(c241m01b.getQuotaCurr()) );
				map.put("ReportBean.column05", CrsUtil.amtDivide1000(c241m01b.getQuotaAmt()));
				map.put("ReportBean.column06", CrsUtil.amtDivide1000(c241m01b.getBalAmt()));
				String column07 = "";
				if(Util.equals(CrsUtil.V_P_20140101, c241m01a.getRptId())){
					column07 = Util.trim(c241m01a.getPbAcct()); //以 LcNo 作為 PbAcct 的預值
				}else{
					column07 = Util.trim(c241m01b.getLcNo());
				}
				map.put("ReportBean.column07", column07);
				
				map.put("ReportBean.column08", Util.nullToSpace(TWNDate.toAD(c241m01b.getUseFDate())));
				map.put("ReportBean.column09", Util.nullToSpace(TWNDate.toAD(c241m01b.getUseEDate())));
				
				if(true){ // J-107-0254
					// [10]子戶餘額幣別
					map.put("ReportBean.column10", Util.trim(c241m01b.getBalCurr()));
					
					String column11 = "";
					String column12 = "";
					String column13 = "";

					if(c241m01a.getLnf660_loan_class().startsWith("H")){ // 永慶房仲適用
						column11 = "("+"買賣價款"+")";
						if(c241m01b.getDocKindP_amt()!=null && c241m01b.getDocKindP_amt().compareTo(BigDecimal.ZERO)>0){
							column12 = Util.trim(c241m01b.getBalCurr());
							column13 = CrsUtil.amtDivide1000(c241m01b.getDocKindP_amt());
						}					
					}else if(c241m01a.getLnf660_loan_class().startsWith("B")){ // 永慶房仲以外
						column11 = "("+"子戶履保額度序號："+Util.trim(c241m01b.getQuotaNo())+")";
						column12 = Util.trim(c241m01b.getQuotaCurr());
						column13 = CrsUtil.amtDivide1000(c241m01b.getQuotaAmt());						
					}					
					// [11]子戶履保額度序號
					map.put("ReportBean.column11", column11);
					
					// [12, 13]買賣價款（仟元）（永慶房仲適用）或子戶履保額度（仟元）（永慶房仲以外適用）					
					map.put("ReportBean.column12", column12);	
					map.put("ReportBean.column13", column13);
					
				}
				
				titleRows.add(map);
				count++;
			}
		}
		if (isFirstPage && FILL_TO_GRIDCNT) {
			for(int i = count ; i < GRID_CNT ; i++){
				map = Util.setColumnMap();	
				map.put("ReportBean.column01", "Y");
				titleRows.add(map);
			}
		}
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "N");//合計列,C241M01C(項目),confFlag(辦理情形)
		titleRows.add(map);
		return titleRows;
	}
	
	private List<Map<String, String>> setCase_S_title(
			List<Map<String, String>> titleRows, C241M01A c241m01a, List<C241M01B> c241m01bList
			, boolean isFirstPage) {
		
		Map<String, String> m_c241m01bQuotaType = retrialService.get_crs_c241m01bQuotaType();
		
		int count = 0;
		Map<String, String> map = null;
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "L");//title 列
		titleRows.add(map);
		
		Map<String, String> gnMap = _procSameAsCntrNo(c241m01bList);
		
		for (C241M01B c241m01b : c241m01bList) {
			if (isFirstPage && count >= GRID_CNT) {
				count++;
			}else if (!isFirstPage && count < GRID_CNT) {
				count++;
			}else{
				map = Util.setColumnMap();	
				map.put("ReportBean.column01", "Y");
				map.put("ReportBean.column02",
						Util.nullToSpace(c241m01b.getSubjectName()));
				map.put("ReportBean.column03",
						Util.nullToSpace(c241m01b.getQuotaNo()));
				map.put("ReportBean.column04", Util.nullToSpace(c241m01b.getQuotaCurr()) );
				map.put("ReportBean.column05", CrsUtil.amtDivide1000(c241m01b.getQuotaAmt()));
				String column06 = Util.trim(TWNDate.toAD(c241m01b.getUseFDate())) + "~"
									+ Util.trim(TWNDate.toAD(c241m01b.getUseEDate()));
				map.put("ReportBean.column06", column06);
				
				String column07 = Util.trim(TWNDate.toAD(c241m01b.getLoanFDate())) + "~"
									+ Util.trim(TWNDate.toAD(c241m01b.getLoanEDate()));
				if(CrsUtil.isNull_or_ZeroDate(c241m01b.getLoanFDate()) 
						&& CrsUtil.isNull_or_ZeroDate(c241m01b.getLoanEDate()) 
						&& Util.equals(("0001-01-01"+ "~"+"0001-01-01"), column07)){
					//以 cntrNo : 006109300194 為例
					//其 授信科目 : 102 透支
					//當借款期限為 0001-01-01~0001-01-01 時，改印出「動用起~迄」
					column07 = column06;
				}
				map.put("ReportBean.column07", column07);
				map.put("ReportBean.column09", Util.trim(m_c241m01bQuotaType.containsKey(c241m01b.getQuotaType())?m_c241m01bQuotaType.get(c241m01b.getQuotaType()):c241m01b.getQuotaType()));
				map.put("ReportBean.column10", Util.trim(gnMap.get(c241m01b.getOid())));
				map.put("ReportBean.column11",
						Util.nullToSpace(c241m01b.getMajorMemo()));
				map.put("ReportBean.column12", Util.trim(c241m01b.getEstCurr()));
				map.put("ReportBean.column13", CrsUtil.amtDivide1000(c241m01b.getEstAmt()));
				map.put("ReportBean.column14", Util.trim(c241m01b.getLoanCurr()));
				map.put("ReportBean.column15", CrsUtil.amtDivide1000(c241m01b.getLoanAmt()));
				if(true){
					String showBalCurr = "";
					String showBalAmt = "";
					if(CrsUtil.has_sBalCurrAmt(c241m01b)){
						showBalCurr = Util.trim(c241m01b.getSBalCurr());
						showBalAmt = CrsUtil.amtDivide1000(c241m01b.getSBalAmt());
					}else{
						showBalCurr = Util.trim(c241m01b.getBalCurr());
						showBalAmt = CrsUtil.amtDivide1000(c241m01b.getBalAmt());
					} 		
					map.put("ReportBean.column16", showBalCurr);
					map.put("ReportBean.column17", showBalAmt);
				}	
				
				titleRows.add(map);
				count++;
			}
		}
		
		if (isFirstPage && FILL_TO_GRIDCNT) {
			for(int i = count ; i < GRID_CNT ; i++){
				map = Util.setColumnMap();	
				map.put("ReportBean.column01", "Y");
				titleRows.add(map);
			}
		}
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "N");//合計列,C241M01C(項目),confFlag(辦理情形)
		titleRows.add(map);
		return titleRows;
	}
	
	private PTEAMAPP getPTEAMAPP(String grpCntrNo){
		HashSet<String> cntrNos = new HashSet<String>();
		cntrNos.add(grpCntrNo);
		List<PTEAMAPP> list = misPTEAMAPPService.getDataByGrpCntrNo(cntrNos);
		if(list.size()>0){
			return list.get(0);
		}		
		return new PTEAMAPP();
	}
	/**
	 * 因為已經有 order by quotaNo
	 * 組出一個map {key:quotaNo, value:一個 list,包含各個 loanNo 的擔保品名稱 }
	 * 
	 * 回傳的 map{key:oid, value:擔保品名稱}
	 */
	private Map<String,String> _procSameAsCntrNo(List<C241M01B> c241m01bList){
		Map<String,String> r = new HashMap<String,String>();
		
		LinkedHashMap<String, List<String>> cntrNo_gnListMap = new LinkedHashMap<String, List<String>>();
		LinkedHashSet<String> cntrNoSet = new LinkedHashSet<String>();
		List<String> cntrNoList = new ArrayList<String>();
		List<String> orgGnList = new ArrayList<String>();
		List<String> newGnList = new ArrayList<String>();
		for(C241M01B c241m01b : c241m01bList){
			cntrNoSet.add(c241m01b.getQuotaNo());
		}
		for(String quotaNo : cntrNoSet){
			List<String> gnList = new ArrayList<String>();
			for(C241M01B c241m01b : c241m01bList){
				if(Util.equals(quotaNo, c241m01b.getQuotaNo())){
					gnList.add(Util.trim(c241m01b.getGuaranteeName()));
				}
			}
			cntrNo_gnListMap.put(quotaNo, gnList);
		}
		//----------------
		//填入 cntrNoList, orgGnList, newGnList
		for(String k:cntrNo_gnListMap.keySet()){
			String v = "";
			List<String> v_arr = cntrNo_gnListMap.get(k);
			if(CollectionUtils.isNotEmpty(v_arr)){
				v = v_arr.get(0);
			}
			cntrNoList.add(k);
			orgGnList.add(v);
			newGnList.add(v);
		}
		//----------------
		//判斷 newGnList 填入同上 
		int size = cntrNoList.size();
		for(int i=1;i<size;i++){
			String preGn = orgGnList.get(i-1);
			String currentGn = orgGnList.get(i);
			//上一列空白, 本列空白, 在本列就不顯示 同上
			if(Util.isNotEmpty(currentGn) && Util.equals(preGn, currentGn)){
				newGnList.set(i, "同上");
			}
		}
		
		LinkedHashMap<String, String> chgMap = new LinkedHashMap<String, String>();
		for(int i=0;i<size;i++){
			chgMap.put(cntrNoList.get(i), newGnList.get(i));
		}
		//------------------
		HashSet<String> runnedCntrNo = new HashSet<String>();
		for(C241M01B c241m01b : c241m01bList){
			String val = "";
			if(runnedCntrNo.contains(c241m01b.getQuotaNo())){
				//同一額度序號下, 第2,3..的 loanNo. 在引入帳務時，已指定為「同上」
				val = c241m01b.getGuaranteeName();
			}else{
				runnedCntrNo.add(c241m01b.getQuotaNo());
				
				val = chgMap.get(c241m01b.getQuotaNo());
			}	
			r.put(c241m01b.getOid(), val);
		}
		//---
		return r;
	}
	
	/**
	 * 塞入變數MAP資料使用(C241M01B)
	 * 
	 * @param titleRows
	 *            存放變數list
	 * @param C241m01bList
	 *            C241M01B資料
	 * @param isFirstPage
	 *            是否首頁(若否，印在附表)
	 * @return List<Map<String, String>> titleRows
	 */
	private List<Map<String, String>> setCase_N_title(
			List<Map<String, String>> titleRows, List<C241M01B> c241m01bList,
			boolean isFirstPage) {
		
		Map<String, String> m_c241m01bQuotaType = retrialService.get_crs_c241m01bQuotaType();
		
		int count = 0;
		Map<String, String> map = null;
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "L");//title 列
		titleRows.add(map);
		
		Map<String, String> gnMap = _procSameAsCntrNo(c241m01bList);
		
		for (C241M01B c241m01b : c241m01bList) {
			if (isFirstPage && count >= GRID_CNT) {
				count++;
			}else if (!isFirstPage && count < GRID_CNT) {
				count++;
			}else{
				map = Util.setColumnMap();	
				map.put("ReportBean.column01", "Y");
				map.put("ReportBean.column02",
						Util.nullToSpace(c241m01b.getSubjectName()));
				map.put("ReportBean.column03",
						Util.nullToSpace(c241m01b.getQuotaNo()));
				map.put("ReportBean.column04", Util.nullToSpace(c241m01b.getQuotaCurr()) );
				map.put("ReportBean.column05", CrsUtil.amtDivide1000(c241m01b.getQuotaAmt()));
				String column06 = Util.trim(TWNDate.toAD(c241m01b.getUseFDate())) + "~"
									+ Util.trim(TWNDate.toAD(c241m01b.getUseEDate()));
				map.put("ReportBean.column06", column06);
				
				String column07 = Util.trim(TWNDate.toAD(c241m01b.getLoanFDate())) + "~"
									+ Util.trim(TWNDate.toAD(c241m01b.getLoanEDate()));
				if(CrsUtil.isNull_or_ZeroDate(c241m01b.getLoanFDate()) 
						&& CrsUtil.isNull_or_ZeroDate(c241m01b.getLoanEDate()) 
						&& Util.equals(("0001-01-01"+ "~"+"0001-01-01"), column07)){
					//以 cntrNo : 006109300194 為例
					//其 授信科目 : 102 透支
					//當借款期限為 0001-01-01~0001-01-01 時，改印出「動用起~迄」
					column07 = column06;
				}
				map.put("ReportBean.column07", column07);
				map.put("ReportBean.column09", Util.trim(m_c241m01bQuotaType.containsKey(c241m01b.getQuotaType())?m_c241m01bQuotaType.get(c241m01b.getQuotaType()):c241m01b.getQuotaType()));
				map.put("ReportBean.column10", Util.trim(gnMap.get(c241m01b.getOid())));
				map.put("ReportBean.column11",
						Util.nullToSpace(c241m01b.getMajorMemo()));
				map.put("ReportBean.column12", Util.trim(c241m01b.getEstCurr()));
				map.put("ReportBean.column13", CrsUtil.amtDivide1000(c241m01b.getEstAmt()));
				map.put("ReportBean.column14", Util.trim(c241m01b.getLoanCurr()));
				map.put("ReportBean.column15", CrsUtil.amtDivide1000(c241m01b.getLoanAmt()));
				if(true){
					String showBalCurr = "";
					String showBalAmt = "";
					if(CrsUtil.has_sBalCurrAmt(c241m01b)){
						showBalCurr = Util.trim(c241m01b.getSBalCurr());
						showBalAmt = CrsUtil.amtDivide1000(c241m01b.getSBalAmt());
					}else{
						showBalCurr = Util.trim(c241m01b.getBalCurr());
						showBalAmt = CrsUtil.amtDivide1000(c241m01b.getBalAmt());
					} 		
					map.put("ReportBean.column16", showBalCurr);
					map.put("ReportBean.column17", showBalAmt);
				}	
				
				titleRows.add(map);
				count++;
			}
		}
		
		if (isFirstPage && FILL_TO_GRIDCNT) {
			for(int i = count ; i < GRID_CNT ; i++){
				map = Util.setColumnMap();	
				map.put("ReportBean.column01", "Y");
				titleRows.add(map);
			}
		}
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "N");//合計列,C241M01C(項目),confFlag(辦理情形)
		titleRows.add(map);
		return titleRows;
	}

	private Map<String, String> setC241M01B_guarantor(
			Map<String, String> rptVariableMap, List<C241M01B> C241m01bList, Properties prop_lms2411m01) {
		String r = "";
		{
			Set<String> set = new LinkedHashSet<String>();
			for (C241M01B C241m01b : C241m01bList) {
				String[] temp = Util.nullToSpace(C241m01b.getGuarantor())
						.split("、");
				for (String temp2 : temp) {
					String s = Util.trim(temp2);
					if(Util.isNotEmpty(s)){
						set.add(s);	
					}					
				}
			}
			r = StringUtils.join(set, "、");
		}
		
		rptVariableMap.put("C241M01B.GUARANTOR", Util.isNotEmpty(r)?r:prop_lms2411m01.getProperty("label.N2"));//無
		return rptVariableMap;
	}
	
	private void setC241M01B_coBorrower(
			Map<String, String> rptVariableMap, List<C241M01B> C241m01bList) {
		String r = "";
		{
			Set<String> set = new LinkedHashSet<String>();
			for (C241M01B C241m01b : C241m01bList) {
				String[] temp = Util.nullToSpace(C241m01b.getCoBorrower())
						.split("、");
				for (String temp2 : temp) {
					String s = Util.trim(temp2);
					if(Util.isNotEmpty(s)){
						set.add(s);	
					}					
				}
			}
			r = StringUtils.join(set, "、");
		}
		
		if(Util.isNotEmpty(r)){
			String mainCustName = Util.trim(rptVariableMap.get(KEY_C241M01A_CUSTNAME));
			rptVariableMap.put(KEY_C241M01A_CUSTNAME, mainCustName+(Util.isNotEmpty(mainCustName)?"　共同借款人：":"")+r);	
		}
	}

	private Map<String, String> caseS_setC241M01CListData(
			Map<String, String> rptVariableMap, C241M01A c241m01a
			, List<C241M01C> c241m01cList, Properties prop_lms2411m01) {
		ArrayList<String> strList = new ArrayList<String>();

		String CRLN = "<br>";
		
		Map<String, C241M01C> c241m01c_item_map = retrialService.toMap_keyAs_itemNo(c241m01cList);
		TreeMap<Integer, List<C241M01C>> tree_map = CrsUtil.caseS_to_seq(c241m01a.getRptId(), c241m01c_item_map);
		for (Integer itemSeq : tree_map.keySet()) {
			List<String> show_text_list = new ArrayList<String>();
			for(C241M01C c241m01c : tree_map.get(itemSeq)){
				rptVariableMap = getCHKYN_NOT_Z(c241m01c, rptVariableMap, prop_lms2411m01);
				
				String chkText = Util.trim(c241m01c.getChkText());
				if(true){
					/*
					 * TODO 消金覆審由 PlainText 改成 HTML(進階)，為避免 string 包含 & < > 等html特定文字，要做轉換
					 *      企金覆審仍維持 PlainText
					 */
					chkText = StringEscapeUtils.escapeHtml(chkText);
				}
				
				if(Util.equals(CrsUtil.S070, c241m01c.getItemNo())){
					/*
					 	註明調閱傳票、相關資料及資金流向詳情
					 */
					String showStr =  CrsUtil.Z_DESC_N007_SINCE_R11;

					//J-111-0031 消金覆審報告表增加文字
					if(Util.equals(c241m01a.getRptId(), CrsUtil.V_S_202204)){
						showStr =  CrsUtil.Z_DESC_N007_SINCE_R11_202204;
					}
					
					show_text_list.add(showStr+(Util.isNotEmpty(chkText)?((CRLN)+chkText):""));
				} else{
					if(Util.isNotEmpty(chkText)){						
						show_text_list.add(chkText);
					}
				}	
			}
			if(show_text_list.size()>0){
				strList.add("第"+itemSeq+"項."+StringUtils.join(show_text_list, CRLN));
			}					
		}
		
		/*
		 * 原本在 rpt 中是細明體8點，但為印出底線，格式由 PlainText 改成 HTML(進階) 後
		 * 印出來的字體比  左邊(覆審項目)的字體 略小
		 * 所以在外面，包一層 div，指定為11px
		 */
		rptVariableMap.put("C241M01C.ITEMTOTALTEXT", "<div style='font-size:11px;'><div>"+StringUtils.join(strList, "</div><div style='margin-top:12px;'>")+"</div></div>");
		return rptVariableMap;
	}
	
	/**
	 * 塞入變數MAP資料使用(C241M01C)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param C241M01C
	 *            C241M01C資料資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setC241M01CListData(
			Map<String, String> rptVariableMap, C241M01A c241m01a
			, List<C241M01C> c241m01cList, Properties prop_lms2411m01) {
		ArrayList<String> strList = new ArrayList<String>();

		String CRLN = "<br>";
		Map<String, C241M01C> map = retrialService.toMap_keyAs_itemNo(c241m01cList);
		C241M01C c241m01c_Y_NY10 = map.get(CrsUtil.Y_NY10);
		C241M01C c241m01c_Y_NY1A = map.get(CrsUtil.Y_NY1A);
		C241M01C c241m01c_Y_NY1B = map.get(CrsUtil.Y_NY1B);
		C241M01C c241m01c_Y_NY20 = map.get(CrsUtil.Y_NY20);		
		C241M01C c241m01c_Y_NY3A = map.get(CrsUtil.Y_NY3A);
		C241M01C c241m01c_Y_NY3B = map.get(CrsUtil.Y_NY3B);
		
		for (C241M01C c241m01c : c241m01cList) {
			if(Util.equals(CrsUtil.Y_項目附屬選項, c241m01c.getItemType())){
				//暫先不抓 Y_項目附屬選項
				continue;
			}
			if(Util.equals(CrsUtil.Z_電腦建檔資料, c241m01c.getItemType())){
				//暫先不抓 Z_電腦建檔
				continue;
			}
			
			rptVariableMap = getCHKYN_NOT_Z(c241m01c, rptVariableMap, prop_lms2411m01);
			
			String chkText = Util.trim(c241m01c.getChkText());
			if(true){
				/*
				 * TODO 消金覆審由 PlainText 改成 HTML(進階)，為避免 string 包含 & < > 等html特定文字，要做轉換
				 *      企金覆審仍維持 PlainText
				 */
				chkText = StringEscapeUtils.escapeHtml(chkText);
			}
			
			if(Util.equals(CrsUtil.N007, c241m01c.getItemNo())){
				/*
				 	註明調閱傳票、相關資料及資金流向詳情
				 */
				String showStr = CrsUtil.Z_DESC_N007;
				if(CrsUtil.docKindN_since_V202008(c241m01a)){
					if(Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202204NA)
							|| Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202204NB)
							|| Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202209NA)
							|| Util.equals(c241m01a.getRptId(), CrsUtil.V_N_202209NB)  ){
						//除了【並影印資金流向資料存覆審卷備查】再加上【或確認E-LOAN授信管理系統貸後管理追蹤檢核表已存載。】
						showStr = "1."+CrsUtil.Z_DESC_N007_SINCE_R11_202204+" "
								+"2."+CrsUtil.Z_DESC_N007_2ND_SINCE_R11;
					}else{
						showStr = "1."+CrsUtil.Z_DESC_N007_SINCE_R11+" "
								+"2."+CrsUtil.Z_DESC_N007_2ND_SINCE_R11;
					}
				}else if(CrsUtil.docKindN_since_R11(c241m01a)){
					showStr = CrsUtil.Z_DESC_N007_SINCE_R11;
				}
				strList.add("第"+c241m01c.getItemSeq()+"項："+showStr+(Util.isNotEmpty(chkText)?((CRLN)+chkText):""));			 	
			} else if(Util.equals(CrsUtil.N010, c241m01c.getItemNo()) 
					&& CrsUtil.docKindN_since_R11(c241m01a) 
					&& c241m01c_Y_NY20!=null){
				String detailStr = "";
				if(true){
					String[] detail_key_arr = { CrsUtil.Y_NY2A,
							CrsUtil.Y_NY2B, CrsUtil.Y_NY2C, CrsUtil.Y_NY2D
					}; 
					
					String buff = "";
					String sepSpace = " ";
					for(String detail_key: detail_key_arr){
						C241M01C c241m01c_Y_detail = map.get(detail_key);
						if(c241m01c_Y_detail!=null){
							String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c_Y_detail), "|");
							String ra =(Util.equals(c241m01c_Y_detail.getChkResult(), Util.getLeftStr(fmt[0], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label."+fmt[0]);			
							String rb =(Util.equals(c241m01c_Y_detail.getChkResult(), Util.getLeftStr(fmt[1], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label."+fmt[1]);
							String rc =(Util.equals(c241m01c_Y_detail.getChkResult(), Util.getLeftStr(fmt[2], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.K_PRT");
							
							buff+= ("<tr>"
										+"<td>"+c241m01c_Y_detail.getChkItem()+"</td>"
										+"<td nowrap>"+sepSpace+ra+sepSpace+rb+" 符合("+rc+")</td>"
									+"</tr>");
						}
					}
					if(Util.isNotEmpty(buff)){
						buff = ("<table>"+buff+"</table>");	
					}					
					
					if(Util.isNotEmpty(buff)){
						detailStr = "<div style='margin-left:4px;'>"+buff+"</div>";
					}	
				}
				//~~~~~~
				List<String> tmp_N010_list = new ArrayList<String>();
				if(Util.isNotEmpty(chkText)){
					tmp_N010_list.add(chkText);
				}
				tmp_N010_list.add(CrsUtil.Y_DESC_NY20_SINCE_R11);
				//~~~~~~
				String chkText_NY20 = StringEscapeUtils.escapeHtml(Util.trim(c241m01c_Y_NY20.getChkText()));
				//~~~~~~
				strList.add("第"+c241m01c.getItemSeq()+"項："
						+StringUtils.join(tmp_N010_list, CRLN)
						+detailStr
						+chkText_NY20
						);						
						
			} else if(Util.equals(CrsUtil.N012, c241m01c.getItemNo())){
				String detailStr = "";
				if(c241m01c_Y_NY10!=null){
					if(Util.equals("N", c241m01c_Y_NY10.getChkResult())){
						//無約定事項
					}else{						
						if(true){
							if(c241m01c_Y_NY1A!=null){
								String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c_Y_NY1A), "|");
								String ra =(Util.equals(c241m01c_Y_NY1A.getChkResult(), Util.getLeftStr(fmt[0], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label."+fmt[0]);			
								String rb =(Util.equals(c241m01c_Y_NY1A.getChkResult(), Util.getLeftStr(fmt[1], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label."+fmt[1]);
								String rc =(Util.equals(c241m01c_Y_NY1A.getChkResult(), Util.getLeftStr(fmt[2], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.K_PRT");
								//在「不適用」時
//								if(Util.equals(c241m01c_Y_NY1A.getChkResult(), Util.getLeftStr(fmt[2], 1))){
//									ra = OFF_BOX+prop_lms2411m01.getProperty("label."+fmt[2]);
//									rb = OFF_BOX+prop_lms2411m01.getProperty("label."+fmt[2]);
//								}
								String memo = "";
								String item_chkText = StringEscapeUtils.escapeHtml(Util.trim(c241m01c_Y_NY1A.getChkText()));
								if(Util.isNotEmpty(item_chkText)){
									memo = "(說明："+item_chkText+")";
								}
								detailStr += (c241m01c_Y_NY1A.getChkItem()+ra+rb+rc+memo);
							}
						}
						if(true){
							if(c241m01c_Y_NY1B!=null){
								String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c_Y_NY1B), "|");
								String ra =(Util.equals(c241m01c_Y_NY1B.getChkResult(), Util.getLeftStr(fmt[0], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label."+fmt[0]);			
								String rb =(Util.equals(c241m01c_Y_NY1B.getChkResult(), Util.getLeftStr(fmt[1], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label."+fmt[1]);
								String rc =(Util.equals(c241m01c_Y_NY1B.getChkResult(), Util.getLeftStr(fmt[2], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.K_PRT");
								//在「不適用」時
//								if(Util.equals(c241m01c_Y_NY1B.getChkResult(), Util.getLeftStr(fmt[2], 1))){
//									ra = OFF_BOX+prop_lms2411m01.getProperty("label."+fmt[2]);
//									rb = OFF_BOX+prop_lms2411m01.getProperty("label."+fmt[2]);
//								}
								String memo = "";
								String item_chkText = StringEscapeUtils.escapeHtml(Util.trim(c241m01c_Y_NY1B.getChkText()));
								if(Util.isNotEmpty(item_chkText)){
									memo = "(說明："+item_chkText+")";
								}
								detailStr += ((CRLN)+c241m01c_Y_NY1B.getChkItem()+ra+rb+rc+memo);
							}
						}
					}	
					if(Util.isNotEmpty(detailStr)){
						detailStr = "<div style='margin-left:16px;'>"+detailStr+"</div>";
					}
				}
				/*
				  	 借戶是否依照約定條件履行？借戶若有違反承諾或約定事項是否依核定條件處置？
				 */
				strList.add("第"+c241m01c.getItemSeq()+"項："
						+(Util.isNotEmpty(chkText)?(chkText+(CRLN)):"")
						+CrsUtil.get_chkItem_Y_NY10(c241m01c_Y_NY10, prop_lms2411m01, false)
						+detailStr
						);	
			} else if(Util.equals(CrsUtil.N026, c241m01c.getItemNo())){
				String detailStr = "";
				if(true){
					boolean fetch_NY3A = false;
					boolean fetch_NY3B = false;
					if(c241m01c_Y_NY3A!=null && Util.isNotEmpty(Util.trim(c241m01c_Y_NY3A.getChkText()))){
						fetch_NY3A = true;
					}
					if(c241m01c_Y_NY3B!=null && Util.isNotEmpty(Util.trim(c241m01c_Y_NY3B.getChkText()))){
						fetch_NY3B = true;
					}
					//===============================
					if(true){
						if(fetch_NY3A){
							String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c_Y_NY3A), "|");
							String ra =(Util.equals(c241m01c_Y_NY3A.getChkResult(), Util.getLeftStr(fmt[0], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label."+fmt[0]);			
							String rb =(Util.equals(c241m01c_Y_NY3A.getChkResult(), Util.getLeftStr(fmt[1], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label."+fmt[1]);
							String rc =(Util.equals(c241m01c_Y_NY3A.getChkResult(), Util.getLeftStr(fmt[2], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.K_PRT");
							
							String memo = "";
							String item_chkText = StringEscapeUtils.escapeHtml(Util.trim(c241m01c_Y_NY3A.getChkText()));
							if(Util.isNotEmpty(item_chkText)){
								memo = "(說明："+item_chkText+")";
							}
							detailStr += (c241m01c_Y_NY3A.getChkItem()+ra+rb+rc+memo);
						}
						if(fetch_NY3A &&  fetch_NY3B){
							detailStr += (CRLN);
						}
						if(fetch_NY3B){
							String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c_Y_NY3B), "|");
							String ra =(Util.equals(c241m01c_Y_NY3B.getChkResult(), Util.getLeftStr(fmt[0], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label."+fmt[0]);			
							String rb =(Util.equals(c241m01c_Y_NY3B.getChkResult(), Util.getLeftStr(fmt[1], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label."+fmt[1]);
							String rc =(Util.equals(c241m01c_Y_NY3B.getChkResult(), Util.getLeftStr(fmt[2], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.K_PRT");
							
							String memo = "";
							String item_chkText = StringEscapeUtils.escapeHtml(Util.trim(c241m01c_Y_NY3B.getChkText()));
							if(Util.isNotEmpty(item_chkText)){
								memo = "(說明："+item_chkText+")";
							}
							detailStr += (c241m01c_Y_NY3B.getChkItem()+ra+rb+rc+memo);
						}						
					}
					
					if(Util.isNotEmpty(detailStr)){
						detailStr = "<div style='margin-left:16px;'>"+detailStr+"</div>";
					}
				}
				if(Util.isNotEmpty(chkText) || Util.isNotEmpty(detailStr) ){
					strList.add("第"+c241m01c.getItemSeq()+"項："
							+(Util.isNotEmpty(chkText)?(chkText+(CRLN)):"")
							+detailStr
						);	
				}
			} else{
				if(Util.isNotEmpty(chkText)){
					
					if(Util.equals(CrsUtil.N013, c241m01c.getItemNo())){
						// 2015-02-09 BR:933反應[已改善]/[未改善]沒有印出來
						if ("Y".equals(c241m01c.getChkPreReview())) {
							strList.add("第"+c241m01c.getItemSeq()+"項："+CrsUtil.Z_DESC_N013_Y+(CRLN)+chkText);
						} else if ("N".equals(c241m01c.getChkPreReview())) {
							strList.add("第"+c241m01c.getItemSeq()+"項："+CrsUtil.Z_DESC_N013_N+(CRLN)+chkText);
						} else {
							strList.add("第"+c241m01c.getItemSeq()+"項：" + chkText);
						}
					}else{
						strList.add("第"+c241m01c.getItemSeq()+"項."+chkText);
					}
				}
			}					
		}
		
		if(c241m01c_Y_NY3A!=null){
			String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c_Y_NY3A), "|");	
			rptVariableMap = getCHKYN_NOT_Z("7A", c241m01c_Y_NY3A.getChkItem(), fmt
					, c241m01c_Y_NY3A.getChkResult(), rptVariableMap, prop_lms2411m01);
		}
		if(c241m01c_Y_NY3B!=null){
			String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c_Y_NY3B), "|");	
			rptVariableMap = getCHKYN_NOT_Z("7B", c241m01c_Y_NY3B.getChkItem(), fmt
					, c241m01c_Y_NY3B.getChkResult(), rptVariableMap, prop_lms2411m01);
		}
		
		if(c241m01c_Y_NY1A!=null){
			String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c_Y_NY1A), "|");	
			rptVariableMap = getCHKYN_NOT_Z("15A", c241m01c_Y_NY1A.getChkItem(), fmt
					, c241m01c_Y_NY1A.getChkResult(), rptVariableMap, prop_lms2411m01);
		}
		if(c241m01c_Y_NY1B!=null){
			String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c_Y_NY1B), "|");	
			rptVariableMap = getCHKYN_NOT_Z("15B", c241m01c_Y_NY1B.getChkItem(), fmt
					, c241m01c_Y_NY1B.getChkResult(), rptVariableMap, prop_lms2411m01);
		}
		
		if(c241m01c_Y_NY20!=null){
			String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c_Y_NY20), "|");	
			rptVariableMap = getCHKYN_NOT_Z("13A", c241m01c_Y_NY20.getChkItem(), fmt
					, c241m01c_Y_NY20.getChkResult(), rptVariableMap, prop_lms2411m01);
		}
		
		boolean validSysData = false;
		{//顯示 Z_電腦建檔
			int mapItemSeq = -1;
			for (C241M01C c241m01c : c241m01cList) {
				if(Util.equals(CrsUtil.N020, c241m01c.getItemNo())){
					validSysData = true;
					mapItemSeq = c241m01c.getItemSeq();
					break;
				}
			}
			if(validSysData){
				strList.add(_zsysinfo(prop_lms2411m01, mapItemSeq, c241m01cList, CRLN, "&nbsp;"));
			}			
		}
		/*
		 * 原本在 rpt 中是細明體8點，但為印出底線，格式由 PlainText 改成 HTML(進階) 後
		 * 印出來的字體比  左邊(覆審項目)的字體 略小
		 * 所以在外面，包一層 div，指定為11px
		 */
		rptVariableMap.put("C241M01C.ITEMTOTALTEXT", "<div style='font-size:11px;'><div>"+StringUtils.join(strList, "</div><div style='margin-top:12px;'>")+"</div></div>");
		return rptVariableMap;
	}

	private String _zsysinfo(Properties prop_lms2411m01, int mapItemSeq, List<C241M01C> c241m01cList, String CRLN, String SPACE){
		List<String> buff = new ArrayList<String>();
		/* 
			除須查核各項電腦檔案建檔作業是否正確外，應特別註明下列項目之建檔資料是否正確：
		*/
		buff.add("※"+mapItemSeq+"項："+CrsUtil.Z_DESC_N020);
		Map<String, C241M01C> map = retrialService.toMap_keyAs_itemNo(c241m01cList);
		C241M01C c241m01c_ZB1A = map.get(CrsUtil.ZB1A);
		C241M01C c241m01c_ZB2A = map.get(CrsUtil.ZB2A);
		
		/*
		 * 案下授信額度[]有 []無擔保品，底下包含﹝使用分區、用地類別、土地性質﹞
		 */
		boolean skipZB11_12_13 = false;
		if(c241m01c_ZB1A!=null && Util.equals("N", c241m01c_ZB1A.getChkResult())){
			skipZB11_12_13 = true;
		}
		String[] skipArr_ZB1A_dependItems = {CrsUtil.ZB11, CrsUtil.ZB12, CrsUtil.ZB13};
		//===============
		//授信戶案下 □有  □無 額度之融資業務分類A-LOAN註記為「#」或海外AS-400註記為「A0#」，若勾「有」則應檢視下列事項)	（1）不動產暨72-2相關資訊註記欄位   □是  □否   維護正確*
		boolean skipZB21 = false;
		if(c241m01c_ZB2A!=null && Util.equals("N", c241m01c_ZB2A.getChkResult())){
			skipZB21 = true;
		}
		String[] skipArr_ZB2A_dependItems = {CrsUtil.ZB21};
		
		for (C241M01C c241m01c : c241m01cList) {
			if(Util.equals(CrsUtil.Z_電腦建檔資料, c241m01c.getItemType())){
				//只抓 Z_電腦建檔
				
				String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c), "|");
				String chkItem = Util.trim(c241m01c.getChkItem());
				if(fmt.length==0){
					//屬 title 類
					if(Util.equals(CrsUtil.ZB00, c241m01c.getItemNo())){
						buff.add("");//空行
					}
					buff.add(chkItem);
					if(Util.equals(CrsUtil.ZA20, c241m01c.getItemNo())){
						buff.add("<u>"+CrsUtil.Z_NOTE_ZA20+"</u>");
					}
					continue;
				}
				
				if(true){
					if(Util.equals(CrsUtil.ZB1A, c241m01c.getItemNo())){
						buff.add(CrsUtil.get_chkItem_ZB1A(c241m01c, prop_lms2411m01, false)); //ZB1A 的選項前後都有文字，這裡加工呈現「選項後面的文字」
						continue;
					}
	
					if(skipZB11_12_13 && CrsUtil.inCollection(c241m01c.getItemNo(), skipArr_ZB1A_dependItems)){
						//not show
						continue;
					}
				}
				//=============================
				if(true){
					if(Util.equals(CrsUtil.ZB2A, c241m01c.getItemNo())){
						buff.add(CrsUtil.get_chkItem_ZB2A(c241m01c, prop_lms2411m01, false)); //ZB2A 的選項前後都有文字，這裡加工呈現「選項後面的文字」
						continue;
					}
				
					if(skipZB21 && CrsUtil.inCollection(c241m01c.getItemNo(), skipArr_ZB2A_dependItems)){
						//not show
						continue;
					}
				}
				
				if(fmt.length==3){		
					String ra =(Util.equals(c241m01c.getChkResult(), Util.getLeftStr(fmt[0], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label."+fmt[0]);			
					String rb =(Util.equals(c241m01c.getChkResult(), Util.getLeftStr(fmt[1], 1))?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label."+fmt[1]);
					//在「不適用」時
					if(Util.equals(c241m01c.getChkResult(), Util.getLeftStr(fmt[2], 1))){
						ra = OFF_BOX+prop_lms2411m01.getProperty("label."+fmt[2]);
						rb = OFF_BOX+prop_lms2411m01.getProperty("label."+fmt[2]);
					}
					String fullspace = "　";
					if(Util.equals(CrsUtil.ZA11, c241m01c.getItemNo())){	
						//避免【央行購屋/空地/建屋貸款註記】換行
						buff.add((SPACE)+chkItem+(SPACE)+ra+(fullspace)+rb);
					}else if(Util.equals(CrsUtil.ZB21, c241m01c.getItemNo())){	
						buff.add(chkItem); //因字串過長，在組字時，就確定會換行
						buff.add(fullspace+ra+(fullspace)+rb+fullspace+CrsUtil.Z_DESC_ZB21_POSTFIX_1);
						buff.add(CrsUtil.Z_DESC_ZB21_POSTFIX_2);
					}else{
						buff.add((SPACE)+chkItem+(fullspace+fullspace)+ra+(fullspace)+rb);
					}
				}	
				
			}
		}	
		
		/*
		 A-LOAN系統：								ZA00
		 	1.L218額度檔資料查詢						ZA10
		 	央行購屋/空地/建屋貸款註記 [ ]是   [ ] 否	ZA11
		 	
		 	2.L219帳務檔資料查詢						ZA20
		 	授信科目 [ ]是   [ ] 否						ZA21
		 	現行利率 [ ]是   [ ] 否						ZA22
		 	用途別 [ ]是   [ ] 否						ZA23
		 	融資業務分類 [ ]是   [ ] 否					ZA24
		 	是否屬興建房屋 [ ]正確  [ ] 不正常			ZA25
		 	
		 	
		 E-LOAN擔保品管理系統：						ZB00
		 	授信擔保品不動產估價報告書				ZB10
		  		使用分區 [ ]是   [ ] 否					ZB11
		  		用地類別 [ ]是   [ ] 否					ZB12
		  		土地性質 [ ]是   [ ] 否					ZB13
		 */
		return StringUtils.join(buff, CRLN);
	}  
	
	private Map<String, String> setC241M01AForM01DData(
			Map<String, String> rptVariableMap, C241M01A c241m01a, Properties prop_lms2411m01) {
		if(CrsUtil.isCaseS(c241m01a)){
			rptVariableMap.put("C241M01A.CONFLAG", "一、"+(Util.equals("1", c241m01a.getConFlag())?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.conFlag.Y")+" "
													+(Util.equals("2", c241m01a.getConFlag())?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.conFlag.N")+" "
													+prop_lms2411m01.getProperty("label.conFlag.1"));
			rptVariableMap.put("C241M01A.CONFLAG2A", "二、"+(Util.equals("Y", c241m01a.getConFlag2A())?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.conFlag2A.Y")+" "
													+(Util.equals("N", c241m01a.getConFlag2A())?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.conFlag2A.N")+" "
													+prop_lms2411m01.getProperty("label.conFlag2A.desc"));
			rptVariableMap.put("C241M01A.CONFLAG2B", "三、"+prop_lms2411m01.getProperty("label.conFlag2B.desc1")+" "
													+(Util.equals("N", c241m01a.getConFlag2B())?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.conFlag2B.N")+" "
													+(Util.equals("Y", c241m01a.getConFlag2B())?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.conFlag2B.Y")+" "
													+prop_lms2411m01.getProperty("label.conFlag2B.desc2"));
			rptVariableMap.put("C241M01A.CONFLAG2C", "四、"+prop_lms2411m01.getProperty("label.conFlag2C.desc1")+" "
													+(Util.equals("N", c241m01a.getConFlag2C())?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.conFlag2C.N")+" "
													+(Util.equals("Y", c241m01a.getConFlag2C())?ON_BOX:OFF_BOX)+prop_lms2411m01.getProperty("label.conFlag2C.Y")
													+prop_lms2411m01.getProperty("label.conFlag2C.desc2"));
			rptVariableMap.put("C241M01A.CONDITION", Util.trim(c241m01a.getCondition()));
		}else{
			String conFlag = "";
			if(Util.equals("1", c241m01a.getConFlag())){
				conFlag = prop_lms2411m01.getProperty("label.conFlag.1");//覆審正常	
			}else if(Util.equals("2", c241m01a.getConFlag())){
				conFlag = prop_lms2411m01.getProperty("label.conFlag.2");//異常情形，應改善或注意事項
			}
			
			rptVariableMap.put("C241M01A.CONFLAG",Util.trim(conFlag));
			rptVariableMap.put("C241M01A.CONDITION", Util.trim(c241m01a.getCondition()));
			rptVariableMap.put("C241M01A.BRANCHCOMM", Util.trim(c241m01a.getBranchComm()));
		}
		return rptVariableMap;
	}
	/**
	 * 若 營運中心的 單位主管可能 不批
	 * 在表格中填入 「/」
	 */
	private Map<String, String> setC241M01EData(Map<String, String> rptVariableMap, C241M01A c241m01a, String l5DescFlag) {
		//--------
		List<C241M01E> c241m01e_list = retrialService.findC241M01E_c241m01a(c241m01a);
		String[] staffJobs = {"L1","L4","L5"};
		String[] branchTypes = {"1", "2"};
		for(String branchType : branchTypes){
			for(String staffJob : staffJobs){
				List<C241M01E> filter = retrialService.findC241M01E_byBranchTypeStaffJob(c241m01e_list, branchType, staffJob);		
				List<String> staffNoStr = new ArrayList<String>();
				if(CollectionUtils.isNotEmpty(filter)){
					for(C241M01E c241m01e:filter){
						String staffNo = Util.trim(c241m01e.getStaffNo());
						String staffName = Util.trim(userInfoService.getUserName(c241m01e.getStaffNo()));
						staffNoStr.add(Util.trim( Util.isNotEmpty(staffName)?staffName:staffNo ));
					}
				}				
				
				rptVariableMap.put("C241M01E." + branchType + "STAFFJOB" + staffJob, 
						StringUtils.join(staffNoStr, "、"));
			}
		}
		
		{//特殊線條
			String slashLine = "";
			
			if(CollectionUtils.isNotEmpty(c241m01e_list) 
					&& Util.isEmpty(rptVariableMap.get("C241M01E.2STAFFJOBL5"))){
				
				C240M01A c240m01a = retrialService.findC240M01A_C241M01A(c241m01a);	
				//只有 931要斜線
				//正常：營運長不看
				//異常：要呈931營運長，不能畫線
				if(CrsUtil.is_flowClass_throughBr(sysParameterService,c240m01a) && Util.notEquals("2", c241m01a.getConFlag())){
				    //因各單位分層劃分表2022/03以後就不在印出斜線
//					slashLine = "Y";//畫線：╱
				}
			}
			rptVariableMap.put("C241M01E.2STAFFJOBL5LINE",  slashLine);
		}
		rptVariableMap.put("C241M01E.2STAFFJOBL5FLAG",  l5DescFlag);
		rptVariableMap.put("C241M01E.1STAFFJOBL5NAME",  (Util.equals(c241m01a.getOwnBrId(),"007")||Util.equals(c241m01a.getOwnBrId(),"201")?"經副襄理":"經理"));
		return rptVariableMap;
	}
	
	private Map<String, String> getCHKYN_NOT_Z(C241M01C c241m01c, Map<String, String> rptVariableMap, Properties prop) {
		String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c), "|");
		return getCHKYN_NOT_Z(String.valueOf(c241m01c.getItemSeq()), c241m01c.getChkItem(), fmt
				, c241m01c.getChkResult(), rptVariableMap, prop);
	}
	private Map<String, String> getCHKYN_NOT_Z(String itemSeqStr, String chkItem, String[] fmt
			, String chkResult, Map<String, String> rptVariableMap, Properties prop) {
		rptVariableMap.put("C241M01C.ITEM" + itemSeqStr+".DESC", chkItem);
						
		String ra = "";
		String r1 = "";
		String rb = "";
		String r2 = "";		
		
		if(fmt.length==3){
			ra = prop.getProperty("label."+fmt[0]);			
			rb = prop.getProperty("label."+fmt[1]);
			
			if(Util.equals(chkResult, Util.getLeftStr(fmt[0], 1))){
				r1 = "V";
			}else if(Util.equals(chkResult, Util.getLeftStr(fmt[1], 1))){
				r2 = "V";
			}else if(Util.equals(chkResult, Util.getLeftStr(fmt[2], 1))){
				r1 = "─";
				r2 = "─";
			}
		}
		rptVariableMap.put("C241M01C.ITEM" + itemSeqStr+".RA", ra);
		rptVariableMap.put("C241M01C.ITEM" + itemSeqStr+".R1", r1);
		rptVariableMap.put("C241M01C.ITEM" + itemSeqStr+".RB", rb);
		rptVariableMap.put("C241M01C.ITEM" + itemSeqStr+".R2", r2);
		return rptVariableMap;
	}

}
