var pageAction = {	
	grid_height: 0,
	build : function(){
        $.ajax({
            handler: "cls9081formhandler",
            type: "POST",
            dataType: "json",
            data: {
                formAction: "loadData"
            },
            success: function(json){
            	$("#inputForm").injectData(json);
            }
        });
        
		$("#inputThickBox").thickbox({
			title : '',
			width : 380,
			height : pageAction.grid_height + 180,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.def,
			buttons : {
				'sure' : function(){
					$.thickbox.close();
					
					//不能直接用 $.capFileDownload(...)，會強制 encode 把  | 轉成 %7C
		            $.form.submit({
		           	url: __ajaxHandler,
		        		target : "_blank",
		        		data : $.extend({
		        			_pa : 'lmsdownloadformhandler',
		        			'fileDownloadName' : "data.xls",
		        			'serviceName' : "cls9081r01rptservice"
		        		}, $("#inputForm").serializeData())
		        	 });
			        
				},
				'cancel' : function(){	
					$.thickbox.close();
				}
			}
		});
		
	}
}

$(function() {
	pageAction.build();
});