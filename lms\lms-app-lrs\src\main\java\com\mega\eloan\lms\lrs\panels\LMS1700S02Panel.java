package com.mega.eloan.lms.lrs.panels;

import com.mega.eloan.common.panels.Panel;

import tw.com.jcs.common.Util;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * [企金]-覆審報告表  一般授信資料
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/29,jessica,new
 *          </ul>
 */
public class LMS1700S02Panel extends Panel {
	private L170M01A meta;

	public LMS1700S02Panel(String id, boolean updatePanelName, L170M01A meta) {
		super(id, updatePanelName);
		this.meta = meta;
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		boolean isRetrialTeam = false;
		if (Util.equals(Util.trim(meta.getCtlType()), LrsUtil.CTLTYPE_自辦覆審)) {
			isRetrialTeam = true;
		} else {
			isRetrialTeam = CrsUtil.isRetrialTeam(user);
		}
		
		model.addAttribute("show_lnBtn",
				isRetrialTeam
				&& Util.equals(meta.getDocStatus(),
						RetrialDocStatusEnum.區中心_編製中.getCode()));
	
	}

	/**/
	private static final long serialVersionUID = 1L;

}
