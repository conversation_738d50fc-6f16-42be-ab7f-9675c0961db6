/* 
 * MisEJV502ServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.ejcic.service.MisEJF502Service;

/**
 * <pre>
 * MIS.KRM040->MIS.EJV50201->MIS.EJV502
 * </pre>
 * @since  2011/11/11
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/11/11,CP,new
 *          </ul>
 */
@Service
public class MisEJF502ServiceImpl extends AbstractEjcicJdbc implements MisEJF502Service {

//	/*
//	 * (non-Javadoc)
//	 * @see com.mega.eloan.ces.ejcic.service.MisEJV502Service#getByIdPaycode(java.lang.String)
//	 */
//	@Override
//	public Map<String, Object> getByIdPaycode(String custId){
//		//	SELECT PRODID, COUNT(*) AS TCOUNT 
//		//	FROM MIS.KRM040 WHERE ID=? AND PAY_CODE NOT IN ('X','N','0',' ') AND ISSUE<>'TOT' AND QDATE=(SELECT MAX(QDATE) FROM MIS.KRM040 WHERE ID=? ) GROUP BY PRODID
//		String tmpStr = custId;
//		return getJdbc().queryForMap("KRM040.findByIdPaycode", new String[]{tmpStr, tmpStr});
//	}
//	
//	/*
//	 * (non-Javadoc)
//	 * @see com.mega.eloan.ces.ejcic.service.MisEJV502Service#getMaxPayCode(java.lang.String)
//	 */
//	@Override
//	public List<Map<String, Object>> getMaxPayCode(String custId){
//		//	SELECT PRODID, MAX(PAY_CODE) AS MAX_PAY_CODE 
//		//	FROM MIS.KRM040 WHERE ID=? AND PAY_CODE NOT IN ('X','N','0',' ') AND ISSUE<>'TOT' AND QDATE=(SELECT MAX(QDATE) FROM MIS.KRM040 WHERE ID=? ) GROUP BY PRODID
//		return getJdbc().queryForList("KRM040.findByIdMaxPayCode", new String[]{custId, custId});
//	}
//	
//	/*
//	 * (non-Javadoc)
//	 * @see com.mega.eloan.ces.ejcic.service.MisEJV502Service#getRevolbalVal(java.lang.String, java.lang.String)
//	 */
//	@Override
//	public List<Map<String, Object>> getRevolbalVal(String custId){
//		//	SELECT PRODID, COUNT(*) AS TCOUNT, SUM(REVOL_BAL/12) AS TREVOLBAL 
//		//	FROM MIS.KRM040 WHERE ID=? AND (PAY_STAT<>'X' AND PAY_STAT<>'1' AND PAY_STAT<>' ') AND ISSUE<>'TOT' AND QDATE=(SELECT MAX(QDATE) FROM MIS.KRM040 WHERE ID=? ) GROUP BY PRODID	
//		return getJdbc().queryForList("KRM040.findByIdRevolbalVal", new String[]{custId, custId});
//	}

	@Override
	public Map<String, Object> findLstYrCreditCardPaymntDelayCount(String id, String qDate, String prodId) {
		return getJdbc().queryForMap("KRM040.findLstYrCreditCardPaymntDelayCount", new String[]{id, qDate, prodId});
	}

	@Override
	public Map<String, Object> findLstYrCreditCardPaymntDelayTime(String id, String qDate, String prodId) {
		return getJdbc().queryForMap("KRM040.findLstYrCreditCardPaymntDelayTime", new String[]{id, qDate, prodId});
	}

	@Override
	public Map<String, Object> findUseCreditCardCycleAmtCount(String id, String qDate, String prodId) {
		return getJdbc().queryForMap("KRM040.findUseCreditCardCycleAmtCount", new String[]{id, qDate, prodId});
	}
}
