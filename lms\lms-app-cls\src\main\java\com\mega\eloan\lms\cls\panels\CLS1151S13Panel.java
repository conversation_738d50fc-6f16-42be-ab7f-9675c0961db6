package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

public class CLS1151S13Panel extends Panel {

	private static final long serialVersionUID = 1L;

	private boolean isShowCls1151s13;

	/**
	 * @param id
	 */
	public CLS1151S13Panel(String id, boolean isShowCls1151s13) {
		super(id);
		this.isShowCls1151s13 = isShowCls1151s13;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		model.addAttribute("isShowGroupLoanBuildCaseTag", isShowCls1151s13);
	}
}
